<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">{{title.text}}</div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">{{title.subtext}}</div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
  import * as echarts from 'echarts'
  export default {
    props: {
      title: {
        type: Object,
        default: () => {
          return {
            show: true,
            text: '活动次数统计',
            subtext: "单位：次"
          }
        }
      },
      legend: {
        type: Object,
        default: () => {
          return {
            type: 'scroll',
            show: true,
            right: "0%",
            itemGap: 24,
            itemWidth: 8,
            itemHeight: 8,
            icon: 'rect',
            textStyle: {
              color: 'rgba(0, 0, 0, 0.35)',
              // lineHeight: 18,
              padding: [0, 0, 0, 3]
            }
          }
        }
      },
      grid: {
        type: Object,
        default: () => {
          return {
            left: '0',
            top: '10%',
            right: '0.1%',
            bottom: '0',
            containLabel: true
          }
        }
      },
      xAxis: {
        type: Object,
        default: () => {
          return {
            data: [1, 2, 3, 4, 5, 6, 7],
            axisLine: {
              lineStyle: {
                color: '#D3D7DE'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.35)'
            },
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            }
          }
        }
      },
      yAxis: {
        type: Object,
        default: () => {
          return {
            axisLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            },
            axisLabel: {
              color: 'rgba(0, 0, 0, 0.35)'
            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
                color: '#D3D7DE'
              }
            }
          }
        }
      },
      tooltip: {
        type: Object,
        default: () => {
          return {
            trigger: 'axis',
            show: true
          }
        }
      },
      series: {
        type: Array,
        default: () => {
          return [
            {
              name: '白天',
              type: 'bar',
              data: [1, 0, 0, 0, 0, 0, 0],
              barWidth: '30%',
              itemStyle: {
                color: '#F29F4C'
              }
            },
            {
              name: '晚上',
              type: 'bar',
              data: [0, 0, 0, 0, 0, 0, 0],
              barWidth: '30%',
              itemStyle: {
                color: '#2C86F8'
              }
            }
          ]
        }
      }
    },
    data() {
      return {
        myEchart: null
      }
    },
    mounted() {
      this.init()
    },
    deactivated () {
      this.removeResizeFun()
    },
    beforeDestroy () {
      this.removeResizeFun()
    },
    watch: {
      'xAxis': {
        deep: true,
        handler(val) {
          this.init()
        }
      }
    },
    methods: {
      init() {
        this.myEchart = echarts.init(this.$refs.echart)
        let option = {
          // title: this.title,
          legend: this.legend,
          grid: this.grid,
          xAxis: this.xAxis,
          yAxis: this.yAxis,
          tooltip: this.tooltip,
          series: this.series
        }
        this.myEchart.setOption(option)
        window.addEventListener('resize', () => this.myEchart.resize())
      },
      removeResizeFun() {
        window.removeEventListener('resize', () => this.myEchart.resize())
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .echart-text {
      font-size: 14px;
      font-family: 'MicrosoftYaHei-Bold';
      font-weight: bold;
      line-height: 20px;
      text-align: center;
    }
    .echart-subtext {
      margin-top: 8px;
      font-size: 12px;
      line-height: 18px;
    }
    .echart-content {
      flex: 1;
    }
  }
</style>
