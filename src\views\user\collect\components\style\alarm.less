.box-top{
    position: relative;
    display: flex;
    justify-content: center;
    .level-title{
        height: 21px;
    }
    .num{
        text-align: center;
        position: absolute;
        top: 0;
        width: 98px;
        color: #fff;
    }
    .favorite{
        position: absolute;
        right: 5px;
    }
}
.box-wrapper{
    display: flex;
    .title{
        color: #999;
        margin-right: 10px;
        line-height: 22px;
    }
    .box-val{
        flex: 1;
        overflow: hidden;    
        text-overflow:ellipsis;    
        white-space: nowrap;
    }
}
.collection{
    /deep/ .icon-shoucang {
        color: #888888 !important;
        text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
        color: #f29f4c !important;
    }
}