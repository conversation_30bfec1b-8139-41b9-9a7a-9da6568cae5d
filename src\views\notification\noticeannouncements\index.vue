<template>
  <div class="notice-more height-full">
    <div class="equipmentimport auto-fill" v-if="!componentName">
      <div class="search-module mb-lg">
        <ui-label class="inline rigth-margin bottom-margin" label="公告名称">
          <Input v-model="searchData.title" class="width-md" placeholder="请输入公告名称"></Input>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
          <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, startSearch)">重置</Button>
        </div>
      </div>
      <div class="table-module auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #option="{ row }">
            <create-tabs
              class="inline"
              component-name="NoticeDetail"
              important-tab-name="公告详情"
              tabs-text="公告详情"
              :tabs-query="{ id: row.id }"
              @selectModule="selectModule"
            >
              <ui-btn-tip icon="icon-chakanxiangqing" content="查看详情"></ui-btn-tip>
            </create-tabs>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <component v-else :is="componentName"></component>
  </div>
</template>
<script>
import systemconfig from '@/config/api/systemconfig';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'NoticeAnnouncements',
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      loading: false,
      searchData: { title: '' },
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'center' },
        {
          title: '公告名称',
          key: 'title',
          align: 'left',
        },
        {
          title: '公告内容',
          key: 'content',
          align: 'left',
          tooltip: true,
        },
        // { title: '发布单位', key: 'modifier', align: 'left' },
        { title: '发布时间', key: 'publishTime', align: 'left' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 70,
          className: 'table-action-padding',
        },
      ],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
    this.startWatch('$route', () => {
      this.getParams();
    });
  },
  methods: {
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
      this.$nextTick(() => {
        if (this.$refs.noticeRef) {
          this.$refs.noticeRef.initFunc();
        }
      });
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        let components = name.split('-');
        this.componentName = components[components.length - 1];
      } else {
        this.componentName = null;
      }
    },
    startSearch() {
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          ...this.searchData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          status: '0',
        };
        let res = await this.$http.post(systemconfig.notifyPageList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    NoticeDetail: require('@/views/notification/noticeannouncements/components/notice-detail.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.equipmentimport {
  overflow: hidden;
  position: relative;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .table-module {
    position: relative;
    @{_deep}.ivu-table td.content-class {
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
</style>
