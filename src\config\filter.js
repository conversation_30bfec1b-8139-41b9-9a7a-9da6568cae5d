// 社戒社康过滤
function toFiltImgUrl3(url) {
  if (!!url && typeof url === 'string') {
    let filtImgUrlObj = { filtImgUrl: null };
    let imgServerMap = ImgServerMap;
    for (let key in imgServerMap) {
      let len = imgServerMap[key].sign.length;
      if (url.slice(0, len) === imgServerMap[key].sign) {
        if (url.slice(0, len) !== 'LOC') {
          filtImgUrlObj.filtImgUrl = 'http://86.87.0.54:6551/DownLoadFile?filename=' + url.substr(1);
        } else {
          filtImgUrlObj.filtImgUrl = 'http://86.87.0.54:6551/DownLoadFile?filename=' + url;
        }
      }
    }
    !filtImgUrlObj.filtImgUrl && (filtImgUrlObj.filtImgUrl = url);
    return filtImgUrlObj.filtImgUrl;
  }
}

// 传进来的正整数字符串变成三位一逗号的字符串
function formatNum(str) {
  if (isNaN(+str)) {
    return '0';
  }
  str += '';
  let i = str.length > 3 ? str.length % 3 : 0;
  return (i ? `${str.substr(0, i)},` : '') + str.substr(i).replace(/(\d{3})(?=\d)/g, '$1' + ',');
}

// 过滤数据量过大变成999+
function bigNum(num) {
  if (num > 999) {
    return `999+`;
  } else {
    return num;
  }
}

// 过滤男女
function sexText(val) {
  return val == 1 ? '男' : '女';
}

// 过滤身份
function idType(val) {
  switch (val) {
    case '0':
      return '身份证';
    case '1':
      return '护照';
    case '2':
      return '其他';
  }
}

function marriage(val) {
  return val == 1 ? '已婚' : '未婚';
}

// 经纬度最多保留8位小数
function filterLngLat(value) {
  let str = '';
  str = value ? String(value).replace(/^(.*\..{8}).*$/, '$1') : '';
  return str;
}
//过滤器start
// 时间过滤（2021-07-13T02:42:01.983+00:00 转 YYYY-MM-DD hh:mm:ss）
function filterDateFun(date) {
  if (!date) return date;
  var dateee = new Date(date).toJSON();
  return new Date(+new Date(dateee) + 8 * 3600 * 1000)
    .toISOString()
    .replace(/T/g, ' ')
    .replace(/\.[\d]{3}Z/, '');
}
//end
//厂商字典转换
function facturer(data) {
  let a = JSON.parse(sessionStorage.getItem('manufacturer'));
  if (!data) return data;
  for (let i of a) {
    if (data == i.value) {
      return i.name;
    }
  }
}

function get_car(data) {
  let a = JSON.parse(sessionStorage.getItem('getfacturer'));
  if (!data) return data;
  for (let i of a) {
    if (data == i.dataKey) {
      return i.dataValue;
    }
  }
}
// 调用方式 message | filterType(list) 公用
function filterType(data, list, key, value, nullData) {
  let keys = key || 'dataKey';
  let values = value || 'dataValue';
  let nullDatas = nullData || '--';
  if (!data) return nullDatas;
  if (list.length == 0) return data;
  if (!data) return data;
  let item = data;
  for (let i of list) {
    if (data == i[keys]) {
      item = i[values];
    }
  }
  return item;
}

// 调用方式 message | filterType(list) 公用
function filterTypeMore(data, list, key, value, nullData) {
  let keys = key || 'dataKey';
  let values = value || 'dataValue';
  let nullDatas = nullData || '--';
  if (!data) return nullDatas;
  if (list.length == 0) return data;
  if (!data) return data;
  let arr = data.split('/');
  var str = '';
  arr.forEach((item) => {
    for (let i of list) {
      if (item == i[keys]) {
        str += i[values] + ' / ';
      }
    }
  });
  return str.substring(0, str.length - 3);
}
//离线在线
function status(data) {
  if (data == false) {
    return '离线';
  } else {
    return '在线';
  }
}

// 数字转换，每三位添加逗号
function numberInfo(data) {
  let num = (data || 0).toString();
  let result = '';
  while (num.length > 3) {
    result = ',' + num.slice(-3) + result;
    num = num.slice(0, num.length - 3);
  }
  if (num) {
    result = num + result;
  }
  return result;
}

function filterDic(value, data = [], dataKey = 'dataKey', dataValue = 'dataValue') {
  let result = data.find((item) => item.dataKey === value);
  if (!result) return '--';
  return result[dataValue];
}

export {
  formatNum,
  bigNum,
  sexText,
  idType,
  marriage,
  filterLngLat,
  filterDateFun,
  facturer,
  get_car,
  filterType,
  filterTypeMore,
  status,
  numberInfo,
  filterDic,
};
