<!--
    * @FileDescription: 人脸详情
    * @Author: H
    * @Date: 2022/12/12
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-10-15 15:37:27
 -->
<template>
  <div class="dom-wrapper">
    <div class="dom-box">
      <header>
        <span>抓拍详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close')"
        ></ui-icon>
      </header>
      <carousel
        ref="carousel"
        :same-position-point="samePositionPoint"
        @changeVid="changeVid"
      ></carousel>
      <section class="dom-content">
        <div class="dom-info">
          <div class="dom-info-left" style="width: 200px">
            <img v-lazy="faceInfo.traitImg" alt="" style="width: 200px" />
            <span class="similarity" v-if="faceInfo.score"
              >{{ faceInfo.score }}%</span
            >
            <div class="title">
              <span
                :class="checkStatus ? 'active' : ''"
                @click="tabsChange(true)"
                >抓拍记录</span
              >
              <span
                :class="!checkStatus ? 'active' : ''"
                @click="tabsChange(false)"
                >人员档案</span
              >
            </div>
            <div class="traffic-record" v-if="checkStatus">
              <div class="dom-content-p">
                <span class="label">抓拍地点</span><span>：</span>
                <!-- <span class="message" v-show-tips>{{ faceInfo.captureAddress || '--' }}</span> -->
                <ui-textOver-tips
                  class="message"
                  :content="faceInfo.captureAddress || '--'"
                ></ui-textOver-tips>
              </div>
              <div class="dom-content-p">
                <span class="label">抓拍时间</span><span>：</span>
                <span class="message">{{
                  faceInfo.absTime || faceInfo.captureTime || "--"
                }}</span>
              </div>
              <!-- <div class="dom-content-p">
                <span class="label">设备类型</span><span>：</span>
                <div class="message" v-show-tips>
                  <span v-if="!Array.isArray(facilitySplit(faceInfo.sbgnlx))">
                    {{
                      faceInfo.sbgnlx | commonFiltering(translate("sbgnlxList"))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(faceInfo.sbgnlx)"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate("sbgnlxList"))
                    }}{{
                      inde + 1 < facilitySplit(faceInfo.sbgnlx).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </div>
              </div> -->
              <structuredmsg :info="faceInfo" type="1"></structuredmsg>
            </div>
            <div class="personnel-files" v-else>
              <div class="dom-content-p">
                <span class="label">视频身份</span><span>：</span>
                <span
                  class="address"
                  :class="{ identity: faceInfo.vid }"
                  @click="toDetail(faceInfo)"
                  >{{ faceInfo.vid || "--" }}</span
                >
              </div>
              <div class="dom-content-p">
                <span class="label">姓名</span><span>：</span>
                <span class="message">{{ faceArchives.xm || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">联系方式</span><span>：</span>
                <span class="message">{{ faceArchives.lxdh || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">身份证号</span><span>：</span>
                <span class="address">{{ faceArchives.gmsfhm || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">家庭住址</span><span>：</span>
                <span class="address">{{
                  faceArchives.xzz_mlxxdz || "--"
                }}</span>
              </div>
            </div>
          </div>
          <div class="dom-info-right">
            <ui-image :src="faceInfo.sceneImg" alt="静态库" viewer />
          </div>
        </div>
      </section>
      <footer>
        <!-- <search-around v-if="cutIcon" @preDetial="preDetial" @nextDetail="nextDetail"></search-around> -->
      </footer>
    </div>
  </div>
</template>

<script>
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import carousel from "./carousel.vue";
import { mapGetters } from "vuex";
import structuredmsg from "@/components/mapdom/structuredmsg.vue";
// import { commonMixins } from "@/mixins/app.js";
export default {
  name: "",
  // mixins: [commonMixins], //全局的mixin
  components: {
    carousel,
    structuredmsg,
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      faceInfo: {},
      checkStatus: true, // 抓拍记录，人员档案tab切换
      faceArchives: {},
      samePositionPoint: [],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      getPeerList: "map/getPeerList",
      getPeerData: "map/getPeerData",
    }),
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * pointItem: 本条数据
     * item：地图撒点数据集合（对象信息，检索结果）
     * type：是否点击列表
     */
    init(pointItem, item, type = "", menuType = "") {
      return new Promise((resolve) => {
        this.checkStatus = true;
        this.$refs.carousel.init();
        this.headtablist(pointItem, item, type, menuType);
        if (menuType == "track") {
          this.faceInfo = { ...pointItem };
        } else {
          this.faceInfo =
            type == "peer"
              ? { ...pointItem[this.getPeerData.type] }
              : { ...pointItem };
        }
        resolve({ data: true });
      });
    },
    // 获取头部列表
    headtablist(item, tabItem, type, menuType) {
      if (menuType == "track") {
        let list = Array.isArray(this.getPeerList)
          ? this.getPeerList
          : this.getPeerList["vidTrajectory"];
        this.samePositionPoint = list.filter((ite, index) => {
          if (ite.deviceId === item.deviceId) {
            ite.Index = index + 1;
          }
          return ite.deviceId === item.deviceId;
        });
      } else {
        if (type == "peer" || type == "peerMap") {
          //同行次数
          let allList = this.getPeerList.filter((ite, index) => {
            if (ite.deviceId === item.deviceId) {
              ite.Index = index + 1;
            }
            return ite.deviceId === item.deviceId;
          });
          let tabIndex = 0;
          this.samePositionPoint = [];
          allList.map((ite, index) => {
            // 判断是直接点击地图，列表
            if (type == "peerMap") {
              //直接点击地图
              if (item.recordId == ite.peerDetailVo.recordId) {
                tabIndex = index;
              }
              this.$set(this.samePositionPoint, index, {
                ...ite.peerDetailVo,
                num: ite.num,
              });
            } else {
              //点击列表
              if (
                item[this.getPeerData.type].recordId ==
                ite[this.getPeerData.type].recordId
              ) {
                tabIndex = index;
              }
              this.$set(this.samePositionPoint, index, {
                ...ite[this.getPeerData.type],
                num: ite.num,
              });
            }
          });
          this.$refs.carousel.init(tabIndex);
        } else if (type == "frequency") {
          this.samePositionPoint = this.positionPoints;
          let tabIndex = 0;
          this.positionPoints.forEach((subItem, subIndex) => {
            if (subItem.recordId == item.recordId) {
              tabIndex = subIndex;
            }
            this.$set(this.samePositionPoint, subIndex, {
              ...subItem,
            });
          });
          this.$refs.carousel.init(tabIndex);
        } else {
          //对象信息、检索信息
          let list = this.positionPoints.filter((ite) => {
            return ite.geoPoint.lat == item.lat && ite.geoPoint.lon == item.lon;
          });
          // this.samePositionPoint = tabItem;
          this.samePositionPoint = list[0].faceCaptureList;
        }
      }
    },
    async tabsChange(state) {
      if (this.checkStatus == state) {
        return;
      }
      this.checkStatus = state;
      if (!this.checkStatus) {
        try {
          if (!this.faceInfo.vid) {
            this.$Message.warning("档案不存在！");
            return;
          }
          let res = await getPersonInfoByPersonId({ vid: this.faceInfo.vid });
          if (res.code === 200) {
            this.faceArchives = res.data || {};
          }
        } catch (error) {
          this.$Message.warning("档案暂无数据");
        }
      }
    },
    toDetail(item) {
      if (!item.vid) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "video-archive",
        query: {
          archiveNo: item.vid,
          source: "video",
          initialArchiveNo: item.vid,
        },
      });
      window.open(href, "_blank");
    },
    // 顶部切换
    changeVid(item) {
      this.$emit("changeListTab", { ...item }, "face");
      this.faceInfo = { ...item };
      this.checkStatus = true;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index";
.dom-info-right {
  /deep/ .ui-image-div {
    img {
      // max-width: 100%;
      // max-height: 100%;
      // width: auto;
      // height: auto;
    }
  }
}
</style>
