<template>
  <ui-modal
    v-model="visible"
    :title="modalAction.title"
    :footer-hide="isView"
    width="35rem"
    @onCancel="handleReset"
    @query="handleSubmit"
  >
    <Form ref="formDataRef" :model="formData" :rules="formRules">
      <FormItem label="汇报标题" prop="title" class="form-item">
        <Input v-if="!isView" type="text" v-model="formData.title" :maxlength="20" placeholder="请输入汇报标题"></Input>
        <span class="font-color" v-else>{{ formData.title }}</span>
      </FormItem>
      <FormItem label="汇报时间" prop="startTime" class="form-item">
        <DatePicker
          v-if="!isView"
          class="width-md"
          v-model="formData.startTime"
          type="date"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formData, 'startTime')"
        ></DatePicker>
        <span v-if="!isView" class="ml-sm mr-sm">--</span>
        <DatePicker
          v-if="!isView"
          class="width-md"
          v-model="formData.endTime"
          type="date"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, formData, 'endTime')"
        ></DatePicker>
        <span class="font-color" v-else>{{ formData.startTime }} 至 {{ formData.endTime }}</span>
      </FormItem>
      <FormItem label="汇报给" prop="receiveOrgCodeList" class="form-item" :class="{'org-text-view': isView}">
        <select-organization-tree
          ref="tree"
          v-if="!isView"
          check-strictly
          :tree-list="noLimitTreeList"
          :flat-tree-list="noLimitFlatTreeList"
          :default-checked-keys="formData.receiveOrgCodeList"
          @check="check"
        >
        </select-organization-tree>
        <span class="font-color" v-else>{{
          formData.receiveOrgCodeNameList && formData.receiveOrgCodeNameList.join(',')
        }}</span>
      </FormItem>
      <FormItem label="汇报内容" prop="content" class="form-item">
        <Input
          v-if="!isView"
          type="textarea"
          v-model="formData.content"
          placeholder="请输入汇报内容"
          :rows="15"
          :maxlength="512"
        ></Input>
        <div class="desc font-color" v-else>{{ formData.content }}</div>
      </FormItem>
      <FormItem label="附件" class="form-item">
        <Upload
          ref="upload"
          class="inline"
          v-if="!isView"
          :action="uploadUrl"
          :show-upload-list="false"
          :headers="headers"
          :max-size="20480"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
          :on-exceeded-size="importExceededSize"
          multiple
        >
          <Button type="primary" :loading="importLoading">
            <i class="icon-font icon-shangchuan f-12 vt-middle"></i>
            <span class="vt-middle ml-sm">上传附件</span>
          </Button>
        </Upload>
        <div v-for="(upItem, upIndex) in uploadData" :key="'upIndex' + upIndex">
          <Button type="text" @click.stop="downLoad(upItem)" title="点击下载">
            <span class="upload-name">{{ upItem.name }}</span>
          </Button>
          <ui-btn-tip
            v-if="!isView"
            icon="icon-shangchuan"
            class="font-blue ml-sm"
            content="重新上传"
            @click.native.stop="reupload(upIndex)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="!isView"
            icon="icon-piliangshanchu"
            class="font-red ml-sm"
            content="删除"
            @click.native.stop="delUpload(upIndex)"
          ></ui-btn-tip>
        </div>
      </FormItem>
    </Form>
    <template #footer>
      <Button type="primary" :loading="saveLoading" @click="handleSubmit">提 交</Button>
    </template>
  </ui-modal>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
import systemconfig from '@/config/api/systemconfig';
export default {
  props: {
    modalAction: {},
    modalData: {},
    value: {
      type: Boolean,
    },
  },
  data() {
    const validateTime = (rule, value, callback) => {
      if (!this.formData.startTime || !this.formData.endTime) {
        callback(new Error('请选择汇报时间'));
      } else {
        callback();
      }
    };
    const validateOrg = (rule, value, callback) => {
      if (value.length === 0) {
        callback(new Error('请选择汇报的组织机构'));
      } else {
        callback();
      }
    };

    return {
      visible: false,
      saveLoading: false,
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.formData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.formData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      formRules: {
        title: [{ required: true, message: '请输入汇报标题', trigger: 'blur' }],
        startTime: [{ required: true, validator: validateTime, trigger: 'blur' }],
        receiveOrgCodeList: [{ required: true, validator: validateOrg, trigger: 'change' }],
        content: [{ required: true, message: '请输入汇报内容', trigger: 'blur' }],
      },
      formData: {
        title: '',
        startTime: '',
        endTime: '',
        receiveOrgCodeList: [],
        content: '',
        fileIds: '',
      },
      uploadUrl: systemconfig.uploadNotify,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      uploadData: [],
      importLoading: false,
      isReload: false,
      reIndex: 0,
    };
  },
  created() {},
  methods: {
    check(orgList) {
      this.formData.receiveOrgCodeList = orgList.map((org) => org.orgCode);
    },
    beforeUpload(file) {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$Message.error('上传文件大小不能超过10MB!');
        return false;
      }
      if (this.uploadData.length > 2) {
        this.$Message.error('最多可上传3个文件！');
        return false;
      }
      this.importLoading = true;
    },
    importSuccess(res, file) {
      this.importLoading = false;
      if (this.isReload) {
        this.uploadData.splice(this.reIndex, 1, file);
        this.isReload = false;
      } else {
        this.uploadData.push(file);
      }
      this.$Message.success(res.msg);
    },
    importExceededSize() {
      this.$Message.error('上传文件超出20M');
      this.importLoading = false;
    },
    importError(err) {
      this.importLoading = false;
      this.$Message.error(err.msg);
    },
    handleClearFiles() {
      this.$refs.upload.clearFiles();
    },
    downLoad(upItem) {
      if (!upItem.url) return;
      this.$util.common.transformBlob(upItem.url, upItem.name);
    },
    delUpload(index) {
      this.uploadData.splice(index, 1);
    },
    reupload(index) {
      this.isReload = true;
      this.reIndex = index;
      this.$refs.upload.handleClick();
    },
    handleReset() {
      this.formData = {
        title: '',
        startTime: '',
        endTime: '',
        receiveOrgCodeList: [],
        content: '',
        fileIds: '',
      };
      this.uploadData = [];
      if (!this.isView) {
        this.$refs.tree.initCheck();
        this.$refs.formDataRef.resetFields();
      }
    },
    async save() {
      try {
        this.saveLoading = true;
        let fileIds = [];
        this.uploadData.forEach((row) => {
          fileIds = [...fileIds, ...row.response.data];
        });
        this.formData.fileIds = fileIds.join(',');
        this.formData.startTime = this.$util.common.formatDate(this.formData.startTime);
        this.formData.endTime = this.$util.common.formatDate(this.formData.endTime);
        const api = this.isEdit ? equipmentassets.workReportUpdate : equipmentassets.workReportAdd;
        const res = await this.$http.post(api, this.formData);
        this.$Message.success(res.data.msg);
        this.handleReset();
      } catch (err) {
        console.log(err);
      } finally {
        this.saveLoading = false;
      }
    },
    handleSubmit() {
      this.$refs.formDataRef.validate(async (valid) => {
        if (valid) {
          await this.save();
          this.visible = false;
          this.$emit('update');
        } else {
          this.$Message.error('请填写完整数据');
        }
      });
    },
  },
  computed: {
    isView() {
      return this.modalAction.action === 'view';
    },
    isEdit() {
      return this.modalAction.action === 'edit';
    },
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      initialOrgList: 'common/getInitialOrgList',
    }),
    noLimitFlatTreeList() {
      return this.initialOrgList.map((item) => {
        return {
          ...item,
          disabled: false,
        };
      });
    },
    noLimitTreeList() {
      return this.$util.common.arrayToJson(this.noLimitFlatTreeList, 'id', 'parentId');
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    modalData(val) {
      Object.assign(this.formData, val);
      !!val.uploadData && (this.uploadData = val.uploadData);
    },
  },
  components: {
    SelectOrganizationTree: require('@/api-components/select-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.font-color {
  color: var(--color-content);
}
@{_deep} .ivu-modal-body {
  padding: 16px 50px 16px 50px;
}
.form-item {
  @{_deep} .ivu-form-item-label {
    width: 80px;
  }
  @{_deep} .ivu-form-item-content {
    margin-left: 80px;
  }
  &.ivu-form-item {
    margin-bottom: 16px !important;
  }
}
.desc,
@{_deep}.org-text-view .ivu-form-item-content {
  max-height: 325px;
  overflow-y: auto;
}
</style>
