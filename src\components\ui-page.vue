<template>
  <div class="pages">
    <span class="total" v-if="showTotal">
      共 <span class="primary">{{ total.toLocaleString() }}条</span> 记录
    </span>
    <!-- <span>共<span class="color">{{total}}条</span>记录，当前第<span class="color">{{current}}/{{pageSize}}</span>页</span>  -->
    <Page :current="current" :total="sumTotal" :size="size" :page-size="pageSize" :page-size-opts="pageSizeOpts" transfer :show-sizer="showSizer" :show-elevator="showElevator" :simple="simple" class="page" placement="top" @on-change="pageChange" @on-page-size-change="pageSizeChange" />
  </div>
</template>

<script>
export default {
  props: {
    total: {
      type: Number,
      default: 100
    },
    countTotal: {
        type: Boolean,
        default: false 
    },
    current: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 10
    },
    pageSizeOpts: {
      type: Array,
      default() {
        return [10, 20, 50, 100]
      }
    },
    showTotal: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'default'
    },
    showSizer: {
      type: Boolean,
      default: true
    },
    showElevator: {
      type: Boolean,
      default: true
    },
    simple: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
    computed: {
        sumTotal() {
            if(this.total >= maxTotal && this.countTotal){
                let pageTotal = Math.floor(maxTotal / this.pageSize) * this.pageSize
                return pageTotal
            }else{
                return this.total
            }
        } 
    },
  watch: {},
  mounted() {},
  created() {},
  methods: {
    pageChange(size) {
      this.$emit('pageChange', size)
    },
    pageSizeChange(size) {
      this.$emit('pageSizeChange', size)
    }
  }
}
</script>
