<template>
  <div class="ui-page">
    <slot name="leftButton"></slot>
    <div>
      <div v-if="pageData.totalCount" class="fl nav-not-selected total-count">
        共<span class="table-text-content"> {{ pageData.totalCount | formatNum }} </span>条记录
      </div>
      <ul class="page-body fr">
        <li
          class="page-item page-turning border"
          :class="{ 'page-disabled': pageData.pageNum === 1 }"
          @click="previousPage(pageData.pageNum)"
        >
          <Icon type="ios-arrow-back" />
        </li>
        <li class="fl overflow" v-if="pageShow">
          <div>
            <!-- 当页码大于5时显示1 -->
            <div class="page-item border" v-if="pageData.pageNum > 5 && !simplePage" @click="jumpPage(1)">1</div>
            <!-- 当页码大于5时可以向前跳转5页 -->
            <div
              class="page-item border"
              v-if="pageData.pageNum > 5 && !simplePage"
              title="向前5页"
              @click="jumpPage(pageData.pageNum - 5)"
            >
              <Icon type="ios-more" />
            </div>
            <!-- 当页码大于5时只显示当前页码前面两个页面 如当前页码为6 则显示4,5 -->
            <div class="fl border" v-if="pageData.pageNum > 5 && !simplePage">
              <div class="page-item" v-for="item in 2" :key="item" @click="jumpPage(pageData.pageNum - 3 + item)">
                {{ pageData.pageNum - 3 + item }}
              </div>
            </div>
            <!-- 当页码小于5时显示页码之前所有的页码 如当前页码为4 则显示1,2,3 -->
            <div class="fl" v-if="pageData.pageNum <= 5 && !simplePage">
              <div
                class="page-item border"
                v-for="(item, index) in pageData.pageNum - 1"
                :key="index"
                @click="jumpPage(item)"
              >
                {{ item }}
              </div>
            </div>
            <!-- 当前页码 -->
            <div class="page-item page-item-active border" @click="jumpPage(pageData.pageNum)">
              {{ pageData.pageNum }}
            </div>
            <!-- 当总页码-当前页码 大于等于5时 显示当前页面后两个 如当前页码为10 则显示11,12 -->
            <div class="fl" v-if="!simplePage && totalPage - pageData.pageNum >= 5">
              <div class="page-item border" v-for="item in 2" :key="item" @click="jumpPage(pageData.pageNum + item)">
                {{ pageData.pageNum + item }}
              </div>
            </div>
            <!-- 当总页码-当前页码 小于5时 显示当前页码后的所有页码 如当前页码为17 则显示18,19,20 -->
            <div class="fl" v-if="totalPage - pageData.pageNum < 5 && !simplePage">
              <div
                class="page-item border"
                v-for="(item, index) in totalPage - pageData.pageNum"
                :key="index"
                @click="jumpPage(pageData.pageNum + item)"
              >
                {{ pageData.pageNum + item }}
              </div>
            </div>
            <!-- 当总页码-当前页码大于5时 显示跳转后5页 -->
            <div
              class="page-item border"
              v-if="totalPage - pageData.pageNum >= 5 && !simplePage"
              title="向后5页"
              @click="jumpPage(pageData.pageNum + 5)"
            >
              <Icon type="ios-more" />
            </div>
            <!-- 当总页码-当前页码大于5时 显示最后一页 -->
            <div
              class="page-item border"
              v-if="totalPage - pageData.pageNum >= 5 && hasLast && !simplePage"
              @click="jumpPage(totalPage)"
            >
              {{ totalPage }}
            </div>
          </div>
        </li>
        <li class="fl overflow" v-if="!pageShow">
          <!-- 当前页码 -->
          <div class="page-item page-item-active border">
            {{ pageData.pageNum }}
          </div>
        </li>
        <li
          class="page-item page-turning border"
          :class="{ 'page-disabled': pageData.pageNum === totalPage }"
          @click="nextPage(pageData.pageNum)"
        >
          <Icon type="ios-arrow-forward" />
        </li>
        <Select
          v-model="pageData.pageSize"
          class="page-selection fl"
          :transfer="transfer"
          @on-change="changePageSize"
          v-if="!simplePage && showChangeSize"
        >
          <Option v-for="item in pageList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
        <li v-if="hasLast" class="page-item total-page border">共{{ totalPage }}页</li>
        <div class="page-jump" v-if="!simplePage && pageShow">
          跳至<InputNumber
            v-model.number="jumpNum"
            @keyup.enter.native="jumpPage(jumpNum)"
            class="page-input"
            :max="totalPage"
            :min="1"
          ></InputNumber
          >页
        </div>
      </ul>
    </div>
  </div>
</template>
<style lang="less" scoped>
.ui-page {
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20px;
  text-align: right;
  margin-right: 0;
  z-index: 199;
  height: 64px;
  background: var(--bg-content);
  color: var(--color-table-header-th);
  position: relative;
  .table-text-content {
    color: var(--color-primary);
  }
  .total-count {
    color: var(--color-table-header-th);
    font-size: 14px;
    line-height: 24px;
    height: 24px;
  }
  .prev-text {
    display: inline-block;
    line-height: 24px;
  }
  .page-body {
    overflow: hidden;
    color: #1b82d2;
    .page-selection {
      margin-left: 10px;
      width: auto;
    }
    // 更改select
    @{_deep} .ivu-select {
      height: 24px;
      color: var(--color-page-item);
      .ivu-select-selection {
        height: 24px;
        border-radius: 4px;
        .ivu-select-selected-value {
          height: 24px;
          line-height: 24px;
        }
      }
      .ivu-select-dropdown {
        .ivu-select-item {
          height: 24px;
          line-height: 24px;
          padding: 0 16px;
          text-align: center;
        }
      }
    }

    .page-item {
      // width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 4px;
      background: var(--bg-checkbox-inner);
      border: 1px solid var(--border-checkbox-inner);
      margin-left: 10px;
      float: left;
      text-align: center;
      color: var(--color-page-item);
      user-select: none;
      padding: 0 10px;
      cursor: pointer;
      &:not(.page-disabled, .total-page):hover {
        border: 1px solid var(--color-primary);
        color: var(--color-page-item-hover);
        background-color: var(--bg-page-item-hover);
      }
      &.page-item-active {
        &:hover {
          background-color: var(--color-primary);
          color: #fff;
        }
      }
    }
    .total-page {
      width: auto;
      padding: 0 10px;
    }
    .page-item-active {
      border-color: var(--color-primary);
      background-color: var(--color-primary);
      color: #fff;
    }
    .page-disabled {
      cursor: not-allowed;
    }
    .page-jump {
      display: inline-block;
      margin-left: 10px;
      color: var(--color-page-item);
      .page-input {
        margin: 0 10px 0 10px;
        height: 24px;
        line-height: 24px;
        width: 50px;

        &.ivu-input-number-focused {
          border: 1px solid #1b82d2 !important;
        }
        @{_deep} .ivu-input-number-input {
          height: 24px;
          line-height: 24px;
        }
      }
    }
  }
}
</style>
<script>
export default {
  data() {
    return {
      // pageList: [
      //   { label: "10 条/页", value: 10 },
      //   { label: "20 条/页", value: 20 },
      //   { label: "50 条/页", value: 50 },
      //   { label: "100 条/页", value: 100 },
      // ],
      jumpNum: null,
    };
  },
  created() {},
  mounted() {},
  methods: {
    jumpPage(page) {
      this.pageData.pageNum = page;
      this.$emit('changePage', page);
    },
    previousPage(page) {
      if (page === 1) {
        return false;
      }
      this.pageData.pageNum = page - 1;
      this.$emit('changePage', page - 1);
    },
    nextPage(page) {
      if (page === this.totalPage) {
        return false;
      }
      this.pageData.pageNum = page + 1;
      this.$emit('changePage', page + 1);
    },
    changePageSize() {
      this.pageData.pageNum = 1;
      this.$emit('changePageSize', this.pageData.pageSize);
    },
  },
  watch: {},
  computed: {
    totalPage() {
      return this.pageData.totalCount === 0 ? 1 : Math.ceil(this.pageData.totalCount / this.pageData.pageSize);
    },
  },
  props: {
    /**
     * totalCount:数据总数
     * size:默认或者small
     * pageNum:当前页码
     * pageSize:当前页码条数
     */
    pageData: {},
    // 是否包含最后一页
    hasLast: {
      default() {
        return true;
      },
    },
    pageShow: {
      default() {
        return true;
      },
    },
    // 短小一些，不会显示很多页码展开并且不能选择pageSize -- add by ning
    simplePage: {
      default() {
        return false;
      },
    },
    transfer: {
      type: Boolean,
      default: false,
    },
    pageList: {
      default() {
        return [
          { label: '10 条/页', value: 10 },
          { label: '20 条/页', value: 20 },
          { label: '50 条/页', value: 50 },
          { label: '100 条/页', value: 100 },
        ];
      },
    },
    showChangeSize: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
};
</script>
