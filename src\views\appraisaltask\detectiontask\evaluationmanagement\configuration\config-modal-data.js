// configuration-modal data的数据
export default {
  data() {
    return {
      activeIndex: 'all',
      modalActionTitle: '选择数据对象',
      multipleSelection: [],
      ruleCustom: {
        pumpQuantity: [{ required: true, message: '请输入必填内容', trigger: 'change' }],
        pumpType: [
          {
            required: true,
            message: '请输入必填内容',
            trigger: 'change',
            type: 'number',
          },
        ],
        perPumpQuantity: [{ required: true, message: '请输入必填内容', trigger: 'blur' }],
      },
      dataObjectShow: false,
      dataObjectData: {},
      moduleAction: {},
      visible: false,
      saveModalLoading: false,
      styles: {
        width: '7rem',
      },
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '指标名称', key: 'indexName', slot: 'indexName' },
        { title: '指标类型', slot: 'indexModule' },
        { title: '参数配置', width: 150, slot: 'option' },
      ],
      tableData: [],
      searchData: {
        schemeId: null,
        indexModule: '',
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      indexModule: '',
      checkList: {},
      modalData: {},
      algVendorsList: {},
      loading: false,
      placeVisible: false,
      dataObject: {
        deviceCondition: {
          objectType: '1',
          filter: {
            checkDeviceFlag: '0',
            placeName: '',
            placeAlias: '',
            placeIds: [],
            sbcjqyList: [],
          },
        },
      },
      tableList: [],
      taskName: '',
      schemeName: '',
      alreadyConfigCount: 0,
      defaultStatisticalModelBo: {
        statisticalModel: [1, 2], //统计模式 2.行政区划 1.组织机构 3.设备标签
        tagIds: [],
      },
      storeTableList: [],
    };
  },
};
