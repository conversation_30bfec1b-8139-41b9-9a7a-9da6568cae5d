<template>
  <div class="knowledgesearch height-full" :class="{ 'has-bg': bigSearchShow }">
    <div class="knowledgesearch-start" v-if="bigSearchShow">
      <p class="knowledgesearch-title">
        <i class="icon-font icon-zhishijiansuo"></i>
        <span>知识搜索</span>
      </p>
      <p class="f-16 base-text-color t-center mt-sm mb-lg">
        约<span class="font-blue-color f-24"> {{ totalCount }} </span>条知识
      </p>
      <big-input
        class="big-input"
        v-model="searchValue"
        placeholder="请输入知识名称、内容或知识目录进行检索"
        @startSearch="startSearch"
      ></big-input>
    </div>
    <search-detail
      v-else
      :default-search-value="searchValue"
      :params="params"
      ref="searchDetail"
      @onBack="onBack"
    ></search-detail>
  </div>
</template>
<script>
import knowledgebase from '@/config/api/knowledgebase';
export default {
  name: 'knowledgesearch',
  data() {
    return {
      searchValue: '',
      totalCount: 0,
      bigSearchShow: true,
      params: {},
    };
  },
  created() {
    this.queryKnowledgeDataTotal();
  },
  activated() {
    let { id } = this.$route.query;
    if (id) {
      this.bigSearchShow = false;
    }
    this.params = {
      questionIdList: id ? [id] : [],
    };
  },
  mounted() {},
  methods: {
    onBack() {
      this.bigSearchShow = true;
      this.searchValue = '';
    },
    startSearch() {
      if (!this.searchValue) {
        this.$Message.warning('请输入知识名称、内容或知识目录进行检索');
        return;
      }
      this.$router.push({
        name: 'knowledgesearch',
        query: {},
      });
      this.bigSearchShow = false;
    },
    async queryKnowledgeDataTotal() {
      try {
        let { data } = await this.$http.get(knowledgebase.queryKnowledgeDataTotal);
        this.totalCount = data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    BigInput: require('./big-input.vue').default,
    SearchDetail: require('./search-detail/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .knowledgesearch {
    &.has-bg {
      background: url('~@/assets/img/knowledgebase/search-bg.png') no-repeat;
      background-size: cover;
    }
    .knowledgesearch-start {
      .knowledgesearch-title {
        background: url('~@/assets/img/knowledgebase/search-button-bg.png') no-repeat;
        background-size: cover;
        color: #fff;
      }
    }
  }
}
[data-theme='deepBlue'] {
  .knowledgesearch {
    .knowledgesearch-start {
      .knowledgesearch-title {
        background: url('~@/assets/img/knowledgebase/search-button-bg-deepBlue.png') no-repeat;
        background-size: contain;
      }
    }
  }
}
.f-24 {
  font-size: 24px;
}
.knowledgesearch {
  position: relative;
  background: var(--bg-content);
  &.has-bg {
    background: url('~@/assets/img/knowledgebase/search-bg-light.png') no-repeat;
    background-size: cover;
  }
  .knowledgesearch-start {
    position: absolute;
    left: 50%;
    top: 30%;
    transform: translate(-50%, -50%);
    .knowledgesearch-title {
      font-size: 30px;
      width: 274px;
      height: 82px;
      line-height: 82px;
      text-align: center;
      font-weight: bold;
      background: url('~@/assets/img/knowledgebase/search-button-bg-light.png') no-repeat;
      background-size: contain;
      color: var(--color-content);
      margin: 0 auto;
      vertical-align: middle;
      .icon-zhishijiansuo {
        font-size: 30px;
        margin-right: 10px;
        color: var(--bg-btn-primary);
      }
    }
    .font-blue-color {
      color: var(--color-bluish-green-text);
    }
    .big-input {
      width: 800px;
    }
  }
}
</style>
