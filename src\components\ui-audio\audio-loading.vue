<!--
 * @Date: 2025-02-27 16:40:24
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 16:50:32
 * @FilePath: \icbd-view\src\components\ui-audio\audio-loading.vue
-->
<template>
  <div class="loading-box">
    <div class="audio-box"></div>
    <div class="loading animation"></div>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.loading-box {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .audio-box {
    width: 44px;
    height: 64px;
    background-image: url("~@/assets/img/audio.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
  .loading {
    position: absolute;
    top: 0;
    width: 100%;
    height: 100%;
    background-image: url("~@/assets/img/loading.png");
    background-repeat: no-repeat;
    background-size: cover;
  }
}
.animation {
  animation: rotation 3s linear infinite;
}
@keyframes rotation {
  from {
    transform: rotateZ(0deg);
  }

  to {
    transform: rotateZ(360deg);
  }
}
</style>
