<template>
  <div class="screen-box">
    <ui-label class="mb-md" align="right" label="行政区划" :width="80">
      <api-area-tree
        class="ml-sm"
        :select-tree="selectTree"
        @selectedTree="selectedArea"
        placeholder="请选择行政区划"
      ></api-area-tree>
    </ui-label>
    <ui-label class="mb-md" align="right" label="点位类型" :width="80">
      <Select class="select-width ml-sm" v-model="searchData.sbdwlx" clearable placeholder="请选择点位类型">
        <Option
          v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
          :key="'sbdwlx' + bdindex"
          :value="sbdwlxItem.dataKey"
          >{{ sbdwlxItem.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="mb-md" align="right" label="功能类型" :width="80">
      <Select class="select-width ml-sm" v-model="searchData.sbgnlx" clearable placeholder="请选择功能类型">
        <Option
          v-for="(sbgnlxItem, bdindex) in dictData['propertySearch_sbgnlx']"
          :key="'sbgnlx' + bdindex"
          :value="sbgnlxItem.dataKey"
          >{{ sbgnlxItem.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="mb-md" align="right" label="空间信息异常" :width="80">
      <Select
        class="select-width ml-sm"
        v-model="searchData.errorMessageList"
        clearable
        multiple
        :max-tag-count="1"
        placeholder="请选择空间信息异常"
      >
        <Option
          v-for="(errortem, seindex) in dictData['device_space_error_type']"
          :key="'error' + seindex"
          :value="errortem.dataKey"
          >{{ errortem.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="mb-md" align="right" label="维护单位" :width="80">
      <Input v-model="searchData.whdw" class="select-width ml-sm" placeholder="请输入维护单位" clearable></Input>
    </ui-label>
    <div class="screen-box-footer">
      <Button type="default" class="mr-sm btn-width" @click="handleReset">重置</Button>
      <Button type="primary" class="btn-width" @click="handleSave">确 定</Button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'screen-box',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
  },
  async mounted() {
    await this.copySearchDataMx(this.searchData);
  },
  data() {
    return {
      selectTree: { regionCode: '' },
      searchData: {
        civilCode: '', // 行政区划
        sbdwlx: '', // 点位类型
        sbgnlx: '', // 功能类型
        errorMessageList: [], // 空间信异常
        whdw: '', //维护单位
      },
    };
  },
  methods: {
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    handleReset() {
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('search', this.searchData);
      });
    },
    handleSave() {
      this.$emit('search', this.searchData);
    },
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .screen-box {
    background: #041d42;
    border: 1px solid #2967c8;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .screen-box {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
  }
}

.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.screen-box {
  position: absolute;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  top: 150px;
  left: 250px;
  width: 347px;
  height: 325px;
  z-index: 9999;
  &-footer {
    display: flex;
  }
}

.btn-width {
  .flex;
  width: 48px !important;
  padding: 0 !important;
  font-size: 12px !important;
}

@{_deep} .select-width {
  width: 230px !important;
}
</style>
