<template>
  <ul class="tag">
    <li :class="{ active: curTag == index }" v-for="(item, index) in list" :key="index" @click="tagChange(index)">
      {{ item }}
    </li>
  </ul>
</template>
<script>
export default {
  name: 'tasktracking',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      curTag: 0,
    };
  },
  created() {},
  methods: {
    tagChange(index) {
      this.curTag = index;
      this.$emit('tagChange', index, this.list[index]);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag {
  // width: 500px;
  // overflow: hidden;
  li {
    float: left;
    text-align: center;
    color: var(--color-switch-tag-tab);
    border: 1px solid var(--border-switch-tag-tab);
    padding: 6px 16px;
    margin-right: -1px;
    cursor: pointer;
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
    }
  }
  .active {
    background: var(--color-switch-tab-active);
    border: 1px solid var(--color-switch-tab-active);
    color: #fff;
    cursor: default;
  }
}
</style>
