<template>
  <div class="trajectory-wrap">
    <div class="trajectory">
      <ui-card title="感知轨迹" class="m-b20" :padding="20">
        <div class="left">
          <div>
            <label>类型:</label>
            <type-tag-list
              :typeTagList="typeTagList"
              @choseTypeHandler="choseTypeHandler"
            ></type-tag-list>
          </div>
          <div class="padding20">
            <label>时间:</label>
            <ui-quick-date
              class="input-width"
              ref="searchQuickDateRef"
              v-model="searchParams.dataRange"
              type="month"
              border
              @change="dataRangeHandler"
            />
          </div>
          <div class="padding20">
            <label>人脸:</label>
            <Select
              v-model="searchParams.vid"
              key="vid"
              class="input-width"
              clearable
              :disabled="!type.includes(0)"
            >
              <Option v-for="item in faceList" :value="item" :key="item">{{
                item
              }}</Option>
            </Select>
          </div>
          <div class="padding20">
            <label>车辆:</label>
            <Select
              v-model="searchParams.plateNo"
              key="plateNo"
              class="input-width"
              clearable
              :disabled="!type.includes(1)"
            >
              <Option v-for="item in vehicleList" :value="item" :key="item">{{
                item
              }}</Option>
            </Select>
          </div>
          <div class="padding20">
            <Button type="primary" class="btn-search" @click="search"
              >查询</Button
            >
            <Button @click="reset">重置</Button>
          </div>
          <TimeLine
            :timelineList="positionPoints"
            :currentClickIndex="currentClickIndex"
            @chooseTimeline="chooseTimeline"
            @handleReachBottom="handleReachBottom"
            :loading="loading"
            :height="540"
          />
        </div>
        <div class="right">
          <mapBase
            ref="map"
            :mapLayerConfig="{
              showStartPoint: false,
              tracing: true,
            }"
            :sectionName="sectionName"
            :currentClickIndex="currentClickIndex"
            :positionPoints="positionPoints"
            cutIcon="track"
            @chooseMapItem="chooseTimeline"
            @onload="mapOnload"
          />
        </div>
      </ui-card>
    </div>
    <div class="trajectory">
      <ui-card title="娱乐场所轨迹" :padding="20">
        <div slot="extra" class="extra">
          <ui-quick-date
            ref="travelQuickDateRef"
            style="text-align: right"
            v-model="trajectoryInfo.dataRange"
            type="month"
            border
            @change="travelDataRangeHandler"
          />
        </div>
        <div class="travel-info">
          <TimelineLevel ref="timeLineSelect" :timeLineList="timeLineList" />
          <div class="list">
            <div
              v-for="(item, index) in travelTypeList"
              :key="index"
              :class="{ active: item.key === travelTypeNameKey.key }"
              class="ivu-tabs-tab"
              @click="tabClick(item)"
            >
              <span>{{ item.travelName }}</span>
            </div>
          </div>
          <ui-table
            ref="table"
            :loading="tableLoading"
            :columns="columnsMap[travelTypeNameKey?.value]?.list || []"
            :data="tableData"
          ></ui-table>
          <div class="page-content">
            <ui-page
              :current="trajectoryPageInfo.pageNumber"
              :total="total"
              :page-size="trajectoryPageInfo.pageSize"
              @pageChange="pageChange"
              @pageSizeChange="pageSizeChange"
            ></ui-page>
          </div>
        </div>
      </ui-card>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import {
  getPersonBaseInfo,
  getVehicleList,
  getPerceivedTrajectory,
  onlineRecordPageList,
} from "@/api/activityTrack";
import mapBase from "@/components/map/index.vue";
import TypeTagList from "@/views/holographic-archives/components/type-tag-list.vue";
import TimeLine from "@/views/juvenile-archives/components/time-line.vue";
import TimelineLevel from "@/views/juvenile-archives/components/time-line-level.vue";
import {
  getConfigPlaceSecondLevels,
  getAlarmDetailPageList,
} from "@/api/monographic/juvenile.js";
export default {
  components: {
    mapBase,
    TypeTagList,
    TimeLine,
    TimelineLevel,
  },
  props: {},
  data() {
    return {
      typeTagList: [
        {
          name: "人脸",
          key: 0,
          checked: true,
        },
        {
          name: "车辆",
          key: 1,
          checked: true,
        },
        // {
        //   name: 'IMSI',
        //   key: 2,
        //   checked: true
        // }
      ],
      columnsMap: {
        default: {
          list: [
            { title: "序号", width: 68, type: "index", key: "index" },
            { title: "出现时间", key: "absTime" },
            { title: "出现次数", key: "kssj" },
            { title: "报警设备", key: "deviceId" },
            { title: "报警地址", key: "deviceName" },
          ],
        },
        internetBar: {
          list: [
            { title: "序号", width: 68, type: "index", key: "index" },
            { title: "网点名称", width: 311, key: "yycsDwmc" },
            { title: "上机时间", width: 300, key: "kssj" },
            { title: "下机时间", width: 300, key: "jssj" },
            { title: "网吧地址", minWidth: 570, key: "yycsDzmc" },
          ],
        },
      },
      tableData: [],
      // 字段隐射处理
      trajectoryMapFiled: {
        hotel: {
          roomNumber: "房间号",
          hotelAddress: "酒店地址",
        },
      },
      travelTypeList: [],
      currentClickIndex: -1, // 当前点击的轨迹节点
      travelTypeNameKey: {},
      positionPoints: [],
      faceList: [],
      vehicleList: [],
      // 活动轨迹参数
      searchParams: {
        dataType: 1,
        dataRange: 2,
        archiveNo: this.$route.query.archiveNo,
        imsi: "",
        plateNo: "",
        vid: "",
        startDate: "",
        endDate: "",
        type: [],
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 200,
      },
      loading: false,
      // 出行轨迹参数
      trajectoryInfo: {
        archiveNo: this.$route.query.archiveNo,
        dataRange: 2,
        startDate: "",
        endDate: "",
      },
      trajectoryPageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      trajectoryAllPageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableLoading: false,
      sectionName: "",
      timeLineList: [],
      scrollTotal: 0,
      total: 0,
    };
  },
  computed: {
    type() {
      const checkedList = this.typeTagList.map((e) => {
        return e.checked ? e.key : undefined;
      });
      let list = [];
      checkedList.forEach((e) => {
        if (e === 0) {
          list.push(e);
        } else {
          if (e) {
            list.push(e);
          }
        }
      });
      return list;
    },
  },
  async mounted() {
    //#region 初始时间处理
    const date1 = this.$refs.searchQuickDateRef.getDate();
    this.searchParams.startDate = date1.startDate;
    this.searchParams.endDate = date1.endDate;
    const date2 = this.$refs.travelQuickDateRef.getDate();
    this.trajectoryInfo.startDate = date2.startDate;
    this.trajectoryInfo.endDate = date2.endDate;
    //#endregion
    await this.getEntertainmentList();
    this.init();
    this.getTravelInfoPageList();
    if (this.travelTypeNameKey.value === "internetBar") {
      this.trajectoryHandler();
    } else {
      this.getEntertainmentAlarm(this.travelTypeNameKey.key);
    }
  },
  methods: {
    /**
     * @description: 地图加载完成
     * 把该放在mounted中的依赖地图加载完成的操作放到这里，避免报错
     */
    mapOnload() {
      this.getPerceivedTrajectory();
      this.$nextTick(() => {
        this.$refs.map.mapidlerWheel();
      });
    },
    async init() {
      await this.getPersonBaseInfo();
      await this.getVehicleList();
    },
    // 人脸
    getPersonBaseInfo() {
      const { archiveNo } = this.$route.query;
      const params = {
        archiveNo,
        dataType: 1,
      };
      getPersonBaseInfo(params).then((res) => {
        if (res.code === 200) {
          const { vids } = res.data;
          this.faceList = [...vids];
        }
      });
    },
    // 车辆
    getVehicleList() {
      const { archiveNo, dataRange, dataType, startDate, endDate } =
        this.searchParams;
      getVehicleList({
        archiveNo,
        dataRange,
        dataType,
        startDate,
        endDate,
      }).then((res) => {
        if (res.code === 200) {
          const { data = [] } = res;
          this.vehicleList = data;
        }
      });
    },

    /**
     * @description: 活动轨迹 - 切换时间类型
     * @param {object} val 起止时间
     */
    dataRangeHandler(val) {
      this.searchParams.startDate = val.startDate;
      this.searchParams.endDate = val.endDate;
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = 200;
      this.getVehicleList();
    },

    /**
     * @description: 出行轨迹 - 切换时间类型
     * @param {object} val 起止时间
     */
    travelDataRangeHandler(val) {
      this.trajectoryInfo.startDate = val.startDate;
      this.trajectoryInfo.endDate = val.endDate;
      this.timeLineList = [];
      this.$refs.timeLineSelect.reset();
      // 当前表格
      if (this.travelTypeNameKey.value === "internetBar") {
        this.trajectoryHandler();
      } else {
        this.getEntertainmentAlarm(this.travelTypeNameKey.key);
      }
      // 全部轨迹
      this.getTravelInfoPageList();
    },
    // 感知轨迹
    getPerceivedTrajectory() {
      this.$refs.map.resetMarker();
      this.$refs.map.resetSpeed();
      const { searchParams, type, pageInfo } = this;
      this.loading = true;
      getPerceivedTrajectory({ ...searchParams, type, ...pageInfo })
        .then((res) => {
          if (res.code === 200) {
            res.data.entities.map((item, index) => {
              item.id = `${index}_${item.vid}`;
            });
            this.scrollTotal = res.data.total;
            this.positionPoints = res.data.entities;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 底部滚动加载更多
    handleReachBottom() {
      if (this.loading) return;
      if (this.total <= this.pageInfo.pageSize) return;
      if (
        this.pageInfo.pageNumber >=
        Math.ceil(this.total / this.pageInfo.pageSize)
      )
        return;
      this.pageInfo.pageNumber++;
      this.getPerceivedTrajectory();
    },
    search() {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = 200;
      this.positionPoints = [];
      this.getPerceivedTrajectory();
    },
    // 重置
    reset() {
      this.typeTagList.forEach((e) => {
        e.checked = true;
      });
      this.searchParams = {
        ...this.searchParams,
        dataRange: 2,
        plateNo: "",
        vid: "",
        startDate: "",
        endDate: "",
      };
      this.$nextTick(() => {
        this.getPerceivedTrajectory();
      });
    },
    //全部娱乐场所轨迹
    async getTravelInfoPageList() {
      let placeArr = this.travelTypeList.map((e) => e.key);
      // 弹出最后的上网记录
      placeArr.pop();
      const { startDate, endDate } = this.trajectoryInfo;
      let param = {
        bizAlarmType: 1,
        endAlarmTime: endDate,
        startAlarmTime: startDate,
        secondLevels: placeArr,
        ...this.trajectoryAllPageInfo,
        idCardNo: this.$route.query.archiveNo,
      };
      let { data } = await getAlarmDetailPageList(param);
      let enArr = data.entities || [];
      // 娱乐场所的轨迹
      this.timeLineList = enArr.map((item) => {
        return { ...item, dataType: 1 };
      });
      let { data: interData } = await onlineRecordPageList({
        ...this.trajectoryInfo,
        ...this.trajectoryPageInfo,
      });
      let intArr = interData.entities || [];
      // 上网记录轨迹
      intArr = intArr.map((item) => {
        return { ...item, dataType: 5 };
      });
      this.timeLineList.push(...intArr);
    },
    // 上网记录
    trajectoryHandler() {
      this.tableLoading = true;
      onlineRecordPageList({
        ...this.trajectoryInfo,
        ...this.trajectoryPageInfo,
      })
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data.entities;
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    // 娱乐场所告警
    getEntertainmentAlarm(placeCode) {
      this.tableLoading = true;
      const { startDate, endDate } = this.trajectoryInfo;
      let param = {
        bizAlarmType: 1,
        endAlarmTime: endDate,
        startAlarmTime: startDate,
        secondLevels: placeCode ? [placeCode] : [],
        ...this.trajectoryAllPageInfo,
        idCardNo: this.$route.query.archiveNo,
      };
      getAlarmDetailPageList(param)
        .then(({ data }) => {
          this.trajectoryPageInfo.pageSize = data.pageSize;
          this.trajectoryPageInfo.pageNumber = data.pageNumber;
          this.total = data.total;
          this.tableData = data.entities || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    tabClick(item) {
      this.travelTypeNameKey = item;
      if (item.value == "default") {
        this.getEntertainmentAlarm(item.key);
        return;
      }
      this.trajectoryPageInfo.pageNumber = 1;
      this.trajectoryHandler();
    },
    chooseTimeline(i) {
      if (i > -1) {
        const { dataType, geoPoint } = this.positionPoints[i];
        if (!geoPoint || !geoPoint.lat || !geoPoint.lon) {
          this.$Message.warning("经纬度信息不全");
          return;
        }
        const typeMap = {
          0: "face",
          1: "vehicle",
          2: "imsi",
        };
        this.sectionName = typeMap[dataType];
      }
      this.$nextTick(() => {
        this.currentClickIndex = i;
      });
    },
    // 选择类型
    choseTypeHandler(i) {
      const { checked } = this.typeTagList[i];
      this.$nextTick(() => {
        this.typeTagList[i].checked = !checked;
        if (!this.type.includes(0)) {
          this.searchParams.vid = "";
        }
        if (!this.type.includes(1)) {
          this.searchParams.plateNo = "";
        }
        if (!this.type.includes(2)) {
          this.searchParams.imsi = "";
        }
      });
    },
    pageChange(val) {
      this.trajectoryPageInfo.pageNumber = val;
      this.trajectoryHandler();
    },
    pageSizeChange(val) {
      this.trajectoryPageInfo.pageSize = val;
      this.trajectoryHandler();
    },
    async getEntertainmentList() {
      const { data } = await getConfigPlaceSecondLevels();
      let arr = [];
      data.forEach((item) => {
        let icon = item.icon;
        try {
          if (icon) {
            icon = JSON.parse(icon);
          } else {
            icon = {
              font_class: "hotel",
              color: "#EB8A5D",
            };
          }
        } catch (e) {
          icon = {
            font_class: "hotel",
            color: "#EB8A5D",
          };
        }
        let val = {
          key: item.typeCode,
          travelName: item.typeName,
          value: "default",
          icon: icon["font_class"],
          color: icon["color"],
        };
        arr.push(val);
      });
      arr.push({
        travelName: "上网记录",
        key: "internetBar",
        value: "internetBar",
      });
      this.travelTypeNameKey = arr[0];
      this.travelTypeList = arr;
    },
  },
};
</script>
<style lang="less" scoped>
.trajectory-wrap {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  width: 100%;
  overflow: hidden;
  overflow-y: auto;
  padding: 16px 10px 10px;
}
.trajectory {
  display: flex;
  flex: 1;
  margin-bottom: 10px;
  height: 100%;
  /deep/.card-content {
    display: flex;
    flex: 1;
    flex-direction: row;
    padding: 20px 20px 0 20px !important;
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    width: 335px;
    height: calc(~"(100% + 5px)");
    // max-height: calc(~'(100% + 5px)');
    // overflow: hidden;

    > div {
      display: flex;
      align-items: center;
      // margin-bottom: 10px;
      > label {
        font-size: 14px;
        margin-right: 10px;
      }
    }
    .input-width {
      width: 270px;
    }
    .btn-search {
      width: 228px;
      margin-right: 10px;
    }
    .padding20 {
      padding-top: 20px;
    }
  }
  .right {
    margin-top: -26px;
    width: calc(~"(100% - 300px)");
    // .map-box {
    //   // height: calc(~'(100% +28px)');
    // }
  }
}
.m-b20 {
  margin-bottom: 20px;
}
.extra {
  margin-top: 12px;
  margin-right: 30px;
}
.travel-info {
  width: 100%;
  position: relative;
  .list {
    height: 34px;
    line-height: 34px;
    margin-bottom: 20px;
    .ivu-tabs-tab {
      float: left;
      border: 1px solid #2c86f8;
      border-right: none;
      padding: 0 15px;
      color: #2c86f8;
      &:hover {
        background: #2c86f8;
        color: #ffffff;
        cursor: pointer;
      }
      &:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        border-right: none;
      }
      &:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-right: 1px solid #2c86f8;
      }
    }
    .active {
      background: #2c86f8;
      color: #fff;
    }
  }
  .page-content {
    height: 50px;
    box-sizing: border-box;
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
