<template>
  <div class="search card-border-color">
    <Form :inline="true">
      <div class="general-search">
        <div class="input-content">
          <div class="input-content-row">
            <FormItem label="分组路径:">
              <Input v-model="formData.groupRoute" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="设备名称:">
              <Input v-model="formData.deviceName" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="数据类型:">
              <Select v-model="formData.dataType" placeholder="请选择" multiple>
                <Option :value="1">人脸</Option>
                <Option :value="2">车辆</Option>
                <Option :value="3">非机动车</Option>
                <Option :value="4">人体</Option>
              </Select>
            </FormItem>
            <FormItem label="数据总量状态:">
              <Select
                v-model="formData.dataState"
                placeholder="请选择"
                multiple
              >
                <Option :value="1">减少百分之20-50</Option>
                <Option :value="2">减少百分之50-80</Option>
                <Option :value="3">减少百分之80以上</Option>
                <Option :value="4">增长百分之20-50</Option>
                <Option :value="5">增长百分之50-80</Option>
                <Option :value="6">增长百分之80以上</Option>
                <Option :value="7">无数据</Option>
                <Option :value="8">正常</Option>
              </Select>
            </FormItem>
            <FormItem label="统计数据日期:">
              <Select v-model="formData.dataTime" placeholder="请选择">
                <Option
                  v-for="item in dateTimeList"
                  :key="item.value"
                  :value="item.value"
                  >{{ item.label }}</Option
                >
              </Select>
            </FormItem>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" @click="search">查询</Button>
          <Button type="default" @click="resetForm">重置</Button>
        </div>
      </div>
    </Form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        groupRoute: "",
        deviceName: "",
        dataType: [],
        dataState: [],
        dataTime: 1,
      },
      dateTimeList: [
        { value: 1, label: "2023-09-07" },
        { value: 2, label: "2023-09-06" },
        { value: 3, label: "2023-09-05" },
        { value: 4, label: "2023-09-04" },
        { value: 5, label: "2023-09-03" },
        { value: 6, label: "2023-09-02" },
        { value: 7, label: "2023-09-01" },
        { value: 8, label: "2023-08-31" },
        { value: 9, label: "2023-08-30" },
        { value: 10, label: "2023-08-29" },
        { value: 11, label: "2023-08-28" },
        { value: 12, label: "2023-08-27" },
        { value: 13, label: "2023-08-26" },
        { value: 14, label: "2023-08-25" },
        { value: 15, label: "2023-08-24" },
      ],
    };
  },
  created() {},
  methods: {
    search() {
      let searchForm = {
        groupRoute: this.formData.groupRoute,
        deviceName: this.formData.deviceName,
        dataType: this.formData.dataType,
        dataState: this.formData.dataState,
        dataTime: this.formData.dataTime,
      };
      this.$emit("searchInfo", searchForm);
    },
    resetForm() {
      this.formData = {
        groupRoute: "",
        deviceName: "",
        dataType: [],
        dataState: [],
        dataTime: 1,
      };
      this.$emit("searchInfo", this.formData);
    },
  },
};
</script>
<style lang="less" scoped>
.btn-group {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
  justify-content: flex-end;
  flex: 1;
}
.search {
  padding: 16px 20px 0;
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;
  .ivu-form-inline {
    width: 100%;
  }
  .ivu-form-item {
    margin-bottom: 16px;
    margin-right: 30px;
    display: flex;
    align-items: center;
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      max-width: 100px;
    }
    /deep/ .ivu-form-item-content {
      display: flex;
    }
  }
  .general-search {
    display: flex;
    width: 100%;
    .input-content {
      width: 75%;
      display: flex;
      flex-wrap: wrap;
      .input-content-row {
        display: flex;
      }
    }
  }
}
</style>
