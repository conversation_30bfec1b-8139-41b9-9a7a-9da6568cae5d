<template>
    <div class="fileFolderTreeAre">
        <el-tree class="tree" :data="treeData" :props="defaultProps" :expand-on-click-node="false" @node-click="clickOnNodeFunc">
            <span class="custom-tree-node" slot-scope="{ node, data }">
                <div class="title">
                    <i v-if="data.type == 1" class="iconfont icon-fenju color-bule"></i>
                    <span :title="node.label">{{ node.label }}</span>
                </div>
                <div class="toolbars">
                    <i v-if="data.type === 1 && node.level < 7" class="iconfont icon-tianjia" title="添加文件夹" @click.prevent="addFolder(data)"></i>
                    <i v-if="data.type === 1" class="iconfont icon-upload" title="上传文件" @click.prevent="addFile(data)"></i>
                    <i v-if="data.type === 1" class="iconfont icon-bianji" title="编辑名称" @click.prevent="editName(data)"></i>
                    <i class="iconfont icon-shanchu1" title="删除文件/文件夹" @click.prevent="deleteFF(data)"></i>
                </div>
            </span>
        </el-tree>
        <edit-tree-pop ref="popModel" @editTreePopClose="editTreePopCloseFunc"></edit-tree-pop>
    </div>
</template>

<script>
import editTreePop from "./editTreePop.vue";
import treeDataUtil from "../mixins/treeDataUtil";

export default {
    name: "fileFolderTree",
    props: {
        treeData: {
            type: Array,
            default: function() {
                return [];
            }
        },
        taskId: ""
    },
    mixins: [treeDataUtil],
    data() {
        return {
            isShowEditTreePop: false,
            selectDevs: "",
            editNode: {},
            editType: "",
            isRootOpen: false,
            defaultProps: {
                children: 'children',
                label: 'name'
            }
        };
    },
    components: {
        editTreePop
    },
    methods: {
        addFolder(record) {
            this.editNode = record;
            this.editType = "addFolder";
            this.$refs.popModel.open();
        },
        addFile(record) {
            this.editNode = record;
            this.editType = "addFile";
            this.$emit("treeEditAddFile", record);
        },
        editName(record) {
            this.editNode = record;
            this.editType = "editName";
            this.$refs.popModel.open(record.name);
        },
        deleteFF(record) {
            this.$emit("treeEditDelete", record);
        },
        editTreePopCloseFunc(param) {
            this.isShowEditTreePop = false;
            if (param.close) return false;
            if (this.editType === "addFolder") {
                this.$emit("treeEditChangeFolder", {
                    value: param.value,
                    id: this.editNode.id
                });
            } else if (this.editType === "editName") {
                this.$emit("treeEditChangeName", {
                    value: param.value,
                    id: this.editNode.id
                });
            }
        },
        clickOnNodeFunc(record) {
            if (record.type && record.type === 2) return false;
            this.$emit("treeClickOnNode", record);
        }
    }
};
</script>

<style lang="less">
.taskTreeList {
    height: 100%;
    overflow-y: auto;
}
.fileFolderTreeAre {
    .fileStructureIcon,
    .fileStructureIconEdit {
        background-size: 0;
        font-weight: normal;
    }
    .custom-tree-node {
        display: flex;
        align-items: center;
        width: 100%;
        overflow: hidden;
        .color-bule {
            color: #2C86F8;
        }
        .iconfont {
            margin-right: 5px;
        }
        .title {
            flex: 1;
            display: flex;
            align-items: center;
            overflow: hidden;
            span{
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .toolbars {
            display: flex;
            align-items: center;
            opacity: 0;
        }
        &:hover {
            .toolbars {
                opacity: 1;
            }
        }
    }
}

.np-tree-node-list li > .node .fileStructureIconEdit {
    color: #666;
}
.np-tree-node-list li > .node .fileStructureIconEdit:hover {
    color: #2C86F8;
}
.np-tree-node-list .icon {
    margin-top: 0px;
}
.skinDark .np-tree-node-list li > .node .fileStructureIconEdit {
    color: #a6e0fe;
}
.skinDark .np-tree-node-list li > .node .fileStructureIconEdit:hover {
    color: #fff;
}
</style>

