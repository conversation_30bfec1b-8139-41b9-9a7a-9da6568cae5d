<!--
    * @FileDescription: 时空碰撞-车辆
    * @Author: H
    * @Date: 2023/4/11
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="vehicle">
        <page :mapType='mapType'></page>
    </div>
</template>

<script>
import page from './index.vue';
export default {
    name: 'timeSpaceColli-vehicle',
    components:{
        page   
    },
    data () {
        return {
            mapType: {
                "mapName":'vehicle',
                'sectionName': 'vehicleMap', // 模态框类型
                'seleType': 'Camera_Vehicle', //框选类型
            }
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {}
}
</script>

<style lang='less' scoped>
.vehicle{
    width: 100%;
    height: 100%;
}
</style>
