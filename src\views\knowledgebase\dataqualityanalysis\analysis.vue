<template>
  <ui-modal v-model="visible" title="设备分析" :width="80">
    <ui-label label="检测任务">
      <Select
        v-model="taskSchemeId"
        placeholder="请选择检测任务"
        class="width-lg mb-lg"
        @on-change="selectTask"
        filterable
      >
        <Option v-for="(item, index) in taskList" :key="index" :label="item.taskName" :value="item.id">
          {{ item.taskName }}
        </Option>
      </Select>
    </ui-label>
    <div v-for="(item, index) in detailFormList" :key="index">
      <ui-label class="inline" label="">
        <Select v-model="item.indexId" placeholder="请选择评测指标" class="width-lg mb-lg mr-sm" filterable>
          <Option v-for="(item, index) in taskIndexIdList" :key="index" :label="item.indexName" :value="item.indexId">
            {{ item.indexName }}
          </Option>
        </Select>
        <Select v-model="frequency" placeholder="请选择" class="width-lg mb-lg mr-sm" filterable>
          <Option v-for="(item, index) in frequencyList" :key="index" :label="item.label" :value="item.value">
            {{ item.label }}
          </Option>
        </Select>
        <template v-if="frequency === 'time'">
          <Select v-model="frequency" placeholder="请选择" class="width-lg mb-lg mr-sm" filterable>
            <Option v-for="(item, index) in frequencyList" :key="index" :label="item.label" :value="item.value">
              {{ item.label }}
            </Option>
          </Select>
        </template>
        <template v-else>
          <Input class="width-xs mb-lg" v-model="item.checkNum"></Input>
          <span class="base-text-color inline unit ml-xs"> 次 </span>
        </template>
      </ui-label>
      <ui-label class="inline" label="检测结果">
        <Select v-model="item.result" placeholder="请选择检测结果" class="width-lg ml-sm mb-lg">
          <Option v-for="(item, index) in resultList" :key="index" :label="item.label" :value="item.value">
            {{ item.label }}
          </Option>
        </Select>
        <Select v-model="item.flag" placeholder="请选择" class="width-mini ml-sm mb-lg">
          <Option v-for="(item, index) in signList" :key="index" :label="item.label" :value="item.value">
            {{ item.label }}
          </Option>
        </Select>
        <Input class="width-xs ml-sm mb-lg" v-model="item.flagNum"></Input>
        <span class="base-text-color inline unit ml-xs"> 次 </span>
      </ui-label>
      <i v-if="index === detailFormList.length - 1" class="icon-font icon-tianjia ml-sm" title="添加" @click="add"></i>
      <i v-if="detailFormList.length > 1" class="icon-font icon-jian ml-sm" title="移除" @click="deleteItem(index)"></i>
    </div>

    <template #footer>
      <Button @click="visible = false">取消</Button>
      <Button type="primary" @click="start">开始运行</Button>
    </template>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  props: {
    value: {},
  },
  data() {
    return {
      visible: false,
      taskSchemeId: '',
      taskList: [],
      taskIndexIdList: [],
      detailFormList: [],
      frequency: 'recent',
      frequencyList: [
        // {
        //   label: '检测时间',
        //   value: 'time',
        // },
        {
          label: '最近检测',
          value: 'recent',
        },
      ],
      resultList: [
        {
          label: '合格',
          value: '1',
        },
        {
          label: '不合格',
          value: '2',
        },
      ],
      signList: [
        {
          label: '>',
          value: '1',
        },
        {
          label: '<',
          value: '2',
        },
        {
          label: '=',
          value: '3',
        },
      ],
    };
  },
  created() {
    this.getListTaskSchemes();
  },
  methods: {
    async start() {
      try {
        const res = await this.$http.post(equipmentassets.analysisSave, {
          taskId: this.taskSchemeId,
          taskName: this.taskList.find((row) => row.id === this.taskSchemeId).taskName,
          detailFormList: this.detailFormList,
        });
        this.$Message.success(res.data.msg);
        this.visible = false;
        this.$emit('startAnalysis');
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.taskList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    async init() {
      try {
        const res = await this.$http.get(equipmentassets.analysisView);
        let defaultDetailFormList = [
          {
            indexId: '',
            checkNum: '',
            result: '',
            flag: '',
            flagNum: '',
          },
        ];
        this.taskSchemeId = res.data.data.taskId;
        this.detailFormList =
          res.data.data.detailFormList && res.data.data.detailFormList.length
            ? res.data.data.detailFormList
            : defaultDetailFormList;
        await this.selectTask();
      } catch (err) {
        console.log(err);
      }
    },
    async selectTask() {
      try {
        const res = await this.$http.get(equipmentassets.getTaskIndexForAnalysis + this.taskSchemeId);
        this.taskIndexIdList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    add() {
      this.detailFormList.push({
        indexId: '',
        checkNum: '',
        result: '',
        flag: '',
        flagNum: '',
      });
    },
    deleteItem(index) {
      this.detailFormList.splice(index, 1);
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .icon-tianjia,
  .icon-jian {
    color: #ccc;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .icon-tianjia,
  .icon-jian {
    color: #888888;
  }
}
@{_deep} .ivu-modal-body {
  max-height: 500px;
  overflow-y: auto;
  padding: 16px 50px;
}
.unit {
  vertical-align: top;
}
.icon-tianjia,
.icon-jian {
  margin-bottom: 20px;
  font-size: 12px;
}
</style>
