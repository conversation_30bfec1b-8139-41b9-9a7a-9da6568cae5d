<template>
  <ui-modal v-model="modalShow" :r-width="dialogData.rWidth" :title="dialogData.title" list-content @onCancel="handleCancel" @onOk="confirmHandle">
    <div class="select-label-container modal-list-has-footer-content">
      <div class="select-label-content">
        <Form inline ref="formData" :model="formData" class="form">
          <Row>
            <FormItem label="库名称:" prop="libName">
              <Input v-model="formData.libName" placeholder="请输入" @keydown.enter.native="handleQuery">
              <Icon type="ios-search" class="font-16 cursor-p" maxlength="50" slot="suffix" @click.prevent="handleQuery" />
              </Input>
            </FormItem>
          </Row>
        </Form>
        <div class="list">
          <div class="list-title">人员库</div>
          <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox>
          <Checkbox class="list-checkbox" v-model="item._isChecked" @on-change="handleCheckBox($event, item)" v-for="(item, index) in listData" :key="index">
            <i class="iconfont icon-tuceng"></i>
            {{ item.libName }}
          </Checkbox>
        </div>
      </div>
      <div class="preview-select-label-content">
        <div class="info-bar">
          <span>已经选：<span>{{ selectListData.length }}</span> 个</span>
          <span class="del-btn" @click="removeAllHandle">
            <i class="iconfont icon-shanchu"></i>清空
          </span>
        </div>
        <div class="label-container">
          <ul>
            <li v-for="(item,index) in selectListData" :key="index">
              <Checkbox v-model="item._isChecked" @on-change="selectChange($event, item, index)">
                <i class="iconfont icon-tuceng"></i>
                {{item.libName}}
              </Checkbox>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { mapGetters } from 'vuex';
import { queryFaceLibList } from '@/api/target-control'
export default {
  components: {
  },
  props: {
    checkedLabels: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
  },
  data () {
    return {
      modalShow: false,
      formData: {
      },
      dialogData: {
        title: "选择库",
        rWidth: 700,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      selectListData: [],
      listData: [],
      indeterminate: false,
      checkAll: false,
    };
  },
  created () {
  },
  computed: {
    ...mapGetters({
      sbgnlxList: 'dictionary/getSbgnlxList' //摄像机功能类型
    })
  },
  methods: {
    handleQuery () {
      this.init()
    },
    init () {
      let params = {
        // controlStatus: 1,
        'libSource': '2',
        libName: this.formData.libName,
        userId: this.userInfo.id
      }
      queryFaceLibList(params)
        .then(res => {
          let list = res.data || [];
          this.listData = list.map(item => {
            item._isChecked = false;
            return item
          });
          this.tableIsSelect()
        })
    },
    /**
     * table回显
     */
    tableIsSelect () {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.listData;
        // 清空table选中状态
        if (this.selectListData.length == 0) {
          Object.keys(obj).forEach(key => {
            obj[key]._isChecked = false
          })
          return
        }
        // 回显
        Object.keys(obj).forEach(key => {
          var row = this.selectListData.find(i => { return obj[key].id == i.id })
          if (row) {
            obj[key]._isChecked = true
          }
        })
        this.allCheckList();
      }, 20)
    },
    /**
     * 显示model
     */
    show (list = []) {
      this.modalShow = true;
      this.indeterminate = false;
      this.checkAll = false;
      this.selectListData = JSON.parse(JSON.stringify(list)); //防止数据浅拷贝，改变父组件
      this.init()
    },
    // 页数改变
    pageChange (size) {
      this.pageInfo.pageNumber = size
      this.init()
    },
    // 页数量改变
    pageSizeChange (size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.init()
    },
    // 全选
    handleCheckAll () {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;
      if (this.checkAll) {
        this.listData.map(item => {
          item._isChecked = true;
        })
      } else {
        this.listData.map(item => {
          item._isChecked = false;
        })
      }
      this.selectListData = this.listData.filter(item => item._isChecked)
    },
    // 选择改变
    handleCheckBox (event, row) {
      this.allCheckList();
      if (event) {
        row._isChecked = true;
        this.selectListData.push(row)
      } else {
        var num = this.selectListData.findIndex(item => { return item.id == row.id })
        this.selectListData.splice(num, 1)
      }
    },
    // 判断全选框状态
    allCheckList () {
      let checkList = this.listData.filter(item => item._isChecked)
      // 判断全选框状态
      if (checkList.length == this.listData.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (checkList.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    /**
     * 确定按钮
     */
    confirmHandle () {
      this.modalShow = false;
      // 选择的数据
      let list = this.selectListData.map(item => item);
      this.$emit("selectData", list)
      this.$refs['formData'].resetFields();
    },
    // 取消
    handleCancel () {
      this.$refs['formData'].resetFields();
    },
    /**
     * 表格右侧 已选中内容操作
     */
    selectChange ($event, row, index) {
      var obj = this.listData;
      if (row._isChecked) { // 选中
        Object.keys(obj).forEach(key => {
          if (obj[key].id == row.id) {
            obj[key]._isChecked = true
          }
        })
      } else { // 取消选中
        Object.keys(obj).forEach(key => {
          if (obj[key].id == row.id) {
            obj[key]._isChecked = false
          }
        })
      }
      this.$nextTick(() => {
        this.selectListData.splice(index, 1);
        this.allCheckList();
      })
    },
    /**
     * 右侧清空
     */
    removeAllHandle () {
      this.selectListData = [];
      this.listData.map(item => {
        item._isChecked = false;
      })
      this.allCheckList();
    },
    /**
     * 重置表单
     */
    resetForm () {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.$refs['formData'].resetFields();
      this.init()
    },
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}

/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 0px 0px;
  display: flex;
  height: 500px;
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 20px;
    height: inherit;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
    .list {
      margin-top: 0.05208rem;
      overflow: auto;
      height: 418px;
      .list-title {
        font-size: 14px;
        font-weight: 700;
        color: #3d3d3d;
        margin: 10px 0;
      }
      /deep/ .ivu-checkbox-group {
        display: flex;
        flex-direction: column;
      }
      .list-checkbox {
        display: block;
      }
    }
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    height: 420px;
    position: relative;
  }
  .organization {
    width: 240px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 472px;
    overflow: auto;
    border-right: 1px solid #d3d7de;

    .title {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }
  }
  .preview-select-label-content {
    width: 290px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}

.icon-tuceng {
  color: #2c86f8;
}

.form {
  width: 100%;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  /deep/.ivu-select {
    width: 100%;
  }

  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    .ivu-form-item-content {
      flex: 1;
      .ivu-select {
        width: 100%;
      }
    }
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
</style>
