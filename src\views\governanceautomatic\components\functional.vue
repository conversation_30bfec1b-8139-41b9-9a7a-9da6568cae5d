<template>
  <div class="governinfo">
    <Form ref="formValidateRef" class="formInfo" :rules="ruleValidate" :model="formValidate">
      <FormItem class="inline govern-type" label="治理方式：" prop="governType">
        <RadioGroup v-model="formValidate.governanceWay" class="align-flex">
          <Radio label="1" class="mr-lg">根据设备11-13位自动设置功能类型</Radio>
          <Radio label="2" class="mr-lg">根据设备产生的数据（人脸图片/车辆图片/视频流）自动设置功能类型</Radio>
          <Radio label="3">根据数据来源设置功能类型</Radio>
        </RadioGroup>
        <ul class="device-sign-property">
          <li v-for="(item, index) in formValidate.deviceSignPropertyList" :key="index">
            <FormItem label="" v-if="formValidate.governanceWay === '1'">
              <span class="mr-sm base-text-color">国标编码11-13位=</span>
              <Input class="width-sm mr-xs" placeholder="" v-model="item.deviceSign"></Input>
              <span class="mr-sm base-text-color"> ，摄像机功能类型设置为</span>
              <Select
                class="mr-sm width-sm"
                v-model="item.sbgnlxList"
                filterable
                multiple
                allow-create
                transfer
                @on-create="handleSbgnlx"
              >
                <Option v-for="item in sxjgnlx_receive" :value="item.dataKey" :key="item.dataKey">{{
                  item.dataValue
                }}</Option>
              </Select>
              <span class="">
                <span
                  class="addition ml-sm"
                  v-if="formValidate.deviceSignPropertyList.length - 1 === index"
                  @click="toAdd"
                  ><i class="icon-font icon-tree-add f-16 font-other" title="添加"></i
                ></span>
                <span class="cancel ml-sm" v-if="index != 0" @click="toDel(index)"
                  ><i class="icon-font icon-shanchu1 f-14 font-other" title="删除"></i></span
              ></span>
            </FormItem>
          </li>
        </ul>
      </FormItem>
      <FormItem class="inline base-text-color" v-if="formValidate.governanceWay === '2'" prop="day">
        <p>
          <span class="mr-sm">近</span>
          <Input class="width-sm mr-xs" type="number" placeholder="" v-model="formValidate.day"></Input>
          天
        </p>
        <p>有抓拍车辆，摄像机功能类型设置为车辆卡口；</p>
        <p>有抓拍人脸，摄像机功能类型设置为人脸卡口；</p>
        <p>有成功调阅视频流记录，摄像机功能类型设置为普通监控。</p>
      </FormItem>
      <FormItem class="inline base-text-color" v-if="formValidate.governanceWay === '3'">
        <div class="synchro-item mb-15 inline">
          设备数据来源为国标平台
          <!-- <Select
                transfer
                placeholder="请选择国标平台"
                clearable
                class="mr-sm width-sm"
                v-model="item.sbdwlx"
              >
                <Option
                  v-for="(item, index) in propertySearch_sbdwlx"
                  :key="index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select> -->
          ，摄像机功能类型设置为普通监控
        </div>
      </FormItem>
      <FormItem label="更新策略：" prop="updateStragty" class="mt-sm base-text-color">
        <FormItem class="inline">
          <div class="synchro-item mb-15 inline">
            <span>1、{{ `${global.filedEnum.sbgnlx}原始值为空时，自动填充：` }}</span>
            <RadioGroup v-model="formValidate.nullValueSyncStrategy" class="align-flex">
              <Radio label="1">是</Radio>
              <Radio label="2">否</Radio>
            </RadioGroup>
          </div>
        </FormItem>
        <FormItem label="" class="inline pl-9" prop="syncStrategy">
          <div class="synchro-item mb-15 inline">
            <span>2、{{ `${global.filedEnum.sbgnlx}原始值不为空时：` }}</span>
            <RadioGroup v-model="formValidate.syncStrategy" class="align-flex">
              <Radio label="1">覆盖原始值</Radio>
              <Radio label="2">保留原始值，不追加新值</Radio>
              <Radio label="3">保留原始值，追加新值</Radio>
            </RadioGroup>
          </div>
        </FormItem>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import user from '@/config/api/user';
export default {
  props: {
    editInfos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const governTypeValidator = (rule, value, callback) => {
      let passFlag = true;
      let errorMessage = '请填写详细的治理方式';
      if (this.formValidate.governanceWay === '1') {
        this.formValidate.deviceSignPropertyList.forEach((item) => {
          let hasNo = Object.values(item).findIndex((one) => {
            return !one || !one.length;
          });
          hasNo !== -1 ? (passFlag = false) : null;
        });
      }
      if (this.formValidate.governanceWay === '2' && !this.formValidate.day) {
        passFlag = false;
        errorMessage = '请设置天数';
      }
      passFlag ? callback() : callback(new Error(errorMessage));
    };
    const updateStragtyValidator = (rule, value, callback) => {
      if (!this.formValidate.nullValueSyncStrategy || !this.formValidate.syncStrategy) {
        callback(new Error('请选择更新策略'));
      } else {
        callback();
      }
    };
    return {
      sxjgnlx_receive: [],
      propertySearch_sbdwlx: [],
      formValidate: {
        governanceWay: '1', // 1-根据设备11-13位自动设置功能类型
        nullValueSyncStrategy: '1', // 1-是 2-否
        syncStrategy: '1', // 通过视频流检测结果，获取设备在线状态 1-是  2-否
        deviceSignPropertyList: [{ deviceSign: '', sbgnlxList: [] }], //
      },

      modelDisabled: true,
      onlineDisabled: true,
      gbObtain: false,
      sdkObtain: false,
      ruleValidate: {
        updateStragty: [
          {
            required: true,
            trigger: 'change',
            validator: updateStragtyValidator,
          },
        ],
        governType: [
          {
            required: true,
            validator: governTypeValidator,
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {
    this.getDictDatas();
    if (!!this.editInfos.propertyJson && this.editInfos.governanceContent === '4') {
      const propertyJson = JSON.parse(this.editInfos.propertyJson);
      this.formValidate.governanceWay = propertyJson.governanceWay || '1';
      this.formValidate.syncStrategy = propertyJson.syncStrategy || '1';
      this.formValidate.nullValueSyncStrategy = propertyJson.nullValueSyncStrategy || '1';
      this.formValidate.deviceSignPropertyList = propertyJson.deviceSignPropertyList;
      this.formValidate.governanceWay === '2' ? (this.formValidate.day = propertyJson.day) : null;
    }
  },
  updated() {
    let data = JSON.parse(JSON.stringify(this.formValidate));
    data.propertyJson = JSON.stringify(this.formValidate);
    this.$emit('getInfos', data);
  },
  methods: {
    async getDictDatas() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    toAdd() {
      this.formValidate.deviceSignPropertyList.push({
        deviceSign: '',
        sbgnlxList: [],
      });
    },
    toDel(index) {
      this.formValidate.deviceSignPropertyList.splice(index, 1);
    },
    handleSbgnlx() {},
    handleReset() {
      this.$refs['formValidate'].resetFields();
      this.$refs['formValidate1'].resetFields();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.formInfo {
  .align-flex {
    display: flex;
    align-items: center;
  }
  .govern-type {
    @{_deep} .ivu-form-item-content {
      float: right;
    }
  }
  .device-sign-property {
    max-height: 172px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-top: 20px;
    &:first-child {
      margin-top: 0;
    }
    li {
      margin-top: 5px;
    }
  }
  .flex-3 {
    width: 30%;
  }
  .flex-4 {
    width: 23%;
  }
  .radio-flex3 {
    width: calc(calc(100% - 210px) / 3);
  }
  .mb-15 {
    margin-bottom: 15px;
  }
  .wrap {
    flex-wrap: wrap;
  }

  .pl-9 {
    padding-left: 92px;
  }
  .pl-10 {
    padding-left: 120px;
  }
}
</style>
