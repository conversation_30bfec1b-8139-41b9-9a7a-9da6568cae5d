<template>
  <div class="vehicle-modal">
    <common-form
      :label-width="170"
      class="common-form"
      ref="commonForm"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="extractCar">
        <template>
          <FormItem label="每设备抽取图片" prop="captureNum">
            <InputNumber
              v-model="formData.captureNum"
              class="input-width"
              placeholder="请输入抽取设备图片"
              clearable
            ></InputNumber>
          </FormItem>
          <FormItem label="" :class="{ 'mt-minus-sm': formData.captureNum }" prop="isMissPic">
            <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
              <span>图片数量不足，则设备不合格</span>
            </Checkbox>
          </FormItem>
          <FormItem
            prop="deviceQueryForm.dayByCapture"
            label="图片抽取范围"
            :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          >
            <span class="base-text-color mr-xs">近</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.dayByCapture"
              :min="0"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color">天，</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.startByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点至</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.endByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点</span>
          </FormItem>
        </template>
      </div>
      <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
          </div>
          <div v-if="['1', '3'].includes(formData.detectMode) && !['VEHICLE_ONLINE_RATE'].includes(indexType)">
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="ml-sm mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <div class="capture-vehicle">
                <span class="base-text-color mr-sm">抓拍车辆不少于</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.countByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">张</span>
                <p class="color-failed">说明：系统只检测满足条件的设备。</p>
              </div>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>
    <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom" :label-width="170">
      <FormItem label="检测结构化属性" class="right-item mb-sm" prop="properties">
        <CheckboxGroup class="mb-sm" v-model="formData.properties">
          <Checkbox v-for="(item, index) in showVehicleProperties" :label="item.dataKey" :key="index">{{
            item.dataDes
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="更新车辆数据准确性状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      treeData: 'common/getInitialAreaList',
      ocrCheckModelList: 'algorithm/ivdg_image_ods_check_model',
      vehiclePropertyDictList: 'algorithm/getVehicleProperty',
    }),
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            quantityConfig: [],
            deviceDetection: {
              dataConfig: {
                key: 'now', //lately 最近
                value: 'month', // 30 10
                quantity: 0,
              },
            },
            visitTimeout: null,
            properties: [],
          };
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      algorithm: [], //算法
      formData: {
        // ocrAlgorithm: '1', //ocr算法
        algVendors: [], //选择厂商
        snap: null, //检测大图标注抓拍时间和地点
        ocrModel: '',
        // algorithm: '', //选择算法厂商
        timeFormat: '', //时延下拉框
        timeDelay: null, //图片上传时延
        // errorTime: '', //误差时间
        hourStart: null, //时间区间开始时间
        hourEnd: null, //时间区间结束时间
        // timeFrame:'', //时间范围
        imageNum: null, //车辆图片数量
        // captureDay: null, //抓拍天数
        captureNum: null, //抓拍数量
        captureDrop: null, //抓拍数量突降
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        orgCodeList: [],
        ruleList: [],
        properties: [],
        isUpdatePhyStatus: 0,
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
      },
      ruleCustom: {
        properties: {
          type: 'array',
          required: true,
          message: '请选择检测结构化属性',
          trigger: 'change',
        },
      },
      schemeList: [],
      regionalizationSelectVisible: false,
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      showVehicleProperties: [], // 用于显示的车辆属性
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
    };
  },
  async created() {
    if (!this.vehiclePropertyDictList?.length) {
      await this.getVehicleProperty();
    }
    this.getShowVehicleProperties();
  },
  methods: {
    ...mapActions({
      getVehicleProperty: 'algorithm/getVehicleProperty',
    }),
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },
    validateForm() {},
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    // 添加时间区间
    toAdd() {
      this.formData.dateRang.push({ hourStart: null, hourEnd: null });
    },
    // 删除时间区间
    toDel(index) {
      this.formData.dateRang.splice(index, 1);
    },
    handleChange(time, item) {
      item.planTime = time;
    },
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      return (await this.$refs['commonForm'].handleSubmit()) && this.$refs.formData.validate();
    },
    // 车辆数据完整率、准确率可配置检测车辆属性
    getShowVehicleProperties() {
      // (重点)车辆卡口设备过车数据准确率 不显示车辆型号
      if (['VEHICLE_INFO_PASS_IMPORTANT'].includes(this.indexType)) {
        this.showVehicleProperties = this.vehiclePropertyDictList.filter((item) => item.dataKey !== 'vehicleModel');
      } else {
        this.showVehicleProperties = this.vehiclePropertyDictList;
      }
      if (this.formModel !== 'edit') {
        this.formData.properties = this.showVehicleProperties.map((item) => item.dataKey);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.vehicle-modal {
  /deep/.select-width,
  .input-width {
    width: 380px;
  }
  .w240 {
    width: 240px;
  }
  .w292 {
    width: 292px;
  }
  .w390 {
    width: 390px;
  }
  .params-pre {
    display: inline-block;
    float: left;
    width: 40px;
    height: 34px;
    line-height: 34px;
    font-size: 16px;
    color: var(--color-primary);
    text-align: center;
    border: 1px solid #10457e;
    opacity: 1;
    border-radius: 4px;
    margin-right: 10px;
  }
  .label-color {
    color: #e44f22;
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .width-picker {
    width: 174px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .assessTime {
    /deep/.ivu-form-item-label {
      &::before {
        content: '*';
        display: inline-block;
        margin-right: 0.020833rem;
        line-height: 1;
        font-family: SimSun;
        font-size: 0.072917rem;
        color: #ed4014;
      }
    }
  }
  .inspection {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  .capture-vehicle {
    margin-left: 40px;
  }
}
.notCheck {
  color: #56789c;
}
</style>
