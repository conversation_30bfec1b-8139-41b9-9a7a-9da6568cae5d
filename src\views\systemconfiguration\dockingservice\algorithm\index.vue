<template>
  <div class="algorithm auto-fill">
    <div class="search-module">
      <ui-label label="算法厂商" class="inline">
        <Select class="width-md" v-model="searchData.algorithmVendorType" placeholder="请选择算法厂商">
          <Option v-for="(item, index) in algorithmList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
      <div class="inline ml-sm">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetSearch">重置</Button>
      </div>
      <Button
        class="fr ml-sm"
        type="primary"
        @click="add()"
        v-permission="{
          route: 'algorithm',
          permission: 'algorithmAdd',
        }"
      >
        <i class="icon-font icon-tianjia f-12 mr-sm"></i>
        <span class="inline vt-middle">新增算法服务</span>
      </Button>
    </div>
    <div class="table-box auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #createTime="{ row }">
          <span>{{ row.createTime || '--' }}</span>
        </template>
        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-bianji2"
            class="mr-md"
            content="编辑"
            @click.native="edit(row)"
            v-permission="{
              route: 'algorithm',
              permission: 'algorithmEdit',
            }"
          >
            ></ui-btn-tip
          >
          <ui-btn-tip
            icon="icon-shanchu3"
            content="删除"
            @click.native="del(row)"
            v-permission="{
              route: 'algorithm',
              permission: 'algorithmDel',
            }"
          >
            ></ui-btn-tip
          >
          <!-- <i
              title="查看"
              class="mr-sm"
              @click="operationTask('detail', row)"
              >查看任务
						</i>-->
          <!-- <i-switch
              :value="row.status !== 1 ? false : true"
              :before-change="() => handleBeforeChange(row)"
              size="small"
            >
						</i-switch>-->
        </template>
      </ui-table>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    ></ui-page>

    <add-edit
      ref="addPage"
      v-model="addEditVisible"
      :action="modalAction"
      :edit-data="modalData"
      :algorithm-list="algorithmList"
      :algorithm-type-list="algorithmTypeList"
      @addSuccess="addSuccess"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import algorithm from '@/config/api/algorithm';

export default {
  name: 'algorithm',
  data() {
    return {
      loading: false,
      addEditVisible: false,
      modalAction: {
        value: 'add',
        title: '新增算法',
      },
      modalData: {},
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '算法厂商', key: 'algorithmVendorTypeLabel' },
        { title: '算法类型', key: 'algorithmTypeLabel' },
        // { title: "创建人", key: "creator" },
        { title: '创建时间', slot: 'createTime' },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          fixed: 'right',
          width: 90,
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        algorithmVendorType: '',
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      selectOrgTree: {
        orgCode: '',
      },
    };
  },
  components: {
    addEdit: require('./add-edit.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
    }),
  },
  async created() {
    if (this.algorithmList.length == 0) await this.getAlldicData();
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async init() {
      try {
        this.loading = true;
        this.$http
          .post(algorithm.getAlgorithmList, this.searchData)
          .then((res) => {
            this.tableData = res.data.data.entities.map((item) => {
              item.algorithmVendorTypeLabel = this.getAlgorithmLabel(item.algorithmVendorType);
              item.algorithmTypeLabel = this.getAlgorithmTypeLabel(item.algorithmType);
              return item;
            });
            this.pageData.totalCount = res.data.data.total;
          })
          .finally(() => {
            this.loading = false;
          });
      } catch (error) {
        console.log(error);
      }
    },

    resetSearch() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        algorithmVendorType: '',
        params: { pageNumber: 1, pageSize: 20 },
        pageNum: 1,
        pageSize: 10,
      };
      this.init();
    },

    addSuccess() {
      this.init();
    },
    add() {
      this.modalAction = {
        value: 'add',
        title: '新增算法服务',
      };
      this.addEditVisible = true;
      this.modalData = {};
    },
    edit(row) {
      this.modalAction = {
        value: 'edit',
        title: '编辑算法服务',
      };
      this.modalData = row;
      this.addEditVisible = true;
    },
    // 删除弹窗内容
    del(row) {
      this.$UiConfirm({
        content: `您要删除${row.algorithmVendorTypeLabel} - ${row.algorithmTypeLabel}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$http.delete(algorithm.delAlgorithm + '/' + row.id).then((res) => {
            this.$Message.success(res.data.msg);
            this.init();
          });
        })
        .catch((res) => {
          console.log(res);
        });
    },

    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      // this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      // this.searchData.pageSize = val;
      this.init();
    },
    // 重置
    resetSearchData() {
      // this.copySearchDataMx(this.searchData);
      this.init();
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    // 选择组织机构名称
    selectTree({ orgCode }) {
      this.searchData.orgCode = orgCode;
      this.init();
    },
    // 开关测评
    handleBeforeChange({ contextId, status }) {
      return new Promise((resolve) => {
        if (status !== 3) {
          this.switch(contextId, status).then((res) => {
            if (res.status === 200) {
              this.$Message.success(res.data.data);
              resolve();
            }
          });
        }
      });
    },
    async switch(contextId, status) {
      try {
        return await this.$http.post(`${status === 1 ? algorithm.pause + contextId : algorithm.resume + contextId}`);
      } catch (err) {
        console.log(err);
      }
    },
    update() {
      this.init();
    },

    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },

    getAlgorithmTypeLabel(val) {
      var arr = this.algorithmTypeList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
      // var arr = val.split(",");
      // var str = "";
      // arr.forEach(item => {
      //   var obj = this.algorithmTypeList.find(ite => { return ite.value == item});
      //   str += obj.label + ","
      // })

      // return str.substring(0, str.length-1)
    },
  },
};
</script>

<style lang="less" scoped>
.algorithm {
  background: var(--bg-content);
  .search-module {
    padding: 20px;
  }
  .table-box {
    padding: 0 20px;
  }
}
.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }
  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}
</style>
