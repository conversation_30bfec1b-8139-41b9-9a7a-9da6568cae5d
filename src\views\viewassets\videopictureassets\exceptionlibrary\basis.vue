<template>
  <div class="basis auto-fill height-full">
    <div class="search-module">
      <div class="mb-lg">
        <ui-label class="inline" label="组织机构" :width="70">
          <api-organization-tree
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline ml-lg" label="关键词" :width="55">
          <Input
            v-model="searchData.keyWord"
            class="keyword-input"
            placeholder="请输入设备名称/设备编码/经纬度/安装地址"
          ></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="数据来源" :width="70">
          <!-- <data-source
            :source-id="searchData.sourceId"
            @selectChange="selectSource"
          ></data-source> -->
          <Select class="width-sm" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
            <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </div>
      </div>
      <div class="select-tabs">
        <ui-select-tabs :list="errorList" @selectInfo="selectInfo" ref="uiSelectTabs"></ui-select-tabs>
        <Button type="primary" class="inilne ml-sm" @click="exportModule">
          <i class="icon-font icon-daochu f-14"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
      </div>
    </div>

    <div class="statistic">
      <p class="statistic-title">
        {{ statistic.title }}
      </p>
      <p class="statistic-total">
        {{ statistic.totalNum | formatNum }}
      </p>
    </div>
    <div class="table-module auto-fill">
      <!-- 这里给每一列增加动态class是为了使deviceID相同的行保持一致颜色 -->
      <ui-table
        class="ui-table auto-fill"
        ref="tableRef"
        row-key="id"
        :table-columns="tableColumns"
        :table-data="tableData"
        :class="[isRepeatRecord ? '' : 'repeat-table']"
        :loading="loading"
        :stripe="isRepeatRecord ? false : true"
      >
        <template #selfCheckBox="{ row }" v-if="isRepeatRecord">
          <div :class="row.rowClass">
            <Checkbox
              v-model="row._repeatCheck"
              :disabled="row._disabled"
              @on-change="checkReapeatChange($event, row)"
            ></Checkbox>
          </div>
        </template>
        <template #Index="{ row, index }">
          <div :class="row.rowClass" class="t-center">
            <span v-if="handleIndex(row)">{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <template #address="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.address">
            {{ row.address }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <template #civilName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.civilName">
            {{ row.civilName }}
          </div>
        </template>
        <template #checkStatus="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="[
                row.checkStatus === '0' ? 'bg-b77a2a' : '',
                row.checkStatus === '1' ? 'bg-17a8a8' : '',
                row.checkStatus === '2' ? 'bg-success' : '',
                row.checkStatus === '3' ? 'bg-failed' : '',
                row.checkStatus === '4' ? 'bg-other' : '',
              ]"
            >
              {{ handleCheckStatus(row.checkStatus) }}
            </span>
          </div>
        </template>
        <template #azsj="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.azsj">
            {{ row.azsj }}
          </div>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            class="mr-md"
            icon="icon-bianji2"
            content="编辑"
            @click.native="deviceModalShow(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            icon="icon-shebeidangan"
            class="mr-md"
            content="设备档案"
            @click.native="deviceArchives(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            icon="icon-xinxitongbu"
            class="mr-md"
            content="同步"
            @click.native="synchronization(row)"
          ></ui-btn-tip>
          <!-- <ui-btn-tip
            icon="icon-zhongsheshizhong"
            class="mr-md"
            content="重设字幕"
            @click.native="setupZ(row)"
          ></ui-btn-tip> -->
          <operation-btn-more
            :btnList="btnList"
            :rowData="row"
            @handleOperations="handleOperations"
          ></operation-btn-more>
          <div :class="row.rowClass">
            <!-- <Button type="text" class="mr-md" @click="deviceModalShow(row)">编辑</Button>
            <Button type="text" class="mr-md" @click="deviceArchives(row)">设备档案</Button>
            <Button type="text" class="mr-md" @click="synchronization(row)">同步</Button> -->
            <!-- <Button type="text" class="mr-lg-s" @click="setupZ(row)"
              >重设字幕</Button
            >
            <Button type="text" class="mr-lg-s" @click="setupT(row)"
              >重设时钟</Button
            >
            <Button type="text" @click="viewRecord(row)">不合格原因</Button> -->
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <device-detail
      v-model="deviceDetailShow"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      @update="search"
    >
    </device-detail>
    <div class="repeat-wrapper" v-if="isRepeatRecord && !!this.checkRepeatGroup.length">
      <p class="mb-lg">
        是否确认这<span class="f-16 font-red"> {{ this.checkRepeatGroup.length }}条 </span>数据为主数据
      </p>
      <p class="mb-xs">1.确认后将屏蔽被排除的其他数据。</p>
      <p class="mb-lg">2.确认后主数据将转入待检测数据库进行待检测。</p>
      <div class="fr">
        <Button class="mr-xs plr-30" type="primary" @click="handleRepeatDeviceInfo">确 认</Button>
        <Button @click="checkRepeatGroup = []" class="plr-30">取 消</Button>
      </div>
    </div>
    <view-detection-field
      v-model="recordShow"
      :view-data="recordData"
      :need-option="true"
      @recordModalShow="deviceModalShow"
    ></view-detection-field>
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
          <Button
            type="primary"
            class="plr-30"
            @click="exportExcel(tagList, 'module')"
            :loading="exportModuleLoading"
            >{{ exportModuleLoading ? '下载中' : '模板导出' }}</Button
          >
        </Tooltip>
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading">{{
            exportDataLoading ? '下载中' : '数据导出'
          }}</Button>
        </Tooltip>
      </template>
    </customize-filter>
    <info-synchro-result ref="InfoSynchroResult"></info-synchro-result>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
import governancetheme from '@/config/api/governancetheme';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  props: {},
  data() {
    return {
      loading: false,
      exportDataLoading: false,
      exportModuleLoading: false,
      errorList: [],
      statistic: {
        title: '视图基础数据',
        totalNum: 0,
      },
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        orgCode: null,
        keyWord: '',
        sourceId: '',
        reasons: [],
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      initialTableColumns: [],
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          title: '',
          align: 'left',
          fixed: 'left',
          slot: 'selfCheckBox',
        }, // 重复记录自己的checkbox
        {
          width: 230,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          //slot: 'deviceName',
          tooltip: true,
        },
        {
          width: 230,
          title: '设备安装地址',
          key: 'address',
          align: 'left',
          //slot: 'address',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          //slot: 'orgName',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          //slot: 'civilName',
          tooltip: true,
        },
        {
          width: 100,
          title: '检测状态',
          align: 'left',
          slot: 'checkStatus',
        },
        {
          width: 300,
          title: '数据来源',
          key: 'sourceIdText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 200,
          title: '设备安装时间',
          key: 'azsj',
          align: 'left',
          //slot: 'azsj',
          tooltip: true,
        },
        {
          width: 150,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          //slot: 'ipAddr',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          //slot: 'macAddr',
          tooltip: true,
        },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'left',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      viewDeviceId: 0,
      deviceCode: '',
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      checkRepeatGroup: [], // 选中多个重复记录
      recordShow: false,
      recordData: {},
      deviceUnqualified: null,
      btnList: [
        { name: '重设字幕', funct: 'setupZ' },
        { name: '重设时钟', funct: 'setupT' },
        { name: '不合格原因', funct: 'viewRecord' },
      ],
      customFilter: false,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: '3.125rem',
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
    };
  },
  async created() {
    this.getPropertyList();
    this.initStatistic();
    this.initErrorList();
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.search();
    if (this.sourceList.length == 0) await this.getAlldicData(); // 数据来源
    this.initialTableColumns = this.$util.common.deepCopy(this.tableColumns);
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 数据来源
    }),
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    selectSource(val) {
      let sourceId = val ? val.sourceId : '';
      this.searchData.sourceIds = [sourceId];
    },
    selectInfo(infoList) {
      this.searchData.reasons = infoList.map((row) => {
        return row.name;
      });
      this.search();
    },
    async initStatistic() {
      try {
        let res = await this.$http.post(equipmentassets.queryAbnormalDeviceInfoCount, {
          orgCode: this.searchData.orgCode,
        });
        this.statistic.totalNum = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(governancetheme.queryDeviceErrorMessageList);
        this.errorList = res.data.data.map((row) => {
          return {
            name: row,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      this.loading = true;
      this.tableData = [];
      try {
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.queryAbnormalDevicePageList, this.searchData);
        let list = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
        // 此处给每一行增加class是为了使deviceID相同的行颜色一致
        this.tableData = this.handleCheckTable(
          list.map((row, index) => {
            if (index % 2 === 0) {
              this.$set(row, 'rowClass', 'even');
            } else {
              this.$set(row, 'rowClass', 'odd');
            }
            if (!!row.children && row.children.length !== 0) {
              row.children.forEach((rw) => {
                if (index % 2 === 0) {
                  this.$set(rw, 'rowClass', 'even');
                } else {
                  this.$set(rw, 'rowClass', 'odd');
                }
              });
            }
            return row;
          }),
        );

        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    clear() {
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.search);
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    handleCheckTable(list) {
      list.forEach((item) => {
        this.$set(item, '_disabled', false);
        this.$set(item, '_showChildren', false);
        let hasChecked = this.checkRepeatGroup.findIndex((checkItem) => {
          return checkItem.id === item.id;
        });
        hasChecked !== -1 ? this.$set(item, '_repeatCheck', true) : this.$set(item, '_repeatCheck', false);

        if (!!item.children && !!item.children.length) {
          this.handleCheckTable(item.children);
        }
      });
      return list;
    },
    async checkReapeatChange(isChecked, row) {
      try {
        // let bool = await this.checkPremission(row);
        // if(!bool){
        //   this.$set(row, '_repeatCheck', false);
        //   this.$Message.error('您没有处理该设备的权限');
        //   return false;
        // }
        let includeRowIndex = this.checkRepeatGroup.findIndex((item) => item.id === row.id);
        // 包含row并且没有选中，去掉
        if (includeRowIndex !== -1 && !row._repeatCheck) {
          this.checkRepeatGroup.splice(includeRowIndex, 1);
        }
        // 不包含row,但是选中，加上
        if (includeRowIndex === -1 && row._repeatCheck) {
          this.checkRepeatGroup.push(row);
        }
        // 相同deviceID只让选择一个作为主数据
        let ind = this.tableData.findIndex((rw) => rw.id === row.id);
        if (ind !== -1) {
          this.tableData[ind]._repeatCheck = isChecked;
          this.tableData[ind].children.forEach((item) => {
            item._disabled = isChecked;
          });
        } else {
          let i = this.tableData.findIndex((rw) => rw.deviceId === row.deviceId);
          this.tableData[i]._disabled = isChecked;
          this.tableData[i].children.forEach((item) => {
            if (item.id === row.id) {
              item._repeatCheck = isChecked;
            } else {
              item._disabled = isChecked;
            }
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 检测该deviceID对应的所有数据是否有权限处理
    async checkPremission(row) {
      try {
        let { data } = await this.$http.get(equipmentassets.ifHasPermisssionHandleRepeatDeviceInfo, {
          params: {
            deviceId: row.deviceId,
          },
        });
        return data.data;
      } catch (err) {
        console.log(err);
      }
    },
    handleIndex(row) {
      let flag = this.tableData.some((item) => {
        return row.id === item.id;
      });
      return flag;
    },
    handleCheckStatus(row) {
      const flag = {
        0: '待检测',
        1: '待去重',
        2: '合格',
        3: '不合格',
        4: '无效',
      };
      return flag[row];
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    async handleRepeatDeviceInfo() {
      try {
        const ids = this.checkRepeatGroup.map((item) => {
          return {
            deviceId: item.deviceId,
            id: item.id,
          };
        });
        let res = await this.$http.post(equipmentassets.handleRepeatDeviceInfo, ids);
        this.$Message.success(res.data.msg);
        this.checkRepeatGroup = [];
        this.search();
        this.initStatistic();
      } catch (err) {
        console.log(err);
      }
    },
    viewRecord(row) {
      this.recordShow = true;
      this.recordData = row;
    },
    deviceModalShow(row, unqualified) {
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    synchronization(row) {
      this.$refs.InfoSynchroResult.init(row);
    },
    setupZ(row) {
      if (row.manufacturer == '' || row.manufacturer == null) {
        this.$Message.error('该设备未关联厂商');
        return;
      }
      this.$UiConfirm({
        content: '您是否要重设字幕?',
        title: '提示',
      })
        .then(() => {
          let num_str = parseInt(row.manufacturer);
          this.$http
            .get(equipmentassets.resetOsd, {
              params: { device_id: row.deviceId, changshang: num_str },
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$Message.success(res.data.data);
              }
            });
        })
        .catch((res) => {
          console.log(res);
        });
    },
    setupT(row) {
      this.$UiConfirm({
        content: '您是否要重设时钟?',
        title: '提示',
      })
        .then(() => {
          this.$http
            .get(equipmentassets.resetOsdTime, {
              params: { device_id: row.deviceId },
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$Message.success(res.data.data);
              }
            });
        })
        .catch((res) => {
          console.log(res);
        });
    },
    handleOperations(obj) {
      console.log(obj);
      this[obj.methods](obj.data);
    },
    exportModule() {
      this.customFilter = true;
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(equipmentassets.exportDevice, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.defaultCheckedList = [
          'deviceId',
          'id',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    isRepeatRecord(val) {
      if (val) {
        this.tableColumns.splice(0, 2);
        this.tableColumns.unshift({
          width: 80,
          align: 'left',
          fixed: 'left',
          slot: 'selfCheckBox',
        });
      } else {
        this.tableColumns = this.$util.common.deepCopy(this.initialTableColumns);
        this.tableColumns.splice(2, 1);
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
    }),
    isRepeatRecord() {
      return false;
    },
  },
  components: {
    // DataSource: require('@/api-components/data-source.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    OperationBtnMore: require('@/components/operation-btn-more.vue').default,
    // ChooseDevice: require('@/components/choose-device.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    InfoSynchroResult: require('../../datagovernance/onlinegovernance/information/info-synchro-result.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 20px 20px 10px 20px;
  .keyword-input {
    width: 300px;
  }
  .select-tabs {
    display: flex;
    align-items: center;
    @{_deep} .tabs {
      flex: 1;
    }
  }
}
.statistic {
  float: right;
  padding-right: 20px;
  padding-top: 20px;
  position: absolute;
  top: 0;
  right: 0;
  .statistic-title {
    color: #fff;
  }
  .statistic-total {
    color: var(--color-bluish-green-text);
    font-size: 20px;
    margin-bottom: 15px;
  }
  .statistic-today {
    color: #f18a37;
    font-size: 20px;
  }
}
.table-module {
  clear: both;
  .ui-table {
    padding: 0 20px;
    @{_deep}.ivu-table-column-center {
      > .ivu-table-cell {
        > span {
          margin: 0 auto;
        }
      }
    }
  }
  @{_deep}.ivu-table-cell-tree {
    background-color: transparent;
    display: flex;
    color: var(--color-primary);
    border: none;
    height: 20px;
    width: 20px;
    margin-left: -10px;
    i {
      font-size: 20px;
      font-weight: 900;
    }
  }
  @{_deep}.ivu-table-cell {
    display: flex;
  }
  .repeat-table {
    // 以下css是为了table表格实现相同deviceID颜色保持一致
    @{_deep} .ivu-table .even {
      // background-color: #062042 !important;
      padding: 0 10px;
      // height: 48px;
      display: flex;
      align-items: center;
    }
    @{_deep} .ivu-table .odd {
      // background-color: #041939 !important;
      padding: 0 10px;
      // height: 48px;
      display: flex;
      align-items: center;
    }
    @{_deep}.ivu-table-tbody {
      .ivu-table-cell-tree {
        align-items: center;
        justify-content: center;
        position: absolute;
      }
      .ivu-table-column-left {
        .ivu-table-cell {
          align-items: center;
          padding: 0;
          .ivu-table-cell-slot {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
