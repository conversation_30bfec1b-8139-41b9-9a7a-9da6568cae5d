const defaultGraphStyle = {
  color: "#2C86F8", //颜色
  fillColor: "#2C86F8", //填充颜色
  weight: 2, //宽度，以像素为单位
  opacity: 1, //透明度，取值范围0 - 1
  fillOpacity: 0.3, //填充的透明度，取值范围0 - 1
};

class CreatGeometryLayer {
  static instance;
  constructor(
    map, // 地图实例对象
    layerName = `graphLayer-${Math.random().toString().substr(2)}`, // 图层名称
    mode = "Sing" // 是否开启单例模式
  ) {
    if (CreatGeometryLayer.graphLayer && mode == "Sing") {
      return CreatGeometryLayer.instance;
    }
    this.graphLayer = new NPMapLib.Layers.OverlayLayer(layerName);
    this.map = map;
    this.mode = mode;
    this.map.addLayer(this.graphLayer);
    mode == "Sing" && (CreatGeometryLayer.instance = this);
  }
  CreatGeometry(data, graphType = "Polygon", style) {
    return this[`Creat${graphType}Geometry`](data, style);
  }
  CreatGeometryGroup(geometryGroup, ...props) {
    //  props =  graphType,style
    return geometryGroup.map((element) => {
      this.CreatGeometry(element, ...props);
    });
  }
  //矩形
  CreatPolygonGeometry(points, style = {}) {
    const geometryPoints = points.map((point) => this.getGeoPoints(point));
    const polygonGeometry = new NPMapLib.Geometry.Polygon(geometryPoints, {
      ...defaultGraphStyle,
      style,
    });
    this.graphLayer.addOverlay(polygonGeometry);
    return polygonGeometry;
  }
  // 圆形
  CreatCircleGeometry({ center, radius = 100000 }, style = {}) {
    const circleGeometry = new NPMapLib.Geometry.Circle(
      this.getGeoPoints(center),
      radius,
      {
        ...defaultGraphStyle,
        style,
      }
    );
    this.graphLayer.addOverlay(circleGeometry);
    return circleGeometry;
  }
  // 椭圆
  CreatEllipseGeometry({ firstPoint, secondPoint, distance }, style = {}) {
    const ellipseGeometry = new NPMapLib.Geometry.Ellipse(
      this.getGeoPoints(firstPoint),
      this.getGeoPoints(secondPoint),
      distance,
      {
        ...defaultGraphStyle,
        style,
      }
    );
    this.graphLayer.addOverlay(ellipseGeometry);
    return ellipseGeometry;
  }
  show() {
    this.graphLayer.show();
  }
  hide() {
    this.graphLayer.hide();
  }
  getGeoPoints(point) {
    return new NPMapLib.Geometry.Point(...point);
  }
  // 清除整个覆盖物图层
  removeOverlay(geometry) {
    if (geometry) {
      // 清楚单个图形
      this.graphLayer.removeOverlay(geometry);
    } else {
    }
  }
  removeAllOverlays() {
    // 单一图层模式下清除图层上所有覆盖物，保留图层本身
    if (this.mode === "Sing") this.graphLayer.removeAllOverlays();
    this.map.removeOverlay(this.graphLayer);
  }
}

export default CreatGeometryLayer;
