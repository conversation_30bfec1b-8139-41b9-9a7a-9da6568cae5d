<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics></index-statistics>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem" :lengName="lengName">
          <template #rank-title>按建档率排名</template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart-multiple class="line-chart"></line-chart-multiple>
    </div>
  </div>
</template>

<script>
export default {
  name: 'echarts',
  components: {
    IndexStatistics: require('./index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    SubordinateChart: require('./subordinate-chart.vue').default,
    LineChartMultiple: require('./line-chart-multiple').default,
  },
  data() {
    return {
      lengName: [
        {
          name: '视频监控未建档数',
          key: 'videoUnDocCount',
          stack: 'video',
        },
        {
          name: '视频监控已建档数',
          key: 'biVideoCount',
          stack: 'video',
        },
        {
          name: '人卡未建档数',
          key: 'faceUnDocCount',
          stack: 'face',
        },
        {
          name: '人卡已建档数',
          key: 'biFaceCount',
          stack: 'face',
        },
        {
          name: '车卡未建档数',
          key: 'vehicleUnDocCount',
          stack: 'vehicle',
        },
        {
          name: '车卡已建档数',
          key: 'biVehicleCount',
          stack: 'vehicle',
        },
      ],
    };
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: 100%;
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
