<!--
 * @Date: 2025-02-27 15:37:26
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-12 10:47:12
 * @FilePath: \icbd-view\src\components\ui-audio\index.vue
-->
<template>
  <Modal
    v-model="visible"
    class-name="vertical-center-modal"
    footer-hide
    :mask-closable="false"
    :width="600"
    :styles="{ bottom: '40px' }"
    @on-cancel="close"
  >
    <div class="audio-content">
      <div class="animation-box">
        <AudioAnimation
          v-show="!loading"
          ref="audioAnimation"
          :sWidth="400"
          :spectrumHeightMin="10"
          :spectrumHeightMax="58"
        ></AudioAnimation>
      </div>
      <div class="microphone-box">
        <div
          class="microphone-image-box"
          @click="changeAudio"
          v-if="!loading"
        ></div>
        <div class="loading" v-else><AudioLoading></AudioLoading></div>
        <div class="message-span">
          <span>{{
            loading ? "语音正在输入中，点击按钮结束" : "识别完成"
          }}</span>
        </div>
        <!-- <Button type="primary" class="search-btn" @click="startAudio">
          音波开始
        </Button>
        <Button type="primary" class="search-btn" @click="endAudio">
          音波结束
        </Button> -->
      </div>
    </div>
  </Modal>
</template>

<script>
import AudioAnimation from "./audio-animation.vue";
import AudioLoading from "./audio-loading.vue";
// import { startAudio, endAudio } from "@/util/modules/audio.js";
import AudioHandler from "@/util/modules/audio.js";
export default {
  name: "ui-audio",
  components: {
    AudioAnimation,
    AudioLoading,
  },
  data() {
    return {
      audioHandler: new AudioHandler(),
      visible: false,
      loading: false,
    };
  },
  methods: {
    // 结束语音动画
    changeAudio() {
      this.$refs.audioAnimation.stopJumping();
      this.endVideo();
    },
    close() {
      this.$refs.audioAnimation.stopJumping();
      this.visible = false;
    },
    // 初始化展示
    async show() {
      try {
        await this.audioHandler.startAudio();
        this.visible = true;
        this.$nextTick(() => {
          this.$refs.audioAnimation.startJumping();
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 开启语音识别
    async startVideo() {
      await this.audioHandler.startAudio();
    },
    // 结束语音识别
    async endVideo() {
      this.loading = true;
      try {
        let { data } = await this.audioHandler.endAudio();
        this.$emit("confirm", data);
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.vertical-center-modal {
  // display: flex;
  // align-items: center;
  // justify-content: center;

  /deep/.ivu-modal {
    top: 200px;
  }
}
.audio-content {
  display: flex;
  align-items: center;
  flex-direction: column;
  .microphone-box {
    display: flex;
    align-items: center;
    flex-direction: column;
    .message-span {
      margin-bottom: 40px;
      span {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        line-height: 18px;
        text-align: center;
      }
    }
  }
}
.microphone-image-box {
  width: 120px;
  height: 120px;
  background-image: url("~@/assets/img/audio_normal.png");
  background-repeat: no-repeat;
  background-size: cover;
  &:hover {
    background-image: url("~@/assets/img/audio_hover.png");
  }
  margin-bottom: 20px;
}
.loading {
  width: 120px;
  height: 120px;
  margin-bottom: 20px;
}
.animation-box {
  width: 400px;
  height: 58px;
  margin-top: 57px;
  margin-bottom: 20px;
}
</style>
