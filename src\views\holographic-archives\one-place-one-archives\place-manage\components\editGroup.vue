<!--
 * @Date: 2025-01-07 17:31:34
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-04 16:35:26
 * @FilePath: \icbd-view\src\views\holographic-archives\one-place-one-archives\place-manage\components\editGroup.vue
-->
<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :r-width="700"
    @onOk="comfirmHandle"
    @onCancel="comfirmCancle"
  >
    <div class="formBox">
      <Form ref="form" :model="formData" :label-width="120">
        <FormItem
          prop="firstLevel"
          label="一级分类名称"
          :rules="[
            { required: true, message: '输入一级分类名称', trigger: 'change' },
          ]"
        >
          <Input
            v-model="formData.firstLevel"
            :placeholder="'请输入'"
            :disabled="isSecond"
          />
        </FormItem>
        <FormItem
          v-if="isSecond"
          prop="secondLevel"
          label="二级分类名称"
          :rules="[
            { required: true, message: '输入二级分类名称', trigger: 'change' },
          ]"
        >
          <Input v-model="formData.secondLevel" :placeholder="'请输入'" />
        </FormItem>
        <FormItem
          prop="typeCode"
          label="类型编码"
          :rules="[
            { required: true, message: '输入类型编码', trigger: 'change' },
          ]"
        >
          <Input
            v-model="formData.typeCode"
            :placeholder="'请输入'"
            :disabled="isEdit"
          />
        </FormItem>
        <!-- 场所部分改动 暂时不生效 -->
        <div>
          <FormItem class="form-item-icon" label="图标" v-if="isSecond">
            <div class="icon-img">
              <ui-icon
                v-if="selectIconInfo.font_class"
                :type="selectIconInfo.font_class"
                :color="selectIconInfo.color"
              ></ui-icon>
              <Icons v-else type="icon-picture" :size="21" color="#037CBE" />
            </div>
            <div class="errorMessage" v-if="!selectIconInfo.font_class">
              请选择图标
            </div>
            <div
              class="title"
              @click="switchShowIconList = !switchShowIconList"
            >
              <span>图标库</span>
              <Icon
                :type="`ios-arrow-dropdown`"
                :class="[switchShowIconList ? ' arrowrun' : ' arrow']"
                :size="18"
                color="#037CBE"
              />
            </div>
          </FormItem>
        </div>
      </Form>
      <modular-icon
        ref="modularIcon"
        v-show="switchShowIconList"
        @on-select-icon="selectIcon"
      ></modular-icon>
    </div>
  </ui-modal>
</template>
<script>
import { placeTypeAdd, updatePlaceType } from "@/api/placeArchive.js";
import { dictDataBatchAdd, dictDataUpdate } from "@/api/config";
import { mapActions } from "vuex";
import Icons from "@/components/icons/icons";
import ModularIcon from "@/components/ui-modular-icon.vue";
export default {
  name: "EditGroup",
  components: { Icons, ModularIcon },
  props: {
    title: {
      type: String,
      default: "新增一级分类",
    },
    otherParam: {
      type: Object,
      default: () => {},
    },
    isSecond: {
      type: Boolean,
      default: false,
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      formData: { firstLevel: "", secondLevel: "", typeCode: "", icon: "" },
      selectIconInfo: { font_class: "", id: "", color: "" },
      switchShowIconList: false,
    };
  },
  computed: {},
  methods: {
    ...mapActions({
      updateDictData: "dictionary/updateDictData",
    }),
    show({ firstLevel, secondLevel, typeCode, icon }) {
      this.selectIconInfo = {};
      this.switchShowIconList = false;
      this.visible = true;
      this.$refs.form.resetFields();
      this.$nextTick(() => {
        if (typeCode) {
          this.$set(this.formData, "typeCode", typeCode);
        }
        if (icon) {
          this.selectIconInfo = JSON.parse(icon);
        }
        if (firstLevel) {
          this.$set(this.formData, "firstLevel", firstLevel);
        }
        if (secondLevel) {
          this.$set(this.formData, "secondLevel", secondLevel);
        }
      });
    },
    // 确认提交
    async comfirmHandle() {
      if (this.isSecond && !this.selectIconInfo.font_class) {
        this.$Message.error("请选择图标");
        return;
      }
      let param = {
        typeName: this.isSecond
          ? this.formData.secondLevel
          : this.formData.firstLevel,
        typeCode: this.formData.typeCode,
        icon: this.isSecond ? JSON.stringify(this.selectIconInfo) : "",
        ...this.otherParam,
      };
      if (!this.isEdit) {
        placeTypeAdd(param).then((res) => {
          this.$Message.success("添加成功");
          this.comfirmCancle();
          this.$emit("comfirm");
        });
      } else {
        updatePlaceType(param).then((res) => {
          this.$Message.success("修改成功");
          this.comfirmCancle();
          this.$emit("comfirm");
        });
      }
    },
    comfirmCancle() {
      this.visible = false;
    },
    selectIcon(e) {
      this.selectIconInfo = {
        font_class: e.font_class,
        color: e.color,
      };
    },
  },
};
</script>
<style lang="less" scoped>
.list {
  height: 300px;
  overflow-y: auto;
}

.formBox {
  /deep/.form-item-icon {
    .ivu-form-item-label:before {
      content: "*";
      display: inline-block;
      margin-right: 5px;
      line-height: 1;
      font-family: SimSun, sans-serif;
      font-size: 12px;
      color: #f8775c;
    }
  }
}

.icon-img {
  height: 60px;
  width: 60px;
  background-color: var(--darkFill);
  border: 1px dashed var(--inputBorder);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .iconfontconfigure {
    font-size: 20px;
  }
}

.errorMessage {
  color: #f8775c;
}

.title {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 90px;
  font-size: 14px;
  font-weight: bold;
  color: var(--standardColor);
  height: 30px;
  line-height: 30px;
  cursor: pointer;

  > span {
    margin-right: 11px;
  }

  .arrowrun {
    transition: 0.5s;
    transform-origin: center;
    transform: rotateZ(180deg);
  }

  .arrow {
    transition: 0.5s;
    transform-origin: center;
    transform: rotateZ(0deg);
  }
}
</style>
