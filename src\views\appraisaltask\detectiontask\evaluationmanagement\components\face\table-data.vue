<template>
  <div class="table-data" v-scroll="500">
    <div class="fail-picture">
      <div class="fail-picture-container" v-for="(item, index) in tableData" :key="index">
        <div class="fail-picture-item" @click="lookScence(index)">
          <ui-image :src="item.facePath" />
        </div>
        <!-- 不合格 -->
        <div class="fail-picture-date ellipsis" v-if="tableShow === 2">
          <span class="icon-font icon-shijian f-12 mr-xs vt-middle"></span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.shotTime || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.shotTime || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 2">
          <span class="icon-font icon-dizhi f-12 mr-xs vt-middle"></span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.address || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.address || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 2 && resultText === 3">
          <span class="icon-font icon-jieguo f-12 mr-xs vt-middle"></span>
          <Tooltip
            class="text vt-middle base-text-color f-12 ellipsis color-failed"
            :content="item.unableDetectTip || '未知'"
          >
            <span class="text vt-middle base-text-color f-12 ellipsis inline color-failed">{{
              item.unableDetectTip || '未知'
            }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 2 && resultText != 3 && titleText === 9">
          <span class="icon-font icon-jieguo f-12 mr-xs vt-middle"></span>
          <Tooltip
            placement="right"
            class="text vt-middle base-text-color f-12 ellipsis color-failed"
            :content="item.resultTip || '未知'"
          >
            <span class="text vt-middle base-text-color f-12 ellipsis inline color-failed">{{
              item.resultTip || '未知'
            }}</span>
          </Tooltip>
        </div>
        <!-- 合格 -->
        <div class="fail-picture-address ellipsis" v-if="tableShow === 1">
          <span class="f-12 vt-middle">抓拍：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.shotTime || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.shotTime || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 1">
          <span class="f-12 vt-middle">接收：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.receiveTime || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.receiveTime || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 1 && resultText != 3">
          <span class="f-12 vt-middle">结果：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.delay || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.delay || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 1 && resultText === 3">
          <span class="f-12 vt-middle">结果：</span>
          <Tooltip
            class="text vt-middle base-text-color f-12 ellipsis color-failed"
            :content="item.unableDetectTip || '未知'"
          >
            <span class="text vt-middle base-text-color f-12 ellipsis inline color-failed">{{
              item.unableDetectTip || '未知'
            }}</span>
          </Tooltip>
        </div>

        <!-- 无法检测 -->
        <div class="fail-picture-address ellipsis" v-if="tableShow === 3">
          <span class="f-12 vt-middle">抓拍：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.shotTime || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.shotTime || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 3">
          <span class="f-12 vt-middle">接收：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.firstIntoViewTime || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{
              item.firstIntoViewTime || '未知'
            }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 3 && resultText != 3">
          <span class="f-12 vt-middle">结果：</span>
          <Tooltip class="text vt-middle base-text-color f-12 ellipsis" :content="item.resultTip || '未知'">
            <span class="text vt-middle base-text-color f-12 ellipsis inline">{{ item.resultTip || '未知' }}</span>
          </Tooltip>
        </div>
        <div class="fail-picture-address ellipsis" v-if="tableShow === 3 && resultText === 3">
          <span class="f-12 vt-middle">结果：</span>

          <Tooltip
            class="text vt-middle base-text-color f-12 ellipsis color-failed"
            :content="item.unableDetectTip || '未知'"
          >
            <span class="text vt-middle base-text-color f-12 ellipsis inline color-failed">{{
              item.unableDetectTip || '未知'
            }}</span>
          </Tooltip>
        </div>
      </div>
    </div>
    <div class="t-center" v-if="tableData.length === 0">
      <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt="" class="no-data" />
      <img v-else src="@/assets/img/common/nodata-light.png" alt="" class="no-data" />
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
  </div>
</template>
<style lang="less" scoped>
.table-data {
  .fail-picture {
    display: flex;
    flex-wrap: wrap;
    .fail-picture-container {
      height: 254px;
      width: 191px;
      background: #0f2f59;
      color: #8797ac;
      padding: 15px;
      margin-right: 13px;
      margin-bottom: 15px;
      &:nth-child(7n) {
        margin-right: 0px;
      }
      .fail-picture-item {
        height: 162px;
        width: 162px;
        margin-bottom: 10px;
        cursor: pointer;
      }
      .fail-picture-date {
        line-height: 18px;
        font-size: 12px;
      }
      .fail-picture-address {
        line-height: 18px;
        font-size: 12px;
        .result-icon {
          font-size: 20px;
        }
        .text {
          // display: inline-block;
          // overflow: hidden;
          // text-overflow: ellipsis;
          // white-space: nowrap;
          cursor: pointer;
          width: 124px;
        }
      }
    }
  }
  .no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
    };
  },
  created() {},
  mounted() {},
  methods: {
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {
    tableData: {
      handler() {
        this.imgList = this.tableData.map((row) => row.scenePath);
      },
      // immediate: true,
      // deep: true,
    },
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  props: {
    tableData: {
      default: () => {
        return [];
      },
      type: Array,
    },
    tableShow: {
      type: Number,
      required: true,
    },
    titleText: {
      type: Number,
      required: true,
    },
    resultText: {
      type: Number,
      required: true,
    },
  },
  components: {
    uiImage: require('@/components//ui-image').default,
    LookScene: require('@/components/look-scene.vue').default,
  },
};
</script>
