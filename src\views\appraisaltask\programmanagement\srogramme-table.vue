<template>
  <div class="page-srogramme auto-fill">
    <div class="search-wrapper">
      <ui-label class="inline mr-md" label="指标名称" :width="66">
        <Input class="input-width" v-model="searchData.indexName" clearable placeholder="请输入指标名称"> </Input>
      </ui-label>
      <ui-label class="inline" label="指标类型" :width="66">
        <Select class="input-width" v-model="searchData.indexModule" placeholder="请选择指标类型" clearable>
          <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id">{{ item.title }} </Option>
        </Select>
      </ui-label>
      <ui-label :width="30" class="inline ml-lg" label="">
        <Button type="primary" class="" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetSearchDataMx(searchData, () => search(searchData))">重置</Button>
      </ui-label>
      <div class="fr">
        <Button class="ml-sm" type="primary" @click="newadded('add')">
          <i class="icon-font icon-tianjia f-12 mr-sm vt-middle"></i>
          <span>关联指标</span>
        </Button>
        <Button class="ml-sm" type="primary" @click="clickDelete">
          <i class="icon-font icon-shanchu mr-sm"></i>
          <span>批量删除</span>
        </Button>
      </div>
    </div>
    <div class="table-box auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="onSelectionChange"
      >
        <template #indexModule="{ row }">
          {{ getIndexModuleName(row) }}
        </template>
        <template #option="{ row, index }">
          <ui-btn-tip
            icon="icon-chakanxiangqing"
            content="查看"
            @handleClick="editModuleIndex('view', row, index)"
          ></ui-btn-tip>
          <ui-btn-tip icon="icon-shanchu3 ml-md" content="删除" @handleClick="deleteModuleIndex(row)"></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="ui-page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
    <!-- 关联指标 -->
    <add-indicators
      ref="child"
      v-model="indexShow"
      :modal-action="indexAction"
      :activeModuleMessageId="activeModuleMessageId"
      :scheme-value="schemeValue"
      @init="init"
    ></add-indicators>
    <!--    查看指标    -->
    <view-index v-model="viewIndex" :indexDetail="fromData" :moduleAction="moduleAction"></view-index>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    activeModuleMessageId: {
      required: true,
      type: Number,
    },
    schemeValue: {
      required: true,
      type: Number,
    },
  },
  data() {
    return {
      dataObjecTitle: '指标评测配置',
      dataObjectShow: false,
      dataObjectData: {},
      activeDataObjectIndex: null,
      moduleAction: {},
      index_id: {
        targetId: '',
        version: '',
      },
      viewIndex: false,
      modalAction: {},
      ivewModal: {},
      loading: false,
      searchData: {
        schemeId: '',
        indexName: '',
        indexModule: '',
        indexModuleId: 1,
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      checkedData: [],
      minusTable: 250,
      tableColumns: [
        { type: 'selection', width: 50, align: 'center' },
        { type: 'index', width: 70, title: '序号' },
        {
          title: '指标名称',
          key: 'indexName',
          width: 300,
          ellipsis: true,
          tooltip: true,
        },
        { title: '达标值', key: 'dataTargetValue', width: 100 },
        {
          title: '指标类型',
          slot: 'indexModule',
          width: 150,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '评价标准',
          key: 'evaluationCriterion',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 75,
          className: 'table-action-padding',
        },
      ],
      tableData: [],
      indexShow: false,
      indexAction: {},
      fromData: {},

      formData: {
        extractDataCount: '',
        extractDeviceType: null,
        extractDeviceCount: '',
      },
      contentStyle: {
        height: '3.125rem',
      },
      multipleSelection: [],
      //方案配置统一在util中配置
      row: {}, //列表行数据
      visibleFocusTrack: false,
      modalData: {},
      capture: {
        tooLessByDays: 30,
        tooLessByCount: 200,
        swoopRatio: 50,
      },
      placeDataObject: {
        deviceCondition: {
          objectType: '1',
          filter: {
            checkDeviceFlag: '0',
            placeName: '',
            placeAlias: '',
            placeIds: [],
            sbcjqyList: [],
          },
        },
      },
      placeVisible: false, //空间
      currentItem: null, //当前列表对象
    };
  },

  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  inject: ['programThis'],
  methods: {
    getIndexModuleName(row) {
      return this.global.indexTypeList.find((item) => item.id == row.indexModule).title || row.indexModule;
    },
    queryPlace(val) {
      let extensionData = JSON.stringify({
        ...val,
      });
      let list = this.tableData[this.activeDataObjectIndex];
      let data = {
        id: list.id,
        indexId: list.indexId,
        schemeId: list.schemeId,
        version: list.version,
        extensionData: extensionData,
        creator: list.creator,
      };
      this.$http.put(governanceevaluation.getConfigIndex, data).then((res) => {
        this.placeVisible = false;
        this.$Message.success(res.data.msg);
        this.init();
      });
    },
    async saveFocusTrack(val) {
      let { algVendors, ...extensionData } = val;
      let data = {
        id: this.modalData.id,
        indexId: this.modalData.indexId,
        schemeId: this.modalData.schemeId,
        version: this.modalData.version,
        extensionData: JSON.stringify(extensionData),
        creator: this.modalData.creator,
        algVendors: algVendors,
      };
      let res = await this.$http.put(governanceevaluation.getConfigIndex, data);
      this.$Message.success(res.data.msg);
      this.init();
      this.dataObjectShow = false;
    },
    saveObjectData(val) {
      this.tableData[this.activeDataObjectIndex].extensionData = JSON.stringify({
        ...this.capture,
        ...val,
      });
      let list = this.tableData[this.activeDataObjectIndex];
      let data = {
        id: list.id,
        indexId: list.indexId,
        schemeId: list.schemeId,
        version: list.version,
        extensionData: list.extensionData,
        creator: list.creator,
      };
      this.$http.put(governanceevaluation.getConfigIndex, data).then((res) => {
        this.$Message.success(res.data.msg);
        this.init();
        this.dataObjectShow = false;
      });
    },
    clickDelete() {
      if (!this.multipleSelection.length) {
        return this.$Message.error('请选择指标');
      }
      let ids = this.multipleSelection.map((item) => item.id);
      this.$UiConfirm({
        content: `您将要删除${ids.length}条指标，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.batchDelete(ids);
        })
        .catch(() => {});
    },
    onSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchDelete(ids) {
      this.multipleSelection = [];
      this.$http.delete(`${governanceevaluation.batchRemoveSchemeIndex}/${ids.join(',')}`).then((res) => {
        this.$Message.success(res.data.msg);
        this.init();
        this.programThis.refreshSchemeCount();
      });
    },
    async init() {
      try {
        this.multipleSelection = [];
        this.loading = true;
        let params = {
          schemeId: this.schemeValue,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          indexName: this.searchData.indexName,
          indexModule: this.searchData.indexModule,
        };
        let res = await this.$http.post(governanceevaluation.getEvaluatingProgramIndexByPage, params);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.index_id.targetId = this.searchData.schemeId;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    deleteModuleIndex(row) {
      this.$UiConfirm({
        content: `您要删除指标${row.indexName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.batchDelete([row.id]);
        })
        .catch(() => {});
    },
    // 新增指标
    newadded(action) {
      switch (action) {
        case 'add':
          this.indexAction = {
            title: '关联指标',
            action: 'add',
          };
          break;
      }
      this.indexShow = true;
    },
    // 编辑指标
    async editModuleIndex(action, row) {
      this.viewIndex = true;
      this.moduleAction = { title: `查看${row.indexName}` };
      this.fromData = row;
    },
    update() {
      this.search();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    clear() {
      this.searchData.indexName = '';
      this.search();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
  },
  watch: {
    activeModuleMessageId(val) {
      this.searchData.schemeId = val;
      this.search();
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddIndicators: require('./add-indicators.vue').default,
    viewIndex: require('./view-index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.ml-30 {
  margin-left: 30px;
}
.page-srogramme {
  position: relative;
  height: 100%;
  background: var(--bg-content);

  .search-wrapper {
    overflow: hidden;

    .input-width {
      width: 230px;
    }
  }

  .table-box {
    position: relative;
    height: 100%;
    overflow: hidden;
    margin-top: 20px;

    @{_deep}.standard {
      padding: 10px 0;
    }

    .ui-table {
      @{_deep} .ivu-table-body {
        td {
          padding: 10px 0 10px 0;
        }
      }
      @{_deep} .ivu-table-fixed-right .ivu-table-fixed-body {
        td {
          padding: 10px 0 10px 0;
        }
      }

      @{_deep} .ivu-table-cell {
        .ivu-tooltip-rel {
          .text_hid();
        }
      }
    }

    .evaluation-criterion {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2; /*超出3行部分显示省略号，去掉该属性 显示全部*/
      -webkit-box-orient: vertical;
    }

    .ui-page {
      position: relative;
      bottom: 0;
    }
  }
}
.btn-text-default {
  cursor: pointer;
  font-size: 14px;
  color: var(--color-primary);
}
</style>
<style lang="less">
// 弹窗列表样式
.diffent-tab {
  .ivu-table-wrapper {
    height: 400px !important;
  }
  .ivu-table-body {
    height: 400px !important;
  }
}
.ml-65 {
  margin-left: 65px;
}
</style>
