<template>
  <div class="map-box" ref="mapBox">
    <div :id="mapId" class="map"></div>
    <!-- 场所范围 -->
    <aoiDetail
      ref="aoiDetail"
      :placeInfo="placeInfo"
      :isShowArchive="isShowArchive"
      :goArchiveInfo="goArchiveInfo"
      @closeplaceInfoWindow="closeplaceInfoWindow"
    />
    <!-- @openFrameModal="openFrameModal" -->
    <!-- 报警详情 -->
    <MapAlarm
      class="map-alarm"
      ref="mapAlarm"
      :data="alarmData"
      @closeplaceInfoWindow="closeAlarmInfoWindow"
    >
    </MapAlarm>
    <slot></slot>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";
import aoiDetail from "./aoiDetail.vue";
import MapAlarm from "./map-alarm.vue";
import { placeType } from "@/map/core/enum/LayerType.js";
import emitter, { alarmEventName } from "@/assets/js/alarmEventPub.js";
let mapMain = null;
let _mapGeometery = null;
let placeInfoWindow = [];
let alarmInfoWindow = [];
let alarmIconList = {
  1: { url: require("@/assets/img/map/mapPoint/task-level-one.png") },
  2: { url: require("@/assets/img/map/mapPoint/task-level-two.png") },
  3: { url: require("@/assets/img/map/mapPoint/task-level-three.png") },
  4: { url: require("@/assets/img/map/mapPoint/task-level-four.png") },
  5: { url: require("@/assets/img/map/mapPoint/task-level-five.png") },
};
export default {
  name: "JuvenileMap",
  components: { aoiDetail, MapAlarm },
  props: {
    // 未成年人场所点位信息
    juvenilePlaceList: {
      type: Array,
      default: () => [],
    },
    // 地图图层配置信息
    mapLayerConfig: {
      type: Object,
      default: () => {
        return {
          tracing: false, // 是否需要刻画轨迹
          showStartPoint: false, // 是否显示起点终点图标
          mapToolVisible: false, // 框选操作栏
          selectionResult: true, // 是否显示框选结果弹框
          resultOrderIndex: false, // 搜索结果排序,
          showLatestLocation: false, // 显示地图最新位置
          selectType: false,
        };
      },
    },
    // 资源图层-选中的图层名称
    layerCheckedNames: {
      type: Array,
      default: () => [],
    },
    // 场所二级图层类型
    layerTypePlaceList: {
      type: Array,
      default: () => [],
    },
    // 场所档案跳转
    isShowArchive: {
      type: Boolean,
      default: false,
    },
    goArchiveInfo: {
      type: Function,
      default: () => {},
    },
    heatData: {
      type: Array,
      default: () => [],
    },
    layerOverlayStatusMap: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    juvenilePlaceList: {
      handler(val) {
        if (val.length) {
          this._initSystemPoints3MapJuvenile(val);
        }
      },
    },
    // 选中的图层
    layerCheckedNames: {
      handler(list) {
        this.setCheckedLayer();
      },
    },
    layerOverlayStatusMap: {
      handler() {
        this.controlLayerOverlayStatus();
      },
    },
    // 热力图数据
    heatData: {
      handler(list) {
        this.renderHeatMap(list, 20);
      },
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      alarmData: {},
      placeInfo: {},
      placeLayer: {},
      heatMapLayer: null,
    };
  },
  async mounted() {
    emitter.on(alarmEventName, this.juvenileAlarmList);
    await this.getMapConfig();
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      resourceCoverage: "map/getResourceCoverage",
      individuation: "individuation",
      ignoreDevices: "player/getIgnoreDevices",
    }),
    // 图层名称数组
    layerTypeList() {
      let list = this.layerTypePlaceList.map((item) => item.key);
      return list;
    },
  },
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain.init(mapId, data, style);
        _mapGeometery = new MapPlatForm.Base.MapGeometry(mapMain.map);
        this.$emit("onload"); // 地图加载完成，避免因为watch中触发地图渲染，但是地图此时还没渲染完成，报错
        this.configDefaultMap();
        // 地图加载完成
        this.$emit("inited");
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    // 设置资源图层
    setCheckedLayer() {
      if (mapMain) {
        this.$nextTick(() => {
          const clusterLayer = mapMain.map.getLayerByName("聚合图层");
          this.layerTypeList.forEach((e) => {
            const show = this.layerCheckedNames
              .filter((item) => item)
              .includes(e);
            if (clusterLayer) clusterLayer.setMakrerTypeVisiable(e, show);
            this.showOrHidePlaceLayer(e, show);
          });
          this.controlLayerOverlayStatus();
          this.closeplaceInfoWindow(); //关闭弹窗
          // 重叠聚合点位过滤
          // mapMain.poleGroupPoints.forEach((v) => {
          //   if (this.layerCheckedNames.includes(v.LayerType)) {
          //     v.isHide = false;
          //   } else {
          //     v.isHide = true;
          //   }
          // });
        });
      }
    },
    controlLayerOverlayStatus() {
      const val = this.layerOverlayStatusMap || {};
      const statusKeys = Object.keys(val);
      if (!statusKeys.length) return;
      const clusterLayer = mapMain.map.getLayerByName("聚合图层");
      const overlays = clusterLayer.getOverlaysArry()[0].dataJson;
      overlays.forEach((item) => {
        const ext = item.ext;
        if (this.layerCheckedNames.includes(ext.LayerType)) {
          const visible = this.getStatusKeys(ext);
          item.setVisible(visible);
        }
      });
      clusterLayer.refresh();
      this.showOrHidePlaceLayerMarket();
    },
    getStatusKeys(ext) {
      const statusKeys = Object.keys(this.layerOverlayStatusMap);
      const checked = statusKeys.filter((key) =>
        this.layerOverlayStatusMap[key].includes(ext[key])
      );
      return checked.length === statusKeys.length;
    },
    //多边形场所范围弹框
    aoiModelShow(pointData) {
      const { properties, ...ontherInfo } = pointData;
      this.placeInfo = {
        ...ontherInfo,
        ...properties,
      };
      const point = new NPMapLib.Geometry.Point(
        pointData.center[0],
        pointData.center[1]
      );
      //   mapMain.map.setCenter(point);
      mapMain.map.panTo(point);
      this.$nextTick(() => {
        const dom = this.$refs["aoiDetail"].$el;
        const opts = {
          offset: new NPMapLib.Geometry.Size(-560 / 2, -280 * 1.1), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          positionBlock: {
            // 箭头样式
            imageSrc: require("@/assets/img/map/triangle.png"),
            imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
            offset: new NPMapLib.Geometry.Size(-0, 80),
          },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        placeInfoWindow.push(infoWindow);
      });
    },
    // 加载未成年人场所到地图上
    _initSystemPoints3MapJuvenile(points) {
      this.$nextTick(() => {
        // 加载点位
        this.renderClusterOverlayMarkers(points);
        this.initRenderPolygon(points);
      });
    },
    renderClusterOverlayMarkers(points) {
      mapMain.renderMarkers(
        mapMain.convertSystemPoint3MapArrPointJuvenile(points),
        this.getPlaceEvents(),
        true
      );
    },
    // 地图场所事件
    getPlaceEvents(points) {
      const opts = {
        click: (point) => {
          this.aoiModelShow(point.ext);
        },
        getUrl: (count, marker) => {
          const feature = marker?.feature || marker;
          const iconType = feature.ext.iconType;
          const {
            clusterUrl,
            url: normalUrl,
            hoverUrl,
          } = placeType?.[iconType] || {};
          // 获取用户的
          let url;
          if (count) {
            // 有count, 说明是聚合类型
            url = clusterUrl;
          } else {
            url = normalUrl;
          }
          return url;
        },
      };
      return opts;
    },
    // 未成年人报警
    juvenileAlarmList(obj) {
      this.$nextTick(() => {
        // 加载点位
        // mapMain.renderMarkers(
        //   mapMain.convertSystemPoint3MapArrPointJuvenileAlarm(points),
        //   this.getAlarmEvents()
        // );
        const points = [obj];
        //清除图层
        let overlayLayer = mapMain.map.getLayerByName("Juvenile-Alarm");
        if (!overlayLayer) {
          overlayLayer = new NPMapLib.Layers.OverlayLayer(
            "Juvenile-Alarm",
            false
          );
          overlayLayer.setZIndex(800);
          mapMain.map.addLayer(overlayLayer);
        } else {
          overlayLayer.removeAllOverlays();
        }
        points.forEach((item) => {
          if (!item.geoPoint) {
            return;
          }
          const icon = new NPMapLib.Symbols.Icon(
            alarmIconList[item.bgIndex]?.url,
            new NPMapLib.Geometry.Size(84, 66)
          );
          const marker = new NPMapLib.Symbols.Marker(
            new NPMapLib.Geometry.Point(item.geoPoint.lon, item.geoPoint.lat)
          );
          marker.setIcon(icon);
          overlayLayer.addOverlay(marker);
          marker.addEventListener(NPMapLib.MARKER_EVENT_CLICK, (e) => {
            this.showAlarmDetail(item);
          });
        });
        overlayLayer.show();
        mapMain.map.setCenter({ ...obj.geoPoint });
        // 直接打开弹窗
        console.log(obj, "obj");
        this.showAlarmDetail(obj);
      });
    },
    getAlarmEvents() {
      const opts = {
        click: (point) => {
          // debugger
          // this.emits("alarmPointClick", point);
        },
        mouseover: (marker) => {
          // debugger
        },
      };
      return opts;
    },
    showAlarmDetail(data) {
      this.alarmData = data;
      this.$refs["mapAlarm"].show();
      const point = new NPMapLib.Geometry.Point(
        data.geoPoint.lon,
        data.geoPoint.lat
      );
      mapMain.map.panTo(point);
      this.$nextTick(() => {
        const dom = this.$refs["mapAlarm"].$el;
        const opts = {
          offset: new NPMapLib.Geometry.Size(-270 / 2, -187 * 1.2), // 信息窗位置偏移值
          iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
          enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
          autoSize: true, // 默认true, 窗口大小是否自适应
          isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
          // positionBlock: {
          //   // 箭头样式
          //   imageSrc: require("@/assets/img/map/triangle.png"),
          //   imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
          //   offset: new NPMapLib.Geometry.Size(-0, 80),
          // },
        };
        const infoWindow = new NPMapLib.Symbols.InfoWindow(
          point,
          null,
          null,
          opts
        );
        infoWindow.setContentDom(dom);
        mapMain.map.addOverlay(infoWindow);
        infoWindow.open(null, false);
        infoWindow.updatePosition();
        alarmInfoWindow.push(infoWindow);
      });
    },
    initRenderPolygon(points) {
      const placeLayerData = {};
      for (let i = 0; i < points.length; i++) {
        const data = points[i];
        const layerType = data.LayerType;
        const iconType = data.iconType;
        const [lon, lat] = JSON.parse(data.centerPoint || "[]");
        const item = {
          properties: {
            ...data,
            geoPoint: {
              lon,
              lat,
            },
          },
          geometry: JSON.parse(data.coord),
        };
        if (!placeLayerData[layerType]) {
          placeLayerData[layerType] = {
            data: [item],
            color: placeType?.[iconType]?.color,
          };
        } else {
          placeLayerData[layerType].data.push(item);
        }
      }
      Object.keys(placeLayerData).forEach((key) => {
        this.setPolygon(placeLayerData[key], key, true);
      });
    },
    addContainFeaturesListener() {
      mapMain.map.addEventListener(NPMapLib.MAP_EVENT_DRAG_END, () => {
        this.getExtentFeatures();
      });
      mapMain.map.addEventListener(NPMapLib.MAP_EVENT_ZOOM_END, () => {
        this.getExtentFeatures();
      });
    },
    // 获取可视区域的点位
    getExtentFeatures() {
      const extents = mapMain.map.getExtent();
      const clusterLayer = mapMain.map.getLayerByName("聚合图层");
      const features = clusterLayer.containFeatures(extents, (marker) => {
        return marker;
      });
      this.setExtentPlaceLayer(features.slice(0, 500));
    },
    setExtentPlaceLayer(features) {
      const featureIdMap = features.reduce(
        (pre, cur) => ({ ...pre, [cur.ext.id]: true }),
        {}
      );
      const alreadyIdMap = {};
      Object.keys(this.placeLayer).forEach((LayerType) => {
        const overlays = this.placeLayer[LayerType].getOverlaysArry();
        overlays.forEach((overlay) => {
          const overlayId = overlay._data.properties?.id;
          if (featureIdMap[overlayId]) {
            alreadyIdMap[overlayId] = true;
          } else {
            this.placeLayer[LayerType].removeOverlay(overlay);
          }
        });
      });
      const newPoints = [];
      for (let i = 0; i < features.length; i++) {
        const marker = features[i];
        if (!alreadyIdMap[marker.ext.id]) newPoints.push({ ...marker.ext });
      }
      if (newPoints.length > 0) this.initRenderPolygon(newPoints);
      this.showOrHidePlaceLayerMarket()
    },
    setPolygon(aoiData, aoiType, isPlaceArchive) {
      var mapGeometry = new MapPlatForm.Base.MapGeometry(mapMain.map);
      if (!this.placeLayer[aoiType]) {
        this.placeLayer[aoiType] = new NPMapLib.Layers.OverlayLayer(
          aoiType,
          false
        );
        mapMain.map.addLayer(this.placeLayer[aoiType]);
      }
      for (var i = 0; i < aoiData.data.length; i++) {
        const { geometry, properties } = aoiData.data[i];
        if (geometry) {
          const polygonItem = mapGeometry.getGeometryByGeoJson(
            geometry,
            mapMain.map
          );
          polygonItem.setStyle({
            color: aoiData.color, //颜色
            fillColor: aoiData.color, //填充颜色
            weight: 2, //宽度，以像素为单位
            opacity: 1, //透明度，取值范围0 - 1
            fillOpacity: 0.5, //填充的透明度，取值范围0 - 1,
            lineStyle: NPMapLib.LINE_TYPE_DASH, //样式
          });
          polygonItem.setData({
            properties: properties,
            center: JSON.parse(properties.centerPoint),
          });
          this.placeLayer[aoiType].addOverlay(polygonItem);
          // this.placeLayer[aoiType].setZIndex(400);
          if (isPlaceArchive) {
            polygonItem.addEventListener(
              NPMapLib.POLYGON_EVENT_CLICK,
              (point) => {
                this.aoiModelShow(point._data);
              }
            );
          }
        }
        // 场所档案只需要一个围栏，不添加事件
        // if (!isPlaceArchive) {
        //   let that = this;
        //   polygonItem.addEventListener(
        //     NPMapLib.POLYGON_EVENT_CLICK,
        //     function (point) {
        //       that.aoiModelShow(point._data);
        //     }
        //   );
        // }
      }

      //右键单击
      // let that = this;
      // mapMain.map.addEventListener(
      //   NPMapLib.MAP_EVENT_RIGHT_CLICK,
      //   function (point) {
      //     that.closeplaceInfoWindow();
      //   }
      // );
    },
    // 删除单个类型多边形
    removeSetPolygon(aoiType) {
      if (this.placeLayer[aoiType]) {
        this.placeLayer[aoiType].removeAllOverlays();
        this.placeLayer[aoiType] = null;
      }
    },
    showOrHidePlaceLayerMarket() {
      Object.keys(this.placeLayer).forEach((layer) => {
        const overlays = this.placeLayer[layer].getOverlaysArry();
        overlays.forEach((overlay) => {
          const properties = overlay._data.properties;
          const visible = this.getStatusKeys(properties);
          visible ? overlay.show() : overlay.hide();
        });
      });
    },
    // 控制图层显隐
    showOrHidePlaceLayer(aoiType, show = true) {
      if (this.placeLayer[aoiType]) {
        show
          ? this.placeLayer[aoiType].show()
          : this.placeLayer[aoiType].hide();
      }
    },

    // 渲染热力图
    renderHeatMap(dataList, radius) {
      if (!dataList || dataList.length === 0) {
        return false;
      }
      const opt = {
        isBaseLayer: false,
        opacity: 1.0,
        projection: "EPSG:4326",
        visible: true,
        radius: radius,
        // name: "heatLayer",
      };
      // 只存在一个 热力图
      if (!this.heatMapLayer) {
        this.heatMapLayer = new NPMapLib.Layers.HeatMapLayer("heatLayer", opt);
        mapMain.map.addLayers([this.heatMapLayer]);
      }
      // max 数据的 获取
      let countMax = 0;
      let countMin = 0;
      let sum = 0 ;
      const _dataList = dataList
        ?.filter((item) => item.lat && item.lon)
        ?.map((val) => {
          const currentCount = val.numCount || 1;
          sum+=currentCount;
          countMax = Math.max(countMax, currentCount);
          countMin = Math.min(countMin, currentCount);
          return {
            lat: val.lat,
            lon: val.lon,
            count: currentCount,
          };
        });
      const dataset = {
        max: parseInt(sum/dataList.length),
        min: countMin,
        data: _dataList,
      };
      try {
        this.heatMapLayer.setDataset(dataset);
      } catch (err) {
        this.configDefaultMap();
      }
    },
    // 控制热力图图层显隐
    setHeatmapVisible(show = true) {
      if (this.heatMapLayer) {
        show ? this.heatMapLayer.show() : this.heatMapLayer.hide();
      }
    },

    // 删除所有类型多边形
    removeSetPolygonAll() {
      for (let i in this.placeLayer) {
        if (this.placeLayer[i]) {
          this.placeLayer[i].removeAllOverlays();
          this.placeLayer[i] = null;
        }
      }
    },
    closeplaceInfoWindow() {
      placeInfoWindow.forEach((row) => {
        row.close();
      });
      placeInfoWindow = [];
    },
    closeAlarmInfoWindow() {
      alarmInfoWindow.forEach((row) => {
        row.close();
      });
      alarmInfoWindow = [];
    },
  },
};
</script>

<style lang="less" scoped>
.map-box {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
</style>
