<template>
  <div class="route_player">
    <div class="route_player_header">
      <Icon type="ios-undo" @click="handleback" />
      <span @click="handleback">警卫路线</span
      ><span>> {{ routeInfo.description }}</span>
    </div>
    <div class="route_player_content">
      <div class="operation-bar">
        <Button
          class="operate-button"
          size="small"
          :type="isPlaying ? 'error' : 'primary'"
          @click="playRoute()"
          >{{ !isPlaying ? "播放" : "停止" }}</Button
        >
      </div>
      <div class="manual-bar" :class="{ disabled: !isPlaying }">
        <Checkbox
          class="manual-checkbox"
          v-model="isOpenManual"
          :disabled="!isPlaying"
          @on-change="manualPlayHandle"
          >切换至手动播放</Checkbox
        >
        <i
          class="iconfont icon-doubleright"
          :class="{ disabled: !isOpenManual }"
          @click="nextManualPlay(routeInfo)"
        ></i>
        <i
          class="iconfont icon-doubleleft"
          :class="{ disabled: !isOpenManual }"
          @click="prevManualPlay(routeInfo)"
        ></i>
      </div>
      <div class="route-content">
        <div class="gpsinfo">
          <p>车队代号:&nbsp;&nbsp;{{ routeInfo.vehicleCode || "暂无" }}</p>
          <p class="gpscode nui-border-bottom">
            GPS编号:&nbsp;&nbsp;{{ routeInfo.gpsId || "暂无" }}
          </p>
        </div>
        <p class="camera-number-title nui-font">
          共有&nbsp;
          <span class="nui-font-number">{{ routeInfo.devices.length }}</span
          >&nbsp;个摄像机
        </p>
        <div class="route-start nui-border-bottom">
          <i class="iconfont i-qd"></i>
          <span :title="routeInfo.startPointName">{{
            routeInfo.startPointName || "暂无起点名称"
          }}</span>
        </div>
        <div class="route-play-cameras-wrap">
          <div
            v-for="(item, index) in routeInfo.devices"
            :key="index"
            class="device"
            :class="playingIds.includes(item.deviceId) ? 'playing' : ''"
            @mousemove="mousemoveItem(item)"
            @mouseout="mouseoutItem(item)"
            @dblclick="clickItem(item)"
          >
            <i class="iconfont" :class="getIconType(item)"></i>
            <span class="name">{{ item.deviceName }}</span>
          </div>
        </div>
        <div class="route-end nui-border-top">
          <i class="iconfont i-zd"></i>
          <span :title="routeInfo.endPointName">{{
            routeInfo.endPointName || "暂无终点名称"
          }}</span>
        </div>
      </div>
    </div>
    <singleScreen
      ref="singleScreen"
      @playingDevice="setPlayingDevice"
    ></singleScreen>
  </div>
</template>

<script>
import singleScreen from "./singleScreen";
export default {
  props: {
    routeInfo: {
      type: Object,
      defaule: () => ({}),
    },
  },
  components: {
    singleScreen,
  },
  data() {
    return {
      isOpenManual: false,
      isPlaying: false,
      playingDevice: {},
    };
  },
  computed: {
    playingIds() {
      let ids = [];
      Object.keys(this.playingDevice).forEach((key) => {
        if (this.playingDevice[key]) {
          ids.push(this.playingDevice[key].deviceId);
        }
      });
      return ids;
    },
  },
  mounted() {
    this.$parent.beforeSetAnimation(this.routeInfo);
  },
  methods: {
    // 图标
    getIconType(camera) {
      return Toolkits.getDeviceIconType(camera);
    },
    handleback() {
      // 退出前停止小车移动
      if (this.isPlaying) this.$parent.stopRoute(this.routeInfo);
      this.$emit("back");
    },
    mousemoveItem(item) {
      this.$emit("mousemoveItem", item);
    },
    mouseoutItem(item) {
      this.$emit("mouseoutItem", item);
    },
    playRoute() {
      this.isPlaying = !this.isPlaying;
      if (this.isPlaying) {
        this.$parent.playRoute(this.routeInfo);
      } else {
        this.$parent.stopRoute(this.routeInfo);
      }
    },
    manualPlayHandle(val) {},
    //手动播放上一个
    prevManualPlay(item) {
      if (this.isPlaying && this.isOpenManual) {
        let deviceId =
          this.playingDevice.passDevice.deviceId ||
          this.playingDevice.nowDevice.deviceId ||
          this.playingDevice.nextDevice.deviceId;
        if (deviceId) {
          let index = this.routeInfo.devices.findIndex(
            (v) => v.deviceId == deviceId
          );
          if (index <= 0) {
            this.$Message.info("已经到第一个");
            return;
          } else {
            this.playCameraOfRoute(this.routeInfo.devices[index - 1], "prev");
          }
        } else {
          this.playCameraOfRoute(
            this.routeInfo.devices[this.routeInfo.devices.length - 1],
            "prev"
          );
        }
      }
    },
    //手动播放下一个
    nextManualPlay(item) {
      if (this.isPlaying && this.isOpenManual) {
        let deviceId =
          this.playingDevice.nextDevice.deviceId ||
          this.playingDevice.nowDevice.deviceId ||
          this.playingDevice.passDevice.deviceId;
        if (deviceId) {
          let index = this.routeInfo.devices.findIndex(
            (v) => v.deviceId == deviceId
          );
          if (index >= this.routeInfo.devices.length - 1) {
            this.$Message.info("已经到最后一个");
            return;
          } else {
            this.playCameraOfRoute(this.routeInfo.devices[index + 1], "next");
          }
        } else {
          this.playCameraOfRoute(this.routeInfo.devices[0], "next");
        }
      }
    },
    routeRefresh(info) {
      if (info.index !== undefined) {
        this.playVideoByOneDevice(info);
      } else {
        if (info.isPlaying && !this.isOpenManual) {
          this.playCameraOfRoute(info.curCamera, "next");
        }
        if (!info.isPlaying) {
          this.isPlaying = false;
          this.isOpenManual = false;
          this.$refs.singleScreen && this.$refs.singleScreen.clearAll();
          this.setPlayingDevice({});
        }
      }
    },
    clickItem(item) {
      this.$refs.singleScreen && this.$refs.singleScreen.playVideoByIndex(item);
    },
    /**
     * 播放视频
     *
     */
    playCameraOfRoute(item, type) {
      this.$refs.singleScreen && this.$refs.singleScreen.playVideo(item, type);
    },
    setPlayingDevice(devices) {
      this.playingDevice = devices;
      this.$parent.setPlayingDeviceIcon(this.playingDevice);
    },
    // 一个设备播放已通过
    playVideoByOneDevice(info) {
      this.$refs.singleScreen &&
        this.$refs.singleScreen.playVideoByOneDevice(info.device, info.index);
    },
  },
};
</script>

<style scoped lang="less">
.route_player {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  max-height: calc(~"100% - 20px");
  .route_player_header {
    height: 40px;
    background: #2c86f8;
    border-bottom: 1px solid #d3d7de;
    color: #fff;
    font-size: 14px;
    line-height: 40px;
    padding-left: 14px;
    display: flex;
    align-items: center;
    .icon-jiantou {
      transform: rotate(90deg);
      display: inline-block;
      cursor: pointer;
    }
    .ivu-icon-ios-undo {
      font-size: 20px;
      cursor: pointer;
    }
    span {
      font-size: 14px;
      cursor: pointer;
      margin-left: 10px;
    }
  }
  .route_player_content {
    .operation-bar {
      text-align: right;
      padding: 10px 10px 0 10px;
    }
    .manual-bar {
      height: 40px;
      line-height: 40px;
      padding: 0 20px;
      &.disabled {
        .iconfont {
          color: #b1b5bf;
          cursor: not-allowed;
        }
      }
      .iconfont {
        float: right;
        margin-left: 10px;
        cursor: pointer;
        color: #2c86f8;
      }
    }
    .route-content {
      height: calc(~"100% - 140px");
      padding: 0 20px;
    }
    .gpsinfo {
      padding: 5px 0px;
      border-top: 1px solid #dddddd;
      border-bottom: 1px solid #dddddd;
      p {
        height: 30px;
        line-height: 30px;
        color: #333333;
      }
      .gpscode {
        height: 35px;
      }
    }
    .camera-number-title {
      line-height: 40px;
    }
    .route-start,
    .route-end {
      width: 285px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      span {
        color: #333333;
      }
    }
    .route-start {
      margin-top: 0px;
      padding-bottom: 5px;
      .i-qd {
        height: 32px;
        width: 23px;
        display: inline-block;
        background: url("~@/assets/img/map/track/blue-start.png");
        margin-top: 5px;
      }
    }
    .route-end {
      padding-top: 5px;
      margin-bottom: 10px;
      .i-zd {
        height: 32px;
        width: 23px;
        display: inline-block;
        background: url("~@/assets/img/map/track/red-end.png");
        margin-top: 5px;
      }
    }
    .xui-loading .xui-loading-shim {
      top: 80px;
    }
    .route-play-cameras-wrap {
      max-height: 400px;
      padding: 10px;
      overflow-y: auto;
      border-top: 1px solid #dddddd;
      border-bottom: 1px solid #dddddd;
      .device {
        display: flex;
        align-items: center;
        padding: 3px;
        &:hover {
          cursor: pointer;
          background: rgba(44, 134, 248, 0.2);
        }
        i {
          margin-right: 5px;
          color: #2c86f8;
        }
        .name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        &.playing {
          color: #ff8d33;
          font-weight: bold;
          i::before {
            content: "";
            display: block;
            color: #ff8d33;
            width: 18px;
            height: 18px;
            background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
          }
        }
      }
    }
  }
}
</style>
