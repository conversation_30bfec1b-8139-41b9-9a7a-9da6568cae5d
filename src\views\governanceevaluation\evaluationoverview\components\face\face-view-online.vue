<!-- 人脸视图库在线率  车辆视图库在线率 -->
<template>
  <div class="face-view-online" ref="onlineScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="tableList">
        <slot name="search"></slot>
        <div class="left-div">
          <ui-table
            class="ui-table"
            :maxHeight="contentClientHeight"
            :loading="loading"
            :table-columns="tableColumns"
            :table-data="tableData"
            ref="table"
          >
            <template #catchPlace>
              <span>{{ getCatchPlace }}</span>
            </template>
            <template #action="{ row }">
              <ui-btn-tip icon="icon-lixianjilu" content="离线记录" @click.native="checkReason(row)"></ui-btn-tip>
            </template>
          </ui-table>
          <ui-page
            class="page"
            :page-data="searchData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
      </div>
    </div>
    <nonconformance
      ref="nonconformance"
      title="离线记录"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonLoading="reasonLoading"
    ></nonconformance>
  </div>
</template>
<style lang="less" scoped>
.face-view-online {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 500px) !important;
      //min-height: 290px !important;
    }
  }

  .information-header {
    margin-top: 10px;
    height: 150px;
    display: flex;
    @{_deep}.information-statistics {
      width: 100%;
      height: 140px;
      padding: 20px;
      background: var(--bg-sub-content);
      margin-right: 0;
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 32.9% !important;
      .monitoring-data {
        margin-left: 30px;
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }

    .tableList {
      height: 100%;
      width: 100%;
      background: var(--bg-content);
      .left-div {
        position: relative;
        width: 100%;
        min-height: 1px;
      }
      .ui-table {
        width: 100%;
      }
      .page {
        padding-right: 0;
      }
      .ui-table-scroll-nodata {
        /deep/ .ivu-table-tip {
          overflow-x: auto;
        }
      }
    }
  }
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'face-view-online',
  data() {
    return {
      statisticsList: [
        {
          name: '检测平台数量',
          value: 0,
          icon: 'icon-renlianjiancepingtaishuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
        },
        {
          name: '昨日在线平台数量',
          value: 0,
          icon: 'icon-zuorizaixianpingtaishuliang',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
        },

        {
          name: '昨日离线平台数量',
          value: 0,
          icon: 'icon-zuorilixianpingtaishuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
      ],
      loading: false,
      orgRegionName: '',
      tableData: [],
      searchData: { totalCount: 0, pageNum: 1, pageSize: 20 },
      statisticalList: {},
      paramsList: {},
      contentClientHeight: 0,
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '组织名称', key: 'orgName' },
        { title: '离线日期', key: 'leaveTime' },
        { title: '平台类型', key: 'platformType' },
      ],
      reasonTableData: [],
      reasonLoading: false,
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20, orgCode: '', taskIndexId: null },
      exportLoading: false,
    };
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.onlineScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 110 * proportion : 0;
  },

  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    // 不合格原因
    checkReason(row) {
      this.reasonPage.orgCode = row.orgCode;
      this.reasonPage.taskIndexId = row.taskIndexId;
      this.getReason();
      this.$refs.nonconformance.init();
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        orgCode: this.reasonPage.orgCode,
        taskIndexId: this.reasonPage.taskIndexId,
      };
      if (this.paramsList.indexType === 'VIDEO_PLATFORM_ONLINE_RATE') {
        params.displayType = 'ORG';
        params.orgRegionCode = this.reasonPage.orgCode;
      }
      try {
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },

    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    tableColumns() {
      let { indexId } = this.paramsList;
      let commonColumns = [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: `${this.orgRegionName}`, key: 'orgName', tooltip: true, minWidth: 150 },
        { title: '平台类型', key: 'catchPlace', slot: 'catchPlace', tooltip: true, minWidth: 150 },
        { title: '本月在线率', key: 'onlineRatioText', tooltip: true, minWidth: 150 },
      ];
      let options = [
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          fixed: 'right',
        },
      ];
      let carOnlineColumns = [
        {
          title: '昨日在线状态',
          key: 'onlineStatusText',
          tooltip: true,
          minWidth: 150,
        },
        { title: '离线时间', key: 'unOnlineDate', tooltip: true, minWidth: 150 },
        { title: '本月在线天数', key: 'onlineDayM', tooltip: true, minWidth: 150 },
        { title: '本月离线天数', key: 'unOnlineDayM', tooltip: true, minWidth: 150 },
        { title: '本月报备天数', key: 'reportDayM', tooltip: true, minWidth: 150 },
        {
          title: '历史在线天数',
          key: 'onlineDayH',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '历史离线天数',
          key: 'unOnlineDayH',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '历史报备天数',
          key: 'reportDayH',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '检测时间',
          key: 'checkDate',
          tooltip: true,
          minWidth: 150,
        },
      ];
      let videoOnlineColumns = [
        { title: '今日离线时长', key: 'unOnlineTimeText', tooltip: true, minWidth: 150 },
        { title: '离线时间', key: 'leaveTime', tooltip: true, minWidth: 150 },
        { title: '本月在线时长', key: 'onlineTimeMText', tooltip: true, minWidth: 150 },
        { title: '本月离线时长', key: 'unOnlineTimeMText', tooltip: true, minWidth: 150 },
        { title: '本月报备次数', key: 'reportNumM', tooltip: true, minWidth: 150 },
        { title: '本月报备时长', key: 'reportTimeM', tooltip: true, minWidth: 150 },
        { title: '历史在线时长', key: 'onlineTimeHText', tooltip: true, minWidth: 150 },
        { title: '历史离线时长', key: 'unOnlineTimeHText', tooltip: true, minWidth: 150 },
        { title: '历史报备次数', key: 'reportNumH', tooltip: true, minWidth: 150 },
        { title: '历史报备时长', key: 'reportTimeH', tooltip: true, minWidth: 150 },
      ];
      if (indexId === 7004) {
        return [...commonColumns, ...videoOnlineColumns, ...options];
      }
      return [...commonColumns, ...carOnlineColumns, ...options];
    },
    getCatchPlace() {
      let { indexId } = this.paramsList;
      if (indexId === 7002) {
        return '人脸视图库';
      } else if (indexId === 7003) {
        return '车辆视图库';
      } else if (indexId === 7004) {
        return '联网共享平台';
      }
      return '';
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticsList[0].value = val.totalNum;
          this.statisticsList[1].value = val.onlineNum;
          this.statisticsList[2].value = val.unOnlineNum;
          if (this.paramsList.indexId == 7002) {
            this.statisticsList[0].icon = 'icon-renlianjiancepingtaishuliang';
          } else if (this.paramsList.indexId == 7004) {
            this.statisticsList[0].icon = 'icon-lianwanggongxiangjiancepingtaishuliang';
          } else {
            this.statisticsList[0].icon = 'icon-cheliangjiancepingtaishuliang';
          }
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          if (this.paramsList.displayType == 'REGION') {
            this.orgRegionName = '行政区划';
          } else {
            this.orgRegionName = '组织机构';
          }

          this.getTableData();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    nonconformance: require('../basics/nonconformance.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    statistics: require('@/components/icon-statistics').default,
  },
};
</script>
