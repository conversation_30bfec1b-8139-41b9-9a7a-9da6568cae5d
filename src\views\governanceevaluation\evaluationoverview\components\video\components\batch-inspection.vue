<template>
  <ui-modal class="batch-inspection" v-model="visible" title="批量复检" :styles="styles">
    <Form :label-width="85" ref="modalData" :model="searchData" :rules="ruleCustom" inline>
      <FormItem label="复检方法：">
        <Select v-model="searchData.videoGenera" class="width-lg">
          <Option v-for="item in recheckList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="算 法：">
        <Select v-model="searchData.videoGeneral" class="width-lg">
          <Option v-for="item in ocrList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <div class="base-text-color f-14">复检设备：</div>
      <div class="information-main">
        <div class="search-wrapper">
          <ui-label class="mr-lg" label="设备编码" width="70">
            <Input class="input-width" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
          </ui-label>
          <ui-label label="设备名称" :width="70" class="mr-lg">
            <Input class="input-width" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
          </ui-label>
          <ui-label label="检测结果" :width="70" class="mr-lg">
            <Select v-model="searchData.qualified" clearable placeholder="请选择检测结果" class="width-input">
              <Option :value="1">合格</Option>
              <Option :value="2">不合格</Option>
              <Option :value="3">无法检测</Option>
            </Select>
          </ui-label>
          <ui-label :width="0" class="fl btns" label=" ">
            <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
            <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
          </ui-label>
        </div>
        <div class="list">
          <ui-table :minus-height="minusTable" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
            <template #detectionMode="{ row }">
              {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
            </template>
            <template #clockSkew="{ row }">
              <span v-if="row.clockSkew">{{ row.clockSkew }} s</span>
            </template>
            <!-- 点位类型 -->
            <template #sbdwlx="{ row }">
              <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
            </template>
            <template #deviceId="{ row }">
              <span class="font-active-color pointer device-id" :class="row.rowClass">{{ row.deviceId }}</span>
            </template>
            <template #qualified="{ row }">
              <span
                :class="{
                  color_qualified: row.qualified === '1',
                  color_unqualified: row.qualified === '2',
                }"
              >
                {{ row.qualified === '1' ? '合格' : row.qualified === '2' ? '不合格' : '无法检测' }}
              </span>
            </template>
            <template #ntpTime="{ row }">
              <span>
                {{ row.ntpTime ? row.ntpTime : '--' }}
              </span>
            </template>
            <template #startTime="{ row }">
              <span>
                {{ !row.startTime ? '--' : row.startTime }}
              </span>
            </template>

            <template #phyStatus="{ row }">
              <span>
                {{ !row.phyStatus ? '--' : row.phyStatus }}
              </span>
            </template>
            <template #reason="{ row }">
              <Tooltip :content="row.reason" transfer max-width="150">
                {{ row.reason }}
              </Tooltip>
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        ></ui-page>
      </div>
    </Form>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'batch-inspection',
  data() {
    return {
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        deviceName: '',
        deviceId: '',
        // customParameters: {
        //   qualified: '',
        // },
        qualified: '',
        user: '',
        password: '',
      },
      ocrList: [{ value: '1', label: '算法1' }],
      recheckList: [{ value: '1', label: '直连SDK' }],
      ruleCustom: {
        // captureDrop: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入抓拍数量突降比例',
        //     trigger: 'blur',
        //   },
        // ],
        // timeDelay: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入时间',
        //     trigger: 'blur',
        //   },
        // ],
        // imageNum: [
        //   {
        //     required: true,
        //     type: 'number',
        //     message: '请输入张数',
        //     trigger: 'blur',
        //   },
        // ],
        // timeFormat: [
        //   {
        //     required: true,
        //     message: '请选择单位',
        //     trigger: 'blur',
        //   },
        // ],
      },
      visible: false,
      styles: {
        width: '9rem',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        { title: '设备物理状态 ', slot: 'phyStatus', minWidth: 120 },
        // { title: '监控点位类型', key: 'orgName', minWidth: 150, tooltip: true },
        {
          title: '检测方式',
          slot: 'detectionMode',
          minWidth: 150,
          tooltip: true,
        },
        { title: '检测结果', slot: 'qualified', minWidth: 150, tooltip: true },
        { title: '异常原因', key: 'reason', minWidth: 150, tooltip: true },
        { title: '北京时间', slot: 'ntpTime', minWidth: 150, tooltip: true },
        {
          title: '对应设备时间',
          slot: 'startTime',
          minWidth: 150,
          tooltip: true,
        },
        { title: '时间误差', slot: 'clockSkew', minWidth: 150, tooltip: true },
      ],
      tableData: [],
      paramsList: {},
      minusTable: 400,
    };
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    init() {
      this.visible = true;
      this.getTableData();
    },
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          displayType: this.paramsList.displayType,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: {
            qualified: this.searchData.qualified,
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
          },
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData = { ...this.searchData, pageNum: 1 };
      this.getTableData();
    },
    reast() {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        qualified: '',
        deviceName: '',
        deviceId: '',
      };
      this.getTableData();
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          this.getTableData(); //表格
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.information-main {
  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    padding-right: 2px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--devider-line);
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
  .search-wrapper {
    margin-top: 10px;
    overflow: hidden;
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    padding-top: 10px;
    .input-width {
      width: 176px;
    }
    .ui-label {
      margin-bottom: 10px;
    }
  }
}
</style>
