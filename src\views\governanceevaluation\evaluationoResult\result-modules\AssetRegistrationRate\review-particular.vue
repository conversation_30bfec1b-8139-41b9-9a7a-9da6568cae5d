<template>
  <div class="review-particular auto-fill">
    <div class="icon-statics-wrapper mb-sm">
      <div class="f-16" style="color: #73d2f6">未注册设备列表</div>
      <div>
        <icon-statics :icon-list="iconStaticsList"></icon-statics>
      </div>
    </div>
    <search-list
      :formItemData="formItemData"
      :search-conditions="searchConditions"
      @startSearch="startSearch"
      @search="startSearch"
      @reset="startSearch"
    >
      <template #otherButton>
        <Button type="primary" class="ml-sm" :loading="exportLoading" @click="onExport">
          <i class="icon-font icon-daochu"></i> 导出
        </Button>
      </template>
    </search-list>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatusText="{ row }">
        <span :style="{ color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)' }">{{
          row.phyStatusText
        }}</span>
      </template>
    </ui-table>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
    <!-- 导出   -->
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>

<script>
import { tableColumns, normalFormData, iconStaticsList } from './utils/enum/ReviewParticular.js';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'review-particular',
  mixins: [particularMixin, dealWatch],
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      formItemData: [],
      iconStaticsList: [],
      searchConditions: {},
      exportLoading: false,
    };
  },
  created() {
    this.tableColumns = tableColumns;
    this.formItemData = normalFormData;
    this.iconStaticsList = iconStaticsList;
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      // 获取列表
      this.getTableData();
      this.getTableDataTotal();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        this.iconStaticsList.forEach((item) => {
          item.count = data[item.fileName] || 0;
        });
      });
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableData();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        // this.pageData.totalCount = data.total
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.pageData.totalCount = data;
      });
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.searchConditions = data;
      this.formData = { ...this.searchConditions };
      this.getTableData();
      this.getTableDataTotal();
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchList: require('./components/search-list').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    exportData: require('../components/export-data').default,
  },
};
</script>

<style lang="less" scoped>
.review-particular {
  padding: 10px;
  overflow-y: auto;
  .icon-statics-wrapper {
    display: flex;
    justify-content: space-between;
  }
  @{_deep} .icon-li {
    &:last-child {
      margin-right: 0 !important;
    }
  }
}
</style>
