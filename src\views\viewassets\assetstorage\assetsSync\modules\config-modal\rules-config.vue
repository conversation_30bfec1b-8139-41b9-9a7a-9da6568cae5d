<template>
  <div class="rules-config">
    <ui-label label="是否自动检测：" class="mb-lg">
      <RadioGroup v-model="formData.autoCheck">
        <Radio class="mr-lg" label="1">是</Radio>
        <Radio label="0">否</Radio>
      </RadioGroup>
      <span class="ml-lg explain">说明：设备同步后（注册到资产库前）系统自动按配置的规则进行质量检测！</span>
    </ui-label>
    <p class="base-text-color mb-lg">请确认需要检测的规则：</p>
    <rule-list :form-model="formModel" :module-action="moduleAction" :index-rule-list="formData.ruleList"></rule-list>
  </div>
</template>
<style lang="less" scoped>
.explain {
  color: rgb(226, 135, 43);
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  data() {
    return {
      moduleAction: {
        indexType: 'BASIC_ACCURACY', // 填报准确率
        taskSchemeId: 2, // 共享联网平台 S
      },
      //标识 add-新增/edit-编辑
      formModel: 'add',
      formData: {
        autoCheck: '0',
        ruleList: [],
      },
    };
  },
  async created() {
    this.moduleAction.taskSchemeId = this.activeStore;
  },
  methods: {
    async getCheckRule() {
      try {
        let res = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: this.moduleAction.indexType },
        });
        let data = res.data.data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: false,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.formData.ruleList = data;
        this.formData.autoCheck = this.defaultParams.autoCheck;
      } catch (err) {
        console.log(err);
      }
    },
    handleIsDefault() {
      let ruleParam =
        this.defaultParams && 'ruleParam' in this.defaultParams ? JSON.parse(this.defaultParams.ruleParam) : [];
      let ruleParamObject = {};
      ruleParam.forEach((ele) => {
        ruleParamObject[ele.ruleId] = ele;
      });
      this.formData.ruleList.forEach((item) => {
        if (ruleParamObject[item.ruleId]) {
          this.$set(item, 'isConfigure', ruleParamObject[item.ruleId].isConfigure);
          // item.isConfigure = ruleParamObject[item.ruleId].isConfigure
        }
      });
    },
  },
  watch: {
    formData: {
      handler(val) {
        this.$emit('updateParams', { ruleParam: JSON.stringify(val.ruleList), autoCheck: val.autoCheck });
      },
      deep: true,
    },
    defaultParams: {
      async handler(val) {
        await this.getCheckRule();
        val ? this.handleIsDefault() : null;
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {},
  props: {
    // 共享联网平台同步: 2 | 视图库同步:3 |一机一档同步:4
    activeStore: {},
    // 默认配置
    defaultParams: {},
  },
  components: {
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
  },
};
</script>
