<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel" :specificConfig="specificConfig">
    <template>
      <FormItem label="设备时间与标准时间允许偏差" prop="timeDelay">
        <Input class="width-input" placeholder="秒" v-model="formData.timeDelay" />
        <span class="base-text-color ml-sm">秒</span>
      </FormItem>
      <FormItem label="检测方法" prop="osdModel">
        <Select
          v-model="formData.osdModel"
          placeholder="请选择"
          transfer
          class="width-lg"
          @on-change="onChangeOsdModel"
        >
          <Option v-for="e in odsCheckModelList" :key="e.dataKey" :value="e.dataKey">{{ e.dataValue }} </Option>
        </Select>
      </FormItem>
      <FormItem class="mb-md">
        <template #label>
          <Tooltip placement="top-start" class="tooltip-sample-graph">
            <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
            <div slot="content">
              <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
            </div>
            检测内容
          </Tooltip>
        </template>
      </FormItem>
      <detect-content
        @on-change="onChangeFormData"
        ref="detectContent"
        form-model="edit"
        v-bind="$props"
        :config-info="formData"
        :detect-content-list="formData.detectContent"
        v-if="formData.osdModel !== 'sdk'"
      >
      </detect-content>
      <detect-content-sdk
        @on-change="onChangeFormData"
        ref="detectContentSdk"
        form-model="edit"
        v-bind="$props"
        :config-info="formData"
        :detect-content-list="formData.detectContent"
        v-else
      >
      </detect-content-sdk>
    </template>
  </basic-recheck>
</template>
<script>
import { mapGetters } from 'vuex';
import {
  defaultDetectContent,
  defaultDetectContentSdk,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 区划与地址信息是否正确',
          value: 'areaLocation',
        },
        {
          label: '',
          text: '(左下角) 摄像机信息是否正确',
          value: 'cameraInfo',
        },
      ],
      specificConfig: {},
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        osdModel: '',
        detectContent: [], // 检测内容
        timeDelay: '',
        isUpdatePhyStatus: '',
        existsTime: false, //-是否在指定区域标注
        //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
        timeFormCheck: false,
        //（右上角）时间信息检测 --位置规范检测：
        timePositionCheck: false,
        //时钟、区划、地址信息需保持右对齐。 右边距: min
        timePositionCheckMin: null,
        //时钟、区划、地址信息需保持右对齐。 右边距: max
        timePositionCheckMax: null,
        //（右上角）时间信息检测 --准确性规则，
        timeDeviationCheck: false,
        //设备时间与标准时间允许偏差 XX 秒
        timeDeviationCheckValue: null,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsArea: false,
        //（右下角）区划与地址信息检测 --位置规范检测：
        areaPositionCheck: false,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
        areaPositionCheckValueLine: false,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
        areaPositionCheckMin: null,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
        areaPositionCheckMax: null,
        //地址信息只能占用一行
        areaPositionCheckLine: false,
        //（右下角）区划与地址信息检测 --准确性规则，
        areaDeviationCheck: false,
        // 省/市/区县、地点信息需与档案信息保持一致
        areaDeviationCheckValue: false,
        //队所（派出所）信息需标注准确（与组织机构表匹配）
        areaDeviationCheckLine: false,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsCamera: false,
        //准确性规则
        cameraDeviationCheck: false,
      },
    };
  },
  created() {
    this.onChangeOsdModel();
  },
  methods: {
    onChangeOsdModel() {
      if (this.formData.osdModel === 'sdk') {
        Object.keys(defaultDetectContent).forEach((key) => {
          delete this.formData[key];
        });
        this.formData = {
          ...this.formData,
          ...defaultDetectContentSdk,
        };
      } else {
        Object.keys(defaultDetectContentSdk).forEach((key) => {
          delete this.formData[key];
        });
        this.formData = {
          ...this.formData,
          ...defaultDetectContent,
        };
      }
    },
    onChangeFormData(formData, detectContent) {
      this.specificConfig = {
        ...formData,
        osdModel: this.formData.osdModel,
        detectContent: detectContent,
        isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
      };
    },
    handleCancel() {
      this.onChangeOsdModel();
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        osdModel: '',
        detectContent: [], // 检测内容
        timeDelay: '',
        isUpdatePhyStatus: '',
      };
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
    }),
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        ...this.$attrs,
      };
    },
  },
  components: {
    DetectContent:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content.vue')
        .default,
    DetectContentSdk:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content-sdk.vue')
        .default,

    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped>
.check-content-label {
  margin-left: 0;
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: #ffffff;
  line-height: 1;
  padding: 10px 12px 10px 0;
  box-sizing: border-box;
  width: 200px; /*no*/
  @{_deep} .ivu-tooltip {
    position: relative;
    &-inner {
      width: 400px;
    }
    &-popper {
      left: -13px !important;
    }
  }
}
.check-content {
  @{_deep}.ivu-form-item-content {
    margin-left: 0 !important;
  }
}
@{_deep} .ivu-modal-body {
  max-height: 650px;
  overflow-y: auto;
}
@{_deep} .detect-content {
  margin-left: 70px;
}

@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: #062f68;
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: #ffffff;
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
    }
  }
}
</style>
