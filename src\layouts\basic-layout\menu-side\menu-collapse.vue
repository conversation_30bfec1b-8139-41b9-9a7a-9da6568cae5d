<template>
  <Dropdown :class="dropdownClasses" placement="right-start">
    <div v-if="topLevel" :class="menuItemClasses">
      <i-menu-side-title :menu="menu" />
    </div>
    <DropdownItem v-else :selected="openNames.indexOf(menu.name) >= 0">
      <i-menu-side-title :menu="menu" />
      <!--<Icon type="ios-arrow-forward" class="i-layout-menu-side-arrow" />-->
      <i class="i-layout-menu-side-arrow"></i>
    </DropdownItem>
    <DropdownMenu slot="list">
      <div
        v-if="showCollapseMenuTitle"
        class="i-layout-menu-side-collapse-title"
      >
        <i-menu-side-title :menu="menu" />
      </div>
      <template v-for="(item, index) in menu.children">
        <template v-if="!item.hidden">
          <i-link
            v-if="item.children === undefined || !item.children.length"
            :to="item.path"
            :target="item.target"
            :key="index"
            @click.native="handleClick(item.path)"
          >
            <DropdownItem
              :divided="item.divided"
              :class="{
                'i-layout-menu-side-collapse-item-selected':
                  item.name === activePath,
              }"
            >
              <i-menu-side-title :menu="item" />
            </DropdownItem>
          </i-link>
          <i-menu-side-collapse v-else :menu="item" :key="index" />
        </template>
      </template>
    </DropdownMenu>
  </Dropdown>
</template>
<script>
import iMenuSideTitle from "./menu-title";
import clickItem from "../mixins/click-item";

import { mapState } from "vuex";

export default {
  name: `iMenuSideCollapse`,
  components: { iMenuSideTitle },
  mixins: [clickItem],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    // 是否是第一级，区分在于左侧和展开侧
    topLevel: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  computed: {
    ...mapState("admin/layout", ["siderTheme", "showCollapseMenuTitle"]),
    ...mapState("admin/menu", ["activePath", "openNames"]),
    dropdownClasses() {
      return {
        "i-layout-menu-side-collapse-top": this.topLevel,
        "i-layout-menu-side-collapse-dark": this.siderTheme === "dark",
      };
    },
    menuItemClasses() {
      return [
        "ivu-menu-item i-layout-menu-side-collapse-top-item",
        {
          "ivu-menu-item-selected ivu-menu-item-active":
            this.openNames.indexOf(this.menu.name) >= 0, // -active 在高亮时，有背景
        },
      ];
    },
  },
};
</script>
