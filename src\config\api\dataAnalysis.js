const service = '/ivdg-analyse-app';
export default {
  getFactorList: `${service}/factor/list`, //get 获取分析内容列表
  getTree: `${service}/task/getTree`, //get 获取任务树
  viewInfo: `${service}/task/view`, //get 根据id获取任务详情
  saveAnalyseTaskConfig: `${service}/task/saveAnalyseTaskConfig`, //post 保存分析任务配置参数
  getDeviceList: `${service}/factor/getDeviceList`, //get 获取设备信息
  getDictData: `${service}/dict/getDictData`, //get 获取字典数据  ---  影响因素
  getHistoryBatchInfoByCustom: `${service}/task/getHistoryBatchInfoByCustom`, //get 检测结果详情-检测时间-下拉选项 自定义选择，返回第N轮
  exportFirstModelData: `${service}/taskResultDetail/exportFirstModelData`, //post 分析结果-设备明细数据导出
  getFirstModelData: `${service}/taskResultDetail/getFirstModelData`, //post 分析结果-设备明细数据列表
  getStatInfoList: `${service}/taskResultDetail/getStatInfoList`, //post 检测结果详情-异常分析列表
  exportStatInfoList: `${service}/taskResultDetail/exportStatInfoList`, //post 分析结果-异常分析列表导出
  getAnalyseEnumResult: `${service}/taskResultDetail/getAnalyseEnumResult`, //post 设备明细数据-分析结果下拉框
  pauseStartIndexJob: `${service}/task/pauseStartIndexJob`, //get 暂停-启用 xxl任务接口
  removeTask: `${service}/task/remove`, //get 删除分析任务(软删除)
  triggerIndexJob: `${service}/task/triggerIndexJob`, //get 立即执行分析任务
  getTableInfo: `${service}/taskResultDetail/getTableInfo`, //post 设备明细数据-动态表格数据(抓拍突降)
  getGraphTrendInfo: `${service}/taskResultDetail/getGraphTrendInfo`, //post 设备明细数据-近X日抓拍趋势
  getHisInfluence: `${service}/taskResultDetail/getHisInfluence`, //post 影响分析因素
  getFirstInfluenceStat: `${service}/taskResultDetail/getFirstInfluenceStat`, //post 影响分析->同分类设备异常率统计
  getSecondInfluenceStat: `${service}/taskResultDetail/getSecondInfluenceStat`, //post 影响分析->异常设备分布占比
  getGraphBarInfo: `${service}/taskResultDetail/getGraphBarInfo`, //post 影响分析->柱状图
  getGraphPieInfo: `${service}/taskResultDetail/getGraphPieInfo`, //post 影响分析->饼图分析
  getGraphScatterInfo: `${service}/taskResultDetail/getGraphScatterInfo`, //post 影响分析->散点图
  getDropDownList: `${service}/taskResultDetail/getDropDownList`, //post 影响分析->抓拍突降平台
  getConfigInfo: `${service}/taskResultDetail/getConfigInfo`, //post 获取一些配置参数
};
