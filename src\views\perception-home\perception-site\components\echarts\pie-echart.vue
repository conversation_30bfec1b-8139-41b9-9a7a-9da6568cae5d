<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xNDuOrPXlC
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [
        { value: 0, name: '设备' },
        { value: 0, name: '人员' },
        { value: 0, name: '物品' },
        { value: 0, name: '事件' },
        { value: 0, name: '地址' }
      ]
    },
    colors: {
      type: Array,
      default: () => ['#198FFF', '#20D7FF', '#08CF84', '#FECB33', '#F8775C']
    }

  },
  data () {
    return {
      myEchart: null
    }
  },
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      this.myEchart = echarts.init(this.$refs.echart)
      var labels = []
      var count = []
      var total = 0
      this.data.forEach((item, i) => {
        labels.push({ name: item.name, textStyle: { color: this.colors[i] }})
        count.push(item.value)
        total = total + item.value
      })

      var option = {
        color: this.colors,
        title: {
          zlevel: 0,
          text: [
            '{value|' + total + '}',
            '{name|总量}'
          ].join('\n'),
          top: 'center',
          left: '47%',
          textAlign: 'center',
          textStyle: {
            rich: {
              value: {
                color: '#fff',
                fontSize: 20,
                fontWeight: 'bold',
                lineHeight: 26
              },
              name: {
                color: '#fff',
                lineHeight: 20
              }
            }
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          left: 0,
          right: 0,
          containLabel: true
        },
        legend: {
          show: false,
          top: 'center',
          right: '12%',
          // selectedMode: false,
          orient: 'vertical',
          icon: `path://M881.387 297.813c38.08 65.387 57.28 136.747 57.28 214.187s-19.094 148.8-57.28 214.187c-38.187 65.28-89.92 117.12-155.2 155.2S589.44 938.667 512 938.667s-148.8-19.094-214.187-57.28c-65.28-38.08-117.013-89.814-155.306-155.307C104.427 660.8 85.333 589.44 85.333 512c0-77.333 19.094-148.693 57.28-214.187 38.08-65.28 89.814-117.013 155.307-155.306C363.2 104.533 434.56 85.333 512 85.333c77.333 0 148.693 19.094 214.187 57.28 65.28 38.187 117.013 89.92 155.2 155.2z m-217.707-47.36C617.387 223.467 566.827 209.92 512 209.92s-105.387 13.547-151.68 40.533-82.987 63.68-109.973 109.974c-26.987 46.293-40.534 96.853-40.534 151.68s13.547 105.386 40.534 151.68c26.986 46.293 63.68 82.986 109.973 109.973 46.293 26.987 96.853 40.533 151.68 40.533s105.387-13.546 151.68-40.533c46.293-26.987 82.987-63.68 109.973-109.973 26.987-46.294 40.534-96.854 40.534-151.68s-13.547-105.387-40.534-151.68c-27.093-46.294-63.786-82.987-109.973-109.974z`,
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 12,
          textStyle: {
            rich: {
              name: {
                color: '#fff',
                fontSize: 14,
                lineHeight: 20,
                padding: [0, 0, 0, 4]
              },
              value: {
                fontSize: 16,
                fontWeight: 'bold',
                lineHeight: 20,
                fontFamily: 'MicrosoftYaHei-Bold'
              }
            }
          },
          data: labels,
          formatter: function (name) {
            var result = ''
            labels.forEach(function (item, i) {
              if (item.name === name) {
                result = `{name| ${item.name}：}{value|${count[i]}}`
              }
            })
            return result
          }
        },
        graphic: {
          elements: [
            {
              type: 'circle',
              style: {
                fill: '#fff'
              },
              left: 'center',
              top: 'center'
            }
          ]
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '80%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: false,
            zlevel: 1,
            label: {
              normal: {
                show: false
                // backgroundColor: '#D8D8D8',
                // position: 'center',
                // formatter: [
                //   '{value|{c}}',
                //   '{name|{b}}'
                // ].join('\n'),
                // rich: {
                //   value: {
                //     color: '#fff',
                //     fontSize: 20,
                //     fontWeight: 'bold',
                //     lineHeight: 26
                //   },
                //   name: {
                //     color: '#fff',
                //     lineHeight: 20
                //   }
                // }
              },
              emphasis: {
                show: false
              }
            },
            labelLine: {
              show: true
            },
            data: this.data
          }
        ]
      }

      this.myEchart.setOption(option)
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
