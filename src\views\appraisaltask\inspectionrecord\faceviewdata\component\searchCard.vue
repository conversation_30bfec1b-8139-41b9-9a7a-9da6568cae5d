<template>
  <div class="base-search">
    <div class="inline search-wrapper">
      <ui-label class="inline mb-sm mr-lg" label="组织机构" :width="70">
        <tree-select
          :tree-data="treeData"
          nodeKey="orgCode"
          class="width-md"
          v-model="searchData.orgCode"
          @current-change="currentChange"
          placeholder="请选择组织机构"
        ></tree-select>
      </ui-label>
      <ui-label class="inline mr-lg" label="抓拍时间" :width="70">
        <div class="date-picker-box">
          <DatePicker
            class="input-width"
            v-model="searchData.minLogTime"
            type="datetime"
            placeholder="请选择开始时间"
            confirm
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="input-width"
            v-model="searchData.maxLogTime"
            type="datetime"
            placeholder="请选择结束时间"
            confirm
          ></DatePicker>
        </div>
      </ui-label>

      <ui-label class="inline mr-lg" label="抓拍设备" :width="70">
        <select-camera
          class="select-camera"
          @pushCamera="pushCamera"
          :device-ids="searchData.deviceIds"
        ></select-camera>
      </ui-label>
      <ui-label class="inline mb-sm mr-lg" label="检测结果" :width="70">
        <Select v-model="searchData.qualified" class="width-md" :clearable="true" placeholder="请选择检测结果">
          <Option v-for="(item, index) in cardSearchList" :value="item.dataKey" :key="index">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
    </div>
    <ui-label :width="0" class="inline mb-sm search-button" label="">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    modular: {
      default: 1,
    },
    cardSearchList: {
      type: Array,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        deviceIds: [],
        minLogTime: '',
        maxLogTime: '',
        qualified: '',
        synthesisResult: '',
        orgCode: '',
        // resultId: ''
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      treeData: [],
    };
  },
  methods: {
    currentChange(data) {
      this.searchData.orgCode = data.orgCode;
      // this.searchData.resultId = resultId
      this.$emit('params-change', this.searchData);
    },
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.searchData.maxLogTime = this.searchData.maxLogTime ? this.formateDate(this.searchData.maxLogTime) : null;
      this.searchData.minLogTime = this.searchData.minLogTime ? this.formateDate(this.searchData.minLogTime) : null;
      this.$emit('startSearch', this.searchData);
    },
    formateDate(chinaStandard) {
      var date = new Date(chinaStandard);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? '0' + d : d;
      var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      var minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      var s = date.getSeconds();
      let time = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + s;
      return time;
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        deviceIds: [],
        minLogTime: '',
        maxLogTime: '',
        qualified: '',
        orgCode: '',
        // resultId: ''
      };
      this.$emit('startSearch', this.searchData);
    },
    async getOrgTreeList(val) {
      if (!!val && val.regionCode) {
        await this.getRegioncode(val.regionCode);
      }
      // if (this.orgCodeList.length) {
      //   let {orgCode, resultId} = this.orgCodeList.find(item=> !item.disabled) || {}
      //   this.searchData.orgCode = orgCode || ''
      //   this.searchData.resultId = resultId || ''
      //   this.treeData = this.$util.common.arrayToJson(
      //     JSON.parse(JSON.stringify(this.orgCodeList)),
      //     'id',
      //     'parentId'
      //   )
      // }
    },
    // 获取组织机构数据
    async getRegioncode(code) {
      try {
        const params = { regioncode: code };
        const res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, { params });
        const orgCodeList = res.data.data;
        // let {orgCode} = orgCodeList.find(item => !item.disabled) || {}
        let { orgCode } = this.$util.common
          .jsonToArray(JSON.parse(JSON.stringify(orgCodeList)))
          .find((item) => !item.disabled);
        this.searchData.orgCode = orgCode || '';
        this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(orgCodeList)), 'id', 'parentId');
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: async function (val) {
        this.copySearchDataMx(this.searchData);
        await this.getOrgTreeList(val);
        this.startSearch();
      },
    },
  },
  components: {
    TreeSelect: require('./tree-select').default,
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.select-camera {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
}
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .search-button {
    white-space: nowrap;
  }
}
</style>
