<template>
  <div
    class="area-noresult"
    v-if="isShow"
    :style="topStyle"
    :class="{ 'area-noresult-small': size === 'small' }"
  >
    <div class="noresult-text">{{ mainText }}</div>
    <div
      v-if="subText && !isButton"
      class="noresult-text noresult-sub-text"
      @click="subTextFoo"
    >
      {{ subText }}
    </div>
    <Button
      class="noresult-button"
      type="primary"
      v-if="subText && isButton"
      @click="subTextFoo"
      >{{ subText }}</Button
    >
  </div>
</template>
<script>
export default {
  name: "noResultPlaceHolder",
  props: {
    isShow: { type: Boolean, default: true }, //是否显示 (可不传 在外层控制亦可)
    isButton: { type: Boolean, default: false }, //是否显示 (可不传 在外层控制亦可)
    offsetTop: { type: Number, default: 0 }, //上偏移量 注意[!!!上偏移量和下偏移量不能同时设置 二者最多设置一个]
    offsetBottom: { type: Number, default: 0 }, //下偏移量
    size: { type: String, default: "normal" }, //大小: normal || small
    mainText: { type: String, default: "" }, // 描述信息
    subText: { type: String, default: "" }, //副本文字 用于添加链接
    subTextFun: { type: Function }, //副本文字的点击事件
  },
  data: function () {
    return {
      styles: {},
    };
  },
  computed: {
    //设置位置 top或bottom值
    topStyle: function () {
      if (this.offsetTop) {
        return { top: this.offsetTop + "px" };
      }
      if (this.offsetBottom) {
        return { bottom: this.offsetBottom + "px" };
      }
    },
  },
  methods: {
    //小文本点击事件
    subTextFoo() {
      this.subTextFun && this.subTextFun();
      this.$emit("subTextFun");
    },
  },
};
</script>

<style lang="less">
.area-noresult {
  /*大小适配解析系统最小展示列表区域块大小*/
  position: relative;
  top: 0;
  width: 300px;
  height: 200px;
  margin: 0 auto;
  background: url("~@/assets/img/empty-page/null_main_icon.png") 50% 0 no-repeat;
	background-size: 40% auto;
  &.area-noresult-small {
    background: url("~@/assets/img/empty-page/null_main_icon.png") 50% 0 no-repeat;
    background-size: 40% auto;
  }
  .noresult-button {
    position: absolute;
    bottom: 0;
    left: 0;
    margin: 0 auto;
    right: 0;
    padding: 5px 15px;
  }
}
.noresult-text {
  position: absolute;
  bottom: 30px;
  width: 100%;
  line-heiht: 20px;
  color: #b1b5bf;
  font-size: 14px;
  text-align: center;
  &.noresult-sub-text {
    bottom: 5px;
    font-size: 12px;
    color: #2c86f8;
    cursor: pointer;
    &:hover {
      color: #2c86f8;
    }
  }
}
.area-noresult-small {
  .noresult-text {
    bottom: 65px;
    font-size: 14px;
    &.noresult-sub-text {
      bottom: 43px;
      color: #2c86f8;
    }
  }
}
</style>
