<template>
  <!-- 关键属性检测 -->
  <div class="zdr-data-detection" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-guanjianshuxingjiance">
      ZDR轨迹数据检测
      <template #filter>
        <TypeSelect @getName="getName" :dataList="typeList" defaultName="全部指标"></TypeSelect>
      </template>
    </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: dataList }">
      <draw-echarts
        v-if="dataList.length != 0"
        ref="drawEcharts"
        :echartOption="echartOption"
        class="charts"
        @echartClick="echartClick"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';

export default {
  name: 'zdr-data-detection',
  props: {
    batchIds: {
      type: Array,
      default: () => [],
    },
    indexModules: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '230px',
      },
      name: '全部指标',
      timer: false,
      top: '18%',
      echartsLoading: false,
      echartOption: {},
      dataList: [],
      dataColor: ['#FAE565', '#CA783F', '#4193FC', '#E1637B', '#CABDDD', '#0DD083'],
      dataObj: [], // 已处理数据格式
      nameArr: [], // x轴
      indexModuleType: 'zdr',
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      getHomeConfig: 'home/getHomeConfig',
    }),
    typeList() {
      let arr = [{ name: '全部指标' }];
      this.dataObj.map((val) => {
        arr.push({ name: val.key });
      });
      return arr;
    },
  },
  mounted() {
    this.top = window.innerWidth < 1852 ? '28%' : '18%';
    window.addEventListener('resize', this.resize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize);
  },
  methods: {
    echartClick(params) {
      let evaluationResultIndexList = this.dataList[params.dataIndex].evaluationResultIndexList;
      let one = evaluationResultIndexList[params.seriesIndex];
      if (this.name !== '全部指标') {
        one = evaluationResultIndexList.find((item) => item.indexName === params.data.selfObject.key);
      }
      this.$emit('makeCreateTabs', one);
    },
    // 监听屏宽变化
    resize() {
      this.top = window.innerWidth < 1852 ? '28%' : '18%';
      if (!this.timer) {
        this.timer = true;
        let that = this;
        setTimeout(function () {
          if (that.name !== '全部指标') {
            that.init();
          } else {
            that.dataLenged = {};
            that.initAll();
          }
          that.timer = false;
        }, 400);
      }
    },
    async queryDeviceCheckColumnReports() {
      try {
        this.echartsLoading = true;
        const params = {
          regionCode: this.getHomeConfig.regionCode,
          batchIds: this.batchIds,
          indexModule: this.indexModules[this.indexModuleType].value,
        };
        const {
          data: { data },
        } = await this.$http.post(home.queryHomePageResultIndex, params);
        if (!!data && data.length) {
          let filterData = data.filter(
            (item) => !!item.evaluationResultIndexList && item.evaluationResultIndexList.length,
          );
          if (filterData.length) {
            this.dataList = filterData.slice(0, 13).reverse();
            this.initAll();
          }
        }
        this.echartsLoading = false;
      } catch (e) {
        this.echartsLoading = false;
        console.log(e);
      }
    },
    getName(value) {
      this.name = value;
      value === '全部指标' ? this.initAll() : this.init();
    },
    fontSize(res) {
      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = clientWidth / 1920;
      return res * fontSize;
    },
    init() {
      var salvProMax = []; // 单个背景色最大值
      var data = {};
      this.dataObj.map((val) => {
        if (val.key === this.name) {
          data = val;
        }
      });
      const maxCount = data.val.map((val) => {
        salvProMax.push(100);
        return Math.abs(val);
      });
      const _this = this;
      const tooltip = {
        show: 'true',
        trigger: 'axis', //触发类型
        axisPointer: {
          //去掉移动的指示线
          type: 'none',
        },
        confine: true,
        padding: [8, 10], //内边距
        extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        formatter: function (params) {
          var result = `<div>${params[0].name}</div>`;
          params.forEach(function (item) {
            // 去除特殊样式series
            if (item.componentSubType !== 'pictorialBar' && item.seriesName !== '背景') {
              var dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
              var seriesName = `<span style="display:inline-block; margin-left:10px;">${_this.name}</span>`;
              var number = `<span style="display:inline-block;float: right;margin-left:10px;">${Math.abs(
                item.value,
              )}%</span>`;
              result += dotHtml + seriesName + number + '</br>';
            }
          });
          return result;
        },
      };
      var option = {
        tooltip: tooltip,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '12%',
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            color: '#fff',
            fontSize: this.fontSize(10),
            formatter: function (value) {
              return value + '%';
            },
          },
        },
        yAxis: {
          type: 'category',
          data: this.nameArr,
          axisLine: {
            lineStyle: {
              color: '#0375B4',
            },
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            color: '#e2e9ff',
            fontSize: this.fontSize(12),
          },
        },
        series: [
          {
            name: '人像档案置信率',
            type: 'bar',
            stack: '总量',
            barWidth: this.$util.common.fontSize(8),
            itemStyle: {
              color: data.color,
            },
            z: 10,
            data: this.getSymbolData(maxCount, data),
          },
          {
            name: 'XXX',
            type: 'pictorialBar',
            symbol:
              'image://data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADoAAAA6CAMAAADWZboaAAAAZlBMVEUAAABe3uVe3+Vf3uVf3+Zf3uVg3+Zg3+Zf3+Vi4OZh4OZg3+Z86/Bh3+Zi4Odj4Odi4OZ86/B76/B86/Bj4ed56+9x5+xn4umB7/N87PB36e+A7/N+7fF/7vJ/7vJ+7fGA7/OB7/PReX+lAAAAIXRSTlMABQkVDREmIhk3MR10LEFFPHh7cUprXE35h2XnqMLAp+mHAG9cAAAB5ElEQVRIx83WjU7CMBQFYIoiKMqU/XUboHv/l/Tce7t2XamDNSacETEmX86tlK2rx4py150o+MstMBLwWRfHKo6JCVxLnvmFGBjFQ58oF1//sUZhGy/ClSTWObgnL4O+bkeN4nY2okfNMbkRt9/vtxz8InoTsWplJSCzFxPmO8+GpSIByX3YQAuGDWtRKhKjCnxDXhF6Z4yxnZ20Wgko7BMRDmxtSGVaI4kdTIgb+zTYoJQlIMlDlmUFgrcDWWC201qSayqlTkiCddWWeV62VU0YlnpRi9VOKaSUsiyq/N0krwq2Ugt7lVpZl5BfHNiytjagMi+XYp0kCR45hMlivVQrE/uU5pXSrCB5bM6d1t2lOZItMqmliT3q5uVxqxzyW/ccfYLNKx7ZTeykMvNyac2yt2Fbc61MHLSC0rwoxbiNdlQ3GBm1NLHQsHUrtEXppR/ljNpW6DbSCoqlFiVoN6YdaFlgsSFVPs1BdT8OaB5QyQzVcaqWDows/zepxR8ObLglTrdtCRVuRNj4Rrxh+//0ke2f8KVL+Kon3GCSbmsJN9OUW3j6g0Ns+LgCij2u0h+Sghc8mlMPBMgdx5DFh59VmOVHrvmDnoNxCz3J7MFWsMuaLyR089xz/xhlfijvwutR8gv3zk6BLUUeCgAAAABJRU5ErkJggg==',
            symbolSize: [46, 46],
            symbolOffset: [20, 0],
            z: 10086,
            itemStyle: {
              color: '#0A74FF',
            },
            test: data,
            data: this.getSymbolData(maxCount, data),
          },
          // {
          //   name: '背景',
          //   type: 'bar',
          //   barWidth: this.$util.common.fontSize(8),
          //   barGap: '-100%',
          //   data: salvProMax,
          //   itemStyle: {
          //     color: this.dataColor[this.name],
          //     borderRadius: 30,
          //     opacity: 0.3,
          //   },
          //   z: 1,
          // },
        ],
      };
      this.echartOption = option;
    },
    initAll() {
      this.dataObj = []; // 多指标展示数据
      this.nameArr = []; // x轴城市
      let evaluationResultIndexList = 0; // 用于判断初次evaluationResultIndexList为空数组,evaluationResultIndexList === dataList.length代表暂无数据
      this.dataList.forEach((val) => {
        this.nameArr.push(val.regionName);
        if (val.evaluationResultIndexList.length === 0) {
          this.dataObj.map((vals) => {
            vals.val.push(0);
          });
          if (this.dataObj.length === 0) {
            evaluationResultIndexList += 1;
          }
        } else {
          val.evaluationResultIndexList.forEach((item, index) => {
            item.resultValue = item.resultValue || 0;
            if (item.indexName === '轨迹重复率') {
              // '-'是为了轨迹重复率能显示在图表分割线左侧
              item.resultValue = Number('-' + (Math.abs(item.resultValue) || 0));
            }
            if (!this.dataObj[item.indexName]) {
              this.dataObj[item.indexName] = [];
            }
            if (
              !this.dataObj
                .map((i) => {
                  return i.key;
                })
                .includes(item.indexName)
            ) {
              if (evaluationResultIndexList >= 1) {
                let arrays = { color: this.dataColor[index], val: [], key: item.indexName };
                for (let i = 0; i < evaluationResultIndexList; i++) {
                  arrays.val.push(0);
                }
                arrays.val.push(item.resultValue);
                this.dataObj.push(arrays);
              } else {
                this.dataObj.push({ color: this.dataColor[index], val: [item.resultValue], key: item.indexName });
              }
            } else {
              this.dataObj.map((i) => {
                if (i.key === item.indexName) {
                  i.val.push(item.resultValue);
                }
              });
            }
          });
        }
      });
      if (evaluationResultIndexList === this.dataList.length) {
        this.dataList = [];
      }
      const legend = {
        show: true,
        itemWidth: 10,
        itemHeight: 10,
        align: 'left',
        top: 10,
        left: 10,
        formatter: function (name) {
          return '{a|' + (name.length > 8 ? name.substr(0, 8) + '...' : name) + '}';
        },
        textStyle: {
          rich: {
            a: {
              width: 120,
              fontSize: this.fontSize(12),
              align: 'left',
              color: '#fff',
            },
          },
        },
        tooltip: {
          show: true,
        },
      };
      const tooltip = {
        show: 'true',
        trigger: 'axis', //触发类型
        axisPointer: {
          //去掉移动的指示线
          type: 'none',
        },
        confine: true,
        padding: [8, 10], //内边距
        extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        formatter: function (params) {
          var result = `<div>${params[0].name}</div>`;
          params.forEach(function (item) {
            // 去除特殊样式series
            if (item.componentSubType !== 'pictorialBar' && item.seriesName !== '背景') {
              var dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
              var seriesName = `<span style="display:inline-block; margin-left:10px;">${item.seriesName}</span>`;
              var number = `<span style="display:inline-block;float: right;margin-left:10px;">${Math.abs(
                item.value,
              )}%</span>`;
              result += dotHtml + seriesName + number + '</br>';
            }
          });
          return result;
        },
      };
      let service = [];
      this.dataObj.forEach((val) => {
        service.push({
          name: val.key,
          test: val,
          type: 'bar',
          stack: 'total',
          label: {
            show: false, //不显示item上面的数字
          },
          lineStyle: {
            width: 2,
            color: val.color, // 线条颜色
          },
          barWidth: this.$util.common.fontSize(10),
          itemStyle: {
            color: val.color,
            borderColor: '#ccc',
            borderWidth: 1,
          },
          data: val.val,
        });
      });
      const option = {
        legend,
        tooltip: tooltip,
        grid: {
          left: '3%',
          right: '4%',
          bottom: '0%',
          top: this.top,
          containLabel: true,
        },
        xAxis: {
          type: 'value',
          splitLine: {
            show: false,
          },
          axisLabel: {
            margin: 10,
            color: '#fff',
            fontSize: this.fontSize(12),
            formatter: function (value) {
              return Math.abs(value);
            },
          },
          axisLine: {
            lineStyle: {
              color: '#0375B4',
            },
          },
        },
        yAxis: {
          type: 'category',
          axisTick: { show: false },
          data: this.nameArr,
          axisLine: {
            lineStyle: {
              color: '#0375B4',
            },
          },
          axisLabel: {
            margin: 10,
            color: '#e2e9ff',
            fontSize: this.fontSize(12),
          },
          splitLine: {
            show: false,
          },
          minorSplitLine: {
            show: false,
          },
        },
        series: service,
      };
      this.echartOption = option;
    },
    getSymbolData(data, selfObject) {
      let arr = [];
      for (var i = 0; i < data.length; i++) {
        arr.push({
          value: data[i],
          symbolPosition: 'end',
          selfObject: selfObject,
        });
      }
      return arr;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    TypeSelect: require('./type-select').default,
  },
  watch: {
    batchIds: {
      handler(val) {
        if (!val.length || !this.getHomeConfig || !this.getHomeConfig.regionCode) return false;
        this.$nextTick(() => {
          this.queryDeviceCheckColumnReports();
        });
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.zdr-data-detection {
  position: absolute;
  width: 462px;
  background: rgba(0, 104, 183, 0.13);
  right: 10px;
  z-index: 11;
  height: 48%;

  .determinant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }

  .echarts-box {
    width: 100%;
    height: calc(100% - 32px) !important;

    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}

.full-screen-container {
  position: absolute;
  height: 45%;
  margin-left: 10px;
  right: 10px;
}
</style>
