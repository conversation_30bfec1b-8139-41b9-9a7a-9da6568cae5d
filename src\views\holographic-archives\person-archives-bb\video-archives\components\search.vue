<!--
    * @FileDescription: 人员档案 - 视频档案-搜索
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="search">
        <Form ref="form" :inline="true" :model="formData" :class="visible ? 'advanced-search-show' : ''">
            <div class="general-search">
                <FormItem label="" prop="keyword">
                    <Input placeholder="请输入身份证号/档案编号进行检索" clearable v-model="formData.keyword" maxlength="50" class="search-input-430">
                    </Input>
                </FormItem>
                <FormItem>
                    <div class="advanced-search-text" @click.stop="advancedSearchHandle($event)">
                        <img src="@/assets/img/down-circle-icon.png" alt />
                        <span class="primary">{{ visible ? '普通检索' : searchText === '高级检索' ? '高级检索' : '更多条件' }}</span>
                    </div>
                </FormItem>
            </div>
            <div class="advanced-search" @click="($event) => $event.stopPropagation()">
                <div class="upload-input-list">
                    <uiUploadImg ref="uploadImg" v-model="images" size="small" :algorithmType="1" @imgUrlChange="imgUrlChange" />
                </div>
                <div class="other-search">
                    <div class="other-search-top card-border-color none-color">
                        <FormItem label="置信来源:"  prop="source" clearable>
                            <Select v-model="formData.source" placeholder="请选择" transfer>
                                <Option v-for="(item, index) in algorithmVendors" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
                            </Select>
                        </FormItem>
                    </div>
                    <div class="other-search-bottom">
                        <div>
                        <FormItem label="相似度:" prop="similarity">
                            <div class="slider-content">
                            <Slider v-model="formData.similarity"></Slider>
                            <span>{{ formData.similarity }}%</span>
                            </div>
                        </FormItem>
                        </div>
                    </div>
                </div>
            </div>
        </Form>
        <slot name='statistics'>
            <div>
                <Button type="primary" @click="startSearch" class='mr-20'>查询</Button>
                <Button @click="resetHandle">重置</Button>
            </div>
        </slot>
    </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
import uiUploadImg from '@/components/ui-upload-img/index'
export default {
	components: {
		uiUploadImg,
	},
	props: {
		//检索类型
		searchText: {
			type: String,
			default: ''
		},
	},
	data() {
		return {
			visible: false,
			gender: '全部',
			images: ['', '', '', '', ''], //图片列表显示
			formData: {
				nations: [],
				idcardNo: '',
				name: '',
				isRealName: '',
				labelZh: '',
				startSnapTime: '',
				endSnapTime: '',
				similarity: 80,
				features: [], //图片入参链接
			},
			algorithmVendors: []
		}
	},
	computed: {
		...mapGetters({
			globalObj: 'systemParam/globalObj',
			genderList: 'dictionary/getGenderList', //性别
			nationList: 'dictionary/getNationList' //民族
		})
	},
	async created() {
		await this.getDictData();
		this.$nextTick(() => {
			if(typeof this.globalObj.searchForPicturesDefaultSimilarity === 'string') {
				this.formData.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity)
			}else{
				this.formData.similarity = this.globalObj.searchForPicturesDefaultSimilarity
			}
		})
		window.addEventListener("click", (e)=> {
			this.visible = false;
		})
	},
  	methods: {
		...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
		
		// 高级搜索切换
		advancedSearchHandle($event) {
			$event.stopPropagation()
			if (this.visible) {
				this.visible = false
			} else {
				this.visible = true
			}
		},
		// 查询
		startSearch() {
			let arr = []
			this.images.forEach(ele => {
				if (ele) {
				arr.push(ele.feature)
				}
			})
			this.formData.features = arr
			this.visible = false;
			this.$emit('searchForm', this.formData)
		},
		
		// 重置
		resetHandle() {
			
			this.formData.idcardNo = ''
			this.formData.deviceIds = []
			this.formData.startSnapTime = ""
			this.formData.endSnapTime = ""
			this.$refs.form.resetFields()
			this.images = ['', '', '', '', '']
			this.formData.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity)
			this.startSearch()
		},
		setData(data) {
			if (['video', 2].includes(this.dataType)) {}
			this.formData.idcardNo = ''
			this.formData.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity)
			if (data) {
				if (this.dataType == 'video') {
					this.formData.idcardNo = data.archiveNo
				}else {
					if(data.idcardNo) this.formData.idcardNo = data.idcardNo
				}
				// if(data.archiveNo) this.formData.archiveNo = data.archiveNo
				if(data.nations) this.formData.nations = data.nations;
				if(data.name) this.formData.name = data.name;
				if(data.similarity) this.formData.similarity = data.similarity*100;
				if(data.features) this.formData.features = data.features;
			}
			this.startSearch()
		},
		// 图片上传
		imgUrlChange(list) {
			this.images = list
		},
		//所属类型字典数据
        getDictDataPageList() {
            queryDataByKeyTypes(["iafg_algorithm_vendor"]).then(res => {
                this.algorithmVendors = res.data[0]['iafg_algorithm_vendor'];
            })
        },
  	}
}
</script>
<style lang="less" scoped>
.search {
  position: relative;
  .general-search {
    .advanced-search-text {
      height: 34px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
  .advanced-search {
    display: flex;
    align-items: center;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    margin-top: 1px;
    z-index: 10;
    max-height: 0px;
    transition: max-height 0.3s;
    overflow: hidden;
    .upload-input-list {
        padding: 10px 0;
        display: flex;
        /deep/.ivu-upload {
            margin-right: 10px;
        }
    }
    .other-search {
      display: flex;
      flex: 1;
      padding: 10px 0 0 10px;
      box-sizing: border-box;
      flex-direction: column;
      .other-search-top {
        display: flex;
        border-bottom: 1px dashed #fff;
        .perceive-data {
          width: 380px;
        }
      }
      .ivu-form-item {
        margin-bottom: 10px;
      }
      .other-search-bottom {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-top: 10px;
        box-sizing: border-box;
        align-items: flex-end;
        .slider-content {
          height: 34px;
        }

      }
    }
  }

  .advanced-search-show {
    .advanced-search {
      max-height: 400px;
      transition: max-height 0.5s;
    }
    .advanced-search-text {
      /deep/img {
        transform: rotate(0deg);
        transition: transform 0.2s;
      }
    }
  }
  /deep/.ivu-select-item-selected,
  .ivu-select-item-selected:hover {
    background-color: #fff !important;
    color: #2c86f8 !important;
  }
  .none-color{
    border-color: transparent !important;
  }
}
</style>
