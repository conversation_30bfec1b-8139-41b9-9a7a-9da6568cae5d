<template>
  <basic-select v-bind="getAttrs" v-on="$listeners">
    <!-- 表格插槽 -->
    <template #outcome="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
    </template>
    <template #detectionMode="{ row }">
      {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
    </template>
    <template #clockSkew="{ row }">
      <span>{{ row.clockSkew || 0 }} s</span>
    </template>
    <template #deviceId="{ row }">
      <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
        row.deviceId
      }}</span>
    </template>
    <template #videoStartTime="{ row }">
      <span>
        {{ !row.videoStartTime ? '--' : row.videoStartTime }}
      </span>
    </template>
    <template #startTime="{ row }">
      <span>
        {{ !row.startTime ? '--' : row.startTime }}
      </span>
    </template>
    <template #ntpTime="{ row }">
      <span>
        {{ row.ntpTime ? row.ntpTime : '--' }}
      </span>
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatus ? '--' : row.phyStatusText }}
      </span>
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
  </basic-select>
</template>
<script>
import errorCodesMixins from './errorCodesMixins';
export default {
  props: {
    qualifiedColorConfig: {},
  },
  mixins: [errorCodesMixins],
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        clockSkewMin: null,
        clockSkewMax: null,
      },
    };
  },
  created() {},
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
        formItemData: this.formItemData, // errorCodesMixins.js
        moduleData: this.moduleData, // errorCodesMixins.js
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
