<template>
  <div>
    <swiper
      ref="mySwiper"
      :options="swiperOption"
      class="my-swiper"
      @slideChange="slideChange"
    >
      <swiper-slide
        v-for="(item, index) in personInfoNew"
        :key="index"
        class="swiper-no-swiping"
      >
        <ui-card :title="title" class="owner-information">
          <div
            slot="extra"
            class="more-btn primary"
            @click="archivesInfoHandle(item.archiveNo)"
          >
            查看档案信息
          </div>
          <div class="owner-information-content card-border-color mb-10">
            <div class="owner-item">
              <div class="owner-label card-bg title-color card-border-color">
                车主姓名
              </div>
              <div class="owner-value text-color card-border-color">
                {{ item.xm }}
              </div>
            </div>
            <div class="owner-item">
              <div class="owner-label card-bg title-color card-border-color">
                车主身份证号码
              </div>
              <div class="owner-value text-color card-border-color">
                {{ item.archiveNo }}
              </div>
            </div>
            <div class="owner-item">
              <div class="owner-label card-bg title-color card-border-color">
                车主联系方式
              </div>
              <!--  如果ownPhone2是空，就展示ownPhone      -->
              <div class="owner-value text-color card-border-color">
                {{ item.ownPhone2 || item.lxdh }}
              </div>
            </div>
            <!-- <div class="owner-item owner-border-bottom">
            <div class="owner-label card-bg title-color card-border-color">车主视频身份</div>
            <div class="owner-value text-color card-border-color">V2367812641289749120</div>
          </div> -->
            <div class="owner-item owner-border-bottom">
              <div class="owner-label card-bg title-color card-border-color">
                车主户籍地址
              </div>
              <div class="owner-value address text-color card-border-color">
                {{ item.hjdzDzmc }}
              </div>
            </div>
          </div>
        </ui-card>
      </swiper-slide>

      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>
    <div class="card-box" v-if="personInfoNew.length == 0">
      <ui-card :title="title">
        <ui-empty></ui-empty>
      </ui-card>
    </div>
  </div>
</template>
<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { mapGetters, mapMutations } from "vuex";

export default {
  components: {
    swiper,
    swiperSlide,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    // 车主信息
    vehicleInfo: {
      type: Array | Object,
      default: () => [],
    },
    personInfo: {
      type: Array | Object,
      default: () => [],
    },
  },
  computed: {
    ...mapGetters({
      baseInfoIndex: "archive/getBaseInfoIndex",
    }),
  },
  data() {
    return {
      swiperOption: {
        effect: "fade",
        noSwiping: true,
        direction: "horizontal",
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      },
      personInfoNew: [],
    };
  },
  watch: {
    personInfo: {
      handler(val) {
        if (!val) {
          this.personInfoNew = [];
        } else {
          if (val.length > 0) {
            this.personInfoNew = val;
          } else {
            this.personInfoNew.push(val);
          }
        }
      },
      //   immediate: true,
      deep: true,
    },
    baseInfoIndex(val) {
      this.$refs.mySwiper.swiper.slideTo(val, 500, false);
    },
  },
  methods: {
    ...mapMutations({
      setBaseInfoIndex: "archive/setBaseInfoIndex",
    }),
    slideChange() {
      this.setBaseInfoIndex(this.$refs.mySwiper.swiper.activeIndex);
    },
    archivesInfoHandle(idcardNo) {
      const { href } = this.$router.resolve({
        name: "people-archive",
        query: {
          archiveNo: idcardNo,
          source: "people",
          initialArchiveNo: idcardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.card-box {
  width: 100%;
  height: 190px;
  position: relative;
  /deep/ .card-content {
    height: calc(~"100% - 50px");
  }
}
.owner-information {
  /deep/ .card-head {
    padding-right: 20px;
  }

  .owner-information-content {
    display: flex;
    flex-wrap: wrap;
    border-left: 1px solid #fff;

    .owner-item {
      display: flex;

      .owner-label {
        width: 170px;
        padding: 9px 10px;
        box-sizing: border-box;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        font-family: "MicrosoftYaHei-Bold";
        border-right: 1px solid #fff;
        border-top: 1px solid #fff;
      }

      .owner-value {
        width: 290px;
        padding: 9px 10px;
        box-sizing: border-box;
        font-size: 14px;
        line-height: 20px;
        border-right: 1px solid #fff;
        border-top: 1px solid #fff;
      }

      .address {
        flex: 1;
      }
    }

    .owner-border-bottom {
      width: 100%;

      .owner-label,
      .owner-value {
        border-bottom: 1px solid #fff;
      }
    }
  }
}
</style>
