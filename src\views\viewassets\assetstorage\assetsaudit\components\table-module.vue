<template>
  <div class="table-module auto-fill">
    <div class="btn-list over-flow mb-sm">
      <div class="fl">
        <Checkbox v-if="active === '0'" v-model="checkAll" @on-change="handleAllCheck"> 全选 </Checkbox>
        <span class="base-text-color" v-if="active === '0'">
          <span class="ml-lg">已选中</span>
          <span class="check-num">
            {{ (checkAll ? pageData.totalCount : chooseTableData.length) | formatNum }}
          </span>
          <span>条</span>
        </span>
        <span class="ml-lg mr-lg line" v-if="active === '0'"></span>
        <RadioGroup v-model="sign" @on-change="changeSign">
          <Radio label="1">标记差异字段</Radio>
          <Radio class="ml-lg" label="2">标记不合格字段</Radio>
        </RadioGroup>
      </div>
      <div class="fr" v-if="active === '0'">
        <Button type="primary" @click="handleBatchAudit">
          <i class="icon-font icon-shenhe mr-xs font-white"></i>
          <span>批量审核</span>
        </Button>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      reserveSelection
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      :is-all="checkAll"
      :defaultStoreData="defaultStoreData"
      @storeSelectList="storeSelectList"
    >
      <template #deviceId="{ row }">
        <span :class="{ 'check-num': checkField(row, 'deviceId111') }">{{ row.deviceId }}</span>
        <span
          v-if="row.sign !== '0'"
          :class="['ml-xs', 'sign', row.sign === '1' ? 'new' : row.sign === '2' ? 'same' : 'diff']"
          >{{ row.sign === '1' ? '新' : row.sign === '2' ? '同' : '异' }}</span
        >
      </template>
      <template #deviceName="{ row }">
        <Tooltip :content="row.deviceName" placement="top">
          <div :class="{ 'check-num': checkField(row, 'deviceName') }" class="width-sm inline ellipsis">
            {{ row.deviceName }}
          </div>
        </Tooltip>
      </template>
      <template #orgName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'orgName') }">{{ row.orgName }}</span>
      </template>
      <template #civilName="{ row }">
        <span :class="{ 'check-num': checkField(row, 'civilName') }">{{ row.civilName }}</span>
      </template>
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'longitude') }">{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span :class="{ 'check-num': checkField(row, 'latitude') }">{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatus="{ row }">
        <span class="check-status-font" :class="row.phyStatus === '1' ? 'font-success' : 'font-failed'">
          {{ row.phyStatus | filterType(phystatusList) }}
        </span>
      </template>
      <template #ipAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'ipAddr') }">{{ row.ipAddr }}</span>
      </template>
      <template #macAddr="{ row }">
        <span :class="{ 'check-num': checkField(row, 'macAddr') }">{{ row.macAddr }}</span>
      </template>
      <template #sbgnlxText="{ row }">
        <Tooltip :content="row.sbgnlxText" placement="top">
          <div :class="{ 'check-num': checkField(row, 'sbgnlx') }" class="width-xs inline ellipsis">
            {{ row.sbgnlxText }}
          </div>
        </Tooltip>
      </template>
      <template #sbdwlxText="{ row }">
        <span :class="{ 'check-num': checkField(row, 'sbdwlx') }">{{ row.sbdwlxText }}</span>
      </template>
      <template #sbcjqy="{ row }">
        <Tooltip :content="row.sbcjqyText" placement="top">
          <div :class="{ 'check-num': checkField(row, 'sbcjqy') }" class="width-xs inline ellipsis">
            {{ row.sbcjqyText }}
          </div>
        </Tooltip>
      </template>
      <template #checkStatusText="{ row }">
        <div>
          <span
            class="check-status ml-xs mr-xs"
            :class="row.checkStatus === '1' ? 'bg-success' : row.checkStatus === '2' ? 'bg-failed' : ''"
          >
            {{ row.checkStatusText }}
          </span>
        </div>
      </template>
      <template #examineStatusText="{ row }">
        <div>
          <span
            class="check-status-font"
            :class="row.examineStatus === '1' ? 'font-success' : row.examineStatus === '2' ? 'font-failed' : ''"
          >
            {{ row.examineStatusText }}
          </span>
        </div>
      </template>
      <template #action="{ row }">
        <div class="action">
          <ui-btn-tip
            v-if="row.examineStatus === '0'"
            class="mr-sm"
            icon="icon-shenhe"
            content="审核"
            :styles="{ color: 'var(--color-success)', 'font-size': '14px' }"
            @handleClick="audit(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.examineStatus !== '0'"
            class="mr-sm"
            icon="icon-chakanxiangqing"
            content="查看"
            :styles="{ color: 'var(--color-success)', 'font-size': '14px' }"
            @handleClick="handleView(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.examineStatus === '2' && row.checkStatus === '2' && row.sign !== '2'"
            class="mr-sm"
            icon="icon-chayixiangqing"
            content="差异详情"
            :styles="{ color: 'var(--color-table-btn-default)', 'font-size': '14px' }"
            :disabled="row.contrastStatus !== '2'"
            @handleClick="differenceDetails(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.examineStatus !== '1'"
            icon="icon-shenhejilu"
            content="审核记录"
            :styles="{ color: 'var(--color-warning)', 'font-size': '14px' }"
            @handleClick="auditRecords(row)"
          ></ui-btn-tip>
        </div>
      </template>
    </ui-table>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    active: {
      type: String,
      default: '0',
    },
    searchParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      sign: '1',
      loading: false,
      columns: [
        { title: '序号', type: 'index', align: 'center', width: 50 },
        { title: '设备编码', slot: 'deviceId', align: 'left', tooltip: true, minWidth: 200 },
        { title: '设备名称', slot: 'deviceName', align: 'left', tooltip: true, minWidth: 200 },
        { title: '组织机构', slot: 'orgName', align: 'left', tooltip: true, minWidth: 200 },
        { title: '行政区划', slot: 'civilName', align: 'left', tooltip: true, minWidth: 200 },
        { title: '经度', slot: 'longitude', align: 'left', tooltip: true, minWidth: 110 },
        { title: '纬度', slot: 'latitude', align: 'left', tooltip: true, minWidth: 110 },
        { title: 'IP地址', slot: 'ipAddr', align: 'left', tooltip: true, minWidth: 110 },
        { title: 'MAC地址', slot: 'macAddr', align: 'left', tooltip: true, minWidth: 150 },
        { title: '摄像机功能类型', slot: 'sbgnlxText', align: 'left', tooltip: true, minWidth: 130 },
        { title: '监控点位类型', slot: 'sbdwlxText', align: 'left', tooltip: true, minWidth: 130 },
        { title: '摄像机采集区域', slot: 'sbcjqy', align: 'left', tooltip: true, minWidth: 130 },
        { title: '设备状态', slot: 'phyStatus', align: 'left', tooltip: true, minWidth: 80 },
        { title: '填报人', key: 'modifier', align: 'left', tooltip: true, minWidth: 200 },
        { title: '填报时间', key: 'modifyTime', align: 'left', tooltip: true, minWidth: 180 },
        { title: '审核人', key: 'examineUser', align: 'left', tooltip: true, minWidth: 200 },
        { title: '审核时间', key: 'examineTime', align: 'left', tooltip: true, minWidth: 180 },
        { title: '入库标识', key: 'putMark', align: 'left', tooltip: true, minWidth: 200 },
        { title: '入库检测状态', slot: 'checkStatusText', align: 'left', tooltip: true, minWidth: 100, fixed: 'right' },
        { title: '审核状态', slot: 'examineStatusText', align: 'left', tooltip: true, minWidth: 80, fixed: 'right' },
        {
          title: '操作',
          slot: 'action',
          align: 'left',
          tooltip: true,
          minWidth: 135,
          fixed: 'right',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
        },
      ],
      tableColumns: [],
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      checkAll: false,
      chooseTableData: [],
      defaultStoreData: [],
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    init() {
      if (!this.phystatusList.length) this.getAlldicData();
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    async getTableList() {
      try {
        this.loading = true;
        let params = Object.assign(
          {
            checkOrDiffer: this.sign,
            pageNumber: this.pageData.pageNum,
            pageSize: this.pageData.pageSize,
          },
          this.searchParams,
        );
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.fillList, params);
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
        this.$emit('getTotalNum', data.total);
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    handlePage(val) {
      this.pageData.pageNumber = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    storeSelectList(selection) {
      this.chooseTableData = selection;
      this.$emit('updateChooseData', selection);
    },
    // 全选
    handleAllCheck() {
      this.chooseTableData = [];
      if (!this.checkAll) {
        this.defaultStoreData = [];
      }
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.checkAll);
        this.$set(item, '_disabled', this.checkAll);
        return item;
      });
      this.$emit('changeCheckAll', this.checkAll);
    },
    checkField(row, field) {
      if (this.sign === '1') {
        if (!row.differList) return;
        return row.differList.includes(field);
      } else {
        if (!row.accuracyList) return;
        return row.accuracyList.includes(field);
      }
    },
    changeSign() {
      this.init();
    },
    audit(row) {
      this.$emit('audit', row);
    },
    abnormal(row) {
      this.$emit('abnormal', row);
    },
    auditRecords(row) {
      this.$emit('auditRecords', row);
    },
    handleView(row) {
      this.$emit('handleView', row);
    },
    differenceDetails(row) {
      this.$emit('differenceDetails', row);
    },
    handleBatchAudit() {
      if (!this.chooseTableData.length && !this.checkAll) {
        return this.$Message.error('请选择设备');
      }
      this.$emit('handleBatchAudit', this.chooseTableData);
    },
  },
  computed: {
    ...mapGetters({
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  watch: {
    active: {
      handler(val) {
        this.tableColumns = this.$util.common.deepCopy(this.columns);
        if (val === '0') {
          this.tableColumns.unshift({ type: 'selection', align: 'center', width: 50 });
          this.tableColumns[this.tableColumns.length - 1].minWidth = 100;
        } else {
          this.tableColumns[this.tableColumns.length - 1].minWidth = 135;
        }
      },
      immediate: true,
    },
    searchParams: {
      handler() {
        this.defaultStoreData = [];
        this.init();
      },
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.table-module {
  .btn-list {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 0 20px;
  }
  .check-num {
    color: var(--color-failed);
  }
  .line {
    height: 18px;
    width: 2px;
    display: inline-block;
    background-color: var(--devider-line);
    vertical-align: middle;
  }
  .sign {
    display: inline-block;
    width: 18px;
    height: 18px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    border-radius: 50%;

    &.new {
      color: var(--color-bluish-green-text);
      border: 1px solid var(--color-bluish-green-text);
      background-color: var(--bg-bluish-green-text);
    }
    &.diff {
      color: #e85128;
      border: 1px solid #e85128;
      background-color: rgba(232, 81, 40, 0.2);
    }
    &.same {
      color: #21d83a;
      border: 1px solid #21d83a;
      background-color: rgba(33, 216, 58, 0.2);
    }
  }
  .ui-table {
    margin: 0 20px;
  }
}
</style>
