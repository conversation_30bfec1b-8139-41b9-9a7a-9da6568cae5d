<template>
  <div class="container"></div>
</template>

<script>
export default {
  name: 'bottom-star',
  props: {},
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>

<style lang="less" scoped>
.container {
  display: inline-block;
  background: url('~@/assets/img/navigation-page/bottomstar.png') no-repeat;
  background-size: cover;
  animation: flash 2s infinite linear;
  width: 71px;
  height: 11px;
}

@keyframes flash {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.4;
  }
  100% {
    opacity: 0;
  }
}
</style>
