
/*
    margin padding fontSize 自定义1到100px
*/
.loopStyle(@counter) when (@counter > -1) {
  .pd@{counter} {
    padding: (1px * @counter);
  }
  .pt@{counter} {
    padding-top: (1px * @counter);
  }
  .pr@{counter} {
    padding-right: (1px * @counter);
  }
  .pb@{counter} {
    padding-bottom: (1px * @counter);
  }
  .pl@{counter} {
    padding-left: (1px * @counter);
  }
  .mg@{counter} {
    margin: (1px * @counter);
  }
  .mt@{counter} {
    margin-top: (1px * @counter);
  }
  .mr@{counter} {
    margin-right: (1px * @counter);
  }
  .mb@{counter} {
    margin-bottom: (1px * @counter);
  }
  .ml@{counter} {
    margin-left: (1px * @counter);
  }
  .fs@{counter} {
    font-size: (1px * @counter);
  }
  .loopStyle((@counter - 1));
}
.fw{
  font-weight: bold;
}
.loopStyle(36);