<template>
  <ui-modal v-model="visible" class="add-list" :title="modalTitle" :styles="styles">
    <div class="copy">
      <ui-btn-tip class="mr-sm" icon="icon-fuzhi" content="复制" @handleClick="copy"></ui-btn-tip>
      <ui-btn-tip v-show="copyData" icon="icon-paste-full" content="粘贴" @handleClick="paste"></ui-btn-tip>
    </div>
    <div class="tab-content">
      <slot name="headerbtn"></slot>
      <ui-switch-tab class="ui-switch-tab" v-model="state" :tab-list="stateOptions" @beforeChangeTab="nextStep">
      </ui-switch-tab>
      <keep-alive>
        <basics-property
          ref="property"
          v-if="state === 0"
          :choosed-org="choosedOrg"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :all-dic-data="allDicData"
          :selected-list="selectedList"
          :has-multiView="hasMultiView"
          :is-required="isRequired"
          @putData="putData"
          @selectCamera="selectCamera"
        >
        </basics-property>
        <state-property
          ref="property"
          v-if="state === 1"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :all-dic-data="allDicData"
          :is-required="isRequired"
          @putData="putData"
        >
        </state-property>
        <administration-property
          ref="property"
          v-if="state === 2"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :choosed-org="choosedOrg"
          :all-dic-data="allDicData"
          :is-required="isRequired"
          @putData="putData"
        >
        </administration-property>
        <label-property
          ref="property"
          v-if="state === 3"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :all-dic-data="allDicData"
          :is-required="isRequired"
          @putData="putData"
        ></label-property>
        <safety-property
          ref="property"
          v-if="state === 4"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :all-dic-data="allDicData"
          :is-required="isRequired"
          @putData="putData"
        >
        </safety-property>
        <photo-gallery
          ref="property"
          v-if="state === 5"
          :default-form="defaultForm"
          :error-data="defaultError"
          :modal-action="modalAction"
          :is-required="isRequired"
          @putData="putData"
        >
        </photo-gallery>
      </keep-alive>
    </div>
    <template #footer>
      <Button v-if="!isView" @click="visible = false" class="plr-30"> 取 消</Button>
      <span class="ml-sm" v-if="!isView">
        <Button type="primary" @click="nextStep()" class="plr-30"> 下一步 </Button>
        <Button type="primary" :loading="submitLoading" @click="submitDeviceInfo" class="plr-30"> 确 定 </Button>
      </span>
    </template>
    <choose-device
      ref="ChooseDevice"
      node-key="deviceId"
      :table-columns="tableColumns"
      :load-data="leftData"
      :search-conditions="searchConditions"
      :selected-list="selectedList"
      :need-handle="false"
      @getDeviceIdList="getDeviceIdList"
      @getOrgCode="getOrgCode"
    >
      <template #search-header>
        <search-list ref="SearchList" :dict-data="dictData" @startSearch="startSearch"></search-list>
      </template>
      <template #checkStatusText="{ row }"
        ><span class="statustag" :style="{ 'background-color': checktSatusBgcolor[row.checkStatus] }">{{
          row.checkStatusText
        }}</span>
      </template>
    </choose-device>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import common from '@/config/api/common';
import taganalysis from '@/config/api/taganalysis';
import user from '@/config/api/user';

export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    choosedOrg: {
      type: Object,
    },
    modalTitle: {
      type: String,
      default: '新增入库数据',
    },
    modalAction: {
      type: String,
    },
    viewDeviceId: {
      type: Number,
    },
    // 错误字段
    unqualified: {
      default: null,
    },
    viewUrl: {
      type: String,
      default: null,
    },
    // 请求后端url
    saveUrl: {
      type: String,
      default: null,
    },
    saveMethod: {
      type: String,
      default: 'put',
    },
    // 入库前需要检测
    checkFun: {
      type: Function,
    },
    // 入库检测结果，资产填报需要提交给后端，后端不用再次调用接口
    checkData: {
      type: Object,
      default: null,
    },
    deviceCode: {
      type: String,
      default: null,
    },
    // 是否包含多目
    hasMultiView: {
      type: Boolean,
      default: true,
    },
    // 工单编辑保存设备需要传入自定义参数
    customParameters: {
      type: Object,
      default: () => {},
    },
    // 是否需要调用验证字段合不合格接口
    hasCheckResult: {
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      submitLoading: false,
      styles: {
        width: '5rem',
      },
      state: 0,
      stateOptions: [
        {
          label: '基础属性',
          value: 0,
        },
        {
          label: '状态属性',
          value: 1,
        },
        {
          label: '管理属性',
          value: 2,
        },
        {
          label: '标签属性',
          value: 3,
        },
        {
          label: '安全属性',
          value: 4,
        },
        {
          label: '现场图片',
          value: 5,
        },
      ],
      sourceForm: {}, //从后端获取的原始数据用来判断是否更改过此数据
      deviceData: {},
      defaultForm: {},
      defaultError: {},
      allDicData: {},
      dicDataEnum: Object.freeze({
        propertySearch_roomtype: 'locationList',
        propertySearch_capdirection: 'carDirectionList',
        propertySearch_directiontype: 'deviceDirectionList',
        propertySearch_sbdwlx: 'cameraPointTypeList',
        propertySearch_positiontype: 'positionTypeList',
        propertySearch_usetype: 'useTypeList',
        // sxjgnlx_receive: 'sbgnlxTypeList',
        sxjgnlx_receive: 'sbgnlxTypeList',
        propertySearch_supplylighttype: 'supplyLightTypeList',
        propertySearch_videocoding_m: 'videocodingMListTypeList',
        propertySearch_videocoding_s: 'videocodingMListTypeList',
        propertySearch_networktype: 'networkTypeList',
        propertySearch_parental: 'whetherList',
        propertySearch_ptztype: 'ptzTypeList',
        manufacturer: 'manufacturerList',
        propertySearch_jtlx: 'jtlxList',
        propertySearch_gdlx: 'gdlxList',
        propertySearch_ljxy: 'ljxyList',
        propertySearch_qdfs: 'qdfsList',
        propertySearch_has: 'hasList',
        propertySearch_is: 'isList',
        sq_nlj: 'nljList',
        propertySearch_sbgnlxExt: 'sbgnlxExtList',
        // 状态属性
        propertySearch_phystatus: 'phyList',
        propertySearch_sblwzt: 'sblwztList',
        propertySearch_isonline: 'onlineList',
        propertySearch_datastatus: 'dataStatusList',
        propertySearch_ptzstatus: 'ptzStatusList',
        // 管理属性
        propertySearch_ssbm: 'departmentList',
        propertySearch_downloadspeed: 'downloadSpeedList',
        propertySearch_usageStatus: 'usageStatusList',
        // 标签属性
        propertySearch_sxjwzlx: 'positionList',
        propertySearch_sxjdypc: 'frequencyList',
        // 安全属性
        propertySearch_isshare: 'shareList',
        propertySearch_registerway: 'registerList',
        propertySearch_certifiable: 'certifiableList',
        propertySearch_svcspacesupportmode: 'svcSpaceSupportModeList',
        propertySearch_safetyway: 'safetyList',
        propertySearch_secrecy: 'secrecyList',
        propertySearch_svctimesupportmode: 'svcTimeSupportModeList',
        propertySearch_sourceId: 'sourceList',
        propertySearch_phyStatusExt: 'phyStatusExtList',
      }),
      dictData: {},
      // 判断字段是否必填
      mandatoryfieldList: [],
      // 获取后端所有设备字段对应中文
      allPropertyList: [],
      searchConditions: {},
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbdwlx}`,
          key: 'sbdwlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          width: 180,
          tooltip: true,
        },
        {
          title: '检测状态',
          slot: 'checkStatusText',
          align: 'left',
          width: 90,
          minWidth: 200,
          tooltip: true,
        },
      ],
      leftData: (parameter) => {
        return this.$http
          .post(
            equipmentassets.queryDeviceInfoPageList,
            Object.assign(this.searchConditions, parameter, {
              queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
            }),
          )
          .then((res) => {
            if (res.data.data.entities.length) {
              res.data.data.entities.forEach((item) => {
                switch (item.checkStatus) {
                  case '1000':
                    item.checkStatus = '0';
                    item.checkStatusText = '待检测';
                    break;
                  case '0000':
                    item.checkStatus = '1';
                    item.checkStatusText = '合格';
                    break;
                  default:
                    item.checkStatus = '2';
                    item.checkStatusText = '不合格';
                    break;
                }
              });
            }
            return res.data;
          });
      },
      checktSatusBgcolor: ['var(--color-warning)', 'var(--color-success)', 'var(--color-warning)'],
      selectedList: [],
      // 拷贝数据
      copyData: null,
    };
  },
  mounted() {
    this.dictTypeListGroupByType();
    this.getAssetFieldCheck();
    this.getPropertyList();
  },
  methods: {
    // 获取必填选项配置
    async getAssetFieldCheck() {
      try {
        const res = await this.$http.get(common.getAssetFieldCheck);
        this.mandatoryfieldList = res.data.data.paramValue.split(',');
      } catch (err) {
        console.log(err);
      }
    },
    // 获取所有设备字段
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 校验字段是否必填
    isRequired(filed) {
      if (filed) {
        return this.mandatoryfieldList.includes(filed);
      } else {
        console.error('请填入必填字段');
      }
    },
    async submitDeviceInfo() {
      try {
        // 提交时触发图片库的下一步
        if (this.state === 5) {
          this.nextStep();
        } else {
          this.$refs.property.validate();
        }
        // 提交时先验证表单信息
        if (!(await this.validate())) return;
        this.deviceData.sbjsTime = this.$util.common.formatDate(this.deviceData.sbjsTime);
        this.deviceData.sbyxnx = this.$util.common.formatDate(this.deviceData.sbyxnx);
        this.deviceData.monitorTime = this.$util.common.formatDate(this.deviceData.monitorTime);
        this.deviceData.createTime = this.$util.common.formatDate(this.deviceData.createTime);
        this.deviceData.certEndTime = this.$util.common.formatDate(this.deviceData.certEndTime);
        this.deviceData.azsj = this.$util.common.formatDate(this.deviceData.azsj);
        this.deviceData.qytime = this.$util.common.formatDate(this.deviceData.qytime);
        this.deviceData.yytime = this.$util.common.formatDate(this.deviceData.yytime);
        this.deviceData.bftime = this.$util.common.formatDate(this.deviceData.bftime);
        /**
         * 判断编辑的设备的字段是否发生变化
         * 没有变化则不提交
         * 发生变化，需要把变化的字段提交给后端，因为他们做不了字段比对 by 莫磊
         *  */
        const differenceList = this.$util.common.isObjectChanged(this.sourceForm, this.deviceData);
        if (differenceList.length) {
          let url = null,
            method = null,
            params = {};
          if (this.saveUrl) {
            url = this.saveUrl;
            method = this.saveMethod;
            params = this.customParameters || {};
          } else {
            url = this.isAdd ? equipmentassets.addDevice : equipmentassets.updateDeviceById;
            method = this.isAdd ? 'post' : 'put';
          }

          // 编辑前后设备字段发生变化，需要把变化的字段和之前变化的字段拼接去重提交给后端
          if (this.sourceForm.updateField) {
            params.updateField = Array.from(
              new Set([...differenceList, ...this.sourceForm.updateField.split(',')]),
            ).join(',');
          } else {
            params.updateField = differenceList.join(',');
          }

          this.submitLoading = true;
          //检测后是否继续入库
          if (this.checkFun) {
            // 当查看为设备资产接口保存又不是设备资产接口时
            if (!this.viewUrl && this.saveUrl && !this.isAdd) {
              Object.assign(params, {
                id: null,
                deviceInfoId: this.deviceData.id,
              });
            }
            // 检测结果
            const isContinue = await this.checkFun(this.deviceData);
            if (!isContinue) {
              this.submitLoading = false;
              return;
            }
            params.accuracyVo = this.checkData;
          }
          let res = await this.$http[method](url, Object.assign({}, this.deviceData, params));
          this.$Message.success(res.data.msg);
          this.state = 0;
          this.$emit('update');
        } else {
          console.log('没有更改任何值，不需要触发保存接口');
        }
        this.visible = false;
      } catch (err) {
        console.log(err);
      } finally {
        this.submitLoading = false;
      }
    },
    validate() {
      // if (!this.deviceData.sourceId) {
      //     this.$Message.error("请选择数据来源");
      //     return false;
      // }
      /**
       * 判断哪些值为空，作出提示
       */
      let emptyProperty = [];
      this.mandatoryfieldList.forEach((filed) => {
        !this.deviceData[filed] && emptyProperty.push(filed);
      });
      // 从后端的所有字段中找出必填字段对应的中文
      emptyProperty.forEach((filed, index) => {
        const property = this.allPropertyList.find((row) => {
          return row.propertyName === filed;
        });
        if (property) {
          emptyProperty[index] = property.propertyColumn;
        } else {
          console.warn(`未在后端字段中找到${filed}对应的中文`);
        }
      });
      console.log(emptyProperty, 'emptyProperty');
      if (emptyProperty.length === 0) {
        return true;
      } else {
        let wariningText = emptyProperty.join('、');
        return this.$UiConfirm({
          content: `${wariningText}未填写，是否继续保存?`,
          title: '警告',
        })
          .then(() => {
            return true;
          })
          .catch(() => {
            return false;
          });
      }
    },
    async nextStep(step) {
      try {
        let validate = await this.$refs.property.validate();
        if (this.state < this.stateOptions.length - 1 && validate && step === undefined) {
          this.state++;
        }
      } catch (err) {
        console.log(err);
      }
    },
    putData(formData) {
      Object.keys(formData).forEach((key) => {
        formData[key] === undefined && (formData[key] = '');
      });
      Object.assign(this.deviceData, formData);
    },
    async getDeviceById() {
      try {
        let url = null;
        if (this.viewUrl) {
          url = this.viewUrl;
        } else {
          url = equipmentassets.getDeviceById;
        }
        let { data } = await this.$http.get(url, {
          params: { id: this.viewDeviceId, deviceId: this.deviceCode },
        });
        this.defaultForm = data.data;
        this.selectedList = data.data.multiViewChildrenList || [];
        this.deviceData = this.$util.common.deepCopy(data.data);
        this.sourceForm = this.$util.common.deepCopy(data.data);
        if (this.hasCheckResult || !this.viewUrl) {
          this.queryDeviceCheckResultList();
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取设备检测错误字段的错误信息
    async queryDeviceCheckResultList() {
      try {
        let params = {
          deviceCode: this.deviceCode,
          // queryAll: true,
          page: false,
        };
        let res = await this.$http.post(equipmentassets.queryUnqualifiedList, params);
        res.data.data.forEach((row) => {
          let bool = this.defaultError.hasOwnProperty(row.checkColumnName);
          let errorMessage = null;
          // 错误信息中如果已经有该字段则拼接错误原因，如果没有则创建该字段错误原因
          if (!bool) {
            errorMessage = row.errorMessage;
            this.$set(this.defaultError, row.checkColumnName, row.errorMessage);
          } else {
            errorMessage = this.defaultError[row.checkColumnName] + ';' + row.errorMessage;
          }
          this.$set(this.defaultError, row.checkColumnName, errorMessage);
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 点击某个错误类型字段时检测设备错误，并且切换到该字段所在的组件
    checkFiled() {
      if (!this.unqualified) return;
      let len = this.stateOptions.length - 1;
      for (let i = 0, length = len; i < length; i++) {
        let bool = this.hasFiled(this.$refs.property.formCustom, this.unqualified);
        if (bool) {
          break;
        } else {
          this.state = i;
        }
      }
    },
    // 判断该组件中是否有该字段
    hasFiled(formCustom, filed) {
      if (formCustom.hasOwnProperty(filed)) {
        return true;
      } else {
        return false;
      }
    },
    async dictTypeListGroupByType() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        const dataObject = data.data;
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = dataObject.find((row) => {
            return !!row[key];
          });
          this.$set(this.allDicData, this.dicDataEnum[key], obj[key]);
        });
      } catch (err) {
        console.log(err);
      }
    },
    selectCamera() {
      this.dictData = {
        sxjgnlx_receive: this.allDicData.sbgnlxTypeList,
        propertySearch_sbdwlx: this.allDicData.cameraPointTypeList,
      };
      this.$refs.ChooseDevice.init();
    },
    startSearch(data) {
      Object.assign(this.searchConditions, data);
      this.$refs.ChooseDevice.getList();
    },
    getOrgCode(code) {
      this.searchConditions.orgCodeList = code;
      this.$refs.SearchList.reset();
    },
    getDeviceIdList(data) {
      this.selectedList = data.chooseDeviceIds;
      this.$set(this.deviceData, 'multiViewChildrenList', data.chooseDeviceIds);
    },
    copy() {
      const deviceData = JSON.stringify(this.deviceData);
      // 主键id无用删除
      delete deviceData.deviceInfoId;
      window.sessionStorage.setItem('copyDeviceData', deviceData);
      this.$Message.success('复制成功');
    },
    paste() {
      const copyData = this.$util.common.deepCopy(this.copyData);
      this.defaultForm.deviceId && (copyData.deviceId = this.defaultForm.deviceId);
      this.defaultForm = copyData;
      this.selectedList = copyData.multiViewChildrenList || [];
      this.deviceData = this.$util.common.deepCopy(copyData);
      this.$Message.success('粘贴成功');
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
    isAdd() {
      return this.modalAction === 'add';
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.defaultError = {};
        this.defaultForm = {};
        this.deviceData = {};
        const deviceData = window.sessionStorage.getItem('copyDeviceData');
        this.copyData = deviceData ? JSON.parse(deviceData) : null;
        // 判断是否是新增获取设备信息
        if (!this.isAdd) {
          this.getDeviceById();
        }
      } else {
        // 关闭时重置为第一步
        this.state = 0;
        this.searchConditions = {};
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    BasicsProperty: require('@/views/viewassets/components/additive-attribute/basics-property.vue').default,
    StateProperty: require('@/views/viewassets/components/additive-attribute/state-property.vue').default,
    AdministrationProperty: require('@/views/viewassets/components/additive-attribute/administration-property.vue')
      .default,
    LabelProperty: require('@/views/viewassets/components/additive-attribute/label-property.vue').default,
    SafetyProperty: require('@/views/viewassets/components/additive-attribute/safety-property.vue').default,
    PhotoGallery: require('@/views/viewassets/components/additive-attribute/photo-gallery.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
    SearchList: require('./search-list.vue').default,
  },
};
</script>
<style lang="less" scoped>
.add-list {
  @{_deep}.ui-switch-tab {
    .list {
      width: 100%;
      .ivu-tabs-tab {
        text-align: center;
        width: 16.6%;
      }
    }
  }
  .copy {
    position: absolute;
    right: 15px;
    top: 7px;
    @{_deep} i {
      color: #8797ac;
      font-size: 16px;
    }
  }
  .add-header {
    text-align: center;
    height: 34px;
    display: flex;
    background-color: #1b3b65;
    align-items: center;
    justify-content: space-around;
    flex-wrap: nowrap;

    .table-head-state {
      color: #fff;
      font-size: 14px;
    }

    .table-content {
      height: 34px;
      line-height: 34px;
      display: inline-block;
      background: var(--color-primary);
      opacity: 1;
      font-size: 14px;
      background-color: var(--color-primary);
      color: #fff;
      cursor: pointer;
    }
  }
  .statustag {
    .flex;
    width: 56px;
    height: 19px;
    font-size: 14px;
    color: #ffffff;
    border-radius: 4px;
  }
  .flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
