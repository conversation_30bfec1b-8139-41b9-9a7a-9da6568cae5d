<template>
  <div>
    <p>{{ tableInfo.title }}</p>
    <Table :columns="columns" :data="tableData"></Table>
  </div>
</template>

<script>
export default {
  name: 'viewassets',
  data() {
    return {
      columns: [
        {
          title: '',
          align: 'center',
          children: [
            {
              title: '',
              key: 'value1',
              align: 'center',
            },
            {
              title: '',
              key: 'value2',
              align: 'center',
            },
          ],
        },
        {
          title: '',
          key: 'value3',
          align: 'center',
        },
      ],
      tableData: [{ age: '123' }],
    };
  },
  created() {},
  mounted() {
    this.setTableInfo(this.tableInfo);
  },
  methods: {
    setTableInfo(obj) {
      this.tableData = [obj];
      this.columns[0].title = obj.title1;
      this.columns[0].children[0].title = obj.title2;
      this.columns[0].children[1].title = obj.title3;
      this.columns[1].title = obj.title4;
    },
  },
  watch: {
    tableInfo(val) {
      this.setTableInfo(val);
    },
  },
  computed: {},
  props: {
    tableInfo: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
p {
  font-size: 16px;
  color: var(--color-table-header-th);
  margin-bottom: 6px;
}
.equipmentlibrary {
  overflow: hidden;
  position: relative;
  background: var(--bg-table) url('~@/assets/img/assetcomparison.png');
}

/deep/ .ivu-table-header th {
  height: 30px;
  color: var(--color-table-header-th);
  background: none !important;
  border: 1px solid var(--border-table);
}

/deep/ .ivu-table .ivu-table-tbody td {
  color: var(--color-display-text);
  font-size: 18px;
  font-weight: bold;
  background: none !important;
  border: 1px solid var(--border-table);
}
/deep/ .ivu-table .ivu-table-body {
  margin-top: -1px;
}
</style>
