<template>
  <header class="archives-header-content">
    <h2 class="archives-title">{{ headerName }}</h2>
    <div class="switch-btn">
      <template
        v-if="
          parentRouteName === 'people-archive' ||
          parentRouteName === 'video-archive'
        "
      >
        <!-- 从实名人档跳转 -->
        <template v-if="source === 'people' || source === 'zdr'">
          <a v-show="parentRouteName === 'video-archive'" @click="switchHandle">
            <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>实名档案
          </a>
          <template v-if="baseInfo.vids && baseInfo.vids.length">
            <Dropdown
              v-show="parentRouteName === 'people-archive'"
              trigger="hover"
              placement="bottom-start"
            >
              <a>
                <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>视频档案
              </a>
              <DropdownMenu slot="list">
                <div class="video-ul">
                  <div
                    v-for="(item, $index) in baseInfo.vids"
                    :key="$index"
                    class="video-li"
                    @click="videoChangeHandle(item)"
                  >
                    <div class="warning-tag-small">视</div>
                    <div class="warning">{{ item }}</div>
                  </div>
                </div>
              </DropdownMenu>
            </Dropdown>
          </template>
        </template>
        <!-- 从视频人档跳转 -->
        <template v-if="source === 'video'">
          <a
            v-show="
              parentRouteName === 'video-archive' && baseInfo.realNameArchiveNo
            "
            @click="videoSwitchHandle"
          >
            <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>实名档案
          </a>
          <a
            v-show="parentRouteName === 'people-archive'"
            @click="nameSwitchHandle"
          >
            <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>视频档案
          </a>
        </template>
      </template>
    </div>
    <nav class="archives-menu-content">
      <ul class="menu-list">
        <li
          class="menu-item"
          style="opacity: 0"
          v-if="filterSider.length < 3"
        ></li>
        <template v-for="(item, index) in filterSider">
          <router-link
            v-if="item.meta.tagShow"
            :key="index"
            tag="li"
            :to="{
              name: item.name,
              query: {
                archiveNo: archiveNo,
                plateNo: plateNo,
                idcardNo: idcardNo,
                source: newSource,
                initialArchiveNo: initialArchiveNo,
                type: type,
              },
            }"
            :class="activePath === item.name ? 'active' : ''"
            class="menu-item"
          >
            <span>{{ item.meta.title }}</span>
          </router-link>
        </template>
        <li
          class="menu-item"
          style="opacity: 0"
          v-if="filterSider.length < 3"
        ></li>
      </ul>
    </nav>
    <operate-bar :baseInfo="baseInfo"></operate-bar>
  </header>
</template>
<script>
import operateBar from "./operate-bar";
import { mapState, mapGetters } from "vuex";
import { getStyle } from "view-design/src/utils/assist";
// eslint-disable-next-line no-unused-vars
import { on, off } from "view-design/src/utils/dom";
// eslint-disable-next-line no-unused-vars
import { throttle } from "lodash";

export default {
  name: `iMenuHead`,
  props: {
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  components: { operateBar },
  computed: {
    ...mapState("admin/menu", ["activePath", "headerName"]),
    ...mapGetters("admin/menu", ["filterSider", "parentRouteName"]),
  },
  data() {
    return {
      archiveNo: "",
      plateNo: "",
      idcardNo: "",
      initialArchiveNo: "", //初始档案号
      source: "",
      type: "", // 视图设备车 | 人
      handleResize: () => {},
      isMenuLimit: false,
      menuMaxWidth: 0, // 达到这个值后，menu 就显示不下了
      newSource: "",
    };
  },
  watch: {
    filterHeader(val) {
      this.handleGetMenuHeight();
    },
    isMobile() {
      this.handleGetMenuHeight();
    },
    $route: {
      handler(to, from) {
        if (from?.path == "/vehicle-archive/vehicle-profile") {
          let paramArr = Object.keys(to.query).length
            ? to.query
            : JSON.parse(sessionStorage.getItem("query"));
          // let paramObj = {}
          // paramArr.forEach(v => {
          //     let key = v.split('=')[0]
          //     let value = v.split('=')[1]
          //     paramObj[key] = value
          // })
          this.archiveNo = paramArr.archiveNo;
          this.plateNo = paramArr.plateNo;
          this.idcardNo = paramArr.idcardNo;
          this.initialArchiveNo = paramArr.initialArchiveNo;
          this.source = paramArr.source;
        }
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  mounted() {
    // 防止因为Anchor锚点导致的路由query参数丢失
    let { archiveNo, source, plateNo, idcardNo, initialArchiveNo, type } =
      Object.keys(this.$route.query).length
        ? this.$route.query
        : JSON.parse(sessionStorage.getItem("query"));
    this.archiveNo = archiveNo;
    this.plateNo = plateNo;
    this.idcardNo = idcardNo;
    this.initialArchiveNo = initialArchiveNo;
    this.source = source;
    this.newSource = source;
    this.type = type;
    // this.handleResize = throttle(this.handleGetMenuHeight, 100, { leading: false });
    // on(window, 'resize', this.handleResize);
    this.handleGetMenuHeight();
  },
  beforeDestroy() {
    // off(window, 'resize', this.handleResize);
  },
  methods: {
    handleGetMenuHeight() {
      const menuWidth = parseInt(getStyle(this.$el, "width"));
      const $menu = this.$refs.menu;
      if ($menu) {
        const menuHeight = parseInt(getStyle(this.$refs.menu.$el, "height"));
        if (menuHeight > 64) {
          if (!this.isMenuLimit) {
            this.menuMaxWidth = menuWidth;
          }
          this.isMenuLimit = true;
        }
      } else if (menuWidth >= this.menuMaxWidth) {
        this.isMenuLimit = false;
      }
    },
    switchHandle() {
      this.archiveNo = this.baseInfo.realNameArchiveNo;
      this.$emit("on-change", "");
      this.newSource = "people";
      this.$router.push({
        name: "people-archive",
        query: {
          archiveNo: this.baseInfo.realNameArchiveNo,
          // source: this.source,
          source: "people",
          initialArchiveNo: this.initialArchiveNo,
        },
      });
    },
    // 切换视频档
    videoChangeHandle(vid) {
      this.archiveNo = vid;
      this.$emit("on-change", "");
      this.newSource = "video";
      this.$router.push({
        name: "video-archive",
        query: {
          archiveNo: vid,
          // source: this.source,
          source: "video",
          initialArchiveNo: this.initialArchiveNo,
        },
      });
    },
    // 视频档案跳转-视频档切换实名档案
    videoSwitchHandle() {
      this.archiveNo = this.baseInfo.realNameArchiveNo;
      this.$emit("on-change", "");
      this.newSource = "people";
      this.$router.push({
        name: "people-archive",
        query: {
          archiveNo: this.baseInfo.realNameArchiveNo,
          // source: this.source,
          source: "people",
          initialArchiveNo: this.initialArchiveNo,
        },
      });
    },
    // 视频档案跳转-实名档切换视频档案
    nameSwitchHandle() {
      this.archiveNo = this.initialArchiveNo;
      this.$emit("on-change", "");
      this.newSource = "video";
      this.$router.push({
        name: "video-archive",
        query: {
          archiveNo: this.initialArchiveNo,
          // source: this.source,
          source: "video",
          initialArchiveNo: this.initialArchiveNo,
        },
      });
    },
  },
};
</script>
