<template>
  <div class="index-container">
    <i id="indexSelect" class="icon-font icon-shaixuan f-16 color-filter" @click="visible = true"></i>
    <Dropdown trigger="custom" :visible="visible" transfer>
      <DropdownMenu slot="list" v-clickoutside="clickOutSize">
        <Collapse v-model="value1" class="dropdown">
          <Panel :name="key" v-for="(value, key, index) in data" :key="index">
            <span>{{ getIndexByKey(key) }}</span>
            <template #content>
              <Checkbox
                :disabled="selected.length > 2 && !item.check"
                :label="item.indexType"
                v-model="item.check"
                class="block"
                v-for="(item) in value"
                @on-change="onChange($event, item.indexType, item)"
                :key="item.id"
              >
                <span class="dis-select">{{ item.indexName }}</span>
              </Checkbox>
            </template>
          </Panel>
        </Collapse>
        <div class="mt-xs flex">
          <!--          <Button @click="visible = false" class="mini-btn">取消</Button>-->
          <Button class="mini-btn ml-sm" type="primary" @click="handleClick">确定</Button>
        </div>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'index-select',
  components: {},
  props: {
    data: {
      required: true,
      default: () => {
        return {};
      },
    },
    value: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      value1: '1',
      selected: [],
      visible: false,
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.selected = val;
        this.setCheckedKeys(this.selected);
      },
    },
    data: {
      deep: true,
      immediate: true,
      handler: function () {
        this.setCheckedKeys(this.selected);
      },
    },
  },
  filter: {},
  created() {},
  methods: {
    clickOutSize($event) {
      // 除了点击筛选按钮，点击其余部分都隐藏
      if ($event.srcElement.id === 'indexSelect') {
        this.visible = true;
        return;
      }
      this.visible = false;
    },
    onChange(val, indexType) {
      let index = this.selected.findIndex((value) => value === indexType);
      if (index !== -1) {
        this.selected.splice(index, 1);
      } else {
        this.selected.push(indexType);
      }
    },
    handleClick() {
      this.visible = false;
      this.$emit('change');
    },
    getIndexByKey(key) {
      let result = this.global.indexTypeList.find((item) => `${item.id}` === `${key}`);
      if (result) {
        return result.title;
      }
      return '其他';
    },
    getCheckedKeys() {
      let list = [];
      Object.keys(this.data).map((key) => {
        this.data[key].map((item) => {
          if (item.check) {
            list.push(item.indexType);
          }
        });
      });
      return list;
    },
    setCheckedKeys(list) {
      Object.keys(this.data).map((key) => {
        list.map((value) => {
          this.data[key].map((item) => {
            if (value === item.indexType) {
              item.check = true;
            }
          });
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
.mini-btn {
  width: 48px;
  height: 28px;
  line-height: 28px;
  padding: 0 5px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.index-container {
  position: relative;
  display: inline-block;
}
@{_deep} .ivu-collapse {
  width: 300px;
  background: #073167;
  border: none;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      border: none;
      color: #ffffff;
      i {
        float: right;
        line-height: 38px;
      }
    }
    .ivu-collapse-content {
      color: #bee2fb;
      background: #082249;
    }
  }
}
</style>
