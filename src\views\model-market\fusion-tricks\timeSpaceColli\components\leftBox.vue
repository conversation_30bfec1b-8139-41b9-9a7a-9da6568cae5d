<!--
    * @FileDescription: 时空碰撞
    * @Author: H
    * @Date: 2023/03/21
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <div class="search_box" :class="{'search_box-pack': packUpDown}">
            <div class="title">
                <p>{{ tabTitle.name }}碰撞</p>
                <ul class="card_cut_ul">
                    <li v-for="(item, index) in showTablist" :key="index" @click="handleTab(item, index)" class="card_cut_li">
                        {{ item.name }}
                    </li>
                </ul>
            </div>
            <div class="search_condition" :class="{'search_condition-pack': packUpDown}">
                <ul class="search_ul">
                    <li class="search_li" v-for="(item, index) in queryForm" :key="index">
                        <div class="search_title">
                            <div class="search_title_left">
                                <div class="list_icon" :style="{border:`3px solid ${item.color}`}"></div>
                                <p class="list_text">条件{{conditionNum[item.conditionNo -1]}}</p>
                            </div>
                            <Icon v-if="(queryForm.length == item.conditionNo) && queryForm.length > 2" type="ios-close" @click="handleDele(index)" />
                        </div>
                        <div class="search_form">
                            <div class="search_wrapper">
                                <div class="form_title">时间范围:</div>
                                <ul class="search_content">
                                    <li class="search_content-li" 
                                        :class="{'search_content-li-active': fastInd == item.fastIndex}"
                                        @click="handleFast(index, fastInd)"
                                        v-for="(fast, fastInd) in fastTime" :key="fastInd">
                                        {{ fast }}
                                    </li>
                                </ul>
                            </div>
                            <div class="search_wrapper">
                                <DatePicker type="datetimerange" 
                                    v-model="item.timeDate"
                                    @on-change="handleTimeChange($event, index)"
                                    format="yyyy-MM-dd HH:mm:ss" 
                                    placeholder="请选择"></DatePicker>
                            </div>
                            <div class="search_wrapper">
                                <div class="form_title">
                                    <p class="search_strut">选择范围:</p>
                                </div>
                                <div class="search_content">
                                    <li class="area-list" v-for="(item, ind) in toolMap" :key="ind"
                                        :class="{'active-area-list': ind == regionIndex[index].num, 'sele-area-list': regionIndex[index].select} "
                                        @click="handleSeleArea(index,ind, item)"> 
                                        <img :src="item.icon" alt=""> 
                                    </li>
                                    <p class="selectNum">已选择<span>({{ item.seleList }})</span></p>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="search_add">
                        <p class="add_left" @click="handleAdd">
                            <Icon type="md-add" />
                            <span>新增条件</span>
                        </p>
                        <p class="add_right">
                            <ui-btn-tip content="注：最多5个条件" :transfer="true" icon="icon-tishi mr-10 color-primary" />
                        </p>
                    </li>
                </ul>
            </div>
            <div class="footer-box">
                <div class="btn-group">
                    <Button type="primary" class="btnwidth" @click="handleSearch">分析</Button>
                    <Button type="default" @click="handleReset">重置</Button>
                </div>
                <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
                    <img :src="packUrl" alt="">
                    <p>{{ packUpDown ? '展开条件' : '收起条件'}}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {  dateTime, getConfigDate} from '@/util/modules/common';
export default {
    name: '',
    props: {
        seleArr: {
            type: Array,
            default: () => [0, 0, 0, 0, 0]
        },
        typeName: {
            type: String,
            default: 'face'
        }
    },
    components:{
    },
    data () {
        return {
            queryForm: [
                {
                    conditionNo: '1',
                    startDate: '',
                    endDate: '',
                    seleList: 0,
                    color: '#2C86F8',
                    fastIndex: -1,
                    timeDate: []
                },
                {
                    conditionNo: '2',
                    startDate: '',
                    endDate: '',
                    seleList: 0,
                    color: '#1FAF81',
                    fastIndex: -1,
                    timeDate: []
                }
            ],
            circleColor: ['#2C86F8','#1FAF81','#F29F4C','#A786FF','#48BAFF'],
            packUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            toolMap: [
                {
                    title: '圆形框选',
                    icon: require('@/assets/img/model/icon/circle.png'),
                    fun: this.selectDraw,
                    value: 'circle',
                    funName: 'selectCircle'
                },
                {
                    title: '矩形框选',
                    icon: require('@/assets/img/model/icon/rectangle.png'),
                    fun: this.selectDraw,
                    value: 'rectangle',
                    funName: 'selectRectangle'
                },
                {
                    title: '多边形框选',
                    icon: require('@/assets/img/model/icon/polygon.png'),
                    fun: this.selectDraw,
                    value: 'polygon',
                    funName: 'selectPolygon'
                },
            ],
            conditionNum: ['一', '二', '三', '四', '五'], 
            regionIndex: [
                {
                    'select': false,
                    'num': '-1'
                },
                {
                    'select': false,
                    'num': '-1'
                },
                {
                    'select': false,
                    'num': '-1'
                },
                {
                    'select': false,
                    'num': '-1'
                },
                {
                    'select': false,
                    'num': '-1'
                },
            ],
            showTablist: [],
            tabSeleList: [
                {   'name': '人脸', 'label': 'face', 
                    'code': 'Camera_Face',
                    'tab': 'faceCollisionResultVoList', 
                    'transfer': 'vid',
                    'sceneImg':'faceImg',
                    'first':'name',
                    'second':'idCardNo',
                    'thirdly':'vid',
                },
                {   'name': '车辆', 'label': 'vehicle',
                    'code': 'Camera_Vehicle', 
                    'tab': 'vehicleCollisionResultVoList', 
                    'transfer': 'vehicleId',
                    'sceneImg':'traitImage',
                    'first':'plateNo',
                    'second':'name',
                    'thirdly':'idCardNo',
                },
                { 'name': 'WiFi', 'label': 'wifi', 'code': 'Camera_Wifi',},
                { 'name': 'RFID', 'label': 'rfid', 'code': 'Camera_RFID',},
                { 'name': '电围', 'label': 'electric', 'code': 'Camera_Electric',},
            ],
            tabTitle: {},
            optionList: ['','','','',''],
            fastTime:['今天', '近7天', '近30天', '近90天'],
            option: {
                shortcuts:[
                    {
                        text: '今天',
                        value () {
                            return  dateTime(new Date().getTime()) ;
                        },
                        onClick: (picker) => {
                        }
                    },
                    {
                        text: '昨天',
                        value () {
                            const date = new Date();
                            date.setTime(date.getTime() - 3600 * 1000 * 24);
                            return dateTime(date.getTime());
                        },
                        onClick: (picker) => {
                        }
                    },
                    {
                        text: '一周前',
                        value () {
                            const date = new Date();
                            date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                            return dateTime(date.getTime());
                        },
                        onClick: (picker) => {
                        }
                    },
                    {
                        text: '一月前',
                        // value () {
                        //     const date = new Date();
                        //     date.setTime(date.getTime() - 3600 * 1000 * 24 * 30);
                        //     console.log(that.queryForm)
                        //     return dateTime(date.getTime());
                        // },
                        onClick: (picker) => {
                            console.log(picker)
                            console.log(dateTime(picker.panelDate), 'picker')
                            return dateTime(picker.panelDate)
                            
                        }
                    },
                ],
            }
        }
    },
    watch:{
        seleArr: {
            handler(val) {
                val.map((item, index) => {
                    if(this.queryForm.length >= index+ 1) {
                        this.queryForm[index].seleList = item;
                    }
                })
            },
            immediate:true
        }    
    },
    computed:{
            
    },
    activated() {
        
    },
    created() {
        this.typeShow()
    },
    mounted(){
            
    },
    methods: {
        typeShow() {
            let list =  this.tabSeleList.filter(item => item.label == this.typeName);
            this.tabTitle = list[0];
            this.showTablist = this.tabSeleList.filter(item => {
                if(item.label != this.tabTitle.label) {
                    return item
                }
            });
        },
        // 切换tab
        handleTab(item, index) {
            switch(item.label){
                case 'face':
                    this.$router.push({
                        name: 'timeSpaceColli-face',
                    })
                    break
                case 'vehicle':
                    this.$router.push({
                        name: 'timeSpaceColli-vehicle',
                    })
                    break
                default:
                    this.$Message.warning('系统建设中')
            }
        },
        handleSearch() {
            let maplist = new Map(this.tabSeleList.map(item=>[item.label, item]))
            let num = [];
            let seleNum = [];
            this.queryForm.map((item, index) => {
                let time = item.timeDate.some(item => item == '')
                // 是否为空
                if(item.timeDate.length == 0 || time){
                    num.push(index + 1);
                }else {
                    item.startDate = this.$dayjs(item.timeDate[0]).format('YYYY-MM-DD HH:mm:ss'); 
                    item.endDate = this.$dayjs(item.timeDate[1]).format('YYYY-MM-DD HH:mm:ss');
                }
                if(item.seleList == 0) {
                    seleNum.push(index+1)
                }
            })
            if(num.length > 0) {
                this.$Message.warning(`条件${num.join(',')}时间未选择`)
                return
            }
            if(seleNum.length > 0) {
                this.$Message.warning(`条件${seleNum.join(',')}范围未选择`);
                return
            }
            this.$emit('search', [this.tabTitle.label], maplist, this.queryForm)
        },
        handleReset() {
            this.queryForm = [
                {
                    conditionNo: '1',
                    startDate: '',
                    endDate: '',
                    seleList: 0,
                    color: '#2C86F8',
                    fastIndex: -1,
                    timeDate: []
                },
                {
                    conditionNo: '2',
                    startDate: '',
                    endDate: '',
                    seleList: 0,
                    color: '#1FAF81',
                    fastIndex: -1,
                    timeDate: []
                }
            ];
            this.optionList = ['','','','',''];
            this.regionIndex.map((item, index) => {
                this.$set(this.regionIndex, index, { 'select': false, 'num': '-1' })
            })
            this.$emit('reset')
        },
        // 新增搜索条件
        handleAdd() {
            if(this.queryForm.length == 5) {
                this.$Message.warning("最多添加5个条件！");
                return
            }
            let colorList = new Map(this.queryForm.map(item=>[item.color, item]))
            let color = '';
            for(let i = 0; i < this.circleColor.length; i++) {
                if(!colorList.get(this.circleColor[i])){
                    color = this.circleColor[i];
                    break;
                }
            }
            this.queryForm.push(
                {
                    conditionNo: (this.queryForm.length+1).toString(),
                    startDate: '',
                    endDate: '',
                    seleList: 0,
                    color: color,
                    fastIndex: -1,
                }
            )
            this.$nextTick(() => {  
                const offset = document.querySelector('.search_box').offsetHeight;
                const scroll = document.querySelector('.search_condition').scrollHeight;
                const box = document.querySelector('.time-space-colli').offsetHeight;
                if(offset + 20 >= box){
                    document.querySelector('.leftBox').style.height = '100%';
                    document.querySelector('.search_box').style.height = 'calc( 100% - 20px)'
                } else {
                    document.querySelector('.leftBox').style.height = 'auto'
                }
                if(offset-40 <= scroll) {
                    document.querySelector('.search_box').style.height = 'calc( 100% - 20px)'
                }
            })
        },
        // 选择时间
        handleFast(index, fastIndex) {
            this.queryForm[index].fastIndex = fastIndex;
            let arr = '';
            switch(fastIndex) {
                case 0:
                    arr = getConfigDate(-1);
                    this.queryForm[index].timeDate = [new Date(arr[1] + ' 00:00:00'),new Date(arr[1] + ' 23:59:59')]
                    break;
                case 1:
                    arr = getConfigDate(-6);
                    this.queryForm[index].timeDate = [new Date(arr[0] + ' 00:00:00'),new Date(arr[1] + ' 23:59:59')]
                    break;
                case 2:
                    arr = getConfigDate(-29);
                    this.queryForm[index].timeDate = [new Date(arr[0] + ' 00:00:00'),new Date(arr[1] + ' 23:59:59')]
                    break;
                case 3:
                    arr = getConfigDate(-89);
                    this.queryForm[index].timeDate = [new Date(arr[0] + ' 00:00:00'),new Date(arr[1] + ' 23:59:59')]
                    break;    
            }
        },
        // 选择区域
        handleSeleArea(index,ind, item) {
            // rectangle 矩形
            // circle    圆形
            // polygon   多边形
            let num = this.regionIndex[index].num;
            if(this.regionIndex[index].select){
                this.$emit('deleDraw', index);
                if( num != ind && num != '-1') {
                    this.$emit('selectDraw', item.funName, index, this.queryForm);
                    this.$set(this.regionIndex, index, { 'select': true, 'num': ind })
                } else {
                    this.$set(this.regionIndex, index, { 'select': false, 'num': '-1' })
                }
            }else{
                this.$emit('selectDraw', item.funName, index, this.queryForm);
                this.$set(this.regionIndex, index, { 'select': true, 'num': ind })
            }
        },
        // 删除
        handleDele(index) {
            this.queryForm.splice(index, 1);
            this.$set(this.regionIndex, index, { 'select': false, 'num': '-1' })
            this.optionList[index] = '';
            this.$nextTick(() => {
                const offset = document.querySelector('.search_box').offsetHeight;
                const scroll = document.querySelector('.search_condition').scrollHeight;
                const box = document.querySelector('.time-space-colli').offsetHeight;
                if(offset-40 >= scroll) {
                    document.querySelector('.leftBox').style.height = 'auto'
                    document.querySelector('.search_box').style.height = 'auto'
                }
            })
            this.$emit('deleDraw', index)
        },
        // 取消框选
        cancelSelect(index) {
            this.$set(this.regionIndex, index, { 'select': false, 'num': '-1' })
        },
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        },
        handleTimeChange(event, index) {
            this.queryForm[index].fastIndex = '-1';
        },
        handleStartChange(event, index) {
            this.queryForm[index].startDate = event;
            this.queryForm[index].endDate = '';
            let time = this.time(3, event);
            this.optionList[index] = {
                disabledDate (date) {
                    return (date && date.valueOf() < new Date(event).getTime()-8640000) ||  (date && date.valueOf() > new Date(time).getTime());
                }
            }
        },
        handleEndChange(event, index) {
            this.queryForm[index].endDate = event;
        },
        time(monthData = 3, times = '') {

            let date = times ? new Date(times) : new Date();
            let year = date.getFullYear();
            let month = date.getMonth() + 1;
            let day = date.getDate();
            let countDown = 12 - monthData;
            if (month <= countDown) {
                month += monthData
            } else if (month > countDown) {
                year += 1;
                month = month - 12 + monthData
            }
            if (month < 10) {
                month = "0" + month
            }
            if (day < 10) {
                day = "0" + day
            }
            let time = year + '-' + month + '-' + day + ' ' + '00:00:00';
            return time
        },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.leftBox{
    position: absolute;
    top: 10px;
    left: 10px;
    // height: 100%;
    .search_box{
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        max-height: calc( ~'100% - 20px');
        overflow: hidden;
        .card_cut_ul{
            display: flex;
            .card_cut_li{
                font-size: 12px;
                font-weight: 400;
                color: #2C86F8;
                margin-right: 20px;
                cursor: pointer;
                width: 26px;
                text-align: center;
                &:hover{
                    color: #4597FF;
                }
            }
        }
        .search_condition{
            padding: 17px 20px;
            max-height: calc( ~'100% - 120px');
            overflow-y: auto;
            box-sizing: border-box;
            .search_ul{
                .search_li{
                    margin: 0px 0 30px;
                    .search_title{
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        &_left{
                            display: flex;
                            align-items: center;
                        }
                        .list_icon{
                            width: 12px;
                            height: 12px;
                            background: rgba(44,134,248,0.1028);
                            opacity: 1;
                            border: 3px solid #2C86F8;
                            border-radius: 10px;
                        }
                        .list_text{
                            font-size: 14px;
                            font-weight: 700;
                            color: rgba(0,0,0,0.75);
                            line-height: 20px;
                            margin-left: 10px;
                        }
                        /deep/.ivu-icon-ios-close{
                            font-size: 30px;
                            cursor: pointer;
                            color: #888888;
                        }
                    }
                    .search_form{
                        .search_wrapper{
                            margin: 10px 0;
                            display: flex;
                            align-items: center;
                            /deep/.ivu-date-picker {
                                width: 350px !important;
                            }
                            .form_title{
                                font-size: 14px;
                                font-weight: 400;
                                color: rgba(0,0,0,0.4); 
                                margin-right: 10px;
                            }
                            .search_content{
                                width: 250px;
                                display: flex;
                                align-items: center;
                                /deep/.ivu-date-picker {
                                    width: 250px !important;
                                }
                                &-li{
                                    font-size: 14px;
                                    color: rgba(0,0,0,0.9);
                                    font-weight: 400;
                                    cursor: pointer;
                                    width: 65px;
                                    text-align: center;
                                    padding: 3px 0;
                                    margin-right: 15px;
                                }
                                &-li-active{
                                    color: #fff;
                                    background: #2C86F8;
                                    border-radius: 2px;
                                }
                                .area-list{
                                    width: 34px;
                                    height: 34px;
                                    background: #FFFFFF;
                                    border-radius: 4px;
                                    border: 1px solid #D3D7DE;
                                    cursor: pointer;
                                    color: rgba(0,0,0,0.6);
                                    margin-right: 10px;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                    img{
                                        opacity: 1;
                                    }
                                }
                                .active-area-list{
                                    border: 1px dashed #2C86F8;
                                    background: rgba(44, 134, 248, 0.10);
                                    color: rgba(44, 134, 248, 1);
                                    img{
                                        opacity: .6;
                                    }
                                }
                                .sele-area-list{
                                    // cursor: not-allowed;
                                }
                                .selectNum{
                                    font-size: 14px;
                                    font-weight: 400;
                                    color: rgba(0,0,0,0.9);
                                    span{
                                        color: rgba(44, 134, 248, 1);
                                    }
                                }
                            }
                        }
                    }
                }
                .search_add{
                    display: flex;
                    justify-content: space-between;
                    .add_left{
                        color: #2C86F8;
                        font-size: 14px;
                        font-weight: 400;
                        cursor: pointer;
                        &:hover{
                            color: #4597FF;
                        }
                    }
                    .add_right{
                        font-size: 14px;
                        color: #F29F4C;
                        /deep/ .icon-tishi{
                            color: #F29F4C !important;
                        }
                    }
                }
            }
        }
        .search_condition-pack{
            height: 0px;
            transition: height 0.2s ease-out;
            overflow: hidden;
            padding: 0;
        }
        .footer-box{
            box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.298);
            background: #FFFFFF;
            border-radius: 4px 4px 4px 4px;
            .btn-group{
                padding: 8px 20px 0 20px;
                .btnwidth{
                    width: 258px;
                }
            }
        }
    }
    .search_box-pack {
        height: 120px;
        transition: height 0.2s ease-out;
        overflow: hidden;
    }
}
/deep/ .ivu-checkbox-group{
    display: flex;
    justify-content: space-between;
}
/deep/ .ivu-checkbox-inner{
    margin-right: 0;
}
</style>
