<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        // 分布式身份确认接口稳定性
        case 'BASIC_CJQY_QUANTITY_STANDARD':
          return 'BasicCjqyQuantityStandard';
        case 'BASIC_ACCURACY':
          return 'BasicAccuracy';
        case 'BASIC_DEVICE_HUNDRED':
          return 'BasicDeviceHundred';
        default:
          return 'Original';
      }
    },
  },
  components: {
    Original: require('../basis-view-config').default,
    BasicAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/basis-view-config/components/basic-accuracy.vue')
        .default,
    BasicCjqyQuantityStandard: require('./components/basic-cjqy-quantity-standard').default,
    BasicDeviceHundred:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/basis-view-config/components/basic-device-hundred.vue')
        .default,
  },
};
</script>
<style lang="less" scoped></style>
