<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <div
        v-if="showNext"
        :class="{ grey: tableList.length == 1 }"
        class="leftIcon icon"
        @click="prev()"
      >
        <Icon type="md-arrow-dropleft" />
      </div>
      <div
        v-if="showNext"
        :class="{ grey: tableList.length == 1 }"
        class="rightIcon icon"
        @click="next()"
      >
        <Icon type="md-arrow-dropright" />
      </div>
      <header>
        <span>报警详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close', $event)"
        ></ui-icon>
      </header>
      <div class="level">
        <div class="level-title">
          <img
            v-if="vehicleInfo.bgIndex == 1"
            class="level"
            src="@/assets/img/target/bg1.png"
            alt
          />
          <img
            v-else-if="vehicleInfo.bgIndex == 2"
            class="level"
            src="@/assets/img/target/bg2.png"
            alt
          />
          <img
            v-else-if="vehicleInfo.bgIndex == 3"
            class="level"
            src="@/assets/img/target/bg3.png"
            alt
          />
          <img
            v-else-if="vehicleInfo.bgIndex == 4"
            class="level"
            src="@/assets/img/target/bg4.png"
            alt
          />
          <img v-else class="level" src="@/assets/img/target/bg5.png" alt />
          <div class="num">
            {{
              vehicleInfo.taskLevel == 1
                ? "一级"
                : vehicleInfo.taskLevel == 2
                ? "二级"
                : "三级"
            }}
          </div>
        </div>
        <div class="favorite">
          <ui-btn-tip
            v-if="vehicleInfo.myFavorite == 1"
            class="collection-icon"
            content="取消收藏"
            icon="icon-yishoucang"
            transfer
            @click.native="collection(2)"
          />
          <ui-btn-tip
            v-else
            class="collection-icon"
            content="收藏"
            icon="icon-shoucang"
            transfer
            @click.native="collection(1)"
          />
        </div>
      </div>
      <section class="dom-content" v-if="vehicleInfo">
        <div class="face-info">
          <div class="face-info-left">
            <div class="right-content">
              <div class="desc">抓拍照片</div>
              <!-- <img :src="vehicleInfo.sceneImg" v-viewer="{inline: true}"/> -->
              <details-largeimg
                :algorithmType="2"
                boxSeleType="vehicleRect"
                :info="vehicleInfo"
                @collection="collection($event, 1)"
                :collectionType="15"
              >
              </details-largeimg>
              <!-- <imgViewer :src="vehicleInfo.sceneImg" :key="Math.random()"/> -->
              <!-- <ui-image :src="vehicleInfo.sceneImg" /> -->
              <!-- <ui-image src="https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fblog%2F202104%2F12%2F20210412065010_25e75.jpeg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1682156782&t=2e9aaa823b61915751e754aef32265d8" alt="静态库" viewer /> -->
            </div>
          </div>
          <div class="face-info-right">
            <div class="contrast">
              <div class="block border">
                <div class="desc">报警照片</div>
                <div class="plateNo">
                  <ui-plate-number
                    :plateNo="vehicleInfo.plateNo"
                    :color="vehicleInfo.plateColor"
                  ></ui-plate-number>
                </div>

                <ui-image :src="vehicleInfo.traitImg"></ui-image>
                <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
              </div>
              <div class="block" v-if="vehicleInfo.taskLevel">
                <ui-image
                  v-if="vehicleInfo.bgIndex == 1"
                  class="animation"
                  :src="c1"
                ></ui-image>
                <ui-image
                  v-else-if="vehicleInfo.bgIndex == 2"
                  class="animation"
                  :src="c2"
                ></ui-image>
                <ui-image
                  v-else-if="vehicleInfo.bgIndex == 3"
                  class="animation"
                  :src="c3"
                ></ui-image>
                <ui-image
                  v-else-if="vehicleInfo.bgIndex == 4"
                  class="animation"
                  :src="c4"
                ></ui-image>
                <ui-image v-else class="animation" :src="c5"></ui-image>
                <div
                  class="num"
                  :class="{
                    c1: vehicleInfo.bgIndex == 1,
                    c2: vehicleInfo.bgIndex == 2,
                    c3: vehicleInfo.bgIndex == 3,
                    c4: vehicleInfo.bgIndex == 4,
                    c5: vehicleInfo.bgIndex == 5,
                  }"
                >
                  VS
                </div>
              </div>
              <div class="block border">
                <div class="desc">布控照片</div>
                <div class="plateNo">
                  <ui-plate-number
                    :plateNo="vehicleInfo.libVehiclePlateNo"
                    :color="vehicleInfo.plateColor"
                  ></ui-plate-number>
                </div>
                <!-- <ui-image :src="vehicleInfo.photoUrls"></ui-image> -->
                <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
                <swiper
                  ref="mySwiper"
                  v-if="vehicleInfo.photoUrls"
                  :options="swiperOption"
                  class="my-swiper"
                  :id="`swipe${index + 1}`"
                >
                  <swiper-slide
                    v-for="(item, index2) in vehicleInfo.photoUrls.split(',')"
                    :key="index2"
                  >
                    <ui-image
                      :src="item"
                      :list="vehicleInfo.photoUrls.split(',')"
                    />
                  </swiper-slide>
                </swiper>
                <img
                  v-else
                  src="@/assets/img/default-img/vehicle_default.png"
                  class="info-img"
                  alt
                />
                <div
                  v-if="vehicleInfo.photoUrls"
                  class="swiper-pagination"
                  :id="`swipe${index + 1}`"
                  :class="
                    vehicleInfo.photoUrls.split(',').length < 2
                      ? 'my-pagination-hidden'
                      : ''
                  "
                ></div>
              </div>
            </div>

            <div class="title">
              <div class="block"></div>
              布控详情
            </div>
            <div class="line">
              <div class="block">
                <div class="row">
                  <span class="label">车主姓名</span>:
                  <span class="message">{{ vehicleInfo.name }}</span>
                </div>
                <div class="row">
                  <span class="label">车辆颜色</span>:
                  <span class="message">{{
                    vehicleInfo.vehicleColor | commonFiltering(bodyColorList)
                  }}</span>
                </div>
              </div>
              <div class="block">
                <div class="row">
                  <span class="label">车辆类型</span>:
                  <span class="message">{{
                    vehicleInfo.vehicleType
                      | commonFiltering(vehicleClassTypeList)
                  }}</span>
                </div>
                <div class="row">
                  <span class="label">车辆品牌</span>:
                  <span class="message">{{
                    vehicleInfo.vehicleBrand | commonFiltering(vehicleBrandList)
                  }}</span>
                </div>
              </div>
            </div>
            <div class="control-info">
              <div class="left">
                <div class="traffic-record">
                  <!-- <div class="row">
                    <span class="label">姓名</span>:
                    <span class="message">{{ vehicleInfo.name }}</span>
                  </div>
                  <div class="row">
                    <span class="label">身份证号</span>:
                    <span class="message">{{ vehicleInfo.idCardNo }}</span>
                  </div> -->
                  <div class="row">
                    <span class="label">布控类型</span>:
                    <!-- <span class="message">{{ vehicleInfo.taskType == '1' ? '单体布控' : '库布控' }}<span v-if="vehicleInfo.taskType == '2'">({{ vehicleInfo.libName }})</span></span> -->
                    <ui-textOver-tips
                      class="message"
                      refName="deviceName"
                      :content="
                        vehicleInfo.taskType == '1'
                          ? '单体布控'
                          : '库布控' +
                            (vehicleInfo.taskType == '2'
                              ? '(' + vehicleInfo.libName + ')'
                              : '')
                      "
                    ></ui-textOver-tips>
                  </div>
                  <div class="row">
                    <span class="label">所属任务</span>:
                    <span class="message herf" @click="goTask()">{{
                      vehicleInfo.taskName
                    }}</span>
                  </div>
                </div>
                <div class="title">
                  <div class="block"></div>
                  报警详情
                </div>
                <div class="traffic-record">
                  <div class="row">
                    <span class="label">报警时间</span>:
                    <span class="message">{{ vehicleInfo.alarmTime }}</span>
                  </div>
                  <div class="row">
                    <span class="label">报警设备</span>:
                    <span class="message" :title="vehicleInfo.deviceName">{{
                      vehicleInfo.deviceName
                    }}</span>
                  </div>
                </div>
              </div>
              <div class="right">
                <!-- <ui-image v-if="vehicleInfo.operationType == 1" class="img" :src="valid"></ui-image> -->
                <img
                  v-if="vehicleInfo.operationType == 1"
                  class="level2"
                  src="@/assets/img/target/valid2.png"
                  alt=""
                />
                <img
                  v-else-if="vehicleInfo.operationType == 2"
                  class="level2"
                  src="@/assets/img/target/invalid2.png"
                  alt=""
                />
                <img
                  v-else
                  class="level2"
                  src="@/assets/img/target/unproces2.png"
                  alt=""
                />
              </div>
            </div>

            <!-- 地图 -->
            <div class="map">
              <mapBase
                v-if="vehicleInfo.geoPoint"
                :mapLayerConfig="{ showLatestLocation: true }"
                :positionPoints="[{ ...vehicleInfo }]"
              />
            </div>

            <!-- 处理意见 -->
            <div class="opinion">
              <Input
                v-model="formData.remark"
                type="textarea"
                :rows="4"
                placeholder="请输入处理意见"
              />
            </div>

            <div class="status">
              <div class="history" v-if="vehicleInfo.operationType !== 0">
                <Poptip
                  trigger="hover"
                  transfer
                  word-wrap
                  @on-popper-show="showHistory()"
                >
                  <i class="iconfont icon-lishijilu"></i>历史处理
                  <div slot="title">
                    <div class="block"></div>
                    <i>历史处理</i>
                  </div>
                  <div slot="content">
                    <Timeline>
                      <TimelineItem
                        v-for="item in historyList"
                        :key="item.creatorId"
                      >
                        <div class="time">
                          <div class="timeContent">
                            <div>{{ item.handleTime }}</div>
                            <div>操作人：{{ item.creatorName }}</div>
                          </div>
                        </div>
                        <div class="content">
                          <div class="content1">
                            <div class="p">
                              <span>处理操作：</span>
                              <div>
                                设为
                                <span v-if="item.operation == 1">"有效"</span
                                ><span v-else>"无效"</span>
                              </div>
                            </div>
                            <div class="p">
                              <span>处理意见：</span>
                              <div>{{ item.remark || "--" }}</div>
                            </div>
                          </div>
                        </div>
                      </TimelineItem>
                    </Timeline>
                  </div>
                </Poptip>
                <!-- <ui-btn-tip icon="icon-lishijilu" transfer/>
                历史处理 -->
              </div>
              <div class="tip">处理状态：</div>
              <RadioGroup v-model="vehicleInfo.operationType">
                <Radio :label="1">有效</Radio>
                <Radio style="margin-left: 20px" :label="2">无效</Radio>
              </RadioGroup>
            </div>
          </div>
        </div>
      </section>
      <footer>
        <Button @click="$emit('close', $event)">取消</Button>
        <Button class="margin" type="primary" @click="save()">保存</Button>
      </footer>
    </div>
  </div>
</template>
<script>
import {
  alarmView,
  batchHandleSensory,
  querySensoryAlarmHandleList,
  getDeviceBaseInfo,
} from "@/api/target-control";
import { addCollection, deleteMyFavorite } from "@/api/user";
import c1 from "@/assets/img/target/c-one.png";
import c2 from "@/assets/img/target/c-two.png";
import c3 from "@/assets/img/target/c-three.png";
import c4 from "@/assets/img/target/c-four.png";
import c5 from "@/assets/img/target/c-five.png";
import valid from "@/assets/img/target/valid.png";
import mapBase from "../../../components/map.vue";
import { mapActions, mapGetters } from "vuex";
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import detailsLargeimg from "@/components/detail/details-largeimg.vue";
import imgViewer from "./img-viewer.vue";
// import easyPlayer from '@/components/easy-player.vue'
import { swiper, swiperSlide } from "vue-awesome-swiper";

export default {
  components: {
    swiper,
    swiperSlide,
    mapBase,
    imgViewer,
    detailsLargeimg,
    // operateBar,
    // easyPlayer,
  },
  props: {
    showNext: {
      type: Boolean,
      default: true,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    tableIndex: {
      type: Number,
      default: 0,
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    vehicleInfo: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    tableList: {
      handler(row) {
        if (this.first) {
          this.vehicleInfo = this.tableList[this.tableIndex];
          this.first = false;
        }
      },
      deep: true,
      immediate: true,
    },
    vehicleInfo: {
      handler(row) {
        this.swiperOption.pagination = {
          el: "#swipe1.swiper-pagination",
          // el: row.photoUrls.split(',').length > 1 ? `#swipe${this.index + 1}` + '.swiper-pagination' : null, //控制分页显示隐藏
          clickable: true, //点击切换
        };
        // this.detail(row)
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      vehicleUseStatus: "dictionary/getVehicleUseStatus", //车辆使用状态
      vehicleUseNature: "dictionary/getVehicleUseNature", //车辆使用性质
      nationList: "dictionary/getNationList", //车辆使用性质
    }),
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        "margin-left": `${offsetX}px`,
        "margin-top": `${offsetY}px`,
      };
      style.maxWidth = style.maxHeight = "100%";
      return style;
    },
  },
  data() {
    return {
      c1,
      c2,
      c3,
      c4,
      c5,
      valid,
      formData: {},
      preview: true,
      tabList: [
        {
          name: "场景大图",
        },
        // {
        //   name: '历史视频'
        // }
      ],
      imgUrl: require("@/assets/img/default-img/vehicle_archives_default.png"),
      currentTabIndex: 0,
      faceInfo: {},
      collectionList: [{ count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }],
      currentCollectionIndex: 0,
      isChoose: false,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      checkStatus: true,
      isRequest: false,
      faceArchives: {},
      status: "",
      vehicleInfo: {},
      pageIndex: 0,
      historyList: [],
      swiperOption: {
        direction: "horizontal",
        pagination: {
          el: ".swiper-pagination",
          clickable: true, //点击切换
        },
      },
      index: 0,
      first: true,
    };
  },

  methods: {
    detail(row) {
      alarmView(row.alarmId).then((res) => {
        this.taskList = res.data;
      });
    },

    init(row) {
      this.checkStatus = true;
      this.faceInfo = { ...row };
      // return getPersonInfoByPersonId({vid: row.vid}).then(res => {
      //   this.transform = {
      //     scale: 1,
      //     deg: 0,
      //     offsetX: 0,
      //     offsetY: 0,
      //     enableTransition: false
      //   }
      //   this.faceInfo = { ...row }
      //   console.log('this.faceInfo', this.faceInfo)
      //   this.faceArchives = res.data
      //   return res
      // })
    },
    prev() {
      this.pageIndex -= 1;
      if (this.pageIndex == -1) {
        this.pageIndex = this.tableList.length - 1;
      }
      this.vehicleInfo = this.tableList[this.pageIndex];
      this.formData.remark = "";
      // this.deviceInfo()
    },
    next() {
      this.pageIndex += 1;
      if (this.pageIndex == this.tableList.length) {
        this.pageIndex = 0;
      }
      this.vehicleInfo = this.tableList[this.pageIndex];
      this.formData.remark = "";
      // this.deviceInfo()
    },
    save() {
      if (this.vehicleInfo.operationType === 0) {
        this.$Message.warning("请选择处理状态");
        return;
      }
      var param = {
        alarmRecordSimpleForms: [
          {
            alarmTime: this.vehicleInfo.alarmTime,
            alarmId: this.vehicleInfo.alarmId,
          },
        ],
        operationType: this.vehicleInfo.operationType,
        remark: this.formData.remark,
      };
      batchHandleSensory(param).then((res) => {
        this.$Message.success(res.data);
      });
    },
    tabsChange() {
      this.checkStatus = !this.checkStatus;
      if (!this.checkStatus) {
        this.archives();
      }
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    selectCollectionHandler(index) {
      this.currentCollectionIndex = index;
    },
    deviceInfo() {
      var params = {
        deviceId: this.vehicleInfo.deviceId,
      };
      getDeviceBaseInfo(params).then((res) => {
        this.$set(this.vehicleInfo, "geoPoint", res.data.geoPoint);
        // this.vehicleInfo.geoPoint = res.data.geoPoint
        this.$forceUpdate();
      });
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case "zoomOut":
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case "clocelise":
          transform.deg += rotateDeg;
          break;
        case "anticlocelise":
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
    handleMouseDown(e) {
      const { scale } = this.transform;
      if (scale === 1 || e.button !== 0) return;
      const { rafThrottle, on, off } = this.$util.common;
      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      const dom = this.$refs["img"];
      on(dom, "mousemove", this._dragHandler);
      dom.addEventListener("mouseup", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      dom.addEventListener("mouseleave", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      e.preventDefault();
    },
    handleDownload(name) {
      this.imgUrl = this.faceInfo.sceneImg;
      //下载图片地址和图片名
      let image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.imgUrl;
    },
    toDetail() {
      if (!this.faceArchives.archiveNo) {
        this.$Message.warning("暂无该人员档案信息");
        return;
      }
      const { href } = this.$router.resolve({
        name: "people-archive",
        query: {
          archiveNo: this.faceArchives.archiveNo,
          source: "people",
          initialArchiveNo: this.faceArchives.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
    archives() {
      if (!this.faceInfo.vid) {
        this.$Message.warning("暂无该人员档案信息");
        return;
      }
      this.isRequest = true;
      getPersonInfoByPersonId({ vid: this.faceInfo.vid }).then((res) => {
        this.transform = {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false,
        };
        this.faceArchives = res.data || {};
      });
    },
    /**
     * 收藏
     */
    collection(flag, type = 0) {
      if (type) {
        this.$set(this.vehicleInfo, "myFavorite", flag); // 用于不让页面刷新
        this.$emit("collection", flag);
        return;
      }
      var param = {
        favoriteObjectId: this.vehicleInfo.alarmId,
        favoriteObjectType: 15,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$set(this.vehicleInfo, "myFavorite", "1"); // 用于不让页面刷新
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$set(this.vehicleInfo, "myFavorite", "0");
          this.$emit("collection", flag);
        });
      }
    },
    /**
     * 处理历史
     */
    showHistory() {
      var param = {
        alarmTime: this.vehicleInfo.alarmTime,
        alarmId: this.vehicleInfo.alarmId,
        compareType: this.compareType,
      };
      querySensoryAlarmHandleList(param).then((res) => {
        this.historyList = res.data;
      });
    },
    goTask() {
      this.$router.push({
        name: "control-task-detailVehicle",
        query: {
          id: this.vehicleInfo.taskId,
          alarmCount: this.vehicleInfo.alarmCount,
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.dom {
  width: 1440px;
  height: 820px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;

  .icon {
    position: absolute;
    top: 46%;
    z-index: 999;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    /* text-align: center; */
    background: rgba(0, 0, 0, 0.5);
    /* line-height: 50px; */
    font-size: 30px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .leftIcon {
    left: -70px;
    z-index: 9;
  }
  .rightIcon {
    right: -70px;
    z-index: 9;
  }
  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }
  .level {
    display: flex;
    justify-content: space-between;
    // height: 30px;
    .level-title {
      position: relative;
      .num {
        position: absolute;
        top: 6px;
        color: #fff;
        left: 20px;
        font-weight: 800;
      }
    }
    .favorite {
      margin-right: 20px;
      margin-top: 10px;
    }
  }
  .level2 {
    width: 160px;
    margin-top: -20px;
  }
  .dom-content {
    position: relative;
    // padding: 10px 20px;
    padding: 0 22px 10px 16px;
    font-size: 14px;

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .face-list {
      display: flex;
      margin-bottom: 5px;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border: 1px solid #d3d7de;
        margin-right: 8px;
        cursor: pointer;
        position: relative;
        > img {
          width: 100%;
          height: 100%;
        }
        > span {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: #2c86f8;
          text-align: center;
          border-radius: 0px 0px 4px 0px;
          font-size: 12px;
          color: #ffffff;
          position: absolute;
          left: 0;
          top: 0;
        }
      }
      .active {
        border: 3px solid rgba(44, 134, 248, 1);
      }
    }

    .face-info {
      margin-top: 7px;
      display: flex;
      &-left {
        // flex: 1;
        height: 660px;
        width: calc(~" 100% - 435px ");
        .right-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .tablist {
            height: 28px;
            line-height: 28px;
            width: 400px !important;
            margin: 0;
            .ivu-tabs-tab {
              float: left;
              border: 1px solid #2c86f8;
              border-right: none;
              padding: 0 15px;
              color: #2c86f8;
              &:hover {
                background: #2c86f8;
                color: #ffffff;
              }
              &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-right: none;
              }
              &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-right: 1px solid #2c86f8;
              }
            }
            .active {
              background: #2c86f8;
              color: #fff;
            }
          }
        }
        .right-content {
          height: 100%;
          // margin-top: 6px;
          // border: 1px solid #d3d7de;
          background: #f9f9f9;
          position: relative;
          .desc {
            position: absolute;
            z-index: 9;
            background: rgba(0, 0, 0, 0.5);
            color: #fff;
            padding: 0 6px;
          }
          .complete-face {
            width: 580px;
            height: 430px;
            position: relative;
            text-align: center;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;

            > img {
              // width: 100%;
              // height: 100%;
            }

            > span {
              display: inline-block;
              width: 100%;
              height: 30px;
              line-height: 30px;
              position: absolute;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);
              .iconfont {
                color: #fff !important;
                padding: 0 12px;
                cursor: pointer;
              }
            }
          }
          .video {
            /deep/.easy-player {
              margin-top: 60px;
            }
          }
        }
      }
      &-right {
        position: relative;
        width: 420px;
        margin-left: 15px;
        > img {
          width: 200px;
          height: 200px;
          border: 1px solid #d3d7de;
        }
        .contrast {
          height: 100px;
          display: flex;
          justify-content: space-between;
          .block {
            position: relative;
            width: 100px;
            overflow: hidden;
            .animation {
              animation: rotation 30s linear infinite;
              /deep/ .ui-image-div {
                border: 0;
                background: transparent;
              }
            }
            @keyframes rotation {
              from {
                transform: rotateZ(360deg);
              }

              to {
                transform: rotateZ(0deg);
              }
            }
            .desc {
              position: absolute;
              z-index: 9;
              background: rgba(0, 0, 0, 0.5);
              color: #fff;
              padding: 0 6px;
            }
            .num {
              position: absolute;
              width: 100%;
              height: 100%;
              top: 0;
              left: 0;
              align-items: center;
              display: flex;
              justify-content: center;
              color: #2c86f8;
            }
            .c1 {
              color: #ea4a36;
            }
            .c2 {
              color: #e77811;
            }
            .c3 {
              color: #ee9f00;
            }
            .c4 {
              color: #36be7f;
            }
            .c5 {
              color: #2c86f8;
            }
          }
          .border {
            border: 1px solid #ebebeb;
          }
        }

        .line {
          .block {
            display: flex;
            .row {
              width: 50%;
              margin-top: 6px;
              .label {
                color: #999;
                width: 60px;
                // text-align: right;
                text-align-last: justify;
                text-align: justify;
              }
              .message {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-left: 10px;
              }
            }
          }
        }
        .title {
          margin-top: 20px;
          font-weight: 600;
          .block {
            width: 3px;
            background: #2c86f8;
            height: 16px;
            float: left;
            margin-top: 3px;
            margin-right: 6px;
          }
        }

        .control-info {
          display: flex;
          // display: none;
          .line {
          }
          .left {
            flex: 1;
            width: 0;
            z-index: 99;
            .title {
              margin-top: 20px;
              font-weight: 600;
              .block {
                width: 3px;
                background: #2c86f8;
                height: 16px;
                float: left;
                margin-top: 3px;
                margin-right: 6px;
              }
            }
            .row {
              display: flex;
              margin-top: 6px;
              .label {
                color: #999;
                width: 60px;
                // text-align: right;
                text-align-last: justify;
                text-align: justify;
              }
              .message {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-left: 10px;
              }
            }
          }
          .right {
            width: 160px;
            flex-shrink: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 12px;

            .img {
              margin-top: 33px;
              width: 100px;
              // height: 98px;
            }
          }
        }

        .map {
          margin-top: 10px;
          height: 130px;
          border: 1px solid #f7f7f7;
        }

        .opinion {
          margin-top: 12px;
          /deep/ .ivu-input {
            resize: none;
          }
        }

        .status {
          margin-top: 8px;
          .history {
            float: right;
            color: #2c86f8;
          }
          .tip {
            color: #999;
            width: 70px;
            text-align: right;
            float: left;
            line-height: 28px;
          }
        }
      }
    }
    .p {
      margin-top: 6px;
      width: 200px;
      display: flex;
      height: 16px;
      line-height: 16px;
      .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 61px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }
      .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .identity {
        cursor: pointer;
        color: #f29f4c;
      }
      .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }

  > footer {
    border-top: 1px solid #d3d7de;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 70px;
    .ivu-btn {
      margin: 0 5px;
    }
  }
}
.dom:before,
.dom:after {
  // content: '';
  // display: block;
  // border-width: 8px;
  // position: absolute;
  // bottom: -16px;
  // left: 420px;
  // border-style: solid dashed dashed;
  // border-color: #ffffff transparent transparent;
  // font-size: 0;
  // line-height: 0;
}
.title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  height: 20px;
  width: 100%;
  display: flex;
  align-items: center;
  margin-top: 10px;
  cursor: pointer;
  > span {
    color: rgba(0, 0, 0, 0.6);
    position: relative;
    margin-right: 34px;
  }
  .active {
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
  }
  .active:before {
    content: "";
    position: absolute;
    width: 56px;
    height: 3px;
    bottom: -3px;
    background: #2c86f8;
  }
  .num {
    font-weight: normal;
    > span {
      font-weight: normal;
      color: #2c86f8;
      position: relative;
    }
  }
  .more {
    color: rgba(0, 0, 0, 0.35);
  }
}

.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }
  .ivu-tooltip-rel {
    // margin-top: 3px;
  }
  /deep/ .icon-shoucang {
    color: #f29f4c !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}
.herf {
  color: #2c86f8;
  cursor: pointer;
}

/deep/ .ui-image-div {
  cursor: default !important;
}
/deep/ .swiper-container {
  height: 100%;
}
.swiper-pagination {
  bottom: 0px;
  width: 100%;
  z-index: 9;
}
/deep/.swiper-pagination-bullet {
  width: 6px;
  height: 6px;
  margin-left: 5px;
  background: #fff;
  opacity: 1;
}
/deep/.swiper-pagination-bullet-active {
  background: #2c86f8;
}

.plateNo {
  position: absolute;
  background: rgba(0, 0, 0, 0.6);
  width: 100%;
  bottom: 0;
  z-index: 9;
  display: flex;
  justify-content: center;
  padding: 2px;
}
/deep/ .map {
  cursor: default !important;
}
.ivu-icon {
  font-size: 60px;
}

.grey {
  /deep/ .ivu-icon {
    color: #5e5e5e;
  }
}
/deep/ .ivu-icon-md-arrow-dropleft {
  margin-left: -6px;
}
/deep/ .ivu-icon-md-arrow-dropright {
  margin-left: 6px;
}
.info-img {
  width: 100%;
  height: 100%;
}
</style>
