<template>
  <!-- 配置去年抓拍数量 -->
  <ui-modal v-model="visible" title="配置去年抓拍数量" :styles="styles" class="ui-modal" @query="query">
    <div class="base-text-color f-16 mb-sm search">
      <div class="inline">
        <InputNumber class="width-xs ml-md" :min="0" v-model="form.value" placeholder="请输入达标数量"></InputNumber>
        <Button class="ml-lg" type="primary" @click="clickBatchInput">批量填入</Button>
      </div>
    </div>
    <ui-table class="ui-table" :table-columns="tableColumns" :table-data="tableData">
      <template #janNum="{ index }">
        <InputNumber
          class="width-percent"
          :min="0"
          v-model="tableData[index]['janNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #febNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['febNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #marNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['marNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #aprNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['aprNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #mayNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['mayNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #junNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['junNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #julNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['julNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #augNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['augNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #septNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['septNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #octNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['octNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #novNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['novNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
      <template #decNum="{ index }">
        <InputNumber
          class="width-percent"
          v-model="tableData[index]['decNum']"
          placeholder="请输入抓拍数量"
        ></InputNumber>
      </template>
    </ui-table>
  </ui-modal>
</template>

<script>
export default {
  name: 'config-area-num',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    value: {},
    data: {},
  },
  data() {
    return {
      styles: {
        width: '6rem',
      },
      visible: false,
      loading: false,
      tableData: [],
      tableColumns: [
        {
          title: '月份',
          minWidth: 80,
          render: () => {
            return <span>抓拍数量</span>;
          },
        },
        { title: '1月', slot: 'janNum', minWidth: 80 },
        { title: '2月', slot: 'febNum', minWidth: 80 },
        { title: '3月', slot: 'marNum', minWidth: 80 },
        { title: '4月', slot: 'aprNum', minWidth: 80 },
        { title: '5月', slot: 'mayNum', minWidth: 80 },
        { title: '6月', slot: 'junNum', minWidth: 80 },
        { title: '7月', slot: 'julNum', minWidth: 80 },
        { title: '8月', slot: 'augNum', minWidth: 80 },
        { title: '9月', slot: 'septNum', minWidth: 80 },
        { title: '10月', slot: 'octNum', minWidth: 80 },
        { title: '11月', slot: 'novNum', minWidth: 80 },
        { title: '12月', slot: 'decNum', minWidth: 80 },
      ],
      form: {
        value: null,
      },
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.resetForm();
      }
    },
    data: {
      deep: true,
      immediate: true,
      type: Array,
      handler: function (val) {
        this.tableData = this.$util.common.deepCopy(val || []);
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    resetForm() {
      this.form.value = null;
    },
    validateForm() {
      let flag = true;
      this.tableData.some((item) => {
        Object.keys(item).some((key) => {
          if (!item[key] && key !== 'civilCode') {
            flag = false;
            this.$Message.error('抓拍数量不能为空');
            return true;
          }
        });
        if (!flag) {
          return true;
        }
      });
      return flag;
    },
    query() {
      if (!this.validateForm()) return;
      this.visible = false;
      this.$emit('commit', this.tableData);
    },
    clickBatchInput() {
      this.tableData.forEach((item) => {
        Object.keys(item).forEach((key) => {
          if (key !== 'civilCode') {
            this.$set(item, key, this.form.value || null);
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  height: 300px;
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
