<template>
	<div class="search">
		<Form :inline="true" ref="form" :model="form">
			<FormItem label="标记名称:" class="mr-20" prop="markTitle">
				<Input placeholder="请输入" v-model="form.markTitle" maxlength="50"></Input>
			</FormItem>
			<FormItem label="标记人:" class="mr-20" prop="creator">
				<Input placeholder="请输入" v-model="form.creator" maxlength="50"></Input>
			</FormItem>
			<FormItem label="标记颜色:" class="mr-20" prop="markColor">
				<color-select :isShowAll="true" :currentColor.sync="markColor"></color-select>
			</FormItem>
			<FormItem label="设备名称:" class="mr-20" prop="deviceName">
				<Input placeholder="请输入" v-model="form.deviceName" maxlength="50"></Input>
			</FormItem>
			<FormItem label="标记时间:" class="mr-20" prop="markTime">
				<DatePicker v-model="markTime" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择时间"></DatePicker>
			</FormItem>
			<FormItem class="btn-group">
				<Button type="primary" @click="startSearch">查询</Button>
				<Button @click="resetHandle">重置</Button>
			</FormItem>
		</Form>
	</div>
</template>
<script>
	import colorSelect from '@/components/color-select.vue'
	import { mapGetters } from "vuex";

	export default {
		components: {
			colorSelect
		},
		data() {
			return {
				form: {
					markTitle: '',
					creator: '',
					deviceName: '',
					markSt: '',
					markEt: '',
					markColor: ''
				},
				markColor: '',
				markTime: [],

			}
		},
		created() { },
		computed: {
			...mapGetters({
				userInfo: "userInfo"
			})
		},
		methods: {
			// 查询
			startSearch() {
				// this.form.userId = this.userInfo.id
				if (this.markTime.length > 0) {
					this.form.markSt = this.$dayjs(this.markTime[0]).format('YYYY-MM-DD HH:mm:ss')
					this.form.markEt = this.$dayjs(this.markTime[1]).format('YYYY-MM-DD HH:mm:ss')
				}
				this.form.markColor = this.markColor == '#EB4B4B' ? '1' : this.markColor == '#F29F4C' ? '2' : this.markColor == '#FDEE38' ? '3' : this.markColor == '#67D28D' ? '4' : this.markColor == '#2379F9' ? '5' : ''
				this.$emit('searchForm', this.form)
			},
			// 重置
			resetHandle() {
				this.$refs.form.resetFields()
				this.markTime = []
				this.form.markSt = ''
				this.form.markEt = ''
				this.markColor = ''
				this.startSearch()
			}
		}
	}
</script>
<style lang="less" scoped>
	.search {
		.mr-20 {
			margin-right: 20px;
		}
		.dialog-input {
			width: 200px;
		}
		.ivu-date-picker {
			width: 330px !important;
		}
	}
</style>
