<!--
    * @FileDescription: 数据统计
    * @Author: H
    * @Date: 2023/12/04
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
	<div class="container">
		<div class="search-bar">
			<searchDevice ref="searchDevice" @searchInfo="searchInfo"></searchDevice>
		</div>
		<div class="card-container">
			<div class="big-car">
				<div class="bg1">
					<div class="title">设备总数</div>
					<!-- <div class="num">{{ deviceNum }}</div> -->
					<count-to :start-val="0" :end-val="deviceNum" :duration="1000" class="num"></count-to>
					<img src="@/assets/img/opration-center/device5.png" alt="">
				</div>
			</div>
			<div class="big-car">
				<div class="bg2">
					<div class="title">数据通道数</div>
					<!-- <div class="num">{{ dataTypeNum }}</div> -->
					<count-to :start-val="0" :end-val="dataTypeNum" :duration="1000" class="num"></count-to>
					<img src="@/assets/img/opration-center/device6.png" alt="">
				</div>
			</div>
			<div class="small-card-box">
				<div class="small-item" v-for="(item,index) in cardList" :key="index">
					<div class="title">{{item.title}}</div>
					<div class="num" :style="{color:item.blue_c}">
						<count-to :start-val="0" :end-val="item.count" :duration="1000"></count-to>
					</div>
					<img :src="require('@/assets/img/opration-center/'+item.imageUrl)" alt="">
				</div>
			</div>
		</div>
		<div class="table-container">
            <Button type="primary" class="derive-btn" @click="handleImport($event)">导出</Button>
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList" :loading="loading">
					<template #dataType="{ row }">
						<div>{{ row.dataType | commonFiltering(dataTypeArrayList)}}</div>
					</template>
					<template #dataStatus="{ row }">
						<div>{{ row.dataStatus | commonFiltering(dataStatusArrayList)}}</div>
					</template>
					<template #networkStatus="{ row }">
						<Button class="statusBtn" :type=" row.networkStatus=='1'?'success':row.networkStatus=='0'?'error':'warning'" size="small"> 
							{{ row.networkStatus | commonFiltering(deviceNetworkStatusList)}} 
						</Button>
					</template>
				</ui-table>
			</div>
			<!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty>
			<ui-loading v-if="loading"></ui-loading> -->
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[10, 20, 40, 80]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
		</div>
        <hl-modal v-model="modalShow" title="提示" :r-width="500" @onCancel="loadCancel">
			<div class="content">
				<p class="tipLoad">数据打包中，请等候......</p>
				<!-- <p>大约尚需{{ maybeTime }}秒</p> -->
                <ui-loading v-if="modalLoading"></ui-loading>
			</div>
		</hl-modal>
	</div>
</template>
<script>
	import CountTo from 'vue-count-to'
	import { mapActions, mapGetters } from 'vuex';
	import searchDevice from './components/search-device';
    import hlModal from '@/components/modal/index.vue'
	import { deviceTotal, dataTypeTotal, deviceCountGroupByDataStatus, deviceDataStatList, deviceDataStatDown } from '@/api/data-warehouse';
	export default {
		name: 'device-check',
		components: {
			searchDevice,
			CountTo,
            hlModal
		},
		data() {
			return {
				queryParam: {
				},
				tableList: [
					// { aa: '蚌埠市雪亮工程/临时调整点位', bb: 'Y_CQ3089禹王宫门口(人脸识别)(结构化)', cc: '************', dd: '8000', ee: '4,946', ff: '人脸', gg: '无数据', hh: '1' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					// { aa: '蚌埠市雪亮工程/临时调整点位/未分配', bb: 'ZX_09_17沱西入村口卡口', cc: '34.54.116.6', dd: '37777', ee: '4,946', ff: '非机动车', gg: '正常', hh: '2' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '************', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/怀远县（主副驾）', bb: 'HY中央花园东门停车场', cc: '34.54.116.1', dd: '8000', ee: '3,710', ff: '车辆', gg: '增长百分之20-50', hh: '1' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '************', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
					// { aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '************', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
				],
				pageInfo: {
					pageNumber: 1,
					pageSize: 10,
				},
				total: 0,
				loading: false,
				columns: [
					{ title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
					{ title: '分组路径', key: 'dirPath', },
					{ title: '设备名称', key: 'deviceName', },
					{ title: 'IP', key: 'ipAddr', },
					{ title: '端口', key: 'port', },
					{ title: '接入数据量', key: 'accessNum', },
					{ title: '数据类型', width: 120, slot: 'dataType', },
					{ title: '统计状态', width: 120, slot: 'dataStatus', },
					{ title: '端口连接状态', width: 120, slot: 'networkStatus' }

				],
				cardList: [
					{ title: '无数据通道', count: 0, dataStatus: 2, imageUrl: 'device7.png' },
					{ title: '增长20%-50%', count: 0, dataStatus: 6, imageUrl: 'device8.png' },
					{ title: '增长50%-80%', count: 0, dataStatus: 7, imageUrl: 'device9.png' },
					{ title: '增长80%以上', count: 0, dataStatus: 8, imageUrl: 'device10.png' },
					{ title: '正常', count: 0, dataStatus: 1, imageUrl: 'device1.png', blue_c: '#48baff' },
					{ title: '减少20%-50%', count: 0, dataStatus: 3, imageUrl: 'device2.png', blue_c: '#48baff' },
					{ title: '减少50%-80%', count: 0, dataStatus: 4, imageUrl: 'device3.png', blue_c: '#48baff' },
					{ title: '减少80%以上', count: 0, dataStatus: 5, imageUrl: 'device4.png', blue_c: '#48baff' },
				],
				deviceNum: 0,
				dataTypeNum: 0,
                modalShow: false,
                modalLoading: false
			}
		},
		computed: {
			...mapGetters({
				dataStatusArrayList: 'dictionary/getDataStatusList', // 数据状态
				dataTypeArrayList: 'dictionary/getDataTypeList', // 数据类型
				deviceNetworkStatusList: 'dictionary/getDeviceNetworkStatusList',
			})
		},
		created() {	
		},
		async mounted() {
			await this.getDictData();
			this.$nextTick(() => {
				this.init();
				this.queryList()
			})
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			init() {
				let formData = this.$refs.searchDevice.formData;
				let params = { ...this.queryParam, ...formData };
				deviceTotal(params)
				.then(res => {
					this.deviceNum = res.data || 0;
				})
				dataTypeTotal(params)
				.then(res => {
					this.dataTypeNum = res.data || 0;
				})
				deviceCountGroupByDataStatus(params)
				.then(res => { 
					let list = res.data;
					let mapApps = new Map(list.map(item => [item.dataStatus, item]))
					this.cardList.forEach(item => {
						let map = mapApps.get(item.dataStatus);
						item.count = map.count;
					})
				})
			},
			queryList() {
				let formData = this.$refs.searchDevice.formData;
				let params = { ...this.queryParam, ...formData }
				this.loading = true;
				this.tableList = []
				deviceDataStatList({ ...params, ...this.pageInfo })
				.then(res => {
					const { total, entities } = res.data
					this.total = total
					this.tableList = entities
				})
				.catch(err => {
					console.error(err)
				})
				.finally(() => {
					this.loading = false;
				})
			},
            // 导出
            handleImport($event){
                $event.stopPropagation()
                this.modalShow = true;
                this.modalLoading = true;
                let formData = this.$refs.searchDevice.formData;
                let params = { ...this.queryParam, ...formData }
                deviceDataStatDown(params)
                .then(res =>{
                    let filePath = res.data.path;
                    let urllength = filePath.split('/')
                    let filename = urllength[urllength.length -1];
                    let flieType = filename.indexOf('zip') > 0 ? 'zip' : 'xlsx';
                    let url = 'http://'+ document.location.host;
                    Toolkits.ocxUpDownHttp(
                        "lis",
                        `${flieType}`,
                        `${url}${filePath}`,
                        `${filename}`
                    );
                    this.modalLoading = false;
                })
                .finally(() => {
                    this.modalShow = false;
                    this.modalLoading = false;
                })
            },
            loadCancel(){
				this.modalShow = false;
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				this.queryParam = obj
				this.queryList()
				this.init();
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.queryList()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.queryList()
			},
		}
	}
</script>
<style lang="less" scoped>
	.container {
		padding: 0;
		width: 100%;
        overflow: hidden;
		.card-container {
			height: 230px;
			padding: 0 20px 20px 20px;
			display: flex;
			.big-car {
				width: 20%;
				height: 100%;
				padding-right: 20px;
				.bg1 {
					height: 100%;
					padding: 20px;
					position: relative;
					background: linear-gradient(315deg, #1c6df4 4%, #39a1fc 100%);
					box-shadow: 0 5px 10px 0 rgba(54, 151, 251, 0.5);
					border-radius: 4px;
				}
				.bg2 {
					height: 100%;
					padding: 20px;
					position: relative;
					background: linear-gradient(141deg, #37d9af 0%, #1ca884 100%);
					box-shadow: 0 5px 10px 0 rgba(34, 180, 143, 0.5);
					border-radius: 4px;
				}
				.title {
					font-size: 16px;
					color: #fff;
				}
				.num {
					font-size: 32px;
					color: #fff;
					font-weight: bold;
				}
				img {
					width: 200px;
					height: 130px;
					position: absolute;
					right: 0;
					bottom: 0;
				}
			}
			.small-card-box {
				flex: 1;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				.small-item {
					width: 23.6%;
					height: 44.7%;
					padding: 20px;
					position: relative;
					background: linear-gradient(270deg, #fff 0%, #eef7ff 100%);
					box-shadow: 0 3px 5px 0 rgba(147, 171, 206, 0.7);
					border-radius: 4px;
					margin-bottom: 20px;
					.title {
						font-size: 16px;
					}
					.num {
						font-size: 28px;
						color: #2c86f8;
						font-weight: bold;
					}
					img {
						width: 70px;
						height: 70px;
						position: absolute;
						right: 12px;
						bottom: 12px;
					}
				}
			}
		}
	}
	.table-container {
		padding: 0 20px 0;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		position: relative;
		height: calc(~"100% - 320px");
        .derive-btn{
            width: 80px;
            margin-bottom: 10px;
        }
		.table-content {
			overflow: auto;
			flex: 1;
			display: flex;
			flex-wrap: wrap;
			justify-content: start;
			align-content: flex-start;
			.ui-table {
				height: 100%;
			}
			.statusBtn{
				cursor: default;
			}
		}
	}
</style>