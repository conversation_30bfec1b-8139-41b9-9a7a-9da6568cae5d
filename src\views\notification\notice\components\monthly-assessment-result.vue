<template>
  <ui-table
    ref="assessmentTable"
    class="ui-table"
    :table-columns="tableColumns"
    :table-data="tableData"
    :loading="loading"
    :row-class-name="rowClassName"
  >
    <template #ORG_REGEION_CODE="{ row }">
      <span class="active-blue">
        {{ row.ORG_REGEION_CODE }}
      </span>
    </template>
  </ui-table>
</template>

<script>
import MathJax from '@/components/math-jax';
export default {
  name: 'monthly-assessment-result',
  props: {
    monthResultNotification: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      orgCode: '',
      tableColumns: [
        {
          title: '行政区划',
          key: 'ORG_REGEION_CODE',
          slot: 'ORG_REGEION_CODE',
        },
      ],
      tableData: [],
    };
  },
  methods: {
    rowClassName(row) {
      if (!!this.$route.query.orgCode && row.orgCode === this.$route.query.orgCode) {
        return 'ivu-table-row-hover';
      }
    },
    // 月考核
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.score || v.score == 0) {
          v.renderHeader = this.renderTableHeader;
        } else {
          delete v.score;
        }
        if (v.code === 'ORG_REGEION_CODE') {
          v.slot = 'ORG_REGEION_CODE';
          v.minWidth = 160;
        } else if (v.code === 'TOTAL_SCORE') {
          v.minWidth = 100;
        } else {
          v.minWidth = v.title.length * 16 + 80;
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'ORG_REGEION_CODE') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr) {
      let item = arr.length && arr[0].length ? arr[0][0] : '';
      this.orgCode = item.orgRegeionCode;
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        v.forEach((k) => {
          if (k.code === 'ORG_REGEION_CODE') {
            tableData[i].ORG_REGEION_CODE = k.orgRegeionName;
            tableData[i].orgCode = k.orgRegeionCode;
            tableData[i].clickable = k.clickable;
          } else if (k.code === 'RANKING') {
            tableData[i][k.code] = {
              score: k.rank,
            };
          } else {
            tableData[i][k.code] = {
              score: k.score,
              examContentItemMonthResultId: k.examContentItemMonthResultId,
            };
          }
        });
      });
      this.tableData = tableData;
    },
    renderTooltip(h, params) {
      return h(
        'Tooltip',
        {
          props: {
            placement: 'right-start',
            prop: {
              transfer: true,
            },
          },
        },
        [
          h(
            'div',
            {
              slot: 'content',
            },
            [
              h(
                'div',
                {
                  class: ['tooltip-title'],
                },
                `${params.column.name}：`,
              ),
              h('div', {}, [
                h('span', {}, '分项分值：'),
                h('span', { class: ['active-blue'] }, `${params.column.score}分`),
              ]),
              h('div', {}, [
                h('span', {}, '实际分值：'),
                h(MathJax, {
                  class: ['active-blue'],
                  props: {
                    mathStr: params.column.examFormula || '',
                  },
                }),
              ]),
              h('div', {}, [h('span', {}, `考核方法：${params.column.examWay || ''}`)]),
            ],
          ),
          h('i', {
            class: ['icon-font', 'icon-wenhao', 'ml-xs', 'icon-warning'],
          }),
        ],
      );
    },
    renderTableHeader(h, params) {
      return h('div', [
        h('div', {}, [
          h('span', {}, `${params.column.title}  ( ${params.column.score}分 )`),
          params.column.examFormula && this.renderTooltip(h, params),
        ]),
        h(
          'i',
          {
            class: ['icon-font ml-xs f-12', params.column.show ? 'icon-xingzhuang' : 'icon-zhankai'],
            style: {
              cursor: 'pointer',
              display: params.column.parentTitle === undefined ? 'inline-block' : 'none',
            },
            on: {
              click: () => {
                this.tableColumns[params.index].show = !this.tableColumns[params.index].show;
                // 收缩
                if (!this.tableColumns[params.index].show) {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code + '_X';
                  this.tableColumns[params.index].children_str = this.tableColumns[params.index].children; //备份child
                  delete this.tableColumns[params.index].children;
                  this.handleTableHeaders(this.tableColumns);
                } else {
                  this.tableColumns[params.index].code = this.tableColumns[params.index].code.replace('_X', '');
                  this.tableColumns[params.index].children = this.tableColumns[params.index].children_str; //备份child
                  delete this.tableColumns[params.index].children_str;
                  this.handleTableHeaders(this.tableColumns);
                }
              },
            },
          },
          '',
        ),
      ]);
    },
    renderTableTd(h, params) {
      let clickable = params.row.clickable;
      // 朱耀 现场反馈 超额推送数量  得分 不支持点击跳转
      if (
        params.column.key === 'TOTAL_SCORE' ||
        !params.row[params.column.key] ||
        params.column.key === 'RANKING' ||
        params.column.name.endsWith('超额推送数量') ||
        params.column.name === '得分'
      ) {
        return h(
          'span',
          {
            attrs: {
              class: params.index === 0 ? 'active-blue' : '',
            },
          },
          params.row[params.column.key] ? params.row[params.column.key].score : '--',
        );
        // 判断大类处理点击事件
      } else {
        return h(
          'span',
          {
            style: {
              color: 'var(--color-title)',
              cursor: clickable ? 'pointer' : 'auto',
              textDecoration: clickable ? 'underline' : 'none',
            },
            attrs: {
              class: params.index === 0 ? 'active-blue' : '',
            },
            on: {
              click: () => {
                if (!clickable) {
                  return this.$Message.error('您没有此权限');
                }
                this.$emit('pictureAttributeIntegrityBtn', params);
              },
            },
          },
          params.row[params.column.key].score,
        );
      }
    },
  },
  watch: {
    monthResultNotification(val) {
      if (!val) return false;
      const monthResData = JSON.parse(val);
      let { headers, body } = monthResData;
      // 处理表头
      this.handleTableHeaders(headers);
      this.tableColumns = headers;
      for (let i of this.tableColumns) {
        i.show = true;
        if (i.name === '排名' || i.name === '总分') {
          i.sortable = true;
          i.sortMethod = (a, b, type) => {
            if (a && b) {
              if (type === 'asc') {
                return a.score < b.score ? -1 : 1;
              } else {
                return a.score > b.score ? -1 : 1;
              }
            }
          };
        }
        if (i.name === '行政区划' || i.name === '排名' || i.name === '总分') {
          i.fixed = 'left';
        }
      }
      // 处理表内容
      this.handleTableBody(body);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style scoped></style>
