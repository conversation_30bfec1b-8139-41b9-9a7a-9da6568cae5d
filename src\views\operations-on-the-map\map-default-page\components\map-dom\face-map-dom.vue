<template>
  <div class="dom">
    <header v-if="showHeader">
      <span>抓拍详情</span>
      <ui-icon
        type="close"
        :size="14"
        @click.native="() => $emit('close')"
      ></ui-icon>
    </header>
    <section class="dom-content">
      <carousel
        ref="carousel"
        v-show="positionPoints.length"
        :same-position-point="samePositionPoint"
        @changeVid="changeVid"
      ></carousel>
      <face-map ref="facemap" :faceInfo="faceInfo"></face-map>
    </section>
    <footer v-if="cutIcon">
      <search-around
        @preDetial="preDetial"
        @nextDetail="nextDetail"
      ></search-around>
    </footer>
  </div>
</template>
<script>
import searchAround from "../search-around.vue";
import carousel from "./carousel.vue";
import faceMap from "@/components/mapdom/face-map";
export default {
  components: {
    searchAround,
    carousel,
    faceMap,
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
    // 是否是地图上点击详情展示
    isMapClick: {
      type: Boolean,
      default: false,
    },
    clickIndexId: {
      type: [Number, String],
      default: 0,
    },
    // 是否展示头部，现在乱七八糟的，加个判断
    showHeader: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        "margin-left": `${offsetX}px`,
        "margin-top": `${offsetY}px`,
      };
      style.maxWidth = style.maxHeight = "100%";
      return style;
    },
    // samePositionPoint () {
    //   return this.positionPoints.filter((item, index) => {
    //     if (item.deviceId === this.faceInfo.deviceId) {
    //       item.Index = index + 1
    //     }
    //     return item.deviceId === this.faceInfo.deviceId
    //   })
    // },
  },
  data() {
    return {
      tabList: [
        {
          name: "场景大图",
        },
        // {
        //   name: '历史视频'
        // }
      ],
      currentTabIndex: 0,
      faceInfo: {},
      faceArchives: {},
      peripheryList: ["摄像机", "RFID设备", "Wi-Fi设备", "电围设备", "位置"],
      isChoose: false,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
      },
      imgUrl: "",
      makerName: "",
      cutIcon: true,
      samePositionPoint: [],
    };
  },
  watch: {},
  methods: {
    preDetial() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.faceInfo.featureId
      );
      if (Index < 1) return;
      this.$emit("preDetial", Index - 1);
    },
    nextDetail() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.faceInfo.featureId
      );
      if (Index >= this.positionPoints.length - 1) return;
      this.$emit("nextDetail", Index + 1);
    },
    init({ vid = "" }, pointItem, name = "", cutIcon = true, position = []) {
      // 为了兼容之前的代码，暂时先返回写死的data: true
      return new Promise((resolve) => {
        this.transform = {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false,
        };

        this.faceInfo = { ...pointItem };
        this.makerName = name;
        this.cutIcon = cutIcon; //用于判断模态框下方左右切换显隐
        this.$refs.facemap.init();
        if (position.length > 0) {
          // 高级搜索，数据上图
          this.tabAdvanced(position);
        }
        if (this.clickIndexId) {
          //普通搜索用于顶部切换
          this.tabmsgList();
        }
        resolve({ data: true });
      });
    },
    changeVid(item) {
      this.$emit("changeListTab", { ...item, name: this.makerName });
      this.faceInfo = { ...item };
      this.$refs.facemap.init();
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    tabmsgList() {
      this.samePositionPoint = this.positionPoints.filter((item, index) => {
        if (item.deviceId === this.faceInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.faceInfo.deviceId;
      });
      let tabIndex = 0;
      this.samePositionPoint.map((item, index) => {
        if (item.id == this.clickIndexId) {
          tabIndex = index;
        }
      });
      this.$refs.carousel.init(tabIndex);
    },
    tabAdvanced(list) {
      this.samePositionPoint = list.filter((item, index) => {
        if (item.deviceId === this.faceInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.faceInfo.deviceId;
      });
      let tabIndex = 0;
      this.$refs.carousel.init(tabIndex);
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.2,
        rotateDeg: 90,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case "zoomOut":
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
        case "clocelise":
          transform.deg += rotateDeg;
          break;
        case "anticlocelise":
          transform.deg -= rotateDeg;
          break;
      }
      transform.enableTransition = enableTransition;
    },
    handleMouseDown(e) {
      const { scale } = this.transform;
      if (scale === 1 || e.button !== 0) return;
      const { rafThrottle, on, off } = this.$util.common;
      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      const dom = this.$refs["img"];
      on(dom, "mousemove", this._dragHandler);
      dom.addEventListener("mouseup", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      dom.addEventListener("mouseleave", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      e.preventDefault();
    },
    handleDownload(name) {
      this.imgUrl = this.faceInfo.sceneImg;
      //下载图片地址和图片名
      let image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.imgUrl;
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
  //   padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}

.dom {
  width: 100%;
  //height: 520px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;

  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }

  .dom-content {
    padding: 10px 20px;
    font-size: 14px;
    height: calc(~"100% - 46px");
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  > footer {
    height: 50px;
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    // height: 55px;
    // line-height: 55px;

    > span {
      color: #2c86f8;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
</style>
