<template>
  <ul class="tab">
    <li class="li" v-for="(item, index) in list" :class="{select: item.value == selectLi}" @click="change(item, index)">
      {{ item.name }}
      <div v-if="index < list.length-1" class="line"></div>
    </li>
  </ul>
</template>
<script>
  
  export default {
    components: { 

    },
    props: { 
      selectLi: { 
        type: Number,
        default: 1
      },
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        select: 0
      }
    },
    computed: {},
    watch: {
      
    },
    mounted() {
    },
    methods: {
        init(index){
            this.selectLi = index
        },
      change(row, index) {
        // this.selectLi = row.value
        this.$emit('change', row)
      }
    }
  }
</script>
<style lang="less" scoped>
.tab {
  display: flex;
  background: #fff;
  margin-bottom: 10px;
  border-radius: 3px;
  .li {
    position: relative;
    padding: 10px 30px;
    font-size: 16px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.90);
    .line {
      height: 50%;
      width: 1px;
      border-right: 1px solid #bfbdbd;
      position: absolute;
      right: 0;
      top: 25%;
    }
  }
  .select {
    font-weight: 600;
    color: #2C86F8;
  }
}
</style>