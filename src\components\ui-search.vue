<template>
  <div class="ui-search" v-clickoutside="clickoutside">
    <slot name="title">
      <span ref="titleRef" class="title" @click="showMoreSearch">
        {{ showModal ? '收起' : '展开' }}
        <span :class="showModal ? 'rotate' : 'norotate'" class="icon-font icon-xiala"></span>
      </span>
    </slot>
    <div ref="searchModel" class="search-model" v-show="showModal">
      <div ref="searchModelBefore" class="search-model-before"></div>
      <div ref="searchModelAfter" class="search-model-after"></div>
      <slot name="content"> </slot>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showModal: false,
    };
  },
  computed: {},
  watch: {
    showModal(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.showModal = val;
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    showMoreSearch() {
      this.showModal = !this.showModal;
      this.$nextTick(() => {
        if (this.showModal) {
          this.$refs.searchModelBefore.style.left = this.$refs.titleRef.offsetLeft + 3 + 'px';
          this.$refs.searchModelAfter.style.left = this.$refs.titleRef.offsetLeft + 2 + 'px';
          this.$refs.searchModel.style.top = this.$refs.titleRef.offsetTop + 40 + 'px';
        }
      });
    },
    clickoutside() {
      this.showModal && (this.showModal = false);
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .ui-search {
    .search-model {
      .search-content {
        background: #0b2348;
        border: 1px solid #2169b9;
      }
    }
    .search-model-before {
      border-bottom: 10px solid #0b2348;
    }
    .search-model-after {
      border-bottom: 9px solid #2169b9;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .ui-search {
    .search-model {
      .search-content {
        background: #fff;
        box-shadow: 0px 2px 5px 0px rgba(147, 171, 206, 0.7016);
      }
    }
    .search-model-before {
      border-bottom: 10px solid #fff;
    }
    .search-model-after {
      border-bottom: 9px solid rgba(147, 171, 206, 0.3);
    }
  }
}
.ui-search {
  display: inline-block;

  .rotate {
    transition: all 0.2s ease-in-out;
    display: inline-block;
    transform: rotate(180deg);
  }
  .norotate {
    transition: all 0.2s ease-in-out;
    display: inline-block;
    transform: rotate(0deg);
  }
  .search-model {
    position: absolute;
    left: 0;
    z-index: 9;

    .search-content {
      transition: all 1s ease-in-out;
      padding: 0 20px;
      padding-top: 20px;
    }
  }
  .search-model-before {
    box-sizing: content-box;
    width: 0px;
    height: 0px;
    position: absolute;
    top: -16px;
    padding: 0;
    // border-bottom: 10px solid #0b2348;
    border-top: 8px solid transparent;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    display: block;
    z-index: 12;
  }
  .search-model-after {
    box-sizing: content-box;
    width: 0px;
    height: 0px;
    position: absolute;
    top: -18px;
    padding: 0;
    // border-bottom: 9px solid #2169b9;
    border-top: 9px solid transparent;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    display: block;
    z-index: 10;
  }
  .title {
    color: var(--color-btn-default);
    cursor: pointer;
    .icon-xiala {
      margin-left: 3px;
      font-size: 12px;
    }
  }
}
</style>
