<template>
  <ui-modal v-model="visible" :title="`设置${title}`" :styles="styles" class="ui-modal" @query="query">
    <common-capture-area-select class="clear-b" ref="captureAreaSelect" :data="areaTreeData">
      <template #configName>
        <div class="width-md">{{ title }}</div>
      </template>
      <template #label="{ data }">
        <div class="width-md">
          <Input v-show="data.check" class="width-xs" v-model="data.count" :placeholder="`请输入${title}`"></Input>
        </div>
      </template>
    </common-capture-area-select>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'regionalization-select',
  components: {
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    title: {},
    defaultProps: {
      default: () => {
        return {
          label: 'regionName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'regionCode',
    },
    data: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      styles: {
        width: '6.5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
      areaTreeData: [],
      form: {
        regionType: 'province',
        numberType: 'sxjValue',
        number: 1000,
      },
    };
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.setTreeData();
      } else {
        this.areaTreeData = [];
      }
    },
  },
  methods: {
    query() {
      let checkedTree = this.$refs.captureAreaSelect.commit();
      let checkedTreeData = checkedTree.map((items) => {
        return {
          key: items.regionCode,
          name: items.regionName,
          count: items.count || 0,
        };
      });
      // debugger
      let result = this.validateCheckedNodes(checkedTreeData);
      if (result) {
        this.$emit('query', checkedTreeData);
        this.visible = false;
      }
    },
    validateCheckedNodes(data) {
      if (!data.length) {
        this.$Message.error('请选择行政区划并设置数量');
        return false;
      }
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        let count = item.count;
        let reg = /^[1-9]\d*$/;
        if (count && !reg.test(count)) {
          this.$Message.error(`${item.name}配置数量必须是正整数`);
          return false;
        }
        if (!count) {
          this.$Message.error(`${item.name}配置数量不能为空`);
          return false;
        }
      }
      return true;
    },
    setTreeData() {
      let treeData = this.$util.common.deepCopy(this.treeData);
      treeData.forEach((item) => {
        let index = this.data.findIndex((value) => value.key === item.regionCode);
        item.count = index !== -1 ? this.data[index]['count'] : null;
        item.check = index !== -1;
      });
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.btn-mini {
  height: 24px;
  @{_deep} .ivu-input {
    height: 24px;
    line-height: 24px;
  }
}
.w100 {
  width: 100px;
}
.w230 {
  width: 230px;
}
.w160 {
  width: 160px;
}
.area-container {
  .area-filter {
    line-height: 34px;
  }
}
</style>
