<template>
  <div class="all-task-order-container height-full auto-fill">
    <search-buttons
      :batch-loading-obj="batchLoadingObj"
      @search="search"
      @clearAll="clearAll"
      @addData="addData"
      @exportExcel="exportExcel"
      @handleBatchOptions="handleBatchOptions"
    ></search-buttons>
    <div class="auto-fill">
      <ui-table
        ref="tableref"
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
        row-key="orderTaskId"
        :indent-size="12"
        :reserve-selection="true"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template slot="rate" slot-scope="{ row }">
          <div class="flex-aic progress-box">
            <Progress :percent="returnPercent(row)" hide-info stroke-color="#2d8cf0" :stroke-width="6" class="mr-sm" />
            <span>{{ row.rate }}</span>
          </div>
        </template>
        <template slot="action" slot-scope="{ row }">
          <template v-if="row.operationList?.length">
            <ui-btn-tip
              v-for="(item, index) in row.operationList"
              :key="index"
              class="operatbtn"
              :icon="item.iconName"
              :content="item.name"
              @click.native="item.func(row)"
            ></ui-btn-tip>
          </template>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        transfer
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
    <add-order
      v-if="addOrderVisible"
      :edit-view-action="{ type: 'add', row: {} }"
      v-model="addOrderVisible"
      @updateOrders="updateOrders('addOrderVisible')"
    ></add-order>
    <order-options-modal
      v-if="orderOptionVisible"
      v-model="orderOptionVisible"
      :batch-statistics-obj="batchStatisticsObj"
      :prop-search-params="propSearchParams"
      :component-name="componentName"
      :is-batch="isBatch"
      :select-data="selectData"
      :is-task-order="true"
      @closeOrderOptionsModal="closeOrderOptionsModal"
      @changeOrderOptionsModal="changeOrderOptionsModal"
    ></order-options-modal>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import { getTaskModeTableColumns } from './enum';
import { ALLORDER, ASSIGN_UNIT } from '../util/enum.js';
import { mapGetters } from 'vuex';
export default {
  name: 'taskWorkOrder',
  props: {
    commonSearchData: {},
    queryConditionStatus: {},
  },
  data() {
    return {
      tableColumns: getTaskModeTableColumns(this).filter((item) => item.isShow),
      tableData: [],
      selectData: [],
      defaultStoreData: [],
      tableLoading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {},
      batchLoadingObj: {
        //控制批量按钮的loading
        Deal: false,
        Assign: false,
        Close: false,
        Sign: false,
        Delete: false,
        Report: false,
        Export: false, //导出
      },
      isBatch: false,
      isCheckAll: false,
      batchStatisticsObj: {
        //批量统计数据
        total: 0,
        useCount: 0,
      },
      //弹窗控制
      addOrderVisible: false,
      orderOptionVisible: false,
      componentName: '',
      bathTypeEnum: {
        Assign: 'assign',
        Deal: 'deal',
        Close: 'close',
        Sign: 'sign',
        Delete: 'delete',
        Report: 'report',
        SignCancel: 'signCancel', //签收撤回
        AssignCancel: 'assignCancel', //指派撤回
      },
      propSearchParams: {}, //传给子组件的参数
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      routerList: 'permission/getRouterList',
      getUserInfo: 'user/getUserInfo',
      permisstionsList: 'permission/getPermisstionsList',
    }),
  },
  methods: {
    async search(searchData) {
      Object.assign(this.searchData, searchData);
      this.pageData.pageNum = 1;
      await this.getTableData();
    },
    clearAll(searchData) {
      Object.assign(this.searchData, searchData);
      this.pageData.pageNum = 1;
      this.selectDataReset();
      this.getTableData();
    },
    storeSelectList(data) {
      this.selectData = this.$util.common.deepCopy(data);
    },
    //清空选中的数据
    selectDataReset() {
      this.selectData = [];
      this.defaultStoreData = [];
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableData(false);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableData(false);
    },
    async getTableData(isClearChcekAll = true) {
      try {
        this.tableLoading = true;
        let { queryType, orgCode, beginTime, endTime, isOwnOrgCode } = this.commonSearchData;
        const params = {
          queryType: queryType,
          queryOrgCode: orgCode === '-1' ? '' : orgCode,
          beginTime: beginTime,
          endTime: endTime,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          isOwnOrgCode: queryType === ASSIGN_UNIT ? isOwnOrgCode : '',
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(governancetask.queryTaskList, params);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total;
        this.tableData = this.tableData.map((item) => {
          if (item.orderTaskList && item.orderTaskList.length) {
            return {
              ...item,
              orderTaskList: item.orderTaskList.map((child) => {
                return { ...child, parentOrderTaskId: item.orderTaskId, parentOrderTaskName: item.taskName };
              }),
            };
          } else {
            return { ...item, _disableExpand: true };
          }
        });
        isClearChcekAll && this.selectDataReset();
        this.handleOptionList();
      } catch (err) {
        console.log(err);
      } finally {
        this.tableLoading = false;
      }
    },
    //返回进度条组件所需要的格式化掉百分号的数
    returnPercent(row) {
      if (!row.rate) {
        return 0;
      }
      if (typeof row.rate === 'string') {
        return row.rate.slice(0, -1) * 1;
      }
      if (typeof row.rate === 'number') {
        return row.rate * 100;
      }
    },
    //
    handleOptionList() {
      let optionList = {
        signed: {
          name: '签收',
          func: (row) => this.handleBatchOptions('singleSignTips', false, row),
          iconName: 'icon-piliangqianshou',
          isShow: (row) => {
            return row.dqsAmount !== 0;
          },
        },
        close: {
          name: '关闭',
          func: (row) => this.handleBatchOptions('Close', false, row),
          iconName: 'icon-guanbigongdan',
          isShow: (row) => {
            return row.dgbAmount !== 0;
          },
        },
        view: {
          name: '查看',
          func: (row) => this.handleViewTaskRow(row),
          iconName: 'icon-chakangongdan',
          isShow: () => true,
        },
      };
      //判断按钮权限
      this.tableData.forEach((row) => {
        this.$set(row, 'operationList', []);
        for (let key in optionList) {
          optionList[key].isShow(row) && row.operationList.push(optionList[key]);
        }
      });
    },
    //点击查看行
    handleViewTaskRow(row) {
      let columnData = {
        key: 'total',
        queryConditionStatus: 'qb',
      };
      this.$emit('onStatisticsClick', row, columnData);
    },
    //新增工单
    addData() {
      this.editViewAction = {
        type: 'add',
        row: {},
      };
      this.addOrderVisible = true;
    },
    updateOrders(type) {
      this[type] = false;
      this.getTableData();
    },
    //导出
    async exportExcel() {
      try {
        this.batchLoadingObj['Export'] = true;
        let { queryType, orgCode, beginTime, endTime, isOwnOrgCode } = this.commonSearchData;
        const params = {
          queryType: queryType,
          queryOrgCode: orgCode === '-1' ? '' : orgCode,
          beginTime: beginTime,
          endTime: endTime,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          isOwnOrgCode: queryType === ASSIGN_UNIT ? isOwnOrgCode : '',
          ...this.searchData,
        };
        let res = await this.$http.post(governancetask.taskListExport, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.batchLoadingObj['Export'] = false;
      }
    },
    //批量所需要的参数
    returnTaskBatchParams(ortherParams = {}) {
      let { queryType, orgCode, beginTime, endTime, isOwnOrgCode } = this.commonSearchData;
      let params = {
        type: ALLORDER, //1是菜单拆分前的全部工单，后端仍需要此参数
        queryType: queryType,
        queryOrgCode: orgCode === '-1' ? '' : orgCode,
        beginTime: beginTime,
        endTime: endTime,
        queryConditionStatus: this.queryConditionStatus,
        pageNumber: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        isOwnOrgCode: queryType === ASSIGN_UNIT ? isOwnOrgCode : '',
        ...this.searchData,
        batchType: this.bathTypeEnum[this.componentName], //componentName：Assign => batchType:assign。
        // ids: this.isCheckAll ? [] : this.selectData.map((item) => item.id),
        orderTaskIdList: this.isCheckAll ? [] : this.selectData.map((item) => item.orderTaskId),
        ...ortherParams,
      };
      return params;
    },
    //点击批量
    async handleBatch(name, isBatch, row) {
      this.isDisabled = true;
      this.componentName = name;
      this.isBatch = isBatch;
      if (this.isBatch) {
        if (!this.selectData.length && !this.isCheckAll) {
          this.$Message.error('请勾选治理工单！');
          return false;
        }
        await this.getBatchBeforeStatistics();
      } else {
        await this.getBatchBeforeStatistics({ orderTaskIdList: [row.orderTaskId] });
        this.isBatch = true; //因为任务模式下点击的表格内的按钮打开的也是批量的弹窗样式，故需要手动将isBatch置为true
      }
      if (this.batchStatisticsObj.useCount === 0) {
        const text = `共${this.batchStatisticsObj.total - this.batchStatisticsObj.useCount}条工单无需（或无权）操作！`;
        this.$Message.error(text);
        return;
      }
      this.orderOptionVisible = true;
    },
    //获取批量操作的数据统计
    async getBatchBeforeStatistics(otherParam = {}) {
      const params = this.returnTaskBatchParams(otherParam);
      this.propSearchParams = params;
      try {
        this.batchLoadingObj[this.componentName] = true;
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, params);
        this.batchStatisticsObj = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.batchLoadingObj[this.componentName] = false;
      }
    },
    /**
     * 指派、处理、关闭三种批量操作需要调用弹窗组件操作
     */
    closeOrderOptionsModal() {
      this.orderOptionVisible = false;
    },
    //操作成功后刷新数据
    changeOrderOptionsModal() {
      this.orderOptionVisible = false;
      this.pageData.pageNum = 1;
      this.pageData.totalCount = 0;
      this.getTableData();
    },

    //对批量操作进行转发
    handleBatchOptions(name, isBatch, row) {
      switch (name) {
        case 'Assign':
        case 'Deal':
        case 'Close':
          this.handleBatch(name, isBatch, row); //指派、处理、关闭
          break;
        case 'batchToReport':
          this.batchToReport();
          break;
        case 'batchSignTips':
          this.batchSignTips();
          break;
        case 'singleSignTips':
          this.singleSignTips(row);
          break;
        case 'batchDelete':
          this.batchDelete();
      }
    },
    /**
     * 报备
     */
    //批量报备
    async batchToReport() {
      if (!this.selectData.length && !this.isCheckAll) {
        this.$Message.error('请勾选治理工单！');
        return false;
      }
      try {
        this.batchLoadingObj['Report'] = true;
        let statisticParams = this.returnTaskBatchParams({ batchType: 'report' });
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParams);
        let statisticData = data.data;
        this.batchLoadingObj['Report'] = false;
        //可用数量为0和超过1000条禁止报备
        if (statisticData.useCount === 0) {
          this.$Message.error(`共${statisticData.total}条工单无需（或无权）报备,请重新选择`);
          return;
        }
        if (statisticData.useCount > 1000) {
          this.$Message.warning({
            content: '每次最多选择1000条设备进行报备！',
            duration: 3,
          });
          return;
        }
        //1、设置提示语
        let tiptext =
          statisticData.total === statisticData.useCount
            ? `确定对此${statisticData.useCount}条工单设备进行报备吗？`
            : `共${statisticData.total - statisticData.useCount}条工单无需（或无权）报备,确定对剩余${
                statisticData.useCount
              }条工单设备进行报备吗？`;
        //2、设置参数
        let params = { ...statisticParams, pageSize: 1000 };
        //3、执行
        this.reportHandler(params, tiptext);
      } catch (err) {
        console.log(err);
      }
    },
    //执行报备操作(跳转到对应页面)
    reportHandler(params, tiptext) {
      this.$UiConfirm({
        content: tiptext,
        title: '提示',
        hasLoading: true,
      }).then(async ($vm) => {
        let datas = [];
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.batchPageList, params);
          datas = data.data.entities;
          if (!datas.length) {
            this.$Message.error('无可报备的设备,请重新选择');
            return; // 未获取到数据不跳转
          }
          let hasPermission = this.routerList.findIndex((item) => item.name === 'situationreporting');
          if (hasPermission == -1) {
            this.$Message.warning({
              content: '您暂无情况报备的菜单权限，请联系管理员添加!',
              duration: 3,
            });
            return;
          }
          //页面跳转到情况报备
          this.$router.push({
            name: 'situationreporting',
            params: {
              purpose: 'autoChooseAndCreateByDatas', //目的
              datas: datas,
            },
          });
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
    /**
     * 签收
     */
    //获取签收的提示（签收方法内使用）
    async getSignStatisticsData(statisticParam = {}) {
      try {
        this.batchLoadingObj['Sign'] = true;
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParam);
        let statisticData = data.data;
        this.batchLoadingObj['Sign'] = false;
        if (statisticData.useCount === 0) {
          this.$Message.error(`共${statisticData.total}条工单无需（或无权）签收,请重新选择`);
          return { isValid: false, tipText: '' };
        }
        // 有不能签收的工单
        if (statisticData.total !== statisticData.useCount) {
          return {
            isValid: true,
            tipText: `${
              statisticData.total - statisticData.useCount
            }个工单无需签收，确定签收剩余的工单任务吗？签收后请及时处理`,
          };
        }
        // 全部都能签收
        if (statisticData.total === statisticData.useCount) {
          return {
            isValid: true,
            tipText: '确定签收选中的工单任务吗？签收后请及时处理',
          };
        }
      } catch (err) {
        console.log(err);
        return {
          isValid: false,
        };
      }
    },
    //处理签收请求（签收方法内使用）
    signTipHandler(params, tipText = '确定签收该工单任务吗？签收后请及时处理！') {
      this.$UiConfirm({
        content: tipText,
        title: '警告',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.batchSign, params);
          this.$util.common.responseMessage({ ...data.data, successText: '成功签收' });
          this.getTableData();
          this.selectDataReset(); // 清空数据
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
    //批量签收
    async batchSignTips() {
      if (!this.selectData.length && !this.isCheckAll) {
        this.$Message.error('请勾选治理工单！');
        return;
      }
      let statisticParams = this.returnTaskBatchParams({ batchType: 'sign' });
      let { isValid, tipText } = await this.getSignStatisticsData(statisticParams);
      if (isValid) {
        this.signTipHandler(statisticParams, tipText);
      }
    },
    //单条签收
    async singleSignTips(row) {
      let statisticParams = this.returnTaskBatchParams({ orderTaskIdList: [row.orderTaskId], batchType: 'sign' });
      let { isValid, tipText } = await this.getSignStatisticsData(statisticParams);
      if (isValid) {
        this.signTipHandler(statisticParams, tipText);
      }
    },
    /**
     * 删除
     */
    //批量删除
    async batchDelete() {
      if (!this.selectData.length && !this.isCheckAll) {
        this.$Message.error('请勾选治理工单！');
        return false;
      }
      try {
        this.batchLoadingObj['Delete'] = true;
        let statisticParams = this.returnTaskBatchParams({ batchType: 'delete' });
        const { data } = await this.$http.post(governancetask.batchBeforeStatistics, statisticParams);
        //可操作的数量，包含总数total和可用useCount
        let statisticData = data.data;
        this.batchLoadingObj['Delete'] = false;
        if (statisticData.useCount === 0) {
          this.$Message.error(`共${statisticData.total}条任务下的工单您无权删除, 请重新选择！`);
          return false;
        }
        //1、设置删除提示语
        let tiptext =
          statisticData.total === statisticData.useCount
            ? `确定要删除此${statisticData.useCount}条工单吗？`
            : `有${statisticData.total - statisticData.useCount}条工单您无权删除。确定要删除剩余${
                statisticData.useCount
              }条工单吗？`;
        //2、设置删除参数
        let params = this.isCheckAll
          ? { ...statisticParams }
          : { orderTaskIdList: this.selectData.map((item) => item.orderTaskId), batchType: 'delete' };
        //3、完成删除请求
        this.deleteHandler(params, tiptext);
      } catch (err) {
        console.log(err);
      }
    },
    //处理删除请求
    deleteHandler(params = {}, tiptext = '确定要删除此工单吗？') {
      this.$UiConfirm({
        content: tiptext,
        title: '警告',
        hasLoading: true,
      }).then(async ($vm) => {
        try {
          $vm.loading = true;
          let { data } = await this.$http.post(governancetask.remove, params);
          this.getTableData();
          this.$Modal.remove();
          this.$util.common.responseMessage({ ...data.data, successText: '成功删除' });
          this.selectDataReset(); // 清空数据
        } catch (err) {
          console.log(err);
        } finally {
          $vm.loading = false;
          $vm.visible = false;
        }
      });
    },
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.getTableData();
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchButtons: require('./search-buttons.vue').default,
    AddOrder: require('../components/add-order/index.vue').default,
    orderOptionsModal: require('../components/order-options-modal/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.all-task-order-container {
  @{_deep} .link {
    cursor: pointer;
    text-decoration: underline;
  }
  @{_deep} .link-warning {
    color: var(--color-warning);
    cursor: pointer;
    text-decoration: underline;
  }
  @{_deep} .link-error {
    color: var(--color-failed);
    cursor: pointer;
    text-decoration: underline;
  }
  .progress-box {
    .ivu-progress {
      width: 140px;
    }

    @{_deep} .ivu-progress-inner {
      border: 1px solid var(--color-progress-default) !important;
      background: var(--bg-table-body-td) !important;
      padding: 2px;
    }
  }
  .operatbtn:not(:last-child) {
    margin-right: 15px;
  }
}
</style>
