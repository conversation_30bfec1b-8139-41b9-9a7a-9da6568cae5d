<template>
  <!-- 
    适配字典查询值
   -->
  <div :class="{'card':styleBol}">
    <div v-if="moreBtn" class="more" @click="showMore()">
      更多
      <Icon v-if="styleBol" type="ios-arrow-down" />
      <Icon v-else type="ios-arrow-up" />
      </div>
    <ul v-if="isChild">
      <li :class="{select: currentIndex == -1}" @click="allClick()">全部</li>
      <li :class="{childSelect: currentIndex == index}" v-for="(item, index) in list" :key="index" @click="liClick(item, index)">
        {{item.name}}
        <div v-show="currentIndex == index" class="sanjiao"></div>
      </li>
    </ul>
    <ul v-else>
      <li :class="{select: currentIndex == -1}" @click="allClick()">全部</li>
      <li :class="{select: currentIndex == index}" v-for="(item, index) in list" :key="index" @click="liClick(item, index)">
        {{item.name}}
        <div v-show="currentIndex == index && isChild" class="sanjiao"></div>
      </li>
    </ul>
    <div class="clear"></div>
    <ul class="child" v-if="dataList.length > 0">
      <li :class="{select: childIndex == index}" v-for="(item, index) in dataList" :key="index" @click="childLiClick(item, index)">
        {{item.name}}
      </li>
    </ul>
    <div class="clear"></div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex' 
export default {
  props: {
    // 数据
    list: { 
      type: Array,
      default: []
    },
    // 用于定义v-model的key
    vModel: {
      type: String,
      default: ""
    },
    // 是否显示更多按钮
    moreBtn: {
      type: Boolean,
      default: false
    },
    // 是否有子集
    isChild: {
      type: Boolean,
      default: false
    },
    // 是否是车身颜色组件
    carColor: {
      type: Boolean,
      default: false
    },
    // 回显，指定选中标签
    echo: {  
      type: String,
      default: null
    },
    oneLevel: {
      type: Object,
      default: () => null
    }
  },
  data () {
    return {
      currentIndex: -1,
      childIndex: null,
      styleBol: true,
      dataList: [],
      oneLevelObj: null,   // 一级对象，回显使用
    }
  },
  watch: {
    echo (newVal, oldVal) {
      // console.log('回显数据', newVal, oldVal, this.list)
      if (newVal) {
        this.list.forEach((item, i) => {
          if (item.dataKey == newVal) {
            this.currentIndex = i
          }
        })
      }
    },
    // oneLevel: {
    //   handler(newVal, oldVal) {
    //     console.log('oneLevel回显数据', newVal, oldVal, this.list)
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
  computed: {
    selectedStyle: ()=> {
      if (this.isChild) {
        return 'childSelect'
      }
      return 'select'
    },
    ...mapGetters({
      vehicleClassTypeKList: 'dictionary/getVehicleClassTypeKList',               // 客车
      vehicleClassTypeHList: 'dictionary/getVehicleClassTypeHList',               // 货车
      vehicleClassTypeQList: 'dictionary/getVehicleClassTypeQList',               // 牵引车
      vehicleClassTypeZList: 'dictionary/getVehicleClassTypeZList',               // 专项作业车
      vehicleClassTypeDList: 'dictionary/getVehicleClassTypeDList',               // 电车
      vehicleClassTypeMList: 'dictionary/getVehicleClassTypeMList',               // 摩托车
      vehicleClassTypeNList: 'dictionary/getVehicleClassTypeNList',               // 三轮汽车
      vehicleClassTypeTList: 'dictionary/getVehicleClassTypeTList',               // 拖拉机
      vehicleClassTypeJList: 'dictionary/getVehicleClassTypeJList',               // 轮式机械
      vehicleClassTypeGList: 'dictionary/getVehicleClassTypeGList',               // 全挂车
      vehicleClassTypeBList: 'dictionary/getVehicleClassTypeBList',               // 半挂车
      vehicleClassTypeXList: 'dictionary/getVehicleClassTypeXList',               // 其他
    })
  },
  async mounted() {
    if (this.carColor) {
      await this.getDictData2()
    }

    if (this.oneLevel && this.oneLevel.currentIndex) {
      this.currentIndex = this.oneLevel.currentIndex
      this.childIndex = this.oneLevel.childIndex
      this.styleBol = false
      this.setDataList(this.oneLevel.type)
    }
  },
  methods: {
    ...mapActions({
      getDictData2: 'dictionary/getDictAllData2'
    }),
    /**
     * 全部
     */
    allClick () {
      this.childIndex = -1
      this.dataList = []
      this.currentIndex = -1
      this.$emit('selectItem', this.vModel, null)
    },
    /**
     * 单个
     */
    liClick(item, index) {
      // console.log('liClick', item, index)
      this.currentIndex = index
      item.currentIndex = index
      this.styleBol = false
      this.setDataList(item.type)

      if (item.type) { // 有子集
        this.childIndex = 0
        item.childIndex = 0
        this.oneLevelObj = item
        this.$emit('selectItem', this.vModel, this.dataList[0], item)
      } else {
        this.$emit('selectItem', this.vModel, item)
      }
    },
    setDataList(type){
        switch (type) {
            case 'vehicleClassTypeKList':
                this.dataList = this.vehicleClassTypeKList
                break;
            case 'vehicleClassTypeHList':
                this.dataList = this.vehicleClassTypeHList
                break;
            case 'vehicleClassTypeQList':
                this.dataList = this.vehicleClassTypeQList
                break;
            case 'vehicleClassTypeZList':
                this.dataList = this.vehicleClassTypeZList
                break;
            case 'vehicleClassTypeDList':
                this.dataList = this.vehicleClassTypeDList
                break;
            case 'vehicleClassTypeMList':
                this.dataList = this.vehicleClassTypeMList
                break;
            case 'vehicleClassTypeNList':
                this.dataList = this.vehicleClassTypeNList
                break;
            case 'vehicleClassTypeTList':
                this.dataList = this.vehicleClassTypeTList
                break;
            case 'vehicleClassTypeJList':
                this.dataList = this.vehicleClassTypeJList
                break;
            case 'vehicleClassTypeGList':
                this.dataList = this.vehicleClassTypeGList
                break;
            case 'vehicleClassTypeBList':
                this.dataList = this.vehicleClassTypeBList
                break;
            case 'vehicleClassTypeXList':
                this.dataList = this.vehicleClassTypeXList
                break;
        
            default:
                break;
        }
    },
    childLiClick (item, index) {
      this.childIndex = index;
      this.oneLevelObj.childIndex = index;
      this.$emit('selectItem', this.vModel, item, this.oneLevelObj)
    },
    /**
     * 清空选中
     */
    clearChecked () {
        this.childIndex = null;
        this.dataList = [];
        this.currentIndex = null;
        this.$emit('selectItem', this.vModel, null)
    },
    /**
     * 显示更多
     */
    showMore () {
      this.styleBol = !this.styleBol
    }
  }
}
</script>
<style lang="less" scoped>
  .card {
    max-height: 60px;
    overflow: hidden;
  }
  .card:after {
    clear: both;
    content: "";
  }

  .select {
    color: #fff;
    background: #2C86F8;
    padding: 0 6px;
    border-radius: 2px;
  }
  .childSelect {
    color: #2C86F8;
  }
  .more {
    float: right;
    color: #2C86F8;
    cursor: pointer;
  }
  ul {
    margin: 0;
    padding: 0;
    li {
      position: relative;
      float: left;
      height: 26px;
      font-size: 14px;
      line-height: 26px;
    //   margin-right: 12px;
      padding: 0 6px;
    }
    li:hover {
      cursor: pointer;
    }
    // li::after {
    //   content:"";
    //   display:block;
    //   visibility:hidden;
    //   clear:both;
    // }
  }

  .child {
    padding: 6px 16px;
    background: #F9F9F9;
  }

  ul::after {
    content:"";
    display:block;
    visibility:hidden;
    clear:both;
  }

  .clear {
    clear: both;
    content: '';
  }

  .sanjiao {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -3px;
    width: 0;
    height: 0;
    border-top: 0px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #F9F9F9;
    border-left: 10px solid transparent;
  }

</style>
