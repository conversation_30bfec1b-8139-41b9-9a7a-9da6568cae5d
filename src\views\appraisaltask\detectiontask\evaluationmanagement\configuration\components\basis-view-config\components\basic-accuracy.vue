<template>
  <div class="basic-accuracy-container">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="125">
      <common-form
        :label-width="125"
        :form-data="formData"
        :form-model="formModel"
        ref="commonForm"
        :moduleAction="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
      >
      </common-form>
      <template>
        <FormItem label="更新填报状态" class="mb-md">
          <RadioGroup v-model="formData.isUpdatePhyStatus">
            <Radio :label="1" class="mr-lg">是</Radio>
            <Radio :label="0">否</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="检测规则设置" required>
          <rule-list
            class="rule-list"
            v-ui-loading="{ loading, tableData: indexRuleList }"
            :formModel="formModel"
            :formData="formData"
            :moduleAction="moduleAction"
            :indexRuleList="indexRuleList"
          ></rule-list>
        </FormItem>
      </template>
    </Form>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
  },

  data() {
    return {
      ruleCustom: {},
      formData: {
        regionCode: '',
        detectMode: '',
        cronType: '',
        cronData: [],
        timePoints: [],
        ruleList: [],
        isUpdatePhyStatus: 0,
      },
      indexRuleList: [],
      list: [],
      loading: false,
    };
  },
  async created() {
    await this.getCheckRule();
  },
  watch: {
    loadConfig: {
      handler(val) {
        this.setFormData();
        if (val) {
          this.setRuleListConfig();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    setFormData() {
      if (this.formModel === 'edit') {
        this.formData = {
          ...this.formData,
          ...this.configInfo,
        };
      } else {
        const { regionCode, schemeType } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode: regionCode, // 检测对象
          detectMode: '1',
          isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
        };
      }
    },
    setRuleListConfig() {
      this.indexRuleList.forEach((value) => {
        let rule = this.formData.ruleList.find((item) => value.ruleCode === item.ruleCode);
        this.$set(value, 'isConfigure', (rule && rule.isConfigure) || '0');
        return value;
      });
    },
    validateForm() {
      if (Array.isArray(this.indexRuleList) && this.indexRuleList.length) {
        return this.indexRuleList.some((item) => item.isConfigure === '1');
      }
      return false;
    },

    async getCheckRule() {
      try {
        this.loading = true;
        let res = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: 'BASIC_ACCURACY' },
        });
        this.indexRuleList = res.data.data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 表单提交校验
    async handleSubmit() {
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return false;
      }
      this.formData.ruleList = this.indexRuleList.filter((value) => value.isConfigure === '1');
      return await this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
    loadConfig() {
      if (this.indexRuleList.length && this.configInfo) {
        return true;
      }
      return false;
    },
  },
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
  },
};
</script>
<style lang="less" scoped>
.basic-accuracy-container {
  margin: 0 30px 0 20px;
}
.setting {
  color: var(--color-primary);
}
.form-content {
  /deep/.width-input {
    width: 468px;
    .ivu-input-group-append {
      background-color: #02162b;
      border: 1px solid #10457e;
      border-left: none;
    }
  }
  /deep/.ivu-form-item {
    .ivu-form-item-content {
      .ivu-checkbox-group {
        display: flex;
        // align-items: center;
        flex-direction: column;
        .ivu-checkbox-wrapper {
          display: flex;
          align-items: center;
          > span {
            margin-right: 10px;
          }
        }
      }
    }
  }
  .rule-list {
    min-height: 300px;
  }
}
</style>
