<template>
  <div class="inline" v-clickoutside="dropHide">
    <span v-if="!!selectTree.title" class="f-12">{{ selectTree.title }}</span>
    <Dropdown trigger="custom" :visible="visible">
      <div
        class="ivu-select ivu-select-single select-width t-left"
        :class="{ 'ivu-select-disabled': disabled }"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
      >
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!treeText">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeText">{{ treeText }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose || !clearable"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="clearable && isClose" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <template v-if="custormNode">
          <div
            v-for="(item, index) in custormNodeData"
            :key="index"
            :class="['custorm-tree-node', selectTree.regionCode === item.regionCode ? 'custom-selected' : '']"
            @click="handleCustormNode(item)"
          >
            {{ item.label }}
          </div>
        </template>
        <el-tree
          class="tree"
          node-key="regionCode"
          ref="treeRef"
          :style="treeStyle"
          :data="newTreeData"
          :props="defaultProps"
          :expand-on-click-node="expandNode"
          :default-expanded-keys="defaultKeys"
          @node-click="handleNodeClick"
        >
          <span class="custom-tree-node" slot-scope="{ node }">
            <span
              :class="{
                'nav-not-selected': node.disabled,
                allowed: node.disabled,
              }"
              >{{ node.label }}</span
            >
          </span>
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      visible: false,
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      defaultKeys: [],
      selectItem: {},
      isClose: false, //清空按钮是否显示
      newTreeData: [],
    };
  },
  created() {
    this.$store.dispatch('common/setAreaList');
  },
  methods: {
    mouseenter() {
      if (!!this.treeText && !this.disabled) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      if (data.disabled) {
        this.$Message.error('您没有此地区权限');
        return false;
      }
      this.selectTree.regionCode = data.regionCode;
      this.$emit('selectedTree', data);
      this.visible = false;
    },
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      if (!this.disabled) {
        this.visible = !this.visible;
      }
    },
    clear() {
      this.reset();
      this.$emit('selectedTree', this.selectTree);
    },
    reset() {
      this.selectTree.regionCode = '';
      this.isClose = false;
    },
    handleCustormNode(data) {
      this.selectTree.regionCode = data.regionCode;
      this.$refs.treeRef.setCurrentKey(null);
      this.$emit('selectedTree', data);
      this.visible = false;
    },
    // 根据传入的code获取下级code
    filterTree() {
      if (!this.filterTreeCode) {
        return (this.newTreeData = this.treeData);
      }
      let treeData = this.initialAreaList.filter((item) => {
        return item.regionCode === this.filterTreeCode || item.parentCode === this.filterTreeCode;
      });
      this.newTreeData = this.$util.common.arrayToJson(
        JSON.parse(JSON.stringify(treeData)),
        'regionCode',
        'parentCode',
      );
    },
  },
  watch: {
    treeData: {
      handler(val) {
        if (val.length !== 0) {
          this.defaultKeys = val.map((row) => {
            return row.regionCode;
          });
          this.filterTree();
        }
      },
      immediate: true,
    },
    filterTreeCode() {
      if (this.treeData.length) {
        this.filterTree();
      }
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getAreaList',
      initialAreaList: 'common/getInitialAreaList',
    }),
    treeText() {
      let value = '';
      const node = this.custormNodeData.find((node) => node.regionCode === this.selectTree.regionCode);
      if (this.custormNode && node) {
        value = node.label;
      } else {
        let node = this.initialAreaList.find((row) => {
          return row.regionCode === this.selectTree.regionCode;
        });
        value = node ? node[this.defaultProps.label] : '';
      }
      return value;
    },
    // 点击父节点是否展开子节点
    expandNode() {
      return !this.selectNode;
    },
  },
  props: {
    //是否显示清空按钮
    clearable: {
      default: true,
    },
    /**
     * selectTree.title: 标题
     * selectTree.regionCode: 选中的regionCode
     */
    selectTree: {
      required: true,
    },
    // 是否可以选中父节点
    selectNode: {
      default() {
        return true;
      },
    },
    // 树结构style
    treeStyle: {},
    placeholder: {
      type: String,
      default: () => {
        return '请选择';
      },
    },
    // 是否自定节点
    custormNode: {
      type: Boolean,
      default: false,
    },
    // 自定义节点数据
    custormNodeData: {
      type: Array,
      default: () => [],
    },
    disabled: {},
    // 根据filterTreeCode截取部分行政区划树展示
    filterTreeCode: {},
  },
};
</script>

<style lang="less" scoped>
.select-width {
  width: 200px;
}

.tree {
  min-width: 200px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
  padding-right: 10px;
}

.custorm-tree-node {
  height: 34px;
  width: 100%;
  padding: 0 6px;
  color: var(--color-select-item);
  cursor: pointer;
  background: var(--bg-el-tree-node-is-expanded);
  &:hover {
    background: var(--bg-select-item-hover);
  }
  &.custom-selected {
    background: var(--bg-select-item-active);
  }
}
@{_deep} .el-tree-node__content {
  height: 34px;
}
</style>
