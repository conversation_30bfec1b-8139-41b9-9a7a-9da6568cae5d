<template>
  <ui-modal
    ref="modal"
    v-model="visible"
    :title="title"
    class="modal"
    @onCancel="onCancel"
    @onOk="onOK"
  >
    <div
      class="content-wrapper"
      ref="mapRef"
      style="height: 600px; width: 1270px"
    >
      <div :id="mapId" class="map"></div>
    </div>
  </ui-modal>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions } from "vuex";

export default {
  name: "ChooseCenterMap",
  components: {},
  props: {
    title: {
      type: String,
      default: "请在地图上点击选择中心点",
    },
    defaultCenterPoint: {
      type: String,
      default: "",
    },
    // 场所围栏
    placeFence: {
      type: Object,
      default: () => {},
    },
    // 是否编辑
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      visible: false,
      keyWords: "",
      centerPoint: "",
      placeLayer: {
        //场所范围
        placeShop: null,
        placeEdu: null,
        placeGovernment: null,
        placeHouse: null,
        placeMedical: null,
        placeCompany: null,
        placeHotel: null,
        placeTraffic: null,
      },
      mapMain: null,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      globalObj: "systemParam/globalObj",
    }),
  },
  created() {},
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    _initMap(data, style) {
      // 配置初始化层级
      if (!this.mapMain) {
        this.mapMain = new NPGisMapMain();
        const mapId = this.mapId;
        this.mapMain.init(mapId, data, style);
        if (this.isEdit) {
          this.mapMain.map.addEventListener("click", (Point) => {
            this.addOneLayer(Point);
          });
        }
      }
      // 场所档案围栏
      if (this.placeFence) {
        this.setPolygon(this.placeFence, this.placeFence.layerType, true);
      }
      if (!!this.defaultCenterPoint) {
        let defaultCenterPoint = JSON.parse(this.defaultCenterPoint);
        const point = new NPMapLib.Geometry.Point(
          defaultCenterPoint[0],
          defaultCenterPoint[1]
        );
        this.addOneLayer(point);
        this.mapMain.map.setCenter(point);
        this.mapMain.map.centerAndZoom(defaultCenterPoint, 16);
      } else {
        this.configDefaultMap();
        this.addOneLayer(this.mapMain.map.getCenter());
      }
    },
    addOneLayer(point) {
      this.mapMain.map.clearOverlays();
      this.$nextTick(() => {
        let imgUrl = require(`@/assets/img/map/red-locate-icon.png`);
        let marker = new NPMapLib.Symbols.Marker(point);
        let size = new NPMapLib.Geometry.Size(60, 60);
        let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
        );
        // let label = new NPMapLib.Symbols.Label('222')
        // label.setStyle({
        //   fontSize: 14, //文字大小
        //   fontFamily: '宋体', //字体
        //   color: '#2C86F8', //文字前景色
        //   align: 'center', //对方方式
        //   isBold: true //是否粗体
        // })
        // label.setOffset(new NPMapLib.Geometry.Size(-0.5, 19.5))
        // marker.setLabel(label)
        marker.setIcon(icon);
        marker.k = point.lon + "_" + point.lat;
        this.mapMain.map.addOverlay(marker);
        this.centerPoint = [point.lon, point.lat];
      });
    },
    //绘制多边形场所范围AOI
    setPolygon(aoiData, aoiType, isPlaceArchive) {
      this.removeSetPolygonAll();
      var mapGeometry = new MapPlatForm.Base.MapGeometry(this.mapMain.map);
      this.placeLayer[aoiType] = new NPMapLib.Layers.OverlayLayer(
        aoiType,
        false
      );
      this.mapMain.map.addLayer(this.placeLayer[aoiType]);
      for (var i = 0; i < aoiData.data.length; i++) {
        var polygonItem = mapGeometry.getGeometryByGeoJson(
          aoiData.data[i].geometry,
          this.mapMain.map
        );
        polygonItem.setStyle({
          color: aoiData.color, //颜色
          fillColor: aoiData.color, //填充颜色
          weight: 2, //宽度，以像素为单位
          opacity: 1, //透明度，取值范围0 - 1
          fillOpacity: 0.5, //填充的透明度，取值范围0 - 1,
          lineStyle: NPMapLib.LINE_TYPE_DASH, //样式
        });
        this.placeLayer[aoiType].addOverlay(polygonItem);
        // let center = polygonItem.getCentroid().toString().split(",");
        let center = polygonItem.getPath()[0].toString().split(",");
        polygonItem.setData({
          properties: aoiData.data[i].properties,
          center,
        });
        this.placeLayer[aoiType].setZIndex(400);
        if (!this.defaultCenterPoint) {
          this.defaultCenterPoint = JSON.stringify(center);
        }
        // 场所档案只需要一个围栏，不添加事件
        if (!isPlaceArchive) {
          let that = this;
          polygonItem.addEventListener(
            NPMapLib.POLYGON_EVENT_CLICK,
            function (point) {
              that.aoiModelShow(point._data);
            }
          );
        }
      }
    },
    // 删除所有类型多边形
    removeSetPolygonAll() {
      for (let i in this.placeLayer) {
        if (this.placeLayer[i]) {
          this.placeLayer[i].removeAllOverlays();
          this.placeLayer[i] = null;
        }
      }
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = this.mapMain.map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      this.mapMain.map.centerAndZoom(point, mapLayerLevel);
    },
    init() {
      this.visible = true;
      this.getMapConfig();
    },
    onCancel() {
      this.visible = false;
      this.removeSetPolygonAll();
    },
    onOK() {
      this.visible = false;
      this.removeSetPolygonAll();
      this.$emit("confirm", this.centerPoint);
    },
    mapSearchHandler() {},
  },
  beforeDestroy() {
    if (this.mapMain) {
      this.mapMain.destroy();
      this.mapMain = null;
    }
  },
};
</script>

<style lang="less" scoped>
.modal {
  /deep/.ivu-modal {
    width: 1300px !important;
  }
  .content-wrapper {
    // height: 600px;
    // width: 1270px;
    .map {
      width: 100%;
      height: 100%;
    }
  }
  .search-input {
    width: 400px;
    margin-bottom: 10px;
    /deep/.ivu-input {
      width: 400px;
    }
    /deep/.ivu-icon-ios-search {
      color: #fff;
    }
  }
}
</style>
