<template>
  <basic-select v-bind="getAttrs" v-on="$listeners">
    <!-- 表格插槽 -->
    <template slot="outcome" slot-scope="{ row }">
      <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
      <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      <Tooltip
        transfer
        placement="bottom"
        v-if="
          row.detectionMode != null &&
          (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
        "
      >
        <i class="icon-font icon-wenhao ml-xs f-12" :style="{ color: 'var(--color-warning)' }"> </i>
        <div slot="content">
          <json-viewer
            :expand-depth="5"
            v-if="row.dateImageText != null"
            :value="JSON.parse(row.dateImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
          <json-viewer
            :expand-depth="5"
            v-if="row.additionalImageText != null"
            :value="JSON.parse(row.additionalImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
          <json-viewer
            :expand-depth="5"
            v-if="row.areaImageText != null"
            :value="JSON.parse(row.areaImageText)"
            theme="my-awesome-json-theme"
          ></json-viewer>
        </div>
      </Tooltip>
    </template>
    <template #detectionMode="{ row }">
      {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatusText ? '--' : row.phyStatusText }}
      </span>
    </template>

    <template #deviceId="{ row }">
      <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
        row.deviceId
      }}</span>
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
  </basic-select>
</template>
<script>
import errorCodesMixins from './errorCodesMixins';
export default {
  props: {
    qualifiedColorConfig: {},
  },
  mixins: [errorCodesMixins],
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
      },
    };
  },
  created() {},
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
        formItemData: this.formItemData, // errorCodesMixins.js
        moduleData: this.moduleData, // errorCodesMixins.js
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
