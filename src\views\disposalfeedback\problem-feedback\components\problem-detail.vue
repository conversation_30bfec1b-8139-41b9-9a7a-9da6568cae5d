<template>
  <ui-modal v-model="visible" title="问题详情" width="738px" footerHide @onCancel="onCancel" @query="onCancel">
    <div v-scroll="400" v-if="visible" class="problem-detail-main" v-ui-loading="{ loading: isLoading }">
      <div class="feedback-box">
        <text-title label="问题反馈信息" class="mb-md"></text-title>
        <detail-row label="问题编号：" :content="showObj.code || '-'"></detail-row>
        <detail-row label="数据类型：" :content="showObj.dataTypeText || '-'"></detail-row>
        <detail-row label="异常类型：" :content="showObj.causeCodesText?.join(',') || '-'"></detail-row>
        <detail-row
          v-for="item in formContent"
          :key="item.key"
          :label="`${item.title}：`"
          :content="item.value || '-'"
        ></detail-row>
        <detail-row label="问题描述：" :content="showObj.issueDetail"></detail-row>
        <detail-row label="反馈图片：" v-if="showObj.issueImages?.length" :imgList="showObj.issueImages"></detail-row>
        <detail-row label="提交人：" labelAlign="right" :content="showObj.name || '-'"></detail-row>
        <detail-row
          label="警号："
          labelAlign="right"
          :content="showObj.police || showObj.anonymousPolice || '-'"
        ></detail-row>
        <detail-row label="联系电话：" :content="showObj.phone || showObj.anonymousPhone || '-'"></detail-row>
        <detail-row :rowClass="'mb-lg'" label="提交时间：" :content="showObj.submitTime || '-'"></detail-row>
      </div>
      <div class="confirm-box mt-lg">
        <text-title label="问题确认信息" class="mb-md"></text-title>
        <detail-row label="有效状态">
          <span :style="{ color: effectiveStatus[showObj.effective]?.color }">{{
            effectiveStatus[showObj.effective]?.text || '-'
          }}</span>
        </detail-row>
        <detail-row label="备注：" labelAlign="right" :content="showObj.effectiveDetail || '-'"></detail-row>
        <detail-row label="确认人：" labelAlign="right" :content="showObj.effectiveUserName || '-'"></detail-row>
        <detail-row label="确认时间：" :rowClass="'mb-lg'" :content="showObj.effectiveTime || '-'"></detail-row>
      </div>
      <div class="dealing-box mt-lg">
        <text-title label="问题处理信息" class="mb-md"></text-title>
        <detail-row label="处理结果：">
          <span :style="{ color: handleResultStatus[showObj.status]?.color }">{{
            handleResultStatus[showObj.status]?.text || '-'
          }}</span>
        </detail-row>
        <detail-row label="处理说明：" :content="showObj.handleDetail || '-'"></detail-row>
        <detail-row label="处理图片：" v-if="showObj.handleImages?.length" :imgList="showObj.handleImages"></detail-row>
        <detail-row label="处理人：" labelAlign="right" :content="showObj.handleUserName || '-'"></detail-row>
        <detail-row label="处理时间：" :content="showObj.handleTime || '-'"></detail-row>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import feedbackApi from '@/config/api/feedback';
import { HANDLE_RESULT_STATUS, EFFECTIVE_STATUS } from '../util/enum';
export default {
  data() {
    return {
      visible: false, //问题详情弹窗
      handleResultStatus: HANDLE_RESULT_STATUS,
      effectiveStatus: EFFECTIVE_STATUS,
      isLoading: false,
      showObj: false,
    };
  },
  components: {
    TextTitle: require('./text-title.vue').default,
    DetailRow: require('./detail-row.vue').default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    tableRowObj: {
      type: Object,
      required: true,
    },
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.getInfoDeatail();
        }
        this.visible = val;
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('changeModalVisible', val);
    },
  },
  computed: {
    formContent() {
      if (typeof this.showObj.formContent === 'string') {
        return JSON.parse(this.showObj.formContent);
      } else {
        return this.showObj.formContent;
      }
    },
  },
  methods: {
    onCancel() {
      this.visible = false;
    },
    //单条获取详情
    async getInfoDeatail(inId) {
      // 若传入id用传入的id，没有则默认用当前id
      let params = {
        ids: [inId ? inId : this.tableRowObj.id],
      };
      try {
        this.isLoading = true;
        let { data } = await this.$http.post(feedbackApi.feedbackGetList, params);
        this.showObj = data.data[0] || this.tableRowObj;
      } catch (err) {
        console.log(err);
      } finally {
        this.isLoading = false;
      }
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .feedback-box,
  .confirm-box {
    border-bottom: 1px solid var(--devider-line);
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .feedback-box,
  .confirm-box {
    border-bottom: 1px solid #d3d7de;
  }
}
@{_deep} .ivu-modal-body {
  padding: 20px 26px 0 50px;
}
.problem-detail-main {
  padding-right: 20px;
  margin-bottom: 40px;
  .dealing-box {
    margin-bottom: 40px;
  }
}
</style>
