<template>
  <div class="layout">
    <Anchor class="anchor" show-ink container=".content">
      <!-- :scroll-offset="30" -->
      <AnchorLink href="#module1" title="全局设置" />
      <AnchorLink href="#module2" title="工作台" />
      <AnchorLink href="#module3" title="图上作战" />
      <!-- <AnchorLink href="#module4" title="智慧云搜" /> -->
      <AnchorLink href="#module5" title="全息档案" />
      <AnchorLink href="#module11" title="语义布控" />
      <AnchorLink href="#module6" title="目标管控" />
      <AnchorLink href="#module7" title="数智立方" />
      <AnchorLink href="#module8" title="数据仓库" />
      <AnchorLink href="#module10" title="模型集市" />
      <AnchorLink href="#module9" title="视频下载申请" />
    </Anchor>
    <!-- <div class="button">
        <Button type="primary" @click="save()">保存</Button>
        </div> -->
    <div class="content">
      <div class="module" id="module1">
        <div class="title">
          <h2>全局设置</h2>
          <Button type="primary" @click="save(1)">保存</Button>
        </div>
        <div class="row">
          <div class="left">水印显示</div>
          <div class="right">
            <div>
              <span class="span">选择展示信息</span>
              <CheckboxGroup v-model="formData.globalConfig.showInfo">
                <Checkbox label="姓名"></Checkbox>
                <Checkbox label="警号"></Checkbox>
              </CheckboxGroup>
            </div>
            <div class="btn">
              <i-switch v-model="formData.globalConfig.watermarkSwitch" />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">以图搜图</div>
          <div class="right2">
            <div class="childLeft">
              <span>搜索结果TOP</span>
              <Input
                v-model="formData.globalConfig.searchForPicturesTopN"
                placeholder="请输入"
                style="width: 150px"
                @on-keyup="verifiValue"
              />
            </div>
            <div class="childRight">
              <span>默认相似度</span>
              <!-- <Input search enter-button="22" placeholder="请输入" style="width: auto" /> -->
              <Input
                placeholder="请输入"
                v-model="
                  formData.globalConfig.searchForPicturesDefaultSimilarity
                "
                style="width: auto"
                @on-keyup="verifiValue($event, true, 100)"
              >
                <!-- <span slot="append">%</span> -->
              </Input>
              <div class="position">%</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">数据脱敏</div>
          <div class="right2">
            <div class="childLeft">
              <span>身份证号脱敏</span>
              <i-switch v-model="formData.globalConfig.idCardSensitiveSwitch" />
            </div>
            <div class="childRight">
              <span>手机号脱敏</span>
              <i-switch
                v-model="formData.globalConfig.phoneNumberSensitiveSwitch"
              />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">地图设置</div>
          <div class="right2">
            <div class="childLeft">
              <span>默认层级</span>
              <Input
                placeholder="请输入"
                v-model="formData.globalConfig.mapLayerLevel"
                style="width: auto"
                @on-keyup="verifiValue($event, false, 19, 1)"
              >
              </Input>
              <div class="position">层</div>
            </div>
            <div class="childRight">
              <span>中心坐标</span>
              <span
                class="link-active-color"
                @click="showChooseMap"
                v-if="!formData.globalConfig.mapCenterPoint"
                >请点击选择中心坐标</span
              >
              <span class="link-active-color" @click="showChooseMap" v-else>{{
                formData.globalConfig.mapCenterPoint
              }}</span>
              <!-- <Input v-model="formData.globalConfig.mapCenterPoint" placeholder="请输入" style="width: 150px" /> -->
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module2">
        <div class="title">
          <h2>工作台</h2>
          <Button type="primary" @click="save(6)">保存</Button>
        </div>
        <div class="row">
          <div class="left">我的收藏</div>
          <div class="right">
            <div class="preview">
              <div class="seleList">
                <draggable
                  :list="draglist"
                  group="tab"
                  animation="300"
                  @change="handleChangeDrag"
                >
                  <transition-group>
                    <div
                      v-for="(item, index) in draglist"
                      :key="item.value"
                      class="seletabs"
                    >
                      <span>{{ item.label }}</span>
                      <i
                        class="iconfont icon-close"
                        @click="handleDeleTab(index)"
                      ></i>
                    </div>
                  </transition-group>
                </draggable>
              </div>
              <Select
                v-model="formData.collect.collectValue"
                multiple
                @on-change="handleChangeCollect"
                @on-select="handleSeleCollect"
                :label-in-value="true"
              >
                <Option
                  v-for="(item, index) in formData.collect.collectList"
                  :value="item.dataKey"
                  :disabled="draglist.length > 4"
                  :key="index"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module3">
        <div class="title">
          <h2>图上作战</h2>
          <Button type="primary" @click="save(2)">保存</Button>
        </div>
        <div class="row">
          <div class="left">搜周边</div>
          <div class="right">
            <div>
              <span class="span">默认范围</span>
            </div>
            <div class="btn">
              <Input
                v-model="formData.mapConfig.maxPeripheryDistance"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue($event, true)"
              >
              </Input>
              <div class="position">M</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">数据上图</div>
          <div class="right">
            <div>
              <span>上图最大点位总量</span>
            </div>
            <div class="btn">
              <Input
                v-model="formData.mapConfig.maxNumberOfPointsInLayer"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue"
              />
            </div>
          </div>
          <!-- <div class="right2">
            <div class="childLeft">
              <span>图层最大创建数量</span>
              <Input
                v-model="formData.mapConfig.maxNumberOfLayer"
                placeholder="请输入"
                style="width: 150px"
                @on-keyup="verifiValue"
              />
            </div>
            <div class="childRight">
              <span>上图最大点位总量</span>
              <Input
                v-model="formData.mapConfig.maxNumberOfPointsInLayer"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue"
              />
            </div>
          </div> -->
        </div>
      </div>
      <!-- <div class="module" id="module4">
        <div class="title">
          <h2>智慧云搜</h2>
          <Button type="primary" @click="save(5)">保存</Button>
        </div>
        <div class="row">
          <div class="left">静态库配置</div>
          <div class="right">
            <Select v-model="formData.searchConfig.faceList" multiple>
              <Option
                v-for="(item, index) in formData.searchConfig.faceLibConfig"
                :value="item.field_name"
                :key="index"
                >{{ item.field_name_cn }}</Option
              >
            </Select>
          </div>
        </div>
      </div> -->
      <div class="module" id="module5">
        <div class="title">
          <h2>全息档案</h2>
          <Button type="primary" @click="save(3)">保存</Button>
        </div>
        <div class="row">
          <div class="left">行为规律</div>
          <div class="right">
            <div>
              <span>白天时间</span>
            </div>
            <div class="btn">
              <TimePicker
                v-model="formData.archivesConfig.timeInfo"
                format="HH"
                type="timerange"
                placement="bottom-end"
                placeholder="选择时间"
              ></TimePicker>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">实名人档</div>
          <div class="right preview">
            <div class="span">背景信息</div>
            <Select
              v-model="formData.archivesConfig.realNameArcBackground"
              multiple
              :max-tag-count="2"
            >
              <Option
                v-for="(item, index) in resourceList"
                :value="item.id"
                :key="index"
                >{{ item.resourceNameCn + "  -  " + item.resourceName }}</Option
              >
            </Select>
          </div>
        </div>
        <div class="row">
          <div class="left">车辆档案</div>
          <div class="right preview">
            <div class="span">背景信息</div>
            <Select
              v-model="formData.archivesConfig.vehicleArcBackground"
              multiple
              :max-tag-count="2"
            >
              <Option
                v-for="(item, index) in resourceList"
                :value="item.id"
                :key="index"
                >{{ item.resourceNameCn + "  -  " + item.resourceName }}</Option
              >
            </Select>
          </div>
        </div>
        <!-- <div class="row">
                <div class="left">背景信息</div>
                <div class="right2">
                    <div class="childLeft">
                    <span>实名人档</span>
                    <Button type="primary">配置</Button>
                    </div>
                    <div class="childRight">
                    <span>车辆档案</span>
                    <Button type="primary">配置</Button>
                    </div>
                </div>
                </div> -->
        <div class="row">
          <div class="left">人-IMSI拟合</div>
          <div class="right2">
            <div class="childLeft">
              <span>最大距离差</span>
              <Input
                v-model="formData.archivesConfig.maxHumanIMSIFittingDistance"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue($event, true)"
              >
              </Input>
              <div class="position">米</div>
              <!-- <Input v-model="value" placeholder="请输入" style="width: 150px" /> -->
            </div>
            <div class="childRight">
              <span>最大时间差</span>
              <Input
                v-model="formData.archivesConfig.maxHumanIMSIFittingTime"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue($event, true)"
              >
              </Input>
              <div class="position">秒</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">topN常去地</div>
          <div class="right">
            <Input
              v-model="formData.archivesConfig.frequentTop"
              type="number"
              placeholder="请输入"
              style="width: 100%"
              @on-keyup="verifiValue"
            ></Input>
          </div>
        </div>
        <template v-if="graphObj">
          <div class="row" v-for="(item, key) in archiveGraphConfig" :key="key">
            <div class="left">{{ item.label }}</div>
            <div class="right2">
              <div class="childLeft">
                <span>图谱名称</span>
                <Select
                  @on-change="graphInfoChange($event, 'archivesConfig', key)"
                  style="width: 150px"
                  v-model="formData.archivesConfig[key].graphId"
                >
                  <Option
                    v-for="cItem in graphInfoList"
                    :value="cItem.id"
                    :key="cItem.id"
                    >{{ cItem.name }}</Option
                  >
                </Select>
              </div>
              <div class="childRight">
                <span>实体类型</span>
                <Select
                  style="margin-left: 20px; width: 150px"
                  v-model="formData.archivesConfig[key].entityInfo.entityId"
                  @on-change="entityInfoChange($event, key)"
                >
                  <Option
                    v-for="cItem in item.entityList"
                    :value="cItem.id"
                    :key="cItem.id"
                    >{{ cItem.nameCn }}</Option
                  >
                </Select>
              </div>
            </div>
          </div>
        </template>
      </div>
      <div class="module" id="module11">
        <div class="title">
          <h2>语义布控</h2>
          <Button type="primary" @click="save(11)">保存</Button>
        </div>
        <div class="row aotoHeight">
          <div class="left">布控报警</div>
          <div class="right table">
            <alarmTable
              :type="'semantic'"
              :tableData="formData.semanticControl.llmAlarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
        <div class="row">
          <div class="left">算法管理</div>
          <div class="right2">
            <div class="childLeft">
              <!-- <span>最小布控相似度<i title="注：不能低于85" class="iconfont icon-tishi"></i></span> -->
              <span>提示词最大条数</span>
              <Input
                v-model="formData.semanticControl.maxCallWordNum"
                placeholder="请输入"
                style="width: auto"
                @on-blur="verifiValue($event, false, 100)"
              >
              </Input>
            </div>
          </div>
        </div>
        <!-- <div class="row">
          <div class="left">报警弹窗</div>
          <div class="right2">
            <div class="childLeft">
              <span>最小布控相似度<i title="注：不能低于85" class="iconfont icon-tishi"></i></span>
              <span>置信度≥</span>
              <Input
                v-model="formData.semanticControl.minSimilarity"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue($event, true, 100)"
              >
              </Input>
              <div class="position">%</div>
            </div>
          </div>
        </div> -->
      </div>
      <div class="module" id="module6">
        <div class="title">
          <h2>目标管控</h2>
          <Button type="primary" @click="save(7)">保存</Button>
        </div>
        <div class="row aotoHeight">
          <div class="left">人员报警</div>
          <div class="right table">
            <alarmTable
              type="people"
              :tableData="formData.control.alarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
        <div class="row aotoHeight">
          <div class="left">车辆报警</div>
          <div class="right table">
            <alarmTable
              type="archive"
              :tableData="formData.control.vehicleAlarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
        <div class="row">
          <div class="left">布控任务</div>
          <div class="right2">
            <div class="childLeft">
              <!-- <span>最小布控相似度<i title="注：不能低于85" class="iconfont icon-tishi"></i></span> -->
              <span>最小布控相似度</span>
              <Input
                v-model="formData.control.compareTaskConfig.minSimilarity"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue($event, true, 100)"
              >
              </Input>
              <div class="position">%</div>
            </div>
            <div class="childRight">
              <span>布控命中TOP</span>
              <Input
                type="number"
                v-model="formData.control.compareTaskConfig.defaultTopN"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue"
              >
              </Input>
              <!-- <div class="position">秒</div> -->
              <!-- <Input suffix="ios-search" placeholder="请输入" style="width: auto" /> -->
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module7">
        <div class="title">
          <h2>数智立方</h2>
          <Button type="primary" @click="save(4)">保存</Button>
        </div>
        <div class="row">
          <div class="left">我的图谱</div>
          <div class="right">
            <div>
              <span>最大创建数量</span>
            </div>
            <div class="btn">
              <Input
                v-model="formData.relationConfig.maxNumberOfCanvases"
                placeholder="请输入"
                style="width: auto"
                @on-keyup="verifiValue"
              />
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">所属图谱</div>
          <div class="right">
            <div>
              <span>图谱名称</span>
            </div>
            <div class="btn">
              <Select
                style="width: 150px"
                multiple
                v-model="formData.relationConfig.graphInfo"
              >
                <Option
                  v-for="item in graphInfoList"
                  :value="
                    JSON.stringify({
                      graphId: item.id,
                      name: item.name,
                      graphBusinessType: item.graphBusinessType,
                      instanceId: item.instanceId,
                    })
                  "
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left">融合图谱</div>
          <div class="right">
            <div>
              <span>图谱名称</span>
            </div>
            <div class="btn">
              <Select
                @on-change="
                  graphInfoChange($event, 'relationConfig', 'fusionGraphInfo')
                "
                style="width: 150px"
                v-model="formData.relationConfig.fusionGraphInfo.graphId"
              >
                <Option
                  v-for="item in fusionGraphInfoList"
                  :value="item.id"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module8">
        <div class="title">
          <h2>数据仓库</h2>
        </div>
        <div class="row">
          <div class="left">数仓概况</div>
          <div class="right justContent">
            <div class="dashed_line" @click="dataConfig('resourceType')">
              资源分类/{{ formData.resourceType.paramValue ? "已" : "未" }}配置
            </div>
            <div class="dashed_line" @click="dataConfig('dataResource')">
              数据来源/{{ formData.dataResource.paramValue ? "已" : "未" }}配置
            </div>
            <div class="dashed_line" @click="dataConfig('dataJob')">
              数据作业/{{ formData.dataJob.paramValue ? "已" : "未" }}配置
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module10">
        <div class="title">
          <h2>模型集市</h2>
          <Button type="primary" @click="save(10)">保存</Button>
        </div>
        <div class="row">
          <div class="left" style="width: 160px">人员频繁出没最大设备数</div>
          <div class="right">
            <Input
              v-model="formData.tacticalConfig.personFrequence.maxDeviceNumber"
              type="number"
              placeholder="请输入"
              style="width: 100%"
              @on-keyup="verifiValue"
            ></Input>
          </div>
        </div>
        <div class="people-case">
          <div class="left">由人到案</div>
          <div class="right">
            <div class="row">
              <div>
                <span>图谱名称</span>
              </div>
              <div class="btn">
                <Select
                  @on-change="
                    graphInfoChange(
                      $event,
                      'tacticalConfig.personToCase',
                      'graphInfo',
                      'personToCaseEntities'
                    )
                  "
                  style="width: 150px"
                  v-model="
                    formData.tacticalConfig.personToCase.graphInfo.graphId
                  "
                >
                  <Option
                    v-for="item in graphInfoList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
            <div class="row">
              <span>检索实体</span>
              <Select
                style="margin-left: 20px; width: 150px"
                multiple
                v-model="formData.tacticalConfig.personToCase.searchEntities"
              >
                <Option
                  v-for="cItem in entityListMap.personToCaseEntities"
                  :value="cItem.name"
                  :key="cItem.name"
                  >{{ cItem.nameCn }}</Option
                >
              </Select>
            </div>
            <div class="row">
              <span>人员档案实体</span>
              <Select
                style="margin-left: 20px; width: 150px"
                multiple
                v-model="formData.tacticalConfig.personToCase.personEntities"
              >
                <Option
                  v-for="cItem in entityListMap.personToCaseEntities"
                  :value="cItem.name"
                  :key="cItem.name"
                  >{{ cItem.nameCn }}</Option
                >
              </Select>
            </div>
          </div>
        </div>
      </div>
      <div class="module" id="module9">
        <div class="title">
          <h2>视频下载申请</h2>
          <Button type="primary" @click="save(9)">保存</Button>
        </div>
        <div class="row">
          <div class="left">申请通知</div>
          <div class="right justContent">
            <div class="dashed_line" @click="handleUser">
              {{
                userList.length == 0 ? `选择用户` : `已选(${userList.length})`
              }}
            </div>
          </div>
        </div>
      </div>
      <!-- <div style="height: 20px"></div> -->
    </div>
    <choose-center-map
      ref="centerMapShowRef"
      @getCenterPoint="getCenterPoint"
      :defaultCenterPoint="formData.globalConfig.mapCenterPoint"
    ></choose-center-map>
    <!-- <resource-type /> -->
    <component
      :is="modelName"
      :ref="modelName"
      @refresh="init"
      :data="dataObj"
    ></component>

    <!-- 选择用户 -->
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { getManageResourceList } from "@/api/dataGovernance";
import {
  getParamDataByKeys,
  paramUpdate,
  queryParamDataByKeys,
  pageList,
  queryGraph,
} from "@/api/config";
import { entityList } from "@/api/number-cube";
import chooseCenterMap from "./choose-center-map.vue";
import resourceType from "./components/resource-type.vue";
import dataResource from "./components/data-resource.vue";
import alarmTable from "./components/alarm-table.vue";
import dataJob from "./components/data-job.vue";
import draggable from "vuedraggable";
import SelectUser from "@/components/select-modal/select-user.vue";

const baseGraphInfo = {
  name: "",
  instanceId: "",
  graphId: null,
  graphBusinessType: "",
};

const defaultGraphInfo = {
  ...baseGraphInfo,
  entityInfo: {
    entityId: null,
    name: "",
    nameCn: "",
  },
};

export default {
  name: "ipbd-system",
  components: {
    chooseCenterMap,
    resourceType,
    dataResource,
    dataJob,
    draggable,
    alarmTable,
    SelectUser,
  },
  props: {},
  data() {
    return {
      userList: [],
      archiveGraphConfig: {
        realNameGraphInfo: { label: "实名档图谱", entityList: [] },
        videoGraphInfo: { label: "视频档图谱", entityList: [] },
        vehicleGraphInfo: { label: "车辆档图谱", entityList: [] },
        placeGraphInfo: { label: "场所档图谱", entityList: [] },
        deviceGraphInfo: { label: "设备档图谱", entityList: [] },
      },
      entityListMap: {
        personToCaseEntities: [],
      },
      formData: {
        // 语义布控
        semanticControl: {
          llmAlarmLevelConfig: [{}, {}, {}],
          // minSimilarity: 80,
          maxCallWordNum: 10,
        },
        control: {
          alarmLevelConfig: [],
          vehicleAlarmLevelConfig: [],
          compareTaskConfig: {},
        },
        globalConfig: {
          nameWatermarkSwitch: false,
          workCodeWatermarkSwitch: false,
          dayStartTime: "",
          dayEndTime: "",
        }, // 全局设置
        mapConfig: {}, // 图上作战
        archivesConfig: {
          // 全息档案
          timeInfo: [],
          realNameArcBackground: [], // 实名人档
          vehicleArcBackground: [], // 车辆档案
          realNameGraphInfo: { ...defaultGraphInfo },
          videoGraphInfo: { ...defaultGraphInfo },
          vehicleGraphInfo: { ...defaultGraphInfo },
          placeGraphInfo: { ...defaultGraphInfo },
          deviceGraphInfo: { ...defaultGraphInfo },
        },
        relationConfig: {
          graphInfo: { ...baseGraphInfo },
          fusionGraphInfo: { ...baseGraphInfo },
          maxNumberOfCanvases: "",
        }, // 数智立方
        searchConfig: {
          faceList: [],
          faceLibConfig: [],
        }, // 智慧云搜
        resourceType: {},
        dataResource: {},
        dataJob: {},
        collect: {
          collectValue: [],
          collectList: [],
        },
        videotValue: [],
        tacticalConfig: {
          personFrequence: {
            maxDeviceNumber: 0,
          },
          personToCase: {
            graphInfo: { ...baseGraphInfo },
            searchEntities: [],
            personEntities: [],
          },
        },
      },
      resourceList: [],
      modelName: "",
      dataObj: {},
      draglist: [],
      graphInfoList: [], // 图谱列表
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
    // 融合图谱列表
    fusionGraphInfoList() {
      return this.graphInfoList.filter(
        (item) => item.graphBusinessType === "01"
      );
    },
  },
  async created() {
    await this.getDictData();
    if (this.graphObj) {
      // 配置图谱才调用接口
      this.getGraphInData();
    }
    this.init();
    this.resourceListFn();
    this.workConfig();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
      getSystemAllData: "systemParam/getSystemAllData",
      setSum: "countCoverage/setSum",
    }),
    workConfig() {
      let params = {
        dataKey: "",
        dataValue: "",
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        typeKey: "ipbd_my_favorite_type",
      };
      pageList(params).then((res) => {
        // 暂时隐藏人口库，目前人员静态库没有收藏，对应dataKey为13
        this.formData.collect.collectList = res.data.entities.filter(
          (v) => v.dataKey !== "13"
        );
      });
    },
    init() {
      var param = [
        "ICBD_GLOBAL_CONFIG",
        "ICBD_MAP_CONFIG",
        "ICBD_ARCHIVES_CONFIG",
        "ICBD_RELATION_CONFIG",
        "ICBD_SEARCH_CONFIG",
        "ICBD_RESOURCE_CATALOG_CONFIG",
        "ICBD_TABLE_DATASOURCES_CONFIG",
        "ICBD_TASK_BUSINESS_CONFIG",
        "ICBD_WORKBENCH_CONFIG",
        "ICBD_TARGET_CONTROL",
        "ICBD_VIDEOAPPLY_CONFIG",
        "ICBD_TACTICAL_CONFIG",
        "ICBD_LLM_TASK_CONFIG", // 语义布控
      ];
      queryParamDataByKeys(param).then((res) => {
        var list = res.data;
        list.forEach((item) => {
          switch (item.paramKey) {
            case "ICBD_GLOBAL_CONFIG":
              this.formData.globalConfig = JSON.parse(item.paramValue);
              break;
            case "ICBD_MAP_CONFIG":
              this.formData.mapConfig = JSON.parse(item.paramValue);
              break;
            case "ICBD_ARCHIVES_CONFIG":
              this.formData.archivesConfig = JSON.parse(item.paramValue);

              // 实名档案图谱
              if (!this.formData.archivesConfig.realNameGraphInfo) {
                this.formData.archivesConfig.realNameGraphInfo = {
                  ...defaultGraphInfo,
                };
              } else {
                // 请求当前图谱下的实体列表，用于回显
                this.getEntityList(
                  this.formData.archivesConfig.realNameGraphInfo.graphId,
                  "realNameGraphInfo"
                );
              }
              // 视频档案图谱
              if (!this.formData.archivesConfig.videoGraphInfo) {
                this.formData.archivesConfig.videoGraphInfo = {
                  ...defaultGraphInfo,
                };
              } else {
                this.getEntityList(
                  this.formData.archivesConfig.videoGraphInfo.graphId,
                  "videoGraphInfo"
                );
              }
              // 车辆档案图谱
              if (!this.formData.archivesConfig.vehicleGraphInfo) {
                this.formData.archivesConfig.vehicleGraphInfo = {
                  ...defaultGraphInfo,
                };
              } else {
                this.getEntityList(
                  this.formData.archivesConfig.vehicleGraphInfo.graphId,
                  "vehicleGraphInfo"
                );
              }
              // 场所档案图谱
              if (!this.formData.archivesConfig.placeGraphInfo) {
                this.formData.archivesConfig.placeGraphInfo = {
                  ...defaultGraphInfo,
                };
              } else {
                this.getEntityList(
                  this.formData.archivesConfig.placeGraphInfo.graphId,
                  "placeGraphInfo"
                );
              }
              // 设备档案图谱
              if (!this.formData.archivesConfig.deviceGraphInfo) {
                this.formData.archivesConfig.deviceGraphInfo = {
                  ...defaultGraphInfo,
                };
              } else {
                this.getEntityList(
                  this.formData.archivesConfig.deviceGraphInfo.graphId,
                  "deviceGraphInfo"
                );
              }
              break;
            case "ICBD_RELATION_CONFIG":
              this.formData.relationConfig = JSON.parse(item.paramValue);
              if (this.formData.relationConfig.graphInfo?.length > 0) {
                this.formData.relationConfig.graphInfo =
                  this.formData.relationConfig.graphInfo.map((item) =>
                    JSON.stringify(item)
                  );
              }
              if (!this.formData.relationConfig.fusionGraphInfo) {
                this.formData.relationConfig.fusionGraphInfo = {
                  ...baseGraphInfo,
                };
              }
              break;
            case "ICBD_SEARCH_CONFIG":
              this.formData.searchConfig = JSON.parse(item.paramValue);
              break;
            case "ICBD_RESOURCE_CATALOG_CONFIG":
              this.formData.resourceType = item;
              break;
            case "ICBD_TABLE_DATASOURCES_CONFIG":
              this.formData.dataResource = item;
              break;
            case "ICBD_TASK_BUSINESS_CONFIG":
              this.formData.dataJob = item;
              break;
            case "ICBD_WORKBENCH_CONFIG":
              this.formData.collect.collectValue = JSON.parse(item.paramValue);
              break;
            case "ICBD_TARGET_CONTROL":
              this.$set(this.formData, "control", JSON.parse(item.paramValue));
              // this.formData.control.compareTaskConfig.minSimilarity = this.formData.control.compareTaskConfig.minSimilarity*100
              // this.formData.control = JSON.parse(item.paramValue);
              if (!this.formData.control.alarmLevelConfig) {
                this.$set(this.formData.control, "alarmLevelConfig", [
                  {},
                  {},
                  {},
                ]);
              }
              if (!this.formData.control.vehicleAlarmLevelConfig) {
                this.$set(this.formData.control, "vehicleAlarmLevelConfig", [
                  {},
                  {},
                  {},
                ]);
              }
              break;
            case "ICBD_VIDEOAPPLY_CONFIG":
              this.formData.videotValue = JSON.parse(item.paramValue);
              this.userList = JSON.parse(item.paramValue);
              break;
            case "ICBD_TACTICAL_CONFIG":
              this.formData.tacticalConfig = {
                ...this.formData.tacticalConfig,
                ...JSON.parse(item.paramValue || "{}"),
              };
              const graphId =
                this.formData.tacticalConfig?.personToCase?.graphInfo?.graphId;
              graphId && this.getEntityMapList(graphId, "personToCaseEntities");
              break;
            case "ICBD_LLM_TASK_CONFIG": {
              this.$set(
                this.formData,
                "semanticControl",
                JSON.parse(item?.paramValue || "{}")
              );
              if (!this.formData.semanticControl.llmAlarmLevelConfig) {
                this.$set(
                  this.formData.semanticControl,
                  "llmAlarmLevelConfig",
                  [{}, {}, {}]
                );
              }
            }
            default:
              break;
          }
        });
      });
    },
    /**
     * 获取已添加管理资源
     */
    resourceListFn() {
      var param = {
        // catalogId: ["20", "3"]
      };
      getManageResourceList(param).then((res) => {
        this.resourceList = res.data;
      });
    },

    colorRefrsh(index, name, type) {
      switch (type) {
        case "people":
          this.formData.control.alarmLevelConfig[index].alarmColour = name;
          break;
        case "archive":
          this.formData.control.vehicleAlarmLevelConfig[index].alarmColour =
            name;
          break;
        case "semantic":
          this.formData.semanticControl.llmAlarmLevelConfig[index].alarmColour =
            name;
          break;
        default:
          break;
      }
      this.$forceUpdate();
    },

    /**
     * @description: 校验输入的内容
     * @param {object} e 输入的值
     * @param {boolean} decimalFlag 是否可以小数
     * @param {number} maxValue 校验的最大值
     * @param {number} minValue 校验的最小值
     */
    verifiValue(e, decimalFlag = false, maxValue, minValue = 0) {
      // 不接受负数
      e.target.value = e.target.value.replaceAll("-", "");
      // 只接受数字
      if (decimalFlag) {
        e.target.value = e.target.value.replace(/[^\d\.]/g, "");
      } else {
        e.target.value = e.target.value.replace(/[^\d]/g, "");
      }
      if (minValue === 0 || minValue) {
        if (e.target.value < minValue) {
          this.$Message.warning("最小值为" + minValue);
          e.target.value = minValue;
        }
      }
      if (maxValue) {
        if (e.target.value > maxValue) {
          this.$Message.warning("最大值为" + maxValue);
          e.target.value = maxValue;
        }
      }
    },

    save(val) {
      let showInfo = this.formData.globalConfig.showInfo;
      if (val == 1 && showInfo && showInfo.length > 0) {
        //全局设置
        if (this.formData.globalConfig.showInfo.includes("姓名")) {
          this.formData.globalConfig.nameWatermarkSwitch = true;
        } else {
          this.formData.globalConfig.nameWatermarkSwitch = false;
        }
        if (this.formData.globalConfig.showInfo.includes("警号")) {
          this.formData.globalConfig.workCodeWatermarkSwitch = true;
        } else {
          this.formData.globalConfig.workCodeWatermarkSwitch = false;
        }
      }
      if (val == 3) {
        //全息档案
        if (this.formData.globalConfig.timeInfo.length > 0) {
          this.formData.globalConfig.dayStartTime =
            this.formData.globalConfig.timeInfo[0];
          this.formData.globalConfig.dayEndTime =
            this.formData.globalConfig.timeInfo[1];
        }
      }
      if (val == 5) {
        //智慧云搜
        if (this.formData.searchConfig.faceList.length > 0) {
          this.formData.searchConfig.faceLibConfig.forEach((item) => {
            if (this.formData.searchConfig.faceList.includes(item.field_name)) {
              item.cloud_search = 1;
            } else {
              item.cloud_search = 0;
            }
          });
        } else {
          this.formData.searchConfig.faceLibConfig.forEach((item) => {
            item.cloud_search = 0;
          });
        }
      }
      if (val == 10) {
        if (
          !this.formData.tacticalConfig.personFrequence.maxDeviceNumber &&
          this.formData.tacticalConfig.personFrequence.maxDeviceNumber < 1
        ) {
          this.$Message.warning("最大设备数不能小于0");
          return;
        }
      }
      // if (val == 7) {
      //   this.formData.control.alarmLevelConfig.forEach(item => {
      //     item.defaultUrl = "http://192.168.1.124:19002/qsdi/2023/04/07/0c2e1f9f5541400c954ec1a0077c6e35.mp3"
      //   })
      // }
      var paramKey = "";
      var paramValue = "";
      var paramType = "icbd";
      switch (val) {
        case 1:
          paramKey = "ICBD_GLOBAL_CONFIG";
          paramValue = JSON.stringify(this.formData.globalConfig);
          break;
        case 2:
          paramKey = "ICBD_MAP_CONFIG";
          paramValue = JSON.stringify(this.formData.mapConfig);
          break;
        case 3:
          paramKey = "ICBD_ARCHIVES_CONFIG";
          paramValue = JSON.stringify(this.formData.archivesConfig);
          break;
        case 4:
          paramKey = "ICBD_RELATION_CONFIG";
          const graphInfo =
            this.formData.relationConfig.graphInfo?.map((item) =>
              JSON.parse(item)
            ) || [];
          paramValue = JSON.stringify({
            ...this.formData.relationConfig,
            graphInfo,
          });
          break;
        case 5:
          paramKey = "ICBD_SEARCH_CONFIG";
          paramValue = JSON.stringify(this.formData.searchConfig);
          break;
        case 6:
          paramKey = "ICBD_WORKBENCH_CONFIG";
          paramValue = JSON.stringify(this.formData.collect.collectValue);
          break;
        case 7:
          paramKey = "ICBD_TARGET_CONTROL";
          // this.formData.control.compareTaskConfig.minSimilarity = this.formData.control.compareTaskConfig.minSimilarity/100
          // if (Number(this.formData.control.compareTaskConfig.minSimilarity) && this.formData.control.compareTaskConfig.minSimilarity < 85) {
          // 	this.$Message.warning('最小布控相似度不能低于85')
          // 	return
          // }
          paramValue = JSON.stringify(this.formData.control);
          break;
        case 9:
          paramKey = "ICBD_VIDEOAPPLY_CONFIG";

          let arr = [];
          this.userList.forEach((item) => {
            // arr.push(String(item.id))
            arr.push({
              id: String(item.id),
              username: item.username,
            });
          });
          paramValue = JSON.stringify(arr);
          break;
        case 10:
          paramKey = "ICBD_TACTICAL_CONFIG";
          paramValue = JSON.stringify(this.formData.tacticalConfig);
          paramType = "icbd";
          break;
        case 11:
          paramKey = "ICBD_LLM_TASK_CONFIG";
          paramValue = JSON.stringify(this.formData.semanticControl);
          paramType = "icbd";
          break;
        default:
          break;
      }
      var param = {
        paramKey: paramKey,
        paramType: paramType,
        paramValue: paramValue,
      };
      paramUpdate(param).then((res) => {
        this.$Message.success("修改成功");
        if (val == 2) {
          this.setSum();
        }
        this.getSystemAllData(1);
      });
    },
    showChooseMap() {
      this.$refs.centerMapShowRef.init();
    },
    getCenterPoint(centerPoint) {
      this.formData.globalConfig.mapCenterPoint = centerPoint;
    },
    /**
     * 数据仓库配置
     */
    dataConfig(val) {
      this.modelName = val;
      this.$nextTick(() => {
        this.$refs[val].show(this.formData[val]);
      });
    },
    // 工作台数据处理
    handleChangeCollect(value) {
      this.draglist = value;
    },
    handleSeleCollect(value) {
      console.log(value);
    },
    // 删除收藏tab
    handleDeleTab(index) {
      this.draglist.splice(index, 1);
      this.formData.collect.collectValue = this.draglist.map(
        (item) => item.value
      );
    },
    handleChangeDrag() {
      this.formData.collect.collectValue = this.draglist.map(
        (item) => item.value
      );
    },
    // 选择用户
    handleUser() {
      this.userList.forEach((item) => {
        item.select = true;
      });
      this.$refs.selectUser.show(this.userList);
    },
    // 通知用户
    handleUserData(list) {
      this.userList = list;
    },
    getGraphInData() {
      queryGraph({ connectStatus: 1 }).then((res) => {
        res.data.forEach((item) => {
          this.graphInfoList.push({
            name: item.name,
            instanceId: item.instanceId,
            id: item.id,
            graphBusinessType: item.graphBusinessType, // 01 - 融合图谱
          });
        });
      });
    },

    /**
     * @description: 切换选中的图谱
     * @param {number} graphId 图谱id
     * @param {string} configType 一级配置名称
     * @param {string} subConfigType 二级配置名称
     */
    graphInfoChange(graphId, configType, subConfigType, entityName) {
      if (subConfigType !== "graphInfo") {
        // 切换图谱时，先清空实体的信息
        this.formData[configType][subConfigType].entityInfo = {
          entityId: null,
          name: "",
          nameCn: "",
        };
        // 如果subConfigType不是graphInfo，则需要请求实体
        this.getEntityList(graphId, subConfigType);
      }
      if (entityName) {
        this.getEntityMapList(graphId, entityName);
      }
      const obj = this.getObjectNames(this.formData, configType);

      if (typeof graphId !== "object") {
        let _info =
          this.graphInfoList.find((item) => item.id === graphId) || {};
        obj[subConfigType].instanceId = _info.instanceId;
        obj[subConfigType].graphBusinessType = _info.graphBusinessType;
        obj[subConfigType].name = _info.name;
      } else {
        let _infos =
          this.graphInfoList.filter((item) => graphId.includes(item.id)) || {};
        const instanceId = [];
        const graphBusinessType = [];
        const name = [];
        _infos.forEach((_info) => {
          instanceId.push(_info.instanceId);
          graphBusinessType.push(_info.graphBusinessType);
          name.push(_info.name);
        });
        obj[subConfigType].instanceId = instanceId;
        obj[subConfigType].graphBusinessType = graphBusinessType;
        obj[subConfigType].name = name;
      }
    },
    getObjectNames(data, keys) {
      const names = keys.split(".");
      let obj = data;
      names.forEach((name) => {
        obj = obj[name];
      });
      return obj;
    },
    /**
     * @description: 切换选中的实体
     * @param {number} entityId 实体id
     * @param {string} configType 配置名称，因只有全息档案配置需要实体，所以这里只传一层配置名称即可
     */
    entityInfoChange(entityId, configType) {
      let { id, name, nameCn, primaryKeys } = this.archiveGraphConfig[
        configType
      ].entityList.find((item) => item.id === entityId);
      this.formData.archivesConfig[configType].entityInfo = {
        entityId: id,
        name,
        nameCn,
        primaryKeys,
      };
    },

    /**
     * @description: 获取选中图谱下的实体列表
     * @param {number} graphId 选中的图谱id
     * @param {string} configType 配置名称
     */
    getEntityList(graphId, configType) {
      entityList({ graphId }).then((res) => {
        this.archiveGraphConfig[configType].entityList = res.data || [];
      });
    },
    /**
     * @description: 获取选中图谱下的实体列表
     * @param {number} graphId 选中的图谱id
     * @param {string} configType entityListMap下实体数组名称
     */
    getEntityMapList(graphId, entityName) {
      entityList({ graphId }).then((res) => {
        this.entityListMap[entityName] = res.data || [];
      });
    },
  },
};
</script>
<style lang="less" scoped>
.layout {
  position: relative;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  // overflow-y: auto;
  padding-bottom: 20px;
  .button {
    position: fixed;
    right: 30px;
    top: 110px;
    cursor: pointer;
  }
  .content {
    width: calc(~"50% + 64px");
    overflow: auto;
    scrollbar-width: none;
    .module {
      padding: 0 32px 20px;
      margin-top: 35px;
      border-bottom: 1px dashed #d3d7de;
      .title {
        display: flex;
        justify-content: space-between;
      }
      .row {
        display: flex;
        justify-content: start;
        font-size: 14px;
        height: 50px;
        line-height: 50px;
        margin-top: 20px;
        .left {
          min-width: 80px;
        }
        .right {
          flex: 1;
          background: #f9f9f9;
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .span {
            float: left;
            margin-right: 20px;
            display: inline-block;
          }
          .ivu-checkbox-group {
            float: left;
          }
          .ivu-select {
            flex: 1;
          }
          .btn {
            position: relative;
            display: flex;
            align-items: center;
            .position {
              position: absolute;
              right: 10px;
            }
          }
        }
        .justContent {
          justify-content: space-around;
        }
        .right2 {
          flex: 1;
          display: flex;
          justify-content: space-between;
          .childLeft {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .position {
              position: absolute;
              right: 20px;
            }
          }
          .childRight {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            .position {
              position: absolute;
              right: 20px;
            }
          }
        }
        .dashed_line {
          border: 1px dashed #3c8bef;
          height: 36px;
          line-height: 36px;
          padding: 0 10px;
          border-radius: 6px;
          color: #2c86f8;
          cursor: pointer;
          background: #ebf4ff;
        }
      }
      .aotoHeight {
        height: auto;
        .right {
          padding: 0;
        }
      }
    }
    .module:last-of-type {
      border: none;
      padding-bottom: 40px;
    }
  }
}
.content::-webkit-scrollbar {
  display: none; /* Chrome Safari */
}
.preview {
  position: relative;
  width: 100%;
  .seleList {
    position: absolute;
    z-index: 9;
    display: inline-block;
    font-size: 12px;
    line-height: 22px;
    // pointer-events: none;
    margin-left: 5px;
    top: 50%;
    transform: translate(0, -50%);
    margin-right: 30px;
    .seletabs {
      border: 1px solid #e8eaec;
      background: #f7f7f7;
      padding: 0 6px;
      display: inline-block;
      font-size: 14px;
      border-radius: 5px;
      margin: 3px 4px 3px 0;
      position: relative;
      cursor: move;
      span {
        margin-right: 14px;
      }
      .icon-close {
        cursor: pointer;
        color: #66666675;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 2px;
      }
    }
  }
}
/deep/ .ivu-input-group-append {
  width: 30px;
}

/deep/ .ivu-input {
  width: 150px;
}
.people-case {
  display: flex;
  margin-top: 10px;
  .left {
    align-self: center;
  }
  .right {
    margin-left: 20px;
    flex: 1;
  }
  .row {
    width: 100%;
    margin-top: 0px !important;
    justify-content: space-between !important;
    background: #f9f9f9;
    padding: 0 10px;
  }
}
.table {
  width: 835px;
  /deep/ .ivu-input {
    width: 120px;
  }
}
.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}
/deep/ .ivu-select-item-disabled {
  color: #c5c8ce;
}
/deep/ .icon-tishi {
  color: #f29f4c;
}

.anchor-point-infomation {
  width: 100px;
  position: fixed;
  top: 78px;
  right: 18px;
  z-index: 9;
  .export-btn {
    margin-bottom: 10px;
  }
}

.anchor {
  width: 120px;
  position: absolute;
  right: 17%;
  margin-top: 30px;
  /deep/ .ivu-anchor-wrapper {
    box-shadow: 0 0 !important;
  }
}
h2 {
  color: #000;
  font-size: 24px;
}
</style>
