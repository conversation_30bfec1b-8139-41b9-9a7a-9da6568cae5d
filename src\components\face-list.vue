<template>
  <div class="face-list">
    <ul class="face-list-ul" :style="{ height: height }">
      <li
        class="face-list-ul-li f-12 mb-sm"
        v-for="(item, index) in faceList"
        :key="index"
        @click.stop="viewBigPic(item)"
      >
        <div class="image-box">
          <ui-image :src="item.facePath" class="image pointer" />
          <!-- <img v-for="src in images" :src="src" :key="src" class="image"> -->
          <p class="shadow-box" v-if="hasHover" title="查看检测结果">
            <i
              class="icon-font icon-yichang search-icon mr-xs base-text-color"
              @click.stop="$emit('uploadTips', item)"
            ></i>
          </p>
          <!-- <p class="tip-box">保留</p> -->
        </div>
        <p class="message-p">
          <i class="icon-font icon-shijian mr-xs f-14"></i>
          <span>{{ item.logTime || '未知' }}</span>
        </p>
        <p class="message-p ellipsis" :title="item.deviceName">
          <i class="icon-font icon-dizhi mr-xs f-14"></i>
          <span class="address-span" :title="item.deviceName">{{ item.deviceName || '未知' }}</span>
          <!-- <span>南山区粤海街道东3巷6号sadfsafsadsadsasadsdasadsasad...</span> -->
        </p>
      </li>
    </ul>
    <div class="no-data" v-if="!faceList.length">
      <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt />
      <img v-else src="@/assets/img/common/nodata-light.png" alt />
    </div>
    <loading v-if="faceLoading"></loading>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
  </div>
</template>
<script>
import LookScene from './look-scene.vue';
import { mapGetters } from 'vuex';
export default {
  props: {
    faceList: {
      type: Array,
    },
    height: {
      default: '2.8rem',
    },
    hasHover: {
      default: true,
    },
    faceLoading: {
      default: false,
    },
  },
  data() {
    return {
      images: [require('@/assets/img/navigation-page/systemmanagement.png')],
      bigPictureShow: false,
      imgList: [],
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  created() {},
  methods: {
    viewBigPic(item) {
      if (!item.scenePath) {
        this.$Message.warning('大图URL缺失/大图URL不可访问');
        return;
      }
      this.imgList = [item.scenePath];
      this.bigPictureShow = true;
    },
  },
  watch: {},
  components: { LookScene },
};
</script>
<style lang="less" scoped>
.face-list {
  .face-list-ul {
    //display: flex;
    //flex-wrap: wrap;
    overflow-y: scroll;
    .face-list-ul-li {
      padding: 10px;
      background: #0f2f59;
      color: #8797ac;
      margin-right: 10px;
      height: 236px;
      float: left;
      .image-box {
        width: 167px;
        height: 167px;
        position: relative;
        > img {
          width: 100%;
          height: 100%;
        }
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          z-index: 10;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
        .tip-box {
          position: absolute;
          right: 0px;
          top: 0px;
          padding: 2px 4px;
          background: #2cabc6;
          color: #fff;
        }
      }
    }
  }
}
.message-p {
  height: 30px;
  line-height: 30px;
  width: 150px;
  display: flex;
  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.no-data {
  > img {
    width: 500px !important;
    height: 250px !important;
  }
}
</style>
