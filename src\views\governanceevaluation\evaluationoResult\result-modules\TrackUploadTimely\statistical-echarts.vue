<template>
  <!--实时轨迹上传及时性 -->
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics :listyle="listyle"></index-statistics>
      </div>
      <div class="unqualified">
        <unqualified-reason></unqualified-reason>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem">
          <template #rank-title>
            <span>{{ sortText }}</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart-tooltip-formatter
        class="line-chart"
        :active-index-item="activeIndexItem"
        :tooltip-formatter="tooltipFormatter"
      ></line-chart-tooltip-formatter>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statistical-echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    UnqualifiedReason: require('@/views/governanceevaluation/evaluationoResult/components/unqualified-reason.vue')
      .default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChartTooltipFormatter: require('../../components/line-chart-tooltip-formatter.vue').default,
  },
  data() {
    return {
      listyle: {
        height: '0.53rem',
        width: `calc((100% - ${30 / 192}rem) / 3)`,
      },
    };
  },
  props: {
    activeIndexItem: {},
    sortText: {
      type: String,
      default: '按合规率排序',
    },
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    tooltipFormatter(data) {
      let str = '';
      data.forEach((item) => {
        str = `<p>${item.data.startTime}</p>
               <p><span class="mr-md">${this.activeIndexItem.indexName}</span>${item.data.vertical || 0}%</p>
               <p><span class="mr-md">检测轨迹数量</span>${item.data.actualNum || 0}</p>
               <p><span class="mr-md">合格轨迹</span>${item.data.qualifiedNum || 0}</p>
               <p><span class="mr-md">不合格轨迹</span>${item.data.unqualifiedNum || 0}</p>`;
      });
      return str;
    },
  },
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: calc(100% - 488px);
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
