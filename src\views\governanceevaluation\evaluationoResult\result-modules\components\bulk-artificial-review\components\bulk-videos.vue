<template>
  <div class="player-list" ref="playListRef">
    <div class="player-list-item" v-for="(item, index) in playingList" :key="index" :style="activeStyle">
      <!-- 截图模式切换 -->
      <template v-if="isScreenAndVideo">
        <span
          class="player-list-item_button pointer"
          :title="item.type === 'screenShot' ? '播放视频' : '查看截图'"
          @click="changeMode(item)"
        >
          {{ item.type === 'screenShot' ? '播放视频' : '查看截图' }}
        </span>
        <ui-image :src="item?.screenShot ?? ''" class="image-box" v-show="item.type === 'screenShot'" />
      </template>
      <EasyPlayer
        class="easy-player easy-player-reset-height"
        v-if="item.type === 'video' || !isScreenAndVideo"
        :videoUrl="item.videoUrl"
        ref="easyPlay"
        :key="index"
      >
      </EasyPlayer>

      <div v-if="showReviewBox" class="radio-box">
        <div class="mt-sm new-status-box">
          <span class="base-text-color inline mr-lg vt-middle"
            >最新状态：<span :class="[getNewStatus(item.qualified).className]"
              >{{ getNewStatus(item.qualified).text }} {{ item.dataMode === '3' ? `(人工)` : '' }}</span
            ></span
          >
          <span
            class="inline mr-lg vt-middle ellipsis new-status-box-item-top"
            :class="[getNewStatus(item.qualified).className]"
            :title="item.reason"
            >备注：{{ item.reason }}</span
          >
        </div>
        <div class="review-info-box">
          <span class="base-text-color mr-xs">人工复核：</span>
          <RadioGroup v-model="reviewVideoObj[item.id].qualified">
            <Radio v-for="item in qualifiedList" :key="item.value" class="mr-sm" :label="item.value">
              {{ item.key }}
            </Radio>
          </RadioGroup>
        </div>
        <Input
          type="textarea"
          class="desc"
          v-model="reviewVideoObj[item.id].reason"
          placeholder="请输入备注信息"
          :rows="2"
          :maxlength="100"
        >
        </Input>
      </div>
    </div>
  </div>
</template>

<script>
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-accuracy',
  props: {
    visible: {},
    deviceList: {
      default: () => [],
    },
    showReviewBox: {
      type: Boolean,
      default: false,
    },
    // true 截图和视频 / false 仅仅视频模式
    isScreenAndVideo: {
      default: false,
    },
    // 获取视频的接口url
    getVideoUrl: {
      default: vedio.getplay,
    },
    // 获取视频的接口的参数函数回调处理
    getVideoParamsFunc: {
      type: Function,
      default: (row) => {
        return { deviceId: row.deviceId };
      },
    },
    qualifiedList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      type: 'screenShot', // video 切换截图模式和视频模式
      videoUrl: '',
      playDeviceCode: '',
      playingList: [],
      videoVisible: false,
      videoLoading: false,
      reviewVideoObj: {}, // 视频列表 对应的 人工复核的信息
    };
  },
  methods: {
    //视频播放
    async clickRow(row) {
      try {
        let data = {
          deviceId: row.deviceId,
          startTime: row.playStartTime,
          endTime: row.playEndTime,
          // pullLevel: 3,
          // urlEncryption: true
        };
        // 重点| 普通 历史视频可调阅率
        let indexTypeArr = ['VIDEO_HISTORY_ACCURACY', 'VIDEO_GENERAL_HISTORY_ACCURACY'];
        if (indexTypeArr.includes(this.$route.query.indexType)) {
          await this.$http.post(vedio.record, data);
        }
        let params = this.getVideoParamsFunc(row);
        let res = await this.$http.post(this.getVideoUrl, params);
        row.videoUrl = res.data.data.hls;
        //row.playDeviceCode = row.deviceId
      } catch (err) {
        console.log(err);
      }
    },
    // 清除视频流
    async clearVideo(playDeviceCode) {
      try {
        await this.$http.post(vedio.stop + playDeviceCode);
      } catch (err) {
        console.log(err);
      }
    },
    changeMode(row) {
      if (row.type === 'screenShot') {
        row.type = 'video';
        this.clickRow(row);
      } else {
        row.type = 'screenShot';
      }
    },
  },
  computed: {
    activeStyle() {
      // let allWidth = this.$refs.playListRef.offsetWidth;
      // let allHeight = this.$refs.playListRef.offsetHeight;
      let total = this.deviceList?.length ?? 0; // 总共条数
      let rowNum = 1; // 几行
      let style = {
        width: `calc(100% / ${total})`,
        height: `calc(100% / ${total})`,
      };
      switch (total) {
        // 1、2个视频：1行展示
        case 1:
        case 2:
          rowNum = 1;
          style.width = `calc(100% / ${total})`;
          break;
        // 3、4个视频：2行展示
        case 3:
        case 4:
          rowNum = 2;
          style.width = 'calc(100% / 2)';
          break;
        case 5:
        case 6:
        case 7:
        case 8:
          rowNum = 2;
          style.width = 'calc(100% / 4)';
          break;
        case 9:
        case 10:
        case 11:
        case 12:
          rowNum = 3;
          style.width = 'calc(100% / 4)';
          break;
        case 13:
        case 14:
        case 15:
        case 16:
          rowNum = 4;
          style.width = 'calc(100% / 4)';
          break;
        default:
      }
      style.height = `calc(100% / ${rowNum})`;
      return {
        width: `${style.width}`,
        height: `${style.height}`,
      };
    },
    getAttrs() {
      return {
        ...this.$attrs,
      };
    },
    // 最新状态
    getNewStatus() {
      return (str) => {
        return {
          text: str == '1' ? '合格' : '不合格',
          className: str == '1' ? 'c-green' : 'c-red',
        };
      };
    },
  },
  watch: {
    deviceList: {
      handler(val, oldArr) {
        if (oldArr?.length) {
          // 重点| 普通 历史视频可调阅率  关闭窗口时，需要调用
          let indexTypeArr = [
            'VIDEO_HISTORY_ACCURACY',
            'VIDEO_GENERAL_HISTORY_ACCURACY',
            'VIDEO_GENERAL_PLAYING_ACCURACY',
            'VIDEO_PLAYING_ACCURACY',
          ];
          if (indexTypeArr.includes(this.$route.query.indexType)) {
            oldArr.forEach((item) => {
              item.deviceId ? this.clearVideo(item.deviceId) : '';
            });
          }
        }
        if (!val?.length) return;
        this.reviewVideoObj = {};
        // 先给一波空数据放上
        this.playingList = val.map((item) => {
          this.$set(this.reviewVideoObj, item.id, {});
          this.$set(this.reviewVideoObj[item.id], 'qualified', '');
          this.$set(this.reviewVideoObj[item.id], 'reason', '');
          return {
            ...item,
            playStartTime: item.playStartTime,
            playEndTime: item.playEndTime,
            deviceId: item.deviceId,
            videoUrl: '',
            playDeviceCode: item.deviceId,
            qualified: item.qualified,
            reason: item.reason,
            screenShot: item?.screenShot,
            type: 'screenShot',
          };
        });
        // 可切换模式：先展示截图
        if (this.isScreenAndVideo) return;
        // 加载批量视频
        this.playingList.forEach((item) => {
          this.clickRow(item);
        });
      },
      immediate: true,
    },
    visible: {
      handler(val) {
        if (!val) {
          // 清除所有上一批的视频流
          this.playingList.forEach((item) => {
            !!item?.playDeviceCode && !!item.videoUrl ? this.clearVideo(item.playDeviceCode) : null;
          });
        }
      },
    },
  },
  components: {
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style lang="less" scoped>
.player-list {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  height: 100%;
  overflow-y: auto;
  .player-list-item {
    display: flex;
    flex-direction: column;
    padding: 5px;
    position: relative;
    .player-list-item_button {
      position: absolute;
      top: 20px;
      left: 20px;
      color: var(--color-primary);
      z-index: 11;
    }
  }
  @{_deep}.easy-player,
  .image-box {
    flex: 1;
    margin-top: 0px !important;
  }
  .image-box {
    max-height: 275px;
    background: #000 !important;
    overflow: hidden;
  }
  .radio-box {
    height: 130px;
    line-height: 30px;
  }
}
// 适配下 海南现场-批量复核时，由于浏览器版本低，有样式不兼容，导致样式不生效，出现高度问题  其中130为radio-box中设置的高度
.easy-player.easy-player-reset-height {
  height: calc(100% - 130px) !important;
  @{_deep}.easy-player {
    height: 100% !important;
  }
}
.new-status-box {
  display: flex;
  .new-status-box-item-top {
    max-width: calc(100% - 180px);
  }
}
.review-info-box {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
</style>
