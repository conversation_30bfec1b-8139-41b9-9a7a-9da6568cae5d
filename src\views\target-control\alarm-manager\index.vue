<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-24 14:46:06
 * @Description: 
-->
<template>
  <div class="layout">
    <tabsPage
      v-model="selectLi"
      :list="alarmList"
      v-show="!$route.query.noSearch"
    />
    <keep-alive>
      <component
        :is="componentName"
        :key="selectLi"
        :compareType="selectLi"
        :isCloudSearch="isCloudSearch"
        :keyWords="keyWords"
      />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import people from "./people/index.vue";
import vehicle from "./vehicle/index.vue";
import sensory from "./sensory/index.vue";
export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    people,
    vehicle,
    sensory,
  },
  data() {
    this.alarmList = Object.freeze([
      { label: "人员报警", value: 1 },
      { label: "车辆报警", value: 2 },
      { label: "WIFI报警", value: 3 },
      { label: "RFID报警", value: 5 },
      { label: "电围报警", value: 4 },
      { label: "ETC报警", value: 6 },
    ]);
    return {
      selectLi: 1,
      isCloudSearch: false, // 是否全景智叟进入
      keyWords: "", // 全景智叟搜索关键词
    };
  },
  computed: {
    componentName() {
      if (1 === this.selectLi) {
        return "people";
      }
      if (2 === this.selectLi) {
        return "vehicle";
      }
      return "sensory";
    },
  },
  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
    // 全景智叟更多进入
    if (this.$route.query.isCloudSearch) {
      this.isCloudSearch = true;
      this.keyWords = this.$route.query.keyWords;
    }
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
