<template>
  <div class='traffic-offence-content card-box mr-10'>
    <div class='header'>
      <div class='icon-wrapper bg-primary'>
        <span class='iconfont icon-qiche f-14 '></span>
      </div>
      <ui-plate-number :plateNo="plateNo" ></ui-plate-number>
    </div>
    <div class='content-wrapper'>
      <div v-for='(item, index) in dataColumn' :key='index' class='ellipsis'>
        <span class='f-14 label'>{{`${item.label}：`}}</span>
        <span class='f-14' :title='data[item.key]'>{{data[item.key]}}</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'traffic-offence-detail',
  props: {
    data: {},
    column: {},
    plateNo: {}
  },
  mixins: [],
  data() {
    return {
      dataDetail: {},
      dataColumn: []
    };
  },
  computed: {},
  created() {
  },
  mounted() {
  },
  watch: {
    data:{
      deep: true,
      immediate: true,
      handler(val){
        this.dataDetail = val || {}
      }
    },
    column:{
      deep: true,
      immediate: true,
      handler(val){
        this.dataColumn = val || []
      }
    },
  },
  methods: {},
};
</script>

<style scoped lang='less'>
.traffic-offence-content {
  position: relative;
  width: 300px;
  border-radius: 4px;
  border: 1px solid #D3D7DE;
  .header {
    height: 30px;
    border-bottom: 1px solid #D3D7DE;
    display: flex;
    align-content: center;
    align-items: center;
    padding-left: 10px;
    .icon-wrapper {
      width: 20px;
      height: 20px;
      border-radius: 2px;
      margin-right: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      .iconfont {
        color: #fff;
      }
    }
  }
  .content-wrapper {
    padding: 10px;
  }
  .label {
    color: rgba(0, 0, 0, 0.5);
  }
}
</style>
