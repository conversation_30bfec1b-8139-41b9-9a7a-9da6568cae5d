<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" footer-hide>
    <tag-view
      class="mb-sm"
      :list="tagList"
      :default-active="defaultActive"
      @tagChange="changeStatus"
      ref="tagView"
    ></tag-view>
    <div class="mb-sm" v-if="searchData.queryFlag !== '1'">
      <ui-label class="inline mr-lg" label="异常原因">
        <Select
          class="width-lg"
          v-model="searchData.reasonTextList"
          placeholder="请选择异常原因"
          clearable
          multiple
          filterable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in errorTypeList" :key="index" :label="item" :value="item"></Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="auto-fill table-module" v-ui-loading="{ loading: loading, tableData: tableData }">
      <pic-mode :card-list="tableData" small-img-key="facePath" img-key="scenePath" :pic-mode-count="7">
        <template #cardInfo="{ item }">
          <div class="mt-sm">
            <div class="pic-text-item" title="抓拍时间">
              <i class="icon-font icon-zhuapaishijian mr-xs f-14"></i>
              <span>{{ item.shotTime || '未知' }}</span>
            </div>
            <div class="pic-text-item" title="入库时间">
              <i class="icon-font icon-jieshoushijian mr-xs f-16"></i>
              <span>{{ item.createTime || '未知' }}</span>
            </div>
            <div class="pic-text-item">
              <i class="icon-font icon-zhuapaididian mr-xs"></i>
              <span class="ellipsis" :title="item.address">{{ item.address || '未知' }}</span>
            </div>
            <Tooltip v-if="searchData.queryFlag !== '1'" placement="right" transfer>
              <i class="icon-font icon-jiancejieguo mr-xs f-14"></i>
              <span class="tips ellipsis">{{ item.reasonText || '未知' }}</span>
              <div slot="content">
                <template v-for="(row, index) in item.reasonText.split(',')">
                  <span :key="index" v-if="index !== 0">、</span>
                  <span :key="index">
                    {{ row }}
                  </span>
                </template>
              </div>
            </Tooltip>
          </div>
        </template>
      </pic-mode>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    detailData: {},
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      styles: {
        width: '7rem',
      },
      tagList: [],
      typeList: [
        {
          label: '合格图像',
          value: '1',
        },
        {
          label: '不合格图像',
          value: '2',
        },
      ],
      errorTypeList: [],
      defaultActive: 1,
      searchData: {
        queryFlag: '',
        reasonTextList: [],
        pageNumber: 1,
        pageSize: 20,
      },
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  created() {
    this.tagList = this.typeList.map((row) => row.label);
  },
  methods: {
    changeStatus(index) {
      this.defaultActive = index;
      this.searchData.queryFlag = this.typeList[index].value;
      this.search();
    },
    async initResonList() {
      try {
        // 合格-拉起弹框，queryFlag 应该传入 '2',这样切换到 不合格 的时候，不合格原因才能查询到数据
        let params = this.searchData.queryFlag === '1' ? { ...this.searchData, queryFlag: '2' } : this.searchData;
        const res = await this.$http.post(equipmentassets.queryReasonList, params);
        this.errorTypeList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.post(equipmentassets.queryDetailList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
      if (this.searchData.queryFlag === '1') {
        this.defaultActive = 0;
      } else {
        this.defaultActive = 1;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        // （0 全部，1 合格数量，2 不合格数量，3 轨迹存疑数量，4 不可访问轨迹数量，5 上传超时轨迹数量，6 未关联设备轨迹数量，7 时间倒挂轨迹数量)
        switch (this.detailData.queryFlag) {
          case 'qualifiedNum':
            this.defaultActive = 0;
            this.searchData.queryFlag = '1';
            break;
          case 'notQualifiedNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '2';
            break;
          case 'correctNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '3';
            break;
          case 'availableNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '4';
            break;
          case 'realNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '5';
            break;
          case 'notDeviceNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '6';
            break;
          case 'downNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '7';
            break;
          case 'shortNum':
            this.defaultActive = 1;
            this.searchData.queryFlag = '8';
            break;
        }
        this.searchData.reasonTextList = [];
        this.searchData.beginTime = this.detailData.beginTime;
        this.searchData.endTime = this.detailData.endTime;
        this.searchData.civilCode = this.detailData.civilCode;
        this.init();
        this.initResonList();
      }
      this.visible = val;
    },
  },
  components: {
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
};
</script>
<style lang="less" scoped>
.tips {
  vertical-align: middle;
  display: inline-block;
  width: 120px;
  color: var(--color-tips);
}
@{_deep}.ivu-modal-body {
  height: 750px;
  display: flex;
  flex-direction: column;
}
.pic-text-item {
  display: flex;
  align-items: center;
}
</style>
