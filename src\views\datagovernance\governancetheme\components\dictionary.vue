<template>
  <div class="dictionary">
    <div class="dictionary-title">新建映射</div>
    <div class="dictionary-wrap">
      <div class="dictionary-left">
        <div class="dictionary-left-content col-flex">
          <div class="dictionary-left-content-select">
            <Select v-model="mappingValue" :disabled="isEdit" placeholder="请选择接入数据表" @on-change="mappingChange">
              <Option
                v-for="(selectItem1, selectIndex1) in accessData"
                :key="selectIndex1"
                :value="selectItem1.id + ',' + selectItem1.kafkaTopicName"
                >{{ selectItem1.kafkaTopicName }}</Option
              >
            </Select>
          </div>
          <div class="dictionary-left-content-select">
            <Select v-model="fieldValue" :disabled="isEdit" placeholder="请选择字段" @on-change="fieldChange">
              <Option
                v-for="(selectItem2, selectIndex2) in mapdictdData"
                :key="selectIndex2"
                :value="selectItem2.propertyName + ',' + selectItem2.propertyColumn"
                >{{ selectItem2.propertyColumn }}</Option
              >
            </Select>
          </div>
          <ui-table
            class="flex-1"
            :tableColumns="sourceColumns"
            :tableData="sourceData"
            @selectTable="selectTable"
          ></ui-table>
          <loading v-if="leftLoading"></loading>
        </div>
      </div>
      <div class="dictionary-center">
        <div class="dictionary-center-icon">
          <connecting-arrow :connectingOptions="connectingOptions"></connecting-arrow>
        </div>
      </div>
      <div class="dictionary-right">
        <div class="dictionary-right-content col-flex">
          <div class="dictionary-right-content-header">
            <Select v-model="targetDictType" @on-change="dictypeChange" placeholder="请选择系统字典">
              <Option
                v-for="(dictTypeItem, dictTypeIndex) in dictTypeData"
                :key="dictTypeIndex"
                :value="dictTypeItem.typeKey"
                >{{ dictTypeItem.typeValue }}</Option
              >
            </Select>
          </div>
          <ui-table class="flex-1" :tableColumns="targetColumns" :tableData="targetData"></ui-table>
        </div>
      </div>
    </div>
    <div class="dictionary-footer">
      <Button type="primary" @click="save">保&nbsp;存</Button>
    </div>
  </div>
</template>
<script>
import UiTable from '../../../../components/ui-table.vue';
import ConnectingArrow from './connecting-arrow.vue';
import governancetheme from '@/config/api/governancetheme';
import user from '@/config/api/user';
export default {
  props: {
    accessData: {
      type: Array,
      default: () => [],
    },
    mapdictdData: {
      type: Array,
      default: () => [],
    },
    dictTypeData: {
      type: Array,
      default: () => [],
    },
    editData: {
      type: Array,
      default: () => [],
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    kafkaValue: {
      type: String,
      default: '',
    },
    fields: {
      type: Object,
      default: () => {},
    },
    topicId: {
      type: Number,
      default: 0,
    },
    leftLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      test: [],
      connectingOptions: {
        width: '1.6%',
        height: '0.04rem',
        top: '50%',
      },
      targetColumns: [
        {
          title: '原字段名称',
          key: 'sourceDictName',
        },
        {
          title: '选项名称',
          key: 'sourceDictValue',
          render: (h, params) => {
            return h(
              'i-select',
              {
                style: {
                  // width: "216px",
                  'text-align': 'left',
                },
                props: {
                  value: params.row.newtargetDictValue + ',' + params.row.newtargetDictName,
                  placeholder: '请选择',
                  transfer: true,
                  'transfer-class-name': 'transfer-table-select',
                },
                on: {
                  'on-change': (val) => {
                    if (!val) return false;
                    const arr = val.split(',');
                    this.targetData[params.index].targetDictValue = arr[0];
                    this.targetData[params.index].targetDictName = arr[1];
                    this.targetData[params.index].newtargetDictValue = arr[0];
                    this.targetData[params.index].newtargetDictName = arr[1];
                    // render函数没有自动更新，需手动强制刷新。
                    this.$forceUpdate();
                  },
                },
              },
              this.dictData.map((row) => {
                return h('i-option', {
                  props: {
                    value: row.dataKey + ',' + row.dataValue,
                    label: row.dataValue,
                  },
                  style: {
                    'background-color': '#1C325A',
                    color: '#ffffff',
                  },
                });
              }),
            );
          },
        },
      ],
      targetData: [],
      sourceColumns: [
        { type: 'selection', width: 50, align: 'center' },
        {
          title: '编码',
          key: 'sourceDictValue',
          // width: 180,
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                disabled: row._checked,
                value: row.sourceDictValue,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.sourceDictValue = val;
                  if (row.sourceDictValue && row.sourceDictName) {
                    row._disabled = false;
                  } else {
                    row._disabled = true;
                  }
                  this.sourceData.splice(index, 1, row);
                },
              },
            });
          },
        },
        {
          title: '选项名称',
          key: 'sourceDictName',
          // width: 250,
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                disabled: row._checked,
                value: row.sourceDictName,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.sourceDictName = val;
                  if (row.sourceDictValue && row.sourceDictName) {
                    row._disabled = false;
                  } else {
                    row._disabled = true;
                  }
                  this.sourceData.splice(index, 1, row);
                },
              },
            });
          },
        },
        {
          title: '添加',
          width: 50,
          key: 'operation',
          renderHeader: (h) => {
            return h('div', [
              h('i', {
                class: ['icon-font', 'icon-tianjia', 'addIcon'],
                props: {},
                style: {
                  color: '#174f98',
                  'font-size': '12px',
                  width: '16px',
                  height: '16px',
                  border: '1px solid #174f98',
                  display: 'flex',
                  'justify-content': 'center',
                  'align-items': 'center',
                },
                on: {
                  click: () => {
                    this.add();
                  },
                },
              }),
            ]);
          },
          render: (h, params) => {
            let row = params.row;
            return h(
              'div',
              [
                h('i', {
                  class: ['icon-font', 'icon-shanchu2', 'deleteIcon'],
                  style: {
                    color: '#174f98',
                    'font-size': '12px',
                    cursor: row._checked ? 'not-allowed' : 'pointer',
                  },
                  on: {
                    click: () => {
                      if (!row._checked) {
                        this.remove(params.index);
                      }
                    },
                  },
                }),
              ],
              {
                style: {
                  width: '216px',
                },
              },
            );
          },
        },
      ],
      sourceData: [],
      mappingValue: '',
      fieldValue: '',
      dictData: [],
      targetDictType: '',
      fielId: '',
      isAdd: false,
    };
  },
  mounted() {
    if (this.isEdit) {
      this.mappingValue = this.kafkaValue;
      this.fieldValue = this.fields.sourcePropertyName + ',' + this.fields.sourcePropertyColumn;
      this.targetDictType = this.fields.targetDictType;
    }
  },
  methods: {
    initData(datas, isadding = false) {
      !isadding ? (this.targetData = []) : (this.dictData = []);
      return datas.map((item, index) => {
        item.keyId = index;
        if (this.isEdit && !isadding) {
          item._checked = true;
          let targetObj = {}; // targetData
          targetObj.newtargetDictValue = item.targetDictValue;
          targetObj.newtargetDictName = item.targetDictName;
          targetObj.sourceDictName = item.sourceDictName;
          targetObj.sourceDictValue = item.sourceDictValue;
          this.targetData.push(targetObj);
        }
        return item;
      });
    },
    // 新增映射字段
    add() {
      this.sourceData = this.initData(this.sourceData, true);
      this.sourceData.push({
        sourceDictValue: '',
        sourceDictName: '',
        _read: false,
        _checked: false,
        _disabled: true,
      });
    },
    // 接入数据表下拉框change事件
    mappingChange(val) {
      if (!val) return false;
      this.mappingValue = val;
      this.$emit('getfieldData', val);
    },
    // 字段下拉框change事件
    fieldChange(val) {
      this.fieldValue = val;
    },
    // 映射字段删除
    remove(index) {
      this.sourceData.splice(index, 1);
    },
    // 多选时触发的事件,返回已选择数据
    selectTable(selection) {
      this.sourceData = this.sourceData.map((item) => {
        let index = selection.findIndex((row) => row.keyId === item.keyId);
        if (index === -1) {
          item._checked = false;
          item._read = false;
        } else {
          item._checked = true;
          item._read = true;
        }
        return item;
      });
      selection = selection.map((item, selectIndex) => {
        item.newtargetDictValue = item.targetDictValue ? item.targetDictValue : '';
        item.newtargetDictName = item.targetDictName ? item.targetDictName : '';
        this.targetData.forEach((targetItem, targetIndex) => {
          if (selectIndex === targetIndex) {
            item.newtargetDictValue = targetItem.newtargetDictValue;
            item.newtargetDictName = targetItem.newtargetDictName;
          }
        });
        return item;
      });
      this.targetData = selection;
    },
    dictypeChange(val) {
      if (val) {
        this.getDictData(val);
      }
    },
    async getDictData(dictType) {
      try {
        const params = {
          typekey: dictType,
        };
        let res = await this.$http.get(user.queryByTypeKey, { params });
        this.dictData = res.data.data ? res.data.data : [];
      } catch (error) {
        console.log(error);
      }
    },
    // 保存
    save() {
      if (!this.targetData.length) {
        return false;
      }
      const dataVaild = this.targetData.filter((item) => {
        return item.newtargetDictName === '' || item.newtargetDictValue === '';
      });
      if (dataVaild.length) {
        this.$Message.error('请将信息填写完整!');
        return false;
      }
      let mapValues = this.mappingValue.split(',');
      const dictDataList = this.targetData.map((item) => {
        const obj = {
          sourceDictName: item.sourceDictName,
          sourceDictValue: item.sourceDictValue,
          targetDictName: item.targetDictName,
          targetDictValue: item.targetDictValue,
        };
        return obj;
      });
      const fieldArr = this.fieldValue.split(',');
      const params = {
        topicTransferId: mapValues[0], // 主题传输id
        kafkaTopicName: mapValues[1], //接入kafka 主题名字
        sourcePropertyName: fieldArr[0], // 原始字段名称
        sourcePropertyColumn: fieldArr[1], // 原始字段备注
        targetDictType: this.targetDictType, // 目标字典类型
        dictDataList: dictDataList,
      };
      if (this.isEdit) {
        params.id = this.fields.id;
      }
      this.updateDict(params);
    },
    async updateDict(params) {
      try {
        let res = await this.$http.post(governancetheme.updateTopicTransferDict, params);
        if (res.data.code == '200') {
          this.$emit('close');
          this.sourceData = [];
          this.targetData = [];
          this.dictData = [];
          this.mappingValue = '';
          this.fieldValue = '';
          this.targetDictType = '';
          this.$Message.success('字典映射配置保存成功！');
          // this.$Message["success"]({
          //   background: true,
          //   content: "字典映射配置保存成功！",
          // });
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    editData() {
      this.targetData = [];
      this.sourceData = this.initData(this.editData);
      return this.editData;
    },
    isEdit() {
      this.targetDictType = '';
      this.mappingValue = this.kafkaValue;
      // this.fieldValue = this.fields.sourcePropertyName;
      return this.isEdit;
    },
    mapdictdData() {
      return this.mapdictdData;
    },
    fields() {
      this.fieldValue = this.fields.sourcePropertyName + ',' + this.fields.sourcePropertyColumn;
      this.targetDictType = this.fields.targetDictType;
    },

    targetDictType(newval) {
      if (newval) {
        this.getDictData(this.targetDictType);
      }
    },
    accessData() {
      this.mappingValue = '';
      this.mappingValue = this.kafkaValue;
    },
  },
  components: { UiTable, ConnectingArrow },
};
</script>
<style lang="less" scoped>
.justify-flex {
  display: flex;
  justify-content: center;
}
.align-flex {
  display: flex;
  align-items: center;
}
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.col-flex {
  display: flex;
  flex-direction: column;
}
.flex-1 {
  flex: 1;
}
.col-div {
  & > div {
    .align-flex;
    &:nth-child(1) {
      flex: 1;
    }
    &:nth-child(2) {
      flex: 1;
    }
    &:nth-child(3) {
      width: 68px;
      justify-content: flex-end;
    }
    i {
      font-size: 12px;
      color: #174f98;
    }
  }
}
.dictionary {
  width: 100%;
  height: 100%;
  padding: 64px 41px 23px;
  @{_deep} .ivu-select {
    outline-style: none;
    text-align: center;
    &-selection {
      border: none;
      background: transparent;
      outline-style: none;
      box-shadow: none;
    }
  }
  @{_deep} .ivu-select-arrow {
    right: 0;
    color: var(--color-primary);
    font-size: 16px;
  }
  &-wrap {
    display: flex;
  }
  &-footer {
    width: 100%;
    margin-top: 24px;
    .justify-flex;
  }
  &-left {
    flex: 1;
    &-content {
      position: relative;
      height: 448px;
      padding: 14px 10px;
      border: 1px solid var(--border-color);
      &-select {
        .flex;
        padding: 10px 20px;
        background: #122948;
        border-bottom: 1px solid var(--border-color);
      }
      &-row {
        height: 48px;
        padding: 0 20px;
        display: flex;
        font-size: 14px;
        color: #ffffff;
        background: #092955;
        .col-div;
        &-operate {
          .flex;
          width: 16px;
          height: 16px;
          border: 1px solid #174f98;
        }
      }
      .default {
        background: #041939;
      }
      .stripe {
        background: #062042;
      }

      @{_deep} .ivu-input {
        height: 34px !important;
      }
      @{_deep} .ivu-form-item {
        margin: 22px 0 14px;
        &-content {
          .align-flex;
          width: 100%;
          .col-div;
          & > div:last-child {
            display: flex;
            justify-content: flex-end;
          }
        }
      }
    }
  }
  @{_deep} .ivu-select {
    .ivu-select {
      &-selection {
        padding: 0;
        color: #ffffff;
      }
      &-placeholder {
        color: #56789c;
        padding: 0;
      }
      &-selected-value {
        padding: 0;
      }
    }
  }
  &-title {
    margin-bottom: 18px;
    font-size: 14px;
    color: #ffffff;
  }
  &-center {
    width: 42px;
    display: flex;
    align-items: center;
    &-icon {
      .justify-flex;
      width: 100%;
      cursor: pointer;
    }
  }
  &-right {
    flex: 1;
    &-content {
      min-height: 448px;
      padding: 14px 10px;
      border: 1px solid var(--border-color);
      // overflow: auto;
      &-header {
        position: relative;
        .flex;
        height: 50px;
        padding: 10px 20px;
        background: #122948;
        border-bottom: 1px solid var(--border-color);
        @{_deep} .ivu-select {
          .flex;
          &-selection {
            width: 100%;
          }
        }
      }
    }
  }
}
.deleteIcon {
  &:hover {
    color: var(--color-primary) !important;
  }
  &:active {
    color: #4e9ef2 !important;
  }
}
.addIcon {
  &:hover {
    color: var(--color-primary) !important;
    border: 1px solid var(--color-primary) !important;
  }
  &:active {
    color: #4e9ef2 !important;
    border: 1px solid #4e9ef2 !important;
  }
}
.dictionary-left-select {
  background: #1c325a !important;
  &:hover {
    background: #48bcde !important;
  }
  li {
    color: #ffffff;
    &:hover {
      background: #48bcde !important;
    }
  }
}
</style>
