<template>
  <div class="selection-algorithm">
    <div class="selection-algorithm-header">请选择人脸结构化算法：</div>
    <div>
      <CheckboxGroup style="display: flex; flex-wrap: wrap" v-model="algorithmData">
        <Checkbox
          style="width: 47%; margin-bottom: 13px"
          v-for="(item, index) in propData"
          :key="index"
          :label="item.dataKey"
          >{{ item.dataValue }}</Checkbox
        >
      </CheckboxGroup>
    </div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    propData: {
      type: Array,
      default: () => {},
    },
    algorithmVendors: {
      type: Array,
      default: () => {},
    },
  },
  data() {
    return {
      algorithmData: [],
    };
  },
  updated() {
    this.$emit('update', this.algorithmData);
  },
  methods: {},
  watch: {
    algorithmVendors() {
      this.algorithmData = this.algorithmVendors;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.selection-algorithm {
  width: 100%;
  &-header {
    margin-bottom: 16px;
    font-size: 14px;
    color: var(--color-primary);
  }
}
@{_deep} .ivu-checkbox {
  margin-right: 10px;
  &-group-item {
    align-items: center;
  }
}
@{_deep} .ivu-checkbox-checked .ivu-checkbox-inner {
  background: var(--color-primary) !important;
}
</style>
