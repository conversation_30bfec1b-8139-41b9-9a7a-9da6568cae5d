<template>
  <section class="content">
    <div class="content-left">
      <ui-card title="基本信息" class="m-b20 info-msg">
        <div class="info-content">
          <p>
            <span class="label">姓名</span
            ><b class="weight">{{ baseInfo.xm || "--" }}</b>
          </p>
          <p>
            <span class="label">年龄</span
            ><b class="half">{{ baseInfo.age || "--" }}</b>
            <span class="label">性别</span
            ><b>{{ baseInfo.xbdm | commonFiltering(genderList) }}</b>
          </p>
          <p>
            <span class="label">民族</span
            ><b class="half">{{
              baseInfo.mzdm | commonFiltering(nationList)
            }}</b>
            <span class="label">联系电话</span>
            <b>{{ baseInfo.lxdh || "--" }}</b>
          </p>
          <p>
            <span class="label">婚姻状态</span
            ><b class="half">{{
              baseInfo.hyzkdm | commonFiltering(marriageList)
            }}</b
            ><span class="label">职业</span
            ><b :title="baseInfo.zy" class="ellipsis zy-width">{{
              baseInfo.zy || "--"
            }}</b>
          </p>
          <p>
            <span class="label">户籍地址</span
            ><b :title="baseInfo.hjdzDzmc" class="ellipsis address-width">{{
              baseInfo.hjdzDzmc || "--"
            }}</b>
          </p>
          <p>
            <span class="label">居住地址</span
            ><b :title="baseInfo.xzzMlxxdz" class="ellipsis address-width">{{
              baseInfo.xzzMlxxdz || "--"
            }}</b>
          </p>
        </div>
      </ui-card>

      <div class="ui-card m-b20">
        <div class="card-head">
          <p
            class="capture-title face-capture"
            :class="carType === 1 ? 'capture-active' : ''"
            @click="handleTab(1)"
          >
            <span>驾乘车辆</span>
          </p>
          <div
            class="capture-title car-capture"
            :class="carType === 2 ? 'capture-active' : ''"
            @click="handleTab(2)"
          >
            <span>名下车辆</span>
          </div>
        </div>
        <div class="card-content">
          <CarSwiper :type="carType" :list="carList" :loading="carLoading" />
        </div>
      </div>

      <div class="ui-card">
        <div class="card-head">
          <p
            class="capture-title face-capture"
            :class="carType2 === 1 ? 'capture-active' : ''"
            @click="handleTab2(1)"
          >
            <span>最近抓拍</span>
          </p>
          <div
            class="capture-title car-capture"
            :class="carType2 === 2 ? 'capture-active' : ''"
            @click="handleTab2(2)"
          >
            <span>最新报警</span>
          </div>
        </div>
        <div class="card-content" v-if="carType2 === 1">
          <snapRecord
            type="video"
            :list="captureList"
            :loading="recordLoading"
          />
        </div>
        <div class="card-content alarm-content" v-else>
          <alarm-tab
            ref="alarmTab"
            :carType="1"
            :showCount="false"
            :idCardNo="archiveNo"
          ></alarm-tab>
        </div>
      </div>
    </div>
    <div class="content-middle">
      <!--标签图谱-->
      <label-cloud-view
        class="label-cloud"
        :labels="baseInfo.labels || []"
        :type="1"
        :info="baseInfo"
        :photoUrl="
          baseInfo.photos && baseInfo.photos.length > 0
            ? baseInfo.photos[0].photoUrl
            : ''
        "
      />
      <!--底部swiper-->
      <middle-swiper :relationship="relationship" type="people" />
    </div>
    <div class="content-right">
      <ui-card title="关系统计" class="m-b20" :padding="0" v-if="graphObj">
        <div
          slot="extra"
          class="play-btn mr-20 color-primary cursor-p"
          @click="toRelationGraph"
        >
          关系图谱
        </div>
        <!-- 关系图谱 -->
        <graph
          v-if="hasGraphData"
          @finish="graphLoaded"
          class="right"
          :relation-can-operate="false"
        ></graph>
        <ui-empty v-else></ui-empty>
      </ui-card>
      <!--行为规律-->
      <law-behaviour
        :dataType="1"
        :archiveNo="this.$route.query.archiveNo"
      ></law-behaviour>
      <!-- 活动轨迹-->
      <activityTrack
        :trackingStatistics="trackingStatistics"
        :trackingLoading="trackingLoading"
        :xAxis="trackingXAxis"
        :series="trackingSeries"
      ></activityTrack>
    </div>
  </section>
</template>
<script>
import * as echarts from "echarts";
import { mapGetters, mapActions } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "@/views/holographic-archives/components/snap-record";
import middleSwiper from "@/views/holographic-archives/one-person-one-archives/video-archive/dashboard/components/middle-swiper";
import lawBehaviour from "../../../components/law-behaviour";
import labelCloudView from "../../../components/label-cloud-view/index";
import CollapseExpand from "@/components/relation-graph/collapse-expand";
import activityTrack from "./components/activity-track";
import CarSwiper from "./components/car-swiper.vue";
import imgloading from "@/assets/img/car1.webp";
import {
  drivingVehicle,
  ownedVehicles,
  getPortraitCapture,
  movementTtrackingStatistics,
  relationshipCard,
} from "@/api/realNameFile";
import { alarmPageList } from "@/api/target-control";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
import alarmTab from "@/views/perception-home/perception-site/components/alarm-tab.vue";
export default {
  mixins: [relativeGraphMixin],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    swiper,
    swiperSlide,
    snapRecord,
    lawBehaviour,
    labelCloudView,
    middleSwiper,
    activityTrack,
    CollapseExpand,
    CarSwiper,
    alarmTab,
  },
  props: {
    // 基本信息
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      recordLoading: false,
      archiveNo: "", //人员档案id
      carLoading: false,
      carType: 1, //1驾乘/2名下车辆
      carType2: 1, //1最近抓拍/2最新报警
      carList: [], //驾乘车辆/名下车辆
      trackingStatistics: {}, //活动轨迹
      captureList: [], //抓拍记录
      trackingLoading: false,
      list: [1, 2, 3, 4],
      photoUrl: "", //标签图片
      relationship: {}, //关系卡片
      alarmInfo: {
        total: 0,
      },
      labelList: [
        { id: "1", name: "重点人员", color: "#EA4A36" },
        { id: "2", name: "常住人口", color: "#48BAFF" },
        { id: "3", name: "涉毒人员", color: "#F29F4C" },
        { id: "4", name: "常住人口", color: "#1FAF8A" },
        { id: "11", name: "重点人员", color: "#EA4A36" },
        { id: "21", name: "常住人口", color: "#48BAFF" },
        { id: "31", name: "涉毒人员", color: "#F29F4C" },
        { id: "41", name: "常住人口1", color: "#1FAF8A" },
        { id: "12", name: "重点人员2", color: "#EA4A36" },
        { id: "22", name: "常住人口重点人员83", color: "#48BAFF" },
        { id: "32", name: "涉毒人员4", color: "#F29F4C" },
        { id: "42", name: "常住人口5", color: "#1FAF8A" },
        { id: "13", name: "重点人员6重点人员6", color: "#EA4A36" },
        { id: "33", name: "涉毒人员7", color: "#F29F4C" },
        { id: "18", name: "重点人员8", color: "#EA4A36" },
        { id: "72", name: "常住人口9", color: "#48BAFF" },
        { id: "63", name: "涉毒人员10", color: "#F29F4C" },
        { id: "45", name: "常住人口11", color: "#1FAF8A" },
        { id: "721", name: "常住人口9", color: "#48BAFF" },
        { id: "631", name: "涉毒人员110", color: "#F29F4C" },
        { id: "415", name: "常住人口111常住人口111", color: "#1FAF8A" },
      ],
      options: {
        disableDragNode: true,
        allowShowMiniToolBar: false,
        // disableZoom: true,
        defaultExpandHolderPosition: "hide",
        checkSelect: false,
        fontSize: 12,
        layouts: [
          {
            label: "中心",
            layoutName: "center",
            layoutClassName: "seeks-layout-center",
            // 节点间距
            distance_coefficient: 0.5,
          },
        ],
      },
      atlasList: [
        {
          id: 1,
          text: "苏A 29999",
          img: imgloading,
          children: [
            // { id: 2, num: 2, text: '同抓拍' },
            // { id: 3, num: 2, text: '同涉案' },
            // { id: 4, num: 2, text: '驾乘人员' },
            // { id: 5, num: 2, text: '同车主' },
            // { id: 6, num: 2, text: '同车主' },
            // { id: 7, num: 2, text: '同车主' }
          ],
        },
      ],
      // 活动轨迹x轴
      trackingXAxis: {
        data: [],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      // 活动轨迹对应值配置
      trackingSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [],
          barWidth: "20",
          itemStyle: {
            normal: {
              barBorderRadius: [15, 15, 15, 15],
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1, //渐变色在下面修改，这里是透明度
                [
                  {
                    offset: 0,
                    color: "#5BA3FF",
                  },
                  {
                    offset: 1,
                    color: "#2C86F8",
                  },
                ]
              ),
            },
          },
        },
      ],
    };
  },

  computed: {
    ...mapGetters({
      genderList: "dictionary/getGenderList", //检索类型
      marriageList: "dictionary/getMarriageList", //婚姻状态
      nationList: "dictionary/getNationList", //民族
      professionList: "dictionary/getProfession", //职业
      relationList: "number-cube/getPeopleRelationList", //职业
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },
  created() {
    let query = this.$route.query;
    // 防止因为Anchor锚点导致的路由query参数丢失，跳档案都先跳到这个页面，先把参数存storage
    sessionStorage.setItem("query", JSON.stringify(query));
    this.archiveNo = query.archiveNo;
    // this.alarmPageList()
    this.drivingVehicle();
    this.getPortraitCapture();
    this.movementTtrackingStatistics();
    this.getCard();
  },
  watch: {
    baseInfo: {
      deep: true,
      handler(val) {
        if (val.photos && val.photos.length > 0) {
          this.atlasList[0].img = val.photos[0].photoUrl;
        }
      },
    },
    relationList: {
      deep: true,
      handler(val) {
        var list = [];
        val.forEach((item, index) => {
          var obj = {
            id: index + 2,
            num: item.count,
            text: item.nameCn,
          };
          list.push(obj);
        });
        this.atlasList[0].children = list;
        this.$refs.CollapseExpand.showSeeksGraph();
      },
    },
  },
  mounted() {
    var list = [];
    this.relationList.forEach((item, index) => {
      var obj = {
        id: index + 2,
        num: item.count,
        text: item.nameCn,
      };
      list.push(obj);
    });
    this.atlasList[0].children = list;
  },
  methods: {
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    // 驾乘车辆
    drivingVehicle() {
      this.carLoading = true;
      let data = {
        archiveNo: this.archiveNo,
        dataType: 1, //1实名2视频
        dataSize: 5, //取5条
      };
      drivingVehicle(data)
        .then((res) => {
          this.carList = res.data || [];
        })
        .catch(() => {})
        .finally(() => {
          this.carLoading = false;
        });
    },
    // 名下车辆
    ownedVehicles() {
      this.carLoading = true;
      ownedVehicles(this.archiveNo)
        .then((res) => {
          this.carList = res.data || [];
        })
        .catch(() => {})
        .finally(() => {
          this.carLoading = false;
        });
    },

    /**
     * @description: 关系内容
     * @param {number} val 1：管控报警 2：抓拍记录 3：常去地 4：关联案件 5：同行人员 6：同户籍 7：违法违章 8：异常行为
     */
    async relationshipCard(val) {
      let data = {
        archiveNo: this.archiveNo,
        type: val,
      };
      let res = await relationshipCard(data);
      return res.data;
    },
    // 循环关系卡片
    async getCard() {
      this.relationship = {};
      // 抓拍记录
      let capture = await this.relationshipCard(2);
      // 常去地
      let place = await this.relationshipCard(3);
      // 关联案件
      let law = await this.relationshipCard(4);
      this.relationship = {
        capture: capture || {},
        place: place || {},
        law: law || {},
        alarmInfo: this.alarmInfo,
      };
    },
    // 抓拍记录 / 最近抓拍
    getPortraitCapture() {
      this.recordLoading = true;
      let data = {
        archiveNo: this.archiveNo,
        dataType: 1, //1实名2视频
        dataSize: 5, //取5条
      };
      getPortraitCapture(data)
        .then((res) => {
          this.captureList = res.data || [];
        })
        .catch(() => {})
        .finally(() => {
          this.recordLoading = false;
        });
    },
    // 活动轨迹
    movementTtrackingStatistics() {
      this.trackingLoading = true;
      let params = {
        archiveNo: this.archiveNo,
        dataRange: 2,
      };
      movementTtrackingStatistics(params)
        .then((res) => {
          const { x, y } = res.data;
          this.trackingXAxis.data = x;
          this.trackingSeries[0].data = y;
        })
        .catch(() => {})
        .finally(() => {
          this.trackingLoading = false;
        });
    },
    // 驾乘车辆和名下车辆切换
    handleTab(val) {
      this.carType = val;
      if (val === 1) {
        // 驾乘车辆
        this.drivingVehicle();
      } else {
        //名下车辆
        this.ownedVehicles();
      }
    },
    // 最近抓拍和最新报警切换
    handleTab2(val) {
      this.carType2 = val;
      if (val === 1) {
        // 最近抓拍
        this.getPortraitCapture();
      } else {
        //最新报警
        // this.alarmPageList()
        /* this.$nextTick(() => {
          this.$refs.alarmTab.handleTabList(val);
        }); */
      }
    },
    async alarmPageList() {
      var params = {
        idCardNo: this.archiveNo,
        // vid: this.vid,
        pageNumber: 1,
        pageSize: 5,
      };
      await alarmPageList(params).then((res) => {
        this.captureList = res.data.entities;
        var row = this.captureList[0];
        row.total = res.data.total;
        this.alarmInfo = row;
        this.relationship.alarmInfo = row;
      });
    },
    /**
     * 最近抓拍、最新报警更多
     */
    moreInfo() {},
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  display: flex;
  height: 100%;
  overflow: auto;
  min-height: 760px;
  padding: 16px 20px 20px;
  box-sizing: border-box;
}
.content-left,
.content-right {
  width: 500px;
  display: flex;
  flex-direction: column;
  height: 100%;
  .ui-card,
  .info-msg {
    flex: 1;
    max-height: calc(~"33.3% - 20px");
  }
  /deep/ .card-content {
    height: calc(~"100% - 30px");
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  /deep/ .ivu-tabs-content {
    height: 234px;
  }
  .info-msg {
    /deep/ .card-content {
      padding: 22px 30px 36px !important;
    }
  }
}
.info-content {
  p {
    display: flex;
    font-size: 14px;
    padding: 1% 0;
    color: rgba(0, 0, 0, 0.9);
    .label {
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      width: 56px;
      font-weight: bold;
      white-space: nowrap;
      color: #2c86f8;
      margin-right: 15px;
    }
    b {
      font-weight: normal;
      word-break: break-all;
      &.half {
        width: 110px;
      }
      &.weight {
        font-size: 20px;
        line-height: 1;
        font-weight: bold;
      }
      &.zy-width {
        width: 190px;
      }
      &.address-width {
        width: 420px;
      }
      &.car-address {
        width: 160px;
      }
    }
  }
}
.capture-title {
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  background: #d3d7de;
  color: #666;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;
  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}
.face-capture {
  position: relative;
}
.car-capture {
  position: absolute;
  left: 120px;
}
.capture-active {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
}
.card-content {
  padding: 20px;
}
.alarm-content {
  padding: 20px 0;
}
.content-middle {
  overflow: hidden;
  flex: 1;
  display: flex;
  width: 100%;
  flex-direction: column;
}
.movelink {
  cursor: pointer;
}
</style>
