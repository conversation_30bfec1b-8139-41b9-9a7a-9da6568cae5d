<template>
  <div class="newdialog">
    <ui-modal
      v-model="visible"
      :title="isEdit ? '更新治理流程' : '新建治理流程'"
      width="29.16rem"
      @onCancel="handleReset"
    >
      <Form ref="form" :model="form" :rules="ruleForm" :label-width="80">
        <FormItem label="流程名称" prop="topicName">
          <Input v-model="form.topicName" placeholder="请填写主题名称"></Input>
        </FormItem>
        <FormItem label="数据类型" prop="topicType">
          <Select v-model="form.topicType" disabled placeholder="请选择数据类型">
            <Option v-for="(item, index) in topicTypeData" :key="index" :value="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="备注" prop="topicDesc">
          <Input
            v-model="form.topicDesc"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 6 }"
            placeholder="请输入备注信息"
          ></Input>
        </FormItem>
      </Form>
      <template slot="footer">
        <Button type="primary" @click="handleSubmit('form')">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
export default {
  props: {
    editForm: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      form: {
        topicName: '',
        topicType: '1',
        topicDesc: '',
      },
      ruleForm: {
        topicName: [{ required: true, message: '请填写主题名称', trigger: 'blur' }],
        topicType: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
        topicDesc: [{ required: true, message: '请输入备注信息', trigger: 'blur' }],
      },
      topicTypeData: [
        { label: '视图', value: '1' },
        // { label: "人脸", value: "2" },
        // { label: "车辆", value: "3" },
        // { label: "视频", value: "4" },
        // { label: "重点人员", value: "5" },
        // { label: "自定义主题", value: "99" },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.visible = true;
    },
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let updateData = JSON.parse(JSON.stringify(this.form));
          if (this.isEdit) {
            updateData.id = this.editForm.id;
          }
          this.visible = false;
          this.$emit('updateTheme', updateData);
          this.handleReset();
        } else {
          this.$Message.error('Fail!');
        }
      });
    },
    handleReset() {
      this.$refs['form'].resetFields();
    },
  },
  watch: {
    editForm: {
      handler() {
        if (this.isEdit) {
          this.form = {
            topicName: this.editForm.topicName,
            topicType: '1',
            topicDesc: this.editForm.topicDesc,
          };
        } else {
          this.form = {
            topicName: '',
            topicType: '1',
            topicDesc: '',
          };
        }
      },
      deep: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 9px 51px;
}
</style>
