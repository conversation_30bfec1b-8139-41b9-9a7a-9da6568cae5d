<template>
  <div style="width: 100%;">
    <Table class="auto-fill table" :columns="columns" border :loading="loading" :data="tableData" max-height="580">
      <template #alarmLevel="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].alarmLevelName" maxlength="20" class="input-wid"></Input>
      </template>
      <template #isNotification="{ index }">
        <i-switch v-model="tableData[index].isNotification" true-value="1" false-value="0" />
      </template>
      <template #alarmColour="{ index }">
        <Dropdown transfer transfer-class-name="alarmColor" @on-click="colorResult(index, $event)">
          <div class="colorBtn"
            :key="Math.random()"
            :class="'color'+tableData[index].alarmColour"
            >
            <!-- :transfer-class-name="'color'+tableData[index].alarmColour" -->
            <Icon type="ios-arrow-down"></Icon>
          </div>
          <DropdownMenu slot="list">
              <DropdownItem name="1"><div class="color1 color"></div></DropdownItem>
              <DropdownItem name="2"><div class="color2 color"></div></DropdownItem>
              <DropdownItem name="3"><div class="color3 color"></div></DropdownItem>
              <DropdownItem name="4"><div class="color4 color"></div></DropdownItem>
              <DropdownItem name="5"><div class="color5 color"></div></DropdownItem>
          </DropdownMenu>
      </Dropdown>
      </template>
      <template #isSound="{ index }">
        <i-switch v-model="tableData[index].isSound" true-value="1" false-value="0" />
      </template>
      <template #searchType="{ index }">
        <RadioGroup v-model="tableData[index].soundType" @on-change="soundTypeChange">
            <Radio label="1">默认</Radio><i class="iconfont icon-shengyin_shiti" @click="sound(tableData[index], 1)"></i>
            <Radio label="2">自定义</Radio>
        </RadioGroup>
        <!-- <Checkbox v-model="single">默认<i class="iconfont icon-shengyin_shiti"></i></Checkbox>
        <Checkbox v-model="tableData[index].soundType">自定义</Checkbox> -->
        <span v-show="tableData[index].soundType == 2 && !tableData[index].soundUrl" class="upload" @click="uploadFile(index)"><i class="iconfont icon-upload"></i>上传</span>
        <i v-if="tableData[index].soundType == 2 && tableData[index].soundUrl" class="iconfont icon-shengyin_shiti" @click="sound(tableData[index], 2)"></i>
        <span v-if="tableData[index].soundType == 2 && tableData[index].soundName" class="upload" @click="uploadFile(index)">{{ tableData[index].soundName }}</span>
        <UploadFile style="display: none;" :maxSize="2048" backType="mp3" url="/qsdi-system-service/file/upload" :fileType="['mp3']" @successPut="fileUploadSuccess" ref="uploadFile" />
      </template>
      <template #dictionaryCode="{ index }">
      </template>
    </Table>
    <audio controls ref="audio" style="position: absolute; opacity: 0.01;">
      <source :src="audioUrl" />
    </audio>
  </div>
</template>
<script>
import { resourceConfigur, queryResourceConfigur } from '@/api/dataGovernance'
import uiUploadImg from '@/components/ui-upload-img/index'
import UploadFile from '@/components/ui-upload-file'
import { mapGetters } from 'vuex'
export default {
  components: {
    uiUploadImg, UploadFile
  },
  props: {
    type: {
      type: String,
      default: 'people'
    },  
    //对应字典
    dictTypedata: {
      type: Array,
      default: () => []
    },
    tableData: {
      type: Array,
      default: () => [{},{},{}]
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      resourceId: '',
      columns: [
        { title: '报警等级', slot: 'alarmLevel', width: 160 },
        { title: '报警弹窗', slot: 'isNotification', width: 100 },
        { title: '报警颜色', slot: 'alarmColour', width: 100 },
        { title: '报警声音', slot: 'isSound', width: 100 },
        { title: '选择声音（支持mp3格式，2M以内）', slot: 'searchType' },
        // { title: ' ', slot: 'dictionaryCode' },
      ],
      // tableData: [{},{},{}]
      tableIndex: 0,
      audioUrl: '',
    }
  },
  computed: {
    ...mapGetters({
      searchTypeList: 'dictionary/getSearchTypeList' //检索类型
    })
  },
  watch: {
    tableData: {
      handler (val) {
        console.log('------------------', val)
        this.$forceUpdate()
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // this.columns.push({
    //   title: ' ',
    //   slot: 'dictionaryCode',
    //   renderHeader: (h, params) => {
    //     return h('div', 
    //     [
    //       h('i',
    //         {
    //           class: 'iconfont icon-tishi',
    //           style: {
    //           },
    //           attrs: {
    //             title: '注：上传的提示音只支持XX格式，最大不超过XX',
    //           }
    //         }
    //       ),
    //     ]);
    //   }
      
    // })
  },
  methods: {
    addForm () {
      this.tableData.push({})
    },
    colorResult(index, name) {
      console.log('colorName', index, name)
      this.$emit("colorRefrsh", index, name, this.type)
      // this.tableData[index].alarmColour = name
      // this.$forceUpdate()
    },
    soundTypeChange(val) {
      this.$forceUpdate()

    },
    uploadFile (index) {
      this.tableIndex = index;
      this.$refs.uploadFile.clickFn()
    },
    fileUploadSuccess(data) {
      this.tableData[this.tableIndex].soundName = data.originalFilename
      this.tableData[this.tableIndex].soundUrl = data.fileUrl
    },
    sound(row, num) {
      if (num == 1) {
        if (!!row.defaultUrl) {
          this.$refs.audio.src = row.defaultUrl
        }else {
          this.$refs.audio.src = "http://*************:19002/qsdi/2023/04/07/0c2e1f9f5541400c954ec1a0077c6e35.mp3"
        }
      }else {
        this.$refs.audio.src = row.soundUrl
      }
      this.$refs.audio.play()
    }
  }
}
</script>
<style lang="less" scoped>
.config {
  /deep/ .ivu-table-body {
    min-height: 220px;
  }
  /deep/.ivu-table-tbody tr td {
    background-color: #f9f9f9 !important;
  }
  /deep/ .ivu-table-border th {
    border-right: 1px solid #d3d7de !important;
  }
  /deep/.ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #d3d7de;
  }
  /deep/.ivu-input,
  .ivu-select {
    width: 120px !important;
  }
}
.add-from {
  background: #f9f9f9;
  margin-top: 3px;
  border: 1px solid #e8eaec;
  text-align: center;
  cursor: pointer;
}

/deep/ .ivu-table-cell {
  width: 100%;

  .icon-tishi {
    float: right;
  }
}

.upload {
  color: #2c86f8;
  cursor: pointer;
}

.color {
  width: 30px;
  height: 30px;
}
.color1 {
  background: #EA4A36;
}
.color2 {
  background: #FC770B;
}
.color3 {
  background: #FFC300;
}
.color4 {
  background: #36BE7F;
}
.color5 {
  background: #2C86F8;
}
.four-color {
  background: #36BE7F;
}
.five-color {
  background: #2C86F8;
}

.colorBtn {
  width: 30px;
  height: 30px;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>

<style lang="less">
.alarmColor {
  .ivu-dropdown-menu {
    min-width: 10px !important;
    width: 30px !important;
  }
  .ivu-dropdown-item {
    padding: 0 !important;
  }
}

.icon-shengyin_shiti {
  cursor: pointer;
  margin-right: 10px;
}
.icon-shengyin_shiti:hover {
  color: #2C86F8;
}

</style>