.main-container{ 
    position: relative;
}
.faceDetail {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
}
.data-above {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.idCardNo {
    background: rgba(0, 0, 0, 0.7);
    height: 26px;
    line-height: 26px;
    text-align: center;
    margin-top: -26px;
    z-index: 999;
    position: absolute;
    width: 100%;
    color: #2c86f8;
    // font-size: 13px;
    font-weight: 600;
}