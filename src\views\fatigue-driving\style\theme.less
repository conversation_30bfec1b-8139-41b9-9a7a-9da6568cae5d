/deep/ .ui-card {
  height: unset;

  .card-content {
    height: 100%;
  }
}

.card-content-box {
  width: 100%;
  position: relative;
  height: 100%;
}

.light-theme {
  background: unset;
  padding: 0;

  .relationship-map-head {
    color: rgba(0, 0, 0, 0.35);
  }

  .label,
  .left,
  .data-name {
    color: rgba(0, 0, 0, 0.6);
  }

  .swiper-button-prev,
  .swiper-button-next {
    .iconfont {
      color: #fff;
    }

    background: rgba(0, 0, 0, 0.5);

    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }

    &:active {
      background: rgba(0, 0, 0, 1);
    }
  }
}

.dark-theme {
  padding: 20px 40px;
  background: url("~@/assets/img/thematic/left.png") no-repeat,
    url("~@/assets/img/thematic/right.png") no-repeat,
    url("~@/assets/img/thematic/bottom.png") no-repeat,
    url("~@/assets/img/thematic/bg-blue.png") no-repeat;
  background-position: left, right, bottom, center;
  background-size: auto, auto, auto, cover;

  .relationship-map-head {
    color: #7195d0;
  }

  .label,
  .left,
  .data-name {
    color: #fff;
  }

  .swiper-button-prev,
  .swiper-button-next {
    .iconfont {
      color: #32ddff;
      font-size: 18px;
    }

    background: rgba(50, 220, 255, 0.2);

    &:hover {
      background: rgba(50, 220, 255, 0.5);
    }

    &:active {
      background: rgba(50, 220, 255, 0.7);
    }
  }
}