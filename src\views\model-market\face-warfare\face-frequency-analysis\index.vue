<!--
    * @FileDescription: 人员频次分析
    * @Author: H
    * @Date: 2024/01/31
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-08-01 15:08:02
 -->
<template>
  <div class="frequency-analysis container">
    <mapCustom
      ref="mapBase"
      :allCameraList="allCameraList"
      :basicPoints="basicPoints"
      crashType="Camera_Face"
      @closeMapTool="closeMapTool"
      @gettingData="gettingData"
      mapType="frequency"
      sectionName="face"
    />
    <!-- 左面信息展示框 -->
    <left-box
      ref="leftbox"
      @seleArea="handleSeleArea"
      @reset="handleReset"
      @searchAnalyse="handleSearchAnalyse"
      @cutAnalyse="handleCutAnalyse"
    >
    </left-box>
    <right-box
      ref="rightBox"
      v-show="rightShowList"
      @details="handleDetails"
      @cancel="handleCancel"
      @facelist="facelist"
    >
    </right-box>
    <detailsBox
      ref="detailsBox"
      v-show="detailsShow"
      @goback="handleGoback"
      @chooseNormalPoint="chooseNormalPoint"
    ></detailsBox>
  </div>
</template>

<script>
import mapCustom from "../../components/map/index.vue";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import { mapMutations } from "vuex";
import detailsBox from "./components/details-box.vue";
import { myMixins } from "../../mixins/index.js";
import { queryFaceFrequenciesDetailList } from "@/api/modelMarket";

export default {
  name: "",
  mixins: [myMixins],
  components: {
    mapCustom,
    leftBox,
    rightBox,
    detailsBox,
  },
  data() {
    return {
      rightShowList: false,
      detailsShow: false,
      searchData: {},
      basicPoints: [],
    };
  },
  watch: {},
  computed: {},
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  mounted() {},
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),

    handleReset() {
      this.detailsShow = false;
      this.rightShowList = false;
    },
    //
    handleSearchAnalyse(val, item, tabIndex) {},
    //加载检索结果数据
    handleCutAnalyse(data) {
      this.searchData = data;
      this.rightShowList = true;
      this.$refs.rightBox.init(data);
    },
    // 框选设备列表
    gettingData(value) {
      this.$refs.leftbox.showDevice(value);
      this.$refs.mapBase.clearDraw();
    },
    handleSeleArea() {
      this.$refs.mapBase.mapLayerConfig.mapToolVisible = true;
    },
    //点击检索结果列表
    handleDetails(item, index) {
      this.detailsShow = true;
      this.rightShowList = false;
      this.$nextTick(() => {
        this.$refs.detailsBox.init(item, this.searchData, index);
        //点击搜索列表获取点位详情
        this.getDetailData(item, this.searchData, index);
      });
    },
    // 检索结果数据上图
    facelist(list) {
      this.basicPoints = list.map((item) => {
        item.showIconBorder = true;
        item.dataType = "face";
        return item;
      });
      this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, false);
    },
    //点击检索结果列表获取详情列表数据上图
    getDetailData(item, data, index) {
      this.searchParams = { ...data, deivceList: [item.deviceId] };
      this.basicPoints = [];
      this.$refs.mapBase.resetMarkerbasicPoint();
      let params = {
        ...this.searchParams,
        dateType: 2,
        endDate: "",
        startDate: "",
        pageNumber: 1,
        pageSize: item.count || 100,
      };
      queryFaceFrequenciesDetailList(params).then((res) => {
        this.basicPoints = res.data.entities.map((subItem) => {
          subItem.showIconBorder = true;
          subItem.dataType = "face";
          subItem.lat = item.geoPoint.lat;
          subItem.lon = item.geoPoint.lon;
          subItem.geoPoint = item.geoPoint;
          subItem.deviceName = item.deviceName;
          subItem.captureAddress = item.deviceName;
          return subItem;
        });
        this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
        this.$refs.mapBase.setCenter(item);
      });
    },
    //详情列表数据点击展示弹框
    chooseNormalPoint(data) {
      this.basicPoints.forEach((item, index) => {
        if (item.recordId == data.recordId) {
          this.$refs.mapBase.chooseNormalPoint(this.basicPoints, "face", index);
        }
      });
    },
    closeMapTool() {
      this.$refs.mapBase.mapLayerConfig.mapToolVisible = false;
    },
    // 返回
    handleGoback() {
      this.detailsShow = false;
      this.rightShowList = true;
      //   清空频次分析结果数据
      this.basicPoints = [];
      this.$refs.mapBase.resetMarkerbasicPoint();
      //   关闭频次分析结果详情弹框
      this.$refs.mapBase.closeMapDom();
      //   重新加载检索结果数据
      this.$refs.rightBox.init(this.searchData);
    },
    handleCancel() {
      this.rightShowList = false;
      this.$refs.mapBase.resetMarkerbasicPoint();
    },
  },
};
</script>

<style lang="less" scoped>
.frequency-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
