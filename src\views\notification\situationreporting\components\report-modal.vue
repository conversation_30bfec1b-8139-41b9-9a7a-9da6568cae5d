<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :width="detailInfos.reportMethod == 2 && detailInfos.deviceList.length > 0 ? '90rem' : '35rem'"
    @onCancel="handleReset"
    @query="handleSubmit"
    :loading="loading"
  >
    <div v-if="!isBatch" class="detail-content mb-md">
      <Collapse v-model="collapseValue">
        <Panel name="1">
          <div class="header">
            <i class="icon1 icon-font icon-sharedservice f-14 mr-xs"></i>
            <span class="font-color f14">报备信息</span>
          </div>
          <div slot="content">
            <report-info :detailData="detailInfos"></report-info>
          </div>
        </Panel>
      </Collapse>
    </div>
    <div :class="['report-form', !isBatch ? 'report-form-border' : '']">
      <p v-if="isBatch" class="report-form-tips font-color mt-md mb-md">
        当前已选用<span class="font-red ml-xs mr-xs">{{ batchIds.length }}</span
        >条报备信息进行审核：
      </p>
      <Form
        ref="formData"
        :model="formData"
        :rules="ruleValidate"
        label-position="right"
        :label-width="110"
        :label-colon="true"
      >
        <FormItem label="审核结果" prop="status">
          <RadioGroup v-model="formData.status">
            <Radio label="2" class="mr-lg">审核通过</Radio>
            <Radio label="3">审核不通过</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="审核备注">
          <Input
            v-model="formData.reviewRemark"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5 }"
            placeholder="请输入审核备注..."
          ></Input>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>

<script>
import examination from '@/config/api/examination';
export default {
  name: 'report-modal',
  props: {
    title: {
      type: String,
      default: '审核',
    },
    isBatch: {
      type: Boolean,
      default: false,
    },
    batchIds: {
      type: Array,
      default: () => {},
    },
    detailData: {
      type: Object,
      default: () => {},
    },
    userCivilCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      collapseValue: '1',
      formData: {
        status: '2',
        reviewRemark: '',
      },
      ruleValidate: {
        status: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
      },
      number: 0,
      detailInfos: {},
    };
  },
  methods: {
    async init(id) {
      this.visible = true;
      let val = [];
      if (!id) {
        val = this.detailData;
      } else {
        let res = await this.$http.get(examination.getView + `/${id}`);
        val = res.data.data;
      }
      this.formData.status = val.status || this.formData.status;
      this.formData.reviewRemark = val.reviewRemark || '';
      this.detailInfos = this.$util.common.deepCopy(val);
      if (this.detailInfos.fileUrl) {
        this.detailInfos.fileUrl =
          this.detailInfos.fileUrl.indexOf(',') !== -1 ? this.detailInfos.fileUrl.split(',') : [val.fileUrl];
      }
    },
    async handleSubmit() {
      if (this.formData.status == 1 || !this.formData.status) {
        this.formData.status = '';
        this.$Message.warning('请选择审核结果');
        this.$refs.formData.validate();
        return;
      }
      try {
        const params = {
          status: this.formData.status,
          reviewRemark: this.formData.reviewRemark,
          userCivilCode: this.userCivilCode,
        };
        params.ids = this.isBatch ? this.batchIds : [this.detailData.id];
        let { data } = await this.$http.post(examination.reviewData, params);
        this.visible = false;
        this.$emit('updateInfo');
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      }
    },
    handleReset() {
      this.visible = false;
    },
  },
  watch: {
    // detailData: {
    //   handler(val){
    //     this.formData.status = val.status || this.formData.status
    //     this.formData.reviewRemark = val.reviewRemark || ''
    //     this.detailInfos = this.$util.common.deepCopy(val)
    //     if(!!this.detailInfos.fileUrl){
    //       this.detailInfos.fileUrl = this.detailInfos.fileUrl.indexOf(',') !== -1 ? this.detailInfos.fileUrl.split(',') : [val.fileUrl]
    //     }
    //   },
    //   immediate: true
    // }
  },
  components: {
    ReportInfo: require('./report-info.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .icon-sharedservice {
    background: linear-gradient(360deg, #159575 0%, #54f2b3 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}
.font-color {
  color: var(--color-content);
}
.detail-content {
  padding: 10px 20px 0;
  @{_deep} .ivu-form-item {
    margin-bottom: 5px !important;
  }
}
.report-form {
  padding: 10px 20px 0;
  &-border {
    border-top: 1px solid var(--border-modal-footer);
  }
  &-tips {
    padding-left: 30px;
  }
  @{_deep} .ivu-radio {
    margin-right: 10px;
  }
}
@{_deep} .ivu-modal-body {
  padding: 0;
}
@{_deep} .ivu-icon-ios-arrow-forward {
  float: right;
  margin-top: 12px;
  color: #99b9e6;
}
@{_deep} .ivu-collapse {
  border: 0;
  background: var(--bg-content);
  &-header {
    border: 0 !important;
    color: #fff;
  }
  &-item {
    margin-bottom: 5px;
    background: var(--bg-collapse-item);
    border: 0 !important;
    color: #fff;
  }
  &-content {
    background: var(--bg-content);
    &-box {
      padding-bottom: 0;
    }
  }
}
.icon-sharedservice {
  color: var(--color-primary);
}
</style>
