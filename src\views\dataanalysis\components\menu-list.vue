<template>
  <div class="menu-list">
    <div class="input-div mb-md" v-if="showFilterInput">
      <Input v-model="searchText" @keyup.enter.native="enter" placeholder="请输入任务名称" type="text">
        <Icon type="ios-search" slot="suffix" @click="enter" />
      </Input>
    </div>
    <div class="tree-menu auto-fill" v-ui-loading="{ loading: loading, tableData: menuList }">
      <el-tree
        ref="tree"
        :data="menuList"
        node-key="id"
        :accordion="accordion"
        :expand-on-click-node="true"
        :default-expanded-keys="defaultExpandedKeys"
        :default-expand-all="defaultExpandAll"
        :filter-node-method="filterNode"
        :props="defaultProps"
        empty-text=""
        @node-click="handleNodeClick"
      >
        <p class="custom-tree-node" slot-scope="{ node, data }">
          <slot :node="node" :data="data">
            <i
              v-if="iconVisible(data, node).isShow"
              :class="['icon-font', 'f-14', 'menu-icon', iconVisible(data, node).icon]"
            ></i>
            <span class="ml-xs menu-text" :class="!data.children ? 'underline-text' : ''">
              <span class="ellipsis" :title="data[defaultProps.label]">{{ data[defaultProps.label] }}</span>
              <span v-if="showChildrenNum && !!data.children" class="mr-xs">
                (&nbsp;<span class="nodeNum">{{ data.children.length }} </span>&nbsp;)
              </span>
              <slot name="textRight" :node="node" :data="data"> </slot>
            </span>
            <slot name="dropdownBtn" :node="node" :data="data"></slot>
          </slot>
        </p>
      </el-tree>
    </div>
  </div>
</template>
<script>
import { menuConfig } from '@/views/dataanalysis/utils/menuConfig.js';
export default {
  name: 'MenuList',
  props: {
    menuList: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => [],
    },
    defaultExpandAll: {
      type: Boolean,
      default: false,
    },
    accordion: {
      type: Boolean,
      default: false,
    },
    showFilterInput: {
      type: Boolean,
      default: false,
    },
    showChildrenNum: {
      type: Boolean,
      default: false,
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          children: 'children',
        };
      },
    },
  },
  data() {
    return {
      searchText: '',
    };
  },
  methods: {
    enter() {
      this.$refs.tree.filter(this.searchText);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1;
    },
    handleNodeClick(data) {
      if (data.children) {
        return false;
      }
      const menuItem = menuConfig.find((item) => item.factorCode.includes(data.factorCode));
      if (!menuItem) return false;
      const menuData = {
        ...data,
        componentName: menuItem.component,
      };
      this.$emit('clickTreeNode', menuData);
    },
    iconVisible(data, node) {
      return {
        isShow: data.icon,
        icon: node.expanded && data.parentId === '0' ? 'icon-qsb-wenjianjiazhankai' : data.icon,
      };
    },
  },
  watch: {},
};
</script>

<style lang="less" scoped>
.menu-list {
  display: flex;
  flex-direction: column;
}
.tree-menu {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  .pl-lg {
    padding-left: 30px;
  }
  .ivu-icon-md-arrow-dropdown {
    font-size: 20px;
  }
  @{_deep} .ivu-menu {
    width: 100% !important;
    border-radius: 4px;
    &-submenu-title {
      padding-top: 10px !important;
      padding-bottom: 10px !important;
      &:hover {
        text-decoration: underline;
      }
    }
    .ivu-icon-ios-arrow-down {
      display: none !important;
    }
    &-item {
      border-radius: 4px !important;
      display: flex;
      align-items: center;
    }
  }
}
.custom-tree-node {
  display: flex;
  align-items: center;
  width: 100%;
  height: 40px;
  overflow: hidden;
  @{_deep} .icon-more {
    display: none;
  }
  &:hover {
    @{_deep} .icon-more {
      display: block;
    }
  }
  .menu-icon {
    font-size: 20px;
    color: var(--color-btn-default);
  }
  .menu-text {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    overflow: hidden;
  }
  .underline-text {
    &:hover {
      text-decoration: underline;
    }
  }
}
.nodeNum {
  color: var(--color-btn-default);
}
@{_deep} .el-tree-node__content {
  height: 40px !important;
}
</style>
