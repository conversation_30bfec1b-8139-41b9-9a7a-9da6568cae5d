<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" class="confimDataModal" footer-hide>
    <div class="content auto-fill" v-if="!!faceData && faceData.total != 0">
      <div class="container">
        <div class="left-content">
          <evaluation-tree @faceDetailIdx="faceDetailIdx" @clickNode="clickNode" expandAll :nodeData="faceTreeData">
          </evaluation-tree>
        </div>
        <div class="center-content"></div>
        <div class="right-content ml-sm auto-fill">
          <div class="times">
            <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
            <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
            <Button type="primary" class="fr" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
          <div class="echarts-title">
            <line-title title-name="检测结果统计"></line-title>
          </div>
          <div class="chart-box">
            <div class="chart">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :moduleData="moduleData"></no-standard>
          </div>
          <div class="echarts-title">
            <line-title title-name="检测数据列表"></line-title>
          </div>
          <div class="table-box auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="tableColumns"
              :table-data="tableData"
              :loading="loading"
            >
              <template #tagNames="{ row }">
                <Tooltip placement="left" :content="row.tagNames" v-if="row.tagNames != null">
                  <span class="tag-names">{{ row.tagNames }}</span>
                </Tooltip>
              </template>
              <template #description="{ row }">
                <span
                  class="check-status"
                  :class="[
                    row.description === '合格' ? 'bg-b77a2a' : '',
                    row.description === '不合格' ? 'bg-17a8a8' : '',
                    row.description === '无法考核' ? 'bg-D66418' : '',
                  ]"
                >
                  {{ row.description }}
                </span>
              </template>
              <template #qualifiedRate="{ row }">
                <span>{{ row.qualifiedRate }}%</span>
              </template>
              <template #option="{ row }">
                <ui-btn-tip icon="icon-chakantupian" content="查看图片" @click.native="clickRow(row)"></ui-btn-tip>
              </template>
            </ui-table>
            <!-- <div class="no-data" v-if="tableData.length === 0">
            <img src="@/assets/img/common/nodata.png" alt />
          </div> -->
          </div>
          <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
          </ui-page>
        </div>
        <face-focus-url-picture v-model="faceFocusUrlPicture" :row="currentRow"></face-focus-url-picture>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.confimDataModal {
  @{_deep} .ivu-modal-body {
    padding: 50px 20px 0 20px;
  }
  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  .no-box {
    width: 1768px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    position: relative;
    height: 820px;
    .left-content {
      width: calc(306px- 20px);
      float: left;
    }
    .center-content {
      float: left;
      margin-top: -50px;
      width: 1px;
      background: #1b3b65;
    }
    .right-content {
      margin-left: 310px;
      height: 100%;
      background: var(--bg-content);
      .times {
        span:first-child {
          margin-right: 40px;
        }
      }
      .echarts-title {
        margin-bottom: 10px;
      }
      //  视频流头部
      .chart-box {
        width: 100%;
        display: flex;
        height: 180px;
        line-height: 180px;
        align-items: center;
        background-color: var(--bg-sub-content);
        .chart {
          height: 180px;
          line-height: 180px;
          width: 48%;
          .charts {
            margin: 0 auto;
          }
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
      .table-box {
        position: relative;
        .ui-table {
          height: 450px;
        }
        .btn-text-default {
          cursor: pointer;
          font-size: 14px;
          color: var(--color-primary);
        }
        .tag-names {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          display: inline-block;
          width: 150px;
        }
      }
    }
  }
}
</style>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: ['value', 'faceDetailData'],
  data() {
    return {
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 250,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
        },
        { title: '设备标签', key: 'tagNames', slot: 'tagNames', width: 150 },
        { title: '组织机构', key: 'orgName', width: 120 },
        { title: '抽检图片数量', key: 'total', width: 120 },
        { title: 'URL可访问数量', key: 'qualifiedNum', width: 150 },
        { title: 'URL不可访问数量', key: 'unqualifiedNum', width: 150 },
        {
          title: '合格率',
          key: 'qualifiedRate',
          slot: 'qualifiedRate',
          width: 120,
        },
        {
          title: '设备是否合格',
          key: 'description',
          slot: 'description',
          width: 120,
        },
        {
          title: '操作',
          slot: 'option',
          width: 100,
          fixed: 'right',
          align: 'center',
        },
      ],
      tableData: [],
      //树
      minusTable: 560,
      searchData: {
        searchValue: '',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      nodeData: [
        {
          deviceName: '人脸卡口',
          total: 0,
          children: [
            {
              label: '二级 1-1',
            },
          ],
        },
      ],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9.21875rem',
      },
      visible: true,
      loading: false,
      moduleData: {
        rate: '重点人脸卡口设备URL可用率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: 0,
        remarkValue: '',
      },
      //当前行
      currentRow: {},
      echartRing: {},
      ringStyle: {
        width: '600px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['合格设备', '不合格设备'],
        showData: [
          { name: '合格设备', value: 0 },
          { name: '不合格设备', value: 0 },
        ],
        count: null,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      faceData: {},
      device: [],
      deviceCode: '',
      deviceName: '',
      deviceResult: '',
      qualifiedRate: 0,
      faceFocusUrlPicture: false,
      faceTreeData: [], //人脸树形图数据
      id: '',
      outcome: '',
      exportLoading: false,
      deviceData: { deviceId: '', deviceName: '' },
    };
  },
  async mounted() {
    await this.getFaceStatistics();
    await this.initRing();
    await this.init();
    this.getFaceTree();
  },
  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          indexName: this.$parent.row.indexName,
          outcome: this.outcome,
          deviceId: this.deviceData.deviceId,
          deviceName: this.deviceData.deviceName,
        };
        let res = await this.$http.get(governanceevaluation.exportFaceDeviceDetailData, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    //   统计
    async getFaceStatistics() {
      try {
        let data = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.faceDeviceResultOverview, data);
        this.faceData = res.data.data;
        this.moduleData.rateValue = this.faceData.qualifiedRate;
        this.moduleData.priceValue = this.$parent.row.standardsValue;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc;
      } catch (err) {
        console.log(err);
      }
    },
    // 列表
    async init(deviceId) {
      let data = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        searchValue: deviceId,
        outcome: this.outcome,
        pageSize: this.searchData.pageSize,
        pageNumber: this.searchData.pageNum,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.facePageList, data);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    // 树
    async getFaceTree(val) {
      let data = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        outcome: val,
      };
      try {
        let res = await this.$http.post(governanceevaluation.getDeviceDataList, data);
        if (res.data.code === 200) {
          if (!res.data.data || res.data.data.length === '0') {
            this.faceTreeData = res.data.data || [];
          } else {
            let data = [{ deviceName: '人脸卡口', total: res.data.data.length }];
            data[0].children = res.data.data;
            this.faceTreeData = data;
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
    faceDetailIdx(val) {
      this.outcome = val;
      this.getFaceTree(val);
    },
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '合格设备') {
          item.value = this.faceData.qualifiedNum;
        } else {
          item.value = this.faceData.unqualifiedNum;
        }
      });
      this.zdryChartObj.count = this.faceData.total;
      let formatData = {
        seriesName: '检测设备总量',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    clickNode(val) {
      this.deviceData = val;
      this.init(val.deviceId);
      this.searchData.pageSize = this.searchData.pageSize;
      this.searchData.pageNum = 1;
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },

    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    clickRow(row) {
      this.currentRow = row;
      this.faceFocusUrlPicture = true;
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    EvaluationTree: require('../evaluation-tree.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    LineTitle: require('@/components/line-title.vue').default,
    NoStandard: require('../no-standard.vue').default,
    faceFocusUrlPicture: require('./face-focus-url-picture.vue').default,
  },
};
</script>
