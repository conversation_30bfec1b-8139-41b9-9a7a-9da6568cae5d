<template>
  <ui-modal
    v-model="visible"
    :styles="styles"
    footer-hide
    :title="`${commonSearchData.name}-${computedMonitorType}日抓拍趋势`"
  >
    <div class="day-capture-box">
      <div class="statistic-header flex-row">
        <div class="width-lg"></div>
        <DatePicker
          type="date"
          placeholder="请选择"
          class="width-lg"
          v-model="searchData.time"
          format="yyyy-MM-dd"
          :options="DISABLED_AFTER_NOW"
          @on-change="changeDate"
        ></DatePicker>
        <div class="width-lg t-right">
          <span class="base-text-color">日抓拍总量：</span
          ><span class="color-active">{{ requestObj?.captureNumCount }}</span>
        </div>
      </div>
      <div class="line-chart-box width-percent">
        <draw-echarts
          :echart-option="lineChartAttr.echartOption"
          :echart-style="lineChartAttr.echartStyle"
          :echarts-loading="requestLoading"
        ></draw-echarts>
      </div>
      <div class="custom-box width-percent">
        <draw-echarts
          :echart-option="customChartAttr.echartOption"
          :echart-style="customChartAttr.echartStyle"
          :echarts-loading="requestLoading"
        ></draw-echarts>
        <div class="during-nodata-box base-text-color f-14">
          累计无数据时长：<span class="color-warning">{{ requestObj?.duration }}</span>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { chartsFactory } from '../utils/chartsFactory';
import datamonitorApi from '@/config/api/datamonitor';
import { MONITORTYPE_FACE, DISABLED_AFTER_NOW } from '../utils/enum';
import LineChartsTooltip from './LineChartsTooltip.vue';
import Vue from 'vue';

export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    modalRowData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    commonSearchData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      DISABLED_AFTER_NOW,
      styles: {
        width: '5.8rem',
      },
      visible: false,
      lineChartAttr: {
        cuptureMonthOrDay: 'day',
        year: '2023-12-01',
        echartsLoading: false,
        echartOption: {},
        echartStyle: {
          width: '100%',
          height: '325px',
        },
      },
      tooltipFormatter: (param) => {
        let LineChartsTooltipVue = Vue.extend(LineChartsTooltip);
        let _that = new LineChartsTooltipVue({
          el: document.createElement('div'),
          data() {
            return {
              data: param,
              cuptureMonthOrDay: 'date', //  month: 月趋势  date: 日趋势
            };
          },
        });
        return _that.$el.outerHTML;
      },
      customChartAttr: {
        echartsLoading: false,
        echartOption: {},
        echartStyle: {
          width: '100%',
          height: '150px',
        },
      },
      searchData: {
        time: new Date(),
      },
      echartsFactory: {},
      requestLoading: false,
      requestObj: {
        captureNumCount: 0,
        dataBoList: [],
      },
    };
  },
  computed: {
    computedMonitorType() {
      return this.commonSearchData.activeMoinitorType === MONITORTYPE_FACE ? '人脸' : '车辆';
    },
  },
  created() {
    this.echartsFactory = new chartsFactory();
  },
  destroyed() {
    this.echartsFactory = null;
  },
  methods: {
    cancel() {
      this.visible = false;
    },
    getSearchTime() {
      let rowTime = this.modalRowData.showDayTime;
      if (rowTime) {
        this.searchData.time = rowTime.replaceAll('/', '-');
      } else {
        let newDate = new Date();
        this.searchData.time = this.$util.common.formatDate(newDate, 'yyyy-MM-dd');
      }
    },
    changeDate(date) {
      this.searchData.time = date;
      this.getRequestData();
    },
    async getRequestData() {
      try {
        this.requestLoading = true;
        const params = {
          regionCode: this.modalRowData.regionCode || this.commonSearchData.regionCode,
          time: this.searchData.time,
          type: this.commonSearchData.activeMoinitorType,
        };
        const {
          data: { data },
        } = await this.$http.get(datamonitorApi.getCaptureNumByDayDetail, { params });

        this.requestObj = data;
        //获取两个图表的options
        this.setCustomChartOption();
        this.setLineChartOption();
      } catch (err) {
        console.log(err);
      } finally {
        this.requestLoading = false;
      }
    },

    setCustomChartOption() {
      //获取自定义图表option
      let chartData = [
        {
          checkTimeD: this.$util.common.formatDate(this.searchData.time, 'yyyy-MM-dd'),
          duration: this.requestObj.duration,
          timerAxis: this.requestObj.timerAxis,
        },
      ];
      this.customChartAttr.echartOption = this.echartsFactory.getDayCaptureCustomOptions(chartData);
    },
    setLineChartOption() {
      //获取线图option
      let seriesData = [];
      if (this.requestObj.dataBoList?.length) {
        seriesData = this.requestObj.dataBoList.map((item) => {
          return { ...item, value: item.sum };
        });
      }
      //复制一份0点到1点的数据，放在第一位占位
      let firstItem = seriesData.find((item) => item.day === '0');
      if (firstItem) {
        seriesData.unshift(firstItem);
      }
      let lineChartParam = {
        seriesData,
        tooltipFormatter: this.tooltipFormatter,
        cuptureMonthOrDay: 'date',
      };
      this.lineChartAttr.echartOption = this.echartsFactory.getDayCaptureLineOptions(lineChartParam);
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          // 获取时间
          this.getSearchTime();
          this.getRequestData();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20px 60px 50px 60px;
}
.day-capture-box {
  background: var(--bg-info-card);
  padding: 30px;
  .custom-box {
    position: relative;
    .during-nodata-box {
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
}
</style>
