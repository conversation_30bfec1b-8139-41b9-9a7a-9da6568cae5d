<template>
  <div class="comparison">
    <div class="content-title">
      <slot name="contentTitle">
        <div>
          <span>字段名称</span>
        </div>
        <div class="over-flow">
          <i class="icon-font icon-zichankushebei"></i>
          <span class="ml-xs">资产库设备</span>
          <div class="fr" v-if="!onlyRead && isEdit">
            <Tooltip class="ml-sm" placement="right">
              <i class="icon-font icon-wenhao"></i>
              <template #content>
                <div>“填充”可把资产库设备信息自动填入到右侧。</div>
              </template>
            </Tooltip>
            <Button type="text" @click="allFilling">
              <span class="inline vt-middle ml-xs">全部填充</span>
              <i class="icon-font icon-lujing148"></i>
            </Button>
          </div>
        </div>
        <div class="over-flow">
          <i class="icon-font icon-tianbaoshebei"></i>
          <span class="ml-xs">填报设备</span>
          <Button v-if="!onlyRead" class="fr" type="text" @click="edit" :loading="saveLoading">
            <span class="inline vt-middle">{{ isEdit ? '保存' : '编辑' }}</span>
          </Button>
        </div>
      </slot>
      <slot name="increaseTitle"></slot>
    </div>
    <div class="content-item" v-for="(item, index) in fieldList" :key="index">
      <slot name="content" :row="item">
        <div>
          <span>{{ item.fieldDesc }}({{ item.fieldName }})</span>
        </div>
        <div class="over-flow">
          <span :class="[sign === 'differ' ? isAbnormal(item) : '', 'fl']">{{ item.fieldValueForDevice }}</span>
          <Button
            type="text"
            class="fr"
            v-if="
              !onlyRead && isEdit && !!item.fieldValueForDevice && item.fieldValueForDevice !== item.fieldValueForFillIn
            "
            @click="filling(item)"
          >
            <span class="inline vt-middle ml-xs">填充</span>
            <i class="icon-font icon-lujing148"></i>
          </Button>
        </div>
        <div>
          <span v-show="!isEdit" :class="[isAbnormal(item)]">{{ item.fieldValueForFillIn }}</span>
          <Input v-show="isEdit" v-model="item.fieldValueForFillIn" :class="['width-lg', isAbnormal(item)]"></Input>
        </div>
      </slot>
      <slot name="increaseContent" :row="item"></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    onlyRead: {
      type: Boolean,
      default: false,
    },
    /**
     * {
     *    label: 中文名称,
     *
     * }
     */
    fieldList: {
      type: Array,
      default: () => [],
    },
    checkFun: {
      type: Function,
      default: () => {
        return true;
      },
    },
    saveFun: {
      type: Function,
      default: () => {},
    },
    sign: {},
  },
  data() {
    return {
      isEdit: false,
      saveLoading: false,
    };
  },
  created() {},
  methods: {
    edit() {
      if (this.isEdit) {
        this.$UiConfirm({
          content: '您确定要保存该设备信息?',
          title: '警告',
        })
          .then(async () => {
            this.saveLoading = true;
            let deviceData = {};
            this.fieldList.forEach((row) => {
              deviceData[row.fieldName] = row.fieldValueForFillIn;
            });
            //是否继续入库
            const isContinue = await this.checkFun(deviceData);
            if (!isContinue) {
              this.saveLoading = false;
              return;
            }
            await this.saveFun(deviceData);
            this.saveLoading = false;
            this.isEdit = !this.isEdit;
          })
          .catch((err) => {
            console.log(err);
            this.saveLoading = false;
          });
      } else {
        this.isEdit = !this.isEdit;
      }
    },
    filling(item) {
      this.$set(item, 'fieldValueForFillIn', item.fieldValueForDevice);
    },
    allFilling() {
      this.fieldList.forEach((row) => {
        row.fieldValueForFillIn = row.fieldValueForDevice;
      });
    },
    isAbnormal(item) {
      if (this.sign === 'differ' && item.isDiffer === '1') {
        return 'abnormal';
      } else if (this.sign === 'unqualified' && item.isUnqualified === '1') {
        return 'abnormal';
      }
    },
  },
  watch: {},
  computed: {
    canEdit() {
      let bool = false;
      this.fieldList.forEach((row) => {
        if (row.fieldValueForFillIn === row.fieldValueForDevice) {
          bool = true;
        }
      });
      return bool;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .comparison {
    .content-title {
      color: rgba(0, 0, 0, 0.9);
      .over-flow {
        .icon-font {
          color: var(--color-primary);
        }
      }
    }
  }
}
.comparison {
  height: 600px;
  overflow-y: auto;
  .content-item {
    border-right: 1px solid var(--border-modal-footer);
    color: var(--color-content);
    line-height: 35px;
    display: flex;
    justify-content: space-between;
    @{_deep}.abnormal {
      color: var(--bg-markers);
      input {
        color: var(--bg-markers);
      }
    }
    > div {
      flex: 1;
      border-right: 1px solid var(--border-modal-footer);
      padding: 5px 20px;
      &:last-of-type {
        border-right: none;
      }
    }
  }
  .content-title {
    display: flex;
    justify-content: space-between;
    background-color: var(--bg-table-header-th);
    line-height: 35px;
    color: var(--color-bluish-green-text);
    .icon-wenhao {
      background-image: linear-gradient(to bottom, #f58d3d, #b8580d);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    > div {
      flex: 1;
      border-right: 1px solid var(--border-modal-footer);
      padding: 0 20px;
      &:last-of-type {
        border-right: none;
      }
    }
  }
  .icon-lujing148 {
    transform: rotateZ(-90deg);
  }
}
</style>
