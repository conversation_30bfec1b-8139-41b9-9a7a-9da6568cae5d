<template>
  <div class="today-duty" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-kaohepaihangbang"> 今日值班 </HomeTitle>
    <div class="duty-list">
      <div class="list-wrapper">
        <div class="list-item title f-14 mb-sm">
          <span>区县</span>
          <span>值班人员</span>
          <span>联系方式</span>
        </div>
        <div class="item-wrapper scroll-y" v-ui-loading="{ loading: loading, tableData: dutyList }">
          <div class="list-item base-text-color f-14 mb-sm" v-for="(item, index) in dutyList" :key="index">
            <span class="orgName ellipsis" :title="item.orgName">{{ item.orgName || '--' }}</span>
            <span class="name ellipsis" :title="item.userName">{{ item.userName || '--' }}</span>
            <span class="phone ellipsis" :title="item.phoneNum">{{ item.phoneNum || '--' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.today-duty {
  height: 31%;
  width: 400px;
  position: absolute;
  right: 10px;
  top: 10px;
  background: rgba(0, 104, 183, 0.13);
  .duty-list {
    height: calc(100% - 32px);
    .list-wrapper {
      height: 100%;
      padding: 10px;
      .list-item {
        display: flex;
        span {
          flex: 1;
        }
      }
      .item-wrapper {
        height: calc(100% - 21px);
      }
      .title {
        color: #68a6f6;
      }
      .name {
        color: #2fcedb;
      }
      .phone {
        color: #36cd9f;
      }
    }
  }
}
.full-screen-container {
  position: absolute;
  top: 9%;
  height: 26%;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'plant-assets',

  data() {
    return {
      dutyList: [],
      loading: false,
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  created() {
    this.initList();
  },
  methods: {
    async initList() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.get(home.getTodayDutyInfo);
        this.dutyList = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
  components: {
    HomeTitle: require('./home-title').default,
  },
};
</script>
