<template>
  <div class="aggre-connect">
    <aggregate :aggregateOptions="propData" :dWidth="dWidth" :bWidth="bWidth"></aggregate>
    <connecting-arrow :connectingOptions="propData.connectingOptions"></connecting-arrow>
  </div>
</template>
<script>
import Aggregate from '@/views/datagovernance/governancetheme/components/aggregate.vue';
import ConnectingArrow from '@/views/datagovernance/governancetheme/components/connecting-arrow.vue';
export default {
  props: {
    propData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dWidth: {
      type: String,
      default: '0',
    },
    bWidth: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    Aggregate,
    ConnectingArrow,
  },
};
</script>
<style lang="less" scoped>
// .aggre-connect {
//   position: absolute;
//   display: flex;
//   align-items: center;
//   // justify-content: flex-end;
//   // width: 100%;
//   // height: 100%;
//   padding: 0;
//   // margin: 0;
//   // &-left{
//   //     // position: absolute;
//   //
//   &-right {
//     flex: 1;
//     margin: 0 8px;
//   }
// }
</style>
