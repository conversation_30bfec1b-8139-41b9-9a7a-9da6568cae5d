<template>
  <div class="search-box">
    <div class="left-box">
      <Form ref="searchRef" :model="formData" class="form" inline>
        <FormItem label="算法名称:" prop="name" class="search-input">
          <Input
            placeholder="请输入"
            clearable
            v-model="formData.name"
            maxlength="50"
          />
        </FormItem>
        <FormItem label="模型名称:" prop="llmModelNames" class="search-input">
          <Select v-model="formData.llmModelNames" clearable multiple>
            <Option
              :value="item.modelName"
              :key="item.modelName"
              v-for="item in modalList"
              >{{ item.modelName }}
            </Option>
          </Select>
        </FormItem>
        <FormItem class="btn-group">
          <Button type="primary" @click="searchHandler">查询</Button>
          <Button @click="resetHandler">重置</Button>
        </FormItem>
      </Form>
    </div>
    <div class="right-box">
      <Button @click="addHandler">
        <ui-icon type="jia" color="#2C86F8"></ui-icon>
        新增
      </Button>
      <Button @click="handleDelBatch">
        <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
        批量删除
      </Button>
    </div>
  </div>
</template>

<script>
export default {
  name: "Search",
  props: {
    modalList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      formData: {
        llmModelNames: [],
        name: "",
      },
    };
  },
  mounted() {},
  methods: {
    searchHandler() {
      this.$emit("searchHandler", { ...this.formData });
    },
    resetHandler() {
      this.formData = {
        llmModelNames: "",
        name: "",
      };
    },
    addHandler() {
      this.$emit("addHandler");
    },
    handleDelBatch() {
      this.$emit("handleDelBatch");
    },
    getSearchParam() {
      return {
        ...this.formData,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.search-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #d3d7de;
  .form {
    width: 100%;
    .search-input {
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input {
        width: 200px;
      }
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .label-text {
      /deep/.ivu-form-item-label {
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines !important; // 这行必加，兼容ie浏览器
        width: 72px;
        white-space: nowrap;
      }
    }
    .btn-group {
      margin-right: 0;
    }
  }
  .right-box {
    display: flex;
    gap: 10px;
  }
}
</style>
