<template>
  <div class="charts-container" ref="statisticsContainer">
    <div
      :class="{ 'charts-item': true, [`bg-${index % 5}`]: true }"
      v-for="(item, index) in abnormalCount"
      :key="index + item.title"
    >
      <div class="charts-item-container" v-if="!item.gatherArea">
        <!-- 不传icon，展示默认图标 -->
        <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
        <span
          :class="{
            'icon-font': true,
            'f-40': true,
            [item.icon]: true,
            [`icon-${index % 5}`]: true,
          }"
        ></span>
        <div class="number-wrapper">
          <p class="f-12 base-text-color">{{ item.title }}</p>
          <p class="f-18 color-num">{{ commaNumber(item.total || 0) }}</p>
        </div>
      </div>
      <Poptip
        trigger="hover"
        placement="right-start"
        transfer
        transfer-class-name="statistics"
        :width="statisticsIndex === 1 ? 660 : 460"
        v-else
        @on-popper-show="popperShowHandle(index)"
      >
        <div class="charts-item-container">
          <!-- 不传icon，展示默认图标 -->
          <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
          <span
            :class="{
              'icon-font': true,
              'f-40': true,
              [item.icon]: true,
              [`icon-${index % 5}`]: true,
            }"
          ></span>
          <div class="number-wrapper">
            <p class="f-12 base-text-color">{{ item.title }}</p>
            <p class="f-18 color-num">{{ commaNumber(item.total || 0) }}</p>
          </div>
          <span class="icon-font icon-dabiao" v-if="item.thresholdReached"></span>
          <span class="icon-font icon-budabiao" v-else></span>
        </div>
        <div slot="content" class="standard-model">
          <ui-table
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="item.gatherArea || []"
            :loading="loading"
            :maxHeight="340"
          >
            <template #faceBayonet="{ row }">
              <span
                v-if="row.faceBayonet"
                :class="[row.faceBayonet.total < row.faceBayonet.threshold ? 'font-red' : '']"
              >
                {{ row.faceBayonet.total }}
              </span>
            </template>

            <template #vehicleBayonet="{ row }">
              <span
                v-if="row.vehicleBayonet"
                :class="[row.vehicleBayonet.total < row.vehicleBayonet.threshold ? 'font-red' : '']"
              >
                {{ row.vehicleBayonet.total }}
              </span>
            </template>
            <template #videoMonitor="{ row }">
              <span
                v-if="row.videoMonitor"
                :class="[row.videoMonitor.total < row.videoMonitor.threshold ? 'font-red' : '']"
              >
                {{ row.videoMonitor.total }}
              </span>
            </template>
          </ui-table>
        </div>
      </Poptip>
    </div>
  </div>
</template>
<script>
const renderTableHeader = (h, params) => {
  return h('div', [h('div', {}, params.column.title), h('div', {}, '上报数量')]);
};
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    abnormalCount: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      statisticsIndex: -1,
      loading: false,
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 60,
          align: 'center',
        },
        {
          width: 100,
          title: '采集区域编码',
          key: 'gatherAreaCode',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: '重点采集区域',
          key: 'gatherAreaDesc',
          tooltip: true,
        },
      ],
      tableColumns: [],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {
    this.tableColumns = this.columns.concat([
      {
        width: 100,
        title: '人脸卡口',
        slot: 'faceBayonet',
        align: 'center',
        renderHeader: renderTableHeader,
      },
      {
        width: 100,
        title: '车辆卡口',
        slot: 'vehicleBayonet',
        align: 'center',
        renderHeader: renderTableHeader,
      },
      {
        width: 100,
        title: '视频监控',
        slot: 'videoMonitor',
        align: 'center',
        renderHeader: renderTableHeader,
      },
    ]);
  },
  mounted() {},
  methods: {
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
    popperShowHandle(index) {
      this.statisticsIndex = index;
      switch (index) {
        case 1:
          this.tableColumns = this.columns.concat([
            {
              width: 100,
              title: '人脸卡口',
              slot: 'faceBayonet',
              align: 'center',
              renderHeader: renderTableHeader,
            },
            {
              width: 100,
              title: '车辆卡口',
              slot: 'vehicleBayonet',
              align: 'center',
              renderHeader: renderTableHeader,
            },
            {
              width: 100,
              title: '视频监控',
              slot: 'videoMonitor',
              align: 'center',
              renderHeader: renderTableHeader,
            },
          ]);
          break;
        case 2:
          this.tableColumns = this.columns.concat([
            {
              width: 100,
              title: '人脸卡口',
              slot: 'faceBayonet',
              align: 'center',
              renderHeader: renderTableHeader,
            },
          ]);
          break;
        case 3:
          this.tableColumns = this.columns.concat([
            {
              width: 100,
              title: '车辆卡口',
              slot: 'vehicleBayonet',
              align: 'center',
              renderHeader: renderTableHeader,
            },
          ]);
          break;
        case 4:
          this.tableColumns = this.columns.concat([
            {
              width: 100,
              title: '视频监控',
              slot: 'videoMonitor',
              align: 'center',
              renderHeader: renderTableHeader,
            },
          ]);
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.charts-container {
  display: flex;
  flex-direction: row;
  .charts-item {
    width: 304px;
    min-width: 200px;
    height: 88px;
    margin-right: 10px;
    background: #0f2f59;
    cursor: pointer;
    border-radius: 4px;
    .number-wrapper {
      display: inline-block;
      margin-left: 16px;
    }
    .icon-dabiao,
    .icon-budabiao {
      position: absolute;
      right: 10px;
      top: 10px;
      font-size: 16px;
      line-height: 16px;
      color: var(--color-success);
    }
    .icon-budabiao {
      color: var(--color-warning);
    }
    /deep/ .ivu-poptip {
      width: 100%;
      height: 100%;
      .ivu-poptip-rel {
        width: 100%;
        height: 100%;
      }
    }
    .charts-item-container {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      width: 100%;
    }
  }
  .f-40 {
    font-size: 40px;
  }
  .color-num {
    font-weight: bold;
    line-height: 18px;
    margin-top: 6px;
    // color: #19d5f6;
    // color: inherit;
  }
  .icon-0 {
    background: var(--icon-card-gradient-cyan);
    -webkit-background-clip: text;
    background-clip: text;
    color: #0000;
  }
  .icon-1 {
    background: var(--icon-card-gradient-green);
    -webkit-background-clip: text;
    background-clip: text;
    color: #0000;
  }
  .icon-2 {
    background: var(--icon-card-gradient-orange);
    -webkit-background-clip: text;
    background-clip: text;
    color: #0000;
  }
  .icon-3 {
    background: var(--icon-card-gradient-purple);
    -webkit-background-clip: text;
    background-clip: text;
    color: #0000;
  }
  .icon-4 {
    background: var(--icon-card-gradient-blue);
    -webkit-background-clip: text;
    background-clip: text;
    color: #0000;
  }
  .bg-0 {
    cursor: default;
    background: var(--bg-card-cyan);
    .color-num {
      color: var(--font-card-cyan);
    }
  }
  .bg-1 {
    background: var(--bg-card-green);
    .color-num {
      color: var(--font-card-green);
    }
  }
  .bg-2 {
    background: var(--bg-card-orange);
    .color-num {
      color: var(--font-card-orange);
    }
  }
  .bg-3 {
    background: var(--bg-card-purple);
    .color-num {
      color: var(--font-card-purple);
    }
  }
  .bg-4 {
    background: var(--bg-card-blue);
    .color-num {
      color: var(--font-card-blue);
    }
  }
}
</style>
