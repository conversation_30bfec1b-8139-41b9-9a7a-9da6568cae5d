<template>
  <div class="key-equiment-report-config">
    <ui-modal v-model="visible" title="配置上报达标数量" width="61rem">
      <div class="table-module auto-fill">
        <Form ref="formData" class="table-module auto-fill" :model="formData" :label-width="0">
          <ui-table
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="formData.tableData"
            :loading="loading"
          >
            <template #faceCount="{ index }">
              <FormItem
                :key="'faceCount' + index"
                :prop="`tableData.${index}.faceCount`"
                :rules="{
                  required: true,
                  type: 'number',
                  message: '',
                  trigger: 'blur',
                }"
              >
                <InputNumber
                  v-model="formData.tableData[index].faceCount"
                  class="table-width-md"
                  placeholder="需上报数量"
                  :max="9999"
                  :min="0"
                  @keyup.native="inputChange(index, 'faceCount')"
                />
              </FormItem>
            </template>
            <template #vehicleCount="{ index }">
              <FormItem
                :key="'vehicleCount' + index"
                :prop="`tableData.${index}.vehicleCount`"
                :rules="{
                  required: true,
                  type: 'number',
                  message: '',
                  trigger: 'blur',
                }"
              >
                <InputNumber
                  v-model="formData.tableData[index].vehicleCount"
                  class="table-width-md"
                  placeholder="需上报数量"
                  :max="9999"
                  :min="0"
                  @keyup.native="inputChange(index, 'vehicleCount')"
                />
              </FormItem>
            </template>
            <template #videoCount="{ index }">
              <FormItem
                :key="'videoCount' + index"
                :prop="`tableData.${index}.videoCount`"
                :rules="{
                  required: true,
                  type: 'number',
                  message: '',
                  trigger: 'blur',
                }"
              >
                <InputNumber
                  v-model="formData.tableData[index].videoCount"
                  class="table-width-md"
                  placeholder="需上报数量"
                  :max="9999"
                  :min="0"
                  @keyup.native="inputChange(index, 'videoCount')"
                />
              </FormItem>
            </template>
          </ui-table>
        </Form>
      </div>
      <template #footer>
        <Button @click="visible = false">取 消</Button>
        <Button :loading="isSubmitLoading" type="primary" @click="submit()">确 定</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
const renderTableHeader = (h, params) => {
  return h('div', [h('div', {}, params.column.title), h('div', {}, '需上报数量')]);
};
import viewassets from '@/config/api/viewassets';
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      visible: false,
      tableColumns: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center',
        },
        {
          width: 120,
          title: '采集区域编码',
          key: 'code',
        },
        {
          minWidth: 180,
          title: '重点采集区域',
          key: 'name',
        },
        {
          width: 140,
          title: '人脸卡口',
          slot: 'faceCount',
          align: 'center',
          renderHeader: renderTableHeader,
        },
        {
          width: 140,
          title: '车辆卡口',
          slot: 'vehicleCount',
          align: 'center',
          renderHeader: renderTableHeader,
        },
        {
          width: 140,
          title: '视频监控',
          slot: 'videoCount',
          align: 'center',
          renderHeader: renderTableHeader,
        },
      ],
      formData: {
        tableData: [],
      },
      loading: false,
      isSubmitLoading: false,
    };
  },
  methods: {
    show() {
      this.visible = true;
      this.loading = true;
      this.$nextTick(() => {
        this.$refs.formData.resetFields();
        this.$http
          .post(viewassets.getKeyEquipmentConfig, {})
          .then((res) => {
            this.formData.tableData = res.data.data;
          })
          .catch(() => {})
          .finally(() => {
            this.loading = false;
          });
      });
    },
    inputChange(index, key) {
      let str = '' + this.formData.tableData[index][key];
      if (str.indexOf('.') != -1) {
        let arr = str.split('');
        arr.splice(arr.length - 1);
        let str2 = arr.join('');
        this.formData.tableData[index][key] = +str2;
      }
    },
    submit() {
      this.$refs.formData.validate((valid) => {
        if (valid) {
          this.isSubmitLoading = true;
          this.$http
            .put(viewassets.putKeyEquipmentConfig, this.formData.tableData)
            .then(() => {
              this.$Message.success('配置成功');
              this.$emit('on-handle-success', '');
              this.visible = false;
            })
            .catch(() => {})
            .finally(() => {
              this.isSubmitLoading = false;
            });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.table-module {
  height: 640px;
}
.table-width-md {
  width: 100px;
}
/deep/ .ivu-form-item {
  margin-bottom: 0;
}
</style>
