<template>
  <div class="auto-fill">
    <FileAccuracy v-if="[602, 604].includes(currentTree.id)" :currentTree="currentTree" :taskObj="taskObj" />
    <FileConfidence v-if="[601, 603].includes(currentTree.id)" :currentTree="currentTree" :taskObj="taskObj" />
  </div>
</template>
<script>
export default {
  components: {
    FileAccuracy: require('./file-accuracy').default,
    FileConfidence: require('./file-confidence').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped></style>
