<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #description="{ row }">
        <span :class="row.outcome == '1' ? 'sucess' : 'error'">{{ row.description }}</span>
      </template>
      <template #unqualifiedNum="{ row, column }">
        <span class="unqualified-color">{{ row[column.key] }}</span>
      </template>
      <template #qualifiedRate="{ row }">
        <span :class="row.outcome === '1' ? '' : 'unqualified-color'">{{ row.qualifiedRate || 0 }}%</span>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakantupian"
          content="查看图片"
          :styles="{ color: 'var(--color-primary)' }"
          @click.native="clickRow(row)"
        >
        </ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <CheckPicture
      v-model="checkPicture"
      :list="currentRow"
      :tagList="tagList"
      :active-index-item="activeIndexItem"
      img-key="scenePath"
    ></CheckPicture>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';
export default {
  name: 'image-quality',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: {},
      currentRow: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      checkPicture: false, //查看图片
      // 1 - 合格、2 - 不合格
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
    };
  },
  methods: {
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    CheckPicture: require('@/views/specialassessment/special-modules/components/check-picture/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
.unqualified-color {
  color: #ea4a36;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
