const licensePlateColors = {
    '1': {
        name: '黑',
        borderColor: '#fff',
        style: {
            background: '#000',
            color: '#fff'
        }
    },
    '2': {
        name: '白',
        borderColor: '#D3D7DE',
        style: {
            background: '#ffffff',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '5': {
        name: '蓝',
        borderColor: '#fff',
        style: {
            background: '#2379F9',
            color: '#fff'
        }
    },
    '6': {
        name: '黄',
        borderColor: '#fff',
        style: {
            background: '#FDEE38',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '9': {
        name: '绿',
        borderColor: '#fff',
        style: {
            background: '#67D28D',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '15':{
        name: '黄绿',
        borderColor: '#fff',
        style: {
            background: 'linear-gradient(to right,#FDEE38 20%,#67D28D 80%)',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '16':{
        name: '渐变绿',
        borderColor: '#fff',
        style: {
            // background: '#67D28D',
            backgroundImage: 'linear-gradient(to bottom, #fff, #67D28D)',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '99': {
        name: '其他',
        borderColor: '#fff',
        style: {
            background: '#D9D9D9',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'J': {
        name: '黑',
        borderColor: '#fff',
        style: {
            background: '#000',
            color: '#fff'
        }
    },
    'A': {
        name: '白',
        borderColor: '#D3D7DE',
        style: {
            background: '#ffffff',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'B': {
        name: '灰',
        borderColor: '#D3D7DE',
        style: {
            background: '#D9D9D9',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'E': {
        name: '红',
        borderColor: '#fff',
        style: {
            background: '#EB4B4B',
            color: '#fff'
        }
    },
    'H': {
        name: '蓝',
        borderColor: '#fff',
        style: {
            background: '#2379F9',
            color: '#fff'
        }
    },
    'C': {
        name: '黄',
        borderColor: '#fff',
        style: {
            background: '#FDEE38',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '7': {
        name: '橙',
        borderColor: '#fff',
        style: {
            background: '#F29F4C',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'I': {
        name: '棕',
        borderColor: '#fff',
        style: {
            background: '#C49F6C',
            color: '#fff'
        }
    },
    'G': {
        name: '绿',
        borderColor: '#fff',
        style: {
            background: '#67D28D',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'F': {
        name: '紫',
        borderColor: '#fff',
        style: {
            background: '#A627A4',
            color: '#fff'
        }
    },
    '11': {
        name: '青',
        borderColor: '#fff',
        style: {
            background: '#7FF2FF',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'D': {
        name: '粉',
        borderColor: '#fff',
        style: {
            background: '#E8A1B7',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    '13': {
        name: '透明',
        borderColor: '#fff',
        style: {
            background: '#67D28D',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    },
    'Z': {
        name: '其他',
        borderColor: '#fff',
        style: {
            background: '#D9D9D9',
            color: 'rgba(0, 0, 0, 0.8)'
        }
    }
}
export default licensePlateColors