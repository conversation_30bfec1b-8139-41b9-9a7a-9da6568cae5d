<template>
  <div class="check-picture">
    <div class="check-picture-content auto-fill">
      <div class="check-picture-header">
        <ui-label class="mr-lg inline" label="检测结果：" :width="70">
          <ui-switch-tab
            class="inline"
            v-model="searchData.outcome"
            :tab-list="tagList"
            @changeTab="changeTab"
          ></ui-switch-tab>
        </ui-label>
        <ui-label class="inline" label="异常原因" :width="70">
          <Select
            v-model="searchData.causeErrors"
            class="width-lg fr"
            clearable
            multiple
            placeholder="请选择异常原因"
            :max-tag-count="1"
            @on-change="changeErrorReason"
          >
            <Option v-for="(item, index) in checkList" :key="index" :value="item.value">{{ item.label }} </Option>
          </Select>
        </ui-label>
      </div>
      <div class="check-content-box auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
        <pic-mode :card-list="cardList" :pic-mode-count="9" @algorithmsReview="algorithmsReview"></pic-mode>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    <carPropertyDialog ref="carPropertyDialog"></carPropertyDialog>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import detectionResult from '@/config/api/detectionResult';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
export default {
  mixins: [particularMixin, dealWatch],
  props: {
    //currentRow 当前行
    list: {
      type: Object,
      default: () => {},
    },
    tagList: {
      type: Array,
      default: () => [],
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    getParams: {},
  },
  data() {
    return {
      searchData: {
        outcome: '1',
        causeErrors: [],
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      loading: false,
      checkList: [],
      artificialVisible: false,
      artificialRow: {},
      cardList: [],
      detailData: {},
      mode: 'device', //默认设备模式  type: 'device', type: 'detail',
      errorCodeList: [], //图片模式错误原因
    };
  },
  created() {
    this.init();
    this.getQualificationList(2);
    if (this.colorType.length === 0) {
      this.getcolorType();
    }
    if (this.vehicleBandType.length === 0) {
      this.getvehicleBandType();
    }
    if (this.vehicleClassType.length === 0) {
      this.getvehicleClassType();
    }
    if (this.plateClassType.length === 0) {
      this.getplateClassType();
    }
  },
  methods: {
    ...mapActions({
      getcolorType: 'algorithm/getcolorType',
      getvehicleBandType: 'algorithm/getvehicleBandType',
      getvehicleClassType: 'algorithm/getvehicleClassType',
      getplateClassType: 'algorithm/getplateClassType',
    }),
    // mode 1:设备模式，2:图片模式
    // 异常原因
    async getQualificationList(mode) {
      try {
        const params = {
          indexType: this.activeIndexItem.jobType,
          model: mode,
        };
        let {
          data: { data },
        } = await this.$http.get(detectionResult.getUnqualifiedInfo, { params });
        const datas = data || [];
        this.checkList = datas.map((item) => {
          // 嘉鹏说: 设备模式查询需转换数字模式
          return { value: mode == 1 ? Number(item.key) : item.key, label: item.value };
        });
      } catch (e) {
        console.log(e);
      }
    },
    algorithmsReview(row) {
      this.$refs.carPropertyDialog.init(row.id, row.indexId);
    },

    changeErrorReason(val) {
      this.searchData.causeErrors = val;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        const params = {
          indexId: this.activeIndexItem.indexId,
          batchId: this.activeIndexItem.batchId,
          access: 'TASK_RESULT',
          displayType: 'REGION',
          orgRegionCode: this.activeIndexItem.civilCode,
          customParameters: {
            outcome: this.searchData.outcome,
            causeErrors: this.searchData.causeErrors || [],
            faceDeviceDetailId: this.list.id, //人脸
            deviceIds: (this.list.deviceId && this.list.deviceId.split(',')) || [], //车辆参数
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        let entities = data.entities || [];
        this.cardList = entities.map((item) => {
          let importFileList = [
            {
              label: '车辆类型：',
              value: this.$options.filters.filterType(
                item.vehicleClass,
                this.vehicleClassType,
                'dictKey',
                'dictValue',
                '缺失',
              ),
            },
            {
              label: '车辆品牌：',
              value: this.$options.filters.filterType(
                item.vehicleBrand,
                this.vehicleBandType,
                'dictKey',
                'dictValue',
                '缺失',
              ),
            },
            {
              label: '车身颜色：',
              value: this.$options.filters.filterType(
                item.vehicleColor,
                this.colorType,
                'dictKey',
                'dictValue',
                '缺失',
              ),
            },
            { label: '车辆型号：', value: item.vehicleModel },
          ];
          // 处理图片模式字段展示
          item.fileList = [
            { label: '车牌号：', value: item.plateNo },
            {
              label: '车牌颜色：',
              value: this.$options.filters.filterType(item.plateColor, this.colorType, 'dictKey', 'dictValue', '缺失'),
            },
            //重点
            ...(this.activeIndexItem.jobType === 'VEHICLE_FULL_INFO_IMPORTANT' ? importFileList : ''),
            { label: '抓拍时间：', value: item.shotTime },
            { label: '抓拍地点：', value: item.address },
          ];
          return item;
        });
        this.searchData.totalCount = data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changeTab(val) {
      this.$set(this.searchData, 'outcome', val);
      this.init();
    },
    // 大图展示
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = val;
      this.init();
    },
  },
  computed: {
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
  },
  components: {
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    LookScene: require('@/components/look-scene.vue').default,
    carPropertyDialog:
      require('@/views/governanceevaluation/evaluationoverview/components/car/component/car-property-dialog.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.check-picture {
  &-content {
    width: 100%;
    height: 800px;
  }

  &-header {
    display: flex;
    justify-content: space-between;
    padding: 16px 0;
    border-bottom: 1px solid var(--border-table);
  }

  @media screen and (max-width: 1366px) {
    .ui-gather-card {
      margin-right: 10px;
      width: 186px !important; /*no*/
      height: 220px !important; /*no*/
    }

    @{_deep}.ivu-modal {
      width: 1202px !important; /*no*/
    }

    .ui-image-card {
      height: 170px !important; /*no*/
      width: 170px !important; /*no*/
      cursor: pointer;
    }

    .image-box {
      @{_deep}.ui-image {
        z-index: initial;

        .ui-image-div {
          .tileImage {
            height: 170px !important; /*no*/
            width: 170px !important; /*no*/
          }
        }
      }
    }

    .icon-box {
      width: 91% !important; /*no*/
      top: 156px !important; /*no*/
    }
  }

  @{_deep} .ivu-modal-header {
    padding: 0;
  }

  @{_deep} .ivu-modal-body {
    height: 100%;
  }

  @{_deep} .ivu-modal-content {
    width: 100%;
    height: 100%;
  }

  .check-content-box {
    height: 88%;
    padding-top: 16px;
  }

  .check-content-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    overflow-y: auto;
    margin-top: 10px;

    .empty-item {
      width: 188px; /*no*/
    }
  }

  .ui-gather-card {
    width: 188px; /*no*/
    height: auto; /*no*/
    margin-bottom: 10px;
    padding: 10px;
    position: relative;

    .image-box {
      position: relative;

      @{_deep}.ui-image {
        z-index: initial;

        .ui-image-div {
          .tileImage {
            height: 190px;
            width: 100%;
          }
        }
      }
    }

    &-right {
      flex: 1;

      &-item {
        margin-bottom: 8px;
        font-size: 12px;

        &-label {
          color: #8797ac !important;
          font-size: 12px;
        }

        &-value {
          color: #8797ac !important;
          font-size: 12px;
        }

        .wrapper {
          width: 100%;
        }
      }
    }
  }

  .icon-box {
    position: absolute;
    width: 100%;
    bottom: 0;
    // z-index: 99;
    padding: 0 5px;
    text-align: center;
    line-height: 30px;

    background: rgba(0, 0, 0, 0.39);

    .icon-inner-box {
      background-color: transparent;
    }

    .pointer {
      width: 100%;
      text-align: center;
    }
  }

  .ui-gather-card-image-item {
    font-size: 14px;
    margin: 4px 0 4px 0;
  }

  .ui-image-card {
    height: 190px; /*no*/
    width: 100%;
    cursor: pointer;
  }

  .img:hover {
    .shadow-artificial {
      display: block;
    }
  }

  .check-list {
    // width: 460px;
    margin-left: 80px;
    margin-top: 10px;
  }

  .check-text {
    display: inline-block;
    // width: 110px;
  }

  .desc {
    margin-left: 80px;
    width: 80%;
  }

  .shadow-artificial {
    height: 28px;
    line-height: 29px;
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 172px;
    width: 90%;
    display: none;
    padding-left: 10px;

    .artificial-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }

    @media screen and (max-width: 1366px) {
      .artificial-text {
        color: var(--color-primary);
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }

  .artificial-data {
    padding: 0 50px;
  }

  @{_deep} .pic-mode {
    &-item {
      width: calc(calc(100% - 80px) / 9) !important;
      //min-width: 189px;
    }
  }

  [data-theme='dark'] {
    .ui-gather-card {
      background: #0f2f59;
    }
  }

  [data-theme='light'],
  [data-theme='deepBlue'] {
    .ui-gather-card {
      background: #ffffff;
    }
  }
}
</style>
