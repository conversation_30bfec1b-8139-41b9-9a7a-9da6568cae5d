<template>
  <div class="container">
    <Search
      ref="searchRef"
      :modalList="modalList"
      @searchHandler="searchHandler"
      @addHandler="addHandler"
      @handleDelBatch="deleteHandler"
    />
    <div class="algorithm-table-box">
      <ui-table
        ref="table"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        @on-selection-change="selectChangeHandler"
      >
        <template #action="{ row }">
          <div class="btn-tips">
            <!-- v-permission="['staticLibr-personal-update']" -->
            <ui-btn-tip
              content="详情"
              icon="icon-xiangqing"
              class="primary"
              @click.native="detailHander(row)"
            />
            <ui-btn-tip
              content="编辑"
              icon="icon-bianji"
              class="primary"
              @click.native="editHander(row)"
            />
            <ui-btn-tip
              content="删除"
              icon="icon-shanchu"
              class="primary"
              @click.native="deleteOneHandler(row)"
            />
          </div>
        </template>
      </ui-table>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    >
    </ui-page>
    <AddModal
      ref="addModalRef"
      :modalList="modalList"
      :isEdit="isEdit"
      @complete="getList"
    ></AddModal>
    <DetailModal ref="detailModalRef"></DetailModal>
  </div>
</template>

<script>
import Search from "./components/search.vue";
import AddModal from "./components/add-modal.vue";
import DetailModal from "./components/detail-modal.vue";
import {
  getLLMCompareAlgorithmPageList,
  selectBaseModelList,
  removeLLMCompareAlgorithmPageList,
} from "@/api/semantic-placement.js";
export default {
  name: "SemanticAlgorithmManagement",
  components: { Search, AddModal, DetailModal },
  data() {
    return {
      loading: false,
      tableData: [],
      columns: [
        { type: "selection", align: "center", width: 60 },
        { type: "index", title: "序号", align: "center", width: 80 },
        { title: "算法名称", key: "name" },
        { title: "模型名称", key: "llmModelName" },
        { title: "创建时间", key: "createTime" },
        { title: "操作", slot: "action", width: 174 },
      ],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      isEdit: false,
      selectedData: [],
      modalList: [],
    };
  },
  mounted() {
    this.getList();
    this.getBaseModelList();
  },
  methods: {
    async getBaseModelList() {
      const { data } = await selectBaseModelList();
      this.modalList = [...data];
    },
    async getList() {
      this.loading = true;
      try {
        let param = {
          ...this.$refs.searchRef.getSearchParam(),
          ...this.params,
        };
        let { data } = await getLLMCompareAlgorithmPageList(param);
        this.tableData = data?.entities || [];
        this.total = data?.total || 0;
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    searchHandler(data) {
      const param = {
        ...data,
        ...this.params,
      };
      this.getList(param);
    },
    addHandler() {
      this.isEdit = false;
      this.$refs.addModalRef.init({});
    },
    editHander(row) {
      this.isEdit = true;
      this.$refs.addModalRef.init({ ...row });
    },
    deleteOneHandler(row) {
      this.deleteHandler([row]);
    },
    detailHander(row) {
      this.$refs.detailModalRef.show({ ...row });
    },
    deleteHandler(param) {
      let ids = [];
      if (param) {
        ids = param.map((item) => item.id);
      } else {
        ids = this.selectedData.map((item) => item.id);
      }
      const data = ids.join(",");
      if (data == "") {
        return this.$message.warning("请选择要删除的算法");
      }
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该算法？`,
        onOk: () => {
          removeLLMCompareAlgorithmPageList(data).then((res) => {
            this.$message.success("删除成功");
            this.getList();
          });
        },
      });
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    selectChangeHandler(selection) {
      this.selectedData = [...selection];
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.algorithm-table-box {
  padding-top: 10px;
  flex: 1;
  width: 100%;
  /deep/ .ui-table {
    width: 100%;
    height: 100%;
  }
}
</style>
