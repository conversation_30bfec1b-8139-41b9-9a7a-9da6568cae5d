<template>
  <div>
    <common-form
      :label-width="getWidth"
      :form-data="formData"
      :form-model="formModel"
      ref="commonForm"
      :moduleAction="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="waycondiction" class="mt-xs">
        <p>
          <span class="base-text-color">检测条件：</span>
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
            >设备可用</Checkbox
          >
          <Checkbox
            class="ml-sm"
            v-if="notReportionDeviceList.includes(indexType)"
            v-model="formData.deviceQueryForm.examReportStatus"
            true-value="1"
            false-value="0"
            >设备未报备</Checkbox
          >
          <Checkbox
            v-if="filterCondition.includes(indexType)"
            v-model="formData.deviceQueryForm.detectIsOnline"
            true-value="1"
            false-value="0"
            >设备在线</Checkbox
          >
          <span v-if="filterCondition.includes(indexType)" class="color-failed"
            >（实时视频可调阅率指标检测合格则设备在线）</span
          >
        </p>
        <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="getWidth">
      <FormItem label="普通设备历史录像" v-if="['VIDEO_GENERAL_HISTORY_ACCURACY'].includes(indexType)">
        <Input class="width-input" placeholder="" v-model="formData.videoGeneralDay" />
        <span class="params-suffix">天</span>
      </FormItem>
      <FormItem
        label="重点设备历史录像"
        v-if="['VIDEO_HISTORY_ACCURACY', 'VIDEO_GENERAL_HISTORY_ACCURACY'].includes(indexType)"
      >
        <Input class="width-input" placeholder="" v-model="formData.videoImportDay" />
        <span class="params-suffix">天</span>
      </FormItem>
      <!-- 普通、重点时钟准确率 -->
      <FormItem label="与标准时间允许的偏差" prop="timeDelay" v-if="timeDeviation.includes(indexType)">
        <Input class="width-input" placeholder="" v-model="formData.timeDelay" />
        <span class="params-suffix">秒</span>
      </FormItem>
      <FormItem label="检测方法" :prop="checkMethodsKey" key="'isShowOsdModel'" v-if="checkMethods.includes(indexType)">
        <Select
          v-model="formData[checkMethodsKey]"
          placeholder="请选择"
          transfer
          class="width-input"
          @on-change="handleCheckMethods"
        >
          <template v-for="e in checkMethodsWay">
            <Option :value="e.dataKey" :key="e.dataKey">{{ e.dataValue }} </Option>
          </template>
        </Select>
      </FormItem>
      <FormItem v-if="checkMethodsVisible" label="检测内容">
        <template v-if="this.formData[this.checkMethodsKey] === '1'">
          <p v-for="(qItem, qIndex) in qualityCheckContent" :key="'q' + qIndex">
            <Checkbox true-value="1" false-value="0" v-model="formData[qItem.key]" :disabled="qItem.disabled">
              {{ qItem.text }}
            </Checkbox>
          </p>
        </template>
        <template v-if="this.formData[this.checkMethodsKey] === '3'">
          <CheckboxGroup v-model="wangliCheckContent">
            <Checkbox
              v-for="(wqItem, wqIndex) in qualityCheckContent"
              :key="'wq' + wqIndex"
              :label="wqIndex"
              :disabled="wqItem.disabled"
            >
              {{ wqItem.text }}
            </Checkbox>
          </CheckboxGroup>
        </template>
      </FormItem>
      <!--      综合质量评分    -->
      <FormItem
        required
        class="quality-score"
        label="综合质量评分低于"
        v-if="comprehensiveQualityIndex.includes(this.indexType) && this.formData[this.checkMethodsKey] === '3'"
      >
        <FormItem prop="threshold" label="">
          <Input class="ipt-width-50" v-model="formData.threshold" />
          <span class="params-suffix"> 分，且单项异常比高于 </span>
        </FormItem>
        <FormItem prop="minThreshold">
          <Input class="ipt-width-50" v-model="formData.minThreshold" />
          <span class="params-suffix"> %非必填，则图像质量不合格</span>
        </FormItem>
      </FormItem>
      <!--      说明    -->
      <FormItem
        label="说明："
        v-if="explainIndex.includes(this.indexType) && this.formData[this.checkMethodsKey] === '3'"
      >
        <div class="color-failed">
          图像质量越好，综合评分越高，多个检测项有细微问题（单项异常占比很低），也可能导致整体评分低，
          如果认为这类图片合格，则可以设置单项异常占比参数，综合质量评分低于设置值，
          且某一异常项占比较超过设置值才判定为不合格
        </div>
      </FormItem>
      <!--      检测内容（含有tooltip）、指标取值、需要视频截图    -->
      <template v-if="!!moduleAction.checkConfig">
        <FormItem
          v-if="isSelectOcrOsdClock"
          :class="'check-content' + getWidth"
          :prop="checkContentTooltipIndex.includes(indexType) ? 'detectionMode' : 'detectionMode'"
          :rules="[
            {
              required: !checkContentTooltipIndex.includes(indexType),
              message: '请选择检测内容',
              trigger: 'change',
              type: 'array',
            },
          ]"
        >
          <label
            v-if="checkContentTooltipIndex.includes(indexType)"
            name="label"
            class="check-content-label"
            :style="{ width: `${getWidth}px` }"
          >
            <Tooltip placement="top-start" class="tooltip-sample-graph">
              <i class="icon-font icon-wenhao f-16" :style="{ color: 'var(--color-warning)' }"></i>
              <div slot="content">
                <div class="check-content-img">
                  <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                </div>
              </div>
            </Tooltip>
            检测内容
          </label>
          <template #label v-if="!checkContentTooltipIndex.includes(indexType)"> 检测内容 </template>
          <CheckboxGroup class="check-inline" v-model="formData.detectionMode" @on-change="checkGroupChange">
            <template v-for="(checkItem, checkIndex) in moduleAction.checkConfig">
              <Checkbox
                v-if="!!checkItem.text"
                :key="'check' + checkIndex"
                :label="checkItem.value"
                :disabled="!!checkItem.disabled ? true : false"
              >
                <span v-if="checkItem.label">{{ checkItem.label }}:</span>{{ checkItem.text }}
                <light-tooltip v-if="checkItem.label === '视频亮度'"></light-tooltip>
              </Checkbox>
              <Checkbox
                v-else
                :key="'check' + checkIndex"
                :label="checkItem.value"
                :disabled="!!checkItem.disabled ? true : false"
              >
                <span v-if="checkItem.label">{{ checkItem.label }}:</span>{{ checkItem.text1 }}
                <Input
                  v-model="formData.detectionTimeOut"
                  placeholder="秒"
                  class="ml-xs mr-xs"
                  style="width: 60px"
                  @on-keyup="handlekeyChange"
                />
                {{ checkItem.text2 }}
              </Checkbox>
              <div
                v-if="checkItem.label === '联网质量' && formData.detectionMode && formData.detectionMode.includes('4')"
                class="base-text-color online"
                :key="checkIndex"
              >
                <div class="mb-sm">
                  信令时延超时：<InputNumber class="mr-sm" v-model.number="formData.delaySipTimeOut"></InputNumber>毫秒
                </div>
                <div class="mb-sm">
                  码流时延超时：<InputNumber class="mr-sm" v-model.number="formData.delayStreamTimeOut"></InputNumber
                  >毫秒
                </div>
                <div>
                  关键帧时延超时：<InputNumber class="mr-sm" v-model.number="formData.delayIdrTimeOut"></InputNumber
                  >毫秒
                </div>
              </div>
            </template>
          </CheckboxGroup>
        </FormItem>
        <FormItem
          label="指标取值"
          :required="indexDetectionAnddetection.includes(indexType)"
          v-if="!unwantedIndexValue.includes(indexType)"
          :prop="indexDetectionAnddetection.includes(indexType) ? 'indexDetectionMode' : ''"
        >
          <RadioGroup v-model="formData.indexDetectionMode" @on-change="handleIndexDetectionModeChange">
            <Radio
              v-for="(checkItem, checkIndex) in moduleAction.checkConfig"
              :key="'check' + checkIndex"
              :label="(checkIndex + 1).toString()"
              :disabled="!!formData.detectionMode && !formData.detectionMode.includes((checkIndex + 1).toString())"
              >{{ checkItem.label }}</Radio
            >
            <Radio
              v-if="isVideoPlayingAccuracy"
              label="6"
              :disabled="!!formData.detectionMode && !['4', '5'].every((it) => formData.detectionMode.includes(it))"
              >联网质量+视频亮度</Radio
            >
          </RadioGroup>
        </FormItem>
        <FormItem label="需要视频截图" v-if="!defaultScreenshotIndex.includes(indexType)">
          <RadioGroup v-model="formData.isScreenshots">
            <Radio label="1" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('3')">是</Radio>
            <Radio
              label="2"
              :disabled="
                (!!formData.osdMode && formData.osdModel === 'ocr') ||
                (isVideoPlayingAccuracy && formData.detectionMode.includes('5'))
              "
              >否</Radio
            >
          </RadioGroup>
        </FormItem>
      </template>
      <FormItem :label="isUpdatePhyStatus(indexType)[0]['label']" v-if="isUpdatePhyStatus(indexType).length > 0">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem v-if="['VIDEO_ACCURACY'].includes(indexType)" label="检测规则设置" prop="rule">
        <rule-list
          :formModel="formModel"
          :formData="formData"
          :moduleAction="moduleAction"
          :indexRuleList="formData.ruleList"
        ></rule-list>
      </FormItem>
      <FormItem label="设备时间与标准时间允许偏差" prop="timeDelay" v-if="deviceTimeDeviationIndex.includes(indexType)">
        <Input class="width-input" placeholder="" v-model="formData.timeDelay" />
        <span class="params-suffix">秒</span>
      </FormItem>
      <FormItem class="cache-result" v-if="getCheckResultIndexs().includes(indexType) && checkStoreVisible">
        <label name="label" class="cache-result-label" :style="{ width: `${getWidth}px` }">
          <Tooltip placement="top-start">
            <i class="icon-font icon-wenhao f-16" :style="{ color: 'var(--color-warning)' }"></i>
            <div slot="content">
              {{ getTip }}
            </div>
          </Tooltip>
          取最近缓存结果
        </label>
        <RadioGroup v-model="formData.useRecentCache">
          <Radio label="true">是</Radio>
          <Radio label="false">否</Radio>
        </RadioGroup>
      </FormItem>

      <template v-if="needRecheckIndex.includes(this.indexType)">
        <FormItem label="是否需要复检">
          <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
            <Radio label="1">是</Radio>
            <Radio label="2">否</Radio>
          </RadioGroup>
          <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
        </FormItem>
        <FormItem label="复检设备" v-if="isRecheck === '1'">
          <RadioGroup v-model="formData.reinspect.model">
            <Radio label="UNQUALIFIED">检测不合格设备</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="复检次数" prop="maxCount" v-if="isRecheck === '1' && needRecheckNumIndex.includes(indexType)">
          <InputNumber
            v-model.number="formData.reinspect.maxCount"
            class="ml-md mr-xs"
            :max="5"
            :min="1"
            :precision="0"
          ></InputNumber>
          <span class="base-text-color">次</span>
        </FormItem>
        <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType">
          <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
            <Radio label="INTERVAL" class="mr-lg">时间间隔</Radio>
            <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
          </RadioGroup>
          <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
            <span class="params-suffix">检测结束</span>
            <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
            <Select class="custom-width100" transfer v-model="formData.reinspect.scheduleKey">
              <Option :value="1">时</Option>
              <Option :value="2">分</Option>
            </Select>
            <span class="base-text-color ml-md">后，开始复检</span>
          </div>
          <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
            <Select class="custom-width100 mr-sm" transfer v-model="formData.reinspect.scheduleKey">
              <Option :value="3">当天</Option>
              <Option :value="4">第二天</Option>
              <Option :value="5">第三天</Option>
            </Select>
            <TimePicker
              :value="formData.reinspect.scheduleValue"
              transfer
              format="HH:mm"
              placeholder="请选择"
              style="width: 112px"
              @on-change="handleChangeTime"
            ></TimePicker>
          </div>
        </FormItem>
      </template>

      <!--   达标数量设置、有效设备检测     VIDEO_VALID_SUBMIT_QUANTITY 视频监控有效报送数量达标率  -->
      <div v-if="indexType === 'VIDEO_VALID_SUBMIT_QUANTITY'">
        <!--      达标数量设置    -->
        <FormItem label="达标数量设置">
          <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true"
            >达标数量设置
            <span>{{ `已选择 ${formData.quantityConfig && formData.quantityConfig.length}个` }}</span>
          </Button>
        </FormItem>
        <!--      有效设备检测    -->
        <FormItem label="有效设备检测">
          <rule-list
            :formModel="formModel"
            :indexRuleList="formData.deviceDetection.ruleList"
            topicType="1"
            :moduleAction="moduleAction"
          ></rule-list>
        </FormItem>
      </div>

      <!--视频卡口设备位置完整率  -->
      <!--摄像机位置类型  -->
      <FormItem label="摄像机位置类型" v-if="['VIDEO_EMPHASIS_LOCATION'].includes(moduleAction.indexType)">
        <RadioGroup class="mb-sm" v-model="formData.typeSource" @on-change="setDefaultEmphasisData">
          <Radio label="deviceTag">设备标签</Radio>
          <Radio label="deviceGather">采集区域字典</Radio>
        </RadioGroup>
        <div class="params-content mb-sm modal-video-image" v-if="formData.typeSource === 'deviceTag'">
          <ui-tag
            @close="handleClose(formData.emphasisData, item, index)"
            :closeable="item.source != 1"
            v-for="(item, index) in formData.emphasisData"
            :key="index + '-a' + item.key"
          >
            {{ item.value }}
          </ui-tag>
          <Button type="primary" @click="clickAdd('设备标签')">
            <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="新增"> </i
            ><span class="vt-middle">新增</span></Button
          >
        </div>
        <div class="area" v-else-if="formData.typeSource === 'deviceGather'">
          <Button type="dashed" class="area-btn" @click="clickArea"
            >请选择采集区域类型
            <span>{{ `已选择 ${(formData.emphasisData || 0) && formData.emphasisData.length}个` }}</span></Button
          >
        </div>
      </FormItem>
      <FormItem label="对历史检测结果比对分析" v-if="historyComparisonConfigModule.includes(indexType)">
        <RadioGroup v-model="formData.isDetectContrast">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <quantity-config
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :data="formData.quantityConfig"
      :regionData="treeData"
      @query="quantityQuery"
    ></quantity-config>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :checkbox-list="allDeviceFileList"
      :default-checked-list="defaultCheckedList"
      :field-name="fieldName"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
  </div>
</template>

<script>
import { defaultEmphasisData } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
import videoConfigsData from '../../video-config';
import {
  isUpdatePhyStatus,
  historyComparisonConfigModule,
  needRecheckNumIndex,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';

export default {
  props: {
    // 指标英文名称
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateDetectionModePass = (rule, value, callback) => {
      !value.length ? callback(new Error('请选择检测内容')) : callback();
    };
    const validateTimeDelayPass = (rule, value, callback) => {
      !value.length ? callback(new Error('请输入与标准时间允许的偏差')) : callback();
    };
    const validateThresholdPass = (rule, value, callback) => {
      !value ? callback(new Error('请输入综合质量评分设置值')) : callback();
    };
    const validateIndexDetectionMode = (rule, value, callback) => {
      !value ? callback(new Error('请选择指标取值')) : callback();
    };
    const validateMinThresholdPass = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        const reg = new RegExp('^([1-9]|[1-9]\\d|100)$');
        !reg.test(value) ? callback(new Error('请输入1-100的整数')) : callback();
      }
    };
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      ruleCustom: {
        timeDelay: [{ validator: validateTimeDelayPass, trigger: 'blur' }],
        detectionMode: [{ validator: validateDetectionModePass, trigger: 'change' }],
        threshold: [{ validator: validateThresholdPass, trigger: 'blur' }],
        minThreshold: [{ validator: validateMinThresholdPass, trigger: 'blur' }],
        indexDetectionMode: [{ validator: validateIndexDetectionMode, trigger: 'change' }],
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      formData: {
        detectMode: '1',
        isScreenshots: '1',
        selectType: 0,
        userUnavailableStatus: 1,
        // indexDetectionMode: '',
        // threshold: '90',
        // minThreshold: ''
        delaySipTimeOut: null,
        delayStreamTimeOut: null,
        delayIdrTimeOut: null,
        isUpdatePhyStatus: 0,
      },
      intactRateSelect: false, // 指标取值完好率
      useRateSelect: false, // 指标取值可用率
      regionalizationSelectVisible: false,
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      screenWidth: '',
      checkMethodsWay: [], // 检测方法数据
      checkMethodsKey: '', // 检测方法配合后端用的字段 (视频质量合格率,视频流质量合格率（人工复核）: videoQualityDetectMode; 其他指标： osdModel)
      wangliCheckContent: [0, 1, 2, 3, 4, 5],
      oldIndexDetectionMode: '',
      isRecheck: '2',
      ...videoConfigsData.data(),
      notReportionDeviceList: [
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'FACE_ONLINE_RATE',
        'VEHICLE_ONLINE_RATE',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ], // 未报备设备显示属性
      historyComparisonConfigModule: historyComparisonConfigModule,
      needRecheckNumIndex: needRecheckNumIndex,
    };
  },
  async created() {
    // 视频质量合格率,视频流质量合格率（人工复核）的检测方法和其他指标不一致，需要做个判断
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
    const qualityIndex = ['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'];
    if (qualityIndex.includes(this.indexType)) {
      this.checkMethodsKey = 'videoQualityDetectMode';
      this.checkMethodsWay = this.qualityCheckMethods;
    } else {
      this.checkMethodsKey = 'osdModel';
      this.checkMethodsWay = this.odsCheckModelList;
    }
  },
  mounted() {
    this.getModalWidth();
    window.onresize = () => {
      // 定义窗口大小变更通知事件
      this.getModalWidth();
    };
  },
  methods: {
    isUpdatePhyStatus(indexType) {
      return isUpdatePhyStatus.filter((item) => item.indexType === indexType);
    },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async getCheckRule() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: 'BASIC_ACCURACY' },
        });
        let rule = data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.formData.deviceDetection.ruleList = rule;
        this.formData.ruleList = rule;
      } catch (err) {
        console.log(err);
      }
    },
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },
    async handleSubmit() {
      const valid = await this.$refs['modalData'].validate();
      if (!valid) {
        this.$Message.error('请将配置信息填写完整！');
        return;
      }
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return;
      }
      if (this.defaultScreenshotIndex.includes(this.indexType)) {
        this.formData.isScreenshots = '1';
      }
      return this.$refs['commonForm'].handleSubmit();
    },
    validateForm() {
      let flag = false;
      if (!this.formData.ruleList || !this.formData.ruleList.length) flag = true;
      this.formData.ruleList &&
        this.formData.ruleList.map((item) => {
          if (item.isConfigure == 1) flag = true;
        });
      return flag;
    },
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    checkGroupChange(val) {
      // 检测内容和指标取值对应联动 - 最严格标准
      if (this.indexDetectionAnddetection.includes(this.indexType)) {
        let findMaxArray = val.sort();
        this.formData.indexDetectionMode = findMaxArray[findMaxArray.length - 1];
      }
      if (!val.length || !!this.osdType.includes(this.indexType)) {
        this.formData.indexDetectionMode = '';
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b); // 从小到大排序
      // 找出最大值进行遍历（第一项单独操作不联动）
      const sortMax = Number(sortDetectionMode[sortDetectionMode.length - 1]);
      if (sortMax > 1) {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 1 : 2;
        for (let mx = startIndex; mx < sortMax; mx++) {
          // 视频亮度 和 联网质量  不联动
          if (
            !sortDetectionMode.includes(mx.toString()) &&
            !(sortMax === 5 && mx == 4 && this.isVideoPlayingAccuracy)
          ) {
            let mode = this.$util.common.deepCopy(this.formData.detectionMode);
            mode.push(mx.toString());
            this.$set(this.formData, 'detectionMode', mode);
          }
        }
      }
      if (!this.formData.detectionMode.includes('3')) {
        this.$set(this.formData, 'isScreenshots', '2');
      } else {
        this.$set(this.formData, 'isScreenshots', '1');
      }
      // 视频亮度 和 联网质量 都勾选，则 指标取值 默认勾选 【联网质量+视频亮度】
      if (['4', '5'].every((it) => this.formData.detectionMode.includes(it)) && this.isVideoPlayingAccuracy) {
        this.formData.indexDetectionMode = '6';
      }
      if (!this.unwantedIndexValue.includes(this.indexType)) return false;
      if (val.includes(this.oldIndexDetectionMode)) {
        this.formData.indexDetectionMode = this.oldIndexDetectionMode;
      } else {
        this.oldIndexDetectionMode = '';
        this.formData.indexDetectionMode = '';
      }
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      sortDetectionMode.forEach((item) => {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 0 : 1;
        // 视频亮度 和 联网质量  互不影响
        if (
          item > startIndex &&
          item !== sortDetectionMode[sortDetectionMode.length - 1] &&
          !(item === '4' && sortDetectionMode.includes('5') && this.isVideoPlayingAccuracy)
        ) {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', true);
        } else {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', false);
        }
      });
    },
    //设置默认值
    setDefaultEmphasisData(val) {
      if (val === 'deviceGather') {
        if (
          this.moduleAction.indexType === 'FACE_EMPHASIS_LOCATION' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultEmphasisData;
        }
      } else {
        this.formData.emphasisData = [];
      }
    },
    handleClose(data, item, index) {
      this.$Modal.confirm({
        title: '警告',
        content: `您要删除${item.value}，是否确认?`,
        onOk: () => {
          data.splice(index, 1);
        },
      });
    },
    async clickAdd(type) {
      this.defaultCheckedList = [];
      try {
        this.defaultCheckedList = (this.formData.emphasisData || []).map((item) => item.key);
        this.customizeAction = {
          title: `新增${type}`,
          leftContent: `所有${type}`,
          rightContent: `已选择${type}`,
          moduleStyle: {
            width: '80%',
          },
        };
        this.customSearch = true;
        let params = {
          tagType: '2',
          isPage: false,
        };
        let {
          data: { data },
        } = await this.$http.post(taganalysis.getDeviceTag, params);
        this.allDeviceFileList = data;
      } catch (error) {
        console.log(error);
      }
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        let data = this.formData.emphasisData || [];
        this.checkedTreeData = data.map((item) => item.key);
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data, dataWithName) {
      this.formData.emphasisData = dataWithName;
    },
    confirmFilter(data) {
      this.customSearch = false;
      let list = [];
      data.map((item) => {
        list.push({
          key: item.tagId,
          value: item.tagName,
        });
      });
      this.formData.emphasisData = list;
    },
    setDefaultTime() {
      if (this.moduleAction.indexType === 'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN') {
        this.$set(this.formData, 'timeDelay', '1');
      }
      // this.orgList = []
    },
    getCheckResultIndexs() {
      let indexArr = [];
      this.checkResultTips.forEach((item) => {
        if (
          (!!item.hasOcr && !!this.formData.osdModel && this.formData.osdModel !== 'sdk') ||
          (!this.formData.osdModel && !item.hasOcr) ||
          ['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'].includes(this.indexType)
        ) {
          indexArr = indexArr.concat(item.indexs);
        }
      });
      return indexArr;
    },
    handleCheckMethods() {
      let modalWidth = this.screenWidth > 1890 ? '4rem' : '5rem';
      this.$emit('changeModalWidth', this.getCheckResultIndexs().includes(this.indexType) ? modalWidth : '3.7rem');
      this.handleCheckMethodsVal();
    },
    handleCheckMethodsVal() {
      if (this.checkMethodsKey !== 'videoQualityDetectMode' && this.formData[this.checkMethodsKey] === '1') {
        return false;
      }
      // this.$set(this.formData, 'useRecentCache', false)
      if (this.comprehensiveQualityIndex.includes(this.indexType) && this.formData[this.checkMethodsKey] === '3') {
        this.$set(this.formData, 'threshold', '80');
        this.$set(this.formData, 'minThreshold', '80');
      }
      this.videoQualityCheckContent.forEach((item) => {
        if (this.formData[item.key]) {
          delete this.formData[item.key];
        }
      });
    },
    handleIndexDetectionModeChange(val) {
      this.oldIndexDetectionMode = val;
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },
    getModalWidth() {
      this.screenWidth = window.screen.width;
      const modalWidth = this.screenWidth > 1890 ? '4.2rem' : '5rem';
      this.$emit(
        'changeModalWidth',
        this.getCheckResultIndexs().includes(this.indexType) || this.filterCondition.includes(this.indexType)
          ? modalWidth
          : '3.7rem',
      );
    },

    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        if (needRecheckNumIndex.includes(this.indexType)) {
          this.$set(this.formData.reinspect, 'maxCount', 1);
        }
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handlekeyChange() {
      const detectionTimeOut = this.formData.detectionTimeOut.replace(/[^\d]/g, '');
      this.$set(this.formData, 'detectionTimeOut', detectionTimeOut);
    },
  },
  watch: {
    'formData.detectionMode'(val) {
      // 不需要禁掉的指标: 通字幕标注合规率、重点字幕标注合规率
      if (!val || !!this.osdType.includes(this.indexType)) return false;
      this.handleCheckDiabled(val);
    },
    formModel: {
      handler(val) {
        const list = [
          'VIDEO_GENERAL_CLOCK_ACCURACY',
          'VIDEO_CLOCK_ACCURACY',
          'VIDEO_GENERAL_OSD_ACCURACY',
          'VIDEO_OSD_ACCURACY',
        ];
        if (val === 'edit') {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
            detectionTimeOut: this.configInfo.detectionTimeOut ? Number(this.configInfo.detectionTimeOut) : '',
            isScreenshots: list.includes(this.indexType) ? '2' : this.configInfo.isScreenshots,
          };
          this.oldIndexDetectionMode = this.configInfo.indexDetectionMode || '';
          // 不需要detectionMode字段的指标
          const unNeedDetectionModeIndex = ['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'];
          if (unNeedDetectionModeIndex.includes(this.indexType)) {
            // 这一步操作主要是为了避免旧数据存在detectionMode数据，导致监听函数里面操作报错
            if (this.formData.detectionMode) delete this.formData.detectionMode;
          } else {
            // 通字幕标注合规率、重点字幕标注合规率指标的字段和其他指标的字段不一致
            if (this.osdType.includes(this.indexType)) {
              this.$set(this.formData, 'detectionMode', this.configInfo.detectContent);
              delete this.formData.detectContent;
            } else {
              this.$set(this.formData, 'detectionMode', this.configInfo.detectionMode);
            }
          }
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            isScreenshots: list.includes(this.indexType) ? '2' : this.configInfo.isScreenshots,
            quantityConfig: [],
            deviceDetection: {},
            userUnavailableStatus: 'true',
            deviceQueryForm: {
              detectPhyStatus: '0',
            },
            indexDetectionMode: '',
            detectionMode: [],
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
          if (
            [
              'VIDEO_GENERAL_PLAYING_ACCURACY',
              'VIDEO_PLAYING_ACCURACY',
              'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
            ].includes(this.indexType)
          ) {
            this.formData.detectionMode = ['2', '3', '4'];
            this.formData.indexDetectionMode = '4';
          }
          if (this.osdType.includes(this.indexType)) {
            this.$set(this.formData, 'detectionMode', ['areaLocation']);
          }
          if (this.needRuleConfig.includes(this.indexType)) {
            if (this.indexType === 'VIDEO_ACCURACY') {
              this.$set(this.formData, 'ruleList', []);
            } else {
              this.$set(this.formData.deviceDetection, 'ruleList', []);
            }
            this.getCheckRule();
          }
          if (this.comprehensiveQualityIndex.includes(this.indexType)) {
            this.$set(this.formData, 'threshold', '80');
            this.$set(this.formData, 'minThreshold', '80');
          }
        }
        if (this.timeDeviation.includes(this.indexType)) {
          this.$set(this.formData, 'timeDelay', this.formData.timeDelay || '30'); // 与标准时间允许的偏差默认值为30
        }
        if (this.needRecheckIndex.includes(this.indexType)) {
          let reinspectObj = this.configInfo.reinspect;
          this.isRecheck = val === 'edit' && !!reinspectObj ? '1' : '2';
          let reinspect = null;
          if (this.isRecheck === '1') {
            reinspect = {
              model: 'UNQUALIFIED',
              type: 'PROGRAM_BATCH',
              scheduleType: val === 'edit' ? reinspectObj.scheduleType : 'INTERVAL',
              scheduleKey: val === 'edit' ? reinspectObj.scheduleKey : 2,
              plan: '2',
            };
            if (needRecheckNumIndex.includes(this.indexType)) {
              reinspect.maxCount = val === 'edit' ? (reinspectObj.maxCount ? reinspectObj.maxCount : null) : 1;
            }
            if (val === 'edit' && !!reinspectObj.scheduleValue) {
              reinspect.scheduleValue =
                reinspectObj.scheduleType === 'INTERVAL'
                  ? Number(reinspectObj.scheduleValue)
                  : reinspectObj.scheduleValue;
            } else {
              reinspect.scheduleValue = null;
            }
          }
          this.$set(this.formData, 'reinspect', reinspect);
        }
        this.setDefaultTime();
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
      treeData: 'common/getInitialAreaList',
    }),
    getWidth() {
      const miniWidthIndexIds = [4025];
      return miniWidthIndexIds.includes(this.moduleAction.indexId)
        ? 125
        : this.getCheckResultIndexs().includes(this.indexType)
        ? 220
        : 180;
    },
    getTip() {
      let tipObj = this.checkResultTips.find((item) => item.indexs.includes(this.indexType));
      // 缓存结果没有默认值，默认是
      'useRecentCache' in this.formData ? null : (this.formData.useRecentCache = 'true');
      // 配置取最近缓存结果
      if ('tipFunc' in tipObj && tipObj.tipFunc instanceof Function) {
        return tipObj.tipFunc('X');
      }
      return tipObj ? tipObj.tip : '';
    },
    checkMethodsVisible() {
      // 如果当前指标为视频质量合格率,视频流质量合格率（人工复核），检测方法选中的是捷尚算法，那么将显示检测内容
      if (['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'].includes(this.indexType)) {
        return ['1', '3'].includes(this.formData[this.checkMethodsKey]);
      }
      return false;
    },
    checkStoreVisible() {
      // 如果当前指标为视频质量合格率,视频流质量合格率（人工复核），检测方法选中的是捷尚算法，那么将显示检测内容
      if (['VIDEO_QUALITY_PASS_RATE', 'VIDEO_QUALITY_PASS_RATE_RECHECK'].includes(this.indexType)) {
        return ['1', '3'].includes(this.formData[this.checkMethodsKey]);
      } else {
        return true;
      }
    },
    qualityCheckContent() {
      if (this.formData[this.checkMethodsKey] === '1') {
        return this.videoQualityCheckContent;
      } else if (this.formData[this.checkMethodsKey] === '3') {
        return this.wangliVideoQualityCheckContent;
      }
      return [];
    },
    isSelectOcrOsdClock() {
      return (
        (['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'].includes(this.indexType) &&
          !!this.formData.osdModel &&
          this.formData.osdModel !== 'sdk') ||
        !['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'].includes(this.indexType)
      );
    },
    // 重点|普通实时视频可调阅率、重点指挥图像在线率
    isVideoPlayingAccuracy() {
      return [
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      ].includes(this.indexType);
    },
  },
  components: {
    CommonForm: require('../../common-form/index.vue').default,
    quantityConfig: require('@/components/quantity-config/index').default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    AreaSelect: require('@/components/area-select').default,
    CustomizeFilter: require('@/components/customize-filter').default,
    UiTag: require('@/components/ui-tag').default,
    LightTooltip: require('./light-tooltip.vue').default,
  },
};
</script>
<style lang="less" scoped>
.online {
  margin-left: 25px;
}
.form-content {
  //padding-left: 60px;
  /deep/.width-input {
    width: 380px;
    .ivu-input-group-append {
      background-color: #02162b;
      border: 1px solid #10457e;
      border-left: none;
    }
  }
  .params-suffix {
    color: var(--color-content);
    margin-left: 5px;
  }
  /deep/.ivu-form-item {
    .ivu-form-item-content {
      .ivu-checkbox-group {
        display: flex;
        flex-direction: column;
        .ivu-checkbox-wrapper {
          width: fit-content;
          display: flex;
          align-items: center;
          > span {
            margin-right: 10px;
          }
        }
      }
      .ivu-radio-group {
        .ivu-radio-group-item {
          margin-right: 30px;
          > span {
            margin-right: 10px;
          }
        }
      }
    }
  }
}
.check-content220 {
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
  }
  @{_deep} .ivu-form-item-error-tip {
    margin-left: 220px !important;
  }
}
.check-content180 {
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
  }
  @{_deep} .ivu-form-item-error-tip {
    margin-left: 180px !important;
  }
}
.check-content-label {
  margin-left: 0;
  text-align: right;
  vertical-align: middle;
  float: left;
  font-size: 14px;
  color: var(--color-label);
  line-height: 1;
  padding: 10px 12px 10px 0;
  box-sizing: border-box;
  @{_deep} .ivu-tooltip {
    position: relative;
    &-inner {
      width: 400px;
    }
    &-popper {
      left: -13px !important;
    }
  }
}
.cache-result {
  &-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: var(--color-label);
    line-height: 1;
    padding: 10px 12px 10px 0;
    box-sizing: border-box;
  }
  @{_deep} .ivu-tooltip {
    position: relative;
    &-inner {
      width: 200px;
      white-space: normal !important;
    }
    &-popper {
      left: -13px !important;
    }
  }
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
  }
}
.check-inline {
  display: inline-block !important;
}
.ipt-width-50 {
  width: 50px;
}
.quality-score {
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
    display: flex;
    align-items: center;
  }
}
.custom-width100 {
  width: 100px;
}
</style>
