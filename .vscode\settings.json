{
  "editor.tabSize": 2,
  "editor.formatOnSave": false, // 保存时自动格式化
  "editor.defaultFormatter": "esbenp.prettier-vscode", // 设置默认格式化工具为 prettier
  // 针对特定的语言进行单独配置
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // 设置 javascript 的默认格式化工具
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode" // 使用 vetur 格式化 vue 文件
  },
  // Vetur 配置
  "vetur.format.defaultFormatter.html": "js-beautify-html", // 格式化 Vue 中的 template 
  "vetur.format.options.tabSize": 2,
  "vetur.format.defaultFormatterOptions": {
    "js-beautify-html": {
      "wrap_attributes": "auto", // auto、force-aligned、aligned-multiple、force-expand-multiline
      "wrap_line_length": 100 // 长度超过多少开始折行
    },
    "prettier": {
      "printWidth": 120,
      "semi": true,
      "useTabs": false,
      "singleQuote": true,
      "quoteProps": "as-needed",
      "arrowParens": "always",
      "proseWrap": "preserve",
      "htmlWhitespaceSensitivity": "css",
      "vueIndentScriptAndStyle": false
    }
  },
  "editor.cursorBlinking": "smooth",
  "editor.cursorSmoothCaretAnimation": "on"
}