//this.Map 
//this._mapGeometry

import { isFunction, isString, isObject, isNumber, getMapMarkerImage, getMapRegisterImages, getMarkerOptsEvent } from "@/util/modules/common.js"
import { getImageByType } from '@/map/core/enum/map-image.js'

export const npMapMixins = {
  data() {
    return {}
  },
  computed: {

  },
  methods: {
    /**
     * 根据图片配置获取图片对象集合
     * @param showInMap - 图片展示配置，默认false
     * @param keys - 图片key值数组
     * @returns {Map<any, any>} - window.Map()
     * @private
     */
    _getMarkerIcons({ showInMap, ...keys } = {}) {
      let icons = new window.Map();
      let icon = null;
      Object.keys(keys).map((key) => {
        icon = this._getIconObj(keys[key]);
        icon && !!showInMap && icon.showInMap(!!showInMap);
        icons.set(key, icon);
      });
      return icons;
    },
    /**
       * 通过Key值获取图片对象
       * @param key - 图片Key值
       * @param showInMap - 图片展示配置，默认false
       * @returns {*} - NPMap.Symbols.Icon对象
       * @private
       */
    _getIconObj(key, showInMap = false) {
      let obj = getMapMarkerImage(key)
      obj.url = key
      return this._mapGeometry.getIconByParam(Object.assign(obj, { showInMap }));
    },
    /**
     * 地图普通marker撒点
     * @param layerName - 撒点的图层
     * @param data - 待撒点的业务数据
     * @param opts - marker配置
     * @param format - 数据格式化
     * @param callBack - 回调函数
     */
    initMarkers(layerName = "", data = [], opts = {}, format, callBack) {
      if (data.length === 0) return;
      let markers = new window.Set();
      let registerImageKeys = new window.Set();
      let formatFun = isFunction(format) ? format : null;
      let { label } = opts;
      //遍历数据，包装marker
      data.map((item, index) => {
        //格式化数据
        let data = formatFun ? formatFun(item) : item;
        let { images, images: { normal, hover } = {} } = data;
        images = images ? images : getImageByType(item)
        normal = normal ? normal : images.normal
        hover = hover ? hover : images.hover
        //获取marker的图标对象
        let icons = this._getMarkerIcons(images);
        //创建marker
        let marker = this._createMarker(data, icons.get("normal"), opts);
        //给marker添加标注
        if (isObject(label)) marker.setLabel(this._getMarkerLabel(data, label, index + ""));
        //给marker填充数据
        marker.setData(data);
        //收集图片和点位信息
        registerImageKeys.add(normal).add(hover);
        markers.add(marker);
      });
      let allImages = [...registerImageKeys].filter(key => isString(key));
      this.addMarkers(layerName, [...markers], allImages, callBack, opts);
    },
    /**
     * 获取marker的label对象
     * @param data - text为函数时有效，业务可以通过marker数据自定义text内容
     * @param text {string|function} - label标签上显示的值
     * @param color - 颜色
     * @param offset - 标签偏移量
     * @param defaultValue - label默认值
     * @returns {*} - NPMap.Symbols.Label
     * @private
     */
    _getMarkerLabel(data, { text, color = "", offset = [0, 0] } = {}, defaultValue = "") {
      let title = isString(text) || isNumber(text) ? text : isFunction(text) ? (text(data) || "").toString() : defaultValue;
      let label = new NPMap.Symbols.Label(title, {
        color,
        offset: new NPMap.Geometry.Size(offset[0], offset[1])
      });
      label.setStyle({ color: color });
      return label;
    },
    /**
       * 创建非聚合图层点位marker
       * @param data - marker对应的数据对象
       * @param icon - marker对应的默认图标对象
       * @param rotate - 配置参数，默认为0
       * @returns {*} - NPMap.Symbols.Marker
       * @private
       */
    _createMarker(data, icon, { rotate = 0, isShowTitle } = {}) {
      return new NPMap.Symbols.Marker(new NPMap.Geometry.Point(data.longitude, data.latitude), {
        icon,
        title: isShowTitle ? "" : data.name,
        rotation: rotate
      });
    },
    /**
       * 添加图层覆盖物
       * @param layerName
       * @param markers
       * @param registerImageKeys
       * @param callBack
       */
    addMarkers(layerName = "", markers = [], registerImageKeys = [], callBack, opts) {
      this.Map.addImages(getMapRegisterImages(registerImageKeys), () => {
        //如果有图层分组则将其加到图层分组上
        let layer = this.Map.getLayerByName(layerName)
        if (layer) {
          layer.addOverlays(markers);
        } else {
          layer = new NPMapLib.Layers.OverlayLayer(layerName);
          this.Map.addLayer(layer);
          layer.addOverlays(markers);
        }

        //marker添加到地图后 给marker绑定事件            
        this.addMarkerEvent(markers, opts);
        if (isFunction(callBack)) callBack(markers);
      });
    },
    /**
     * 给marker绑定事件和设置是否可编辑 适配2D只能在marker添加到地图后才能给marker添加事件和设置编辑状态
     * @param {*} markers 
     * @param {*} opts marker公共配置
     */
    addMarkerEvent(markers, opts) {
        let clickNodeId = "";
        const MARKER_EVENTS = new window.Map([
            [NPMap.MARKER_EVENT_MOUSE_OVER, function(icons, m, clickHover) {
                if(icons.get("hover") && !clickHover) {
                    m.setIcon(icons.get("hover"));
                    m.refresh();
                }
            }],
            [NPMap.MARKER_EVENT_MOUSE_OUT, function(icons, m, clickHover) {
                if(icons.get("normal") && icons.get("hover") && !clickHover) {
                    m.setIcon(icons.get("normal"));
                    m.refresh();
                }
            }],
            [NPMap.MARKER_EVENT_CLICK, function(icons, m) {
                if(icons.get("hover")) {
                    m.setIcon(icons.get("hover"));
                    m.refresh();
                }
            }]
        ]);
        markers.forEach(marker => {
            [...MARKER_EVENTS.keys()].map(event => {
                marker.addEventListener(event, m => {
                    let data = m.getData();
                    let images = data.images ? data.images : getImageByType(data)
                    let icons = this._getMarkerIcons(images);
                    let isSelf = clickNodeId === (data && data.id);
                    MARKER_EVENTS.get(event)(icons, m, isSelf);    // 执行鼠标默认事件    
                    // 执行用户自定义鼠标事件 回调用于处理弹窗场景           
                    getMarkerOptsEvent(opts).get(event)(m);
                });
            })
        });
    },
    addOverlays(layerName = "", markers = []) {
      let layer = this.Map.getLayerByName(layerName)
      if (layer) {
        layer.addOverlays(markers);
      } else {
        layer = new NPMapLib.Layers.OverlayLayer(layerName);
        layer.addOverlays(markers);
        this.Map.addLayer(layer);
      }
    },
    /**
     * 画线
     * @param {*} linePoints 线上的点位信息
     * @param {*} lineStyle 线的样式
     */
    drawPolyline(linePoints, lineStyle) {
      if (!linePoints || !linePoints.length) return;
      let data = linePoints.map(
        item => new NPMap.Geometry.Point(item[0], item[1])
      );
      return new NPMap.Geometry.Polyline(data, Object.assign({}, {
        color: "black",
        lineStyle: 0, //线的样式
        weight: 4, //线的宽度，以像素为单位
        opacity: 1 //线的透明度，取值范围0-1
      }, lineStyle));
    },
    // 添加地图弹窗
    /**
     *  @param {*} content 窗体div元素
     *  @param {*} point {lon: node.longitude, lat: node.latitude}
     *  @param {*} opts 
     *         width: 130,//信息窗宽度，单位像素
     *         height: 130,//信息窗高度，单位像素
     *         offset: [0,-30],//信息窗位置偏移值
     *         iscommon: false, //是否添加箭头
     *         isAdaptation: true //是否自适应
     *  @param {*} title 窗体标题
    */
    addInfoWindow(...args) {
      let [point, content, opts, title = ""] = args;
      if ((point.longitude && point.latitude) || (point.lon && point.lat)) {
        point = new NPMap.Geometry.Point(point.longitude||point.lon, point.latitude||point.lat);
      }
      let defaultPositionBlock = {
        imageSrc: require('@/assets/img/map/triangle.png'),
        imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
        offset: new NPMapLib.Geometry.Size(-0, 80)
      };
      let defaultOpt = {
        iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
        enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
        autoSize: true, // 默认true, 窗口大小是否自适应
        isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
      }
      this.Map.centerAndZoom(point, 17)
      opts = Object.assign({}, defaultOpt, opts || {});
      opts.positionBlock = Object.assign({}, defaultPositionBlock, opts.positionBlock || {});
      if (opts.offset) opts.offset = new NPMap.Geometry.Size(opts.offset);
      let htmlFontSize = window.getComputedStyle(window.document.documentElement)['font-size']
      let offsetLeft = (opts.width) / 192 * parseFloat(htmlFontSize) / 2
      let offsetTop = (opts.height) / 192 * parseFloat(htmlFontSize)
      let left = -offsetLeft + 5;
      let top =  -offsetTop - 60;
      opts.offset = new NPMapLib.Geometry.Size(left, top) // 信息窗位置偏移值

      const infowindow = new NPMapLib.Symbols.InfoWindow(point, title || "", null, opts)
      infowindow.setContentDom(content)
      this.Map.addOverlay(infowindow);
      infowindow.open();
      infowindow.updatePosition()
      infowindow.setBackgroundColor("transparent");
      return infowindow;
    },
    closeAllInfoWindows() {
      this.Map.closeAllInfoWindows()
    },
    /**
     * 移除指定图层上的所有覆盖物
     * @param {String/图层对象} layerName 图层名称或图层对象 
     */
    removeAllOverlays(layerName) {
      let layer
      if (typeof layerName === "string") {
        layer = this.Map.getLayerByName(layerName)
      } else {
        layer = layerName
      }
      if (layer) {
        this.Map.removeOverlay(layer);
        layer.removeAllOverlays();
      }
    },
    // 根据图层名称显示图层
    showLayer(layerName) {
      let layer
      if (typeof layerName === "string") {
        layer = this.Map.getLayerByName(layerName)
      } else {
        layer = layerName
      }
      if (layer) {
        layer.show();
      }
    },
    // 根据图层名称隐藏图层
    hideLayer(layerName) {
      let layer
      if (typeof layerName === "string") {
        layer = this.Map.getLayerByName(layerName)
      } else {
        layer = layerName
      }
      if (layer) {
        layer.hide();
      }
    }
  }
}
