<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :table-loading="tableLoading"
      :form-item-data="formItemData"
      :form-data="formData"
      @startSearch="startSearch"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize"
    >
      <template #otherButton>
        <div class="operation-bar">
          <Button slot="export" type="primary" class="button-export ml-md" @click="onExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white mr-xs"></i>
            <span class="ml-xs">导出</span>
          </Button>
        </div>
      </template>
      <template #iconstatics>
        <ui-switch v-model="formData.sbgnlx" :data="sbgnlxData" @on-change="onChangeSbgnlx" class="mb-sm mt-sm">
        </ui-switch>
        <icon-statics :icon-list="iconList" class="mb-sm"> </icon-statics>
      </template>
      <!-- 表格插槽 -->
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatusText="{ row }">
        <span :class="row.phyStatusText === '可用' ? 'font-green' : 'font-red'">{{ row.phyStatusText }}</span>
      </template>
      <template slot="action" slot-scope="{ row }">
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          class="mr-sm"
          content="查看不合格原因"
          @click.native="checkReason(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <!-- 导出   -->
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
    <nonconformance
      ref="nonconformance"
      title="检测不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonLoading="reasonLoading"
    ></nonconformance>
  </div>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import { iconStaticsList, tableColumn, formItemData } from './util/enum/ReviewParticular.js';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  mixins: [downLoadTips, particularMixin, dealWatch],
  props: {},
  data() {
    return {
      iconList: [],
      tableColumns: tableColumn,
      formItemData: formItemData,
      formData: {
        deviceId: '',
        deviceName: '',
        phyStatus: '',
        sbgnlx: 1,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      tableLoading: false,
      tableData: [],
      selectTabs: [],
      exportLoading: false,
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],

      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonLoading: false,
      sbgnlxData: [
        { value: 1, label: '未建档视频监控' },
        { value: 2, label: '未建档车辆卡口库' },
        { value: 3, label: '未建档人脸卡口' },
      ],
    };
  },
  created() {
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
        this.iconList = iconStaticsList.video;
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    onChangeSbgnlx(val) {
      this.pageData.pageNum = 1;
      this.initAll();
      switch (val) {
        case 1:
          this.iconList = iconStaticsList.video;
          break;
        case 2:
          this.iconList = iconStaticsList.vehicle;
          break;
        case 3:
          this.iconList = iconStaticsList.face;
          break;
      }
    },
    initAll() {
      this.getTableList();
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableList();
    },
    getTableList() {
      this.tableData = [];
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        this.totalCount = data.total;
      });
    },
    handlePage(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    handlePageSize(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = data.map((item) => {
          return {
            name: item,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },

    // 不合格原因
    checkReason(row) {
      this.getReason(row.deviceInfoId);
      this.$refs.nonconformance.init();
    },
    async getReason(deviceInfoId) {
      this.reasonLoading = true;
      let params = {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        // access: this.paramsList.access,
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
        customParameters: { deviceInfoId },
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getSecondaryPopUpData,params, 'post',this.$route.query.cascadeId, {})
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas || [];
        // this.reasonPage.totalCount = datas.total
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    UiSwitch: require('./components/ui-switch').default,
    Particular: require('../../ui-pages/particular.vue').default,
    exportData: require('../components/export-data').default,
    nonconformance: require('./components/nonconformance.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tabs {
    flex: 1;
  }
}
</style>
