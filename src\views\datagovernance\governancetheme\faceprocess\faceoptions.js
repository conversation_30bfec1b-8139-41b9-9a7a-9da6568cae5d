let faceData = [
  {
    addVisible: false,
    top: '1.88rem',
    left: '1.2%',
    datas: [
      {
        name: 'dataInput',
        icon: 'icon-zu16191',
        title: '数据输入',
        desc: '选择待治理数据',
        left: '10px',
        iconPass: true,
        iconSetting: false,
        switch: false,
      },
    ],
    connectingOptions: {
      // width: "1.6%",
      width: '5.6%',
      height: '0.04rem',
      top: '2.09rem',
      left: '15.5%',
    },
  },
  {
    addVisible: false,
    top: '0.88rem',
    // left: "17.2%",
    left: '21.3%',
    datas: [
      {
        name: 'fieldMapping',
        icon: 'icon-tuxiangzhuapaishijianzhunquexingjiance',
        title: '图像抓拍时间准确性检测',
        desc: '检测图像抓拍时间是否准确',
        left: '10px',
        switch: true,
      },
      {
        name: 'Imgtest',
        icon: 'icon-tuxiangshangchuanjishixingjiance',
        title: '图像上传及时性检测',
        desc: '检测图像上传是否及时',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
      {
        name: 'urltest',
        icon: 'icon-datuURLjiance',
        title: '图像URL检测',
        desc: '小图对应的大图可以正常访问',
        left: '10px',
        iconSetting: true,
        switch: true,
      },
      {
        name: 'repeatTest',
        icon: 'icon-zhongfutuxiangshibiechuli',
        title: '重复图像检测',
        desc: '检测小图url是否重复',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '5.6%',
      height: '0.04rem',
      top: '2.09rem',
      // left: "32.9%",
      left: '37%',
    },
  },
  {
    addVisible: false,
    top: '1.88rem',
    // left: "34.6%",
    left: '42.8%',
    datas: [
      {
        name: 'FaceStructure',
        icon: 'icon-renlianjiegouhua',
        title: '人脸结构化',
        desc: '选择多家算法对人脸图像进行结构化',
        left: '10px',
        iconSetting: true,
        iconPass: true,
        switch: true,
      },
    ],
    connectingOptions: {
      width: '5.6%',
      height: '0.04rem',
      top: '2.09rem',
      // left: "48.9%",
      left: '57.1%',
    },
  },
  // {
  //     //   addVisible: true,
  // top: "1.88rem",
  // left: "50.6%",
  //     datas: [
  //         {
  //             name: "Repeattest",
  //             icon: "icon-zhongfutuxiangshibiechuli",
  //             title: "短时大量抓拍检测",
  //             desc: "同一点位极短时间多次抓拍同一目标",
  //             left: "10px",
  //             iconSetting: true,
  //             switch: true
  //         },
  //         // {
  //         //     name: "FuzzyDetection",
  //         //     icon: "icon-mohutuxiangshibiechuli",
  //         //     title: "模糊图像识别处理",
  //         //     desc: "多算法验证图片是否能提取对象特征",
  //         //     left: "10px",
  //         //     iconSetting: true,
  //         // },
  //     ],
  //     connectingOptions: {
  //         width: "1.6%",
  //         height: "0.04rem",
  // top: "2.09rem",
  // left: "65%",
  //     },
  // },
  {
    addVisible: false,
    top: '1.46rem',
    // left: "66.7%",
    // top: "1.88rem",
    // left: "50.6%",
    left: '62.9%',
    datas: [
      {
        name: 'daxiaotest',
        icon: 'icon-daxiaotuguanlianzhengquejiance',
        title: '大小图关联正确检测',
        desc: '检测小图是否出现在大图中',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      {
        name: 'UniqueDetection',
        icon: 'icon-xiaotuweiyirenlianjiancechuli',
        title: '小图唯一人脸检测处理',
        desc: '多算法验证小图是否只包含唯一人脸',
        left: '10px',
        iconSetting: false,
        switch: true,
      },
      // {
      //   icon: "icon-sharedservice",
      //   title: "设备编码格式检测",
      //   desc: "需符合《GB/T 28181 2016》中关于设备编码的规定",
      //   left: "10px",
      //   iconSetting: false,
      // },
      // {
      //   name: "testing",
      //   icon: "icon-sharedservice",
      //   title: "空间信息检测",
      //   desc: "经纬度与地址大量重复、 经纬度越界、经纬度偏 移检测",
      //   left: "10px",
      //   iconSetting: true,
      // },
    ],
    connectingOptions: {
      width: '5.6%',
      height: '0.04rem',
      top: '2.09rem',
      // left: "82.6%",
      // top: "2.09rem",
      // left: "66.3%",
      left: '78.8%',
    },
  },
  {
    addVisible: false,
    top: '1.88rem',
    // left: "84.4%",
    // left: "68.05%",
    left: '84.6%',
    datas: [
      {
        name: 'output',
        icon: 'icon-zu1665',
        title: '数据输出',
        desc: '追踪查阅数据最终检测结果',
        left: '10px',
        iconSetting: false,
        switch: false,
      },
    ],
    // connectingOptions: {
    //     width: "0.51rem",
    //     height: "0.04rem",
    //     top: "2.8rem",
    //     left: "2.84rem",
    //     angle: 90,
    // },
  },
  // {
  //     addVisible: false,
  //     top: "3.11rem",
  //     left: "2.59rem",
  //     datas: [
  //         {
  //             name: "Repeattest",
  //             icon: "icon-sharedservice",
  //             title: "重复图像识别处理",
  //             desc: "同一点位极短时间多次 抓拍同一目标",
  //             left: "10px",
  //             iconSetting: true,
  //         },
  //     ],
  //     connectingOptions: {
  //         width: "1.26rem",
  //         height: "0.04rem",
  //         top: "3.3rem",
  //         left: "3.68rem",
  //         // angle: 90,
  //     },
  // },
];
export { faceData };
