<template>
  <div class="ui-slider">
    <div class="slider-content">
      <Slider v-model="score" class="width-sm fl" :disabled="disabled" show-tip="never"></Slider>
      <span class="inline ml-sm white-color">{{ score }}%</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.ui-slider {
  overflow: hidden;
  padding: 5px 0;
  .slider-content {
    line-height: normal;
    //background: #113666;
    overflow: hidden;
    padding: 0 10px;
    > span {
      vertical-align: sub;
    }
  }
  @{_deep} .ivu-slider-wrap {
    margin: 8px 0;
  }
}
</style>
<script>
export default {
  data() {
    return {
      score: 80,
    };
  },
  mounted() {
    this.score = this.value;
  },
  methods: {},
  watch: {
    score(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.score = val;
    },
  },
  props: {
    value: {
      type: Number,
      default() {
        return 80;
      },
    },
    disabled: {
      type: Boolean,
      default() {
        return true;
      },
    },
  },
  components: {},
};
</script>
