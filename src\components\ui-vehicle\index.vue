<!--
    * @FileDescription: 车牌
    * @Author: H
    * @Date: 2023/5/20
 * @LastEditors: du<PERSON><PERSON> du<PERSON>@qishudi.com
 * @LastEditTime: 2024-06-04 14:07:27
-->
<template>
  <div class="plateNum" :class="size">
    <div
      class="super-box"
      v-if="size == 'super'"
      :style="{ background: getColorByType(color).color }"
    >
      <div :class="'plate-' + getColorByType(color).class">
        <span
          class="license-plate"
          :style="getColorByType(color).style"
          v-clipboard="plateNo"
          v-html="plateNo || '未识别'"
        ></span>
      </div>
    </div>
    <div
      class="new-energy"
      :style="getColorByType(color).style"
      :class="{ douback: color == 15 }"
      v-else
    >
      <span
        v-if="color != 15"
        class="license-plate-small"
        :class="getColorByType(color).class"
        :style="getColorByType(color).style"
        v-clipboard="plateNo"
        v-html="plateNo || '未识别'"
      ></span>
      <div
        class="new-energy-num"
        :class="{ 'douback-num': color == 15 }"
        v-else
        v-clipboard="plateNo"
      >
        <span
          class="license-plate-douColor"
          :class="getColorByType(color).class"
          v-html="plateNo || '未识别'"
        ></span>
      </div>
    </div>
    <div class="energy-dou" v-if="color == 15">
      <span
        class="license-plate-douColor"
        :class="getColorByType(color).class"
        v-html="plateNo || '未识别'"
      ></span>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    color: {
      type: String,
      default: "H",
    },
    plateNo: {
      type: String,
      default: "",
    },
    size: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  methods: {
    getColorByType: (color) => {
      switch (color) {
        case "2":
        case "A":
          return {
            name: "白",
            color: "#ffffff",
            class: "white",
            style: {
              background: "#ffffff",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "6":
        case "C":
          return {
            name: "黄",
            color: "#FDEE38",
            class: "yellow",
            style: {
              background: "#FDEE38",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "9":
        case "G":
          return {
            name: "绿",
            color: "#67D28D",
            class: "green",
            style: {
              background: "#67D28D",
              // border: '1px solid #131313',
              color: "#000",
            },
          };
        case "5":
        case "H":
          return {
            name: "蓝",
            color: "#2379F9",
            class: "blue",
            style: {
              background: "#2379F9",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        case "1":
        case "J":
          return {
            name: "黑",
            color: "#000",
            class: "black",
            style: {
              background: "#000",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        case "15":
          return {
            name: "黄绿",
            color: "#000",
            class1: "yellow",
            class2: "green",
            style: {
              background: "#FDEE38",
              // border: '1px solid #ffffff',
              color: "#000",
            },
            style1: {
              background: "#FDEE38",
              // border: '1px solid #ffffff',
              color: "#000",
            },
            style2: {
              background: "#67D28D",
              // border: '1px solid #ffffff',
              color: "#000",
            },
          };
        case "16":
          return {
            name: "渐变绿",
            color: "#000",
            class: "green",
            style: {
              background: "linear-gradient(to bottom, #fff, #67D28D)",
              // border: '1px solid #ffffff',
              color: "#000",
            },
          };
        case "99":
        case "Z":
          return {
            name: "其他",
            color: "#D9D9D9",
            class: "other",
            style: {
              background: "#D9D9D9",
              // border: '1px solid #ffffff',
              color: "#ffffff",
            },
          };
        default:
          return {
            name: "其他",
            color: "#D9D9D9",
            class: "other",
            style: {
              background: "#D9D9D9",
              // border: '1px solid #ffffff',
              color: "#131313",
            },
          };
      }
    },
  },
};
</script>

<style lang='less' scoped>
.plate-white,
.white::after {
  border: 1px solid #131313;
}
.plate-yellow,
.yellow::after {
  border: 1px solid #131313;
}
.plate-green,
.green::after {
  border: 1px solid #131313;
}
.plate-blue,
.blue::after {
  border: 1px solid #ffffff;
}
.plate-black,
.black::after {
  border: 1px solid #ffffff;
}
.plate-other,
.other::after {
  border: 1px solid #131313;
}

.plateNum {
  // bottom: 0;
  // width: 100%;
  display: flex;
  justify-content: center;
  position: relative;
}
.mini {
  // width: 88px;
  // height: 22px;
  padding: 2px;
  span {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.small {
}
.medium {
  height: 40px !important;
  bottom: 0 !important;
  span {
    font-size: 16px;
    padding: 8px 12px;
  }
}
.douback-num {
  span {
    padding: 0;
  }
}
.super-box {
  padding: 5px;
  border-radius: 3px;
}
.super {
  padding: 5px;
  span {
    font-size: 30px;
    padding: 0 18px;
    height: 100%;
    line-height: inherit;
  }
}
.new-energy {
  padding: 1px;
  border-radius: 2px;

  .new-energy-num {
    border: 1px solid #131313;
    .license-plate-douColor {
      color: #000;
    }
  }
  // .yellow{
  //     color: #FDEE38;
  // }
  // .green{
  //     color: #67D28D;
  // }
  .license-plate-left {
    display: inline-block;
    background: #2379f9;
    /* border-radius: 0.01042rem; */
    font-weight: bold;
    font-size: 14px;
    line-height: 14px;
    font-family: "MicrosoftYaHei-Bold, MicrosoftYaHei";
    padding: 4px 0px 4px 6px;
    color: #fff;
    position: relative;
    &::after {
      content: "";
      border: 1px solid #131313;
      position: absolute;
      width: calc(~"100% - 4px");
      height: calc(~"100% - 4px");
      top: 2px;
      left: 5px;
      border-right: none;
    }
  }
  .license-plate-right {
    display: inline-block;
    background: #2379f9;
    /* border-radius: 0.01042rem; */
    font-weight: bold;
    font-size: 14px;
    line-height: 14px;
    font-family: "MicrosoftYaHei-Bold, MicrosoftYaHei";
    padding: 4px 6px 4px 0;
    color: #fff;
    position: relative;
    &::after {
      content: "";
      border: 1px solid #131313;
      position: absolute;
      width: calc(~"100% - 4px");
      height: calc(~"100% - 4px");
      top: 2px;
      left: 0px;
      border-left: none;
    }
  }
}
.douback {
  position: relative;
  padding: 2px;
  &::before {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    width: 31%;
    height: 100%;
    overflow: hidden;
    left: 0;
    background-color: #fdee38; /* 左边背景颜色 */
  }
  &::after {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    width: 69%;
    height: 100%;
    overflow: hidden;
    left: 31%;
    background-color: #67d28d; /* 右边背景颜色 */
  }
}
.energy-dou {
  position: absolute;
  top: 4px;
  border: 1px solid;
  padding: 0 1px;
  .license-plate-douColor {
    font-weight: bold;
  }
}
</style>
