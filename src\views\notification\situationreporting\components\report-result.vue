<template>
  <ui-modal v-model="visible" :title="title" width="35rem" @onCancel="handleReset" :loading="loading">
    <examine-info :detailData="detailData"></examine-info>
    <template #footer>
      <Button class="plr-30" @click="handleReset">取 消</Button>
    </template>
  </ui-modal>
</template>

<script>
export default {
  name: 'report-result',
  props: {
    title: {
      type: String,
      default: '审核结果',
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      detailData: {},
    };
  },
  methods: {
    init(row) {
      this.visible = true;
      this.detailData = row;
    },
    handleReset() {
      this.visible = false;
    },
  },
  components: {
    ExamineInfo: require('./examine-info.vue').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding-left: 50px;
}
</style>
