.btn-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  .advanced-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 30px;
    font-size: 14px;
    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
.search {
  padding: 10px 20px 0;
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;
  .ivu-form-inline {
    width: 100%;
  }
  .ivu-form-item {
    margin-bottom: 0;
    margin-right: 30px;
    display: flex;
    // align-items: flex-start;
    align-items: center;
    .add-subtract{
        cursor: pointer;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    /deep/ .ivu-form-item-content {
      display: flex;
    }
  }
  .general-search {
    display: flex;
    width: 100%;
    .input-content {
      flex: 1;
      display: flex;
      .upload-input-list {
        display: flex;
        max-width: 440px;
      }
      .other-search {
        display: flex;
        flex: 1;
        box-sizing: border-box;
        flex-direction: column;
        padding-left: 10px;
        .other-search-top {
          display: flex;
          border-bottom: 1px dashed #fff;
        }
        .ivu-form-item {
          display: flex;
          margin-bottom: 10px;
          /deep/ .ivu-form-item-label {
            padding-right: 10px;
          }
          .ivu-input-wrapper,
          .ivu-select {
            width: 200px;
          }
        }
        .other-search-bottom {
          display: flex;
          justify-content: space-between;
          padding-top: 10px;
          box-sizing: border-box;
          .slider-form-item {
            /deep/ .ivu-form-item-content {
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
  }
  .advanced-search {
    display: flex;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    z-index: 11;
    max-height: 0px;
    top: 100%;
    left: 0;
    transition: max-height 0.3s;
    overflow: hidden;
    flex-direction: column;
    .search-container {
      display: flex;
      overflow-y: scroll;
      .search-item-container {
        flex: 1;
        overflow-y: auto;
        padding-bottom: 20px;
      }
      .img-search {
        width: 500px;
        border-top: 1px solid #d3e3fe;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
    }
    .classify-content {
      background: #f9f9f9;
      display: flex;
      border-top: 1px solid #d3d7de;
      border-right: 1px solid #d3d7de;
      width: 100%;
      .classify-name {
        width: 50px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: bold;
        font-size: 14px;
        padding-top: 15px;
        text-align: center;
      }
      .items {
        flex: 1;
        background: #fff;
        padding: 0 10px;
      }
    }
    .operate-bottom {
      display: flex;
      align-items: center;
      padding: 10px 0;
      justify-content: flex-end;
      border-top: 1px solid #d3d7de;
    }
    .advanced-search-item {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px dashed #fff;
      align-items: center;
      &.cur {
        // background: #F9F9F9;
        background: #e0e9f5;
      }
      &:last-child {
        border-bottom: 0;
      }
      .ivu-form-item {
        &.percent-70 {
          width: 70%;
        }
        &.percent-60 {
          width: 60%;
        }
        display: flex;
        margin: 0;
        .text-radio-group {
          margin-left: -10px;
        }
      }
      /deep/ .ivu-form-item-content {
        display: flex;
        align-items: center;
      }
    }
  }
  .advanced-search-show {
    .advanced-search {
      max-height: calc(~"100vh - 280px");
      transition: max-height 0.7s;
    }
    .advanced-search-text {
      /deep/ .icon-jiantou {
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
}

.vehicle-front,
.vehicle-back {
  position: relative;
  margin-top: 70px;
  img {
    display: block;
    width: 350px;
    margin: auto;
  }
  span {
    position: absolute;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
    &:hover {
      color: #2c86f8;
    }
    &.cur {
      color: #2c86f8;
    }
    &.vehicle-cp {
      left: -30px;
      top: 104px;
    }
    &.vehicle-pp {
      left: -20px;
      top: 70px;
    }
    &.vehicle-cspz {
      left: -10px;
      top: 40px;
    }
    &.vehicle-zjh {
      left: 30px;
      top: 15px;
    }
    &.vehicle-bj {
      left: 85px;
      top: -2px;
    }
    &.vehicle-gj {
      left: 145px;
      top: -20px;
    }
    &.vehicle-tc {
      right: 70px;
      top: -8px;
    }
    &.vehicle-zyb {
      right: 1px;
      top: -6px;
    }
    &.vehicle-hs {
      right: -8px;
      top: 26px;
    }
    &.vehicle-csys {
      right: -58px;
      top: 96px;
    }
    &.vehicle-back-cspz {
      left: -48px;
      top: 136px;
    }
    &.vehicle-back-cp {
      left: -30px;
      top: 102px;
    }
    &.vehicle-back-pp {
      left: -5px;
      top: 62px;
    }
    &.vehicle-back-chg {
      left: 15px;
      top: 30px;
    }
    &.vehicle-back-tx {
      left: 58px;
      top: -5px;
    }
    &.vehicle-back-xlj {
      right: 50px;
      top: -8px;
    }
    &.vehicle-back-hs {
      right: -30px;
      top: 42px;
    }
  }
}
