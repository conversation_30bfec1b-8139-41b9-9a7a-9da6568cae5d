<template>
  <!-- 人脸抓拍数据合理性 -->
  <div class="face-capture-rationality auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false">
        <template #status>
          <i class="icon-font icon-budabiao font-red" v-if="statisticalList.qualified === '2'"></i>
          <i class="icon-font icon-dabiao font-green" v-if="statisticalList.qualified !== '2'"></i>
        </template>
      </statistics>
      <div class="information-echart">
        <div class="echart-box" v-ui-loading="{ loading: echartLoading, tableData: echartData }">
          <draw-echarts
            v-if="echartData.length != 0"
            :echart-option="determinantEchart"
            :echart-style="ringStyle"
            ref="captureChart"
            class="charts"
          ></draw-echarts>
          <span class="next-echart" v-if="echartData.length > comprehensiveConfig.basicNum">
            <i
              class="icon-font icon-youjiantou1 f-12"
              @click="scrollRight('captureChart', echartData, [], comprehensiveConfig.basicNum)"
            ></i>
          </span>
        </div>
      </div>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div class="export">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="search-wrapper">
        <ui-label class="inline" label="设备编码" :width="70">
          <Input class="width-md" placeholder="请输入设备编码" v-model="searchData.deviceId"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="设备名称" :width="70">
          <Input class="width-md" placeholder="请输入设备名称" v-model="searchData.deviceName"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="检测结果" :width="70">
          <Select class="width-lg" v-model="searchData.checkStatus" clearable placeholder="请选择检测结果">
            <Option :value="1" label="正常"></Option>
            <Option :value="2" label="异常"></Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" label="异常原因" :width="70">
          <Select
            class="width-lg"
            v-model="searchData.messages"
            placeholder="请选择异常原因"
            multiple
            :max-tag-count="1"
          >
            <Option value="ERROR_TODAY_NO_DATA" label="昨日无抓拍"></Option>
            <Option value="ERROR_NO_DATA" label="无抓拍数据"></Option>
            <Option value="ERROR_TOO_LESS_DATA" label="抓拍数据过少"></Option>
            <Option value="ERROR_DATA_SWOOP" label="抓拍数量突降"></Option>
          </Select>
        </ui-label>
        <ui-label :width="70" class="inline" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button class="mr-sm" @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
        </ui-label>
      </div>
      <div class="list auto-fill">
        <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
              row.deviceId
            }}</span>
          </template>
          <template #checkStatus="{ row }">
            <span
              class="tag"
              :style="{
                background: row.checkStatus === '1' ? '#0E8F0E' : '#BC3C19',
              }"
              >{{ checkStatusList(row.checkStatus) }}</span
            >
          </template>
          <template #tagNames="{ row }">
            <tags-more :tag-list="row.tagList || []"></tags-more>
          </template>
          <template #action="{ row }">
            <ui-btn-tip
              icon="icon-chakanjietu"
              content="查看抓拍图片"
              class="mr-md"
              @click.native="checkReason(row)"
            ></ui-btn-tip>
            <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
          </template>
        </ui-table>
        <loading v-if="loading"></loading>
      </div>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
      <face-pictute
        v-model="capturePictureVisible"
        v-if="capturePictureVisible"
        :tableData="reasonTableData"
        :reasonPage="reasonPage"
        :reasonLoading="reasonLoading"
        @handlePageChange="handlePageChange"
        @handlePageSizeChange="handlePageSizeChange"
        title="人脸抓拍图片"
      ></face-pictute>
      <customize-filter
        v-model="customSearch"
        :customize-action="customizeAction"
        :content-style="contentStyle"
        :field-name="fieldName"
        :checkbox-list="deviceTagData"
        :default-checked-list="defaultCheckedList"
        @confirmFilter="confirmFilter"
      >
      </customize-filter>
    </div>
  </div>
</template>

<style lang="less" scoped>
.face-capture-rationality {
  position: relative;
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: 755px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }
    .information-echart {
      position: relative;
      display: inline-block;
      width: 650px;
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .echart-box {
        width: 100%;
        height: 100%;
      }
      .echarts-box {
        width: 100%;
        height: 100% !important;
        .charts {
          width: 100%;
          height: 100% !important;
        }
      }
      .next-echart {
        width: 22px;
        height: 22px;
        background: #02162b;
        border: 1px solid #10457e;
        opacity: 1;
        border-radius: 4px;
        top: 50%;
        right: 8px;
        position: absolute;
        text-align: center;
        line-height: 22px;
        transform: translate(0, -50%);
        .icon-youjiantou1 {
          color: var(--color-primary);
          font-size: 12px;
          vertical-align: top !important;
        }
        &:active {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid var(--color-primary);
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: #4e9ef2;
            font-size: 12px;
            vertical-align: top !important;
          }
        }
        &:hover {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid #146ac7;
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: var(--color-primary);
            font-size: 12px;
            vertical-align: top !important;
          }
        }
      }
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 48px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }
    .list {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      .tag {
        display: inline-block;
        width: 54px;
        height: 22px;
        border-radius: 4px;
        text-align: center;
        vertical-align: middle;
      }
    }
  }
  .export {
    @{_deep}.ivu-select-dropdown {
      position: absolute !important;
      left: 1647px !important;
      top: 364px !important;
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import dataZoom from '@/mixins/data-zoom';
import downLoadTips from '@/mixins/download-tips';
import { mapGetters, mapActions } from 'vuex';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import taganalysis from '@/config/api/taganalysis';
export default {
  name: 'face-capture-rationality',
  mixins: [dataZoom, downLoadTips],
  data() {
    return {
      ringStyle: {
        width: '96%',
        height: '250px',
      },
      determinantEchart: {},
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '人脸卡口总量',
          value: 0,
          icon: 'icon-renliankakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          textColor: 'color1',
          type: 'number',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
        },
        {
          name: '抓拍数量异常设备',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          name: '抓拍数量合格设备',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
        },
        {
          name: '人脸抓拍数量合格率',
          value: 0,
          icon: 'icon-xinxihegeZDRshu',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          textColor: 'color9',
          type: 'percentage',
          key: 'status',
          qualified: '0',
        },
      ],
      loading: false,
      tableColumns: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          width: 120,
          ellipsis: true,
          tooltip: true,
        },
        { title: '抓拍总数量', key: 'captureCount', width: 100 },
        { title: '近天抓拍数量', key: 'captureRecentlyCount', width: 120 },
        { title: '昨日抓拍数量', key: 'captureTodayCount', width: 120 },
        { title: '持续无抓拍天数', key: 'noCaptureDays', width: 120 },
        {
          title: '历史同天平均抓拍量',
          key: 'historyAverageCount',
          width: 180,
          renderHeader: (h) => {
            return (
              <Tooltip max-width="400" transfer>
                <span class="vt-middle">历史同天平均抓拍量</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
                <template slot="content">
                  <p class="mb-md f-14">抓拍数量突降计算逻辑：</p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    昨日抓拍量C1： 2021/10/19日抓拍量；
                  </p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    历史同天抓拍量C2： 平台上线至2021年10月 19 日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；
                  </p>
                  <p class="f-12">
                    <div class="white-circle mr-xs"></div>
                    若（C2-C1）/C2 >= 50%（ 配置值），则判定抓拍数据量突降。
                  </p>
                </template>
              </Tooltip>
            );
          },
        },
        { title: '昨日变化', key: 'changeRatio', width: 80 },
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 90,
          slot: 'checkStatus',
        },
        {
          title: '异常原因',
          key: 'message',
          width: 150,
          ellipsis: true,
          tooltip: true,
        },
        { title: '检测时间', key: 'examineTime', width: 150 },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        checkStatus: '',
        messages: '',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      exportLoading: false,
      reasonLoading: false,
      selectTabs: [],
      statisticalList: {},
      rankData: [],
      paramsList: {},
      currentRow: {},
      capturePictureVisible: false,
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      echartLoading: false,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  mounted() {
    this.getTagList();
  },
  methods: {
    ...mapActions({
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    checkStatusList(checkStatus) {
      return checkStatus === '1' ? '正常' : '异常';
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    search() {
      this.searchData.pageNum = 1;
      this.getTableData();
    },

    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            checkStatus: this.searchData.checkStatus,
            messages: this.searchData.messages,
          },
        };
        this.$_openDownloadTip();
        /* const res = await this.$http.post(
          evaluationoverview.exportDeviceDetailData,
          params
        )*/
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    // 柱状图统计
    async getGraphsInfo() {
      this.echartLoading = true;
      let data = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      };
      try {
        // let res = await this.$http.post(evaluationoverview.getGraphsInfo, data)
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getGraphsInfo,
          data,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        this.echartData = res.data.data || [];
        this.initRing();
        this.echartLoading = false;
      } catch (err) {
        console.log(err);
        this.echartLoading = false;
      }
    },
    // 表格
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            checkStatus: this.searchData.checkStatus,
            messages: this.searchData.messages,
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        this.copySearchDataMx(this.searchData);
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        /*let res = await this.$http.post(
          evaluationoverview.getDetailData,
          params
        )*/
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },

    initRing() {
      this.barData = this.echartData.map((row, index) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion || 0,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
      if (this.echartData.length != 0) {
        setTimeout(() => {
          this.setDataZoom('captureChart', [], this.comprehensiveConfig.basicNum);
        });
      }
    },

    async getReason() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.currentRow.deviceId,
            deviceType: this.currentRow.deviceType,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.reasonLoading = true;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getSecondaryPopUpData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        /*let res = await this.$http.post(
          evaluationoverview.getSecondaryPopUpData,
          params
        )*/
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    checkReason(row) {
      this.currentRow = row;
      this.capturePictureVisible = true;
      this.getReason(row);
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticsList[0].value = val.actualNum;
          this.statisticsList[1].value = val.actualNum;
          this.statisticsList[2].value = val.unqualifiedNum;
          this.statisticsList[3].value = val.qualifiedNum;
          this.statisticsList[4].value = val.resultValue || '0%';
          this.statisticalList = val;
          this.statisticsList[4].qualified = val.qualified;
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getTableData(); //表格
          this.getGraphsInfo(); //柱状图
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
      systemConfig: 'common/getSystemConfig',
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  components: {
    statistics: require('@/components/icon-statistics').default,
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    facePictute: require('./components/face-pictute.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
