<template>
  <div class="top" :class="visible ? 'more-search-show' : ''">
    <RadioGroup
      v-model="queryParam.operationType"
      type="button"
      @on-change="radioChange"
    >
      <Radio
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.key"
        >{{ item.value }}</Radio
      >
    </RadioGroup>
    <div class="right">
      <div class="inline-box">
        <div class="title wid100">时间类型：</div>
        <selectTag
          class="selectTag"
          :list="timeList"
          ref="taskTimeType"
          vModel="taskTimeType"
          @selectItem="selectItem"
        />
        <div class="item fixWidth">
          <div class="title wid100">任务名称：</div>
          <Input
            v-model="queryParam.taskName"
            placeholder="请输入任务名称"
          ></Input>
        </div>
      </div>
      <div class="btn">
        <Button class="margin" type="primary" @click="query()">查询</Button>
        <Button @click="reset()">重置</Button>
      </div>
      <div class="btn-group" @click="toggleSearch(!visible)">
        <img src="@/assets/img/down-circle-icon.png" alt />
        <div class="more more-search-text">
          {{ visible ? "普通检索" : "高级检索" }}
        </div>
      </div>
    </div>
    <div class="more-search">
      <slot></slot>
    </div>
  </div>
</template>
<script>
import { deepCopy } from "@/util/modules/common";
import selectTag from "../../../components/select-tag.vue";
export default {
  props: {
    radioList: {
      type: Array,
      default: () => [],
    },
    taskList: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "",
    },
    // 区分tab
    compareType: {
      type: [Number, String],
      default: "",
    },
  },
  components: { selectTag },
  data() {
    return {
      dateType: 1,
      queryParam: {
        operationType: 99,
        taskName: "",
        taskId: "",
        compareType: "1",
        taskTimeType: "",
      },
      timeList: [
        { name: "永久", value: 0 },
        // { name: '周期', value: '2' },
        { name: "自定义", value: 1 },
      ],
      visible: false,
    };
  },
  async mounted() {
    let query = this.$route.query;
    if (query.taskId && this.compareType === query.compareType) {
      this.queryParam.taskId = query.taskId;
    }
  },
  methods: {
    /**
     * @description: 切换状态
     */
    radioChange() {
      this.$emit("query");
    },

    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryParam.endAlarmTime = value.endDate;
    },

    /**
     * @description: 查询
     */
    query() {
      this.visible = false;
      this.$emit("query");
    },

    /**
     * @description: 重置
     */
    reset() {
      this.dateType = 1;
      this.queryParam.operationType = 99;
      this.queryParam.taskId = null;
      this.queryParam.taskName = "";
      this.queryParam.taskTimeType = null;
      this.$refs.taskTimeType.currentIndex = -1;
      this.$nextTick(() => {
        this.$emit("reset");
      });
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件的方法
     * @return {object}
     */
    getQueryParams() {
      let query = { ...this.queryParam };
      return query;
    },

    /**
     * @description: 收起 | 展开高级搜索
     * @param {boolean} flag 状态
     */
    toggleSearch(flag = false) {
      this.visible = flag;
    },
  },
};
</script>
<style lang="less" scoped>
.top {
  position: relative;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dfdfdf;
  padding-bottom: 16px;
  /deep/ .ivu-radio {
    margin-right: 0 !important;
  }
  /deep/ .ivu-radio-wrapper-checked {
    background: rgba(44, 134, 248, 0.1) !important;
  }
  .right {
    display: flex;
    .inline-box {
      display: inline-flex;
      align-items: center;
    }
    .quick-date-wrap {
      display: flex;
      align-items: center;
    }
    .item {
      display: flex;
      .title {
        line-height: 36px;
        font-size: 14px;
        text-wrap: nowrap;
      }
      .wid100 {
        width: 100px;
        min-width: 70px;
      }
    }
    .fixWidth {
      width: 250px;
      margin-left: 26px;
    }
    .time-form {
      display: flex;
      align-items: center;
    }
    .right20 {
      margin-right: 20px;
    }
    .btn {
      padding: 0 30px;
      .margin {
        margin-right: 12px;
      }
    }
    .more {
      line-height: 36px;
      color: #1678f5;
      cursor: pointer;
    }
  }
}

.more-search {
  display: flex;
  position: absolute;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  z-index: 20;
  max-height: 0px;
  top: 100%;
  left: 0;
  transition: 0.3s;
  overflow: hidden;
  flex-direction: column;
  box-shadow: 0 2px 3px #b7cbe5;
}

.btn-group {
  display: flex;
  align-items: end;
  .more-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    // margin-right: 30px;
    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
.more-search-show {
  .more-search {
    max-height: 300px;
    transition: 0.7s;
    padding-top: 20px;
    margin-top: 1px;
  }
  .more-search-text {
    /deep/ .icon-jiantou {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
}
.selectTag {
  display: flex;
  align-items: center;
}

.btn-group {
  height: 34px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    transform: rotate(180deg);
    transition: transform 0.2s;
  }
}
.more-search-show {
  .advanced-search {
    max-height: 400px;
    transition: max-height 0.5s;
  }
  .btn-group {
    /deep/img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
</style>
