<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem label="与标准时间允许的偏差" prop="timeDelay">
        <Input class="width-lg" placeholder="秒" v-model="formData.timeDelay" />
        <span class="base-text-color ml-sm">秒</span>
      </FormItem>
      <FormItem label="检测方法" prop="osdModel">
        <Select v-model="formData.osdModel" placeholder="请选择" transfer class="width-lg">
          <Option v-for="e in odsCheckModelList" :key="e.dataKey" :value="e.dataKey">{{ e.dataValue }} </Option>
        </Select>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        timeDelay: '',
        osdModel: '',
        isUpdatePhyStatus: '',
      },
    };
  },
  async created() {
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        timeDelay: '',
        osdModel: '',
        isUpdatePhyStatus: '',
      };
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
    }),
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          timeDelay: this.formData.timeDelay,
          osdModel: this.formData.osdModel,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
