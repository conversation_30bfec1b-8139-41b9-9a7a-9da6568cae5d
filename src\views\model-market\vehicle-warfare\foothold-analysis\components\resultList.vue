<template>
    <div class="result-list-page">
        <div class="result-header">
            <Button class="ml-10" type="primary" size="small" @click="goback(0)">返回</Button>
            <div class="header-r">
                <div class="change-mode">
                    <a href="javascript:void(0)"
                        class="d-mode mr-5"
                        :class="{'active': showTableMode}"
                        title="表格模式"
                        @click="showTableMode=true"></a>
                    <a href="javascript:void(0)"
                        class="s-mode"
                        :class="{'active': !showTableMode}"
                        title="列表模式"
                        @click="showTableMode=false"></a>
                </div>
                <span>共 <span class="t-blue-color">{{pageParams.totalCount}}</span> 条记录</span>
            </div>
        </div>
        
        <ul class="foothold-left-result"
                v-scroll>
            <li class="list-item"
                v-for="(item,index) in holdList"
                :key="index"
                :class="{'active': item.active}">
                <div class="detail-mode"
                    v-show="showTableMode"
                    @click="showCaptureList(item, index)">
                <div class="detail-head">
                    <span class="simple-title">区域{{index+1}}</span>
                    <i :class="item.clazz">{{item.footholdType}}</i>
                    <a href="javascript:void(0);"
                    class="watch-area"
                    @click="showFootHoldArea(item,index,$event)">查看落脚区域</a>
                </div>
                <div class="detail-body">
                    <div class="body-foothold">
                    <i class="balloon">{{item.largeAccessLog.flag}}</i>
                    <div>
                        <p :title="item.largeAccessLog.deviceName">{{item.largeAccessLog.deviceName}}</p>
                    </div>
                    <!-- <span class="addFoothold" ng-click="addFoothold(item,$event)">添加</span> -->
                    </div>
                    <p class="detail-tip">至</p>
                    <div class="body-foothold">
                    <i class="balloon">{{item.lessAccessLog.flag}}</i>
                    <div>
                        <p :title="item.lessAccessLog.deviceName">{{item.lessAccessLog.deviceName}}</p>
                    </div>
                    </div>
                </div>
                <div class="detail-footer">
                    <span class="left-message">
                    平均落脚
                    <i class="important-color">{{item.averageTime.toFixed(2)}}</i>
                    小时
                    </span>
                    <span class="right-message">
                    落脚
                    <i class="important-color">{{item.stayCount}}</i>
                    次
                    </span>
                </div>
                </div>

                <div class="simple-mode"
                    v-show="!showTableMode"
                    @click="showCaptureList(item, index)">
                <div class="simple-body">
                    <span class="simple-foothold-title">区域{{index+1}}</span>
                    <i class="balloon">{{item.largeAccessLog.flag}}</i>
                    <i>至</i>
                    <i class="balloon">{{item.lessAccessLog.flag}}</i>
                    <!-- <span class="addFoothold" @click="addFoothold(item)">添加</span> -->
                    <a href="javascript:void(0)"
                    class="watch-area"
                    @click="showFootHoldArea(item,index, $event)">查看落脚区域</a>
                </div>
                </div>
            </li>
        </ul>

        <div class="result-pagination">
            <ui-page :simple="true" :show-elevator="false" :show-sizer="false" :total="pageParams.totalCount" countTotal :current="pageParams.pageNumber" :page-size="pageParams.pageSize" @pageChange="handleCurrentChange" size="small" :showTotal="false">
			</ui-page>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ResultList',
    props: 
    {
        holdList: {
            type: Array,
            default: []
        },
        pageParams: {
            type: Object,
            default: {
                pageSize: 10,
                pageNumber: 1,
                totalCount: 0
            }
        }
    },
    data(){
        return {
            showTableMode: true,
        }
    },
    methods: {
        goback(step) {
            this.$emit('goback', step)
        },
        showCaptureList(item, index){
            this.$emit('showCaptureList', item, index);
        },
        showFootHoldArea(item, index, event){
            event && event.stopPropagation();
            this.$emit('showFootHoldArea', item, index);
        },
        handleCurrentChange(val){
            if(this.pageParams.pageNumber == val) return;
            this.$emit('change-page', val);
        }
    }
}
</script>

<style lang="less" scoped>
    .result-list-page{
        width: 100%;
        height: 100%;
        position: relative;
        .t-blue-color {
            color: #47a3ff;
        }
        .result-header {
            height: 40px;
            line-height: 40px;
            margin-right: 10px;
            text-align: right;
            // color: #989cad;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .header-r {
                display: flex;
                align-items: center;
            }
            .change-mode {
                display: flex;
                align-items: center;
                margin-right: 10px;
                a {
                    display: inline-block;
                    width: 13px;
                    height: 13px;
                    opacity: 0.2;
                    background: url("~@/assets/img/two-model.png") no-repeat;
                    &.active {
                        opacity: 1 !important;
                    }
                }
                .d-mode {
                    background-position: -13px -13px;
                }
                .s-mode {
                    background-position: 0 0;
                }
            }
        }
        .foothold-left-result {
            position: absolute;
            width: 100%;
            top: 40px;
            left: 0;
            right: 0;
            bottom: 44px;
            li {
                position: relative;
                border-bottom: 1px solid #d3d7de;
                cursor: pointer;
                .balloon {
                    background: url("~@/assets/img/list-item-mark.png") no-repeat -16px 4px;
                    width: 16px;
                    height: 30px;
                    line-height: 26px;
                    text-align: center;
                    color: #fff;
                    display: inline-block;
                }
                .detail-mode {
                    margin: 10px;
                    padding: 5px 10px;
                .detail-head {
                    line-height: 30px;
                    height: 30px;
                    span {
                        float: left;
                        margin-right: 5px;
                    }
                    .work-foothold {
                        color: #fff;
                        background-color: #839ae7;
                        padding: 1px 10px;
                        border-radius: 10px;
                        height: 18px;
                        line-height: 18px;
                    }

                    .live-foothold {
                        color: #fff;
                        background-color: #f8b551;
                        padding: 1px 10px;
                        border-radius: 10px;
                        height: 18px;
                        line-height: 18px;
                    }

                    .temp-foothold {
                        color: #fff;
                        background-color: #989cad;
                        padding: 1px 10px;
                        border-radius: 10px;
                        height: 18px;
                        line-height: 18px;
                    }
                    .watch-area {
                        float: right;
                        margin: 0 5px;
                        color: #47a3ff;
                    }
                }
                .detail-body {
                    overflow: hidden;
                    .body-foothold {
                        overflow: hidden;
                        clear: both;
                        margin: 5px 0;
                    div {
                        display: inline-block;
                        vertical-align: middle;
                        margin-left: 5px;
                        width: 280px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        p {
                            padding: 0 10px;
                            line-height: 20px;
                            overflow: hidden;
                            height: 20px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    }
                    .detail-tip {
                        position: absolute;
                        right: 20px;
                        top: 65px;
                        text-align: right;
                    }
                }
                .detail-footer {
                    line-height: 25px;
                    overflow: hidden;
                    height: 25px;
                    padding: 0 5px;
                    border-top: 1px #d3d7de dotted;
                    .left-message {
                        float: left;
                        overflow: hidden;
                        width: 70%;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                    .right-message {
                        float: right;
                        overflow: hidden;
                        width: 30%;
                        text-align: right;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
                }
                .simple-mode {
                    margin: 0 10px;
                    .simple-body {
                        height: 30px;
                        line-height: 30px;
                        .simple-foothold-title {
                            float: left;
                            overflow: hidden;
                            width: 20%;
                            margin-left: 5px;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                        i {
                            float: left;
                            margin-left: 5px;
                        }
                        .watch-area {
                            float: right;
                            margin: 0 5px;
                            color: #00a0e9;
                        }
                    }
                }
                &:hover {
                    .detail-mode {
                        background-color: #f3f3f3;
                    }
                    .simple-mode {
                        background-color: #f3f3f3;
                    }
                }
                &.active {
                    .balloon {
                        background: url("~@/assets/img/list-item-mark.png") no-repeat 0 4px;
                    }
                    .detail-mode {
                        background-color: #f3f3f3;
                    }
                    .simple-mode {
                        background-color: #f3f3f3;
                    }
                }
            }
            }
        .result-pagination{
            position: absolute;
            bottom: 0;
            right: 0;
            padding: 10px;
            .pages {
                height: auto;
            }
        }
    }
</style>
