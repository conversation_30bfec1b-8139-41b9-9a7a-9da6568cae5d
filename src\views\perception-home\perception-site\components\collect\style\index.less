// .table-content{
    .list-card{
        position: relative;
        background-color: #F9F9F9;
        height: min-content;
        box-sizing: border-box;
        width: 100%;
        margin-bottom: 0;
        .operate-bar {
          height: 30px;
          background: linear-gradient(90deg, rgba(87, 187, 252, 0.8) 0%, #2C86F8 100%);
          border-radius: 0px 0px 4px 0px;
          position: absolute;
          right: -100%;
          transition: all .3s;
          bottom: 0;
          transform: skewX(-20deg);
          .operate-content {
            padding: 0 5px;
            transform: skewX(20deg);
            height: 100%;
            display: flex;
            align-items: center;
            color: #fff;
            /deep/ .ivu-tooltip-rel {
              padding: 6px;
            }
          }
        }
    
      }
        .collection {
            width: 30px;
            height: 30px;
            position: absolute;
            // background: #F29F4C;
            z-index: 2;
            top: 3px;
            right: 3px;
            .bg {
                width: 0px;
                height: 0px;
                border-left: 30px solid transparent;
                border-top: 30px solid #F29F4C;
            }
            .collection-icon {
                position: absolute;
                top: -1px;
                right: 1px;
                /deep/ .iconfont {
                    font-size: 14px;
                    color: #fff;
                }
                .ivu-tooltip-rel {
                    margin-top: 3px;
                }
                /deep/ .icon-shoucang {
                    color: #888888 !important;
                    text-shadow: 0px 1px 0px #e1e1e1;
                }
                /deep/ .icon-yishoucang {
                    color: #f29f4c !important;
                }
            }
        
        }
        .paddingIcon{
            top: 13px;
            right: 13px;
        }
      .empty-card-1{
        width: 10.7%;
      }
      .empty-card-2{
        width: 12.2%;
      }
      .box-1{
        width: 166px;
        height: 198px;
        padding: 10px;
        overflow: hidden;
        border-radius:4px;
        border: 1px solid #D3D7DE;
        box-shadow: 1px 1px 7px #cdcdcd;
        margin-left: 6px; 
        &:hover {
          // border: 1px solid #2C86F8;
          // padding: 9px;
          .operate-bar {
            right: -6px;
            bottom: -1px;
          }
          &:before{
            border-color: #2C86F8;
          }
          .img-content{
            .check-box{
              display: inline-block;
            }
          }
        }
        .img-content {
          width: 100%;
          position: relative;
          border: 1px solid #CFD6E6;
          height: 127px;
          img {
            width: 100%;
            height: 100%;
            display: block;
          }
          .check-box{
            position: absolute;
            top: -5px;
            left: -5px;
            z-index: 1;
            display: none;
          }
          .num, .shade {
            position: absolute;
          }
          .num {
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 4px;
            color: #fff;
            background: linear-gradient(180deg, #5BA3FF 0%, #2C86F8 100%);
            // transform: scale(.5);
            // left: -12px;
            // top: -9px;
            z-index: 8;
          }
          .shade {
              background: rgba(0, 0, 0, .7);
              font-size: 12px !important;
              width: 100%;
              bottom: 0;
              left: 0;
              text-align: center;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
              line-height: 18px;
              padding: 3px 0;
              .plate-number-content{
                  width: 88px;
                  margin: auto;
                  height: 22px;
                  background: #2379F9;
                  border-radius: 2px;
                  color: #fff;
                  padding:2px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  box-sizing: border-box;
                  span{
                      height: 18px;
                      width: 100%;
                      border-radius: 2px;
                      border: 1px solid #FFFFFF;
                      white-space: nowrap;
                      font-size: 12px;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      font-weight: bold;
                  }
              }
          }
    
        }
        .bottom-info {
          padding-top: 5px;
          time, p {
            display: flex;
            align-items: center;
            overflow: hidden;
            text-overflow: ellipsis;
            color: rgba(0, 0, 0, .8);
            white-space: nowrap;
            width: 100%;
            .iconfont {
              margin-right: 2px;
              color: #888;
            }
          }
        }
    
      }
      .box-2 {
      //   width: 12.5%;
        // height: 198px;
        padding: 3px;
        .content{
          overflow: hidden;
          position: relative;
          height: 100%;
          box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
          border-radius: 4px;
          border: 1px solid #D3D7DE;
          &:before{
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 2px solid transparent;
            border-radius: 4px;
          }
    
          .check-box{
            position: absolute;
            top:4px;
            left:4px;
            display:none
          }
          &:hover{
            &:before{
              border-color: #2C86F8;
            }
            .operate-bar{
              right: -6px;
              bottom: -1px;
            }
          }
    
        }
        &:hover{
          &:before{
            border-color: #2C86F8;
          }
          .content{
            .check-box{
              display: inline-block;
            }
          }
          // .operate-bar{
          //   right: -6px;
          //   bottom: -1px;
          // }
        }
        .identifier-content{
          display: flex;
          color: #7263E1;
          font-size: 16px;
          align-items: center;
          border-bottom: 1px solid #D3D7DE;
          padding: 5px 10px;
          font-weight: bold;
          .img-icon{
            width: 20px;
            height: 20px;
            margin-right: 7px;
          }
        }
        .bottom-info {
          padding: 10px;
          time{
              margin-bottom: 8px;
          }
          time, p {
            display: flex;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100%;
            line-height: 18px;
            color: rgba(0,0,0,.8);
            font-size: 12px;
          //   margin-bottom: 4px;
            span{
              flex: 1;
            }
            .iconfont{
              margin-right: 3px;
              font-size: 12px;
              color: #888;
            }
          }
        }
      }
      .checked{
        border: 2px solid #2C86F8;
        padding: 9px;
        .operate-bar {
          right: -6px;
          bottom: -1px;
        }
        .img-content{
          .check-box{
            display: inline-block;
          }
        }
      }
      .isChecked{
        &:before{
          border-color: #2C86F8;
        }
        .content{
          .check-box{
            display: inline-block;
          }
        }
        // .operate-bar{
        //   right: -6px;
        //   bottom: -1px;
        // }
      }
  //   }