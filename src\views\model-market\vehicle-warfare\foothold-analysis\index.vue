<!--
    * @FileDescription: 落脚点分析
    * @Date: 2024/01/10
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-21 15:50:48
-->
<template>
  <div class="foothold-analysis container">
    <ui-loading v-if="loading" />
    <!-- 地图 -->
    <mapCustom
      ref="mapBase"
      mapType="footholdAnalysis"
      :footholdData="currentPageGateList"
      :currentFootholdArea="currentFootholdArea"
    />
    <!-- 左面信息展示框 -->
    <div class="leftBox">
      <div class="search-box">
        <div class="title">
          <p>落脚点分析</p>
        </div>
        <!-- 搜索框 -->
        <search-form
          ref="searchForm"
          v-show="showQueryCondition"
          :taskParams="taskParams"
          @search="handleSearch"
        ></search-form>
        <!-- 搜索结果 -->
        <div class="left-search-content" v-if="!showQueryCondition">
          <result-list
            :holdList="holdList"
            :pageParams="pageParams"
            @goback="goback"
            @change-page="changePage"
            @showCaptureList="showCaptureList"
            @showFootHoldArea="showFootHoldArea"
          ></result-list>
        </div>
      </div>
    </div>

    <Modal
      v-model="dialogVisible"
      footer-hide
      sticky
      title="详情"
      width="520px"
      center
      :mask="false"
    >
      <div class="foothold-detail-container" v-scroll>
        <ul class="pic-whole-picInfo">
          <li v-for="(item, $index) in pointList" :key="$index">
            <div class="foothold-index">
              {{ $index + 1 }}
            </div>

            <div class="whole-picInfo-pic" style="float: left">
              <div class="img-outclude">
                <img v-viewer :src="item.trail1.sceneImg" alt="" />
              </div>
              <p>{{ item.trail1.absTime }}</p>
              <p :title="item.trail1.deviceName">
                {{ item.trail1.deviceName }}
              </p>
            </div>

            <div class="whole-picInfo-pic" style="float: left">
              <div class="img-outclude">
                <img v-viewer :src="item.trail2.sceneImg" alt="" />
              </div>
              <p>{{ item.trail2.absTime }}</p>
              <p :title="item.trail2.deviceName">
                {{ item.trail2.deviceName }}
              </p>
            </div>

            <div class="foothold-duration">
              <p>落脚时长</p>
              <p>
                <span>{{ item.stayTime }}</span> 小时
              </p>
            </div>
          </li>
        </ul>
      </div>
    </Modal>
  </div>
</template>
<script>
import mapCustom from "../components/map/index.vue";
import searchForm from "./components/searchForm.vue";
import resultList from "./components/resultList.vue";
import { vehiclePointList, getRegion } from "@/api/modelMarket";
import { mapMutations } from "vuex";
export default {
  name: "foothold-analysis",
  components: {
    mapCustom,
    searchForm,
    resultList,
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      params: {},
      pageParams: {
        pageNumber: 1,
        totalCount: 0,
        pageSize: 10,
      },
      showQueryCondition: true,
      currentPageGateList: [],
      holdList: [],
      cameraList: [],
      mapFlag: [],
      pointList: [],
      currentFootholdArea: null,
      dialogVisible: false,
    };
  },
  async created() {
    if (Toolkits.isEmptyObject(this.taskParams)) {
      this.setLayoutNoPadding(true);
    }
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  async mounted() {
    if (!Toolkits.isEmptyObject(this.taskParams) && this.taskParams.taskResult)
      this.loading = true;
    await this.getCameraList();
    this.mapFlag = [
      "A",
      "B",
      "C",
      "D",
      "E",
      "F",
      "G",
      "H",
      "I",
      "J",
      "K",
      "L",
      "M",
      "N",
      "O",
      "P",
      "Q",
      "R",
      "S",
      "T",
    ];
    // 推荐中心查看
    console.log("taskParams: ", this.taskParams);
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      if (this.taskParams.queryStartTime)
        this.$refs.searchForm.queryParams.startTime =
          this.taskParams.queryStartTime;
      if (this.taskParams.queryEndTime)
        this.$refs.searchForm.queryParams.endTime =
          this.taskParams.queryEndTime;
      if (this.taskParams.params)
        this.$refs.searchForm.queryParams = {
          ...this.$refs.searchForm.queryParams,
          ...this.taskParams.params,
        };
      if (this.taskParams.taskResult) this.$refs.searchForm.handleSearch();
      this.loading = false;
    }
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    handleSearch(params) {
      console.log(params, "params");
      this.params = { ...params };
      this.queryList(true);
    },
    queryList(isSearch) {
      let vm = this;
      let param = {
        id: this.params.plateNo + "_5",
        startTime: this.params.startTime,
        endTime: this.params.endTime,
        stayTime: this.params.stayTime,
        plateNo: this.params.plateNo,
        pageNumber: this.pageParams.pageNumber,
        pageSize: this.pageParams.pageSize,
      };
      this.loading = true;
      vehiclePointList(param)
        .then((res) => {
          if (res.code == 200) {
            vm.showQueryCondition = false;
            let list = res.data.entities;
            let gateList = [];
            vm.currentPageGateList = [];
            let roadmonitorIds = [];
            list.forEach((t) => {
              switch (t.stayType) {
                case 1:
                  t.clazz = "live-foothold";
                  t.footholdType = "居住落脚点";
                  break;
                case 2:
                  t.clazz = "work-foothold";
                  t.footholdType = "工作落脚点";
                  break;
                default:
                  t.clazz = "temp-foothold";
                  t.footholdType = "临时落脚点";
              }
              //var largeIndex = roadmonitorIds.indexOf(t.largeAccessLog.deviceId);
              t.largeAccessLog = t.pointList[0].trail1;
              t.lessAccessLog = t.pointList[0].trail2;
              let largeIndex = roadmonitorIds.indexOf(
                t.largeAccessLog.deviceId
              );
              if (largeIndex === -1) {
                gateList.push(t.largeAccessLog);
                roadmonitorIds.push(t.largeAccessLog.deviceId);
                t.largeAccessLog.flag = vm.mapFlag[roadmonitorIds.length - 1];
              } else {
                t.largeAccessLog.flag = vm.mapFlag[largeIndex];
              }

              var lessIndex = roadmonitorIds.indexOf(t.lessAccessLog.deviceId);
              if (lessIndex === -1) {
                gateList.push(t.lessAccessLog);
                roadmonitorIds.push(t.lessAccessLog.deviceId);
                t.lessAccessLog.flag = vm.mapFlag[roadmonitorIds.length - 1];
              } else {
                t.lessAccessLog.flag = vm.mapFlag[lessIndex];
              }

              let lessCamera = vm.cameraList.find((camera) => {
                return camera.deviceGbId == t.lessAccessLog.deviceId;
              });
              if (lessCamera) {
                t.lessAccessLog.deviceName = lessCamera.deviceName;
                t.lessAccessLog.longitude = lessCamera.geoPoint.lon;
                t.lessAccessLog.latitude = lessCamera.geoPoint.lat;
              }

              let largeCamera = vm.cameraList.find((camera) => {
                return camera.deviceGbId == t.largeAccessLog.deviceId;
              });
              if (largeCamera) {
                t.largeAccessLog.deviceName = largeCamera.deviceName;
                t.largeAccessLog.longitude = largeCamera.geoPoint.lon;
                t.largeAccessLog.latitude = largeCamera.geoPoint.lat;
              }

              t.pointList.forEach((item) => {
                let camera = vm.cameraList.find((o) => {
                  return o.deviceGbId == item.trail1.deviceId;
                });
                if (camera) {
                  item.trail1.deviceName = camera.deviceName;
                  if (item.trail1.deviceId == item.trail2.deviceId) {
                    item.trail2.deviceName = item.trail1.deviceName;
                  } else {
                    var ca = vm.cameraList.find((o) => {
                      return o.deviceGbId == item.trail2.deviceId;
                    });
                    if (ca) item.trail2.deviceName = ca.deviceName;
                  }
                }
              });
            });
            list[0].active = true;
            vm.holdList = list;
            vm.currentPageGateList = gateList;
            vm.showFootHoldArea(vm.holdList[0]);
            vm.pageParams.pageNumber = res.data.pageNumber;
            vm.pageParams.pageSize = res.data.pageSize;
            vm.pageParams.totalCount = res.data.total;
          }
        })
        .catch((err) => {})
        .finally(() => {
          this.loading = false;
        });
    },
    changePage(val) {
      if (this.pageParams.pageNumber == val) return;
      this.pageParams.pageNumber = val;
      this.queryList();
    },
    //查看落脚区域
    showFootHoldArea(item, index, event) {
      event && event.stopPropagation();
      let vm = this;
      vm.holdList.forEach((t) => {
        t.active = false;
      });
      item.active = true;
      if (index || index == 0) this.$set(this.holdList, index, item);
      vm.currentFootholdArea = item;
    },
    goback(step) {
      if (step == 0) {
        this.showQueryCondition = true;
        this.holdList = [];
        this.$refs.mapBase.clearPoint();
        this.dialogVisible = false;
      }
    },
    setCameraList(obj) {
      this.cameraList = obj.cameraList;
    },
    getCameraList() {
      let self = this;
      return new Promise((resolve) => {
        getRegion({ dataTypeList: [1, 2] }).then((res) => {
          let pointList = res.data.entities.filter((d) => {
            return d.deviceType;
          });
          let deviceIdList = [];
          pointList.forEach((p) => {
            deviceIdList.push(p.deviceGbId);
          });
          let tt = deviceIdList.join(",");
          let deviceObj = {
            cameraList: pointList,
            deviceIdList: tt,
          };
          self.setCameraList(deviceObj);
          resolve();
        });
      });
    },
    showCaptureList(item, index) {
      let vm = this;
      vm.pointList = item.pointList;
      vm.dialogVisible = true;
      vm.holdList.forEach((t) => {
        t.active = false;
      });
      item.active = true;
      this.$set(this.holdList, index, item);
    },
  },
};
</script>
<style lang="less" scoped>
.foothold-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .leftBox {
    position: absolute;
    top: 10px;
    left: 10px;
    // 头部名称
    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      height: 40px;
      position: relative;
      line-height: 40px;
      padding-left: 20px;
      border-bottom: 1px solid #d3d7de;
      display: flex;
      justify-content: space-between;
      align-items: center;
      top: 0;
      z-index: 1;
      background: #fff;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      &:before {
        content: "";
        position: absolute;
        width: 3px;
        height: 20px;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
        background: #2c86f8;
      }
      span {
        color: #2c86f8;
      }
      /deep/.ivu-icon-ios-close {
        font-size: 30px;
        cursor: pointer;
      }
    }
    .search-box {
      background: #fff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      width: 370px;
      filter: blur(0px);
      .left-search-content {
        height: calc(~"100vh - 170px");
      }
    }
  }
}
.foothold-detail-container {
  width: 100%;
  height: 400px;
  //padding: 10px;
  .pic-whole-picInfo {
    display: inline-block;
    li {
      display: inline-block;
      .foothold-index {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        float: left;
        background-color: #45a2ff;
        text-align: center;
        line-height: 20px;
        color: #fff;
      }
      .whole-picInfo-pic {
        position: relative;
        margin: 0px 5px 10px;
        border: 1px solid #d3d7de;
        .img-outclude {
          width: 180px;
          height: 180px;
          position: relative;
          img {
            width: 100%;
            height: 100%;
            cursor: pointer;
          }
        }
        p {
          width: 180px;
          padding: 0 5px;
          line-height: 24px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #515a6e;
        }
      }
      .foothold-duration {
        float: left;
        p {
          line-height: 30px;
          span {
            color: #45a2ff;
            font-size: 18px;
          }
        }
      }
    }
  }
}
</style>
