<template>
  <div class="archives-layout">
    <iMenuHead :baseInfo="baseInfo" @on-change="changeArchives"></iMenuHead>
    <div class="archives-container">
      <router-view :baseInfo="baseInfo" />
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import iMenuHead from "./menu-head";
import { getPersonBaseInfo, addViews } from "@/api/realNameFile.js";
import {
  getDeviceBaseInfo,
  getElectricDeviceInfo,
  getETCDeviceInfo,
  getWIFIDeviceInfo,
  getRFIDDeviceInfo,
  getGPSDeviceInfo,
} from "@/api/device";
import {
  getVehicleBaseInfo,
  queryNonMotorVehicleList,
} from "@/api/vehicleArchives";
import { getPlaceArchiveDetailInfoAPI } from "@/api/placeArchive";
export default {
  name: "archivesLayout",
  components: {
    iMenuHead,
  },
  data() {
    return {
      baseInfo: {},
    };
  },
  watch: {
    $route: {
      handler(to, from) {
        if (from?.path == "/vehicle-archive/vehicle-profile") {
          this.changeArchives();
        }
        // 防止在其他页签修改了档案标签，跳回概览页重新请求
        if (to.path.includes("dashboard")) {
          this.getPersonBaseInfoRequest();
        }
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  computed: {
    // 路由地址
    pathName() {
      return this.$route.path.split("/")[1];
    },
    // 档案类型
    archiveType() {
      let type = "";
      switch (this.pathName) {
        case "people-archive":
          type = "people";
          break;
        case "juvenile-archive":
          type = "people";
          break;
        case "importantPerson-archive":
          type = "people";
          break;
        case "community-archive":
          type = "people";
          break;
        case "placeControl-archive":
          type = "people";
          break;
        case "video-archive":
          type = "video";
          break;
        case "device-archive":
          type = "device";
          break;
        case "place-archive":
          type = "place";
          break;
        case "community-place-archive":
          type = "place";
          break;
        case "placeControl-place-archive":
          type = "place";
          break;
        default:
          type = "car";
      }
      return type;
    },
  },
  async created() {
    await this.getDictData();
    // 基本信息
    this.getPersonBaseInfoRequest();
    addViews({ module: this.archiveType });
    this.appInfo();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
      appInfo: "appInfo",
    }),
    getPersonBaseInfoRequest() {
      let { archiveNo, source, deviceType } = this.$route.query;
      //#region 基本信息
      // 实名档案 未成年人档案
      if (
        this.pathName === "people-archive" ||
        this.pathName === "juvenile-archive" ||
        this.pathName === "importantPerson-archive" ||
        this.pathName === "community-archive" ||
        this.pathName === "placeControl-archive"
      ) {
        getPersonBaseInfo({
          archiveNo: archiveNo,
          dataType: source === "zdr" ? 3 : 1,
        })
          .then((res) => {
            this.baseInfo = res.data;
            this.baseInfo.archiveNo = archiveNo;
          })
          .catch(() => {})
          .finally(() => {});
      }
      // 视频档案
      if (this.pathName === "video-archive") {
        getPersonBaseInfo({
          archiveNo: archiveNo,
          dataType: 2,
        })
          .then((res) => {
            this.baseInfo = res.data;
            this.baseInfo.archiveNo = archiveNo;
          })
          .catch(() => {})
          .finally(() => {});
      }
      // 车辆档案
      if (this.pathName === "vehicle-archive") {
        getVehicleBaseInfo(eval(decodeURIComponent(archiveNo))).then((res) => {
          this.baseInfo = res.data;
          this.baseInfo.archiveNo = res.data.archiveNo;
        });
      }
      // 设备档案
      if (this.pathName === "device-archive") {
        getDeviceBaseInfo({ deviceId: archiveNo }).then((res) => {
          this.baseInfo = res.data;
        });
      }
      // 非机动车档案
      if (this.pathName === "non-motor-archive") {
        queryNonMotorVehicleList({
          plateNo: eval(decodeURIComponent(archiveNo)),
        }).then((res) => {
          if (res.data.entities) {
            this.baseInfo = res.data.entities[0];
            this.baseInfo.archiveNo = eval(decodeURIComponent(archiveNo));
          }
        });
      }
      // 场所档案
      if (
        this.pathName === "place-archive" ||
        this.pathName === "community-place-archive" ||
        this.pathName === "placeControl-place-archive"
      ) {
        getPlaceArchiveDetailInfoAPI(archiveNo).then((res) => {
          this.baseInfo = res.data;
          if (
            this.baseInfo.labelIds &&
            typeof this.baseInfo.labelIds === "string"
          ) {
            // 场所档案返回的labelIds为labelId以,拼接的字符串，需要转为数组
            this.baseInfo.labelIds = this.baseInfo.labelIds.split(",");
          }
        });
      }
      // 感知设备档案
      if (this.pathName === "device-archives") {
        let req = null;
        switch (deviceType) {
          case "3":
            req = getWIFIDeviceInfo;
            break;
          case "4":
            req = getElectricDeviceInfo;
            break;
          case "5":
            req = getRFIDDeviceInfo;
            break;
          case "6":
            req = getGPSDeviceInfo;
            break;
          case "16":
            req = getETCDeviceInfo;
            break;
        }
        req(archiveNo).then((res) => {
          this.baseInfo = res.data;
        });
      }
    },
    changeArchives() {
      setTimeout(() => {
        this.getPersonBaseInfoRequest();
      }, 300);
    },
  },
};
</script>
<style scoped="scoped" lang="less"></style>
