<template>
  <div class="search-image-container">
    <!-- 图片上传成功后，选择图片 -->
    <image-recognition ref="imageRecognition" :dataCopper="dataCopper" :tempUrl="tempUrl" @getMinImgUrl="getMinImgUrl" v-if="imageRecognitionVisible" />
    <img v-show="false" :src="imageSrc" alt />
  </div>
</template>
<script>
import imageRecognition from '@/components/ui-upload-img/image-recognition.vue'
import {  picturePick } from '@/api/wisdom-cloud-search'
// getAllStructure,
export default {
  name: 'search-image',
  components: { imageRecognition },
  props: {},
  data() {
    return {
      tempUrl: '',
      imageSrc: '',
      dataCopper: [],
      urlList: {},
      imageRecognitionVisible: false,
      selectedUploadIndex: 0, //选中的uploadIndex
      picData: {
        algorithmType: '2', //1人脸 2 车辆 3 人体 4 非机动车
        similarity: '0.75' // 相似度
      },
      algorithmTypeMap: {
        faces: '1',
        motor: '2',
        non_motor: '4'
      }
    }
  },
  created() {},
  methods: {
    async searchImage(picData, src) {
      try {
        if (!src) return this.$Message.error('获取图片失败')
        this.fileUploading = this.$Message.loading({ content: '正在获取特征值...', duration: 0 })
        this.picData = { ...this.picData, ...picData }
        let proxySrc = await this.$util.common.getImageProxy(src)
        let file = await this.$util.common.getImage(proxySrc)
        let fileData = new FormData()
        fileData.append('file', file)
        fileData.append('algorithmType', this.picData.algorithmType)
        let { data } = await picturePick(fileData)
        if (!data.length) {
          return this.$Message.error('没有识别出目标，请选择其它图片')
        }
        if (data.length == 1) {
          this.tempUrl = window.URL.createObjectURL(file)
          this.oneImageInfo(data)
          return
        }
        this.imageRecognition()
        this.imageDataHandle(data)
        this.tempUrl = window.URL.createObjectURL(file)
      } catch (e) {
        console.log(e)
      } finally {
        this.fileUploading()
        this.fileUploading = null
      }
    },
    async imageStructure(src) {
      try {
        //重置 算法类型、阈值
        this.picData = {}
        if (!src) return this.$Message.error('获取图片失败')
        this.fileUploading = this.$Message.loading({ content: '正在获取特征值...', duration: 0 })
        let params = {
          url: src
        }
        let { data } = await getAllStructure(params)
        if (!data) {
          return this.$Message.error('没有识别出目标，请选择其它图片')
        }
        this.imageRecognition()
        this.convertImageBox(data)
        this.tempUrl = src
      } catch (e) {
        console.log(e)
      } finally {
        this.fileUploading()
        this.fileUploading = null
      }
    },
    imageRecognition(data) {
      this.imageRecognitionVisible = true

      this.$nextTick(() => {
        this.$refs.imageRecognition.init(data)
      })
    },
    getMinImgUrl(urlObj, algorithmType) {
      this.urlList = urlObj
      let params = {
        algorithmType: this.picData.algorithmType || algorithmType,
        similarity: this.picData.similarity || "0.75",
        urlList: this.urlList
      }
      this.$emit('on-submit', params)
    },
    /**
     * 一张图片操作
     */
    async oneImageInfo(list) {
      let _this = this
      await this.imageDataHandle(list)
      this.$nextTick(() => {
        let canvas = document.createElement('canvas')
        const { left, top, right, bottom } = list[0]
        canvas.width = right - left
        canvas.height = bottom - top
        let ctx = canvas.getContext('2d')
        let image = new Image()
        image.crossOrigin = 'anonymous' // 网络图片跨域
        // image.src = 'https://img0.baidu.com/it/u=2484538263,336635826&fm=26&fmt=auto&gp=0.jpg'
        image.src = this.tempUrl
        image.onload = () => {
          ctx.drawImage(image, left, top, right, bottom, 0, 0, right, bottom)
          // ctx.drawImage(image, 295, 40, 100, 100, 0 ,0, 100, 100)
          // var img = canvas.toDataURL("image/jpeg", 1); // toDataUrl可以接收2个参数，参数一：图片类型，参数二： 图片质量0-1（不传默认为0.92）
          // 将图片转成base64格式
          _this.imageSrc = canvas.toDataURL('image/png')
          let obj = {
            feature: list[0].feature,
            fileUrl: _this.imageSrc
          }
          _this.urlList = obj
          let params = {
            ..._this.picData,
            urlList: _this.urlList
          }
          _this.$emit('on-submit', params)
        }
      })
    },
    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      let arr = []
      list.forEach(item => {
        let obj = {
          x: item.left,
          y: item.top,
          width: item.right - item.left,
          height: item.bottom - item.top,
          feature: item.feature
        }

        arr.push(obj)
      })
      this.dataCopper = arr
    },
    /**
     * person人体 不支持搜索
     * @param data
     */
    convertImageBox(data) {
      let imageBoxList = []
      Object.keys(data).forEach(key => {
        let structures = data[key] || []
        if (Array.isArray(structures) && key !== 'person') {
          structures.forEach(item => {
            let { rect, feature, feat_int8, box } = item
            // faces 特殊处理 算法返回的不一样
            if (key === 'faces') {
              imageBoxList.push({
                x: rect.left,
                y: rect.top,
                width: rect.right - rect.left,
                height: rect.bottom - rect.top,
                feature: feature,
                type: key,
                algorithmType: this.algorithmTypeMap[key]
              })
            } else {
              imageBoxList.push({
                ...box,
                feature: feat_int8,
                type: key,
                algorithmType: this.algorithmTypeMap[key]
              })
            }
          })
        }
      })
      console.log('imageBoxList', imageBoxList)
      this.dataCopper = imageBoxList
    }
  }
}
</script>
<style scoped lang="less">
.search-image-container {
  position: relative;
}
</style>
