<template>
  <div class="filter">
    <div class="left-container">
      <ui-label label="检测任务" :width="70" class="inline">
        <Select class="width-260" v-model="task" @on-change="handleChangeTask" filterable placeholder="请选择检测任务">
          <Option v-for="(item, index) in optionList" :value="item.taskSchemeId" :key="index">{{
            item.taskName
          }}</Option>
        </Select>
      </ui-label>
      <ui-label label="执行时间" :width="70" class="inline ml-lg">
        <DatePicker
          class="input-width"
          v-model="searchData.startTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择执行开始时间"
          @on-ok="onOk"
          @on-change="startTimeChange"
          confirm
          clearable
          :disabled="!task"
        >
        </DatePicker>
        <span class="ml-sm mr-sm base-text-color">--</span>
        <DatePicker
          class="input-width"
          v-model="searchData.endTime"
          type="datetime"
          format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择执行结束时间"
          @on-ok="onOk"
          @on-change="endTimeChange"
          confirm
          clearable
          :disabled="!task"
        >
        </DatePicker>
      </ui-label>
      <ui-label label="最新时间" :width="70" class="inline ml-lg">
        <Select
          v-model="searchData.tagType"
          class="width-md"
          clearable
          placeholder="请选择时间"
          @on-change="onChangeDate"
        >
          <Option v-for="(item, index) in resultOptionList" :key="index" :label="item.examineTime" :value="item.id">
          </Option>
        </Select>
      </ui-label>
    </div>
    <div class="right-container">
      <ui-label :width="0" label=" " class="fr">
        <Button type="primary" class="fr ml-lg" @click="dataExport" :loading="loading">
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle"> </i>
          <span class="vt-middle">数据导出</span>
        </Button>
      </ui-label>
    </div>
  </div>
</template>

<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import { mapGetters } from 'vuex';

export default {
  name: 'device-statistics',
  props: {
    data: {},
  },
  data() {
    return {
      task: '',
      optionList: [],
      taskObj: {},
      searchData: {
        startTime: '',
        endTime: '',
      },
      loading: false,
      resultOptionList: [],
    };
  },
  methods: {
    handleChangeTask(val) {
      this.taskObj = this.optionList.find((item) => item.taskSchemeId === val) || {};
      this.$emit('on-statistics-change', this.taskObj);
    },
    async dataExport() {
      try {
        this.loading = true;
        let params = {
          evaluationStatisticsTypeId: this.$parent.currentTree.id,
          orgCode: this.getDeviceTestOverview.currentOrgCode,
          sbdwlx: this.getDeviceTestOverview.sbdwlx,
          sbgnlx: this.getDeviceTestOverview.sbgnlx,
          resultId: this.taskObj.resultId,
        };
        let data = await this.$http.post(inspectionrecord.getEvaluationOverviewExport, params, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(data);
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    onChangeDate(val) {
      this.taskObj.resultId = val;
      this.$emit('on-statistics-change', this.taskObj);
    },
    startTimeChange(val) {
      this.searchData.startTime = val;
    },
    endTimeChange(val) {
      this.searchData.endTime = val;
    },
    onOk() {
      if (this.searchData.startTime && this.searchData.endTime) {
        this.getEvaluationResultList();
      }
    },
    async getEvaluationResultList() {
      try {
        let params = {
          taskId: this.task,
          startTime: this.searchData.startTime,
          endTime: this.searchData.endTime,
        };
        let {
          data: { data },
        } = await this.$http.get(inspectionrecord.getEvaluationResultList, {
          params,
        });
        this.resultOptionList = data;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {
    data: {
      handler: function (val) {
        this.optionList = val;
        this.$nextTick(() => {
          if (val.length) {
            this.taskObj = val[0];
            this.task = val[0].taskSchemeId;
            this.$emit('on-statistics-change', this.taskObj);
          }
        });
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      getDeviceTestOverview: 'inspectionrecord/getDeviceTestOverview',
    }),
  },
};
</script>

<style lang="less" scoped>
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 53px;
}

.col-blue {
  color: var(--color-primary);
}

.col-gay {
  color: #8797ac;
}

.width-260 {
  width: 260px;
}
.input-width {
  width: 200px;
}
</style>
