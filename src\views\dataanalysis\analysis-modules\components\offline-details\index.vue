<template>
  <div class="auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #phyStatus="{ row }">
        <span
          :class="{
            'qualified-color': row.phyStatus === '1',
            'unqualified-color': row.phyStatus === '2',
          }"
        >
          {{ row.phyStatusText || '--' }}
        </span>
      </template>
      <template v-for="(key, index) in showNopageCalenderList" #[key]="{ row }">
        <Tooltip
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          :disabled="(!row[key] && row[key] !== 0) || !(row.startDate && row.endDate)"
          :key="index"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color" :class="{ 'underline-text': row[key] || row[key] === 0 }">
            {{ row[key] || row[key] === 0 ? row[key] : '--' }}
          </span>
          <div slot="content" v-if="row[key]">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.startDate"
              :end-date="row.endDate"
              :hiatus="row.offlineDates"
              :legend-data="legendData"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
    </ui-table>
    <slot name="page"></slot>
  </div>
</template>
<script>
export default {
  name: 'active-details',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    rowData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      showOfflineDetails: false,
      currentRow: {},
      legendData: [
        { text: '在线', color: '#0E8F0E', key: 'success' },
        { text: '离线', color: '#BC3C19', key: 'hiatus' },
      ],
      // 需要显示 nopage-calender组件的 字段值
      showNopageCalenderList: ['accOfflineNum', 'serialOfflineNum'],
    };
  },
  methods: {
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    openOfflineDetails(row, str = '') {
      if (row[str] === 0) return;
      this.showOfflineDetails = true;
      this.currentRow = { ...row };
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    NopageCalender:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/components/nopage-calender.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
.underline-text {
  text-decoration: underline;
  cursor: pointer;
}
.unqualified-color {
  color: var(--color-failed);
}
.qualified-color {
  color: var(--color-success);
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
@{_deep}.calender-tip.ivu-tooltip-popper {
  @{_deep}.ivu-tooltip-content .ivu-tooltip-inner {
    max-width: 100% !important;
    max-height: 100% !important;
    overflow: hidden !important;
  }
}
</style>
