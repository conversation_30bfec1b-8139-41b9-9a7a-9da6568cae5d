<template>
  <div class="catalogmaintain">
    <div class="menu height-full">
      <div class="title">
        <span>目录名称</span>
        <Button type="primary" @click="add">
          <i class="icon-font icon-tianjia"></i>
          <span class="ml-sm">新建目录</span>
        </Button>
      </div>
      <ui-search-tree
        ref="uiTree"
        class="auto-fill"
        node-key="nodeKey"
        placeholder="请输入目录名称"
        :show-checkbox="false"
        :highlight-current="false"
        :tree-data="treeData"
        :default-props="defaultProps"
        :defaultCheckedKeys="[]"
        :tree-loading="treeLoading"
        expandAll
        checkStrictly
        @selectTree="selectTree"
        @contextmenu="showContextmenu"
      >
        <template #label="{ node, data }">
          <span>{{ node.label }}</span>
          <span class="fr mr-sm option context-menu" @click.stop="showContextmenu($event, data, node)">···</span>
        </template>
      </ui-search-tree>
    </div>
    <div class="content-box auto-fill height-full">
      <div class="base mb-md">
        <div class="title mb-sm">
          <i class="icon-font"></i>
          <span>基础信息</span>
        </div>
        <div class="base-message">
          <div class="mb-sm" title="">
            <span class="message-title">目录名称：</span>
            <span class="message">{{ selectedData.name }}</span>
          </div>
          <div class="mb-sm">
            <span class="message-title">直接关联知识数量：</span>
            <span class="message">{{ selectedData.knowledgeCount }}</span>
          </div>
          <div class="mb-sm">
            <span class="message-title">描述：</span>
            <span class="message">{{ selectedData.remark }}</span>
          </div>
          <div>
            <span class="message-title">创建人：</span>
            <span class="message">{{ selectedData.creator }}</span>
          </div>
          <div>
            <span class="message-title">所属单位：</span>
            <span class="message">{{ selectedData.orgName }}</span>
          </div>
          <div>
            <span class="message-title">创建时间：</span>
            <span class="message">{{ selectedData.createTime }}</span>
          </div>
        </div>
      </div>
      <div class="list auto-fill">
        <div class="title">
          <i class="icon-font"></i>
          <span>目录</span>
        </div>
        <div class="search-module mb-sm">
          <div>
            <ui-label class="inline" label="目录名称">
              <Input class="width-lg" placeholder="请输入目录名称"></Input>
            </ui-label>
            <div class="ml-lg inline">
              <Button type="primary" @click="search"> 查询 </Button>
              <Button class="ml-sm" @click="reset"> 重置 </Button>
            </div>
          </div>
          <div>
            <Button type="primary" @click="add">
              <i class="icon-font icon-tianjia"></i>
              <span class="ml-sm">新建目录</span>
            </Button>
            <Button type="primary" class="ml-sm" :loading="deleteLoading" @click="batchDelete">
              <i class="icon-font icon-piliangshanchu mr-xs f-12"></i>
              <span>批量删除</span>
            </Button>
          </div>
        </div>
        <div class="table-module auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="loading"
            @selectTable="selectTable"
          >
            <template #Index="{ row, index }">
              <div :class="row.rowClass">
                <span>{{ index + 1 }}</span>
              </div>
            </template>
            <template #action="{ row }">
              <div>
                <ui-btn-tip class="mr-md" icon="icon-bianji2" content="编辑" @click.native="edit(row)"></ui-btn-tip>
                <ui-btn-tip
                  class="mr-md"
                  :styles="{ color: 'rgb(67, 140, 255)', 'font-size': '16px' }"
                  icon="icon-chakanxiangqing"
                  content="查看"
                  @click.native="view(row)"
                ></ui-btn-tip>
                <ui-btn-tip
                  :styles="{ color: '#CF3939 ', 'font-size': '14px' }"
                  icon="icon-piliangshanchu"
                  content="删除"
                  @click.native="singleDelete(row)"
                ></ui-btn-tip>
              </div>
            </template>
          </ui-table>
          <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
          </ui-page>
        </div>
      </div>
    </div>
    <add-edit
      v-model="addEditShow"
      :modal-action="addEditAction"
      :default-form="defaultForm"
      :catalog="treeData"
      @update="update"
    >
    </add-edit>
    <ul class="right-menu" v-if="contextmenuVisible" :style="contextmenuStyle" v-clickoutside="hideContextmenu">
      <li @click="edit(contextmenuData)">编辑</li>
      <li @click="singleDelete(contextmenuData)">删除</li>
      <li @click="add(contextmenuData)">新建目录</li>
    </ul>
  </div>
</template>
<script>
import knowledgebase from '@/config/api/knowledgebase';
export default {
  name: 'catalogmaintain',
  data() {
    return {
      loading: false,
      treeLoading: false,
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      treeData: [],
      selectedData: {},
      searchData: {
        name: '',
        parentId: '',
        pageNum: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      deleteLoading: false,
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          minWidth: 190,
          title: '目录名称',
          key: 'name',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: '关联知识数量',
          key: 'knowledgeCount',
          align: 'left',
        },
        {
          title: '描述 ',
          key: 'remark',
          align: 'left',
          tooltip: true,
        },
        { width: 160, title: '创建人', key: 'creator' },
        { width: 160, title: '创建时间', key: 'createTime' },
        {
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
      checkedData: [],
      // 编辑
      addEditShow: false,
      addEditAction: {
        action: 'add',
        title: '新增设备',
      },
      defaultForm: {},
      // 右击菜单
      contextmenuVisible: false,
      contextmenuStyle: {
        top: 0,
        left: 0,
      },
      contextmenuData: {},
    };
  },
  async created() {
    await this.getCatalog();
  },
  mounted() {},
  methods: {
    async selectTree(val) {
      await this.getDetail(val);
      Object.assign(this.selectedData, this.defaultForm);
      this.searchData.parentId = val.id;
      this.init();
    },
    async getCatalog() {
      try {
        this.treeLoading = true;
        const res = await this.$http.get(knowledgebase.getAllCatalogue);
        this.treeData = this.$util.common.arrayToJson(res.data.data, 'id', 'parentId');
        if (!this.searchData.parentId) {
          if (this.treeData.length) {
            this.searchData.parentId = this.treeData[0].id;
            Object.assign(this.selectedData, this.treeData[0]);
          }
        }
        this.init();
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(knowledgebase.getCatalogueList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNum = 1;
      this.pageData.pageNumber = 1;
      this.init();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    batchDelete() {
      const ids = this.checkedData.map((row) => row.id);
      if (!ids.length) {
        this.$Message.warning('请选择要删除的数据');
        return false;
      }
      this.$UiConfirm({
        content: `您要删除这${ids.length}项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteFetch(ids);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    singleDelete(row) {
      this.hideContextmenu();
      this.$UiConfirm({
        content: `您要删除${row.name}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteFetch([row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteFetch(ids) {
      try {
        this.deleteLoading = true;
        const res = await this.$http.post(knowledgebase.removeCatalogue, ids);
        this.$Message.success(res.data.msg);
        this.checkedData = [];
        this.update();
      } catch (err) {
        console.log(err);
        this.$Message.error(err.data.msg);
      } finally {
        this.deleteLoading = false;
      }
    },
    add(contextmenuData) {
      this.hideContextmenu();
      this.addEditShow = true;
      this.addEditAction = {
        action: 'add',
        title: '新建目录',
      };
      this.defaultForm = {
        parentId: contextmenuData.id || '',
      };
    },
    async view(row) {
      await this.getDetail(row);
      this.addEditShow = true;
      this.addEditAction = {
        action: 'view',
        title: '查看目录',
      };
    },
    async edit(row) {
      this.hideContextmenu();
      await this.getDetail(row);
      this.addEditShow = true;
      this.addEditAction = {
        action: 'edit',
        title: '编辑目录',
      };
    },
    async getDetail(row) {
      try {
        const res = await this.$http.get(knowledgebase.viewCatalogue, {
          params: { id: row.id },
        });
        this.defaultForm = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    update() {
      this.getCatalog();
    },
    showContextmenu(event, data) {
      // 右击根据鼠标指针所在位置出现
      if (event.type === 'contextmenu') {
        this.contextmenuStyle = {
          top: event.clientY + 'px',
          left: event.clientX + 'px',
        };
      } else {
        /**
         * 点击···根据当前dom所在浏览器位置
         * top = 当前dom所在浏览器顶部距离 + 当前dom的高度 + 10
         * left = 当前dom距离浏览器左侧的距离 + 当前dom的宽度 + 当前右击菜单的宽度
         *  */
        const boundingClientRect = event.target.getBoundingClientRect();
        this.contextmenuStyle = {
          top: boundingClientRect.top + 10 + boundingClientRect.height + 'px',
          left: boundingClientRect.left - 135 + boundingClientRect.width + 'px',
        };
      }
      this.contextmenuVisible = true;

      this.contextmenuData = data;
    },
    hideContextmenu() {
      this.contextmenuVisible = false;
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    AddEdit: require('./add-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .catalogmaintain {
    .option {
      background-color: #08234d;
      &:hover {
        background-color: #2a77cc;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .catalogmaintain {
    .option {
      color: #fff;
      background-color: var(--bg-btn-primary);
      &:hover {
        background-color: var(--bg-btn-primary-hover);
      }
    }
  }
}
.catalogmaintain {
  overflow: hidden;
  .option {
    width: 17px;
    height: 17px;
    line-height: 17px;
    border-radius: 50%;
    text-align: center;
    margin-top: 2px;
  }
  .menu {
    float: left;
    width: 250px;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    background: var(--bg-content);
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10px;
      color: var(--color-content);
    }
    @{_deep} .input-div {
      padding: 0 10px;
    }
    .tree {
      overflow-y: auto;
    }
    @{_deep} .custom-tree-node {
      .context-menu {
        display: none;
      }
      &:hover .context-menu {
        display: inline;
      }
    }
  }
  .content-box {
    background: var(--bg-content);
    margin-left: 255px;
    .title {
      font-size: 16px;
      color: var(--color-display-sub-title);
    }
    .base {
      padding-top: 20px;
      padding-bottom: 10px;
      margin: 0 20px;
      border-bottom: 1px solid var(--border-color);
      .base-message {
        div {
          display: inline-block;
          width: 400px;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .message-title {
          color: var(--color-label);
        }
        .message {
          color: var(--color-content);
        }
      }
    }
    .list {
      .title {
        padding: 20px 0 15px 20px;
      }
      .search-module {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
      }
    }
  }
  .right-menu {
    position: fixed;
    width: 135px;
    z-index: 999;
    li {
      padding: 7px 16px;
      line-height: 20px;
      color: var(--color-select-item);
      background-color: var(--bg-select-dropdown);
      cursor: pointer;
      &:hover {
        background-color: var(--bg-select-item-active);
      }
    }
  }
  @{_deep} .ui-table {
    padding: 0 20px;
  }
}
</style>
