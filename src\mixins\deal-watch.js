/**
 * 解决 watch 多次执行问题
 * import dealWatch from "@/mixins/deal-watch";
 * mixins: [dealWatch]
 */
export default {
  data() {
    return {
      _watchWho: null,
      _callMethod: null,
    };
  },
  methods: {
    /**
     *
     * @param {需要监听的对象} watchWho
     * @param {监听后需要触发的方法} callMethod
     */
    startWatch(watchWho, callMethod, config) {
      this._watchWho = watchWho;
      this._callMethod = callMethod;
      this.unwatch = this.$watch(
        watchWho,
        (news, olds) => {
          callMethod(news, olds);
        },
        config,
      );
    },
  },
  activated() {
    if (this._watchWho && this._callMethod) {
      this.startWatch(this._watchWho, this._callMethod, { deep: true });
    }
  },
  deactivated() {
    !!this.unwatch && this.unwatch();
  },
  beforeDestroy() {
    !!this.unwatch && this.unwatch();
  },
};
