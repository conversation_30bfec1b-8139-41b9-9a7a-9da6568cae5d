import {
  defaultIconStaticsList,
  qualifiedColorConfig,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export const iconStaticsList = [
  ...defaultIconStaticsList,
  {
    name: '视频流质量合格率:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    style: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-xinxichayishebei',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const tableColumns = () => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 50,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '组织机构',
      key: 'orgName',
      align: 'left',
      tooltip: true,
      minWidth: 200,
    },
    {
      title: '设备物理状态 ',
      slot: 'phyStatus',
      minWidth: 120,
    },
    {
      minWidth: 150,
      title: '设备标签',
      slot: 'tagNames',
    },
    {
      title: '检测结果',
      key: 'qualified',
      slot: 'qualified',
      width: 120,
    },
    {
      title: '原因',
      key: 'errorCodeName',
      minWidth: 150,
      tooltip: true,
    },
    {
      title: '备注',
      key: 'reason',
      minWidth: 150,
      tooltip: true,
    },
    {
      title: '检测时间',
      slot: 'videoStartTime',
      width: 160,
    },
    {
      title: '操作',
      slot: 'option',
      align: 'center',
      tooltip: true,
      minWidth: 150,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
    },
  ];
};
