<template>
  <div class="govern-effect-container">
    <ui-table :table-columns="watchTableColumns" :loading="loading" :table-data="tableData" :stripe="false"></ui-table>
  </div>
</template>

<script>
//治理成效
export default {
  name: 'govern-effect',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    data: {},
    tableColumns: {},
    loading: {
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      watchTableColumns: [],
    };
  },
  watch: {
    data: {
      handler(val) {
        this.tableData = val;
      },
      deep: true,
      immediate: true,
    },
    tableColumns: {
      handler(val) {
        this.watchTableColumns = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  methods: {},
};
</script>

<style lang="less" scoped>
.govern-effect-container {
  position: relative;
  height: 100%;
  width: 100%;
  @{_deep} .ui-table {
    height: 100%;
    width: 100%;
    .ivu-table-default {
      background-color: transparent !important;
    }

    .ivu-table-header th {
      background: transparent !important;
      box-shadow: none !important;
      color: #bee2fb !important;
      height: 36px;
      font-size: 12px;
    }
    .ivu-table-body td {
      background: transparent !important;
      color: #bee2fb;
      height: 36px;
      font-size: 12px;
    }
  }
  @{_deep} .color-red {
    color: #ea5252;
  }
  @{_deep} .color-blue {
    color: var(--color-primary);
  }
  @{_deep} .color-green {
    color: #0e8f0e;
  }
}
</style>
