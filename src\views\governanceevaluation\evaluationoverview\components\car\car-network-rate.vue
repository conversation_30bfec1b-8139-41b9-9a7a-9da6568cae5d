<template>
  <!-- 车辆卡口联网率 -->
  <div class="basic-information" ref="contentScroll">
    <div class="information-header">
      <!-- 统计 -->
      <statistics class="statistics" :statistics-list="statisticsList" :isIconBg="true"></statistics>
      <!-- 排行 -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class="" v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class="" v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class="" v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue }}分</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
      <!-- 查询 -->
      <searchBayonet ref="search" class="mb-sm" :paramsList="paramsList" @startSearch="startSearch" />
      <!-- 列表 -->
      <ui-table :maxHeight="contentClientHeight" :table-columns="columns" :table-data="tableData" :loading="loading">
        <!-- <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template> -->
        <template #description="{ row }">
          <span
            class="check-status"
            :class="row.description === '已联网' ? 'bg-b77a2a' : row.description === '未联网' ? 'bg-17a8a8' : ''"
            >{{ row.description }}</span
          >
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize" />
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'car-network-rate',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    searchBayonet: require('./component/searchBayonet.vue').default,
    statistics: require('@/components/icon-statistics').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
  },
  props: {
    indexName: {
      type: String,
      default: '',
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      rankLoading: false,
      exportLoading: false,
      imgList: [],
      bigPictureShow: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      day: '',
      statisticsList: [
        {
          name: '车辆卡口总数量',
          value: 0,
          icon: 'icon-cheliangkakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'total',
          textColor: 'color1',
        },
        {
          name: '实际测设备数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'deviceDataTotal',
          textColor: 'color2',
        },
        {
          name: '已联网设备',
          value: 0,
          icon: 'icon-yilianwangshebei',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'passDeviceDataTotal',
          textColor: 'color6',
        },
        {
          name: '未联网设备',
          value: 0,
          icon: 'icon-weilianwangshebei',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'notPassDeviceDataTotal',
          textColor: 'color4',
        },
        {
          name: '车辆卡口联网率',
          value: 0,
          icon: 'icon-cheliangkakoulianwangjiance',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          qualified: true,
          key: 'resultValue',
          textColor: 'color3',
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        searchData: {},
      },
      indexId: null,
      column: [],
      columns: [],
      contentClientHeight: 0,
    };
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.day = val.time;
          let addColumns = [
            {
              type: 'index',
              width: 70,
              title: '序号',
              fixed: 'left',
              align: 'center',
            },
            { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId' },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              tooltip: true,
              width: 180,
            },
            { title: '组织机构', key: 'orgName', tooltip: true, width: 120 },
            { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
            { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' }, // 点位类型
            { title: val.time + '过车数量', key: 'total' },
            { title: '设备联网状态', key: 'description', slot: 'description' },
          ];
          this.columns = addColumns;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          this.indexId = val.indexId;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 170 * proportion : 0;
    if (this.propertySearchLbdwlx.length === 0) await this.getAlldicData();
    let addColumns = [
      { title: this.day + '过车数量', key: 'total' },
      { title: '设备联网状态', key: 'description', slot: 'description' },
    ];
    this.columns = [...this.column, ...addColumns];
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 初始化
    async init() {
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        searchData: {},
      };
      this.$nextTick(() => {
        this.$refs.search.reashfalf();
      });
      this.getList();
    },
    // 列表接口
    async getList() {
      this.loading = true;
      let data = {
        orgRegionCode: this.paramsList.orgRegionCode,
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.displayType,
        pageSize: this.pageData.pageSize,
        pageNumber: this.pageData.pageNum,
        customParameters: this.pageData.searchData,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (err) {
        // console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 导出
    async getExport() {
      this.exportLoading = true;
      let params = {
        orgRegionCode: this.paramsList.orgRegionCode,
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.displayType,
        customParameters: this.pageData.searchData,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        this.exportLoading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getList();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.getList();
    },
    // 检索
    startSearch(searchData) {
      this.pageData.searchData = {};
      this.pageData.searchData = searchData;
      this.getList();
    },
    //统计
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    @{_deep}.information-statistics {
      width: 1500px;
      height: 252px;
      padding: 20px;
      background: var(--bg-sub-content);
    }
    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      padding: 10px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
  /deep/.information-statistics .statistics-ul .icon-budabiao {
    top: 180px !important;
  }
  /deep/.information-statistics .statistics-ul .icon-dabiao {
    top: 180px !important;
  }
  /deep/.information-statistics .statistics-ul li {
    width: 19.4% !important;
  }
}
</style>
