import { mapGetters } from 'vuex';
import { OPPOSE, SAME, SERVER, UNOPPOSE, UNSAME } from '../util/enum';
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  data() {
    return {
      selfDeviceListByDevice: [],
      selfStatisticsByDevice: {},
      superiorDeviceListByDevice: [],
      superiorStatisticsByDevice: {},
      superiorVideoPlatformOnlineDetail: [],
      selfVideoPlatformOnlineDetail: [],
      loading: false,
      exportDataLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      configData: 'governanceevaluation/configData',
    }),
    OPPOSE() {
      return OPPOSE;
    },
    UNOPPOSE() {
      return UNOPPOSE;
    },
    SAME() {
      return SAME;
    },
    UNSAME() {
      return UNSAME;
    },
  },
  methods: {
    async handleReview(row) {
      let { id } = this.activeItem;
      let params = {
        cascadeIssueId: id,
        oppose: row.oppose === OPPOSE ? UNOPPOSE : OPPOSE,
        ids: [row.id],
      };
      try {
        await this.$UiConfirm({
          content: row.oppose === OPPOSE ? '您确定要取消复核吗？' : '上级检测结果和本级检测结果不一致，提出异议',
          title: '警告',
        });
        await this.resultReview(params);
        await this.initList(false);
      } catch (e) {
        console.log(e);
      }
    },
    async handleReviews(oppose) {
      if (!this.selectTableData.length) return this.$Message.error('请选择结果');
      let { id } = this.activeItem;
      let ids = this.selectTableData.map((item) => item.id);
      let params = {
        cascadeIssueId: id,
        oppose: oppose,
        ids: ids,
      };
      try {
        await this.$UiConfirm({
          content: oppose === UNOPPOSE ? '您确定要取消复核吗？' : '上级检测结果和本级检测结果不一致，提出异议',
          title: '警告',
        });
        await this.resultReview(params);
        await this.initList(false);
        this.selectTableData = [];
      } catch (e) {
        console.log(e);
      }
    },
    async resultReview(val) {
      try {
        let params = {
          cascadeIssueId: null,
          oppose: null,
          ids: [],
          ...val,
        };
        let { data } = await this.$http.post(governanceevaluation.resultReview, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      }
    },
    setStatisticalQuantityList() {
      this.statisticalQuantityList.forEach((item) => {
        item.leftCount = this.superiorStatisticsByDevice[item.key];
        item.rightCount = this.selfStatisticsByDevice[item.key];
      });
    },
    async handleUpdateResult() {
      this.pageData.pageNum = 1;
      await this.initList(true);
      this.setStatisticalQuantityList();
    },
    getParams(refresh) {
      let { id, indexId, batchId, orgCode } = this.activeItem;
      let commonParams = {
        indexId,
        batchId,
        access: 'TASK_RESULT',
        displayType: 'ORG',
        orgRegionCode: orgCode,
      };
      return {
        id,
        firstModelData: {
          requestMethod: 'post',
          uri: '/evaluation/app/taskIndexResultDetail/getFirstModelData',
          id,
          server: SERVER.evaluationApp,
          postForm: commonParams,
        },
        statInfo: {
          requestMethod: 'post',
          uri: '/evaluation/app/taskIndexResultDetail/getStatInfo',
          id,
          server: SERVER.evaluationApp,
          postForm: commonParams,
        },
        refresh,
      };
    },
    async initList(refresh) {
      try {
        this.loading = true;
        let { pageNum, pageSize } = this.pageData;
        let { superiorToken, deployOrgCode } = this.configData;
        let params = {
          ...this.getParams(refresh),
          pageNumber: pageNum,
          pageSize,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.resultComparison, params, {
          headers: {
            token: superiorToken,
            orgCode: deployOrgCode,
          },
        });
        this.selfDeviceListByDevice = data.selfDeviceListByDevice || [];
        this.superiorDeviceListByDevice = data.superiorDeviceListByDevice || [];
        this.selfStatisticsByDevice = data.selfStatisticsByDevice || {};
        this.superiorStatisticsByDevice = data.superiorStatisticsByDevice || {};
        this.superiorVideoPlatformOnlineDetail = data.superiorVideoPlatformOnlineDetail || [];
        this.selfVideoPlatformOnlineDetail = data.selfVideoPlatformOnlineDetail || [];
        this.pageData.totalCount = data.total || 0;
        this.difference = data.difference || 0;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async exportExcel() {
      try {
        this.exportDataLoading = true;
        this.$_openDownloadTip();
        let res = await this.$http.post(governanceevaluation.resultComparisonExport, this.getParams(false));
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportDataLoading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList(false);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList(false);
    },
  },
};
