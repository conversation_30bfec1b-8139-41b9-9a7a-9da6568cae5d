<template>
  <div class="antv-g6-container">
    <div :id="container" class="g6-container" ref="containerRef"></div>
    <transition name="fade">
      <div class="g6-mask" v-show="maskVisible">
        <antv-loading :text="loadingText"></antv-loading>
      </div>
    </transition>
  </div>
</template>
<script lang="jsx">
import G6, { Algorithm } from "@antv/g6";
import G6DefaultConfig from "./config";
import antvLoading from "./components/antv-loading.vue";
import { ViewOperations } from "./factory/view-operations";
import { LayoutSetting, getLayoutConfig } from "./factory/layout-setting";
import { CustomNode } from "./factory/custom-node";
import { TimeBar } from "./factory/timebar";
import { useRegisterBehavior } from "./hooks/custom-behavior";
import { clearStates } from "./util/graph-deal";
import { merge } from "lodash";

export default {
  props: {
    container: {
      type: String,
      default: "g6-container",
    },
    graphData: {
      type: Object,
      default: () => {
        return {
          nodes: [],
          edges: [],
        };
      },
    },
    hasTimebar: {
      type: Boolean,
      default: false,
    },
    hasMinimap: {
      type: Boolean,
      default: false,
    },
    relationCanOperate: {
      type: Boolean,
      default: true,
    },
    layoutData: {
      type: Object,
    },
    defaultLayoutName: {
      type: String,
    },
    isExcavate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      maskVisible: true,
      loadingText: "",
      viewOperations: {},
      layoutSetting: {},
      customBehavior: null,
      G6Config: {},
    };
  },
  created() {
    this.customBehavior = useRegisterBehavior(G6);
  },
  methods: {
    async initGraph() {
      try {
        this.G6Config = { ...G6DefaultConfig(this.container) };
        const customNode = new CustomNode();
        customNode.createCustomNode({
          G6,
          G6Config: this.G6Config,
          relationCanOperate: this.relationCanOperate,
        });
        const plugins = this.setPlugin();
        this.graph = new G6.Graph({
          modes: {
            default: [
              "drag-node",
              {
                type: "drag-canvas",
                enableOptimize: true,
              },
              {
                type: "zoom-canvas",
                enableOptimize: true,
              },
              {
                type: "click-select",
                trigger: "ctrl",
                selectEdge: true,
              },
              "drag-node-move-source-neighbors",
              "activate-relations-custom",
              {
                type: "brush-select",
                trigger: "shift",
                includeEdges: false,
              },
            ],
            ...this.customBehavior,
            removeEvent: [],
          },
          layout: {
            // type: 'gForce',
            // linkDistance: 200,
            // nodeSize: 60,
            // preventOverlap: true,
            // gpuEnabled: false,
            ...getLayoutConfig(this.defaultLayoutName),
            ...this.layoutData,
          },
          plugins,
          ...this.G6Config,
        });
        G6.Util.processParallelEdges(this.graphData.edges);
        this.graph.data({
          nodes: this.graphData.nodes,
          edges: this.graphData.edges,
        });
        this.graph.node((node) => {
          return {
            ...node,
            img: node.img ? node.img : "",
          };
        });
        this.graph.render();

        this.graph.on("node:click", (evt) => {
          const node = evt.item;
          const states = node.get("states");
          if (states.includes("secondary"))
            this.graph.setItemState(node, "selected", false);
        });
        // this.graph.on("node:mouseenter", (evt) => {
        //   const cfg = evt.item._cfg;
        //   if (this.isExcavate && cfg.edges && cfg.edges.length > 0) {
        //     let flag = cfg.edges.some(
        //       (item) => "isExcavate" in item._cfg.model
        //     );
        //     if (!flag) {
        //       this.graph.setMode("removeEvent");
        //     } else {
        //       this.graph.setMode("default");
        //     }
        //   }
        // });

        this.graph.on("node:dblclick", (evt) => {
          const node = evt.item;
          const model = node.getModel();
          this.$emit("nodeDblclick", model);
        });

        this.graph.on("edge:mouseenter", (evt) => {
          const edge = evt.item;
          edge.toFront();
          // 设置目标节点的 hover 状态 true
          this.graph.setItemState(edge, "hover", true);
          if (this.isExcavate && !edge._cfg.model.isExcavate) {
            this.graph.setItemState(edge, "hover", false);
          }
        });

        this.graph.on("edge:mouseleave", (evt) => {
          const edge = evt.item;
          // 设置目标节点的 hover 状态 false
          this.graph.setItemState(edge, "hover", false);
        });

        this.graph.on("edge:click", (evt) => {
          const edge = evt.item;
          const targetNode = edge.getTarget().getModel();
          const sourceNode = edge.getSource().getModel();
          const model = edge.getModel();
          this.$emit("edgeClick", { edge: model, targetNode, sourceNode });
          if (this.isExcavate && !edge._cfg.model.isExcavate) {
            this.graph.setItemState(edge, "selected", false);
          } else {
            this.graph.setItemState(edge, "selected", true);
          }
        });
        this.graph.on("afteritemstatechange", (e) => {
          const item = e.item;
          const state = e.state;
          const type = item.get("type");
          if (["analysis", "secondary", "connectionAnalysis"].includes(state)) {
            if (type === "edge") {
              const model = item.get("model");
              this.updateItemById(item, {
                labelCfg: merge(
                  {},
                  model.labelCfg,
                  this.G6Config.edgeStateStyles[state].labelCfg
                ),
              });
            }
            item.enableCapture(state !== "secondary");
          }
        });
        // this.graph.on("beforeitemstatesclear", (e) => {
        // const item = e.item;
        // const state = e.state;
        // const type = item.get("type");
        // if (type === "edge" && ["analysis", "secondary"].includes(state)) {
        //   debugger;
        //   this.updateItemById(item, {
        //     labelCfg: this.G6Config.edgeStateStyles["analysis"].labelCfg,
        //   });
        // }
        // });

        this.graph.on("nodeselectchange", (e) => {
          e.selectedItems.nodes.forEach((node) => {
            const model = node.get("model");
            this.graph.updateItem(node, {
              stateStyles: {
                selected: {
                  fill: model.fill,
                  stroke: "green",
                  lineWidth: 2,
                  shadowColor: "green",
                  shadowBlur: 10,
                },
              },
            });
          });
        });

        this.graph.on("custom-text-expand:click", (evt) => {
          const node = evt.item;
          const model = node.getModel();
          this.$emit("expandGroup", model);
        });

        this.graph.on("node:contextmenu", (evt) => {
          evt.preventDefault();
          const node = evt.item;
          const model = node.getModel();
          model.clientX = evt.clientX;
          model.clientY = evt.clientY;
          this.$emit("nodeContextmenu", model);
        });

        this.graph.on("canvas:click", () => {
          this.$emit("canvasClick");
        });

        this.graph.on("afterlayout", () => {
          this.maskVisible = false;
        });

        this.graph.on("wheelzoom", () => {
          const zoom = this.graph.getZoom();
          this.$emit("wheelzoom", zoom);
        });

        this.viewOperations = new ViewOperations({ graph: this.graph });
        this.layoutSetting = new LayoutSetting({ graph: this.graph });
      } catch (err) {
        console.error(err, "err");
      }
    },
    setPlugin() {
      const plugins = [];
      if (this.hasTimebar) {
        const timebar = new TimeBar();
        plugins.push(timebar);
      }
      if (this.hasMinimap) {
        const minimap = new G6.Minimap({
          size: [150, 100],
          type: "keyShape",
        });
        plugins.push(minimap);
      }
      return plugins;
    },
    setState(data, state) {
      try {
        data.edges.forEach((edge) => {
          this.graph.setItemState(edge.id || edge, state, true);
        });
        data.nodes.forEach((node) => {
          this.graph.setItemState(node.id || node, state, true);
        });
      } catch (err) {
        console.warn(err, "err");
      }
    },
    clearStates(states) {
      if (states.includes("secondary")) this.initLabelCfg("secondary");
      clearStates(this.graph, states);
    },
    initLabelCfg(state) {
      const edges = this.graph.findAllByState("edge", state);
      const nodes = this.graph.findAllByState("node", state);

      edges.forEach((row) => {
        this.updateItemById(row, {
          labelCfg: this.G6Config.defaultEdge.labelCfg,
        });
        row.enableCapture(true);
      });
      nodes.forEach((row) => {
        row.enableCapture(true);
      });
    },
    focusItem(id) {
      this.graph.focusItem(id);
    },
    downloadFullImage(name = null, type, imageConfig = {}) {
      this.graph.downloadFullImage(name, type, imageConfig);
    },
    deleteNodes(nodes) {
      nodes.forEach((node) => {
        this.graph.removeItem(node);
      });
    },
    deleteItemsById({ nodes = [], edges = [] }) {
      [...nodes, ...edges].forEach((item) => {
        this.graph.removeItem(item.id);
      });
    },
    lock(isLock, nodes) {
      nodes.forEach((node) => {
        isLock ? node.lock() : node.unlock();
      });
    },
    getNodes(callback) {
      return this.graph.getNodes().map((node) => {
        const model = node.get("model");
        callback && callback(node);
        return {
          ...model,
          visible: node.get("visible"),
        };
      });
    },
    getEdges() {
      return this.graph.getEdges().map((edge) => edge.get("model"));
    },
    getNodesByState(state) {
      const nodes = this.graph.getNodes();
      const arr = nodes.filter((node) => node.hasState(state));
      return arr;
    },
    visibleItemById(id, visible) {
      visible ? this.graph.showItem(id) : this.graph.hideItem(id);
    },
    findNeighborsNodes(id, type = null) {
      return this.graph.getNeighbors(id, type).map((row) => row.getModel());
    },
    // 定位节点
    locationItem(id, state = "selected") {
      this.clearStates([state]);
      this.graph.setItemState(id, state, true);
      this.focusItem(id);
      this.itemToFront(this.getNodeById(id));
    },
    itemToFront(item) {
      item.toFront();
    },
    // 获取当前节点的所有边
    findNeighborsEdges(node) {
      return node.getEdges().map((row) => row.getModel());
    },
    /**
     * 根据已经在图谱上的数据去重之后返回需要添加的数据
     * @param {*} data
     */
    getAddNodes(data) {
      const nodes = this.getNodes();
      const edges = this.getEdges();
      const newNodes = [];
      const newEdges = [];
      data.nodes.forEach((relationNode) => {
        const hasNode = nodes.find((node) => node.id === relationNode.id);
        if (!hasNode) {
          newNodes.push(relationNode);
        }
      });
      data.edges.forEach((relationEdge) => {
        const hasEdge = edges.find((edge) => {
          return (
            edge.source === relationEdge.source &&
            edge.target === relationEdge.target
          );
        });
        if (!hasEdge) {
          newEdges.push(relationEdge);
        }
      });
      return {
        nodes: newNodes,
        edges: newEdges,
      };
    },
    addNodes({ nodes, edges }, node) {
      nodes.forEach((nodeModel) => {
        if (node) {
          nodeModel.x = node.x;
          nodeModel.y = node.y;
        }
        if (!node || nodeModel.id !== node.id) {
          this.graph.addItem("node", nodeModel);
        }
      });
      edges.forEach((eModel) => {
        this.graph.addItem("edge", { curveOffset: 0, ...eModel });
      });
    },
    /**
     * 找到合适的圆心防止新关联的节点相互遮挡
     * ！！注意：数据量过大可能会有性能问题
     * @param {*} points
     * @param {*} initialPoint
     * @param {*} radius
     */
    findSuitableCircleCenter(points, initialPoint, radius) {
      let center = initialPoint;
      let isSuitable = false;
      isSuitable = isCircleValid(center, radius, points);

      // 定义一个函数来检查圆内是否包含其他点
      function isCircleValid(center, radius, points) {
        for (let i = 0; i < points.length; i++) {
          if (
            Math.sqrt(
              Math.pow(points[i].x - center.x, 2) +
                Math.pow(points[i].y - center.y, 2)
            ) <=
            radius + 60
          ) {
            return false;
          }
        }
        return true;
      }

      let direction = 0;
      let move = 0;

      // 尝试找到一个合适的圆心位置
      while (!isSuitable) {
        /**
         * 共检测8个方向
         * 0 右 1 左 2 上 3 下 4 右上 5 右下 6 左上 7 左下
         */
        switch (direction) {
          case 0:
            move++;
            center = { x: center.x + move, y: center.y };
            direction++;
            break;
          case 1:
            center = { x: center.x - move, y: center.y };
            direction++;
            break;
          case 2:
            center = { x: center.x, y: center.y - move };
            direction++;
            break;
          case 3:
            center = { x: center.x, y: center.y + move };
            direction++;
            break;
          case 4:
            center = { x: center.x + move, y: center.y - move };
            direction++;
            break;
          case 5:
            center = { x: center.x + move, y: center.y + move };
            direction++;
            break;
          case 6:
            center = { x: center.x - move, y: center.y - move };
            direction++;
            break;
          case 7:
            center = { x: center.x - move, y: center.y + move };
            direction = 0;
            break;
        }
        // 检查新的圆心位置是否合适
        isSuitable = isCircleValid(center, radius, points);
      }

      return center;
    },
    /**
     * 为了避免添加节点后刷新整体布局，这里需要子图布局
     * @param {*} param0
     */
    subgraphLayout({ nodes, edges }, { layoutName, node, level }) {
      const unitRadius = 200;
      const nodesPosition = this.getNodes()
        .filter((row) => row.id !== node.id)
        .map((row) => {
          return {
            x: row.x,
            y: row.y,
          };
        });
      this.addNodes({ nodes, edges }, node);
      // 当扩展节点除了节点本身和新关联的节点只有超过两个时才触发检测节点是否会覆盖
      if (nodes.length > 2) {
        const ce = this.findSuitableCircleCenter(
          nodesPosition,
          { x: node.x, y: node.y },
          unitRadius * level
        );
        this.graph.updateItem(node.id, {
          x: ce.x,
          y: ce.y,
        });
      }

      const subLayout = new G6.Layout[layoutName]({
        center: [node.x, node.y],
        nodeSize: 60,
        nodeSpacing: 50,
        linkDistance: 200,
        unitRadius,
        preventOverlap: true,
        equidistant: true,
        focusNode: node.id,
      });
      subLayout.init({
        nodes,
        edges,
      });
      subLayout.execute();
      this.graph.positionsAnimate();
    },
    toFullDataURL({ callback, type, imageConfig }) {
      this.graph.toFullDataURL(callback, type, imageConfig);
    },
    clear() {
      this.viewOperations.clear();
    },
    updateGraph(data) {
      this.processParallelEdges(data.edges);
      this.graph.read(data);
    },
    changeData(data) {
      G6.Util.processParallelEdges(data.edges);
      this.graph.changeData(data);
    },
    processParallelEdges(edges) {
      G6.Util.processParallelEdges(edges);
    },
    fullScreen(isFullscreen) {
      this.$nextTick(() => {
        if (isFullscreen) {
          this.graph.changeSize(1920, 1080);
        } else {
          this.graph.changeSize(
            this.$refs.containerRef.clientWidth,
            this.$refs.containerRef.clientHeight
          );
        }
      });
    },
    getPointByCanvas(canvasX, canvasY) {
      return this.graph.getPointByCanvas(canvasX, canvasY);
    },
    getNodeById(id) {
      return this.graph.findById(id);
    },
    getNodeProperty(node, filed) {
      return node.get(filed);
    },
    getGraphProperty(filed) {
      return this.graph.get(filed);
    },
    getNodeEdges(node) {
      return node.getEdges().map((edge) => edge.getModel());
    },
    getGraphCenterPoint() {
      return this.graph.getViewPortCenterPoint();
    },
    updateItemById(id, model) {
      this.graph.updateItem(id, model);
    },
    zoomTo(toRatio) {
      this.graph.zoomTo(toRatio);
    },
    fitView(...opts) {
      this.graph.fitView(...opts);
    },
    loadingShow({ visible, text }) {
      this.maskVisible = visible;
      this.loadingText = text || "";
    },
    clearGraphData() {
      this.graph.clear();
    },
    getGraphAlgorithm() {
      return Algorithm;
      // let graphData = data;
      // if(!graphData) graphData = {
      //    nodes :this.graph.getNodes(),
      //    edges :this.graph.getEdges()
      //  }
      //  return {
      //   graphData,
      //   ...getGraphAlgorithm(graphData)
      //  }
    },
  },
  watch: {
    graphData: {
      handler(val) {
        this.loadingShow({ visible: true, text: "数据加载中" });
        if (this.graph) {
          this.changeData(val);
        } else {
          this.initGraph();
        }
      },
    },
  },
  components: {
    antvLoading,
  },
  beforeDestroy() {
    this.graph = null;
  },
};
</script>
<style lang="less" scoped>
.antv-g6-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.g6-mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

.g6-container {
  position: relative;
  width: 100%;
  height: 100%;
  /deep/ .g6-minimap {
    position: absolute;
    bottom: 20px;
    right: 20px;

    .g6-minimap-container {
      width: 100%;
      height: 100%;
      border: 1px solid #999;
    }

    .g6-minimap-viewport {
      border: 2px solid rgb(25, 128, 255);
    }
  }
}
</style>
