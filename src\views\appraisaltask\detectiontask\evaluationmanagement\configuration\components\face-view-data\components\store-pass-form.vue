<!-- 人脸卡口设备图片存储时长达标率（FACE_IMAGE_STORE_PASS ）、车辆卡口设备图片存储时长达标率(VEHICLE_IMAGE_STORE_PASS) -->
<template>
  <div class="store-pass-form">
    <common-form
      :label-width="160"
      class="common-form"
      ref="commonFormRef"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="waycondiction" class="mt-xs" v-if="formData.detectMode === '1'">
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
          </div>
          <div>
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="ml-sm mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <span class="base-text-color mr-sm"
                >抓拍{{ moduleAction.indexType === 'FACE_IMAGE_STORE_PASS' ? '人脸' : '车辆' }}不少于</span
              >
              <InputNumber
                v-model.number="formData.deviceQueryForm.countByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">张</span>
              <p class="color-failed">说明：系统只检测满足条件的设备。</p>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>
    <Form class="form-content" :label-width="160">
      <FormItem label="图片存储时长" class="base-text-color">
        <InputNumber
          v-model.number="formData.storeDuration"
          :min="1"
          :precision="0"
          class="mr-sm width-mini"
        ></InputNumber>
        天
      </FormItem>
      <FormItem label="接口访问最大等待时长" class="base-text-color">
        <InputNumber
          v-model.number="formData.maximumLatency"
          :min="1"
          :precision="0"
          class="mr-sm width-mini"
        ></InputNumber>
        秒
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    configText() {
      // 人脸卡口设备图片存储时长达标率
      let isFace = ['FACE_IMAGE_STORE_PASS'].includes(this.moduleAction.indexType);
      if (isFace) {
        return '人脸';
      } else {
        return '车辆';
      }
    },
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        //this.schemeList = this.getHour()
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            quantityConfig: [],
            deviceDetection: {
              dataConfig: {
                key: 'now', //lately 最近
                value: 'month', // 30 10
                quantity: 0,
              },
            },
            deviceQueryForm: {
              ...this.deviceQueryForm,
            }, //默认值
            visitTimeout: null,
            properties: [],
          };
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      dictData: [], //算法
      formData: {
        storeDuration: null,
        maximumLatency: null,
        // hourStart: null, //时间区间开始时间
        // hourEnd: null, //时间区间结束时间
        // captureNum: null, //抓拍数量
        // properties: [],
      },
      //schemeList: [],
      deviceQueryForm: {
        detectPhyStatus: null,
        filterOnline: null,
        dayByFilterOnline: 2,
        countByFilterOnline: 1,
        startByFilterOnline: 0,
        endByFilterOnline: 23,
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
    };
  },
  async created() {},
  methods: {
    // async getDictData () {
    //   try {
    //     const params = {
    //       typekey: 'vehicle_property',
    //     }
    //     let { data } = await this.$http.get(user.queryByTypeKey, { params })
    //     this.dictData = data.data.map((item) => {
    //       return {
    //         dataDes: item.dataDes,
    //         dataKey: item.dataKey,
    //       }
    //     })
    //   } catch (error) {
    //     console.log(error)
    //   }
    // },
    // 获取时间列表
    // getHour () {
    //   let arr = []
    //   for (var i = 0; i <= 24; i++) {
    //     let sum = null
    //     if (i < 10) {
    //       sum = '0' + i + ':00'
    //     } else {
    //       sum = i + ':00'
    //     }
    //     arr.push({ label: sum, value: i })
    //   }
    //   return arr
    // },
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      return this.$refs['commonFormRef'].handleSubmit();
    },
  },
};
</script>

<style lang="less" scoped>
.store-pass-form {
}
</style>
