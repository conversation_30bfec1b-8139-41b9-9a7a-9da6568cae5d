<template>
  <div class="archives-file">
    <div class="equipment-title">
      <div class="title-name"></div>
    </div>
    <div class="equipment-content">
      <div>
        <div class="equipment-content-header">
          <Title :title="'设 备 信 息'" />
        </div>
        <div class="equipment-content-container">
          <div class="container-header">
            <div class="container-header-left" v-if="info && info.imageUrls && info.imageUrls.length">
              <el-carousel class="equipment-image-list" indicator-position="none" :autoplay="false">
                <el-carousel-item v-for="(item, index) in info.imageUrls" :key="index">
                  <div class="equipment-image-item">
                    <img :src="item" alt="" />
                  </div>
                </el-carousel-item>
                <div class="equipment-image-count" v-if="info && info.imageUrls && info.imageUrls.length">
                  {{ activeIndex }}/{{ info.imageUrls.length }}
                </div>
              </el-carousel>
            </div>
            <div class="container-header-left empty" v-else>
              <i class="icon-font icon-zanwutupian1"></i>
              <div>暂无图片</div>
            </div>
            <div class="container-header-right">
              <div>
                <span class="content-title" :title="info.deviceId"> {{ global.filedEnum.deviceId }}： </span>
                <span :class="info.deviceId ? 'deviceId' : 'base-text-color'">{{ info.deviceId || '' }}</span>
              </div>
              <div>
                <span class="content-title"> {{ global.filedEnum.deviceName || '' }}： </span>
                <span class="base-text-color">{{ info.deviceName || '' }}</span>
              </div>
              <div>
                <span class="content-title"> 点位类型： </span>
                <span class="base-text-color">{{ info.sbdwlxText || '' }}</span>
              </div>
              <div>
                <span class="content-title"> 功能类型： </span>
                <span class="base-text-color">{{ info.sbgnlxText || '' }}</span>
              </div>

              <div>
                <span class="content-title"> 采集区域： </span>
                <Tooltip
                  v-if="info.sbcjqyText && info.sbcjqyText.length > 46"
                  :max-width="560"
                  :offset="0"
                  :content="info.sbcjqyText"
                  placement="bottom"
                >
                  <span class="base-text-color">{{ info.sbcjqyText || '1' }}</span>
                </Tooltip>
                <span v-else class="base-text-color">{{ info.sbcjqyText || '' }}</span>
              </div>
              <div>
                <span class="content-title"> 设备状态： </span>
                <span class="base-text-color">{{ info.isOnlineText || '' }}</span>
              </div>
              <div>
                <span class="content-title"> 安装时间： </span>
                <span class="base-text-color time">{{ info.azsj || '' }}</span>
              </div>
              <div>
                <span class="content-title"> 所属机构： </span>
                <span class="base-text-color">{{ info.orgName || '' }}</span>
              </div>
            </div>
          </div>
          <div class="container-center">
            <div class="container-center-left">
              <span class="title-right-big">
                <img src="@/assets/img/equipmentfiles/title-right-big.png" alt="" />
              </span>
              <span class="map-camera-active">
                <img v-if="themeType === 'dark'" src="@/assets/img/equipmentfiles/map-camera-active.png" alt="" />
                <img v-else src="@/assets/img/equipmentfiles/map-camera-active-light.png" alt="" />
              </span>
              <span class="content-title"> 地址： </span>
              <span class="base-text-color">{{ info.address }}</span>
            </div>
            <Button type="primary" @click="videoPlayback">
              <i class="icon-font icon-bofanganniu f-12"></i>视频播放
            </Button>
          </div>
          <div class="container-footer">
            <div class="footer-title">设 备 标 签</div>
            <div class="tag-list">
              <tags-more class="inline" :tag-list="info.tagList || []" :tag-data="false" expandAll>
                <span class="btn-add">
                  <Tooltip content="新增">
                    <i class="icon-font icon-tree-add" @click="addTags(info)"></i>
                  </Tooltip>
                </span>
                <div v-if="!info.tagList || !info.tagList.length" class="noData">暂无设备标签，请点击右下角添加</div>
              </tags-more>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div class="equipment-content-header">
          <Title :title="'图 上 位 置'" />
        </div>
        <MapPointLon :id="$route.query.id" :chooseMapItem="chooseMapItem" v-if="Object.keys(chooseMapItem).length" />
      </div>
    </div>
    <!-- 设备属性 -->
    <div class="equipment-attribute">
      <div v-for="(item, index) in attrList" :key="index">
        <div class="attribute-list-title" :key="index" @click="show_detail(index)">
          <div>
            <i class="icon-font" :class="item.icon" :style="{ color: item.color, fontSize: `${item.size}px` }"></i>
            <span class="list-text vt-middle">{{ item.label }}</span>
          </div>
          <div>
            <i
              :class="[!item.show ? 'icon-font icon-xialazhankai arrow' : 'icon-font icon-xialazhankai arrowrun']"
              @click.stop="show_detail(index)"
            ></i>
          </div>
        </div>
        <div v-if="item.show" class="attribute-content">
          <template v-for="(e, i) in item.list">
            <div class="list-item" :key="i" v-if="e.isShow">
              <div :class="[e.asterisk ? 'item-title required-field' : 'item-title']">
                {{ e.value }}
              </div>
              <div class="item-content" v-if="['latitude', 'longitude'].includes(e.key)">
                {{ info[e.key] | filterLngLat }}
              </div>
              <div v-else-if="e.key === 'manufacturer'" class="item-content">
                {{ info.manufacturer || '' | manufacturer(that) }}
              </div>
              <div v-else class="item-content">
                {{ info[e.key] }}
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <!-- 设备历史抓拍分析    -->
    <div class="equipment-attribute-other">
      <div class="title">
        <i class="icon-font icon-shebeilishizhuapaifenxi vt-middle"></i>
        <span class="list-text vt-middle ml-sm"> 设 备 历 史 抓 拍 分 析 </span>
      </div>
      <div class="content-list">
        <LineChart :deviceId="info.deviceId" v-if="info.deviceId" />
      </div>
    </div>
    <!-- 设备在线情况分析 -->
    <div class="equipment-attribute-other">
      <div class="title">
        <i class="icon-font icon-shebeizaixianqingkuangfenxi vt-middle"></i>
        <span class="list-text vt-middle ml-sm"> 设 备 在 线 情 况 分 析 </span>
      </div>
      <div class="content-list">
        <archives-calender :deviceId="info.deviceId" v-if="info.deviceId"> </archives-calender>
      </div>
    </div>
    <!-- 修改记录 -->
    <div class="equipment-attribute-other">
      <div class="title">
        <i class="icon-font icon-xiugaijilu vt-middle"></i>
        <span class="list-text vt-middle ml-sm"> 修 改 记 录 </span>
      </div>
      <div class="content-list">
        <ui-table
          class="table-record auto-fill"
          :table-columns="tableColumns"
          :table-data="deviceRecord"
          :loading="loading"
          :max-height="530"
          :minusHeight="530"
        >
          <template #remark="{ row }">
            <p v-for="(item, index) in row.remark" :key="index">
              <span>修改了</span>
              <span class="ml-sm chang-name">【{{ item.propertyName }}】</span>
              <span class="ml-sm">旧值</span>
              <span class="ml-sm">{{ item.oldValue }}</span>
              <span class="ml-sm">新值</span>
              <span class="ml-sm">{{ item.newValue }}</span>
            </p>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :checkboxList="queryTagList"
      :field-name="fieldName"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
      :content-style="contentStyle"
    >
    </customize-filter>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onVideoPlayCancel"
    >
      <div>
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import algorithm from '@/config/api/algorithm';
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import vedio from '@/config/api/vedio-threm';
export default {
  data() {
    return {
      id: null,
      info: {},
      attrList: [
        {
          icon: 'icon-jichushuxing',
          label: '基 础 属 性',
          show: false,
          color: '#04b87f',
          size: '20',
          name: 'base',
        },
        {
          icon: 'icon-zhuangtaishuxing',
          label: '状 态 属 性',
          show: false,
          color: '#fc8180',
          size: '20',
          name: 'staus',
        },
        {
          icon: 'icon-guanlishuxing',
          label: '管 理 属 性',
          show: false,
          color: '#e28d14',
          size: '16.5',
          name: 'manage',
        },
        {
          icon: 'icon-biaoqianshuxing',
          label: '标 签 属 性',
          show: false,
          color: '#a858f4',
          size: '17',
          name: 'label',
        },
        {
          icon: 'icon-anquanshuxing',
          label: '安 全 属 性',
          show: false,
          color: '#11b978',
          size: '20',
          name: 'safe',
        },
      ],
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      queryTagList: [],
      customSearch: false,
      customizeAction: {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      defaultCheckedList: [],
      chooseOne: {
        tagList: [],
      },
      that: this,
      facturer: [],
      deviceRecord: [],
      tableColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        { title: '修改人', key: 'modifier', width: 175 },
        { title: '所属组织', key: 'orgNames' },
        { title: '修改时间', key: 'modifyTime', width: 150 },
        { title: '修改记录', key: 'remark', slot: 'remark' },
      ],
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      activeIndex: 1, // 轮播图显示的图片下标
      videoVisible: false, // 视频流弹框
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      chooseMapItem: {},
    };
  },
  filters: {
    manufacturer(value, that) {
      if (!value) return '';
      for (let i of that.facturer) {
        if (value == i.dataKey) {
          return i.dataValue;
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  mounted() {
    this.init();
    this.getTagList();
    this.initList();
    this.$http.get(algorithm.dictData + 'manufacturer').then((res) => {
      this.facturer = res.data.data;
    });
  },
  methods: {
    change(index) {
      this.activeIndex = index + 1;
    },
    initList() {
      this.pageData.pageNum = 1;
      this.getDeviceRecordPageList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getDeviceRecordPageList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getDeviceRecordPageList();
    },
    init() {
      if (!this.$route.query.id && !this.$route.query.deviceId) {
        return false;
      }
      if (this.$route.query.id) {
        this.id = this.$route.query.id;
      }
      this.getDetail();
    },

    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.queryTagList = res.data.data;
        this.defaultCheckedList = this.queryTagList.map((item) => {
          return item.tagId;
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 新增标签
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      this.deviceTagsShow = false;
      if (!row.tagList || !row.tagList.length) {
        this.defaultCheckedList = [];
        return;
      }
      this.defaultCheckedList = row.tagList.map((item) => {
        return item.tagId;
      });
    },
    confirmFilter(val, isFirst) {
      !isFirst ? (this.customSearch = false) : null;
      if (this.customizeAction.title === '添加设备标签') {
        const tagIds = val.map((item) => {
          return item.tagId;
        });
        const params = {
          id: this.chooseOne.id,
          tagIds: tagIds,
        };
        this.addTagsFunc(params);
      } else {
        this.filterTagList = val;
      }
    },
    async addTagsFunc(params) {
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.init();
      } catch (err) {
        console.log(err);
      }
    },
    async getDetail() {
      let interfaceName = 'getDeviceById';
      let params = {
        id: this.id,
      };
      if (this.$route.query.deviceId) {
        interfaceName = 'viewDeviceId';
        params = {
          deviceId: this.$route.query.deviceId,
        };
      }
      try {
        let res = await this.$http.get(equipmentassets[interfaceName], {
          params: params,
        });
        const { data } = res.data;
        this.info = data;
        const { latitude, longitude, deviceColumnJson } = data;
        const { base = [], staus = [], manage = [], label = [], safe = [] } = JSON.parse(deviceColumnJson);
        let attributeMap = { base, staus, manage, label, safe };
        this.attrList.forEach((e) => {
          e.list = attributeMap[e.name] || [];
        });
        this.chooseMapItem = {
          latitude,
          longitude,
        };
        this.imgList = res.data.data.imageUrls || [];
      } catch (err) {
        console.log(err);
      }
    },
    async getDeviceRecordPageList() {
      try {
        let params = {
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        if (this.$route.query.id) params.deviceInfoId = this.id;
        if (this.$route.query.deviceId) params.deviceInfoDeviceId = this.$route.query.deviceId;
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.getDeviceRecord, params);
        this.deviceRecord = (data.entities || []).map((item) => {
          item.remark = item.remark ? JSON.parse(item.remark) : '';
          return item;
        });
        this.pageData.totalCount = data.total || 0;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 属性展开 收起
    show_detail(index) {
      const { show } = this.attrList[index];
      this.attrList[index].show = !show;
    },
    // 视频播放
    async videoPlayback() {
      try {
        this.videoVisible = true;
        const { deviceId } = this.info;
        let res = await this.$http.post(vedio.getplay, { deviceId });
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    // 停止视频播放
    onVideoPlayCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.info.deviceId);
    },
  },
  watch: {},
  props: {},
  components: {
    TagsMore: require('@/components/tags-more.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
    ArchivesCalender: require('./components/archives-calender').default,
    LineChart: require('./components/line-chart').default,
    Title: require('./components/title').default,
    MapPointLon: require('./components/map-position').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .archives-file {
    background-color: #dde1ea;
    .equipment-title {
      background: url('~@/assets/img/equipmentfiles/title-bg-light.png');
      background-size: 100% 100%;
      .title-name {
        width: 180px;
        background: transparent;
        position: relative;
        &::before {
          content: '设 备 档 案';
          font-weight: bold;
          color: #000000;
          font-size: 36px;
          position: absolute;
          left: 0;
          top: -25px;
          height: 100%;
          width: 100%;
        }
      }
    }
    .equipment-content {
      background: transparent;
      > div {
        background: #ffffff;
        box-shadow: 0px 0px 8px 0px rgba(147, 171, 206, 0.7);
        .equipment-content-header {
          background: rgba(44, 134, 248, 0.05);
        }
      }
    }
    .content-title {
      color: rgba(0, 0, 0, 0.35) !important;
    }
    .container-footer {
      background: url('~@/assets/img/equipmentfiles/tag-bg-light.png') !important;
      background-size: 100% 100% !important;
      .footer-title {
        background: #2c86f8 !important;
        text-shadow: none !important;
        background: linear-gradient(90deg, #2c86f8 0%, #dce7fc 100%) !important;
      }
    }
    .equipment-attribute {
      .attribute-list-title {
        background: #ffffff;
        box-shadow: 0px 0px 8px 0px rgba(147, 171, 206, 0.7);
        .list-text {
          text-shadow: none;
        }
        .arrowrun {
          color: #888888;
        }
        .arrow {
          color: #888888;
        }
      }
      .attribute-content {
        background: #ffffff;
        .list-item {
          border: 1px solid #d3d7de;
          .item-title {
            background-color: #f9f9f9;
            color: rgba(0, 0, 0, 0.9);
            font-weight: bold;
            border-right: 1px solid #d3d7de;
          }
          .item-content {
            color: rgba(0, 0, 0, 0.8);
            background-color: #ffffff;
          }
          .item-text-content {
            color: rgba(0, 0, 0, 0.8);
            background-color: #ffffff;
            border-right: 1px solid #d3d7de;
            border-top: 1px solid #d3d7de;
          }
        }
        .list-item:nth-child(3n) {
          border-right: 1px solid #d3d7de;
        }
        .list-item:last-child {
          border-right: 1px solid #d3d7de;
        }
      }
    }
    .equipment-attribute-other {
      background-color: #ffffff;
      .title {
        border-bottom: 0;
        background: rgba(44, 134, 248, 0.05);
        > span {
          color: rgba(0, 0, 0, 0.9);
        }
        i {
          color: var(--color-primary);
        }
      }
    }
    .empty {
      border: 1px solid var(--border-color);
    }
  }
  .btn-add {
    .icon-tree-add {
      color: var(--color-primary);
    }
  }
  .tags-more {
    .noData {
      color: var(--color-primary) !important;
    }
  }

  .content-list /deep/ .table-record {
    .ivu-table {
      td {
        background: #f9f9f9 !important;
      }
      .ivu-table-header th {
        background: #ebedf1 !important;
      }
      .ivu-table-body tr:nth-child(2n) td {
        background: #f1f1f1 !important;
      }
      .ivu-table-tip table td {
        background-color: #f9f9f9 !important;
      }
    }
  }
}
[data-theme='deepBlue'] {
  .archives-file {
    .equipment-title {
      background: url('~@/assets/img/equipmentfiles/title-bg-deepBlue.png');
      background-size: 100% 100%;
    }
    .container-footer {
      .footer-title {
        background: linear-gradient(90deg, #3D6C9B 0%, #ffffff 100%) !important;
      }
    }
    .container-center{
      .ivu-btn {
        background: linear-gradient(0deg, #113961 0%, #3F96ED 100%) !important;
      }
      .ivu-btn:hover {
        background: linear-gradient(0deg, #175cb2 0%, #65a7ee 100%) !important;
      }
    }
  }
}
.tags-more {
  display: inline-block;
}
.required-field {
  position: relative;
  &:before {
    content: '*';
    position: absolute;
    left: 15px;
    color: var(--color-failed);
  }
}
.btn-add {
  .icon-tree-add {
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    color: #56789c;
    &:hover {
      color: #146ac7;
    }
    &:active {
      color: #4e9ef2;
    }
  }
}
.archives-file {
  background-color: var(--bg-content);
  background-size: 100% 100%;
  min-height: 100%;
  .equipment-title {
    height: 66px;
    line-height: 80px;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    background: url('~@/assets/img/equipmentfiles/title-bg.png');
    background-size: cover;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 99;
    .title-name {
      height: 42px;
      width: 155px;
      background: url('~@/assets/img/equipmentfiles/title-name.png');
      background-size: cover;
    }
  }
  .equipment-content {
    height: 535px;
    width: 100%;
    background: url('~@/assets/img/equipmentfiles/equipment-bg.png');
    background-size: cover;
    display: flex;
    > div {
      width: 50%;
      height: 507px;
      background: rgba(8, 47, 106, 0.75);
      margin: 11px 18px;
      .equipment-content-header {
        height: 33px;
        background: linear-gradient(90deg, rgba(23, 76, 157, 0.99) 51%, rgba(0, 88, 179, 0) 100%);
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .equipment-content-container {
        padding: 16px 18px;
        .container-header {
          display: flex;
        }
        .container-header-left {
          height: 273px;
          width: 280px;
          margin-right: 26px;
          .equipment-image-list {
            height: 100%;
            width: 100%;
            margin-bottom: 10px;
            .equipment-image-item {
              width: 100%;
              height: 100%;
              > img {
                width: 100%;
                height: 100%;
              }
            }
            @{_deep} .el-carousel__container {
              height: 100%;
            }
            @{_deep} .el-carousel__arrow {
              width: 34px;
              height: 34px;
              background: #082e67;
              border: 1px solid #0068b7;
              opacity: 0.26;
              border-radius: 50%;
              &:hover {
                background-color: #078bff;
              }
            }
            @{_deep} .el-carousel__arrow--left {
              top: 135px;
              left: 8px;
            }
            @{_deep} .el-carousel__arrow--right {
              top: 135px;
              right: 8px;
            }
          }
          .equipment-image-count {
            position: absolute;
            bottom: 0;
            right: 0;
            height: 24px;
            line-height: 24px;
            width: 280px;
            color: #fff;
            z-index: 999;
            text-align: center;
            background: rgba(0, 0, 0, 0.2);
          }
        }
        .empty {
          background-color: var(--bg-content);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          color: var(--color-no-data-img);
          i {
            font-size: 95px;
          }
          > div {
            font-size: 14px;
            margin-top: -15px;
          }
        }
        .container-header-right {
          > div {
            height: 35px;
            line-height: 35px;
            width: 610px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            display: flex;
            // align-items: center;
            .content-title {
              font-size: 14px;
              color: #ffffff;
              font-weight: bold;
            }
            @{_deep}.ivu-tooltip {
              .ivu-tooltip-rel {
                .base-text-color {
                  display: inline-block;
                  height: 35px;
                  line-height: 35px;
                  width: 540px;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  overflow: hidden;
                  word-break: break-all;
                }
              }
              .ivu-tooltip-popper {
                top: 294px !important;
              }
            }
            .deviceId {
              color: var(--color-display-text);
              font-weight: bold;
              font-size: 18px;
            }
            .base-text-color {
              font-size: 14px;
            }
            .time {
              font-weight: bold;
            }
          }
        }
        .container-center {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 20px;
          margin-top: 10px;
          height: 18px;
          line-height: 18px;
          .container-center-left {
            vertical-align: baseline;
            .title-right-big {
              display: inline-block;
              height: 18px;
              width: 88px;
              > img {
                width: 100%;
                height: 100%;
                margin-bottom: -2px;
              }
            }
            .map-camera-active {
              display: inline-block;
              height: 23px;
              width: 18px;
              margin-right: 5px;
              > img {
                width: 100%;
                height: 100%;
                margin-bottom: -7px;
              }
            }
            .content-title {
              font-size: 14px;
              color: #ffffff;
              font-weight: bold;
              height: 18px;
              line-height: 22px;
            }
            .base-text-color {
              font-size: 14px;
              height: 18px;
              line-height: 22px;
            }
          }
          .ivu-btn {
            height: 26px;
            line-height: 26px;
            padding: 0;
            width: 88px;
            border-radius: 13px;
            background: linear-gradient(0deg, #106be0 0%, #29b4f7 100%);
            i {
              margin-right: 4px;
            }
          }
          .ivu-btn:hover {
            background: linear-gradient(0deg, #175cb2 0%, #65a7ee 100%);
          }
        }
        .container-footer {
          height: 111px;
          width: 100%;
          background: url('~@/assets/img/equipmentfiles/tag-bg.png');
          background-size: 100% 100%;
          position: relative;
          .footer-title {
            position: absolute;
            top: 1px;
            left: 1px;
            color: #ffffff;
            font-size: 14px;
            padding-left: 7px;
            width: 102px;
            height: 23px;
            font-size: 16px;
            background: #0c9af4;
            border-radius: 4px 0px 0px 0px;
            text-shadow: 0px 3px 0px rgba(10, 68, 174, 0.6);
            background: linear-gradient(90deg, #0c9af4 51%, #062a5e 100%);
          }
          @{_deep}.tag-list {
            overflow: hidden;
            height: 99px;
            overflow-y: auto;
            .tags-more {
              margin: 26px 0 18px 0;
              width: 100%;
              padding: 0 55px;
              .ivu-tag {
                margin: 9px 9px 0 9px !important;
              }
              .ivu-tooltip-rel {
                position: absolute;
                bottom: 8px;
                right: 13px;
              }
              .noData {
                font-size: 14px;
                color: #3768b1;
                width: 100%;
                text-align: center;
              }
            }
          }
          .add-tag {
            display: inline-block;
            padding: 0 5px;
            background: rgba(26, 163, 242, 0.2);
            color: #1aa3f2;
            line-height: 20px;
            cursor: pointer;
            border: 1px solid #1aa3f2;
            border-radius: 2px;
            &:hover {
              color: #fff;
              border: 1px solid #fff;
            }
          }
        }
      }
    }
    > div:first-child {
      margin-right: 0px;
    }
  }
  .equipment-attribute {
    padding: 0 18px;
    .attribute-list-title {
      height: 50px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #072e69;
      padding: 0 18px;
      margin-bottom: 4px;
      .list-text {
        font-size: 16px;
        margin-left: 11px;
        font-weight: bold;
        color: var(--color-content);
        line-height: 45px;
        text-shadow: 0px 3px 0px rgba(10, 68, 174, 0.6);
      }
      .arrowrun {
        margin-left: 5px;
        color: #93a5c6;
        font-size: 12px !important;
        transition: 0.2s;
        transform-origin: center;
        transform: rotateZ(-180deg);
      }
      .arrow {
        margin-left: 5px;
        color: #93a5c6;
        font-size: 12px !important;
        transition: 0.2s;
        transform-origin: center;
        transform: rotateZ(0deg);
      }
    }
    .attribute-content {
      padding: 18px 17px;
      width: 100%;
      background: rgba(6, 37, 83, 0.93);
      display: flex;
      flex-wrap: wrap;
      .list-item {
        display: flex;
        height: 36px;
        width: 33.33%;
        border: 1px solid #0068b7;
        .item-title {
          text-align: left;
          padding-left: 25px;
          line-height: 36px;
          width: 262px;
          background-color: #063166;
          color: #25e6fd;
          font-size: 14px;
          border-right: 1px solid #0068b7;
        }
        .item-content {
          text-align: left;
          padding-left: 21px;
          font-size: 14px;
          line-height: 36px;
          padding: 0 10px;
          color: #fff;
          width: 363px;
          background-color: #042c5c;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .item-text-content {
          width: calc(100% - 232px);
          text-align: left;
          padding-left: 21px;
          line-height: 36px;
          padding: 0 10px;
          font-size: 14px;
          color: #fff;
          background-color: #042c5c;
          border-right: 1px solid #0068b7;
          border-top: 1px solid #0068b7;
        }
      }
      .list-item:nth-child(n + 4) {
        border-top: none;
      }
      .list-item:nth-child(n + 1) {
        border-right: none;
      }
      .list-item:nth-child(3n) {
        border-right: 1px solid #1d5fc0;
      }
      .list-item:last-child {
        border-right: 1px solid #1d5fc0;
      }
    }
  }
  .equipment-attribute-other {
    margin: 18px;
    background-color: #072e69;
    .title {
      height: 50px;
      line-height: 50px;
      padding: 0 18px;
      border-bottom: 1px solid #254f94;
      > span {
        font-size: 16px;
        font-weight: bold;
        color: #ffffff;
      }
      i {
        color: #0fd0ff;
      }
    }
    .content-list {
      padding: 17px;
    }
    .test {
      width: 1465px;
      height: 145px;
      background: linear-gradient(0deg, #00ffc6 0%, #1deff7 100%);
      opacity: 0.7;
    }
  }
}
.ellipsiText {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /*行数*/
}
.chang-name {
  color: var(--color-primary);
}
@{_deep}.table-record {
  min-height: 200px !important;
  .ivu-table {
    td {
      background: #062553;
    }
    .ivu-table-header th {
      background: #0e387e !important;
    }
    .ivu-table-body tr:nth-child(2n) td {
      background: #092d66;
    }
    .ivu-table-tip table td {
      background-color: #062553 !important;
    }
    .ivu-table-tip table td {
      > span {
        display: inline-block;
        min-height: 200px;
      }
    }
  }
}
</style>
