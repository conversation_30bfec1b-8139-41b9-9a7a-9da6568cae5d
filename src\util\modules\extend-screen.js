import Vue from "vue";
/**
 * 处理视频扩展屏一些事务
 * @type {{data:....}}}
 */

let extendScreenType = "";
let urlConfig = {
  monitor: "/exscreen",
  mapDefaultPage: "/operations-on-the-map/map-default-page",
  monitorGroup: "/exmgscreen",
};

//打开扩展屏
async function _openExScreen(type, title) {
  var screenLeft = window.screenLeft,
    screenWidth = window.screen.width,
    leftPosition = screenLeft >= screenWidth ? 0 : screenWidth,
    win = null,
    isMainScreen = screenLeft < screenWidth; // true: 当前页面在主屏上 false:当前页面在扩展屏上
  var openPage = urlConfig[type] || "/#/exscreen";
  //根据屏幕信息进行发送页面动作
  let data = await new Promise((resolve) => {
    H5Player.getExpandScreenInfo((result) => {
      resolve(result.info);
    });
  });
  //当前屏幕
  let rect = {
      left: 0,
      top: 0,
      width: 1440,
      height: 900,
      mode: 1,
    },
    screens = data ? JSON.parse(data) : [],
    L = screens.length;
  //根据鼠标的位置，初始化要发送的屏定位
  //TODO [data.isMousePrimary 需要判断]
  // 判断是有没有扩展屏
  let isSingle = L <= 1;
  if (isSingle) {
    win = window.open(openPage, type || "_blank");
    win.onload = function () {
      win.document.title = title || "视频图像信息综合应用平台-扩展屏";
    };
  } else {
    win = window.open(
      openPage,
      type,
      "top=0,left=" +
        leftPosition +
        ",toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no,depended=true"
    );
    if (isMainScreen) {
      for (var j = 0; j <= L - 1; j++) {
        if (screens[j].isPrimary === 1) {
          continue;
        }
        rect.left = screens[j].curRect.left;
        rect.top = screens[j].curRect.top;
      }
    } else if (!isMainScreen) {
      for (var k = 0; k <= L - 1; k++) {
        if (screens[k].isPrimary === 1) {
          rect.left = screens[k].curRect.left;
          rect.top = screens[k].curRect.top;
        }
      }
    }
    //发送屏幕
    let sendParam = {
      title: title || "视频图像信息综合应用平台-扩展屏",
      showtype: 1,
      left: rect.left,
      top: rect.top,
    };
    //由于h5vp移动扩展屏是根据title的，所以使用定时器判断扩展屏窗口是否加载完成，若加载完成，则进行移动，否则不移动
    let timer = requestAnimationFrame(function fn() {
      if (win.pageInitFinished) {
        win.document.title = sendParam.title;
        cancelAnimationFrame(timer);
        timer = null;
        setTimeout(() => {
          //暂这样处理，加个延时，否则扩展屏不移动,原因？
          sendParam.process = "chrome.exe";
          H5Player.moveProcessWindow(sendParam).then(() => {});
          sendParam.process = "iexplore.exe";
          H5Player.moveProcessWindow(sendParam).then(() => {});
        }, 1500);
      } else {
        timer = requestAnimationFrame(fn);
      }
    });
  }
  //返回窗口对象
  window.ExtendWindow = win;
  extendScreenType = type;
  // win.onunload = () => window.ExtendWindow = null;
  return win;
}

function openScreen(type, param = {}) {
  if (
    !!(
      window.ExtendWindow &&
      window.ExtendWindow.window &&
      window.ExtendWindow.pageInitFinished &&
      !window.ExtendWindow.closed
    ) &&
    extendScreenType &&
    extendScreenType != type
  ) {
    return Vue.prototype.$Message.error("扩展屏正在被占用,请关闭扩展屏后重试");
  }
  if (
    !!(
      window.ExtendWindow &&
      window.ExtendWindow.window &&
      window.ExtendWindow.pageInitFinished &&
      !window.ExtendWindow.closed
    )
  ) {
    window.ExtendWindow.postMessage(JSON.stringify(param), "*");
  } else {
    _openExScreen(type);
    extendScreenType = type;
    var tryCount = 0,
      timer;
    timer = setInterval(function () {
      tryCount++;
      console.log("......wait ExtendWindow pageInitFinished");
      if (
        !!(
          window.ExtendWindow &&
          window.ExtendWindow.window &&
          window.ExtendWindow.pageInitFinished
        )
      ) {
        clearInterval(timer);
        window.ExtendWindow.postMessage(JSON.stringify(param), "*");
      }
      if (tryCount > 100) {
        clearInterval(timer);
      }
    }, 300);
  }
}
/**
 * 发送到扩展屏之前的环境验证
 * 根据当前扩展屏正在处理的事情，进行必要规避和拦截【轮巡大于播放】
 */
function checkExtendScreen() {
  if (
    window.ExtendWindow &&
    window.ExtendWindow.window &&
    !window.ExtendWindow.closed
  ) {
    //当前扩展屏正在处理相关业务
    if (extendScreenType === "monitor") {
      //当前正在进行视频轮巡任务，拦截其他任务
      Vue.prototype.$Message.error(
        "当前扩展屏正在进行视频轮巡，请关闭扩展屏页面重试"
      );
      return false;
    }
    if (extendScreenType === "mapDefaultPage") {
      //当前正在进行时空分析任务，拦截其他任务
      Vue.prototype.$Message.error(
        "当前扩展屏正在进行时空分析，请关闭扩展屏页面重试"
      );
      return false;
    }
    if (extendScreenType === "monitorGroup") {
      //当前正在进行监巡分组任务，拦截其他任务
      Vue.prototype.$Message.error(
        "当前扩展屏正在播放监巡分组，请关闭扩展屏页面重试"
      );
      return false;
    }
  }
  return true;
}

function exMonitorScreen(deviceList) {
  if (!checkExtendScreen()) return;
  openScreen("monitor", deviceList);
}

function exMapDefaultPage(deviceInfo) {
  openScreen("mapDefaultPage", deviceInfo);
}

function exMonitorGroupScreen(params, callback) {
  if (!checkExtendScreen()) return;
  callback && callback();
  openScreen("monitorGroup", params);
}

export default {
  exMonitorScreen: exMonitorScreen, //图上作战框选设备轮巡
  exMapDefaultPage: exMapDefaultPage, //视频播放跳转到时空分析
  exMonitorGroupScreen: exMonitorGroupScreen, //监巡分组
};
