<template>
  <div>
    <ui-modal v-model="visible" :styles="styles" title="修改密码">
      <Form ref="passwordForm" :model="passwordForm" :label-width="100">
        <FormItem
          label="旧密码"
          prop="oldPassword"
          :rules="[{ validator: validatorOldPassword, trigger: 'blur', required: true }]"
        >
          <Input
            type="password"
            password
            v-model="passwordForm.oldPassword"
            placeholder="请输入旧密码"
            class="width-sm custom-icon"
          ></Input>
        </FormItem>
        <FormItem
          label="新密码"
          prop="newPassword"
          :rules="[{ validator: validatorNewPassword, trigger: 'blur', required: true }]"
        >
          <Input
            type="password"
            password
            placeholder="请输入新密码"
            v-model="passwordForm.newPassword"
            class="width-sm custom-icon"
          >
          </Input>
        </FormItem>
        <FormItem
          label="确认新密码"
          prop="surePassword"
          :rules="[{ validator: validatorSurePassword, trigger: 'blur', required: true }]"
        >
          <Input
            type="password"
            password
            placeholder="请输入确认新密码"
            v-model="passwordForm.surePassword"
            class="width-sm custom-icon"
          >
          </Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button class="mdf-button plr-30" @click="cancel">取 消</Button>
        <Button type="primary" :loading="saveModalLoading" class="plr-30" @click="save('passwordForm')">保 存</Button>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import user from '@/config/api/user';
import md5 from 'md5';
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      styles: {
        width: '2.8rem',
      },
      visible: false,
      saveModalLoading: false, //保存密码按钮loading
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        surePassword: '',
      },
      validatorOldPassword: (rule, value, callback) => {
        // let reg = /^[A-Za-z0-9]{6,16}$/
        if (!value) {
          callback(new Error('请输入旧密码'));
        } else {
          callback();
        }
      },
      validatorNewPassword: (rule, value, callback) => {
        let reg = /^(?=[a-zA-Z])(?=.*\d)[a-zA-Z\d@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,20}$/;
        if (!value) {
          callback(new Error('请输入新密码'));
        } else if (!reg.test(value)) {
          callback(new Error('请输入大写或小写字母开头包含数字的8-20位密码'));
        } else if (value === this.passwordForm.oldPassword) {
          callback(new Error('新密码不要等于老密码'));
        } else {
          callback();
        }
      },
      validatorSurePassword: (rule, value, callback) => {
        let reg = /^(?=[a-zA-Z])(?=.*\d)[a-zA-Z\d@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,20}$/;
        if (!value) {
          callback(new Error('请输入确认新密码'));
        } else if (!reg.test(value)) {
          callback(new Error('请输入大写或小写字母开头包含数字的8-20位密码'));
        } else if (value !== this.passwordForm.newPassword) {
          callback(new Error('两次输入密码不一致'));
        } else {
          callback();
        }
      },
    };
  },
  methods: {
    ...mapActions({
      removeAreaList: 'common/removeAreaList',
    }),
    async save(name) {
      let validate = await this.$refs[name].validate();
      if (!validate) return;
      let passwordForm = {
        newPsw: md5(this.passwordForm.newPassword),
        oldPsw: md5(this.passwordForm.oldPassword),
        confirmPsw: md5(this.passwordForm.surePassword),
      };
      this.saveModalLoading = true;
      this.$http
        .put(user.updatePersonalPsw, passwordForm)
        .then((res) => {
          this.$Message.success(res.data.msg);
          this.visible = false;
          //修改完密码之后重新登陆
          window.sessionStorage.clear();
          this.removeAreaList();
          this.$router.push({ name: 'login' });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.saveModalLoading = false;
        });
    },
    cancel() {
      this.visible = false;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      this.$refs.passwordForm.resetFields();
    },
  },
  computed: {},
  props: {
    value: {},
  },
  components: {},
};
</script>
<style lang="less" scoped>
.width-sm {
  width: 100%;
}

@{_deep} .ivu-modal-body {
  padding: 50px 50px 26px 50px;
}
@{_deep} .ivu-modal-header {
  padding: 0;
}
.custom-icon {
  @{_deep}.ivu-input-suffix {
    .ivu-icon-ios-eye-off-outline:before {
      font-family: icon-font;
      content: '\e793';
      color: #56789c;
      font-size: 12px;
    }
    .ivu-icon-ios-eye-outline:before {
      font-family: icon-font;
      content: '\e792';
      font-size: 15px;
      color: #56789c;
    }
  }
  @{_deep}.ivu-icon-ios-close-circle:before {
    font-family: icon-font;
    color: #56789c;
    content: '\e791';
    font-size: 15px;
  }
}
/deep/ .ivu-form-item-error-tip {
  font-size: 12px;
}
</style>
