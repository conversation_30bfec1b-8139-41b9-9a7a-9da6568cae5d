<template>
  <ui-modal v-model="visible" :styles="styles" :title="modalAction.title" @query="query">
    <Form class="form" ref="form" autocomplete="off" label-position="right" :model="formData" :rules="formRules">
      <FormItem class="form-item" label="人员证件照" required prop="url">
        <upload-img :multiple-num="1" :default-list="defaultList" @successPut="successPut" ref="upload"></upload-img>
      </FormItem>
      <FormItem class="form-item" label="人员姓名" prop="name">
        <Input class="width-lg" v-model="formData.name" placeholder="请填写人员姓名"></Input>
      </FormItem>
      <FormItem class="form-item" label="证件号" required prop="idCard">
        <Input class="width-lg" v-model="formData.idCard" placeholder="请填写证件号"></Input>
      </FormItem>
      <FormItem class="form-item" label="所属地" required prop="civilCode">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    modalAction: {},
    save: {},
    modalData: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      selectTree: {
        regionCode: '',
      },
      defaultList: [],
      formData: {
        id: '',
        url: '',
        name: '',
        idCard: '',
        civilCode: '',
        type: 1,
      },
      formRules: {
        url: [
          {
            required: true,
            message: '请上传人员证件照',
          },
        ],
        idCard: [
          {
            required: true,
            type: 'string',
            max: 20,
            message: '请填写正确的证件号',
          },
        ],
        civilCode: [
          {
            required: true,
            message: '请选择行政区划',
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    successPut(list) {
      this.formData.url = list[0];
    },
    selectedArea(area) {
      this.formData.civilCode = area.regionCode;
    },
    query() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const res = await this.save(this.formData);
          this.$Message.success(res.data.msg);
          this.visible = false;
          this.$emit('update');
        } else {
          this.$Message.error('请填写必须要填写的信息');
        }
      });
    },
    resetFile() {
      this.selectTree.regionCode = '';
      this.defaultList = [];
      this.$refs.upload.clear();
      this.formData = {
        id: '',
        url: '',
        name: '',
        idCard: '',
        civilCode: '',
        type: 1,
      };
    },
  },
  watch: {
    value(val) {
      if (this.isAdd) {
        this.resetFile();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    modalData(val) {
      let length = Object.keys(val).length;
      if (length > 0) {
        this.resetFile();
        Object.keys(val).forEach((key) => {
          if (this.formData.hasOwnProperty(key)) {
            switch (key) {
              case 'civilCode':
                this.selectTree.regionCode = val[key];
                this.formData[key] = val[key] || '';
                break;
              case 'url':
                this.defaultList.push(val[key]);
                this.formData[key] = val[key] || '';
                break;
              default:
                this.formData[key] = val[key] || '';
                break;
            }
          }
        });
      }
    },
  },
  computed: {
    isAdd() {
      return this.modalAction.action === 'add';
    },
  },
  components: {
    UploadImg: (resolve) => {
      require(['@/components/upload-img'], resolve);
    },
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.upload-img {
  justify-content: start !important;
}
@leftMargin: 100px;
.form-item {
  @{_deep} .ivu-form-item-error-tip {
    margin-left: @leftMargin;
  }
  @{_deep}.ivu-form-item-label {
    width: @leftMargin;
  }
}
</style>
