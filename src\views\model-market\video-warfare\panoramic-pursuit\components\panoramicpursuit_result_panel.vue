<template>
  <fullModal
    class="fullModalBox"
    title="全景追逃"
    :footer-hide="true"
    @on-cancel="closeFn"
    ref="dialog"
  >
    <template v-if="isShow">
      <div class="libBox skill_body">
        <div class="skills_content_wrap">
          <div class="skills_left">
            <div class="skills_left_title">
              <span class="lable">搜索范围:</span>
              <Input class="input" v-model="searchRange" clearable></Input>
              <Button
                class="button"
                size="small"
                type="primary"
                @click="searchCamera"
                >确定</Button
              >
            </div>
            <span class="label-title" style="">附近摄像机资源:</span>
            <div class="skills_left_tree_container">
              <div v-scroll class="skills_left_tree">
                <div class="tree-container">
                  <!-- 数据需要服务器端重构一下-->
                  <div v-for="(item, index) in allCamerasData" :key="index">
                    <div class="item-title" @click="displayDetailsList(item)">
                      <i
                        :class="[
                          !item.isShowCameraList
                            ? 'icon-tianjia'
                            : 'icon-shanchu2',
                        ]"
                        class="iconfont"
                      ></i>
                      <!--<span class="title">{{item.name}}[{{item.isOnlineNum}}/{{item.totalNum}}]</span>-->
                      <span class="title"
                        >{{ item.name }}[{{ item.data.length }}]</span
                      >
                    </div>
                    <ul
                      class="camera-item-list-ul"
                      v-show="item.isShowCameraList && item.data.length > 0"
                    >
                      <li
                        class="camera-item-list-li"
                        v-for="(cameraItem, index) in item.data"
                        :key="index"
                      >
                        <i
                          class="iconfont"
                          :class="[
                            cameraItem.isFocus
                              ? 'icon-focus'
                              : computedDeviceIcon(cameraItem),
                            cameraItem.isOnline == '1' ? 'offline' : '',
                          ]"
                        ></i>
                        <div
                          class="camera-item-name"
                          :class="[cameraItem.isFocus ? 'focus' : '']"
                          :title="cameraItem.deviceName"
                        >
                          {{ cameraItem.deviceName }}
                        </div>
                        <div
                          class="camera-item-meter"
                          :title="cameraItem.meter"
                        >
                          {{ cameraItem.meter }}米
                        </div>
                        <i
                          class="iconfont icon-yunhang"
                          title="播放"
                          @click="playCamera(cameraItem, item.position)"
                        ></i>
                      </li>
                    </ul>
                    <div
                      class="no-camera"
                      v-show="item.isShowCameraList && item.data.length === 0"
                    >
                      暂无摄像机
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="operate-bar">
                                <Button class="trackBtn" type="primary" @click="saveTrack">保存轨迹追踪</Button>
                                <Button class="trackBtn" type="primary" @click="viewTrack">查看轨迹追踪</Button>
                            </div>  -->
            </div>
            <div
              ref="panoramicpursuit-map"
              id="panoramicpursuit-map"
              class="panoramicpursuit-map"
            ></div>
          </div>
          <div class="skills_content_right">
            <div id="FULL_VIEW_UIOCX_WRAPER" style="width: 100%; height: 100%">
              <h5-player
                sceneFrom="panoramicPursuit"
                :options="{ layout: '3*3' }"
                ref="panoramicPursuit"
                @successEvents="successEvents"
              ></h5-player>
            </div>
          </div>
        </div>
      </div>
    </template>
  </fullModal>
</template>
<script>
let _mapGeometery = null;

import sendTrack from "./sendTrack.js";
import { mapGetters } from "vuex";
import fullModal from "@/components/full-modal/index.vue";
import { getGeometryCamera } from "@/api/modelMarket.js";
import { NPGisMapMain } from "@/map/map.main";
let _buffStyle = {
  color: "#1976ED",
  fillColor: "#1976ED", //填充颜色
  opacity: 0.1,
  weight: 1,
  fillOpacity: 0.25, //填充的透明度，取值范围0 - 1
};
export default {
  name: "panoramicpursuitResultPanel",
  components: {
    fullModal,
  },
  props: {
    centerMarkerData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      cameraInfo: {},
      bufferGeometry: {},
      allCamerasData: [
        {
          name: "正北",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 1,
          isCenterCamera: false,
        },
        {
          name: "东北",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 2,
          isCenterCamera: false,
        },
        {
          name: "正东",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 5,
          isCenterCamera: false,
        },
        {
          name: "东南",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 8,
          isCenterCamera: false,
        },
        {
          name: "正南",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 7,
          isCenterCamera: false,
        },
        {
          name: "西南",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 6,
          isCenterCamera: false,
        },
        {
          name: "正西",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 3,
          isCenterCamera: false,
        },
        {
          name: "西北",
          data: [],
          isOnlineNum: 0,
          totalNum: 0,
          position: 0,
          isCenterCamera: false,
        },
      ],
      searchRange: 500,
      isShow: false,
    };
  },
  watch: {
    centerMarkerData(val) {
      this.$nextTick(() => {
        if (!window.panoramicpursuitMap) {
          this.createMap().then(() => {
            _mapGeometery = new MapPlatForm.Base.MapGeometry(
              window.panoramicpursuitMap
            );
            this.init(val);
          });
        } else {
          this.init(val);
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
    }),
  },
  mounted() {},
  methods: {
    open() {
      this.$refs.dialog.open();
      this.isShow = true;
    },
    //初始化地图
    createMap() {
      return new Promise((resolve) => {
        //创建地图
        this.$nextTick(() => {
          var mapJson = new MapPlatForm.Base.MapConfig().createMap(
            this.$refs["panoramicpursuit-map"],
            this.mapConfig
          );
          window.panoramicpursuitMapJSON = mapJson;
          window.panoramicpursuitMap = mapJson.map;
          this.trackLayer = new NPMap.Layers.OverlayLayer("trackLayer");
          window.panoramicpursuitMap.addLayer(this.trackLayer);
          let zoom = this.$parent.$refs.mapBase.getZoom();
          // 默认选择父地图层级 如果父地图层级小于16 选择16
          if (zoom < 14) {
            zoom = 14;
          }
          window.panoramicpursuitMap.setZoom(zoom);
          resolve();
        });
      });
    },
    computedDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    //以摄像机为中心点，按范围进行搜索
    searchCamera() {
      var re = /^[0-9]+$/;
      if (!re.test(this.searchRange) || this.searchRange > 1000) {
        this.$Message.warning("搜索范围请输入0至1000内的整数");
        return;
      }
      var centerCamera = this.curCenterCamera.deviceId
        ? this.curCenterCamera
        : this.centerMarkerData.data;
      this.getAllCamerasData(centerCamera, "search");
      this.changeBuff(this.searchRange);
    },
    /**
     * 获取中心点周围八个方位的摄像机
     * @param {*} centerMarkerData 中心点
     */
    init(centerMarkerData) {
      this.isFirstCenterVideo = true; //是否是第一个中心点即是否是追踪轨迹起点
      let centerCamera = centerMarkerData.data;
      this.cacheCenterVideos = []; //缓存中心点信息
      this.setCenterVideo(centerCamera); //第二个参数为true，代表默认第一次渲染中心点视频
    },
    /**
     * 关闭窗体
     */
    closeFn() {
      if (this.$refs.panoramicPursuit) {
        this.$refs.panoramicPursuit.closeAll();
      }
      this.searchRange = 500;
      window.panoramicpursuitMap.destroyMap();
      window.panoramicpursuitMap = null;
      this.isShow = false;
      this.$emit("close", true);
      this.groupId = void 0;
    },
    /**
     *
     */
    setCenterVideo(currentCamera, callback) {
      this.curCenterCamera = currentCamera;
      this.getAllCamerasData(this.curCenterCamera);
    },
    /**
     * 播放摄像机
     * @param {Object} cameraItem 播放信息
     * @param {Number} position 播放位置
     */
    playCamera(cameraItem, position, from) {
      if (position !== 4) {
        this.allCamerasData.forEach((item) => {
          if (item.position === position) {
            item.data.forEach((camera) => {
              this.$set(camera, "isFocus", false); //现将该方位上的摄像机播放状态还原
            });
          }
        });
      }
      this.$refs.panoramicPursuit.playStream(cameraItem, "live", position);
      if (position === 4) {
        let centerVideo = {
          deviceId: cameraItem.deviceId,
          longitude: cameraItem.longitude,
          latitude: cameraItem.latitude,
          deviceName: cameraItem.deviceName,
          time: Date.now(),
        };
        if (from !== "search") {
          this.cacheCenterVideos.push(centerVideo);
        }
        window.videoTrackWindow &&
          !window.videoTrackWindow.closed &&
          sendTrack.openTrackWindow({
            isAppend: this.isFirstCenterVideo ? false : true,
            data: [centerVideo],
          });
        this.cameraInfo = cameraItem;
      } else {
        this.$set(cameraItem, "isFocus", true); //将当前播放的摄像机状态置为正在播放中
      }
    },
    /**
     * 查询8个方位的摄像机
     * <AUTHOR>
     * @param  {[type]} center[中心点marker]
     */
    getAllCamerasData(center, from) {
      var positionObj = {
        leftUp: 112.5,
        up: 67.5,
        rightUp: 22.5,
        left: 157.5,
        center: 4,
        right: -22.5,
        leftDown: 202.5,
        down: 247.5,
        rightDown: 292.5,
      };
      var upParam, points;
      var ajaxDataArr = [];
      for (var key in positionObj) {
        if (key === "center") {
          continue;
        }

        if (positionObj.hasOwnProperty(key)) {
          upParam = positionObj[key];
          //获取扇形区域点串
          points = this.getPoints(
            [center.longitude, center.latitude],
            this.searchRange,
            upParam
          );
          ajaxDataArr.push({
            geometry: points,
            key: key,
          });
        }
      }

      this._getPositionCameras({ ajaxDataArr }, center, from);
    },
    /**
     * 根据半径、起始角度计算扇形缓冲区
     **/
    getPoints(center, radius, angle) {
      var point = new NPMap.Geometry.Point(center[0], center[1]);
      var circleSector = _mapGeometery.createCircleSector(
        point,
        radius,
        45,
        angle
      );
      return _mapGeometery.getGGeoJsonByGeometry(circleSector);
    },
    /**
     * 将点位数组转换成geoJSON格式
     * @param pointarr - 点位数组对象
     * @param type - 数据类型
     * @returns {*}
     */
    convertArrayToGeoJson(pointarr, type) {
      if (
        !pointarr ||
        pointarr === null ||
        pointarr === "" ||
        pointarr === "undefined"
      ) {
        return;
      }
      var resultarr = [];
      var arr = [];
      for (var i = 0, j = pointarr.length; i < j; i++) {
        var point = [];
        point.push(pointarr[i].lon ? pointarr[i].lon : pointarr[i][0]);
        point.push(pointarr[i].lat ? pointarr[i].lat : pointarr[i][1]);
        arr.push(point);
      }
      resultarr.push(arr);
      if (type === "LineString") {
        //对于非闭合类型来说，缓冲区坐标少包一层
        resultarr = resultarr[0];
      }
      var result = {
        type: type,
        coordinates: resultarr,
      };
      return JSON.stringify(result);
    },
    setPositionCameras(cameras, centerPointObj) {
      var centerPoint = new NPMap.Geometry.Point(
        centerPointObj.longitude,
        centerPointObj.latitude
      );
      if (cameras) {
        for (var key in cameras) {
          var dirCameras = cameras[key];
          if (dirCameras && dirCameras.length > 0) {
            for (var i = 0; i < dirCameras.length; i++) {
              var item = dirCameras[i];
              var point = new NPMap.Geometry.Point(
                item.longitude,
                item.latitude
              );
              var meter = Math.round(
                window.panoramicpursuitMap.getDistance(centerPoint, point)
              );
              dirCameras[i].meter = meter;
            }
            dirCameras.sort(function (a, b) {
              return a.meter - b.meter;
            });
          }
          cameras[key] = dirCameras;
        }
      }
      return cameras;
    },
    /**
     * 获取8个方位的摄像机
     * <AUTHOR>
     * @param  {[type]}
     */
    _getPositionCameras(obj, centerPoint, from) {
      var self = this;
      getGeometryCamera(obj.ajaxDataArr).then((res) => {
        var data = self.setPositionCameras(res.data, centerPoint),
          upData = this._emptyCamera(data.up),
          rightUpData = this._emptyCamera(data.rightUp),
          rightData = this._emptyCamera(data.right),
          rightDownData = this._emptyCamera(data.rightDown),
          downData = this._emptyCamera(data.down),
          leftDownData = this._emptyCamera(data.leftDown),
          leftData = this._emptyCamera(data.left),
          leftUpData = this._emptyCamera(data.leftUp);
        /*1. 渲染左上侧，树信息*/
        self.allCamerasData = [
          {
            name: "正北",
            data: upData,
            totalNum: upData.length,
            position: 1,
            isCenterCamera: false,
            isShowCameraList: upData.length === 0 ? false : true,
          },
          {
            name: "东北",
            data: rightUpData,
            totalNum: rightUpData.length,
            position: 2,
            isCenterCamera: false,
            isShowCameraList: rightUpData.length === 0 ? false : true,
          },
          {
            name: "正东",
            data: rightData,
            totalNum: rightData.length,
            position: 5,
            isCenterCamera: false,
            isShowCameraList: rightData.length === 0 ? false : true,
          },
          {
            name: "东南",
            data: rightDownData,
            totalNum: rightDownData.length,
            position: 8,
            isCenterCamera: false,
            isShowCameraList: rightDownData.length === 0 ? false : true,
          },
          {
            name: "正南",
            data: downData,
            totalNum: downData.length,
            position: 7,
            isCenterCamera: false,
            isShowCameraList: downData.length === 0 ? false : true,
          },
          {
            name: "西南",
            data: leftDownData,
            totalNum: leftDownData.length,
            position: 6,
            isCenterCamera: false,
            isShowCameraList: leftDownData.length === 0 ? false : true,
          },
          {
            name: "正西",
            data: leftData,
            totalNum: leftData.length,
            position: 3,
            isCenterCamera: false,
            isShowCameraList: leftData.length === 0 ? false : true,
          },
          {
            name: "西北",
            data: leftUpData,
            totalNum: leftUpData.length,
            position: 0,
            isCenterCamera: false,
            isShowCameraList: leftUpData.length === 0 ? false : true,
          },
        ];
        /*2. 渲染左下侧，地图数据*/
        var markersArray = [];
        markersArray = self.allCamerasData.concat([
          {
            name: "中心",
            totalNum: 0,
            data: [this.curCenterCamera],
            position: 4,
            isCenterCamera: true,
          },
        ]);
        window.panoramicpursuitMap.clearOverlays();
        self.addMarkersOnMap(markersArray);
        /*3. 播放录像*/
        this.$refs.panoramicPursuit && this.$refs.panoramicPursuit.closeAll();

        self.allCamerasData.forEach(function (item, index) {
          if (!!item.data[0]) {
            self.playCamera(item.data[0], item.position); //播放八个方位的录像
          }
        });
        this.playCamera(this.curCenterCamera, 4, from); //播放中心点录像
      });
    },
    _emptyCamera(cameraList) {
      var centerCameraId = this.curCenterCamera.deviceId
        ? this.curCenterCamera.deviceId
        : this.centerMarkerData.data.deviceId;
      if (cameraList && cameraList.length) {
        return cameraList.filter((currentCamera, index) => {
          return (
            currentCamera.deviceId &&
            currentCamera.longitude &&
            currentCamera.latitude &&
            centerCameraId !== currentCamera.deviceId
          ); //过滤没有经纬度的摄像机及中心点
        });
      } else {
        return [];
      }
    },
    /**
     * 向小地图进行撒点
     * @param {*} data
     */
    addMarkersOnMap(data) {
      var defaultLayer = window.panoramicpursuitMap.getDefaultLayer();
      defaultLayer.removeAllOverlays();
      var iconNormal, iconHover, isSetCenter, centerIndex;
      const PANORAMICPURSUIT_CENTER = {
        url: require("@/assets/img/map/mapPoint/panoramicpursuit-center2.png"),
        size: {
          width: 32,
          height: 32,
        },
        markerType: 0,
      };
      const PANORAMICPURSUIT_MARKER = {
        url: require("@/assets/img/map/mapPoint/panoramicpursuit-marker.png"),
        size: {
          width: 12,
          height: 12,
        },
        markerType: 0,
      };

      var formatMarkerDatas = [];
      data.forEach(function (value, key) {
        if (value.position === 4) {
          iconNormal = PANORAMICPURSUIT_CENTER;
          isSetCenter = true;
          centerIndex = key;
        } else {
          iconNormal = PANORAMICPURSUIT_MARKER;
          isSetCenter = false;
        }
        value.data.forEach(function (item, index) {
          var tempObj = {
            marker: {
              latitude: item.latitude,
              longitude: item.longitude,
              iconNormal: iconNormal,
              isSetCenter: isSetCenter,
            },
          };
          formatMarkerDatas.push(tempObj);
        });
      });
      var _geometry = new MapPlatForm.Base.MapGeometry(
        window.panoramicpursuitMap
      );
      var markers = [];
      formatMarkerDatas.forEach((item, index) => {
        if (item.marker.iconNormal === PANORAMICPURSUIT_CENTER) {
          this.bufferGeometry = new NPMap.Geometry.Point(
            item.marker.longitude,
            item.marker.latitude
          );
        }
        var marker = _geometry.createMarker(
          new NPMap.Geometry.Point(item.marker.longitude, item.marker.latitude),
          item.marker.iconNormal
        );
        markers.push(marker);
        if (item.marker.isSetCenter === true) {
          window.panoramicpursuitMap.setCenter(marker.getPosition());
        }
      });
      window.panoramicpursuitMap.addOverlays(markers);
      this.addBuff(this.bufferGeometry);
    },
    displayDetailsList(item, event) {
      if (item.isShowCameraList) {
        this.$set(item, "isShowCameraList", false);
      } else {
        this.$set(item, "isShowCameraList", true);
      }
    },
    //设置中心画面
    successEvents(param) {
      this.isFirstCenterVideo = false;
      this.setCenterVideo(param.cameraInfo); //第二个参数为false，代表重新渲染中心点视频
    },
    //查看轨迹
    viewTrack() {
      sendTrack.openTrackWindow({
        isAppend: false,
        data: this.cacheCenterVideos,
      }); //isAppend表示是否在之前的基础上追加
      !window.videoTrackWindow.closed && $tip("已发送至轨迹页面", "success");
    },
    //保存轨迹
    saveTrack() {
      let param = [];
      this.cacheCenterVideos.forEach((item) => {
        param.push({
          resourceId: item.deviceId,
          resourceName: item.deviceName,
          groupId: this.groupId,
          time: item.time,
        });
      });
      saveTrack(param).then((res) => {
        $tip("保存成功", "success");
        this.groupId = res;
      });
    },
    //添加缓冲区
    addBuff(geometry) {
      // this.$emit("drawedLine", [geometry, type]);
      // _geometry = geometry;
      // if (_buffPolygon) {
      //   _routeGroup && _routeGroup.removeOverlay(_buffPolygon);
      // }
      let _defualtLayer = window.panoramicpursuitMap.getDefaultLayer();
      _defualtLayer.addGroup("route");
      let _routeGroup = _defualtLayer.getGroupByName("route");
      let mapService = new MapPlatForm.Base.MapService(
        window.panoramicpursuitMap
      );
      mapService.getGeometryBuffer(geometry, this.searchRange, function (result) {
        let _buffPolygon = new NPMap.Geometry.Polygon(result.rings);
        _buffPolygon.setStyle(_buffStyle);
        _routeGroup.addOverlay(_buffPolygon);
      });
    },
    //修改缓冲区
    changeBuff(buffDis) {
      let _defualtLayer = window.panoramicpursuitMap.getDefaultLayer();
      let _routeGroup = _defualtLayer.getGroupByName("route");
      let _buffPolygon = _routeGroup
        .getAllOverlayers()
        .find((v) => v.CLASS_NAME === "NPMap.Geometry.Polygon");
        _routeGroup.removeOverlay(_buffPolygon);
      // let mapService = new MapPlatForm.Base.MapService(
      //   window.panoramicpursuitMap
      // );
      // mapService.getGeometryBuffer(
      //   this.bufferGeometry,
      //   buffDis,
      //   function (result) {
      //     debugger
      //     if (_buffPolygon) {
      //       _routeGroup.removeOverlay(_buffPolygon);
      //     }
      //     _buffPolygon = new NPMap.Geometry.Polygon(result.rings);
      //     _buffPolygon.setStyle(_buffStyle);
      //     _routeGroup.addOverlay(_buffPolygon);
      //   }
      // );
    },
  },
  beforeDestroy() {
    if (window.panoramicpursuitMap) {
      window.panoramicpursuitMap.destroyMap();
      window.panoramicpursuitMap = null;
    }
  },
};
</script>
<style lang="less" scoped>
@import "./panoramicpursuit_result_panel.less";
</style>
