<template>
  <div class="no-helmet">
    <div class="no-helmet-top">
      <div class="left">
        <RadioGroup v-model="current" type="button" @on-change="pageRadioChange">
            <Radio v-for="(item, index) in pageList" :key="index" :label="item.key" >{{ item.value }}</Radio>
        </RadioGroup>
      </div>
      <div class="search-bar">
        <searchForm v-if="current == 2" @search="searchHandle"></searchForm>
      </div>
    </div>
    <div class="no-helmet-content">
      <frequencyCom ref="frequencyCom" v-if="current == 1" :illegalType="32"></frequencyCom>
      <listCom v-if="current == 2" ref="listCom" :illegalType="32"></listCom>
    </div>
  </div>
</template>
<script>
import frequencyCom from './frequency-com/index.vue'
import listCom from './list-com/index.vue'
import searchForm from './search-form.vue'

export default {
  components: { frequencyCom, listCom, searchForm },
  props: {
      taskParams: {
          type: Object,
          default: () => ({})
      }
  },
  data() {
    return {
      current: 1,
      pageList: [
        {key: 1, value: '高频违法分析'},
        {key: 2, value: '违法记录查询'}
      ],
    };
  },
  created() {
    // 推荐中心查看
    console.log('taskParams: ', this.taskParams)
    if (!Toolkits.isEmptyObject(this.taskParams)) {
      this.current = 1
      this.$nextTick(() => {
        if (this.taskParams.queryStartTime) this.$refs.frequencyCom.$refs.searchBar.queryParams.startDate = this.taskParams.queryStartTime
        if (this.taskParams.queryEndTime) this.$refs.frequencyCom.$refs.searchBar.queryParams.endDate = this.taskParams.queryEndTime
        if (this.taskParams.params) this.$refs.frequencyCom.$refs.searchBar.queryParams = {...this.$refs.frequencyCom.$refs.searchBar.queryParams, ...this.taskParams.params}
        if (this.taskParams.taskResult) this.$refs.frequencyCom.$refs.searchBar.handleSearch()
      })
    } else {
      this.current = 1
    }
  },
  methods: {
    pageRadioChange(label) {
      console.log(label)
    },
    searchHandle(searchForm) {
      this.$refs.listCom.searchHandle(searchForm)
    },
  }
}
</script>
<style lang="less" scoped>
.no-helmet {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  .no-helmet-top {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #D3D7DE;
    margin-bottom: 15px;
    height: 50px;
  }
  .no-helmet-content {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
}
</style>