<template>
  <div class="basis auto-fill height-full">
    <div class="search-module">
      <div class="mb-lg">
        <ui-label class="inline" label="关键词" :width="55">
          <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入姓名或证件号"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="重点人员类型" :width="90">
          <Select class="width-md" placeholder="请选择重点人员类型" clearable v-model="searchData.personType">
            <Option v-for="(item, index) in importantTypes" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
        </ui-label>
        <!-- <ui-label class="inline ml-lg" label="数据来源" :width="55">
          <Select
            class="width-md"
            placeholder="请选择数据来源"
            clearable
            v-model="searchData.generateStatus"
          >
            <Option
              v-for="(item, index) in dataStatuList"
              :key="index"
              :value="item.dataKey"
              >{{ item.dataValue }}</Option
            >
          </Select>
        </ui-label> -->
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </div>
      </div>
      <ui-select-tabs :list="errorList" @selectInfo="selectInfo" ref="uiSelectTabs"></ui-select-tabs>
    </div>

    <div class="statistic">
      <p class="statistic-title">
        {{ statistic.title }}
      </p>
      <p class="statistic-total">
        {{ statistic.totalNum | formatNum }}
      </p>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        ref="tableRef"
        row-key="id"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :stripe="isRepeatRecord ? false : true"
      >
        <template #identityPhoto="{ row }">
          <div class="keyPhoto">
            <img :src="row.identityPhoto" alt="" />
          </div>
        </template>
        <template #gender="{ row }">
          <span>{{ !row.gender ? '未知' : row.gender === '1' ? '男' : '女' }}</span>
        </template>
        <template #cardType="{ row }">
          <span>{{ !row.cardType ? '其他' : row.gender === '1' ? '身份证' : '护照' }}</span>
        </template>
        <template #personType="{ row }">
          <span>{{ keyPersonnelObj[row.personType] }}人员</span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            icon="icon-chakanyichangxiangqing"
            content="不合格原因"
            @click.native="viewRecord(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <device-detail
      v-model="deviceDetailShow"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :unqualified="deviceUnqualified"
      @update="search"
    >
    </device-detail>
    <div class="repeat-wrapper" v-if="isRepeatRecord && !!this.checkRepeatGroup.length">
      <p class="mb-lg">
        是否确认这<span class="f-16 font-red"> {{ this.checkRepeatGroup.length }}条 </span>数据为主数据
      </p>
      <p class="mb-xs">1.确认后将屏蔽被排除的其他数据。</p>
      <p class="mb-lg">2.确认后主数据将转入待检测数据库进行待检测。</p>
      <div class="fr">
        <Button class="mr-xs plr-30" type="primary" @click="handleRepeatDeviceInfo">确 认</Button>
        <Button @click="checkRepeatGroup = []" class="plr-30">取 消</Button>
      </div>
    </div>
    <unqualified-reason ref="UnqualifiedReason"></unqualified-reason>
    <!-- <view-detection-field
      v-model="recordShow"
      :view-data="recordData"
      :need-option="false"
    ></view-detection-field> -->
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import user from '@/config/api/user';
export default {
  props: {},
  data() {
    return {
      loading: false,
      errorList: [],
      statistic: {
        title: '重点人员基础数据',
        totalNum: '41,626,923,145',
      },
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        keyWord: '',
        personType: '',
        reasons: [],
        checkStatus: '3', // 0待检测 1 检测中 2合格 3不合格 4无效
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      minusTable: 303,
      tableData: [],
      initialTableColumns: [],
      tableColumns: [
        { title: '序号', width: 80, type: 'index', align: 'center' },
        { title: '人员照片', slot: 'identityPhoto', align: 'left' },
        { title: '姓名', key: 'name', align: 'left' },
        { title: '姓别', slot: 'gender', align: 'left' },
        { title: '民族', key: 'nation', align: 'left' },
        { title: '证件类型', slot: 'cardType', align: 'left' },
        { title: '证件号', key: 'idCard', align: 'left', width: 200 },
        {
          title: '重点人员类型',
          width: 200,
          slot: 'personType',
          align: 'left',
        },
        {
          title: '居住地址',
          width: 200,
          tooltip: true,
          key: 'homeAddress',
          align: 'left',
        },
        {
          width: 60,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
        },
      ],
      viewDeviceId: 0,
      deviceDetailShow: false,
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      checkRepeatGroup: [], // 选中多个重复记录
      recordShow: false,
      recordData: {},
      deviceUnqualified: null,
      dataStatuList: [],
      keyPersonnelObj: {},
      importantTypes: [],
    };
  },
  async created() {
    this.getPersonCount();
    this.importantTypes = await this.initSource('person_type');
    this.getErrorMessage();
    this.getTableData();
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    // 获取表格数据 getWorkOrderList
    async getTableData() {
      try {
        this.loading = true;
        let params = this.searchData;
        let res = await this.$http.post(equipmentassets.queryImportantPersonPageList, params);
        const datas = res.data.data;
        this.tableData = datas.entities;
        this.importantTypes.forEach((item) => {
          this.keyPersonnelObj[item.dataKey] = item.dataValue;
        });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取字典数据
    async initSource(typekey) {
      try {
        let res = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: typekey },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async getErrorMessage() {
      try {
        let res = await this.$http.get(equipmentassets.queryImportantPersonErrorMessage);
        this.errorList = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          obj.value = item;
          return obj;
        });
      } catch (err) {
        console.log(err);
      }
    },
    async getPersonCount() {
      try {
        let params = {
          checkStatus: '',
          orgCode: '',
        };
        let res = await this.$http.post(equipmentassets.queryImportantPersonCount, params);
        this.statistic.totalNum = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getTableData();
    },
    selectInfo(infoList) {
      this.searchData.reasons = infoList.map((row) => {
        return row.name;
      });
      this.search();
    },
    clear() {
      this.selectOrgTree.orgCode = null;
      this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.search);
      console.log(this.searchData);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    viewRecord(row) {
      // this.recordShow = true;
      // this.recordData = row;
      this.$refs.UnqualifiedReason.init(row);
    },
    deviceModalShow(row, unqualified) {
      this.deviceUnqualified = unqualified || null;
      this.deviceDetailShow = true;
      this.viewDeviceId = row.id;
    },
  },
  watch: {
    isRepeatRecord(val) {
      if (val) {
        this.tableColumns.splice(0, 2);
        this.tableColumns.unshift({
          width: 80,
          align: 'left',
          fixed: 'left',
          slot: 'selfCheckBox',
        });
      } else {
        this.tableColumns = this.$util.common.deepCopy(this.initialTableColumns);
        this.tableColumns.splice(2, 1);
      }
    },
  },
  computed: {
    isRepeatRecord() {
      return false;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    // ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    UnqualifiedReason: require('./unqualified-reason.vue').default,
  },
};
</script>
<style lang="less" scoped>
.keyPhoto {
  width: 56px;
  height: 56px;
  margin: 12px 0;
  img {
    width: 100%;
    height: 100%;
  }
}
.search-module {
  padding: 20px 20px 10px 20px;
  .keyword-input {
    width: 300px;
  }
}
.statistic {
  float: right;
  padding-right: 20px;
  padding-top: 20px;
  position: absolute;
  top: 0;
  right: 0;
  .statistic-title {
    color: #fff;
  }
  .statistic-total {
    color: var(--color-bluish-green-text);
    font-size: 20px;
    margin-bottom: 15px;
  }
  .statistic-today {
    color: #f18a37;
    font-size: 20px;
  }
}
.table-module {
  clear: both;
  .ui-table {
    padding: 0 20px;
  }
  @{_deep}.ivu-table-cell-tree {
    background-color: transparent;
    display: flex;
    color: var(--color-primary);
    border: none;
    height: 20px;
    width: 20px;
    margin-left: -10px;
    i {
      font-size: 20px;
      font-weight: 900;
    }
  }
  // @{_deep}.ivu-table-cell {
  //   display: flex;
  // }
  .repeat-table {
    // 以下css是为了table表格实现相同deviceID颜色保持一致
    @{_deep} .ivu-table .even {
      background-color: #062042 !important;
      padding: 0 20px;
      height: 48px;
      display: flex;
      align-items: center;
    }
    @{_deep} .ivu-table .odd {
      background-color: #041939 !important;
      padding: 0 20px;
      height: 48px;
      display: flex;
      align-items: center;
    }
    @{_deep}.ivu-table-tbody {
      .ivu-table-cell-tree {
        align-items: center;
        justify-content: center;
        position: absolute;
      }
      .ivu-table-column-left {
        .ivu-table-cell {
          align-items: center;
          //padding: 0;
          .ivu-table-cell-slot {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
