export const tabList = [
    { name: '异常人员推荐',
        value: 'RY',
        imgUrl: require('@/assets/img/recommendation/people.png'),
        modelList: [
          {
            'name': '疑似失控人员推荐',
            'type': 'RY_SK',
            // 是否阻止手工研判
            'noManual': false,
            // 默认固定的查询参数
            'defaultParam': {},
            'component': () => import('@/views/recommendation-center/recommendation-home/components/outofcontrolPerson/out-person.vue'),
            // 参数配置
            'paramSetting': [
                {
                  'label': '布控库',
                  'type': 'alarmLib',
                  'paramKey': 'taskId'
                },{
                  'label': '时间范围',
                  'type': 'daySum',
                  'paramKey': 'daySum'
                }
            ]
          },
          {
            'name': '疑似近期首次入城人员推荐',
            'type': 'RY_SCRC',
            'component': () => import('@/views/model-market/face-warfare/face-first-enter/index.vue'),
            'paramSetting': [{
                'label': '视频身份',
                'type': 'input',
                'paramKey': 'vid'
              },{
                'label': '开始时间(必填)',
                'type': 'datetime',
                'paramKey': 'st'
              },{
                'label': '结束时间(必填)',
                'type': 'datetime',
                'paramKey': 'et'
              },{
                'label': '回溯时长(必填)',
                'type': 'inputNumber',
                'paramKey': 'backDays'
              },{
                'label': '选择设备',
                'type': 'selectDevice',
                'paramKey': 'deviceGbIdList'
              }]
          },
          {
            'name': '疑似昼伏夜出人员推荐',
            'type': 'RY_ZFYC',
            'component': () => import('@/views/model-market/face-warfare/face-day-night/index.vue'),
            'paramSetting': [{
                'label': '时间范围',
                'type': 'daySum',
                'paramKey': 'lastDays'
              },{
                'label': '夜出时段',
                'type': 'selectDayNightTime',
                'paramKey': 'nightSt',
                'paramKey2': 'nightEt'
              }]
          },
          {
            'name': '疑似骑电动车不戴头盔人员推荐',
            'type': 'RY_BDTK',
            'component': () => import('@/views/recommendation-center/recommendation-home/components/nonMotorIllegal/no-helmet.vue'),
            'defaultParam': {
              'illegalType': 31
            },
            'paramSetting': [{
                  'label': '时间范围',
                  'type': 'daySum',
                  'paramKey': 'dayNum'
                }, {
                  'label': '出现频次',
                  'type': 'inputNumber',
                  'paramKey': 'illegalCount'
                }, {
                  'label': '身份证号',
                  'type': 'input',
                  'paramKey': 'idCardNo'
                },{
                  'label': '选择设备',
                  'type': 'selectNonMotorIllegalDevice',
                  'paramKey': 'deviceGbIdList'
                }]
          },
          {
            'name': '疑似骑电动车非法载人推荐',
            'type': 'RY_FFZR',
            'component': () => import('@/views/recommendation-center/recommendation-home/components/nonMotorIllegal/illegal-manned.vue'),
            'defaultParam': {
              'illegalType': 32
            },
            'paramSetting': [{
                'label': '时间范围',
                'type': 'daySum',
                'paramKey': 'dayNum'
              }, {
                'label': '出现频次',
                'type': 'inputNumber',
                'paramKey': 'illegalCount'
              }, {
                'label': '身份证号',
                'type': 'input',
                'paramKey': 'idCardNo'
              },{
                'label': '选择设备',
                'type': 'selectNonMotorIllegalDevice',
                'paramKey': 'deviceGbIdList'
              }]
          },
          {
            'name': '疑似娱乐场所从业人员推荐',
            'type': 'RY_YLCS',
            'component': () => import('@/views/recommendation-center/recommendation-home/components/entertainment-venues-workers/index.vue'),
            'paramSetting': [{
                'label': '时间范围',
                'type': 'daySum',
                'paramKey': 'dayNum'
              }, {
                'label': '出现频次',
                'type': 'inputNumber',
                'paramKey': 'frequencyNum'
              }]
          },
          {
            'name': '疑似卖淫人员推荐',
            'type': 'RY_MY',
            'component': () => import('@/views/model-market/face-warfare/prostitutes/index.vue'),
            'paramSetting': [{
                'label': '时间范围',
                'type': 'daySum',
                'paramKey': 'lastDays'
              }, {
                'label': '出现时段',
                'type': 'timeRange',
                'paramKey': 'timePeriodSt',
                'paramKey2': 'timePeriodEt'
              }, {
                'label': '出现区域',
                'type': 'inputNumber',
                'paramKey': 'appearRegionCount'
              }, {
                'label': '年龄段',
                'type': 'ageRange',
                'paramKey': 'ageMin',
                'paramKey2': 'ageMax',
              }]
          },
        ]
    },
  {
    name: "异常车辆推荐",
    value: "CL",
    imgUrl: require("@/assets/img/recommendation/car.png"),
    modelList: [
      {
        name: "疑似套牌车辆推荐",
        type: "CL_TP",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/deck-analysis/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
        ],
      },
      {
        name: "疑似开车打电话违法车辆推荐",
        type: "CL_KCDDH",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/drivephone-search/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
        ],
      },
      {
        name: "疑似未系安全带违法车辆推荐",
        type: "CL_KCWXAQD",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/nonseatbelt-search/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
        ],
      },
      {
        name: "疑似夜间频繁活动车辆推荐",
        type: "CL_PFYC",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/frequently-night-out/index.vue"
          ),
        paramSetting: [
          {
            label: "天数范围(天)",
            type: "inputNumber",
            paramKey: "daysRange",
          },
          {
            label: "夜出天数",
            type: "inputNumber",
            paramKey: "dayNum",
          },
          {
            label: "车牌号",
            type: "input",
            paramKey: "plateNo",
          },
          {
            label: "车牌颜色",
            type: "plateColor",
            paramKey: "plateColor",
          },
          {
            label: "车辆颜色",
            type: "vehicleColor",
            paramKey: "vehicleColor",
          },
          {
            label: "车辆类型",
            type: "vehicleType",
            paramKey: "vehicleType",
          },
          {
            label: "车辆品牌",
            type: "vehicleBrand",
            paramKey: "vehicleBrand",
          },
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
        ],
      },
      {
        name: "疑似车辆落脚点推荐",
        type: "CL_LJD",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/foothold-analysis/index.vue"
          ),
        paramSetting: [
          {
            label: "车牌号",
            type: "input",
            paramKey: "plateNo",
            required: true,
            rules: {
              required: true,
              message: '车牌号为必填字段',
              trigger: 'blur'
            }
          },
          {
            label: "停靠时间(小时)",
            type: "inputNumber",
            paramKey: "stayTime",
          },
        ],
      },
      {
        name: "疑似隐匿车辆推荐",
        type: "CL_YNCL",
        component: () =>
          import(
            "@/views/model-market/vehicle-warfare/hidden-vehicle-analysis/index.vue"
          ),
        paramSetting: [
          {
            label: "追查天数",
            type: "inputNumber",
            paramKey: "dayNum",
            required: true
          },
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
        ],
      },
      {
        name: "疑似关注地区车辆首次出现推荐",
        type: "CL_SCCX",
        component: () =>
          import("@/views/model-market/vehicle-warfare/first-enter/index.vue"),
        paramSetting: [
          {
            label: "开始时间",
            type: "datetime",
            paramKey: "st",
            required: true
          },
          {
            label: "结束时间",
            type: "datetime",
            paramKey: "et",
            required: true
          },
          {
            label: "回溯时长(天)",
            type: "inputNumber",
            paramKey: "backDays",
            required: true
          },
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
          {
            label: "关注地区车牌",
            type: "input",
            paramKey: "plateNo",
          },
          {
            label: "车牌颜色",
            type: "plateColor",
            paramKey: "plateColor",
          },
          {
            label: "车辆品牌",
            type: "vehicleBrand",
            paramKey: "vehicleBrand",
          },
          {
            label: "车辆颜色",
            type: "vehicleColor",
            paramKey: "vehicleColor",
          },
          {
            label: "车辆类型",
            type: "vehicleType",
            paramKey: "vehicleType",
          },
          {
            label: "车牌类型",
            type: "plateClass",
            paramKey: "plateClass",
          },
        ],
      },
    ],
  },
  { name: '区域推荐', 
    value: 'QY', 
    imgUrl: require('@/assets/img/recommendation/area.png'),
    modelList: [
      {
        'name': '疑似人员工作区域推荐',
        'type': 'QY_GZQY',
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-recommend/work-area.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似人员居住区域推荐',
        'type': 'QY_RYJZ',
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-recommend/live-area.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似关注人员高频活动区域推荐',
        'type': 'QY_GPHDQY',
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-recommend/high-freq.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似关注车辆高频活动区域推荐',
        'type': 'QY_GZCLGPHDQY',
        'noManual': true,
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-result.vue'),
        'paramSetting': [{
            'label': '车牌号',
            'type': 'input',
            'paramKey': 'plateNo'
          },{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似关注数据高频活动区域推荐',
        'type': 'QY_GZSJGPHDQY',
        'noManual': true,
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-result.vue'),
        'paramSetting': [{
            'label': 'MAC地址',
            'type': 'input',
            'paramKey': 'mac'
          },{
            'label': 'IMSI编码',
            'type': 'input',
            'paramKey': 'imsi'
          },{
            'label': 'IMEI编码',
            'type': 'input',
            'paramKey': 'imei'
          },{
            'label': 'RFID编码',
            'type': 'input',
            'paramKey': 'rfidCode'
          },{
            'label': 'ETC编号',
            'type': 'input',
            'paramKey': 'obuId'
          },{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似布控告警高频出现区域推荐',
        'type': 'QY_BKGJGPCXQY',
        'noManual': true,
        'component': () => import('@/views/recommendation-center/recommendation-home/components/area-result.vue'),
        'paramSetting': [{
            'label': '频次',
            'type': 'inputNumber',
            'paramKey': 'frequencyNum'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似骑电瓶车不戴头盔高频违法地点推荐',
        'type': 'QY_BDTKGPWFSB',
        'component': () => import('@/views/recommendation-center/recommendation-home/components/nonMotorIllegal/frequency-device-com/index.vue'),
        'defaultParam': {
          'illegalType': 31
        },
        'paramSetting': [{
              'label': '时间范围',
              'type': 'daySum',
              'paramKey': 'dayNum'
            }, {
              'label': '出现频次',
              'type': 'inputNumber',
              'paramKey': 'illegalCount'
            }, {
              'label': '选择设备',
              'type': 'selectNonMotorIllegalDevice',
              'paramKey': 'deviceGbIdList'
            }]
      },
    ]
  },
  {
    name: "设备异常推荐",
    value: "SB",
    imgUrl: require("@/assets/img/recommendation/setting.png"),
    modelList: [
      {
        name: "疑似抓拍异常设备推荐",
        type: "SB_ZPYC",
        component: () =>
          import(
            "@/views/opration-center/device-check/data-detection/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
          {
            label: "数据总量状态",
            type: "selectStatus",
            paramKey: "dataStatusList",
          },
        ],
      },
      {
        name: "疑似解析异常设备推荐",
        type: "SB_JXYC",
        component: () =>
          import(
            "@/views/opration-center/device-check/data-detection/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
          {
            label: "数据总量状态",
            type: "selectStatus",
            paramKey: "structureDataStatusList",
          },
        ],
      },
      {
        name: "疑似数据链路异常推荐",
        type: "SB_SJLLYC",
        component: () =>
          import(
            "@/views/opration-center/device-check/data-reporting-status/index.vue"
          ),
        paramSetting: [
          {
            label: "选择设备",
            type: "selectDevice",
            paramKey: "deviceGbIdList",
          },
          {
            label: "数据有无状态",
            type: "selectHaveDataStatus",
            paramKey: "haveDataStatusList",
          },
          {
            label: "数据延迟状态",
            type: "selectDelayStatus",
            paramKey: "delayStatusList",
          },
        ],
      },
    ],
  },
  { name: '疑似多维感知数据关系确认推荐', 
    value: 'DWRY', 
    imgUrl: require('@/assets/img/recommendation/relation.png'),
    modelList: [
      {
        'name': '疑似人-电磁关系绑定推荐',
        'type': 'DWRY_YSRDWGXBD',
        component: () => import('@/views/model-market/electic-warfare/vid-electric/index.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          },{
            'label': '同行频次',
            'type': 'inputNumber',
            'paramKey': 'minCount'
          },{
            'label': '间隔时间(S)',
            'type': 'inputNumber',
            'paramKey': 'rangTime'
          }]
      },
      {
        'name': '疑似人-RFID关系绑定推荐',
        'type': 'DWRY_YSRFIDGXBD',
        'component': () => import('@/views/model-market/electic-warfare/vid-rfid/index.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          },{
            'label': '同行频次',
            'type': 'inputNumber',
            'paramKey': 'minCount'
          },{
            'label': '间隔时间(S)',
            'type': 'inputNumber',
            'paramKey': 'rangTime'
          }]
      },
      {
        'name': '疑似人-车辆关系绑定推荐',
        'type': 'DWRY_YSRCHEBD',
        'component': () => import('@/views/recommendation-center/recommendation-home/components/vid-vehicle/index.vue'),
        'paramSetting': [{
            'label': '视频身份',
            'type': 'input',
            'paramKey': 'vid'
          },{
            'label': '时间范围',
            'type': 'daySum',
            'paramKey': 'dayNum'
          }]
      },
      {
        'name': '疑似车辆-ETC关系绑定推荐',
        'type': 'DWRY_YSCLETCGXBD',
        'paramSetting': []
      },
    ]
  },
];
