<template>
  <div class="auto-fill">
    <div>
      <ChartsContainer :abnormalCount="abnormalCount" />
      <div class="hearder-title">
        <search-rationality
          :currentTree="currentTree"
          :treeData="treeData"
          :taskObj="taskObj"
          :width="width"
          :searchList="searchList"
          @params-change="paramsChange"
          @startSearch="startSearch"
        >
          <template #search>
            <div class="export">
              <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
                <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
                <span class="inline ml-xs">导出</span>
              </Button>
            </div>
          </template>
        </search-rationality>
      </div>
    </div>
    <ui-table class="auto-fill" :table-columns="columns" :table-data="tableData" :loading="loading">
      <template #deviceId="{ row, index }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #checkStatus="{ row }">
        <span
          class="tag"
          :style="{
            background: row.checkStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
          >{{ checkStatusList(row.checkStatus) }}</span
        >
      </template>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <ui-btn-tip icon="icon-chakanjietu" content="查看抓拍图片" @click.native="viewCapturePicture(row)"></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <capture-picture v-model="capturePictureVisible" :params="currentRow"></capture-picture>
  </div>
</template>
<script>
import { icons } from '../components/common';
import inspectionrecord from '@/config/api/inspectionrecord';

export default {
  name: 'capture-rationality',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchCard: require('./component/searchCard.vue').default,
    SearchRationality: require('./component/search-rationality.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    CapturePicture: require('./component/capture-picture').default,
  },
  props: {
    /**
     * 右上角检测任务筛选
     */
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    /**
     * 左侧树结构
     */
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      loading: false,
      hasLast: false,
      width: 155, // 设备模式数量检索展示对应label宽度
      searchList: [], // 设备模式检索下拉框
      abnormalCount: [{ title: '采集设备总数', icon: 'icon-exceptionlibrary' }], // 统计展示
      modelTag: 0, // 聚档模式,图像模式
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      minusHeight: 512, // 表格
      columns: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          width: 120,
          ellipsis: true,
          tooltip: true,
        },
        { title: '抓拍总数量', key: 'captureCount', width: 100 },
        { title: '近天抓拍数量', key: 'captureRecentlyCount', width: 120 },
        { title: '昨日抓拍数量', key: 'captureTodayCount', width: 120 },
        { title: '持续无抓拍天数', key: 'noCaptureDays', width: 120 },
        {
          title: '历史同天平均抓拍量',
          key: 'historyAverageCount',
          width: 180,
          renderHeader: (h) => {
            return (
              <Tooltip max-width="400" transfer>
                <span class="vt-middle">历史同天平均抓拍量</span>
                <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
                <template slot="content">
                  <p class="mb-md f-14">抓拍数量突降计算逻辑：</p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>昨日抓拍量C1：
                    假设今日2021/10/20日发起检测，则C1=2021/10/19日抓拍量；
                  </p>
                  <p class="mb-md f-12">
                    <div class="white-circle mr-xs"></div>
                    历史同天抓拍量C2： 平台上线至2021年10月 19 日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；
                  </p>
                  <p class="f-12">
                    <div class="white-circle mr-xs"></div>
                    若（C2-C1）/C2>=50%（ 配置值），则判定抓拍数据量突降。
                  </p>
                </template>
              </Tooltip>
            );
          },
        },
        { title: '昨日变化', key: 'changeRatio', width: 80 },
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 90,
          slot: 'checkStatus',
        },
        {
          title: '异常原因',
          key: 'message',
          width: 150,
          ellipsis: true,
          tooltip: true,
        },
        { title: '检测时间', key: 'examineTime', width: 150 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 60,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      cardInfo: [],
      cardSearchList: [],
      tableData: [],
      currentIcon: 'icon-exceptionlibrary',
      currentRow: {},
      capturePictureVisible: false,
      exportLoading: false,
    };
  },
  computed: {
    computedDeviceType() {
      /**
       * ("设备类型 1-车辆卡扣 2-视图抓拍类")
       * private String deviceType;
       * 309 视图抓拍类
       * 412 车辆卡口
       */
      return this.currentTree.id === 309 ? 2 : 1;
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        if (val.orgCodeList && val.orgCodeList.length) {
          this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(val.orgCodeList)), 'id', 'parentId');
          // this.selectOrgCode(this.treeData[0])
        }
      },
    },
    currentTree: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        this.getIcon();
      },
    },
  },
  methods: {
    paramsChange(val) {
      this.searchData = val;
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        deviceType: this.computedDeviceType,
        resultId: this.searchData.resultId,
        ...this.searchData,
      };
      try {
        let res = await this.$http.post(inspectionrecord.getEvaluationCaptureRationalityExport, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        if (!this.searchData.orgCode) {
          return this.$Message.error('请选择组织机构');
        }
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(
          inspectionrecord.getRationalityPageList,
          Object.assign(
            this.pageData,
            {
              resultId: this.searchData.resultId,
              deviceType: this.computedDeviceType,
            },
            this.searchData,
          ),
        );
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    async statisticsCount() {
      // 统计接口
      if (!this.searchData.orgCode) {
        return false;
        // return this.$Message.error("请选择组织机构")
      }
      let params = {
        orgCode: this.searchData.orgCode,
        resultId: this.searchData.resultId,
        isRecord: true, //true 为检测记录
        indexId: this.currentTree.indexId,
      };
      let {
        data: { data },
      } = await this.$http.get(inspectionrecord.getRationalityRecordStatistics, { params });
      this.infoObj = data;
    },
    viewCapturePicture(row) {
      this.currentRow = row;
      this.capturePictureVisible = true;
    },
    // 检索
    async startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.$nextTick(async () => {
        await this.statisticsCount();
        await this.abnormalCountMap();
        this.initList();
      });
    },
    getIcon() {
      this.currentIcon = icons[this.currentTree.id] || 'icon-exceptionlibrary';
    },
    // 统计参数填充
    abnormalCountMap() {
      let map = {
        totalDeviceCount: '设备总数',
        checkDeviceCount: '检测设备数',
        abnormalDeviceCount: '异常设备数',
        normalDeviceCount: '合格设备数',
        normalPercent: '合格率',
        errorNoData: '历史无抓拍数目',
        errorTodayNoData: '昨日无抓拍',
        errorTooLessData: '抓拍数据过少',
        errorDataSwoop: '抓拍数量突降',
      };
      let list = [];
      this.infoObj &&
        this.infoObj.map((item) => {
          list.push({
            title: map[item.key] || '',
            count: item.desc,
            icon: this.currentIcon,
          });
        });
      this.abnormalCount = list;
      if (!list.length) {
        this.abnormalCount = [{ title: '采集设备总数', icon: this.currentIcon }];
      }
      return list;
    },
    checkStatusList(checkStatus) {
      return checkStatus === '1' ? '正常' : '异常';
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.white-circle {
  position: relative;
  display: inline-block;
  line-height: 10px;
  vertical-align: middle;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #f5f5f5;
}
.export {
  display: flex;
  justify-content: flex-end;
}
</style>
