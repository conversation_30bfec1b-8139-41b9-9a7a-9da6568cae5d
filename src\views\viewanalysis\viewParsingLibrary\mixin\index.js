/*
    * @FileDescription: 时间处理
    * @Author: H
    * @Date: 2023/08/23
    * @LastEditors: 
    * @LastEditTime: 
*/
import { mapMutations } from 'vuex';
export const myMixins = {
    data() {
        return {
            
        }
    },
    // deactivated() {
    //     this.setLayoutNoPadding(false)
    // },
    // beforeDestroy() {
    //     this.setLayoutNoPadding(false)
    // },
    created() {
        // this.setLayoutNoPadding(true)
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),

        /**
         * 上一个
         */
        prePage(pageNum) {
            if (pageNum < 1) {
                this.$Message.warning("已经是第一个了")
                return
            } else {
                this.pageInfo.pageNumber = pageNum;
                if(!this.fallsPage) {
                    this.queryList(1)
                }
            }
        },
        /**
         * 下一个
         */
        async nextPage(pageNum) {
            this.pageInfo.pageNumber = pageNum;
            let num = this.pageInfo.pageNumber;
            let size = this.pageInfo.pageSize;
            if (this.total <= num * size) {
                this.$Message.warning("已经是最后一个了")
                return
            } else {
                if(!this.fallsPage) {
                    this.queryList(2)
                }
            }
        },
        // 排序
        handleAdvancedSort(val){
            if(val != this.queryParam.sortField) {
                this.queryParam.sortField = val;
                if (val == 'similarity'){
                    this.queryParam.order = this.similUpDown ? 'asc' : 'desc';
                } else {
                    this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
                }
            }else{
                this.queryParam.sortField = val;
                if(val == 'similarity') {
                    this.similUpDown = !this.similUpDown
                    this.queryParam.order = this.similUpDown ? 'asc' : 'desc';
                }else{
                    this.timeUpDown = !this.timeUpDown
                    this.queryParam.order = this.timeUpDown ? 'asc' : 'desc';
                } 
            }
            this.refreshDataList(false, true)
        },
    }
}