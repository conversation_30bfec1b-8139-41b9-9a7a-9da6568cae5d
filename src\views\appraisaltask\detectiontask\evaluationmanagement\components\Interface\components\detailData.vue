<template>
  <!-- 轨迹准确率弹框 -->
  <ui-modal class="detailData-accuracy" v-model="visible" title="查看返回数据" :styles="styles" :footer-hide="true">
    <div class="list">
      <div class="carItem" v-for="item in tableData" :key="item.id">
        <div class="item">
          <!-- <div class="num" v-if="item.similarity">{{ similarityVal(item.similarity) }}</div> -->
          <div class="img">
            <ui-image :src="item.storagePath" />
          </div>
          <div class="group-message">
            <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据'}`">
              <i class="icon-font icon-shijian"></i>
              <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ item.shotTime }}</span>
            </p>
            <!-- <p :title="item.catchPlace">
              <i class="icon-font icon-dizhi"></i>
              <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                item.catchPlace ? item.catchPlace : '暂无数据'
              }}</span>
            </p> -->
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
  </ui-modal>
</template>

<style lang="less" scoped>
.detailData-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .list {
    position: relative;
    margin-top: 10px;
    height: 750px;
    overflow-y: auto;

    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
    }
  }
  .carItem {
    height: 236px;
    margin: 10px 10px 0px 0;
    width: 188px;
    display: inline-block;
    // max-width: 188px;
    .item {
      position: relative;
      height: 100%;
      background: #0f2f59;
      .num {
        position: absolute;
        right: 0;
        z-index: 100;
        padding: 10px;
        border-radius: 5px;
        background: rgba(42, 95, 175, 0.6);
      }
      .img {
        position: relative;
        cursor: pointer;
        position: relative;
        width: calc(100% - 28px);
        height: 167px;
        padding-top: 14px;
        margin-left: 14px;
        display: flex;
        align-items: center;
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          z-index: 100;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
        span {
          position: absolute;
          top: 50%;
          left: 40%;
          z-index: 100;
          cursor: pointer;
        }
        .percent {
          position: absolute;
          top: 1px;
          left: 1px;
          display: inline-block;
          padding: 0 2px;
          min-width: 32px;
          text-align: center;
          background: #ea800f;
          color: #ffffff;
          z-index: 99;
        }
      }
      img {
        width: 100%;
        max-width: 100%;
        max-height: 156px;
        background: #999;
      }

      .group-message {
        padding-left: 12px;
        margin-top: 12px;
      }
      .icon-URLbukefangwen1 {
        color: #bc3c19;
        position: absolute;
        bottom: 54px;
        right: 14px;
        font-size: 60px;
        z-index: 10;
      }
    }
  }
}
.onlys {
  width: 80%;
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  data() {
    return {
      tableData: [],
      styles: {
        width: '9rem',
      },
      visible: false,
      loading: false,
    };
  },
  // async mounted() {
  //   await this.init()
  // },

  methods: {
    init(id, apiIndex) {
      this.visible = true;
      this.$http
        .get(governanceevaluation.Interdetail, {
          params: { id: id, apiIndex: apiIndex },
        })
        .then((res) => {
          this.tableData = res.data;
        });
    },
    // viewBigPic(item) {
    //   if (!item.storagePath) {
    //     this.$Message.warning('大图URL缺失')
    //     return
    //   }
    //   this.imgList = [item.storagePath]
    //   this.bigPictureShow = true
    // },
    // viewBig(item) {
    //   this.imgList = [item.identityPhoto]
    //   this.bigPictureShow = true
    // },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {},
};
</script>
