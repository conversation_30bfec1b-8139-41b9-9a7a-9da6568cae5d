<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      :algorithmList="algorithmList"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-selection-change="selectChangeHandler"
        >
          <template #historyTime="{ row }">
            <div>
              {{ row.videoStartTime | timeFormat }} -
              {{ row.videoEndTime | timeFormat }}
            </div>
          </template>
          <template #taskType="{ row }">
            <span>
              {{ taskTypeList[row.taskType] }}
            </span>
          </template>
          <template #status="{ row }">
            <span>{{
              taskStatusList.find((item) => item.key == row.status).label
            }}</span>
          </template>
          <template #pointNum="{ row }">
            <DevicePop :deviceList="row.taskResourceList"></DevicePop>
          </template>
          <template #algorithmName="{ row }">
            <AlgorithmTagPop
              :data="row.compareAlgorithmNames"
            ></AlgorithmTagPop>
          </template>
          <template #alarmLevel="{ row }">
            <AlarmLevel :alarmLevel="row.taskLevel"></AlarmLevel>
          </template>
          <template #action="{ row }">
            <TableAction
              :row="row"
              :subTaskType="subTaskType"
              @handleEdit="handleEdit"
              @handleSearch="
                (val) =>
                  toDetailByTask({
                    taskId: val.id,
                    taskType: params.taskParsingType,
                  })
              "
              @mapLoaction="mapLoaction"
              @handleDel="handleDel"
            ></TableAction>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <addModal
      v-if="isShowAdd"
      ref="addModal"
      :title="subTaskId ? '编辑历史解析任务' : '新增历史解析任务'"
      :subTaskType="subTaskType"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      :algorithmList="algorithmList"
      @updated="jobUpdated"
      @close="handlerClose"
    ></addModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "../components/search.vue";
import addModal from "../components/add-modal.vue";
import TableAction from "../components/table-action.vue";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import DevicePop from "@/views/multimodal-analysis/components/device-pop.vue";
import TaskHandler from "@/views/semantic-placement/mixins/taskHandler.js";
import AlgorithmTagPop from "../components/algorithm-tag-pop.vue";
import AlarmLevel from "../components/alarm-level.vue";
import ExpandRow from "@/views/semantic-placement/components/expandRow.vue";
import { getLLMCompareTaskPageList } from "@/api/semantic-placement.js";
import { taskType } from "@/api/largeScreen";
export default {
  name: "SemanticHistoryAnalysis",
  components: {
    Search,
    addModal,
    adjustPosition,
    DevicePop,
    AlgorithmTagPop,
    AlarmLevel,
    TableAction,
  },
  props: {},
  mixins: [TaskHandler],
  data() {
    return {
      subTaskType: "history",
      list: [],
      tagList: [
        {
          labelName: "居中算法",
          labelColor: "#B06292",
        },
        {
          labelName: "验货算法",
          labelColor: "#A06292",
        },
        {
          labelName: "计算机算法",
          labelColor: "#D06292",
        },
      ],
      loading: false,
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        taskParsingType: 3, // 结构化解析类型 3历史解析
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const deviceInfoList = row?.taskResourceList?.map((item) => {
              const { deviceInfo, ...otherParam } = item;
              return {
                ...deviceInfo,
                ...otherParam,
                deviceId: deviceInfo?.id,
              };
            }) || [{}, {}, {}];
            return h(ExpandRow, {
              props: {
                tableList: deviceInfoList,
                columns: this.childColumns,
                currentJob: this.list[index],
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: val.id,
                    ...val,
                    taskType: this.params.taskParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  // 子任务删除
                  this.deleteTasks([val]);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 170, ellipsis: true },
        { title: "历史时段", slot: "historyTime", width: 360 },
        { title: "处理类型", slot: "taskType" },
        { title: "算法名称", slot: "algorithmName", width: 160 },
        { title: "报警级别", slot: "alarmLevel" },
        { title: "任务状态", slot: "status", align: "center" },
        { title: "点位数量", slot: "pointNum" },
        { title: "处理时长", key: "handleTime" },
        { title: "创建人", key: "creator", ellipsis: true },
        { title: "操作", width: 120, slot: "action" },
      ],
      childColumns: [
        { title: "设备名称", slot: "name", align: "center" },
        { title: "任务状态", slot: "status", align: "center" },
        { title: "处理进度", slot: "analysisSchedule" },
        { title: "处理时长", slot: "analysisTime" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {},
  mounted() {
    this.getLLMCompareAlgorithm();
    this.getList();
  },
  beforeDestroy() {},
  methods: {
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    handlerClose() {
      this.isShowAdd = false;
    },
    // 查询列表
    async getList(otherParam = {}) {
      this.loading = true;
      try {
        let param = {
          ...this.$refs.searchRef.getSearchParam(),
          ...this.params,
          ...otherParam,
        };
        const { data } = await getLLMCompareTaskPageList(param);
        this.list = data?.entities || [];
        this.total = data?.total || 0;
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init();
      });
    },
    handleEdit(row, subDeviceId = "") {
      if (row.status != 0) {
        return;
      }
      this.subTaskId = row.id;
      this.subDeviceId = subDeviceId;
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init({ ...row });
      });
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const deviceInfoList = item.taskResourceList.map((item) => {
        return item.deviceInfo;
      });
      var arrayP = [];
      deviceInfoList &&
        deviceInfoList.length > 0 &&
        deviceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      }
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
  },
};
</script>
<style lang="less" scoped>
@import "../style/index.less";
</style>
