<template>
  <div>
    <el-carousel
      indicator-position="none"
      ref="carousel"
      class="img-list"
      :arrow="imgList.length > 1 ? 'always' : 'never'"
      :autoplay="false"
      :initial-index="index"
      @change="($event) => $emit('changeCarousel', $event)"
    >
      <el-carousel-item v-for="(item, index) in imgList" :key="`${index}-${item}`">
        <div class="content" @click="$emit('clickImage')">
          <uiImage v-if="!!item" :src="item" />
          <div v-else class="none">
            <span class="secondary">暂无大图</span>
          </div>
          <slot name="totalNum" :currentNum="index + 1"></slot>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<style lang="less" scoped>
@_deep: ~'>>>';
.img-list {
  height: 650px;
  margin-top: 20px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      max-width: 100%;
      max-height: 100%;
    }
    // /deep/ .tileImage {
    //   width: 100%;
    //   height: 100%;
    // }
    .none {
      height: 650px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  @{_deep} .el-carousel__container {
    height: 100%;
  }
}
</style>

<script>
export default {
  data() {
    return {
      index: 0,
    };
  },
  created() {},
  mounted() {
    document.onkeyup = (e) => {
      if (e.keyCode === 37) {
        this.$refs.carousel.prev();
      } else if (e.keyCode === 39) {
        this.$refs.carousel.next();
      }
    };
  },
  methods: {},
  watch: {
    viewIndex(val) {
      this.index = val;
      this.$refs.carousel.setActiveItem(this.index);
    },
  },
  computed: {},
  props: {
    viewIndex: {
      default: 0,
    },
    imgList: {
      type: Array,
      required: true,
    },
  },
  components: {
    uiImage: require('@/components/ui-image.vue').default,
  },
  beforeDestroy() {
    document.onkeyup = null;
  },
};
</script>
