<template>
  <div class="single-trans-box">
    <div class="tans-form">
      <Form :model="formData" ref="formData" :rules="ruleValidate" :label-width="200">
        <FormItem label="经度" prop="lng">
          <Input v-model="formData.lng" placeholder="请输入经度"></Input>
        </FormItem>
        <FormItem label="纬度" prop="lat">
          <Input v-model="formData.lat" placeholder="请输入维度"></Input>
        </FormItem>
        <FormItem label="原始坐标系">
          <RadioGroup v-model="formData.type">
            <Radio label="WGS84">WGS84(国际通用)</Radio>
            <Radio label="GCJ02">GCJ02(高德、QQ地图)</Radio>
            <Radio label="BD09">BD09(百度地图)</Radio>
            <Radio label="CGCS2000">CGCS2000(2000国家大地坐标)</Radio>
          </RadioGroup>
        </FormItem>
        <!-- <FormItem label="精度">
          <InputNumber :min="0" v-model="formData.accuracy" placeholder="请输入整数"></InputNumber>
        </FormItem> -->
      </Form>
    </div>
    <div class="btn-box d_flex width-300 mt-lg mb-lg">
      <Button type="primary" class="mr-sm grow-1" :loading="loading" @click.stop="handleTrans">{{
        loading ? '坐标转换中' : '坐标转换'
      }}</Button>
      <Button @click.stop="resest">清空</Button>
    </div>
    <div class="result-box">
      <Form :model="resultData" :label-width="200">
        <FormItem label="WGS84(lng,lat)">
          <Input readonly v-model="resultData.WGS84"></Input>
        </FormItem>
        <FormItem label="GCJ02(lng,lat)">
          <Input readonly v-model="resultData.GCJ02"></Input>
        </FormItem>
        <FormItem label="BD09(lng,lat)">
          <Input readonly v-model="resultData.BD09"></Input>
        </FormItem>
        <FormItem label="CGCS2000(east,north)">
          <Input readonly v-model="resultData.CGCS2000"></Input>
        </FormItem>
      </Form>
    </div>
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';
export default {
  data() {
    return {
      formData: {
        lng: '', //经度
        lat: '', //纬度
        type: 'WGS84', //原始坐标系
        // accuracy: null, //精度（整数）
      },
      ruleValidate: {
        lng: [{ required: true, message: '经度不能为空', trigger: 'blur' }],
        lat: [{ required: true, message: '纬度不能为空', trigger: 'blur' }],
      },
      resultData: {
        WGS84: '',
        GCJ02: '',
        BD09: '',
        CGCS2000: '',
      },
      loading: false,
    };
  },
  name: '',
  computed: {},
  methods: {
    resest() {
      this.$refs['formData'].resetFields();
      this.formData.lng = '';
      this.formData.lat = '';
      this.formData.type = 'WGS84';
      this.clearObjectVlue(this.resultData);
    },
    //点击转换
    async handleTrans() {
      this.$refs['formData'].validate(async (valid) => {
        if (!valid) {
          return;
        }

        try {
          this.loading = true;
          const params = {
            ...this.formData,
          };
          let res = await this.$http.post(governancetoolset.coordinateTrans, params);
          let coordData = res.data.data;
          Object.keys(coordData).forEach((key) => {
            this.$nextTick(() => {
              this.resultData[key] = coordData[key]?.lng + ',' + coordData[key]?.lat;
            });
          });
        } catch (err) {
          console.log(err);
        } finally {
          this.loading = false;
        }
      });
    },
    //清空对象的值
    clearObjectVlue(obj) {
      Object.keys(obj).forEach((key) => {
        obj[key] = '';
      });
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.single-trans-box {
  .width-300 {
    width: 300px;
  }
  @{_deep} .ivu-form-item {
    margin-bottom: 20px;
  }
  @{_deep}.ivu-input-wrapper,
  .ivu-input-number {
    width: 300px;
  }

  .btn-box {
    margin-left: 200px;

    .grow-1 {
      flex-grow: 1;
    }
  }
}
</style>
