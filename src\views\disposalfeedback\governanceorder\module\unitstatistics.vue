<template>
  <div class="work-order-statistics-container">
    <work-order-statistics
      ref="workOrderStatistics"
      v-bind="$attrs"
      v-on="$listeners"
      :table-columns="computedShowColumns"
      :backstage-data="backstageData"
      :getColumnLoading="getColumnLoading"
      :search-data="searchData"
      :commonSearchData="commonSearchData"
      @certainChangeColumn="getColumnsAfterSelectMx"
    >
    </work-order-statistics>
  </div>
</template>
<script>
import statisticsColumnMixin from '@/views/disposalfeedback/governanceorder/util/statisticsColumnMixin';
export default {
  mixins: [statisticsColumnMixin],
  props: {
    commonSearchData: {},
  },
  data() {
    return {
      searchData: {
        statisticsType: 1,
      },
    };
  },
  methods: {
    search() {
      this.spliceColumnsMx();
      this.$refs.workOrderStatistics.initList();
    },
  },
  async created() {
    await this.getColumnsMx(); //获取展示的列
  },
  components: {
    WorkOrderStatistics: require('@/views/disposalfeedback/governanceorder/module/work-order-statistics.vue').default,
  },
};
</script>
<style lang="less" scoped>
.work-order-statistics-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
