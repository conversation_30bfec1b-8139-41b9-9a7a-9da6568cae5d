<template>
  <div class="history-container auto-fill">
    <div class="history-wrapper auto-fill">
      <div class="header mb-md">
        <div>
          <i class="icon-font icon-shujujiance f-18 vt-middle"></i>
          <span class="ml-sm f-16 font-white vt-middle"
            ><slot>{{ compareTarget.label }}</slot></span
          >
        </div>
        <div>
          <ui-label class="inline mr-md" label="对账时间">
            <Select class="width-md" v-model="searchData.yearMonth" placeholder="请选择对账时间" clearable @on-change='onChangeYearMonth'>
              <Option v-for="(item, index) in compareHistoryList" :key="index" :value="item">{{ item }} </Option>
            </Select>
          </ui-label>
          <ui-label class="inline" label="行政区划">
            <api-area-tree
              :select-tree="selectTree"
              @selectedTree="selectedArea"
              :filter-tree-code="filterCivilCode"
              placeholder="请选择行政区划"
            ></api-area-tree>
          </ui-label>
        </div>
      </div>
      <div class="table-wrapper auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
        >
          <template #compare="{ row, index }">
            <div class="mb-md">{{ compareTarget.source }}</div>
            <div>{{ compareTarget.target }}</div>
          </template>
          <template #total="{ row, index }">
            <div class="mb-md">{{ row.targetTotal }}</div>
            <div>{{ row.sourceTotal }}</div>
          </template>
          <template #exclusive="{ row, index }">
            <div class="mb-md target-only">{{ row.targetOnly }}</div>
            <div class="source-only">{{ row.sourceOnly }}</div>
          </template>
          <template #diff="{ row, index }">
            <span class="diff">{{ row.diff }}</span>
          </template>
          <template #action="{ row, index }">
            <create-tabs
              :componentName="themDataDetail.componentName"
              :importantTabName="themDataDetail.title"
              :tabs-text="themDataDetail.text"
              :tabs-query="{
                batchId: row.batchId,
                id: row.id,
                civilCode: row.civilCode,
                type,
              }"
              class="inline mr-md"
            >
              <ui-btn-tip icon="icon-chakanxiangqing mr-md" content="明细"></ui-btn-tip>
            </create-tabs>
          </template>
        </ui-table>
      </div>
    </div>
  </div>
</template>
<script>
import assetcomparison from '@/config/api/assetcomparison';
import { TAB_LIST, THIS_LEVEL_BY_GB28181 } from '@/views/viewassets/assetcompare/modules/enum.js';
import { isCurrentLevelByType } from '@/views/viewassets/assetcompare/modules/util';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'history',
  data() {
    return {
      themDataDetail: {
        componentName: 'AssetComparisonResult', // 需要跳转的组件名
        text: '对账明细', // 跳转页面标题
        title: '本级对账-对账明细',
        type: 'view',
      },

      compareHistoryList: [],
      selectTree: {
        regionCode: '',
      },
      type: THIS_LEVEL_BY_GB28181,
      searchData: {
        yearMonth: '',
        civilCode: '',
      },
      tableLoading: false,
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'center'},
        { title: '比对对象', slot: 'compare', minWidth: 100, align: 'left', className: 'compare' },
        { title: '设备总量', slot: 'total', minWidth: 100, align: 'left' },
        { title: '独有数量 ', slot: 'exclusive', minWidth: 100, align: 'left' },
        { title: '相同数量', key: 'same', minWidth: 100, align: 'left' },
        { title: '差异数量', slot: 'diff', minWidth: 100, align: 'left' },
        { title: '对账时间', key: 'executeTime', minWidth: 100, align: 'left' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          align: 'center',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      configInfo: 'assetcompare/getConfigInfo',
      getDeployNetwork: 'algorithm/getDeployNetwork',
    }),
    filterCivilCode() {
      return this.$route.query.civilCode || '';
    },
    compareTarget() {
      const { type } = this.$route.query;
      let tab = TAB_LIST.find((item) => item.value === type);
      const { selfDomain, otherDomain } = this.configInfo[tab.value] || {};
      const sourceName = this.$options.filters.filterDic(selfDomain, this.getDeployNetwork);
      const targetName = this.$options.filters.filterDic(otherDomain, this.getDeployNetwork);
      if (isCurrentLevelByType(type)) {
        return tab
      }else {
        return {
          ...tab,
          source : `${sourceName}${tab.source}`,
          target : `${targetName}${tab.target}`,
        }
      }
    },
  },
  async created() {
    this.getConfig();
    await this.getHistoryDropDown();
    this.setDefaultHistory(this.compareHistoryList);
    this.setDefaultRegion();
    await this.getHistory();
  },
  methods: {
    ...mapActions({
      getConfig: 'assetcompare/getConfig', // 批量获取字典值
    }),
    onChangeYearMonth() {
      this.getHistory();
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
      this.getHistory();
    },
    setDefaultHistory(compareHistoryList) {
      if (!compareHistoryList.length) return;
      this.searchData.yearMonth = compareHistoryList[0];
    },
    setDefaultRegion() {
      let { civilCode } = this.$route.query;
      this.searchData.civilCode = civilCode;
      this.selectTree.regionCode = civilCode;
    },
    async getHistoryDropDown() {
      try {
        let { type } = this.$route.query;
        let params = {
          type,
        };
        let { data } = await this.$http.get(assetcomparison.getHistoryDropDown, { params });
        this.compareHistoryList = data.data.yearMonths || [];
      } catch (e) {
        console.log(e);
      }
    },
    async getHistory() {
      try {
        this.tableLoading = true;
        let { type } = this.$route.query;
        let params = {
          type,
          yearMonth: this.searchData.yearMonth,
          civilCode: this.searchData.civilCode,
        };
        let { data } = await this.$http.get(assetcomparison.getHistory, { params });
        this.tableData = data.data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style scoped lang="less">
.history-container {
  position: relative;
  height: 100%;
  background: #071b39;

  .history-wrapper {
    padding: 15px 20px;

    .header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 15px;
      border-bottom: 1px solid #074277;

      .icon-font {
        background: linear-gradient(180deg, #19dff6 0%, #3680d4 100%);
        -webkit-background-clip: text;
        color: #0000;
      }
    }
    .table-wrapper {
      @{_deep} .compare {
        padding: 15px 0;
      }

      @{_deep} .target-only {
        color: #d66418;
      }
      @{_deep} .source-only {
        color: #b1b836;
      }
      @{_deep} .diff {
        color: #bc3c19;
      }
    }
  }
}
</style>
