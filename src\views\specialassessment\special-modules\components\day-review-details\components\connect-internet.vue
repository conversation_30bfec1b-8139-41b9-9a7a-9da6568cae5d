<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #deviceId="{ row }">
        <span>{{ row.deviceId }}</span>
        <span class="more-span" v-if="row.multiDevice">多目</span>
      </template>
      <template #description="{ row }">
        <span :class="row.outcome == '1' ? 'sucess' : 'error'">{{ row.description }}</span>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
      </template>
      <template slot="qualified" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #phyStatus="{ row }">
        <span v-if="row.phyStatusText" :class="row.phyStatus === '1' ? 'qualified-color' : 'unqualified-color'">
          {{ row.phyStatusText }}
        </span>
        <span v-else>--</span>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakanjietu"
          content="查看抓拍图片"
          :styles="{ color: 'var(--color-primary)' }"
          @click.native="checkReason(row)"
        >
        </ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <look-picture
      v-model="capturePictureVisible"
      :table-data="reasonTableData"
      :reason-page="reasonPage"
      :reason-loading="reasonLoading"
      :active-index-item="activeIndexItem"
      @handlePageChange="handleImgPageChange"
      @handlePageSizeChange="handleImgPageSizeChange"
      title="查看抓拍图片"
    ></look-picture>
    <look-picture-multi-device
      v-model="capturePictureVisibleMultiDevice"
      :table-data="reasonTableData"
      :reason-page="reasonPage"
      :reason-loading="reasonLoading"
      :active-index-item="activeIndexItem"
      :current-row="currentRow"
      @handlePageChange="handleImgPageChange"
      @handlePageSizeChange="handleImgPageSizeChange"
      @onChangeDeviceIds="onChangeDeviceIds"
      title="查看抓拍图片"
    ></look-picture-multi-device>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'image-online',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: {},
      currentRow: {},
      capturePictureVisible: false,
      capturePictureVisibleMultiDevice: false,
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      reasonLoading: false,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  methods: {
    async getFaceReason() {
      try {
        let params = {
          indexId: this.currentRow.indexId,
          batchId: this.currentRow.batchId,
          access: 'TASK_RESULT',
          displayType: 'REGION',
          customParameters: {
            deviceId: this.currentRow.deviceId,
            deviceType: this.currentRow.deviceType,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        params.displayType === 'REGION'
          ? (params.orgRegionCode = this.$route.query.regionCode)
          : (params.orgRegionCode = this.$route.query.orgCode);
        this.reasonLoading = true;
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    getReason() {
      this.getFaceReason();
    },
    checkReason(row) {
      this.reasonPage = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.currentRow = row;
      if (row.multiDevice) {
        this.capturePictureVisibleMultiDevice = true;
      } else {
        this.capturePictureVisible = true;
      }
      this.getReason(row);
    },
    handleImgPageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handleImgPageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    onChangeDeviceIds(deviceIds) {
      this.reasonPage.pageNum = 1;
      this.picDeviceIds = deviceIds;
      this.getReason();
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    LookPictureMultiDevice:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/BayonetConnectionRate/components/look-picture.vue')
        .default,
    LookPicture:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/BayonetOnlineRate/components/look-picture.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
.more-span {
  display: inline-block;
  padding: 0 4px;
  margin-left: 10px;
  color: var(--color-primary);
  border-radius: 4px;
  background: rgba(44, 134, 248, 0.2);
  border: 1px solid var(--color-primary);
  font-size: 12px;
}
</style>
