<template>
  <div class="container box">
    <div class="person">
      <!-- 查询 -->
      <Search @searchForm="searchForm" />
      <ui-table ref="seletction" :columns="columns" :data="tableListByPage" :loading="loading">
        <template #fileSize="{ row }">
          <div>{{_byteFormat(row.receivedSize)}}/{{_byteFormat(row.totalSize)}}</div>
        </template>
        <template #progress="{ row }">
          <div class="progress">
            <div class="progress-l">
              <Progress :percent="parseFloat(((row.receivedSize/row.totalSize)*100).toFixed(2))" :stroke-width="6" :status="formatState(row.state).progress" />
            </div>
            <div class="progress-r">
              <span v-if="row.state == 'download'">{{ getSpeed(row) }}/S</span>
            </div>
          </div>
        </template>
        <template #state="{ row }">
          <div>
            <Tag :color="formatState(row.state).color">{{ formatState(row.state).text }}</Tag>
          </div>
        </template>
        <template #action="{ row }">
          <div class="btn-tips">
            <ui-btn-tip v-if="row.state == 'download'" content="暂停" icon="icon-pause2" class="mr-20 primary" @click.native="stop(row)"></ui-btn-tip>
            <ui-btn-tip v-if="row.state == 'stop'" content="继续下载" icon="icon-start" class="mr-20 primary" @click.native="continueD(row)"></ui-btn-tip>
            <ui-btn-tip v-if="row.state == 'error'" content="重新下载" icon="icon-reload" class="mr-20 primary" @click.native="reDownload(row)"></ui-btn-tip>
            <!-- <ui-btn-tip v-if="row.state == 'success'" content="打开文件夹" icon="icon-a-filemode" class="mr-20 primary" @click.native="openFolder(row)"></ui-btn-tip> -->
            <ui-btn-tip content="删除" icon="icon-shanchu" class="mr-20 primary" @click.native="delet(row)"></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <!-- 分页 -->
      <ui-page :current="params.pageNumber" :total="total" :page-size="params.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
    </div>
  </div>
</template>
<script>
import Search from './components/search'
import { mapGetters, mapActions } from 'vuex'
import { byteFormat } from '@/util/modules/common'
export default {
  components: { Search },
  props: {},
  data() {
    return {
      loading: false,
      pageForm: {
        //默认入参必要条件
        type: '',
        fileName: '',
        time: []
      },
      params: {
        pageNumber: 1,
        pageSize: 10
      },
      columns: [
        { title: '序号', width: 90, type: 'index', key: 'index' },
        { title: '文件名', key: 'fileName' },
        { title: '文件大小', slot: 'fileSize' },
        { title: '下载进度', slot: 'progress' },
        { title: '状态', slot: 'state' },
        { title: '下载请求时间', key: 'time' },
        { title: '操作', slot: 'action', width: 140 }
      ]
    }
  },
  computed: {
    ...mapGetters({
      downloadList: 'my-download/getDownloadList'
    }),
    tableList() {
      let list = [...this.downloadList]
      if (this.pageForm.type) {
        list = list.filter(v => v.state == this.pageForm.type)
      }
      if (this.pageForm.fileName) {
        list = list.filter(v => v.fileName.includes(this.pageForm.fileName))
      }
      if (this.pageForm.time && this.pageForm.time.length > 0) {
        list = list.filter(v => {
          return (this.pageForm.time[0] < new Date(v.time)) && (this.pageForm.time[1] > new Date(v.time))
        })
      }
      return list
    },
    total() {
      return this.tableList.length
    },
    tableListByPage() {
      return this.tableList.slice((this.params.pageNumber - 1) * this.params.pageSize, this.params.pageNumber * this.params.pageSize)
    }
  },
  methods: {
    ...mapActions({
      stopItem: 'my-download/stopItem',
      delItem: 'my-download/delItem',
      download: 'my-download/download'
    }),
    formatState(state) {
        let tip = '';
        switch (state) {
            case 'success':
                tip = {progress: 'success', text: '下载成功', color: 'success'}
                break;
            case 'download':
                tip = {progress: 'active', text: '下载中', color: 'primary'}
                break;
            case 'error':
                tip = {progress: 'wrong', text: '下载失败', color: 'error'}
                break;
            case 'stop':
                tip = {progress: 'normal', text: '暂停', color: 'warning'}
                break;
            default:
                tip = {progress: 'normal', text: '--', color: 'default'}
        }
        return tip;
    },
    _byteFormat(val) {
      return byteFormat(val)
    },
    getSpeed(item) {
      if (item.state == 'download' && item.downloadSpeed) {
        return byteFormat(item.downloadSpeed)
      } else {
        return byteFormat(0)
      }
    },
    // 查询
    searchForm(search) {
      const { type, fileName, time } = search
      this.pageForm = {
        type,
        fileName,
        time
      }
      this.params.pageNumber = 1
    },
    stop(item) {
      this.stopItem(item.id)
    },
    continueD(item) {
      this.download({url:item.url, id: item.id})
    },
    reDownload(item) {
      this.download({url:item.url, id: item.id, reDownload: true})
    },
    openFolder(item) {
      window.location.href = "chrome://downloads";
    },
    // 删除
    delet(item) {
      this.$Modal.confirm({
        title: '提示',
        closable: true,
        content: `确定删除吗？`,
        onOk: () => {
          this.delItem(item.id)
        }
      })
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size
      this.params.pageNumber = 1
    }
  }
}
</script>
<style lang="less" scoped>
.box {
  overflow: hidden;
}
.person {
  display: flex;
  flex: 1;
  height: 100%;
  width: 100%;
  // width: 1820px;
  flex-direction: column;
}
.progress {
  display: flex;
  align-items: center;
  .progress-l {
    flex: 1;
    margin-right: 10px;
    /deep/ .ivu-progress{
      font-size: inherit;
      display: flex;
      .ivu-progress-text {
        font-size: inherit;
      }
    }
  }
  .progress-r {
    width: 90px;
  }
}
</style>
