import magBg from '@/assets/img/map-bg.png';
const symbolNum = (num) => {
  return num.toLocaleString();
};
export const options = (mapData, queryAccessData) => {
  return new Promise((resolve, reject) => {
    try {
      let domImg = new Image();
      domImg.style.height = domImg.height = domImg.width = domImg.style.width = '8px';
      domImg.src = magBg;
      domImg.onload = () => {
        resolve({
          tooltip: {
            trigger: 'item',
            show: true,
            enterable: true,
          },
          geo: [
            {
              // center: [109.844902, 19.0392],
              // layoutCenter: ['51%', '53%'],
              // layoutSize: "600%",
              map: 'map',
              aspectScale: 1.2,
              top: 40,
              // layoutCenter: ['30%', '30%'],
              // layoutSize: 100,
              silent: true,
              roam: false,
              // z: 0,
              itemStyle: {
                areaColor: '#0E2F6B',
                shadowColor: 'rgba(0, 0, 0, 1)',
                shadowOffsetX: 0,
                shadowOffsetY: 15,
                shadowBlur: 15,
                borderColor: '#52C9FB',
                borderWidth: 2,
              },
            },
          ],
          series: [
            {
              // center: [109.844902, 19.0392],
              // layoutCenter: ['50%', '50%'],
              // layoutSize: "600%",
              top: 20,
              //layoutCenter: ['0%', '30%'],
              type: 'map',
              zoom: 0,
              roam: false,
              map: 'map',
              aspectScale: 1.2,
              tooltip: {
                show: true,
                borderColor: '#19D5F6',
                borderWidth: 1,
                backgroundColor: 'rgba(14, 47, 107, .75)',
                padding: 10,
                formatter: function (params) {
                  let item = mapData[params.dataIndex];
                  if (!item.data) {
                    item.data = {
                      deviceTotalAmount: 0,
                      faceViewAmount: 0,
                      vehicleViewAmount: 0,
                      zdrTrackAmount: 0,
                    };
                  }
                  return `<Tooltip always placement="top">
                      <div slot="content" class="base-text-color">
                        <p><i class="icon-font icon-dingwei mr-sm" style="color:#279EFA"></i>${item.name}</p>
                        ${queryAccessData
                          .map((qItem) => {
                            return `<p>${qItem.label}：<span class="mb-sm" style="color: ${qItem.color}">${symbolNum(
                              item.data[qItem.key] || 0,
                            )}</span></p>`;
                          })
                          .join('')}
                      </div>
                    </Tooltip>`;
                },
              },
              label: {
                show: true,
                color: '#fff',
                emphasis: {
                  textStyle: {
                    color: '#fff',
                  },
                },
              },
              itemStyle: {
                borderColor: '#B8FFFF',
                borderWidth: 1,
                areaColor: {
                  image: magBg,
                  repeat: 'repeat',
                },
                shadowColor: '#B8FFFF',
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowOffsetY: 1,
              },
              emphasis: {
                itemStyle: {
                  areaColor: {
                    // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
                    type: 'radial',
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                      {
                        offset: 0,
                        color: '#1F5D97', // 0% 处的颜色
                      },
                      {
                        offset: 1,
                        color: '#5BF5F7', // 100% 处的颜色
                      },
                    ],
                    global: false, // 缺省为 false
                  },
                  borderColor: '#2ab8ff',
                  borderWidth: 2,
                  shadowColor: 'rgba(11, 39, 100, 1)',
                  shadowOffsetX: 0,
                  shadowOffsetY: 15,
                  shadowBlur: 15,
                  label: {
                    show: false,
                  },
                },
              },
              data: mapData,
            },
            {
              // 波纹
              tooltip: {
                show: false,
              },
              zlevel: 999,
              type: 'effectScatter',
              coordinateSystem: 'geo',
              rippleEffect: {
                scale: 10,
                brushType: 'stroke',
              },
              showEffectOn: 'render',
              itemStyle: {
                shadowColor: '#1AFBD9',
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowOffsetY: 0,
                color: function () {
                  return '#1AFBD9';
                },
              },
              label: {
                color: 'green',
              },
              symbol: 'circle',
              symbolSize: [10, 5],
              symbolOffset: [0, -20],
              data: mapData,
            },
            {
              // 摄像头
              type: 'scatter',
              coordinateSystem: 'geo',
              itemStyle: {
                color: '#f00',
              },
              symbol: function (value, params) {
                return params.data.img;
              },
              tooltip: {
                show: false,
              },
              symbolSize: [24, 24],
              symbolOffset: [0, -60],
              data: mapData,
              zlevel: 999,
            },
            {
              // 柱子
              type: 'scatter',
              coordinateSystem: 'geo',
              itemStyle: {
                color: '#1AFBD9',
              },
              emphasis: {
                scale: false,
              },
              label: {
                show: false,
                emphasis: {
                  show: false,
                },
              },
              symbol: 'rect',
              tooltip: {
                show: false,
              },
              symbolSize: [2, 30],
              symbolOffset: [0, -35],
              data: mapData,
              zlevel: 998,
              silent: true,
            },
          ],
        });
      };
    } catch (err) {
      reject(err);
    }
  });
};
