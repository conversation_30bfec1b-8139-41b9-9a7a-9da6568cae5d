<template>
  <div class="child-tendency-chart-box">
    <div class="mt-sm grap-content">
      <div class="flex-row mb-md">
        <div class="flex-aic">
          <Checkbox v-model="isSortByScore" @on-change="onChangeSortScore" class="mr-md">按得分排序</Checkbox>
          <Checkbox v-model="isSortByRank" @on-change="onChangeSortRank">按名次排序</Checkbox>
        </div>
        <div class="flex-aic">
          <div class="flex-aic ml-md" v-for="(item, index) in filterList" :key="item.code">
            <div
              :ref="`${item.code}SelectRef`"
              class="color-block mr-sm"
              @click="clickExamClassification(item, index)"
              :style="{ background: echartsAttrs.colors[index % 12] }"
            ></div>
            <Poptip trigger="click" v-if="item && item.children_item" content="content" placement="bottom">
              <div class="base-text-color f-12 pointer">{{ item.name }}</div>
              <div slot="content" class="poptip-content">
                <div v-for="child in item.children_item" :key="child.code" class="poptip-content-row">
                  <Checkbox v-model="examItemFilter[child.code]" @on-change="setShowChartsData" size="small">{{
                    child.name
                  }}</Checkbox>
                </div>
              </div>
            </Poptip>
          </div>
        </div>
      </div>
      <div class="echarts-box relative">
        <div
          v-if="!bodyList?.length"
          v-ui-loading="{ loading: echartsLoading || changeLoading }"
          class="no-chart-box no-data"
        >
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
        <template v-else>
          <draw-echarts
            :echart-option="echartsAttrs.options"
            :echart-style="echartsAttrs.style"
            class="charts"
            :echarts-loading="echartsLoading || changeLoading"
            @echartClick="echartClick"
          ></draw-echarts>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue';
import childTendencyTooltip from '../../components/child-tendency-tooltip.vue';
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'childTendencyChart',
  props: {
    echartsLoading: {
      type: Boolean,
      default: false,
    },
    headerList: {
      type: Array,
      default: () => [],
    },
    bodyList: {
      type: Array,
      default: () => [],
    },
    getChildTendencyParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      isSortByScore: false, // 按得分排序
      isSortByRank: false, // 按名次排序
      // echarts配置
      echartsAttrs: {
        options: {},
        style: {
          width: '100%',
          height: '100%',
        },
        colors: [
          $var('--color-series-blue-1'),
          $var('--color-series-blue-2'),
          $var('--color-series-blue-3'),
          $var('--color-series-blue-4'),
          $var('--color-series-blue-5'),
          $var('--color-series-blue-6'),
          $var('--color-series-blue-7'),
          $var('--color-series-blue-8'),
          $var('--color-series-blue-9'),
          $var('--color-series-blue-10'),
          $var('--color-series-blue-11'),
          $var('--color-series-blue-12'),
        ],
      },
      tooltipFormatter: (data) => {
        let month = data[0].axisValue;
        const monthNumber = typeof month === 'string' && month.includes('月') ? month.replace('月', '') * 1 : month;
        let showBodyItem = {};
        this.showBodyList.forEach((item) => {
          if (item.month == monthNumber) {
            showBodyItem = item;
          }
        });
        let showKeys = [];
        this.headerList.forEach((item) => {
          if (!['month', 'rank', 'score'].includes(item.code)) {
            showKeys.push(item);
          }
        });
        let colors = this.echartsAttrs.colors;
        let taskTooltipShow = Vue.extend(childTendencyTooltip);
        let taskTooltipPointer = new taskTooltipShow({
          el: document.createElement('div'),
          data() {
            return {
              month,
              dataItem: showBodyItem,
              colors,
              showKeys,
            };
          },
        });
        return taskTooltipPointer.$el.outerHTML;
      },
      showBodyList: [],
      showBodyListByRank: [],
      showBodyListByScore: [],
      showExamineClassification: [], //考核分类，除去headerList中的月、排名、得分剩下的就是考核分类
      //图表过滤
      filterList: [], //图表过滤条件的渲染数组,由数组中每个对象渲染考核分类， 由每个对象的child_item数组渲染考核项复选框
      examClassificationFilter: {}, //考核分类过滤 => { 考核分类1: true, 考核分类2: true},
      examItemFilter: {}, // 考核项过滤 => {考核分类1中的考核项1: true,考核分类1中的考核项2: true,...,考核分类2中的考核项1: true}
      changeLoading: false,
      removeSchemeContentCodeList: [], //移除计算的考核内容
    };
  },
  watch: {
    bodyList: {
      handler(val) {
        this.isSortByScore = false;
        this.isSortByRank = false;
        this.showBodyList = this.$util.common.deepCopy(val);
        this.getShowExamineClassification(); // 获取考核分类项
        this.getSortShowBodyList(); // 根据ShowBodyList获取按得分和名词两个排序数据
        this.getEchartsOptions(); // 获取图表option
        this.getFilterBodyList(); //获取图表过滤的条件
      },
      deep: true,
    },
  },
  methods: {
    //获取考核分类
    getShowExamineClassification() {
      try {
        this.showExamineClassification = [];
        //除去headerList中的月、排名、得分剩下的就是考核方案。
        this.headerList.forEach((item) => {
          if (!['month', 'rank', 'score'].includes(item.code)) {
            this.showExamineClassification.push(item);
          }
        });
      } catch (err) {
        console.log(err);
        this.showExamineClassification = [];
      }
    },
    // 获取Eharts的series
    getEchartsSeries(paramBodyList) {
      // 根据考核方案生成对应的data数据
      let series = this.showExamineClassification.map((examineItem) => {
        return {
          name: examineItem.name || '-',
          type: 'bar',
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barWidth: '30px',
          showSymbol: false,
          label: {
            show: false,
            position: 'top',
          },
          data: paramBodyList.map((bodyItem) => {
            if (bodyItem[examineItem.code]) {
              return bodyItem[examineItem.code];
            } else {
              return 0; //有些月份没有考核项，将其置为0
            }
          }),
        };
      });
      //添加线形图
      series.push({
        name: '排名',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          normal: {
            color: $var('--color-green-7'), //拐点颜色
            lineStyle: {
              color: $var('--color-green-7'), //折线颜色
            },
          },
        },
        data: paramBodyList.map((bodyItem) => bodyItem.rank),
      });
      return series;
    },
    getEchartsOptions(showLegends) {
      //1. 判断显示的数据
      let chartBodyList = [];
      if (this.isSortByRank) {
        chartBodyList = this.showBodyListByRank;
      } else if (this.isSortByScore) {
        chartBodyList = this.showBodyListByScore;
      } else {
        chartBodyList = this.showBodyList;
      }
      //2. 根据bodyList列表对应月份
      let xAxisNames = [];
      chartBodyList.forEach((bodyItem) => {
        xAxisNames.push(bodyItem.month + '月');
      });
      //3. 根据bodyList获取series数据
      const series = this.getEchartsSeries(chartBodyList);
      //4. 设置legendSelected，控制需要显示哪些图例
      let legendSelected = {};
      showLegends
        ? (legendSelected = showLegends)
        : series.forEach((item) => {
            legendSelected[item.name] = true;
          });
      const opts = {
        xAxis: xAxisNames,
        tooltipFormatter: this.tooltipFormatter,
        series,
        colors: this.echartsAttrs.colors,
        legendSelected,
      };
      this.echartsAttrs.options = this.$util.doEcharts.examinationChildTendencyChart(opts);
    },
    onChangeSortScore() {
      this.isSortByRank = false;
      this.getEchartsOptions();
    },
    onChangeSortRank() {
      this.isSortByScore = false;
      this.getEchartsOptions();
    },
    echartClick(param) {
      this.$emit('echartClick', param);
    },
    //获取按排名排序、按成绩排序的列表
    getSortShowBodyList() {
      //按排名排序
      let listByRank = this.$util.common.deepCopy(this.showBodyList);
      listByRank.sort((a, b) => a.rank - b.rank);
      this.showBodyListByRank = listByRank;
      //按成绩排序
      let listByScore = this.$util.common.deepCopy(this.showBodyList);
      listByScore.sort((a, b) => b.score - a.score);
      this.showBodyListByScore = listByScore;
    },
    //获取自定义设置的图例legend筛选项
    getFilterBodyList() {
      // 1. 除去_score项就是所有的考核分类
      this.filterList = this.showExamineClassification.map((item) => {
        let examitemList = item.childList.filter((childItem) => !childItem.code.includes('_score'));
        return {
          ...item,
          children_item: examitemList,
        };
      });
      // 2. 将控制考核分类和考核项复选框的对象中的值置为true
      this.examClassificationFilter = {};
      this.examItemFilter = {};
      // 考核分类复选对象结构如右 => examClassificationFilter: { 考核分类1: true, 考核分类2: true},
      // 考核项复选对象结构如右 => examItemFilter: {考核分类1中的考核项1: true,考核分类1中的考核项2: true,...,考核分类2中的考核项1: true}
      this.filterList.forEach((item) => {
        this.examClassificationFilter[item.code] = true;
        item.children_item.forEach((child) => {
          this.examItemFilter[child.code] = true;
        });
      });
    },
    //点击考核分类
    async clickExamClassification(item, index) {
      // 1. 点击考核分类，将其对应的图例选中或取消选中，更新色块
      this.examClassificationFilter[item.code] = !this.examClassificationFilter[item.code];
      this.$refs[`${item.code}SelectRef`][0].style.background = this.examClassificationFilter[item.code]
        ? this.echartsAttrs.colors[index % 12]
        : '#999';
      // 2. 遍历考核分类的筛选项,重置legend
      let showLegendNames = {};
      Object.keys(this.examClassificationFilter).forEach((key) => {
        let headerExamObj = this.headerList.find((headerItem) => headerItem.code === key);
        showLegendNames[headerExamObj.name] = !!this.examClassificationFilter[key];
      });
      // 3. 更新图表
      this.getEchartsOptions(showLegendNames);
    },
    //切换考核项复选框，重新请求接口并加入removeSchemeContentCodeList参数，更新showBodyList
    async setShowChartsData() {
      try {
        this.changeLoading = true;
        // 1. 判断需要将哪些考核项排除
        let removeSchemeContentCodeList = [];
        Object.keys(this.examItemFilter).forEach((item) => {
          if (!this.examItemFilter[item]) {
            removeSchemeContentCodeList.push(item);
          }
        });
        // 2. 加入排除的考核项参数后，重新请求接口
        let params = { ...this.getChildTendencyParams, removeSchemeContentCodeList };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getExamChildScoreTrend, params);
        // 3. 更新数据、排序数据、图表
        this.showBodyList = data.bodyList;
        this.getSortShowBodyList();
        this.getEchartsOptions();
      } catch (err) {
        console.log(err);
      } finally {
        this.changeLoading = false;
      }
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.child-tendency-chart-box {
  height: 100%;
  .color-block {
    width: 24px;
    height: 14px;
    border-radius: 4px;
    cursor: pointer;
  }
  .poptip-content {
    .poptip-content-row ~ .poptip-content-row {
      margin-top: 10px;
    }
  }
  .echarts-box {
    height: 350px;
    .no-chart-box {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
