const operationList = [
  {
    key: 'online',
    label: '在线状态',
    placeholder: '请选择在线状态',
    data: [
      { label: '在线', value: '1' },
      { label: '离线', value: '2' },
      { label: '无法检测', value: '3' },
    ],
  },
  {
    key: 'normal',
    label: '完好状态',
    placeholder: '请选择完好状态',
    data: [
      { label: '取流及时响应', value: '1' },
      { label: '取流超时响应', value: '2' },
      { label: '无法检测', value: '3' },
    ],
  },
  {
    key: 'canPlay',
    label: '可用状态',
    placeholder: '请选择可用状态',
    data: [
      { label: '取流成功', value: '1' },
      { label: '取流失败', value: '2' },
      { label: '无法检测', value: '3' },
    ],
  },
  {
    key: 'qualified',
    label: '检测结果',
    placeholder: '请选择检测结果',
    data: [
      { label: '合格', value: '1' },
      { label: '不合格', value: '2' },
      { label: '无法检测', value: '3' },
    ],
  },
];
const tableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '设备编码', key: 'deviceId', align: 'center', minWidth: 170, tooltip: true },
  { title: '设备名称', key: 'deviceName', align: 'center', minWidth: 150, tooltip: true },
  { title: '组织机构', key: 'orgName', align: 'center', minWidth: 100, tooltip: true },
  { title: '设备状态', key: 'phyStatusText', align: 'center', minWidth: 80, tooltip: true },
  {
    title: '在线状态',
    slot: 'online',
    align: 'center',
    minWidth: 80,
    tooltip: true,
    renderHeader: (h) => {
      return h('div', [
        h('span', '在线状态'),
        h(
          'Tooltip',
          {
            props: {
              transfer: true,
              placement: 'bottom',
            },
            style: { verticalAlign: 'middle' },
          },
          [
            h('i', {
              class: 'icon-font icon-wenhao ml-xs f-12',
              style: {
                width: '13px',
                verticalAlign: 'top',
                color: 'var(--color-warning)',
              },
            }),
            h(
              'span',
              {
                slot: 'content',
              },
              '设备在国标平台为在线状态',
            ),
          ],
        ),
      ]);
    },
  },
  {
    title: '完好状态',
    slot: 'normal',
    align: 'center',
    minWidth: 80,
    tooltip: true,
    renderHeader: (h) => {
      return h('div', [
        h('span', '完好状态'),
        h(
          'Tooltip',
          {
            props: {
              transfer: true,
              placement: 'bottom',
            },
            style: { verticalAlign: 'middle' },
          },
          [
            h('i', {
              class: 'icon-font icon-wenhao ml-xs f-12',
              style: {
                width: '13px',
                verticalAlign: 'top',
                color: 'var(--color-warning)',
              },
            }),
            h(
              'span',
              {
                slot: 'content',
              },
              '设备在线，拉流请求有响应',
            ),
          ],
        ),
      ]);
    },
  },
  {
    title: '可用状态',
    slot: 'canPlay',
    align: 'center',
    minWidth: 80,
    tooltip: true,
    renderHeader: (h) => {
      return h('div', [
        h('span', '可用状态'),
        h(
          'Tooltip',
          {
            props: {
              transfer: true,
              placement: 'bottom',
            },
            style: { verticalAlign: 'middle' },
          },
          [
            h('i', {
              class: 'icon-font icon-wenhao ml-xs f-12',
              style: {
                width: '13px',
                verticalAlign: 'top',
                color: 'var(--color-warning)',
              },
            }),
            h(
              'span',
              {
                slot: 'content',
              },
              '成功接收到实时视频流',
            ),
          ],
        ),
      ]);
    },
  },
  { title: '信令时延（毫秒)', key: 'delaySipMillSecond', align: 'center', minWidth: 70, tooltip: true },
  { title: '码流时延（毫秒)', key: 'delayStreamMillSecond', align: 'center', minWidth: 70, tooltip: true },
  { title: '关键帧时延（毫秒)', key: 'delayIdrMillSecond', align: 'center', minWidth: 80, tooltip: true },
  {
    title: '检测结果',
    slot: 'qualified',
    align: 'center',
    minWidth: 80,
    tooltip: true,
    renderHeader: (h) => {
      return h('div', [
        h('span', '检测结果'),
        h(
          'Tooltip',
          {
            props: {
              transfer: true,
              placement: 'bottom',
            },
            style: { verticalAlign: 'middle' },
          },
          [
            h('i', {
              class: 'icon-font icon-wenhao ml-xs f-12',
              style: {
                width: '13px',
                verticalAlign: 'top',
                color: 'var(--color-warning)',
              },
            }),
            h(
              'span',
              {
                slot: 'content',
                style: { whiteSpace: 'normal', wordBreak: 'normal', maxWidth: '300px' },
              },
              '设备的【在线状态/完好状态/可用状态】作为指标计算结果',
            ),
          ],
        ),
      ]);
    },
  },
  { title: '检测时间', key: 'startTime', align: 'center', minWidth: 150, tooltip: true },
  {
    minWidth: 150,
    title: '设备标签',
    slot: 'tagNames',
  },
  { title: '操作', slot: 'option', align: 'center', minWidth: 100, tooltip: true, fixed: 'right' },
];
const reporRateTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '设备编码', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  { title: '设备名称', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '所属单位', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '行政区划', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '经度', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '纬度', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: 'MAC地址', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: 'IPv4地址', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '摄像机功能类型', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '监控点位类型', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '摄像机采集区域', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '设备状态', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '数据来源', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '检测时间', key: 'deviceName', align: 'center', minWidth: 100, tooltip: true },
  { title: '操作', slot: 'option', align: 'center', minWidth: 100, tooltip: true, fixed: 'right' },
];
const reasonTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '不合格原因', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  { title: '异常类型', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  { title: '检测项', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  { title: '检测规则', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  { title: '实际结果', key: 'deviceId', align: 'center', minWidth: 100, tooltip: true },
  // { title: '解决方案', slot: 'option', align: 'center', minWidth: 50, tooltip: true, btnText: '查看', btnIcon: 'icon-chakanxiangqing' },
];

// 视频监控资产匹配率
const matchRateStaticList = [
  {
    name: '检测行政区划数量',
    key: 'total',
    total: 0,
    icon: 'icon-jiancehangzhengquhuashuliang-01',
    liBg: '#094060',
    iconBgColor: '#1b4c6a',
    iconColor: 'linear-gradient(180deg, #1BAFD5 0%, #0A9F90 100%)',
    textColor: '#19D5F6',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '视频监控资产匹配率达标数量',
    key: 'qualifiedNum',
    qualifiedNum: 0,
    icon: 'icon-shipinjiankongzichanpipeishuaidabiaoshuliang-01',
    liBg: '#0c4544',
    iconBgColor: '#19514c',
    iconColor: 'linear-gradient(180deg, #26D82C 0%, #127D0A 100%)',
    textColor: '#22C326',
    type: 'number', // number数字 percentage 百分比
  },
  {
    name: '视频监控资产匹配率不达标数量',
    key: 'unQualifiedNum',
    unQualifiedNum: 0,
    icon: 'icon-shipinjiankongzichanpipeishuaibudabiaoshuliang-01',
    liBg: '#302a46',
    iconBgColor: '#3f354e',
    iconColor: 'linear-gradient(180deg, #F24E2C 0%, #772C0A 100%)',
    textColor: '#DD4826',
    type: 'number', // number数字 percentage 百分比
  },
];
const matchRateTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '行政区划/组织机构', slot: 'orgRegionName', align: 'center', minWidth: 170, tooltip: true },
  { title: '联网共享平台视频监控数量', key: 'platformCount', align: 'center', minWidth: 170, tooltip: true },
  { title: '资产库视频监控数量', key: 'assetCount', align: 'center', minWidth: 170, tooltip: true },
  { title: '视频监控资产匹配率', slot: 'resultValueFormat', align: 'center', minWidth: 170, tooltip: true },
  { title: '达标情况', slot: 'qualified', align: 'center', minWidth: 170, tooltip: true },
];

// 视频监控在线率提升
const onlineTableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  {
    title: '所在市县',
    key: 'orgRegionText',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '区划类型',
    key: 'regionTypeText',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '视频监控建设总量',
    key: 'total',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '检测数量',
    key: 'detectCount',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '在线数量',
    slot: 'onLineCount',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '离线数量',
    slot: 'offLineCount',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '本年度历史最高在线数',
    key: 'maxCountOfYear',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '在线率提升',
    key: 'onLineLiftRate',
    align: 'center',
    minWidth: 100,
  },
];
export {
  operationList,
  tableColumns,
  reporRateTableColumns,
  reasonTableColumns,
  matchRateStaticList,
  matchRateTableColumns,
  onlineTableColumns,
};
