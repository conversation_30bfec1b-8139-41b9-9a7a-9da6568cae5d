<!--
    * @FileDescription: 
    * @Author: H
    * @Date: 2024/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="leftBox">
    <searchBox
      ref="searchBox"
      @seleArea="seleArea"
      @searchList="querySearch"
      @packBox="handlePackup"
    ></searchBox>
    <div
      class="object-information"
      :style="{ height: `calc( 100% - ${top + 10}px )` }"
      v-if="showlist"
    >
      <div class="title">
        <p>{{ title }}</p>
        <Icon type="ios-close" @click="handleCancel" />
      </div>
      <div class="box-modal">
        <ul
          class="box-content"
          :class="{ 'overflow-box': objectMsgList.length > 3 }"
          v-infinite-scroll="load"
          infinite-scroll-distance="1"
        >
          <li
            class="box-list"
            :class="{ 'active-box-list': objectIndex == index }"
            v-for="(item, index) in objectMsgList"
            :key="index"
          >
            <div class="content-top">
              <div class="content-top-img">
                <template v-if="item.photos && item.photos.length > 0">
                  <img-list :dataObj="item" :index="index"></img-list>
                </template>
                <ui-image v-else viewer :src="item.photoUrl" />
                <div v-if="item.score" class="similarity">
                  <span>{{ getScore(item.score) }}%</span>
                </div>
              </div>
              <div class="content-top-right">
                <span class="ellipsis">
                  <ui-icon type="camera" :size="14"></ui-icon>
                  <span class="orange" :class="{ block: !item.plateNo }">{{
                    item.plateNo || "--"
                  }}</span>
                </span>
                <span class="ellipsis">
                  <ui-icon type="xingming" :size="14"></ui-icon>
                  <span class="block">{{ item.ownerName || "--" }}</span>
                </span>
              </div>
            </div>
            <div class="content-bottom">
              <div class="iconList">
                <!-- <opera-floor iconSec="icon-dangan2"></opera-floor> -->
              </div>
              <div
                class="analyseIcon"
                :class="{ activeAnaly: objectIndex === index }"
                @click="handleAnalyse($event, item, index)"
              >
                <ui-icon type="duoren" :size="14"></ui-icon>
                <span>频次分析</span>
              </div>
            </div>
          </li>
          <ui-empty
            v-if="objectMsgList.length === 0 && loading == false"
          ></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
          <p class="loading" v-if="loadingText">加载中...</p>
          <p class="endlist" v-if="noMore && objectMsgList.length !== 0">
            没有更多了
          </p>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { queryVehicleList } from "@/api/modelMarket";
import searchBox from "./searchBox.vue";
import operaFloor from "../../../components/operat-floor/index.vue";
export default {
  name: "",
  components: {
    searchBox,
    operaFloor,
  },
  props: {
    title: {
      type: String,
      default: "对象信息",
    },
  },
  data() {
    return {
      objectMsgList: [],
      loading: false,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      objectIndex: -1,
      loadingText: false,
      noMore: false,
      total: 0,
      searchInfo: {},
      typaTag: {},
      showlist: false,
      top: 0,
      searchParams: {},
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {
    this.objHeight();
  },
  methods: {
    // 高度
    objHeight() {
      setTimeout(() => {
        this.top = document.querySelector(".search_box").scrollHeight + 20;
      }, 210);
    },
    //
    handlePackup() {
      this.objHeight();
    },
    querySearch(item) {
      this.showlist = true;
      this.objHeight();
      this.$emit("reset");
      this.init(item);
    },
    seleArea() {
      this.$emit("seleArea");
    },
    init(item, isFirst = true) {
      if (isFirst) {
        this.objectMsgList = [];
        this.loading = true;
        this.page.pageNumber = 1;
      } else {
        this.loadingText = true;
      }
      this.noMore = false;
      let params = {};
      this.searchInfo = item;
      params = {
        plateNo: item.plateNo,
        dateType: item.dateType,
        vehicleBrand: item.vehicleBrand,
        deivceList: item.deivceList,
        endDate: item.endDate,
        startDate: item.startDate,
        vehicleColor: item.vehicleColor,
        plateColor: item.plateColor,
        vehicleType: item.vehicleType,
      };
      this.searchParams = params;
      queryVehicleList({ ...params, ...this.page })
        .then((res) => {
          if (res.data) {
            let list = (res.data && res.data.entities) || [];
            this.objectMsgList = this.objectMsgList.concat(...list);
            this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
          this.loadingText = false;
          this.noMore = false;
        });
    },
    // 频次分析
    handleAnalyse(event, item, index) {
      let params = {
        plateNo: item.plateNo,
        plateColor: item.plateColor,
        vehicleColor: item.vehicleColor,
        vehicleBrand: [item.vehicleBrandCN],
        vehicleType: item.vehicleType,
      };
      this.objectIndex = index;
      this.$emit("cutAnalyse", { ...this.searchParams, ...params });
    },
    // 加载
    load() {
      let totalPage = Math.ceil(this.total / this.page.pageSize);
      if (this.total <= 10) {
        return;
      }
      if (this.page.pageNumber >= totalPage) {
        this.noMore = true;
        return;
      } else {
        this.noMore = false;
        this.page.pageNumber = this.page.pageNumber + 1;
        this.init(this.searchInfo, false);
      }
    },
    // 关闭
    handleCancel() {
      this.showlist = false;
      this.$emit("reset");
    },
    // 反显设备
    showDevice(list) {
      list.map((item) => {
        item.select = true;
      });
      this.$refs.searchBox.selectData(list);
    },
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },
  },
};
</script>

<style lang='less' scoped>
@import "./style/index";
@import "../../../style/boxContent";
.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: calc(~"100% - 20px");
}
</style>
