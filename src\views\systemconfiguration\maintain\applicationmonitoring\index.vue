<template>
  <div class="applicationmonitoring auto-fill">
    <div class="search-content">
      <ui-label class="inline" label="系统名称">
        <Input v-model="searchData.whdw" class="width-md" placeholder="请输入系统名称"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="系统类型">
        <Select class="width-md" v-model="searchData.type" placeholder="请选择系统类型">
          <Option v-for="(item, index) in typeList" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" label="统计时间">
        <DatePicker
          class="width-md"
          v-model="searchData.startModifyTime"
          type="datetime"
          placeholder="请选择开始时间"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startModifyTime')"
          :options="startTimeOption"
          confirm
        />
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="width-md"
          v-model="searchData.endModifyTime"
          type="datetime"
          placeholder="请选择结束时间"
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endModifyTime')"
          :options="endTimeOption"
          confirm
        />
      </ui-label>
      <div class="ml-lg inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: 'rgb(67, 140, 255)', 'font-size': '12px' }"
              icon="icon-chakan"
              content="查看"
              @click.native="view(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </div>
</template>
<script>
export default {
  name: 'applicationmonitoring',
  props: {},
  data() {
    return {
      loading: false,
      typeList: [],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endModifyTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startModifyTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      searchData: {
        type: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          title: '应用名称',
          minWidth: 200,
        },
        {
          title: '应用类型',
        },
        {
          title: '当前状态',
          slot: 'phyStatus',
          tooltip: true,
        },
        {
          title: '离线次数',
          width: 80,
        },
        {
          title: '离线时长',
        },
        {
          title: '在线率',
          width: 80,
        },
        {
          title: '更新时间',
        },
        {
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.search();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.applicationmonitoring {
  background: var(--bg-content);
  .search-content {
    padding: 20px;
  }
  .table-module {
    padding: 0 20px;
  }
}
</style>
