<!--
 * @Date: 2025-01-13 18:35:00
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-05 15:45:08
 * @FilePath: \icbd-view\src\views\juvenile\index.vue
 * @description: 未成年人专题首页
-->
<template>
  <div class="juvenile-home-page">
    <div class="juvenile-left-box">
      <ui-card
        title="今日事件统计分析"
        padding="10,0"
        class="analysis-card flex-1 m-b10 h-520"
      >
        <ul class="analysis-list-box">
          <li
            class="analysis-item"
            v-for="(item, index) in analysisList"
            :key="index"
          >
            <img :src="item.imgUrl" :alt="item.title" />
            <p class="count-num">
              <count-to
                :start-val="0"
                :end-val="item.num"
                :duration="1000"
                class="h1"
              ></count-to>
            </p>
            <p class="data-name">{{ item.title }}</p>
          </li>
        </ul>
      </ui-card>
      <ui-card
        title="前科人员发现报警"
        padding="20,0"
        class="criminal-record-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="extra-left">
            <span
              >报警总数：<span class="primary-color">{{
                alarmTotal
              }}</span></span
            >
          </div>
          <div class="extra-right">
            <span class="more" @click="handleMore(1)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <AlarmTab
          ref="alarmTab"
          :list="alarmList"
          :loading="alarmListLoading"
        ></AlarmTab>
      </ui-card>
      <ui-card title="活跃人员分析" padding="13,10" class="active-card flex-1">
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left">
            <ul class="tabs-ui">
              <li>
                <div
                  class="tab-item"
                  :class="activeAnalysisSelect == 2 ? 'active-item' : ''"
                  @click="() => (activeAnalysisSelect = 2)"
                >
                  前科人员
                </div>
              </li>
              <li>
                <div
                  class="tab-item"
                  :class="activeAnalysisSelect == 1 ? 'active-item' : ''"
                  @click="() => (activeAnalysisSelect = 1)"
                >
                  非前科人员
                </div>
              </li>
            </ul>
          </div>
          <div class="extra-right">
            <span class="more" @click="handleMore(6)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <div class="active-table">
          <ui-table
            ref="tableRef"
            :columns="columns"
            :data="tabList"
            :loading="tableLoading"
            class="table-content table-small-padding"
            @scroll.stop
          >
          </ui-table>
        </div>
      </ui-card>
    </div>
    <div class="juvenile-main-box">
      <div class="map-box">
        <!-- <div class="title">
          <img src="@/assets/img/home/<USER>" alt="" srcset="" />
          <h2>未成年人保护专题</h2>
        </div> -->
        <juvenile-map></juvenile-map>
      </div>
      <ui-card
        title="未成年人前科人员专题库"
        padding="0,20"
        class="main-ui-card"
      >
        <div class="center-image">
          <div class="center-text" @click="goToLibFace">
            <div class="num">{{ allRecordNum }}</div>
            <div class="title">未成年人前科人员库</div>
          </div>
          <div
            class="six-start-box"
            v-for="(item, index) in criminalRecord"
            :class="item.class"
            :key="index"
            v-show="item.show"
            @click="goToLibFace"
          >
            <div class="num">{{ item.num }}</div>
            <div class="title">{{ item.title }}</div>
          </div>
        </div>
      </ui-card>
    </div>
    <div class="juvenile-right-box">
      <ui-card title="频繁出没娱乐场所" :padding="5" class="flex-1 m-b10">
        <div slot="extra" class="card-extra">
          <div class="entertainment extra-left">
            <Menu
              mode="horizontal"
              theme="light"
              :active-name="selectEntertain"
              @on-select="(vale) => (selectEntertain = vale)"
            >
              <MenuItem
                v-for="(item, index) in showEntertainmentList"
                :key="index"
                :name="item.key"
              >
                {{ item.title }}
              </MenuItem>
            </Menu>
            <div class="more-list" v-if="moreEntertainmentList.length > 0">
              <Icon
                :type="showEnterMore ? 'ios-arrow-down' : 'ios-arrow-up'"
                class="icon"
                @click="showEnterMore = !showEnterMore"
              ></Icon>
              <div class="more-list-menu" v-if="showEnterMore">
                <Menu
                  mode="horizontal"
                  theme="light"
                  :active-name="selectEntertain"
                  @on-select="(vale) => (selectEntertain = vale)"
                >
                  <MenuItem
                    v-for="(item, index) in moreEntertainmentList"
                    :key="index"
                    :name="item.key"
                  >
                    {{ item.title }}
                  </MenuItem>
                </Menu>
              </div>
            </div>
          </div>
          <div class="extra-right">
            <span class="more" @click="handleMore(2)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <FrequentTab
          ref="FrequentTab"
          :list="frequentList"
          :loading="frequentListLoading"
        ></FrequentTab>
      </ui-card>
      <ui-card title="上学时间校外出现" :padding="0" class="flex-1 m-b10">
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(3)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <SchoolOutTab
          ref="frequentTab"
          :list="schoolOutList"
          :loading="schoolOutListLoading"
        ></SchoolOutTab>
      </ui-card>
      <ui-card
        title="深夜时间出行"
        padding="10,10,0,10"
        class="darktime-card flex-1 m-b10"
      >
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(4)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <NightOutTab
          :list="nightOutList"
          :loading="nightOutListLoading"
        ></NightOutTab>
      </ui-card>
      <ui-card
        title="与违法前科人员同行"
        :padding="0"
        class="together-card flex-1"
      >
        <div slot="extra" class="card-extra">
          <div class="active-analysis extra-left"></div>
          <div class="extra-right">
            <span class="more" @click="handleMore(5)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <TogetherTab
          ref="togetherTab"
          :list="recordWithList"
          :loading="recordWithListLoading"
        ></TogetherTab>
      </ui-card>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import CountTo from "vue-count-to";
import AlarmTab from "./components/alarm-tab.vue";
import FrequentTab from "./components/frequent-tab.vue";
import SchoolOutTab from "./components/school-out-tab.vue";
import TogetherTab from "./components/together-tab.vue";
import NightOutTab from "./components/night-out.vue";
import JuvenileMap from "./components/juvenile-map";
import UiImage from "@/components/ui-image.vue";
import { MenuItem } from "view-design";
import {
  appearInRecreationPersonCount,
  compareAlarmCount,
  personBizLabelStatistics,
  travelAlongPersonCount,
  getOverviewPageList,
  getConfigPlaceSecondLevels,
  getAppearInRecreationPageList,
  queryJuvenileAlarmDetail,
  travelAlongDetailPageList,
  getFaceAlarmPageList,
} from "@/api/monographic/juvenile.js";

export default {
  name: "juvenile",
  components: {
    CountTo,
    AlarmTab,
    FrequentTab,
    SchoolOutTab,
    TogetherTab,
    NightOutTab,
    JuvenileMap,
    MenuItem,
  },
  data() {
    return {
      analysisList: [
        {
          imgUrl: require("@/assets/img/juvenile/qianke.png"),
          num: 0,
          title: "前科人员发现报警",
          request: compareAlarmCount,
        },
        {
          imgUrl: require("@/assets/img/juvenile/pingfan.png"),
          num: 0,
          title: "频繁出没娱乐场所",
          request: appearInRecreationPersonCount,
        },
        {
          imgUrl: require("@/assets/img/juvenile/tongxing.png"),
          num: 0,
          title: "同行人有违法前科",
          request: travelAlongPersonCount,
        },
      ],
      activeAnalysisSelect: 2,
      alarmTotal: 0,
      // type: "index",
      columns: [
        {
          title: "排名",
          key: "sort",
          align: "center",
          width: "40",
          render: (h, { index }) => {
            if (index > 2) {
              return h("div", index + 1);
            }
            let richList = ["one", "two", "three"];
            let image = require(`@/assets/img/rich-${richList[index]}.png`);
            if (index <= 2) {
              return h(
                "div",
                {
                  style: {
                    width: "20px",
                    height: "20px",
                    margin: "0 auto",
                  },
                },
                [
                  h(UiImage, {
                    props: {
                      alt: index,
                      src: image,
                      viewer: false,
                    },
                  }),
                ]
              );
            }
          },
        },
        {
          title: "姓名",
          key: "name",
          align: "center",
          width: "80",
          ellipsis: true,
        },
        { title: "年龄", key: "age", align: "center", width: "60" },
        {
          title: "活跃度",
          key: "activeScore",
          align: "center",
          width: "80",
          render: (h, { row, index }) => {
            let colorArr = ["#F29F4C", "#2C86F8"];
            if (index < 3) {
              return h(
                "div",
                { style: { color: colorArr[0], fontWeight: "bold" } },
                row.activeScore
              );
            } else {
              return h(
                "div",
                { style: { color: colorArr[1], fontWeight: "bold" } },
                row.activeScore
              );
            }
          },
        },
        {
          title: "最后一次出现时间",
          key: "lastActiveTime",
          align: "center",
          tooltip: "light",
        },
      ],
      criminalRecord: [
        { num: "0", title: "", class: "position-one", show: false },
        { num: "0", title: "", class: "position-two", show: false },
        { num: "0", title: "", class: "position-three", show: false },
        { num: "0", title: "", class: "position-four", show: false },
        { num: "0", title: "", class: "position-five", show: false },
        { num: "0", title: "", class: "position-six", show: false },
      ],
      allRecordNum: 0,
      entertainmentList: [],
      showEnterMore: false,
      selectEntertain: "",
      tabList: [],
      alarmList: [],
      alarmListLoading: false,
      tableLoading: false,
      frequentList: [],
      frequentListLoading: false,
      schoolOutList: [],
      schoolOutListLoading: false,
      nightOutList: [],
      nightOutListLoading: false,
      recordWithList: [],
      recordWithListLoading: false,
      alarmConfigInfo: {},
    };
  },
  computed: {
    ...mapGetters({
      placeSecondLevelList: "dictionary/getPlaceSecondLevelList", // 场所二级分类
      targetObj: "systemParam/targetObj",
    }),
    // 展示前四个
    showEntertainmentList() {
      return this.entertainmentList.slice(0, 4);
    },
    // 后四个放到更多
    moreEntertainmentList() {
      return this.entertainmentList.slice(4);
    },
  },
  async created() {
    await this.getDictData();
    this.getSecondPlaceList();
  },
  mounted() {
    this.getAlarmList();
    this.getNowDateStatistics();
    this.getRecordStatistics();
    this.getActivePeople();
    this.getSchoolOutList();
    this.getNightOutList();
    this.getRecordWithList();
  },
  watch: {
    // 活跃人员前科类型更新
    activeAnalysisSelect: {
      handler(val) {
        this.getActivePeople();
      },
    },
    // 频繁娱乐场所菜单更新
    selectEntertain: {
      handler(val) {
        this.getFrequentList();
      },
    },
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    getNowDateStatistics() {
      const date = new Date();
      let param = {
        startDate: date.format("yyyy-MM-dd") + " 00:00:00",
        endDate: date.format("yyyy-MM-dd") + " 23:59:59",
      };
      for (let i = 0; i < 3; i++) {
        try {
          this.analysisList[i].request(param).then(({ data }) => {
            this.analysisList[i].num = data || 0;
          });
        } catch (error) {}
      }
    },
    getRecordStatistics() {
      personBizLabelStatistics().then(({ data }) => {
        // 不足就能展示几个就几个，超过就取top6
        if (!data) {
          data = [];
        }
        this.allRecordNum = data.count;
        delete data.count;
        if (Object.keys(data).length <= 6) {
          Object.keys(data).forEach((key, index) => {
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
          });
        } else {
          this.allRecordNum = 0;
          Object.keys(data).forEach((key, index) => {
            if (index > 5) {
              return;
            }
            this.criminalRecord[index].title = key;
            this.criminalRecord[index].num = data[key];
            this.criminalRecord[index].show = true;
            this.allRecordNum += data[key];
          });
        }
      });
    },
    getAlarmList() {
      this.alarmListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
      };
      getFaceAlarmPageList(param)
        .then(({ data }) => {
          let arr = data.entities || [];
          arr.forEach((item) => {
            let info = this.targetObj.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
          this.alarmList = arr;
          this.alarmTotal = data.total || 0;
        })
        .finally(() => {
          this.alarmListLoading = false;
        });
    },
    getActivePeople() {
      this.tableLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 10,
        orderType: 1,
        libBizType: this.activeAnalysisSelect,
      };
      getOverviewPageList(param)
        .then(({ data }) => {
          this.tabList = data.entities || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    async getSecondPlaceList() {
      const { data } = await getConfigPlaceSecondLevels();
      let arr = [];
      data.forEach((item) => {
        let val = {
          key: item.typeCode,
          title: item.typeName,
        };
        arr.push(val);
      });
      this.entertainmentList = arr;
      this.$nextTick(() => {
        this.selectEntertain = arr[0].key;
      });
    },
    getFrequentList() {
      this.frequentListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
        placeSecondLevelCodes: [this.selectEntertain],
      };
      getAppearInRecreationPageList(param)
        .then(({ data }) => {
          this.frequentList = data.entities || [];
        })
        .finally(() => {
          this.frequentListLoading = false;
        });
    },
    getSchoolOutList() {
      this.schoolOutListLoading = true;
      let param = {
        bizAlarmType: 2,
        topN: 3,
      };
      queryJuvenileAlarmDetail(param)
        .then(({ data }) => {
          this.schoolOutList = data || [];
        })
        .finally(() => {
          this.schoolOutListLoading = false;
        });
    },
    getNightOutList() {
      this.nightOutListLoading = true;
      let param = {
        bizAlarmType: 3,
        topN: 3,
      };
      queryJuvenileAlarmDetail(param)
        .then(({ data }) => {
          this.nightOutList = data || [];
        })
        .finally(() => {
          this.nightOutListLoading = false;
        });
    },
    getRecordWithList() {
      this.recordWithListLoading = true;
      let param = {
        pageNumber: 1,
        pageSize: 3,
      };
      travelAlongDetailPageList(param)
        .then(({ data }) => {
          this.recordWithList = data.entities || [];
        })
        .finally(() => {
          this.recordWithListLoading = false;
        });
    },
    // 更多跳转到目标管控
    handleMore(value) {
      this.$router.push({
        name: "juvenile-alarm-manager",
        query: {
          // 直接跳到人员报警
          compareType: value,
        },
      });
    },
    // 跳转人脸档案
    goToLibFace() {
      this.$router.push({
        name: "special-library",
        query: {},
      });
    },
  },
};
</script>

<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}

.h-520 {
  height: 520px;
}

.flex-1 {
  flex: 1;
}

.primary-color {
  color: #2c86f8;
}

.juvenile-home-page {
  display: flex;
  flex: 1;
  background: #dde1ea;
  position: relative;

  /deep/ .card-head .title {
    text-wrap: nowrap;
  }

  .card-extra {
    display: flex;
    color: #515a6e;
    justify-content: space-between;
    font-size: 14px;
    height: 30px;
    line-height: 30px;

    .more {
      margin-right: 6px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.35);
      cursor: pointer;
    }

    .extra-left {
      width: 200px;
    }

    .entertainment {
      width: unset;
      flex: 1;
      padding-right: 15px;
      display: flex;

      /deep/ .ivu-menu-horizontal {
        height: 100%;
        line-height: unset;
        display: flex;
        gap: 5px;
      }

      /deep/ .ivu-menu {
        &::after {
          background: none;
        }

        .ivu-menu-item {
          padding: 0;
          text-wrap: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          width: 40px;
        }
      }

      .more-list {
        position: relative;

        .icon {
          cursor: pointer;
        }

        .more-list-menu {
          position: absolute;
          box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
          border-radius: 4px;
          width: 80px;

          /deep/ .ivu-menu {
            flex-direction: column;
            width: 100%;
            padding: 5px;

            .ivu-menu-item {
              width: 100%;
            }
          }
        }
      }
    }

    .extra-right {
      margin-right: 10px;
      width: 50px;
      text-wrap: nowrap;
      flex: 1;
    }

    .active-analysis {
      width: 150px;
      margin-right: 40px;

      .tabs-ui {
        display: flex;
        justify-content: space-between;
        font-weight: bold;

        .tab-item {
          color: #515a6e;
          cursor: pointer;
        }

        .active-item {
          color: #2c86f8;
          border-bottom: 2px solid #2c86f8;
        }
      }
    }
  }

  .juvenile-left-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-right: 10px;

    .analysis-card {
      flex: 0.75;
    }

    .analysis-list-box {
      display: flex;
      justify-content: space-around;
      width: 100%;
      height: 100%;
      align-items: center;

      .analysis-item {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        img {
          width: 90px;
          height: 90px;
        }

        .count-num {
          font-size: 20px;
          font-weight: bold;
          color: #2c86f8;
          margin: 5px 0;
        }
      }
    }

    .criminal-record-card {
      .criminal-record-extra {
        display: flex;
        color: black;
        width: 40%;
        justify-content: space-between;
      }
    }

    .active-card {
      .active-table {
        width: 100%;
        height: 100%;
        /deep/ .ui-table {
          height: 100%;
        }
      }
      .table-content {
        /deep/ .ivu-table-header {
          border-radius: 4px 4px 4px 4px;

          .ivu-table-cell {
            padding-left: 0;
            padding-right: 0;
            text-wrap: nowrap;
            text-overflow: ellipsis;
          }
        }

        /deep/ .ivu-table-body {
          td {
            padding: 6px 0px !important;
            height: 20px;
            line-height: 20px;

            .ivu-table-cell {
              padding-left: 0;
              padding-right: 0;
            }
          }
        }
      }

      .table-small-padding {
        /deep/ th {
          padding: 5px 0 6px 0 !important;
        }
      }
    }
  }

  .juvenile-main-box {
    flex: 1;
    height: 100%;
    display: flex;
    flex-direction: column;

    .map-box {
      width: 1000px;
      height: calc(~"100% - 280px");
      position: relative;

      .title {
        text-align: center;
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 50;

        h2 {
          margin-top: -42px;
        }
      }
    }

    .main-ui-card {
      height: 280px;
      background-image: url("../../assets/img/juvenile/juvenile-bg.png");
      background-repeat: no-repeat;
      background-size: contain;
      background-position: 100% 70%;

      .center-image {
        width: 100%;
        height: 100%;
        background-image: url("../../assets/img/juvenile/juvenile-center-bg.png");
        background-repeat: no-repeat;
        background-size: contain;
        background-position: center;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;

        .center-text {
          position: absolute;
          width: 12%;
          top: 20.7%;
          text-align: center;
          color: #fff;
          cursor: pointer;
          .num {
            font-size: 24px;
            font-weight: bold;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
          }
        }

        .six-start-box {
          width: 108px;
          height: 108px;
          clip-path: polygon(
            50% 0%,
            100% 25%,
            100% 75%,
            50% 100%,
            0% 75%,
            0% 25%
          );
          background-image: url("../../assets/img/juvenile/six-ploy-bg.png");
          background-repeat: no-repeat;
          background-size: contain;
          background-position: center;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          .num {
            font-size: 24px;
            font-weight: bold;
            color: #2c86f8;
          }

          .title {
            font-size: 14px;
            margin-top: 5px;
            color: rgba(0, 0, 0, 0.6);
          }
        }

        .position-one {
          position: absolute;
          top: 40px;
          left: 62px;
        }

        .position-two {
          position: absolute;
          top: 0;
          left: 220px;
        }

        .position-three {
          position: absolute;
          top: 130px;
          left: 180px;
        }

        .position-four {
          position: absolute;
          top: 0;
          left: 652px;
        }

        .position-five {
          position: absolute;
          top: 130px;
          left: 692px;
        }

        .position-six {
          position: absolute;
          top: 40px;
          left: 810px;
        }
      }
    }
  }

  .juvenile-right-box {
    width: 450px;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-left: 10px;

    .together-card {
      // height: 280px;
      flex: 1.25;
    }

    .darktime-card {
    }
  }
}
/deep/.ui-card {
  .card-content {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
</style>
