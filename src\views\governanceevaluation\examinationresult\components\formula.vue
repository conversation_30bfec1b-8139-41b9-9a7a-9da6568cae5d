<template>
  <ui-modal v-model="visible" :title="title" :width="30" footerHide :styles="styles">
    <div class="base-text-color">{{ formulaData.examFormula }}</div>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    title: {},
    formulaData: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        height: '1rem',
      },
    };
  },
  created() {},
  methods: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-header {
  padding: 0 !important;
}
@{_deep}.ivu-modal-body {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
