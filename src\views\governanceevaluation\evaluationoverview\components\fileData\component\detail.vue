<template>
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="list auto-fill">
      <line-title title-name="检测结果统计"></line-title>
      <chartsContainer :abnormalCount="abnormalCount" />
      <line-title title-name="检测结果详情"></line-title>
      <Table class="table" :columns="tableColumns" :data="tableData">
        <template #apiIndex="{ row }">
          {{ row.apiIndex == 1 ? '以图搜图' : '车牌检索' }}
        </template>
        <template #apiSum="{ row }">
          {{ row.apiSuccess + row.apiFail }}
        </template>
        <template #apiRate="{ row }">
          {{ !!row.apiRate ? row.apiRate + '%' : 0 }}
        </template>
      </Table>
      <loading v-if="loading"></loading>
    </div>
    <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import expandRow from './table-expand.vue';
export default {
  components: {
    lineTitle: require('@/components/line-title').default,
    chartsContainer: require('./chartsContainer').default,
  },
  props: {
    detail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableColumns: [
        {
          type: 'expand',
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                row: params.row,
              },
            });
          },
        },
        { title: '接口名称', slot: 'apiIndex', tooltip: true, minWidth: 150 },
        { title: '调用次数', slot: 'apiSum', tooltip: true, minWidth: 150 },
        { title: '调用成功次数', key: 'apiSuccess', tooltip: true, minWidth: 150 },
        { title: '调用失败次数', key: 'apiFail', tooltip: true, minWidth: 150 },
        { title: '接口稳定性', slot: 'apiRate', tooltip: true, minWidth: 150 },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: false,
      loading: false,
      bigPictureShow: false,
      interface: {},
      abnormalCount: [
        {
          title: '检测接口数量',
          key: 'numInterfaces',
          numInterfaces: 0,
          // count: '0',
          icon: 'icon-jiancejiekoushuliang-01',
        },
        {
          title: '检测接口总次数',
          key: 'Alltime',
          Alltime: 0,
          // count: '0',
          icon: 'icon-tiaoyongjiekouzongcishu-01',
        },
        {
          title: '调用成功次数',
          key: 'apiSuccess',
          apiSuccess: 0,
          // count: '0',
          icon: 'icon-tiaoyongchenggongcishu-01',
        },
        {
          title: '调用失败次数',
          key: 'apiFail',
          apiFail: 0,
          // countKey: '0',
          icon: 'icon-tiaoyongshibaicishu-01',
        },
        {
          title: '接口稳定性',
          key: 'apiRate',
          apiRate: 0,
          type: 'percentage',
          // count: '0',
          icon: 'icon-tiaoyongchenggongcishu-01',
        },
      ],
    };
  },
  methods: {
    info(row) {
      this.interface = row;
      this.abnormalCount = this.abnormalCount.map((item) => {
        if (item.key === 'numInterfaces') {
          item[item.key] = row.apiIndexNew.length;
        } else {
          item[item.key] = row[item.key];
        }
        return item;
      });
      this.init();
    },
    // 列表
    async init() {
      this.visible = true;
      let data = {
        batchId: this.detail.batchId,
        indexId: this.detail.indexId,
        pageSize: this.searchData.pageSize,
        pageNumber: this.searchData.pageNum,
        apiIndex: this.interface.apiIndex,
        orgCode: this.interface.orgCode,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.orgCodePageList, data);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.apiStatus == 200) {
            i.apiStatus = '成功';
          } else {
            i.apiStatus = '失败';
          }
          i.apiDuration = (i.apiDuration % (1000 * 60)) / 1000 + ' 秒 ';
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
      // this.getDetailCount()
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    // async getDetailCount() {
    //   let data = {
    //     batchId: this.detail.batchId,
    //     indexId: this.detail.indexId,
    //     pageSize: this.searchData.pageSize,
    //     pageNumber: this.searchData.pageNum,
    //     apiIndex: this.interface.apiIndex,
    //     orgCode: this.interface.orgCode,
    //   }
    //   let res = await this.$http.post(governanceevaluation.getDetailCount, data)
    //   var obj = res.data
    //   this.abnormalCount[0].count = obj.numInterfaces || 0
    //   this.abnormalCount[1].count = obj.zong || 0
    //   this.abnormalCount[2].count = obj.zong - obj.apiFail || 0
    //   this.abnormalCount[3].count = obj.apiFail || 0
    //   this.abnormalCount[4].count = this.interface.apiRate
    // },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.init();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .list {
    position: relative;
    margin-top: 10px;
    height: 750px;
    overflow-y: auto;

    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
    }
  }
}

.table {
  margin-top: 16px;
}

/deep/ .ivu-table {
  overflow-y: auto;
}
</style>
