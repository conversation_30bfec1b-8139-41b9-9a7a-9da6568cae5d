<template>
  <ui-modal v-model="captureVisible" title="抓拍详情" :footerHide="true" width="89%">
    <Row>
      <Col span="4">
        <div class="contentLeft" v-if="personObj">
          <div class="capture-details-left-sculpture">
            <ui-image :src="personObj.facePath" />
          </div>
          <div
            v-for="(item, index) in captureInfo"
            :key="index"
            style="white-space: nowrap"
            class="ellipsis"
            :title="personObj[item.value] || '未知'"
          >
            <span class="lable-left" style="white-space: nowrap">{{ item.name }}</span
            >{{ personObj[item.value] || '未知' }}
          </div>
          <tags-more
            v-if="personObj.personType"
            :tagList="personObj.personType"
            :defaultTags="4"
            placement="left-start"
            bgColor="#2D435F"
          ></tags-more>
        </div>
      </Col>
      <Col span="20" style="overflow: hidden">
        <div class="content">
          <div class="capture-details-right-top" v-if="staticsList.length">
            <label v-for="(item, index) in staticsList" :key="index"
              >{{ item.label }}：<span>{{ item.count }}</span></label
            >
          </div>
          <slot name="searchList"></slot>
          <div v-ui-loading="{ loading: loading, tableData: tableData }">
            <div :style="styleScroll6">
              <div class="carItem" v-for="item in tableData" :key="item.id">
                <div class="item">
                  <div class="img" @click="viewBigPic(item.facePath)">
                    <ui-image :src="item.facePath" />
                    <div class="percentage" v-if="item.similarity">
                      {{ similarityVal(item.similarity) }}
                    </div>
                    <i class="icon-font icon-cunyi" v-if="item.judgmentResult !== 1"></i>
                    <p class="shadow-box" style="z-index: 11" title="准确性存疑原因" v-if="item.judgmentResult !== 1">
                      <i
                        class="icon-font icon-yichang search-icon mr-xs base-text-color"
                        @click.stop="captureDetail(item)"
                      ></i>
                    </p>
                  </div>
                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据'}`">
                      <i class="icon-font icon-shijian"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ item.shotTime }}</span>
                    </p>
                    <p :title="item.address">
                      <i class="icon-font icon-dizhi"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                        item.address ? item.address : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <ui-page
              class="page menu-content-background"
              :page-data="pageData"
              @changePage="changePage"
              @changePageSize="changePageSize"
            ></ui-page>
          </div>
        </div>
        <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
      </Col>
    </Row>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    resultId: {},
    // 筛选条件
    defaultSearchData: {
      type: Object,
      default() {
        return {
          trackType: 3,
        };
      },
    },
    captureInfo: {
      type: Array,
      default() {
        return [];
      },
    },
    // 接口名称
    interFaceName: {
      default: governanceevaluation.personAccuracyDetails,
    },
    staticsList: {
      type: Array,
      default() {
        return [
          {
            label: '抓拍总量',
            count: 0,
            filedName: 'urlNum',
          },
          {
            label: '准确性存疑数量',
            count: 0,
            filedName: 'abnormalTrajectoryNum',
          },
        ];
      },
    },
  },
  data() {
    return {
      captureVisible: false,
      tableData: [],
      searchData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '660px',
        'overflow-y': 'scroll',
      },
      bigPictureShow: false,
      imgList: [],
      personObj: {}, // 人员信息对象
      loading: false,
    };
  },
  async created() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    show(item) {
      this.personObj = item;
      this.pageData.pageNum = 1;
      this.infoList();
      this.staticsList.forEach((one) => {
        if (this.personObj[one.filedName]) {
          one.count = this.personObj[one.filedName];
        }
        if (one.label === 'urlNotAvailableAmount') {
          one.count = this.personObj.smallUrlNotAvailableAmount + this.personObj.largeUrlNotAvailableAmount;
        }
      });
      this.captureVisible = true;
    },
    async infoList() {
      this.loading = true;
      let params = Object.assign(this.searchData, this.defaultSearchData);
      params.pageNumber = this.pageData.pageNum;
      params.pageSize = this.pageData.pageSize;
      params.personLibId = this.personObj.personLibId;
      params.batchId = this.personObj.batchId;
      let res = await this.$http.post(this.interFaceName, Object.assign(params));
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    viewBigPic(item) {
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    captureDetail(item) {
      this.$emit('detail', item, this.personObj);
    },
    // 分页
    changePage(val) {
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.infoList();
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
    },
    // 检索，父组件调用
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.infoList();
    },
    similarityVal(val) {
      return val.toFixed(2) + '%';
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      !val ? this.reset() : null;
      this.captureVisible = val;
    },
  },
  components: {
    // trajectoryModal: require('../component/trajectory-modal').default,
    // uploadModal: require('../component/upload-modal').default,
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('@/components/tags-more').default,
  },
};
</script>

<style lang="less" scoped>
.contentLeft {
  color: #fff;
  padding: 0 30px;
  div {
    margin-top: 12px;
  }

  img {
    width: 100%;
  }

  ul {
    li {
      float: left;
      padding: 6px 10px;
      background: #1a447b;
      border-radius: 4px;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
  .lable-left {
    color: #8797ac;
  }
}
.content {
  height: 600px;
  color: #fff;
}

.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  display: inline-block;
  // max-width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: #0f2f59;
    .num {
      position: absolute;
      right: 0;
      z-index: 100;
      padding: 10px;
      border-radius: 5px;
      background: rgba(42, 95, 175, 0.6);
    }
    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      z-index: 10;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
      color: #8797ac;
    }

    .icon-URLbukefangwen1 {
      color: #bc3c19;
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }
  }
}
/deep/ .ivu-row {
  align-content: start;
}
.capture-details-left-sculpture {
  height: 210px;
  margin-bottom: 27px;
}
.capture-details-right-top {
  padding: 20px 0;
  font-size: 14px;
  color: #ffffff;
  label {
    margin-right: 58px;
    span {
      color: var(--color-bluish-green-text);
    }
  }
}
.onlys {
  width: 80%;
}
.percentage {
  // width: 32px;
  height: 18px;
  padding: 0 4px;
  line-height: 18px;
  background: #ea800f;
  color: #fff;
  text-align: center;
  position: absolute;
  top: 12px;
  left: -2px;
  z-index: 11;
}
.icon-cunyi {
  color: #bc3c19;
  font-size: 48px;
  position: absolute;
  bottom: -11px;
  right: 0;
  z-index: 11;
}
</style>
