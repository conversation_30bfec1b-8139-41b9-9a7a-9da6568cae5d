<template>
  <div class="face-container auto-fill">
    <div class="search-container">
      <div class="jump">
        <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
      </div>
      <div class="search">
        <ui-label label="设备类型" :width="70" class="inline ml-lg">
          <Select class="width-sm" v-model="formValidate.isImportantDevice" clearable placeholder="请选择设备类型">
            <Option :value="1" label="普通标签"></Option>
            <Option :value="2" label="重点标签"></Option>
          </Select>
        </ui-label>
        <Button type="primary" class="ml-lg" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetForm">重置</Button>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :stripe="false"
      :loading="loading"
    >
      <template #orgCode="{ row }">
        <span v-if="currentOrgCode == row.orgCode">{{ row.orgCodeName }}</span>
        <span class="span-btn" v-else type="text" @click="onClickOrg(row.orgCode, row.orgCodeName, 'orgCode')">{{
          row.orgCodeName
        }}</span>
      </template>
    </ui-table>
  </div>
</template>

<script>
import UiBreadcrumb from '../components/ui-breadcrumb';
import inspectionrecord from '@/config/api/inspectionrecord';
import user from '@/config/api/user';

export default {
  name: 'testing-overview',
  components: {
    UiBreadcrumb,
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    taskObj: {},
  },
  data() {
    return {
      tableColumns: [{ slot: 'orgCode' }],
      tableData: [],
      hasLast: false,
      formValidate: {
        isImportantDevice: '',
      },
      dictData: {},
      loading: false,
      abnormalCount: [
        { title: '检测设备数量', icon: 'icon-exceptionlibrary' },
        { title: '单设备检测图像数量', icon: 'icon-exceptionlibrary' },
        { title: '检测图像总数量', icon: 'icon-exceptionlibrary' },
      ],
      currentOrgCode: '',
      breadcrumbData: [],
    };
  },
  methods: {
    indexResults() {
      if (this.taskObj.indexResults && this.taskObj.indexResults.length) {
        return this.taskObj.indexResults[0];
      }
      return {};
    },
    onClickOrg(code, orgName, name) {
      this.currentOrgCode = code;
      this.breadcrumbData.push({ id: code, add: orgName });
      this.init(code, name);
    },
    search() {
      this.init();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleChange(val) {
      this.currentOrgCode = val.id;
      this.init();
    },
    async init(code, name) {
      try {
        this.loading = true;
        const indexResultData = this.taskObj.indexResults.map((item) => {
          let obj = {};
          obj.batchId = item.batchId;
          obj.indexId = item.indexId;
          obj.taskIndexId = item.taskIndexId;
          return obj;
        });
        let params = {
          evaluationRecordTypeId: this.$parent.currentTree.id,
          taskSchemeId: this.taskObj.taskSchemeId,
          indexResults: indexResultData,
          sbdwlx: this.formValidate.isImportantDevice,
        };
        params[name] = code;
        let {
          data: { data },
        } = await this.$http.post(inspectionrecord.getEvaluationFaceOverviewV2, params);
        this.tableData = data.details || [];
        this.tableColumns = data.headers || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    resetForm() {
      this.formValidate = {
        isImportantDevice: '',
      };
      this.search();
    },
    resetBreadcrumb() {
      this.breadcrumbData = [];
      this.currentOrgCode = '';
      this.tableData = [];
    },
  },
  mounted() {
    this.getDictData();
  },
  computed: {},
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function () {
        let { regionCode, regionName } = this.indexResults();
        this.resetBreadcrumb();
        this.onClickOrg(regionCode, regionName, 'regionCode');
      },
    },
  },
};
</script>

<style lang="less" scoped>
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.search-container {
  display: flex;
  justify-content: space-between;
  margin: 10px 0 10px 0;
  .jump {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.ui-table {
  /deep/ .header-table {
    box-shadow: inset 1px -1px 0 0 var(--border-table);
  }
  @{_deep} td {
    background: var(--bg-content);
  }
  @{_deep} th {
    .ivu-table-cell {
      color: #8797ac;
    }
  }
  @{_deep} .ivu-table-wrapper {
    border-top: 1px solid var(--border-table);
  }
  @{_deep} .ivu-table-tip {
    overflow-x: auto;
  }
}
.span-btn {
  cursor: pointer;
  color: var(--color-primary);
}
.face-container {
  margin-bottom: 10px;
}
</style>
