<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt=''/><span class="title-text">标签产生趋势</span>
    </div>
    <div class="echart-wrap">
      <line-echart v-if="labelProduceObj.names.length" :smooth="true" :echart-style="echartStyle" :names="labelProduceObj.names" :values="labelProduceObj.values"/>
    </div>
  </ui-card>
</template>
<script>
  import LineEchart from './echarts/line-echart.vue'
  export default {
    components: {
      LineEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        echartStyle: {
          lineColor: '#E99E53',
          lineWidht: 2,
          startColor: 'rgba(255, 177, 91, 0.3)',
          endColor: 'rgba(213, 66, 15, 0)',
          axisLineColor: '#07355E',
          yAxisLineType: 'dashed',
          yAxisLineColor: '#07355E',
          xAxisAxisTick: false,
          showSymbol: false
        },
        labelProduceObj: {
          names: [],
          values: []
        }
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelProduceObj = this.updataLabelProduce(this.statisticsList)
        },
        immediate: true
      }
    },
    created () {
      this.labelProduceObj = {
        names: [],
        values: []
      }
    },
    methods: {
      /**
       * 标签产生趋势
      */
      updataLabelProduce (list) {
        return {
          names: list.map(item => {
            return item.month + '月'
          }),
          values: list.map(item => {
            return item.count
          })
        }
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
  }
</style>