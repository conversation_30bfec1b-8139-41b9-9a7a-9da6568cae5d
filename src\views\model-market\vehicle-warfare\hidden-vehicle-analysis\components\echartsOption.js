export default {
    backgroundColor: "#fff",
    grid: {
      top: 15,
      left: 56,
      right: 36,
      bottom: 60
    },
    legend: {
      data: [],
      selectedMode: true,
      textStyle: {
        color: "#666"
      },
      itemGap: 20,
      top: 16,
      right: 30,
      itemHeight: 10,
      itemWidth: 20
    },
    axisPointer: {
      label: {
        textStyle: {
          color: "#FF3379"
        },
        backgroundColor: "rgba(0,0,0,0)"
      }
    },
    tooltip: {
      trigger: "axis",
      formatter: (v)=>{
        return `${v[0].seriesName}: ${v[0].value}`
      }
    },
    yAxis: {
      type: "value",
      position: "top",
      minInterval: 2,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        textStyle: {
          color: "#ccc"
        }
      },
      splitNumber: 2,
      splitLine: {
        lineStyle: {
          type: "dashed",
          color: "#999"
        }
      },
      axisPointer: {
        show: true,
        type: "line",
        snap: true,
        triggerTooltip: false,
        lineStyle: {
          color: "#FFB8D0",
          type: "dashed"
        },
        label:{
          precision: 0
        }
      }
    },
    xAxis: {
      type: "category",
      axisLine: {
        show: true,
        lineStyle: {
          color: "#adadad"
        }
      },
      axisLabel: {
        interval: "auto",
        textStyle: {}
      },
      axisTick: {
        show: false
      },
      axisPointer: {
        show: true,
        snap: true,
        type: "line",
        lineStyle: {
          color: "#FF9C10"
        },
        shadowStyle: {
          color: {
            type: "",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(255,156,16,.15)"
              },
              {
                offset: "0.9788888888888888888",
                color: "rgba(255,156,16,.15)"
              },
              {
                offset: "0.9788888888888888889",
                color: "rgba(255,156,16,1)"
              },
              {
                offset: 1,
                color: "rgba(255,156,16,1)"
              }
            ]
          },
          z: 0
        }
      },
      data: []
    },
    dataZoom: [
      {
        left: 56,
        right: 36,
        bottom: 20,
        height: 4,
        showDataShadow: false,
        borderColor: "transparent",
        backgroundColor: "#e2e2e2",
        handleSize: 15,
        handleStyle: {
          color: "#ffffff",
          shadowBlur: 6,
          shadowOffsetX: 1,
          shadowOffsetY: 2,
          shadowColor: "#ffffff"
        },
        showDetail: 0,
        fillerColor: "#ffffff"
      },
      {
        type: "inside",
        zoomOnMouseWheel: "shift"
      }
    ],
    series: [
      {
        name: "频次",
        type: "line",
        symbol: "circle",
        showSymbol: false,
        smooth: true,
        lineStyle: {
          normal: {
            width: 3,
            shadowBlur: 10,
            shadowColor: "rgba(17,118,241, 0.4)"
          }
        },
        itemStyle: {
          normal: {
            color: "#FF3379"
          }
        },
        data: []
      }
    ]
  }