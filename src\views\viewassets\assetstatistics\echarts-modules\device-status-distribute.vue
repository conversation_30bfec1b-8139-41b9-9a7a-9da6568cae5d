<template>
  <div class="function-distribute">
    <tab-title v-model="searchData.dataKey" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <!-- <drop-select v-model="searchData.sbdwlxList" multiple :data="dropData"
          @on-change="handleChangeSelect"></drop-select> -->
        <drop-checkbox @on-change="handleChangeSelect"></drop-checkbox>
      </template>
    </tab-title>
    <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <draw-echarts
        class="charts"
        :echart-option="propertyEchart"
        :echart-style="echartStyle"
        ref="chartRef"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <next-chart-icon
        v-if="echartList.length > comprehensiveConfig.homeNum"
        @scrollRight="scrollRight('chartRef', echartList, [], comprehensiveConfig.homeNum)"
      ></next-chart-icon>
    </div>
  </div>
</template>

<script>
import assetstatisticsMultipleBar from '@/views/viewassets/assetstatistics/utils/echarts-config-bar.js';
import equipmentassets from '@/config/api/equipmentassets';
import dataZoom from '@/mixins/data-zoom';
import { mapGetters } from 'vuex';

export default {
  name: 'pie-chart',
  mixins: [dataZoom],
  components: {
    NextChartIcon: require('../components/next-chart-icon.vue').default,
    DropCheckbox: require('../components/drop-checkbox.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    TabTitle: require('../components/tab-title.vue').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
  },
  data() {
    return {
      searchData: {
        dataKey: 'phystatus_overview_list',
      },
      dropData: [
        { label: '一类点', id: '1' },
        { label: '二三类点', id: '2' },
        { label: '内部监控', id: '4' },
      ],
      activeValue: '1',
      tabData: [
        { label: '设备状态区域分布', id: 'phystatus_overview_list', legendData: ['可用', '不可用'] },
        { label: '重点类型区域分布', id: 'important_overview_list', legendData: ['重点设备', '普通设备'] },
        { label: '资产质量区域统计', id: 'checkstatus_overview_list', legendData: ['合格', '不合格'] },
      ],
      legendData: ['可用', '不可用'],
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      echartList: [],
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    activeTab: {
      async handler() {
        this.postQueryStatisticsByAmount();
      },
    },
    homeConfig: {
      async handler(val) {
        if (Object.keys(val).length > 0) {
          this.postQueryStatisticsByAmount();
        }
      },
      deep: true,
    },
  },
  methods: {
    onChangeTitle(val) {
      let one = this.tabData.find((item) => item.id === val);
      this.legendData = one.legendData;
      this.postQueryStatisticsByAmount();
    },
    handleChangeSelect(val) {
      // 单独处理功能类型，单选也要传数组
      Object.assign(this.searchData, val);
      this.postQueryStatisticsByAmount();
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('chartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    /**
     *     SBCJQY_TYPE_CODE("sbcjqy_type_code","采集类型区域分布")
     *     ,AMOUNT_CHANGE_TREND("amount_change_trend","数量变化趋势")
     *     ,PHYSTATUS_OVERVIEW_LIST("phystatus_overview_list","设备状态统计区域分布")
     *     ,IMPORTANT_OVERVIEW_LIST("important_overview_list","重点类型统计区域分布")
     *     ,CHECKSTATUS_OVERVIEW_LIST("checkstatus_overview_list","资产质量统计区域分布")
     */
    async postQueryStatisticsByAmount() {
      try {
        this.echartsLoading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryStatisticsByCommonCondition, params);
        this.echartList = data || [];
        this.handleQualityCharts();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    /**
     * {
     *   "civilCode": "320100",
     *   "civilCodeName": "南京市",
     *   "amount": 15594,
     *   "unAmount": 8881
     * }
     */
    handleQualityCharts() {
      let data = {
        // color: ['#24B3E1', '#1F8DE2', '#2565F9'],
        titleText: '设备数量',
        legendData: this.legendData,
        xAxisData: this.echartList.map((item) => item.civilCodeName),
        seriesData: [
          {
            name: this.legendData[0],
            stack: '使用情况',
            barWidth: 18,
            itemStyle: {
              normal: { color: $var('--color-green-11') },
            },
            data: this.echartList.map((item) => item.amount),
          },
          {
            name: this.legendData[1],
            stack: '使用情况',
            barWidth: 18,
            data: this.echartList.map((item) => item.unAmount),
            itemStyle: {
              normal: { color: $var('--color-orange-7') },
            },
          },
        ],
      };
      this.propertyEchart = assetstatisticsMultipleBar(data);
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.function-distribute {
  width: 100%;
  height: 320px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .body-container {
    width: 100%;
    position: relative;
    height: calc(100% - 30px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
