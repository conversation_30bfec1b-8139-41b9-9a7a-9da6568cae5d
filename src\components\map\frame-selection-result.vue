<template>
  <div class="select-result-dom">
    <section class="dom-content" v-if="!!Object.keys(selectPointsList).length">
      <div class="dom-content-search">
        <Select v-model="myfilter.type" @on-change="filterHandle">
          <Option :value="'All'">全部</Option>
          <template v-for="(value, key) in deviceTypeMap">
            <Option
              :value="key"
              :key="key"
              v-if="selectPointsList[key].list.length"
              >{{ value.name }}</Option
            >
          </template>
        </Select>
        <Input
          v-model.trim="myfilter.value"
          :maxlength="20"
          placeholder="请输入"
          style="width: auto"
        >
          <Icon type="ios-search" slot="suffix" @click="filterHandle" />
        </Input>
        <Button class="ml-10" type="primary" @click="handleExport">导出</Button>
      </div>
      <div class="layerList" v-if="!!Object.keys(selectPointsList).length">
        <template v-for="(e, key) in selectPointsList">
          <div
            class="dom-content-type"
            :key="key"
            v-if="
              selectPointsList[key].list.length &&
              !selectPointsList[key].show &&
              selectPointsList[key].isLayerShow
            "
          >
            <div class="header" @click.stop="switchHandle(key)">
              <ui-icon
                :class="selectPointsList[key].isShow ? 'arrowrun' : 'arrow'"
                type="caret-right"
                color="#187AE4"
              ></ui-icon>
              <div class="device-type">
                <ui-icon
                  :type="deviceTypeMap[key].icon"
                  :color="deviceTypeMap[key].color"
                ></ui-icon>
                <span>{{ deviceTypeMap[key].name }}</span>
                <span
                  v-if="selectPointsList[key].list.length"
                  class="type-total"
                  >({{
                    selectPointsList[key].list.filter((e) => !e.show).length
                  }})</span
                >
              </div>
              <ui-icon
                type="eye"
                title="隐藏"
                v-if="
                  layerCheckedNames.includes(selectPointsList[key].layerType)
                "
                @click.native.stop="
                  layerChange('close', selectPointsList[key].layerType)
                "
              ></ui-icon>
              <ui-icon
                type="eye-close"
                v-else
                title="显示"
                @click.native.stop="
                  layerChange('open', selectPointsList[key].layerType)
                "
              ></ui-icon>
            </div>
            <div class="dom-content-list" slot="content" v-if="e.isShow">
              <template v-for="(item, i) in e.list">
                <div
                  :key="i"
                  :class="item.Status == '1' ? 'offline' : ''"
                  @click.stop="deviceClick(item)"
                  @dblclick.stop="deviceDblClick(item)"
                  v-if="!item.show"
                >
                  <ui-icon
                    :type="deviceTypeMap[key].icon"
                    :color="deviceTypeMap[key].color"
                  ></ui-icon>
                  <p :title="item.deviceName">{{ item.deviceName }}</p>
                  <!-- <ui-icon
                    title="查看档案"
                    type="eye"
                    @click.native.stop="checkDeviceDoc(item)"
                  ></ui-icon> -->
                  <ui-icon
                    title="复制名称"
                    type="ETL-guanlian"
                    @click.native.stop="copyText(item.deviceName)"
                  ></ui-icon>
                  <ui-icon
                    title="删除"
                    type="shanchu"
                    @click.native.stop="delItem(e.list, i)"
                  ></ui-icon>
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <div class="action-wrapper" v-if="listShow">
        <Button class="grouping" type="primary" @click="handleGrouping"
          >添加到自定义分组</Button
        >
      </div>
    </section>
    <Modal v-model="groupingBox" title="添加到我的分组" center :width="460">
      <div class="content">
        <div class="content-header">
          <div class="grouping-tip" v-if="inputShow" @click="handleAdd">
            <Icon type="md-add" />
            <span>添加到新的分组</span>
          </div>
          <div class="grouping-input" v-else>
            <Input
              v-model.trim="groupName"
              :maxlength="20"
              placeholder="请输入新分组名称"
              style="width: 100%"
            >
              <Icon
                type="md-checkmark"
                title="确定"
                slot="suffix"
                @click="handleAddName"
              />
              <Icon
                type="md-close"
                title="取消"
                slot="suffix"
                @click="handleCancel"
              />
            </Input>
          </div>
        </div>
        <div class="content-box">
          <RadioGroup v-model="selectGroup">
            <Radio
              :label="item.id"
              v-for="(item, index) in groupList"
              :key="index"
              :disabled="item.permission != 'write'"
              >{{ item.groupName }}</Radio
            >
          </RadioGroup>
          <ui-empty v-if="groupList.length === 0 && !loading"></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
        </div>
      </div>
      <div slot="footer">
        <Button class="btn cancel" type="default" @click="onCancel"
          >取消</Button
        >
        <Button
          :loading="submitLoading"
          type="primary"
          class="btn ok"
          @click="onSubmit"
        >
          {{ submitLoading ? "提交中" : "确定" }}
        </Button>
      </div>
    </Modal>
  </div>
</template>
<script>
import * as XLSX from "xlsx";
import uiIcon from "../ui-icon.vue";
import { queryMyVideoGroupList, addMyVideoGroup } from "@/api/player";
import { batchSave } from "@/api/operationsOnTheMap";
import { mapGetters } from "vuex";
import { copyText } from "@/util/modules/common";
export default {
  components: { uiIcon },
  props: {
    selectDeviceList: {
      type: Array,
      default: () => [],
    },
    listShow: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    selectDeviceList: {
      handler(val) {
        const { layerTypeList, layerCheckedNames, crashType } =
          this.$parent.$parent;
        // 组装框选数据分组
        let selectPointsMap = {};
        layerTypeList.forEach((e) => {
          selectPointsMap[e] = {
            isLayerShow: this.listShow
              ? layerCheckedNames.includes(e)
                ? true
                : false
              : true,
            isShow: true,
            list: [],
          };
        });
        val.forEach((e) => {
          if (this.listShow) {
            // 选中了图层才加入到列表中
            if (layerCheckedNames.includes(e.LayerType)) {
              selectPointsMap[e.LayerType].list.push(e);
              selectPointsMap[e.LayerType].layerType = e.LayerType;
            }
          } else {
            // 布控框选范围
            if (crashType[e.LayerType]) {
              selectPointsMap[e.LayerType].list.push(e);
              selectPointsMap[e.LayerType].layerType = e.LayerType;
            }
          }
        });
        this.myfilter = {
          type: "All", // 下拉框选中项
          value: "", // 过滤设备名称
        };
        this.selectPointsList = {
          ...selectPointsMap,
        };
      },
      // immediate: true
    },
  },
  computed: {
    layerCheckedNames() {
      const { layerCheckedNames } = this.$parent.$parent;
      return layerCheckedNames;
    },
    // 计算总总数
    selectedTotal() {
      const { selectPointsList } = this;
      let length = null;
      for (let key in selectPointsList) {
        length +=
          selectPointsList[key].list && selectPointsList[key].list.length;
      }
      return length;
    },
    newSelectDeviceList() {
      let list = [];
      Object.values(this.selectPointsList).forEach((v) => {
        if (v.isLayerShow) list = [...list, ...v.list];
      });
      return list;
    },
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },

  data() {
    return {
      selectPointsList: {}, //框选设备
      deviceTypeMap: {
        // Camera: { name: '高清视频', icon: 'shebeizichan', color: '#187AE4' },
        Camera_QiuJi: { name: "球机", icon: "qiuji", color: "#187AE4" },
        Camera_QiangJi: {
          name: "枪机",
          icon: "shebeizichan",
          color: "#187AE4",
        },
        Camera_Vehicle: { name: "车辆卡口", icon: "qiche", color: "#1faf8a" },
        Camera_Face: { name: "人脸卡口", icon: "yonghu", color: "#48BAFF" },
        Camera_RFID: { name: "RFID设备", icon: "RFID", color: "#8173FF" },
        Camera_Wifi: { name: "Wi-Fi设备", icon: "wifi", color: "#914FFF" },
        Camera_Electric: {
          name: "电围设备",
          icon: "ZM-dianwei",
          color: "#614FFF",
        },
        Camera_ETC: { name: "ETC设备", icon: "a-ETC1x", color: "#48BAFF" },
        Place_Hotel: { name: "酒店", icon: "hotel", color: "#EB8A5D" },
        Place_InterBar: { name: "网吧", icon: "diannao", color: "#EB6C6C" },
        Place_Government: {
          name: "政府机关",
          icon: "gejizhengfu",
          color: "#187AE4",
        },
        Place_School: { name: "学校", icon: "xuexiao", color: "#1faf8a" },
        Place_Key: { name: "重点场所", icon: "yishoucang", color: "#EA4A36" },
      },
      myfilter: {
        type: "All", // 下拉框选中项
        value: "", // 过滤设备名称
      },
      groupingBox: false,
      inputShow: true,
      dataList: [],
      loading: false,
      groupList: [],
      selectGroup: "",
      groupName: "",
      submitLoading: false,
    };
  },
  methods: {
    copyText(name) {
      copyText(name);
    },
    // 添加到自定义分组
    handleGrouping() {
      console.log(this.selectPointsList, "this.selectPointsList");
      this.groupingBox = true;
      this.selectGroup = "";
      this.groupName = "";
      this.queryMyGroupList();
    },
    queryMyGroupList() {
      this.loading = true;
      queryMyVideoGroupList({ userId: this.userInfo.id, searchKey: "" })
        .then((res) => {
          // 过滤只允许读的设备
          // .filter(
          //   (item) => item.permission == "write"
          // );
          this.groupList = res.data.grouplist;
          if (this.groupName) {
            this.groupList.forEach((item) => {
              if (item.groupName == this.groupName) {
                this.selectGroup = item.id;
              }
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleAdd() {
      this.inputShow = false;
    },
    handleAddName() {
      if (!this.groupName) {
        this.$Message.warning("请输入新的分组名称");
        return;
      }
      let params = {
        userId: this.userInfo.id,
        groupName: this.groupName,
      };
      addMyVideoGroup(params).then((res) => {
        if (res.code == 200) {
          this.$Message.success("新增完成");
          this.inputShow = true;
          this.queryMyGroupList();
        }
      });
    },
    handleCancel() {
      this.inputShow = true;
    },
    onSubmit() {
      this.submitLoading = true;
      let deviceIds = [];
      for (let key in this.selectPointsList) {
        // 在选中的图层中，并且为视图设备，才加入分组，因为感知设备没法播放视频，这个加入分组功能是给视频播放分组功能使用的
        if (
          this.layerCheckedNames.includes(key) &&
          !["Camera_Wifi", "Camera_RFID", "Camera_Electric"].includes(key)
        ) {
          let list = [];
          list = this.selectPointsList[key].list;
          let ids = list.map((item) => item.deviceId);
          deviceIds.push(...ids);
        }
      }
      let params = {
        groupId: this.selectGroup,
        deviceIds: deviceIds,
      };
      batchSave(params)
        .then((res) => {
          this.$Message.success("添加成功");
          this.groupingBox = false;
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    onCancel() {
      this.groupingBox = false;
    },
    // 展开-收起
    switchHandle(key) {
      const { isShow } = this.selectPointsList[key];
      this.selectPointsList[key].isShow = !isShow;
    },
    // 更新图层
    layerChange(operateType, layerType) {
      operateType === "close"
        ? this.layerCheckedNames.splice(
            this.layerCheckedNames.indexOf(layerType),
            1
          )
        : this.layerCheckedNames.push(layerType);
      this.$emit("updateLayerCheckedNames", this.layerCheckedNames);
    },
    deviceClick(item) {
      if (item.deviceId) {
        if (!item.Lat || !item.Lon) {
          this.$Message.info("该设备没有点位坐标信息！");
        } else {
          this.$emit("handleMapSelectDevice", item);
        }
      }
    },
    // 打开地图弹框
    deviceDblClick(item) {
      if (item.deviceId) {
        if (!item.Lat || !item.Lon) {
          this.$Message.info("该设备没有点位坐标信息！");
        } else {
          this.$emit("handleMapCurrentDevice", item);
        }
      }
    },
    // 名称过滤
    filterHandle() {
      const { selectPointsList, myfilter } = this;
      for (let key in selectPointsList) {
        for (const [key, value] of Object.entries(selectPointsList)) {
          if (myfilter.type === "All") {
            value.show = false;
          } else {
            myfilter.type === key ? (value.show = false) : (value.show = true);
          }
          if (myfilter.value) {
            value.list =
              value.list &&
              value.list.length &&
              value.list.map((e) => {
                return {
                  ...e,
                  show: e.deviceName.includes(myfilter.value) ? false : true,
                };
              });
          } else {
            value.list =
              value.list &&
              value.list.length &&
              value.list.map((e) => {
                return {
                  ...e,
                  show: false,
                };
              });
          }
        }
      }
      this.selectPointsList = {
        ...selectPointsList,
      };
    },
    delItem(array, index) {
      array.splice(index, 1);
    },
    handleExport() {
      let headers = [
        { label: "设备国标编号", key: "deviceGbId" },
        { label: "设备名称", key: "deviceName" },
        { label: "经度", key: "Lon" },
        { label: "纬度", key: "Lat" },
      ];
      let sheets = [];
      Object.keys(this.selectPointsList).forEach((key) => {
        if (this.selectPointsList[key].list.length) {
          sheets.push({
            name: this.deviceTypeMap[key].name,
            headers: headers.map((v) => v.label),
            list: this.selectPointsList[key].list.map((v) => {
              let obj = {};
              headers.forEach((i) => {
                obj[i.label] = v[i.key];
              });
              return obj;
            }),
          });
        }
      });
      this.jsonToSheetXlsx(sheets);
    },
    jsonToSheetXlsx(sheets) {
      let filename = `框选设备-${this.$dayjs().format("YYYYMMDDHHmmss")}.xlsx`;
      const workbook = {
        SheetNames: [],
        Sheets: {},
      };
      sheets.forEach((v) => {
        workbook.SheetNames.push(v.name);
        workbook.Sheets[v.name] = XLSX.utils.json_to_sheet(v.list, {
          header: v.headers,
        });
      });
      XLSX.writeFile(workbook, filename, { bookType: "xlsx" });
    },
    // 跳转设备档案
    checkDeviceDoc(item) {
      const { href } = this.$router.resolve({
        name: "device-archive",
        query: { archiveNo: item.deviceGbId },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.select-result-dom {
  margin-right: 10px;
  height: calc(~"100vh - 220px");
  .dom-content {
    padding-bottom: 5px;
    height: 100%;
    &-search {
      padding: 10px 15px;
      display: flex;

      /deep/.ivu-select {
        width: 120px;
        margin-right: 10px;
      }
      /deep/.ivu-input-wrapper {
        flex: 1;
      }
    }
    .layerList {
      height: calc(~"100% - 104px");
      overflow-y: auto;
    }
    &-type {
      padding: 0 16px;
      .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        .device-type {
          flex: 1;
          display: flex;
          align-items: center;
          margin-left: 10px;
          > span {
            color: rgba(0, 0, 0, 0.9);
            font-weight: bold;
            margin-left: 11px;
          }
          .type-total {
            color: #2c86f8;
          }
        }
        .arrowrun {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(90deg);
        }
        .arrow {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(0deg);
        }
      }
    }
    &-list {
      padding-left: 28px;
      cursor: pointer;
      .offline {
        i {
          color: #888888 !important;
        }
      }
      > div {
        display: flex;
        align-items: center;
        height: 30px;
        width: 100%;
      }
      P {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
      }
      > div:hover {
        background: rgba(44, 134, 248, 0.1);
        > P {
          color: rgba(0, 0, 0, 0.8);
        }
      }
      i {
        margin-left: 10px;
      }
      .label {
        display: inline-block;
        color: #ffffff;
        white-space: nowrap;
        width: 54px;
      }
      .message {
        display: inline-block;
        color: #b4ceef;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 18px;
      }
      .font-green {
        color: #19a33e;
      }
      .font-yellow {
        color: #bfa631;
      }
      .font-orange {
        color: #f5852a;
      }
    }
  }
}
/deep/.ivu-input-with-suffix {
  padding-right: 0.16667rem;
}
.action-wrapper {
  padding: 10px 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.grouping {
  margin-right: 10px;
}
// 添加分组
.content {
  height: 400px;
  .grouping-tip {
    color: #2c86f8;
    font-size: 14px;
    height: 34px;
    display: flex;
    align-items: center;
    cursor: pointer;
    .ivu-icon-md-add {
      font-size: 20px;
    }
  }
  /deep/.ivu-input-suffix {
    right: 7px;
    width: 40px;
    font-weight: bold;
    /deep/ .ivu-icon-md-checkmark {
      margin-right: 5px;
      color: #2c86f8;
      font-weight: bold;
      cursor: pointer;
    }
    /deep/ .ivu-icon-md-close {
      font-weight: bold;
      cursor: pointer;
    }
  }
  .content-box {
    position: relative;
    width: 100%;
    height: calc(~"100% - 34px");
    padding: 20px;
    overflow: auto;
    /deep/.ivu-radio-group {
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
