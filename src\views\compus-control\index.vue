<template>
  <div
    class="big-screen-layout"
    :class="theme === 'light' ? 'light-theme' : 'dark-theme'"
  >
    <div class="data-warehouse-overview">
      <div class="data-warehouse-left">
        <component
          :is="themeComponent"
          title="事件统计分析"
          :padding="0"
          class="first-card resource-classification relationship-map m-b10"
        >
          <ul class="dataShow">
            <li class="dataList" v-for="(item, index) in dataList" :key="index">
              <div class="data-msg" v-if="!item.type">
                <img :src="item.imgUrl" alt="" />
                <p class="data-total">
                  <count-to
                    :start-val="0"
                    :end-val="item.num"
                    :duration="1000"
                    class="h1"
                  ></count-to>
                </p>
                <p class="data-name">{{ item.name }}</p>
              </div>
              <div v-else class="line"></div>
            </li>
          </ul>
        </component>
        <component
          :is="themeComponent"
          title="陌生人员"
          :padding="0"
          class="secend-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <div class="card-content-box">
            <div v-if="firstList.length !== 0">
              <div class="my-swiper-container" id="mySwiper">
                <swiper
                  ref="mySwiper"
                  :options="swiperOption"
                  class="my-swiper"
                >
                  <template v-for="(item, index) in firstList">
                    <swiper-slide :key="index">
                      <div class="swiper-item">
                        <div class="angle">
                          <div></div>
                          <div></div>
                          <div></div>
                          <div></div>
                        </div>
                        <listCard
                          class="listCard"
                          :itemInfo="item"
                          :theme="theme"
                        />
                      </div>
                    </swiper-slide>
                  </template>
                </swiper>
                <div
                  class="swiper-button-prev snap-prev-record"
                  slot="button-prev"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
                <div
                  class="swiper-button-next snap-next-record"
                  slot="button-next"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
              </div>
            </div>
            <ui-loading v-if="loading" />
            <ui-empty v-if="firstList.length === 0"></ui-empty>
          </div>
        </component>
        <component
          :is="themeComponent"
          title="陌生人员分析"
          :padding="0"
          class="third-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <ul class="fight_ul">
            <li class="flexLi" v-for="(item, index) in firstList" :key="index">
              <div class="left">
                {{ item.name || "未知" }}
              </div>
              <div class="idNumber">{{ item.idNumber || "未知" }}</div>
              <div>{{ item.schoolId | commonFiltering(schoolList) }}</div>
              <div class="right">
                {{ item.absTime }}
              </div>
            </li>
            <ui-loading v-if="loading" />
            <ui-empty v-if="firstList.length === 0"></ui-empty>
          </ul>
        </component>
      </div>
      <div class="data-warehouse-main">
        <div class="main">
          <div class="map">
            <mapBase ref="map" :siteList="siteList" @inited="mapInited" />
          </div>
          <component
            :is="themeComponent"
            title="陌生人员年龄段"
            :padding="0"
            class="age"
          >
            <div class="age-content">
              <div class="center">
                <count-to
                  :start-val="0"
                  :end-val="ageCount[0]"
                  :duration="1000"
                ></count-to>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[1]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">0-20</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[2]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">21-25</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[3]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">26-30</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[4]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">31-35</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[5]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">36-40</span>
              </div>
              <div class="item">
                <span class="num">
                  <count-to
                    :start-val="0"
                    :end-val="ageCount[6]"
                    :duration="1000"
                  ></count-to>
                </span>
                <span class="label">41及以上</span>
              </div>
            </div>
          </component>
        </div>
      </div>
      <div class="data-warehouse-left">
        <component
          :is="themeComponent"
          title="陌生人员统计"
          :padding="0"
          class="fourth-card resource-classification relationship-map m-b10"
        >
          <div class="line-echart">
            <LineEchart
              ref="lineEchartRef"
              :title="dataAccess.title"
              :legend="dataAccess.legend"
              :grid="dataAccess.grid"
              :xAxis="dataAccess.xAxis"
              :yAxis="dataAccess.yAxis"
              :series="dataAccess.series"
              :theme="theme"
            />
          </div>
        </component>
        <component
          :is="themeComponent"
          title="重点人员"
          :padding="0"
          class="fifth-card resource-classification relationship-map m-b10"
        >
          <div
            slot="extra"
            @click="handleMore(0)"
            class="relationship-map-head"
          >
            <div class="more">更多</div>
            <i class="iconfont icon-doubleright"></i>
          </div>
          <div class="card-content-box">
            <div v-if="secendList.length !== 0">
              <div class="my-swiper-container" id="mySwiper2">
                <swiper
                  ref="mySwiper2"
                  :options="swiperOption"
                  class="my-swiper"
                >
                  <template v-for="(item, index) in secendList">
                    <swiper-slide :key="index">
                      <div class="swiper-item">
                        <div class="angle">
                          <div></div>
                          <div></div>
                          <div></div>
                          <div></div>
                        </div>
                        <listCard
                          class="listCard"
                          :itemInfo="item"
                          :theme="theme"
                        />
                      </div>
                    </swiper-slide>
                  </template>
                </swiper>
                <div
                  class="swiper-button-prev snap-prev-record"
                  slot="button-prev"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
                <div
                  class="swiper-button-next snap-next-record"
                  slot="button-next"
                >
                  <i class="iconfont icon-caret-right"></i>
                </div>
              </div>
            </div>
            <ui-loading v-if="loading" />
            <ui-empty v-if="secendList.length === 0"></ui-empty>
          </div>
        </component>
        <component
          :is="themeComponent"
          title="学校陌生人统计"
          :padding="0"
          class="third-card resource-classification relationship-map m-b10"
        >
          <div ref="pieChart" class="pie-chart"></div>
        </component>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from "echarts";
import CountTo from "vue-count-to";
import RadarEchart from "@/components/echarts/radar-echart";
import PieEchart from "@/components/echarts/pie-echart";
import LineEchart from "@/components/echarts/line-echart";
import listCard from "@/views/compus-control-list/components/list-card.vue";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { strangerPageList } from "@/api/monographic/compus-control";
import { mapActions, mapGetters, mapState } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import mapBase from "./components/compus-control-map.vue";
import card from "@/components/screen/srceen-card.vue";
import UiCard from "@/components/ui-card.vue";
export default {
  name: "perception-site",
  components: {
    RadarEchart,
    PieEchart,
    LineEchart,
    CountTo,
    listCard,
    swiper,
    swiperSlide,
    mapBase,
    // card,
  },
  data() {
    return {
      dataList: [
        {
          imgUrl: require("@/assets/img/compus-control/vidicon.png"),
          num: 0,
          name: "校园抓拍设备",
        },
        { type: "line" },
        {
          imgUrl: require("@/assets/img/compus-control/perception.png"),
          num: 0,
          name: "今日陌生人员",
        },
        { type: "line" },
        {
          imgUrl: require("@/assets/img/compus-control/policeService.png"),
          num: 0,
          name: "近7天陌生人员",
        },
      ],
      swiperOption: {
        effect: "coverflow",
        slidesPerView: 1.28,
        centeredSlides: true,
        initialSlide: 2,
        speed: 1000,
        coverflowEffect: {
          rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
          stretch: 50, // 每个slide之间的拉伸值，越大slide靠得越紧。
          depth: 90, // slide的位置深度。值越大z轴距离越远，看起来越小。
          modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
          slideShadows: true, // 开启slide阴影。默认 true。
        },
        navigation: {
          nextEl: ".snap-next-record",
          prevEl: ".snap-prev-record",
        },
        observer: true,
        observeParents: true,
      },
      dataAccess: {
        title: {
          show: false,
        },
        grid: {
          left: "0",
          top: "20%",
          right: "0.1%",
          bottom: "10%",
          containLabel: true,
        },
        legend: {
          type: "scroll",
          data: ["陌生人员"],
          top: "-3%",
          itemGap: 24,
          itemWidth: 12,
          itemHeight: 2,
          icon: "rect",
          textStyle: {
            color: "inherit",
            padding: [0, 0, 0, 3],
          },
        },
        xAxis: {
          type: "category",
          data: [
            "11-21",
            "11-22",
            "11-23",
            "11-24",
            "11-25",
            "11-26",
            "11-27",
            "11-28",
          ],
          boundaryGap: true,
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitNumber: 5,
          splitLine: {
            lineStyle: {
              type: "dashed",
            },
          },
        },
        series: [
          {
            type: "line",
            name: "陌生人员",
            data: [80, 35, 38, 89, 50, 61, 70, 100],
            symbolSize: 0,
            smooth: true,
            lineStyle: {
              width: 2,
            },
            color: "#F1BA4D",
            areaStyle: {
              //区域填充样式
              color: new echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: "rgba(241, 186, 77, 0.6)",
                  },
                  {
                    offset: 1,
                    color: "rgba(241, 186, 77, 0)",
                  },
                ],
                false
              ),
            },
          },
        ],
      },
      loading: false,
      firstList: [],
      secendList: [],
      ageCount: [0, 0, 0, 0, 0, 0, 0],
      siteList: [],
      inited: false,
      timer: null,
      themeComponent: UiCard,
    };
  },
  computed: {
    ...mapGetters({
      schoolList: "dictionary/getSchoolList", // 学校
      strangerFlagList: "dictionary/getStrangerFlagList", // 检测状态
    }),
    ...mapState("common", ["theme"]),
  },
  watch: {
    theme(val) {
      this.themeComponent = val === "light" ? UiCard : card;
      this.dataAccess.legend.textStyle.color =
        val === "light" ? "inherit" : "rgba(255, 255, 255, 0.6)";
      this.$refs.lineEchartRef.resizeEchart();
      this.$nextTick(() => {
        this.barCharts(
          this.$refs.pieChart,
          [12345, 14444, 16534, 21212, 24232],
          [
            "安徽电子信息职业技术学院",
            "安徽科技学院",
            "安徽财经大学",
            "蚌埠医科大学",
            "蚌埠学院",
          ]
        );
      });
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    // this.getDevice()
    this.getPersonPageList(true);
    this.getKeyPersonList(true);
    this.timer = setInterval(() => {
      this.getPersonPageList();
    }, 60000);
    this.$nextTick(() => {
      this.barCharts(
        this.$refs.pieChart,
        [12345, 14444, 16534, 21212, 24232],
        [
          "安徽电子信息职业技术学院",
          "安徽科技学院",
          "安徽财经大学",
          "蚌埠医科大学",
          "蚌埠学院",
        ]
      );
    });
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    getDevice() {
      // 主副驾设备
      queryDeviceList({
        orgCodes: ["34032"],
        pageNumber: 1,
        pageSize: 9999,
      }).then((res) => {
        const { entities } = res.data;
        this.siteList = entities.map((v) => {
          v.longitude = v.geoPoint.lon;
          v.latitude = v.geoPoint.lat;
          return v;
        });
        this.$set(this.dataList[0], "num", this.siteList.length);
      });
    },
    getPersonPageList(isFirst) {
      if (isFirst) this.loading = true;
      let params = {
        pageNumber: 1,
        pageSize: 20,
        strangerFlag: "1",
        startDate: this.$dayjs().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        endDate: this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };
      strangerPageList(params)
        .then((res) => {
          this.firstList = res.data.entities;
          this.$set(this.dataList[2], "num", res.data.total);
        })
        .finally(() => {
          this.loading = false;
          if (this.inited) this.addMapInfoWindow({ ...this.firstList[0] });
        });
    },
    getKeyPersonList(isFirst) {
      if (isFirst) this.loading = true;
      let params = {
        pageNumber: 1,
        pageSize: 20,
        strangerFlag: "1",
        startDate: this.$dayjs()
          .subtract(7, "day")
          .format("YYYY-MM-DD HH:mm:ss"),
        endDate: this.$dayjs().format("YYYY-MM-DD HH:mm:ss"),
      };
      strangerPageList(params)
        .then((res) => {
          this.secendList = res.data.entities;
          this.$set(this.dataList[4], "num", res.data.total);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleMore() {
      const path = this.$route.path;
      const noMenu = this.$route.query.noMenu;
      this.$router.push({
        path,
        query: { noMenu, sectionName: "strangersFound" },
      });
    },
    mapInited() {
      this.inited = true;
      if (this.firstList.length)
        this.addMapInfoWindow({ ...this.firstList[0] });
    },
    addMapInfoWindow(item) {
      this.$refs.map.thematicAlert(item, "compusControl");
    },
    //柱状排名配置
    barCharts(el, data, ydata) {
      this.myEchart = echarts.init(el, this.theme);
      let img1 =
        "data:image/png;base64,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";
      let img2 =
        "data:image/png;base64,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";
      let img3 =
        "data:image/png;base64,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";
      // let ydata = ['xxxxxx路口10wwwwww', 'xxxxxx路口9', 'xxxxxx路口8', 'xxxxxx路口7', 'xxxxxx路口6', 'xxxxxx路口5', 'xxxxxx路口4', 'xxxxxx路口3', 'xxxxxx路口2', 'xxxxxx路口1',];
      let option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "120",
          right: "50",
          bottom: "0",
          top: "0",
          containLabel: false,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "category",
          data: ydata,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 120,
            align: "left",
            interval: 0,
            formatter: (name, index) => {
              let yname = "";
              if (name.length > 7) {
                yname = name.substr(0, 7) + "...";
              } else {
                yname = name;
              }
              if (data.length - index < 4) {
                return `{icon${data.length - index}|}` + `{name|${yname}}`;
              } else {
                return `{count|${data.length - index}}` + `{name|${yname}}`;
              }
            },
            rich: {
              icon1: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img1,
                },
              },
              icon2: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img2,
                },
              },
              icon3: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img3,
                },
              },
              count: {
                padding: [2, 0, 0, 0],
                width: 20,
                height: 18,
                align: "center",
                fontSize: 12,
                fontFamily: "DIN",
                fontWeight: 500,
                // shadowColor: '#008AFF',
                // borderColor: '#008AFF',
                borderRadius: 50,
                borderWidth: 1,
                // backgroundColor: {
                // 	image: img4,
                // },
              },
              // inherit
              name: {
                width: 85,
                fontSize: 12,
                align: "left",
                fontFamily: "Source Han Sans CN",
                fontWeight: 500,
              },
            },
          },
        },
        series: [
          {
            type: "bar",
            showBackground: false,
            label: {
              show: true,
              position: "right",
              formatter: "{c}",
            },
            barWidth: 10,
            itemStyle: {
              emphasis: {
                barBorderRadius: 7,
              },
              //颜色样式部分
              normal: {
                barBorderRadius: 8,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#5BC7FF" },
                  { offset: 1, color: "#2C86F8" },
                ]),
              },
            },
            data: data,
          },
        ],
      };
      this.myEchart.setOption(option, true);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
  },
};
</script>
<style lang="less" scoped>
@import "~@/views/fatigue-driving/style/theme.less";
.m-b10 {
  margin-bottom: 10px;
}
.big-screen-layout {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}
.data-warehouse-overview {
  overflow: hidden;
  display: flex;
  flex: 1;
  position: relative;
  .data-warehouse-left {
    width: 500px;
    height: 100%;
    display: flex;
    flex-direction: column;

    /deep/.card {
      width: 100%;
      flex: unset;
      height: 38%;
      .card-head .title span {
        font-size: 18px;
        letter-spacing: 0;
      }
      .card-content {
        display: flex;
        height: calc(~"100% - 30px");
      }
    }
    .first-card,
    .secend-card,
    .fourth-card,
    .fifth-card {
      height: 30%;
    }
    .third-card {
      height: 40%;
    }
    .dataShow {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 42px;
      justify-content: space-around;
      .dataList {
        display: flex;
        .data-msg {
          display: flex;
          flex-direction: column;
          align-items: center;
          img {
            width: 90px;
            height: 90px;
          }
          .data-total {
            font-size: 20px;
            font-weight: bold;
            color: #2ecbff;
            margin: 5px 0;
          }
          .data-name {
            font-size: 12px;
          }
        }
        .line {
          height: 60px;
          border-left: 1px solid #012a63;
        }
      }
    }
    .resource-classification {
      /deep/ .card-content {
        padding: 0 !important;
      }
    }

    .relationship-map {
      .relationship-map-head {
        display: flex;
        margin-right: 10px;
        cursor: pointer;
        align-items: center;
        .more {
          margin-right: 6px;
          font-size: 14px;
        }
        .total-entity,
        .relationship-entity {
          display: flex;
          align-items: center;
          margin-right: 40px;
          .name {
            font-size: 12px;
            line-height: 18px;
          }
          .number {
            font-size: 14px;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            margin-left: 4px;
            line-height: 20px;
          }
        }
        .relationship-entity {
          margin-right: 30px;
        }
      }
      /deep/ .card-content {
        //   padding: 0 !important;
        height: calc(~"100% - 70px");
        margin-top: 20px;
      }
    }
    .fight_ul {
      overflow-y: auto;
      width: 100%;
      padding: 0 20px;
      padding-bottom: 10px;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      height: 100%;
      position: relative;
      .iconfont {
        line-height: normal;
      }
      .flexLi {
        color: #fff;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: linear-gradient(
          90deg,
          rgba(6, 55, 131, 0) 0%,
          rgba(6, 55, 131, 0.5) 51%,
          rgba(6, 55, 131, 0) 100%
        );
        border: 1px solid;
        border-image: linear-gradient(
            90deg,
            rgba(255, 255, 255, 0),
            rgba(121, 173, 255, 0.5),
            rgba(121, 173, 255, 0)
          )
          1 1;
        padding: 7px;
        margin-bottom: 7px;
        .left {
          width: 42px;
          font-weight: bold;
          font-size: 14px;
          color: #2ecbff;
          /* cursor: pointer; */
          .iconfont {
            margin-right: 6px;
          }
        }
        /* .right {
						font-size: 14px;
					} */
      }
    }
    .my-swiper-container {
      padding: 0 30px;
      position: relative;
      .my-swiper {
        margin: auto;
        padding: 15px 0;
        .swiper-item {
          width: 100%;
          box-sizing: border-box;
          overflow: hidden;
          .angle {
            div {
              width: 10px;
              height: 10px;
              background: url("~@/assets/img/screen/angle.png") no-repeat;
              z-index: 1;
              &:nth-child(1) {
                position: absolute;
                top: 0;
                left: 0;
                background-position: 0 0;
              }
              &:nth-child(2) {
                position: absolute;
                top: 0;
                right: 0;
                background-position: -11px 0px;
              }
              &:nth-child(3) {
                position: absolute;
                bottom: 0;
                left: 0;
                background-position: 0 -11px;
              }
              &:nth-child(4) {
                position: absolute;
                bottom: 0;
                right: 0;
                background-position: -11px -11px;
              }
            }
          }
        }
      }
      /* /deep/ .swiper-container-3d {
                    .swiper-slide-shadow-left {
                    background-image: linear-gradient(to left, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                    }
                    .swiper-slide-shadow-right {
                    background-image: linear-gradient(to right, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
                    }
                } */
      .swiper-button-prev,
      .swiper-button-next {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        margin-top: -15px;
        .iconfont {
          font-size: 18px;
        }
      }
      .swiper-button-prev {
        transform: rotate(180deg);
        left: 12px;
      }
      .swiper-button-next {
        right: 12px;
      }
    }
    .line-echart,
    .pie-chart {
      height: 100%;
      width: 100%;
      padding: 0 10px;
    }
  }
  .data-warehouse-main {
    flex: 1;
    position: relative;
    height: 100%;
    .main {
      height: 100%;
      display: flex;
      flex-direction: column;
      .map {
        flex: 1;
        height: 100%;
        width: 100%;
        position: relative;
        padding: 0 10px;
      }
      .age {
        margin: 0 10px 10px 10px;
        width: calc(~"100% - 20px");
        .age-content {
          position: relative;
          height: 250px;
          background: url("~@/assets/img/thematic/bg.png") no-repeat 0 0;
          background-size: cover;
          .center {
            padding-top: 54px;
            color: #fff;
            font-size: 20px;
            text-align: center;
            width: 300px;
            height: 200px;
            background: url("~@/assets/img/thematic/center.png") no-repeat 0 0;
            background-size: cover;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
          .item {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            width: 100px;
            height: 100px;
            background: url("~@/assets/img/thematic/item-bg.png") no-repeat 0 0;
            background-size: cover;
            .num {
              color: #2ecbff;
              font-size: 20px;
            }
            .label {
              font-size: 14px;
            }
            &:nth-of-type(2) {
              position: absolute;
              left: 5%;
              top: 20%;
            }
            &:nth-of-type(3) {
              position: absolute;
              left: 20%;
              top: 0;
            }
            &:nth-of-type(4) {
              position: absolute;
              left: 15%;
              bottom: 7%;
            }
            &:nth-of-type(5) {
              position: absolute;
              right: 15%;
              bottom: 7%;
            }
            &:nth-of-type(6) {
              position: absolute;
              right: 20%;
              top: 0;
            }
            &:last-child {
              position: absolute;
              right: 5%;
              top: 20%;
            }
          }
        }
      }
    }
  }
}
.relationship-map-head {
  padding-right: 10px;
  display: flex;
  color: rgba(0, 0, 0, 0.35);
  cursor: pointer;
  align-items: center;
  .more {
    margin-right: 6px;
    font-size: 14px;
  }
}
</style>
