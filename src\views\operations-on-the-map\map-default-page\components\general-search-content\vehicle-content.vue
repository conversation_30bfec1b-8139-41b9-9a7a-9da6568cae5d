<template>
    <div class="vehicle-content-wrapper">
        <div class="vehicle-content warpper-box">
            <div v-for="(e, i) in vehicleList" :key="i" class="vehicle-item box-item" 
                :class="{ active: currentClickIndex == i }" @click="chooseMapItem(i)">
                <div class="header">
                    <div class="header-left">
                        <span class="serialNumber"
                                :class="{ activeNumber: currentClickIndex == i }">
                            <span>{{ i + 1 }}</span>
                        </span>
                        <!-- <span class="license-plate-small">{{ e.plateNo || '--' }}</span> -->
                        <ui-plate-number :plateNo="e.plateNo" :color="e.plateColor"></ui-plate-number>
                    </div>
                    <div>
                        <operate-bar :list='e' :tabType="{'type': 6}" @collection="collection($event, e, i)"></operate-bar>
                    </div>
                </div>
                <div class="content">
                    <div class="content-left">
                        <img v-lazy="e.traitImg" alt="" />
                    </div>
                    <div class="content-right">
                        <span class="ellipsis">
                            <ui-icon type="time"
                                    :size="14"></ui-icon>
                            <span>{{ e.absTime || '--' }}</span>
                        </span>
                        <span class="ellipsis">
                            <ui-icon type="location"
                                    :size="14"></ui-icon>
                            <span>{{ e.deviceName || '--' }}</span>
                        </span>
                        <span v-if="e.similarity" class="ellipsis">
                            <ui-icon type="wenjianxiangsidupeizhi"
                                    :size="14"></ui-icon>
                            <span>相似度：</span>
                            <span class="score">{{ `${e.similarity}%` || '--' }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <ui-loading v-if="loading" />
        <ui-empty v-if="!loading && !vehicleList.length" />
        <div class="general-search-footer">
        <ui-page :simple="true"
                :show-elevator="false"
                :show-sizer="false"
                :total="total"
                countTotal
                :current="pageInfo.pageNumber"
                :page-size="pageInfo.pageSize"
                @pageChange="pageChange"
                size="small"
                show-total> </ui-page>
        </div>
    </div>
</template>

<script>
import { queryVehicleRecordSearch } from '@/api/operationsOnTheMap'
import operateBar from '@/components/mapdom/operate-bar.vue'
import { mapMutations, mapGetters } from 'vuex';
export default {
    components: {
        operateBar
    },
    props: {
        //搜索条件
        searchPrams: {
            type: Object,
            default: () => { }
        },
        // 当前点击顺序
        currentClickIndex: {
            type: Number,
            default: -1
        }
    },
    watch: {
        currentClickIndex: {
            handler (newVal){
                if(newVal > -1){
                    let list =  document.querySelectorAll('.vehicle-item');
                    list[newVal].scrollIntoView(false)
                }
            },
        },
        getCollectJudge: {
            handler(val) {
                if(this.currentClickIndex > -1) {
                    this.$set(this.vehicleList[this.currentClickIndex], 'myFavorite', val)
                }
            },  
            immediate: true
        }
    },
    data () {
        return {
            vehicleList: [],
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            },
            loading: false
        }
    },
    computed:{
        ...mapGetters({
            getCollectJudge: 'map/getCollectJudge',
        })
    },
    created () {
        this.init()
    },
    methods: {
        ...mapMutations({setCollectJudge: 'map/setCollectJudge'}),
        init () {
            this.queryVehicleRecordSearch()
        },
        chooseMapItem (index) {
            this.$emit('chooseMapItem', index)
        },
        // 收藏/取消收藏
        collection($event, item, index){
            this.$set(this.vehicleList[index], 'myFavorite', $event)
            // item.myFavorite = $event;
            this.setCollectJudge($event)
        },
        queryVehicleRecordSearch () {
            const { pageInfo, searchPrams } = this
            const params = { ...pageInfo, ...searchPrams }
            this.loading = true
            // 后端字段命名未统一
            params.devices = params.deviceIds
            params.similarity = params.similarity / 100;
            delete params.deviceIds
            queryVehicleRecordSearch(params)
            .then(res => {
                if (res.code === 200) {
                    const {
                        data: { entities = [], total = 0 }
                    } = res
                    this.vehicleList = entities
                    this.total = total
                    this.$emit('mapResultHandler', entities, 1)
                }
            })
            .catch(() => {
                this.vehicleList = []
                this.$emit('mapResultHandler', [])
            })
            .finally(() => {
                this.loading = false
            })
        },
        pageChange (pageNumber) {
            this.pageInfo.pageNumber = pageNumber
            this.queryVehicleRecordSearch()
        }
    }
}
</script>

<style lang="less" scoped>
@import 'style/index';
.vehicle-content {
    .header-left{
        width: 100%;
    }
}
</style>
