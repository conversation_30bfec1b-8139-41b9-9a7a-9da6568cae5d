import { imageStructure } from "@/api/multimodal-analysis.js";
import UiMutilLoading from "@/components/ui-mutl-loading.vue";
import Vue from "vue";
export const mutilMixins = {
  data() {
    return {
      mutilLoading: false,
      multiDataList: [],
      isShowMutilAnalysis: false,
    };
  },
  methods: {
    async multilAnalysisHandler(type, info) {
      const loadingTemplate = new Vue({
        render: (h) =>
          h(UiMutilLoading, { id: "mutil-loading" }, [h("span", "解析中...")]),
      }).$mount();
      document.body.appendChild(loadingTemplate.$el);
      try {
        const param = {
          sceneImg: info.sceneImg,
        };
        const { data } = await imageStructure(param);
        // const data = {
        //   picDesc:
        //     "图片中展示了一对骑电动车的情侣。男性骑在前面，女性坐在后面。他们都戴着白色的头盔，穿着白色短袖衬衫和蓝色牛仔裤。男性穿着白色运动鞋，女性穿着白色运动鞋和白色袜子。他们身后挂着透明的雨衣，雨衣可以单人或双人使用。背景是浅蓝色，图片左上角有红色和白色的标签文字。",
        //   picKeyWord: "透明雨衣 双人骑行 无异味 轻柔透",
        //   picOcr: "透明雨衣单双人自由切换“无异味轻柔透”JD.COM 京东",
        // };
        const allData = {
          ...data,
          picUrl: info?.sceneImg || "",
          absTime: info?.absTime || "",
          deviceInfo: {
            name: info.deviceName || "",
          },
        };
        this.multiDataList = [allData];
        this.isShowMutilAnalysis = true;
        this.$nextTick(() => {
          this.$refs.detailMutiAnalysisRef.showList(0);
        });
      } catch (e) {
        console.log(e);
      } finally {
        document.body.removeChild(loadingTemplate.$el);
        loadingTemplate.$destroy();
      }
    },
  },
};
