<template>
  <div>
    <div class="search-list">
      <ui-label class="inline right-margin mb-lg" label="设备编码" :width="70">
        <Input
          v-model="searchData.deviceId"
          class="input-width"
          placeholder="请输入设备编码"
          @on-blur="search"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="设备名称" :width="70">
        <Input
          v-model="searchData.deviceName"
          class="input-width"
          placeholder="请输入设备名称"
          @on-blur="search"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="点位类型" :width="70">
        <Select
          class="input-width"
          v-model="searchData.sbdwlxList"
          multiple
          :max-tag-count="1"
          clearable
          placeholder="请选择点位类型"
          @on-change="search"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="功能类型" :width="70">
        <Select
          class="input-width"
          v-model="searchData.sbgnlxList"
          multiple
          :max-tag-count="1"
          clearable
          placeholder="请选择功能类型"
          @on-change="search"
        >
          <Option
            v-for="(sbgnlxItem, bdindex) in dictData['propertySearch_sbgnlx']"
            :key="'sbgnlx' + bdindex"
            :value="sbgnlxItem.dataKey"
            >{{ sbgnlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!--      <ui-label class="inline right-margin mb-lg" label="检测状态" :width="70">-->
      <!--        <Select-->
      <!--            class="input-width"-->
      <!--            multiple-->
      <!--            :max-tag-count="1"-->
      <!--            v-model="searchData.checkStatuses"-->
      <!--            placeholder="请选择检测状态"-->
      <!--            clearable-->
      <!--            @on-change="search"-->
      <!--        >-->
      <!--          <Option-->
      <!--              v-for="(statuItem, index) in dictData['check_status']"-->
      <!--              :key="'status' + index"-->
      <!--              :value="statuItem.dataKey"-->
      <!--          >{{ statuItem.dataValue }}-->
      <!--          </Option-->
      <!--          >-->
      <!--        </Select>-->
      <!--      </ui-label>-->
      <ui-label class="inline right-margin mb-lg" label="上报状态" :width="70">
        <Select
          class="input-width"
          v-model="searchData.cascadeReportStatus"
          placeholder="请选择上报状态"
          clearable
          @on-change="search"
        >
          <Option value="0">未上报 </Option>
          <Option value="1">已上报 </Option>
        </Select>
      </ui-label>
      <!--      <ui-label class="inline right-margin mb-lg" label="设备标签" :width="70">-->
      <!--        <Select-->
      <!--            class="input-width"-->
      <!--            :max-tag-count="1"-->
      <!--            v-model="searchData.tagIds"-->
      <!--            placeholder="请选择设备标签"-->
      <!--            filterable-->
      <!--            multiple-->
      <!--            @on-change="search"-->
      <!--        >-->
      <!--          <Option v-for="item in errorList" :value="item.value" :key="item.value">{{-->
      <!--              item.label-->
      <!--            }}-->
      <!--          </Option>-->
      <!--        </Select>-->
      <!--      </ui-label>-->
      <div class="inline mb-lg">
        <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
        <Button type="default" @click="reset"> 重置 </Button>
      </div>
    </div>
    <div class="selects mb-md">
      <span class="fl mt-md mr-sm">空间信息异常原因</span>
      <ui-select-tabs
        class="ui-select-tabs"
        :list="errorList"
        @selectInfo="selectInfo"
        ref="uiSelectTabs"
      ></ui-select-tabs>
    </div>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';

export default {
  name: 'search-list',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
    searchConditions: {
      type: Object,
      default: () => {},
    },
    baseErrorReasonArr: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      searchData: {
        deviceId: '', // 设备编码
        deviceName: '', // 设备名称
        sbdwlxList: [], // 设备点位类型
        sbgnlxList: [], // 设备功能类型
        checkStatuses: [], // 状态
        cascadeReportStatus: '', // 上报状态
        errorMessageList: [], //  异常原因
        baseErrorMessageList: [],
      },
      errorList: [],
    };
  },
  async mounted() {
    await this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', this.searchData);
      });
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(equipmentassets.queryCheckErrorList);
        this.errorList = res.data.data.map((row) => {
          return {
            label: row,
            value: row,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    selectInfo(data) {
      const infoList = data.map((item) => item.value);
      this.searchData.baseErrorMessageList = infoList;
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {
    searchConditions(val) {
      if (val) {
        Object.keys(this.searchData).forEach((key) => {
          this.searchData[key] = val[key];
        });
      }
    },
    baseErrorReasonArr(val) {
      if (val.length) {
        try {
          let matchArr = this.dictData.device_space_error_type.map((item) => item.dataKey);
          let showErrorReasonArr = val.filter((item) => matchArr.includes(item.errorType));
          this.errorList = showErrorReasonArr.map((item) => {
            let obj = {};
            obj.name = item.dataValue;
            obj.value = item.dataKey;
            return obj;
          });
        } catch {
          this.errorList = [];
        }
      }
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
.right-margin {
  margin-right: 30px;
}

.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.selects {
  width: 100%;
}

.input-width {
  width: 199px;
}

.align-flex {
  display: flex;
  align-items: center;
}

.data-list {
  .align-flex;
  color: var(--color-content);
  font-size: 14px;

  .ui-select-tabs {
    flex: 1;
  }
}
</style>
