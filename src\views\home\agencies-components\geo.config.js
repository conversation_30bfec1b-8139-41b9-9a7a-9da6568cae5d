import { fontSize } from '@/util/module/common.js';

export const options = (mapData) => {
  let domImg = document.createElement('img');
  domImg.style.height = domImg.height = domImg.width = domImg.style.width = '8px';
  domImg.src = require('@/assets/img/map-bg.png');
  return {
    tooltip: {
      trigger: 'item',
      show: true,
      enterable: true,
    },
    geo: [
      {
        // center: [109.844902, 19.0392],
        // layoutCenter: ['51%', '53%'],
        // layoutSize: "600%",
        map: 'map',
        aspectScale: 1.2,
        top: 40,
        // layoutCenter: ['30%', '30%'],
        // layoutSize: 100,
        silent: true,
        roam: false,
        z: 0,
        itemStyle: {
          areaColor: '#0E2F6B',
          shadowColor: 'rgba(0, 0, 0, 1)',
          shadowOffsetX: 0,
          shadowOffsetY: 15,
          shadowBlur: 15,
          borderColor: '#52C9FB',
          borderWidth: 2,
        },
      },
    ],
    series: [
      {
        // center: [109.844902, 19.0392],
        // layoutCenter: ['50%', '50%'],
        // layoutSize: "600%",
        top: 20,
        //layoutCenter: ['0%', '30%'],
        type: 'map',
        zoom: 0,
        roam: true,
        map: 'map',
        aspectScale: 1.2,
        tooltip: {
          show: true,
          borderColor: '#19D5F6',
          borderWidth: 1,
          backgroundColor: 'rgba(14, 47, 107, .75)',
          padding: 10,
          formatter: function (params) {
            let item = mapData[params.dataIndex];
            if (!item.data) {
              item.data = {
                fileProblemTotal: 0,
                basicProblemTotal: 0,
                faceProblemTotal: 0,
                vehicleProblemTotal: 0,
                focusProblemTotal: 0,
              };
            }
            return `<Tooltip always placement="top">
                    <div slot="content">
                      <p><i class="icon-font icon-dingwei mr-sm" style="color:#279EFA"></i>${item.name}</p>
                      <p>基础数据问题：<span class="mb-sm" style="color:#19D5F6">${
                        item.data.basicProblemTotal || 0
                      }</span></p>
                      <p>人脸图像数据问题：<span class="mb-sm" style="color:#BF89F7">${
                        item.data.faceProblemTotal || 0
                      }</span></p>
                      <p>车辆图像数据问题：<span class="mb-sm" style="color:#F5AF55" >${
                        item.data.vehicleProblemTotal || 0
                      }</span></p>
                      <p>ZDR轨迹数据问题：<span style="color:#EA5252">${item.data.focusProblemTotal || 0}</span></p>
                      <p>档案数据问题：<span class="mb-sm" style="color: #0dd083">${
                        item.data.fileProblemTotal || 0
                      }</span></p>
                    </div>
                  </Tooltip>`;
          },
        },
        label: {
          show: false,
          color: '#fff',
          emphasis: {
            textStyle: {
              color: '#fff',
            },
          },
        },
        itemStyle: {
          borderColor: '#B8FFFF',
          borderWidth: 1,
          areaColor: {
            image: domImg,
            repeat: 'repeat',
          },
          shadowColor: '#B8FFFF',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 1,
          emphasis: {
            areaColor: {
              // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                {
                  offset: 0,
                  color: '#1F5D97', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#5BF5F7', // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
            borderColor: '#2ab8ff',
            borderWidth: 2,
            shadowColor: 'rgba(11, 39, 100, 1)',
            shadowOffsetX: 0,
            shadowOffsetY: 15,
            shadowBlur: 15,
            label: {
              show: false,
            },
          },
        },
        data: mapData,
      },
      // 柱状体的底部
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        geoIndex: 0,
        zlevel: 4,
        label: {
          // 这儿是处理的
          formatter: '{b}',
          position: 'bottom',
          color: '#fff',
          fontSize: fontSize(12),
          distance: 10,
          show: true,
        },
        top: 20,
        symbol: 'circle',
        symbolSize: [10, 5],
        symbolOffset: [0, -30],
        itemStyle: {
          // color: '#F7AF21',
          color: function (params) {
            if (!params.data.limitColor) return 'transparent';
            let colorArray = params.data.limitColor;
            return colorArray[0];
          },
          opacity: 1,
        },
        silent: true,
        data: mapData,
      },
      // 底部外框
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        geoIndex: 0,
        zlevel: 4,
        label: {
          show: false,
        },
        symbol: 'circle',
        symbolSize: [20, 10],
        symbolOffset: [0, -30],
        itemStyle: {
          color: function (params) {
            if (!params.data.limitColor) return 'transparent';
            let colorArray = params.data.limitColor;
            return {
              type: 'radial',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                {
                  offset: 0,
                  color: colorArray[1], // 0% 处的颜色
                },
                {
                  offset: 0.75,
                  color: colorArray[1], // 100% 处的颜色
                },
                {
                  offset: 0.751,
                  color: colorArray[0], // 100% 处的颜色
                },
                {
                  offset: 1,
                  color: colorArray[0], // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            };
          },
          opacity: 1,
        },
        silent: true,
        data: mapData,
      },
      {
        // 波纹
        tooltip: {
          show: false,
        },
        type: 'effectScatter',
        coordinateSystem: 'geo',
        rippleEffect: {
          scale: 10,
          brushType: 'stroke',
        },
        showEffectOn: 'render',
        itemStyle: {
          shadowColor: '#1AFBD9',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: function (params) {
            if (!params.data.limitColor) return 'transparent';
            let colorArray = params.data.limitColor;
            return colorArray[0];
          },
        },
        label: {
          color: 'green',
        },
        symbol: 'circle',
        symbolSize: [10, 5],
        symbolOffset: [0, -30],
        data: mapData,
      },
      {
        // 顶部数量
        type: 'scatter',
        coordinateSystem: 'geo',
        itemStyle: {
          color: 'rgb(22,255,255, .1)',
          opacity: 1,
        },
        label: {
          show: true,
          color: '#fff',
          fontWeight: 'bold',
          formatter: function (value) {
            let data = value.data;
            return data.total;
          },
          position: 'top',
        },
        // itemStyle: {
        //   color: function (params) {
        //     if (!params.data.limitColor) return 'transparent'
        //     let colorArray = params.data.limitColor
        //     return colorArray[0]
        //   },
        // },
        // symbol: function (value, params) {
        //   return params.data.img;
        // },
        tooltip: {
          show: false,
        },
        symbol: 'circle',
        symbolSize: [12, 12],
        symbolOffset: [0, -50],
        data: mapData,
        zlevel: 999,
      },
      {
        // 柱子
        type: 'scatter',
        coordinateSystem: 'geo',
        itemStyle: {
          color: function (params) {
            if (!params.data.limitColor) return 'transparent';
            let colorArray = params.data.limitColor;
            return {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: colorArray[1], // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: colorArray[0], // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            };
          },
        },
        emphasis: {
          scale: false,
        },
        label: {
          show: false,
          emphasis: {
            show: false,
          },
        },
        symbol: 'rect',
        tooltip: {
          show: false,
        },
        symbolSize: [10, 30],
        symbolOffset: [0, -45],
        data: mapData,
        zlevel: 998,
        silent: true,
      },
    ],
  };
};
