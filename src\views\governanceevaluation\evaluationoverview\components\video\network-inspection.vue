<template>
  <div class="auto-fill">
    <div class="top-wrapper mt-sm">
      <statistics class="statistics" :statistics-list="abnormalCount" :isIconBg="true"></statistics>
      <rank :rank-data="rankList"></rank>
    </div>
    <div class="abnormal-title">
      <div class="fl">
        <i class="icon-font icon-xiugaijilu f-14 color-filter"></i>
        <span class="f-16 color-filter ml-sm"> 检测结果详情</span>
      </div>
      <div class="export fr">
        <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs f-14">导出</span>
        </Button>
      </div>
    </div>
    <div class="hearder-title">
      <network-inspection-search @startSearch="startSearch" />
    </div>
    <ui-table class="auto-fill" :table-columns="columns" :table-data="tableData" :loading="loading">
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #deviceStatus="{ row }">
        <span
          class="tag"
          :style="{
            background: row.deviceStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
          >{{ checkStatusList(row.deviceStatus) }}</span
        >
      </template>
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page
      class="page"
      :pageData="pageData"
      :hasLast="hasLast"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="width: 95%; margin: auto">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import inspectionrecord from '@/config/api/inspectionrecord';
import videoThrem from '@/config/api/vedio-threm';
import { decryptDes } from '@/util/module/common';
export default {
  name: 'capture-rationality',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    NetworkInspectionSearch: require('./network-inspection-search').default,
    // ChartsContainer: require('../components/chartsContainer').default,
    statistics: require('@/components/icon-statistics').default,
    rank: require('../components/rank').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
  props: {
    strativeName: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      rankList: [],
      statisticsList: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      hasLast: false,
      abnormalCount: [], // 统计展示
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: 'true',
        },
        { title: '组织机构', key: 'orgCode' },
        { title: this.global.filedEnum.sbdwlx, key: 'sbdwlx', width: 150 },
        { title: `${this.global.filedEnum.ipAddr}`, key: 'ipAddr', width: 150 },
        { title: '设备联网状态', slot: 'deviceStatus', width: 100 },
        { title: '检测时间', key: 'createTime', width: 150 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      currentIcon: 'icon-exceptionlibrary',
      videoVisible: false,
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      resultId: null,
      indexId: null,
      exportLoading: false,
    };
  },
  created() {},
  watch: {
    strativeName: {
      deep: true,
      immediate: true,
      handler: async function () {
        this.indexId = this.$route.query.indexId;
        this.resultId = this.$route.query.resultId;
        this.getRankData();
        this.pageData.pageNum = 1;
        this.initList();
        await this.statisticsCount();
        this.abnormalCountMap();
      },
    },
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        orgCode: this.$route.query.orgCode,
        resultId: this.resultId,
        indexId: this.indexId,
      };
      try {
        let res = await this.$http.post(inspectionrecord.exportDetailData, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.$refs.livePlayer.cancelplay();
      this.$http.post(videoThrem.stop + this.playDeviceCode);
    },
    async clickRow(row) {
      try {
        this.videoVisible = true;
        let params = {
          deviceId: row.deviceId,
        };
        let {
          data: { data },
        } = await this.$http.post(videoThrem.getplay, params);
        this.videoUrl = decryptDes(data.ts, 'QSDI123456');
      } catch (err) {
        console.log(err);
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(
          inspectionrecord.getConnectInternetList,
          Object.assign(
            this.pageData,
            {
              orgCode: this.$route.query.orgCode,
              resultId: this.resultId,
              indexId: this.indexId,
            },
            this.searchData,
          ),
        );
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    async getRankData() {
      try {
        // 统计接口
        let params = {
          orgCode: this.$route.query.orgCode,
          resultId: this.resultId,
          indexId: this.indexId,
        };
        let {
          data: { data },
        } = await this.$http.post(inspectionrecord.queryLowerLevelScale, params);
        this.rankList = data.map((item, index) => {
          return {
            rank: index + 1,
            regionName: item.orgName,
            standardsValue: item.scale + '%',
          };
        });
      } catch (error) {
        // console.log(error);
      }
    },
    async statisticsCount() {
      // 统计接口
      let params = {
        orgCode: this.$route.query.orgCode,
        resultId: this.resultId,
        indexId: this.indexId,
      };
      let {
        data: { data },
      } = await this.$http.post(inspectionrecord.getConnectInternetStatistics, params);
      this.infoObj = data;
    },
    // 检索
    async startSearch(searchData = {}) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.$nextTick(async () => {
        await this.statisticsCount();
        await this.abnormalCountMap();
        this.initList();
      });
    },
    // 统计参数填充
    abnormalCountMap() {
      let map = {
        shouldCheckDeviceNum: {
          title: '视频监控设备总数',
          icon: 'icon-yingjianceshebeishuliang',
        },
        practicalCheckDeviceNum: {
          title: '实际检测设备数量',
          icon: 'icon-shijijianceshebeishuliang',
        },
        unConnectNum: {
          title: '未联网设备数量',
          icon: 'icon-weilianwangshebei',
        },
        connectNum: {
          title: '已联网设备数量',
          icon: 'icon-yilianwangshebei',
        },
        scale: {
          title: '联网率',
          icon: 'icon-lianwangshuai',
        },
      };
      let list = [];
      Object.keys(this.infoObj).map((key, index) => {
        let value = this.infoObj[key];
        let type = 'number';
        key === 'scale' ? (type = 'percentage') : (type = 'number');
        list.push({
          name: map[key].title || '',
          value: value,
          icon: map[key].icon,
          liBg: `li-bg${index + 1}`,
          iconColor: `icon-bg${index + 1}`,
          type: type,
        });
      });
      this.abnormalCount = list;
      if (!list.length) {
        this.abnormalCount = [{ title: '采集设备总数', icon: this.currentIcon }];
      }
      return list;
    },
    checkStatusList(deviceStatus) {
      return deviceStatus === '1' ? '联网' : '未联网';
    },
  },
};
</script>
<style lang="less" scoped>
.top-wrapper {
  display: flex;
  .statistics {
    width: 1430px;
    height: 252px;
    padding: 20px;
    background: var(--bg-sub-content);
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;

  .mr20 {
    margin-right: 20px;
  }

  .blue {
    color: #19c176;
  }
}
.abnormal-title {
  height: 48px;
  line-height: 48px;

  border-bottom: 0.005208rem solid var(--devider-line);
  .color-filter {
    color: rgba(43, 132, 226, 1);
    vertical-align: middle;
  }
}
.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.white-circle {
  position: relative;
  display: inline-block;
  line-height: 10px;
  vertical-align: middle;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #f5f5f5;
}
</style>
