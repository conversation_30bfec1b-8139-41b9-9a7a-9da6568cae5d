<template>
  <div class="interface-exception auto-fill">
    <Collapse v-model="collapseValue" simple>
      <Panel name="1" class="mb-sm">
        <span class="title">视频监控设备离线 </span>
        <i-switch
          class="fr switch"
          v-model="formData['0501'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">离线</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0501'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isNetworkOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0501', index)"
                  @on-clear="clearTask('0501', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0501', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isNetworkOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0501')"
                ></i>
                <i
                  v-if="index !== 0 && isNetworkOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0501', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0501')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0501')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0501')"
              :table-data="receiveData['0501'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isNetworkOpen ? '' : 'not-allowed']"
                  @click="() => isNetworkOpen && selectNotification('0501', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isNetworkOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0501', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0501'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0501systemTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isNetworkOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0501dialogTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isNetworkOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0501homeTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isNetworkOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0501smsTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
      <Panel name="2" class="mb-sm">
        <span class="title">人脸卡口设备离线</span>
        <i-switch
          class="fr switch"
          v-model="formData['0502'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">设备离线（检测不合格）</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0502'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isFaceOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0502', index)"
                  @on-clear="clearTask('0502', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0502', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isFaceOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0502')"
                ></i>
                <i
                  v-if="index !== 0 && isFaceOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0502', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0502')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0502')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0502')"
              :table-data="receiveData['0502'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isFaceOpen ? '' : 'not-allowed']"
                  @click="() => isFaceOpen && selectNotification('0502', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isFaceOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0502', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0502'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0502systemTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isFaceOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0502dialogTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isFaceOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0502homeTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isFaceOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0502smsTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
      <Panel name="3">
        <span class="title">车辆卡口设备离线</span>
        <i-switch
          class="fr switch"
          v-model="formData['0503'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">设备离线（检测不合格）</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0503'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isVehicleOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0503', index)"
                  @on-clear="clearTask('0503', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0503', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isVehicleOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0503')"
                ></i>
                <i
                  v-if="index !== 0 && isVehicleOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0503', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0503')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0503')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0503')"
              :table-data="receiveData['0503'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isVehicleOpen ? '' : 'not-allowed']"
                  @click="() => isVehicleOpen && selectNotification('0503', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isVehicleOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0503', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0503'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0503systemTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isVehicleOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0503dialogTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isVehicleOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0503homeTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isVehicleOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0503smsTemplateRef">
                    【<span>#组织机构#</span>】 共 <span>#异常设备数#</span> 条视频监控设备 <span>#异常原因#</span>,
                    请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
    </Collapse>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      collapseValue: [],
      listTaskSchemes: [], //检测任务
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          type: 'expand',
          width: 50,
        },
        {
          title: '所属组织机构',
          minWidth: 300,
          key: 'orgName',
        },
        {
          title: '配置通知接收人',
          minWidth: 300,
          slot: 'people',
        },
        {
          width: 300,
          title: '操作',
          slot: 'option',
        },
      ],
      formData: {
        '0501': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
        '0502': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
        '0503': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
      },
      // 由于后端返回的数据不能兼容前端页面所以这里要单独处理接收人的数据
      receiveData: {
        '0501': {
          receiveConfig: [],
        },
        '0502': {
          receiveConfig: [],
        },
        '0503': {
          receiveConfig: [],
        },
      },
      defaultPeopleList: [],
      receiveConfigData: null, //点击配置接收人时记录
    };
  },
  async created() {
    await this.getListTaskSchemes();
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     */
    await this.initMx(['0501', '0502', '0503']);
    this.dealReceiveCofingEchoMx();
  },
  methods: {
    ...mapActions({
      setCopyReceiveConfig: 'systemconfiguration/setCopyReceiveConfig',
    }),
    selectNotification(type, org) {
      this.receiveConfigData = {
        type,
        org,
      };
      this.defaultPeopleList = this.getDefaultPeopleMx(org.peopleList);
      this.peopleSelectShow = true;
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      // 根据点击selectNotification时的组织机构赋值
      const receiveOrg = this.receiveData[this.receiveConfigData.type].receiveConfig.find(
        (row) => row.orgCode === this.receiveConfigData.org.orgCode,
      );
      receiveOrg.peopleList = peopleList.map(({ id, orgCode, orgName, name, username, phone }) => {
        return {
          id,
          name,
          orgCode,
          orgName,
          username,
          phone,
          orgCodeByAttach: receiveOrg.orgCode,
          orgCodeByAttachName: receiveOrg.orgName,
        };
      });
    },
    reset() {
      this.receiveData = this.$util.common.deepCopy(this.initializedReceiveDataMx);
      this.resetMx();
    },
    async save() {
      try {
        this.dealReceiveCofingSaveMx();
        this.formData['0501'].systemTemplate = this.$refs['0501systemTemplateRef'].innerText;
        this.formData['0501'].dialogTemplate = this.$refs['0501dialogTemplateRef'].innerText;
        this.formData['0501'].homeTemplate = this.$refs['0501homeTemplateRef'].innerText;
        this.formData['0501'].smsTemplate = this.$refs['0501smsTemplateRef'].innerText;
        this.formData['0502'].systemTemplate = this.$refs['0502systemTemplateRef'].innerText;
        this.formData['0502'].dialogTemplate = this.$refs['0502dialogTemplateRef'].innerText;
        this.formData['0502'].homeTemplate = this.$refs['0502homeTemplateRef'].innerText;
        this.formData['0502'].smsTemplate = this.$refs['0502smsTemplateRef'].innerText;
        this.formData['0503'].systemTemplate = this.$refs['0503systemTemplateRef'].innerText;
        this.formData['0503'].dialogTemplate = this.$refs['0503dialogTemplateRef'].innerText;
        this.formData['0503'].homeTemplate = this.$refs['0503homeTemplateRef'].innerText;
        this.formData['0503'].smsTemplate = this.$refs['0503smsTemplateRef'].innerText;
        await this.saveMx(['0501', '0502', '0503']);
        // this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    addTask(type) {
      this.formData[type].taskIds.push('');
    },
    deleteTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds.splice(index, 1);
      this.delReceiveOrgMx(type, this.formData);
    },
    async selectTask(val, type, index) {
      if (!val) return;
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = val;
      await this.setReceiveOrgMx(val, type);
      this.delReceiveOrgMx(type, this.formData);
    },
    clearTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = '';
      this.delReceiveOrgMx(type, this.formData);
    },
    deleteRow(type, index) {
      this.receiveData[type].receiveConfig.splice(index, 1);
    },
    copyConfig(type) {
      this.setCopyReceiveConfig(this.$util.common.deepCopy(this.receiveData[type].receiveConfig));
      this.$Message.success('复制成功');
    },
    paste(type) {
      if (!this.copyReceiveConfig) return;
      this.receiveData[type].receiveConfig = this.$util.common.deepCopy(this.copyReceiveConfig);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      copyReceiveConfig: 'systemconfiguration/getCopyReceiveConfig',
    }),
    isEdit() {
      return this.action === 'edit';
    },
    isNetworkOpen() {
      return this.formData['0501'].markEnable === 1 && this.action === 'edit';
    },
    isFaceOpen() {
      return this.formData['0502'].markEnable === 1 && this.action === 'edit';
    },
    isVehicleOpen() {
      return this.formData['0503'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
.interface-exception {
  overflow-y: auto;
  .task-list {
    width: 310px;
    max-height: 300px;
    overflow-y: auto;
  }
  .table-module {
    clear: both;
    height: 260px;
  }
  .notification-method {
    label {
      display: block;
      margin-bottom: 5px;
    }
    &:nth-child(n + 2) {
      margin-left: 80px;
    }
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .message-content {
    margin-left: 10px;
    display: inline-block;
    border: 1px solid var(--border-input);
    padding: 0 10px;
    background-color: var(--bg-input);
    width: 760px;
    vertical-align: text-top;
    border-radius: 4px;
    span {
      color: var(--color-title);
    }
  }
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      color: var(--color-primary);
    }
    .not-copy {
      color: var(--color-btn-primary-disabled);
      cursor: not-allowed;
    }
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: var(--color-navigation-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        margin: 0 20px;
        .title {
          font-size: 16px;
          font-weight: 900;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: var(--bg-content);
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: var(--bg-content);
      }
    }
  }
}
</style>
