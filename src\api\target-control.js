import request from "@/libs/request";
import {
  compareService,
  manager,
  holographicArchives,
  service,
} from "./Microservice";

/**
 * 报警管理
 */
// 报警首页统计
export function alarmCount (data) {
  return request({
    url: compareService + "/alarm/count",
    method: "post",
    data,
  });
}
// 布控库列表
export function queryFaceLibList (data) {
  return request({
    url: manager + "/lib/face/queryFaceLibList",
    method: "post",
    data,
  });
}
// 查询人像静态库列表(内部)
export function faceQueryList (data) {
  return request({
    url: manager + "/lib/face/queryList",
    method: "post",
    data,
  });
}
// 报警首页列表
export function alarmPageList (data) {
  return request({
    url: compareService + "/alarm/pageList",
    method: "post",
    data,
  });
}

// 批量处理（包含单个）
export function batchHandle (data) {
  return request({
    url: compareService + "/alarm/handle/batchHandle",
    method: "post",
    data,
  });
}

// 处理历史
export function queryAlarmHandleList (data) {
  return request({
    url: compareService + "/alarm/handle/queryAlarmHandleList",
    method: "post",
    data,
  });
}

// 详情，地图位置
export function getDeviceBaseInfo (data) {
  return request({
    url: holographicArchives + "/device/getDeviceBaseInfo",
    method: "post",
    data,
  });
}

// 报警详情
export function alarmView (key) {
  return request({
    url: compareService + "/alarm/view/" + key,
    method: "get",
  });
}

/**
 * 报警管理-车辆
 */
// 报警首页列表-车辆
export function vehiclePageList (data) {
  return request({
    url: compareService + "/alarm/pageList/vehicle",
    method: "post",
    data,
  });
}
// 报警首页搜索-车辆
export function queryVehicleLibList (data) {
  return request({
    url: manager + "/lib/vehicle/queryVehicleLibList",
    method: "post",
    data,
  });
}
// 报警详情-车辆
export function vehicleView (key) {
  return request({
    url: compareService + "/alarm/view/vehicle/" + key,
    method: "get",
  });
}

// 批量处理（包含单个）-车辆
export function batchHandleVehicle (data) {
  return request({
    url: compareService + "/alarm/handle/batchHandle/vehicle",
    method: "post",
    data,
  });
}
// 处理历史-车辆
export function queryVehicleAlarmHandleList (data) {
  return request({
    url: compareService + "/alarm/handle/queryVehicleAlarmHandleList",
    method: "post",
    data,
  });
}

/**
 * 布控任务
 */
// 布控任务列表统计
export function taskCount (data) {
  return request({
    url: compareService + "/compare/task/count",
    method: "post",
    data,
  });
}
// 布控任务列表
export function taskPageList (data) {
  return request({
    url: compareService + "/compare/task/pageList",
    method: "post",
    data,
  });
}
// 布控任务列表
export function taskQueryList (data) {
  return request({
    url: compareService + "/compare/task/queryList",
    method: "post",
    data,
  });
}
// 布控任务新增
export function taskAdd (data) {
  return request({
    url: compareService + "/compare/task/add",
    method: "post",
    data,
  });
}
// 布控任务编辑
export function taskUpdate (data) {
  return request({
    url: compareService + "/compare/task/update",
    method: "post",
    data,
  });
}
// 批量修改布控状态
export function batchEdit (data) {
  return request({
    url: compareService + "/compare/task/status/batchEdit",
    method: "post",
    data,
  });
}
// 布控任务删除
export function taskRemove (id) {
  return request({
    url: compareService + "/compare/task/remove/" + id,
    method: "post",
  });
}

// 获取布控比对任务主详细信息
export function taskView (key) {
  return request({
    url: compareService + "/compare/task/view/" + key,
    method: "get",
  });
}

/**
 * 目标管控-车辆
 * @param {*} data
 * @returns
 */
// 布控任务列表
export function taskPageListVehicle (data) {
  return request({
    url: compareService + "/compare/task/pageList/vehicle",
    method: "post",
    data,
  });
}
// 布控任务新增
export function taskAddVehicle (data) {
  return request({
    url: compareService + "/compare/task/add/vehicle",
    method: "post",
    data,
  });
}

// 布控任务编辑
export function taskUpdateVehicle (data) {
  return request({
    url: compareService + "/compare/task/update/vehicle",
    method: "post",
    data,
  });
}

// 获取布控比对任务主详细信息
export function taskViewVehicle (key) {
  return request({
    url: compareService + "/compare/task/view/vehicle/" + key,
    method: "get",
  });
}

/**
 * 全局告警
 */

// 用户列表查询（报警通知）
export function userPageList (data) {
  return request({
    url: service + "/user/pageList",
    method: "post",
    data,
  });
}

// 设备列表（布控范围）
export function queryDeviceInfoPageList (data) {
  return request({
    url: holographicArchives + "/device/queryDeviceInfoPageList",
    method: "post",
    data,
  });
}

// 人脸列表（单体布控）
export function queryFaceLibPageList (data) {
  return request({
    url: manager + "/lib/face/queryFaceLibPageList",
    method: "post",
    data,
  });
}

// 库布控列表 - 人像
export function queryFaceLibPersonPageList (data) {
  return request({
    url: manager + "/lib/facePerson/queryFaceLibPersonPageList",
    method: "post",
    data,
  });
}
// 库布控列表 - 车辆
export function queryVehicleLibageList (data) {
  return request({
    url: manager + "/lib/vehicle/queryVehicleLibageList",
    method: "post",
    data,
  });
}
// 库布控列表 - 车辆
export function queryVehicleLibInfoPageList (data) {
  return request({
    url: manager + "/lib/vehicleLibInfo/queryVehicleLibInfoPageList",
    method: "post",
    data,
  });
}
// 布控任务审核
export function taskApply (data) {
  return request({
    url: compareService + "/compare/task/apply",
    method: "post",
    data,
  });
}

// 感知告警记录分页查询
export function sensory (data) {
  return request({
    url: compareService + "/alarm/pageList/sensory",
    method: "post",
    data,
  });
}

// 批量处理（包含单个）- 感知设备
export function batchHandleSensory (data) {
  return request({
    url: compareService + "/alarm/handle/batchHandle/sensory",
    method: "post",
    data,
  });
}

// 获取车辆归属地组织树
export function getCarPlateRegion (data) {
  return request({
    url: compareService + "/carPlateRegion/tree",
    method: "post",
    data,
  });
}

// 处理历史-车辆
export function querySensoryAlarmHandleList (data) {
  return request({
    url: compareService + "/alarm/handle/querySensoryAlarmHandleList",
    method: "post",
    data,
  });
}

// 设备列表（布控范围）
export function queryDevicePageList (data) {
  return request({
    url: service + '/device/pageList',
    method: 'post',
    data
  })
}