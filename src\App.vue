<template>
  <div id="app">
    <div id="sy" class="table-sy"></div>
    <router-view />
    <div class="config">
      <template v-for="(item, index) in sendList">
        <!-- 语义布控告警推送 -->
        <pushAlarmCard 
          v-if="item.compareType == 101"
          :key="index"
          @click.native="detailLLMAlarmFn(item)"
          :class="'position' + index"
          :alarmInfo="item"
          @allDel="allDel"
          @delAlarm="delAlarm"/>
        <vehicleCard
          v-else-if="item.compareType == 2"
          :key="index"
          @click.native="detailVehicleFn()"
          :class="'position' + index"
          :alarmInfo="item"
          @allDel="allDel"
          @delAlarm="delAlarm"
        />
        <configCard
          v-else
          :key="index + 'a'"
          :alarmInfo="item"
          :class="'position' + index"
          @allDel="allDel"
          @detailFn="detailFn"
          @delAlarm="delAlarm"
        />
      </template>
    </div>
    <webSocket @sendInfo="sendInfo" v-if="individuation.alarmConfig" />
    <alarmDetail
      v-if="detailShow"
      :showNext="false"
      :tableList="sendList"
      @hidePage="detailShow = false"
      :alarmInfo="alarmInfo"
      :tableIndex="0"
      ref="alarmDetail"
      @close="close"
    />
    <vehicleDetail
      v-if="detailVehicleShow"
      :showNext="false"
      :tableList="sendList"
      @hidePage="detailVehicleShow = false"
      :alarmInfo="alarmInfo"
      :tableIndex="0"
      ref="detailVehicle"
      @close="close"
    />
    <llmAlarmDetail
      v-if="llmDetailShow"
      :detailsInfo="llmAlarmInfo"
      @close="close"
    />
    <audio controls ref="audio" style="margin-left: -300px">
      <source :src="audioUrl" />
    </audio>

    <!-- 视频下载申请告警 -->
    <div class="config-apply">
      <template v-for="(item, index) in applyList">
        <applyCard
          @click.native="toApply"
          :key="index"
          :class="'position' + index"
          :applyInfo="item"
          @allDel="allDel"
          @delAlarm="delAlarm"
        />
      </template>
    </div>
    <webSocketApply @sendInfo="sendInfo2" />
    <!-- 提示框 -->
    <hl-modal v-model="tipVisible" title="公告" :r-width="680">
      <div class="content">
        <img src="@/assets/img/QBZ.jpg" alt="" />
      </div>
      <div slot="footer">
        <div></div>
      </div>
    </hl-modal>
    <!-- 小智 -->
    <gpt v-if="machine == 'qizhi' && xzObj == 1 && isLogin" />
    <manMachine v-if="machine == 'lingxi' && xzObj == 1 && isLogin" />
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import configCard from "@/views/target-control/components/config-card";
import vehicleCard from "@/views/target-control/components/vehicle-card";
import pushAlarmCard from "@/views/semantic-placement/alert-manager/components/push-alarm-card";
import llmAlarmDetail from "@/views/semantic-placement/alert-manager/components/alarm-detail.vue";
import watermark from "watermark-dom";
import alarmDetail from "@/views/target-control/components/alarm-detail.vue";
import vehicleDetail from "@/views/target-control/components/vehicle-detail.vue";
import webSocket from "./components/webSocket.vue";
import webSocketApply from "./components/webSocketApply.vue";
import applyCard from "./components/apply-card.vue";
import { queryParamDataByKeys } from "@/api/config";
import hlModal from "@/components/modal/index.vue";
import manMachine from "./components/man-machine";
import gpt from "./components/gpt";
import emitter, { alarmEventName } from "@/assets/js/alarmEventPub.js";
import { getRouteDomain, getAllTaskIds } from "@/api/monographic/base.js";
export default {
  components: {
    configCard,
    webSocket,
    alarmDetail,
    vehicleCard,
    vehicleDetail,
    pushAlarmCard,
    llmAlarmDetail,
    applyCard,
    webSocketApply,
    hlModal,
    manMachine,
    gpt,
  },
  data() {
    return {
      firstRequest: true,
      watermarkTxt: "",
      sendList: [],
      detailShow: false,
      detailVehicleShow: false,
      llmDetailShow:false,
      alarmInfo: {},
      alarmConfigInfo: {},
      llmAlarmInfo:{},
      audioUrl: "",
      muted: true,
      applyList: [],
      tipVisible: false,
      machine: machine,
      isLogin: false,
      taskIds: [],
      isDomain: false,
    };
  },
  computed: {
    ...mapGetters({
      targetObj: "systemParam/targetObj",
      semanticObj: "systemParam/getSemanticObj",
      userInfo: "userInfo",
      globalObj: "systemParam/globalObj",
      individuation: "individuation",
      xzObj: "systemParam/xzObj",
    }),
  },
  created() {
    this.setDownloadList();
    window.addEventListener("beforeunload", (e) => this.beforeunloadFn(e));
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", (e) => this.beforeunloadFn(e));
    localStorage.remove("showModal");
  },
  watch: {
    async "$route.path"(newVal) {
      if (newVal !== "/login") {
        this.isLogin = true;
        if (this.firstRequest) {
          this.firstRequest = false;
          await this.getSystemAllData();
          await this.getDictData();
        }
        this.$nextTick(() => {
          if (localStorage.getItem("showModal") == 1) {
            // this.tipVisible = true;  //登录成功公告提示框
            localStorage.setItem("showModal", 2);
          }
          // //       水印显示           姓名                警号
          const {
            watermarkSwitch,
            nameWatermarkSwitch,
            workCodeWatermarkSwitch,
          } = this.globalObj;
          const { username, workCode, loginIp, loginMac, orgVoList } =
            this.userInfo;
          let waterName = "";
          let waterOrgName = "";
          if (nameWatermarkSwitch) waterName = username;
          if (workCodeWatermarkSwitch) waterOrgName = orgVoList[0].orgName;
          let data = {
            // 水印修改
            name: waterName,
            loginIp: loginIp,
            loginMac: loginMac,
            orgName: waterOrgName,
          };
          if (watermarkSwitch) {
            this.loadSy(data);
          }
          // if (watermarkSwitch) {
          // 	if (nameWatermarkSwitch) this.watermarkTxt = name
          // 	if (workCodeWatermarkSwitch) this.watermarkTxt += workCode
          // 	watermark.load({
          // 		watermark_txt: this.watermarkTxt,
          // 		watermark_x_space: 200, //水印x轴间隔
          // 		watermark_y_space: 50,
          // 		watermark_angle: 30,
          // 		watermark_fontsize: '14px',
          // 		watermark_color: '#8E96A4', //水印字体颜色
          // 		watermark_alpha: 0.6, //水印透明度，要求设置在大于等于0.005
          // 		watermark_rows: 0, //水印行数
          // 		watermark_cols: 0,
          // 		watermark_width: 180, //水印宽度
          // 		watermark_height: 100, //水印长度
          // 		watermark_x: 100, //水印起始位置x轴坐标
          // 		watermark_y: 0,
          // 	})
          // 	this.$forceUpdate()
          // } else {
          // 	try {
          // 		watermark.remove()
          // 	} catch (error) { }
          // }
        });
        if (this.$route.path == "/user/mydownload") {
          this.applyList = [];
        }
      } else {
        this.isLogin = false;
        if (location.href.indexOf("#reloaded") == -1) {
          location.href = location.href + "#reloaded";
          location.reload();
        }
        watermark.remove();
        this.applyList = [];
      }
    },
  },
  methods: {
    ...mapActions({
      getSystemAllData: "systemParam/getSystemAllData",
      getDictData: "dictionary/getDictAllData",
      setDownloadList: "my-download/setDownloadList",
      cancleAll: "my-download/cancleAll",
    }),
    beforeunloadFn(e) {
      this.cancleAll();
      this._beforeUnload_time = new Date().getTime();
    },
    async init() {
      var param = ["ICBD_TARGET_CONTROL"];
      await queryParamDataByKeys(param).then((res) => {
        if (res.data.length > 0) {
          this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
        }
      });
    },
    async getTaskIds() {
      const res = await getAllTaskIds();
      this.taskIds = res?.data || [];
    },
    //
    async sendInfo(row) {
      // if (this.alarmConfigInfo.alarmLevelConfig == undefined) await this.init();
      let rows = JSON.parse(row);
      this.isDomain = getRouteDomain();
      if (this.isDomain && this.taskIds.length === 0) {
        await this.getTaskIds();
      }
      if (this.isDomain && !this.taskIds?.includes(rows.taskId)) {
        return;
      }
      if (rows.compareType == 101) {
        const llmCompareAlarmVo = rows.llmCompareAlarmVo
        let info = this.semanticObj.llmAlarmLevelConfig.find(
          (item) => item.alarmLevel == llmCompareAlarmVo.taskLevel
        );
        rows.bgIndex = Number(info.alarmColour);
        // this.alarmInfo = rows
        this.sendList.unshift({...rows,...llmCompareAlarmVo});
        if (info.soundType == 1) {
          this.$refs.audio.src = info.defaultUrl;
        } else {
          this.$refs.audio.src = info.soundUrl;
        }
        if (info.isSound == "1") {
          this.$refs.audio.play();
        }
      } else {
        let info = this.targetObj.alarmLevelConfig.find(
          (item) => item.alarmLevel == rows.taskLevel
        );
        rows.bgIndex = Number(info.alarmColour);
        // this.alarmInfo = rows
        this.sendList.unshift(rows);
        if (info.soundType == 1) {
          this.$refs.audio.src = info.defaultUrl;
        } else {
          this.$refs.audio.src = info.soundUrl;
        }
        if (info.isSound == "1") {
          this.$refs.audio.play();
        }
      }
      // 是否是子应用
      if (this.isDomain) {
        emitter.emit(alarmEventName, rows);
      }
      // this.$forceUpdate()
    },
    async sendInfo2(row) {
      var rows = JSON.parse(row);
      this.applyList.unshift(JSON.parse(rows.msg));
    },
    delAlarm() {
      this.sendList.shift();
      this.applyList.shift();
      setTimeout(() => {
        this.alarmInfo = this.sendList[0];
        this.applyInfo = this.applyList[0];
      }, 50);
      // this.$Message.success('删除成功')
      this.$forceUpdate();
    },
    close() {
      this.detailVehicleShow = false;
      this.detailShow = false;
      this.llmDetailShow = false;
    },
    detailFn(row, index) {
      this.tableIndex = index;
      this.detailShow = true;
      this.alarmInfo = row;
    },
    detailVehicleFn(row, index) {
      this.tableIndex = index;
      this.detailVehicleShow = true;
      this.alarmInfo = row;
    },
    detailLLMAlarmFn(row) {
      this.llmDetailShow = true;
      this.llmAlarmInfo = {...row };
    },
    allDel() {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定一键关闭吗？`,
        onOk: () => {
          this.sendList = [];
          this.applyList = [];
        },
      });
    },
    // 跳转到下载审核页面
    toApply() {
      const { href } = this.$router.resolve({
        path: "/user/mydownload",
      });
      window.open(href, "_blank");
    },
    loadSy(dataObj) {
      var config = {
        tw: 350, //内容高度
        th: 150, // 内容宽度
        c: 80, // 为了避免页面下方有多余的空白, 少显示一行。所以在计算每个水印高度的时候，需要加个差值，使水印分布的更均匀（一般为内容高度的一半即可）
      };
      var dom = document.querySelector(".table-sy");
      var data = {
        name: dataObj.name,
        loginIp: dataObj.loginIp,
        loginMac: dataObj.loginMac,
        orgName: dataObj.orgName,
      };
      var width = document.body.offsetWidth;
      dom.style.width = width + "px";
      var height = document.body.offsetHeight;
      dom.style.height = height + "px";
      var wnum = parseInt(width / config.tw) || 1;
      var hnum = parseInt(height / config.th) || 1;
      var wc = (width - wnum * config.tw) / wnum - 1 || 0;
      var hc = (height - hnum * config.th + config.c) / hnum - 1 || 0;
      var num = wnum * (hnum - 1);
      var html = [];
      for (var i = 0; i < num; i++) {
        let className = "";
        if ((i + 1) % 5 == 2 || (i + 1) % 5 == 4) {
          className = "evenNumber";
        }
        let name = data.name ? data.name + "<br/>" : "",
          loginIp = data.loginIp ? data.loginIp + "<br/>" : "",
          loginMac = data.loginMac ? data.loginMac + "<br/>" : "",
          orgName = data.orgName ? data.orgName : "";
        html.push(
          '<div class="' +
            className +
            '" style="width: ' +
            (config.tw + wc) +
            "px;height: " +
            (config.th + hc) +
            'px">',
          name,
          loginIp,
          loginMac,
          orgName,
          "</div>"
        );
        // html.push('<div style="width: ' + (config.tw + wc) + 'px;height: ' + (config.th + hc) + 'px">', data.name, '<br/>', data.loginIp, '<br/>', data.loginMac,'<br/>', data.orgName, '</div>');
      }
      dom.innerHTML = html.join("");
    },
  },
};
</script>

<style lang="less" scoped>
#app {
  height: 100%;
  position: relative;
  overflow: hidden;
}
.config {
  .position0 {
    right: 26px;
    bottom: 26px;
    z-index: 999;
  }
  .position1 {
    right: 20px;
    bottom: 20px;
    z-index: 998;
  }
  /deep/ .ivu-icon-ios-close {
    font-size: 30px;
    line-height: 40px;
    cursor: pointer;
  }
}
.config-apply {
  cursor: pointer;
  .position0 {
    right: 26px;
    bottom: 46px;
    z-index: 999;
  }
  .position1 {
    right: 20px;
    bottom: 40px;
    z-index: 998;
  }
  /deep/ .ivu-icon-ios-close {
    font-size: 30px;
    line-height: 40px;
    cursor: pointer;
  }
}
/deep/ .table-sy {
  width: 100%;
  height: 100%;
  position: absolute;
  pointer-events: none;
  font-size: 14px;
  color: #8e96a4;
  z-index: 99999999999;
  opacity: 0.6;
  display: flex;
  flex-wrap: wrap;
}
/deep/ .table-sy > div {
  /*width: 300px;*/
  /*height: 150px;*/
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(-30deg);
  // float: left;
}
/deep/.table-sy > .evenNumber {
  margin-top: 100px;
}
.content {
  img {
    width: 100%;
    height: 650px;
  }
}
</style>
