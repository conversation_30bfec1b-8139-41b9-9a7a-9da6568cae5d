<template>
  <div id="app">
    <nav-header v-if="!needHeader" :menu-list="routerTreeList"></nav-header>
    <layout-content v-if="!needHeader">
      <keep-alive :include="cacheRouterName">
        <router-view class="height-full"></router-view>
      </keep-alive>
    </layout-content>
    <router-view v-else :class="{ 'wrapper-full': !needHeader }"></router-view>
    <sso-loading v-if="thirdPartyLoading"></sso-loading>
    <!-- 小智 -->
    <xiaozhi v-if="getToken && isShowXiaoZhi"></xiaozhi>
  </div>
</template>

<style lang="less" scoped>
#app {
  height: 100%;
  position: relative;
}
</style>
<script>
import { mapActions, mapGetters, mapMutations } from 'vuex';
import logOutMixin from '@/mixins/logout-mixin';
import { initTheme, allThemeList } from '@/style/theme';
export default {
  mixins: [logOutMixin],
  data() {
    return {};
  },
  created() {
    this.setTheme();
    // /* 多系统免登录跳转 */
    let url = window.location.href;
    if (url.includes('refresh_token')) {
      let token = url.substr(url.indexOf('refresh_token') + 14);
      window.sessionStorage.setItem('token', token);
      this.$router.push({ name: this.getPortalConfig });
    }

    this.getChromeVersion();
  },
  mounted() {
    this.setShowXizoZhi();
  },

  methods: {
    ...mapMutations({
      setToken: 'user/setToken',
    }),
    ...mapActions({
      startWebsock: 'websocket/startWebsock',
      setShowXizoZhi: 'common/setShowXizoZhi',
      getPortalConfig: 'common/getPortalConfig'
    }),
    ...mapGetters({
      getPortalConfig: 'common/getPortalConfig'
    }),

    // 退出登陆
    websoketlogOut() {
      // logOutMixin中方法
      this.logOutMx();
    },

    // 获取谷歌浏览器版本
    getChromeVersion() {
      let userAgent = navigator.userAgent.split(' ');
      // 不是webkit的内核
      if (navigator.userAgent.indexOf('WebKit') === -1) {
        this.chromeVersionTips('您使用的浏览器版本过低，为了更好地体验请下载谷歌浏览器,');
        return;
      }
      // 谷歌浏览器版本小于50
      let chromeVersion = '';
      for (let i = 0; i < userAgent.length; i++) {
        if (/chrome/i.test(userAgent[i])) chromeVersion = userAgent[i];
      }
      if (Number(chromeVersion.split('/')[1].split('.')[0]) <= 50) {
        this.chromeVersionTips('您使用的谷歌浏览器版本过低，为了更好地体验请将浏览器升级版本,');
      }
    },
    chromeVersionTips(text) {
      this.$Message.warning({
        duration: 20,
        closable: true,
        render: (h) => {
          return h('p', {}, [
            text,
            h(
              'a',
              {
                class: 'font-active-color ml-sm pointer',
                attrs: {
                  href: '/temp/Chrome64.exe',
                },
              },
              ['点击下载'],
            ),
          ]);
        },
      });
    },
    // 设置主题版本
    setTheme() {
      // 若localStorage中拿不到theme，则初始化allThemeList第一个为主题
      let localStorageTheme = localStorage.getItem('theme');
      if (localStorageTheme && allThemeList.findIndex((item) => item.theme === localStorageTheme) !== -1) {
        initTheme(localStorageTheme);
      } else {
        initTheme(allThemeList[0].theme);
      }
    },
  },
  watch: {
    // 账号在别处登陆或者token失效
    getLogOutFlag(val) {
      val ? this.websoketlogOut() : this.$store.commit('websocket/setLogOut', false);
    },
  },
  computed: {
    ...mapGetters({
      getLogOutFlag: 'websocket/getLogOutFlag',
      cacheRouterList: 'tabs/getCacheRouterList',
      routerTreeList: 'permission/getRouterTreeList',
      systemConfig: 'common/getSystemConfig',
      thirdPartyLoading: 'user/getThirdPartyLoading',
      getToken: 'user/getToken',
      isShowXiaoZhi: 'common/getIsShowXiaoZhi'
    }),
    needHeader() {
      const fullScreenRoutes = ['login', 'archives', 'error_401', 'portal'];
      return !this.$route.name || fullScreenRoutes.includes(this.$route.name);
    },
    cacheRouterName() {
      return this.cacheRouterList.map((row) => row.name);
    },
    isLogin() {
      return this.$route.name === 'login';
    },
  },
  props: {},
  components: {
    NavHeader: require('@/components/nav-header.vue').default,
    LayoutContent: require('@/components/layout-content.vue').default,
    SsoLoading: require('@/views/login/components/SsoLoading.vue').default,
    xiaozhi: require('./components/gpt').default
  },
};
</script>
