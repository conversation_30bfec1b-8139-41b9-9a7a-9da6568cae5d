<template>
  <div>
    <component
      v-for="(component, index) in componentList"
      :is="component.componentName"
      :ref="component.componentRef"
      :key="index"
      v-bind="component.componentProps"
      v-on="component.componentEvent"
    ></component>

    <Modal
        class="video-modal"
        ref="videoModalRef"
        title="查看设备"
        width="70%"
        v-model="showVideo"
        draggable
        sticky
        scrollable
        footer-hide
        :styles="{ height: '80%', left: 0 }"
        :mask="false"
        @on-cancel="beforeClose"
    >
      <DeviceList ref="deviceListRef" />
    </Modal>

  </div>
</template>
<script lang="jsx">
import axios from "axios";
import {mapGetters, mapMutations} from "vuex";
import { QSGptMain } from "@/gpt/gpt.main.js";
import equipmentassets from '@/config/api/equipmentassets';
import Statistic from "./components/statistic.vue";
import NoData from "./components/nodata.vue";
import Refresh from "./components/refresh";
import DeviceList from "./components/device-list";

export default {
  props: {},
  data() {
    return {
      gptMain: null,
      componentList: [],

      archiveType: 1, // 1: 人脸，2：设备， 3：人员轨迹
      imageRecognitionVisible: false,
      dataCopper: [],
      answerParams: {},

      showVideo: false,
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  mounted() {
    this.init();
  },
  methods: {
    ...mapMutations({ setTrajectoryParams: "gpt/setTrajectoryParams" }),
    async getGptConfig() {
      try {
        const res = await axios.get("/js/gpt/gptconfig.json");
        return res.data;
      } catch (err) {
        console.error(err, "err");
      }
    },

    async init() {
      const gptConfig = await this.getGptConfig();
      this.gptMain = new QSGptMain({ gptDom: document.body, gptConfig });
      this.gptMain.init();
      this.addAnswerFilter();
      this.addHistoryFilter();
    },
    beforeClose() {
      this.showVideo = false;
    },
    openDeviceList(list, total, param) {
      this.showVideo = true;
      this.$refs.deviceListRef.init(list, total, param);
    },
    addAnswerFilter() {
      this.gptMain.addAnswerFilter(async (answer) => {
        try {
          console.log(answer, "answer");
          const answerContent =
            typeof answer === "string" ? JSON.parse(answer) : answer;
          const params = answerContent.data;
          this.answerParams = { ...params };
          let answerObj = null;
          switch (answerContent.type) {
            case "query_device":
              // 查设备的档案
              answerObj = await this.queryDeviceList(params, { hasOpen: true });
              return answerObj;
            default:
              break;
          }
        } catch (err) {
          console.error(err, "err");
        } finally {
          this.gptMain.setAnswerLoading({
            loading: false,
          });
        }
      });
    },

    addHistoryFilter() {
      this.gptMain.addHistoryFilter(async (history) => {
        try {
          const historyJson = JSON.parse(history);
          switch (historyJson.type) {
            case "query_device":
              if (historyJson.domParams) {
                return await this.createStatistic(...historyJson.domParams);
              } else {
                return await this.createNoData();
              }
          }
        } catch {}
      });
    },

    /**
     * 查询设备列表
     * @param {*} params 查询参数
     * @param {*} open 是否显示可打开按钮
     * @param {*} play 立即打开视频监控页面
     */
    async queryDeviceList(params, { hasOpen = false, play = false }) {
      try {
        const param = {
          orgCode: this.defaultSelectedOrg.orgCode,
          pageNumber: 1,
          pageSize: 10,
          ...params,
        };
        const res = await this.$http.post(equipmentassets.getPageDeviceList, param);
        this.gptMain.setAnswerLoading({
          text: `正在调取${param.deviceName}的档案`,
          loading: true,
        });
        const list = res.data.data.entities;
        const total = res.data.data.total
        return await this.createStatistic({
          list:list,
          total: total,
          param: param,
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },

    async createNoData(props = {}) {
      const component = await this.createComponent({ name: "NoData", props });
      return {
        dom: component.$el,
      };
    },

    async createStatistic({list, total, param}, onEvent) {
      if (list.length === 0) {
        return await this.createNoData();
      }
      const component = await this.createComponent({
        name: "Statistic",
        props: {
          list: list,
          total: total,
          param: param,
        },
        onEvent: {
          openDeviceList: () => {
            this.openDeviceList(list, total, param);
          },
        },
      });
      return {
        dom: component.$el,
        domParams: [...arguments],
      };
    },
    async createComponent({ name, props = {}, onEvent = {} }) {
      const length = this.componentList.length;
      this.componentList.push({
        componentName: name,
        componentProps: { ...props },
        componentEvent: { ...onEvent },
        componentRef: `${name}${length}`,
      });
      await this.$nextTick();
      return this.$refs[`${name}${length}`][0];
    },
  },
  watch: {},
  components: {
    Statistic,
    NoData,
    DeviceList,
    Refresh
  },
};
</script>
<style scoped lang="less">
.video-modal {
  /deep/ .ivu-modal-wrap {
    z-index: 99999 !important;
  }
  /deep/ .ivu-modal {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    right: 0 !important;
    position: relative !important;
  }
  /deep/ .ivu-modal-content {
    position: absolute;
    width: 620px;
    top: 30px;
    right: 0;
  }
  /deep/ .ivu-modal-content {
    left: 10px;
    overflow: hidden;
  }
  /deep/ .ivu-modal-header {
    background: linear-gradient(214deg, #2ee0fc 0%, #2c48ff 100%);
    .ivu-modal-header-inner {
      color: #fff;
    }
  }
}
.image-recognition {
  /deep/ .ivu-modal-wrap {
    z-index: 99999 !important;
  }
  /deep/ .ivu-modal {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    right: 0 !important;
    position: relative !important;
  }
  /deep/ .ivu-modal-content {
    position: absolute;
    right: 20px;
    width: 620px;
    top: 30px;
  }
}
</style>
