<template>
  <div>
    <component
      v-for="(component, index) in componentList"
      :is="component.componentName"
      :ref="component.componentRef"
      :key="index"
      v-bind="component.componentProps"
      v-on="component.componentEvent"
    ></component>
    <!-- 图片上传成功后，选择图片 -->
    <image-recognition
      v-if="imageRecognitionVisible"
      ref="imageRecognition"
      class="image-recognition"
      :data-copper="dataCopper"
      :temp-url="tempUrl"
      @getMinImgUrl="getMinImgUrl"
    />
    <Modal
      class="video-modal"
      ref="videoModalRef"
      title="视频播放"
      width="70%"
      v-model="showVideo"
      draggable
      sticky
      scrollable
      footer-hide
      :styles="{ height: '80%', left: 0 }"
      :mask="false"
      @on-cancel="beforeClose()"
    >
      <Exscreen ref="exscreenRef" />
    </Modal>
    <!-- 多模态详情 -->
    <DetailMutiAnalysis
      v-if="isShowMutilAnalysis"
      ref="detailMutiAnalysisRef"
      :tableList="multiDataList"
      :isNoPage="true"
      @close="isShowMutilAnalysis = false"
      class="multi-analysis-card-detail"
    ></DetailMutiAnalysis>
  </div>
</template>
<script lang="jsx">
import axios from "axios";
import { mapMutations } from "vuex";
import { QSGptMain } from "@/gpt/gpt.main.js";
import UiListCard from "@/components/ui-list-card";
import ImageRecognition from "@/components/ui-upload-img/image-recognition";
import NoData from "./components/nodata.vue";
import Statistic from "./components/statistic.vue";
import Exscreen from "./components/exscreen";
import Refresh from "./components/refresh";
import Graph from "./components/graph";
import TajectoryCard from "./components/trajectory-card";

import { getPersonPageList } from "@/api/realNameFile";
import { queryVehicleList } from "@/api/vehicleArchives";
import { queryDeviceInfoPageList } from "@/api/device";
import { picturePick, queryDeviceList } from "@/api/wisdom-cloud-search";
import { queryPlaceAndKeys } from "@/api/operationsOnTheMap";
import { llmStructureSearchPageList } from "@/api/multimodal-analysis";
import { getKgSummary, getKgDetective } from "@/api/number-cube";
import LLMCard from "@/views/multimodal-analysis/multimodal-analysis-lib/components/result-card.vue";
import DetailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
import { tajectoryTypeMap, archivesTypeMap, transformField } from "./enum";
import { getToken } from "@/libs/configuration/util.common";
import AudioHandler from "@/util/modules/audio.js";
import { dateTime } from "@/util/modules/common";

export default {
  props: {},
  data() {
    return {
      gptMain: null,
      componentList: [],

      archiveType: 1, // 1: 人脸，2：设备， 3：人员轨迹
      imageRecognitionVisible: false,
      dataCopper: [],
      imageDataHandleResolve: () => {},
      answerParams: {},

      showVideo: false,
      needOpenRecord: {}, // 意图为打开档案 需要打开的档案
      isShowMutilAnalysis: false,
      multiDataList: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    ...mapMutations({
      setTrajectoryParams: "gpt/setTrajectoryParams",
      setNumCodeParams: "gpt/setNumCodeParams",
    }),
    async getGptConfig() {
      try {
        const res = await axios.get("/js/gpt/gptconfig.json");
        return res.data;
      } catch (err) {
        console.error(err, "err");
      }
    },

    async init() {
      const gptConfig = await this.getGptConfig();
      this.gptMain = new QSGptMain({ gptDom: document.body, gptConfig });
      this.gptMain.init(getToken());
      const audioHandler = new AudioHandler();
      this.gptMain.setAudioToText(
        audioHandler.startAudio,
        audioHandler.endAudio
      );
      this.addComponentFilter();
      this.addAnswerFilter();
      this.addHistoryFilter();
      this.addAnswerCopy();
    },
    addComponentFilter() {
      this.gptMain.setComponentFilter(async (componentType, componentData) => {
        let cardList = null;
        const div = document.createElement("div");
        switch (componentType) {
          case "archive":
            cardList = await this.createUiListCard(
              {
                type: "people",
                data: componentData,
                index: 0,
              },
              {
                archivesDetailHandle: () => {
                  this.archivesDetailHandle({
                    type: "people",
                    archiveNo: componentData.archiveNo,
                    deviceId: componentData?.deviceId || "",
                    plateNo: componentData.plateNo,
                  });
                },
              },
              "UiListCard"
            );
            div.appendChild(cardList.dom);
            break;
          case "capture":
            if (componentData?.length > 0) {
              for (let i = 0; i < componentData.length; i++) {
                cardList = await this.createUiListCard(
                  {
                    type: "tajectory",
                    data: {
                      ...componentData[i],
                      traitImg: componentData[i].sceneImg,
                    },
                    index: i,
                  },
                  {},
                  "TajectoryCard"
                );
                div.appendChild(cardList.dom);
              }
            }
            break;
          case "relation":
            cardList = await this.createGraphCard({
              hasData: true,
              relationRes: {
                data: {
                  entitys: componentData.entities,
                  relations: componentData.relations,
                },
              },
            });
            div.appendChild(cardList.dom);
            div.style.position = "relative";
            div.style.width = "20vw";
            break;
          case "llm":
            cardList = await this.createComponent({
              name: "LLMCard",
              props: {
                data: { ...componentData },
                style: { marginBottom: "10px" },
              },
              onEvent: {
                // getDetailInfo: () => {
                //   this.multiDataList = [{ ...item }];
                //   this.isShowMutilAnalysis = true;
                //   this.$nextTick(() => {
                //     this.$refs.detailMutiAnalysisRef.showList(0);
                //   });
                // },
              },
            });
            div.appendChild(cardList.$el);
            break;
        }
        return div;
      });
    },

    addAnswerFilter() {
      this.gptMain.addAnswerFilter(async (answer) => {
        try {
          // answer = {};
          // answer.type = "open_by_llm";
          // answer.type = "query_by_llm";
          // answer.data = {
          //   keyword: "黑衣服",
          // };
          let answerContent = null;
          try {
            answerContent =
              typeof answer === "string" ? JSON.parse(answer) : answer;
          } catch (e) {
            return null;
          }

          const params = answerContent.data;
          this.answerParams = { ...params };
          let answerObj = null;
          const segmentation = answerContent.type.split("_");
          switch (answerContent.type) {
            // case "query_portrait_archives":
            //   this.archiveType = 1;
            //   // 查人的档案
            //   answerObj = await this.queryFaceList(params);
            //   return answerObj;
            // case "open_portrait_archives":
            //   this.archiveType = 1;
            //   // 打开人的档案
            //   answerObj = await this.queryFaceList(params);
            //   this.archivesDetailHandle(this.needOpenRecord)
            //   return answerObj;
            // case "query_portrait_archives_by_pic":
            //   this.archiveType = 1;
            //   // 查人的档案（图片）
            //   answerObj = await this.picturePick(1);
            //   return answerObj;
            // case "open_portrait_archives_by_pic":
            //   this.archiveType = 1;
            //   // 打开人的档案（图片）
            //   answerObj = await this.picturePick(1);
            //   this.archivesDetailHandle(this.needOpenRecord)
            //   return answerObj;
            // case "query_vehicle_archives":
            //   // 查车的档案
            //   answerObj = await this.queryVehicleList(params);
            //   return answerObj;
            // case "open_vehicle_archives":
            //   // 打开车的档案
            //   answerObj = await this.queryVehicleList(params);
            //   this.archivesDetailHandle(this.needOpenRecord)
            //   return answerObj;
            // case "query_vehicle_archives_by_pic":
            //   // 查车的档案(图片)
            //   this.archiveType = 2;
            //   answerObj = await this.picturePick(2);
            //   return answerObj;
            // case "open_vehicle_archives_by_pic":
            //   // 打开车的档案(图片)
            //   this.archiveType = 2;
            //   answerObj = await this.picturePick(2);
            //   this.archivesDetailHandle(this.needOpenRecord)
            //   return answerObj;
            // case "query_place_archives":
            //   // 查询场所档案
            //   answerObj = await this.queryPlaceList(params);
            //   return answerObj;
            // case "open_place_archives":
            //   // 打开场所档案
            //   answerObj = await this.queryPlaceList(params);
            //   this.archivesDetailHandle(this.needOpenRecord)
            //   return answerObj;

            case "query_device":
              // 查设备的档案
              answerObj = await this.queryDeviceList(params, { hasOpen: true });
              return answerObj;
            case "open_device":
              // 打开设备
              answerObj = await this.queryDeviceList(params, {
                hasOpen: false,
                play: true,
              });
              return answerObj;
            // case "query_portrait_trajectory":
            //   // 查询人员轨迹
            //   this.archiveType = 3;
            //   answerObj = await this.getMinImgUrl(params);
            //   return answerObj;
            // case "query_vehicle_trajectory":
            //   // 查询车辆轨迹
            //   this.archiveType = 4;
            //   answerObj = await this.getMinImgUrl(params);
            //   return answerObj;
            // case "query_portrait_trajectory_by_pic":
            //   // 以图查询人员轨迹
            //   this.archiveType = 3;
            //   answerObj = await this.picturePick(1);
            //   return answerObj;
            // case "query_vehicle_trajectory_by_pic":
            //   // 以图查询车辆轨迹
            //   this.archiveType = 4;
            //   answerObj = await this.picturePick(2);
            //   return answerObj;
            case "search_all_by_keyword":
              answerObj = await this.queryCloudPageCard(params, true);
              return answerObj;
            // case "query_vid_relation_by_pic":
            //   // 查询人员关系 图片
            //   this.archiveType = 5;
            //   answerObj = await this.picturePick(1, answerContent.type);
            //   return answerObj;
            // case "query_person_relation":
            //   // 查询人员关系 身份证
            //   answerObj = await this.numCodePersonRelationIdCard(
            //     answerContent.type,
            //     `已为${params.idcard}查询关系`
            //   );
            //   return answerObj;
            // default:
            //   return await this.querRelationCard(answerContent);
          }
          // 档案意图分词
          if (segmentation.includes("archives"))
            return await this.querArchivesCard(params, segmentation);
          // 轨迹意图分词
          if (segmentation.includes("trajectory"))
            return await this.querTajectoryCard(params, segmentation);
          // 关系意图分词
          if (segmentation.includes("relation"))
            return await this.querRelationCard(answerContent);
          // 多模块意图分词
          if (segmentation.includes("llm"))
            return await this.querLLmCard(answerContent);
          return {};
        } catch (err) {
          console.error(err, "err");
        } finally {
          this.gptMain.setAnswerLoading({
            loading: false,
          });
        }
      });
    },

    addHistoryFilter() {
      this.gptMain.addHistoryFilter(async (history) => {
        try {
          const historyJson = JSON.parse(history);
          const segmentation = historyJson.type.split("_");
          switch (historyJson.type) {
            case "query_portrait_archives":
            case "open_portrait_archives":
            case "query_portrait_archives_by_pic":
            case "open_portrait_archives_by_pic":
            case "query_vehicle_archives":
            case "open_vehicle_archives":
            case "query_vehicle_archives_by_pic":
            case "open_vehicle_archives_by_pic":
            case "query_place_archives":
            case "open_place_archives":
            case "query_device":
              if (historyJson.domParams) {
                return await this.createArchive(...historyJson.domParams);
              } else {
                return await this.createNoData();
              }
            case "open_device":
              if (historyJson.domParams) {
                return await this.createArchive({
                  ...historyJson.domParams[0],
                  hasOpen: true,
                });
              } else {
                return await this.createNoData();
              }
            case "search_all_by_keyword":
              return {
                ...(await this.queryCloudPageCard(params)),
              };
          }
          if (segmentation.includes("trajectory")) {
            if (segmentation.includes("open")) {
              return await this.openTajectoryCard(
                { ...historyJson.domParams[0] },
                false
              );
            } else
              return await this.createTajectoryArchive(
                ...historyJson.domParams
              );
          }
          if (segmentation.includes("relation"))
            return this.addHistoryFilterRelation(historyJson);
          // 多模块意图分词
          if (segmentation.includes("llm"))
            return this.addHistoryFilterLLM(historyJson);
        } catch {}
      });
    },
    addAnswerCopy() {
      this.gptMain.setAnswerCopy((history) => {
        // 不同意图下的复制
        const { text, domParams } = history;
        try {
          const historyJson = JSON.parse(text);
          const segmentation = historyJson.type.split("_");
          // 返回字符串
          return "该意图复制内容";
        } catch {
          // 不能被JSON化久直接返回原本的文本
          return text;
        }
      });
    },
    // 全景智搜
    async queryCloudPageCard({ keyword }, hasOpen) {
      return await this.queryPageCard(
        {
          text: `已为${keyword}查询搜索内容`,
          clickText: "打开全景智搜",
          pageName: "cloud-default-page",
          params: {
            type: 1,
            keyWord,
          },
        },
        hasOpen
      );
    },
    async queryPageCard(
      { text, clickText = "打开", pageName = "", params, path = "" },
      hasOpen
    ) {
      !!hasOpen && this.goSearchPage(pageName, path, { ...params });
      return await this.createRefresh({
        text,
        clickText,
        clickFun: () => {
          this.goSearchPage(pageName, path, { ...params });
        },
      });
    },
    async querRelationCard(answerContent) {
      // 查询人员关系 图片
      const { type, data } = answerContent;
      const segmentation = type.split("_");
      const picSerch = segmentation.includes("pic");
      if (picSerch) {
        this.archiveType = 5;
        const answerObj = await this.picturePick(
          segmentation[1] === "person" ? "1" : "2",
          type
        );
        return answerObj;
      } else {
        const answerObj = await this.numCodePersonRelationIdCard(
          type,
          `已为${data.keyword}查询关系`
        );
        return answerObj;
      }
    },
    // 查询多模块内容
    async querLLmCard(param) {
      const { type, data } = param;
      const { startDate = "", endDate = "", keyword = "" } = data;
      const segmentation = type.split("_");
      const isOpen = segmentation.includes("open");
      // 打开多模态
      if (isOpen) {
        return await this.queryPageCard(
          {
            text: `正在为您打开多模态解析`,
            clickText: "打开多模态解析",
            path: "/multimodal-analysis/multimodal-analysis-lib",
            params: {
              endTime: endDate,
              startTime: startDate,
              keyWords: keyword,
            },
          },
          true
        );
      } else {
        try {
          // 查询多模态内容
          const param = {
            endTime: endDate,
            startTime: startDate,
            keyWords: keyword,
            pageNumber: 1,
            pageSize: 3,
            searchType: 1, // 只有自然语义搜索
          };
          this.gptMain.setAnswerLoading({
            text: `正在查询多模态内容`,
            loading: true,
          });
          const res = await llmStructureSearchPageList(param);
          const list = res.data.entities;
          return await this.createLLmCard({
            list,
            total: res.data.total,
            param,
          });
        } catch (e) {
          console.log(e);
        } finally {
          this.gptMain.setAnswerLoading({
            loading: false,
          });
        }
      }
    },
    // 添加历史
    async addHistoryFilterLLM(historyJson) {
      const segmentation = historyJson.type.split("_");
      const { startDate = "", endDate = "", keyword = "" } = historyJson.data;
      if (segmentation.includes("open")) {
        return await this.queryPageCard(
          {
            text: `正在为您打开多模态解析`,
            clickText: "打开多模态解析",
            path: "/multimodal-analysis/multimodal-analysis-lib",
            params: {
              endTime: endDate,
              startTime: startDate,
              keyWords: keyword,
            },
          },
          false
        );
      } else {
        const param = {
          endTime: endDate,
          startTime: startDate,
          keyWords: keyword,
          pageNumber: 1,
          pageSize: 3,
          searchType: 1, // 只有自然语义搜索
        };
        const res = await llmStructureSearchPageList(param);
        const list = res.data.entities;
        return await this.createLLmCard({
          list,
          total: res.data.total,
          param,
        });
      }
    },
    displayMessage(data) {
      this.gptMain.displayMessage(data);
    },
    getRelationGraph(type) {
      const that = this;
      this.$eventBus.$emit("getSaveGraphData", async ({ edges, nodes }) => {
        const newEdges = JSON.parse(JSON.stringify(edges));
        newEdges?.forEach((item) => {
          const properties = item?.ext?.detail?.properties || {};
          const timeKey = Object.keys(properties).filter((key) =>
            key.endsWith("_time")
          );
          timeKey.forEach((key) => {
            if (properties[key]) {
              properties[key] = dateTime(properties[key]);
            }
          });
        });
        const request = type === 1 ? getKgSummary : getKgDetective;
        const res = await request({
          kgData: JSON.stringify({ edges: newEdges, nodes }),
        });
        const data = res.data;
        let thinkContent = "";
        let content = "";
        // 提取<think>标签内的内容
        if (type === 1) {
          const thinkRegex = /<think>([\s\S]*?)<\/think>/;
          const thinkContentMatch = data.match(thinkRegex);
          thinkContent = thinkContentMatch ? thinkContentMatch[1] : "";
          content = data.replace(thinkRegex, "");
        } else {
          thinkContent = data.reasoning;
          content = JSON.stringify(data.suspect || []);
        }
        let text = "";
        const deepThinkArray = [
          {
            type: "reasoning_content",
            content: thinkContent,
          },
          {
            type: "content",
            content: content,
          },
        ];
        // if(type === 1) {
        //   const thinkRegex =  /<[\/]*think>/g
        //   text = res.data?.replace(thinkRegex, '')
        // } else{
        //   text = res.data?.join("");
        // }
        that.displayMessage({ thinking: true, deepThinkArray });
      });
    },
    renderDeepGraph() {
      const getRelationGraph = this.getRelationGraph;
      return {
        functional: true,
        props: [],
        render(h, context) {
          return [
            h(
              "span",
              {
                class: "click-text",
                on: {
                  click: () => getRelationGraph(1),
                },
              },
              ["关系描述"]
            ),
            h(
              "span",
              {
                class: "click-text",
                on: {
                  click: () => getRelationGraph(2),
                },
              },
              ["关系研判"]
            ),
          ];
        },
      };
    },
    goNumCubeParams(segmentation, historyJson) {
      return {
        type: segmentation[1],
        picSerch: segmentation.includes("pic"),
        info: { ...historyJson.data },
      };
    },
    async addHistoryFilterRelation(historyJson, hasOpen) {
      const segmentation = historyJson.type.split("_");
      if (!segmentation.includes("relation")) return {};
      const numCodeParams = this.goNumCubeParams(segmentation, historyJson);
      const openPage = segmentation.includes("open");
      openPage && hasOpen && this.goNumCube({ ...numCodeParams });
      return openPage
        ? {
            ...(await this.createRefresh({
              text: historyJson.text,
              clickText: "打开关系",
              extComponent: this.renderDeepGraph(),
              clickFun: () => {
                this.goNumCube({ ...numCodeParams });
              },
            })),
          }
        : {
            ...(await this.createGraphCard({
              numCodeParams,
            })),
          };
    },
    // 查询人像档案列表
    async queryFaceList(params) {
      try {
        const param = {
          similarity: 0.8,
          labelType: "2",
          dataType: 1,
          pageNumber: 1,
          pageSize: 10,
          ...params,
        };
        this.gptMain.setAnswerLoading({
          text: `正在调取档案`,
          loading: true,
        });
        const res = await getPersonPageList(param);
        const list = res.data.entities;
        return await this.createPeopleArchive({
          list,
          total: res.data.total,
          param,
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },
    // 查询场所档案信息
    async queryPlaceList(params) {
      try {
        const param = {
          searchKey: params.keyword,
          pageNumber: 1,
          pageSize: 10,
        };
        this.gptMain.setAnswerLoading({
          text: `正在调取档案`,
          loading: true,
        });
        const res = await queryPlaceAndKeys(param);
        const list = res.data.entities;
        return await this.createPlaceArchive({
          list,
          total: res.data.total,
          param,
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },
    async openTajectoryCard({ text, params }, hasOpen) {
      !!hasOpen && this.goTrajectory(params);
      return await this.createRefresh(
        {
          text,
          clickText: "打开轨迹",
          clickFun: () => {
            this.goTrajectory(params);
          },
        },
        { domParams: [{ params, text }] }
      );
    },
    // 档案意图分词
    async querArchivesCard(params, segmentation) {
      const picSerch = segmentation.includes("pic");
      let answerObj = null;
      // 图片查询意图
      if (picSerch) {
        this.archiveType = archivesTypeMap[segmentation[1]].archiveType;
        answerObj = await this.picturePick(this.archiveType);
        // 打开意图
        if (segmentation[0] === "open")
          setTimeout(() => {
            this.archivesDetailHandle(this.needOpenRecord);
          }, 1000);
        return answerObj;
      }
      answerObj = await this[archivesTypeMap[segmentation[1]].api](params);
      // 打开意图
      if (segmentation[0] === "open")
        setTimeout(() => {
          this.archivesDetailHandle(this.needOpenRecord);
        }, 1000);
      return answerObj;
    },
    async querTajectoryCard(params, segmentation) {
      // 查询人员关系 图片
      const picSerch = segmentation.includes("pic");
      let urlObj = null;
      if (picSerch) {
        this.archiveType = 3;
        const { algorithmType } = tajectoryTypeMap[segmentation[1]];
        urlObj = await this.picturePick(algorithmType, segmentation);
      }
      const answerObj = await this.queryTajectoryList({
        text: "已为你查询轨迹",
        data: { ...transformField(params), urlObj },
        segmentation,
        hasOpen: true,
      });
      return answerObj;
    },
    // 查询轨迹列表
    async queryTajectoryList({ text, data, segmentation, hasOpen }) {
      try {
        const type = segmentation[1]; // 轨迹类型
        const { api, searchKeyWord } = tajectoryTypeMap[type];
        const { deviceKeyWords, ...newParams } = data;
        const keyWords = searchKeyWord ? data[searchKeyWord] : "此类";
        let deviceIds = data?.deviceIds || [];
        if (deviceKeyWords) {
          const resp = await queryDeviceList({
            deviceName: deviceKeyWords,
            pageNumber: 1,
            pageSize: 5000,
            sbgnlxs: [],
            deviceId: "",
            detailAddress: "",
            orgCodes: [],
            filter: false,
          });
          deviceIds = resp.data?.entities?.map((item) => item.deviceId) || [];
        }
        const historyParams = { info: { deviceIds, ...newParams }, type };
        if (segmentation.includes("open"))
          return await this.openTajectoryCard(
            { text, params: { ...historyParams } },
            hasOpen
          );
        const apiParams = {
          pageNumber: 1,
          pageSize: 10,
          similarity: 0.8,
          dataSource: "2",
          deviceIds,
          ...newParams,
          urlObj: undefined,
          features: newParams.urlObj ? [newParams.urlObj.feature] : undefined,
        };
        this.gptMain.setAnswerLoading({
          text: `正在查询${keyWords}的轨迹`,
          loading: true,
        });
        const res = await api(apiParams);
        const list = res.data.entities;
        return await this.createTajectoryArchive({
          list,
          total: res.data.total,
          param: { ...historyParams },
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },

    // 查询车辆档案列表
    async queryVehicleList(params) {
      try {
        const param = {
          pageNumber: 1,
          pageSize: 10,
          ...params,
        };
        this.gptMain.setAnswerLoading({
          text: `正在调取${param.plateNo}的档案`,
          loading: true,
        });
        const res = await queryVehicleList(param);
        const list = res.data.entities;
        return await this.createVehicleArchive({
          list,
          total: res.data.total,
          param,
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },

    /**
     * 查询设备列表
     * @param {*} params 查询参数
     * @param {*} open 是否显示可打开按钮
     * @param {*} play 立即打开视频监控页面
     */
    async queryDeviceList(params, { hasOpen = false, play = false }) {
      try {
        const param = {
          deviceType: "1",
          pageNumber: 1,
          pageSize: 10,
          ...params,
        };
        const res = await queryDeviceInfoPageList(param);
        this.gptMain.setAnswerLoading({
          text: `正在调取${param.deviceName}的档案`,
          loading: true,
        });
        const list = res.data.entities;
        if (play) {
          this.openVideo(list);
        }
        return await this.createDeviceArchive({
          list,
          total: res.data.total,
          param,
          hasOpen,
        });
      } catch (err) {
        console.error(err, "err");
      } finally {
        this.gptMain.setAnswerLoading({
          loading: false,
        });
      }
    },

    async createNoData(props = {}) {
      const component = await this.createComponent({ name: "NoData", props });
      return {
        dom: component.$el,
      };
    },

    async createRefresh(props = {}, extentParams) {
      const component = await this.createComponent({ name: "Refresh", props });
      return {
        dom: component.$el,
        ...extentParams,
      };
    },

    async createStatistic(props, onEvent) {
      const component = await this.createComponent({
        name: "Statistic",
        props,
        onEvent,
      });
      return {
        dom: component.$el,
      };
    },

    async createTajectoryArchive({ list, total, param }) {
      const cardType = "tajectory";
      return await this.createArchive(
        { list, total, param, cardType },
        "TajectoryCard",
        {
          openPage: this.goTrajectory,
        }
      );
    },

    async createPeopleArchive({ list, total, param }) {
      const cardType = "people";
      return await this.createArchive({ list, total, param, cardType });
    },

    async createVehicleArchive({ list, total, param }) {
      const cardType = "vehicle";
      return await this.createArchive({ list, total, param, cardType });
    },

    async createPlaceArchive({ list, total, param }) {
      const cardType = "place";
      return await this.createArchive({ list, total, param, cardType });
    },

    async createDeviceArchive({ list, total, param, hasOpen }) {
      const cardType = "device";
      return await this.createArchive({
        list,
        total,
        param,
        cardType,
        hasOpen,
      });
    },

    async createArchive(
      { list, total, param, cardType, hasOpen },
      componentName,
      onEvent
    ) {
      if (list.length === 0) {
        return await this.createNoData();
      }
      const answerDom = document.createElement("div");
      const statistic = await this.createStatistic(
        {
          answer: { total, param, cardType, hasOpen },
        },
        {
          openVideo: () => {
            this.openVideo(list);
          },
          ...onEvent,
        }
      );
      answerDom.appendChild(statistic.dom);
      const div = document.createElement("div");
      list.slice(0, 3).forEach(async (row, index) => {
        row.itemType = cardType;
        if (cardType === "place") {
          row.archiveNo = row.id;
        }
        // 打开档案 默认打开第一个
        if (index === 0) {
          this.needOpenRecord = {
            type: cardType,
            archiveNo: row.archiveNo,
            plateNo: row.plateNo,
          };
        }
        const cardList = await this.createUiListCard(
          {
            type: cardType,
            data: row,
            index,
          },
          {
            archivesDetailHandle: () => {
              this.archivesDetailHandle({
                type: cardType,
                archiveNo: row.archiveNo,
                deviceId: row?.deviceId || "",
                plateNo: row.plateNo,
              });
            },
          },
          componentName
        );

        div.appendChild(cardList.dom);
      });
      answerDom.appendChild(div);
      return {
        dom: answerDom,
        domParams: [...arguments],
      };
    },

    async createUiListCard(props, onEvent = {}, name = "UiListCard") {
      const component = await this.createComponent({
        name,
        props,
        onEvent,
      });
      return {
        dom: component.$el,
      };
    },

    async createGraphCard(props, onEvent = {}) {
      const component = await this.createComponent({
        name: "Graph",
        props,
        onEvent,
      });
      return {
        dom: component.$el,
      };
    },

    async createComponent({ name, props = {}, onEvent = {} }) {
      const length = this.componentList.length;
      this.componentList.push({
        componentName: name,
        componentProps: { ...props },
        componentEvent: { ...onEvent },
        componentRef: `${name}${length}`,
      });
      await this.$nextTick();
      return this.$refs[`${name}${length}`][0];
    },
    async createLLmCard({ list, total, param }) {
      if (list.length === 0) {
        return await this.createNoData();
      }
      const answerDom = document.createElement("div");
      const div = document.createElement("div");
      const statistic = await this.createStatistic(
        {
          answer: { total, param, hasOpen: false },
        },
        {
          more: () => {
            this.goSearchPage({
              path: "/multimodal-analysis/multimodal-analysis-lib",
              query: { ...param },
            });
          },
        }
      );
      answerDom.appendChild(statistic.dom);
      list.slice(0, 3)?.forEach(async (item) => {
        const cardList = await this.createComponent({
          name: "LLMCard",
          props: {
            data: { ...item },
            style: { marginBottom: "10px" },
          },
          onEvent: {
            getDetailInfo: () => {
              this.multiDataList = [{ ...item }];
              this.isShowMutilAnalysis = true;
              this.$nextTick(() => {
                this.$refs.detailMutiAnalysisRef.showList(0);
              });
            },
          },
        });
        div.appendChild(cardList.$el);
      });
      answerDom.appendChild(div);
      return {
        dom: answerDom,
      };
    },
    archivesDetailHandle({ type, archiveNo, deviceId, plateNo }) {
      // 清空上一次打开的档案信息
      this.needOpenRecord = {};
      if (type === "people") {
        const { href } = this.$router.resolve({
          name: "people-archive",
          query: {
            archiveNo: archiveNo,
            source: "people",
            initialArchiveNo: archiveNo,
          },
        });
        window.open(href, "_blank");
      }
      if (type === "device") {
        const { href } = this.$router.resolve({
          name: "device-archive",
          query: { archiveNo: archiveNo || deviceId },
        });
        window.open(href, "_blank");
      }
      if (type === "vehicle") {
        const { href } = this.$router.resolve({
          name: "vehicle-archive",
          query: {
            archiveNo: JSON.stringify(archiveNo),
            plateNo: JSON.stringify(plateNo),
            source: "car",
          },
        });
        window.open(href, "_blank");
      }
      if (type === "place") {
        const { href } = this.$router.resolve({
          name: "place-dashboard",
          query: { archiveNo, source: "place" },
        });
        window.open(href, "_blank");
      }
    },
    beforeClose() {
      this.showVideo = false;
      if (this.$refs.exscreenRef) {
        this.$refs.exscreenRef.stopInspect();
      }
    },
    // 打开监控
    openVideo(list) {
      this.showVideo = true;
      this.$refs.exscreenRef.init(list);
    },

    /**
     * 获取图片识别结果
     * @param algorithmType  1 人像 2 车辆 3 人体
     */
    async picturePick(algorithmType, type) {
      // let algorithmType = this.archiveType === 4 ? 2 : 1;
      const fileData = new FormData();
      const file = this.gptMain.getLastImageFile();
      this.tempUrl = window.URL.createObjectURL(file.raw);
      fileData.append("file", file.raw);
      fileData.append("algorithmType", algorithmType);
      const res = await picturePick(fileData);
      if (res.data.length === 0) {
        this.$Message.error("没有识别出目标，请选择其它图片");
        return { text: "没有识别出目标，请选择其它图片" };
      }
      if (res.data.length === 1) {
        return await this.getMinImgUrl(res.data[0], type);
      }
      return await this.imageDataHandle(res.data);
    },

    // 获取
    async getMinImgUrl(urlObj, type) {
      this.imageRecognitionVisible = false;
      // 以图查人的档案
      if (this.archiveType === 1) {
        const answerObj = await this.queryFaceList({
          ...this.answerParams,
          features: [urlObj.feature],
        });
        this.imageDataHandleResolve({ ...answerObj });
        return {
          ...answerObj,
        };
      }
      // 以图查车的档案
      if (this.archiveType === 2) {
        const answerObj = await this.queryVehicleList({
          ...this.answerParams,
          features: [urlObj.feature],
        });
        this.imageDataHandleResolve({ ...answerObj });
        return {
          ...answerObj,
        };
      }
      // 查询人员轨迹
      if (this.archiveType === 3) {
        this.imageDataHandleResolve({ ...urlObj });
        return urlObj;
        // this.goTrajectory({ type: 1, info: urlObj });
        // this.imageDataHandleResolve({
        //   text: "已为查询轨迹",
        //   data: {
        //     ...this.answerParams,
        //     feature: urlObj.feature,
        //     imageUrl: urlObj.imageUrl,
        //   },
        //   ...(await this.createRefresh({
        //     text: "已为查询轨迹",
        //     clickText: "打开轨迹",
        //     clickFun: () => {
        //       this.goTrajectory({
        //         type: 1,
        //         info: urlObj,
        //       });
        //     },
        //   })),
        // });
        // return {
        //   text: "已为查询轨迹",
        //   data: {
        //     ...this.answerParams,
        //     feature: urlObj.feature,
        //     imageUrl: urlObj.imageUrl,
        //   },
        //   ...(await this.createRefresh({
        //     text: "已为查询轨迹",
        //     clickText: "打开轨迹",
        //     clickFun: () => {
        //       this.goTrajectory({
        //         type: 1,
        //         info: urlObj,
        //       });
        //     },
        //   })),
        // };
      }
      // 查询车辆轨迹
      // if (this.archiveType === 4) {
      //   this.goTrajectory({ type: 2, info: urlObj });
      //   this.imageDataHandleResolve({
      //     text: "已为查询轨迹",
      //     data: {
      //       ...this.answerParams,
      //       feature: urlObj.feature,
      //       imageUrl: urlObj.imageUrl,
      //     },
      //     ...(await this.createRefresh({
      //       text: "已为查询轨迹",
      //       clickText: "打开轨迹",
      //       clickFun: () => {
      //         this.goTrajectory({
      //           type: 2,
      //           info: urlObj,
      //         });
      //       },
      //     })),
      //   });
      //   return {
      //     text: "已为查询轨迹",
      //     data: {
      //       ...this.answerParams,
      //       feature: urlObj.feature,
      //       imageUrl: urlObj.imageUrl,
      //     },
      //     ...(await this.createRefresh({
      //       text: "已为查询轨迹",
      //       clickText: "打开轨迹",
      //       clickFun: () => {
      //         this.goTrajectory({
      //           type: 2,
      //           info: urlObj,
      //         });
      //       },
      //     })),
      //   };
      // }
      // 查询人员关系
      if (this.archiveType === 5) {
        return {
          ...(await this.numCodePersonRelationIdCard(
            type,
            "已为查询关系",
            {
              feature: urlObj.feature,
              imageUrl: urlObj.imageUrl,
            },
            true
          )),
        };
        // this.goNumCube({ type: 3, info: urlObj });
        // this.imageDataHandleResolve({
        //   text: "已为查询关系",
        //   data: {
        //     ...this.answerParams,
        //     feature: urlObj.feature,
        //     imageUrl: urlObj.imageUrl,
        //   },
        //   ...(await this.createRefresh({
        //     text: "已为查询关系",
        //     clickText: "打开关系",
        //     clickFun: () => {
        //       this.goNumCube({ type: 3, info: urlObj });
        //     },
        //   })),
        // });
        // return {
        //   text: "已为查询关系",
        //   data: {
        //     ...this.answerParams,
        //     feature: urlObj.feature,
        //     imageUrl: urlObj.imageUrl,
        //   },
        //   ...(await this.createRefresh({
        //     text: "已为查询关系",
        //     clickText: "打开关系",
        //     clickFun: () => {
        //       this.goNumCube({ type: 3, info: urlObj });
        //     },
        //   })),
        // };
      }
    },
    async numCodePersonRelationIdCard(type, text, params, isPic) {
      const data = {
        ...this.answerParams,
        ...params,
      };
      const refreshDom = await this.addHistoryFilterRelation(
        {
          text,
          type,
          data,
        },
        true
      );
      isPic &&
        this.imageDataHandleResolve({
          text,
          data,
          ...refreshDom,
        });
      return {
        text,
        data,
        ...refreshDom,
      };
    },
    /**
     *
     * @param name 路由名称
     * @param path 路由地址
     * @param query 传递参数
     */
    async goSearchPage(name, path, query) {
      let param = (param = {
        ...(name && { name }),
        ...(path && { path }),
      });
      await this.$router.push({
        ...param,
        query: {
          from: "gpt",
          ...query,
        },
      });
    },
    async goTrajectory(params) {
      await this.$router.push({
        name: `map-default-page`,
        query: {
          from: "gpt",
        },
      });
      this.$nextTick(() => {
        this.setTrajectoryParams({ ...params });
      });
    },
    // 跳转数智立方 做实体关系查询
    async goNumCube(params) {
      await this.$router.push({
        name: `number-cube-info`,
        query: {
          from: "gpt",
        },
      });
      this.$nextTick(() => {
        this.setNumCodeParams({ ...params });
      });
    },
    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      return new Promise((resolve, reject) => {
        this.imageRecognitionVisible = true;
        const arr = [];
        list.forEach((item) => {
          const obj = {
            x: item.left,
            y: item.top,
            width: item.right - item.left,
            height: item.bottom - item.top,
            feature: item.feature,
          };

          arr.push(obj);
        });
        this.dataCopper = arr;
        this.imageDataHandleResolve = resolve;
      });
    },
  },
  watch: {},
  components: {
    NoData,
    UiListCard,
    Statistic,
    Exscreen,
    ImageRecognition,
    Refresh,
    Graph,
    TajectoryCard,
    DetailMutiAnalysis,
    LLMCard,
  },
};
</script>
<style scoped lang="less">
.video-modal {
  /deep/ .ivu-modal-wrap {
    z-index: 99999 !important;
  }

  /deep/ .ivu-modal {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    right: 0 !important;
    position: relative !important;
  }

  /deep/ .ivu-modal-content {
    position: absolute;
    width: 620px;
    top: 30px;
    right: 0;
  }

  /deep/ .ivu-modal-content {
    left: 10px;
    overflow: hidden;
  }

  /deep/ .ivu-modal-header {
    background: linear-gradient(214deg, #2ee0fc 0%, #2c48ff 100%);

    .ivu-modal-header-inner {
      color: #fff;
    }
  }
}

.image-recognition {
  /deep/ .ivu-modal-wrap {
    z-index: 99999 !important;
  }

  /deep/ .ivu-modal {
    width: 100% !important;
    height: 100% !important;
    top: 0 !important;
    right: 0 !important;
    position: relative !important;
  }

  /deep/ .ivu-modal-content {
    position: absolute;
    right: 20px;
    width: 620px;
    top: 30px;
  }
}

.multi-analysis-card-detail.dom-wrapper {
  z-index: 10000;
}
</style>
