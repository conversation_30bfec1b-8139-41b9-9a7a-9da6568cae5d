const tooltip = {
  show: 'true',
  trigger: 'axis', //触发类型
  axisPointer: {
    type: 'none', //去掉移动的指示线
  },
  confine: true,
  padding: [8, 10], //内边距
  extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
  formatter: function (params) {
    let result = `<div>${params[0].name}</div>`;
    params.forEach(function (item) {
      // 去除特殊样式series
      if (item.componentSubType !== 'pictorialBar' && item.seriesName !== '背景') {
        const dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
        const seriesName = `<span style="display:inline-block; margin-left:10px;">${item.seriesName}</span>`;
        const number = `<span style="display:inline-block;float: right;margin-left:10px;">${Math.abs(
          item.value,
        )}%</span>`;
        result += dotHtml + seriesName + number + '</br>';
      }
    });
    return result;
  },
};
const fontSize = (res) => {
  const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
  if (!clientWidth) return;
  const fontSize = clientWidth / 1920;
  return res * fontSize;
};
export const options = (chartData) => {
  const legendName = chartData.dataobj.map((item) => item.name);
  const seriesData = chartData.dataobj.map((xItem) => {
    const obj = {
      name: xItem.name,
      type: 'bar',
      stack: '数量',
      barWidth: fontSize(14),
      itemStyle: {
        color: xItem.color,
        shadowBlur: [0, 0, 0, 10],
        shadowColor: '#ebe806',
        borderRadius: [20, 20, 20, 20],
      },
      z: 5,
      data: xItem.list,
    };
    return obj;
  });
  const option = {
    tooltip: tooltip,
    legend: {
      data: legendName,
      itemWidth: 10,
      itemHeight: 10,
      top: 10,
      textStyle: {
        color: '#fff',
      },
      itemStyle: {},
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '12%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      axisLabel: {
        margin: 10,
        color: '#fff',
        fontSize: fontSize(12),
        formatter: (value) => value,
      },
    },
    yAxis: {
      type: 'category',
      data: chartData.dataY,
      axisLine: { show: false },
      axisTick: { show: false },
      splitLine: { show: false },
      axisLabel: {
        margin: 10,
        color: '#e2e9ff',
        fontSize: fontSize(12),
      },
    },
    series: seriesData,
  };
  return option;
};
