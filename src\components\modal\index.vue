<template>
  <div class="ui-modal">
    <!-- :styles="stylesObj" -->
    <Modal
      ref="modal"
      v-model="modalShow"
      :loading="loading"
      :width="width"
      :ok-text="okText"
      :styles="stylesObj"
      :mask-closable="maskClosable"
      :footer-hide="footerHide"
      :transfer="false"
      v-bind="$attrs"
      :class="{'footerModel': footer, 'list-content': listContent}"
      class="modal"
      class-name="vertical-center-modal"
      @on-cancel="onCancel"
      @on-ok="onOk"
      :before-close="handleClose"
      @upStepHandle="upStepHandle"
    >
      <p slot="header" class="header">
        <span>{{ title }}</span>
        <slot name="header"></slot>
      </p>
      <slot name="input"></slot>
      <slot></slot>
      <div slot="footer">
           <slot name="footer"></slot>
      </div>
    </Modal>
  </div>
</template>
<script>
import { Modal } from 'view-design'

export default {
  props: {
    ...Modal.props,
    stepCurrent: {
      default: 0
    },
    stepList: {
      default: () => []
    },
    // 次宽度可直接写px单位数值，最终转换为rem
    rWidth: {
      type: [String, Number],
      default: 0
    },
    // 是否为列表弹框
    listContent: {
      type: Boolean,
      default: false
    },
    maskClosable: {
      default: false
    },
    // 判断是否显示footer
    footer: {
        type: Boolean,
        default: false,
    }
  },
  data() {
    return {
      modalShow: false
    }
  },
  computed: {
    upStenBtnShow() {
      if (this.stepCurrent === 1 || this.stepCurrent === 0) {
        return false
      } else {
        return true
      }
    },
    confirmText() {
      if (this.stepCurrent !== 0) {
        if (this.stepCurrent === this.stepList.length) {
          return '确定'
        } else {
          return '下一步'
        }
      } else {
        return '确定'
      }
    },
    stylesObj() {
      let stylesObj = {}
      if (this.rWidth) {
        const rWidth = parseFloat(this.rWidth)
        if (isNaN(rWidth)) {
          stylesObj.width = '5rem'
        } else {
          stylesObj.width = parseFloat(this.rWidth) / 192 + 'rem'
        }
      } else {
        stylesObj = this.styles
      }
      return stylesObj
    }
  },
  watch: {
    modalShow(val) {
      this.$emit('input', val)
    },
    value: {
      handler(val) {
        this.modalShow = val
      },
      immediate: true
    }
  },
  methods: {
    full() {
      this.$emit('full')
    },
    onCancel() {
      this.$emit('onCancel')
    },
    onOk() {
      this.$emit('onOk')
    },
    handleClose($event) {
      this.$emit('before-close', $event)
    },
    upStepHandle() {
      this.$emit('upStepHandle')
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/i_model';
.modal{
    /deep/ .ivu-modal-footer{
        display: none;
    }
}
.footerModel{
   /deep/ .ivu-modal-footer{
        display: block;
    } 
}
</style>
