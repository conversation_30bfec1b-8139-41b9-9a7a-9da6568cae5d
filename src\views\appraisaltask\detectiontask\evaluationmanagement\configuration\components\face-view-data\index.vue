<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        case 'FACE_ACCURACY':
          return 'BasicAccuracy';
        // 人脸卡口设备图片存储时长达标 - 2011
        case 'FACE_IMAGE_STORE_PASS':
          //return 'CompleteRuleForm'
          return 'StorePassForm';
        // 人脸抓拍数据上传合规率  - 2013
        case 'FACE_DATA_COMPLY_RULE_RATE':
        case 'FACE_CAPTURE_COMPLETENESS_RATE': // 人脸抓拍数据上传完整率 - 2012
          return 'CompleteRuleForm';
        //人脸卡口设备图片地址可用率
        case 'FACE_URL_AVAILABLE':
        case 'FACE_FOCUS_URL_AVAILABLE':
          return 'FaceUrlAvailable';
        case 'FACE_SMALL_URL_AVAILABLE': // 人脸卡口设备小图地址可用率
          return 'FaceThumbnailUrlAvailable';
        case 'FACE_OFFLINE_STAT':
          return 'FaceOfflineStat';
        case 'FACE_QUALITY_PASS_RATE':
          return 'FaceCaptureQualityRate';
        // 人脸抓拍图片评分率
        case 'FACE_CAPTURE_SCORE':
          return 'FaceCaptureScore';
        default:
          return 'Original';
      }
    },
  },
  components: {
    Original: require('../face-view-data.vue').default,
    FaceUrlAvailable:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-url-available.vue')
        .default,
    FaceThumbnailUrlAvailable:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-url-thumbnail-available.vue')
        .default,
    BasicAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/basis-view-config/components/basic-accuracy.vue')
        .default,
    StorePassForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/store-pass-form.vue')
        .default,
    CompleteRuleForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/complete-rule-form.vue')
        .default,
    FaceOfflineStat:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-offline-stat.vue')
        .default,
    FaceCaptureQualityRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-capture-quality-rate.vue')
        .default,
    FaceCaptureScore:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-capture-score.vue')
        .default,
  },
};
</script>
<style lang="less" scoped></style>
