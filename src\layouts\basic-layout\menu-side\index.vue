<template>
  <div>
    <div
      :class="{ 'i-layout-sider-logo-dark': siderTheme === 'dark' }"
      class="i-layout-sider-logo"
    >
      <transition name="fade-quick">
        <i-link v-show="!hideLogo" to="/">
          <img src="@/assets/img/logo.png" alt />
        </i-link>
      </transition>
    </div>
    <Menu
      ref="menu"
      :accordion="menuAccordion"
      :theme="siderTheme"
      :active-name="openNames[0]"
      :open-names="openNames"
      :class="{ 'i-layout-menu-side-collapse': menuCollapse }"
      class="i-layout-menu-side i-scrollbar-hide"
      width="auto"
    >
      <template v-for="(item, index) in filterSider">
        <i-menu-side-item
          v-if="item.children === undefined || !item.children.length"
          :key="index"
          :menu="item"
        />
        <i-menu-side-collapse
          v-else
          :menu="item"
          :key="index"
          :top-level="false"
        />
      </template>
      <!-- <div class="hrefTo" v-if="hrefTo" @click="openWindow('10000005')">
        <i class="iconfont icon-biaoqian"></i>
        <div>标签中心</div>
      </div>
      <div class="hrefTo" v-if="hrefTo" @click="openWindow('00000008')">
        <i class="iconfont icon-a-dashujushujujiegouguanxiguanxifenxigongtonglianxirenzuzhi"></i>
        <div>关系中心</div>
      </div>
      <div class="hrefTo" v-if="hrefTo" @click="openWindow('00000003')">
        <i class="iconfont icon-wangguan1"></i>
        <div>数据网关</div>
      </div>
      <div class="hrefTo" v-if="hrefTo" @click="openWindow('00000002')">
        <i class="iconfont icon-iVDG"></i>
        <div>治理中心</div>
      </div> -->
    </Menu>
  </div>
</template>
<script>
import iMenuSideItem from "./menu-item";
import iMenuSideSubmenu from "./submenu";
import iMenuSideCollapse from "./menu-collapse";
import tTitle from "../mixins/translate-title";
import { getToken } from "@/libs/configuration/util.common";

import { mapState, mapGetters } from "vuex";

// 元素是否在可视区域
function isElementInViewport(el) {
  const rect = el.getBoundingClientRect();
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
    rect.right <= (window.innerWidth || document.documentElement.clientWidth)
  );
}

export default {
  name: `iMenuSide`,
  components: { iMenuSideItem, iMenuSideSubmenu, iMenuSideCollapse },
  mixins: [tTitle],
  props: {
    hideLogo: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      hrefTo: false,
    };
  },
  computed: {
    ...mapState("admin/layout", [
      "siderTheme",
      "menuAccordion",
      "menuCollapse",
    ]),
    ...mapState("admin/menu", ["activePath", "openNames"]),
    ...mapGetters("admin/menu", ["filterSider"]),
    ...mapGetters({ userInfo: "userInfo" }),
  },
  watch: {
    $route: {
      handler(val) {
        if (val.name == "resource-manage" || val.name == "directory-manage") {
          this.hrefTo = true;
        } else {
          this.hrefTo = false;
        }
        this.handleUpdateMenuState();
      },
      immediate: true,
    },
    // 在展开/收起侧边菜单栏时，更新一次 menu 的状态
    menuCollapse() {
      this.handleUpdateMenuState();
    },
  },
  mounted() {
    setTimeout(() => {
      // console.log(this.filterSider, 'filterSider');
    }, 3000);
  },
  methods: {
    handleUpdateMenuState() {
      this.$nextTick(() => {
        if (this.$refs.menu) {
          this.$refs.menu.updateActiveName();
          if (this.menuAccordion) this.$refs.menu.updateOpened();
          // 聚焦当前项
          this.$nextTick(() => {
            const $activeMenu = document.getElementsByClassName(
              "ivu-menu-item ivu-menu-item-active ivu-menu-item-selected"
            );
            if (
              $activeMenu &&
              $activeMenu.length &&
              !isElementInViewport($activeMenu[0])
            ) {
              const activeMenuTop = $activeMenu[0].getBoundingClientRect().top;
              const $menu = this.$refs.menu.$el;
              setTimeout(() => {
                if (this.$ScrollTop) {
                  this.$ScrollTop($menu, {
                    to: activeMenuTop,
                    time: 0,
                  });
                }
              }, 300);
            }
          });
        }
      });
    },
    openWindow(val) {
      var list = this.userInfo.sysApplicationVoList;
      var row = list.find((item) => {
        return item.applicationCode == val;
      });
      if (row) {
        window.open(row.address + "?refresh_token=" + getToken());
      }

      if (val == "00000002") {
        window.open("http://*************/home?refresh_token=" + getToken());
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hrefTo {
  height: 70px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
  padding-top: 10px;
  cursor: pointer;
}
.hrefTo:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.1);
}
</style>
