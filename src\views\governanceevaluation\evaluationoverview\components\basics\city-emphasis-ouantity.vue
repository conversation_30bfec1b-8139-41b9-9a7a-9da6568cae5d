<template>
  <!-- 重点位置类型视频图像设备数量达标率 （市） -->
  <div class="city-emphasis-ouantity auto-fill">
    <div class="content">
      <div class="container">
        <div class="abnormal-title">
          <div class="fl">
            <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
            <span class="f-16 color-filter ml-sm">检测结果统计</span>
          </div>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs f-14">导出</span>
            </Button>
          </div>
        </div>
        <div class="list">
          <ui-table :tableColumns="tableColumns" :table-data="tableData" :loading="loading" row-key="id">
            <template #index="{ row, index }">
              <span :class="row.rowClass">{{ index + 1 }}</span>
            </template>
            <template #civilName="{ row }">
              <span class="civil" :title="row.civilName" :class="row.rowClass">{{
                row.civilName === null ? '--' : row.civilName
              }}</span>
            </template>
            <template #civilTypeName="{ row }">
              <span :class="row.rowClass">{{ row.civilTypeName === null ? '--' : row.civilTypeName }}</span>
            </template>
            <template #sxjCount="{ row }">
              <span :class="row.rowClass">{{ row.sxjCount }}</span>
            </template>
            <template #sxjReportCount="{ row }">
              <span :class="row.rowClass">{{ row.sxjReportCount === null ? '--' : row.sxjReportCount }}</span>
            </template>

            <template #sxjStandardsValue="{ row }">
              <span :class="row.rowClass"
                >{{
                  row.sxjStandardsValue === null ? '--' : $util.common.financial(row.sxjStandardsValue * 100)
                }}%</span
              >
            </template>

            <template #rlkkCount="{ row }">
              <span :class="row.rowClass">{{ row.rlkkCount === null ? '--' : row.rlkkCount }}</span>
            </template>
            <template #rlkkReportCount="{ row }">
              <span :class="row.rowClass">{{ row.rlkkReportCount === null ? '--' : row.rlkkReportCount }}</span>
            </template>

            <template #rlkkStandardsValue="{ row }">
              <span :class="row.rowClass"
                >{{
                  row.rlkkStandardsValue === null ? '--' : $util.common.financial(row.rlkkStandardsValue * 100)
                }}%</span
              >
            </template>
            <template #clkkCount="{ row }">
              <span :class="row.rowClass">{{ row.clkkCount === null ? '--' : row.clkkCount }}</span>
            </template>

            <template #clkkReportCount="{ row }">
              <span :class="row.rowClass">{{ row.clkkReportCount === null ? '--' : row.clkkReportCount }}</span>
            </template>

            <template #clkkStandardsValue="{ row }">
              <span :class="row.rowClass"
                >{{
                  row.clkkStandardsValue === null ? '--' : $util.common.financial(row.clkkStandardsValue * 100)
                }}%</span
              >
            </template>
          </ui-table>
        </div>
        <table class="table_content">
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>达标区县数量</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantitySxjReportCount === null ? '--' : indexList.emphasisQuantitySxjReportCount
                }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantityRlkkReportCount === null ? '--' : indexList.emphasisQuantityRlkkReportCount
                }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantityClkkReportCount === null ? '--' : indexList.emphasisQuantityClkkReportCount
                }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>不达标区县数量</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{ sxjCount }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{ rlkkCount }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{ clkkCount }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>分项达标率</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantitySxjFormula === null ? '--' : indexList.emphasisQuantitySxjFormula
                }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantityRlkkFormula === null ? '--' : indexList.emphasisQuantityRlkkFormula
                }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantityClkkFormula === null ? '--' : indexList.emphasisQuantityClkkFormula
                }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>数量达标率</span>
              </div>
            </td>
            <td class="td_text">
              <div>
                <span class="font-blue">{{
                  indexList.emphasisQuantityFormula === null ? '--' : indexList.emphasisQuantityFormula
                }}</span>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.city-emphasis-ouantity {
  .content {
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
    position: relative;
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    @{_deep}.ivu-table-cell-tree {
      display: inline-block;
    }
    @{_deep}.ivu-table-cell-slot {
      display: inline-block;
    }
    @{_deep}.ivu-table-cell-tree {
      border: 0.005208rem solid #2b84e2;
      background-color: #062042;
      color: var(--color-primary);
    }
    @{_deep}.ivu-table-cell-tree-empty {
      cursor: default !important;
      color: transparent !important;
      background-color: transparent !important;
      border-color: transparent !important;
      display: none !important;
    }
    .list {
      height: 425px !important; /*no*/
      border: solid 1px #085aa7 !important;
      @{_deep}.ivu-table-wrapper {
        height: 420px !important; /*no*/
        width: 100% !important;
      }

      @{_deep}.ivu-table-body {
        max-height: 421px !important;
        overflow-y: auto;
      }
      @{_deep}.ivu-table-fixed-header {
        th {
          background: #0d477d !important;
          font-size: 13px;
          font-family: MicrosoftYaHei;
          line-height: 19px;
          color: #a9bed9 !important;
          // opacity: 0.9;
        }
      }
      @{_deep} .ivu-table .even1 {
        // background-color: #062042 !important;
        padding: 0 10px;
        // height: 48px;
        display: flex;
        align-items: center;
        color: #19d5f6;
      }
      @{_deep} .ivu-table .even2 {
        // background-color: #062042 !important;
        padding: 0 10px;
        // height: 48px;
        display: flex;
        align-items: center;
        color: #2470c2;
      }
      @{_deep} .ivu-table .odd1 {
        // background-color: #041939 !important;
        padding: 0 10px;
        // height: 48px;
        display: flex;
        align-items: center;
        color: #19d5f6;
      }
      @{_deep} .ivu-table .odd2 {
        // background-color: #041939 !important;
        padding: 0 10px;
        // height: 48px;
        display: flex;
        align-items: center;
        color: #2470c2;
      }
      @{_deep}.ivu-table table {
        width: 100% !important;
        border-collapse: unset !important;
      }
      @{_deep}.ivu-table-header {
        &:first-child {
          tr {
            border-top: solid 1px #085aa7 !important;
            border-left: solid 1px #085aa7 !important;
            th {
              &:last-child {
                border-bottom: 1px solid #085aa7 !important;
                border-left: none !important;
              }
            }
          }
        }
        tr {
          border-top: solid 1px #085aa7 !important;
          border-left: solid 1px #085aa7 !important;

          th {
            border-bottom: 1px solid #085aa7 !important;
            border-left: solid 1px #085aa7 !important;

            span {
              color: #a9bed9 !important;
            }
          }
          &:nth-child(1) {
            th {
              &:nth-child(1) {
                border-left: none !important;
              }
            }
          }
        }
      }

      @{_deep}.ivu-table-tbody {
        tr {
          border-top: 1px solid var(--border-color) !important;
          border-right: 1px solid var(--border-color) !important;
          td {
            border-left: 1px solid var(--border-color) !important;
            border-bottom: 1px solid var(--border-color) !important;
            &:nth-child(1) {
              border-left: none !important;
            }
          }
        }
      }
      .ui-table {
        position: relative;
        width: 100%;
      }
    }
    .table_content {
      height: 200px;
      width: 100%;
      border-bottom: 1px solid var(--border-color);

      .con_one {
        height: 50px;
        width: 100%;
        display: flex;
        border-top: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);

        td {
          text-align: center;
          line-height: 50px;
          border-left: 1px solid var(--border-color);
          div {
            height: 100%;
            display: inline-block;
            width: 100%;
          }
        }

        .con_color {
          color: #0d477d;
        }
        .quantity_color {
          color: #bc3c19;
        }

        .td_one {
          width: 260px; /*no*/
          background-color: #092955;
          span {
            color: #a9bed9 !important;
            font-size: 14px;
          }
          // flex: 1;
        }
        .td_text {
          flex: 1;
          span {
            color: #19c176 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }

        .td_two {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
        .td_three {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
        .td_four {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
      }
    }
    @media screen and (max-width: 1366px) {
      .list {
        height: 350px !important; /*no*/
        @{_deep}.ivu-table-wrapper {
          height: 345px !important; /*no*/
        }
      }
      .table_content {
        height: 200px;
        width: 100%;
        border-bottom: 1px solid var(--border-color);

        .con_one {
          height: 50px;
          width: 100%;
          display: flex;
          border-top: 1px solid var(--border-color);
          border-right: 1px solid var(--border-color);

          td {
            text-align: center;
            line-height: 50px;
            border-left: 1px solid var(--border-color);
            div {
              height: 100%;
              display: inline-block;
              width: 100%;
            }
          }

          .con_color {
            color: #0d477d;
          }
          .quantity_color {
            color: #bc3c19;
          }

          .td_one {
            width: 260px !important; /*no*/
            background-color: #092955;
            span {
              color: #a9bed9 !important;
              font-size: 14px;
            }
            // flex: 1;
          }
          .td_text {
            flex: 1;
            span {
              color: #19c176 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }

          .td_two {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
          .td_three {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
          .td_four {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
        }
      }
    }
  }
}
.civil {
  width: 60px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'city-emphasis-ouantity',
  data() {
    return {
      loading: false,
      tableColumns: [
        {
          title: '序号',
          width: 60,
          slot: 'index',
          align: 'center',
        },

        {
          title: '行政区域',
          slot: 'civilName',
          align: 'center',
          width: 100,
          tree: true,
        },
        {
          title: '区域类型',
          key: 'civilTypeName',
          slot: 'civilTypeName',
          align: 'center',
          width: 100,
        },
        {
          title: '视频监控',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'sxjCount',
              slot: 'sxjCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'sxjReportCount',
              slot: 'sxjReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'sxjStandardsValue',
              slot: 'sxjStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
        {
          title: '人脸卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'rlkkCount',
              slot: 'rlkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'rlkkReportCount',
              slot: 'rlkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'rlkkStandardsValue',
              slot: 'rlkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },

        {
          title: '车辆卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'clkkCount',
              slot: 'clkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'clkkReportCount',
              slot: 'clkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'clkkStandardsValue',
              slot: 'clkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '7.7rem',
      },
      visible: true,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
      sxjCount: 0,
      rlkkCount: 0,
      clkkCount: 0,
      exportLoading: false,
      paramsList: {},
    };
  },
  mounted() {},

  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },

    async getEvaluationRecord() {
      try {
        this.loading = true;
        this.tableData = [];
        let data = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        this.indexList = res.data.data;
        this.tableData = this.$util.common.arrayToJson(
          this.indexList.emphasisQuantityRecordDetailVos,
          'selfCode',
          'parentCode',
        );
        this.tableData.forEach((item, index) => {
          this.$set(item, '_showChildren', true);

          if (index % 2 === 0) {
            this.$set(item, 'rowClass', 'even2');
          } else {
            this.$set(item, 'rowClass', 'odd2');
          }
        });

        this.sxjCount = this.indexList.emphasisQuantitySxjCount - this.indexList.emphasisQuantitySxjReportCount || '--';
        this.rlkkCount =
          this.indexList.emphasisQuantityRlkkCount - this.indexList.emphasisQuantityRlkkReportCount || '--';
        this.clkkCount =
          this.indexList.emphasisQuantityClkkCount - this.indexList.emphasisQuantityClkkReportCount || '--';
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getEvaluationRecord();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
