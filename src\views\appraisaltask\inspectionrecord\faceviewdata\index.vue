<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 模式切换 ------------------------------------------------------------------------------------------------------>
    <tagView class="tagView" ref="tagView" :list="['图片模式', '设备模式']" @tagChange="tagChange1" />
    <!-- 图片模式 ------------------------------------------------------------------------------------------------------>
    <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag == 0">
      <div slot="search" class="hearder-title">
        <SearchCard
          :cardSearchList="cardSearchList"
          :taskObj="taskObj"
          :treeData="treeData"
          @startSearch="startSearch"
          @params-change="paramsChange"
        />
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <InfoCard class="card1" :list="row" :cardInfo="cardInfo" @bigImageUrl="bigImageUrl"> </InfoCard>
      </template>
    </TableCard>
    <!-- 设备模式 ------------------------------------------------------------------------------------------------------>
    <TableList
      v-if="modelTag == 1 && columns.length > 0"
      ref="infoList"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <searchList
          :currentTree="currentTree"
          :lable="lable"
          :treeData="treeData"
          :taskObj="taskObj"
          :width="width"
          :searchList="searchList"
          @startSearch="startSearch"
          @params-change="paramsChange"
        />
      </div>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <!-- <span class="font-table-action pointer" @click="bigImageUrl()">查看不合格图片</span> -->
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          content="查看不合格图片"
          @click.native="getBigImageUrl(row)"
        ></ui-btn-tip>
      </template>
    </TableList>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import { icons } from '../components/common';
import inspectionrecord from '@/config/api/inspectionrecord';

export default {
  components: {
    TableList: require('../components/tableList.vue').default,
    TableCard: require('../components/tableCard.vue').default,
    tagView: require('../components/tags').default,
    SearchCard: require('./component/searchCard.vue').default,
    searchList: require('./component/searchList.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    InfoCard: require('./component/infoCard.vue').default,
    LookScene: require('@/components/look-scene').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      lable: '', // 设备模式数量检索展示
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
      searchList: [], // 设备模式检索下拉框
      abnormalCount: [], // 统计展示
      modelTag: 0, // 聚档模式,图像模式
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      minusHeight: 488, // 表格
      columns: [], // 表头
      // 卡片展示参数，
      cardInfo: [],
      // 卡片下拉框参数
      cardSearchList: [],
      // 卡片接口
      loadDataCard: (parameter) => {
        const params = {
          evaluationRecordTypeId: this.currentTree.id,
          taskSchemeId: this.taskObj.taskSchemeId,
          batchId: this.indexResult().batchId,
          ...this.searchData,
        };
        return this.$http
          .post(inspectionrecord.getEvaluationFaceByPictureModel, Object.assign(parameter, params))
          .then((res) => {
            return res.data;
          });
      },
      // 设备模式接口
      loadDataList: (parameter) => {
        const params = {
          evaluationRecordTypeId: this.currentTree.id,
          taskSchemeId: this.taskObj.taskSchemeId,
          batchId: this.indexResult().batchId,
          ...this.searchData,
        };
        return this.$http
          .post(inspectionrecord.getEvaluationFaceByDeviceModel, Object.assign(parameter, params))
          .then((res) => {
            return res.data;
          });
      },
      currentIcon: 'icon-exceptionlibrary',
    };
  },
  computed: {},
  watch: {
    currentTree: {
      deep: true,
      immediate: true,
      handler: function () {
        this.getIcon();
        this.infoData();
      },
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    indexResult() {
      if (this.taskObj.indexResults && this.taskObj.indexResults.length) {
        return this.taskObj.indexResults[0];
      }
      return {};
    },
    paramsChange(val) {
      this.searchData = val;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    async statisticsCount() {
      // 统计接口
      let countParams = {
        batchId: this.indexResult().batchId, // 批次号
        taskSchemeId: this.taskObj.taskSchemeId,
        evaluationRecordTypeId: this.currentTree.id,
        ...this.searchData,
      };
      await this.$http.post(inspectionrecord.getEvaluationFaceStatisticsCount, countParams).then((res) => {
        this.infoObj = res.data.data;
      });
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async getBigImageUrl(item) {
      try {
        let params = {
          faceDeviceDetailId: item.faceDeviceDetailId,
        };
        let {
          data: { data },
        } = await this.$http.get(inspectionrecord.getEvaluationUnqualified, { params });
        let list = data.map((item) => item.scenePath);
        this.imgList = list;
        this.bigPictureShow = true;
      } catch (e) {
        console.log(e);
      }
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
    },
    async init() {
      try {
        await this.statisticsCount();
        await this.abnormalCountMap();
        await this.infoData();
        if (this.modelTag === 1) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 检索
    startSearch(searchData) {
      this.$nextTick(() => {
        this.searchData = searchData;
        this.init();
      });
    },
    getIcon() {
      this.currentIcon = icons[this.currentTree.id] || 'icon-exceptionlibrary';
      this.abnormalCount = [{ title: '采集设备总数', icon: this.currentIcon }];
    },
    // 统计参数填充
    abnormalCountMap() {
      let list = [];
      this.infoObj.map((item) => {
        list.push({
          title: item.dataDesc,
          count: item.dataValue,
          icon: this.currentIcon,
        });
      });
      this.abnormalCount = list;
      return list;
    },
    // searchList 人脸表格搜索条件  columns 表头  cardSearchList 卡片检索条件  cardInfo 卡片展示 abnormalCount 统计 车辆除检测概况，其余页面一样，通过动态数据来区分展示
    infoData() {
      switch (this.currentTree.id) {
        case 302:
          this.width = 165;
          this.lable = '小图非唯一人脸图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: '小图唯一人脸' },
            { dataKey: 2, dataValue: '小图非唯一人脸' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { value: 'shotTime', icon: 'icon-shijian' },
            { value: 'address', icon: 'icon-dizhi' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: '小图唯一人脸图片数量', key: 'qualifiedNum' },
            { title: '小图非唯一人脸图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 303:
          this.width = 165;
          this.lable = '时钟错误图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: '时钟准确' },
            { dataKey: 2, dataValue: '时钟错误' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍：', value: 'shotTime' },
            { name: '接收：', value: 'firstIntoViewTime' },
            { name: '', value: 'tip' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: '时钟错误图片数量', key: 'qualifiedNum' },
            { title: '时钟正确图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 304:
          this.width = 165;
          this.lable = '上传超时人脸图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: '上传及时' },
            { dataKey: 2, dataValue: '上传超时' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍：', value: 'shotTime' },
            { name: '接收：', value: 'receiveTime' },
            { type: 'lazy', name: '延时：', value: 'delay' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: '上传超时图片数量', key: 'qualifiedNum' },
            { title: '上传及时图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 305:
          this.width = 165;
          this.lable = '上传超时人脸图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: '上传及时' },
            { dataKey: 2, dataValue: '上传超时' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { name: '抓拍：', value: 'shotTime' },
            { name: '接收：', value: 'receiveTime' },
            { name: '延时：', value: 'delay', type: 'lazy' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: '上传超时图片数量', key: 'qualifiedNum' },
            { title: '上传及时图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 306:
          this.width = 165;
          this.lable = 'URL不可用人脸图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: 'URL可用' },
            { dataKey: 2, dataValue: 'URL不可用' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { value: 'shotTime', icon: 'icon-shijian' },
            { value: 'address', icon: 'icon-dizhi' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: 'URL不可用图片数量', key: 'qualifiedNum' },
            { title: 'URL可用图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
        case 307:
          this.width = 165;
          this.lable = 'URL不可用人脸图片数量';
          this.cardSearchList = [
            { dataKey: 1, dataValue: 'URL可用' },
            { dataKey: 2, dataValue: 'URL不可用' },
            { dataKey: 3, dataValue: '无法检测' },
          ];
          this.cardInfo = [
            { value: 'shotTime', icon: 'icon-shijian' },
            { value: 'address', icon: 'icon-dizhi' },
          ];
          this.columns = [
            { type: 'index', width: 70, title: '序号', fixed: 'left', align: 'center' },
            {
              title: `${this.global.filedEnum.deviceId}`,
              key: 'deviceId',
              slot: 'deviceId',
              width: 200,
              fixed: 'left',
            },
            {
              title: `${this.global.filedEnum.deviceName}`,
              key: 'deviceName',
              ellipsis: true,
              tooltip: true,
            },
            { title: '组织机构', key: 'orgCodeName', ellipsis: true, tooltip: true },
            { title: this.global.filedEnum.sbdwlx, key: 'sbdwlxText' },
            { title: '检测图片数量', key: 'total', width: 120 },
            { title: 'URL不可用图片数量', key: 'qualifiedNum' },
            { title: 'URL可用图片数量', key: 'unqualifiedNum' },
            {
              title: '操作',
              slot: 'action',
              align: 'center',
              width: 60,
              className: 'table-action-padding',
              fixed: 'right',
            },
          ];
          break;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    // position: absolute;
    // right: 20px;
    // top: 134px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
  .card1 {
    width: calc(calc(100% - 81px) / 8);
    margin: 0 10px 10px 0;
  }
}
</style>
