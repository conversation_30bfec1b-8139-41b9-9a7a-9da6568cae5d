<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import equipmentassets from '@/config/api/equipmentassets';
const props = {
  isEdit: {
    type: Boolean,
    default: false,
  },
  bubbleData: {
    type: Object,
    default: () => {},
  },
  activeLayer: {
    type: String,
    default: '',
  },
  locationActive: {
    type: Boolean,
    default: false,
  },
  locationLayerShow: {
    type: Boolean,
    default: false,
  },
  deviceDirectionList: {
    type: Array,
    default: () => [],
  },
  locationList: {
    type: Array,
    default: () => [],
  },
};
export default {
  props: props,
  data() {
    const validateName = (rule, value, callback) => {
      return validPass(rule, value, '请输入设备名称', callback);
    };
    const validateOrgCode = (rule, value, callback) => {
      return validPass(rule, value, '请选择所属单位', callback);
    };
    const validateCivilCode = (rule, value, callback) => {
      return validPass(rule, value, '请选择行政区划', callback);
    };
    const validateLng = (rule, value, callback) => {
      return validPass(rule, value, '请选择经度', callback);
    };
    const validateLat = (rule, value, callback) => {
      return validPass(rule, value, '请选择纬度', callback);
    };
    // const validateSbcjqy = (rule, value, callback) => {
    //   return validPass(rule, value, '请选择采集区域', callback);
    // };
    function validPass(rule, value, text, callback) {
      if (!value) {
        callback(new Error(text));
      } else {
        callback();
      }
    }
    return {
      formValidate: {
        deviceId: '',
        deviceName: null,
        orgCode: null,
        civilCode: null,
        longitude: null,
        latitude: null,
        address: '',
        sbcjqy: '',
        directionType: '',
        horizontalHeight: '',
        errorMessage: '',
        imageUrls: [],
        tagList: [],
        roomType: '', //室内外
      },
      ruleValidate: {
        deviceName: [{ required: true, validator: validateName, trigger: 'blur' }],
        orgCode: [{ required: true, validator: validateOrgCode, trigger: 'change' }],
        civilCode: [{ required: true, validator: validateCivilCode, trigger: 'change' }],
        longitude: [{ required: true, validator: validateLng, trigger: ['blur', 'change'] }],
        latitude: [{ required: true, validator: validateLat, trigger: ['blur', 'change'] }],
        address: [{ required: false, trigger: 'change' }],
        // sbcjqy: [
        //   { required: true, validator: validateSbcjqy, trigger: 'change' }
        // ],
      },
      selectOrgTree: {
        orgCode: null,
      },
      selectAreaTree: {
        regionCode: '',
      },
      testData: {},
      sbcjqyList: [],
      devId: '',
      visibleScence: false,
      viewIndex: 0,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      sbcjqyText: '',
      copyedFormValidate: {}, //拷贝的表单对象，用于后续保存时判断是否有变更表单的操作
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.getDeviceSbcjqyData();
    });
  },
  methods: {
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data, dataWithName) {
      const sbcjqyArr = dataWithName.map((item) => item.key);
      this.checkedTreeData = sbcjqyArr;
      this.formValidate.sbcjqy = sbcjqyArr.join('/');
    },
    async handleSave() {
      //保存时，先判定表单是否有变更
      const differenceList = this.$util.common.isObjectChanged(this.formValidate, this.copyedFormValidate);
      //若未发生变更则不发请求
      if (!differenceList.length) {
        this.handleReset();
        return;
      }
      try {
        const valid = await this.$refs['formValidate'].validate((valid) => valid);
        if (!valid) {
          this.$Message.error(' 请将信息填写完整!');
          return;
        }
        const params = {
          id: this.devId,
          ...this.formValidate,
          updateField: differenceList.join(','),
        };
        // const res = await this.$http.put(equipmentassets.devSpaceCollecteUpdate, params);
        await this.$http.put(equipmentassets.devSpaceCollecteUpdateV2, params);
        this.$Message.success('编辑成功！');
        // 保存已变更的信息
        this.copyedFormValidate = this.$util.common.deepCopy(this.formValidate);
        // this.$emit('on-close-bubble')
        this.handleReset(true);
      } catch (err) {
        console.log(err);
      }
    },
    handleLocation() {
      this.$emit('setLocation', this.bubbleData);
    },
    selectOrgCode(data) {
      this.formValidate.orgCode = data.orgCode;
    },
    selectedArea(area) {
      this.formValidate.civilCode = area.regionCode;
    },
    async getDeviceSbcjqyData() {
      try {
        let { data } = await this.$http.get(governanceevaluation.getDeviceSbcjqyData);
        this.sbcjqyList = this.$util.common.arrayToJson(data.data, 'code', 'parentCode');
      } catch (err) {
        console.log(err);
      }
    },
    lookImg(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
    edit() {
      this.$emit('handleEdit', this.bubbleData);
    },
    handleReset(isNeedLoadList = false) {
      this.$emit('handleCloseBubble', isNeedLoadList);
    },
  },
  components: {
    TagsMore: require('@/components/tags-more').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiTreeSelect: require('@/components/ui-tree-select').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    uiImage: require('@/components/ui-image').default,
    AreaSelect: require('@/components/area-select').default,
  },
  watch: {
    bubbleData: {
      handler(val) {
        this.$nextTick(() => {
          Object.keys(this.formValidate).forEach((key) => {
            this.$set(this.formValidate, key, val[key]);
          });
          this.devId = val.id;
          this.selectOrgTree.orgCode = val.orgCode;
          this.selectAreaTree.regionCode = val.civilCode;
          this.formValidate.orgName = val.orgName;
          this.formValidate.civilName = val.civilName;
          this.formValidate.directionTypeText = val.directionTypeText;
          this.formValidate.imageUrls = this.formValidate.imageUrls || [];
          this.formValidate.tagList = this.formValidate.tagList || [];
          this.formValidate.roomType = val.roomType; //室内/外
          let sbcjqyArr = [];
          if (this.formValidate.sbcjqy) {
            sbcjqyArr =
              this.formValidate.sbcjqy.indexOf('/') === -1
                ? [this.formValidate.sbcjqy]
                : this.formValidate.sbcjqy.split('/');
          } else {
            sbcjqyArr = [];
          }
          this.sbcjqyText = val.sbcjqyText || '';
          this.$set(this, 'checkedTreeData', sbcjqyArr);
          this.copyedFormValidate = this.$util.common.deepCopy(this.formValidate);
        });
      },
      deep: true,
    },
  },
};
</script>
