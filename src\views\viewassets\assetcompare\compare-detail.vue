<template>
  <div class="history-container auto-fill">
    <div class="history-wrapper auto-fill">
      <div class="header mb-md"></div>
      <div class="table-wrapper auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="tableLoading"
        >
        </ui-table>
      </div>
    </div>
  </div>
</template>
<script>
import assetcomparison from '@/config/api/assetcomparison';

export default {
  name: 'history',
  data() {
    return {
      tableLoading: false,
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'left' },
        { title: '比对对象', key: 'host', align: 'left' },
        { title: '设备总量', key: 'vuln_name', align: 'left' },
        { title: '独有数量 ', width: 80, key: 'vuln_level', align: 'left' },
        { title: '相同数量', key: 'vuln_url', align: 'left' },
        { title: '差异数量', key: 'description', align: 'left' },
        { title: '对账时间', key: 'advice', align: 'left' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          align: 'center',
        },
      ],
    };
  },
  created() {
    this.getHistoryDropDown();
  },
  methods: {
    async getHistoryDropDown() {
      try {
        let { data } = await this.$http.post(assetcomparison.getHistoryDropDown);
        console.log(data);
      } catch (e) {
        console.log(e);
      } finally {
      }
    },
    async getHistory() {
      try {
        let { data } = await this.$http.post(assetcomparison.getHistory);
        console.log(data);
      } catch (e) {
        console.log(e);
      } finally {
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style scoped lang="less">
.history-container {
  position: relative;
  height: 100%;
  background: #071b39;

  .history-wrapper {
    padding: 0 20px;

    .header {
      padding-bottom: 15px;
      border-bottom: 1px solid #074277;
    }
  }
}
</style>
