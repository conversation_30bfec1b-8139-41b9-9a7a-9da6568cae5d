<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-08 17:07:34
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-11 15:45:44
 * @Description: 卡片-新，完善ui-card功能
-->
<!-- TODO 后续测试合适，放到公共组件目录下，替换之前的ui-card -->
<template>
  <div :class="!disHover ? 'ui-module' : 'ui-module-hover'">
    <div class="module-head">
      <div v-if="oneTitle" class="title active">
        <span>{{ title }}</span>
      </div>
      <div class="module-tag" v-else>
        <div
          v-for="item in title"
          :class="[
            'title',
            item[defaultProp.value] === titleModel ? 'active' : '',
          ]"
          :key="item[defaultProp.label]"
          @click="tabClick(item[defaultProp.value])"
        >
          <span>{{ item[defaultProp.label] }}</span>
        </div>
      </div>
      <slot name="content"></slot>
      <slot name="extra"></slot>
    </div>
    <div :style="{ padding: paddingValue }" class="module-content">
      <slot></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: "UiModule",
  props: {
    // 卡片标题，可接收多个标题，单标题传字符串，多标题传数组
    title: {
      required: true,
      type: [String, Array],
      default: "",
    },
    // 默认字段，可配置
    defaultProp: {
      type: Object,
      default: () => {
        return { label: "label", value: "value" };
      },
    },
    // 当前选中的title，多title时必传
    titleModel: {
      type: [Number, String],
    },
    // 卡片内部间距，单位 px
    padding: {
      type: [Number, String],
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    paddingValue() {
      if (this.padding.constructor === Number) {
        return this.padding / 192 + "rem";
      } else {
        let padding = this.padding.split(",");
        return padding[0] / 192 + "rem" + " " + padding[1] / 192 + "rem";
      }
    },
    // 一个标题
    oneTitle() {
      return typeof this.title === "string";
    },
  },
  methods: {
    /**
     * @description: 切换卡片标题
     * @param {string | number} val 当前标题值
     */
    tabClick(val) {
      if (val === this.titleModel) return;
      this.$emit("tab-click", val);
    },
  },
};
</script>
<style lang="less" scoped>
.ui-module,
.ui-module-hover {
  width: 100%;
  height: 100%;
  background: #fff;
  box-shadow: 0px 3px 5px 0px fade(#93abce, 70%);
  border-radius: 4px;
  overflow: hidden;
  .module-head {
    width: 100%;
    height: 30px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .module-tag {
      display: inline-flex;
      div + div {
        margin-left: -1px;
      }
    }
    .active {
      background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%) !important;
      color: #fff !important;
      font-weight: bold !important;
    }
    .title {
      font-size: 16px;
      font-weight: 400;
      position: relative;
      cursor: pointer;
      line-height: 30px;
      text-align: center;
      background: #d3d7de;
      color: #666;
      transform: skewX(-18deg);
      padding: 0 23px;
      left: -6px;
      span {
        transform: skewX(18deg);
        display: inline-block;
      }
    }
  }
  .module-content {
    width: 100%;
    height: calc(~"100% - 30px");
    box-sizing: border-box;
    position: relative;
  }
}
.ui-module-hover:hover {
  // cursor: pointer;
  box-shadow: 0 0 2px rgba(255, 255, 255, 0.2);
}
</style>