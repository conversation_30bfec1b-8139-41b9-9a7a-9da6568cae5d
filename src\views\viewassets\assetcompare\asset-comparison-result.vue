<template>
  <div class="asset-comparison-wrap auto-fill">
    <div class="header">
      <div class="header-left">
        <div class="title">
          <span>
            <i class="icon-font icon-shujuyuan1 font-blue"></i>
            <span>数据对象：</span>
            <span v-if='isCurrentLevel'>{{ tabName.source }}</span>
            <span v-else>{{ configInfo.selfDomain | filterDic(getDeployNetwork) }}{{ tabName.target }}</span>
          </span>
          <span>
<!--            <i class="icon-font icon-jianceshebeishuliang"></i>-->
            <span>数据来源：</span>
            <span>
              {{ configInfo.deviceSource | filterDic(sourceList) }}
            </span>
          </span>
          <span>
<!--            <i class="icon-font icon-jianceshebeishuliang"></i>-->
            <span>功能类型：</span>
            <span>
              {{ configInfo.deviceSbgnlx | filterDic(propertySearchLbgnlx) }}
            </span>
          </span>
          <span>
<!--            <i class="icon-font icon-jianceshebeishuliang"></i>-->
            <span>设备状态：</span>
            <span>
              {{ configInfo.devicePhyStatus | filterDic(phystatusList) }}
            </span>
          </span>
        </div>
        <div class="content">
          <template v-for="(e, i) in statisticalQuantityList">
            <div class="content-item">
              <span>
                <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
              </span>
              <span>
                <div class="name">{{ e.name }}</div>
                <div class="count" :style="{ color: e.color }">
                  {{ e.leftCount }}
                </div>
              </span>
            </div>
          </template>
        </div>
      </div>
      <div class="header-right">
        <div class="title">
          <span>
            <i class="icon-font icon-shujuyuan1 font-blue"></i>
            <span v-if='isCurrentLevel'> 数据对象：{{ tabName.target }}</span>
            <span v-else>{{ configInfo.otherDomain | filterDic(getDeployNetwork) }}{{ tabName.target }}</span>
          </span>
          <!--          <span>-->
          <!--            <i class="icon-font icon-jianceshebeishuliang"></i>-->
          <!--            <span-->
          <!--              >数据源：-->
          <!--              {{ config.deviceSbgnlx | filterDic(propertySearchLbgnlx) }}-->
          <!--            </span>-->
          <!--          </span>-->
          <!--          <span>-->
          <!--            <i class="icon-font icon-jianceshebeishuliang"></i>-->
          <!--            <span>功能类型：{{ config.deviceSbgnlx | filterDic(propertySearchLbgnlx) }}</span>-->
          <!--          </span>-->
        </div>
        <div class="content">
          <template v-for="(e, i) in statisticalQuantityList">
            <div class="content-item">
              <span>
                <i class="icon-font" :class="e.icon" :style="{ color: e.color }"></i>
              </span>
              <span>
                <div class="name">{{ e.name }}</div>
                <div class="count" :style="{ color: e.color }">
                  {{ e.rightCount }}
                </div>
              </span>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="tab-line">
      <div class="tab-line-left">
        <tag-view :list="statusList" @tagChange="tabClick" ref="tagView"></tag-view>
      </div>
      <div class="tab-line-right">
        <Checkbox v-model="isAll" :disabled="!tableDataRightList.length && !tableDataLeftList.length">全部 </Checkbox>
        <Button type="primary" class="ml-lg button-blue" :loading="exportDataLoading" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
      </div>
    </div>
    <div class="content">
      <div class="content-left auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :row-key="'key'"
          :table-columns="tableColumnsLeftList"
          :table-data="tableDataLeftList"
          :loading="loading"
          :minusHeight="160"
          @rowDbClick="checkData"
          @selectTable="selectTableLeft"
        >
          <template v-for="(item, index) in tableColumnsLeftList" :slot="item.slot" slot-scope="{ row }">
            <span
              style="color: #bc3c19"
              v-if="row.inconsistentColumns && row.inconsistentColumns.includes(item.slot)"
            >{{ row[item.slot] }}</span
            >
            <span v-else>{{ row[item.slot] }}</span>
            <!-- <div v-else>
              <Tooltip placement="bottom-start">
                <span style="color: #bc3c19">
                  {{
                    row[item.slot].length > 10
                      ? `${row[item.slot].slice(1, 10) + "..."}`
                      : row[item.slot]
                  }}</span
                >
                <div slot="content">
                  {{ row[item.slot] }}
                </div>
              </Tooltip>
            </div> -->
          </template>
        </ui-table>
      </div>
      <div class="content-right auto-fill">
        <ui-table
          class="ui-table auto-fill"
          :table-columns="tableColumnsRightList"
          :table-data="tableDataRightList"
          :loading="searchInfo.searchType !== '2' ? loading : uniqueRightloading"
          :minusHeight="160"
          @rowDbClick="checkData"
          @selectTable="selectTableRight"
        >
          <template v-for="(item, index) in tableColumnsRightList" :slot="item.slot" slot-scope="{ row }">
            <span
              style="color: #bc3c19"
              v-if="row.inconsistentColumns && row.inconsistentColumns.includes(item.slot)"
            >{{ row[item.slot] }}</span
            >
            <span v-else>{{ row[item.slot] }}</span>
          </template>
        </ui-table>
      </div>
    </div>
    <div v-if="searchInfo.searchType !== '3'">
      <ui-page
        class="page"
        :page-data="pageData"
        @changePage="changeComparisonListPageNumber"
        @changePageSize="changeComparisonListPageSize"
      >
      </ui-page>
    </div>
    <div class="footer" v-if="searchInfo.searchType === '3'">
      <ui-page
        class="page page-left"
        :page-data="uniquePageDataLeft"
        @changePage="chanheUniqueLeftListPageNumber"
        @changePageSize="chanheUniqueLeftListPageSize"
      >
      </ui-page>
      <ui-page
        class="page page-right"
        :page-data="uniquePageDataRight"
        @changePage="chanheUniqueRightListPageNumber"
        @changePageSize="chanheUniqueRightListPageSize"
      >
      </ui-page>
    </div>
    <!--    <equipment-Info-comparison
      ref="info-comparison"
      :title="'设备信息比对'"
      :comparison-fields="comparisonFields"
      :table-data-left="tableDataLeft"
      :table-data-right="tableDatableRight"
      :resultData="resultData"
      @handleResetTableData="getComparisonResultList"
    ></equipment-Info-comparison>-->
  </div>
</template>

<script>
import assetcomparison from '@/config/api/assetcomparison.js';
import { mapActions, mapGetters } from 'vuex';
import {
  TAB_LIST,
  THIS_LEVEL_BY_GAT1400_FACE, THIS_LEVEL_BY_GAT1400_VEHICLE,
  THIS_LEVEL_BY_GB28181,
} from '@/views/viewassets/assetcompare/modules/enum.js';
import { isCurrentLevelByType } from '@/views/viewassets/assetcompare/modules/util';

export default {
  name: 'asset-comparison-result',
  components: {
    TagView: require('@/components/tag-view.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    /*EquipmentInfoComparison: require('./components/equipment-info-comparison.vue')
      .default,*/
  },
  props: {},
  data() {
    return {
      statusList: [
        {
          label: '相同',
          value: '1',
        },
        {
          label: '差异',
          value: '2',
        },
        {
          label: '独有',
          value: '3',
        },
      ],
      statisticalQuantityList: [
        {
          name: '设备总量',
          leftCount: 0,
          rightCount: 0,
          color: '#22C326',
          icon: 'icon-jianceshebeishuliang',
          leftKey: 'targetTotal',
          rightKey: 'sourceTotal',
        },
        {
          name: '信息相同设备',
          leftCount: 0,
          rightCount: 0,
          color: '#2B84E2',
          icon: 'icon-xinxixiangtongshebei',
          leftKey: 'same',
          rightKey: 'same',
        },
        {
          name: '信息差异设备',
          leftCount: 0,
          rightCount: 0,
          color: '#DD4826',
          icon: 'icon-xinxichayishebei',
          leftKey: 'diff',
          rightKey: 'diff',
        },
        {
          name: '独有设备',
          leftCount: 0,
          rightCount: 0,
          color: '#19D5F6',
          icon: 'icon-duyoushebei',
          leftKey: 'targetOnly',
          rightKey: 'sourceOnly',
        },
      ],
      tableDataLeftList: [],
      tableDataRightList: [],
      loading: false,
      uniqueRightloading: false,
      tableColumnsLeftList: [],
      tableColumnsRightList: [],
      uniqueLeftPageData: {},
      uniqueRightPageData: {},
      comparisonFields: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchInfo: {
        left: {
          tableType: '0',
        },
        right: {
          tableType: '1',
        },
        searchType: '1',
      },
      uniquePageDataLeft: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      uniquePageDataRight: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      tableDataLeft: {}, // 修改弹框数据-左
      tableDatableRight: {}, // 修改弹框数据-左
      resultHeaderInfo: {},
      isAll: false,
      exportDataLoading: false,
      selectionDataLeft: [], //左侧选中
      selectionDataRight: [], //右侧选中

      defaultTableColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
      ],
      selectionTableColumns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
        },
      ],
      //配置信息
      configInfo:{},
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
      getDeployNetwork: 'algorithm/getDeployNetwork',
      cancelSource: 'common/getSource',
    }),
    tabName() {
      let { type } = this.$route.query;
      let tab = TAB_LIST.find((item) => item.value === type);
      return tab || {};
    },
    isCurrentLevel() {
      const { type } = this.$route.query;
      return isCurrentLevelByType(type);
    },
  },
  watch: {
    isAll(val) {
      this.tableDataRightList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
      this.tableDataLeftList.forEach((item) => {
        this.$set(item, '_checked', val);
        this.$set(item, '_disabled', val);
      });
    },
  },
  async created() {
    this.deviceIds = [];
    this.deviceIdsRight = [];
    this.tableDataLeftList = [];
    this.tableDataRightList = [];
    this.isAll = false;
    if (this.propertySearchLbgnlx.length === 0) await this.getAlldicData();
    await this.getComparisonStatistics();
    await this.getComparisonTableHeader();
    if (this.searchInfo.searchType === '3') {
      this.queryLeftUniqueDeviceList();
      this.queryRightUniqueDeviceList();
    } else {
      this.getComparisonResultList();
    }
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
      setSource: 'common/setSource',
    }),
    compareField(row, column, index, target) {
      try {
        if (this.searchInfo.searchType === '3') return false; //独有 不对比
        if (!target.length) return false;
        return row[column['key']] !== target[index][column['key']];
      } catch (e) {
        console.log('对比', e);
      }
    },
    async getComparisonTableHeader() {
      this.tableColumnsLeftList = this.configInfo.compareField.map((item) => {
        return {
          title: item.fieldRemark,
          key: item.fieldName,
          align: 'left',
          ellipsis: true,
          render: (h, { row, column, index }) => {
            return (
              <span class={this.compareField(row, column, index, this.tableDataRightList) ? 'font-red' : ''}>{`${
                row[column['key']] || '--'
              }`}</span>
            );
          },
        };
      });
      this.tableColumnsRightList = this.configInfo.compareField.map((item) => {
        return {
          title: item.fieldRemark,
          key: item.fieldName,
          align: 'left',
          ellipsis: true,
          render: (h, { row, column, index }) => {
            return (
              <span class={this.compareField(row, column, index, this.tableDataLeftList) ? 'font-red' : ''}>{`${
                row[column['key']] || '--'
              }`}</span>
            );
          },
        };
      });

      if (this.searchInfo.searchType === '3') {
        // 独有 左右都需要勾选
        this.tableColumnsLeftList = [
          ...this.selectionTableColumns,
          ...this.defaultTableColumns,
          ...this.tableColumnsLeftList,
        ];
        this.tableColumnsRightList = [
          ...this.selectionTableColumns,
          ...this.defaultTableColumns,
          ...this.tableColumnsRightList,
        ];
      } else {
        // 非独有实际是同一条数据
        this.tableColumnsLeftList = [
          ...this.selectionTableColumns,
          ...this.defaultTableColumns,
          ...this.tableColumnsLeftList,
        ];
        this.tableColumnsRightList = [...this.defaultTableColumns, ...this.tableColumnsRightList];
      }
    },
    //相同差异对比结果
    async getComparisonResultList() {
      try {
        this.loading = true;
        this.tableDataLeftList = [];
        this.tableDataRightList = [];
        let { batchId, civilCode } = this.$route.query;
        let params = {
          batchId,
          civilCode,
          type: this.searchInfo.searchType,
        };
        let { entities, total } = await this.getDetail(params);
        if (entities && entities.length === 0) return;
        entities.forEach((item) => {
          this.tableDataLeftList.push({ ...item, ...JSON.parse(item.targetContent || '[]') });
          this.tableDataRightList.push({ ...item, ...JSON.parse(item.sourceContent || '[]') });
        });
        this.pageData.totalCount = total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },

    /**
     * 获取对账历史详情
     * @param params
     * @returns {Promise<any>}
     */
    async getDetail(params) {
      try {
        let {
          data: { data },
        } = await this.$http.post(assetcomparison.getDetail, params);
        return data;
      } catch (e) {
        console.log(e);
      }
    },
    async tabClick(index) {
      this.cancelSource.cancel && this.cancelSource.cancel('取消请求');
      this.setSource(this.$http.CancelToken.source());
      const { value } = this.statusList[index];
      this.searchInfo.searchType = value;
      this.deviceIds = [];
      this.deviceIdsRight = [];
      this.tableDataLeftList = [];
      this.tableDataRightList = [];
      this.isAll = false;

      this.uniquePageDataLeft = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.uniquePageDataRight = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      if (value === '3') {
        this.queryLeftUniqueDeviceList();
        this.queryRightUniqueDeviceList();
      } else {
        this.getComparisonResultList();
      }
    },
    // 差异对比弹框
    checkData(data) {
      if (this.searchInfo.searchType !== '2') {
        const { inconsistentColumns = [], idLeft, idRight } = data;
        this.tableColumnsLeftList.forEach((e) => {
          if (Object.keys(data).includes(e.slot)) {
            this.tableDataLeft[e.slot] = data[e.slot];
          }
        });
        this.tableColumnsRightList.forEach((e) => {
          if (Object.keys(data).includes(e.slot)) {
            this.tableDatableRight[e.slot] = data[e.slot];
          }
        });
        this.tableDataLeft = {
          ...this.tableDataLeft,
          idLeft,
          inconsistentColumns,
        };
        this.tableDatableRight = {
          ...this.tableDatableRight,
          idRight,
          inconsistentColumns,
        };
        setTimeout(() => {
          this.$refs['info-comparison'].init();
        }, 0);
      }
    },
    // 对比结果统计
    async getComparisonStatistics() {
      try {
        let { civilCode, batchId } = this.$route.query;
        let params = {
          batchId,
          civilCode,
        };
        let {
          data: { data },
        } = await this.$http.get(assetcomparison.getStatistics, { params });
        this.configInfo = data.configVo || {};
        this.statisticalQuantityList = this.statisticalQuantityList.map((e) => {
          return {
            ...e,
            leftCount: data[e.leftKey] || 0,
            rightCount: data[e.rightKey] || 0,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 独有-左
    async queryLeftUniqueDeviceList() {
      try {
        this.loading = true;
        this.tableDataLeftList = [];
        this.tableDataRightList = [];
        let { batchId, civilCode } = this.$route.query;
        let data = await this.getDetail({
          batchId,
          civilCode,
          type: this.searchInfo.searchType,
          pageNumber: this.uniquePageDataLeft.pageNum,
          pageNum: this.uniquePageDataLeft.pageSize,
        });
        const { entities, total } = data;
        this.tableDataLeftList = entities.map((item) => {
          return {
            ...item,
            ...JSON.parse(item.targetContent || '{}'),
          };
        });
        this.uniquePageDataLeft.totalCount = total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 独有-右 调用两次 传 3 4
    async queryRightUniqueDeviceList() {
      try {
        this.uniqueRightloading = true;
        this.tableDataLeftList = [];
        this.tableDataRightList = [];
        let { batchId, civilCode } = this.$route.query;
        let data = await this.getDetail({
          batchId,
          civilCode,
          type: '4',
          pageNumber: this.uniquePageDataRight.pageNum,
          pageNum: this.uniquePageDataRight.pageSize,
        });
        const { entities, total } = data;
        this.tableDataRightList = entities.map((item) => {
          return {
            ...item,
            ...JSON.parse(item.sourceContent || '{}'),
          };
        });
        this.uniquePageDataRight.totalCount = total;
      } catch (e) {
        console.log(e);
      } finally {
        this.uniqueRightloading = false;
      }
    },
    // 相同差异 分页
    changeComparisonListPageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getComparisonResultList();
    },

    changeComparisonListPageNumber(val) {
      this.pageData.pageNum = val;
      this.getComparisonResultList();
    },
    // 独有-左分页
    chanheUniqueLeftListPageSize(val) {
      this.uniquePageDataLeft.pageNum = 1;
      this.uniquePageDataLeft.pageSize = val;
      this.queryLeftUniqueDeviceList();
    },

    chanheUniqueLeftListPageNumber(val) {
      this.uniquePageDataLeft.pageNum = val;
      this.queryLeftUniqueDeviceList();
    },
    // 独有-右分页
    chanheUniqueRightListPageSize(val) {
      this.uniquePageDataRight.pageNum = 1;
      this.uniquePageDataRight.pageSize = val;
      this.queryRightUniqueDeviceList();
    },

    chanheUniqueRightListPageNumber(val) {
      this.uniquePageDataRight.pageNum = val;
      this.queryRightUniqueDeviceList();
    },
    selectTableLeft(selection) {
      this.selectionDataLeft = selection;
    },
    selectTableRight(selection) {
      this.selectionDataRight = selection;
    },
    // 处理全选分页
    handleData(array) {
      array.forEach((item) => {
        this.$set(item, '_checked', true);
        this.$set(item, '_disabled', true);
      });
      return array;
    },
    async exportExcel() {
      const { selectionDataLeft, selectionDataRight, isAll } = this;
      if (!isAll && !selectionDataLeft.length && !selectionDataRight.length) {
        return this.$Message.error('请先选择导出数据');
      }
      let { batchId, civilCode } = this.$route.query;
      let params = {
        batchId,
        civilCode,
        type: this.searchInfo.searchType,
      };
      let sourceIds = selectionDataRight.map((item) => item.id);
      let targetIds = selectionDataLeft.map((item) => item.id);
      switch (this.searchInfo.searchType) {
        case '3': //独有
          params = {
            ...params,
            targetOnlySelect: targetIds,
            sourceOnlySelect: sourceIds,
          };
          break;
        case '1':
          params = {
            ...params,
            sameSelect: targetIds,
          };
          break;
        case '2':
          params = {
            ...params,
            diffSelect: targetIds,
          };
          break;
      }
      try {
        console.log(params);
        this.exportDataLoading = true;
        let res = await this.$http.post(assetcomparison.detailExport, params, {
          responseType: 'blob',
        });
        this.$util.common
          .exportfile(res)
          .then()
          .catch((err) => {
            this.$Message.warning(err.msg);
          });
      } catch (err) {
        console.log(err);
      } finally {
        this.exportDataLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.asset-comparison-wrap {
  height: 100%;
  padding: 0 20px 20px;
  .header {
    display: flex;
    > div {
      width: 50%;
      padding-bottom: 10px;
      .title {
        height: 53px;
        line-height: 53px;
        font-size: 16px;
        color: #2b84e2;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        & > span {
          margin-right: 62px;
          i {
            margin-right: 4px;
          }
        }
      }
      .content {
        height: 110px;
        display: flex;
        align-items: center;
        width: 100%;
        background: #08264d;
        .content-item {
          width: 25%;
          display: flex;
          align-items: center;
          position: relative;
          .icon-font {
            font-size: 37px;
            margin-left: 30px;
            margin-right: 10px;
          }
          .name {
            font-size: 14px;
            color: #f5f5f5;
          }
          .count {
            font-size: 18px;
          }
          &:first-child::after {
            content: '';
            width: 0px;
          }
        }
        .content-item::after {
          content: '';
          width: 1px;
          height: 40px;
          position: absolute;
          top: 10px;
          left: 0;
          background: #1568ad;
        }
      }
    }
    .header-left {
      border-right: 1px solid #074277;
      padding-right: 10px;
    }
    .header-right {
      padding-left: 10px;
    }
  }
  .tab-line {
    display: flex;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    background: #08264d;
    padding: 10px;
    margin-bottom: 10px;
    .ivu-tabs-tab {
      float: left;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      border: 1px solid #1b82d2;
      border-right: none;
      padding: 0 22px;
      color: #406186;
      &:hover {
        background: #2b84e2;
        color: #fff;
        cursor: pointer;
      }
      &:last-child {
        border-right: 1px solid #1b82d2;
      }
    }
    .active {
      background: #2b84e2;
      color: #fff;
    }
    .tab-line-right {
      display: flex;
      align-items: center;
    }
    .tab-line-left {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  /deep/ .content {
    display: flex;
    .content-left {
      width: 50%;
      border-right: 1px solid #074277;
      padding-right: 10px;
    }
    .content-right {
      width: 50%;
      padding-left: 10px;
    }
    .content-left,
    .content-right {
      .ui-table {
        .ivu-table-column-left {
          padding-left: 10px;
        }
      }
    }
    .ivu-table-cell-ellipsis {
      .ivu-table-cell-slot {
        > span {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .footer {
    display: flex;
    .page-right,
    .page-left {
      width: 50%;
      padding: 20px 8px;
    }
  }
}
</style>
