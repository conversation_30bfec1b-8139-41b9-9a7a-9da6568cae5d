export default {
  pageList: '/ivdg-governance-app/auto/governance/task/pageList', // 查询任务列表
  getTaskStatusList: '/ivdg-governance-app/auto/governance/task/getTaskStatusList', // 获取任务状态列表
  delete: '/ivdg-governance-app/auto/governance/task/delete', // 删除任务
  edit: '/ivdg-governance-app/auto/governance/task/edit', // 新增/编辑任务
  getSourceTypeList: '/ivdg-governance-app/auto/governance/task/getSourceTypeList', // 获取资源类型列表
  getGovernanceContentBysourceType: '/ivdg-governance-app/auto/governance/task/getGovernanceContentBysourceType', // 根据资源类型获取治理内容
  start: '/ivdg-governance-app/auto/governance/task/start', // 启动任务
  pause: '/ivdg-governance-app/auto/governance/task/pause', // 暂停任务
  getView: '/ivdg-governance-app/auto/governance/task/view', // 查询任务详细信息
  governanceResultPageList: '/ivdg-governance-app/auto/governance/task/governanceResultPageList', // 查看结果(时钟和设备)
  getDeviceView: '/ivdg-governance-app/fullInMessage/osdInfo', // 获取设备详细信息
  deviceResetSub: '/ivdg-governance-app/fullInMessage/resetSub', // 设备重设字幕
  governanceResultPageListExport: '/ivdg-governance-app/auto/governance/task/governanceResultPageListExport', // 导出
};
