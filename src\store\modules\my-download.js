import localforage from 'localforage';
import streamSaver from "@jeremyckahn/streamsaver"
streamSaver.mitm = process.env.NODE_ENV === 'development' ? `http://${location.host}/mitm.html` : `https://${location.host}/mitm.html`

let downloadObj = {} // 根据id缓存
export default {
  namespaced: true,
  state: {
    downloadList: [],   // 下载记录
  },
  mutations: {
    setDownloadList(state, array) {
      state.downloadList = array
    },
  },
  getters: {
    getDownloadList (state) {
      return state.downloadList
    }
  },
  actions: {
    setDownloadList({commit}) {
      localforage.getItem('downloadList').then(function(value) {
        value && value.forEach(v => {
          if (v.state == 'download') v.state = 'stop'
        })
        commit('setDownloadList', value ? value : [])
      })
    },
    download({commit, state}, {url, id: itemId, reDownload}) {
      if (!url) return
      let match = url.match(/name=([^&]+)/)
      let fileName = match ? match[1] : '录像.mp4'
      let id = itemId ? itemId : Date.now()
      let prevTimestamp, prevLoaded = 0, isNew = false
      if (reDownload) downloadObj[id] = null
      if (!downloadObj[id]) {
        // 创建新的下载
        isNew = true
        downloadObj[id] = {
          receivedSize: 0,
          state: 'download'
        }
      }
      downloadObj[id].abortController = new AbortController()
      try {
        fetch(url, {
          headers: {
              "Range": "bytes=" + downloadObj[id].receivedSize.toString() + "-"
          },
          signal: downloadObj[id].abortController.signal,
        }).then(res => {
          let totalSize = parseInt(res.headers.get('Content-Length'));
          totalSize = isNaN(totalSize) ? parseInt(res.headers.get("Content-Range").split("/")[1]) : totalSize;
          downloadObj[id].reader = res.body.getReader()
          // 缓存
          prevTimestamp = Date.now()
          if (isNew) {
            let fileStream = streamSaver.createWriteStream(fileName, {size: totalSize})
            downloadObj[id].writer = fileStream.getWriter()
            if (!itemId) {
              let item = {
                id,
                url,
                fileName,
                state: 'download',
                totalSize,
                receivedSize: downloadObj[id].receivedSize,
                time: new Date().format('yyyy-MM-dd hh:mm:ss')
              }
              state.downloadList.unshift(item)
              localforage.setItem('downloadList', state.downloadList)
            } else {
              let i = state.downloadList.find(v => v.id == id)
              i.state = 'download'
              downloadObj[id].state = 'download'
            }
          } else {
            let i = state.downloadList.find(v => v.id == id)
            i.state = 'download'
            downloadObj[id].state = 'download'
          }
          return downloadObj[id].reader
        }).then(async function (reader){
          let i = state.downloadList.find(v => v.id == id)
          let ok = false
          while(!ok){
            let con = await reader.read();
            ok = con.done;
            if(typeof con.value == "undefined"){break;}
            // 计算速度
            const currentTimestamp = Date.now();
            const elapsedTime = currentTimestamp - prevTimestamp;
            if (elapsedTime > 1000) {
              const downloadSpeed = (downloadObj[id].receivedSize - prevLoaded) / (elapsedTime / 1000);
              i.downloadSpeed = downloadSpeed
              prevTimestamp = currentTimestamp
              prevLoaded = downloadObj[id].receivedSize
            }
            try {
              await downloadObj[id].writer.write(con.value)
              downloadObj[id].receivedSize += con.value.byteLength;
              i.receivedSize = downloadObj[id].receivedSize
              localforage.setItem('downloadList', state.downloadList)
            } catch(e) {
              downloadObj[id].abortController.abort()
              downloadObj[id].reader.cancel();
              downloadObj[id].writer.abort()
              let i = state.downloadList.find(v => v.id == id)
              i.state = i.state == 'download' ? 'stop' : i.state
              downloadObj[id].state = 'stop'
              localforage.setItem('downloadList', state.downloadList)
            }
          }
          if(ok){
            downloadObj[id].writer.close()
            i.state = 'success'
            downloadObj[id].state = 'success'
            localforage.setItem('downloadList', state.downloadList)
          }
        })
      } catch(e) {
        let i = state.downloadList.find(v => v.id == id)
        if (i) i.state = 'error'
        if (downloadObj[id]) downloadObj[id].state = 'error'
        localforage.setItem('downloadList', state.downloadList)
      }
    },
    stopItem({commit, state}, id) {
      if (downloadObj[id] && downloadObj[id].state == 'download') {
        downloadObj[id].state = 'stop'
        downloadObj[id].abortController.abort()
        // downloadObj[id].writer.abort()
        downloadObj[id].reader.cancel()
      }
      let i = state.downloadList.find(v => v.id == id)
      i.state = 'stop'
      localforage.setItem('downloadList', state.downloadList)
    },
    delItem({commit, state}, id) {
      if (downloadObj[id] && downloadObj[id].state == 'download') {
        downloadObj[id].state = 'stop'
        downloadObj[id].abortController.abort()
        downloadObj[id].writer.abort()
        downloadObj[id].reader.cancel()
      }
      let index = state.downloadList.findIndex(v => v.id == id)
      state.downloadList.splice(index, 1)
      localforage.setItem('downloadList', state.downloadList)
    },
    cancleAll({commit, state}) {
      Object.entries(downloadObj).forEach(([key, value]) => {
        if (value.state == 'download') {
          value.abortController.abort()
          value.writer.abort()
          value.reader.cancel()
          let i = state.downloadList.find(v => v.id == key)
          i.state = i.state == 'download' ? 'stop' : i.state
          localforage.setItem('downloadList', state.downloadList)
        }
      });
    }
  }
}
