<template>
  <div
    :class="['advanced-search-result', { 'expand-menu': !expand }]"
    ref="advanced"
  >
    <div class="search-result-inner">
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in advancedMenuList"
          :key="index"
          :name="item.name"
          v-permission="item.permission"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
          <div
            class="search-active"
            v-if="sectionName === item.name && !expand"
          ></div>
        </MenuItem>
      </Menu>
      <transition :name="!expand ? 'draw' : ''">
        <keep-alive>
          <component
            @dataAboveMapHandler="dataAboveMapHandler"
            :menuType="menuType"
            ref="menuPage"
            @mapSearching="mapSearching"
            :mapList="mapList"
            :mapOnData="true"
            :is="sectionName"
            class="search-result-content"
            v-show="expand"
          ></component>
        </keep-alive>
      </transition>
      <div class="switch" @click="switchHandle">
        <img v-if="!expand" src="@/assets/img/expand.png" alt="" />
        <img v-else src="@/assets/img/stow.png" alt="" />
      </div>
    </div>
  </div>
</template>

<script>
import faceContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/face-contents";
import vehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/vehicle-content";
import humanBodyContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/human-body-content.vue";
import nonmotorVehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/nonmotor-vehicle-content.vue";
export default {
  components: {
    faceContent, //人脸模块
    humanBodyContent,
    nonmotorVehicleContent,
    vehicleContent,
  },
  props: {
    layerManageMap: {
      type: Object,
      default: () => {
        return {};
      },
    },
    menuType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      advancedMenuList: [
        {
          label: "人像",
          value: 2,
          iconName: "icon-renlian",
          name: "faceContent",
          permission: ["track-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicleContent",
          permission: ["track-vehicle"],
        },
        {
          label: "人体",
          value: 8,
          iconName: "icon-renti",
          name: "humanBodyContent",
          permission: ["track-humanBody"],
        },
        {
          label: "非机动车",
          value: 9,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicleContent",
          permission: ["track-nonmotorVehicle"],
        },
      ],
      sectionName: "",
      expand: true,
      mapList: [], // 已上图数据
    };
  },
  mounted() {
    let permissionList = this.advancedMenuList.filter((d) =>
      this.$_has(d.permission)
    );
    this.sectionName = permissionList.length > 0 ? permissionList[0].name : "";
  },
  methods: {
    // 点击-切换
    selectItemHandle(sectionName) {
      this.expand = true;
      this.sectionName = sectionName;
      this.currentClickIndex = -1;
      this.mapList = this.layerManageMap[sectionName] || [];
    },
    // 收起-展开
    switchHandle() {
      this.expand = !this.expand;
    },
    mapSearching(data, page) {
      console.log(data, page, "advanced");
      this.sectionName = page;
      this.$nextTick(() => {
        this.$refs.menuPage.mapSearchingPage(data, page);
      });
    },
    // 数据上图
    dataAboveMapHandler(mapData) {
      if (!this.menuType) {
        this.switchHandle();
      }
      this.$emit("updateLayerManager", mapData);
    },
  },
};
</script>

<style lang="less" scoped>
.advanced-search-result {
  position: absolute;
  left: 20px;
  top: 66px;
  height: calc(~"100% - 88px");
  width: calc(~"100% - 90px");
  .draw-enter-active,
  .draw-leave-active {
    transition: transform 0.8s;
  }
  .draw-enter,
  .draw-leave-to {
    transform: translateX(-100%);
  }
  .search-result-inner {
    height: 100%;
    width: 100%;
    position: relative;
    .search-active {
      width: 10px;
      height: 10px;
      border-radius: 5px;
      background: #f8775c;
    }
  }
  .switch {
    width: 18px;
    height: 90px;
    position: absolute;
    bottom: 10px;
    top: 50%;
    right: -68px;
    transform: translateY(-50%);
    cursor: pointer;
    > img {
      width: 100%;
    }
  }
  .search-result-content {
    position: absolute;
    left: 50px;
    top: 0px;
    height: 100%;
    width: 100%;
    background: rgb(255, 255, 255);
    box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
    border-radius: 0px 0px 4px 4px;
    display: flex;
    // z-index: 999;
    // overflow: hidden;
  }
  /deep/.ivu-menu {
    z-index: 11 !important;
    width: 100%;
    position: relative;
    .ivu-menu-item-active {
      position: relative;
      .search-active {
        position: absolute;
        top: 3px;
        right: 3px;
      }
    }
  }
}
.expand-menu {
  width: 0px;
}
</style>
