import * as PortraitTool from '@/util/module/common';
/**
 * creator: wyr
 * time: 2017.6.3
 * description: 地图弹框工厂, 用于新建弹出，缓存弹窗信息
 */
export class InfoWindowFactory {
  cacheInfoWindows;

  /**
   * 从缓存中移除infoWindow
   * @param winId
   * @returns 返回移除的infoWindow实例
   */
  removeInfoWindow(winId) {
    let result;
    if (this.cacheInfoWindows && this.cacheInfoWindows[winId]) {
      result = this.cacheInfoWindows[winId];
      this.cacheInfoWindows[winId] = null;
      delete this.cacheInfoWindows[winId];
      // console.debug("this.cacheInfoWindows",this.cacheInfoWindows);
    }
    return result;
  }

  getById(winId) {
    if (this.cacheInfoWindows && this.cacheInfoWindows[winId]) {
      return this.cacheInfoWindows[winId];
    } else {
      return;
    }
  }

  addInfoWindow(win) {
    if (!this.cacheInfoWindows) {
      this.cacheInfoWindows = {};
    }
    let uuid = PortraitTool.getUUID();
    this.cacheInfoWindows[uuid] = win;
    return uuid;
  }

  addEventListener(win, uuid, callbacks) {
    if (win == null) return;
    if (callbacks == null) return;

    win.addEventListener(NPMapLib.INFOWINDOW_EVENT_OPEN, () => {
      if (typeof callbacks['open'] === 'function') {
        callbacks['open'].call(win, uuid);
      }
    });
    win.addEventListener(NPMapLib.INFOWINDOW_EVENT_CLOSE, () => {
      if (typeof callbacks['close'] === 'function') {
        callbacks['close'].call(win, uuid);
      }
    });
    win.addEventListener(NPMapLib.INFOWINDOW_EVENT_HIDE, () => {
      if (typeof callbacks['hide'] === 'function') {
        callbacks['hide'].call(win, uuid);
      }
    });
  }

  constructor() {}
  /**
   * 获取infowindow弹出框实例
   * @param point
   * @param title
   * @param dom
   * @param opts
   * time: 2017.6.3
   * @returns {NPMapLib.Symbols.InfoWindow}
   */
  static getInfoWindow(point, title, dom, opts) {
    return new NPMapLib.Symbols.InfoWindow(point, title, dom, opts);
  }

  /**
   * 获得弹出框的配置信息
   * @param opts
   * time: 2017.6.3
   * @returns {any}
   */
  static getInfoWindowOpts(opts) {
    return PortraitTool.extend(true, {}, this.getDefaultInfoWindowOpts(), opts);
  }

  /**
   * 默认弹出框信息
   * time: 2017.6.3
   * @returns {infoWindowOpts}
   */
  static getDefaultInfoWindowOpts() {
    return {
      width: 404,
      height: 404,
      offset: new NPMapLib.Geometry.Size(0, 15),
      iscommon: false,
      enableCloseOnClick: false,
      isAnimationOpen: true,
      isAdaptation: true,
      positionBlock: {
        imageSrc: '/libs/npgis/1.0.4.0/Netposa/img/iw_tail.png',
        imageSize: new NPMapLib.Geometry.Size(51, 31),
      },
    };
  }
}
