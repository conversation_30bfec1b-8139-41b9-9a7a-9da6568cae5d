<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="姓名:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="前科类型:" prop="recordKind">
      <Input
        v-model="queryParam.recordKind"
        placeholder="请输入"
        style="width: 80px"
      ></Input>
    </FormItem>
    <FormItem label="年龄:">
      <div class="flex-box">
        <Input
          v-model="queryParam.minAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
        <div class="separtor"></div>
        <Input
          v-model="queryParam.maxAge"
          placeholder="请输入"
          style="width: 80px"
        ></Input>
      </div>
    </FormItem>
    <FormItem label="民族:" prop="national">
      <Select
        v-model="queryParam.national"
        placeholder="请选择"
        multiple
        transfer
        :max-tag-count="1"
      >
        <Option
          :value="item.dataKey"
          v-for="item in nationTypeList"
          :key="item.dataKey"
          >{{ item.dataValue }}</Option
        >
      </Select>
    </FormItem>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
export default {
  data() {
    return {
      selectDeviceList: [], // 选中的设备
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        recordKind: "",
        minAge: "",
        maxAge: "",
      },
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
    keyWords() {
      return this.$route.query.keyWords || "";
    },
  },
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 选择设备，打开弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceGbId);
    },

    /**
     * @description: 重置
     */
    reset() {},

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
.gerling {
  color: #f29f4c;
}
.hk {
  color: #2c86f8;
}
.separtor {
  margin: 0 10px;
  width: 16px;
  height: 1px;
  background: rgba(0, 0, 0, 0.45);
}
.flex-box {
  display: flex;
  align-items: center;
}
.select-record-kind {
  /deep/.ivu-select-item {
    width: 210px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
</style>
