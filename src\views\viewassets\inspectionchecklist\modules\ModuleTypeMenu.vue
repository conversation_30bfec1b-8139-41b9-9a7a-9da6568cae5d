<template>
  <div class="module-type-menu-box">
    <div
      v-for="item in showModuleTypes"
      class="module-item pointer base-text-color flex-row"
      :class="{ 'active': item.id === activedModule.id }"
      :key="item.id"
      @click="handleClickModuleItem(item)"
    >
      <div class="flex-aic">
        <i class="icon-font f-14 mr-sm" :class="item.iconName"></i>
        <span class="inline ml-xs f-14">{{ item.name }}</span>
      </div>
      <Button
        v-if="activedModule.id === configModuleType.id && item.id === configModuleType.id"
        type="text"
        :loading="analysisLoading"
        @click.stop="handleAnalysis"
      >
        <span class="font-white f-12">立即分析</span>
      </Button>
    </div>
  </div>
</template>
<script>
import { configModuleType } from '../utils/enum.js';
export default {
  name: 'moduleTypeMenu',
  props: {
    showModuleTypes: {
      type: Array,
      default: () => [],
    },
    activedModule: {
      type: Object,
      default: () => ({
        name: '综合巡检清单',
        id: 1,
      }),
    },
    analysisLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      configModuleType,
    };
  },
  methods: {
    handleClickModuleItem(item) {
      this.$emit('handleClickModuleItem', item);
    },
    //点击立即分析
    handleAnalysis() {
      if (this.analysisLoading) {
        return;
      }
      this.$emit('handleAnalysis');
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .module-type-menu-box {
    .module-item {
      @{_deep} .ivu-btn-text {
        color: var(--color-btn-text);
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .module-type-menu-box {
    .module-item {
      @{_deep} .ivu-btn-text {
        color: var(--color-select-item-active);
      }
    }
  }
}
.module-type-menu-box {
  .module-item {
    height: 42px;
    padding: 0 10px 0 15px;
    .icon-font {
      color: var(--color-vertical-tab-icon);
    }
    &:hover {
      background: var(--bg-vertical-tab-hover);
      color: var(--color-vertical-tab-hover);
    }
  }
  .active {
    background: var(--bg-vertical-tab-active) !important;
    color: var(--color-vertical-tab-active) !important;
    .icon-font {
      color: var(--color-vertical-tab-active);
    }
  }
}
</style>
