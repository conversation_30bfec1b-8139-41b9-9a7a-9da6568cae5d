import { dataMonitorOfflineDetail, dataMonitorDayCapLine, dataMonitorDayCapCustom } from '@/util/module/doEcharts.js';

export class chartsFactory {
  dealChartData(dataList, colors, state) {
    let timeData = [];
    let lastTime = null;
    dataList.forEach((row, index) => {
      row.timerAxis.forEach((rw) => {
        lastTime = rw.t2; //记录最后一条的结束时间，以用于做今天到24点的空白填充
        timeData.push({
          itemStyle: { color: colors[rw.state] },
          name: state[rw.state],
          value: [index, `2020-01-01 ${rw.t1}`, `2020-01-01 ${rw.t2}`, rw.state === 0],
        });
      });
    });
    // 给自定义图表设置空白的填充
    if (dataList?.length && lastTime && lastTime !== ' 24:00') {
      timeData.push({
        itemStyle: { color: 'transparent' },
        name: '',
        value: [dataList.length, `2020-01-01 ${lastTime}`, '2020-01-01 24:00', false],
      });
    }
    return timeData;
  }
  getOfflineColor(row) {
    if (row.timerAxis.length === 1) {
      if (row.timerAxis[0].state === 2) {
        return '#2B84E2';
      } else if (row.timerAxis[0].state === 1) {
        return $var('--color-normal');
      } else {
        return $var('--color-error');
      }
    } else {
      return $var('--color-error');
    }
  }

  //获取持续无数据Option
  getContinuousNoDataOption(chartData) {
    try {
      const colors = [$var('--color-error'), $var('--color-normal'), '#2B84E2'];
      const state = ['离线', '在线'];
      const params = {
        colors: colors,
        state: state,
        yAxis: chartData.map((row) => row.dayText) || [],
        categoryData:
          chartData.map((row) => {
            return {
              value: row.duration,
              textStyle: {
                color: this.getOfflineColor(row),
              },
            };
          }) || [],
        data: this.dealChartData(chartData, colors, state),
      };
      return dataMonitorOfflineDetail(params);
    } catch (err) {
      console.log(err);
    }
  }

  //获取离线详情detail
  getOfflineDetailOption(chartData) {
    try {
      const colors = [$var('--color-error'), $var('--color-normal'), '#2B84E2'];
      const state = ['离线', '在线', '报备'];
      const params = {
        colors: colors,
        state: state,
        yAxis: chartData.map((row) => row.dayText) || [],
        categoryData:
          chartData.map((row) => {
            return {
              value: row.duration,
              textStyle: {
                color: this.getOfflineColor(row),
              },
            };
          }) || [],
        data: this.dealChartData(chartData, colors, state),
      };
      return dataMonitorOfflineDetail(params);
    } catch (err) {
      console.log(err);
    }
  }

  //获取日抓拍自定义option
  getDayCaptureCustomOptions(chartData) {
    try {
      const colors = [$var('--color-error'), $var('--color-normal'), '#2B84E2'];
      const state = ['无数据', '有数据'];
      const params = {
        colors: colors,
        state: state,
        yAxis: chartData.map((row) => row.checkTimeD) || [],
        categoryData:
          chartData.map((row) => {
            return {
              value: row.duration,
              textStyle: {
                color: this.getOfflineColor(row),
              },
            };
          }) || [],
        data: this.dealChartData(chartData, colors, state),
      };
      return dataMonitorDayCapCustom(params);
    } catch (err) {
      console.log(err);
    }
  }

  //获取日抓拍折线图options
  getDayCaptureLineOptions(params) {
    return dataMonitorDayCapLine(params);
  }
}
