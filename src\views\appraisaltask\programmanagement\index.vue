<template>
  <div class="page-evaluationmanagement">
    <div class="left-content auto-fill">
      <div class="record-title font-active-color">
        <!-- <i class="font_family icon-fanganmoxing f-14"></i> -->
        <span class="name base-text-color f-14">评测方案</span>
        <Button type="primary" @click="showAddModule('add')" class="btn_add fr">
          <i class="icon-font icon-tianjia f-14" title="添加"> </i>
          <span class="inline vt-middle ml-sm">新建方案</span>
        </Button>
      </div>
      <div class="record-search">
        <Input
          v-model="schemeData.name"
          :clearable="true"
          @on-clear="infoSearch"
          @on-enter="infoSearch"
          placeholder="请输入考核方案内容"
        >
          <i class="icon-font icon-sousuo" @on-click="infoSearch" slot="suffix"></i>
          <!-- <Icon type="ios-search" /> -->
        </Input>
      </div>
      <div
        class="assessment-list"
        v-infinite-scroll="handleReachBottom"
        :infinite-scroll-immediate="false"
        infinite-scroll-distance="10"
        v-ui-loading="{ tableData: moduleList }"
      >
        <Card
          v-for="(item, index) in moduleList"
          :key="index"
          :class="{ active: item.id === activeModuleMessageId }"
          class="card-box"
        >
          <div class="collapse-content-p pointer" @click="selectModule(item, index)">
            <div class="card-desc">
              <span :class="['icon-font', 'f-14', 'vt-middle', getIconByType(item.type)]"></span>
              <span class="ml-sm vt-middle" :title="item.name">{{ item.name }}</span>
            </div>
            <div class="operationBox">
              <tag :type="item.type" class="mr-sm">{{ item.typeText }}</tag>
              <span class="kpi-text">共{{ item.indexCount }}个考核指标</span>
              <!-- <span class="icon-font icon-shanchu2 fr f-14 btn-defalut"></span> -->
              <Button
                :disabled="item.type != 3"
                type="text"
                title="编辑"
                class="fr f-14 mr-sm btn-text"
                @click.stop="showAddModule('edit', item)"
                ><span
                  class="icon-font icon-bianji f-14"
                  :class="
                    item.type == 3
                      ? 'btn-defalut'
                      : 'btn-disable' && item.id === activeModuleMessageId
                        ? 'btn-del-focus'
                        : 'btn-disable'
                  "
                ></span
              ></Button>
              <Button
                :disabled="item.type != 3"
                type="text"
                title="删除"
                class="f-14 fr btn-text"
                @click.stop="deleteModule(item)"
                ><span
                  class="icon-font icon-shanchu2 f-14"
                  :class="
                    item.type == 3
                      ? 'btn-defalut'
                      : 'btn-disable' && item.id === activeModuleMessageId
                        ? 'btn-del-focus'
                        : 'btn-disable'
                  "
                ></span
              ></Button>
              <!-- <span
                @click.stop="showAddModule('edit', item)"
                class="fr f-14 btn-defalut mr-sm"
              ></span> -->
            </div>
          </div>
        </Card>
        <div v-show="loading">
          <img src="@/assets/img/common/loading.gif" alt="" />
        </div>
      </div>
    </div>
    <div class="right-content">
      <!--   srogramme-table     -->
      <srogramme-table
        ref="childTable"
        :active-module-message-id="activeModuleMessageId"
        :active-version="activeVersion"
        :scheme-value="schemeValue"
      ></srogramme-table>
    </div>

    <add-check-module
      v-model="moduleShow"
      :modal-action="moduleAction"
      :modal-id="moduleId"
      @update="update"
    ></add-check-module>
  </div>
</template>

<style lang="less" scoped>
.page-evaluationmanagement {
  overflow: hidden;
  height: 100%;
  .left-content {
    border-right: 1px solid var(--border-color);
    background: var(--bg-content);
    float: left;
    width: 300px;
    background: var(--bg-content);
    height: 100%;
    padding: 10px;

    .record-title {
      height: 34px;
      line-height: 34px;
      margin: 0 10px 10px;

      .name {
        position: relative;
        font-weight: bold;
      }

      .btn_add {
        .icon-dianjishangchuantupian-line {
          float: left;
        }

        span {
          float: right;
        }
      }
    }
    .record-search {
      padding-left: 10px;
      padding-right: 10px;
      margin-bottom: 10px;
      @{_deep} .ivu-input-suffix .icon-font {
        color: var(--color-primary);
      }
    }
    .assessment-list {
      position: relative;
      height: 100%;
      overflow-y: auto;
      padding-left: 10px;
      padding-right: 10px;
      @{_deep} .ivu-card {
        margin-bottom: 10px;
        border: 1px solid var(--border-vertical-tab);
        border-radius: 4px;
        background: var(--bg-vertical-tab);
        &:hover {
          background-color: var(--bg-vertical-tab-hover);
          .collapse-content-p {
            color: var(--color-vertical-tab-hover);
            .btn-defalut {
              color: var(--color-vertical-hover-tab-btn);
              &:hover {
                color: var(--color-vertical-hover-tab-btn-hover);
              }
            }
            .btn-disable {
              color: var(--color-vertical-hover-tab-btn-disabled);
            }
            .operationBox {
              border-top: 1px dashed var(--devider-vertical-tab-hover);
            }
          }
        }
        .ivu-card-body {
          padding: 0px !important;
        }

        &.active {
          background: var(--bg-vertical-tab-active);

          .collapse-content-p {
            color: var(--color-vertical-tab-active);
            .btn-defalut {
              color: var(--color-vertical-active-tab-btn);
              &:hover {
                color: var(--color-vertical-active-tab-btn-hover);
              }
            }
            .btn-disable {
              color: var(--color-vertical-active-tab-btn-disabled);
            }
            .operationBox {
              border-top: 1px dashed var(--devider-vertical-tab-active);
            }
            .card-desc {
              .icon-font {
                color: var(--color-vertical-tab-active);
              }
            }
          }
        }

        .collapse-content-p {
          color: var(--color-vertical-tab);
          .card-desc {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 10px;
            .icon-font {
              color: var(--color-vertical-tab-icon);
            }
          }
          .operationBox {
            display: flex;
            padding: 10px;
            border-top: 1px dashed var(--devider-vertical-tab);
          }

          .btn-text {
            height: 22px !important;
            line-height: 22px !important;
          }
          .kpi-text {
            line-height: 22px;
            font-size: 12px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .kpi-btn {
            margin-right: 4px;
          }

          .btn-defalut {
            color: var(--color-vertical-tab-btn);
            &:hover {
              color: var(--color-vertical-hover-tab-btn);
            }
          }
          .btn-white {
            color: #fff;
            &:hover {
              color: var(--color-primary);
            }
          }
          .btn-disable {
            color: var(--color-vertical-tab-btn-disabled);
          }

          .btn-edit-focus {
            color: var(--color-primary);
          }

          .btn-del-focus {
            color: var(--color-btn-primary-disabled);
          }
        }
      }
    }
  }

  .right-content {
    float: right;
    width: calc(~'100% - 300px');
    height: 100%;
    padding: 20px 20px 0 20px;
    background: var(--bg-content);

    .tab {
      padding: 10px;
    }
  }
  .assessment-list {
    .icon-sousuo {
      color: var(--color-primary);
    }
    .ivu-input-wrapper {
      margin-bottom: 20px;
    }
  }
}
</style>
<style lang="less">
.page-indexmanagement {
  .form-content {
    padding: 0 50px;

    .ivu-form-item {
      margin-bottom: 20px;

      .time {
        width: 22%;
      }

      .lag {
        width: 13.5%;
        margin: 0 10px;
      }

      .canshu {
        width: 15%;
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';

export default {
  name: 'programmanagement',
  data() {
    return {
      moduleList: [],
      loading: false,
      activeModuleMessageId: 0,
      activeVersion: 0,
      schemeValue: 0,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '指标名称', key: 'indexName' },
        { title: '设备合格率', key: 'passrate', width: 200 },
        { title: '计算方法', key: 'compmethod', width: 350 },
        { title: '评价标准', key: 'pingjia', width: 350 },
        { title: '操作', slot: 'option' },
      ],
      tableData: [],
      minusTable: 260,
      schemeData: { name: '', pageNumber: 1, pageSize: 20 },
      visible: false,
      formData: {},
      modalAction: {
        title: '编辑信息',
      },
      idx: '',
      moduleShow: false, //控制弹窗出现变量
      moduleAction: {},
      moduleId: {},
      edit_item: {}, //点击编辑存储数据
      total: 0,
      done: false,
      currentScheme: {},
    };
  },
  async created() {
    await this.get_left();
    this.setSelectScheme(this.moduleList);
  },
  provide() {
    return {
      programThis: this,
    };
  },
  components: {
    SrogrammeTable: require('./srogramme-table.vue').default,
    AddCheckModule: require('./add-check-module.vue').default,
    tag: require('./tag').default,
  },
  methods: {
    async infoSearch() {
      await this.get_left();
      this.setSelectScheme(this.moduleList);
    },
    getIconByType(type) {
      switch (type) {
        case '1':
          return 'icon-quanliangshujujiance';
        case '2':
          return 'icon-shangbaokaohejiance';
        case '3':
          return 'icon-linshijiance';
        default:
          'icon-zidingyizhilifangan';
      }
    },
    resetSearchData() {
      this.schemeData.pageSize = 20;
      this.schemeData.pageNumber = 1;
    },
    async handleReachBottom() {
      try {
        this.loading = true;
        if (!(this.schemeData.pageNumber * this.schemeData.pageSize >= this.total)) {
          this.schemeData.pageNumber++;
          let {
            data: { data },
          } = await this.$http.post(governanceevaluation.getProjram, this.schemeData);
          let scheme = this.setSchemeListPageInfo(data.entities);
          this.moduleList.push(...scheme);
        }
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async get_left() {
      this.resetSearchData();
      //左侧列表的接口
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getProjram, this.schemeData);
        let scheme = this.setSchemeListPageInfo(data.entities);
        this.moduleList = scheme;
        this.total = data.total;
      } catch (err) {
        console.log(err);
      }
    },
    setSchemeListPageInfo(data) {
      return data.map((item) => {
        item.pageNumber = data.pageNumber;
        item.pageSize = data.pageSize;
        return item;
      });
    },
    async refreshSchemeCount() {
      let { pageNumber, pageSize } = this.currentScheme;
      let {
        data: { data },
      } = await this.$http.post(governanceevaluation.getProjram, {
        ...this.schemeData,
        pageNumber,
        pageSize,
      });
      this.setSchemeListCount(data.entities);
    },
    setSchemeListCount(scheme) {
      scheme.map((item) => {
        this.moduleList.map((ele) => {
          if (item.id === ele.id) {
            ele.indexCount = item.indexCount;
          }
        });
      });
    },
    setSelectScheme(data = []) {
      if (data.length > 0) {
        this.selectModule(data[0]);
      }
    },
    // 切换左侧导航菜单
    selectModule(item) {
      this.currentScheme = item;
      this.activeModuleMessageId = item.id;
      this.activeVersion = item.version;
      this.schemeValue = item.id;
    },
    // 左侧导航编辑
    showAddModule(action, item) {
      switch (action) {
        case 'add':
          this.moduleAction = {
            title: '新建方案',
            action: 'add',
          };
          break;
        case 'edit':
          this.moduleAction = {
            title: '编辑方案',
            action: 'edit',
          };
          this.edit_item = item;
          break;
      }
      this.moduleShow = true;
    },
    deleteModule(item) {
      this.$UiConfirm({
        content: `您要删除${item.name}，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          let { data } = await this.$http.delete(`${governanceevaluation.deleteScheme}/${item.id}`);
          this.$Message.success(data.msg);
          this.resetSearchData();
          await this.get_left();
          this.setSelectScheme(this.moduleList);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async update(val) {
      this.resetSearchData();
      await this.get_left();
      val && this.selectModule(val.data);
    },
  },
};
</script>
