<template>
  <div class="rightBox line-title">
    <div class="title">
      <p>碰撞结果</p>
      <Icon type="ios-close" @click="handleCancel" />
    </div>
    <div class="box-content">
      <ul class="box-ul">
        <li
          class="box-li"
          v-for="(item, index) in analysisResultList"
          :key="index"
        >
          <div class="box-li-title">
            <div class="square"></div>
            <p class="box-li-text">结果{{ index + 1 }}</p>
          </div>
          <div class="case-content">
            <div class="box-li-people">
              <div class="people-header">
                <Icon type="ios-person" color="#2C86F8" :size="26" />人员
              </div>
              <UiListCard
                type="people"
                :showBar="false"
                :collectIcon="false"
                :data="item"
                @archivesDetailHandle="archivesDetailHandle"
                @on-archive="archivesDetailHandle"
              />
            </div>
            <div class="box-li-childer">
              <div
                v-for="(el, ind) in item.policeAndCase"
                class="box-li-childer-item"
                :key="ind"
              >
                <i
                  class="iconfont range"
                  :class="el.policeDataType | getType('icon')"
                ></i>
                <div class="case_name">
                  {{ el.policeDataType | getType("name") }}
                </div>
                <span
                  class="case_id"
                  :class="{
                    selected: selectedId === getSelectedId(item, el),
                  }"
                  @click="openPositionAreaTheWindow(item, el)"
                  >{{ el.policeDataNo }}</span
                >
                <div class="track_num" @click="openTrackLine(el)">
                  <span>{{ el.trajectoryCount }}</span
                  >条轨迹
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
import UiListCard from "@/components/ui-list-card";

const { mapState } = createNamespacedHelpers("personToCase");

export default {
  name: "",
  components: {
    UiListCard,
  },
  data() {
    return {
      selectedId: "",
    };
  },
  watch: {},
  computed: {
    ...mapState(["analysisResultList"]),
  },
  created() {},
  mounted() {},
  filters: {
    getType(val, attName) {
      const map = {
        1: {
          name: "警情",
          icon: "icon-jingqing",
        },
        2: {
          name: "案件",
          icon: "icon-anjian",
        },
      };
      return map[val][attName];
    },
  },
  methods: {
    openPositionAreaTheWindow(item, el) {
      this.selectedId = this.getSelectedId(item, el);
      this.$emit("openPositionAreaTheWindow", el);
    },
    getSelectedId({ archiveNo }, { policeDataNo, policeDataType }) {
      return `${archiveNo}-${policeDataType}-${policeDataNo}`;
    },
    // 详情
    openTrackLine(el) {
      const params = {
        idCard: el.idCard,
        [el.policeDataType == 1 ? "incidentNo" : "caseNo"]: el.policeDataNo,
      };
      this.$emit("openTrackLine", params);
    },
    handleCancel() {
      this.$emit("cancel");
    },
    archivesDetailHandle(item) {
      let query = {
        archiveNo: item.archiveNo,
        source: "people",
        initialArchiveNo: item.archiveNo,
      };
      const { href } = this.$router.resolve({
        name: "people-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../components/style/index";

.rightBox {
  width: 370px;
  position: absolute;
  right: 10px;
  top: 10px;
  background: #fff;
  height: calc(~"100% - 20px");
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  filter: blur(0px);
  transition: height 0.2s ease-out;

  .box-content {
    height: calc(~"100% - 50px");
    overflow-y: auto;

    .box-ul {
      padding: 15px;

      .box-li {
        background: #f9f9f9;
        border-radius: 0px 0px 0px 0px;
        padding-bottom: 8px;
        margin-bottom: 10px;

        &:hover {
          color: #2c86f8;
        }

        .box-li-title {
          width: 230px;
          height: 28px;
          background: url("~@/assets/img/model/searchIcon/titleBg.png")
            no-repeat;
          display: flex;
          align-items: center;
          padding-left: 10px;

          .square {
            width: 6px;
            height: 6px;
            background: #ffffff;
            margin-right: 10px;
          }

          .box-li-text {
            font-weight: 700;
            font-size: 14px;
            color: #ffffff;
          }
        }

        .case-content {
          padding: 0 15px;

          .box-li-people {
            .people-header {
              height: 36px;
              display: flex;
              align-items: center;
              gap: 8px;
              font-weight: 700;
              font-size: 14px;
              color: #3d3d3d;
              font-family: Microsoft YaHei, Microsoft YaHei;
            }
          }

          .box-li-childer {
            .box-li-childer-item {
              display: flex;
              height: 30px;
              align-items: center;
              gap: 10px;

              .iconfont {
                color: red;
              }

              .case_name {
                font-weight: 700;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.9);
              }

              .case_id {
                flex: 1;
                font-weight: 700;
                font-size: 14px;
                color: #2c86f8;
                cursor: pointer;
                text-decoration-line: underline;
                &.selected {
                  color: #f29f4c;
                }
              }

              .track_num {
                font-weight: 400;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.6);
                cursor: pointer;

                > span {
                  color: #2c86f8;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
