import axios from 'axios';
import router from '../../router';
import store from '../../store';
import { getUUID } from '../../util/module/common';
import { Message } from 'view-design';

let pending = [];
const cancelToken = axios.CancelToken;

const removePending = () => {
  pending.forEach((row) => {
    row.f('请求取消');
  });
  pending = [];
};
// 是否正在刷新的标记
let isRefreshing = false;
// 重试队列，每一项将是一个待执行的函数形式
let requests = [];
function retryOriginalRequest(config) {
  return new Promise((resolve) => {
    requests.push((token) => {
      config.headers['Authorization'] = `Bearer ${token}`;
      config.headers['X-TRACE'] = `Web ${getUUID().replace(/-/g, '')}`;
      resolve(config);
    });
  });
}

//http request 请求拦截器
axios.interceptors.request.use(
  (config) => {
    /**
     * 无感刷新 返回Promise 阻止请求
     */
    return new Promise((resolve) => {
      // // 拦截重复请求(即当前正在进行的相同请求)
      // removePending(config); //在一个ajax发送前执行一下取消操作
      config.cancelToken = new cancelToken((c) => {
        // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式
        pending.push({
          u: config.url + '&' + config.method,
          f: c,
        });
      });
      // 存储cancelToken可以用来阻止访问接口
      config.cancelToken = store.state.common.source.token;
      // 记录日志
      // 这里如果没有传入该接口对应的菜单地址则使用浏览器现在的菜单地址
      if (!config.headers.resourceUrl) {
        config.headers['resourceUrl'] = router.currentRoute.path;
      }
      let token = window.sessionStorage.getItem('token');
      if (
        config.url === '/qsdi-auth-service/oauth/token' ||
        config.url === '/qsdi-auth-service/oauth/token?grant_type=refresh_token'
      ) {
        config.headers['Authorization'] = 'Basic cXNkaTpxc2Rp';
        resolve(config);
      } else if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
        config.headers['X-TRACE'] = `Web ${getUUID().replace(/-/g, '')}`;
      }
      const expirationTimestamp =
        store.state.user.expirationTimestamp || window.sessionStorage.getItem('expirationTimestamp');
      if (
        // 1、判断超时时间，如果超时则请求refresh接口
        expirationTimestamp &&
        expirationTimestamp - new Date().getTime() < 30 * 1000
      ) {
        const refresh_token = store.state.user.refreshToken || window.sessionStorage.getItem('refreshToken');
        //2、isRefreshing === true 时表示正在刷新token 此时将请求保存到requests中
        if (!isRefreshing) {
          isRefreshing = true;
          store
            .dispatch('user/updateToken', refresh_token)
            .then((res) => {
              //3、刷新token后重新请求 即将Promise状态置为 resolve
              requests.forEach((cb) => cb(res.access_token));
              requests = [];
              //4、清空requests
            })
            .catch((err) => {
              console.log(err, 'err');
            })
            .finally(() => {
              isRefreshing = false;
            });
        }
        resolve(retryOriginalRequest(config));
      } else {
        resolve(config);
      }
    });
  },
  (err) => {
    return Promise.reject(err);
  },
);
// 设置超时时间
axios.defaults.timeout = 3000000;
// http response 服务器响应拦截器
axios.interceptors.response.use(
  (response) => {
    // removePending(response.config);  //在一个ajax响应后再执行一下取消操作，把已经完成的请求从pending中移除
    // 获取json没有返回resultCode所以需要判断
    if (response.data.code) {
      switch (response.data.code) {
        //信息填报导入错误以及上传错误不再提示msg而是弹框报错
        case 81599:
        case 81598:
          return Promise.reject(response);
        // 成功
        case 200:
          return response;
        // 成功
        case 417:
          return response;
        default:
          let errorText = response.data.msg || '系统内部错误';
          Message.error(errorText);
          return Promise.reject(response);
      }
    }

    return response;
  },
  (error) => {
    if (error.code === 'ECONNABORTED') {
      //Message.error('请求超时');
      return Promise.reject(error);
    }
    if (error.response) {
      let errorText = error.response.data.msg || '系统内部错误';
      switch (error.response.status) {
        case 401:
          // Message.error(errorText);
          removePending();
          setTimeout(() => {
            window.closeAppWindow();
          }, 500);
          // window.sessionStorage.clear();
          // Message.error(errorText);
          // removePending();
          // console.log("=====退出登录====");
          // setTimeout(() => {
          //   location.reload();
          // }, 500);
          break;
        case 403:
          if (window.sessionStorage.getItem('authorUrl')) {
            window.location.href = `${window.sessionStorage.getItem('authorUrl')}?redirect=${encodeURIComponent(
              window.location.href,
            )}`;
          } else {
            window.location.href = window.location.host + '/404';
          }
          break;
        case 404:
          const url = error.response.config.url;
          const geoJsonErr = url.includes('/json/map-china-config');
          const regionCode = url.split('/').pop().split('.').shift();
          if (geoJsonErr) {
            Message.error({
              content: `所选行政区域编码${regionCode}无实际区域展示`,
              duration: 3,
            });
          } else {
            Message.error(errorText);
          }
          break;
        default:
          Message.error(errorText);
          break;
      }
    }
    return Promise.reject(error);
  },
);

export default axios;
