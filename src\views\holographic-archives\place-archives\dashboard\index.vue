<template>
  <div class="place-archive-dashboard-container">
    <!-- 基本信息 -->
    <div class="base-info-wrapper">
      <BaseInfo :info="baseInfo" />
    </div>
    <!-- 标签和图片 -->
    <div class="label-image-wrapper">
      <LabelCloudView
        :labels="baseInfo.labels || []"
        :type="5"
        :info="baseInfo"
      />
    </div>
    <!-- 关系统计 -->
    <div class="relation-wrapper" v-if="graphObj">
      <ui-module title="关系统计" :padding="0">
        <div
          slot="extra"
          class="mr-20 color-primary cursor-p"
          @click="toRelationGraph"
        >
          关系图谱
        </div>
        <!-- 关系图谱 -->
        <graph
          v-if="hasGraphData"
          @finish="graphLoaded"
          class="right"
          :relation-can-operate="false"
        ></graph>
        <ui-empty v-else></ui-empty>
      </ui-module>
    </div>
    <!-- 场所资源 -->
    <div class="place-resource-wrapper">
      <PlaceResource />
    </div>
    <!-- 数据统计 -->
    <div
      class="data-statistic-wrapper"
      :style="{ 'grid-area': graphObj ? 'e' : 'c' }"
    >
      <DataParse />
    </div>
    <!-- 图上位置 -->
    <div class="location-wrapper">
      <ui-module title="图上位置">
        <MapBase
          ref="mapRef"
          class="map"
          v-if="baseInfo.coord"
          :place-fence="placeFence"
        />
      </ui-module>
    </div>
    <!-- 抓拍 -->
    <div class="capture-wrapper">
      <CaptureBehavior />
    </div>
  </div>
</template>

<script>
import BaseInfo from "./components/base-info.vue";
import PlaceResource from "./components/place-resource.vue";
import DataParse from "./components/data-parse.vue";
import CaptureBehavior from "./components/capture-behavior";
import LabelCloudView from "@/views/holographic-archives/components/label-cloud-view/index";
import MapBase from "@/components/map/index";
import { mapGetters } from "vuex";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
import UiModule from "../components/ui-module.vue";
import { placeFenceType } from "./components/common";

export default {
  name: "PlaceArchiveDashboard",
  mixins: [relativeGraphMixin],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    BaseInfo,
    PlaceResource,
    LabelCloudView,
    MapBase,
    DataParse,
    CaptureBehavior,
    UiModule,
  },
  props: {
    baseInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
    // 场所围栏
    placeFence() {
      if (this.baseInfo.coord) {
        let placeFence = placeFenceType.find(
          (v) => v.code === this.baseInfo.firstLevel
        );
        placeFence.data = [
          {
            properties: { ...this.baseInfo },
            geometry: JSON.parse(this.baseInfo.coord),
          },
        ];
        return placeFence;
      }
    },
  },
  methods: {
    /**
     * @description: 跳转关系图谱
     */
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
  },
};
</script>

<style lang="less" scoped>
.place-archive-dashboard-container {
  width: 100%;
  height: 100%;
  padding: 16px 20px 30px;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: 500px 1fr 500px;
  grid-gap: 20px;
  grid-template-areas:
    "a b c"
    "d b e"
    "f g g";
  & > div {
    // 让每项高度保持一样，不被自动撑开
    min-height: 0;
  }
  .base-info-wrapper {
    grid-area: a;
  }
  .label-image-wrapper {
    grid-area: b;
  }
  .relation-wrapper {
    grid-area: c;
  }
  .place-resource-wrapper {
    grid-area: d;
  }
  .data-statistic-wrapper {
    position: relative;
    grid-area: e;
  }
  .location-wrapper {
    grid-area: f;
  }
  .capture-wrapper {
    position: relative;
    grid-area: g;
  }
}
</style>