<template>
  <div class="card-container" >
    <div class="card-header">
      <i class="icon-font f-18 vt-middle" :class="icon"></i>
      <span class="ml-sm f-16 base-text-color vt-middle"><slot></slot></span>
    </div>
    <div class="card-body" v-ui-loading="{ loading }">
      <div class="venn-wrapper" v-ui-loading="{ tableData }">
        <ui-venn :options="vennOptions" v-show="tableData.length > 0"></ui-venn>
      </div>
      <div class="table-wrapper">
        <table class="f-14">
          <thead>
            <tr>
              <th scope="col" class="f-12">对账对象</th>
              <th scope="col" class="f-12">设备总量</th>
              <th scope="col" class="f-12 only">独有数量</th>
              <th scope="col" class="f-12 diff">差异数量</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <th class="yellow f-12">{{ sourceName }}</th>
              <td>{{ data.targetTotal || 0 | formatNum}}</td>
              <td>
                <span class="color-warning">{{ data.targetOnly || 0 | formatNum }}</span>
              </td>
              <td rowspan="2" class="color-failed diff">{{ data.diff || 0 | formatNum }}</td>
            </tr>
            <tr>
              <th class="green">{{ targetName }}</th>
              <td>{{ data.sourceTotal || 0 }}</td>
              <td>
                <span class="color-success">{{ data.sourceOnly || 0 | formatNum }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="card-footer">
      <div class="latest-time-wrapper">
        <span class="icon-font icon-shijian f-14 mr-xs"></span>
        <span class="time-label">最新时间：</span>
        <span class="time">{{ data.executeTime || '--' }}</span>
      </div>
      <div v-show='tableData.length > 0'>
        <create-tabs
          :componentName="themDataDetail.componentName"
          :importantTabName="themDataDetail.title"
          :tabs-text="themDataDetail.text"
          :tabs-query="{
                batchId: data.batchId,
                id: data.id,
                civilCode: data.civilCode,
                type,
              }"
          class="inline mr-md"
        >
          <ui-btn-tip icon="icon-shebeimingxi" content="明细"></ui-btn-tip>
        </create-tabs>
        <create-tabs
          :componentName="themData.componentName"
          :importantTabName="themData.title"
          :tabs-text="themData.text"
          :tabs-query="{
                id: data.id,
                civilCode: data.civilCode,
                type,
              }"
          class="inline"
        >
          <ui-btn-tip icon="icon-lishijilu-01" content="历史"></ui-btn-tip>
        </create-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import { assetCompareCardOptions } from '@/views/viewassets/assetcompare/modules/vennOptions';

export default {
  name: 'asset-compare-card',
  props: {
    icon: {
      type: String,
    },
    type: {
      type: String,
    },
    loading: {
      type: Boolean,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    /**
     * 对比对象 本级对账 上下级对账 跨网对账
     */
    compareTarget: {
      type: Object,
    },
    sourceName: {},
    targetName: {},
  },
  data() {
    return {
      themData: {
        componentName: 'CurrentLevelCompareHistory', // 需要跳转的组件名
        text: '对账历史', // 跳转页面标题
        title: `${this.compareTarget.label}-对账历史`,
        type: 'view',
      },
      themDataDetail: {
        componentName: 'AssetComparisonResult', // 需要跳转的组件名
        text: '对账明细', // 跳转页面标题
        title: `${this.compareTarget.label}-对账明细`,
        type: 'view',
      },
    };
  },
  computed: {
    vennOptions() {
      return assetCompareCardOptions({
        data: this.data,
        sourceName: this.sourceName,
        targetName: this.targetName,
      });
    },
    tableData() {
      return Object.keys(this.data);
    },
  },
  components: {
    UiVenn: require('@/components/ui-venn.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style scoped lang="less">
@green: #b1b836;
[data-theme='light'],
[data-theme='deepBlue'] {
  .card-container {
    border: 1px solid var(--border-color);
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    .card-header {
      background: rgba(44, 134, 248, 0.05);
      border-bottom: 0;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      .icon-font {}
    }
    .card-footer {
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      .latest-time-wrapper {
        color: rgba(0, 0, 0, 0.8);
        .icon-font {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
}
.font-green {
  color: @green;
}
.card-container {
  position: relative;
  border-radius: 4px;
  height: 100%;
  width: 100%;
  .card-header {
    height: 44px;
    line-height: 44px;
    padding: 0 20px;
    background: var(--bg-collapse-item);
    border-bottom: 1px solid var(--devider-line);
    .icon-font {
      color: var(--color-display-title-before);
      background: var(--color-icon-echarts);
      -webkit-background-clip: text;
      color: #0000;
    }
  }
  .card-body {
    position: relative;
    display: flex;
    padding: 0 20px;
    height: 194px;
    background: var(--bg-tooltip);
    border-bottom: 1px solid var(--devider-line);

    .venn-wrapper {
      height: 200px;
      width: 200px;
      margin-right: 30px;
      @{_deep} .g2-tooltip {
        padding: 0 !important;
        color: var(--color-content) !important;
        background: var(--bg-tooltip) !important;
        box-shadow: none !important;
      }
    }

    .table-wrapper {
      display: flex;
      align-items: center;
      flex: 1;
      table {
        width: 100%;
        text-align: left;
        thead {
          color: var(--color-content);
          .only {
            width: 50px;
          }
          .diff {
            width: 100px;
            padding-left: 30px;
          }
        }
        tbody {
          color: var(--color-content);
          tr:nth-child(1) {
            border-bottom: 1px solid var(--devider-line);
          }
          th {
            position: relative;
            vertical-align: middle;
            &:before {
              position: absolute;
              left: -10px;
              top: 40%;
              content: '';
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: red;
            }
          }
          .yellow {
            &:before {
              background: var(--color-warning);
            }
          }
          .green {
            &:before {
              background: var(--color-success);
            }
          }
        }
        tr {
          height: 40px;
          .diff {
            padding-left: 30px;
          }
        }
        td {
          vertical-align: middle;
        }
      }
    }
  }
  .card-footer {
    color: var(--color-content);
    height: 44px;
    padding: 0 20px;
    background: var(--bg-tooltip);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .latest-time-wrapper {
      color: #a9bed9;
    }

    @{_deep} .icon-shebeimingxi, .icon-lishijilu-01 {
      color: var(--color-primary) !important;
    }
    @{_deep} .icon-lishijilu-01 {
      font-size: 16px !important;
    }
  }
}
</style>
