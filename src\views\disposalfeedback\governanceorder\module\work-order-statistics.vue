<template>
  <div class="work-order-statistics-container auto-fill">
    <div class="search-bar">
      <div class="search">
        <slot name="search"></slot>
      </div>
      <div class="flex-aic">
        <select-table-columns
          :statisticsType="searchData.statisticsType"
          :queryType="commonSearchData.queryType"
          :showColumns="showColumns"
          :backstageData="backstageData"
          @certainChangeColumn="certainChangeColumn"
        ></select-table-columns>
        <Button type="primary" class="ml-md" @click="onExport" :loading="exportLoading">
          <i class="icon-font icon-daochu f-14 mr-xs"></i>导出
        </Button>
      </div>
    </div>
    <div class="auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading || getColumnLoading"
        :show-summary="isShowSummary"
        :full-border="true"
        v-on="$listeners"
      >
      </ui-table>
    </div>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import downLoadTips from '@/mixins/download-tips';
import { ALLORDER, ASSIGN_UNIT } from '../util/enum';
export default {
  mixins: [downLoadTips],
  props: {
    searchData: {},
    tableColumns: {},
    commonSearchData: {},
    statisticsType: {},
    getColumnLoading: {
      default: false,
    },
    showColumns: [],
    backstageData: [],
  },
  data() {
    return {
      loading: false,
      exportLoading: false,
      tableData: [],
      TYPE_ALLORDER: ALLORDER,
      QUERYTYPE_ASSIGN_UNIT: ASSIGN_UNIT, //指派给单位类型
    };
  },
  created() {
    this.initList();
  },
  computed: {
    // 单位统计不展示统计数据
    isShowSummary() {
      //单位统计的id是‘1’
      if (this.statisticsType?.id == '1') {
        return false;
      }
      return true;
    },
  },
  methods: {
    async initList() {
      try {
        this.loading = true;
        let { queryType, orgCode, beginTime, endTime, isOwnOrgCode, type } = this.commonSearchData;
        let params = {
          queryType,
          beginTime,
          endTime,
          queryOrgCode: orgCode === '-1' ? '' : orgCode,
          isOwnOrgCode: type === this.TYPE_ALLORDER && queryType === this.QUERYTYPE_ASSIGN_UNIT ? isOwnOrgCode : '',
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(governancetask.getStatisticsList, params);
        this.tableData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async onExport() {
      try {
        this.exportLoading = true;
        this.$_openDownloadTip();
        let { queryType, orgCode, beginTime, endTime, isOwnOrgCode, type } = this.commonSearchData;
        let params = {
          queryType,
          beginTime,
          endTime,
          queryOrgCode: orgCode === '-1' ? '' : orgCode,
          ...this.searchData,
          isOwnOrgCode: type === this.TYPE_ALLORDER && queryType === this.QUERYTYPE_ASSIGN_UNIT ? isOwnOrgCode : '',
        };
        let res = await this.$http.post(governancetask.statisticsListExport, params, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(res);
      } catch (e) {
        console.log(e);
      } finally {
        this.exportLoading = false;
      }
    },
    certainChangeColumn(val) {
      this.$emit('certainChangeColumn', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SelectTableColumns: require('@/views/disposalfeedback/governanceorder/components/select-table-columns.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .link {
  cursor: pointer;
  text-decoration: underline;
}
@{_deep} .link-warning {
  color: var(--color-warning);
  cursor: pointer;
  text-decoration: underline;
}
@{_deep} .link-error {
  color: var(--color-failed);
  cursor: pointer;
  text-decoration: underline;
}
.work-order-statistics-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;

  .search-bar {
    height: 50px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search {
      display: flex;
      align-items: center;
    }
  }
}

@{_deep} .ui-table {
  .ivu-table-with-summary {
    .ivu-table {
      overflow: hidden;
      > div {
        overflow: auto !important;
      }
      .ivu-table-header {
        overflow: hidden !important;
      }
      div:has(.ivu-table-summary) {
        overflow: hidden !important;
      }
    }

    .ivu-table-summary {
      border-top: none;
      tr {
        td {
          background: var(--bg-table);
        }
      }
    }
  }
  .ivu-table-header {
    tr {
      th {
        box-shadow: none;
        border-bottom: 0 !important;
      }
    }
  }
}
</style>
