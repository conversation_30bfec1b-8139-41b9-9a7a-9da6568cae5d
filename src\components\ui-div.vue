<template>
  <div class="ui-div">
    <div class="div-left-top"></div>
    <div class="div-left-bottom"></div>
    <div class="div-right-top"></div>
    <div class="div-right-bottom"></div>
    <slot></slot>
  </div>
</template>

<style lang="less" scoped>
.ui-div {
  position: relative;
  //border: 1px solid #2e4e65;
}
.div-left-top {
  position: absolute;
  top: 0;
  left: 0;
  width: 10px;
  height: 10px;
  border-top: 1px solid #0068b7;
  border-left: 1px solid #0068b7;
}
.div-left-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 10px;
  height: 10px;
  border-bottom: 1px solid #0068b7;
  border-left: 1px solid #0068b7;
}
.div-right-top {
  position: absolute;
  top: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-top: 1px solid #0068b7;
  border-right: 1px solid #0068b7;
}
.div-right-bottom {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  border-bottom: 1px solid #0068b7;
  border-right: 1px solid #0068b7;
}
</style>

<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {},
  computed: {},
  props: {},
  components: {},
};
</script>
