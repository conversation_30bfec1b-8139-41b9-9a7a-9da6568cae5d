<template>
  <div class="i-layout-menu-side-title">
    <span
      v-if="menu.meta.icon"
      :class="{ 'i-layout-menu-side-title-icon-single': hideTitle }"
      class="i-layout-menu-side-title-icon"
    >
      <i :class="menu.meta.icon" class="router-icon iconfontconfigure"></i>
    </span>
    <span
      v-if="!hideTitle"
      :class="{ 'i-layout-menu-side-title-text-selected': selected }"
      class="i-layout-menu-side-title-text ellipsis"
      :title="menu.meta.title"
    >
      {{ menu.meta.title }}
    </span>
  </div>
</template>
<script>
import tTitle from "../mixins/translate-title";
export default {
  name: `iMenuSideTitle`,
  mixins: [tTitle],
  props: {
    menu: {
      type: Object,
      default() {
        return {};
      },
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
    // 用于侧边栏收起 Dropdown 当前高亮
    selected: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
