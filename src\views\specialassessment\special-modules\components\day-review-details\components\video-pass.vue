<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #phyStatus="{ row }">
        <span>
          {{ !row.phyStatus ? '--' : row.phyStatusText }}
        </span>
      </template>
      <template slot="qualified" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          class="mr-sm play-btn-color"
          icon="icon-bofangshipin"
          content="播放视频"
          :styles="{ color: 'var(--color-primary)' }"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          icon="icon-chakanjietu"
          content="查看截图"
          :styles="{ color: 'var(--color-primary)' }"
          :disabled="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
          @click.native="viewResult(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <video-player
      v-model="videoVisible"
      :video-url="videoUrl"
      :play-device-code="playDeviceCode"
      @onCancel="videoUrl = ''"
    ></video-player>
    <review-and-detail
      v-model="reviewDetailVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="artificialRow"
      :table-data="tableData"
      :review-visible="reviewVisible"
      :qualified-list="qualifiedList"
      :styles="{ width: detailModelWidth }"
      :title="reviewDetailTitle"
    ></review-and-detail>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-pass',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    detailModelWidth: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),

      reviewDetailVisible: false,
      reviewVisible: false,
      reviewDetailTitle: '查看截图',
      reviewDetailStyles: {
        width: '4rem',
      },
      artificialRow: {},
      qualifiedList: [
        { key: '视频质量正常', value: '1' },
        { key: '视频质量异常', value: '2' },
      ],
    };
  },
  methods: {
    //视频播放
    async clickRow(row) {
      try {
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    // 查看截图
    viewResult(row) {
      if (!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot) return;
      this.artificialRow = row;
      this.reviewDetailStyles.width = '6rem';
      this.reviewDetailVisible = true;
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/index.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
