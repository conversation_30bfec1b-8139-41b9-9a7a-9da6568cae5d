<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search type="video" @searchForm="searchForm" :searchText="'高级检索'" />
      <div class="card-content" :loading="loading">
        <div v-for="(item, index) in dataList" :key="index" class="card-item">
          <UiListCard type="vehicle" :showBar="true" :data="item" @archivesDetailHandle="archivesDetailHandle(item)" @collection="init" />
        </div>
        <ui-empty v-if="dataList.length < 1"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <!-- <ui-page :current="pageForm.pageNumber" :total="13" :page-size="pageForm.pageSize"></ui-page> -->
       <!-- 分页 -->
      <ui-page 
        :current="pageInfo.pageNumber" 
        :total="total" 
        :page-size="pageInfo.pageSize" 
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
        >
        </ui-page>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import Search from './components/search.vue'
import UiListCard from '@/components/ui-list-card'
import { queryVehicleList } from '@/api/vehicleArchives'
export default {
  name: 'one-vehicle-one-archives',
  components: { Search, UiListCard },
  props: {},
  data() {
    return {
      loading: false,
      dataList: [],
      pageForm: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
    }
  },
  async created() {
    await this.getDictData()
    this.init()
  },
  methods: {
    ...mapActions({
      getDictData: 'dictionary/getDictAllData'
    }),
    init() {
        this.loading = false;
        var param = Object.assign(this.pageInfo, this.pageForm);
        delete param.bodyTypeobj;
        var labelIds = [];
        if (param.labelIds && param.labelIds.length > 0) {
            param.labelIds.forEach(item => {
                if (item && item != undefined) {
                    labelIds.push(item.id)
                }
            })
            param.labelIds = labelIds;
        }
        queryVehicleList(param).then((res) => {
            this.dataList = res.data.entities
            this.total = res.data.total
        }).catch(() => {})
        .finally(() => {
            this.loading = false
        })
    },
    // 档案详情
    archivesDetailHandle(row) {
      const { href } = this.$router.resolve({
        name: 'vehicle-archive',
        query: { 
          archiveNo: JSON.stringify(row.archiveNo),
          plateNo: JSON.stringify(row.plateNo),
          source: 'car',
          idcardNo: row.idcardNo
        }
      })
      window.open(href, '_blank')
    },
    // 查询
    searchForm(form) {
      this.pageInfo.pageNumber = 1
      this.pageForm = form
      this.init()
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size
      this.init()
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.init()
    },
  }
}
</script>
<style lang="less" scoped>
.person {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
