<template>
  <div class="result-detail auto-fill">
    <div class="info-statics mt-sm">
      <info-statics-list :staticsList="statisticsList"></info-statics-list>
    </div>
    <div class="abnormal-title">
      <div>
        <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
        <span class="f-16 color-filter ml-sm">{{ tableTitle }}</span>
      </div>
      <div class="export">
        <slot class="buttonSlot"></slot>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :loading="loading"
      :table-columns="tableColumns"
      :table-data="tableData"
      ref="table"
    >
      <slot class="tableSlot"></slot>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
  </div>
</template>

<script>
export default {
  name: 'result-detail',
  props: {
    tableTitle: {
      default: '检测结果详情',
    },
    statisticsList: {
      default: () => [],
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  mounted() {},
  methods: {
    changePage(val) {
      this.pageData.pageNum = val;
      this.$emit('changePage', this.pageData);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.$emit('changePage', this.pageData);
    },
  },
  components: {
    InfoStaticsList: require('../components/info-statics-list').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.result-detail {
  padding: 10px;
  height: 100%;
  .info-statics {
    width: 100%;
    height: 102px;
  }
  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 55px;
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
}
</style>
