/**
* 多张照片
 */
<template>
    <div class="swiper-list">
        <swiper ref="mySwiper" :options="swiperOption" class="my-swiper" :id="`swipe${index + 1}`">
            <swiper-slide v-for="(ite, ind) in dataObj.photos" :key="ind">
                <ui-image :src="ite.photoUrl" viewer />
            </swiper-slide>
        </swiper>
        <div class="swiper-pagination" :id="`swipe${index + 1}`" :class="dataObj.photos.length < 2 ? 'my-pagination-hidden' : ''"></div>
    </div>
</template>

<script>
import { swiper, swiperSlide } from 'vue-awesome-swiper';
export default {
    name: '',
    props:{
        dataObj: {
            type: Object,
            default: () => {}
        },
        index: {
            type: Number,
            default: 0
        },
    },
    components:{
        swiper, swiperSlide  
    },
    data () {
        return {
            swiperOption: {
                direction: 'horizontal'
            }
        }
    },
    watch:{
        dataObj: {
            handler(val) {
                let arr = []
                if (val.photos && val.photos.length > 0) {
                    arr = val.photos
                }
                if (val.imageUrls && val.imageUrls.length > 0) {
                    arr = val.imageUrls
                }
                this.swiperOption.pagination = {
                    el: arr.length > 1 ? `#swipe${this.index + 1}` + '.swiper-pagination' : null, //控制分页显示隐藏
                    clickable: true //点击切换
                }
            },
            deep: true,
            immediate: true
        },    
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {}
}
</script>

<style lang='less' scoped>
@import '../style/index';
</style>
