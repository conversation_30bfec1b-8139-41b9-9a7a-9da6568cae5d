<template>
  <div class="otherassets auto-fill">
    <div class="search-box">
      <ui-label class="inline mr-lg mb-sm" label="异常原因">
        <Input class="width-md" v-model="searchData.errorMessage" placeholder="请输入异常原因"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="数据类型">
        <Select class="width-md" v-model="searchData.indexModule" placeholder="请选择数据类型" clearable>
          <Option v-for="(item, index) in dataTypeList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="startSearch">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="over-flow mb-sm">
      <!-- <Checkbox class="checks mr-lg"
                v-model="isCheckAll"
                @on-change="">
        全选</Checkbox> -->
      <div class="btn fr">
        <Button type="primary" class="ml-sm" @click="addKnowledge">
          <i class="icon-font icon-tianjia f-14"></i>
          <span class="vt-middle ml-sm">新建问题</span>
        </Button>
        <Button type="primary" class="ml-sm" :loading="deleteLoading" @click="batchDelete">
          <i class="icon-font icon-piliangshanchu mr-xs f-12"></i>
          <span>批量删除</span>
        </Button>
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        reserveSelection
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template #indexModule="{ row }">
          <span>{{ dataTypeObject[row.indexModule] }}</span>
        </template>
        <template #knowledgeCount="{ row }">
          <span class="font-active-color link" @click="jumpKnowledgesearch(row)">{{ row.knowledgeCount || 0 }}</span>
        </template>
        <template #governmentNameList="{ row }">
          <span
            @click="goToolpage(row.governmentList[index], item)"
            v-for="(item, index) in row.governmentNameList || []"
            :key="index + '-' + item"
            class="font-blue pointer"
            >{{ item + ',' }}</span
          >
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              icon="icon-bianji2"
              content="编辑"
              @click.native="editKnowledge(row)"
            ></ui-btn-tip>

            <ui-btn-tip
              :styles="{ color: '#CF3939 ', 'font-size': '14px' }"
              icon="icon-piliangshanchu"
              content="删除"
              @click.native="singleDelete(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <add-edit
      v-model="addEditShow"
      :modal-action="addEditAction"
      :data-type-list="dataTypeList"
      :default-form="defaultForm"
      @update="startSearch"
    ></add-edit>
  </div>
</template>
<script>
import knowledgebase from '@/config/api/knowledgebase';
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'otherassets',
  props: {
    activeCatalog: {},
  },
  mixins: [dealWatch],
  data() {
    return {
      dataTypeObject: {},
      dataTypeList: [],
      searchData: {
        errorMessage: '',
        indexModule: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      importLoading: false,
      deleteLoading: false,
      loading: false,
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: '数据类型',
          slot: 'indexModule',
          align: 'left',
        },
        {
          title: '异常原因',
          key: 'errorMessage',
          align: 'left',
          tooltip: true,
        },
        {
          title: '问题描述',
          key: 'remark',
          align: 'left',
        },
        {
          title: '关联知识推荐',
          slot: 'knowledgeCount',
          align: 'left',
        },
        {
          title: '治理工具',
          slot: 'governmentNameList',
          align: 'left',
        },
        {
          title: '创建人',
          key: 'creator',
        },
        { title: '创建时间', key: 'createTime' },
        {
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
      checkedData: [],
      // 编辑
      addEditShow: false,
      addEditAction: {
        action: 'add',
        title: '新增问题',
      },
      detailShow: false,
      activeItem: {},
      defaultForm: {},
      defaultStoreData: [],
    };
  },
  async created() {
    this.copySearchDataMx(this.searchData);
    this.getIndexModuleList();
    this.getGovernmentToolsList();
    this.initList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    goToolpage(key) {
      if (!key) return;
      let name = '';
      switch (key) {
        case '10':
          name = 'governanceorder';
          break;
        case '11':
          name = 'governancetoolset';
          break;
        default:
          name = 'governanceautomatic';
      }
      this.$router.push({
        name: name,
        query: name === 'governanceautomatic' ? { tab: key } : {},
      });
    },
    jumpKnowledgesearch({ id }) {
      this.$router.push({
        name: 'knowledgesearch',
        query: { id },
      });
    },
    storeSelectList(selection) {
      this.defaultStoreData = selection;
    },
    async initStatistic() {
      try {
        await this.$http.post(equipmentassets.queryOtherStatistics);
      } catch (err) {
        console.log(err);
      }
    },
    async getGovernmentToolsList() {
      try {
        await this.$http.get(knowledgebase.getGovernmentToolsList);
      } catch (err) {
        console.log(err);
      }
    },
    async getIndexModuleList() {
      try {
        const { data } = await this.$http.get(knowledgebase.getIndexModuleList);
        this.dataTypeObject = data.data;
        this.dataTypeList = Object.keys(data.data).map((key) => {
          return {
            dataKey: key,
            dataValue: data.data[key],
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    async initList() {
      try {
        this.loading = true;
        const { data } = await this.$http.post(knowledgebase.questionPageList, this.searchData);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    startSearch() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initList();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.startSearch);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.initList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.startSearch();
    },
    addKnowledge() {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'add',
        title: '新增问题',
      };
    },
    editKnowledge(row) {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'edit',
        title: '编辑问题',
        row: row,
      };
    },
    viewDetail(row) {
      this.detailShow = true;
      this.activeItem = row;
    },
    async getDevice(row) {
      try {
        const res = await this.$http.get(equipmentassets.viewOtherDevice, {
          params: { id: row.id },
        });
        this.defaultForm = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async exportModule() {
      try {
        const res = await this.$http.get(equipmentassets.downloadOtherTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.data.tip);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.search();
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    batchDelete() {
      let ids = this.defaultStoreData.map((item) => item.id);
      this.$UiConfirm({
        content: `您要删除这${ids.length}项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDevice(ids);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    singleDelete(row) {
      this.$UiConfirm({
        content: '您要删除这个问题，是否确认?',
        title: '警告',
      })
        .then(() => {
          this.deleteDevice([row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteDevice(ids) {
      try {
        const res = await this.$http.post(knowledgebase.removeQuestion, ids);
        this.$Message.success(res.data.msg);
        this.defaultStoreData = [];
        this.startSearch();
      } catch (err) {
        console.log(err);
        // this.$Message.error(err.data.msg)
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getQuestionList: 'knowledgebase/getQuestionList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      phystatusList: 'algorithm/propertySearch_phystatus',
      other_device_type: 'algorithm/other_device_type',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddEdit: require('./add-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-box {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 10px;
}
.otherassets {
  padding: 10px 20px 0 20px;
  background-color: var(--bg-content);
  .link {
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
