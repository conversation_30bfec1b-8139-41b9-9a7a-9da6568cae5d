<template>
  <div class="auroras">
    <div v-for="(item, $index) in 6" :key="$index" :class="'aurora'+$index" class="aurora-line"></div>
  </div>
</template>
<style lang="less" scoped>
  .auroras {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    z-index: 2;
    .aurora-line {
      width: 2px;
      height: 68px;
      background: linear-gradient(180deg, rgba(210, 240, 255, 1), rgba(235, 249, 255, 0));
      position: absolute;
    }
    .aurora0 {
      left: 10%;
      bottom: 150px;
      animation: auroraMove0 1.2s infinite;
    }
    @keyframes auroraMove0 {
      from {
        bottom: 150px;
        opacity: 1;
      }
      to {
        bottom: 300px;
        opacity: 0;
      }
    }
    .aurora1 {
      left: 26%;
      bottom: 100px;
      animation: auroraMove1 1.4s infinite;
    }
    @keyframes auroraMove1 {
      from {
        bottom: 100px;
        opacity: 1;
      }
      to {
        bottom: 200px;
        opacity: 0;
      }
    }
    .aurora2 {
      left: 42%;
      bottom: 200px;
      animation: auroraMove2 1.2s infinite;
    }
    @keyframes auroraMove2 {
      from {
        bottom: 200px;
        opacity: 1;
      }
      to {
        bottom: 260px;
        opacity: 0;
      }
    }
    .aurora3 {
      left: 58%;
      bottom: 220px;
      animation: auroraMove3 1.4s infinite;
    }
    @keyframes auroraMove3 {
      from {
        bottom: 220px;
        opacity: 1;
      }
      to {
        bottom: 300px;
        opacity: 0;
      }
    }
    .aurora4 {
      left: 74%;
      bottom: 120px;
      animation: auroraMove4 1.2s infinite;
    }
    @keyframes auroraMove4 {
      from {
        bottom: 120px;
        opacity: 1;
      }
      to {
        bottom: 220px;
        opacity: 0;
      }
    }
    .aurora5 {
      left: 90%;
      bottom: 220px;
      animation: auroraMove5 1.2s infinite;
    }
    @keyframes auroraMove5 {
      from {
        bottom: 220px;
        opacity: 1;
      }
      to {
        bottom: 330px;
        opacity: 0;
      }
    }
  }
</style>