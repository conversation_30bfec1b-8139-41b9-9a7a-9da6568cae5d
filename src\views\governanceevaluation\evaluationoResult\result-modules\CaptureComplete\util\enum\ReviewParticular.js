import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import global from '@/util/global';
export const iconStaticsList = [
  {
    name: '设备总量',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-renliankakou',
    fileName: 'deviceNum',
  },
  {
    name: '实际检测设备数量',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'actualNum',
  },
  {
    name: '合格设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancebuhegeshebeishu',
    fileName: 'qualifiedNum',
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const imgFormData = [
  {
    type: 'start-end-time',
    label: '抓拍时间',
    startKey: 'minLogTime',
    endKey: 'maxLogTime',
  },
  {
    type: 'select',
    key: 'qualified',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'camera',
    label: '抓拍设备',
    key: 'deviceIds',
  },
  {
    type: 'select',
    key: 'causeErrors',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const FaceTableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',

    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '点位类型',
    key: 'sbdwlxText',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测图片数量',
    key: 'total',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '合格图片数量',
    key: 'qualifiedNum',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '不合格图片数量',
    key: 'unqualifiedNum',
    slot: 'unqualifiedNum',
    tooltip: true,
    width: 200,
  },
  {
    title: '合格率',
    key: 'qualifiedRate',
    slot: 'qualifiedRate',
    tooltip: true,
    width: 120,
  },
  {
    title: '检测结果',
    slot: 'checkStatus',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测时间',
    key: 'startTime',
    tooltip: true,
    width: 200,
  },
  {
    title: '操作',
    slot: 'option',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];
