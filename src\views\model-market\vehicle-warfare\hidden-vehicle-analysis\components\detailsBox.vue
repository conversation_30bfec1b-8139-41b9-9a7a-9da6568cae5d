<!--
    * @FileDescription: 隐匿车分析=>列表
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-12 14:15:37
 -->
<template>
  <div class="detailsBox">
    <div class="details-list">
      <div class="box-hint">
        <Icon type="ios-undo" @click="handleback" />
        <span @click="handleback">隐匿车分析 > 查询结果</span>
      </div>
      <div class="table-box">
        <ul class="vehicle-ul">
          <li
            class="freno-item"
            style="display: flex"
            :class="[{ 'freno-item-hover': currentIndex === index }]"
            v-for="(item, index) in vehicleList"
            :key="index"
            @click.stop="showDetail(item, index)"
          >
            <div class="detail">
              <span class="plate-number vehicle-plate">{{ item.plateNo }}</span>
              <p class="count">
                案发前{{ dayNum }}天过车: &nbsp;&nbsp;<span>{{
                  item.beforeTimesNum
                }}</span>
                次
              </p>
              <p class="count">
                案发后{{ dayNum }}天过车: &nbsp;&nbsp;<span>{{
                  item.afterTimesNum
                }}</span>
                次
              </p>
            </div>
          </li>
        </ul>
        <Page
          size="small"
          :total="pageInfo.total"
          :page-size="pageInfo.pageSize"
          transfer
          show-total
          class="page"
          @on-change="handlePageChange"
        />
        <ui-empty v-if="vehicleList.length == 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getHiddenVehicleList,
  getVehicleCaptureList,
  getVehicleDailyCaptureNum,
} from "@/api/modelMarket";

export default {
  name: "",
  props: {
    dayNum: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      vehicleList: [],
      loading: false,
      detailsParams: {},
      currentIndex: -1,
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleList(param) {
      this.resetAll();
      this.detailsParams = { ...param };
      this.queryList();
    },
    queryList() {
      this.loading = true;
      let param = {
        ...this.detailsParams,
        pageNumber: this.pageInfo.pageNumber,
        pageSize: this.pageInfo.pageSize,
      };
      param.date = this.$dayjs(param.date).format("YYYY-MM-DD HH:mm:ss");
      getHiddenVehicleList(param)
        .then((res) => {
          let list = res.data ? res.data.entities : [];
          this.pageInfo.total = res.data.total;
          this.vehicleList = list;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleback() {
      this.$emit("backSearch");
    },
    resetAll() {
      this.currentIndex = -1;
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      };
      this.vehicleList = [];
    },
    handlePageChange(page) {
      this.pageInfo.pageNumber = page;
      this.queryList();
    },
    showDetail(item, index) {
      this.currentIndex = index;
      this.getDetailsListRequest();
    },
    getDetailsListRequest(type = "before") {
      let isBefore = type == "before" ? true : false;
      let param = {
        ...this.detailsParams,
        plateNo: this.vehicleList[this.currentIndex].plateNo,
        pageNumber: 1,
        pageSize: 9999,
        isBefore,
      };
      param.date = this.$dayjs(param.date).format("YYYY-MM-DD HH:mm:ss");
      getVehicleCaptureList(param).then((res) => {
        this.$emit("showCountDetail", res.data.entities);
        this.$emit("setPlateNo", this.vehicleList[this.currentIndex].plateNo);
      });
      let detailParam = {
        ...this.detailsParams,
        plateNo: this.vehicleList[this.currentIndex].plateNo,
        isBefore,
      };
      detailParam.date = this.$dayjs(detailParam.date).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      getVehicleDailyCaptureNum(detailParam).then((res) => {
        this.$emit("setChartList", res.data);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.detailsBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: 95%;
  .details-list {
    height: 100%;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    filter: blur(0px);
    position: relative;
    .box-hint {
      width: 370px;
      height: 40px;
      background: #2c86f8;
      border-bottom: 1px solid #d3d7de;
      color: #fff;
      font-size: 14px;
      line-height: 40px;
      padding-left: 14px;
      display: flex;
      align-items: center;
      .icon-jiantou {
        transform: rotate(90deg);
        display: inline-block;
        cursor: pointer;
      }
      .ivu-icon-ios-undo {
        font-size: 20px;
        cursor: pointer;
      }
      span {
        font-size: 14px;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
  .table-box {
    height: calc(~"100% - 40px");
    position: relative;
    .vehicle-ul {
      height: calc(~"100% - 30px");
      overflow-y: auto;
    }
    .page {
      text-align: right;
      margin-bottom: 5px;
    }
  }
  .freno-item {
    position: relative;
    list-style: none;
    padding-left: 20px;
    padding-right: 20px;
    height: 72px;
    cursor: pointer;
    display: flex;
    align-items: center;
    .operate {
      i {
        color: #2c86f8;
        margin: 0 3px;
      }
    }
    &:hover {
      background-color: rgba(44, 134, 248, 0.1);
      color: #666666;
      .count {
        color: #666666;
        span {
          color: #2c86f8;
        }
      }
    }
    .count {
      color: #666666;
      span {
        color: #2c86f8;
      }
    }
    .detail {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .plate-number {
      font-size: 14px;
      font-weight: normal;
      display: inline-block;
    }
  }
  .freno-item-hover {
    background-color: #2c86f8;
    color: #fff;
    .count {
      color: #fff;
      span {
        color: #fff;
      }
    }
  }
}
</style>
