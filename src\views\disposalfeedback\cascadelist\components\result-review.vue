<template>
  <ui-modal
    v-model="visible"
    :styles="styles"
    :title="title"
    class="result-review"
    @query="handleSubmit"
    @onCancel="visible = false"
  >
    <div class="result-review">
      <div class="result-review-wrapper">
        <i class="icon-font icon-jinggao"></i>
        <span class="base-text-color ml-xs">{{
          isOppose === OPPOSE ? '上级检测结果和本级检测结果不一致，提出异议' : '您确定要取消复核吗？'
        }}</span>
        <Form ref="form" :model="formData" :rules="rules" class="form-content mt-md" :label-width="100">
          <FormItem label="有异议日期" prop="id">
            <Select v-model="formData.id" placeholder="请选择有异议日期" class="width-input" filterable transfer>
              <Option v-for="(item, index) in data" :key="index" :label="item.checkTimeD" :value="item.id">
                {{ item.checkTimeD }}
              </Option>
            </Select>
          </FormItem>
        </Form>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import deviceCompareMixin from '@/views/disposalfeedback/cascadelist/mixins/deviceCompareMixin.js';
import { mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';

export default {
  name: 'offline-result-compare',
  components: {},
  mixins: [deviceCompareMixin, downLoadTips],

  props: {
    value: {},
    /**
     * 标题
     */
    title: {},
    /**
     * 上级检测结果
     */
    data: {},
    /**
     * 是否复核
     */
    isOppose: {},
    activeItem: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      formData: {
        id: '',
      },
      rules: {
        id: [{ required: true, message: '请选择有异议日期', trigger: 'change', type: 'number' }],
      },
    };
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        this.$refs.form.resetFields();
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  created() {},
  computed: {
    ...mapGetters({
      configData: 'governanceevaluation/configData',
    }),
  },
  methods: {
    async handleSubmit() {
      try {
        let { id } = this.activeItem;
        let res = await this.$refs.form.validate();
        if (!res) return;
        let params = {
          cascadeIssueId: id,
          oppose: this.isOppose,
          ids: [this.formData.id],
        };
        await this.resultReview(params);
        this.visible = false;
        this.$emit('on-success');
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>
<style scoped lang="less">
@{_deep} .ivu-modal-body {
  height: 150px !important;
  overflow: hidden;
}
.result-review {
  display: flex;
  align-items: center;
  justify-content: center;

  .result-review-wrapper {
    .icon-jinggao {
      color: var(--color-warning);
    }
  }
}
</style>
