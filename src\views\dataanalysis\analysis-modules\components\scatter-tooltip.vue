<template>
  <!-- influenceName、subtext、 scatterData: 调用的时候，new xxx() 时会设置到 data -->
  <div class="tooltip-container-view">
    <p class="text ellipsis" :title="this.scatterData.data.deviceNames">
      设备名称：{{ this.scatterData.data.deviceNames }}
    </p>
    <p class="text ellipsis" :title="this.scatterData.data.deviceIds">
      设备编码：{{ this.scatterData.data.deviceIds }}
    </p>
    <p class="text ellipsis">{{ subtext }}：{{ this.scatterData.data.avgShotNum }}</p>
    <p class="text ellipsis">{{ influenceName }}：{{ this.scatterData.data.fieldValue || '' }}</p>
  </div>
</template>

<script>
export default {
  name: 'capture-trend-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.tooltip-container-view {
  .text {
    max-width: 500px;
  }
}
</style>
