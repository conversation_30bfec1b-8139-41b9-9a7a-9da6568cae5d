<template>
  <div class="base-search">
    <Button type="primary" class="exportBtn mr-sm" @click="exportFn"> <em class="icon-font icon-daochu" />导出 </Button>
    <ui-label class="fl" label="抓拍时间" :width="70">
      <div class="date-picker-box">
        <!-- <DatePicker
					class="input-width mb-md"
					type="datetime"
					v-model="searchData.startTime"
					placeholder="请选择开始时间"
					@on-change="
                            (formatTime, timeType) =>
                            changeTimeMx(formatTime, timeType, searchData, 'startTime')"
				></DatePicker>
				<span class="ml-sm mr-sm">--</span>
				<DatePicker
					class="input-width"
					type="datetime"
					v-model="searchData.endTime"
					placeholder="请选择结束时间"
					@on-change="
                            (formatTime, timeType) =>
                            changeTimeMx(formatTime, timeType, searchData, 'endTime')"
				></DatePicker>-->
        <DatePicker
          class="input-width mb-md"
          v-model="searchData.startTime"
          type="datetime"
          placeholder="请选择开始时间"
          confirm
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          v-model="searchData.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          confirm
        ></DatePicker>
      </div>
    </ui-label>
    <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
      <select-camera :cameraType="[1]" @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
    </ui-label>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  props: {
    exportErrTypeList: {
      type: Array,
      default: () => [],
    },
    showExport: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      searchData: {
        deviceIds: [],
        // startTime: new Date(new Date().setDate(new Date().getDate() - 7)),
        // endTime: new Date(),
        startTime: '',
        endTime: '',
        type: '',
        numbers: [],
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.searchData.endTime = this.searchData.endTime ? this.formateDate(this.searchData.endTime) : null;
      this.searchData.startTime = this.searchData.startTime ? this.formateDate(this.searchData.startTime) : null;
      this.$emit('startSearch', this.searchData);
    },
    formateDate(chinaStandard) {
      var date = new Date(chinaStandard);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? '0' + d : d;
      var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      var minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      var s = date.getSeconds();
      let time = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + s;
      return time;
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        type: '',
        numbers: [],
      };
      this.$emit('startSearch', this.searchData);
    },
    /**
     * responseType: 'blob'， 若有乱码问题用这个
     */
    exportFn() {
      var type = this.$store.getters['carAndVideo/exportType'];
      this.searchData.type = type == 0 ? '' : type;
      var url = api.carImportAllInfo;

      if (type == 0) {
        // 数据输出
      } else if (type == 5) {
        // 车辆结构化属性准确检性测优化
        url = api.carImportVehicleStructure;
      } else {
        // 其它4个导出通用
        url = api.carImportExcel;
      }
      this.$http.post(url, this.searchData, { responseType: 'arraybuffer' }).then((res) => {
        if (res.status == 200) {
          let a = document.createElement('a');

          //ArrayBuffer 转为 Blob
          let blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          });

          let objectUrl = URL.createObjectURL(blob);
          a.setAttribute('href', objectUrl);
          var fileName = '导出数据';

          switch (type) {
            case 0:
              fileName = '数据输出';
              break;
            case 1:
              fileName = '图像抓拍时间准确性检测';
              break;
            case 2:
              fileName = '图像上传及时性检测';
              break;
            case 3:
              fileName = '大图URL检测';
              break;
            case 4:
              fileName = '车牌识别准确性检测优化';
              break;
            case 5:
              fileName = '车辆结构化属性准确检性测优化';
              break;
            default:
              break;
          }

          let now = new Date(),
            year = now.getFullYear(),
            mon = now.getMonth() + 1,
            day = now.getDate(),
            hours = now.getHours(),
            min = now.getMinutes(),
            sec = now.getSeconds();
          var dataStr =
            '' +
            year +
            (mon < 10 ? '0' + mon : mon) +
            (day < 10 ? '0' + day : day) +
            '-' +
            (hours < 10 ? '0' + hours : hours) +
            (min < 10 ? '0' + min : min) +
            (sec < 10 ? '0' + sec : sec);

          a.setAttribute('download', '车辆视图数据 - ' + fileName + ' - [iVDG] - [' + dataStr + '].xls');
          a.click();
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
  },
  watch: {
    exportErrTypeList(newVal) {
      var arr = [];
      newVal.forEach((item) => {
        arr.push(parseInt(item));
      });
      this.searchData.numbers = arr;
    },
  },
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  margin: 15px 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
.exportBtn {
  float: right;
  margin-top: 3px;

  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
</style>
