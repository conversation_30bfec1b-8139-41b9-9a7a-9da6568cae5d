<template>
  <ui-card :title="title" class="relationship-information">
    <div class="relationship-infomation-content">
      <div class="relationship-map">
        <!-- 关系图谱 -->
        <graph
          v-if="hasGraphData"
          :relation-can-operate="false"
          :layoutData="{ linkDistance: 200 }"
          @finish="getData"
        />
        <ui-empty v-else></ui-empty>
      </div>
      <!-- 人人同行/ 车辆跟随 -->
      <div class="relationship-list card-border-color">
        <template v-if="allRelativeList.length">
          <section v-for="(item, $index) in allRelativeList" :key="$index">
            <div class="title-color">{{ item.title }}</div>
            <div class="relationship-list-content m-b10">
              <div
                v-for="(one, $index) in item.list"
                :key="$index"
                class="relationship-list-card"
              >
                <div
                  class="relationship-list-card-li card-border-color card-bg"
                >
                  <div class="text-color">
                    {{ item.title }}
                    <span class="primary">{{ one.times }}</span
                    >次
                  </div>
                  <div class="capture-list">
                    <div class="capture-li">
                      <ui-image
                        viewer
                        :type="type"
                        :src="one.myselfPic"
                        :viewerImage="one.myselfBigPic"
                        class="capture-img card-border-color"
                      />
                      <div class="pic-tag-primary-small">
                        {{ one.myselfText }}
                      </div>
                    </div>
                    <div class="capture-li">
                      <ui-image
                        viewer
                        :type="type"
                        :src="
                          ['rcjs', 'rccz'].includes(one.relationType)
                            ? one.otherBigPic
                            : one.otherPic
                        "
                        :viewerImage="one.otherBigPic"
                        class="capture-img card-border-color"
                      />
                      <div class="pic-tag-warning-small">{{ item.title }}</div>
                      <div class="pic-title" :title="one.otherText">
                        <p class="pic-title-p">{{ one.otherText }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </template>
        <ui-loading v-if="loading" />
        <ui-empty v-if="!allRelativeList.length && !loading" />
        <span class="more-tips" v-else @click="viewMore">更多>></span>
      </div>
    </div>
  </ui-card>
</template>
<script>
import CollapseExpand from "@/components/relation-graph/collapse-expand";
import peopleImg from "@/assets/img/people1.webp";
import vehicleImg from "@/assets/img/car1.webp";
import relativeGraphMixin2 from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
import relativeDetailMixin from "@/views/holographic-archives/mixins/relativeDetailMixin.js";

export default {
  mixins: [relativeGraphMixin2, relativeDetailMixin],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    CollapseExpand,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    // 人人同行/ 车辆跟随
    list1: {
      type: Array,
      default: () => [],
    },
    loading1: {
      type: Boolean,
      default: false,
    },
    // 乘车同行/ 驾乘人员
    list2: {
      type: Array,
      default: () => [],
    },
    loading2: {
      type: Boolean,
      default: false,
    },
    baseInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // relativeDetailMixin 这个mixin会修改这个值
      allRelativeList: [],
      vehicleImgUrl: require("@/assets/img/default-img/vehicle_default.png"),
      // 关系图谱配置信息
      relationshipOptions: {
        disableDragNode: true,
        allowShowMiniToolBar: false,
        disableZoom: true,
        defaultExpandHolderPosition: "hide",
        checkSelect: false,
        fontSize: 12,
        layouts: [
          {
            label: "中心",
            layoutName: "center",
            layoutClassName: "seeks-layout-center",
            // 节点间距
            distance_coefficient: 0.5,
          },
        ],
      },
      atlasList: [
        {
          id: 1,
          text: "苏A 29999",
          img: this.type === "people" ? peopleImg : vehicleImg,
          children: [
            {
              id: 2,
              num: 2,
              text: "同抓拍",
            },
            {
              id: 3,
              num: 2,
              text: "同涉案",
            },
          ],
        },
      ],
      loading: false,
    };
  },
  methods: {
    viewMore() {
      this.MixinToNumCube();
    },
    getData(data) {
      this.graphLoaded(data);
      this.handleDataByRelationMap(data);
    },
  },
};
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}

.relationship-information {
  /deep/ .card-content {
    padding: 10px 15px 20px 20px !important;
    display: flex;
  }
}

/deep/ .cover {
  .icon {
    width: 110px;
    height: 110px;
  }

  .text {
    font-size: 14px;
  }
}

.relationship-infomation-content {
  width: 100%;
  height: 395px;
  display: flex;

  .relationship-map {
    position: relative;
    width: 47%;
    height: 100%;
  }

  .relationship-list {
    position: relative;
    width: 53%;
    max-height: 400px;
    border-left: 1px solid #fff;
    padding-left: 20px;
    overflow-y: auto;
    .title-color {
      font-family: "MicrosoftYaHei-Bold";
      font-weight: bold;
      font-size: 14px;
      line-height: 20px;
    }
    .more-tips {
      position: absolute;
      top: 0;
      right: 10px;
      color: rgba(0, 0, 0, 0.55);
      cursor: pointer;
      z-index: 20;
    }
    .relationship-list-content {
      display: flex;
      flex-wrap: wrap;
      margin-left: -5px;
      position: relative;

      .relationship-list-card {
        width: 33.33%;
        height: 150px;
        padding: 0 5px;
        margin-top: 10px;

        .relationship-list-card-li {
          padding: 10px;
          border: 1px solid #fff;
          height: 100%;
          border-radius: 4px;

          .text-color {
            font-size: 14px;
            line-height: 20px;

            .primary {
              font-weight: bold;
              margin: 0 2px;
            }
          }

          .capture-list {
            display: flex;
            margin-top: 10px;

            .capture-li {
              position: relative;
              margin-left: 10px;
              height: 100px;

              .capture-img {
                width: 100px;
                height: 100px;
                object-fit: contain;
                border: 1px solid #fff;
                box-sizing: border-box;
              }
              .pic-title-p {
                padding: 0 0 0 5px;
                width: 100px;
                /*超出隐藏*/
                overflow: hidden;
                /*文本超出显示为省略号*/
                text-overflow: ellipsis;
                /*文本不换行*/
                white-space: nowrap;
              }
            }

            .capture-li:first-child {
              margin: 0;
            }
          }
        }
      }
    }
  }
}
</style>
