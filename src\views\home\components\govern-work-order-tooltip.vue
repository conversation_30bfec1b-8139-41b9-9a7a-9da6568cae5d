<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box">
      <p>{{ toolTipData[0].axisValue }}</p>
    </div>
    <div v-for="(item, index) in toolTipData" :key="index" class="tooltip-content-box">
      <p class="legend-box">
        <span
          :class="{ block: item.seriesType === 'bar', line: item.seriesType === 'line' }"
          :style="{ 'background-color': item.data.color }"
        >
        </span>
        <span> {{ item.seriesName }}： </span>
        <span :style="{ color: item.data.color }" class="font-num">
          {{ item.value }}
          <span v-if="item.seriesType === 'line'">%</span>
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #ffffff;
    .font-vs {
      font-weight: 600;
      color: #1b86ff;
      margin: 0 15px;
    }
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .block {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 10px;
  }
  .line {
    display: inline-block;
    height: 2px;
    width: 6px;
    margin-right: 10px;
  }
  .font-num {
    font-weight: 600;
  }
}
</style>
