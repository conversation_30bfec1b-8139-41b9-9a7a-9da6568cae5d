<template>
  <div class="area-container">
    <div class="tree-title base-text-color">
      <div class="width-md">行政区划名称</div>
      <div class="d_flex">
        <slot name="configName">
          <span class="width-md">去年同期抓拍数量</span>
        </slot>
        <div class="width-mini">是否考核</div>
        <div class="width-mini">全部</div>
      </div>
    </div>
    <div class="tree-wrapper">
      <ui-search-tree
        ref="uiTree"
        class="ui-search-tree"
        no-search
        :highlight-current="false"
        node-key="nodeKey"
        :tree-data="areaTreeData"
        :default-props="defaultProps"
        expandAll
        checkStrictly
      >
        <template #label="{ node, data }">
          <div class="flex">
            <div class="vt-middle left-item">{{ data.regionName }}</div>
            <div class="inline right-item">
              <slot name="label" :node="node" :data="data">
                <div class="width-md">
                  <Button type="text" class="mr-xs" @click="configArea(data)">{{
                    isConfig(data) ? '已配置' : '配置'
                  }}</Button>
                  <Button type="text" class="mr-xs" @click="copy(data)">复制</Button>
                  <Button type="text" class="mr-xs" @click="paste(data)">粘贴</Button>
                </div>
              </slot>
              <div class="vt-middle width-mini">
                <i-switch v-model="data.check" size="small" @on-change="check($event, node, data)"></i-switch>
              </div>
              <Checkbox
                class="vt-middle width-mini"
                v-model="data.checkAll"
                @on-change="checkAll($event, node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
                >{{ `${data.checkAll ? '取消' : '全部'} ` }}</Checkbox
              >
            </div>
          </div>
        </template>
      </ui-search-tree>
      <loading v-if="loading"></loading>
    </div>
  </div>
</template>
<script>
export default {
  name: 'capture-area-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'regionName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'regionCode',
    },
    data: {
      default: () => {
        return [];
      },
    },
    importData: {}, //重点必报采集区域达标数量
    otherData: {}, //其他必报采集区域达标数量
    allData: {}, //其他必报采集区域达标数量
    areaConfigList: {},
    copyFun: {},
    pasteFun: {},
    configFun: {},
    //是否已配置
    isConfig: {},
  },
  data() {
    return {
      configAreaNumVisible: false,
      visible: false,
      loading: false,
      checkedTreeData: [], //返回选中的组织机构
      checkedTree: [], //返回选中的数据
      areaTreeData: [], //行政区划树
      importantCjqyList: [], //传入的重点采集区域
      otherCjqyList: [], //传入的其他采集区域
      configList: [
        /*{
          civilCode: '000000',
          cjqyQuantityList:[]
        },
        {
          civilCode: '33333',
          cjqyQuantityList:[]
        }*/
      ],
      copyRegionCode: '',
      allAreaData: [],
      configCjqyList: [],
      captureConfigList: [],
    };
  },
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.areaTreeData = val || [];
      },
    },
  },
  methods: {
    commit() {
      this.checkedTree = [];
      this.getCheckedKeys(this.areaTreeData);
      return this.checkedTree;
    },
    configArea(item) {
      this.configFun(item);
    },
    copy(item) {
      this.copyFun(item);
    },
    paste(item) {
      this.pasteFun(item);
    },
    query() {
      this.checkedTreeData = [];
      this.$emit('query', this.checkedTreeData);
      this.visible = false;
    },
    checkAll(val, node, data) {
      this.$set(node.data, 'check', data.checkAll);
      if (node.childNodes) {
        if (data.checkAll) {
          this.checkParent(node, data.checkAll);
        } else {
          this.checkChildren([node], data.checkAll);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    getCheckRegion() {
      this.getCheckedKeys(this.areaTreeData);
      return this.checkedTreeData || [];
    },
    getCheckedKeys(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push(item.regionCode);
          this.checkedTree.push(item);
        }
        if (item.children) {
          this.getCheckedKeys(item.children);
        }
      });
    },
    check(val, node) {
      this.checkParent(node, val);
      this.checkChildren([node], val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
    checkChildren(node, check) {
      node &&
        node.map((item) => {
          this.$set(item.data, 'checkAll', false);
          this.$set(item.data, 'check', check);
          if (item.childNodes) {
            this.checkChildren(item.childNodes);
          }
        });
    },
  },
};
</script>

<style lang="less" scoped>
.area-container {
  border: 1px solid var(--border-modal-footer);
  .tree-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    line-height: 48px;
    padding: 0 15px;
    background: var(--bg-table-header-th);
  }

  .tree-wrapper {
    height: 500px;
    overflow: auto;
    .ui-search-tree {
      height: 100%;
    }
    @{_deep} .el-tree {
      .el-tree-node {
        .el-tree-node__content {
          height: 40px;
          &.has-child-panel {
            margin-bottom: 5px;
          }
          .custom-tree-node {
            line-height: 34px;
          }
          .el-tree-node__expand-icon {
            margin-left: 20px;
            font-size: 16px;
            margin-top: -2px;
          }
        }
      }
    }
  }
}

.flex {
  display: flex;
  justify-content: space-between;
}
.right-item {
  display: flex;
  align-items: center;
}
.is-all,
.is-checked,
.switch-width,
.check-width {
  width: 100px;
}
</style>
