<template>
  <common-form v-bind="$props" ref="formData">
    <template #indexDefinition>
      <div class="color-failed">
        <p>备注：结果最大等于1。</p>
      </div>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'basic-full-dir',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {};
  },
  methods: {
    async validate() {
      try {
        return await this.$refs.formData.validate();
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less"></style>
