import request from "@/libs/request";
import { management, holographicArchives } from "./Microservice";

// 分页标签列表
export function getLabelList(data) {
  return request({
    url: management + "/label/page",
    method: "post",
    data,
  });
}

// 分页标签列表
export function getLabelAllList(data) {
  return request({
    url: management + "/label/list",
    method: "post",
    data,
  });
}

// 新增标签
export function labelUpdateOrAdd(data) {
  const { id } = data;
  return request({
    url: management + "/label" + (id ? "/id/" + id : ""),
    method: id ? "put" : "post",
    data,
  });
}

// 新增标签
export function getLabelInfo(id) {
  return request({
    url: management + "/label/id/" + id,
    method: "get",
  });
}

// 标签删除 批量删除
export function labelDelete(ids) {
  return request({
    url: management + "/label/ids/" + ids,
    method: "delete",
  });
}

// 批量查询所有标签组
export function getLabelGroupAllList() {
  return request({
    url: management + "/label/group",
    method: "get",
  });
}

// 标签组列表
export function getLabelGroupList(data) {
  return request({
    url: management + "/label/group/page",
    method: "post",
    data,
  });
}

// 标签组列表
export function labelGroupDelete(ids) {
  return request({
    url: management + "/label/group/ids/" + ids,
    method: "delete",
  });
}

// 新增标签
export function labelGroupUpdateOrAdd(data) {
  const { id } = data;
  return request({
    url: management + "/label/group" + (id ? "/id/" + id : ""),
    method: id ? "put" : "post",
    data,
  });
}

// 分页查询标签分析的对象
export function getLabelObjectList(data) {
  return request({
    url: management + "/label/object/pageObjectList",
    method: "post",
    data,
  });
}

// 分页查询标签分析的对象
export function getLabelObjectStatistics(data) {
  return request({
    url: management + "/label/object/statistics?onlyReturn=byDays",
    method: "post",
    data,
  });
}

// 分页查询标签分析的对象
export function deleteLabelObject(ids) {
  return request({
    url: management + "/label/object/ids/" + ids,
    method: "delete",
  });
}

// 分页查询标签关联的对象
export function getObjListByLabel(data) {
  const { id } = data;
  return request({
    url: management + "/label/id/" + id + "/object/page",
    method: "post",
    data,
  });
}
// 分页查询标签关联的对象
export function getObjListpage(data) {
  return request({
    url: management + "/label/detail/page",
    method: "post",
    data,
  });
}

// 标签关联对象
export function labelConnectObj(data) {
  const { codes, id } = data;
  return request({
    url: management + "/label/id/" + id + "/object/codes/" + codes,
    method: "put",
    data,
  });
}
// 对象关联标签
export function objConnectLabel(data) {
  const { code, ids } = data;
  return request({
    url: management + "/label/object/code/" + code + "/ids/" + ids,
    method: "put",
    data,
  });
}

// 标签统计
export function getLabelStatistics(data) {
  return request({
    url: management + "/label/statisticsByLabelType",
    method: "post",
    data,
  });
}

// 标签日志
export function getLabelLog(data) {
  const { id } = data;
  return request({
    url: management + `/label/id/${id}/log`,
    method: "post",
    data,
  });
}
// 标签对象日志
export function getLabelObjLog(data) {
  return request({
    url: management + `/label/object/pageObjectLog`,
    method: "post",
    data,
  });
}
// 删除关联标签分析
export function deleteRelationLabel(code, ids) {
  return request({
    url: management + `/label/device/code/${code}/label/ids/${ids}`,
    method: "delete",
  });
}
// 收藏列表
export function getCollectionList(data) {
  return request({
    url: management + "/label/collection/pageList",
    method: "post",
    data,
  });
}
// 用户收藏新增
export function addCollection(data) {
  return request({
    url: management + "/label/collection/add",
    method: "post",
    data,
  });
}
// 用户收藏取消
export function removeCollection(id) {
  return request({
    url: management + "/label/collection/remove/" + id,
    method: "delete",
  });
}
// 查询设备
export function isCollection(data) {
  return request({
    url: management + "/label/collection/isCollection",
    method: "post",
    data,
  });
}

// 全息档案查询标签接口
export function queryPageList(data) {
  return request({
    url: holographicArchives + "/labels/queryPageList",
    method: "post",
    data,
  });
}

// 查询标签信息详情列表信息
export function queryLabelsByIds(data) {
  return request({
    url: holographicArchives + "/labels/queryLabelsByIds",
    method: "post",
    data,
  });
}

// 一人一档-视频档案编辑标签
export function motifyVidArchiveLableIds(data) {
  return request({
    url: holographicArchives + "/videoPerson/motifyVidArchiveLableIds",
    method: "post",
    data,
  });
}

// 设备资料--编辑设备标签
export function motifyDeviceLableIds(data) {
  return request({
    url: holographicArchives + "/device/motifyDeviceLableIds",
    method: "post",
    data,
  });
}

// 实名档案-删除标签
export function personDelLabel(data) {
  return request({
    url: holographicArchives + "/person/deleteRealNameLableIds",
    method: "post",
    data,
  });
}

// 视频档案-删除标签
export function videoDelLabel(data) {
  return request({
    url: holographicArchives + "/videoPerson/deleteVidArchiveLableIds",
    method: "post",
    data,
  });
}
// 车辆档案-删除标签
export function vehicleDelLabel(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/deleteVehicleLableIds",
    method: "post",
    data,
  });
}
// 一机一档档案-删除标签
export function deviceDelLabel(data) {
  return request({
    url: holographicArchives + "/device/deleteDeviceLableIds",
    method: "post",
    data,
  });
}

// #region 场所档案
// 标签编辑
export function modifyPlaceArchiveLabelsAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/update/tags`,
    method: "post",
    data,
  });
}
// 删除标签
export function placeDelLabelAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/remove/tags`,
    method: "post",
    data,
  });
}
//#endregion
