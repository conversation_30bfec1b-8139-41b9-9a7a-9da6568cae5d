const vehicleBrands = {
  '5' : {
    name: '大众',
    url: require('@/assets/img/vehicle-brand/dazhong.png')
  },
  '6' : {
    name: '别克',
    url: require('@/assets/img/vehicle-brand/dazhong.png')
  },
  '36' : {
    name: '宝马',
    url: require('@/assets/img/vehicle-brand/baoma.png')
  },
  '2' : {
    name: '本田',
    url: require('@/assets/img/vehicle-brand/bentian.png')
  },
  '7' : {
    name: '标致',
    url: require('@/assets/img/vehicle-brand/biaozhi.png')
  },
  '1' : {
    name: '丰田',
    url: require('@/assets/img/vehicle-brand/fengtian.png')
  },
  '8' : {
    name: '福特',
    url: require('@/assets/img/vehicle-brand/fute.png')
  },
  '9' : {
    name: '日产',
    url: require('@/assets/img/vehicle-brand/richan.png')
  },
  '11' : {
    name: '奥迪',
    url: require('@/assets/img/vehicle-brand/aodi.png')
  },
  '19' : {
    name: '马自达',
    url: require('@/assets/img/vehicle-brand/mazida.png')
  },
  '12' : {
    name: '雪佛兰',
    url: require('@/assets/img/vehicle-brand/xuefulan.png')
  },
  '10' : {
    name: '雪铁龙',
    url: require('@/assets/img/vehicle-brand/xuetelong.png')
  },
  '3' : {
    name: '现代',
    url: require('@/assets/img/vehicle-brand/xiandai.png')
  },
  '25' : {
    name: '奇瑞',
    url: require('@/assets/img/vehicle-brand/qirui.png')
  },
  '13' : {
    name: '起亚',
    url: require('@/assets/img/vehicle-brand/qiya.png')
  },
  '80' : {
    name: '荣威',
    url: require('@/assets/img/vehicle-brand/rongwei.png')
  },
  '20' : {
    name: '三菱',
    url: require('@/assets/img/vehicle-brand/sanling.png')
  },
  '38' : {
    name: '斯柯达',
    url: require('@/assets/img/vehicle-brand/sikeda.png')
  },
  '31' : {
    name: '吉利',
    url: require('@/assets/img/vehicle-brand/jili.png')
  },
  '37' : {
    name: '中华',
    url: require('@/assets/img/vehicle-brand/zhonghua.png')
  },
  '48' : {
    name: '沃尔沃',
    url: require('@/assets/img/vehicle-brand/woerwo.png')
  },
  '45' : {
    name: '雷克萨斯',
    url: require('@/assets/img/vehicle-brand/leikesasi.png')
  },
  '82' : {
    name: '菲亚特',
    url: require('@/assets/img/vehicle-brand/feiyate.png')
  },
  '24' : {
    name: '吉利帝豪',
    url: require('@/assets/img/vehicle-brand/jili.png')
  },
  '22' : {
    name: '东风',
    url: require('@/assets/img/vehicle-brand/dongfeng.png')
  },
  '23' : {
    name: '比亚迪',
    url: require('@/assets/img/vehicle-brand/biyadi.png')
  },
  '27' : {
    name: '铃木',
    url: require('@/assets/img/vehicle-brand/lingmu.png')
  },
  '18' : {
    name: '金杯',
    url: require('@/assets/img/vehicle-brand/jinbei.png')
  },
  '77' : {
    name: '海马',
    url: require('@/assets/img/vehicle-brand/haima.png')
  },
  '224' : {
    name: '五菱',
    url: require('@/assets/img/vehicle-brand/wuling.png')
  },
  '49' : {
    name: '江淮',
    url: require('@/assets/img/vehicle-brand/jianghuai.png')
  },
  '46' : {
    name: '斯巴鲁',
    url: require('@/assets/img/vehicle-brand/sibalu.png')
  },
  '33' : {
    name: '英伦',
    url: ''
  },
  '244' : {
    name: '长城',
    url: require('@/assets/img/vehicle-brand/changcheng.png')
  },
  '30' : {
    name: '哈飞',
    url: require('@/assets/img/vehicle-brand/hafei.png')
  },
  '73' : {
    name: '庆铃(五十铃)',
    url: require('@/assets/img/vehicle-brand/qinglingwushiling.png')
  },
  '17' : {
    name: '东南',
    url: require('@/assets/img/vehicle-brand/dongnan.png')
  },
  '15' : {
    name: '长安',
    url: require('@/assets/img/vehicle-brand/changan.png')
  },
  '43' : {
    name: '福田',
    url: require('@/assets/img/vehicle-brand/futian.png')
  },
  '21' : {
    name: '夏利',
    url: ''
  },
  '32' : {
    name: '奔驰',
    url: require('@/assets/img/vehicle-brand/benchi.png')
  },
  '4' : {
    name: '一汽',
    url: require('@/assets/img/vehicle-brand/yiqi.png')
  },
  '60' : {
    name: '依维柯',
    url: require('@/assets/img/vehicle-brand/yiweike.png')
  },
  '42' : {
    name: '力帆',
    url: require('@/assets/img/vehicle-brand/lifan.png')
  },
  '47' : {
    name: '雷诺',
    url: require('@/assets/img/vehicle-brand/leiruo.png')
  },
  '53' : {
    name: 'MG名爵',
    url: require('@/assets/img/vehicle-brand/mgmingjue.png')
  },
  '87' : {
    name: '凯马',
    url: ''
  }
}
export default vehicleBrands