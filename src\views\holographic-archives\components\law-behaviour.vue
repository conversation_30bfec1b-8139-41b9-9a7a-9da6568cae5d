<template>
  <ui-card title="行为规律" class="m-b20">
    <div slot="extra" class="btn-group">
      <Button
        v-for="(item, index) in timeList"
        :key="index"
        :type="item.value === daysNum ? 'primary' : 'default'"
        size="small"
        @click="switchDayHandle(item.value)"
        >{{ item.label }}
      </Button>
    </div>
    <div class="behaviour-content">
      <div class="bar-content">
        <div class="bar-item bar-day">
          <span class="num">{{ day }}</span>
          <p class="bar" :style="{ height: day + 'px' }"></p>
          <span>白天</span>
        </div>
        <div class="bar-item bar-night">
          <span class="num">{{ night }}</span>
          <p class="bar" :style="{ height: night + 'px' }"></p>
          <span>晚上</span>
        </div>
      </div>
      <PolarBarEchart :title="{}" class="polar-bar" :series="timeSlotSeries" />
      <ui-loading v-if="timeSlotLoading" />
    </div>
  </ui-card>
</template>
<script>
import PolarBarEchart from "@/components/echarts/polar-bar-echart";
import { behavioralRulesStatics } from "@/api/realNameFile";
import { behavioralRulesStaticsVehicle } from "@/api/vehicleArchives";
export default {
  components: { PolarBarEchart },
  props: {
    // 卡片标题
    title: {
      type: String,
      default: "",
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    //1实名档案/2路人档案/3车辆档案
    dataType: {
      type: Number,
      default: 1,
    },
    // 档案id
    archiveNo: {
      type: String,
      default: "",
    },
    // 档案id
    plateNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      timeSlotLoading: false,
      day: "", //白天
      night: "", //晚上
      daysNum: 4,
      timeList: [
        { value: 4, label: "一天" },
        { value: 1, label: "一周" },
        { value: 2, label: "一月" },
      ],
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
    };
  },
  mounted() {
    if (this.dataType == 3) {
      this.vehicle();
    } else {
      this.behavioralRulesStatics();
    }
  },
  methods: {
    // 切换时间
    switchDayHandle(daysNum) {
      this.daysNum = daysNum;
      if (this.dataType == 3) {
        this.vehicle();
      } else {
        this.behavioralRulesStatics();
      }
    },
    // 行为规律-时间段
    behavioralRulesStatics() {
      this.timeSlotLoading = true;
      behavioralRulesStatics({
        archiveNo: this.archiveNo,
        plateNo: this.plateNo,
        dataType: this.dataType,
        type: this.daysNum,
      })
        .then((res) => {
          const { day, night, daytimeRange, x, y } = res.data;
          this.day = day;
          this.night = night;
          this.timeSlotSeries[0].data = [];
          let dayStart = daytimeRange.split("-")[0];
          let dayEnd = daytimeRange.split("-")[1];
          let timeList = x;
          let dataList = y;
          dataList.forEach((v, i) => {
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i}点`,
                value: v,
                itemStyle: {
                  color: "#F29F4C",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
    },
    // 行为规律-时间段
    vehicle() {
      this.timeSlotLoading = true;
      behavioralRulesStaticsVehicle({
        archiveNo: this.archiveNo,
        plateNo: this.plateNo,
        dataType: this.dataType,
        type: this.daysNum,
      })
        .then((res) => {
          const { day, night, daytimeRange, x, y } = res.data;
          this.day = day;
          this.night = night;
          this.timeSlotSeries[0].data = [];
          let dayStart = daytimeRange.split("-")[0];
          let dayEnd = daytimeRange.split("-")[1];
          let timeList = x;
          let dataList = y;
          dataList.forEach((v, i) => {
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i}点`,
                value: v,
                itemStyle: {
                  color: "#EC9240",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.btn-group {
  padding-top: 10px;
  padding-right: 20px;
  button + button {
    margin-left: 10px;
  }
  .ivu-btn-small {
    border-color: #d3d7de;
    height: 24px;
    padding: 0 10px;
    color: rgba(0, 0, 0, 0.6);
    border-radius: 2px;
    &.ivu-btn-primary {
      color: #fff;
      border-color: #4597ff;
    }
  }
}
.behaviour-content {
  height: 100%;
  display: flex;
  align-items: flex-end;
  font-size: 16px;
  .bar-content {
    width: 320px;
    display: flex;
    justify-content: space-around;
    height: 100%;
    padding: 20px 30px;
    .bar-item {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      span {
        color: rgba(0, 0, 0, 0.6);
        margin-top: 6px;
      }
      .num {
        color: rgba(0, 0, 0, 0.9);
      }
      .bar {
        width: 16px;
        background: linear-gradient(180deg, #5bc0ff 0%, #2c86f8 100%);
        border-radius: 8px;
      }
      &.bar-night {
        .bar {
          background: linear-gradient(207deg, #f7b93d 0%, #ec9240 100%);
        }
      }
    }
  }
}
</style>
