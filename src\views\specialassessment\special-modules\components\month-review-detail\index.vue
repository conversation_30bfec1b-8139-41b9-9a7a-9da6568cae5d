<template>
  <component v-if="!!componentName" :is="componentName" v-bind="getAttrs()" v-on="$listeners" @sortChange="sortChange">
    <Button slot="export" type="default" class="button-export" @click="getExport" :loading="exportLoading">
      <i class="icon-font icon-daochu font-white mr-xs f-14"></i>
      <span class="ml-xs">导出</span>
    </Button>
  </component>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { menuConfig } from '@/views/specialassessment/utils/menuConfig.js';
export default {
  name: 'month-review-detail',
  mixins: [downLoadTips],
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: null,
      loading: false,
      exportLoading: false,
      sortData: {},
    };
  },
  created() {
    const queryParams = this.$route.query;
    const menuItem = menuConfig.find((item) => item.type === queryParams.indexType);
    this.componentName = menuItem.monthDetailComponent;
  },
  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let { examineTime, dateType, indexType, statisticsCode } = this.$route.query;
        let { dataDimensionEnum, detail, code } = this.detailData;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType,
          dataDimensionEnum: dataDimensionEnum,
          nodeEnum: indexType,
          paramForm: {
            orgRegionCode: statisticsCode === '3' ? null : code,
            indexId: detail[0].indexId,
            tagIds: statisticsCode === '3' && code !== '-1' ? [Number(code)] : null,
          },
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (this.sortData.sortField) {
          params.paramForm.sortField = this.sortData.sortField;
          params.paramForm.sort = this.sortData.sort;
        }
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportSpecialDetailByMonth, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (e) {
        console.log(e);
      } finally {
        this.exportLoading = false;
      }
    },
    getAttrs() {
      return {
        indexData: this.indexData,
        detailData: this.detailData,
        ...this.$attrs,
      };
    },
    sortChange(data) {
      this.sortData = data;
    },
  },
  components: {
    DefaultDetail: require('./components/default-detail.vue').default,
    QualityDetail: require('./components/quality-detail.vue').default,
  },
};
</script>

<style lang="less" scoped>
.icon-daochu {
  color: var(--color-btn-default);
}
</style>
