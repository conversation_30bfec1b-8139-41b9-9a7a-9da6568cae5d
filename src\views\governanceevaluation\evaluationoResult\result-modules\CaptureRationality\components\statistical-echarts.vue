<template>
  <div class="capture-rationality">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics :listyle="listyle"></index-statistics>
      </div>
      <div class="unqualified">
        <abnormal-statistics-chart>
          <template #title>
            <span>不合格原因分布统计</span>
          </template>
        </abnormal-statistics-chart>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem">
          <template #rank-title> 按合理性排序 </template>
        </subordinate-chart>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-abnormal-chart></subordinate-abnormal-chart>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart-multiple class="line-chart"></line-chart-multiple>
    </div>
  </div>
</template>

<script>
export default {
  name: 'rule',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    AbnormalStatisticsChart:
      require('@/views/governanceevaluation/evaluationoResult/components/abnormal-statistics-chart.vue').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    LineChartMultiple: require('@/views/governanceevaluation/evaluationoResult/components/line-chart-multiple.vue')
      .default,
    SubordinateAbnormalChart:
      require('@/views/governanceevaluation/evaluationoResult/components/subordinate-abnormal-chart.vue').default,
  },
  data() {
    return {
      listyle: {
        height: '0.53rem',
        width: `calc((100% - ${30 / 192}rem) / 3)`,
      },
    };
  },
  props: {
    activeIndexItem: {},
  },
  mixins: [],

  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.capture-rationality {
  position: relative;
  height: 100%;
  width: 100%;
  margin: 10px 0 0 10px;
  overflow-y: auto;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      flex: 1;
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: 100%;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
