<template>
  <div class="data-source">
    <api-tree
      ref="sourceTree"
      placeholder="请选择数据来源"
      :drop-width="410"
      :default-props="defaultModuleProps"
      :tree-props="treeModuleProps"
      :list="treeData"
      :is-default="false"
      :has-cascader="true"
      :cascader-props="dataSourceProps"
      @clearTree="clearModuleTree"
      @selectCascader="selectCascader"
    >
    </api-tree>
  </div>
</template>
<style lang="less" scoped>
.data-source {
  display: inline-block;
  position: relative;
}
</style>

<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    sourceId: {},
  },
  data() {
    return {
      defaultModuleProps: {
        label: 'orgName',
        children: 'children',
      },
      treeModuleProps: {
        id: '',
        treeValue: '',
        nodeKey: 'orgCode',
      },
      treeData: [],
      dataSourceProps: {
        label: 'sourceName',
        value: 'sourceId',
        nodeKey: 'cascader',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async getDataSource() {
      try {
        let res = await this.$http.get(equipmentassets.getDataSourceStatistic);
        this.questionList = res.data.data.map((val) => {
          this.$set(val, 'orgCode', val.sourceId);
          return val;
        });
        let arr = [...this.questionList, ...this.initialOrgList];
        let orgTree = this.$util.common.arrayToJson(
          this.$util.common.deepCopy(arr),
          'code',
          'orgCode',
          this.dataSourceProps.nodeKey,
        );
        this.treeData = this.$util.common.arrayToJson(this.$util.common.deepCopy(orgTree), 'id', 'parentId');
      } catch (err) {
        console.log(err);
      }
    },
    selectCascader(data) {
      this.$emit('selectChange', data);
    },

    clearModuleTree() {
      this.$emit('selectChange', null);
    },
  },
  watch: {
    getOrganizationList: {
      handler(val) {
        if (val.length !== 0) {
          this.getDataSource();
        }
      },
      immediate: true,
    },
    sourceId(val) {
      this.treeModuleProps.id = val;
    },
  },
  computed: {
    ...mapGetters({
      initialOrgList: 'common/getInitialOrgList',
      getOrganizationList: 'common/getOrganizationList',
    }),
  },
  components: {
    ApiTree: require('@/components/api-tree').default,
  },
};
</script>
