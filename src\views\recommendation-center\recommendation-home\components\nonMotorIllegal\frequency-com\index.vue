<!--
    * @FileDescription: 高频违法分析
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="high-frequency-violations container">
        <!-- 地图 -->
        <mapCustom ref="mapBase" mapType="high-frequency-violations" sectionName="nonMotorIllegal"/>
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" @search="handleSearch" v-show="searchBox"></leftBox>
        <detailsBox ref="detailBox" 
            @backSearch="handleBackSearch"
            v-show="!searchBox"
            @cutAnalyse="handleCutAnalyse"></detailsBox>
        <!-- 右侧结果 -->
        <right-list-one :appearList="appearList" v-if="rightShowList" pageName='frequencyCom' @show-pic="showPic"></right-list-one>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import detailsBox from './components/detailsBox.vue';
import RightListOne from '@/views/model-market/components/RightListOne.vue';
import mapCustom from '@/views/model-market/components/map/index.vue';
import { nonMotorIllegalSearchList } from "@/api/recommend";
export default {
    name: 'high-frequency-violations',
    components:{
        mapCustom,
        leftBox,
        detailsBox,
        RightListOne
    },
    props: {
      // 违法类型（31：未佩戴带头盔，32：非机动车载人）
      illegalType: {
        type: Number,
        default: 31
      }
    },
    data () {
        return {
            searchBox: true,
            rightShowList: false,
            appearList: []
        }
    },
    methods: {
        handleSearch(params){
            console.log(params, 'params')
            this.searchBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList({...params, illegalType: this.illegalType})
            })
        },
        handleBackSearch(){
            this.searchBox = true;
            this.handleCancel()
        },
        handleCutAnalyse(data) {
            this.rightShowList = true;
            this.$refs.mapBase.closeMapDom();
            this.$refs.mapBase.resetMarkerbasicPoint();
            nonMotorIllegalSearchList({
              endDate: data.endDate,
              illegalType: data.illegalType,
              pageNumber: 1,
              pageSize: 9999,
              startDate: data.startDate,
              idCardNo: data.idCardNo
            })
            .then((res) => {
              this.appearList = res.data.entities || []
              this.basicPoints = this.appearList.map((item) => {
                  item.showIconBorder = true;
                  item.dataType = "nonMotorIllegal";
                  item.traitImg = item.faceImg
                  return item;
              });
              this.$refs.mapBase.sprinkleNormalPoint(this.basicPoints, true);
              this.$refs.mapBase.setCenter(this.basicPoints[0]);
            })
        },
        handleCancel() {
            this.rightShowList = false;
            this.$refs.mapBase.resetMarkerbasicPoint();
            this.$refs.mapBase.clearPoint()
        },
        showPic(item,index){
          this.$refs.mapBase.chooseNormalPoint(
                this.basicPoints,
                "nonMotorIllegal",
                index
            );
        },
    }
}
</script>

<style lang='less' scoped>
.high-frequency-violations{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
