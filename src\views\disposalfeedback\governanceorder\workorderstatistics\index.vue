<template>
  <div class="workorder-statistics-container">
    <div class="flex-row">
      <api-organization-tree
        ref="orgTree"
        class="mr-sm"
        :select-tree="searchData"
        :custorm-node="false"
        @selectedTree="selectedOrgTree"
        placeholder="请选择组织机构"
      >
      </api-organization-tree>
      <tag-view
        :list="permissionTagList"
        :default-active="defaultTag"
        @tagChange="changeStatus"
        ref="tagView"
      ></tag-view>
    </div>
    <div class="devider-line"></div>
    <div class="search-bar">
      <div class="flex-aic">
        <underline-menu
          v-model="searchData.queryType"
          :data="assignList"
          @on-change="onChangeUnderlineMenu"
        ></underline-menu>
        <Checkbox
          v-model="searchData.isOwnOrgCode"
          true-value="1"
          false-value="0"
          v-if="searchData.type === TYPE_ALLORDER && searchData.queryType === QUERYTYPE_ASSIGN_UNIT"
          class="flex-aic"
          @on-change="changeOwnerOrder"
        >
          <span>本单位在手工单</span>
          <Tooltip
            max-width="300"
            content="【本单位在手工单】仅包含当前指派给本单位处理的工单！取消勾选，系统则会统计当前指派给本单位处理和下发给下级单位处理的所有工单！"
          >
            <i class="icon-font icon-wenhao f-14 ml-xs font-dark-blue" @click.stop.prevent></i>
          </Tooltip>
        </Checkbox>
      </div>
      <ui-label class="inline mr-lg" label="创建时间：">
        <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
      </ui-label>
    </div>
    <div class="component-container auto-fill">
      <component
        ref="workOrder"
        :key="searchData.type"
        :is="componentName"
        :statistics-type="permissionTagList.length > 0 && permissionTagList[defaultTag]"
        :common-search-data="searchData"
        :statistics-detail="statisticsDetail"
        @click="onClickLink"
        @click-org="onOrgClickLink"
      >
      </component>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { tagList, deviceTypeList, ALLORDER, UNIT_CREATION, ASSIGN_UNIT } from '../util/enum.js';
export default {
  name: 'workorderstatistics',
  props: {},
  data() {
    return {
      componentName: null,
      searchData: {
        type: ALLORDER,
        orgCode: '000000',
        queryType: UNIT_CREATION,
        beginTime: '',
        endTime: '',
        isOwnOrgCode: '0', //本单位在手工单
      },
      permissionDeviceTypeList: [],
      permissionTagList: [],
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '今日', value: 'today' },
          { label: '本周', value: 'thisWeek' },
          { label: '本月', value: 'thisMonth' },
          { label: '今年', value: 'thisYear' },
          { label: '全部', value: 'all' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      assignList: [
        /**
         * @ApiModelProperty("查询类型（为空查询所有，1 单位创建，2 指派给单位，3 当前指派给我，4 我创建的，5 我签收的，6 我处理的，7 我关闭的）")
         * private Integer queryType;
         */
        { code: '1', label: '单位创建' },
        { code: '2', label: '指派给单位' },
      ],
      defaultTag: 0,
      statisticsDetail: null,
      TYPE_ALLORDER: ALLORDER,
      QUERYTYPE_ASSIGN_UNIT: ASSIGN_UNIT, //指派给单位
    };
  },
  async mounted() {
    this.setOrgTreeDefaultNode();
    this.permissionDeviceTypeList = deviceTypeList;
    this.permissionTagList = tagList;
    await this.$nextTick();
    if (!this.$route.params?.purpose) {
      this.changeStatus(0);
    }
  },
  watch: {},
  async activated() {},
  methods: {
    async onClickLink(row, column) {
      let params = {
          purpose:'viewStatisticsDetail',
          orgCode: row.orgCode  || this.searchData.orgCode,
          queryConditionStatus: column.queryConditionStatus,
          queryType: this.searchData.queryType,
        }
        // 类型统计带上指标搜索条件
        if(this.defaultTag === 1) {
          params.indexId = row.indexId
          params.indexModule = row.indexModule
        }
      this.$router.push({
        name:'unitworkorder',
        params,
      })
    },
    onOrgClickLink(row) {
      if (this.searchData.orgCode === row.orgCode) return;
      this.searchData.orgCode = row.orgCode;
      this.$refs.workOrder.search();
    },
    selectedOrgTree(area) {
      this.searchData.orgCode = area.orgCode;
      this.$refs.workOrder.search();
    },
    removeTooltipNode() {
      let tooltipNode = document.querySelectorAll('.ivu-tooltip-popper, .tips-box');
      tooltipNode.forEach((node) => {
        node.style.display = 'none';
      });
    },
    changeStatus(index) {
      this.defaultTag = index;
      this.componentName = this.permissionTagList[index]['component'];
      this.removeTooltipNode();
    },
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
      this.$refs.workOrder.search();
    },
    async onChangeUnderlineMenu() {
      await this.$nextTick();
      this.$refs.workOrder.search();
    },
    //选中本单位在手工单触发
    changeOwnerOrder() {
      this.$refs.workOrder.search();
    },
    //设置单位筛选默认选中
    setOrgTreeDefaultNode() {
      const { orgCode } = this.defaultSelectedOrg;
      if (this.$refs.orgTree) {
        this.$refs.orgTree.selectTree.orgCode = orgCode || this.initialOrgList[0]?.orgCode;
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiTabsWrapped: require('@/components/ui-tabs-wrapped.vue').default,
    TagView: require('@/components/tag-view.vue').default,
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    UiSwitchDrop: require('@/components/ui-switch-drop/ui-switch-drop.vue').default,
    UnderlineMenu: require('@/components/underline-menu').default,
    workOrderStatistics: require('@/views/disposalfeedback/governanceorder/module/work-order-statistics.vue').default,
    typedetails: require('@/views/disposalfeedback/governanceorder/module/typedetails.vue').default,
    unitstatistics: require('@/views/disposalfeedback/governanceorder/module/unitstatistics.vue').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.workorder-statistics-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-content);
  padding: 10px 20px 0 20px;

  display: flex;
  flex-direction: column;
  .devider-line {
    background: var(--devider-line);
    height: 1px;
    margin: 10px 0;
  }
  .search-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    border-radius: 4px;
    background: var(--bg-navigation);

    @{_deep} .underline-menu-wrapper {
      background: transparent;
      width: auto;
      .tab {
        font-size: 14px;
      }
      .tab:before {
        content: none;
      }
    }
  }
  @{_deep} .custorm-tree-node {
    height: 26px;
    line-height: 26px;
  }
}
</style>
