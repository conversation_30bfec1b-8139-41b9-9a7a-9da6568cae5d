<template>
  <div class="over-flow">
    <div
      class="camera fl"
      @click="selectCamera"
      :class="{ 'reset-width': width }"
      :style="{ '--reset-width': `${width / 192}rem` }"
    >
      <i class="icon-font icon-jianceshebeishuliang inline font-blue f-16 mr-sm"></i>
      <span class="font-blue f-14 inline vt-middle" v-if="deviceList.length === 0">请选择摄像机</span>
      <span v-else class="font-blue f-14 inline vt-middle">已选择({{ deviceList.length }})</span>
    </div>
    <!-- <div class="camera-select fl" @click="cameraMap" title="通过地图选择摄像机">
            <i class="icon-font icon-xuanzeshexiangjifanwei"></i>
        </div> -->

    <camera-module
      v-model="cameraShow"
      @pushCamera="pushCamera"
      :camera-type="cameraType"
      :default-camera-list="deviceList"
    >
    </camera-module>

    <!-- <camera-map v-model="cameraMapShow" :device-list="deviceList" @putCameraList="pushCamera"></camera-map> -->
  </div>
</template>
<style lang="less" scoped>
.camera {
  width: 230px;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: var(--bg-choose-device);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: var(--bg-choose-device-active);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}

.camera-select {
  width: 89px;
  height: 32px;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  cursor: pointer;
  .icon-xuanzeshexiangjifanwei {
    font-size: 20px;
    color: var(--color-primary);
  }
}
@{_deep} .ivu-modal-header {
  border-bottom: 1px solid var(--border-color);
}
.reset-width {
  width: var(--reset-width) !important;
}
</style>
<script>
import common from '@/config/api/common';
export default {
  data() {
    return {
      cameraShow: false,
      cameraMapShow: false,
      deviceList: [],
    };
  },
  created() {},
  mounted() {
    // 此处判断是因为从其他路由跳转传入deviceNo时watch获取不到其变化
    if (this.deviceIds.length !== 0) {
      this.searchCamera();
    }
  },
  methods: {
    selectCamera() {
      this.cameraShow = true;
    },
    cameraMap() {
      this.cameraMapShow = true;
    },
    pushCamera(deviceList) {
      this.deviceList = deviceList;
      this.$emit('pushCamera', deviceList);
    },
    searchCamera() {
      // this.deviceList = [];
      let tempObj = {};
      this.deviceIds.forEach((row) => {
        tempObj[row] = row;
      });
      // this.cameraList.forEach(row=>{
      //     if(!!tempObj[row.deviceId]){
      //         this.deviceList.push(row);
      //     }
      // });
    },
    async getDevice(orgCodes) {
      try {
        let res = await this.$http.post(common.getDeviceList, {
          orgCodes: orgCodes,
          sbgnlxs: this.cameraType,
        });
        this.allCameraList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    list(val) {
      this.deviceList = val;
    },
    deviceIds: {
      handler(val) {
        if (val.length === 0) {
          this.deviceList = [];
        } else {
          this.searchCamera();
        }
      },
      immediate: true,
    },
  },
  computed: {},
  props: {
    list: {
      type: Array,
      default() {
        return [];
      },
    }, //已选中摄像头
    deviceIds: {
      type: Array,
      default() {
        return [];
      },
    },
    // 摄像机功能类型 1:车辆卡口,2:人员卡口,3:微卡口,4:特征摄像机,5:普通监控,6:高空瞭望摄像机,99:其他
    cameraType: {
      type: Array,
      default: () => [],
    },
    width: {
      type: Number,
    },
  },
  components: {
    CameraModule: require('@/components/camera-module.vue').default,
    // CameraMap: require('@/components/camera-map.vue').default,
  },
};
</script>
