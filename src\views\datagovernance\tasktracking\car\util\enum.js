export default {
  aggregateEnums: {
    accurrency: '图像抓拍时间准确性检测',
    timeliness: '图像上传及时性检测',
    bigPic: '大图URL检测',
    faceStructure: '车辆结构化',
    repeatImage: '车牌识别准确性检测优化',
    bigContactSmall: '车辆结构化属性准确性检测优化',
    dataexport: '数据输出',
  },
  aggregateOptions: [
    {
      addVisible: false,
      top: '1.1rem',
      left: '1.25%',
      datas: [
        {
          icon: 'icon-zu16191',
          title: '数据输入',
          topicComponentId: 51,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '接入数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '今日增量', num: 0, color: '#F18A37', fileName: 'todayAccessDataCount' },
          ],
          left: '10px',
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '16.2%',
      },
    },
    {
      addVisible: false,
      top: '0.35rem',
      left: '21.7%',
      datas: [
        {
          name: 'carModal1',
          icon: 'icon-tuxiangzhuapaishijianzhunquexingjiance',
          title: '图像抓拍时间准确性检测',
          subTitle: '异常数据',
          topicComponentId: 27,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '时间异常', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#bc3c19', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'carModal2',
          icon: 'icon-tuxiangshangchuanjishixingjiance',
          title: '图像上传及时性检测',
          subTitle: '异常数据',
          topicComponentId: 28,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '上传超时', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#bc3c19', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'carModal3',
          icon: 'icon-datuURLjiance',
          title: '大图URL检测',
          subTitle: '异常数据',
          topicComponentId: 29,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#bc3c19', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '37.1%',
      },
    },
    {
      addVisible: false,
      top: '1.1rem',
      left: '42.5%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-cheliangshitushuju',
          title: '车辆结构化',
          topicComponentId: 31,
          list: [
            { title: '算法数量', num: 0, color: '#05FEF5', fileName: 'algorithmCount' },
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '结构化数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '转化率', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '57.2%',
      },
    },

    {
      addVisible: false,
      top: '0.58rem',
      left: '63.1%',
      datas: [
        {
          name: 'carModal4',
          icon: 'icon-shebeibianmageshijiance',
          title: '车牌识别准确性检测优化',
          subTitle: '异常数据',
          topicComponentId: 34,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#bc3c19', fileName: 'existingExceptionRate' },
            { title: '治理优化', num: 0, color: '#13b13d', fileName: 'governanceOptimizationCount' },
            { title: '优化占比', num: '0%', color: '#13b13d', fileName: 'governanceOptimizationRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'carModal5',
          icon: 'icon-cheliangshitushuju',
          title: '车辆结构化属性完整性与准确检性测优化',
          subTitle: '异常数据',
          topicComponentId: 36,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#bc3c19', fileName: 'existingExceptionRate' },
            { title: '治理优化', num: 0, color: '#13b13d', fileName: 'governanceOptimizationCount' },
            { title: '优化占比', num: '0%', color: '#13b13d', fileName: 'governanceOptimizationRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '5%',
        height: '0.04rem',
        top: '1.28rem',
        left: '79.2%',
      },
    },
    {
      addVisible: false,
      top: '0.98rem',
      left: '84.8%',
      datas: [
        {
          name: 'carModal6',
          icon: 'icon-zu1665',
          title: '数据输出',
          topicComponentId: 37,
          list: [
            { title: '检测数量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#bc3c19', fileName: 'existingExceptionCount' },
            { title: '合格率', num: '0%', color: '#13b13d', fileName: 'qualifiedRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
    },
  ],
};
