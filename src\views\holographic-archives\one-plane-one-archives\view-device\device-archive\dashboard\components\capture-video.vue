<template>
  <div>
    <Tabs class="ui-tabs" type="card">
      <TabPane label="历史录像分析">
        <CaptureBehavior />
      </TabPane>
      <TabPane label="在线情况分析">
        <OnlineSituation :deviceId="info.deviceId" v-if="info.deviceId" />
      </TabPane>
    </Tabs>
  </div>
</template>
<script>
import CaptureBehavior from './capture-behavior'
import OnlineSituation from './online-situation'
export default {
  components: {
    CaptureBehavior,
    OnlineSituation
  },
  props: {
    info: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  computed: {},
  created() {},
  mounted() {},

  methods: {}
}
</script>
<style lang="less" scoped></style>
