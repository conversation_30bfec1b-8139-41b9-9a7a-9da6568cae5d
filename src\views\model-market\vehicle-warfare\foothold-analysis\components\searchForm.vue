<!--
    * @FileDescription: 落脚点分析
 -->
<template>
  <div class="search_condition">
    <div class="search_form">
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut"><span>*</span>车牌号码:</p>
        </div>
        <div class="search_content">
          <Input
            v-model="queryParams.plateNo"
            placeholder="请输入精确车牌号码"
            class="wrapper-input"
          ></Input>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">开始时间:</p>
        </div>
        <div class="search_content daterange">
          <DatePicker
            v-model="queryParams.startTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="开始时间"
            transfer
          ></DatePicker>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">结束时间:</p>
        </div>
        <div class="search_content daterange">
          <DatePicker
            v-model="queryParams.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="结束时间"
            transfer
          ></DatePicker>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">间隔时间:</p>
        </div>
        <div class="search_content">
          <Input v-model="queryParams.stayTime" class="wrapper-input"></Input
          ><span class="tip">&nbsp;小时以上</span>
        </div>
      </div>
      <div class="btn-group">
        <Button type="primary" class="btnwidth" @click="handleSearch"
          >查询</Button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      queryParams: {
        plateNo: "",
        startTime: "",
        endTime: "",
        stayTime: 2,
      },
    };
  },
  watch: {},
  mounted() {
    if (!this.queryParams.startTime)
      this.queryParams.startTime = this.$dayjs()
        .subtract(30, "day")
        .format("YYYY-MM-DD HH:mm:ss");
    if (!this.queryParams.endTime)
      this.queryParams.endTime = this.$dayjs().format("YYYY-MM-DD HH:mm:ss");
  },
  methods: {
    // 查询
    handleSearch() {
      if (!this.queryParams.startTime || !this.queryParams.endTime) {
        this.$Message.error("请输入开始时间和结束时间");
        return;
      }
      this.queryParams.startTime = this.$dayjs(
        this.queryParams.startTime
      ).format("YYYY-MM-DD HH:mm:ss");
      this.queryParams.endTime = this.$dayjs(this.queryParams.endTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.$emit("search", this.queryParams);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
</style>
