export const PUBLISHSTATUS = {
  '1': {
    color: 'var(--color-failed)',
    text: '未下发',
  },
  '2': {
    color: 'var(--color-success)',
    text: '已下发',
  },
};
export const PUBLISHSTATUSLIST = Object.keys(PUBLISHSTATUS).map((key) => {
  return {
    dicKey: key,
    dicValue: PUBLISHSTATUS[key].text,
  };
});
export const HANDLESTATUS = {
  '1': {
    color: 'var(--color-failed)',
    text: '未处理',
  },
  '2': {
    color: 'var(--color-success)',
    text: '已处理',
  },
  '3': {
    color: 'var(--color-success)',
    text: '检测结果异议',
  },
};
export const HANDLESTATUSLIST = Object.keys(HANDLESTATUS).map((key) => {
  return {
    dicKey: key,
    dicValue: HANDLESTATUS[key].text,
  };
});
export const CONFIGORGTYPE = {
  '省级': '1',
  '市级': '2',
  '区级': '3',
};
export const INDEXMODULE = {
  '1': '视图基础数据',
  '2': '人脸视图数据',
  '3': '车辆视图数据',
  '4': '视频流数据 ',
  '5': '重点人员数据',
  '6': '档案数据指标',
  '7': '平台可用性指标',
  '8': '场所数据指标',
};
export const INDEXMODULELIST = Object.keys(INDEXMODULE).map((key) => {
  return {
    dicKey: key,
    dicValue: INDEXMODULE[key],
  };
});
export const selfTableColumns = [
  { type: 'selection', align: 'center', width: 50 },
  { title: '序号', type: 'index', align: 'center', width: 40 },
  { title: '问题名称', key: 'name', align: 'left', tooltip: true, minWidth: 280 },
  { title: '达标值', key: 'standardsValue', align: 'center', minWidth: 100 },
  { title: '实际值', slot: 'resultValue', align: 'center', minWidth: 100 },
  { title: '检测时间', key: 'examineTime', align: 'center', minWidth: 150 },
  { title: '所属单位', key: 'orgName', align: 'center', minWidth: 150 },
  { title: '问题类型', slot: 'indexModule', align: 'center', minWidth: 150 },
  { title: '下发状态', slot: 'publishStatus', align: 'center', key: 'publishStatus', minWidth: 150 },
  { title: '处理状态', slot: 'handleStatus', align: 'center', minWidth: 150 },
  { title: '下发时间', key: 'publishTime', align: 'center', minWidth: 150 },
  { title: '处理时间', key: 'handleTime', align: 'center', minWidth: 150 },
  {
    title: '操作',
    slot: 'action',
    align: 'center',
    fixed: 'right',
    className: 'table-action-padding',
    minWidth: 150,
  },
];
export const superiorTableColumns = [
  { type: 'selection', align: 'center', width: 50 },
  { title: '序号', type: 'index', align: 'center', width: 40 },
  { title: '问题名称', key: 'name', align: 'left', tooltip: true, minWidth: 300 },
  { title: '达标值', key: 'standardsValue', align: 'center', minWidth: 100 },
  { title: '实际值', slot: 'resultValue', align: 'center', minWidth: 100 },
  { title: '检测时间', key: 'examineTime', align: 'center', minWidth: 150 },
  { title: '所属单位', key: 'orgName', align: 'center', minWidth: 200 },
  { title: '问题类型', slot: 'indexModule', align: 'center', minWidth: 200 },
  { title: '处理状态', slot: 'handleStatus', align: 'center', minWidth: 150 },
  { title: '下发时间', key: 'publishTime', align: 'center', minWidth: 150 },
  { title: '处理时间', key: 'handleTime', align: 'center', minWidth: 150 },
  {
    title: '操作',
    slot: 'action',
    align: 'center',
    fixed: 'right',
    className: 'table-action-padding',
    minWidth: 150,
  },
];
export const iconStaticsList = [
  {
    name: '累计在线',
    count: '0',
    countStyle: {
      color: 'var(--color-success)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'onlineTimeM',
  },
  {
    name: '累计离线',
    count: '0',
    countStyle: {
      color: 'var(--color-failed)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'unOnlineTimeM',
  },
  {
    name: '累计报备',
    count: '0',
    countStyle: {
      color: 'var(--color-primary)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'reportDayNumOfM',
  },
  {
    name: '在线率',
    count: '0',
    countStyle: {
      color: 'var(--color-display-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
  },
];
export const hasDetailIndexList = [
  1001, 4008, 4002, 4003, 4006, 4007, 4004, 3005, 3001, 3003, 3002, 3006, 3007, 3008, 3010, 3011, 3009, 2001, 2002,
  2003, 2004, 2005, 2006, 2008, 3013, 2014, 3018,
];
/**
 * component 对比组件
 * resultComponent 结果页组件
 */
export const compareIndexType = [
  {
    indexId: '4001',
    indexName: '重点实时视频可调阅率',
    indexType: 'VIDEO_PLAYING_ACCURACY',
    component: 'device-result-compare',
    resultComponent: 'video-playing-accuracy-result',
  },
  {
    indexId: '4009',
    indexName: '普通实时视频可调阅率',
    indexType: 'VIDEO_GENERAL_PLAYING_ACCURACY',
    component: 'device-result-compare',
    resultComponent: 'video-playing-accuracy-result',
  },
  {
    indexId: '4012',
    indexName: '视频流质量合格率',
    indexType: 'VIDEO_QUALITY_PASS_RATE',
    component: 'device-result-compare',
    resultComponent: 'video-quality-pass-rate-result',
  },
  {
    indexId: '4024',
    indexName: '视频流质量合格率（人工复核）',
    indexType: 'VIDEO_QUALITY_PASS_RATE_RECHECK',
    component: 'device-result-compare',
    resultComponent: 'video-quality-pass-rate-result',
  },
  {
    indexId: '7004',
    indexName: '共享联网平台在线率',
    indexType: 'VIDEO_PLATFORM_ONLINE_RATE',
    component: 'offline-result-compare',
    resultComponent: 'viedo-platform-online-rate-result',
  },
  {
    indexId: '4025',
    indexName: '视频监控数量达标率',
    indexType: 'VIDEO_QUANTITY_STANDARD',
    component: 'quantity-result-compare',
  },
];
export const deviceTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '设备编码',
    key: 'deviceId',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '设备名称',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '检测结果',
    slot: 'qualified',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
];
//视频监控数量达标率对比详情
export const quantityTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: '区县名称',
    key: 'deviceId',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '行政区划代码',
    key: 'deviceId',
    align: 'left',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '达标数量',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '满分数量',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '上级检测数量',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '本级已上报数量',
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '比对结果',
    slot: 'qualified',
    align: 'left',
    tooltip: true,
    minWidth: 120,
  },
  {
    minWidth: 100,
    title: '操作',
    slot: 'action',
    align: 'center',
  },
];

/**
 * 是否异议 类型 0默认 1异议"
 * @type {number}
 */
export const OPPOSE = 1;
/**
 * 是否异议 类型 0默认 1异议"
 * @type {number}
 */
export const UNOPPOSE = 0;

/**
 * "请求服务类型：0评测服务（默认） 1资产服务 2视频流播放服务"
 * @type {{assetApp: number, videoStreamApp: number, evaluationApp: number}}
 */
export const SERVER = {
  evaluationApp: 0,
  assetApp: 1,
  videoStreamApp: 2,
};

/**
 * "是否相同 类型 0不相同 1相同
 */
export const SAME = 1;
export const UNSAME = 0;

//是否重点指标
export const IS_IMPORTANT = '1';
