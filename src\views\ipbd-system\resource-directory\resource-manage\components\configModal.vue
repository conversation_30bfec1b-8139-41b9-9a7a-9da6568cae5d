<template>
  <ui-modal v-model="visible" :r-width="1200" title="配置" list-content @onOk="confirmHandle" class="config">
    <p class="mb-5 color-warning">请优先输入中文名称，对应配置项才会生效；如果不输入中文名称，对应配置项不会生效!</p>
    <Table class="auto-fill table" :columns="columns" border :loading="loading" :data="tableData" max-height="580">
      <template #fieldNameCn="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].fieldNameCn" maxlength="50" class="input-wid"></Input>
      </template>
      <template #isShow="{ index }">
        <Select placeholder="请选择" v-model="tableData[index].isShow" transfer :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''">
          <Option :value="0" placeholder="请选择">否</Option>
          <Option :value="1" placeholder="请选择">是</Option>
        </Select>
      </template>
      <template #isSearch="{ index }">
        <Select placeholder="请选择" v-model="tableData[index].isSearch" transfer :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''">
          <Option :value="0" placeholder="请选择">否</Option>
          <Option :value="1" placeholder="请选择">是</Option>
        </Select>
      </template>
      <template #searchType="{ index }">
        <Select placeholder="请选择" v-model="tableData[index].searchType" transfer :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''">
          <Option v-if="!tableData[index].isSearch" :value="0">无</Option>
          <Option v-for="(item, $index) in searchTypeList" 
                :value="item.dataKey" 
                :disabled="handleDis(index, item)" 
                :key="$index">{{ item.dataValue }}</Option>
        </Select>
      </template>
      <template #dictionaryCode="{ index }">
        <Select placeholder="请选择" filterable v-model="tableData[index].dictionaryCode" transfer :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''">
          <Option value="">无</Option>
          <Option v-for="(item, $index) in dictTypedata" :value="item.typeKey" :key="$index">{{ item.typeValue }}</Option>
        </Select>
      </template>
      <template #fieldSort="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].fieldSort" :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''"></Input>
      </template>
      <template #searchSort="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].searchSort" :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''"></Input>
      </template>
      <template #cloudSearch="{ index }">
        <Select placeholder="请选择" v-model="tableData[index].cloudSearch" transfer :disabled="tableData[index].fieldNameCn == '' || tableData[index].fieldNameCn == null || tableData[index].fieldNameCn.trim() == ''">
          <Option :value="0" placeholder="请选择">否</Option>
          <Option :value="1" placeholder="请选择">是</Option>
        </Select>
      </template>
    </Table>
  </ui-modal>
</template>
<script>
import { resourceConfigur, queryResourceConfigur } from '@/api/dataGovernance'
import { mapGetters } from 'vuex'
export default {
  props: {
    //对应字典
    dictTypedata: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      resourceId: '',
      columns: [
        { title: '序号', align: 'center', width: 90, type: 'index', key: 'index', fixed: 'left' },
        { title: '字段名称', key: 'fieldName', width: 160, fixed: 'left' },
        { title: '中文名称', slot: 'fieldNameCn', width: 160 },
        { title: '是否显示字段', slot: 'isShow', width: 140 },
        { title: '是否作为检索条件', slot: 'isSearch', width: 160 },
        { title: '检索类型', slot: 'searchType', width: 140 },
        { title: '对应字典', slot: 'dictionaryCode', width: 160 },
        { title: '字段显示排序', slot: 'fieldSort', width: 140 },
        { title: '查询条件排序', slot: 'searchSort', width: 140 },
        { title: '是否作为云搜检索字段', slot: 'cloudSearch', width: 180 }
      ],
      tableData: []
    }
  },
  computed: {
    ...mapGetters({
      searchTypeList: 'dictionary/getSearchTypeList' //检索类型
    })
  },
  watch: {},
  created() {},
  methods: {
    handleDis(index, item){
        if(this.tableData[index].isSearch === 1){
            if(this.tableData[index].fieldName === 'ccrq'){
                if(item.dataKey == '6'){
                    return false
                }else{
                    return true
                }
            }
            return false
        }else{
            return true
        }
    },
    // 初始化
    show(val) {
        this.visible = true
        this.resourceId = val.id
        this.$nextTick(() => {
            this.loading = true
            let data = {
                resourceId: val.id,
                resourceName: val.resourceName
            }
            queryResourceConfigur(data).then(res => {
                let data = res.data
                // 默认值为否---0否/1是
                this.tableData = data.map(item => {
                    let obj = {
                        ...item,
                        resourceId: val.id,
                        isShow: item.isShow || 0,
                        isSearch: item.isSearch || 0,
                        searchType: item.searchType || 0,
                        dictionaryCode: item.dictionaryCode || '',
                        cloudSearch: item.cloudSearch || 0
                    }
                    return obj
                })
                this.loading = false;
                console.log(this.searchTypeList)
            })
        })
    },
    // 确认提交
    confirmHandle() {
      let data = {
        resourceId: this.resourceId,
        configEntityList: this.tableData
      }
      resourceConfigur(data).then(res => {
        this.visible = false
        this.$Message.success(res.msg)
        this.$emit('refreshDataList')
      })
    }
  }
}
</script>
<style lang="less" scoped>
.config {
  /deep/ .ivu-table-body {
    min-height: 220px;
  }
  /deep/.ivu-table-tbody tr td {
    background-color: #f9f9f9 !important;
  }
  /deep/ .ivu-table-border th {
    border-right: 1px solid #d3d7de !important;
  }
  /deep/.ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #d3d7de;
  }
  /deep/.ivu-input,
  .ivu-select {
    width: 110px;
  }
}
</style>
