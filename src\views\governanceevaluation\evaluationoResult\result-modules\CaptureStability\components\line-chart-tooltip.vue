<template>
  <div class="tooltip-container">
    <div v-for="(item, index) in data" :key="index">
      <span v-html="item.marker"></span>
      <div v-for="(column, index) in columns" :key="index">
        <span class="mr-sm">{{ column.renderTitle ? column.renderTitle(item) : column.title }}</span>
        <span class="fr">{{ column.render ? column.render(item) : item.data[column.key] }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'line-chart-tooltip.vue',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  background: rgba(13, 53, 96, 0);
  border: none;
  opacity: 1;
  .block {
    display: inline-block;
    height: 6px;
    width: 6px;
    line-height: 14px;
  }
}
</style>
