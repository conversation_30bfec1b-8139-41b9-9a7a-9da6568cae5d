import axios from 'axios';
import algorithm from '@/config/api/algorithm';
import user from '@/config/api/user';
export default {
  namespaced: true,
  state: {
    // 1400标准字典管理
    colorType: [],
    vehicleBandType: [],
    vehicleClassType: [],
    plateClassType: [],
    // 批量字典值，默认添加obj对象格式，例propertySearch_sbdwlxobj：{1:设备}
    propertySearch_sbdwlx: [],
    sxjgnlx_receive: [],
    check_status: [],
    algorithmVendorType: [],
    person_type: [],
    error_category: [],
    propertySearch_phystatus: [],
    tool_vehicle_info_reason_type: [],
    tag_category: [],
    propertySearch_sourceId: [],
    algorithmType: [],
    algorithmTransfer: [],
    ivdg_video_ods_check_model: [],
    address_type: [],
    camera_type: [],
    camera_add_type: [],
    camera_team_type: [],
    area_type: [],
    report_interface_type: [], // 数据上报注册-接口类型
    manufacturer: [], // 生成厂商
    data_source_category: [], // 资产对比 来源标识
    assets_data_source: [], // 资产对比 数据源
    ivdg_image_ods_check_model: [], // ocr 算法
    sq_nlj: [], //能力集
    sq_kznlj: [], //扩展能力集
    other_device_type: [], //设备类型
    propertySearch_isonline: [], // 设备在线状态
    evaluation_report_reason: [], // 报备原因
    propertySearch_sbgnlxExt: [], // 设备功能类型扩展
    propertySearch_phyStatusExt: [], // 设备状态扩展
    algorithmList: [], // 查询多算法厂商配置分页列表
    video_code_type: [],
    vehicle_property: [], //车辆结构化属性列表
    propertySearch_sourceDomain: [] //数据来源域
  },
  getters: {
    // 1400标准字典管理
    algorithmTransfer: (state) => {
      return state.algorithmTransfer;
    },
    // 1400标准字典管理
    colorType: (state) => {
      return state.colorType;
    },
    vehicleBandType: (state) => {
      return state.vehicleBandType;
    },
    vehicleClassType: (state) => {
      return state.vehicleClassType;
    },
    plateClassType: (state) => {
      return state.plateClassType;
    },
    // 批量获取字典值
    propertySearch_sbdwlx(state) {
      return state.propertySearch_sbdwlx;
    },
    propertySearch_sbdwlxobj(state) {
      return state.propertySearch_sbdwlxobj;
    },
    sxjgnlx_receive(state) {
      return state.sxjgnlx_receive;
    },
    propertySearch_sbgnlxobj(state) {
      return state.propertySearch_sbgnlxobj;
    },
    check_status(state) {
      return state.check_status;
    },
    check_statusobj(state) {
      return state.check_statusobj;
    },
    algorithmVendorType(state) {
      return state.algorithmVendorType;
    },
    algorithmVendorTypeobj(state) {
      let obj = {};
      state.algorithmVendorType.map((val) => {
        obj[val.dataKey] = val.dataValue;
      });
      return state.obj;
    },
    person_type(state) {
      return state.person_type;
    },
    error_category(state) {
      return state.error_category;
    },
    propertySearch_phystatus(state) {
      return state.propertySearch_phystatus;
    },
    algorithmType(state) {
      return state.algorithmType;
    },
    tool_vehicle_info_reason_type(state) {
      return state.tool_vehicle_info_reason_type;
    },
    tag_category(state) {
      return state.tag_category;
    },
    propertySearch_sourceId(state) {
      return state.propertySearch_sourceId;
    },
    ivdg_video_ods_check_model(state) {
      return state.ivdg_video_ods_check_model;
    },
    address_type(state) {
      return state.address_type;
    },
    camera_type(state) {
      return state.camera_type;
    },
    camera_add_type(state) {
      return state.camera_add_type;
    },
    camera_team_type(state) {
      return state.camera_team_type;
    },
    area_type(state) {
      return state.area_type;
    },
    report_interface_type(state) {
      return state.report_interface_type;
    },
    manufacturer(state) {
      return state.manufacturer;
    },
    data_source_category(state) {
      return state.data_source_category;
    },
    assets_data_source(state) {
      return state.assets_data_source;
    },
    ivdg_image_ods_check_model(state) {
      return state.ivdg_image_ods_check_model;
    },
    sq_nlj(state) {
      return state.sq_nlj;
    },
    sq_kznlj(state) {
      return state.sq_kznlj;
    },
    other_device_type(state) {
      return state.other_device_type;
    },
    propertySearch_isonline(state) {
      return state.propertySearch_isonline;
    },
    evaluation_report_reason(state) {
      return state.evaluation_report_reason;
    },
    getSbgnlxExt(state) {
      return state.propertySearch_sbgnlxExt;
    },
    getPhyStatusExt(state) {
      return state.propertySearch_phyStatusExt;
    },
    getAlgorithmList(state) {
      return state.algorithmList;
    },
    getVideoCodeType(state) {
      return state.video_code_type;
    },
    getVehicleProperty(state) {
      return state.vehicle_property;
    },
    getDeployNetwork(state) {
      return state.deploy_network;
    },
    propertySearch_sourceDomain(state){
      return state.propertySearch_sourceDomain;
    }
  },

  mutations: {
    setAlldicData(state, { data, params }) {
      data.map((val) => {
        params.map((key) => {
          if (val[key]) {
            val[key].map((val) => {
              val.name = val.dataValue; // 有一处是被字段为name（未找到）
            });
            state[key] = val[key];
          }
        });
      });
    },
    // 1400标准字典管理
    setcolorType(state, list) {
      state.colorType = list;
    },
    setvehicleBandType(state, list) {
      state.vehicleBandType = list;
    },
    setvehicleClassType(state, list) {
      state.vehicleClassType = list;
    },
    setplateClassType(state, list) {
      state.plateClassType = list;
    },
    setAlgorithmTransfer(state, list) {
      state.algorithmTransfer = list;
    },
    setAlgorithmList(state, list) {
      state.algorithmList = list;
    },
    setVehicleProperty(state, list) {
      state.vehicle_property = list;
    },
  },
  actions: {
    // 批量获取字典值 原algorithm.dictData接口统一改为批量接口
    async getAlldicData({ commit }) {
      const params = [
        'ivdg_video_ods_check_model',
        'propertySearch_sbdwlx',
        'sxjgnlx_receive',
        'check_status',
        'algorithmVendorType',
        'person_type',
        'error_category',
        'propertySearch_phystatus',
        'device_data_category',
        'algorithmType',
        'tool_vehicle_info_reason_type',
        'tag_category',
        'propertySearch_sourceId',
        'report_interface_type',
        'manufacturer',
        'data_source_category',
        'assets_data_source',
        'ivdg_image_ods_check_model',
        'sq_nlj',
        'sq_kznlj',
        'other_device_type',
        'propertySearch_isonline',
        'evaluation_report_reason',
        'propertySearch_sbgnlxExt',
        'propertySearch_phyStatusExt',
        'video_code_type',
        'vehicle_property',
        'deploy_network',
        'propertySearch_sourceDomain'
      ];
      await axios.post(user.queryDataByKeyTypes, params).then((res) => {
        commit('setAlldicData', {
          data: res.data.data,
          params: params,
        });
      });
    },
    // 批量获取字典值 (osd字幕设置详情)
    async getAlldicData1({ commit }) {
      const params = ['address_type', 'camera_type', 'camera_add_type', 'camera_team_type', 'area_type'];
      await axios.post(user.queryDataByKeyTypes, params).then((res) => {
        commit('setAlldicData', {
          data: res.data.data,
          params: params,
        });
      });
    },
    // 车辆颜色字典表 -- 1400标准字典管理
    async getcolorType({ commit }) {
      await axios
        .post(algorithm.standard1400, {
          dictType: ['colorType'],
          type: 'vehicle',
        })
        .then((res) => {
          // this.colorList = res.data.data
          commit('setcolorType', res.data.data);
        });
    },
    // 车辆型号字典表 -- 1400标准字典管理
    async getvehicleBandType({ commit }) {
      await axios
        .post(algorithm.standard1400, {
          dictType: ['vehicleBrandType'],
          type: 'vehicle',
        })
        .then((res) => {
          commit('setvehicleBandType', res.data.data);
        });
    },
    // 车辆种类字典表 -- 1400标准字典管理
    async getvehicleClassType({ commit }) {
      await axios
        .post(algorithm.standard1400, {
          dictType: ['vehicleClassType'],
          type: 'vehicle',
        })
        .then((res) => {
          commit('setvehicleClassType', res.data.data);
        });
    },
    // 车牌类型 -- 1400标准字典管理
    async getplateClassType({ commit }) {
      await axios
        .post(algorithm.standard1400, {
          dictType: ['plateClassType'],
          type: 'vehicle',
        })
        .then((res) => {
          commit('setplateClassType', res.data.data);
        });
    },
    // 车辆属性
    async getVehicleProperty({ commit }) {
      try {
        const params = {
          typekey: 'vehicle_property',
        };
        let { data } = await axios.get(user.queryByTypeKey, { params });
        commit('setVehicleProperty', data.data || []);
      } catch (error) {
        console.log(error);
      }
    },

    async getQueryListTransfer({ commit, state }) {
      try {
        if (state.algorithmTransfer.length > 0) return;
        let {
          data: { data },
        } = await axios.post(algorithm.queryListTransfer, { 'vendor': 'UNIVIEW' });
        commit('setAlgorithmTransfer', data);
      } catch (e) {
        console.log(e);
      }
    },
    //查询多算法厂商配置分页列表
    async getAlgorithmList({ commit, state }) {
      try {
        if (state.algorithmList.length > 0) return;
        const params = {
          algorithmType: '7,8',
          pageNumber: 1,
          pageSize: 20,
        };
        const {
          data: { data },
        } = await axios.post(algorithm.getAlgorithmList, params);
        commit('setAlgorithmList', data.entities || []);
      } catch (e) {
        console.log(e);
      }
    },
  },
};
