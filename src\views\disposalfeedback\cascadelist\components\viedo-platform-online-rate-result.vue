<template>
  <online-details v-model="onlineDetailShow" :get-echarts="getEcharts">
    <template #header>
      <div class="detail-title">
        <Button type="primary" :loading="exportLoading" @click="onExport">
          <i class="icon-font icon-daochu"></i>
          <span class="ml-sm">导出</span>
        </Button>
      </div>
      <icon-statics :icon-list="iconList"> </icon-statics>
    </template>
  </online-details>
</template>
<script>
import cascadeListMixin from '@/views/disposalfeedback/cascadelist/mixins/cascadeListMixin.js';
import { iconStaticsList } from '@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/util/enum/ReviewParticular.js';

export default {
  name: 'viedo_platform_online_rate_result',
  components: {
    OnlineDetails:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/components/online-details.vue')
        .default,
    IconStatics: require('@/components/icon-statics.vue').default,
  },
  props: {
    value: {},
    activeItem: {},
  },
  mixins: [cascadeListMixin],
  data() {
    return {
      detailData: {},
      onlineDetailShow: false,
      iconList: iconStaticsList,
      exportLoading: false,
    };
  },
  watch: {
    onlineDetailShow(val) {
      this.$emit('input', val);
    },
    value: {
      handler(val) {
        this.onlineDetailShow = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    async getEcharts() {
      try {
        // 获取统计
        let paramsObj = {
          statisticType: 'ORG',
          cascadeId: this.activeItem.id,
          ...this.activeItem,
        };
        const res = await this.mixinGetStatInfo(paramsObj);
        let statInfo = res.data.data || {};
        // 设备模式统计
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            statInfo.qualified === '1' ? (item.tailIcon = 'icon-dabiao') : (item.tailIcon = ' icon-budabiao');
          }
          item.count = statInfo[item.fileName] || 0;
        });
        // 获取详情
        let dataObj = {
          statisticType: 'ORG',
          cascadeId: this.activeItem.id,
          pageNum: 1,
          pageSize: 10000,
          ...this.activeItem,
        };
        const { data } = await this.mixinGetFirstModeData(dataObj);
        this.detailData = data.data.entities;
        const colors = [$cssVar('--color-failed'), $cssVar('--color-success'), '#2B84E2'];
        const state = ['离线', '在线', '报备'];
        const params = {
          colors: colors,
          state: state,
          yAxis: this.detailData.map((row) => row.checkTimeD),
          categoryData: this.detailData.map((row) => {
            return {
              value: row.duration,
              rowInfo: { ...row }, // 用于处理 其他业务逻辑
              textStyle: {
                /**
                 * 如果离线时间只有一条数据
                 * 则判断state： 2报备，1在线，0离线
                 * 如果有多条记录则显示离线时间
                 *  */
                color: () => {
                  if (row.timerAxis.length === 1) {
                    if (row.timerAxis[0].state === 2) {
                      return '#2B84E2';
                    } else if (row.timerAxis[0].state === 1) {
                      return '#0E8F0E';
                    } else {
                      return '#BC3C19';
                    }
                  } else {
                    return '#BC3C19';
                  }
                },
              },
            };
          }),
          data: this.dealData(colors, state),
        };
        return params;
      } catch (err) {
        console.log(err);
      }
    },
    dealData(colors, state) {
      let timeData = [];
      this.detailData.forEach((row, index) => {
        row.timerAxis.forEach((rw) => {
          timeData.push({
            itemStyle: { color: colors[rw.state] },
            name: state[rw.state],
            /**
             * Echarts的x轴只能显示 xxxx-xx-xx xx:xx:xx
             * 这里y轴作为了日期所以年月只需要写死即可
             * 0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
             *  */
            value: [index, `2020-01-01 ${rw.t1}`, `2020-01-01 ${rw.t2}`, rw.state === 0],
          });
        });
      });
      return timeData;
    },
    async onExport() {
      let { id, batchId, indexId, orgCode } = this.activeItem;
      let params = {
        statisticType: 'ORG',
        cascadeId: id,
        indexId,
        batchId,
        orgRegionCode: orgCode,
      };
      this.mixinGetExport(params);
    },
  },
};
</script>
<style scoped lang="less"></style>
