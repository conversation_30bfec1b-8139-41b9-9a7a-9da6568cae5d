// 引入mockjs
import Mock from 'mockjs'
// 分页查询标签
Mock.mock('/label-manager-service/label/page', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'total': 8,
      'entities|8': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'color': '@color',
          'level': '2',
          'createTime': '@date',
          'property': '1'
        }
      ]
    }
})

// 分页查询标签
Mock.mock('/label-manager-service/label/page', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'total': 8,
      'entities|8': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'color': '@color',
          'level': '2',
          'createTime': '@date',
          'property': '1'
        }
      ]
    }
})
