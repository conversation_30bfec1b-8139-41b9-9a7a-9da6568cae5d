<template>
  <div class="assessmen-task auto-fill">
    <div v-if="!componentName" class="evaluat_div">
      <div class="right-content auto-fill">
        <div class="search-wrapper">
          <ui-label label="通报标题" :width="70" class="fl">
            <Input class="width-md" v-model="searchData.taskName" placeholder="请输入公告名称"></Input>
          </ui-label>
          <ui-label :width="0" label=" " class="fl ml-lg">
            <Button type="primary" class="mr-sm fl" @click="search"> 查询 </Button>
            <Button class="mr-lg fl" @click="resetSearchDataMx()"> 重置 </Button>
          </ui-label>
        </div>
        <div class="table-box auto-fill">
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
            <template #notificationType="{ row }">
              <span>
                {{ row.notificationType === '1' ? '月考核通报' : '' }}
              </span>
            </template>
            <template #releaseStatus="{ row }">
              <span
                class="check-statu"
                :class="[
                  row.releaseStatus == 0 ? 'bg-offline' : '',
                  row.releaseStatus == 1 ? 'bg-warning' : '',
                  row.releaseStatus == 2 ? 'bg-success' : '',
                  row.releaseStatus == 3 ? 'bg-failed' : '',
                  row.releaseStatus == 4 ? 'bg-failed' : '',
                ]"
              >
                {{ row.releaseStatus == 0 ? '未发布' : '已发布' }}
              </span>
            </template>
            <template #option="{ row }">
              <ui-btn-tip
                :disabled="row.releaseStatus === '1'"
                class="color_qualified mr-sm"
                icon="icon-fabu-01"
                :styles="{ 'font-size': '20px' }"
                content="发布"
                @click.native="releaseFn(row)"
              ></ui-btn-tip>
              <create-tabs
                v-if="row.releaseStatus == '1'"
                :componentName="themData.componentName"
                :tabs-text="themData.text"
                @selectModule="selectModule"
                :tabs-query="{
                  id: row.id,
                  year: row.year,
                  month: row.month,
                  releaseTime: row.releaseTime,
                }"
                class="inline"
              >
                <ui-btn-tip
                  :disabled="row.releaseStatus === '0'"
                  :styles="{ color: '#438CFF' }"
                  content="查看结果"
                  icon="icon-chakanxiangqing"
                ></ui-btn-tip>
              </create-tabs>
              <ui-btn-tip
                v-else
                :disabled="true"
                :styles="{ color: '#438CFF' }"
                content="查看结果"
                icon="icon-chakanxiangqing"
              ></ui-btn-tip>
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page menu-content-background"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        ></ui-page>
      </div>
      <!-- 新增/编辑 -->
      <addor-edit-modal
        ref="addOrUpdate"
        v-if="addOrUpdateVisible"
        :moduleList="moduleList"
        :title="title"
        @update="update"
      ></addor-edit-modal>
    </div>
    <keep-alive v-else>
      <component :is="componentName" @changeComponent="changeComponentHandle"></component>
    </keep-alive>
  </div>
</template>
<script>
import notice from '@/config/api/notice';
import dealWatch from '@/mixins/deal-watch';
import governanceevaluation from '@/config/api/governanceevaluation.js';
export default {
  name: 'notice',
  mixins: [dealWatch],
  data() {
    return {
      title: '新增考核任务',
      addOrUpdateVisible: false,
      componentName: null,
      id: 1,
      month: null,
      modifyTime: null,
      themData: {
        componentName: 'AssessmentResult', // 需要跳转的组件名
        text: '考核结果', // 跳转页面标题
        title: '考核结果',
        type: 'view',
      },
      loading: false,
      switchLoading: false,
      tableColumns: [
        {
          type: 'index',
          tooltip: true,
          width: 50,
          title: '序号',
          align: 'center',
        },
        {
          title: '通报标题',
          key: 'notificationName',
          tooltip: true,
          align: 'left',
          minWidth: 200,
        },
        {
          title: '通报类型',
          slot: 'notificationType',
          tooltip: true,
          align: 'left',
        },
        {
          title: '发布状态',
          slot: 'releaseStatus',
          tooltip: true,
          align: 'left',
        },
        {
          title: '发布人',
          key: 'creator',
          tooltip: true,
          align: 'left',
        },
        {
          title: '发布时间',
          key: 'modifyTime',
          tooltip: true,
          align: 'left',
          width: 160,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          tooltip: true,
          width: 120,
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      resultList: [
        { name: '未开始', value: '0', index: 0 },
        { name: '进行中', value: '1', index: 1 },
        { name: '已完成', value: '2', index: 2 },
        { name: '已暂停', value: '3', index: 3 },
        { name: '任务异常', value: '4', index: 4 },
      ],
      tableData: [],
      row: null,
      searchData: {
        taskName: '',
        taskRunState: '', //考核结果
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      moduleList: [], //考核方案
      editList: {}, //任务详情
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
    };
  },
  created() {
    this.getParams();
    this.getTableList();
    this.getAllExamScheme();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  methods: {
    async getTableList() {
      this.loading = true;
      // let data = {
      //   taskName: this.searchData.taskName,
      //   taskRunState: this.searchData.taskRunState,
      //   pageNumber: this.searchData.pageNum,
      //   pageSize: this.searchData.pageSize,
      //   params: {
      //     pageNumber: this.searchData.pageNum,
      //     pageSize: this.searchData.pageSize,
      //   },
      // }
      try {
        let res = await this.$http.post(notice.notificationList, {
          taskName: this.searchData.taskName,
          taskRunState: this.searchData.taskRunState,
          pageNumber: this.searchData.pageNum,
          pageSize: this.searchData.pageSize,
          params: {
            pageNumber: this.searchData.pageNum,
            pageSize: this.searchData.pageSize,
          },
        });
        // const res = {"code":200,"msg":"成功","data":{"pageNumber":1,"pageSize":100,"entities":[{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2288,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_槐荫区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_槐荫区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370104"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2289,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_市辖区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_市辖区异常问题明细.zip","exceptionDownSize":"18.0K","orgCode":null,"regionCode":"370101"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2291,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_历城区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_历城区异常问题明细.zip","exceptionDownSize":"18.1K","orgCode":null,"regionCode":"370112"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2292,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_市中区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_市中区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370103"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2293,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_天桥区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_天桥区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370105"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2294,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_长清区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_长清区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370113"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2295,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_章丘区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_章丘区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370114"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2296,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_济阳区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_济阳区异常问题明细.zip","exceptionDownSize":"18.1K","orgCode":null,"regionCode":"370115"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2297,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_莱芜区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_莱芜区异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370116"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2298,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_钢城区异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_钢城区异常问题明细.zip","exceptionDownSize":"18.1K","orgCode":null,"regionCode":"370117"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2299,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_平阴县异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_平阴县异常问题明细.zip","exceptionDownSize":"18.1K","orgCode":null,"regionCode":"370124"},{"creator":null,"createTime":"2022-03-09 17:28:38","modifier":null,"modifyTime":"2022-03-09 17:28:38","id":2300,"notificationInfoId":125,"exceptionDownName":"2022年02月考核_商河县异常问题明细.zip","exceptionDownUrl":"http://*************:9002/qsdi/notification/2022年02月考核_商河县异常问题明细.zip","exceptionDownSize":"18.2K","orgCode":null,"regionCode":"370126"}],"count":1,"total":12,"firstPage":true,"lastPage":true}}
        this.tableData = res.data.data.entities ? res.data.data.entities : [];
        this.searchData.totalCount = res.data.data.total ? res.data.data.total : 0;
      } catch (err) {
        console.log(err);
      }
      this.loading = false;
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.getTableList();
    },
    handleCheckStatus(row) {
      let flag = {
        0: '未开始',
        1: '运行中',
        2: '已完成',
        3: '失败',
        4: '任务异常',
      };
      return flag[row];
    },
    resultShow(row) {
      // this.row = row
      if (row.releaseStatus === '0') return;
      this.id = row.id;
      this.months = row.month;
      this.modifyTimes = row.modifyTime;
      this.componentName = 'AssessmentResult';
    },
    /**
     * 发布  0:未发布, 1: 已发布
     */
    async releaseFn(row) {
      if (row.releaseStatus === '1') return;
      await this.$http.put(notice.notificationUpdate, {
        id: row.id,
        releaseStatus: 1,
      });
      this.$Message.success('发布成功');
      this.getTableList();
    },
    // 新增和编辑弹窗
    async operationTask(action, row) {
      let list = [];
      if (action === 'edit') {
        this.title = '编辑考核任务';
        await this.getTaskEdit(row);
        list = {
          ...this.editList,
        };
      } else {
        this.title = '新增考核任务';
      }
      this.addOrUpdateVisible = true;
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(list);
      });
    },
    // 编辑回显
    async getTaskEdit(row) {
      let data = {
        id: row.id,
        schemeId: row.schemeId,
        orgCode: row.orgCode,
      };
      try {
        let res = await this.$http.post(governanceevaluation.queryEvaluationTaskById, data);
        this.editList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 立即执行
    execute(row) {
      this.$UiConfirm({
        content: `您确认要立即执行此任务嘛?`,
        title: '警告',
      })
        .then(() => {
          this.executeNowInit(row);
        })
        .catch(() => {});
    },
    async executeNowInit(val) {
      let params = {
        jobId: val.jobId,
      };
      try {
        let res = await this.$http.get(governanceevaluation.executeNow, {
          params,
        });
        this.$Message.success(res.data.msg);
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    // 删除
    deleteTask(row) {
      this.$UiConfirm({
        content: `您确认要删除此任务嘛?`,
        title: '警告',
      })
        .then(() => {
          this.deleteInit(row);
        })
        .catch(() => {});
    },
    async deleteInit(val) {
      let params = {
        jobId: val.jobId,
        taskId: val.id,
      };
      try {
        let res = await this.$http.get(governanceevaluation.deleteTask, {
          params,
        });
        this.$Message.success(res.data.msg);
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    // 考核方案列表
    async getAllExamScheme() {
      try {
        let res = await this.$http.post(governanceevaluation.getAllExamScheme);
        this.moduleList = res.data.data ? res.data.data : [];
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableList();
    },
    update() {
      this.addOrUpdateVisible = false;
      this.search();
    },
    // 重置
    resetSearchDataMx() {
      this.searchData.taskName = '';
      this.searchData.taskRunState = '';
      this.search();
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'AssessmentResult' ? 0 : 1;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    changeComponentHandle(name) {
      this.componentName = name;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    AddorEditModal: require('./components/addor-edit-modal.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    // AssessmentResult: require('./assessment-result.vue').default,
    detectionToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    FaceCarOnlineNum: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-num.vue')
      .default,
    FaceCarTopOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-top-online.vue')
      .default,
    FaceCarReportRate:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-report-rate.vue').default,
    VideoHistoryOnline:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-history-online.vue').default,
    OnOffLineDetail: require('@/views/governanceevaluation/evaluationoverview/components/video/on-off-line-detail.vue')
      .default,
    AssessmentResult: require('./assessment-result.vue').default,
    overviewEvaluation: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.assessmen-task {
  height: 100%;
  .color_qualified {
    color: #13b13d;
  }
  .color_unqualified {
    color: #e44f22;
  }
  .color_cannot {
    color: var(--color-warning);
  }
  .deleteicon {
    color: rgba(255, 255, 255, 0.25);
    cursor: no-drop;
  }
  .evaluat_div {
    height: 100%;
    .right-content {
      width: 100%;
      height: 100%;
      background: var(--bg-content);
      @{_deep}.search-wrapper {
        overflow: hidden;
        padding: 0 20px !important;
        margin: 20px 0px;
        .ui-label {
          line-height: 30px;
        }
        .ivu-select {
          height: 30px;
        }
      }
      .table-box {
        padding: 0 20px 20px 20px;
        position: relative;
        .sucess {
          color: @color-success;
        }
        .error {
          color: @color-failed;
        }
        .no-data {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        @{_deep} .ivu-table-tbody {
          td {
            padding: 10px 0 10px 0;
          }
        }
        @{_deep} .ivu-table-body {
          td {
            padding: 10px 0 10px 0;
          }
        }
      }
    }
  }
}
.check-statu {
  min-width: 70px;
  height: 23px;
  line-height: 23px;
  text-align: center;
  display: inline-block;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
}

.iconfont {
  font-size: 12px !important;
}
/deep/ .icon-zu2540:before {
  font-size: 12px !important;
}
</style>
