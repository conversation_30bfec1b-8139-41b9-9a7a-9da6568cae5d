<template>
  <div class="table-content">
    <div class="table-list" v-for="(item, index) in list" :key="index">
      <div class="content flex-b">
        <div class="left-content">
          <span class="mr-30" v-for="(e, key) in item" :key="key">
            <template v-if="key != 'id'">
              <label class="text-secondary">{{ key }}：</label>
              <!-- 适配【全景智搜】搜索结果高亮，所以用v-html插入数据 -->
              <span class="title-color font-bold" v-html="fmtValue(e)"></span>
            </template>
          </span>
        </div>
        <p>
          <!-- <ui-btn-tip content="收藏" icon="icon-shoucang mr-10 color-warning" /> -->
          <ui-btn-tip
            content="详情"
            :transfer="true"
            icon="icon-xiangqing mr-10 color-primary"
            @click.native="checkDetail(item)"
          />
          <!-- <ui-btn-tip content="布控" icon="icon-dunpai color-error" transfer /> -->
        </p>
      </div>
    </div>
    <ui-empty v-if="list.length === 0"></ui-empty>
    <ui-loading v-if="loading"></ui-loading>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    checkDetail(item) {
      this.$emit("checkDetail", item);
    },
    // 格式化数据
    fmtValue(val) {
      return Array.isArray(val) ? val.join(",") : val;
    },
  },
};
</script>
<style lang="less" scoped>
.mr-30:first-child {
  margin-right: 0 !important;
}
.table-content {
  font-size: 14px;
  height: 100%;
  .table-list {
    width: 100%;
    border: 1px solid #d3d7de;
    background: #f9f9f9;
    margin-bottom: 10px;
    .content {
      padding: 16px 20px;
      .left-content {
        width: 96%;
      }
      .font-bold {
        font-weight: bold;
      }
    }
  }
}
</style>
