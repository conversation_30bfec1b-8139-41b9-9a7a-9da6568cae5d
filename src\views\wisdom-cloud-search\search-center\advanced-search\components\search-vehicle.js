export const vehicleClassTypeList = [ //车身类型
  { dataValue: '客车', value: 'vehicleClassType_k', type: 'vehicleClassTypeKList' },
  { dataValue: '货车', value: 'vehicleClassType_h', type: 'vehicleClassTypeHList' },
  { dataValue: '牵引车', value: 'vehicleClassType_q', type: 'vehicleClassTypeQList' },
  { dataValue: '专项作业车', value: 'vehicleClassType_z', type: 'vehicleClassTypeZList' },
  { dataValue: '电车', value: 'vehicleClassType_d', type: 'vehicleClassTypeDList' },
  { dataValue: '摩托车', value: 'vehicleClassType_m', type: 'vehicleClassTypeMList' },
  { dataValue: '三轮汽车', value: 'vehicleClassType_n', type: 'vehicleClassTypeNList' },
  { dataValue: '拖拉机', value: 'vehicleClassType_t', type: 'vehicleClassTypeTList' },
  { dataValue: '轮式机械', value: 'vehicleClassType_j', type: 'vehicleClassTypeJList' },
  { dataValue: '全挂车', value: 'vehicleClassType_g', type: 'vehicleClassTypeGList' },
  { dataValue: '半挂车', value: 'vehicleClassType_b', type: 'vehicleClassTypeBList' },
  { dataValue: '其他', value: 'vehicleClassType_x', type: 'vehicleClassTypeXList' },
]
        
export const captureTimePeriod = [ //抓拍时段
  { name: '近一天', value: '1' },
  { name: '近三天', value: '2' },
  { name: '近一周', value: '3' },
  { name: '自定义', value: '4' }
]
export const angleList = [ // 角度
  { name: '部分遮挡', value: '1' },
  { name: '全部遮挡', value: '2' },
  { name: '无车牌', value: '3' },
  { name: '未知', value: '4' }
]
export const carTopList = [ // 车顶物件           
  { name: '背景', value: '1' },
  { name: '天窗', value: '2' },
  { name: '天线', value: '3' },
  { name: '行李架', value: '4' }
]
export const yearCheckList = [ // 年检标           
  { name: '0', value: '1' },
  { name: '1', value: '2' },
  { name: '2', value: '3' },
  { name: '3或3个以上', value: '4' }
]
export const markerList = [ //    标志物                                
  { name: '标志物', value: '1' },
  { name: '摆件', value: '2' },
  { name: '挂件', value: '3' },
  { name: '纸巾盒', value: '4' },
  { name: '主驾遮阳板', value: '5' },
  { name: '副驾遮阳板', value: '6' },
  { name: '背景', value: '7' },
  { name: '车身红绳', value: '8' },
  { name: '其他', value: '9' }
]
export const shelterList = [ //遮挡
  { name: '部分遮挡', value: '1' },
  { name: '全部遮挡', value: '2' },
  { name: '无车牌', value: '3' },
  { name: '未知', value: '4' }
]
export const coDriverList = [ //副驾有人
    { dataValue: '是', dataKey: '1' },
    { dataValue: '否', dataKey: '0' },
]
export const sunVisorList = [ //遮阳板                        
  { name: '夜间主副驾驶均未开启', value: '1' },
  { name: '夜间主驾开启', value: '2' },
  { name: '夜间副驾开启', value: '3' },
  { name: '夜间主副驾均开启', value: '4' },
  { name: '白天主副驾驶均未开启', value: '5' },
  { name: '白天主驾开启', value: '6' },
  { name: '白天副驾开启', value: '7' },
  { name: '白天主副驾均开启', value: '8' },
]
export const faceList = [ // 面部遮挡              
  { name: '面部遮挡', value: '1' },
  { name: '车顶面部遮挡', value: '2' },
  { name: '墨镜面部遮挡', value: '3' },
  { name: '遮阳板面部遮挡', value: '4' },
  { name: '其他', value: '5' },
]
export const vehiclePlateTypes = [ //车牌类型
  { name: '大型汽车号牌', value: '1' },
  { name: '小型汽车号牌', value: '2' },
  { name: '使馆汽车号牌', value: '3' },
  { name: '领馆汽车号牌', value: '4' },
  { name: '境外汽车号牌', value: '5' },
  { name: '外籍汽车号牌', value: '6' },
  { name: '两三轮摩托车号牌', value: '7' },
  { name: '轻便摩托车号牌', value: '8' },
  { name: '使馆摩托车号牌', value: '9' },
  { name: '领馆摩托车号牌', value: '10' },
  { name: '外籍摩托车号牌', value: '11' }
]
export const vehiclePlateColors = [// 车牌颜色
  { name: '绿色', value: '1' },
  { name: '黄绿', value: '2' },
  { name: '蓝牌', value: '3' },
  { name: '黄牌', value: '4' },
  { name: '白牌', value: '5' },
  { name: '黑牌', value: '6' },
  { name: '渐绿牌', value: '7' },
  { name: '其他', value: '8' }
]
export const vehicleBodyTypes = [
  { name: '轿车', value: '1' },
  { name: '面包车', value: '2' },
  { name: '皮卡', value: '3' },
  { name: '商务车', value: '4' },
  { name: '越野车', value: '5' },
  { name: '中型普通客车', value: '6' },
  { name: '大型普通客车', value: '7' },
  { name: '小型普通客车', value: '8' },
  { name: '中型普通货车', value: '9' },
  { name: '重型普通货车', value: '10' },
  { name: '轻型普通货车', value: '11' },
  { name: '微型普通货车', value: '12' },
  { name: '公交车', value: '13' },
  { name: '校车', value: '14' },
  { name: '大型货车', value: '15' },
  { name: '三轮车', value: '16' }
]
export const anchorFrontList = [  // 锚点列表
  { name: '车牌', dataId: 'vehicleCP', class: 'vehicle-cp' },
  { name: '品牌', dataId: 'vehiclePP', class: 'vehicle-pp' },
  { name: '车身喷字', dataId: 'vehicleCSPZ', class: 'vehicle-cspz' },
  { name: '纸巾盒', dataId: 'marker', class: 'vehicle-zjh' },
  { name: '摆件', dataId: 'marker', class: 'vehicle-bj' },
  { name: '挂件', dataId: 'marker', class: 'vehicle-gj' },
  { name: '天窗', dataId: 'markerTop', class: 'vehicle-tc' },
  { name: '遮阳板', dataId: 'vehicleZYB', class: 'vehicle-zyb' },
  { name: '红绳', dataId: 'marker', class: 'vehicle-hs' },
  { name: '车身颜色', dataId: 'vehicleCSYS', class: 'vehicle-csys' },
]

export const anchorAfterList = [  // 锚点列表
  { name: '车身喷字', dataId: 'vehicleCSPZ', class: 'vehicle-back-cspz' },
  { name: '车牌', dataId: 'vehicleCP', class: 'vehicle-back-cp' },
  { name: '品牌', dataId: 'vehiclePP', class: 'vehicle-back-pp' },
  { name: '车后盖', dataId: 'vehicleCHG', class: 'vehicle-back-chg' },
  { name: '天线', dataId: 'markerTop', class: 'vehicle-back-tx' },
  { name: '行李架', dataId: 'markerTop', class: 'vehicle-back-xlj' },
  { name: '红绳', dataId: 'marker', class: 'vehicle-back-hs' },
]

export const vehicleBodyColors = [
  { name: '', value: '1' },
  { name: '', value: '2' },
  { name: '', value: '3' },
  { name: '', value: '4' },
  { name: '', value: '5' },
  { name: '', value: '6' },
  { name: '', value: '7' },
  { name: '', value: '8' },
  { name: '', value: '9' },
  { name: '', value: '10' },
  { name: '', value: '11' },
  { name: '', value: '12' },
  { name: '', value: '13' },
  { name: '', value: '14' }
]
export const colors = [
  {
    dataKey: '1',
    borderColor: '#fff',
    style: {
      background: '#67D28D',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  },
  {
    dataKey: '2',
    borderColor: '#fff',
    class: 'yellow-plate',
    style: {
      background: '#67D28D',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  },
  {
    dataKey: '3',
    borderColor: '#fff',
    style: {
      background: '#2379F9',
      color: '#fff'
    }
  },
  {
    dataKey: '4',
    borderColor: '#fff',
    style: {
      background: '#FDEE38',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  },
  {
    dataKey: '5',
    borderColor: '#D3D7DE',
    style: {
      background: '#ffffff',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  },
  {
    dataKey: '6',
    borderColor: '#fff',
    style: {
      background: '#000',
      color: '#fff'
    }
  },
  {
    dataKey: '7',
    borderColor: '#fff',
    style: {
      background: 'linear-gradient(180deg, #CFFFE4 0%, #67D28D 100%)',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  },
  {
    dataKey: '8',
    borderColor: '#fff',
    style: {
      background: '#D9D9D9',
      color: 'rgba(0, 0, 0, 0.8)'
    }
  }
]
export const bodyColors = [
  {
    dataKey: '1',
    borderColor: '#D3D7DE',
    style: {
      background: '#FFFFFF'
    }
  },
  {
    dataKey: '2',
    borderColor: '#FFFFFF',
    style: {
      background: '#D9D9D9'
    }
  },
  {
    dataKey: '3',
    borderColor: '#FFFFFF',
    style: {
      background: '#000000'
    }
  },
  {
    dataKey: '4',
    borderColor: '#FFFFFF',
    style: {
      background: '#EB4B4B'
    }
  },
  {
    dataKey: '5',
    borderColor: '#FFFFFF',
    style: {
      background: '#FDEE38'
    }
  },
  {
    dataKey: '6',
    borderColor: '#FFFFFF',
    style: {
      background: '#2379F9'
    }
  },
  {
    dataKey: '7',
    borderColor: '#FFFFFF',
    style: {
      background: '#67D28D'
    }
  },
  {
    dataKey: '8',
    borderColor: '#FFFFFF',
    style: {
      background: '#F29F4C'
    }
  },
  {
    dataKey: '9',
    borderColor: '#FFFFFF',
    style: {
      background: '#7FF2FF'
    }
  },
  {
    dataKey: '10',
    borderColor: '#FFFFFF',
    style: {
      background: '#A627A4'
    }
  },
  {
    dataKey: '11',
    borderColor: '#FFFFFF',
    style: {
      background: '#E8A1B7'
    }
  },
  {
    dataKey: '12',
    borderColor: '#fff',
    class: 'yellow-body',
    style: {
      background: '#67D28D'
    }
  },
  {
    dataKey: '13',
    borderColor: '#FFFFFF',
    style: {
      background: '#C49F6C'
    }
  },
  {
    dataKey: '14',
    borderColor: '#FFFFFF',
    style: {
      background: '#BABABA'
    }
  }
]
export const specialVehicles = [
  { name: '危化品', value: '1' },
  { name: '黄标车', value: '2' },
  { name: '渣土车', value: '3' },
  { name: '邮政车', value: '4' },
  { name: '环卫、园林车、市政工程', value: '5' },
  { name: '救护车', value: '6' },
  { name: '工程救险车', value: '7' },
  { name: '警车', value: '8' },
  { name: '安保车 ', value: '9' },
  { name: '消防车', value: '10' },
  { name: '其他车辆', value: '11' }
]
