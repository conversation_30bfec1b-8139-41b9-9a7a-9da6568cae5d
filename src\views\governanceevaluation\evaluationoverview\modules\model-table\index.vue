<template>
  <div class="model-table height-full auto-fill">
    <div class="model-table-title">
      <div v-if="isDeepActiveItem.isFirstLevel">
        <ui-label class="inline" label="评测时间">
          <template>
            <!-- 选择季度时间组件 -->
            <quarter-picker
              v-if="searchData.type === 4"
              :quarter-year="searchData.year"
              :quarter-value="searchData.quarter"
              @quarterChange="quarterChange"
            ></quarter-picker>
            <DatePicker
              v-else
              v-model="monthDate"
              :type="dateType"
              :format="dateFormat"
              placeholder="请选择评测时间"
              @on-change="changeTime"
            ></DatePicker>
          </template>
        </ui-label>
        <ui-label class="inline ml-lg" label="数据选择">
          <Select
            class="input-width"
            v-model="searchData.indexModule"
            placeholder="请选择指标类型"
            clearable
            multiple
            @on-change="selectIndexModule"
          >
            <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id">
              {{ item.title }}
            </Option>
          </Select>
        </ui-label>
        <RadioGroup v-model="searchData.type" @on-change="changeRadio">
          <Radio class="ml-lg" :label="2"> 最新值</Radio>
          <Radio class="ml-lg" :label="3"> 每日结果</Radio>
          <Radio class="ml-lg" :label="1"> 月平均值</Radio>
          <Radio class="ml-lg" :label="4"> 季度平均</Radio>
        </RadioGroup>
        <Button type="primary" class="ml-lg" @click="startSearch" :loading="buttonLoading">查询</Button>
        <Button class="ml-sm mr-lg" @click="resetSearchData">重置</Button>
      </div>
      <div>
        <!-- <ui-btn-tip icon="icon-wenhao" class="vt-middle" @click.native="hintModalShow">
          <template #content>
            <p>评价指标说明</p>
            <p @click="hintModalShow" class="click-hint">立即点开查看</p>
          </template>
        </ui-btn-tip>
        <span class="vt-middle information-index mr-sm" @click="hintModalShow">指标信息</span> -->
        <Button type="primary" class="btn_search" :loading="isDisabled" @click="getExport">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs">导出</span>
        </Button>
      </div>
    </div>
    <ui-table
      ref="assessmentTable"
      class="model-table-wrapper auto-fill mt-md"
      full-border
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template v-for="item in slotsList" #[item]="{ row }">
        <div :key="row[item]?.title">
          <!-- 可以点击，查看指标详情 -->
          <template v-if="!['ORG_REGION_CODE', 'DAY'].includes(item) && row[item].clickable">
            <create-tabs
              v-if="!isExistIndex(item)"
              class="inline"
              :class="[qualityEnum[row[item].type], row[item].clickable ? 'can-click' : '']"
              :componentName="resultData.componentName"
              :tabs-text="resultData.text"
              @selectModule="() => $emit('selectModule')"
              :tabs-query="{
                indexType: item,
                indexId: row[item].indexId,
                code: row[item].regionCode,
                access: 'TASK_RESULT',
                batchId: row[item].batchId,
              }"
            >
              <span class="can-click" :class="row.className">{{ filterNum(row[item]?.title) }} </span>
            </create-tabs>
            <span
              v-else
              :class="[qualityEnum[row[item].type], row[item].clickable ? 'can-click' : '']"
              @click="handleClickJump(row[item])"
            >
              {{ filterNum(row[item]?.title) }}
            </span>
          </template>

          <!-- 行政区划-->
          <span
            :class="{
              'can-click': row[item].clickable,
              'region-color': item === 'ORG_REGION_CODE',
              'is-parent-color': row[item].parent,
            }"
            v-else-if="item === 'ORG_REGION_CODE' || (item === 'DAY' && row[item].clickable)"
            @click="clickOrgRegion(row, item)"
          >
            {{ row[item]?.title ?? '--' }}
          </span>
          <!-- 不能点击 -->
          <span :class="[qualityEnum[row[item].type]]" v-else>
            {{ item === 'DAY' ? row[item]?.title : filterNum(row[item]?.title) }}
          </span>
        </div>
      </template>
    </ui-table>
    <hint-modal-show ref="hintModalShow" v-model="hintShow" title="评价指标说明"></hint-modal-show>
    <!-- <export-data ref="exportModuleRef" @exportAdd="exportAdd"></export-data> -->
    <common-model ref="model" v-model="visible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>

<script>
import { firstLevelTableColumn, secondLevelTableColumn, qualityEnum } from './utils/enum.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import downLoadTips from '@/mixins/download-tips';
export default {
  props: {
    statisticType: {
      default: 'REGION',
    },
    taskSchemeId: {
      default: '',
    },
    // [报表模式]判断是第一层 or 第二层
    isDeepActiveItem: {
      default: () => {
        return {
          id: null,
          isFirstLevel: true,
        };
      },
    },
  },
  mixins: [evaluationoResultMixin, downLoadTips],
  data() {
    return {
      buttonLoading: false,
      hintShow: false,
      isDisabled: false,
      taskList: [],
      slotsList: [],
      qualityEnum: Object.freeze(qualityEnum),
      resultData: {
        componentName: 'overviewEvaluation', // 需要跳转的组件名
        text: '评测详情', // 跳转页面标题
        title: '评测详情',
        type: 'view',
      },
      tableColumns: [],
      tableData: [],
      loading: false,
      monthDate: '',
      dateType: '',
      dateFormat: '',
      searchData: {
        indexModule: [],
        year: '',
        month: '',
        type: 2,
        day: null,
        quarter: null,
      },
      sortValue: {},
      currentRow: {},
      visible: false,
    };
  },
  created() {
    this.initSearchData();
  },
  methods: {
    changeRadio() {
      this.initSearchData();
      this.startSearch();
    },
    resetSearchData() {
      this.searchData.indexModule = [];
      this.searchData.type = 2;
      this.initSearchData();
      this.getIndexOverviewReportModeDataByAvg();
    },
    filterNum(num) {
      if (!num) return '--';
      if (num === '-') return '--';
      return `${num}%`;
    },
    initSearchData() {
      let date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      this.monthDate = `${year}-${month}`;
      this.searchData.year = year;
      this.searchData.month = month;


      this.searchData.day = null;
      this.searchData.quarter = null;
      switch (this.searchData.type) {
        case 3:
          this.dateType = 'date';
          this.dateFormat = 'yyyy-MM-dd';
          this.searchData.day = day;
          this.monthDate = `${year}-${month}-${day}`;
          break;
        case 4:
          this.searchData.quarter = this.getQuarter(month);
          this.searchData.month = null;
          break;
        default:
          this.dateType = 'month';
          this.dateFormat = 'yyyy-MM';
          break;
      }
      //取消默认值 @济南现场
    },
    // 获取当前月份 处于 第几季度
    getQuarter(month) {
      let num = 1;
      if (month <= 3) {
        num = 1;
      } else if (month > 3 && month <= 6) {
        num = 2;
      } else if (month > 6 && month <= 9) {
        num = 3;
      } else if (month > 9 && month <= 12) {
        num = 4;
      }
      return num;
    },
    startSearch() {
      this.buttonLoading = true;
      this.getIndexOverviewReportModeDataByAvg();
    },
    changeTime(date) {
      let newDate = date.split('-');
      this.searchData.year = newDate[0];
      this.searchData.month = newDate[1];
      this.searchData.day = newDate[2] ? Number(newDate[2]) : null;
    },
    selectIndexModule() {},
    getSearchParams() {
      let { indexModule, year, month, day, type, quarter } = this.searchData;
      let params = {
        taskSchemeId: this.taskSchemeId,
        displayType: this.statisticType,
        modules: indexModule,
        year: year,
        month: month,
        day: day,
        quarter: quarter,
        type: type,
      };

      let { isFirstLevel, id, parentId, level } = this.isDeepActiveItem;
      !isFirstLevel ? (params.orgRegionCode = id) : null;

      // 季度评价 --- 是支持查看    省 / 具体月份 / 日   三层的数据的，所以要特殊处理下
      if (!isFirstLevel && parentId && type === 4) {
        if (level === 2) {
          params.orgRegionCode = id;
        } else if (level === 3) {
          params.orgRegionCode = parentId;
          params.month = id.split('-')[1];
          params.type = 1;
        }
      }
      return params;
    },
    async getIndexOverviewReportModeDataByAvg(isSort = false) {
      if (!isSort) {
        this.tableColumns = [];
        this.sortValue = {};
      }
      this.tableData = [];
      this.loading = true;
      try {
        let params = this.getSearchParams();

        let {
          data: {
            data: { headers, details },
          },
        } = await this.$http.post(evaluationoverview.getIndexOverviewReportModeDataByAvg, {
          ...params,
          ...this.sortValue,
        });
        if (!headers) {
          return;
        }
        if (!isSort) {
          this.handleTableColumn(headers ?? []);
          this.handleSlotData();
        }
        this.tableData = details ?? [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
        this.buttonLoading = false;
      }
    },
    handleTableColumn(data) {
      this.tableColumns = data.map((item) => {
        let realItem = {
          title: item.title,
          key: item.key,
          align: 'center',
          className: 'header-table',
          standards: item.standards,
        };
        let vm = this;
        realItem.children = item.children?.map((item) => {
              return {
                id: item.id,
                standards: item.standards,
                className: 'header-table',
                title: item.title,
                key: item.key,
                slot: item.key,
                align: 'center',
                minWidth: item.title.length * 14 + 90, // padding 有 左右的5px
                // maxWidth: 300,
                tooltip: true,
                renderHeader: (h, params) => {
                  return h('div', [
                    h(
                      'p',
                      {
                        class: 'ellipsis header-width inline vt-middle t-center reset-width',
                        style: { '--reset-width': `${(params.column.title.length * 14 + 80) / 192}rem` },
                      },
                      [
                        h('i', {
                          class: 'icon-font icon-wenhao mr-sm vt-middle',
                          on: {
                            click() {
                              vm.currentRow = {
                                indexType: params.column.key,
                                indexName: params.column.title,
                                id: params.column.id,
                              };
                              vm.visible = true;
                            },
                          },
                        }),
                        params.column.title,
                        // h('span', {
                        //   class: 'ellipsis header-width inline vt-middle',
                        // }, params.column.title),
                      ],
                    ),
                    h(
                      'p',
                      {
                        class: 't-center ml-sm',
                      },
                      `( ${params.column?.standards ?? 0}% )`,
                    ),
                  ]);
                },
              };
            });
        
        // 完好率
        if (['COMPLE_RATE'].includes(item.key)) {
          realItem.slot = item.key;
          realItem.fixed = 'left';
          realItem.width = 100;
          delete realItem.children;
        }
        return realItem;
      });
      this.tableColumns = this.isDeepActiveItem.isFirstLevel
        ? firstLevelTableColumn.concat(this.tableColumns)
        : secondLevelTableColumn.concat(this.tableColumns);
      },
    handleSlotData() {
      this.tableColumns.forEach((item) => {
        'slot' in item ? this.slotsList.push(item.slot) : null;
        if ('children' in item && item.children instanceof Array) {
          item.children.forEach((one) => {
            'slot' in one ? this.slotsList.push(one.slot) : null;
          });
        }
      });
    },
    hintModalShow() {
      this.hintShow = true;
    },
    // onClickIndex () {
    //   console.log(this.allMessage,'allMessage')
    //   let code = this.statisticType == 'REGION' ? this.allMessage.selfRegionCode : this.allMessage.selfOrgCode
    //   this.$refs.exportModuleRef.init(this.statisticType,code);
    // },
    clickOrgRegion(row, item) {
      // 选中最新值||每日结果，不允许点击行政区划查看
      if ([2, 3].includes(this.searchData.type)) return;
      // 点击行政区划，需要切换表格形式
      this.$emit('changeBreadcrumb', row[item]);
    },
    // exportAdd (val) {
    //   this.getExport(val);
    // },
    // 导出
    async getExport() {
      try {
        let params = this.getSearchParams();
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportIndexOverviewReportModeDataByAvg, params);
        await this.$util.common.transformBlob(res.data.data);
        this.$refs.exportModule.hide();
      } catch (err) {
        console.log(err);
      }
    },
    handleClickJump(row) {
      if (!row.batchId) return;
      //evaluationoResultMixin.js
      this.jump({
        orgCode: row.orgCode,
        regionCode: row.regionCode,
        statisticType: this.statisticType,
        taskSchemeId: this.taskSchemeId,
        indexId: `${row.indexId}`,
        indexType: row.key,
        batchId: row.batchId,
        noShowResult: true, // 评测概览  跳转 评测结果  特殊处理下
      });
    },
    // rowClassName(row, index) {
    //   if (!!this.$route.query.orgCode && row.orgCode === this.$route.query.orgCode) {
    //     return 'ivu-table-row-hover';
    //   }
    // },
    onSortChange(obj) {
      let { order } = obj;
      let val = order.toUpperCase();
      if (val !== 'NORMAL') {
        this.sortValue = {
          sort: val,
        };
      } else {
        this.sortValue = {};
      }
      this.getIndexOverviewReportModeDataByAvg(true);
    },
    quarterChange({ year, quarter }) {
      this.searchData.year = year;
      this.searchData.quarter = quarter;
    },
  },
  watch: {
    isDeepActiveItem: {
      handler(val) {
        if (!val.id) return;
        this.getIndexOverviewReportModeDataByAvg();
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    // exportData: require('@/views/governanceevaluation/evaluationoverview/export-data.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    //taskToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue').default,
    // captureBox: require('@/views/governanceevaluation/evaluationoverview/components/capture-box.vue').default,
    hintModalShow: require('@/views/governanceevaluation/evaluationoverview/components/hint-modal-show.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
    //exportData: require('./export-data.vue').default,
    //ProblemSolving: require('@/views/governanceevaluation/evaluationoverview/components/problem-solving.vue').default,
    QuarterPicker: require('@/components/quarter-picker/index.vue').default,
  },
};
</script>

<style lang="less" scoped>
.model-table {
  @{_deep}.click-hint {
    color: #2d8cf0;
    cursor: pointer;
  }
  .input-width {
    width: 230px;
  }
  @{_deep}.icon-wenhao {
    background: var(--bg-wenhao) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  @{_deep}.ivu-select-selection {
    > div {
      display: flex;
      overflow: hidden;
      .ivu-tag {
        flex-shrink: 0;
        width: 150px;
      }
    }
  }
  .model-table-title {
    padding: 0 0px 15px 10px;
    position: relative;
    border-bottom: solid 1px var(--border-color);
    display: flex;
    justify-content: space-between;

    .information-index {
      color: #a9bed9;
      margin-left: 5px;
      cursor: pointer;
    }
  }

  @{_deep}.model-table-wrapper {
    margin-bottom: 20px;
    overflow-y: auto !important;
  }
  .can-click {
    cursor: pointer;
    text-decoration: underline !important;
  }
  .is-parent-color {
    color: var(--color-warning) !important;
  }
  .fail-color {
    color: var(--color-failed) !important;
  }
  .pass-color {
    color: var(--color-success) !important;
  }
  .region-color {
    color: var(--color-display-text);
  }
}
</style>
