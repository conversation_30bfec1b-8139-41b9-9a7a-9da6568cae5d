<template>
  <Form ref="formData" :model="queryParam" :inline="true" :label-width="80">
    <FormItem label="算法名称:" prop="compareAlgorithmId">
      <Select v-model="queryParam.compareAlgorithmId"  transfer clearable style="width: 150px">
        <Option
          v-for="item in algorithmList"
          :value="item.id"
          :key="item.id"
          placeholder="请选择算法"
          >{{ item.name }}</Option
        >
      </Select>
    </FormItem>
    <FormItem label="报警级别:" prop="taskLevel">
      <selectTag
        class="selectTag"
        :list="levelList"
        ref="taskLevel"
        vModel="taskLevel"
        @selectItem="selectItem"
      />
    </FormItem>
    <FormItem label="报警设备:"  prop="deviceIds">
      <div class="select-tag-button" @click="selectDevice()">
        选择设备/已选（{{ queryParam.deviceIds.length }}）
      </div>
    </FormItem>
    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />
  </Form>
</template>
<script>
import selectTag from "@/views/target-control/components/select-tag.vue";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { getLLMCompareAlgorithmPageList } from "@/api/semantic-placement";
export default {
  components: { selectTag },
  data() {
    return {
      selectDeviceList: [],
      queryParam: {
        compareAlgorithmId: "", // 算法名称
        taskLevel: null, // 布控级别：null - 全部
        deviceIds: [], // 选中设备的Id
      },
      // 布控级别
      levelList: [
        { name: "一级", value: 1 },
        { name: "二级", value: 2 },
        { name: "三级", value: 3 },
      ],
      algorithmList:[]
    };
  },
  computed: {
    keyWords() {
      return this.$route.query.keyWords || "";
    },
  },
  async created() {
    if (this.keyWords) {
      let params = {
        deviceName: this.keyWords,
        pageNumber: 1,
        pageSize: 100,
        filter: this.judgeUser,
        orgCodes: [],
      };
      let { data } = await queryDeviceList(params);
      data.entities.map((item) => {
        item.select = true;
      });
      this.selectData(data.entities);
    }
  },
  mounted(){
    this.queryAlgorithmPageList()
  },
  methods: {
    async queryAlgorithmPageList(){
      const res = await getLLMCompareAlgorithmPageList({pageNumber:1,pageSize: 9999});
      this.algorithmList = res?.data?.entities || []
    },
    /**
     * @description: 选择设备，打开弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceId);
    },

    /**
     * @description: 重置
     */
    reset() {
      this.$refs.formData.resetFields();
      this.$refs.taskLevel.currentIndex = -1;
      this.selectDeviceList = [];
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      const {compareAlgorithmId,taskLevel,deviceIds} = this.queryParam;
      return {
        compareAlgorithmIds:compareAlgorithmId ? [compareAlgorithmId] : [],
        taskLevelList:taskLevel ? [taskLevel] :[],
        resourceIds:deviceIds
      }
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
</style>