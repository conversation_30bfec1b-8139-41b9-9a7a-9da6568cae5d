<template>
  <div class="zdr-data-governance auto-fill">
    <div class="search-module mb-sm">
      <ui-label class="inline mr-lg" label="抓拍时间：">
        <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #timeReversalNums="{ row }">
        <span
          :class="[row.timeReversalNums === 0 ? '' : 'statistic-num', 'font-red']"
          @click="detailDialogShow(row, 'timeReversalNums', '2')"
        >
          {{ row.timeReversalNums }}
        </span>
      </template>
      <template #fieldMisNums="{ row }">
        <span
          :class="[row.fieldMisNums === 0 ? '' : 'statistic-num', 'font-red']"
          @click="detailDialogShow(row, 'fieldMisNums', '1')"
        >
          {{ row.fieldMisNums }}
        </span>
      </template>
    </ui-table>
    <Detail v-model="detailVisible" :detail-data="detailData"></Detail>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {},
  data() {
    return {
      loading: false,
      detailVisible: false,
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '全部', value: 'all' },
          { label: '今日', value: 'today' },
          { label: '7天', value: 'week' },
          { label: '30天', value: 'month' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      searchData: {
        beginTime: '',
        endTime: '',
        sort: 'ASC',
      },
      tableColumns: [
        {
          width: 100,
          title: '序号',
          type: 'index',
          align: 'center',
        },
        // {
        //   minWidth: 200,
        //   title: '行政区划',
        //   sortType: 'asc',
        //   sortable: 'custom',
        //   key: 'civilName',
        // },
        {
          minWidth: 100,
          title: '车流量总量',
          key: 'vehicleTotalNums',
        },
        {
          minWidth: 100,
          title: '合格数量',
          key: 'quantityNums',
        },
        {
          minWidth: 100,
          title: '时间倒挂',
          slot: 'timeReversalNums',
        },
        {
          minWidth: 100,
          title: '属性字段缺少',
          slot: 'fieldMisNums',
        },
      ],
      tableData: [],
      detailData: {},
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  methods: {
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.queryStatistics, this.searchData);
        this.tableData = [res.data.data];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    onSortChange({ order }) {
      if (order.toUpperCase() === 'NORMAL') {
        this.searchData.sort = 'ASC';
      } else {
        this.searchData.sort = order.toUpperCase();
      }
      this.init();
    },
    search() {
      this.init();
    },
    reset() {
      this.timeRadio.value = 'all';
      this.timeRadio.timePickerShow = false;
      this.timeRadio.startTime = '';
      this.timeRadio.endTime = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    detailDialogShow(row, filed, type) {
      if (row[filed] === 0) return;
      this.detailData = {
        civilCode: row.civilCode,
        beginTime: this.searchData.beginTime,
        endTime: this.searchData.endTime,
        imageType: type,
      };
      this.detailVisible = true;
    },
  },
  watch: {},
  components: {
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    Detail: require('./detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.zdr-data-governance {
  .search-module {
    padding: 0 20px;
  }
  .statistic-num {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
