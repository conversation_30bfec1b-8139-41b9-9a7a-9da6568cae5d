<template>
  <div class="base-search">
    <div class="inline">
      <ui-label class="inline mb-sm mr-lg" label="抓拍时间" :width="70">
        <div class="date-picker-box">
          <DatePicker
            class="w150 mb-md"
            v-model="searchData.startTime"
            type="datetime"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="w150"
            v-model="searchData.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
          ></DatePicker>
        </div>
      </ui-label>
      <ui-label class="inline mb-sm mr-lg" label="组织机构" :width="70">
        <ApiOrganizationTree
          style="width: auto !important"
          :treeData="treeData"
          :taskObj="taskObj"
          :selectTree="searchData"
          placeholder="请选择组织机构"
        >
        </ApiOrganizationTree>
      </ui-label>
      <ui-label class="inline mb-sm mr-lg" label="抓拍设备" :width="70">
        <select-camera
          @pushCamera="pushCamera"
          :device-ids="searchData.deviceIds"
          class="select-camera"
        ></select-camera>
      </ui-label>
      <ui-label class="inline mb-sm mr-lg" label="检测结果" :width="70">
        <Select
          class="w150 select-camera"
          v-model="searchData.outcomeAll"
          :clearable="true"
          placeholder="请选择检测结果"
        >
          <Option v-for="(item, index) in cardSearchList" :value="item.dataKey" :key="index">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>
    </div>
    <ui-label :width="30" class="inline mb-sm search-button" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    modular: {
      default: 1,
    },
    cardSearchList: {
      type: Array,
      default() {},
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      searchData: {
        deviceIds: [],
        startTime: '',
        endTime: '',
        synthesisResult: '',
        orgCode: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    // this.copySearchDataMx(this.searchData)
    this.reashfalf();
  },
  mounted() {},
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      if (this.searchData.outcomeAll) {
        this.searchData.outcome = this.searchData.outcomeAll.split('__')[0];
        if (this.searchData.outcome == 4) {
          this.searchData.column = this.searchData.outcomeAll.split('__')[1] || '';
          this.searchData.outcome = 2;
        } else if (this.searchData.outcome == 2) {
          this.searchData.errorColumn = this.searchData.outcomeAll.split('__')[1] || '';
          this.searchData.outcome = 2;
        } else {
          this.searchData.errorColumn = '';
          this.searchData.column = '';
        }
      }
      this.searchData.endTime = this.searchData.endTime ? this.formateDate(this.searchData.endTime) : null;
      this.searchData.startTime = this.searchData.startTime ? this.formateDate(this.searchData.startTime) : null;
      this.$emit('startSearch', this.searchData);
    },
    formateDate(chinaStandard) {
      var date = new Date(chinaStandard);
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      m = m < 10 ? '0' + m : m;
      var d = date.getDate();
      d = d < 10 ? '0' + d : d;
      var h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
      var minute = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
      var s = date.getSeconds();
      let time = y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + s;
      return time;
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.reashfalf();
      this.$emit('startSearch', this.searchData);
    },
    reashfalf() {
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        outcomeAll: '',
        errorColumn: '',
        column: '',
        outcome: '',
        orgCode: '',
      };
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.searchData.orgCode = orgCode || ''
    },
  },
  watch: {
    taskObj: {
      deep: true,
      handler: function () {
        this.reashfalf();
        this.$nextTick(() => {
          // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
          // this.searchData.orgCode = orgCode || ''
        });
      },
    },
  },
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
    ApiOrganizationTree: require('../../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  // padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .w150 {
    width: 150px;
  }
  .ui-label {
    // line-height: 40px;
  }
  .search-button {
    white-space: nowrap;
  }
}
.mt4 {
  margin-top: 4px;
}
.exportBtn {
  float: right;
  margin-top: 3px;

  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
.select-camera {
  display: inline-block;
  vertical-align: middle;
  color: #fff;
}
</style>
