<template>
  <ui-modal v-model="visible" :styles="styles" title="资产同步" :loading="loading" @query="handleSubmit">
    <div class="operation-information">
      <p class="base-text-color mb-lg font-active-color">确定将资产库设备同步到上报库吗？同步后上报库设备将被覆盖</p>
      <div class="mb-lg">
        <ui-label class="mr-40" label="资产库独有设备" :width="110">
          <RadioGroup v-model="formData.syncDeviceAlone">
            <Radio label="0" class="input-width"> 不同步 </Radio>
            <Radio label="1" class="input-width"> 同步 </Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="mr-40" label="上报库独有设备" :width="110">
          <RadioGroup v-model="formData.syncReportAlone">
            <Radio label="0" class="input-width"> 删除</Radio>
            <Radio label="1" class="input-width"> 保留</Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="mr-40" label="已上报设备" :width="110">
          <RadioGroup v-model="formData.finshReport">
            <Radio label="0" class="input-width"> 不覆盖</Radio>
            <Radio label="1" class="input-width"> 覆盖</Radio>
          </RadioGroup>
        </ui-label>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import viewassets from '@/config/api/viewassets';
export default {
  props: ['value'],
  data() {
    return {
      visible: false,
      styles: {
        width: '3.8rem',
      },
      formData: {
        syncReportAlone: '0',
        syncDeviceAlone: '0',
        finshReport: '0',
      },
      loading: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async handleSubmit() {
      this.loading = true;
      try {
        // 未读信息接口统计
        await this.$http.post(viewassets.syncDevice, this.formData);
        this.visible = false;
        this.$emit('updateList');
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
  },

  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
};
</script>
<style lang="less" scoped>
.operation-information {
  padding: 10px 20px;
  margin: 0 auto;
  .input-width {
    width: 80px;
  }
}
</style>
