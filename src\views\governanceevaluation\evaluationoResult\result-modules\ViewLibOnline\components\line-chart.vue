<template>
  <div class="line-chart-container">
    <title-section title-name="历史趋势 ">
      <div class="search" slot="content">
        <tag-view slot="mid" :list="tagList" @tagChange="changeStatus" ref="tagView" class="tag-view"></tag-view>
        <DatePicker
          class="ml-md"
          ref="DatePicker"
          v-if="dateType === 'DAY'"
          type="month"
          placeholder="请选择月份"
          format="yyyy年MM月"
          :value="month"
          :editable="false"
          @on-change="handleChange"
        ></DatePicker>
        <DatePicker
          class="ml-md"
          ref="yearPicker"
          v-else-if="dateType === 'MONTH'"
          type="year"
          placeholder="请选择年"
          format="yyyy年"
          :value="year"
          :editable="false"
          @on-change="handleChangeYear"
        ></DatePicker>
      </div>
    </title-section>
    <div class="echarts-box" v-ui-loading="{ loading: loading, tableData: Object.keys(echart) }">
      <draw-echarts
        v-if="Object.keys(echart).length"
        :echart-option="echartRin"
        :echart-style="rinStyle"
        ref="zdryChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    TitleSection: require('@/views/governanceevaluation/evaluationoResult/components/title-section').default,
    TagView: require('@/components/tag-view.vue').default,
  },
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  mixins: [dealWatch],
  data() {
    return {
      barData: [],
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
      loading: false,
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      echart: {},
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
      tagList: ['日', '月'],
      dateType: 'DAY',
      chartData: {
        xData: [],
        onlineData: [],
        offLineData: [],
        reportData: [],
      },
    };
  },
  mounted() {
    this.getDate();
    this.startWatch(
      '$route.query',
      () => {
        this.getDayCaptureStatistics();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    /**
     * DAY/MONTH
     * @param index
     * @param item
     */
    changeStatus(index, item) {
      if (item === '日') {
        this.dateType = 'DAY';
        this.getDate();
      } else if (item === '月') {
        this.month = null;
        this.monthIndex = 0;
        this.dateType = 'MONTH';
      }
      this.getDayCaptureStatistics();
    },
    initChart() {
      this.dateType === 'DAY' ? this.initBarChart() : this.initLineChart();
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear(value) {
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    async getDayCaptureStatistics() {
      try {
        const { regionCode, orgCode, statisticType, taskSchemeId, indexType, indexId } = this.$route.query;
        this.loading = true;
        let params = {
          taskSchemeId: taskSchemeId,
          indexId: indexId,
          indexType: indexType,
          dateType: this.dateType,
          year: this.year,
          month: this.monthIndex,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          needCustomDetail: '1',
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getHistoryTrend, params);
        this.echart = data || {};
        if (this.dateType === 'DAY') {
          const duration = 24;
          this.chartData.xData = this.echart.default.map((item) => {
            switch (item.detail.isOnline) {
              case '1':
                this.chartData.onlineData.push(duration);
                this.chartData.offLineData.push(0);
                this.chartData.reportData.push(0);
                break;
              case '2':
                this.chartData.offLineData.push(duration);
                this.chartData.onlineData.push(0);
                this.chartData.reportData.push(0);
                break;
              default:
                this.chartData.reportData.push(duration);
                this.chartData.onlineData.push(0);
                this.chartData.offLineData.push(0);
            }
            return item.horizontal;
          });
        }
        this.initChart();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    initLineChart() {
      let opts = {
        data: this.echart,
        dateType: this.dateType,
        tooltipFormatter: (data) => {
          let str = '';
          data.forEach((item) => {
            str = `<p>${item.data.startTime}</p>
                   <p><span class="mr-md">${this.activeIndexItem.indexName}</span>${item.data.vertical || 0}%</p>
                   <p><span class="mr-md">报备天数</span>${item.data.detail.reportDayNumOfM || 0}</p>
                   <p><span class="mr-md">离线天数</span>${item.data.detail.unOnlineNumOfM || 0}</p>
                   <p><span class="mr-md">在线天数</span>${item.data.detail.onlineNumOfM || 0}</p>
`;
          });
          return str;
        },
      };
      this.echartRin = this.$util.doEcharts.indexTrendChart(opts);
    },
    initBarChart() {
      const opts = {
        color: [`${$var('--color-orange-5')}`, `${$var('--color-green-8')}`, `${$var('--color-yellow-12')}`],
        legendData: ['在线', '离线', '报备'],
        yAxisShow: {
          show: false,
        },
        xData: this.chartData.xData,
        series: [
          {
            name: '在线',
            type: 'bar',
            data: this.chartData.onlineData,
            stack: 'value',
            barWidth: '22px',
            barCategoryGap: '3%',
            barGap: 0.3, //柱间距离
          },
          {
            name: '离线',
            type: 'bar',
            data: this.chartData.offLineData,
            stack: 'value',
            barWidth: '22px',
            barCategoryGap: '3%',
            barGap: 0.3, //柱间距离
          },
          {
            name: '报备',
            type: 'bar',
            data: this.chartData.reportData,
            stack: 'value',
            barWidth: '22px',
            barCategoryGap: '3%',
            barGap: 0.3, //柱间距离
          },
        ],
      };
      this.echartRin = this.$util.doEcharts.reportChart(opts);
    },
  },
};
</script>

<style lang="less" scoped>
.line-chart-container {
  position: relative;
  height: 100%;
  width: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  border-radius: 4px;
  .search {
    position: relative;
    display: flex;
    align-items: center;

    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;

      > div {
        color: #fff;
        margin-left: 42px;
        font-size: 14px;
      }

      .font-sky {
        color: #25e6fd;
      }

      .font-orange {
        color: #f78b2e;
      }
    }
  }

  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);

    .charts {
      height: 100% !important;
    }
  }
}
</style>
