<template>
  <div class="time-modal">
    <RadioGroup class="mb-sm" type="button" button-style="solid" v-model="form.cronType" @on-change="setPlan">
      <div v-if="!['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(type)">
        <Radio :label="item.value" v-for="(item, index) in planList" :key="index">{{ item.label }}</Radio>
      </div>
      <div v-if="['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(type)">
        <Radio :label="item.value" v-for="(item, index) in planLis" :key="index">{{ item.label }}</Radio>
      </div>
    </RadioGroup>
    <div class="form-row mb-lg">
      <span v-if="form.cronType === '2'" class="width-time mr-sm">
        <Select
          key="week"
          placeholder="请选择"
          v-model="form.cronData"
          transfer
          multiple
          clearable
          :max-tag-count="1"
          @on-change="changeData"
        >
          <Option v-for="(item, index) in planWeek" :key="index" :value="item.value">{{ item.label }}</Option>
        </Select>
      </span>
      <span v-if="form.cronType === '3'" class="width-time mr-sm">
        <Select
          key="month"
          placeholder="请选择"
          v-model="form.cronData"
          transfer
          multiple
          clearable
          :max-tag-count="1"
          @on-change="changeData"
        >
          <Option v-for="(item, index) in planMonth" :key="index" :value="item.value">{{ item.label }}</Option>
        </Select>
      </span>
      <span class="width-picker">
        <Select
          class="time-picker"
          v-model="form.timePoints"
          transfer
          multiple
          clearable
          :max-tag-count="form.cronType === '1' ? 3 : 1"
          :class="form.cronType === '1' ? 'width18' : 'width12'"
          @on-change="changeCronTime"
        >
          <Option v-for="item in schemeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    formData: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    indexType: {
      type: String,
      default: '',
    },
    maxTag: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      form: {
        cronType: '1',
        timePoints: [],
        cronData: [],
      },
      planList: [
        { label: '每天', value: '1' },
        { label: '每周', value: '2' },
        { label: '每月', value: '3' },
      ],
      planLis: [{ label: '每天', value: '1' }],
      planWeek: [
        { label: '周一', value: 1 },
        { label: '周二', value: 2 },
        { label: '周三', value: 3 },
        { label: '周四', value: 4 },
        { label: '周五', value: 5 },
        { label: '周六', value: 6 },
        { label: '周日', value: 7 },
      ],
      planMonth: [],
      schemeList: [],
      type: '',
    };
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.form = {
            cronType: this.formData.cronType,
            timePoints: this.formData.cronType === '1' ? this.formData.cronData : this.formData.timePoints,
            cronData: this.formData.cronType === '1' ? [] : this.formData.cronData,
          };
        } else {
          this.form = {
            ...this.form,
          };
        }
      },
      immediate: true,
      deep: true,
    },
    indexType: {
      handler(val) {
        this.type = val;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getHour();
    this.mGetDate();
  },
  mounted() {},
  methods: {
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      this.schemeList = arr;
    },
    // 获取当前月天数
    mGetDate() {
      var date = new Date();
      var curretMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);
      var curre = curretMonth.getDate();
      var arr = [];
      for (var i = 1; i <= curre; i++) {
        arr.push({ label: i + '号', value: i });
      }
      this.planMonth = arr;
    },
    // 选择天/周/月
    setPlan(e) {
      this.form.timePoints = [];
      this.form.cronData = [];
      this.form.cronType = e;
      this.$emit('checkTime', {
        cronData: [],
        cronType: this.form.cronType,
        timePoints: [],
      });
    },
    // 选择周/月
    changeData() {
      if (this.form.cronType !== '1' && this.form.timePoints.length > 0) {
        this.$emit('checkTime', this.form);
      }
    },
    changeCronTime(val) {
      if (['FACE_PLATFORM_ONLINE_RATE', 'VEHICLE_PLATFORM_ONLINE_RATE'].includes(this.type)) {
        if (val.length > 1) {
          this.form.timePoints.shift();
        }
      }
      if (this.maxTag && val.length > this.maxTag) {
        this.form.timePoints.shift();
      }
      const { cronType, cronData, timePoints } = this.form;
      const time = {
        cronData: cronType === '1' ? timePoints : cronData,
        cronType,
        timePoints: cronType === '1' ? [] : timePoints,
      };
      this.$emit('checkTime', time);
    },
  },
};
</script>
<style lang="less" scoped>
.time-modal {
  text-align: left;
  .width-time {
    width: 154px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 20px;
  }
  .width18 {
    width: 380px;
  }
  .width12 {
    width: 217px;
  }
}
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
}
@{_deep}.ivu-modal-body {
  padding: 10px 50px !important;
}

@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  border: 1px solid var(--border-checkbox-inner);
  background: var(--bg-checkbox-inner);
  color: var(--color-switch-tab);
  > span {
    margin: 0;
  }
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:after {
  background: var(--border-checkbox-inner);
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
  background: var(--border-checkbox-inner);
}
@{_deep} .ivu-radio-group-button-solid .ivu-radio-wrapper-checked:not(.ivu-radio-wrapper-disabled) {
  background: var(--color-primary);
  color: #fff;
  border: 1px solid var(--color-primary);
  box-shadow: -1px 0 0 0 var(--color-primary);
}
@{_deep} .time-picker .ivu-icon-ios-arrow-down:before {
  content: '\F2DA';
}
@{_deep} .ivu-select-multiple .ivu-tag{
  margin: 4px 3px 7px 0 !important;
}
</style>
