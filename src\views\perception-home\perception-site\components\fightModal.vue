<template>
  <Modal
    v-model="visible"
    class-name="vertical-center-modal"
    title="我的作战"
    :width="770"
    footer-hide
  >
    <div class="header-title" slot="header">
      <p>我的作战</p>
    </div>
    <div class="content">
      <Form ref="form" :model="form" :rules="ruleForm">
        <Row :gutter="20">
          <Col span="10">
            <FormItem label="作战名称:" prop="resourceNameCn">
              <Input
                v-model="form.name"
                :maxlength="50"
                @on-change="handleinputchange"
                placeholder="请输入"
                class="width-md"
              />
            </FormItem>
          </Col>
          <Col span="14">
            <FormItem label="范围时间:" prop="resourceNameCn">
              <DatePicker
                v-model="form.time"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                @on-change="handleOnchange"
                @on-ok="handleOkchange"
                @on-clear="handleClearchange"
                placeholder="请选择时间"
                style="width: 100%"
              ></DatePicker>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="list">
      <ul>
        <li class="flexLi" v-for="(item, index) in fightList" :key="index">
          <div class="left" @click="handlefight(item)">
            <i
              class="iconfont"
              :class="item.source == 'owner' ? 'icon-caozuojilu' : 'icon-share'"
            ></i>
            {{ item.name }}
          </div>
          <div class="right">
            {{ item.createTime }}
          </div>
        </li>
        <ui-empty v-if="fightList.length === 0"></ui-empty>
        <ui-loading v-if="loading" />
      </ul>
    </div>
    <!-- 目前就10条，暂不需要分页 -->
    <!-- <div slot="footer">
            <ui-page :current="page.pageNumber" :total="total" :page-size="page.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
        </div> -->
  </Modal>
</template>

<script>
import { pageList } from "@/api/home";
import { getAllCombatRecord } from "@/api/operationsOnTheMap";

export default {
  name: "",
  components: {},
  data() {
    return {
      visible: false,
      loading: false,
      form: {
        name: "",
        time: "",
      },
      ruleForm: {},
      fightList: [],
      total: 0,
      page: {
        pageNumber: 1,
        pageSize: 10,
      },
      searchTime: [],
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    show() {
      this.visible = true;
      this.searchTime = [];
      this.form = {
        name: "",
        time: "",
      };
      this.searchList();
    },
    searchList() {
      this.loading = true;
      this.fightList = [];
      let endTime = "",
        startTime = "";
      if (this.searchTime.length > 0) {
        startTime = this.searchTime[0];
        endTime = this.searchTime[1];
      }
      let params = {
        endTime: endTime,
        startTime: startTime,
        name: this.form.name,
        ...this.page,
      };
      getAllCombatRecord(params)
        .then((res) => {
          if (res.code === 200) {
            this.fightList = res.data.filter((item) =>
              item.name.includes(this.form.name)
            );
            let timestampS = new Date(startTime).getTime();
            let timestampE = new Date(endTime).getTime();
            if (timestampS) {
              this.fightList = this.fightList.filter(
                (item) =>
                  timestampS < new Date(item.createTime).getTime() &&
                  new Date(item.createTime).getTime() < timestampE
              );
            }
            // this.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleOnchange(val) {
      this.searchTime = val;
    },
    handleOkchange() {
      this.searchList();
    },
    handleClearchange() {
      this.searchTime = [];
      this.searchList();
    },
    handleinputchange() {
      this.searchList();
    },
    /* pageChange(page) {
            this.params.pageNumber = page;
            this.searchList();
        },
        pageSizeChange(size) {
            this.page.pageSize = size;
            this.page.pageNumber = 1;
            this.searchList();
        }, */
    handlefight(item) {
      this.visible = false;
      this.$router.push({
        path: "/track-center/map-track",
        query: {
          content: {
            name: item.name,
            dataText: item.dataText,
            id: item.id,
          },
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.ivu-modal-header {
  background: rgba(211, 215, 222, 0.3);
}
.header-title {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.9);
  p {
    font-weight: bold !important;
  }
}
/deep/ .ivu-modal-body {
  padding: 0;
}
/deep/ .ivu-form-item {
  margin-bottom: 16px;
  display: flex;
  .ivu-form-item-content {
    flex: 1;
  }
}
.content {
  padding: 16px 20px 0 20px;
}
.list {
  border-top: 1px solid #d3d7de;
  padding: 20px;
}
ul {
  width: 100%;
  // padding: 0 20px;
  // padding-bottom: 10px;
  height: 400px;
  overflow-y: auto;
  position: relative;
  .flexLi {
    width: 100%;
    // height: 38px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f9f9f9;
    padding: 9px;
    margin-bottom: 10px;
    .left {
      font-weight: bold;
      font-size: 14px;
      color: #2c86f8;
      cursor: pointer;
      .iconfont {
        margin-right: 6px;
      }
    }
    .right {
      font-size: 14px;
    }
  }
}
/deep/ .pages {
  height: auto;
}
/deep/ .vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }
}
</style>
