<template>
  <div>
    <div class="search">
      <DatePicker
        type="month"
        placeholder="请选择月份"
        format="yyyy年MM月"
        :value="month"
        :editable="false"
        :clearable="false"
        @on-change="handleChange"
      ></DatePicker>
      <div class="statistical-duration">
        <div>
          <span>抓拍总量：</span>
          <span class="font-sky f-16">{{ totalCapture | numberInfo }}</span>
        </div>
        <div>
          <span>{{ monthIndex }}月抓拍量：</span>
          <span class="font-orange f-16">{{ monthTotalCapture | numberInfo }}</span>
        </div>
      </div>
    </div>
    <div class="echarts-box">
      <draw-echarts
        :echart-option="echartRin"
        :echart-style="rinStyle"
        ref="zdryChart"
        class="charts"
        :echarts-loading="loading"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
import Vue from 'vue';
import chartDom from '@/views/archives/components/chart-dom.vue';
export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    deviceId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '230px',
      },
      loading: false,
      month: '',
      monthIndex: '', // 选中月份序号
      echart: {},
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
      tooltipFormatter: (data) => {
        let lineChartDom = Vue.extend(chartDom);
        let _this = new lineChartDom({
          el: document.createElement('div'),
          data() {
            return {
              data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  mounted() {
    this.getDate();
    this.getDayCaptureStatistics();
  },
  methods: {
    initRin() {
      this.barData = this.echart.map((row) => {
        return {
          id: row.id,
          value: row.vehicle,
          day: row.day,
        };
      });
      let opts = {
        xAxis: this.echart.map((row) => row.day),
        data: this.barData,
        dayType: '日期',
        tooltipFormatter: this.tooltipFormatter,
      };
      this.echartRin = this.$util.doEcharts.equipmentArchives(opts);
    },
    // 获取当前月份
    getDate() {
      let year = new Date().getFullYear();
      let month = new Date().getMonth() + 1;
      this.monthIndex = month >= 10 ? month : `0${month}`;
      this.month = `${year}-${this.monthIndex}`;
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.getDayCaptureStatistics();
    },
    async getDayCaptureStatistics() {
      try {
        const { deviceId, month } = this;
        this.loading = true;
        let res = await this.$http.get(equipmentassets.getDayCaptureStatistics, {
          params: {
            deviceId,
            month,
          },
        });
        const { data } = res.data;
        this.echart = data.list;
        this.totalCapture = data.totalCapture || 0;
        this.monthTotalCapture = data.monthTotalCapture || 0;
        this.initRin();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  .statistical-duration {
    margin-left: 26px;
    display: flex;
    align-items: center;
    > div {
      color: var(--color-content);
      margin-left: 42px;
      font-size: 14px;
    }
    .font-sky {
      color: var(--color-bluish-green-text);
    }
    .font-orange {
      color: #f78b2e;
    }
  }
}
</style>
