export default {
  dictPageList: '/ivdg-knowledge-app/system/dict/pageList', // 元数据字典分页列表
  dictBatchAdd: '/ivdg-knowledge-app/system/dict/batchAdd', // 元数据字典批量新增
  dictRemove: '/ivdg-knowledge-app/system/dict/remove/', // 元数据字典删除 + {ids}
  dictUpload: '/ivdg-knowledge-app/system/dict/update', // 元数据编辑
  dictView: '/ivdg-knowledge-app/system/dict/view/', // 元数据详情 + {id}
  detailPageList: '/ivdg-knowledge-app/system/detail/pageList', // 元数据代码表分页查询
  detailAdd: '/ivdg-knowledge-app/system/detail/add', // 元数据代码表新增
  detailUpload: '/ivdg-knowledge-app/system/detail/update', // 元数据代码表编辑
  detailRemove: '/ivdg-knowledge-app/system/detail/remove/', // 元数据代码表删除 + {ids}
  dictGetAllData: '/ivdg-knowledge-app/system/dict/getAllData', // 获取所有字典数据
  managementPageList: '/ivdg-knowledge-app/system/management/pageList', // 基础字段分页列表
  managementRelateTableInfo: '/ivdg-knowledge-app/system/management/relateTableInfo/', // 基础字段关联表信息查询 + {id}
  managementAdd: '/ivdg-knowledge-app/system/management/add', // 基础字段新增
  managementUpdate: '/ivdg-knowledge-app/system/management/update', // 基础字段编辑
  managementRemove: '/ivdg-knowledge-app/system/management/remove/', // 基础字段删除 + {id}
  mangeTableList: '/ivdg-knowledge-app/system/manage/tableList', // 基础表列表
  mangeAdd: '/ivdg-knowledge-app/system/manage/add', // 基础表新增
  mangeRemove: '/ivdg-knowledge-app/system/manage/remove/', // 基础表删除 + {ids}
  mangeUpdate: '/ivdg-knowledge-app/system/manage/update', // 基础表编辑
  mangeView: '/ivdg-knowledge-app/system/manage/view/', // 基础表详情 + {id}
};
