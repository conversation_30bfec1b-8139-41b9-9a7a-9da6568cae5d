<template>
	<ui-modal v-model="visible" :title="!isEdit ? '新增': '编辑'" :r-width="1010" @onOk="comfirmHandle">
		<div ref="personnelThematic" class="personnel-thematic-database">
			<Form ref="personnelForm" :model="personnelForm" :rules="ruleInline" inline class="personnel-form">
				<div class="form-item-title card-border-color">
					<div class="title-line bg-primary"></div>
					<div class="title-text title-color">基本信息</div>
				</div>
				<div class="information-form essential-form">
					<FormItem prop="photoUrlList" class="img-formitem">
						<div class="essential-information-img">
							<div class="avatar-img card-border-color">
								<img v-if="!previewImg" src="@/assets/img/empty-page/null_img_icon.png" class="avatar-null-img" alt='' />
								<template v-else>
									<img v-if="isEdit" v-viewer :src="previewImg.photoUrl" alt />
									<img v-else v-viewer :src="previewImg" alt />
								</template>
							</div>
							<UploadImg choosed ref="uploadImg" :deleted="true" :isEdit="isEdit" :multipleNum="10" :choosedIndex="avatarIndex" :defaultList="personnelForm.photoUrlList" class="upload-img" @on-choose="chooseHandle" />
						</div>
					</FormItem>
					<div class="information-body">
						<div class="info-item">
							<FormItem prop="name">
								<div slot="label"><span class="label-text">姓名</span>:</div>
								<Input v-model="personnelForm.name" placeholder="请输入姓名" class="input-200" />
							</FormItem>
							<FormItem prop="sex">
								<div slot="label"><span class="label-text">性别</span>:</div>
								<RadioGroup v-model="personnelForm.sex" class="input-200">
									<Radio label="1">男</Radio>
									<Radio label="2">女</Radio>
								</RadioGroup>
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="idCardNoType" label="证件类型:">
								<Select v-model="personnelForm.idCardNoType" placeholder="请选择证件类型" class="input-200" filterable>
									<Option v-for="(item, $index) in identityTypeList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
								</Select>
							</FormItem>
							<FormItem prop="idCardNo" label="证件号码:">
								<Input v-model="personnelForm.idCardNo" placeholder="请输入证件号码" class="input-200" />
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="phoneNo" label="联系电话:">
								<Input v-model="personnelForm.phoneNo" placeholder="请输入联系电话" class="input-200" />
							</FormItem>
							<FormItem prop="national">
								<div slot="label"><span class="label-text">民族</span>:</div>
								<Select v-model="personnelForm.national" placeholder="请选择民族" class="input-200" filterable>
									<Option v-for="(item, $index) in nationTypeList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
								</Select>
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="religious" label="宗教信仰:">
								<Input v-model="personnelForm.religious" placeholder="请输入宗教信仰" class="input-200" />
							</FormItem>
							<FormItem prop="nativePlace">
								<div slot="label"><span class="label-text">籍贯</span>:</div>
								<Input v-model="personnelForm.nativePlace" placeholder="请输入籍贯" class="input-200" />
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="marriageStatus" label="婚姻状态:">
								<Select v-model="personnelForm.marriageStatus" placeholder="请选择婚姻状态" class="input-200">
									<Option v-for="(item, $index) in marriageList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
								</Select>
							</FormItem>
							<FormItem prop="bloodType">
								<div slot="label"><span class="label-text">血型</span>:</div>
								<Input v-model="personnelForm.bloodType" placeholder="请输入血型" class="input-200" />
							</FormItem>
						</div>
						<!-- <div class="info-item">
              <FormItem prop="fugitiveNo" label="在逃编号:">
                <Input v-model="personnelForm.fugitiveNo" placeholder="请输入在逃编号" class="input-200"/>
              </FormItem>
              <FormItem prop="degreeEducation" label="文化程度:">
                <Select v-model="personnelForm.degreeEducation" placeholder="请选择文化程度" class="input-200"></Select>
              </FormItem>
            </div> -->
						<div class="info-item">
							<FormItem prop="professional">
								<div slot="label"><span class="label-text">职位</span>:</div>
								<Input v-model="personnelForm.professional" placeholder="请输入职位" class="input-200" />
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="registeredResidence" label="户籍地址:">
								<Input v-model="personnelForm.registeredResidence" placeholder="请输入户籍地址" class="input-520" />
							</FormItem>
						</div>
						<div class="info-item">
							<FormItem prop="address" label="居住地址:">
								<Input v-model="personnelForm.address" placeholder="请输入居住地址" class="input-520" />
							</FormItem>
						</div>
					</div>
				</div>
				<!-- <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">车辆信息</div>
        </div>
        <div class="information-form vehicle-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="licensePlate" label="车牌号码:" class="license-plate-number">
                <Input v-model="personnelForm.licensePlate" transfer placeholder="请输入车牌号码" class="input-200"/>
              </FormItem>
              <FormItem prop="licensePlateColor" label="车牌颜色:" class="license-plate-color">
                <Select v-model="personnelForm.licensePlateColor" transfer placeholder="请选择车牌颜色" class="input-200">
                  <Option v-for="(item, $index) in licensePlateColorList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
                </Select>
              </FormItem>
              <FormItem prop="vehicleColor" label="车辆颜色:">
                <Select v-model="personnelForm.vehicleColor" transfer placeholder="请选择车辆颜色" class="input-200">
                  <Option v-for="(item, $index) in bodyColorList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
                </Select>
              </FormItem>
            </div>
          </div>
        </div> -->
				<!-- <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">其他信息</div>
        </div>
        <div class="information-form other-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="RFIDNumber" label="RFID号码:" class="rfid-number">
                <Input v-model="personnelForm.RFIDNumber" placeholder="请输入RFID号码" class="input-200"/>
              </FormItem>
              <FormItem prop="MACNumber" label="MAC地址:" class="mac-number">
                <Input v-model="personnelForm.MACNumber" placeholder="请输入MAC地址" class="input-200"/>
              </FormItem>
              <FormItem prop="IMEINumber" label="IMEI号码:">
                <Input v-model="personnelForm.IMEINumber" placeholder="请输入IMEI号码" class="input-200"/>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="IMSINumber" label="IMSI号码:" class="rfid-number">
                <Input v-model="personnelForm.IMSINumber" placeholder="请输入IMSI号码" class="input-200"/>
              </FormItem>
              <FormItem prop="remark">
                <div slot="label"><span class="label-text">备注</span>:</div>
                <Input v-model="personnelForm.remark" placeholder="请输入备注" class="input-520"/>
              </FormItem>
            </div>
          </div>
        </div> -->
			</Form>
		</div>
	</ui-modal>
</template>
<script>
	import { mapActions, mapGetters } from 'vuex'
	import { addFaceLibPersonInfo, motifyFaceLibPersonInfo } from '@/api/dataGovernance'
	import UploadImg from '@/components/ui-upload-img'
	export default {
		components: {
			UploadImg
		},
		props: {
			// 当前库对象
			currentRow: {
				type: Object,
				default: {}
			},
			// 证件类型字典
			identityTypeList: {
				type: Array,
				default: []
			},
			// 民族类型字典
			nationTypeList: {
				type: Array,
				default: []
			},
		},
		data() {
			const idCardNo = (rule, value, callback) => {
				if (value === '') {
					callback(new Error('请输入'));
				}
				if (!/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/.test(value)) {
					callback(new Error('身份证格式错误'));
				}
				callback();
			}
			const phoneNo = (rule, value, callback) => {
				if (!/^1(3|4|5|7|8)\d{9}$/.test(value)) {
					callback(new Error('手机号码格式错误'));
				}
				callback();
			}
			return {
				visible: false,
				isEdit: true,    //false-新增, true-编辑
				avatarIndex: '',
				personnelForm: {
					imageList: [],
					photoUrlList: [],
					name: '',
					sex: '1',
					idCardNoType: '',
					idCardNo: '',
					phoneNo: '',
					national: '',
					religious: '',
					marriageStatus: '',
					bloodType: '',
					fugitiveNo: '',
					degreeEducation: '',
					professional: '',
					nativePlace: '',
					address: '',
					licensePlate: '',
					licensePlateColor: '',
					vehicleColor: '',
					RFIDNumber: '',
					MACNumber: '',
					IMEINumber: '',
					IMSINumber: '',
					remark: ''
				},
				previewImg: '',
				ruleInline: {
					name: [
						{ required: true, message: '请输入姓名', trigger: 'blur' }
					],
					sex: [
						{ required: true, message: '请选择性别', trigger: 'blur' }
					],
					idCardNoType: [
						{ required: true, message: '请选择证件类型', trigger: 'change' }
					],
					idCardNo: [
						{ validator: idCardNo, trigger: 'blur' }
						// { required: true, message: '请输入证件号码', trigger: 'blur' }
					],
					phoneNo: [
						{ required: false, message: '请选择证件类型', trigger: 'change' },
						{ validator: phoneNo, trigger: 'change' },
					],
				}
			}
		},
		computed: {
			...mapGetters({
				cardTypeList: 'dictionary/getCardTypeList', //证件类型
				marriageList: 'dictionary/getMarriageList', //婚姻状态
				licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
				bodyColorList: 'dictionary/getBodyColorList' //车辆颜色
			})
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			show(bool, item) {
				this.isEdit = bool
				this.visible = true
				this.previewImg = ''
				this.avatarIndex = ''
				this.$nextTick(() => {
					this.$refs.personnelThematic.scrollTop = 0
					this.$refs.personnelForm.resetFields()
					if (this.isEdit) {
						// 编辑
						this.avatarIndex = 0
						this.personnelForm = JSON.parse(JSON.stringify(item))
						this.previewImg = item.photoUrlList[0]
						this.$forceUpdate()
					}
				})
			},
			// 选择证件照
			chooseHandle(item) {
				this.previewImg = item
			},
			// 确认
			comfirmHandle() {
				this.$refs.personnelForm.validate((valid) => {
					if (valid) {
						this.personnelForm.faceLibId = this.currentRow.featureLibId;
						// 新增
						if (!this.isEdit) {
							addFaceLibPersonInfo(this.personnelForm).then(res => {
								this.visible = false
								this.$Message.success('新增成功')
								this.$parent.init()
							})
						} else {
							//编辑
							this.personnelForm.photoUrlList = [...this.personnelForm.photoUrlList, ...this.$refs.uploadImg.delImageList]
							motifyFaceLibPersonInfo(this.personnelForm).then(res => {
								this.visible = false
								this.$Message.success('编辑成功')
								this.$parent.init()
							})
						}
					}
				})
			}
		}
	}
</script>
<style lang="less" scoped>
	.input-200 {
		width: 200px;
	}
	.input-520 {
		width: 520px;
	}
	/deep/ .ivu-modal-body {
		padding: 0 !important;
	}
	.personnel-thematic-database {
		overflow-x: hidden;
		overflow-y: auto;
		padding: 20px;
		max-height: 680px;
		.personnel-form {
			padding: 0 30px;
			box-sizing: border-box;
			.form-item-title {
				display: flex;
				align-items: center;
				padding-bottom: 4px;
				border-bottom: 1px solid #fff;
				.title-line {
					width: 3px;
					height: 16px;
					margin-right: 6px;
				}
				.title-text {
					font-size: 14px;
					line-height: 20px;
					font-weight: bold;
					font-family: "MicrosoftYaHei-Bold";
				}
			}
			.information-form {
				display: flex;
				justify-content: space-between;
				margin: 20px 0 30px 0;
				.essential-information-img {
					width: 240px;
					margin-right: 64px;
					display: flex;
					flex-direction: column;
					.avatar-img {
						width: 240px;
						height: 318px;
						border: 1px solid #fff;
						& > img {
							width: 100%;
							height: 100%;
							object-fit: contain;
							cursor: pointer;
						}
						.avatar-null-img {
							cursor: unset;
						}
					}
					.upload-img {
						margin-top: 10px;
						justify-content: flex-start;
						/deep/ .upload-item {
							width: 54px;
							height: 54px;
							.ivu-icon-ios-add {
								font-size: 30px;
								font-weight: bold;
							}
							.upload-text {
								line-height: 18px;
								display: none;
							}
						}
					}
				}
				.information-body {
					flex: 1;
					.info-item {
						display: flex;
						justify-content: space-between;
						/deep/ .ivu-form-item {
							display: inline-flex;
							margin-right: 0;
							margin-bottom: 10px;
							.ivu-form-item-label {
								display: flex;
								align-items: center;
								justify-content: end;
								padding-right: 10px;
								white-space: nowrap;
								.label-text {
									text-align-last: justify;
									display: inline-block;
								}
							}
							.ivu-form-item-label::before {
								margin: 0;
							}
							.ivu-radio-wrapper {
								margin-right: 30px;
							}
						}
					}
				}
			}
			.essential-form {
				margin-bottom: 0;
				/deep/ .ivu-form-item {
					.ivu-form-item-label {
						width: 80px !important;
						.label-text {
							width: 58px;
						}
					}
				}
				.img-formitem {
					margin: 0 !important;
				}
			}
			.vehicle-form {
				margin: 20px 0;
				/deep/ .ivu-form-item {
					.ivu-form-item-label {
						width: 74px;
					}
				}
				.info-item {
					justify-content: unset !important;
					.license-plate-number {
						margin-right: 36px !important;
					}
					.license-plate-color {
						margin-right: 46px !important;
					}
				}
			}
			.other-form {
				margin: 20px 0;
				/deep/ .ivu-form-item {
					.ivu-form-item-label {
						width: 74px;
						.label-text {
							width: 58px;
						}
					}
				}
				.info-item {
					justify-content: unset !important;
					.rfid-number {
						margin-right: 36px !important;
					}
					.mac-number {
						margin-right: 46px !important;
					}
				}
			}
		}
	}
	/deep/ .ivu-form-item-error-tip {
		z-index: 9;
	}
</style>