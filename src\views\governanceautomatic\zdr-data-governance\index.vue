<template>
  <div class="zdr-data-governance auto-fill">
    <div class="search-module mb-sm">
      <ui-label class="inline mr-lg" label="抓拍时间：">
        <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #qualifiedNum="{ row }">
        <span :class="[row.qualifiedNum === 0 ? '' : 'statistic-num']" @click="ZDRDetailShow(row, 'qualifiedNum')">
          {{ row.qualifiedNum }}
        </span>
      </template>
      <template #notQualifiedNum="{ row }">
        <span
          :class="[row.notQualifiedNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'notQualifiedNum')"
        >
          {{ row.notQualifiedNum }}
        </span>
      </template>
      <template #correctNum="{ row }">
        <span
          :class="[row.correctNum === 0 ? '' : 'statistic-num', 'color-warning']"
          @click="ZDRDetailShow(row, 'correctNum')"
        >
          {{ row.correctNum }}
        </span>
      </template>
      <template #availableNum="{ row }">
        <span
          :class="[row.availableNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'availableNum')"
        >
          {{ row.availableNum }}
        </span>
      </template>
      <template #realNum="{ row }">
        <span
          :class="[row.realNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'realNum')"
        >
          {{ row.realNum }}
        </span>
      </template>
      <template #notDeviceNum="{ row }">
        <span
          :class="[row.notDeviceNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'notDeviceNum')"
        >
          {{ row.notDeviceNum }}
        </span>
      </template>
      <template #downNum="{ row }">
        <span
          :class="[row.downNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'downNum')"
        >
          {{ row.downNum }}
        </span>
      </template>
      <template #shortNum="{ row }">
        <span
          :class="[row.shortNum === 0 ? '' : 'statistic-num', 'color-failed']"
          @click="ZDRDetailShow(row, 'shortNum')"
        >
          {{ row.shortNum }}
        </span>
      </template>
    </ui-table>
    <ZDR-detail v-model="ZDRDetailVisible" :detail-data="detailData"></ZDR-detail>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {},
  data() {
    return {
      loading: false,
      ZDRDetailVisible: false,
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '全部', value: 'all' },
          { label: '今日', value: 'today' },
          { label: '7天', value: 'week' },
          { label: '30天', value: 'month' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      searchData: {
        beginTime: '',
        endTime: '',
        sort: 'ASC',
      },
      tableColumns: [
        {
          width: 60,
          title: '序号',
          type: 'index',
          align: 'center',
        },
        {
          minWidth: 100,
          title: '行政区划',
          sortType: 'asc',
          sortable: 'custom',
          key: 'civilName',
        },
        {
          minWidth: 100,
          title: '治理轨迹总量',
          key: 'governNum',
        },
        {
          minWidth: 100,
          title: '合格轨迹数量',
          slot: 'qualifiedNum',
        },
        {
          minWidth: 100,
          title: '不合格轨迹数量',
          slot: 'notQualifiedNum',
        },
        {
          minWidth: 100,
          title: '准确存疑轨迹数量',
          slot: 'correctNum',
        },
        {
          minWidth: 100,
          title: '不可访问轨迹数量',
          slot: 'availableNum',
        },
        {
          minWidth: 100,
          title: '上传超时轨迹数量',
          slot: 'realNum',
        },
        {
          minWidth: 100,
          title: '未关联设备轨迹数量',
          slot: 'notDeviceNum',
        },
        {
          minWidth: 100,
          title: '时间倒挂轨迹数量',
          slot: 'downNum',
        },
        {
          minWidth: 100,
          title: '抓拍时间不一致轨迹数量',
          slot: 'shortNum',
        },
      ],
      tableData: [],
      detailData: {},
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  methods: {
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.queryStatisticsList, this.searchData);
        this.tableData = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    onSortChange({ order }) {
      if (order.toUpperCase() === 'NORMAL') {
        this.searchData.sort = 'ASC';
      } else {
        this.searchData.sort = order.toUpperCase();
      }
      this.init();
    },
    search() {
      this.init();
    },
    reset() {
      this.timeRadio.value = 'all';
      this.timeRadio.timePickerShow = false;
      this.timeRadio.startTime = '';
      this.timeRadio.endTime = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    ZDRDetailShow(row, filed) {
      if (row[filed] === 0) return;
      this.detailData = {
        civilCode: row.civilCode,
        beginTime: this.searchData.beginTime,
        endTime: this.searchData.endTime,
        queryFlag: filed,
      };
      this.ZDRDetailVisible = true;
    },
  },
  watch: {},
  components: {
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    ZDRDetail: require('./zdr-detail.vue').default,
  },
};
</script>
<style lang="less" scoped>
.zdr-data-governance {
  .search-module {
    padding: 0 20px;
  }
  .statistic-num {
    cursor: pointer;
    text-decoration: underline;
  }
}
</style>
