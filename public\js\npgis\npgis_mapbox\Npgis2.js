!function t(e,i,o){function r(s,n){if(!i[s]){if(!e[s]){var l="function"==typeof require&&require;if(!n&&l)return l(s,!0);if(a)return a(s,!0);throw new Error("Cannot find module '"+s+"'")}var h=i[s]={exports:{}};e[s][0].call(h.exports,(function(t){var i=e[s][1][t];return r(i||t)}),h,h.exports,t,e,i,o)}return i[s].exports}for(var a="function"==typeof require&&require,s=0;s<o.length;s++)r(o[s]);return r}({1:[function(require,module,exports){function _newArrowCheck(t,e){if(t!==e)throw new TypeError("Cannot instantiate an arrow function")}var that,e,MulRegion;window.MapPlatForm={},window.MapPlatForm.Base={},window.MapPlatForm.ModdleMarker=0,window.MapPlatForm.BottomMarker=1,window.MapPlatForm.CustomMarker=2,console.log("%cNPGIS 2.5D SDK Extension Version 3.1.1","background:#0078D9;color:#fff;padding:2px 6px;border-radius:2px;"),window.MapPlatForm.VERSIONNUMBER="3.1.1",window.MapPlatForm.version="3.1.1",window.MapPlatForm.ServiceType="npgis",window.MapPlatForm.AK="",window.MapPlatForm.dataServiceURL="/npgisdataservice/",MapPlatForm.Base.MapConfig=function(t,e){this.CLASS_NAME="MapConfig",this.dataServiceURL=t||MapPlatForm.dataServiceURL,this.otherServiceURL=e,this.mapServiceURL="",this.mapInfo=null,this._mapJson=null},MapPlatForm.Base.MapConfig.prototype._addLayerToMap=function(t,e,i){var o=[];if(e&&e.length>0){for(var r,a,s,n=0,l=e.length;n<l;n++){s=(r=e[n]).layerType.split(".");var h=r.layerOpt;"ArcgisTileLayer"==s[s.length-1]&&(h=r.layerOpt.layerInfo),a=new NPMap.Layers[s[s.length-1]](r.layerOpt.url,r.layerName,h),o.push(a)}o.length>1?t.addLayers(o,!0):(i&&o[0].setStyle(i),t.addLayers(o))}return o},MapPlatForm.Base.MapConfig.prototype.createMap=function(t,e,i){this.mapInfo=e,this.styleJson=i;var o=new NPMap.Map(t,e.mapOpts),r=this._addLayerToMap(o,e.vectorLayer,this.styleJson);return this._mapJson={map:o,vectorLayer:r},this._mapJson},MapPlatForm.Base.MapConfig.prototype.showVectorLayer=function(){if(this._mapJson&&!this._mapJson.vectorLayer){if(this._mapJson.sattilateLayer){for(var t=0;t<this._mapJson.sattilateLayer.length;t++)this._mapJson.map.removeLayer(this._mapJson.sattilateLayer[t]);this._mapJson.sattilateLayer=null}var e=this._addLayerToMap(this._mapJson.map,this.mapInfo.vectorLayer);this._mapJson.vectorLayer=e}},MapPlatForm.Base.MapConfig.prototype.showSattilateLayer=function(){if(this._mapJson&&!this._mapJson.sattilateLayer){if(this._mapJson.vectorLayer){for(var t=0;t<this._mapJson.vectorLayer.length;t++)this._mapJson.map.removeLayer(this._mapJson.vectorLayer[t]);this._mapJson.vectorLayer=null}var e=this._addLayerToMap(this._mapJson.map,this.mapInfo.sattilateLayer);this._mapJson.sattilateLayer=e}},MapPlatForm.Base.MapConfig.prototype.createSceneMap=function(t,e,i,o,r){var a=MapPlatForm.dataServiceURL+"scene/"+e,s=this;NPMap.Utils.Request.Get(a,"",(function(e){var a;if(!(e=(e=JSON.parse(e)).data.sceneInfo))return(l=s.createMap(t,o,r)).load((function(){i&&i()})),l.map;if((e=JSON.parse(e)).sceneOpts.centerPoint=e.sceneOpts.center,e.sceneOpts.defaultZoom=e.sceneOpts.zoom,e.sceneOpts.minZoom=5,e.sceneOpts.maxZoom=24,o){if(e.sceneOpts)for(var n in e.sceneOpts)e.sceneOpts.hasOwnProperty(n)&&(o.mapOpts[n]=e.sceneOpts[n]);var l=s.createMap(t,o,r);a=l.map}else e.sceneOpts.showBackground=!1,a=new NPMap.Map(t,e.sceneOpts);a.load((function(){var t=new NPMap.Layers.ThreeLayer("threeLayer");a.addLayer(t),e.sceneOpts.showSkyBox&&((new BigScreenMap.MapSkyBox2).addToMap(t),a._mapContainer.style.backgroundColor="#172437"),s._creatScene(a,t,e),i&&i(a,t)})),s._mapJson={map:a}}),(function(){var e=s.createMap(t,o,r);return e.load((function(){i&&i()})),e.map}))},MapPlatForm.Base.MapConfig.prototype._creatScene=function(t,e,i){for(var o=MapPlatForm.dataServiceURL+"models/{modelID}/{modelID}.gltf",r=[],a=0;a<i.models.length;a++){var s=new Promise((function(t,r){var s=o.replace("{modelID}",i.models[a]).replace("{modelID}",i.models[a]),n=a;e.loader.load(s,(function(e){t({index:n,model:e.scene})}),void 0,void 0,(function(){t({index:n,model:null})}))}));r.push(s)}if(Promise.all(r).then((function(o){function r(t){var e={};function i(t){if(t.children.length>0)for(var o=0;o<t.children.length;o++)"Mesh"==t.children[o].type&&(e[t.children[o].material.uuid]||(e[t.children[o].material.uuid]=t.children[o].material.clone())),i(t.children[o])}function r(t){if(t.children.length>0)for(var i=0;i<t.children.length;i++)"Mesh"==t.children[i].type&&(t.children[i].material=e[t.children[i].material.uuid]),r(t.children[i])}for(var a=0;a<o.length;a++)if(t==o[a].index){if(o[a].model){var s=o[a].model.clone();return i(s),r(s),s}return null}}if(i.buildings)for(var a=0;a<i.buildings.length;a++)(s=r(i.buildings[a].modelIndex))&&((d=e.addModel(s,i.buildings[a].position,{rotation:i.buildings[a].rotation,scale:i.buildings[a].scale,name:i.buildings[a].name,id:i.buildings[a].id})).type="building");if(i.appliances)for(a=0;a<i.appliances.length;a++)(s=r(i.appliances[a].modelIndex))&&((d=e.addModel(s,i.appliances[a].position,{rotation:i.appliances[a].rotation,scale:i.appliances[a].scale,name:i.appliances[a].name,id:i.appliances[a].id})).type="appliances");if(i.outRoom)for(a=0;a<i.outRoom.length;a++){var s;(s=r(i.outRoom[a].modelIndex))&&((d=e.addModel(s,i.outRoom[a].position,{rotation:i.outRoom[a].rotation,scale:i.outRoom[a].scale,name:i.outRoom[a].name,id:i.outRoom[a].id})).type="outRoom")}if(i.tree){var n=new BigScreenMap.TreeManager(t,e.tb);for(a=0;a<i.tree.length;a++){for(var l=i.tree[a],h=[],p=0;p<l.treeGeometries.length;p++)if("POLYLINE"===l.type){var c=WKT.read(l.treeGeometries[p]);h.push(c)}else if("POLYGON"===l.type)c=WKT.read(l.treeGeometries[p]),h.push(c);else{var u=new NPMap.Geometry.Point(l.treeGeometries[p][0],l.treeGeometries[p][1]);h.push(u)}var m=[];for(p=0;p<l.models.length;p++){var d;(d=r(l.models[p]))&&m.push(d)}if(m.length){var y=new BigScreenMap.TreeCollect(h,l.type,l.split,m,l.side);n.addTreeCollect(y)}}}})),i.particle){var n=new BigScreenMap.WaterFountainManager(t,e.tb),l=new BigScreenMap.FireManager(t,e.tb);for(a=0;a<i.particle.length;a++)if("water"===i.particle[a].type){var h=new BigScreenMap.WaterFountain({dX:i.particle[a].system[0],dY:i.particle[a].system[1],dZ:i.particle[a].system[2],dX1:i.particle[a].system[3],dY1:i.particle[a].system[4],dZ1:i.particle[a].system[5]}),p=i.particle[a].position;n.addWaterFountain(h,new NPMap.Geometry.Point(p[0],p[1]),{height:p[2]})}else if("fire"===i.particle[a].type){var c=new BigScreenMap.Fire(e.tb);c.scale(i.particle[a].scale[0],i.particle[a].scale[1],i.particle[a].scale[2]),p=i.particle[a].position,l.addFire(c,new NPMap.Geometry.Point(p[0],p[1]),{height:p[2]})}}if(i.walls){var u=new BigScreenMap.WallManager(t,e.tb);for(a=0;a<i.walls.length;a++)for(var m=0;m<i.walls[a].geomtries.length;m++){var d=WKT.read(i.walls[a].geomtries[m]),y=new BigScreenMap.Wall(d,i.walls[a].height,{imageURL:i.walls[a].image,width_height:i.walls[a].width_height});u.addWall(y)}}var _=[],f=new BigScreenMap.GroundManager(t,e.tb);if(i.grounds)for(a=0;a<i.grounds.length;a++){var g=WKT.read(i.grounds[a].landGeometry),v=[];for(m=0;m<i.grounds[a].grassGeometries.length;m++){var M=WKT.read(i.grounds[a].grassGeometries[m]);v.push(M)}_.push(g),i.grounds[a].landImage.imageURL=i.grounds[a].landImage.imageURL,i.grounds[a].grassImage.imageURL=i.grounds[a].grassImage.imageURL;var P=new BigScreenMap.Ground(g,v,{land:i.grounds[a].landImage,grass:i.grounds[a].grassImage},i.grounds[a].hasHoles);f.addGround(P)}if(i.plane&&i.plane.geometry){var b=WKT.read(i.plane.geometry),x=new BigScreenMap.Ground(b,_,{land:{color:i.plane.color}},!0);f.addGround(x)}if(i.water){var w=new BigScreenMap.WaterEffectManager(t,e.tb);for(a=0;a<i.water.length;a++){var S=WKT.read(i.water[a].geometry);w.addWaterRegion(S,i.water[a].height)}}},MapPlatForm.Base.MapGeometry=function(t){this.CLASS_NAME="MapPlatForm.Base.MapGeometry",this.map=t},MapPlatForm.Base.MapGeometry.prototype.getGeometryByGeoJson=function(t){return GeoJSON.read(t)},MapPlatForm.Base.MapGeometry.prototype.getGeometryByWKT=function(t){return WKT.read(t)},MapPlatForm.Base.MapGeometry.prototype.getFGeoJsonByGeometry=function(t){return GeoJSON.write(t,this.map)},MapPlatForm.Base.MapGeometry.prototype.getGGeoJsonByGeometry=function(t){var e=GeoJSON.write(t,this.map),i=JSON.parse(e);return t=JSON.stringify(i.geometry)},MapPlatForm.Base.MapGeometry.prototype.getWKTByGeometry=function(t){return WKT.write(t,this.map)},MapPlatForm.Base.MapGeometry.prototype.getExtent2Polygon=function(t){var e=[];return e.push(new NPMap.Geometry.Point(t.sw.lon,t.sw.lat)),e.push(new NPMap.Geometry.Point(t.ne.lon,t.sw.lat)),e.push(new NPMap.Geometry.Point(t.ne.lon,t.ne.lat)),e.push(new NPMap.Geometry.Point(t.sw.lon,t.ne.lat)),e.push(new NPMap.Geometry.Point(t.sw.lon,t.sw.lat)),new NPMap.Geometry.Polygon(e)},MapPlatForm.Base.MapGeometry.prototype.createMarker=function(t,e){var i=e.markerType?e.markerType:MapPlatForm.ModdleMarker,o=new NPMap.Symbols.Icon(e.url,new NPMap.Geometry.Size(e.size.width,e.size.height));i===MapPlatForm.BottomMarker&&(o.setAnchor(new NPMap.Geometry.Size(0,-e.size.height/2)),e.labelOffset=e.labelOffset?e.labelOffset:{x:0,y:-e.size.height/2}),i==MapPlatForm.CustomMarker&&o.setAnchor(new NPMap.Geometry.Size(-e.iconOffset.width,-e.iconOffset.height)),e.showInMap&&(o._showInMap=e.showInMap);var r=new NPMap.Symbols.Marker(t);if(r.setIcon(o),e.text){label=new NPMap.Symbols.Label(e.text),label.setStyle({color:e.color?e.color:"#FFFFFF"}),e.labelOffset=e.labelOffset?e.labelOffset:{x:0,y:0};var a=new NPMap.Geometry.Size(e.labelOffset.x,e.labelOffset.y);label.setOffset(a),r.setLabel(label)}return e.title&&r.setTitle(e.title),r},MapPlatForm.Base.MapGeometry.prototype.createCircleSector=function(t,e,i,o){var r,a,s,n=NPMap.T.helper.webMoctorJW2PM(t.lon,t.lat),l=[],h=[];o=o*Math.PI/180,i=i*Math.PI/180,l.push(new NPMap.Geometry.Point(n.lon,n.lat));for(var p=0;p<61;p++)r=o+i/60*p,a=n.lon+e*Math.cos(r),s=n.lat+e*Math.sin(r),l.push(new NPMap.Geometry.Point(a,s));for(l.push(new NPMap.Geometry.Point(n.lon,n.lat)),p=0;p<l.length;p++){var c=NPMap.T.helper.inverseMercator(l[p].lon,l[p].lat);c=new NPMap.Geometry.Point(c.lon,c.lat),h.push(c)}return new NPMap.Geometry.Polygon(h)},MapPlatForm.Base.MapGeometry.prototype.getIconByParam=function(t){markerType=t.markerType?t.markerType:MapPlatForm.ModdleMarker;var e=new NPMap.Symbols.Icon(t.url,new NPMap.Geometry.Size(t.size.width,t.size.height));return markerType===MapPlatForm.BottomMarker&&e.setAnchor(new NPMap.Geometry.Size(0,-t.size.height/2)),markerType===MapPlatForm.CustomMarker&&e.setAnchor(new NPMap.Geometry.Size(-t.iconOffset.width,-t.iconOffset.height)),e},MapPlatForm.Base.MapGeometry.prototype.getExtentByOverlays=function(t){for(var e,i,o,r,a=t.length-1;a>=0;a--){var s=t[a].getExtent();e&&i&&o&&r?(e>s.left&&(e=s.left),i>s.bottom&&(i=s.bottom),o<s.right&&(o=s.right),r<s.top&&(r=s.top)):(e=s.left,i=s.bottom,o=s.right,r=s.top)}return new NPMap.Geometry.Extent(e,i,o,r)},MapPlatForm.Base.MapGeometry.prototype.sortingResourceByLine=function(t,e,i){i=i||50;for(var o=[],r=this._getLinePoints(e,i),a=this._getShortPointsInLine(t,e),s=0;s<r.length;s++){var n=null;n=s<r.length-1?r[s+1]:new NPMap.Geometry.Point(r[s].lon+(r[s].lon-r[s-1].lon)/2,r[s].lat+(r[s].lat-r[s-1].lat)/2);for(var l=this._findShortestMarker(a,r[s],n,i),h=l.length-1;h>=0;h--){for(var p=!1,c=0;c<o.length;c++)o[c]&&o[c].id===l[h].id&&(p=!0);!p&&l[h]&&o.push(l[h])}}for(s=0;s<t.length;s++){for(p=!1,c=0;c<o.length;c++)o[c].id===t[s].id&&(p=!0);p||o.push(t[s])}return o},MapPlatForm.Base.MapGeometry.prototype._getLinePoints=function(t,e){for(var i=t.getPath(),o=[],r=0;r<i.length-1;r++){s_points=this._splitPoints(i[r],i[r+1],e);for(var a=0;a<s_points.length;a++)o.push(s_points[a])}return o},MapPlatForm.Base.MapGeometry.prototype._splitPoints=function(t,e,i){var o=[t],r=this.map.getDistance(t,e,"4326");if(r<=i)return[t,e];var a,s,n=0;i>0&&(n=Math.ceil(r/i));for(var l=1;l<n;l++){a=l/n;var h=parseFloat((e.lon-t.lon)*a)+parseFloat(t.lon),p=parseFloat((e.lat-t.lat)*a)+parseFloat(t.lat);s=new NPMap.Geometry.Point(h,p),o.push(s)}return o},MapPlatForm.Base.MapGeometry.prototype._getShortPointsInLine=function(t,e){for(var i=[],o=0;o<t.length;o++){var r=null;r=t[o].longitude&&t[o].latitude?new NPMap.Geometry.Point(t[o].longitude,t[o].latitude):t[o].getPosition();var a=e.getPath(),s=999999,n=null;sline=null;for(var l=0;l<a.length-1;l++){var h=this._getSibgleShortPointInLine(r.lon,r.lat,a[l].lon,a[l].lat,a[l+1].lon,a[l+1].lat),p=this._calculateEuclideanDistance(h.lon,h.lat,r.lon,r.lat);s>p&&(s=p,n=h,sline=new NPMap.Geometry.Polyline([a[l],a[l+1]]))}i.push({key:o,shortPoint:n,data:t[o]})}return i},MapPlatForm.Base.MapGeometry.prototype._findShortestMarker=function(t,e,i,o){for(var r=[],a=0;a<t.length;a++){var s=t[a].shortPoint,n=this.map.getDistance(s,e,"4326"),l=this.map.getDistance(s,i,"4326"),h=this.map.getDistance(e,i,"4326");n<o/2&&(l*l>n*n+h*h&&(n=0-n),r.push({distance:n,marker:t[a].data}))}var p=[];for(a=(r=r.sort((function(t,e){return t.distance-e.distance}))).length-1;a>=0;a--)p.push(r[a].marker);return p},MapPlatForm.Base.MapGeometry.prototype._getSibgleShortPointInLine=function(t,e,i,o,r,a){var s,n,l;if((s=this._calculateEuclideanDistance(i,o,r,a))===(n=this._calculateEuclideanDistance(i,o,t,e))+(l=this._calculateEuclideanDistance(r,a,t,e)))return new NPMap.Geometry.Point(t,e);if(s<=1e-6)return new NPMap.Geometry.Point(i,o);if(l*l>=s*s+n*n)return new NPMap.Geometry.Point(i,o);if(n*n>=s*s+l*l)return new NPMap.Geometry.Point(r,a);if(i==r)return new NPMap.Geometry.Point(i,e);var h=(a-o)/(r-i),p=(t+(e-o)*h+h*h*i)/(h*h+1),c=h*p-h*i+o;return new NPMap.Geometry.Point(p,c)},MapPlatForm.Base.MapGeometry.prototype._calculateEuclideanDistance=function(t,e,i,o){return Math.sqrt((t-i)*(t-i)+(e-o)*(e-o))},MapPlatForm.Base.MapGeometry.prototype.makeSortResourcesWorker=function(){var t=this,e=this._sortResourcesWork,i={},o=new Worker(URL.createObjectURL(new Blob(["(".concat(e.toString(),")()")])));return o.onmessage=function(e){_newArrowCheck(this,t);var o=e.data,r=o.result,a=o.jobId;i[a](r),delete i[a]}.bind(this),function(){for(var t=this,e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];return new Promise(function(e){_newArrowCheck(this,t);var a=String(Math.random());i[a]=e,o.postMessage({jobId:a,message:r})}.bind(this))}},MapPlatForm.Base.MapGeometry.prototype._sortResourcesWork=function(){function t(t,e,i){var o=[t],r=a(t[1],t[0],e[1],e[0]),s=Math.ceil(r/i);if(0!==s)for(var n=e[0]-t[0],l=e[1]-t[1],h=1;h<s;h++){var p=h/s,c=n*p+t[0],u=l*p+t[1];o.push([c,u])}return o}function e(t,e,i,o){for(var r=[],s=e[0],n=e[1],l=i[0],h=i[1],p=0,c=t.length;p<c;p++){var u=t[p],m=u.footPoint,d=m[0],y=m[1],_=u.resource,f=a(y,d,n,s),g=a(y,d,h,l),v=a(n,s,h,l);f<o&&(g*g>f*f+v*v&&(f=0-f),r.push({distance:f,resource:_}))}for(var M=[],P=(r=r.sort((function(t,e){return t.distance-e.distance}))).length-1;P>=0;P--)M.push(r[P].resource);return M}function i(t,e,i,r,a,s){var n=o(i,r,a,s),l=o(i,r,t,e),h=o(a,s,t,e);if(n===l+h)return[t,e];if(n<=1e-6)return[i,r];if(h*h>=n*n+l*l)return[i,r];if(l*l>=n*n+h*h)return[a,s];var p=(s-r)/(a-i),c=(t+(e-r)*p+p*p*i)/(p*p+1);return[c,p*c-p*i+r]}function o(t,e,i,o){return Math.sqrt((t-i)*(t-i)+(e-o)*(e-o))}function r(t){return t*Math.PI/180}function a(t,e,i,o){var a=r(t),s=r(i),n=a-s,l=r(e)-r(o),h=2*Math.asin(Math.sqrt(Math.pow(Math.sin(n/2),2)+Math.cos(a)*Math.cos(s)*Math.pow(Math.sin(l/2),2)));return 1e3*(h*=6378.137)}onmessage=function(r){for(var a=r.data,s=a.jobId,n=a.message,l=[],h=n[0],p=n[1],c=n[2]||50,u=(c=c<0?Math.abs(c):c)/2,m=function(e,i){for(var o=[],r=0,a=e.length-1;r<a;r++)o.push.apply(o,t(e[r],e[r+1],i));return o}(p,c),d=function(t,e){for(var r=[],a=e.length-1,s=0,n=t.length;s<n;s++){for(var l=t[s],h=Number.MAX_VALUE,p=null,c=0;c<a;c++){var u=i(l.longitude,l.latitude,e[c][0],e[c][1],e[c+1][0],e[c+1][1]),m=o(u[0],u[1],l.longitude,l.latitude);h>m&&(h=m,p=u)}r.push({key:s,footPoint:p,resource:l})}return r}(h,m),y=m.length,_=0;_<y;_++){var f=m[_],g=null;if(_<y-1)g=m[_+1];else{var v=m[_-1];g=[f[0]+(f[0]-v[0])/2,f[1]+(f[1]-v[1])/2]}for(var M=e(d,f,g,u),P=M.length-1;P>=0;P--){for(var b=!1,x=M[P],w=0,S=l.length;w<S;w++){var L=l[w];if(L&&L.id===x.id){b=!0;break}}!b&&x&&l.push(x)}}postMessage({jobId:s,result:l}),self.close()}},function(){MapPlatForm.Base.MapService=function(t,e,i){this.CLASS_NAME="MapService",this._currentService=null,this.map=t,this.mapConfig=new MapPlatForm.Base.MapConfig(e,i),this.mapGeometry=new MapPlatForm.Base.MapGeometry(this.map),this.routeService=null},MapPlatForm.Base.MapService.prototype.queryRoadByName=function(roadName,callBack){var url=this.mapConfig.dataServiceURL+"query/getRoadsByName",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params={roadName:roadName},this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadByNameService=service.query(url,params,(function(result){for(var lines=[],i=result.length-1;i>=0;i--){var geometry=eval("("+result[i].feature+")"),obj={},line=GeoJSON.read(geometry);if(line instanceof Array)for(var j=line.length-1;j>=0;j--)line[j].data=result[i];else line.data=result[i];obj.name=result[i].name,obj.geometry=line,lines.push(obj)}callBack instanceof Function&&callBack(lines)}))},MapPlatForm.Base.MapService.prototype.getGeometryBuffer=function(t,e,i){var o=this.mapConfig.dataServiceURL+"gis/buffer",r=new NPMap.Services.bufferParams;r.projection=this.map.getProjection(),r.distance=e,r.units="m",r.geometry=t;var a=new NPMap.Services.BufferService(this.map,NPMap.MAPTYPE_NPGIS);this.geometryBufferService&&(this.geometryBufferService.abort(),this.geometryBufferService=null),this.geometryBufferService=a.buffer(o,r,i)},MapPlatForm.Base.MapService.prototype.queryPOIByName=function(name,callBack,maxResult,rowIndex){if(this.queryPOIByNameService&&(this.queryPOIByNameService.abort(),this.queryPOIByNameService=null),"gaode"!=MapPlatForm.ServiceType)if("minemap"!=MapPlatForm.ServiceType){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.keyWord=name,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByNameService=service.query(url,params,(function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result)}))}else{var tempUrl=this.mapConfig.otherServiceURL+"search/keyword";this.queryPOIByNameService=queryPOIByMinMapServer(tempUrl,{key:name,searchType:"poi",source:3,pageCount:maxResult,pageNumber:rowIndex,token:MapPlatForm.AK},(function(t,e){callBack(t,e)}))}else{var tempUrl=this.mapConfig.otherServiceURL+"search/poi";this.queryPOIByNameService=queryPOIByGaodeServer(tempUrl,{query:name,region:"全国",page_size:maxResult,page_num:rowIndex,ak:MapPlatForm.AK},(function(t,e){callBack(t,e)}))}},MapPlatForm.Base.MapService.prototype.queryPOIByCoord=function(point,callBack){if(this.queryPOIByCoordService&&(this.queryPOIByCoordService.abort(),this.queryPOIByCoordService=null),"gaode"!=MapPlatForm.ServiceType)if("minemap"!=MapPlatForm.ServiceType){var url=this.mapConfig.dataServiceURL+"query/poicoord",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params={coord:point.lon+","+point.lat},this.queryPOIByCoordService=service.query(url,params,(function(result){var point;if(result&&result.geometry){var geometry=eval("("+result.geometry+")");"Point"!==geometry.type&&"point"!==geometry.type||(point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]),point.data=result)}callBack instanceof Function&&callBack(point)}))}else{var tempUrl=this.mapConfig.otherServiceURL+"coder/reverseGeocoding";this.queryPOIByCoordService=queryGeoByMinMapServer(tempUrl,{location:point.lon+","+point.lat,type:1,radius:1e3,roadRadius:1e3,kind:0,source:3,token:MapPlatForm.AK},(function(t){callBack(t)}))}else{var tempUrl=this.mapConfig.otherServiceURL+"rgeo";this.queryPOIByCoordService=queryGeoByGaodeServer(tempUrl,{location:point.lon+","+point.lat,pois:1,ak:MapPlatForm.AK},(function(t){callBack(t)}))}},MapPlatForm.Base.MapService.prototype.addPOI=function(t,e){var i=this.mapConfig.dataServiceURL+"query/addPoi",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={name:t.data.name,poiType:t.data.type,address:t.data.address,x:t.lon,y:t.lat},this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.addPOIService=o.updata(i,r,(function(i){i?(t.data=i,e instanceof Function&&e(t)):e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.updataPOI=function(t,e){var i=this.mapConfig.dataServiceURL+"query/updataPoi",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={gid:t.data.gid,name:t.data.name,poiType:t.data.type,address:t.data.address,x:t.lon,y:t.lat},this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this.updataPOIService=o.updata(i,r,(function(i){i?(t.data=i,e instanceof Function&&e(t)):e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.queryPOIByGeometry=function(geometry,callBack,maxResult,rowIndex){if("gaode"==MapPlatForm.ServiceType){for(var tempUrl=this.mapConfig.otherServiceURL+"search/poi",geoStr="",points=geometry.getPath(),i=0;i<points.length;i++)geoStr+=points[i].lon+","+points[i].lat+";";return geoStr=geoStr.substring(0,geoStr.length-1),void queryPOIByGaodeServer(tempUrl,{query:name,regionType:"polygon",bounds:geoStr,page_size:maxResult,page_num:rowIndex,ak:MapPlatForm.AK},(function(t,e){callBack(t,e)}))}var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.queryPOIByGeometryService=service.query(url,params,(function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)}))},MapPlatForm.Base.MapService.prototype.queryPOIByFilter=function(filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.keyWord=fs[1],this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.queryPOIByFilterService=service.query(url,params,(function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)}))},MapPlatForm.Base.MapService.prototype.queryPOIByGeometryAndFilter=function(geometry,filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.key=fs[1],params.requestType="post",this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryPOIByGeometryAndFilterService=service.query(url,params,(function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)}))},MapPlatForm.Base.MapService.prototype.queryRoadInterByGeometry=function(geometry,callBack){var url=this.mapConfig.dataServiceURL+"query/roadInterByGeo",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,params.requestType="post",this.queryRoadInterByGeometryService&&(this.queryRoadInterByGeometryService.abort(),this.queryRoadInterByGeometryService=null),this.queryRoadInterByGeometryService=service.query(url,params,(function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points)}))},MapPlatForm.Base.MapService.prototype.queryRoadCrossByName=function(t,e){var i=this.mapConfig.dataServiceURL+"query/getRoadCrossByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r.roadName=t,this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this.queryRoadCrossByNameService=o.query(i,r,(function(t){for(var i=[],o=0;o<t.length;o++){var r=new NPMap.Geometry.Point(t[o].lon,t[o].lat);r.data=t[o],i.push(r)}e instanceof Function&&e(i)}))},MapPlatForm.Base.MapService.prototype.queryRoadCrossByGeometry=function(t,e){var i=this.mapGeometry.getWKTByGeometry(t),o=this.mapConfig.dataServiceURL+"query/searchRoadCrossInBounds",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a=new NPMap.Services.queryParams;a.wkt=i,this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryRoadCrossByGeometryService=r.query(o,a,(function(t){for(var i=[],o=0;o<t.data.length;o++){var r=new NPMap.Geometry.Point(t.data[o].lon,t.data[o].lat);r.data=t.data,i.push(r)}e instanceof Function&&e(i)}))},MapPlatForm.Base.MapService.prototype.queryAllFeaturesByName=function(t,e){if(this.queryFeaturesByNameService&&(this.queryFeaturesByNameService.abort(),this.queryFeaturesByNameService=null),"gaode"!=MapPlatForm.ServiceType){var i=this.mapConfig.dataServiceURL+"query/getFOIByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r.keyWordString=t,this.queryFeaturesByNameService=o.query(i,r,(function(t){for(var i=new MapPlatForm.Base.MapGeometry(this.map),o=[],r=0;r<t.length;r++){var a=null,s={};"road"===t[r].type?(s.address=t[r].address,a=i.getGeometryByGeoJson(t[r].feature)):"poi"===t[r].type?(s.address=t[r].address,a=i.getGeometryByGeoJson(t[r].wkt)):a=i.getGeometryByGeoJson(t[r].wkt),s.name=t[r].name,s.type=t[r].type,s.geometry=a,o.push(s)}e instanceof Function&&e(o)}))}else{var a=this.mapConfig.otherServiceURL+"search/poi";this.queryFeaturesByNameService=queryPOIByGaodeServer(a,{query:t,region:"全国",page_size:10,page_num:1,ak:MapPlatForm.AK},(function(t,i){for(var o=[],r=0;r<t.length;r++){var a={};a.name=t[r].data.name,a.address=t[r].data.address,a.type="poi",a.geometry=t[r],o.push(a)}e(o)}))}},MapPlatForm.Base.MapService.prototype.addRoadCross=function(t){var e=this.mapConfig.dataServiceURL+"query/addRoadCross",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={name:t.data.name,x:t.lon,y:t.lat},this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.addRoadCrossService=i.updata(e,o,(function(t){t?((void 0).data=t,callBack instanceof Function&&callBack(void 0)):callBack instanceof Function&&callBack(null)}))},MapPlatForm.Base.MapService.prototype.updataRoadCross=function(t){var e=this.mapConfig.dataServiceURL+"query/updataRoadCross",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={gid:t.data.gid,name:t.data.name,x:t.lon,y:t.lat},this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this._updataRoadCrossService=i.updata(e,o,(function(t){t?((void 0).data=t,callBack instanceof Function&&callBack(void 0)):callBack instanceof Function&&callBack(null)}))},MapPlatForm.Base.MapService.prototype.searchRouteByCoor=function(t,e){var i=null,o=new NPMap.Services.routeParams,r=this.mapConfig.dataServiceURL+"/gis/na";"gaode"==MapPlatForm.ServiceType?(r=this.mapConfig.otherServiceURL,i=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_ARCGISTILE),o.extendURL="route/",o.ak=MapPlatForm.AK):(i=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_GEOSERVER),o.service="na",o.request="getroute",o.networkName="shanghai_roadnet_supermap",o.geoBarriers=[],o.algorithm="Dijkstra"),o.startStop=t.startStop,o.endStop=t.endStop,o.trafficModel=t.trafficModel,o.planRoadType=t.planRoadType,this.routeService&&(this.routeService.abort(),this.routeService=null),this.routeService=i.route(r,o,e)},MapPlatForm.Base.MapService.prototype.searchRouteByMultiPoints=function(t,e,i){for(var o=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_GEOSERVER),r=new NPMap.Services.routeParams,a="",s=0;s<t.length;s++)a+=t[s].lon+","+t[s].lat+";";a=a.substr(0,a.length-1),r.stops=a,this.routeMultiService&&(this.routeMultiService.abort(),this.routeMultiService=null),this.routeMultiService=o.routeByMultPoints(this.mapConfig.dataServiceURL+"/gis/routing",r,(function(t){var i=[];if(t&&t instanceof Array)for(var o=0;o<t.length;o++){for(var r=[],a=t[o].expend.split(";"),s=0;s<a.length;s++){var n=a[s].split(","),l=parseFloat(n[0]),h=parseFloat(n[1]);r.push(new NPMap.Geometry.Point(l,h))}var p=new NPMap.Geometry.Polyline(r);p.setData({length:t[o].length,start:new NPMap.Geometry.Point(parseFloat(t[o].start.split(",")[0]),parseFloat(t[o].start.split(",")[1])),end:new NPMap.Geometry.Point(parseFloat(t[o].end.split(",")[0]),parseFloat(t[o].end.split(",")[1]))}),i.push(p)}e(i)}),i)},MapPlatForm.Base.MapService.prototype.searchDistrictsByID=function(t,e){var i=this.mapConfig.dataServiceURL+"query/getRegionalBound",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={addvcd:t},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var a=this;this._searchDistrictsService=o.query(i,r,(function(t){if(t){if(t.geometry&&(t.geometry=a.mapGeometry.getGeometryByGeoJson(t.geometry)),t.districts)for(var i=0;i<t.districts.length;i++)t.districts[i].geometry&&(t.districts[i].geometry=a.mapGeometry.getGeometryByGeoJson(t.districts[i].geometry));e instanceof Function&&e(t)}else e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.searchDistrictsByName=function(t,e){var i=this.mapConfig.dataServiceURL+"query/getRegionalBoundByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={name:t},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var a=this;this._searchDistrictsService=o.query(i,r,(function(t){if(t){if(t.geometry&&(t.geometry=a.mapGeometry.getGeometryByGeoJson(t.geometry)),t.districts)for(var i=0;i<t.districts.length;i++)t.districts[i].geometry&&(t.districts[i].geometry=a.mapGeometry.getGeometryByGeoJson(t.districts[i].geometry));e instanceof Function&&e(t)}else e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.searchPanoramaByPoint=function(t,e,i){var o=this.mapConfig.dataServiceURL+"panorama/getConfigsByPosition",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a=new NPMap.Services.queryParams;a={position:this.mapGeometry.getWKTByGeometry(t),distance:e},this._searchPanoramaByPoint&&(this._searchPanoramaByPoint.abort(),this._searchPanoramaByPoint=null);this._searchPanoramaByPoint=r.query(o,a,(function(t){t?i instanceof Function&&i(t):i instanceof Function&&i(null)}))},MapPlatForm.Base.MapService.prototype.searchSNPanoramaPoints=function(t){var e=this.mapConfig.dataServiceURL+"panorama/getAllSnPoints",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={},this._searchSNPanoramaPoints&&(this._searchSNPanoramaPoints.abort(),this._searchSNPanoramaPoints=null);this._searchSNPanoramaPoints=i.query(e,o,(function(e){e?t instanceof Function&&t(e):t instanceof Function&&t(null)}))},MapPlatForm.Base.MapService.prototype.searchSNPanoramaByPointID=function(t,e){var i=this.mapConfig.dataServiceURL+"panorama/getSnConfigsByParentId",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={parentid:t},this._searchSNPanoramaByPointID&&(this._searchSNPanoramaByPointID.abort(),this._searchSNPanoramaByPointID=null);this._searchSNPanoramaByPointID=o.query(i,r,(function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.queryRoomFloor=function(t,e,i){var o=this.mapConfig.dataServiceURL+"indoormap/getFloorByPidAndName",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a={pid:t,floorName:e};this._queryRoomFloor&&(this._queryRoomFloor.abort(),this._queryRoomFloor=null),this._queryRoomFloor=r.query(o,a,(function(t){t?i instanceof Function&&i(t):i instanceof Function&&i(null)}))},MapPlatForm.Base.MapService.prototype.queryRoomFloorsByGeometry=function(t,e){var i="";if(t instanceof NPMap.Geometry.Polygon){i=WKT.write(t);var o=this.mapConfig.dataServiceURL+"indoormap/listFloorByBounds",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a={bounds:i};this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryRoomFloorsByExtent=r.query(o,a,(function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)}))}},MapPlatForm.Base.MapService.prototype.queryBuildingRoomList=function(t,e){var i=this.mapConfig.dataServiceURL+"indoormap/listBuilding",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS);t={pageSize:(t=t||{}).pageSize,recordIndex:t.pageIndex},this._queryBuildingRoomList&&(this._queryBuildingRoomList.abort(),this._queryBuildingRoomList=null),this._queryBuildingRoomList=o.query(i,t,(function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.queryBuildingByName=function(t,e){var i=this.mapConfig.dataServiceURL+"indoormap/getBuildingByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r={name:t};this._queryBuildingByName&&(this._queryBuildingByName.abort(),this._queryBuildingByName=null),this._queryBuildingByName=o.query(i,r,(function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)}))},MapPlatForm.Base.MapService.prototype.cancelService=function(){this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryRoomFloor&&(this._queryRoomFloor.abort(),this._queryRoomFloor=null),this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryBuildingByName&&(this._queryBuildingByName.abort(),this._queryBuildingByName=null),this.routeService&&(this.routeService.abort(),this.routeService=null),this.queryRoadInterByGeometryService&&(this.queryRoadInterByGeometryService.abort(),this.queryRoadInterByGeometryService=null)};var queryPOIByGaodeServer=function queryPOIByGaodeServer(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,(function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),points=[],features=[];if(result.results)for(var i=0;i<result.results.length;i++){var obj=result.results[i],point=new NPMap.Geometry.Point(parseFloat(obj.location.lng),parseFloat(obj.location.lat)),feature={gid:obj.uid,address:obj.address,name:obj.name,telephone:obj.telephone,geometry:'{"type":"Point","coordinates":['+obj.location.lng+","+obj.location.lat+"]}"};point.data=feature,points.push(point)}successCallback(points,{totalCount:result.total,features:features})}}),(function(t){"function"==typeof errorCallback&&errorCallback(t)}))},queryGeoByGaodeServer=function queryGeoByGaodeServer(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,(function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),point;if(result.results){var obj=result.results[0],point=new NPMap.Geometry.Point(parseFloat(obj.location.lng),parseFloat(obj.location.lat)),name=obj.formatted_address;obj.pois&&obj.pois.length>0&&(name=obj.pois[0].name);var feature={address:obj.formatted_address,name:name,geometry:'{"type":"Point","coordinates":['+obj.location.lng+","+obj.location.lat+"]}"};point.data=feature}successCallback(point)}}),(function(t){"function"==typeof errorCallback&&errorCallback(t)}))},queryPOIByMinMapServer=function queryPOIByMinMapServer(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,(function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),points=[],features=[];if(result=result.data,result.rows)for(var i=0;i<result.rows.length;i++){var obj=result.rows[i],point=new NPMap.Geometry.Point(parseFloat(obj.geom.coordinates[0]),parseFloat(obj.geom.coordinates[1])),feature={gid:obj.id,address:obj.address,name:obj.name,telephone:obj.tel,geometry:'{"type":"Point","coordinates":['+obj.geom.coordinates[0]+","+obj.geom.coordinates[1]+"]}"};point.data=feature,points.push(point)}successCallback(points,{totalCount:result.total,features:features})}}),(function(t){"function"==typeof errorCallback&&errorCallback(t)}))},queryGeoByMinMapServer=function queryGeoByMinMapServer(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,(function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),point,xy=params.location.split(",");if(result.data){var obj=result.data,point=new NPMap.Geometry.Point(parseFloat(xy[0]),parseFloat(xy[1])),name=obj.restName,feature={address:obj.city+obj.dist+obj.town+obj.village+obj.restName,name:name,geometry:'{"type":"Point","coordinates":['+params.location+"]}"};point.data=feature}successCallback(point)}}),(function(t){"function"==typeof errorCallback&&errorCallback(t)}))}}(),MapPlatForm.Base.MapTag=function(t,e){this.CLASS_NAME="MapTag",this.layer=e,this.map=t,this._activeMarker=null,this.callback=null,this._mapGeometry=new MapPlatForm.Base.MapGeometry(t),this.markerParam=null,this.layer||(this.layer=this.map.getDefaultLayer()),that=this,this.isclick=!1},MapPlatForm.Base.MapTag.prototype._clickCallBack=function(){if(!that.isclick&&that._activeMarker){that.isclick=!0;var t=that._mapGeometry.createMarker(that._activeMarker.getPosition(),that.markerParam);that.layer.addOverlay(t),that.delAdrawMarker(),that.callback&&that.callback instanceof Function&&that.callback(t)}else that.delAdrawMarker()},MapPlatForm.Base.MapTag.prototype._moveCallBack=function(t){var e=t.object.getLonLatFromPixel(t.xy),i=new NPMap.Geometry.Point(e.lon,e.lat);i=NPMap.T.getPoint(that.map,i),that._activeMarker?that.isclick||that._activeMarker.setPosition(i):(that._activeMarker=that._mapGeometry.createMarker(i,that.markerParam),that.layer.addOverlay(that._activeMarker),that._activeMarker.addEventListener("click",that._clickCallBack),that._activeMarker.addEventListener("rightclick",that._rigthClickCallBack))},MapPlatForm.Base.MapTag.prototype._rigthClickCallBack=function(){that._activeMarker&&(that.delAdrawMarker(),that.cancelCallback&&that.cancelCallback())},MapPlatForm.Base.MapTag.prototype._mouseMoveCallBack=function(t){that._activeMarker?that._activeMarker.setPosition(t):(that._activeMarker=that._mapGeometry.createMarker(t,that.markerParam),that.layer.addOverlay(that._activeMarker),that._activeMarker.addEventListener("click",that._clickCallBack),that._activeMarker.addEventListener("rightclick",that._rigthClickCallBack))},MapPlatForm.Base.MapTag.prototype.adrawMarker=function(t,e,i,o,r){this.markerParam=t,this.isclick=!1,this.delAdrawMarker(),this.callback=e,this.cancelCallback=r,this.layer.removeOverlay(this._activeMarker),this._activeMarker=null,this._contextHeight="20px","EN"===NPMap.CULTURE?(this.map.activateMouseContext("Click on add annotations, right click to cancel"),this._contextHeight="34px"):this.map.activateMouseContext("点击添加标注,右键取消");var a=this.map.getMouseContextStyle();if(a.height=this._contextHeight,i&&this.map.activateMouseContext(i),o)for(var s in o)a[s]=o[s];this.map.addEventListener(NPMap.MAP_EVENT_MOUSE_MOVE,that._mouseMoveCallBack),this.map.addEventListener(NPMap.MAP_EVENT_RIGHT_CLICK,that._rigthClickCallBack),this.map.addEventListener(NPMap.MAP_EVENT_CLICK,that._clickCallBack),this.map.getContainer().onmouseenter=function(){that._activeMarker&&that._activeMarker.show()},this.map.getContainer().onmouseleave=function(){that._activeMarker&&that._activeMarker.hide()}},MapPlatForm.Base.MapTag.prototype.delAdrawMarker=function(){this.map&&(this._activeMarker&&(this._activeMarker.removeEventListener("click",that._clickCallBack),this._activeMarker.removeEventListener("rightclick",that._rigthClickCallBack)),this.layer.removeOverlay(this._activeMarker),this.map.deactivateMouseContext(),this.map.removeEventListener("click",that._clickCallBack),this.map.removeEventListener("rightclick",that._rigthClickCallBack),this.map.removeEventListener("mousemove",that._mouseMoveCallBack))},MapPlatForm.Base.MapTag.prototype.removeDrawMarker=function(t){this.map&&t&&this.layer.removeOverlay(t)},MapPlatForm.Base.MapTag.prototype.removeDrawMarkers=function(t){var e=this;this.map&&t&&t.forEach(function(t){_newArrowCheck(this,e),this.layer.removeOverlay(t)}.bind(this))},MapPlatForm.Base.MapTools=function(t,e){this.CLASS_NAME="MapTools",this.map=t,this.measureTool=null,this.drawTool=null,this.searchCircle=null,this.editMarker=null,this.layer=e},MapPlatForm.Base.MapTools.prototype._initMeasureTool=function(){this.measureTool=new NPMap.Tools.MeasureTool(this.map)},MapPlatForm.Base.MapTools.prototype._getStyle=function(t){return t?t.cursor="crosshair":t={cursor:"crosshair"},t},MapPlatForm.Base.MapTools.prototype._initDrawTool=function(){this.drawTool=new NPMap.Tools.DrawingTool(this.map),this.map.MapTools=this},MapPlatForm.Base.MapTools.prototype.measureDistance=function(t,e){this.measureTool||this._initMeasureTool(),this.cancelDraw(),this.measureTool.setMode(NPMap.MEASURE_MODE_DISTANCE,t,e)},MapPlatForm.Base.MapTools.prototype.measureArea=function(t,e){this.measureTool||this._initMeasureTool(),this.cancelDraw(),this.measureTool.setMode(NPMap.MEASURE_MODE_AREA,t,e)},MapPlatForm.Base.MapTools.prototype.cancelMeasure=function(){this.measureTool&&this.measureTool.cancleMeasure()},MapPlatForm.Base.MapTools.prototype.cancelDraw=function(){this.drawTool&&this.drawTool.cancleDraw()},MapPlatForm.Base.MapTools.prototype.drawLine=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_POLYLINE,t,e)},MapPlatForm.Base.MapTools.prototype.drawRectangle=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_RECT,t,e)},MapPlatForm.Base.MapTools.prototype.drawCircle=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_CIRCLE,t,e)},MapPlatForm.Base.MapTools.prototype.drawPolygon=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_POLYLGON,t,e)},MapPlatForm.Base.MapTools.prototype.drawCircleByDiameter=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_CIRCLE_DIAMETER,t,e)},MapPlatForm.Base.MapTools.prototype.drawFreehand=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_FREEHAND,t,e)},MapPlatForm.Base.MapTools.prototype.addCircleSearchControl=function(t,e,i,o,r){this.removeCircleSearchControl();var a=1e3,s="米";"EN"===NPMap.CULTURE&&(s="M"),i?a=i:i=500,o||(o=5e3),r&&r>=i&&r<=o&&(a=r);var n=e;this.searchCircle=new NPMap.Geometry.Circle(t,a,{color:"#acb9d1",fillColor:"#6980bc",weight:2,opacity:1,fillOpacity:.2});var l=this.map.getDefaultLayer();(this.layer?this.layer:this.map.getDefaultLayer()).addOverlay(this.searchCircle);var h=new NPMap.Geometry.Size(76,24);this.map.addImages([["editCircle",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/editCircle.png"]]);var p=new NPMap.Symbols.Icon("editCircle",h);this.editMarker=new NPMap.Symbols.Marker(t),l.addOverlay(this.editMarker),this.editMarker.setIcon(p),this.editMarker.setOffset(new NPMap.Geometry.Size(25,-12));var c=new NPMap.Symbols.Label(a+s,{offset:new NPMap.Geometry.Size(45/14,-12/14)});c.setStyle({fontSize:12,fontFamily:"宋体",align:"left"}),this.editMarker.setLabel(c);var u=this.searchCircle.getCenter(),m=NPMap.T.helper.webMoctorJW2PM(u.lon,u.lat);m.lon=m.lon+a,u=NPMap.T.helper.inverseMercator(m.lon,m.lat),this.editMarker.setPosition(u),this.editMarker.isEnableEdit=!0,this._resetControlMarker(),this.editMarker.addEventListener("draging",this._updateCircle(i,o,s).bind(this)),this.editMarker.addEventListener("dragend",this._callback(n).bind(this)),this.map.addEventListener("moving",this._resetControlMarker.bind(this)),e&&e instanceof Function&&e(this.searchCircle)},MapPlatForm.Base.MapTools.prototype._updateCircle=function(t,e,i){return function(){var o=this.searchCircle.getCenter(),r=NPMap.T.helper.webMoctorJW2PM(o.lon,o.lat),a=this.editMarker.getPosition(),s=NPMap.T.helper.webMoctorJW2PM(a.lon,a.lat),n=Math.sqrt(Math.pow(s.lon-r.lon,2)+Math.pow(s.lat-r.lat,2));this.map._obj.transform.pitch=0,n<t?n=t:n>e&&(n=e);var l=this.editMarker.getLabel();l.setContent(Math.round(n)+i),this.editMarker.setLabel(l),this.searchCircle.setRadius(n),this._resetControlMarker()}},MapPlatForm.Base.MapTools.prototype._callback=function(t){return function(){this._resetControlMarker(),t&&t instanceof Function&&t(this.searchCircle)}},MapPlatForm.Base.MapTools.prototype._resetControlMarker=function(){if(this.searchCircle){for(var t,e,i=this.searchCircle.getPath(),o=[],r=0;r<i.length;r++){var a=this.map.pointToPixel(i[r]);o.push(a)}for(t=o[0].x,e=o[0],r=1;r<o.length;r++)t<o[r].x&&(e=o[r],t=o[r].x);var s=this.map.pixelToPoint({x:e.x,y:e.y});this.editMarker.setPosition(s)}},MapPlatForm.Base.MapTools.prototype.setCircleSearchControlRadius=function(t,e){if(this.searchCircle&&this.editMarker){var i="米";"EN"===NPMap.CULTURE&&(i="M");var o=this.searchCircle.getCenter(),r=NPMap.T.helper.webMoctorJW2PM(o.lon,o.lat);r.lon=r.lon+t,o=NPMap.T.helper.inverseMercator(r.lon,r.lat),this.editMarker.setPosition(o),this.editMarker.setPosition(o),this.searchCircle.setRadius(t);var a=this.editMarker.getLabel();a.setContent(Math.round(t)+i),this.editMarker.setLabel(a),this.editMarker.refresh();var s=this.searchCircle.getExtent();this.map.zoomToExtent(s),e&&e instanceof Function&&e(self.searchCircle)}},MapPlatForm.Base.MapTools.prototype.removeCircleSearchControl=function(){this.editMarker&&(this.editMarker.removeEventListener("dragend",this._callback),this.editMarker.removeEventListener("draging",this._updateCircle),this.map.removeEventListener("moving",this._resetControlMarker));var t=this.map.getDefaultLayer(),e=this.layer?this.layer:this.map.getDefaultLayer();t.removeOverlay(this.editMarker),e.removeOverlay(this.searchCircle),this.map.disableEditing()},function(){var t;MapPlatForm.Base.MapRoutePlan=function(e,i,o,r){this.CLASS_NAME="MapRoutePlan",this.map=e,this.layer=i||this.map.getDefaultLayer(),this.startMarker=null,this._planRoadType=1,this._trafficModel="car",this._currentPolyline=null,this.endMarker=null,this.editMarker=null,this._throughMarkerInfo={},this._routes=[],this._throughMarkerNum=0,this._routeIndex=0,this.dataServiceURL=o,this.otherServiceURL=r,this._mapService=new MapPlatForm.Base.MapService(e,o,r),this._mapGeometry=new MapPlatForm.Base.MapGeometry(e),this.result1=null,this.result2=null,this._researchCallback=function(){},this.routeGroup=this.layer.addGroup("route"),this.editGroup=this.layer.addGroup("route_edit"),this.defaultName="未知地址",this.flag=!1,t=this,"EN"===NPMap.CULTURE&&(this.defaultName="Unknown Position"),window.getElementsByClassName=function(t){for(var e=[],i=document.getElementsByTagName("*"),o=0;o<i.length;o++)i[o].className===t&&(e[e.length]=i[o]);return e}},MapPlatForm.Base.MapRoutePlan.prototype._addPath=function(t){if(t.features.length<1||t.features.length<1)this._errorCallBack&&this._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?this._errorCallBack("There is no suitable route, please re-drag！"):this._errorCallBack("没有合适的路线，请重新拖动！"));else{var e=t.features[0];e.setStyle({color:"red",weight:5}),this.routeGroup.addOverlay(e);var i=this;e.addEventListener("mousemove",(function(t,e){var o=new MapPlatForm.Base.MapGeometry(map);i.editMarker?(i.editMarker.setPosition(e),i.editMarker.show()):(i.editMarker=o.createMarker(e,{url:i._crossMarkerStyle.editImageUrl,size:{width:i._crossMarkerStyle.width,height:i._crossMarkerStyle.height},markerType:2}),i.editGroup.addOverlay(i.editMarker),i.editMarker.isEnableEdit=!0,i.editMarker.enableEditing())}))}},MapPlatForm.Base.MapRoutePlan.prototype.addEidtMarkerEvent=function(){this.editMarker&&this.editMarker.addEventListener(NPMap.MARKER_EVENT_DRAG_END,(function(t){_afterDrag(t)}))},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(t,e,i){var o=null,r=null,a=null;e?(r=e.startPosition,a=e.stopPosition):(o=_currentPolyline.getPath(),r=o[0],a=o[o.length-1]);var s={startStop:r,endStop:t,trafficModel:this._trafficModel,planRoadType:this._planRoadType},n=this;_mapService.searchRouteByCoor(s,(function(o){s={startStop:t,endStop:a,trafficModel:this._trafficModel,planRoadType:this._planRoadType},_mapService.searchRouteByCoor(s,(function(s){o.features.length<1||s.features.length<1?n._errorCallBack&&n._errorCallBack instanceof Function&&("EN"==NPMap.CULTURE?n._errorCallBack("There is no suitable route, please re-drag！"):n._errorCallBack("没有合适的路线，请重新拖动！")):_addRoutes(t,{result1:o,result2:s,startPosition:r,stopPosition:a,throughMarkerRelativeInfo:e,key:i})}))}))},MapPlatForm.Base.MapRoutePlan.prototype._addRoutes=function(t){this.routeGroup.addOverlay(t.features[0])},MapPlatForm.Base.MapRoutePlan.prototype._clearEditInfoOnMap=function(){this.editGroup&&(this.editGroup.removeAllOverlays(),this.editMarker=null),this.routeGroup&&(this.routeGroup.removeAllOverlays(),this.editMarker=null),this.map.closeAllInfoWindows()},MapPlatForm.Base.MapRoutePlan.prototype._queryRoute=function(){var t={startStop:this._startMarker.getPosition(),endStop:this._endMarker.getPosition(),trafficModel:this._trafficModel,planRoadType:this._planRoadType},e=this;this._mapService.searchRouteByCoor(t,(function(t){if(t.features.length<1)e._errorCallBack&&e._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?e._errorCallBack("There is no suitable route, please re-drag！"):e._errorCallBack("没有合适的路线，请重新拖动！"));else{var i=t.features[0];e._setPolylineStyle([i]),e.routeGroup.addOverlay(i),i.setData({index:e._routeIndex}),e._routeArray=[i],e._addEventToLines([i]),e._routes=[{index:e._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:i,routeInfo:t.messages}];var o={routes:e._routes,polyline:i};e._researchCallback(o)}}))},MapPlatForm.Base.MapRoutePlan.prototype._addEventToLines=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].addEventListener("mousemove",this._addMarker),t[i].addEventListener("mouseout",this._removeMarker)},MapPlatForm.Base.MapRoutePlan.prototype._removeMarker=function(){t.editGroup.removeOverlay(t.editMarker),t.editMarker=null},MapPlatForm.Base.MapRoutePlan.prototype._addMarker=function(e){if(!this.flag){var i=e.srcObject,o=new NPMap.Geometry.Point(e.lon,e.lat);t.editMarker?(t.editMarker.setPosition(o),t.editMarker.show()):(t.editMarker=t._mapGeometry.createMarker(o,{url:t._crossMarkerStyle.editImageUrl,size:{width:t._crossMarkerStyle.width,height:t._crossMarkerStyle.height},markerType:0}),t.editGroup.addOverlay(t.editMarker),t._dragEdit()),t.editMarker.setData({line:i})}},MapPlatForm.Base.MapRoutePlan.prototype._removeEventToLines=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].removeEventListener("mousemove",this._addMarker)},MapPlatForm.Base.MapRoutePlan.prototype._dragEdit=function(){if(this.editMarker){this.editMarker.isEnableEdit=!0;var t=this;this.map.ModifyFeatureControl&&!this.map.ModifyFeatureControl._active&&(this.map.ModifyFeatureControl.disableEdit(),this.map.ModifyFeatureControl=null),this.map.ModifyFeatureControl||(this.map.ModifyFeatureControl=new NPMap.Controls.ModifyFeatureControl("",this.editMarker)),this.editMarker.addEventListener("dragstart",(function(){this.flag=!0})),this.editMarker.addEventListener("dragend",(function(){this.flag=!1,t._currentPolyline=t.editMarker.getData().line,t._afterDrag(t.editMarker.getPosition()),t._removeMarker()}))}},MapPlatForm.Base.MapRoutePlan.prototype._getRelativeRoute=function(t){for(var e=null,i=null,o=0,r=this._routes.length;o<r;o++)if(this._routes[o].dragIconIndex.icon1===parseInt(t,10)||this._routes[o].dragIconIndex.icon2===parseInt(t,10))if(e){if(!i){i=this._routes[o];break}}else e=this._routes[o];return{route1:e,route2:i}},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(t,e){var i=null,o=null,r=null,a="add";if(e){var s=this._getRelativeRoute(e),n=s.route1.route.getPath(),l=s.route2.route.getPath();o=n[0],r=l[l.length-1],a="edit"}else o=(i=this._currentPolyline.getPath())[0],r=i[i.length-1],a="add";var h,p,c,u={startStop:o,endStop:t,trafficModel:this._trafficModel,planRoadType:this._planRoadType},m=this;this._mapService.searchRouteByCoor(u,(function(i){h=i,m._searchRouteByDrag(t,a,s,e,h,p,c)}));var d={startStop:t,endStop:r,trafficModel:this._trafficModel,planRoadType:this._planRoadType};new MapPlatForm.Base.MapService(this.map,this.dataServiceURL,this.otherServiceURL).searchRouteByCoor(d,(function(i){p=i,m._searchRouteByDrag(t,a,s,e,h,p,c)})),new MapPlatForm.Base.MapService(this.map,this.dataServiceURL,this.otherServiceURL).queryPOIByCoord(t,(function(i){c=i,m._searchRouteByDrag(t,a,s,e,h,p,c)}))},MapPlatForm.Base.MapRoutePlan.prototype._searchRouteByDrag=function(t,e,i,o,r,a,s){if(r&&a&&s){for(var n=this.routeGroup.getAllOverlayers(),l=[],h=0;h<n.length;h++)"NPMap.Geometry.Polyline"===n[h].CLASS_NAME&&l.push(n[h]);this._addEventToLines(l),r.features.length<1||a.features.length<1?this._errorCallBack&&this._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?this._errorCallBack("There is no suitable route, please re-drag！"):this._errorCallBack("没有合适的路线，请重新拖动！")):this._getAddressByCoor(t,{result1:r,result2:a,type:e,relativeRoutes:i,key:o},s)}},MapPlatForm.Base.MapRoutePlan.prototype._getAddressByCoor=function(t,e,i){"add"===e.type?this._afteDragAdd(t,e,i):this._afterDragEdit(t,e,i)},MapPlatForm.Base.MapRoutePlan.prototype._afteDragAdd=function(e,i,o){var r=i.result1.features[0],a=i.result2.features[0];this._setPolylineStyle([r,a]),r.setData({index:++this._routeIndex}),a.setData({index:++this._routeIndex}),this.routeGroup.addOverlay(r),this.routeGroup.addOverlay(a),this._addEventToLines([r,a]);var s=this._crossMarkerStyle.crossImageUrl,n=this._mapGeometry.createMarker(e,{url:s,size:{width:this._crossMarkerStyle.width,height:this._crossMarkerStyle.height},markerType:0});this.editGroup.addOverlay(n);var l=o.data.name,h=l||this.defaultName;this._throughMarkerNum++,n.setData({key:this._throughMarkerNum,name:h});var p=this._getRouteByIndex(this._currentPolyline.getData().index),c=this._addInfowin(h,this._throughMarkerNum,e);c.getBaseDiv().title=h,this._throughMarkerInfo[this._throughMarkerNum]={infoWindow:c,marker:n},n.isEnableEdit=!0;var u=this;n.addEventListener("mouseover",(function(){t.map.disableInertialDragging()})),n.addEventListener("mouseout",(function(){t.map.enableInertialDragging()})),n.addEventListener("dragend",(function(){var t=parseInt(n.getData().key,10);u._throughMarkerInfo[t].infoWindow.close(),u._afterDrag(n.getPosition(),t)})),i.result1.messages.startPointName=p.routeInfo.startPointName,i.result2.messages.startPointName=h,this._refreshRouteArray({route1:{index:r.getData().index,dragIconIndex:{icon1:p.dragIconIndex.icon1,icon2:this._throughMarkerNum},route:r,routeInfo:i.result1.messages},route2:{index:a.getData().index,dragIconIndex:{icon1:this._throughMarkerNum,icon2:p.dragIconIndex.icon2},route:a,routeInfo:i.result2.messages},key:i.key},i.type),this.routeGroup.removeOverlay(this._currentPolyline)},MapPlatForm.Base.MapRoutePlan.prototype._afterDragEdit=function(t,e,i){var o=e.relativeRoutes;this.routeGroup.removeOverlay(o.route1.route),this.routeGroup.removeOverlay(o.route2.route);var r=e.result1.features[0],a=e.result2.features[0];this._setPolylineStyle([r,a]),r.setData({index:o.route1.route.getData().index}),a.setData({index:o.route2.route.getData().index}),this.routeGroup.addOverlay(r),this.routeGroup.addOverlay(a),this._addEventToLines([r,a]);var s=i.data.name,n=s||this.defaultName;this._throughMarkerInfo[e.key].marker.setData({key:e.key,name:n}),o.route1.route=r,o.route1.routeInfo=e.result1.messages,o.route2.route=a,o.route2.routeInfo=e.result2.messages,o.route2.routeInfo.startPointName=n;var l=this._addInfowin(n,e.key,t);this._throughMarkerInfo[e.key].infoWindow=l,this._refreshRouteArray({route1:o.route1,route2:o.route2,key:e.key},e.type)},MapPlatForm.Base.MapRoutePlan.prototype._getThroughInfo=function(t,e){var i=document.createElement("div");i.style.fontSize="13px",i.style.border="1px solid #dfdfdf",i.style.borderRadius="5px",i.style.webkitBorderRadius="5px",i.style.height="21px",i.style.backgroundColor="#fff";var o=document.createElement("span");o.style.maxWidth="100px",o.style.overflow="hidden",o.style.whiteSpace="nowrap",o.style.textOverflow="ellipsis",o.style.float="left",o.style.marginLeft="2px",o.style.display="inline-block",o.style.lineHeight="21px",o.innerText=t;var r=document.createElement("i");return r.style.width="14px",r.style.height="14px",r.style.float="left",r.style.display="inline-block",r.style.marginLeft="5px",r.style.marginTop="3px",r.style.cursor="pointer",r.style.background="url("+NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/close1.png) no-repeat",r.className="infowindow-close",r.setAttribute("key",e),i.appendChild(o),i.appendChild(r),i},MapPlatForm.Base.MapRoutePlan.prototype._addInfowin=function(t,e,i){var o=this._getThroughInfo(t,e),r=new NPMap.Symbols.InfoWindow(i,"",o,{iscommon:!0,closeButton:!1,offset:new NPMap.Geometry.Size(7,-9)});this.map.addOverlay(r),r.open(),r.getBaseDiv().style.padding="2px 5px 2px";for(var a=document.getElementsByClassName("infowindow-close"),s=this,n=a.length-1;n>=0;n--)a[n].onclick=function(){key=this.getAttribute("key");var t=s._getRelativeRoute(key),e=t.route1.route.getPath(),i=t.route2.route.getPath(),o={startStop:e[0],endStop:i[i.length-1],trafficModel:s._trafficModel,planRoadType:s._planRoadType};s._mapService.searchRouteByCoor(o,(function(e){if(e.features[0]){var i=s._throughMarkerInfo[key];i.infoWindow.close(),s.editGroup.removeOverlay(i.marker),s.routeGroup.removeOverlay(t.route1.route),s.routeGroup.removeOverlay(t.route2.route);var o=e.features[0],r=++s._routeIndex;o.setData({index:r}),s._setPolylineStyle([o]),s.routeGroup.addOverlay(o),e.messages.startPointName=t.route1.routeInfo.startPointName,s._refreshRouteArray({route:{index:r,dragIconIndex:{icon1:t.route1.dragIconIndex.icon1,icon2:t.route2.dragIconIndex.icon2},route:e.features[0],routeInfo:e.messages},key:key},"delete"),s._addEventToLines([e.features[0]])}else s._errorCallBack()}))};return r},MapPlatForm.Base.MapRoutePlan.prototype._refreshRouteArray=function(t,e){var i,o=null,r=null;if("add"===e){for(var a=this._currentPolyline.getData().index,s=0,n=this._routes.length;s<n;s++)if(this._routes[s].index===parseInt(a,10)){i=s;break}this._routes.splice(i,1,t.route1),this._routes.splice(i+1,0,t.route2)}else if("edit"===e){for(s=0,n=this._routes.length;s<n;s++)if(this._routes[s].dragIconIndex.icon1===parseInt(t.key,10)||this._routes[s].dragIconIndex.icon2===parseInt(t.key,10))if(o){if(!r){r=this._routes[s],this._routes.splice(s,1,t.route2);break}}else o=this._routes[s],this._routes.splice(s,1,t.route1)}else for(s=0,n=this._routes.length;s<n;s++)if(this._routes[s].dragIconIndex.icon1===parseInt(t.key,10)||this._routes[s].dragIconIndex.icon2===parseInt(t.key,10))if(o){if(!r){r=this._routes[s],this._routes.splice(s,1);break}}else o=this._routes[s],this._routes.splice(s,1,t.route);var l,h=[];if(this._routes&&this._routes.length>0){for(s=0,n=this._routes.length;s<n;s++)h=h.concat(this._routes[s].route.getPath());l=new NPMap.Geometry.Polyline(h)}var p={routes:this._routes,polyline:l};this._researchCallback(p)},MapPlatForm.Base.MapRoutePlan.prototype._setPolylineStyle=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].setStyle(this._lineStyle)},MapPlatForm.Base.MapRoutePlan.prototype._getRouteByIndex=function(t){for(var e=null,i=0,o=this._routes.length;i<o;i++)if(this._routes[i].index===parseInt(t,10)){e=this._routes[i];break}return e},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfo=function(t,e){var i=e.startPosition,o=e.stopPosition;for(var r in this._throughMarkerInfo)if(t!==parseInt(r)){var a=this._throughMarkerInfo[r].startPosition,s=this._throughMarkerInfo[r].stopPosition,n=this._throughMarkerInfo[r].crossPoint;i.lat===a.lat&&i.lon===a.lon&&(this._throughMarkerInfo[r].startPosition=e.crossPoint,this._throughMarkerInfo[r].route1=e.route2),o.lat===s.lat&&o.lon===s.lon&&(this._throughMarkerInfo[r].stopPosition=e.crossPoint,this._throughMarkerInfo[r].route2=e.route1),i.lat===n.lat&&i.lon===n.lon&&(this._throughMarkerInfo[r].stopPosition=e.crossPoint,this._throughMarkerInfo[r].route2=e.route1),o.lat===n.lat&&o.lon===n.lon&&(this._throughMarkerInfo[r].startPosition=e.crossPoint,this._throughMarkerInfo[r].route1=e.route2)}},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfoDel=function(t,e,i){var o=e.crossPoint;for(var r in _throughMarkerInfo)if(t!==parseInt(r)){var a=this._throughMarkerInfo[r].startPosition,s=this._throughMarkerInfo[r].stopPosition;o.lat===a.lat&&o.lon===a.lon&&(this._throughMarkerInfo[r].startPosition=e.startPosition,this._throughMarkerInfo[r].route1=i),o.lat===s.lat&&o.lon===s.lon&&(this._throughMarkerInfo[r].stopPosition=e.stopPosition,this._throughMarkerInfo[r].route2=i)}},MapPlatForm.Base.MapRoutePlan.prototype.addRoutePlanControl=function(t,e,i){if(this._planRoadType=t.planRoadType?t.planRoadType:this._planRoadType,this._trafficModel=t.trafficModel?t.trafficModel:this._trafficModel,this._startMarker=t.startMarker,this._endMarker=t.endMarker,this._lineStyle=t.lineStyle?t.lineStyle:{color:"green",weight:10,opacity:.7},this.map.addImages([["path-cross",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/path-cross.png"],["path-edit",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/path-edit.png"]]),this._crossMarkerStyle=t.crossMarkerStyle?t.crossMarkerStyle:{crossImageUrl:"path-cross",editImageUrl:"path-edit",height:22,width:22},this._clearEditInfoOnMap(),this._researchCallback=e,this._errorCallBack=i,t.polyline){var o=t.polyline;this._setPolylineStyle([o]),this.routeGroup.addOverlay(o),o.setData({index:this._routeIndex}),this._routeArray=[o],this._addEventToLines([o]),this._routes=[{index:this._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:o,routeInfo:""}]}else this._queryRoute()},MapPlatForm.Base.MapRoutePlan.prototype.stopEdit=function(){for(var t=this.routeGroup.getAllOverlayers(),e=[],i=0;i<t.length;i++)"NPMap.Geometry.Polyline"===t[i].CLASS_NAME&&e.push(t[i]);for(var o in this._removeEventToLines(e),this.editGroup.removeOverlay(this.editMarker),this.editMarker=null,this._throughMarkerInfo)this._throughMarkerInfo[o].marker&&this._throughMarkerInfo[o].marker.disableEditing();var r=document.getElementsByClassName("infowindow-close");for(i=r.length-1;i>=0;i--)r[i].onclick=null},MapPlatForm.Base.MapRoutePlan.prototype.startEdit=function(){for(var t=this.routeGroup.getAllOverlayers(),e=[],i=0;i<t.length;i++)"NPMap.Geometry.Polyline"===t[i].CLASS_NAME&&e.push(t[i]);for(var o in this._addEventToLines(e),this._throughMarkerInfo)this._throughMarkerInfo[o].marker&&this._throughMarkerInfo[o].marker.enableEditing();var r=document.getElementsByClassName("infowindow-close"),a=this;for(i=r.length-1;i>=0;i--)r[i].onclick=function(){key=this.getAttribute("key");var t=a._getRelativeRoute(key),e=t.route1.route.getPath(),i=t.route2.route.getPath(),o={startStop:e[0],endStop:i[i.length-1],trafficModel:a._trafficModel,planRoadType:a._planRoadType};a._mapService.searchRouteByCoor(o,(function(e){if(e.features[0]){var i=a._throughMarkerInfo[key];i.infoWindow.close(),a.editGroup.removeOverlay(i.marker),a.routeGroup.removeOverlay(t.route1.route),a.routeGroup.removeOverlay(t.route2.route);var o=e.features[0],r=++a._routeIndex;o.setData({index:r}),a._setPolylineStyle([o]),a.routeGroup.addOverlay(o),e.messages.startPointName=t.route1.routeInfo.startPointName,a._refreshRouteArray({route:{index:r,dragIconIndex:{icon1:t.route1.dragIconIndex.icon1,icon2:t.route2.dragIconIndex.icon2},route:e.features[0],routeInfo:e.messages},key:key},"delete"),a._addEventToLines([e.features[0]])}else a._errorCallBack()}))}}}(),e=MapPlatForm.Base.AnimationLineManager=function(t){this._map=t,this.animationLineArr=[]},e.prototype.addAnimationLines=function(t){this.animationLineArr=t;for(var e,i=0,o=0;o<this.animationLineArr.length;o++){this.animationLineArr.length>1&&(this.animationLineArr[o].isSetCenter=!1);var r=this.animationLineArr[o].getAllSegCount();i<r&&(e=this.animationLineArr[o],i=r),this.animationLineArr[o].unRedraw=!0}e.unRedraw=!1},e.prototype.start=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].start()},e.prototype.stop=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].stop()},e.prototype.restart=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].restart()},e.prototype.continus=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].continus()},e.prototype.pause=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].pause()},function(t){"use strict";var e=function(t,e,i){if(this._line_layer_name="my-line-layer",this._line_layer_source="my-line-source",this._point_animate_layer_name="my-point-animate-layer",this._line_animate_layer_source="my-line-animate-source",this._line_animate_layer_name="my-line-animate-layer",this._flowIdentityBackFiled="flowIdentityBack",this._flowIdentityValueLength=1,this._flowIdentityPlayHandler=0,this._map=t._obj,this._opts=this._mergeOpts(i),this.__animateFps=1e3/this._opts.flowFps,this._animageLineSegLength=this._opts.flowLineSegLength,this._data=this._processedData(t,e),this._animatePointHandler=0,this._animatePointFlag=!0,this._animateLineHandler=0,this._animateLineFlag=!0,this._lineItemlengthArr=[],this._lineItemlengthArrBack=[],this._publisher=new MapPlatForm.Base.publisher,this._map.loaded())this._addLayers();else{var o=this;this._map.on("load",(function(){o._addLayers()}))}};e.prototype.subscribePlayTopic=function(t){this._publisher.subscribe("spriteLine-play","identity",t)},e.prototype.cancelPlayTopic=function(){this._publisher.cancel("spriteLine-play","identity")},e.prototype.destroy=function(){if("visible"===this._opts.lineVisibility&&(this._map.removeLayer(this._line_layer_name),this._map.removeSource(this._line_layer_source)),this._opts.flow){this.removeFlowIdentityPlay();var e=this;if(this._data.map((function(t){var i=t.properties;return void 0!==i[e._flowIdentityBackFiled]&&(i[e._opts.flowIdentity]=i[e._flowIdentityBackFiled],i[e._flowIdentityBackFiled]=void 0,t.properties=i),t})),"point"===this._opts.flowType)this._map.removeLayer(this._point_animate_layer_name),this._map.removeSource(this._point_animate_layer_name),this._animatePointFlag=!1,t.cancelAnimationFrame(this._animatePointHandler);else{for(var i=0,o=this._opts.animateLineLayerFilters.length;i<o;i++)this._map.removeLayer(this._line_animate_layer_name+"_"+i);this._map.removeSource(this._line_animate_layer_source),this._animateLineFlag=!1,t.cancelAnimationFrame(this._animateLineHandler)}}},e.prototype.show=function(){if("visible"===this._opts.lineVisibility&&this._map.setLayoutProperty(this._line_layer_name,"visibility","visible"),this._opts.flow)if("point"===this._opts.flowType)this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","visible");else for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","visible")},e.prototype.hide=function(){if("visible"===this._opts.lineVisibility&&this._map.setLayoutProperty(this._line_layer_name,"visibility","none"),this._opts.flow)if("point"===this._opts.flowType)this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","none");else for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","none")},e.prototype.disableFlow=function(){if(this._opts.flow)if("point"===this._opts.flowType)this._animatePointFlag&&(this._animatePointFlag=!1,this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","none"));else if(this._animateLineFlag){this._animateLineFlag=!1;for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","none")}},e.prototype.enableFlow=function(){if(this._opts.flow)if("point"===this._opts.flowType)this._animatePointFlag||(this._animatePointFlag=!0,this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","visible"),this._animatePoint());else if(!this._animateLineFlag){this._animateLineFlag=!0;for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","visible");this._animateLine()}},e.prototype.addFlow=function(){if(!this._opts.flow)if(this._opts.flow=!0,this._opts.flowIdentityPlay=!1,"point"===this._opts.flowType){this._animatePointFlag=!0;for(var t=JSON.parse('{"type":"FeatureCollection","features":[]}'),e=[],i=0,o=this._data.length;i<o;i++){var r={type:"Feature"},a={type:"Point",coordinates:(l=(p=this._data[i].geometry).coordinates)[0]};r.geometry=a,e.push(r)}t.features=e,this._addAnimatePointLayer(t)}else{this._animateLineFlag=!0;var s=JSON.parse('{"type":"FeatureCollection","features":[]}'),n=[];for(i=0,o=this._data.length;i<o;i++){var l,h={type:"Feature"},p=this._data[i].geometry,c=this._data[i].properties,u={type:"LineString",coordinates:[(l=p.coordinates)[l.length-1]]};h.geometry=u,h.properties=c,n.push(h)}s.features=n,this._addAnimateLineLayer(s)}},e.prototype.removeFlow=function(){if(this._opts.flow){if(this.removeFlowIdentityPlay(),"point"===this._opts.flowType)this._map.removeLayer(this._point_animate_layer_name),this._map.removeSource(this._point_animate_layer_name),this._animatePointFlag=!1,t.cancelAnimationFrame(this._animatePointHandler);else{for(var e=0,i=this._opts.animateLineLayerFilters.length;e<i;e++)this._map.removeLayer(this._line_animate_layer_name+"_"+e);this._map.removeSource(this._line_animate_layer_source),this._animateLineFlag=!1,t.cancelAnimationFrame(this._animateLineHandler)}this._opts.flow=!1}},e.prototype.addFlowIdentityPlay=function(){if(!this._opts.flow)throw"flow 属性必须为 true !";if(!this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength){var t=this._map.getSource(this._line_animate_layer_source)._data;this._cycleUpdateAnimateLineSourceFlowIdentityValue(t),this._opts.flowIdentityPlay=!0}},e.prototype.removeFlowIdentityPlay=function(){if(this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength){t.clearInterval(this._flowIdentityPlayHandler);var e=this._map.getSource(this._line_animate_layer_source)._data;this._updateAnimateLineSourceFlowIdentityValue(e,0),this._opts.flowIdentityPlay=!1}},e.prototype._mergeOpts=function(t){var e={lineColor:"#ff0000",lineWidth:1,lineOpacity:1,lineDasharray:[-1,-1],lineBlur:0,lineVisibility:"visible",flow:!0,flowType:"point",flowFps:60,flowIdentity:void 0,flowIdentityPlay:!1,flowIdentityPlayInterval:3e4,flowLineSegLength:15,flowLineSegStepCount:5,flowLineSegStep:1,flowLineColor:"#ffffff",flowLineWidth:2,flowLineOpacity:1,flowLineCap:"round",flowLinejoin:"round",flowLineDasharray:[-1,-1],flowLineBlur:0,flowPointColor:"#ffffff",flowPointSize:2,flowPointBlur:0,flowPointOpacity:1,flowPointStrokeWidth:0,flowPointStrokeColor:"#ffffff",flowPointStrokeOpacity:1};if(t)for(var i in t)t.hasOwnProperty(i)&&void 0!==t[i]&&(e[i]=t[i]);if(void 0!==e.flowLineGradient&&void 0===e.flowIdentity)throw"flowIdentity 属性不正确!";var o={flow:e.flow,flowType:e.flowType,flowFps:e.flowFps,flowIdentity:e.flowIdentity,flowIdentityPlay:e.flowIdentityPlay,flowIdentityPlayInterval:e.flowIdentityPlayInterval,flowLineSegLength:e.flowLineSegLength,flowLineSegStepCount:e.flowLineSegStepCount,flowLineSegStep:e.flowLineSegStep};o.lineLayerPaint={"line-color":e.lineColor,"line-width":e.lineWidth,"line-dasharray":e.lineDasharray,"line-opacity":e.lineOpacity,"line-blur":e.lineBlur},o.lineVisibility=e.lineVisibility;var r=this._animateLineLayerPaintAndFilterBuilder(e);return o.animateLineLayerPaints=r.animateLineLayerPaints,o.animateLineLayerFilters=r.animateLineLayerFilters,o.animateLineLayerLayout={"line-cap":e.flowLineCap,"line-join":e.flowLinejoin},o.animatePointLayerPaint={"circle-radius":e.flowPointSize,"circle-color":e.flowPointColor,"circle-blur":e.flowPointBlur,"circle-opacity":e.flowPointOpacity,"circle-stroke-width":e.flowPointStrokeWidth,"circle-stroke-color":e.flowPointStrokeColor,"circle-stroke-opacity":e.flowPointStrokeOpacity},o},e.prototype._animateLineLayerPaintAndFilterBuilder=function(t){var e=[],i=[],o=t.flowLineGradient;if(void 0!==o&&o.length>0)for(var r=o.length,a=0;a<r;a++){var s=o[a];if(2!==s.length)throw"flowLineGradient 格式错误!";var n,l=s[0],h=s[1];(n={})["line-width"]=t.flowLineWidth,n["line-opacity"]=t.flowLineOpacity,n["line-blur"]=t.flowLineBlur,void 0!==h?n["line-gradient"]=this._paintLineGradientBuilder(h):n["line-dasharray"]=t.flowLineDasharray,i[a]=n;var p,c=o[a+1];p=void 0===c?Number.MAX_VALUE:c[0],e[a]=["all",[">=",t.flowIdentity,l],["<",t.flowIdentity,p]]}else(n={})["line-color"]=t.flowLineColor,n["line-width"]=t.flowLineWidth,n["line-opacity"]=t.flowLineOpacity,n["line-blur"]=t.flowLineBlur,i[0]=n,e[0]=["==","$type","LineString"];return{animateLineLayerPaints:i,animateLineLayerFilters:e}},e.prototype._paintLineGradientBuilder=function(t){var e=["interpolate",["linear"],["line-progress"]],i=t.length;if(i>0)for(var o=0,r=i;o<r;o++){var a=1/r;e.push(a*o),e.push(t[o])}else e.push(0),e.push("blue"),e.push(1),e.push("red");return e},e.prototype._processedData=function(t,e){var i=this._convertData(t,e);return this._calculateLinesSeg(i)},e.prototype._convertData=function(t,e){for(var i=0,o=e.length;i<o;i++)e[i].geometry.coordinates=e[i].geometry.coordinates.map((function(e){var i=NPMap.T.setPoint(t,{lon:e[0],lat:e[1]});return[i.lon,i.lat]}));return e},e.prototype._calculateLinesSeg=function(t){for(var e=0,i=t.length;e<i;e++){for(var o=t[e].geometry.coordinates,r=[],a=0,s=o.length;a<s;a++){var n=this._calculateLineSeg(o,a);r=r.concat(n)}t[e].geometry.coordinates=r}return t},e.prototype._calculateLineSeg=function(t,e){var i=[];if(e+1>=t.length)return i.push(0),i;var o,r=t[e],a=t[e+1],s=this._getDistance(r,a),n=0;this._animageLineSegLength>0&&(n=Math.round(s/this._animageLineSegLength));for(var l=1;l<n;l++){o=l/n;var h=parseFloat((a[0]-r[0])*o)+parseFloat(r[0]),p=parseFloat((a[1]-r[1])*o)+parseFloat(r[1]);i.push([h,p])}return i.push(t[e+1]),i},e.prototype._getDistance=function(t,e){var i=NPMap.T.helper.webMoctorJW2PM(t[0],t[1]),o=NPMap.T.helper.webMoctorJW2PM(e[0],e[1]);return Math.sqrt((i.lon-o.lon)*(i.lon-o.lon)+(i.lat-o.lat)*(i.lat-o.lat))},e.prototype._cycleAnimatePoint=function(){var e=this;t.setTimeout((function(){e._animatePointHandler=t.requestAnimationFrame((function(){e._animatePoint()}))}),this.__animateFps)},e.prototype._animatePoint=function(){if(this._animatePointFlag){for(var t=this._map.getSource(this._point_animate_layer_name)._data,e=0,i=this._data.length;e<i;e++){var o=this._data[e].geometry.coordinates,r=this._lineItemlengthArr[e];0===r&&(r=this._lineItemlengthArr[e]=this._lineItemlengthArrBack[e]),t.features[e].geometry.coordinates=o[r],this._lineItemlengthArr[e]=this._lineItemlengthArr[e]-1}this._map.getSource(this._point_animate_layer_name).setData(t),this._cycleAnimatePoint()}},e.prototype._cycleAnimateLine=function(){var e=this;t.setTimeout((function(){e._animateLineHandler=t.requestAnimationFrame((function(){e._animateLine()}))}),this.__animateFps)},e.prototype._animateLine=function(){if(this._animateLineFlag){for(var t=this._map.getSource(this._line_animate_layer_source)._data,e=0,i=this._data.length;e<i;e++){var o=this._data[e].geometry.coordinates,r=this._lineItemlengthArr[e];r<0&&(r=this._lineItemlengthArr[e]=this._lineItemlengthArrBack[e],t.features[e].geometry.coordinates=[]);var a=t.features[e].geometry.coordinates;a.length===this._opts.flowLineSegStepCount&&a.shift(),a.push(o[r]),t.features[e].geometry.coordinates=a,this._lineItemlengthArr[e]=this._lineItemlengthArr[e]-this._opts.flowLineSegStep}this._map.getSource(this._line_animate_layer_source).setData(t),this._cycleAnimateLine()}},e.prototype._addLayers=function(){for(var t=JSON.parse('{"type":"geojson","data":{"type":"FeatureCollection","features":[]}}'),e=JSON.parse('{"type":"FeatureCollection","features":[]}'),i=JSON.parse('{"type":"FeatureCollection","lineMetrics":true,"features":[]}'),o=[],r=[],a=[],s=0,n=this._data.length;s<n;s++){var l={type:"Feature"},h={type:"Feature"},p={type:"Feature"},c=this._data[s].geometry,u=c.coordinates;"visible"===this._opts.lineVisibility&&(l.geometry=c,o.push(l));var m=u.length-1;this._lineItemlengthArr.push(m),this._lineItemlengthArrBack.push(m);var d={type:"Point",coordinates:u[0]};h.geometry=d,r.push(h);var y={type:"LineString",coordinates:[]};p.geometry=y,p.properties=this._processAnimateLineSourceProperties(this._data[s].properties),a.push(p)}t.data.features=o,e.features=r,i.features=a,"visible"===this._opts.lineVisibility&&this._addLineLayer(t),"line"===this._opts.flowType?(this._addAnimateLineLayer(i),this._opts.flow&&this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength&&this._cycleUpdateAnimateLineSourceFlowIdentityValue(i)):this._addAnimatePointLayer(e)},e.prototype._processAnimateLineSourceProperties=function(t){var e=t[this._opts.flowIdentity];return e instanceof Array&&(t[this._opts.flowIdentity]=e[0],t[this._flowIdentityBackFiled]=e,this._flowIdentityValueLength=e.length),t},e.prototype._updateAnimateLineSourceFlowIdentityValue=function(t,e){if(void 0!==t){var i=this;t.features.map((function(t){var o=t.properties;return o[i._opts.flowIdentity]=o[i._flowIdentityBackFiled][e],t.properties=o,t})),this._publisher.publish({name:"spriteLine-play",callback:"identity"},e)}},e.prototype._cycleUpdateAnimateLineSourceFlowIdentityValue=function(e){var i=0,o=this;this._flowIdentityPlayHandler=t.setInterval((function(){o._opts.flowIdentityPlay&&(i>o._flowIdentityValueLength-1&&(i=0),o._updateAnimateLineSourceFlowIdentityValue(e,i),i++)}),this._opts.flowIdentityPlayInterval)},e.prototype._addLineLayer=function(t){this._map.addSource(this._line_layer_source,t);var e=this._getBuildingLayerId();this._map.addLayer({id:this._line_layer_name,type:"line",source:this._line_layer_source,paint:this._opts.lineLayerPaint,filter:["==","$type","LineString"]},e)},e.prototype._addAnimateLineLayer=function(t){if(this._opts.flow){this._map.addSource(this._line_animate_layer_source,{type:"geojson",lineMetrics:!0,data:t});for(var e=this._getBuildingLayerId(),i=0,o=this._opts.animateLineLayerFilters.length;i<o;i++)this._map.addLayer({id:this._line_animate_layer_name+"_"+i,type:"line",source:this._line_animate_layer_source,filter:this._opts.animateLineLayerFilters[i],layout:this._opts.animateLineLayerLayout,paint:this._opts.animateLineLayerPaints[i]},e);this._animateLine()}},e.prototype._addAnimatePointLayer=function(t){if(this._opts.flow){var e=this._getBuildingLayerId();this._map.addLayer({id:this._point_animate_layer_name,type:"circle",source:{type:"geojson",data:t},paint:this._opts.animatePointLayerPaint},e),this._animatePoint()}},e.prototype._getBuildingLayerId=function(){return void 0===this._map.getLayer("city_normal_building_id")?null:"city_normal_building_id"},t.MapPlatForm.Base.SpriteLine=e}(window),function(){"use strict";var t=function(){this.callbacks={},this.preCallbacks={}};t.prototype={subscribe:function(t,e,i){t&&e&&i&&(this.callbacks[t]||(this.callbacks[t]={}),this.callbacks[t][e]=i,this.preCallbacks[t]&&this.preCallbacks[t][e]&&this.preCallbacks[t][e].length&&(i.apply(this,this.preCallbacks[t][e][0]),delete this.preCallbacks[t][e]))},cancel:function(t,e){t&&this.callbacks[t]&&(e?delete this.callbacks[t][e]:this.callbacks[t]=[])},publish:function(){var t=Array.prototype.shift.call(arguments);if(t&&t.name){var e=arguments;if(this.callbacks[t.name])if(t.callback)this.callbacks[t.name][t.callback]&&this.callbacks[t.name][t.callback].apply(this,e);else for(var i in this.callbacks[t.name])this.callbacks[t.name][i].apply(this,e);else this.preCallbacks[t.name]||(this.preCallbacks[t.name]={}),this.preCallbacks[t.name][t.callback]||(this.preCallbacks[t.name][t.callback]=[]),this.preCallbacks[t.name][t.callback][0]=e}}},window.MapPlatForm.Base.publisher=t}(window),MapPlatForm.Base.MapShapRegion=function(t,e,i){this.geoRegion=e;var o=++NPMap.LayerIndex;this._layerID="RegionLayer"+o,this.CLASS_NAME="MapPlatForm.Base.MapShapRegion",this._map=t;var r=GeoJSON.read(e.geometry);this.fullExtent=r.getExtent(),this._layers=[],this._regions=[],this.acvtiveLayer=null,this.acvtiveRegion=null,this.opts=i||{},this.fillColor=this.opts.fillColor?this.opts.fillColor:"#1a65fb",this.lineColor=this.opts.lineColor?this.opts.lineColor:"#FFFFFF",this.sharderFillColor=this.opts.sharderFillColor?this.opts.sharderFillColor:"#3961b1",this.sharderLineColor=this.opts.sharderLineColor?this.opts.sharderLineColor:"#1040a0",this.opacity=this.opts.opacity?this.opts.opacity:.5,this.hoverColor=this.opts.hoverColor?this.opts.hoverColor:"#3778fb",this.hoverStrokeColor=this.opts.hoverStrokeColor?this.opts.hoverStrokeColor:"#FFFFFF",this.font=this.opts.font?this.opts.font:"14px 宋体",this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.sharderWidth=this.opts.sharderWidth?this.opts.sharderWidth:20,this.minZoom=this.opts.minZoom?this.opts.minZoom:7,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:12,this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:function(){},this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:function(){},this.click=this.opts.click?this.opts.click:function(){},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this.labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":1},filter:["==","$type","Point"]},this.addGeoRegions(e)},MapPlatForm.Base.MapShapRegion.prototype._getFeature=function(t){if(this._map.getZoom()>=12)return[];for(var e=this._map._obj.style._order.length-1;e>=0;e--)if(this._map._obj.style._order[e].indexOf(this._layerID+"_polygon")>-1){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&o.length>0)return[o[0]]}return[]},MapPlatForm.Base.MapShapRegion.prototype._mouseout=function(){this.mouseOut&&this.acvtiveRegion&&(this._map._obj.getCanvas().style.cursor="",this.mouseOut(this.acvtiveRegion),this.acvtiveRegion=null,this.refresh())},MapPlatForm.Base.MapShapRegion.prototype._initInteractive=function(t){this._map._events.through=!0;var e=this;!this._moveFun&&(this._moveFun=function(t){var i=e._map._obj.getBearing(),o=e._map._obj.getPitch()*e.sharderWidth/60,r=0-Math.sin(i/180*Math.PI)*o,a=Math.cos(i/180*Math.PI)*o;e._map._obj.setPaintProperty(e._layerID+"_sharderPolygon","fill-translate",[r,a])}),!this._mouseMoveFun&&(this._mouseMoveFun=function(t){var i=e._getFeature(t);0!=i.length?e._map.getZoom()>=12||e.acvtiveRegion&&i[0].properties.id==e.acvtiveRegion.id||(e._mouseout(),i&&i.length>0&&(e._map._obj.getCanvas().style.cursor="pointer",e.acvtiveRegion=e._regions[i[0].properties.index],e.mouseOver&&e.acvtiveRegion&&(e.refresh(),e.mouseOver(e.acvtiveRegion)))):e._mouseout()}),!this._clickFun&&(this._clickFun=function(){e.acvtiveRegion&&e.click(e.acvtiveRegion)}),t?(this._map._obj.off("move",this._moveFun),this._map._obj.off("mousemove",this._mouseMoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("move",this._moveFun),this._map._obj.on("mousemove",this._mouseMoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapShapRegion.prototype.addGeoRegions=function(t){if(t.districts){for(var e=0;e<t.districts.length;e++)if(t.districts[e]){var i=GeoJSON.read(t.districts[e].geometry);t.districts[e].id=i.id,t.districts[e].center=[i.getCenter().lon,i.getCenter().lat],this._regions.push(t.districts[e]);var o={type:"Feature",geometry:{type:t.districts[e].geometry.type,coordinates:i._getCoordinates(this._map)},properties:{id:i.id,index:e,children:1}},r={type:"Feature",geometry:{type:"Point",coordinates:[i.getCenter().lon,i.getCenter().lat]},properties:{id:i.id,title:t.districts[e].name,"text-offset":[0,1],"text-opacity":1,"text-size":12,"text-color":this.fontColor,visible:!0,index:e}};this._source.data.features.push(o),this._source.data.features.push(r)}var a=GeoJSON.read(t.geometry),s={type:"Feature",geometry:{type:t.geometry.type,coordinates:a._getCoordinates(this._map)},properties:{id:a.id,children:0}};this._source.data.features.push(s),this._map._obj.addSource(this._layerID+"_source",this._source),this._addLayers()}this._initInteractive()},MapPlatForm.Base.MapShapRegion.prototype._addLayers=function(){var t=this._map._obj.getBearing(),e=this._map._obj.getPitch()*this.sharderWidth/60,i=0-Math.sin(t/180*Math.PI)*e,o=Math.cos(t/180*Math.PI)*e,r={id:this._layerID+"_sharderPolygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.sharderFillColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0],"fill-translate-anchor":"map","fill-translate":[i,o]},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(r),this._layers.push(r);var a={id:this._layerID+"_polygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.fillColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.8,this.maxZoom,0],"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1]]};this._map._obj.addLayer(a),this._layers.push(a);var s={id:this._layerID+"_Line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.lineColor,"line-width":1,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",1]]};this._map._obj.addLayer(s),this._layers.push(s);var n={id:this._layerID+"_parentLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.lineColor,"line-width":4,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(n),this._layers.push(n),this.acvtiveLayer={id:this._layerID+"_polygonActive",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.hoverColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0],"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]},this._map._obj.addLayer(this.acvtiveLayer),this._layers.push(this.acvtiveLayer);var l={id:this._layerID+"_activeLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.hoverStrokeColor,"line-width":4,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,1,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]};this._map._obj.addLayer(l),this._layers.push(l),this._map._obj.addLayer(this.labelLayer),this._layers.push(this.labelLayer)},MapPlatForm.Base.MapShapRegion.prototype.destory=function(){if(this._map){this._initInteractive(!0);for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map._obj.removeSource(this._layerID+"_source"),this._map=null}this._regions=[],this._layers=[],this.acvtiveRegion=null},MapPlatForm.Base.MapShapRegion.prototype.refresh=function(){this.acvtiveRegion?(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1],["!=","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]])):(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]))},MapPlatForm.Base.MapMultiMarkers=function(t,e){this.CLASS_NAME="MapMultiMarkers",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._markers=[],this._showMarkers=[],this._imgMarkers={},this.acvtivePoint=null,this.activeFeature=null,this._points={},this.mouseOver=e?e.mouseOver:null,this.mouseOut=e?e.mouseOut:null,this.click=e?e.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._markerLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"icon-image":["get","icon"],"text-field":"{title}","text-font":["Microsoft YaHei"],"text-offset":["get","text-offset"],"icon-offset":["get","icon-offset"],"icon-size":["get","icon-size"],"text-size":["get","text-size"],"icon-allow-overlap":!0,"text-allow-overlap":!0,"icon-ignore-placement":!0,"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"],"icon-opacity":["get","icon-opacity"]},filter:["==","$type","Point"]},this._activeMarkerLayer={id:this._layerID+"_activeSymbol",type:"symbol",source:this._layerID+"_source",layout:{"icon-image":["get","icon"],"text-field":"{title}","text-font":["Microsoft YaHei"],"text-offset":["get","text-offset"],"icon-offset":["get","icon-offset"],"icon-size":["get","icon-size"],"text-size":["get","text-size"],"icon-allow-overlap":!0,"text-allow-overlap":!0,"icon-ignore-placement":!0,"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"],"icon-opacity":["get","icon-opacity"]},filter:["==","$type","Point"]}},MapPlatForm.Base.MapMultiMarkers.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;e>=0;e--)if(this._map._obj.style._order[e].indexOf(this._layerID)>-1){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&o.length>0)return[o[0]]}return[]},MapPlatForm.Base.MapMultiMarkers.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activePoint&&this.mouseOut(this.activePoint),this.activeFeature=null,this.activePoint=null},MapPlatForm.Base.MapMultiMarkers.prototype._initInteractive=function(t){var e=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var i=e._getFeature(t);0!=i.length?e.activeFeature&&i[0].properties.id==e.activeFeature.properties.id||(e._mouseout(),i&&i.length>0&&(e._map._obj.getCanvasContainer().style.cursor="pointer",e.activeFeature=i[0],e.activePoint=e._points[i[0].properties.id],e.mouseOver&&e.activePoint&&(e.mouseOver(e.activePoint),e._map._obj.setFilter(e._activeMarkerLayer.id,["==","id",e.activePoint.id])))):e._mouseout()}),!this._clickFun&&(this._clickFun=function(){e.activePoint&&e.click(e.activePoint)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapMultiMarkers.prototype.addMarkers=function(t){if(t&&t.length>0)for(var e=0;e<t.length;e++)if(t[e].opts.url){this._points[t[e].id]=t[e];var i=NPMap.T.setPoint(this._map,t[e]),o=t[e].opts||{};this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:[i.lon,i.lat]},properties:{id:t[e].id,layerId:this._layerID,title:o.label?o.label:"",icon:o.url,"icon-offset":o.iconOffset?o.iconOffset:void 0,"text-offset":o.labelOffset?o.labelOffset:void 0,"text-opacity":o.textOpacity?o.textOpacity:1,"icon-opacity":o.iconOpacity?o.iconOpacity:1,"icon-rotate":o.rotation?o.rotation:void 0,"icon-size":o.iconSize?o.iconSize:1,"text-size":o.fontSize?o.fontSize:12,"icon-rotation-alignment":o.showInMap?"map":"viewport","text-color":o.fontColor?o.fontColor:void 0}})}this._map._obj.getSource(this._layerID)?this._map._obj.getSource(this._layerID+"_source").setData(this._source.data):(this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._markerLayer),this._map._obj.addLayer(this._activeMarkerLayer)),this._initInteractive()},MapPlatForm.Base.MapMultiMarkers.prototype.setStyle=function(t){this._map&&!this._map._obj.getLayer(this._layerID+"_activeSymbol")&&(t&&t.url&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-image",t.url),t&&t.iconSize&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-size",t.iconSize),t&&t.fontSize&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-size",t.fontSize),t&&t.fontColor&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-color",t.fontColor),t&&t.iconOffset&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-offset",t.iconOffset),t&&t.textOffset&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-offset",t.textOffset),t&&t.label&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-field",t.label))},MapPlatForm.Base.MapMultiMarkers.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.removeLayer(this._markerLayer.id),this._map._obj.removeLayer(this._activeMarkerLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activePoint=null,this._points={}},MapPlatForm.Base.MapMultiMarkers.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID)&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapMultiMarkers.prototype.setVisibile=function(t){t?(this._map._obj.getLayer(this._layerID+"_symbol")&&this._map._obj.setLayoutProperty(this._layerID+"_symbol","visibility","visible"),this._map._obj.getLayer(this._layerID+"_activeSymbol")&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","visibility","visible")):(this._map._obj.getLayer(this._layerID+"_symbol")&&this._map._obj.setLayoutProperty(this._layerID+"_symbol","visibility","none"),this._map._obj.getLayer(this._layerID+"_activeSymbol")&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","visibility","none"))},MulRegion=function(t,e,i){this.lonLats=t,this.context=e,this.opts=i,this.visibile=!0,this.pixels=[],this.nears=[],this.defaultStrokeColor="rgb(255,255,255)",this.strokeColor=this.defaultStrokeColor,this.fillColor=(this.strokeColor.substring(0,this.strokeColor.lastIndexOf(")"))+",1)").replace("rgb","rgba"),this.defaultFillColor=this.fillColor,this.backOffset=[40,0],this.acvtiveOffset=[5,0],this.font="18px 微软雅黑",this.fontColor="#000000",this.value=1,this.mainValue=1e3,this.center=this.opts.center?this.opts.center:null,this.name=this.opts.name?this.opts.name:"",this.resetStyle=function(){this.fillColor=this.defaultFillColor,this.strokeColor=this.defaultStrokeColor},this.reset=function(t,e,i,o){this.resetStyle(),this.pixels=[];for(var r=0,a=0,s=0,n=0;n<this.lonLats.length;n++){var l={x:e.x+(this.lonLats[n][0]-t.lon)/i,y:e.y-(this.lonLats[n][1]-t.lat)/i};r+=l.x,a+=l.y,s++,this.pixels.push(l)}this.centerXY={x:r/s,y:a/s},this.center&&o&&(this.centerXY={x:e.x+(this.center.lon-t.lon)/i,y:e.y-(this.center.lat-t.lat)/i})},this.draw=function(){if(0!=this.pixels.length){this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);if(this.context.closePath(),this.gradientColor1&&this.gradientColor2){var e=this.context.createLinearGradient(this.context.canvas.width/2,0,this.context.canvas.width/2,this.context.canvas.height);e.addColorStop(0,this.gradientColor1),e.addColorStop(1,this.gradientColor2),this.context.fillStyle=e,this.context.fill()}else this.context.fillStyle=this.fillColor,this.context.fill()}},this.drawLine=function(){if(0!=this.pixels.length){this.context.beginPath(),this.context.lineJoin="round",this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.strokeStyle=this.strokeColor,this.context.lineWidth=1,this.context.stroke()}},this.drawTJ=function(t){if(void 0!==this.value){var e=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value,this.centerXY.x-5,this.centerXY.y-20);t?(e.addColorStop(0,this._getOpacityColor(this.TJActivecolor)),e.addColorStop(1,this.TJActivecolor)):(e.addColorStop(0,this._getOpacityColor(this.TJcolor)),e.addColorStop(1,this.TJcolor)),this.context.fillStyle=e,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value,10,85*this.value),this.mainValue>0&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-20-.5,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=t?this.TJActivetextcolor:this.TJtextcolor,this.context.fillText(this.mainValue,this.centerXY.x,this.centerXY.y-85*this.value-30),this.context.restore())}},this._getOpacityColor=function(t){return t.indexOf("rgba")>-1?t=t.substring(0,t.lastIndexOf(","))+",0)":t.indexOf("rgb")>-1&&(t=(t=t.replace("rgb","rgba")).substring(0,xx.indexOf(")"))+",0)"),t},this.drawTJ2=function(t){if(void 0!==this.value){var e=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value*t,this.centerXY.x-5,this.centerXY.y-20);e.addColorStop(0,this._getOpacityColor(this.TJcolor)),e.addColorStop(1,this.TJcolor),this.context.fillStyle=e,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value*t,10,85*this.value*t),this.mainValue>0&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-.5-20,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=this.TJtextcolor,this.context.fillText(Math.ceil(this.mainValue*t),this.centerXY.x,this.centerXY.y-85*this.value*t-30),this.context.restore())}},this.drawBack=function(){if(0!=this.pixels.length){for(var t=1;t<this.pixels.length-1;t++){this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x+this.backOffset[0],this.pixels[t].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x+this.backOffset[0],this.pixels[t+1].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x+this.backOffset[0],this.pixels[t].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x+this.backOffset[0],this.pixels[t+1].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath();var e=(this.pixels[t+1].x+this.pixels[t].x)/2,i=(this.pixels[t+1].y+this.pixels[t].y)/2,o=this.context.createLinearGradient(e,i,e+this.backOffset[0],i+this.backOffset[1]);o.addColorStop(0,this.backShaderColor),o.addColorStop(1,this.backShaderColor2),this.context.fillStyle=o,this.context.fill()}for(this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y),t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore()}},this.drawActive=function(){if(0!=this.pixels.length)if(this.JianBian){for(this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y),t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}else{for(var t=1;t<this.pixels.length-1;t++)this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x-this.acvtiveOffset[0],this.pixels[t+1].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x-this.acvtiveOffset[0],this.pixels[t+1].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.fillStyle=this.backShaderColor,this.context.fill();this.context.beginPath(),this.context.moveTo(this.pixels[0].x-this.acvtiveOffset[0],this.pixels[0].y-this.acvtiveOffset[1]);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}},this.drawTitle=function(){this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font=this.font,this.context.fillStyle=this.fontColor,this.context.fillText(this.name,this.centerXY.x,this.centerXY.y),this.context.restore())},this.crossMul=function(t,e){return t.x*e.y-t.y*e.x},this.checkCross=function(t,e,i,o){var r={x:t.x-i.x,y:t.y-i.y},a={x:e.x-i.x,y:e.y-i.y},s={x:o.x-i.x,y:o.y-i.y},n=this.crossMul(r,s)*this.crossMul(a,s);return r={x:i.x-t.x,y:i.y-t.y},a={x:o.x-t.x,y:o.y-t.y},s={x:e.x-t.x,y:e.y-t.y},n<=0&&this.crossMul(r,s)*this.crossMul(a,s)<=0},this.containsPoint=function(t,e){var i,o,r,a;i=t,o={x:-1e5,y:t.y};for(var s=0,n=0;n<e.length-1;n++)r=e[n],a=e[n+1],this.checkCross(i,o,r,a)&&s++;return r=e[e.length-1],a=e[0],this.checkCross(i,o,r,a)&&s++,s%2!=0},this.isContains=function(t){if(void 0!==this.pixels[0].x)return this.containsPoint(t,this.pixels);for(var e=0,i=0;i<this.pixels.length;i++)this.containsPoint(t,this.pixels[i])&&e++;return e%2!=0},this.setStyle=function(t){t&&(t.fillColor&&(this.fillColor=t.fillColor),t.strokeColor&&(this.strokeColor=t.strokeColor))}},MapPlatForm.Base.ShapMap=function(t,e,i){for(var o in this.mapContainer=t,this.CLASS_NAME="MapPlatForm.Base.ShapMap",this.fullExtent=this._getExtent(e),this.scrollTop=0,this._regions=[],this._backRegion=null,this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left="0px",this.canvas.style.top="0px",this.canvas.width=t.offsetWidth,this.canvas.height=t.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,t.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.canvasActive=document.createElement("canvas"),this.canvasActive.style.position="absolute",this.canvasActive.style.left="0px",this.canvasActive.style.top="0px",this.canvasActive.width=t.offsetWidth,this.canvasActive.height=t.offsetHeight,this.canvasActive.style.pointerEvents="none",this.canvasActive.style.zIndex=206,t.appendChild(this.canvasActive),this.canvasActive.setAttribute("class","olScrollable"),this.contextActive=this.canvasActive.getContext("2d"),this.points=i&&i.points?i.points:[],this.acvtiveRegion=null,this.legendColor=i&&i.legendColor?i.legendColor:{},this.legendValue=i&&i.legendValue?i.legendValue:{},this.maxValue=0,this.showAllTJ=!i||!1!==i.showAllTJ,this.showAllNames=!(!i||!i.showAllNames),this.legendValue)this.legendValue[o]>this.maxValue&&(this.maxValue=this.legendValue[o]);this.maxValue=this.maxValue?this.maxValue:100,"{}"===JSON.stringify(this.legendColor)?(this.JianBian=!0,this.gradientColor1=i&&i.gradientColor1?i.gradientColor1:"#3f96c3",this.gradientColor2=i&&i.gradientColor2?i.gradientColor2:"#175579",this.hoverColor=i&&i.hoverColor?i.hoverColor:"#154e6e"):this.hoverColor=i&&i.hoverColor?i.hoverColor:null,this.JianBian?this.backShaderColor=i&&i.backShaderColor?i.backShaderColor:"#11a4b5":this.backShaderColor=i&&i.backShaderColor?i.backShaderColor:"#bcdefd",this.backShaderColor2=i&&i.backShaderColor2?i.backShaderColor2:this.backShaderColor,this.JianBian?this.strokeColor=i&&i.strokeColor?i.strokeColor:"#15b1e5":this.strokeColor=i&&i.strokeColor?i.strokeColor:"#FFFFFF",this.TJcolor=i&&i.TJcolor?i.TJcolor:"rgba(255,198,0,255)",this.TJtextcolor=i&&i.TJtextcolor?i.TJtextcolor:this.TJcolor,this.TJActivecolor=i&&i.TJActivecolor?i.TJActivecolor:"rgba(242,19,75,255)",this.TJActivetextcolor=i&&i.TJActivetextcolor?i.TJActivetextcolor:this.TJActivecolor,this.backOffset=i&&i.backOffset?i.backOffset:[40,0],this.acvtiveOffset=i&&i.acvtiveOffset?i.acvtiveOffset:[5,0],this.font=i&&i.font?i.font:"14px 宋体",this.fontColor=i&&i.fontColor?i.fontColor:"#000000",this.shadowColor=i&&i.shadowColor?i.shadowColor:"rgba(61,154,235,0.2)",this.fillColors=["#7fc3ff","#3d9aeb","#2a7cc4","#215e94"],this.mouseOver=i?i.mouseOver:null,this.mouseOut=i?i.mouseOut:null,this.mouseMove=i?i.mouseMove:null,this.click=i?i.click:null,this.starRadio=i?i.starRadio:3,this.starColor=i?i.starColor:"rgba(255,255,255,0.8)",this._initPoints(),this.addGeoRegions(e)},MapPlatForm.Base.ShapMap.prototype._initInteractive=function(){var t=this;this.mapContainer.onmousemove=function(e){var i={x:e.offsetX,y:e.offsetY};i.y=i.y+t.scrollTop;for(var o=!1,r=0;r<t._regions.length;r++)if(t._regions[r].isContains(i)){if(t._regions[r]==t.acvtiveRegion)return void(t.mouseMove&&t.mouseMove(t.acvtiveRegion,i));o=!0,t.mouseOut&&t.acvtiveRegion&&(t.refresh(),t.mouseOut(t.acvtiveRegion)),t.acvtiveRegion=t._regions[r],t.mouseOver&&(t.refresh(t.acvtiveRegion),t.acvtiveRegion.drawTJ(!0),t.acvtiveRegion.drawTitle(),t.mouseOver(t.acvtiveRegion,i)),t.mouseMove&&t.mouseMove(t.acvtiveRegion,i);break}!o&&t.acvtiveRegion&&(t.mouseOut&&(t.refresh(),t.mouseOut(t.acvtiveRegion)),t.acvtiveRegion=null)},this.mapContainer.onmousedown=function(e){var i={x:e.offsetX,y:e.offsetY};t.curentXY=i},this.mapContainer.onclick=function(e){if(e.offsetX==t.curentXY.x&&e.offsetY==t.curentXY.y){var i={x:e.offsetX,y:e.offsetY};i.y=i.y+t.scrollTop;for(var o=0;o<t._regions.length;o++)if(t._regions[o].isContains(i)){isContains=!0,t.click&&t.click(t._regions[o]);break}}}},MapPlatForm.Base.ShapMap.prototype._initPoints=function(){for(var t=[],e=Math.ceil(this.points.length/1e3),i=0;i<this.points.length;i+=e)t.push(this.points[i]);this.points=t},MapPlatForm.Base.ShapMap.prototype._resetAndDrawPoint=function(t,e,i){for(var o=0;o<this.points.length;o++){var r=e.x+(this.points[o][0]-t.lon)/i,a=e.y-(this.points[o][1]-t.lat)/i;this.context.beginPath();var s=this.context.createRadialGradient(r,a,0,r,a,this.starRadio);s.addColorStop(0,this.starColor),s.addColorStop(1,"rgba(255,255,255,0)"),this.context.fillStyle=s,this.context.arc(r,a,this.starRadio,0,2*Math.PI,!0),this.context.closePath(),this.context.fill()}},MapPlatForm.Base.ShapMap.prototype.addGeoRegions=function(t){var e,i,o=this.fullExtent,r={lon:(o.minX+o.maxX)/2,lat:(o.minY+o.maxY)/2},a={x:this.canvas.width/2,y:this.canvas.height/2};Math.abs(o.maxY-o.minY)/this.canvas.height>Math.abs(o.maxX-o.minX)/this.canvas.width?(e=this.canvas.height,i=Math.abs(o.maxY-o.minY)):(e=this.canvas.width,i=Math.abs(o.maxX-o.minX));for(var s=i/(e-80),n=0;n<t.geometry.coordinates.length;n++)for(var l=t.geometry.coordinates[n],h=0;h<l.length;h++){var p=l[h];this._backRegion=new MulRegion(p,this.context,{center:r})}if(this._backRegion.strokeColor=this.strokeColor,this._backRegion.backOffset=this.backOffset,this._backRegion.backShaderColor=this.backShaderColor,this._backRegion.backShaderColor2=this.backShaderColor2,this._backRegion.shadowColor=this.shadowColor,this._backRegion.reset(r,a,s),this._backRegion.drawBack(),this._resetAndDrawPoint(r,a,s),this.JianBian&&(this._backRegion.gradientColor1=this.gradientColor1,this._backRegion.gradientColor2=this.gradientColor2,this._backRegion.JianBian=this.JianBian,this._backRegion.draw()),t.districts)for(n=0;n<t.districts.length;n++)if(t.districts[n]){var c=t.districts[n].geometry.coordinates[0][0],u=t.districts[n].name,m=JSON.parse(t.districts[n].center).coordinates,d=new MulRegion(c,this.contextActive,{center:m,name:u});d.defaultStrokeColor=this.strokeColor,d.strokeColor=this.strokeColor,d.defaultFillColor=this.legendColor[u]?this.legendColor[u]:this.fillColors[n%4],d.fillColor=this.legendColor[u]?this.legendColor[u]:this.fillColors[n%4],d.hoverColor=this.hoverColor?this.hoverColor:d.fillColor,d.JianBian=this.JianBian,d.backShaderColor=this.backShaderColor,d.backShaderColor2=this.backShaderColor2,d.acvtiveOffset=this.acvtiveOffset,d.font=this.font,d.fontColor=this.fontColor,d.value=void 0!==this.legendValue[u]?this.legendValue[u]/this.maxValue:void 0,d.mainValue=this.legendValue[u]?this.legendValue[u]:0,d.TJcolor=this.TJcolor,d.TJtextcolor=this.TJtextcolor,d.TJActivecolor=this.TJActivecolor,d.TJActivetextcolor=this.TJActivetextcolor,d.reset(r,a,s),this._regions.push(d)}this.v=0,this._anim(),this._initInteractive()},MapPlatForm.Base.ShapMap.prototype._anim=function(){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left="0px",this.canvasActive.style.top="0px";for(var t=0;t<this._regions.length;t++){var e=this._regions[t];this.JianBian?e.drawLine(!0):e.draw(!0),this.showAllNames&&e.drawTitle()}if(this.showAllTJ){this.v=this.v+.02;for(var i=0;i<this._regions.length;i++)this._regions[i].drawTJ2(this.v);var o=this;this.v<1&&setTimeout((function(){o._anim()}),2)}},MapPlatForm.Base.ShapMap.prototype._getExtent=function(t){for(var e,i,o,r,a=t.geometry.coordinates,s=0;s<a.length;s++)for(var n=0;n<a[s].length;n++)for(var l=a[s][n],h=0;h<l.length;h++)0==n&&0==h&&0==s?(e=l[h][0],o=l[h][1],i=l[h][0],r=l[h][1]):(e>l[h][0]&&(e=l[h][0]),o<l[h][0]&&(o=l[h][0]),i>l[h][1]&&(i=l[h][1]),r<l[h][1]&&(r=l[h][1]));return{minX:e,minY:i,maxX:o,maxY:r}},MapPlatForm.Base.ShapMap.prototype.destory=function(){this.mapContainer.onclick=null,this.mapContainer.onmousemove=null,this.mapContainer.onmousedown=null,this.mapContainer&&(this.mapContainer=null),this._regions=[],this._backRegion=null,this.canvas.remove(),this.canvasActive.remove()},MapPlatForm.Base.ShapMap.prototype.refresh=function(t){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left="0px",this.canvasActive.style.top="0px";for(var e=0;e<this._regions.length;e++){var i=this._regions[e];this.JianBian?i.drawLine(!0):i.draw(!0),this.showAllNames&&i.drawTitle()}if(t&&t.drawActive(),this.showAllTJ)for(var o=0;o<this._regions.length;o++)this._regions[o].drawTJ()},function(){var t=MapPlatForm.Base.ArrowLine=function(t,e){this._map=t,this.id=NPMap.Utils.BaseUtils.uuid(),this._visible=!0,this._layers=[],this.image=new Image,this.image.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgEAYAAAAj6qa3AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAZiS0dEAAAAAAAA+UO7fwAAAAlwSFlzAAAASAAAAEgARslrPgAAAjBJREFUaN7tmCFIQ1EUhvd0TmQTQdAgIrLkkmkIYjBYTGPBYhKLxWKwG8dQhphtYjCYLILNooJgGQwMBhGDhgV1bGznN/xb2GTHqfd6ZHjKz+DBvv977913uaHQ/7gdCAQSiVhz/HLhwUEAAC4vmbUac2+PGQTWnJ4FLC2h3QgEsr1tzelPAABgcpJFX150EZub1rz+RAgEsrDAtuVyGwvCXF215vUnAgCwvNy8FrROtcpMp615/YkQCGR9Heq8vfG6uTlrXs8islldRLHI66anrXndC6h/Bllwf18X8fDQWFStuT2J6O1lHh/rIm5vKWx01Jrbk4iBAeb5uS7i6ooiYjFrbvciBAIZGmLRmxt9/3B2xuzvt+Z2LwIAMDbGvLvTn4jDQ2ZPjzW3JxFTU7zTT0/6E7GzY83rT4RAIDMznW2tk8mf/l/YuvCHCUJBKCiX+aNa/UQXrHGdDW9rPM58fNTvfDZrzeuuuEAgIyNsVyjoi+DBAbMLzheaD1Kur/XiJye8Pvz3XtnvFY9EWOz0VH/ULy74Ixq15v558fr3m8WOjvQ7ns8zh4etuR0L2N3Vi9/fU9DEhDWv4+JbW3rx52dmImHN6664QCBra3rx11fm7Kw1r7viAIBUitk4+mqdSoW5uGjN67j4/DyzVGqztNcPRVdWrHndFRcIZHycxYpF/ZHf2LDmdS8AAJBO68UzGWtOfwIaO7qmjUvj3c/lmF2wdf2akL4+a47/6XDeAd6F40H7Yd0JAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE4LTEyLTI4VDE1OjEzOjQyKzA4OjAwCT5KagAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOC0xMi0yOFQxNToxMzo0MiswODowMHhj8tYAAABKdEVYdHN2ZzpiYXNlLXVyaQBmaWxlOi8vL2hvbWUvYWRtaW4vaWNvbi1mb250L3RtcC9pY29uX3R4N3JpcjU3ZXpiL2ppYW50b3Uuc3Zn6JRGFAAAAABJRU5ErkJggg==",this.size=32,this.offsetX=-32,this._isAnimation=e,this._isLoadImage=!1};t.prototype._drawImage=function(){var t=this;return{width:this.size,height:this.size,data:new Uint8Array(this.size*this.size*4),onAdd:function(){var t=document.createElement("canvas");t.width=this.width,t.height=this.height,this.context=t.getContext("2d");this.context},render:function(){if(!t._isAnimation&&t._isLoadImage)return!1;t.offsetX=t.offsetX+1,t.offsetX>0&&(t.offsetX=-32);var e=this.context;return e.clearRect(0,0,this.width,this.height),e.drawImage(t.image,t.offsetX,0,32,32),e.drawImage(t.image,t.offsetX+32,0,32,32),this.data=e.getImageData(0,0,this.width,this.height).data,t._map._obj.triggerRepaint(),t._isLoadImage=!0,!0}}},t.prototype.load=function(t){if(this._map._obj.hasImage(this.id+"arrawIcon")||this._map._obj.addImage(this.id+"arrawIcon",this._drawImage(),{pixelRatio:1}),t&&t.length>0)for(var e=0;e<t.length;e++){var i={id:this.id+"_line"+t[e].id,type:"line",source:t[e].layer._layerID+"_source",paint:{"line-width":t[e]._weight,"line-translate-anchor":"map","line-opacity":t[e]._visible?t[e]._opacity:1,"line-pattern":this.id+"arrawIcon"},filter:["==","id",t[e].id]};this._layers.push(i),this._map._obj.addLayer(i)}},t.prototype.remove=function(){if(this._map._obj.hasImage(this.id+"arrawIcon")&&this._map._obj.removeImage(this.id+"arrawIcon"),this._layers.length)for(var t=0;t<this._layers.length;t++)this._map._obj.getLayer(this._layers[t].id)&&this._map._obj.removeLayer(this._layers[t].id)},t.prototype.show=function(t){if(this._layers.length&&!this._visible){for(var e=0;e<this._layers.length;e++)this._map._obj.setLayoutProperty(this._layers[e].id,"visibility","visible");this._visible=!0}},t.prototype.hide=function(t){if(this._layers.length&&this._visible){for(var e=0;e<this._layers.length;e++)this._map._obj.setLayoutProperty(this._layers[e].id,"visibility","none");this._visible=!1}}}(),function(){var t=function(t,e){this.points=[t.point],this._map=e,this.id=t.id,this._point=t,this._count=60,this.offset=t.offset,this._currentIndex=0,this._linePoints=[],this._lineLength=20,this._visible=!0,this._show3D=!1;var i=document.createElement("div");i.className="marker",i.style.backgroundImage="url("+t.url+")",i.style.width=(t.iconSize?t.iconSize[0]:32)+"px",i.style.height=(t.iconSize?t.iconSize[1]:32)+"px",i.style.cursor="pointer";var o=this;i.addEventListener("click",(function(){return t.click&&t.click instanceof Function&&t.click(o._point),!1})),this._element=i;var r=NPMap.T.setPoint(this._map,{lon:t.point.lon,lat:t.point.lat});this.marker=new mapboxgl.Marker({element:i,offset:this.offset}).setLngLat([r.lon,r.lat]),this.setUrl=function(t){this._point.url=t,i.style.backgroundImage="url("+t+")"},this.addToMap=function(){this.marker.addTo(this._map._obj)},this.addPoint=function(t){if(this._currentIndex=0,this.points.push(t),2!=this.points.length){var e=this.points[this.points.length-2],i=this.points[this.points.length-1],o=(i.lon-e.lon)/this._count*this._currentIndex+e.lon,r=(i.lat-e.lat)/this._count*this._currentIndex+e.lat,a=NPMap.T.setPoint(this._map,{lon:o,lat:r});this.marker.setLngLat([a.lon,a.lat]),this._linePoints.length>0&&a.lon==this._linePoints[this._linePoints.length-1].lon&&a.lat==this._linePoints[this._linePoints.length-1].lat||(this._linePoints.push(a),this._linePoints=this._linePoints.slice(-this._lineLength))}},this._calculateAngle=function(){var t=this.points[this.points.length-2],e=this.points[this.points.length-1];if(t.lon==e.lon&&t.lat==e.lat)return-100;var i=180*Math.atan2(e.lat-t.lat,e.lon-t.lon)/Math.PI;return(i=(i-=270)>0?i:360+i)/180*Math.PI},this.moveNext=function(){if(!(this.points.length<2||this._currentIndex>=this._count-1)){this._currentIndex++;var t=this.points[this.points.length-2],e=this.points[this.points.length-1],i=(e.lon-t.lon)/this._count*this._currentIndex+t.lon,o=(e.lat-t.lat)/this._count*this._currentIndex+t.lat,r=NPMap.T.setPoint(this._map,{lon:i,lat:o});if(this.marker.getLngLat().lng===r.lon&&this.marker.getLngLat().lat===r.lat||this.marker.setLngLat([r.lon,r.lat]),this._linePoints.length>0&&r.lon==this._linePoints[this._linePoints.length-1].lon&&r.lat==this._linePoints[this._linePoints.length-1].lat||(this._linePoints.push(r),this._linePoints=this._linePoints.slice(-this._lineLength)),this._show3D){var a=-100;t.lon==e.lon&&t.lat==e.lat&&(a=-100),this._map.getDistance(t,e)<1&&(a=-100);var s=[0,0,a=this._calculateAngle()];-100==a&&(s=[this._modelTransform.rotateX,this._modelTransform.rotateY,this._modelTransform.rotateZ]),this._modelTransform={translateX:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).x,translateY:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).y,translateZ:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).z,rotateX:s[0],rotateY:s[1],rotateZ:s[2],scale:3e-8}}}},this.hide=function(){this._visible=!1,this._element.style.display="none"},this.show=function(){this._visible=!0,this._element.style.display="block"},this.hide3DModel=function(){this._show3D&&(this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._element.style.display="block",this._show3D=!1)},this.show3DModel=function(){if(!this._show3D&&this._point.url3d){this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._element.style.display="none",this._show3D=!0;var t=0,e=0,i=[0,0,0];if(this.points.length>1){var o=this.points[this.points.length-2],r=this.points[this.points.length-1];t=(r.lon-o.lon)/this._count*this._currentIndex+o.lon,e=(r.lat-o.lat)/this._count*this._currentIndex+o.lat;i=[0,0,this._calculateAngle()]}else i=[0,0,0];var a=NPMap.T.setPoint(this._map,{lon:t,lat:e});this._modelTransform={translateX:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).x,translateY:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).y,translateZ:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).z,rotateX:i[0],rotateY:i[1],rotateZ:i[2],scale:3e-8};var s=this,n={id:"GPSRefreshManager-3DModel",type:"custom",renderingMode:"3d",onAdd:function(t,e){this.camera=new THREE.Camera,this.scene=new THREE.Scene;var i=new THREE.DirectionalLight(16777215);i.position.set(-200,0,200).normalize(),this.scene.add(i);var o=new THREE.DirectionalLight(16777215);o.position.set(200,0,200).normalize(),this.scene.add(o);var r=new THREE.DirectionalLight(16777215);r.position.set(0,-200,200).normalize(),this.scene.add(r);var a=new THREE.DirectionalLight(16777215);a.position.set(0,200,200).normalize(),this.scene.add(a),(new THREE.GLTFLoader).load(s._point.url3d,function(t){this.scene.add(t.scene)}.bind(this)),this.map=t,this.renderer=new THREE.WebGLRenderer({antialias:!0,canvas:t.getCanvas(),context:e}),this.renderer.autoClear=!1},render:function(t,e){var i=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(1,0,0),s._modelTransform.rotateX),o=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,1,0),s._modelTransform.rotateY),r=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,0,1),s._modelTransform.rotateZ),a=(new THREE.Matrix4).fromArray(e),n=(new THREE.Matrix4).makeTranslation(s._modelTransform.translateX,s._modelTransform.translateY,s._modelTransform.translateZ).scale(new THREE.Vector3(s._modelTransform.scale,-s._modelTransform.scale,s._modelTransform.scale)).multiply(i).multiply(o).multiply(r);this.camera.projectionMatrix.elements=e,this.camera.projectionMatrix=a.multiply(n),this.renderer.state.reset(),this.renderer.render(this.scene,this.camera),this.map.triggerRepaint()}};this._map._obj.getLayer("GPSRefreshManager-3DModel")||(this._map._obj.getLayer("city_normal_building_id")?this._map._obj.addLayer(n,"city_normal_building_id"):this._map._obj.addLayer(n))}}},e=MapPlatForm.Base.GPSRefreshManager=function(t,e){this._map=t,this._gpsPoints={},this._status=1,this._popup=null,this._layerID=NPMap.Utils.BaseUtils.uuid(),this.interval=e&&e.interval?e.interval:50,this.time=e&&e.time?e.time:3e3,this._lineColor=e&&e.lineColor?e.lineColor:"red",this._lineWidth=e&&e.lineWidth?e.lineWidth:4,this._lineLength=e&&e.lineLength?e.lineLength:20,this._lineTypes=e&&e.lineTypes?e.lineTypes:{car:this._lineColor,man:"yellow"},this._source={type:"geojson",lineMetrics:!0,data:{type:"FeatureCollection",features:[]}},this._map._obj.addSource(this._layerID+"_source",this._source),this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this._lineTypes.man,"line-width":this._lineWidth,"line-gradient":["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,this._lineTypes.man]},filter:["all",["==","visible",!0],["==","type","man"]]},this._map._obj.addLayer(this._lineLayer),this._lineLayer2={id:this._layerID+"_line2",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this._lineTypes.man,"line-width":this._lineWidth,"line-gradient":["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,this._lineTypes.car]},filter:["all",["==","visible",!0],["==","type","car"]]},this._map._obj.addLayer(this._lineLayer2),this._map._obj.getLayer("city_normal_building_id")&&(this._map._obj.moveLayer(this._layerID+"_line","city_normal_building_id"),this._map._obj.moveLayer(this._layerID+"_line2","city_normal_building_id"))};e.prototype.setGPSLineTpes=function(t){this._map._obj.setPaintProperty(this._layerID+"_line","line-gradient",["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,t.man]),this._map._obj.setPaintProperty(this._layerID+"_line2","line-gradient",["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,t.car])},e.prototype.addGPSPoints=function(e){for(var i=0;i<e.length;i++)e[i].url3d=e[i].url3d?e[i].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[e[i].id]=new t(e[i],this._map),this._gpsPoints[e[i].id]._count=this.time/this.interval,this._gpsPoints[e[i].id]._lineLength=this._lineLength,this._gpsPoints[e[i].id].addToMap()},e.prototype.updataGPSPoints=function(e){if(this._status)for(var i=0;i<e.length;i++)this._gpsPoints[e[i].id]?(e[i].url3d=e[i].url3d?e[i].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[e[i].id]._point=e[i],this._gpsPoints[e[i].id].addPoint(e[i].point)):(e[i].url3d=e[i].url3d?e[i].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[e[i].id]=new t(e[i],this._map),this._gpsPoints[e[i].id]._count=this.time/this.interval,this._gpsPoints[e[i].id]._lineLength=this._lineLength,this._gpsPoints[e[i].id].addToMap())},e.prototype.setLineVisible=function(t){t?(this._map._obj.setLayoutProperty(this._lineLayer.id,"visibility","visible"),this._map._obj.setLayoutProperty(this._lineLayer2.id,"visibility","visible")):(this._map._obj.setLayoutProperty(this._lineLayer.id,"visibility","none"),this._map._obj.setLayoutProperty(this._lineLayer2.id,"visibility","none"))},e.prototype.setGPSVisibleByFilter=function(t,e,i){var o=this;Object.keys(o._gpsPoints).forEach((function(r){o._gpsPoints[r]._point[t]==e&&o.setGPSVisibleByID(r,i)}))},e.prototype.setGPSVisibleByID=function(t,e){if(this._gpsPoints[t])if(e)this._gpsPoints[t].show();else{this._gpsPoints[t].hide();var i=this._gpsPoints[t].marker.getPopup();i&&i.isOpen()&&i.remove()}},e.prototype.show3DModelByID=function(t){this._gpsPoints[t]&&this._gpsPoints[t].show3DModel()},e.prototype.hide3DModelByID=function(t){this._gpsPoints[t]&&this._gpsPoints[t].hide3DModel()},e.prototype.setGPSStyleByID=function(t,e){this._gpsPoints[t]&&(e.url&&this._gpsPoints[t].setUrl(e.url),e.offset&&this._gpsPoints[t].marker.setOffset(e.offset),e.iconSize&&(this._gpsPoints[t]._element.style.width=e.iconSize[0]+"px",this._gpsPoints[t]._element.style.height=e.iconSize[1]+"px"))},e.prototype.setGPSInfoWindowByID=function(t,e,i){if(this._popup&&(this._popup.remove(),this._popup=null),this._gpsPoints[t]){var o=i&&i.width?i.width:0,r=i&&i.height?i.height:0,a=this._gpsPoints[t]._point.iconSize?this._gpsPoints[t]._point.iconSize[1]:32;this._gpsPoints[t]._point.iconSize&&this._gpsPoints[t]._point.iconSize[0];this._popup=new mapboxgl.Popup({closeButton:!1,closeOnClick:!1,onlyLeftTop:!0,offset:[0-o/2,0-a-r-2]}).setDOMContent(e).addTo(this._map._obj),this._gpsPoints[t].marker.setPopup(this._popup),this._activeID=t,this._popup._container.setAttribute("id",this._layerID+"Infowindow")}},e.prototype.closeGPSInfoWindow=function(){this._popup&&(this._popup.remove(),this._popup=null)},e.prototype.getGPSInfoWindowID=function(){return this._popup&&this._popup._container?this._popup._container.getAttribute("id"):null},e.prototype.unbindInfoWindow=function(){this._activeID&&this._gpsPoints[this._activeID]&&this._gpsPoints[this._activeID].marker._popup&&(this._gpsPoints[this._activeID].marker._popup._map=null,this._gpsPoints[this._activeID].marker._popup=null)},e.prototype.bindInfoWindow=function(){this._activeID&&this._gpsPoints[this._activeID]&&this._popup&&(this._popup._map=this._map._obj,this._gpsPoints[this._activeID].marker._popup=this._popup)},e.prototype.bindCamera=function(t,e){this._isTrack=!0,this._bindGPSID=t,this._callBack=e},e.prototype.unbindCamera=function(){this._isTrack=!1,this._bindGPSID=null,this._callBack=null},e.prototype.start=function(){var t=this;this._status=!0;setTimeout((function e(){if(t._status){if(t._source.data.features=[],Object.keys(t._gpsPoints).forEach((function(e){t._gpsPoints[e].moveNext(t._bindGPSID==e);for(var i={type:"Feature",geometry:{type:"LineString",coordinates:[]},properties:{id:e,visible:t._gpsPoints[e]._visible,type:t._gpsPoints[e]._point.type?t._gpsPoints[e]._point.type:"car"}},o=0;o<t._gpsPoints[e]._linePoints.length;o++)i.geometry.coordinates.push([t._gpsPoints[e]._linePoints[o].lon,t._gpsPoints[e]._linePoints[o].lat]);t._source.data.features.push(i)})),t._map._obj.getSource(t._layerID+"_source").setData(t._source.data),t._gpsPoints[t._bindGPSID]&&t._isTrack){var i=t._gpsPoints[t._bindGPSID].marker.getLngLat();t._map._obj.jumpTo({center:[i.lng,i.lat]});var o=NPMap.T.getPoint(t._map,{lon:i.lng,lat:i.lat});t._callBack&&t._callBack(t._bindGPSID,new NPMap.Geometry.Point(o.lon,o.lat))}setTimeout(e,t.interval-15)}}),this.interval-15)},e.prototype.stop=function(){this._status=0},e.prototype.getGPSPositionByID=function(t){var e=this._gpsPoints[t].marker.getLngLat(),i={lon:e.lng,lat:e.lat};return NPMap.T.getPoint(this._map,i)},e.prototype.getGPSVisibleByID=function(t){return this._gpsPoints[t]._visible},e.prototype.destory=function(){this.stop();var t=this;Object.keys(this._gpsPoints).forEach((function(e){t._gpsPoints[e].marker.remove(),t._gpsPoints[e]=null})),this._map._obj.removeLayer(this._layerID+"_line"),this._map._obj.removeLayer(this._layerID+"_line2"),this._map._obj.removeSource(this._layerID+"_source"),this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._gpsPoints={},this._map=null}}(),function(){MapPlatForm.Base.ShapMap3D=function(t,e,i){this.geoRegion=e;var o=++NPMap.LayerIndex;this._layerID="RegionLayer"+o,this.CLASS_NAME="MapPlatForm.Base.ShapMap3D",this._map=t;var r=GeoJSON.read(e.geometry);this.fullExtent=r.getExtent(),this._regions=[],this._layers=[],this.acvtiveLayer=null,this.acvtiveRegion=null,this.activeFeature=null,this.opts=i||{},this.fillColor=this.opts.fillColors?this.opts.fillColors:["#A4D3EE","#7EC0EE","#63B8FF","#5CACEE","#87CEFF"];var a={};if(this.geoRegion.districts)for(var s=0;s<this.geoRegion.districts.length;s++){var n=s%this.fillColor.length;a[this.geoRegion.districts[s].name]=this.fillColor[n]}this.mapParam=this.opts.mapParam?this.opts.mapParam:null,this.markers=this.opts.markers?this.opts.markers:null,this.legendColor=this.opts.legendColor?this.opts.legendColor:a,this.opacity=this.opts.opacity?this.opts.opacity:.5,this.hoverColor=this.opts.hoverColor?this.opts.hoverColor:"#154e6e",this.fontSize=this.opts.fontSize?this.opts.fontSize:12,this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.fontOpacity=this.opts.fontOpacity?this.opts.fontOpacity:1,this.fontOffset=this.opts.fontOffset?this.opts.fontOffset:[0,-1],this.minZoom=this.opts.minZoom?this.opts.minZoom:5,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:12,this.maxExtrusionHeight=this.opts.maxExtrusionHeight?this.opts.maxExtrusionHeight:1e4,this.minExtrusionHeight=this.opts.minExtrusionHeight?this.opts.minExtrusionHeight:500,this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:function(){},this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:function(){},this.click=this.opts.click?this.opts.click:function(){},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this.labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"]},minzoom:this.minZoom,maxzoom:this.maxZoom,filter:["==","$type","Point"]},this.addGeoRegions(e)},MapPlatForm.Base.ShapMap3D.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;e>=0;e--)if(this._map._obj.style._order[e].indexOf("RegionLayer")>-1){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&o.length>0)return[o[0]]}return[]},MapPlatForm.Base.ShapMap3D.prototype._mouseout=function(){this._map._obj.getCanvas().style.cursor="",this.mouseOut&&this.activeFeature&&(this.refresh(),this.mouseOut(this.acvtiveRegion)),this.acvtiveRegion=null,this.activeFeature=null,this.acvtiveLayer=null},MapPlatForm.Base.ShapMap3D.prototype._initInteractive=function(t){this._map._events.through=!0;var e=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var i=e._getFeature(t);0!=i.length?e.activeFeature&&i[0].properties.id==e.activeFeature.properties.id||(e._mouseout(),i&&i.length>0&&(e._map._obj.getCanvas().style.cursor="pointer",e.activeFeature=i[0],e.acvtiveRegion=e._regions[i[0].properties.index],e.acvtiveLayer=e._layers[i[0].properties.index],e.mouseOver&&e.activeFeature&&(e.refresh(e.acvtiveLayer),e.mouseOver(e.acvtiveRegion)))):e._mouseout()}),!this._clickFun&&(this._clickFun=function(){e.acvtiveRegion&&e.click(e.acvtiveRegion)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.ShapMap3D.prototype._getCoordinates=function(t,e){for(var i=[],o=0;o<t.length;o++){for(var r=[],a=0;a<t[o].length;a++)if("MultiPolygon"==e){for(var s=[],n=0;n<t[o][a].length;n++){var l=t[o][a][n],h=NPMap.T.setPoint(this._map,new NPMap.Geometry.Point(l[0],l[1]));s.push([h.lon,h.lat])}r.push(s)}else{l=t[o][a],h=NPMap.T.setPoint(this._map,new NPMap.Geometry.Point(l[0],l[1]));r.push([h.lon,h.lat])}i.push(r)}return i},MapPlatForm.Base.ShapMap3D.prototype._getCenter=function(t){for(var e=0,i=0,o=0,r=0;r<t[0][0].length;r++){var a=t[0][0][r];e++,i+=a.lon,o+=a.lat}return{lon:i/e,lat:o/e}},MapPlatForm.Base.ShapMap3D.prototype.addGeoRegions=function(geoRegion){if(geoRegion.districts){for(var i=0;i<geoRegion.districts.length;i++)if(geoRegion.districts[i]){var coordinates=this._getCoordinates(geoRegion.districts[i].geometry.coordinates,geoRegion.districts[i].geometry.type);this._regions.push(geoRegion.districts[i]);var f={type:"Feature",geometry:{type:geoRegion.districts[i].geometry.type,coordinates:coordinates},properties:{id:NPMap.Utils.BaseUtils.uuid(),fillColor:this.legendColor[geoRegion.districts[i].name],index:i}},center=geoRegion.districts[i].center?GeoJSON.read(eval("("+geoRegion.districts[i].center+")")):this._getCenter(coordinates);center=NPMap.T.setPoint(this._map,center);var l={type:"Feature",geometry:{type:"Point",coordinates:[center.lon,center.lat]},properties:{id:NPMap.Utils.BaseUtils.uuid(),title:geoRegion.districts[i].name,"text-offset":this.fontOffset,"text-opacity":this.fontOpacity,"text-size":this.fontSize,"text-color":this.fontColor,index:i}};this._source.data.features.push(f),this._source.data.features.push(l)}this._map._obj.addSource(this._layerID+"_source",this._source);for(var i=0;i<geoRegion.districts.length;i++)if(geoRegion.districts[i]){var polygonLayer={id:this._layerID+"_polygon"+i,type:"fill-extrusion",source:this._layerID+"_source",paint:{"fill-extrusion-color":this.legendColor[geoRegion.districts[i].name],"fill-extrusion-opacity":this.opacity,"fill-extrusion-translate-anchor":"map","fill-extrusion-height":{stops:[[this.minZoom,this.maxExtrusionHeight],[this.maxZoom,this.minExtrusionHeight]]},"fill-extrusion-base":0},minzoom:this.minZoom,maxzoom:this.maxZoom,filter:["all",["==","index",i],["==","$type","Polygon"]]};this._layers.push(polygonLayer),this._map._obj.addLayer(polygonLayer)}for(var i=0;i<this._layers.length;i++)this._map._obj.setPaintProperty(this._layers[i].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[i].name]);this._map._obj.addLayer(this.labelLayer)}this.mapParam?this._map.jumpTo(this.mapParam):this._map.zoomToExtent(this.fullExtent),this.markers&&this._map.addOverlays(this.markers),this._initInteractive()},MapPlatForm.Base.ShapMap3D.prototype.setLegendColor=function(t){this.legendColor=t;for(var e=0;e<this._layers.length;e++)this._map._obj.setPaintProperty(this._layers[e].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[e].name])},MapPlatForm.Base.ShapMap3D.prototype.getRegionByName=function(t){if("string"==typeof t&&void 0!==t&&this._regions&&this._regions.length>0)for(var e=0;e<this._regions.length;e++)if(this._regions[e].name==t)return this._regions[e];return[]},MapPlatForm.Base.ShapMap3D.prototype.flash=function(t){var e;e="string"==typeof t&&void 0===t.name?this.getRegionByName(t):t;for(var i=0;i<this._regions.length;i++)e&&this._regions[i].name==e.name&&this._flash(i)},MapPlatForm.Base.ShapMap3D.prototype._flash=function(t){var e=this,i=0;this.timer&&(window.clearTimeout(this.timer),this.show(),this.timer=null);!function o(){if(++i%2){e._map._obj.setPaintProperty(e._layers[t].id,"fill-extrusion-opacity",0);for(var r=0;r<e._source.data.features.length;r++)e._source.data.features[r].properties.index===t&&(e._source.data.features[r].properties["text-opacity"]=0)}else{e._map._obj.setPaintProperty(e._layers[t].id,"fill-extrusion-opacity",.8);for(r=0;r<e._source.data.features.length;r++)e._source.data.features[r].properties.index===t&&(e._source.data.features[r].properties["text-opacity"]=1)}e._map._obj.getSource(e._layerID+"_source").setData(e._source.data),e.timer=i<6?window.setTimeout(o,500):null}()},MapPlatForm.Base.ShapMap3D.prototype.destory=function(){if(this._map){this._initInteractive(!0);for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map._obj.removeLayer(this.labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null}this._regions=[],this._layers=[],this.acvtiveRegion=null,this.activeFeature=null},MapPlatForm.Base.ShapMap3D.prototype.refresh=function(t){for(var e=0;e<this._layers.length;e++)this._map._obj.setPaintProperty(this._layers[e].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[e].name]);t&&this._map._obj.setPaintProperty(t.id,"fill-extrusion-color",this.hoverColor)}}(),MapPlatForm.Base.MapGeoLine=function(t,e){this.CLASS_NAME="MapGeoLine",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._lines={},this.activeFeature=null,this.activeLine=null,this.mouseOver=e?e.mouseOver:null,this.mouseOut=e?e.mouseOut:null,this.click=e?e.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]}},MapPlatForm.Base.MapGeoLine.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;e>=0;e--)if(this._map._obj.style._order[e].indexOf(this._layerID)>-1){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&o.length>0)return[o[0]]}return[]},MapPlatForm.Base.MapGeoLine.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activeLine&&this.mouseOut(this.activeLine),this.activeFeature=null},MapPlatForm.Base.MapGeoLine.prototype._initInteractive=function(t){var e=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var i=e._getFeature(t);0!=i.length?e.activeFeature&&i[0].properties.id==e.activeFeature.properties.id||(e._mouseout(),i&&i.length>0&&(e._map._obj.getCanvasContainer().style.cursor="pointer",e.activeFeature=i[0],e.activeLine=e._lines[i[0].properties.id],e.mouseOver&&e.activeLine&&e.mouseOver(e.activeLine))):e._mouseout()}),!this._clickFun&&(this._clickFun=function(){e.activeLine&&e.click(e.activeLine)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapGeoLine.prototype.addGeoLines=function(t){if(t&&t.length>0){for(var e=0;e<t.length;e++)this._lines[t[e].id]=t[e],this._source.data.features.push({type:"Feature",geometry:{type:"LineString",coordinates:t[e]._getCoordinates(this._map)},properties:{id:t[e].id,layerId:this._layerID,color:t[e]._color,width:t[e]._weight,hasDash:t[e]._lineStyle!=NPMap.LINE_TYPE_SOLID,opacity:t[e]._visible?t[e]._opacity:0,join:"round",visible:t[e]._visible}});this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._lineLayer)}this._initInteractive()},MapPlatForm.Base.MapGeoLine.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.removeLayer(this._lineLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activeLine=null,this._lines={}},MapPlatForm.Base.MapGeoLine.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapRoomControl=function(t,e){this.CLASS_NAME="MapRoomControl",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._activeID=null,this._minZoom=e&&e.showZoom?e.showZoom:17,this._floorControlStyle=e?e.floorControlStyle:null,this._events=new NPMap.Events(this,["changed"]),this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._floorRegionLayer={id:this._layerID+"_floor_region",type:"fill",source:this._layerID+"_source",paint:{"fill-color":"#f5f2eb","fill-opacity":1},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"]]},this._normalFloorLineLayer={id:this._layerID+"_normal_floor_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#adafb1","line-width":3},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"],["==","active",!1]]},this._activeFloorLineLayer={id:this._layerID+"_active_floor_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#f69428","line-width":3},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"],["==","active",!0]]},this._roomLayer={id:this._layerID+"_room",type:"fill-extrusion",source:this._layerID+"_source",layout:{},paint:{"fill-extrusion-color":["string",["get","colorvalue"]],"fill-extrusion-base":0,"fill-extrusion-height":1.5,"fill-extrusion-opacity":.8},minzoom:this._minZoom,filter:["all",["==","datatype","room"],["==","$type","Polygon"]]},this._labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{name}","text-font":["Microsoft YaHei"],"text-size":11,"text-offset":[0,0],"text-anchor":"center"},paint:{"text-color":"#3b3d3d","text-opacity":1,"text-halo-width":1,"text-halo-color":"#FFF"},minzoom:this._minZoom,filter:["==","$type","Point"]},this._addLayers(),this._mapService=new MapPlatForm.Base.MapService(t)},MapPlatForm.Base.MapRoomControl.prototype._addLayers=function(){this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._floorRegionLayer),this._map._obj.addLayer(this._normalFloorLineLayer),this._map._obj.addLayer(this._activeFloorLineLayer),this._map._obj.getLayer("city_normal_building_id")&&(this._map._obj.moveLayer(this._floorRegionLayer.id,"city_normal_building_id"),this._map._obj.moveLayer(this._normalFloorLineLayer.id,"city_normal_building_id"),this._map._obj.moveLayer(this._activeFloorLineLayer.id,"city_normal_building_id")),this._map._obj.addLayer(this._roomLayer),this._map._obj.addLayer(this._labelLayer)},MapPlatForm.Base.MapRoomControl.prototype.showFloorChangeInMap=function(){this._map.addEventListener("moveend",this._updateFloors,this),this._updateFloors()},MapPlatForm.Base.MapRoomControl.prototype._updateFloors=function(){var t=this._map.getZoom(),e=document.getElementById("NPGIS_Room_Control");if(t<this._minZoom?e&&(e.style.display="none"):e&&(e.style.display="block"),t>=this._minZoom&&t<21){var i=this._map.getContainer(),o=map._obj.unproject({x:0,y:0}),r=map._obj.unproject({x:i.offsetWidth,y:0}),a=map._obj.unproject({x:i.offsetWidth,y:i.offsetHeight}),s=map._obj.unproject({x:0,y:i.offsetHeight});o=new NPMap.Geometry.Point(o.lng,o.lat),r=new NPMap.Geometry.Point(r.lng,r.lat),a=new NPMap.Geometry.Point(a.lng,a.lat),s=new NPMap.Geometry.Point(s.lng,s.lat);var n=new NPMap.Geometry.Polygon([o,r,a,s,o]),l=this;this._mapService.queryRoomFloorsByGeometry(n,(function(t){l.addFloors(t.data)}))}},MapPlatForm.Base.MapRoomControl.prototype.addFloors=function(t){if(t||(t=[]),this.floors=t,0==t.length)return this._activeID=null,this._source.data.features=[],this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data),void this._removeControl();var e=[];if(this._activeID==t[0].id)for(var i=0;i<this._source.data.features.length;i++)this._source.data.features[i].properties.buildid==this._activeID&&e.push(this._source.data.features[i]);this._source.data.features=e;for(var o=0;o<t.length;o++)0==o?this._activeID!==t[o].id&&(this._activeID=t[o].id,this._removeControl(),this._addControl(t[o]),this.addFloor(t[o],!0)):this.addFloor(t[o],!1);this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapRoomControl.prototype._changeFloor=function(t,e){for(var i=[],o=0;o<this._source.data.features.length;o++)this._source.data.features[o].properties.active||i.push(this._source.data.features[o]);this._source.data.features=i;var r=this;this._mapService.queryRoomFloor(t,e,(function(t){r.addFloor(t.data[0],!0),r._map&&r._map._obj.getSource(r._layerID+"_source").setData(r._source.data)}))},MapPlatForm.Base.MapRoomControl.prototype.addFloor=function(t,e){var i=t.floor.floor,o=i.properties.namecode;i.properties={floorname:o,buildid:t.id,datatype:"floor",active:e},this._source.data.features.push(i);for(var r=0;r<t.floor.shops.length;r++){var a=t.floor.shops[r],s=a.properties.name,n=a.properties.font_anthor_point,l="#d4b7dd";"company"==a.properties.type?l="#b6f7e7":"food"==a.properties.type?l="#f1aff7":"shop"==a.properties.type?l="#a0f1f7":"elevator"==a.properties.type&&(l="#c6cdce"),a.properties={buildid:t.id,floorname:o,datatype:"room",colorvalue:l,active:e},this._source.data.features.push(a),this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:n},properties:{name:s,floorname:o,buildid:t.id,active:e}})}for(r=0;r<t.floor.cons.length;r++){var h=t.floor.cons[r],p=h.properties.name;h.properties={buildid:t.id,floorname:o,name:p,active:e},this._source.data.features.push(h)}for(r=0;r<t.floor.pubs.length;r++){var c=t.floor.pubs[r],u=c.properties.name;c.properties={buildid:t.id,floorname:o,name:u,active:e},this._source.data.features.push(c)}e&&this._events.triggerEvt("changed",{buildid:t.id,floorname:o})},MapPlatForm.Base.MapRoomControl.prototype._addControl=function(t){var e=map.getContainer(),i=document.createElement("div");i.id="NPGIS_Room_Control",i.className="npgis-indoormap-floorbar-control",i.title=t.building.properties.name_cn,this._floorControlStyle&&this._floorControlStyle.bottom&&(i.style.bottom=this._floorControlStyle.bottom),this._floorControlStyle&&this._floorControlStyle.right&&(i.style.right=this._floorControlStyle.right);var o=document.createElement("div");o.className="panel-box";var r=document.createElement("div");r.className="select-dock";var a=document.createElement("div");a.className="floor-list-box";var s=document.createElement("ul");s.className="floor-list";for(var n=0,l=2,h=t.building.properties.floor_nonas,p=t.id,c=this,u=h.length-1;u>=0;u--){h[u]==t.floor.floor.properties.namecode&&(n=u);var m=document.createElement("li");m.className="floor-list-item",m.data=u;var d=document.createElement("div");d.innerText=h[u],d.className="floor-btn floor-nonas",m.appendChild(d),m.addEventListener("click",(function(t){h.length>5?r.style.top=28*(l-t.currentTarget.data+5)+6+"px":r.style.top=28*(h.length-1-t.currentTarget.data)+"px";var e=t.currentTarget.innerText;e=(e=e.replace(/\r\n/g,"")).replace(/\n/g,""),c._changeFloor(p,e)})),s.appendChild(m)}if(a.appendChild(s),h.length>5){var y=document.createElement("div");y.className="floor-btn floor-nav floor-plus",y.addEventListener("click",(function(){l<h.length-5&&(l++,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")}));var _=document.createElement("div");_.className="floor-btn floor-nav floor-minus",_.addEventListener("click",(function(){l>0&&(l--,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")})),s.style.top=6-28*(h.length-1-n-2)+"px",r.style.top="90px",o.appendChild(y),o.appendChild(r),o.appendChild(a),o.appendChild(_),i.addEventListener("mousewheel",(function(t){t.deltaY<0?l<h.length-5&&(l++,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px"):l>0&&(l--,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")}),!1)}else r.style.top=28*(h.length-1-n)+"px",o.appendChild(r),o.appendChild(a);i.appendChild(o),e.appendChild(i)},MapPlatForm.Base.MapRoomControl.prototype._removeControl=function(){var t=document.getElementById("NPGIS_Room_Control");t&&t.remove()},MapPlatForm.Base.MapRoomControl.prototype.destroy=function(){this._map.removeEventListener("moveend",this._updateFloors,this),this._map._obj.removeLayer(this._floorRegionLayer.id),this._map._obj.removeLayer(this._normalFloorLineLayer.id),this._map._obj.removeLayer(this._activeFloorLineLayer.id),this._map._obj.removeLayer(this._roomLayer.id),this._map._obj.removeLayer(this._labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._removeControl(),this._mapService=null,this._events=null},MapPlatForm.Base.MapRoomControl.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},MapPlatForm.Base.MapRoomControl.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},MapPlatForm.Base.MapGeoRegion=function(t,e){this.CLASS_NAME="MapGeoRegion",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._polygons={},this._lines={},this.activeFeature=null,this.activePolygon=null,this.activeLine=null,this.opts=e||{},this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:null,this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:null,this.click=this.opts.click?this.opts.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._polygonLayer={id:this._layerID+"_polygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]},filter:["==","$type","Polygon"]},this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]},this._activePolygonLayer={id:this._layerID+"_activePolygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]},filter:["==","$type","Polygon"]},this._activeLineLayer={id:this._layerID+"_activeLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]},this._labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"]},filter:["==","$type","Point"]}},MapPlatForm.Base.MapGeoRegion.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;e>=0;e--)if(this._map._obj.style._order[e].indexOf(this._layerID)>-1){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&o.length>0)return[o[0]]}return[]},MapPlatForm.Base.MapGeoRegion.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activePolygon&&this.mouseOut(this.activePolygon),this.activeFeature=null,this.activeLine=null,this.activePolygon=null},MapPlatForm.Base.MapGeoRegion.prototype._initInteractive=function(t){var e=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var i=e._getFeature(t);0!=i.length?e.activeFeature&&i[0].properties.id==e.activeFeature.properties.id||(e._mouseout(),i&&i.length>0&&(e._map._obj.getCanvasContainer().style.cursor="pointer",e.activeFeature=i[0],e.activePolygon=e._polygons[i[0].properties.id],e.activeLine=e._lines[i[0].properties.id+"line"],e.mouseOver&&e.activePolygon&&(e.mouseOver(e.activePolygon),e._map._obj.setFilter(e._activePolygonLayer.id,["==","id",e.activePolygon.id]),e._map._obj.setFilter(e._activeLineLayer.id,["==","id",e.activeLine.properties.id])))):e._mouseout()}),!this.clickFun&&(this.clickFun=function(){e.activePolygon&&e.click(e.activePolygon)}),t?(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this.clickFun)):(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this.clickFun))},MapPlatForm.Base.MapGeoRegion.prototype.addGeoRegions=function(t,e){var i=(e=e||{}).fontSize?e.fontSize:12,o=e.fontColor?e.fontColor:"#000000",r=e.fontOpacity?e.fontOpacity:1,a=e.fontOffset?e.fontOffset:[0,0],s=!0;if(t&&t.length>0){for(var n=0;n<t.length;n++){t[n]&&"NPMap.Geometry.Polygon"!=t[n].CLASS_NAME&&(s=!1);var l=s?t[n]:GeoJSON.read(t[n].geometry);l._fillColor=s||void 0===t[n].properties.fillColor?l._fillColor:t[n].properties.fillColor,l._fillOpacity=s||void 0===t[n].properties.fillOpacity?l._fillOpacity:t[n].properties.fillOpacity,l._weight=s||void 0===t[n].properties.weight?l._weight:t[n].properties.weight,l._color=s||void 0===t[n].properties.color?l._color:t[n].properties.color,l._opacity=s||void 0===t[n].properties.opacity?l._opacity:t[n].properties.opacity,this._polygons[l.id]=l;var h={type:"Feature",geometry:{type:l.polygonType,coordinates:l._getCoordinates(this._map)},properties:{id:l.id,fillColor:l._fillColor,fillOpacity:l._fillOpacity}};this._source.data.features.push(h);for(var p=0;p<l._getCoordinates(this._map).length;p++){var c={type:"Feature",geometry:{type:"LineString",coordinates:l._getCoordinates(this._map)[p][0]},properties:{id:l.id+"line",color:l._color,width:l._weight,opacity:l._opacity,join:"round"}};this._lines[l.id+"line"]=c,this._source.data.features.push(c)}if(!s||void 0!==t[n].properties.name){var u={type:"Feature",geometry:{type:"Point",coordinates:[l.getCenter().lon,l.getCenter().lat]},properties:{id:l.id,title:t[n].properties.name,"text-offset":a,"text-opacity":r,"text-size":i,"text-color":o}};this._source.data.features.push(u)}}this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._polygonLayer),this._map._obj.addLayer(this._lineLayer),this._map._obj.addLayer(this._activePolygonLayer),this._map._obj.addLayer(this._activeLineLayer),s&&void 0===t[n].properties.name||this._map._obj.addLayer(this._labelLayer)}this._initInteractive()},MapPlatForm.Base.MapGeoRegion.prototype.setStyle=function(t){t&&t.fillColor&&this._map._obj.setPaintProperty(this._layerID+"_activePolygon","fill-color",t.fillColor),t&&t.fillOpacity&&this._map._obj.setPaintProperty(this._layerID+"_activePolygon","fill-opacity",t.fillOpacity),t&&t.color&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-color",t.color),t&&t.width&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-width",t.width),t&&t.opacity&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-opacity",t.opacity)},MapPlatForm.Base.MapGeoRegion.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.addLayer(this._polygonLayer.id),this._map._obj.addLayer(this._lineLayer.id),this._map._obj.addLayer(this._activePolygonLayer.id),this._map._obj.addLayer(this._activeLineLayer.id),this._labelLayer&&this._map._obj.addLayer(this._labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activePolygon=null,this.activeLine=null,this._polygons={},this._lines={}},MapPlatForm.Base.MapGeoRegion.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},function(){var t=MapPlatForm.Base.FlyManager=function(t){this._map=t,this.stops=[],this.currentIndex=0,this.status=!1,this.events=new NPMap.Events(this,["preStop","afterStop"])};t.prototype.addStops=function(t){this.stops=t},t.prototype.play=function(t){this.status||(this.currentIndex=0,this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this.status=!0,this.isRepeat=t,this._map.disableMapOperation(),this._play())},t.prototype._play=function(){var t=this;this.status&&this._map.setCenter(this.stops[t.currentIndex].position,this.stops[t.currentIndex].zoom,{bearing:this.stops[t.currentIndex].bearing,pitch:this.stops[t.currentIndex].pitch,speed:this.stops[t.currentIndex].speed,curve:this.stops[t.currentIndex].curve,callback:function(){t.status&&(t.events.triggerEvt("afterStop",{stop:t.stops[t.currentIndex]}),setTimeout((function(){t.currentIndex++,t.status&&(t.currentIndex<t.stops.length?(t.events.triggerEvt("preStop",{stop:t.stops[t.currentIndex]}),t._play()):t.isRepeat?(t.currentIndex=0,t.events.triggerEvt("preStop",{stop:t.stops[t.currentIndex]}),t._play()):(t.status=!1,t._map.enableMapOperation()))}),t.stops[t.currentIndex].waitTime))},isOnlyFly:!0})},t.prototype.stop=function(){this._map._obj.stop(),this._map.enableMapOperation(),this.status=!1,this.currentIndex=0},t.prototype.rePlay=function(){this._map._obj.stop(),this.currentIndex=0,this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this._play()},t.prototype.continus=function(){this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this._play()},t.prototype.pause=function(){this._map._obj.stop(),this.status=!1},t.prototype.addEventListener=function(t,e,i){this.events.register(t,e)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)}}(),MapPlatForm.Base.Stop=function(t,e){this.position=t,this.bearing=e&&e.bearing?e.bearing:0,this.pitch=e&&e.pitch?e.pitch:0,this.zoom=e&&e.zoom?e.zoom:14,this.waitTime=e&&e.waitTime?e.waitTime:0,this.speed=e&&e.speed?e.speed:1.2,this.curve=e&&e.curve?e.curve:1.42,this.name=e&&e.name?e.name:"未知站点"},MapPlatForm.Base.Stop.prototype.setData=function(t){this._data=t},MapPlatForm.Base.Stop.prototype.getData=function(){return this._data},function(){var t=function(t,e,i,o,r,a){this.id=NPMap.Utils.BaseUtils.uuid(),this.maxR=e||1e3,this.minR=i||0,this.point=t,this.defaultR=o,this._legend=r||[0,"rgba(0,255,0,0)",.5,"rgba(0,255,0,0.8)",1,"rgba(255,0,0,0.8)"],this._layer={id:this.id,type:"heatmap",source:a,minzoom:0,maxzoom:22,paint:{"heatmap-weight":["interpolate",["linear"],["get","value"],0,0,1,1],"heatmap-intensity":1,"heatmap-color":["interpolate",["linear"],["heatmap-density"]],"heatmap-radius":100,"heatmap-opacity":.8},filter:["==","id",this.id]};for(var s=0;s<this._legend.length;s++)this._layer.paint["heatmap-color"].push(this._legend[s]);this.addMap=function(t){this._map=t,t._obj.addLayer(this._layer)},this._getPixelDistance=function(t,e){var i=this._map._obj.unproject({x:this._map._obj.transform.width,y:this._map._obj.transform.height/2}),o=this._map._obj.getCenter();return e/this._map.getDistance({lon:i.lng,lat:i.lat},{lon:o.lng,lat:o.lat})*this._map._obj.transform.width/2},this.removeMap=function(){this._map&&this._map.getLayer(this._layer.id)&&this._map._obj.removeLayer(this._layer.id)},this.changeRadius=function(t){this.defaultR=t;var e=this._getPixelDistance(this.point,t);this._layer.paint["heatmap-radius"]=e,this._map&&this._map._obj.getLayer(this._layer.id)&&(this.defaultR==this.minR?(this._map._obj.removeLayer(this._layer.id),this._map._obj.addLayer(this._layer)):(this._map._obj.setPaintProperty(this._layer.id,"heatmap-opacity",1-this.defaultR/this.maxR),this._map._obj.setPaintProperty(this._layer.id,"heatmap-radius",e)))},this.updataRadius=function(){var t=this._map._obj.getCenter(),e=this._getPixelDistance({lon:t.lng,lat:t.lat},this.defaultR);this._layer.paint["heatmap-radius"]=e,this._map&&this._map._obj.getLayer(this._layer.id)&&this._map._obj.setPaintProperty(this._layer.id,"heatmap-radius",e)}},e=MapPlatForm.Base.AnimationCircle=function(t){this._map=t,this.id=NPMap.Utils.BaseUtils.uuid(),this._visible=!0,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._circles=[]};e.prototype._eventsFun=function(t){if(!this._callBack){var e=this;this._callBack=function(){for(var t=0;t<e._circles.length;t++)e._circles[t].updataRadius()}}t?(this._map._obj.on("zoom",this._callBack),this._map._obj.on("zoomend",this._callBack)):(this._map._obj.off("zoom",this._callBack),this._map._obj.off("zoomend",this._callBack))},e.prototype.addCircles=function(e){if(e&&e.length>0){for(var i=0;i<e.length;i++){var o=Math.floor(Math.random()*(e[i].maxR-e[i].minR)+e[i].minR),r=new t(e[i].point,e[i].maxR,e[i].minR,o,e[i].legend,this.id);this._circles.push(r)}this._setData(this._circles);for(i=0;i<this._circles.length;i++)this._circles[i].addMap(this._map),this._circles[i].updataRadius()}},e.prototype._setData=function(t){for(var e=0,i=t.length;e<i;e++){var o=this._map?NPMap.T.setPoint(this._map,t[e].point):t[e].point;this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:[o.lon,o.lat]},properties:{value:1,id:t[e].id}})}this._map._obj.getSource(this.id)?this._map._obj.getSource(this.id).setData(this._source.data):this._map._obj.addSource(this.id,this._source)},e.prototype.start=function(){var t=this;requestAnimationFrame((function(){for(var e=0;e<t._circles.length;e++){var i=t._circles[e].defaultR;(i+=20)>t._circles[e].maxR&&(i=t._circles[e].minR),t._circles[e].changeRadius(i)}t.start()}))}}(),function(){var t=MapPlatForm.Base.MapAnimationCircle=function(t,e){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=e&&e.minZoom?e.minZoom:0,this._maxZoom=e&&e.maxZoom?e.maxZoom:22,this._layer={id:this._layerID,type:"fill",source:this._layerID,minzoom:this._minZoom,maxzoom:this._maxZoom,paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]}},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._map._obj.addSource(this._layerID,this._source),this._map._obj.addLayer(this._layer)};t.prototype.addCircles=function(t){if(t&&t.length>0){this._circles=[];for(var e=0;e<t.length;e++)if(t[e]&&t[e].point){var i=t[e].maxR?t[e].maxR:2e3,o=Math.floor(Math.random()*(i-0)),r={point:t[e].point,maxR:i,defaultR:o,color:t[e].color?t[e].color:"#FFFFFF"};this._circles.push(r)}this._setData(this._circles)}},t.prototype._getCoords=function(t,e){for(var i=this._map?NPMap.T.setPoint(this._map,t):t,o=NPMap.T.helper.webMoctorJW2PM(i.lon,i.lat),r=NPMap.Utils.MapUtil.createRegularPolygon(o,e,60),a=[],s=[],n=0;n<r.length;n++){var l=NPMap.T.helper.inverseMercator(r[n].lon,r[n].lat);s.push([l.lon,l.lat])}return a.push(s),a},t.prototype._setData=function(t){for(var e=0,i=t.length;e<i;e++)this._source.data.features.push({type:"Feature",geometry:{type:"Polygon",coordinates:this._getCoords(t[e].point,t[e].defaultR)},properties:{fillColor:t[e].color,fillOpacity:1-t[e].defaultR/t[e].maxR,defaultR:t[e].defaultR,maxR:t[e].maxR,center:t[e].point}});this._map._obj.getSource(this._layerID)?this._map._obj.getSource(this._layerID).setData(this._source.data):this._map._obj.addSource(this._layerID,this._source)},t.prototype.start=function(){this._remove=!1,this._start()},t.prototype._start=function(){var t=this;requestAnimationFrame((function(){if(!t._remove){for(var e=performance.now()%5e3/5e3,i=0;i<t._source.data.features.length;i++){var o=t._source.data.features[i].properties.maxR,r=o/2*.9*e+o/2*.1;t._source.data.features[i].properties.defaultR=r,t._source.data.features[i].properties.fillOpacity=.3*(1-e),t._source.data.features[i].geometry.coordinates=t._getCoords(t._source.data.features[i].properties.center,r)}t._map._obj.getSource(t._layerID).setData(t._source.data),t._start()}}))},t.prototype.destory=function(){this._remove=!0,this._map._obj.getLayer(this._layerID)&&this._map._obj.removeLayer(this._layerID),this._map._obj.getSource(this._layerID)&&this._map._obj.removeSource(this._layerID),this._map=null}}(),function(){var t=function(t,e,i){this.map=t,this.tb=e,this.id=i.id,this.centerPoint=i.centerPoint,this.height=i.height,this.position=i.position;var o=i.points;function r(t,e){return{x:e/Math.sqrt(t*t+e*e),y:-t/Math.sqrt(t*t+e*e)}}this.name=i&&i.name?i.name:"",this.isBloom=null!=i.isBloom&&i.isBloom,this.floorNum=i.floorNum,this.buildBufferGeometry=new THREE.BufferGeometry,this.material=i.material,this.verticesData={},this.verticesData.repeat=null!=i.repeat&&i.repeat,this.verticesData.vecColor=null!=this.material.side.vecColor&&this.material.side.vecColor,this.verticesData.topColor=this.material.side.topColor?this.material.side.topColor:"white",this.verticesData.bottomColor=this.material.side.bottomColor?this.material.side.bottomColor:"white",this.vertices=[],this.normals=[],this.uvs=[],this.colors=[],this.verticesData.vertices=[],this.verticesData.normals=[],this.verticesData.uvs=[],this.verticesData.colors=[],this.verticesData.topvertices=[],this.verticesData.topnormals=[],this.verticesData.topuvs=[],this.verticesData.topcolors=[];for(var a=0,s=[],n=e.utils.projectToWorld([this.position.lon,this.position.lat,0]),l=0;l<o.length-1;l++){var h=NPMap.T.setPoint(t,new NPMap.Geometry.Point(o[l][0],o[l][1])),p=e.utils.projectToWorld([h.lon,h.lat,this.height]),c=[p.x-n.x,p.y-n.y,0];0==a&&(a=p.z),s.push(c)}!function(t,e,i,o){for(var a=t[0],s=[new THREE.Vector2(a[0],a[1])],n=a[0],l=a[0],h=a[0],p=a[0],c=1;c<t.length;c++)t[c][0]>l&&(l=t[c][0]),t[c][0]<n&&(n=t[c][0]),t[c][1]>p&&(p=t[c][1]),t[c][1]<h&&(h=t[c][1]),s.push(new THREE.Vector2(t[c][0],t[c][1]));var u=l-n,m=p-h,d=[],y=[];o.vecColor&&(d=NPMap.Utils.MapUtil.colorRgb(o.topColor),y=NPMap.Utils.MapUtil.colorRgb(o.bottomColor));var _=THREE.ShapeUtils.triangulateShape(s,[]);for(c=0;c<_.length;c++){var f=_[c];o.topvertices.push(s[f[0]].x,s[f[0]].y,e),o.topvertices.push(s[f[1]].x,s[f[1]].y,e),o.topvertices.push(s[f[2]].x,s[f[2]].y,e),o.topnormals.push(0,0,1),o.topnormals.push(0,0,1),o.topnormals.push(0,0,1),o.topuvs.push((s[f[0]].x-n)/u,(s[f[0]].y-h)/m),o.topuvs.push((s[f[1]].x-n)/u,(s[f[1]].y-h)/m),o.topuvs.push((s[f[2]].x-n)/u,(s[f[2]].y-h)/m),o.vecColor&&(o.topcolors.push(d[0]/256,d[1]/256,d[2]/256),o.topcolors.push(d[0]/256,d[1]/256,d[2]/256),o.topcolors.push(d[0]/256,d[1]/256,d[2]/256))}for(c=0;c<t.length;c++){var g=c+1;g==t.length&&(g=0);var v=r(t[g][0]-t[c][0],t[g][1]-t[c][1]);o.vertices.push(t[c][0],t[c][1],t[c][2]+e),o.vertices.push(t[g][0],t[g][1],t[g][2]),o.vertices.push(t[c][0],t[c][1],t[c][2]),o.normals.push(v.x,v.y,0),o.normals.push(v.x,v.y,0),o.normals.push(v.x,v.y,0),o.vecColor&&(o.colors.push(d[0]/256,d[1]/256,d[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256));var M=Math.round(i/5);M<1&&(M=1),o.repeat?(o.uvs.push(M/5,1),o.uvs.push(0,0),o.uvs.push(0,1)):(o.uvs.push(1,1),o.uvs.push(0,0),o.uvs.push(0,1)),o.vertices.push(t[c][0],t[c][1],t[c][2]+e),o.vertices.push(t[g][0],t[g][1],t[g][2]+e),o.vertices.push(t[g][0],t[g][1],t[g][2]),o.normals.push(v.x,v.y,0),o.normals.push(v.x,v.y,0),o.normals.push(v.x,v.y,0),o.vecColor&&(o.colors.push(d[0]/256,d[1]/256,d[2]/256),o.colors.push(d[0]/256,d[1]/256,d[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256)),o.repeat?(o.uvs.push(M/5,1),o.uvs.push(M/5,0),o.uvs.push(0,0)):(o.uvs.push(1,1),o.uvs.push(1,0),o.uvs.push(0,0))}}(s,a,this.floorNum,this.verticesData),this.buildBufferGeometry.addGroup(this.vertices.length/3,this.verticesData.topvertices.length/3,0);for(var u=0;u<this.verticesData.topvertices.length;u++)this.vertices.push(this.verticesData.topvertices[u]);for(u=0;u<this.verticesData.topnormals.length;u++)this.normals.push(this.verticesData.topnormals[u]);for(u=0;u<this.verticesData.topuvs.length;u++)this.uvs.push(this.verticesData.topuvs[u]);for(u=0;u<this.verticesData.topcolors.length;u++)this.colors.push(this.verticesData.topcolors[u]);this.buildBufferGeometry.addGroup(this.vertices.length/3,this.verticesData.vertices.length/3,1);for(u=0;u<this.verticesData.vertices.length;u++)this.vertices.push(this.verticesData.vertices[u]);for(u=0;u<this.verticesData.normals.length;u++)this.normals.push(this.verticesData.normals[u]);for(u=0;u<this.verticesData.uvs.length;u++)this.uvs.push(this.verticesData.uvs[u]);for(u=0;u<this.verticesData.colors.length;u++)this.colors.push(this.verticesData.colors[u]);var m,d,y,_=new Float32Array(this.vertices.length);(_.set(this.vertices),parseInt(THREE.REVISION)>=110)?(this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(_,3)),(m=new Float32Array(this.normals.length)).set(this.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(m,3)),(d=new Float32Array(this.uvs.length)).set(this.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(d,2)),(y=new Float32Array(this.colors.length)).set(this.colors),this.buildBufferGeometry.setAttribute("color",new THREE.BufferAttribute(y,3))):(this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(_,3)),(m=new Float32Array(this.normals.length)).set(this.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(m,3)),(d=new Float32Array(this.uvs.length)).set(this.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(d,2)),(y=new Float32Array(this.colors.length)).set(this.colors),this.buildBufferGeometry.setAttribute("color",new THREE.BufferAttribute(y,3)));this.mesh=new THREE.Mesh(this.buildBufferGeometry,[this.material.top,this.material.side]),this.mesh.name="GeoBuilding3D-"+i.id,this.mesh.eventID=i.id},e=MapPlatForm.Base.MapGeoBuilding3D=function(t,e,i){this._map=t,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.textureImageUrl=i&&i.textureImageUrl?i.textureImageUrl:void 0,this.buildTopColor=i&&i.buildTopColor?i.buildTopColor:"rgb(53,86,160)",this.buildSideColor=i&&i.buildSideColor?i.buildSideColor:["rgb(53,86,160)"],this.transparent=!(!i||null==i.transparent)&&i.transparent,this.repeat=!(!i||null==i.repeat)&&i.repeat,this.opacity=i&&i.opacity?i.opacity:1,this.buildMaterialStyle={textureImageUrl:this.textureImageUrl,buildTopColor:this.buildTopColor,buildSideColor:this.buildSideColor,transparent:this.transparent,repeat:this.repeat,opacity:this.opacity},this.edgesLineColor=i&&i.edgesLineColor?i.edgesLineColor:"yellow",this.tb=e,this.timer=null;var o=this;this.animationSpeed=i&&i.animationSpeed?i.animationSpeed:.15,this.beforeLayerID=i&&i.beforeLayerID?i.beforeLayerID:"city_normal_building_id",this.buildingsArrayGeoJson=[],this._geoBuilding3Ds=[],this._setBuildMaterial(this.buildMaterialStyle),o._map._obj.on("click",(function(t){var e=function(t){for(var e=o.tb.queryRenderedFeatures(t),i=0;i<e.length;i++)if(-1!=e[i].object.name.indexOf("GeoBuilding3D"))return e[i];return null}(t.point);if(e){var i=e.object.eventID,r=o._geoBuilding3Ds[i];o._events.triggerEvt("click",{build:r})}}))};e.prototype._setBuildMaterial=function(t){t.textureImageUrl?(this.texture=(new THREE.TextureLoader).load(t.textureImageUrl),this.texture.wrapS=this.texture.wrapT=THREE.RepeatWrapping,this.texture.repeat.set(t.repeat?5:1,1),this.buildSideMaterial=new THREE.MeshStandardMaterial({map:this.texture,transparent:t.transparent,opacity:t.opacity}),this.buildSideMaterial.repeat=t.repeat,this.buildTopMaterial=new THREE.MeshStandardMaterial({color:t.buildTopColor,transparent:t.transparent,opacity:t.opacity})):t.buildSideColor.length>1?(this.buildSideMaterial=new THREE.MeshStandardMaterial({vertexColors:THREE.VertexColors,transparent:t.transparent,opacity:t.opacity}),this.buildSideMaterial.topColor=t.buildSideColor[0],this.buildSideMaterial.bottomColor=t.buildSideColor[1],this.buildSideMaterial.vecColor=!0,this.buildTopMaterial=new THREE.MeshStandardMaterial({vertexColors:THREE.VertexColors,transparent:t.transparent,opacity:t.opacity})):(this.buildSideMaterial=new THREE.MeshStandardMaterial({color:t.buildSideColor[0],transparent:t.transparent,opacity:t.opacity}),this.buildTopMaterial=new THREE.MeshStandardMaterial({color:t.buildTopColor,transparent:t.transparent,opacity:t.opacity})),this.buildMaterials={},this.buildMaterials.top=this.buildTopMaterial,this.buildMaterials.side=this.buildSideMaterial},e.prototype.addBuildings=function(e){function i(t){if(!(t.length<2)){for(var e=0,i=0,o=0,r=t[1],a=2;a<t.length;a++){var s=t[a],n=(t[0][0]*r[1]+r[0]*s[1]+s[0]*t[0][1]-r[0]*t[0][1]-s[0]*r[1]-t[0][0]*s[1])/2;o+=n,e+=(t[0][0]+r[0]+s[0])*n,i+=(t[0][1]+r[1]+s[1])*n,r=s}var l=e/o/3,h=i/o/3;return new NPMap.Geometry.Point(l,h)}}this.buildingsArrayGeoJson=e,this._geoBuilding3Ds=[];var o=this;for(var r in o._geoBuilding3Ds=function(e,r,a){for(var s=[],n=0;n<e.length;n++){var l=e[n].points,h=e[n].floorNum,p=i(l),c="";e[n].name&&(c=e[n].name);var u,m=e[n].height,d=new NPMap.Geometry.Point(e[n].points[0][0],e[n].points[0][1]),y=e[n].id,_=!!e[n].isBloom&&e[n].isBloom,f=NPMap.T.setPoint(r,d),g={id:y,material:o.buildMaterials,floorNum:h,repeat:o.repeat,centerPoint:p,height:m,name:c,position:f,points:l,isBloom:_};(u=new t(o._map,a,g)).data=e[n].data?e[n].data:{},s[y]=u}return s}(o.buildingsArrayGeoJson,o._map,o.tb),o._geoBuilding3Ds)o._geoBuilding3Ds[r].isBloom?o._geoBuilding3Ds[r].mesh.layers.set(1):o._geoBuilding3Ds[r].mesh.layers.set(0),o.tb.addAtCoordinate(o._geoBuilding3Ds[r].mesh,[o._geoBuilding3Ds[r].position.lon,o._geoBuilding3Ds[r].position.lat])},e.prototype.changeBuildMaterial=function(t){for(var e in t.repeat=this.repeat,this._setBuildMaterial(t),this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.material=[this.buildTopMaterial,this.buildSideMaterial]},e.prototype.upBuildHeight=function(t){if(!(t>1||0==t))for(var e in this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.scale.set(1,1,t)},e.prototype.downBuildHeight=function(t){if(!(t<0||0==t))for(var e in this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.scale.set(1,1,t)},e.prototype._animationDownBuild=function(t){var e=this;this.timer=setTimeout((function(){e.scale=e.scale-.05,e.downBuildHeight(e.scale),e.scale<=0?t&&t instanceof Function&&t():e._animationDownBuild(t)}),50)},e.prototype.animationDownBuild=function(t){this.scale=1,clearTimeout(this.timer),this._animationDownBuild(t)},e.prototype._animationUpBuild=function(t){var e=this;this.timer=setTimeout((function(){e.scale=e.scale+.05,e.upBuildHeight(e.scale),e.scale>=1?t&&t instanceof Function&&t():e._animationUpBuild(t)}),50)},e.prototype.animationUpBuild=function(t){this.scale=0,clearTimeout(this.timer),this._animationUpBuild(t)},e.prototype.getBuildings=function(){return this._geoBuilding3Ds},e.prototype.setBuildSelectStyle=function(t){this.edgesLineColor=t&&t.edgesLineColor?t.edgesLineColor:this.edgesLineColor,this.setBuildSelection(this._selections)},e.prototype.setBuildSelection=function(t){for(var e in this.deleteBuildSelection(),this._selections=t,t)if(t[e]&&-1!=t[e].mesh.name.indexOf("GeoBuilding3D")){var i=new THREE.EdgesGeometry(t[e].buildBufferGeometry,1),o=new THREE.LineBasicMaterial({color:this.edgesLineColor}),r=new THREE.LineSegments(i,o);r.name="cubeLine"+t[e].id,this.tb.addAtCoordinate(r,[t[e].position.lon,t[e].position.lat])}},e.prototype.deleteBuildSelection=function(){for(var t in this._selections)this.tb.world.getObjectByName("cubeLine"+this._selections[t].id)&&this.tb.remove(this.tb.world.getObjectByName("cubeLine"+this._selections[t].id));this._selections=[]},e.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},e.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},e.prototype.destory=function(){for(var t in this._geoBuilding3Ds){var e=this._geoBuilding3Ds[t].id;this.tb.remove(this._geoBuilding3Ds[e].mesh),this._geoBuilding3Ds[t].buildBufferGeometry=null,this._geoBuilding3Ds[t].mesh=null,this._geoBuilding3Ds[t]=null}this._geoBuilding3Ds=null}}(),function(){var t=function(t,e,i){this.position=t,this.points=e,this.material=i.material,this.centerPoint=i.centerPoint,this.floorIndoorHeight=i.floorIndoorHeight,this.floorIndoorLabelHeight=i.floorIndoorLabelHeight,this.meshName=i.meshName,this.shape=new THREE.Shape;for(var o=0;o<e.length;o++)0==o?this.shape.moveTo(e[o][0],e[o][1]):this.shape.lineTo(e[o][0],e[o][1]);this.geometry=new THREE.ExtrudeGeometry(this.shape,{depth:this.points[0][2],steps:parseInt(1),bevelEnabled:!1});var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)r=Math.max(r,this.geometry.vertices[this.geometry.faces[o].a].z);var a=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==r&&this.geometry.vertices[this.geometry.faces[o].b].z==r&&this.geometry.vertices[this.geometry.faces[o].c].z==r?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),a+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(a+=1);if(a>=2)for(o=a;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.floorIndoorHeight=this.floorIndoorHeight,this.mesh.floorIndoorLabelHeight=this.floorIndoorLabelHeight,this.mesh.name=this.meshName},e=function(t,e){this.material=e.material,this.geometry=e.geometry;for(var i=0,o=0;o<this.geometry.faceVertexUvs[0].length;o++)i=Math.max(i,this.geometry.vertices[this.geometry.faces[o].a].z);var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==i&&this.geometry.vertices[this.geometry.faces[o].b].z==i&&this.geometry.vertices[this.geometry.faces[o].c].z==i?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),r+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(r+=1);if(r>=2)for(o=r;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.name=t},i=function(t,e,i){this.meshName=i.meshName,this.shape=new THREE.Shape,this.material=i.material,this.realHeight=i.height,this.floorNum=i.floorNum,this.position=t,this.points=e,this.count=this.points.length;for(var o=0;o<e.length;o++)0==o?this.shape.moveTo(e[o][0],e[o][1]):this.shape.lineTo(e[o][0],e[o][1]);this.geometry=new THREE.ExtrudeGeometry(this.shape,{depth:this.points[0][2],steps:parseInt(this.floorNum),bevelEnabled:!1});var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)r=Math.max(r,this.geometry.vertices[this.geometry.faces[o].a].z);var a=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==r&&this.geometry.vertices[this.geometry.faces[o].b].z==r&&this.geometry.vertices[this.geometry.faces[o].c].z==r?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),a+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(a+=1);if(a>=2)for(o=a;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.name=this.meshName},o=MapPlatForm.Base.MapGeoIndoor3D=function(t,e){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=e&&e.minZoom?e.minZoom:11,this._maxZoom=e&&e.maxZoom?e.maxZoom:22,this.textureImageUrl=e&&e.textureImageUrl?e.textureImageUrl:void 0,this.buildTopColor=e&&e.buildTopColor?e.buildTopColor:"rgb( 65,105,225)",this.buildSideColor=e&&e.buildSideColor?e.buildSideColor:"rgb(44,72,139)",this.buildInternalTopColor=e&&e.buildInternalTopColor?e.buildInternalTopColor:"rgb(51,69,92)",this.buildInternalSideColor=e&&e.buildInternalSideColor?e.buildInternalSideColor:"rgb(137,125,139)",this.opacity=e&&e.opacity?e.opacity:1,this.isTransparent=null==e.isTransparent||e.isTransparent,this._animation=null==e.animation||e.animation,this.isCollision=null==e.isCollision||e.isCollision,this.tb=null,this.timer=null;var i=this;this.notCollisionMeshs=[],this.notCollisionBounds=[],this.notCollisionIndexs=[],this._buildingsLayer={id:this._layerID,type:"custom",renderingMode:"3d",render:function(t,e){t.enable(t.DEPTH_TEST),i._map.getZoom()>=i._minZoom&&(i.tb.update(),i.isCollision&&i._map._obj.isMoving()&&i._updateMarker3D(i))}},this.buildTopMaterial=new THREE.MeshLambertMaterial({color:this.buildTopColor,transparent:this.isTransparent,opacity:this.opacity}),null==this.textureImageUrl?this.buildSideMaterial=new THREE.MeshLambertMaterial({color:this.buildSideColor,transparent:this.isTransparent,opacity:this.opacity}):(this.buildingtexture=(new THREE.TextureLoader).load(this.textureImageUrl),this.buildSideMaterial=new THREE.MeshLambertMaterial({map:this.buildingtexture,transparent:this.isTransparent,opacity:this.opacity,depthTest:!0,depthWrite:!0,wireframe:!1})),this.floorTopMaterial=new THREE.MeshLambertMaterial({color:this.buildTopColor,transparent:this.isTransparent,opacity:this.opacity}),null==this.textureImageUrl?this.floorSideMaterial=new THREE.MeshLambertMaterial({color:this.buildSideColor,transparent:this.isTransparent,opacity:this.opacity}):(this.buildingtexture=(new THREE.TextureLoader).load(this.textureImageUrl),this.floorSideMaterial=new THREE.MeshLambertMaterial({map:this.buildingtexture,transparent:this.isTransparent,opacity:this.opacity,depthTest:!0,depthWrite:!0,wireframe:!1})),this.buildInternalTopMaterial=new THREE.MeshLambertMaterial({color:this.buildInternalTopColor,transparent:this.isTransparent,opacity:this.opacity}),this.buildInternalSideMaterial=new THREE.MeshLambertMaterial({color:this.buildInternalSideColor,transparent:this.isTransparent,opacity:this.opacity})};o.prototype.addBuildings=function(t){this.buildingsArrayGeoJson=t,this._geoBuilding3Ds;var e=this;this._map&&this._map._obj.getLayer(this._layerID)&&(this._map._obj.removeLayer(this._layerID),this.tb=null);e=this;this._buildingsLayer.onAdd=function(t,o){e.tb=new Threebox(t,o,{defaultLights:!0}),e._geoBuilding3Ds=function(t,o,r){for(var a=[],s=0;s<t.length;s++){var n=t[s].building.geometry.coordinates[0],l=2e4*t[s].building.properties.height,h=t[s].building.properties.numberofFloor,p=new NPMap.Geometry.Point(n[0][0],n[0][1]),c=NPMap.T.getPoint(o,p);c=NPMap.T.setPoint(o,c);for(var u,m=r.utils.projectToWorld([c.lon,c.lat,l]),d=[[0,0,m.z]],y=1;y<n.length-1;y++){var _=NPMap.T.getPoint(o,new NPMap.Geometry.Point(n[y][0],n[y][1]));_=NPMap.T.setPoint(o,new NPMap.Geometry.Point(_.lon,_.lat));var f=r.utils.projectToWorld([_.lon,_.lat,l]),g=[f.x-m.x,f.y-m.y,f.z];d.push(g)}var v={meshName:"build-"+s,material:[e.buildTopMaterial,e.buildSideMaterial],floorNum:h,height:l};u=new i(c,d,v),a.push(u)}return a}(e.buildingsArrayGeoJson,e._map,e.tb);for(var r=0;r<e._geoBuilding3Ds.length;r++)e.tb.addAtCoordinate(e._geoBuilding3Ds[r].mesh,[e._geoBuilding3Ds[r].position.lon,e._geoBuilding3Ds[r].position.lat])},e._animation,this._map._obj.getLayer("city_normal_building_id")?this._map._obj.addLayer(this._buildingsLayer,"city_normal_building_id"):this._map._obj.addLayer(this._buildingsLayer)},o.prototype.expandFloors=function(t,i){this.tb.world.getObjectByName("build-"+t)&&this.tb.remove(this.tb.world.getObjectByName("build-"+t)),this._removeBuild_Floor(),this._removeBuild_Floor_Internal(),this._removeBuild_Floor_Sprite(),this.currentBuildIndex=t,this.currentfloorNumIndex=i,this.currentBuild=this._geoBuilding3Ds[this.currentBuildIndex],this.currentBuildHeight=this.currentBuild.points[0][2],this.realCurrentBuildHeight=this.currentBuild.realHeight,this.currentBuildFloor=this.currentBuild.floorNum;this.floorStartHeightArr=[],this.realFloorStartHeightArr=[],this.floorEndHeightArr=[],this.realFloorEndHeightArr=[],this.floorIndoorHeightArr=[],this.realFloorIndoorHeightArr=[],this.floorIndoorLabelHeightArr=[],this.realFloorIndoorLabelHeightArr=[],this.depthReal=this.realCurrentBuildHeight/this.currentBuildFloor,this.depth=this.tb.utils.projectToWorld([0,0,this.currentBuildHeight/this.currentBuildFloor]).z;var o={geometry:new THREE.ExtrudeGeometry(this.currentBuild.shape,{depth:this.currentBuildHeight/this.currentBuildFloor,steps:parseInt(1),bevelEnabled:!1}),material:[this.floorTopMaterial,this.floorSideMaterial]};this.floorMeshArr=[];for(var r=0;r<this.currentBuildFloor;r++){var a=new e("floor-"+r,o);this.floorStartHeightArr[r]=this.currentBuildHeight*r/this.currentBuildFloor,this.realFloorStartHeightArr[r]=this.realCurrentBuildHeight*r/this.currentBuildFloor,this.floorEndHeightArr[r]=this.currentBuildHeight*r/this.currentBuildFloor,this.realFloorEndHeightArr[r]=this.realCurrentBuildHeight*r/this.currentBuildFloor,this.floorIndoorHeightArr[r]=this.floorEndHeightArr[r],this.realFloorIndoorHeightArr[r]=this.realFloorEndHeightArr[r],this.floorIndoorLabelHeightArr[r]=this.floorEndHeightArr[r]+this.currentBuildHeight/this.currentBuildFloor,this.realFloorIndoorLabelHeightArr[r]=this.realFloorEndHeightArr[r]+this.realCurrentBuildHeight/this.currentBuildFloor,this.floorMeshArr.push(a.mesh)}for(r=1;r<this.currentfloorNumIndex+1;r++)this.tb.addAtCoordinate(this.floorMeshArr[r-1],[this.currentBuild.position.lon,this.currentBuild.position.lat]),this.floorMeshArr[r-1].translateZ(this.floorEndHeightArr[r-1]);this.CurrentInitFloorJson=[],this.CurrentInitSpriteJson=[];for(var s=0;s<this.buildingsArrayGeoJson[0].floor.shops.length;s++){var n=[],l=[];for(r=0;r<this.buildingsArrayGeoJson[0].floor.shops[s].length;r++)n.push(this.buildingsArrayGeoJson[0].floor.shops[s][r].geometry.coordinates[0]),l.push(this.buildingsArrayGeoJson[0].floor.shops[s][r].properties.name);this.CurrentInitFloorJson.push(n),this.CurrentInitSpriteJson.push(l)}this.isCollision=!0,this.floorInternalsArrAll=this._initFloorInternals(this.CurrentInitFloorJson,this._map,this.tb),this.spriteInternalsArrAll=this._initSpriteInternals();for(r=0;r<this.floorInternalsArrAll[this.currentfloorNumIndex].length;r++)this.tb.addAtCoordinate(this.floorInternalsArrAll[this.currentfloorNumIndex][r].mesh,[this.floorInternalsArrAll[this.currentfloorNumIndex][r].position.lon,this.floorInternalsArrAll[this.currentfloorNumIndex][r].position.lat]),this.floorInternalsArrAll[this.currentfloorNumIndex][r].mesh.translateZ(this.floorIndoorHeightArr[this.currentfloorNumIndex]);this._updateMarker3D(this),this._map._obj.triggerRepaint()},o.prototype.unexpandFloors=function(t){this._removeBuild_Floor_Internal(),this._removeBuild_Floor_Sprite(),this._removeBuild_Floor(),null!=t?null==this.tb.world.getObjectByName("build-"+t)&&this.tb.addAtCoordinate(this.currentBuild.mesh,[this.currentBuild.position.lon,this.currentBuild.position.lat]):null==this.tb.world.getObjectByName("build-"+this.currentBuildIndex)&&this.tb.addAtCoordinate(this.currentBuild.mesh,[this.currentBuild.position.lon,this.currentBuild.position.lat]),this._map._obj.triggerRepaint()},o.prototype._initFloorInternals=function(e,i,o){function r(t,e,i){return(t[0]*e[1]+e[0]*i[1]+i[0]*t[1]-e[0]*t[1]-i[0]*e[1]-t[0]*i[1])/2}function a(t){for(var e=0,i=0,o=0,a=t[1],s=2;s<t.length;s++)p2=t[s],area=r(t[0],a,p2),o+=area,e+=(t[0][0]+a[0]+p2[0])*area,i+=(t[0][1]+a[1]+p2[1])*area,a=p2;return[e/o/3,i/o/3]}for(var s=[],n=0;n<e.length;n++){for(var l=[],h=e[n],p=null,c=0;c<h.length;c++){var u=new NPMap.Geometry.Point(h[c][0][0],h[c][0][1]),m=a(h[c]);p=NPMap.T.getPoint(i,new NPMap.Geometry.Point(m[0],m[1])),p=NPMap.T.setPoint(i,new NPMap.Geometry.Point(p.lon,p.lat));var d=NPMap.T.getPoint(i,u);d=NPMap.T.setPoint(i,d);for(var y=o.utils.projectToWorld([d.lon,d.lat,3]),_=[[0,0,y.z]],f=1;f<h[c].length;f++){var g=NPMap.T.getPoint(i,new NPMap.Geometry.Point(h[c][f][0],h[c][f][1]));g=NPMap.T.setPoint(i,new NPMap.Geometry.Point(g.lon,g.lat));var v=o.utils.projectToWorld([g.lon,g.lat,3]),M=[v.x-y.x,v.y-y.y,v.z];_.push(M)}var P,b={meshName:"floor"+n+"-"+c,centerPoint:p,material:[this.buildInternalTopMaterial,this.buildInternalSideMaterial],floorIndoorHeight:this.floorIndoorHeightArr[c],floorIndoorLabelHeight:this.floorIndoorLabelHeightArr[c]};P=new t(d,_,b),l.push(P)}s.push(l)}return s},o.prototype._initSpriteInternals=function(){for(var t=[],e=0;e<this.floorInternalsArrAll.length;e++){for(var i=[],o=0;o<this.floorInternalsArrAll[e].length;o++){var r=this._makeTextPoint(this.CurrentInitSpriteJson[e][o],{});r.name="sprite"+e+"-"+o,r.floorIndoorHeight=this.floorIndoorHeightArr[e],r.floorIndoorLabelHeight=this.floorIndoorLabelHeightArr[e],i.push(r)}t.push(i)}return t},o.prototype._makeTextSprite=function(t,e){void 0===e&&(e={});e.hasOwnProperty("fontface")&&e.fontface,e.hasOwnProperty("fontsize")&&e.fontsize;var i=e.hasOwnProperty("borderThickness")?e.borderThickness:4,o=e.hasOwnProperty("borderColor")?e.borderColor:{r:0,g:0,b:0,a:1},r=e.hasOwnProperty("backgroundColor")?e.backgroundColor:{r:255,g:255,b:255,a:.5},a=(h=document.createElement("canvas")).getContext("2d");a.font="22px bold";a.measureText(t).width;a.fillStyle="rgba("+r.r+","+r.g+","+r.b+","+r.a+")",a.strokeStyle="rgba("+o.r+","+o.g+","+o.b+","+o.a+")",a.lineWidth=i,a.fillText(t,150,40,100);var s=new THREE.Texture(h);s.needsUpdate=!0;var n=new THREE.SpriteMaterial({map:s}),l=new THREE.Sprite(n),h=document.createElement("canvas");return l.scale.set(.4,.2,1),l},o.prototype._makeTextPoint=function(t){var e=document.createElement("canvas");e.width=200,e.height=200,e.style.width="100px",e.style.height="100px";var i=e.getContext("2d");i.font='24px " 微软雅黑';var o=i.measureText(t).width;o<=200?(i.lineWidth=6,i.strokeStyle="white",i.strokeText(t,100-o/2,100,200),i.fillStyle="black",i.fillText(t,100-o/2,100,200)):o>200&&(i.lineWidth=6,i.strokeStyle="white",i.strokeText(t,0,100,200),i.fillStyle="black",i.fillText(t,0,100,200));var r=new THREE.Texture(e);r.needsUpdate=!0;(new THREE.TextureLoader).load("../../css/images/camera_active.png");var a=new THREE.PointsMaterial({size:100,sizeAttenuation:!1,map:r,alphaTest:.3,transparent:!0}),s=new THREE.BufferGeometry,n=[];return n.push(0,0,0),s.setAttribute("position",new THREE.Float32BufferAttribute(n,3)),new THREE.Points(s,a)},o.prototype._caluPOIRect=function(t,e,i){var o=t.lon,r=t.lat,a=this._map._obj.project(new mapboxgl.LngLat(o,r),e);return{x:a.x-50,y:a.y-14,w:100,h:14}},o.prototype._isPOIRect=function(t,e){var i=t.x,o=t.y,r=t.w,a=t.h,s=e.x,n=e.y,l=e.w,h=e.h;return!(i>=s&&i>=s+l)&&(!(i<=s&&i+r<=s)&&(!(o>=n&&o>=n+h)&&!(o<=n&&o+a<=n)))},o.prototype._removeBuild=function(t){this.tb.world.getObjectByName("build-"+t)&&this.tb.remove(this.tb.world.getObjectByName("build-"+t))},o.prototype._removeBuild_Floor=function(){if(null!=this.floorMeshArr)for(var t=0;t<this.floorMeshArr.length;t++)this.tb.world.getObjectByName("floor-"+t)&&this.tb.remove(this.floorMeshArr[t])},o.prototype._removeBuild_Floor_Sprite=function(){if(this.floorInternalsArrAll){this.isCollision=!1;for(var t=0;t<this.floorInternalsArrAll.length;t++)for(var e=0;e<this.floorInternalsArrAll[t].length;e++)this.tb.world.getObjectByName("sprite"+t+"-"+e)&&this.tb.remove(this.tb.world.getObjectByName("sprite"+t+"-"+e))}},o.prototype._removeBuild_Floor_Internal=function(){if(this.floorInternalsArrAll)for(var t=0;t<this.floorInternalsArrAll.length;t++)for(var e=0;e<this.floorInternalsArrAll[t].length;e++)this.tb.world.getObjectByName("floor"+t+"-"+e)&&this.tb.remove(this.tb.world.getObjectByName("floor"+t+"-"+e))},o.prototype._updateMarker3D=function(t){var e=t;if(e.spriteInternalsArrAll){for(var i=0;i<e.notCollisionMeshs.length;i++){var o=[],r=[],a=[];e.notCollisionMeshs[i].forEach((function(t){var s=e._caluPOIRect(t.centerPoint,e.realFloorIndoorLabelHeightArr[i]);for(var n in o){var l=o[n];if(e._isPOIRect(l,s))return}o.push(s),r.push(parseInt(t.mesh.name.split("-")[1])),a.push(t)})),e.notCollisionBounds[i]=o,e.notCollisionIndexs[i]=r,e.notCollisionMeshs[i]=a}if(0==e.notCollisionMeshs.length)for(i=0;i<e.floorInternalsArrAll.length;i++){o=[],r=[],a=[];e.floorInternalsArrAll[i].forEach((function(t){var s=e._caluPOIRect(t.centerPoint,e.realFloorIndoorLabelHeightArr[i]);for(var n in o){var l=o[n];if(e._isPOIRect(l,s))return}o.push(s),r.push(parseInt(t.mesh.name.split("-")[1])),a.push(t)})),e.notCollisionBounds.push(o),e.notCollisionIndexs.push(r),e.notCollisionMeshs.push(a)}for(i=0;i<e.floorInternalsArrAll.length;i++)e.floorInternalsArrAll[i].forEach((function(t){var o=e._caluPOIRect(t.centerPoint,e.realFloorIndoorLabelHeightArr[i]);for(var r in e.notCollisionBounds[i]){var a=e.notCollisionBounds[i][r];if(parseInt(e.notCollisionMeshs[i][r].mesh.name.split("-")[1])==parseInt(t.mesh.name.split("-")[1]))return;if(e._isPOIRect(a,o))return}e.notCollisionBounds[i].push(o),e.notCollisionMeshs[i].push(t),e.notCollisionIndexs[i].push(parseInt(t.mesh.name.split("-")[1]))}));for(var s=0;s<e.floorInternalsArrAll[e.currentfloorNumIndex].length;s++)-1==e.notCollisionIndexs[e.currentfloorNumIndex].indexOf(s)&&e.tb.world.getObjectByName("sprite"+e.currentfloorNumIndex+"-"+s)&&e.tb.remove(e.tb.world.getObjectByName("sprite"+e.currentfloorNumIndex+"-"+s)),-1==e.notCollisionIndexs[e.currentfloorNumIndex].indexOf(s)||e.tb.world.getObjectByName("sprite"+e.currentfloorNumIndex+"-"+s)||(e.tb.addAtCoordinate(e.spriteInternalsArrAll[e.currentfloorNumIndex][s],[e.floorInternalsArrAll[e.currentfloorNumIndex][s].centerPoint.lon,e.floorInternalsArrAll[e.currentfloorNumIndex][s].centerPoint.lat]),e.spriteInternalsArrAll[e.currentfloorNumIndex][s].translateZ(e.floorIndoorLabelHeightArr[e.currentfloorNumIndex]))}},o.prototype._update=function(){this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),100)},o.prototype.destory=function(){if(this.timer&&clearTimeout(this.timer),this._map&&this._map._obj.getLayer(this._layerID)){this._map._obj.removeLayer(this._layerID);for(var t=0;t<this._geoBuilding3Ds.length;t++)this.tb.remove(this._geoBuilding3Ds[t].mesh),this._geoBuilding3Ds[t].geometry=null,this._geoBuilding3Ds[t].mesh=null,this._geoBuilding3Ds[t].shape=null;this._geoBuilding3Ds.length=0;for(t=0;t<this.floorMeshArr.length;t++)this.tb.remove(this.floorMeshArr[t]),this.floorMeshArr[t]=null;this.floorMeshArr.length=0;for(t=0;t<this.floorInternalsArrAll.length;t++)this.tb.remove(this.floorInternalsArrAll[t]),this.floorInternalsArrAll[t].mesh=null,this.floorInternalsArrAll[t].geometry=null,this.floorInternalsArrAll[t].shape=null,this.floorInternalsArrAll[t]=null;this.floorInternalsArrAll.length=0;for(t=0;t<this.spriteInternalsArrAll.length;t++)this.spriteInternalsArrAll[t]=null;this.spriteInternalsArrAll.length=0,this.tb=null}}}(),function(){var t=function(t,e,i,o){this.position=t,this.points=e,this.count=e.length,this.geometry=new THREE.Geometry,this.colors=[];for(var r=0;r<e.length;r++)this.geometry.vertices.push(e[r]),this.geometry.colors.push(new THREE.Color(o[0],o[1],o[2])),this.colors.push(o[0],o[1],o[2]);this.line=new THREE.Line(this.geometry,i)},e=MapPlatForm.Base.MapGeoWallLine3D=function(t,e,i){this._map=t,this._springLines=[],this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=i&&i.minZoom?i.minZoom:11,this._lineCount=i&&i.lineCount?i.lineCount:10,this._animation=i&&i.animation,this.tb=e,this.timer=null,this._moveCount=Math.floor(this._lineHeight/this._speed)};e.prototype.addLines=function(e){var i=new THREE.LineBasicMaterial({color:16777215,vertexColors:!0,linewidth:5});this._geoLine3Ds=[],this.lines=e;this._geoLine3Ds=function(e,o,r,a){for(var s=[],n=0;n<e.length;n++){for(var l={index:0,count:a,lines:[],colors:[]},h=e[n].getPath(),p=e[n].getData(),c=(p=p||{}).colors?p.colors:["#6ff3f3","#f7e513","#a4ec62","#77e8b4","#81d1e8","#c48eec","#c48eec"],u=0;u<a;u++){for(var m=p.height?p.height:20,d=NPMap.T.setPoint(o,h[0]),y=r.utils.projectToWorld([d.lon,d.lat,(u+1)*(m/a)]),_=[new THREE.Vector3(0,0,y.z)],f=1;f<h.length;f++){var g=NPMap.T.setPoint(o,h[f]),v=r.utils.projectToWorld([g.lon,g.lat,(u+1)*(m/a)]),M=new THREE.Vector3(v.x-y.x,v.y-y.y,v.z);_.push(M)}var P=NPMap.Utils.MapUtil.colorRgb(c[u%c.length]),b=new t(d,_,i,[P[0]/256,P[1]/256,P[2]/256]);p.isBloom&&b.line.layers.set(1),l.lines.push(b),l.colors.push(b.geometry.colors)}s.push(l)}return s}(this.lines,this._map,this.tb,this._lineCount);for(var o=0;o<this._geoLine3Ds.length;o++)for(var r=0;r<this._geoLine3Ds[o].lines.length;r++)this.tb.addAtCoordinate(this._geoLine3Ds[o].lines[r].line,[this._geoLine3Ds[o].lines[r].position.lon,this._geoLine3Ds[o].lines[r].position.lat]);this._animation&&this._update()},e.prototype._update=function(){for(var t=0;t<this._geoLine3Ds.length;t++){this._geoLine3Ds[t].index<0&&(this._geoLine3Ds[t].index=this._geoLine3Ds[t].count-1);for(var e=0;e<this._lineCount;e++){var i=this._geoLine3Ds[t].colors[(this._geoLine3Ds[t].index+e)%this._lineCount];if(this._geoLine3Ds[t].lines[e].line.geometry._bufferGeometry){i=this._geoLine3Ds[t].lines[(this._geoLine3Ds[t].index+e)%this._lineCount].colors;this._geoLine3Ds[t].lines[e].line.geometry._bufferGeometry.setAttribute("color",new THREE.Float32BufferAttribute(i,3))}else this._geoLine3Ds[t].lines[e].line.geometry.colors=i}this._geoLine3Ds[t].index--}this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),200)},e.prototype.destory=function(){this.timer&&clearTimeout(this.timer);for(var t=0;t<this._geoLine3Ds.length;t++)for(var e=0;e<this._geoLine3Ds[t].lines.length;e++)this.tb.remove(this._geoLine3Ds[t].lines[e].line);this._geoLine3Ds.length=0}}(),MapPlatForm.Base.ClusterManager=function(t,e){this.CLASS_NAME="ClusterManager",this.map=t,this._layers={},this.opts=e},MapPlatForm.Base.ClusterManager.prototype.addClusters=function(t){var e=this.opts.clusterImage;for(var i in t)if(this.map.getLayerByName(i))(a=this.map.getLayerByName(i)).useCluster&&a.addClusters(t[i]);else{for(var o=[],r=0;r<e.length;r++)o.push(r%2==0?i+e[r]:e[r]);this.opts.clusterImage=o;var a=new NPMap.Layers.OverlayLayer(i,!0,this.opts),s=new NPMap.Symbols.ClusterPoints(t[i]);this.map.addLayer(a),a.addOverlay(s),this._layers[i]=a}},MapPlatForm.Base.ClusterManager.prototype.addClusterPoints=function(t,e){this.map.getLayerByName(t)&&this.map.getLayerByName(t).addClusterPoints(e)},MapPlatForm.Base.ClusterManager.prototype.setMakrerTypeVisiable=function(t,e,i){this.map.getLayerByName(t)&&this.map.getLayerByName(t).setMakrerTypeVisiable(e,i)},MapPlatForm.Base.ClusterManager.prototype.hide=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].hide()},MapPlatForm.Base.ClusterManager.prototype.show=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].show()},MapPlatForm.Base.ClusterManager.prototype.refresh=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].refresh()},MapPlatForm.Base.ClusterManager.prototype.getVisibleClustersByExtent=function(t,e){return this._layers[t]&&this._layers[t].getVisible()?this._layers[t].containFeatures(e,(function(t){return t.location._visible})):[]},MapPlatForm.Base.ClusterManager.prototype.getVisibleByKey=function(t){return!(!this._layers[t]||!this._layers[t].getVisible())},function(){var t=function(t,e,i){this._map=t,this._tb=e,this.id=i.id,this.el=i.element,this.marker=new mapboxgl.Marker(this.el,{anchor:"bottom"}),this.position=i.position,this.height=i&&i.height?i.height:0,this.baseLineHeight=i&&i.baseLineHeight?i.baseLineHeight:0,this.lineColor=i&&i.lineColor?i.lineColor:"white",this.lineEnableTexture=!(!i||null==i.lineEnableTexture)&&i.lineEnableTexture,this.lineTextureUrl=i&&i.lineTextureUrl?i.lineTextureUrl:"",this.lineTextureWidth=i&&i.lineTextureWidth?i.lineTextureWidth:2,this.isBloom=null!=i.isBloom&&i.isBloom;var o=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.baseLineHeight]),r=o.z,a=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.height]),s=a.z;if(this.lineEnableTexture&&""!=this.lineTextureUrl){var n=(new THREE.TextureLoader).load(this.lineTextureUrl),l=new THREE.MeshBasicMaterial({map:n,side:THREE.DoubleSide,blending:THREE.AdditiveBlending,transparent:!0}),h=new THREE.BufferGeometry,p=new Float32Array(36),c=.01*this.lineTextureWidth;p.set([-1*c,0,r,c,0,r,c,0,s,c,0,s,-1*c,0,s,-1*c,0,r,0,-1*c,r,0,c,r,0,c,s,0,c,s,0,-1*c,s,0,-1*c,r]),parseInt(THREE.REVISION)>=110?h.setAttribute("position",new THREE.BufferAttribute(p,3)):h.addAttribute("position",new THREE.BufferAttribute(p,3));var u=new Float32Array(36);u.set([0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0]),parseInt(THREE.REVISION)>=110?h.setAttribute("normal",new THREE.BufferAttribute(u,3)):h.addAttribute("normal",new THREE.BufferAttribute(u,3));var m=new Float32Array(24);m.set([0,0,1,0,1,1,1,1,0,1,0,0,0,0,1,0,1,1,1,1,0,1,0,0]),parseInt(THREE.REVISION)>=110?h.setAttribute("uv",new THREE.BufferAttribute(m,2)):h.addAttribute("uv",new THREE.BufferAttribute(m,2)),this.line=new THREE.Mesh(h,l),this.line.name="MapMarker3DLine"+this.id}else{l=new THREE.LineBasicMaterial({color:this.lineColor,linecap:"round",linejoin:"round"});var d=new THREE.Geometry,y=new THREE.Vector3(0,0,o.z),_=new THREE.Vector3(0,0,a.z);d.vertices.push(y,_),this.line=new THREE.Line(d,l),this.line.name="MapMarker3DLine"+this.id}},e=MapPlatForm.Base.MapMarker3DLayer=function(t,e,i){this._map=t,this._tb=e,this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.lineVisiable=!(!i||null==i.lineVisiable)&&i.lineVisiable,this.projectChange=!i||null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this.marker3DsDataArray=[]};e.prototype.addMarker3Ds=function(e){for(var i=this,o=0;o<e.length;o++)this.marker3DsDataArray.push(e[o]);this._mapMarker3Ds=[];i._mapMarker3Ds=function(e,o,r){for(var a=[],s=0;s<e.length;s++){var n={id:NPMap.Utils.BaseUtils.uuid(),element:e[s].dom,position:e[s].point,height:e[s].height,baseLineHeight:e[s].baseLineHeight,lineColor:e[s].lineColor,lineEnableTexture:e[s].lineEnableTexture,lineTextureUrl:e[s].lineTextureUrl,lineTextureWidth:e[s].lineTextureWidth,isBloom:null!=e[s].isBloom&&e[s].isBloom},l=new t(i._map,i._tb,n);l.data=e[s].data?e[s].data:{},a.push(l)}return a}(i.marker3DsDataArray,i._map,i._tb);for(o=0;o<i._mapMarker3Ds.length;o++){var r=null;r=i.projectChange?NPMap.T.setPoint(i._map,new NPMap.Geometry.Point(i._mapMarker3Ds[o].position.lon,i._mapMarker3Ds[o].position.lat)):i._mapMarker3Ds[o].position,i.lineVisiable&&(i._mapMarker3Ds[o].isBloom?i._mapMarker3Ds[o].line.layers.set(1):i._mapMarker3Ds[o].line.layers.set(0),i._tb.addAtCoordinate(i._mapMarker3Ds[o].line,[r.lon,r.lat])),i._mapMarker3Ds[o].marker.setLngLat([r.lon,r.lat],i._mapMarker3Ds[o].height),i._mapMarker3Ds[o].marker.addTo(i._map._obj),function(t){var e=i._mapMarker3Ds[t];e.el.onclick=function(t){i._events.triggerEvt("click",e),t.stopPropagation()},e.el.onmousemove=function(){i._events.triggerEvt("mousemove",e)},e.el.onmouseover=function(){i._events.triggerEvt("mouseover",e)},e.el.onmouseout=function(){i._events.triggerEvt("mouseout",e)}}(o)}},e.prototype.removeMarker3Ds=function(){for(var t=0;t<this._mapMarker3Ds.length;t++){this._mapMarker3Ds[t].marker.remove();var e=this._tb.world.getObjectByName("MapMarker3DLine"+this._mapMarker3Ds[t].id);e&&this._tb.remove(e)}},e.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},e.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},e.prototype.destory=function(){this._events=null;for(var t=0;t<this._mapMarker3Ds.length;t++){this._mapMarker3Ds[t].marker.remove(),this._mapMarker3Ds[t].marker=null;var e=this._tb.world.getObjectByName("MapMarker3DLine"+this._mapMarker3Ds[t].id);e&&this._tb.remove(e),this._mapMarker3Ds[t].line=null,this._mapMarker3Ds[t]=null}this.marker3DsDataArray.length=0,this._mapMarker3Ds.length=0}}(),function(){var t=function(t,e,i){this._map=t,this._tb=e,this.id=i.id,this.position=i.position,this.height=i&&i.height?i.height:0,this.showType=i&&i.showType?i.showType:"text",this.imgUrl=i&&i.imgUrl?i.imgUrl:"",this.imgSize=i&&i.imgSize?i.imgSize:24,this.message=i&&i.message?i.message:"",this.baseLineHeight=i.baseLineHeight?i.baseLineHeight:0,this.lineColor=i&&i.lineColor?i.lineColor:"white",this.lineEnableTexture=null!=i.lineEnableTexture&&i.lineEnableTexture,this.lineTextureUrl=i&&i.lineTextureUrl?i.lineTextureUrl:"",this.lineTextureWidth=i&&i.lineTextureWidth?i.lineTextureWidth:2,this.isBloom=null!=i.isBloom&&i.isBloom,this.bounds=i.bounds;var o=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.baseLineHeight]),r=o.z,a=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.height]),s=a.z;if("img"==this.showType&&(this.imgTexture=(new THREE.TextureLoader).load(this.imgUrl),this.material=new THREE.PointsMaterial({size:this.imgSize,sizeAttenuation:!1,map:this.imgTexture,alphaTest:.3,transparent:!0})),"text"==this.showType){var n=document.createElement("canvas");n.width=200,n.height=200,n.style.width="200px",n.style.height="200px";var l=n.getContext("2d");l.font='24px " 微软雅黑';var h=l.measureText(this.message).width;h<=200?(l.lineWidth=6,l.strokeStyle="white",l.strokeText(this.message,100-h/2,100,200),l.fillStyle="black",l.fillText(this.message,100-h/2,100,200)):h>200&&(l.lineWidth=6,l.strokeStyle="white",l.strokeText(message,0,100,200),l.fillStyle="black",l.fillText(message,0,100,200)),this.textTexture=new THREE.Texture(n),this.textTexture.needsUpdate=!0,this.material=new THREE.PointsMaterial({size:100,sizeAttenuation:!1,map:this.textTexture,alphaTest:.3,transparent:!0})}if(this.geometry=new THREE.BufferGeometry,this.geometry.setAttribute("position",new THREE.Float32BufferAttribute([0,0,0],3)),this.pointMesh=new THREE.Points(this.geometry,this.material),this.pointMesh.name="markerReal3D"+this.id,this.pointMesh.eventID=i.id,this.lineEnableTexture&&""!=this.lineTextureUrl){var p=(new THREE.TextureLoader).load(this.lineTextureUrl),c=new THREE.MeshBasicMaterial({map:p,side:THREE.DoubleSide,blending:THREE.AdditiveBlending,transparent:!0}),u=new THREE.BufferGeometry,m=new Float32Array(36),d=.01*this.lineTextureWidth;m.set([-1*d,0,r,d,0,r,d,0,s,d,0,s,-1*d,0,s,-1*d,0,r,0,-1*d,r,0,d,r,0,d,s,0,d,s,0,-1*d,s,0,-1*d,r]),parseInt(THREE.REVISION)>=110?u.setAttribute("position",new THREE.BufferAttribute(m,3)):u.addAttribute("position",new THREE.BufferAttribute(m,3));var y=new Float32Array(36);y.set([0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0]),parseInt(THREE.REVISION)>=110?u.setAttribute("normal",new THREE.BufferAttribute(y,3)):u.addAttribute("normal",new THREE.BufferAttribute(y,3));var _=new Float32Array(24);_.set([0,0,1,0,1,1,1,1,0,1,0,0,0,0,1,0,1,1,1,1,0,1,0,0]),parseInt(THREE.REVISION)>=110?u.setAttribute("uv",new THREE.BufferAttribute(_,2)):u.addAttribute("uv",new THREE.BufferAttribute(_,2)),this.line=new THREE.Mesh(u,c),this.line.name="markerLineReal3D"+this.id}else{var f=new THREE.LineBasicMaterial({color:this.lineColor,linecap:"round",linejoin:"round"}),g=new THREE.Geometry,v=(g=new THREE.Geometry,new THREE.Vector3(0,0,o.z)),M=new THREE.Vector3(0,0,a.z);g.vertices.push(v,M),this.line=new THREE.Line(g,f),this.line.name="markerLineReal3D"+this.id}},e=MapPlatForm.Base.MapMarkerReal3DLayer=function(t,e,i){this._map=t,this._tb=e;this._mapRealMarker3Ds=[],this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.isCollision=!(!i||null==i.isCollision)&&i.isCollision,this.lineVisiable=!(!i||null==i.lineVisiable)&&i.lineVisiable,this.projectChange=!i||null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"])};e.prototype.addMarker3Ds=function(e){var i=this;this._mapRealMarker3Ds=[];for(var o in this._mapRealMarker3Ds=function(e,o,r){for(var a=[],s=0;s<e.length;s++){var n={point:e[s].point,height:e[s].height?e[s].height:0,message:e[s].message,showType:e[s].showType,imgSize:e[s].imgSize},l=i._caluPOIRect(n),h={id:NPMap.Utils.BaseUtils.uuid(),position:e[s].point,height:e[s].height?e[s].height:0,showType:e[s].showType,imgUrl:e[s].imgUrl,imgSize:e[s].imgSize,message:e[s].message,baseLineHeight:e[s].baseLineHeight?e[s].baseLineHeight:0,lineColor:e[s].lineColor,lineEnableTexture:e[s].lineEnableTexture,lineTextureUrl:e[s].lineTextureUrl,lineTextureWidth:e[s].lineTextureWidth,isBloom:null!=e[s].isBloom&&e[s].isBloom,bounds:l};a[e[s].id]=new t(i._map,i._tb,h),a[e[s].id].data=e[s].data?e[s].data:{}}return a}(e,i._map,i._tb),i._mapRealMarker3Ds){var r=null;if(r=i.projectChange?NPMap.T.setPoint(i._map,i._mapRealMarker3Ds[o].position):i._mapRealMarker3Ds[o].position,i._mapRealMarker3Ds[o].isBloom?i._mapRealMarker3Ds[o].pointMesh.layers.set(1):i._mapRealMarker3Ds[o].pointMesh.layers.set(0),i._tb.addAtCoordinate(i._mapRealMarker3Ds[o].pointMesh,[r.lon,r.lat]),i._mapRealMarker3Ds[o].height>0){var a=i._tb.utils.projectToWorld([i._mapRealMarker3Ds[o].position.lon,i._mapRealMarker3Ds[o].position.lat,i._mapRealMarker3Ds[o].height]).z;i._mapRealMarker3Ds[o].pointMesh.translateZ(a)}i.lineVisiable&&(i._mapRealMarker3Ds[o].isBloom?i._mapRealMarker3Ds[o].line.layers.set(1):i._mapRealMarker3Ds[o].line.layers.set(0),i._tb.addAtCoordinate(i._mapRealMarker3Ds[o].line,[r.lon,r.lat]))}i._map._obj.on("mousemove",(function(t){var e=t.point;for(var o in i._mapRealMarker3Ds){var r=i._mapRealMarker3Ds[o],a={point:r.position,height:r.height,message:r.message,showType:r.showType,imgSize:r.imgSize},s=i._caluPOIRect(a);i._mapRealMarker3Ds[o].bounds=s,i._pointIsPOIRect(e,i._mapRealMarker3Ds[o].bounds)&&(i._events.triggerEvt("mousemove",i._mapRealMarker3Ds[o]),i._map._obj.getCanvas().style.cursor="pointer")}})),i._map._obj.on("click",(function(t){var e=t.point;for(var o in i._mapRealMarker3Ds)i._pointIsPOIRect(e,i._mapRealMarker3Ds[o].bounds)&&i._events.triggerEvt("click",i._mapRealMarker3Ds[o])})),i._map._obj.on("mouseover",(function(t){var e=t.point;for(var o in i._mapRealMarker3Ds)i._pointIsPOIRect(e,i._mapRealMarker3Ds[o].bounds)&&(i._events.triggerEvt("mouseover",i._mapRealMarker3Ds[o]),i._map._obj.getCanvas().style.cursor="pointer")})),i._map._obj.on("mouseout",(function(t){var e=t.point;for(var o in i._mapRealMarker3Ds)i._pointIsPOIRect(e,i._mapRealMarker3Ds[o].bounds)&&(i._events.triggerEvt("mouseout",i._mapRealMarker3Ds[o]),i._map._obj.getCanvas().style.cursor="pointer")}))},e.prototype.removeMarker3Ds=function(){for(var t=0;t<this._mapRealMarker3Ds.length;t++){var e=this._tb.world.getObjectByName("markerReal3D"+this._mapRealMarker3Ds[t].id);e&&this._tb.remove(e);var i=this._tb.world.getObjectByName("markerLineReal3D"+this._mapRealMarker3Ds[t].id);i&&this._tb.remove(i)}},e.prototype._caluPOIRect=function(t){var e=t.point;e=NPMap.T.setPoint(this._map,e);var i=t.height,o=t&&t.message?t.message:"",r=t.showType,a=t.imgSize,s=e.lon,n=e.lat,l=this._map._obj.project(new mapboxgl.LngLat(s,n),i);if("text"==r){var h=document.createElement("canvas");h.width=200,h.height=200,h.style.width="200px",h.style.height="200px";var p=h.getContext("2d");p.font='24px " 微软雅黑';var c=p.measureText(o).width;if(c<=200)var u=l.x-c/2/2,m=l.y-14,d=c/2,y=14;else if(c>200)u=l.x-50,m=l.y-14,d=100,y=14;return{x:u,y:m,w:d,h:y}}if("img"==r)return{x:l.x-a/2,y:l.y-a/2,w:a,h:a}},e.prototype._isPOIRect=function(t,e){var i=t.x,o=t.y,r=t.w,a=t.h,s=e.x,n=e.y,l=e.w,h=e.h;return!(i>=s&&i>=s+l)&&(!(i<=s&&i+r<=s)&&(!(o>=n&&o>=n+h)&&!(o<=n&&o+a<=n)))},e.prototype._pointIsPOIRect=function(t,e){var i=e.x,o=e.y,r=e.w,a=e.h,s=t.x,n=t.y;return s>i&&s<i+r&&n>o&&n<o+a},e.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},e.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},e.prototype.destory=function(){this._events=null;for(var t=0;t<this._mapRealMarker3Ds.length;t++){var e=this._tb.world.getObjectByName("markerReal3D"+this._mapRealMarker3Ds[t].id);e&&this._tb.remove(e),this._mapRealMarker3Ds[t].pointMesh=null;var i=this._tb.world.getObjectByName("markerLineReal3D"+this._mapRealMarker3Ds[t].id);i&&this._tb.remove(i),this._mapRealMarker3Ds[t].line=null,this._mapRealMarker3Ds[t]=null}this._mapRealMarker3Ds.length=0}}(),function(){var t=MapPlatForm.Base.AnimationRadar=function(t,e,i){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=i&&i.minZoom?i.minZoom:11,this._lineWidth=i&&i.lineWidth?i.lineWidth:5,this._lineColor=i&&i.lineColor?i.lineColor:"#FF0000",this._angleColor=i&&i.angleColor?i.angleColor:"#FF0000",this._animation=i&&i.animation,this.tb=e,this.timer=null,this.depthTest=!1,this._initMaterial()};t.prototype._initMaterial=function(){this.lineMaterial=new THREE.LineMaterial({color:this._lineColor,linewidth:5e-4*this._lineWidth,dashed:!1,transparent:!0,opacity:1,fog:!0,depthTest:this.depthTest,wireframe:!1}),this.dlineMaterial=new THREE.LineMaterial({color:this._lineColor,dashSize:1,gapSize:1,dashScale:2,scale:2,linewidth:.0015,opacity:1,depthTest:this.depthTest,dashed:!0}),this.dlineMaterial.defines.USE_DASH="",this.dlineMaterial1=new THREE.LineMaterial({color:this._lineColor,depthTest:this.depthTest,linewidth:.001,opacity:1}),this.matLineBasic=new THREE.LineBasicMaterial({color:this._lineColor,opacity:.2,transparent:!0,depthTest:this.depthTest}),this.rmaterial=new THREE.ShaderMaterial({uniforms:{color:{value:new THREE.Color(16777215)},fogColor:{value:this.tb.scene.fog.color},fogNear:{value:this.tb.scene.fog.near},fogFar:{value:this.tb.scene.fog.far}},depthTest:this.depthTest,fog:!0,vertexShader:"#include <fog_pars_vertex>\nattribute float cusmtomOpacity;attribute vec3 customColor;varying vec3 vColor;varying float vOpacity;void main() {vColor = customColor;vOpacity = cusmtomOpacity;vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );gl_Position = projectionMatrix * mvPosition;\n\t#include <fog_vertex>\n\t}",fragmentShader:"#include <fog_pars_fragment>\nuniform vec3 color;varying vec3 vColor;varying float vOpacity;void main() {gl_FragColor = vec4(color * vColor,vOpacity);\n\t#include <fog_fragment>\n\t}",transparent:!0}),this.circleMaterial=this.rmaterial.clone(),this.materialCuXu=new THREE.LineMaterial({color:this._lineColor,linewidth:.005,depthTest:this.depthTest,opacity:1})},t.prototype.add=function(t,e){var i=this;this.obj=function(t,e,o,r,a,s){for(var n=NPMap.Utils.MapUtil.colorRgb(a),l={},h=NPMap.T.setPoint(o,t),p=r.utils.projectToWorld([h.lon,h.lat,0]),c=NPMap.T.helper.webMoctorJW2PM(h.lon,h.lat),u=NPMap.Utils.MapUtil.createRegularPolygon(c,e,60),m=[],d=[],y=[],_=0;_<u.length-1;_++){var f=NPMap.T.helper.inverseMercator(u[_].lon,u[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);m.push(p.x-p.x,p.y-p.y,0),d.push(n[0]/256,n[1]/256,n[2]/256),y.push(0),m.push(g.x-p.x,g.y-p.y,0),d.push(n[0]/256,n[1]/256,n[2]/256),y.push(.2),f=NPMap.T.helper.inverseMercator(u[_+1].lon,u[_+1].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]),m.push(g.x-p.x,g.y-p.y,0),d.push(n[0]/256,n[1]/256,n[2]/256),y.push(.2)}var v=new THREE.BufferGeometry,M=new Float32Array(m.length);M.set(m),v.setAttribute("position",new THREE.BufferAttribute(M,3));var P=new Float32Array(d.length);P.set(d),v.setAttribute("customColor",new THREE.BufferAttribute(P,3));var b=new Float32Array(y.length);b.set(y),v.setAttribute("cusmtomOpacity",new THREE.BufferAttribute(b,1));var x=new THREE.Mesh(v,i.circleMaterial);r.addAtCoordinate(x,[h.lon,h.lat]),l.circle=x;var w=[];for(_=0;_<u.length;_++){f=NPMap.T.helper.inverseMercator(u[_].lon,u[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);w.push(g.x-p.x,g.y-p.y,g.z)}var S=new THREE.LineGeometry;S.setPositions(w);var L=new THREE.Line2(S,i.lineMaterial);r.addAtCoordinate(L,[h.lon,h.lat]),l.outLine=L;var C=[];for(_=0;_<u.length;_+=5){f=NPMap.T.helper.inverseMercator(u[_].lon,u[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);C.push(0,0,0),C.push(g.x-p.x,g.y-p.y,g.z)}var B=new THREE.BufferGeometry;B.setAttribute("position",new THREE.Float32BufferAttribute(C,3)),B.computeBoundingSphere();var I=new THREE.Line(B,i.matLineBasic);r.addAtCoordinate(I,[h.lon,h.lat]),l.sline=I;var k=NPMap.Utils.MapUtil.createRegularPolygon(c,.85*e,60),T=[];for(_=0;_<k.length;_++){f=NPMap.T.helper.inverseMercator(k[_].lon,k[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);T.push(g.x-p.x,g.y-p.y,g.z)}var R=new THREE.LineGeometry;R.setPositions(T);var E=new THREE.Line2(R,i.dlineMaterial);E.computeLineDistances(),r.addAtCoordinate(E,[h.lon,h.lat]),l.dline=E,l.lines=[];for(var F=0;F<3;F++){var A=NPMap.Utils.MapUtil.createRegularPolygon(c,e*(.7-.15*F),60),D=[];for(_=0;_<A.length;_++){f=NPMap.T.helper.inverseMercator(A[_].lon,A[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);D.push(g.x-p.x,g.y-p.y,g.z)}if(0==F){l.lines2=[];for(var N=0;N<3;N++){var O=[];for(_=20*N;_<20*N+8;_++){f=NPMap.T.helper.inverseMercator(A[_].lon,A[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);O.push(g.x-p.x,g.y-p.y,g.z)}var j=new THREE.LineGeometry;j.setPositions(O);var G=new THREE.Line2(j,i.materialCuXu);r.addAtCoordinate(G,[h.lon,h.lat]),l.lines2.push(G)}}var H=new THREE.LineGeometry;H.setPositions(D);var U=new THREE.Line2(H,i.dlineMaterial1);U.computeLineDistances(),r.addAtCoordinate(U,[h.lon,h.lat]),l.lines.push(U)}var z=NPMap.Utils.MapUtil.createCircleSector(c,e,15,90,0,!1),q=[],W=[],V=[],J=new THREE.BufferGeometry;for(_=0;_<z.length-1;_++){f=NPMap.T.helper.inverseMercator(z[_].lon,z[_].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]);q.push(p.x-p.x,p.y-p.y,0),W.push(n[0]/256,n[1]/256,n[2]/256),V.push(0),q.push(g.x-p.x,g.y-p.y,0),W.push(n[0]/256,n[1]/256,n[2]/256),V.push(.9/z.length*_),f=NPMap.T.helper.inverseMercator(z[_+1].lon,z[_+1].lat),g=r.utils.projectToWorld([f.lon,f.lat,0]),q.push(g.x-p.x,g.y-p.y,0),W.push(n[0]/256,n[1]/256,n[2]/256),V.push(.9/z.length*(_+1))}var Z=new Float32Array(q.length);Z.set(q),J.setAttribute("position",new THREE.BufferAttribute(Z,3));var Y=new Float32Array(W.length);Y.set(W),J.setAttribute("customColor",new THREE.BufferAttribute(Y,3));var X=new Float32Array(V.length);X.set(V),J.setAttribute("cusmtomOpacity",new THREE.BufferAttribute(X,1));var Q=new THREE.Mesh(J,i.rmaterial);return r.addAtCoordinate(Q,[h.lon,h.lat]),l.rMesh=Q,l}(t,e,this._map,this.tb,this._angleColor,this._lineColor),this.scale=0,clearTimeout(this.timer),this._update()},t.prototype.remove=function(){if(clearTimeout(this.timer),this.obj){this.tb.remove(this.obj.outLine),this.obj.outLine.geometry=null,this.obj.outLine=null,this.tb.remove(this.obj.sline),this.obj.sline.geometry=null,this.obj.sline=null,this.tb.remove(this.obj.dline),this.obj.dline.geometry=null,this.obj.dline=null;for(var t=0;t<this.obj.lines.length;t++)this.tb.remove(this.obj.lines[t]),this.obj.lines[t].geometry=null,this.obj.lines[t]=null;this.obj.lines.length=0;for(t=0;t<this.obj.lines2.length;t++)this.tb.remove(this.obj.lines2[t]),this.obj.lines2[t].geometry=null,this.obj.lines2[t]=null;this.obj.lines2.length=0,this.tb.remove(this.obj.rMesh),this.obj.rMesh.geometry=null,this.obj.rMesh=null,this.obj.circle&&(this.tb.remove(this.obj.circle),this.obj.circle.geometry=null,this.obj.circle=null),this.obj=null}},t.prototype._update=function(){if(this.scale=this.scale+.04,this.scale<1.04)this.obj.circle.scale.set(this.scale,this.scale,1),this.rmaterial.visible=!1,this.dlineMaterial.visible=!1,this.dlineMaterial1.visible=!1,this.matLineBasic.visible=!1,this.lineMaterial.visible=!1,this.materialCuXu.visible=!1;else{this.obj.circle&&(this.tb.remove(this.obj.circle),this.obj.circle.geometry=null,this.obj.circle=null),this.rmaterial.visible=!0,this.dlineMaterial.visible=!0,this.dlineMaterial1.visible=!0,this.matLineBasic.visible=!0,this.lineMaterial.visible=!0,this.materialCuXu.visible=!0,this.obj.rMesh.rotateZ(5/180*Math.PI),this.obj.dline.rotateZ(-.5/180*Math.PI);for(var t=0;t<this.obj.lines2.length;t++)this.obj.lines2[t].rotateZ(.5/180*Math.PI)}this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),30)},t.prototype.destory=function(){this.remove(),this.rmaterial=null,this.dlineMaterial=null,this.dlineMaterial1=null,this.matLineBasic=null,this.lineMaterial=null,this.materialCuXu=null,this.circleMaterial=null}}(),function(){var t=function(t,e,i){this._map=t,this._tb=e,this.lineMarkerMaterial=new THREE.LineMaterial({color:i.color?i.color:"red",linewidth:i.weight?i.weight/1e3:.002,opacity:i.opacity?i.opacity:1,transparent:!0}),this.id=i.id,this.isBloom=null!=i.isBloom&&i.isBloom;var o=new THREE.LineGeometry,r=i.position,a=r[0];a=NPMap.T.setPoint(t,a);for(var s=[],n=this._tb.utils.projectToWorld([a.lon,a.lat,0]),l=0;l<r.length;l++){var h=r[l];h=NPMap.T.setPoint(t,h);var p=this._tb.utils.projectToWorld([h.lon,h.lat,0]),c=[p.x-n.x,p.y-n.y,0];s.push(c[0],c[1],0)}o.setPositions(s),this.position=a,this.line=new THREE.Line2(o,this.lineMarkerMaterial),this.line.name="threeOverLayPolyline-"+this.id},e=function(t,e,i){this._map=t,this._tb=e;var o,r;o=new THREE.MeshBasicMaterial({color:i.fillColor?i.fillColor:"white",transparent:!!i.fillOpacity,opacity:i.fillOpacity?i.fillOpacity:1}),r=new THREE.LineMaterial({linewidth:5e-4*i.weight,color:i.color?i.color:"black",transparent:!!i.opacity,opacity:i.opacity?i.opacity:1}),this.id=i.id,this.outLineMesh=[],this.isBloom=null!=i.isBloom&&i.isBloom;var a=i.position,s=a[0],n=!1;s instanceof Array&&(s=a[0][0],n=!0),s=NPMap.T.setPoint(t,s);var l=this._tb.utils.projectToWorld([s.lon,s.lat,0]);n||(a=[a]);for(var h=new THREE.Shape,p=0;p<a.length;p++){for(var c=[],u=0;u<a[p].length;u++){var m=a[p][u];m=NPMap.T.setPoint(t,m);var d=this._tb.utils.projectToWorld([m.lon,m.lat,0]),y=[d.x-l.x,d.y-l.y,0];c.push([y[0],y[1],0])}var _=[];if(0==p)for(u=0;u<c.length;u++)0==u?h.moveTo(c[u][0],c[u][1]):h.lineTo(c[u][0],c[u][1]),_.push(c[u][0],c[u][1],0);else{var f=new THREE.Path;for(u=0;u<c.length;u++)0==u?f.moveTo(c[u][0],c[u][1]):f.lineTo(c[u][0],c[u][1]),_.push(c[u][0],c[u][1],0);h.holes.push(f)}var g=new THREE.LineGeometry;g.setPositions(_);var v=new THREE.Line2(g,r);v.name="threeOverLayPolygonOutLine-"+this.id,this.outLineMesh.push(v)}this.position=s;var M=new THREE.ShapeBufferGeometry(h);this.mesh=new THREE.Mesh(M,o),this.mesh.name="threeOverLayPolygon-"+this.id},i=MapPlatForm.Base.MapThreeOverLayer=function(t,e,i){this._map=t,this._tb=e,this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.visible=null==i.visible||i.visible,this.projectChange=null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this.PolylineOverLays=[],this.PolygonOverLays=[]};i.prototype.addOverLays=function(i){for(var o=0;o<i.length;o++)if("NPMap.Geometry.Polyline"===i[o].CLASS_NAME){var r,a=(r=i[o]).getPath(),s={id:r.getId(),position:a,color:r.getColor(),weight:r.getWeight(),opacity:r.getOpacity(),isBloom:r.isBloom};(r=new t(this._map,this._tb,s)).isBloom?r.line.layers.set(1):r.line.layers.set(0),this._tb.addAtCoordinate(r.line,[r.position.lon,r.position.lat]),this.PolylineOverLays.push(r)}else{if("NPMap.Geometry.Polygon"!==i[o].CLASS_NAME)continue;var n;a=(n=i[o]).getPath(),s={id:n.getId(),position:a,color:n.getColor(),fillColor:n.getFillColor(),isBloom:n.isBloom,opacity:n.getOpacity(),fillOpacity:n.getFillOpacity(),weight:n.getWeight()};if((n=new e(this._map,this._tb,s)).isBloom){n.mesh.layers.set(1);for(var l=0;l<n.outLineMesh.length;l++)n.outLineMesh[l].layers.set(1)}else{n.mesh.layers.set(0);for(l=0;l<n.outLineMesh.length;l++)n.outLineMesh[l].layers.set(0)}this._tb.addAtCoordinate(n.mesh,[n.position.lon,n.position.lat]);for(l=0;l<n.outLineMesh.length;l++)this._tb.addAtCoordinate(n.outLineMesh[l],[n.position.lon,n.position.lat]);this.PolygonOverLays.push(n)}},i.prototype.showOverLays=function(t){if(this.PolylineOverLays)for(var e=0;e<this.PolylineOverLays.length;e++){(i=this._tb.world.getObjectByName("threeOverLayPolyline-"+this.PolylineOverLays[e].id))&&(i.material.visible=null==t||t)}if(this.PolygonOverLays)for(e=0;e<this.PolygonOverLays.length;e++){var i=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[e].id),o=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[e].id);i&&(i.material.visible=null==t||t),o&&(o.material.visible=null==t||t)}},i.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},i.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},i.prototype.removeAllOverlays=function(){if(this.PolylineOverLays){for(var t=0;t<this.PolylineOverLays.length;t++){(e=this._tb.world.getObjectByName("threeOverLayPolyline-"+this.PolylineOverLays[t].id))&&this._tb.remove(e)}this.PolylineOverLays[t]=null,this.PolylineOverLays.length=0}if(this.PolygonOverLays){for(t=0;t<this.PolygonOverLays.length;t++){var e;for((e=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[t].id))&&this._tb.remove(e);;){var i=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[t].id);if(!i)break;this._tb.remove(i)}}this.PolygonOverLays[t]=null,this.PolygonOverLays.length=0}},i.prototype.destory=function(){this.removeAllOverlays(),this._events=null},i.prototype.removeOverlays=function(t){for(var e=0;e<t;e++){var i=this._tb.world.getObjectByName("threeOverLayPolyline-"+t[e].id);if(i)return this._tb.remove(i),void this.PolylineOverLays.splice(e,1);i=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[e].id);var o=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[e].id);o&&this._tb.remove(o),i&&(this._tb.remove(i),this.PolygonOverLays.splice(e,1))}}}(),function(){var t=MapPlatForm.Base.MapBloomManager=function(t,e,i){this._map=t,this._tb=e,this.exposure=i&&i.exposure?parseFloat(i.exposure):1,this.bloomStrength=i&&i.bloomStrength?parseFloat(i.bloomStrength):1,this.bloomThreshold=i&&i.bloomThreshold?parseFloat(i.bloomThreshold):0,this.bloomRadius=i&&i.bloomRadius?parseFloat(i.bloomRadius):0};t.prototype.add=function(){var t={exposure:this.exposure,bloomStrength:this.bloomStrength,bloomThreshold:this.bloomThreshold,bloomRadius:this.bloomRadius};if(THREE.RenderPass){var e=new THREE.RenderPass(this._tb.scene,this._tb.camera),i=new THREE.UnrealBloomPass(new THREE.Vector2(window.innerWidth,window.innerHeight),1.5,.4,.85);i.threshold=t.bloomThreshold,i.strength=t.bloomStrength,i.radius=t.bloomRadius,this._tb.bloomComposer=new THREE.EffectComposer(this._tb.renderer),this._tb.bloomComposer.addPass(e),this._tb.bloomComposer.addPass(i),this._tb.renderer.toneMappingExposure=Math.pow(t.exposure,4)}},t.prototype.remove=function(){},t.prototype.destory=function(){this._tb.bloomComposer.renderToScreen=!1,this._tb.bloomComposer=null}}(),function(){var t=MapPlatForm.Base.MapSkyBox=function(t){var e=function t(){var e=t.SkyShader,i=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:THREE.UniformsUtils.clone(e.uniforms),side:THREE.BackSide});THREE.Mesh.call(this,new THREE.BoxBufferGeometry(1,1,1),i)};e.prototype=Object.create(THREE.Mesh.prototype),e.SkyShader={uniforms:{luminance:{value:1},turbidity:{value:2},rayleigh:{value:1},mieCoefficient:{value:.005},mieDirectionalG:{value:.8},sunPosition:{value:new THREE.Vector3},up:{value:new THREE.Vector3(0,1,0)}},vertexShader:["uniform vec3 sunPosition;","uniform float rayleigh;","uniform float turbidity;","uniform float mieCoefficient;","uniform vec3 up;","varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","const float e = 2.71828182845904523536028747135266249775724709369995957;","const float pi = 3.141592653589793238462643383279502884197169;","const vec3 lambda = vec3( 680E-9, 550E-9, 450E-9 );","const vec3 totalRayleigh = vec3( 5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5 );","const float v = 4.0;","const vec3 K = vec3( 0.686, 0.678, 0.666 );","const vec3 MieConst = vec3( 1.8399918514433978E14, 2.7798023919660528E14, 4.0790479543861094E14 );","const float cutoffAngle = 1.6110731556870734;","const float steepness = 1.5;","const float EE = 1000.0;","float sunIntensity( float zenithAngleCos ) {","\tzenithAngleCos = clamp( zenithAngleCos, -1.0, 1.0 );","\treturn EE * max( 0.0, 1.0 - pow( e, -( ( cutoffAngle - acos( zenithAngleCos ) ) / steepness ) ) );","}","vec3 totalMie( float T ) {","\tfloat c = ( 0.2 * T ) * 10E-18;","\treturn 0.434 * c * MieConst;","}","void main() {","\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );","\tvWorldPosition = worldPosition.xyz;","\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","\tgl_Position.z = gl_Position.w;","\tvSunDirection = normalize( sunPosition );","\tvSunE = sunIntensity( dot( vSunDirection, up ) );","\tvSunfade = 1.0 - clamp( 1.0 - exp( ( sunPosition.y / 450000.0 ) ), 0.0, 1.0 );","\tfloat rayleighCoefficient = rayleigh - ( 1.0 * ( 1.0 - vSunfade ) );","\tvBetaR = totalRayleigh * rayleighCoefficient;","\tvBetaM = totalMie( turbidity ) * mieCoefficient;","}"].join("\n"),fragmentShader:["varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","uniform float luminance;","uniform float mieDirectionalG;","uniform vec3 up;","const vec3 cameraPos = vec3( 0.0, 0.0, 0.0 );","const float pi = 3.141592653589793238462643383279502884197169;","const float n = 1.0003;","const float N = 2.545E25;","const float rayleighZenithLength = 8.4E3;","const float mieZenithLength = 1.25E3;","const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;","const float THREE_OVER_SIXTEENPI = 0.05968310365946075;","const float ONE_OVER_FOURPI = 0.07957747154594767;","float rayleighPhase( float cosTheta ) {","\treturn THREE_OVER_SIXTEENPI * ( 1.0 + pow( cosTheta, 2.0 ) );","}","float hgPhase( float cosTheta, float g ) {","\tfloat g2 = pow( g, 2.0 );","\tfloat inverse = 1.0 / pow( 1.0 - 2.0 * g * cosTheta + g2, 1.5 );","\treturn ONE_OVER_FOURPI * ( ( 1.0 - g2 ) * inverse );","}","const float A = 0.15;","const float B = 0.50;","const float C = 0.10;","const float D = 0.20;","const float E = 0.02;","const float F = 0.30;","const float whiteScale = 1.0748724675633854;","vec3 Uncharted2Tonemap( vec3 x ) {","\treturn ( ( x * ( A * x + C * B ) + D * E ) / ( x * ( A * x + B ) + D * F ) ) - E / F;","}","void main() {","\tfloat zenithAngle = acos( max( 0.0, dot( up, normalize( vWorldPosition - cameraPos ) ) ) );","\tfloat inverse = 1.0 / ( cos( zenithAngle ) + 0.15 * pow( 93.885 - ( ( zenithAngle * 180.0 ) / pi ), -1.253 ) );","\tfloat sR = rayleighZenithLength * inverse;","\tfloat sM = mieZenithLength * inverse;","\tvec3 Fex = exp( -( vBetaR * sR + vBetaM * sM ) );","\tfloat cosTheta = dot( normalize( vWorldPosition - cameraPos ), vSunDirection );","\tfloat rPhase = rayleighPhase( cosTheta * 0.5 + 0.5 );","\tvec3 betaRTheta = vBetaR * rPhase;","\tfloat mPhase = hgPhase( cosTheta, mieDirectionalG );","\tvec3 betaMTheta = vBetaM * mPhase;","\tvec3 Lin = pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * ( 1.0 - Fex ), vec3( 1.5 ) );","\tLin *= mix( vec3( 1.0 ), pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * Fex, vec3( 1.0 / 2.0 ) ), clamp( pow( 1.0 - dot( up, vSunDirection ), 5.0 ), 0.0, 1.0 ) );","\tvec3 direction = normalize( vWorldPosition - cameraPos );","\tfloat theta = acos( direction.y ); // elevation --\x3e y-axis, [-pi/2, pi/2]","\tfloat phi = atan( direction.z, direction.x ); // azimuth --\x3e x-axis [-pi/2, pi/2]","\tvec2 uv = vec2( phi, theta ) / vec2( 2.0 * pi, pi ) + vec2( 0.5, 0.0 );","\tvec3 L0 = vec3( 0.1 ) * Fex;","\tfloat sundisk = smoothstep( sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta );","\tL0 += ( vSunE * 19000.0 * Fex ) * sundisk;","\tvec3 texColor = ( Lin + L0 ) * 0.04 + vec3( 0.0, 0.0003, 0.00075 );","\tvec3 curr = Uncharted2Tonemap( ( log2( 2.0 / pow( luminance, 4.0 ) ) ) * texColor );","\tvec3 color = curr * whiteScale;","\tvec3 retColor = pow( color, vec3( 1.0 / ( 1.2 + ( 1.2 * vSunfade ) ) ) );","\tgl_FragColor = vec4( retColor, 1.0 );","}"].join("\n")},this._map=t;var i=this;this.id=NPMap.Utils.BaseUtils.uuid(),this._skyBoxLayer={id:this.id,type:"custom",renderingMode:"3d",onAdd:function(t,o){this.camera=new THREE.PerspectiveCamera,this.scene=new THREE.Scene,this.map=t;var r=new e;r.scale.setScalar(45e4),this.scene.add(r);var a=new THREE.Mesh(new THREE.SphereBufferGeometry(2e4,16,8),new THREE.MeshBasicMaterial({color:16777215}));a.position.y=-7e5,a.visible=!1,this.scene.add(a);var s=10,n=2,l=.005,h=.8,p=1,c=.49,u=.25,m=!1,d=r.material.uniforms;d.turbidity.value=s,d.rayleigh.value=n,d.mieCoefficient.value=l,d.mieDirectionalG.value=h,d.luminance.value=p;var y=Math.PI*(c-.5),_=2*Math.PI*(u-.5);a.position.x=4e5*Math.cos(_),a.position.y=4e5*Math.sin(_)*Math.sin(y),a.position.z=4e5*Math.sin(_)*Math.cos(y),a.visible=m,d.sunPosition.value.copy(a.position),this.renderer=new THREE.WebGLRenderer({antialias:!0,canvas:t.getCanvas(),context:o}),this.renderer.autoClear=!1,i.skyMaterial=r.material,i.sunSphere=a},render:function(t,e){var i=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(1,0,0),Math.PI/2),o=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,1,0),Math.PI/2),r=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,0,1),0),a=(new THREE.Matrix4).fromArray(e),s=(new THREE.Matrix4).makeTranslation(0,0,0).scale(new THREE.Vector3(1,-1,1)).multiply(i).multiply(o).multiply(r);this.camera.projectionMatrix.elements=e,this.camera.projectionMatrix=a.multiply(s),this.renderer.state.reset(),this.renderer.render(this.scene,this.camera),this.map.triggerRepaint()}}};t.prototype.add=function(){this._map._obj.getLayer("background")?this._map._obj.addLayer(this._skyBoxLayer,"background"):this._map._obj.addLayer(this._skyBoxLayer)},t.prototype.updateSunEffect=function(t){var e={turbidity:10,rayleigh:2,mieCoefficient:.005,mieDirectionalG:.8,luminance:1,inclination:.5,azimuth:.25,sun:!1};for(var i in t)e[i]=t[i];var o=this.skyMaterial.uniforms;o.turbidity.value=e.turbidity,o.rayleigh.value=e.rayleigh,o.mieCoefficient.value=e.mieCoefficient,o.mieDirectionalG.value=e.mieDirectionalG,o.luminance.value=e.luminance;var r=Math.PI*(e.inclination-.5),a=2*Math.PI*(e.azimuth-.5);this.sunSphere.position.x=4e5*Math.cos(a)*Math.cos(r),this.sunSphere.position.y=4e5*Math.sin(a)*Math.sin(r),this.sunSphere.position.z=4e5*Math.sin(a)*Math.cos(r),this.sunSphere.visible=e.sun,o.sunPosition.value.copy(this.sunSphere.position)},t.prototype.destory=function(){this._map._obj.removeLayer(this._skyBoxLayer.id)}}(),MapPlatForm.Base.HYService=function(t){this.CLASS_NAME="HYService",this.map=t},MapPlatForm.Base.HYService.prototype.initHyMap=function(t){var e=this,i=document.createElement("div");i.id="hymap",i.style.height="0px",i.style.width="0px";var o=this.map._containerID;document.getElementById(o).appendChild(i),hy.initMap({container:"hymap",maxZoom:17,minZoom:8}),hy.listen("loaded",function(i){_newArrowCheck(this,e),this.hyMap=map,console.log("hy map loaded..."),t&&t(map)}.bind(this))},MapPlatForm.Base.HYService.prototype.getHyPath=function(t,e,i,o){for(var r=this,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:20,s=[],n=e.split(";"),l=[],h=[],p=0,c=n.length;p<c;p++){var u={sPoint:n[p],ePoint:n[p+1]};l.push(u)}for(;0!==l.length;){var m=l.splice(0,a),d={st:t,wayPoints:JSON.stringify(m)};h.push(this._getHyPathPromise(d))}Promise.all(h).then(function(t){var e=this;_newArrowCheck(this,r),t.forEach(function(t){var i=this;_newArrowCheck(this,e),JSON.parse(t).result.forEach(function(t){var e=this;_newArrowCheck(this,i);var r=JSON.parse(t.jobResult);if(0!=r.errcode)return console.error(r.errmsg),void o(r.errmsg);r.data.rows.forEach(function(t){_newArrowCheck(this,e);var i={},o=t.distance,r=t.routelatlon;t.duration,i.length=o,i.expend=r,i.start=null,i.end=null,s.push(i)}.bind(this))}.bind(this))}.bind(this)),i&&i(s)}.bind(this),function(t){_newArrowCheck(this,r),console.error(t),o&&o(t)}.bind(this))},MapPlatForm.Base.HYService.prototype._getHyPathPromise=function(t){var e=this;return new Promise(function(i,o){var r=this;_newArrowCheck(this,e),hy.getPath(t,function(t){_newArrowCheck(this,r),i(t)}.bind(this),function(t){_newArrowCheck(this,r),console.erro(t),o(t)}.bind(this))}.bind(this))}},{}]},{},[1]);