<template>
  <div class="third-step-box">
    <div class="top-tips">注：确认填写挖掘规则必填项</div>
    <div class="step-content">
      <div class="left-box" id="mountNodeThird"></div>
      <div class="right-box">
        <div class="title">
          <div class="title-l">挖掘规则设置</div>
          <div class="query-box">
            <DatePicker
              v-model="dataTime"
              format="yyyy-MM-dd"
              type="daterange"
              placement="bottom-end"
              placeholder="请选择挖掘时间"
              @on-change="dataTimeChange"
            />
            <!-- <CustomDate
              class="time"
              v-model="queryParams.startTime"
              placeholder="开始时间"
              key="startDate"
              @affirm="changeCustomDate($event, 'startDate')"
            />-<CustomDate
              class="time"
              v-model="queryParams.endTime"
              placeholder="结束时间"
              key="endDate"
              @affirm="changeCustomDate($event, 'endDate')"
            /> -->
            <Select v-model="queryParams.isIntersection">
              <Option value="1">交集</Option>
              <Option value="2">并集</Option>
            </Select>
          </div>
        </div>
        <div class="rules-box">
          <div v-for="(value, key) in dataForms" :key="dataForms[key].id">
            <Form
              :model="dataForms[key]"
              :rules="ruleValidate"
              :label-width="85"
            >
              <div class="rule-type">
                <div
                  class="rule-item"
                  v-if="
                    dataForms[key].ruleName || dataForms[key].ruleName == ''
                  "
                >
                  <FormItem class="ml0">
                    <div class="rule-name">
                      <img src="./img/relation.png" alt="" />
                      <div>{{ dataForms[key].ruleName }}</div>
                    </div>
                  </FormItem>
                </div>
                <div
                  class="rule-item"
                  :style="item.style"
                  v-for="(item, index) in dataForms[key].ruleList"
                  :key="key + index"
                >
                  <FormItem
                    :label="item.labelName"
                    :prop="item.required ? item.labelKey : ''"
                    :label-width="item.labelWidth"
                  >
                    <!-- <Select
                        v-if="item.type == 'selectWeekStart'"
                        @on-change="startWeekChange(key, item.labelValue)"
                        v-model="item.labelValue"
                      >
                        <Option
                          v-for="(item1, index1) in dataForms[key].startWeekData"
                          :key="index1 + 'm'"
                          :value="item1.value"
                          :label="item1.name"
                          :disabled="item1.disabled"
                        ></Option>
                      </Select>
                      <Select
                        v-if="item.type == 'selectWeekEnd'"
                        @on-change="endWeekChange(key, item.labelValue)"
                        v-model="item.labelValue"
                      >
                        <Option
                          v-for="(item2, index2) in dataForms[key].endWeekData"
                          :key="index2 + 'e'"
                          :value="item2.value"
                          :label="item2.name"
                          :disabled="item2.disabled"
                        ></Option>
                      </Select> -->
                    <Select
                      v-if="item.type == 'timePeriod'"
                      v-model="item.labelValue"
                      transfer
                    >
                      <Option
                        v-for="(item3, index3) in dataForms[key].timePeriodData"
                        :key="index3 + 'l'"
                        :value="item3.value"
                        :label="item3.name"
                      ></Option>
                    </Select>
                    <div>
                      <Select
                        :class="
                          item.multiple && item.showOps ? 'multiple-width' : ''
                        "
                        v-if="item.type == 'selectCommon'"
                        v-model="item.labelValue"
                        :multiple="item.multiple"
                        filterable
                        clearable
                        transfer
                        @on-change="
                          (val) =>
                            selectCascadeChange(
                              val,
                              item,
                              dataForms[key].ruleList
                            )
                        "
                      >
                        <Option
                          v-for="(item1, index1) in item.selectOptionList"
                          :key="index1 + 'i'"
                          :value="item1.value"
                          :label="item1.name"
                        ></Option>
                      </Select>
                      <Select
                        style="width: 25%; margin-left: 2%"
                        v-if="item.multiple && item.showOps"
                        v-model="item.isIntersection"
                      >
                        <Option value="12">交集</Option>
                        <Option value="11">并集</Option>
                      </Select>
                    </div>
                    <Input
                      v-if="item.type == 'input'"
                      v-model="item.labelValue"
                      placeholder="请输入"
                    ></Input>
                    <InputNumber
                      v-if="item.type == 'inputNumber'"
                      :max="9999"
                      :min="1"
                      v-model="item.labelValue"
                      style="width: 100% !important"
                    />
                  </FormItem>
                </div>
              </div>
            </Form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
import request from "@/libs/request";
import { getEntityByNames } from "@/api/number-cube";
import { queryDataByKeyTypes } from "@/api/user";
import CustomDate from "@/components/hl-daterange/index.vue";

export default {
  components: { CustomDate },
  props: {
    firstStepData: {
      type: Object,
      default: () => {},
    },
    secondStepData: {
      type: Object,
      default: () => {},
    },
    relationSelectListZhijie: {
      type: Array,
      default: () => [],
    },
    relationSelectListJianjie: {
      type: Array,
      default: () => [],
    },
    targetNodeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      graphThird: null,
      graphData: {
        nodes: [],
        edges: [],
      },
      startEdge: [], //页面初始化直接关系
      startNodes: [], //页面初始化实体
      dataForms: {},
      ruleValidate: {
        startWeek: [
          { required: true, message: "请选择开始周期", trigger: "blur" },
        ],
        endWeek: [
          { required: true, message: "请选择结束周期", trigger: "blur" },
        ],
        timePeriod: [
          { required: true, message: "请选择时间周期", trigger: "blur" },
        ],
        countAlgorithm: [
          { required: true, message: "请选择次数算法", trigger: "blur" },
        ],
        // countNum: [{ required: true, message: "请输入次数", trigger: "blur" }],
      },

      queryParams: {
        startTime: "",
        endTime: "",
        isIntersection: "1",
      },
      dataTime: [],
      requiredList: [],
    };
  },
  mounted() {
    this.graphThird = new G6.Graph({
      container: "mountNodeThird",
      width: 646,
      height: 465,
      fitView: true,
      fitViewPadding: [20, 100, 20, 100],
      modes: {
        default: ["drag-canvas", "zoom-canvas", "'drag-node'"],
      },
    });
  },
  methods: {
    async getRelatonData() {
      //   console.log(this.secondStepData.nodes[0].ext.label, "来源实体");
      let relationSelects = [];
      relationSelects = [
        ...this.relationSelectListZhijie,
        ...this.relationSelectListJianjie,
      ];
      this.dataForms = {};
      this.dataTime = [];
      this.queryParams.startTime = "";
      this.queryParams.endTime = "";
      let namesList = [];
      let names = [];
      relationSelects.forEach((item, index) => {
        namesList[index] = [];
        namesList[index] = [
          item.relationInfo.sourceEntity,
          item.relationInfo.targetEntity,
        ];
        namesList[index] = [...new Set(namesList[index])];
        if (namesList[index].length > 1) {
          namesList[index] = namesList[index].filter(
            (ite) => ite != this.secondStepData.nodes[0].ext.label
          );
        }
        names.push(namesList[index][0]);
        //初始化规则
        this.dataForms[`form${index + 1}`] = {
          id: Math.random().toString(16),
          archiveName: names[index],
          ruleName: item.name,
          ruleNameEn: item.relationInfo.name,
          //   startWeekData: [
          //     { name: "周一", disabled: false, value: "1" },
          //     { name: "周二", disabled: false, value: "2" },
          //     { name: "周三", disabled: false, value: "3" },
          //     { name: "周四", disabled: false, value: "4" },
          //     { name: "周五", disabled: false, value: "5" },
          //     { name: "周六", disabled: false, value: "6" },
          //     { name: "周日", disabled: false, value: "7" },
          //   ],
          //   endWeekData: [
          //     { name: "周一", disabled: false, value: "1" },
          //     { name: "周二", disabled: false, value: "2" },
          //     { name: "周三", disabled: false, value: "3" },
          //     { name: "周四", disabled: false, value: "4" },
          //     { name: "周五", disabled: false, value: "5" },
          //     { name: "周六", disabled: false, value: "6" },
          //     { name: "周日", disabled: false, value: "7" },
          //   ],
          timePeriodData: [
            { name: "每天", value: "00" },
            { name: "每个周一至周五", value: "01" },
            { name: "每周末", value: "02" },
            { name: "每周", value: "03" },
            { name: "每月", value: "04" },
            { name: "每年", value: "05" },
            { name: "某天", value: "10" },
            { name: "某个周一至周五", value: "11" },
            { name: "某周末", value: "12" },
            { name: "某周", value: "13" },
            { name: "某月", value: "14" },
            { name: "某年", value: "15" },
          ],
          ruleList: [
            // {
            //   labelKey: "startWeek",
            //   labelValue: "1",
            //   labelName: "时间周期",
            //   type: "selectWeekStart",
            //   required: true,
            //   style: {
            //     width: "60%",
            //   },
            // },
            // {
            //   labelKey: "endWeek",
            //   labelValue: "7",
            //   labelName: "",
            //   type: "selectWeekEnd",
            //   required: true,
            //   style: {
            //     width: "40%",
            //   },
            //   labelWidth: 10,
            // },
            {
              labelKey: "timePeriod",
              labelValue: "",
              labelName: "时间周期",
              type: "timePeriod",
              required: true,
              style: {
                width: "100%",
              },
            },
            {
              labelKey: "countAlgorithm",
              labelValue: "",
              labelName: "次数",
              type: "selectCommon",
              required: true,
              style: {
                width: "60%",
              },
              selectOptionList: [
                { name: ">", value: "1" },
                { name: ">=", value: "2" },
                { name: "<", value: "3" },
                { name: "<=", value: "4" },
                { name: "=", value: "5" },
                { name: "!=", value: "6" },
              ],
            },
            {
              labelKey: "countNum",
              labelValue: null,
              labelName: "",
              type: "inputNumber",
              required: false,
              style: {
                width: "40%",
              },
              labelWidth: 10,
            },
          ],
        };
      });
      //查询更多规则回显
      let params = {
        // graphId: relationSelects[0].relationInfo.graphId,
        graphInstanceId:
          this.firstStepData.nodes[1].targetNodeInfo.graphInstanceId,
        names,
      };
      const res = await getEntityByNames(params);
      //   console.log(res.data);
      if (res.data && res.data.length > 0) {
        let properties = [];
        for (let key in this.dataForms) {
          res.data.forEach((item) => {
            if (this.dataForms[key].archiveName == item.name) {
              item.properties = item.properties.filter(
                (itm) => itm.minedable == 1
              );
              properties = item.properties.map((ite) => {
                if (!ite.propertyDicType && !ite.propertyDataInterface) {
                  let option = {
                    labelKey: ite.name,
                    labelValue: "",
                    labelName: ite.nameCn,
                    type: "input",
                    required: false,
                  };
                  return option;
                } else {
                  //   console.log("特殊配置");
                  //   console.log(ite.propertyDicType, "获取下拉框列表的字典值");
                  return this.getDicTypes(ite);
                }
              });
            }
          });
          this.dataForms[key].ruleList = [
            ...this.dataForms[key].ruleList,
            ...properties,
          ];
          this.$forceUpdate();
        }
      }
    },
    // 获取需要级联的数据
    getMultipleChoice(name) {
      const { multipleChoice = [] } = JSON.parse(
        this.targetNodeData.specialAttributes || "{}"
      );

      let obj = { ops: "", showOps: false, multiple: false };
      multipleChoice.forEach((item) => {
        if (name == item.name) {
          obj = { ops: item.ops[0], showOps: item.showOps, multiple: true };
        } else {
          obj = { ops: "", showOps: false, multiple: false };
        }
      });
      return obj;

      //   return multipleChoice.includes(name);
    },
    getDicTypes(ite) {
      let option = {
        labelKey: ite.name,
        labelValue: "",
        labelName: ite.nameCn,
        type: "selectCommon",
        required: false,
        multiple: this.getMultipleChoice(ite.name).multiple,
        parentPropertyName: ite.parentPropertyName,
        selectOptionList: [],
        isIntersection: this.getMultipleChoice(ite.name).ops,
        showOps: this.getMultipleChoice(ite.name).showOps,
      };
      if (ite.propertyDataInterface) {
        option.propertyDataInterface = JSON.parse(
          ite.propertyDataInterface || "{}"
        );
        this.setPropertyDicType(option);
      } else {
        queryDataByKeyTypes([ite.propertyDicType]).then((res) => {
          res.data[0][ite.propertyDicType].forEach((item, index) => {
            this.$set(option.selectOptionList, index, {
              name: item.dataValue,
              value: item.dataKey,
            });
            this.$forceUpdate();
          });
        });
      }
      return option;
    },
    async setPropertyDicType(option) {
      const { parentPropertyName, propertyDataInterface } = option;
      if (parentPropertyName) return;
      const data = await this.querySelectData(propertyDataInterface);
      option.selectOptionList = data;
      this.$forceUpdate();
    },
    // 级联下拉选择
    async selectCascadeChange(val, item, dataFormsList) {
      const { labelKey } = item;
      const subItem = dataFormsList.find(
        (el) => el.parentPropertyName === labelKey
      );
      if (subItem) {
        const { propertyDataInterface, multiple } = subItem;
        let selectOptionList = [];
        const isNullValue =
          Object.prototype.toString.call(val) === "[object Array]"
            ? val?.length
            : val;
        if (isNullValue) {
          selectOptionList = await this.querySelectData(
            propertyDataInterface,
            val
          );
        }
        subItem.labelValue = multiple ? [] : undefined;
        subItem.selectOptionList = selectOptionList;
        this.$forceUpdate();
      }
    },
    // 获取非字典，从其它接口获取的数据
    async querySelectData(propertyDataInterface, val = "") {
      const { fieldNames, url, method, params } = propertyDataInterface;
      const newParams = {};
      for (const key in params) {
        const obj = params[key];
        newParams[key] = obj === "$" ? val : obj;
      }
      const resp = await request({
        url,
        method,
        [method === "post" ? "data" : "params"]: newParams,
      });
      const data = resp?.data?.entities || resp?.data || [];
      return data.map((item) => {
        const obj = {};
        for (const key in fieldNames) {
          obj[key] = item[fieldNames[key]];
        }
        return obj;
      });
    },
    init() {
      this.startNodes = this.secondStepData?.nodes.map((item) => item) || [];
      this.startEdge = this.secondStepData?.edges.map((item) => item) || [];
      this.startEdge.forEach((item) => {
        item.labelCfg.style.cursor = "default";
      });
      this.graphData.edges = [...this.startEdge];
      this.graphData.nodes = [...this.startNodes];
      G6.Util.processParallelEdges(this.graphData.edges, 30);
      this.graphThird.data(this.graphData);
      this.graphThird.render();
    },
    startWeekChange(key, val) {
      this.dataForms[key].endWeekData.forEach((item) => {
        if (Number(val) > Number(item.value)) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
      this.$forceUpdate();
    },
    endWeekChange(key, val) {
      this.dataForms[key].startWeekData.forEach((item) => {
        if (Number(val) < Number(item.value)) {
          item.disabled = true;
        } else {
          item.disabled = false;
        }
      });
      this.$forceUpdate();
    },
    dataTimeChange(val) {
      this.dataTime = val;
      if (this.dataTime && this.dataTime.length > 0 && this.dataTime[0] != "") {
        this.queryParams.startTime = this.$dayjs(this.dataTime[0]).format(
          "YYYY-MM-DD 00:00:00"
        );
        this.queryParams.endTime = this.$dayjs(this.dataTime[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.dataTime = [];
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
    },
    changeCustomDate(val, type) {
      if (type === "startDate") {
        this.queryParams.startTime = this.$dayjs(val).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      if (type === "endDate") {
        this.queryParams.endTime = this.$dayjs(val).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
    },
    //下一步
    next() {
      this.requiredList = [];
      for (let key in this.dataForms) {
        if (this.dataForms.hasOwnProperty(key)) {
          this.dataForms[key].ruleList.forEach((item) => {
            if (item.required || item.labelKey == "countNum") {
              this.requiredList.push(item);
            }
          });
        }
      }
      if (this.queryParams.startTime == "") {
        this.$Message.warning("请选择挖掘时间！");
      } else if (
        this.requiredList.findIndex((item) => !item.labelValue) != -1
      ) {
        this.$Message.warning("请正确选择时间周期、选择次数算法、填写次数！");
      } else {
        this.$emit("getThirdData", {
          dataForms: { ...this.dataForms },
          ...this.queryParams,
        });
      }
    },
  },
  watch: {
    secondStepData: {
      handler(val) {
        if (val) {
          this.init();
          if (
            this.relationSelectListZhijie.length > 0 ||
            this.relationSelectListJianjie.length > 0
          ) {
            this.getRelatonData();
          }
        }
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.multiple-width {
  width: 73%;
}
.third-step-box {
  width: 100%;
  height: 100%;
  .top-tips {
    width: 100%;
    text-align: right;
    font-size: 14px;
    margin-bottom: 5px;
    color: #f29f4c;
  }
  .step-content {
    width: 100%;
    height: calc(~"100% - 10px");
    display: flex;
    background: #ffffff;
    #mountNodeThird {
      width: 60%;
      height: 100%;
      border: 1px solid #d3d7de;
      margin-right: 10px;
    }
    .right-box {
      flex: 1;
      height: 100%;
      border: 1px solid #d3d7de;
      .title {
        height: 40px;
        line-height: 40px;
        padding-left: 10px;
        background: #f9f9f9;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .title-l {
          color: rgba(0, 0, 0, 0.9);
          font-weight: 700;
          width: 90px;
        }
        .query-box {
          flex: 1;
          text-align: right;
          .time {
            width: 180px;
            line-height: 34px;
          }
          /deep/ .ivu-select {
            width: 25%;
            margin: 0 10px 0 10px;
            .ivu-select-item {
              text-align: center;
            }
          }
          /deep/ .ivu-date-picker {
            width: 210px;
          }
        }
      }
      .rules-box::-webkit-scrollbar {
        width: 0;
      }
      .rules-box {
        width: 100%;
        height: calc(~"100% - 40px");
        overflow: auto;
        padding: 10px;
        .rule-type {
          width: 100%;
          background: #f9f9f9;
          padding: 10px;
          margin-bottom: 10px;
          display: flex;
          flex-wrap: wrap;
          .rule-item {
            width: 100%;
          }
          .rule-name {
            display: flex;
            align-items: center;
            height: 30px;
            color: #3d3d3d;
            font-weight: 700;
            border-bottom: 1px solid #d3d7de;
            img {
              width: 16px;
              height: 15px;
              margin-right: 5px;
            }
          }
          .ml0 {
            /deep/ .ivu-form-item-content {
              margin-left: 0 !important;
            }
          }
          /deep/ .ivu-form-item {
            margin-bottom: 12px;
            .ivu-form-item-label {
              color: rgba(0, 0, 0, 0.4513);
            }
            .ivu-form-item-content {
              margin-left: 85px;
              .ivu-form-item-error-tip {
                top: 80%;
                font-size: 10px;
              }
              .ivu-icon-ios-close {
                top: 0;
                font-size: 20px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
