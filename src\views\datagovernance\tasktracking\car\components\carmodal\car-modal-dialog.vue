<template>
  <ui-modal ref="modalChild" class="more-info-modal" :title="title" v-model="visible" :width="width" :footerHide="true">
    <!-- 图像抓拍时间准确性检测 子弹框 -->
    <div class="detail" style="margin: 0 auto; width: 300px" v-if="curIndex == 1">
      <div>
        抓拍时间：
        <span class="blue">{{ infoObj.shotTime }}</span>
      </div>
      <div>
        入库时间：
        <span class="blue">{{ infoObj.firstIntoViewTime }}</span>
      </div>
      <div style="padding-left: 28px">
        结论：
        <span class="red">{{ infoObj.reason }}</span>
      </div>
      <div class="clear"></div>
    </div>

    <div class="detail" style="margin: 0 auto; width: 300px" v-if="curIndex == 2">
      <div>
        设备类型：
        <span class="blue">{{ infoObj.isImportant ? isImportantObj[infoObj.isImportant] : '--' }}</span>
      </div>
      <div>
        抓拍时间：
        <span class="blue">{{ infoObj.shotTime }}</span>
      </div>
      <div>
        入库时间：
        <span class="blue">{{ infoObj.firstIntoViewTime }}</span>
      </div>
      <div style="padding-left: 28px">
        结论：
        <span class="red">{{ infoObj.reason }}</span>
      </div>
      <div class="clear"></div>
    </div>
    <div v-if="curIndex == 4 || curIndex == 5">
      <div v-if="tableData.length > 0 && !resonError">
        <tabsView
          :multiSelect="false"
          v-if="curIndex == 5"
          :needExpand="false"
          :list="infoObj.resultDetailList"
          @selectInfo="selectInfo"
        />
        <div class="carTitle mt20">
          {{ list.originalField ? propertyNameObj[list.originalField] : '' }}原始值：
          <span class="blue">{{ list ? list.originalFieldValue : '' }}</span>
        </div>
        <ui-table class="ui-table mt20" :table-columns="columns" :table-data="tableData" :minus-height="650">
          <template #algorithmVendor="{ row }">
            <a v-if="row.type && row.type === 'all'">综合判定</a>
            <span v-else>{{ row.algorithmVendor }}</span>
          </template>
          <template #recognitionResult="{ row }">
            <div v-if="curIndex == 4">
              <span class="greenbg" v-if="row.recognitionResult == list.originalFieldValue">{{
                row.recognitionResult ? row.recognitionResult : '无法判定'
              }}</span>
              <span class="bluebg" v-else>{{ row.recognitionResult ? row.recognitionResult : '无法判定' }}</span>
            </div>
            <div v-else>
              <span class="greenbg" v-if="row.recognitionResult == list.originalFieldValue">{{
                row.recognitionResult ? row.recognitionResult : '无法判定'
              }}</span>
              <span class="bluebg" v-else>{{ row.recognitionResult ? row.recognitionResult : '无法判定' }}</span>
            </div>
          </template>
          <template #pdjg="{ row }">
            <span v-if="row.type && row.type === 'all' && curIndex == 5">
              <img v-if="!row.recognitionResult" src="@/assets/img/car-modal/unabletoverify.png" alt />
              <img
                v-else-if="row.recognitionResult == list.originalFieldValue"
                src="@/assets/img/car-modal/consistent.png"
                alt
              />
              <img
                v-else-if="row.recognitionResult != list.originalFieldValue"
                src="@/assets/img/car-modal/no-consistent.png"
                alt
              />
            </span>
            <span v-else-if="row.type && row.type === 'all' && curIndex == 4">
              <img v-if="!row.recognitionResult" src="@/assets/img/car-modal/unabletoverify.png" alt />
              <img
                v-else-if="row.recognitionResult == list.originalFieldValue"
                src="@/assets/img/car-modal/car-consistent.png"
                alt
              />
              <img
                v-else-if="row.recognitionResult != list.originalFieldValue"
                src="@/assets/img/car-modal/car-no-consistent.png"
                alt
              />
            </span>
            <div v-else>
              <span v-if="row.recognitionResult == list.originalFieldValue" class="green">一致</span>
              <span v-else class="red">不一致</span>
            </div>
          </template>
        </ui-table>
        <div class="mt30">
          算法选举正确{{ propertyNameObj[list.originalField] }}为：
          <span v-if="list.result">
            <span class="greenbg" v-if="list && list.result == list.originalFieldValue">{{ list.result }}</span>
            <span class="bluebg" v-else>{{ list.result ? list.result : '无法判定' }}</span>
          </span>
        </div>
      </div>
      <div v-else class="item-detail">
        <span class>结论：</span>
        <span class="red">{{ infoObj.reason }}</span>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import carThrem from '@/config/api/car-threm';
export default {
  props: {
    id: {
      type: [String, Number],
      default: '',
    },
    title: {
      type: String,
      default: '车牌判定',
    },
    // 算法类型
    algorithmVendorType: {
      type: Object,
      default() {},
    },
    // 设备类型
    isImportantObj: {
      type: Object,
      default() {},
    },
    curIndex: {
      type: Number,
      default: 1,
    },
    // 检测属性
    propertyNameObj: {
      type: Object,
      default() {},
    },
  },
  data() {
    return {
      width: 680,
      visible: false,
      columns: [
        { title: '检测算法', slot: 'algorithmVendor' },
        { title: '识别结果', slot: 'recognitionResult' },
        { title: '判定结果', slot: 'pdjg' },
      ],
      list: {},
      infoObj: {},
      resonError: '',
      tabsList: [],
      tableData: [],
    };
  },
  created() {},
  methods: {
    async open() {
      await this.$http.get(carThrem.viewVehicleResult + this.id).then((res) => {
        if (res.data.code === 200) {
          this.resonError = false;
          this.setInfoObj(res.data.data);
        }
      });
      this.$nextTick(() => {
        this.visible = true;
      });
    },
    async setInfoObj(infoObj) {
      infoObj.resultDetailList = infoObj.resultDetail ? JSON.parse(infoObj.resultDetail) : [];
      if (this.curIndex == 4 || this.curIndex == 5) {
        if (infoObj.resultDetailList.length == 0) {
          this.resonError = true;
          this.width = 680;
          this.infoObj = infoObj;
        }
        if (infoObj.resultDetailList && infoObj.resultDetailList.length > 0) {
          infoObj.resultDetailList.map((val) => {
            val.name = this.propertyNameObj[val.originalField];
            val.list.push({ type: 'all', recognitionResult: val.result });
            val.list.map((vals) => {
              if (!vals.type || vals.type !== 'all') {
                vals.algorithmVendor = this.algorithmVendorType[vals.algorithmVendor];
                vals.recognitionResult = vals.recognitionResult || '暂无识别';
              }
            });
          });
          this.list = infoObj.resultDetailList[0];
          this.infoObj = infoObj;
          this.tableData = this.list.list || [];
        } else {
          this.list = {};
          this.tableData = [];
        }
      } else {
        this.infoObj = infoObj;
      }
    },
    // 选中
    selectInfo(item) {
      this.list = item;
      this.tableData = this.list.list || [];
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    tabsView: require('@/components/ui-select-tabs').default,
  },
};
</script>
<style lang="less" scoped>
.more-info-modal {
  .detail {
    div {
      position: relative;
      margin-top: 16px;
      span {
        position: absolute;
        left: 80px;
      }
    }
  }
  .item-detail {
    height: 100px;
    line-height: 100px;
    text-align: center;
  }
  .blue {
    color: var(--color-primary);
  }
  .red {
    color: #e44f22;
  }
  /deep/ .ivu-modal-body {
    padding-top: 0px;
  }
}
.bluebg {
  background: var(--color-primary);
  padding: 6px 10px;
  display: inline-block;
  border-radius: 6px;
  color: #fff;
}
.greenbg {
  background: #087120;
  padding: 6px 10px;
  display: inline-block;
  border-radius: 6px;
  color: #fff;
}
.green {
  color: #087120;
}
.blue {
  color: var(--color-primary);
}
.mt20 {
  margin-top: 20px;
}
.mt30 {
  margin-top: 30px;
}
/deep/ .ui-table .no-data {
  width: 100%;
}
</style>
