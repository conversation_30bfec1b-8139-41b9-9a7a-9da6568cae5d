<!--
    * @FileDescription: 高危人员风险行为
    * @Author: H
    * @Date: 2023/05/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-21 13:57:13
 -->
<template>
  <div class="card-item">
    <ui-image :src="data.sceneImg"></ui-image>
    <div class="type">
      {{ data.riskyBehaviorType | commonFiltering(riskyBehaviorTypeList) }}
    </div>
    <div class="info">
      <p>
        报警时间：<span class="ellipsis">{{
          data.alarmTime || data.absTime
        }}</span>
      </p>
      <p>
        报警设备：<span class="ellipsis">{{ data.deviceName || "--" }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
export default {
  name: "TogetherAlarm",
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      riskyBehaviorTypeList: "dictionary/getRiskyBehaviorTypeList", // 行为类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
  },
};
</script>

<style lang="less" scoped>
.mr-5 {
  margin-right: 5px;
}
.card-item {
  width: 345px;
  height: 295px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  padding: 10px;
  .ui-image {
    width: 100%;
    height: 200px;
  }
  .type {
    color: #2c86f8;
    font-weight: 700;
    line-height: 26px;
    font-size: 16px;
  }
  .info {
    color: rgba(0, 0, 0, 0.6);
    p {
      white-space: nowrap;
      display: flex;
      line-height: 26px;
    }
    span {
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
