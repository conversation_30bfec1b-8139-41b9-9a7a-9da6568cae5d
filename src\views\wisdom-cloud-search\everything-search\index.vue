<!--
 * @Author: <PERSON><PERSON><PERSON> du<PERSON><PERSON>@qishudi.com
 * @Date: 2024-05-27 15:01:43
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-03-19 15:34:07
 * @FilePath: \icbd-view\src\views\wisdom-cloud-search\cloud-default-page\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <keep-alive>
    <component :is="componentName"></component>
  </keep-alive>
</template>
<script>
import { mapActions } from "vuex";
import searchIndex from "./search-index/index";
import advancedSearch from "@/views/wisdom-cloud-search/cloud-default-page/advanced-search/index";

export default {
  name: "cloud-default-page",
  components: { searchIndex, advancedSearch },
  data() {
    return {
      componentNames: ["searchIndex", "advancedSearch"],
      componentName: "",
    };
  },
  async created() {
    await this.getDictData();
  },
  watch: {
    $route: {
      handler(to, from) {
        let { componentName } = to.query;
        this.componentName = this.componentNames.includes(componentName)
          ? componentName
          : this.componentNames[0];
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
  },
};
</script>
