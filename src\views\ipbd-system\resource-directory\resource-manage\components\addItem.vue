<template>
  <ui-modal v-model="visible" :r-width="1200" title="新增数据项" list-content @onCancel="addFieldNum = ''"
    @onOk="confirmHandle" class="config">
    <div class="operate_bar">
      <Button type="primary" @click="addFieldHandle">新增</Button>
      <Input v-model="addFieldNum" placeholder="输入" maxlength="2">
      <span slot="append">条</span>
      </Input>
    </div>
    <Table class="auto-fill table" :columns="columns" border :loading="loading" :data="tableData" :rules="ruleValidate"
      max-height="580">
      <template #fieldName="{ index }">
        <Input placeholder="请输入字段名" v-model="tableData[index].fieldName"></Input>
      </template>
      <template #fieldNameCn="{ index }">
        <Input placeholder="请输入中文名" v-model="tableData[index].fieldNameCn"></Input>
      </template>
      <template #fieldType="{ index }">
        <Select :placeholder="tableData[index].fieldType" v-model="tableData[index].fieldType" transfer>
          <Option v-for="(item, $index) in fileType" :value="item" :key="$index">{{ item }}</Option>
        </Select>
      </template>
      <template #fieldTypeLen="{ index }">
        <Input :placeholder="tableData[index].fieldTypeLen" v-model="tableData[index].fieldTypeLen"
          :disabled="notSetLen.includes(tableData[index].fieldType)"></Input>
      </template>
      <template #isPk="{ index }">
        <Select v-model="tableData[index].isPk" transfer>
          <Option :value="0">否</Option>
          <Option :value="1">是</Option>
        </Select>
      </template>
      <template #isIndex="{ index }">
        <Select v-model="tableData[index].isIndex" transfer>
          <Option :value="0">否</Option>
          <Option :value="1">是</Option>
        </Select>
      </template>
      <template #sensitiveLv="{ index }">
        <Select v-model="tableData[index].sensitiveLv" transfer>
          <Option :value="0">一级</Option>
          <Option :value="1">二级</Option>
        </Select>
      </template>
      <template #dataElement="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].dataElement"></Input>
      </template>
      <template #determiner="{ index }">
        <Input placeholder="请输入" v-model="tableData[index].determiner"></Input>
      </template>
    </Table>
  </ui-modal>
</template>
<script>
import { queryResItems, addResItems } from '@/api/dataGovernance'
export default {
  data () {
    return {
      visible: false,
      loading: false,
      resourceId: '',
      addFieldNum: '',
      fileType: ["VARCHAR", "DATETIME", "TIMESTAMP", "NUMBER", "CHAR", "BLOB"],
      // 不设置长度的字段类型
      notSetLen: ['DATETIME', 'TIMESTAMP'],
      ruleValidate: {
        fieldName: [{ required: true, message: '字段名称', trigger: 'blur' }],
        fieldNameCn: [{ required: true, message: '字段中文名称', trigger: 'blur' }],
        fileType: [{ required: true, message: '字段类型', trigger: 'blur' }]
      },
      columns: [
        { title: '序号', align: 'center', width: 90, type: 'index', key: 'index', fixed: 'left' },
        { title: '字段名称', slot: 'fieldName', width: 140 },
        { title: '字段中文名称', slot: 'fieldNameCn', width: 160 },
        { title: '字段类型', slot: 'fieldType', width: 140 },
        { title: '字段类型长度', slot: 'fieldTypeLen', width: 140 },
        { title: '是否主键', slot: 'isPk', width: 140 },
        { title: '是否索引', slot: 'isIndex', width: 140 },
        { title: '敏感级别', slot: 'sensitiveLv', width: 140 },
        { title: '数据元', slot: 'dataElement', width: 140 },
        { title: '限定词', slot: 'determiner', width: 180 }
      ],
      tableData: [],
      newItems: []
    }
  },
  methods: {
    // 初始化
    show (val) {
      this.newItems = []
      this.tableData = []
      this.visible = true
      this.resourceId = val.id
      this.$nextTick(() => {
        this.loading = true
        queryResItems(val.id).then(res => {
          this.tableData = res.data
          this.loading = false
        })
      })
    },
    // 新增字段
    addFieldHandle () {
      if (!this.addFieldNum) {
        this.$Message.error('请输入新增字段数量')
        return
      }
      for (let i = 0; i < this.addFieldNum; i++) {
        let obj = {
          fieldName: '',
          fieldNameCn: '',
          fieldType: 'VARCHAR',
          fieldTypeLen: '255',
          isPk: 0,
          isIndex: 0,
          sensitiveLv: '',
          dataElement: '',
          determiner: ''
        }
        this.tableData.unshift(obj)
        this.newItems.unshift(obj)
      }
      this.addFieldNum = ''
    },
    // 确认提交
    confirmHandle () {
      let correctCount = 0
      this.newItems.forEach(item => {
        if (!item.fieldName) {
          this.$Message.error('第' + (this.newItems.indexOf(item) + 1) + '条数据项字段名称为空')
          return
        }
        if (!item.fieldNameCn) {
          this.$Message.error('第' + (this.newItems.indexOf(item) + 1) + '条数据项字段中文名称为空')
          return
        }
        correctCount++
      })
      if (correctCount === this.newItems.length) {
        addResItems({ resourceId: this.resourceId, itemDetails: this.newItems }).then(res => {
          if (res.code !== 200) {
            this.$Message.error(res.msg)
            return
          }
          this.$Message.success('新增成功')
          this.visible = false
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.operate_bar {
  display: flex;
  align-items: center;
  width: 100%;

  &>.ivu-input-group {
    display: flex;
    margin-right: 10px;
    height: 100%;

    /deep/ .ivu-input-group-append {
      background-color: #e3e3e3;
      width: 25px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &>.ivu-btn {
    margin-right: 5px;
  }

  /deep/ .ivu-input {
    border-radius: 0;
  }
}

.config {
  /deep/ .ivu-table-body {
    min-height: 220px;
  }

  /deep/.ivu-table-tbody tr td {
    background-color: #f9f9f9 !important;
  }

  /deep/ .ivu-table-border th {
    border-right: 1px solid #d3d7de !important;
  }

  /deep/.ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #d3d7de;
  }

  /deep/.ivu-input,
  .ivu-select {
    width: 110px;
  }
}
</style>
