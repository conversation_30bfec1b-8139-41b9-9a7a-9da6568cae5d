<template>
  <!-- 
    适配字典查询值
   -->
  <div>
    <ul>
      <li
        :class="{ select: currentIndex == index }"
        v-for="(item, index) in list"
        :key="index"
        @click="liClick(item, index)"
      >
        {{ item.name }}
      </li>
    </ul>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
export default {
  props: {
    // 数据
    list: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      currentIndex: 0,
      dataList: [],
    };
  },
  computed: {},
  activated() {
    this.currentIndex = 0;
  },
  async mounted() {},
  methods: {
    /**
     * 单个
     */
    liClick(item, index) {
      this.currentIndex = index;
      this.$emit("selectItem", item, index);
    },
  },
};
</script>
<style lang="less" scoped>
.card {
  max-height: 60px;
  overflow: hidden;
}
.card:after {
  clear: both;
  content: "";
}

.select {
  color: #fff;
  background: #2c86f8;
  // padding: 0 6px;
  // border-radius: 2px;
  border-color: #2c86f8;
}
.childSelect {
  color: #fff;
  background: #2c86f8;
}
.more {
  float: right;
  color: #2c86f8;
  cursor: pointer;
}
ul {
  margin: 0;
  padding: 0;
  width: 600px;
  display: flex;
  justify-content: center;

  li {
    //   position: relative;
    //   float: left;
    margin-right: 12px;
    border: 1px solid #dddbdb;
    padding: 2px 6px;
    border-radius: 5px;
  }
  li:hover {
    cursor: pointer;
  }
  // li::after {
  //   content:"";
  //   display:block;
  //   visibility:hidden;
  //   clear:both;
  // }
}

.child {
  padding: 6px 16px;
  background: #f9f9f9;
}

ul::after {
  content: "";
  display: block;
  visibility: hidden;
  clear: both;
}

.clear {
  clear: both;
  content: "";
}

.sanjiao {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -3px;
  width: 0;
  height: 0;
  border-top: 0px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #f9f9f9;
  border-left: 10px solid transparent;
}
</style>
