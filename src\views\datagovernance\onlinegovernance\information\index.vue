<template>
  <div class="equipmentlibrary auto-fill">
    <!--    <slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"></slide-unit-tree>-->
    <div class="search-module">
      <ui-label class="inline mr-lg mb-lg" label="组织机构">
        <api-organization-tree
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectOrgCode"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="关键词">
        <Input v-model="searchData.keyWord" class="keyword-input" placeholder="请输入设备名称/设备编码/IP/MAC"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbgnlx">
        <Select
          class="width-md"
          v-model="searchData.sbgnlxList"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
        >
          <Option
            v-for="(item, index) in allDicData.sbgnlxTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          ></Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbdwlx">
        <Select
          class="width-md"
          v-model="searchData.sbdwlxList"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(item, index) in allDicData.cameraPointTypeList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="设备重点类型">
        <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
          <Option label="普通设备" :value="0"> </Option>
          <Option label="重点设备" :value="1"> </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="填报状态">
        <Select class="width-md" v-model="searchData.status" clearable placeholder="请选择填报状态">
          <Option label="未填报" :value="0"> </Option>
          <Option label="已填报" :value="1"> </Option>
          <Option label="填报通过" :value="2"> </Option>
          <Option label="填报异常" :value="3"> </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="检测状态">
        <Select
          @on-change="choseStuses"
          class="width-md"
          v-model="searchData.checkStatuses"
          multiple
          placeholder="请选择检测状态"
          clearable
          :max-tag-count="1"
        >
          <Option
            v-for="(statuItem, index) in allDicData['check_status']"
            :key="'status' + index"
            :value="statuItem.dataKey"
            >{{ statuItem.dataValue }}</Option
          >
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="异常类型" v-if="searchData.checkStatuses.indexOf('3') != -1">
        <Select
          @on-change="choseType"
          class="width-md"
          v-model="searchData.errorCategory"
          placeholder="请选择异常类型"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in global.errorTypeList" :key="index" :value="item.value">{{
            item.label
          }}</Option>
        </Select>
      </ui-label>
      <ui-label
        class="inline mr-lg mb-lg"
        label="错误类别"
        v-if="searchData.errorCategory && searchData.checkStatuses.indexOf('3') != -1"
      >
        <Select
          class="width-md"
          v-model="searchData.errorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="key" v-for="(item, key, index) in messageList" :key="index">{{ item }}</Option>
        </Select>
      </ui-label>
      <div class="inline mb-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
      <div class="fr mb-lg">
        <Upload
          action="/ivdg-asset-app/device/importDevice"
          ref="upload"
          class="inline fr"
          :show-upload-list="false"
          :headers="headers"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
        >
          <Button type="primary" class="ml-sm" :loading="importLoading">
            <i class="icon-font icon-daoruwentishebei f-12"></i>
            <span class="vt-middle ml-sm">导入设备</span>
          </Button>
        </Upload>
        <Button type="primary" class="ml-sm" @click="exportModule">
          <i class="icon-font icon-daochu f-14"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
        <Button class="ml-sm" type="primary" @click="synchroModalShow"
          ><i class="icon-font f-16 icon-xinxizidongtongbu"></i
          ><span class="vt-middle ml-sm">信息自动同步</span></Button
        >
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <template #status="{ row }">
          <div :class="row.rowClass">
            <span
              class="check-status"
              :class="[
                row.status === '0' ? 'bg-color-grey' : '',
                row.status === '1' ? 'bg-other' : '',
                row.status === '2' ? 'bg-success' : '',
                row.status === '3' ? 'bg-failed' : '',
              ]"
            >
              {{ handleCheckStatus(row.status) }}
            </span>
          </div>
        </template>
        <!-- <template #address="{ row }">
          <div class="ellipsis" :title="row.address">
            {{ row.address }}
          </div>
        </template> -->
        <!-- <template #errorMessages="{ row }">
          <div class="ellipsis" :title="row.errorMessages">
            {{ row.errorMessages }}
          </div>
        </template> -->
        <template #action="{ row }">
          <div :class="row.rowClass">
            <ui-btn-tip
              class="mr-md"
              icon="icon-tianbao"
              content="填报"
              @click.native="deviceModalShow(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              icon="icon-shebeidangan"
              content="设备档案"
              @click.native="deviceArchives(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              icon="icon-xinxitongbu"
              content="信息同步"
              @click.native="synchroResultModalShow(row)"
            ></ui-btn-tip>
            <OperationBtnMore :btnList="btnList" :rowData="row" @handleOperations="handleOperations"></OperationBtnMore>
            <!-- <ui-btn-tip
              class="mr-md"
              icon="icon-zhongsheshizhong"
              @click.native="subtitleReset(row)"
              content="重设字幕"
            ></ui-btn-tip>
            <ui-btn-tip
              :styles="{ color: 'rgb(196, 86, 200)' }"
              icon="icon-zhongsheshizhong"
              content="重设时钟"
              @click.native="subtitleResetTime(row)"
            ></ui-btn-tip> -->
            <!-- <Button type="text" class="mr-lg" @click="deviceModalShow(row)">填报</Button> -->
            <!-- <Button type="text" class="mr-lg" @click="deviceArchives(row)">设备档案</Button> -->
            <!-- <Button type="text">填报记录</Button> -->
            <!-- <Button type="text" @click="synchroResultModalShow(row)">信息同步</Button> -->
          </div>
        </template>
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <device-detail
      v-model="deviceDetailShow"
      :choosed-org="choosedOrg"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :fetch-url="fetchUrl"
      @update="init"
    >
    </device-detail>
    <info-synchro-result ref="InfoSynchroResult"></info-synchro-result>
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
          <Button
            type="primary"
            class="plr-30"
            @click="exportExcel(tagList, 'module')"
            :loading="exportModuleLoading"
            >{{ exportModuleLoading ? '下载中' : '模板导出' }}</Button
          >
        </Tooltip>
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading">{{
            exportDataLoading ? '下载中' : '数据导出'
          }}</Button>
        </Tooltip>
      </template>
    </customize-filter>
    <upload-error v-model="uploadErrorVisible" :error-data="errorData" :error-columns="errorColumns"></upload-error>
    <!-- 重设字幕 -->
    <SubtitleReset ref="subtitleReset" />
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import user from '@/config/api/user';
import downLoadTips from '@/mixins/download-tips';
import { mapGetters } from 'vuex';
export default {
  name: 'information',
  mixins: [downLoadTips],
  props: {},
  data() {
    return {
      btnList: [
        { name: '重设字幕', funct: 'subtitleReset' },
        { name: '重设时钟', funct: 'subtitleResetTime' },
      ],
      loading: false,
      // selectKey: '0',
      // checkStatuses: [],
      exportDataLoading: false,
      exportModuleLoading: false,
      messageList: [],
      searchData: {
        orgCode: '',
        keyWord: '',
        sbgnlxList: [],
        sbdwlxList: [],
        checkStatuses: ['3'],
        errorCategory: 'BASICS',
        errorMessageList: [],
        isImportant: '',
        status: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      choosedOrg: {},
      tableData: [],
      checkedData: [],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          minWidth: 200,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 110,
          title: `${this.global.filedEnum.sbdwlx}`,
          key: 'sbdwlxText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 180,
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          align: 'left',
          tooltip: true,
        },
        {
          width: 80,
          title: `${this.global.filedEnum.phyStatus}`,
          key: 'phyStatusText',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 180,
          title: '安装地址',
          key: 'address',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 200,
          title: '异常原因',
          key: 'errorMessages',
          align: 'left',
          tooltip: true,
          // slot: 'errorMessages',
        },
        { width: 100, title: '填报状态', slot: 'status', align: 'left' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      deviceDetailShow: false,
      deviceDetailTitle: '设备信息填报',
      deviceDetailAction: 'edit',
      viewDeviceId: 0,
      deviceCode: '',
      allDicData: {},
      dicDataEnum: Object.freeze({
        sxjgnlx_receive: 'sbgnlxTypeList',
        propertySearch_sbdwlx: 'cameraPointTypeList',
        check_status: 'check_status',
      }),
      fetchUrl: equipmentassets.updateDeviceInfo,
      customFilter: false,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: '3.125rem',
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      uploadErrorVisible: false,
      errorData: [],
      errorColumns: [],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
    };
  },
  created() {
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.getPropertyList();
    // this.selectKey = this.defaultSelectedOrg.orgCode
    this.dictTypeListGroupByType();
    this.init();
    this.choseType('BASICS');
  },
  methods: {
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      this.choosedOrg = data;
      // this.search()
    },
    async dictTypeListGroupByType() {
      try {
        let { data } = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        const dataObject = data.data;
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = dataObject.find((row) => {
            return !!row[key];
          });
          this.$set(this.allDicData, this.dicDataEnum[key], obj[key]);
        });
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.reportPageList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.resetSearchDataMx(this.searchData, this.search);
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    handleCheckStatus(row) {
      const flag = {
        0: '未填报',
        1: '已填报',
        2: '填报通过',
        3: '填报异常',
      };
      return flag[row];
    },
    deviceModalShow(row) {
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    synchroModalShow() {
      this.$router.push({
        name: 'governanceautomatic',
        params: {
          governanceContent: '2',
        },
      });
    },
    synchroResultModalShow(row) {
      this.$refs.InfoSynchroResult.init(row);
    },
    handleOperations(obj) {
      this[obj.methods](obj.data);
    },
    // 字幕重设
    subtitleReset(row) {
      this.$refs.subtitleReset.init(row);
    },
    // 时钟重设
    subtitleResetTime(row) {
      this.$UiConfirm({
        content: '您是否要重设时钟?',
        title: '提示',
      })
        .then(() => {
          this.$http
            .get(equipmentassets.resetOsdTime, {
              params: { device_id: row.deviceId },
            })
            .then((res) => {
              if (res.data.code == 200) {
                this.$Message.success(res.data.data);
              }
            });
        })
        .catch((res) => {
          console.log(res);
        });
    },
    exportModule() {
      this.customFilter = true;
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
          ids: this.checkedData.map((item) => item.id),
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(equipmentassets.exportDevice, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.defaultCheckedList = [
          'deviceId',
          'id',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.msg);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.search();
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    dealError(error) {
      this.errorData = JSON.parse(error);
      this.uploadErrorVisible = true;
      this.errorColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'left',
        },
        {
          title: '设备编码',
          key: 'deviceId',
          align: 'left',
        },
        {
          title: '错误原因',
          key: 'errorReason',
          align: 'left',
        },
      ];
    },
    choseStuses(val) {
      //清空
      if (val.indexOf('3') === -1) {
        this.searchData.errorCategory = '';
        this.searchData.errorMessageList = [];
      }
    },
    async choseType(val) {
      try {
        this.searchData.errorMessageList = [];
        let res = await this.$http.get(equipmentassets.queryErrorReason, {
          params: { errorType: val },
        });
        if (val === 'BASICS') {
          this.messageList = res.data.data;
        } else if (val === 'IMAGE') {
          this.messageList = res.data.data;
        } else if (val === 'VIDEO') {
          this.messageList = res.data.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      checkStatus: 'algorithm/check_status', // 检测状态
    }),
    isRepeatRecord() {
      return this.activeKey === 5;
    },
  },
  components: {
    // SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    InfoSynchroResult: require('./info-synchro-result.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    UploadError: require('./components/upload-error.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    SubtitleReset: require('./subtitle-reset.vue').default,
    OperationBtnMore: require('@/components/operation-btn-more.vue').default,
  },
};
</script>
<style lang="less" scoped>
.equipmentlibrary {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .search-module {
    padding: 20px 20px 0;
    .keyword-input {
      width: 300px;
    }
  }
  .select-tabs {
    padding: 0 20px;
    margin-bottom: 20px;
  }
  .ui-table {
    padding: 0 20px;
  }
}
</style>
