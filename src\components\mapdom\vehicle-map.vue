<template>
  <div class="vehicle-info">
    <div class="vehicle-info-left" style="width: 200px">
      <div class="smallImg" style="width: 200px; height: inherit">
        <img :src="vehicleInfo.traitImg" alt="" />
        <!-- <div class="plateNum" style="width: 198px;">
                    <span class="license-plate-small">{{ vehicleInfo.plateNo }}</span>
                </div> -->
        <ui-plate-number
          :plateNo="vehicleInfo.plateNo"
          :color="vehicleInfo.plateColor"
          size="medium"
        ></ui-plate-number>
      </div>
      <!-- <span class="similarity" v-if="vehicleInfo.similarity"
        >{{ vehicleInfo.similarity }}%</span
      > -->
      <div class="title">
        <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)"
          >通行记录</span
        >
        <!-- 没有车牌号时不展示档案 -->
        <span
          v-show="vehicleInfo.plateNo"
          :class="{ active: checkIndex == 1 }"
          @click="tabsChange(1)"
          >车辆档案</span
        >
      </div>
      <div class="traffic-record" v-if="checkStatus">
        <div class="dom-content-p">
          <span class="label">通过地点</span><span>：</span>
          <ui-textOver-tips
            class="message"
            refName="deviceName"
            :content="vehicleInfo.deviceName || vehicleInfo.captureAddress"
          ></ui-textOver-tips>
        </div>
        <div class="dom-content-p">
          <span class="label">通过时间</span><span>：</span>
          <span class="message">{{
            vehicleInfo.absTime || vehicleInfo.captureTime || "--"
          }}</span>
        </div>
        <structuredmsg
          :info="vehicleInfo.vehicleStructuralVo || vehicleInfo"
          type="2"
        ></structuredmsg>
      </div>
      <div class="personnel-files" v-else>
        <div class="dom-content-p">
          <span class="label">车牌号码</span><span>：</span>
          <span
            class="address"
            :class="{ plateNo: vehicleInfo.plateNo }"
            @click="toDetail(vehicleInfo)"
            >{{ vehicleInfo.plateNo || "--" }}</span
          >
        </div>
        <div class="dom-content-p">
          <span class="label">车辆类型</span><span>：</span>
          <span class="message">
            {{ vehicleArchives.vehicleType | commonFiltering(vehicleTypeList) }}
          </span>
        </div>
        <div class="dom-content-p">
          <span class="label">车辆品牌</span><span>：</span>
          <span class="message"
            >{{
              vehicleArchives.vehicleBrand | commonFiltering(vehicleBrandList)
            }}
          </span>
        </div>
        <div class="dom-content-p">
          <span class="label">机动车主</span><span>：</span>
          <span class="message">{{ vehicleArchives.motorists || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">身份证号</span><span>：</span>
          <span class="address">{{ vehicleArchives.idcardNo || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">联系电话</span><span>：</span>
          <span class="message">{{ vehicleArchives.phone || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">登记地址</span><span>：</span>
          <span class="address">{{ vehicleArchives.djdz || "--" }}</span>
        </div>
      </div>
    </div>
    <div class="vehicle-info-right common-info-right">
      <!-- <div class="right-header">
                <div class="tablist">
                    <div class="title">
                        <span>场景大图</span>
                    </div>
                </div>
                <operate-bar :list='vehicleInfo' :tabType="{'type': 6}" @collection="collection($event)"></operate-bar>
            </div> -->
      <!-- <div class="right-content"> -->
      <!-- <ui-image :src="vehicleInfo.sceneImg" alt="静态库" viewer /> -->
      <!-- </div> -->
      <div class="fun-img" id="fun-img">
        <img
          id="imgBox"
          :src="vehicleInfo.sceneImg"
          @load="loadImage($event, vehicleInfo.vehicleRect)"
          alt=""
        />
        <div
          class="select-preview"
          :style="{
            left: imgBoxList.x + 'px',
            top: imgBoxList.y + 'px',
            width: imgBoxList.width + 'px',
            height: imgBoxList.height + 'px',
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import { getCarInfoByCarNo } from "@/api/operationsOnTheMap";
import operateBar from "./operate-bar.vue";
import structuredmsg from "./structuredmsg.vue";
import { myMixins } from "@/views/operations-on-the-map/map-default-page/components/mixins/index.js";
export default {
  name: "",

  components: {
    operateBar,
    structuredmsg,
  },
  mixins: [myMixins], //全局的mixin
  props: {
    // 通行记录数据
    vehicleInfo: {
      type: Object,
      default: () => {},
    },
    // 抓拍记录，人员档案tab切换
    // checkStatus: {
    //     type: Boolean,
    //     default: true
    // }
  },
  data() {
    return {
      vehicleArchives: {},
      checkStatus: true, // 抓拍记录，人员档案tab切换
      checkIndex: 0,
    };
  },
  watch: {
    getCollectJudge: {
      handler(val) {
        this.$set(this.vehicleInfo, "myFavorite", val);
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getCollectJudge: "map/getCollectJudge",
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations({ setCollectJudge: "map/setCollectJudge" }),
    init() {
      this.checkStatus = true;
      this.checkIndex = 0;
      this.vehicleArchives = {};
    },
    async tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
      this.checkStatus = this.checkIndex == 0 ? true : false;
      if (!this.checkStatus) {
        if (!this.vehicleInfo.plateNo) {
          this.$Message.warning("档案不存在！");
          return;
        }
        try {
          let res = await getCarInfoByCarNo({
            // 这里需要携带车牌颜色
            carNo: this.vehicleInfo.plateNo + "_" + this.vehicleInfo.plateColor,
          });
          if (res.code === 200 && !!res.data) {
            this.vehicleArchives = res.data || {};
          } else {
            throw error;
          }
        } catch (error) {
          this.$Message.warning("档案暂无数据");
        }
      }
    },
    // 收藏/取消收藏
    collection($event, item) {
      this.vehicleInfo.myFavorite = $event;
      this.setCollectJudge($event);
    },
    toDetail(item) {
      if (!item.plateNo) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query: {
          // archiveNo: JSON.stringify(this.vehicleArchives.archiveNo),
          archiveNo: JSON.stringify(item.plateNo + "_" + item.plateColor),
          plateNo: JSON.stringify(this.vehicleArchives.plateNo || item.plateNo),
          source: "car",
          idcardNo: this.vehicleArchives.idcardNo || item.idcardNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.vehicle-info {
  margin-top: 7px;
  display: flex;
  &-left {
    width: 200px;
    height: 200px;
    position: relative;
    .smallImg {
      width: 200px;
      height: 200px;
      border: 1px solid #d3d7de;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      background: #f9f9f9;
      position: relative;
      > img {
        // width: 100%;
        // height: 100%;
        max-width: 100%;
        max-height: 100%;
      }
      .plateNum {
        position: absolute;
        height: 40px;
        width: 100%;
        background: rgba(0, 0, 0, 0.7);
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          font-size: 16px;
          padding: 8px 12px;
        }
      }
    }
    .traffic-record {
      margin-top: 10px;
    }
    .personnel-files {
      margin-top: 10px;
      height: 120px;
      overflow-y: auto;
      .complete-file {
        height: 18px;
        line-height: 18px;
        margin: 15px 0 17px 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        > span {
          margin-left: 5px;
        }
      }
    }
  }
  &-right {
    // width: 100%;
    // height: 412px;
    width: calc(~"100% - 220px");
    min-height: 360px;
    max-height: 400px;
    border: 1px solid #d3d7de;
    background: #f9f9f9;
    position: relative;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tablist {
        height: 28px;
        line-height: 28px;
        width: 400px !important;
        margin: 0;
        display: flex;
        align-items: center;
        .title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.9);
          padding-left: 9px;
          position: relative;
          height: 20px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          > span {
            font-weight: bold;
          }
        }
        .title:before {
          content: "";
          position: absolute;
          width: 3px;
          height: 16px;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          background: #2c86f8;
        }
        .ivu-tabs-tab {
          float: left;
          border: 1px solid #2c86f8;
          border-right: none;
          padding: 0 15px;
          color: #2c86f8;
          &:hover {
            background: #2c86f8;
            color: #ffffff;
          }
          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            border-right: none;
          }
          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            border-right: 1px solid #2c86f8;
          }
        }
        .active {
          background: #2c86f8;
          color: #fff;
        }
      }
    }
    .right-content {
      margin-top: 6px;
      max-width: 580px;
      height: 370px;
      border: 1px solid #d3d7de;
      background: #f9f9f9;
      .complete-face {
        width: 580px;
        height: 430px;
        position: relative;
        text-align: center;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9f9f9;
        > img {
          width: 100%;
          height: auto;
          max-height: 430px;
          // max-width: 580px;
        }

        > span {
          display: inline-block;
          width: 100%;
          height: 30px;
          line-height: 30px;
          position: absolute;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          .iconfont {
            color: #fff !important;
            padding: 0 12px;
            cursor: pointer;
          }
        }
      }
      .video {
        /deep/.easy-player {
          margin-top: 60px;
        }
      }
    }
    margin-left: 15px;
  }
}
</style>
