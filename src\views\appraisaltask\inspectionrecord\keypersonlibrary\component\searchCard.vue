<template>
  <div class="base-search">
    <ui-label class="fl" label="姓名" :width="40">
      <Input v-model="searchData.name" class="width-md" placeholder="请输入姓名"></Input>
    </ui-label>
    <ui-label class="fl ml-lg" label="证件号" :width="55">
      <Input v-model="searchData.idCard" class="width-md" placeholder="请输入证件号"></Input>
    </ui-label>
    <!--    <ui-label class="fl  ml-lg mb10" label="组织机构" :width="70">-->
    <!--      <ApiOrganizationTree-->
    <!--        style="width: auto!important;"-->
    <!--        :treeData="treeData"-->
    <!--        :taskObj="taskObj"-->
    <!--        :select-tree="searchData"-->
    <!--        placeholder="请选择组织机构"-->
    <!--      >-->
    <!--      </ApiOrganizationTree>-->
    <!--    </ui-label>-->
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      searchData: {
        idCard: '',
        name: '',
        // orgCode: '',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        name: '',
        idCard: '',
        orgCode: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {},
  components: {
    // ApiOrganizationTree: require('../../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  padding-bottom: 16px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
.mt4 {
  margin-top: 4px;
}
.exportBtn {
  float: right;
  margin-top: 3px;

  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
.width-md {
  width: 150px !important;
}
</style>
