<template>
  <div class="button-group">
    <div>
      <i class="icon-font icon-suoxiao" @click="pointMap('narrow')"></i>
    </div>
    <div>
      <i class="icon-font icon-fangda1" @click="pointMap('enlarge')"></i>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    pointMap(type) {
      this.$emit('pointMap', type);
    },
  },
};
</script>
<style lang="less" scoped>
.button-group {
  position: absolute;
  right: 30px;
  top: 382px;
  > div {
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    // border: 1px solid #2967c8;
    box-shadow: 0px 0px 5px -1px rgba(147, 171, 206, 1);

    .icon-font {
      color: var(--color-primary);
      font-size: 18px;
    }
  }
  > div:first-child {
    margin-bottom: 10px;
  }
  > div:last-child {
    border-top: none;
  }
}
</style>
