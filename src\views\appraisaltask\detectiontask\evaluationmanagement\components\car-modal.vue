<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" class="confimDataModal" @onCancel="closed" footer-hide>
    <div class="left-content">
      <ui-tree @clickNode="clickNode" :nodeData="nodeData" @idx="idx" ref="childNode"></ui-tree>
    </div>
    <div class="center-content"></div>
    <div
      class="right-content ml-sm"
      v-if="deviceListData.length > 0 || deviceResult != '' || deviceCode != '' || deviceName != ''"
    >
      <div class="times">
        <span>指标名称： {{ this.title_tab }}</span>
        <span>测评时间：{{ this.$parent.row.examineTime }}</span>
        <span>抽样设备数：{{ this.device.totalDeviceNum }}</span>
        <Button type="primary" class="export" @click="exportFn" :loading="exportLoading">
          <span v-if="!exportLoading" class="ent">导出</span>
          <span v-else class="ent">Loading...</span>
        </Button>
      </div>
      <div class="echarts-title">
        <i></i>
        <div class="titles">检测结果统计</div>
      </div>
      <div class="chart-box">
        <div class="chart">
          <PipEchart :deviceObj="deviceObj"></PipEchart>
        </div>
        <div class="line"></div>
        <div class="equip_num">
          <div class="p">
            <i></i>
            <span class="tips" :title="this.$parent.row.indexName"> {{ this.$parent.row.indexName }}： </span>
            <span>{{ this.$parent.row.resultValue.toFixed(2) }}%</span>
            <img :title="this.$parent.row.calculateMethod" src="@/assets/img/car-modal/icon1.png" alt="" />
          </div>
          <div class="p">
            <i></i>
            <span class="img"> 达标值：{{ this.$parent.row.standardsValue }}% </span>
          </div>
        </div>
        <div class="result">
          <div class="p">
            <i></i>
            <span>考核结果：</span>
          </div>
          <img
            src="@/assets/img/car-modal/qualified1.png"
            alt=""
            v-if="deviceObj.qualified >= this.$parent.row.standardsValue"
          />
          <img src="@/assets/img/car-modal/qualified2.png" alt="" v-else />
        </div>
      </div>
      <!-- <div class="statistics">
        <div v-if="id==''">
          <span>抽样设备数：</span>
          <span class="font-E7E9ED">{{this.device.totalDeviceNum}}</span>
        </div>
        <div v-if="id==''">
          <span>评测总数：</span>
          <span class="font-E7E9ED">{{this.device.total}}</span>
        </div>
        <div>
          <span>合格数：</span>
          <span class="font-success ">{{this.device.qualifiedNum}}</span>
        </div>
        <div>
          <span>不合格数：</span>
          <span class="color-failed">{{this.device.unqualifiedNum}}</span>
        </div>
        <div v-if="id==''">
          <span>{{this.$parent.row.name}}：</span>
          <span class='color-failed'>{{this.device.dataPassRate}}%</span>
        </div>
        <div v-if="id!=''">
          <span>设备合格率: </span>
          <span class="color-failed">  {{this.device.dataPassRate}}%</span>
        </div>
      </div> -->
      <div class="echarts-title">
        <i></i>
        <div class="titles">检测数据列表</div>
      </div>
      <div class="search-wrapper">
        <ui-label class="fl" :label="global.filedEnum.deviceId" :width="70">
          <Input class="input-width" v-model="deviceCode" :placeholder="`请输入${global.filedEnum.deviceId}`"></Input>
        </ui-label>
        <ui-label class="fl ml-lg" :label="global.filedEnum.deviceName" :width="70">
          <Input class="input-width" :placeholder="`请输入${global.filedEnum.deviceName}`" v-model="deviceName"></Input>
        </ui-label>
        <ui-label class="fl ml-lg" label="检查结果" :width="70">
          <Select v-model="deviceResult" clearable placeholder="请选择" class="width-input">
            <Option value="1">合格</Option>
            <Option value="2">不合格</Option>
            <Option value="3">无法考核</Option>
          </Select>
          <!-- <Input class="input-width" placeholder="请输入检查结果" v-model="deviceResult"></Input> -->
        </ui-label>
        <ui-label :width="30" class="fl" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button type="default" class="mr-sm" @click="reast"> 重置 </Button>
        </ui-label>
      </div>
      <div class="table-box mt-sm">
        <ui-table
          v-if="isShow"
          :minus-height="minusTable"
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="deviceListData"
          :loading="loading_tab"
        >
          <template #resultDesc="{ row }">
            <span
              :class="{
                color_qualified: row.description == '合格',
                color_unqualified: row.description == '不合格',
                color_cannot: row.description == '无法考核',
              }"
            >
              {{ row.description }}
            </span>
          </template>
          <template #reason="{ row }">
            <span style="color: red">{{ row.reason }}</span>
          </template>
          <template #img="{ row }">
            <img
              v-if="row.imageUrl != null"
              :src="row.imageUrl"
              alt=""
              style="width: 100px; height: 75px"
              @click="view(row)"
            />
            <img v-else src="@/assets/img/load-error-img.png" alt="" style="width: 100px; height: 75px" />
          </template>
          <template #option="{ row }">
            <Poptip placement="top" transfer>
              <div class="api" slot="content">
                <el-table :data="funListData" style="width: 100%">
                  <el-table-column
                    v-for="(item, index) in funTh"
                    :key="index"
                    :prop="item.key"
                    :label="item.title"
                  ></el-table-column>
                </el-table>
              </div>
              <i class="icon-font icon-zidingyizhilifangan" title="查看" @click="viewAppraisalResult(row)">
                <!-- <img src="@/assets/img/car-modal/detial.png" alt="" >
                   -->
                <!-- <img src="@/assets/img/car-modal/detial2.png" alt="" v-else> -->
              </i>
            </Poptip>
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
      </div>
      <loading v-if="loading"></loading>
    </div>
    <div class="no-data no-data_str" v-else>
      <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt />
      <img v-else src="@/assets/img/common/nodata-light.png" alt />
    </div>
    <template slot="footer">
      <el-button type="primary" size="small" @click="handleSubmit()" class="plr-30"> 确 定 </el-button>
      <el-button plain type="primary" size="small" @click="visible = false" class="plr-30"> 取 消 </el-button>
    </template>
    <viewer class="viewer" ref="viewer" style="display: none" :navbar="false" @inited="inited" :images="images">
      <template slot-scope="scope">
        <img v-for="(item, index) in images" :src="item" :key="item + index" />
      </template>
    </viewer>
  </ui-modal>
</template>

<script>
import examine from '@/config/api/governanceevaluation';
import algorithm from '@/config/api/algorithm';
import { mapActions, mapGetters } from 'vuex';
export default {
  // props: {
  //   treeData:{
  //     type: Object,
  //     default: {}
  //   },
  //   value: {},
  //   modalAction: {},
  //   modalData: {},
  // },
  props: ['treeData', 'value'],
  data() {
    return {
      title_tab: '',
      images: [require('@/assets/img/navigation-page/systemmanagement.png')], // 初次给个默认值，不会影响图片展示，用于清除大图展示下方list
      tableColumns: [{ slot: 'resultDesc' }, { slot: 'img' }, { slot: 'option' }, { slot: 'reason' }],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      },
      styles: {
        width: '8rem',
      },
      minusTable: 500,
      visible: false,
      loading_tab: false,
      loading: false,
      detailList: [
        {
          value1: '字段1',
          vaule: 1,
        },
      ],
      nodeData: [
        {
          label: '一级 1',
          children: [
            {
              label: '二级 1-1',
            },
          ],
        },
      ],
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      device: [],
      deviceObj: {}, //设备数据
      id: '', //树形图id
      deviceListData: [],
      // type_id: '', //合格参数
      funTh: [], //算法详情
      funListData: [], //算法列表数据
      showFun: false,
      deviceCode: '',
      deviceName: '',
      deviceResult: '',
      vehicleList: [],
      cartypeList: [],
      colorList: [],
      isShow: true,
      exportLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
      themeType: 'common/getThemeType',
    }),
  },
  methods: {
    // 处理大图小图展示事件
    inited(viewer) {
      this.$viewer = viewer;
    },
    view(row) {
      this.images = [row.imageUrl];
      this.$nextTick(() => {
        this.$viewer.view(0);
      });
    },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    handleSubmit() {},
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.params.pageNumber = val;
      if (this.id != '') {
        this.pageList(this.id, this.deviceResult, this.deviceCode, this.deviceName);
      } else {
        this.devicePageList(
          this.$parent.row.indexId,
          this.$parent.row.resultId,
          this.deviceResult,
          this.deviceCode,
          this.deviceName,
        );
      }
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      if (this.id != '') {
        this.pageList(this.id, this.deviceResult, this.deviceCode, this.deviceName);
      } else {
        this.devicePageList(
          this.$parent.row.indexId,
          this.$parent.row.resultId,
          this.deviceResult,
          this.deviceCode,
          this.deviceName,
        );
      }
    },
    // 关闭弹窗
    closed() {
      this.$refs.childNode.qualified = '0';
    },
    // getDetailInfo() {
    //   this.detailList = [
    //     {
    //       value1: "字段1",
    //     },
    //   ];
    // },
    // 点击节点设备
    clickNode(val) {
      this.deviceCode = '';
      this.deviceName = '';
      this.showFun = false;
      // 存储树形图id
      this.id = val.id;
      this.deviceData(this.$parent.row.indexId, this.$parent.row.resultId, val.id);
      if (val.id) {
        //子节点
        this.searchData.params.pageNumber = 1;
        this.pageData.pageNum = 1;
        this.tableTh(1, this.$parent.row.indexId);
        this.pageList(this.id, this.deviceResult, this.deviceCode, this.deviceName);
        // this.devicePageList(this.$parent.row.indexId, this.$parent.row.resultId)
      } else {
        //根节点
        this.$nextTick(() => {
          this.id = '';
          this.deviceResult = '';
          this.tableTh(3);
          this.searchData.params.pageNumber = 1;
          this.pageData.pageNum = 1;
          this.devicePageList(this.$parent.row.indexId, this.$parent.row.resultId);
          this.$refs.childNode.qualified = '0';
        });
      }
    },
    // 设备合格选项
    idx(val) {
      // 清空子设备节点
      this.id = '';
      this.showFun = false;
      this.deviceResult = val;
      // 掉用父组件方法
      this.$emit('idx', val);
      // this.type_id = val
      this.tableTh(3);
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.deviceCode = '';
      this.deviceName = '';
      // this.deviceResult = ''
      // this.deviceResult = ''
      this.devicePageList(this.$parent.row.indexId, this.$parent.row.resultId, val);
    },
    // 设备数据详情接口
    deviceData(indexId, resultId, id) {
      let data = {};
      data.indexId = indexId;
      data.resultId = resultId;
      data.id = id;
      this.$http.post(examine.deviceResultOverviewCar, data).then((res) => {
        if (res.data.code == 200) {
          this.device = res.data.data;
          this.deviceObj = this.device;
          if (this.id) {
            if (this.deviceObj.totalDeviceNum == 0) {
              this.deviceObj.qualified = 0;
            } else {
              this.deviceObj.qualified = (this.device.qualifiedNum / this.device.totalDeviceNum).toFixed(2) * 100;
            }
          } else {
            this.deviceObj.qualified = this.deviceObj.dataPassRate;
          }
        }
      });
    },
    // 表头接口
    tableTh(type, indexId) {
      this.$http
        .get(examine.getTableHeader, {
          params: { indexId: indexId, type: type },
        })
        .then((res) => {
          if (res.data.code == 200) {
            let a = res.data.data.map((item) => {
              return { title: item.fieldName, key: item.field };
            });
            // if(a[a.length - 1].title == '算法详情') {
            //   a[a.length - 1].slot = 'option'
            //   delete a[a.length - 1].key
            // }
            //循环列表
            a.forEach((item) => {
              if (item.title == '检测结果') {
                item.slot = 'resultDesc';
                delete item.key;
              }
              if (item.title == '图片') {
                item.slot = 'img';
                delete item.key;
              }
              if (item.title == '算法详情') {
                item.slot = 'option';
                delete item.key;
              }
              if (item.title == '不合格原因') {
                item.slot = 'reason';
                delete item.key;
              }
            });
            this.isShow = false;
            this.$nextTick(() => {
              this.isShow = true;
            });
            if (type == 2) {
              this.funTh = a;
            } else {
              this.tableColumns = a;
            }
          }
        });
    },
    // 初始化列表接口
    devicePageList(indexId, resultId, outcome, deviceId, deviceName) {
      this.loading_tab = true;
      this.searchData.indexId = indexId;
      this.searchData.resultId = resultId;
      this.searchData.outcome = outcome;
      this.searchData.deviceId = deviceId;
      this.searchData.deviceName = deviceName;
      this.$http.post(examine.devicePageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.deviceListData = res.data.data.entities;
          this.deviceListData.forEach((item) => {
            if (item.plateColor) {
              item.plateColor = this.carList(item.plateColor, this.colorList);
            }
            if (item.vehicleColor) {
              item.vehicleColor = this.carList(item.vehicleColor, this.colorList);
            }
            // 车辆品牌字典转换
            if (item.vehicleBrand) {
              item.vehicleBrand = this.carList(item.vehicleBrand, this.cartypeList);
            }
            // 车辆类型
            if (item.vehicleClass) {
              item.vehicleClass = this.carList(item.vehicleClass, this.vehicleList);
            }
          });
          this.pageData.totalCount = res.data.data.total;
        }
        this.loading_tab = false;
        this.loading = false;
      });
    },
    // 选择某个具体设备列表接口
    pageList(evaluationDeviceDetailId, outcome, deviceId, deviceName) {
      this.tableTh(2, this.$parent.row.indexId);
      this.loading_tab = true;
      this.searchData.evaluationDeviceDetailId = evaluationDeviceDetailId;
      this.searchData.outcome = outcome;
      this.searchData.deviceId = deviceId;
      this.searchData.deviceName = deviceName;
      this.$http.post(examine.pageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.deviceListData = res.data.data.entities;
          this.deviceListData.forEach((item) => {
            if (item.plateColor) {
              item.plateColor = this.carList(item.plateColor, this.colorList);
            }
            if (item.vehicleColor) {
              item.vehicleColor = this.carList(item.vehicleColor, this.colorList);
            }
            // 车辆品牌字典转换
            if (item.vehicleBrand) {
              item.vehicleBrand = this.carList(item.vehicleBrand, this.cartypeList);
            }
            // 车辆类型
            if (item.vehicleClass) {
              item.vehicleClass = this.carList(item.vehicleClass, this.vehicleList);
            }
          });
          this.pageData.totalCount = res.data.data.total;
        }
        this.loading = false;
        this.loading_tab = false;
      });
    },
    // 算法列表接口
    funList(id) {
      let a = new FormData();
      a.append('vehicleDetailId', id);
      this.$http.post(examine.getListByVehicleDetailId, a).then((res) => {
        if (res.data.code == 200) {
          this.funListData = res.data.data;
          this.funListData.forEach((item) => {
            item.algorithmTitle = this.getAlgorithmLabel(item.algorithmTitle); //厂商名称
            item.algorithmPlateColor = this.carList(item.algorithmPlateColor, this.colorList); //车牌颜色
            item.algorithmVehicleColor = this.carList(item.algorithmVehicleColor, this.colorList); //车辆颜色
            item.algorithmVehicleClass = this.carList(item.algorithmVehicleClass, this.vehicleList); //类型
            item.algorithmVehicleBrand = this.carList(item.algorithmVehicleBrand, this.cartypeList); //品牌
          });
        }
      });
    },
    // 算法详情
    viewAppraisalResult(row) {
      // this.tableTh(2, this.$parent.row.indexId)
      // console.log(row)
      this.showFun = !this.showFun;
      this.funList(row.vehicleDetailId);
    },
    // 厂商转换
    getAlgorithmLabel(val) {
      let a;
      this.algorithmList.forEach((item) => {
        if (val == item.dataKey) {
          a = item.dataValue;
        }
      });
      return a;
    },
    // 车颜色转换
    carList(val, list) {
      let a;
      list.forEach((item) => {
        if (val == item.dictKey) {
          a = item.dictValue;
        }
      });
      return a;
    },
    // // 车类型字典转换
    // carType(val) {
    //   let a;
    //   this.cartypeList.forEach(item => {
    //     if(val == item.dictKey) {
    //       a = item.dictValue
    //     }
    //   })
    //   return a;
    // },
    // // 车类型字典转换
    // carType(val) {
    //   let a;
    //   this.cartypeList.forEach(item => {
    //     if(val == item.dictKey) {
    //       a = item.dictValue
    //     }
    //   })
    //   return a;
    // },
    // 检索
    search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      if (this.id != '') {
        this.pageList(this.id, this.deviceResult, this.deviceCode, this.deviceName);
      } else {
        this.devicePageList(
          this.$parent.row.indexId,
          this.$parent.row.resultId,
          this.deviceResult,
          this.deviceCode,
          this.deviceName,
        );
      }
    },
    // 重置
    reast() {
      this.deviceCode = '';
      this.deviceName = '';
      this.deviceResult = '';
      this.searchData = {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      };
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      //选中子节点重置
      if (this.id != '') {
        this.pageList(this.id, this.deviceResult);
      } else {
        this.devicePageList(
          this.$parent.row.indexId,
          this.$parent.row.resultId,
          this.deviceResult,
          this.deviceCode,
          this.deviceName,
        );
      }
    },
    // 导出
    exportFn() {
      this.exportLoading = true;
      let w;
      if (this.deviceObj.dataPassRate > this.$parent.row.standardsValue) {
        w = '达标';
      } else {
        w = '不达标';
      }
      this.$http
        .post(
          examine.carimportExcel,
          {
            resultId: this.$parent.row.resultId,
            indexName: this.$parent.row.indexName,
            indexId: this.$parent.row.indexId,
            outcome: this.searchData.outcome,
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            upToStandard: w,
          },
          { responseType: 'arraybuffer' },
        )
        .then((res) => {
          if (res.status == 200) {
            this.exportLoading = false;
            let a = document.createElement('a');
            let blob = new Blob([res.data], {
              type: 'application/vnd.ms-excel',
            });
            let objectUrl = URL.createObjectURL(blob);
            a.setAttribute('href', objectUrl);
            let now = new Date(),
              year = now.getFullYear(),
              mon = now.getMonth() + 1,
              day = now.getDate(),
              hours = now.getHours(),
              min = now.getMinutes(),
              sec = now.getSeconds();
            var dataStr =
              '' +
              year +
              (mon < 10 ? '0' + mon : mon) +
              (day < 10 ? '0' + day : day) +
              '-' +
              (hours < 10 ? '0' + hours : hours) +
              (min < 10 ? '0' + min : min) +
              (sec < 10 ? '0' + sec : sec);
            a.setAttribute(
              'download',
              '车辆视图数据 - ' + this.$parent.row.indexName + ' - [iVDG] - [' + dataStr + '].xls',
            );
            a.click();
          } else {
            this.exportLoading = false;
          }
        });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      this.id = '';
      if (val) {
        this.title_tab = this.$parent.row.indexName;
        this.loading = true;
        this.getAlldicData();
        this.deviceData(this.$parent.row.indexId, this.$parent.row.resultId);
        this.tableTh(3);
        this.devicePageList(this.$parent.row.indexId, this.$parent.row.resultId);
        // 车辆颜色字典表
        this.$http.post(algorithm.standard1400, { dictType: 'colorType' }).then((res) => {
          if (res.data.code == 200) {
            this.colorList = res.data.data;
          }
        });
        // 车辆型号字典表
        this.$http.post(algorithm.standard1400, { dictType: 'vehicleBrandType' }).then((res) => {
          if (res.data.code == 200) {
            this.cartypeList = res.data.data;
          }
        });
        // 车辆种类字典表
        this.$http.post(algorithm.standard1400, { dictType: 'vehicleClassType' }).then((res) => {
          if (res.data.code == 200) {
            this.vehicleList = res.data.data;
          }
        });
        this.reast();
      }
    },
    treeData(val) {
      this.nodeData = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiTree: require('@/components/ui-tree.vue').default,
    PipEchart: require('./pip-echart.vue').default,
  },
};
</script>

<style lang="less" scoped>
/deep/.ivu-modal-mask {
  z-index: 1000 !important;
}
/deep/.ivu-modal-wrap {
  z-index: 1000 !important;
}
.icon-xiangqing:before {
  content: '\e6f1';
}
.icon-font {
  font-family: 'icon-font' !important;
  color: #56789c;
}
.icon-font:hover {
  color: var(--color-primary);
}
.confimDataModal {
  .color_qualified {
    color: var(--color-success);
  }
  .color_unqualified {
    color: var(--color-failed);
  }
  .color_cannot {
    color: var(--color-warning);
  }
  @{_deep} .ivu-modal-wrap {
    .ivu-modal .ivu-modal-body {
      display: flex !important;
    }
  }
  .left-content {
    width: 303px;
  }
  .center-content {
    width: 1px;
    background: #1b3b65;
    margin-right: 10px;
  }
  .right-content {
    flex: 1;
    // padding-top: 20px;
    background: var(--bg-content);
    .times {
      margin-bottom: 20px;
      span:first-child {
        margin-right: 30px;
      }
      span:nth-child(2) {
        margin-right: 30px;
      }
      .export {
        float: right;
        position: relative;
        top: -10px;
        .ent {
          margin-right: 0;
        }
      }
    }
    .echarts-title {
      width: 100%;
      display: flex;
      margin-top: -10px;
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      i {
        width: 8px;
        height: 100%;
        background: #239df9;
        margin-right: 6px;
      }
      .titles {
        width: 100%;
        padding-left: 5px;
        background-image: linear-gradient(to right, #0a4f8d, #09284d);
      }
    }
    //  视频流头部
    .chart-box {
      width: 100%;
      display: flex;
      height: 150px;
      align-items: center;
      background-color: var(--bg-sub-content);
      margin-bottom: 20px;
      .chart {
        width: 48%;
      }
      .line {
        width: 1px;
        height: 70%;
        background-color: #0d477d;
      }
      .equip_num {
        height: 100%;
        padding-left: 50px;
        width: 320px;
        padding-top: 45px;
        .p {
          height: 20px;
          margin-bottom: 20px;
          i {
            display: inline-block;
            width: 6px;
            height: 18px;
            background-color: var(--color-primary);
            margin-right: 20px;
          }
          img {
            margin-left: 3px;
          }
          .tips {
            display: inline-block;
            width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            position: relative;
            top: 5px;
          }
        }
      }
      .result {
        height: 100%;
        display: flex;
        padding-top: 45px;
        .p {
          position: relative;
          top: 5px;
          i {
            width: 6px;
            height: 18px;
            background-color: var(--color-primary);
            margin-right: 20px;
          }
        }
        img {
          position: relative;
          top: -15px;
        }
      }
    }
    .statistics {
      color: #8d9cb1;
      padding: 0 12px 0 20px;
      margin-bottom: 20px;
      > div {
        display: inline-block;
        margin-right: 30px;
      }
    }
    .search-wrapper {
      overflow: hidden;
      .input-width {
        width: 176px;
      }
    }
    .table-box {
      overflow: hidden;
      position: relative;
      margin-top: 10px;
      .sucess {
        color: var(--color-success);
      }
      .error {
        color: var(--color-failed);
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .no-data_str {
    transform: translate(-24%, -50%);
  }
}
/deep/.ivu-table-tip {
  table {
    height: 400px;
  }
}
/deep/ .el-table::before {
  content: ' ';
  background-color: #1b3b65;
}
/deep/ .el-table {
  // width: 99.9%;
  tr {
    background-color: #0d3560 !important;
  }
  td,
  th {
    background-color: #0d3560 !important;
    color: #fff;
    border: none;
  }
  .el-table__body-wrapper {
    background-color: #0d3560 !important;
  }
  th {
    font-weight: normal;
  }
  th.gutter {
    display: table-cell !important;
  }
}
</style>
