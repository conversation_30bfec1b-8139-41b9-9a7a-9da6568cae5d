<template>
  <div class="tendency-table-container">
    <div class="flex-row mb-md">
      <Checkbox v-model="isShowRank" @on-change="handleChangeShowRank">隐藏排名</Checkbox>
      <div class="btn-box">
        <slot name="tableButtons" :isShowRank="isShowRank"></slot>
      </div>
    </div>
    <div class="tendency-table">
      <ui-table
        ref="tendencyTable"
        class="ui-table"
        :table-columns="showTableColumns"
        :table-data="showTableData"
        :loading="tableLoading"
        full-border
      >
        <template #civilName="{ row }">
          <span class="span-btn" @click="onClickCivil(row)">
            {{ row.civilName }}
          </span>
        </template>
      </ui-table>
    </div>
  </div>
</template>
<script>
export default {
  name: 'tendencyTable',
  props: {
    tableData: {
      type: Array,
      default: () => [],
    },
    tableLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultTableColumns: [
        {
          title: '行政区划',
          key: 'civilName',
          slot: 'civilName',
          width: 200,
          align: 'center',
          tooltip: true,
          className: 'header-table',
        },
        {
          title: '总得分',
          key: 'score',
          minWidth: 130,
          align: 'center',
          tooltip: true,
          className: 'header-table',
        },
      ],
      showTableColumns: [],
      showTableData: [],
      isShowRank: false, //是否展示排名
    };
  },
  watch: {
    tableData: {
      handler(val) {
        this.showTableData = this.$util.common.deepCopy(val);
        this.setShowTableData(this.showTableData);
        this.setShowTableColumns(this.showTableData);
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    setShowTableColumns(tableData) {
      if (!tableData?.length) {
        return;
      }
      this.showTableColumns = [...this.defaultTableColumns];
      //根据tableData中的内容动态设置表头
      tableData[0].childList.forEach((child) => {
        let monthRow = {
          minWidth: 200,
          align: 'center',
          title: child.month + '月',
          className: 'header-table',
        };
        // 不展示排名直接显示
        if (this.isShowRank) {
          monthRow.key = 'monthScore_' + child.month;
          monthRow.sortable = true;
        }
        //展示排名
        if (!this.isShowRank) {
          monthRow.children = [
            {
              title: '得分',
              key: 'monthScore_' + child.month,
              minWidth: 100,
              tooltip: true,
              align: 'center',
              sortable: true,
              className: 'header-table',
            },
            {
              title: '排名',
              key: 'monthRank_' + child.month,
              minWidth: 100,
              tooltip: true,
              align: 'center',
              sortable: true,
              className: 'header-table',
            },
          ];
        }
        this.showTableColumns.push(monthRow);
      });
    },
    setShowTableData(tableData) {
      //将子列表中的数据按字段平铺出来
      this.showTableData = tableData.map((tableItem) => {
        let monthAttrs = {};
        tableItem.childList.forEach((child) => {
          monthAttrs['monthScore_' + child.month] = child.score;
          monthAttrs['monthRank_' + child.month] = child.rank;
        });
        return { ...tableItem, ...monthAttrs };
      });
    },
    //是否显示排名
    handleChangeShowRank() {
      this.setShowTableData(this.showTableData);
      this.setShowTableColumns(this.showTableData);
    },
    //点击行政区划
    onClickCivil(row) {
      this.$emit('onClickCivil', row);
    },
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.tendency-table-container {
  .tendency-table {
    position: relative;
    .span-btn {
      cursor: pointer;
      color: var(--color-primary);
      text-decoration: underline;
    }
    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    @{_deep} .ui-table {
      height: 600px;
    }
  }
}
</style>
