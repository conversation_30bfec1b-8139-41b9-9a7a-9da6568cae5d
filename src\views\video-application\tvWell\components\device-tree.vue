<template>
  <div class="device-tree">
    <ui-loading v-if="loading" />
    <div class="search-top">
      <Input
        placeholder="请输入"
        v-model="searchInput"
        clearable
        @keydown.enter.native="searchName(true)"
        @on-clear="searchName(true)"
      >
        <template #append>
          <Button icon="ios-search" @click.prevent="searchName(true)"></Button>
        </template>
      </Input>
    </div>
    <xn-tree
      class="deviceTree"
      :ref="'tree'"
      :option="option"
      :label="labelFn"
      :fileOpe="fileOpe"
      @dblclickNode="dblclickNode"
      @startInspect="startInspect"
    ></xn-tree>

    <div class="general-search-footer" v-if="searchInput">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="pageInfo.pageNumber"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { queryDeviceOrgTree, getTreeAncestors } from "@/api/player";
import { copyText } from "@/util/modules/common";
import xnTree from "@/components/xn-tree/index.vue";
import inspectMixin from "../mixins/inspect-mixin";

export default {
  components: {
    xnTree,
  },
  data() {
    return {
      searchInput: "",
      loading: false,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      treeData: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        draggable: true,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            this.loading = true;
            let roleParam = {
              roleId: this.userInfo.roleVoList[0].id,
              filter:
                this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
            };
            return new Promise((resolve) => {
              queryDeviceOrgTree({
                orgCode: node.orgCode,
                deviceType: 1,
                ...roleParam,
              }).then((res) => {
                this.loading = false;
                resolve(this._formatDeviceOrgList(res));
              });
            });
          },
        },
      },
      fileOpe: [
        {
          label: "查看视频",
          show: this.$_has(["video-realTime"]),
          clickFn: (item) => {
            this.dblclickNode(item);
          },
        },
        // {
        // 	label: '历史录像',
        // 	clickFn: (item) => {
        // 		this.dblclickNode(item, 'vod')
        // 	}
        // },
        {
          label: "查看档案",
          show: true,
          clickFn: (item) => {
            const { href } = this.$router.resolve({
              name: "device-archive",
              query: { archiveNo: item.deviceId },
            });
            window.open(href, "_blank");
          },
        },
        {
          label: "查看目录",
          show: true,
          clickFn: (item) => {
            getTreeAncestors(item.deviceId).then((res) => {
              if (res.code == 200) {
                let list = res.data.data.deviceOrgList;
                let text = list.map((v) => v.orgName).join("/");
                this.$Message.info({ content: text, duration: 5 });
              } else {
                this.$Message.error(res.msg);
              }
            });
          },
        },
        {
          label: "复制名称",
          show: true,
          clickFn: (item) => {
            copyText(item.deviceName);
          },
        },
        // {
        //   label: "复制国标编号",
        //   show: true,
        //   clickFn: (item) => {
        //     copyText(item.deviceGbId);
        //   },
        // },
        // {
        //   label: "发送工单",
        //   show: true,
        //   clickFn: (item) => {
        //     Toolkits.toServiceCatalog(this.userInfo.username, item.deviceGbId);
        //   },
        // },
        // {
        //   label: "查询工单",
        //   show: true,
        //   clickFn: (item) => {
        //     Toolkits.toProcessOrder(this.userInfo.username, item.deviceGbId);
        //   },
        // },
      ],
    };
  },
  mixins: [inspectMixin],
  props: {
    playingDeviceIds: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      ignoreDevices: "player/getIgnoreDevices",
    }),
  },
  watch: {
    playingDeviceIds: {
      handler(val) {
        if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
      },
    },
  },
  mounted() {
    this.getParentData();
  },
  methods: {
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconClass =
        data.deviceId && this.playingDeviceIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? "icon-fenju"
          : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let ignoreClass = this.ignoreDevices.includes(data.deviceId)
        ? "ignore"
        : "";
      let onLine = `<span class="color-bule">${data.onlineTotal || 0}</span>`;
      let statistics = !data.deviceId
        ? `(${onLine}/${data.allTotal || 0})`
        : "";
      let operate = "";
      if (this.$_has(["video-realTime"]) && !data.isLeaf) {
        // operate += `<i class="iconfont operate startInspect icon-lunxunkaishi" title="轮巡播放"></i>`;
      }
      let html = `<div class="node-title ${titleClass} ${playingClass} ${ignoreClass}">
                          <i class="iconfont ${iconClass}" style="color: ${iconColor}"></i>
                          <span class="label">${data.label}</span>
                          <span class="statistics">${statistics}</span>
                      </div>
                      <div class="operateBtns">${operate}</div>
                      `;
      return html;
    },
    getParentData() {
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
      };
      this.loading = true;
      let params = { searchKey: this.searchInput, deviceType: 1, ...roleParam };
      if (this.searchInput) params = { ...params, ...this.pageInfo };
      queryDeviceOrgTree(params).then((res) => {
        this.total = res.data.total || 0;
        this.loading = false;
        this.treeData = this._formatDeviceOrgList(res);
        this.$refs.tree.initTree(this.treeData);
      });
    },
    dblclickNode(nodeData, playType = "live") {
      if (nodeData.deviceId) {
        let parentNode = nodeData.$pId
          ? this.$refs.tree.xnTree.getNodeById(nodeData.$pId)
          : {};
        let obj = { ...nodeData };
        let { orgCode, orgName } = { ...parentNode };
        this.$emit("handleClick", {
          ...obj,
          orgCode,
          orgName,
          devicetype: liveType,
          playType,
        });
        if (playType == "live") {
          this.queryLog({
            muen: "视频中心",
            name: "电视墙",
            type: "4",
            remark: `【${nodeData.deviceName}】视频上墙`,
          });
        }
      }
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    // 格式化设备、组织、统计数据
    _formatDeviceOrgList(deviceOrgResult) {
      let deviceList = deviceOrgResult.data.deviceList
        ? deviceOrgResult.data.deviceList.map((v) => {
            v.id = v.deviceId;
            v.label = v.deviceName;
            (v.isLeaf = true),
              (v.ptzType = v.deviceChildType ? v.deviceChildType : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
            return v;
          })
        : [];
      let deviceOrgList = deviceOrgResult.data.deviceOrgList
        ? deviceOrgResult.data.deviceOrgList.map((v) => {
            v.label = v.orgName;
            return v;
          })
        : [];
      deviceOrgList = deviceOrgList.filter((v) => v.allTotal > 0);
      return [...deviceOrgList, ...deviceList];
    },
    searchName(isReset) {
      if (this.loading) return;
      if (isReset) this.pageInfo.pageNumber = 1;
      this.getParentData();
    },
    pageChange(pageNumber) {
      this.pageInfo.pageNumber = pageNumber;
      this.searchName();
    },
  },
};
</script>
<style lang="less" scoped>
.device-tree {
  @import "./style/index";
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  .deviceTree {
    overflow: hidden;
  }

  .group-list {
    max-height: 300px;
    overflow-y: auto;
  }
  /deep/.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
    cursor: pointer;
  }
  .tree-operators-bar-header {
    display: flex;
    align-items: center;
    padding-left: 15px;
    /deep/ .ivu-checkbox-wrapper {
      color: #fff;
    }
    .tree-operators-icon-wrap {
      position: relative;
      .tree-filter-wrap {
        position: absolute;
        top: 28px;
        left: -80px;
        z-index: 9;
        width: 325px;
        background: rgba(44, 48, 51, 0.9);
        border-radius: 3px;
        color: var(--font-white-color);
        padding: 5px 10px;
        .triangle {
          width: 0;
          height: 0;
          overflow: hidden;
          font-size: 0;
          line-height: 0;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent rgba(44, 48, 51, 0.9)
            transparent;
          position: absolute;
          left: 82px;
          top: -10px;
        }
        .tree-filter-wrap-content {
          .tree-filter-wrap-title {
            color: #fff;
            height: 22px;
            font-size: 14px;
            line-height: 25px;
          }
          .tree-filter-check-wrap {
            display: flex;
          }
          .xui-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            .xui-checkbox {
              margin: 0 15px 0 0 !important;
              height: 25px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
}
</style>
