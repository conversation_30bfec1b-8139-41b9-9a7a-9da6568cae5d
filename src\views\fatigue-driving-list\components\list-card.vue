<template>
  <div class="list-card" :class="theme">
    <!-- <div class="top">
      <ui-btn-tip class="sfgl-icon" content="身份关联" icon="icon-guanlian" transfer @click.stop.native="handleRelevance()" />
    </div> -->
    <div class="contrast">
      <template v-if="itemInfo.lastFace">
        <div class="block border">
          <div class="desc">最后出现</div>
          <ui-image
            v-if="itemInfo.lastFace.traitImg || itemInfo.lastFace.sceneImg"
            :src="itemInfo.lastFace.traitImg || itemInfo.lastFace.sceneImg"
            v-viewer="option"
            :data-src="$util.common.imgUrlProxy(itemInfo.lastFace.sceneImg)"
          />
          <ui-image v-else :src="defaultImg" />
        </div>
      </template>
      <template v-else>
        <div class="block border">
          <div class="desc">最后出现</div>
          <ui-image
            v-if="itemInfo.cdStartVehicle.traitImg || itemInfo.cdStartVehicle.sceneImg"
            :src="itemInfo.cdStartVehicle.traitImg || itemInfo.cdStartVehicle.sceneImg"
            v-viewer="option"
            :data-src="$util.common.imgUrlProxy(itemInfo.cdStartVehicle.sceneImg)"
          />
          <ui-image v-else :src="defaultImg" />
        </div>
      </template>
      <!-- <div class="block" style="padding: 12px">
        <ui-image class="animation" :src="c5"></ui-image>
        <div class="num c5" v-if="itemInfo.similarity">{{(itemInfo.similarity * 100).toFixed(2)}}%</div>
      </div> -->
      <template v-if="itemInfo.cdAlarmVehicle">
        <div class="block border">
          <div class="desc">连续驾驶告警</div>
          <ui-image
            v-if="itemInfo.cdAlarmVehicle.traitImg || itemInfo.cdAlarmVehicle.sceneImg"
            :src="itemInfo.cdAlarmVehicle.traitImg || itemInfo.cdAlarmVehicle.sceneImg"
            v-viewer="option"
            :data-src="$util.common.imgUrlProxy(itemInfo.cdAlarmVehicle.sceneImg)"
          />
          <ui-image v-else :src="defaultImg" />
        </div>
      </template>
      <template v-else>
        <div class="block border">
          <div class="desc">首次告警</div>
          <ui-image
            v-if="itemInfo.firstFace.traitImg || itemInfo.firstFace.sceneImg"
            :src="itemInfo.firstFace.traitImg || itemInfo.firstFace.sceneImg"
            v-viewer="option"
            :data-src="$util.common.imgUrlProxy(itemInfo.firstFace.sceneImg)"
          />
          <ui-image v-else :src="defaultImg" />
        </div>
      </template>
    </div>
    <div class="info">
      <div class="left">
        <div class="p" v-if="itemInfo.firstAlarmTime">
          <div class="title">首次告警时间:</div>
          <div class="val">{{ itemInfo.firstAlarmTime }}</div>
        </div>
        <div class="p" v-if="itemInfo.secondAlarmTime">
          <div class="title">连续驾驶告警时间:</div>
          <div class="val">{{ itemInfo.secondAlarmTime }}</div>
        </div>
        <div class="p" v-if="itemInfo.alarmTime">
          <div class="title">告警时间:</div>
          <div class="val">{{ itemInfo.alarmTime }}</div>
        </div>
        <div class="p">
          <div class="title">车牌号:</div>
          <div class="val link" @click.stop="toVehicleTrack">{{ itemInfo.plateNo }}</div>
        </div>
        <div class="p" v-if="itemInfo.firstFace">
          <div class="title">姓名:</div>
          <div class="val">{{ itemInfo.name }}</div>
        </div>
        <div class="p" v-if="itemInfo.firstFace">
          <div class="title">身份证号:</div>
          <div class="val link" @click.stop="toTrack">{{ itemInfo.idNumber }}</div>
        </div>
        <div class="p" v-if="itemInfo.firstFace">
          <div class="title">电话:</div>
          <div class="val">{{ itemInfo.phone }}</div>
        </div>
        <div class="p">
          <div class="title">车辆类型:</div>
          <div class="val">{{ itemInfo.vehicleType | commonFiltering(vehicleClassTypeList) }}</div>
        </div>
        <div class="p" v-if="itemInfo.firstFace">
          <div class="title">是否有驾驶证:</div>
          <div class="val">{{ itemInfo.hasDrivingLicense == 1 ? '是' : '否' }}</div>
        </div>
      </div>
    </div>
    <!-- 其他显示来源 -->
    <div class="content-source" v-if="showDetail">
      <i class="iconfont icon-chayue" title="详情" @click.stop="modalVisible = true"></i>
    </div>

    <Modal title="疲劳驾驶预警详情"
               v-model="modalVisible"
               footer-hide
               width="1500px">
      <div class="modal-box">
        <div class="title">告警信息</div>
        <div class="content-wrapper mb-10">
          <div class="dom-content-detail">
            <div class="dom-content-p" v-if="itemInfo.firstFace">
              <span class="label">姓名</span>
              <span class="message">{{ itemInfo.name || '未知' }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.firstFace">
              <span class="label">身份证号</span>
              <span class="message link" @click.stop="toTrack">{{ itemInfo.idNumber || '未知' }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.firstFace">
              <span class="label">电话</span>
              <span class="message">{{ itemInfo.phone || '未知' }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">车牌号</span>
              <span class="message link" @click.stop="toVehicleTrack">{{ itemInfo.plateNo || '未知' }}</span>
            </div>
            <div class="dom-content-p">
              <span class="label">车辆类型</span>
              <span class="message">{{ itemInfo.vehicleType | commonFiltering(vehicleClassTypeList) || '未知' }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.firstFace">
              <span class="label">是否有驾驶证</span>
              <span class="message">{{ itemInfo.hasDrivingLicense == 1 ? '是' : '否' }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.lastFace">
              <span class="label">预警前最后出现时间</span>
              <span class="message" v-show-tips>{{ itemInfo.lastFace.absTime }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.firstFace">
              <span class="label">首次告警时间</span>
              <span class="message" v-show-tips>{{ itemInfo.firstFace.absTime }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.cdStartVehicle">
              <span class="label">连续驾驶开始时间</span>
              <span class="message" v-show-tips>{{ itemInfo.cdStartVehicle.absTime }}</span>
            </div>
            <div class="dom-content-p" v-if="itemInfo.cdAlarmVehicle">
              <span class="label">连续驾驶告警时间</span>
              <span class="message" v-show-tips>{{ itemInfo.cdAlarmVehicle.absTime }}</span>
            </div>
          </div>
        </div>

        <!-- <div class="title">告警列表</div>
        <div class="content-wrapper">
          <Timeline>
						<TimelineItem v-if="itemInfo.lastFace">
              <div class="capture-li">
                <div class="flex mb-10">
                  <div class="time">{{itemInfo.lastFace.absTime}}</div>
                  <div class="label ml-10">预警前最后出现</div>
                </div>
                <div class="card-box">
                  <ui-image
                    :src="itemInfo.lastFace.traitImg || itemInfo.lastFace.sceneImg"
                    v-viewer="option"
                    :data-src="$util.common.imgUrlProxy(itemInfo.lastFace.sceneImg)"
                  />
                  <div class="ml-10 ellipsis" :title="itemInfo.lastFace.deviceName">{{itemInfo.lastFace.deviceName}}</div>
                </div>
              </div>
            </TimelineItem>
            <TimelineItem v-if="itemInfo.firstFace">
              <div class="capture-li">
                <div class="flex mb-10">
                  <div class="time">{{itemInfo.firstFace.absTime}}</div>
                  <div class="label ml-10">首次告警</div>
                </div>
                <div class="card-box">
                  <ui-image
                    :src="itemInfo.firstFace.traitImg || itemInfo.firstFace.sceneImg"
                    v-viewer="option"
                    :data-src="$util.common.imgUrlProxy(itemInfo.firstFace.sceneImg)"
                  />
                  <div class="ml-10 ellipsis" :title="itemInfo.firstFace.deviceName">{{itemInfo.firstFace.deviceName}}</div>
                </div>
              </div>
            </TimelineItem>
            <TimelineItem v-if="itemInfo.cdStartVehicle">
              <div class="capture-li">
                <div class="flex mb-10">
                  <div class="time">{{itemInfo.cdStartVehicle.absTime}}</div>
                  <div class="label ml-10">连续驾驶开始</div>
                </div>
                <div class="card-box">
                  <ui-image
                    :src="itemInfo.cdStartVehicle.traitImg || itemInfo.cdStartVehicle.sceneImg"
                    v-viewer="option"
                    :data-src="$util.common.imgUrlProxy(itemInfo.cdStartVehicle.sceneImg)"
                  />
                  <div class="ml-10 ellipsis" :title="itemInfo.cdStartVehicle.deviceName">{{itemInfo.cdStartVehicle.deviceName}}</div>
                </div>
              </div>
            </TimelineItem>
            <TimelineItem v-if="itemInfo.cdAlarmVehicle">
              <div class="capture-li">
                <div class="flex mb-10">
                  <div class="time">{{itemInfo.cdAlarmVehicle.absTime}}</div>
                  <div class="label ml-10">连续驾驶告警</div>
                </div>
                <div class="card-box">
                  <ui-image
                    :src="itemInfo.cdAlarmVehicle.traitImg || itemInfo.cdAlarmVehicle.sceneImg"
                    v-viewer="option"
                    :data-src="$util.common.imgUrlProxy(itemInfo.cdAlarmVehicle.sceneImg)"
                  />
                  <div class="ml-10 ellipsis" :title="itemInfo.cdAlarmVehicle.deviceName">{{itemInfo.cdAlarmVehicle.deviceName}}</div>
                </div>
              </div>
            </TimelineItem>
          </Timeline>
        </div> -->

        <div class="title">轨迹列表<span v-if="startDate && endDate">({{startDate}}-{{endDate}})</span></div>
        <div class="track-wrapper">
          <div class="portrait-capture-list">
            <PortraitCaptureCard
              v-for="(item, $index) in dataList"
              :key="$index"
              type="people"
              :data="item"
              @faceDetailFn="faceDetailFn(item, $index)"
            />
            <ui-empty v-if="(!dataList || !dataList.length) && !loading" />
          </div>
          <ui-page
            :current="pageInfo.pageNumber"
            :total="total"
            countTotal
            :page-size="pageInfo.pageSize"
            :page-size-opts="[21, 42, 84, 105]"
            @pageChange="pageChange"
            @pageSizeChange="pageSizeChange"
          >
          </ui-page>
          <ui-loading v-if="loading" />
        </div>
      </div>
    </Modal>

    <!-- 动态库人脸详情 -->
    <details-vehicle-modal
      v-if="videoShow"
      ref="videoDetail"
      :toIdentity="true"
      :btnJur= "['rl', 'ss', 'lx', 'fd', 'sx', 'xz', 'dw']"
      @close="videoShow = false"
      @prePage="prePage"
      @nextPage="nextPage"
    ></details-vehicle-modal>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import c5 from "@/assets/img/target/c-five.png";
import PortraitCaptureCard from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/card/portrait-capture-card.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import { getVehicleTrajectory } from "@/api/vehicleArchives.js";

export default {
  components: {
    PortraitCaptureCard,
    detailsVehicleModal
  },
  props: {
    itemInfo: {
      type: Object,
      default: () => {},
    },
    showDetail: {
      type: Boolean,
      default: () => false,
    },
    theme: {
      type: String,
      default: () => 'light',  //light、dark、transparent
    },
  },
  watch: {
    modalVisible: {
        handler (newVal){
          if (newVal) {
            this.queryList()
          }
        },
    }
  },
  data() {
    return {
      c5,
      option: {
        url: "data-src",
      },
      defaultImg: require("@/assets/img/default-img/real_name_default.png"),
      modalVisible: false,
      dataList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 21,
      },
      total: 0,
      loading: false,
      startDate: '',
      endDate: '',
      videoShow: false
    };
  },
  computed: {
		...mapGetters({
			associationIdCardStatusList: 'dictionary/getAssociationIdCardStatusList', // 状态
      vehicleClassTypeList: 'dictionary/getVehicleTypeList', //车辆类型
		})
	},
  async created() {
		// await this.getDictData();
	},
  mounted() {},
  methods: {
    ...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
    handleRelevance() {
      
    },
    toTrack() {
      let query = {
          archiveNo: this.itemInfo.idNumber,
          noMenu: 1,
      }
      const { href } = this.$router.resolve({
          name: 'real-name-track',
          query
      })
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem('query', JSON.stringify(query))
      this.$util.openNewPage(href, "_blank");
    },
    toVehicleTrack() {
      let query = {
          archiveNo: JSON.stringify(this.itemInfo.plateNo+'_'+this.itemInfo.plateColor),
          plateNo: JSON.stringify(this.itemInfo.plateNo),
          noMenu: 1
      }
      const { href } = this.$router.resolve({
          name: 'vehicle-track',
          query
      })
      this.$util.openNewPage(href, "_blank");
    },
    queryList(page = false) {
      this.dataList = [];
      if (!this.itemInfo.plateNo) return
      this.startDate = this.itemInfo.lastFace ? this.itemInfo.lastFace.absTime : this.itemInfo.cdStartVehicle.absTime
      this.endDate = this.itemInfo.cdAlarmVehicle ? this.itemInfo.cdAlarmVehicle.absTime : this.itemInfo.firstFace.absTime
      this.loading = true;
      let params = {
        plateNo: this.itemInfo.plateNo,
        startDate: this.startDate,
        endDate: this.endDate,
        ...this.pageInfo,
      };
      getVehicleTrajectory(params)
        .then((res) => {
          this.dataList = res.data.entities || [];
          this.total = res.data.total || 0;
          if (page == 1) {
            this.$refs.videoDetail.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.videoDetail.nextPage(this.dataList);
          }
          this.$forceUpdate();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    /**
     * 上一个
     */
     prePage(pageNum) {
      if(pageNum < 1) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        this.queryList(1);
      }
    },
    /**
     * 下一个
     */
    async nextPage(pageNum) {
        let size = this.pageInfo.pageSize;
        if(this.total <= pageNum*size) {
            this.$Message.warning("已经是最后一个了")
            return
        }else{
            this.pageInfo.pageNumber = pageNum;
            this.queryList(2)
        }
    },
    faceDetailFn(row, index) {
      this.currentIndex = index;
      this.videoShow = true;
      this.$nextTick(() => {
        let zIndex = this.getModalIndex()
        this.$refs.videoDetail.$el.style.zIndex = zIndex+1
        console.log(zIndex)
        this.$refs.videoDetail.init(
          row,
          this.dataList,
          index,
          this.pageInfo.pageNumber
        );
      });
    },
    getModalIndex() {
      const allE = document.querySelectorAll('.ivu-modal-mask')
      let maxIndex = -Infinity
      for(let e of allE) {
        const style = window.getComputedStyle(e)
        const zIndex = parseInt(style.zIndex, 10)
        if (!isNaN(zIndex) && zIndex>maxIndex) {
          maxIndex = zIndex
        }
      }
      return maxIndex === -Infinity ? 0 : maxIndex
    }
  },
};
</script>
<style lang="less" scoped>
.mb-10 {
  margin-bottom: 10px;
}
.list-card {
  position: relative;
  /* height: 295px; */
  overflow: hidden;
  border-radius: 3px;
  &.light {
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    border: 1px solid #ededed;
    background: #f9f9f9;
    .info .title{
      color: rgba(0, 0, 0, 0.6);
    }
    .info .val{
      color: rgba(0, 0, 0, 0.9);
    }
    .link {
      cursor: pointer;
      color: #2c86f8 !important;
    }
    .contrast .border {
      border: 1px solid #ebebeb;
    }
  }
  &.dark {
    box-shadow: 0px 4px 26px 0px #010E1E;
    background: #062F61;
    .info .title{
      color: rgba(255, 255, 255, 0.7);
    }
    .info .val{
      color: rgb(255, 255, 255);
    }
    .link {
      cursor: pointer;
      color: #2ECBFF !important;
    }
    .contrast .border {
      border: 1px solid #000000;
    }
  }
  &.transparent {
    background: transparent;
    .info .title{
      color: rgb(255, 255, 255);
    }
    .info .val{
      color: rgb(255, 255, 255);
    }
    .link {
      cursor: pointer;
      color: #2ECBFF !important;
    }
    .contrast .border {
      border: 1px solid #000000;
    }
  }
  .top {
    position: relative;
    display: flex;
    justify-content: flex-end;
    padding: 0 6px;
    .sfgl-icon {
      color: #2c86f8;
    }
  }
  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    margin-top: 10px;
    .block {
      position: relative;
      width: 100px;
      .ui-image {
        width: 100px;
        height: 100%;
        cursor: pointer;
        /deep/ img{
          height: 100%;
          width: 100%;
        }
      }
      .animation {
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .desc {
        position: absolute;
        z-index: 9;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 0 6px;
      }
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
      .c1 {
        color: #ea4a36;
      }
      .c2 {
        color: #e77811;
      }
      .c3 {
        color: #ee9f00;
      }
      .c4 {
        color: #36be7f;
      }
      .c5 {
        color: #2c86f8;
      }
    }
  }

  .info {
    display: flex;
    padding: 12px;
    font-size: 12px;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        .title {
          margin-right: 10px;
          line-height: 22px;
          white-space: nowrap;
        }
        .val {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }

  .content-source {
    font-size: 12px;
    color: #2c86f8;
    cursor: pointer;
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
}
.modal-box {
  /deep/ .ivu-timeline-item-head {
    top: 2px;
  }
  .capture-li {
    display: flex;
    flex-direction: column;
    .time {
      font-size: 12px;
    }
    .label {
      font-weight: bold;
    }
    .card-box {
      display: flex;
      align-items: flex-end;
      height: 100px;
      width: 400px;
      padding: 10px;
      .ui-image {
        height: 80px;
        width: 80px;
        cursor: pointer;
        flex-shrink: 0;
        /deep/ img{
          height: 100%;
          width: 100%;
        }
      }
      div {
        white-space: nowrap;
      }
    }
  }
  .title {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    padding-left: 9px;
    height: 30px;
    width: 100%;
    font-weight: bold;
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    &:before {
      content: '';
      position: absolute;
      width: 3px;
      height: 16px;
      top: 50%;
      transform: translateY(-50%);
      left: 0;
      background: #2c86f8;
    }
  }
  .dom-content-detail {
    display: flex;
    flex-wrap: wrap;
    .dom-content-p {
      width: 50%;
      line-height: 34px;
      display: flex;
      border: 1px solid #d3d7de;
      border-top: none;
      &:nth-child(2n+2){
        border-left: none;
      }
      &:nth-child(1),&:nth-child(2) {
        border-top: 1px solid #d3d7de;
      }
      .label {
        border-right: 1px solid #d3d7de;
        font-size: 12px;
        display: inline-block;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        white-space: nowrap;
        width: 120px;
        background: #f9f9f9;
        padding: 0 5px;
      }

      .message {
          padding: 0 5px;
          font-size: 12px;
          width: 140px;
          display: inline-block;
          color: rgba(0, 0, 0, 0.9);
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
      }
    }
  }
  .content-wrapper {
    max-height: 400px;
    overflow-y: auto;
  }
  .track-wrapper {
    height: 480px;
    display: flex;
    flex-direction: column;
    position: relative;
    .portrait-capture-list {
      flex: 1;
      display: flex;
      margin: 0 -5px;
      overflow: auto;
      flex-wrap: wrap;
      position: relative;
      align-content: flex-start;
      .portrait-capture-card {
        width: calc(~"14.28% - 10px");
        margin: 0 5px 10px 5px;
      }
    }
  }
}
/deep/ .ivu-modal {
  top: 60px;
}
</style>