/*
 * @Date: 2024-12-18 15:22:33
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-11 11:36:30
 * @FilePath: \icbd-view\src\router\index.js
 */
import Vue from "vue";
import Router from "vue-router";
import { asyncRoutes } from "./router.config";
// 处理vue-router升级导致的错误，https://github.com/vuejs/vue-router/issues/2881#issuecomment-520554378
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject)
    return originalPush.call(this, location, onResolve, onReject);
  return originalPush.call(this, location).catch((err) => err);
};
Vue.use(Router);

export default new Router({
  mode: "history",
  base: BASE_URL || process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }),
  routes: asyncRoutes,
});
