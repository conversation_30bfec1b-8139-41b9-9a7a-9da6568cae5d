import Vue from 'vue';
import Router from 'vue-router';
Vue.use(Router);

/**
 * 解决报错问题：报错显示是路由重复
 * Error: Avoided redundant navigation to current location
 * https://stackoverflow.com/questions/62462276/how-to-solve-avoided-redundant-navigation-to-current-location-error-in-vue
 */
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

export const constantRoutes = [
  {
    path: '/login',
    name: 'login',
    component: (resolve) => require(['@/views/login/index.vue'], resolve),
  },
  {
    path: '/portal',
    name: 'portal',
    component: (resolve) => require(['@/views/portal/index.vue'], resolve),
  },
  {
    path: '/navigationpage',
    name: 'navigationpage',
    component: (resolve) => require(['@/views/navigation-page/index.vue'], resolve),
  },
  {
    path: '/usercenter',
    name: 'usercenter',
    component: (resolve) => require(['@/views/user-center/index.vue'], resolve),
  },
  {
    path: '/downloadcenter',
    name: 'downloadcenter',
    component: (resolve) => require(['@/views/download-center/index.vue'], resolve),
  },
  {
    path: '/mymessage',
    name: 'mymessage',
    component: (resolve) => require(['@/views/my-message/index.vue'], resolve),
  },
  {
    path: '/archives',
    name: 'archives',
    component: (resolve) => require(['@/views/archives/index.vue'], resolve),
  },
  {
    path: '/401',
    name: 'error_401',
    component: (resolve) => require(['@/views/error-page/401.vue'], resolve),
  },
];
const createRouter = () =>
  new Router({
    mode: 'history',
    routes: constantRoutes,
  });

const router = createRouter();

export default router;
