<!--
    * @FileDescription: top3统计
    * @Author: H
    * @Date: 2024/05/08
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div class="chart-title">
            <div class="chart-dot"></div>
            <p>{{ title }}</p>
        </div>
        <div id="rangking" class="rangking" ref="rangking"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default{
    props: {
        title: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            formData: {
                type: ''
            },
            
            myEchart: null
        }
    },
    mounted() {
        // this.init()
        // this.rangkingChart()
    },
    methods: {
        init(list) {
            let yLeft = [];
            let yRight = [];
            list.forEach(item => {
                yLeft.push(item.creatorName);
                yRight.push(item.count)
            })
            this.rangkingChart(yLeft, yRight)
        },
        rangking<PERSON>hart(yLeft, yRight) {
            let rank1 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAA7NJREFUOE+Nle1vFFUUxp9z52W7W7rb7vZlV7ekGE1Dq1i1rSkx4S+wmogsIfGDpikajFaiRqNoRRKjRNMY36IkfiE6q2DUD4ToB4VYX/CtCBJRYJdSbVlbutPS2d2ZufeanZ2aLrXgzXw75/zmOWfOPJdwhSPPfNIOhb3ipXGxna654+TlSmiloLzweQRzxWcgig/auVG9nKc39dlQ9NcQdnZSdJP5X7XLgFJKhvGPByDE887s8Wb3/CgkL3i1pAShNvdBq+/IgdEOtN29h4jEUnAVUGY+2gDwEXd+osuZOgxRnMZiglxSxWoaobeshxJKjEFlQ7Rmy6HFsJcvM++3QdJL0p7bWJr8mrh5+kqj9eJKeA0C8R5Jau0+cHqc1t6brQBP7TWcmRMpe/IbSOFWYJ4kCUhZfsBqWyCtHMDKJfSvchCDHu+GFr0uTe1bN1eAf7xrFLNfpFwz6ysTkFxA7xyAEuuEEl0LUTKxcGAjmKIBVD16NbwaNcn1aerY5gNPvmMUs4dTrnnWFyYgnBLCm76EO3sKWvM6cHMc85/eCUUPAUypGokaTiLY2pumziEf+NsbRiE7mnLz5yrdSgFpF8BtG9zKo+mhcXAzi/n9/WDBOhBTq4GRqxBMdqfphkd94IlXjUL225Sbn/DnJyC4C2Fb4HYBjQ/8Dp7PYm5fP5RgHbAMGEcoeVOabnzSB/76smFlvk+5+b/8N5e/iIR0HfBSAdHBo+D5szA/vB1KTRh0acuRFoRa16Wpa4cPPPaiYWV+Sjn5yep14Q64bSE6OAa3rPCDfiiB8LIZapFmhFo70nTzTh94dJdhZX9JObNTVUBZBjoWYlvHvJZNYyVgI0Kr29N0yws+cGzYWMgcTzn53JL9EwhcvxmSVIR67oco5lE89h5gzcA+/RmIMW8fJSS0SAy1rdemqWe3D/zxKaM0PZkq/HkGEOUUAfASIvccBAs2VKl2Jo7g4oGHQczfR8YQTCQRiDWmqXfEB/78RBtcsVs4xbusiXFy8jOQbgHCngd3FiC57alhqg6mrQLTwyBFh1bfgFAiLpkW2A/pPEZ9b1V+vcUjjzyyAZAjzpzZZZ3Lwr1oQgoHKCv27IaBmA61NoJQ8mpodavGwDBEvW9Wm0MVVA4zfHd+QHK+qzR9oakwlYN0uTdb0hQE400IxOr/JlV9Grcm9hANr2xfVeAfBiOw8azk7jZrcsYz2FCiwSZFeR268hx1v/3/DPZS35Jf3dcOiMoVAHc73bb3slfAPyCfpSQtTlMXAAAAAElFTkSuQmCC';
            let rank2 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAA8JJREFUOE+NlV1sVEUUx/8z99797FrBCN1goVBrdzfEbmtMrJIWFSQqUWtN1wf7YALRRB8IiT4pBvVBa0Ig6pPEjweTXQRqNBClFUJDgjGxH26gW0vBtMW22BRXWLa7c2eOuR+7XUHB+/UyZ37z/58zcy7DLa6Ri9TINeyxwpTEzqZVbOxmU9h/DU4sUHWugF15E6+emiCPFbehnhX9Oj4KevF2/XKW/be5NwCJiI/MYJsC3hmeohUnzwHXigAYEDCAtgYgfhe7BODNljD2M8ZUJfgfwMEZalcSeycvU7wvQ5j7C2CsFEL2POu7MgRsijCsWcaGuYYdLWF2sgS1o09foDrdQE82j+f6M4pl5irWtCIclnNVSIisBB5t5HS7HwdNgddb17Lf7OEfJyk5OEWJ/jGCKd2JRFBE8GpkK8oJYD7HwBmrUA3onLA5wtFcy1IPrGbPOwonKXl4WCXOzi7BpFR46UGGres16JojK31RYtdRE0Wlo5wJBsRqGJ6N81RrCXjqAiV7RyygmydFWBRFfNhpoO9MDlMLAi+0htCyxofPTxfw5SCDoWvlLEQtYBNPbVjrKhw47yqcIztFShEKhSJMkUc2JyCEQFuDgU9erMGR9CJ6+hV8Po9jnQg2MK6l2ta5wBPnKdk7pBKjswTrth5TSgjThFIKhYLA9od0vLzxNuz+Notjv2oI+Hy2bcuTZbmjWUs9XAIeP0fJw0PSsVxRVasoUkrEVpjY1xXE2EwR3Z/+iapQCF6PAeaWPBZ2FD5yt6uwb9wBlhW62VFS4d6wQk+HD9MLAt375yGYH8FgAJrm5NBa37Lc2aylNjW4wO/HrBya5aJYPiyr99cqvPe0D+npArZ/MQ8BL4JVAXgNz9J+JMBRqKe2NLrA7zKUPGQBZ5wqW1bXLTPxWbffztNXP12BUByGoePMLMOJcQbGl3Z4LMzREddST0Rc4JGzlPx5SiaOjSp7Y0tponW1xL5E1Q3n/+BgAe//IKFruq1S5wyPRTnuq9VST8ZcYO8o1ekSH2QXqbMvY7L0tEA+n8fVqzkURdHeHpxzGLoOv99vv4ZhIFrDsTmiUbWPHTI1vNYRdY9eScY3v1C74tg7ccmMH00v4vfLwq5y+RhzDo+uY9VyDx5f70HdHXyYadjxVPS65lDpy2pfX6exrSDo3aFpcefAuMQ14UQEDIaN9+hoqdX+0D3sjWdit2hfleADE1TNr+CtRYlXBsZNu8G2N+hFr4aPVQi7u+r/Z4O9vgoHRqgRCnvA7ZGdXU03/wX8DXWepCQ0dknbAAAAAElFTkSuQmCC';
            let rank3 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAABDFJREFUOE+N1X1M3GQcB/Bv22uPOwZ3B9wJNzgYx5vgIk6RERfQoJnLjDHx5UQzXzISE13MMl+yGN0Apya+EEzQxASjif5xpw6XmPg2/0HcWJYhOKcRFDhg5N64F+6Fu+u1z2PaDgaZom2aNG2fT3/99umvDP5j+YaG6nVg+ykAGeTIPsY6tdUQ5t9OnqYRkwj5WEbOHRoNLwqUUrSXOMQ8jh8UwPXdxRSt/NPYa0BKKXuKBLsp6KsXV/y2H8OLSEkiKCEwcjzaSyrRbCkLMmBeuY+1DTEMQzbCm8DPcr4OAjKwlEk0jyx7EcykoFS2aSMUVr0RnaVOVOSbJ1mwhx/iy0bWUBX8KD1XxfP8m/Fc9oGx8CLzVzJyFSHkGnDtBnWFxeiw19ACwfCFLOVefNKww6uCQ+KCeyoecp2LLCJHZIACRJZBJBk2Vg8TJ8AvriIqZwCGVYtRIlBgjmHQbq/BTmu5p1twPKyC72fn3KPBOddcMqpVQwgkMYfe8lbcY3WqAKEUJ/1TOOEdA3ScdowQgFI4zVbsrb7B87R+hwYOpGfdZ0OzrvlkTMWITCCupvGouQbTwSXoZIq+1v2wGQvw+PgpjGfDYDjuys0VsASd1Ts9hw3VGvh2+k/3WMDrWlirUJaRS2eQjichJlLIJlLo330vuhpacPDcMM5mQ+AEfj3bKrMVnc4mz/OGWg18fXXKfT7gdV1OXH1kOSdBEAnuLCyHnTPgYF0LLoV96Drjga6oABwvrIMOcwlur2n0vGRs0MBjyd/dE4EF11IiqmZCKYECWgmPH3Y/ouYVSafw2oXvMByegd5coD7yWo4VlhLc5mz09G1r1MCjyV/dF30LLn8ypoYPQlVQTCQhJLKwSAx6Wvfjjsp6HJ38HsORGbBKhspKKbZbitFW0+R5Y1uTBj6X+MV9ybfoCiRi629ZmTa51QyyyRSyKwnss9Xgw70H8OnsBI5Pj0KnF9ZBu6UYLbXXe94puFEDD6387P7Df9kVil8BZYJ8yqDXuQenfTPQyxSPVd+EOrMNz575El/FvNAZ9Eo6agGllmLsqq33DJp2aeBTsXF3YCXimgv61ClDJAm6jISv27pgNxaqWSXFDAYnR/Du9BjyiszaWwYFCwZ1jipUlJZ5PjDdrIFPRCeqGIhvZSTp/sWAjwlHIsgmVyHG4jBRDiwB/KtxsHoBQmE+eKMBYFkUWyxwOiqpQRBOUpAXPra0aZ/e2nIger6DUDIQTyaavfPziEdjkDNZdbKD49SqlM2Yn48qhwNmk0ltDp9Ybt3cHDaiPZSyv0XHuokkn1heXrb6fD5IuZx6CcfzsG+3w1pcEtLxupcbTK1DPVu1r43wg5ELJpFmjkuS/EzQ5xeUjnFdWZnI6bj3BCav9/OiW/5fg92IKvt3h36qZyD3qx0G3JFvrXu2/AX8DQ2XQzNG9LGgAAAAAElFTkSuQmCC';
            this.myEchart = echarts.init(this.$refs.rangking);
            // const yAxisDataLeft = ['张三', '李四', '王五'];
            // const yAxisDataRight = ['24','23','12',];
            const yAxisDataLeft = yLeft;
            const yAxisDataRight = yRight;
            let option = {
                grid: {
                    left: 10,
                    bottom: 0,
                    right: 30,
                    top: 5,
                    containLabel: true,
                },
                xAxis: {
                    show: false,
                    type: 'value'
                },
                yAxis: [
                    {
                        type: 'category',
                        inverse: true,
                        data: yAxisDataLeft,
                        axisLabel: {
                            show: true,
                            inside: false,
                            margin:10,
                            // lineHeight:28,
                            verticalAlign: 'center',
                            // textStyle: {
                            //     color: '#D0DDE7',
                            //     fontSize: 14,
                            //     align: 'left',
                            // },
                            formatter: function (value, index) {
                                if (index < 3) {
                                    return `{b${index}|${''}} {a|${value}}`
                                } else {
                                    return `{b|NO.${index+1}}`
                                }
                            },
                            rich: {
                                a: {
                                    color: 'rgba(0,0,0,0.8)',
                                    lineHeight: 16,
                                },
                                b0: {
                                    width: 20,
                                    height: 20,
                                    backgroundColor: {
                                        image: rank1,
                                    },
                                },
                                b1: {
                                    width: 20,
                                    height: 20,
                                    backgroundColor: {
                                        image: rank2,
                                    },
                                },
                                b2: {
                                    width: 20,
                                    height: 20,
                                    backgroundColor: {
                                        image: rank3,
                                    },
                                },
                            }
                        },
                        //offset: 30,
                        splitLine: {
                            show: false
                        },
                        axisTick: {
                            show: false
                        },
                        axisLine: {
                            show: false
                        }
                    },
                ],
                series: [
                    {
                        type: 'bar',
                        zlevel: 1,
                        label: {
                            // 
                            show: true, //数值是否显示
                            // rotate: 60, //数值显示的角度
                            position: 'right', // 数值相对于柱状图显示的位置
                            textStyle: {
                                color: '#2C86F8', // 标签字体颜色
                                fontSize: '14', // 标签字体大小
                                fontStyle: 'italic', // 标签字体斜体
                                fontFamily: '', //标签字体
                                fontWeight: '700'
                            }
                        },
                        itemStyle: {
                            normal: {
                                barBorderRadius: 10,
                                color: {
                                    type: 'linear',
                                    colorStops: [
                                        {
                                            offset: 0,
                                            color: 'rgba(91, 199, 255, 1)'
                                        },
                                        {
                                            offset: 1,
                                            color: 'rgba(44, 134, 248, 1)'
                                        }
                                    ]
                                }
                            }
                        },
                        barWidth: 10,
                        emphasis: {
                            disabled: true,
                            focus: 'none',
                        },
                        data: yAxisDataRight,
                    },
                    // {
                    //     // 辅助系列
                    //     type: 'bar',
                    //     barGap: '-100%',
                    //     silent: true,
                    //     itemStyle: {
                    //         color: 'rgba(255, 255, 255, 0.1)',
                    //     },
                    //     barWidth: 8,
                    //     data: [130, 130, 130, 130, 130, 130]
                    // },
                    // {
                    //     type: 'bar',
                    //     data: [120, 100, 90, 60, 30, 20],
                    //     barWidth: 8,
                    //     label: {
                    //         position: [10, 10],
                    //         normal: {
                    //             position: [800, -24],
                    //             show: true,
                    //             textStyle: {
                    //                 color: '#8db0ff',
                    //                 fontSize: 14,
                    //             },
                    //         },
                    //     },
                    //     itemStyle: {
                    //         normal: {
                    //         color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    //             'rgba(0,143,236,0)', '#00B9EC',
                    //             ].map((color, offset) => ({
                    //                 color,
                    //                 offset
                    //             })))
                    //         }
                    //     },

                    // }
                ]
            }
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .chart-title{
        position: absolute;
        left: 50%;
        transform: translate(-50%, 0);
        font-size: 16px;
        font-weight: 700;
        color: #3D3D3D;
        display: flex;
        align-items: center;
        .chart-dot{
            width: 8px;
            height: 8px;
            background: #2C86F8;
            margin-right: 10px;
        }
    }
    .rangking{
        height: 90%;
        width: 95%;
        margin: 0 auto;
    }
}
</style>