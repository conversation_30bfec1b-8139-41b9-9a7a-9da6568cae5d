<template>
  <div class="inline" v-clickoutside="dropHide">
    <Dropdown trigger="custom" :visible="visible" transfer>
      <div class="ivu-select ivu-select-single width-md" @mouseenter="mouseenter" @mouseleave="mouseleave">
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!selectTree[defaultProps.label]">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="selectTree[defaultProps.label]">{{
              selectTree[defaultProps.label]
            }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="isClose" />
        </div>
      </div>
      <DropdownMenu slot="list" class="width-md">
        <el-tree
          ref="tree"
          class="tree"
          :node-key="nodeKey"
          multiple
          :style="treeStyle"
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultKeys"
          @node-click="handleNodeClick"
          @current-change="currentChange"
        >
          <template #default="{ node }">
            <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">{{ node.label }}</span>
          </template>
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.tree {
  min-width: 150px;
  overflow-x: auto;
  font-size: 12px;
  margin-right: 10px;
}
@{_deep} .ivu-dropdown {
  position: relative;
  width: inherit;
}
</style>
<script>
export default {
  name: 'tree-select',
  directives: {
    TransferDom: require('view-design/src/directives/transfer-dom'),
  },
  data() {
    return {
      treeText: '',
      visible: false,
      isClose: false, //清空按钮是否显示
      selectTree: {},
    };
  },
  created() {},
  methods: {
    mouseenter() {
      if (this.treeText) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      if (data.disabled) {
        this.$Message.error('您没有此组织机构权限');
        return false;
      }
      this.selectTree = data;
      this.$emit('input', data[this.nodeKey]);
      this.$emit('current-change', data);
    },
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      this.visible = !this.visible;
    },
    currentChange(data) {
      if (data.disabled) {
        return false;
      }
      this.visible = false;
    },
    clear() {
      this.isClose = false;
      this.$emit('input', '');
      this.$emit('current-change', {});
    },
  },
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.$nextTick(() => {
          if (val) {
            this.$refs.tree.setCurrentKey(val);
            this.selectTree = this.$refs.tree.getCurrentNode() || {};
            this.$emit('current-change', this.selectTree);
          } else {
            this.$refs.tree.setCurrentKey(null);
            this.selectTree = {};
            this.$emit('current-change', {});
          }
        });
      },
    },
    treeData: {
      deep: true,
      immediate: true,
      handler: function (val) {
        if (val && val.length !== 0) {
          this.$nextTick(() => {
            this.$refs.tree.setCurrentKey(this.value);
            this.selectTree = this.$refs.tree.getCurrentNode() || {};
            this.$emit('current-change', this.selectTree);
          });
        }
      },
    },
  },
  computed: {},
  props: {
    value: {},
    nodeKey: {
      type: String,
      default: 'id',
    },
    defaultKeys: {
      default: () => [],
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    // 树结构style
    treeStyle: {},
    placeholder: {
      required: true,
      type: String,
      default: '请选择',
    },
    treeData: {
      required: true,
      type: Array,
      default: () => [],
    },
    taskObj: {
      type: Object,
      default() {},
    },
  },
};
</script>
