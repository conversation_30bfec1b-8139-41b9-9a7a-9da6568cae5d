<template>
  <div class="module-title-container">
    <p>
      【{{ label }}】
      <span class="count">
        <CountTo :start-val="0" :end-val="end" :duration="1000" class="num" />
      </span>
      个
    </p>
    <div class="morePage" @click="handlePageMore">
      <span>更多</span>
      <ui-icon type="more" :size="14"></ui-icon>
    </div>
  </div>
</template>

<script>
import CountTo from "vue-count-to";

export default {
  name: "ModuleTitle",
  props: {
    label: {
      required: true,
      type: String,
    },
    start: {
      type: Number,
      default: 0,
    },
    end: {
      required: true,
      type: Number,
    },
    duration: {
      type: Number,
      default: 1000,
    },
  },
  components: { CountTo },
  methods: {
    handlePageMore() {
      this.$emit("more");
    },
  },
};
</script>

<style lang="less" scoped>
.module-title-container {
  display: flex;
  align-items: center;
  position: relative;
  font-size: 18px;
  background: linear-gradient(
    90deg,
    rgba(44, 134, 248, 0.1) 0%,
    rgba(44, 134, 248, 0) 100%
  );
  font-weight: 700;
  justify-content: space-between;
  margin-bottom: 16px;
  &:before {
    content: "";
    position: absolute;
    width: 4px;
    height: 27px;
    top: 50%;
    transform: translateY(-50%);
    background: #2c86f8;
  }
  .count {
    color: #2c86f8;
  }
  .morePage {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.35);
    cursor: pointer;
  }
}
</style>