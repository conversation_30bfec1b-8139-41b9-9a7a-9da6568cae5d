<template>
  <div class="asset-compare-container ">
    <div class="asset-compare-wrapper " v-show="!tagComponentName">
      <div class="header mb-md">
        <tagView
          ref="tagView"
          :default-cur-tag="defaultCurTag"
          :list="MENU_LIST"
          :no-active="MENU_LIST.length === 1"
          @tagChange="tagChange"
        />
        <div class="config">
          <ui-label class="inline mr-md" label="对账时间" v-show="isSupSubCompare">
            <Select
              v-model="searchData.batchId"
              class="width-md"
              placeholder="请选择对账时间"
              clearable
              @on-change="onChangeBatchId"
            >
              <Option v-for="(item, index) in hideBatchList" :key="index" :value="item.key">{{ item.value }}</Option>
            </Select>
          </ui-label>
          <span class="icon-font icon-peizhibiduiziduan vt-middle"></span>
          <span class="ml-xs vt-middle link-text-box pointer" @click="handleClickConfig">配置管理</span>
        </div>
      </div>
      <div class='asset-compare-content'>
        <component ref="compareRef" :is="componentName" :batch-id="searchData.batchId" :compare-target='compareTarget'></component>
      </div>
    </div>
    <component :is="tagComponentName"></component>
    <config v-model="configVisible" :default-menu='defaultCurTag'></config>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';

import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import { MENU_LIST, VIDEO_DEVICE_REVOCATION } from '@/views/viewassets/assetcompare/modules/enum.js';
export default {
  name: 'assetCompare',
  mixins: [dealWatch],
  data() {
    return {
      //create-tabs
      tagComponentName: null,
      // componentLevel: 0,

      componentName: 'CurrentLevelCompare',
      tableLoading: false,
      configVisible: false,
      defaultCurTag: 0,
      MENU_LIST,
      hideBatchList: [],
      searchData: {
        batchId: '',
      },
    };
  },
  async created() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
    await this.getHideBatchList();
    this.setDefaultBatchId();
    this.getConfig();

  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 批量获取字典值
      getConfig: 'assetcompare/getConfig', // 批量获取字典值
    }),
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.tagComponentName = name.split('-')[this.componentLevel];
      } else {
        this.tagComponentName = null;
      }
    },
    handleClickConfig() {
      this.configVisible = true;
    },
    tagChange(index, item) {
      this.defaultCurTag = index;
      this.componentName = item.code;
    },
    async getHideBatchList() {
      try {
        let params = {
          type: VIDEO_DEVICE_REVOCATION.indexType, //type 硬编码
        };
        let { data } = await this.$http.get(evaluationoverview.getHideBatchList, { params });
        this.hideBatchList = data.data || [];
      } catch (e) {
        console.log(e);
      }
    },
    setDefaultBatchId() {
      if (!this.hideBatchList.length) return;
      this.searchData.batchId = this.hideBatchList[0]['key'];
    },
    onChangeBatchId(val) {
      // this.$refs.compareRef.getStatInfoList();
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      assetsDataSource: 'algorithm/assets_data_source', // 数据源
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      dataSourceCategory: 'algorithm/data_source_category', //来源标识
    }),
    isSupSubCompare() {
      return this.defaultCurTag === 1;
    },
    componentLevel() {
      let componentLevelMap = {
        AssetComparisonResult: 1,
        SupSubComparisonResult: 1,
      };
      if (!this.$route.query.componentName) return 0;
      let componentNames = this.$route.query.componentName.split('-');
      if (componentNames.length > 1) {
        return  componentLevelMap[componentNames[componentNames.length -1]];
      }
      return 0;
    },
    /**
     * 对比对象 区分 本级对账、上下级对账和跨网对账
     * @returns {{label: string, componentName: string}|{label: string, componentName: string}|{label: string, componentName: string}|{}}
     */
    compareTarget() {
      return this.MENU_LIST[this.defaultCurTag] || {};
    }
  },
  props: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AssetCompareCard: require('@/views/viewassets/assetcompare/components/asset-compare-card.vue').default,
    tagView: require('@/components/tag-view').default,
    CurrentLevelCompare: require('@/views/viewassets/assetcompare/current-level-compare.vue').default,
    SupSubCompare: require('@/views/viewassets/assetcompare/sup-sub-compare.vue').default,
    CrossNetCompare: require('@/views/viewassets/assetcompare/cross-net-compare.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    Config: require('@/views/viewassets/assetcompare/config/index.vue').default,
    CurrentLevelCompareHistory: require('@/views/viewassets/assetcompare/current-level-compare-history.vue').default,
    SupSubLevelCompareHistory: require('@/views/viewassets/assetcompare/sup-sub-compare-history.vue').default,
    AssetComparisonResult: require('@/views/viewassets/assetcompare/asset-comparison-result.vue').default,
    SupSubComparisonResult: require('@/views/viewassets/assetcompare/sup-sub-comparison-result.vue').default,
  },
};
</script>

<style lang="less" scoped>
.asset-compare-container {
  position: relative;
  height: 100%;
  background: var(--bg-content);
  .asset-compare-wrapper {
    height: 100%;
    padding: 15px 20px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 15px;
      border-bottom: 1px solid var(--devider-line);
      .config {
        color: var(--color-active);
      }
    }
    .statistics {
      position: relative;
      display: flex;
      gap: 20px;
      .card-wrapper {
        position: relative;
        flex: 1;
        //width: 580px;
        height: 280px;
      }
    }
    .asset-compare-content {
      height: calc(~'100% - 70px');
      overflow-y: auto;
    }
  }
}
</style>
