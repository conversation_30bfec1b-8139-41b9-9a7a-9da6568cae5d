<template>
  <div class="face-result">
    <div v-for="(item, index) in aggregateOptions" :key="index">
      <flow-process :propData="item" dWidth="11.8%" bWidth="13.01%"></flow-process>
    </div>
    <imagecapture-popup
      ref="imagecapturePopup"
      :face-list="faceList"
      :total-list-count="totalListCount"
      :face-loading="faceLoading"
      @startSearch="startSearch"
      @changePage="changePage"
      @changePageSize="changePageSize"
      @repeatImageShow="repeatImageShow"
      @popUpGetData="popUpGetData"
      @imageExceptMessageShow="showExceptMessage"
    >
    </imagecapture-popup>
    <!-- <repeat-tips-popup v-model="repeatImageView"> </repeat-tips-popup> -->
    <determine-popup v-model="determineResultShow" :table-list="determineList" :pop-up-title="determinePopUpTitle">
    </determine-popup>
    <image-tips-popup
      v-model="imageTips"
      :pop-up-title="popUpTitle"
      :nomal-label-list="nomalLabelList"
    ></image-tips-popup>
    <export-data-popup ref="exportDataPopup" :import-total-count="importTotalCount" @closeOpenModal="closeOpenModal">
    </export-data-popup>
    <div class="viewLoading">
      <loading v-if="viewLoading"></loading>
    </div>
  </div>
</template>
<script>
import faceEnum from './util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  name: 'FaceResult',
  provide() {
    return {
      // 流程图组件process-block组件调用
      openTypeModal: this.openTypeModal,
    };
  },
  props: {
    id: {
      type: Number,
    },
    importTotalCount: {},
  },
  data() {
    return {
      aggregateOptions: faceEnum.aggregateOptions,
      repeatImageView: false,
      determineResultShow: false,
      exportResultShow: false,
      searchData: {
        startTime: '',
        endTime: '',
        deviceIds: [],
        //reasons:[],
        //orgCode: null,
        topicComponentId: null,
        pageNumber: 1,
        pageSize: 20,
      },
      viewLoading: false,
      faceList: [],
      totalListCount: 0,
      imageTips: false,
      popUpTitle: '抓拍时间异常',
      nomalLabelList: [
        { name: '抓拍时间', value: '未知' },
        { name: '入库时间', value: '未知' },
        { name: '结论', value: '未知' },
      ],
      determineList: [],
      faceLoading: false,
      determinePopUpTitle: '非唯一人脸判定',
    };
  },
  async created() {
    this.copySearchDataMx(this.searchData);
    // await this.getViewList()
  },
  methods: {
    openTypeModal(name, option) {
      console.log(name, option);
      //this.$refs[name].init(name, option)
    },
    async popUpGetData(option) {
      this.resetSearchDataMx(this.searchData);
      this.searchData.topicComponentId = option.filedData.topicComponentId;
      this.modalExpectList();
    },
    // 查询人脸不合格原因
    async showExceptMessage(messageText, options, item) {
      this.popUpTitle = messageText.popUpTitle;
      try {
        let params = {
          faceLibAbnormalId: item.id,
          topicComponentId: options.filedData.topicComponentId,
          deviceId: item.deviceId,
        };
        let { data } = await this.$http.post(tasktracking.queryFaceLibAbnormalInfoResult, params);
        this.handleExceptResult(messageText, options, data.data);
      } catch (err) {
        console.log(err);
      }
    },
    handleExceptResult(messageText, options, data) {
      switch (options.title) {
        // 图像抓拍时间准确性检测
        case faceEnum.aggregateEnums.accurrency:
          this.imageCaptureTest(messageText, data);
          break;
        // 图像上传及时性检测
        case faceEnum.aggregateEnums.timeliness:
          this.imageUploadTest(messageText, data);
          break;
        // 大图URL检测
        case faceEnum.aggregateEnums.bigPic:
          let errorMessage = data.list.map((item) => item.errorMessage).join(',');
          this.$Message.warning(errorMessage);
          break;
        // 大小图关联正确检测
        case faceEnum.aggregateEnums.bigContactSmall:
          this.bigAndSmallPicTest(data);
          break;
        // 小图唯一人脸检测处理
        case faceEnum.aggregateEnums.smallOnly:
          this.smallOnlyTest(data);
          break;
      }
    },
    // 图像抓拍时间准确性检测
    imageCaptureTest(messageText, data) {
      this.nomalLabelList = messageText.nomalLabelList;
      this.imageTips = true;
      // 结论赋值
      let last = this.nomalLabelList[this.nomalLabelList.length - 1];
      let erroMessage = data.list
        .map((item) => {
          return item.errorMessage;
        })
        .join(',');
      last.value = erroMessage || '暂无';
    },
    // 图像上传及时性检测
    imageUploadTest(messageText, data) {
      this.nomalLabelList = messageText.nomalLabelList;
      let logTimeItem = this.nomalLabelList.find((item) => item.name === '抓拍时间');
      let createTimeItem = this.nomalLabelList.find((item) => item.name === '入库时间');
      logTimeItem.value = data.list[0].logTime;
      createTimeItem.value = data.list[0].createTime;
      let isTime = '';
      let isTimeType = '';
      if (data.isImportantDevice) {
        this.nomalLabelList[0].value = '重点人脸卡口';
        isTimeType = data.list[0].extraParamObj.importantTime;
        isTime = data.list[0].extraParamObj.important;
      } else {
        this.nomalLabelList[0].value = '普通人脸卡口';
        isTimeType = data.list[0].extraParamObj.generalTime;
        isTime = data.list[0].extraParamObj.general;
      }
      this.nomalLabelList = messageText.nomalLabelList;
      this.imageTips = true;
      let timeType = {
        '1': '时',
        '2': '分',
        '3': '秒',
      };
      if (!data.list.length) return;
      const delaytime = `${data.list[0].timeDifference}，超过${isTime}${timeType[isTimeType]}`;
      // 时延赋值
      let last = this.nomalLabelList[this.nomalLabelList.length - 1];
      last.value = delaytime;
    },
    // 大小图关联正确检测
    bigAndSmallPicTest(data) {
      this.determineResultShow = true;
      this.determinePopUpTitle = '大小图关联错误判定';
      if (!data.list.length) return;
      this.determineList = data.list[0].extraResultObj.map((item) => {
        return {
          name: item.key,
          value: item.value,
          isface: false,
        };
      });
    },
    // 小图唯一人脸检测处理
    smallOnlyTest(data) {
      this.determineResultShow = true;
      this.determinePopUpTitle = '非唯一人脸判定';
      if (!data.list.length) return;
      this.determineList = data.list[0].extraResultObj.map((item) => {
        return {
          name: item.key,
          value: item.value,
          isface: true,
        };
      });
    },
    handleData(aggregateList) {
      this.aggregateOptions = this.$util.common.deepCopy(faceEnum.aggregateOptions);
      // 面板数据拍平
      let allAggregateDatas = [];
      this.aggregateOptions.forEach((item) => {
        allAggregateDatas.push(...item.datas);
      });
      // 面板datas数据添加filedData字段（后端返回的所有数据）
      aggregateList.forEach((element, index) => {
        let isOne = allAggregateDatas[index];
        isOne.filedData = element;
        //isOne.title = element.componentName
        let staticListData = isOne.filedData.topicComponentStatistics;
        if (!staticListData || !staticListData.accessDataCount) return;
        // 成功数据需要前端计算
        isOne.list.forEach((item) => {
          if (item.fileName) {
            item.num = staticListData[item.fileName];
          }
          if (item.fileName === 'successData') {
            item.num = staticListData.accessDataCount - staticListData.existingExceptionCount;
          }
          if (item.fileName === 'successDataRate') {
            item.num =
              ((staticListData.accessDataCount - staticListData.existingExceptionCount) /
                staticListData.accessDataCount) *
              100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
          if (item.fileName === 'existingExceptionRate') {
            item.num = item.num * 100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
        });
      });
    },
    async getViewList() {
      try {
        this.viewLoading = true;
        let { data } = await this.$http.get(tasktracking.viewTackeAggregate, {
          params: { id: 2 },
        });
        this.handleData(data.data.componentList);
        this.viewLoading = false;
      } catch (err) {
        this.viewLoading = false;
        console.log(err);
      }
    },
    startSearch(val) {
      Object.assign(this.searchData, val);
      this.searchData.pageNumber = 1;
      this.modalExpectList();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.modalExpectList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.modalExpectList();
    },
    async modalExpectList() {
      try {
        this.faceLoading = true;
        let { data } = await this.$http.post(tasktracking.queryFaceLibAbnormalTaskPage, this.searchData);
        this.faceList = data.data.entities;
        this.totalListCount = data.data.total;
        this.faceLoading = false;
      } catch (err) {
        console.log(err);
        this.faceLoading = false;
      }
    },
    repeatImageShow() {
      this.repeatImageView = true;
    },
    closeOpenModal() {
      this.$router.push({ query: {} });
    },
  },
  watch: {},
  components: {
    FlowProcess: require('@/business-components/flow-process/index.vue').default,
    imagecapturePopup: require('./popup/imagecapture-popup.vue').default,
    // RepeatTipsPopup: require('./popup/repeat-tips-popup.vue').default,
    DeterminePopup: require('./popup/determine-popup.vue').default,
    ExportDataPopup: require('./popup/export-data-popup.vue').default,
    ImageTipsPopup: require('@/components/image-tips-popup.vue').default,
  },
};
</script>
<style lang="less" scoped>
.face-result {
  position: relative;
  width: 100%;
  height: 100%;
  background-image: url('~@/assets/img/thememanagement/traking-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
}
</style>
