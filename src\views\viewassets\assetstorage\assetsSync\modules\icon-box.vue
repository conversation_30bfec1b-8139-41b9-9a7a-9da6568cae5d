<template>
  <div class="icon-box">
    <icon-statics :icon-list="iconList"></icon-statics>
    <div>
      <Button
        v-if="tabsListObject[activeTabs] === '共享联网平台'"
        type="warning"
        class="mr-md"
        :loading="buttonLoading"
        @click="updateSyncDevice"
      >
        <i class="icon-font icon-xinxizidongtongbu f-12 mr-sm vt-middle"></i>
        <span class="vt-middle">获取平台设备</span>
      </Button>
      <Button type="warning" @click="$emit('bulkAssetsInstore')">
        <i class="icon-font icon-zichanruku f-12 mr-sm vt-middle"></i>
        <span class="vt-middle">资产入库</span>
      </Button>
    </div>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import assetsSync from '@/config/api/assetsSync';
import { tabsListObject } from '../util/enum.js';
import { mapGetters } from 'vuex';
export default {
  props: {
    activeTabs: {},
    orgId: {},
    searchParams: {},
  },
  data() {
    return {
      buttonLoading: false,
      tabsListObject: Object.freeze(tabsListObject),
      iconList: [
        {
          name: '同步总次数:',
          count: '0',
          countStyle: { color: 'var(--color-display-text)' },
          iconName: 'icon-tongbuzongcishu',
          fileName: 'asycTimes',
        },
        {
          name: '最新同步总量:',
          count: '0',
          countStyle: { color: 'var(--color-warning)' },
          iconName: 'icon-weichujieguozhibiaoshuliang',
          fileName: 'asycTotal',
        },
        {
          name: '新增:',
          count: '0',
          countStyle: { color: 'var(--color-success)' },
          iconName: 'icon-xinzeng',
          fileName: 'asycAdd',
        },
        {
          name: '差异:',
          count: '0',
          countStyle: { color: 'var(--color-failed)' },
          iconName: 'icon-xinxichayishebei',
          fileName: 'asycDiffer',
        },
        {
          name: '删除:',
          count: '0',
          countStyle: { color: 'var(--color-failed)' },
          iconName: 'icon-shanchu3',
          fileName: 'asycDel',
        },
      ],
      createTime: '未知',
    };
  },
  created() {},
  mounted() {
    this.queryNewAsycRecord();
    this.getSyncLoading();
  },
  methods: {
    async getSyncLoading() {
      try {
        const res = await this.$http.get(equipmentassets.queryUserOperateStatus, {
          params: {
            operate: 'catalog',
          },
        });
        this.buttonLoading = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async queryNewAsycRecord() {
      try {
        const { data } = await this.$http.get(assetsSync.queryNewAsycRecord, {
          params: { configType: this.activeTabs },
        });
        this.iconList.forEach((item) => {
          item.count = !!data.data && item.fileName in data.data ? data.data[item.fileName] : 0;
        });
        this.createTime = data?.data?.createTime || '未知';
      } catch (err) {
        console.log(err);
      }
    },
    async updateSyncDevice() {
      try {
        this.buttonLoading = true;
        let { data } = await this.$http.post(equipmentassets.catalogAndDeleteListData, {
          orgId: this.orgId,
          ...this.searchParams,
        });
        this.$Message.success(data.data);
        this.$emit('updateList');
      } catch (err) {
        console.log(err);
        this.buttonLoading = false;
      }
    },
  },
  watch: {
    activeTabs() {
      this.queryNewAsycRecord();
      this.getSyncLoading();
    },
    'notifyConfig.1000': {
      handler() {
        this.buttonLoading = false;
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      notifyConfig: 'websocket/getNotifyConfig',
    }),
  },
  components: {
    IconStatics: require('../components/icon-statics.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .icon-box {
    @{_deep}.ivu-btn-warning {
      background: linear-gradient(180deg, #ffcc65 -52%, #e77811 135%);
    }
  }
}
.icon-box {
  height: 60px;
  line-height: 60px;
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  border-bottom: 1px solid var(--devider-line);
  @{_deep}.ivu-btn-warning {
    background: linear-gradient(to bottom, #e5a014, #973e03);
  }
}
</style>
