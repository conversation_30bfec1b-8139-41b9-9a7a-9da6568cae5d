<template>
  <ui-card :title="title" class="involve-card">
    <div class="involve-box">
      <div class="card-small" v-for="(item, index) in list" :key="index">
        <div class="head-card">
          <i class="icon icon_alarm"></i>{{ item.caseNo }}
        </div>
        <div class="content-card">
          <div class="image">
            <ui-image :src="item.src" :type="'people'"></ui-image>
            <div
              class="label"
              :class="item.casePersonType == 1 ? 'criminal-suspect' : 'victim'"
            >
              {{ item.casePersonType == 1 ? "嫌疑人" : "受害人" }}
            </div>
          </div>
          <div class="info-list">
            <div class="info-item">
              <i class="iconfont icon-time"></i>
              <div>{{ item.reportingTime || "--" }}</div>
            </div>
            <div class="info-item">
              <i class="iconfont icon-leixing"></i>
              <div class="primary-text" style="text-wrap: auto">
                {{ item.desc || "--" }}
              </div>
            </div>
          </div>
          <div class="bg-image">
            <img src="@/assets/img/archives/juvenile_card_image.png" />
          </div>
        </div>
      </div>

      <ui-loading v-if="loading" />
      <ui-empty v-if="(!list || !list.length) && !loading" />
    </div>
  </ui-card>
</template>

<script>
export default {
  name: "involve",
  data() {
    return {
      alarmList: [
        {
          id: "J3213225523010500009",
          time: "2022-02-20  15:01:02",
          desc: "2022年10月17日15时，徐某报警其于XXXX被他人殴打",
          src: "http://*************:19002/qsdi/manager/excel/2025/02/11/120101199203070253_30033_0.jpg",
          identity: 1,
        },
        {
          id: "J3213225523010500009",
          time: "2022-02-20  15:01:02",
          desc: "2022年10月17日15时，徐某报警其于XXXX被他人殴打",
          src: "http://*************:19002/qsdi/manager/excel/2025/02/11/120101199203070253_30033_0.jpg",
          identity: 2,
        },
      ],
    };
  },
  props: {
    title: {
      type: String,
      default: "涉案/警记录",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
};
</script>

<style lang="less" scoped>
.involve-card {
  width: 100%;
}

.involve-box {
  display: flex;
  flex-wrap: nowrap;
  min-height: 166px;
  .card-small {
    width: 316px;
    background: #f9f9f9;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border: 1px solid #d3d7de;
    margin-right: 10px;
    .head-card {
      height: 30px;
      font-size: 16px;
      color: #2c86f8;
      display: flex;
      align-items: center;
      padding-left: 5px;
      border-bottom: 1px solid #d3d7de;
    }
    .content-card {
      padding: 10px;
      height: 135px;
      display: flex;
      gap: 10px;
      position: relative;
      .image {
        width: 115px;
        height: 115px;
        position: relative;
        .label {
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          box-shadow: 0px 7px 10px 0px rgba(221, 87, 30, 0.2);
          font-size: 12px;
          color: #ffffff;
          padding: 1px 5px;
        }
        .criminal-suspect {
          background: linear-gradient(144deg, #ff8c5b 0%, #d5210f 100%);
        }
        .victim {
          background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
        }
      }
      .info-list {
        width: 154px;
        .info-item {
          display: flex;
          text-wrap: nowrap;
          i {
            margin-right: 5px;
          }
          div {
            font-size: 14px;
            color: #484847;
          }
          .primary-text {
            color: rgba(0, 0, 0, 0.8);
          }
        }
      }
      .bg-image {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 118px;
        height: 104px;
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
.icon {
  width: 20px;
  height: 20px;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  margin-right: 10px;
  display: inline-block;
  &.icon_alarm {
    background-image: url("~@/assets/img/archives/icons/alarm-icon.png");
  }
}
</style>
