<!--
    * @FileDescription: 导出选择框
    * @Author: H
    * @Date: 2023/10/11
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-13 10:44:23
-->
<template>
  <div class="import_option" @click="($event) => $event.stopPropagation()">
    <div class="radio_box">
      <RadioGroup v-model="queryparams.type">
        <Radio label="1">{{ exportText }}</Radio>
        <Radio label="2" class="radio_box_select"
          >导出前
          <Input
            v-model="queryparams.downloadSize"
            placeholder="请输入"
            style="width: 70px"
          ></Input>
          条数据</Radio
        >
      </RadioGroup>
    </div>
    <div class="export_img" v-if="needPic">
      <Checkbox v-model="queryparams.downloadPics">图片</Checkbox>
    </div>
    <div class="export_btns">
      <!-- <Button type="primary" @click="searchHandle">确定</Button> -->
      <Button
        :loading="loading"
        type="primary"
        class="btn ok"
        @click="searchHandle"
      >
        {{ loading ? "等待中" : "确定" }}
      </Button>
      <Button class="resetBtn" @click="resetHandle">取消</Button>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    needPic: {
      type: Boolean,
      default: true,
    },
    // 整页数据
    wholePage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      queryparams: {
        type: "1",
        downloadSize: 100,
        downloadPics: false,
      },
      loading: false,
    };
  },
  computed: {
    exportText() {
      return this.wholePage ? "导出当前页数据" : "导出当前所选数据";
    },
  },
  methods: {
    searchHandle() {
      // this.loading = true;
      this.$emit("confirm", this.queryparams);
    },
    handleEnd() {
      this.loading = false;
    },
    resetHandle() {
      this.$emit("cancel");
    },
  },
};
</script>
<style lang="less" scoped>
.import_option {
  position: absolute;
  width: 250px;
  height: 188px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
  border-radius: 4px 4px 4px 4px;
  z-index: 12;
  font-size: 14px;
  border: 1px solid #d3d7de;
  top: 33px;
  .radio_box {
    margin: 0 16px;
    padding: 7px 0 11px;
    border-bottom: 1px solid #d3d7de;
    .radio_box_select {
      margin-top: 17px;
    }
  }
  .export_img {
    padding: 7px 0 7px 15px;
    border-bottom: 1px solid #d3d7de;
  }
  .export_btns {
    margin-top: 10px;
    text-align: center;
    .resetBtn {
      margin-left: 10px;
    }
  }
}
</style>
