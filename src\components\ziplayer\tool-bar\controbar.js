import ExpandScreen from "@/util/modules/extend-screen.js";
import { base64ToFile } from "@/util/modules/common.js";
import { imgUploadCloud } from "@/api/player";
import store from "@/store/index.js";

var videoTitle = "videoTitle"; //标题
var videoRate = "videoRate"; //速率
var videoRealHover = "videoRealHover";
var instantReplay = "instantReplay"; //回放
var videoZoom = "videoZoom";
var videoHisHover = "videoHisHover";
var slowPlay = "slowPlay"; //慢放
var videoPlay = "videoPlay"; //播放
var videoPause = "videoPause"; //暂停
var stepPlay = "stepPlay"; //单帧播放
var fastPlay = "fastPlay"; //快放
var curRate = "curRate"; //倍速
var fastBackward = "fastBackward";
var fastForward = "fastForward";
var videoTime = "videoTime";

var videoClose = (ctx) => {
  return {
    id: "videoClose",
    function: (obj) => {
      let index = obj.index;
      ctx.stopStream(index);
      if (index == ctx.videoObj.focusIndex) {
        ctx.isAutoNext = false;
        ctx.$refs.videoSearchRef.show(false);
      }
    },
  };
};

var fullScreen = (ctx) => {
  return {
    id: "fullScreen",
    type: "button",
    style: "right",
    className: "full-screen",
    show: true,
    tooltip: "全屏",
    function: (obj) => {
      setTimeout(() => {
        const element = document.querySelector(
          `.h5vp-video-containerEx[index="${obj.index}"]`
        );
        if (document.fullscreenElement || document.fullscreen) {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            /* Firefox */
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            /* Chrome, Safari and Opera */
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            /* IE/Edge */
            document.msExitFullscreen();
          }
        } else {
          if (element.requestFullscreen) {
            element.requestFullscreen();
            element.querySelector("video").style.width = "100%";
            element.querySelector("video").style.height = "100%";
          } else if (element.mozRequestFullScreen) {
            /* Firefox */
            element.mozRequestFullScreen();
            element.querySelector("video").style.width = "100%";
            element.querySelector("video").style.height = "100%";
          } else if (element.webkitRequestFullscreen) {
            /* Chrome, Safari and Opera */
            element.webkitRequestFullscreen();
            element.querySelector("video").style.width = "100%";
            element.querySelector("video").style.height = "100%";
          } else if (element.msRequestFullscreen) {
            /* IE/Edge */
            element.msRequestFullscreen();
            element.querySelector("video").style.width = "100%";
            element.querySelector("video").style.height = "100%";
          }
        }
      });
    },
  };
};

var videoFilter = (ctx) => {
  return {
    id: "videoFilter",
    type: "button",
    style: "right",
    className: "video-filter",
    show: true,
    tooltip: "色彩调节",
    menuList: {
      list: [],
      dom: ctx.createFilterComponent().$el,
    },
  };
};

var videoProgress = (ctx, iframes) => {
  return {
    id: "videoProgress",
    type: "progress",
    style: "progress",
    className: "video-progress",
    show: true,
    tooltip: "进度条",
    showFrameMark: iframes && iframes.length ? true : false,
    frameMark: {
      width: 320,
      height: 180,
      list: iframes,
    },
  };
};

var videoCustomProgress = (ctx, iframes) => {
  return {
    id: "videoCustomProgress",
    type: "customProgress",
    style: "progress",
    className: "video-progress",
    show: true,
    tooltip: "进度条",
    showFrameMark: iframes && iframes.length ? true : false,
    frameMark: {
      width: 320,
      height: 180,
      list: iframes,
    },
  };
};

var customSlowPlay = (ctx) => {
  return {
    id: "slowPlay", ////自定义customSlowPlay
    type: "button",
    style: "left",
    className: "slow-play",
    show: true,
    tooltip: "慢放",
    isExecuteDefaultFunc: true,
  };
};

var customFastPlay = (ctx) => {
  return {
    id: "fastPlay", //自定义customFastPlay
    type: "button",
    style: "left",
    className: "fast-play",
    show: true,
    tooltip: "快放",
    isExecuteDefaultFunc: true,
  };
};

var customFastBackward = (ctx) => {
  return {
    id: "customFastBackward",
    type: "button",
    style: "left",
    className: "fast-backward",
    show: true,
    tooltip: "快退",
    isExecuteDefaultFunc: true,
  };
};

var customFastForward = (ctx) => {
  return {
    id: "customFastForward",
    type: "button",
    style: "left",
    className: "fast-forward",
    show: true,
    tooltip: "快进",
    isExecuteDefaultFunc: true,
  };
};

var continuousShooting = (ctx) => {
  return {
    id: "continuousShooting",
    type: "button",
    style: "right",
    className: "continuous-shooting",
    show: true,
    tooltip: "画面连拍",
    function: (obj) => {
      let item = ctx.winList[obj.index];
      let count = 5;
      ctx.printScreenOption.title = "连拍";
      let imgUrls = [];
      ctx.videoObj.continuousShooting(
        obj.index,
        "jpeg",
        count,
        (number, data) => {
          if (number >= 0) {
            imgUrls.push({
              name: item.deviceName,
              url: data,
            });
            if (number == count - 1) {
              ctx.printScreenOption.imgUrls = imgUrls;
              ctx.printScreenOption.open = true;
            }
          } else {
            console.log("continuousShooting error ", data);
          }
        }
      );
    },
  };
};

var videoPrintScreen = (ctx) => {
  return {
    id: "videoPrintScreen",
    tooltip: "视频截图",
    className: "video-printscreen",
    function: (obj) => {
      let item = ctx.winList[obj.index];
      let imgUrl = ctx.videoObj.capturePicture(obj.index, "jpeg");
      if (imgUrl) {
        // 上传我的云盘
        uploadToCloud(imgUrl, item.deviceName).then((res) => {
          ctx.printScreenOption.title = "截图";
          ctx.printScreenOption.imgUrls = [
            {
              name: item.deviceName,
              url: imgUrl,
            },
          ];
          ctx.printScreenOption.open = true;
        });
      }
    },
  };
};

var videoRatio = (ctx) => {
  return {
    id: "videoRatio",
    className: "video-ratio",
    tooltip: "画面比例",
  };
};

var videoToMap = (ctx) => {
  return {
    id: "videoToMap",
    type: "button",
    style: "right",
    className: "video-tomap",
    show: true,
    tooltip: "地图定位",
    function: (obj) => {
      let index = obj.index;
      let item = ctx.winList[index];
      ExpandScreen.exMapDefaultPage(item);
    },
  };
};

var videoHisNormal = (ctx) => {
  return {
    id: "videoHisNormal",
    show: ctx.$_has(["video-history"]),
    function: (obj) => {
      let index = obj.index;
      ctx.videoObj.focusIndex = index;
      let item = ctx.winList[index];
      ctx.videoSearchOption.deviceId = item.deviceGbId
        ? item.deviceGbId
        : item.deviceId;
      ctx.videoSearchOption.stream = item.stream;
      ctx.$refs.videoSearchRef.show(true);
    },
  };
};

var sdHdPlay = (ctx) => {
  return {
    id: "sdHdPlay",
    selectId: "sdPlay",
    isExecuteDefaultFunc: false,
    function: function (obj) {
      console.log(ctx.videoObj.getVideoInfo(0));
    },
  };
};

var videoFrameMark = (ctx) => {
  return {
    id: "videoFrameMark",
    type: "button",
    style: "right",
    className: "video-framemark",
    show: true,
    tooltip: "帧标记",
    function: (obj) => {
      let imgUrl = ctx.videoObj.capturePicture(obj.index, "jpeg");
      if (imgUrl) {
        let index = obj.index;
        let item = ctx.winList[index];
        ctx.frameMarkOption = {
          open: true,
          src: imgUrl,
          name: "",
          absTime: ctx.videoObj._isVodPlay(obj.index)
            ? ctx.videoObj.getPlayPts(obj.index)
            : new Date().getTime(),
          color: "red",
          deviceId: item.deviceId,
        };
      }
    },
  };
};

var searchPictures = (ctx) => {
  return {
    id: "searchPictures",
    type: "button",
    style: "right",
    className: "search-pictures",
    show: true,
    tooltip: "以图搜图",
    function: (obj) => {
      let className = "search-pictures";
      if (obj.item.className == className) {
        obj.item.className = className + "-active";
        let dom = document.querySelector("." + className);
        dom.classList.remove(className);
        dom.classList.add(className + "-active");
        // 暂停播放
        ctx.videoObj.pause(obj.index, true);
        ctx.videoObj.startDrawing(obj.index, "rect", {
          lineColor: "rgba(255, 234, 75, 1)",
          cornerRadius: "5px",
        });
      } else {
        obj.item.className = className;
        let dom = document.querySelector("." + className + "-active");
        dom.classList.remove(className + "-active");
        dom.classList.add(className);
        ctx.videoObj.pause(obj.index, false);
        ctx.videoObj.stopDrawing(obj.index, true);
      }
    },
  };
};

var ptzControl = (ctx, isptz) => {
  return {
    id: "ptzControl",
    type: "button",
    style: "right",
    className: "ptz-control",
    show: ctx.$_has(["video-control"]) && isptz,
    tooltip: "云台控制",
    function: (obj) => {
      ctx.isShowPTZ = true;
    },
  };
};

var videoRealNormal = (ctx) => {
  return {
    id: "videoRealNormal",
    show: ctx.$_has(["video-realTime"]),
    function: (obj) => {
      let index = obj.index;
      let item = ctx.winList[index];
      ctx.playStream(item, "live", index);
    },
  };
};

var stepBack = (ctx) => {
  return {
    id: "stepBack",
    type: "button",
    style: "left",
    className: "step-back",
    show: true,
    tooltip: "单帧后退",
    function: (obj) => {
      ctx.videoObj.videos[obj.index].singleFrameBack();
    },
  };
};

var videoSearch = (ctx) => {
  return {
    id: "videoSearch",
    type: "button",
    style: "right",
    className: "video-search",
    show: true,
    tooltip: "历史调阅",
    function: (obj) => {
      let index = obj.index;
      ctx.videoObj.focusIndex = index;
      let item = ctx.winList[index];
      ctx.openVideoSearch(item);
    },
  };
};

var videoLock = (ctx) => {
  let isShow = false;
  let item = ctx.winList[ctx.videoObj.focusIndex];
  isShow = !!item.inspecting;
  return {
    id: "videoLock",
    type: "button",
    style: "right",
    className: "video-lock",
    show: isShow,
    tooltip: "锁定",
    function: (obj) => {
      store.commit("player/addLockedWin", obj.index);
      ctx.videoObj.showOrHideToolbar(obj.index, true, "videoUnlock");
      ctx.videoObj.showOrHideToolbar(obj.index, false, "videoLock");
    },
  };
};

var videoUnlock = (ctx) => {
  return {
    id: "videoUnlock",
    type: "button",
    style: "right",
    className: "video-unlock",
    show: false,
    tooltip: "解锁",
    function: (obj) => {
      store.commit("player/delLockedWin", obj.index);
      ctx.videoObj.showOrHideToolbar(obj.index, false, "videoUnlock");
      ctx.videoObj.showOrHideToolbar(obj.index, true, "videoLock");
    },
  };
};

// base64上传到我的云盘
const uploadToCloud = (base64Url, filename) => {
  return new Promise((resolve) => {
    let file = base64ToFile(base64Url, filename);
    const fd = new FormData();
    fd.append("file", file);
    fd.append("deviceName", filename);
    fd.append("userId", store.getters.userInfo.id);
    imgUploadCloud(fd).then((res) => {
      resolve(res);
    });
  });
};

export {
  videoTitle,
  videoRate,
  videoClose,
  fullScreen,
  videoFilter,
  continuousShooting,
  videoPrintScreen,
  videoRatio,
  videoToMap,
  videoRealHover,
  videoHisNormal,
  instantReplay,
  sdHdPlay,
  videoZoom,
  videoFrameMark,
  searchPictures,
  ptzControl,
  videoProgress,
  videoCustomProgress,
  videoRealNormal,
  videoHisHover,
  slowPlay,
  stepBack,
  videoPlay,
  videoPause,
  stepPlay,
  fastPlay,
  curRate,
  fastBackward,
  fastForward,
  videoSearch,
  videoTime,
  customSlowPlay,
  customFastPlay,
  customFastBackward,
  customFastForward,
  videoLock,
  videoUnlock,
};
