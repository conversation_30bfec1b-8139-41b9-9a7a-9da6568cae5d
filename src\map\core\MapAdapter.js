/**
 * 存放所有与地图操作相关的方法,
 * 最终操作地图一定是经过这个方法的
 */
export class MapAdapter {
  closeInfoWindow(win) {
    if (win != null) {
      win.close();
    }
  }
  addControl(control) {
    if (this.map != null) {
      this.map.addControl(control);
    }
  }

  createClusterMarker(location, opt) {
    return new NPMapLib.Symbols.ClusterMarker(location, opt);
  }

  /**
   * @param lon 经度 -> x
   * @param lat 纬度 -> y
   * @returns {NPMapLib.Geometry.Point}
   */
  getPoint(lon, lat) {
    return new NPMapLib.Geometry.Point(lon, lat);
  }

  /**
   * 添加一个图层
   * @param layer
   */
  addLayer(layer) {
    if (this.map != null) {
      this.map.addLayer(layer);
    }
  }

  /**
   * 添加图层组
   * @param layers
   */
  addLayers(layers) {}

  /**
   * 将覆盖物添加到地图中，一个覆盖物实例只能向地图中添加一次
   * @param overlay
   */
  addOverlay(overlay) {
    if (this.map != null) {
      this.map.addOverlay(overlay);
    }
  }

  /**
   * 将多个覆盖物添加到地图中
   * @param overlays
   */
  addOverlays(overlays) {
    if (this.map != null) {
      this.map.addOverlays(overlays);
    }
  }

  /**
   * 地图根据指定的点和级别进行对中
   * @param center
   * @param zoom
   */
  centerAndZoom(center, zoom) {
    if (this.map != null) {
      this.map.centerAndZoom(center, zoom);
    }
  }

  /**
   * 注册一个click事件，当鼠标在地图上点击时，地图会以鼠标点击位置为中心点重绘。
   */
  centerAtMouse() {}

  /**
   * 清除地图上所有覆盖物
   */
  clearOverlays() {}

  /**
   * 清除所有的信息窗口
   */
  closeAllInfoWindows() {}

  /**
   * 获取选中区域的要素
   */
  containFeatures() {
    return null;
  }

  /**
   * 销毁鼠标提示控件
   */
  deactivateMouseContext() {}

  /**
   * 销毁Map
   */
  destroyMap() {
    if (this.map != null) {
      this.map.destroyMap();
    }
  }

  /**
   * 禁用地图框选
   */
  disableBoxSelect() {}

  /**
   * 禁用双击放大
   */
  disableDoubleClickZoom() {}

  /**
   * 禁用修改
   */
  disableEditing() {}

  /**
   * 禁用地图惯性拖拽
   */
  disableInertialDragging() {}

  /**
   * 禁用键盘操作
   */
  disableKeyboard() {}

  /**
   * 禁用图层切换
   */
  disableLayerSwitcher() {}

  /**
   * 禁用
   */
  disableMapOperation() {}

  /**
   * 禁用滚轮放大缩小
   */
  disableScrollWheelZoom() {}

  /**
   * 启用地图框选
   */
  enableBoxSelect() {}

  /**
   * 启用双击放大，默认启用
   */
  enableDoubleClickZoom() {}

  /**
   * 启用修改
   */
  enableEditing() {}

  /**
   * 启用地图惯性拖拽，默认禁用
   */
  enableInertialDragging() {}

  /**
   * 启用键盘操作，默认启用
   */
  enableKeyboard() {}

  /**
   * 启用图层切换
   */
  enableLayerSwitcher() {}

  /**
   * 启用
   */
  enableMapOperation() {}

  /**
   * 启用滚轮放大缩小，默认启用
   */
  enableScrollWheelZoom() {}

  /**
   * 全图
   */
  fullExtent() {}

  /**
   * 获取所有图层
   * @returns {undefined}
   */
  getAllLayers() {
    if (this.map != null) {
      return this.map.getAllLayers();
    }
    return undefined;
  }

  /**
   * 返回地图当前中心点
   * @returns {undefined}
   */
  getCenter() {
    return undefined;
  }

  /**
   * 返回地图的容器元素
   * @returns {undefined}
   */
  getContainer() {
    return undefined;
  }

  /**
   * 返回地图鼠标指针样式
   * @returns {any}
   */
  getCursor() {
    if (this.map != null) {
      return this.map.getCursor();
    }
    return null;
  }

  /**
   * 获取默认图层
   * @returns {undefined}
   */
  getDefaultLayer() {
    return undefined;
  }

  /**
   * 返回两点之间的距离，单位是米
   * @param start
   * @param end
   * @returns {undefined}
   */
  getDistance(start, end) {
    return undefined;
  }

  /**
   * 返回地图可视区域，以地理坐标表示
   * @returns {undefined}
   */
  getExtent() {
    return undefined;
  }

  /**
   * 获取所有的信息窗口
   * @returns {undefined}
   */
  getInfoWindows() {
    return undefined;
  }

  /**
   * 获取图层，根据图层id
   * @param id
   * @returns {undefined}
   */
  getLayer(id) {
    return undefined;
  }

  /**
   * 获取图层，根据图层
   * @param name
   * @returns {undefined}
   */
  getLayerByName(name) {
    if (this.map != null) {
      return this.map.getLayerByName(name);
    }
  }

  /**
   * 获取地图ID
   * @returns {undefined}
   */
  getMapId() {
    if (this.map != null) {
      return this.map.getMapId();
    }
    return undefined;
  }

  /**
   * 获取点位的图片资源路径
   * @param point
   * @returns {undefined}
   */
  getMapTileUrl(point) {
    return undefined;
  }

  /**
   * 获取Map 的单位
   * @returns {undefined}
   */
  getMapUnits() {
    return undefined;
  }

  /**
   * 返回地图允许的最大缩放级别
   * @returns {undefined}
   */
  getMaxZoom() {
    if (this.map != null) {
      return this.map.getMaxZoom();
    }
    return null;
  }

  /**
   * 返回地图允许的最小缩放级别
   * @returns {undefined}
   */
  getMinZoom() {
    return undefined;
  }

  /**
   * 返回地图上的所有覆盖物
   * @returns {undefined}
   */
  getOverlays() {
    return undefined;
  }

  /**
   * 返回地图的投影方式。
   * @returns {undefined}
   */
  getProjection() {
    return undefined;
  }

  /**
   * 获取地图限制区域
   * @returns {undefined}
   */
  getRestrictedExtent() {
    return undefined;
  }

  /**
   * 返回地图视图的大小，以像素表示
   * @returns {undefined}
   */
  getSize() {
    if (this.map != null) {
      return this.map.getSize();
    }
    return undefined;
  }

  /**
   * 获取地图切片大小
   * @returns {undefined}
   */
  getTitleSize() {
    return undefined;
  }

  /**
   * 获取地图版本
   * @returns {undefined}
   */
  getVersion() {
    return undefined;
  }

  /**
   * 获取可见图层
   * @returns {undefined}
   */
  getVisibleLayers() {
    return undefined;
  }

  /**
   * 返回地图当前缩放级别
   * @returns {undefined}
   */
  getZoom() {
    if (this.map != null) {
      return this.map.getZoom();
    }
    return null;
  }

  /**
   * 平移
   */
  pan() {}

  /**
   * 将地图在水平位置上移动x像素，垂直位置上移动y像素
   * @param x
   * @param y
   */
  panByPixel(x, y) {}

  /**
   * 将地图的中心点更改为给定的点
   * @param center
   */
  panTo(center) {}

  /**
   * 像素坐标转换为经纬度坐标
   * @param pixel
   * @returns {any}
   */
  pixelToPoint(pixel) {
    if (this.map != null) {
      return this.map.pixelToPoint(pixel);
    }
    return null;
  }

  /**
   * 经纬度坐标转换为像素坐标
   * @param point
   * @returns {any}
   */
  pointToPixel(point) {
    if (this.map != null) {
      return this.map.pointToPixel(point);
    }
    return null;
  }

  /**
   * 从地图中移除控件。如果控件从未被添加到地图中，则该移除不起任何作用
   * @param control
   */
  removeControl(control) {}

  /**
   * 移除百度地图手的样式
   */
  removeHandStyle() {}

  /**
   * 移除一个图层，根据图层id
   * @param id
   */
  removeLayer(id) {
    if (this.map != null) {
      this.map.removeLayer(id);
    }
  }

  /**
   * 移除一个图层，根据图层name
   * @param name
   */
  removeLayerByName(name) {
    if (this.map != null) {
      this.map.removeLayerByName(name);
    }
  }

  /**
   * 从地图中移除覆盖物。如果覆盖物从未被添加到地图中，则该移除不起任何作用
   * @param overlay
   */
  removeOverlay(overlay) {
    if (this.map != null) {
      this.map.removeOverlay(overlay);
    }
  }

  /**
   * 从地图中移除覆盖物。如果覆盖物从未被添加到地图中，则该移除不起任何作用
   * @param overlays
   */
  removeOverlays(overlays) {}

  /**
   * 重新设置地图，恢复地图初始化时的中心点和级别
   */
  reset() {}

  /**
   * 设置基础图层
   * @param layer
   */
  setBaseLayer(layer) {}

  /**
   * 设置地图中心点
   * @param center
   * @param zoom
   */
  setCenter(center, zoom) {}

  /**
   * 设置地图鼠标指针样式
   * @param cursor
   */
  setCursor(cursor) {
    if (this.map != null) {
      this.map.setCursor(cursor);
    }
  }

  /**
   * 设置鼠标模式样式
   * @param cursor
   */
  setDefaultMapCursor(cursor) {}

  /**
   * 设置是否禁用浏览器事件，默认为true(禁止)
   * @param fallThrough
   */
  setFallThrough(fallThrough) {}

  /**
   * 设置地图风格样式，当前只能设置的参数是：灰色，浅绿，天蓝，粉色，经典黑 注意低版本IE 不支持
   * @param mapStyle
   */
  setMapStyle(mapStyle) {}

  /**
   * 设置地图允许的最大级别。取值不得大于地图类型所允许的最大级别
   * @param zoom
   */
  setMaxZoom(zoom) {}

  /**
   * 设置地图允许的最小级别。取值不得小于地图类型所允许的最小级别
   * @param zoom
   */
  setMinZoom(zoom) {}

  /**
   * 设置默认图层可见性
   * @param visibility
   */
  setVisibilityDefalutLayer(visibility) {}

  /**
   * 将视图切换到指定的缩放等级，中心点坐标不变
   * @param zoom
   */
  setZoom(zoom) {}

  /**
   * 切换图层。index为添加到地图时的顺序。
   * @param index
   */
  switchLayer(index) {}

  /**
   * 切换图层。layerName为添加到地图时的顺序。
   * @param layerName
   */
  switchLayerByName(layerName) {}

  /**
   * 调整画布大小
   */
  updateSize() {
    if (this.map != null) {
      this.map.updateSize();
    }
  }

  /**
   * 拉框放大
   */
  zoomIn() {}

  /**
   * 固定放大一个级别
   */
  zoomInFixed() {}

  /**
   * 取消拉框缩放
   */
  zoomInOutStop() {}

  /**
   * 拉框缩小
   */
  zoomOut() {}

  /**
   * 固定缩小一个级别
   */
  zoomOutFixed() {}

  /**
   * 缩放到指定范围
   * @param position
   * @param zoom
   */
  zoomTo(position, zoom) {}

  /**
   * 缩放到指定范围
   * @param extent
   */
  zoomToExtent(extent) {}

  /**
   * 添加事件监听函数
   * @param event
   * @param handler
   */
  addEventListener(event, handler) {
    if (this.map != null) {
      this.map.addEventListener(event, handler);
    }
  }

  /**
   * 移除事件监听函数
   * @param event
   */
  removeEventListener(event) {
    if (this.map != null) {
      this.map.removeEventListener(event);
    }
  }

  // 打开自定义地图信息窗
  openSpecialInfoWindow(lonlat, title, content, options, done, callback) {
    let ID = title + lonlat.lon + lonlat.lat;
    let defaults = {
      width: 170,
      height: 170,
      offset: new NPMapLib.Geometry.Size(0, 0),
      iscommon: false,
      enableCloseOnClick: false,
      autoSize: false,
      isAnimationOpen: false,
      isAdaptation: false,
      positionBlock: {
        imageSrc: '/libs/npgis/1.0.4.0/Netposa/img/iw_tail.png',
        imageSize: {
          width: 16,
          height: 12,
        },
        offset: new NPMapLib.Geometry.Size(-(160 / 2) - 8, -13),
      },
    };
    let settings = $.extend({}, defaults, options);
    let dialogElement = content;
    let $dialog = $(dialogElement).clone();
    let infoWindowContent = $dialog[0];
    let infoWindow = new NPMapLib.Symbols.InfoWindow(lonlat, title, infoWindowContent, settings);
    infoWindow.ID = ID;
    if (callback) {
      infoWindow.callback = callback;
    }
    if (done) {
      done();
    }
    infoWindow.infoWindowContent = infoWindowContent;
    this.map.addOverlay(infoWindow);
    infoWindow.open(null, false);
    return infoWindow;
  }
  //关闭自定义地图信息窗
  closeSpecialInfoWindow(win) {
    if (win != null) {
      win.callback && win.callback();
      win.close();
    }
  }
  //隐藏自定义地图信息窗
  hideSpecialInfoWindow(win) {
    if (win != null) {
      win.hide();
    }
  }
  //显示自定义地图信息窗
  showSpecialInfoWindow(win) {
    if (win != null) {
      win.show();
    }
  }

  map = null;

  constructor(map) {
    this.map = map;
  }
}
