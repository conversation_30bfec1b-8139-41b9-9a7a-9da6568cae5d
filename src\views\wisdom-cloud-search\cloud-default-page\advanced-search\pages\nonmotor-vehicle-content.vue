<!--
    * @FileDescription: 非机动车
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
	<div class="main-container">
		<div class="search-bar">
			<search-nonmotor ref="searchBar" @search="searchHandle" @reset="resetHandle" />
		</div>
		<div class="table-container">
			<div class="table-content">
				<div class="list-card box-1" v-for="(item, index) in dataList" :key="index">
					<div class="collection paddingIcon">
						<div class="bg"></div>
						<ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
						<ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
					</div>
					<p class="img-content">
						<span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
						<ui-image :src="item.traitImg" alt="动态库" @click.native="handleDetail(item, index)" />
						<!-- <img :src="item.traitImg" alt="" v-viewer /> -->
					</p>
					<div class="bottom-info">
						<time>
							<Tooltip content="抓拍时间" placement="right" transfer theme="light">
								<i class="iconfont icon-time"></i>
							</Tooltip>
							{{ item.absTime }}
						</time>
						<p>
							<Tooltip content="抓拍地点" placement="right" transfer theme="light">
								<i class="iconfont icon-location"></i>
							</Tooltip>
							<span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
						</p>
					</div>
					<!-- <div class="operate-bar">
						<p class="operate-content">
							<ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
							<ui-btn-tip content="分析" icon="icon-fenxi" />
							<ui-btn-tip content="布控" icon="icon-dunpai" transfer />
						</p>
					</div> -->
				</div>
				<div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
			</div>
			<ui-empty v-if="dataList.length === 0"></ui-empty>
			<ui-loading v-if="listLoading"></ui-loading>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange">
			</ui-page>
		</div>
		<details-modal v-show="humanShow" ref='nonmotor' @close="humanShow = false"></details-modal>
	</div>
</template>

<script>
	import detailsModal from '@/components/detail/details-modal.vue'
	import searchNonmotor from '../../ordinary-search/components/search-nonmotor.vue';
	import { queryNonmotorRecordSearch } from '@/api/wisdom-cloud-search';
	import { myMixins } from '../../components/mixin/index.js';
	export default {
		name: '',
		components: {
			searchNonmotor,
			detailsModal
		},
		mixins: [myMixins], //全局的mixin
		data() {
			return {
				dataList: [],
				listLoading: false,
				total: 0,
				pageInfo: {
					pageNumber: 1,
					pageSize: 20
				},
				humanShow: false,
				queryParam: {

				}
			}
		},
		watch: {

		},
		computed: {
			...mapGetters({
				getMaxLayer: 'countCoverage/getMaxLayer',
				getNum: 'countCoverage/getNum',
				getNewAddLayer: 'countCoverage/getNewAddLayer',
				getListNum: 'countCoverage/getListNum',
			}),
		},
		activated() {
			this.$nextTick(() => {
				this.queryList()
			})
		},
		created() {

		},
		mounted() {

		},
		methods: {
			searchHandle() {
				this.pageInfo.pageNumber = 1;
				this.queryList();
			},
			queryList() {
				let queryParam = this.$refs.searchBar.queryParam;
				queryParam = { ...queryParam, ...this.indexSearchData, ...this.pageInfo };
				let deviceIds = queryParam.selectDeviceList.map(item => {
					return item.deviceId
				})
				this.queryParam = queryParam;
				this.dispTime()
				this.queryParam.deviceIds = deviceIds;
				this.getDataList();
			},
			resetHandle() {
				this.pageInfo = {
					pageNumber: 1,
					pageSize: 20
				};
				let queryParam = this.$refs.searchBar.queryParam
				queryParam = { ...queryParam, ...this.pageInfo }
				this.queryParam = queryParam
                this.dispTime()
				this.getDataList()
			},
			async getDataList() {
				this.listLoading = true;
				queryNonmotorRecordSearch(this.queryParam)
					.then(res => {
						this.total = res.data.total;
						this.dataList = res.data.entities;
					})
					.finally(() => {
						this.listLoading = false
					})
			},
			// 详情
			handleDetail(row, index) {
				this.humanShow = true;
				this.$refs.nonmotor.init(row, this.dataList, index, 2)
			},
			collection(data, flag) {
				var param = {
					favoriteObjectId: data.recordId,
					favoriteObjectType: 17,
				}
				if (flag == 1) {
					addCollection(param).then(res => {
						this.$Message.success("收藏成功");
						this.getDataList()
					})
				} else {
					deleteMyFavorite(param).then(res => {
						this.$Message.success("取消收藏成功");
						this.getDataList()
					})
				}
			},
			...mapMutations({
				setNum: 'countCoverage/setNum',
				setList: 'countCoverage/setList',
			}),
			archivesPage() {

			},
			pageChange() {

			},
			pageSizeChange() {

			}
		}
	}
</script>

<style lang='less' scoped>
	@import "style/index";
</style>
