export class QSGptMain {
  gpt;
  gptDom;
  gptConfig;
  constructor({ gptDom, gptConfig }) {
    this.gptDom = gptDom;
    this.gptConfig = gptConfig;
  }

  init() {
    this.gpt = new QSGpt({ gptDom: this.gptDom, gptConfig: this.gptConfig });
    this.gpt.init();
  }

  addAnswerFilter(answerFilter) {
    this.gpt.addAnswerFilter(answerFilter);
  }

  addHistoryFilter(historyFilter) {
    this.gpt.addHistoryFilter(historyFilter);
  }

  setAnswerLoading({ loading, text }) {
    this.gpt.setAnswerLoading(loading, text);
  }

  getLastImageFile() {
    return this.gpt.getLastImageFile();
  }
}
