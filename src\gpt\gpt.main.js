export class QSGptMain {
  gpt;
  gptDom;
  gptConfig;
  constructor({ gptDom, gptConfig }) {
    this.gptDom = gptDom;
    this.gptConfig = gptConfig;
  }

  init(token) {
    this.gpt = new QSGpt({ gptDom: this.gptDom, gptConfig: this.gptConfig });
    this.gpt.init(token);
  }

  addAnswerFilter(answerFilter) {
    this.gpt.addAnswerFilter(answerFilter);
  }

  addHistoryFilter(historyFilter) {
    this.gpt.addHistoryFilter(historyFilter);
  }

  setAnswerLoading({ loading, text }) {
    this.gpt.setAnswerLoading(loading, text);
  }

  getLastImageFile() {
    return this.gpt.getLastImageFile();
  }
  setAudioToText(startCallback, endCallback) {
    return this.gpt.setAudioToText(startCallback, endCallback);
  }
  setAnswerCopy(callBack) {
    return this.gpt.setAnswerCopy(callBack);
  }
  setApiError(callBack) {
    return this.gpt.setApiError(callBack);
  }
  displayMessage(result) {
    return this.gpt.displayMessage(result);
  }
  setComponentFilter(callBack) {
    return this.gpt.setComponentFilter(callBack);
  }
}
