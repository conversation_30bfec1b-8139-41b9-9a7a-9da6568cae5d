<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" class="check-picture" footer-hide>
    <ui-label class="mr-lg inline" label="检测结果：" :width="70">
      <ui-switch-tab class="inline" v-model="tab" :tab-list="tagList" @changeTab="changeTab"></ui-switch-tab>
    </ui-label>
    <ui-label
      v-permission="{
        route: $route.name,
        permission: 'artificialreviewr',
      }"
      class="inline fr"
      label="异常原因"
      :width="70"
      v-if="[2001, 2002].includes(resultId.indexId) && searchData.outcome == '2'"
    >
      <Select v-model="searchData.causeError" @on-change="changeErrorReason" class="width-lg fr">
        <Option v-for="(item, index) in checkList" :key="index" :value="item.key">{{ item.value }}</Option>
      </Select>
    </ui-label>
    <div class="check-content-box auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
      <!-- 1:人脸卡口设备图片地址(url)可用率  2:人脸卡口设备及时上传率 3.人脸设备抓拍图片合格率(人脸卡口设备抓拍合格率) 4:人脸卡口设备时钟准确率-->
      <div class="check-content-wrap">
        <div class="ui-gather-card" v-for="(item, index) in tableData" :key="index">
          <div class="img">
            <div @click="viewBigPic(item.scenePath)" class="image-box">
              <ui-image :src="item.facePath" class="ui-image-card" />
            </div>
            <p
              v-permission="{
                route: $route.name,
                permission: 'artificialreviewr',
              }"
              v-if="[2001, 2002].includes(resultId.indexId)"
              class="shadow-artificial font-blue"
              style="z-index: 11"
              title="人工复核"
              @click="artificialReview(item)"
            >
              <i class="font-blue icon-font icon-xiajikaohedefen vt-middle f-14 mr-xs"></i>
              <span class="artificial-text f-14 font-blue">人工复核</span>
            </p>
          </div>
          <div class="ui-gather-card-image-item">
            <!-- 1:人脸卡口设备图片地址(url)可用率   3.人脸设备抓拍图片合格率(人脸卡口设备抓拍合格率) -->
            <!-- 时间 -->
            <p class="ellipsis" v-if="checkNum == 1 || checkNum == 3">
              <i class="icon-font icon-fabushijian base-text-color vt-middle mr-xs ui-gather-card-right-item-label"></i>
              <span class="ui-gather-card-right-item-label" :title="item.shotTime">{{ item.shotTime || '暂无' }}</span>
            </p>
            <!-- 地址 -->
            <p class="ellipsis" v-if="checkNum == 1 || checkNum == 3">
              <i class="icon-font icon-dizhi base-text-color vt-middle mr-xs ui-gather-card-right-item-label"></i>
              <span class="ui-gather-card-right-item-label" :title="item.address">{{ item.address || '暂无' }}</span>
            </p>
            <!--    访问时间        -->
            <p class="ellipsis" v-if="checkNum == 1 || checkNum == 3">
              <i class="icon-font icon-fabushijian base-text-color vt-middle mr-xs ui-gather-card-right-item-label"></i>
              <span class="ui-gather-card-right-item-label" :title="item.facePathVisitTime">{{
                item.facePathVisitTime ? `${item.facePathVisitTime} 毫秒` : '暂无'
              }}</span>
            </p>
            <!-- 2:人脸卡口设备及时上传率 -->
            <!-- 抓拍时间 -->
            <p class="" v-if="checkNum == 2">
              <i class="base-text-color vt-middle mr-xs ui-gather-card-right-item-label">抓拍时间:</i>
              <span class="ui-gather-card-right-item-label" :title="item.shotTime">{{ item.shotTime || '暂无' }}</span>
            </p>
            <!-- 接收时间 -->
            <p class="" v-if="checkNum == 2">
              <i class="base-text-color vt-middle mr-xs ui-gather-card-right-item-label">接收时间:</i>
              <span class="ui-gather-card-right-item-label" :title="item.receiveTime">{{
                item.receiveTime || '暂无'
              }}</span>
            </p>
            <!-- 2:人脸卡口设备时钟准确率 -->
            <!-- 抓拍时间 -->
            <p class="" v-if="checkNum == 4">
              <i class="base-text-color vt-middle mr-xs ui-gather-card-right-item-label">抓拍时间:</i>
              <span class="ui-gather-card-right-item-label" :title="item.shotTime">{{ item.shotTime || '暂无' }}</span>
            </p>
            <!-- 接收时间 -->
            <p class="" v-if="checkNum == 4">
              <i class="base-text-color vt-middle mr-xs ui-gather-card-right-item-label">接收时间:</i>
              <span class="ui-gather-card-right-item-label" :title="item.firstIntoViewTime">{{
                item.firstIntoViewTime || '暂无'
              }}</span>
            </p>
            <p class="">
              <!-- <i
                v-if="checkNum === 4 || checkNum === 2"
                class="icon-font icon-shujujiancegongju-01-01 f-14 vt-middle mr-xs ui-gather-card-right-item-label"
              ></i> -->
              <i
                v-if="checkNum === 1 || checkNum === 3"
                class="icon-font icon-shujujiancegongju-01-01 f-14 vt-middle mr-xs ui-gather-card-right-item-label"
              ></i>
              <span class="font-red" v-if="item.qualified != 1" :title="item.resultTip">{{ item.resultTip }}</span>
              <span class="font-green" v-if="item.qualified == 1" :title="item.resultTip">{{ item.resultTip }}</span>
            </p>
          </div>
        </div>
        <div class="empty-item" v-for="(empty, eIndex) in 8" :key="'empty' + eIndex"></div>
      </div>
    </div>
    <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    <!-- 人工复核 -->
    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <div class="artificial-data">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <Radio label="1">图片可用 </Radio>
            <Radio label="2" class="ml-lg">图片不可用 </Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block check-list" label="" :width="0" v-if="artificialData.qualified === '2'">
          <CheckboxGroup v-model="artificialData.errorCode">
            <Checkbox v-for="(item, index) in checkList" :key="index" :label="item.key">
              <span class="check-text">{{ item.value }}</span>
            </Checkbox>
          </CheckboxGroup>
        </ui-label>
        <ui-label class="block mt-sm" label="" :width="0">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>
      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
  </ui-modal>
</template>
<style lang="less" scoped>
.check-picture {
  @media screen and (max-width: 1366px) {
    .ui-gather-card {
      margin-right: 10px;
      width: 186px !important; /*no*/
      height: 220px !important; /*no*/
    }
    @{_deep}.ivu-modal {
      width: 1202px !important; /*no*/
    }
    .ui-image-card {
      height: 170px !important; /*no*/
      width: 170px !important; /*no*/
      cursor: pointer;
    }
    .image-box {
      @{_deep}.ui-image {
        z-index: initial;
        .ui-image-div {
          .tileImage {
            height: 170px !important; /*no*/
            width: 170px !important; /*no*/
          }
        }
      }
    }

    .icon-box {
      width: 91% !important; /*no*/
      top: 156px !important; /*no*/
    }
  }

  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  @{_deep} .ivu-modal-body {
    height: 100%;
  }
  @{_deep} .ivu-modal-content {
    width: 100%;
    height: 100%;
  }
  .check-content-box {
    height: 88%;
  }
  .check-content-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    overflow-y: auto;
    margin-top: 10px;
    .empty-item {
      width: 188px; /*no*/
    }
  }
  .ui-gather-card {
    width: 188px; /*no*/
    height: auto; /*no*/
    margin-bottom: 10px;
    padding: 10px;
    background: var(--bg-info-card);
    border: 1px solid var(--border-info-card);
    position: relative;
    .image-box {
      position: relative;
      @{_deep}.ui-image {
        z-index: initial;
        .ui-image-div {
          .tileImage {
            height: 190px;
            width: 100%;
          }
        }
      }
    }
    &-right {
      flex: 1;
      &-item {
        margin-bottom: 8px;
        font-size: 12px;
        &-label {
          color: #8797ac !important;
          font-size: 12px;
        }
        &-value {
          color: #8797ac !important;
          font-size: 12px;
        }
        .wrapper {
          width: 100%;
        }
      }
    }
  }
  .icon-box {
    position: absolute;
    width: 100%;
    bottom: 0;
    // z-index: 99;
    padding: 0 5px;
    text-align: center;
    line-height: 30px;

    background: rgba(0, 0, 0, 0.39);
    .icon-inner-box {
      background-color: transparent;
    }
    .pointer {
      width: 100%;
      text-align: center;
    }
  }
  .ui-gather-card-image-item {
    font-size: 14px;
    margin: 4px 0 4px 0;
  }
  .ui-image-card {
    height: 190px; /*no*/
    width: 100%;
    cursor: pointer;
  }
  .img:hover {
    .shadow-artificial {
      display: block;
    }
  }
  .check-list {
    // width: 460px;
    margin-left: 80px;
    margin-top: 10px;
  }
  .check-text {
    display: inline-block;
    // width: 110px;
  }
  .desc {
    margin-left: 80px;
    width: 80%;
  }

  .shadow-artificial {
    height: 28px;
    line-height: 29px;
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 172px;
    width: 90%;
    display: none;
    padding-left: 10px;
    .artificial-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
    @media screen and (max-width: 1366px) {
      .artificial-text {
        color: var(--color-primary);
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }
  .artificial-data {
    padding: 0 50px;
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  props: {
    checkData: {
      type: Array,
      default: () => [],
    },
    checkNum: {
      default: ''
    },
    // 接口名称
    interFaceName: {
      default: governanceevaluation.faceDetailPageList,
    },
    list: {
      type: Object,
      default: () => {},
    },
    tagList: {
      type: Array,
      default: () => [],
    },
    value: {
      required: true,
      type: Boolean,
    },
    resultId: {},
  },
  data() {
    return {
      checkList: [],
      visible: false,
      tab: 'URLAvailable',
      searchData: {
        outcome: 1,
        causeError: null,
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '1400px',
        height: '800px',
      },
      cardInfo: [
        { icon: 'icon-shijian', value: 'name', text: 'idCard' },
        { icon: 'icon-dizhi', value: 'idCard', text: 'idCard' },
      ],
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      tableData: [],
      loading: false,
      artificialStyles: {
        width: '3rem',
      },
      artificialData: { qualified: '1', reason: '', errorCode: [] },
      artificialVisible: false,
      artificialRow: {},
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialData.errorCode = [];
      this.artificialRow = row;
      this.artificialVisible = true;
    },

    async artificial() {
      let data = {};
      if (this.artificialData.qualified == '1') {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            deviceDetailId: this.artificialRow.faceDeviceDetailId,
          },
          indexId: this.resultId.indexId,
          batchId: this.resultId.batchId,
        };
      } else {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            errorCode: this.artificialData.errorCode.toString(),
            deviceDetailId: this.artificialRow.faceDeviceDetailId,
          },
          indexId: this.resultId.indexId,
          batchId: this.resultId.batchId,
        };
        if (this.artificialData.errorCode == '') {
          this.$Message.error('请选择异常原因');
          return;
        }
      }
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.artificialVisible = false;
        this.$emit('recount');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    changeErrorReason(val) {
      this.searchData.causeError = val;
      this.init();
    },
    async init() {
      try {
        let data = {
          indexId: this.resultId.indexId,
          batchId: this.resultId.batchId,
          access: this.resultId.access,
          displayType: this.resultId.displayType,
          orgRegionCode: this.resultId.orgRegionCode,
          customParameters: {
            outcome: this.searchData.outcome,
            causeError: this.searchData.causeError || null,
            faceDeviceDetailId: this.list.id,
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        !data.causeError && delete data.causeError;
        this.loading = true;
        this.tableData = [];
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(this, this.interFaceName, data, 'post', this.$route.query.cascadeId, {});
        // let res = await this.$http.post(this.interFaceName, data)
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    changeTab(val) {
      if (!val) return false;
      const checkObj = this.tagList.find((row) => row.value === val);
      let outcome = checkObj.outcome ? checkObj.outcome : '';
      let causeError = checkObj.causeError ? checkObj.causeError : '';
      this.$set(this.searchData, 'outcome', outcome);
      this.$set(this.searchData, 'causeError', causeError);
      this.init();
    },
    // tagChange1(val) {
    //   this.modelTag = val + 1
    //   this.init()
    // },
    // 大图展示
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = val;
      this.init();
    },
  },
  watch: {
    checkData: {
      handler(val) {
        if (val) {
          this.checkList = val;
        }
      },
      deep: true,
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      this.init();
    },
  },
  computed: {},

  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    LookScene: require('@/components/look-scene.vue').default,
  },
};
</script>
