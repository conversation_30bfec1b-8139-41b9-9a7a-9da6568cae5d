<template>
    <Table
        class="auto-fill table" 
        ref="table"
        :columns="columns" 
        :data="tableData"
    >
        <template #screen="{ row }">
            <span>{{ row.screen == 3 ? 'HDMI' : row.screen == 1 ? 'BNC': '' }}</span>
        </template>
    </Table>
</template>
<script>
import { demodifierScreen } from '@/api/config';
export default {
    props: {
        row: {
            type: Object,
            default:() =>{
                return {}
            }
        },
        videoWallId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableData: [],
            columns: [
                { title: '监视器编号', key: 'gbdecodeChanID' },
                { title: '窗口编号', key: 'window' },
                { title: '输出类型', slot: 'screen' },
            ],
        }  
    },
    watch:{
        row:{
            handler(val){
                if(val && val.id){
                    this.queryScreen(val);
                }
            },
            immediate:true
        } 
    },
    methods:{
        // 监视器信息
        queryScreen(data) {
            let params = {
                videoWallId: data.videoWallId,
                id: data.id,
                name: data.name,
                ip: data.ip,
                port: data.port,
                userName: data.userName,
                password: data.password
            }
            demodifierScreen(params)
            .then(res => {
                this.tableData = res.data;
            })
        },
    },
}
</script>
<style lang="less" scoped>

</style>