/**
 * 导出提示弹框
 * import downloadTip from '@/views/download-center/download-tip'
 * mixins: [downloadTip]
 */
import { mapActions, mapGetters } from 'vuex';
const mixin = {
  data() {
    return {
      _downloadTips: null,
    };
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
      setTipsNum: 'websocket/setTipsNum',
    }),
    $_openDownloadTip(
      { message, path, redirectParams } = {
        message: '如果文件过大，导出耗时过长，请稍后在【我的下载】页面下载',
        path: '/downloadcenter',
        redirectParams: null,
      },
    ) {
      this.setTipsNum(this.tipsNum + 1);
      // if (this._downloadTips) {
      //   this._downloadTips.close();
      // }
      const h = this.$createElement;
      this._downloadTips = this.$notify({
        title: '消息',
        customClass: `tips-notification-${this.tipsNum}`,
        onClose: () => {
          this.setTipsNum(this.tipsNum - 1);
        },
        message: h(
          'div',
          {
            style: {
              display: 'flex',
              alignItem: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              height: '100%',
            },
          },
          [
            h(
              'p',
              {
                style: {
                  position: 'relative',
                },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      width: '16px',
                      height: '16px',
                      borderRadius: '50%',
                      display: 'inline-block',
                      verticalAlign: 'middle',
                      lineHeight: '16px',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                    },
                  },
                  [
                    h('i', {
                      class: 'icon-font icon-xiaoxi1',
                      style: {
                        lineHeight: '16px',
                        color: 'var(--color-warning)',
                      },
                    }),
                  ],
                ),
                h(
                  'span',
                  {
                    style: {
                      display: 'inline-block',
                      marginLeft: '20px',
                      color: 'var(--color-content)',
                      maxHeight: '90px',
                      overflowY: 'auto',
                    },
                  },
                  message,
                ),
              ],
            ),
            // 如果是导出则显示去下载中心，如果是websocket则显示推送消息内容
            path
              ? path === '/downloadcenter'
                ? h(
                    'Button',
                    {
                      class: 'mt-lg',
                      style: 'width: 100%',
                      on: {
                        click: () => {
                          this.$_addTab({
                            path,
                            redirectParams,
                          });
                        },
                      },
                      props: {
                        type: 'text',
                      },
                    },
                    [
                      h(
                        'span',
                        {
                          style: {
                            color: 'var(--color-primary)',
                            textDecoration: 'underline',
                          },
                        },
                        '立即前往下载中心',
                      ),
                    ],
                  )
                : h(
                    'div',
                    {
                      style: {
                        display: 'flex',
                        alignItem: 'center',
                        justifyContent: 'center',
                      },
                    },
                    [
                      h(
                        'Button',
                        {
                          on: {
                            click: () => {
                              this.$_addTab({
                                path,
                                redirectParams,
                              });
                            },
                          },
                          props: {
                            type: 'text',
                          },
                        },
                        [
                          h(
                            'span',
                            {
                              style: {
                                color: 'var(--color-primary)',
                                textDecoration: 'underline',
                              },
                            },
                            '查看详情',
                          ),
                        ],
                      ),
                      h(
                        'Button',
                        {
                          class: 'ml-lg',
                          on: {
                            click: () => {
                              this.$_addTab({
                                path: '/mymessage',
                                redirectParams,
                              });
                            },
                          },
                          props: {
                            type: 'text',
                          },
                        },
                        [
                          h(
                            'span',
                            {
                              style: {
                                color: 'var(--color-primary)',
                                textDecoration: 'underline',
                              },
                            },
                            '查看全部消息',
                          ),
                        ],
                      ),
                    ],
                  )
              : null,
          ],
        ),
        position: 'bottom-right',
        duration: 0,
      });
    },
    $_closeDownloadTips() {
      this.setTipsNum(this.tipsNum - 1);
      this._downloadTips.close();
    },
    //添加标签
    $_addTab(route) {
      const lastIndex = route.path.lastIndexOf('/');
      const name = route.path.substring(lastIndex + 1, route.path.length);
      const index = this.cacheRouterList.findIndex((row) => row.name === name);
      if (index === -1 && name === 'downloadcenter') {
        this.cacheRouterList.push({
          name: name,
          path: `${route.path}`,
          text: '我的下载',
        });
        this.setCacheRouterList(this.cacheRouterList);
      } else if (index === -1 && name === 'mymessage') {
        this.cacheRouterList.push({
          name: name,
          path: `${route.path}`,
          text: '我的消息',
        });
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({
        name: name,
        query: route.redirectParams ? JSON.parse(route.redirectParams) : {},
      });
    },
  },
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
      tipsNum: 'websocket/getTipsNum',
    }),
  },
};
export default mixin;
