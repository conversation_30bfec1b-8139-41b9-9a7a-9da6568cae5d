<template>
  <div>
    <ui-modal ref="modal" title="图像URL检测配置">
      <CheckboxGroup v-model="selectFactrys">
        <Row>
          <Col span="12">
            <Checkbox label="大图可访问"></Checkbox>
          </Col>
          <Col span="12">
            <Checkbox label="小图可访问"></Checkbox>
          </Col>
        </Row>
      </CheckboxGroup>

      <template slot="footer">
        <Button @click="submit" type="primary"> 保&nbsp;存</Button>
      </template>
    </ui-modal>

    <!-- <ui-modal> -->
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  name: 'carConfig',
  props: {},
  data() {
    return {
      currentItem: null, // 当前操作的块
      selectFactrys: [], // 厂商v-model
      picList: [],
    };
  },
  async created() {},
  computed: {},
  methods: {
    init(processOptions) {
      // this.topicComponentId = processOptions.topicComponentId;
      // this.visible = true;
      // this.getUploadTime();
      this.showModal(processOptions);
    },
    init2() {
      this.selectFactrys = [];
      this.$http
        .get(api.queryTopicPropertyCheckList + '?topicComponentId=' + this.currentItem.topicComponentId)
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data;
            this.picList = list;
            list.forEach((item) => {
              if (item.componentCheckRuleId != null) {
                if (item.componentCheckRuleId == 28) {
                  this.selectFactrys.push('大图可访问');
                } else {
                  this.selectFactrys.push('小图可访问');
                }
              }
            });
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    showModal(item) {
      this.currentItem = item;
      this.$refs.modal.modalShow = true;
      this.init2();
    },

    submit() {
      if (this.selectFactrys.length == 0) {
        this.$Message.warning('至少选择一条');
        return;
      }
      var list = [];
      if (this.selectFactrys.length > 0) {
        this.selectFactrys.forEach((item) => {
          if (item == '大图可访问') {
            list.push({
              componentCheckRuleId: 28,
              topicComponentId: this.currentItem.topicComponentId,
            });
          }
          if (item == '小图可访问') {
            list.push({
              componentCheckRuleId: 35,
              topicComponentId: this.currentItem.topicComponentId,
            });
          }
        });
      }

      var param = {
        propertyCheckList: list,
        topicComponentId: this.currentItem.topicComponentId,
      };

      this.$http
        .post(api.updateImgUrl, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$refs.modal.modalShow = false;
            this.$Message.success('配置成功');
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.p {
  margin-top: 10px;
  color: #fff;
}
@{_deep} .ivu-modal-body {
  padding: 20px 51px 37px;
}
</style>
