/**
 * 获取设备所有标签
 */
import taganalysis from '@/config/api/taganalysis';
const mixin = {
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
  data() {
    return {
      MixinTagList: [],
    };
  },
  created() {
    this.MixinGetTagList();
  },
  methods: {
    // 查询所有标签
    async MixinGetTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.MixinTagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    MixinSelectInfo(infoList) {
      this.searchData.tagIds = infoList.map((item) => item.id);
    },
  },
};
export default mixin;
