<!--点击全选按钮会选中当前节点以及当前节点下的所有子节点-->
<template>
  <div class="inline select-organization-tree">
    <Dropdown trigger="custom" :visible="visible" v-clickoutside="dropHide">
      <div class="ivu-select ivu-select-single t-left">
        <div class="ivu-select-selection" @click="dropShow">
          <div class="select-content">
            <span class="ivu-select-placeholder" v-if="!selectTreeData.length">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-else>
              <span v-for="(item, index) in selectTreeData" :key="index">
                {{ item[defaultProps.label] }}
              </span>
            </span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow"></Icon>
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="treeList"
          :default-props="defaultProps"
          :default-checked-keys="defaultCheckedKeys"
          :expand-all="isExpandAll"
          checkStrictly
          @selectTree="selectTree"
        >
          <template #label="{ node, data }">
            <div class="options" :class="data.children && 'is_parent'">
              <Checkbox
                v-model="data.check"
                class="mr-sm"
                :disabled="data.disabled"
                @on-change="check($event, node, data)"
              >
                <span>{{ data[defaultProps.label] }}</span>
              </Checkbox>
              <Button
                v-if="!data.disabled"
                class="mr-md"
                type="text"
                @click="checkAll(node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
              >
                {{ `${data.checkAll ? '取消全选' : '全选'} ` }}
              </Button>
            </div>
          </template>
        </ui-search-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'select-organization-tree-checkall',
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    nodeKey: {
      type: String,
      default: 'orgCode',
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    placeholder: {
      type: String,
      default: '请选择组织机构',
    },
    // 点击全选是否选中孙节点
    isSelectGrandchild: {
      type: Boolean,
      default: true,
    },
    isExpandAll: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      codes: [],
      selectTreeData: [],
      treeList: [],
    };
  },
  methods: {
    dropShow() {
      this.visible = !this.visible;
    },
    dropHide() {
      this.visible = false;
    },
    check(check, node) {
      this.$set(node.data, 'check', check);
      const codeIndex = this.codes.indexOf(node.data[this.nodeKey]);
      if (!check && codeIndex !== -1) {
        this.codes.splice(codeIndex, 1);
        this.selectTreeData.splice(codeIndex, 1);
      } else {
        this.codes.push(node.data[this.nodeKey]);
        let obj = {};
        obj[this.defaultProps.label] = node.data[this.defaultProps.label];
        obj[this.nodeKey] = node.data[this.nodeKey];
        this.selectTreeData.push(obj);
      }
      if (this.isSelectGrandchild) this.handleCancelCheckAll();
      this.$emit('getSelectTree', this.codes);
    },
    // 点击全选按钮,会选中当前节点的所有子节点
    checkAll(node, data) {
      // 判断当前节点是否有子节点，有则将子节点数据扁平化处理checked值
      if (node.childNodes) {
        if (!data.checkAll) {
          this.handleCheckNodeChildren(data.children, true);
          this.$set(node.data, 'checkAll', true);
          this.$set(node.data, 'check', true);
        } else {
          this.handleCheckNodeChildren(data.children, false);
          this.$set(node.data, 'checkAll', false);
          this.$set(node.data, 'check', false);
          const codeIndex = this.selectTreeData.findIndex((item) => item[this.nodeKey] === data[this.nodeKey]);
          this.selectTreeData.splice(codeIndex, 1);
        }
        if (this.isSelectGrandchild) this.handleCancelCheckAll();
      }
    },
    handleCheckNodeChildren(childNodes, check) {
      if (childNodes.length) {
        for (let i = 0; i < childNodes.length; i++) {
          this.$set(childNodes[i], 'check', check);
          if (this.isSelectGrandchild) this.$set(childNodes[i], 'checkAll', check);
          if (!check) {
            const codeIndex = this.selectTreeData.findIndex(
              (item) => item[this.nodeKey] === childNodes[i][this.nodeKey],
            );
            this.selectTreeData.splice(codeIndex, 1);
          }
          if (!!childNodes[i].children && childNodes[i].children.length && this.isSelectGrandchild) {
            this.handleCheckNodeChildren(childNodes[i].children, check);
          }
        }
      }
    },
    selectTree(data) {
      if (data.checkAll) {
        const selectTreeData = this.$util.common.jsonToArray([JSON.parse(JSON.stringify(data))]);
        let checkData = [];
        if (this.isSelectGrandchild) {
          checkData = selectTreeData;
        } else {
          checkData = selectTreeData.filter((item) => !!item.check);
        }
        const seletDatas = checkData.map((item) => {
          let obj = {};
          obj[this.defaultProps.label] = item[this.defaultProps.label];
          obj[this.nodeKey] = item[this.nodeKey];
          return obj;
        });
        seletDatas.forEach((item) => {
          if (!this.codes.includes(item[this.nodeKey])) {
            this.selectTreeData.push(item);
          }
        });
      }
      this.codes = this.selectTreeData.map((item) => item[this.nodeKey]);
      this.$emit('getSelectTree', this.codes);
    },
    handleCancelCheckAll() {
      // 对树进行一次处理
      this.treeList = this.treeList.map((item) => {
        return this.handleCancelChildren(item);
      });
    },
    handleCancelChildren(item) {
      // 取消全选时判断树是否有子节点
      if (!!item.children && item.children.length) {
        // 当前节点有字节点，则判断当前节点的子节点数和已选中的子节点数比较，不相等则将当前节点的【checkAll】值置成false
        // 将当前节点的所有子节点扁平化
        const selectData = this.$util.common.jsonToArray(JSON.parse(JSON.stringify(item.children)));
        // 在已扁平化的子节点里过滤出已选中的子节点
        const childCheckedLen = selectData.filter((childItem) => childItem.check);
        // 将所有子节点数和已选中的子节点数进行比较， 相等则将当前节点的【checkAll】值置成 true ，否则 false
        if (selectData.length === childCheckedLen.length) {
          this.$set(item, 'checkAll', true);
        } else {
          this.$set(item, 'checkAll', false);
        }
        // 对当前节点还有子节点的进行递归处理
        item.children.forEach((childItem) => {
          this.handleCancelChildren(childItem);
        });
        // 返回处理后的数据
        return item;
      }
    },
    handleCancelCheck() {
      this.codes = [];
      this.selectTreeData = [];
      let treeArr = this.$util.common.jsonToArray(JSON.parse(JSON.stringify(this.treeList)));
      treeArr = treeArr.map((item) => {
        item.check = false;
        item.checked = false;
        return item;
      });
      this.treeList = this.$util.common.arrayToJson(treeArr, 'id', 'parentId');
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  watch: {
    treeData: {
      handler(val) {
        this.selectTreeData = [];
        if (!this.defaultCheckedKeys.length) {
          this.treeList = JSON.parse(JSON.stringify(val));
          return false;
        }
        let treeArr = this.$util.common.jsonToArray(JSON.parse(JSON.stringify(val)));
        treeArr = treeArr.map((item) => {
          if (this.defaultCheckedKeys.includes(item[this.nodeKey])) {
            this.selectTreeData.push(item);
            item.check = true;
          }
          return item;
        });
        this.treeList = this.$util.common.arrayToJson(treeArr, 'id', 'parentId');
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.select-organization-tree {
  position: relative;
  width: 100%;

  .tree {
    overflow-x: auto;
    font-size: 12px;
    max-height: 280px;
  }

  .ivu-select-selection {
    width: 100%;
    height: 32px;
    line-height: 32px;

    .select-content {
      height: 32px;
      line-height: 32px;

      .ivu-select-placeholder {
        height: 32px;
        line-height: 32px;
      }

      .ivu-select-selected-value {
        height: 32px;
        line-height: 32px;
      }
    }
  }

  .is_parent {
    display: flex;
    justify-content: space-between;
  }

  @{_deep} {
    .ivu-dropdown {
      width: 100%;

      .ivu-select-dropdown {
        min-height: 280px;
        max-height: 280px;
        top: 40px !important;
        padding-top: 0 !important;
        overflow: auto;
      }
    }

    .ui-search-tree {
      padding-top: 0 !important;

      .el-tree {
        overflow: hidden !important;
      }
    }
  }
}
</style>
