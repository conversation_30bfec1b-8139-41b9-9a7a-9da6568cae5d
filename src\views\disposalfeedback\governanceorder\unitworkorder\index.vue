<template>
  <div>
    <div v-if="!tabComponentName" class="unit-workorder-container">
      <div class="flex-row">
        <api-organization-tree
          ref="orgTree"
          class="mr-sm"
          :select-tree="searchData"
          :custorm-node="false"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
        <tag-view
          :list="permissionTagList"
          :default-active="defaultTag"
          @tagChange="changeStatus"
          ref="tagView"
        ></tag-view>
      </div>
      <div class="devider-line"></div>
      <div class="search-bar">
        <div class="flex-aic">
          <underline-menu
            v-model="searchData.queryType"
            :data="assignList"
            @on-change="onChangeUnderlineMenu"
          ></underline-menu>
          <Checkbox
            v-model="searchData.isOwnOrgCode"
            true-value="1"
            false-value="0"
            v-if="searchData.type === TYPE_ALLORDER && searchData.queryType === QUERYTYPE_ASSIGN_UNIT"
            class="flex-aic"
            @on-change="changeOwnerOrder"
          >
            <span>本单位在手工单</span>
            <Tooltip
              max-width="300"
              content="【本单位在手工单】仅包含当前指派给本单位处理的工单！取消勾选，系统则会统计当前指派给本单位处理和下发给下级单位处理的所有工单！"
            >
              <i class="icon-font icon-wenhao f-14 ml-xs font-dark-blue" @click.stop.prevent></i>
            </Tooltip>
          </Checkbox>
        </div>
        <ui-label class="inline mr-lg" label="创建时间：">
          <ui-radio-time :time-radio="timeRadio" @selectTime="selectTime"></ui-radio-time>
        </ui-label>
      </div>
      <ui-switch-drop
        v-if="componentName === 'all-work-order'"
        class="ui-switch-tab mt-md mb-md"
        v-model="queryConditionStatus"
        :tab-list="stateOptions"
        @changeTab="onChangeSwitchDrop"
      >
      </ui-switch-drop>
      <div
        class="component-container auto-fill"
        v-ui-loading="{ tableData: [...permissionTagList, ...permissionDeviceTypeList] }"
      >
        <component
          ref="workOrder"
          :key="searchData.type"
          :is="componentName"
          :statistics-type="permissionTagList.length > 0 && permissionTagList[defaultTag]"
          :common-search-data="searchData"
          :query-condition-status="queryConditionStatus"
          :statistics-detail="statisticsDetail"
          @onStatisticsClick="onClickLink"
          @click-org="onOrgClickLink"
          @on-change-table-data="onChangeTableData"
        >
        </component>
      </div>
      <create-tabs
        ref="createTabsRef"
        :componentName="themData.componentName"
        :tabs-text="themData.text"
        :tabs-query="themData.tabsQuery"
        @selectModule="selectModule"
        class="inline btn-text-default"
      >
      </create-tabs>
    </div>
    <keep-alive>
      <component :is="tabComponentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import {
  stateOptionsMap,
  deviceTypeList,
  unitWorkOrderTagList,
  ALLORDER,
  UNIT_CREATION,
  ASSIGN_UNIT,
} from '../util/enum.js';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'unitworkorder',
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      queryConditionStatus: 'qb',
      componentName: null,
      searchData: {
        type: ALLORDER,
        orgCode: '000000',
        queryType: UNIT_CREATION,
        beginTime: '',
        endTime: '',
        isOwnOrgCode: '0', //本单位在手工单
      },
      permissionDeviceTypeList: [],

      permissionTagList: [],
      timeRadio: {
        title: '',
        value: 'all',
        timeList: [
          { label: '今日', value: 'today' },
          { label: '本周', value: 'thisWeek' },
          { label: '本月', value: 'thisMonth' },
          { label: '今年', value: 'thisYear' },
          { label: '全部', value: 'all' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
      },
      assignList: [
        /**
         * @ApiModelProperty("查询类型（为空查询所有，1 单位创建，2 指派给单位，3 当前指派给我，4 我创建的，5 我签收的，6 我处理的，7 我关闭的）")
         * private Integer queryType;
         */
        { code: '1', label: '单位创建' },
        { code: '2', label: '指派给单位' },
      ],
      stateOptions: [],
      defaultTag: 0,
      statisticsDetail: null,
      themData: {
        componentName: 'unitorderdetail', // 需要跳转的组件名
        text: '工单详情', // 跳转页面标题
        type: 'view',
        tabsQuery: {},
      },
      tabComponentName: '',
      TYPE_ALLORDER: ALLORDER,
      QUERYTYPE_UNIT_CREATION: UNIT_CREATION, //单位创建
      QUERYTYPE_ASSIGN_UNIT: ASSIGN_UNIT, //指派给单位
    };
  },
  created() {
    this.getParams();
  },
  async mounted() {
    this.setOrgTreeDefaultNode();
    this.stateOptions = stateOptionsMap[this.searchData.queryType];
    this.permissionDeviceTypeList = deviceTypeList;
    this.permissionTagList = unitWorkOrderTagList;
    await this.$nextTick();
    if (!this.$route.params?.purpose) {
      this.changeStatus(0);
    }
  },
  watch: {},
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (Object.keys(to.params).length) {
        vm.receiveParamsFromOtherPages(to.params);
      }
    });
  },
  methods: {
    async onClickLink(row, column) {
      let param = {
        purpose: 'pickTaskOrder',
        orderTaskId: row.orderTaskId || row.parentOrderTaskId,
        taskName: row.parentOrderTaskName || row.taskName,
        orgCode: row.orgCode,
        columnKey: column.key,
        queryConditionStatus: column.queryConditionStatus,
        defaultOrg: this.searchData.orgCode,
        queryType: this.searchData.queryType,
      };
      Object.assign(this.themData.tabsQuery, param);
      this.$refs.createTabsRef.create();
    },
    onOrgClickLink(row) {
      if (this.searchData.orgCode === row.orgCode) return;
      this.searchData.orgCode = row.orgCode;
      this.$refs.workOrder.search();
    },
    selectedOrgTree(area) {
      this.searchData.orgCode = area.orgCode;
      this.$refs.workOrder.search();
    },
    removeTooltipNode() {
      let tooltipNode = document.querySelectorAll('.ivu-tooltip-popper, .tips-box');
      tooltipNode.forEach((node) => {
        node.style.display = 'none';
      });
    },
    async changeStatus(index, item) {
      if (!this.permissionTagList?.length) {
        this.permissionTagList = unitWorkOrderTagList;
      }
      this.defaultTag = index;
      this.componentName = this.permissionTagList[index]['component'];
      this.removeTooltipNode();
      if (item && item.component === 'all-work-order') {
        await this.$nextTick();
        //如果item有值说明从组件点击传出的事件
        this.$refs.workOrder.search();
      }
    },
    selectTime(timeRadio) {
      this.searchData.beginTime = timeRadio.startTime;
      this.searchData.endTime = timeRadio.endTime;
      this.$refs.workOrder.search();
    },
    async onChangeUnderlineMenu(val) {
      this.stateOptions = stateOptionsMap[val];
      this.queryConditionStatus = 'qb';
      await this.$nextTick();
      this.$refs.workOrder.search();
    },
    async onChangeSwitchDrop(val) {
      try {
        this.queryConditionStatus = val;
        await this.$nextTick();
        this.$refs.workOrder.search();
      } catch (e) {
        console.log(e);
      }
    },
    async onChangeTableData({ total }) {
      let index = this.stateOptions.findIndex((item) => item.value === this.queryConditionStatus);
      if (index !== -1) {
        this.$set(this.stateOptions[index], 'total', total);
      }
    },
    //选中本单位在手工单触发
    changeOwnerOrder() {
      this.$refs.workOrder.search();
    },
    //设置单位筛选默认选中
    setOrgTreeDefaultNode() {
      const { orgCode } = this.defaultSelectedOrg;
      if (this.$refs.orgTree) {
        this.$refs.orgTree.selectTree.orgCode = orgCode || this.initialOrgList[0]?.orgCode;
      }
    },
    //接收从其他页面过来的数据
    receiveParamsFromOtherPages(params) {
      if (!params.purpose) {
        return;
      }
      //所有路由跳转的操作
      switch (params.purpose) {
        case 'openOrderDetailModalById':
          this.openOrderDetailModal(params);
          break;
        case 'viewStatisticsDetail':
          this.viewStatisticsDetail(params);
          break;
      }
    },
    //打开工单详情
    async openOrderDetailModal(params) {
      this.defaultTag = 2;
      this.componentName = 'all-work-order';
      await this.$nextTick();
      if (this.$refs.workOrder) {
        this.$refs.workOrder.editViewAction = { row: { id: params.detailId }, type: 'view' };
        this.$refs.workOrder.editViewOrderShow = true;
      }
    },
    async viewStatisticsDetail(params) {
      this.changeStatus(1); //切换到工单模式
      await this.$nextTick();
      this.searchData.queryType = params.queryType
      this.queryConditionStatus = params.queryConditionStatus
      this.searchData.orgCode = params.orgCode
      
      //清空search-and-button的searchdata和workorder的searchdata
      if(this.$refs.workOrder?.$refs.searchButtonRef){
        this.$refs.workOrder.$refs.searchButtonRef.searchData = {}
        this.$refs.workOrder.assingSearchData(this.$refs.workOrder.$refs.searchButtonRef.defaultSearchData)
      }
      // 赋值相关指标搜索条件
      if(params.indexId && params.indexModule) {
        this.$refs.workOrder.$refs.searchButtonRef.searchData.indexModule =  params.indexModule;
        this.$refs.workOrder.$refs.searchButtonRef.onChangeIndexModule(params.indexModule);
        if(params.indexModule !== '100'){
          this.$refs.workOrder.$refs.searchButtonRef.searchData.indexId =  params.indexId;
          this.$refs.workOrder.$refs.searchButtonRef.onChangeIndexId(params.indexId);
          this.$refs.workOrder.assingSearchData({
            indexModule: params.indexModule,
            indexId: params.indexId
          })
        }else{
          this.$refs.workOrder.assingSearchData({
            indexModule: params.indexModule,
          })
        }
      }
      await this.$nextTick();
      this.$refs.workOrder.search();
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.tabComponentName = name;
      } else {
        this.tabComponentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiTabsWrapped: require('@/components/ui-tabs-wrapped.vue').default,
    TagView: require('@/components/tag-view.vue').default,
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    AllWorkOrder: require('@/views/disposalfeedback/governanceorder/module/all-work-order.vue').default,
    UiSwitchDrop: require('@/components/ui-switch-drop/ui-switch-drop.vue').default,
    UnderlineMenu: require('@/components/underline-menu').default,
    AllTaskOrder: require('@/views/disposalfeedback/governanceorder/unitworkorder/all-task-order.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    unitorderdetail: require('../unitorderdetail/index.vue').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.unit-workorder-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-content);
  padding: 10px 20px 0 20px;

  display: flex;
  flex-direction: column;
  .devider-line {
    background: var(--devider-line);
    height: 1px;
    margin: 10px 0;
  }
  .search-bar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    border-radius: 4px;
    background: var(--bg-navigation);

    @{_deep} .underline-menu-wrapper {
      background: transparent;
      width: auto;
      .tab {
        font-size: 14px;
      }
      .tab:before {
        content: none;
      }
    }
  }
  @{_deep} .custorm-tree-node {
    height: 26px;
    line-height: 26px;
  }
}
</style>
