<template>
  <CommunityPeople
    :bizLibType="[3]"
    :isNewGoInfo="true"
    :goInfo="goArchivesInfo"
  ></CommunityPeople>
</template>

<script>
import CommunityPeople from "@/views/holographic-archives/one-person-one-archives/juvenile-archive/index.vue";
export default {
  name: "CommunityPeopleArchive",
  components: { CommunityPeople },
  data() {
    return {};
  },
  methods: {
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        path: "/community-archive/people-dashboard",
        query: {
          archiveNo: item.archiveNo,
          source: "people",
          initialArchiveNo: item.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped></style>
