<template>
  <div class="camera-module">
    <ui-modal v-model="visible" title="选择摄像机" :styles="style">
      <Row class="camera-content height-full">
        <Col :span="6" class="tree height-full">
          <ui-search-tree
            ref="uiSearchTree"
            :scroll="375"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-keys="defaultExpandedKeys"
            :show-checkbox="showCheckbox"
            :node-key="nodeKey"
            @selectTree="selectTree"
            @check="check"
          >
          </ui-search-tree>
        </Col>
        <Col :span="12" class="camera-list height-full auto-fill">
          <div class="select-top">
            <span class="fl base-text-color"
              >摄像机列表 <span class="red-color">{{ cameraList.length }}</span> 个</span
            >
            <div class="fr">
              <span class="ml-sm">
                <Input
                  search
                  placeholder="请输入摄像机名称"
                  class="width-md"
                  v-model="searchText"
                  @on-search="searchCamaraAdd"
                  @on-enter="searchCamaraAdd"
                />
              </span>
              <span class="ml-md">
                <Checkbox v-model="mainChecked" @on-change="chooseChecked">
                  <span class="base-text-color">全部</span>
                </Checkbox>
              </span>
            </div>
          </div>
          <div class="pt-sm select-content" ref="cameraBox" v-ui-loading="{ loading: loading, tableData: cameraList }">
            <div
              v-for="(item, index) in viewCameraList"
              :key="index"
              :title="item.deviceName"
              class="mr-sm mb-xs base-text-color inline camera-item ellipsis"
            >
              <Checkbox v-if="showCheckbox" v-model="item.checked" @on-change="selectCamera(item)"></Checkbox>
              {{ item.deviceName }}
            </div>
          </div>
        </Col>
        <Col :span="6" class="selected-camera height-full auto-fill">
          <div class="selected-title over-flow">
            <span class="base-text-color fl"
              >已选 <span class="red-color">{{ selectedCameraList.length }}</span> 个</span
            >
            <span class="table-text-content fr pointer empty" @click="unSelectAll">清空</span>
          </div>
          <div class="selected-list auto-fill" ref="selectedCameraBox">
            <div>
              <template v-for="(item, index) in viewSelectedCameraList">
                <div class="over-flow selected-item" v-if="item.checked" :key="index">
                  <span class="fl">{{ item.deviceName }}</span>
                  <Icon
                    v-if="showCheckbox"
                    type="ios-close"
                    class="protruding close fr"
                    @click="unSelect(item, index)"
                  />
                </div>
              </template>
            </div>
          </div>
        </Col>
      </Row>
      <template #footer>
        <Button type="primary" @click="query" class="plr-30">确 定</Button>
        <Button type="default" @click="cancel" class="plr-30">取 消</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.camera-module {
  @{_deep}.ivu-icon-ios-search {
    color: var(--color-primary);
  }
  @{_deep} .ivu-modal-body {
    padding: 0;
  }
  @{_deep} .ivu-modal-header {
    padding-bottom: 36px;
  }
  .camera-content {
    height: 700px;
    .tree {
      padding-top: 10px;
      padding-right: 20px;
      padding-left: 16px;
    }
    .camera-list {
      border-left: 1px solid var(--border-modal-footer);
      border-right: 1px solid var(--border-modal-footer);
      .select-top {
        padding: 0 15px;
        line-height: 75px;
        border-bottom: 1px solid var(--border-modal-footer);
      }
    }
  }
  .select-content {
    padding: 10px;
    overflow-y: auto;
    height: calc(100% - 51px);
    .camera-item {
      width: 48%;
    }
  }

  .selected-camera {
    .selected-title {
      padding: 0 15px;
      line-height: 75px;
      border-bottom: 1px solid var(--border-modal-footer);
    }
    .selected-list {
      padding: 5px 0;
      overflow-y: auto;
      .close {
        font-size: 20px;
        cursor: pointer;
      }
      .selected-item {
        padding: 5px 10px;
        // color: #5A6775;
        color: var(--color-content);
        &:hover {
          background-color: var(--color-primary);
          color: #ffffff;
        }
      }
    }
  }
}
.red-color {
  color: var(--color-failed);
}
.empty {
  color: var(--color-btn-text);
}
</style>
<script>
import { mapGetters } from 'vuex';
import common from '@/config/api/common';

export default {
  data() {
    return {
      visible: false,
      loading: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      nodeKey: 'orgCode',
      cameraList: [],
      selectedCameraList: [], //右边选中摄像头列表
      viewSelectedCameraList: [], //当摄像头过多的时候不要显示所有的选中的摄像头
      viewCameraList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initCameraList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
      // areaList: [],       // 一维的 arealist
      style: {
        width: '6.25rem',
      },
      mainChecked: false,
      searchText: '',
      cameraSliceNum: 200,
      selectedCameraSliceNum: 200,
    };
  },
  created() {},
  async mounted() {
    this.listenScroll();
  },
  methods: {
    muchCamera() {
      //这里处理如果摄像机列表过多，则显示前200个，否则dom过多显示会卡死
      this.cameraSliceNum = 200;
      this.selectedCameraSliceNum = 200;
      this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
      this.viewSelectedCameraList = this.selectedCameraList.slice(0, this.selectedCameraSliceNum);
    },
    //监听滑动距离加载隐藏的摄像机列表
    listenScroll() {
      let box = this.$refs.cameraBox;
      box.addEventListener(
        'scroll',
        () => {
          // console.log(box.scrollTop + box.clientHeight === box.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
          if (box.scrollTop + box.clientHeight === box.scrollHeight) {
            this.cameraSliceNum += 200;
            //到底部要做的操作
            this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
          }
        },
        false,
      );
      let selectedCameraBox = this.$refs.selectedCameraBox;
      selectedCameraBox.addEventListener(
        'scroll',
        () => {
          // console.log(selectedCameraBox.scrollTop + selectedCameraBox.clientHeight === selectedCameraBox.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
          if (selectedCameraBox.scrollTop + selectedCameraBox.clientHeight === selectedCameraBox.scrollHeight) {
            this.selectedCameraSliceNum += 200;
            //到底部要做的操作
            this.viewSelectedCameraList = this.selectedCameraList.slice(0, this.selectedCameraSliceNum);
          }
        },
        false,
      );
    },
    //------------------全选所有摄像机函数---------------------------------
    chooseChecked(val) {
      this.cameraList.forEach((row) => {
        row.checked = val;
      });
      this.selectedCameraList = this.$util.common.deepCopy(this.cameraList);
      this.selectedCameraSliceNum = 200;
      this.viewSelectedCameraList = this.selectedCameraList.slice(0, this.selectedCameraSliceNum);
    },
    //-----------------根据名字检索摄像机函数---------------------------
    searchCamaraAdd() {
      let searchArr = [];
      if (this.searchText !== '') {
        for (let i = 0; i < this.initCameraList.length; i++) {
          let str = this.initCameraList[i].deviceName || '';
          if (str.indexOf(this.searchText) !== -1) {
            searchArr.push(this.initCameraList[i]);
          }
        }
        this.cameraList = searchArr;
      } else {
        this.cameraList = this.initCameraList;
      }
      this.muchCamera();
    },
    selectTree() {},
    async getDevice(orgCodes) {
      if (!orgCodes || orgCodes.length === 0) {
        this.allCameraList = [];
        return;
      }
      try {
        this.loading = true;
        let res = await this.$http.post(common.getDeviceList, {
          orgCodes: orgCodes,
          sbgnlxs: this.cameraType,
        });
        this.allCameraList = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async check(checkedKeys) {
      this.cameraList = [];
      let tempObj = {}; //一个临时对象
      // 将一个数组转换为一个对象减少for循环
      checkedKeys.forEach((row) => {
        tempObj[row] = row;
      });
      await this.getDevice(checkedKeys);
      //从所有的摄像机列表中和选中的树结构的区域编码做对比，如果是选中的区域编码则加入中间的摄像机列表
      this.allCameraList.forEach((row) => {
        if (tempObj[row.orgCode]) {
          this.cameraList.push(row);
        }
      });
      //提取一个对象减少for循环
      let tempObj2 = {};
      this.selectedCameraList.forEach((row) => {
        tempObj2[row.deviceId] = row.deviceId;
      });
      //初始化中间摄像机列表时，要跟右边已选中摄像机列表做对比，选中摄像机
      this.cameraList = this.cameraList.map((row) => {
        if (tempObj2[row.deviceId]) {
          this.$set(row, 'checked', true);
        } else {
          this.$set(row, 'checked', false);
        }
        return row;
      });
      //当摄像头过多时进行处理不显示所有摄像头只先显示200个
      this.muchCamera();
      this.initCameraList = this.cameraList;
      this.isCheckAll();
    },
    //中间摄像头列表选中
    selectCamera(item) {
      //这里因为不想要中间的摄像头列表影响到右边选中的摄像头列表所以需要深度克隆
      let temp = this.$util.common.deepCopy(item);
      let index = this.selectedCameraList.findIndex((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (temp.checked) {
        if (index === -1) {
          this.selectedCameraList.push(temp);
        }
      } else {
        this.selectedCameraList.splice(index, 1);
      }
      this.viewSelectedCameraList = this.selectedCameraList.slice(0, this.selectedCameraSliceNum);
      this.isCheckAll();
    },
    // 检查是否全选所有中间摄像机列表
    isCheckAll() {
      if (this.cameraList.length === 0) {
        this.mainChecked = false;
        return false;
      }
      this.mainChecked = true;
      this.cameraList.forEach((row) => {
        if (!row.checked) this.mainChecked = false;
      });
    },
    unSelect(item, index) {
      item.checked = false;
      let temp = this.$util.common.deepCopy(item);
      let obj = this.cameraList.find((row) => {
        return temp.deviceId === row.deviceId;
      });
      if (obj) {
        obj.checked = item.checked;
      }
      this.selectedCameraList.splice(index, 1);
      this.isCheckAll();
    },
    unSelectAll() {
      this.cameraList.forEach((row) => {
        row.checked = false;
      });
      this.initCameraList.forEach((row) => {
        row.checked = false;
      });
      this.selectedCameraList = [];
      this.viewSelectedCameraList = [];
      this.mainChecked = false;
    },
    query() {
      this.visible = false;
      this.$emit('pushCamera', this.selectedCameraList);
    },
    cancel() {
      this.visible = false;
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    defaultAreaList(val) {
      this.selectedCameraList = [];
      this.$refs.uiSearchTree.setCheckedKeys(val);
      this.check(val);
      this.query();
    },
    //初始化选中的摄像机
    defaultCameraList(val) {
      this.selectedCameraList = [];
      let checkTreeKey = [];
      this.cameraList = val.map((row) => {
        checkTreeKey.push(row.orgCode);
        this.$set(row, 'checked', true);
        this.selectCamera(row);
        return row;
      });
      checkTreeKey = Array.from(new Set(checkTreeKey));
      this.$refs.uiSearchTree.setCheckedKeys(checkTreeKey);
      this.check(checkTreeKey);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultExpandedKeys',
    }),
  },
  props: {
    value: {},
    defaultCameraList: {
      default() {
        return [];
      },
    },
    defaultAreaList: {},
    showCheckbox: {
      default() {
        return true;
      },
    },
    // 摄像机功能类型 1:车辆卡口,2:人员卡口,3:微卡口,4:特征摄像机,5:普通监控,6:高空瞭望摄像机,99:其他
    cameraType: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  beforeDestroy() {},
};
</script>
