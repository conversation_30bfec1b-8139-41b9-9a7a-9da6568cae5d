import request from "@/libs/request";
import { holographicArchives, manager, cloudSearch } from "./Microservice";
// 车辆档案
// 卡片查询列表
export function getVehicleList(data) {
  return request({
    url: holographicArchives + "/vehicle/vehicleList",
    method: "POST",
    data: data,
  });
}
// 车辆品牌
export function getVehicleBrandList(letter) {
  return request({
    url: holographicArchives + "/vehicle/selectVehicleBrand?first=" + letter,
    method: "GET",
  });
}
// 车辆抓拍
export function getVehicleCapture(data) {
  return request({
    url: holographicArchives + "/vehicle/getVehicleCapture",
    method: "POST",
    data: data,
  });
}
// 人车同拍
export function getPeopleCarCaptureList(data) {
  return request({
    url: holographicArchives + "/carArchives/PCSnap",
    method: "POST",
    data: data,
  });
}
// 最新位置
export function getLatestLocationList(data) {
  return request({
    url: holographicArchives + "/carArchives/latestLocation",
    method: "POST",
    data: data,
  });
}
// 常去地
export function getFrequentedList(data) {
  return request({
    url: holographicArchives + "/carArchives/frequented",
    method: "POST",
    data: data,
  });
}
// 行为规律-活动时间段
export function getBehavioralRulesStatics(data) {
  return request({
    url: holographicArchives + "/vehicle/behavioralRulesStatics",
    method: "POST",
    data: data,
  });
}
// 行为规律-活动次数
export function getActivitiesNumStatics(data) {
  return request({
    url: holographicArchives + "/carArchives/activitiesNumStatics",
    method: "POST",
    data: data,
  });
}
// 涉案信息
export function getVehicleCasePageList(data) {
  return request({
    url: holographicArchives + "/carArchives/casePageList",
    method: "POST",
    data: data,
  });
}
// 违法违章
export function getVehicleIllegalPageList(data) {
  return request({
    url: holographicArchives + "/vehicle/illegalPageList",
    method: "POST",
    data: data,
  });
}
// 车辆跟随
export function getFollowTheVehicleList(data) {
  return request({
    url: holographicArchives + "/vehicle/followTheVehicle",
    method: "POST",
    data: data,
  });
}
// 驾乘人员
export function getDrivingList(data) {
  return request({
    url: holographicArchives + "/carArchives/drivingPersonPageList",
    method: "POST",
    data: data,
  });
}

// 获取已添加管理资源 - 实名档案 - 背景信息 tab
export function getManageResourceList(data) {
  return request({
    url: manager + "/resource/getManageResourceList",
    method: "POST",
    data: data,
  });
}

// 获取已添加管理资源 - 实名档案 - 背景信息 table表头
export function getResourceConfigur(data) {
  return request({
    url: manager + "/resource/getResourceConfigur",
    method: "POST",
    data: data,
  });
}
// 获取已添加管理资源 - 实名档案 - 背景信息 tableDate
export function policeDataPageList(data) {
  return request({
    url: cloudSearch + "/policeDataPageList",
    method: "POST",
    data: data,
  });
}

// 一车一档 列表
export function queryVehicleList(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/queryVehicleList",
    method: "POST",
    data: data,
  });
}

// 车辆档案-车辆概览-抓拍记录
export function queryVehicleCaptureList(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryVehicleCaptureList?plateNo=" +
      plateNo,
    method: "GET",
  });
}

// 车辆档案 基本信息
export function getVehicleBaseInfo(archiveNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/getVehicleBaseInfo?archiveNo=" +
      archiveNo,
    method: "GET",
  });
}
/**
 * 车辆档案 基本信息 黔西南版本
 * @param params
 * @returns {AxiosPromise}
 */
// export function getVehicleBaseInfo (params) {
//     return request({
//       url: holographicArchives + '/vehicleArchives/getVehicleBaseInfoV2',
//       method: 'GET',
//       params
//     })
//   }

/**
 * 车辆档案 基本信息v2
 * @param params
 * @returns {AxiosPromise}
 */
export function getVehicleBaseInfoV2(params) {
  return request({
    url: holographicArchives + "/vehicleArchives/getVehicleBaseInfoV2",
    method: "GET",
    params,
  });
}
// 车辆档案 车主信息
export function personBaseInfo(data) {
  return request({
    url: holographicArchives + "/person/personBaseInfo",
    method: "POST",
    data: data,
  });
}

// 车辆档案 抓拍轨迹
export function captureTrack(data) {
  return request({
    url: holographicArchives + "/activityTrack/captureTrack",
    method: "POST",
    data: data,
  });
}

// 车辆档案 轨迹 
export function getVehicleTrajectory (data) {
  return request({
    url: holographicArchives + '/vehicleArchives/getVehicleTrajectory',
    method: 'POST',
    data: data
  })
}

// 车辆资料-行为规律-活动次数
export function activitiesNumStatics(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/activitiesNumStatics",
    method: "POST",
    data: data,
  });
}

// 车辆资料（车辆概览）-行为规律-时间段
export function behavioralRulesStaticsVehicle(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/behavioralRulesStatics",
    method: "POST",
    data: data,
  });
}

// 车辆资料-车辆抓拍(根据抓拍时间倒序)
export function getVehicleCaptureList(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/getVehicleCaptureList",
    method: "POST",
    data: data,
  });
}

// 车辆资料-位置信息-最新位置信息
export function latestLocation(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/latestLocation",
    method: "POST",
    data: data,
  });
}

// 车辆资料-位置信息-常去地
export function frequented(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/frequented",
    method: "POST",
    data: data,
  });
}

// 车辆概览-驾乘人员
export function queryDrivingPerson(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryDrivingPerson?plateNo=" +
      plateNo,
    method: "GET",
  });
}

// 车辆概览-同车主（中间6个）
export function querySameOwner(idcardNo, plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/querySameOwner?idcardNo=" +
      idcardNo +
      "&plateNo=" +
      plateNo,
    method: "GET",
  });
}
// 车辆概览-抓拍记录（中间6个）
export function queryVehicleCapture(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryVehicleCapture?plateNo=" +
      plateNo,
    method: "GET",
  });
}
// 车辆概览-经常途径（中间6个）
export function queryRegularApproach(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryRegularApproach?plateNo=" +
      plateNo,
    method: "GET",
  });
}
// 车辆概览-关联案件（中间6个）
export function queryVehicleRelatedCases(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryVehicleRelatedCases?plateNo=" +
      plateNo,
    method: "GET",
  });
}
// 车辆概览-违法违章
export function queryVehiclellegalList(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryVehiclellegalList?plateNo=" +
      plateNo,
    method: "GET",
  });
}

// 车辆资料-人车同拍
export function queryPeopleAndVehicleSnapList(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/queryPeopleAndVehicleSnapList?plateNo=" +
      plateNo,
    method: "GET",
  });
}

// 编辑车辆标签
export function motifyVehicleLableIds(data) {
  return request({
    url: holographicArchives + "/vehicleArchives/motifyVehicleLableIds",
    method: "POST",
    data,
  });
}

// 根据车牌号码查询车辆信息
export function getVehicleBaseInfoByplateNo(plateNo) {
  return request({
    url:
      holographicArchives +
      "/vehicleArchives/getVehicleBaseInfoByplateNo?plateNo=" +
      plateNo,
    method: "get",
  });
}

// 一车一档 非机动车档案列表
export function queryNonMotorVehicleList(data) {
  return request({
    url: holographicArchives + "/nonMotor/archivePageList",
    method: "POST",
    data: data,
  });
}

// 非机动车车辆档案 抓拍轨迹
export function nonMotorCaptureTrack(data) {
  return request({
    url: holographicArchives + "/nonMotor/captureTrack",
    method: "POST",
    data: data,
  });
}

// 车辆资料（车辆概览）-行为规律-时间段 非机动车
export function behavioralRulesStaticsNonMotor(data) {
  return request({
    url: holographicArchives + "/nonMotor/behavioralRulesStatics",
    method: "POST",
    data: data,
  });
}

// 车辆资料-行为规律-活动次数 非机动车
export function activitiesNumStaticsNonMotor(data) {
  return request({
    url: holographicArchives + "/nonMotor/activitiesNumStatics",
    method: "POST",
    data: data,
  });
}

// 车辆资料-基础信息 非机动车
export function archiveInfo(data) {
  return request({
    url: holographicArchives + "/nonMotor/archiveInfo",
    method: "POST",
    data: data,
  });
}

// 车辆资料-位置信息-常去地  非机动车
export function nonMotorFrequented(data) {
  return request({
    url: holographicArchives + "/nonMotor/frequented",
    method: "POST",
    data: data,
  });
}

// 车辆资料-位置信息-最新位置信息  非机动车
export function nonMotorLatestLocation(data) {
  return request({
    url: holographicArchives + "/nonMotor/latestLocation",
    method: "POST",
    data: data,
  });
}

// 车辆资料-车辆抓拍(根据抓拍时间倒序)  非机动车
export function getNonMotorVehicleCaptureList(data) {
  return request({
    url: holographicArchives + "/nonMotor/getVehicleCaptureList",
    method: "POST",
    data: data,
  });
}

/**
 * 查询车辆违章信息
 * @param idCardNo  522323198608300011
 * @param pageSize 10
 * @param pageNumber 20
 * @returns @returns {AxiosPromise}
 */
export function getVehicleArchivesViolation(params) {
  return request({
    url: holographicArchives + "/vehicleArchives/getVehicleViolation",
    method: "GET",
    params,
  });
}
