<template>
  <div class="map-wrap">
    <mapTrack
      ref="mapTrack"
      :idlerWheel="true"
      cutIcon="track"
      :trackPoints="layerManageMap[sectionName]"
      :sectionName="sectionName"
      :disableScroll="false"
      :currentClickIndex.sync="currentClickIndex"
      :trackConnected="trackConnected"
      @chooseMapItem="chooseMapItem($event, true)"
      :mapLayerConfig="{ ...mapLayerConfig }"
      @loaded="mapLoaded"
    />
    <!-- 作战记录 -->
    <operationalRecords
      ref="operationalRecords"
      @openLayerManager="openLayerManager"
      @closeLayerManager="closeLayerManager"
    ></operationalRecords>
    <!-- 高级搜索结果 -->
    <!-- 高级搜索展示的页面位置 -->
    <keep-alive :include="aliveComponents">
      <advancedSearch
        ref="advancedSearch"
        v-if="searchType == 'advanced'"
        @updateLayerManager="updateLayerManager"
      />
    </keep-alive>
    <template v-if="canWrite">
      <div
        class="advanced-search-back"
        v-if="searchType == 'advanced'"
        :class="searchType == 'advanced' ? 'advanced-search-active' : ''"
        @click="advancedSearchButton"
      >
        <span>轨迹搜索</span>
        <ui-icon type="return" :size="20" color="#2C86F8"></ui-icon>
      </div>
      <div class="advanced-search" @click="advancedSearchButton" v-else>
        <ui-icon type="gaojisousuo" :size="16"></ui-icon>
        <span>轨迹搜索</span>
      </div>
    </template>
    <!-- 轨迹上图结果 -->
    <div class="search-result" v-if="isShowTrack">
      <div class="search-result-header" :class="{ 'expand-header': !expand }">
        <div>
          <span v-if="expand">{{ recordName }}</span>
          <ui-icon
            v-if="expand && canWrite"
            class="ml-10"
            type="baocun"
            color="#2C86F8"
            :size="16"
            @click.native="addOrEditCombatRecord"
          ></ui-icon>
        </div>
        <div>
          <Checkbox
            v-if="expand"
            :value="trackConnected"
            @click.prevent.native="handleTrackConnect"
            >轨迹连线</Checkbox
          >
          <ui-icon
            type="close"
            :size="16"
            @click.native.stop="closeLayerManager(false)"
          ></ui-icon>
        </div>
      </div>
      <!-- 50 / 192 + 'rem' -->
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in menuList"
          :key="index"
          :name="item.name"
          v-permission="item.permission"
          :class="!!item.num ? 'active-search' : ''"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
        </MenuItem>
      </Menu>
      <!-- 普通搜索展示的页面位置 -->
      <div :class="{ 'expand-content': !expand }">
        <component
          class="search-result-content"
          :ref="sectionName"
          :is="sectionName"
          :updateLayerId="updateLayerId"
          :dataList="layerManageMap[sectionName]"
          :currentClickIndex="currentClickIndex"
          :orderType="menuItem.order"
          :canWrite="canWrite"
          @chooseMapItem="chooseMapItem"
          @changeOrder="changeOrder"
          @deleteItem="deleteItem"
        ></component>
      </div>
      <div class="switch" @click="switchHandle">
        <img v-if="!expand" src="@/assets/img/expand.png" alt="" />
        <img v-else src="@/assets/img/stow.png" alt="" />
      </div>
    </div>

    <ui-modal
      ref="modal"
      v-model="modalVisible"
      :r-width="450"
      title="新增"
      class="modal"
      @onCancel="onCancel"
      @onOk="onOK"
    >
      <div>
        <span>名称:</span>
        <Input
          placeholder="请输入名称"
          v-model="addLayerName"
          :maxlength="20"
        />
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { mapActions, mapMutations, mapGetters } from "vuex";
import { addCombatRecord, updateCombat } from "@/api/operationsOnTheMap";
import mapTrack from "@/components/map/map-track.vue";
import operationalRecords from "./components/operational-records.vue";
import advancedSearch from "@/views/operations-on-the-map/map-default-page/components/advanced-search";
import face from "./components/track-result/face-content.vue";
import wifi from "./components/track-result/wifi-content.vue";
import vehicle from "./components/track-result/vehicle-content.vue";
import humanbody from "./components/track-result/humanBody-content.vue";
import nonmotorVehicle from "./components/track-result/nonmotorVehicle-content.vue";
import electric from "./components/track-result/electric-content.vue";
import rfid from "./components/track-result/rfid-content.vue";
import gps from "./components/track-result/gps-content.vue";
import etc from "./components/track-result/etc-content.vue";
import all from "./components/track-result/all-content.vue";

export default {
  name: "map-track",
  components: {
    mapTrack,
    operationalRecords,
    advancedSearch,
    face, //人脸模块
    vehicle, //车辆模块
    wifi, //wifi模块
    rfid, //rfid模块
    electric, //电围模块
    gps, //电围模块
    humanbody,
    nonmotorVehicle,
    etc,
    all,
  },
  data() {
    return {
      positionPoints: [],
      searchType: "",
      layerManageMap: {
        face: [],
        vehicle: [],
        humanbody: [],
        nonmotorVehicle: [],
        wifi: [],
        rfid: [],
        electric: [],
        gps: [],
        etc: [],
        all: [],
      },
      isShowTrack: false,
      hasResult: false,
      menuList: [
        {
          label: "全部",
          value: 0,
          iconName: "icon-sifenping",
          name: "all",
          numName: "all",
          order: "desc",
          permission: "yes",
        },
        {
          label: "人像",
          value: 2,
          iconName: "icon-yonghu",
          name: "face",
          numName: "face",
          order: "desc",
          permission: ["track-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicle",
          numName: "vehicleRecord",
          order: "desc",
          permission: ["track-vehicle"],
        },
        {
          label: "人体",
          value: 9,
          iconName: "icon-renti",
          name: "humanbody",
          numName: "humanRecord",
          order: "desc",
          permission: ["track-humanBody"],
        },
        {
          label: "非机动车",
          value: 10,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicle",
          numName: "nonmotorRecord",
          order: "desc",
          permission: ["track-nonmotorVehicle"],
        },
        {
          label: "Wi-Fi",
          value: 4,
          iconName: "icon-wifi",
          name: "wifi",
          numName: "wifiRecord",
          order: "desc",
          permission: ["track-wifi"],
        },
        {
          label: "RFID",
          value: 5,
          iconName: "icon-RFID",
          name: "rfid",
          numName: "rfidRecord",
          order: "desc",
          permission: ["track-rfid"],
        },
        {
          label: "电围",
          value: 6,
          iconName: "icon-ZM-dianwei",
          name: "electric",
          numName: "electricCircumferenceRecord",
          order: "desc",
          permission: ["track-electric"],
        },
        {
          label: "GPS",
          value: 7,
          iconName: "icon-gps",
          name: "gps",
          numName: "gpsRecord",
          order: "desc",
          permission: ["track-gps"],
        },
        {
          label: "ETC",
          value: 8,
          iconName: "icon-a-ETC1x",
          name: "etc",
          numName: "etcRecord",
          order: "desc",
          permission: ["track-etc"],
        },
      ],
      sectionName: "",
      currentClickIndex: -1, // 当前点击的轨迹节点
      recordName: "",
      // 地图配置信息
      mapLayerConfig: {
        mapToolVisible: false, // 底部框选操作栏
        trackResult: true, //搜索结果撒点判断
      },
      isEditLayer: false, // 编辑图层
      updateLayerId: null,
      modalVisible: false,
      addLayerName: "",
      trackConnected: false,
      expand: true,
      backType: "", // 数据上图返回的菜单
      aliveComponents: [], // 要缓存的组件，当进行估计搜索时，需要将其缓存
      source: "",
    };
  },
  computed: {
    ...mapGetters({
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
    }),
    menuItem() {
      let menuItem = this.menuList.find((v) => v.name == this.sectionName);
      return menuItem || {};
    },
    canWrite() {
      return this.source !== "share";
    },
  },
  created() {
    this.setSum();
  },
  activated() {
    this.$store.commit("common/setPageType", 1);
    this.setLayoutNoPadding(true);
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
    this.setUpImageData();
    this.aliveComponents = [];
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    ...mapMutations({ setUpImageData: "map/setUpImageData" }),
    ...mapActions({
      setSum: "countCoverage/setSum",
    }),
    mapLoaded() {
      if (this.$route.query.mapData) {
        let item = this.$route.query.mapData;
        let { type, list, deleIdent } = JSON.parse(item);
        this.updateLayerManager({ type, list, deleIdent });
      }
      if (this.$route.query.content) {
        let item = this.$route.query.content;
        let params = {
          name: item.name,
          layerMap: JSON.parse(item.dataText),
          id: item.id,
        };
        this.openLayerManager(params);
      }
    },
    // 打开作战记录图层管理
    openLayerManager({ name, layerMap, id, source }) {
      if (this.$refs["operationalRecords"]) {
        this.$refs["operationalRecords"].operationalRecords = false;
      }
      if (!layerMap || !id) {
        this.recordName ? (this.recordName = name) : "";
      } else {
        this.clearLayerData();
        this.isEditLayer = true;
        this.recordName = name;
        this.updateLayerId = id;
        this.source = source;
        if (layerMap) {
          setTimeout(() => {
            this.layerManageMap = {
              ...layerMap,
            };
            // 将作战中的已上图数据保存起来，避免在此基础上，继续添加，导致重复
            this.setUpImageData(layerMap["all"]);
            this.sectionName = "all";
            this.expand = true;
            this.trackConnected = false;
            this.isShowTrack = true;
            this.hasResult = true;
          }, 0);
        }
      }
    },
    // 编辑或新增作战记录
    addOrEditCombatRecord() {
      if (this.isEditLayer) {
        const params = {
          name: this.recordName,
          dataText: JSON.stringify(this.layerManageMap),
          id: this.updateLayerId,
        };
        updateCombat(params)
          .then((res) => {
            if (res.code == 200) {
              this.$message.success({
                message: "保存成功",
                duration: 1500,
                customClass: "message-success",
                offset: 25,
              });
            }
          })
          .catch((e) => {
            console.log(e);
          });
      } else {
        this.modalVisible = true;
      }
    },
    // 确认新增作战记录
    onOK() {
      if (!this.addLayerName) {
        return this.$Message.warning("请输入名称");
      }
      let params = {
        dataText: JSON.stringify(this.layerManageMap),
        name: this.addLayerName,
      };
      addCombatRecord(params).then((res) => {
        if (res.code === 200) {
          // 这里使用view-design的$Message弹框，在车辆保存作战记录时弹框异常关闭，找不到原因，用element-ui的$message弹框
          this.$message.success({
            message: "保存成功",
            duration: 1500,
            customClass: "message-success",
            offset: 25,
          });
          this.onCancel();
          this.clearLayerData();
        }
      });
    },
    onCancel() {
      this.modalVisible = false;
      this.addLayerName = "";
    },

    /**
     * @description: 关闭图层
     * @param {boolean} val true: 删除正在打开的作战记录, false: 普通关闭，手动触发
     */
    closeLayerManager(val) {
      if (val) {
        return this.clearLayerData();
      }
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定关闭吗？`,
        onOk: () => {
          this.$refs["operationalRecords"].openLayerIndex = -1;
          this.isEditLayer = false;
          this.updateLayerId = null;
          this.recordName = "";
          this.clearLayerData();
        },
      });
    },
    advancedSearchButton() {
      this.aliveComponents = ["OperationOnMapSearch"];
      const { searchType } = this;
      this.$refs.mapTrack.closeDeviceModal();
      this.searchType =
        searchType === "advanced"
          ? ((this.isShowTrack = this.hasResult), (this.searchType = ""))
          : ((this.isShowTrack = false), (this.searchType = "advanced"));
      // 数据上图页面返回时处理定位到返回的菜单
      this.$nextTick(() => {
        if (this.$refs.advancedSearch) {
          if (this.backType === "humanbody") this.backType = "humanBodyContent";
          if (this.backType === "nonmotorVehicle")
            this.backType = "nonmotorVehicleContent";
          if (this.backType !== "")
            this.$refs.advancedSearch.selectItemHandle(this.backType);
        }
      });
    },
    clearLayerData() {
      // 清除感知数据的缓存状态
      this.aliveComponents = [];
      // 重置来源
      this.source = "";
      // 需要清除掉已经上图的数据
      Object.keys(this.layerManageMap).forEach((key) => {
        this.layerManageMap[key] = [];
      });
      this.setUpImageData();
      this.currentClickIndex = -1;
      this.$refs["mapTrack"].resetMarker();
      this.hasResult = false;
      this.trackConnected = false;
      this.isShowTrack = false;
    },
    // 上图数据更新
    updateLayerManager({ type, list, deleIdent }) {
      this.recordName = this.recordName || "保存记录";
      this.selectItemHandle(type);
      this.expand = true;
      this.trackConnected = false;
      this.hasResult = true;
      this.isShowTrack = true;
      this.searchType = "";
      let position = list.map((e) => {
        return {
          ...e,
          type: type,
          lat: e.geoPoint && e.geoPoint.lat,
          lon: e.geoPoint && e.geoPoint.lon,
        };
      });
      // 删除取消上图的内容
      let deleteList = position.filter((e) => !e.isChecked);
      deleteList.forEach((e) => {
        this.deleteItem(e, -1);
      });
      position = position.filter((e) => e.lat && e.lon && e.isChecked);
      if (!this.layerManageMap[type]) {
        this.layerManageMap[type] = [];
      }
      let arr = [...this.layerManageMap[type], ...position];
      arr.sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return this.menuItem.order == "desc" ? timeB - timeA : timeA - timeB;
      });
      this.layerManageMap[type] = arr;
      // 全部
      let allArr = [...this.layerManageMap["all"], ...position];
      let allItem = this.menuList.find((v) => v.name == "all");
      allArr.sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return allItem.order == "desc" ? timeB - timeA : timeA - timeB;
      });
      this.layerManageMap["all"] = allArr;
      // 已上的数据存store
      this.setUpImageData(allArr);
      this.backType = type;
    },
    selectItemHandle(sectionName) {
      if (this.sectionName != sectionName) {
        this.trackConnected = false;
        this.$refs["mapTrack"].resetMarker();
      }
      this.sectionName = sectionName;
      this.currentClickIndex = -1;
    },
    // 左侧选中项 点击普通搜索列表
    chooseMapItem(index, noZoom) {
      this.currentClickIndex = index;
      if (!noZoom) this.$refs.mapTrack.zoomInto();
    },
    changeOrder(order) {
      this.trackConnected = false;
      let menuItem = this.menuList.find((v) => v.name == this.sectionName);
      menuItem.order = order;
      this.layerManageMap[this.sectionName].sort((a, b) => {
        const timeA = new Date(a.absTime);
        const timeB = new Date(b.absTime);
        return order == "desc" ? timeB - timeA : timeA - timeB;
      });
    },
    handleTrackConnect() {
      this.trackConnected = !this.trackConnected;
    },
    deleteItem(item, index) {
      let typeMap = {
        face: { idKey: "id" },
        vehicle: { idKey: "id" },
        humanbody: { idKey: "recordId" },
        nonmotorVehicle: { idKey: "recordId" },
        wifi: { idKey: "id" },
        rfid: { idKey: "id" },
        electric: { idKey: "id" },
        gps: { idKey: "recordId" },
        etc: { idKey: "recordId" },
      };
      let idKey = typeMap[item.type].idKey;
      if (index === -1) {
        this.layerManageMap[item.type].forEach((e, ind) => {
          if (e[idKey] === item[idKey]) {
            index = ind;
          }
        });
        if (index === -1) {
          return;
        }
      }
      if (this.sectionName == "all") {
        this.layerManageMap["all"].splice(index, 1);
        this.layerManageMap[item.type] = this.layerManageMap[item.type].filter(
          (v) => v[idKey] != item[idKey]
        );
      } else {
        this.layerManageMap[this.sectionName].splice(index, 1);
        this.layerManageMap["all"] = this.layerManageMap["all"].filter(
          (v) => v[idKey] != item[idKey]
        );
      }
      this.setUpImageData(this.layerManageMap["all"]);
    },
    // 收起-展开
    switchHandle() {
      this.expand = !this.expand;
    },
  },
};
</script>
<style lang="less" scoped>
.map-wrap {
  width: 100%;
  position: relative;
  .advanced-search {
    position: absolute;
    left: 160px;
    top: 20px;
    width: 120px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
    .iconfont {
      margin-right: 5px;
    }
  }

  .advanced-search:hover {
    color: #2c86f8;
    .iconfont {
      color: #2c86f8 !important;
    }
  }

  .advanced-search-back {
    position: absolute;
    left: 20px;
    top: 20px;
    width: 160px;
    height: 40px;
    line-height: 40px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 12px;

    > span {
      font-size: 16px;
      font-weight: bold;
      margin-left: 24px;
      color: rgba(0, 0, 0, 0.9);
      position: relative;
    }

    > span:before {
      content: "";
      width: 4px;
      height: 20px;
      background: #2c86f8;
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  /deep/.search-menu {
    height: 100%;
    z-index: 10;

    .active-search {
      position: relative;

      &::before {
        position: absolute;
        right: 3px;
        top: 5px;
        content: "";
        width: 10px;
        height: 10px;
        border-radius: 5px;
        background: #f8775c;
      }
    }
  }

  .search-result {
    position: absolute;
    left: 20px;
    top: 66px;
    height: calc(~"100% - 120px");

    &-header {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      width: 410px;
      height: 34px;
      padding: 0 10px;
      background: #ffffff;
      box-shadow: inset 0px -1px 0px 0px #d3d7de;
      border-radius: 4px 4px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .total {
        color: #f29f4c;
        font-weight: bold;
      }

      .search-tool {
        cursor: pointer;
      }

      .iconfont {
        margin-right: 5px;
      }

      /deep/.ivu-dropdown {
        .ivu-select-dropdown {
          padding: 0;
          width: 410px;
          left: 0 !important;
          top: -4px !important;

          .ivu-dropdown-menu {
            .arrow-up {
              height: 36px;
              display: flex;
              align-items: center;
              justify-content: end;
              border-bottom: 1px solid #d3d7de;
              padding-left: 20px;
              padding-right: 10px;
            }

            .ivu-form {
              padding: 15px 20px 0px;

              &-item {
                margin-bottom: 15px;

                .frame-selection {
                  width: 34px;
                  height: 34px;
                  border-radius: 4px;
                  border: 1px solid #d3d7de;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  margin-left: 10px;
                  cursor: pointer;

                  .iconfont {
                    margin-right: 0;
                  }
                }

                .frame-selection:hover {
                  border: 1px solid #2c86f8;

                  .iconfont {
                    color: #2c86f8 !important;
                  }
                }

                &-content {
                  display: flex;

                  .ivu-date-picker {
                    width: 243px !important;
                  }

                  .ivu-select-dropdown {
                    padding: 5px 0px;
                    width: 432px;
                    left: 0px !important;
                    top: 32px !important;
                  }
                }
              }

              .btn-group {
                float: right;
              }
            }
          }
        }
      }
      &.expand-header {
        width: 50px;
      }
    }

    &-content {
      position: absolute;
      left: 50px;
      top: 34px;
      height: 100%;
      background: rgba(255, 255, 255, 0.9);
      box-shadow: 0px 3px 5px 0px rgba(0, 21, 41, 0.12);
      border-radius: 0px 0px 4px 4px;
    }
    .switch {
      width: 18px;
      height: 90px;
      position: absolute;
      bottom: 10px;
      top: 50%;
      right: -18px;
      transform: translateY(-50%);
      cursor: pointer;
      > img {
        width: 100%;
      }
    }
    .expand-content {
      transform: translateX(-100%);
    }
  }

  .modal {
    /deep/.ivu-modal-body {
      display: flex;
      align-items: center;
      // height: 120px;
      justify-content: center;

      > div {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.45);

        .ivu-input-wrapper {
          width: 250px;
          margin-left: 5px;
        }
      }
    }
  }
}
</style>

<style lang="less">
.message-success {
  background-color: #fff;
  border-color: #fff;
  padding: 8px 16px;
  min-width: unset;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  .el-message__icon {
    font-size: 16px;
  }
  .el-icon-success {
    color: #19be6b;
  }
  .el-message__content {
    color: #515a6e;
    line-height: 22px;
  }
}
</style>
