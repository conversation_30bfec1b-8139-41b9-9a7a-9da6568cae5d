<!--
    * @FileDescription: 昼伏夜出
    * @Date: 2023/12/14
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-25 14:21:27
-->
<template>
  <div class="day-night container">
    <ui-loading v-if="loading" />
    <!-- 地图 -->
    <mapCustom ref="mapBase" mapType="dayNight" />
    <!-- 左面信息展示框 -->
    <div class="leftBox">
      <div class="search-box">
        <div class="title">
          <p>昼伏夜出</p>
        </div>
        <!-- 搜索框 -->
        <search-form
          v-if="showQueryCondition"
          @search="handleSearch"
        ></search-form>
        <!-- 搜索结果1 -->
        <div
          class="left-search-content"
          v-if="!showQueryCondition && showResult1"
        >
          <result-list
            :resultList="resultList"
            pageName="vehicle-vespertine"
            @goback="goback"
            :pageParams="pageParams"
            @change-page="changePage"
            @change-Item="changeItem"
          ></result-list>
        </div>
        <!-- 搜索结果2 -->
        <div
          class="left-search-content"
          v-if="!showQueryCondition && showResult2"
        >
          <result-list2
            :currentItem="currentItem"
            :params="params"
            @goback="goback"
            @gotoDetail="gotoDetail"
          ></result-list2>
        </div>
      </div>
    </div>
    <!-- 进度条 -->
    <search-progress
      :progress="progress"
      :percent="percent"
      v-show="showProgress"
      :isBigProgress="true"
    ></search-progress>
    <!-- 右侧结果 -->
    <right-list-one
      :appearList="appearList"
      pageName="vehicle-vespertine"
      v-if="showResult3"
      @show-pic="showPic"
    ></right-list-one>
  </div>
</template>
<script>
import searchForm from "./components/searchForm.vue";
import mapCustom from "../components/map/index.vue";
import resultList from "@/views/model-market/components/resultList.vue";
import resultList2 from "./components/resultList2.vue";
import RightListOne from "@/views/model-market/components/RightListOne.vue";
import SearchProgress from "@/components/SearchProgress.vue";
import {
  getVehicleNocturnalListWithPage,
  getProgress,
  getNocturnalTrailList,
} from "@/api/modelMarket";
import { getUUID } from "@/util/modules/common.js";
import { mapMutations } from "vuex";
export default {
  name: "day-night",
  components: {
    mapCustom,
    searchForm,
    SearchProgress,
    resultList,
    resultList2,
    "right-list-one": RightListOne,
  },
  data() {
    return {
      params: {
        startTime: "10",
        endTime: "22",
      },
      pageParams: {
        pageNumber: 1,
        totalCount: 0,
        pageSize: 10,
      },
      currentUUID: "",
      resultList: [],
      showProgress: false,
      progress: 0,
      percent: 0,
      showQueryCondition: true,
      showResult1: false,
      showResult2: false,
      showResult3: false,
      currentInterval: null,
      currentItem: {},
      cameraList: [],
      appearList: [],
      currentObj: {},
      loading: false,
    };
  },
  created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    handleSearch(params) {
      console.log(params, "params");
      this.params = { ...params };
      this.pageParams = {
        pageNumber: 1,
        totalCount: 0,
        pageSize: 10,
      };
      this.queryList(true);
    },
    //查询昼伏夜出列表
    queryList(isSearch) {
      if (new Date(this.params.startDate) > new Date(this.params.endDate)) {
        this.$Message.info("开始时间不能大于结束时间");
        return;
      }
      this.currentUUID = getUUID();
      let param = {
        startDate: this.params.startDate,
        endDate: this.params.endDate,
        threshold: 1,
        key: "",
        startTime: this.params.startTime,
        endTime: this.params.endTime,
        pageNumber: this.pageParams.pageNumber,
        pageSize: this.pageParams.pageSize,
      };
      if (!isSearch) param.key = this.params.key;
      param.progressId = this.currentUUID;
      this.showProgress = true;
      getVehicleNocturnalListWithPage(param)
        .then((res) => {
          if (res.code == 200) {
            this.resultList = res.data.list || [];
            this.pageParams.pageNumber = res.data.pageNumber;
            this.pageParams.pageSize = res.data.pageSize;
            this.pageParams.totalCount = res.data.totalRow;
            this.showQueryCondition = false;
            this.showResult1 = true;
            this.showResult2 = false;
            this.params.key = res.data.key;
          }
        })
        .catch((error) => {})
        .finally(() => {
          this.showProgress = false;
          this.progress = 0;
          if (this.currentInterval) {
            clearInterval(this.currentInterval);
            this.currentInterval = null;
          }
        });
      if (this.showProgress) {
        this.queryProgress();
      }
    },
    //查询当前任务的进度
    queryProgress() {
      let that = this;
      clearInterval(that.currentInterval);
      that.currentInterval = null;
      that.currentInterval = setInterval(() => {
        getProgress({ progressId: that.currentUUID }).then((resp) => {
          if (resp.code == 200) {
            that.progress = resp.data;
            that.percent = (resp.data / 100) * 360;
          }
        });
      }, 200);
    },
    changePage(val) {
      this.pageParams.pageNumber = val;
      this.queryList();
    },
    changeItem(item) {
      this.currentItem = item;
      this.showResult1 = false;
      this.showResult2 = true;
    },
    goback(step) {
      if (step == 0) {
        this.showQueryCondition = true;
        this.showResult1 = false;
        this.resultList = [];
      } else if (step == 1) {
        this.showResult2 = false;
        this.showResult1 = true;
        this.showResult3 = false;
        this.appearList = [];
      }
    },
    gotoDetail({ item, type }) {
      let that = this;
      if (type == 1) {
        if (item.dayCount == 0) {
          this.$Message.warning("白天出现次数为0");
          return;
        }
      }
      var param = {
        id: this.currentItem.id,
        day: item.day,
        startTime: this.params.startTime,
        endTime: this.params.endTime,
        type: type,
      };
      this.loading = true;
      getNocturnalTrailList(param)
        .then(function (resp) {
          if (resp.code == 200) {
            that.showResult3 = true;
            let list = resp.data;
            if (list.length > 0 && that.cameraList.length > 0) {
              list.forEach((t) => {
                let camera = that.cameraList.find((ca) => {
                  return ca.id == t.deviceId;
                });
                if (camera) t.deviceName = camera.deviceName;
              });
            }
            that.appearList = list;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    showPic(item) {
      this.currentObj = item;
      if (item.longitude && item.latitude) {
        this.$refs.mapBase.clearPoint();
        this.$refs.mapBase.sprinkleDot(item);
      }
    },
  },
  beforeDestroy() {
    if (this.currentInterval) {
      clearInterval(this.currentInterval);
      this.currentInterval = null;
    }
  },
};
</script>

<style lang="less" scoped>
.day-night {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
  .leftBox {
    position: absolute;
    top: 10px;
    left: 10px;
    // 头部名称
    .title {
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      height: 40px;
      position: relative;
      line-height: 40px;
      padding-left: 20px;
      border-bottom: 1px solid #d3d7de;
      display: flex;
      justify-content: space-between;
      align-items: center;
      top: 0;
      z-index: 1;
      background: #fff;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
      &:before {
        content: "";
        position: absolute;
        width: 3px;
        height: 20px;
        top: 50%;
        transform: translateY(-50%);
        left: 10px;
        background: #2c86f8;
      }
      span {
        color: #2c86f8;
      }
      /deep/.ivu-icon-ios-close {
        font-size: 30px;
        cursor: pointer;
      }
    }
    .search-box {
      background: #fff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      width: 370px;
      filter: blur(0px);
      .left-search-content {
        height: calc(~"100vh - 170px");
      }
    }
  }
}
</style>
