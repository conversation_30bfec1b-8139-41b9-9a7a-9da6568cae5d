<template>
  <div class="influence-view auto-fill" v-ui-loading="{ loading: tabLoading, tableData: tabList }">
    <div class="tab-box" v-show="tabList.length > 0">
      <div
        v-for="(item, index) in tabList"
        :key="index"
        :class="{ 'tab-active': item.inflKey === activeTabId }"
        class="tab-item pointer"
        @click="changeTab(item)"
      >
        {{ item.inflName }}
      </div>
    </div>
    <div class="factor-box" v-show="getInfluenceList.length > 0">
      <span class="factor-title">影响因素（可能）：</span>
      <ui-select-tabs
        ref="uiSelectTabs"
        class="tabs-ui"
        :list="getInfluenceList"
        :default-select-indexs="defaultSelectIndexs"
        :multi-select="false"
        :default-props="defaultProps"
        @selectInfo="changeFactor"
      ></ui-select-tabs>
    </div>
    <div class="influence-content" v-show="getInfluenceList.length > 0">
      <div class="echarts-view">
        <!-- 平台或网络：柱状图 -->
        <BarEchart
          class="bar-echart-box"
          v-if="isPlatform"
          :active-influence-item="activeInfluenceItem"
          :active-tab-id="activeTabId"
          ref="barEchart"
        ></BarEchart>
        <template v-else>
          <div class="mr-sm">
            <!--饼状图-->
            <PieEchart
              :active-influence-item="activeInfluenceItem"
              :active-tab-id="activeTabId"
              ref="pieEchart"
            ></PieEchart>
          </div>
          <div>
            <!-- 安装位置：显示地图组件 -->
            <MapEchart
              v-if="activeInfluenceItem.key === 'address'"
              :active-influence-item="activeInfluenceItem"
              :active-tab-id="activeTabId"
              ref="mapEchart"
            ></MapEchart>
            <!-- 散点图 -->
            <ScatterEchart
              v-else
              :active-influence-item="activeInfluenceItem"
              :active-tab-id="activeTabId"
              ref="scatterEchart"
            ></ScatterEchart>
          </div>
        </template>
      </div>
      <div class="table-box">
        <div class="influence-title">
          <span class="title-rect"></span>
          <span class="ml-sm mr-sm title-span f-16">同分类设备异常率统计</span>
          <template v-if="tableResult">
            <i class="icon-font icon-jinggao mr-xs tip-color"></i>
            <div class="f-14 tip-color tip-text ellipsis">
              <Tooltip placement="bottom" :content="tableResult" :disabled="disabledTooltip">
                <span class="ellipsis" @mouseenter="handleTooltip">
                  {{ tableResult }}
                </span>
              </Tooltip>
            </div>
          </template>
        </div>
        <!-- @onSortChange="onSortChange"  :loading="loading"-->
        <ui-table
          class="ui-table"
          :max-height="1010"
          :table-columns="tableColumns"
          :table-data="tableData"
          v-ui-loading="{ loading: loading }"
        >
        </ui-table>
        <ui-page
          v-if="pageData.totalCount > 20"
          :page-data="pageData"
          :show-change-size="false"
          @changePage="handlePage"
        >
        </ui-page>
      </div>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { getInfluenceTableColumns } from '../utils/tableConfig.js';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PieEchart: require('../components/pie-echart.vue').default,
    BarEchart: require('../components/bar-echart.vue').default,
    ScatterEchart: require('../components/scatter-echart.vue').default,
    MapEchart: require('../components/map-echart/index.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      activeTabId: '',
      tabList: [],
      tabLoading: false,
      activeInfluenceItem: {},
      defaultSelectIndexs: [],
      defaultProps: {
        name: 'name',
      },
      // 表格
      tableData: [],
      tableColumns: [],
      tableSlotList: [],
      tableResult: '',
      loading: false,
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      disabledTooltip: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  computed: {
    // 影响因素 列表
    getInfluenceList() {
      let tab = this.tabList.filter((item) => item.inflKey === this.activeTabId);
      return tab[0]?.ths || [];
    },
    // 平台或网络
    isPlatform() {
      return this.activeInfluenceItem.key === 'org_code';
    },
  },
  created() {
    this.startWatch(
      '$route',
      () => {
        this.getTabList();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    handleTooltip(e) {
      this.disabledTooltip = e.target.scrollWidth > e.target.clientWidth ? false : true;
    },
    async getTabList() {
      this.activeInfluenceItem = {};
      this.activeTabId = '';
      this.defaultSelectIndexs = [];
      this.tabList = [];
      let { batchId } = this.$route.query;
      if (!batchId) return;
      try {
        this.tabLoading = true;
        let data = {
          batchId: batchId,
        };
        let res = await this.$http.post(dataAnalysis.getHisInfluence, data);
        this.tabList = res.data.data || [];
        if (this.tabList.length > 0) {
          // 默认勾选 第一项
          this.changeTab(this.tabList[0]);
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.tabLoading = false;
      }
    },
    changeTab(tabItem) {
      this.activeTabId = tabItem.inflKey;
      this.defaultSelectIndexs = [0];
      this.changeFactor(tabItem.ths[0] || {});
    },
    changeFactor(item) {
      this.activeInfluenceItem = { ...item };

      let { code } = this.$route.query;
      this.tableColumns = getInfluenceTableColumns({
        title: this.isPlatform ? '平台名称' : item.name,
        dataKey: item.key,
        key: 'dimeName', // 取值字段名
      })[code];
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    onSortChange(column) {
      if (column.order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: column.key,
          sort: column.order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    async getTableList() {
      let { batchId } = this.$route.query;
      if (!batchId) return;
      try {
        this.loading = true;
        let { pageNum, pageSize } = this.pageData;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
          },
          pageNumber: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(dataAnalysis.getFirstInfluenceStat, data);
        let list = res.data.data.pageVo.entities || [];
        // 平台或网络，需要拆分字段
        this.tableData = this.isPlatform
          ? list.map((item) => {
              let valArr = item.dimeName ? item.dimeName.split('#') : ['', ''];
              return { ...item, platformName: valArr[0], dropTime: valArr[1] };
            })
          : list;
        this.tableResult = res.data.data.result || '';
        this.pageData.totalCount = res.data.data.pageVo.total || 0;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.influence-view {
  font-family: MicrosoftYaHei;
  font-size: 14px;
  padding: 16px 20px;
  .tab-box {
    width: 100%;
    height: 50px;
    background: var(--bg-navigation);
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 0 20px;
    .tab-item {
      height: 100%;
      display: flex;
      align-items: center;
      color: var(--color-content);
      margin-right: 30px;
      &:hover {
        color: var(--color-title);
      }
      &.tab-active {
        color: var(--color-title);
        border-bottom: 3px solid var(--color-title);
      }
    }
  }
  .factor-box {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    .factor-title {
      color: var(--color-content);
    }
    // 覆盖组件内的样式
    .tabs-ui {
      flex: 1;
      @{_deep} .tabs {
        background: var(--bg-content) !important;
        .ul {
          li {
            background: var(--bg-nav-tag);
            border: 1px solid var(--border-nav-tag);
            color: var(--color-nav-tag);
          }
          li:hover,
          .active {
            border: 1px solid var(--border-nav-tag-active) !important;
            color: var(--color-nav-tag-active) !important;
            background: var(--bg-nav-tag-active) !important;
          }
        }
      }
    }
  }
  .influence-content {
    flex: 1;
    width: 100%;
    overflow: hidden;
    overflow-y: auto;
    .influence-title {
      width: 100%;
      height: 46px;
      background: var(--bg-navigation);
      display: flex;
      align-items: center;
      margin-bottom: 5px;
      .title-rect {
        display: inline-block;
        width: 5px;
        height: 20px;
        margin-left: 20px;
        background: var(--bg-title-rect);
      }
      .title-span {
        color: var(--color-sub-title-inpage);
      }
      .tip-color {
        color: var(--color-warning);
      }
      .tip-text {
        flex: 1;
        @{_deep}.ivu-tooltip {
          max-width: 100%;
          .ivu-tooltip-rel {
            display: flex;
          }
          .ivu-tooltip-inner {
            white-space: initial;
            max-width: 700px;
            max-height: 400px;
            overflow: hidden;
            overflow-y: auto;
          }
        }
      }
    }
    .table-box {
      width: 100%;
      @{_deep} .ui-table {
        overflow: visible;
        .ivu-table-wrapper {
          height: initial !important;
          max-height: 1010px !important;
          overflow: visible;
        }
        .ivu-table {
          overflow: visible;
          // 滚动区域，实现表格-表头 吸顶效果，注意  滚动区域 到  表头 ，这之间el若存在设置了 overflow， 则需要设置为 overflow: visible; 不然不生效
          .ivu-table-header {
            position: sticky;
            top: 0px;
            z-index: 10;
          }
        }
        .ivu-spin-show-text {
          min-height: 300px;
        }
        .no-shadow .ivu-table-tip {
          min-height: 200px;
        }
      }
    }
    .echarts-view {
      width: 100%;
      height: 398px;
      padding: 3px;
      display: flex;
      margin-bottom: 16px;
      & > div {
        width: calc((100% - 10px) / 2);
        background: var(--bg-sub-echarts-content);
        box-shadow: 0px 0px 5px -1px rgba(147, 171, 206, 0.5);
      }
      .bar-echart-box {
        width: 100%;
      }
    }
  }
}
@{_deep}.unqualified-color {
  color: var(--color-failed);
}
</style>
