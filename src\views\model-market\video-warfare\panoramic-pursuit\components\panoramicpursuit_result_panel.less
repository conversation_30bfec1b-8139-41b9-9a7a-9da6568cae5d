.fullModalBox {
  z-index: 666 !important;
}
.content {
  background-color: #DDE1EA;
}
.skill_body{
  position: relative;
  height: 100%;
  width:100%;

  .skills_content_wrap{
    position: absolute;
    width: 100%;
    height: 100%;
    font-size: 0;
    padding:0px;
    top:0px;
    overflow: hidden;
  }
}
.skill_body .skills_content_wrap{
  .skills_left{
    width:300px;
    height:100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    display:flex;
    flex-direction: column;
    padding-top:0px;
    .label-title{
      font-size: 12px;
      height: 30px;
      padding-left: 20px;
      line-height: 30px;
    }
    .skills_left_title{
      height: 40px;
      display: flex;
      align-items: center;
      padding-left: 20px;
      border-bottom: 1px solid rgba(167,172,184,0.3);
      .lable {
        font-size: 12px;
        margin-right: 5px;
      }
      .input {
        width: 110px;
        margin-right: 8px;
      }
      .xui-input > * {
        vertical-align: inherit;
        font-size: 12px;
      }
    }
    .skills_left_tree_container{
      height: calc(~"(100% - 70px) * 0.7");
      border-bottom:1px solid rgba(167,172,184,0.3);
      overflow-y: auto;
      overflow-x: hidden;
      font-size: 12px;
      .skills_left_tree {
        height: calc(~"100% - 90px");
      }
      .operate-bar {
        height: 90px;
      }
      .trackBtn {
        width: calc(~"100% - 40px");
        height: 30px;
        margin: 10px 20px 0 20px;
      }
      .tree-container{
        margin:0 20px 20px 20px;

        .item-title{
          cursor: pointer;
          line-height: 25px;
          height: 25px;
        }
        .camera-item-list-ul{
          cursor: pointer;
          .showList{
            display:none;
          }
          .camera-item-list-li{
            display: flex;
            padding: 0 4px;
            height: 24px;
            line-height: 24px;
            cursor: pointer;
            &:hover{
              background: rgba(70,121,250,0.1);
            }
            i {
              color: #2c86f8;
              &.offline {
                color: #515a6e;
              }
            }
          }
        }
        .no-camera {
          line-height: 26px;
          padding-left: 24px;
        }
        .icon-tianjia {
          font-size: 12px;
          color: #b1b5bf;
        }
        .icon-shanchu2 {
          font-size: 12px;
          color: #2c86f8;
        }
        span.title{
          font-weight: bold;
        }
        .i-b_sxj, .i-b_qj {
          font-size: 14px;
          margin-right: 5px;
        }
        .online-style {
          color: #2c86f8;
          font-weight: normal;
        }
        .offline-style{
            color: #b1b5bf;
        }
        .icon-focus {
          width: 16px;
          height: 16px;
          margin-right: 3px;
          margin-top: 5px;
          background: url('~@/assets/img/player/playing.gif') no-repeat;
	        background-size: contain;
        }
        .focus {
          color: #ff9429;
          font-weight: 600;
        }
        .camera-item-name{
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            display: inline-block;
            flex: 1;
        }
        .camera-item-meter{
          display: inline-block;
          width: 50px;
          color: #b1b5bf;
        }
        i.i-f_a_sbbf{
          cursor: pointer;
          color: #2c86f8;
          font-size: 12px;
          margin-top: 2px;
          margin-left: 40px;
        }
      }
    }
    #panoramicpursuit-map {
      width: 100%;
      height: calc(~"(100% - 70px) * 0.3");
    }
  }
  .skills_content_right{
    position: absolute;
    top: 0px;
    left: 300px;
    bottom: 0px;
    right: 0px;
    width: calc(~"100% - 300px");
    height:100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    margin-left: 10px;
  }
  #UIOCXSTREETMAP{
    height: 100%;
    width: 100%;
  }
}