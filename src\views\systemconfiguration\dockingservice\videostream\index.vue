<template>
  <div class="page-evaluationmanagement">
    <tagView class="tagView" ref="tagView" :list="['下级域', '本域']" @tagChange="tagChange" />
    <component v-bind:is="currentPage" class="tab"></component>
  </div>
</template>

<script>
export default {
  name: 'videostream',
  data() {
    return {
      currentPage: 'subDomain',
      currentPageEn: '下级域',
    };
  },
  components: {
    tagView: require('./tags').default,
    subDomain: require('./sub-domain-list.vue').default,
    localDomain: require('./local-domain-list.vue').default,
  },
  computed: {},
  async created() {},
  methods: {
    tagChange(index, val) {
      if (this.currentPageEn == val) {
        return;
      }
      this.currentPageEn = val;
      if (val == '下级域') {
        this.currentPage = 'subDomain';
      } else {
        this.currentPage = 'localDomain';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.tagView {
  position: absolute;
  top: 30px;
  left: 30px;
}
</style>
