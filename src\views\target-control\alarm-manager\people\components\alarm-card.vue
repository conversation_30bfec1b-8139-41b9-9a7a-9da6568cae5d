<template>
  <div class="alarm">
    <div class="top">
      <Checkbox
        v-if="isShowCheckBox"
        v-model="alarmInfo.checked"
        @on-change="cardBox($event)"
        @click.stop.native="() => {}"
      />
      <div class="level-content">
        <!-- <div class="brand">
                    <div class="haikang">
                        <span>海康</span>
                    </div>
                </div> -->
        <div class="level-title">
          <img
            v-if="alarmInfo.bgIndex == 1"
            class="level"
            src="@/assets/img/target/title1.png"
            alt
          />
          <img
            v-if="alarmInfo.bgIndex == 2"
            class="level"
            src="@/assets/img/target/title2.png"
            alt
          />
          <img
            v-if="alarmInfo.bgIndex == 3"
            class="level"
            src="@/assets/img/target/title3.png"
            alt
          />
          <img
            v-if="alarmInfo.bgIndex == 4"
            class="level"
            src="@/assets/img/target/title4.png"
            alt
          />
          <img
            v-if="alarmInfo.bgIndex == 5"
            class="level"
            src="@/assets/img/target/title5.png"
            alt
          />
          <div class="num">
            {{
              alarmInfo.taskLevel == 1
                ? "一级"
                : alarmInfo.taskLevel == 2
                ? "二级"
                : "三级"
            }}
          </div>
        </div>
        <!-- <div class="brand">
                    <div class="gerling">
                        <span>格灵</span>
                    </div>
                </div> -->
      </div>
      <!-- <div class="level-title">
                <img v-if="alarmInfo.bgIndex == 1" class="level" src="@/assets/img/target/title1.png" alt />
                <img v-if="alarmInfo.bgIndex == 2" class="level" src="@/assets/img/target/title2.png" alt />
                <img v-if="alarmInfo.bgIndex == 3" class="level" src="@/assets/img/target/title3.png" alt />
                <img v-if="alarmInfo.bgIndex == 4" class="level" src="@/assets/img/target/title4.png" alt />
                <img v-if="alarmInfo.bgIndex == 5" class="level" src="@/assets/img/target/title5.png" alt />
                <div class="num">
                {{ 
                    alarmInfo.taskLevel == 1 ? '一级' :
                    alarmInfo.taskLevel == 2 ? '二级' : '三级'
                }}
                </div>
            </div> -->
      <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
      <div v-if="collectIcon">
        <ui-btn-tip
          class="kjzz-icon"
          content="跨镜追踪"
          icon="icon-mubiaoguankong"
          transfer
          @click.stop.native="toCrossTrack()"
        />
        <ui-btn-tip
          v-if="alarmInfo.myFavorite == 1"
          class="collection-icon"
          content="取消收藏"
          icon="icon-yishoucang"
          transfer
          @click.stop.native="collection(2)"
        />
        <ui-btn-tip
          v-else
          class="collection-icon"
          content="收藏"
          icon="icon-shoucang"
          transfer
          @click.stop.native="collection(1)"
        />
      </div>
    </div>
    <div class="contrast">
      <div class="block border">
        <div class="desc">报警照片</div>
        <ui-image :src="alarmInfo.traitImg"></ui-image>
        <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
      </div>
      <div class="block" style="padding: 12px">
        <!-- <ui-image :src="round"></ui-image> -->
        <!-- <ui-image v-if="alarmInfo.bgIndex == 1" class="animation" :src="c1"></ui-image>
            <ui-image v-else-if="alarmInfo.bgIndex == 2" class="animation" :src="c2"></ui-image>
            <ui-image v-else-if="alarmInfo.bgIndex == 3" class="animation" :src="c3"></ui-image>
            <ui-image v-else-if="alarmInfo.bgIndex == 4" class="animation" :src="c4"></ui-image>
            <ui-image v-else class="animation" :src="c5"></ui-image> -->

        <ui-image
          class="animation"
          :src="getAlgorithmTypeBoundary(alarmInfo.algorithmVendor, 'src')"
        ></ui-image>
        <div
          class="num"
          :class="getAlgorithmTypeBoundary(alarmInfo.algorithmVendor, 'class')"
        >
          {{
            (alarmInfo.simScore.toFixed(4) * 100).toString().substring(0, 5)
          }}%
        </div>
      </div>
      <div class="block border">
        <div class="desc">布控照片</div>
        <ui-image :src="alarmInfo.photoUrl"></ui-image>
      </div>
    </div>
    <div class="info">
      <div class="left">
        <!-- <div class="p" v-for="item in 6">
            <div class="title">布控目标：</div>
            <div class="val">布控人员</div>
            </div> -->
        <div class="p">
          <div class="title">布控目标:</div>
          <div class="val" v-html="alarmInfo.name || '未知'"></div>
        </div>
        <div class="p">
          <div class="title">布控来源:</div>
          <div class="val">
            {{ alarmInfo.taskType == "1" ? "单体布控" : "库布控" }}
            {{
              alarmInfo.taskType == "1" ? "" : "（" + alarmInfo.libName + "）"
            }}
          </div>
        </div>
        <div class="p">
          <div class="title">报警时间:</div>
          <div class="val">{{ alarmInfo.alarmTime }}</div>
        </div>
        <div class="p">
          <div class="title">报警设备:</div>
          <div
            class="val"
            v-html="alarmInfo.deviceName"
            :title="
              alarmInfo.deviceName
                ? alarmInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                : ''
            "
          ></div>
        </div>
        <div class="p">
          <div class="title">所属任务:</div>
          <div class="val">{{ alarmInfo.taskName }}</div>
        </div>
      </div>
      <div class="right">
        <ui-image
          v-if="alarmInfo.operationType == 1"
          class="img"
          :src="valid"
        ></ui-image>
        <ui-image
          v-if="alarmInfo.operationType == 2"
          class="img"
          :src="invalid"
        ></ui-image>
        <ui-image
          v-if="alarmInfo.operationType == 0"
          class="img"
          :src="unproces"
        ></ui-image>
      </div>
    </div>
    <!-- <div class="btn">
        <div>设为有效<span></span></div>
        <div>设为无效</div>
        </div> -->
    <div class="btn" v-if="showOperate">
      <div
        v-if="[0, 2].includes(alarmInfo.operationType)"
        @click.stop="configStatus(1)"
      >
        设为有效<span></span>
      </div>
      <div
        v-if="[0, 1].includes(alarmInfo.operationType)"
        @click.stop="configStatus(2)"
      >
        设为无效<span></span>
      </div>
      <div @click.stop="() => {}" v-if="alarmInfo.operationType != 0">
        <Poptip
          trigger="hover"
          transfer
          word-wrap
          @on-popper-show="showHistory()"
          popper-class="alarm-card-poptip"
        >
          历史处理
          <div slot="title">
            <div class="block"></div>
            <i>历史处理</i>
          </div>
          <div slot="content">
            <Timeline>
              <TimelineItem v-for="item in historyList" :key="item">
                <div class="time">
                  <div class="timeContent">
                    <div>{{ item.handleTime }}</div>
                    <div>操作人：{{ item.creatorName }}</div>
                  </div>
                </div>
                <div class="content">
                  <div class="content1">
                    <div class="p">
                      <span>处理操作：</span>
                      <div>
                        <span
                          >设为{{
                            item.operation == 1 ? '"有效"' : '"无效"'
                          }}</span
                        >
                      </div>
                    </div>
                    <div class="p">
                      <span>处理意见：</span>
                      <div>{{ item.remark || "--" }}</div>
                    </div>
                  </div>
                </div>
              </TimelineItem>
            </Timeline>
          </div>
        </Poptip>
      </div>
    </div>
  </div>
</template>
<script>
import { addCollection, deleteMyFavorite } from "@/api/user";
import { batchHandle, queryAlarmHandleList } from "@/api/target-control";
import round from "@/assets/img/target/round.png";
import valid from "@/assets/img/target/valid.png";
import c2 from "@/assets/img/target/c-two.png";
import c5 from "@/assets/img/target/c-five.png";
import invalid from "@/assets/img/target/invalid.png";
import unproces from "@/assets/img/target/unproces.png";
const algorithmBg = [c5, c2];
const algorithmClass = ["c5", "c2"];
export default {
  props: {
    alarmInfo: {
      type: Object,
      default: () => {},
    },
    isShowCheckBox: {
      type: Boolean,
      default: true,
    },
    // 是否显示收藏图标
    collectIcon: {
      type: Boolean,
      default: true,
    },
    // 是否可以处理
    showOperate: {
      type: Boolean,
      default: true,
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      single: false,
      round,
      valid,
      invalid,
      unproces,
      historyList: [],
    };
  },
  computed: {
    algorithmTypeBoundary() {
      const obj = {};
      this.algorithmTypeSelect?.forEach((key, index) => {
        obj[key] = {
          class: algorithmClass[index],
          src: algorithmBg[index],
        };
      });
      return obj;
    },
  },
  activated() {},
  mounted() {},
  methods: {
    getAlgorithmTypeBoundary(algorithmVendor, attr) {
      return this.algorithmTypeBoundary?.[algorithmVendor]?.[attr] || "";
    },
    collection(num) {
      var param = {
        favoriteObjectId: this.alarmInfo.alarmTopId,
        favoriteObjectType: 14,
      };
      if (num == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$emit("collection");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$emit("collection");
        });
      }
    },
    cardBox(e) {
      this.$emit("singleChecked", e);
    },
    /**
     * 修改状态
     * @param {*} val
     */
    configStatus(val) {
      var param = {
        alarmRecordSimpleForms: [
          {
            alarmTime: this.alarmInfo.alarmTime,
            alarmTopId: this.alarmInfo.alarmTopId,
          },
        ],
        operationType: val,
      };
      batchHandle(param).then((res) => {
        this.$Message.success(res.data);
        this.$emit("refresh");
      });
    },
    /**
     * 处理历史
     */
    showHistory() {
      var param = {
        alarmTime: this.alarmInfo.alarmTime,
        alarmTopId: this.alarmInfo.alarmTopId,
        compareType: this.compareType,
      };
      queryAlarmHandleList(param).then((res) => {
        this.historyList = res.data;
      });
    },
    // 跳转跨镜追踪
    toCrossTrack() {
      let _this = this;
      const { href } = this.$router.resolve({
        path: `/model-market/video-warfare/cross-track`,
        query: {
          noMenu:1
        },
      });
      let app = "page-npmap.html#/model-market/video-warfare/cross-track";
      let win = window.open(
       href,
        "_blank"
      );
      var tryCount = 0,
        timer;
      timer = setInterval(function () {
        tryCount++;
        console.log("......wait ExtendWindow pageInitFinished");
        if (!!(win && win.window && win.pageInitFinished)) {
          clearInterval(timer);
          win.postMessage(JSON.stringify(_this.alarmInfo), "*");
        }
        if (tryCount > 100) {
          clearInterval(timer);
        }
      }, 300);
    },
  },
};
</script>
<style lang="less" scoped>
.alarm {
  position: relative;
  // width: 340px;
  height: 260px;
  // box-shadow: 0 1px 3px #d9d9d9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #ededed;
  background: #f9f9f9;
  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
    .level {
      position: absolute;
      // left: 50%;
      // margin-left: -46px;
    }
    .level-content {
      display: flex;
      justify-content: space-between;
      .brand {
        font-size: 14px;
        font-weight: 700;
        width: 60px;
        height: 19px;
        position: relative;
        text-align: center;
        line-height: 19px;
        span {
          position: relative;
          z-index: 1;
        }
      }
      .haikang {
        color: #fff;
        &::before {
          content: "";
          transform: skewX(24deg);
          background: #2c86f8;
          width: 60px;
          height: 19px;
          position: absolute;
          z-index: 0;
          top: 0;
          left: 0;
        }
      }
      .gerling {
        color: #f29f4c;
        &::before {
          content: "";
          width: 60px;
          height: 19px;
          transform: skewX(-24deg);
          border: 1px solid #f29f4c;
          position: absolute;
          z-index: 0;
          top: 0;
          left: 0;
        }
      }
    }
    .level-title {
      width: 98px;
    }
    .num {
      position: absolute;
      width: 88px;
      text-align: center;
      color: #fff;
    }
  }

  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    margin-top: 5px;
    .block {
      position: relative;
      width: 100px;
      .animation {
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .desc {
        position: absolute;
        z-index: 1;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 0 6px;
      }
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
      // .c1 {
      //   color: #ea4a36;
      // }
      .c2 {
        color: #e77811;
      }
      // .c3 {
      //   color: #ee9f00;
      // }
      // .c4 {
      //   color: #36be7f;
      // }
      .c5 {
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    display: flex;
    padding: 12px;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        height: 22px;
        .title {
          color: rgba(0, 0, 0, 0.6);
          margin-right: 10px;
          line-height: 22px;
        }
        .val {
          color: rgba(0, 0, 0, 0.9);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          line-height: 22px;
        }
      }
    }
    .right {
      width: 92px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      /deep/ .ui-image-div {
        border: none;
      }
      .img {
        width: 92px;
        height: 88px;
      }
    }
  }

  .btn {
    position: absolute;
    width: 100%;
    background: #2c86f8;
    color: #fff;
    display: flex;
    text-align: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    bottom: -30px;
    transition: 0.3s;
    cursor: pointer;
    div {
      position: relative;
      flex: 1;
      span {
        display: inline-block;
        width: 2px;
        height: 20px;
        border-right: 1px solid #d1cbcb;
        position: absolute;
        right: -1px;
        top: 5px;
      }
    }
  }

  &:hover {
    border: 1px solid #2c86f8;
    .btn {
      bottom: 0;
      transition: 0.3s;
    }
  }
}

.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }
  .ivu-tooltip-rel {
    // margin-top: 3px;
  }
  /deep/ .icon-shoucang {
    color: #f29f4c !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}

.kjzz-icon {
  margin-right: 5px;
  /deep/ .iconfont {
    font-size: 14px;
    color: #e77811;
  }
}
</style>
<style lang="less">
.ivu-poptip-popper.alarm-card-poptip {
  width: 450px !important;
  .block {
    width: 3px;
    background: #2c86f8;
    height: 16px;
    float: left;
    margin-top: 3px;
    margin-right: 6px;
  }
}

.ivu-timeline-item {
  .timeContent {
    display: flex;
    justify-content: space-between;
  }
  .content1 {
    .p {
      display: flex;
      align-items: center;
      margin-top: 10px;
      span {
        width: 80px;
      }
      div {
        flex: 1;
      }
    }
  }
}

.ivu-poptip-body {
  max-height: 300px;
  overflow: auto;

  .ivu-timeline-item-head {
    background-color: #e3dada;
    border: 0;
  }

  .ivu-timeline {
    .ivu-timeline-item {
      .ivu-timeline-item-tail {
        left: 10px;
      }

      .ivu-timeline-item-head {
        left: 4px;
      }
      &:first-child {
        .ivu-timeline-item-head {
          background-color: #2d8cf0;
          left: 4px;
        }
        .ivu-timeline-item-head::after {
          content: "";
          position: absolute;
          top: -3px;
          right: -3px;
          width: 19px;
          height: 19px;
          border: 1px solid #2d8cf0 !important;
          border-radius: 50px;
          z-index: 999;
        }
      }
    }
  }
}

/deep/ .ivu-checkbox-input {
  z-index: 999;
}
</style>
