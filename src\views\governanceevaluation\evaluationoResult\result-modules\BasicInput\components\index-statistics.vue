<template>
  <div class="index-statistics" v-ui-loading="{ loading: abnormalListLoading, tableData: abnormalList }">
    <icon-statistics
      class="index-statistics mb-sm"
      :statistics-list="abnormalList"
      :isflexfix="false"
      :listyle="listyle"
    ></icon-statistics>
    <icon-statistics-multiple
      :statistics-list="abnormalListMultiple"
      :isflexfix="false"
      :listyle="listyle"
    ></icon-statistics-multiple>
  </div>
</template>

<script>
import AbnormalListConfig from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AbnormalListConfig';
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'index-statistics',
  components: {
    IconStatistics: require('@/components/icon-statistics').default,
    IconStatisticsMultiple: require('./icon-statistics-multiple').default,
  },
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      abnormalList: [],
      abnormalListLoading: false,
      listyle: {
        height: '0.53rem',
        width: `calc((100% - ${30 / 192}rem) / 4)`,
      },
      abnormalListMultiple: [
        {
          name: '视频<br>监控',
          icon: 'icon-jianceshebeishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color6',
          detail: [
            {
              name: '上级建档',
              key: 'biParentVideoCount',
              value: 0,
            },
            {
              name: '本级建档',
              key: 'biVideoCount',
              value: 0,
            },
            {
              name: '未建档数',
              key: 'videoUnDocCount',
              value: 0,
            },
            {
              name: '建档率',
              key: 'biVideoResultValueFormat',
              value: 0,
            },
          ],
        },
        {
          name: '人脸<br>卡口',
          icon: 'icon-renliankakou',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
          detail: [
            {
              name: '上级建档',
              key: 'biParentFaceCount',
              value: 0,
            },
            {
              name: '本级建档',
              key: 'biFaceCount',
              value: 0,
            },
            {
              name: '未建档数',
              key: 'faceUnDocCount',
              value: 0,
            },
            {
              name: '建档率',
              key: 'biFaceResultValueFormat',
              value: 0,
            },
          ],
        },
        {
          name: '车辆<br>卡口',
          icon: 'icon-cheliangkakou',
          iconColor: 'icon-bg11',
          liBg: 'li-bg11',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color11',
          detail: [
            {
              name: '上级建档',
              key: 'biParentVehicleCount',
              value: 0,
            },
            {
              name: '本级建档',
              key: 'biVehicleCount',
              value: 0,
            },
            {
              name: '未建档数',
              key: 'vehicleUnDocCount',
              value: 0,
            },
            {
              name: '建档率',
              key: 'biVehicleResultValueFormat',
              value: 0,
            },
          ],
        },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getStatInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    /**
     * actualNum        检测总数
     * qualifiedNum        合格数
     * unqualifiedNum        不合格数
     * deviceNum        设备总数
     * resultValue        **率
     * detectionCityOrCountyCount        检测地市数量
     * detectionCityOrCountyUnQualifiedCount        不达标地市数量
     * @returns {Promise<void>}
     */
    async getStatInfo() {
      const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
      let informationStatistics = AbnormalListConfig.find((item) => item.indexId === indexId) || {};
      let abnormalList = JSON.parse(JSON.stringify(informationStatistics.abnormalList || []));
      const importantIdList = [4001, 4002, 4003, 4004];
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        isImportant: importantIdList.includes(indexId) ? '1' : undefined,
      };
      this.abnormalListLoading = true;
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        let statInfo = data || {};
        abnormalList.forEach((value) => {
          (value.name = value.name.replace('地市', this.getOrgName(data.dataType))),
            (value.value = statInfo[value.key] || 0);
          if (value.key === 'resultValueFormat' && statInfo.qualified === '1') {
            value.qualified = true;
          } else if (value.key === 'resultValueFormat' && statInfo.qualified !== '1') {
            value.qualified = false;
          }
        });
        this.abnormalList = abnormalList;
        this.abnormalListMultiple.forEach((item) => {
          item.detail.forEach((val) => {
            val.value = statInfo[val.key] || 0;
          });
        });
      } catch (err) {
        console.log(err);
        this.abnormalList = abnormalList;
      } finally {
        this.abnormalListLoading = false;
      }
    },
    getOrgName(type) {
      //1省 2市 3区县
      switch (type) {
        case '1':
          return '地市';
        case '2':
        case '3':
          return '区县';
        default:
          return '';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.index-statistics {
  position: relative;
  height: 100%;
  width: 100%;
  @{_deep} {
    .information-statistics {
      height: 102px;
    }
  }
  .index-statistics {
    @{_deep} .statistics-ul {
      li:last-child {
        height: 214.5px !important;
        width: calc((100% - 30px) / 4);
        .monitoring-data .ml-md {
          flex: 0;
        }
      }
      //li:nth-last-child(1) {
      //  margin-right: 0;
      //}
    }
  }
}
</style>
