<template>
  <div class="statistics-list-box">
    <div class="tag-bar mb-sm">
      <tag-view :list="tagList" @tagChange="tagChange"></tag-view>
      <Button type="primary" class="button-export" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu mr-xs f-14"></i>
        <span class="ml-xs">导出</span>
      </Button>
    </div>
    <div class="statistics-list-content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @onSortChange="onSortChange"
      >
        <template #action="{ row, column }">
          <div class="action">
            <ui-btn-tip
              v-for="(btnItem, btnIndex) in column.btnArr"
              :key="btnIndex"
              class="mr-sm"
              :icon="btnItem.icon"
              :content="btnItem.text"
              @handleClick="$emit(btnItem.emitFun, row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
    </div>
  </div>
</template>

<script>
import detectionResult from '@/config/api/detectionResult';

import dealWatch from '@/mixins/deal-watch';
import downLoadTips from '@/mixins/download-tips';
import tableColumns from '@/views/governanceevaluation/evaluationoResult/common-pages/statistical-results/util/tableColumns.js';

export default {
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  mixins: [dealWatch, downLoadTips],
  data() {
    return {
      exportLoading: false,
      tagList: [
        {
          'label': '全量',
          'value': 'DEVICE_ALL',
          'sort': -1,
        },
        {
          'label': '普通',
          'value': 'DEVICE_NORMAL',
          'sort': 0,
        },
        {
          'label': '重点',
          'value': 'DEVICE_IMPORTANT',
          'sort': 1,
        },
      ],
      dataDimensionEnum: 'DEVICE_ALL',
      tableColumns: [],
      tableData: [],
      loading: false,
      sortData: {
        // sortField: '', // 排序字段
        // sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      paramsList: {},
      codeKey: '',
    };
  },
  created() {},
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.paramsList = this.$route.query;
        this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
        this.getTableColumns();
        this.getTableList();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    getTableColumns() {
      //根据指标获取columns
      let obj = { ...this.paramsList, indexName: this.activeIndexItem.indexName };
      this.tableColumns = tableColumns(obj)[this.paramsList.indexType];
    },
    tagChange(index, item) {
      this.dataDimensionEnum = item.value;
      this.getTableList();
    },
    onSortChange(column) {
      let { order, key } = column;
      if (order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: key === 'civilName' ? 'civil_code' : key === 'orgName' ? 'org_code' : key,
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    async getTableList() {
      try {
        this.loading = true;
        let params = {
          batchId: this.paramsList.batchId,
          displayType: this.paramsList.statisticType,
          indexId: this.paramsList.indexId,
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          customParameters: {
            dataDimensionEnum: this.dataDimensionEnum,
          },
          ...this.sortData,
        };
        const {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.tableData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 导出
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          batchId: this.paramsList.batchId,
          displayType: this.paramsList.statisticType,
          indexId: this.paramsList.indexId,
          orgRegionCode: this.paramsList[this.codeKey], // displayType为ORG时传组织机构code;displayType为REGION时传行政区划code
          customParameters: {
            dataDimensionEnum: this.dataDimensionEnum,
          },
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportStatInfoList, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.statistics-list-box {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .tag-bar {
    display: flex;
    justify-content: space-between;
  }
}
</style>
