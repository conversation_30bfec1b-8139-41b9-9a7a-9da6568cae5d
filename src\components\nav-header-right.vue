<template>
  <div class="nav-right">
    <div class="user-box">
      <Dropdown @on-click="openNewSystem" class="dropdown">
        <i class="icon-font icon-qiehuanzhizidingyicaidan i-icon"></i>
        <DropdownMenu slot="list">
          <DropdownItem :name="JSON.stringify(item)" v-for="(item, index) in systemList" :key="index">
            {{ item.applicationName }}
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
    <div class="user-box" v-if="!portal">
      <Dropdown @on-click="selectTheme" class="dropdown">
        <i class="icon-font icon-pifu theme-icon"></i>
        <DropdownMenu slot="list">
          <DropdownItem
            v-for="item in allThemeList"
            :key="item.theme"
            :name="item.theme"
            :selected="themeType === item.theme"
            >{{ item.text }}</DropdownItem
          >
        </DropdownMenu>
      </Dropdown>
    </div>
    <div class="user-box user-center">
      <author-poptip v-if="effectiveDays <= noticeDay" :effectiveDays="effectiveDays"></author-poptip>
      <Dropdown @on-click="onClickUserCenter" class="dropdown">
        <a href="javascript:void(0)" @click="onClickUserCenter()" :title="`${userInfo.name}-${organization}`" class="user-item">
          <div class="user-avatar vt-middle">
            <Badge class="message-num" :count="noticeNum" overflow-count="99">
              <ui-avatar :src="userInfo.avatar"></ui-avatar>
            </Badge>
          </div>
          <div class="user-name vt-middle">
            <span>{{ userInfo.name }}</span>
            <span>-</span>
            <span>{{ organization }}</span>
          </div>
          <Icon type="md-arrow-dropdown i-icon" class="f-16" />
        </a>
        <DropdownMenu slot="list">
          <DropdownItem
            :name="JSON.stringify(item)"
            v-for="(item, index) in userSystemList"
            :key="index"
            :divided="item.name === 'login'"
          >
            <span>{{ item.applicationName }}</span>
            <Badge v-if="item.name === 'mymessage'" class="message-num" :count="noticeNum" overflow-count="99"> </Badge>
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
    <div class="user-box">
      <Tooltip content="关闭">
        <div class="exit-wrapper" @click="logOut">
          <div class="exit-icon mr-xs" >
            <i class="icon-font icon-tuichuanniu log-out f-14"></i>
          </div>
          <span>关闭</span>
        </div>
      </Tooltip>
    </div>
  </div>
</template>

<script>
import common from '@/config/api/common';
import user from '@/config/api/user';
import { mapActions, mapGetters } from 'vuex';
import logOutMixin from '@/mixins/logout-mixin';
import { allThemeList } from '@/style/theme.js';
export default {
  mixins: [logOutMixin],
  props: {
    portal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      allThemeList,
      userMessage: {}, //登录用户的信息
      systemList: [],
      userSystemList: [
        { applicationName: '个人中心', name: 'usercenter' },
        { applicationName: '我的下载', name: 'downloadcenter' },
      ],
      effectiveDays: 0,
      noticeDay: 0,
    };
  },
  async mounted() {
    this.userMessage = JSON.parse(window.sessionStorage.getItem('userMessage')) || '';
    this.applicationList();
    this.getNoticeDay();
    this.effectiveDays = this.$store.state.common.authorInfo ? this.$store.state.common.authorInfo.effectiveDays : 0;
    this.setNoticeNum();
  },
  methods: {
    ...mapActions({
      setManagerAppUrl: 'user/setManagerAppUrl',
      setNoticeNum: 'websocket/setNoticeNum',
      setThemeType: 'common/setThemeType',
    }),
    async getNoticeDay() {
      try {
        const res = await this.$http.get(common.getAuthorNoticeDay);
        this.noticeDay = res.data.data.paramValue;
      } catch (err) {
        console.log(err);
      }
    },
    // 获取应用
    applicationList() {
      this.$http.get(user.getSysApplicationInfo).then((res) => {
        res.data.data.map((val) => {
          if (val.applicationCode !== '00000002') {
            this.systemList.push(val);
          }

          // 工具集跳转采集系统管理平台
          if (val.applicationCode === '00000004') {
            this.setManagerAppUrl(val.address);
          }
        });
      });
    },
    selectHome(name) {
      this.$router.push({ name: name });
    },
    onClickUserCenter(val) {
      if (!val) {
        this.addTab({ applicationName: '个人中心', name: 'usercenter' });
        return;
      }
      let obj = JSON.parse(val);
      if (obj.name === 'login') {
        this.logOut();
        return;
      }
      this.addTab(obj);
    },
    openNewSystem(val) {
      let obj = JSON.parse(val);
      if (!obj.applicationCode) {
        this.addTab(obj);
      } else {
        window.open(obj.address + '?refresh_token=' + this.$store.state.user.token);
      }
    },
    //添加标签
    addTab(route) {
      const index = this.cacheRouterList.findIndex((row) => row.name === route.name);
      if (index === -1) {
        this.cacheRouterList.push({
          name: route.name,
          path: `/${route.name}`,
          text: route.applicationName,
        });
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({
        name: route.name,
      });
    },
    async selectTheme(theme) {
      if (this.themeType === theme) {
        return;
      }
      await this.setThemeType(theme);
      window.location.reload();
    },
    logOut() {
      this.logOutMx();
      // this.$UiConfirm({ content: '您确定要退出吗？' }).then(() => {
      //   // logOutMixin中方法
      //   this.logOutMx();
      // });
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
      userInfo: 'user/getUserInfo',
      noticeNum: 'websocket/getNoticeNum',
      themeType: 'common/getThemeType',
    }),
    organization(){
      if (!this.userInfo.orgVoList.length ) return;
      return this.userInfo.orgVoList[0]['orgName'];
    }
  },
  components: {
    UiAvatar: require('@/components/ui-avatar').default,
    AuthorPoptip: require('@/components/author-poptip').default,
  },
};
</script>

<style lang="less" scoped>
.nav-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 100%;
  .user-box {
    cursor: pointer;
    height: 24px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    &:last-of-type {
      margin-right: 0;
    }
    .user-item {
      display: flex;
      align-items: center;
    }
    .log-out {
      line-height: 14px;
      color: #fff;
    }
    .message,
    .theme-icon,
    .i-icon {
      font-size: 17px;
      color: var(--color-header-icon);
    }
    .user-avatar {
      height: 32px;
      display: inline-block;
    }
    .message-num {
      @{_deep}.ivu-badge-count {
        box-shadow: none;
        transform: translateX(67%) scale(0.8);
      }
    }
    .user-name {
      display: inline-block;
      max-width: 120px;
      height: 32px;
      margin-left: 10px;
      color: var(--color-touxiang);
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      span {
        line-height: 32px;
      }
    }
    .exit-wrapper {
      display: flex;
      align-items: center;
      color: #f8775c;
      .exit-icon {
        width: 24px;
        height: 24px;
        background: #f8775c;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

  }
}
.user-center {
  margin-right: 21px;
}
.dropdown {
  @{_deep} .ivu-dropdown-item {
    color: var(--color-dropdown-item);
    text-align: left;
  }
  @{_deep} .ivu-select-dropdown {
    background-color: var(--bg-layout-header-tab);
  }
  @{_deep} .ivu-dropdown-item:hover {
    background: var(--bg-select-item-hover);
    color: var(--color-select-item);
  }
  @{_deep} .ivu-dropdown-item-divided {
    border-top: 1px solid var(--devider-line);
    &::before {
      background-color: initial;
    }
  }
}
</style>
