<template>
  <div class="keyperson">
    <!-- 重点人 -->
    <common-form
      ref="commonForm"
      :label-width="160"
      :form-data="formData"
      :form-model="formModel"
      :module-action="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="extract">
        <template
          v-if="
            ['FOCUS_TRACK_REAL', 'FOCUS_REPEAT_RATE', 'FOCUS_DEVICE_RELATED_RATE'].includes(moduleAction.indexType) &&
            formData.detectMode === '3'
          "
        >
          <FormItem
            prop="deviceQueryForm.dayByCapture"
            label="图片检测范围"
            :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          >
            <span class="base-text-color mr-xs">近</span>
            <InputNumber
              v-model.number="formData.dayByCapture"
              :min="0"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color">天，</span>
            <InputNumber
              v-model.number="formData.startByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点至</span>
            <InputNumber
              v-model.number="formData.endByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点</span>
          </FormItem>
        </template>
      </div>
    </common-form>
    <Form
      v-if="['FOCUS_ACCURACY', 'FOCUS_TRACK_REAL'].includes(moduleAction.indexType)"
      ref="formData"
      class="form-content edit-form"
      :model="formData"
      :label-width="160"
    >
      <FormItem v-if="moduleAction.indexType === 'FOCUS_ACCURACY'" label="检测规则设置" class="right-item mb-lg">
        <rule-list
          :formModel="formModel"
          :formData="formData"
          :moduleAction="moduleAction"
          topicType="5"
          v-ui-loading="{ loading, tableData: indexRuleList }"
          :indexRuleList="indexRuleList"
        ></rule-list>
      </FormItem>
      <template v-if="['FOCUS_TRACK_REAL'].includes(moduleAction.indexType)">
        <FormItem label="轨迹上传时延" class="right-item mt-md">
          <FormItem
            class="inline one-time"
            prop="beforeTimeDelay"
            :rules="[{ validator: validateTimeDelayBefore, trigger: 'change', required: false }]"
          >
            <InputNumber
              v-model.number="formData.beforeTimeDelay"
              :formatter="(value) => `${parseInt(value)}`"
              class="width-mini"
            ></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="beforeTimeFormat">
            <Select v-model="formData.beforeTimeFormat" transfer class="width-mini ml-sm">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
          <span class="base-text-color">&nbsp;&le; 接收时间 - 抓拍时间 &le;&nbsp;</span>
          <FormItem
            class="inline one-time"
            prop="timeDelay"
            :rules="[{ validator: validateTimeDelay, trigger: 'change', required: true }]"
          >
            <InputNumber
              v-model.number="formData.timeDelay"
              :formatter="(value) => `${parseInt(value)}`"
              class="width-mini mr-sm"
            ></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="timeFormat">
            <Select v-model="formData.timeFormat" transfer class="width-mini">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
        </FormItem>
        <p class="color-failed" :style="{ marginLeft: `160px` }">说明：不配置表示允许时间倒挂</p>
      </template>
    </Form>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    CommonForm: require('./common-form/index').default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
  },
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      loading: false,
      indexRuleList: [],
      formData: {
        threshold: null,
        dataType: '0',
        detectMode: '1',
        ruleList: [],
        beforeTimeDelay: null, //轨迹时延
        beforeTimeFormat: 'h',
        timeDelay: null,
        timeFormat: 'h',
        //图片检测范围
        dayByCapture: 2,
        startByCapture: 0,
        endByCapture: 23,
      },
      validateTimeDelay: (rule, value, callback) => {
        let { timeDelay } = this.formData;
        if (!timeDelay && timeDelay !== 0) {
          callback(new Error('请输入时间'));
        } else if (timeDelay <= 0) {
          callback(new Error('只允许填写正数'));
        }
        callback();
      },
      validateTimeDelayBefore: (rule, value, callback) => {
        let { beforeTimeDelay } = this.formData;
        if (beforeTimeDelay > 0) {
          callback(new Error('不允许填写正数'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片检测范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片检测范围开始时间不能大于结束时间'));
        }
        callback();
      },
    };
  },
  async created() {
    if (this.ocrCheckModelList.length == 0) await this.getAlldicData();
    this.getCheckRule();
  },
  watch: {
    loadConfig: {
      handler(val) {
        this.setFormData();
        if (val) {
          this.setRuleListConfig();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    setFormData() {
      if (this.formModel === 'edit') {
        this.formData = {
          ...this.formData,
          ...this.configInfo,
        };
      } else {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode: regionCode, // 检测对象
          detectMode: '1',
          deviceQueryForm: {
            detectPhyStatus: '0',
          },
        };
      }
    },
    setRuleListConfig() {
      this.indexRuleList.forEach((value) => {
        let rule = this.formData.ruleList.find((item) => value.ruleCode === item.ruleCode);
        this.$set(value, 'isConfigure', (rule && rule.isConfigure) || '0');
        return value;
      });
    },
    // 表单提交校验
    async handleSubmit() {
      let valid = true;
      if (this.$refs['formData']) {
        //此form仅在部分指标中有，故需要增加判断
        valid = await this.$refs['formData'].validate();
      }
      let validate = await this.$refs['commonForm'].handleSubmit();
      if (validate) {
        this.formData.ruleList = this.indexRuleList.filter((value) => value.isConfigure === '1');
      }
      return validate && valid;
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
    async getCheckRule() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: this.moduleAction.indexType },
        });
        this.indexRuleList = data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    setDefaultTime() {
      //实时轨迹上传及时性   默认2h
      if (this.moduleAction.indexType === 'FOCUS_TRACK_REAL') {
        if (!this.formData.timeDelay) {
          this.formData.timeDelay = 2;
        }
        if (!this.formData.timeFormat) {
          this.formData.timeFormat = 'h';
        }
      }
    },
  },
  computed: {
    ...mapGetters({
      ocrCheckModelList: 'algorithm/ivdg_image_ods_check_model',
    }),
    loadConfig() {
      if (this.indexRuleList.length && this.configInfo) {
        return true;
      }
      return false;
    },
  },
};
</script>
<style lang="less" scoped>
@import url('../../../../components/common.less');
.keyperson {
  margin-bottom: 20px;
  .text {
    margin-left: 160px;
  }
  .dis-select {
    user-select: none;
  }
  .setting {
    color: var(--color-primary);
  }
  .params-lazy-time {
    width: 160px;
  }
  @{_deep} .select-width {
    width: 380px;
  }
  .one-time /deep/ .ivu-form-item-error-tip {
    width: 155px;
  }
}
</style>
