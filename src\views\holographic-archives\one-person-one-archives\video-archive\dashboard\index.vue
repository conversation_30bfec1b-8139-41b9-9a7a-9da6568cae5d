<template>
    <section class="content">
        <div class="content-left">
            <ui-card title="基本信息 " class="m-b20 info-msg">
                <div class="info-content">
                <img
                    class="bg-yzx"
                    v-if="baseInfo.realNameArchiveNo"
                    src="@/assets/img/icons/icon-yzx.png"
                    alt=""
                />
                <p>
                    <span class="label">聚类图像数量</span
                    ><b class="weight">{{ baseInfo.clusteringImagesNum }}</b>
                </p>
                <p>
                    <span class="label">首次抓拍时间</span
                    ><b>{{ baseInfo.firstCaptureTime }}</b>
                </p>
                <p>
                    <span class="label">首次抓拍地点</span
                    ><b
                    :title="baseInfo.firstCaptureLocation"
                    class="ellipsis address"
                    >{{ baseInfo.firstCaptureLocation }}</b
                    >
                </p>
                <p>
                    <span class="label">最近抓拍时间</span
                    ><b>{{ baseInfo.recentCaptureTime }}</b>
                </p>
                <p>
                    <span class="label">最近抓拍地点</span
                    ><b
                    :title="baseInfo.recentCaptureLocation"
                    class="ellipsis address"
                    >{{ baseInfo.recentCaptureLocation }}</b
                    >
                </p>
                </div>
            </ui-card>
            <div class="ui-card m-b20">
                <div class="card-head">
                <p
                    class="capture-title face-capture"
                    :class="tabType === 0 ? 'capture-active' : ''"
                    @click="handleTab(0)"
                >
                    <span>同行人员</span>
                </p>
                <div
                    class="capture-title car-capture"
                    :class="tabType === 1 ? 'capture-active' : ''"
                    @click="handleTab(1)"
                >
                    <span>驾乘车辆</span>
                </div>
                </div>
                <div class="card-content">
                <peer-swiper
                    v-if="tabType === 0"
                    :loading="peeLoading"
                    :list="peerList"
                    @details="handlePeerDetails"
                ></peer-swiper>
                <CarSwiper
                    v-if="tabType === 1"
                    :loading="peeLoading"
                    :type="tabType"
                    :list="drivingList"
                />
                </div>
            </div>
            <div class="ui-card m-b20">
                <div class="card-head">
                <p
                    class="capture-title face-capture"
                    :class="carType2 === 1 ? 'capture-active' : ''"
                    @click="handleTab2(1)"
                >
                    <span>抓拍记录</span>
                </p>
                <div
                    class="capture-title car-capture"
                    :class="carType2 === 2 ? 'capture-active' : ''"
                    @click="handleTab2(2)"
                >
                    <span>最新报警</span>
                </div>
                </div>
                <div class="card-content" v-if="carType2 === 1">
                <snapRecord
                    type="video"
                    :list="captureList"
                    :loading="recordLoading"
                />
                </div>
                <div class="card-content alarm-content" v-else>
                <alarm-tab
                    ref="alarmTab"
                    :carType="1"
                    :showCount="false"
                    :vid="archiveNo"
                ></alarm-tab>
                </div>
            </div>
        </div>
        <div class="content-middle">
        <!--标签图谱-->
            <label-cloud-view
                :labels="baseInfo.labels || []"
                :type="2"
                :info="baseInfo"
            />
            <!--底部swiper-->
            <middle-swiper :relationship="relationship" type="video" />
        </div>
        <div class="content-right">
            <ui-card title="关系统计" class="m-b20" :padding="0" v-if="graphObj">
                <div
                slot="extra"
                class="play-btn mr-20 color-primary cursor-p"
                @click="toRelationGraph"
                >
                关系图谱
                </div>
                <graph
                v-if="hasGraphData"
                @finish="graphLoaded"
                class="right"
                :relation-can-operate="false"
                ></graph>
                <ui-empty v-else></ui-empty>
            </ui-card>
            <!--行为规律-->
            <law-behaviour
                :dataType="2"
                :archiveNo="this.$route.query.archiveNo"
            ></law-behaviour>
            <ui-card title="最新位置">
                <mapBase
                v-if="baseInfo.geoPoint"
                :mapLayerConfig="{ showLatestLocation: true }"
                :chooseMapPosition="{
                    lat: baseInfo.geoPoint.lat,
                    lon: baseInfo.geoPoint.lon,
                    address: baseInfo.recentCaptureLocation,
                }"
                />
            </ui-card>
        </div>
        <listModal ref="listrefModal"></listModal>
    </section>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "../../../components/snap-record";
import middleSwiper from "./components/middle-swiper";
import peerSwiper from "./components/peer-swiper";
import lawBehaviour from "../../../components/law-behaviour";
import labelCloudView from "../../../components/label-cloud-view/index";
import CarSwiper from "../../people-archive/dashboard/components/car-swiper.vue";
import mapBase from "@/components/map/index/";
import CollapseExpand from "@/components/relation-graph/collapse-expand";
import imgloading from "@/assets/img/car1.webp";
import { alarmPageList } from "@/api/target-control";
import {
  drivingVehicle,
  getPortraitCapture,
  relationshipCardVideo,
} from "@/api/realNameFile";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
import alarmTab from "@/views/perception-home/perception-site/components/alarm-tab.vue";
import { queryFacePeerCountPageListByTimeliness } from "@/api/modelMarket";
import listModal from "./components/list-modal.vue";
export default {
  mixins: [relativeGraphMixin],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    swiper,
    swiperSlide,
    snapRecord,
    peerSwiper,
    lawBehaviour,
    labelCloudView,
    middleSwiper,
    CollapseExpand,
    mapBase,
    CarSwiper,
    alarmTab,
    listModal,
  },
  props: {
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      peeLoading: false,
      recordLoading: false,
      archiveNo: "", //人员档案id
      tabType: 0, //0同行人员1驾乘车辆
      carType2: 1, //1抓拍记录 2 最新报警
      drivingList: [], //驾乘车辆
      relationship: {}, //关系卡片
      alarmInfo: {
        total: 0,
      }, //关系卡片
      peerList: [],
      captureList: [],
      labelList: [
        { id: "1", name: "不戴眼镜", color: "#EA4A36" },
        { id: "1", name: "不戴帽子", color: "#1FAF8A" },
        { id: "1", name: "昼伏夜出", color: "#ef2381" },
        { id: "1", name: "短发", color: "#48BAFF" },
        { id: "1", name: "重点人员", color: "#EA4A36" },
        { id: "1", name: "频繁出没", color: "#ef8932" },
        { id: "1", name: "戴口罩", color: "#48BAFF" },
      ],
      swiperOption: {
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
          renderBullet(index, className) {
            return `<span class="${className} swiper-pagination-bullet-custom">${index + 1}</span>`;
          },
        },
      },
      atlasList: [
        // {
        //   id: 1,
        //   text: '苏A 29999',
        //   img: imgloading,
        //   children: [
        //     // { id: 2, num: 2, text: '同抓拍' },
        //     // { id: 3, num: 2, text: '同涉案' },
        //     // { id: 4, num: 2, text: '驾乘人员' },
        //     // { id: 5, num: 2, text: '同车主' },
        //     // { id: 6, num: 2, text: '同车主' },
        //     // { id: 7, num: 2, text: '同车主' }
        //   ]
        // }
      ],
      options: {
        disableDragNode: true,
        allowShowMiniToolBar: false,
        // disableZoom: true,
        defaultExpandHolderPosition: "hide",
        checkSelect: false,
        fontSize: 12,
        layouts: [
          {
            label: "中心",
            layoutName: "center",
            layoutClassName: "seeks-layout-center",
            // 节点间距
            distance_coefficient: 0.5,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      relationList: "number-cube/getVideoRelationList", // 关系统计
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },
  watch: {
    // 'baseInfo': {
    //   deep: true,
    //   handler (val) {
    //     console.log('-----video baseInfo', val)
    //     this.atlasList[0].img = val.photo
    //   }
    // },
    // 'relationList': {
    //   deep: true,
    //   handler(val) {
    //     var list = []
    //     val.forEach((item, index) => {
    //       var obj = {
    //         id: index+2,
    //         num: item.count,
    //         text: item.nameCn
    //       }
    //       list.push(obj)
    //     })
    //     this.atlasList[0].children = list
    //     // this.$refs.CollapseExpand.showSeeksGraph()
    //   }
    // }
  },
  mounted() {
    let query = this.$route.query;
    // 防止因为Anchor锚点导致的路由query参数丢失，跳档案都先跳到这个页面，先把参数存storage
    sessionStorage.setItem("query", JSON.stringify(query));
    this.archiveNo = query.archiveNo;
    this.getPeerData();
    // this.alarmPageList()
    this.getPortraitCapture();
    this.getCard();
    // this.relationStat({type: 'video', searchKey: this.$route.query.archiveNo})
    var list = [];
    this.relationList.forEach((item, index) => {
      var obj = {
        id: index + 2,
        num: item.count,
        text: item.nameCn,
      };
      list.push(obj);
    });
    // this.atlasList[0].img = this.baseInfo.photo
    // this.atlasList[0].children = list
    // setTimeout(() => {
    //   console.log(this.baseInfo, 'this.baseInfo')
    // }, 1000)
  },
  methods: {
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    // 驾乘车辆
    drivingVehicle() {
      this.peeLoading = true;
      let data = {
        archiveNo: this.archiveNo,
        dataType: 2, //1实名2视频
        dataSize: 5, //默认5条
      };
      drivingVehicle(data)
        .then((res) => {
          if (res.code === 200) {
            this.drivingList = res.data || [];
          }
        })
        .catch(() => {})
        .finally(() => {
          this.peeLoading = false;
        });
    },
    // 最近抓拍和最新报警切换
    handleTab2(val) {
      this.carType2 = val;
      if (val === 1) {
        // 抓拍记录
        this.getPortraitCapture();
      }
    },
    // 抓拍记录
    getPortraitCapture() {
      this.recordLoading = true;
      let data = {
        archiveNo: this.archiveNo,
        queryAllTime:true,
        dataType: 2,
        dataSize: 5,
      };
      getPortraitCapture(data)
        .then((res) => {
          if (res.code === 200) {
            this.captureList = res.data || [];
          }
        })
        .catch(() => {})
        .finally(() => {
          this.recordLoading = false;
        });
    },
    alarmPageList() {
      var params = {
        vid: this.archiveNo,
        pageNumber: 1,
        pageSize: 5,
      };
      alarmPageList(params).then((res) => {
        this.captureList = res.data.entities;
        var row = this.captureList[0];
        row.total = res.data.total;
        this.relationship.alarmInfo = row;
        this.alarmInfo = row;
        this.$forceUpdate();
        console.log("this.relationship", this.relationship);
      });
    },
    // 关系卡片
    async relationshipCardVideo(val) {
      let data = {
        archiveNo: this.archiveNo,
        type: val,
      };
      let res = await relationshipCardVideo(data);
      return res.data;
    },
    // 循环关系卡片
    async getCard() {
      this.relationship = {};
      // 常去地
      let place = await this.relationshipCardVideo(2);
      this.relationship = {
        place: place || {},
        control: {},
        alarmInfo: this.alarmInfo,
      };
    },
    // 同行人员/乘车辆切换
    handleTab(val) {
      this.tabType = val;
      if (val === 1) {
        // 驾乘车辆
        this.drivingVehicle();
      } else {
        this.getPeerData();
      }
    },
    // 同行人员
    getPeerData() {
      this.peerList = [];
      this.peeLoading = true;
      let data = {
        // dateType: 2, // 这里调用该接口不需要该参数
        vid: this.archiveNo,
        pageNumber: 1,
        pageSize: 5,
      };
      queryFacePeerCountPageListByTimeliness(data)
        .then((res) => {
          if (
            res.code === 200 &&
            res.data.entities &&
            res.data.entities.length > 0
          ) {
            this.peerList = res.data.entities.map((item) => {
              return {
                name: item.deviceAddress,
                dataType: "1",
                num: item.peerNum,
                time: item.absTime,
                img: item.traitImg,
                ...item
              };
            });
          }
        })
        .catch(() => {})
        .finally(() => {
          this.peeLoading = false;
        });
    },
    handlePeerDetails(item, index) {
        console.log(this.baseInfo, 'baseInfo')
        this.$refs.listrefModal.init(item, this.baseInfo.archiveNo)
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: auto;
  min-height: 780px;
  padding: 16px 20px 20px;
  box-sizing: border-box;
}
.content-left,
.content-right {
  width: 500px;
  display: flex;
  flex-direction: column;
  height: 100%;
  .ui-card,
  .info-msg {
    flex: 1;
    max-height: calc(~"33.3% - 20px");
  }
  /deep/ .card-content {
    height: calc(~"100% - 30px");
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  /deep/ .ivu-tabs-content {
    height: calc(~"100% - 30px");
    .ivu-tabs-tabpane {
    }
  }
}
.content-middle {
  overflow: hidden;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.info-content {
  position: relative;
  .bg-yzx {
    position: absolute;
    top: -25px;
    right: -10px;
    width: 95px;
    height: 75px;
  }
  p {
    display: flex;
    font-size: 14px;
    padding: 8px 0;
    color: rgba(0, 0, 0, 0.9);
    .label {
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      font-weight: bold;
      white-space: nowrap;
      color: #2c86f8;
      margin-right: 15px;
    }
    b {
      font-weight: normal;
      word-break: break-all;
      &.half {
        width: 110px;
      }
      &.weight {
        font-size: 20px;
        line-height: 1;
        font-weight: bold;
      }
      &.address {
        width: 420px;
      }
      &.car-address {
        width: 160px;
      }
    }
  }
}
.capture-title {
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  background: #d3d7de;
  color: #666;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;
  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}
.face-capture {
  position: relative;
}
.car-capture {
  position: absolute;
  left: 120px;
}
.capture-active {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
}
.card-content {
  padding: 20px;
}
.alarm-content {
  padding: 20px 0;
}
.map-box {
  margin-bottom: 0;
}
.movelink {
  cursor: pointer;
}
</style>
