<template>
  <div class="auto-fill">
    <div>
      <ChartsContainer :abnormalCount="abnormalCount" />
      <div class="hearder-title">
        <network-inspection-search
          :currentTree="currentTree"
          :treeData="treeData"
          :taskObj="taskObj"
          :width="width"
          :searchList="searchList"
          @startSearch="startSearch"
          @params-change="paramsChange"
        />
      </div>
    </div>
    <ui-table class="auto-fill" :table-columns="columns" :table-data="tableData" :loading="loading">
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #deviceStatus="{ row }">
        <span class="tag" :style="{ background: row.deviceStatus === '1' ? '#0E8F0E' : '#BC3C19' }">{{
          checkStatusList(row.deviceStatus)
        }}</span>
      </template>
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page
      class="page"
      :pageData="pageData"
      :hasLast="hasLast"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="width: 95%; margin: auto">
        <EasyPlayer :video-url="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { icons } from '../components/common';
import inspectionrecord from '@/config/api/inspectionrecord';
import videoThrem from '@/config/api/vedio-threm';
import { decryptDes } from '@/util/module/common';
export default {
  name: 'capture-rationality',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    NetworkInspectionSearch: require('./network-inspection-search').default,
    ChartsContainer: require('../components/chartsContainer').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
  props: {
    /**
     * 右上角检测任务筛选
     */
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    /**
     * 左侧树结构
     */
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      loading: false,
      hasLast: false,
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      searchList: [], // 设备模式检索下拉框
      abnormalCount: [{ title: '采集设备总数', icon: 'icon-exceptionlibrary' }], // 统计展示
      modelTag: 0, // 聚档模式,图像模式
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      minusHeight: 512, // 表格
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          width: 200,
        },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', tooltip: 'true' },
        { title: '组织机构', key: 'orgCode' },
        { title: this.global.filedEnum.sbdwlx, key: 'sbdwlx', width: 150 },
        { title: `${this.global.filedEnum.ipAddr}`, key: 'ipAddr', width: 150 },
        { title: '设备联网状态', slot: 'deviceStatus', width: 100 },
        { title: '检测时间', key: 'createTime', width: 150 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 60,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      cardInfo: [],
      cardSearchList: [],
      tableData: [],
      currentIcon: 'icon-exceptionlibrary',
      videoVisible: false,
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
    };
  },
  computed: {
    computedDeviceType() {
      /**
       * ("设备类型 1-车辆卡扣 2-视图抓拍类")
       * private String deviceType;
       * 309 视图抓拍类
       * 412 车辆卡口
       */
      return this.currentTree.id === 309 ? 2 : 1;
    },
  },
  watch: {
    currentTree: {
      deep: true,
      immediate: true,
      handler: function () {
        this.getIcon();
      },
    },
  },
  methods: {
    paramsChange(val) {
      this.searchData = val;
    },
    onCancel() {
      this.$refs.livePlayer.cancelplay();
      this.$http.post(videoThrem.stop + this.playDeviceCode);
    },
    async clickRow(row) {
      try {
        this.videoVisible = true;
        let params = {
          deviceId: row.deviceId,
        };
        let {
          data: { data },
        } = await this.$http.post(videoThrem.getplay, params);
        this.videoUrl = decryptDes(data.ts, 'QSDI123456');
      } catch (err) {
        console.log(err);
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        if (!this.searchData.orgCode) {
          return this.$Message.error('请选择组织机构');
        }
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(
          inspectionrecord.getConnectInternetList,
          Object.assign(
            this.pageData,
            {
              indexId: this.currentTree.indexId,
              deviceType: this.computedDeviceType,
            },
            this.searchData,
          ),
        );
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    async statisticsCount() {
      // 统计接口
      let params = {
        orgCode: this.searchData.orgCode,
        resultId: this.searchData.resultId,
        indexId: this.currentTree.indexId,
      };
      let {
        data: { data },
      } = await this.$http.post(inspectionrecord.getConnectInternetStatistics, params);
      this.infoObj = data;
    },
    // 检索
    async startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      if (!this.searchData.orgCode) {
        return this.$Message.error('请选择组织机构');
      }
      this.$nextTick(async () => {
        await this.statisticsCount();
        await this.abnormalCountMap();
        this.initList();
      });
    },
    // 组织机构切换
    currentChange(data) {
      this.currentOrgObj = data;
    },
    getIcon() {
      this.currentIcon = icons[this.currentTree.id] || 'icon-exceptionlibrary';
    },
    // 统计参数填充
    abnormalCountMap() {
      let map = {
        shouldCheckDeviceNum: '视频监控设备总数',
        practicalCheckDeviceNum: '实际检测设备数量',
        unConnectNum: '未联网设备数量',
        connectNum: '已联网设备数量',
        scale: '联网率',
      };
      let list = [];
      Object.keys(this.infoObj).map((key) => {
        // 检测记录去掉scale
        if (key !== 'scale') {
          let value = this.infoObj[key];
          list.push({
            title: map[key] || '',
            count: value,
            icon: this.currentIcon,
          });
        }
      });
      this.abnormalCount = list;
      if (!list.length) {
        this.abnormalCount = [{ title: '采集设备总数', icon: this.currentIcon }];
      }
      return list;
    },
    checkStatusList(deviceStatus) {
      return deviceStatus === '1' ? '联网' : '未联网';
    },
  },
};
</script>
<style lang="less" scoped>
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;

  .mr20 {
    margin-right: 20px;
  }

  .blue {
    color: #19c176;
  }
}

.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.white-circle {
  position: relative;
  display: inline-block;
  line-height: 10px;
  vertical-align: middle;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #f5f5f5;
}
</style>
