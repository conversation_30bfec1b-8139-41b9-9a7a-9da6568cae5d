<template>
    <div v-scroll ref="scroll" class="history-track">
        <div v-if="trackList.length">
            <div class="group-item" v-for="(group, groupIndex) in trackList" :key="groupIndex">
                <div class="group-header">
                    <i class="iconfont i-f_a_ls"></i>
                    <span class="name" :title="group.name" @click="showCameraList(groupIndex)">{{group.name}}</span>
                    <i class="iconfont icon-shengchengguiji" title="查看轨迹" @click="viewTrack(group)"></i>
                    <i class="iconfont icon-shanchu1" title="删除轨迹" @click="deleteTrack(group)"></i>
                </div>
                <div v-if="group.data&&group.data.length">
                    <div v-show="groupIndex===currentGroupIndex" class="camera-item" v-for="(camera, index) in group.data" :key="index">
                        <span class="number">{{index+1}}</span>
                        <span class="name" :title="camera.name">{{camera.name}}</span>
                        <span class="time" :title="camera.time|FTimeFil">{{camera.time|FTimeFil}}</span>
                        <i class="iconfont icon-shanchu1" title="删除轨迹点位" @click="deleteTrack(camera)"></i>
                    </div>
                </div>
                <div class="no-point" v-else v-show="groupIndex===currentGroupIndex">暂无数据</div>
            </div>
        </div>
        <ui-empty v-else></ui-empty>
    </div>
</template>
<script>
// import sendTrack from "./sendTrack";
export default {
    name: "historyTrack",
    data() {
        return {
            trackList: [],
            currentGroupIndex: 0
        }
    },
    mounted() {
        this.getTrackList();
    },
    methods: {
        getTrackList() {
            let param = {
                currentPage: 1,
                pageSize: 100
            }
            // getTrackList(param).then(res => {
            //     this.trackList = res.data;
            // })
        },
        showCameraList(index) {
            if(this.currentGroupIndex === index) {
                this.currentGroupIndex = "";
                return;
            }
            this.currentGroupIndex = index;
        },
        viewTrack(group) {
            if(!(group.data && group.data.length)) {
                this.$Message.warning("该轨迹下暂无记录");
                return;
            }
            // sendTrack.openTrackWindow({isAppend: false, data: group.data, groupName: group.name});
            // window.videoTrackWindow && !window.videoTrackWindow.closed && this.$Message.success("已发送至轨迹页面");
        },
        deleteTrack(data) {
            let param = {id: data.uid || data.id};
            // deleteTrack(param).then(() => {
            //     this.$Message.success("删除成功");
            //     this.getTrackList();
            // })
        }
    }
}
</script>
<style lang="less">
.history-track {
    height: calc(~"100% - 135px");
    position: relative;
    .group-item {
        border-bottom: 1px dotted rgba(167,172,184,0.3);
        .group-header {
            display: flex;
            height: 40px;
            line-height: 40px;
            width: 100%;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            color: #333333;
            &:hover {
                .icon-shanchu1, .icon-shengchengguiji {
                    display: inline-block;
                    flex: 0 0 20px;
                    text-align: right;
                }
            }
            .i-f_a_ls {
                flex: 0 0 20px;
                color: #b1b5bf;
            }
        }
        .camera-item {
            display: flex;
            padding-left: 20px;
            color: #666666;
            line-height: 30px;
            height: 30px;
            cursor: pointer;
            &:hover {
                .icon-shanchu1 {
                    display: inline-block;
                }
            }
            .number {
                width: 20px;
                line-height: 26px;
                text-align: center;
                background: url("~@/assets/img/map/aggregation/track-car-marker.png") center center no-repeat;
                color: #fff;
                background-size: 100% 60%;
                margin-right: 5px;
            }
            .name {
                width: 125px;
            }
        }
        .icon-shanchu1 {
            color: #2c86f8;
            font-size: 12px;
            display: none;
        }
        .icon-shengchengguiji {
            display: none;
            color: #2c86f8;
        }
        .name, .time {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-right: 5px;
        }
        .no-point {
            height: 40px;
            line-height: 40px;
            padding-left: 20px;
            color: #b1b5bf;
        }
    }
}
</style>

