/*
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-07-18 16:13:00
 * @LastEditors: duansen
 * @LastEditTime: 2024-10-14 18:02:16
 * @Description:
 */
import request from "@/libs/request";
import {
  holographicArchives,
  streamService,
  modelSearch,
} from "./Microservice";
// 一机一档
// 查询列表
export function queryDeviceInfoPageList(data) {
  return request({
    url: holographicArchives + "/device/queryDeviceInfoPageList",
    method: "POST",
    data: data,
  });
}
// 基础信息
export function getDeviceBaseInfo(data) {
  return request({
    url: holographicArchives + "/device/getDeviceBaseInfo",
    method: "POST",
    data: data,
  });
}

// 抓拍记录
export function queryDeviceSnapRecordList(data) {
  return request({
    url: holographicArchives + "/deviceSnap/queryDeviceSnapRecordList",
    method: "POST",
    data: data,
  });
}
// 抓拍分析月统计
export function queryDeviceSnapAnalysisMonth(data) {
  return request({
    url: holographicArchives + "/deviceSnap/queryDeviceSnapAnalysisMonth",
    method: "POST",
    data: data,
  });
}
// 抓拍分析近7天
export function queryDeviceSnapAnalysisDay(data) {
  return request({
    url: holographicArchives + "/deviceSnap/queryDeviceSnapAnalysisDay",
    method: "POST",
    data: data,
  });
}

// 设备资料
export function getDeviceArchivesInfo(data) {
  return request({
    url: holographicArchives + "/device/getDeviceArchivesInfo",
    method: "POST",
    data: data,
  });
}

// 视频监控
// 历史录像分析
export function queryHistoryVideo(data) {
  return request({
    url: holographicArchives + "/deviceVideo/queryHistoryVideoStatistics",
    method: "POST",
    data: data,
  });
}
//视频质量分析
export function queryVideoQuality(data) {
  return request({
    url: holographicArchives + "/deviceVideo/queryVideoQualityStatistics",
    method: "POST",
    data: data,
  });
}
//视频在线情况分析
export function queryOnlineVideo(data) {
  return request({
    url: holographicArchives + "/deviceVideo/queryOnlineVideoStatistics",
    method: "POST",
    data: data,
  });
}
//视频播放
export function playVideo(data) {
  return request({
    url: holographicArchives + "/deviceVideo/playVideo",
    method: "POST",
    data: data,
  });
}

// 查询设备列表
export function queryDeviceList(data) {
  return request({
    url: service + `/device/queryDeviceList`,
    method: "POST",
    data: data,
  });
}

// 视频播放2
export function playing(data) {
  return request({
    url: streamService + "/stream/media/playing",
    method: "POST",
    data: data,
  });
}

// 关闭视频
export function stopPlaying(data) {
  return request({
    url: streamService + `/stream/media/stop/${data}`,
    method: "POST",
  });
}

// 电围设备基本信息
export function getElectricDeviceInfo(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getElectricDeviceInfo/${deviceId}`,
    method: "GET",
  });
}

// 电围设备数据采集记录
export function getBaseStationDataList(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getBaseStationDataList/${deviceId}`,
    method: "GET",
  });
}
//电围设备数据采集趋势
export function baseStationDataAnalysis(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/baseStationDataAnalysis",
    method: "POST",
    data: data,
  });
}
//电围设备数据采集统计
export function dataAnalysisDetails(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/baseStationDataAnalysis/details",
    method: "POST",
    data: data,
  });
}

//#region etc设备
// 基本信息
export function getETCDeviceInfo(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getEtcDeviceInfo/${deviceId}`,
    method: "GET",
  });
}

// 数据采集记录
export function getETCDeviceDataList(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getEtcDataList/${deviceId}`,
    method: "GET",
  });
}

// 数据采集统计
export function getETCDeviceAnalysisDetails(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/etcAnalysisDetails/details",
    method: "POST",
    data,
  });
}
//#endregion

//#region wifi设备
export function getWIFIDeviceInfo(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getWifiDeviceInfo/${deviceId}`,
    method: "GET",
  });
}

// 数据采集记录
export function getWIFIDeviceDataList(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getWifiDataList/${deviceId}`,
    method: "GET",
  });
}

// 数据采集统计
export function getWIFIDeviceAnalysisDetails(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/wifiAnalysisDetails/details",
    method: "POST",
    data,
  });
}
//#endregion

//#region rfid设备
export function getRFIDDeviceInfo(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getRfidDeviceInfo/${deviceId}`,
    method: "GET",
  });
}

// 数据采集记录
export function getRFIDDeviceDataList(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getRfidDataList/${deviceId}`,
    method: "GET",
  });
}

// 数据采集统计
export function getRFIDDeviceAnalysisDetails(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/rfidAnalysisDetails/details",
    method: "POST",
    data,
  });
}
//#endregion

//#region rfid设备
export function getGPSDeviceInfo(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getGpsDeviceInfo/${deviceId}`,
    method: "GET",
  });
}

// 数据采集记录
export function getGPSDeviceDataList(deviceId) {
  return request({
    url: modelSearch + `/CloudSearchEx/getGpsDataList/${deviceId}`,
    method: "GET",
  });
}

// 数据采集统计
export function getGPSDeviceAnalysisDetails(data) {
  return request({
    url: modelSearch + "/CloudSearchEx/gpsAnalysisDetails/details",
    method: "POST",
    data,
  });
}
//#endregion
