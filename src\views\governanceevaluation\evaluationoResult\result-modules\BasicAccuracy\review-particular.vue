<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :icon-list="iconList"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :table-loading="tableLoading"
      :form-item-data="formItemData"
      :form-data="formData"
      @startSearch="startSearch"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize"
    >
      <template #selectTabs>
        <div class="operation-bar mb-md">
          <div class="tabs">
            <ui-select-tabs
              ref="uiSelectTabs"
              class="tabs-ui"
              @selectInfo="selectInfo"
              :list="selectTabs"
            ></ui-select-tabs>
          </div>
          <Button
            slot="export"
            type="primary"
            class="button-export ml-md"
            @click="onClickIndex"
            :loading="exportLoading"
          >
            <i class="icon-font icon-daochu font-white mr-xs"></i>
            <span class="ml-xs">导出</span>
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #phyStatusText="{ row }">
        <span :class="row.phyStatusText === '可用' ? 'font-green' : 'font-red'">{{ row.phyStatusText }}</span>
      </template>
      <template slot="action" slot-scope="{ row }">
        <ui-btn-tip
          icon="icon-chakanyichangxiangqing"
          class="mr-sm"
          content="查看不合格原因"
          @click.native="checkReason(row)"
        ></ui-btn-tip>
      </template>
    </Particular>
    <export-data
      ref="exportModule"
      :export-loading="exportLoading"
      :is-abnormal-reason-export="true"
      @handleExport="handleExport"
    >
    </export-data>
    <nonconformance
      ref="nonconformance"
      title="检测不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonLoading="reasonLoading"
    ></nonconformance>
  </div>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import { iconStaticsList, tableColumn, formItemData } from './util/enum/ReviewParticular.js';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapGetters } from 'vuex';
export default {
  mixins: [downLoadTips, particularMixin, dealWatch],
  props: {},
  data() {
    return {
      iconList: iconStaticsList,
      tableColumns: tableColumn,
      formItemData: [],
      formData: {
        deviceId: '',
        deviceName: '',
        phyStatus: '',
        sbgnlx: '',
        qualified: '2',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      tableLoading: false,
      tableData: [],
      selectTabs: [],
      exportLoading: false,
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive',
    }),
  },
  created() {
    this.getSelectTabs();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  watch: {
    // 页面先隐藏设备功能类型下拉搜索
    // 处理 摄像机功能类型
    propertySearchSxjgnlx: {
      handler(arr) {
        if (!arr?.length) return;
        let options = [];
        arr.forEach((item) => {
          options.push({ value: item.dataKey, label: item.dataValue });
        });
        this.formItemData = formItemData.map((item) => {
          if (item.key === 'sbgnlx') {
            item.options = options;
          }
          return item;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    initAll() {
      this.getTableList();
      this.getTableDataTotal();
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableList();
      this.getTableDataTotal();
    },
    getTableList() {
      this.tableData = [];
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        if (!['VEHICLE_ACCURACY', 'FACE_ACCURACY'].includes(this.$route.query.indexType)) {
          this.totalCount = data.total;
        }
      });
    },
    getTableDataTotal() {
      if (!['VEHICLE_ACCURACY', 'FACE_ACCURACY'].includes(this.$route.query.indexType)) {
        return;
      }
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    handlePage(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    handlePageSize(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = data.map((item) => {
          return {
            name: item,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
      this.pageData.pageNum = 1;
      this.getTableList();
      this.getTableDataTotal();
    },
    // 导出
    onClickIndex() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    // 不合格原因
    checkReason(row) {
      this.getReason(row.deviceInfoId);
      this.$refs.nonconformance.init();
    },
    async getReason(deviceInfoId) {
      this.reasonLoading = true;
      let params = {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
        customParameters: { deviceInfoId },
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getSecondaryPopUpData,params, 'post',this.$route.query.cascadeId, {})
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas || [];
        // this.reasonPage.totalCount = datas.total
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  components: {
    Particular: require('../../ui-pages/particular.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    nonconformance: require('./components/nonconformance.vue').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .tabs {
    flex: 1;
  }
}
</style>
