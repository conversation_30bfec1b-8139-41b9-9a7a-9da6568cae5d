<template>
  <ui-modal v-model="visible" :title="title" width="50rem" @onCancel="handleReset">
    <div class="data-detection">
      <div class="data-detection-type">
        <div class="data-detection-label">请选择原始待治理数据</div>
      </div>
      <div class="data-detection-radiodiv">
        <RadioGroup v-model="formValidate.conditionType" @on-change="handleRadioChange">
          <Radio label="1">按条件选择</Radio>
          <Radio label="2">按设备选择</Radio>
        </RadioGroup>
      </div>
      <div v-show="formValidate.conditionType === '1'" class="condition-content">
        <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="140">
          <FormItem label="组织机构" prop="orgCode">
            <select-organization-tree
              :defaultCheckedKeys="defaultCheckedKeys"
              @check="check"
            ></select-organization-tree>
          </FormItem>
          <FormItem :label="global.filedEnum.sbdwlx" prop="sbdwlx">
            <Select v-model="formValidate.sbdwlx" multiple :placeholder="`请选择${global.filedEnum.sbdwlx}`">
              <Option
                v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
                :key="'sbdwlx' + bdindex"
                :value="sbdwlxItem.dataKey"
                >{{ sbdwlxItem.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem :label="global.filedEnum.sbgnlx" prop="sbgnlx">
            <Select v-model="formValidate.sbgnlx" :placeholder="`请选择${global.filedEnum.sbgnlx}`" multiple>
              <Option
                v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
                :key="'sbgnlx' + bdindex"
                :value="sbgnlxItem.dataKey"
                >{{ sbgnlxItem.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="设备目录" prop="categoryIds">
            <api-tree
              :list="treeData"
              :treeProps="treeProps"
              :has-cascader="true"
              :defaultProps="defaultProps"
              :isCustom="true"
              :checkIds="checkIds"
              :customAreaNames="customAreaNames"
              @selected="handleSelectTree"
              @handleSaveCategory="handleSaveCategory"
              @resetCategory="resetCategory"
              @clearTree="resetCategory"
            >
              <div slot="right-table">
                <Collapse v-model="collapseName" simple accordion>
                  <Panel name="1">
                    重点场所目录
                    <CheckboxGroup
                      slot="content"
                      v-model="importantAreaIds"
                      v-if="importantPlaceCategory.length"
                      @on-change="categoryChange"
                    >
                      <Checkbox
                        v-for="(importantItem, importantIndex) in importantPlaceCategory"
                        :key="importantIndex"
                        :label="importantItem.deviceTagId"
                        >{{ importantItem.deviceTagName }}</Checkbox
                      >
                    </CheckboxGroup>
                  </Panel>
                </Collapse>
                <el-tree
                  ref="categorytree"
                  :data="categoryList"
                  show-checkbox
                  node-key="id"
                  :check-strictly="true"
                  :props="{
                    children: 'childList',
                    label: 'areaTreeName',
                  }"
                  :default-checked-keys="checkIds"
                  @check="handleCheck"
                >
                </el-tree>
              </div>
            </api-tree>
          </FormItem>
          <FormItem v-if="false" label="已检测的重新检测" prop="isAgain" class="retest">
            <RadioGroup v-model="formValidate.isAgain">
              <Radio label="0">否</Radio>
              <Radio label="1">是</Radio>
            </RadioGroup>
          </FormItem>
        </Form>
      </div>
      <div v-show="formValidate.conditionType === '2'" class="device-content">
        <div class="camera fl" @click="selectCamera">
          <span class="font-blue camera-text" v-if="deviceIdList.length">已选择{{ deviceIdList.length }}条设备</span>
          <span v-else>
            <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
            <span class="font-blue camera-text">请选择摄像机</span>
          </span>
        </div>
      </div>
      <select-device ref="SelectDevice" :selectedList="deviceIdList" @getDeviceIdList="getDeviceIdList"></select-device>
    </div>
    <template slot="footer">
      <Button type="primary" @click="update">保&nbsp;存</Button>
    </template>
    <loading v-if="loading"></loading>
  </ui-modal>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import category from '@/config/api/catalogmanagement';
import user from '@/config/api/user';
import { mapGetters } from 'vuex';
export default {
  props: {
    warehousData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      title: '数据范围选择',
      formValidate: {
        conditionType: '1',
        orgCode: '',
        sbdwlx: [],
        sbgnlx: [],
        isAgain: '0',
        categoryIds: [],
      },
      ruleValidate: {
        orgCode: [
          {
            required: false,
            message: '请选择组织机构',
            trigger: 'change',
            type: 'string',
          },
        ],
        sbdwlx: [
          {
            required: false,
            message: '请选择点位类型',
            trigger: 'change',
            type: 'array',
          },
        ],
        sbgnlx: [
          {
            required: false,
            message: '请选择摄像机功能类型',
            trigger: 'change',
            type: 'array',
          },
        ],
        isAgain: [{ required: false, message: '请选择', trigger: 'change' }],
        categoryIds: [
          {
            required: false,
            message: '请选择设备目录',
            trigger: 'change',
            type: 'array',
          },
        ],
      },
      dataCount: '',
      deviceIdList: [],
      selectOrgTree: {
        orgCode: null,
      },
      dictData: {},
      defaultCheckedKeys: [],
      treeProps: {
        treeValue: '',
        id: '',
        nodeKey: '',
      },
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      categoryList: [],
      checkNodes: [],
      checkIds: [],
      importantAreaIds: [], // 重点场所目录ID
      customAreaNames: [], // 已选目录名称
      importantPlaceCategory: [],
      importantObj: {},
      importantName: [],
      collapseName: '1',
      loading: false,
    };
  },
  methods: {
    async init() {
      await this.getDictData();
      await this.getDetail();
      this.visible = true;
    },
    async getDetail() {
      try {
        const params = {
          topicId: this.warehousData.id,
        };
        let res = await this.$http.get(governancetheme.queryByTopicId, {
          params,
        });
        const datas = res.data.data;
        this.formValidate.conditionType = datas.conditionType;
        this.selectOrgTree.orgCode = datas.orgCode;
        this.defaultCheckedKeys = datas.orgCode ? datas.orgCode.split(',') : [];
        this.formValidate.orgCode = datas.orgCode;
        this.formValidate.sbdwlx = datas.sbdwlx ? datas.sbdwlx.split(',') : [];
        this.formValidate.sbgnlx = datas.sbgnlx ? datas.sbgnlx.split(',') : [];
        this.formValidate.isAgain = datas.isAgain;
        this.dataCount = datas.dataCount;
        this.importantAreaIds = datas.importantAreaIds ? datas.importantAreaIds.split(',').map(Number) : []; // 重点场所目录ID
        this.checkIds = datas.customAreaIds ? datas.customAreaIds.split(',').map(Number) : []; // 自定义目录ID;
        this.customAreaNames = datas.customAreaNames ? datas.customAreaNames.split(',') : []; // 目录名字
        this.deviceIdList = datas.deviceIdList ? datas.deviceIdList : [];
        this.formValidate.categoryIds = this.checkIds.concat(this.importantAreaIds);
        this.checkNodes = this.checkIds.map((item, checkedIndex) => {
          let obj = {};
          let importantLen = this.importantAreaIds.length;
          obj.areaTreeName = this.customAreaNames[importantLen + checkedIndex];
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectCamera() {
      this.$refs.SelectDevice.init(this.dictData, this.warehousData.id);
    },
    handleReset() {
      this.$refs['formValidate'].resetFields();
      this.visible = false;
      this.$emit('render');
    },
    getDeviceIdList(data) {
      this.deviceIdList = data;
    },
    check(list) {
      this.formValidate.orgCode = list.map((item) => {
        return item.orgCode;
      });
      this.formValidate.orgCode = this.formValidate.orgCode.join(',');
      this.$refs.formValidate.validateField('orgCode');
    },
    async handleSelectTree(data) {
      this.collapseName = '';
      await this.initImportantPlace(data.orgCode);
      await this.getAllCustomAreaTree(data.orgCode);
    },
    async getAllCustomAreaTree(orgCode) {
      try {
        let { data } = await this.$http.post(category.getAllCustomAreaTree, {
          orgCode: orgCode,
        });
        data.data.map((item) => {
          item.disabled = true;
          item.areaTreeName = item.customAreaName;
        });
        this.categoryList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initImportantPlace(orgCode) {
      try {
        let { data } = await this.$http.get(category.getAllImportantArea, {
          params: { orgCode: orgCode },
        });
        this.importantPlaceCategory = data.data.map((item) => {
          this.importantObj[item.deviceTagId] = item.deviceTagName;
          return item;
        });
      } catch (err) {
        console.log(err);
      }
    },

    handleCheck(data) {
      let index = this.checkIds.indexOf(data.id);
      if (index === -1) {
        this.checkIds.push(data.id);
        this.checkNodes.push(data);
      } else {
        this.checkNodes.splice(index, 1);
        this.checkIds.splice(index, 1);
      }
    },
    handleSaveCategory() {
      let importantNames = [];
      let customNames = [];
      importantNames = this.importantAreaIds.map((item) => {
        return this.importantObj[item.toString()];
      });
      customNames = this.checkNodes.map((item) => {
        return item.areaTreeName;
      });
      this.customAreaNames = importantNames.concat(customNames);
      this.formValidate.categoryIds = this.checkIds.concat(this.importantAreaIds);
      this.$refs.formValidate.validateField('categoryIds');
    },
    resetCategory() {
      if (!this.checkIds.length && !this.importantAreaIds.length) {
        return false;
      }
      this.checkIds = [];
      this.checkNodes = [];
      this.importantAreaIds = [];
      this.formValidate.categoryIds = [];
      this.customAreaNames = [];
      this.$refs.formValidate.validateField('categoryIds');
    },
    async update() {
      const valid = await this.$refs['formValidate'].validate((valid) => valid);
      if (valid) {
        try {
          this.loading = true;
          let params = {};
          params.conditionType = this.formValidate.conditionType;
          params.topicId = this.warehousData.id;
          if (this.formValidate.conditionType === '1') {
            params.orgCode = this.formValidate.orgCode;
            params.sbdwlx = this.formValidate.sbdwlx.join(',');
            params.sbgnlx = this.formValidate.sbgnlx.join(',');
            params.isAgain = this.formValidate.isAgain;
            params.importantAreaIds = this.importantAreaIds.length ? this.importantAreaIds.join(',') : '';
            params.customAreaIds = this.checkIds.length ? this.checkIds.join(',') : '';
            params.customAreaNames = this.customAreaNames.length ? this.customAreaNames.join(',') : '';
          } else {
            params.deviceIdList = this.deviceIdList;
          }
          // let params = {
          //   orgCode: this.formValidate.orgCode,
          //   sbdwlx: this.formValidate.sbdwlx.join(","),
          //   sbgnlx: this.formValidate.sbgnlx.join(","),
          //   isAgain: this.formValidate.isAgain,
          //   deviceIdList: this.deviceIdList,
          //   topicId: this.warehousData.id,

          // };
          await this.$http.post(governancetheme.update, params);
          this.$Message.success('选择治理数据成功!');
          this.handleReset();
        } catch (error) {
          console.log(error);
        } finally {
          this.loading = false;
        }
      } else {
        this.$Message.error('请将信息填写完整!');
      }
    },
    categoryChange(data) {
      this.formValidate.categoryIds = data;
    },
    handleRadioChange(val) {
      this.formValidate.conditionType = val;
      if (val === '1') {
        this.formValidate.categoryIds = [];
      } else {
        this.formValidate.orgCode = '';
        this.formValidate.sbdwlx = [];
        this.formValidate.sbgnlx = [];
        this.formValidate.isAgain = '0';
      }
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {},
  components: {
    SelectDevice: require('./select-device.vue').default,
    SelectOrganizationTree: require('@/api-components/select-organization-tree.vue').default,
    apiTree: require('@/components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.data-detection {
  min-height: 350px;
  font-size: 14px;
  &-type {
    width: 100%;
    display: flex;
    align-items: center;
  }
  &-label {
    margin-top: 28px;
    font-size: 14px;
    color: var(--color-primary);
  }
  &-radiodiv {
    margin: 18px 0 14px;
  }
}
.device-content {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 38px;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
@{_deep} .ivu-radio-wrapper {
  margin-right: 42px;
}
@{_deep} .api-organization-tree {
  width: 100% !important;
}
@{_deep} .ivu-dropdown {
  width: 100% !important;
}
@{_deep} .select-width {
  width: 100% !important;
}
@{_deep} .ivu-modal-body {
  padding: 26px 50px 36px;
}
@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-6px, 3px) scale(0.45) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    height: 0.135417rem;
    line-height: 0.135417rem;
    padding: 0 6px;
    font-size: 12px !important;
    color: #ffffff;
    border-bottom: none !important;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
    .ivu-icon-ios-arrow-forward {
      font-size: 12px !important;
      color: #239df9;
      transform: rotate(-90deg) scale(0.4);
      &:before {
        font-family: 'icon-font';
        content: '\e7a3';
      }
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      padding-bottom: 0 !important;
    }
  }
}
@{_deep} .ivu-checkbox-group {
  padding: 0 24px;
  &-item {
    width: 100%;
    height: 0.135417rem;
    line-height: 0.135417rem;
  }
}
@{_deep} .ivu-checkbox-inner {
  width: 0.072917rem;
  height: 0.072917rem;
}
@{_deep} .ivu-form-item-content {
  line-height: normal;
}
@{_deep} .el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep} .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.retest {
  @{_deep} .ivu-form-item-content {
    line-height: 0.166667rem !important;
  }
}
@{_deep} .ivu-modal .title {
  z-index: 15;
}
</style>
