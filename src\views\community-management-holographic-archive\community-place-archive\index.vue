<template>
  <PlaceArchive
    :isNewGoInfo="true"
    :goInfo="goArchivesInfo"
    :searchPlaceApi="getPlacePageList"
  ></PlaceArchive>
</template>

<script>
import PlaceArchive from "@/views/holographic-archives/one-place-one-archives/place-archives/index.vue";
import { getPlacePageList } from "@/api/monographic/community-management.js";
export default {
  name: "communityPeopleArchive",
  components: { PlaceArchive },
  data() {
    return {
      getPlacePageList,
    };
  },
  methods: {
    /**
     * @description: 跳转到档案详情
     * @param {string} 场所档案id
     */
    goArchivesInfo(archiveNo) {
      const { href } = this.$router.resolve({
        path: "/community-place-archive/place-dashboard",
        query: { archiveNo, source: "place" },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped></style>
