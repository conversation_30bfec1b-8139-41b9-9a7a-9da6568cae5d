<template>
  <div class="add-modal" v-if="isShow">
    <div class="title-box" v-if="!isApproval">
      <div class="goback" @click="isShow = false">
        <i class="iconfont icon-return"></i>
      </div>
      返回<span> > {{ title }}</span>
    </div>
    <div class="content">
      <div
        class="video-left mr-10"
        :class="{ 'sac-content-his': subTaskType === 'his' }"
      >
        <Form
          ref="form"
          :model="taskInfo"
          :disabled="isApproval"
          class="form"
          :label-width="80"
        >
          <FormItem label="任务名称:" prop="name" class="search-input">
            <Input
              placeholder="请输入"
              clearable
              v-model="taskInfo.name"
              maxlength="50"
            />
          </FormItem>
          <FormItem
            label="有效时间:"
            class="search-input"
            v-if="subTaskType === 'real'"
          >
            <div class="datepicker-wrap">
              <hl-daterange
                class="mb-20"
                v-model="taskInfo.times[0].startTime"
                key="1"
              ></hl-daterange>
              <hl-daterange
                v-model="taskInfo.times[0].endTime"
                key="2"
              ></hl-daterange>
            </div>
          </FormItem>
          <FormItem
            label="处理时段:"
            class="search-input"
            v-if="subTaskType === 'real'"
          >
            <div class="timepicker-wrap">
              <el-checkbox
                class="handle-range"
                v-model="taskInfo.isTaskSection"
              />
              <el-time-select
                class="ml-5"
                size="small"
                :disabled="!taskInfo.isTaskSection"
                v-model="taskInfo.sectionStart"
                align="left"
                placeholder="开始时段"
                :picker-options="{
                  start: '00:00',
                  step: '01:00',
                  end: '23:00',
                  maxTime: taskInfo.sectionEnd,
                }"
              />
              <div class="line"></div>
              <el-time-select
                size="small"
                :disabled="!taskInfo.isTaskSection"
                v-model="taskInfo.sectionEnd"
                align="left"
                placeholder="结束时段"
                :picker-options="{
                  start: '00:00',
                  step: '01:00',
                  end: '24:00',
                  minTime: taskInfo.sectionStart,
                }"
              />
            </div>
          </FormItem>
          <FormItem
            label="执行时间:"
            class="search-input"
            v-if="subTaskType === 'his'"
          >
            <hl-daterange
              v-model="taskInfo.times[0].startTime"
              key="1"
            ></hl-daterange>
          </FormItem>
          <FormItem label="申请理由:" class="search-input">
            <Input
              v-model="taskInfo.requestReason"
              type="textarea"
              placeholder="请输入"
            ></Input>
          </FormItem>
          <FormItem label="申请书:" class="search-input">
            <div class="uploadImg">
              <UploadImg
                choosed
                ref="uploadImg"
                :deleted="true"
                :defaultList="defaultList"
                :multipleNum="1"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
          <div v-if="subTaskType === 'his'" class="sac-config-item-split">
            ------------分割线---------------
          </div>
          <FormItem
            label="录像时段:"
            class="search-input"
            v-if="subTaskType === 'his'"
          >
            <div class="datepicker-wrap">
              <hl-daterange
                class="mb-20"
                v-model="histime[0]"
                key="1"
                @affirm="pickerClose"
              ></hl-daterange>
              <hl-daterange
                v-model="histime[1]"
                key="2"
                @affirm="pickerClose"
              ></hl-daterange>
            </div>
          </FormItem>
          <FormItem label="选择设备:" class="search-input" v-if="!isApproval">
            <div class="select-tag-button" @click="selectDevice()">
              选择设备/已选（{{ taskInfo.taskList.length }}）
            </div>
          </FormItem>
        </Form>
        <div
          class="sac-config-item-split"
          v-show="taskInfo.taskList.length > 0"
        >
          ----分割线----
        </div>
        <div
          class="sac-config-item sac-config-device-type"
          v-show="taskInfo.taskList.length > 0"
        >
          <label class="sac-config-title">标注信息类型 : </label>
          <span
            class="sac-config-device-type-name fr"
            style="margin-right: 30px"
          >
            <i
              v-for="item in types"
              :key="item.name"
              :class="{ nonmotorstyle: item.oldType == 'nonmotor' }"
              >{{ item.name }}</i
            >
          </span>
        </div>
        <div
          class="sac-config-item sac-config-device-total"
          style="margin: -5px 0 -5px 0"
          v-show="taskInfo.taskList.length > 0"
        >
          <span
            class="sac-config-device-type-name-check fr"
            style="margin-right: 30px"
          >
            <el-checkbox
              v-for="item in types"
              :key="item.name"
              class="sac-config-check-all"
              :class="{ nonmotorstyle: item.oldType == 'nonmotor' }"
              style="margin: 0"
              v-model="IsCheckAll[item.checkType]"
              @change="checkAllFun(item.oldType, IsCheckAll[item.checkType])"
            />
          </span>
        </div>
        <!--实时结构化设备列表-->
        <div
          v-if="subTaskType === 'real'"
          v-show="taskInfo.taskList.length > 0"
          class="sac-config-item sac-config-device-list"
        >
          <div v-scroll style="height: 100%">
            <div
              v-for="(item, index) in taskInfo.taskList"
              :key="index"
              :style="{ width: 'auto' }"
            >
              <span
                :class="{ 'sac-active-playing': curCIndex == index }"
                class="sac-config-device-info dib"
              >
                <span
                  class="sac-camera-name dib"
                  :title="item.deviceName"
                  @dblclick="playVideo(item, '', index)"
                >
                  <i
                    :class="getDeviceType(item)"
                    class="iconfont nui-iconfont mr-5"
                  ></i
                  ><span>{{ item.deviceName }}</span>
                </span>
                <span class="sac-config-device-type-name-check sac-checks fr">
                  <i
                    class="sac-config-check iconfont icon-qiche1 cl"
                    :class="{ checked: item.structureOldType['vehicle'] }"
                    @click="
                      checkOneFun(
                        'vehicle',
                        item.structureOldType['vehicle'],
                        index
                      )
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-diandongche fjdc nonmotorstyle"
                    :class="{ checked: item.structureOldType['nonmotor'] }"
                    @click="
                      checkOneFun(
                        'nonmotor',
                        item.structureOldType['nonmotor'],
                        index
                      )
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-renlian1 rl"
                    :class="{ checked: item.structureOldType['face'] }"
                    @click="
                      checkOneFun('face', item.structureOldType['face'], index)
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-renti1 rt"
                    :class="{ checked: item.structureOldType['human'] }"
                    @click="
                      checkOneFun(
                        'human',
                        item.structureOldType['human'],
                        index
                      )
                    "
                  />
                </span>
                <i
                  class="iconfont icon-shanchu1 sac-icon-delete"
                  @click="deleteDevice(index)"
                ></i>
              </span>
            </div>
          </div>
        </div>
        <!--历史结构化设备列表-->
        <div
          v-if="subTaskType === 'his'"
          v-show="taskInfo.taskList.length > 0"
          class="sac-config-item sac-config-device-list his"
        >
          <div v-scroll style="height: 100%">
            <div
              v-for="(item, index) in taskInfo.taskList"
              :key="index"
              :style="{ width: 'auto' }"
            >
              <span
                :class="{
                  nohis: !item.hasHis,
                  'sac-active-playing': curCIndex == index,
                }"
                class="sac-config-device-info dib"
              >
                <span
                  class="sac-camera-name dib"
                  :title="item.deviceName"
                  @dblclick="playVideo(item, true, index)"
                >
                  <i
                    :class="getDeviceType(item)"
                    class="iconfont nui-iconfont mr-5"
                  ></i
                  ><span>{{ item.deviceName }}</span>
                </span>
                <span class="sac-config-device-type-name-check sac-checks fr">
                  <i
                    class="sac-config-check iconfont icon-qiche1 cl"
                    :class="{
                      disabled: !item.hasHis,
                      checked: item.structureOldType['vehicle'],
                    }"
                    @click="
                      checkOneFun(
                        'vehicle',
                        item.structureOldType['vehicle'],
                        index,
                        !item.hasHis
                      )
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-diandongche fjdc nonmotorstyle"
                    :class="{
                      disabled: !item.hasHis,
                      checked: item.structureOldType['nonmotor'],
                    }"
                    @click="
                      checkOneFun(
                        'nonmotor',
                        item.structureOldType['nonmotor'],
                        index,
                        !item.hasHis
                      )
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-renlian1 rl"
                    :class="{
                      disabled: !item.hasHis,
                      checked: item.structureOldType['face'],
                    }"
                    @click="
                      checkOneFun(
                        'face',
                        item.structureOldType['face'],
                        index,
                        !item.hasHis
                      )
                    "
                  />
                  <i
                    class="sac-config-check iconfont icon-renti1 rt"
                    :class="{
                      disabled: !item.hasHis,
                      checked: item.structureOldType['human'],
                    }"
                    @click="
                      checkOneFun(
                        'human',
                        item.structureOldType['human'],
                        index,
                        !item.hasHis
                      )
                    "
                  />
                </span>
                <i
                  class="iconfont icon-shanchu1 sac-icon-delete"
                  @click="deleteDevice(index)"
                ></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <!-- 实时和历史结构化 都需要工具栏 -->
        <h5-player
          ref="H5Player"
          :options="{ layout: 1 * 1 }"
          :isShowToolbar="subTaskType === 'his' || subTaskType === 'real'"
        ></h5-player>
      </div>
    </div>
    <div class="viewanalysis-footer" v-if="!isApproval">
      <Button class="mr-20" @click="closePanel">取消</Button>
      <Button type="primary" @click="saveTask" :disabled="isSaveMuiltForbit"
        >保存</Button
      >
    </div>

    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
  </div>
</template>

<script>
import UploadImg from "@/components/ui-upload-img-static-library";
import { getTaskDetailById } from "@/api/viewAnalysis";
import hlDaterange from "@/components/hl-daterange/index.vue";
import mixinTaskSave from "../mixins/saveTask.js";
import { queryCameraDeviceList } from "@/api/player.js";
import { getDeviceByIds, getDeviceById } from "@/api/frame.js";
export default {
  name: "addModal",
  mixins: [mixinTaskSave],
  components: {
    hlDaterange,
    UploadImg,
  },
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    subTaskId: {
      type: String,
      require: "",
    },
    subTaskType: {
      type: String,
      require: "",
    },
    subDeviceId: {
      type: String,
      require: "",
    },
    // 是否是审核详情
    isApproval: {
      type: Boolean,
      require: false,
    },
  },
  watch: {
    isShow(val) {
      this.$emit("input", val);
      // 重置录像时段时间
      this.histime = [
        this.$dayjs(new Date().getTime() - 2 * 60 * 60 * 1000).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      ];
    },
    value: {
      handler(val) {
        this.isShow = val;
        if (val) {
          this.init();
        } else {
          this.reset();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      isShow: false,
      title: "结构化任务",
      taskInfo: {
        options: "0",
        times: [
          {
            startTime: this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
            endTime: "",
          },
        ],
        taskList: [],
        isTaskSection: false, // 是否启用处理时段
        sectionStart: null,
        sectionEnd: null,
        speed: 1,
        requestReason: null,
        requestAttachmentUrl: null,
      },
      histime: [
        this.$dayjs(new Date().getTime() - 2 * 60 * 60 * 1000).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
      ], //录像时段 历史结构化使用
      types: [
        {
          name: "车辆",
          oldType: "vehicle",
          checkType: "isvehicleAll",
          innerClass: "icon-qiche1",
        },
        {
          name: "非机动车",
          oldType: "nonmotor",
          checkType: "isnonmotorAll",
          innerClass: "icon-diandongche",
        },
        {
          name: "人脸",
          oldType: "face",
          checkType: "isfaceAll",
          innerClass: "icon-renlian1",
        },
        {
          name: "人体",
          oldType: "human",
          checkType: "ishumanAll",
          innerClass: "icon-renti1",
        },
      ],
      IsCheckAll: {
        //全选控制
        isvehicleAll: false,
        isfaceAll: false,
        ishumanAll: false,
        isnonmotorAll: false,
      },
      curCIndex: -1, //当前设备索引
      isSaveMuiltForbit: false,
      defaultList: [],
    };
  },
  methods: {
    init() {
      this.initData(this.subTaskId);
      if (!this.subTaskId) {
        //新建 给个默认任务名
        this.taskInfo.name = new Date().format("yyyyMMddhhmmss");
      }
    },
    //初始化数据
    initData: function (taskId) {
      this.title =
        (taskId ? "编辑" : "新增") +
        (this.subTaskType === "real" ? "实时" : "历史") +
        "结构化任务";
      this.defaultList = [];
      if (taskId) {
        var carCheckedLen = 0,
          nonmotorCheckedLen = 0,
          humanCheckedLen = 0,
          faceCheckedLen = 0,
          taskLen = 0;
        this.getTaskDetail(
          taskId,
          function (task) {
            this.treeSelectValue = [];
            //var task = taskInfo.structure;
            //this.taskInfo.options = task.options + "";
            this.defaultList = [task.requestAttachmentUrl];
            this.taskInfo.requestReason = task.requestReason;
            this.taskInfo.requestAttachmentUrl = task.requestAttachmentUrl;
            this.taskInfo.name = task.name;
            this.taskInfo.times[0].startTime = task.tasks[0].taskStartTime
              ? this.$dayjs(task.tasks[0].taskStartTime).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              : task.tasks[0].taskStartTime;
            this.taskInfo.times[0].endTime = task.tasks[0].taskEndTime
              ? this.$dayjs(task.tasks[0].taskEndTime).format(
                  "YYYY-MM-DD HH:mm:ss"
                )
              : task.tasks[0].taskEndTime;

            if (
              task.tasks[0].workStartTime === 0 ||
              (task.tasks[0].workStartTime &&
                typeof task.tasks[0].workStartTime === "number")
            ) {
              var _sSTime =
                task.tasks[0].workStartTime < 10
                  ? "0" + task.tasks[0].workStartTime
                  : "" + task.tasks[0].workStartTime;
              this.taskInfo.sectionStart = _sSTime + ":00";
            }
            if (
              task.tasks[0].workEndTime &&
              typeof task.tasks[0].workEndTime === "number"
            ) {
              var _sETime =
                task.tasks[0].workEndTime < 10
                  ? "0" + task.tasks[0].workEndTime
                  : "" + task.tasks[0].workEndTime;
              this.taskInfo.sectionEnd = _sETime + ":00";
              this.taskInfo.isTaskSection = true;
            }
            if (task.type == 2) {
              this.histime[0] = this.$dayjs(
                task.tasks[0].videoStartTime
              ).format("YYYY-MM-DD HH:mm:ss");
              this.histime[1] = this.$dayjs(task.tasks[0].videoEndTime).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
            this.taskInfo.createTime = task.createTime;
            this.taskInfo.userName = task.userName;
            this.taskInfo.taskList = task.tasks.map(
              function (o, index) {
                o._index = index;
                o.deviceName = o.name;
                o.deviceGbId = o.deviceGbId;
                if (o.structureOldType.vehicle) {
                  carCheckedLen++;
                }
                if (o.structureOldType.nonmotor) {
                  nonmotorCheckedLen++;
                }
                if (o.structureOldType.human) {
                  humanCheckedLen++;
                }
                if (o.structureOldType.face) {
                  faceCheckedLen++;
                }
                delete o.name;
                return o;
              }.bind(this)
            );
            // 编辑时判断是否触发全选
            taskLen = this.taskInfo.taskList.length;
            if (taskLen === carCheckedLen) {
              this.IsCheckAll.isvehicleAll = true;
            }
            if (taskLen === nonmotorCheckedLen) {
              this.IsCheckAll.isnonmotorAll = true;
            }
            if (taskLen === faceCheckedLen) {
              this.IsCheckAll.isfaceAll = true;
            }
            if (taskLen === humanCheckedLen) {
              this.IsCheckAll.ishumanAll = true;
            }
            if (!this._isRealStr()) {
              //如果是历史结构化 查一下录像接口
              this.getDeviceHis();
            }
            this.singleDevicePlay();
          }.bind(this)
        );
      }
    },
    //设备列表修改设备时，需要播放选中设备
    singleDevicePlay: function () {
      if (!this.subDeviceId && this.taskInfo.taskList[0]) {
        this.playVideo(this.taskInfo.taskList[0], !this._isRealStr(), 0);
        return;
      }
      let index = this.taskInfo.taskList.findIndex(
        (v) => v.deviceGbId == this.subDeviceId
      );
      this.playVideo(this.taskInfo.taskList[index], !this._isRealStr(), index);
    },
    //根据任务ID获取任务详情
    getTaskDetail: function (_id, callback) {
      getTaskDetailById(_id).then((res) => {
        if (res.data) {
          callback && callback(res.data);
        }
      });
    },
    reset() {
      this.taskInfo = {
        options: "0",
        times: [
          {
            startTime: this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss"),
            endTime: "",
          },
        ],
        taskList: [],
        isTaskSection: false, // 是否启用处理时段
        sectionStart: null,
        sectionEnd: null,
      };
      this.IsCheckAll = {
        //全选控制
        isvehicleAll: false,
        isfaceAll: false,
        ishumanAll: false,
        isnonmotorAll: false,
      };
      this.curCIndex = -1;
    },
    //当前是否为实时结构化
    _isRealStr: function () {
      return this.subTaskType === "real";
    },
    /**
     * 获取设备对应的icon
     */
    getDeviceType(item) {
      return item.deviceChildType == "1" || item.deviceChildType == "2"
        ? "icon-qiuji"
        : "icon-shebeizichan";
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.taskInfo.taskList, "");
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.closeVideo();
      this.IsCheckAll = {
        isvehicleAll: false,
        ishonmotorAll: false,
        isfaceAll: false,
        ishumanAll: false,
      };
      this.taskInfo.taskList = this._filterWithOldDevice(list).filter(
        (item) => item != undefined
      );
      if (!this._isRealStr()) {
        this.getDeviceHis();
      }
    },
    pickerClose() {
      if (this.taskInfo.taskList.length < 1) {
        return;
      } //无设备不做响应
      this.getDeviceHis();
    },
    //查询摄像机是否有录像和录像时间区域
    getDeviceHis: function () {
      if (this._isRealStr()) {
        return;
      } //[仅历史结构化下触发]
      this.waitForList(this.taskInfo.taskList, 0, 5);
    },
    async waitForList(arr, index, step) {
      if (!this.histime[0] || !this.histime[1]) {
        this.$Message.error("录像开始时间和结束时间不能为空");
        return;
      }
      arr.forEach((v) => (v.hasHis = false));
      for (let i = index; i < arr.length; i += step) {
        const batch = arr.slice(i, i + step);
        const promises = batch.map(({ deviceGbId }) =>
          Toolkits.searchVodList(
            this.$refs.H5Player.videoObj,
            deviceGbId,
            this.histime
          )
        );
        const results = await Promise.all(promises);
        results.forEach((item, index) => {
          let obj = arr[i + index];
          obj.hasHis = !!item.length;
          this.$set(arr, i + index, obj);
        });
      }
      console.log(arr);
    },
    //设备选择: 如果是存在的设备 保持数据不动,添加的设备 即添加,删除的设备,则删除
    _filterWithOldDevice: function (deviceObj) {
      var cacheData = [...this.taskInfo.taskList],
        cL = cacheData.length;
      var res = deviceObj.map(function (item, index) {
        for (var i = 0; i < cL; i++) {
          if (cacheData[i]["deviceGbId"] === item.deviceGbId) {
            item = cacheData[i];
            return item;
          }
        }
        return {
          ...item,
          _index: index,
          structureOldType: {
            face: false,
            human: false,
            vehicle: false,
            nonmotor: false,
          },
          hasHis: false,
        };
      });
      return res;
    },
    //单选逻辑 参数同上( 首字母要大写) ==>如果触发未勾选 把全选按钮状态改变 不检测全选触发全选状态一功能 这个逻辑没毛病
    checkOneFun: function (type, val, index, isDisabled = false) {
      if (isDisabled) return;
      val = !val;
      var type_ex = "is" + type + "All",
        _type_ex = type.toLowerCase();
      this.$set(this.taskInfo.taskList[index].structureOldType, _type_ex, val);
      if (!val && this.IsCheckAll[type_ex]) {
        this.$set(this.IsCheckAll, type_ex, false);
      } else {
        var isChAll = this._isAllChecked(_type_ex);
        this.$set(this.IsCheckAll, type_ex, isChAll);
      }
      this.$forceUpdate();
    },
    //全选逻辑 type = vehicle/face/human 参数格式要严格按照要求传入!!!
    checkAllFun: function (type, val, forceVal) {
      if (forceVal !== undefined) {
        //默认勾选模式
        val = forceVal;
      }
      var type_ex = "" + type;
      this.taskInfo.taskList.map((item, index) => {
        //是否可选
        if (item.hasHis) {
          this.$set(
            this.taskInfo.taskList[index].structureOldType,
            type_ex,
            val
          );
        } else if (this._isRealStr()) {
          this.$set(
            this.taskInfo.taskList[index].structureOldType,
            type_ex,
            val
          );
        }
      });
      this.$forceUpdate();
    },
    //循环遍历是否全部勾选 => 触发全选复选框状态改变
    _isAllChecked: function (type) {
      if (this.taskInfo.taskList.length == 0) {
        return false;
      }
      for (let index = 0; index < this.taskInfo.taskList.length; index++) {
        const t = this.taskInfo.taskList[index];
        if (!t.structureOldType[type]) {
          return false;
        }
      }
      return true;
    },
    //删除一个设备 index:序号
    deleteDevice: function (index) {
      // var delItem = this.taskInfo.taskList.splice(index, 1)[0];
      this.taskInfo.taskList = this.taskInfo.taskList
        .filter((_item) => _item._index != index)
        .map((item, index) => {
          return {
            ...item,
            _index: index,
          };
        });
      //当已选设备为空时，清空信息类型的已选状态
      if (this.taskInfo.taskList.length === 0) {
        this.IsCheckAll = {
          isvehicleAll: false,
          isfaceAll: false,
          ishumanAll: false,
          isnonmotorAll: false,
        };
      }
      this.closeVideo();
    },
    async playVideo(cameraInfo, isHis, index, callback) {
      //存储当前播放视频的index
      this.curCIndex = index;
      if (isHis) {
        this.playHis(cameraInfo);
        return;
      }
      let { deviceName, deviceGbId, deviceChildType } = { ...cameraInfo };

      // 播放设备工具栏 需要设备的详细信息包括 经纬度/设备类型等
      let deviceInfo = {};
      const data = await queryCameraDeviceList({
        deviceId: cameraInfo.deviceId || cameraInfo.deviceGbId,
      });
      if (data.code === 200) {
        deviceInfo = data.data[0];
      }
      let param = {
        ...deviceInfo,
        deviceName,
        deviceGbId,
        devicetype: liveType,
      };
      this.$refs.H5Player.playStream(param, "live");
      callback && callback();
    },
    async playHis(item) {
      if (!item.hasHis) return;
      let params = {
        deviceId: item.deviceGbId,
        deviceName: item.deviceName,
        geoPoint: {
          lon: item.Lon,
          lat: item.Lat,
        },
        devicetype: vodType,
        playType: "vod",
        begintime: this.histime[0],
        endtime: this.histime[1],
      };
      // 视频入参无设备类型 地图定位会出现错误
      const data = await queryCameraDeviceList({
        deviceId: params.deviceId,
      });
      let deviceInfo = {};
      if (data.code === 200) {
        deviceInfo = data.data[0];
      }
      let param = {
        ...deviceInfo,
        ...params,
        geoPoint: {
          lon: deviceInfo.longitude,
          lat: deviceInfo.latitude,
        },
      };
      this.$refs.H5Player.playStream(param, params.playType);
    },
    closeVideo() {
      if (this.curCIndex != -1 && this.$refs.H5Player) {
        this.curCIndex = -1;
        this.$refs.H5Player.closeAll();
      }
    },
    closePanel() {
      this.closeVideo();
      this.isShow = false;
      this.isSaveMuiltForbit = false; //放开禁用的按钮
    },
    //检测设备勾选结构化类型 不能有设备没有勾选任何任务类型 否则不满足要求
    _testDeviceTypes: function () {
      var cs = this.taskInfo.taskList,
        l = cs.length;
      if (l < 1) return false;
      var isHis = !this._isRealStr();
      for (var i = 0; i < l; i++) {
        if (isHis && !cs[i].hasHis) {
          //如果是没有录像的历史结构化任务设备就不做处理
          continue;
        }
        if (
          !(
            cs[i].structureOldType.face ||
            cs[i].structureOldType.human ||
            cs[i].structureOldType.vehicle ||
            cs[i].structureOldType.nonmotor
          )
        ) {
          return false;
        }
      }
      return true;
    },
    // 选择申请书
    chooseHandle(item) {
      this.taskInfo.requestAttachmentUrl = item;
    },
  },
};
</script>

<style lang="less" scoped>
.add-modal {
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .title-box {
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    .goback {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
  }
  .content {
    flex: 1;
    padding: 10px;
    display: flex;
    overflow: hidden;
    .color-blue {
      color: #2c86f8;
    }
    .video-left {
      width: 416px;
      height: 100%;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      .timepicker-wrap {
        display: flex;
        align-items: center;
        .line {
          height: 3px;
          width: 20px;
          background: #d2d8db;
          margin: 0 5px;
        }
      }
      .sac-config-item {
        width: 100%;
        padding: 0 0 0 10px;
        margin: 10px 0;
        box-sizing: border-box;
      }
      .sac-config-item-split {
        width: 100%;
        padding: 0;
        margin: 10px 0 20px 0;
        border-top: 1px dotted #ccc;
        height: 1px;
        font-size: 0;
      }
      .sac-config-device-type {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .fr {
        float: right;
      }
      .nonmotorstyle {
        width: 48px !important;
      }
      /*设备展示列表*/
      .sac-config-device-type-name,
      .sac-config-device-type-name-check {
        height: 100%;
        width: max-content;
        i,
        .sac-config-check-all,
        .sac-config-check {
          padding-left: 10px;
          width: 38px;
          position: relative;
          vertical-align: middle;
          margin-left: 0;
          text-align: center;
          color: #4a4a4a;
          font-style: normal;
          // margin-left: 20px ;
          line-height: 28px;
          float: left;
          cursor: pointer;
          &.checked {
            &.cl {
              color: #15997d;
            }
            &.fjdc {
              color: #49bf00;
            }
            &.rl {
              color: #d36c29;
            }
            &.rt {
              color: #08c6d4;
            }
          }
          &.disabled {
            color: #999;
          }
        }
        i {
          text-align: right;
          white-space: nowrap;
        }
      }
      .sac-config-device-type-name-check.sac-checks {
        white-space: nowrap;
        overflow: visible;
        flex-shrink: 0;
        &.his-to-right {
          left: 200px;
        }
      }
      .sac-config-device-list {
        flex: 1;
        padding: 0 0 0 10px;
        overflow-y: auto;
        overflow-x: hidden;
        /* &.his { top: 415px; }*/
      }
      .sac-config-device-info {
        position: relative;
        height: 28px;
        display: inline-flex;
        width: 100%;
        .sac-icon-delete {
          display: block;
          position: relative;
          width: 30px;
          text-align: right;
          color: #4a4a4a;
          font-style: normal;
          top: 2px;
          cursor: pointer;
        }
        .sac-camera-name {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          flex: 1;
          i {
            color: #2c86f8;
          }
        }
        &:hover {
          background-color: #f9f9f9;
          .sac-icon-delete {
            display: inline-block !important;
          }
          .sac-camera-name span {
            color: #2c86f8;
            font-weight: 600;
            cursor: pointer;
          }
        }
        &.sac-active-playing {
          .sac-camera-name span {
            color: #2c86f8;
            font-weight: 600;
          }
        }
        &.nohis {
          .sac-camera-name {
            color: #999;
          }
          &:hover {
            .sac-camera-name span {
              color: #999;
              font-weight: normal;
            }
          }
        }
      }
      .uploadImg {
        display: flex;
        .upload-img {
          justify-content: flex-start;
          /deep/ .upload-item {
            height: 80px !important;
            width: 80px !important;
            margin: 0 !important;
          }
        }
      }
    }
    .right-content {
      flex: 1;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 5px;
    }
  }
  .viewanalysis-footer {
    height: 50px;
    text-align: center;
    padding-top: 5px;
  }
  .handle-range {
    /deep/ .el-checkbox__inner {
      border: 1px solid #515a6e !important;
    }
  }
}
</style>
