<template>
  <div class="ui-quick-date-container">
    <div class="ui-date-tag" v-show="!isCustom">
      <span
        v-for="e in dateList"
        :class="{ active: e.value === dateType, border }"
        :key="e.value"
        @click="tagClick(e.value)"
      >
        {{ e.label }}
      </span>
    </div>
    <div class="ui-date-time" v-show="isCustom">
      <CustomDate
        v-model="customStartDate"
        placeholder="开始时间"
        key="startDate"
        @affirm="changeCustomDate($event, 'startDate')"
      />
      <!-- 宽度小于阈值，不展示横杠 -->
      <div class="line" v-show="parentDomWidth >= thresholdValue"></div>
      <Icon
        v-show="parentDomWidth < thresholdValue"
        type="ios-close-circle-outline"
        :size="16"
        color="gainsboro"
        @click="cancel"
      />
      <CustomDate
        v-model="customEndDate"
        placeholder="结束时间"
        key="endDate"
        @affirm="changeCustomDate($event, 'endDate')"
      />
      <Icon
        v-show="parentDomWidth >= thresholdValue"
        type="ios-close-circle-outline"
        :size="16"
        color="gainsboro"
        @click="cancel"
      />
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import CustomDate from "@/components/hl-daterange/index.vue";
export default {
  name: "UIQuickDate",
  props: {
    label: {
      type: String,
      default: "",
    },
    // 控件类型：week - 近一周，month - 近30天，quarter - 近三个月
    type: {
      type: String,
      default: "week",
    },
    rangeType: {
      type: String,
      default: "datetimerange",
    },
    format: {
      type: String,
    },
    border: {
      type: Boolean,
      default: false,
    },
    dateType: Number,
  },
  components: { CustomDate },
  model: {
    prop: "dateType",
  },
  data() {
    this.formatList = Object.freeze({
      date: "yyyy-MM-dd",
      datetimerange: "yyyy-MM-dd HH:mm:ss",
    });
    this.list = Object.freeze({
      week: [
        { label: "近一天", value: 1, interval: 1 },
        { label: "近三天", value: 2, interval: 3 },
        { label: "近一周", value: 3, interval: 7 },
        { label: "自定义", value: 4, custom: true },
      ],
      month: [
        { label: "今天", value: 1, interval: 1 },
        { label: "近7天", value: 2, interval: 7 },
        { label: "近30天", value: 3, interval: 30 },
        { label: "自定义", value: 4, custom: true },
      ],
      quarter: [
        { label: "近一周", value: 1, interval: 7 },
        { label: "近一个月", value: 2, interval: 30 },
        { label: "近三个月", value: 3, interval: 90 },
        { label: "自定义", value: 0, custom: true },
      ],
    });
    return {
        customStartDate: "", // 自定义开始时间
        customEndDate: "", // 自定义结束时间
        oldValue: undefined,
        parentDomWidth: 0, // 组件宽度
        thresholdValue: 300, // 阈值，设置自定义时间的样式
    };
  },
  computed: {
    dateList() {
      return this.list[this.type];
    },
    isCustom() {
      return this.dateList.find((e) => e.value === this.dateType).custom;
    },
    fmtDate() {
      let stFmt = "YYYY-MM-DD",
        etFmt = "YYYY-MM-DD";
      let fmtArr = this.formatType.split(" ");
      if (fmtArr.length > 1) {
        stFmt += ` ${"00:00:00".substring(0, fmtArr[1].length)}`;
        etFmt += ` ${"23:59:59".substring(0, fmtArr[1].length)}`;
      }
      return { stFmt, etFmt };
    },
    formatType() {
      return this.format || this.formatList[this.rangeType];
    },
  },
  created() {
    // 初始化时记录旧的tag
    this.oldValue = this.dateType;
  },
  mounted() {
    // 加载完时，获取下宽度，来进行展示
    this.$nextTick(() => {
      this.parentDomWidth = document.querySelector(
        ".ui-quick-date-container"
      ).clientWidth;
    });
  },
  methods: {
    /**
     * @description: 点击时间快捷标签
     * @param {number} value 时间标识
     */
    tagClick(value) {
      if (value === this.dateType) {
        // 点击了当前选中的tag
        return;
      }
      // 记住选择自定义tag前的那个tag，在取消后回到之前选中的tag
      this.oldValue = this.dateType;
      this.$emit("input", value);
      this.$nextTick(() => {
        if (!this.isCustom) {
          // 非自定义tag下，切换时触发change事件，满足切换tag直接操作的需求
          this.confirmChooseDate();
        } else {
          // 在自定义模式下，重新获取组件宽度
          this.parentDomWidth = document.querySelector(
            ".ui-quick-date-container"
          ).clientWidth;
        }
      });
    },

    /**
     * @description: 自定义时间选择框取消
     */
    cancel() {
      this.customStartDate = "";
      this.customEndDate = "";
      // 取消自定义tag，回退到前一个选中的tag
      this.$emit("input", this.oldValue);
      this.$nextTick(() => {
        this.confirmChooseDate(true);
      });
    },
    handleInit(dateType,startDate, endDate,) {
        if(dateType === 4 || dateType === 0) {
            this.customStartDate = startDate;
            this.customEndDate = endDate;
        }
        this.confirmChooseDate()
    },
    /**
     * @description: 时间选择确认
     * @param {boolean} isCancel 是否是退出自定义时间，此时不需要做时间校验
     */
    confirmChooseDate(isCancel = false) {
        let params = this.getDate(isCancel);
        this.$emit("change", { ...params, timeSlot: this.dateType });
    },  

    /**
     * @description: 获取当前快捷时间tag下的起止日期
     * @param {boolean} isCancel 是否是退出自定义时间，此时不需要做时间校验
     * @return {object} 起止日期
     */
    getDate(isCancel) {
      let st = "",
        et = "";
      if ((this.dateType === 4 || this.dateType === 0) && !isCancel) {
        // 自定义时间
        if (!this.customStartDate) {
          this.$Message.warning("开始时间不能为空！");
          return;
        }
        if (!this.customEndDate) {
          this.$Message.warning("结束时间不能为空！!");
          return;
        }
        if (
          new Date(this.customEndDate).getTime() <
          new Date(this.customStartDate).getTime()
        ) {
          this.$Message.warning("结束开始时间不能小于开始时间！");
          return;
        }
        st = this.customStartDate;
        et = this.customEndDate;
      } else {
        // 快捷时间tag
        let _type = this.dateList.find((e) => e.value === this.dateType);
        if (_type) {
          st = dayjs()
            .subtract(_type.interval - 1, "day")
            .format(this.fmtDate.stFmt);
          et = dayjs().format(this.fmtDate.etFmt);
        }
      }
      return {
        startDate: st,
        endDate: et,
      };
    },

    /**
     * @description: 设置默认时间
     * @param {array} date 起止时间
     */
    setDefaultDate(date = []) {
      if (date.length) {
        this.customStartDate = date[0];
        this.customEndDate = date[1];
      } else {
        this.customStartDate = "";
        this.customEndDate = "";
      }
    },

    /**
     * @description: 自定义时间框改变时间
     * @param {string} val 选中的时间
     * @param {string} type 开始 | 结束
     */
    changeCustomDate(val, type) {
      if (type === "startDate") {
        this.customStartDate = val;
      }
      if (type === "endDate") {
        this.customEndDate = val;
      }
      // 当一个时间确认时，另一个时间也有值，才触发emit
      if (this.customStartDate && this.customEndDate) {
        this.confirmChooseDate();
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ui-quick-date-container {
  min-height: 32px;
  line-height: 32px;
  & > div {
    display: inline-block;
  }
  .ui-date-tag {
    & > span {
      height: 18px;
      border-radius: 2px;
      padding: 1px 7px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      margin-right: 10px;
      cursor: pointer;
      &:last-child {
        margin-right: 0;
      }
    }

    .border {
      background: #fff;
      border: 1px solid #e8eaec;
    }

    .active {
      color: #fff;
      background: #2c86f8;
    }
  }
  .ui-date-time {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    gap: 6px;
    .line {
      width: 16px;
      height: 2px;
      background: gainsboro;
    }
  }
  .ivu-icon {
    position: relative;
    cursor: pointer;
  }
}
</style>
