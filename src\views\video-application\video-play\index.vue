<template>
  <div class="video-play">
    <div class="video-left mr-10" :style="{ width: leftWidth + 'px' }">
      <ul class="search_tab">
        <li
          v-for="(item, index) in tablist"
          :key="index"
          class="tabslist"
          @click="handleClickTab(index)"
          :class="{ active: tabIndex == index }"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="tab-content">
        <component
          :is="tablist[tabIndex].componentName"
          @handleClick="handleNodeClick"
          @handlePlayByAlias="handlePlayByAlias"
          @changeLayout="changeLayout"
          @inspectPlay="inspectPlay"
          @closeAll="closeAll"
          :playingDeviceIds="playingDeviceIds"
          :playingHistoryIds="playingHistoryIds"
        ></component>
      </div>
      <toggle-button
        class="toggle-w"
        :boxWidth.sync="leftWidth"
      ></toggle-button>
    </div>
    <div class="right-content">
      <div class="top">
        <h5-player
          ref="H5Player"
          addHistory
          :isShowTimershaft.sync="isShowTimershaft"
          @changeWinList="getWinList"
        ></h5-player>
      </div>
    </div>
    <div class="toolbar" :class="{ 'toolbar-hide': !isShowToolBar }">
      <div
        class="toggle"
        :class="{ hide: !isShowToolBar }"
        @click="isShowToolBar = !isShowToolBar"
      >
        <Tooltip
          :always="!isShowToolBar"
          content="工具栏"
          placement="bottom"
          max-width="18"
        >
          <i class="iconfont icon-doubleright" v-if="isShowToolBar"></i>
          <i class="iconfont icon-doubleleft" v-if="!isShowToolBar"></i>
        </Tooltip>
      </div>
      <div class="toolbar-title">播放控件</div>
      <div class="toolbar-buttons">
        <Dropdown
          trigger="click"
          transfer
          placement="left-start"
          @on-click="changeLayout($event)"
        >
          <i class="iconfont icon-fenping" title="切换分屏"></i>
          <DropdownMenu slot="list">
            <DropdownItem
              name="1*1"
              :class="{ current: currentLayout == '1*1' }"
            >
              <i class="iconfont icon-danfenping mr-10" />单分屏
            </DropdownItem>
            <DropdownItem
              name="1A2"
              :class="{ current: currentLayout == '1A2' }"
            >
              <i class="iconfont icon-sanfenping1 mr-10" />三分屏
            </DropdownItem>
            <DropdownItem
              name="2A1"
              :class="{ current: currentLayout == '2A1' }"
            >
              <i class="iconfont icon-sanfenping2 mr-10" />三分屏
            </DropdownItem>
            <DropdownItem
              name="2*2"
              :class="{ current: currentLayout == '2*2' }"
            >
              <i class="iconfont icon-sifenping mr-10" />四分屏
            </DropdownItem>
            <DropdownItem
              name="1A5"
              :class="{ current: currentLayout == '1A5' }"
            >
              <i class="iconfont icon-liufenping mr-10" />六分屏
            </DropdownItem>
            <DropdownItem
              name="1A7"
              :class="{ current: currentLayout == '1A7' }"
            >
              <i class="iconfont icon-bafenping mr-10" />八分屏
            </DropdownItem>
            <DropdownItem
              name="3*3"
              :class="{ current: currentLayout == '3*3' }"
            >
              <i class="iconfont icon-jiufenping mr-10" />九分屏
            </DropdownItem>
            <DropdownItem
              name="4*4"
              :class="{ current: currentLayout == '4*4' }"
            >
              <i class="iconfont icon-shiliufenping mr-10" />十六分屏
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <i
          class="iconfont icon-touping"
          title="发送到扩展屏"
          @click="moveExtendedScreen"
        ></i>
        <!-- <i class="iconfont icon-shijianzhou" title="录像时间轴" :class="{ 'color-blue': isShowTimershaft }" @click="changeRcdtimershaft"></i> -->
        <i
          class="iconfont icon-quanping_xian"
          title="全屏"
          @click="setFullScreen"
        ></i>
        <i
          class="iconfont icon-guanbi close"
          title="全部关闭"
          @click="closeAll"
        ></i>
      </div>
    </div>
  </div>
</template>

<script>
import deviceTree from "./components/device-tree.vue";
import groupTree from "./components/group-tree.vue";
import playHistory from "./components/play-history.vue";
import toggleButton from "@/components/toggle-button.vue";

export default {
  name: "video-play",
  components: {
    deviceTree,
    groupTree,
    playHistory,
    toggleButton,
  },
  data() {
    return {
      tablist: [
        { name: "视频资源", componentName: "deviceTree" },
        { name: "我的分组", componentName: "groupTree" },
        { name: "播放历史", componentName: "playHistory" },
      ],
      tabIndex: 0,
      currentLayout: "2*2",
      isShowTimershaft: false,
      isShowToolBar: false,
      playingHistoryIds: [],
      playingDeviceIds: [],
      leftWidth:
        (parseFloat(
          window.getComputedStyle(window.document.documentElement)["font-size"]
        ) /
          192) *
        316,
    };
  },
  computed: {
    // leftWidth() {
    //     if(parseFloat(window.getComputedStyle(window.document.documentElement)['font-size']) == 16){
    //         return 316;
    //     }else{
    //         return (parseFloat(window.getComputedStyle(window.document.documentElement)['font-size']) / 192) * 316;
    //     }
    // },
  },
  methods: {
    handleClickTab(index) {
      this.tabIndex = index;
    },
    handleNodeClick(item) {
      if (item.playType == "live") {
        this.$refs.H5Player.playStream(item, item.playType);
      } else {
        this.$refs.H5Player.openVideoSearch(item, true);
      }
    },
    handlePlayByAlias(item, tabelSuffix) {
      if (item.playType == "live") {
        this.$refs.H5Player.playAlarmStream(
          item,
          item.playType,
          false,
          tabelSuffix
        );
      }
    },
    // 切换布局
    changeLayout(layout) {
      this.currentLayout = layout;
      this.$refs.H5Player.changeLayout(layout);
    },
    // 扩展屏
    moveExtendedScreen() {
      this.$refs.H5Player.moveExtendedScreen();
    },
    // 时间轴
    changeRcdtimershaft() {
      this.isShowTimershaft = !this.isShowTimershaft;
      this.$refs.H5Player.changeRcdtimershaft(this.isShowTimershaft);
    },
    // 全屏
    setFullScreen() {
      this.$refs.H5Player.setFullScreen();
    },
    closeAll() {
      this.$refs.H5Player.closeAll();
    },
    getWinList(val) {
      this.playingHistoryIds = val.map((v) => v.historyId);
      this.playingDeviceIds = val.map((v) => v.deviceId);
    },
    // 轮巡
    inspectPlay({ devices, indexs }) {
      devices.forEach((item, index) => {
        this.$refs.H5Player.playStream(
          { inspecting: true, ...item },
          item.playType,
          indexs[index]
        );
      });
    },
  },
};
</script>
<style lang="less" scoped>
.video-play {
  width: 100%;
  display: flex;
  .video-left {
    width: 316px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    position: relative;
    .search_tab {
      display: flex;
      justify-content: space-evenly;
      border-bottom: 1px solid #d3d7de;
      margin-bottom: 15px;
      .tabslist {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        width: 25%;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;
      }
      .active {
        color: #2c86f8;
        border-bottom: 3px solid #2c86f8;
      }
    }
    .tab-content {
      height: calc(~"100% - 54px");
    }
    .toggle-w {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
  .right-content {
    flex: 1;
    background-color: #2c3033;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    position: relative;
    .top {
      flex: 1;
      padding: 7px;
    }
  }
  .toolbar {
    position: fixed;
    top: 286px;
    transform: translate(0, -50%);
    right: 0;
    width: 70px;
    height: 360px;
    background: #fff;
    box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
    transition: all 0.5s;
    &-hide {
      right: -70px;
    }
    .toolbar-title {
      width: 100%;
      text-align: center;
      line-height: 39px;
      font-size: 14px;
      font-weight: 700;
      color: #3d3d3d;
      border-bottom: 1px solid #d3d7de;
    }
    .toolbar-buttons {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      width: 100%;
      .color-blue {
        color: #2c86f8 !important;
      }
      .ivu-dropdown {
        width: 100%;
        text-align: center;
      }
      .iconfont {
        font-size: 22px;
        color: #484847;
        cursor: pointer;
        &:hover {
          color: #2c86f8;
        }
      }
    }
    .toggle {
      position: absolute;
      top: 4px;
      left: -18px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
      width: 18px;
      height: 36px;
      background: #2c86f8;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &.hide {
        i {
          transform: rotate(180deg);
        }
      }
    }
  }
}
/deep/ .ivu-tooltip-inner {
  padding: 3px;
  font-size: 12px;
  border-radius: 2px;
}
/deep/ .ivu-tooltip-popper {
  left: 0 !important;
}
</style>
<style lang="less">
.ivu-dropdown-transfer {
  max-height: 300px !important;
  .current {
    background: rgba(44, 134, 248, 0.1029);
    color: #2c86f8;
  }
}
</style>
