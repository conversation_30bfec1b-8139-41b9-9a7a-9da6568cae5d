<template>
  <ui-search-tree class="collapse-tree" :no-search="noSearch" v-bind="$attrs" v-on="$listeners"></ui-search-tree>
</template>
<script>
export default {
  props: {
    noSearch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.collapse-tree {
  @{_deep}.el-tree {
    & > .el-tree-node {
      > .el-tree-node__content {
        padding-left: 10px !important;
        background-color: var(--bg-collapse-item);
        color: var(--color-vertical-tab);
        .el-tree-node__expand-icon {
          color: var(--color-primary);
        }
      }
    }
    .el-tree-node__content {
      height: 34px;
    }
    .el-tree-node__expand-icon {
      position: absolute;
      right: 10px;
      &::before {
        font-size: 14px;
        content: '\e6e0';
      }
    }
  }
}
</style>
