<template>
  <div class="title-bar">
    <div class="title-bar-header">
      <span class="title-bar-title">
        <span class="title-bar-rect"></span>
        <span class="ml-sm">{{ getTitle }}</span>
      </span>
    </div>
    <div class="tab-content">
      <div class="tab-content-box">
        <slot name="left">
          <div class="bar-tab">
            <span
              v-for="(item, index) in tabList"
              :key="index"
              @click="clickItem(item, index)"
              class="link-text"
              :class="{ 'link-text-active': index === activeValue }"
            >
              {{ item.label }}
            </span>
          </div>
        </slot>
        <slot name="right">
          <div class="time-box">
            <span>统计时间：</span>
            <DatePicker
              v-model="examineTime"
              type="month"
              placeholder="请选择时间"
              class="width-150"
              :options="timeOption"
              @on-change="onChangeTime"
            />
            <template v-if="showTimeNum">
              <span class="ml-xs mr-xs span-line"></span>
              <Select v-model="batchId" class="width-150" @on-change="changeRouteUrl">
                <Option v-for="(item, index) in batchIdList" :value="item.batchId" :key="`${index}-${item.batchId}`">
                  {{ item.desc }}
                </Option>
              </Select>
            </template>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';

export default {
  components: {},
  props: {
    // 树菜单列表
    menuList: {
      type: Array,
      default: () => [],
    },
    tabList: {
      type: Array,
      default: () => [],
    },
    showTimeNum: {
      type: Boolean,
      default: true,
    },
    detectionTimeList: {
      type: Array,
      default: () => [],
    },
  },
  mixins: [dealWatch],
  data() {
    return {
      routeQuery: {},
      activeValue: 0,
      examineTime: '',
      batchId: '',
      dateOptions: [],
      batchIdList: [],
      timeOption: {
        disabledDate: (date) => {
          const dateArr = this.detectionTimeList.map((item) => {
            const dateFormat = new Date(`${item.date} 00:00:00`);
            return dateFormat.getTime();
          });
          const dateTime = date.valueOf();
          return !dateArr.includes(dateTime);
        },
      },
    };
  },
  computed: {
    getTitle() {
      if (this.routeQuery.treeId) {
        let menus = this.$util.common.deepCopy(this.menuList);
        let menusArr = this.$util.common.jsonToArray(menus);
        let treeArr = menusArr.filter((item) => item.id === this.routeQuery.treeId);
        return treeArr[0]?.title || '';
      }
      return '';
    },
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  watch: {
    detectionTimeList: {
      handler(val) {
        if (val.length === 0) {
          this.examineTime = '';
          this.batchId = '';
          this.batchIdList = [];
          this.dateOptions = [];
        } else {
          let { examineTime, batchId } = this.$route.query;
          val.map((item) => {
            this.$set(this.dateOptions, item.date, item.options);
          });
          this.examineTime = examineTime || val[0].date;
          this.batchIdList = this.dateOptions[this.examineTime];
          this.batchId = batchId || this.batchIdList[0].batchId;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getParams() {
      this.routeQuery = this.$route.query;
    },
    clickItem(val, index) {
      if (this.activeValue === index) return;
      this.activeValue = index;
      this.$emit('on-change', val, index);
    },
    onChangeTime(date) {
      this.examineTime = date;
      this.batchIdList = this.dateOptions[this.examineTime];
      this.batchId = this.batchIdList[0].batchId;
      this.changeRouteUrl();
    },
    changeRouteUrl() {
      this.routeQuery = this.$route.query;
      this.$router.push({
        query: {
          ...this.routeQuery,
          examineTime: this.examineTime || '',
          batchId: this.batchId,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='deepBlue'] {
  .link-text-active {
    color: var(--color-primary) !important;
  }
}
.title-bar {
  font-size: 14px;
  .title-bar-header {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    background: var(--bg-navigation);
    .title-bar-title {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: bold;
      color: var(--color-content);
    }
    .title-bar-rect {
      display: inline-block;
      width: 5px;
      height: 20px;
      background: var(--bg-title-rect);
    }
  }
  .tab-content {
    padding: 0 20px 16px;
    .tab-content-box {
      display: flex;
      justify-content: space-between;
      height: 67px;
      border-bottom: 1px solid var(--border-color);
      .link-text {
        position: relative;
        color: var(--color-tab-title);
        cursor: pointer;
        width: 92px;
        display: inline-block;
        text-align: center;
        line-height: 67px;
        &.link-text-active {
          background: var(--bg-gradient-tab);
          color: var(--color-select-item-active);
        }
      }
    }
  }

  @{_deep}.time-box {
    display: flex;
    align-items: center;
    color: var(--color-label);
    .span-line {
      display: inline-block;
      width: 10px;
      border: 1px solid var(--border-table);
    }
    .ivu-select-selected-value,
    .ivu-select-selection {
      height: 34px;
      line-height: 34px;
    }
  }
  .width-150 {
    width: 150px;
  }
}
</style>
