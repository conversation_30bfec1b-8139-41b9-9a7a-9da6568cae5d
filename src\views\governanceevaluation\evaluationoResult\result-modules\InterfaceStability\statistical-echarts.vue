<template>
  <div class="rule-container">
    <div class="statistics-wrapper">
      <div class="statistics">
        <index-statistics></index-statistics>
      </div>
      <div class="unqualified">
        <unqualified-reason></unqualified-reason>
      </div>
    </div>
    <div class="subordinate-wrapper mt-sm">
      <div class="city mr-sm">
        <subordinate-chart :activeIndexItem="activeIndexItem">
          <template #rank-title>
            <span>按可调阅率排名</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart class="line-chart"></line-chart>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statistical-echarts',
  components: {
    IndexStatistics: require('@/views/governanceevaluation/evaluationoResult/components/index-statistics').default,
    ResultRank: require('./components/result-rank.vue').default,
    UnqualifiedReason: require('./components/unqualified-reason.vue').default,
    SubordinateChart: require('./components/subordinate-chart.vue').default,
    LineChart: require('./components/line-chart.vue').default,
  },
  data() {
    return {};
  },
  props: {
    activeIndexItem: {},
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.rule-container {
  position: relative;
  height: 100%;
  width: 100%;
  padding: 10px 0 0 10px;
  .statistics-wrapper {
    display: flex;
    height: 215px;
    .statistics {
      width: calc(100% - 488px);
    }
    .unqualified {
      width: 488px;
    }
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
