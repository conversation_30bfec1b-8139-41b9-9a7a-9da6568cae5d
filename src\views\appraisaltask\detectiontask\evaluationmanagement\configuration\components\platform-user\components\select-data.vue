<template>
  <ui-modal v-model="visible" :styles="styles" :title="modalAction.title" @query="query">
    <div class="search-module over-flow">
      <slot name="searchModule"></slot>
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="table-module auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
      <picture-item
        class="picture-item"
        v-for="(item, index) in tableData"
        v-model="item.checked"
        :key="index"
        :img-src="item.url"
      >
        <template #message>
          <slot name="message" :item="item">
            <p class="text">
              <i class="icon-font icon-ren"></i>
              <span class="inline vt-middle base-text-color ml-xs ellipsis" :title="item.name">{{
                item.name || '未知'
              }}</span>
            </p>
            <p class="text mt-xs">
              <i class="icon-font icon-shenfenzheng"></i>
              <span class="inline vt-middle base-text-color ml-xs ellipsis" :title="item.idCard">{{
                item.idCard
              }}</span>
            </p>
            <p class="text mt-xs">
              <i class="icon-font icon-dizhi"></i>
              <span class="inline vt-middle base-text-color ml-xs">{{ item.civilName }}</span>
            </p>
          </slot>
        </template>
      </picture-item>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {
      type: Boolean,
    },
    modalAction: {},
    searchParams: {},
    defaultChecked: {},
  },
  data() {
    return {
      styles: {
        width: '8.5rem',
      },
      visible: false,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {},
      tableData: [],
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        const res = await this.$http.post(equipmentassets.getManagementData, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
        this.checkDefault();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    query() {
      const selectedArr = this.tableData.filter((row) => !!row.checked);
      this.$emit('query', selectedArr);
      this.visible = false;
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.$emit('reset');
      this.$nextTick(() => {
        this.search();
      });
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    checkDefault() {
      this.tableData.forEach((row) => {
        this.defaultChecked.forEach((rw) => {
          if (row.id === rw) {
            this.$set(row, 'checked', true);
          }
        });
      });
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    searchParams: {
      handler(val) {
        this.searchData = {
          ...val,
        };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    PictureItem:
      require('@/views/systemconfiguration/dockingservice/inspectiondatamanagement/components/picture-item.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  height: 600px;
  display: flex;
  flex-direction: column;
}
.table-module {
  padding: 20px 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  overflow-y: auto;
  .picture-item {
    margin-left: 0;
    margin-right: 10px;
  }
}
.text {
  span {
    width: 160px;
  }
  i {
    color: #8797ac;
    width: 14px;
    text-align: center;
    font-size: 12px;
  }
}
</style>
