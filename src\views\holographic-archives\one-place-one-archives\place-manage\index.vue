<!--
 * @Date: 2025-01-07 11:13:41
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-13 18:16:31
 * @FilePath: \icbd-view\src\views\holographic-archives\one-place-one-archives\place-manage\index.vue
-->
<template>
  <div class="place-manage-page">
    <div class="right-list-box mr-10">
      <div class="search-box">
        <Input
          placeholder="请输入场所类型名称"
          v-model="searchInput"
          @keydown.enter.native="searchPlace"
        >
          <Icon
            type="ios-search"
            class="font-16 cursor-p"
            slot="suffix"
            maxlength="50"
            @click.prevent="searchPlace"
          />
        </Input>
        <i
          class="iconfont icon-jia auxiliary-color ml-15 add-group"
          @click="addFirstGroup"
        ></i>
      </div>
      <div class="place-tree-box">
        <el-tree
          ref="treeRef"
          :data="isSearch ? placeSearchList : placeTreeList"
          node-key="id"
          :default-expand-all="true"
          :expand-on-click-node="false"
          :highlight-current="true"
          :current-node-key="currentNodeKey"
        >
          <!-- :filter-node-method="filterNodeMethod"
          @node-click="handleNodeClick" -->
          <template slot-scope="{ node, data }">
            <div class="tree-item" @click.stop="treeNodeClick(data)">
              <div class="tree-title">
                <div class="tree-label">
                  {{ data.label }}(
                  <div class="tree-number" style="">
                    {{ data.allTotal || 0 }}
                  </div>
                  )
                </div>
              </div>
              <div class="tree-action">
                <i
                  class="iconfont color-blue icon-tianjia1"
                  title="新增"
                  v-if="data.leave == 1"
                  @click.stop="addSecondGroup(data)"
                ></i>
                <i
                  class="iconfont color-blue icon-bianji"
                  title="编辑"
                  @click.stop="editGroupDataValue(data, node)"
                ></i>
                <i
                  class="iconfont color-blue icon-shanchu1"
                  title="删除"
                  @click.stop="deleteGroup(node)"
                ></i>
              </div>
            </div>
          </template>
        </el-tree>
        <ui-empty v-if="placeTreeList.length === 0"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
    </div>
    <div class="container left-content-box">
      <div>
        <Search
          ref="searchRef"
          @search="searchTablePlace"
          @add="addPlace"
          @delete="deleteHandler(selectionList)"
        />
      </div>
      <div class="place-table-box">
        <ui-table
          ref="tableRef"
          :columns="columns"
          :data="placeList"
          :loading="tableLoading"
          @on-selection-change="selectionChangeHandle"
          @scroll.stop
        >
          <template #image="{ row }">
            <div class="table-image" style="width: 110px; height: 110px">
              <ui-image viewer type="place" :src="row.image" />
            </div>
          </template>
          <template #action="{ row }">
            <div class="table-action">
              <i
                class="iconfont icon-xiangqing"
                @click="openDetail(row)"
                title="详情"
              ></i>
              <i
                class="iconfont icon-bianji"
                @click="openDetailEdit(row)"
                title="编辑"
              ></i>
              <i
                class="iconfont icon-shanchu1"
                @click="deleteHandler([row])"
                title="删除"
              ></i>
            </div>
          </template>
        </ui-table>
      </div>
      <!-- 分页 -->
      <div>
        <ui-page
          :current="pageNumber"
          :total="total"
          :page-size="pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <EditGroup
      ref="editGroup"
      v-bind="{ ...editGroupProp }"
      @comfirm="editConfirmHandler"
    ></EditGroup>
    <PlaceDetail
      ref="placeDetailRef"
      :detailInfo="selectDetailInfo"
    ></PlaceDetail>
    <PlaceEdit
      ref="placeEditRef"
      :is-edit="isEdit"
      @confirm="editConfirm"
    ></PlaceEdit>
  </div>
</template>
<script>
import Search from "./components/search.vue";
import EditGroup from "./components/editGroup.vue";
import PlaceDetail from "./components/placeDetail.vue";
import PlaceEdit from "./components/placeEdit.vue";
import { deepCopy } from "@/util/modules/common";

import {
  countBySecondLevel,
  getaoi,
  deletePlaceArchiveModify,
} from "@/api/operationsOnTheMap";
import { getPlaceTypeList, deletePlaceType } from "@/api/placeArchive.js";
export default {
  name: "place-manage",
  components: {
    Search,
    EditGroup,
    PlaceDetail,
    PlaceEdit,
  },
  async mounted() {
    this.loadNode();
  },
  data() {
    return {
      searchInput: "",
      loading: false,
      placeParams: {},
      isSearch: false,
      placeTreeList: [],
      placeSearchList: [],
      currentNodeKey: "",
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "场所照片", slot: "image", align: "center" },
        { title: "场所名称", key: "name", align: "center" },
        { title: "场所面积(m²)", key: "area", align: "center" },
        { title: "所属区县", key: "adname", align: "center", width: 100 },
        { title: "所属街道", key: "townname", align: "center" },
        { title: "场所地址", key: "address", align: "center" },
        { title: "数据更新时间", key: "modifyTime", align: "center" },
        { title: "操作", slot: "action", align: "center" },
      ],
      placeList: [],
      tableLoading: false,
      pageNumber: 1,
      total: 20,
      pageSize: 10,
      form: {
        name: "",
        address: "",
      },
      selectDetailInfo: {},
      isEdit: false,
      editGroupProp: {
        title: "",
        otherParam: {},
        isEdit: false,
        isSecond: false,
      },
      selectionList: [],
      placeTypeList: [],
    };
  },
  computed: {},
  watch: {},
  methods: {
    async initPlaceTypeList() {
      let { data } = await getPlaceTypeList({});
      return data;
    },
    searchPlace() {
      if (this.searchInput !== "") {
        this.isSearch = true;
        this.loading = true;
        this.placeSearchList = [];
      } else {
        this.isSearch = false;
        return;
      }
      this.placeTreeList.forEach((item) => {
        if (item.label.includes(this.searchInput)) {
          this.placeSearchList.push(item);
        } else {
          let searchChildList = deepCopy(item);
          searchChildList.children = [];
          let total = 0;
          item.children.forEach((v) => {
            if (v.label.includes(this.searchInput)) {
              total += v.allTotal;
              searchChildList.children.push(v);
            }
          });
          if (searchChildList.children.length > 0) {
            searchChildList.allTotal = total;
            this.placeSearchList.push(searchChildList);
          }
        }
      });
      this.loading = false;
    },
    // 添加一级分类
    addFirstGroup() {
      this.editGroupProp = {
        title: "新增一级分类",
        otherParam: {
          parentId: -1,
        },
        isSecond: false,
        isEdit: false,
      };
      this.$refs.editGroup.show({});
    },
    // 添加二级分类
    addSecondGroup(data) {
      this.editGroupProp = {
        title: "新增二级分类",
        otherParam: {
          parentId: data.id,
        },
        isSecond: true,
        isEdit: false,
      };
      this.$refs.editGroup.show({ firstLevel: data.label });
    },
    // 编辑分类
    editGroupDataValue(data, node) {
      this.editGroupProp.title =
        data.leave == 1 ? "编辑一级分类" : "编辑二级分类";
      this.editGroupProp.otherParam = {
        id: data.id,
        parentId: data.leave == 1 ? -1 : node.parent.data.id,
      };
      this.editGroupProp.isSecond = data.leave == 2;
      this.editGroupProp.isEdit = true;
      let firstLevelName =
        data.leave == 1 ? data.label : node.parent.data.label;
      let secondLevelName = data.leave == 2 ? data.label : "";
      this.$refs.editGroup.show({
        firstLevel: firstLevelName,
        secondLevel: secondLevelName,
        typeCode: data.typeCode,
        icon: data.icon,
      });
    },
    searchTablePlace(value) {
      this.form = value;
      this.pageNumber = 1;
      this.queryInfo();
    },
    addPlace() {
      this.isEdit = false;
      this.$refs.placeEditRef.showModal({ isEdit: this.isEdit });
    },
    // 初始化树数据
    async loadNode() {
      this.loading = true;
      let { data } = await countBySecondLevel();
      const placeListAll = await this.initPlaceTypeList();
      // 一级二级组合
      let group = [];
      placeListAll.forEach((v) => {
        // 二级场所类型
        if (v.parentId != -1) {
          return;
        }
        // 一级场所类型 添加二级内容
        let children = placeListAll.filter((item) => item.parentId == v.id);
        let total = 0;
        children = children.map((item) => {
          for (let i = 0; i < data.length; i++) {
            if (item.typeCode === data[i].key) {
              item.allTotal = data[i].statisticsQuantity || 0;
              total += data[i].statisticsQuantity || 0;
              data.splice(i, 1);
              break;
            }
          }
          return {
            ...item,
            label: item.typeName,
            id: item.id,
            leaf: true,
            leave: 2,
          };
        });
        group.push({
          ...v,
          children,
          label: v.typeName,
          id: v.id,
          leave: 1,
          allTotal: total,
        });
      });
      this.placeTreeList = group;
      // 默认选中第一项
      this.queryInfo({
        firstLevel: this.placeTreeList[0]?.typeCode || "",
      });
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(this.placeTreeList[0]?.id || "");
        this.currentNodeKey = this.placeTreeList[0]?.id;
      });
      this.loading = false;
    },
    /**
     * @description 点击子树
     * @param data
     */
    treeNodeClick(data) {
      // 更新elTree 的选中树
      this.$nextTick(() => {
        this.$refs.treeRef.setCurrentKey(data.id);
      });
      this.currentNodeKey = data.id;
      // 右侧table查询参数
      if (data.leave > 1) {
        // 二级参数
        this.placeParams = {
          secondLevelList: [data.typeCode],
        };
      } else {
        // 一级参数
        this.placeParams = {
          firstLevel: data.typeCode,
        };
      }
      // 初始化分页
      this.pageNumber = 1;
      this.queryInfo();
    },
    // 删除分类
    deleteGroup(node) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该分类？`,
        onOk: () => {
          const placeTypeId = node?.data?.id;
          deletePlaceType(placeTypeId).then(async (res) => {
            this.$Message.success(res.msg);
            // 更新elTree树数据
            this.loadNode();
          });
        },
      });
    },
    /**
     * @description 列表二级查询
     * @param param 二级信息
     */
    queryInfo(param = {}) {
      this.tableLoading = true;
      getaoi({
        pageNumber: this.pageNumber,
        pageSize: this.pageSize,
        ...this.placeParams,
        ...this.form,
        ...param,
      })
        .then(({ data }) => {
          this.total = data.total;
          this.placeList = data.entities;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    pageChange(value) {
      this.pageNumber = value;
      this.queryInfo();
    },
    pageSizeChange(value) {
      this.pageSize = value;
      this.queryInfo();
    },
    // 打开编辑
    openDetail(data) {
      this.selectDetailInfo = { ...data };
      this.$refs.placeDetailRef.showModal();
    },
    // 完成编辑
    editConfirm() {
      this.queryInfo();
    },
    openDetailEdit(data) {
      this.isEdit = true;
      this.$refs.placeEditRef.showModal({
        ...data,
        image: [data.image],
        isEdit: this.isEdit,
      });
    },
    // 场所删除
    deleteHandler(value) {
      if (value.length < 1) {
        this.$Message.warning("请选择需要删除的场所");
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该场所？`,
        onOk: () => {
          let param = value.map((item) => item.id).join(",");
          deletePlaceArchiveModify(param).then((res) => {
            this.$Message.success(res.msg);
            this.queryInfo();
          });
        },
      });
    },
    // 表格选中框事件
    selectionChangeHandle(list) {
      this.selectionList = list;
    },
    // 编辑类型确认
    async editConfirmHandler() {
      await this.loadNode();
      if (this.isSearch) {
        this.searchPlace();
      }
    },
  },
};
</script>
<style lang="less" scoped>
.place-manage-page {
  display: flex;
  flex: 1;

  .right-list-box {
    width: 316px;
    height: 100%;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    padding: 16px 20px 0 20px;
    .search-box {
      display: flex;
      height: 40px;
      justify-content: flex-start;
      align-items: center;

      .add-group {
        cursor: pointer;
        font-size: 20px;
        border-radius: 4px;
        padding: 1px 5.5px;
        border: 1px solid #d3d7de;
      }

      /deep/ .ivu-btn {
        padding-left: 0;
        padding-right: 0;
        background: white;
        border: 1px solid rgb(211, 215, 222);
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
        border-left: none;
        color: #808695;

        .ivu-icon {
          color: #808695;
        }
        &:hover {
          border: 1px solid #248afc;
          border-left: none;
        }
      }

      /deep/ .ivu-input {
        &:hover {
          border: 1px solid #248afc;
        }
      }

      &:focus-within {
        /deep/ .ivu-btn {
          border: 1px solid #248afc;
          border-left: none;
        }
      }
    }
    .place-tree-box {
      overflow-y: scroll;
      margin-top: 15px;
      height: calc(~"100% - 80px");
      // el-tree组件右侧不对其
      position: relative;
      left: -7px;
      .tree-item {
        width: 100%;
        display: flex;
        justify-content: space-between;
        height: 35px;
        line-height: 35px;
        .tree-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.8);
          .tree-label {
            font-weight: 600;
            display: flex;
            .tree-number {
              color: #2c86f8;
              font-weight: normal;
            }
          }
        }

        .tree-action {
          display: none;
          i {
            padding-left: 10px;
            font-size: 16px;
            color: #2c86f8;
          }
        }
        &:hover {
          .tree-action {
            display: block;
          }
        }
      }
      /deep/.el-tree-node.is-current > .el-tree-node__content {
        background: #2c86f8 !important;
        color: #fff;
        .tree-title {
          color: #fff;
          .tree-label {
            .tree-number {
              color: #fff;
            }
          }
        }
        .tree-action {
          display: block;
          i {
            color: #fff;
          }
        }
      }
    }
  }
  .left-content-box {
    width: 1494px;
    overflow-x: hidden;
    display: flex;
    flex: 1;
    flex-direction: column;
    .place-table-box {
      flex: 1;
      width: 100%;
      // overflow: scroll;
      /deep/ .ui-table {
        height: 100%;
      }
      /deep/ .ivu-table-cell-slot {
        display: flex;
        justify-content: center;
      }
      .table-action {
        color: #2c86f8;
        i {
          margin-left: 20px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
