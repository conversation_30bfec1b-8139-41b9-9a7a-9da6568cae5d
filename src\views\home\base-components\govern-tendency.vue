<template>
  <!-- 治理趋势 -->
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: tableData }">
    <draw-echarts :echart-option="echartOption" ref="governChart" :data="governEffectData"></draw-echarts>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import Vue from 'vue';
import GovernTendencyTooltip from '@/views/home/<USER>/govern-tendency-tooltip';
import commonStyle from '@/views/home/<USER>/module/common-style';
export default {
  name: 'govern-tendency',
  props: {
    year: {
      default: '',
    },
    selectData: {
      default: () => [],
    },
    styleType: {},
  },
  data() {
    return {
      open: false,
      sourceData: {},
      governEffectData: [],
      indexModuleSelectData: '1',
      echartOption: {},
      echartsLoading: false,
      echart1: [],
      tableData: [],
      legendList: [],
      colorListGradient: [
        ['rgba(255, 167, 0, 0.3)', 'rgba(255, 167, 0, 0)'],
        ['rgba(25, 129, 245, 0.3)', 'rgba(25, 129, 245, 0)'],
        ['rgba(1, 239, 119, 0.3)', 'rgba(1, 239, 119, 0)'],
        ['rgba(4, 205, 244, 0.3)', 'rgba(4, 205, 244, 0)'],
      ],
      colorList: ['#FFA700', '#1981F5', '#01EF77', 'rgba(4, 205, 244, 1)'],
      tooltipFormatter: (data) => {
        let year = this.year;
        let GovernTendencyTooltip1 = Vue.extend(GovernTendencyTooltip);
        let _this = new GovernTendencyTooltip1({
          el: document.createElement('div'),
          data() {
            return {
              data,
              year,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      tableColumns: [
        {
          title: '治理内容',
          key: 'indexName',
          minWidth: 120,
          ellipsis: true,
          render: (h, { row }) => {
            return <span title={row.indexName}>{row.indexName}</span>;
          },
        },
        {
          title: '治理前（1月平均）',
          key: 'minResultValue',
          tooltip: true,
          align: 'center',
          minWidth: 80,
          render: (h, { row }) => {
            return <span class="color-red">{row.minResultValue}%</span>;
          },
          renderHeader: (h) => {
            return (
              <span>
                <p>治理前</p>
                <p>({this.minMonth}月平均)</p>
              </span>
            );
          },
        },
        {
          title: '治理后（12月平均）',
          key: 'maxResultValue',
          tooltip: true,
          align: 'center',
          minWidth: 80,
          render: (h, { row }) => {
            return <span class="color-blue">{row.maxResultValue}%</span>;
          },
          renderHeader: (h) => {
            return (
              <span>
                <p>治理后</p>
                <p>({this.maxMonth}月平均)</p>
              </span>
            );
          },
        },
        {
          title: '变化',
          key: 'changeValue',
          tooltip: true,
          align: 'right',
          minWidth: 70,
          render: (h, { row }) => {
            return (
              <span>
                <span class={Number.parseFloat(row.changeValue) >= 0 ? ['color-green'] : ['color-red']}>
                  {row.changeValue}%
                </span>
                <span
                  class={
                    Number.parseFloat(row.changeValue) >= 0
                      ? ['icon-font', 'icon-shangsheng', 'color-green']
                      : ['icon-font', 'icon-xiajiang', 'color-red']
                  }
                ></span>
              </span>
            );
          },
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
    }),
    minMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['minMonth'];
      }
      return 1;
    },
    maxMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['maxMonth'];
      }
      return 12;
    },
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  watch: {
    getFullscreen() {
      setTimeout(() => {
        this.governRin();
      }, 100);
    },
  },
  mounted() {},
  methods: {
    async initAll() {
      await this.getAllEvaluationIndex();
      await this.initGovernTendency();
      this.governRin();
    },
    onChangeTitle() {
      if (this.activeValue !== 'DrawEcharts') {
        this.initList();
      }
    },
    async onChangeTab(val) {
      if (val === 'DrawEcharts') {
        await this.initGovernTendency();
        await this.governRin();
      } else {
        await this.initList();
      }
    },
    handleClick() {
      this.open = !this.open;
    },
    handleChange(date) {
      this.year = date;
    },
    async handleOk() {
      this.open = false;
      if (this.activeValue === 'DrawEcharts') {
        await this.initGovernTendency();
        await this.governRin();
      } else {
        await this.initList();
      }
    },
    /**
     * 通过indexType获取IndexName
     * @param indexType
     */
    getIndexNameByIndexType(indexType) {
      let keys = Object.keys(this.sourceData);
      for (let j = 0; j < keys.length; j++) {
        let key = keys[j];
        let data = this.sourceData[key];
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          if (item.indexType === indexType) {
            return item.indexName;
          }
          if (indexType === 'SCHEME_INDEX_RATE') {
            return '指标达标率';
          }
        }
      }
    },
    async getAllEvaluationIndex() {
      try {
        this.echartsLoading = true;
        let {
          data: { data },
        } = await this.$http.get(home.getAllEvaluationIndex);
        this.sourceData = data || {};
      } catch (error) {
        this.echartsLoading = false;
      }
    },
    async initGovernTendency() {
      try {
        this.echartsLoading = true;
        let params = {
          years: this.year,
          schemes: this.selectData,
        };
        let {
          data: { data },
        } = await this.$http.post(home.queryGovernanceTrends, params);
        this.tableData = [];
        this.legendList = [];
        Object.keys(data).map((key) => {
          this.tableData.push(data[key]);
          this.legendList.push(this.getIndexNameByIndexType(key));
        });
      } catch (e) {
        // console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    async handleChangeIndex() {
      await this.initGovernTendency();
      await this.governRin();
    },
    governRin() {
      let series = this.tableData.map((item, index) => {
        return {
          name: this.legendList[index],
          type: 'line',
          data: this.tableData[index],
          showSymbol: false,
          smooth: true,
          lineStyle: {
            width: this.$util.common.fontSize(2),
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          areaStyle: {
            //区域填充样式
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: this.colorListGradient[index][0],
                },
                {
                  offset: 1,
                  color: this.colorListGradient[index][1],
                },
              ],
              false,
            ),
            /*shadowColor: 'rgba(10,219,250, 0.5)', //阴影颜色
              shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。*/
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 5,
            shadowOffsetX: 0,
            shadowOffsetXY: 0,
          },
          label: {
            show: false,
            position: 'top',
            color: this.colorList[index % this.colorList.length],
          },
        };
      });
      let opts = {
        data: series,
        dayType: this.dayType,
        year: this.year,
        tooltipFormatter: this.tooltipFormatter,
        gridWidth: this.$refs?.governChart?.$el?.clientWidth,
        legendLength: this.legendList.length || 1,
        tooltipBg: this.commonStyle.tooltipBg,
      };
      this.echartOption = this.$util.doEcharts.baseHomeGovernTendency(opts);
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: calc(100% - 30px);
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
