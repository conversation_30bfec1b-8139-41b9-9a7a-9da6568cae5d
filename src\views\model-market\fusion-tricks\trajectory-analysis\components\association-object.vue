<!--
    * @FileDescription: 关联对象
    * @Author: H
    * @Date: 2022/12/20
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="association" :style="{top: top + 'px'}">
        <div class="title">
            <p>关联对象</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="tree-box">
            <el-tree 
                ref="tree"
                :data="treeList" 
                :highlight-current="true"
                class="tree-modal"
                show-checkbox 
                default-expand-all 
                :expand-on-click-node="false" 
                :current-node-key="resourceType" 
                @node-click="handleNodeClick">
                <div slot-scope="{ data }" class="slot-node">
                    <span class="node-title">
                        <span v-if="data.num > 0">
                            <!-- <i class="iconfont color-bule mr-5" :class="data.catalogName ? (node.expanded ? 'icon-folder-open-fill' : 'icon-folder-fill') : 'icon-file-text-fill'"></i>
                            <span :title="data.catalogName ? data.catalogName : data.resourceNameCn" class="ellipsis name-width">{{ data.catalogName ? data.catalogName : data.resourceNameCn }}</span> -->
                            {{ data.name }} (<span class="num">{{ data.num }}</span>)
                        </span>
                        <span v-else>
                            {{ data.name }}
                        </span>
                        <span class="node-action">
                        </span>
                    </span>
                </div>
            </el-tree>
            <ui-loading v-if="loading"></ui-loading>
        </div>
        <div class="footer">
            <Button type="primary" class="btn" @click="handleTrack">查看轨迹</Button>
        </div>
    </div>
</template>

<script>
import { associatedObjects } from '@/api/modelMarket';
export default {
    name: '',
    components:{
            
    },
    props: {
        marginTop:{
            type: Number,
            default: -40
        }
    },
    data () {
        return {
            treeList: [
                {
                    label: '1',
                    name: '全部',
                    num: 0,
                    children: []
                }
            ], //树数据
            // 关联对象类型
            releType:[ 
                { 'name': '视频身份', 'type': 'vids'},
                { 'name': '车辆', 'type': 'plateNos'},
                { 'name': 'RFID', 'type': 'RFID'},
                { 'name': 'IMSI', 'type': 'IMSI'},
            ],
            resourceType: 0,
            loading: false
        }
    },
    watch:{
            
    },
    computed:{
        top() {
            return this.marginTop;
        }   
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(item, typeIndex){
            if(typeIndex.tab == 1) {
                this.treeList =  [
                    {
                        label: '1',
                        name: '全部',
                        num: 0,
                        children: [
                            {
                                label: 0,
                                name: '车辆',
                                type: 'plateNos',
                                num: 1,
                                children: [
                                    {
                                      label: item.plateNo , 
                                      name: item.plateNo, 
                                      type: 'plateNos' 
                                    }
                                ]
                            }
                        ]
                    }
                ];
            } else {
                this.personnel(item, typeIndex)
            }
        },
        // tab为人员
        personnel(item, typeIndex) {
            this.loading = true;
            this.treeList =  [
                {
                    label: '1',
                    name: '全部',
                    num: 0,
                    children: []
                }
            ];
            let dataType = typeIndex.secTab == 0 ? 1 : 2;
            let params = {
                archiveNo: item.archiveNo,
                dataType: dataType,
            };
            associatedObjects(params)
            .then(res => {
                let number = 0;
                this.releType.forEach((item, index) => {
                    if(res.data[item.type] && res.data[item.type].length > 0) {
                        let children = [];
                        // 第三层数据
                        res.data[item.type].forEach(ite =>{
                            children.push(
                                { label: ite, name: ite, type: item.type}
                            )
                        })
                        // 第二层数据
                        this.treeList[0].children.push({
                            label: index,
                            name: item.name,
                            type: item.type,
                            num: res.data[item.type].length,
                            children: children
                        })
                        let num = res.data[item.type].length;
                        number = num + number;
                    }
                })
                this.treeList[0].num = number;
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleCancel() {
            this.$emit('cancel')
        },  
        handleTrack() {
            let list = this.$refs.tree.getCheckedNodes();
            if(list.length == 0) {
                this.$Message.warning("请选择需要查看轨迹的数据");
                return;
            }
            let typeList = list.filter(item => {
                if(!item.children){
                    return item
                }
            })
            let typeObj = {
                'vids':[], 
                'plateNos':[], 
                'RFID':[], 
                'IMSI':[], 
            };
            typeList.map((item, index) => {
                typeObj[item.type].push(item.label)
            })
            this.$emit('viewTrack', typeObj)
        },
        handleNodeClick() {

        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.association{
    width: 370px;
    height: 391px;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    position: absolute;
    left: 380px;
    .tree-box{
        padding:10px 0 22px 30px;
        .tree-modal{
            height: 271px;
            overflow-y: auto;
            .node-title{
                font-size: 14px;
                font-weight: bold;
                color: rgba(0,0,0,0.9);
                .num{
                    color: #2C86F8;
                }
            }
        }
    }
    .footer{
        padding: 0 20px;
        .btn{
            width: 100%;
        }
    }
}
</style>
