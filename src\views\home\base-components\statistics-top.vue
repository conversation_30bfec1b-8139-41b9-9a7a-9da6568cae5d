<template>
  <div class="statistics-container" :class="getFullscreen ? 'full-screen-container' : ''">
    <div
      class="statistics-wrapper"
      v-for="(item, index) in queryAccessData"
      :key="index"
      :class="{ 'pointer': item?.tooltipShow }"
    >
      <div class="statistics-image">
        <img class="dock" src="@/assets/img/base-home/statistics/dizuo.png" />
        <img class="shine" src="@/assets/img/base-home/statistics/bg-shine.png" />
        <img class="image" :src="item.img" />
        <div class="bubbles"></div>
      </div>
      <div class="statistics-item">
        <p class="count ellipsis color f-22 f-b">
          <countTo :startVal="0" :endVal="item[item.key] || 0" :duration="3000"></countTo>
        </p>
        <p class="name f-14 ellipsis">{{ item.label }}</p>
      </div>
      <div class="statistics-wrapper-tip" v-if="item?.tooltipShow">
        <slot :name="`tipslot${index}`"></slot>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'statistics-top',
  components: {
    countTo: require('vue-count-to').default,
  },
  props: {
    queryAccessData: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {},
  filter: {},
  mounted() {
    this.bubbles();
  },
  methods: {
    bubbles() {
      document.querySelectorAll('.bubbles').forEach((element) => {
        let bubblecount = (element.offsetWidth / 50) * 10;
        for (let i = 0; i <= bubblecount; i++) {
          let size = this.rnd(20, 40) / 10;
          let particle = document.createElement('span');
          particle.classList.add('particle');
          particle.style.top = this.rnd(20, 80) + '%';
          particle.style.left = this.rnd(0, 95) + '%';
          particle.style.width = size + 'px';
          particle.style.height = size + 'px';
          particle.style.animationDelay = this.rnd(0, 30) / 10 + 's';
          element.appendChild(particle);
        }
      });
    },
    rnd(m, n) {
      m = parseInt(m);
      n = parseInt(n);
      return Math.floor(Math.random() * (n - m + 1)) + m;
    },
  },
};
</script>

<style lang="less" scoped>
.fs34 {
  font-size: 34px;
}
.f-22 {
  font-size: 22px;
}
.f-b {
  font-weight: bold;
}

.statistics-container {
  width: 900px;
  position: absolute;
  left: 51%;
  top: -10px;
  display: flex;
  transform: translateX(-50%);
  z-index: 3;
  .statistics-wrapper {
    position: relative;
    // overflow: hidden;
    display: flex;
    flex: 1;
    height: 92px;
    .statistics-wrapper-tip {
      position: absolute;
      top: 80px;
      left: 100px;
      display: none;
    }

    &:hover {
      .statistics-wrapper-tip {
        display: block;
      }
    }

    &:not(&:last-child) {
      margin-right: 10px;
    }
    .statistics-item {
      position: relative;
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      .color {
        background: linear-gradient(180deg, #45e1fb 0%, #ffffff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      .count {
        span {
          text-shadow: 0 0 10px rgba(14, 200, 242, 0.6);
        }
      }
      .name {
        color: #e1f2fd;
        font-size: 14px;
      }
    }
    .statistics-image {
      position: relative;
      width: 100px;
      height: 100px;
      /* background: url('~@/assets/img/base-home/statistics/dizuo.png') no-repeat;
      background-position-x: center;
      background-position-y: 25px;*/
      .dock {
        height: 53px;
        width: 77px;
        position: absolute;
        top: 30%;
        left: 12%;
      }
      .shine {
        height: 40px;
        width: 42px;
        //transform: translate(27px, 18px);
        position: absolute;
        left: 50%; /* 定位父级的50% */
        top: 40%;
        transform: translate(-50%, -50%); /*自己的50% */
        animation: shine 2s ease-in infinite;
      }
      .image {
        position: absolute;
        left: 50%; /* 定位父级的50% */
        top: 25%;
        transform: translate(-50%, -50%); /*自己的50% */
        animation: jump 3s ease-in infinite;
        height: 60px;
      }
      .bubbles {
        position: absolute;
        left: 50%; /* 定位父级的50% */
        top: 45%;
        transform: translate(-50%, -50%); /*自己的50% */
        width: 40px;
        height: 40px;

        @{_deep} .particle {
          opacity: 0;
          position: absolute;
          background-color: #ffffff;
          animation: bubbles 3s ease-in infinite;
          border-radius: 100%;
        }
      }
    }
  }
  @keyframes jump {
    0% {
      transform: translate(-50%, -50%);
    }
    50% {
      transform: translate(-50%, -40%);
    }
    100% {
      transform: translate(-50%, -50%);
    }
  }

  @keyframes bubbles {
    0% {
      opacity: 0;
    }
    20% {
      opacity: 1;
      transform: translate(0, -20%);
    }
    100% {
      opacity: 0;
      transform: translate(0, -500%);
    }
  }
  @keyframes shine {
    0% {
      opacity: 0.1;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.1;
    }
  }
}
.full-screen-container {
  top: 6.5%;
}
</style>
