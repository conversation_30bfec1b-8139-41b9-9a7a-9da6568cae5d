<template>
  <div>
    <ui-modal ref="modal" title="不合格详情" :styles="styles" footer-hide>
      <div class="content" v-if="this.obj != '{}'">
        <div class="echarts-title" v-if="row.areaImage">
          <i></i>
          <div class="titles">位置图片信息</div>
        </div>
        <div class="img">
          <img :src="this.row.areaImage" alt="" v-if="row.areaImage" />
          <!-- <img src="@/assets/img/load-error-img.png" alt="" v-else style="width:150px" /> -->
        </div>
        <div class="echarts-title" v-if="row.dateImage">
          <i></i>
          <div class="titles">时间图片信息</div>
        </div>
        <div class="img">
          <img :src="this.row.dateImage" alt="" v-if="row.dateImage" />
          <!-- <img src="@/assets/img/load-error-img.png" alt="" v-else style="width:150px" /> -->
        </div>
        <div class="echarts-title" v-if="row.additionalImage">
          <i></i>
          <div class="titles" v-if="row.additionalImage">
            <span class="sp-title">附加图片信息</span>
          </div>
        </div>
        <div class="img" style="display: flex">
          <img :src="this.row.additionalImage" alt="" v-if="row.additionalImage" />
          <!-- <img src="@/assets/img/load-error-img.png" alt="" v-else style="width:150px" /> -->
          <!-- <div class="text" style="margin-left:50px">
            {{ JSON.parse(this.row.additionalImageText) }}
          </div> -->
        </div>
        <div class="echarts-title" v-if="row.screenShot">
          <i></i>
          <div class="titles">大图信息</div>
        </div>
        <div class="img big">
          <img :src="this.row.screenShot" alt="" class="big_img" v-if="row.screenShot" />
          <!-- <img src="@/assets/img/load-error-img.png" alt="" v-else style="width:150px" /> -->
        </div>
      </div>
      <div class="content" v-else>
        <div class="no-data">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
export default {
  data() {
    return {
      styles: {
        width: '80%',
      },
      row: {},
      obj: {},
      name: {},
    };
  },
  methods: {
    showModal(row) {
      this.row = row;
      this.name = JSON.parse(this.row.additionalImageText);
      this.$refs.modal.modalShow = true;
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  height: 800px;
  overflow-y: auto;
}
.echarts-title {
  width: 100%;
  display: flex;
  margin-top: 5px;
  height: 30px;
  line-height: 30px;
  margin-bottom: 5px;
  i {
    width: 8px;
    height: 100%;
    background: #239df9;
    margin-right: 6px;
  }
  .titles {
    display: flex;
    width: 100%;
    padding-left: 5px;
    background-image: linear-gradient(to right, #0a4f8d, #09284d);
    .text {
      margin-left: 100px;
      width: 1000px;
      display: inline-block;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
.img {
  width: 800px;
  img {
    width: 100%;
  }
}
.big {
  width: 100%;
}
</style>
