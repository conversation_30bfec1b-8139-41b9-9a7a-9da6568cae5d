/*
 * @Author: duans<PERSON>
 * @Date: 2024-08-26 19:17:21
 * @LastEditors: duansen
 * @LastEditTime: 2024-08-27 10:30:48
 * @Description: 
 */
import BasicLayout from "@/layouts/basic-layout";
export default [
  {
    path: "user",
    name: "user",
    component: BasicLayout,
    children: [
      {
        path: "/user/collect",
        name: "collect",
        tabShow: true,
        parentName: "user-collect",
        meta: {
          title: "我的收藏",
          icon: "",
        },
        text: "我的收藏",
        component: (resolve) =>
          require(["@/views/user/collect/index.vue"], resolve),
      },
      {
        path: "/user/information",
        name: "userCenter",
        tabShow: true,
        component: (resolve) =>
          require(["@/views/userCenter/information/index.vue"], resolve),
        meta: {
          title: "个人中心",
        },
      },
      {
        path: "/user/mydownload",
        name: "mydownload",
        tabShow: true,
        component: (resolve) =>
          require(["@/views/video-application/my-download/index.vue"], resolve),
        meta: {
          title: "下载审核",
        },
      },
      {
        path: "/user/myTargetControl",
        name: "myTargetControl",
        tabShow: true,
        component: (resolve) =>
          require([
            "@/views/target-control/my-target-control/index.vue",
          ], resolve),
        meta: {
          title: "布控审核",
        },
      },
      {
        path: "/user/mySubStructuration",
        name: "mySubStructuration",
        tabShow: true,
        component: (resolve) =>
          require([
            "@/views/viewanalysis/mySubStructuration/index.vue",
          ], resolve),
        meta: {
          title: "解析审核",
        },
      },
      {
        path: "/user/myApply",
        name: "myApply",
        tabShow: true,
        component: (resolve) =>
          require(["@/views/user/apply/index.vue"], resolve),
        meta: {
          title: "我的申请",
        },
      },
    ],
  },
];
