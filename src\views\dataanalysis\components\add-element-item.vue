<template>
  <div class="add-box">
    <div class="item-box">
      <span class="item-title">{{ title }}</span>
      <span v-for="(item, index) in selectList" :key="index" class="add-item">
        {{ item.dataValue }}
        <img class="off-icon" src="~@/assets/img/dataanalysis/close.png" alt="" @click="offFn(index)" />
      </span>
    </div>
    <Poptip
      v-model="showDropdown"
      transfer
      placement="right"
      transfer-class-name="add-dropdown add-dropdown-box"
      width="255"
      @on-popper-show="addFn"
    >
      <span class="pointer add-icon">+影响因素</span>
      <template #content>
        <CheckboxGroup v-model="currentCheck" class="check-box">
          <Checkbox v-for="(item, index) in getFactorList" :label="item.dataKey" :disabled="item.disabled" :key="index">
            {{ item.dataValue }}
          </Checkbox>
        </CheckboxGroup>
        <div class="tip-text" v-if="currentCheck.length > 3 && currentMode === 'group'">
          组合分析：不能超过3项，请重新选择
        </div>
        <div class="btn-footer">
          <!-- <Button class="mr-sm" @click="onSubmit('alone')">独立分析</Button>
          <Button @click="onSubmit('group')">组合分析</Button> -->
          <Button class="mr-sm" @click="hideFn">取消</Button>
          <Button type="primary" class="mr-sm" @click="onSubmit('alone')">确定</Button>
        </div>
      </template>
    </Poptip>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    // 对应的类型
    typeKey: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showDropdown: false,
      selectValue: [], // 已选择列表中项的value的集合
      selectList: [], // 已选择列表
      currentCheck: [],
      currentMode: '', // alone: 独立分析    group: 组合分析
      factorList: [],
    };
  },
  computed: {
    ...mapGetters({
      influenceList: 'dataAnalysis/getInfluenceList',
    }),
    // 不同类型 不同的显示列表
    getFactorList() {
      let typesArr = this.typeKey.split(',');
      return this.factorList.filter((item) => {
        let types = item.typeKey.split(',');
        return types.some((key) => typesArr.includes(key));
      });
    },
  },
  created() {
    if (this.influenceList.length === 0) {
      this.setInfluenceInfo();
    }
  },
  watch: {
    influenceList: {
      handler(val) {
        this.factorList = this.$util.common.deepCopy(val);
      },
      immediate: true,
      deep: true,
    },
    list: {
      handler(arr) {
        let { selectList, selectValue } = this.handlerSelectList(arr);
        this.selectList = selectList;
        this.selectValue = selectValue;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapActions({
      setInfluenceInfo: 'dataAnalysis/getInfluenceInfo',
    }),
    hideFn() {
      this.showDropdown = false;
    },
    offFn(index) {
      this.selectList.splice(index, 1);
      this.selectValue.splice(index, 1);
      this.$emit('changeSelectItem', this.selectValue);
    },
    addFn() {
      this.showDropdown = true;
      this.currentCheck = [];
      this.currentMode = '';
    },
    /**
     * 处理 已选择列表
     * @param {Array} arr   下拉列表factorList的value值 的集合   如： ['1', '2', '3,4,5']
     * @description 最终 组合成 [{ dataKey: '1', dataValue: '设备厂商xx' },{ dataKey: '2,3,4', dataValue: 'aaa+bbb+ccc' }]
     */
    handlerSelectList(arr) {
      let resArr = [];
      arr.forEach((val) => {
        let valueArr = val.split(',');
        let selectArr = this.factorList.filter((item) => valueArr.includes(item.dataKey));
        if (valueArr.length === 1 && selectArr.length > 0) {
          resArr.push(selectArr[0]);
        } else {
          resArr.push({
            dataKey: val,
            dataValue: selectArr.map((item) => item.dataValue).join('+'),
          });
        }
      });
      return {
        selectList: this.$util.common.deepCopy(resArr),
        selectValue: [...arr],
      };
    },
    // 注意：不要重复添加
    onSubmit(str = '') {
      this.currentMode = str;
      let diffCheck = [];
      if (str === 'alone') {
        // 独立分析
        diffCheck = this.currentCheck.filter((item) => !this.selectValue.includes(item));
      } else {
        // 组合分析
        if (this.currentCheck.length > 3) {
          this.$Message.error('不能超过3项，请重新选择');
          return;
        }
        // 已选择列表selectValue的每一项中，都存在 与  当前勾选列表currentCheck 不同的值，才可以添加
        let res = [];
        this.selectValue.forEach((item, index) => {
          let itemArr = item.split(',');
          if (itemArr.length === this.currentCheck.length) {
            res[index] = this.currentCheck.some((checkItem) => !itemArr.includes(checkItem));
          } else {
            res[index] = true;
          }
        });
        let flag = res.every((item) => item === true);
        if (flag) {
          diffCheck = [this.currentCheck.join(',')];
        }
      }
      let { selectList, selectValue } = this.handlerSelectList([...this.selectValue, ...diffCheck]);
      this.selectList = selectList;
      this.selectValue = selectValue;
      this.$emit('changeSelectItem', this.selectValue);
      this.hideFn();
    },
  },
};
</script>

<style lang="less" scoped>
.add-box {
  display: flex;
  flex: 1;
  font-size: 14px;
  .item-box {
    background: var(--bg-form-item);
    max-width: calc(100% - 137px);
    padding: 0 20px;
    margin-right: 20px;
    display: flex;
    flex-wrap: wrap;
    .item-title {
      display: inline-block;
      margin: 9px 40px 0 0;
      color: var(--color-content);
    }
    .add-item {
      position: relative;
      background: var(--bg-tag);
      display: inline-block;
      border-radius: 4px;
      margin: 8px 16px 8px 0;
      padding: 1px 17px 1px 10px;
      border-radius: 4px;
      color: #ffffff;
      height: 34px;
      .off-icon {
        position: absolute;
        top: -5px;
        right: -8px;
        cursor: pointer;
        width: 19px;
        height: 19px;
      }
    }
  }
}
.add-icon {
  display: inline-block;
  background: var(--bg-form-item);
  color: var(--color-btn-default);
  width: 117px;
  text-align: center;
  line-height: 50px;
}

.add-dropdown-box {
  height: 100%;
  position: relative;
  .check-box {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    line-height: 32px;
    @{_deep}.ivu-checkbox-wrapper {
      min-width: 90px !important;
    }
  }
  .btn-footer {
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .tip-text {
    font-size: 12px;
    color: #ed4014;
  }
}
</style>
