<template>
  <!-- 下级地市情况 -->
  <div class="subordinate-chart">
    <div class="ranking-title f-14">
      <span class="icon-font icon-xiajidishiqingkuang mr-xs"></span>
      <span>下级地市情况</span>
      <Checkbox v-model="sortField" class="fr" @on-change="onChangeSortField">
        <slot name="rank-title">按稳定性排名</slot>
      </Checkbox>
    </div>
    <div class="echarts-box">
      <draw-echarts
        v-if="rankInfoList.length"
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="rankInfoList.length > 20">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('propertyChart', rankInfoList, [], 20)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import dataZoom from '@/mixins/data-zoom';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'subordinate-chart',
  mixins: [dataZoom, dealWatch],
  data() {
    return {
      propertyEchart: {},
      echartsLoading: false,
      echartData: [[], [], []],
      colorList: ['rgba(22, 174, 22, 1)', 'rgba(213, 94, 41, 1)'],
      lengName: [],
      rankList: [],
      echartList: [],
      xAxis: [],
      series: [],
      sortField: false,
      rankInfoLoading: false,
      rankInfoList: [],
    };
  },
  props: {
    activeIndexItem: {},
  },
  mounted() {
    this.getNumRankInfo();
    this.getRankInfo();
    this.typeRing();
  },
  methods: {
    onChangeSortField() {
      this.getNumRankInfo();
      this.getRankInfo();
      this.typeRing();
    },
    getRankInfo() {
      this.rankList = [
        {
          'regionCode': '23030000',
          'regionName': '鸡西市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 1,
          'rankRise': '0',
          'rankType': 'SAME',
        },
        {
          'regionCode': '23110000',
          'regionName': '黑河市公安局',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 2,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23040000',
          'regionName': '鹤岗市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 3,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23080000',
          'regionName': '佳木斯市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 4,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23090000',
          'regionName': '七台河市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 5,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23270000',
          'regionName': '大兴安岭地区',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 6,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23060000',
          'regionName': '大庆市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 7,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23070000',
          'regionName': '伊春市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 8,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23120000',
          'regionName': '绥化市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 9,
          'rankRise': '0',
          'rankType': 'SAME',
        },
        {
          'regionCode': '23100000',
          'regionName': '牡丹江',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 10,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23010000',
          'regionName': '哈尔滨',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 11,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23050000',
          'regionName': '双鸭山市',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 12,
          'rankRise': '0',
          'rankType': 'RISE',
        },
        {
          'regionCode': '23020000',
          'regionName': '齐齐哈尔市公安局',
          'standardsValue': 0,
          'dataType': null,
          'dataTypeDesc': null,
          'rank': 13,
          'rankRise': '0',
          'rankType': 'RISE',
        },
      ];
      // this.rankLoading = true
      // const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query
      // let data = {
      //   indexId: indexId,
      //   batchId: batchId,
      //   access: access || 'REPORT_MODE',
      //   displayType: statisticType,
      //   orgRegionCode:  statisticType === 'REGION' ? regionCode : orgCode,
      //   sortField: this.sortField ? 'RESULT_VALUE' : null,
      // }
      // try {
      //   let res = await this.$http.post(evaluationoverview.getRankInfo, data)
      //   this.rankList = res.data.data || []
      // } catch (err) {
      //   console.log(err)
      // } finally {
      //   this.rankLoading = false
      // }
    },
    async getNumRankInfo() {
      this.rankInfoList = [
        {
          'regionCode': '23030000',
          'regionName': '鸡西市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23110000',
          'regionName': '黑河市公安局',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23040000',
          'regionName': '鹤岗市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23080000',
          'regionName': '佳木斯市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23090000',
          'regionName': '七台河市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23270000',
          'regionName': '大兴安岭地区',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23060000',
          'regionName': '大庆市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23070000',
          'regionName': '伊春市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23120000',
          'regionName': '绥化市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23100000',
          'regionName': '牡丹江',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23010000',
          'regionName': '哈尔滨',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23050000',
          'regionName': '双鸭山市',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
        {
          'regionCode': '23020000',
          'regionName': '齐齐哈尔市公安局',
          'standardsValue': null,
          'dataType': 'CITY',
          'dataTypeDesc': null,
          'actualNum': 0,
          'qualifiedNum': 0,
          'unqualifiedNum': 0,
        },
      ];
      // this.rankInfoLoading = true
      // const {regionCode, orgCode, statisticType, indexId, access, batchId} = this.$route.query
      // let data = {
      //   indexId: indexId,
      //   batchId: batchId,
      //   access: access || 'REPORT_MODE',
      //   displayType: statisticType,
      //   orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      //   sortField: this.sortField ? 'RESULT_VALUE' : null,
      // }
      // try {
      //   let res = await this.$http.post(governanceevaluation.getNumRankInfo, data)
      //   this.rankInfoList = res.data.data || []
      // } catch (err) {
      //   console.log(err)
      // } finally {
      //   this.rankInfoLoading = false
      // }
    },
    typeRing() {
      this.echartData = [];
      let qualifiedNum = [];
      let unqualifiedNum = [];
      this.lengName = ['不合格次数', '合格次数'];

      this.rankInfoList.forEach((item) => {
        qualifiedNum.push(item.qualifiedNum || 0);
        unqualifiedNum.push(item.unqualifiedNum || 0);
      });

      let series = [
        {
          name: '合格次数',
          data: qualifiedNum,
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[0] },
        },
        {
          name: '不合格次数',
          data: unqualifiedNum,
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[1] },
        },
      ];
      let line = [
        {
          name: this.activeIndexItem.indexName,
          type: 'line',
          itemStyle: {
            color: 'rgba(184, 241, 98, 1)',
          },
          yAxisIndex: 1,
          data: this.rankList.map((item) => item.standardsValue),
        },
      ];
      let opts = {
        xAxis: this.rankInfoList.map((item) => item.regionName),
        series: [...series, ...line],
        lengName: [...this.lengName, this.activeIndexItem.indexName],
      };
      this.propertyEchart = this.$util.doEcharts.ReviewConsequenceEcharts(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], 20);
      });
    },
    getDataType() {
      /**
       *  NONE("-1","默认值"),
       *     PROVINCE("1","省"),
       *     CITY("2","市"),
       *     COUNTY("3","区县");
       */
      if (this.rankInfoList.length > 0) {
        let { dataType } = this.rankInfoList[0];
        switch (dataType) {
          case 'PROVINCE':
          case 'CITY':
            return '地市';
          case 'COUNTY':
            return '区县';
          default:
            return '--';
        }
      }
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.subordinate-chart {
  position: relative;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);

  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }

  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;

    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}

.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }

  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }

  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
