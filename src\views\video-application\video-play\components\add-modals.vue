<template>
  <ui-modal
    v-model="visible"
    :title="type === 'add' ? '新增分组' : '编辑'"
    :r-width="1600"
    :loading="modalLoading"
    @onOk="comfirmHandle"
    @onCancel="onCancel"
  >
    <div class="content">
      <Form ref="form" :model="form" :rules="ruleForm" class="form">
        <FormItem label="分组名称" :label-width="80" prop="groupName">
          <Input
            v-model="form.groupName"
            :maxlength="20"
            placeholder="请输入"
          />
        </FormItem>
      </Form>
      <div class="content-bottom">
        <div class="select_left">
          <div class="select-bottom">
            <div class="tree-list">
              <!-- <xn-tree class="deviceTree" :ref="'tree'" :option="option" :label="labelFn" @clickNode="clickNode"></xn-tree> -->
              <el-tree
                highlight-current
                ref="tree"
                :props="treeProps"
                lazy
                :load="loadNode"
                node-key="id"
                draggable
                :expand-on-click-node="false"
              >
                <span
                  @click="handleNodeClick(data)"
                  class="custom-tree-node"
                  slot-scope="{ node, data }"
                  :key="data.id"
                >
                  <template>
                    <span class="label">
                      <i class="iconfont icon-fenju"></i>
                      {{ node.data.orgName }}
                      <!-- ({{ node.data.allTotal }}) -->
                    </span>
                  </template>
                </span>
              </el-tree>
              <ui-loading v-if="loading"></ui-loading>
            </div>
            <div class="table-box">
              <Form
                inline
                ref="formData"
                :model="formData"
                class="form"
                @submit.native.prevent
              >
                <Row>
                  <Col span="6">
                    <FormItem label="设备名称:" prop="deviceName">
                      <Input
                        v-model="formData.deviceName"
                        size="small"
                        placeholder="请输入"
                      ></Input>
                    </FormItem>
                  </Col>
                  <!-- <Col span="6">
                                        <FormItem label="设备编码:" prop="deviceId">
                                            <Input v-model="formData.deviceId" size="small" placeholder="请输入"></Input>
                                        </FormItem>
                                    </Col> -->
                  <!-- <Col span="6">
                                        <FormItem label="安装地址:" prop="detailAddress">
                                            <Input v-model="formData.detailAddress" size="small" placeholder="请输入"></Input>
                                        </FormItem>
                                    </Col> -->
                  <Col span="4">
                    <FormItem>
                      <Button class="find" type="primary" @click="handleQuery"
                        >查询</Button
                      >
                      <Button type="default" @click="resetForm">重置</Button>
                    </FormItem>
                  </Col>
                </Row>
              </Form>
              <div class="checkall" v-if="checkShow">
                <Checkbox
                  v-model="selectAllCheck"
                  @on-change="handleChangeCheck"
                  >全部</Checkbox
                >
              </div>
              <Table
                class="auto-fill table"
                ref="table"
                :height="checkShow ? 449 : 480"
                :columns="columns"
                :data="tableData"
                :loading="tableLoading"
                @on-select="onSelect"
                @on-select-cancel="onSelectCancel"
                @on-select-all="onSelectAll"
                @on-select-all-cancel="onSelectAllCancel"
              >
                <template #loading>
                  <ui-loading></ui-loading>
                </template>
                <!-- <template slot="deviceId" slot-scope="{ row }">
                                    <span class="link-btn cursor-p" @click="deviceArchives(row)">{{ row.deviceId }}</span>
                                </template> -->
                <template slot="sbgnlx" slot-scope="{ row }">
                  <span>{{ getGnlx(row.sbgnlx) }}</span>
                </template>
                <template #labels="{ row }">
                  <ui-tag-poptip
                    v-if="row.labels && row.labels.length"
                    :data="row.labels"
                  />
                </template>
              </Table>
              <ui-page
                :current="pageInfo.pageNumber"
                :total="pageTotal"
                :page-size="pageInfo.pageSize"
                @pageChange="pageChange"
                @pageSizeChange="pageSizeChange"
              >
              </ui-page>
            </div>
          </div>
        </div>
        <div class="show-right">
          <div class="right-header">
            <p>
              已选择:
              <span class="bule-color">{{ selectTableData.length }}</span
              >设备
            </p>
            <span class="deleAll bule-color" @click="handleDele">
              <i class="iconfont icon-shanchu"></i>清空全部
            </span>
            <!-- <p class="deleAll bule-color" @click="handleDele">删除全部</p> -->
          </div>
          <div class="device-list">
            <!-- <div>
                            <p></p>
                            <i class="iconfont color-white icon-shanchu ml-10" @click="remove(data)"></i>
                        </div> -->
            <ul>
              <li v-for="(item, index) in selectTableData" :key="index">
                <Checkbox
                  v-model="item.select"
                  @on-change="selectChange(item, index)"
                  >{{ item.deviceName }}</Checkbox
                >
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import {
  queryDeviceOrgTree,
  addMyVideoGroup,
  updateMyVideoGroup,
  deviceDirectoryTree,
  queryVideoDeviceGroupList,
} from "@/api/player";
import xnTree from "@/components/xn-tree/index.vue";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
export default {
  data() {
    return {
      type: "add", //新增/编辑
      visible: false,
      form: {
        groupName: "",
      },
      ruleForm: {
        //表单分组名称校验
        groupName: [
          { required: true, message: "请输入分组名称", trigger: "blur" },
        ],
      },
      treeData: [],
      loading: false,
      tableLoading: false,
      modalLoading: false,
      total: 0,
      playingDeviceIds: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageTotal: 0,
      columns: [
        {
          title: "选择",
          width: 65,
          align: "center",
          type: "selection",
          key: "index",
        },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "功能类型", slot: "sbgnlx" },
        { title: "设备名称", key: "deviceName" },
        { title: "设备编码", key: "deviceGbId" },
        // { title: '安装地址', key: 'detailAddress' },
      ],
      tableData: [],
      formData: {
        deviceName: "",
        deviceId: "",
        detailAddress: "",
        orgCodes: [],
      },
      selectAllCheck: false,
      checkShow: false,
      treeProps: {
        label: "orgName",
        isLeaf: "isLeaf",
        children: "children",
      },
      node: null,
      resolveFunc: null,
      selectTableData: [],
    };
  },
  components: {
    xnTree,
  },
  async created() {
    await this.getDictData();
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      sbgnlxList: "dictionary/getSbgnlxList", //摄像机功能类型
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 初始化
    show(item) {
      this.type = item ? "edit" : "add";
      this.visible = true;
      // 默认进入 查询设备
      this.handleQuery();
      this.$nextTick(() => {
        // this.getParentData()
        this.$refs.form.resetFields();
        if (this.type === "edit") {
          this.tableLoading = true;
          queryVideoDeviceGroupList({ groupId: item.id })
            .then((res) => {
              res.data.forEach((item) => {
                item.select = true;
                this.selectTableData.push(item);
              });
            })
            .finally(() => {
              this.tableLoading = false;
            });
          // 编辑字段回显
          this.form = {
            groupName: item.groupName,
            id: item.id,
          };
        }
      });
    },
    handleNodeClick(data) {
      this.checkShow = true;
      this.selectAllCheck = false;
      this.formData.orgCodes = [data.orgCode];
      this.queryList();
    },
    queryList() {
      this.tableData = [];
      this.tableLoading = true;
      var param = {
        sbgnlxs: [],
        filter: this.judgeUser,
        deviceType: 1,
      };
      queryDeviceList({ ...this.formData, ...this.pageInfo, ...param })
        .then((res) => {
          const { total, entities } = res.data;
          this.pageTotal = total;
          this.tableData = entities;
          this.comparisonList();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    loadNode(node, resolve) {
      // 加载子树数据的方法
      if (node.level === 0) {
        this.node = node;
        this.resolveFunc = resolve;
        deviceDirectoryTree({
          orgCode: "",
          filter: this.judgeUser,
          deviceType: 1,
        }).then((res) => {
          if (res.code === 200) {
            let list = res.data.deviceOrgList.filter((item) => {
              if (item.allTotal > 0) {
                return item;
              }
            });
            resolve(list);
          }
        });
      } else {
        deviceDirectoryTree({
          orgCode: node.data.orgCode,
          filter: this.judgeUser,
          deviceType: 1,
        }).then((res) => {
          if (res.code === 200) {
            let list = res.data.deviceOrgList.filter((item) => {
              if (item.allTotal > 0) {
                return item;
              }
            });
            resolve(list);
          }
        });
      }
    },
    // 确认提交
    comfirmHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let deviceIds = [];
          this.selectTableData.forEach((item) => {
            deviceIds.push(item.deviceId);
          });
          this.modalLoading = true;
          // 新增
          if (this.type === "add") {
            addMyVideoGroup({
              userId: this.userInfo.id,
              groupName: this.form.groupName,
              deviceIds: deviceIds,
            })
              .then((res) => {
                if (res.data.code && res.data.code == 500) {
                  this.$Message.warning(res.data.msg);
                } else {
                  this.visible = false;
                  this.$Message.success(res.msg);
                  this.$emit("refreshDataList");
                  this.onCancel();
                }
              })
              .finally(() => {
                this.modalLoading = false;
              });
          } else {
            //编辑
            updateMyVideoGroup({
              groupName: this.form.groupName,
              id: this.form.id,
              deviceIds: deviceIds,
            })
              .then((res) => {
                if (res.data.code && res.data.code == 500) {
                  this.$Message.warning(res.data.msg);
                } else {
                  this.visible = false;
                  this.$Message.success(res.msg);
                  this.$emit("refreshDataList");
                  this.onCancel();
                }
              })
              .finally(() => {
                this.modalLoading = false;
              });
          }
        }
      });
    },
    onCancel() {
      this.tableData = [];
      this.handleDele();
    },
    handleQuery() {
      this.queryList();
    },
    resetForm() {
      this.formData.deviceName = "";
      this.formData.deviceId = "";
      this.formData.detailAddress = "";
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.queryList();
    },
    handleDele() {
      this.selectTableData = [];
      this.selectAllCheck = false;
      this.$refs.table.selectAll(false);
    },
    selectChange(row, index) {
      var obj = this.$refs.table.objData;
      if (row.select) {
        // 选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = true;
          }
        });
      } else {
        // 取消选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = false;
          }
        });
      }
      this.$nextTick(() => {
        this.selectTableData.splice(index, 1);
      });
    },
    // 对比当前已选数据
    comparisonList() {
      this.$nextTick(() => {
        var obj = this.$refs.table.objData;
        let selectList = new Map(
          this.selectTableData.map((item) => [item.deviceId, item])
        );
        Object.keys(obj).forEach((key) => {
          if (selectList.get(obj[key].deviceId)) {
            obj[key]._isChecked = true;
          }
        });
      });
    },
    handleChangeCheck(val) {
      if (!val) {
        this.handleDele();
        return;
      }
      if (this.selectTableData.length == 5000) {
        this.selectAllCheck = false;
        this.$Message.warning("单次选择最大数量不能大于5000，请重新选择");
        return;
      }
      this.tableLoading = true;
      var param = {
        deviceType: 1,
        sbgnlxs: [],
        filter: this.judgeUser,
      };
      queryDeviceList({
        ...this.formData,
        pageNumber: 1,
        pageSize: 5000,
        ...param,
      })
        .then((res) => {
          const { total, entities } = res.data;
          if (this.selectTableData.length + entities.length > 5000) {
            this.selectAllCheck = false;
            this.$Message.warning("单次选择最大数量不能大于5000，请重新选择");
          } else {
            entities.forEach((item) => {
              item.select = true;
              var obj = this.selectTableData.find((itm) => {
                return itm.deviceId == item.deviceId;
              });
              if (!obj) {
                this.selectTableData.push(item);
              }
            });
            this.comparisonList();
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    onSelect(selection, row) {
      var obj = this.selectTableData.find((item) => {
        return item.deviceId == row.deviceId;
      });
      row.select = true;
      if (!obj) {
        this.selectTableData.push(row);
      } else {
        obj.select = true;
      }
    },
    onSelectCancel(selection, row) {
      var num = this.selectTableData.findIndex((item) => {
        return item.deviceId == row.deviceId;
      });
      this.selectTableData.splice(num, 1);
    },
    // 全选
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        var obj = this.selectTableData.find((itm) => {
          return itm.deviceId == item.deviceId;
        });
        if (!obj) {
          this.selectTableData.push(item);
        }
      });
    },
    // 取消全选
    onSelectAllCancel(selection) {
      this.tableData.forEach((item) => {
        var num = this.selectTableData.findIndex((itm) => {
          return itm.deviceId == item.deviceId;
        });
        if (num != -1) {
          this.selectTableData.splice(num, 1);
        }
      });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    getGnlx(str) {
      // str 可能为空
      if (!str) return "";
      var arr = str.split("/");
      var gnlx = "";
      arr.forEach((item) => {
        var row = this.sbgnlxList.find((i) => i.dataKey == item);
        gnlx += row.dataValue + " / ";
      });
      return gnlx.substring(0, gnlx.length - 2);
    },
    // 表单重置
    resertForm() {
      this.$refs.form.resetFields();
      this.$refs.formData.resetFields();
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ivu-modal-body {
  max-height: none !important;
  height: 720px;
}
.dialog-input {
  width: 330px;
}
// .form {
//   height: 200px;
// }
.content {
  border: 1px solid #d3d7de;
  // padding: 10px 0;
  height: 100%;
}
.content-bottom {
  display: flex;
  // height: 680px;
  border-top: 1px solid #d3d7de;
  height: calc(~"100% - 64px");
  .select_left {
    flex: 1;
    .select-bottom {
      display: flex;
      height: 100%;
      .tree-list {
        position: relative;
        overflow-y: auto;
        height: 580px;
        width: 300px;
        // margin-right: 10px;
        margin-top: 10px;
      }
      .table-box {
        display: flex;
        flex-direction: column;
        flex: 1;
        border-left: 1px solid #d3d7de;
        border-right: 1px solid #d3d7de;
        padding: 0px 10px 0 10px;
        position: relative;
        .checkall {
          margin-bottom: 10px;
        }
      }
    }
  }
  .show-right {
    width: 300px;
    padding: 10px;
    // margin-left: 10px;
    background: #f6f7f8;
    .right-header {
      display: flex;
      justify-content: space-between;
      .bule-color {
        color: #2c86f8;
      }
      .deleAll {
        cursor: pointer;
      }
    }
    .device-list {
      overflow-y: scroll;
      height: calc(~" 100% - 24px");
      position: relative;
    }
  }
}
.find {
  margin-right: 10px;
}
.form {
  width: 100%;
  padding: 10px 16px;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  // /deep/ .ivu-input {
  //   width: 125px;
  // }
  /deep/.ivu-select {
    width: 100%;
  }
  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    .ivu-form-item-content {
      flex: 1;
      .ivu-select {
        width: 100%;
      }
    }
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
  // /deep/ .ivu-form-item-content{
  //   float: left;
  // }
  // .btn-group {
  //   margin-right: 0;
  // }
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-size: 14px;
    color: #000;
    i {
      color: #23a8f9;
      margin-right: 10px;
    }
  }
}
</style>
