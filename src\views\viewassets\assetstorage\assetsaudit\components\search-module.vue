<template>
  <div>
    <div class="search-header">
      <div class="status over-flow mt-sm">
        <div class="tag-change">
          <tag-view :list="tagList" @tagChange="changeStatus" ref="tagView"></tag-view>
          <Tooltip class="ml-sm" placement="right">
            <i class="icon-font icon-wenhao"></i>
            <template #content>
              <div>
                <span class="tip">【新增设备】</span>
                <span>：新增或者导入资产库不存在的设备。</span>
              </div>
              <div>
                <span class="tip">【修订设备】</span>
                <span>：对资产库的设备进行编辑，或者导入资产库已有设备。</span>
              </div>
            </template>
          </Tooltip>
        </div>
      </div>
      <div class="search-content mt-sm">
        <ui-label class="inline mr-md mb-sm" label="组织机构">
          <api-organization-tree
            ref="apiOrgTree"
            :custorm-node="true"
            :custorm-node-data="custormNodeData"
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline mr-md mb-sm" label="行政区划">
          <api-area-tree
            :select-tree="selectTree"
            :custorm-node="true"
            :custorm-node-data="custormAreaNodeData"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <ui-label class="inline mr-md mb-sm" label="关键词">
          <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入设备编码/名称/IP/MAC"></Input>
        </ui-label>
        <ui-label class="inline mr-md mb-sm" label="入库检测状态">
          <Select class="width-sm" v-model="searchData.checkStatus" placeholder="请选择入库检测状态" clearable>
            <Option v-for="(item, index) in checkStatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <!--        <ui-label class="inline mr-md mb-sm" label="异常原因">-->
        <!--          <Select-->
        <!--              v-if="searchData.baseCheckStatus !== '1'"-->
        <!--              class="width-sm"-->
        <!--              v-model="searchData.baseErrorMessageList"-->
        <!--              placeholder="请选择异常原因"-->
        <!--              clearable-->
        <!--          >-->
        <!--            <Option-->
        <!--                v-for="(val, key, index) in baseErrorMessageList"-->
        <!--                :value="key"-->
        <!--                :key="index"-->
        <!--            >{{ val }}-->
        <!--            </Option>-->
        <!--          </Select>-->
        <!--        </ui-label>-->
        <ui-label class="inline mr-md mb-sm" label="入库标识">
          <Input v-model="searchData.putMark" class="width-md" placeholder="请输入入库标识"></Input>
        </ui-label>
        <ui-label class="inline mr-md mb-sm" label="填报人">
          <Input v-model="searchData.userName" class="width-md" placeholder="请输入填报人"></Input>
        </ui-label>
        <ui-label class="inline mr-sm mb-sm" label="填报时间">
          <DatePicker
            class="width-md"
            v-model="searchData.startTime"
            type="datetime"
            placeholder="请选择开始时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
            :options="startTimeOption"
            confirm
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="width-md"
            v-model="searchData.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
            :options="endTimeOption"
            confirm
          ></DatePicker>
        </ui-label>
        <ui-label v-if="active === '2'" class="inline mr-md mb-sm" label="审核人">
          <Input v-model="searchData.examineUser" class="width-md" placeholder="请输入审核人"></Input>
        </ui-label>
        <ui-label v-if="active === '2'" class="inline mr-sm mb-sm" label="审核时间">
          <DatePicker
            class="width-md"
            v-model="searchData.examineStartTime"
            type="datetime"
            placeholder="请选择开始时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'examineStartTime')"
            :options="examineStartTimeOption"
            confirm
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="width-md"
            v-model="searchData.examineEndTime"
            type="datetime"
            placeholder="请选择结束时间"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'examineEndTime')"
            :options="examineEndTimeOption"
            confirm
          ></DatePicker>
        </ui-label>
        <div class="inline search">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="reset">重置</Button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    active: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      checkStatusList: [
        {
          dataValue: '待检测',
          dataKey: '0',
        },
        {
          dataValue: '合格',
          dataKey: '1',
        },
        {
          dataValue: '不合格',
          dataKey: '2',
        },
      ],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
        {
          label: '组织机构匹配异常',
          orgCode: '-2',
        },
      ],
      custormAreaNodeData: [
        {
          label: '未分配行政区划',
          regionCode: '-1',
        },
        {
          label: '行政区划匹配异常',
          regionCode: '-2',
        },
      ],
      tagList: [],
      selectTree: {
        regionCode: '',
      },
      defaultSearch: {
        type: '0',
        orgCode: '',
        civilCode: '',
        keyWord: '',
        checkStatus: '',
        // baseErrorMessageList: '',
        putMark: '',
        userName: '',
        startTime: '',
        endTime: '',
      },
      searchData: {},
      // baseErrorMessageList: [],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let beginTime = new Date(this.searchData.beginTime);
          if (beginTime) {
            return date < beginTime;
          }
          return false;
        },
      },
      examineStartTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.examineEndTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      examineEndTimeOption: {
        disabledDate: (date) => {
          let beginTime = new Date(this.searchData.examineStartTime);
          if (beginTime) {
            return date < beginTime;
          }
          return false;
        },
      },
      deviceStatusList: [
        { label: '全部设备', value: '0' },
        { label: '新增设备', value: '1' },
        { label: '修订设备', value: '2' },
      ],
    };
  },
  created() {
    this.tagList = this.deviceStatusList.map((row) => row.label);
    // this.queryErrorList()
  },
  methods: {
    // 请求3个异常列表
    // async queryErrorList() {
    //   let res = await this.$http.get(equipmentassets.queryErrorReason, {
    //     params: {errorType: 'BASICS'},
    //   })
    //   this.baseErrorMessageList = res.data.data
    // },
    changeStatus(index) {
      this.searchData.type = this.deviceStatusList[index].value;
      this.$emit('search', this.searchData);
      // this.$emit('changeStatus', this.deviceStatusList[index].value)
    },
    selectedOrgTree(val) {
      this.$set(this.searchData, 'orgCode', val.orgCode);
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    search() {
      this.$emit('search', this.searchData);
    },
    reset() {
      this.selectOrgTree.orgCode = '';
      this.selectTree.regionCode = '';
      this.$refs.tagView.curTag = 0;
      this.$refs.apiOrgTree.reset();
      this.resetSearchDataMx(this.searchData, this.search);
    },
    // DatePicker选择时间后格式化
    changeTimeMx(formatTime, timeType, searchData, timeField) {
      this.$set(searchData, timeField, formatTime);
    },
  },
  computed: {
    ...mapGetters({
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  watch: {
    active: {
      handler(val) {
        this.$nextTick(() => {
          this.selectOrgTree.orgCode = '';
          this.selectTree.regionCode = '';
          this.$refs.tagView.curTag = 0;
        });
        this.searchData = this.$util.common.deepCopy(this.defaultSearch);
        if (val === '2') {
          this.searchData = Object.assign(
            {
              examineUser: '',
              examineStartTime: '',
              examineEndTime: '',
            },
            this.searchData,
          );
        }
        this.copySearch = null;
        this.copySearchDataMx(this.searchData);
        this.search();
      },
      immediate: true,
    },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-header {
  padding: 0 20px;
  position: relative;
  display: inline-block;

  .status {
    width: 100%;
    height: 35px;
  }

  .tag-change {
    display: flex;
    align-items: center;

    .tip {
      color: var(--color-primary);
    }

    .icon-wenhao {
      background-image: linear-gradient(to bottom, #f58d3d, #b8580d);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    @{_deep} .ivu-tooltip-inner {
      max-width: none;
    }
  }

  .search-content {
    @{_deep}.select-width {
      width: 230px;
    }
    .search {
      margin-left: 20px;
    }
  }
}
</style>
