<template>
  <ui-modal v-model="visible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
    <div class="artificial-data">
      <ui-label class="block" label="人工复核:" :width="80">
        <RadioGroup v-model="artificialData.qualified">
          <!-- <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{
              item.key
            }}</Radio> -->
          <Radio label="1">设备合格</Radio>
          <Radio label="2" class="ml-lg">设备不合格</Radio>
        </RadioGroup>
      </ui-label>
      <ui-label class="block mt-sm" label="" :width="80">
        <Input
          type="textarea"
          class="desc"
          v-model="artificialData.reason"
          placeholder="请输入备注信息"
          :rows="5"
          :maxlength="256"
        ></Input>
      </ui-label>
    </div>
    <template #footer>
      <Button type="primary" class="plr-30" @click="artificialConfirm">确定复核结果</Button>
    </template>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  props: {
    value: {},
    artificialRow: {
      default: () => {
        return { id: null };
      },
    },
  },
  data() {
    return {
      artificialStyles: {
        width: '3rem',
      },
      artificialData: { qualified: '1', reason: '' },
      visible: false,
    };
  },
  created() {},
  methods: {
    async artificialConfirm() {
      let data = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
          type: 'device',
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.visible = false;
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped></style>
