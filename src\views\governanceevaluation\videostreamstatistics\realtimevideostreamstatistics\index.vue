<template>
  <div class="videostreamstatistics-container auto-fill">
    <statistics indexId="4009" :switchData="switchData"> </statistics>
  </div>
</template>

<script>
export default {
  name: 'realtimevideostreamstatistics',
  components: {
    statistics: require('@/views/governanceevaluation/videostreamstatistics/components/statistics.vue').default,
  },
  props: {},
  data() {
    return {
      switchData: [
        { label: '全量视频监控', value: 0 },
        { label: '重点视频监控', value: 1 },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.videostreamstatistics-container {
  width: 100%;
  height: 100%;
  background: var(--bg-content);
  padding: 10px 20px 20px 20px;
}
</style>
