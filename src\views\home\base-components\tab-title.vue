<template>
  <div :class="homeStyle">
    <div class="header">
      <span
        v-for="(item, index) in tabData"
        :key="item.id"
        @click="clickItem(item, index)"
        :id="item.id"
        class="link-text ml-md"
        :class="{
          'link-text-active': item.id === activeValue,
          'link-text-disabled': item?.disabled,
          'hight-light': hasHighLight && item.id === highLightId,
        }"
      >
        <span>{{ item.label }}</span>
        <slot name="postfix" :row="item"></slot>
      </span>
    </div>
    <div class="right-header">
      <span class="icon-font icon-fanhui link-text" v-if="isBack" @click="onClickBack"></span>
      <slot></slot>
    </div>
  </div>
</template>

<script>

export default {
  name: 'tab-title',
  components: {},
  props: {
    data: {},
    value: {},
    /**
     * 是否显示返回按钮
     */
    isBack: {
      type: Boolean,
      default: false,
    },
    // 是否有高亮效果，与highLightId一起使用
    hasHighLight: {
      default: false,
    },
    highLightId: {
      default: null,
    },
    styleType: {},
  },
  data() {
    return {
      tabData: [],
      activeValue: '',
    };
  },
  computed: {
    homeStyle() {
      return this.styleType === '2' ? `tab-title-container-${this.styleType}` : 'tab-title-container';
    },
  },
  watch: {
    data: {
      handler(val) {
        this.tabData = val || [];
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler(val) {
        this.activeValue = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    clickItem(val, index) {
      if (val?.disabled || this.activeValue === val.id) return;
      this.activeValue = val.id;
      this.$emit('input', val.id);
      this.$emit('on-change', val.id, val, index);
    },
    onClickBack() {
      this.$emit('on-click-back');
    },
  },
};
</script>

<style lang="less" scoped>
.back-color {
  color: rgba(27, 134, 255, 1);
}
.link-text-disabled {
  color: #d1651c;
  font-weight: normal;
}
.hight-light {
  font-size: 24px;
  color: #27eee1;
}
.tab-title-container {
  position: relative;
  background: url('~@/assets/img/base-home/tab-title-bg.png') no-repeat;
  background-size: 100% 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  &::before {
    content: '';
    background: url('~@/assets/img/base-home/tab-title-jiantou.png') no-repeat;
    background-size: contain;
    position: absolute;
    left: 2px;
    top: 50%;
    transform: translateY(-46%);
    width: 27px;
    height: 25px;
    z-index: -1;
  }
  .header {
    position: relative;
    font-size: 20px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    width: 100%;

    .link-text-active {
      color: #ffffff;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: 90px;
        height: 50px;
        background-image: url('~@/assets/img/base-home/select.png'); /* 标准的语法（必须放在最后） */
        bottom: -55px;
        background-size: contain;
        z-index: 0;
        left: 45%;
        transform: translate(-50%, -50%);
      }
    }
    .link-text {
      position: relative;
      color: #1b86ff;
      cursor: pointer;
      &:first-child {
        margin-left: 30px;
      }
    }
  }
  .right-header {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
  }
}
.tab-title-container-2 {
  position: relative;
  height: 32px;
  line-height: 32px;
  background: linear-gradient(90deg, rgba(64, 225, 254, 0.36) 0%, rgba(64, 225, 254, 0) 100%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #17d8e9;
    border-radius: 1px;
    border: 1px solid #ffffff;
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-46%);

    z-index: -1;
  }
  .header {
    position: relative;
    font-size: 16px;
    font-weight: 400;
    width: 100%;
    .link-text {
      position: relative;
      color: rgba(48, 233, 245, 0.5);
      cursor: pointer;
      &:first-child {
        margin-left: 25px;
      }
    }

    .link-text-active {
      color: #30e9f5;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: 34px;
        height: 2px;
        background: #30e9f5;
        bottom: -8px;
        background-size: contain;
        z-index: 0;
        left: calc(50% - 1px);
        transform: translate(-50%, -50%);
      }
    }
  }
  .right-header {
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
  }
}
</style>
