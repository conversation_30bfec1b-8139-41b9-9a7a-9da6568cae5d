<!--
    * @FileDescription: 高频违法分析
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <div class="search-box">
            <div class="title">
                <p>高频违法分析</p>
            </div>
            <div class="search_condition">
                <div class="search_form">
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">出现频次</p>
                        </div>
                        <div class="search_content">
                            <InputNumber v-model="queryParams.illegalCount" class="wrapper-input"></InputNumber>
                            <span class="ml-10">次及以上</span>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">开始时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.startDate" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="开始时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">结束时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.endDate" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="结束时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">身份证号</p>
                        </div>
                        <div class="search_content">
                            <Input v-model="queryParams.idCardNo" placeholder="请输入身份证号" class="wrapper-input"></Input>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">设备资源</p>
                        </div>
                        <div class="search_content">
                            <div class="select-tag-button" @click="selectDevice()">
                                选择设备/已选（{{ queryParams.selectDeviceList.length }}）
                            </div>
                        </div>
                    </div>
                    <div class="btn-group">
                        <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
                    </div>
                </div>
            </div>
        </div>
        <!-- 选择设备 -->
        <select-nonMotorIllegal-device ref="selectDevice" @selectData="selectData" />
    </div>
</template>

<script>
import selectNonMotorIllegalDevice from '@/components/select-modal/select-nonMotorIllegal-device.vue'
export default {
    name: '',
    components: {
        selectNonMotorIllegalDevice
    },
    data () {
        return {
            queryParams:{
                idCardNo: '',
                illegalCount: 5,
                selectDeviceList: []
            }
        }
    },
    async created() {
        this.queryParams.startDate = this.getAgoDay(30);
        this.queryParams.endDate = this.getAgoDay(0);
    },
    methods: {
        // 查询
        handleSearch() {
            this.queryParams.startDate = this.$dayjs(this.queryParams.startDate).format('YYYY-MM-DD HH:mm:ss');
            this.queryParams.endDate = this.$dayjs(this.queryParams.endDate).format('YYYY-MM-DD HH:mm:ss');
            this.$emit('search', this.queryParams)
        },
        // 时间判断
        getAgoDay(n){
            let date= new Date();
            let newDate = new Date(date.getTime() - n*24*60*60*1000);
            var Y = newDate.getFullYear() + '-';
            var M = (newDate.getMonth()+1 < 10 ? '0'+(newDate.getMonth()+1) : newDate.getMonth()+1) + '-';
            var D = newDate.getDate() < 10 ? '0'+ newDate.getDate() + ' ' : newDate.getDate() + ' ';
            var h = newDate.getHours() < 10 ? '0'+ newDate.getHours() + ':' : newDate.getHours() + ':';
            var m = newDate.getMinutes() < 10 ? '0'+ newDate.getMinutes() + ':' : newDate.getMinutes() + ':';
            var s = newDate.getSeconds() < 10 ? '0'+ newDate.getSeconds() : newDate.getSeconds();
            return Y+M+D+h+m+s;
        },
        /**
         * 选择设备
         */
        selectDevice() {
            this.$refs.selectDevice.show(this.queryParams.selectDeviceList);
        },
        /**
         * 已选择设备数据返回
         */
        selectData(ids) {
            this.queryParams.selectDeviceList = ids;
        },
    }
}
</script>

<style lang='less' scoped>
@import './style/index';

/deep/ .ivu-date-picker{
    width: 100%;
}
/deep/ .ivu-tag-select-option{
    margin-right: 5px !important;
}
</style>
