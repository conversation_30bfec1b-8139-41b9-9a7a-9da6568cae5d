<template>
  <div>
    <div v-if="!componentName">
      test111111
      <create-tabs
        v-for="(item, index) in componentList"
        :key="index"
        :component-name="item.componentName"
        :tabs-text="item.text"
        @selectModule="selectModule"
      >
        {{ item.text }}
      </create-tabs>
      <Button @click="closeTabs">关闭此标签</Button>
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import { mapActions } from 'vuex';
export default {
  mixins: [dealWatch],
  name: 'test1',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentList: [
        { componentName: 'test11', text: '模块1-1' },
        { componentName: 'test12', text: '模块1-2' },
      ],
      componentLevel: 1, //组件标签层级 如果是标签组件套用标签组件需要此参数
    };
  },
  created() {
    this.getParams();
  },
  activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
  },
  methods: {
    ...mapActions({
      closeCacheRouter: 'tabs/closeCacheRouter',
    }),
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    closeTabs() {
      this.closeCacheRouter(this.$options.name);
    },
  },
  watch: {},
  computed: {},
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    test11: require('@/components/create-tabs/test11.vue').default,
    test12: require('@/components/create-tabs/test12.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
