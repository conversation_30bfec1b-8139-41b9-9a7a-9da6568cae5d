<template>
  <div class="ui-search-tree">
    <div class="input-div" v-if="noSearch">
      <Input :placeholder="placeholder" type="text" v-model="searchText" @keyup.enter.native="enter">
        <template slot="append">
          <i class="icon-font icon-sousuo" @click="enter"></i>
        </template>
      </Input>
    </div>
    <el-tree
      :data="treeData"
      :props="defaultProps"
      :show-checkbox="showCheckbox"
      :node-key="nodeKey"
      :default-expanded-keys="defaultKeys"
      :default-checked-keys="defaultCheckedKeys"
      :default-expand-all="expandAll"
      :expand-on-click-node="expandOnClickNode"
      :render-after-expand="renderAfterExpand"
      :current-node-key="currentNodeKey"
      :filter-node-method="filterNode"
      :check-strictly="checkStrictly"
      :style="{ maxHeight: maxHeight / 192 + 'rem' }"
      @check="check"
      @check-change="checkChange"
      @node-click="handleNodeClick"
      @node-contextmenu="nodeContextmenu"
      ref="uiTree"
      class="el-tree"
      v-scroll="scroll"
    >
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <slot name="label" :node="node" :data="data">
          <span
            :class="{
              'nav-not-selected': node.disabled,
              allowed: node.disabled,
            }"
          >
            {{ node.label }}
          </span>
        </slot>
      </div>
    </el-tree>
    <loading v-if="treeLoading" fix></loading>
  </div>
</template>
<style lang="less" scoped>
@{_deep}.el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep}.el-tree-node > .el-tree-node__children {
  overflow: initial;
}
@{_deep}.el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}
.ui-search-tree {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;
  padding-right: 2px;
  padding-top: 10px;
  .input-div {
    margin-bottom: 10px;
  }
  .el-tree {
    font-size: 14px;
    .custom-tree-node {
      width: 100%;
    }
    @{_deep} .el-tree-node__content {
      height: 34px;
    }
  }
  @{_deep}.ivu-input-group-append {
    background: var(--bg-ivu-input-group-append);
    border: none;
    color: #fff;
    &:hover {
      background: var(--bg-ivu-input-group-append-bover);
      border: none;
    }
  }
}
</style>
<script>
export default {
  data() {
    return {
      searchText: '',
      onedimensionalData: [],
      nodeClickItem: {},
    };
  },
  created() {},
  mounted() {},
  methods: {
    filterNode(value, data) {
      return this.searchFilter(value, data);
    },
    searchFilter(value, data) {
      if (!value) return true;
      return data[this.defaultProps.label].indexOf(value) !== -1 || data[this.nodeKey] === value;
    },
    enter() {
      this.$refs.uiTree.filter(this.searchText);
    },
    // 节点选中触发
    handleNodeClick(data, Node, components) {
      if (data.disabled) {
        if (this.clickDisabledItemShow) {
          this.$Message.error('您没有此组织机构权限');
        }
        return false;
      }
      if (this.cancelCurrentNodeChecked) {
        if (this.nodeClickItem[this.nodeKey] === data[this.nodeKey]) {
          // 取消当前高亮的节点
          this.$refs.uiTree.setCurrentKey(null);
          this.nodeClickItem = {};
        } else {
          this.nodeClickItem = data;
        }
        data = this.nodeClickItem;
      }
      this.$emit('selectTree', data, Node, components);
    },
    // 选择checkbox触发
    check(data, checkData) {
      // fixed element-tree中当父节点被禁用子节点全部手动选中后父节点也会被选中
      if (this.checkStrictly && this.relation) {
        const isCheck = checkData.checkedKeys.includes(data[this.nodeKey]);
        this.downwardCorrelation(data[this.nodeKey], isCheck);
        this.upwardAssociation(data[this.nodeKey]);
        this.$emit('check', this.getCheckedKeys(), data, this.getCheckedNodes());
      } else {
        this.$emit('check', checkData.checkedKeys, data, checkData);
      }
      if (this.simpleCheckedNodes) {
        this.$emit('simpleCheckedNodes', this.getSimpleCheckedNodes(this.$refs.uiTree.store));
      }
    },
    getParentIds(key) {
      const treeData = this.onedimensionalData.find((row) => row[this.nodeKey] === key);
      if (!treeData && !treeData.parentIds) {
        console.error('没有parentIds这个字段无法手动选中上级');
        return null;
      }
      return treeData.parentIds;
    },
    downwardCorrelation(key, isCheck = true) {
      const treeData = this.onedimensionalData.find((row) => row[this.nodeKey] === key);
      // 找到所有节点中父结点中包含当前节点的树节点
      const childOrgList = this.onedimensionalData
        .filter((row) => {
          const parentIds = row.parentIds.split(',').map((row) => +row);
          return parentIds.includes(treeData.id);
        })
        .map((row) => row[this.nodeKey]);
      // 选中或取消选中所有子节点
      childOrgList.forEach((key) => {
        const node = this.getNode(key);
        if (!node.disabled) {
          node.checked = isCheck;
          node.indeterminate = false;
        }
      });
    },
    upwardAssociation(key) {
      const parentIds = this.getParentIds(key);
      if (!parentIds) return;
      // 获取当前选中的层级的所有父级
      const pIds = parentIds.split(',');
      while (pIds.length) {
        let pId = +pIds.shift();
        if (pId !== 0) {
          const treeData = this.onedimensionalData.find((row) => row.id === pId);
          // 如果不存在父节点，说明本节点已经是根节点，则不再进行向上关联操作
          if (!treeData) return;
          let node = this.getNode(treeData[this.nodeKey]);
          let childNodes = node.childNodes;
          // 判断是否有半选或者全选子节点，如果没有则取消选中此节点 indeterminate为是否半选
          let checkNodes = childNodes.filter((childNode) => childNode.checked || childNode.indeterminate);
          if (checkNodes.length !== 0) {
            for (let i = 0, len = childNodes.length; i < len; i++) {
              /**
                如果当前节点的所有子节点中有一个没有被选中，则该节点为半选节点
                如果全部选中且该节点没有被禁用，则该节点也选中,否则半选该节点
              */
              if (!childNodes[i].checked) {
                node.checked = false;
                node.indeterminate = true;
                break;
              }
              if (i === len - 1) {
                if (node.disabled) {
                  node.checked = false;
                  node.indeterminate = true;
                } else {
                  node.checked = true;
                  node.indeterminate = false;
                }
              }
            }
          } else {
            node.checked = false;
            node.indeterminate = false;
          }
        }
      }
    },
    checkChange(checkedObj, status) {
      this.$emit('check-change', checkedObj, status);
    },
    setCheckedKeys(data) {
      this.$refs.uiTree.setCheckedKeys(data);
    },
    setChecked(key, bool, includeChild = false) {
      this.$refs.uiTree.setChecked(key, bool, includeChild);
    },
    getCheckedKeys() {
      return this.$refs.uiTree.getCheckedKeys();
    },
    getNode(key) {
      return this.$refs.uiTree.getNode(key);
    },
    getCheckedNodes(isChild = false, hasHalf = false) {
      return this.$refs.uiTree.getCheckedNodes(isChild, hasHalf);
    },
    // 选中父级只传父级节点，子级全选传父级节点
    getSimpleCheckedNodes(store) {
      // 定义数组
      const checkedNodes = [];
      // 判断是否为全选，若为全选状态返回被全选的节点，不为全选状态正常返回被选中的节点
      const traverse = function (node) {
        const childNodes = node.root ? node.root.childNodes : node.childNodes;
        childNodes.forEach((child) => {
          if (child.checked) {
            checkedNodes.push(child.data);
          }
          if (child.indeterminate) {
            traverse(child);
          }
        });
      };
      traverse(store);
      return checkedNodes;
    },
    nodeContextmenu(event, data, node) {
      this.$emit('contextmenu', event, data, node);
    },
  },
  watch: {
    currentNodeKey(val) {
      this.$refs.uiTree.setCurrentKey(val);
    },
    treeData: {
      handler(val) {
        if (val && val.length) {
          let arr = this.$util.common.deepCopy(val);
          this.onedimensionalData = this.$util.common.jsonToArray(arr);
        }
      },
      immediate: true,
    },
    defaultCheckedKeys(val) {
      this.$nextTick(() => {
        if (val && this.checkStrictly && this.relation) {
          val.forEach((key) => {
            this.upwardAssociation(key);
            this.downwardCorrelation(key);
          });
        }
      });
    },
  },
  computed: {},
  props: {
    treeData: {
      type: Array,
    },
    placeholder: {
      default: '请输入区域名称或区域编码',
    },
    // 传入的属性对应的值
    defaultProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          children: 'children',
        };
      },
    },
    // 默认展开的数组
    defaultKeys: {},
    // 默认选中的keys
    defaultCheckedKeys: {},
    // 当前选中的节点
    currentNodeKey: [String, Number],
    // 是否默认全部展开
    expandAll: {},
    // 是否显示checkbox
    showCheckbox: {},
    // loading
    treeLoading: {
      default: false,
    },
    // 滑动高度
    scroll: {
      type: Number,
    },
    // 最大高度
    maxHeight: {
      type: Number,
    },
    // 关联字段
    nodeKey: {
      required: true,
    },
    // 是否子级父级不关联选择
    checkStrictly: {
      default: false,
      type: Boolean,
    },
    /**
     * fixed element-tree中当父节点被禁用子节点全部手动选中后父节点也会被选中，传入defaultCheckedKeys过多时会卡顿的问题
     * 配合checkStrictly为true 使用手动关联上下级
     *  */
    relation: {
      default: false,
      type: Boolean,
    },
    // 是否可以搜索
    noSearch: {
      default: true,
    },
    // 选中父级只传父级节点，子级全选传父级节点
    simpleCheckedNodes: {
      default: false,
    },
    // 再次点击时 是否取消当前选中状态
    cancelCurrentNodeChecked: {
      default: false,
    },
    // 禁止选择时，默认会提示
    clickDisabledItemShow: {
      default: true,
    },
    renderAfterExpand: {
      type: Boolean,
      default: true,
    },
    expandOnClickNode: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
};
</script>
