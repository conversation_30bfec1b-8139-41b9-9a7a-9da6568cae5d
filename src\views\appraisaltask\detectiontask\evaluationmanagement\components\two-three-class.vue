<template>
  <ui-modal
    v-model="visible"
    ref="uiModal"
    :title="title || '二三类指标名称'"
    :styles="styles"
    class="confimDataModal"
    @on-cancel="onCancel"
    @on-visible-change="onVisibleChange"
    footer-hide
  >
    <div class="container">
      <div class="times">
        <span>测评时间：{{ this.$parent.row.examineTime }}</span>
        <Button type="primary" class="export" @click="exportFn"> 导出 </Button>
      </div>
      <line-title title-name="检测结果统计"></line-title>
      <two-three-line-echart v-if="echartData.length > 0" :lineEchartList="echartData" />

      <Row :gutter="24">
        <Col span="12">
          <div>
            <line-title :title-name="dataInfo.deviceAllNum"></line-title>
            <Row :gutter="0" class="table-title">
              <Col span="5"
                ><div>{{ dataInfo.deviceName }}</div></Col
              >
              <Col span="5"></Col>
              <Col span="5"><div>设备总数</div></Col>
              <!-- <Col span="5"><div>{{dataInfo.deviceAllNum}}</div></Col> -->
              <Col span="5"></Col>
              <Col span="4"
                ><div>{{ dataInfo.interlv }}</div></Col
              >
            </Row>
            <Row :gutter="0" class="left-content">
              <Col span="5" class="blue"
                ><div>{{ dataInfoVal.deviceName }}</div></Col
              >
              <Col span="5">/</Col>
              <Col span="5" class="blue"
                ><div>{{ dataInfoVal.deviceAllNum }}</div></Col
              >
              <Col span="5">=</Col>
              <Col span="4" class="blue"
                ><div>{{ dataInfoVal.interlv }}%</div></Col
              >
            </Row>
          </div>
        </Col>
        <Col span="12">
          <div>
            <line-title title-name="提升率计算"></line-title>
            <Row :gutter="20" class="table-title">
              <Col span="6"><div>评测批次</div></Col>
              <Col span="3"></Col>
              <Col span="6"><div>治理前联网数量</div></Col>
              <Col span="3"></Col>
              <Col span="6"><div>提升率</div></Col>
            </Row>
            <Row :gutter="20" class="left-content">
              <Col span="6"
                ><div class="line">{{ topTime }}</div></Col
              >
              <Col span="3">/</Col>
              <Col span="6" class="blue">
                <div>
                  <Select v-model="dataTimeVal">
                    <Option v-for="(item, index) in dataList" :value="item.value" :key="index">{{ item.label }}</Option>
                  </Select>
                </div>
              </Col>
              <Col span="3">=</Col>
              <Col span="6" class="green"
                ><div>{{ intelRate }}%</div></Col
              >
            </Row>
          </div>
        </Col>
      </Row>
    </div>
  </ui-modal>
</template>

<script>
import api from '@/config/api/governanceevaluation';
export default {
  // props: ["twoThreeObj"],
  props: {
    twoThreeObj: {},
  },
  data() {
    return {
      visible: false,
      title: '',
      styles: {
        width: '80%',
      },
      dataList: [],
      dataTimeVal: '',
      dataInfo: {
        deviceName: '可联网设备数',
        deviceAllNum: '设备总数',
        interlv: '联网率',
      },
      dataInfoVal: {
        deviceName: '可联网设备数',
        deviceAllNum: '设备总数',
        interlv: '联网率',
      },
      echartData: [],
    };
  },
  async mounted() {
    this.title = this.twoThreeObj.indexName;
    if (this.twoThreeObj.strategy == 'VIDEO_NETWORKING_PROMOTION_RATE') {
      this.dataInfo = {
        deviceName: '可联网设备数',
        deviceAllNum: '本次检测联网率',
        interlv: '联网率',
      };
    }
    if (this.twoThreeObj.strategy == 'VIDEO_READ_PROMOTION_RATE') {
      this.dataInfo = {
        deviceName: '可调阅设备数',
        deviceAllNum: '本次检测可调阅率',
        interlv: '可调阅率',
      };
    }
    if (this.twoThreeObj.strategy == 'VIDEO_OSD_ACCURACY_PROMOTION_RATE') {
      this.dataInfo = {
        deviceName: '字幕标注合规设备数',
        deviceAllNum: '本次检测字幕标注合规率',
        interlv: '字幕标注合规率',
      };
    }
    if (this.twoThreeObj.strategy == 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE') {
      this.dataInfo = {
        deviceName: '时钟准确设备数',
        deviceAllNum: '本次检测时钟准确率',
        interlv: '时钟准确率',
      };
    }
    this.init();
  },
  methods: {
    init() {
      this.$http.post(api.currentDetectData, { resultIndexId: this.twoThreeObj.id, time: new Date() }).then((res) => {
        if (res.data.code == 200) {
          this.dataInfoVal = {
            deviceName: res.data.data.passNum,
            deviceAllNum: res.data.data.total,
            interlv: res.data.data.dataPassRate,
            tslVal: res.data.data.promotionRate,
          };
        } else {
          this.$Message.error(res.data.msg);
        }
      });

      this.echartData = [];
      this.dataTimeVal = null;
      this.$http
        .post(api.promotionRateStatisticalData, { resultIndexId: this.twoThreeObj.id, time: new Date() })
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data.yData;
            this.echartData.push(res.data.data.xData);
            var arr = [];
            list.forEach((item) => {
              arr.push(item.resultValue);
            });
            this.echartData.push(arr);
          } else {
            this.$Message.error(res.data.msg);
          }
        });

      this.$http.post(api.queryHistoryData, { resultIndexId: this.twoThreeObj.id, time: new Date() }).then((res) => {
        if (res.data.code == 200) {
          this.dataList = [];
          var list = res.data.data;
          list.forEach((item) => {
            this.dataList.push({ label: item.createTime, value: item.resultValue });
          });
          this.dataTimeVal = this.dataList[0].value;
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
    onCancel() {},

    onVisibleChange() {},
    exportFn() {
      this.$http
        .post(
          api.importExcel,
          { id: this.$parent.row.id, indexId: this.$parent.row.indexId },
          { responseType: 'arraybuffer' },
        )
        .then((res) => {
          if (res.status == 200) {
            let a = document.createElement('a');
            let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
            let objectUrl = URL.createObjectURL(blob);
            a.setAttribute('href', objectUrl);
            let now = new Date(),
              year = now.getFullYear(),
              mon = now.getMonth() + 1,
              day = now.getDate(),
              hours = now.getHours(),
              min = now.getMinutes(),
              sec = now.getSeconds();
            var dataStr =
              '' +
              year +
              (mon < 10 ? '0' + mon : mon) +
              (day < 10 ? '0' + day : day) +
              '-' +
              (hours < 10 ? '0' + hours : hours) +
              (min < 10 ? '0' + min : min) +
              (sec < 10 ? '0' + sec : sec);
            a.setAttribute(
              'download',
              '视频流视图数据 - ' + this.$parent.row.indexName + ' - [iVDG] - [' + dataStr + '].xls',
            );
            a.click();
          }
        });
    },
  },
  watch: {
    twoThreeObj() {
      this.title = this.twoThreeObj.indexName;
      if (this.twoThreeObj.strategy == 'VIDEO_NETWORKING_PROMOTION_RATE') {
        this.dataInfo = {
          deviceName: '可联网设备数',
          deviceAllNum: '本次检测联网率',
          interlv: '联网率',
        };
      }
      if (this.twoThreeObj.strategy == 'VIDEO_READ_PROMOTION_RATE') {
        this.dataInfo = {
          deviceName: '可调阅设备数',
          deviceAllNum: '本次检测可调阅率',
          interlv: '可调阅率',
        };
      }
      if (this.twoThreeObj.strategy == 'VIDEO_OSD_ACCURACY_PROMOTION_RATE') {
        this.dataInfo = {
          deviceName: '字幕标注合规设备数',
          deviceAllNum: '本次检测字幕标注合规率',
          interlv: '字幕标注合规率',
        };
      }
      if (this.twoThreeObj.strategy == 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE') {
        this.dataInfo = {
          deviceName: '时钟准确设备数',
          deviceAllNum: '本次检测时钟准确率',
          interlv: '时钟准确率',
        };
      }
      this.init();
    },
  },
  computed: {
    topTime() {
      return this.twoThreeObj.createTime;
    },
    intelRate() {
      if (this.dataTimeVal == 0 || this.dataTimeVal == null) {
        return 0;
      }
      return (((this.dataInfoVal.interlv - this.dataTimeVal) / this.dataTimeVal) * 100).toFixed(2);
    },
  },
  components: {
    lineTitle: require('@/components/line-title').default,
    TwoThreeLineEchart: require('./two-three-line-echart.vue').default,
  },
};
</script>

<style lang="less" scoped>
.confimDataModal {
  @{_deep} .ivu-modal-body {
    padding: 50px 20px 20px 20px;
  }
  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  .container {
    position: relative;
    height: 666px;
    overflow-y: auto;
    overflow-x: hidden;
    .scheme_header {
      height: 30px;
      line-height: 30px;
      width: 100%;
      display: inline-block;
      margin-top: 10px;
      .scheme_line {
        width: 8px;
        height: 30px;
        vertical-align: middle;
        display: inline-block;
        background: #239df9;
        opacity: 1;
      }
      .scheme_title {
        width: calc(100% - 18px);
        height: 30px;
        vertical-align: middle;
        display: inline-block;
        margin-left: 10px;
        padding-left: 10px;
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
      }
    }
    .times {
      .export {
        float: right;
      }
    }
    .table-title {
      color: rgba(255, 255, 255, 0.75);
      .ivu-col {
        margin: 36px 0 20px;
      }
      // .ivu-col-span-3 {
      //   text-align: center;
      // }
    }

    .left-content {
      .blue {
        font-size: 20px;
        font-weight: 600;
        color: #239df9;
      }
      .green {
        font-size: 20px;
        font-weight: 600;
        color: #0e8f0e;
      }

      .line {
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        border: 1px solid rgba(16, 69, 126, 0.75);
      }
      .ivu-col-span-3 {
        text-align: center;
        line-height: 30px;
      }
      .ivu-col-span-5 {
        line-height: 30px;
      }
    }
  }
}
</style>
