<template>
  <ui-modal v-model="visible" :title="getTitle" :styles="styles" class="task-modal-box">
    <!-- 任务基础信息 -->
    <Collapse v-model="collapseValue" simple class="collapse-box" v-ui-loading="{ loading: loading }">
      <Panel name="1" hide-arrow>
        <div class="detail-title-box f-14">
          <div>
            <i class="icon-font icon-renwutianjia f-16 mr-xs" />
            <span class="span-title">任务基础信息</span>
          </div>
          <div class="c-right">
            <i class="icon-font f-16 mr-xs" :class="[collapseValue.includes('1') ? 'icon-shouqi' : 'icon-zhankai1']" />
            <span class="f-12">{{ collapseValue.includes('1') ? '收起' : '展开' }}</span>
          </div>
        </div>
        <template #content>
          <Form
            ref="basicForm"
            label-colon
            :model="formData"
            :rules="ruleCustom"
            class="form-content"
            :label-width="90"
            label-position="left"
          >
            <FormItem label="任务名称" class="mr-lg" prop="taskName">
              <Input v-model="formData.taskName" placeholder="请输入任务名称" clearable></Input>
            </FormItem>
            <FormItem label="任务权限" class="mr-lg" prop="taskPrivileges">
              <select-organization-tree-checkall
                v-if="visible"
                ref="SelectOrganizationTreeCheckall"
                class="select-organization-tree-box"
                :treeData="treeData"
                :node-key="nodeKey"
                :default-props="defaultProps"
                :default-checked-keys="defaultCheckedKeys"
                :placeholder="treePlaceholder"
                :is-select-grandchild="false"
                :is-expand-all="false"
                @getSelectTree="checkTree"
              >
              </select-organization-tree-checkall>
            </FormItem>
            <FormItem label="设备范围" prop="totalCount">
              <div class="camera" @click="selectCamera">
                <span class="font-blue" v-if="formData?.customConfig?.deviceQueryForm?.totalCount"
                  >已选择{{ formData.customConfig.deviceQueryForm.totalCount }}条设备</span
                >
                <span v-else>
                  <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
                  <span class="font-blue">请选择设备</span>
                </span>
              </div>
            </FormItem>
            <FormItem label="分析内容" prop="factorIds" class="factor-content-box">
              <div v-for="(item, index) in analysisContentList" :key="index">
                <ui-label :label="`${item.name}：`" v-if="item.children && item.children.length > 0">
                  <CheckboxGroup v-model="formData.factorIds">
                    <Checkbox v-for="(childItem, childIndex) in item.children" :label="childItem.id" :key="childIndex">
                      {{ childItem.factorName }}
                    </Checkbox>
                  </CheckboxGroup>
                </ui-label>
              </div>
            </FormItem>
            <FormItem label="分析计划" prop="cronForm.timePoints" class="plan-box">
              <TestPlan
                align="row"
                :form-data="formData.cronForm"
                :custom-plan-list="customPlanList"
                :cron-strictly="false"
                @checkTime="checkTime"
              ></TestPlan>
            </FormItem>
          </Form>
        </template>
      </Panel>

      <!-- 分析配置 -->
      <Panel name="2" hide-arrow>
        <div class="detail-title-box f-14">
          <div>
            <i class="icon-font icon-fenxipeizhi f-16 mr-xs" />
            <span class="span-title">分析配置</span>
          </div>
          <div class="c-right">
            <i class="icon-font f-16 mr-xs" :class="[collapseValue.includes('2') ? 'icon-shouqi' : 'icon-zhankai1']" />
            <span class="f-12">{{ collapseValue.includes('2') ? '收起' : '展开' }}</span>
          </div>
        </div>
        <template #content>
          <div class="config-box" v-show="menuList.length">
            <menu-list
              ref="treeBoxRef"
              class="tree-box"
              :menu-list="menuList"
              :default-expand-all="true"
              :default-props="defaultPropsMenu"
              @clickTreeNode="clickTreeNode"
            >
              <!-- <template #textRight="{ data }">
                <img
                  v-if="allHasValue[data.factorCode]"
                  class="ml-sm img-gouxuan"
                  src="~@/assets/img/device-map/gouxuan.png"
                />
              </template> -->
            </menu-list>
            <template v-for="item in configTreeByMenu">
              <!-- 活跃分析 -->
              <template v-if="item.componentName === 'ActiveAnalysicConfig'">
                <active-analysic-config
                  v-show="currentTreeData.id === item.id"
                  :key="item.id"
                  :entry-type="entryType"
                  :factor-config="getFactorConfig(item.id)"
                  :ref="item.refName"
                  @changeFormData="changeFormData($event, item.refName, item.factorCode)"
                ></active-analysic-config>
              </template>
              <!-- 离线分析 -->
              <template v-if="item.componentName === 'OfflineAnalysicConfig'">
                <offline-analysic-config
                  v-show="currentTreeData.id === item.id"
                  :key="item.id"
                  :entry-type="entryType"
                  :factor-config="getFactorConfig(item.id)"
                  :ref="item.refName"
                  @changeFormData="changeFormData($event, item.refName, item.factorCode)"
                ></offline-analysic-config>
              </template>
              <!-- 质量分析 -->
              <!-- ... -->
            </template>
          </div>
        </template>
      </Panel>
    </Collapse>

    <!-- 选择设备  :region-code="regionCode"-->
    <select-device
      ref="selectDevice"
      :form-model="entryType"
      :parameters="getParameters"
      @getDeviceQueryForm="getDeviceQueryForm"
    ></select-device>

    <template #footer>
      <div>
        <Button @click="onCancel" class="plr-30">取消</Button>
        <Button
          v-if="!(entryType === 'edit' && formData.storageStatus !== 0)"
          class="plr-30"
          @click="onDraft"
          :loading="draftLoading"
        >
          存草稿
        </Button>
        <Button type="primary" @click="handleSubmit" :loading="saveLoading" class="plr-30">保存</Button>
      </div>
    </template>
  </ui-modal>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import dataAnalysis from '@/config/api/dataAnalysis.js';

export default {
  components: {
    SelectOrganizationTreeCheckall: require('@/api-components/select-organization-tree-checkall.vue').default,
    SelectDevice: require('./select-device.vue').default,
    TestPlan: require('@/views/systemconfiguration/dockingservice/generalconfig/test-plan.vue').default,
    MenuList: require('./menu-list.vue').default,
    ActiveAnalysicConfig: require('./active-analysic-config.vue').default,
    OfflineAnalysicConfig: require('./offline-analysic-config.vue').default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    // add: 新增   edit: 编辑
    entryType: {
      type: String,
      default: 'add',
    },
    nodeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    const validateTotalCount = (rule, value, callback) => {
      if (!this.formData.customConfig.deviceQueryForm.totalCount) {
        callback(new Error('请选择设备范围'));
      } else {
        callback();
      }
    };
    const validateTimePoints = (rule, value, callback) => {
      let { timePoints, cronData } = this.formData.cronForm;
      if (!timePoints.length || !cronData.length) {
        callback(new Error('请选择分析计划'));
      } else {
        callback();
      }
    };
    return {
      styles: {
        width: '6.93rem',
      },
      collapseValue: ['1', '2'],
      visible: true,
      loading: false,
      saveLoading: false,
      draftLoading: false,
      customPlanList: [
        { label: '每周', value: '2' },
        { label: '每月', value: '3' },
      ],
      formData: {
        taskName: '',
        taskPrivileges: [],
        // 设备范围
        customConfig: {
          deviceQueryForm: {},
          selectType: 2, // 1.按目录选择 2.按设备选择
        },
        // 分析内容
        factorIds: [],
        // 分析计划
        cronForm: {
          cronType: '2',
          timePoints: [],
          cronData: [],
        },
      },
      ruleCustom: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
          },
        ],
        taskPrivileges: [
          {
            required: true,
            message: '请选择任务权限',
            type: 'array',
            trigger: 'change',
          },
        ],
        totalCount: [
          {
            required: true,
            validator: validateTotalCount,
            trigger: 'change',
          },
        ],
        'cronForm.timePoints': [
          {
            required: true,
            validator: validateTimePoints,
            trigger: 'change',
            type: 'array',
          },
        ],
        factorIds: [
          {
            required: true,
            message: '请选择分析内容',
            type: 'array',
            trigger: 'change',
          },
        ],
      },
      // 任务权限
      defaultCheckedKeys: [],
      treeData: [],
      nodeKey: 'orgCode',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      treePlaceholder: '请选择组织机构',
      // 设备范围
      regionCode: '',
      orgCode: '',
      // 分析内容
      analysisContentList: [],
      // 配置
      defaultPropsMenu: {
        label: 'name',
        children: 'children',
      },
      menuList: [],
      configTreeByMenu: [], // 与分析配置树menuList 中相关的内容， 用于 保存时 获取入参 和 提示语
      currentTreeData: {},
      allHasValue: {},
    };
  },
  computed: {
    ...mapGetters({
      organizationList: 'common/getOrganizationList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      factorList: 'dataAnalysis/getFactorList',
    }),
    getTitle() {
      return this.entryType === 'add' ? '新建分析任务' : '编辑分析任务';
    },
    getFactorConfig() {
      return (id) => {
        let factorArr = this.formData?.extensionData?.factorConfigs?.filter((item) => item.factorId === id) || [];
        return factorArr[0];
      };
    },
    getParameters() {
      let deviceQueryForm = this.formData.customConfig.deviceQueryForm;
      let arr = deviceQueryForm && Object.keys(deviceQueryForm).length ? deviceQueryForm : null;
      return arr || { orgCodeList: [this.orgCode] };
    },
  },
  created() {
    this.initData();
    if (this.organizationList.length === 0) {
      this.setOrganizationList();
    }
  },
  mounted() {
    // 分析内容列表
    if (this.factorList.length === 0) {
      this.setFactorList();
    }
  },
  watch: {
    // value(val) {
    //   this.visible = val;
    //   if (val) {
    //     this.initData();
    //     if (this.organizationList.length === 0) {
    //       this.setOrganizationList();
    //     }
    //   }
    // },
    visible(val) {
      this.$emit('input', val);
    },
    organizationList() {
      this.setTreeData();
    },
    // 根据勾选的分析内容， 形成一个 分析树
    'formData.factorIds': {
      handler(val) {
        let menuArr = [];
        let configTree = [];
        this.analysisContentList.forEach((item) => {
          let selectList = item.children.filter((childItem) => val.includes(childItem.id));
          if (selectList.length) {
            menuArr.push({
              ...item,
              name: `${item.name}分析`,
              children: selectList,
            });

            selectList.forEach((selectItem) => {
              let { refName, componentName } = this.getRefName(selectItem.id);
              configTree.push({
                ...selectItem,
                title: `分析配置-${item.name}分析-${selectItem.factorName}`,
                refName: refName,
                componentName: componentName,
              });
            });
          }
        });
        this.configTreeByMenu = configTree;
        this.menuList = this.$util.common.deepCopy(menuArr);
        this.$nextTick(() => {
          // 默认选中 第一个分析内容
          if (this.menuList.length > 0) {
            let menuItem = this.menuList[0].children[0];
            this.currentTreeData = { ...menuItem };
            this.$refs.treeBoxRef.$refs.tree.setCurrentKey(menuItem.id);
          }
        });
      },
      deep: true,
      immediate: true,
    },
    // 处理 分析内容列表
    factorList: {
      handler(arr) {
        let tree = [];
        arr.forEach((item) => {
          item.name = item.factorName;
          let treeIndex = tree.findIndex((treeItem) => treeItem.id === item.factorModule);
          if (treeIndex === -1) {
            tree.push({
              name: item.moduleName,
              id: item.factorModule,
              children: [item],
            });
          } else {
            tree[treeIndex].children.push(item);
          }
        });
        this.analysisContentList = this.$util.common.deepCopy(tree);
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
      setFactorList: 'dataAnalysis/getFactorInfo',
    }),
    // 获取 【分析配置】 中渲染的组件 信息
    getRefName(id) {
      let obj = {
        refName: '',
        componentName: '',
      };
      switch (id) {
        case 200:
          // 人脸-卡口活跃分析
          obj.refName = 'faceActiveConfigRef';
          obj.componentName = 'ActiveAnalysicConfig';
          break;
        case 300:
          // 车辆-卡口活跃分析
          obj.refName = 'vehicleActiveConfigRef';
          obj.componentName = 'ActiveAnalysicConfig';
          break;
        case 201:
          // 人脸-卡口离线分析
          obj.refName = 'faceOfflineConfigRef';
          obj.componentName = 'OfflineAnalysicConfig';
          break;
        case 301:
          // 车辆-卡口离线分析
          obj.refName = 'vehicleOfflineConfigRef';
          obj.componentName = 'OfflineAnalysicConfig';
          break;
        default:
          break;
      }
      return obj;
    },
    // 查询已配置信息
    async getDataInfo() {
      if (this.entryType === 'add') {
        this.setTreeData();
        return;
      }
      try {
        this.loading = true;
        let res = await this.$http.get(`${dataAnalysis.viewInfo}/${this.nodeData.id}`);
        this.formData = res.data.data || {};
        this.defaultCheckedKeys = this.formData.taskPrivileges;
        this.setTreeData();
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    initData() {
      let { orgCode, regionCode } = this.defaultSelectedOrg;
      // 任务权限： 默认勾选 所属的组织机构
      this.defaultCheckedKeys = [orgCode];
      this.formData.taskPrivileges = [orgCode];
      this.orgCode = orgCode;
      this.$nextTick(() => {
        this.regionCode = regionCode;
      });
      this.getDataInfo();
    },
    // 任务权限 -- 组织树
    setTreeData() {
      this.$nextTick(() => {
        this.treeData = this.$util.common.deepCopy(this.organizationList) || [];
      });
    },
    checkTree(codes) {
      this.formData.taskPrivileges = codes;
    },
    // 设备选择参数
    getDeviceQueryForm(val) {
      this.formData.customConfig.deviceQueryForm = { ...val };
    },
    selectCamera() {
      this.$refs.selectDevice.selectCamera();
    },
    // 分析计划
    checkTime(obj) {
      let { cronType, timePoints, cronData } = obj;
      this.formData.cronForm.cronType = cronType;
      this.formData.cronForm.timePoints = timePoints || [];
      this.formData.cronForm.cronData = cronData || [];
    },
    clickTreeNode(data) {
      this.currentTreeData = { ...data };
    },
    onCancel() {
      this.visible = false;
    },
    async changeFormData() {},
    // 保存
    async handleSubmit() {
      try {
        let basicValid = await this.$refs.basicForm.validate();

        // 分析配置 -- 必填校验
        let validArr = [];
        for (let i = 0; i < this.formData.factorIds.length; i++) {
          let id = this.formData.factorIds[i];
          let configItem = this.configTreeByMenu.filter((item) => item.id === id);
          validArr.push({
            valid: true,
            ...configItem[0],
          });
          if (configItem[0].refName) {
            let { valid } = await this.$refs[configItem[0].refName][0].handleSubmit();
            validArr[i].valid = valid;
          }
        }

        if (basicValid && validArr.every((item) => item.valid === true)) {
          this.saveLoading = true;
          let data = this.handlerSaveParams();
          await this.saveFn(data);
        } else {
          let tip = !basicValid ? '【任务基础信息】中存在必填字段为空的情况，请重新填写' : '';
          let validItems = validArr.filter((item) => !item.valid);
          if (basicValid && validItems.length > 0) {
            tip = `【${validItems[0].title}】中存在必填字段为空的情况，请重新填写`;
          }
          this.$Message.error(tip);
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.saveLoading = false;
      }
    },
    // 存草稿    --- 任务名称、任务权限 必填
    async onDraft() {
      try {
        let validTaskName = '';
        await this.$refs.basicForm.validateField('taskName', (valid) => {
          // valid:返回错误信息    --- 有值，则代表 数据为空
          validTaskName = valid;
        });

        let validTaskP = '';
        await this.$refs.basicForm.validateField('taskPrivileges', (valid) => {
          validTaskP = valid;
        });

        if (validTaskP || validTaskName) {
          return;
        }
        this.draftLoading = true;
        let data = this.handlerSaveParams('草稿');
        await this.saveFn(data);
      } catch (error) {
        console.log(error);
      } finally {
        this.draftLoading = false;
      }
    },
    async saveFn(data) {
      try {
        await this.$http.post(dataAnalysis.saveAnalyseTaskConfig, data);
        this.$Message.success('保存成功');
        this.$emit('saveSuccessCb');
        this.onCancel();
      } catch (error) {
        throw new Error(error);
      }
    },
    handlerSaveParams(str = '') {
      let factorConfigs = [];
      this.formData.factorIds.forEach((id) => {
        let formData = {};
        let configItem = this.configTreeByMenu.filter((item) => item.id === id);
        if (configItem.length > 0 && configItem[0].refName) {
          formData = this.$refs[configItem[0].refName][0].getFormData();
        }
        factorConfigs.push({
          factorId: id,
          // 异常分析配置
          abnormalConfig: formData.anomalyFormData,
          // 影响分析配置
          influenceConfig: formData.influenceFormData,
          // 其他配置
          otherConfig: {},
        });
      });

      let data = {
        ...this.formData,
        storageStatus: str === '草稿' ? 0 : 1, // 0-草稿，1-有效
        // 各分析内容（因子）对应的 异常分析配置和影响分析配置
        extensionData: {
          factorConfigs: factorConfigs,
        },
      };
      return data;
    },
  },
};
</script>

<style lang="less" scoped>
.detail-title-box {
  display: flex;
  height: 41px;
  line-height: 41px;
  color: var(--color-title);
  justify-content: space-between;
  .c-right {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }
  .span-title {
    font-weight: bold;
    color: var(--color-content);
  }
}
.form-content {
  display: flex;
  flex-wrap: wrap;
  padding: 16px 0 0 0;
  @{_deep}.ivu-form-item {
    margin-bottom: 15px;
  }
  @{_deep}.ivu-input,
  .select-organization-tree-box,
  .camera {
    width: 250px;
  }
  @{_deep} .factor-content-box {
    width: 100%;
    .ui-label label {
      width: 90px !important;
      color: var(--color-content);
    }
    .ivu-checkbox-group .ivu-checkbox-wrapper {
      width: 130px !important;
    }
    .ivu-form-item-error-tip {
      padding-top: 0;
    }
  }
  .camera {
    padding: 0;
    height: 34px;
    line-height: 32px;
    background: var(--bg-choose-device);
    border: 1px dashed var(--border-choose-device);
    &:hover {
      background: var(--bg-choose-device-active);
      border: 1px dashed var(--border-choose-device-active);
    }
    .icon-xuanzeshexiangji {
      line-height: 30px;
    }
  }
}
.config-box {
  display: flex;
  margin-top: 16px;
  .tree-box {
    width: 208px;
    background: var(--bg-form-item);
  }
  .config-context {
    flex: 1;
  }
  .img-gouxuan {
    width: 14px;
    height: 14px;
    margin-right: 10px;
  }
  @{_deep} .ivu-checkbox-wrapper {
    width: fit-content;
  }
  // @{_deep} .ipt-requried.ivu-input-number {
  //   border-color: #ed4014;
  // }
}

@{_deep} .ivu-checkbox-wrapper {
  color: var(--color-content);
  .ivu-checkbox {
    margin-right: 0;
  }
}
@{_deep} .flex-label-item .ivu-form-item-content {
  display: flex;
}
@{_deep}.form-item-box {
  margin-bottom: 20px !important;
}
@{_deep}.ivu-modal-body {
  max-height: 750px;
  overflow: hidden;
  overflow-y: auto;
  padding: 0 60px;
}
@{_deep} .ivu-collapse {
  border: 0;
  .ivu-collapse-item {
    border: 0;
  }
  .ivu-collapse-header{
    margin-bottom: 10px;
  }
}
</style>
