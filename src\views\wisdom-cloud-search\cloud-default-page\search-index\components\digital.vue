<template>
  <div class="digitals">
    <div 
      v-for="(item, $index) in digitalList" 
      :key="$index"
      :style="item.style"
      :class="item.class"
      class="digital-item">
      {{item.label}}
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        letterList: [ 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2', '3', '4', '5', '6', '7', '8', '9' ],
        digitalList: []
      }
    },
    mounted() {
      for(let i = 0; i < 550; i++) {
        this.digitalList.push({
          label: this.letterList[Math.round(Math.random() * (this.letterList.length - 1))],
          class: Math.round(Math.random()*1000) % 3 === 0 ? 'digital0' : Math.round(Math.random()*1000) % 8 === 0 ? 'digital1' : Math.round(Math.random()*1000) % 15 === 0 ? 'digital2' : 'digital3',
          style: {
            margin: Math.round(Math.random()*1000) % 8 === 0 ? '2px 100px' :  Math.round(Math.random()*1000) % 15 === 0 ? '2px 150px' : '2px'
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .digitals {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
    box-sizing: border-box;
    z-index: 1;
    .digital-item {
      font-size: 22px;
      color: rgba(225, 239, 249, 0.25);
      margin: 2px;
      line-height: 22px;
    }
    .digital0 {
      animation: digitalMove1 1.5s infinite;
    }
    .digital1 {
      animation: digitalMove1 1.5s 0.5s infinite;
      color: rgba(225, 239, 249, 0.2);
    }
    .digital2 {
      animation: digitalMove1 2s infinite;
      color: rgba(225, 239, 249, 0.1);
    }
    .digital3 {
      animation: digitalMove1 2s infinite;
      color: rgba(225, 239, 249, 0.05);
    }
    @keyframes digitalMove1 {
      0% {
        opacity: 1;
      }
      50% {
        opacity: 0.2;
      }
      100% {
        opacity: 1;
      }
    }
  }
</style>