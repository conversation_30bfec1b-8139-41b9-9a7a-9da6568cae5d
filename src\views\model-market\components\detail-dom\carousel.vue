<template>
    <div class="carousel">
        <i class="iconfont icon-doubleleft mr-10 cursor-p" v-if='samePositionPoint.length > 10' @click="toLeft"></i>
        <div class="face-list-wrapper">
            <div class="face-list" :style="{transform: `translateX(${transformWidth}%)`,width: `${totalSize*100 + 2}%`}">
                <template v-for="(item, index) in samePositionPoint">
                    <div :key="index" @click="selectCollectionHandler(index,item)" :class="{ active: currentCollectionIndex === index }">
                        <img v-lazy="item.traitImg" alt="" />
                        <span v-if="item.num > -1">{{ item.num }}</span>
                        <span v-if="item.headType">
                            <i class="iconfont" :class="item.headType == 'face' ? 'icon-renlian' : 'icon-cheliang' "></i>
                        </span>
                    </div>
                </template>
            </div>
        </div>
        <i class="iconfont icon-doubleright ml-10 cursor-p" v-if='samePositionPoint.length > 10' @click="toRight"></i>
    </div>
</template>
<script>
export default {
    props: {
        samePositionPoint: {
            type: Array,
            default: () => []
        },
    },
    data () {
        return {
            currentCollectionIndex: 0,
            currentPageAction: {
                pageSize: 10,
                pageNum: 1
            },
            transformWidth: 0,
        }
    },
    created () { },
    methods: {
        init(index = 0) {
            this.currentCollectionIndex = index;
            this.transformWidth = 0;
        },
        selectCollectionHandler (index,item) {
            this.currentCollectionIndex = index
            this.$emit('changeVid', item)
        },
        toLeft () {
            if(this.currentPageAction.pageNum > 1){
                this.currentPageAction.pageNum--;
                let per = Math.ceil(100 / this.totalSize);
                // this.transformWidth = ((this.currentPageAction.pageNum -1) * per) + Number(this.transformWidth);
                this.transformWidth = per + Number(this.transformWidth);
            }
        },
        toRight () {
            if(this.currentPageAction.pageNum < this.totalSize){
                this.currentPageAction.pageNum++;
                let per = Math.ceil(100 / this.totalSize);
                this.transformWidth = `${-(this.currentPageAction.pageNum-1) * per }`;
            }
        },
    },
    watch: {
    },
    computed: {
        totalSize(){
            return Math.ceil(this.samePositionPoint.length/this.currentPageAction.pageSize)
        }
    },
    components: {}
}
</script>
<style lang='less' scoped>
.carousel {
    position: relative;
    height: 70px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}
.face-list-wrapper {
    overflow: hidden;
    width: 100%;
}
.face-list {
    transform: translateX(0px);
    transition: transform 2s ease;
    >div {
        width: 65px;
        height: 65px;
        border: 1px solid #d3d7de;
        margin-right: 10px;
        white-space: nowrap;
        cursor: pointer;
        position: relative;
        display: inline-block;
        >img {
            width: 100%;
            height: 100%;
        }

        >span {
            width: 25px;
            height: 16px;
            background: #2c86f8;
            text-align: center;
            border-radius: 0px 0px 4px 0px;
            font-size: 12px;
            color: #ffffff;
            position: absolute;
            left: 0;
            top: 0;
            display: flex;
            align-items: center;
            .iconfont{
                font-size: 14px;
            }
        }
    }
    .active {
        border: 3px solid rgba(44, 134, 248, 1);
    }
}
</style>
