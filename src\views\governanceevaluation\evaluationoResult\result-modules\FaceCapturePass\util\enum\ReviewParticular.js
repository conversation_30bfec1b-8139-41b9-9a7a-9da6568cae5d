import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { iconStaticsFaceAndVehicle } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  ...iconStaticsFaceAndVehicle,
  {
    name: '人脸设备抓拍图片合格率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const imgFormData = [
  {
    type: 'start-end-time',
    label: '抓拍时间',
    startKey: 'minLogTime',
    endKey: 'maxLogTime',
  },
  {
    type: 'select',
    key: 'qualified',
    label: '检测结果',
    width: 160,
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'camera',
    label: '抓拍设备',
    key: 'deviceIds',
    width: 180,
  },
  {
    type: 'select',
    key: 'causeErrors',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const FaceTableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '点位类型',
    key: 'sbdwlxText',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测图片数量',
    key: 'total',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '合格图片数量',
    key: 'qualifiedNum',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '不合格图片数量',
    key: 'unqualifiedNum',
    slot: 'unqualifiedNum',
    tooltip: true,
    width: 200,
  },
  {
    title: '合格率',
    key: 'qualifiedRate',
    slot: 'qualifiedRate',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测结果',
    key: 'outcome',
    slot: 'outcome',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    tooltip: true,
    minWidth: 120,
  },
  {
    minWidth: 150,
    title: '设备标签',
    slot: 'tagNames',
  },
  {
    title: '操作',
    slot: 'option',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];
