<template>
  <div class="assetsreport auto-fill">
    <div class="select-header">
      <Badge :count="countStatic">
        <i class="icon-font icon-xiaoxi2" @click="searchNow" title="未读信息"></i>
      </Badge>
    </div>
    <!-- 顶部统计 -->
    <ChartsContainer :abnormal-count="countList" class="charts" />
    <!-- 表格 -->
    <TableList
      ref="tableList"
      :columns="tableColumns"
      :check-all="isAll"
      :load-data="loadDataList"
      @selectAction="selectAction"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <Search ref="search" @startSearch="startSearch" @startStatic="startStatic" @reset="reset"></Search>
        <!-- 操作按钮 -->
        <div class="operation">
          <Checkbox v-model="isAll">全选</Checkbox>
          <div class="right-btn fr">
            <Button type="primary" @click="exportDevice">
              <i class="icon-font icon-daochu mr-xs f-12"></i>
              <span>导出</span>
            </Button>
            <Button type="primary" @click="report()"> <i class="icon-font icon-piliangshangbao"></i> 批量上报 </Button>
          </div>
        </div>
      </div>
      <!-- 表格操作 -->
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          :styles="{ color: '#438CFF', 'font-size': '14px' }"
          icon="icon-shujushangbao"
          content="数据上报"
          @click.native="report(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          :styles="{ color: '#269F26', 'font-size': '14px' }"
          class="mr-md"
          icon="icon-shebeidangan"
          @click.native="deviceArchives(row)"
          content="设备档案"
        ></ui-btn-tip>
        <ui-btn-tip
          :disabled="row.cascadeReportStatus != 1"
          :styles="{ color: '#DE990F', 'font-size': '14px' }"
          @click.native="reportError(row)"
          class="mr-md"
          icon="icon-shebeijiucuo"
          content="上报纠错"
        ></ui-btn-tip>
        <ui-btn-tip
          :disabled="row.deviceCheckStatus !== 3"
          :styles="{ color: '#CC4242', 'font-size': '14px' }"
          @click.native="viewRecord(row)"
          icon="icon-yichangyuanyin"
          content="异常原因"
        ></ui-btn-tip>
      </template>
      <template #deviceId="{ row }">
        <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
          row.deviceId
        }}</span>
      </template>
      <template #cascadeReportStatus="{ row }">
        <span
          :style="{
            color:
              row.cascadeReportStatus == 1
                ? 'var(--color-success)'
                : row.cascadeReportStatus == 0
                  ? 'var(--color-warning)'
                  : '',
          }"
          >{{ row.cascadeReportStatus == 1 ? '已上报' : row.cascadeReportStatus == 0 ? '未上报' : '--' }}</span
        >
      </template>
      <template #recentlyReportStatus="{ row }">
        <span
          :style="{
            color:
              row.recentlyReportStatus == 1
                ? 'var(--color-success)'
                : row.recentlyReportStatus == 0
                  ? 'var(--color-warning)'
                  : '',
          }"
          >{{ row.recentlyReportStatus == 1 ? '上报成功' : row.recentlyReportStatus == 0 ? '上报失败' : '--' }}</span
        >
      </template>
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
      <template #sbgnlx="{ row }">
        <Tooltip
          placement="top"
          :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
          :disabled="row.sbgnlx.length < 2"
        >
          <div class="tooltipType">
            {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
          </div>
        </Tooltip>
      </template>
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #isOnline="{ row }">
        <div>
          <span
            class="check-status-font"
            :class="row.isOnline === '1' ? 'font-success' : row.isOnline === '2' ? 'font-failed' : ''"
          >
            {{ row.isOnlineText }}
          </span>
        </div>
      </template>
      <template #checkStatus="{ row }">
        <div :class="row.rowClass">
          <span
            class="check-status"
            :class="
              row.checkStatus === '0000'
                ? 'bg-success'
                : row.checkStatus === '1000'
                  ? 'bg-other'
                  : row.checkStatus
                    ? 'bg-failed'
                    : ''
            "
          >
            {{ handleCheckStatus(row.checkStatus) }}
          </span>
        </div>
      </template>
      <template #phyStatus="{ row }">
        <span :style="{ color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)' }">{{
          row.phyStatus | filterType(phystatusList)
        }}</span>
      </template>
      <template #recentlyReportTime="{ row }">
        <span>{{ row.recentlyReportTime == '1970-01-01 08:00:01' ? '--' : row.recentlyReportTime }}</span>
      </template>
      <template #reportSuccessTime="{ row }">
        <span>{{ row.reportSuccessTime == '1970-01-01 08:00:01' ? '--' : row.reportSuccessTime }}</span>
      </template>
    </TableList>
    <!-- 不合格数据 -->
    <UnqualifiedModal ref="unqualifiedModal" :reportPlatformId="reportPlatformId" :intefaceList="intefaceList" />
    <device-detail
      v-model="deviceDetailShow"
      :choosed-org="choosedOrg"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      @update="init"
    >
    </device-detail>
    <view-detection-field
      v-model="recordShow"
      :view-data="recordData"
      :need-option="true"
      @recordModalShow="deviceModalShow"
    ></view-detection-field>
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading"
            >{{ exportDataLoading ? '下载中' : '数据导出' }}
          </Button>
        </Tooltip>
      </template>
    </customize-filter>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import viewassets from '@/config/api/viewassets';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';

export default {
  mixins: [downLoadTips],
  name: 'assetsreport',
  components: {
    ChartsContainer: require('./components/chartsContainer').default,
    TableList: require('./components/tableList').default,
    Search: require('./components/search').default,
    UnqualifiedModal: require('./unqualifiedModal').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
  props: {},
  data() {
    return {
      countStatic: 0,
      reportPlatformId: '',
      intefaceList: [],
      choosedOrg: {},
      isAll: false,
      searchData: {},
      selectTable: [],
      totalCount: 0,
      countList: [
        { title: '设备总量', count: '0', icon: 'icon-equipmentlibrary' },
        { title: '已上报总量', count: '0', icon: 'icon-assetsreport' },
        { title: '未上报总量', count: '0', icon: 'icon-weishangbaoshuliang' },
        {
          title: '最近一次失败数量',
          countKey: '0',
          icon: 'icon-zuijinyicishangbaoshibaishuliang',
        },
      ],
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          fixed: 'left',
        },
        {
          width: 100,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
        },
        { width: 120, title: '组织机构', key: 'orgName', tooltip: true },
        { width: 100, title: '行政区划', key: 'civilName', tooltip: true },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        { minWidth: 100, title: '采集区域', key: 'sbcjqyText', tooltip: true },
        {
          width: 100,
          title: '在线状态',
          slot: 'isOnline',
          align: 'left',
          // fixed: 'right',
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        { width: 90, title: '检测状态', slot: 'checkStatus' },
        {
          minWidth: 90,
          title: '上报状态',
          slot: 'cascadeReportStatus',
          tooltip: true,
        },
        {
          width: 150,
          title: '最近一次上报状态',
          slot: 'recentlyReportStatus',
          tooltip: true,
        },
        {
          width: 150,
          title: '最近一次上报时间',
          slot: 'recentlyReportTime',
          tooltip: true,
        },
        { width: 150, title: '最近成功上报时间', slot: 'reportSuccessTime' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      recordShow: false,
      recordData: {},
      deviceUnqualified: null,
      deviceCode: '',
      viewDeviceId: 0,
      deviceDetailShow: false,
      deviceDetailAction: 'edit',
      deviceDetailTitle: '修改入库数据',
      // 下载模板、导出
      customFilter: false,
      exportDataLoading: false,
      exportUrl: viewassets.asertCenterExportReportDevice,
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  watch: {},
  filter: {},
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
    this.unreadStatistics();
    this.getPropertyList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    reset() {
      this.$refs.tableList.reset();
    },
    startSearch(searchData, choosedOrg) {
      this.choosedOrg = choosedOrg;
      let params = Object.assign(this.searchData, searchData);
      if (params.recentlyReportTimeStart) {
        params.recentlyReportTimeStart = this.$util.common.formatDate(this.searchData.recentlyReportTimeStart);
      }
      if (params.recentlyReportTimeEnd) {
        params.recentlyReportTimeEnd = this.$util.common.formatDate(this.searchData.recentlyReportTimeEnd);
      }

      this.searchData.isAll = this.isAll ? 1 : 0;
      this.startStatic();
      this.init();
    },
    // 获取统计
    startStatic() {
      this.$http.post(viewassets.queryDeviceInfoStatistics, this.searchData).then((res) => {
        this.countList = [
          {
            title: '设备总量',
            count: res.data.data.deviceInfoCount || '0',
            icon: 'icon-equipmentlibrary',
          },
          {
            title: '已上报总量',
            count: res.data.data.reportedDeviceCount || '0',
            icon: 'icon-assetsreport',
          },
          {
            title: '未上报总量',
            count: res.data.data.unReportDeviceCount || '0',
            icon: 'icon-weishangbaoshuliang',
          },
          {
            title: '最近一次失败数量',
            count: res.data.data.lastReportFailedCount || '0',
            icon: 'icon-zuijinyicishangbaoshibaishuliang',
          },
          {
            title: '最近一次上报总量',
            count: res.data.data.lastReportSuccessedCount || '0',
            icon: 'icon-zuijinyicishangbaozongliang',
          },
        ];
      });
    },
    init() {
      this.$refs.tableList.init();
    },
    loadDataList(searchData) {
      let params = Object.assign(this.searchData, searchData);
      return this.$http.post(viewassets.queryDeviceInfoPageList, params).then((res) => {
        this.totalCount = res.data.data.total;
        return res.data;
      });
    },
    unreadStatistics() {
      // 未读信息接口统计
      this.$http.post(viewassets.unreadStatistics).then((res) => {
        this.countStatic = res.data.data;
      });
    },
    // 最新信息查询
    searchNow() {
      this.$http.post(viewassets.updateAllMessageStatus).then(() => {
        this.$refs.unqualifiedModal.open();
        this.countStatic = 0;
      });
    },
    // 表格选中
    selectAction(selection) {
      this.selectTable = selection;
    },
    // 上报 || 批量上报
    report(rows) {
      let content = '';
      if (rows) {
        content = '确定上报吗？';
        this.reportAll(content, rows);
      } else if (this.isAll) {
        content = `已选择${this.totalCount}条设备，确定上报吗？`;
        this.reportAll(content);
      } else {
        content = '已选择' + this.selectTable.length + '条设备，确定上报吗？';
        if (this.selectTable.length === 0) {
          this.$Message.warning('请勾选待上报设备');
        } else {
          this.reportAll(content);
        }
      }
    },
    reportAll(content, rows) {
      this.$UiConfirm({
        content: content,
        title: '数据上报',
      }).then(() => {
        this.searchData.isAll = this.isAll ? 1 : 0;
        let params = JSON.parse(JSON.stringify(this.searchData));
        if (rows) {
          params.deviceIds = [rows.deviceId];
        } else if (!this.isAll) {
          params.deviceIds = this.selectTable.map((val) => {
            return val.deviceId;
          });
        }
        // params.reportPlatformId = this.reportPlatformId
        this.$http.post(viewassets.batchReport, params).then(() => {
          this.$refs.tableList.search();
          this.startStatic();
          this.$Message.success('提交成功');
        });
      });
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    // 上报纠错
    reportError(row) {
      if (row.cascadeReportStatus == 1) {
        this.$UiConfirm({
          content: '该设备确定未成功上报吗？纠错后将删除全部历史上报记录！',
          title: '上报纠错',
        }).then(() => {
          this.$http.get(viewassets.errorCorrecting + row.id).then(() => {
            this.$refs.tableList.search();
            this.startStatic();
            this.$Message.success('纠错成功');
          });
        });
      }
    },
    viewRecord(row) {
      this.recordShow = true;
      this.recordData = row;
    },
    handleCheckStatus(row) {
      const flag = {
        1000: '待检测',
        '0000': '合格',
      };
      let msg = '';
      if (row) {
        msg = flag[row] ? flag[row] : '不合格';
      }
      return msg;
    },
    deviceModalShow(row, unqualified) {
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceUnqualified = unqualified || null;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailShow = true;
    },
    exportDevice() {
      this.customFilter = true;
      this.defaultCheckedList = [
        'deviceId',
        'id',
        'cascadeReportStatus',
        'recentlyReportStatus',
        'recentlyReportTime',
        'reportSuccessTime',
        'orgCode',
        'civilCode',
        'deviceName',
        'sbgnlx',
        'sbdwlx',
        'ipAddr',
        'macAddr',
        'longitude',
        'latitude',
        'sbcjqy',
        'phyStatus',
      ];
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = [
          ...data.data,
          ...[
            { propertyName: 'cascadeReportStatus', propertyColumn: '上报状态' },
            { propertyName: 'recentlyReportStatus', propertyColumn: '最近一次上报状态' },
            { propertyName: 'recentlyReportTime', propertyColumn: '最近一次上报时间' },
            { propertyName: 'reportSuccessTime', propertyColumn: '最近成功上报时间' },
          ],
        ];
        this.leftDisabled = (item) => {
          return (
            item.propertyName === 'deviceId' ||
            item.propertyName === 'id' ||
            item.propertyName === 'cascadeReportStatus' ||
            item.propertyName === 'recentlyReportStatus' ||
            item.propertyName === 'recentlyReportTime' ||
            item.propertyName === 'reportSuccessTime'
          );
        };
        this.rightDisabled = (item) => {
          return !(
            item.propertyName === 'deviceId' ||
            item.propertyName === 'id' ||
            item.propertyName === 'cascadeReportStatus' ||
            item.propertyName === 'recentlyReportStatus' ||
            item.propertyName === 'recentlyReportTime' ||
            item.propertyName === 'reportSuccessTime'
          );
        };
      } catch (err) {
        console.log(err);
      }
    },
    async exportExcel(propertyList) {
      try {
        this.$_openDownloadTip();
        this.exportDataLoading = true;
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          ids: this.selectTable.map((item) => item.id),
          isImportant: 0,
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(this.exportUrl, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportDataLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue']{
  .select-header .icon-font{
    color: #888888;
    &:hover {
      color: var(--color-switch-tab-active);
    }
  }
}
.assetsreport {
  overflow: hidden;
  position: relative;
  background-color: var(--bg-content);
  .tables {
    @{_deep} .ui-table {
      padding: 0 20px;
    }
  }
  .charts {
    margin: 10px 0 0 20px;
  }
  .operation {
    margin-bottom: 10px;
    height: 34px;
    line-height: 34px;
    .right-btn {
      button {
        margin-left: 12px;
      }
    }
  }
  .select-header {
    position: absolute;
    right: 20px;
    top: 20px;
    z-index: 11;
    color: var(--color-switch-tab);
    &:hover {
      color: var(--color-switch-tab-active);
    }
    .ivu-badge {
      @{_deep} .ivu-badge-count {
        height: 16px;
        line-height: 16px;
        border-radius: 14px;
      }
    }
  }
  .icon-xiaoxi2 {
    font-size: 19px;
    position: relative;
    left: -5px;
  }
  .icon-piliangshangbao {
    font-size: 12px;
    margin-right: 10px;
    position: relative;
    top: -1px;
  }
  .tooltipType {
    width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
