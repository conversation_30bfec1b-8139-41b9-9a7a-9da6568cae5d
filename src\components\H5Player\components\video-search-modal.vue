<template>
	<Modal ref="videoSearchModal" v-model="options.open" draggable :z-index="10000" sticky :mask="false" footer-hide :title="`${options.title}-${options.deviceName}`" width="550" :styles="{ top: 0, width: 'auto' }">
		<div class="top">
			<div class="datepicker-wrap">
				<hl-daterange v-model="queryTime.startDate" key="1" @affirm="dataChange"></hl-daterange>
				<div class="line"></div>
				<hl-daterange v-model="queryTime.endDate" key="2" @affirm="dataChange"></hl-daterange>
			</div>
			<Button class="mr-10" size="small" type="primary" @click="querysSearchList">查询</Button>
			<Button class="mr-10" size="small" v-if="isSearched && dataList.length" v-permission="['video-down']" @click="batchDownload">下载</Button>
			<!-- <Button @click="toMyDownload">我的下载</Button> -->
		</div>
		<div class="table">
			<ui-table :columns="columns" :data="dataList" :loading="loading">
				<template #time="{ row }">
					<div>{{ $dayjs(row.StartTime).format('YYYY-MM-DD HH:mm:ss') }} - {{ $dayjs(row.EndTime).format('YYYY-MM-DD HH:mm:ss') }}</div>
				</template>
				<template #action="{ row }">
					<div class="btn-tips">
						<ui-btn-tip v-permission="['video-history']" content="播放" icon="icon-start" class="mr-20 primary" @click.native="handlePlay(row)"></ui-btn-tip>
						<ui-btn-tip v-permission="['video-down']" content="下载" icon="icon-download" class="mr-20 primary" @click.native="handleDownload(row)"></ui-btn-tip>
					</div>
				</template>
			</ui-table>
		</div>
		<downloadApplyModal ref="downloadApplyModal" />

	</Modal>
</template>
<script>
	import { fetchVod, startVod } from '@/api/player'
	import { mapActions, mapGetters, mapMutations } from 'vuex'
	import downloadApplyModal from './video-download-apply-model.vue'
	import hlDaterange from '@/components/hl-daterange/index.vue';


	export default {
		components: {
			downloadApplyModal,
			hlDaterange
		},
		props: {
			options: {
				type: Object,
				default: () => { }
			},
			videoObj: {
				type: Object,
				default: () => { }
			}
		},
		data() {
			return {
				loading: false,
				dataList: [],
				columns: [
					{ title: '序号', width: 70, type: 'index', key: 'index' },
					{ title: '时间段', slot: 'time' },
					{ title: '操作', slot: 'action', width: 100 }
				],
				queryTime:{
					startDate:'',
					endDate: ''
				},
				isSearched: false
			}
		},
		beforeDestroy() {
			this.options.open = false
		},
		deactivated() {
			this.options.open = false
		},
		computed: {
			...mapGetters({
				userInfo: "userInfo",
				vodSearchTime: "player/getVodSearchTime"
			})
		},
		methods: {
			...mapActions({
				download: 'my-download/download',
			}),
			...mapMutations({ setVodSearchTime: 'player/setVodSearchTime' }),
			dataChange() {
				this.isSearched = false
			},
			show(flag) {
				this.options.open = flag
				if (flag) {
					console.log(this.vodSearchTime)
					// if (!this.queryTime.startDate || !this.queryTime.endDate) {
						if (this.vodSearchTime.length == 2) {
							this.queryTime.startDate = this.vodSearchTime[0]
							this.queryTime.endDate = this.vodSearchTime[1]
						} else {
							let date = new Date()
							let startTime = new Date(date.getTime() - 2 * 60 * 60 * 1000)
							let endTime = date
							this.queryTime.startDate = this.$dayjs(startTime).format('YYYY-MM-DD HH:mm:ss')
							this.queryTime.endDate = this.$dayjs(endTime).format('YYYY-MM-DD HH:mm:ss')
						}
					// }
					this.searchList().then(arr => {
						this.isSearched = true
						let item = arr[0]
						this.handlePlay(item)
					})
					this.$nextTick(() => {
						// 显示在左下角
						let dragDom = this.$refs.videoSearchModal.$el.querySelector('.ivu-modal-content-drag')
						let left = 0
						let top = window.innerHeight - dragDom.offsetHeight
						dragDom.style.left = left + 'px'
						dragDom.style.top = top + 'px'
					})
				}
			},
			querysSearchList() {
				this.searchList().then(arr => {
					this.isSearched = true
					let item = arr[0]
					this.handlePlay(item)
				})
			},
			searchList() {
				if (!this.queryTime.startDate || !this.queryTime.endDate) {
					this.dataList = []
					this.$Message.error('开始时间和结束时间不能为空')
					return
				}
				// this.setVodSearchTime([this.queryTime.startDate, this.queryTime.endDate])
				return new Promise((resolve) => {
					this.loading = true
					this.dataList = []
					if (vodType == 'pvg67' || vodType == 'pvgplus') {
						let record = {
							begintime: this.$dayjs(this.queryTime.startDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
							channel: this.options.stream.channel,
							desencode: this.options.stream.desencode,
							devicetype: vodType,
							endtime: this.$dayjs(this.queryTime.endDate).format('YYYY-MM-DD HH:mm:ss:SSS'),
							ip: this.options.stream.ip,
							password: this.options.stream.password,
							port: this.options.stream.port,
							streamtype: "vod",
							title: this.options.stream.title,
							user: this.options.stream.user,
							vod: vodStorage == "device" ? 1 : 0
						}
						this.videoObj.queryRecord(record, (res) => {
							try {
								this.loading = false
								if (res.error != 0) {
									this.$Message.error("查询录像失败！" + this.videoObj.errorCodeToString(res.error));
									this.dataList = []
									resolve([])
									return
								}
								let obj = JSON.parse(res.info);
								if (!obj) {
									this.dataList = []
									resolve([])
									return
								}
								this.dataList = obj.map(v => {
									v.StartTime = v.begintime
									v.EndTime = v.endtime
									return v
								})
								resolve(this.dataList)
							} catch (e) { }
						})
					} else {
						let params = {
							endtime: this.queryTime.endDate,
							starttime: this.queryTime.startDate,
							channel: this.options.deviceId,
							download: true,
							storage: vodStorage
						}
						fetchVod(params)
							.then(res => {
								if (res.code === 200) {
									this.dataList = res.RecordList || []
									resolve(res.RecordList || [])
								}
							})
							.finally(() => {
								this.loading = false
							})
					}
				})
			},
			handlePlay(row) {
				if (row) {
					if (vodType == 'pvg67' || vodType == 'pvgplus') {
						let record = {
							begintime: this.$dayjs(row.StartTime).format('YYYY-MM-DD HH:mm:ss:SSS'),
							channel: this.options.stream.channel,
							desencode: this.options.stream.desencode,
							devicetype: vodType,
							endtime: this.$dayjs(row.EndTime).format('YYYY-MM-DD HH:mm:ss:SSS'),
							ip: this.options.stream.ip,
							password: this.options.stream.password,
							port: this.options.stream.port,
							streamtype: "vod",
							title: this.options.stream.title,
							user: this.options.stream.user,
							vod: vodStorage == "device" ? 1 : 0
						}
						this.$emit('playvod', record)
					} else {
						let deviceID = vodStorage == "platform" ? row.Device : row.DownloadUrl.split('.')[5]
						startVod({
							channel: row.ID,
							device: deviceID,
							starttime: row.StartTime,
							endtime: row.EndTime,
							hls: true,
							storage: vodStorage
						}).then(res => {
							this.$emit('playvod', {
								duration: new Date(row.EndTime).getTime() - new Date(row.StartTime).getTime(),
								begintime: new Date(row.StartTime).format('yyyyMMddhhmmss') + '000',
								endtime: new Date(row.EndTime).format('yyyyMMddhhmmss') + '000',
								url: res[vodType.toUpperCase()],
								streamid: res.streamid,
								isCustomControl: vodStorage == "platform" ? false : true, // 设备录像，通过联网平台接口来控制播放进度
								streamtype: 'vod',
								vod: vodStorage == "device" ? 1 : 0,
								devicetype: vodType,
								title: this.options.deviceName,
							})
						})
					}
				} else {
					this.$emit('playvod', '')
				}
			},
			handleDownload(row) {
				const downloadUrls = [{
					StartTime: row.StartTime,
					EndTime: row.EndTime,
					DownloadUrl: row.DownloadUrl
				}]
				if (Number(this.userInfo.shortNumber) >= 60) {
					this.$emit('downloadVod', downloadUrls)
				} else {
					const {StartTime, EndTime} = {...row}
					this.$refs.downloadApplyModal.show({ downloadUrls, StartTime, EndTime, ...this.options })
				}
			},
			batchDownload() {
				if (!this.dataList.length) return
				let downloadUrls = this.dataList.map(v => {
					let obj = {
						StartTime: v.StartTime,
						EndTime: v.EndTime,
						DownloadUrl: v.DownloadUrl
					}
					return obj
				})
				if (Number(this.userInfo.shortNumber) >= 60) {
					this.$emit('downloadVod', downloadUrls)
				} else {
					this.$refs.downloadApplyModal.show({ downloadUrls, StartTime: this.queryTime.startDate, EndTime: this.queryTime.endDate, ...this.options })
				}
			},
			toMyDownload() {
				this.$router.push({
					name: 'mydownload',
				})
			}
		}
	}
</script>
<style lang="less" scoped>
	.top {
		display: flex;
		margin-bottom: 10px;
	}
	.table {
		height: 200px;
		.ui-table {
			height: 100%;
		}
	}
	/deep/ .ivu-modal-header {
		padding: 6px 10px;
		background: rgba(211, 215, 222, 0.3);
		.ivu-modal-header-inner {
			font-weight: 700;
			color: rgba(0, 0, 0, 0.9);
			font-size: 16px;
		}
	}
	/deep/ .ivu-modal-body {
		padding: 10px;
	}
	/deep/ .ivu-modal-close {
		top: 0;
		right: 2px;
	}
	.datepicker-wrap{
        display: flex;
        align-items: center;
		line-height: 26px;
        .line{
            height: 3px;
            width: 20px;
            background: #d2d8db;
            margin: 0 5px;
        }
        .hl-btn{
            color: #2C86F8;
            margin-left: 10px;
            cursor: pointer;
        }
		margin-right: 10px;
    }
</style>
