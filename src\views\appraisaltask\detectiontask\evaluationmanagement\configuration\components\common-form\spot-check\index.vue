<template>
  <div>
    <FormItem label="">
      <RadioGroup v-model="whole" @on-change="handleDetectPhyStatus">
        <Radio :label="1" class="mr-sm">统一配置抽检设备数量</Radio>
        <Radio :label="0">自定义配置抽检设备数量</Radio>
      </RadioGroup>
    </FormItem>
    <!-- 之前写的统一配置抽检设备数量，所以并未改动插槽插入 -->
    <slot name="unify" :isunify="!!whole"></slot>
    <!-- 自定义配置抽检设备数量 -->
    <FormItem label="抽检对象和抽取设备数量" key="chooseOrg" prop="orgList" v-if="!whole">
      <Button @click="goConfig">
        <span v-if="!!Object.keys(orgCountMap).length">已配置{{ Object.keys(orgCountMap).length }}个抽检设备</span>
        <span v-else>去配置</span>
      </Button>
    </FormItem>
    <!-- v-model原理写，语法糖暂不知为何不能用 -->
    <config-define
      :model-value="selfConfigShow"
      @update:modelValue="(newValue) => (selfConfigShow = newValue)"
      v-bind="$attrs"
      @selfSelectedDevice="alreadyDefine"
    ></config-define>
  </div>
</template>
<script setup>
import ConfigDefine from './config-define.vue';
import { useAttrs, ref } from 'vue';
// const { originForm } = defineProps(['originForm','flatTreeData'])
const attrs = useAttrs();
const emit = defineEmits(['updateParams']);
const whole = 'whole' in attrs['origin-form'] && attrs['origin-form'].whole ? ref(1) : ref(0);
const selfConfigShow = ref(false);
const orgCountMap =
  !!whole.value || (!'orgCountMap') in attrs['origin-form'] ? ref({}) : ref(attrs['origin-form'].orgCountMap);
const handleDetectPhyStatus = (whole) => {
  orgCountMap.value = {};
  if ('whole' in attrs['origin-form'] && !!whole === attrs['origin-form'].whole) {
    orgCountMap.value = 'orgCountMap' in attrs['origin-form'] ? attrs['origin-form'].orgCountMap : {};
  }
  let params = { whole: !!whole, orgCountMap: orgCountMap.value };
  emit('updateParams', params);
};
const goConfig = () => {
  selfConfigShow.value = !selfConfigShow.value;
};
const alreadyDefine = (data) => {
  selfConfigShow.value = false;
  orgCountMap.value = data;
  // attrs.originForm.orgCountMap = data
  let params = { whole: !!whole.value, orgCountMap: orgCountMap.value };
  emit('updateParams', params);
};
</script>
