<template>
	<div class="map-box">
		<div :id="mapId" class="map"></div>

		<!-- 设备治理 -->
		<div ref="deviceDom" class="deviceDom">
			<div class="header">{{ currentDevice.name }}</div>
			<div class="formBox">
				<Form ref="formData" :model="currentDevice" class="form" @submit.native.prevent>
                    <FormItem label="设备名称:" prop="name">
						<Input v-model="currentDevice.name" size="small" placeholder="请输入"></Input>
					</FormItem>
					<FormItem label="经度:" prop="longitude">
						<Input v-model="currentDevice.longitude" size="small" placeholder="请输入"></Input>
					</FormItem>
					<FormItem label="纬度:" prop="latitude">
						<Input v-model="currentDevice.latitude" size="small" placeholder="请输入"></Input>
					</FormItem>
					<FormItem label="监控类型:" prop="childType" v-if="currentDevice.type == 1">
						<RadioGroup v-model="currentDevice.childType">
							<Radio :label="0">枪机</Radio>
							<Radio :label="1">球机</Radio>
							<Radio :label="2">半球机</Radio>
						</RadioGroup>
					</FormItem>
					<FormItem class="footer">
						<Button class="mr-20" type="primary" size="small" @click="handleUpdate">保存</Button>
						<Button type="default" size="small" @click="handleCancle">取消</Button>
					</FormItem>
                </Form>
			</div>
		</div>
	</div>
</template>

<script>
	import { NPGisMapMain } from '@/map/map.main'
	import { mapGetters, mapActions } from 'vuex'
	import { updateDevice } from '@/api/player'
	let mapMainDevice = null
	let deviceInfoWindow = null
	let deviceMarkerLayer = null
	export default {
		props: {
			// 地图图层配置信息
			mapLayerConfig: {
				type: Object,
				default: () => {
					return {
						tracing: false, // 是否需要刻画轨迹
						showStartPoint: false, // 是否显示起点终点图标
						selectionResult: false, // 是否显示框选结果弹框
						resultOrderIndex: false, // 搜索结果排序,
						showLatestLocation: false // 显示地图最新位置
					}
				}
			},
		},
		computed: {
			...mapGetters({
				mapConfig: 'common/getMapConfig',
				mapStyle: 'common/getMapStyle',
				mapObj: 'systemParam/mapObj',
				globalObj: 'systemParam/globalObj',
				resourceCoverage: 'map/getResourceCoverage',
			})
		},
		data() {
			return {
				mapId: 'mapId' + Math.random(),
				currentDevice: {}
			}
		},
		deactivated() {
			
		},
		async mounted() {
			await this.getMapConfig()
		},
		methods: {
			...mapActions({
				setMapConfig: 'common/setMapConfig',
				setMapStyle: 'common/setMapStyle'
			}),
			async getMapConfig() {
				try {
					await Promise.all([this.setMapConfig(), this.setMapStyle()])
					this._initMap(this.mapConfig)
				} catch (err) {
					console.log(err)
				}
			},
			_initMap(data, style) {
				this.$nextTick(() => {
					// 配置初始化层级
					mapMainDevice = new NPGisMapMain()
					const mapId = this.mapId
					mapMainDevice.init(mapId, data, style)
					this.configDefaultMap();
					this.$emit('loaded')
				})
			},
			/**
			 * 系统配置的中心点和层级设置
			 */
			configDefaultMap() {
				let mapCenterPoint = this.globalObj.mapCenterPoint
				let mapCenterPointArray = !!mapCenterPoint ? this.globalObj.mapCenterPoint.split('_') : ''
				let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14
				let point = mapMainDevice.map.getCenter()
				if (!!mapCenterPointArray.length) {
					point = new NPMapLib.Geometry.Point(parseFloat(mapCenterPointArray[0]), parseFloat(mapCenterPointArray[1]));
				}
				mapMainDevice.map.centerAndZoom(point, mapLayerLevel)
			},
			getIcon(point) {
				switch (point.type) {
					case '1':
						if (point.childType == '1' || point.childType == '2') {
							return require('@/assets/img/map/mapPoint/map-qiuji.png')
						}else {
							return require('@/assets/img/map/mapPoint/map-qiangji.png')
						}
					case '11':
						return require('@/assets/img/map/mapPoint/map-face.png')
					case '2':
						return require('@/assets/img/map/mapPoint/map-vehicle.png')
					case '3':
						return require('@/assets/img/map/mapPoint/map-wifi.png')
					case '5':
						return require('@/assets/img/map/mapPoint/map-rfid.png')
					case '4':
						return require('@/assets/img/map/mapPoint/map-electric.png')
					default:
						return require('@/assets/img/map/mapPoint/map-qiangji.png')
					}
			},
			// 设备治理
			addMarkers(item) {
				this.currentDevice = {...item}
				debugger
				let device = {...item}
				device.images = this.getIcon(device)
				device.lon = device.lon || device.longitude || mapMainDevice.map.getCenter().lon;
				device.lat = device.lat || device.latitude || mapMainDevice.map.getCenter().lat;
				if (deviceMarkerLayer) {
					mapMainDevice.map.removeLayer(deviceMarkerLayer)
					deviceMarkerLayer = null
				}
				this._initMarkers(
					"pointMark",
					[device],
					{
						click: (m, callBack) => {},
						mouseover: () => {},
						draging: m => {
							let _position = m.getPosition();
							this.currentDevice.latitude = _position.lat.toFixed(6)
							this.currentDevice.longitude = _position.lon.toFixed(6)
							deviceInfoWindow.setPosition(_position)
						},
						dragend: m => {}
					},
					m => {
						this.showInfoWin(m[0])
						mapMainDevice.map.centerAndZoom(new NPMapLib.Geometry.Point(m[0].ext.lon, m[0].ext.lat), mapMainDevice.map.getMaxZoom())
					},
					{
						enableEdit: true,
						isShowTitle: false
					})
			},
			/**
			 * 地图普通marker撒点
			 * @param layerName - 撒点的图层
			 * @param data - 待撒点的业务数据
			 * @param evOpts - 事件配置
			 * @param callBack - 回调函数
			 * @param opts - 事件配置
			 */
			_initMarkers(layerName = "", data = [], evOpts = {}, callBack, opts) {
				if (data.length === 0) return;
				let markers = [];  
				//遍历数据，包装marker
				data.map((item, index) => {
					let { images } = item;
					let size = new NPMapLib.Geometry.Size(24, 27);
					let icon = new NPMapLib.Symbols.Icon(images, size)
					let marker = new NPMapLib.Symbols.Marker(item)
					marker.setIcon(icon)
					marker.ext = item
					markers.push(marker);
				});
				if (!deviceMarkerLayer) {
					deviceMarkerLayer = new NPMapLib.Layers.OverlayLayer(layerName)
					mapMainDevice.map.addLayer(deviceMarkerLayer)
				}
				deviceMarkerLayer.addOverlays(markers);
				this._addMarkerEvent(markers, evOpts, opts)
				if (callBack) callBack(markers)
			},
			/**
			 * 给marker绑定事件和设置是否可编辑 适配2D只能在marker添加到地图后才能给marker添加事件和设置编辑状态
			 * @param {*} markers 
			 * @param {*} evOpts - 事件配置
			 * @param {*} opts marker公共配置
			 */
			_addMarkerEvent(markers, evOpts, opts) {
				let { enableEdit = false } = opts;
				markers.forEach(marker => {
					//标记marker是否可以编辑/移动
					if (enableEdit) marker.enableEditing();
					Object.keys(evOpts).forEach(event => {
						marker.addEventListener(event, m => {
							evOpts[event](m)
						})
					})
				});
			},
			// 设备治理-窗口
			showInfoWin(marker) {
				let { lat, lon } = marker.ext;
				const point = new NPMapLib.Geometry.Point(lon, lat)
				this.$nextTick(() => {
					const dom = this.$refs['deviceDom'];
					let htmlFontSize = window.getComputedStyle(window.document.documentElement)['font-size']
					let left = (-200) / 192 * parseFloat(htmlFontSize);
					let top = (-380) / 192 * parseFloat(htmlFontSize);
					const opts = {
						offset: new NPMapLib.Geometry.Size(left, top), // 信息窗位置偏移值
						iscommon: true, // 是否为普通的信息窗，普通的不带箭头,默认false
						enableCloseOnClick: false, // 默认false，是否开启改变地图（移动或缩放）关闭信息窗口（默认关闭）
						autoSize: true, // 默认true, 窗口大小是否自适应
						isAdaptation: false, // 默认true, 信息窗打开时，地图是否平滑移动，默认不平滑移动
						positionBlock: {
							// 箭头样式
							imageSrc: require('@/assets/img/map/triangle.png'),
							imageSize: new NPMapLib.Geometry.Size(20, 10), //NPMapLib.Geometry.Size
							offset: new NPMapLib.Geometry.Size(-0, 80)
						}
					}
					deviceInfoWindow = new NPMapLib.Symbols.InfoWindow(point, null, null, opts)
					deviceInfoWindow.setContentDom(dom)
					mapMainDevice.map.addOverlay(deviceInfoWindow)
					deviceInfoWindow.open(null, false)
					deviceInfoWindow.updatePosition()
				})
			},
			// 设备治理-更新
			handleUpdate() {
				let { name, longitude, latitude, childType, id } = { ...this.currentDevice }
				updateDevice({
					childType,
					id,
					latitude,
					longitude,
					name}).then(res => {
						if (res.code == 200) {
							this.closeMarker()
							this.$Message.success('更新成功')
						} else {
							this.$Message.error(res.msg)
						}
					})
			},
			handleCancle() {
				this.closeMarker()
			},
			closeMarker() {
				if (deviceMarkerLayer) {
					mapMainDevice.map.removeLayer(deviceMarkerLayer)
					deviceMarkerLayer = null
				}
				if (deviceInfoWindow) deviceInfoWindow.close()
				this.currentDevice = {}
			}
		},
		beforeDestroy() {
			if (mapMainDevice) {
				this.closeMarker()
				mapMainDevice.destroy()
				mapMainDevice = null
			}
		}
	}
</script>

<style lang="less" scoped>
	.map-box {
		width: 100%;
		height: 100%;
		overflow: hidden;
		position: relative;

		.map {
			height: 100%;
			width: 100%;
			position: relative;
		}

		.deviceDom {
			width: 400px;
			background: #fff;
			.header {
				height: 36px;
				line-height: 36px;
				background-color: #2c86f8;
				color: #fff;
				border-radius: 2px 2px 0 0;
				font-size: 14px;
				font-weight: bold;
				padding: 0 10px;
			}
			.formBox {
				padding: 20px 20px 1px 20px;
				.ivu-form-item {
					display: flex;
					/deep/ .ivu-form-item-label {
						width: 80px;
					}
					/deep/ .ivu-form-item-content {
						flex: 1;
					}
				}
			}
			.footer {
				/deep/ .ivu-form-item-content {
					display: flex;
					justify-content: center;
				}
			}
		}
	}
	/deep/ #npgis_GroupDiv {
		overflow: inherit !important;
	}
	/deep/.olPopupContent {
		overflow: inherit !important;
	}
	/deep/.domModal {
		.ivu-modal-body {
			padding: 0 !important;
		}
	}
	/deep/ .ivu-modal-close {
		top: 5px;
		.ivu-icon-ios-close {
			color: #fff;
		}
	}
	/deep/ .ivu-modal-header {
		background: rgb(44, 134, 248);
		border-bottom: none;
		padding: 10px;
		.ivu-modal-header-inner {
			font-size: 14px;
			font-weight: 700;
			color: #fff;
		}
	}
</style>
