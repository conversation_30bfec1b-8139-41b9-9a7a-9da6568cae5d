<!--
    * @FileDescription: ismi设备/ mac设备
    * @Author: H
    * @Date: 2023/10/07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-17 15:39:00
 -->
<template>
    <div class="equipment_page">
        <div class="list-card box-2">
            <div class="content">
                <!-- <div class="collection">
                    <div class="bg"></div>
                    <ui-btn-tip class="collection-icon" v-if="itemList.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(itemList, 2)" />
                    <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(itemList, 1)" />
                </div> -->
                <p class='identifier-content'>
                    <img v-show="deviceType == 7" class="img-icon" src="@/assets/img/icons/icon-wifi.png" alt="" />
                    <img v-show="deviceType == 8" class="img-icon" src="@/assets/img/icons/icon-RFID.png" alt="" />
                    <img v-show="deviceType == 9" class="img-icon" src="@/assets/img/icons/icon-electric.png" alt="" />
                    <img v-show="deviceType == 16" class="img-icon" src="@/assets/img/icons/icon-etc.png" alt="" />
                    <img v-show="deviceType == 6" class="img-icon" src="@/assets/img/icons/icon-gps.png" alt="" />
                    {{itemList.deviceId}}
                </p>
                <div class="bottom-info">
                    <time>
                        <Tooltip content="设备名称" placement="right" transfer theme="light">
                        <i class="iconfont icon-leixing"></i>
                        </Tooltip>
                        <span class="ellipsis" v-show-tips v-html="itemList.deviceName"></span>
                    </time>
                    <p>
                        <Tooltip content="位置" placement="right" transfer theme="light">
                            <i class="iconfont icon-location"></i>
                        </Tooltip>
                        <span class="ellipsis" v-show-tips>{{itemList.detailAddress}}</span>
                    </p>
                </div>
                <!-- <div class="operate-bar">
                    <p class="operate-content">
                        <ui-btn-tip content="分析" icon="icon-fenxi"/>
                        <ui-btn-tip content="布控" icon="icon-dunpai" transfer/>
                    </p>
                </div> -->
            </div>
        </div>
    </div>
</template>

<script>
import { addCollection, deleteMyFavorite } from '@/api/user'
export default {
    props:{
        itemList:{
            type: Object,
            default: () =>{
                return {}
            }
        },
        deviceType: {
            type: [String, Number],
            default: 7
        }
    },
    components: {
    },
    data(){
        return {

        }
    },
    methods: {
        collection(data, flag) {
            var param = {
                favoriteObjectId: data.deviceId,
                favoriteObjectType: this.deviceType,
            }
            if (flag == 1) {
                addCollection(param).then(res => {
                    this.$Message.success("收藏成功");
                    this.itemList.myFavorite = 2;
                })
            } else {
                deleteMyFavorite(param).then(res => {
                    this.$Message.success("取消收藏成功");
                    this.itemList.myFavorite = 1;
                })
            }
        }
    },
}
</script>

<style lang='less' scoped>
.equipment_page {
  .list-card {
    position: relative;
    background-color: #f9f9f9;
    margin-bottom: 10px;
    height: min-content;
    box-sizing: border-box;
        .operate-bar {
            height: 30px;
            background: linear-gradient(
                90deg,
                rgba(87, 187, 252, 0.8) 0%,
                #2c86f8 100%
            );
            border-radius: 0px 0px 4px 0px;
            position: absolute;
            right: -100%;
            transition: all 0.3s;
            bottom: 0;
            transform: skewX(-20deg);
            .operate-content {
                padding: 0 5px;
                transform: skewX(20deg);
                height: 100%;
                display: flex;
                align-items: center;
                color: #fff;
                /deep/ .ivu-tooltip-rel {
                    padding: 6px;
                }
            }
        }
        .collection {
            width: 30px;
            height: 30px;
            position: absolute;
            // background: #F29F4C;
            z-index: 2;
            top: 3px;
            right: 3px;
            .collection-icon {
                position: absolute;
                top: -1px;
                right: 1px;
                /deep/ .iconfont {
                    font-size: 14px;
                    color: #fff;
                }
                /deep/ .icon-shoucang {
                    color: #888888 !important;
                    text-shadow: 0px 1px 0px #e1e1e1;
                }
                /deep/ .icon-yishoucang {
                    color: #f29f4c !important;
                }
            }
        }
        .paddingIcon {
            top: 13px;
            right: 13px;
        }
    }
    .empty-card-1 {
        width: 10.7%;
    }
    .empty-card-2 {
        width: 12.2%;
    }
    .box-2 {
        width: 12.5%;
        height: 126px;
        padding: 3px;
        .content {
            overflow: hidden;
            position: relative;
            height: 100%;
            box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
            border-radius: 4px;
            border: 1px solid #d3d7de;
            &:hover {
                &:before {
                    border-color: #2c86f8;
                }
                .operate-bar {
                    right: -6px;
                    bottom: -1px;
                }
            }
        }
        .identifier-content {
            display: flex;
            color: #7263e1;
            font-size: 16px;
            align-items: center;
            border-bottom: 1px solid #d3d7de;
            padding: 5px 10px;
            font-weight: bold;
            .img-icon {
                width: 20px;
                height: 20px;
                margin-right: 7px;
            }
        }
        .bottom-info {
            padding: 10px;
            time {
                margin-bottom: 4px;
            }
            time,
            p {
                display: flex;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                width: 100%;
                line-height: 18px;
                color: rgba(0, 0, 0, 0.8);
                font-size: 12px;
                margin-bottom: 4px;
                span {
                    flex: 1;
                }
                .iconfont {
                    margin-right: 3px;
                    font-size: 12px;
                    color: #888;
                }
            }
        }
    }
}
</style>
