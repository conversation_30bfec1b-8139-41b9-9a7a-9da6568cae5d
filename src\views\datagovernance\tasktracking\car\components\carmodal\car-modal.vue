<template>
  <div class="more-dialog" :class="curIndex == 6 ? 'more-dialog-all' : ''">
    <ui-modal v-model="modalShow" ref="modal" :title="modalTitle" width="95%" :footerHide="true">
      <!-- echats插槽 -->
      <slot name="echarts"></slot>
      <tagView class="mt10" ref="tagView" :list="['图像模式', '设备模式']" @tagChange="tagChange1" />

      <carModalImage
        v-show="modelTag == 0"
        ref="carModalImage"
        :curIndexShow="curIndexShow"
        :curIndex="curIndex"
        :infoModalShow="infoModalShow"
        :bigImgShow="bigImgShow"
        :modalTitle="modalTitle"
        :titleChild="titleChild"
        :tabsList="tabsList"
        @abnormalDetail="abnormalDetail"
      >
        <template slot="Introduction">
          <!-- 数量简介插槽 -->
          <slot name="Introduction"></slot>
        </template>
      </carModalImage>
      <div v-if="modalShow" style="width: 100%">
        <faceShebeiList
          v-show="modelTag == 1"
          ref="faceShebeiList"
          :minusHeight="minusHeight"
          v-model="modelTag"
          :listObj="listObj"
          @unqualified="unqualified"
          :importApi="importApi"
        />
      </div>
    </ui-modal>
  </div>
</template>
<script>
import carThrem from '@/config/api/car-threm';
export default {
  name: 'moreDialog',
  props: {
    infoModalShow: {
      type: Boolean,
      default: false, // 详情按钮
    },
    curIndexShow: {
      type: Boolean,
      default: false, // 是否展示changes
    },
    curIndex: {
      type: Number,
      default: 1, // 当前展示步骤
    },
    modalTitle: {
      type: String,
      default: '图像抓拍时间准确性检测', // 弹框名称
    },
    titleChild: {
      type: String,
      default: '抓拍时间异常', // 详情弹框名称
    },
    bigImgShow: {
      type: Boolean,
      default: false, // 是否可点击大图
    },
    tabsList: {
      type: Object,
      default() {
        return { '0': [], '1': [] }; // 数据输出类型
      },
    },
    listObj: {
      type: Object,
      default() {
        return {}; // 设备模式
      },
    },
  },
  data() {
    return {
      importApi: false,
      modalShow: false,
      modelTag: 0, // 设备模式,图像模式
    };
  },
  created() {},
  methods: {
    // 初始化
    async showModal() {
      this.$refs.carModalImage.searchData.deviceIds = [];
      this.importApi = { api: carThrem.importQueryVehicleList };
      if (this.$refs.tagView) {
        this.$refs.tagView.curTag = 0;
      }
      this.modelTag = 0;
      this.modalShow = true;
      this.$nextTick(() => {
        this.$refs.carModalImage.showModal(true);
        this.$refs.faceShebeiList.info(true);
      });
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
      // if (this.modelTag == 1) {
      //   this.$nextTick(() => {
      //   	this.$refs.faceShebeiList.info(true)
      //   })
      // } else {
      //   this.$nextTick(() => {
      //   	this.$refs.carModalImage.showModal(true)
      //   })
      // }
    },
    // 数据输出详情返回
    abnormalDetail(item, searchData) {
      this.$emit('abnormalDetail', item, searchData);
    },
    // 不合格图片改变图像模式参数
    async unqualified(val) {
      this.modelTag = 0;
      this.$refs.tagView.curTag = 0;
      this.$nextTick(() => {
        this.$refs.carModalImage.searchData.deviceIds = [val.deviceId];
        this.$refs.carModalImage.showModal();
      });
    },
  },
  watch: {
    value: {
      handler(val) {
        this.modalShow = val;
      },
      immediate: true,
    },
  },
  computed: {
    minusHeight() {
      let minusHeight = 378;
      switch (this.curIndex) {
        case 4:
          minusHeight = 316;
          break;
        case 5:
          minusHeight = 316;
          break;
        case 6:
          minusHeight = 574;
          break;
        default:
          minusHeight = 378;
          break;
      }
      return minusHeight;
    },
  },
  components: {
    carModalImage: require('./car-modal-image.vue').default,
    tagView: require('./tags').default,
    faceShebeiList: require('@/views/datagovernance/tasktracking/components/face-shebei-list').default,
  },
};
</script>
<style lang="less" scoped>
.more-dialog-all {
  /deep/ .ivu-modal {
    // top: 0.2rem;
    // padding-bottom: 20px;
  }
}
/deep/ .ivu-modal-body {
  color: #fff;
  padding: 20px 10px 20px 20px;
}
.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  max-width: 188px;
  .item {
    height: 100%;
    background: #0f2f59;
    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }
  }
}

.group-message {
  margin: 0 auto;
  padding: 4px 15px 0 15px;
  color: #afbcd4;
  .group-text {
    width: 140px;
  }
  i {
    font-size: 12px;
  }
}
.marginP {
  margin-bottom: 6px;
  margin-top: 4px;
}
.mr20 {
  margin-right: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mt20 {
  margin-top: 20px;
}
.mt10 {
  margin-top: 10px;
}
</style>
