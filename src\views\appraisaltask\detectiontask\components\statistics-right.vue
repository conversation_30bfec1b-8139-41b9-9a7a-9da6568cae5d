<template>
  <div class="information-statistics">
    <ul class="statistics-ul">
      <li v-for="(item, index) in statisticsList" :key="index">
        <div class="monitoring-data">
          <span class="icon" :class="item.iconColor">
            <i class="icon-font f-30 base-text-color" :class="[item.icon]"></i>
          </span>
          <div class="information-data">
            <p>
              <span>{{ item.name }}</span>
              <span class="statistic-num">
                <countTo
                  :class="item.textColor"
                  class="f-18 ml-xs"
                  :startVal="0"
                  :endVal="item.value || 0"
                  :duration="3000"
                ></countTo>
              </span>
            </p>
            <p>
              <Progress :percent="item.progressNum" :stroke-width="5" :stroke-color="item.progressColor" />
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'statistic-card',
  props: {
    statisticsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      cardObj: {},
    };
  },
  mounted() {},
  methods: {},
  watch: {},
  components: {
    countTo: require('vue-count-to').default,
  },
};
</script>

<style scoped lang="less">
.information-statistics {
  display: flex;
  width: 100%;
  height: 100%;
  // .color7 {
  //   color: #19d5f6 !important;
  // }
  .li-bg7 {
    background: var(--bg-sub-content) !important;
  }

  .icon-bg3 {
    // background: linear-gradient(180deg, #c122f8 0%, #c122f8 100%) !important;
    background: linear-gradient(to bottom, #c122f8, #c122f8);
  }
  .icon-bg4 {
    // background: linear-gradient(360deg, #26d82c 0%, #127d0a 100%) !important;
    background: linear-gradient(to bottom, #26d82c, #127d0a);
  }
  .icon-bg5 {
    // background: linear-gradient(360deg, #b58e0f 0%, #bb6603 100%) !important;
    background: linear-gradient(to bottom, #b58e0f, #bb6603);
  }
  .icon-bg6 {
    // background: linear-gradient(180deg, #f24e2c 0%, #772c0a 100%) !important;
    background: linear-gradient(to bottom, #f24e2c, #772c0a);
  }

  .color3 {
    color: #b113b1 !important;
  }
  .color4 {
    color: #26d82c !important;
  }
  .color5 {
    color: #b58e0f !important;
  }
  .color6 {
    color: #f24e2c !important;
  }

  .statistics-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    li {
      flex: 1;
      height: 100px;
      display: flex;
      // margin-right: 10px;

      align-items: center;
      background: var(--bg-sub-content) !important;
      &:nth-child(4) {
        margin-right: 0;
        .information-data {
          border-right: none !important;
        }
        @{_deep}.ivu-progress-inner {
          background-color: rgba(33, 13, 7, 1) !important;
        }
        @{_deep}.ivu-progress-text-inner {
          color: #f24e2c !important;
        }
      }
      &:nth-child(3) {
        @{_deep}.ivu-progress-inner {
          background-color: rgba(93, 62, 7, 1) !important;
        }
        @{_deep}.ivu-progress-text-inner {
          color: #b58e0f !important;
        }
      }
      &:nth-child(2) {
        @{_deep}.ivu-progress-inner {
          background-color: rgba(7, 69, 7, 1) !important;
        }
        @{_deep}.ivu-progress-text-inner {
          color: #26d82c !important;
        }
      }
      &:nth-child(1) {
        @{_deep}.ivu-progress-inner {
          background-color: rgba(84, 36, 108, 1) !important;
        }
        @{_deep}.ivu-progress-text-inner {
          color: #b113b1 !important;
        }
      }
      .monitoring-data {
        width: 100%;
        height: 100px;
        line-height: 100px;
        display: flex;
        align-items: center;

        .icon {
          display: inline-block;
          height: 50px;
          width: 50px;
          line-height: 50px;
          text-align: center;
          margin-left: 40px;
          border-radius: 50%;
          .f-30 {
            font-size: 28px;
          }
        }
        .information-data {
          border-right: 1px solid #2b5388;
          margin-left: 14px;
          text-align: left;
          flex: 1;

          p {
            white-space: nowrap;
            font-style: normal;
            height: 20px;
            line-height: 20px;
            color: #f5f5f5;
            font-size: 12px;
          }
          @{_deep}.ivu-progress-text {
            display: block;
            margin-left: 0px;
          }
          @{_deep}.ivu-progress-outer {
            padding-right: 35px;
          }
          .statistic-num {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
            vertical-align: sub;
          }
        }
      }
    }
  }
}
</style>
