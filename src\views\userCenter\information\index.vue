<template>
  <div class="container">
    <div class="title-text card-border-color">
      <div class="title-text-cn title-color">{{ userInfo.name }}，欢迎您！</div>
    </div>
    <div class="user-info-wrap">
      <div class="user-Info">
        <div class="user-Info-left">
          <div class="userImg">
            <img
              v-if="userInfo.avatar"
              :src="userInfo.avatar"
              :onerror="defaultImg"
              style="object-fit: cover"
              alt
            />
            <img
              v-else
              src="@/assets/img/user-center/default_user_avatar.png"
              alt=""
            />
          </div>
          <div>
            <Button type="primary" @click="editUser">修改信息</Button>
            <Button type="primary" @click="changePassword">修改密码</Button>
          </div>
        </div>
        <div class="user-Info-right">
          <div class="info-item">
            <div class="primary">
              <span class="name-cn">姓名</span>
              <span class="name-zn">Name</span>
            </div>
            <div class="userName title-color">{{ userInfo.name }}</div>
          </div>
          <div class="info-item item-sec">
            <div>
              <div class="primary">
                <span class="name-cn">账号</span>
                <span class="name-zn">User&nbsp;Name</span>
              </div>
              <div class="info-item-des title-color">
                {{ userInfo.username }}
              </div>
            </div>
            <div>
              <div class="primary">
                <span class="name-cn">警号</span>
                <span class="name-zn">Badges </span>
              </div>
              <div class="info-item-des title-color">
                {{ userInfo.workCode }}
              </div>
            </div>
            <div>
              <div class="primary">
                <span class="name-cn">性别</span>
                <span class="name-zn">Gender</span>
              </div>
              <div class="info-item-des title-color">
                {{ userInfo.sex === "0" ? "男" : "女" }}
              </div>
            </div>
          </div>
          <div class="info-item">
            <div class="primary">
              <span class="name-cn">角色</span>
              <span class="name-zn">Role</span>
            </div>
            <div class="info-item-des title-color">
              <template v-for="(e, i) in userInfo.roleVoList">
                <span :key="e.id">
                  <span>{{ e.roleName }}</span>
                  <Divider
                    v-if="i !== userInfo.roleVoList.length - 1"
                    type="vertical"
                  />
                </span>
              </template>
            </div>
          </div>
          <div class="info-item">
            <div class="primary">
              <span class="name-cn">组织</span>
              <span class="name-zn">Organization</span>
            </div>
            <div class="info-item-des title-color">
              <template v-for="(e, i) in userInfo.orgVoList">
                <span :key="e.id">
                  <span>{{ e.orgName }}</span>
                  <Divider
                    v-if="i !== userInfo.orgVoList.length - 1"
                    type="vertical"
                  />
                </span>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <mdf-password v-model="passwordModal"></mdf-password>
    <user-form v-model="userModalVisible" :info="userInfo"></user-form>
  </div>
</template>

<script>
import { mapGetters, mapMutations, mapActions } from "vuex";

export default {
  name: "userCenter",
  components: {
    MdfPassword: require("./mdf-password").default,
    UserForm: require("./user-form.vue").default,
  },
  data() {
    return {
      passwordModal: false,
      userModalVisible: false,
      defaultImg:
        'this.src="' +
        require("@/assets/img/user-center/default_user_avatar.png") +
        '"',
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  created() {
    this.updateMenuCollapse(false);
    this.$nextTick(() => {
      if (this.$route.query.firstTime) {
        this.changePassword();
      }
    });
  },
  methods: {
    ...mapMutations("admin/layout", ["updateMenuCollapse"]),
    changePassword() {
      this.passwordModal = true;
    },
    editUser() {
      this.userModalVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  overflow-y: auto;
  position: relative;
  background-image: url("~@/assets/img/user-center/information_bg.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: left bottom;
  padding: 30px 40px;
  .title-text {
    border-bottom: 1px solid transparent;
    position: relative;
    width: 100%;
    height: 56px;
    background: url("~@/assets/img/user-center/welcome.png") no-repeat 40px 16px;
    background-size: 294px 30px;
    .title-text-cn {
      font-size: 28px;
      font-weight: bold;
      font-family: "MicrosoftYaHei-Bold";
      position: absolute;
      line-height: 36px;
      top: 0px;
      left: 0;
    }
  }
  .user-info-wrap {
    height: 89%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .user-Info {
    text-align: center;
    margin-top: -6%;
    vertical-align: top;
    min-width: 800px;
    position: relative;
    z-index: 3;

    .ivu-btn-primary {
      margin: 0px 5px;
    }

    .user-Info-left {
      width: 380px;
      height: 380px;
      position: relative;
      display: inline-block;
      vertical-align: top;

      .userImg {
        width: 180px;
        height: 180px;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        // background-color: #010f1f;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      div {
        position: absolute;
        bottom: -35px;
        width: 400px;
        text-align: center;
      }
    }

    .user-Info-left::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 380px;
      height: 380px;
      background: url("~@/assets/img/user-center/userHead_bg.png") no-repeat
        left bottom;
      background-size: 100% 100%;
      -webkit-animation: rotation 60s linear infinite;
      animation: rotation 60s linear infinite;
    }

    @keyframes rotation {
      from {
        transform: rotateZ(0deg);
      }

      to {
        transform: rotateZ(360deg);
      }
    }

    .user-Info-right {
      display: inline-block;
      vertical-align: top;
      padding: 25px 0 10px;
      margin-left: 85px;
      font-size: 16px;

      .info-item {
        margin-bottom: 40px;
        text-align: left;
        width: 455px;
        word-wrap: break-word;

        .userName {
          font-size: 30px;
          line-height: 40px;
          font-weight: bold;
          position: relative;
          z-index: 0;
          transition: all 1.5s ease;
          font-family: "MicrosoftYaHei-Bold";
        }

        .userName::before {
          content: "";
          position: absolute;
          pointer-events: none;
          z-index: -1;
          top: 0;
          left: -139px;
          width: 530px;
          height: 105px;
          background: url("~@/assets/img/user-center/name_bg.png") no-repeat
            left bottom;
          background-size: 100% 100%;
        }

        .name-cn,
        .name-zn {
          display: inline-block;
          margin-bottom: 10px;
        }

        .name-cn {
          font-size: 16px;
          line-height: 22px;
        }

        .name-zn {
          font-size: 12px;
          opacity: 0.35;
          margin-left: 5px;
          line-height: 18px;
        }

        .info-item-des {
          font-size: 20px;
          line-height: 26px;
        }
      }

      .item-sec {
        display: flex;
        justify-content: space-between;
        width: 455px;
        padding-top: 20px;
      }
    }
  }

  .left {
    position: absolute;
    left: -20px;
    bottom: 0;
    z-index: 1;
    width: 552px;
    padding-top: 1%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .right {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 1320px;
    z-index: 1;

    img {
      width: 100%;
    }
  }
}
</style>
