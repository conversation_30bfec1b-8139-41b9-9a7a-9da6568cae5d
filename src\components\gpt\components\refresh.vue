<template>
  <div>
    <span>{{ text }}</span>
    <span class="click-text" @click="clickFun">
      {{ clickText }}
    </span>
    <component :is="extComponent"></component>
  </div>
</template>
<script>
export default {
  props: {
    text: {
      type: String,
      default: "很遗憾，暂未查到数据！",
    },
    clickText: {
      type: String,
      default: "重新打开",
    },
    clickFun: {
      type: Function,
    },
    extComponent:{
      type:Object
    }
  },
};
</script>
<style scoped>
.click-text {
  cursor: pointer;
  color: #2c86f8;
  margin-left: 5px;
}
</style>
