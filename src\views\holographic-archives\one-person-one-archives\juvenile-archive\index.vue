<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        @searchForm="searchForm"
        :dataType="pageForm.dataType"
        :page="'holographic'"
        :searchText="'高级检索'"
      >
      </Search>
      <div class="card-content">
        <div
          v-for="(item, index) in list"
          :key="index"
          :class="item.type === 'people' ? 'people-card' : 'video-card'"
          class="card-item"
        >
          <UiListCard
            type="zdr"
            :data="item"
            :showBar="false"
            :index="index"
            @archivesDetailHandle="
              isNewGoInfo ? goInfo(item) : goArchivesInfo(item)
            "
            @collection="getList"
          />
        </div>
        <ui-empty v-if="list.length === 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="params.pageNumber"
        countTotal
        :showElevator="true"
        :total="total"
        :page-size="params.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "@/views/holographic-archives/one-person-one-archives/real-name-file/components/search.vue";
import UiListCard from "@/components/ui-list-card";
import Statistics from "@/views/holographic-archives/components/statistics.vue";
import { getPersonPageList } from "@/api/realNameFile";
import SearchImage from "@/components/search-image/index.vue";
export default {
  components: { SearchImage, Search, UiListCard, Statistics },
  props: {
    isNewGoInfo: {
      type: Boolean,
      default: false,
    },
    goInfo: {
      type: Function,
      default: () => {},
    },
    // 重点人1  未成年人2  社区人员3
    bizLibType: {
      type: Array,
      default: () => [2],
    },
  },
  data() {
    return {
      list: [],
      loading: false,
      pageForm: {
        dataType: 1,
      },
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    getList() {
      this.loading = true;
      var param = Object.assign(this.pageForm, this.params);
      var labels = [];
      // 专题类型 字段
      param.bizLibTypes = this.bizLibType;
      if (param.labels && param.labels.length > 0) {
        param.labels.forEach((item) => {
          labels.push(item.id);
        });
        param.labelIds = labels;
      }
      param.dataType = 1;
      getPersonPageList(param)
        .then((res) => {
          const { entities, pageNumber, pageSize, total } = res.data;
          this.list = entities || [];
          this.params = {
            pageNumber: pageNumber,
            pageSize: pageSize,
          };
          this.total = total;
          // 重新获取页码后回到顶部
          document.querySelector(".card-content").scrollTop = 0;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 跳转档案
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        name: "juvenile-archive",
        query: {
          archiveNo: item.archiveNo,
          source: "people",
          initialArchiveNo: item.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
    // 查询
    searchForm(form) {
      var labelIds = [];
      let formData = { ...form };
      if (form.labelIds && form.labelIds.length > 0) {
        form.labelIds.forEach((item) => {
          if (item && item != undefined) {
            labelIds.push(item.id);
          }
        });
        formData.labelIds = labelIds;
      }
      this.pageForm = {
        ...formData,
        similarity: (form.similarity / 100).toFixed(2),
      };
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
// @import "~@/views/holographic-archives/style/page-hide.less";
.person {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        /deep/.content-img {
          width: 115px;
          height: 115px;
          background: #fff;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
