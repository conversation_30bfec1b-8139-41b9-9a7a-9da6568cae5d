<template>
  <div>
    <ui-modal v-model="visible" :title="modalAction.title" @query="query">
      <Form ref="formData" :model="formData" @submit.native.prevent>
        <FormItem
          label="方案名称"
          class="right-item"
          prop="schemeName"
          :label-width="100"
          :rules="[
            {
              required: true,
              message: '请填写方案名称',
              trigger: 'blur',
            },
          ]"
        >
          <Input type="text" v-model="formData.schemeName" :maxlength="20" placeholder="请填写方案名称"></Input>
        </FormItem>
        <FormItem label="方案类型" class="right-item" prop="schemeType" :label-width="100" required>
          <Select v-model="formData.schemeType" placeholder="请填写方案名称">
            <Option v-for="item in typeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </FormItem>
        <FormItem label="上传方案文档" class="right-item" prop="schemeName" :label-width="100">
          <ui-upload
            v-model="formData.url"
            :accept="accept"
            :format="format"
            :upload-url="uploadUrl"
            :get-upload-params="getUploadParams"
          ></ui-upload>
        </FormItem>
      </Form>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    modalAction: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '3rem',
      },
      formData: {},
      uploadName: '',
      uploadUrl: governanceevaluation.uploadStatistics,
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      accept: '.bmp,.jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
      typeList: [
        {
          value: 1,
          label: '本级（考核成绩）',
        },
        {
          value: 2,
          label: '上级（GAB考核成绩）',
        },
      ],
      defaultFormData: {
        schemeType: 1,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    getUploadParams(uploadData) {
      let uploadParams = [];
      uploadData.forEach((row) => {
        uploadParams.push(row.url || row.response.data);
      });
      return uploadParams.join(',');
    },
    async query() {
      if (this.validate('formData') === 'error') {
        return false;
      }
      if (this.formData.id) {
        this.$http.put(governanceevaluation.schemeUpdata, this.formData).then((res) => {
          this.$Message.success(res.data.msg);
          this.$emit('query');
          this.visible = false;
        });
      } else {
        this.$http.post(governanceevaluation.schemeAdd, this.formData).then((res) => {
          this.$Message.success(res.data.msg);
          this.$emit('query', res.data);
          this.visible = false;
        });
      }
    },
    open(item) {
      this.visible = true;
      this.$refs['formData'].resetFields();
      this.$nextTick(() => {
        this.formData = item ? JSON.parse(JSON.stringify(item)) : { ...this.defaultFormData };
      });
    },
    // 表单校验
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          message = 'error';
        }
      });
      return message;
    },
  },
  watch: {},
  computed: {},
  components: {
    UiUpload: require('@/components/ui-upload.vue').default,
  },
};
</script>
<style lang="less" scoped>
.mb-0 {
  margin-bottom: 0;
}
@{_deep} .ivu-modal-body {
  padding: 20px 50px 50px;
}
.upload-name {
  display: inline-block;
  max-width: 265px;
  overflow: hidden;
  vertical-align: middle;
  text-align: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-decoration: underline;
}
</style>
