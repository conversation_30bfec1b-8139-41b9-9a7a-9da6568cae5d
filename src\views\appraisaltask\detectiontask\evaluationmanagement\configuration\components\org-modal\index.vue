<template>
  <ui-modal v-model="visible" title="选择组织机构" :styles="styles" class="ui-modal" @query="query">
    <div class="area-container">
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          :highlight-current="false"
          node-key="nodeKey"
          :tree-data="areaTreeData"
          :default-props="defaultProps"
          expandAll
          checkStrictly
        >
          <template #label="{ node, data }">
            <div class="options" :class="data.children && 'is_parent'">
              <Checkbox v-model="data.check" class="mr-sm" @on-change="check($event, node, data)"></Checkbox>
              <span>{{ data.orgName }}</span>
              <Button
                type="text"
                @click="checkAll(node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
                >{{ `${data.checkAll ? '取消全选' : '全选'} ` }}</Button
              >
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'regionalization-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    edit: {
      default: true,
    },
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 默认勾选的数据
    orgList: {
      // required: true,
      type: Array,
      default: () => [],
    },
    areaTreeData: {
      require: true,
      type: Array,
      default: () => {
        return [];
      },
    },
    nodeKey: {
      default: 'orgCode',
    },
  },
  data() {
    return {
      styles: {
        width: '3.5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
    };
  },
  created() {},
  mounted() {
    // 操作dom 给只有自己的树转节点 添加类名
    document.querySelectorAll('.el-tree .is_parent').forEach((itemDom) => {
      itemDom.parentNode.parentNode.classList.add('has-child-panel');
    });
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getInitialAreaList',
    }),
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val && this.areaTreeData.length && this.formModel === 'edit') {
        this.getCheckedEcho(this.areaTreeData, this.orgList);
      }
    },
    async visible(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      this.$emit('query', this.checkedTreeData);
      this.visible = false;
    },
    getCheckedEcho(data, list) {
      data.map((item) => {
        this.$set(item, 'check', list.includes(item[this.nodeKey]));
        if (item.children) {
          this.getCheckedEcho(item.children, list);
        }
      });
    },
    getCheckedNodes(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push(item.orgCode);
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    checkAll(node, data) {
      if (node.childNodes) {
        if (!data.checkAll) {
          this.$set(data, 'checkAll', true);
          this.checkParent(node, true);
        } else {
          this.$set(data, 'checkAll', false);
          this.checkParent(node, false);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
    },
    checkParent(node, check) {
      this.$set(node.data, 'check', check);
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.btn-mini {
  height: 24px;
  @{_deep} .ivu-input {
    height: 24px;
    line-height: 24px;
  }
}
.w100 {
  width: 100px;
}
.w230 {
  width: 230px;
}
.w160 {
  width: 160px;
}
.area-container {
  .area-filter {
    line-height: 34px;
  }
}
</style>
