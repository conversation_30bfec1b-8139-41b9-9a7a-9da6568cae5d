<!--
    * @FileDescription: 时序图
    * @Author: H
    * @Date: 2022/12/21
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="sequence-box" :class="{'sequence-box-pack': pickDownUp}">
        <div class="pickUp" :class="{pickDown: pickDownUp}" @click="handlePick"></div>
        <div class="title">
            <p>时序图</p>
        </div>
        <div ref="echart" class="chart"></div>
    </div>
</template>

<script>
import * as echarts from 'echarts';
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            pickDownUp: false,
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
        this.$nextTick(() => {
            this.init();
        })    
    },
    methods: {
        handlePick() {
            this.pickDownUp = !this.pickDownUp;
        },
        init() {
            let option = {
                grid: {
                    top: '10%',
                    left: '5%',
                    right: '5%',
                    bottom: '10%'
                },
                xAxis: {
                    type: 'category',
                    data:['10-25', '10-26', '10-27', '10-28', '10-29', '10-30', '10-31'],
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color:'#D3D7DE'
                        }
                    },
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color:' #D3D7DE'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    boundaryGap: false,
                },
                yAxis: {
                    type: "category",
                    data: ["IMSI", "MAC", "RFID", "车辆", "人脸"],
                    axisLine: {
                        show: false,
                    },
                    axisTick: {
                        show: false
                    }
                },
                series: [
                    {
                        data: ["IMSI", 'IMSI', '', 'IMSI', "IMSI",],
                        type: 'line',
                        itemStyle: {
                            normal: {
                                color: '#AD48FF',
                                borderColor: '#AD48FF'
                            },
                            emphasis: {
                                color: '#AD48FF',
                            }
                        }
                    },
                    {
                        data: ["MAC", 'MAC', 'MAC', 'MAC', "MAC",],
                        type: 'line',
                        itemStyle: {
                            normal: {
                                color: '#48BAFF',
                                borderColor: '#48BAFF'
                            },
                            emphasis: {
                                color: '#48BAFF',
                            }
                        }
                    }
                ]
            };
            this.myEchart = echarts.init(this.$refs.echart);
            this.myEchart.setOption(option)
            // window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.sequence-box{
    width: 1060px;
    height: 251px;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translate(-50%, 0);
    transition: height 1s ease-out;
    .pickUp{
        width: 90px;
        height: 18px;
        position: absolute;
        background: url('../../../../../assets/img/model/packUp.png') no-repeat;
        top: -20px;
        left: 50%;
        transform: translate(-50px, 0);
        cursor: pointer;
    }
    .pickDown{
        background: url('../../../../../assets/img/model/packDown.png') no-repeat;
    }
    .chart{
        height: 200px;
        width: 100%;
    }
}
.sequence-box-pack{
    height: 0;
    bottom: 0;
    transition: height 1s ease-out;
}
</style>
