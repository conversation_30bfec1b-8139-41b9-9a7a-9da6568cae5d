import knowledgebase from '@/config/api/knowledgebase';
import governanceautomatic from '@/config/api/governanceautomatic';
import axios from 'axios';
export default {
  namespaced: true,
  state: {
    questionList: [],
    indexModuleList: [],
    indexModuleObject: {},
    sourceTypeList: [],
    catalogFlatTreeData: [],
    catalogTreeData: [],
  },
  mutations: {
    setQuestionList(state, data) {
      state.questionList = data;
    },
    setIndexModuleList(state, data) {
      state.indexModuleList = data;
    },
    setIndexModuleObject(state, data) {
      state.indexModuleObject = data;
    },
    setCatalogTreeData(state, data) {
      state.catalogTreeData = data;
    },
    setCatalogFlatTreeData(state, data) {
      state.catalogFlatTreeData = data;
    },
    setSourceTypeList(state, data) {
      state.sourceTypeList = data;
    },
  },
  getters: {
    getQuestionList(state) {
      return state.questionList;
    },
    getIndexModuleList(state) {
      return state.indexModuleList;
    },
    getIndexModuleObject(state) {
      return state.indexModuleObject;
    },
    getCatalogTreeData(state) {
      return state.catalogTreeData;
    },
    getCatalogFlatTreeData(state) {
      return state.catalogFlatTreeData;
    },
    getSourceTypeList(state) {
      return state.sourceTypeList;
    },
  },
  actions: {
    /**
     *
     * 目录
     * @param params {username: "", password: ""}
     * @returns {Promise<unknown>}
     */
    async setCatalogTreeData({ commit }) {
      try {
        const res = await axios.get(knowledgebase.getAllCatalogue);
        let treeData = this._vm.$util.common.arrayToJson(res.data.data, 'id', 'parentId');
        commit('setCatalogFlatTreeData', res.data.data);
        commit('setCatalogTreeData', treeData);
      } catch (error) {
        console.log(error);
      }
    },
    // 获取所有问题
    async setQuestionList({ commit }) {
      try {
        const { data } = await axios.post(knowledgebase.questionPageList, { notSearchTotal: false });
        commit('setQuestionList', data.data.entities);
      } catch (error) {
        console.log(error);
      }
    },
    // 获取数据类型
    async setIndexModuleList({ commit, state }) {
      if (state.indexModuleList.length) return;
      try {
        const { data } = await axios.get(knowledgebase.getIndexModuleList);
        let list = Object.keys(data.data).map((key) => {
          return {
            dataKey: key,
            dataValue: data.data[key],
          };
        });
        commit('setIndexModuleList', list);
        commit('setIndexModuleObject', data.data);
      } catch (error) {
        console.log(error);
      }
    },
    // 获取所有资源类型
    setSourceTypeList({ commit, state }) {
      if (state.sourceTypeList.length) return;
      axios.get(governanceautomatic.getSourceTypeList).then((res) => {
        let allList = [];
        let allPromiseList = res.data.data.map((ele) => {
          return (async () => {
            let array = await getGovernanceContentBysourceType(ele.code);
            allList = allList.concat(array);
          })();
        });
        Promise.all(allPromiseList).then(() => {
          let self = [
            { code: 'PlaceManagement', label: '区域管理' },
            { code: 'SpaceInformation', label: '空间信息治理' },
          ];
          allList = [...allList, ...self];
          commit('setSourceTypeList', allList);
        });
      });
    },
  },
};
const getGovernanceContentBysourceType = async function (code) {
  return await axios
    .get(governanceautomatic.getGovernanceContentBysourceType, {
      params: { sourceType: code }, // 默认资源类型：视图基础数据
    })
    .then((res) => {
      return res.data.data;
    });
};
