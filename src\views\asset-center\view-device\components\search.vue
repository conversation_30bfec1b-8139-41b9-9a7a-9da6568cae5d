<template>
  <div class="search">
    <Form ref="form" :model="formData" class="form" inline>
      <FormItem label="设备名称:" prop="deviceName" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.deviceName" maxlength="50" />
      </FormItem>
      <FormItem label="设备编码:" prop="deviceId" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.deviceId" maxlength="50" />
      </FormItem>
      <FormItem label="安装地址:" prop="address" class="search-input">
        <Input placeholder="请输入" clearable v-model="formData.address" maxlength="50" />
      </FormItem>
      <FormItem label="点位类型:" prop="sbdwlx" class="search-input">
        <Select v-model="formData.sbdwlx" clearable>
          <Option v-for="item in sbdwlxList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
        </Select>
      </FormItem>
      <div class="flex-b">
        <div>
          <FormItem label="功能类型:" prop="sbgnlx" class="search-input">
            <Select v-model="formData.sbgnlx" clearable>
              <Option v-for="item in sbgnlxList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">{{ item.dataValue }}</Option>
            </Select>
          </FormItem>
          <!-- <FormItem label="标  签:" prop="" class="search-input label-text">
            <div class="select-tag-button" @click="selectLabelHandle">请选择标签{{ formData.labelIds && formData.labelIds.length > 0 ? `/已选(${formData.labelIds.length})` : '' }}</div>
          </FormItem> -->
          <!-- <FormItem class="nullLabel" prop="">
            <Select v-model="formData.labelType" placeholder="请选择" style="width: 80px;" transfer>
              <Option value="2">并集</Option>
              <Option value="1">交集</Option>
            </Select>
          </FormItem> -->
        </div>
        <FormItem class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
    </Form>
    <!-- 选择标签 -->
    <LabelModal :labelType="1" @setCheckedLabel="setCheckedLabel" ref="labelModal" />
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
import LabelModal from '@/views/holographic-archives/components/relationship-map/label-add'
export default {
  components: {
    LabelModal
  },

  data() {
    return {
      visible: false,
      formData: {
        deviceName: '',
        deviceId: '',
        address: '',
        sbdwlx: '',
        sbgnlx: '',
        labelIds: [],
        labelType: '2'
      }
    }
  },
  computed: {
    ...mapGetters({
      sbdwlxList: 'dictionary/getSbdwlxList', // 摄像机点位类型
      sbgnlxList: 'dictionary/getSbgnlxList' //摄像机功能类型
    })
  },
  created() {},
  methods: {
    // 选择标签
    selectLabelHandle() {
      this.$refs.labelModal.init(this.formData.labelIds)
    },
    // 已选标签
    setCheckedLabel(val) {
      this.formData.labelIds = JSON.parse(JSON.stringify(val))
    },
    // 查询
    startSearch() {
      this.$emit('searchForm', this.formData)
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields()
      this.formData.labelIds = []
      this.$refs.labelModal.removeAllHandle()
      this.startSearch()
    },
    setData (data) {
      if (data) {
        if(data.deviceName) this.formData.deviceName = data.deviceName
        if(data.deviceId) this.formData.deviceId = data.deviceId
        if(data.address) this.formData.address = data.address
        if(data.sbdwlx) this.formData.sbdwlx = data.sbdwlx
        if(data.sbgnlx) this.formData.sbgnlx = data.sbgnlx
        if(data.labelIds) this.formData.labelIds = data.labelIds
      }
      this.startSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;
  .form {
    width: 100%;
    .search-input {
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input {
        width: 200px;
      }
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .label-text {
      /deep/.ivu-form-item-label {
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines !important; // 这行必加，兼容ie浏览器
        width: 72px;
        white-space: nowrap;
      }
    }
    .btn-group {
      margin-right: 0;
    }
  }
}
</style>
