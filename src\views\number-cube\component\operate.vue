<template>
  <div class="relation-graph-header-tool">
    <div v-show="!isExcavate" @click="$emit('save')">
      <i class="iconfont icon-baocun1"></i>
      <div>保存</div>
    </div>
    <div v-show="!isExcavate" class="toll-border"></div>
    <div class="triangle">
      <i class="iconfont icon-daochutupian"></i>
      <div>导出图片</div>
      <div class="menus">
        <div @click="$emit('downloadAsImage', 'image/jpeg')">JPG</div>
        <div @click="$emit('downloadAsImage', 'image/png')">PNG</div>
        <div @click="$emit('downloadAsImage', 'image/webp')">WEBP</div>
        <div @click="$emit('downloadAsImage', 'image/bmp')">BMP</div>
      </div>
    </div>
    <div class="toll-border"></div>
    <div @click="$emit('addNode')" v-show="!currentTab.relation && !isExcavate">
      <i class="iconfont icon-tianjia"></i>
      <div>增加实体</div>
    </div>
    <div
      @click="$emit('deleteNode')"
      v-show="!currentTab.relation && !isExcavate"
    >
      <i class="iconfont icon-shanchu2"></i>
      <div>删除实体</div>
    </div>
    <div @click="$emit('suoding', true)">
      <i class="iconfont icon-suoding"></i>
      <div>锁定</div>
    </div>
    <div @click="$emit('suoding', false)">
      <i class="iconfont icon-jiesuo"></i>
      <div>解锁</div>
    </div>
    <div v-show="!isExcavate" class="toll-border"></div>
    <div
      class="guanxishaixuan-box"
      :class="{ 'active-select': relationFilterShow }"
      @click="relationShaiXuanClick()"
      v-show="!currentTab.relation && !isExcavate"
    >
      <i class="iconfont icon-guanxishaixuan"></i>
      <div>关系筛选</div>
    </div>
    <div
      class="shitishaixuan"
      :class="{ 'active-select': nodeFilterShow }"
      @click="entityShaiXuanClick()"
      v-show="!isExcavate"
    >
      <i class="iconfont icon-shitishaixuan"></i>
      <div>实体筛选</div>
    </div>
    <div v-show="!isExcavate" class="toll-border"></div>
    <div
      @click="$emit('pathDeductionHandle')"
      v-show="!currentTab.relation && !currentTab.analysis && !isExcavate"
    >
      <i class="iconfont icon-lujingtuiyan"></i>
      <div>路径推演</div>
    </div>
    <div
      @click="$emit('lianjiefenxi')"
      v-show="!currentTab.relation && !currentTab.analysis && !isExcavate"
    >
      <i class="iconfont icon-lianjiefenxi"></i>
      <div>连接分析</div>
    </div>
    <div
      @click="$emit('communityAnalysisHandle')"
      v-show="!currentTab.relation && !currentTab.analysis && !isExcavate"
    >
      <i class="iconfont icon-shequnfenxi"></i>
      <div>社群分析</div>
    </div>
    <div
      class="triangle"
      @click="$emit('guanxiwajue')"
      @mouseleave="wajueMouseleaveHandle()"
      v-show="!isExcavate"
    >
      <i class="iconfont icon-caozuotubiao"></i>
      <div>关系挖掘</div>
      <div
        class="menus"
        v-if="
          guanxiwajueData.relationList &&
          guanxiwajueData.relationList.length > 0
        "
      >
        <div
          @click.stop="
            relationExcavate(
              guanxiwajueData.node,
              item.name,
              guanxiwajueData.excavateNodeConfigData,
              item
            )
          "
          v-for="(item, index) in guanxiwajueData.relationList"
          :key="index"
          class="menus-item"
          :title="item.label.length > 5 ? item.label : ''"
        >
          {{
            item.label.length > 5 ? item.label.slice(0, 5) + "..." : item.label
          }}
          <div class="del" v-if="item.name != 'relation-excavate-add'">
            <i
              class="iconfont icon-xiangqing"
              @click.stop="viewRelationExcavate(item)"
            ></i>
            <i
              class="iconfont icon-shanchu"
              @click.stop="delRelationExcavate(item)"
            ></i>
          </div>
        </div>
      </div>
    </div>
    <div class="triangle" v-show="!currentTab.relation && !isExcavate">
      <i class="iconfont" :class="currentLayout.icon"></i>
      <div>{{ currentLayout.label }}</div>
      <div class="menus">
        <div
          v-for="(item, index) in layoutList"
          :key="item.label"
          @click="layoutClick(item, index)"
          :class="{ 'layout-checked': item.label === currentLayout.label }"
        >
          <i class="iconfont" :class="item.icon"></i>{{ item.label }}
        </div>
      </div>
    </div>
    <div class="toll-border" v-show="!currentTab.relation"></div>
    <div
      v-show="
        isExcavate &&
        excavateType == 'excavateAdd' &&
        !currentTab.relation &&
        !currentTab.analysis
      "
      @click="excavateBack"
    >
      <i class="iconfont icon-fanhui icon-blue"></i>
      <div>返回</div>
    </div>
    <div
      v-show="isExcavate && !currentTab.relation && !currentTab.analysis"
      class="triangle"
    >
      <i class="iconfont icon-baocun2 icon-blue"></i>
      <div>关系保存</div>
      <div class="menus" v-if="excavateType == 'excavateAdd'">
        <div
          :style="{ cursor: saveExcavateLoading ? 'not-allowed' : 'pointer' }"
          @click="excavateSave('relation')"
        >
          保存关系
        </div>
        <div
          :style="{ cursor: saveExcavateLoading ? 'not-allowed' : 'pointer' }"
          @click="excavateSave('relationAndResult')"
        >
          保存关系及结果
        </div>
      </div>
      <div class="menus" v-else>
        <div
          :style="{ cursor: saveExcavateLoading ? 'not-allowed' : 'pointer' }"
          @click="excavateSave('result')"
        >
          保存结果
        </div>
      </div>
    </div>
    <div
      @click="excavateClose"
      v-show="isExcavate && !currentTab.relation && !currentTab.analysis"
    >
      <i class="iconfont icon-close-circle-fill1"></i>
      <div>关闭</div>
    </div>
    <div @click="closeView" v-show="currentTab.relation || currentTab.analysis">
      <i class="iconfont icon-close-circle-fill1"></i>
      <div>关闭</div>
    </div>
    <div class="relation-graph-header-search">
      <Select
        v-model="keyWords"
        filterable
        clearable
        class="input-width"
        placeholder="查找实体"
        @on-change="onEntityChange"
      >
        <Icon type="ios-search" slot="prefix" class="slot-icon" />
        <Option
          v-for="(option, index) in entitysList"
          :value="option.id"
          :key="index"
          >{{ option.label }}</Option
        >
      </Select>
    </div>
  </div>
</template>
<script>
import { layoutTypeList } from "../js/layoutParamsDict.js";
export default {
  components: {},
  props: {
    currentTab: {
      type: Object,
      default: () => {
        return { relation: false, analysis: false };
      },
    },
    isExcavate: {
      type: Boolean,
      default: false,
    },
    nodeFilterShow: {
      type: Boolean,
      default: false,
    },
    relationFilterShow: {
      type: Boolean,
      default: false,
    },
    excavateType: {
      type: String,
      default: () => "",
    },
    guanxiwajueData: {
      type: Object,
      default: () => {},
    },
    entitysList: {
      type: Array,
      default: () => [],
    },
    layoutName: {
      type: String,
      default: () => "",
    },
    saveExcavateLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      layoutList: layoutTypeList,
      keyWords: "",
    };
  },
  computed: {
    currentLayout() {
      return this.layoutList.find((item) => item.type === this.layoutName);
    },
  },
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    layoutClick(item, index) {
      // this.currentLayout = item;
      this.$emit("layoutClick", item, index);
    },
    closeView() {
      if (this.currentTab.relation) {
        this.$emit("closeRelationView");
      } else {
        this.$emit("closeAnalysis");
      }
    },
    excavateBack() {
      this.$emit("excavateBack");
    },
    excavateClose() {
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定关闭此次关系挖掘吗？`,
        onOk: () => {
          this.$emit("excavateClose");
        },
      });
    },
    excavateSave(val) {
      if (!this.saveExcavateLoading) {
        this.$emit("excavateSave", val);
      }
    },
    relationExcavate(node, name, excavateNodeConfigData, item) {
      this.$emit("relationExcavate", node, name, excavateNodeConfigData, item);
    },
    viewRelationExcavate(item) {
      this.$emit("viewRelationExcavate", item);
    },
    delRelationExcavate(item) {
      this.$emit("delRelationExcavate", item);
    },
    relationShaiXuanClick() {
      this.$emit("dataFilter", "link");
    },
    entityShaiXuanClick() {
      this.$emit("dataFilter", "node");
    },
    onEntityChange(id) {
      this.$emit("locationEntity", id);
    },
    wajueMouseleaveHandle() {
      this.$emit("guanxiwajue", true);
    },
  },
};
</script>
<style lang="less" scoped>
.relation-graph-header-tool {
  display: flex;
  flex-wrap: nowrap;
  margin-left: 8px;
  .relation-graph-header-search {
    margin-top: 13px;
    position: absolute;
    right: 100px;

    .input-width {
      width: 250px;
      border-radius: 4px;
    }

    /deep/.ivu-select-arrow {
      right: 30px;
    }

    /deep/.ivu-select-item {
      text-align: left;
    }

    .slot-icon {
      position: absolute;
      right: 10px;
      top: 9px;
      font-size: 17px;
      color: #888888;
    }
  }
  .toll-border {
    border-left: 1px solid #d3d7de;
    height: 48px;
    padding: 0px !important;
  }
  & > div {
    text-align: center;
    margin-right: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    padding: 4px;
    cursor: pointer;
    i {
      color: #333333;
      font-size: 18px;
    }
  }
  & > .triangle {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    display: block;
    background: linear-gradient(-38deg, #888888 4%, transparent 4%);
    .menus {
      position: absolute;
      padding: 10px 0;
      width: 142px;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      z-index: 99;
      margin-top: 4px;
      display: none;
      & > div {
        position: relative;
        display: flex;
        height: 30px;
        padding-left: 16px;
        line-height: 30px;
        cursor: pointer;
        font-size: 14px;
        text-align: left;
        color: rgba(0, 0, 0, 0.9);
        i > {
          margin-right: 10px;
        }
        &:hover {
          background: #2c86f8;
          color: #fff;
          i {
            color: #fff;
          }
        }
      }
      .menus-item:hover {
        .del {
          display: block;
        }
      }
      .del {
        display: none;
        position: absolute;
        right: 0;
        font-size: 20px;
        top: 0;
        line-height: 27px;
        color: #fff;
        i {
          font-size: 14px;
        }
      }
    }
  }
  & > .triangle:hover {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    display: block;
    border-bottom-right-radius: 0px;
    background: linear-gradient(-38deg, #2c86f8 4%, rgba(44, 134, 248, 0.1) 4%);
    .menus {
      display: block;
      &:hover {
        display: block;
      }
    }
  }
  & > div:hover {
    background: rgba(44, 134, 248, 0.1);
    border-radius: 4px;
    color: #2c86f8;
    & > i {
      color: #2c86f8;
    }
  }
  .icon-blue {
    color: #2c86f8;
  }
}
.icon-close-circle-fill1 {
  color: #ff2929 !important;
}
.active-select {
  background: rgba(44, 134, 248, 0.1);
  border-radius: 4px;
  color: #2c86f8 !important;
  & > i {
    color: #2c86f8 !important;
  }
}
</style>
