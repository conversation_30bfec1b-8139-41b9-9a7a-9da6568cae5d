/**
 * 防抖：n秒内不触发事件才执行事件
 * @param {event} 绑定事件(默认点击事件)
 * @param {delay} 默认300ms内不触发事件就执行
 * @param {fn} 执行函数
 */

export default function (Vue) {
  Vue.directive('debounce', {
    // 当绑定元素插入到Dom中
    inserted(el, bind) {
      let [fn, event = 'click', delay = 300] = bind.value;
      let timer;
      el.addEventListener(event, () => {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
          fn();
        }, delay);
      });
    },
  });
}
