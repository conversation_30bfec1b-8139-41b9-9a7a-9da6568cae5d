import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

export const tableColumns = (params) => {
  return [
    { type: 'index', width: 70, title: '序号', align: 'center' },
    {
      title: '行政区划',
      key: 'civilName',
      align: 'left',
      tooltip: true,
      minWidth: 130,
      renderHeader: (h, { column, index }) => {
        return renderHeaderStatistics(h, { column, index, params });
      },
    },
    {
      title: '平台当前状态',
      slot: 'isOnline',
      tooltip: true,
      minWidth: 100,
    },
    {
      title: '本月离线时长',
      slot: 'unOnlineTimeM',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '本月在线时长',
      slot: 'onlineTimeM',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '本月在线率',
      slot: 'rateFormat',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '本月报备天数',
      slot: 'reportDayNumOfM',
      tooltip: true,
      minWidth: 150,
    },
    {
      title: '操作',
      slot: 'action',
      align: 'center',
      width: 80,
      fixed: 'right',
    },
  ];
};

export const iconStaticsList = [
  {
    name: '本月在线时长',
    count: '0',
    countStyle: {
      color: '#0E8F0E',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'onlineTimeM',
  },
  {
    name: '本月离线时长',
    count: '0',
    countStyle: {
      color: '#BC3C19',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'unOnlineTimeM',
  },
  {
    name: '本月报备天数',
    count: '0',
    countStyle: {
      color: 'var(--color-primary)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'reportDayNumOfM',
  },
  {
    name: '在线率',
    count: '0',
    countStyle: {
      color: '#05FEF5',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
  },
];
