<template>
  <div class="add-modal" v-if="isShow">
    <div class="title-box" v-if="!isApproval">
      <div class="goback" @click="isShow = false">
        <i class="iconfont icon-return"></i>
      </div>
      返回<span> > {{ title }}</span>
    </div>
    <div class="content">
      <div class="video-left mr-10">
        <Form
          ref="form"
          :model="fileTaskValue"
          :disabled="isApproval"
          class="form"
          :label-width="80"
        >
          <FormItem label="任务名称:" prop="name" class="search-input">
            <Input
              placeholder="请输入"
              clearable
              v-model="fileTaskValue.taskName"
              @input="taskNameInput"
              maxlength="50"
            />
          </FormItem>
          <FormItem label="执行时间:" class="search-input">
            <hl-daterange
              v-model="fileTaskValue.runTime"
              key="1"
            ></hl-daterange>
          </FormItem>
          <FormItem label="解析类型:" class="search-input">
            <checkGroup
              v-model="fileTaskValue.analyzeType"
              :optionList="types"
              @change="analyzeTypeChange"
            ></checkGroup>
          </FormItem>
          <FormItem label="申请理由:" class="search-input">
            <Input
              v-model="fileTaskValue.requestReason"
              type="textarea"
              placeholder="请输入"
            ></Input>
          </FormItem>
          <FormItem label="申请书:" class="search-input">
            <div class="uploadImg">
              <UploadImg
                choosed
                ref="uploadImg"
                :deleted="true"
                :defaultList="defaultList"
                :multipleNum="1"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
        </Form>
        <div class="tipWordDiv">
          <span class="tipsWordSpan nui-font fs-f2"
            >任务文件列表 (注：单次导入上限50个文件)</span
          >
        </div>
        <div class="taskTreeList">
          <component
            ref="fileFolderTreeComp"
            :is="componentId"
            :ocxFileGet="ocxFileGet"
            :treeData="treeData"
            @treeEditChange="treeEditChangeFunc"
            @treeEditDelete="treeEditDeleteFunc"
            :taskId="taskId"
            @treeEditChangeName="treeEditChangeNameFunc"
            @treeEditChangeFolder="treeEditChangeFolderFunc"
            @treeEditAddFile="treeEditAddFileFunc"
            @treeClickOnNode="treeClickOnNodeFunc"
          ></component>
        </div>
      </div>
      <div class="right-content">
        <div class="fileFilterArea">
          <span>文件名称</span>
          <Input v-model="filterStr" placeholder="请输入文件名称"></Input>
        </div>
        <div class="fileToolArea">
          <Button size="small" @click="mulLacation">
            <ui-icon type="ditudingwei" color="#2C86F8"></ui-icon>
            批量定位
          </Button>
          <Button size="small" @click="mulAdjustTime">
            <ui-icon type="lishijilu" color="#2C86F8"></ui-icon>
            批量校准
          </Button>
        </div>
        <div class="fileTableArea">
          <ui-table
            :columns="columns"
            :data="tableShowData"
            @on-select="onSelect"
            @on-select-cancel="onSelectCancel"
            @on-select-all="onSelectAll"
            @on-select-all-cancel="onSelectAllCancel"
          >
            <template #baseTime="{ row }">
              <div>{{ row.baseTime | timeFormat }}</div>
            </template>
            <template #position="{ row }">
              <div>
                {{
                  row.position &&
                  row.position !== "0 0" &&
                  row.position != "0.0,0.0"
                    ? `${parseFloat(
                        row.position.replaceAll(" ", ",").split(",")[0]
                      ).toFixed(6)} ${parseFloat(
                        row.position.replaceAll(" ", ",").split(",")[1]
                      ).toFixed(6)}`
                    : row.position
                }}
              </div>
            </template>
            <template #analyzeType="{ row }">
              <checkGroup
                :ref="row.id"
                :value="row.analyzeType"
                :optionList="types"
                @change="tableAnalyzeTypeChange(row, $event)"
              ></checkGroup>
            </template>
            <template #opreate="{ row }">
              <div class="tools">
                <Poptip trigger="hover" placement="left-start">
                  <i class="iconfont icon-gengduo"></i>
                  <div class="mark-poptip" slot="content">
                    <p v-if="taskId" @click="playVideo(row)">
                      <i class="iconfont icon-bianji"></i>编辑
                    </p>
                    <p @click="adjustTime(row)">
                      <i class="iconfont icon-lishijilu"></i>时间校准
                    </p>
                    <p @click="position(row)">
                      <i class="iconfont icon-dongtai-shangtudaohang"></i
                      >位置校准
                    </p>
                    <p @click="deleteFile(row)">
                      <i class="iconfont icon-shanchu1"></i>删除
                    </p>
                  </div>
                </Poptip>
              </div>
            </template>
          </ui-table>
        </div>
      </div>
    </div>
    <div class="file-footer" v-if="!isApproval">
      <Button class="mr-20" @click="closePanel">取消</Button>
      <Button type="primary" @click="saveFunc">保存</Button>
    </div>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      @closeDragDialog="closeDragDialog"
    ></adjustPosition>
    <adjust-time
      v-model="isShowAdjustTime"
      :adjustProps="adjustProps"
      @adjustTimeClose="adjustTimeFunc"
    ></adjust-time>
    <file-player
      v-model="isShowFilePlayer"
      :editVideoValue="editVideoValue"
    ></file-player>
  </div>
</template>

<script>
import { getTaskDetailById, updateJob, saveJob } from "@/api/viewAnalysis";
import hlDaterange from "@/components/hl-daterange/index.vue";
import ocxFolderUploader from "./ocxFolderUploader.vue";
import fileFolderTree from "./fileFolderTree.vue";
import checkGroup from "./check-group.vue";
import util from "../mixins/treeDataUtil";
import dataUtil from "../mixins/dataUtilMixin";
import adjustTime from "./adjustTime.vue";
import adjustPosition from "./adjustPosition.vue";
import filePlayer from "./filePlayer.vue";
import UploadImg from "@/components/ui-upload-img-static-library";
export default {
  name: "fileAddModal",
  components: {
    hlDaterange,
    ocxFolderUploader,
    fileFolderTree,
    checkGroup,
    adjustTime,
    adjustPosition,
    filePlayer,
    UploadImg,
  },
  mixins: [util, dataUtil],
  props: {
    value: {
      type: Boolean,
      require: true,
    },
    taskId: {
      type: String,
      require: "",
    },
    subDeviceId: {
      type: String,
      require: "",
    },
    // 是否是审核详情
    isApproval: {
      type: Boolean,
      require: false,
    },
  },
  watch: {
    isShow(val) {
      this.$emit("input", val);
    },
    value: {
      handler(val) {
        this.isShow = val;
        if (val) {
          this.init();
        } else {
          this.reset();
        }
      },
      immediate: true,
    },
  },
  computed: {
    tableShowData() {
      return this.tableData.filter(
        (v) => v.fileName && v.fileName.includes(this.filterStr)
      );
    },
  },
  data() {
    return {
      filterStr: "",
      isShow: false,
      title: "文件结构化",
      isSaveFlag: true,
      isShowDragDialog: false,
      pointData: [],
      selectVideoPath: "",
      editVideoValue: {},
      treeData: [],
      tableData: [],
      isShowAdjustTime: false,
      isShowFilePlayer: false,
      adjustProps: [],
      nowSelectedFileArr: [],
      newFolderClick: false,
      modules: {},
      oldAnalyzeType: "",
      componentId: "",
      treeData: [],
      tableData: [],
      selectedData: [], //表格中选中的数据
      fileTaskValue: {
        taskName: `${new Date().format("yyyyMMddhhmmss")}`,
        runTime: "",
        analyzeType: "1,2,4,8",
        requestReason: null,
        requestAttachmentUrl: null,
      },
      types: [
        {
          text: "人脸",
          value: "1",
        },
        {
          text: "人体",
          value: "2",
        },
        {
          text: "机动车辆",
          value: "4",
        },
        {
          text: "非机动车辆",
          value: "8",
        },
      ],
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "文件名称", key: "fileName", minWidth: 150 },
        { title: "校准时间", slot: "baseTime" },
        { title: "定位", slot: "position" },
        { title: "分辨率", key: "ratio" },
        { title: "文件大小", key: "fileSizeTable" },
        { title: "解析类型", slot: "analyzeType", minWidth: 250 },
        { title: "操作", slot: "opreate", width: 80 },
      ],
      defaultList: [],
    };
  },
  mounted() {
    Array.prototype.remove = function (obj) {
      for (var i = 0; i < this.length; i++) {
        var temp = this[i];
        if (!isNaN(obj)) {
          temp = i;
        }
        if (temp == obj) {
          for (var j = i; j < this.length; j++) {
            this[j] = this[j + 1];
          }
          this.length = this.length - 1;
        }
      }
      return this;
    };

    Array.prototype.contains = function (val) {
      for (var i = 0; i < this.length; i++) {
        if (this[i] == val) {
          return true;
        }
      }
      return false;
    };
    Array.prototype.uniquelize = function () {
      var ra = new Array();
      for (var i = 0; i < this.length; i++) {
        if (!ra.includes(this[i])) {
          ra.push(this[i]);
        }
      }
      return ra;
    };
    Array.minus = function (a, b) {
      let setArr = [];
      if (a.length > b.length)
        a.uniquelize().forEach((o) => {
          if (!b.includes(o)) setArr.push(o);
        });
      else
        b.uniquelize().forEach((o) => {
          if (!a.includes(o)) setArr.push(o);
        });
      return setArr;
    };
  },
  methods: {
    init() {
      this.initData(this.taskId);
    },
    //初始化数据
    initData: function (taskId) {
      this.title = (taskId ? "编辑" : "新增") + "文件结构化任务";
      if (taskId) {
        this.getTreeDataFromInter(this.taskId);
      } else {
        this.componentId = "ocxFolderUploader";
      }
      this.oldAnalyzeType = this.fileTaskValue.analyzeType;
    },
    reset() {
      this.filterStr = "";
      this.isShowDragDialog = false;
      this.pointData = [];
      this.selectVideoPath = "";
      this.ocxParam = [];
      this.originDataTank = {};
      this.treeData = [];
      this.tableData = [];
      this.isShowAdjustTime = false;
      this.adjustProps = [];
      this.oldAnalyzeType = "";
      this.nowSelectedFileArr = [];
      this.fileTaskValue = {
        taskName: `${new Date().format("yyyyMMddhhmmss")}`,
        runTime: "",
        analyzeType: "1,2,4,8",
        requestReason: null,
        requestAttachmentUrl: null,
      };
      this.selectedData = []; //表格中选中的数据
      this.componentId = "";
      this.defaultList = [];
    },
    closePanel() {
      this.isShow = false;
      this.reset();
    },
    tableCheckboxFunc(record, v) {
      let root = this;
      let analyzeTypeSet = new Set();
      root.originDataTank = root.treeDataToArray(root.treeData[0]);
      root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
        if (
          (item.type === 2 ||
            (item.fileConvertId && item.fileConvertId !== "")) &&
          item.fileConvertId === record.fileConvertId
        ) {
          item.analyzeType = v;
          item.detectTypeId && (item.detectTypeId = v);
          item.videoTasks &&
            item.videoTasks.length > 0 &&
            (() => {
              item.videoTasks.forEach((item, index) => {
                item.detectTypeId = v;
              });
            })();
        }
      });
      //通过set() 反选任务解析类型
      root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
        if (
          item.type === 2 &&
          item.analyzeType &&
          item.analyzeType.length > 0
        ) {
          item.analyzeType.split(",").forEach((t) => {
            analyzeTypeSet.add(t);
          });
        }
      });
      this.fileTaskValue.analyzeType = Array.from(analyzeTypeSet).toString();
      this.oldAnalyzeType = Array.from(analyzeTypeSet).toString();
      root.tableData = root.getTableDataFunc();
      root.treeData = root.getTreeDataFunc();
      root.selectedData = [];
    },
    taskNameInput(el) {
      let val = el.srcElement.value;
      // $(`.fileFolderTreeAre .level-1 span[title="${this.originDataTank.name}"]`).text(val);
      if (this.treeData && this.treeData.length > 0)
        this.treeData[0].name = val;
    },
    tableAnalyzeTypeChange(row, val) {
      if (!val) {
        this.$Message.warning("解析类型至少需要选择一个");
        this.$refs[row.id].updateList();
      } else {
        row.analyzeType = val;
        this.tableCheckboxFunc(row, val);
      }
    },
    analyzeTypeChange(record) {
      let root = this;
      let oldAnalyzeType = root.oldAnalyzeType;
      let intersecSyb = "";
      let checkType = "";
      if (!record) {
        this.$Message.warning("解析类型至少需要选择一个");
        this.fileTaskValue.analyzeType = oldAnalyzeType;
        return;
      }
      let midArr = root.getInterSect(
        oldAnalyzeType.split(","),
        record.split(",")
      );
      if (!!midArr && midArr.length > 0) intersecSyb = midArr[0];
      if (
        oldAnalyzeType.split(",").filter((item) => item).length >
        record.split(",").filter((item) => item).length
      ) {
        //删
        checkType = "delete";
      } else {
        //增
        checkType = "add";
      }
      root.originDataTank = root.treeDataToArray(root.treeData[0]);
      if (
        root.originDataTank &&
        root.originDataTank.taskTreeNodeExtendList &&
        root.originDataTank.taskTreeNodeExtendList.length > 0
      ) {
        let usedArr = [];
        if (root.nowSelectedFileArr && root.nowSelectedFileArr.length > 0) {
          root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
            root.nowSelectedFileArr.forEach((val, i) => {
              if (item.fileConvertId === val.fileConvertId) {
                usedArr.push(item);
              }
            });
          });
        }
        root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
          if (
            item.type === 2 ||
            (item.fileConvertId && item.fileConvertId !== "")
          ) {
            if (root.nowSelectedFileArr && root.nowSelectedFileArr.length > 0) {
              root.nowSelectedFileArr.forEach((val, i) => {
                if (item.fileConvertId === val.fileConvertId) {
                  let analyzeTypeArr = item.analyzeType
                    .split(",")
                    .filter((item) => item);
                  let recordArr = record.split(",").filter((item) => item);
                  let compArr = analyzeTypeArr.concat(recordArr);
                  let analyzeTypeInr = "";
                  // if(!!item.analyzeType) {
                  if (checkType === "delete") {
                    let arrIndex = analyzeTypeArr.indexOf(intersecSyb);
                    if (arrIndex >= 0)
                      analyzeTypeInr = analyzeTypeArr
                        .remove(arrIndex)
                        .join(",");
                    else analyzeTypeInr = item.analyzeType;
                  } else if (checkType === "add") {
                    // analyzeTypeInr = [...new Set(analyzeTypeArr.concat([intersecSyb]))].join(',');
                    analyzeTypeInr = [...new Set(compArr)].join(",");
                  }
                  // }
                  item.analyzeType = analyzeTypeInr;
                  item.detectTypeId = analyzeTypeInr;
                  item.videoTasks &&
                    item.videoTasks.length > 0 &&
                    (() => {
                      item.videoTasks.forEach((item, index) => {
                        item.detectTypeId = analyzeTypeInr;
                      });
                    })();
                }
              });
            } else {
              let analyzeTypeArr = item.analyzeType
                .split(",")
                .filter((item) => item);
              let recordArr = record.split(",").filter((item) => item);
              let compArr = analyzeTypeArr.concat(recordArr);
              let analyzeTypeInr = "";
              // if(!!item.analyzeType) {
              if (checkType === "delete") {
                let arrIndex = analyzeTypeArr.indexOf(intersecSyb);
                if (arrIndex >= 0)
                  analyzeTypeInr = analyzeTypeArr.remove(arrIndex).join(",");
                else analyzeTypeInr = item.analyzeType;
              } else if (checkType === "add") {
                // analyzeTypeInr = [...new Set(analyzeTypeArr.concat([intersecSyb]))].join(',');
                analyzeTypeInr = [...new Set(compArr)].join(",");
              }
              // }
              item.analyzeType = analyzeTypeInr;
              item.detectTypeId = analyzeTypeInr;
              item.videoTasks &&
                item.videoTasks.length > 0 &&
                (() => {
                  item.videoTasks.forEach((item, index) => {
                    item.detectTypeId = analyzeTypeInr;
                  });
                })();
            }
          }
        });
        root.tableData = root.getTableDataFunc();
        root.selectedData = [];
      }
      root.treeData = root.getTreeDataFunc();
      root.oldAnalyzeType = root.fileTaskValue.analyzeType;
    },
    ocxFileGet(value) {
      new Promise((resolve, reject) => {
        console.log("0-0-0-0-0-0-0-0-0-0-0-0-0-0----value");
        let root = this;
        if (root.treeData && root.treeData.length > 0) {
          root.addTreeFile(root.treeData[0], value);
        } else {
          let fileStructureData = {};
          let folderLevel = {};
          let allFolder = [];
          let rootFolder = "";
          let maxLevel = 0;
          let minLevel = 0;
          let fileSizeArr = value.info.dest[0].filesize;
          let dest = value.info.dest;
          if (value.info.src[0].filetype === "folder") {
            var srcList = [];
            for (let i = 0; i < value.info.src[0].filelist.length; i++) {
              const file = value.info.src[0].filelist[i];
              var filepath = file.filepath.replace(/\\/g, "/");
              var fileNameArray = filepath.split("/");
              var fileNameGet = fileNameArray[fileNameArray.length - 1];
              dest[0].destsvr[i].name = fileNameGet;
              srcList.push(file.filepath);
            }
            var src = srcList;
          } else {
            var src = value.info.src;
          }
          let taskName = root.fileTaskValue.taskName;
          let name = "";
          if (!dest) return false;
          let resource = root.getTreeData(
            src,
            fileSizeArr,
            dest,
            this.fileTaskValue
          );
          this.originDataTank = resource;
          this.tableData = this.getTableDataFunc();
          this.selectedData = [];
          console.log("tableData---", this.tableData);
          this.treeData = this.getTreeDataFunc(); //ocx上传时不一定有文件名
          console.log("treeData---", this.treeData);
          this.componentId = "fileFolderTree";
          this.handleEmptyNode();
        }
      });
    },
    getTreeDataFromInter(taskId) {
      //编辑情况下,从接口读取树结构数据
      let root = this;
      getTaskDetailById(taskId)
        .then((result) => {
          let res = result.data;
          let analyzeTypeSet = new Set();
          res.tasks.forEach((item, index) => {
            if (item.type === 2) {
              item.structureOldType.face ? analyzeTypeSet.add(1) : null;
              item.structureOldType.human ? analyzeTypeSet.add(2) : null;
              item.structureOldType.vehicle ? analyzeTypeSet.add(4) : null;
              item.structureOldType.nonmotor ? analyzeTypeSet.add(8) : null;
              item.analyzeType = Array.from(analyzeTypeSet).toString();
            }
          });
          this.fileTaskValue.analyzeType =
            Array.from(analyzeTypeSet).toString();
          root.$set(root.fileTaskValue, "taskName", res.name);
          root.$set(
            root.fileTaskValue,
            "runTime",
            res.executeTime
              ? this.$dayjs(res.executeTime).format("YYYY-MM-DD HH:mm:ss")
              : res.executeTime
          );
          root.$set(root.fileTaskValue, "requestReason", res.requestReason);
          root.defaultList = [res.requestAttachmentUrl];
          res.tasks.forEach((task) => {
            task.detectTypeId = task.structureTypeStr;
          });
          root.originDataTank = { ...res, taskTreeNodeExtendList: res.tasks };
          root.originDataTank.structureTaskId = taskId;
          res.taskTreeNodeExtendList = res.tasks;
          root.tableData = root.getTableDataFunc();
          root.selectedData = [];
          root.treeData = root.transDataInter(root.originDataTank);
          root.componentId = "fileFolderTree";
          root.handleEmptyNode();
        })
        .catch((err) => {
          root.$Message.error(`查询结构化任务信息失败,错误信息:${err}`);
        });
    },
    treeEditChangeFunc(data) {
      this.originDataTank = data;
      this.treeData = this.getTreeDataFunc();
    },
    treeEditChangeNameFunc(record) {
      let texts = record.value;
      this.originDataTank = this.treeDataToArray(this.treeData[0]);
      if (this.originDataTank.id === record.id) {
        // $(`.fileFolderTreeAre .level-1 span[title="${this.originDataTank.name}"]`).text(texts);
        this.treeData[0].name = texts;
        this.$set(this.fileTaskValue, "taskName", texts);
      } else {
        this.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
          if (item.id === record.id) {
            this.originDataTank.taskTreeNodeExtendList[index].name = texts;
          }
        });
        this.treeData = this.getTreeDataFunc();
      }
    },
    treeEditChangeFolderFunc(record) {
      let text = record.value;
      let root = this;
      root.originDataTank = root.treeDataToArray(root.treeData[0]);
      if (root.originDataTank.id === record.id) {
        let obj = {
          id: `${root.originDataTank.id}-${Math.random() * 10 + 1}`,
          parentId: root.originDataTank.id,
          path: `${root.originDataTank.id}-${Math.random() * 10 + 1}`,
          parentPath: root.originDataTank.path || root.originDataTank.id,
          name: text,
          type: 1,
          childLoaded: true,
          children: [],
          isOpen: false,
        };
        root.originDataTank.taskTreeNodeExtendList.push(obj);
      } else {
        root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
          if (item.id === record.id) {
            let obj = {
              id: `${item.id}-${Math.random() * 10 + 1}`,
              parentId: item.id,
              path: `${item.id}-${Math.random() * 10 + 1}`,
              parentPath: item.path,
              name: text,
              type: 1,
              childLoaded: true,
              children: [],
              isOpen: false,
            };
            root.originDataTank.taskTreeNodeExtendList.push(obj);
          }
        });
      }
      root.treeData = root.getTreeDataFunc();
      root.handleEmptyNode();
    },
    treeEditDeleteFunc(record) {
      let name = record.fileName || record.name;
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确认删除${name}?`,
        onOk: () => {
          if (record.type && record.type === 2) {
            this.originDataTank = this.treeDataToArray(this.treeData[0]);
            this.originDataTank.taskTreeNodeExtendList.splice(
              this.originDataTank.taskTreeNodeExtendList.findIndex(
                (v) => v.fileConvertId === record.fileConvertId
              ),
              1
            );
            this.treeData = this.getTreeDataFunc();
            this.tableData = this.getTableDataFunc();
            this.selectedData = [];
          } else if (record.type && record.type === 1) {
            let treeVal = IX.clone(this.treeData)[0];
            if (treeVal.id === record.id) {
              treeVal.children = [];
            } else {
              run(treeVal.children);
            }
            function run(children) {
              children.forEach((item, index) => {
                if (item.id === record.id) {
                  children[index] = [];
                } else {
                  if (item.children && item.children.length > 0)
                    run(item.children);
                }
              });
            }
            this.originDataTank = this.treeDataToArray(treeVal);
            this.treeData = this.getTreeDataFunc();
            this.tableData = this.getTableDataFunc();
            this.selectedData = [];
          }
        },
      });
    },
    treeEditAddFileFunc(record) {
      let root = this;
      console.log("----------treeEditAddFileFunc---------", record);
      this.openFileDlg("video", "UIOCXFILEUPMINI", (resultVal) => {
        this.addTreeFile(record, resultVal);
      });
    },
    addTreeFile(record, resultVal) {
      let root = this;
      if (!resultVal.info || !resultVal.info.src) return;
      let originArr = [],
        taskTreeNodeExtendListCache = [];
      resultVal.info.src.forEach((item, index) => {
        let strArr = resultVal.info.src[index].filepath
          ? resultVal.info.src[index].filepath.split("/")
          : resultVal.info.src[index].split("/");
        let destArr = resultVal.info.dest[index].dest
          ? resultVal.info.dest[index].dest[index].split("/")
          : resultVal.info.dest[index].split("/");
        let cobj = {};
        cobj.parentPath = record.path ? record.path : record.id;
        cobj.parentId = record.id;
        cobj.path = strArr[strArr.length - 1];
        cobj.id = cobj.path;
        cobj.ip = resultVal.info.dest[index].dest
          ? resultVal.info.dest[index].ip
          : resultVal.info.ip;
        cobj.name = cobj.path.substr(0, cobj.path.lastIndexOf("."));
        cobj.fileSize = resultVal.info.dest[index].dest
          ? resultVal.info.dest[index].filesize[index]
          : resultVal.info.filesize[index];
        cobj.fileSizeTable = root.getFileSizeTable(cobj.fileSize);
        cobj.fileConvertId = destArr[destArr.length - 2].substr(
          0,
          destArr[destArr.length - 2].length - 1
        );
        cobj.originalFileLocalPath = resultVal.info.src[index].filepath
          ? resultVal.info.src[index].filepath
          : resultVal.info.src[index];
        cobj.type = 2;
        cobj.children = [];
        cobj.isLeaf = true;
        cobj.analyzeType = root.fileTaskValue.analyzeType;
        cobj.videoTasks = [
          {
            detectTypeId: root.fileTaskValue.analyzeType,
            ocxParam: "",
          },
        ];
        cobj.baseTime = root.getBaseTime(cobj);
        originArr = root.treeDataToArray(root.treeData[0]);
        taskTreeNodeExtendListCache.push(cobj);
      });
      originArr.taskTreeNodeExtendList =
        originArr.taskTreeNodeExtendList.concat(taskTreeNodeExtendListCache);
      root.originDataTank = originArr;
      root.treeData = root.getTreeDataFunc();
      root.tableData = root.getTableDataFunc();
      root.selectedData = [];
    },
    treeClickOnNodeFunc(record) {
      let root = this;
      let nowSelectedArr = root.treeDataToArray(record);
      if (
        nowSelectedArr.taskTreeNodeExtendList &&
        nowSelectedArr.taskTreeNodeExtendList.length > 0
      ) {
        root.newFolderClick = false;
      } else {
        root.newFolderClick = true;
      }
      root.nowSelectedFileArr = nowSelectedArr.taskTreeNodeExtendList;
    },
    handleEmptyNode() {
      let root = this;
      setTimeout(() => {
        root.originDataTank = root.treeDataToArray(root.treeData[0]);
        root.treeData = root.getTreeDataFunc();
      }, 800);
    },
    /**
     * table 选中一项
     */
    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
    saveFunc() {
      //dialog保存提交接口
      let root = this;
      let taskName = root.fileTaskValue.taskName;
      let analyzeType = root.fileTaskValue.analyzeType;
      if (!taskName) {
        this.$Message.warning("请输入任务名称");
        return false;
      }
      if (!new RegExp(/^[\u4E00-\u9FA5a-zA-Z0-9]+$/).test(taskName)) {
        this.$Message.warning("任务名称不支持特殊字符，只支持数字、字母、汉字");
        return false;
      }
      if (!analyzeType) {
        this.$Message.warning("请选择解析类型");
        return false;
      }
      if (!root.fileTaskValue.runTime) {
        root.fileTaskValue.runTime = this.$dayjs(new Date()).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      } else {
        if (root.fileTaskValue.runTime < Date.now()) {
          // this.$Message.warning("执行时间不能小于当前时间")
          // return false
          root.fileTaskValue.runTime = this.$dayjs(new Date()).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
      }
      let treeData = root.getTreeDataFunc();
      let inputValue = treeData[0];
      inputValue.name = root.fileTaskValue.taskName;
      inputValue.requestReason = root.fileTaskValue.requestReason;
      inputValue.requestAttachmentUrl = root.fileTaskValue.requestAttachmentUrl;
      inputValue.executeTime = root.fileTaskValue.runTime.getTime
        ? root.fileTaskValue.runTime.getTime()
        : root.fileTaskValue.runTime;
      //添加文件非空校验
      if (!inputValue.children.length) {
        this.$Message.warning("请添加视频文件");
        return;
      }
      if (this.isSaveFlag) {
        this.isSaveFlag = false;
        if (root.taskId && root.taskId !== "") {
          var param = this.formatStructureData(inputValue, root.taskId);
          //添加文件非空校验
          if (!param.sources.length) {
            this.isSaveFlag = true;
            this.$Message.warning("请添加视频文件");
            return;
          }
          updateJob(param).then((res) => {
            this.isSaveFlag = true;
            var structureType = "";
            this.types.forEach((item) => {
              if (item.value == (item.value & param.tasks[0].structureType)) {
                structureType += item.text + ",";
              }
            });
            this.queryLog({
              muen: "视图解析",
              name: "文件结构化",
              type: "2",
              remark: `修改文件结构化任务的【任务名称为:${
                param.structureJob.name
              }，执行时间为:${Toolkits.mills2datetime(
                param.tasks[0].taskStartTime
              )}，解析类型为:${structureType.substr(
                0,
                structureType.length - 1
              )}】`,
            });
            root.closePanel();
            this.$emit("updated");
          });
        } else {
          var param = this.formatStructureData(inputValue);
          //添加文件非空校验
          if (!param.sources.length) {
            this.isSaveFlag = true;
            this.$Message.warning("请添加视频文件");
            return;
          }
          saveJob(param)
            .then((res) => {
              this.isSaveFlag = true;
              this.queryLog({
                muen: "视图解析",
                name: "文件结构化",
                type: "1",
                remark: `选择【${param.sources.map((a) => {
                  return a.name;
                })}】文件夹/视频文件，创建【${
                  param.structureJob.name
                }】视频文件结构化任务`,
              });
              root.closePanel();
              this.$emit("updated");
            })
            .catch((res) => {
              this.isSaveFlag = true;
            });
        }
      }
    },
    formatStructureData(data, taskId) {
      const structureJob = {
        id: taskId || "",
        name: data.name,
        namePy: "",
        type: 3,
        createTime: data.createTime,
        requestReason: data.requestReason,
        requestAttachmentUrl: data.requestAttachmentUrl,
      };
      let treeNodesArray = this.getFileTreeData(data);
      let files = treeNodesArray.filter((item) => item.isLeaf);
      let folders = treeNodesArray
        .filter((item) => !item.isLeaf)
        .map((item) => {
          return {
            id: item.id,
            path: item.path,
            parentPath: item.parentPath,
            parentId: item.parentId ? item.parentId : data.id,
            name: item.name,
          };
        });
      let tasks = files.map((item) => {
        return {
          deviceId: "",
          structureType: item.analyzeType
            ? item.analyzeType
                .split(",")
                .reduce((a, b) => parseInt(a) + parseInt(b))
            : 0,
          structureJobType: 3,
          taskStartTime: data.executeTime,
          videoSourceId: item.id,
          speed: parseInt(window.defaultFileSpeed),
          ocxParam: item.videoTasks[0].ocxParam,
        };
      });
      let sources = files.map((item) => {
        return {
          ...item,
          folderId: item.parentId,
        };
      });
      console.log("treeNodesArray", treeNodesArray);
      console.log("folders", folders);
      console.log("tasks", tasks);
      console.log("sources", sources);
      return {
        structureJob,
        tasks,
        folders,
        sources,
      };
    },
    getFileTreeData(node, format) {
      let data = [];
      function getTreeData(_node) {
        if (!_node || !_node.children || !_node.children.length) {
          return;
        }
        _node.children.forEach((_item) => {
          let _item_node = { ..._item };
          delete _item_node.children;
          typeof format == "function"
            ? data.push(format(_item_node))
            : data.push(_item_node);
          if (_item.children.length) {
            getTreeData(_item);
          }
        });
      }
      getTreeData(node);
      return data;
    },
    mulLacation() {
      if (this.selectedData.length > 0) this.position();
      else this.$Message.warning("当前未选中文件");
    },
    mulAdjustTime() {
      if (this.selectedData.length > 0) this.adjustTime();
      else this.$Message.warning("当前未选中文件");
    },
    deleteFile(record) {
      let root = this;
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确认删除${record.fileName}?`,
        onOk: () => {
          let reitem = {};
          root.originDataTank = root.treeDataToArray(root.treeData[0]);
          root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
            let fileConvertId = item.fileConvertId;
            if (fileConvertId === record.fileConvertId) {
              reitem = item;
              root.originDataTank.taskTreeNodeExtendList.splice(index, 1);
            }
          });
          root.tableData = root.getTableDataFunc();
          root.selectedData = [];
          root.treeData = root.getTreeDataFunc(); //全部刷新
        },
      });
    },
    position(items) {
      if (!items) items = this.selectedData;
      else items = [items];
      let root = this;
      root.pointData = [];
      items.forEach((item, index) => {
        item.x = item.longitude;
        item.y = item.latitude;
        item.structureType = 1;
        item.data = item;
        root.pointData.push(item);
      });
      root.isShowDragDialog = true;
    },
    closeDragDialog(items) {
      this.isShowDragDialog = false;
      if (!items) return false;
      let root = this;
      root.originDataTank = root.treeDataToArray(root.treeData[0]);
      root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
        let fileConvertId = item.fileConvertId;
        items.forEach((val, i) => {
          let cfileConvertId = val.fileConvertId;
          if (fileConvertId === cfileConvertId) {
            item.longitude =
              val.longitude && parseFloat(val.longitude.toFixed(6));
            item.latitude = val.latitude && parseFloat(val.latitude.toFixed(6));
            item.position = `${item.longitude} ${item.latitude}`;
          }
        });
      });
      this.tableData = this.getTableDataFunc();
      this.selectedData = [];
      this.treeData = this.getTreeDataFunc();
    },
    adjustTime(items) {
      if (!items) items = this.selectedData;
      else items = [items];
      let root = this;
      root.adjustProps = items;
      root.isShowAdjustTime = true;
    },
    adjustTimeFunc(values) {
      let root = this;
      if (!values) {
        root.isShowAdjustTime = false;
        return false;
      }
      root.originDataTank = root.treeDataToArray(root.treeData[0]);
      root.originDataTank.taskTreeNodeExtendList.forEach((item, index) => {
        let fileConvertId = item.fileConvertId;
        values.forEach((val, i) => {
          let cfileConvertId = val.fileConvertId;
          if (fileConvertId === cfileConvertId) {
            item.baseTime = val.baseTime;
            item.adjustTime = val.baseTime;
          }
        });
      });
      this.tableData = this.getTableDataFunc();
      this.selectedData = [];
      this.treeData = this.getTreeDataFunc();
      root.isShowAdjustTime = false;
    },
    playVideo(record) {
      if (record.decladdedFilePfsPath) {
        this.editVideoValue = record;
        this.isShowFilePlayer = true;
      } else {
        this.$Message.warning("未获取到视频地址");
      }
    },
    // 选择申请书
    chooseHandle(item) {
      this.fileTaskValue.requestAttachmentUrl = item;
    },
  },
};
</script>

<style lang="less" scoped>
.add-modal {
  width: 100%;
  height: 100%;
  background: white;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .title-box {
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    .goback {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
  }
  .content {
    flex: 1;
    padding: 10px;
    display: flex;
    overflow: hidden;
    .color-blue {
      color: #2c86f8;
    }
    .video-left {
      width: 416px;
      height: 100%;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      padding: 10px;
      display: flex;
      flex-direction: column;
      .taskTreeList {
        flex: 1;
      }
      .uploadImg {
        display: flex;
        .upload-img {
          justify-content: flex-start;
          /deep/ .upload-item {
            height: 80px !important;
            width: 80px !important;
            margin: 0 !important;
          }
        }
      }
    }
    .right-content {
      flex: 1;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 10px;
      .fileFilterArea {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        span {
          margin-right: 10px;
        }
        .ivu-input-wrapper {
          width: 200px;
        }
      }
      .fileToolArea {
        margin-bottom: 10px;
        button {
          margin-right: 10px;
        }
      }
      .fileTableArea {
        flex: 1;
        .ui-table {
          height: 100%;
        }
        .tools {
          color: #2c86f8;
          width: 20px;
          height: 20px;
          text-align: center;
          line-height: 20px;
          margin-left: 5px;
          .icon-gengduo {
            transform: rotate(90deg);
            transition: 0.1s;
            display: inline-block;
          }
          p:hover {
            color: #2c86f8;
          }
          &:hover {
            background: #2c86f8;
            color: #fff;

            .icon-gengduo {
              transform: rotate(0deg);
              transition: 0.1s;
            }
            border-radius: 10px;
          }
          /deep/ .ivu-poptip-popper {
            min-width: 150px !important;
            width: 40px !important;
            height: auto;
          }
          /deep/.ivu-poptip-body {
            height: auto !important;
          }
          .mark-poptip {
            color: #000;
            cursor: pointer;
            text-align: left;
            /deep/ .ivu-icon-ios-add-circle-outline {
              font-weight: 600;
            }
          }
        }
      }
    }
  }
  .file-footer {
    height: 50px;
    text-align: center;
    padding-top: 5px;
  }
}
</style>
