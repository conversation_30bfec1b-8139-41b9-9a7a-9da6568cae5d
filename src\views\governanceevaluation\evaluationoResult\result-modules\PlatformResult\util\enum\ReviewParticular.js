export const iconStaticsList = [
  {
    name: '本月在线时长',
    count: '0',
    countStyle: {
      color: 'var(--color-success)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'onlineTimeM',
  },
  {
    name: '本月离线时长',
    count: '0',
    countStyle: {
      color: 'var(--color-failed)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'unOnlineTimeM',
  },
  {
    name: '本月报备天数',
    count: '0',
    countStyle: {
      color: 'var(--color-primary)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'reportDayNumOfM',
  },
  {
    name: '共享联网平台在线率',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
  },
];
export const tableColumns = [
  { title: '序号', type: 'index', align: 'center', width: 50 },
  { title: '组织机构', key: 'orgName', align: 'left', minWidth: 200, tooltip: true },
  { title: '平台类型', key: 'platformType', minWidth: 120, tooltip: 'true' },
  { title: '离线时间', key: 'leaveTime', minWidth: 150 },
  {
    title: '操作',
    slot: 'action',
    align: 'center',
    tooltip: true,
    width: 60,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];

export const detectionResultArr = (checkType) => {
  return [
    {
      value: '1',
      label: checkType == 1 ? '拉流成功' : '平台在线',
      color: 'var(--color-success)',
    },
    {
      value: '2',
      label: checkType == 1 ? '拉流失败' : '平台离线',
      color: 'var(--color-failed)',
    },
  ];
};

export const datailsSearchData = (params) => {
  return [
    {
      type: 'start-end-time',
      label: '抓拍时间',
      startKey: 'beginTime',
      endKey: 'endTime',
    },
    {
      type: 'select',
      key: 'onlineStatus',
      label: '检测结果',
      placeholder: '请选择检测结果',
      options: detectionResultArr(params.detail.checkType),
    },
  ];
};

export const datailsTableColumns = (params) => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 60,
    },
    {
      title: '平台名称',
      key: 'orgName',
      align: 'left',
      minWidth: 200,
      tooltip: true,
    },
    {
      title: '配置取流路数',
      key: 'deviceNum',
      minWidth: 120,
      tooltip: 'true',
    },
    {
      title: '拉流成功设备数量',
      slot: 'succNum',
      minWidth: 120,
      renderHeader: (h) => {
        return params.detail.checkType === 1 ? <span>拉流成功设备数量</span> : <span>在线设备数量</span>;
      },
    },
    {
      title: '拉流失败设备数量',
      slot: 'failNum',
      minWidth: 120,
      renderHeader: (h) => {
        return params.detail.checkType === 1 ? <span>拉流失败设备数量</span> : <span>离线设备数量</span>;
      },
    },
    {
      title: '取流成功率',
      slot: 'rate',
      minWidth: 120,
      renderHeader: (h) => {
        return params.detail.checkType === 1 ? <span>取流成功率</span> : <span>在线率</span>;
      },
    },
    {
      title: '检测结果',
      slot: 'onlineStatus',
      minWidth: 120,
    },
    {
      title: '检测时间',
      key: 'checkDate',
      minWidth: 150,
    },
  ];
};

export const deviceDatailsSearchData = (params) => {
  return [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '设备重点类型',
      options: [
        {
          value: 0,
          label: '普通设备',
        },
        {
          value: 1,
          label: '重点设备',
        },
      ],
    },
    {
      type: 'select',
      key: 'onlineStatus',
      label: '检测结果',
      options: detectionResultArr(params.detail.checkType),
    },
    {
      type: 'select',
      key: 'reasonList',
      label: '不合格原因',
      selectMutiple: true,
      maxTagCount: 1,
      options: [],
    },
  ];
};

export const deviceDatailsTableColumns = () => {
  return [
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 60,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      minWidth: 200,
      tooltip: true,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      minWidth: 200,
      tooltip: true,
    },
    {
      title: '摄像机功能类型',
      key: 'sbgnlxText',
      align: 'left',
      minWidth: 130,
      tooltip: true,
    },
    {
      title: '监控点位类型',
      key: 'sbdwlxText',
      align: 'left',
      minWidth: 130,
    },
    {
      title: '重点类型',
      slot: 'isImportant',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    {
      title: '设备状态',
      key: 'phyStatusText',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    {
      title: '检测结果',
      slot: 'onlineStatus',
      align: 'left',
      minWidth: 120,
    },
    {
      title: '原因',
      key: 'reason',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    {
      title: '操作',
      slot: 'action',
      align: 'center',
      tooltip: true,
      width: 100,
      fixed: 'right',
      className: 'table-action-padding', // 操作栏列-单元格padding设置
    },
  ];
};
