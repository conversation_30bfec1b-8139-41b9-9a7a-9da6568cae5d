<template>
  <ui-modal v-model="visible" :title="title" :styles="styles">
    <div class="content-wrap">
      <div class="right-module">
        <div class="search-module betw-flex">
          <span class="base-text-color">
            标签列表
            <span class="font-red">{{ tagData.length }}</span>
            条
          </span>
          <Input
            v-model="tagName"
            placeholder="请输入标签名称"
            search
            class="width-md ml-lg"
            @on-search="searchTag"
          ></Input>
        </div>
        <div class="tag-list" v-ui-loading="{ loading: tagLoading, tableData: tagData }">
          <Checkbox
            :indeterminate="indeterminate"
            :value="checkAll"
            class="mb-sm"
            @click.prevent.native="handleCheckAll"
            >全选</Checkbox
          >
          <div>
            <Checkbox
              class="mb-sm"
              v-for="(item, index) in tagData"
              :key="index"
              :label="item.tagId"
              v-model="item.checked"
              @on-change="checkTag($event, item)"
            >
              <span class="tag-item ellipsis" :title="item.tagName">
                {{ item.tagName }}
              </span>
            </Checkbox>
          </div>
        </div>
        <div class="preview-tag">
          <ui-tag v-for="(item, index) in checkedTagData" @close="handleCloseTag(index)" :closeable="true" :key="index">
            {{ item.tagName }}
          </ui-tag>
        </div>
        <div class="upload-box">
          <ui-label class="fl" :label="`${uploadLabel}：`">
            <Upload
              v-if="action !== 'batchDelete'"
              ref="upload"
              class="fr"
              action=""
              :multiple="false"
              :show-upload-list="false"
              :headers="headers"
              :before-upload="beforeUpload"
            >
              <Button class="ml-sm" type="primary" v-show="!searchData.url">
                <i class="icon-font icon-daoruwentishebei mr-sm"></i>
                <span>导入设备</span>
              </Button>
              <div v-show="searchData.url">
                <span class="upload-url font-active-color pointer">{{ searchData.url }}</span>
                <i class="icon-font icon-shanchu3 ml-sm" title="删除" @click.stop="reset"></i>
              </div>
            </Upload>
            <span class="base-text-color" v-else>共{{ checkedDeviceData.length || deleteTotal }}条</span>
          </ui-label>
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" class="plr-30" @click="query" :loading="queryloading"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    fetchUrl: {
      type: String,
    },
    title: {
      type: String,
    },
    uploadLabel: {
      type: String,
    },
    action: {
      type: String,
    },
    checkedDeviceData: {
      type: Array,
    },
    deleteParams: {
      type: Object,
    },
    deleteTotal: {
      type: Number,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '4rem',
      },
      queryloading: false,
      searchData: {
        url: '',
        file: null,
      },
      tagName: '',
      initTagData: [],
      tagData: [],
      tagLoading: false,
      checkedTagData: [],
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importFailData: [],
      checkAll: false,
      indeterminate: false,
    };
  },
  created() {},
  methods: {
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;
      this.tagData = this.tagData.map((item) => {
        return {
          ...item,
          checked: this.checkAll,
        };
      });
      if (this.checkAll) {
        this.checkedTagData = JSON.parse(JSON.stringify(this.tagData));
      } else {
        this.checkedTagData = [];
      }
    },
    beforeUpload(fileXls) {
      if (!/\.(xlsx|xls)$/.test(fileXls.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        return false;
      }
      const isLt30M = fileXls.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!');
        return false;
      }
      this.searchData.url = fileXls.name;
      this.searchData.file = fileXls;
      this.$Message.success('上传成功');
      return false;
    },

    // 查询所有标签
    async initTagList() {
      try {
        this.tagLoading = true;
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagData = res.data.data.map((row) => {
          this.$set(row, 'checked', false);
          return row;
        });
        this.initTagData = this.tagData;
      } catch (err) {
        console.log(err);
      } finally {
        this.tagLoading = false;
      }
    },
    searchTag() {
      this.tagLoading = true;
      let searchArr = [];
      if (this.tagName !== '') {
        for (let i = 0, len = this.initTagData.length; i < len; i++) {
          let str = this.initTagData[i].tagName;
          if (str.indexOf(this.tagName) !== -1) {
            searchArr.push(this.initTagData[i]);
          }
        }
        this.tagData = searchArr;
      } else {
        this.tagData = this.initTagData;
      }
      this.tagLoading = false;
    },
    checkTag(isCheck, item) {
      if (isCheck) {
        this.checkedTagData.push(item);
      } else {
        const index = this.checkedTagData.findIndex((row) => row.tagId === item.tagId);
        this.checkedTagData.splice(index, 1);
      }
      this.changeCheckAll();
    },
    handleCloseTag(index) {
      let tag = this.tagData.find((row) => row.tagId === this.checkedTagData[index].tagId);
      this.$set(tag, 'checked', false);
      this.checkedTagData.splice(index, 1);
      this.changeCheckAll();
    },
    changeCheckAll() {
      if (this.checkedTagData.length === this.tagData.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (this.checkedTagData.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    async query() {
      try {
        this.queryloading = true;
        let formData = null;
        if (this.action === 'batchDelete') {
          formData = {
            ids: this.checkedDeviceData,
            removeTagIdList: this.checkedTagData.map((item) => item.tagId),
            ...this.deleteParams,
          };
        } else {
          if (!this.searchData.file) {
            this.$Message.warning('请导入要删除的设备');
            return;
          }
          formData = new FormData();
          formData.append('file', this.searchData.file);
          formData.append(
            'tagIds',
            this.checkedTagData.map((item) => item.tagId),
          );
        }

        let { data } = await this.$http.post(this.fetchUrl, formData);
        this.importFailData = [];
        let result = data.data;
        if (result) {
          !!result.failData.length &&
            (this.importFailData = result.failData.map((item) => {
              return { deviceId: item };
            }));
          this.$Message.info({
            duration: 10,
            closable: true,
            render: (h) => {
              return h(
                'p',
                {
                  style: {
                    transform: "translateX('-10px')",
                  },
                },
                [
                  '导入成功',
                  h(
                    'span',
                    {
                      class: 'font-green',
                    },
                    [` ${result.successNum} `],
                  ),
                  ['条, 导入失败'],
                  h(
                    'span',
                    {
                      class: 'font-red',
                    },
                    [` ${result.failNum}`],
                  ),
                  ' 条 , 正在同步中...',
                  h(
                    'a',
                    {
                      on: {
                        click: () => {
                          this.$emit('importData', this.importFailData);
                        },
                      },
                    },
                    [`${result.failNum ? '查看导入失败设备' : ''}`],
                  ),
                ],
              );
            },
          });
        } else {
          this.$Message.success(data.msg);
        }
        this.$emit('update');
        this.visible = false;
      } catch (e) {
        console.log(e);
      } finally {
        this.queryloading = false;
      }
    },
    reset() {
      this.searchData.file = null;
      this.searchData.url = '';
      this.checkAll = false;
      this.indeterminate = false;
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.initTagList();
        this.searchData.orgCode = this.getDefaultSelectedOrg.orgCode;
      } else {
        this.checkedTagData = [];
        this.reset();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    UiModal: require('@/components/ui-modal.vue').default,
    UiTag: require('@/components/ui-tag').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 20px 0 0 0;
}
.mr-tp20 {
  margin-top: 20px;
}
.flex-1 {
  flex: 1;
}
.betw-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-wrap {
  width: 100%;
  height: 750px;
  display: flex;
}

.right-module {
  width: 100%;
  border-top: 1px solid var(--border-modal-footer);
  .search-module {
    padding: 20px;
    //border-bottom: 1px solid var(--border-color);
    @{_deep} .ivu-input-icon {
      color: var(--color-primary) !important;
    }
  }
  .tag-list {
    width: 100%;
    height: 300px;
    padding: 0 20px 20px;
    overflow-x: hidden;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-modal-footer);
    .tag-item {
      display: inline-block;
      vertical-align: middle;
      width: 103px;
    }
  }
  .preview-tag {
    padding: 20px;
    height: 230px;
    overflow-x: hidden;
    overflow-y: auto;
  }
  .upload-box {
    padding: 20px;
    height: 120px;
    .upload-url {
      text-decoration: underline;
    }
  }
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
</style>
