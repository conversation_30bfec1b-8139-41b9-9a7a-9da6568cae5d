html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  // font-size: 100%;
  // font: inherit;
  vertical-align: baseline;
  font-family: "Microsoft YaHei","Arial","黑体","宋体",sans-serif;
}


/* HTML5 display-role reset for older browsers */

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

i {
  font-style: normal;
}

ol,
ul {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

html,
body {
  height: 100%;
  min-width: 1366PX; /*no*/
  overflow: auto;
  overflow-y: hidden;
}

body {
  font-size: 12PX !important; /*no*/
}
/*解决首页google浏览器自带的输入框缓存的颜色*/
input:-webkit-autofill {
/*  -webkit-box-shadow: 0 0 0 1000px #010F1F inset !important;
  !*关于解决输入框框背景色的问题*!
  -webkit-text-fill-color: rgba(255, 255, 255, 1) !important;*/
  /*关于解决输入框文字颜色*/
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
//   -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0);
  border-radius: 10px;
  // background-color: #010F1F;
  background-color: transparent;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
  background-color: #DDE1EA;
}
// 定义滚动条hover
::-webkit-scrollbar-thumb:hover {
  background-color: #D3D7DE;
}
// 定义滚动条交汇处颜色
::-webkit-scrollbar-corner {
  background: transparent;
}
@media screen and (max-width: 1366px) {
  html {
    font-size: 136.6PX !important; /*no*/
  }
}
.olPopup {
  height: 100% !important;
  background-color: transparent !important;
}
.olMapViewport {
  // background-color: #0d2147 !important; //地图移动时背景颜色
}
