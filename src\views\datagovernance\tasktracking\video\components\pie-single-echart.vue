<template>
  <!-- <div class="echart" :id="refName" width="150" height="150"></div> -->
  <div class="echart" style="width: 150px; height: 150px" :id="refName" width="150" height="150"></div>
</template>
<script>
import { dotArr } from '@/util/module/doEcharts.js';
export default {
  name: 'tasktracking',
  props: ['dataNum', 'color', 'refName', 'title'],
  data() {
    return {};
  },
  created() {
    this.$nextTick(() => {
      // this.init();
    });
  },
  methods: {
    init() {
      var idName = this.refName;
      let myChart = this.$echarts.init(document.getElementById(idName));
      var option = {
        tooltip: {
          trigger: 'item',
        },
        color: {
          type: 'linear',
          colorStops: [
            { offset: 0, color: this.color[0] },
            { offset: 1, color: this.color[1] },
          ],
        },
        title: {
          text: this.title,
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          subtext: '236598',
          subtextStyle: {
            fontSize: 12,
            color: this.color,
          },
        },
        series: [
          {
            type: 'pie',
            zlevel: 3,
            silent: true,
            radius: ['53%', '54%'],
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: dotArr(),
          },
          {
            name: '',
            type: 'pie',
            radius: ['60%', '80%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '12',
                // fontWeight: 'bold'
              },
            },
            labelLine: {
              show: false,
            },
            data: [{ value: this.dataNum, name: this.title }],
          },
        ],
      };

      myChart.setOption(option);

      window.onresize = function () {
        myChart.resize();
      };
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  width: 100%;
  height: 100%;
}
</style>
