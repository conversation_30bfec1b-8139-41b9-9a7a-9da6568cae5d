<template>
  <div>
    <div
      class="bar"
      :style="{
        width: getWidth,
        transform: `translateX(${getLeft})`,
        backgroundColor: getColor(),
      }"
    >
      <span class="start-time">
        <span v-if="!isToday()">{{ getMonthAndDay(barData.lastTaskStartTimeStamp) }}</span>
        <span>{{ getTime(barData.lastTaskStartTimeStamp) }}</span>
      </span>
      <span v-if="!inProgress()" class="end-time">{{ getTime(barData.lastTaskEndTimeStamp) }}</span>
    </div>
    <span
      class="next-time"
      v-for="(item, index) in barData.nextTriggerTimeList"
      :key="index"
      :style="{
        transform: `translateX(${getNextLeft(item)})`,
      }"
    >
      <div class="triangle">▼</div>
      <div class="next-time-text">{{ getHours(item) }}</div>
    </span>
  </div>
</template>
<script>
export default {
  props: {
    // 时间轴的长度，用来计算bar百分比宽度
    timeWidth: {
      required: true,
      type: Number,
    },
    // 任务状态
    resultList: {
      type: Array,
    },
    barData: {
      type: Object,
    },
  },
  data() {
    return {
      zeroTimeStamp: new Date(new Date().toLocaleDateString()).getTime(),
    };
  },
  created() {},
  methods: {
    getMonthAndDay(timestamp) {
      return `${new Date(timestamp).getMonth() + 1}月${new Date(timestamp).getDate()}日`;
    },
    getTime(timestamp) {
      let minutes = new Date(timestamp).getMinutes();
      minutes = minutes < 10 ? '0' + minutes : minutes;
      let hours = new Date(timestamp).getHours();
      hours = hours < 10 ? '0' + hours : hours;
      let seconds = new Date(timestamp).getSeconds();
      seconds = seconds < 10 ? '0' + seconds : seconds;
      return `${hours}:${minutes}:${seconds}`;
    },
    getHours(timestamp) {
      let hours = new Date(timestamp).getHours();
      hours = hours < 10 ? '0' + hours : hours;
      return `${hours}时`;
    },
    isToday() {
      return this.barData.lastTaskStartTimeStamp - this.zeroTimeStamp >= 0;
    },
    inProgress() {
      return this.barData.taskStatus === '1';
    },
    getColor() {
      switch (this.barData.taskStatus) {
        case '1':
          return '#0E8F0E';
        case '2':
          return '#4F5458';
        case '3':
          return '#D66418';
        case '4':
          return '#BC3C19';
      }
    },
    getNextLeft(timestamp) {
      const fontSize = parseFloat(getComputedStyle(document.documentElement)['font-size']);
      const diff = timestamp - this.zeroTimeStamp;
      let left = (this.timeWidth / 25) * (diff / 1000 / 60 / 60) * (fontSize / 192);
      return left + 'px';
    },
  },
  watch: {},
  computed: {
    getWidth() {
      const fontSize = parseFloat(getComputedStyle(document.documentElement)['font-size']);
      let diff = 0;
      if (this.inProgress()) {
        if (this.isToday()) {
          diff = new Date().getTime() - this.barData.lastTaskStartTimeStamp;
        } else {
          diff = new Date().getTime() - this.zeroTimeStamp;
        }
      } else if (this.isToday()) {
        diff = this.barData.lastTaskEndTimeStamp - this.barData.lastTaskStartTimeStamp;
      } else {
        diff = this.barData.lastTaskEndTimeStamp - this.zeroTimeStamp;
      }
      let width = (this.timeWidth / 25) * (diff / 1000 / 60 / 60) * (fontSize / 192);
      width = width >= 0.08 ? width : 0.08;
      return width + 'px';
    },
    getLeft() {
      const fontSize = parseFloat(getComputedStyle(document.documentElement)['font-size']);
      let diff = 0;
      if (this.isToday()) {
        diff = this.barData.lastTaskStartTimeStamp - this.zeroTimeStamp;
      } else {
        diff = 0;
      }
      let left = (this.timeWidth / 25) * (diff / 1000 / 60 / 60) * (fontSize / 192);
      return left + 'px';
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.bar {
  position: relative;
  display: inline-block;
  width: 100px;
  height: 16px;
  .start-time {
    position: absolute;
    width: 120px;
    left: -130px;
    display: inline-block;
    text-align: right;
    line-height: 16px;
  }
  .end-time {
    position: absolute;
    right: -110px;
    width: 100px;
    display: inline-block;
    text-align: left;
    line-height: 16px;
  }
  .next-time {
    position: absolute;
    width: 60px;
    line-height: 16px;
    left: -7px;
    .triangle {
      font-size: 14px;
      color: #0e8f0e;
    }
    .next-time-text {
      margin-left: -8px;
    }
  }
}
</style>
