<template>
  <div class="face-url-available">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="130">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          基础信息
          <div slot="content" class="mt-md">
            <common-form
              :label-width="130"
              class="common-form"
              ref="commonForm"
              :moduleAction="moduleAction"
              :form-data="formData"
              :form-model="formModel"
              :task-index-config="taskIndexConfig"
              @updateFormData="updateFormData"
              @handleDetect="getDetect"
            >
              <div slot="extract">
                <template>
                  <FormItem label="每设备抽取图片" prop="captureNum">
                    <InputNumber
                      v-model="formData.captureNum"
                      class="input-width-number"
                      placeholder="请输入抽取图片"
                      clearable
                    ></InputNumber>
                  </FormItem>
                  <FormItem label="" :class="{ 'mt-minus-sm': formData.captureNum }" prop="isMissPic">
                    <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
                      <span>图片数量不足，则设备不合格</span>
                    </Checkbox>
                  </FormItem>
                  <FormItem
                    prop="deviceQueryForm.dayByCapture"
                    label="图片抽取范围"
                    :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
                  >
                    <span class="base-text-color mr-xs">近</span>
                    <InputNumber
                      v-model.number="formData.deviceQueryForm.dayByCapture"
                      :min="0"
                      :precision="0"
                      class="mr-sm width-mini"
                    ></InputNumber>
                    <span class="base-text-color">天，</span>
                    <InputNumber
                      v-model.number="formData.deviceQueryForm.startByCapture"
                      :min="0"
                      :max="23"
                      :precision="0"
                      class="mr-sm width-mini"
                    ></InputNumber>
                    <span class="base-text-color mr-sm">点至</span>
                    <InputNumber
                      v-model.number="formData.deviceQueryForm.endByCapture"
                      :min="0"
                      :max="23"
                      :precision="0"
                      class="mr-sm width-mini"
                    ></InputNumber>
                    <span class="base-text-color mr-sm">点</span>
                  </FormItem>
                </template>
              </div>
              <div slot="waycondiction" class="mt-xs" v-if="['1', '3'].includes(formData.detectMode)">
                <div>
                  <span class="base-text-color">检测条件：</span>
                  <div>
                    <Checkbox
                      class="ml-sm"
                      v-model="formData.deviceQueryForm.detectPhyStatus"
                      true-value="1"
                      false-value="0"
                      >设备可用</Checkbox
                    >
                  </div>
                  <div>
                    <FormItem
                      prop="deviceQueryForm.dayByFilterOnline"
                      label=""
                      :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
                    >
                      <Checkbox class="ml-sm mb-sm" v-model="formData.deviceQueryForm.filterOnline"
                        >设备有流水</Checkbox
                      >
                      <span class="base-text-color">近</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                        :min="0"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color">天，</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.startByFilterOnline"
                        :min="0"
                        :max="23"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color mr-sm">点至</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.endByFilterOnline"
                        :min="0"
                        :max="23"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color mr-sm">点</span>
                      <div class="capture-vehicle">
                        <span class="base-text-color mr-sm">抓拍人脸不少于</span>
                        <InputNumber
                          v-model.number="formData.deviceQueryForm.countByFilterOnline"
                          :min="0"
                          :precision="0"
                          class="mr-sm width-mini"
                        ></InputNumber>
                        <span class="base-text-color">张</span>
                        <p class="color-failed">说明：系统只检测满足条件的设备。</p>
                      </div>
                    </FormItem>
                  </div>
                </div>
              </div>
            </common-form>
          </div>
        </Panel>
        <Panel name="2">
          检测参数
          <div slot="content" class="mt-md">
            <FormItem label="最大等待时长" class="mb-md">
              <InputNumber
                v-model.number="formData.visitTimeout"
                :min="0"
                :max="Number.MAX_SAFE_INTEGER"
                :precision="0"
                placeholder="请输入最大等待时长"
                class="mr-sm width-lg"
              ></InputNumber>
              <span class="base-text-color">毫秒</span>
            </FormItem>
          </div>
        </Panel>
        <Panel name="3">
          高级参数
          <div slot="content" class="mt-md">
            <FormItem label="更新人脸图片可用性状态" :label-width="170" class="mb-md">
              <RadioGroup v-model="formData.isUpdatePhyStatus">
                <Radio :label="1" class="mr-lg">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="是否需要复检" :label-width="170" class="mb-md">
              <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
                <Radio label="1" class="mr-lg">是</Radio>
                <Radio label="2">否</Radio>
              </RadioGroup>
              <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
            </FormItem>
            <FormItem label="复检设备" v-if="isRecheck === '1'" :label-width="170" class="mb-md">
              <RadioGroup v-model="formData.reinspect.model">
                <Radio label="UNQUALIFIED">检测不合格设备</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="复检次数"
              prop="maxCount"
              :label-width="170"
              v-if="isRecheck === '1' && needRecheckNumIndex.includes(indexType)"
            >
              <InputNumber
                v-model.number="formData.reinspect.maxCount"
                class="mr-xs"
                :max="5"
                :min="1"
                :precision="0"
              ></InputNumber>
              <span class="base-text-color">次</span>
            </FormItem>
            <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType" :label-width="170" class="mb-md">
              <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
                <Radio label="INTERVAL" class="mr-lg">时间间隔</Radio>
                <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
              </RadioGroup>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
                <span class="base-text-color">检测结束</span>
                <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
                <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="1">时</Option>
                  <Option :value="2">分</Option>
                </Select>
                <span class="base-text-color ml-md">后，开始复检</span>
              </div>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
                <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="3">当天</Option>
                  <Option :value="4">第二天</Option>
                  <Option :value="5">第三天</Option>
                </Select>
                <TimePicker
                  :value="formData.reinspect.scheduleValue"
                  transfer
                  format="HH:mm"
                  placeholder="请选择"
                  class="width-xs"
                  @on-change="handleChangeTime"
                ></TimePicker>
              </div>
            </FormItem>
          </div>
        </Panel>
      </Collapse>
    </Form>
  </div>
</template>

<script>
import {
  isUpdatePhyStatus,
  needRecheckNumIndex,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
export default {
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      formData: {
        snap: 2,
        reinspect: null,
        captureNum: null,
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        ocrModel: '',
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,

          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
        isUpdatePhyStatus: 0,
      },
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 区划与地址信息是否正确',
          value: 'areaLocation',
        },
        {
          label: '',
          text: '(左下角) 摄像机信息是否正确',
          value: 'cameraInfo',
        },
      ],
      ruleCustom: {
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      isRecheck: '2',
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      collapse: ['1', '2', '3'],
      needRecheckNumIndex: needRecheckNumIndex,
    };
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    configInfo: {
      handler(val) {
        if (this.formModel === 'edit' && val) {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
            snap: 2,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            }
          };
          if (this.formData.reinspect) {
            this.formData.reinspect.maxCount = this.formData.reinspect.maxCount || null;
          }
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            visitTimeout: null,
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
        }
        this.setRecheck();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    setRecheck() {
      this.formData.reinspect ? (this.isRecheck = '1') : (this.isRecheck = '2');
    },
    isUpdatePhyStatus(indexType) {
      return isUpdatePhyStatus.filter((item) => item.indexType === indexType);
    },
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        if (needRecheckNumIndex.includes(this.indexType)) {
          this.$set(this.formData.reinspect, 'maxCount', 1);
        }
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },
    getDetect(val) {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
      this.detect = val;
    },
    // 表单提交校验
    async handleSubmit() {
      let modalDataValid = await this.$refs['modalData'].validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      return modalDataValid && commonFormValid;
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
  },
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.face-url-available {
  margin: 20px;
  .capture-vehicle {
    margin-left: 40px;
  }
}
.input-width-number {
  width: 380px;
}
@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    margin-bottom: 10px;
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: var(--bg-collapse-item);
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: var(--color-collapse-arrow);
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
      .ivu-collapse-content-box {
        padding: 0;
      }
    }
  }
}
</style>
