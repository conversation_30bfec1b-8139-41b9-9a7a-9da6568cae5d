<!--
    * @FileDescription: 违法前科人员同行
    * @Author: H
    * @Date: 2023/05/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-21 13:57:13
 -->
<template>
  <div class="together-box">
    <div class="head-location">
      <i class="iconfont icon-location mr-5"></i>{{ data.deviceName }}
    </div>
    <div class="images-box">
      <div class="image-box object-image">
        <div class="tag obj">对象</div>
        <ui-image :src="data.srcFaceCaptureVo.traitImg"></ui-image>
        <div class="name-info">
          {{ `${data.srcPersonInfo.name}(${data.srcPersonInfo.age})` }}
        </div>
        <div class="time">{{ data.srcFaceCaptureVo.absTime }}</div>
      </div>
      <div class="image-box together-image">
        <div class="tag together">同行</div>
        <ui-image :src="data.criminalFaceCaptureVo.traitImg"></ui-image>
        <div class="name-info">{{ `${data.criminalPersonInfo.name}` }}</div>
        <div class="time">{{ data.srcFaceCaptureVo.absTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TogetherAlarm",
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.mr-5 {
  margin-right: 5px;
}
.together-box {
  width: 340px;
  height: 215px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  padding: 5px 20px;
  .head-location {
    width: 100%;
    text-align: left;
    overflow: hidden;
    font-size: 14px;
  }
  .images-box {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    justify-content: space-around;
    .together-image {
      border: 2px solid #ea4a36;
    }
    .image-box {
      width: 160px;
      height: 140px;
      position: relative;

      .tag {
        position: absolute;
        top: 0;
        left: 0;
        background: #1faf8a;
        border-radius: 0 0 8px 0;
        color: #fff;
        padding: 3px 8px;
        z-index: 10;
      }
      .obj {
        background: #1faf8a;
      }
      .together {
        background: #ea4a36;
      }
      .name-info {
        position: absolute;
        bottom: 0px;
        width: 100%;
        background: rgba(0, 0, 0, 0.4979);
        color: #fff;
        text-align: center;
        line-height: 18px;
      }
      .time {
        width: 100%;
        text-align: center;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.35);
        line-height: 18px;
        text-wrap: nowrap;
        margin-top: 5px;
      }
    }
  }
}
</style>
