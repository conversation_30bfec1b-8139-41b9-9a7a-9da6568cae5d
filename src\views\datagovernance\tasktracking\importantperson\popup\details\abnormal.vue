<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <!-- <slot name="unqualifiedheader">
      <ui-select-tabs
        class="tabs-ui"
        v-if="tabsList.length"
        :multiSelect="false"
        @selectInfo="selectInfo"
        :list="tabsList"
      >
      </ui-select-tabs>
    </slot> -->
    <div class="tabs">
      <span @click="choose1" :class="!nomalShow ? 'cur' : ''">轨迹照片准确性存疑</span>
      <span @click="choose2" :class="nomalShow ? 'cur' : ''">上传超时</span>
    </div>
    <div class="content-div">
      <ul class="image-tips-ul" v-if="nomalShow">
        <!-- <li v-for="(item, index) of nomalLabelList" :key="index">
          <span class="label mr-xs">{{ item.name }}: </span>
          <span
            class="content"
            :class="{
              'font-table-action': index !== nomalLabelList.length - 1,
              'font-red': index === nomalLabelList.length - 1,
            }"
          >
            {{ item.value }}
          </span>
        </li> -->
        <li class="times">
          <span class="sp">抓拍时间：</span> <span style="color: #037cbe">{{ otherMsg.logTime }}</span>
        </li>
        <li class="times">
          <span class="sp">入库时间：</span> <span style="color: #037cbe">{{ otherMsg.triggerTime }}</span>
        </li>
        <li class="times">
          <span class="sp">结论：</span> <span style="color: red">{{ this.result }}</span>
        </li>
      </ul>
      <trail-decide v-else :otherMsg="otherMsg" :ownMsg="ownMsg"></trail-decide>
      <loading v-if="errorLoading"></loading>
    </div>
  </ui-modal>
</template>
<script>
// import { overTimeFunc } from "../util/selfutil";
import importantEnums from '../../util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    popUpTitle: {
      default: '非唯一人脸判定',
    },
    disqualifyItem: {
      type: Object,
    },
    value: {},
    title: {
      type: String,
      default: '不合格原因',
    },
    activeBtn: {
      type: String,
      default: '',
    },
    istasktracking: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "1.2rem",
        width: '80%',
      },
      tabsList: [],
      nomalLabelList: [
        { name: '抓拍时间', value: '' },
        { name: '入库时间', value: '' },
        { name: '结论', value: '' },
      ],
      currentTabObject: {},
      allTabMessageObject: {},
      isImportantDevice: null,
      tableList: [],
      nomalShow: false,
      trailDecideData: {},
      errorLoading: false,
      timeType: ['时', '分', '秒'],
      otherMsg: {},
      ownMsg: {},
      result: '', //结论
    };
  },
  created() {},
  mounted() {},
  computed: {
    isUnQuatify() {
      return this.tableList.some((item) => {
        return !item.value;
      });
    },
  },
  methods: {
    showModal(other, own) {
      this.otherMsg = other;
      this.ownMsg = own;
      let dataBegin = new Date(this.otherMsg.logTime).getTime(); // 抓拍时间毫秒数
      let dataEnd = new Date(this.otherMsg.triggerTime).getTime(); // 入库时间毫秒数
      let dateDiff = dataEnd - dataBegin; //相差毫秒数
      var dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)); //计算出相差天数
      var leave1 = dateDiff % (24 * 3600 * 1000); //计算天数后剩余的毫秒数
      var hours = Math.floor(leave1 / (3600 * 1000)); //计算出小时数
      //计算相差分钟数
      var leave2 = leave1 % (3600 * 1000); //计算小时数后剩余的毫秒数
      var minutes = Math.floor(leave2 / (60 * 1000)); //计算相差分钟数
      //计算相差秒数
      var leave3 = leave2 % (60 * 1000); //计算分钟数后剩余的毫秒数
      var seconds = Math.round(leave3 / 1000);
      if (this.otherMsg.logTime == null || this.otherMsg.triggerTime == null) {
        this.result = '无法判断';
      } else {
        this.result = ' 时延 ' + dayDiff + '天 ' + hours + '小时 ' + minutes + ' 分钟' + seconds + ' 秒';
      }
      this.visible = true;
    },
    choose1() {
      this.nomalShow = false;
    },
    choose2() {
      this.nomalShow = true;
    },
    // 获取人员轨迹检测结果详情
    async queryPersonLibCheckResultDetail() {
      try {
        this.errorLoading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonLibCheckResultDetail, {
          importantPersonLibId: this.disqualifyItem.id,
        });
        // if( !data.data.list.length){
        //     console.log('暂无不合格原因')
        //     return
        // }
        // let fake = [
        //     {componentCode: '10001',errorMessage: '测试2'},
        //     {componentCode: '8009',errorMessage: '测试1'}
        // ]
        // this.handleAllData({ list: fake})
        // this.handleTabData( { list: fake} )
        this.handleAllData(data.data);
        this.handleTabData(data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.errorLoading = false;
      }
    },
    handleTabData(data) {
      this.tabsList = data.list.map((item) => {
        return {
          name: item.errorMessage,
          select: false,
          itemMessage: item,
        };
      });
      if (this.istasktracking) {
        this.selectInfo(this.tabsList[0]);
      }
    },
    selectInfo() {},
    handleAllData(data) {
      data.list.forEach((element) => {
        // 上传时间超时
        if (element.componentCode === importantEnums.aggregateCodeEnums['图像上传及时性检测']) {
          const overObj = data.list.filter((item) => item.componentCode === '8009');
          const delaytime = `${overObj[0].timeDifference}，超过${
            overObj[0].extraParamObj.important
          }${this.timeType[overObj[0].extraParamObj.importantTime - 1]}`;
          if (!this.istasktracking) {
            this.nomalLabelList = [
              {
                name: '数据类型',
                value: data.isImportantDevice ? '重点人员' : '普通人员',
              },
              { name: '抓拍时间', value: overObj[0].logTime },
              { name: '上传时间', value: overObj[0].triggerTime },
              { name: '时延', value: delaytime },
            ];
          } else {
            this.nomalLabelList = [
              {
                name: '设备类型',
                value: data.isImportantDevice ? '重点人脸卡口' : '普通人脸卡口',
              },
              { name: '抓拍时间', value: overObj[0].logTime },
              { name: '入库时间', value: overObj[0].triggerTime },
              { name: '结论', value: delaytime },
            ];
          }

          // this.nomalLabelList = overTimeFunc(
          //   element,
          //   data.isImportantDevice,
          //   this.disqualifyItem
          // );
          // this.nomalShow = true;
        }
        // 人员轨迹照片准确性存疑
        // if (
        //   element.componentCode ===
        //   importantEnums.aggregateCodeEnums["人员轨迹准确性检测优化"]
        // ) {
        //   this.trailDecideData = element;
        //   this.trailDecideData.row = this.disqualifyItem;
        //   // this.nomalShow = false;
        // }
      });
    },
  },
  watch: {
    // visible(val) {
    //   this.$emit("input", val);
    //   if (val) {
    //     this.tabsList = [];
    //     this.nomalLabelList = [];
    //     this.trailDecideData = {};
    //     if (this.istasktracking) {
    //       this.nomalLabelList = [
    //         { name: "设备类型", value: "" },
    //         { name: "抓拍时间", value: "" },
    //         { name: "入库时间", value: "" },
    //         { name: "结论", value: "" },
    //       ];
    //     } else {
    //       this.nomalLabelList = [
    //         { name: "数据类型", value: "" },
    //         { name: "抓拍时间", value: "" },
    //         { name: "上传时间", value: "" },
    //         { name: "时延", value: "" },
    //       ];
    //     }
    //     // this.queryPersonLibCheckResultDetail();
    //   }
    // },
    // value(val) {
    //   this.visible = val;
    // },
    activeBtn() {
      //   this.nomalShow = this.activeBtn === "8009" ? true : false;
    },
  },
  components: {
    TrailDecide: require('./trail-decide_str.vue').default,
    // UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.image-tips-ul {
  display: flex;
  align-items: center;
  flex-direction: column;
  color: #fff;
  padding: 70px 0 0 0;
  > li {
    height: 40px;
    line-height: 40px;
    // width: 300px;
    .label {
      //   display: inline-block;
      width: 60px;
      text-align: left;
    }
  }
}
.determine-ul {
  color: #fff;
  > li {
    height: 60px;
    line-height: 60px;
    display: flex;
    padding: 0 20px;
    > span {
      flex: 1;
    }
  }
  &:nth-child(even) {
    background: #041939;
  }
  &:nth-child(odd),
  .determine-ul-li-result {
    background: #062042;
  }
  .determine-ul-li-title {
    background: #092955;
  }
  .determine-ul-li-result {
    height: 100px;
    line-height: 100px;
    color: var(--color-primary);
    display: flex;
    > p {
      flex: 1;
    }
  }
  .standard_icon {
    vertical-align: middle;
    font-size: 120px;
    position: relative;
    .icon_text_error {
      font-size: 16px;
      position: absolute;
      right: 37px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
    .icon_text_succeed {
      font-size: 16px;
      position: absolute;
      right: 44px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
  }
}
.tabs-ui {
  margin-bottom: 20px;
}
.content-div {
  height: 650px;
  position: relative;
}
.url-p {
  text-align: center;
  padding-top: 150px;
}
@{_deep} .ivu-modal {
  &-body {
    padding: 20px 50px !important;
  }
}
.tabs {
  margin-bottom: 20px;
  span {
    padding: 8px 22px;
    color: #56789c;
    display: inline-block;
    border: 1px solid #1b82d2;
    cursor: pointer;
  }
  span:nth-child(1) {
    border-right: none;
  }
  .cur {
    background-color: var(--color-primary);
    color: #fff;
  }
}
.times {
  width: 250px;
  .sp {
    width: 80px;
    text-align: right;
  }
}
</style>
