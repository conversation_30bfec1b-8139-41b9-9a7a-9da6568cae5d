<template>
  <div class="cube-tabs">
    <div class="">
      <Tabs
        ref="Tabs"
        :value="currentTag"
        :animated="false"
        type="card"
        @on-click="handleClickTab"
        @on-tab-remove="handleClickClose"
        :before-remove="beforeRemove"
        @on-drag-drop="handleDragDrop"
        @on-contextmenu="handleContextMenu"
      >
        <!--eslint-disable-->
        <TabPane
          v-for="(item, $index) in tabsList"
          :key="Math.random($index, $index + 1)"
          context-menu
          :label="item | filterLabel"
          :name="item.flag"
          :closable="tabsList.length > 0"
        />
        <template slot="contextMenu" v-if="tabsList.length > 0">
          <DropdownItem @click.native="handleClose('left')"
            >关闭左侧</DropdownItem
          >
          <DropdownItem @click.native="handleClose('right')"
            >关闭右侧</DropdownItem
          >
          <DropdownItem @click.native="handleClose('other')"
            >关闭其他</DropdownItem
          >
          <DropdownItem @click.native="handleClose('all')"
            >全部关闭</DropdownItem
          >
        </template>
      </Tabs>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { cloneDeep } from "lodash";

export default {
  name: `cubeTabs`,
  props: {
    tabsList: {
      type: Array,
      default: () => [],
    },
    currentTag: {
      type: String,
      default: "",
    },
  },
  computed: {},
  data() {
    return {
      rightTab: "", // 右击tab
    };
  },
  mounted() {
    this.$refs.Tabs.scrollable = true;
    document.addEventListener("scroll", this.handleScroll, { passive: true });
    this.handleScroll();
  },
  beforeDestroy() {
    document.removeEventListener("scroll", this.handleScroll);
  },
  filters: {
    filterLabel(item) {
      return item.name + (item.save ? "" : "*");
    },
  },
  methods: {
    // ...mapActions({
    //   closeAdminTab: 'admin/page/close'
    // }),
    /**
     * 点击tab
     * @param {*} tabName
     */
    handleClickTab(tabName) {
      this.$emit("tabClick", tabName);
    },
    /**
     * 删除tag
     */
    handleClickClose(tabName) {
      this.$emit("tabDel", tabName);
      if (this.tabsList.length === 1) {
        // if (this.$route.query.from === "gpt") {
        //   this.$router.push({
        //     path: "/number-cube",
        //   });
        // } else {
        //   this.$router.go(-1);
        // }

        this.$router.push({
          path: "/number-cube",
        });
      }
    },
    /**
     * 删除前验证
     */
    beforeRemove(index) {
      return new Promise((resolve, reject) => {
        const row = this.tabsList[index];
        if (row.save) {
          resolve();
        } else {
          this.$Modal.confirm({
            title: "删除提示",
            content: "当前图谱未保存，是否保存？",
            okText: "保存",
            cancelText: "不保存",
            closable: true,
            onOk: () => {
              this.$emit("save", resolve);
            },
            onCancel: () => {
              resolve();
            },
          });
        }
      });
    },
    handleScroll() {
      if (this.tabsFix && !this.headerFix) {
        const scrollTop =
          document.body.scrollTop + document.documentElement.scrollTop;
        this.scrollTop = scrollTop > 64 ? 64 : scrollTop;
      }
    },
    /**
     * 右击关闭事件
     * @param {*} name
     */
    handleClose(type) {
      this.$emit("tabDelMore", type, this.rightTab);
    },
    handleDragDrop(name, newName, a, b) {
      const tabsList = cloneDeep(this.tabsList);
      tabsList.splice(b, 1, ...tabsList.splice(a, 1, tabsList[b]));
      this.updateOpened({ tabsList });
    },
    // 右击触发
    handleContextMenu(data) {
      this.rightTab = data.name;
      this.contextData = data;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ivu-tabs-nav-scroll {
  background: #dde1ea;
}

/deep/ .ivu-tabs-nav {
  .ivu-tabs-tab {
    background: #d3d7de !important;
  }
  .ivu-tabs-tab:hover {
    border: 0 !important;
    font-weight: 600;
    color: #333 !important;
    background: #ebedf1 !important;
  }

  .ivu-tabs-tab-active {
    border: 0 !important;
    font-weight: 600;
    color: #333 !important;
    background: #ebedf1 !important;
  }
}

/deep/ .ivu-tabs-bar {
  margin-bottom: 0 !important;
}

/deep/ .ivu-tabs-nav-container {
  background: #dde1ea;
}
</style>
