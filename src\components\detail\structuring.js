/**
 * @FileDescription: 结构化信息表
 * @Author: H
 * @Date: 2023/5/25
 * @LastEditors:
 * @LastEditTime:
 */
/**
 * filter: 数据字典
 * wear： 穿戴处理
 */
// 人脸
export const faceList = [
  // { key: "sbgnlx", title: "设备类型", dictionary: "sbgnlxList" },
  { key: "gender", title: "性别", type: "filter", dictionary: "genderList" },
  { key: "ageUpLimit", title: "年龄", dictionary: "" },
  // {'key': 'ageUpLimit', 'title': '年龄上限', 'dictionary': ''},
  // {'key': 'ageLowerLimit', 'title': '年龄下限', 'dictionary': ''},
  {
    key: "eyeglass",
    title: "眼镜",
    type: "filter",
    dictionary: "captureGlassesList",
  },
  { key: "ethnic", title: "民族", type: "filter", dictionary: "nationList" },
  { key: "isCap", title: "帽子", type: "filter", dictionary: "captureCapList" },
  {
    key: "skinColour",
    title: "肤色",
    type: "filter",
    dictionary: "skinColorList",
  },
  { key: "mask", title: "口罩", type: "filter", dictionary: "captureMaskList" },
];
// 车辆
export const veicleList = [
  // { key: "sbgnlx", title: "设备类型", dictionary: "sbgnlxList" },
  { key: "vehicleSpeed", title: "车辆速度", dictionary: "" },
  { key: "violationType", title: "违章标识", dictionary: "" },
  { key: "zanding", title: "运动方向", dictionary: "" },
  { key: "zanding", title: "运动速度", dictionary: "" },
  {
    key: "vehicleHead",
    title: "车辆角度",
    type: "filter",
    dictionary: "pointViewList",
  },
  {
    key: "plateClass",
    title: "车牌类型",
    type: "filter",
    dictionary: "plateClassList",
  },
  {
    key: "plateColor",
    title: "车牌颜色",
    type: "filter",
    dictionary: "licensePlateColorList",
  },
  {
    key: "plateCover",
    title: "车牌遮挡",
    type: "filter",
    dictionary: "plateOcclusionList",
  },
  {
    key: "vehicleType",
    title: "车辆类型",
    type: "filter",
    dictionary: "vehicleTypeList",
  },
  {
    key: "vehicleColor",
    title: "车辆颜色",
    type: "filter",
    dictionary: "plateColorIpbdList",
  },
  {
    key: "vehicleBrand",
    title: "车辆品牌",
    type: "filter",
    dictionary: "vehicleBrandList",
  },
  { key: "vehicleYear", title: "车辆年款", dictionary: "" },
  {
    key: "specialCar",
    title: "特殊车辆",
    type: "filter",
    dictionary: "specialVehicleList",
  },
  {
    key: "roofItems",
    title: "车顶物件",
    type: "filter",
    dictionary: "roofItemsList",
  },
  { key: "vehicleSubBrand", title: "品牌子类", dictionary: "" },
  { key: "vehicleYear", title: "品牌年款", dictionary: "" },
  {
    key: "annualInspectionNum",
    title: "年检标",
    type: "filter",
    dictionary: "annualInspectionNumList",
  },
  {
    key: "markerType",
    title: "标志物",
    type: "filter",
    dictionary: "markerTypeList",
  },
  {
    key: "faceCover",
    title: "面部遮挡",
    type: "filter",
    dictionary: "facialOcclusionList",
  },
  {
    key: "markerType",
    title: "遮阳板",
    type: "filter",
    dictionary: "sunVisorStatusList",
  },
  { key: "driverPhone", title: "主驾是否打电话", dictionary: "" },
  { key: "codriverPhone", title: "副驾是否打电话", dictionary: "" },
  {
    key: "hasDriverFu",
    title: "副驾驶是否有人",
    type: "filter",
    dictionary: "copilotList",
  },
  { key: "driveBelt", title: "主驾是否系安全带", dictionary: "" },
  { key: "codriverBelt", title: "副驾是否系安全带", dictionary: "" },
];
// 人体
export const homanBodyList = [
  // { key: "sbgnlx", title: "设备类型", dictionary: "sbgnlxList" },
  { key: "zanding", title: "运动方向", dictionary: "" },
  { key: "zanding", title: "运动速度", dictionary: "" },
  { key: "gender", title: "性别", type: "filter", dictionary: "genderList" },
  { key: "zanding", title: "年龄范围", dictionary: "" },
  { key: "zanding", title: "头部标识", dictionary: "" },
  // {'key': 'ageUpLimit', 'title': '年龄上限',  'dictionary': ''},
  // {'key': 'ageLowerLimit', 'title': '年龄下限',  'dictionary': ''},
  {
    key: "isCap",
    title: "是否戴帽子",
    type: "filter",
    dictionary: "captureCapList",
  },
  {
    key: "hairStyle",
    title: "发型",
    type: "filter",
    dictionary: "hairStyleList",
  },
  {
    key: "sleeveStyle",
    title: "上身类型",
    type: "filter",
    dictionary: "upperSleeveTypeList",
  },
  {
    key: "upperTexture",
    title: "上身纹理",
    type: "filter",
    dictionary: "upperBodyTextureList",
  },
  {
    key: "upperColor",
    title: "上身颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  {
    key: "lowerStyle",
    title: "下身服饰",
    type: "filter",
    dictionary: "lowerBodyType",
  },
  {
    key: "lowerColor",
    title: "下身颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  {
    key: "appendix",
    title: "随身物品",
    type: "filter",
    dictionary: "appendantList",
  },
  {
    key: "shoesColor",
    title: "鞋子颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  {
    key: "shoesStyle",
    title: "鞋子类别",
    type: "filter",
    dictionary: "shoeCategory",
  },
  { key: "ethnic", title: "民族", type: "filter", dictionary: "nationList" },
  {
    key: "behavior",
    title: "行为",
    type: "filter",
    dictionary: "behaviorList",
  },
  {
    key: "bagType",
    title: "包类型",
    type: "filter",
    dictionary: "packetTypeList",
  },
  {
    key: "personAngle",
    title: "人员角度",
    type: "filter",
    dictionary: "nonmotorVehicleAngle",
  },
  {
    key: "eyeglass",
    title: "是否戴眼镜",
    type: "filter",
    dictionary: "captureGlassesList",
  },
  {
    key: "mask",
    title: "是否戴口罩",
    type: "filter",
    dictionary: "captureMaskList",
  },
  { key: "safehat", title: "是否带头盔", type: "wear", dictionary: "" },
];
// 非机动车
export const nonmotorList = [
  // { key: "sbgnlx", title: "设备类型", dictionary: "sbgnlxList" },
  { key: "zanding", title: "运动方向", dictionary: "" },
  { key: "zanding", title: "运动速度", dictionary: "" },
  { key: "gender", title: "性别", type: "filter", dictionary: "genderList" },
  { key: "ethnic", title: "民族", type: "filter", dictionary: "nationList" },
  {
    key: "vehicleType",
    title: "车辆类型",
    type: "filter",
    dictionary: "nonmotorVehicleType",
  },
  {
    key: "vehicleColor",
    title: "车辆颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  { key: "zanding", title: "头部标识", dictionary: "" },
  {
    key: "illegalPassenger",
    title: "载人情况",
    type: "filter",
    dictionary: "mannedSituation",
  },
  {
    key: "vehicleAngle",
    title: "车辆角度",
    type: "filter",
    dictionary: "nonmotorVehicleAngle",
  },
  {
    key: "sleeveStyle",
    title: "上身袖子类型",
    type: "filter",
    dictionary: "upperSleeveTypeList",
  },
  {
    key: "upperColor",
    title: "上身颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  {
    key: "upperTexture",
    title: "上身纹理",
    type: "filter",
    dictionary: "upperBodyTextureList",
  },
  {
    key: "appendix",
    title: "随身物品",
    type: "filter",
    dictionary: "appendantList",
  },
];
// 步态
export const gaitList = [
  { key: "zanding", title: "设备类型", dictionary: "" },
  { key: "zanding", title: "运动方向", dictionary: "" },
  { key: "zanding", title: "运动速度", dictionary: "" },
  { key: "gender", title: "性别", type: "filter", dictionary: "genderList" }, // 对应字典 gender
  {
    key: "age",
    title: "年龄范围",
    type: "filter",
    dictionary: "yhsdFaceCaptureAge",
  }, // yhsd_capture_age
  { key: "zanding", title: "头部标识", dictionary: "" },
  {
    key: "hatType",
    title: "是否戴帽子",
    type: "filter",
    dictionary: "yhsdCaptureCap",
  }, // yhsd_capture_cap
  {
    key: "hairType",
    title: "发型",
    type: "filter",
    dictionary: "hairStyleList",
  }, // B38_1400
  {
    key: "uptype",
    title: "上身类型",
    type: "filter",
    dictionary: "upperSleeveTypeList",
  }, // SleeveStyle
  {
    key: "upPic",
    title: "上身纹理",
    type: "filter",
    dictionary: "yhsdUpperBodyTexture",
  }, // yhsd_B40
  {
    key: "upColor",
    title: "上身颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  }, // B34_1400
  {
    key: "downType",
    title: "下身服饰",
    type: "filter",
    dictionary: "yhsdLowerBodyType",
  }, // yhsd_B41
  {
    key: "downColor",
    title: "下身颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  { key: "zanding", title: "随身物品", dictionary: "" },
  {
    key: "shoesColor",
    title: "鞋子颜色",
    type: "filter",
    dictionary: "recognitionColorList",
  },
  {
    key: "shoesStyle",
    title: "鞋子类别",
    type: "filter",
    dictionary: "shoeCategory",
  }, // B321_1400
  { key: "zanding", title: "民族", dictionary: "" },
  {
    key: "gaitType",
    title: "行为",
    type: "filter",
    dictionary: "yhsdBehaviorList",
  }, // yhsd_B312_1400
  {
    key: "bagType",
    title: "包类型",
    type: "filter",
    dictionary: "packetTypeList",
  }, // B67
  {
    key: "angle",
    title: "人员角度",
    type: "filter",
    dictionary: "nonmotorVehicleAngle",
  }, // NonmotorAngle
  { key: "zanding", title: "是否戴眼镜", dictionary: "" },
  {
    key: "maskState",
    title: "是否戴口罩",
    type: "filter",
    dictionary: "yhsdCaptureMask",
  }, // yhsd_capture_mask
  { key: "zanding", title: "是否带头盔", dictionary: "" },
];
