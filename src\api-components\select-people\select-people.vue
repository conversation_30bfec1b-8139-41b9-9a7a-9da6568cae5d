<template>
  <div class="camera-module">
    <ui-modal v-model="visible" :title="title" width="62.5rem">
      <Row class="camera-content">
        <Col :span="6" class="tree height-full">
          <ui-search-tree
            ref="uiSearchTree"
            placeholder="请输入组织机构名称"
            class="ui-search-tree"
            :check-strictly="true"
            :show-checkbox="true"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-keys="defaultExpandedKeys"
            :node-key="nodeKey"
            @check="check"
          >
          </ui-search-tree>
        </Col>
        <Col :span="18" class="camera-list height-full">
          <div class="select-top">
            <div class="fl">
              <span>
                <Input
                  search
                  placeholder="请输入姓名"
                  class="width-md"
                  v-model="searchText"
                  @on-search="searchCamaraAdd()"
                  @on-enter="searchCamaraAdd"
                />
              </span>
            </div>
          </div>
          <div class="height-full pt-sm select-content" v-scroll="335">
            <div
              v-for="(item, index) in cameraList"
              :key="index"
              class="mr-sm mb-sm secondary fl people-item"
              @click="selectPeople(item)"
              :class="{ active: item.name === selectedPeople.name }"
            >
              {{ item.name }}
            </div>
            <loading v-if="loading"></loading>
          </div>
        </Col>
      </Row>
      <template #footer>
        <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
        <Button class="plr-30" type="primary" :disabled="isDisabled" @click="query">确 认</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.camera-module {
  .ui-search-tree {
    height: 650px;
  }
  @{_deep} .ivu-modal-body {
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
  .camera-content {
    border-top: 1px solid var(--border-modal-footer);
    .tree {
      padding: 10px 20px 10px;
      @{_deep}.el-tree {
        //padding-top: 20px;
      }
    }
    .camera-list {
      border-left: 1px solid var(--border-modal-footer);
      .select-top {
        overflow: hidden;
        padding: 0 15px;
        line-height: 50px;
        border-bottom: 1px solid var(--border-modal-footer);
      }
      .people-item {
        padding: 5px 10px;
        color: var(--color-btn-text);
        border: 1px solid var(--border-btn-default);
        border-radius: 4px;
        cursor: pointer;
        &.active {
          color: #fff;
          background-color: var(--color-btn-dashed);
        }
        &:hover {
          color: #fff;
          background-color: var(--color-btn-dashed-hover);
        }
      }
    }
  }
  .select-content {
    padding: 20px;
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
import user from '@/config/api/user';

export default {
  data() {
    return {
      visible: false,
      loading: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      nodeKey: 'id',
      cameraList: [],
      viewCameraList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initCameraList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
      searchText: '',
      cameraSliceNum: 200,
      selectedPeople: {},
      isDisabled: true,
    };
  },
  created() {},
  mounted() {},
  methods: {
    muchCamera() {
      //这里处理如果摄像机列表过多，则显示前200个，否则dom过多显示会卡死
      if (this.cameraList.length > 200) {
        this.listenScroll();
      }
      this.cameraSliceNum = 200;
      this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
    },
    //监听滑动距离加载隐藏的摄像机列表
    listenScroll() {
      let box = this.$refs.cameraBox;
      box.addEventListener(
        'scroll',
        () => {
          // console.log(box.scrollTop + box.clientHeight === box.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个摄像头
          if (box.scrollTop + box.clientHeight === box.scrollHeight) {
            this.cameraSliceNum += 200;
            //到底部要做的操作
            this.viewCameraList = this.cameraList.slice(0, this.cameraSliceNum);
          }
        },
        false,
      );
    },
    //-----------------根据名字检索摄像机函数---------------------------
    searchCamaraAdd() {
      let searchArr = [];
      if (this.searchText !== '') {
        for (let i = 0; i < this.initCameraList.length; i++) {
          let str = this.initCameraList[i].name || '';
          if (str.indexOf(this.searchText) !== -1) {
            searchArr.push(this.initCameraList[i]);
          }
        }
        this.cameraList = searchArr;
      } else {
        this.cameraList = this.initCameraList;
      }
      this.muchCamera();
    },
    async getOrgPoliceman(checkedData) {
      try {
        this.loading = true;
        let res = await this.$http.post(user.queryUserList, {
          orgIdList: checkedData,
        });
        this.cameraList = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async check(checkedData) {
      if (checkedData.length === 0) {
        this.cameraList = [];
      } else {
        await this.getOrgPoliceman(checkedData);
      }
      //当摄像头过多时进行处理不显示所有摄像头只先显示200个
      this.muchCamera();
      this.initCameraList = this.cameraList;
    },
    query() {
      let peopleLen = Object.keys(this.selectedPeople).length;
      if (peopleLen) {
        this.isDisabled = false;
        this.visible = false;
        this.$emit('putPeople', this.selectedPeople, this.filedName);
      } else {
        this.isDisabled = true;
      }
    },
    cancel() {
      this.visible = false;
    },
    selectPeople(item) {
      this.selectedPeople = item;
      this.isDisabled = false;
    },
    reset() {
      this.selectedPeople = {};
    },
  },
  watch: {
    value(val) {
      // 关闭该弹框时清空已选中的树列表
      if (!val) {
        this.$refs.uiSearchTree.setCheckedKeys([]);
        this.cameraList = [];
        this.selectedCameraList = [];
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    //初始化选中的摄像机
    defaultPeopleList(val) {
      this.selectedCameraList = [];
      let checkTreeKey = [];
      val.forEach((row) => {
        checkTreeKey.push(row.orgCode);
        this.$set(row, 'checked', true);
      });
      checkTreeKey = Array.from(new Set(checkTreeKey));
      this.$refs.uiSearchTree.setCheckedKeys(checkTreeKey);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  props: {
    value: {},
    defaultPeopleList: {},
    title: {
      default: '工单接收人',
    },
    // 是否查看
    isView: {
      type: Boolean,
    },
    filedName: {
      default: '',
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
