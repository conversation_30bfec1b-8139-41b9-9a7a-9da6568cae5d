<template>
  <ui-modal
    class="configuration-basic-information"
    v-model="visible"
    :styles="styles"
    :title="moduleAction.title"
    :loading="loading"
    @onCancel="remove"
    @query="query"
  >
    <div class="basic-information">
      <component
        ref="moduleActionModal"
        v-if="visible"
        :is="getComponentName"
        :form-model="formModel"
        :index-type="moduleAction.indexType"
        :module-action="moduleAction"
        :config-info="configInfo"
        :task-index-config="taskIndexConfig"
        @changeModalWidth="changeModalWidth"
      >
      </component>
    </div>
  </ui-modal>
</template>

<script>
import {
  checkConfigmodule,
  detectionRules,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions } from 'vuex';

export default {
  name: 'basic-information',
  components: {
    videoConfig: require('../components/video-config/index.vue').default,
    basisViewConfig: require('../components/basis-view-config/index').default,
    faceViewData: require('../components/face-view-data/index.vue').default,
    archiveData: require('../components/archive-data.vue').default,
    keyperson: require('../components/keyperson/index.vue').default,
    placeData: require('../components/place-data.vue').default,
    platformUser: require('../components/platform-user/index.vue').default,
    vehicleConfig: require('../components/vehicle-config/index').default,
    compositeConfig: require('../components/composite-config/index').default,
    BodyConfig: require('../components/body-config/index.vue').default,
  },
  props: {},
  data() {
    return {
      styles: {
        width: '3.6rem',
      },
      visible: false,
      componentsMap: {
        1: 'basisViewConfig', // 基础
        2: 'faceViewData', // 人脸
        3: 'vehicleConfig', // 车辆
        4: 'videoConfig', // 视频流
        5: 'keyperson', // 重点人员
        6: 'archiveData', // 档案指标
        7: 'platformUser', // 平台可用性
        8: 'placeData', // 场所数据
        9: 'bodyConfig', // 人体
      },
      formModel: '',
      configInfo: {},
      moduleAction: {},
      loading: false,
      taskIndexConfig: {},
    };
  },
  filter: {},
  mounted() {},
  methods: {
    ...mapActions({
      getAlgorithmList: 'algorithm/getAlgorithmList',
    }),
    async init(moduleAction) {
      this.styles.width = '3.7rem';
      if (['PLATFORM_API_STABILITY', 'VIDEO_DEVICE_REVOCATION'].includes(moduleAction.indexType)) {
        this.styles.width = '5rem';
      }
      if (moduleAction.indexType === 'VIDEO_MONITOR_VALID_SUBMIT_QUANTITY_RATE') {
        this.styles.width = '4.5rem';
      }
      if (
        [
          'VIDEO_OSD_ACCURACY_PROMOTION_RATE',
          'BASIC_CJQY_QUANTITY_STANDARD',
          'VIDEO_READ_PROMOTION_RATE',
          'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE',
          'VIDEO_PLATFORM_ONLINE_RATE',
        ].includes(moduleAction.indexType)
      ) {
        this.styles.width = '4.5rem';
      }
      this.moduleAction = JSON.parse(JSON.stringify(moduleAction));
      if (checkConfigmodule.includes(this.moduleAction.indexType)) {
        const ruleListArr = [];
        if (detectionRules[this.moduleAction.indexType]) {
          this.moduleAction.checkConfig = this.$util.common.deepCopy(detectionRules[this.moduleAction.indexType]);
        } else {
          const curRule = ruleListArr.filter((item) => item.indexs.includes(this.moduleAction.indexType));
          this.moduleAction.checkConfig = this.$util.common.deepCopy(
            curRule.length ? curRule[0].rule : detectionRules['routine'],
          );
        }
      }
      await this.getAlgorithmList();
      if (moduleAction.config) {
        this.getConfig();
      } else {
        this.formModel = 'add';
        this.visible = true;
      }
    },
    async query() {
      let validate = await this.$refs['moduleActionModal'].handleSubmit(); // 校验必填项
      if (validate) {
        await this.addConfig();
        this.$emit('updateInfo');
      }
    },
    remove() {
      this.visible = false;
    },
    async addConfig() {
      this.loading = true;
      const { formData } = this.$refs['moduleActionModal']; // 配置弹框数据
      const { id, indexId, taskSchemeId } = this.moduleAction;
      const {
        cronData,
        cronType,
        detectMode,
        depIndexId,
        timePoints,
        directoryNames,
        directoryIds,
        deviceQueryForm,
        selectType,
        orgCodes,
        orgCodeList,
        captureNum,
        deviceNum,
        reinspect,
        // detectionMode,
        orgCountMap,
        whole,
        ...rest
      } = formData;
      const params = {
        id,
        indexId,
        taskSchemeId,
        cronData,
        cronType,
        detectMode,
        depIndexId,
        timePoints,
        extensionData: {
          indexConfig: {
            ...rest,
          },
          customConfig: {
            selectType,
            deviceQueryForm: {
              ...deviceQueryForm,
              directoryNames,
              directoryIds,
            },
          },
          sampleConfig: {
            orgCodes,
            captureNum,
            deviceNum,
          },
        },
      };
      // 检测方式: 抽检方式参数需添加到extensionData
      if (params.detectMode === '2') {
        orgCountMap !== undefined ? (params.extensionData.sampleConfig.orgCountMap = orgCountMap) : null;
        whole !== undefined ? (params.extensionData.sampleConfig.whole = whole) : null;
      }
      // 抽检方式：从 xxxx指标 检测结果统计
      if (params.detectMode === '4') {
        params.cronType = '1';
        params.cronData = [0];
      }
      // 共享联网平台在线率 检测平台字段和其他指标不一致做的处理
      if (this.moduleAction.indexType === 'VIDEO_PLATFORM_ONLINE_RATE') {
        params.extensionData.indexConfig.orgCodeList = [];
        params.extensionData.sampleConfig.orgCodes = orgCodeList.length ? orgCodeList.join(',') : '';
        params.extensionData.sampleConfig.deviceNum = parseInt(deviceNum);
      }
      if (['VIDEO_CODE_STANDARD_RATE'].includes(this.moduleAction.indexType)) {
        params.extensionData.indexConfig.videoCodeType = formData.videoCodeType.length
          ? formData.videoCodeType.join(',')
          : '';
      }
      if (!!reinspect && Object.keys(reinspect).length) {
        params.extensionData.reinspect = reinspect;
      }
      try {
        let res = await this.$http.post(governanceevaluation.addTaskIndexConfig, params);
        this.visible = false;
        let data = {};
        this.global.indexTypeList.map((item) => {
          if (this.moduleAction.indexModule == item.id) {
            data = item;
          }
        });
        this.$emit('updata', data);
        this.$Message.success(res.data.msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    getConfig() {
      const { id } = this.moduleAction;
      this.$http
        .get(governanceevaluation.getTaskIndexConfig + `/${id}`)
        .then((res) => {
          const { cronData, cronType, detectMode, extensionData, timePoints, cronNum, isImportantIndex } =
            res.data.data;
          this.taskIndexConfig = res.data.data;
          const {
            customConfig: {
              deviceQueryForm: { directoryNames, directoryIds, ...rest },
              selectType,
            },
            indexConfig,
            sampleConfig,
            reinspect,
          } = JSON.parse(extensionData);
          this.configInfo = {
            cronData,
            cronType,
            detectMode,
            timePoints,
            ...indexConfig,
            ...sampleConfig,
            directoryNames,
            directoryIds,
            selectType,
            cronNum,
            reinspect,
            deviceQueryForm: {
              isImportantIndex,
              ...rest,
            },
          };
          this.formModel = 'edit';
          this.visible = true;
        })
        .catch((err) => {
          console.log(err, 'err');
        });
    },
    changeModalWidth(width) {
      this.styles.width = width;
    },
  },
  computed: {
    getComponentName() {
      if (this.moduleAction.indexType === 'COMPOSITE_INDEX') {
        return 'compositeConfig'; // 新增指标
      }
      return this.componentsMap[this.moduleAction.indexModule];
    },
  },
};
</script>

<style scoped lang="less">
.configuration-basic-information {
  @{_deep} .ivu-modal-body {
    padding: 0 15px 15px 15px !important;
  }
}
.basic-information {
  max-height: 700px;
  overflow-x: auto;
}

.flex-content {
  @{_deep} .ivu-form-item-content {
    display: flex;
  }
}
.color-primary {
  color: var(--color-primary);
}
.color-white {
  color: #ffffff;
}
.w160 {
  width: 160px;
}
</style>
