<template>
	<div class="search  card-border-color">
		<Form :inline="true">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content-row">
						<FormItem>
							<Select v-model="formData.status" placeholder="请选择申请状态">
								<Option :value="''">全部</Option>
								<Option :value="0">未处理</Option>
								<Option :value="1">同意</Option>
								<Option :value="2">驳回</Option>
							</Select>
						</FormItem>
					</div>
					<div class="btn-group">
						<Button type="primary" @click="search">查询</Button>
						<Button type="default" @click="resetForm">重置</Button>
					</div>
				</div>

			</div>
		</Form>
	</div>
</template>
<script>

	export default {
		components: {},
		data() {
			return {
				formData: {
					status: '',
				},
			}
		},
		created() {

		},
		methods: {
			search() {
				let searchForm = {
					status: this.formData.status,
				};
				this.$emit('searchInfo', searchForm)
			},
			resetForm() {
				this.formData = {
					status: '',
				};
				this.$emit('searchInfo', this.formData)
			},
		}
	}
</script>
<style lang="less" scoped>
	.btn-group {
		display: flex;
	}
	.search {
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;
		.ivu-form-inline {
			width: 100%;
		}
		.ivu-form-item {
			margin-bottom: 16px;
			margin-right: 30px;
			display: flex;
			align-items: center;
			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				max-width: 90px;
			}
			/deep/ .ivu-form-item-content {
				display: flex;
			}
		}
		.general-search {
			display: flex;
			width: 100%;
			.input-content {
				// width: 85%;
				display: flex;
				flex-wrap: wrap;
				.input-content-row {
					display: flex;
					width: 200px;
					margin: 0 10px;
				}
			}
		}
	}
</style>
