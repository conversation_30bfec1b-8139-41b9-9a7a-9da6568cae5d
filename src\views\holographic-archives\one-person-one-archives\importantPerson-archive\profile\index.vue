<template>
  <div class="people-archive-container">
    <div class="content" id="content930">
      <!-- 基础信息 -->
      <BasicInformation :labelType="2" :baseInfo="baseInfo" />
      <div class="main-information">
        <!-- 通联方式 -->
        <Communication
          :id="'communication'"
          class="m-b10"
          :list="communicationList"
          :loading="communicationLoading"
        ></Communication>
        <!-- 涉案/警记录 -->
        <Involve
          :id="'involve'"
          class="m-b10"
          :list="involveList"
          :loading="involveLoading"
        ></Involve>
        <!-- 名下资产 -->
        <SelfProperty
          class="m-b10"
          :list="selfPropertyList"
          :loading="selfPropertyLoading"
        />
        <!-- 人像抓拍 -->
        <PortraitCapture
          :id="'portrait_capture' + routeParams"
          title="人像抓拍"
          :baseInfo="baseInfo"
          :archiveNo="archiveNo"
          :list="portraitCaptureList"
          :loading="portraitCaptureLoading"
          class="m-b10"
        />
        <!-- 人车同拍 -->
        <PeopleCarCapture
          :id="'people_car_capture' + routeParams"
          title="人车同拍"
          :list="peopleCarCaptureList"
          :loading="peopleCarCaptureLoading"
          class="m-b10"
        />
        <!-- 位置信息 -->
        <PositionInformation
          :id="'position_information' + routeParams"
          title="位置信息"
          :list="latestLocationList"
          :latestLocationLoading="latestLocationLoading"
          :oftenGoList="oftenGoList"
          :oftenGoLoading="oftenGoLoading"
          :positionPoints="positionPoints"
          :heatData="heatData"
          class="m-b10"
          @on-change="frequentedList"
        />
        <!-- 行为规律 -->
        <LawBehavior
          :id="'law_behavior' + routeParams"
          title="行为规律"
          :timeSlotSeries="timeSlotSeries"
          :activeNumXAxis="activeNumXAxis"
          :activeNumSeries="activeNumSeries"
          :timeSlotLoading="timeSlotLoading"
          :activeNumLoading="activeNumLoading"
          class="m-b10"
          @on-change="lawBehavior"
        />
        <!-- 背景信息 -->
        <BackgroundInformation
          :id="'background_information' + routeParams"
          title="背景信息"
          class="m-b10"
        />
        <!-- 关系信息 -->
        <RelationshipInfomation
          v-if="graphObj"
          :id="'relationship_information' + routeParams"
          title="关系信息"
          class="m-b10"
          :baseInfo="baseInfo"
          :loading1="peopleTogetherLoading"
          :list1="peopleTogetherList"
          :loading2="byCarTogetherLoading"
          :list2="byCarTogetherList"
        />
        <!-- 其他信息 -->
        <OtherInformation
          :id="'other_information' + routeParams"
          title="其他信息"
          class="m-b10"
        />
      </div>
      <div class="anchor-point-infomation">
        <!-- <Button type="primary" class="export-btn">导出</Button> -->
        <!-- 锚点 -->
        <UiAnchor :anchorLinkList="sumAnchorLinkList" />
      </div>
    </div>
  </div>
</template>
<script>
import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information.vue";
import Communication from "@/views/juvenile-archives/profile/components/communication.vue";
import Involve from "@/views/juvenile-archives/profile/components/involve.vue";
import PortraitCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/portrait-capture";
import PeopleCarCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/people-car-capture";
import PositionInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/position-information";
import LawBehavior from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/law-behavior";
import BackgroundInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/background-information.vue";
import OtherInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/other-information.vue";
import RelationshipInfomation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/relationship-infomation.vue";
import Alarm from "@/views/juvenile-archives/profile/components/alarm.vue";
import UiAnchor from "@/components/ui-anchor.vue";
import SelfProperty from "./components/self-property.vue";
import {
  getPortraitCaptureList,
  getPeopleCarCaptureList,
  getLatestLocationList,
  getFrequentedList,
  drivingVehicle,
  getBehavioralRulesStatics,
  getActivitiesNumStatics,
  ownedVehicles,
} from "@/api/realNameFile";
import {
  getPersonCommunication,
  getPersonCase,
  getPersonIncidents,
} from "@/api/monographic/base.js";
import mixinWidget from "@/mixins/mixinWidget.js";
import { mapGetters } from "vuex";

export default {
  name: "people-archive",
  props: {
    baseInfo: {
      type: Object | String,
      default: {},
    },
  },
  components: {
    BasicInformation,
    Communication,
    Involve,
    PortraitCapture,
    PeopleCarCapture,
    PositionInformation,
    UiAnchor,
    Alarm,
    LawBehavior,
    BackgroundInformation,
    RelationshipInfomation,
    OtherInformation,
    SelfProperty,
  },
  mixins: [mixinWidget],
  data() {
    return {
      archiveNo: "",
      routeParams: "",
      anchorLinkList: [
        { href: "#communication", title: "通联方式" },
        { href: "#involve", title: "涉案/警记录" },
        { href: "#portrait_capture", title: "人像抓拍" },
        { href: "#people_car_capture", title: "人车同拍" },
        { href: "#position_information", title: "位置信息" },
        { href: "#law_behavior", title: "行为规律" },
        { href: "#background_information", title: "背景信息" },
        { href: "#relationship_information", title: "关系信息" },
        { href: "#other_information", title: "其他信息" },
      ],
      communicationLoading: false,
      communicationList: [],
      involveList: [],
      involveLoading: false,
      selfPropertyList: [],
      selfPropertyLoading: false,
      assetsUnderNameList: [],
      assetsUnderNameLoading: false,
      portraitCaptureList: [],
      portraitCaptureLoading: false,
      peopleCarCaptureList: [],
      peopleCarCaptureLoading: false,
      latestLocationList: [],
      latestLocationLoading: false,
      oftenGoList: [],
      oftenGoLoading: false,
      latestLocationPoints: [],
      positionPoints: [],
      heatData: [],
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
      timeSlotLoading: false,
      activeNumXAxis: {
        data: [1, 2, 3, 4, 5, 6, 7],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
          // rotate: 40
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      activeNumSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [0, 0, 0, 0, 0, 0, 0],
          barWidth: "30%",
          itemStyle: {
            color: "#2C86F8",
          },
        },
        {
          name: "晚上",
          type: "bar",
          stack: "one",
          data: [0, 0, 0, 0, 0, 0, 0],
          barWidth: "30%",
          itemStyle: {
            color: "#F29F4C",
          },
        },
      ],
      activeNumLoading: false,
      peopleTogetherList: [],
      peopleTogetherLoading: false,
      byCarTogetherList: [],
      byCarTogetherLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
    sumAnchorLinkList() {
      if (this.graphObj) {
        return this.anchorLinkList;
      } else {
        return this.anchorLinkList.filter(
          (e) => e.href !== "#relationship_information"
        );
      }
    },
  },
  async created() {
    let { archiveNo, source, initialArchiveNo } = this.$route.query;
    this.archiveNo = archiveNo;
    this.routeParams = `?archiveNo=${archiveNo}&source=${source}&initialArchiveNo=${initialArchiveNo}`;
    this.sumAnchorLinkList.forEach((v) => {
      v.href = v.href + this.routeParams;
    });
    // 人像抓拍
    this.portraitCaptureLoading = true;
    getPortraitCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 1,
    })
      .then((res) => {
        this.portraitCaptureList = res.data || [];
      })
      .catch(() => {})
      .finally(() => {
        this.portraitCaptureLoading = false;
      });
    // 人车同拍
    this.peopleCarCaptureLoading = true;
    getPeopleCarCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 5,
      dataType: 1, //1-实名档案，2-路人档案
    })
      .then((res) => {
        this.peopleCarCaptureList = res.data || [];
      })
      .catch(() => {})
      .finally(() => {
        this.peopleCarCaptureLoading = false;
      });
    // 最新位置
    this.latestLocationLoading = true;
    getLatestLocationList({
      archiveNo: this.archiveNo,
      dataSize: 5,
      dataType: 1,
    })
      .then((res) => {
        this.latestLocationList = res.data;
        this.latestLocationPoints = this.latestLocationList.map((v) => {
          return {
            ...v,
            ...v.geoPoint,
          };
        });
        // 将最新位置点位和常去地点位合并
        this.positionPoints = this.positionPoints.concat(
          this.latestLocationPoints
        );

        this.frequentedList(1);
      })
      .catch(() => {})
      .finally(() => {
        this.latestLocationLoading = false;
      });
    this.lawBehavior(1);
  },
  mounted() {
    this.getCommunicationList();
    this.getInvolveList();
    this.getSelfPropertyList();
  },
  methods: {
    async getOwnVehicleList(source) {
      try {
        if (!this.archiveNo) return;
        this.assetsUnderNameLoading = true;
        let params = {
          archiveNo: this.archiveNo,
          dataSize: 5,
          dataType: source == "zdr" ? 3 : 1,
        };
        //   let res = await getOwnVehicleByIdCard(params);
        let res = await drivingVehicle(params);
        this.assetsUnderNameList = res.data;
      } catch (e) {
        console.log(e);
      } finally {
        this.assetsUnderNameLoading = false;
      }
    },
    frequentedList(val, startDate, endDate) {
      this.oftenGoLoading = true;
      getFrequentedList({
        archiveNo: this.archiveNo,
        dataRange: val,
        dataType: 1,
        startDate,
        endDate,
      })
        .then((res) => {
          let { locationList, heatmapList } = res.data;
          this.oftenGoList = locationList;
          // 常去地点位
          let positionPoints = this.oftenGoList.map((v) => {
            if (v.type === 1) {
              // 人脸抓拍位置
              return {
                ...v.geoPoint,
                ...v,
                type: "face",
                markerIconUrl: require("@/assets/img/archives/marker_face.png"),
              };
            } else if (v.type === 2) {
              // 车辆抓拍位置
              return {
                ...v.geoPoint,
                ...v,
                type: "vehicle",
                markerIconUrl: require("@/assets/img/archives/marker_vehicle.png"),
              };
            } else if (v.type === 3) {
              // IMSI感知数据位置
              return {
                ...v.geoPoint,
                ...v,
                type: "imsi",
                markerIconUrl: require("@/assets/img/archives/marker_imsi.png"),
              };
            }
          });
          // 将最新位置点位和常去地点位合并
          this.positionPoints = [
            ...this.latestLocationPoints,
            ...positionPoints,
          ];
          // 常去地热力图
          this.heatData = heatmapList.map((v) => {
            return {
              ...v.geoPoint,
              numCount: v.times,
            };
          });
        })
        .catch(() => {})
        .finally(() => {
          this.oftenGoLoading = false;
        });
    },
    // 通联信息
    async getCommunicationList() {
      this.communicationLoading = true;
      try {
        let { data = [] } = await getPersonCommunication(this.archiveNo);
        this.communicationList = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.communicationLoading = false;
      }
    },
    // 涉案列表
    async getInvolveList() {
      this.involveLoading = true;
      try {
        let { data } = await getPersonCase(this.archiveNo);
        this.involveList = data;
        // 涉警暂时没信息
        let { dataIncidents } = await getPersonIncidents(this.archiveNo);
        this.involveList.push(...dataIncidents);
      } catch (err) {
        console.log(e);
      } finally {
        this.involveLoading = false;
      }
    },
    // 名下资产
    async getSelfPropertyList() {
      this.selfPropertyLoading = true;
      try {
        let { data = [] } = await ownedVehicles(this.archiveNo);
        this.selfPropertyList = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.selfPropertyLoading = false;
      }
    },
    lawBehavior(val, startDate, endDate) {
      // 活动时间段
      this.timeSlotLoading = true;
      getBehavioralRulesStatics({
        archiveNo: this.archiveNo,
        dataType: 1,
        type: val,
        startDate,
        endDate,
      })
        .then((res) => {
          this.timeSlotSeries[0].data = [];
          // 后端返回白天、晚上开始时间格式为"白天开始时间-晚上开始时间"
          let dayStart = res.data.daytimeRange.split("-")[0];
          let dayEnd = res.data.daytimeRange.split("-")[1];
          let timeList = res.data.x;
          let dataList = res.data.y;
          dataList.forEach((v, i) => {
            // 当前时间》=白天开始时间且《晚上开始时间则为白天，否则为晚上
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i}点`,
                value: v,
                itemStyle: {
                  color: "#F29F4C",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
      // 活动次数
      this.activeNumLoading = true;
      getActivitiesNumStatics({
        archiveNo: this.archiveNo,
        dataType: 1,
        type: val,
        startDate,
        endDate,
      })
        .then((res) => {
          if (val === 2) {
            this.activeNumXAxis.axisLabel.rotate = 0;
          } else {
            this.activeNumXAxis.axisLabel.rotate = 40;
          }
          this.activeNumXAxis.data = res.data.x;
          this.activeNumSeries[0].data = res.data.day;
          this.activeNumSeries[1].data = res.data.night;
        })
        .catch(() => {})
        .finally(() => {
          this.activeNumLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}
.people-archive-container {
  padding: 16px 10px 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
  .content {
    display: flex;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    flex: 1;
    .main-information {
      width: 1442px;
      height: min-content;
      padding: 0 10px;
      margin-left: 350px;
      /deep/ .ui-card {
        overflow: unset !important;
        .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
      }
    }
    .anchor-point-infomation {
      width: 100px;
      position: fixed;
      top: 78px;
      right: 18px;
      z-index: 9;
      .export-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
