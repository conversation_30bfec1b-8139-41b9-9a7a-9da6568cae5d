/**
 * Created by dell on 2017/8/14.
 */
import { MapPointModel } from "../../map.main";
const ClusterMarker = NPMapLib.Symbols.ClusterMarker;
/**
 * 统一在此处转换界面上的model
 */
export default class ConvertModelUtil {
  /**
   *
   * @param origins
   * @return {Array<MapPointModel>} 若origins为空, 则返回空数组
   */
  static convertCameraArr2MapPoint(origins) {
    debugger;
    let result = [];
    if (origins && origins.length > 0) {
      angular.forEach(origins, (model) => {
        let temp = ConvertModelUtil.convertCamera2MapPoint(model);
        if (temp) {
          result.push(temp);
        }
      });
    }
    return result;
  }

  /**
   *
   * @param origin
   * @return 若origin为空, 则返回null
   */
  static convertCamera2MapPoint(origin) {
    let result = null;
    // 由于当前摄像机不存在点位信息, 所以过掉
    if (!origin || !origin.systemPoint) return result;

    result = {};
    result.ID = origin.systemPoint.ID;
    result.Name = origin.Name;
    result.ObjectID = origin.systemPoint.ObjectID;
    result.ObjectType = origin.systemPoint.ObjectType;
    result.LayerType = origin.systemPoint.LayerType;
    result.Lat = origin.systemPoint.Lat;
    result.Lon = origin.systemPoint.Lon;
    result.Ext = origin;
    return result;
  }

  static convertMapPointArr2Camera(origins) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = ConvertModelUtil.convertMapPoint2Camera(model);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }

  static convertMapPoint2Camera(origin) {
    return origin && origin.Ext;
  }
  /**
   * 用于将systempoint数组转换为地图点位model
   * @param data
   * @param active 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPointArr2MapPoint(origins, active) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = ConvertModelUtil.convertSystemPoint2MapPoint(model, active);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }
  /**
   * 用于将systempoint数组转换为地图点位model
   * 用于资源图层场所数据
   * @param data
   * @param active 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPointArr3MapPoint(origins, active) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = null;
      if (model.mapType) {
        temp = ConvertModelUtil.convertSystemPoint2MapPoint(
          model,
          active,
          "mapType"
        );
      } else {
        temp = ConvertModelUtil.convertSystemPoint3MapPoint(
          model,
          active,
          "placeTypeCode"
        );
      }
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }
  /**
   * 用于将systempoint转换为地图点位model
   * @param data
   * @param active: 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPoint2MapPoint(origin, active) {
    let result = null;
    if (!origin) return result;
    result = {};
    switch (origin.mapType) {
      case "1":
        if (origin.deviceChildType == "1" || origin.deviceChildType == "2") {
          result.LayerType = "Camera_QiuJi";
        } else {
          result.LayerType = "Camera_QiangJi";
        }
        break;
      case "11":
        result.LayerType = "Camera_Face";
        break;
      case "2":
        result.LayerType = "Camera_Vehicle";
        break;
      case "3":
        result.LayerType = "Camera_Wifi";
        break;
      case "5":
        result.LayerType = "Camera_RFID";
        break;
      case "4":
        result.LayerType = "Camera_Electric";
        break;
      case "16":
        result.LayerType = "Camera_ETC";
        break;
      default:
        result.LayerType = "Camera";
        break;
    }
    result.OrgName = origin.orgCodeName || "暂未挂载";
    result.deviceId = origin.deviceId;
    result.deviceGbId = origin.deviceGbId;
    result.ObjectType = "Camera";
    result.Lat = origin.geoPoint
      ? origin.geoPoint.lat
      : origin.lat || origin.latitude || origin.Lat;
    result.Lon = origin.geoPoint
      ? origin.geoPoint.lon
      : origin.lon || origin.longitude || origin.Lon;
    result.Ext = origin.extModel;
    result.deviceName = origin.deviceName || "暂未挂载";
    result.detailAddress = origin.detailAddress || "暂未挂载";
    result.Status = origin.isOnline;
    result.mapType = origin.mapType;
    result.isNormal = origin.checkStatus;
    result.TagList = origin.tagList
      ? origin.tagList.filter((e) => e.tagName)
      : [];
    result.ErrorMessage = origin.errorMessage
      ? origin.errorMessage.filter((e) => e)
      : [];
    result.ImageUrls = origin.imageUrls;
    result.imageUrls = origin.imageUrls;
    result.sbgnlx = origin.sbgnlx;
    result.deviceType = origin.deviceType;
    result.deviceChildType = origin.deviceChildType;
    result.picUrl = origin.picUrl;
    result.ptzType = origin.ptzType;
    result.isCommunity = !!origin.isCommunity;
    result.sx_place = origin.sx_place;
    result.sx_project = origin.sx_project;
    result.sx_construction_unit = origin.sx_construction_unit;
    result.sx_warranty_period = origin.sx_warranty_period;
    result.sx_operation_unit = origin.sx_operation_unit;
    if (origin.active) {
      result.Active = true;
    }
    return result;
  }
  /**
   * 用于将systempoint转换为地图点位model
   * @param data
   * @param active: 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPoint3MapPoint(origin, active) {
    let result = null;
    if (!origin) return result;
    result = {};
    switch (origin.placeTypeCode) {
      case "B1001":
        result.LayerType = "Place_Hotel";
        break;
      case "B0903":
        result.LayerType = "Place_InterBar";
        break;
      case "B0101":
        result.LayerType = "Place_Government";
        break;
      case "B0701":
        result.LayerType = "Place_School";
        break;
      case "B9999":
        result.LayerType = "Place_Key";
        break;
      default:
        result.LayerType = "Place_Hotel";
        break;
    }
    result.OrgName = origin.orgCodeName || "暂未挂载";
    result.deviceId = origin.deviceId;
    result.ObjectType = "Place_Hotel";
    result.Lat = origin.location ? origin.location.lat : origin.geoPoint.lat;
    result.Lon = origin.location ? origin.location.lon : origin.geoPoint.lon;
    result.Ext = origin.extModel;
    result.deviceName = origin.deviceName || "暂未挂载";
    result.detailAddress = origin.detailAddress || "暂未挂载";
    result.Status = origin.isOnline;
    result.placeTypeCode = origin.placeTypeCode;
    result.mapType = origin.placeTypeCode;
    result.isNormal = origin.checkStatus;
    result.TagList = origin.tagList
      ? origin.tagList.filter((e) => e.tagName)
      : [];
    result.ErrorMessage = origin.errorMessage
      ? origin.errorMessage.filter((e) => e)
      : [];
    result.ImageUrls = origin.imageUrls;
    result.sbgnlx = origin.sbgnlx;
    result.deviceType = origin.deviceType;
    result.picUrl = origin.picUrl;

    result.placeName = origin.placeName;
    result.placeTypeName = origin.placeTypeName;
    if (origin.active) {
      result.Active = true;
    }
    return result;
  }

  // 未成年人场所点位数据/图标类型添加
  static convertSystemPoint3MapPointJuvenile(origin, active) {
    let result = null;
    if (!origin) return result;
    result = { ...origin};
    result.LayerType =  origin.LayerType;
    let point = JSON.parse(origin.centerPoint || "[]");
    result.Lat = point[1];
    result.Lon = point[0];
    result.isPlace = true;
    result.center = point;
    result.lat =  point[1];
    result.lon =  point[0];
    result.properties =  {
        id: origin.id,
      }
    // result.otherParam = {
    //   ...origin,
    //   center: point,
    //   lat: point[1],
    //   lon: point[0],
    //   properties: {
    //     id: origin.id,
    //   },
    // };

    return result;
  }
  // 未成年人场所批量
  static convertSystemPoint3MapArrPointJuvenile(origins, active) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = null;
      temp = ConvertModelUtil.convertSystemPoint3MapPointJuvenile(model);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }

  // 未成年人报警
  static convertSystemPoint3MapPointJuvenileAlarm(origin, active) {
    let result = null;
    if (!origin) return result;
    result = {};
    // 根据 taskLevel 来判断报警级别渲染报警图标
    switch (origin.taskLevel) {
      case "1":
        result.LayerType = "LevelOne";
        break;
      case "2":
        result.LayerType = "LevelTwo";
        break;
      case "3":
        result.LayerType = "LevelThree";
        break;
      default:
        result.LayerType = "LevelThree";
        break;
    }
    let point = origin.geoPoint;
    result.Lat = point.lat;
    result.Lon = point.lon;
    result.otherParam = {
      ...origin,
      center: point,
      lat: point.lat,
      lon: point.lon,
      properties: {
        id: origin.id,
      },
    };

    return result;
  }
  static convertSystemPoint3MapArrPointJuvenileAlarm(origin, active) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = null;
      temp = ConvertModelUtil.convertSystemPoint3MapPointJuvenileAlarm(model);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }
  /**
   * 将聚合点位变成地图点位实体
   * map文件夹内部使用的转换方法, 外部业务请勿使用
   * @private
   */
  static _convertClusterMarkerEx2MapPointModel(origin) {
    return (origin && origin.ext) || null;
  }

  /**
   * 将聚合点位增加一些额外参数
   * map文件夹内部使用的转换方法, 外部业务请勿使用
   * @private
   */
  static _convertClusterMarker2ClusterMarkerEx(origin, ext) {
    if (!origin) return null;
    origin.id = ext.ID;
    origin.objectId = ext.ObjectID;
    origin.titleName = ext.Name;
    origin.ext = ext;
  }
}
