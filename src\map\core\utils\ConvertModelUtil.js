/**
 * Created by dell on 2017/8/14.
 */
import { MapPointModel } from '../../map.main';
const ClusterMarker = NPMapLib.Symbols.ClusterMarker;
/**
 * 统一在此处转换界面上的model
 */
export default class ConvertModelUtil {
  /**
   *
   * @param origins
   * @return {Array<MapPointModel>} 若origins为空, 则返回空数组
   */
  static convertCameraArr2MapPoint(origins) {
    let result = [];
    if (origins && origins.length > 0) {
      angular.forEach(origins, (model) => {
        let temp = ConvertModelUtil.convertCamera2MapPoint(model);
        if (temp) {
          result.push(temp);
        }
      });
    }
    return result;
  }

  /**
   *
   * @param origin
   * @return 若origin为空, 则返回null
   */
  static convertCamera2MapPoint(origin) {
    let result = null;
    // 由于当前摄像机不存在点位信息, 所以过掉
    if (!origin || !origin.systemPoint) return result;

    result = {};
    result.ID = origin.systemPoint.ID;
    result.Name = origin.Name;
    result.ObjectID = origin.systemPoint.ObjectID;
    result.ObjectType = origin.systemPoint.ObjectType;
    result.LayerType = origin.systemPoint.LayerType;
    result.Lat = origin.systemPoint.Lat;
    result.Lon = origin.systemPoint.Lon;
    result.Ext = origin;
    return result;
  }

  static convertMapPointArr2Camera(origins) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = ConvertModelUtil.convertMapPoint2Camera(model);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }

  static convertMapPoint2Camera(origin) {
    return origin && origin.Ext;
  }
  /**
   * 用于将systempoint数组转换为地图点位model
   * @param data
   * @param active 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPointArr2MapPoint(origins, active) {
    let result = [];
    if (!origins || origins.length == 0) return result;

    origins.forEach((model) => {
      let temp = ConvertModelUtil.convertSystemPoint2MapPoint(model, active);
      if (temp) {
        result.push(temp);
      }
    });
    return result;
  }
  /**
   * 用于将systempoint转换为地图点位model
   * @param data
   * @param active: 用来显示是否为选中状态
   * @return 若origin为空, 则返回null
   */
  static convertSystemPoint2MapPoint(origin, active) {
    let result = null;
    if (!origin) return result;
    result = {};
    if (origin.sbgnlx && !origin.sbgnlx.includes('/')) {
      switch (origin.sbgnlx) {
        case '2':
          result.LayerType = 'Camera_Vehicle';
          break;
        case '3':
          result.LayerType = 'Camera_Face';
          break;
        case '1':
          result.LayerType = 'Camera';
          break;
        default:
          result.LayerType = 'Camera';
          break;
      }
    } else if (origin.sbgnlx && origin.sbgnlx.includes('/')) {
      result.LayerType = 'Camera_Multi';
    }
    result.OrgName = origin.orgCodeName || '暂未挂载';
    result.ObjectID = origin.deviceId;
    result.id = origin.id;
    result.ObjectType = 'Camera';
    result.Lat = origin.latitude;
    result.Lon = origin.longitude;
    result.Ext = origin.extModel;
    result.Name = origin.deviceName || '暂未挂载';
    result.Address = origin.address || '暂未挂载';
    result.Status = origin.isOnline;
    result.isNormal = origin.checkStatus;
    result.TagList = origin.tagList ? origin.tagList.filter((e) => e.tagName) : [];
    result.ErrorMessage = origin.errorMessage ? origin.errorMessage.filter((e) => e) : [];
    result.ImageUrls = origin.imageUrls;
    if (origin.active) {
      result.Active = true;
    }
    return result;
  }

  /**
   * 将聚合点位变成地图点位实体
   * map文件夹内部使用的转换方法, 外部业务请勿使用
   * @private
   */
  static _convertClusterMarkerEx2MapPointModel(origin) {
    return (origin && origin.ext) || null;
  }

  /**
   * 将聚合点位增加一些额外参数
   * map文件夹内部使用的转换方法, 外部业务请勿使用
   * @private
   */
  static _convertClusterMarker2ClusterMarkerEx(origin, ext) {
    if (!origin) return null;
    origin.id = ext.ID;
    origin.objectId = ext.ObjectID;
    origin.titleName = ext.Name;
    origin.ext = ext;
  }
}
