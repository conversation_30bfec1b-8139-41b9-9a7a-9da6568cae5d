<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" class="confimDataModal" footer-hide>
    <div class="container">
      <div class="left-content">
        <evaluation-tree @faceDetailIdx="faceDetailIdx" @clickNode="clickNode" expandAll :nodeData="treeData">
        </evaluation-tree>
      </div>
      <div class="center-content"></div>
      <div class="right-content ml-sm">
        <div class="times">
          <span class="fl text_left mr-lg">指标名称：重点人脸卡口设备URL可用率</span>
          <span class="fl text_left">评测时间：{{ row.createTime }}</span>
        </div>
        <div class="echarts-title">
          <line-title title-name="检测结果统计"></line-title>
        </div>
        <div class="chart-box">
          <div class="chart">
            <draw-echarts
              :echart-option="echartRing"
              :echart-style="ringStyle"
              ref="zdryChart"
              class="charts"
            ></draw-echarts>
          </div>
          <div class="line"></div>
          <disqualification :moduleData="moduleData"></disqualification>
        </div>
        <div class="btns">
          <Button class="btn_jk" :class="{ active: curBtn === 2 }" @click="changeBtn(2)"> url不可访问 </Button>
          <Button class="btn_kk" :class="{ active: curBtn === 1 }" @click="changeBtn(1)"> url可访问 </Button>
          <!-- <Button class="btn_k" :class="{ active: curBtn === 3 }" @click="changeBtn(3)">
            无法检测
          </Button> -->
        </div>
        <div class="table-box">
          <table-data
            :tableData="tableData"
            :table-show="2"
            :title-text="9"
            :result-text="resultText"
            ref="tableData"
          ></table-data>
          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: ['value', 'row'],
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9.21875rem',
      },
      echartRing: {},
      ringStyle: {
        width: '600px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['URL可访问图片数量', 'URL不可访问图片数量'],
        showData: [
          { name: 'URL可访问图片数量', value: 0 },
          { name: 'URL不可访问图片数量', value: 0 },
        ],
        count: null,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      faceData: {},
      visible: false,
      loading: false,
      detailList: [
        {
          value1: '字段1',
          vaule: 1,
        },
      ],
      nodeData: [
        {
          deviceName: '人脸卡口',
          total: 0,
          children: [
            {
              label: '二级 1-1',
            },
          ],
        },
      ],
      //树
      defaultProps: {
        children: 'children',
        label: 'deviceName',
      },
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      //disqualification
      moduleData: {
        rate: '设备合格率',
        rateValue: 0,
        price: '设备合格达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      treeData: [],
      tableData: [],
      minusTable: 500,
      curBtn: 2,
      searchValue: '',
      resultText: 2,
    };
  },
  created() {},
  mounted() {},
  methods: {
    async getFaceResultOverview() {
      try {
        let res = await this.$http.get(governanceevaluation.faceResultOverview, {
          params: { id: this.row.id },
        });
        this.faceData = res.data.data;
        this.moduleData.rateValue = this.faceData.qualifiedRate;
        this.moduleData.priceValue = this.faceData.standardsValue;
        this.moduleData.resultValue = this.faceData.qualifiedText;
      } catch (err) {
        console.log(err);
      }
    },
    changeBtn(val) {
      this.curBtn = val;
      this.resultText = val;
      this.init();
    },
    async init() {
      try {
        let data = {
          outcome: this.curBtn,
          faceDeviceDetailId: this.row.id,
          searchValue: this.searchValue,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.faceDetailPageList, data);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    async getWithinFaceTree(val) {
      let data = {
        resultId: this.row.resultId,
        indexId: this.row.indexId,
        deviceId: this.row.deviceId,
        outcome: val,
      };
      try {
        let res = await this.$http.post(governanceevaluation.getDeviceDataList, data);
        if (res.data.code === 200) {
          if (!res.data.data || res.data.data.length === 0) {
            this.treeData = res.data.data || [];
          } else {
            let data = [{ deviceName: '人脸卡口', total: res.data.data.length }];
            data[0].children = res.data.data;
            this.treeData = data;
          }
        }
      } catch (err) {
        console.log(err);
      }
    },
    faceDetailIdx(val) {
      this.getWithinFaceTree(val);
    },
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === 'URL可访问图片数量') {
          item.value = this.faceData.qualifiedNum;
        } else {
          item.value = this.faceData.unqualifiedNum;
        }
      });
      this.zdryChartObj.count = this.faceData.total;
      let formatData = {
        seriesName: '检测图片总量',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    clickNode(val) {
      this.searchValue = val.deviceId;
      this.init();
      this.searchData.pageSize = this.searchData.pageSize;
      this.searchData.pageNum = 1;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
  },
  watch: {
    async visible(val) {
      this.$emit('input', val);
      await this.getFaceResultOverview();
      await this.initRing();
      await this.init();
      await this.getWithinFaceTree();
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    EvaluationTree: require('../evaluation-tree.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    LineTitle: require('@/components/line-title.vue').default,
    disqualification: require('../disqualification.vue').default,
    tableData: require('./table-data.vue').default,
  },
};
</script>

<style lang="less" scoped>
.confimDataModal {
  @{_deep} .ivu-modal-body {
    padding: 50px 20px 0 20px;
  }
  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  .container {
    position: relative;
    // height: 900px;
    .left-content {
      width: 306px-20px;
      float: left;
    }
    .center-content {
      float: left;
      margin-top: -50px;
      width: 1px;
      background: #1b3b65;
      // height: 950px;
    }
    .right-content {
      margin-left: 310px;
      height: 100%;
      background: var(--bg-content);
      .times {
        span:first-child {
          margin-right: 40px;
        }
      }
      .echarts-title {
        margin-bottom: 10px;
      }
      //  视频流头部
      .chart-box {
        width: 100%;
        display: flex;
        height: 180px;
        line-height: 180px;
        align-items: center;
        background-color: var(--bg-sub-content);
        .chart {
          height: 180px;
          line-height: 180px;
          width: 48%;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
      .btns {
        margin-top: 10px;
        margin-bottom: 10px;
        .btn_jk {
          border-right: none !important;
        }
        .btn_kk {
          // border-right: none !important;
        }
        .btn_k {
        }
        button {
          color: #56789c;
          background-color: #12294e;
          border-radius: 0;
        }
        .active {
          color: #fff;
          background-color: #2d8cf0;
        }
        button:hover {
          color: #fff;
          background-color: #2d8cf0;
        }
        .ivu-btn.ivu-btn-default:hover {
          color: #fff !important;
        }
      }
      .table-box {
        overflow: hidden;
        position: relative;
      }
    }
  }
}
</style>
