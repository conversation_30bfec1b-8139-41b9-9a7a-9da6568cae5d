<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo">
      <div slot="search" class="hearder-title">
        <!-- <SearchAccuracy ref="searchCard" @startSearch="startSearch" /> -->
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard
          class="card"
          :list="row"
          :personTypeList="personTypeList"
          :cardInfo="cardInfo"
          @detail="detailInfo"
        ></UiGatherCard>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- 抓拍详情 -->
    <CaptureDetail
      ref="captureDetail"
      :resultId="taskObj.resultId"
      :captureInfo="captureInfo"
      :currentTree="currentTree"
      @detail="detail"
    >
      <template #searchList>
        <CaptureSearch class="mb-sm" @startSearch="startDetailSearch" />
      </template>
    </CaptureDetail>
    <!-- 人脸存疑详情 -->
    <FaceDoubt ref="faceDoubt" />
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    UiGatherCard: require('./component/ui-gather-card.vue').default,
    // SearchAccuracy: require('./component/searchAccuracy').default,
    ChartsContainer: require('../components/chartsContainer').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('../components/tableCard.vue').default,
    CaptureDetail: require('./component/capture-detail.vue').default,
    CaptureSearch: require('./component/capture-search.vue').default,
    FaceDoubt: require('./component/face-doubt.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      bigPictureShow: false,
      // treeData: [],
      imgList: [],
      // selectKey: '', // 机构树
      infoObj: {},
      abnormalCount: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'urlNum' },

        {
          name: '一行',
          children: [
            { name: '异常轨迹：', value: 'abnormalTrajectoryNum', color: '#BC3C19', judge: '' },
            { name: '占比：', value: '1', color: '#BC3C19' },
          ],
        },
      ],
      captureInfo: [],
      loadDataCard: (parameter) => {
        if (!this.taskObj.batchId) {
          console.log('%c!!!重要。 由于batchId为必输项，如果为空，固不发出请求。', 'color:red;');
          return;
        } else {
          return this.$http
            .post(
              governanceevaluation.accuracyLidt,
              Object.assign(parameter, {
                batchId: this.taskObj.batchId,
                indexId: this.taskObj.indexId,
                orgCode: this.taskObj.regionCode,
              }),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    taskObj: {
      handler() {
        this.info();
      },
      deep: true,
      immediate: true,
    },
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(info);
    },
    // 抓拍详情内详情
    detail(info, personObj) {
      if (this.currentTree.id === 602) {
        // 人脸
        this.$refs.faceDoubt.open(info, personObj);
      } else {
        // 车辆
      }
    },
    async info() {
      // this.treeData = this.taskObj.orgCodeList && this.taskObj.orgCodeList.length > 0 ? this.$util.common.arrayToJson(JSON.parse(JSON.stringify(this.taskObj.orgCodeList)), 'id', 'parentId') : []
      // this.selectKey = this.taskObj.orgCode
      switch (this.currentTree.id) {
        case 602:
          this.abnormalCount = [
            {
              title: '检测实有人口档案数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: 0,
            },
            {
              title: '实有人口聚档准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: 0,
            },
            {
              title: '实有人口聚档不准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: 0,
            },
          ];
          this.captureInfo = [
            { name: '姓名：', value: 'name' },
            { name: '证件号：', value: 'idCard' },
          ];
          break;
        case 604:
          this.abnormalCount = [
            {
              title: '注册登记车辆总数',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '检测车辆档案数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '车辆聚档准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '车辆聚档不准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
          ];
          this.captureInfo = [
            { name: '车牌：', value: 'name' },
            { name: '车牌颜色：', value: 'idCard' },
            { name: '车辆类型：', value: 'total' },
          ];
          break;
      }
      await this.static();
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    static() {
      if (!this.taskObj.batchId) {
        console.log('%c!!!重要。 由于batchId为必输项，如果为空，固不发出请求。', 'color:red;');
        return;
      }
      this.$http
        .post(governanceevaluation.personAccuracyCount, {
          batchId: this.taskObj.batchId,
          indexId: this.taskObj.indexId,
          orgCode: this.taskObj.regionCode,
        })
        .then((res) => {
          let data = res.data.data || {};
          this.abnormalCount = [
            {
              title: '检测实有人口档案数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.accuracyCount,
            },
            {
              title: '实有人口聚档准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.count,
            },
            {
              title: '实有人口聚档不准确数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.accuracyCount - data.count,
            },
          ];
        });
    },
    // 检索
    // startSearch(searchData) {
    //   this.searchData = {}
    //   this.selectKey = searchData.orgCodeList
    //   this.searchData = searchData
    //   this.$refs.infoCard.info(true)
    // },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
</style>
