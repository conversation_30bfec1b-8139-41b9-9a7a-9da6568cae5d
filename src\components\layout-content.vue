<template>
  <div class="layout">
    <Layout class="layout-self">
      <Sider
        ref="side"
        hide-trigger
        collapsible
        :collapsed-width="60"
        :value="isCollapsed"
        :width="240"
        :style="siderStyle"
        v-show="isShowSider"
      >
        <nav-Left :menu-list="leftMenuList" :is-collapsed="hasCollapse" @selectMenu="selectMenu"></nav-Left>
      </Sider>
      <Layout>
        <Header class="layout-header-bar">
          <!-- <router-link
            to="/home"
            tag="span"
            class="option go-home pointer"
            :class="homeActive ? 'active' : ''"
          >
            <i class="icon-font icon-zhuyegudingdaohang f-14"></i>
          </router-link> -->
          <ui-tabs
            class="ui-tabs"
            :value="activeRouterName"
            @on-click="selectRouter"
            @on-tab-remove="closeRouter"
            @on-contextmenu="onContextmenu"
          >
            <ui-tab-pane
              v-for="(item, index) in cacheRouterList"
              :key="index"
              :name="item.name"
              :label="item.text"
              :meta="item.meta"
              :closable="hasClosed(index)"
            ></ui-tab-pane>
            <template #contextmenu>
              <ul class="right-menu">
                <li @click="closeTabs(1)">关闭其他标签页</li>
                <li @click="closeTabs(2)">关闭所有标签页</li>
                <li @click="closeTabs(3)">关闭当前标签页</li>
              </ul>
            </template>
          </ui-tabs>
        </Header>
        <Content class="layout-content margin-wrapper-flow" :class="{ p0: isHome }">
          <slot></slot>
        </Content>
      </Layout>
    </Layout>
    <div class="menu-icon" @click="collapsedSider" v-show="!!leftMenuList && !!leftMenuList.length">
      <i :class="rotateIcon"></i>
    </div>
  </div>
</template>
<script>
import { LayoutComponentsEnum } from '@/components/nav-left/utils/enum';
import { mapActions, mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  props: {},
  data() {
    return {
      rightClickName: null,
      hasCollapse: true,
      LayoutComponentsEnum: Object.freeze(LayoutComponentsEnum),
    };
  },
  created() {
    // 将存在session中的缓存路由读取出来
    const cacheRouter = sessionStorage.getItem('cacheRouter');
    // isPortalSkip: true  从门户跳转过来，不需要设置这一步
    if (cacheRouter && !this.isPortalSkip) {
      this.setCacheRouterList(JSON.parse(cacheRouter));
    }
    // 监听浏览器刷新页面，刷新时存入vuex中已经缓存的菜单到session中
    window.addEventListener('beforeunload', () => {
      if (!sessionStorage.getItem('token')) return;
      sessionStorage.setItem('cacheRouter', JSON.stringify(this.cacheRouterList));
    });
    // 获取标签选中状态
    this.getTabsActive();
    // 获取左侧菜单列表
    this.getNavLeftMenu();
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
      closeCacheRouter: 'tabs/closeCacheRouter',
      setActiveRouterName: 'tabs/setActiveRouterName',
      setIsCollapsed: 'common/setIsCollapsed',
      setLeftMenuList: 'permission/setLeftMenuList',
      setWebsocketMsg: 'websocket/setWebsocketMsg',
    }),
    // 缩放左侧菜单
    collapsedSider() {
      this.hasCollapse = !this.hasCollapse;
      // this.setIsCollapsed(!this.isCollapsed)
    },
    // 选中左侧菜单列表
    selectMenu(name) {
      const index = this.cacheRouterList.findIndex((row) => row.name === name);
      if (index === -1) {
        const router = this.routerList.find((item) => item.name === name);
        this.cacheRouterList.push(router);
        this.setCacheRouterList(this.cacheRouterList);
      }
      this.$router.push({ name: name });
    },
    // 选中标签页
    selectRouter(name, meta) {
      this.setActiveRouterName(name);
      // 如果选中的为组件标签，则跳转至组件标签所在地址，并且传入该组件名称参数
      if (meta) {
        this.$router.push({
          name: meta.routeName,
          query: Object.assign(meta.queryParams, {
            componentName: meta.componentName,
          }),
        });
      } else {
        this.$router.push({ name: name });
      }
    },
    // 关闭标签页
    closeRouter(name) {
      // 如果关闭的是当前路由标签，则需要判断向后或向前激活标签并跳转路由
      this.closeCacheRouter(name);
    },
    // 右击标签页
    onContextmenu(name) {
      this.rightClickName = name;
    },
    // 右击菜单点击其中选项触发事件
    closeTabs(num) {
      let routerItem;
      switch (num) {
        case 1:
          routerItem = this.cacheRouterList.find((row) => row.name === this.rightClickName);
          this.setCacheRouterList([routerItem]);
          if (this.activeRouterName !== routerItem.name) {
            this.setActiveRouterName(routerItem.name);
            if (routerItem.meta) {
              this.$router.push({
                name: routerItem.meta.routeName,
                query: Object.assign(routerItem.meta.queryParams, {
                  componentName: routerItem.meta.componentName,
                }),
              });
            } else {
              this.$router.push({ name: this.activeRouterName });
            }
          }
          break;
        case 2:
          this.setCacheRouterList([]);
          this.$router.push({ name: 'home' });
          break;
        case 3:
          this.closeRouter(this.rightClickName);
          break;
      }
    },
    // 获取标签页激活状态
    getTabsActive() {
      const componentName = this.$route.query.componentName || null;
      // 如果路由参数中存在组件标签参数，则激活名称为组件标签名称，否则为当前路由名称
      if (componentName) {
        const router = this.cacheRouterList.find((row) => !!row.meta && row.meta.componentName === componentName);
        this.setActiveRouterName(router.name);
      } else {
        this.setActiveRouterName(this.$route.name);
      }
    },
    recursiveTreeByLastLevel(name) {
      // debugger
      const route = this.routerList.find((row) => row.name === name);
      if (!route) return;
      if (route.parentId) {
        const parentRoute = this.routerList.find((row) => row.id === route.parentId);
        this.recursiveTreeByLastLevel(parentRoute.name);
      } else {
        const menu = this.routerTreeList.find((row) => {
          return row.name === route.name;
        });
        this.setLeftMenuList(menu.children || []);
      }
    },
    getNavLeftMenu() {
      this.setLeftMenuList([]);
      this.recursiveTreeByLastLevel(this.$route.name);
    },
    hasClosed(index) {
      if (index === 0) {
        return this.cacheRouterList.length === 1 ? false : true;
      } else {
        return true;
      }
    },
  },
  watch: {
    $route() {
      this.getTabsActive();
      this.getNavLeftMenu();
    },
    cacheRouterList() {
      this.getTabsActive();
    },
    notifyConfig: {
      handler(val) {
        if (val && val.message) {
          this.$_openDownloadTip(this.notifyConfig);
        }
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      getNavConfigType: 'common/getNavConfigType',
      routerTreeList: 'permission/getRouterTreeList',
      routerList: 'permission/getRouterList',
      leftMenuList: 'permission/getLeftMenuList',
      cacheRouterList: 'tabs/getCacheRouterList',
      activeRouterName: 'tabs/getActiveRouterName',
      isCollapsed: 'common/getIsCollapsed',
      websocketMsg: 'websocket/getWebsocketMsg',
      notifyConfig: 'websocket/getNotifyConfig',
      isPortalSkip: 'common/getIsPortalSkip',
    }),
    menuitemClasses() {
      return ['menu-item', 'menu', this.isCollapsed ? 'collapsed-menu' : ''];
    },
    // 主页激活状态
    homeActive() {
      return this.$route.name === 'home';
    },
    rotateIcon() {
      return ['icon-font', this.hasCollapse ? 'icon-indent' : 'icon-outdent'];
    },
    isHome() {
      return this.$route.path === '/home';
    },
    isShowSider() {
      // isExpandShow - 点击收起是直接隐藏还是展示图标
      let isShowSider =
        !!this.leftMenuList.length && (this.hasCollapse || LayoutComponentsEnum[this.getNavConfigType].isExpandShow);
      return isShowSider;
    },
    siderStyle() {
      // 如果点击收起 - 展示图标不隐藏
      if (LayoutComponentsEnum[this.getNavConfigType].isExpandShow && !this.hasCollapse) {
        // 动态控制sider宽度
        return LayoutComponentsEnum[this.getNavConfigType].iconStatuStyle;
      }
      return LayoutComponentsEnum[this.getNavConfigType].style;
    },
  },
  components: {
    NavLeft: require('@/components/nav-left/index.vue').default,
    UiTabs: require('@/components/ui-tabs/ui-tabs.vue').default,
    UiTabPane: require('@/components/ui-tabs/ui-tab-pane.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .ivu-layout-sider {
    background-color: #484847;
  }
}
.p0 {
  padding: 0;
}
.ivu-layout-sider {
  background-color: #011b37;
  width: 240px !important;
  min-width: 240px !important;
  max-width: 240px !important;
  flex: 0 0 240px !important;
}
.layout {
  background: #f5f7f9;
  position: relative;
  overflow: hidden;
  height: calc(~'100% - 53px');
  clear: both;
  .menu-icon {
    width: 50px;
    height: 25px;
    line-height: 25px;
    padding-left: 10px;
    position: absolute;
    left: 0;
    bottom: 10px;
    background: linear-gradient(270deg, rgba(43, 132, 226, 0) 0%, #2b84e2 100%);
    cursor: pointer;
    z-index: 9999;
    i {
      color: rgba(255, 255, 255, 0.65);
      font-size: 18px;
      //height: 20px;
      //line-height: 20px;
      &:hover {
        color: #fff;
      }
    }
  }
  .layout-self {
    height: 100%;

    @{_deep}.ivu-layout-sider-children {
      margin-top: 0;
    }
  }
  .layout-header-bar {
    padding: 0;
    background: var(--bg-layout-header-tab);
    height: 36px;
    box-shadow: var(--shadow-layout-header-tab);
    position: relative;
    .option {
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #768192;
      &:hover {
        background-color: #0d3560;
        color: #fff;
      }
      &.active {
        background-color: #0d3560;
        color: #fff;
      }
      &.go-home {
        position: absolute;
        left: 36px;
        width: 41px;
        border-right: 1px solid;
        border-color: var(--color-tab-icon);
        z-index: 1;
      }
    }
    .right-menu {
      li {
        padding: 7px 16px;
        line-height: 20px;
        color: var(--color-select-item);
        &:hover {
          background-color: var(--bg-tab-pane-active);
        }
      }
    }
    .router-list {
      height: 36px;
      line-height: 36px;
    }
  }
  .layout-content {
    background-color: var(--bg-layout-content);
    height: calc(100% - 36px);
  }
}
</style>
