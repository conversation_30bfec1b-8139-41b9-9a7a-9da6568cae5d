<!--
    * @FileDescription: 警情，案件列表组件
    * @Author: H
    * @Date: 2024/04/10
 * @LastEditors: zhengmingming zhengmingming
 * @LastEditTime: 2024-05-13 10:36:30
 -->
<template>
  <li class="details-box">
    <div class="details-box-top">
      <div class="box-top-left">
        <div class="case_tag case_ellipsis" v-show="rowObj.ajlb">
          {{ rowObj.ajlb | commonFiltering(caseTypeList) }}
        </div>
        <div class="case_tag case_ellipsis" v-show="rowObj.jjlx">
          {{ rowObj.jjlx | commonFiltering(partiTypeList) }}
        </div>
        <div class="case_coding">
          【
          <p class="case_ellipsis" :title="rowObj.ajbh">
            {{ rowObj.ajbh || rowObj.jjbh }}
          </p>
          】
        </div>
        <p class="case_title ellipsis">{{ rowObj.barxm || rowObj.bjr }}</p>
      </div>
      <i
        class="iconfont color-bule icon-shanchu"
        v-show="showDel"
        @click="handleDele"
      ></i>
    </div>
    <div class="details-box-bottom">
      <div>
        <ui-icon type="time" :size="14"></ui-icon>
        <span>{{ rowObj.fxsj || rowObj.bjdhsj }}</span>
      </div>
      <div class="box-bottom-site">
        <ui-icon type="location" :size="14"></ui-icon>
        <p class="address case_ellipsis" :title="rowObj.ajdzmc || rowObj.sfdd">
          {{ rowObj.ajdzmc || rowObj.sfdd }}
        </p>
      </div>
    </div>
  </li>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  name: "",
  components: {},
  props: {
    rowObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
    showDel: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    ...mapGetters({
      caseTypeList: "dictionary/getCaseTypeList", //案件类型
      partiTypeList: "dictionary/getPartiTypeList", // 警情类型
    }),
  },
  methods: {
    handleDele() {
      this.$emit("deleList");
    },
  },
};
</script>

<style lang='less' scoped>
.details-box {
  background: #f9f9f9;
  padding: 8px 10px;
  margin-top: 10px;

  .details-box-top {
    display: flex;
    justify-content: space-between;

    .box-top-left {
      display: flex;
      overflow: hidden;
      .case_tag {
        flex: 1;
        font-weight: 400;
        font-size: 12px;
        color: #2c86f8;
        padding: 0px 6px;
        height: 20px;
        background: rgba(44, 134, 248, 0.1028);
        border-radius: 4px 4px 4px 4px;
        border: 1px solid #2c86f8;
      }

      .case_coding {
        display: flex;

        p {
          max-width: 60px;
          color: #2c86f8;
        }
      }

      .case_title {
        width: 120px;
      }
    }

    .color-bule {
      color: #2b84e2;
      cursor: pointer;
    }
  }

  .details-box-bottom {
    display: flex;
    margin-top: 6px;

    .box-bottom-site {
      margin-left: 19px;
      display: flex;

      .address {
        width: 120px;
      }
    }
  }

  .case_ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
