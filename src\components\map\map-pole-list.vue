<!--
    * @FileDescription: 地图-资源图层-重叠点位列表
 -->
<template>
    <div class="dom-wrapper">
        <div class="dom">
            <div class="hover-content">
                <div class="item" :class="item.Status == '1' ? 'offline' : ''" v-for="item in polePoints" :key="item.deviceId" @click="handleClick(item)">
                    <img :src="getIcon(item)" alt="">
                    <span :title="item.deviceName">{{item.deviceName}}</span>
                </div>
            </div>
            <div class='hover-bottom'></div>
        </div>
    </div>
</template>
<script>
export default {
    components: {  },
    props: {
        polePoints: {
            type: Array,
            default: () => []
        }
    },
    watch: {
    },
    computed: {
    },

    data () {
        return {
        }
    },
    methods: {
        handleClick(item) {
            this.$emit('polePointClick', item)
        },
        getIcon(point) {
            switch (point.deviceType) {
                case '1':
                    if (point.deviceChildType == '1' || point.deviceChildType == '2') {
                        return require('@/assets/img/map/mapPoint/map-qiuji.png')
                    }else {
                        return require('@/assets/img/map/mapPoint/map-qiangji.png')
                    }
                case '11':
                    return require('@/assets/img/map/mapPoint/map-face.png')
                case '2':
                    return require('@/assets/img/map/mapPoint/map-vehicle.png')
                case '3':
                    return require('@/assets/img/map/mapPoint/map-wifi.png')
                case '5':
                    return require('@/assets/img/map/mapPoint/map-rfid.png')
                case '4':
                    return require('@/assets/img/map/mapPoint/map-electric.png')
                default:
                    return require('@/assets/img/map/mapPoint/map-qiangji.png')
                }
        }
    }
}
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
//   padding: 10px 28px 25px 0;
padding: 10px 28px 0px 0;
  height: 100%;
}
.dom {
  width: 380px;
  position: relative;
  display: flex;
    justify-content: center;
    align-items: end;
    .hover-content{
        background: #fff;
        padding: 5px;
        border-radius: 5px;
        box-shadow: 0px 3px 5px 0px rgba(147,171,206,0.7);
        display: inline-block;
        max-height: 202px;
        overflow-y: auto;
        .item {
            cursor: pointer;
            padding: 5px;
            display: flex;
            align-items: center;
            img {
                margin-right: 8px;
                height: 22px;
            }
            span {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            &.offline {
                img {
                    filter: grayscale(100%);
                }
            }
        }
    }
    .hover-bottom{
        background: #fff;
    }
    .hover-bottom:before,
    .hover-bottom:after {
        content: '';
        display: block;
        border-width: 8px;
        position: absolute;
        bottom: -16px;
        left: 146px;
        border-style: solid dashed dashed;
        border-color: #ffffff transparent transparent;
        font-size: 0;
        line-height: 0;
    }
}

// .dom:before,
// .dom:after {
//   content: '';
//   display: block;
//   border-width: 8px;
//   position: absolute;
//   bottom: -16px;
//   left: 146px;
//   border-style: solid dashed dashed;
//   border-color: #ffffff transparent transparent;
//   font-size: 0;
//   line-height: 0;
// }
</style>
