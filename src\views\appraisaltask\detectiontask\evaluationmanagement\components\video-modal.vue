<template>
  <ui-modal v-model="visible" title="查看详情" :styles="styles" class="vedioModal" footer-hide>
    <div class="right-content" v-if="this.videoData.totalDeviceNum != 0 || outcome != ''">
      <div class="times">
        <span>指标名称：{{ this.title_tab }}</span>
        <span>评测时间：{{ this.$parent.row.examineTime }}</span>
        <span>抽样设备数：{{ this.videoData.totalDeviceNum }}</span>
        <Button type="primary" class="export" @click="exportFn" :loading="exportLoading">
          <span v-if="!exportLoading" class="ent">导出</span>
          <span v-else class="ent">Loading...</span>
        </Button>
      </div>
      <div class="echarts-title">
        <i></i>
        <div class="titles">检测结果统计</div>
      </div>
      <div class="chart-box">
        <div class="chart">
          <PipEchart :deviceObj="deviceObj"></PipEchart>
        </div>
        <div class="line"></div>
        <div>
          <div class="d_flex right-height">
            <div class="equip_num">
              <div class="p">
                <i></i>
                <span class="tips" :title="this.$parent.row.indexName">
                  {{ this.$parent.row.indexName }}
                </span>
                <span>
                  {{
                    this.$parent.row.resultValue && this.$parent.row.resultValue !== 0
                      ? this.$parent.row.resultValue.toFixed(2)
                      : '0.00'
                  }}%</span
                >
                <img :title="this.$parent.row.calculateMethod" src="@/assets/img/car-modal/icon1.png" alt="" />
              </div>
              <div class="p">
                <i></i>
                <span class="img"> 达标值：{{ this.$parent.row.standardsValue }}% </span>
              </div>
            </div>
            <div class="result">
              <div class="p">
                <i></i>
                <span>考核结果：</span>
              </div>
              <img
                src="@/assets/img/car-modal/qualified1.png"
                alt=""
                v-if="deviceObj.qualified >= this.$parent.row.standardsValue"
              />
              <img src="@/assets/img/car-modal/qualified2.png" alt="" v-else />
              <loading v-if="loading"></loading>
            </div>
          </div>
          <p
            class="color_unqualified explain"
            v-if="
              title_tab === '重点实时视频通畅率' ||
              title_tab === '普通实时视频可调阅率' ||
              title_tab === '重点历史录像通畅率' ||
              title_tab === '普通历史录像可调阅率'
            "
          >
            说明：根据评测方案配置要求，{{
              resultText ? `将设备的【${resultText}】作为指标计算结果。` : '暂无设备的状态作为指标计算结果。'
            }}
          </p>
        </div>
      </div>
      <div class="echarts-title">
        <i></i>
        <div class="titles">检测数据列表</div>
      </div>
      <div class="search-wrapper">
        <ui-label :label="global.filedEnum.deviceId" :width="70" class="mb-sm margin-r">
          <Input class="input-width" :placeholder="`请输入${global.filedEnum.deviceId}`" v-model="deviceId"></Input>
        </ui-label>
        <ui-label :label="global.filedEnum.deviceName" :width="70" class="mb-sm margin-r">
          <Input class="input-width" :placeholder="`请输入${global.filedEnum.deviceName}`" v-model="deviceName"></Input>
        </ui-label>
        <template
          v-if="
            title_tab === '重点实时视频通畅率' ||
            title_tab === '普通实时视频可调阅率' ||
            title_tab === '重点历史录像通畅率' ||
            title_tab === '普通历史录像可调阅率'
          "
        >
          <ui-label label="在线状态" :width="70" class="margin-r">
            <Select
              v-model="online"
              v-if="title_tab === '重点实时视频通畅率' || title_tab === '普通实时视频可调阅率'"
              clearable
              placeholder="请选择"
              class="width-input"
            >
              <Option :value="1">在线</Option>
              <Option :value="2">离线</Option>
            </Select>
            <Select
              v-model="online"
              v-if="title_tab === '重点历史录像通畅率' || title_tab === '普通历史录像可调阅率'"
              clearable
              placeholder="请选择"
              class="width-input"
            >
              <Option :value="1">文件存在</Option>
              <Option :value="2">文件不存在</Option>
            </Select>
          </ui-label>
          <ui-label label="完好状态" :width="70" class="margin-r">
            <Select v-model="normal" clearable placeholder="请选择" class="width-input">
              <Option :value="1">取流及时响应</Option>
              <Option :value="2">取流超时响应</Option>
            </Select>
          </ui-label>
          <ui-label label="可用状态" :width="70" class="margin-r">
            <Select v-model="canPlay" clearable placeholder="请选择" class="width-input">
              <Option :value="1">取流成功</Option>
              <Option :value="2">取流失败</Option>
            </Select>
          </ui-label>
        </template>
        <ui-label label="检测结果" :width="70">
          <Select v-model="outcome" clearable placeholder="请选择" class="width-input">
            <Option :value="1">合格</Option>
            <Option :value="2">不合格</Option>
            <Option :value="3">无法考核</Option>
          </Select>
          <!-- <Input class="input-width" placeholder="请输入检查结果" v-model="outcome"></Input> -->
        </ui-label>
        <ui-label :width="30" label=" ">
          <Button type="primary" class="mr-sm" @click="search">查询</Button>
          <Button type="default" class="mr-sm" @click="reast">重置</Button>
        </ui-label>
      </div>
      <div class="table-box mt-sm">
        <ui-table
          v-if="isShow"
          :loading="loading_tab"
          class="ui-table"
          :minus-height="minusTable"
          :table-columns="tableColumns"
          :table-data="deviceListData"
        >
          <template #onlineDesc="{ row }">
            <span
              v-if="title_tab === '重点实时视频通畅率' || title_tab === '普通实时视频可调阅率'"
              :class="{
                color_qualified: row.online === '1',
                color_unqualified: row.online === '2',
              }"
            >
              {{ row.online === '1' ? '在线' : row.online === '2' ? '离线' : '' }}
            </span>
            <span
              v-if="title_tab === '重点历史录像通畅率' || title_tab === '普通历史录像可调阅率'"
              :class="{
                color_qualified: row.online === '1',
                color_unqualified: row.online === '2',
              }"
            >
              {{ row.online === '1' ? '文件存在' : row.online === '2' ? '文件不存在' : '' }}
            </span>
          </template>
          <template #normalDesc="{ row }">
            <span
              :class="{
                color_qualified: row.normal === '1',
                color_unqualified: row.normal === '2',
              }"
            >
              {{ row.normal === '1' ? '取流及时响应' : row.normal === '2' ? '取流超时响应' : '' }}
            </span>
          </template>
          <template #canDesc="{ row }">
            <span
              :class="{
                color_qualified: row.canPlay === '1',
                color_unqualified: row.canPlay === '2',
              }"
            >
              {{ row.canPlay === '1' ? '取流成功' : row.canPlay === '2' ? '取流失败' : '' }}
            </span>
          </template>
          <template #resultDesc="{ row }">
            <span
              :class="{
                color_qualified: row.description == '合格',
                color_unqualified: row.description == '不合格',
                color_cannot: row.description == '无法考核',
              }"
            >
              {{ row.description }}
            </span>
            <!-- ocr模式 时钟-->
            <!-- <Tooltip placement="bottom" v-if="row.detectionMode === 1 && row.dateImageText != null && (row.indexId == '62' || row.indexId == '24')">
              <i class="icon-font icon-wenhao ml-xs f-12"> </i>
              <div slot="content">
                <div v-for="(item, index) in JSON.parse(row.dateImageText)" :key="index">
                  <p>
                    {{ item.text }}
                  </p>
                  <p>{{ item.verticeList }}</p>
                </div>
              </div>
            </Tooltip> -->
            <!-- ocr 字幕 -->
            <Tooltip
              placement="bottom"
              transfer
              v-if="
                row.detectionMode != null &&
                (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
              "
            >
              <i class="icon-font icon-wenhao ml-xs f-12"> </i>
              <div slot="content">
                <!-- <div v-if="row.dateImageText != null">
                  <div v-for="(item, index) in JSON.parse(row.dateImageText)" :key="index">
                    <p>
                      {{ item.text }}
                    </p>
                    <p>{{ item.verticeList }}</p>
                  </div>
                </div>
                <div v-if="row.additionalImageText != null">
                  <div v-for="(item, index) in JSON.parse(row.additionalImageText)" :key="index">
                    <p>
                      {{ item.text }}
                    </p>
                    <p>{{ item.verticeList }}</p>
                  </div>
                </div>
                <div v-if="row.areaImageText != null">
                  <div v-for="(item, index) in JSON.parse(row.areaImageText)" :key="index">
                    <p>
                      {{ item.text }}
                    </p>
                    <p>{{ item.verticeList }}</p>
                  </div>
                </div> -->
                <json-viewer
                  :expand-depth="5"
                  v-if="row.dateImageText != null"
                  :value="JSON.parse(row.dateImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
                <json-viewer
                  :expand-depth="5"
                  v-if="row.additionalImageText != null"
                  :value="JSON.parse(row.additionalImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
                <json-viewer
                  :expand-depth="5"
                  v-if="row.areaImageText != null"
                  :value="JSON.parse(row.areaImageText)"
                  theme="my-awesome-json-theme"
                ></json-viewer>
              </div>
            </Tooltip>
            <!-- sdk -->
            <!-- <Tooltip
              placement="bottom"
              v-if="
                row.detectionMode === 2 &&
                  row.additionalImageText != null &&
                  (row.indexId == '62' || row.indexId == '23' || row.indexId == '63' || row.indexId == '24')
              "
            >
              <i class="icon-font icon-wenhao ml-xs f-12"> </i>
              <div slot="content">
                <json-viewer :value="JSON.parse(row.additionalImageText)" theme="my-awesome-json-theme"></json-viewer>
              </div>
            </Tooltip> -->
          </template>
          <template #delaySipMillSecond="{ row }">
            <span>
              {{ row.delaySipMillSecond == 0 ? '--' : row.delaySipMillSecond }}
            </span>
          </template>
          <template #delayStreamMillSecond="{ row }">
            <span>
              {{ row.delayStreamMillSecond == 0 ? '--' : row.delayStreamMillSecond }}
            </span>
          </template>
          <template #delayIdrMillSecond="{ row }">
            <span>
              {{ row.delayIdrMillSecond == 0 ? '--' : row.delayIdrMillSecond }}
            </span>
          </template>
          <template slot-scope="{ row }" slot="reason">
            <span style="color: red">{{ row.reason }}</span>
          </template>
          <template slot-scope="{ row }" slot="option">
            <ui-btn-tip
              v-if="
                $parent.row.indexId == 21 ||
                $parent.row.indexId == 23 ||
                $parent.row.indexId == 24 ||
                $parent.row.indexId == 62 ||
                $parent.row.indexId == 63 ||
                $parent.row.indexId == 65
              "
              icon="icon-bofangshipin"
              content="播放视频"
              @click.native="clickRow(row)"
            ></ui-btn-tip>
            <!-- 历史 -->
            <ui-btn-tip
              v-if="$parent.row.indexId == 22 || $parent.row.indexId == 64"
              icon="icon-chakanlishiluxiang"
              content="播放历史录像"
              @click.native="clickHis(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              icon="icon-chakanjietu"
              content="查看截图"
              disabled
              v-if="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
            ></ui-btn-tip>
            <ui-btn-tip v-else icon="icon-chakanjietu" content="查看截图" @click.native="showResult(row)"></ui-btn-tip>
            <!-- <span style="color:#2B84E2;cursor: pointer;" @click="testing(row)">检测</span> -->
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
      </div>
      <resultModel ref="result"></resultModel>
    </div>
    <div class="content" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onCancel"
    >
      <div style="margin-top: -15px">
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import vedio from '@/config/api/vedio-threm';
import { proxyInterfacefunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      styles: {
        width: '8rem',
      },
      resultText: '',
      minusTable: 500,
      visible: false,
      loading: false,
      loadingVideo: false,
      isLive: true,
      loading_tab: false,
      videoData: {},
      deviceObj: {},
      tableColumns: [],
      deviceListData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      },
      deviceId: '',
      deviceName: '',
      outcome: '',
      online: '',
      normal: '',
      canPlay: '',
      isShow: true,
      exportLoading: false,
      title_tab: '',
      //播放组件参数
      videoStyles: {
        width: '5rem',
      },
      videoUrl: '',
      videoVisible: false,
      playDeviceCode: '',
    };
  },

  methods: {
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.params.pageNumber = val;
      this.getListPage(
        this.deviceId,
        this.deviceName,
        this.outcome,
        this.online,
        this.normal,
        this.canPlay,
        this.$parent.row.indexId,
        this.$parent.row.resultId,
      );
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getListPage(
        this.deviceId,
        this.deviceName,
        this.outcome,
        this.online,
        this.normal,
        this.canPlay,
        this.$parent.row.indexId,
        this.$parent.row.resultId,
      );
    },
    // 获取设备详情数
    getVedio(indexId, resultId) {
      let data = {};
      data.indexId = indexId;
      data.resultId = resultId;
      // 级联清单特殊处理(需拦截接口替换)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.deviceResultOverview;
        proxyInterfacefunc(data, interfaceName, cascadeId, superiorToken).then((funcData) => {
          this.deviceObj = funcData;
          this.videoData = { ...funcData };
          this.videoData.dataPassRate = this.videoData.dataPassRate ? this.videoData.dataPassRate.toFixed(2) : 0;
          if (this.deviceObj.total == 0) {
            this.deviceObj.qualified = 0;
          } else {
            this.deviceObj.qualified = (this.deviceObj.qualifiedNum / this.deviceObj.total).toFixed(2) * 100;
          }
        });
        return;
      }
      // 正常处理
      this.$http.post(governanceevaluation.deviceResultOverview, data).then((res) => {
        if (res.data.code == 200) {
          this.deviceObj = res.data.data;
          this.videoData = { ...res.data.data };
          this.videoData.dataPassRate = this.videoData.dataPassRate ? this.videoData.dataPassRate.toFixed(2) : 0;
          if (this.deviceObj.total == 0) {
            this.deviceObj.qualified = 0;
          } else {
            this.deviceObj.qualified = (this.deviceObj.qualifiedNum / this.deviceObj.total).toFixed(2) * 100;
          }
        }
      });
    },
    handleGetTh(data) {
      var a = data.map((item) => {
        return { title: item.fieldName, key: item.field, minWidth: 50 };
      });
      // var data = res.data.data ? res.data.data : []
      if (!data) {
        data = [];
      }
      if (data.length > 0) {
        var online = data[0].online ? '(' + data[0].online + ')' : '';
        var normal = data[0].normal ? '(' + data[0].normal + ')' : '';
        var canPlay = data[0].canPlay ? '(' + data[0].canPlay + ')' : '';
        this.resultText =
          data[0].indexDetectionMode === 1
            ? '在线状态'
            : data[0].indexDetectionMode === 2
            ? '完好状态'
            : data[0].indexDetectionMode === 3
            ? '可用状态'
            : '';
        var result =
          data[0].indexDetectionMode === 1
            ? online
            : data[0].indexDetectionMode === 2
            ? normal
            : data[0].indexDetectionMode === 3
            ? canPlay
            : '';
      }
      //循环列表
      a.forEach((item) => {
        if (this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率') {
          item.width = 160;
        }
      });
      a.forEach((item) => {
        if (item.title === '设备编码') {
          item.width = 180;
        }
      });
      a.forEach((item) => {
        if (item.title == '在线状态') {
          item.renderHeader = (h) => {
            return h('div', [
              h('span', `在线状态${online}`),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'baseline',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                      ? '设备在国标平台为在线状态'
                      : '录像文件存在',
                  ),
                ],
              ),
            ]);
          };
          item.slot = 'onlineDesc';
          item.width = 160;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title == '完好状态') {
          item.renderHeader = (h) => {
            return h('div', [
              h('span', `完好状态${normal}`),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'baseline',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                      ? '设备在线，拉流请求有响应'
                      : '录像文件存在，拉流请求有响应',
                  ),
                ],
              ),
            ]);
          };
          item.slot = 'normalDesc';
          item.width = 160;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title == '可用状态') {
          item.renderHeader = (h) => {
            return h('div', [
              h('span', `可用状态${canPlay}`),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'bottom',
                  },
                  style: { verticalAlign: 'middle' },
                },
                [
                  h('i', {
                    class: 'icon-font icon-wenhao ml-xs f-12',
                    style: {
                      width: '13px',
                      verticalAlign: 'baseline',
                      color: 'var(--color-warning)',
                    },
                  }),
                  h(
                    'span',
                    {
                      slot: 'content',
                    },
                    this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                      ? '成功接收到实时视频流'
                      : '成功接收到历史视频流',
                  ),
                ],
              ),
            ]);
          };
          item.slot = 'canDesc';
          item.width = 160;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title == '检测结果') {
          if (
            this.title_tab === '重点实时视频可调阅率' ||
            this.title_tab === '普通实时视频可调阅率' ||
            this.title_tab === '重点历史录像可调阅率' ||
            this.title_tab === '普通历史录像可调阅率'
          ) {
            item.renderHeader = (h) => {
              return h('div', [
                h('span', `检测结果${result}`),
                h(
                  'Tooltip',
                  {
                    props: {
                      transfer: true,
                      placement: 'bottom',
                      maxWidth: 200,
                    },
                    style: { verticalAlign: 'middle' },
                  },
                  [
                    h('i', {
                      class: 'icon-font icon-wenhao ml-xs f-12',
                      style: {
                        width: '13px',
                        verticalAlign: 'baseline',
                        color: 'var(--color-warning)',
                      },
                    }),
                    h(
                      'span',
                      {
                        slot: 'content',
                      },
                      this.resultText
                        ? `设备的【${this.resultText}】作为指标计算结果`
                        : '暂无设备的状态作为指标计算结果',
                    ),
                  ],
                ),
              ]);
            };
            item.slot = 'resultDesc';
            item.width = 160;
          } else {
            item.slot = 'resultDesc';
          }
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title === '信令时延(毫秒)') {
          item.slot = 'delaySipMillSecond';
          item.width = 130;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title === '视频流时延(毫秒)') {
          item.slot = 'delayStreamMillSecond';
          item.width = 130;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title === '关键帧时延(毫秒)') {
          item.slot = 'delayIdrMillSecond';
          item.width = 130;
          delete item.key;
        }
      });
      a.forEach((item) => {
        if (item.title == '不合格原因') {
          item.slot = 'reason';
          delete item.key;
        }
        if (item.title == '设备编码') {
          item.width = 200;
        }
      });
      a.push({
        title: '操作',
        width: '100',
        fixed: 'right',
        align: 'center',
        slot: 'option',
      });
      this.isShow = false;
      this.$nextTick(() => {
        this.isShow = true;
      });
      this.tableColumns = a;
    },
    // 获取表头
    getTh(type, indexId, resultId) {
      // 级联清单特殊处理(需拦截接口替换)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.getTableHeader;
        proxyInterfacefunc(
          { type: type, indexId: indexId, resultId: resultId },
          interfaceName,
          cascadeId,
          superiorToken,
          'get',
        ).then((funcData) => {
          this.handleGetTh(funcData);
        });
        return;
      }
      this.$http
        .get(governanceevaluation.getTableHeader, {
          params: { type: type, indexId: indexId, resultId: resultId },
        })
        .then((res) => {
          if (res.data.code == 200) {
            var a = res.data.data.map((item) => {
              return { title: item.fieldName, key: item.field };
            });
            var data = res.data.data ? res.data.data : [];
            if (data.length > 0) {
              var online = data[0].online ? '(' + data[0].online + ')' : '';
              var normal = data[0].normal ? '(' + data[0].normal + ')' : '';
              var canPlay = data[0].canPlay ? '(' + data[0].canPlay + ')' : '';
              this.resultText =
                data[0].indexDetectionMode === 1
                  ? '在线状态'
                  : data[0].indexDetectionMode === 2
                  ? '完好状态'
                  : data[0].indexDetectionMode === 3
                  ? '可用状态'
                  : '';
              var result =
                data[0].indexDetectionMode === 1
                  ? online
                  : data[0].indexDetectionMode === 2
                  ? normal
                  : data[0].indexDetectionMode === 3
                  ? canPlay
                  : '';
            }
            //循环列表
            a.forEach((item) => {
              if (this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率') {
                item.width = 160;
              }
            });
            a.forEach((item) => {
              if (item.title === '设备编码') {
                item.width = 180;
              }
            });
            a.forEach((item) => {
              if (item.title == '在线状态') {
                item.renderHeader = (h) => {
                  return h('div', [
                    h('span', `在线状态${online}`),
                    h(
                      'Tooltip',
                      {
                        props: {
                          transfer: true,
                          placement: 'bottom',
                        },
                        style: { verticalAlign: 'middle' },
                      },
                      [
                        h('i', {
                          class: 'icon-font icon-wenhao ml-xs f-12',
                          style: {
                            width: '13px',
                            verticalAlign: 'baseline',
                            color: 'var(--color-warning)',
                          },
                        }),
                        h(
                          'span',
                          {
                            slot: 'content',
                          },
                          this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                            ? '设备在国标平台为在线状态'
                            : '录像文件存在',
                        ),
                      ],
                    ),
                  ]);
                };
                item.slot = 'onlineDesc';
                item.width = 160;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title == '完好状态') {
                item.renderHeader = (h) => {
                  return h('div', [
                    h('span', `完好状态${normal}`),
                    h(
                      'Tooltip',
                      {
                        props: {
                          transfer: true,
                          placement: 'bottom',
                        },
                        style: { verticalAlign: 'middle' },
                      },
                      [
                        h('i', {
                          class: 'icon-font icon-wenhao ml-xs f-12',
                          style: {
                            width: '13px',
                            verticalAlign: 'baseline',
                            color: 'var(--color-warning)',
                          },
                        }),
                        h(
                          'span',
                          {
                            slot: 'content',
                          },
                          this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                            ? '设备在线，拉流请求有响应'
                            : '录像文件存在，拉流请求有响应',
                        ),
                      ],
                    ),
                  ]);
                };
                item.slot = 'normalDesc';
                item.width = 160;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title == '可用状态') {
                item.renderHeader = (h) => {
                  return h('div', [
                    h('span', `可用状态${canPlay}`),
                    h(
                      'Tooltip',
                      {
                        props: {
                          transfer: true,
                          placement: 'bottom',
                        },
                        style: { verticalAlign: 'middle' },
                      },
                      [
                        h('i', {
                          class: 'icon-font icon-wenhao ml-xs f-12',
                          style: {
                            width: '13px',
                            verticalAlign: 'baseline',
                            color: 'var(--color-warning)',
                          },
                        }),
                        h(
                          'span',
                          {
                            slot: 'content',
                          },
                          this.title_tab === '重点实时视频可调阅率' || this.title_tab === '普通实时视频可调阅率'
                            ? '成功接收到实时视频流'
                            : '成功接收到历史视频流',
                        ),
                      ],
                    ),
                  ]);
                };
                item.slot = 'canDesc';
                item.width = 160;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title == '检测结果') {
                if (
                  this.title_tab === '重点实时视频可调阅率' ||
                  this.title_tab === '普通实时视频可调阅率' ||
                  this.title_tab === '重点历史录像可调阅率' ||
                  this.title_tab === '普通历史录像可调阅率'
                ) {
                  item.renderHeader = (h) => {
                    return h('div', [
                      h('span', `检测结果${result}`),
                      h(
                        'Tooltip',
                        {
                          props: {
                            transfer: true,
                            placement: 'bottom',
                            maxWidth: 200,
                          },
                          style: { verticalAlign: 'middle' },
                        },
                        [
                          h('i', {
                            class: 'icon-font icon-wenhao ml-xs f-12',
                            style: {
                              width: '13px',
                              verticalAlign: 'baseline',
                              color: 'var(--color-warning)',
                            },
                          }),
                          h(
                            'span',
                            {
                              slot: 'content',
                            },
                            this.resultText
                              ? `设备的【${this.resultText}】作为指标计算结果`
                              : '暂无设备的状态作为指标计算结果',
                          ),
                        ],
                      ),
                    ]);
                  };
                  item.slot = 'resultDesc';
                  item.width = 160;
                } else {
                  item.slot = 'resultDesc';
                }
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title === '信令时延(毫秒)') {
                item.slot = 'delaySipMillSecond';
                item.width = 130;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title === '视频流时延(毫秒)') {
                item.slot = 'delayStreamMillSecond';
                item.width = 130;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title === '关键帧时延(毫秒)') {
                item.slot = 'delayIdrMillSecond';
                item.width = 130;
                delete item.key;
              }
            });
            a.forEach((item) => {
              if (item.title == '不合格原因') {
                item.slot = 'reason';
                delete item.key;
              }
              if (item.title == '设备编码') {
                item.width = 200;
              }
            });
            a.push({
              title: '操作',
              width: '100',
              fixed: 'right',
              align: 'center',
              slot: 'option',
            });
            this.isShow = false;
            this.$nextTick(() => {
              this.isShow = true;
            });
            this.tableColumns = a;
          }
        });
    },
    // 获取列表数据
    getListPage(deviceId, deviceName, outcome, online, normal, canPlay, indexId, resultId) {
      this.loading_tab = true;
      this.searchData.deviceId = deviceId;
      this.searchData.deviceName = deviceName;
      this.searchData.outcome = outcome;
      this.searchData.online = online;
      this.searchData.normal = normal;
      this.searchData.canPlay = canPlay;
      this.searchData.indexId = indexId;
      this.searchData.resultId = resultId;
      // 级联清单特殊处理(需拦截接口替换)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.videoPageList;
        proxyInterfacefunc(this.searchData, interfaceName, cascadeId, superiorToken).then((funcData) => {
          funcData.entities.forEach((item) => {
            if (item.status == 0) {
              item.status = '离线';
            } else if (item.status == 1) {
              item.status = '在线';
            } else if (item.status == 2) {
              item.status = '无法检测';
            }
          });
          this.deviceListData = funcData.entities;
          this.pageData.totalCount = funcData.total;
          this.loading = false;
          this.loading_tab = false;
        });
        return;
      }
      this.$http.post(governanceevaluation.videoPageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          res.data.data.entities.forEach((item) => {
            if (item.status == 0) {
              item.status = '离线';
            } else if (item.status == 1) {
              item.status = '在线';
            } else if (item.status == 2) {
              item.status = '无法检测';
            }
          });
          this.deviceListData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
        this.loading = false;
        this.loading_tab = false;
      });
    },
    // 搜索
    search() {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getListPage(
        this.deviceId,
        this.deviceName,
        this.outcome,
        this.online,
        this.normal,
        this.canPlay,
        this.$parent.row.indexId,
        this.$parent.row.resultId,
      );
    },
    // 重置
    reast() {
      this.deviceId = '';
      this.deviceName = '';
      this.outcome = '';
      this.online = '';
      this.normal = '';
      this.canPlay = '';
      this.searchData = {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        totalCount: 0,
      };
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      this.getListPage(
        this.deviceId,
        this.deviceName,
        this.outcome,
        this.online,
        this.normal,
        this.canPlay,
        this.$parent.row.indexId,
        this.$parent.row.resultId,
      );
    },
    // 点击检测
    // testing(row) {
    //   let data = {};
    //   data.deviceId = row.deviceId;
    //   this.$http.post(governanceevaluation.checkDeviceStatus, data).then((res) => {
    //     if (res.data.code == 200) {
    //       console.log(res.data.data.data);
    //       this.$Message.success(res.data.msg);
    //     }
    //   });
    // },
    //视频流导出
    // 导出
    exportFn() {
      this.exportLoading = true;
      let w;
      if (this.deviceObj.qualified > this.$parent.row.standardsValue) {
        w = '达标';
      } else {
        w = '不达标';
      }
      let params = {
        resultId: this.$parent.row.resultId,
        indexName: this.$parent.row.indexName,
        indexId: this.$parent.row.indexId,
        deviceId: this.deviceId,
        deviceName: this.deviceName,
        outcome: this.outcome,
        online: this.online,
        normal: this.normal,
        canPlay: this.canPlay,
        upToStandard: w,
      };
      // 级联清单特殊替换处理接口(后端转发)
      if (this.$parent.row.cascadeId) {
        let cascadeId = this.$parent.row.cascadeId;
        let superiorToken = this.$parent.row.superiorToken;
        let interfaceName = governanceevaluation.vedioimportExcel;
        proxyInterfacefunc(params, interfaceName, cascadeId, superiorToken, 'post', {
          responseType: 'arraybuffer',
        }).then((res) => {
          this.handleExportFn(res.data);
        });
        return;
      }
      this.$http
        .post(governanceevaluation.vedioimportExcel, params, {
          responseType: 'arraybuffer',
        })
        .then((res) => {
          if (res.status == 200) {
            this.exportLoading = false;
            let a = document.createElement('a');
            let blob = new Blob([res.data], {
              type: 'application/vnd.ms-excel',
            });
            let objectUrl = URL.createObjectURL(blob);
            a.setAttribute('href', objectUrl);
            let now = new Date(),
              year = now.getFullYear(),
              mon = now.getMonth() + 1,
              day = now.getDate(),
              hours = now.getHours(),
              min = now.getMinutes(),
              sec = now.getSeconds();
            var dataStr =
              '' +
              year +
              (mon < 10 ? '0' + mon : mon) +
              (day < 10 ? '0' + day : day) +
              '-' +
              (hours < 10 ? '0' + hours : hours) +
              (min < 10 ? '0' + min : min) +
              (sec < 10 ? '0' + sec : sec);
            a.setAttribute(
              'download',
              '视频流视图数据 - ' + this.$parent.row.indexName + ' - [iVDG] - [' + dataStr + '].xls',
            );
            a.click();
          } else {
            this.exportLoading = false;
          }
        });
    },
    handleExportFn(data) {
      this.exportLoading = false;
      let a = document.createElement('a');
      let blob = new Blob([data], {
        type: 'application/vnd.ms-excel',
      });
      let objectUrl = URL.createObjectURL(blob);
      a.setAttribute('href', objectUrl);
      let now = new Date(),
        year = now.getFullYear(),
        mon = now.getMonth() + 1,
        day = now.getDate(),
        hours = now.getHours(),
        min = now.getMinutes(),
        sec = now.getSeconds();
      var dataStr =
        '' +
        year +
        (mon < 10 ? '0' + mon : mon) +
        (day < 10 ? '0' + day : day) +
        '-' +
        (hours < 10 ? '0' + hours : hours) +
        (min < 10 ? '0' + min : min) +
        (sec < 10 ? '0' + sec : sec);
      a.setAttribute(
        'download',
        '视频流视图数据 - ' + this.$parent.row.indexName + ' - [iVDG] - [' + dataStr + '].xls',
      );
      a.click();
    },
    //视频播放
    async clickRow(row) {
      try {
        // this.isLive = true
        // this.loadingVideo = true
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        // this.loadingVideo = false
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    async clickHis(row) {
      try {
        // this.isLive = false
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let obj = {};
        obj.startTime = row.playStartTime;
        obj.endTime = row.playEndTime;
        obj.deviceId = row.deviceId;
        obj.pullLevel = 3;
        obj.urlEncryption = true;
        // 级联清单特殊处理(需拦截接口替换)
        if (this.$parent.row.cascadeId) {
          let cascadeId = this.$parent.row.cascadeId;
          let superiorToken = this.$parent.row.superiorToken;
          let interfaceName = vedio.playback;
          proxyInterfacefunc(obj, interfaceName, cascadeId, superiorToken).then((funcData) => {
            this.videoUrl = funcData.hls;
          });
          return;
        }
        let res = await this.$http.post(vedio.playback, obj);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.playDeviceCode);
    },
    showResult(row) {
      this.$refs.result.showModal(row);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.title_tab = this.$parent.row.indexName;
        this.loading = true;
        this.getVedio(this.$parent.row.indexId, this.$parent.row.resultId);
        this.getTh(1, this.$parent.row.indexId, this.$parent.row.resultId);
        this.getListPage(
          this.deviceId,
          this.deviceName,
          this.outcome,
          this.online,
          this.normal,
          this.canPlay,
          this.$parent.row.indexId,
          this.$parent.row.resultId,
        );
        this.reast();
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PipEchart: require('./pip-echart.vue').default,
    resultModel: require('@/components/result-model.vue').default,
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style lang="less" scoped>
.vedioModal {
  @{_deep} .ivu-modal-wrap {
    .ivu-modal .ivu-modal-body {
      // display: flex !important;
    }
  }
  .color_qualified {
    color: var(--color-success);
  }
  .color_unqualified {
    color: var(--color-failed);
  }
  .color_cannot {
    color: var(--color-warning);
  }
  .content {
    width: 500px;
    height: 650px;
  }
  .left-content {
    width: 303px;
    background: #1b3b65;
    /deep/.el-tree {
      // margin-top: 50px;
    }
  }
  .center-content {
    width: 1px;
    background: #1b3b65;
    margin-left: 30px;
  }
  .right-content {
    // flex: 1;
    margin: 0 4px;
    // padding-top: 20px;
    background: var(--bg-content);
    .search-wrapper {
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      .input-width {
        width: 176px;
      }
    }
    .times {
      margin-bottom: 20px;
      color: #fff;
      span:first-child {
        margin-right: 30px;
      }
      span:nth-child(2) {
        margin-right: 30px;
      }
      .export {
        margin-right: 0;
        float: right;
        position: relative;
        top: -10px;
        .ent {
          margin-right: 0;
        }
      }
    }
    .echarts-title {
      width: 100%;
      display: flex;
      margin-top: -10px;
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      color: #fff;
      i {
        width: 8px;
        height: 100%;
        background: #239df9;
        margin-right: 6px;
      }
      .titles {
        width: 100%;
        padding-left: 5px;
        background-image: linear-gradient(to right, #0a4f8d, #09284d);
      }
    }
    .table-box {
      overflow: hidden;
      position: relative;
      margin-top: 10px;
      .sucess {
        color: @color-success;
      }
      .error {
        color: @color-failed;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
  .btn {
    background-color: var(--color-primary);
    border: 0;
    color: #fff;
  }
  /deep/.ivu-table-tip {
    table {
      height: 400px;
    }
  }
  //  视频流头部
  .chart-box {
    position: relative;
    width: 100%;
    display: flex;
    height: 150px;
    align-items: center;
    background-color: var(--bg-sub-content);
    margin-bottom: 20px;
    .chart {
      width: 48%;
    }
    .line {
      width: 1px;
      height: 70%;
      background-color: #0d477d;
    }
    .right-height {
      height: 110px;
      padding-top: 30px;
      padding-left: 50px;
    }
    .explain {
      padding-left: 50px;
    }
    .equip_num {
      height: 100%;
      // padding-left: 50px;
      color: #fff;
      width: 320px;
      // padding-top: 30px;
      .p {
        height: 20px;
        margin-bottom: 20px;
        i {
          width: 6px;
          height: 18px;
          background-color: var(--color-primary);
          margin-right: 20px;
        }
        img {
          margin-left: 3px;
        }
      }
    }
    .result {
      height: 100%;
      display: flex;
      color: #fff;
      // padding-top: 30px;

      .p {
        // position: relative;
        // top: 5px;
        i {
          width: 6px;
          height: 18px;
          background-color: var(--color-primary);
          margin-right: 20px;
        }
      }
      img {
        position: relative;
        top: -15px;
      }
    }
  }
  /deep/.ivu-tooltip {
    margin-right: 10px;
  }
  /deep/.ui-page {
    padding: 10px 20px 0 20px;
    height: 45px;
  }
  /deep/ .el-loading-mask {
    background-color: #000 !important;
  }
  /deep/.el-loading-spinner {
    height: 0;
    left: 0;
    top: 50%;
    background: #000;
    i {
      font-size: 30px;
    }
  }
}
.margin-r {
  margin-right: 20px;
}
.z-ind {
  z-index: 10;
  width: 200px;
}
// .video-js {
//   .vjs-play-progress:before {
//     top: -5px;
//   }
// }
// .vjs-paused,
// .vjs-fullscreen-control {
//   display: flex;
// }
/deep/ .icon-wenhao {
  color: var(--color-warning);
}
/deep/.jv-code {
  overflow: auto;
}
/deep/ .ivu-tooltip-inner {
  height: 220px;
  overflow: auto;
}
</style>
