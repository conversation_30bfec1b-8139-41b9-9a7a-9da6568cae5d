<template>
  <div class="slide-unit-tree" :class="{ shadowing: isShadow }" ref="reference" @click.stop="dropHide">
    <div class="tree-div" :style="{ width: autoWidth }" @click.stop>
      <slot></slot>
      <!-- :max-height="750" -->
      <div class="ui-search-tree">
        <ui-search-tree
          placeholder="请输入组织机构名称或组织机构编码"
          :node-key="nodeKey"
          :no-search="noSearch"
          :tree-data="treeData"
          :default-props="defaultProps"
          :current-node-key="selectKey"
          :default-keys="getDefaultOrgExpandedKeys"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </div>
      <slide-icon ref="slideIconRef" direction="left" :hidden.sync="defaultHide" :width="width" @isHidden="isHidden">
      </slide-icon>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    noShadow: {
      type: Boolean,
      default: false,
    },
    // 默认先滑动隐藏
    defaultHide: {
      type: Boolean,
      default: true,
    },
    noSearch: {
      type: Boolean,
      default: true,
    },
    // v-slide 滑动更宽的距离
    width: {
      default: 0,
    },
    // 彻底隐藏，设置宽度为0
    isDispear: {
      default: false,
    },
    nodeKey: {
      type: String,
      default: 'orgCode',
    },
    // 当前选中的节点
    selectKey: {
      type: String,
    },
    defaultProps: {
      type: Object,
      default() {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    treeData: {
      type: Array,
      default() {
        return this.defaultTreeData;
      },
    },
  },
  data() {
    return {
      isShadow: false,
      autoWidth: `${300 / 192}rem`,
    };
  },
  created() {},
  mounted() {
    this.isDispear ? (this.autoWidth = '0') : (this.autoWidth = `${300 / 192}rem`);
  },
  methods: {
    dropHide() {
      document.getElementsByClassName('hide-btn')[0].click();
    },
    selectTree(data) {
      this.$emit('selectOrgCode', data);
    },
    isHidden(flag) {
      this.isShadow = flag;
      if (this.noShadow) {
        this.isShadow = !this.noShadow;
      }
      if (!this.isDispear) return;
      if (this.isShadow) {
        this.autoWidth = `${300 / 192}rem`;
      } else {
        this.autoWidth = '0';
      }
    },
    getDeaultPermissionTree(data) {
      const findPermissionTree = data.find((item) => {
        return item.orgCode === this.getDefaultSelectedOrg.orgCode;
      });
      if (!findPermissionTree) {
        for (let i = 0; i < data.length; i++) {
          if (!!data[i].children && data[i].children.length !== 0) {
            let org = this.getDeaultPermissionTree(data[i].children);
            if (org) return org;
          }
        }
      } else {
        return findPermissionTree;
      }
    },
  },
  watch: {
    treeData: {
      handler(val) {
        if (!val) return;
        if (val.length !== 0 && !this.selectKey) {
          this.selectTree(this.getDeaultPermissionTree(this.treeData));
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      defaultTreeData: 'common/getOrganizationList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      getDefaultOrgExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getInitialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    SlideIcon: require('@/components/slide-icon.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.slide-unit-tree {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 100;
  &.shadowing {
    width: 100%;
    background: rgba(0, 0, 0, 0.5);
  }
  .tree-div {
    //width: 300px;
    padding: 10px 20px;
    background: var(--bg-content);
    height: 100%;
    position: absolute;
    left: 0px;
    .option-div {
      border: 1px solid rgba(255, 255, 255, 0.1);
      padding: 2px 10px;
    }
    .hide-btn {
      left: 300px;
    }
    .ui-search-tree {
      height: 100%;
    }
  }
}
</style>
