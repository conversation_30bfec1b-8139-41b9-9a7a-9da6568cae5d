<template>
  <div class="subordinate-chart">
    <div class="ranking-title f-14">
      <span class="icon-font icon-yuekaohepaihang-01"></span>
      <slot>
        <span>地市上报数量</span>
      </slot>
    </div>
    <div class="echarts-box">
      <draw-echarts
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="echartList.length > comprehensiveConfig.homeNum">
        <i
          class="icon-font icon-zuojiantou1 f-12"
          @click="scrollRight('propertyChart', echartList, [], comprehensiveConfig.homeNum)"
        ></i>
      </span>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
export default {
  name: 'subordinate-chart',
  mixins: [dataZoom],
  data() {
    return {
      propertyEchart: {},
      echartsLoading: false,
      echartData: [[], [], []],
      colorList: ['rgba(213, 94, 41, 1)', 'rgba(22, 174, 22, 1)'],
      lengName: [],
      echartList: [],
      xAxis: [],
      series: [],
    };
  },
  props: {},
  async mounted() {
    this.typeRing();
  },
  methods: {
    typeRing() {
      this.echartData = [
        [12, 123],
        [12, 123],
        [12, 123],
        [12, 123],
      ];
      this.lengName = ['不达标', '达标'];
      let series = this.lengName.map((row, index) => {
        return {
          name: this.lengName[index],
          data: this.echartData[index],
          barWidth: '51px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[index] },
        };
      });
      let opts = {
        xAxis: ['哈尔滨', '黑龙江', '测速', '是士大夫十分'],
        series: series,
        lengName: this.lengName,
      };
      this.propertyEchart = this.$util.doEcharts.ReviewConsequenceEcharts(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], this.comprehensiveConfig.homeNum);
      });
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.subordinate-chart {
  position: relative;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
