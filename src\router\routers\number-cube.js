/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from "@/layouts/basic-layout";
export default [
  {
    path: "/number-cube1",
    name: "number-cube1",
    component: BasicLayout,
    children: [
      {
        path: "/number-cube/info",
        name: "number-cube-info",
        tabShow: true,
        parentName: "number-cube",
        component: (resolve) =>
          require(["@/views/number-cube/info-g6.vue"], resolve),
        meta: {
          title: "图谱详情",
        },
      },
    ],
  },
];
