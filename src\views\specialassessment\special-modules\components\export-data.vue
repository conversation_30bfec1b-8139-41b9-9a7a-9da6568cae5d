<template>
  <ui-modal v-model="visible" :title="title" :styles="styles">
    <div class="export-data">
      <Form ref="modalData" :model="searchData" class="form-content" :label-width="160">
        <FormItem :label="treeTitle">
          <select-organization-tree-checkall
            v-if="visible"
            ref="SelectOrganizationTreeCheckall"
            :treeData="treeData"
            :node-key="nodeKey"
            :default-props="defaultProps"
            :default-checked-keys="defaultCheckedKeys"
            :placeholder="treePlaceholder"
            :is-select-grandchild="false"
            :is-expand-all="false"
            @getSelectTree="checkTree"
          >
          </select-organization-tree-checkall>
        </FormItem>
        <FormItem :label="`待导出${treeTitle}`" v-if="isAbnormalReasonExport">
          <RadioGroup v-model="searchData.multiSheet">
            <Radio label="false">导出设备总表</Radio>
            <Radio label="true">按异常原因导出分表</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" @click="exportAdd" :loading="exportLoading" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'export-data',
  props: {
    // 级联清单单独处理
    cascadeId: {},
    superiorToken: {},
    exportLoading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '导出设备信息',
    },
    isAbnormalReasonExport: {
      type: Boolean,
      default: false,
    }, // 是否需要按异常原因导出分表,
    treeTitle: {
      type: String,
      default: '行政区划',
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    allTagList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      styles: { width: '4rem' },
      searchData: {
        multiSheet: 'false',
        codes: [],
        orgRegionCode: null,
      },
      treeData: [],
      // defaultCheckedKeys: [],
      treeList: [],
      nodeKey: 'regionCode',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      treePlaceholder: '请选择行政区划',
    };
  },
  methods: {
    init(val) {
      this.visible = true;
      let { statisticsCode } = this.$route.query;
      if (statisticsCode === '3') {
        this.nodeKey = 'tagId';
        this.defaultProps.label = 'tagName';
        this.treePlaceholder = '请选择设备标签';
        this.searchData.orgRegionCode = null;
        this.$nextTick(() => {
          this.treeData = this.$util.common.deepCopy(this.allTagList);
        });
      } else {
        this.nodeKey = statisticsCode === '1' ? 'orgCode' : 'regionCode';
        this.defaultProps.label = statisticsCode === '1' ? 'orgName' : 'regionName';
        this.treePlaceholder = statisticsCode === '1' ? '请选择组织机构' : '请选择行政区划';
        this.searchData.orgRegionCode = statisticsCode === '1' ? 'orgCode' : 'regionCode';
        this.getOrg(val);
      }
    },
    hide() {
      this.visible = false;
      this.searchData.multiSheet = 'false';
      this.searchData.orgCodes = [];
      this.treeData = [];
    },
    async getOrg(val) {
      try {
        let res = await this.$http.get(evaluationoverview.getCurrentUserSysOrgListByTaskId, {
          params: { batchId: val },
        });
        let tempArr = this.$util.common.deepCopy(res.data.data);
        tempArr = tempArr.map((item) => {
          return item;
        });
        this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
      } catch (error) {
        console.log(error);
      }
    },
    checkTree(codes) {
      this.searchData.codes = codes;
      this.searchData.exportZip = true;
    },
    exportAdd() {
      let { statisticsCode, indexType } = this.$route.query;
      const exportListkey =
        statisticsCode === '1'
          ? 'orgCodes'
          : statisticsCode === '2'
            ? 'regionCodes'
            : statisticsCode === '3'
              ? 'tagCodes'
              : null;
      if (!this.searchData.codes.length) {
        this.$Message.error(
          `请选择${exportListkey === 'orgCodes' ? '组织机构' : exportListkey === 'regionCodes' ? '行政区划' : '设备标签'}`,
        );
        return;
      }
      const exportParams = {
        // exportZip: true,
        // multiSheet: this.searchData.multiSheet,
      };
      // 人车-目录一致率 不支持   exportZip导出
      if (!['FACE_CATALOGUE_SAME', 'VEHICLE_CATALOGUE_SAME'].includes(indexType)) {
        exportParams.exportZip = true;
        exportParams.multiSheet = this.searchData.multiSheet;
      }
      exportParams.displayType =
        statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : null;
      exportParams[exportListkey] = this.searchData.codes;
      this.$emit('handleExport', exportParams);
    },
  },
  watch: {
    defaultCheckedKeys: {
      handler(val) {
        this.searchData.codes = val;
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    SelectOrganizationTreeCheckall: require('@/api-components/select-organization-tree-checkall.vue').default,
  },
};
</script>

<style lang="less" scoped>
.export-data {
}
@{_deep} .ivu-form .ivu-form-item-label {
  color: rgba(0, 0, 0, 0.45) !important;
}
@{_deep} .ivu-modal {
  width: 520px;
  .ivu-modal-header {
    margin-bottom: 0;
  }
  &-body {
    padding: 70px 20px 140px !important;
  }
  .export-content {
    height: 420px;
    overflow: auto;
    position: relative;
  }
}
@{_deep} .select-organization-tree {
  width: 90% !important;
}
</style>
