<template>
  <div class="search card-border-color">
    <Form :inline="true">
      <div class="general-search">
        <div class="input-content">
          <div class="input-content-row">
            <FormItem label="服务名称:">
              <Input
                v-model="formData.applicationNameDesc"
                placeholder="请输入"
              ></Input>
            </FormItem>
            <FormItem label="节点名称:">
              <Select
                v-model="formData.ip2"
                placeholder="请选择"
                clearable
                @on-change="ipChange"
              >
                <Option
                  v-for="item in nodeNameList"
                  :key="item.ip"
                  :value="item.ip"
                  >{{ item.serverName }}</Option
                >
              </Select>
            </FormItem>
            <FormItem label="节点IP:">
              <Input v-model="formData.serverIp" placeholder="请输入"></Input>
            </FormItem>
            <FormItem label="端口号:">
              <Input
                v-model="formData.applicationPort"
                placeholder="请输入"
              ></Input>
            </FormItem>
            <FormItem label="运行状态:">
              <div
                class="app-state"
                :class="curIndex == index ? 'active' : ''"
                @click="stateChange(item.value, index)"
                v-for="(item, index) in stateList"
                :key="item.name"
              >
                {{ item.name }}
              </div>
            </FormItem>
          </div>
        </div>
        <div class="btn-group">
          <Button type="primary" @click="search">查询</Button>
          <Button type="default" @click="resetForm">重置</Button>
        </div>
      </div>
    </Form>
  </div>
</template>
<script>
import { selectList } from "@/api/opration-center";

export default {
  components: {},
  data() {
    return {
      formData: {
        appName: "",
        nodeName: "",
        nodeIp: "",
        hostId: "",
        applicationStatus: "",
      },
      nodeNameList: [{ value: "开发服务器121" }],
      stateList: [
        { name: "全部", value: "" },
        { name: "正常", value: "UP" },
        { name: "异常", value: "DOWN" },
        { name: "离线", value: "OFFLINE" },
        { name: "未注册", value: "UNREGISTERED" },
      ],
      curIndex: 0,
    };
  },
  mounted() {
    selectList(null).then((res) => {
      this.nodeNameList = res.data;
    });
  },
  methods: {
    search() {
      let searchForm = {
        appName: this.formData.appName,
        nodeName: this.formData.nodeName,
        nodeIp: this.formData.nodeIp,
        hostId: this.formData.hostId,
        applicationStatus: this.formData.applicationStatus,
      };
      this.$emit("searchInfo", searchForm);
    },
    resetForm() {
      this.formData = {
        appName: "",
        nodeName: "",
        nodeIp: "",
        hostId: "",
        applicationStatus: "",
      };
      this.curIndex = 0;
      this.$emit("searchInfo", this.formData);
    },
    stateChange(val, index) {
      this.formData.applicationStatus = val;
      this.curIndex = index;
    },
    ipChange(e) {
      this.formData.serverIp = e;
    },
  },
};
</script>
<style lang="less" scoped>
.app-state {
  padding: 0 10px;
  height: 24px;
  display: flex;
  align-items: center;
  margin-right: 5px;
  cursor: pointer;
}
.active {
  background-color: #2c86f8;
  color: #fff;
}
.btn-group {
  display: flex;
  align-items: flex-end;
  margin-bottom: 16px;
  justify-content: flex-end;
  flex: 1;
}
.search {
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;
  .ivu-form-inline {
    width: 100%;
  }
  .ivu-form-item {
    margin-bottom: 16px;
    margin-right: 30px;
    display: flex;
    align-items: center;
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      max-width: 90px;
    }
    /deep/ .ivu-form-item-content {
      display: flex;
    }
  }
  .general-search {
    display: flex;
    width: 100%;
    .input-content {
      width: 90%;
      display: flex;
      flex-wrap: wrap;
      .input-content-row {
        display: flex;
      }
    }
  }
}
</style>
