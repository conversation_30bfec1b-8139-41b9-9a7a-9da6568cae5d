<template>
  <div class="statistics-list auto-fill">
    <title-bar
      v-bind="$attrs"
      :index-data="indexData"
      :tab-list="tabList"
      :detection-time="detectionTime"
      :sort-data="sortData"
      @handleChangeTab="handleChangeTab"
    >
      <template #filterCriteria="{ timeOption }">
        <ui-label label="检测时间：">
          <DatePicker
            type="date"
            class="select-width"
            v-model="examineTime"
            placeholder="Select date"
            :options="timeOption"
            @on-change="changeDatePicker"
          >
          </DatePicker>
        </ui-label>
      </template>
      <!-- 不显示 【统计项】， 后端没处理 -->
      <template #filterStatistics>
        <span></span>
      </template>
    </title-bar>
    <div class="statistics-list-content auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @onSortChange="onSortChange"
      >
        <template #isOnline="{ row }">
          <span :class="[getOnlineVal(row.detail?.[0]?.customVO?.isOnline).className]">
            {{ getOnlineVal(row.detail?.[0]?.customVO?.isOnline).text }}
          </span>
        </template>
        <template #unOnlineTimeM="{ row }">
          <span class="unqualified-color underline-text pointer" @click="viewDetail(row)">
            {{ getFieldName(row, 'unOnlineTimeM') }}</span
          >
        </template>
        <template #onlineTimeM="{ row }">
          <span class="underline-text pointer" @click="viewDetail(row)"> {{ getFieldName(row, 'onlineTimeM') }}</span>
        </template>
        <template #rateFormat="{ row }">
          <span class="underline-text pointer" @click="viewDetail(row)"> {{ row.detail?.[0]?.rateFormat }}</span>
        </template>
        <template #reportDayNumOfM="{ row }">
          <span class="underline-text pointer" @click="viewDetail(row)">
            {{ getFieldName(row, 'reportDayNumOfM') }}</span
          >
        </template>
        <template #action="{ row }">
          <div class="action">
            <ui-btn-tip
              v-if="row.detail?.[0]?.customVO?.plan === 1"
              class="mr-sm"
              icon="icon-chakanxiangqing"
              content="检测详情"
              @handleClick="viewDetectionDetail(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
    </div>
    <online-details v-model="onlineDetailShow" :get-echarts="getEcharts">
      <template #header>
        <div class="detail-title">
          <Button :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i>
            <span class="ml-sm">导出</span>
          </Button>
        </div>
        <icon-statics :icon-list="iconList"> </icon-statics>
      </template>
    </online-details>
    <detection-details
      v-model="detectionDetailShow"
      :index-info="indexInfo"
      :row-data="currentRowData"
      @openDeviceDetails="openDeviceDetails"
    ></detection-details>
    <device-details
      v-model="deviceDetailShow"
      :row-data="deviceSelectedRow"
      :parent-row-data="currentRowData"
      :index-info="indexInfo"
    ></device-details>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';

import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { tableColumns, iconStaticsList } from './tableColumns.js';

export default {
  name: 'NetworkOnline',
  mixins: [dealWatch, particularMixin],
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
    detectionTime: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      examineTime: null,
      loading: false,
      tableColumns: [],
      tableData: [],
      tabList: [],
      dataDimensionEnum: 'DEVICE_ALL',
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      paramsList: {},
      currentRowData: {},
      // 离线详情
      onlineDetailShow: false,
      detailData: {},
      exportLoading: false,
      iconList: iconStaticsList,
      // 检测详情
      detectionDetailShow: false,
      deviceDetailShow: false,
      deviceSelectedRow: {},
      indexInfo: {
        statisticType: 'REGION',
        access: 'TASK_RESULT',
        indexId: '',
        batchId: '',
      },
    };
  },
  computed: {},
  created() {},
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  watch: {},
  methods: {
    getOnlineVal(val) {
      return {
        className: val === '1' ? 'font-green' : val === '2' ? 'unqualified-color' : '',
        text: val === '1' ? '在线' : val === '2' ? '离线' : '',
      };
    },
    getFieldName(row, name) {
      return row.detail?.[0]?.customVO[name];
    },
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
    getParams() {
      this.paramsList = this.$route.query;
      this.tableData = [];
      const { examineTime, statisticsCode } = this.$route.query;
      this.indexInfo.statisticType =
        statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '';
      this.examineTime = examineTime;
      this.tableColumns = tableColumns({ statisticsCode: statisticsCode });
      this.getTableList();
    },
    handleChangeTab(data) {
      this.dataDimensionEnum = data.value;
      this.getTableList();
    },
    onSortChange({ order, key }) {
      if (order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: key,
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
    },
    async getTableList() {
      try {
        this.loading = true;
        let { examineTime, dateType, indexType, batchIds, statisticsCode } = this.paramsList;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType || '2',
          dataDimensionEnum: this.dataDimensionEnum,
          nodeEnum: indexType,
          paramForm: {},
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (dateType === '2') {
          params.batchIds = batchIds ? batchIds.split(',') : [];
        }
        if (this.sortData.sortField) {
          params.paramForm = {
            indexId: this.indexData.details[0].indexId,
            ...this.sortData,
          };
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfoList, params);

        if (!data || !data.length) {
          this.tableData = [];
          return false;
        }
        // this.tabList = data[0].dataDimensionsDisplay || [];
        data.forEach((item) => {
          if (item?.detail?.[0]?.customVO) {
            item.detail[0] = { ...item.detail[0], ...item.detail[0].customVO };
          }
        });
        this.tableData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    changeDatePicker(date) {
      this.examineTime = date;
      // 需要找到 当前月份 对应的最新日期 的batchIds
      let batchIds = '';
      let arr = this.detectionTime.filter((item) => {
        return item.date.includes(date);
      });
      if (arr[0]) {
        batchIds = arr[0].options[0].batchIds.join(',');
      }
      this.$router.push({
        query: {
          ...this.paramsList,
          examineTime: this.examineTime || '',
          batchIds: batchIds,
        },
      });
    },
    // 检测详情
    viewDetectionDetail(row) {
      this.currentRowData = row;
      this.indexInfo = {
        ...this.indexInfo,
        indexId: row.detail[0].indexId,
        batchId: row.detail[0].batchId,
      };
      this.detectionDetailShow = true;
    },
    openDeviceDetails(row) {
      this.deviceDetailShow = true;
      this.deviceSelectedRow = row;
    },
    // 离线详情
    async viewDetail(row) {
      try {
        this.currentRowData = row;
        this.onlineDetailShow = true;
      } catch (err) {
        console.log(err);
      }
    },
    // 获取统计
    async getStatInfo() {
      try {
        let { civilCode, detail } = this.currentRowData;
        let data = {
          indexId: detail[0].indexId,
          batchId: detail[0].batchId,
          access: 'TASK_RESULT',
          displayType: 'REGION',
          orgRegionCode: civilCode,
        };
        let res = await this.$http.post(evaluationoverview.getStatInfo, data);
        return res.data.data || {};
      } catch (error) {
        throw Error(error);
      }
    },
    async getDetailsData() {
      try {
        let { civilCode, detail } = this.currentRowData;
        let data = {
          indexId: detail[0].indexId,
          batchId: detail[0].batchId,
          // batchId: "D#4531#0000009682", '320400'||
          access: 'TASK_RESULT',
          displayType: 'REGION',
          orgRegionCode: civilCode,
          pageNumber: 1,
          pageSize: 100,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        return (
          res.data.data || {
            total: 0,
            entities: [],
          }
        );
      } catch (error) {
        throw Error(error);
      }
    },
    async getEcharts() {
      try {
        // 获取统计
        const statInfo = await this.getStatInfo();
        // 设备模式统计
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            statInfo.qualified === '1' ? (item.tailIcon = 'icon-dabiao') : (item.tailIcon = ' icon-budabiao');
          }
          item.count = statInfo[item.fileName] || 0;
        });
        // 获取详情
        const data = await this.getDetailsData();
        this.detailData = data.entities;
        const colors = [$cssVar('--color-failed'), $cssVar('--color-success'), '#2B84E2'];
        const state = ['离线', '在线', '报备'];
        const params = {
          colors: colors,
          state: state,
          yAxis: this.detailData.map((row) => row.checkTimeD),
          categoryData: this.detailData.map((row) => {
            return {
              value: row.duration,
              rowInfo: { ...row }, // 用于处理 其他业务逻辑
              textStyle: {
                /**
                 * 如果离线时间只有一条数据
                 * 则判断state： 2报备，1在线，0离线
                 * 如果有多条记录则显示离线时间
                 *  */
                color: () => {
                  if (row.timerAxis.length === 1) {
                    if (row.timerAxis[0].state === 2) {
                      return '#2B84E2';
                    } else if (row.timerAxis[0].state === 1) {
                      return '#0E8F0E';
                    } else {
                      return '#BC3C19';
                    }
                  } else {
                    return '#BC3C19';
                  }
                },
              },
            };
          }),
          data: this.dealData(colors, state),
        };
        return params;
      } catch (err) {
        console.log(err);
      }
    },
    dealData(colors, state) {
      let timeData = [];
      this.detailData.forEach((row, index) => {
        row.timerAxis.forEach((rw) => {
          timeData.push({
            itemStyle: { color: colors[rw.state] },
            name: state[rw.state],
            /**
             * Echarts的x轴只能显示 xxxx-xx-xx xx:xx:xx
             * 这里y轴作为了日期所以年月只需要写死即可
             * 0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
             *  */
            value: [index, `2020-01-01 ${rw.t1}`, `2020-01-01 ${rw.t2}`, rw.state === 0],
          });
        });
      });
      return timeData;
    },
    async onExport() {
      try {
        this.exportLoading = true;
        let { civilCode } = this.currentRowData;
        let { batchIds } = this.paramsList;
        let params = {
          indexId: this.indexData.details[0].indexId,
          batchId: batchIds ? batchIds.split(',')[0] : '',
          displayType: 'REGION',
          orgRegionCode: civilCode,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        this.exportLoading = false;
      }
    },
  },
  components: {
    TitleBar: require('@/views/specialassessment/components/title-bar.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    OnlineDetails:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/components/online-details.vue')
        .default,
    IconStatics: require('@/components/icon-statics.vue').default,
    DetectionDetails:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/components/detection-details.vue')
        .default,
    DeviceDetails:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/PlatformResult/components/device-details.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.statistics-list-content {
  padding: 0 20px;
}
.underline-text {
  text-decoration: underline;
}
.unqualified-color {
  color: var(--color-failed);
}
</style>
