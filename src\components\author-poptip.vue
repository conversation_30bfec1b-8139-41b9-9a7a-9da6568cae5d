<template>
  <div class="author" @click="visible = true">
    <div class="author-warning">
      <img src="@/assets/img/warning_icon.png" />
    </div>
    <div class="author-container" v-show="visible">
      <div class="author-title">授权到期提醒</div>
      <div class="author-content">{{ `您的授权还有${effectiveDays}天到期` }}</div>
      <div class="author-btns">
        <Button class="mr" @click.stop="cancelHandle">取消</Button>
        <Button type="primary" @click.stop="authorHandle">去授权</Button>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    effectiveDays: {
      type: [Number, String],
      default: 0,
    },
  },
  data() {
    return {
      visible: true,
    };
  },
  methods: {
    cancelHandle() {
      this.visible = false;
    },
    authorHandle() {
      if (window.sessionStorage.getItem('authorUrl')) {
        window.location.href = `${window.sessionStorage.getItem('authorUrl')}?redirect=${encodeURIComponent(
          window.location.href,
        )}&applicationCode=00000002&type=licence`;
      } else {
        window.location.href = window.location.host + '/404';
      }
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .author .author-container {
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  }
}
.author {
  display: inline-block;
  height: 53px;
  margin-right: 10px;
  position: relative;
  .author-warning {
    padding: 10px;
    cursor: pointer;
    display: flex;
    height: 100%;
    align-items: center;
    & > img {
      width: 18px;
      height: 18px;
    }
  }
  .author-container {
    position: absolute;
    background: var(--bg-select-dropdown);
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 6px rgb(0, 0, 0, 0.2);
    z-index: 900;
    left: 50%;
    transform: translate(-50%, 0);
    color: var(--color-content);
    .author-title {
      line-height: 20px;
      text-align: left;
    }
    .author-content {
      line-height: 20px;
      text-align: center;
      margin: 20px 0;
    }
    .author-btns {
      display: flex;
      .mr {
        margin-right: 30px;
      }
    }
  }
  .author-container::after {
    content: '';
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid var(--bg-select-dropdown);
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translate(-50%, 0);
  }
}
</style>
