<template>
  <div class="choose-recheck-device">
    <div class="camera" @click="selectCamera">
      <span class="font-blue camera-text" v-if="totalCount">已选择{{ totalCount }}条设备</span>
      <span v-else>
        <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
        <span class="font-blue camera-text">请选择设备</span>
      </span>
    </div>
    <choose-device
      ref="chooseDevice"
      v-model="chooseDeviceVisible"
      :table-columns="getColums"
      :load-table-data="getTableData"
      @getDeviceIdList="getDeviceIdList"
    >
      <template #search-module>
        <search-list
          :form-item-data="formItemData"
          :search-conditions="searchData"
          @startSearch="startSearch"
          @search="startSearch"
          @reset="reset"
        >
          <template v-for="(item, index) in formItemData" :slot="item.slot">
            <slot :name="item.slot" :index="index"></slot>
          </template>
        </search-list>
      </template>
      <template v-for="(item, index) in tableColumns" :slot="item.slot" slot-scope="{ row }">
        <slot :name="item.slot" :row="row" :item="item" :index="index"></slot>
      </template>
    </choose-device>
  </div>
</template>

<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'choose-recheck-device',
  mixins: [particularMixin],
  props: {
    regionCode: {
      type: String,
      default: '',
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    nodeKey: {
      type: String,
      default: 'deviceId',
    },
    paramsList: {},
    // 搜索条件
    formItemData: {},
    //自定义筛选参数 如使用插槽 必填 否则无法重置
    searchData: {
      required: true,
    },
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      searchConditions: {},
      totalCount: 0,
      formData: {},
      ids: [],
      chooseDeviceVisible: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
    };
  },
  created() {
    // const paramsList = this.$route.query
  },
  methods: {
    selectCamera() {
      this.chooseDeviceVisible = true;
    },
    getDeviceIdList({ chooseDeviceIds, selectedDevNum, checkDeviceFlag }) {
      this.ids = chooseDeviceIds;
      this.totalCount = selectedDevNum;
      this.searchConditions = {
        ...this.searchConditions,
        checkDeviceFlag,
      };
      this.$emit('getDeviceQueryForm', {
        ids: [...this.ids],
        totalCount: this.totalCount,
        params: undefined,
        ...this.searchConditions,
      });
    },
    async getTableData(data) {
      this.pageData.pageNum = data.pageNum;
      this.pageData.pageSize = data.pageSize;
      try {
        this.tableLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: 'TASK_RESULT',
          displayType: this.paramsList.statisticType,
          customParameters: this.formData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        params.orgRegionCode = params.displayType === 'REGION' ? this.paramsList.regionCode : this.paramsList.orgCode;
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        this.pageData.totalCount = res.data.data.total || 0;
        return res.data;
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    startSearch(data) {
      if (data.errorCodes) {
        data = this.$util.common.deepCopy(data);
        let errorCodes = [];
        data.errorCodes.forEach((item) => {
          if (typeof item === 'string') {
            let itemArr = item.split(',');
            itemArr.forEach((i) => {
              errorCodes.push(i);
            });
          } else {
            errorCodes.push(item);
          }
        });
        data.errorCodes = errorCodes;
      }
      this.searchConditions = data;
      this.formData = this.searchConditions;
      this.$refs.chooseDevice.startSearch();
    },
    reset(data) {
      this.$emit('reset');
      this.searchConditions = data;
      this.formData = this.searchConditions;
      this.$refs.chooseDevice.startSearch();
    },
  },
  computed: {
    getColums() {
      let columns = this.$util.common.deepCopy(this.tableColumns);
      const obj = {
        type: 'selection',
        align: 'center',
        width: 50,
      };
      columns.unshift(obj);
      return columns.filter((item) => item.slot !== 'option');
    },
  },
  watch: {},
  components: {
    SearchList: require('./search-list').default,
    ChooseDevice: require('./choose-device.vue').default,
  },
};
</script>

<style lang="less" scoped>
.choose-recheck-device {
  .camera {
    width: 230px;
    margin: 0 auto;
    padding: 0;
    height: 34px;
    line-height: 32px;
    background: rgba(43, 132, 226, 0.1);
    border: 1px dashed var(--color-primary);

    &:hover {
      background: rgba(43, 132, 226, 0.2);
      border: 1px dashed #3c90e9;
    }

    .icon-xuanzeshexiangji {
      line-height: 30px;
    }
  }
}
</style>
