<template>
  <div class="inline select-area-tree">
    <Dropdown trigger="custom" :visible="visible" v-clickoutside="dropHide">
      <div class="ivu-select ivu-select-single t-left">
        <div class="ivu-select-selection" @click="dropShow">
          <div class="select-content">
            <span class="ivu-select-placeholder" v-if="selectedList.length <= 0">请选择行政区划</span>
            <span class="ivu-select-selected-value" v-else>
              <span v-for="(item, index) in selectedList" :key="index">
                {{ item.regionName }}
              </span>
            </span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow"></Icon>
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <div class="tree-title over-flow">
          <Checkbox class="fl" v-model="all" @on-change="checkedAll">
            <span class="table-text-content">全选</span>
          </Checkbox>
          <!-- <Button class="fr" type="primary" size="small" @click="query">
            确定
          </Button> -->
        </div>
        <el-tree
          class="tree"
          node-key="regionCode"
          ref="tree"
          :tree-style="treeStyle"
          :props="defaultProps"
          :data="treeList == [] ? treeData : treeList"
          :expand-on-click-node="false"
          :show-checkbox="true"
          :default-expanded-keys="defaultKeys"
          :default-checked-keys="defaultCheckedKeys"
          :check-strictly="checkStrictly"
          @check="check"
        >
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.select-area-tree {
  width: 100%;
  .tree-title {
    padding: 5px 10px;
    line-height: 25px;
    border-bottom: 1px solid #2e4e65;
  }
  .tree {
    // min-width: 200px;
    overflow-x: auto;
    font-size: 12px;
    max-height: 280px;
    // margin-right: 10px;
  }
  .ivu-select-selection {
    height: 40px;
    width: 100%;
    line-height: 40px;
    // background-color: #101e2b !important;
    .select-content {
      height: 40px;
      line-height: 40px;
      .ivu-select-placeholder {
        height: 40px;
        line-height: 40px;
      }
      .ivu-select-selected-value {
        height: 40px;
        line-height: 40px;
      }
    }
  }
  @{_deep}.ivu-dropdown {
    width: 100%;
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      visible: false,
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      defaultKeys: [],
      selectedList: [], //选中的区域
      all: false,
    };
  },
  created() {
    this.$store.dispatch('common/setAreaList');
  },
  mounted() {},
  methods: {
    initCheck() {
      this.checkedAll(false);
    },
    // 选择checkbox触发
    check(data, checkData) {
      this.selectedList = checkData.checkedNodes;
      this.$emit('check', this.selectedList);
    },
    dropShow() {
      this.visible = !this.visible;
    },
    dropHide() {
      this.visible = false;
    },
    query() {
      this.$emit('check', this.selectedList);
      this.dropHide();
    },
    checkedAll(val) {
      let keys = [];
      let checkData = [];
      if (val) {
        this.initialAreaList.forEach((row) => {
          if (!row.disabled) {
            keys.push(row.regionCode);
          }
        });
      }
      this.$refs.tree.setCheckedKeys(keys);
      this.selectedList = this.initialAreaList;
      checkData = this.$refs.tree.getCheckedNodes(this.initialAreaList);
      this.selectedList = checkData || [];
      this.$emit('check', this.selectedList);
      this.dropHide();
    },
    getCheckedKeys(leafOnly = false) {
      return this.$refs.tree.getCheckedKeys(leafOnly);
    },
  },
  watch: {
    treeList(val) {
      !val.length ? (this.selectedList = []) : null;
    },
    defaultCheckedKeys() {
      this.$nextTick(() => {
        let checkedNodes = this.$refs.tree.getCheckedNodes();
        this.selectedList = checkedNodes;
      });
    },
    selectedList() {
      // 比较选中的第一级节点是否为全部
      let checkedTree = this.getCheckedKeys();
      let obj = {};
      checkedTree.forEach((row) => {
        obj[row] = row;
      });
      this.all = true;
      this.initialAreaList.forEach((row) => {
        if (!row.disabled && !obj[row.regionCode]) {
          this.all = false;
        }
        if (!obj[row.regionCode]) {
          this.all = false;
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getAreaList',
      initialAreaList: 'common/getInitialAreaList',
    }),
  },
  props: {
    // 树结构style
    treeStyle: {},
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 true
    checkStrictly: {
      default: false,
      type: Boolean,
    },
    // 默认选中的组织机构
    defaultCheckedKeys: {
      type: Array,
    },
    treeList: {
      type: Array,
    },
  },
  components: {},
};
</script>
