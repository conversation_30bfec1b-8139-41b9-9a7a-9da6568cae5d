<template>
  <div class="search-buttons flex-row mt-sm mb-sm">
    <div class="search-module">
      <ui-label class="inline mr-lg" label="任务名称">
        <Input v-model="searchData.taskName" clearable class="width-md" placeholder="请输入任务名称"></Input>
      </ui-label>
      <Button type="primary" @click="startSearch">查询</Button>
      <Button class="ml-sm" @click="clear">重置</Button>
    </div>
    <div class="btn-div flex-aic">
      <Button type="primary" @click="$emit('addData')" v-permission="{ route: $route.name, permission: 'create' }">
        <i class="icon-font icon-tianjia f-12"></i>
        <span class="vt-middle ml-sm">新建工单</span>
      </Button>
      <Button
        type="primary"
        class="ml-sm"
        @click="$emit('handleBatchOptions', 'Assign', true)"
        :loading="batchLoadingObj.Assign"
        v-permission="{ route: $route.name, permission: 'assign' }"
      >
        <i class="icon-font icon-piliangzhipai f-12"></i>
        <span class="vt-middle ml-sm">批量指派</span>
      </Button>
      <Button
        type="primary"
        class="ml-sm"
        @click="$emit('handleBatchOptions', 'batchSignTips', true)"
        :loading="batchLoadingObj.Sign"
        v-permission="{ route: $route.name, permission: 'sign' }"
      >
        <i class="icon-font icon-piliangqianshou f-12"></i>
        <span class="vt-middle ml-sm">批量签收</span>
      </Button>
      <Button
        type="primary"
        class="ml-sm"
        @click="$emit('handleBatchOptions', 'Close', true)"
        :loading="batchLoadingObj.Close"
        v-permission="{ route: $route.name, permission: 'close' }"
      >
        <i class="icon-font icon-piliangguanbi f-12"></i>
        <span class="vt-middle ml-sm">批量关闭</span>
      </Button>
      <Button
        type="primary"
        class="ml-sm"
        @click="$emit('handleBatchOptions', 'batchToReport', true)"
        :loading="batchLoadingObj.Report"
        v-permission="{ route: $route.name, permission: 'report' }"
      >
        <i class="icon-font icon-piliangbaobei f-12"></i>
        <span class="vt-middle ml-sm">批量报备</span>
      </Button>
      <Button
        type="primary"
        class="ml-sm"
        @click="$emit('handleBatchOptions', 'batchDelete', true)"
        :loading="batchLoadingObj.Delete"
        v-permission="{ route: $route.name, permission: 'delete' }"
      >
        <i class="icon-font icon-piliangshanchu f-12"></i>
        <span class="vt-middle ml-sm">批量删除</span>
      </Button>
      <Button type="primary" class="ml-sm" @click="$emit('exportExcel')" :loading="batchLoadingObj.Export">
        <i class="icon-font icon-daochu f-12"></i>
        <span class="vt-middle ml-sm">导出</span>
      </Button>
    </div>
  </div>
</template>
<script>
export default {
  name: '',
  props: {
    batchLoadingObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        taskName: '',
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    startSearch() {
      this.$emit('search', this.searchData);
    },
    clear() {
      this.resetSearchDataMx(this.searchData);
      this.$emit('clearAll', this.searchData);
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.search-buttons {
  .btn-div {
    .icon-piliangqianshou {
      color: #ffffff;
    }
  }
}
</style>
