<template>
  <div class="map-echarts" :class="getFullscreen ? 'full-screen-container' : ''">
    <div id="map-chart" style="width: 100%; height: 100%" ref="chartsRef"></div>
    <div class="map-legend">
      <p class="f-14 mb-sm">问题占比数</p>
      <ul>
        <li class="mb-xs" v-for="(item, index) in questionPercent" :key="index">
          <i class="point mr-sm" :style="{ background: item.color }"></i>
          {{ item.label }}
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
import mapMixin from '@/views/home/<USER>/map.Mixin.js';
import { options, options2 } from '@/views/home/<USER>/geo.config.js';
import home from '@/config/api/home';
export default {
  mixins: [mapMixin],
  props: {
    styleType: {
      type: String,
    },
  },
  data() {
    return {
      // 供mixin取值调用，每个地图动态配置字段
      mapStaticKey: 'regionCode',
      questionPercent: Object.freeze([
        {
          label: '≤ 20%',
          color: '#01EF77',
        },
        {
          label: ' 20%-50%',
          color: '#FFA700',
        },
        {
          label: '≥ 50%',
          color: '#C60235',
        },
      ]),
    };
  },
  methods: {
    /**
     * 供mixin调用，必有
     * 地图toolTip的统计值
     */
    async getIndexMapStatistics() {
      try {
        let { data } = await this.$http.get(home.getIndexMapStatistics);
        let list = data.data;
        return list;
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 供mixin调用，必有
     * 初始化地图配置
     */
    async handleMapFunction() {
      const optionEchart = this.styleType === '1' ? options : options2;
      return await optionEchart(this.geoCoordMapData, this.homePageConfig, (toolTipData, params) => {
        this.$emit('on-jump', toolTipData, params);
      });
    },
    /**
     * 供mixin调用，必有
     * 点击地图调用配置
     */
    async handleClickMapFunction() {
      const optionEchart = this.styleType === '1' ? options : options2;
      return await optionEchart(this.geoCoordMapData, this.homePageConfig);
    },
  },
};
</script>
<style lang="less" scoped>
.map-echarts {
  position: absolute;
  left: 50%;
  top: 10%;
  transform: translateX(-50%);
  width: 56.5%;
  height: 56%;
  z-index: 2;
}
.map-legend {
  position: absolute;
  right: 20px;
  bottom: 10px;
  color: #fff;
  .point {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }
}
.full-screen-container {
  top: 15%;
  width: 54%;
  .map-echarts {
    height: 53%;
  }
  .map-legend {
    right: 20px;
    bottom: 10px;
  }
}
</style>
