<template>
  <div>
    <div
      class="float-position"
      v-if="!modal"
      @click="showModel()"
      ref="floatDrag"
      :style="{
        left: left + 'px',
        top: top + 'px',
        right: right + 'px',
        zIndex: zIndex,
      }"
      @touchmove.prevent
      @mousemove.prevent
      @mousedown="mouseDown"
      @mouseup="mouseUp"
    >
      <img
        v-if="allXz"
        style="width: 200px"
        src="@/assets/img/home/<USER>"
        alt=""
        srcset=""
      />
      <img
        class="xz"
        v-show="showPng && !allXz"
        style="width: 200px"
        @mouseenter="showPng = false"
        src="@/assets/img/home/<USER>"
        alt=""
        srcset=""
      />
      <img
        class="xz"
        v-show="!showPng"
        style="width: 200px"
        @mouseleave="showPng = true"
        src="@/assets/img/home/<USER>"
        alt=""
        srcset=""
      />
    </div>

    <Modal
      class="modal"
      v-model="modal"
      draggable
      sticky
      scrollable
      :class="{ hidePadding: isInit }"
      :mask="false"
      title="智能问答助手"
      width="530"
      :styles="{ right: '20px' }"
      class-name="modal-position"
      footer-hide
    >
      <div slot="header" v-if="isInit" class="header">
        <!-- <img class="header-bg" src="@/assets/img/home/<USER>" alt="" srcset="" /> -->
      </div>
      <div v-if="isInit" class="hello">
        <div class="say">
          <img src="@/assets/img/home/<USER>" alt="" srcset="" />
          <div>(^_^)你好，我是小智！</div>
        </div>
        <p>
          作为你的智能伙伴，我既能快速查询、目标关系分析。还能进行案事件研判和嫌疑目标动态布控等。
        </p>
      </div>
      <div class="content" :class="{ maxContent: isInit }" ref="content">
        <div class="initSay">
          <div class="title">
            <img src="@/assets/img/home/<USER>" alt="" srcset="" />
            <div>你可以试着问我：</div>
          </div>
          <ul>
            <li v-for="item in initSay" @click="initData(item)">
              <div>{{ item }}</div>
            </li>
          </ul>
        </div>
        <template v-for="(item, index) in answerList">
          <div class="left" v-if="item.type == 'answer'">
            <div class="name">
              <img src="@/assets/img/home/<USER>" alt="" srcset="" />
            </div>
            <div class="co">
              <div class="time">
                {{ $dayjs().format("YYYY-MM-DD hh:mm:ss") }}
              </div>
              <div class="info">
                <div v-if="item.total" style="margin-bottom: 10px">
                  共查询到 {{ item.total }} 条信息
                  <span v-if="item.total > 3">,仅展示前3条</span>。 可查看
                  <span class="more" @click="more(item)">更多</span> 信息。
                  <div
                    v-if="item.videoBtn"
                    class="more"
                    @click="openVideo(item.data)"
                  >
                    打开摄像头
                  </div>
                </div>
                <!-- 设备列表 -->
                <div class="list" v-if="item.showType == 'list'">
                  <div
                    v-for="(ite, index2) in item.data"
                    :key="index2"
                    class="card-item"
                  >
                    <UiListCard
                      v-if="index2 < 3"
                      :type="item.cardType"
                      :data="ite"
                      :index="index2"
                      @archivesDetailHandle="archivesDetailHandle(ite)"
                    />
                  </div>
                </div>
                <div class="list" v-else-if="item.showType == 'line'">
                  <div>{{ item.title }}</div>
                  <ul>
                    <li v-for="i in item.list">
                      <div class="line" @click="initData(i)">
                        {{ i }}
                      </div>
                    </li>
                  </ul>
                </div>
                <div v-else class="cons">
                  <div>{{ item.val }}</div>
                </div>
              </div>
            </div>

            <div class="seat"></div>
          </div>
          <div class="right" v-else>
            <div class="seat"></div>
            <div class="co">
              <div class="time">
                {{ $dayjs().format("YYYY-MM-DD hh:mm:ss") }}
              </div>
              <div class="zh">
                <img
                  v-if="item.img"
                  class="cons"
                  :src="item.url"
                  alt=""
                  srcset=""
                />
                <div>{{ item.val }}</div>
              </div>
              <!-- <div class="div">
                <div class="direction" v-if="item.img">
                  <div style="flex: 1"></div>
                  <img class="cons" :src="item.url" alt="" srcset="" />
                </div>
                <span class="direction">
                  <div style="flex: 1"></div>
                  <div class="cons">{{ item.val }}</div>
                </span>
              </div> -->
            </div>
            <div class="name">
              <img
                class="user"
                src="@/assets/img/user-center/default_user_avatar.png"
                alt=""
                srcset=""
              />
            </div>
          </div>
        </template>
        <div class="load" v-if="loading">
          <div class="loading">
            <Spin fix>
              <Icon
                type="ios-loading"
                size="18"
                class="demo-spin-icon-load"
              ></Icon>
              <span style="margin-left: 10px">小智正在处理中...</span>
            </Spin>
            <!-- <Spin></Spin>
            <Spin></Spin>
            <Spin></Spin> -->
          </div>
        </div>
        <div style="height: 30px"></div>
      </div>
      <div class="line"></div>
      <div class="ask">
        <div class="upload">
          <div class="select" @click="uploadImgFn()">上传图片</div>
          <!-- <uiUploadImg v-show="showImage" class="pic" ref="uploadImg" v-model="images" size="small" :algorithmType="1" @imgUrlChange="imgUrlChange" /> -->
          <upload-img
            ref="upload"
            :multiple-num="2000"
            class="upload-img"
            @successPut="uploadLogoSuccess"
            @getFileInfo="getFileInfo"
          >
          </upload-img>
          <div class="pic" v-show="imgUrl">
            <div class="item">
              <Icon class="close" type="md-close-circle" @click="imgUrl = ''" />
              <img :src="imgUrl" alt="" srcset="" />
            </div>
          </div>
        </div>
        <!-- <Input v-model="value6" placeholder="输入问题..." search enter-button="提交" 
            @on-search="submit()"/> -->
        <Input
          class="input"
          v-model.trim="value6"
          placeholder="输入问题..."
          type="textarea"
          :rows="5"
          @on-enter="submit()"
          suffix="sfsd"
        />
        <div class="send" @click="submit()">
          <img src="@/assets/img/home/<USER>" alt="" srcset="" />
        </div>
      </div>
      <!-- 图片上传成功后，选择图片 -->
      <image-recognition
        ref="imageRecognition"
        :dataCopper="dataCopper"
        :tempUrl="tempUrl"
        @getMinImgUrl="getMinImgUrl"
        v-if="imageRecognitionVisible"
      />
      <Modal
        class="modal2"
        ref="modal2"
        @on-cancel="beforeClose()"
        v-model="displayModal"
        draggable
        sticky
        scrollable
        :mask="false"
        title="视频播放"
        width="70%"
        :styles="{ height: '80%', left: 0 }"
        footer-hide
      >
        <!-- <div class="video"> -->
        <!-- <div class="frame"> -->
        <exscreen ref="exscreen" />
        <!-- </div> -->
        <!-- </div> -->
      </Modal>
    </Modal>
  </div>
</template>
<script>
import { mapActions, mapMutations, mapGetters } from "vuex";
import { queryVehicleList } from "@/api/vehicleArchives";
import { queryDeviceInfoPageList } from "@/api/device";
import { picturePick } from "@/api/wisdom-cloud-search";
import { getPersonPageList } from "@/api/realNameFile";
import { queryDeviceRecordSearch } from "@/api/operationsOnTheMap";
import UploadImg from "@/components/ui-upload-img";
import uiUploadImg from "@/components/ui-upload-img/index";
import UiListCard from "@/components/ui-list-card";
import exscreen from "./components/exscreen";
import { answer } from "@/api/home";
import imageRecognition from "@/components/ui-upload-img/image-recognition";
// https://www.cnblogs.com/deng-jie/p/16408876.html
export default {
  components: {
    UiListCard,
    UploadImg,
    uiUploadImg,
    imageRecognition,
    exscreen,
  },
  props: {
    distanceRight: {
      type: Number,
      default: 36,
    },
    distanceBottom: {
      type: Number,
      default: 100,
    },
    isScrollHidden: {
      type: Boolean,
      default: false,
    },
    isCanDraggable: {
      type: Boolean,
      default: true,
    },
    zIndex: {
      type: Number,
      default: 50,
    },
    value: {
      type: String,
      default: "悬浮球！",
    },
  },
  data() {
    return {
      isInit: true,
      modal: false,
      allXz: true,
      isPress: false, // 是否按住
      showPng: true, // 显示png or gif
      displayModal: false,
      clientWidth: null,
      clientHeight: null,
      left: null,
      top: null,
      right: 0,
      timer: null,
      currentTop: 0,
      mousedownX: 0,
      mousedownY: 0,
      value6: "",
      answerList: [],
      images: [""], // 图片
      showImage: false,
      loading: false,
      initSay: [
        "查询云密城摄像头",
        "查询陶昊名的档案信息",
        "打开云密城摄像头",
        "查询苏AD67L8的档案",
      ],
      imgUrl: "",
      dataCopper: [],
      tempUrl: "",
      fileInfo: null,
      imageRecognitionVisible: false,
      archiveType: 1, // 1: 人脸，2：设备， 3：人员轨迹
      paramList: [], // 参数列表
      methodType: "", // 接口类型
      pageWidth: 0,
    };
  },
  computed: {},
  created() {
    this.clientWidth = document.documentElement.clientWidth;
    this.clientHeight = document.documentElement.clientHeight;
  },
  mounted() {
    this.isCanDraggable &&
      this.$nextTick(() => {
        this.floatDrag = this.$refs.floatDrag;
        // 获取元素位置属性
        this.floatDragDom = this.floatDrag.getBoundingClientRect();
        console.log("---获取元素位置属性", this.floatDragDom);
        // 设置初始位置
        // this.left = this.clientWidth - this.floatDragDom.width - this.distanceRight;
        this.right = 40;
        this.top =
          this.clientHeight - this.floatDragDom.height - this.distanceBottom;
        this.initDraggable();
      });
    // this.isScrollHidden && window.addEventListener('scroll', this.handleScroll);
    window.addEventListener("resize", this.handleResize);

    document
      .querySelector(".content")
      .addEventListener("scroll", this.scrolling);
    // this.answer()

    this.pageWidth = window.innerWidth;
  },
  beforeUnmount() {
    window.removeEventListener("scroll", this.handleScroll);
    window.removeEventListener("resize", this.handleResize);
  },
  beforeDestroy() {},
  watch: {
    modal(newV, oldV) {},
  },
  methods: {
    ...mapMutations({ setMachineInfo: "map/setMachineInfo" }),
    initData(str) {
      this.value6 = str;
    },
    answer() {
      var param = {
        prompt: "查⼀下钱的信息",
        stream: false,
        uuid: "6b8d9de4-723b-11ee-b3ec-9a08dae6f67c",
        appid: "2022",
        // "appid": "2011",
        app_type: "001",
      };
      answer(param).then((res) => {
        console.log("---123", res);
      });
    },
    submit() {
      this.isInit = false;
      if (this.value6 == "") {
        return;
      }
      this.loading = true;
      var val = JSON.parse(JSON.stringify(this.value6));
      this.value6 = "";

      //   if (this.imgUrl != '') {
      //     this.answerList.push({
      //       type: 'ask',
      //       img: true,
      //       url: this.imgUrl
      //     })
      //   }

      var ask = {
        type: "ask",
        val: val,
      };
      if (this.imgUrl != "") {
        ask.img = true;
        ask.url = this.imgUrl;
      }
      this.answerList.push(ask);

      this.scrollToBottom();

      if (this.imgUrl != "") {
        val = "根据上图" + val;
      }
      var param = {
        prompt: val,
        stream: false,
        uuid: "6b8d9de4-723b-11ee-b3ec-9a08dae6f67c",
        appid: appid,
        // appid: '2257',
        // "appid": "2011",
        app_type: "001",
      };
      this.imgUrl = "";
      answer(param).then((res) => {
        if (res.error_code == 0) {
          res.data.output.forEach(async (item) => {
            var row = JSON.parse(item.content);
            this.paramList = row.param;
            this.methodType = row.instructionType;
            if (
              row.instructionType == "query_portrait_archives" ||
              row.instructionType == "query_portrait_archives_by_pic"
            ) {
              this.archiveType = 1;
              if (row.instructionType == "query_portrait_archives") {
                // 查人的档案
                this.queryFaceList(row.instructionType, row.param);
                return;
              } else {
                await this.picturePick();
              }
            } else if (
              row.instructionType == "query_portrait_trajectory_by_pic"
            ) {
              // 查询人员轨迹
              this.archiveType = 3;
              await this.picturePick();
            } else if (row.instructionType == "query_vehicle_archives") {
              // 车辆档案
              this.queryVehicleList(row.param);
            } else if (row.instructionType == "query_vehicle_trajectory") {
              // 车辆轨迹
              this.archiveType = 4;
              await this.picturePick();
            } else if (row.instructionType == "query_device") {
              // 查询摄像头
              // 查询设备档案
              this.queryDeviceList(row.param, true);
            } else if (row.instructionType == "query_device2") {
              this.archiveType = 2;
              // 查询设备档案
              this.queryDeviceList(row.param);
            } else if (row.instructionType == "open_device") {
              // 打开摄像头
              //   this.queryDeviceList(row.param)
              this.queryDeviceList(row.param, false, true);
              this.displayModal = true;
              var answer = {
                type: "answer",
                val: "已为您打开摄像头",
              };
              this.answerList.push(answer);
              //   this.$refs.exscreen.init()
            } else if (row.instructionType == "unknown_scene") {
              var answer = {
                type: "answer",
                val: "未知场景",
              };
              this.answerList.push(answer);
            } else {
              var answer = {
                type: "answer",
                val: row.result || "很遗憾，暂未查到数据！",
              };
              this.answerList.push(answer);
            }
          });
          if (res.data.output.length == 0)
            this.answerList.push({
              type: "answer",
              val: "该问题还在学习中，换个问题吧！",
            });
          this.loading = false;
          this.scrollToBottom();
        }
        this.value6 = "";
        this.scrollToBottom();
      });
    },
    // 查询人像档案列表
    queryFaceList(type, params) {
      var param = {
        similarity: 0.8,
        labelType: "2",
        dataType: 1,
        pageNumber: 1,
        pageSize: 10,
      };

      if (params) {
        params.forEach((item) => {
          param[item.paramName] = item.paramValue;
        });
      }

      if (type == "query_portrait_archives_by_pic") {
        param.features = [this.images[0].feature];
        this.images = [];
        this.showImage = false;
      }

      getPersonPageList(param).then((res) => {
        console.log("---查询人像档案列表", res);
        if (res.data.entities.length > 0) {
          var list = res.data.entities;
          list.forEach((item) => {
            item.itemType = "people";
          });
          this.answerList.push({
            type: "answer",
            cardType: "people",
            showType: "list",
            data: list,
            total: res.data.total,
            param: params, // 接口请求参数
          });
        } else {
          this.answerList.push({
            type: "answer",
            val: "很遗憾，暂未查到数据！",
          });
        }
        this.loading = false;
        this.scrollToBottom();
      });
    },
    /**
     * 查询设备列表
     * @param {*} params 查询参数
     * @param {*} open 是否显示可打开按钮
     * @param {*} play 立即打开视频监控页面
     */
    queryDeviceList(params, open = false, play = false) {
      var param = {
        deviceType: "1",
        pageNumber: 1,
        pageSize: 10,
      };
      params.forEach((item) => {
        if (item.paramName == "deviceName") param.deviceName = item.paramValue;
      });

      queryDeviceInfoPageList(param).then((res) => {
        console.log("---设备列表查询", res);
        if (res.data.entities.length > 0) {
          var list = res.data.entities;

          list.forEach((item) => {
            item.itemType = "device";
            // var arr = item.imageUrls.split(',')
            // var arr2 = []
            // item.imageUrls.forEach(i => {
            //   arr2.push({ photoUrl: i })
            // })
            // item.imageUrls = arr2
          });
          this.answerList.push({
            type: "answer",
            cardType: "device",
            showType: "list",
            data: list,
            videoBtn: open,
            total: res.data.total,
            param: params, // 接口请求参数
          });

          if (play) {
            this.openVideo(list);
          }
        } else {
          this.answerList.push({
            type: "answer",
            val: "很遗憾，暂未查到数据！",
          });
        }
        this.loading = false;
        this.scrollToBottom();
      });
    },
    // 打开监控
    openVideo(list) {
      this.displayModal = true;
      this.$refs.exscreen.init(list);
    },
    // 查询设备列表
    queryDeviceList2(params) {
      var param = {
        deviceType: "1",
        pageNumber: 1,
        pageSize: 3,
      };
      params.forEach((item) => {
        if (item.paramName == "keyWords") param.keyWords = item.paramValue;
      });

      queryDeviceRecordSearch(param).then((res) => {
        console.log("---设备列表查询", res);
        if (res.data.entities.length > 0) {
          var list = res.data.entities;

          list.forEach((item) => {
            item.itemType = "device";
            var arr = item.imageUrls.split(",");
            var arr2 = [];
            arr.forEach((i) => {
              arr2.push({ photoUrl: i });
            });
            item.imageUrls = arr2;
          });
          this.answerList.push({
            type: "answer",
            cardType: "device",
            showType: "list",
            data: list,
            total: res.data.total,
            param: params, // 接口请求参数
          });
        } else {
          this.answerList.push({
            type: "answer",
            val: "很遗憾，暂未查到数据！",
          });
        }
        this.loading = false;
        this.scrollToBottom();
      });
    },
    uploadImgFn() {
      //   this.$refs.upload.$refs.upload.handleClick()
      this.$refs.upload.clickFn();
    },
    // 档案详情
    archivesDetailHandle(row) {
      console.log("---", row);
      if (row.itemType == "people") {
        const { href } = this.$router.resolve({
          name: "people-archive",
          query: {
            archiveNo: row.archiveNo,
            source: "people",
            initialArchiveNo: row.archiveNo,
          },
        });
        window.open(href, "_blank");
      }
      if (row.itemType == "device") {
        const { href } = this.$router.resolve({
          name: "device-archive",
          query: { archiveNo: row.deviceId },
        });
        window.open(href, "_blank");
      }
      if (row.itemType == "vehicle") {
        const { href } = this.$router.resolve({
          name: "vehicle-archive",
          query: {
            archiveNo: JSON.stringify(row.archiveNo),
            plateNo: JSON.stringify(row.plateNo),
            source: "car",
          },
        });
        window.open(href, "_blank");
      }
    },

    // 更多跳转
    more(row) {
      console.log("---more", row);
      var name = "";
      var param = {};
      if (row.param) {
        row.param.forEach((item) => {
          param[item.paramName] = item.paramValue;
        });
      }
      if (row.cardType == "people") name = "real-name-file";
      if (row.cardType == "vehicle") name = "one-vehicle-one-archives";
      if (row.cardType == "device") name = "one-plane-one-archives";
      this.$router.push({
        name: name,
        query: param,
      });
      //   const { href } = this.$router.resolve({
      //     name: name,
      //     query: { archiveNo: row.archiveNo, source: 'people', initialArchiveNo: row.archiveNo }
      //   })
      //   window.open(href, '_blank')
    },
    // 查询车辆档案
    queryVehicleList(params) {
      console.log("---查询车辆档案", params);
      var param = {
        pageNumber: 1,
        pageSize: 10,
      };
      if (params) {
        params.forEach((item) => {
          param[item.paramName] = item.paramValue;
        });
      }
      queryVehicleList(param)
        .then((res) => {
          console.log("---查询车辆档案列表", res);
          if (res.data.entities.length > 0) {
            var list = res.data.entities;
            list.forEach((item) => {
              item.itemType = "vehicle";
            });
            this.answerList.push({
              type: "answer",
              cardType: "vehicle",
              showType: "list",
              data: list,
              total: res.data.total,
              param: params, // 接口请求参数
            });
          } else {
            this.answerList.push({
              type: "answer",
              val: "很遗憾，暂未查到数据！",
            });
          }
          this.loading = false;
          this.scrollToBottom();
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },

    // 图片上传
    imgUrlChange(list) {
      //   this.images = list
      //   console.log('---this.images', this.images)
    },

    uploadLogoSuccess(arr) {
      this.imgUrl = arr[arr.length - 1];
      //   this.$refs.upload.successData = []
      this.answerList.push({
        type: "answer",
        showType: "line",
        title: "我猜你想：",
        list: [
          "查下这个人的档案",
          "查下这个人的轨迹",
          "查下这辆车的档案",
          "查下这辆车的轨迹",
        ],
      });
      this.scrollToBottom();
    },
    // 原图文件
    getFileInfo(file) {
      this.fileInfo = file;
    },
    beforeClose() {
      this.displayModal = false;
      if (this.$refs.exscreen) {
        this.$refs.exscreen.stopInspect();
      }
    },
    /**
     * 获取图片识别结果
     */
    picturePick() {
      var val = 1;
      if (this.archiveType == 4) val = 2;
      let fileData = new FormData();
      fileData.append("file", this.fileInfo);
      fileData.append("algorithmType", val);
      picturePick(fileData)
        .then((res) => {
          console.log("文件上传返回值", res);
          if (res.data.length == 0) {
            this.$Message.error("没有识别出目标，请选择其它图片");
            return;
          }

          if (res.data.length == 1) {
            this.tempUrl = window.URL.createObjectURL(this.fileInfo);
            // this.oneImageInfo(res.data)
            this.getMinImgUrl(res.data[0]);
            return;
          }
          this.imageDataHandle(res.data);
          this.tempUrl = window.URL.createObjectURL(this.fileInfo);
        })
        .finally(() => {
          this.fileUploading = null;
        });
    },
    // 获取
    getMinImgUrl(urlObj) {
      console.log("---urlObj", urlObj);
      this.imageRecognitionVisible = false;
      this.images = [urlObj];
      // 查人的档案
      if (this.archiveType == 1) {
        this.queryFaceList(this.methodType, this.paramList);
      }

      // 查询设备档案
      if (this.archiveType == 2) this.queryDeviceList(row.param);

      // 查询人员轨迹
      if (this.archiveType == 3) {
        urlObj.algorithmType = 1;
        this.setMachineInfo({ type: 1, info: urlObj });
        this.$router.push({
          name: `map-default-page`,
          params: urlObj,
        });
      }
      // 查询车辆轨迹
      if (this.archiveType == 4) {
        urlObj.algorithmType = 2;
        this.setMachineInfo({ type: 2, info: urlObj });
        this.$router.push({
          name: `map-default-page`,
          params: urlObj,
        });
      }
    },
    /**
     * 一张图片操作
     */
    async oneImageInfo(list) {
      let _that = this;
      await this.imageDataHandle(list);
      this.$nextTick(() => {
        var canvas = document.getElementById("mycanvas");
        const { left, top, right, bottom } = list[0];
        canvas.width = right - left;
        canvas.height = bottom - top;
        let ctx = canvas.getContext("2d");
        let image = new Image();
        image.crossOrigin = "anonymous"; // 网络图片跨域
        // image.src = 'https://img0.baidu.com/it/u=2484538263,336635826&fm=26&fmt=auto&gp=0.jpg'
        image.src = this.tempUrl;
        image.onload = function () {
          ctx.drawImage(image, left, top, right, bottom, 0, 0, right, bottom);
          // ctx.drawImage(image, 295, 40, 100, 100, 0 ,0, 100, 100)
          // var img = canvas.toDataURL("image/jpeg", 1); // toDataUrl可以接收2个参数，参数一：图片类型，参数二： 图片质量0-1（不传默认为0.92）
          // 将图片转成base64格式
          _that.imageSrc = canvas.toDataURL("image/png");
          var obj = {
            feature: list[0].feature,
            fileUrl: _that.imageSrc,
          };
          //   _that.$set(_that.urlList, _that.selectedUploadIndex, obj)
          _that.imgUrlChange();
        };
      });
    },
    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      if (list.length > 1) {
        this.imageRecognitionVisible = true;
      } else {
      }
      var arr = [];
      list.forEach((item) => {
        var obj = {
          x: item.left,
          y: item.top,
          width: item.right - item.left,
          height: item.bottom - item.top,
          feature: item.feature,
        };

        arr.push(obj);
      });
      this.dataCopper = arr;
    },

    //滚动条保持最底部方法
    scrollToBottom() {
      this.$nextTick(() => {
        let bott = this.$refs.content;
        bott.scrollTo({ top: bott.scrollHeight, behavior: "smooth" });
      });
    },

    scrolling() {
      let scrollTop = document.querySelector(".content").scrollTop;
      // 更新——滚动前，滚动条距文档顶部的距离
      let scrollStep = scrollTop - this.oldScrollTop;
      this.oldScrollTop = scrollTop;
      //判断当前是向上or向下滚动
      if (scrollStep < 0) {
        //向上
        console.log("正在向上滚动");
        this.scrollFlag = false;
      } else {
        console.log("正在向下滚动");
        this.scrollFlag = true;
      }
    },
    showModel() {
      // this.modal = true
    },
    /**
     * 窗口resize监听
     */
    handleResize() {
      console.log("---handleResize", window.innerWidth);
      // this.clientWidth = document.documentElement.clientWidth;
      // this.clientHeight = document.documentElement.clientHeight;
      // console.log(window.innerWidth);
      // console.log(document.documentElement.clientWidth);

      this.checkDraggablePosition();
    },
    /**
     * 初始化draggable
     *  参考： https://www.jb51.net/javascript/2907375g3.htm
     */
    initDraggable() {
      this.floatDrag.addEventListener("touchstart", this.toucheStart);
      this.floatDrag.addEventListener("touchmove", (e) => this.touchMove(e));
      this.floatDrag.addEventListener("touchend", this.touchEnd);
      this.floatDrag.addEventListener("mousemove", (e) => this.mousemove(e));
    },
    mousemove(e) {
      if (this.isPress) {
        console.log("---pageWidth", this.pageWidth);
        let num = 1810;
        if (this.pageWidth != 1920) {
          num = 1810 - (1920 - this.pageWidth);
        }

        if (this.left > num) {
          this.left = num;
          this.allXz = false;
        }

        if (this.left < num - 10) {
          this.allXz = true;
        } else {
          this.allXz = false;
          this.left = num;
        }
      }
    },
    mouseDown(e) {
      this.isPress = true;
      console.log("---mouseDown", e);
      const event = e || window.event;
      this.mousedownX = event.screenX;
      this.mousedownY = event.screenY;
      const that = this;
      let floatDragWidth = this.floatDragDom.width / 2;
      let floatDragHeight = this.floatDragDom.height / 2;
      if (event.preventDefault) {
        event.preventDefault();
      }
      this.canClick = false;
      this.floatDrag.style.transition = "none";
      document.onmousemove = function (e) {
        var event = e || window.event;
        that.left = event.clientX - floatDragWidth;
        that.top = event.clientY - floatDragHeight;
        if (that.left < 0) that.left = 0;
        if (that.top < 0) that.top = 0;
        // 鼠标移出可视区域后给按钮还原
        if (
          event.clientY < 0 ||
          event.clientY > Number(this.clientHeight) ||
          event.clientX > Number(this.clientWidth) ||
          event.clientX < 0
        ) {
          this.right = 0;
          this.top =
            this.clientHeight - this.floatDragDom.height - this.distanceBottom;
          document.onmousemove = null;
          this.floatDrag.style.transition = "all 0.3s";
          return;
        }
        if (
          that.left >=
          document.documentElement.clientWidth - floatDragWidth * 2
        ) {
          that.left = document.documentElement.clientWidth - floatDragWidth * 2;
        }
        if (that.top >= that.clientHeight - floatDragHeight * 2) {
          that.top = that.clientHeight - floatDragHeight * 2;
        }
      };
    },
    mouseUp(e) {
      console.log("---mouseUp", e);
      this.isPress = false;
      let num = 1810;
      if (this.pageWidth != 1920) {
        num = 1810 - (1920 - this.pageWidth);
      }
      let addNum = 0;
      if (this.pageWidth == 1366) {
        addNum = 30;
      }
      if (this.left > num) {
        this.left = num + addNum;
        this.allXz = false;
      } else {
        this.allXz = true;
        this.showPng = true;
      }
      const event = e || window.event;
      //判断只是单纯的点击，没有拖拽
      if (
        this.mousedownY == event.screenY &&
        this.mousedownX == event.screenX
      ) {
        this.modal = true;
        // this.$emit('handlepaly');
      }
      document.onmousemove = null;
      // this.checkDraggablePosition();
      this.floatDrag.style.transition = "all 0.3s";
    },
    handleScroll() {
      console.log("---222");
    },
    toucheStart() {
      this.canClick = false;
      this.floatDrag.style.transition = "none";
    },
    touchMove(e) {
      console.log("---1", e);
      this.canClick = true;
      if (e.targetTouches.length === 1) {
        // 单指拖动
        let touch = event.targetTouches[0];
        this.left = touch.clientX - this.floatDragDom.width / 2;
        this.top = touch.clientY - this.floatDragDom.height / 2;
      }
    },
    touchEnd() {
      if (!this.canClick) return; // 解决点击事件和touch事件冲突的问题
      this.floatDrag.style.transition = "all 0.3s";
      // this.checkDraggablePosition();
    },
    /**
     * 判断元素显示位置
     * 在窗口改变和move end时调用
     */
    checkDraggablePosition() {
      this.clientWidth = document.documentElement.clientWidth;
      this.clientHeight = document.documentElement.clientHeight;
      if (this.left + this.floatDragDom.width / 2 >= this.clientWidth / 2) {
        // 判断位置是往左往右滑动
        this.left = this.clientWidth - this.floatDragDom.width;
      } else {
        this.left = 60;
      }
      if (this.top < 20) {
        // 判断是否超出屏幕上沿
        this.top = 60;
      }
      if (this.top + this.floatDragDom.height >= this.clientHeight) {
        // 判断是否超出屏幕下沿
        this.top = this.clientHeight - this.floatDragDom.height;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.hidePadding {
  /deep/ .ivu-modal-header {
    padding: 0;
  }
}
.header-bg {
  margin-left: -16px;
  margin-top: -16px;
}
.header {
  height: 190px;
  background-image: url("../../assets/img/home/<USER>");
  background-size: 100% 100%;
  background-color: #fff;
}
.hello {
  background: linear-gradient(214deg, #2ee0fc 0%, #2c48ff 100%);
  color: #fff;
  padding: 20px;
  margin-top: -120px;
  opacity: 1;
  border: 1px solid #afecff;
  //   border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  border-radius: 10px;
  margin-bottom: 20px;
  box-shadow: 0px 2px 10px 0px rgba(44, 139, 253, 0.6);
  .say {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    img {
      margin-right: 20px;
    }
  }
}
.tubiao {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9;
  background: red;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  cursor: pointer;
}
// .modal-position {
//   /deep/ .ivu-modal {
//     width: 100% ;
//     height: 100%;
//     top: 0;
//     position: relative;
//   }
//   /deep/ .ivu-modal-content {
//     position: absolute;
//     width: 620px;
//     top: 30px;
//     right: 20px;
//   }
// }

/deep/ .ivu-modal {
  width: 100% !important;
  height: 100% !important;
  top: 0 !important;
  right: 0px !important;

  position: relative !important;
}
/deep/ .ivu-modal-content {
  position: absolute;
  width: 620px;
  top: 30px;
  right: 0px;
}

// /deep/ .ivu-modal-content {
//   position: fixed;
//   width: 620px;
//   top: 30px;
//   right: 20px !important;
// }

.modal2 {
  /deep/ .ivu-modal-content {
    left: 10px;
    overflow: hidden;
  }
  /deep/ .ivu-modal-header {
    background: linear-gradient(214deg, #2ee0fc 0%, #2c48ff 100%);
    .ivu-modal-header-inner {
      color: #fff;
    }
  }
}
.modal {
  display: flex;
  /deep/ .ivu-modal-content {
    right: 20px;
  }
  .initSay {
    .title {
      font-weight: 800;
      img {
        float: left;
        margin-right: 10px;
      }
    }
    ul {
      li {
        position: relative;
        margin-top: 16px;
        cursor: pointer;
        height: 36px;
        div {
          position: absolute;
          padding: 10px 16px;
          background: #fff;
          border-radius: 50px;
        }
      }
    }
  }

  /deep/ .ivu-modal-header {
    background: linear-gradient(214deg, #2ee0fc 0%, #2c48ff 100%);
    .ivu-modal-header-inner {
      color: #fff;
    }
  }
  /deep/ .ivu-icon-ios-close {
    color: #fff;
  }
  /deep/ .ivu-modal-body {
    background: #f5f9fc;
  }

  .content {
    flex: 1;
    height: 600px;
    // min-height: 300px;
    max-height: 600px;
    overflow: auto;
    // padding: 10px 0;
    .more {
      color: #2c86f8;
      cursor: pointer;
    }
    .left {
      display: flex;
      justify-content: flex-start;
      margin-top: 16px;
      .time {
        color: #999;
        font-size: 12px;
      }
      .co {
        .info {
          padding: 10px;
          background: #fff;
          border-radius: 10px;
        }
      }
      .con {
        // display: flex;
        div {
          padding: 10px;
          background: #fff;
          border-radius: 10px;
        }
      }
    }
    .right {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
      .co {
        text-align: right;
        .time {
          color: #999;
          font-size: 12px;
        }
        .zh {
          padding: 10px;
          border-radius: 10px;
          background: #e3f3ff;
        }
        img {
          max-width: 200px;
          max-height: 200px;
        }
      }
      .con {
        display: flex;
        justify-content: end;
        flex-direction: column;

        .div {
          display: flex;
          justify-content: end;
          flex-direction: column;
          border-radius: 10px;
        }
        .cons {
          padding: 10px;
        }
        .direction {
          display: flex;
          background: #e3f3ff;
        }

        img {
          max-width: 200px;
          max-height: 200px;
        }
      }
      .name {
        text-align: right;
        .user {
          width: 39px;
          height: 39px;
        }
      }
    }
    .name {
      width: 60px;
    }
    .con {
      flex: 1;
    }
    .list {
      flex: 1;
      //   padding-left: 22px;
      background: #fff;
      ul {
        display: flex;
        flex-direction: column;
        li {
          line-height: 30px;
          margin: 5px 0;
          .line {
            float: left;
            background: #e2f6ff;
            padding: 0 20px;
            border-radius: 50px;
            cursor: pointer;
          }
        }
      }
    }
    .seat {
      width: 60px;
    }
  }

  .maxContent {
    height: 430px;
  }
  .line {
    // border-top: 1px solid #e9e9e9;
  }
  .ask {
    position: relative;
    padding: 10px 0;
    height: 150px;
    // overflow: hidden;
    .upload {
      position: relative;
      .select {
        float: left;
        background: #2c86f8;
        border: 1px solid #2c86f8;
        padding: 0px 10px;
        border-radius: 50px;
        color: #fff;
        cursor: pointer;
      }
      .pic {
        //   width: 80px;
        width: 80%;
        height: 80px;
        // border: 1px solid #ddd;
        position: absolute;
        bottom: -30px;
        right: 0px;
        z-index: 99999;
        display: flex;
        justify-content: end;
        // background: #2c86f8;
        .close {
          color: #d57f7f;
          position: absolute;
          right: 2px;
          top: 2px;
          cursor: pointer;
          display: none;
        }
        &:hover {
          .close {
            display: block;
          }
        }
        .item {
          height: 100%;
          width: 80px;
          background: #fbfbfb;
          border: 1px solid #e9e9e9;
          display: flex;
          justify-content: center;
        }
        /deep/ .ivu-upload {
          margin-right: 0 !important;
        }
      }
    }
    .input {
      margin-top: 12px;
    }
    .send {
      position: absolute;
      background: linear-gradient(180deg, #2ee0fc 0%, #2c48ff 100%);
      bottom: 10px;
      right: 10px;
      border-radius: 50px;
      cursor: pointer;
      display: flex;
      padding: 6px 16px;
    }
  }
}

/deep/ .ivu-input {
  border: none;
}
/deep/ .ivu-input:hover {
  border: none;
}

.float-position {
  position: fixed;
  z-index: 10003 !important;
  right: 20px;
  top: 70%;
  width: 70px;
  height: 70px;
  border-radius: 50%;
  //   background: pink;
  // background: #716af2;
  // box-shadow: 0px 0px 10px 2px #a299ff;
  // border-radius: 50%;
  // overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
  // border-left: 2px dashed #716af2;
  // border-right: 2px dashed #c00;
  cursor: pointer;

  .xz {
    margin-right: -150px;
  }
  .content {
    width: 50px;
    height: 50px;
    background: #716af2;
    box-shadow: 0px 0px 10px 2px #a299ff;
    border-radius: 50%;
    position: relative;
    padding: 0.8em;
    display: flex;
    align-content: center;
    justify-content: center;
  }
  .close {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: rgba(0, 0, 0, 0.6);
    position: absolute;
    right: -10px;
    top: -12px;
    cursor: pointer;
  }
}
.cart {
  border-radius: 50%;
  width: 5em;
  height: 5em;
  display: flex;
  align-items: center;
  justify-content: center;
}
.header-notice {
  display: inline-block;
  transition: all 0.3s;
  span {
    vertical-align: initial;
  }
  .notice-badge {
    color: inherit;
    .header-notice-icon {
      font-size: 16px;
      padding: 4px;
    }
  }
}
.drag-ball .drag-content {
  overflow-wrap: break-word;
  font-size: 14px;
  color: #fff;
  letter-spacing: 2px;
}
.load {
  height: 30px;
  width: 100%;
  position: relative;
  .loading {
    display: flex;
    justify-content: center;
  }
}

.upload-img {
  display: none;
}

/deep/ .ivu-spin-fix {
  background: transparent;
}

.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.demo-spin-col {
  height: 100px;
  position: relative;
  border: 1px solid #eee;
}

.video {
  position: fixed;
  width: 80%;
  height: 80%;
  left: 10%;
  top: 10%;
  .frame {
    position: relative;
  }
}
</style>
