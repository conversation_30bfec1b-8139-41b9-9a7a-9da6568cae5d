/*
 * @Date: 2024-10-10 09:48:41
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-10-10 10:18:45
 * @FilePath: \icbd-view\src\components\gpt\enum\index.js
 */
import {
  queryFaceRecordSearch,
  queryVehicleRecordSearch,
  queryWifiRecordSearch,
  queryRfidRecordSearch,
  queryElectricCircumferenceRecordSearch,
} from "@/api/operationsOnTheMap";

import {
  queryHumanRecordSearch,
  queryNonmotorRecordSearch,
  gpsSearch,
  etcHighwayExitOrEntryVehicleTrack,
} from "@/api/wisdom-cloud-search";

// 轨迹分词
export const tajectoryTypeMap = {
  portrait: {
    api: queryFaceRecordSearch,
    searchKeyWord: "name",
    algorithmType: "1",
    menuName: "face",
    // cardHeadName:'id'
  },
  vehicle: {
    api: queryVehicleRecordSearch,
    searchKeyWord: "plateNo",
    algorithmType: "2",
    menuName: "vehicle",
  },
  // 人体
  human: {
    api: queryHumanRecordSearch,
    algorithmType: "3",
    menuName: "humanbody",
  },
  // 非机动车
  nonmotor: {
    api: queryNonmotorRecordSearch,
    algorithmType: "4",
    menuName: "nonmotorVehicle",
  },
  mac: {
    api: queryWifiRecordSearch,
    searchKeyWord: "macAddr",
    menuName: "wifi",
  },
  rfid: {
    api: queryRfidRecordSearch,
    searchKeyWord: "rfidCode",
    menuName: "rfid",
  },
  imsi: {
    api: queryElectricCircumferenceRecordSearch,
    searchKeyWord: "imsiCode",
    menuName: "electric",
  },
  gps: {
    api: gpsSearch,
    searchKeyWord: "gpsCode",
    menuName: "gps",
  },
  etc: {
    api: etcHighwayExitOrEntryVehicleTrack,
    menuName: "etc",
  },
};
// 档案分词
export const archivesTypeMap = {
  // 人员
  portrait: {
    archiveType: 1,
    api: 'queryFaceList',
  },
  // 车辆
  vehicle: {
    archiveType: 2,
    api: 'queryVehicleList',
  },
  // 场所
  place: {
    api: 'queryPlaceList',
  },
};

export const fieldMap = {
  mac: "macAddr",
  rfid: "rfidCode",
  IMSI: "imsiCode",
  gps: "gpsCode",
};

export const transformField = (fieldObject = {}) => {
  return Object.entries(fieldObject).reduce((obj, [key, value]) => {
    const newKey = fieldMap[key] || key;
    obj[newKey] = value;
    return obj;
  }, {});
};
