<template>
  <div>
    <div class="breadcrumb-container">
      <span class="box"></span>
      <span class="title">详细信息</span>
    </div>
    <div class="radioGroup-content" v-if="showTab">
      <RadioGroup v-model="relationName" type="button" button-style="solid">
        <Radio
          v-for="item in relationData"
          :label="item.relationName"
          :key="item.relationName"
          >{{ item.relationNameCn }}</Radio
        >
      </RadioGroup>
    </div>
    <div class="relationship-content-list">
      <Timeline>
        <TimelineItem
          v-for="(item, index) in selectRelationData.detailList"
          :key="index"
        >
          <div>
            <span
              v-for="headerItem in selectRelationData.groupFieldList"
              :key="headerItem.fieldName"
            >
              <span>
                <span class="header-title">{{ headerItem.fieldNameCn }}: </span>
                <!-- headerItem.color 这个颜色字段作为代码预留，暂时用不上 -->
                <span
                  class="pr20 header-value"
                  :style="{ color: headerItem.color || '#2C86F8' }"
                  >{{
                    item.groupData[headerItem.fieldName]
                      | formatterText(headerItem, propertyDicMap)
                  }}</span
                >
              </span>
            </span>
          </div>
          <div
            class="about-layout"
            v-if="selectRelationData.detailType == 'card'"
          >
            <div class="row" v-for="(itemObj, index) in item.data" :key="index">
              <div
                class="col"
                v-for="item1 in itemObj"
                :key="getItemValue(item1).id"
              >
                <div
                  class="img-content"
                  v-for="detail in selectRelationData.detailFieldList.filter(
                    (el) => el.fieldType === 'image'
                  )"
                  :key="detail.fieldName"
                >
                  <ui-image
                    :src="getItemValue(item1)[detail.fieldName]"
                    :viewerImage="getItemValue(item1).sceneImg"
                    alt="图片"
                    viewer
                    :imgStyle="{ objectFit: 'contain' }"
                    :round="false"
                  />
                </div>
                <div class="rig">
                  <p
                    v-for="detail in selectRelationData.detailFieldList.filter(
                      (el) => el.fieldType !== 'image'
                    )"
                    :key="detail.fieldName"
                    v-show-tips
                  >
                    <span class="header-title1"
                      >{{ detail.fieldNameCn }}：</span
                    >
                    <span>{{
                      getItemValue(item1)[detail.fieldName]
                        | formatterText(detail, propertyDicMap)
                    }}</span>
                  </p>
                </div>
                <div
                  class="page"
                  v-if="item1.itemData && item1.itemData.length > 1"
                >
                  <div
                    class="page-prev"
                    :class="{ disabled: item1.index === 0 }"
                    @click="onPageChange(item1, -1)"
                  >
                    <Icon type="md-arrow-dropleft" />
                  </div>
                  <div
                    class="page-next"
                    :class="{
                      disabled: item1.index === item1.itemData.length - 1,
                    }"
                    @click="onPageChange(item1, 1)"
                  >
                    <Icon type="md-arrow-dropright" />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="lsit-content-item" v-else>
            <div
              class="inlineb"
              v-for="(itemObj, index) in item.data"
              :key="index"
            >
              <div
                class="inlineb pr30"
                v-for="detail in selectRelationData.detailFieldList"
                :key="detail.fieldName"
              >
                <span class="header-title1">{{ detail.fieldNameCn }}：</span>
                <span>{{
                  itemObj[0][detail.fieldName]
                    | formatterText(detail, propertyDicMap)
                }}</span>
              </div>
            </div>
          </div>
        </TimelineItem>
      </Timeline>
    </div>
  </div>
</template>
<script>
import dayjs from "dayjs";

export default {
  components: {},
  props: {
    relationData: {
      type: Array,
      default() {
        return [];
      },
    },
    showTab: {
      type: Boolean,
    },
    propertyDicMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      relationName: "",
    };
  },
  created() {
    this.relationName = this.relationData[0]?.relationName;
  },
  computed: {
    selectRelationData() {
      return (
        this.relationData.find((el) => el.relationName === this.relationName) ||
        {}
      );
    },
  },
  filters: {
    formatterText(
      val,
      { fieldType, fieldFormat, enumList, dicTypeCode },
      propertyDicMap
    ) {
      let dicList = [];
      if (fieldType === "time") {
        return dayjs(val).format(fieldFormat);
      }
      if (enumList?.length > 0) {
        dicList = enumList;
      }
      if (dicTypeCode && propertyDicMap[dicTypeCode]) {
        dicList = propertyDicMap[dicTypeCode];
      }
      if (dicList?.length > 0)
        return dicList.find((el) => el.dataKey == val)?.dataValue || val;
      return val;
    },
  },
  methods: {
    getItemValue(item) {
      if (!item.itemData) return item;
      return item.itemData?.[item.index];
    },
    onPageChange(item, num) {
      const newIndex = item.index + num;
      if (newIndex >= 0 && newIndex < item.itemData.length)
        item.index = item.index + num;
    },
  },
};
</script>
<style lang="less" scoped>
.box {
  display: inline-block;
  width: 3px;
  height: 20px;
  background: #2b84e2;
}
.breadcrumb-container {
  padding-top: 16px;
  padding-bottom: 20px;
  .title {
    position: relative;
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    top: -3px;
    padding-left: 8px;
  }
}
.radioGroup-content {
  margin-bottom: 10px;
}
.relationship-content-list {
  max-height: 314px;
  overflow-y: auto;
  overflow-x: hidden;

  .header-title,
  .header-value {
    font-size: 14px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    font-family: MicrosoftYaHei, MicrosoftYaHei;
  }
  .lsit-content-item {
    margin-top: 10px;
    background: rgba(221, 225, 234, 0.3);
    border-radius: 4px;
    padding: 10px 10px 0 10px;
    & > div {
      padding-bottom: 10px;
    }
    .header-title1 {
      font-size: 12px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.8);
    }
  }
  // 时间轴样式
  /deep/ .ivu-timeline-item-head {
    width: 14px;
    height: 14px;
    background: #2c86f8;
    border: 3px solid #ffffff;
    left: -1px;
  }
}
.pl20 {
  padding-left: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pr6 {
  padding-right: 6px;
}
.pr30 {
  padding-right: 30px;
}
.inlineb {
  display: inline-block;
}

.about-layout {
  width: 100%;
  .row {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    .col {
      position: relative;
      box-sizing: border-box;
      padding: 10px;
      flex: 1;
      background: rgba(221, 225, 234, 0.3);
      border-radius: 4px;
      display: flex;
      align-items: center;
      margin: 0 5px;
      .img-content {
        width: 80px;
        height: 80px;
        margin-right: 10px;
      }
      .rig {
        flex: 1;
        font-family: MicrosoftYaHei;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        line-height: 20px;
        .header-title1 {
          font-weight: bold;
        }
      }
      .page {
        position: absolute;
        right: 5px;
        bottom: 5px;
        display: flex;
        .page-prev,
        .page-next {
          width: 24px;
          height: 24px;
          text-align: center;
          line-height: 24px;
          background: #f9f9f9;
          border-radius: 4px;
          border: 1px solid #d3d7de;
          margin: 0 2px;
          cursor: pointer;
          i {
            font-size: 22px;
          }
          &.disabled {
            cursor: not-allowed;
            background: #ccc;
          }
        }
      }
    }
  }
}
</style>
