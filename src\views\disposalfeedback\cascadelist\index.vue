<template>
  <div class="cascade-container auto-fill">
    <div class="cascade-list auto-fill" v-show="!componentName">
      <!-- <get-token></get-token> -->
      <!-- 省级不展示 -->
      <tagView
        v-if="!isProvincial"
        ref="tagView"
        :default-cur-tag="defaultCurTag"
        :list="tabList"
        :no-active="tabList.length === 1"
        @tagChange="tagChange"
      />
      <div v-if="staticShow">
        <section class="panel-list">
          <panel-item
            v-for="(item, index) of panelList"
            :key="index"
            :item="item"
            @click.native="changeActive(item)"
            class="mr-sm mt-md mb-sm panel-item inline"
          ></panel-item>
        </section>
        <line-title
          iconName="icon-wentixiangqingsvg-01-01"
          :title="`${!searchForm.orgName ? '' : searchForm.orgName + '-'}问题详情`"
        ></line-title>
      </div>
      <search-module class="mt-md mb-md" :is-superior="!activeTab" @reset="resetSelect" @startSearch="startSearch">
        <Button type="primary" class="fr" v-if="staticShow" @click="bulkPublish">
          <i class="icon-font icon-piliangxiafa mr-xs"></i>批量下发
        </Button>
      </search-module>
      <ui-table
        ref="table"
        reserveSelection
        class="ui-table auto-fill"
        :loading="loading"
        :table-columns="tableColumns"
        :table-data="tableData"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template #handleStatus="{ row }">
          <Tag :color="handleStatus[row.handleStatus === '3' ? '2' : row.handleStatus].color">{{
            handleStatus[row.handleStatus === '3' ? '2' : row.handleStatus].text
          }}</Tag>
        </template>
        <template #publishStatus="{ row }">
          <Tag :color="publishStatus[row.publishStatus].color">{{ publishStatus[row.publishStatus].text }}</Tag>
        </template>
        <template #resultValue="{ row }">
          <span class="font-warning">{{ row.resultValue }}</span>
        </template>
        <template #indexModule="{ row }">
          <span>{{ INDEXMODULE[row.indexModule] }}</span>
        </template>
        <template slot="action" slot-scope="{ row }">
          <create-tabs
            v-if="hasDetail(row)"
            :componentName="themData.componentName"
            :tabs-text="themData.text"
            @selectModule="selectModule"
            :tabs-query="{
              displayType: displayType,
              indexId: row.indexId,
              code: row.orgCode,
              access: 'REPORT_MODE',
              batchId: row.batchId,
              uuid: uuid,
              indexName: row.indexName,
              orgName: row.orgName,
              cascadeId: activeTab === 1 ? row.id : '',
            }"
            class="inline w-16"
          >
            <ui-btn-tip class="operatbtn mr-sm" icon="icon-chakanxiangqing" content="查看详情"></ui-btn-tip>
          </create-tabs>

          <ui-btn-tip
            class="operatbtn mr-sm options-color f-14"
            v-for="(item, index) in row.operateList"
            :key="`${item.content}-${index}`"
            :icon="item.icon"
            :content="item.content"
            @click.native="item.funcName(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
      <question-handle
        v-model="handleShow"
        :is-result="isResult"
        :config-data="configData"
        :active-item="activeItem"
        :is-superior="!staticShow"
        @updateList="updateList"
      ></question-handle>
      <qustion-issue v-model="issueShow" :active-item="activeItem" @updateList="updateList"></qustion-issue>
      <component :is="compareComponentName" :active-item="activeItem" v-model="deviceResultVisible"></component>
    </div>
    <keep-alive v-show="componentName">
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import {
  PUBLISHSTATUS,
  HANDLESTATUS,
  CONFIGORGTYPE,
  INDEXMODULE,
  selfTableColumns,
  superiorTableColumns,
  hasDetailIndexList,
  compareIndexType,
} from './util/enum';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin.js';
import cascadeList from '@/config/api/cascadeList';
import governanceevaluation from '@/config/api/governanceevaluation';
import { proxyInterfacefunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import dealWatch from '@/mixins/deal-watch';
import evaluationreport from '@/config/api/evaluationreport';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
import { allIndexTypeObject } from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig.js';

export default {
  name: 'cascadelist',
  props: {},
  mixins: [dealWatch, particularMixin, evaluationoResultMixin],
  data() {
    return {
      INDEXMODULE: INDEXMODULE,
      defaultCurTag: 0,
      interfaceName: cascadeList.getIndexResult,
      componentName: null,
      publishStatus: PUBLISHSTATUS,
      handleStatus: HANDLESTATUS,
      activeTab: 0,
      loading: false,
      panelList: [],
      handleShow: false,
      issueShow: false,
      deviceResultVisible: false,
      tabList: ['本级检测', '上级下发'],
      tableColumns: selfTableColumns,
      tableData: [],
      defaultStoreData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchForm: {
        handleStatus: '',
        indexModule: '',
        name: '',
        publishStatus: '',
        orgCode: '',
        orgName: '',
        pageNumber: 1,
        pageSize: 20,
      },
      activeItem: {},
      staticShow: false,
      isResult: false,
      detallShow: false,
      videoDetallShow: false,
      basicDetallShow: false,
      faceDetailShow: false,
      faceOnlineShow: false,
      faceClockShow: false,
      faceFocusUploadShow: false,
      faceFocusUrlAvailable: false,
      faceBauonetUrl: false,
      carOnline: false,
      CarModalShow: false,
      faceUploadShow: false,
      row: {},
      storeListIds: [],
      treeData: {}, //树形图数据
      themData: {
        componentName: 'taskToOverview', // 需要跳转的组件名
        text: '评测结果', // 跳转页面标题
        title: '评测结果',
        type: 'view',
        // title: "视图基础数据治理主题",
      },
      displayType: 'ORG',
      uuid: '',
      optionalResults: {},
      compareComponentName: null,
    };
  },
  async created() {
    await this.getCascadeConfig();
    this.getParams();
    await this.getOptionalResultsByTaskType();
    await this.getIndexOverviewData();
    this.initConfig();
    this.updateList();
  },
  methods: {
    ...mapActions({
      getCascadeConfig: 'governanceevaluation/getCascadeConfig',
    }),
    /**
     * 是否支持查看结果对比
     * @param row 包含indexType
     * @returns {{indexType: string, component: string, indexName: string, indexId: string} | {indexType: string, component: string, indexName: string, indexId: string} | {indexType: string, component: string, indexName: string, indexId: string} | {indexType: string, component: string, indexName: string, indexId: string} | {indexType: string, component: string, indexName: string, indexId: string}}
     */
    isResultCompare(row) {
      let { indexId } = row;
      // return compareIndexType.find(item => item.indexType === indexType)
      return compareIndexType.find((item) => item.indexId === `${indexId}`);
    },
    isShowDetailsBtn(row) {
      let { indexId } = row;
      return compareIndexType.find((item) => item.indexId === `${indexId}` && item.resultComponent);
    },
    resetSelect() {
      this.$refs.table.selectAll(false);
    },
    async getIndexOverviewData() {
      let data = {
        selfRegionCode: this.optionalResults.selfRegionCode,
        batchIds: this.optionalResults.batchIds,
        dataType: this.optionalResults.targetDataType,
        displayType: this.displayType,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getIndexOverviewReportModeData, data);
        this.uuid = res.data.data.uuid;
      } catch (err) {
        console.log(err);
      }
    },
    async getOptionalResultsByTaskType() {
      this.optionalResults = [];
      try {
        let {
          data: { data },
        } = await this.$http.get(evaluationreport.getOptionalResultsByTaskType, {
          params: {
            taskType: 1, //考核任务 视图数据全量检测任务 写死
          },
        });
        this.optionalResults = data;
        this.titleName = data.selfRegionName;
        this.selfRegionCode = this.optionalResults.selfRegionCode;
        this.administrative = this.selfRegionCode;
        this.year = this.optionalResults.year;
        this.month = this.optionalResults.month;
      } catch (err) {
        console.log(err);
      }
    },
    hasDetail(row) {
      return hasDetailIndexList.includes(row.indexId);
    },
    changeActive(item) {
      this.panelList.forEach((one) => {
        one.orgCode === item.orgCode ? this.$set(one, 'active', !item.active) : this.$set(one, 'active', false);
      });
      if (item.active) {
        this.searchForm.orgCode = item.orgCode;
        this.searchForm.orgName = item.orgName;
      } else {
        this.searchForm.orgCode = '';
        this.searchForm.orgName = '';
      }
      this.searchForm.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initList();
    },
    updateList() {
      this.handleShow = false;
      this.issueShow = false;
      this.searchForm.pageNumber = 1;
      this.initListGet();
      if (this.activeTab === 0) {
        this.row = {};
        this.initSelfStaticList();
      }
    },
    startSearch(searchForm) {
      Object.assign(this.searchForm, searchForm);
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 20;
      this.pageData.pageNum = 1;
      this.initListGet();
    },
    async initConfig() {
      if (this.configData.deployAreaType === CONFIGORGTYPE['区级']) {
        this.tabList = ['上级下发'];
        this.activeTab = 1;
      }
    },
    specailHandleStatus(params) {
      // 未处理(1) - 已处理(已处理2、不处理3)  1未处理 2已处理 3检测结果异议
      if (params.handleStatus) {
        params.handleStatus === '2' ? (params.handleStatus = ['2', '3']) : (params.handleStatus = ['1']);
      } else {
        params.handleStatus = [];
      }
      return params;
    },
    async initList() {
      this.loading = true;
      this.staticShow = true;
      this.tableData = [];
      let params = Object.assign(
        {
          isSuperior: false,
          orgCode: this.configData.deployOrgCode,
        },
        this.searchForm,
      );
      params = this.specailHandleStatus(params);
      // 特殊处理状态
      try {
        let { data } = await this.$http.post(cascadeList.getSelfIssuePageList, params);
        this.tableData = data.data.entities.map((item) => {
          if (item.handleStatus !== '1') {
            this.$set(item, '_disabled', true);
          }
          return item;
        });
        this.pageData.totalCount = data.data.total;
        this.handleOpearation();
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    handleOpearation() {
      this.tableData.forEach((row) => {
        row.operateList = [];
        // 上级下发 && 未处理
        if (row.handleStatus === '1' && this.activeTab === 1) {
          row.operateList.unshift({
            icon: 'icon-chuli',
            content: '处理',
            funcName: (row) => this.showHandle(row),
          });
        }
        // 本级检测 && 未下发
        if (row.publishStatus === '1' && this.activeTab === 0) {
          row.operateList.unshift({
            icon: 'icon-xiafa',
            content: '问题下发',
            funcName: (row) => this.showIssue(row),
          });
        }
        // 已处理、不处理
        if (['2', '3'].includes(row.handleStatus)) {
          row.operateList.unshift({
            icon: 'icon-chakanchulijieguo-01',
            content: '查看处理结果',
            funcName: (row) => this.showHandle(row, true),
          });
        }
        // 结果对比
        if (this.isResultCompare(row) && this.activeTab === 1) {
          row.operateList.unshift({
            icon: 'icon-duibimingxi',
            content: '结果比对',
            funcName: (row) => this.resultCompare(row),
          });
        }
        //查看详情
        if (this.isShowDetailsBtn(row)) {
          row.operateList.unshift({
            icon: 'icon-chakanxiangqing',
            content: '查看详情',
            funcName: (row) => this.handleJump(row),
          });
        }
      });
    },
    async pageListBySuperior() {
      this.loading = true;
      this.tableData = [];
      let params = Object.assign(
        {
          isSuperior: true,
          orgCode: this.configData.deployOrgCode,
        },
        this.searchForm,
      );
      params = this.specailHandleStatus(params);
      try {
        let { data } = await this.$http.post(cascadeList.pageListBySuperior, params, {
          headers: {
            token: this.configData.superiorToken,
            orgCode: this.configData.deployOrgCode,
            flag: 'visitor',
          },
        });
        this.tableData = data.data.entities.map((item) => {
          if (item.handleStatus !== '1') {
            this.$set(item, '_disabled', true);
          }
          item.indexType = allIndexTypeObject[item.indexId]['indexType'];
          return item;
        });
        this.pageData.totalCount = data.data.total;
        this.handleOpearation();
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    async initSelfStaticList() {
      try {
        let { data } = await this.$http.get(cascadeList.cascadeIssueStatistics);
        this.panelList = data.data.map((item) => {
          this.$set(item, 'active', false);
          return item;
        });
      } catch (error) {
        console.log(error);
      }
    },
    showHandle(item, isResult = false) {
      this.handleShow = true;
      this.isResult = isResult;
      this.activeItem = item;
    },
    resultCompare(item) {
      let { indexId } = item;
      // let {component} = compareIndexType.find(item => item.indexType === indexType)
      let { component } = compareIndexType.find((item) => item.indexId === `${indexId}`);
      this.compareComponentName = component;
      this.deviceResultVisible = true;
      this.activeItem = item;
    },
    showIssue(item) {
      this.activeItem = item;
      this.issueShow = true;
    },
    tagChange(index, item) {
      this.resetSelect();
      this.tableData = [];
      this.searchForm = {
        handleStatus: '',
        indexModule: '',
        name: '',
        publishStatus: '',
        orgCode: '',
        orgName: '',
        pageNumber: 1,
        pageSize: 20,
      };
      this.pageData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
      const tabObject = {
        本级检测: 0,
        上级下发: 1,
      };
      this.activeTab = tabObject[item];
      this.row = {};
      // 本级检测 0,上级下发1
      this.activeTab ? (this.tableColumns = superiorTableColumns) : (this.tableColumns = selfTableColumns);
      this.updateList();
    },
    storeSelectList(list) {
      this.storeListIds = list.map((item) => item.id);
    },
    bulkPublish() {
      if (!this.storeListIds.length) {
        this.$Message.error('请选择下发问题');
        return;
      }
      this.$UiConfirm({
        content: `您要下发${this.storeListIds.length}条问题 ，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          await this.$http.post(cascadeList.publishIssue, this.storeListIds);
          this.updateList();
        })
        .catch((res) => {
          console.log(res);
        });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchForm.pageNumber = val;
      this.initListGet();
    },
    initListGet() {
      // 默认统计不展示
      this.staticShow = false;
      // 如果没配置token就只有本级检测
      if (this.activeTab === 1 && !!this.configData.superiorToken) {
        this.pageListBySuperior();
      }
      if (this.activeTab === 0) {
        this.initList();
        this.$nextTick(() => {
          this.staticShow = true;
        });
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = val;
      this.initListGet();
    },
    async getTree(indexId, resultId, outcome) {
      let data = {
        indexId: indexId,
        resultId: resultId,
        outcome: outcome,
      };
      let res = null;
      /**------级联清单特殊替换处理接口(后端转发)-------**/
      if (this.row.hasOwnProperty('cascadeId')) {
        let cascadeId = this.row.cascadeId;
        let superiorToken = this.row.superiorToken;
        let interfaceName = governanceevaluation.deviceList;
        res = await proxyInterfacefunc(data, interfaceName, cascadeId, superiorToken, 'post', {});
      } else {
        res = await this.$http.post(governanceevaluation.deviceList, data);
      }
      if (res.data.code == '200') {
        if (res.data.data.length === 0) {
          this.treeData = res.data.data;
        } else {
          let data = [{ deviceName: '车辆卡口设备' }];
          data[0].children = res.data.data;
          this.treeData = data;
        }
      }
    },
    idx(val) {
      this.getTree(this.row.indexId, this.row.resultId, val);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[0];
      } else {
        this.componentName = name;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    handleJump(row) {
      if (this.activeTab === 1) {
        //上级下发
        this.deviceResultVisible = true;
        this.activeItem = row;
        let { resultComponent } = this.isResultCompare(row);
        this.compareComponentName = resultComponent;
      } else {
        let { indexId, batchId, indexType, orgCode, regionCode, taskSchemeId, id } = row;
        this.jump({
          indexId,
          batchId,
          indexType,
          orgCode,
          regionCode,
          statisticType: 'ORG',
          taskSchemeId,
          cascadeId: id,
          displayType: 'ORG',
        });
      }
    },
  },
  watch: {
    $route: 'getParams',
  },
  computed: {
    ...mapGetters({
      configData: 'governanceevaluation/configData',
    }),
    isProvincial() {
      return this.configData.deployAreaType === CONFIGORGTYPE['省级'];
    },
  },
  components: {
    taskToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue').default,
    DeviceResultCompare: require('@/views/disposalfeedback/cascadelist/components/device-result-compare.vue').default,
    offlineResultCompare: require('@/views/disposalfeedback/cascadelist/components/offline-result-compare.vue').default,
    QuantityResultCompare: require('@/views/disposalfeedback/cascadelist/components/quantity-result-compare.vue')
      .default,
    VideoPlayingAccuracyResult:
      require('@/views/disposalfeedback/cascadelist/components/video-playing-accuracy-result.vue').default,
    ViedoPlatformOnlineRateResult:
      require('@/views/disposalfeedback/cascadelist/components/viedo-platform-online-rate-result.vue').default,
    VideoQualityPassRateResult:
      require('@/views/disposalfeedback/cascadelist/components/video-quality-pass-rate-result.vue').default,
    tagView: require('@/components/tag-view').default,
    UiTable: require('@/components/ui-table.vue').default,
    SearchModule: require('./search-module.vue').default,
    QuestionHandle: require('./components/question-handle.vue').default,
    QustionIssue: require('./components/qustion-issue.vue').default,
    LineTitle: require('./components/line-title.vue').default,
    GetToken: require('./get-token.vue').default,
    PanelItem: require('./components/panel-item.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    // indexid 划分
    // 人脸卡口在线率,
    // 71,
    // 人脸卡口设备时钟准确率
    // 16,
    // 重点 - 人脸卡口设备及时上传率
    // 15
    // 人脸卡口设备url可用率
    // 11
    // 重点人脸卡口设备url可用率
    // 12
    // 人脸卡口设备抓拍合格率
    // 13
    // 人脸卡口设备及时上传率
    // 14
    // 车辆卡口在线率
    // 72,
    // 35 重点车辆卡口设备图片url可用率
    // 29,25,27,26,30,31,32,34,35,33
    // 63 普通字母标注合规率
    // 21,22,23,24,62,63
  },
};
</script>
<style lang="less" scoped>
.options-color {
  color: #438cff;
  font-size: 14px !important;
}
.cascade-container {
  overflow-y: auto;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .cascade-list {
    .panel-list {
      display: flex;
      flex-wrap: wrap;
      .panel-item {
      }
    }
    .ui-table {
      height: 700px;
    }
    @{_deep}.icon-chuli {
      color: #12b839;
    }
    @{_deep}.icon-chakanxiangqing {
      color: #438cff;
    }
  }
}
</style>
