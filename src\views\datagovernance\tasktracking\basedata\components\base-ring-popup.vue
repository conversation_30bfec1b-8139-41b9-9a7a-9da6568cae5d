<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <!-- <tagView :list="['入库失败', '入库成功']" @tagChange="tagChange" /> -->
    <slot name="modalTop"></slot>
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <draw-echarts
        v-for="(item, index) of echartList"
        :key="index"
        :echart-option="item.echartRingOption"
        :echart-style="ringStyle"
        :echarts-loading="echartsLoading"
        :ref="'zdryChart' + index"
        class="charts"
      >
      </draw-echarts>
    </div>
    <slot name="tableTop"></slot>
    <div class="box-wrapper">
      <ui-table
        class="ui-table mt-sm"
        :table-columns="tableColumns"
        :table-data="tableData"
        :minus-height="minusTable"
        :loading="loading"
      >
        <template #action="{ row }">
          <slot name="tabelAction" :item="{ row }"></slot>
        </template>
      </ui-table>
    </div>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {},
    tableColumns: {},
    tableData: {
      default: () => [],
    },
    totalCount: {
      default: 0,
    },
    echartList: {
      default: () => [],
    },
    loading: {
      default: false,
    },
    echartsLoading: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        // top: "0.5rem",
        width: '95%',
      },
      activeRouterName: null,
      minusTable: 530,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      allDataEchartsRing: {},
      ringStyle: {
        height: '180px',
        //width: '300px',
      },
      popUpOption: {
        title: '',
      },
      permission: '',
      title: '',
    };
  },
  methods: {
    // process-block组件触发
    init(popUpOption) {
      this.title = popUpOption.title;
      if (['7001', '7002', '7003', '7004', '7006'].includes(popUpOption.filedData.componentCode)) {
        this.minusTable = 600;
      } else {
        this.minusTable = 560;
      }
      if (popUpOption.subTitle) {
        this.title = `${popUpOption.title}-${popUpOption.subTitle}`;
      }
      this.popUpOption = popUpOption;
      this.visible = true;
      this.$emit('popUpGetData', popUpOption);
    },
    tagChange(val) {
      this.curTag = val;
    },
    changePage(val) {
      this.$emit('changePage', val);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.$emit('changePageSize', val);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    totalCount(val) {
      this.pageData.totalCount = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  background: var(--bg-sub-content);
  margin: 10px 0 0;
  width: 100%;
  //height: px;
  .charts {
    flex: 1;
  }
}
.statics {
  color: #fff;
}
.box-wrapper {
  position: relative;
}
@{_deep}.ui-table .no-data {
  top: 60%;
}
.ui-page {
  padding: 20px 0;
}
</style>
