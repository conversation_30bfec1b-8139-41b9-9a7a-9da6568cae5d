<template>
  <div class="statistics-nums">
    <div class="num-div" v-for="(num, index) in numLength" :key="index">
      <div class="top top-1" :class="topAnimation(index)">
        <span>{{ numString[index] }}</span>
      </div>
      <div class="top top-2">
        <span>{{ newString[index] }}</span>
      </div>
      <div class="bottom bottom-1" :class="bottomAnimation(index)">
        <span class="bottom-num">{{ numString[index] }}</span>
      </div>
      <div class="bottom bottom-2">
        <span class="bottom-num">{{ newString[index] }}</span>
      </div>
      <span class="split" v-if="numLength !== index + 1 && index % 3 === (numLength - 1) % 3">,</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.statistics-nums {
  height: 62px;
}
.num-div {
  height: 60px;
  display: inline-block;
  position: relative;
  font-size: 40px;
  width: 30px;
  margin-left: 10px;
}
.split {
  position: absolute;
  right: -9px;
  bottom: 0;
  color: #fff;
  font-weight: 700;
  font-size: 30px;
}
.active {
  transform: translateY(40px) rotateX(180deg);
}
.transparent {
  opacity: 0;
}
.transition {
  transition: all 1s linear;
}
.top {
  position: absolute;
  top: 0;
  left: 0;
  background: radial-gradient(at center bottom, #14455f, #1c6f88);
  border-radius: 5px 5px 0 0;
  color: #fff;
  height: 50%;
  text-align: center;
  line-height: 55px;
  border-bottom: 2px solid #0d4f82;
  overflow: hidden;
  z-index: 1;
  width: 30px;
  span {
    font-family: UniDreamLED;
  }
}
.top-1 {
  z-index: 2;
}
.bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  background: radial-gradient(at center top, #14455f, #1c6f88);
  border-radius: 0 0 5px 5px;
  color: #fff;
  height: 50%;
  text-align: center;
  line-height: 30px;
  overflow: hidden;
  width: 30px;
  span {
    font-family: UniDreamLED;
  }
}
.bottom-1 {
  z-index: 1;
}
.bottom-num {
  display: block;
  margin-top: -50%;
}
</style>

<script>
export default {
  data() {
    return {
      newString: '',
      numString: '',
      top1Transparent: false,
      bottom1Transparent: false,
      top1Active: false,
      transition: true,
      changeArr: [],
    };
  },
  mounted() {
    this.numToString();
  },
  filters: {},
  methods: {
    numToString() {
      this.numString = this.statisticsNum.num.toString();
      this.newString = this.statisticsNum.newNum.toString();
      this.initChangeArr();
      this.isChange();
      this.topAnimation();
      this.bottomAnimation();
      this.undateNum();
    },
    initChangeArr() {
      for (let i = 0, length = this.numString.length; i < length; i++) {
        this.changeArr.push(false);
      }
    },
    undateNum() {
      this.top1Active = true;
      this.top1Transparent = true;
      this.bottom1Transparent = true;
      let time = setTimeout(() => {
        this.transition = false;
        this.top1Active = false;
        this.top1Transparent = false;
        this.top1Active = false;
        this.bottom1Transparent = false;
        this.statisticsNum.num = this.statisticsNum.newNum;
        this.numString = this.statisticsNum.num.toString();
        let time2 = setTimeout(() => {
          this.transition = true;
          time2 = null;
          time = null;
        }, 50);
      }, 1000);
    },
    isChange() {
      for (let i = 0, length = this.numString.length; i < length; i++) {
        if (this.numString[i] !== this.newString[i]) {
          this.changeArr[i] = true;
        }
      }
    },
    topAnimation(index) {
      if (this.changeArr[index]) {
        return {
          'transparent': this.top1Transparent,
          'active': this.top1Active,
          'transition': this.transition,
        };
      }
    },
    bottomAnimation(index) {
      if (this.changeArr[index]) {
        return { 'transparent': this.bottom1Transparent, 'transition': this.transition };
      }
    },
  },
  watch: {
    'statisticsNum.newNum'() {
      this.numToString();
    },
  },
  computed: {
    numLength() {
      return this.statisticsNum.num.toString().length;
    },
  },
  props: {
    statisticsNum: {
      required: true,
    },
  },
  components: {},
};
</script>
