<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" footer-hide>
    <dynamic-condition
      class="mb-sm"
      :form-item-data="filterList"
      :form-data="searchData"
      :need-reset-copy-search="needResetCopySearch"
      @search="search"
      @reset="search"
    >
      <template #otherButton>
        <Button type="primary" :loading="exportLoading" @click="onExport">
          <i class="icon-font icon-daochu font-white mr-xs"></i> 导出
        </Button>
      </template>
    </dynamic-condition>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #isDel="{ row }">
        <span>{{ row.isDel === 1 ? '是' : '否' }}</span>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<script>
import { filterList, tableColumns_details } from './tableConfig';
import viewassets from '@/config/api/viewassets';

export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
  },
  props: {
    detailData: {
      type: Object,
    },
    value: {
      type: Boolean,
    },
    title: {
      type: String,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      styles: {
        width: '9rem',
        height: '4.3rem',
      },
      filterList: filterList,
      tableColumns: [],
      tableData: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        regionCode: '', // 树组件必须传入字段
        civilCode: '', // 接口需要传入的真正的【行政区划】过滤条件字段
        types: [], // 类型  --  number  1新增 2移除 3还原
        startTime: '',
        endTime: '',
        sbgnlx: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      needResetCopySearch: false,
      exportLoading: false,
    };
  },
  created() {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        // 初始化 默认查询条件
        let { civilCode, startTime, endTime, type, entryType } = this.detailData;
        this.searchData.regionCode = civilCode; // 默认查询带过来的 行政区划
        this.searchData.civilCode = civilCode;
        this.searchData.startTime = startTime;
        this.searchData.endTime = endTime;
        this.searchData.types = type === 'add' ? [1, 3] : [2];
        this.searchData.sbgnlx =
          entryType === 'video' ? '1' : entryType === 'vehicle' ? '2' : entryType === 'face' ? '3' : '';
        this.tableColumns = tableColumns_details(this.detailData);
        this.needResetCopySearch = true;
        this.$nextTick(() => {
          this.needResetCopySearch = false;
        });
        this.getTableList();
      }
    },
    'searchData.regionCode'(val) {
      this.searchData.civilCode = val;
    },
  },
  methods: {
    search(obj) {
      this.searchData = { ...obj };
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    async getTableList() {
      try {
        this.loading = true;
        let { pageNum, pageSize } = this.pageData;
        let data = {
          ...this.searchData,
          pageNumer: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(viewassets.getPageList, data);
        this.tableData = res.data.data.entities || [];
        this.pageData.totalCount = res.data.data.total || 0;
      } catch (error) {
        this.tableData = [];
        this.pageData.totalCount = 0;
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 导出
    async onExport() {
      try {
        this.exportLoading = true;
        let { pageNum, pageSize } = this.pageData;
        let data = {
          ...this.searchData,
          pageNumer: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(viewassets.pageListExport, data, { responseType: 'blob' });
        this.$util.common.exportfile(res);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-content {
  height: 100%;
  .ivu-modal-body {
    display: flex;
    flex-direction: column;
    height: calc(100% - 15px);
  }
  .ui-table {
    flex: 1;
  }
}
</style>
