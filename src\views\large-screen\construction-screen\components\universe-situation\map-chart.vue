<!--
    * @FileDescription: 地图
    * @Author: H
    * @Date: 2024/04/29
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="mapChart" class="mapChart" ref="mapChart"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts';
import mapConfig from './bengbu.json';
export default {
    props: {
        urlType: {
            type: Number,
            default: 0
        },
        picData: {
            type: Array,
            default: () => {
                let list = [
                    { value: '75', name: '有效' },
                    { value: '25', name: '无效' },
                ];
                return list
            }
        },
        title: {
            type: String,
            default: ''
        }
    },  
    data() {
        return {
            myEchart: null,
        }
    },
    mounted() {
        // this.init()
    },
    methods: {
        init(list) {
            var mapDate = [
                { name: '蚌山区', value: 100, regionCode: '340303', exData: {} },
                { name: '龙子湖区', value: 33.33, regionCode: '340302', exData: {} },
                { name: '禹会区', value: 16.67, regionCode: '340304', exData: {} },
                { name: '淮上区', value: 40, regionCode: '340311', exData: {} },
                { name: '五河县', value: 6.67, regionCode: '340322', exData: {} },
                { name: '固镇县', value: 26.67, regionCode: '340323', exData: {} },
                { name: '怀远县', value: 33.33, regionCode: '340321', exData: {} },
            ];
            let mapList = new Map(list.map(item => [item.regionCode, item]))
            mapDate.forEach(item => {
                if(mapList.get(item.regionCode)) {
                    let obj = JSON.parse(mapList.get(item.regionCode).extensionData)
                    item.value = this.parseValue(obj);
                    item.exData = obj;
                }
            })
            this.mapchart(mapDate)
        },
        parseValue(obj) {
            let num = 0;
            for(let key in obj) {
                num += obj[key]
            }
            return num
        },
        mapchart(mapDate) {
            this.myEchart = echarts.init(this.$refs.mapChart);
            echarts.registerMap('蚌埠市', mapConfig);
            var mapName = "蚌埠市"
            // var mapDate = [
            //     { name: '蚌山区', value: 100 },
            //     { name: '龙子湖区', value: 33.33 },
            //     { name: '禹会区', value: 16.67 },
            //     { name: '淮上区', value: 40 },
            //     { name: '五河县', value: 6.67 },
            //     { name: '固镇县', value: 26.67 },
            //     { name: '怀远县', value: 33.33 },
            // ];
            let option = {
                tooltip: {
                    trigger: "item",
                    borderWidth: '0',
                    borderColor: "none",
                    padding: 16,
                    textStyle: {
                        fontSize: 12,
                        color: 'rgba(209, 212, 220, 1)',
                    },
                    tooltip: {
                        show: true
                    },
                    extraCssText: 'background: rgba(61, 63, 71, 1)',
                    className: "custom-tooltip-box custom-tooltip-box-mini",
                    formatter: (params) => {
                        let taskObj = {};
                        mapDate.forEach(item => {
                            if(item.name == params.name) {
                                taskObj = item;
                            }
                        })
                        return `<div class='custom-tooltip-style'>
                                <div class="tooltip-title">
                                    <div class="tooltip-dot"></div>
                                    <span>${params.name}</span> 
                                </div>
                                <div class="tooltip-withe-line"></div>
                                <div class="tooltip-content">
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">规划建设总数：</span>
                                        <span class="tooltip-warpper-content">${ taskObj.exData.planBuildCount}</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">已建数量：</span>
                                        <span class="tooltip-warpper-content">${ taskObj.exData.buildCount }%</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">摄像机覆盖率：</span>
                                        <span class="tooltip-warpper-content">${ taskObj.exData.cameraCoverage}%</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">高清摄像机占比：</span>
                                        <span class="tooltip-warpper-content">${ taskObj.exData.cameraHDRatio}%</span>
                                    </p>
                                    <p class="tooltip-warpper">
                                        <span class="tooltip-warpper-name">智能抓拍机占比：</span>
                                        <span class="tooltip-warpper-content">${ taskObj.exData.cameraSmartRatio }%</span>
                                    </p>   
                                </div>
                            </div>`
                    }
                },
                geo: [
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        z: 2,
                        layoutCenter: ["50%", "42%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                        emphasis: {
                            show: false,
                        },
                        },
                        itemStyle: {
                        normal: {
                            areaColor: {
                                type: "linear",
                                x: 60,
                                y: 10,
                                x2: 20,
                                y2: 110,
                                colorStops: [
                                    {
                                    offset: 0,
                                    color: "rgba(38,58,207,0.8)", // 0% 处的颜色
                                    },
                                    {
                                    offset: 1,
                                    color: "rgba(74,133,225,0.8)", // 50% 处的颜色
                                    },
                                ],
                                global: true, // 缺省为 false
                            },
                            borderColor: "#fff",
                            borderWidth: 0.2,
                        },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "43%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "44%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "45%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 1,
                                shadowColor: "#8cd3ef",
                                shadowOffsetY: 10,
                                shadowBlur: 100,
                                areaColor: "transparent",
                            },
                        }
                    },
                    {
                        map: mapName,
                        aspectScale: 0.8,
                        zoom: 1,
                        top: "12%",
                        type: "map",
                        layoutCenter: ["50%", "46%"],
                        layoutSize: "100%",
                        show: true,
                        roam: false,
                        label: {
                            emphasis: {
                                show: false,
                            },
                        },
                        itemStyle: {
                            normal: {
                                borderColor: "#4778C4",
                                borderWidth: 10,
                                shadowColor: "rgba(121,149,250,1)",
                                shadowOffsetY: 10,
                                shadowBlur: 5,
                                opacity: 0.2,
                                areaColor: "rgba(121,149,250,0.1)",
                            },
                        }
                    },
                ],
                series: [
                    {
                        name: mapName,
                        type: "map",
                        mapType: mapName,
                        selectedMode: "false", //是否允许选中多个区域
                        showLegendSymbol: true,
                        roam: false,
                        top: "8%",
                        aspectScale: 0.8,
                        layoutSize: "100%",
                        layoutCenter: ["50%", "42%"],
                        zoom: 0, //当前视角的缩放比例
                        itemStyle: {
                            normal: {
                                areaColor: 'transparent',
                                borderColor: "#80AAE1",
                                borderWidth: 2
                            },
                            //选中样式
                            emphasis: {
                                disabled: true,
                                borderWidth: 2,
                                label:{
                                    textStyle: {
                                        fontSize: 12,
                                        fontWeight: "bolder",
                                        color: "#9CCDDC",
                                        shadowColor: "rgba(39,76,193,0.4)",
                                        shadowBlur: 2,
                                        shadowOffsetX: 1,
                                        shadowOffsetY: 1,
                                    },
                                },
                                borderColor: "#80AAE1",
                                areaColor: "rgba(8, 224, 255, 0.8)",
                            },
                        },
                        label: {
                            normal: {
                                show: true,
                                textStyle: {
                                    fontSize: 12,
                                    fontWeight: "bolder",
                                    color: "#9CCDDC",
                                    shadowColor: "rgba(39,76,193,0.4)",
                                    shadowBlur: 2,
                                    shadowOffsetX: 1,
                                    shadowOffsetY: 1,
                                },
                            },
                        },
                        data: mapDate,
                    },
                ],
            };
            this.myEchart.setOption(option)
            window.addEventListener('resize', () => this.myEchart.resize())
        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    .mapChart{
        height: 539px;
        width: 757px;
        margin: auto;
    }
    
}
</style>
<style lang='less'>
@import "../../../style/resetui.less";
</style>