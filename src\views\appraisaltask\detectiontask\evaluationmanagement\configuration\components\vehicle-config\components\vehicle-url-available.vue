<template>
  <div class="vehicle-modal">
    <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom" :label-width="130">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          基础信息
          <div slot="content" class="mt-md">
            <common-form
              :label-width="130"
              class="common-form"
              ref="commonForm"
              :moduleAction="moduleAction"
              :form-data="formData"
              :form-model="formModel"
              :task-index-config="taskIndexConfig"
              @updateFormData="updateFormData"
              @handleDetect="getDetect"
            >
              <div slot="extractCar">
                <FormItem label="每设备抽取图片" prop="captureNum">
                  <InputNumber
                    v-model="formData.captureNum"
                    class="input-width-number"
                    placeholder="请输入抽取设备图片"
                    clearable
                  ></InputNumber>
                </FormItem>
                <FormItem label="" :class="{ 'mt-minus-sm': formData.captureNum }" prop="isMissPic">
                  <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
                    <span>图片数量不足，则设备不合格</span>
                  </Checkbox>
                </FormItem>
                <FormItem
                  prop="deviceQueryForm.dayByCapture"
                  label="图片抽取范围"
                  :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
                >
                  <span class="base-text-color mr-xs">近</span>
                  <InputNumber
                    v-model.number="formData.deviceQueryForm.dayByCapture"
                    :min="0"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color">天，</span>
                  <InputNumber
                    v-model.number="formData.deviceQueryForm.startByCapture"
                    :min="0"
                    :max="23"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color mr-sm">点至</span>
                  <InputNumber
                    v-model.number="formData.deviceQueryForm.endByCapture"
                    :min="0"
                    :max="23"
                    :precision="0"
                    class="mr-sm width-mini"
                  ></InputNumber>
                  <span class="base-text-color mr-sm">点</span>
                </FormItem>
              </div>
              <div slot="waycondiction" class="mt-xs">
                <div>
                  <span class="base-text-color">检测条件：</span>
                  <div>
                    <Checkbox
                      class="ml-sm"
                      v-model="formData.deviceQueryForm.detectPhyStatus"
                      true-value="1"
                      false-value="0"
                      >设备可用
                    </Checkbox>
                  </div>
                  <div v-if="formData.detectMode !== '2'">
                    <FormItem
                      prop="deviceQueryForm.dayByFilterOnline"
                      label=""
                      :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
                    >
                      <Checkbox class="ml-sm mb-sm" v-model="formData.deviceQueryForm.filterOnline"
                        >设备有流水</Checkbox
                      >
                      <span class="base-text-color">近</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                        :min="0"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color">天，</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.startByFilterOnline"
                        :min="0"
                        :max="23"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color mr-sm">点至</span>
                      <InputNumber
                        v-model.number="formData.deviceQueryForm.endByFilterOnline"
                        :min="0"
                        :max="23"
                        :precision="0"
                        class="mr-sm width-mini"
                      ></InputNumber>
                      <span class="base-text-color mr-sm">点</span>
                      <div class="capture-vehicle">
                        <span class="base-text-color mr-sm">抓拍车辆不少于</span>
                        <InputNumber
                          v-model.number="formData.deviceQueryForm.countByFilterOnline"
                          :min="0"
                          :precision="0"
                          class="mr-sm width-mini"
                        ></InputNumber>
                        <span class="base-text-color">张</span>
                        <p class="color-failed">说明：系统只检测满足条件的设备。</p>
                      </div>
                    </FormItem>
                  </div>
                </div>
              </div>
            </common-form>
          </div>
        </Panel>
        <Panel name="2">
          检测参数
          <div slot="content" class="mt-md">
            <FormItem label="最大等待时长">
              <InputNumber
                v-model.number="formData.visitTimeout"
                :min="0"
                :max="Number.MAX_SAFE_INTEGER"
                :precision="0"
                placeholder="请输入最大等待时长"
                class="mr-sm input-width-number"
              ></InputNumber>
              <span class="base-text-color">毫秒</span>
            </FormItem>
            <FormItem label="检测大图标注抓拍时间和地点">
              <RadioGroup v-model="formData.snap" @on-change="onChangeSnap">
                <Radio :label="1" class="mr-lg">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              v-if="formData.snap !== 0 && !!formData.snap"
              label="优先算法"
              prop="ocrModel"
              key="'isShowOcrModel'"
            >
              <Select
                v-model="customFormData.optimizationAlgorithm"
                placeholder="请选择算法"
                transfer
                class="input-width-number"
                @on-change="onChangeOcrModel($event, 'optimizationAlgorithm')"
              >
                <template v-for="(algorithmItem, index) in algorithmVendorData">
                  <Option
                    :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
                    :key="`algorithmItem${index}`"
                    :data="algorithmItem.algorithmType"
                  >
                    {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
                  </Option>
                </template>
              </Select>
            </FormItem>
            <!--     深网 才支持751       -->
            <FormItem class="mb-xs" v-if="customFormData.optimizationAlgorithm">
              <Checkbox
                v-model="customFormData.optimizationAlgorithm751"
                @on-change="onChange751($event, 'optimizationAlgorithm')"
                :true-value="1"
                :false-value="0"
                >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
              >
            </FormItem>
            <!--          <FormItem  prop="optimizationAlgorithmDetectContent"  v-if="customFormData.optimizationAlgorithm === 1 && customFormData.optimizationAlgorithm751" >
            <template #label>
              <Tooltip placement="top-start" class="tooltip-sample-graph">
                <i class="icon-font icon-wenhao f-16 mr-xs" :style="{color: 'var(--color-warning)'}"></i>
                <span>检测内容</span>
                <div slot="content">
                  <div class="check-content-img">
                    <img
                      src="@/assets/img/datagovernance/subtitle-reset.png"
                      alt="示例图"
                      style="width: 100%"
                    />
                  </div>
                </div>
              </Tooltip>
            </template>
            <CheckboxGroup class="inline" v-model="formData.optimizationAlgorithm.detectContent">
              <div  v-for="(checkItem, checkIndex) in detectionRules"
                    :key="'check' + checkIndex">
                <Checkbox :label="checkItem.value" >
                  <span>{{ checkItem.text }}</span>
                </Checkbox>
              </div>
            </CheckboxGroup>
          </FormItem>-->
            <FormItem
              class="mb-md"
              v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
            >
              <template #label>
                <Tooltip placement="top-start" class="tooltip-sample-graph">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                  </div>
                  检测内容
                </Tooltip>
              </template>
            </FormItem>
            <detect-content
              ref="optimizationAlgorithmDetectContent"
              v-bind="$props"
              :config-info="formData.optimizationAlgorithm"
              :detect-content-list="formData.optimizationAlgorithm.detectContent"
              v-if="customFormData.optimizationAlgorithm && customFormData.optimizationAlgorithm751"
            >
            </detect-content>
            <FormItem v-if="formData.snap !== 0 && !!formData.snap" label="备用算法" class="mb-md">
              <Select
                v-model="customFormData.standbyAlgorithm"
                placeholder="请选择备用算法"
                transfer
                clearable
                class="input-width-number"
                @on-change="onChangeOcrModel($event, 'standbyAlgorithm')"
              >
                <template v-for="(algorithmItem, index) in algorithmVendorData">
                  <Option
                    :value="`${algorithmItem.algorithmType},${algorithmItem.algorithmVendorType}`"
                    :key="`algorithmItem${index}2`"
                    :data="algorithmItem.algorithmType"
                  >
                    {{ algorithmItem.algorithmVendorTypeName }}-{{ algorithmItem.algorithmTypeName }}
                  </Option>
                </template>
              </Select>
            </FormItem>
            <FormItem class="mb-xs" v-if="customFormData.standbyAlgorithm">
              <Checkbox
                v-model="customFormData.standbyAlgorithm751"
                @on-change="onChange751($event, 'standbyAlgorithm')"
                :true-value="1"
                :false-value="0"
                >按照《GAT-751-2008视频图像文字标注规范》要求检测</Checkbox
              >
            </FormItem>
            <!--          <FormItem label="" prop="standbyAlgorithmDetectContent" v-if="customFormData.standbyAlgorithm === 1 && customFormData.standbyAlgorithm751"  >
            <template #label>
              <Tooltip placement="top-start" class="tooltip-sample-graph">
                <i class="icon-font icon-wenhao f-16 mr-xs" :style="{color: 'var(--color-warning)'}"></i>
                <span>检测内容</span>
                <div slot="content">
                  <div class="check-content-img">
                    <img
                      src="@/assets/img/datagovernance/subtitle-reset.png"
                      alt="示例图"
                      style="width: 100%"
                    />
                  </div>
                </div>
              </Tooltip>
            </template>
            <CheckboxGroup class="inline" v-model="formData.standbyAlgorithm.detectContent">
              <div v-for="(checkItem, checkIndex) in detectionRules"
                   :key="'check' + checkIndex">
                <Checkbox
                  :label="checkItem.value"
                >
                  <span>{{ checkItem.text }}</span>
                </Checkbox>
              </div>
            </CheckboxGroup>
          </FormItem>-->
            <FormItem class="mb-md" v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751">
              <template #label>
                <Tooltip placement="top-start" class="tooltip-sample-graph">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                  </div>
                  检测内容
                </Tooltip>
              </template>
            </FormItem>
            <detect-content
              ref="standbyAlgorithmDetectContent"
              v-bind="$props"
              :config-info="formData.standbyAlgorithm"
              :detect-content-list="formData.standbyAlgorithm.detectContent"
              v-if="customFormData.standbyAlgorithm && customFormData.standbyAlgorithm751"
            >
            </detect-content>
          </div>
        </Panel>
        <Panel name="3">
          高级参数
          <div slot="content" class="mt-md">
            <FormItem label="更新车辆图片可用性状态" :label-width="170" class="mb-sm">
              <RadioGroup v-model="formData.isUpdatePhyStatus">
                <Radio :label="1" class="mr-lg">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="是否需要复检" :label-width="170">
              <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
                <Radio label="1" class="mr-lg">是</Radio>
                <Radio label="2">否</Radio>
              </RadioGroup>
              <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
            </FormItem>
            <FormItem label="复检设备" v-if="isRecheck === '1'" prop="reinspect.model" :label-width="170">
              <RadioGroup v-model="formData.reinspect.model">
                <Radio label="UNQUALIFIED">检测不合格设备</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="复检次数"
              prop="maxCount"
              :label-width="170"
              v-if="isRecheck === '1' && needRecheckNumIndex.includes(indexType)"
            >
              <InputNumber
                v-model.number="formData.reinspect.maxCount"
                class="mr-xs"
                :max="5"
                :min="1"
                :precision="0"
              ></InputNumber>
              <span class="base-text-color">次</span>
            </FormItem>
            <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType" :label-width="170">
              <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
                <Radio label="INTERVAL" class="mr-lg">时间间隔</Radio>
                <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
              </RadioGroup>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
                <span class="base-text-color">检测结束</span>
                <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
                <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="1">时</Option>
                  <Option :value="2">分</Option>
                </Select>
                <span class="base-text-color ml-md">后，开始复检</span>
              </div>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
                <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="3">当天</Option>
                  <Option :value="4">第二天</Option>
                  <Option :value="5">第三天</Option>
                </Select>
                <TimePicker
                  :value="formData.reinspect.scheduleValue"
                  transfer
                  format="HH:mm"
                  placeholder="请选择"
                  class="width-xs"
                  @on-change="handleChangeTime"
                ></TimePicker>
              </div>
            </FormItem>
          </div>
        </Panel>
      </Collapse>
    </Form>
  </div>
</template>

<script>
import { defaultEmphasisData } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import { mapGetters } from 'vuex';
import { isUpdatePhyStatus } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
import {
  defaultDetectContent,
  needRecheckNumIndex,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    DetectContent:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmVendorData: 'algorithm/getAlgorithmList',
    }),
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    configInfo: {
      handler(val) {
        if (this.formModel === 'edit' && val) {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
            optimizationAlgorithm: {
              ...this.formData.optimizationAlgorithm,
              ...this.configInfo.optimizationAlgorithm,
            },
            standbyAlgorithm: {
              ...this.formData.standbyAlgorithm,
              ...this.configInfo.standbyAlgorithm,
            },
          };
          if (this.formData.reinspect) {
            this.formData.reinspect.maxCount = this.formData.reinspect.maxCount || null;
          }
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
        }
        this.setDefaultTime();
        this.setRecheck();
        this.setOcrModel();
      },
      deep: true,
      immediate: true,
    },
  },
  data() {
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateOcrModel = (rule, value, callback) => {
      if (this.formData.snap && !this.customFormData.optimizationAlgorithm) {
        callback(new Error('请选择优先算法'));
      }
      callback();
    };
    const validateOptimizationAlgorithmDetectContent = ({ triggerElement }, value, callback) => {
      if (this.customFormData[`${triggerElement}751`] && this.formData[triggerElement].detectContent.length === 0) {
        callback(new Error('请选择检测内容'));
      }
      callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      algorithm: [], //算法
      formData: {
        snap: null, //检测大图标注抓拍时间和地点
        captureNum: null, //抓拍数量
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        reinspect: null,
        detectMode: '1',
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: null,
          countByFilterOnline: null,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
        visitTimeout: null,
        isUpdatePhyStatus: 0,
        optimizationAlgorithm: {
          //优先算法
          ocrModel: '', //算法
          algorithmType: '', //算法类型
          detectContent: [], //检测内容
          existsTime: false, //-是否在指定区域标注
          //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
          timeFormCheck: false,
          //（右上角）时间信息检测 --位置规范检测：
          timePositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。 右边距: min
          timePositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。 右边距: max
          timePositionCheckMax: null,
          //（右上角）时间信息检测 --准确性规则，
          timeDeviationCheck: false,
          //设备时间与标准时间允许偏差 XX 秒
          timeDeviationCheckValue: 0,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsArea: false,
          //（右下角）区划与地址信息检测 --位置规范检测：
          areaPositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
          areaPositionCheckValueLine: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
          areaPositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
          areaPositionCheckMax: null,
          //地址信息只能占用一行
          areaPositionCheckLine: false,
          //（右下角）区划与地址信息检测 --准确性规则，
          areaDeviationCheck: false,
          // 省/市/区县、地点信息需与档案信息保持一致
          areaDeviationCheckValue: false,
          //队所（派出所）信息需标注准确（与组织机构表匹配）
          areaDeviationCheckLine: false,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsCamera: false,
          //准确性规则
          cameraDeviationCheck: false,
          //精确匹配2、综合匹配1
          areaDeviationCheckValueType: 1,
          // 【省级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeProvince: false,
          // 【地市级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeCity: false,
          // 【区县级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeCounty: false,
          // 【地址】信息需与档案信息保持一致
          areaDeviationCheckValueTypeInfo: false,
        },
        standbyAlgorithm: {
          //备用算法
          ocrModel: '',
          algorithmType: '',
          detectContent: [],
          existsTime: false, //-是否在指定区域标注
          //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
          timeFormCheck: false,
          //（右上角）时间信息检测 --位置规范检测：
          timePositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。 右边距: min
          timePositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。 右边距: max
          timePositionCheckMax: null,
          //（右上角）时间信息检测 --准确性规则，
          timeDeviationCheck: false,
          //设备时间与标准时间允许偏差 XX 秒
          timeDeviationCheckValue: 0,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsArea: false,
          //（右下角）区划与地址信息检测 --位置规范检测：
          areaPositionCheck: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
          areaPositionCheckValueLine: false,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
          areaPositionCheckMin: null,
          //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
          areaPositionCheckMax: null,
          //地址信息只能占用一行
          areaPositionCheckLine: false,
          //（右下角）区划与地址信息检测 --准确性规则，
          areaDeviationCheck: false,
          // 省/市/区县、地点信息需与档案信息保持一致
          areaDeviationCheckValue: false,
          //队所（派出所）信息需标注准确（与组织机构表匹配）
          areaDeviationCheckLine: false,

          //（右下角）区划与地址信息检测 --是否在指定区域标注
          existsCamera: false,
          //准确性规则
          cameraDeviationCheck: false,
          //精确匹配2、综合匹配1
          areaDeviationCheckValueType: 1,
          // 【省级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeProvince: false,
          // 【地市级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeCity: false,
          // 【区县级】信息需与档案信息保持一致
          areaDeviationCheckValueTypeCounty: false,
          // 【地址】信息需与档案信息保持一致
          areaDeviationCheckValueTypeInfo: false,
        },
      },
      customFormData: {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      },
      detectionRules: [
        {
          label: '',
          text: '(右上角) 时间信息是否正确',
          value: 'timeLocation',
        },
        {
          label: '',
          text: '(右下角) 区划与地址信息是否正确',
          value: 'areaLocation',
        },
        {
          label: '',
          text: '(左下角) 摄像机信息是否正确',
          value: 'cameraInfo',
        },
      ],
      ruleCustom: {
        ocrModel: {
          validator: validateOcrModel,
          required: true,
          message: '请选择优先算法',
          trigger: 'change',
        },
        optimizationAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'optimizationAlgorithm',
        },
        standbyAlgorithmDetectContent: {
          validator: validateOptimizationAlgorithmDetectContent,
          required: true,
          trigger: 'change',
          triggerElement: 'standbyAlgorithm',
        },
        'reinspect.model': {
          required: true,
          message: '请选择复检设备',
          trigger: 'change',
        },
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      isRecheck: '2',
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      collapse: ['1', '2', '3'],
      needRecheckNumIndex: needRecheckNumIndex,
    };
  },

  methods: {
    setRecheck() {
      this.formData.reinspect ? (this.isRecheck = '1') : (this.isRecheck = '2');
    },
    isUpdatePhyStatus(indexType) {
      return isUpdatePhyStatus.filter((item) => item.indexType === indexType);
    },
    onChange751(val, key) {
      this.formData[key] = {
        ...this.formData[key],
        detectContent: val ? ['timeLocation', 'areaLocation'] : [],
        ...defaultDetectContent,
      };
    },
    setOcrModel() {
      // 删除无用字段 ocrModel ocrModel2
      delete this.formData.ocrModel;
      delete this.formData.ocrModel2;
      if (this.formData.optimizationAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.optimizationAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.optimizationAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.optimizationAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
      if (this.formData.standbyAlgorithm) {
        let { ocrModel, algorithmType, detectContent } = this.formData.standbyAlgorithm;
        let algorithmVendor = this.algorithmVendorData.find(
          (item) => item.algorithmVendorType === ocrModel && item.algorithmType === algorithmType,
        );
        this.customFormData.standbyAlgorithm = algorithmVendor
          ? `${algorithmVendor.algorithmType},${algorithmVendor.algorithmVendorType}`
          : '';
        this.customFormData.standbyAlgorithm751 = detectContent.length > 0 ? 1 : 0;
      }
    },
    /**
     *
     * @param val 算法值 "7,SW"
     * @param key 'optimizationAlgorithm' || 'standbyAlgorithm'
     */
    onChangeOcrModel(val, key) {
      let valList = val ? val.split(',') : [];
      let type = valList[0];
      let vendorType = valList[1];
      this.customFormData[`${key}751`] = 0;
      let { algorithmVendorType, algorithmType } =
        this.algorithmVendorData.find(
          (item) => item.algorithmType === type && item.algorithmVendorType === vendorType,
        ) || {};
      this.formData[key] = {
        ocrModel: algorithmVendorType || '',
        algorithmType: algorithmType || '',
        detectContent: [],
        ...this.defaultDetectContent,
      };
    },
    onChangeSnap() {
      this.formData.optimizationAlgorithm = {
        //优先算法
        ocrModel: '', //算法
        algorithmType: '', //算法类型
        detectContent: [], //检测内容
      };
      this.formData.standbyAlgorithm = {
        //备用算法
        ocrModel: '',
        algorithmType: '',
        detectContent: [],
      };
      this.customFormData = {
        optimizationAlgorithm: '',
        standbyAlgorithm: '',
        optimizationAlgorithm751: '',
        standbyAlgorithm751: '',
      };
    },
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        if (needRecheckNumIndex.includes(this.indexType)) {
          this.$set(this.formData.reinspect, 'maxCount', 1);
        }
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
      // this.formData.deviceQueryForm.dayByFilterOnline = this.formData.deviceQueryForm.dayByFilterOnline || null;
      // this.formData.deviceQueryForm.countByFilterOnline = this.formData.deviceQueryForm.countByFilterOnline || null;
    },
    async handleSubmit() {
      let modalDataValid = await this.$refs['formData'].validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      if (modalDataValid && commonFormValid) {
        let standbyAlgorithmDetectContent =
          this.$refs.standbyAlgorithmDetectContent && this.$refs.standbyAlgorithmDetectContent.getFormData();
        let optimizationAlgorithmDetectContent =
          this.$refs.optimizationAlgorithmDetectContent && this.$refs.optimizationAlgorithmDetectContent.getFormData();
        if (optimizationAlgorithmDetectContent) {
          this.formData.optimizationAlgorithm = {
            ...this.formData.optimizationAlgorithm,
            ...optimizationAlgorithmDetectContent.formData,
            detectContent: optimizationAlgorithmDetectContent.detectContent,
          };
        }
        if (standbyAlgorithmDetectContent) {
          this.formData.standbyAlgorithm = {
            ...this.formData.standbyAlgorithm,
            ...standbyAlgorithmDetectContent.formData,
            detectContent: standbyAlgorithmDetectContent.detectContent,
          };
        }
      }

      let detectContentValid = true;
      if (this.$refs.optimizationAlgorithmDetectContent) {
        detectContentValid = await this.$refs.optimizationAlgorithmDetectContent.handleSubmit();
      }
      let standDetectContentValid = true;
      if (this.$refs.standbyAlgorithmDetectContent) {
        standDetectContentValid = await this.$refs.standbyAlgorithmDetectContent.handleSubmit();
      }
      return modalDataValid && commonFormValid && detectContentValid && standDetectContentValid;
    },
    setDefaultTime() {
      this.formData.deviceQueryForm.dayByFilterOnline = this.formData.deviceQueryForm.dayByFilterOnline || null;
      this.formData.deviceQueryForm.countByFilterOnline = this.formData.deviceQueryForm.countByFilterOnline || null;
    },
    //设置默认值
    setDefaultEmphasisData(val) {
      if (val === 'deviceGather') {
        if (
          this.moduleAction.indexType === 'FACE_EMPHASIS_LOCATION' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultEmphasisData;
        }
      } else {
        this.formData.emphasisData = [];
      }
    },
  },
};
</script>

<style lang="less" scoped>
.vehicle-modal {
  margin: 20px;
  .capture-vehicle {
    margin-left: 40px;
  }
}
.input-width-number {
  width: 380px;
}
@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    margin-bottom: 10px;
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: var(--bg-collapse-item);
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: var(--color-collapse-arrow);
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
      .ivu-collapse-content-box {
        padding: 0;
      }
    }
  }
}
</style>
