<template>
  <div class="inline select-organization-tree">
    <Dropdown trigger="custom" :visible="visible" v-clickoutside="dropHide">
      <div class="ivu-select ivu-select-single t-left">
        <div class="ivu-select-selection" @click="dropShow">
          <div class="select-content">
            <span class="ivu-select-placeholder" v-if="selectedList.length <= 0">请选择组织机构</span>
            <span class="ivu-select-selected-value" :title="selectedList.map((row) => row.orgName)" v-else>
              <span v-for="(item, index) in selectedList" :key="index">
                {{ item.orgName }}
              </span>
            </span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow"></Icon>
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <div class="tree-title over-flow">
          <Checkbox class="fl" v-model="all" @on-change="checkedAll">
            <span class="table-text-content">全选</span>
          </Checkbox>
          <!-- <Button class="fr" type="primary" size="small" @click="query">
            确定
          </Button> -->
        </div>
        <el-tree
          class="tree"
          node-key="orgCode"
          ref="tree"
          :tree-style="treeStyle"
          :props="defaultProps"
          :data="treeList.length ? treeList : treeData"
          :expand-on-click-node="false"
          :show-checkbox="true"
          :default-expanded-keys="defaultKeys"
          :default-checked-keys="defaultCheckedKeys"
          :check-strictly="checkStrictly"
          @check="check"
        >
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
[data-theme='dark'] {
  .tree-title {
    border-bottom: 1px solid #2e4e65;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .tree-title {
    border-bottom: 1px solid #d3d7de;
  }
}

.select-organization-tree {
  width: 100%;
  .tree-title {
    padding: 5px 10px;
    line-height: 25px;
  }
  .tree {
    // min-width: 200px;
    overflow-x: auto;
    font-size: 12px;
    max-height: 280px;
    // margin-right: 10px;
  }
  .ivu-select-selection {
    // width: 305px;
    width: 100%;
    height: 34px;
    line-height: 34px;
    .select-content {
      height: 34px;
      line-height: 34px;
      .ivu-select-placeholder {
        height: 34px;
        line-height: 34px;
      }
      .ivu-select-selected-value {
        height: 34px;
        line-height: 34px;
      }
    }
  }
  @{_deep}.ivu-dropdown {
    width: 100%;
  }
  // @{_deep} .ivu-select-dropdown {
  //   // top: 0 !important;
  //   left: 0 !important;
  // }
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      visible: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      defaultKeys: [],
      selectedList: [], //选中的区域
      all: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    initCheck() {
      this.checkedAll(false);
    },
    // 选择checkbox触发
    check(data, checkData) {
      this.selectedList = checkData.checkedNodes;
      this.$emit('check', this.selectedList);
    },
    dropShow() {
      this.visible = !this.visible;
    },
    dropHide() {
      this.visible = false;
    },
    query() {
      this.$emit('check', this.selectedList);
      this.dropHide();
    },
    checkedAll(val) {
      let keys = [];
      let checkData = [];
      if (val) {
        this.isInitialOrgList.forEach((row) => {
          if (!row.disabled) {
            keys.push(row.orgCode);
          }
        });
      }
      this.$refs.tree.setCheckedKeys(keys);
      this.selectedList = this.isInitialOrgList;
      checkData = this.$refs.tree.getCheckedNodes(this.isInitialOrgList);
      this.selectedList = checkData || [];
      this.$emit('check', this.selectedList);
      this.dropHide();
    },
    getCheckedKeys(leafOnly = false) {
      return this.$refs.tree.getCheckedKeys(leafOnly);
    },
  },
  watch: {
    treeList(val) {
      !val.length ? (this.selectedList = []) : null;
    },
    defaultCheckedKeys() {
      this.$nextTick(() => {
        let checkedNodes = this.$refs.tree.getCheckedNodes();
        this.selectedList = checkedNodes;
      });
    },
    selectedList() {
      // 比较选中的第一级节点是否为全部
      let checkedTree = this.getCheckedKeys();
      let obj = {};
      checkedTree.forEach((row) => {
        obj[row] = row;
      });
      this.all = true;
      this.isInitialOrgList.forEach((row) => {
        if (!row.disabled && !obj[row.orgCode]) {
          this.all = false;
        }
        if (!obj[row.orgCode]) {
          this.all = false;
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      initialOrgList: 'common/getInitialOrgList',
    }),
    isInitialOrgList() {
      return this.flatTreeList.length ? this.flatTreeList : this.initialOrgList;
    },
  },
  props: {
    // 树结构style
    treeStyle: {},
    // 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 true
    checkStrictly: {
      default: false,
      type: Boolean,
    },
    // 默认选中的组织机构
    defaultCheckedKeys: {
      type: Array,
    },
    /**
     * 树默认是组织机构数据
     *
     * (自定义)传treeList
     * 替换树结构的数据
     */
    treeList: {
      type: Array,
      default: () => [],
    },
    flatTreeList: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
};
</script>
