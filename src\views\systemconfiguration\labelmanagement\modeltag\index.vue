<template>
  <div class="model-tag-container auto-fill height-full">
    <div class="modeltag-header flex-row">
      <div class="d_flex">
        <ui-label label="模型名称">
          <Input
            v-model="searchValue"
            placeholder="请输入模型名称"
            class="width-lg"
            @keydown.enter.native="getModelTagsList"
          />
        </ui-label>
        <Button type="primary" class="ml-lg" @click="getModelTagsList">查询</Button>
      </div>
      <Button type="primary" @click="handleCreateModel">
        <i class="icon-font icon-tianjia f-12 mr-sm"></i>新增模型</Button
      >
    </div>
    <div class="modeltag-main auto-fill mt-lg">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template slot="type" slot-scope="{ row }">
          <span>{{ row.type == '1' ? '设备' : '' }}</span>
        </template>
        <template slot="tag" slot-scope="{ row }">
          <span>{{ row.tagVo?.tagName || '-' }}</span>
        </template>
        <template slot="status" slot-scope="{ row }">
          <i-switch v-model="row.status" @on-change="changeStatus(row, row.status)">
            <span slot="open">开</span>
            <span slot="close">关</span>
          </i-switch>
        </template>
        <template slot="action" slot-scope="{ row }">
          <ui-btn-tip
            class="operatbtn mr-sm f-14 font-blue"
            icon="icon-bianji3"
            content="编辑"
            @click.native="handleUpdateModel(row)"
          >
          </ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <modify-modeltag
      v-model="modifyModelTagVisible"
      :all-device-tag-list="allDeviceTagList"
      :mode="modifyModelTagMode"
      :rowObj="rowObj"
      @modelTagVisibleChange="modelTagVisibleChange"
      @submitSuccess="getModelTagsList"
    ></modify-modeltag>
  </div>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
import { MODALTAG_COLUMN } from './util/enum.js';
export default {
  name: 'modeltag',
  data() {
    return {
      searchValue: '',
      tableColumns: MODALTAG_COLUMN,
      tableData: [],
      loading: false,

      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      allDeviceTagList: [],
      modifyModelTagMode: 'create',
      modifyModelTagVisible: false,
      rowObj: {},
      statusss: true,
    };
  },
  components: {
    ModifyModeltag: require('./components/modify-modeltag.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {},
  methods: {
    //获取所有标签，选择关联标签
    async getAllDeviceTagList() {
      try {
        const res = await this.$http.get(taganalysis.getAllDeviceTagList);
        this.allDeviceTagList = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    //获取模型标签列表
    async getModelTagsList() {
      try {
        this.loading = true;
        let params = {
          name: this.searchValue,
          pageSize: this.pageData.pageSize,
          pageNumber: this.pageData.pageNumber,
        };
        const res = await this.$http.post(taganalysis.tagModelDataQueryList, params);
        let data = res.data.data;
        this.pageData.totalCount = data.total;
        this.tableData = data.entities?.map((item) => {
          return {
            ...item,
            status: item.status == '1' ? true : false,
          };
        });
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    changePage() {
      this.getModelTagsList();
    },
    changePageSize() {
      this.pageData.pageNum = 1;
      this.getModelTagsList();
    },
    //点击新增模型
    handleCreateModel() {
      this.rowObj = {};
      this.modifyModelTagMode = 'create';
      this.modifyModelTagVisible = true;
    },
    modelTagVisibleChange(val) {
      this.modifyModelTagVisible = val;
    },
    //行内操作点击编辑
    handleUpdateModel(row) {
      this.rowObj = row;
      this.modifyModelTagMode = 'update';
      this.modifyModelTagVisible = true;
    },
    //切换状态
    async changeStatus(row, status) {
      try {
        let params = {
          id: row.id,
          status: status ? '1' : '0',
        };
        await this.$http.get(taganalysis.tagModelDataUpdateStatus, { params });
        this.$Message.success('切换成功');
      } catch (error) {
        let fIndex = this.tableData.findIndex((item) => (item.id = row.id));
        this.$set(this.tableData[fIndex], 'status', !status);
        console.log(error);
      }
    },
  },
  async mounted() {
    await this.getModelTagsList();
    this.getAllDeviceTagList();
  },
};
</script>
<style lang="less" scoped>
.model-tag-container {
  padding: 20px;
  background: var(--bg-content);
}
</style>
