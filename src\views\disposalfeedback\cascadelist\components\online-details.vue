<template>
  <div>
    <slot name="header"></slot>
    <draw-echarts
      v-on="$listeners"
      :echart-option="echartOption"
      :echart-style="echartStyle"
      :echarts-loading="echartsLoading"
      @echartMouseover="echartMouseover"
      @echartMouseout="echartMouseout"
    ></draw-echarts>
    <div id="info-box" v-show="showDetails">
      <div>{{ currentRow.checkTimeD }}</div>
      <p class="line">录像缺失时段：</p>
      <template v-for="(item, index) in currentRow.timerAxis">
        <div v-if="item?.state === 0" :key="index">{{ item.t1 }} - {{ item.t2 }}</div>
      </template>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    getEcharts: {
      type: Function,
    },
    data: {},
  },
  data() {
    return {
      styles: {
        width: '5.8rem',
      },
      echartStyle: {
        width: '860',
        height: '500px',
      },
      visible: false,
      echartsLoading: false,
      echartOption: {},
      showDetails: false,
      categoryData: [],
      currentRow: {},
    };
  },
  created() {
    this.setEcharts();
  },
  methods: {
    async setEcharts() {
      try {
        this.echartsLoading = true;
        const params = await this.getEcharts();
        this.categoryData = params.categoryData || [];
        /**
         * 参考
         * 1.https://blog.csdn.net/qq706352062/article/details/112605026
         * 2.http://chartlib.datains.cn/detail?id=b075ecbfdb3a48e08fbe0507c2f63afc
         */
        this.echartOption = this.$util.doEcharts.onlineDetailCompare(params);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    cancel() {
      this.visible = false;
    },
    echartMouseover(params) {
      if (params.componentType === 'yAxis' && params.targetType === 'axisLabel') {
        this.showDetails = true;
        this.currentRow = this.categoryData[params.dataIndex].rowInfo || {};
        this.$nextTick(() => {
          let tooltip = document.getElementById('info-box');
          tooltip.style.left = params.event.offsetX + 30 + 'px';
          tooltip.style.top = params.event.offsetY - tooltip.offsetHeight / 2 + 'px';
        });
      }
    },
    echartMouseout() {
      this.showDetails = false;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  #info-box {
    color: #ffffff;
    .line {
      border-bottom: 1px solid var(--devider-line);
    }
  }
}
@{_deep} .ivu-modal-body {
  padding: 0;
  .detail-title {
    display: flex;
    justify-content: right;
    margin: 0 50px 10px 50px;
  }
  .icon-ul {
    background-color: var(--bg-card-1);
    height: 50px;
    padding: 0 50px;
  }
  .echarts {
    padding: 0 50px;
  }
}
#info-box {
  position: absolute;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  padding: 15px 28px 15px 20px;
  width: 148px;
  background: var(--bg-tooltip);
  border-radius: 4px;
  box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
  .line {
    border-bottom: 1px solid #d8d8d8;
    padding-bottom: 5px;
    margin-bottom: 5px;
  }
}
</style>
