<template>
  <div class="place-management height-full">
    <div class="map-box">
      <map-base
        class="map-base"
        :is-edit="false"
        :camera-list="areaCameraList"
        :map-position="mapPosition"
        :geo-regions="geoRegions"
      ></map-base>
    </div>
    <div class="place-box height-full auto-fill">
      <Input
        class="mb-sm"
        v-model="searchData.keyWord"
        placeholder="请输入地名名称/编码/俗称"
        search
        enter-button
        @on-search="search"
      ></Input>
      <api-area-tree
        class="area-tree mb-sm"
        placeholder="请选择行政区划"
        :select-tree="selectAreaTree"
        @selectedTree="selectedAreaTree"
      ></api-area-tree>
      <div class="btn-div">
        <Button class="mb-sm mr-sm add-place" type="primary" @click="uploadPlace">
          <i class="icon-font icon-shangchuan mr-sm f-16"></i>
          <span class="inline vt-middle">上传场所</span>
        </Button>
        <Button class="mb-sm add-place" type="primary" @click="addPlace">
          <i class="icon-font icon-tianjia mr-sm f-14"></i>
          <span class="inline vt-middle">新增场所</span>
        </Button>
      </div>

      <div class="base-text-color">
        <span
          >共 <span class="font-red">{{ searchData.totalCount }}</span> 条
          <Tooltip content="滚动将加载更多数据" placement="top" transfer max-width="240">
            <i class="icon-font icon-wenhao"></i>
          </Tooltip>
        </span>
      </div>
      <Scroll
        class="place-list auto-fill"
        :on-reach-top="onReachTop"
        :on-reach-bottom="onReachBottom"
        v-ui-loading="{ loading: listLoading, tableData: placeList }"
      >
        <div
          class="place-item"
          v-for="(item, index) in placeList"
          :key="index"
          :class="{ active: item.id === selectPlace.id }"
          @click="viewPlace(item)"
        >
          <span>
            <i :class="['icon-font', 'mr-xs', item.status === 0 ? 'icon-weishangchuan' : 'icon-yishangchuan']"></i>
            <span class="inline vt-middle">{{ item.placeName }}</span>
          </span>
          <i v-if="item.status === 0" class="icon-font icon-shanchu2" @click.stop="deletePlace(item)"></i>
        </div>
      </Scroll>
    </div>

    <div class="place-message" v-ui-loading="{ loading: messageLoading }">
      <Form v-show="placeList.length !== 0 || isEdit" ref="formRef" :model="selectPlace" :rules="formRule">
        <FormItem label="区域图片" class="left-item" prop="placeImage">
          <upload-img
            v-if="isEdit"
            :multiple-num="1"
            :default-list="defaultList"
            @successPut="successPut"
            ref="upload"
          ></upload-img>
          <ui-image v-else class="upload-img" :src="selectPlace.placeImage" />
        </FormItem>
        <FormItem label="行政区划" class="left-item" prop="regionCode">
          <api-area-tree
            v-if="isEdit"
            class="api-area-tree"
            :select-tree="defaultTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区域"
          ></api-area-tree>
          <span v-else class="base-text-color">{{ selectPlace.regionName }}</span>
        </FormItem>
        <FormItem v-if="!!selectPlace.id" class="left-item" label="地名编码" prop="placeInternalCode">
          <Input
            v-if="isEdit"
            type="text"
            v-model="selectPlace.placeInternalCode"
            placeholder="请输入地名编码"
            class="width-form"
            :maxlength="20"
          ></Input>
          <span v-else class="base-text-color">{{ selectPlace.placeInternalCode }}</span>
        </FormItem>
        <FormItem label="地名名称" class="left-item" prop="placeName">
          <Input
            v-if="isEdit"
            type="text"
            v-model="selectPlace.placeName"
            placeholder="请输入地名名称"
            class="width-form"
            :maxlength="20"
          ></Input>
          <span v-else class="base-text-color">{{ selectPlace.placeName }}</span>
        </FormItem>
        <FormItem label="地名俗称" class="left-item" prop="placeAlias">
          <Input
            v-if="isEdit"
            type="text"
            v-model="selectPlace.placeAlias"
            placeholder="请输入地名俗称"
            class="width-form"
            :maxlength="20"
          ></Input>
          <span v-else class="base-text-color">{{ selectPlace.placeAlias }}</span>
        </FormItem>
        <FormItem label="区域类型" class="left-item" prop="sbcjqyList">
          <Button v-if="isEdit" type="dashed" class="area-btn" @click="selectSbcjqy"
            >请选择采集区域 {{ `已选择 ${selectPlace.sbcjqyList.length}个` }}</Button
          >
          <span v-else class="base-text-color inline width-sm ellipsis" :title="selectPlace.sbcjqyText">{{
            selectPlace.sbcjqyText
          }}</span>
        </FormItem>
        <FormItem label="详细地址" class="left-item" prop="placeStandardAddress">
          <Input
            v-if="isEdit"
            type="textarea"
            class="width-form"
            v-model="selectPlace.placeStandardAddress"
            placeholder="请输入详细地址"
            :rows="2"
            :autosize="{ minRows: 2, maxRows: 3 }"
            :maxlength="100"
          ></Input>
          <span v-else class="base-text-color">{{ selectPlace.placeStandardAddress }}</span>
        </FormItem>
        <FormItem label="经度" class="left-item" prop="longitude">
          <InputNumber
            v-if="isEdit"
            type="number"
            class="width-form"
            v-model="selectPlace.longitude"
            placeholder="请输入经度"
            :min="0"
            :max="999999999"
          ></InputNumber>
          <span v-else class="base-text-color">{{ selectPlace.longitude }}</span>
        </FormItem>
        <FormItem label="纬度" class="left-item" prop="latitude">
          <InputNumber
            v-if="isEdit"
            type="number"
            class="width-form"
            v-model="selectPlace.latitude"
            placeholder="请输入纬度"
            :min="0"
            :max="999999999"
          ></InputNumber>
          <span v-else class="base-text-color">{{ selectPlace.latitude }}</span>
        </FormItem>
        <FormItem label=" " prop="position" class="left-item">
          <div class="place-select" @click="selectedPlace">
            <span v-if="!selectPlace.deviceInfoVoList || selectPlace.deviceInfoVoList.length === 0">采集区域范围</span>
            <span v-else>该区域{{ selectPlace.deviceInfoVoList.length }}个设备</span>
          </div>
        </FormItem>
        <FormItem label=" " class="left-item">
          <Button v-if="isEdit" type="primary" @click="query" :loading="saveLoading">保存</Button>
          <Button v-else type="primary" @click="editPlace">编辑</Button>
          <Button v-if="isEdit" class="ml-sm" @click="cancel">取消</Button>
        </FormItem>
      </Form>
      <div class="no-data" v-show="placeList.length === 0 && !isEdit">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
        <span class="null-data-text">暂无数据</span>
      </div>
    </div>
    <select-place-modal
      v-model="cameraMapShow"
      :map-position="mapPosition"
      :default-device-list="selectPlace.deviceInfoVoList"
      :is-edit="isEdit"
      :geo-regions="geoRegions"
      @putPlaceMessage="putPlaceMessage"
    ></select-place-modal>
    <upload-place-modal v-model="uploadShow" @putChoose="putChoose"></upload-place-modal>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checked-tree-data-list="checkedTreeData"
    ></area-select>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'placemanagement',
  props: {},
  data() {
    return {
      isEdit: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      geoRegions: [],
      messageLoading: false,
      listLoading: false,
      selectAreaTree: {
        regionCode: '',
      },
      searchData: {
        keyWord: '',
        regionCode: '',
        pageNumber: 1,
        pageSize: 50,
        totalCount: 0,
        firstPage: false,
        lastPage: false,
      },
      tableData: [],
      placeList: [],
      defaultList: [],
      areaCameraList: [],
      selectPlace: {
        placeImage: '',
        regionCode: '',
        regionName: '',
        placeInternalCode: '',
        placeName: '',
        placeAlias: '',
        sbcjqyList: [],
        placeTypeName: '',
        placeStandardAddress: '',
        deviceInfoVoList: [],
        longitude: null,
        latitude: null,
        geometry: '',
      },
      formRule: {
        regionCode: [{ required: true, message: '请选择行政区域', trigger: 'blur' }],
        placeInternalCode: [{ required: true, message: '请输入地名编码', trigger: 'blur' }],
        placeName: [{ required: true, message: '请输入地名名称', trigger: 'blur' }],
        placeAlias: [{ required: true, message: '请输入地名俗称', trigger: 'blur' }],
        sbcjqyList: [
          {
            required: true,
            message: '请选择采集区域',
            trigger: 'blur',
            type: 'array',
          },
        ],
        longitude: [
          {
            required: true,
            message: '请输入经度',
            trigger: 'blur',
            type: 'number',
          },
        ],
        latitude: [
          {
            required: true,
            message: '请输入纬度',
            trigger: 'blur',
            type: 'number',
          },
        ],
      },
      defaultTree: {
        regionCode: '',
      },
      saveLoading: false,
      cameraMapShow: false,
      mapPosition: {},
      uploadShow: false,
    };
  },
  mounted() {},
  activated() {
    !!this.defaultSelectedArea && this.init();
  },
  methods: {
    selectedAreaTree(val) {
      this.searchData.regionCode = val.regionCode;
      this.getGeoRegions(val.regionCode);
      this.search();
    },
    async getGeoRegions(code) {
      try {
        let { data } = await this.$http.get(`/json/map-china-config/${code}.json`);
        this.geoRegions = data.features.map((row) => {
          // fixed:这里来处理geoJSON中的properties存在center会导致地图sdk中报错
          if (row.properties.center) {
            this.$set(row.properties, 'cp', row.properties.center);
            delete row.properties.center;
          }
          return row;
        });
      } catch (err) {
        console.log(err, 'err');
      }
    },
    setDefaultArea() {
      if (this.$route.query.regionCode) {
        this.selectAreaTree.regionCode = this.$route.query.regionCode;
      } else {
        this.selectAreaTree.regionCode = this.defaultSelectedArea.regionCode;
      }
      this.searchData.regionCode = this.selectAreaTree.regionCode;
      this.getGeoRegions(this.searchData.regionCode);
    },
    setDefaultPlace() {
      if (this.$route.query.regionCode) {
        let place = this.placeList && this.placeList.find((item) => item.id == this.$route.query.placeId);
        if (!place) return this.$Message.error('场所不存在');
        this.viewPlace(place);
      } else {
        !!this.placeList.length && this.viewPlace(this.placeList[0]);
      }
    },
    async init() {
      this.setDefaultArea();
      this.listLoading = true;
      await this.initPlaceList();
      this.listLoading = false;
      this.placeList = [...this.tableData];
      this.setDefaultPlace();
    },
    async initPlaceList() {
      try {
        const res = await this.$http.post(equipmentassets.placeManagerList, this.searchData);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
        this.searchData.firstPage = res.data.data.firstPage;
        this.searchData.lastPage = res.data.data.lastPage;
      } catch (err) {
        console.log(err);
      }
    },
    async onReachTop() {
      try {
        if (this.searchData.firstPage) return;
        const bool = this.placeList.length / this.searchData.pageSize > 3;
        // 如果页码大于3或数据大于3倍pageSize才进行数据更改
        if (this.searchData.pageNumber > 3 || bool) {
          this.searchData.pageNumber--;
          await this.initPlaceList();
          this.placeList.splice(this.placeList.length - this.searchData.pageSize - 1, this.placeList.length - 1);
          this.placeList = [...this.tableData, ...this.placeList];
          !!this.placeList.length && this.viewPlace(this.placeList[0]);
        }
      } catch (err) {
        console.log(err);
      }
    },
    async onReachBottom() {
      try {
        if (this.searchData.lastPage) return;
        this.searchData.pageNumber++;
        await this.initPlaceList();
        const bool = this.placeList.length / this.searchData.pageSize > 3;
        bool && this.placeList.splice(0, this.searchData.pageSize - 1);
        this.placeList = [...this.placeList, ...this.tableData];
        !!this.placeList.length && this.viewPlace(this.placeList[0]);
      } catch (err) {
        console.log(err);
      }
    },
    async search() {
      this.searchData.pageNumber = 1;
      await this.initPlaceList();
      this.placeList = [...this.tableData];
      if (this.placeList.length) {
        this.viewPlace(this.placeList[0]);
      } else {
        this.selectPlace = {
          placeImage: '',
          regionCode: '',
          regionName: '',
          placeInternalCode: '',
          placeName: '',
          placeAlias: '',
          sbcjqyList: [],
          placeStandardAddress: '',
          deviceInfoVoList: [],
          longitude: null,
          latitude: null,
          geometry: '',
        };
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.initPlaceList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    async viewPlace(item) {
      try {
        this.messageLoading = true;
        this.isEdit = false;
        this.$refs.formRef.resetFields();
        const res = await this.$http.get(equipmentassets.viewPlace, {
          params: { id: item.id },
        });
        this.selectPlace = res.data.data;
        res.data.data.position ? (this.mapPosition = JSON.parse(res.data.data.position)) : (this.mapPosition = {});
        this.areaCameraList = res.data.data.deviceInfoVoList;
      } catch (err) {
        console.log(err);
      } finally {
        this.messageLoading = false;
      }
    },
    editPlace() {
      this.isEdit = true;
      this.$nextTick(() => {
        this.defaultList = [this.selectPlace.placeImage];
        this.defaultTree.regionCode = this.selectPlace.regionCode;
      });
    },
    cancel() {
      if (this.placeList.length) {
        this.viewPlace(this.placeList[0]);
      } else {
        this.selectPlace = {
          placeImage: '',
          regionCode: '',
          regionName: '',
          placeInternalCode: '',
          placeName: '',
          placeAlias: '',
          sbcjqyList: [],
          placeStandardAddress: '',
          deviceInfoVoList: [],
          longitude: null,
          latitude: null,
          geometry: '',
        };
      }
      this.isEdit = false;
    },
    addPlace() {
      this.modalData = {
        action: 'add',
        title: '新增场所',
      };
      const region = this.initialAreaList.find((row) => row.regionCode === this.selectAreaTree.regionCode);
      this.selectPlace = {
        placeImage: '',
        regionCode: this.selectAreaTree.regionCode,
        regionName: region.regionName,
        placeInternalCode: '',
        placeName: '',
        placeAlias: '',
        sbcjqyList: [],
        placeStandardAddress: '',
        deviceInfoVoList: [],
        longitude: null,
        latitude: null,
        geometry: '',
      };
      this.defaultTree.regionCode = this.selectAreaTree.regionCode;
      this.defaultList = [];
      this.mapPosition = {};
      this.isEdit = true;
    },
    uploadPlace() {
      this.uploadShow = true;
    },
    deletePlace(item) {
      this.$UiConfirm({
        content: `您将要删除区域 ${item.placeName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteInit(item);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteInit(item) {
      try {
        const res = await this.$http.post(equipmentassets.removePlace, {
          ids: [item.id],
        });
        this.$Message.success(res.data.msg);
        this.init();
        this.mapPosition = {};
      } catch (err) {
        console.log(err);
      }
    },
    async putChoose(list) {
      try {
        const deviceIds = list.map((row) => row.id);
        const res = await this.$http.post(equipmentassets.uploadPlace, {
          ids: deviceIds,
        });
        this.uploadShow = false;
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    successPut(list) {
      this.selectPlace.placeImage = list[0];
    },
    putPlaceMessage(deviceList, mapPosition) {
      if (Object.keys(mapPosition).length) {
        this.selectPlace.position = JSON.stringify(mapPosition);
        this.selectPlace.longitude = mapPosition.center.lon;
        this.selectPlace.latitude = mapPosition.center.lat;
        const geometry = [];
        mapPosition.points.forEach((row) => {
          geometry.push(`${row.lat} ${row.lon}`);
        });
        this.selectPlace.geometry = `POLYGON((${geometry}))`;
      }
      this.selectPlace.deviceInfoVoList = deviceList;
    },
    selectedArea(area) {
      this.selectPlace.regionCode = area.regionCode;
      this.selectPlace.regionName = area.regionName;
    },
    query() {
      this.$refs['formRef'].validate((valid) => {
        if (valid) {
          this.saveLoading = true;
          let params = {
            deviceIds: this.selectPlace.deviceInfoVoList
              ? this.selectPlace.deviceInfoVoList.map((row) => row.id).join(';')
              : '',
          };
          Object.assign(this.selectPlace, params);
          let url = this.selectPlace.id ? equipmentassets.updatePlace : equipmentassets.addPlace;
          let method = this.selectPlace.id ? 'put' : 'post';
          this.$http[method](url, this.selectPlace)
            .then((res) => {
              this.saveLoading = false;
              this.isEdit = false;
              this.search();
              this.$Message.success(res.data.msg);
            })
            .catch((err) => {
              this.saveLoading = false;
              console.log(err);
            });
        } else {
          this.$Message.error('请根据规则填写');
        }
      });
    },
    selectedPlace() {
      this.cameraMapShow = true;
    },
    selectSbcjqy() {
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.selectPlace.sbcjqyList || [];
    },
    confirmArea(data) {
      this.selectPlace.sbcjqyList = data;
    },
  },
  watch: {
    defaultSelectedArea: {
      handler(val) {
        !!val && this.init();
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedArea: 'common/getDefaultSelectedArea',
      initialAreaList: 'common/getInitialAreaList',
    }),
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    MapBase: require('@/components/map-base/index.vue').default,
    UploadImg: (resolve) => {
      require(['@/components/upload-img'], resolve);
    },
    SelectPlaceModal: require('./components/select-place-modal.vue').default,
    UploadPlaceModal: require('./components/upload-place-modal.vue').default,
    AreaSelect: require('@/components/area-select').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .place-item {
    color: var(--color-content) !important;
    &:hover {
      background-color: #d5e7fe !important;
    }
    &.active {
      background-color: var(--color-primary) !important;
      color: #ffffff !important;
      i {
        color: #fff !important;
      }
    }
  }
}
.place-management {
  background-color: var(--bg-content);
  .add-place {
    padding: 0 15px;
  }
  .place-box {
    width: 300px;
    padding: 10px 20px 10px 20px;
    border-right: 1px solid var(--border-color);
    margin-left: -100%;
    float: left;
    .area-tree {
      @{_deep}.ivu-select {
        width: 259px;
      }
      @{_deep}.ivu-select-dropdown {
        width: 259px;
      }
    }
    .btn-div {
      display: flex;
      justify-content: space-between;
    }
    .place-list {
      width: 100%;
      @{_deep} .ivu-scroll-container {
        height: 100% !important;
        overflow-y: auto;
      }
      .place-item {
        color: #fff;
        padding: 5px 10px;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &:hover {
          background-color: #041129;
        }
        &.active {
          background-color: #184f8d;
        }
        .icon-weishangchuan {
          color: #99a4af;
        }
        .icon-yishangchuan {
          color: #269f26;
        }
        .icon-shanchu2 {
          color: #99a4af;
          &:hover {
            color: #fff;
          }
        }
      }
    }
  }
  .map-box {
    float: left;
    width: 100%;
    height: 100%;
    .map-base {
      margin: 0 330px 0 300px;
    }
  }
  .place-message {
    width: 330px;
    height: 100%;
    border-left: 1px solid var(--border-color);
    float: left;
    padding: 10px 10px;
    margin-left: -330px;
    .left-item {
      @{_deep} .ivu-form-item-error-tip {
        margin-left: 90px;
      }
      @{_deep}.ivu-form-item-label {
        width: 90px;
      }
    }
    .upload-img {
      float: left;
      width: 120px;
      height: 120px;
    }
    @{_deep}.ivu-form-item-label {
      color: var(--color-form-label) !important;
    }
    .api-area-tree {
      @{_deep}.ivu-dropdown {
        width: 180px;
      }
      @{_deep}.ivu-select {
        width: 180px;
      }
    }
    @{_deep}.ivu-input-number {
      width: 100%;
    }
    .api-place-type-tree {
      @{_deep}.ivu-dropdown {
        width: 180px;
      }
      @{_deep}.ivu-select-selection {
        width: 180px;
      }
    }
    .ui-tree-select {
      width: 180px;
      @{_deep} .ivu-select-dropdown {
        width: 270px;
      }
    }
    .width-form {
      width: 180px;
    }
    .place-select {
      width: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid var(--border-input);
      color: var(--color-content);
      cursor: pointer;
    }
    .no-data {
      height: 80px;
      width: 80px;
      color: var(--color-primary);
      font-size: 14px;
      .null-data-text {
        color: var(--color-no-data-img);
        font-size: 16px;
        line-height: 1.5;
      }
    }
  }
}
</style>
