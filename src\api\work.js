import request from "@/libs/request";
import { manager } from "./Microservice";

// 工单对接
// 获取海康免密token接口
export function generateUserToken(userId) {
  return request({
    url: manager + `/work/order/generateUserToken/${userId}`,
    method: "get",
  });
}

// 根据国标id获取资源内码
export function getResourceId(gbId) {
  return request({
    url: manager + `/work/order/getResourceId/${gbId}`,
    method: "get",
  });
}
