<template>
  <ui-modal
    v-model="visible"
    :title="`${showOnly ? '' : '选择'}采集区域类型`"
    :footerHide="showOnly"
    :styles="styles"
    class="ui-modal"
    @query="query"
  >
    <div class="area-container">
      <div class="tree-filter" v-show="!showOnly">
        <ui-label label="关键词" :width="50" class="inline mr-lg">
          <Input v-model="keyWord" class="width-lg" clearable placeholder="请输入区域名称或编码"></Input>
        </ui-label>
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetForm">重置</Button>
      </div>
      <div :class="['tree-wrapper', showOnly ? 'h560' : 'h500']">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          no-search
          node-key="code"
          :tree-data="treeData"
          :default-props="defaultProps"
          :expand-all="expandAll"
          :default-keys="checkedTreeDataList"
          checkStrictly
        >
          <template #label="{ node, data }">
            <Checkbox
              v-model="data.check"
              class="mr-sm"
              v-show="!data.children && !showOnly"
              :disabled="data.disabled"
              @on-change="check($event, node, data)"
            ></Checkbox>
            <span>{{ data.code }}</span>
            <span>{{ data.name }}</span>
            <Button
              type="text"
              @click="checkAll(node, data)"
              v-show="data.level === 2 && !showOnly"
              class="fr mr-sm btn-mini"
              >{{ `${data.checkAll ? '取消' : '全选'}` }}</Button
            >
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
/**
 * @example
 * AreaSelect: require("@/components/area-select").default,
 * <area-select v-model="areaSelectModalVisible" @confirm="confirmArea" :checkedTreeDataList="checkedTreeData"></area-select>
 */
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'area-select',
  components: {
    UiSearchTree: require('@/components/ui-search-tree').default,
  },
  props: {
    value: {},
    //默认选中且禁用
    defaultCheckedKeys: {
      type: Array,
      default: () => [],
    },
    /**
     * ['A0101','A0102]
     */
    checkedTreeDataList: {
      required: true,
      default: () => [],
    },
    //当dataSource 为true时生效
    data: {
      default: () => [],
    },
    /**
     * 数据源 默认为 false
     * false 使用组件内data
     * true 使用传入的data
     */
    dataSource: {
      type: Boolean,
      default: false,
    },
    /**
     * 仅作为查看组件
     */
    showOnly: {
      type: Boolean,
      default: false,
    },
    expandAll: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      styles: {
        width: '5rem',
      },
      treeData: [],
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
      keyWord: '',
      initData: [],
      checkedTreeDataWithName: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        if (!this.dataSource) {
          await this.initTree();
          await this.setCheckedKeys();
          this.treeData = this.$util.common.arrayToJson(
            this.$util.common.deepCopy(this.initData),
            'code',
            'parentCode',
          );
        }
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: async function (val) {
        if (this.dataSource) {
          this.initData = this.$util.common.deepCopy(val);
          await this.setCheckedKeys();
          this.treeData = this.$util.common.arrayToJson(
            this.$util.common.deepCopy(this.initData),
            'code',
            'parentCode',
          );
        }
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    resetForm() {
      this.keyWord = '';
      this.search();
    },
    checkAll(node, data) {
      this.$set(data, 'checkAll', !data.checkAll);
      if (data.children) {
        data.children.map((item) => {
          if (item.disabled) return;
          this.$set(item, 'check', data.checkAll);
        });
      }
    },
    check(val, data) {
      this.$set(data, 'check', val);
    },
    query() {
      this.checkedTreeData = [];
      this.checkedTreeDataWithName = [];
      this.getCheckedKeys(this.treeData);
      this.visible = false;
      this.$emit('confirm', this.checkedTreeData, this.checkedTreeDataWithName);
    },
    getCheckedKeys(data) {
      data.map((item) => {
        if (item.check) {
          this.checkedTreeData.push(item.code);
          this.checkedTreeDataWithName.push({
            key: item.code,
            value: item.name,
          });
        }
        if (item.children) {
          this.getCheckedKeys(item.children);
        }
      });
    },
    setCheckedKeys() {
      this.initData.forEach((item) => {
        this.$set(item, 'check', false);
        let checkedKey = this.checkedTreeDataList.find((code) => code === item.code);
        let defaultKey = this.defaultCheckedKeys.find((key) => key === item.code);
        if (checkedKey || defaultKey) {
          this.$set(item, 'check', true);
        }
        if (defaultKey) {
          this.$set(item, 'disabled', true);
        }
      });
    },
    resetInitData() {
      this.initData.map((item) => {
        this.$set(item, 'check', false);
      });
    },
    search() {
      this.$refs.uiTree.$refs.uiTree.filter(this.keyWord.trim().toUpperCase());
    },
    async initTree() {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getDeviceSbcjqyData);
        this.initData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import 'index';
</style>
