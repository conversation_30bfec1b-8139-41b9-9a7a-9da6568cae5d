<template>
  <section class="search-module">
    <ui-label class="inline mr-lg" label="问题名称">
      <Input class="width-md" clearable v-model="searchForm.name" placeholder="请填写问题名称"></Input>
    </ui-label>
    <ui-label class="inline mr-lg" label="问题类型">
      <Select v-model="searchForm.indexModule" placeholder="请选择问题类型" class="width-md" clearable>
        <Option v-for="(item, index) in indexModuleList" :key="index" :value="item.dicKey">{{ item.dicValue }}</Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg" label="处理状态">
      <Select v-model="searchForm.handleStatus" placeholder="请选择指标" class="width-md" clearable>
        <Option v-for="(item, index) in handleStatusList" :key="index" :value="item.dicKey">{{ item.dicValue }}</Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg" label="下发状态" v-if="isSuperior">
      <Select v-model="searchForm.publishStatus" placeholder="请选择指标" class="width-md" clearable>
        <Option v-for="(item, index) in publishStatusList" :key="index" :value="item.dicKey">{{
          item.dicValue
        }}</Option>
      </Select>
    </ui-label>
    <ui-label class="inline" label="">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetForm">重置</Button>
    </ui-label>
    <slot></slot>
  </section>
</template>
<script>
import { INDEXMODULELIST, HANDLESTATUSLIST, PUBLISHSTATUSLIST } from './util/enum';
export default {
  props: {
    isSuperior: {},
  },
  data() {
    return {
      searchForm: {
        handleStatus: '',
        indexModule: '',
        name: '',
        publishStatus: '',
      },
      indexModuleList: INDEXMODULELIST,
      publishStatusList: PUBLISHSTATUSLIST,
      handleStatusList: HANDLESTATUSLIST,
    };
  },
  created() {
    this.copySearchDataMx(this.searchForm);
  },
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchForm);
    },
    resetForm() {
      this.$emit('reset', this.searchForm);
      this.resetSearchDataMx(this.searchForm, this.startSearch);
    },
  },
  watch: {
    isSuperior() {
      this.resetSearchDataMx(this.searchForm);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.search-module {
}
</style>
