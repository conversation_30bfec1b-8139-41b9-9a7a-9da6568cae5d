// slideLeft 指令 绑定在触发滑动事件的按钮上 滑动隐藏的为它的父元素
// 例如：v-slide = "20" 会加上value滑动更宽的距离,不输入值则为父元素的距离
export default function (Vue) {
  Vue.directive('slide', {
    inserted (el, binding, vnode) {
      const bind = binding.value ? binding : { value: 0 }
      const fn = () => {
        binding.def.toggle(el, bind)
      }
      el.addEventListener('click', fn)
    },
    toggle (el, binding) {
      if (el.classList.contains('hidden')) {
        el.classList.remove('hidden')
        Object.assign(el.parentElement.style, {
          transform: '',
          transition: `transform .6s ease`
        })
      } else {
        el.classList.add('hidden')
        Object.assign(el.parentElement.style, {
          transform: `translateX(${el.parentElement.offsetWidth - binding.value}px)`,
          transition: `transform .6s ease`
        })
      }
    }
  })
}
