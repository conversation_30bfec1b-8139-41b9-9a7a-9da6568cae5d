/**
 * slide 指令 绑定在触发滑动事件的按钮上 滑动隐藏的为它的父元素
 * 如果默认不展出--绑定指令的父元素加上hidden 类名
 * @param {width} 例如：v-slide = "{ width: 20 }" 会加上value滑动更宽的距离,不输入值则为父元素的距离
 * @param {direction} 例如：v-slide = "{ direction: 'right}" 方向为向右边隐藏,不输入则为从左边滑出
 * @param {callBack} 例如：v-slide = "{ callBack: func }" 滑动的之后调用的回调函数
 */

export default function (Vue) {
  Vue.directive('slide', {
    inserted(el, binding) {
      let values = binding.value;
      if (!values || !values.hasOwnProperty('width')) {
        values.width = 0;
      }
      let fn = () => binding.def.toggle(el, values);
      // 父元素加上类名hidden，就默认隐藏起来
      if (el.classList.contains('hidden')) {
        Object.assign(el.parentElement.style, {
          transform: _handleDirection(el, values),
        });
      }
      el.addEventListener('click', fn);
    },
    toggle(el, values) {
      // 传入回调函数，滑动的时候执行
      if (values.callBack) {
        values.callBack(el.classList.contains('hidden'));
      }
      if (el.classList.contains('hidden')) {
        el.classList.remove('hidden');
        Object.assign(el.parentElement.style, {
          transform: '',
          'transition-property': 'transform,width',
          'transition-duration': '.6s,.2s',
        });
      } else {
        el.classList.add('hidden');
        Object.assign(el.parentElement.style, {
          transform: _handleDirection(el, values),
          //transition: `transform .6s ease`,
          'transition-property': 'transform,width',
          'transition-duration': '.6s,6s',
        });
      }
    },
  });
}

function _handleDirection(el, values) {
  !values.hasOwnProperty('direction') ? (values.direction = 'left') : null;
  if (values.direction === 'right') {
    return `translateX(${el.parentElement.offsetWidth + values.width}px)`;
  } else {
    return `translateX(${-el.parentElement.offsetWidth - values.width}px)`;
  }
}
