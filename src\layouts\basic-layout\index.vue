<template>
  <Layout class="i-layout">
    <Sider
      v-show="!noMenu"
      :class="siderClasses"
      :width="menuSideWidth"
      class="i-layout-sider"
    >
      <i-menu-side :hide-logo="isHeaderStick && headerFix && showHeader" />
      <div v-if="filterSider.length > 1" class="slider_collapse">
        <i-header-collapse @on-toggle-drawer="handleToggleDrawer" />
      </div>
    </Sider>
    <Layout :class="insideClasses" class="i-layout-inside">
      <transition name="fade-quick">
        <Header
          v-show="showHeader && !noMenu"
          :class="headerClasses"
          class="i-layout-header"
          style="width: 100%"
        >
          <i-header-logo />
          <!--<i-header-collapse @on-toggle-drawer="handleToggleDrawer"/>-->
          <i-menu-head ref="menuHead" />
          <i-header-project />
          <ui-icon
            @click.native="changeTheme"
            type="pifu"
            :size="20"
            :color="'#fff'"
          ></ui-icon>
          <!-- <Button @click="changeTheme">换肤</Button> -->
          <i-header-user />
          <i-header-setting
            @setting="handleSetting"
            v-if="isBaseApp"
          ></i-header-setting>
          <Divider type="vertical" v-if="isBaseApp" />
          <div class="i-largeScreen-header-right" v-if="isBaseApp">
            <i-large-screen></i-large-screen>
          </div>
          <!-- <div class="i-layout-header-right">
            <i-header-logout />
          </div> -->
        </Header>
      </transition>
      <!-- <Content :class="{contentClasses, indexStyle: indexStyle}" class="i-layout-content"> -->
      <Content :class="contentClasses" class="i-layout-content">
        <transition name="fade-quick">
          <i-tabs v-if="tabs" v-show="showHeader && !noMenu" />
        </transition>
        <div
          class="i-layout-content-main"
          :class="{ 'no-padding': isNoPadding, 'no-padding2': noPadding }"
        >
          <router-view :key="$route.name" />
        </div>
      </Content>
    </Layout>
    <ui-modal
      v-model="visible"
      title="个性化设置"
      :r-width="630"
      @onOk="comfirmHandle"
      @onCancel="onCancel"
    >
      <div class="content">
        <ul>
          <li>
            <p class="form-title">视频播放默认画面比例:</p>
            <div class="form-wrapper">
              <RadioGroup v-model="individuationConfig.videoRatio">
                <Radio
                  v-for="(item, index) in videoList"
                  :key="index"
                  :label="item.label"
                  >{{ item.value }}</Radio
                >
              </RadioGroup>
            </div>
          </li>
          <li class="mt-20 device-icons">
            <p class="form-title">自定义监控图标:</p>
            <div class="form-wrapper">
              <div class="form-item">
                <div class="label mr-10">枪机</div>
                <div
                  class="icons qiangji"
                  :style="`--qiangjiColor: ${individuationConfig.deviceIcons.qiangji.color}`"
                >
                  <i
                    class="iconfont mr-5"
                    v-for="item in qiangjiList"
                    :key="item.className"
                    :class="[
                      item.className,
                      individuationConfig.deviceIcons.qiangji.className ==
                      item.className
                        ? 'selected'
                        : '',
                    ]"
                    @click="
                      individuationConfig.deviceIcons.qiangji.className =
                        item.className
                    "
                  />
                </div>
                <ColorPicker
                  size="small"
                  v-model="individuationConfig.deviceIcons.qiangji.color"
                />
              </div>
              <div class="form-item">
                <div class="label mr-10">球机</div>
                <div
                  class="icons qiuji"
                  :style="`--qiujiColor: ${individuationConfig.deviceIcons.qiuji.color}`"
                >
                  <i
                    class="iconfont mr-5"
                    v-for="item in qiujiList"
                    :key="item.className"
                    :class="[
                      item.className,
                      individuationConfig.deviceIcons.qiuji.className ==
                      item.className
                        ? 'selected'
                        : '',
                    ]"
                    @click="
                      individuationConfig.deviceIcons.qiuji.className =
                        item.className
                    "
                  />
                </div>
                <ColorPicker
                  size="small"
                  v-model="individuationConfig.deviceIcons.qiuji.color"
                />
              </div>
            </div>
          </li>
          <li class="mt-20">
            <p class="form-title">时空分析设备弹框数量:</p>
            <div class="form-wrapper">
              <RadioGroup v-model="individuationConfig.deviceDomNum">
                <Radio
                  v-for="(item, index) in deviceDomNumList"
                  :key="index"
                  :label="item.label"
                  >{{ item.value }}</Radio
                >
              </RadioGroup>
            </div>
          </li>
          <li class="mt-20">
            <p class="form-title">时空分析点位屏蔽:</p>
            <div class="form-wrapper">
              <CheckboxGroup
                v-model="individuationConfig.hideDeviceTypes"
                class="check-group"
              >
                <div v-for="item in deviceTypeList" :key="item.value">
                  <Checkbox :label="item.value">{{ item.label }}</Checkbox>
                </div>
              </CheckboxGroup>
              <Checkbox v-model="individuationConfig.hideCommunity"
                >社会资源点位屏蔽</Checkbox
              >
            </div>
          </li>
          <li class="mt-20">
            <p class="form-title">报警推送:</p>
            <div class="form-wrapper">
              <Checkbox v-model="individuationConfig.alarmConfig"
                >推送</Checkbox
              >
            </div>
          </li>
          <li class="mt-20">
            <p class="form-title">菜单是否默认收起:</p>
            <div class="form-wrapper">
              <Checkbox v-model="individuationConfig.menuConfig">收起</Checkbox>
            </div>
          </li>
        </ul>
      </div>
    </ui-modal>
  </Layout>
</template>
<script>
import iMenuHead from "./menu-head";
import iMenuSide from "./menu-side";
import iHeaderLogo from "./header-logo";
import iHeaderUser from "./header-user";
import iHeaderProject from "./header-project";
import iHeaderLogout from "./header-logout";
import iHeaderCollapse from "./header-collapse";
import iHeaderSetting from "./header-setting";
import iLargeScreen from "./large-screen";
import iTabs from "./tabs";
import { mapState, mapGetters, mapMutations } from "vuex";
import Setting from "@/libs/configuration/setting";
import { requestAnimation } from "@/libs/configuration/util";
import setTheme from "@/libs/theme";
import { settingQuery, settingAdd, settingUpdate } from "@/api/user";
import { getRouteDomain } from "@/api/monographic/base";
export default {
  name: "BasicLayout",
  components: {
    iMenuHead,
    iMenuSide,
    iHeaderLogo,
    iHeaderCollapse,
    iTabs,
    iHeaderLogout,
    iHeaderUser,
    iHeaderProject,
    iHeaderSetting,
    iLargeScreen,
  },
  data() {
    return {
      isBaseApp: !getRouteDomain(),
      showDrawer: false,
      ticking: false,
      headerVisible: true,
      oldScrollTop: 0,
      isDelayHideSider: false, // hack，当从隐藏侧边栏的 header 切换到正常 header 时，防止 Logo 抖动
      indexStyle: true,
      homeStyle: true,
      theme: "light",
      noPadding: false,
      visible: false,
      individuationConfig: {
        videoRatio: "",
        deviceDomNum: 1,
        hideCommunity: false,
        alarmConfig: false,
        menuConfig: false,
        hideDeviceTypes: [],
        deviceIcons: {
          qiangji: {
            className: "icon-shebeizichan",
            color: "#2C86F8",
          },
          qiuji: {
            className: "icon-qiuji",
            color: "#2C86F8",
          },
        },
      },
      settingType: false,
      settingId: "",
      deviceDomNumList: [
        { label: 1, value: "1" },
        { label: 2, value: "2" },
        { label: 3, value: "3" },
      ],
      videoList: [
        { label: "origin", value: "原始" },
        { label: "stretch", value: "拉伸" },
        { label: "4:3", value: "4:3" },
        { label: "16:9", value: "16:9" },
        { label: "16:10", value: "16:10" },
      ],
      deviceTypeList: [
        { label: "枪机", value: 1 },
        { label: "球机", value: 2 },
        { label: "人脸卡口", value: 3 },
        { label: "车辆卡口", value: 4 },
        { label: "Wi-Fi设备", value: 5 },
        { label: "RFID设备", value: 6 },
        { label: "电围设备", value: 7 },
      ],
      noMenu: false,
      qiangjiList: [
        { className: "icon-shebeizichan" },
        { className: "icon-qiangji1" },
        { className: "icon-qiangji2" },
        { className: "icon-qiangji3" },
        { className: "icon-qiangji4" },
        { className: "icon-qiangji5" },
      ],
      qiujiList: [
        { className: "icon-qiuji" },
        { className: "icon-qiuji1" },
        { className: "icon-qiuji2" },
        { className: "icon-qiuji3" },
        { className: "icon-qiuji4" },
        { className: "icon-qiuji5" },
      ],
    };
  },
  computed: {
    ...mapState("admin/layout", [
      "siderTheme",
      "headerTheme",
      "headerStick",
      "tabs",
      "tabsFix",
      "siderFix",
      "headerFix",
      "headerHide",
      "headerMenu",
      "isMobile",
      "isTablet",
      "isDesktop",
      // "menuCollapse",
      "showMobileLogo",
      "showSearch",
      "showNotice",
      "showFullscreen",
      "showSiderCollapse",
      "showBreadcrumb",
      "showLog",
      "showReload",
      "enableSetting",
    ]),
    ...mapGetters({
      menuCollapse: "menuCollapse",
      individuation: "individuation",
    }),
    ...mapGetters({ isNoMenu: "common/getNoMenu" }),
    ...mapGetters("admin/menu", ["hideSider", "filterSider"]),
    ...mapState("admin/layout", ["isNoPadding"]),
    // 如果开启 headerMenu，且当前 header 的 hideSider 为 true，则将顶部按 headerStick 处理
    // 这时，即使没有开启 headerStick，仍然按开启处理
    isHeaderStick() {
      let state = this.headerStick;
      if (this.hideSider) state = true;
      return state;
    },
    showHeader() {
      let visible = true;
      if (this.headerFix && this.headerHide && !this.headerVisible)
        visible = false;
      return visible;
    },
    headerClasses() {
      return [
        `i-layout-header-color-${this.headerTheme}`,
        {
          "i-layout-header-fix": this.headerFix,
          "i-layout-header-fix-collapse": this.headerFix && this.menuCollapse,
          "i-layout-header-mobile": this.isMobile,
          "i-layout-header-stick": this.isHeaderStick,
          "i-layout-header-with-menu": this.headerMenu,
          "i-layout-header-with-hide-sider":
            this.hideSider || this.isDelayHideSider,
        },
      ];
    },
    headerStyle() {
      const menuWidth = this.isHeaderStick
        ? 0
        : this.menuCollapse
        ? Setting.menuCollapseWidth
        : Setting.menuSideWidth;
      return this.isMobile || !this.headerFix
        ? {}
        : {
            width: `calc(100% - ${menuWidth}px)`,
          };
    },
    siderClasses() {
      return {
        "i-layout-sider-fix": this.siderFix,
        "i-layout-sider-dark": this.siderTheme === "dark",
      };
    },
    contentClasses() {
      return {
        "i-layout-content-fix-with-header": this.headerFix,
        "i-layout-content-with-tabs": this.tabs,
        "i-layout-content-with-tabs-fix": this.tabs && this.tabsFix,
        "i-layout-content-nomenu": this.noMenu,
        homeStyle: this.homeStyle,
      };
    },
    insideClasses() {
      return {
        indexStyle: this.indexStyle,
        "i-layout-inside-fix-with-sider": this.siderFix,
        "i-layout-inside-fix-with-sider-collapse":
          this.siderFix && this.menuCollapse,
        "i-layout-inside-with-hide-sider": this.hideSider,
        "i-layout-inside-mobile": this.isMobile,
      };
    },
    drawerClasses() {
      let className = "i-layout-drawer";
      if (this.siderTheme === "dark") className += " i-layout-drawer-dark";
      return className;
    },
    menuSideWidth() {
      const sideMenuLength = this.filterSider.reduce((total, item) => {
        if (!item.hidden) {
          total += 1;
          if (item.children && item.children.length > 0) {
            total += 1;
          }
          return total;
        } else {
          return total;
        }
      }, 0);
      return this.menuCollapse && sideMenuLength > 1
        ? Setting.menuCollapseWidth
        : Setting.menuSideWidth;
    },
  },
  watch: {
    hideSider() {
      this.isDelayHideSider = true;
      setTimeout(() => {
        this.isDelayHideSider = false;
      }, 0);
    },
    $route: {
      handler(to, from) {
        this.updateMenuCollapseFun();

        if (to.name === "index") {
          this.indexStyle = true;
        } else {
          this.indexStyle = false;
        }
        if (to.path === "/home/<USER>") {
          this.homeStyle = true;
        } else {
          this.homeStyle = false;
        }

        if (
          to.name == "map-default-page" ||
          to.name == "cloud-default-page" ||
          to.name == "map-track" ||
          to.name == "device-manage"
        ) {
          this.noPadding = true;
        } else {
          this.noPadding = false;
        }

        if (to.query && to.query.noMenu) {
          this.noMenu = true;
          setTimeout(() => {
            document.title = to.meta.title;
          }, 500);
        } else {
          this.noMenu = false;
        }
      },
      immediate: true,
    },
    isNoMenu: {
      handler(val) {
        this.noMenu = val;
      },
    },
  },
  mounted() {
    document.addEventListener("scroll", this.handleScroll, { passive: true });
    if (this.$route.path === "/home/<USER>") {
      this.homeStyle = true;
    } else {
      this.homeStyle = false;
    }
  },
  beforeDestroy() {
    document.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    ...mapMutations(["updateMenuCollapse","setGlobalTheme"]),
    ...mapMutations("common",["setGlobalTheme"]),
    handleToggleDrawer(state) {
      if (typeof state === "boolean") {
        this.showDrawer = state;
      } else {
        this.showDrawer = !this.showDrawer;
      }
    },
    handleScroll() {
      if (!this.headerHide) return;
      const scrollTop =
        document.body.scrollTop + document.documentElement.scrollTop;
      if (!this.ticking) {
        this.ticking = true;
        requestAnimation(() => {
          if (this.oldScrollTop > scrollTop) {
            this.headerVisible = true;
          } else if (scrollTop > 300 && this.headerVisible) {
            this.headerVisible = false;
          } else if (scrollTop < 300 && !this.headerVisible) {
            this.headerVisible = true;
          }
          this.oldScrollTop = scrollTop;
          this.ticking = false;
        });
      }
    },
    handleHeaderWidthChange() {
      const $breadcrumb = this.$refs.breadcrumb;
      if ($breadcrumb) {
        $breadcrumb.handleGetWidth();
        $breadcrumb.handleCheckWidth();
      }
      const $menuHead = this.$refs.menuHead;
      if ($menuHead) {
        // todo $menuHead.handleGetMenuHeight();
      }
    },
    updateMenuCollapseFun() {
      const sideMenuLength = this.filterSider.reduce((total, item) => {
        if (!item.hidden) {
          total += 1;
          if (item.children && item.children.length > 0) {
            total += 1;
          }
          return total;
        } else {
          return total;
        }
      }, 0);
      // 只含有一级路由通过数据处理hidden=true排除
      if (this.individuation.menuConfig) {
        this.updateMenuCollapse(sideMenuLength > 1 && this.menuCollapse);
      } else {
        this.updateMenuCollapse(sideMenuLength > 1);
      }
      // this.updateMenuCollapse(sideMenuLength !== 0);
    },
    // 换肤
    changeTheme() {
      if (this.theme === "light") {
        this.theme = "dark";
      } else {
        this.theme = "light";
      }
      this.setGlobalTheme(this.theme)
      setTheme(this.theme);
    },
    handleSetting() {
      this.visible = true;
      let params = {
        paramType: "individuation",
        user: this.userInfo.username,
      };
      settingQuery(params).then((res) => {
        if (res.data != null) {
          let configValue = JSON.parse(res.data.paramValue);
          this.individuationConfig.videoRatio = configValue.videoRatio;
          this.individuationConfig.deviceDomNum = configValue.deviceDomNum || 1;
          this.individuationConfig.hideCommunity = configValue.hideCommunity;
          this.individuationConfig.alarmConfig = configValue.alarmConfig;
          this.individuationConfig.menuConfig = configValue.menuConfig;
          this.individuationConfig.hideDeviceTypes =
            configValue.hideDeviceTypes;
          this.individuationConfig.deviceIcons = configValue.deviceIcons;
          this.settingType = true;
          this.settingId = res.data.id;
        } else {
          this.settingType = false;
        }
      });
    },
    comfirmHandle() {
      let params = {
        user: this.userInfo.username,
        paramType: "individuation",
        paramValue: JSON.stringify(this.individuationConfig),
      };
      if (this.settingType) {
        settingUpdate({ ...params, id: this.settingId }).then((res) => {
          this.onCancel();
          this.$Message.success("设置成功");
          this.$store.dispatch("getIndividuation");
        });
      } else {
        settingAdd(params).then((res) => {
          this.onCancel();
          this.$Message.success("设置成功");
          this.$store.dispatch("getIndividuation");
        });
      }
    },
    onCancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="less">
.no-padding2 {
  padding: 0 !important;
}
.mt-20 {
  margin-top: 20px;
}
.check-group {
  display: flex;
  flex-wrap: wrap;
}
.i-layout-sider {
  /deep/ .ivu-select-dropdown {
    max-height: none !important;
  }
}
.device-icons {
  .form-item {
    display: flex;
    .icons {
      width: 128px;
    }
    .qiangji {
      .iconfont {
        cursor: pointer;
        &.selected {
          color: var(--qiangjiColor);
        }
      }
    }
    .qiuji {
      .iconfont {
        cursor: pointer;
        &.selected {
          color: var(--qiujiColor);
        }
      }
    }
  }
  .ivu-color-picker {
    .ivu-input {
      height: 22px;
    }
    .ivu-color-picker-picker {
      .ivu-input {
        width: 85%;
        margin-right: 20px;
      }
    }
  }
}
</style>
