<template>
  <div class="index-detail">
    <ui-modal v-model="visible" :title="title" :width="95" footerHide class="org-detail">
      <div class="over-flow">
        <ui-label class="inline" label="行政区划">
          <api-area-tree
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <ui-label class="ml-lg inline" label="考核时间">
          <template>
            <DatePicker
              type="month"
              format="yyyy-MM"
              placeholder="请选择考核时间"
              v-model="searchData.time"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'time')"
            ></DatePicker>
          </template>
        </ui-label>
        <Button type="primary" class="ml-lg" @click="search">查询</Button>
        <Button
          type="primary"
          class="fr mb-sm mr-sm"
          @click="handleCheck"
          :loading="exportLoading"
          v-show="!isCheck"
          v-permission="{
            route: $route.name,
            permission: 'examinationVerification',
          }"
        >
          <span class="vt-middle">成绩核验</span>
        </Button>
        <Button
          type="primary"
          class="fr mb-sm mr-sm"
          @click="handleCancel"
          v-show="isCheck"
          v-permission="{
            route: $route.name,
            permission: 'examinationVerification',
          }"
        >
          <span class="vt-middle">取消</span>
        </Button>
        <Button
          type="primary"
          class="fr mb-sm mr-sm"
          @click="handleSave"
          :loading="modifyLoading"
          v-show="isCheck"
          v-permission="{
            route: $route.name,
            permission: 'examinationVerification',
          }"
        >
          <span class="vt-middle">保存核验结果</span>
        </Button>
        <Button type="primary" class="fr mb-sm mr-sm" @click="getExport" :loading="exportLoading">
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="添加"></i>
          <span class="vt-middle">导出</span>
        </Button>
      </div>

      <div class="detail-content auto-fill clear-b" v-ui-loading="{ loading: loading, tableData: tableData }">
        <div class="table-box auto-fill">
          <!-- 统计详情 -->
          <ui-table
            class="ui-table table-content auto-fill spend"
            ref="tableContent"
            :table-columns="tableColumns"
            :table-data="tableData"
            noFill
            @click.native="handleClickTableColumn"
          >
            <template #EVALUATION_TIME="{ row }">
              <span v-if="systemConfig.distinguishVersion !== '4'">
                {{ row.EVALUATION_TIME }}
              </span>
              <Button type="text" v-else class="span-btn" @click="lookDetail(row)">
                {{ row.EVALUATION_TIME }}
              </Button>
            </template>
          </ui-table>
          <!-- 总计模块 -->
          <ui-table
            class="ui-table table-total"
            ref="tableTotal"
            :show-header="false"
            :table-columns="totalTableColumns"
            :table-data="totalTableData"
            :span-method="handleSpan"
          >
          </ui-table>
        </div>
      </div>
    </ui-modal>
    <assessment-details
      v-model="assessmentDetailsShow"
      :title="assessmentDetailsTitle"
      :index-detail-params="indexDetailParams"
      @showHkDetail="showHkDetail"
      @showYsDetail="showYsDetail"
    ></assessment-details>
    <ui-modal v-model="editVisible" title="编辑考核项得分" width="25" @query="handleSetScore">
      <InputNumber v-model="score" placeholder="请输入考核项得分" class="width-percent"></InputNumber>
    </ui-modal>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import CreateTabs from '@/components/create-tabs/create-tabs.vue';
import { mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';
import MathJax from '@/components/math-jax';
import { SCORE_OPERATION_TYPE, ITEM_SCORE } from '@/views/governanceevaluation/resultExaminationSC/modules/index.js';
export default {
  mixins: [downLoadTips],
  props: {
    taskObj: {
      type: [Object, String],
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AssessmentDetails: require('./assessment-details.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
  data() {
    return {
      SCORE_OPERATION_TYPE,
      visible: false,
      loading: false,
      title: '',
      row: {},
      taskDetail: {},
      tableColumns: [
        {
          title: '检测时间',
          key: 'EVALUATION_TIME',
          slot: 'EVALUATION_TIME',
        },
      ],
      tableData: [],
      table_row: {}, //点击获取的数据
      examTitle: '',
      totalTableColumns: [],
      totalTableData: [],
      countColumnObj: {}, //合并总得分第二行所需
      exportLoading: false,
      listSearchData: {},
      indexDetailParams: {},
      assessmentDetailsShow: false,
      assessmentDetailsTitle: '',
      selectTree: {
        regionCode: '',
      },
      searchData: {
        time: '',
        regionCode: '',
      },

      styles: {
        // width: "480px",
        height: '200px',
      },
      isCheck: false,
      modifyLoading: false,
      editVisible: false,
      score: null,
      oldScore: null, //旧值
      currentRow: null,

      children: [],
    };
  },
  watch: {
    taskObj: {
      handler(val) {
        if (val) {
          this.taskDetail = val;
          this.tableColumns = [
            {
              title: '检测时间',
              key: 'EVALUATION_TIME',
              slot: 'EVALUATION_TIME',
            },
          ];
          this.tableData = [];
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 监听下面总计表格滚动 上面表格和下面滚动保持一致
    this.$refs.tableTotal.$refs.table.$refs.body.addEventListener('scroll', this.handScrollLeft);
  },
  methods: {
    /**
     * 根据 scoreOperationType 获取 scoreOperationType 描述
     * @param params
     * @returns {string|string}
     */
    getScoreOperationTypeDesc(params) {
      switch (params.row[params.column.key]['scoreOperationType']) {
        case SCORE_OPERATION_TYPE.OBSOLETE:
          return '已作废';
        case SCORE_OPERATION_TYPE.MODIFY:
          return '已修改';
        default:
          return '';
      }
    },
    /**
     * 是否已作废
     * @returns {boolean}
     */
    isObsolete(params) {
      return params.row[params.column.key]['scoreOperationType'] === SCORE_OPERATION_TYPE.OBSOLETE;
    },
    /**
     * 是否已修改
     * @returns {boolean}
     */
    isModify(params) {
      return params.row[params.column.key]['scoreOperationType'] === SCORE_OPERATION_TYPE.MODIFY;
    },
    /**
     * 获取表头最后一级
     * @param tree 表头树结构
     * @param callback 回调
     * @returns {*[]} 表头最后一级组成的数组，扁平化
     */
    flattenTree(tree, callback) {
      const stack = tree;
      const flatArray = [];
      const childrenList = [];

      while (stack.length > 0) {
        const node = stack.pop();
        callback && callback(node);

        // 将当前节点添加到平铺数组中
        const { children, ...rest } = node;
        flatArray.push(rest);

        // 如果节点有子节点，将子节点压入栈中
        if (children && children.length) {
          stack.push(...children);
        }
        if (!children) {
          childrenList.push(rest);
        }
      }

      return childrenList;
    },
    async modifyExamByDetailId() {
      try {
        this.modifyLoading = true;
        let examModelScoreChildrenList = [];
        this.tableData.forEach((value) => {
          Object.keys(value).forEach((key) => {
            if (key.includes(ITEM_SCORE) && value[key]['scoreOperationType']) {
              examModelScoreChildrenList.push(value[key]);
            }
          });
        });
        let params = {
          examModelScoreChildrenList,
        };
        let { data } = await this.$http.post(governanceevaluation.updateExamByDetailId, params);
        this.$Message.success(data.msg);
      } catch (e) {
        console.log(e);
      } finally {
        this.modifyLoading = false;
      }
    },
    handleSetScore() {
      try {
        let { id, index, column } = this.currentRow;
        let score = null;
        this.flattenTree(this.$util.common.deepCopy(this.tableColumns), (val) => {
          if (val.code === column.parentCode) {
            score = val.score;
          }
        });
        if (this.oldScore === this.score) return this.$Message.error('考核项得分不能与旧得分一致！');
        if (this.score > score) return this.$Message.error(`考核项得分不能大于${score}分！`);
        if (id === 'modifyRow') {
          this.editRow();
        } else if (id === 'modifyColumn') {
          this.editColumn();
        } else if (id === 'modify') {
          this.$set(this.tableData[index][column.key], 'score', this.score);
          this.$set(this.tableData[index][column.key], 'scoreOperationType', SCORE_OPERATION_TYPE.MODIFY);
        } else {
          this.$set(this.tableData[index][column.key], 'score', this.oldScore);
          this.$set(this.tableData[index][column.key], 'scoreOperationType', SCORE_OPERATION_TYPE.DEFAULT);
        }
        this.editVisible = false;
      } catch (e) {
        console.log(e);
        this.$Message.error('修改失败');
      }
    },
    editRow() {
      let { index } = this.currentRow;
      this.children.forEach((item) => {
        if (this.tableData[index].hasOwnProperty(item.code) && item.code.includes(ITEM_SCORE)) {
          this.$set(this.tableData[index][item.code], 'score', this.score);
          this.$set(this.tableData[index][item.code], 'scoreOperationType', SCORE_OPERATION_TYPE.MODIFY);
        }
      });
    },
    editColumn() {
      let { column } = this.currentRow;
      this.tableData.forEach((item) => {
        if (item.hasOwnProperty(column.code) && column.code.includes(ITEM_SCORE)) {
          this.$set(item[column.code], 'score', this.score);
          this.$set(item[column.code], 'scoreOperationType', SCORE_OPERATION_TYPE.MODIFY);
        }
      });
    },
    /**
     * 点击事件放在table上，优化性能
     * @param target 点击对象
     */
    handleClickTableColumn({ target }) {
      try {
        let id = target.getAttribute('data-id');
        let index = Number(target.getAttribute('data-index'));
        let row = JSON.parse(target.getAttribute('data-row'));
        let column = JSON.parse(target.getAttribute('data-column'));

        switch (id) {
          case 'obsolete':
            //作废 或 取消作废
            let isObsolete = row[column.key]['scoreOperationType'] === SCORE_OPERATION_TYPE.OBSOLETE;
            this.tableData[index][column.key]['score'] = isObsolete
              ? row[column.key]['oldScore']
              : this.tableData[index][column.key]['score'];
            this.tableData[index][column.key]['scoreOperationType'] = isObsolete
              ? SCORE_OPERATION_TYPE.DEFAULT
              : SCORE_OPERATION_TYPE.OBSOLETE;
            break;
          case 'obsoleteRow':
            //作废 或 取消作废 整行
            this.children.forEach((item) => {
              if (
                item.code !== 'EVALUATION_TIME' &&
                this.tableData[index].hasOwnProperty(item.code) &&
                item.code.includes(ITEM_SCORE)
              ) {
                this.$set(this.tableData[index][item.code], 'scoreOperationType', SCORE_OPERATION_TYPE.OBSOLETE);
              }
            });
            break;
          case 'obsoleteColumn':
            if (column.code !== 'EVALUATION_TIME') {
              this.tableData.forEach((item) => {
                if (item.hasOwnProperty(column.code)) {
                  this.$set(item[column.code], 'scoreOperationType', SCORE_OPERATION_TYPE.OBSOLETE);
                }
              });
            }
            break;
          case 'unModify':
            //取消 修改
            this.tableData[index][column.key]['score'] =
              row[column.key]['oldScore'] || this.tableData[index][column.key]['score'];
            this.tableData[index][column.key]['scoreOperationType'] = SCORE_OPERATION_TYPE.DEFAULT;
            break;
          case 'modify':
          case 'modifyRow':
          case 'modifyColumn':
            this.score = (row && row[column.key] && row[column.key]['score']) || null;
            this.oldScore = this.score;
            this.editVisible = true;
            this.currentRow = {
              id,
              index,
              row,
              column,
            };
            break;
        }
      } catch (e) {
        console.log(e);
      }
    },
    async handleSave() {
      let result = await this.$UiConfirm({
        content: '确定要保存核验结果吗？保存后系统将重新计算平均成绩和总成绩！',
      });
      if (!result) return;
      await this.modifyExamByDetailId();
      await this.search();
      this.isCheck = false;
    },
    handleCheck() {
      this.isCheck = true;
    },
    handleCancel() {
      this.isCheck = false;
      this.search();
    },
    selectedArea(area) {
      this.searchData.regionCode = area.regionCode;
    },
    renderTooltip(h, params) {
      return h(
        'Tooltip',
        {
          props: {
            placement: 'right-start',
            transfer: true,
            // prop: {
            //   transfer: true,
            // },
          },
        },
        [
          h(
            'div',
            {
              slot: 'content',
            },
            [
              h(
                'div',
                {
                  class: ['tooltip-title'],
                },
                `${params.column.name}：`,
              ),
              h('div', {}, [
                h('span', {}, '分项分值：'),
                h('span', { class: ['active-blue'] }, `${params.column.score}分`),
              ]),
              h('div', {}, [
                h('span', {}, '实际分值：'),
                h(MathJax, {
                  class: ['active-blue'],
                  props: {
                    mathStr: params.column.examFormula || '',
                  },
                }),
              ]),
              h('div', {}, [h('span', {}, `考核方法：${params.column.examWay || ''}`)]),
            ],
          ),
          h('span', {}, [h('span', {}, `${params.column.title}`)]),
          h('i', {
            class: ['icon-font', 'icon-wenhao', 'ml-xs', 'icon-warning'],
          }),
        ],
      );
    },
    async getExport() {
      this.exportLoading = true;
      let data = {
        examSchemeId: this.listSearchData.examSchemeId + '',
        examTaskId: this.listSearchData.examTaskId + '',
        month: this.listSearchData.month + '',
        orgRegionFlag: '2',
        year: this.listSearchData.year + '',
        orgRegeionCode: this.searchData.regionCode + '',
        category: this.table_row.column.category || '',
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(governanceevaluation.queryExamContentItemIndexResultListExport, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    financial(string, fractionDigits) {
      return Number.parseFloat(Number.parseFloat(string).toFixed(fractionDigits));
    },
    init(params, searchData) {
      this.listSearchData = searchData;
      this.visible = true;
      this.isCheck = false;
      this.table_row = params;
      this.examTitle = this.table_row.column.name.replace('考核得分', '');
      this.title = `${this.table_row.row.ORG_REGEION_CODE} - ${this.examTitle}考核明细`;
      this.searchData.regionCode = params.row.orgCode;
      this.selectTree.regionCode = params.row.orgCode;
      this.searchData.time = searchData.time;
      this.indexDetailParams = {
        examSchemeId: '' + searchData.examSchemeId,
        examTaskId: '' + searchData.examTaskId,
        month: '' + searchData.month,
        orgRegionFlag: '2', //这里按组织机构查询
        year: '' + searchData.year,
        orgRegeionCode: '' + this.searchData.regionCode,
        evaluationTaskSchemeId: searchData.evaluationTaskSchemeId || '', //新增查询参数
      };
      params.column.category ? (this.indexDetailParams['category'] = params.column.category) : null;
      this.getDetail(this.indexDetailParams);
    },
    search() {
      const data = this.indexDetailParams;
      data.year = parseInt(this.$util.common.formatDate(this.searchData.time, 'yyyy'));
      data.month = parseInt(this.$util.common.formatDate(this.searchData.time, 'MM'));
      data.orgRegeionCode = this.searchData.regionCode;
      this.getDetail(data);
    },
    getDetail(searchData) {
      this.loading = true;
      this.$http
        .post(governanceevaluation.queryExamContentItemIndexResultList, searchData)
        .then((res) => {
          let { headers, body } = res.data.data;
          // let { headers, body } = moke_detail

          this.tableColumns = this.$util.common.deepCopy(headers);
          this.children = this.flattenTree(this.$util.common.deepCopy(headers));
          /**
           * 处理表头
           * 把所有考核项增加得分
           */
          for (let i of this.tableColumns) {
            if (i.name !== '考核内容' && i.code !== 'EVALUATION_TIME') {
              i.name = `${i.name} (${i.score}分)`;
            }
            for (let k of i.children) {
              if (!k.name.includes('得分') && k.name !== '考核项') {
                if (k.code.includes('EX')) {
                  k.name = `${k.name} (${k.excessMaxScoreValue}分)`;
                } else {
                  k.name = `${k.name} (${k.score}分)`;
                }
              }
            }
          }
          this.handleTableHeaders(this.tableColumns);
          // 处理表内容
          this.handleTableBody(body);
          // 处理总计
          this.handleSummary(headers);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleTableHeaders(arr) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.align = 'center';
        if (!v.children) {
          v.minWidth = 150;
        }
        // 左侧一列浮动在左侧
        if (v.title === '考核内容' || v.title === '考核项' || v.title === '检测时间') {
          v.fixed = 'left';
          v.width = 180;
          v.render = (h, { row, column, index }) => {
            if (this.isCheck) {
              return (
                <div class="active-wrapper">
                  <span class="value">{row[v.code]}</span>
                  <div class="active">
                    <span
                      class="link-text-box pointer"
                      data-id="obsoleteRow"
                      data-row={JSON.stringify(row)}
                      data-column={JSON.stringify(column)}
                      data-index={index}
                    >
                      作废
                    </span>
                    <span
                      class="link-text-box ml-sm pointer"
                      data-id="modifyRow"
                      data-row={JSON.stringify(row)}
                      data-column={JSON.stringify(column)}
                      data-index={index}
                    >
                      修改
                    </span>
                  </div>
                </div>
              );
            } else {
              return (
                <span class="link-text-box pointer" vOn:click_stop_prevent={this.lookDetail(row)}>
                  {row[v.code]}
                </span>
              );
            }
          };
        }
        /**
         * 1.非得分非时间 直接展示
         * 2.时间那一列则更改为三角展示左下右上
         */
        if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') {
          v.renderHeader = this.renderTableHeader;
        } else if (v.code === 'EVALUATION_TIME') {
          v.slot = 'EVALUATION_TIME';
          v.renderHeader = this.randerSlash;
          v.className = 'triangle';
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr) {
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        /**
         * 循环每一项插入列表中
         * 判断是时间，得分，指标，百分比
         * */

        v.forEach((k) => {
          if (k.code === 'EVALUATION_TIME') {
            tableData[i].EVALUATION_TIME = k.evaluationTimeStr;
          } else if (typeof k.evaluationResultValue === 'number') {
            tableData[i][k.code] = {
              score: k.evaluationResultValue + '%',
              // 四川省厅需求增加字段跳转页
              // linkUrl: k.linkUrl || null,
              evaluationStatisticsId: k.evaluationStatisticsId || null,
            };
          } else {
            tableData[i][k.code] = {
              score: k.score,
              examContentItemMonthResultDetailId: k.examContentItemMonthResultDetailId,
              scoreOperationType: k.scoreOperationType,
              oldScore: k.oldScore,
            };
          }
        });
      });
      this.tableData = tableData;
    },
    /**
     * 总计列表合成
     * 共两行
     * 表头由 考核内容，考核分项表头组成 隐藏表头只显示表中的内容
     * 内容第一列 为总分 合并第一列
     * 内容第二行合并为总具体得分 合并第二列第二行所有的行
     */
    handleSummary(columns) {
      this.totalTableData = [];
      this.totalTableColumns = [];
      this.countColumnObj = {};
      /**
       * 第一行内容
       * 第一列为固定总分
       * 后面列为各个考核项平均得分
       * 第二行内容
       * 第一列为固定总分
       * 后面列为各个考核内容总分
       */
      let scores = {};
      let assessmentScores = {};
      columns.forEach((row) => {
        row.children.forEach((rw) => {
          if (rw.name === '考核项') {
            this.totalTableColumns.push({
              title: rw.name,
              key: 'examinationContent',
              width: 180,
              align: 'center',
              fixed: 'left',
              parentCode: rw.parentCode,
            });
            scores.examinationContent = '总得分';
            assessmentScores.examinationContent = '总得分';
          } else {
            this.totalTableColumns.push({
              title: rw.name,
              key: rw.code,
              minWidth: rw.children ? 150 * rw.children.length : 150,
              align: 'center',
              className: 'total',
              parentCode: rw.parentCode,
            });
            scores[rw.code] = '';
            if (rw.parentCode === row.code) {
              assessmentScores[rw.code] = row.finalName
                ? `${row.name}${row.finalName}`
                : `${row.name}最终得分：（--分）`;
              /**
               * 第二行内容横向合并需要计算合并的数量
               * 当指标的parentCode相同时就合并在一起
               */
              if (this.countColumnObj.hasOwnProperty(rw.parentCode)) {
                this.countColumnObj[rw.parentCode]++;
              } else {
                this.countColumnObj[rw.parentCode] = 0;
                this.countColumnObj[rw.parentCode]++;
              }
            }
          }
        });
      });
      /**
       * 下面用来计算总计行中的第一行，每个考核项的平均得分
       * 由于后端难以计算 所以此处由前端计算 后端为lf
       */
      let codeFrequency = {}; // 用来记录考核项有多少次得分
      this.tableData.forEach((row) => {
        Object.keys(scores).forEach((key) => {
          // 如果没有此考核项则添加此考核项为0次
          if (!codeFrequency.hasOwnProperty(key)) {
            codeFrequency[key] = 0;
          }
          /**
           *  如果返回的数据中有此code则把所有code相同的得分相加得到此code的总分
           */
          if (row.hasOwnProperty(key)) {
            scores[key] = scores[key] - 0 + Number(row[key].score);
            codeFrequency[key]++;
          } else if (
            row.hasOwnProperty(key + '_ITEM_SCORE') &&
            row[key + '_ITEM_SCORE']['scoreOperationType'] !== SCORE_OPERATION_TYPE.OBSOLETE
          ) {
            scores[key] = scores[key] - 0 + Number(row[key + '_ITEM_SCORE'].score);
            codeFrequency[key]++;
          }
        });
      });
      /**
       * 如果考核总分为空则标记为没有考核为--
       * 如果考核次数不为0 则计算平均考核得分
       */
      Object.keys(scores).forEach((key) => {
        if (scores[key] === '') {
          scores[key] = '--';
        } else {
          if (codeFrequency[key] !== 0) {
            scores[key] = this.financial(scores[key] / codeFrequency[key], 5);
          }
        }
      });
      this.totalTableData.push(scores);
      this.totalTableData.push(assessmentScores);
      /**
       * 第三行内容
       * 第一列为固定总分
       * 后面列合并为具体总得分
       * key为上面内容表格中的列的下标为1的key 因为下面总计表格中的表头是由上面统计内容的表头
       * 而且要合并第二列之后的所有列 所以要在列表中第二列赋值
       */
      this.totalTableData.push({
        examinationContent: '总得分',
        cellClassName: {
          [this.totalTableColumns[1].key]: 'total-score',
        },
        [this.totalTableColumns[1].key]: `${this.examTitle}考核最终得分：${
          this.table_row.row[this.table_row.column.code].score
        }`,
      });
    },
    evaluationDetailBtn(item) {
      let obj = item.row[item.column.key];
      this.$emit('selectModuleClick', obj);
    },
    handleSpan({ column, rowIndex, columnIndex }) {
      // 合并第一列中的总分
      if (rowIndex === 0 && columnIndex === 0) {
        return [3, 1];
      } else if (rowIndex !== 0 && columnIndex === 0) {
        return [0, 0];
      }
      // 合并第二行中的第二列之后的行
      // 直接往后合并相同parentCode的列
      if (rowIndex === 1 && columnIndex === 1) {
        return [1, Object.values(this.countColumnObj)[0]];
      }
      // 当发现后面的列和当前列相同时切掉此列，当不同时向后合并相同parentCode的列数
      if (rowIndex === 1 && columnIndex > 1) {
        if (column.parentCode === this.totalTableColumns[columnIndex - 1]['parentCode']) {
          return [0, 0];
        } else if (column.parentCode !== this.totalTableColumns[columnIndex - 1]['parentCode']) {
          return [1, this.countColumnObj[column.parentCode]];
        }
      }

      // 合并第三行第二列之后的所有行
      if (rowIndex === 2 && columnIndex === 1) {
        return [1, this.totalTableColumns.length - 1];
      } else if (rowIndex === 2 && columnIndex > 1) {
        return [0, 0];
      }
    },
    handScrollLeft() {
      this.$refs.tableContent.$refs.table.$refs.body.scrollLeft =
        this.$refs.tableTotal.$refs.table.$refs.body.scrollLeft;
    },
    lookDetail(row) {
      this.indexDetailParams.examDate = row.EVALUATION_TIME;
      this.assessmentDetailsTitle = this.title;
      this.assessmentDetailsShow = true;
    },
    renderTableTd(h, params) {
      if (!params.row[params.column.key] && params.row[params.column.key] !== 0) {
        return h('span', {}, '--');
      } else {
        if (this.isCheck && params.column.key.includes(ITEM_SCORE)) {
          if (this.isObsolete(params)) {
            return (
              <div class="active-wrapper">
                <span class="value">
                  <span>{params.row[params.column.key].score}</span>
                  <span class="font-red ml-xs">{this.getScoreOperationTypeDesc(params)}</span>
                </span>
                <div class="active">
                  <span
                    class="link-text-box pointer"
                    data-id="obsolete"
                    data-row={JSON.stringify(params.row)}
                    data-column={JSON.stringify(params.column)}
                    data-index={params.index}
                  >
                    {this.isObsolete(params) ? '取消作废' : '作废'}
                  </span>
                </div>
              </div>
            );
          } else if (this.isModify(params)) {
            return (
              <div class="active-wrapper">
                <span class="value">
                  <span>{params.row[params.column.key].score}</span>
                  <span class="font-red ml-xs">{this.getScoreOperationTypeDesc(params)}</span>
                </span>
                <div class="active">
                  <span
                    class="link-text-box ml-sm pointer"
                    data-id="modify"
                    data-row={JSON.stringify(params.row)}
                    data-column={JSON.stringify(params.column)}
                    data-index={params.index}
                  >
                    修改
                  </span>
                  <span
                    class="link-text-box ml-sm pointer"
                    data-id="unModify"
                    data-row={JSON.stringify(params.row)}
                    data-column={JSON.stringify(params.column)}
                    data-index={params.index}
                  >
                    取消修改
                  </span>
                </div>
              </div>
            );
          } else {
            return (
              <div class="active-wrapper">
                <span class="value">
                  <span>{params.row[params.column.key].score}</span>
                  <span class="font-red ml-xs">{this.getScoreOperationTypeDesc(params)}</span>
                </span>
                <div class="active">
                  <span
                    class="link-text-box pointer"
                    data-id="obsolete"
                    data-row={JSON.stringify(params.row)}
                    data-column={JSON.stringify(params.column)}
                    data-index={params.index}
                  >
                    {this.isObsolete(params) ? '取消作废' : '作废'}
                  </span>
                  <span
                    class="link-text-box ml-sm pointer"
                    data-id="modify"
                    data-row={JSON.stringify(params.row)}
                    data-column={JSON.stringify(params.column)}
                    data-index={params.index}
                  >
                    修改
                  </span>
                </div>
              </div>
            );
          }
        } else {
          return (
            <span>
              <span>{this.isObsolete(params) ? '--' : params.row[params.column.key].score}</span>
            </span>
          );
        }
        // 如果是得分则不增加跳转 如果是指标则跳转详情
        if (params.column.code.includes('SCORECODE')) {
          return h('span', {}, params.row[params.column.key].score);
        } else {
          return h(
            CreateTabs,
            {
              props: {
                componentName: 'detectionToOverview',
                tabsText: '评测结果', // 跳转页面标题
                tabsQuery: {
                  indexId: params.row[params.column.key].indexId,
                  code: params.row[params.column.key].orgRegeionCode,
                  access: 'TASK_RESULT',
                  batchId: params.row[params.column.key].evaluationBatchId,
                  taskIndexId: params.row[params.column.key].evaluationTaskIndexId,
                  startTime: params.row[params.column.key].evaluationTime,
                },
              },
              style: {
                color: 'var(--color-primary)',
                cursor: 'pointer',
                textDecoration: 'underline',
              },
              on: {
                selectModule: () => {
                  this.evaluationDetailBtn(params);
                },
              },
            },
            params.row[params.column.key].score,
          );
        }
      }
    },
    randerSlash(h) {
      return h(
        'div',
        {
          attrs: {
            class: 'type',
          },
        },
        [
          h(
            'strong',
            {
              attrs: {
                class: 'detection-time',
              },
            },
            '检测时间',
          ),
          h(
            'strong',
            {
              attrs: {
                class: 'detection-indicator',
              },
            },
            '检测指标',
          ),
        ],
      );
    },
    renderTableHeader(h, params) {
      if (params.column.examFormula) {
        return this.renderTooltip(h, params);
      }

      if (params.column.level === 3 && params.column.code.includes(ITEM_SCORE)) {
        if (this.isCheck) {
          return (
            <div class="active-wrapper">
              <span class="value">{`${params.column.title}`}</span>
              <div class="active">
                <span
                  class="link-text-box pointer"
                  data-id="obsoleteColumn"
                  data-row={JSON.stringify(params.row)}
                  data-column={JSON.stringify(params.column)}
                  data-index={params.index}
                >
                  作废
                </span>
                <span
                  class="link-text-box ml-sm pointer"
                  data-id="modifyColumn"
                  data-row={JSON.stringify(params.row)}
                  data-column={JSON.stringify(params.column)}
                  data-index={params.index}
                >
                  修改
                </span>
              </div>
            </div>
          );
        } else {
          return h('div', [h('div', {}, `${params.column.title}`)]);
        }
      } else {
        return h('div', [h('div', {}, `${params.column.title}`)]);
      }
    },
    jumpToThird(row) {
      window.open(row.linkUrl, '_blank');
    },
    showHkDetail(row) {
      this.$emit('showHkDetail', row);
    },
    showYsDetail(row) {
      this.$emit('showYsDetail', row);
    },
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
  beforeDestroy() {
    this.$refs.tableTotal.$refs.table.$refs.body.removeEventListener('scroll', this.handScrollLeft);
  },
};
</script>
<style lang="less">
.tooltip-title {
  color: var(--color-primary);
  font-size: 14px;
  font-weight: bold;
}

.active-blue {
  color: var(--color-bluish-green-text) !important;
  .molecular {
    &::after {
      border-color: var(--color-bluish-green-text) !important;
    }
  }
}
</style>
<style lang="less" scoped>
.index-detail {
  color: #fff;
  .org-detail {
    @{_deep} .ivu-modal {
      height: 93%;
      > .ivu-modal-content {
        height: 100%;
        > .ivu-modal-body {
          height: 96.5%;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }

  .detail-content {
    .table-content {
      .span-btn {
        @{_deep}span {
          text-decoration: underline;
        }
      }
      @{_deep}.ivu-table-wrapper {
        height: 100% !important;
      }
      @{_deep} .ivu-table {
        .ivu-table-body {
          overflow-x: hidden !important;
          border-right: 0;
        }
      }
      @{_deep}.ivu-table-overflowX {
        & ~ .ivu-table-fixed {
          height: 100%;
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
            height: calc(~'100% - 150PX - 22PX') !important;
          }
        }
      }

      @{_deep}.ivu-table-fixed {
        height: 100%;
        > .ivu-table-fixed-body {
          // 浮动高度应该为表格高度 - 浮动表头高度
          height: calc(~'100% - 150px - 10px') !important;
        }
      }
      @{_deep} .active-wrapper {
        .active {
          display: none;
        }
        &:hover {
          .active {
            display: inline;
          }
          .value {
            display: none;
          }
        }
      }
    }
    .table-total {
      @{_deep}.ivu-table-wrapper,
      @{_deep}.ivu-table-body {
        height: auto !important;
      }
      @{_deep}.ivu-table-overflowX {
        & ~ .ivu-table-fixed {
          height: calc(~'100% - 11px');
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
            height: 100% !important;
          }
        }
      }

      @{_deep}.ivu-table-fixed {
        height: 100%;
        > .ivu-table-fixed-body {
          // 浮动高度应该为表格高度 - 浮动表头高度
          height: 100% !important;
        }
      }
    }
    // min-height: 260px;
    .ui-table {
      padding: 0 10px;
      @{_deep}.total {
        font-size: 22px;
      }
      @{_deep}.total-score {
        color: var(--color-bluish-green-text);
      }
      @{_deep}.ivu-table {
        &:before {
          width: 0;
        }
        .ivu-table-header {
          tr {
            th {
              border-right: none !important;
            }
          }
        }
      }
      @{_deep} .ivu-table {
        th,
        td {
          border: 1px solid var(--border-color) !important;
        }
        &:before {
          content: '';
          position: absolute;
          background-color: #0d477d !important;
        }
        .ivu-table-summary {
          td {
            background: #062042;
          }
        }
      }
    }
    @{_deep} .ivu-table-fixed-header {
      .triangle {
        padding: 0;
        .ivu-table-cell {
          padding: 0;
          width: 100%;
          height: 100%;
        }
        .type {
          position: relative;
          background-color: #0d477d;
          width: 100%;
          height: 100%;
          z-index: 0;
          &:after {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            clip-path: polygon(100% calc(100% - 0.5px), 100% 0px, 0px -0.5px);
            position: absolute;
            top: 0;
            background-color: #092955;
          }
          &:before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
            position: absolute;
            top: 0;
            background-color: #092955;
          }
          .detection-time {
            position: absolute;
            left: 6px;
            bottom: 5px;
            z-index: 1;
          }
          .detection-indicator {
            position: absolute;
            right: 6px;
            top: 5px;
            z-index: 1;
          }
        }
      }
    }
  }
}
</style>
