<template>
  <div class="choose-time">
    <div class="custom" v-if="currentKey == 4"> 
        <!-- daterange -->
      <DatePicker :type="dateType" 
      placement="bottom-end" 
      :options="options" 
      :format="dateFormat" 
      @on-change="datePickerChange" 
      placeholder="请选择时间" 
      @on-clear="resetTimePicker"></DatePicker>
      <Icon type="ios-close-circle-outline" :size="16" @click="close" />
    </div>
    <template v-for="(e, i) in timeList" v-else>
      <span :class="{ active: e.key === currentKey }" :key="i" @click="choseTypeHandler(e.key)"> {{ e.value }} </span>
    </template>
  </div>
</template>

<script>
export default {
  name: "time-select",
  props: {
    dateFormat:{
      default: "yyyy-MM-dd"
    },
    dateType: {
        default: "daterange"
    },
    data: {
      default: ()=>[
        { value: '今天', key: 1 },
        { value: '近7天', key: 2 },
        { value: '近30天', key: 3 },
        { value: '自定义', key: 4 }
      ],
    },
    /**
     *  DatePicker options
     */
    options: {
      default: ()=>({
        disabledDate(date) {
          let day = 90 * 24 * 3600 * 1000
          return date.valueOf() + day < Date.now() - 86400000
        }
      })
    }
  },
  data() {
    return {
      timeList: [],
      currentKey: 2,
      times: [],
    }
  },
  watch: {
    data: {
      handler(val){
        this.timeList = val
      },
      deep: true,
      immediate: true
    }
  },
    methods: {
        choseTypeHandler(key) {
            this.currentKey = key
            this.$emit('dataRangeHandler', key)
        },
        close() {
            this.currentKey = 2
            this.times = []
            this.$emit('dataRangeHandler', this.currentKey, this.times)
        },
        datePickerChange(val) {
            this.times = val
            this.$emit('dataRangeHandler', this.currentKey, this.times)
        },
        // 重置
        resetTimePicker() {
            this.currentKey = 4
            this.times = []
        }
    }
}
</script>

<style lang="less" scoped>
.choose-time {
  .custom {
    position: relative;
    /deep/.ivu-date-picker {
      width: 240px !important;
    }
    .ivu-icon {
      margin-left: 8px;
      position: absolute;
      right: -22px;
      top: 8px;
      color: #888;
    }
  }
  > span {
    height: 18px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #e8eaec;
    padding: 1px 7px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-right: 10px;
    cursor: pointer;
    &:last-child{
        margin-right: 0;
    }
  }

  .active {
    color: #fff;
    background: #2c86f8;
  }
}
</style>
