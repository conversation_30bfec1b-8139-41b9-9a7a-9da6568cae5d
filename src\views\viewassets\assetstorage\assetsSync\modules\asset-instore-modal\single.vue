<template>
  <difference
    v-model="differenceShow"
    :field-list="fieldList"
    :footer-hide="false"
    sign="differ"
    onlyRead
    modal-title="资产入库"
    @onCancel="cancel"
  >
    <template #filter>
      <RadioGroup class="fl" v-model="sign" @on-change="changeSign">
        <Radio label="">全部比对字段</Radio>
        <Radio class="ml-lg" label="0">相同字段</Radio>
        <Radio class="ml-lg" label="1">差异字段</Radio>
      </RadioGroup>
    </template>
    <template #footer>
      <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
      <Button class="plr-30" type="primary" @click="saveQuery">保存</Button>
    </template>
    <template #increaseTitle>
      <div>入库策略</div>
    </template>
    <template #increaseContent="{ row }">
      <div>
        <Select class="width-input" v-model="row.addType" :placeholder="`请选择入库策略`" clearable :max-tag-count="1">
          <Option v-for="(item, index) in addTypeEnum" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
        </Select>
      </div>
    </template>
  </difference>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    activeDifferDetail: {},
  },
  data() {
    return {
      sign: '',
      fieldList: [],
      differenceShow: false,
    };
  },
  created() {},
  methods: {
    cancel() {
      this.differenceShow = false;
    },
    changeSign(val) {
      if (val === '') {
        this.fieldList = this.$util.common.deepCopy(this.activeDifferDetail);
        return;
      }
      this.handleDifferList(val);
    },
    handleDifferList(type) {
      this.fieldList = this.activeDifferDetail.filter((row) => row.isDiffer === type);
    },
    // 保存
    async saveQuery() {
      let storageParam = JSON.stringify(
        this.fieldList.map((item) => {
          let obj = {};
          obj.fieldName = item.fieldName;
          obj.fieldRemark = item.fieldDesc;
          obj.addType = item.addType;
          console.log(obj);
          return obj;
        }),
      );
      this.$emit('oneAssetsConfirmInstore', storageParam);
    },
  },
  watch: {
    value(val) {
      this.differenceShow = val;
      if (val) {
        this.fieldList = this.$util.common.deepCopy(this.activeDifferDetail);
      }
    },
    differenceShow(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      aysc_storage_type: 'assets/aysc_storage_type',
    }),
    addTypeEnum() {
      return this.aysc_storage_type;
    },
  },
  components: {
    Difference: require('@/views/viewassets/components/difference.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
