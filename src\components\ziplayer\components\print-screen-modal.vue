<template>
  <Modal ref="printScreenModal" v-model="options.open" draggable :z-index="10000" sticky :mask="false" :title="options.title" footer-hide width="880">
    <div class="img-box" :style="{ height: options.imgUrls.length > 1 ? '564px' : '514px' }">
      <img-preview ref='imgPreview' :imgUrls="options.imgUrls" :structured="true"></img-preview>
    </div>
  </Modal>
</template>
<script>
import imgPreview from '@/components/img-preview.vue'

export default {
  components: {
    imgPreview
  },
  props: {
    options: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    'options.open': {
        handler (val) {
            if(!val){
                this.$nextTick(() => {
                    this.$refs.imgPreview.initialize()
                })
            }
        },
        immediate: true
    },
  },
  data() {
    return {}
  },
  beforeDestroy() {
    this.options.open = false
  },
  deactivated() {
    this.options.open = false
  },
  methods: {}
}
</script>
<style lang="less" scoped>
.img-box {
  width: 100%;
}
/deep/ .ivu-modal-header {
  padding: 6px 10px;
  background: rgba(211, 215, 222, 0.3);
  .ivu-modal-header-inner {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.9);
    font-size: 16px;
  }
}
/deep/ .ivu-modal-body {
  padding: 10px;
}
/deep/ .ivu-modal-close {
  top: 0;
  right: 2px;
}
</style>
