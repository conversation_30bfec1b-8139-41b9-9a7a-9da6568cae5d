<template>
  <div>
    <common-form
      :label-width="200"
      :form-data="formData"
      :form-model="formModel"
      ref="commonForm"
      :moduleAction="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="waycondiction" class="mt-xs">
        <p>
          <span class="base-text-color">检测条件：</span>
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
            >设备可用</Checkbox
          >
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.examReportStatus" true-value="1" false-value="0"
            >设备未报备</Checkbox
          >
        </p>
        <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="200">
      <FormItem label="检测内容" prop="detectionMode">
        <CheckboxGroup
          ref="detectionMode"
          class="check-inline"
          v-model="formData.detectionMode"
          @on-change="checkGroupChange"
        >
          <Checkbox label="1" class="block"> 在线率：设备在国标平台为在线状态 </Checkbox>
          <Checkbox label="2" class="block" :disabled="formData.detectionMode.includes('3')">
            <span>完好率：设备在线，拉流</span>
            <InputNumber
              v-model="formData.detectionTimeOut"
              placeholder="秒"
              class="ml-xs mr-xs width-mini"
            ></InputNumber>
            <span>秒内有响应</span>
          </Checkbox>
          <Checkbox label="3" class="block">
            <span>可用率：成功接收到实时视频流</span>
          </Checkbox>
          <Checkbox label="4" class="block">
            <span>联网质量：检测信令时延是否超时、码流时延是否超时、关键帧时延是否超时</span>
          </Checkbox>
        </CheckboxGroup>
        <div class="block" v-show="formData.detectionMode && formData.detectionMode.includes('4')">
          <div class="mb-sm base-text-color">
            信令时延超时：<InputNumber class="mr-sm" v-model.number="formData.delaySipTimeOut"></InputNumber>毫秒
          </div>
          <div class="mb-sm base-text-color">
            码流时延超时：<InputNumber class="mr-sm" v-model.number="formData.delayStreamTimeOut"></InputNumber>毫秒
          </div>
          <div class="base-text-color">
            关键帧时延超时：<InputNumber class="mr-sm" v-model.number="formData.delayIdrTimeOut"></InputNumber>毫秒
          </div>
        </div>
      </FormItem>
      <FormItem label="指标取值" prop="indexDetectionMode">
        <RadioGroup v-model="formData.indexDetectionMode" @on-change="handleIndexDetectionModeChange">
          <Radio label="1" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('1')">在线率</Radio>
          <Radio label="2" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('2')">完好率</Radio>
          <Radio label="3" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('3')">可用率</Radio>
          <Radio label="4" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('4')"
            >联网质量</Radio
          >
        </RadioGroup>
      </FormItem>
      <FormItem label="需要视频截图">
        <RadioGroup v-model="formData.isScreenshots">
          <Radio label="1" :disabled="!!formData.detectionMode && !formData.detectionMode.includes('3')">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="检测合格更新设备在线状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem>
        <template #label>
          <Tooltip placement="top-start" max-width="200">
            <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
            <div slot="content">
              如果设备最近X小时（在后台可配置，默认12小时）有检测成功记录，则直接取该次缓存的检测结果，不再重新拉流检测。
            </div>
            取最近缓存结果
          </Tooltip>
        </template>
        <RadioGroup v-model="formData.useRecentCache">
          <Radio label="true">是</Radio>
          <Radio label="false">否</Radio>
        </RadioGroup>
      </FormItem>

      <FormItem label="是否需要复检">
        <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
          <Radio label="1">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup>
        <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
      </FormItem>
      <FormItem label="复检设备" v-if="isRecheck === '1'">
        <RadioGroup v-model="formData.reinspect.model">
          <Radio label="UNQUALIFIED">检测不合格设备</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType">
        <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
          <Radio label="INTERVAL">时间间隔</Radio>
          <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
        </RadioGroup>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
          <span class="base-text-color">检测结束</span>
          <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
          <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="1">时</Option>
            <Option :value="2">分</Option>
          </Select>
        </div>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
          <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="3">当天</Option>
            <Option :value="4">第二天</Option>
            <Option :value="5">第三天</Option>
          </Select>
          <TimePicker
            :value="formData.reinspect.scheduleValue"
            transfer
            format="HH:mm"
            placeholder="请选择"
            class="width-xs"
            @on-change="handleChangeTime"
          ></TimePicker>
        </div>
      </FormItem>
      <FormItem label="治理前可调阅率">
        <RadioGroup value="1">
          <Radio label="1">手动填写</Radio>
          <!--          <Radio label="2">规则指定</Radio>-->
        </RadioGroup>
      </FormItem>
      <div class="fr mb-md ml-sm">
        <ui-label required class="inline ml-lg" label="可调阅率" :width="70">
          <InputNumber v-model="callRate" :min="0" :max="100" class="width-md" placeholder="请输入可调阅率">
          </InputNumber>
        </ui-label>
        <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
      </div>
      <common-capture-area-select class="clear-b" ref="captureAreaSelect" :data="areaTreeData">
        <template #configName>
          <span class="inline width-md">治理前可调阅率</span>
        </template>
        <template #label="{ data }">
          <div class="width-md">
            <InputNumber
              v-show="data.check"
              :min="0"
              :max="100"
              class="width-xs"
              v-model.number="data.value"
              placeholder="请输入治理前可调阅率"
            ></InputNumber>
          </div>
        </template>
      </common-capture-area-select>
    </Form>
  </div>
</template>

<script>
// VIDEO_PLAYING_ACCURACY
// VIDEO_READ_PROMOTION_RATE
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    // 指标英文名称
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateDetectionModePass = (rule, value, callback) => {
      !value.length ? callback(new Error('请选择检测内容')) : '';
      //完好率 需要 detectionTimeOut 参数
      if (value.includes('2') && !this.formData.detectionTimeOut) {
        callback(new Error('请输入响应时间'));
      }
      //联网质量
      if (
        value.includes('4') &&
        (!this.formData.delaySipTimeOut || !this.formData.delayStreamTimeOut || !this.formData.delayIdrTimeOut)
      ) {
        callback(new Error('请输入延时'));
      }
      callback();
    };
    const validateThresholdPass = (rule, value, callback) => {
      !value ? callback(new Error('请输入综合质量评分设置值')) : callback();
    };
    const validateIndexDetectionMode = (rule, value, callback) => {
      !value ? callback(new Error('请选择指标取值')) : callback();
    };
    const validateMinThresholdPass = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        const reg = new RegExp('^([1-9]|[1-9]\\d|100)$');
        !reg.test(value) ? callback(new Error('请输入1-100的整数')) : callback();
      }
    };
    return {
      defaultProps: {
        default: () => {
          return {
            label: 'regionName',
            children: 'children',
          };
        },
      },
      ruleCustom: {
        detectionMode: [{ validator: validateDetectionModePass, trigger: 'change', required: true }],
        threshold: [{ validator: validateThresholdPass, trigger: 'blur' }],
        minThreshold: [{ validator: validateMinThresholdPass, trigger: 'blur' }],
        indexDetectionMode: [{ validator: validateIndexDetectionMode, trigger: 'change', required: true }],
        scheduleType: { validator: validateCheduleType, required: true, trigger: 'change' },
      },
      formData: {
        detectMode: '1',
        selectType: 0,
        quantityConfig: [],
        deviceQueryForm: {
          detectPhyStatus: '0',
        },

        isScreenshots: '',
        detectionMode: [],
        delaySipTimeOut: null,
        delayStreamTimeOut: null,
        delayIdrTimeOut: null,
        detectionTimeOut: null,
        checkRegion: {}, //治理前可调阅率
        indexDetectionMode: '',
        isUpdatePhyStatus: 0,
      },
      screenWidth: '',
      wangliCheckContent: [0, 1, 2, 3, 4, 5],
      oldIndexDetectionMode: '',
      isRecheck: '2',

      areaTreeData: [],
      checkedTreeData: [],

      callRate: null,
    };
  },
  async created() {
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    setTreeData() {
      let treeData = this.$util.common.deepCopy(
        this.$util.common.getTreeChildren(this.treeData, this.formData.regionCode),
      );
      treeData.forEach((item) => {
        item.value = this.formData.checkRegion[item.regionCode] || null;
        item.check = !!this.formData.checkRegion[item.regionCode];
      });
      this.areaTreeData = this.$util.common.arrayToJson(treeData, 'regionCode', 'parentCode');
    },
    async handleSubmit() {
      this.formData.checkRegion = {};
      this.getCheckedKeys(this.areaTreeData);
      if (Object.keys(this.formData.checkRegion).length === 0) {
        this.$Message.error('治理前可调阅率不能为空');
        return false;
      }
      const valid = await this.$refs['modalData'].validate();
      if (!valid) {
        return;
      }
      return this.$refs['commonForm'].handleSubmit();
    },
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    checkGroupChange(val) {
      if (!val.includes('3')) {
        this.$set(this.formData, 'isScreenshots', '2');
      } else {
        this.$set(this.formData, 'isScreenshots', '1');
      }
      if (val.includes('3') && !val.includes('2')) {
        this.formData.detectionMode.push('2');
      }
      this.formData.indexDetectionMode = `${Math.max(...this.formData.detectionMode)}`;
    },
    handleCheckDiabled(val) {
      if (!val.length) {
        return false;
      }
      const sortDetectionMode = JSON.parse(JSON.stringify(val)).sort((a, b) => a - b);
      sortDetectionMode.forEach((item) => {
        let startIndex = this.historyVideoIndexs.includes(this.indexType) ? 0 : 1;
        if (item > startIndex && item !== sortDetectionMode[sortDetectionMode.length - 1]) {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', true);
        } else {
          this.$set(this.moduleAction.checkConfig[item - 1], 'disabled', false);
        }
      });
    },
    getCheckResultIndexs() {
      let indexArr = [];
      this.checkResultTips.forEach((item) => {
        if (
          (!!item.hasOcr && !!this.formData.osdModel && this.formData.osdModel !== 'sdk') ||
          (!this.formData.osdModel && !item.hasOcr) ||
          ['VIDEO_GENERAL_OSD_CLOCK_ACCURACY', 'VIDEO_OSD_CLOCK_ACCURACY'].includes(this.indexType)
        ) {
          indexArr = indexArr.concat(item.indexs);
        }
      });
      return indexArr;
    },
    handleCheckMethods() {
      let modalWidth = this.screenWidth > 1890 ? '4rem' : '5rem';
      this.$emit('changeModalWidth', this.getCheckResultIndexs().includes(this.indexType) ? modalWidth : '3.7rem');
      this.handleCheckMethodsVal();
    },
    handleCheckMethodsVal() {
      if (this.checkMethodsKey !== 'videoQualityDetectMode' && this.formData[this.checkMethodsKey] === '1') {
        return false;
      }
      // this.$set(this.formData, 'useRecentCache', false)
      if (this.comprehensiveQualityIndex.includes(this.indexType) && this.formData[this.checkMethodsKey] === '3') {
        this.$set(this.formData, 'threshold', '90');
      }
      this.videoQualityCheckContent.forEach((item) => {
        if (this.formData[item.key]) {
          delete this.formData[item.key];
        }
      });
    },
    handleIndexDetectionModeChange(val) {
      this.oldIndexDetectionMode = val;
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },

    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handlekeyChange() {
      const detectionTimeOut = this.formData.detectionTimeOut.replace(/[^\d]/g, '');
      this.$set(this.formData, 'detectionTimeOut', detectionTimeOut);
    },
    checkAll(val, node, data) {
      this.$set(node.data, 'check', data.checkAll);
      if (node.childNodes) {
        if (data.checkAll) {
          this.checkParent(node, data.checkAll);
        } else {
          this.checkChildren([node], data.checkAll);
        }
        node.childNodes.map((item) => {
          this.$set(item.data, 'check', data.checkAll);
        });
      }
    },
    check(val, node) {
      this.checkParent(node, val);
      this.checkChildren([node], val);
    },
    checkParent(node, check = true) {
      this.$set(node.data, 'check', check);
      if (node.parent) {
        if (check) {
          this.checkParent(node.parent, check);
        }
      }
    },
    checkChildren(node, check) {
      node &&
        node.map((item) => {
          this.$set(item.data, 'checkAll', false);
          this.$set(item.data, 'check', check);
          if (item.childNodes) {
            this.checkChildren(item.childNodes);
          }
        });
    },
    clickBatchInput() {
      if (!this.callRate) {
        return this.$Message.error('请输入可调阅率');
      }
      this.batchInput(this.areaTreeData);
    },
    batchInput(data) {
      data.map((item) => {
        if (item.check) {
          this.$set(item, 'value', this.callRate);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },

    getCheckedKeys(data) {
      data.map((item) => {
        if (item.check) {
          this.formData.checkRegion[item.regionCode] = item.value;
        }
        if (item.children) {
          this.getCheckedKeys(item.children);
        }
      });
    },
    setCheckedKeys() {
      this.checkedTreeData.map((key) => {
        this.areaTreeData.map((item) => {
          if (item.code === key) {
            this.$set(item, 'check', true);
          }
        });
      });
    },
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
          };
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
        }
        this.setTreeData();
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
      treeData: 'common/getInitialAreaList',
    }),
    qualityCheckContent() {
      if (this.formData[this.checkMethodsKey] === '1') {
        return this.videoQualityCheckContent;
      } else if (this.formData[this.checkMethodsKey] === '3') {
        return this.wangliVideoQualityCheckContent;
      }
      return [];
    },
  },
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.form-content {
  .params-suffix {
    color: #fff;
    margin-left: 5px;
  }
}
</style>
