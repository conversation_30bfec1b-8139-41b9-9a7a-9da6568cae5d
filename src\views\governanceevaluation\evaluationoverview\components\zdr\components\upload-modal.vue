<template>
  <ui-modal v-model="captureVisible" :title="title" :footerHide="true" :width="width">
    <div class="content" v-if="captureVisible">
      <!-- <div> <span>数据类型：</span> 重点人员 </div> -->
      <div><span>抓拍时间：</span> {{ currentObj.shotTime || '未知' }}</div>
      <div><span>上传时间：</span> {{ currentObj.receiveTime || '未知' }}</div>
      <div><span>时延：</span> {{ currentObj.delayHourStr || '未知' }}</div>
    </div>
  </ui-modal>
</template>

<script>
export default {
  name: 'captureDetail',
  props: {},
  data() {
    return {
      width: 600,
      title: '上传超时原因',
      captureVisible: false,
      currentObj: {},
    };
  },
  async created() {},
  computed: {},
  methods: {
    init(item) {
      this.currentObj = item;
      // var param = {
      //   id: item.id,
      // }
      // this.$http.post(api.queryFocusTrackRealDetailById, param).then((res) => {
      //   this.currentObj = res.data.data
      // })
    },
    show(item) {
      this.captureVisible = true;
      this.init(item);
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.captureVisible = val;
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.content {
  padding-left: 160px;
  color: #fff;
  height: 150px;
  div {
    position: relative;
    margin-top: 10px;
    padding-left: 80px;
    span {
      position: absolute;
      left: 0;
    }
  }
}
</style>
