<template>
  <div class="upload-list" :class="size ? 'upload-' + size : ''">
    <template>
      <!-- 已上传图片展示 -->
      <div class="img-content" v-if="JSON.stringify(uploadImgUrl) !== '{}'">
        <img :src="uploadImgUrl.imageUrl || uploadImgUrl.fileUrl" alt />
        <p class="delete-mask" @click.stop="minImgDeleteHandle()">
          <Icon type="md-close" />
        </p>
      </div>
      <!-- 上传图片按钮 -->
      <Upload
        :beforeUpload="(file) => beforeUpload(file)"
        action="*"
        :show-upload-list="false"
      >
        <template>
          <p class="upload-btn">点击上传图片</p>
        </template>
      </Upload>
    </template>

    <!-- 图片上传成功后，选择图片 -->
    <image-recognition
      ref="imageRecognition"
      :dataCopper="dataCopper"
      :tempUrl="tempUrl"
      @getMinImgUrl="getMinImgUrl"
      @destroy="imageRecognitionVisible = false"
      v-if="imageRecognitionVisible"
    />
    <canvas v-show="false" id="mycanvas"></canvas>
    <img v-show="false" :src="imageSrc" alt />
  </div>
</template>
<script>
import imageRecognition from "./image-recognition";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { imgUpload } from "@/api/number-cube";
export default {
  name: "SearchPictures",
  components: { imageRecognition },
  props: {
    size: {
      type: String,
      default: "", // large、 default、 small
    },
    value: {
      type: Object,
      default: () => {},
    },
    // 1人脸2车辆
    algorithmType: {
      type: [String, Number],
      default: null,
    },
    uploadUlr: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    uploadImgUrl(val) {
      if (val) {
        console.log(val, "val");
        this.$emit("input", val);
        this.imgUrlChange();
      }
    },
    value: {
      handler(val) {
        if (val) {
          this.uploadImgUrl = val;
        } else {
          this.uploadImgUrl = {};
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      uploadImgUrl: {},
      dataCopper: [],
      tempUrl: "",
      imageRecognitionVisible: false,
      baseImg: null,
      bigImageBase64: null,
      imageSrc: "",
    };
  },
  methods: {
    //删除单个图片
    minImgDeleteHandle() {
      this.uploadImgUrl = {};
      this.$forceUpdate();
      this.imgUrlChange();
      this.$emit("deleteImgUrl", this.uploadImgUrl);
    },
    // 获取
    async getMinImgUrl(data) {
      const response = await this.getBase64ByImageCoordinate(data);
      const _d = response.data;
      this.uploadImgUrl = {
        fileUrl: "data:image/jpeg;base64," + _d.imageBase,
        feature: _d.feature,
        imageBase: _d.imageBase,
        imageUrl: "data:image/jpeg;base64," + _d.imageBase,
      };
      this.$forceUpdate();
      this.imgUrlChange();
    },
    imgUrlChange() {
      this.$emit("imgUrlChange", this.uploadImgUrl);
    },
    //上传图片
    beforeUpload(file) {
      this.getBase64(file).then((res) => {
        this.bigImageBase64 = res;
      });
      let isAskFile = false;
      isAskFile = /\.(PNG|JPG|JPEG|BMP|png|jpg|jpeg|bmp)$/.test(file.name);
      if (!isAskFile) {
        this.$Message.error("请上传png、jpg、jpeg或bmp格式图片！");
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error("上传文件大小不能超过 30MB!");
      }
      if (!isAskFile || !isLt30M) return false;
      this.fileUploading = this.$Message.loading({
        content: "文件上传中...",
        duration: 0,
      });
      let fileData = new FormData();
      fileData.append("file", file);
      fileData.append("algorithmType", this.algorithmType);
      picturePick(fileData)
        .then((res) => {
          if (res.data.length == 0) {
            this.$Message.error("没有识别出目标，请选择其它图片");
            return;
          }
          if (this.uploadUlr) {
            this.controlImageInfo(file);
          } else {
            if (res.data.length == 1) {
              this.tempUrl = window.URL.createObjectURL(file);
              this.oneImageInfo(res.data);
              return;
            }
            this.imageRecognition();
            this.imageDataHandle(res.data);
            // this.dataCopper = dataCopper
            this.tempUrl = window.URL.createObjectURL(file);
          }
        })
        .finally(() => {
          this.fileUploading();
          this.fileUploading = null;
        });
      return false;
    },
    // 打开图片识别器
    imageRecognition(data) {
      this.imageRecognitionVisible = true;
      this.$nextTick(() => {
        this.$refs.imageRecognition.init(data);
      });
    },
    // 布控中识别有头像 图片全传
    controlImageInfo(file) {
      const fd = new FormData();
      fd.append("file", file);
      imgUpload(fd).then((res) => {
        let obj = {
          fileUrl: res.data.fileUrl,
        };
        this.uploadImgUrl = obj;
        this.imgUrlChange();
      });
    },
    /**
     * 一张图片操作
     */
    async oneImageInfo(list) {
      let _that = this;
      await this.imageDataHandle(list);
      const response = await this.getBase64ByImageCoordinate(list[0]);
      const data = response.data;
      this.$nextTick(() => {
        var canvas = document.getElementById("mycanvas");
        const { left, top, right, bottom } = list[0];
        canvas.width = right - left;
        canvas.height = bottom - top;
        let ctx = canvas.getContext("2d");
        let image = new Image();
        image.crossOrigin = "anonymous"; // 网络图片跨域
        // image.src = 'https://img0.baidu.com/it/u=2484538263,336635826&fm=26&fmt=auto&gp=0.jpg'
        image.src = this.tempUrl;
        image.onload = function () {
          ctx.drawImage(image, left, top, right, bottom, 0, 0, right, bottom);
          // ctx.drawImage(image, 295, 40, 100, 100, 0 ,0, 100, 100)
          // var img = canvas.toDataURL("image/jpeg", 1); // toDataUrl可以接收2个参数，参数一：图片类型，参数二： 图片质量0-1（不传默认为0.92）
          // 将图片转成base64格式
          _that.imageSrc = canvas.toDataURL("image/png");
          var obj = {
            feature: data.feature,
            fileUrl: _that.imageSrc,
            imageBase: data.imageBase,
            imageUrl: data.imageUrl,
          };
          _that.uploadImgUrl = obj;
          _that.imgUrlChange();
        };
      });
    },

    /**
     * 图片转base64
     */
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        const reader = new FileReader();
        let imgResult = "";
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },

    /**
     * 图片上传返回数据处理
     * feature: 返回后台用
     *
     * 后台返回的数据结构：bottom, left, right, top
     * 返回值为图片的2个坐标点，分别为左上和右下
     * 左上：left top
     * 右下：right bottom
     */
    imageDataHandle(list) {
      var arr = [];
      list.forEach((item) => {
        var obj = {
          x: item.left,
          y: item.top,
          width: item.right - item.left,
          height: item.bottom - item.top,
          feature: item.feature,
          imageBase: item.imageBase,
          initData: item, // 用于后续请求
        };

        arr.push(obj);
      });
      this.dataCopper = arr;
    },
    //base64转Blob
    convertBase64UrlToBlob(urlData) {
      //去掉url的头，并转换为byte
      var split = urlData.split(",");
      var bytes = window.atob(split[1]);
      //处理异常,将ascii码小于0的转换为大于0
      var ab = new ArrayBuffer(bytes.length);
      var ia = new Uint8Array(ab);
      for (var i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i);
      }
      return new Blob([ab], { type: split[0] });
    },
    // 根据图片坐标截取图片base64
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },
  },
};
</script>
<style lang="less" scoped>
.upload-list {
  width: 100%;
  height: inherit;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  .upload-btn {
    cursor: pointer;
  }
  .img-content {
    display: flex;
    align-items: center;
    justify-content: center;
    // position: relative;
    // width: 320px;
    // height: 400px;
    // width: 100%;
    // height: 100%;
    border: 1px solid #2c86f8;
    // background: rgb(223, 227, 237, .8);
    background: #fff;
    position: absolute;
    top: 22px;
    left: 22px;
    border-radius: 4px;
    width: 280px;
    height: 280px;
    img {
      display: block;
      height: auto;
      width: 100%;
      max-height: 100%;
    }
    .delete-mask {
      position: absolute;
      top: 4px;
      right: 4px;
      width: 32px;
      height: 32px;
      background: #000000;
      opacity: 0.2;
      border-radius: 50%;
      cursor: pointer;
      text-align: center;
      line-height: 32px;
      .ivu-icon-md-close {
        font-size: 24px;
        color: #fff;
      }
    }
  }
}
</style>