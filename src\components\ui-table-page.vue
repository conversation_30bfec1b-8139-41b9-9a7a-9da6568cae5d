<template>
  <div class="auto-fill">
    <slot name="search"></slot>
    <div class="left-div auto-fill">
      <ui-table
        v-if="columns"
        ref="table"
        :class="tableData.length > 0 ? '' : 'ui-table-scroll-nodata'"
        :loading="loading"
        :columns="columns"
        :data="tableData"
        :minus-height="minusHeight"
        class="ui-table auto-fill"
        v-on="$listeners"
      >
        <template
          v-for="(item, index) in slotList"
          #[item.slot]="{ row, index }"
        >
          <slot :name="item.slot" :row="row" :index="index" :item="item"></slot>
        </template>
      </ui-table>
    </div>
    <ui-page
      v-if="paging"
      :current="pageData.pageNumber"
      :page-size="pageData.pageSize"
      :total="pageData.totalCount"
      class="page_content"
      @pageChange="changePage"
      @pageSizeChange="changePageSize"
    ></ui-page>
  </div>
</template>
<script>
export default {
  name: "UiTablePage",
  components: {
    UiTable: require("@/components/ui-table/ui-table.vue").default,
  },
  props: {
    columns: {
      // 表头
      type: Array,
      default() {
        return [];
      },
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    minusHeight: {
      // 表格计算高度
      type: [Number, String],
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: "entities",
    },
    paging: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      pageData: {
        totalCount: 0,
        pageNumber: 1,
        pageSize: 10,
      },
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
        pageNumber: 1,
        pageSize: 10,
      },
      tableData: [],
    };
  },
  computed: {
    slotList() {
      const array = [];
      this.columns.map((val) => {
        if (val.slot) {
          array.push(val);
        }
      });
      return array;
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 此方法在父组件调用表格方法传true重置条件
    async info(boolen) {
      if (boolen) {
        this.tableData = [];
        this.pageData.totalCount = 0;
        await this.reset();
      }
      if (!this.paging) {
        this.searchData = {};
      }
      this.loading = true;
      const result = this.loadData(this.pageData);
      if (typeof result === "object" && typeof result.then === "function") {
        result
          .then((res) => {
            if (res) {
              this.tableData = res[this.listKey];
              this.pageData.totalCount = res.total;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.tableData = [];
        this.pageData.totalCount = 0;
        this.loading = false;
      }
    },
    reset() {
      this.pageData = { totalCount: 0, pageNumber: 1, pageSize: 20 };
      this.searchData = {
        params: { pageNumber: 1, pageSize: 20 },
        pageNumber: 1,
        pageSize: 20,
      };
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.searchData.pageNumber = val;
      this.pageData.pageNumber = val;
      this.info();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.searchData.pageNumber = 1;
      this.pageData.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.info();
    },
  },
};
</script>
<style lang="less" scoped>
.left-div {
  position: relative;
  width: 100%;
  min-height: 1px;
}
.ui-table {
  width: 100%;
}
.ui-table-scroll-nodata {
  /deep/ .ivu-table-tip {
    overflow-x: auto;
  }
}
</style>
