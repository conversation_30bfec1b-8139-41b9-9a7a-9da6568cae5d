<template>
  <!-- 大图右上角--时钟相关信息 -->
  <div class="card-content">
    <slot name="pseudo-class"></slot>
    <div v-for="(topItem, topIndex) in currentAlgorithmInfo.clockData" :key="`${topIndex}-${topItem.key}`">
      <div class="p-text">
        {{ topItem.value }}
      </div>
      <template v-if="topItem.tooltipTextList.length">
        <div v-for="(tipItem, tipIndex) in topItem.tooltipTextList" :key="`${topIndex}-${tipIndex}`">
          <span class="sub-title" v-if="topItem.tooltipTextList.length > 1 && showAlgorithmName">
            {{ tipItem.algorithmName }}：
          </span>
          <p class="p-tip" v-for="(text, textIndex) in tipItem.textList" :key="`${textIndex}-${tipIndex}`">
            {{ text }}
          </p>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentAlgorithmInfo: {
      type: Object,
    },
    showAlgorithmName: {
      type: Boolean,
      default: true,
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .p-tip {
    color: rgba(255, 255, 255, 0.5) !important;
  }
}
.card-content {
  padding: 8px 10px;
  background: var(--bg-card);
  font-size: 13px;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  color: var(--color-content);
  line-height: 21px;
  .sub-title {
    font-size: 12px;
    color: var(--color-sub-title);
  }
  .p-text {
    position: relative;
    padding: 1px 0 1px 18px;
    color: var(--color-failed);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      background: var(--bg-quadrate-outside);
      transform: rotate(45deg);
      position: absolute;
      top: 5px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      background: var(--bg-quadrate-interior);
      transform: rotate(45deg);
      position: absolute;
      top: 8px;
      left: 3px;
    }
  }
  .p-tip {
    position: relative;
    padding: 1px 0 1px 18px;
    color: rgba(0, 0, 0, 0.8);
    &::before {
      content: ' ';
      width: 11px;
      height: 11px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-outside);
      position: absolute;
      top: 6px;
      left: 0;
    }
    &::after {
      content: ' ';
      width: 5px;
      height: 5px;
      display: inline-block;
      border-radius: 50%;
      background: var(--bg-circle-interior);
      position: absolute;
      top: 9px;
      left: 3px;
    }
  }
}
</style>
