<template>
  <div>
    <div class="search-list">
      <ui-label class="inline right-margin mb-lg" label="设备编码" :width="70">
        <Input
          v-model="searchData.deviceId"
          class="input-width"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="设备名称" :width="70">
        <Input
          v-model="searchData.deviceName"
          class="input-width"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
          clearable
        ></Input>
      </ui-label>
      <ui-label
        v-for="(item, index) in operationList"
        :key="index"
        class="inline right-margin mb-lg"
        :label="item.label"
        :width="70"
      >
        <Select class="input-width" v-model="searchData[item.key]" :placeholder="item.placeholder" clearable>
          <Option
            v-for="(optionItem, optionIndex) in item.data"
            :key="'option' + optionIndex"
            :value="optionItem.value"
            >{{ optionItem.label }}</Option
          >
        </Select>
      </ui-label>
      <div class="inline mb-lg">
        <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
        <Button type="default" @click="reset"> 重置 </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { operationList } from '../staticfields';
export default {
  name: 'search-list',
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        online: '',
        normal: '',
        canPlay: '',
        qualified: '',
        deviceName: '',
        deviceId: '',
      },
      operationList: [],
    };
  },
  created() {
    this.operationList = operationList;
  },
  async mounted() {
    await this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.checkStatuses = [];
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', this.searchData);
      });
    },
  },
  watch: {
    paramsData(val) {
      this.searchData.qualified = val.isOnline === '1' ? '1' : '2';
      this.search();
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.right-margin {
  margin-right: 30px;
}
.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.right-margin {
  margin-right: 15px;
}
.input-width {
  width: 140px;
}
</style>
