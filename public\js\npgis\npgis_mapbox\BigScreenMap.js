function initWater(){THREE.Math.sign=Math.sign,THREE.Reflector=function(t,e){THREE.Mesh.call(this,t),this.type="Reflector";var l=this,i=void 0!==(e=e||{}).color?new THREE.Color(e.color):new THREE.Color(8355711),r=e.textureWidth||512,a=e.textureHeight||512,h=e.clipBias||0,o=e.shader||THREE.Reflector.ReflectorShader,p=void 0!==e.recursion?e.recursion:0,u=new THREE.Plane,c=new THREE.Vector3,d=new THREE.Vector3,f=new THREE.Vector3,m=new THREE.Matrix4,v=new THREE.Vector3(0,0,-1),g=new THREE.Vector4,y=new THREE.Vector3,x=new THREE.Vector3,_=new THREE.Vector4,b=new THREE.Matrix4,w=new THREE.PerspectiveCamera,s={minFilter:THREE.LinearFilter,magFilter:THREE.LinearFilter,format:THREE.RGBFormat,stencilBuffer:!1},M=new THREE.WebGLRenderTarget(r,a,s);THREE.Math.isPowerOfTwo(r)&&THREE.Math.isPowerOfTwo(a)||(M.texture.generateMipmaps=!1);var n=new THREE.ShaderMaterial({uniforms:THREE.UniformsUtils.clone(o.uniforms),fragmentShader:o.fragmentShader,vertexShader:o.vertexShader});n.uniforms.tDiffuse.value=M.texture,n.uniforms.color.value=i,n.uniforms.textureMatrix.value=b,this.material=n,this.onBeforeRender=function(t,e,i){if("recursion"in i.userData){if(i.userData.recursion===p)return;i.userData.recursion++}if(d.setFromMatrixPosition(l.matrixWorld),f.setFromMatrixPosition(i.matrixWorld),m.extractRotation(l.matrixWorld),c.set(0,0,1),c.applyMatrix4(m),y.subVectors(d,f),!(0<y.dot(c))){y.reflect(c).negate(),y.add(d),m.extractRotation(i.matrixWorld),v.set(0,0,-1),v.applyMatrix4(m),v.add(f),x.subVectors(d,v),x.reflect(c).negate(),x.add(d),w.position.copy(y),w.up.set(0,1,0),w.up.applyMatrix4(m),w.up.reflect(c),w.lookAt(x),w.far=i.far,w.updateMatrixWorld(),w.projectionMatrix.copy(i.projectionMatrix),w.userData.recursion=0,b.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),b.multiply(w.projectionMatrix),b.multiply(w.matrixWorldInverse),b.multiply(l.matrixWorld),u.setFromNormalAndCoplanarPoint(c,d),u.applyMatrix4(w.matrixWorldInverse),g.set(u.normal.x,u.normal.y,u.normal.z,u.constant);var r=w.projectionMatrix;_.x=(Math.sign(g.x)+r.elements[8])/r.elements[0],_.y=(Math.sign(g.y)+r.elements[9])/r.elements[5],_.z=-1,_.w=(1+r.elements[10])/r.elements[14],g.multiplyScalar(2/g.dot(_)),r.elements[2]=g.x,r.elements[6]=g.y,r.elements[10]=g.z+1-h,r.elements[14]=g.w,l.visible=!1;var a=t.getRenderTarget(),o=t.vr.enabled,s=t.shadowMap.autoUpdate;t.vr.enabled=!1,t.shadowMap.autoUpdate=!1,t.setRenderTarget(M),t.clear(),t.render(e,w),t.vr.enabled=o,t.shadowMap.autoUpdate=s,t.setRenderTarget(a);var n=i.viewport;void 0!==n&&t.state.viewport(n),l.visible=!0}},this.getRenderTarget=function(){return M}},THREE.Reflector.prototype=Object.create(THREE.Mesh.prototype),THREE.Reflector.prototype.constructor=THREE.Reflector,THREE.Reflector.ReflectorShader={uniforms:{color:{value:null},tDiffuse:{value:null},textureMatrix:{value:null}},vertexShader:["uniform mat4 textureMatrix;","varying vec4 vUv;","void main() {","\tvUv = textureMatrix * vec4( position, 1.0 );","\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","}"].join("\n"),fragmentShader:["uniform vec3 color;","uniform sampler2D tDiffuse;","varying vec4 vUv;","float blendOverlay( float base, float blend ) {","\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );","}","vec3 blendOverlay( vec3 base, vec3 blend ) {","\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );","}","void main() {","\tvec4 base = texture2DProj( tDiffuse, vUv );","\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );","}"].join("\n")},THREE.Refractor=function(t,e){THREE.Mesh.call(this,t),this.type="Refractor";var n=this,i=void 0!==(e=e||{}).color?new THREE.Color(e.color):new THREE.Color(8355711),r=e.textureWidth||512,a=e.textureHeight||512,o=e.clipBias||0,s=e.shader||THREE.Refractor.RefractorShader,l=new THREE.PerspectiveCamera;l.matrixAutoUpdate=!1,l.userData.refractor=!0;var h=new THREE.Plane,p=new THREE.Matrix4,u={minFilter:THREE.LinearFilter,magFilter:THREE.LinearFilter,format:THREE.RGBFormat,stencilBuffer:!1},c=new THREE.WebGLRenderTarget(r,a,u);THREE.Math.isPowerOfTwo(r)&&THREE.Math.isPowerOfTwo(a)||(c.texture.generateMipmaps=!1),this.material=new THREE.ShaderMaterial({uniforms:THREE.UniformsUtils.clone(s.uniforms),vertexShader:s.vertexShader,fragmentShader:s.fragmentShader,transparent:!1}),this.material.uniforms.color.value=i,this.material.uniforms.tDiffuse.value=c.texture,this.material.uniforms.textureMatrix.value=p;var d,f,m,v,g,y,x,_,b,w,M,E,T=(d=new THREE.Vector3,f=new THREE.Vector3,m=new THREE.Matrix4,v=new THREE.Vector3,g=new THREE.Vector3,function(t){return d.setFromMatrixPosition(n.matrixWorld),f.setFromMatrixPosition(t.matrixWorld),v.subVectors(d,f),m.extractRotation(n.matrixWorld),g.set(0,0,1),g.applyMatrix4(m),v.dot(g)<0}),R=(y=new THREE.Vector3,x=new THREE.Vector3,_=new THREE.Quaternion,b=new THREE.Vector3,function(){n.matrixWorld.decompose(x,_,b),y.set(0,0,1).applyQuaternion(_).normalize(),y.negate(),h.setFromNormalAndCoplanarPoint(y,x)}),P=(w=new THREE.Plane,M=new THREE.Vector4,E=new THREE.Vector4,function(t){l.matrixWorld.copy(t.matrixWorld),l.matrixWorldInverse.getInverse(l.matrixWorld),l.projectionMatrix.copy(t.projectionMatrix),l.far=t.far,w.copy(h),w.applyMatrix4(l.matrixWorldInverse),M.set(w.normal.x,w.normal.y,w.normal.z,w.constant);var e=l.projectionMatrix;E.x=(Math.sign(M.x)+e.elements[8])/e.elements[0],E.y=(Math.sign(M.y)+e.elements[9])/e.elements[5],E.z=-1,E.w=(1+e.elements[10])/e.elements[14],M.multiplyScalar(2/M.dot(E)),e.elements[2]=M.x,e.elements[6]=M.y,e.elements[10]=M.z+1-o,e.elements[14]=M.w});this.onBeforeRender=function(t,e,i){var r;!0!==i.userData.refractor&&(!0!=!T(i)&&(R(),r=i,p.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),p.multiply(r.projectionMatrix),p.multiply(r.matrixWorldInverse),p.multiply(n.matrixWorld),P(i),function(t,e,i){n.visible=!1;var r=t.getRenderTarget(),a=t.vr.enabled,o=t.shadowMap.autoUpdate;t.vr.enabled=!1,t.shadowMap.autoUpdate=!1,t.setRenderTarget(c),t.clear(),t.render(e,l),t.vr.enabled=a,t.shadowMap.autoUpdate=o,t.setRenderTarget(r);var s=i.viewport;void 0!==s&&t.state.viewport(s),n.visible=!0}(t,e,i)))},this.getRenderTarget=function(){return c}},THREE.Refractor.prototype=Object.create(THREE.Mesh.prototype),THREE.Refractor.prototype.constructor=THREE.Refractor,THREE.Refractor.RefractorShader={uniforms:{color:{value:null},tDiffuse:{value:null},textureMatrix:{value:null}},vertexShader:["uniform mat4 textureMatrix;","varying vec4 vUv;","void main() {","\tvUv = textureMatrix * vec4( position, 1.0 );","\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","}"].join("\n"),fragmentShader:["uniform vec3 color;","uniform sampler2D tDiffuse;","varying vec4 vUv;","float blendOverlay( float base, float blend ) {","\treturn( base < 0.5 ? ( 2.0 * base * blend ) : ( 1.0 - 2.0 * ( 1.0 - base ) * ( 1.0 - blend ) ) );","}","vec3 blendOverlay( vec3 base, vec3 blend ) {","\treturn vec3( blendOverlay( base.r, blend.r ), blendOverlay( base.g, blend.g ), blendOverlay( base.b, blend.b ) );","}","void main() {","\tvec4 base = texture2DProj( tDiffuse, vUv );","\tgl_FragColor = vec4( blendOverlay( base.rgb, color ), 1.0 );","}"].join("\n")},THREE.Water=function(t,e){THREE.Mesh.call(this,t),this.type="Water";var s=this,i=void 0!==(e=e||{}).color?new THREE.Color(e.color):new THREE.Color(16777215),r=e.textureWidth||512,a=e.textureHeight||512,o=e.clipBias||0,n=e.flowDirection||new THREE.Vector2(1,0),l=e.flowSpeed||.03,h=e.reflectivity||.5,p=e.scale||1,u=e.shader||THREE.Water.WaterShader,c=new THREE.TextureLoader,d=e.flowMap||void 0,f=e.normalMap0||c.load(NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/water/Water_1_M_Normal.jpg"),m=e.normalMap1||c.load(NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/water/Water_2_M_Normal.jpg"),v=.15,g=.5*v,y=new THREE.Matrix4,x=new THREE.Clock;if(void 0!==THREE.Reflector)if(void 0!==THREE.Refractor){var _=new THREE.Reflector(t,{textureWidth:r,textureHeight:a,clipBias:o}),b=new THREE.Refractor(t,{textureWidth:r,textureHeight:a,clipBias:o});_.matrixAutoUpdate=!1,b.matrixAutoUpdate=!1,this.material=new THREE.ShaderMaterial({uniforms:THREE.UniformsUtils.merge([THREE.UniformsLib.fog,u.uniforms]),vertexShader:u.vertexShader,fragmentShader:u.fragmentShader,transparent:!1,fog:!0}),void 0!==d?(this.material.defines.USE_FLOWMAP="",this.material.uniforms.tFlowMap={type:"t",value:d}):this.material.uniforms.flowDirection={type:"v2",value:n},f.wrapS=f.wrapT=THREE.RepeatWrapping,m.wrapS=m.wrapT=THREE.RepeatWrapping,this.material.uniforms.tReflectionMap.value=_.getRenderTarget().texture,this.material.uniforms.tNormalMap0.value=f,this.material.uniforms.tNormalMap1.value=m,this.material.uniforms.color.value=i,this.material.uniforms.reflectivity.value=h,this.material.uniforms.textureMatrix.value=y,this.material.uniforms.config.value.x=0,this.material.uniforms.config.value.y=g,this.material.uniforms.config.value.z=g,this.material.uniforms.config.value.w=p,this.onBeforeRender=function(t,e,i){var r,a,o;r=i,y.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),y.multiply(r.projectionMatrix),y.multiply(r.matrixWorldInverse),y.multiply(s.matrixWorld),a=x.getDelta(),(o=s.material.uniforms.config).value.x+=l*a,o.value.y=o.value.x+g,o.value.x>=v?(o.value.x=0,o.value.y=g):o.value.y>=v&&(o.value.y=o.value.y-v),s.visible=!1,_.matrixWorld.copy(s.matrixWorld),b.matrixWorld.copy(s.matrixWorld),_.onBeforeRender(t,e,i),b.onBeforeRender(t,e,i),s.visible=!0}}else console.error("THREE.Water: Required component THREE.Refractor not found.");else console.error("THREE.Water: Required component THREE.Reflector not found.")},THREE.Water.prototype=Object.create(THREE.Mesh.prototype),THREE.Water.prototype.constructor=THREE.Water,THREE.Water.WaterShader={uniforms:{color:{type:"c",value:null},reflectivity:{type:"f",value:0},tReflectionMap:{type:"t",value:null},tRefractionMap:{type:"t",value:null},tNormalMap0:{type:"t",value:null},tNormalMap1:{type:"t",value:null},textureMatrix:{type:"m4",value:null},config:{type:"v4",value:new THREE.Vector4}},vertexShader:["#include <fog_pars_vertex>","#include <logdepthbuf_pars_vertex>","uniform mat4 textureMatrix;","varying vec4 vCoord;","varying vec2 vUv;","varying vec3 vToEye;","void main() {","\tvUv = uv;","\tvCoord = textureMatrix * vec4( position, 1.0 );","\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );","\tvToEye = cameraPosition - worldPosition.xyz;","\tvec4 mvPosition =  viewMatrix * worldPosition;","\tgl_Position = projectionMatrix * mvPosition;","\t#include <logdepthbuf_vertex>","\t#include <fog_vertex>","}"].join("\n"),fragmentShader:["#include <common>","#include <fog_pars_fragment>","#include <logdepthbuf_pars_fragment>","uniform sampler2D tReflectionMap;","uniform sampler2D tRefractionMap;","uniform sampler2D tNormalMap0;","uniform sampler2D tNormalMap1;","#ifdef USE_FLOWMAP","\tuniform sampler2D tFlowMap;","#else","\tuniform vec2 flowDirection;","#endif","uniform vec3 color;","uniform float reflectivity;","uniform vec4 config;","varying vec4 vCoord;","varying vec2 vUv;","varying vec3 vToEye;","void main() {","\t#include <logdepthbuf_fragment>","\tfloat flowMapOffset0 = config.x;","\tfloat flowMapOffset1 = config.y;","\tfloat halfCycle = config.z;","\tfloat scale = config.w;","\tvec3 toEye = normalize( vToEye );","\tvec2 flow;","\t#ifdef USE_FLOWMAP","\t\tflow = texture2D( tFlowMap, vUv ).rg * 2.0 - 1.0;","\t#else","\t\tflow = flowDirection;","\t#endif","\tflow.x *= - 1.0;","\tvec4 normalColor0 = texture2D( tNormalMap0, ( vUv * scale ) + flow * flowMapOffset0 );","\tvec4 normalColor1 = texture2D( tNormalMap1, ( vUv * scale ) + flow * flowMapOffset1 );","\tfloat flowLerp = abs( halfCycle - flowMapOffset0 ) / halfCycle;","\tvec4 normalColor = mix( normalColor0, normalColor1, flowLerp );","\tvec3 normal = normalize( vec3( normalColor.r * 2.0 - 1.0, normalColor.b,  normalColor.g * 2.0 - 1.0 ) );","\tfloat theta = max( dot( toEye, normal ), 0.0 );","\tfloat reflectance = reflectivity + ( 1.0 - reflectivity ) * pow( ( 1.0 - theta ), 5.0 );","\tvec3 coord = vCoord.xyz / vCoord.w;","\tvec2 uv = coord.xy + coord.z * normal.xz * 0.05;","\tvec4 reflectColor = texture2D( tReflectionMap, vec2( 1.0 - uv.x, uv.y ) );","\tvec4 refractColor = texture2D( tRefractionMap, uv );","   vec4 color1 = vec4(93.0/255.0,159.0/255.0,216.0/255.0,1.0);","\tgl_FragColor = vec4( color, 1.0 ) * mix( refractColor, reflectColor, reflectance );","\tgl_FragColor = mix( color1, gl_FragColor, 0.5);","\t#include <tonemapping_fragment>","\t#include <encodings_fragment>","\t#include <fog_fragment>","}"].join("\n")}}window.BigScreenMap={},BigScreenMap.VERSION_NUMBER="v2.5.2",BigScreenMap.ShapRegion=function(t,e,i){this.geoRegion=e;var r=++NPMap.LayerIndex;this._layerID="RegionLayer"+r,this.CLASS_NAME="BigScreenMap.ShapRegion",this._map=t;var a=GeoJSON.read(e.geometry);this.fullExtent=a.getExtent(),this._layers=[],this._regions=[],this.acvtiveLayer=null,this.acvtiveRegion=null,this.opts=i||{},this.fillColor=this.opts.fillColor?this.opts.fillColor:"#1a65fb",this.fillImage=this.opts.fillImage,this.lineColor=this.opts.lineColor?this.opts.lineColor:"#FFFFFF",this.lineWidth=this.opts.lineWidth?this.opts.lineWidth:1,this.outBorder=this.opts.outBorder?this.opts.outBorder:4,this.sharderFillColor=this.opts.sharderFillColor?this.opts.sharderFillColor:"#3961b1",this.sharderLineColor=this.opts.sharderLineColor?this.opts.sharderLineColor:"#1040a0",this.opacity=this.opts.opacity?this.opts.opacity:.8,this.sharderOpacity=this.opts.sharderOpacity?this.opts.sharderOpacity:.5,this.useGradual=!1!==this.opts.useGradual||this.opts.useGradual,this.hoverColor=this.opts.hoverColor?this.opts.hoverColor:"#3778fb",this.hoverWidth=this.opts.hoverWidth?this.opts.hoverWidth:0,this.hoverStrokeColor=this.opts.hoverStrokeColor?this.opts.hoverStrokeColor:"#FFFFFF",this.hoverStrokeWidth=this.opts.hoverStrokeWidth?this.opts.hoverStrokeWidth:4,this.isShowFont=!1!==this.opts.isShowFont,this.fontSize=this.opts.fontSize?this.opts.fontSize:14,this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.sharderWidth=this.opts.sharderWidth?this.opts.sharderWidth:20,this.minZoom=this.opts.minZoom?this.opts.minZoom:7,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:12,this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:function(){},this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:function(){},this.click=this.opts.click?this.opts.click:function(){},this.fontOffset=this.opts.fontOffset?this.opts.fontOffset:[0,0],this.fontHaloColor=this.fontHaloColor?this.fontHaloColor:"#183b67",this.fontHaloWidth=this.fontHaloWidth?this.fontHaloWidth:.5,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this.labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":1,"text-halo-color":this.fontHaloColor,"text-halo-width":this.fontHaloWidth},filter:["==","$type","Point"]},this.addGeoRegions(e)},BigScreenMap.ShapRegion.prototype._getFeature=function(t){if(this._map.getZoom()>=this.maxZoom)return[];for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf(this._layerID+"_polygon")){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var r=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(r&&0<r.length)return[r[0]]}return[]},BigScreenMap.ShapRegion.prototype._mouseout=function(){this.mouseOut&&this.acvtiveRegion&&(this._map._obj.getCanvas().style.cursor="",this.mouseOut(this.acvtiveRegion),this.acvtiveRegion=null,this.refresh())},BigScreenMap.ShapRegion.prototype._initInteractive=function(t){this._map._events.through=!0;var h=this;!this._moveFun&&(this._moveFun=function(t){var e=h._map._obj.getBearing(),i=h._map._obj.getPitch(),r=i*h.sharderWidth/60,a=0-Math.sin(e/180*Math.PI)*r,o=Math.cos(e/180*Math.PI)*r;h._map._obj.setPaintProperty(h._layerID+"_sharderPolygon","fill-translate",[a,o]);var s=i*h.hoverWidth/60,n=Math.sin(e/180*Math.PI)*s,l=0-Math.cos(e/180*Math.PI)*s;h._map._obj.setPaintProperty(h._layerID+"_polygonActive","fill-translate",[n,l]),h._map._obj.setPaintProperty(h._layerID+"_activeLine","line-translate",[n,l])}),!this._mouseMoveFun&&(this._mouseMoveFun=function(t){var e=h._getFeature(t);0!=e.length?h._map.getZoom()>=h.maxZoom||h.acvtiveRegion&&e[0].properties.id==h.acvtiveRegion.id||(h._mouseout(),e&&0<e.length&&(h._map._obj.getCanvas().style.cursor="pointer",h.acvtiveRegion=h._regions[e[0].properties.index],h.mouseOver&&h.acvtiveRegion&&(h.refresh(),h.mouseOver(h.acvtiveRegion)))):h._mouseout()}),!this._clickFun&&(this._clickFun=function(){h.acvtiveRegion&&h.click(h.acvtiveRegion)}),t?(this._map._obj.off("move",this._moveFun),this._map._obj.off("mousemove",this._mouseMoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("move",this._moveFun),this._map._obj.on("mousemove",this._mouseMoveFun),this._map._obj.on("click",this._clickFun))},BigScreenMap.ShapRegion.prototype.resetGeoRegions=function(t){this._source.data.features=[],this._regions=[],this._addData(t),this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},BigScreenMap.ShapRegion.prototype.addGeoRegions=function(t){this._addData(t),this._map._obj.addSource(this._layerID+"_source",this._source),this._addLayers(),this._initInteractive()},BigScreenMap.ShapRegion.prototype._addData=function(t){if(t.districts){for(var e=0;e<t.districts.length;e++)if(t.districts[e]){var i=GeoJSON.read(t.districts[e].geometry);t.districts[e].id=i.id,t.districts[e].center=[i.getCenter().lon,i.getCenter().lat],this._regions.push(t.districts[e]);var r={type:"Feature",geometry:{type:t.districts[e].geometry.type,coordinates:i._getCoordinates(this._map)},properties:{id:i.id,code:t.districts[e].code,index:e,fillColor:t.districts[e].fillColor?t.districts[e].fillColor:this.fillColor,lineColor:t.districts[e].lineColor?t.districts[e].lineColor:this.lineColor,children:1}};if(this._source.data.features.push(r),this.isShowFont){var a={type:"Feature",geometry:{type:"Point",coordinates:[i.getCenter().lon,i.getCenter().lat]},properties:{id:i.id,title:t.districts[e].name,"text-offset":[this.fontOffset[0]/this.fontSize,this.fontOffset[1]/this.fontSize],"text-opacity":1,"text-size":this.fontSize,"text-color":this.fontColor,visible:!0,index:e}};this._source.data.features.push(a)}}var o=GeoJSON.read(t.geometry),s={type:"Feature",geometry:{type:t.geometry.type,coordinates:o._getCoordinates(this._map)},properties:{id:o.id,children:0}};this._source.data.features.push(s)}},BigScreenMap.ShapRegion.prototype._addLayers=function(){var t=this._map._obj.getBearing(),e=this._map._obj.getPitch()*this.sharderWidth/60,i=0-Math.sin(t/180*Math.PI)*e,r=Math.cos(t/180*Math.PI)*e,a={id:this._layerID+"_sharderPolygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.sharderFillColor,"fill-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,this.sharderOpacity,this.maxZoom,0]:this.sharderOpacity,"fill-translate-anchor":"map","fill-translate":[i,r]},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(a),this._layers.push(a);var o={id:this._layerID+"_polygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":["get","fillColor"],"fill-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,this.opacity,this.maxZoom,0]:this.opacity,"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1]]};this.fillImage&&(o.paint["fill-pattern"]=this.fillImage),this._map._obj.addLayer(o),this._layers.push(o);var s={id:this._layerID+"_Line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","lineColor"],"line-dasharray":this.opts.dashline,"line-width":this.lineWidth,"line-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]:this.opacity},filter:["all",["==","$type","Polygon"],["==","children",1]]};s.paint["line-dasharray"]||delete s.paint["line-dasharray"],this._map._obj.addLayer(s),this._layers.push(s);var n={id:this._layerID+"_parentLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.lineColor,"line-width":this.outBorder,"line-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]:this.opacity},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(n),this._layers.push(n),t=this._map._obj.getBearing();var l=this._map._obj.getPitch()*this.hoverWidth/60,h=Math.sin(t/180*Math.PI)*l,p=0-Math.cos(t/180*Math.PI)*l;this.acvtiveLayer={id:this._layerID+"_polygonActive",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.hoverColor,"fill-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,this.opacity,this.maxZoom,0]:this.opacity,"fill-translate":[h,p],"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]},this._map._obj.addLayer(this.acvtiveLayer),this._layers.push(this.acvtiveLayer);var u={id:this._layerID+"_activeLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.hoverStrokeColor,"line-width":this.hoverStrokeWidth,"line-opacity":this.useGradual?["interpolate",["linear"],["zoom"],this.minZoom,1,this.maxZoom,0]:1,"line-translate":[h,p]},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]};this._map._obj.addLayer(u),this._layers.push(u),this._map._obj.addLayer(this.labelLayer),this.isShowFont&&this._layers.push(this.labelLayer)},BigScreenMap.ShapRegion.prototype.destory=function(){if(this._map){this._initInteractive(!0);for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map=null}this._regions=[],this._layers=[],this.acvtiveRegion=null},BigScreenMap.ShapRegion.prototype.refresh=function(){this.acvtiveRegion?(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1],["!=","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]])):(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]))},BigScreenMap.ShapRegion.prototype.setRegionActiveByName=function(t){for(var e=0;e<this._regions.length;e++)if(this._regions[e].name==t){this.acvtiveRegion=this._regions[e],this.refresh();break}},BigScreenMap.ShapRegion.prototype.setRegionActiveByCode=function(t){for(var e=0;e<this._regions.length;e++)if(this._regions[e].code==t){this.acvtiveRegion=this._regions[e],this.refresh();break}},BigScreenMap.ShapRegion.prototype.show=function(){for(var t=0;t<this._layers.length;t++)this._map._obj.setLayoutProperty(this._layers[t].id,"visibility","visible")},BigScreenMap.ShapRegion.prototype.hide=function(){for(var t=0;t<this._layers.length;t++)this._map._obj.setLayoutProperty(this._layers[t].id,"visibility","none")},BigScreenMap.ShapRegion.prototype.setRegionsStyle=function(t){for(var e=0;e<this._source.data.features.length;e++)if("Point"!=this._source.data.features[e].geometry.type){var i=this._source.data.features[e].properties.code;t[i]&&(t[i].fillColor&&(this._source.data.features[e].properties.fillColor=t[i].fillColor),t[i].lineColor&&(this._source.data.features[e].properties.lineColor=t[i].lineColor))}this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},function(){var t=BigScreenMap.StatisticsInfo=function(t,e){this.layerId=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._layers=[],this.opts=e||{},this.style,this.timer,this._visible=!0,this.headUrl=NPMap.Utils.BaseUtils.getHostPath(),this.defaultCircleImages=[],this.defaultLineImages=[],this.clickStatus=!1,this.animting=!1,this.clickID="",this._events=new NPMap.Events(this,["click"]);for(var i=0;i<10;i++)this.defaultCircleImages.push(this.headUrl+"/Netposa/circleImages/work"+(i+1)+".png");for(i=0;i<10;i++)this.defaultLineImages.push(this.headUrl+"/Netposa/blueline/"+(i+1)+".png");this._circleImages=this.opts.circleImages?this.opts.circleImages:this.defaultCircleImages,this._activeCircleImages=this.opts.activeCircleImages?this.opts.activeCircleImages:[],this._clickCircleImages=this.opts.clickCircleImages?this.opts.clickCircleImages:[],this._lineImages=this.opts.lineImages?this.opts.lineImages:this.defaultLineImages;var r=[];this.markerfilename=this.layerId+"_Marker",this.activeMarkerFileName=this.layerId+"_ActiveMarker",this.clickMarkerFileName=this.layerId+"_ClickMarker",this.linefilename=this.layerId+"_Line";for(i=0;i<this._circleImages.length;i++)r.push([this.markerfilename+(i+1),this._circleImages[i]]);for(i=0;i<this._lineImages.length;i++)r.push([this.linefilename+(i+1),this._lineImages[i]]);for(i=0;i<this._clickCircleImages.length;i++)r.push([this.clickMarkerFileName+(i+1),this._clickCircleImages[i]]);for(i=0;i<this._activeCircleImages.length;i++)r.push([this.activeMarkerFileName+(i+1),this._activeCircleImages[i]]);this.tagIcon=this.opts.tagIcon?this.opts.tagIcon:null,this.tagIcon&&(this.tagName=this.layerId+"_TagMarker",r.push([this.tagName,this.tagIcon])),this.tagTextFontSize=this.opts.tagTextFontSize?this.opts.tagTextFontSize:12,this.tagTextOffset=this.opts.tagTextOffset?this.opts.tagTextOffset:[0,0],this._tagOffset=this.opts.tagOffset?this.opts.tagOffset:[0,-20],this._activeTagOffset=this.opts.activeTagOffset?this.opts.activeTagOffset:[0,-46],this._lineOffset=this.opts.lineOffset?this.opts.lineOffset:[0,-20],this.hideLine=!!this.opts.hideLine&&this.opts.hideLine,this.hideCircle=!!this.opts.hideCircle&&this.opts.hideCircle,this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.minSize=this.opts.minSize?this.opts.minSize:.2,this.maxSize=this.opts.maxSize?this.opts.maxSize:1,this.minZoom=this.opts.minZoom?this.opts.minZoom:0,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:22,this.count=1,this.interval=this.opts.interval?this.opts.interval:100,this.nameFontColor=this.nameFontColor?this.nameFontColor:"#FFFFFF",this.nameFontHaloColor=this.nameFontHaloColor?this.nameFontHaloColor:"#183b67",this.nameFontHaloWidth=this.nameFontHaloWidth?this.nameFontHaloWidth:.5,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}};var a=this;this._map.addImages(r,function(){a._init()})};t.prototype._init=function(){this._map._obj.getSource(this.layerId)||(this._map._obj.addSource(this.layerId,this._source),this.hideCircle||this._createCircleLayer(),this.hideLine||this._createLineLayer(),this.tagIcon&&(this._createTagTextLayer(),this._createTagIconLayer()),this.timer=!0,this._start())},t.prototype.addPoints=function(t){this._points=t;var e=this._points.length>this.count?this.count:this._points.length;e<this.count&&(this.count=e),this._source.data.features=[];for(var i=0;i<this._points.length;i++){var r=NPMap.T.setPoint(this._map,this._points[i]),a=i%this.count;this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:[r.lon,r.lat]},properties:{id:this._points[i].id,dataID:this._points[i].getData().id,text:this._points[i].getData().text,name:this._points[i].getData().name,layertype:a}})}this._map&&this._map._obj.getSource(this.layerId)&&this._map._obj.getSource(this.layerId).setData(this._source.data)},t.prototype._createCircleLayer=function(){for(var t=0;t<this.count;t++){var e=this.markerfilename+Math.round(Math.random()*(this._circleImages.length-1)+1),i={id:this.layerId+"_symbol"+t,type:"symbol",source:this.layerId,layout:{"icon-image":e,"icon-size":{stops:[[this.minZoom,this.minSize],[this.maxZoom,this.maxSize]]},"icon-rotation-alignment":"viewport","icon-ignore-placement":!1,"icon-allow-overlap":!0,"text-allow-overlap":!1},paint:{"icon-opacity":1},filter:["==","layertype",t]};this._layers.push(i),this._map._obj.addLayer(i)}},t.prototype._createLineLayer=function(){var t=this.linefilename+Math.round(Math.random()*(this._lineImages.length-1)+1),e={id:this.layerId+"_line",type:"symbol",source:this.layerId,layout:{"icon-image":t,"icon-size":1,"icon-offset":this._lineOffset,"icon-rotation-alignment":"viewport","icon-ignore-placement":!0,"icon-allow-overlap":!0,"text-allow-overlap":!0},paint:{"icon-opacity":1}};this._layers.push(e),this._map._obj.addLayer(e)},t.prototype._createTagIconLayer=function(){var t={id:this.layerId+"icon_layer",type:"symbol",source:this.layerId,layout:{"icon-image":this.tagName,"icon-ignore-placement":!1,"icon-offset":this._tagOffset,"text-field":"{text}","icon-size":.4,"text-font":["Microsoft YaHei"],"text-offset":[this.tagTextOffset[0]/this.tagTextFontSize,this.tagTextOffset[1]/this.tagTextFontSize],"text-size":this.tagTextFontSize,"icon-optional":!1,"symbol-avoid-edges":!1,"text-ignore-placement":!1,"icon-ignore-placement":!0,"icon-allow-overlap":!1,"text-allow-overlap":!1},paint:{"icon-opacity":1,"text-color":this.fontColor,"text-halo-color":"rgba(18,18,18,0.4)","text-halo-width":5},minzoom:this.minZoom,maxzoom:this.maxZoom};this._layers.push(t),this._map._obj.addLayer(t);var e={id:this.layerId+"activeicon_layer",type:"symbol",source:this.layerId,layout:{"icon-image":this.tagName,"icon-ignore-placement":!0,"icon-offset":this._tagOffset,"text-field":"{text}","icon-size":.4,"text-font":["Microsoft YaHei"],"text-offset":[this.tagTextOffset[0]/this.tagTextFontSize,this.tagTextOffset[1]/this.tagTextFontSize],"text-size":this.tagTextFontSize,"text-ignore-placement":!0,"icon-allow-overlap":!0,"text-allow-overlap":!0},paint:{"icon-opacity":1,"text-color":this.fontColor,"text-halo-color":"rgba(18,18,18,0.4)","text-halo-width":5},filter:["==","dataID",""]};this._layers.push(e),this._map._obj.addLayer(e);var i={id:this.layerId+"clickicon_layer",type:"symbol",source:this.layerId,layout:{"icon-image":this.tagName,"icon-ignore-placement":!0,"icon-offset":this._tagOffset,"text-field":"{text}","icon-size":.6,"text-font":["Microsoft YaHei"],"text-offset":[this.tagTextOffset[0]/this.tagTextFontSize,this.tagTextOffset[1]/this.tagTextFontSize],"text-size":this.tagTextFontSize,"text-ignore-placement":!0,"icon-allow-overlap":!0,"text-allow-overlap":!0},paint:{"icon-opacity":1,"text-color":this.fontColor,"text-halo-color":"rgba(18,18,18,0.4)","text-halo-width":5},filter:["==","dataID",""]};this._layers.push(i),this._map._obj.addLayer(i);var r=this;this._map._obj.on("click",this.layerId+"icon_layer",function(t){for(var e=t.features[0],i=0;i<r._points.length;i++)if(e.properties.id===r._points[i].id)return void r._events.triggerEvt("click",r._points[i])}),this._map._obj.on("mousemove",this.layerId+"icon_layer",function(t){r._map._obj.getCanvas().style.cursor="pointer"})},t.prototype._createTagTextLayer=function(){var t={id:this.layerId+"text_layer",type:"symbol",source:this.layerId,layout:{"text-field":"{name}","text-font":["Microsoft YaHei"],"text-offset":[0,3],"text-size":this.tagTextFontSize,"text-ignore-placement":!1,"text-allow-overlap":!1},paint:{"icon-opacity":1,"text-color":this.nameFontColor,"text-halo-color":this.nameFontHaloColor,"text-halo-width":this.nameFontHaloWidth},minzoom:this.minZoom,maxzoom:this.maxZoom};this._layers.push(t),this._map._obj.addLayer(t)},t.prototype._start=function(){if(this.timer){if(!this.hideCircle){for(var t=0;t<this.count;t++){var e=this._map._obj.getLayoutProperty(this.layerId+"_symbol"+t,"icon-image"),i=(parseInt(e.replace(this.markerfilename,""))+1)%this._circleImages.length;e=0==i?this.markerfilename+this._circleImages.length:this.markerfilename+i,this._map._obj.setLayoutProperty(this.layerId+"_symbol"+t,"icon-image",e)}var r=this._map.getPitch()/60*this._tagOffset[1];this._map._obj.getLayer(this.layerId+"icon_layer")&&(this._map._obj.setLayoutProperty(this.layerId+"icon_layer","icon-offset",[0,r]),this._map._obj.setLayoutProperty(this.layerId+"activeicon_layer","icon-offset",[0,r]))}if(!this.hideLine){var a=this._map._obj.getLayoutProperty(this.layerId+"_line","icon-image");lineIconIndex=(parseInt(a.replace(this.linefilename,""))+1)%this._lineImages.length,a=0==lineIconIndex?this.linefilename+this._lineImages.length:this.linefilename+lineIconIndex,this._map._obj.setLayoutProperty(this.layerId+"_line","icon-image",a),this._map._obj.setLayoutProperty(this.layerId+"_line","icon-size",this._map.getPitch()/60*1)}if(0<this._circleImages.length){this._map._obj.setLayoutProperty(this.layerId+"activeicon_layer","icon-offset",this._tagOffset);var o=this._map._obj.getLayoutProperty(this.layerId+"activeicon_layer","icon-image"),s=(parseInt(o.replace(this.markerfilename,""))+1)%this._circleImages.length;o=0==s?this.markerfilename+this._circleImages.length:this.markerfilename+s,this._map._obj.setLayoutProperty(this.layerId+"activeicon_layer","icon-image",o)}if(!this.animting&&this.clickStatus&&0<this._clickCircleImages.length){this._map._obj.setFilter(this.layerId+"clickicon_layer",["==","dataID",this.clickID]),this._map._obj.setLayoutProperty(this.layerId+"clickicon_layer","icon-offset",this._activeTagOffset);var n=this._map._obj.getLayoutProperty(this.layerId+"clickicon_layer","icon-image"),l=(parseInt(n.replace(this.clickMarkerFileName,""))+1)%this._clickCircleImages.length;n=0==l?this.clickMarkerFileName+this._clickCircleImages.length:this.clickMarkerFileName+l,this._map._obj.setLayoutProperty(this.layerId+"clickicon_layer","icon-image",n)}this.timer&&setTimeout(this._start.bind(this),this.interval)}},t.prototype.clearPoints=function(){if(this.timer=!1,this._layers.length){for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map._obj.removeSource(this.layerId)}},t.prototype.hide=function(){if(this.timer=!1,this._layers.length&&this._visible){for(var t=0;t<this._layers.length;t++)this._map._obj.setLayoutProperty(this._layers[t].id,"visibility","none");this._visible=!1}},t.prototype.show=function(){if(this._layers.length&&!this._visible){for(var t=0;t<this._layers.length;t++)this._map._obj.setLayoutProperty(this._layers[t].id,"visibility","visible");this.timer=!0,this._start(),this._visible=!0}},t.prototype.hideTagByID=function(t){this._map._obj.setFilter(this.layerId+"icon_layer",["!=","dataID",t])},t.prototype.cancelHideTag=function(){this._map._obj.setFilter(this.layerId+"icon_layer",null)},t.prototype.setActiveByID=function(t){var e=this._map._obj.getFilter(this.layerId+"clickicon_layer");if(e){if(e[2]==t)return;e[0]="!=";var i=["all",e,["!=","dataID",t]];this._map._obj.setFilter(this.layerId+"icon_layer",i)}else this._map._obj.setFilter(this.layerId+"icon_layer",["!=","dataID",t]);this._map._obj.setFilter(this.layerId+"activeicon_layer",["==","dataID",t]);var r=this.markerfilename+1;this._map._obj.setLayoutProperty(this.layerId+"activeicon_layer","icon-image",r);var a=this,o=function(){var t=a._map._obj.getLayoutProperty(a.layerId+"activeicon_layer","icon-size");a.clickStatus?a._map._obj.setLayoutProperty(a.layerId+"activeicon_layer","icon-size",.6):t<.6&&(t+=.05,a._map._obj.setLayoutProperty(a.layerId+"activeicon_layer","icon-size",t),setTimeout(o,60))};a._map._obj.setLayoutProperty(a.layerId+"activeicon_layer","icon-size",.4),o()},t.prototype.setClickActiveByID=function(t){this.clickStatus=!0,this.animting=!0,this.clickID=t,this._map._obj.setFilter(this.layerId+"icon_layer",["!=","dataID",t]),this._map._obj.setFilter(this.layerId+"activeicon_layer",["==","dataID",""]),this._map._obj.setFilter(this.layerId+"clickicon_layer",["==","dataID",t]);var e=this,i=1,r=function(){var t=e.activeMarkerFileName+i;e._map._obj.setLayoutProperty(e.layerId+"clickicon_layer","icon-offset",e._activeTagOffset),e._map._obj.setLayoutProperty(e.layerId+"clickicon_layer","icon-image",t),++i<e._activeCircleImages.length+1?setTimeout(r,60):(e.animting=!1,e._map._obj.setLayoutProperty(e.layerId+"clickicon_layer","icon-image",e.clickMarkerFileName+"1"))};r()},t.prototype.resetNormal=function(){var t=this._map._obj.getFilter(this.layerId+"clickicon_layer");""!=t[2]?t[0]="!=":t=null,this._map._obj.setFilter(this.layerId+"icon_layer",t),this._map._obj.setFilter(this.layerId+"activeicon_layer",["==","dataID",""])},t.prototype.cancalClickActive=function(){this.clickStatus=!1,this.animting=!1,this.clickID="";var t=this._map._obj.getFilter(this.layerId+"activeicon_layer");""!=t[2]?t[0]="!=":t=null,this._map._obj.setFilter(this.layerId+"icon_layer",t),this._map._obj.setFilter(this.layerId+"clickicon_layer",["==","dataID",""])},t.prototype.addEventListener=function(t,e){this._events.register(t,e)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)}}(),function(){var a=function(t,e,i,r){this._interval=200,this.markerfilename=r.markerfilename,this.linefilename=r.linefilename,this.tagfilename=r.tagfilename,this._alarmInfo=e,this._lineOffset=r.lineOffset,this._tagOffset=r.tagOffset,this._map=i,this.id=t,this._timer=null,this._callback=r.callback,this._stauts=!0,this.init=function(){var t=NPMap.T.setPoint(this._map,this._alarmInfo),e={type:"geojson",data:{type:"FeatureCollection",features:[{type:"Feature",geometry:{type:"Point",coordinates:[t.lon,t.lat]},properties:{}}]}};this._map._obj.addSource(this.id,e),this._createCircleLayer(this.id),this._createLineLayer(this.id),this._createTagIconLayer(this.id)},this._createCircleLayer=function(t){var e={id:t+"_Circle",type:"symbol",source:t,layout:{"icon-image":this.markerfilename+1,"icon-size":.8,"icon-rotation-alignment":"map","icon-ignore-placement":!0,"icon-allow-overlap":!0},paint:{"icon-opacity":1}};this._map._obj.addLayer(e)},this._createLineLayer=function(t){var e={id:t+"_line",type:"symbol",source:t,layout:{"icon-image":this.linefilename+0,"icon-size":1,"icon-offset":this._lineOffset,"icon-rotation-alignment":"viewport","icon-ignore-placement":!0,"icon-allow-overlap":!0,"text-allow-overlap":!0},paint:{"icon-opacity":1}};this._map._obj.addLayer(e)},this._createTagIconLayer=function(t){var e={id:t+"icon_layer",type:"symbol",source:t,layout:{"icon-image":this.tagfilename+1,visibility:"none","icon-ignore-placement":!0,"icon-offset":this._tagOffset,"icon-size":.4,"icon-optional":!1,"icon-allow-overlap":!0},paint:{"icon-opacity":1}};this._map._obj.addLayer(e)},this._remove=function(){this._map._obj.getLayer(this.id+"_Circle")&&this._map._obj.removeLayer(this.id+"_Circle"),this._map._obj.getLayer(this.id+"_line")&&this._map._obj.removeLayer(this.id+"_line"),this._map._obj.getLayer(this.id+"icon_layer")&&this._map._obj.removeLayer(this.id+"icon_layer"),this._map._obj.getSource(this.id)&&this._map._obj.removeSource(this.id)},this._start=function(){if(this._stauts){var t=this._map._obj.getLayoutProperty(this.id+"_Circle","icon-image"),e=parseInt(t.replace(this.markerfilename,""))+1;if(!(e<21))return this._remove(),void this._callback();if(t=this.markerfilename+e,this._map._obj.setLayoutProperty(this.id+"_Circle","icon-image",t),2<e&&e<10){var i=this._map._obj.getLayoutProperty(this.id+"_line","icon-image"),r=parseInt(i.replace(this.linefilename,""))+1;i=this.linefilename+r,this._map._obj.setLayoutProperty(this.id+"_line","icon-image",i)}if(this._map._obj.setLayoutProperty(this.id+"_line","icon-size",this._map.getPitch()/60),7<e&&e<14){this._map._obj.setLayoutProperty(this.id+"icon_layer","visibility","visible");var a=this._map._obj.getLayoutProperty(this.id+"icon_layer","icon-image"),o=parseInt(a.replace(this.tagfilename,""))+1;o=o%2?1:2,a=this.tagfilename+o,this._map._obj.setLayoutProperty(this.id+"icon_layer","icon-image",a);var s=this._map.getPitch()/60*this._tagOffset[1];this._map._obj.getLayer(this.id+"icon_layer")&&this._map._obj.setLayoutProperty(this.id+"icon_layer","icon-offset",[0,s])}14<=e&&(this._map._obj.setLayoutProperty(this.id+"_line","visibility","none"),this._map._obj.setLayoutProperty(this.id+"icon_layer","visibility","none")),this._timer=setTimeout(this._start.bind(this),this._interval)}},this.stop=function(){this._stauts=!1,this._timer&&(clearTimeout(this._timer),this._timer=null,this._remove())}},t=BigScreenMap.AlarmInfoManager=function(t,e){this.layerId=NPMap.Utils.BaseUtils.uuid(),this._map=t,this.opts=e||{},this.style,this.timer,this.headUrl=NPMap.Utils.BaseUtils.getHostPath(),this.defaultCircleImages=[],this.defaultLineImages=[],this.defaultTagImages={car:[],man:[]};for(var i=0;i<10;i++)this.defaultCircleImages.push(this.headUrl+"Netposa/circleImages/work"+(i+1)+".png");for(i=0;i<10;i++)this.defaultLineImages.push(this.headUrl+"Netposa/blueline/"+(i+1)+".png");this._circleImages=this.opts.circleImages?this.opts.circleImages:this.defaultCircleImages,this._lineImages=this.opts.lineImages?this.opts.lineImages:this.defaultLineImages,this._tagImages=this.opts.tagImages?this.opts.tagImages:this.defaultTagImages,this.markerfilename=this.layerId+"_Marker",this.linefilename=this.layerId+"_Line",this.tagCarfilename=this.layerId+"_TagCar",this.tagManfilename=this.layerId+"_TagMan",this._lineOffset=this.opts.lineOffset?this.opts.lineOffset:[0,-50],this._tagOffset=this.opts.tagOffset?this.opts.tagOffset:[0,-90];var r=[];for(i=0;i<this._circleImages.length;i++)r.push([this.markerfilename+(i+1),this._circleImages[i]]);for(i=0;i<this._lineImages.length;i++)r.push([this.linefilename+(i+1),this._lineImages[i]]);for(i=0;i<this._tagImages.car.length;i++)r.push([this.tagCarfilename+(i+1),this._tagImages.car[i]]);for(i=0;i<this._tagImages.man.length;i++)r.push([this.tagManfilename+(i+1),this._tagImages.man[i]]);this._map.addImages(r),this._activeAlarms={}};t.prototype.addAlarmInfo=function(t){var e=NPMap.Utils.BaseUtils.uuid(),i=this,r=new a(e,t,this._map,{markerfilename:this.markerfilename,linefilename:this.linefilename,tagfilename:"man"===t.type?this.tagManfilename:this.tagCarfilename,lineOffset:this._lineOffset,tagOffset:this._tagOffset,callback:function(t){i._activeAlarms[t]=null,delete i._activeAlarms[t]}});r.init(),this._activeAlarms[e]=r,this._activeAlarms[e]._start()},t.prototype.removeAllAlarms=function(){for(var t in this._activeAlarms)this._activeAlarms[t].stop(),this._activeAlarms[t]._remove(),this._activeAlarms[t]=null;this._activeAlarms={}}}(),function(){var t=BigScreenMap.LightManager=function(t,e){this._map=t,this.tb=e};t.prototype.addLight=function(t){var e=t&&t.lightType?t.lightType:"SpotLight",i=t&&t.name?t.name:t.lightType,r=t&&t.color?t.color:16777215,a=t&&t.intensity?t.intensity:4,o=t&&t.distance?t.distance:1e5,s=t&&t.decay?t.decay:0,n=t&&t.position?t.position:{lon:0,lat:0};if(n=NPMap.T.setPoint(map,n),"SpotLight"==e){var l=t&&t.angle?t.angle:0;(u=new THREE.SpotLight(r,a,o,l,0,s)).name=i,u.realDistance=o;var h=t&&t.height?t.height:1e3;this.tb.addAtCoordinate(u,[n.lon,n.lat,h]);var p=this;return u.move=function(t,e){p.tb.moveToCoordinate(u,[t.lon,t.lat,e])},(c=new THREE.Object3D).name=u.name+"-target",this.tb.addAtCoordinate(c,[n.lon,n.lat,0]),this.tb.cameraSync.updateCamera(),c.move=function(t,e){p.tb.moveToCoordinate(c,[t.lon,t.lat,e])},u.target=c,{light:u,target:c,position:n,height:h}}if("PointLight"==e){(u=new THREE.PointLight(r,a,o,s)).name=i,u.realDistance=o;h=t&&t.height?t.height:500;this.tb.addAtCoordinate(u,[n.lon,n.lat,h]),this.tb.cameraSync.updateCamera();p=this;return u.move=function(t,e){p.tb.moveToCoordinate(u,[t.lon,t.lat,e])},{light:u,position:n,height:h}}if("PointLight2"==e){(u=new THREE.PointLight2(r,a,o,s)).name=i;h=t&&t.height?t.height:500;this.tb.addAtCoordinate(u,[n.lon,n.lat,h]);p=this;return u.move=function(t,e){p.tb.moveToCoordinate(u,[t.lon,t.lat,e])},{light:u,position:n,height:h}}if(e="AmbientLight")return(u=new THREE.AmbientLight(r,a)).name=i,this.tb.addAtCoordinate(u,[0,0,0]),{light:u};if(e="DirectionalLight"){var u;(u=new THREE.DirectionalLight(r,a)).name=i,this.tb.addAtCoordinate(u,[n.lon,n.lat,h]);var c;p=this;return u.move=function(t,e){p.tb.moveToCoordinate(u,[t.lon,t.lat,e])},(c=new THREE.Object3D).name=u.name+"-target",this.tb.addAtCoordinate(c,[n.lon,n.lat,0]),c.move=function(t,e){p.tb.moveToCoordinate(c,[t.lon,t.lat,e])},u.target=c,{light:u,position:n,height:h}}},t.prototype.removeLight=function(t){var e=this.tb.world.getObjectByName(t);if(e){this.tb.remove(e),e=null;var i=this.tb.world.getObjectByName(t+"-target");i&&(this.tb.remove(i),i=null)}}}(),function(){var m=function(t,e,i,r,n,a,o,s,l,h,p,u){this.LineObj=function(t,e){var i=t.clone(),r=e.clone();r.x=i.x=(t.x+e.x)/2,r.y=i.y=(t.y+e.y)/2,r.z=i.z=t.distanceTo(e)/4;var a=new THREE.CubicBezierCurve3(t,i,r,e),o=new THREE.Geometry;o.vertices=a.getPoints(50);var s=new THREE.LineBasicMaterial({color:n});return{curve:a,lineMesh:new THREE.Line(o,s)}}(e,i),this.point=t,this.lCount=r;var c=new Float32Array(3*r),d=new Float32Array(3*r),f=new Float32Array(r);this.geometry=new THREE.BufferGeometry,this.index=r+Math.floor(Math.random()*p);var m=[],v=[],g=[],y=NPMap.Utils.MapUtil.gradientColor(o,a,r,!0);this.vertices=this.LineObj.curve.getPoints(p),m=this.index>=r?this.vertices.slice(this.index-r,this.index):this.vertices.slice(0,this.index);for(var x=0;x<r;x++)v.push(y[x].r/256,y[x].g/256,y[x].b/256),1==u?g.push(s-Math.abs((s-l)/r*(x-r/2)*2)):g.push(l+x*(s-l)/r);var _=[];for(x=0;x<m.length;x++)_.push(m[x].x,m[x].y,m[x].z);c.set(_),d.set(v),f.set(g),this.geometry.setAttribute("position",new THREE.BufferAttribute(c,3)),this.geometry.setAttribute("customColor",new THREE.BufferAttribute(d,3)),this.geometry.setAttribute("size",new THREE.BufferAttribute(f,1)),this.points=new THREE.Points(this.geometry,h)},t=BigScreenMap.MapAirLine=function(t,e,i){this._map=t,this._springLines=[],this._layerID=NPMap.Utils.BaseUtils.uuid(),this._startColor=i&&i.startColor?i.startColor:"#FF0000",this._endColor=i&&i.endColor?i.endColor:"#FFFF00",this._startSize=i&&i.startSize?i.startSize:50,this._endSize=i&&i.endSize?i.endSize:10,this._count=i&&i.count?i.count:150,this._showLine=!i||!1!==i.showLine||i.showLine,this._lineCount=i&&i.lineCount?i.lineCount:300,this._minZoom=i&&i.minZoom?i.minZoom:0,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this._spriteLineType=i&&i.spriteLineType?i.spriteLineType:0,this._step=i&&i.step?i.step:5,this.tb=e,this.timer=null;var r=NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/spark1.png";this.material=new THREE.ShaderMaterial({uniforms:{color:{value:new THREE.Color(16777215)},fogColor:{value:this.tb.scene.fog.color},fogNear:{value:this.tb.scene.fog.near},fogFar:{value:this.tb.scene.fog.far},texture:{value:(new THREE.TextureLoader).load(r)}},vertexShader:"#include <fog_pars_vertex>\nattribute float size;attribute vec3 customColor;varying vec3 vColor;void main() {vColor = customColor;vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );gl_PointSize = size * ( 300.0 / -mvPosition.z );gl_Position = projectionMatrix * mvPosition;\n\t#include <fog_vertex>\n\t}",fragmentShader:"#include <fog_pars_fragment>\nuniform vec3 color;uniform sampler2D texture;varying vec3 vColor;void main() {gl_FragColor = vec4( color * vColor, 1.0 );gl_FragColor = gl_FragColor * texture2D( texture, gl_PointCoord );\n\t#include <fog_fragment>\n\t}",blending:THREE.AdditiveBlending,depthTest:!1,fog:!0,transparent:!0})};t.prototype.addAirLines=function(t){this._springLines=[];this.timer&&clearTimeout(this.timer),this._springLines=function(t,e,i,r){for(var a=[],o=0;o<t.length;o++){t[o];var s=NPMap.T.setPoint(e,t[o].startPoint.point),n=i.utils.projectToWorld([s.lon,s.lat,t[o].startPoint.h]),l=n,h=NPMap.T.setPoint(e,t[o].endPoint.point),p=i.utils.projectToWorld([h.lon,h.lat,t[o].endPoint.h]),u=new THREE.Vector3(l.x-n.x,l.y-n.y,l.z),c=new THREE.Vector3(p.x-n.x,p.y-n.y,p.z),d=t[o].lineColor?t[o].lineColor:16711680,f=new m(s,u,c,r._count,d,r._startColor,r._endColor,r._startSize,r._endSize,r.material,r._lineCount,r._spriteLineType);a.push(f)}return a}(t,this._map,this.tb,this);for(var e=0;e<this._springLines.length;e++)this._showLine&&this.tb.addAtCoordinate(this._springLines[e].LineObj.lineMesh,[this._springLines[e].point.lon,this._springLines[e].point.lat]),this.tb.addAtCoordinate(this._springLines[e].points,[this._springLines[e].point.lon,this._springLines[e].point.lat]);this._update()},t.prototype._update=function(){for(var t=0;t<this._springLines.length;t++){this._springLines[t].index>this._lineCount+this._count&&(this._springLines[t].index=this._count);var e=[];if(this._springLines[t].index>=this._count&&(e=this._springLines[t].vertices.slice(this._springLines[t].index-this._count+1,this._springLines[t].index),this._springLines[t].index>this._lineCount)){var i=this._springLines[t].vertices.slice(0,this._springLines[t].index-this._lineCount);e=e.concat(i)}for(var r=new Float32Array(3*e.length),a=[],o=0;o<e.length;o++)a.push(e[o].x,e[o].y,e[o].z);r.set(a),this._springLines[t].points.geometry.setAttribute("position",new THREE.Float32BufferAttribute(r,3)),this._springLines[t].index+=this._step,r=null}this._map.getZoom()>=this._minZoom&&this._map.getZoom()<=this._maxZoom?(this.material.visible=!0,this._map._obj.repaint=!0):this.material.visible=!1,this.timer=setTimeout(this._update.bind(this),40)},t.prototype.destory=function(){if(this.timer&&clearTimeout(this.timer),this._map&&this._map._obj.getLayer(this._layerID)){this._map._obj.removeLayer(this._layerID);for(var t=0;t<this._springLines.length;t++)this.tb.remove(this._springLines[t].points),this._springLines[t].geometry=null,this._springLines[t].points=null;this._springLines.length=0,this.tb=null}}}(),function(){var e=function(t,e,i){for(var r={},a=0;a<t.wall.length;a++)"range"===t.type?(r["wall-"+a+"-minV"]=t.wall[a].minV,r["wall-"+a+"-maxV"]=t.wall[a].maxV):r["wall-"+a+"-value"]=t.wall[a].value,r["wall-"+a+"-vecColor"]=t.wall[a].vecColor,t.wall[a].vecColor&&(r["wall-"+a+"-topColor"]=t.wall[a].topColor,r["wall-"+a+"-bottomColor"]=t.wall[a].bottomColor),r["wall-"+a+"-repeat"]=t.wall[a].repeat,r["wall-"+a+"-operatUV"]=t.wall[a].operatUV,r["wall-"+a+"-fnumber"]=t.wall[a].imageFloorNumer,r["wall-"+a+"-wnumber"]=t.wall[a].imageWidthNumer;r["buildMaterial-wall-number"]=t.wall.length,r["buildMaterial-type"]=t.type,r["buildMaterial-key"]=t.key,r.row_cols=e,this.buildMaterial=t,i.postMessage(r)},t=BigScreenMap.MapGeoBuilding3D=function(t,e,i){var p=this;if(this._map=t,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.textureImageUrl=i&&i.textureImageUrl?i.textureImageUrl:void 0,this.filterTextureImages=i&&i.filterTextureImages?i.filterTextureImages:void 0,this.buildTopColor=i&&i.buildTopColor?i.buildTopColor:"rgb(53,86,160)",this.buildSideColor=i&&i.buildSideColor?i.buildSideColor:this.buildTopColor,this.isTransparent=null==i.isTransparent||i.isTransparent,this.opacity=i&&i.opacity?i.opacity:1,this._animation=null==i.animation||i.animation,this.tb=e,this.url=i.url,this.timer=null,this.clock=new THREE.Clock,this.animationSpeed=i&&i.animationSpeed?i.animationSpeed:.15,this.beforeLayerID=i&&i.beforeLayerID?i.beforeLayerID:"city_normal_building_id",this.buildingsArrayGeoJson=[],this._geoBuilding3Ds=[],this._buildCatchs={},this.resolutions=[156543.0339,78271.516953125,39135.7584765625,19567.87923828125,9783.939619140625,4891.9698095703125,2445.9849047851562,1222.9924523925781,611.4962261962891,305.74811309814453,152.87405654907226,76.43702827453613,38.218514137268066,19.109257068634033,9.554628534317017,4.777314267158508,2.388657133579254,1.194328566789627,.5971642833948135],this.currentRowCols=this._getTileRowColomns(),this.maxExtent=20037508.3427892,this.buildTopMaterial=new THREE.MeshStandardMaterial({color:this.buildTopColor,transparent:this.isTransparent,opacity:this.opacity}),this.buildMaterials={},this.buildMaterials.key="height",this.buildMaterials.type="range",this.buildMaterials.wall=[],this.filterTextureImages){this.buildMaterials.key=this.filterTextureImages[0],this.buildMaterials.type=this.filterTextureImages[1];for(var r=2;r<this.filterTextureImages.length;r++){var a,o={};a="range"==this.filterTextureImages[1]?(o.minV=this.filterTextureImages[r][0],o.maxV=this.filterTextureImages[r][1],this.filterTextureImages[r][2]):(o.value=this.filterTextureImages[r][0],this.filterTextureImages[r][1]),o.repeat=void 0===a.repeat||a.repeat,o.operatUV=a.operatUV,o.imageFloorNumer=a.imageFloorNumer?a.imageFloorNumer:5;var s=a.w_h?a.w_h:[1,1];if(a.opacity=a.opacity?a.opacity:1,a.url){var n=(new THREE.TextureLoader).load(a.url);o.imageWidthNumer=o.imageFloorNumer/s[1]*s[0],n.wrapS=n.wrapT=THREE.RepeatWrapping,n.repeat.set(1,1),n.rotation=-Math.PI/2,o.materials=new THREE.MeshStandardMaterial({map:n,emissive:a.canvas?16777215:0,emissiveMap:a.canvas?new THREE.CanvasTexture(a.canvas):null,roughness:a.canvas?.89:void 0,metalness:a.canvas?.5:void 0,transparent:a.transparent,opacity:a.opacity})}else 1<a.color.length?(o.materials=new THREE.MeshStandardMaterial({vertexColors:THREE.VertexColors,emissive:0,transparent:a.transparent,opacity:a.opacity}),o.topColor=a.color[0],o.bottomColor=a.color[1],o.vecColor=!0):o.materials=new THREE.MeshStandardMaterial({color:a.color[0],emissive:a.canvas?16777215:0,emissiveMap:a.canvas?new THREE.CanvasTexture(a.canvas):null,roughness:a.canvas?.89:void 0,metalness:a.canvas?.5:void 0,transparent:a.transparent,opacity:a.opacity});o.materials.emissiveMap&&(o.materials.emissiveMap.wrapS=o.materials.emissiveMap.wrapT=THREE.RepeatWrapping,o.materials.emissiveMap.rotation=-Math.PI/2,o.materials.emissiveMap.repeat.set(1,1));var l=new THREE.MeshStandardMaterial({color:a.color[0],vertexColors:o.vecColor?THREE.VertexColors:void 0,transparent:a.transparent,opacity:a.opacity,emissive:0,roughness:a.canvas?.89:void 0,metalness:a.canvas?.5:void 0});o.topMaterial=l,this.buildMaterials.wall.push(o)}}else this.textureImageUrl?(this.notAnimationTexture=(new THREE.TextureLoader).load(this.textureImageUrl),this.notAnimationTexture.wrapS=this.notAnimationTexture.wrapT=THREE.RepeatWrapping,this.notAnimationTexture.repeat.set(1,5),this.notAnimationTexture.rotation=-Math.PI/2,this.materialNotAnimation=new THREE.MeshStandardMaterial({map:this.notAnimationTexture,color:this.buildTopColor}),this.buildMaterials.wall.push({minV:0,maxV:1e5,materials:this.materialNotAnimation,topMaterial:this.buildTopMaterial,repeat:!0})):(this.materialNotAnimation=this.buildTopMaterial,this.buildMaterials.wall.push({minV:0,maxV:1e5,materials:this.materialNotAnimation,topMaterial:this.buildTopMaterial,repeat:!1}));var h=this._getWorkerContent(),u=new Blob([h],{type:"application/javascript"});this.main=new Worker(URL.createObjectURL(u)),this.main.onmessage=function(t){if(0<t.data.groups.length){var e=new THREE.BufferGeometry;e.groups=t.data.groups;var i=t.data.vertices1,r=t.data.normals1,a=t.data.uvs1,o=t.data.colors1,s=t.data.position;e.setAttribute("position",new THREE.BufferAttribute(i,3)),e.setAttribute("normal",new THREE.BufferAttribute(r,3)),e.setAttribute("uv",new THREE.BufferAttribute(a,2)),e.setAttribute("color",new THREE.BufferAttribute(o,3));for(var n=[],l=0;l<p.buildMaterials.wall.length;l++)n.push(p.buildMaterials.wall[l].topMaterial),n.push(p.buildMaterials.wall[l].materials);var h=new THREE.Mesh(e,n);h.name=t.data.name,h.isBuild=!0,p._buildCatchs[t.data.name]||(s=NPMap.T.setPoint(p._map,s),p.tb.addAtCoordinate(h,[s.lon,s.lat]),p._buildCatchs[t.data.name]={position:s,mesh:h,load:!0,buildBufferGeometry:e,vertices1:i,normals1:r,uvs1:a,colors1:o}),h=null,e.dispose();i=[];delete i;r=[];delete r;a=[];delete a;o=[];delete o;s=e=null;p.tb.map.repaint=!0}},this.main.onerror=function(t){},this._initTile(this.url,i&&i.zoom?i.zoom:12)};t.prototype.updataMaterials=function(){for(var t=0;t<this.buildMaterials.wall.length;t++)this.buildMaterials.wall[t].materials.emissiveMap&&(this.buildMaterials.wall[t].materials.emissiveMap.needsUpdate=!0);this._map._obj.repaint=!0},t.prototype._loadTile=function(t){new e(this.buildMaterials,t,this.main)},t.prototype._removeBuildTile=function(t){var e=this.tb.world.getObjectByName(t);e&&this.tb.remove(e)},t.prototype._initTile=function(t,u){var c=this,d=this._map.getCenter();d=NPMap.T.helper.inverseMercator(d.lon,d.lat);var f=[],m=THREE.LoaderUtils.extractUrlBase(t);-1==m.indexOf("http")&&(m=window.location.origin+m),fetch(t,{method:"get"}).then(function(t){return t.json()}).then(function(t){for(var e=0;e<t.data.length;e++){var i=t.data[e].url,r=m+i,a=i.split("/"),o=a.length,s=a[o-2],n=a[o-1].split(".")[0];if(t.data[e].center){var l=t.data[e].center,h=NPMap.T.helper.inverseMercator(l.lon,l.lat);f.push({url:r,row:s,col:n,distance:Math.sqrt((d.lon-h.lon)*(d.lon-h.lon)+(d.lat-h.lat)*(d.lat-h.lat))})}else{var p=c._getTileInfo(s,n,u);f.push({url:r,row:s,col:n,distance:Math.sqrt((d.lon-p.center.lon)*(d.lon-p.center.lon)+(d.lat-p.center.lat)*(d.lat-p.center.lat))})}}f.sort(function(t,e){return t.distance-e.distance}),c._loadTile(f)})},t.prototype._getTileInfo=function(t,e,i){var r=256*t*this.resolutions[i]-this.maxExtent,a=256*(t+1)*this.resolutions[i]-this.maxExtent,o={lon:r,lat:this.maxExtent-256*(e+1)*this.resolutions[i]},s={lon:a,lat:this.maxExtent-256*e*this.resolutions[i]};return{min:o,max:s,center:NPMap.T.getPoint(this._map,{lon:(o.lon+s.lon)/2,lat:(o.lat+s.lat)/2})}},t.prototype._getTileRowColomns=function(){var t=this._map.getExtent(),e=parseInt(this._map.getZoom()-1),i=t.sw,r=t.ne;i=NPMap.T.helper.webMoctorJW2PM(i.lon,i.lat),r=NPMap.T.helper.webMoctorJW2PM(r.lon,r.lat);var a=this.resolutions[e],o=i.lon,s=i.lat,n=r.lon,l=r.lat;return{zoom:e,minRow:Math.floor((o+this.maxExtent)/(256*a)),maxCol:Math.ceil((this.maxExtent-s)/(256*a)),maxRow:Math.ceil((n+this.maxExtent)/(256*a)),minCol:Math.floor((this.maxExtent-l)/(256*a))}},t.prototype.destory=function(){for(var t in this._buildCatchs)this._buildCatchs[t]&&(this._removeBuildTile(t),this._buildCatchs[t].buildMesh=null,this._buildCatchs[t].buildBufferGeometry.dispose(),this._buildCatchs[t].buildBufferGeometry=null,this._buildCatchs[t].buildMaterial=null,this._buildCatchs[t].vertices1=[],delete this._buildCatchs[t].vertices1,this._buildCatchs[t].normals1=[],delete this._buildCatchs[t].normals1,this._buildCatchs[t].uvs1=[],delete this._buildCatchs[t].uvs1,this._buildCatchs[t].colors1=[],delete this._buildCatchs[t].colors1,this._buildCatchs[t]=null);this.tb.map.repaint=!0,this.main.terminate()},t.prototype._getWorkerContent=function(){return"self.onmessage = mainFunc;function mainFunc(e) {    let row_cols = e.data['row_cols'];    for (let i = 0; i < row_cols.length; i++) {        self.num = i;        let url = row_cols[i].url;        let row = row_cols[i].row;        let col = row_cols[i].col;        fetch(url, {            method: 'get',            responseType: 'arraybuffer'        }).then(function(response) {            return response.arrayBuffer();        }).then(function(data) {            var res = geobuf.decode(new Pbf(data));            return {                data: res,                url: url,                row: row,                col: col,            };        }).then(function(data){            var name = data.url;            var url1 = name.split('/');            var url2 = url1[2] + '_' + url1[3];            var url3 = url1[4].split('.');            url2 = url2 + '_' + url3[0];            var row = data.row;             var col = data.col;            var res = data.data;            if (res.features.length > 0) {                var buildings = [];                var _position = res.features[0].properties.coord;                _position = _position.split(',');                var position = {                    lon: parseFloat(_position[0]),                    lat: parseFloat(_position[1])                };                 for (var i = 0; i < res.features.length; i++) {                     var points = res.features[i].geometry.coordinates[                        0];                    points.pop();                    buildings.push({                        points2: points,                         height: (threeboxUtil.projectToWorld([                            _position[0],                            _position[1],                            parseFloat(res.features[i].properties.HEIGH)                        ])).z,                        height_real: parseFloat(res.features[i].properties.HEIGH)                    });                };                var buildMaterial = {};                buildMaterial.wall = [];                var buildMaterialWallNumber = e.data['buildMaterial-wall-number'];                for (var i = 0; i < buildMaterialWallNumber; i++) {                    buildMaterial.wall[i] = {};                    buildMaterial.wall[i].minV = e.data['wall-' + i + '-minV'];                    buildMaterial.wall[i].maxV = e.data['wall-' + i + '-maxV'];                    buildMaterial.wall[i].vecColor = e.data['wall-' + i + '-vecColor'];                    buildMaterial.wall[i].repeat = e.data['wall-' + i + '-repeat'];                    buildMaterial.wall[i].operatUV = e.data['wall-' + i + '-operatUV'];                    buildMaterial.wall[i].topColor = e.data['wall-' + i + '-topColor'];                    buildMaterial.wall[i].bottomColor = e.data['wall-' + i + '-bottomColor'];                    buildMaterial.wall[i].fnumber = e.data['wall-' + i + '-fnumber'];                    buildMaterial.wall[i].wnumber = e.data['wall-' + i + '-wnumber'];                    buildMaterial.wall[i].vertices = [];                    buildMaterial.wall[i].normals = [];                    buildMaterial.wall[i].uvs = [];                    buildMaterial.wall[i].colors = [];                    buildMaterial.wall[i].topvertices = [];                    buildMaterial.wall[i].topnormals = [];                    buildMaterial.wall[i].topuvs = [];                    buildMaterial.wall[i].topcolors = [];                }                var buildMaterialKey = e.data['buildMaterial-key'];                var buildMaterialType = e.data['buildMaterial-type'];                if (buildings.length === 0) {                    return;                }                for (var j = 0; j < buildings.length; j++) {                    var index = 0;                    var value = 0;                    if (buildMaterialKey == 'height') {                        value = buildings[j].height_real;                    }                    for (var i = 0; i < buildMaterial.wall.length; i++) {                        if (buildMaterialType == 'range') {                            if (value >= e.data['wall-' + i + '-minV'] && value < e.data['wall-' + i + '-maxV']) {                                index = i;                                break;                            }                        } else {                            if (value === e.data['wall-' + i + '-value']) {                                index = i;                                break;                            }                        }                    }                    threeboxUtil.extrudeGeometry(buildings[j].points2, buildings[j].height, buildings[j].height_real / 3, buildMaterial.wall[index]);                }                var vertices1 = [];                var normals1 = [];                var uvs1 = [];                var colors1 = [];                var groups = [];                for (var i = 0; i < buildMaterialWallNumber; i++) {                    var topvertices = buildMaterial.wall[i].topvertices;                    var topnormals = buildMaterial.wall[i].topnormals;                    var topuvs = buildMaterial.wall[i].topuvs;                    var topcolors = buildMaterial.wall[i].topcolors;                    var vertices = buildMaterial.wall[i].vertices;                    var normals = buildMaterial.wall[i].normals;                    var uvs = buildMaterial.wall[i].uvs;                    var colors = buildMaterial.wall[i].colors;                    groups.push({                        start: vertices1.length / 3,                        count: topvertices.length / 3,                        materialIndex: i * 2                    });                    for (var m = 0; m < topvertices.length; m++) {                        vertices1.push(topvertices[m]);                    }                    for (var m = 0; m < topnormals.length; m++) {                        normals1.push(topnormals[m]);                    }                    for (var m = 0; m < topuvs.length; m++) {                        uvs1.push(topuvs[m]);                    }                    for (var m = 0; m < topcolors.length; m++) {                        colors1.push(topcolors[m]);                    }                               groups.push({                        start: vertices1.length / 3,                        count: vertices.length / 3,                        materialIndex: i * 2 + 1                    });                    for (var m = 0; m < vertices.length; m++) {                        vertices1.push(vertices[m]);                    }                    for (var m = 0; m < normals.length; m++) {                        normals1.push(normals[m]);                    }                    for (var m = 0; m < uvs.length; m++) {                        uvs1.push(uvs[m]);                    }                    for (var m = 0; m < colors.length; m++) {                        colors1.push(colors[m]);                    }                }                groups = groups;                var v1 = new Float32Array(vertices1.length);                v1.set(vertices1);                var n1 = new Float32Array(normals1.length);                n1.set(normals1);                var u1 = new Float32Array(uvs1.length);                u1.set(uvs1);                var c1 = new Float32Array(colors1.length);                c1.set(colors1);                var newData = {                    vertices1: v1,                    normals1: n1,                    uvs1: u1,                    colors1: c1,                    groups: groups,                    position: position,                    name:   row + '_' + col,                    row: row,                    col: col                };                if (vertices1.length == 0) {                    v1.length = 0;                    delete v1;                    n1.length = 0;                    delete n1;                    u1.length = 0;                    delete u1;                    c1.length = 0;                    delete c1;                } else {                    postMessage(newData);                    v1.length = 0;                    delete v1;                    n1.length = 0;                    delete n1;                    u1.length = 0;                    delete u1;                    c1.length = 0;                    delete c1;                }            }        })    }};var threeboxUtil = {    projectToWorld: function (coords) {        var projected = [            -THREEBOX.MERCATOR_A * THREEBOX.DEG2RAD * coords[0] * THREEBOX.PROJECTION_WORLD_SIZE,            -THREEBOX.MERCATOR_A * Math.log(Math.tan((Math.PI * 0.25) + (0.5 * THREEBOX.DEG2RAD * coords[1]))) * THREEBOX.PROJECTION_WORLD_SIZE        ];        if (!coords[2]) projected.push(0);        else {            var pixelsPerMeter = this.projectedUnitsPerMeter(coords[1]);            projected.push(coords[2] * pixelsPerMeter);        }        return {            x: projected[0],            y: projected[1],            z: projected[2]        };    },    projectedUnitsPerMeter: function (latitude) {        return Math.abs(THREEBOX.WORLD_SIZE / Math.cos(THREEBOX.DEG2RAD * latitude) / THREEBOX.EARTH_CIRCUMFERENCE);    },    extrudeGeometry: function (points, height, floorNum, wall) {        var p = points[0];        var vertices = [{            x: p[0],            y: p[1]        }];        var minx = p[0],            maxx = p[0],            miny = p[0],            maxy = p[0];        for (var i = 1; i < points.length; i++) {            if (points[i][0] > maxx) {                maxx = points[i][0];            }            if (points[i][0] < minx) {                minx = points[i][0];            }            if (points[i][1] > maxy) {                maxy = points[i][1];            }            if (points[i][1] < miny) {                miny = points[i][1];            }            vertices.push({                x: points[i][0],                y: points[i][1]            });        }        var x = maxx - minx;        var y = maxy - miny;        var color1 = [],            color2 = [];        if (wall.vecColor) {            color1 = colorRgb(wall.topColor);            color2 = colorRgb(wall.bottomColor);        }        var triangles = ShapeUtils.triangulateShape(vertices, []);        for (var i = 0; i < triangles.length; i++) {            var f = triangles[i];            wall.topvertices.push(vertices[f[0]].x, vertices[f[0]].y, 0 + height);            wall.topvertices.push(vertices[f[1]].x, vertices[f[1]].y, 0 + height);            wall.topvertices.push(vertices[f[2]].x, vertices[f[2]].y, 0 + height);            wall.topnormals.push(0, 0, 1);            wall.topnormals.push(0, 0, 1);            wall.topnormals.push(0, 0, 1);            wall.topuvs.push((vertices[f[0]].x - minx) / x, (vertices[f[0]].y - miny) / y);            wall.topuvs.push((vertices[f[1]].x - minx) / x, (vertices[f[1]].y - miny) / y);            wall.topuvs.push((vertices[f[2]].x - minx) / x, (vertices[f[2]].y - miny) / y);            if (wall.vecColor) {                wall.topcolors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);                wall.topcolors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);                wall.topcolors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);            }        }        var len = 0;        var v1 = 0;        if(wall.operatUV){            var newPoints = points.slice(0,points.length);           newPoints.push(newPoints[0]);           len = getLength(newPoints);        }        for (var i = 0; i < points.length; i++) {            var m = i;            var t = m + 1;            if (t == points.length) {                t = 0;            }            var n = normal(points[t][0] - points[i][0], points[t][1] - points[i][1]);            wall.vertices.push(points[i][0], points[i][1],  0 + height);            wall.vertices.push(points[t][0], points[t][1], 0);            wall.vertices.push(points[i][0], points[i][1],  0);            wall.normals.push(n.x, n.y, 0);            wall.normals.push(n.x, n.y, 0);            wall.normals.push(n.x, n.y, 0);            if (wall.vecColor) {                wall.colors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);                wall.colors.push(color2[0] / 256, color2[1] / 256, color2[2] / 256);                wall.colors.push(color2[0] / 256, color2[1] / 256, color2[2] / 256);            }            var q = Math.round(floorNum / wall.fnumber);            var v2 = 1;            if(wall.operatUV){                var tempLen = len;                if( t > 0 ){                   var tempLen = getLength(points.slice(0,t+1));                }                v2 = wall.repeat?(tempLen / (height/floorNum)/wall.wnumber):(tempLen / height);            }            if (wall.repeat) {                wall.uvs.push(q/1, v2);                wall.uvs.push(0, v1);                wall.uvs.push(0, v2);            } else {                wall.uvs.push(1, v2);                wall.uvs.push(0, v1);                wall.uvs.push(0, v2);            }            wall.vertices.push(points[i][0], points[i][1], /*points[i][2] +*/ height);            wall.vertices.push(points[t][0], points[t][1], /*points[t][2] +*/ height);            wall.vertices.push(points[t][0], points[t][1], /*points[t][2]*/ 0);            wall.normals.push(n.x, n.y, 0);            wall.normals.push(n.x, n.y, 0);            wall.normals.push(n.x, n.y, 0);            if (wall.vecColor) {                wall.colors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);                wall.colors.push(color1[0] / 256, color1[1] / 256, color1[2] / 256);                wall.colors.push(color2[0] / 256, color2[1] / 256, color2[2] / 256);            }            if (wall.repeat) {                wall.uvs.push(q / 1, v2);                wall.uvs.push(q / 1, v1);                wall.uvs.push(0, v1);            } else {                wall.uvs.push(1, v2);                wall.uvs.push(1, v1);                wall.uvs.push(0, v1);            }            if(wall.operatUV){                v1 = v2;            }        }    }};var THREEBOX = {    WORLD_SIZE: 1024000,    PROJECTION_WORLD_SIZE: 1024000 / (6378137.0 * Math.PI * 2),    MERCATOR_A: 6378137.0,     DEG2RAD: Math.PI / 180,    RAD2DEG: 180 / Math.PI,    EARTH_CIRCUMFERENCE: 40075000, };function normal(x, y) {    return {        x: y / (Math.sqrt(x * x + y * y)),        y: -x / (Math.sqrt(x * x + y * y))    };};function getLength(points){   var len = 0;    for(var i = 0; i < points.length-1; i++){        var x = Math.abs(points[i+1][0]-points[i][0]);        var y = Math.abs(points[i+1][1]-points[i][1]);        len += Math.sqrt(x*x+y*y);    }    return len;};function colorRgb(sColor) {    var reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;    var sColor = sColor.toLowerCase();    if (sColor && reg.test(sColor)) {        if (sColor.length === 4) {            var sColorNew = '#';            for (var i = 1; i < 4; i += 1) {                sColorNew += sColor.slice(i, i + 1).concat(sColor.slice(i, i + 1));            }            sColor = sColorNew;        }        var sColorChange = [];        for (var i = 1; i < 7; i += 2) {            sColorChange.push(parseInt('0x' + sColor.slice(i, i + 2)));        }        return sColorChange;    } else {        return sColor;    }};var Earcut = {    triangulate: function (data, holeIndices, dim) {        dim = dim || 2;        var hasHoles = holeIndices && holeIndices.length,            outerLen = hasHoles ? holeIndices[0] * dim : data.length,            outerNode = linkedList(data, 0, outerLen, dim, true),            triangles = [];        if (!outerNode || outerNode.next === outerNode.prev) return triangles;        var minX, minY, maxX, maxY, x, y, invSize;        if (hasHoles) outerNode = eliminateHoles(data, holeIndices, outerNode, dim);             if (data.length > 80 * dim) {            minX = maxX = data[0];            minY = maxY = data[1];            for (var i = dim; i < outerLen; i += dim) {                x = data[i];                y = data[i + 1];                if (x < minX) minX = x;                if (y < minY) minY = y;                if (x > maxX) maxX = x;                if (y > maxY) maxY = y;            }                      invSize = Math.max(maxX - minX, maxY - minY);            invSize = invSize !== 0 ? 1 / invSize : 0;        }        earcutLinked(outerNode, triangles, dim, minX, minY, invSize);        return triangles;    }};function linkedList(data, start, end, dim, clockwise) {    var i, last;    if (clockwise === (signedArea(data, start, end, dim) > 0)) {        for (i = start; i < end; i += dim) last = insertNode(i, data[i], data[i + 1], last);    } else {        for (i = end - dim; i >= start; i -= dim) last = insertNode(i, data[i], data[i + 1], last);    }    if (last && equals(last, last.next)) {        removeNode(last);        last = last.next;    }    return last;};function filterPoints(start, end) {    if (!start) return start;    if (!end) end = start;    var p = start,        again;    do {        again = false;        if (!p.steiner && (equals(p, p.next) || area(p.prev, p, p.next) === 0)) {            removeNode(p);            p = end = p.prev;            if (p === p.next) break;            again = true;        } else {            p = p.next;        }    } while (again || p !== end);    return end;};function earcutLinked(ear, triangles, dim, minX, minY, invSize, pass) {    if (!ear) return;    if (!pass && invSize) indexCurve(ear, minX, minY, invSize);    var stop = ear,        prev, next;     while (ear.prev !== ear.next) {        prev = ear.prev;        next = ear.next;        if (invSize ? isEarHashed(ear, minX, minY, invSize) : isEar(ear)) {                 triangles.push(prev.i / dim);            triangles.push(ear.i / dim);            triangles.push(next.i / dim);            removeNode(ear);            ear = next.next;            stop = next.next;            continue;        }        ear = next;         if (ear === stop) {                    if (!pass) {                earcutLinked(filterPoints(ear), triangles, dim, minX, minY, invSize, 1);                  } else if (pass === 1) {                ear = cureLocalIntersections(ear, triangles, dim);                earcutLinked(ear, triangles, dim, minX, minY, invSize, 2);                        } else if (pass === 2) {                splitEarcut(ear, triangles, dim, minX, minY, invSize);            }            break;        }    }};function isEar(ear) {    var a = ear.prev,        b = ear,        c = ear.next;    if (area(a, b, c) >= 0) return false;     var p = ear.next.next;    while (p !== ear.prev) {        if (pointInTriangle(a.x, a.y, b.x, b.y, c.x, c.y, p.x, p.y) &&            area(p.prev, p, p.next) >= 0) return false;        p = p.next;    }    return true;};function isEarHashed(ear, minX, minY, invSize) {    var a = ear.prev,        b = ear,        c = ear.next;    if (area(a, b, c) >= 0) return false;        var minTX = a.x < b.x ? (a.x < c.x ? a.x : c.x) : (b.x < c.x ? b.x : c.x),        minTY = a.y < b.y ? (a.y < c.y ? a.y : c.y) : (b.y < c.y ? b.y : c.y),        maxTX = a.x > b.x ? (a.x > c.x ? a.x : c.x) : (b.x > c.x ? b.x : c.x),        maxTY = a.y > b.y ? (a.y > c.y ? a.y : c.y) : (b.y > c.y ? b.y : c.y);      var minZ = zOrder(minTX, minTY, minX, minY, invSize),        maxZ = zOrder(maxTX, maxTY, minX, minY, invSize);    var p = ear.prevZ,        n = ear.nextZ;     while (p && p.z >= minZ && n && n.z <= maxZ) {        if (p !== ear.prev && p !== ear.next &&            pointInTriangle(a.x, a.y, b.x, b.y, c.x, c.y, p.x, p.y) &&            area(p.prev, p, p.next) >= 0) return false;        p = p.prevZ;        if (n !== ear.prev && n !== ear.next &&            pointInTriangle(a.x, a.y, b.x, b.y, c.x, c.y, n.x, n.y) &&            area(n.prev, n, n.next) >= 0) return false;        n = n.nextZ;    }    while (p && p.z >= minZ) {        if (p !== ear.prev && p !== ear.next &&            pointInTriangle(a.x, a.y, b.x, b.y, c.x, c.y, p.x, p.y) &&            area(p.prev, p, p.next) >= 0) return false;        p = p.prevZ;    }        while (n && n.z <= maxZ) {        if (n !== ear.prev && n !== ear.next &&            pointInTriangle(a.x, a.y, b.x, b.y, c.x, c.y, n.x, n.y) &&            area(n.prev, n, n.next) >= 0) return false;        n = n.nextZ;    }    return true;};function cureLocalIntersections(start, triangles, dim) {    var p = start;    do {        var a = p.prev,            b = p.next.next;        if (!equals(a, b) && intersects(a, p, p.next, b) && locallyInside(a, b) && locallyInside(b, a)) {            triangles.push(a.i / dim);            triangles.push(p.i / dim);            triangles.push(b.i / dim);            removeNode(p);            removeNode(p.next);            p = start = b;        }        p = p.next;    } while (p !== start);    return p;};function splitEarcut(start, triangles, dim, minX, minY, invSize) {    var a = start;    do {        var b = a.next.next;        while (b !== a.prev) {            if (a.i !== b.i && isValidDiagonal(a, b)) {                     var c = splitPolygon(a, b);                      a = filterPoints(a, a.next);                c = filterPoints(c, c.next);                earcutLinked(a, triangles, dim, minX, minY, invSize);                earcutLinked(c, triangles, dim, minX, minY, invSize);                return;            }            b = b.next;        }        a = a.next;    } while (a !== start);};function eliminateHoles(data, holeIndices, outerNode, dim) {    var queue = [],        i, len, start, end, list;    for (i = 0, len = holeIndices.length; i < len; i++) {        start = holeIndices[i] * dim;        end = i < len - 1 ? holeIndices[i + 1] * dim : data.length;        list = linkedList(data, start, end, dim, false);        if (list === list.next) list.steiner = true;        queue.push(getLeftmost(list));    }    queue.sort(compareX);    for (i = 0; i < queue.length; i++) {        eliminateHole(queue[i], outerNode);        outerNode = filterPoints(outerNode, outerNode.next);    }    return outerNode;};function compareX(a, b) {    return a.x - b.x;};function eliminateHole(hole, outerNode) {    outerNode = findHoleBridge(hole, outerNode);    if (outerNode) {        var b = splitPolygon(outerNode, hole);        filterPoints(b, b.next);    }};function findHoleBridge(hole, outerNode) {    var p = outerNode,        hx = hole.x,        hy = hole.y,        qx = -Infinity,        m;    do {        if (hy <= p.y && hy >= p.next.y && p.next.y !== p.y) {            var x = p.x + (hy - p.y) * (p.next.x - p.x) / (p.next.y - p.y);            if (x <= hx && x > qx) {                qx = x;                if (x === hx) {                    if (hy === p.y) return p;                    if (hy === p.next.y) return p.next;                }                m = p.x < p.next.x ? p : p.next;            }        }        p = p.next;    } while (p !== outerNode);    if (!m) return null;    if (hx === qx) return m.prev;     var stop = m,        mx = m.x,        my = m.y,        tanMin = Infinity,        tan;    p = m.next;    while (p !== stop) {        if (hx >= p.x && p.x >= mx && hx !== p.x &&            pointInTriangle(hy < my ? hx : qx, hy, mx, my, hy < my ? qx : hx, hy, p.x, p.y)) {            tan = Math.abs(hy - p.y) / (hx - p.x);             if ((tan < tanMin || (tan === tanMin && p.x > m.x)) && locallyInside(p, hole)) {                m = p;                tanMin = tan;            }        }        p = p.next;    }    return m;};function indexCurve(start, minX, minY, invSize) {    var p = start;    do {        if (p.z === null) p.z = zOrder(p.x, p.y, minX, minY, invSize);        p.prevZ = p.prev;        p.nextZ = p.next;        p = p.next;    } while (p !== start);    p.prevZ.nextZ = null;    p.prevZ = null;    sortLinked(p);};function sortLinked(list) {    var i, p, q, e, tail, numMerges, pSize, qSize,        inSize = 1;    do {        p = list;        list = null;        tail = null;        numMerges = 0;        while (p) {            numMerges++;            q = p;            pSize = 0;            for (i = 0; i < inSize; i++) {                pSize++;                q = q.nextZ;                if (!q) break;            }            qSize = inSize;            while (pSize > 0 || (qSize > 0 && q)) {                if (pSize !== 0 && (qSize === 0 || !q || p.z <= q.z)) {                    e = p;                    p = p.nextZ;                    pSize--;                } else {                    e = q;                    q = q.nextZ;                    qSize--;                }                if (tail) tail.nextZ = e;                else list = e;                e.prevZ = tail;                tail = e;            }            p = q;        }        tail.nextZ = null;        inSize *= 2;    } while (numMerges > 1);    return list;};function zOrder(x, y, minX, minY, invSize) {    x = 32767 * (x - minX) * invSize;    y = 32767 * (y - minY) * invSize;    x = (x | (x << 8)) & 0x00FF00FF;    x = (x | (x << 4)) & 0x0F0F0F0F;    x = (x | (x << 2)) & 0x33333333;    x = (x | (x << 1)) & 0x55555555;    y = (y | (y << 8)) & 0x00FF00FF;    y = (y | (y << 4)) & 0x0F0F0F0F;    y = (y | (y << 2)) & 0x33333333;    y = (y | (y << 1)) & 0x55555555;    return x | (y << 1);};function getLeftmost(start) {    var p = start,        leftmost = start;    do {        if (p.x < leftmost.x || (p.x === leftmost.x && p.y < leftmost.y)) leftmost = p;        p = p.next;    } while (p !== start);    return leftmost;};function pointInTriangle(ax, ay, bx, by, cx, cy, px, py) {    return (cx - px) * (ay - py) - (ax - px) * (cy - py) >= 0 &&        (ax - px) * (by - py) - (bx - px) * (ay - py) >= 0 &&        (bx - px) * (cy - py) - (cx - px) * (by - py) >= 0;};function isValidDiagonal(a, b) {    return a.next.i !== b.i && a.prev.i !== b.i && !intersectsPolygon(a, b) &&        locallyInside(a, b) && locallyInside(b, a) && middleInside(a, b);};function area(p, q, r) {    return (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);};function equals(p1, p2) {    return p1.x === p2.x && p1.y === p2.y;};function intersects(p1, q1, p2, q2) {    if ((equals(p1, p2) && equals(q1, q2)) ||        (equals(p1, q2) && equals(p2, q1))) return true;    return area(p1, q1, p2) > 0 !== area(p1, q1, q2) > 0 &&        area(p2, q2, p1) > 0 !== area(p2, q2, q1) > 0;};function intersectsPolygon(a, b) {    var p = a;    do {        if (p.i !== a.i && p.next.i !== a.i && p.i !== b.i && p.next.i !== b.i &&            intersects(p, p.next, a, b)) return true;        p = p.next;    } while (p !== a);    return false;};function locallyInside(a, b) {    return area(a.prev, a, a.next) < 0 ?        area(a, b, a.next) >= 0 && area(a, a.prev, b) >= 0 :        area(a, b, a.prev) < 0 || area(a, a.next, b) < 0;};function middleInside(a, b) {    var p = a,        inside = false,        px = (a.x + b.x) / 2,        py = (a.y + b.y) / 2;    do {        if (((p.y > py) !== (p.next.y > py)) && p.next.y !== p.y &&            (px < (p.next.x - p.x) * (py - p.y) / (p.next.y - p.y) + p.x))            inside = !inside;        p = p.next;    } while (p !== a);    return inside;};function splitPolygon(a, b) {    var a2 = new Node(a.i, a.x, a.y),        b2 = new Node(b.i, b.x, b.y),        an = a.next,        bp = b.prev;    a.next = b;    b.prev = a;    a2.next = an;    an.prev = a2;    b2.next = a2;    a2.prev = b2;    bp.next = b2;    b2.prev = bp;    return b2;};function insertNode(i, x, y, last) {    var p = new Node(i, x, y);    if (!last) {        p.prev = p;        p.next = p;    } else {        p.next = last.next;        p.prev = last;        last.next.prev = p;        last.next = p;    }    return p;};function removeNode(p) {    p.next.prev = p.prev;    p.prev.next = p.next;    if (p.prevZ) p.prevZ.nextZ = p.nextZ;    if (p.nextZ) p.nextZ.prevZ = p.prevZ;};function Node(i, x, y) {    this.i = i;    this.x = x;    this.y = y;    this.prev = null;    this.next = null;    this.z = null;    this.prevZ = null;    this.nextZ = null;     this.steiner = false;};function signedArea(data, start, end, dim) {    var sum = 0;    for (var i = start, j = end - dim; i < end; i += dim) {        sum += (data[j] - data[i]) * (data[i + 1] + data[j + 1]);        j = i;    }    return sum;}; var ShapeUtils = {    area: function (contour) {        var n = contour.length;        var a = 0.0;        for (var p = n - 1, q = 0; q < n; p = q++) {            a += contour[p].x * contour[q].y - contour[q].x * contour[p].y;        }        return a * 0.5;    },    isClockWise: function (pts) {        return ShapeUtils.area(pts) < 0;    },    triangulateShape: function (contour, holes) {        var vertices = [];         var holeIndices = [];         var faces = [];         removeDupEndPts(contour);         addContour(vertices, contour);                 var holeIndex = contour.length;        holes.forEach(removeDupEndPts);        for (var i = 0; i < holes.length; i++) {            holeIndices.push(holeIndex);            holeIndex += holes[i].length;            addContour(vertices, holes[i]);        }                var triangles = Earcut.triangulate(vertices, holeIndices);                for (var i = 0; i < triangles.length; i += 3) {            faces.push(triangles.slice(i, i + 3));        }        return faces;     }};function removeDupEndPts(points) {    var l = points.length;    if (l > 2 && points[l - 1].x == points[0].x && points[l - 1].y == points[0].y) {        points.pop();    }};function addContour(vertices, contour) {    for (var i = 0; i < contour.length; i++) {        vertices.push(contour[i].x);        vertices.push(contour[i].y);    }};! function (e) {    if ('object' == typeof exports && 'undefined' != typeof module) module.exports = e();    else if ('function' == typeof define && define.amd) define([], e);    else {        var t;        t = 'undefined' != typeof window ? window : 'undefined' != typeof global ? global : 'undefined' != typeof self ? self : this, t.geobuf = e()    }}(function () {    return function e(t, r, n) {        function i(a, u) {            if (!r[a]) {                if (!t[a]) {                    var f = 'function' == typeof require && require;                    if (!u && f) return f(a, !0);                    if (o) return o(a, !0);                }                var s = r[a] = {                    exports: {}                };                t[a][0].call(s.exports, function (e) {                    var r = t[a][1][e];                    return i(r ? r : e)                }, s, s.exports, e, t, r, n)            }            return r[a].exports        }        for (var o = 'function' == typeof require && require, a = 0; a < n.length; a++) i(n[a]);        return i    }({        1: [function (e, t) {            'use strict';            function r(e) {                P = 2, S = Math.pow(10, 6), V = null, M = [], w = [];                var t = e.readFields(n, {});                return M = null, t            }            function n(e, t, r) {                1 === e ? M.push(r.readString()) : 2 === e ? P = r.readVarint() : 3 === e ? S = Math.pow(10, r.readVarint()) : 4 === e ? i(r, t) : 5 === e ? o(r, t) : 6 === e && a(r, t)            }            function i(e, t) {                return t.type = 'FeatureCollection', t.features = [], e.readMessage(u, t)            }            function o(e, t) {                return t.type = 'Feature', e.readMessage(f, t)            }            function a(e, t) {                return e.readMessage(l, t)            }            function u(e, t, r) {                1 === e ? t.features.push(o(r, {})) : 13 === e ? w.push(d(r)) : 15 === e && c(r, t)            }            function f(e, t, r) {                1 === e ? t.geometry = a(r, {}) : 11 === e ? t.id = r.readString() : 12 === e ? t.id = r.readSVarint() : 13 === e ? w.push(d(r)) : 14 === e ? t.properties = c(r, {}) : 15 === e && c(r, t)            }            function l(e, t, r) {                1 === e ? t.type = m[r.readVarint()] : 2 === e ? V = r.readPackedVarint() : 3 === e ? s(t, r, t.type) : 4 === e ? (t.geometries = t.geometries || [], t.geometries.push(a(r, {}))) : 13 === e ? w.push(d(r)) : 15 === e && c(r, t)            }            function s(e, t, r) {                'Point' === r ? e.coordinates = p(t) : 'MultiPoint' === r ? e.coordinates = y(t, !0) : 'LineString' === r ? e.coordinates = y(t) : 'MultiLineString' === r ? e.coordinates = h(t) : 'Polygon' === r ? e.coordinates = h(t, !0) : 'MultiPolygon' === r && (e.coordinates = v(t))            }            function d(e) {                for (var t = e.readVarint() + e.pos, r = null; e.pos < t;) {                    var n = e.readVarint(),                        i = n >> 3;                    1 === i ? r = e.readString() : 2 === i ? r = e.readDouble() : 3 === i ? r = e.readVarint() : 4 === i ? r = -e.readVarint() : 5 === i ? r = e.readBoolean() : 6 === i && (r = JSON.parse(e.readString()))                }                return r            }            function c(e, t) {                for (var r = e.readVarint() + e.pos; e.pos < r;) t[M[e.readVarint()]] = w[e.readVarint()];                return w = [], t            }            function p(e) {                for (var t = e.readVarint() + e.pos, r = []; e.pos < t;) r.push(e.readSVarint() / S);                return r            }            function g(e, t, r, n) {                var i, o, a = 0,                    u = [],                    f = [];                for (o = 0; P > o; o++) f[o] = 0;                for (; r ? r > a : e.pos < t;) {                    for (i = [], o = 0; P > o; o++) f[o] += e.readSVarint(), i[o] = f[o] / S;                    u.push(i), a++                }                return n && u.push(u[0]), u            }            function y(e) {                return g(e, e.readVarint() + e.pos)            }            function h(e, t) {                var r = e.readVarint() + e.pos;                if (!V) return [g(e, r, null, t)];                for (var n = [], i = 0; i < V.length; i++) n.push(g(e, r, V[i], t));                return V = null, n            }            function v(e) {                var t = e.readVarint() + e.pos;                if (!V) return [                    [g(e, t, null, !0)]                ];                for (var r = [], n = 1, i = 0; i < V[0]; i++) {                    for (var o = [], a = 0; a < V[n]; a++) o.push(g(e, t, V[n + 1 + a], !0));                    n += V[n] + 1, r.push(o)                }                return V = null, r            }            t.exports = r;            var M, w, V, P, S, m = ['Point', 'MultiPoint', 'LineString', 'MultiLineString', 'Polygon', 'MultiPolygon', 'GeometryCollection']        }, {}],        2: [function (e, t) {            'use strict';            function r(e, t) {                w = {}, V = 0, P = 0, S = 1, n(e), S = Math.min(S, m);                for (var r = Math.ceil(Math.log(S) / Math.LN10), i = Object.keys(w), o = 0; o < i.length; o++) t.writeStringField(1, i[o]);                return 2 !== P && t.writeVarintField(2, P), 6 !== r && t.writeVarintField(3, r), 'FeatureCollection' === e.type ? t.writeMessage(4, f, e) : 'Feature' === e.type ? t.writeMessage(5, l, e) : t.writeMessage(6, s, e), w = null, t.finish()            }            function n(e) {                var t, r;                if ('FeatureCollection' === e.type)                    for (t = 0; t < e.features.length; t++) n(e.features[t]);                else if ('Feature' === e.type) {                    n(e.geometry);                    for (r in e.properties) u(r)                } else if ('Point' === e.type) a(e.coordinates);                else if ('MultiPoint' === e.type) o(e.coordinates);                else if ('GeometryCollection' === e.type)                    for (t = 0; t < e.geometries.length; t++) n(e.geometries[t]);                else if ('LineString' === e.type) o(e.coordinates);                else if ('Polygon' === e.type || 'MultiLineString' === e.type) i(e.coordinates);                else if ('MultiPolygon' === e.type)                    for (t = 0; t < e.coordinates.length; t++) i(e.coordinates[t]);                for (r in e) M(r, e.type) || u(r)            }            function i(e) {                for (var t = 0; t < e.length; t++) o(e[t])            }            function o(e) {                for (var t = 0; t < e.length; t++) a(e[t])            }            function a(e) {                P = Math.max(P, e.length);                for (var t = 0; t < e.length; t++)                    for (; Math.round(e[t] * S) / S !== e[t] && m > S;) S *= 10            }            function u(e) {                void 0 === w[e] && (w[e] = V++)            }            function f(e, t) {                for (var r = 0; r < e.features.length; r++) t.writeMessage(1, l, e.features[r]);                d(e, t, !0)            }            function l(e, t) {                t.writeMessage(1, s, e.geometry), void 0 !== e.id && ('number' == typeof e.id && e.id % 1 === 0 ? t.writeSVarintField(12, e.id) : t.writeStringField(11, e.id)), e.properties && d(e.properties, t), d(e, t, !0)            }            function s(e, t) {                t.writeVarintField(1, F[e.type]);                var r = e.coordinates;                if ('Point' === e.type) p(r, t);                else if ('MultiPoint' === e.type) g(r, t, !0);                else if ('LineString' === e.type) g(r, t);                else if ('MultiLineString' === e.type) y(r, t);                else if ('Polygon' === e.type) y(r, t, !0);                else if ('MultiPolygon' === e.type) h(r, t);                else if ('GeometryCollection' === e.type)                    for (var n = 0; n < e.geometries.length; n++) t.writeMessage(4, s, e.geometries[n]);                d(e, t, !0)            }            function d(e, t, r) {                var n = [],                    i = 0;                for (var o in e) r && M(o, e.type) || (t.writeMessage(13, c, e[o]), n.push(w[o]), n.push(i++));                t.writePackedVarint(r ? 15 : 14, n)            }            function c(e, t) {                var r = typeof e;                'string' === r ? t.writeStringField(1, e) : 'boolean' === r ? t.writeBooleanField(5, e) : 'object' === r ? t.writeStringField(6, JSON.stringify(e)) : 'number' === r && (e % 1 !== 0 ? t.writeDoubleField(2, e) : e >= 0 ? t.writeVarintField(3, e) : t.writeVarintField(4, -e))            }            function p(e, t) {                for (var r = [], n = 0; P > n; n++) r.push(Math.round(e[n] * S));                t.writePackedSVarint(3, r)            }            function g(e, t) {                var r = [];                v(r, e), t.writePackedSVarint(3, r)            }            function y(e, t, r) {                var n, i = e.length;                if (1 !== i) {                    var o = [];                    for (n = 0; i > n; n++) o.push(e[n].length - (r ? 1 : 0));                    t.writePackedVarint(2, o)                }                var a = [];                for (n = 0; i > n; n++) v(a, e[n], r);                t.writePackedSVarint(3, a)            }            function h(e, t) {                var r, n, i = e.length;                if (1 !== i || 1 !== e[0].length) {                    var o = [i];                    for (r = 0; i > r; r++)                        for (o.push(e[r].length), n = 0; n < e[r].length; n++) o.push(e[r][n].length - 1);                    t.writePackedVarint(2, o)                }                var a = [];                for (r = 0; i > r; r++)                    for (n = 0; n < e[r].length; n++) v(a, e[r][n], !0);                t.writePackedSVarint(3, a)            }            function v(e, t, r) {                var n, i, o = t.length - (r ? 1 : 0),                    a = new Array(P);                for (i = 0; P > i; i++) a[i] = 0;                for (n = 0; o > n; n++)                    for (i = 0; P > i; i++) {                        var u = Math.round(t[n][i] * S) - a[i];                        e.push(u), a[i] += u                    }            }            function M(e, t) {                if ('type' === e) return !0;                if ('FeatureCollection' === t) {                    if ('features' === e) return !0                } else if ('Feature' === t) {                    if ('id' === e || 'properties' === e || 'geometry' === e) return !0                } else if ('GeometryCollection' === t) {                    if ('geometries' === e) return !0                } else if ('coordinates' === e) return !0;                return !1            }            t.exports = r;            var w, V, P, S, m = 1e6,                F = {                    Point: 0,                    MultiPoint: 1,                    LineString: 2,                    MultiLineString: 3,                    Polygon: 4,                    MultiPolygon: 5,                    GeometryCollection: 6                }        }, {}],        3: [function (e, t, r) {            'use strict';            r.encode = e('./encode'), r.decode = e('./decode')        }, {            './decode': 1,            './encode': 2        }]    }, {}, [3])(3)});! function (t) {    if ('object' == typeof exports && 'undefined' != typeof module) module.exports = t();    else if ('function' == typeof define && define.amd) define([], t);    else {        var i;        i = 'undefined' != typeof window ? window : 'undefined' != typeof global ? global : 'undefined' != typeof self ? self : this, i.Pbf = t()    }}(function () {    return function t(i, e, r) {        function s(o, h) {            if (!e[o]) {                if (!i[o]) {                    var a = 'function' == typeof require && require;                    if (!h && a) return a(o, !0);                    if (n) return n(o, !0);                            }                var f = e[o] = {                    exports: {}                };                i[o][0].call(f.exports, function (t) {                    var e = i[o][1][t];                    return s(e ? e : t)                }, f, f.exports, t, i, e, r)            }            return e[o].exports        }        for (var n = 'function' == typeof require && require, o = 0; o < r.length; o++) s(r[o]);        return s    }({        1: [function (t, i, e) {            'use strict';            function r(t) {                this.buf = ArrayBuffer.isView && ArrayBuffer.isView(t) ? t : new Uint8Array(t || 0), this.pos = 0, this.type = 0, this.length = this.buf.length            }            function s(t, i, e) {                var r, s, n = e.buf;                if (s = n[e.pos++], r = (112 & s) >> 4, s < 128) return o(t, r, i);                if (s = n[e.pos++], r |= (127 & s) << 3, s < 128) return o(t, r, i);                if (s = n[e.pos++], r |= (127 & s) << 10, s < 128) return o(t, r, i);                if (s = n[e.pos++], r |= (127 & s) << 17, s < 128) return o(t, r, i);                if (s = n[e.pos++], r |= (127 & s) << 24, s < 128) return o(t, r, i);                if (s = n[e.pos++], r |= (1 & s) << 31, s < 128) return o(t, r, i);                throw new Error('Expected varint not more than 10 bytes')            }            function n(t) {                return t.type === r.Bytes ? t.readVarint() + t.pos : t.pos + 1            }            function o(t, i, e) {                return e ? 4294967296 * i + (t >>> 0) : 4294967296 * (i >>> 0) + (t >>> 0)            }            function h(t, i) {                var e, r;                if (t >= 0 ? (e = t % 4294967296 | 0, r = t / 4294967296 | 0) : (e = ~(-t % 4294967296), r = ~(-t / 4294967296), 4294967295 ^ e ? e = e + 1 | 0 : (e = 0, r = r + 1 | 0)), t >= 0x10000000000000000 || t < -0x10000000000000000)                i.realloc(10), a(e, r, i), u(r, i)            }            function a(t, i, e) {                e.buf[e.pos++] = 127 & t | 128, t >>>= 7, e.buf[e.pos++] = 127 & t | 128, t >>>= 7, e.buf[e.pos++] = 127 & t | 128, t >>>= 7, e.buf[e.pos++] = 127 & t | 128, t >>>= 7, e.buf[e.pos] = 127 & t            }            function u(t, i) {                var e = (7 & t) << 4;                i.buf[i.pos++] |= e | ((t >>>= 3) ? 128 : 0), t && (i.buf[i.pos++] = 127 & t | ((t >>>= 7) ? 128 : 0), t && (i.buf[i.pos++] = 127 & t | ((t >>>= 7) ? 128 : 0), t && (i.buf[i.pos++] = 127 & t | ((t >>>= 7) ? 128 : 0), t && (i.buf[i.pos++] = 127 & t | ((t >>>= 7) ? 128 : 0), t && (i.buf[i.pos++] = 127 & t)))))            }            function f(t, i, e) {                var r = i <= 16383 ? 1 : i <= 2097151 ? 2 : i <= 268435455 ? 3 : Math.ceil(Math.log(i) / (7 * Math.LN2));                e.realloc(r);                for (var s = e.pos - 1; s >= t; s--) e.buf[s + r] = e.buf[s]            }            function d(t, i) {                for (var e = 0; e < t.length; e++) i.writeVarint(t[e])            }            function p(t, i) {                for (var e = 0; e < t.length; e++) i.writeSVarint(t[e])            }            function c(t, i) {                for (var e = 0; e < t.length; e++) i.writeFloat(t[e])            }            function l(t, i) {                for (var e = 0; e < t.length; e++) i.writeDouble(t[e])            }            function w(t, i) {                for (var e = 0; e < t.length; e++) i.writeBoolean(t[e])            }            function F(t, i) {                for (var e = 0; e < t.length; e++) i.writeFixed32(t[e])            }            function b(t, i) {                for (var e = 0; e < t.length; e++) i.writeSFixed32(t[e])            }            function v(t, i) {                for (var e = 0; e < t.length; e++) i.writeFixed64(t[e])            }            function g(t, i) {                for (var e = 0; e < t.length; e++) i.writeSFixed64(t[e])            }            function x(t, i) {                return (t[i] | t[i + 1] << 8 | t[i + 2] << 16) + 16777216 * t[i + 3]            }            function V(t, i, e) {                t[e] = i, t[e + 1] = i >>> 8, t[e + 2] = i >>> 16, t[e + 3] = i >>> 24            }            function y(t, i) {                return (t[i] | t[i + 1] << 8 | t[i + 2] << 16) + (t[i + 3] << 24)            }            function M(t, i, e) {                for (var r = '', s = i; s < e;) {                    var n = t[s],                        o = null,                        h = n > 239 ? 4 : n > 223 ? 3 : n > 191 ? 2 : 1;                    if (s + h > e) break;                    var a, u, f;                    1 === h ? n < 128 && (o = n) : 2 === h ? (a = t[s + 1], 128 === (192 & a) && (o = (31 & n) << 6 | 63 & a, o <= 127 && (o = null))) : 3 === h ? (a = t[s + 1], u = t[s + 2], 128 === (192 & a) && 128 === (192 & u) && (o = (15 & n) << 12 | (63 & a) << 6 | 63 & u, (o <= 2047 || o >= 55296 && o <= 57343) && (o = null))) : 4 === h && (a = t[s + 1], u = t[s + 2], f = t[s + 3], 128 === (192 & a) && 128 === (192 & u) && 128 === (192 & f) && (o = (15 & n) << 18 | (63 & a) << 12 | (63 & u) << 6 | 63 & f, (o <= 65535 || o >= 1114112) && (o = null))), null === o ? (o = 65533, h = 1) : o > 65535 && (o -= 65536, r += String.fromCharCode(o >>> 10 & 1023 | 55296), o = 56320 | 1023 & o), r += String.fromCharCode(o), s += h                }                return r            }            function S(t, i, e) {                for (var r, s, n = 0; n < i.length; n++) {                    if (r = i.charCodeAt(n), r > 55295 && r < 57344) {                        if (!s) {                            r > 56319 || n + 1 === i.length ? (t[e++] = 239, t[e++] = 191, t[e++] = 189) : s = r;                            continue                        }                        if (r < 56320) {                            t[e++] = 239, t[e++] = 191, t[e++] = 189, s = r;                            continue                        }                        r = s - 55296 << 10 | r - 56320 | 65536, s = null                    } else s && (t[e++] = 239, t[e++] = 191, t[e++] = 189, s = null);                    r < 128 ? t[e++] = r : (r < 2048 ? t[e++] = r >> 6 | 192 : (r < 65536 ? t[e++] = r >> 12 | 224 : (t[e++] = r >> 18 | 240, t[e++] = r >> 12 & 63 | 128), t[e++] = r >> 6 & 63 | 128), t[e++] = 63 & r | 128)                }                return e            }            i.exports = r;            var B = t('ieee754');            r.Varint = 0, r.Fixed64 = 1, r.Bytes = 2, r.Fixed32 = 5;            var k = 4294967296,                P = 1 / k;            r.prototype = {                destroy: function () {                    this.buf = null                },                readFields: function (t, i, e) {                    for (e = e || this.length; this.pos < e;) {                        var r = this.readVarint(),                            s = r >> 3,                            n = this.pos;                        this.type = 7 & r, t(s, i, this), this.pos === n && this.skip(r)                    }                    return i                },                readMessage: function (t, i) {                    return this.readFields(t, i, this.readVarint() + this.pos)                },                readFixed32: function () {                    var t = x(this.buf, this.pos);                    return this.pos += 4, t                },                readSFixed32: function () {                    var t = y(this.buf, this.pos);                    return this.pos += 4, t                },                readFixed64: function () {                    var t = x(this.buf, this.pos) + x(this.buf, this.pos + 4) * k;                    return this.pos += 8, t                },                readSFixed64: function () {                    var t = x(this.buf, this.pos) + y(this.buf, this.pos + 4) * k;                    return this.pos += 8, t                },                readFloat: function () {                    var t = B.read(this.buf, this.pos, !0, 23, 4);                    return this.pos += 4, t                },                readDouble: function () {                    var t = B.read(this.buf, this.pos, !0, 52, 8);                    return this.pos += 8, t                },                readVarint: function (t) {                    var i, e, r = this.buf;                    return e = r[this.pos++], i = 127 & e, e < 128 ? i : (e = r[this.pos++], i |= (127 & e) << 7, e < 128 ? i : (e = r[this.pos++], i |= (127 & e) << 14, e < 128 ? i : (e = r[this.pos++], i |= (127 & e) << 21, e < 128 ? i : (e = r[this.pos], i |= (15 & e) << 28, s(i, t, this)))))                },                readVarint64: function () {                    return this.readVarint(!0)                },                readSVarint: function () {                    var t = this.readVarint();                    return t % 2 === 1 ? (t + 1) / -2 : t / 2                },                readBoolean: function () {                    return Boolean(this.readVarint())                },                readString: function () {                    var t = this.readVarint() + this.pos,                        i = M(this.buf, this.pos, t);                    return this.pos = t, i                },                readBytes: function () {                    var t = this.readVarint() + this.pos,                        i = this.buf.subarray(this.pos, t);                    return this.pos = t, i                },                readPackedVarint: function (t, i) {                    var e = n(this);                    for (t = t || []; this.pos < e;) t.push(this.readVarint(i));                    return t                },                readPackedSVarint: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readSVarint());                    return t                },                readPackedBoolean: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readBoolean());                    return t                },                readPackedFloat: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readFloat());                    return t                },                readPackedDouble: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readDouble());                    return t                },                readPackedFixed32: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readFixed32());                    return t                },                readPackedSFixed32: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readSFixed32());                    return t                },                readPackedFixed64: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readFixed64());                    return t                },                readPackedSFixed64: function (t) {                    var i = n(this);                    for (t = t || []; this.pos < i;) t.push(this.readSFixed64());                    return t                },                skip: function (t) {                    var i = 7 & t;                    if (i === r.Varint)                        for (; this.buf[this.pos++] > 127;);                    else if (i === r.Bytes) this.pos = this.readVarint() + this.pos;                    else if (i === r.Fixed32) this.pos += 4;                    else {                        if (i !== r.Fixed64) throw new Error('Unimplemented type: ' + i);                        this.pos += 8                    }                },                writeTag: function (t, i) {                    this.writeVarint(t << 3 | i)                },                realloc: function (t) {                    for (var i = this.length || 16; i < this.pos + t;) i *= 2;                    if (i !== this.length) {                        var e = new Uint8Array(i);                        e.set(this.buf), this.buf = e, this.length = i                    }                },                finish: function () {                    return this.length = this.pos, this.pos = 0, this.buf.subarray(0, this.length)                },                writeFixed32: function (t) {                    this.realloc(4), V(this.buf, t, this.pos), this.pos += 4                },                writeSFixed32: function (t) {                    this.realloc(4), V(this.buf, t, this.pos), this.pos += 4                },                writeFixed64: function (t) {                    this.realloc(8), V(this.buf, t & -1, this.pos), V(this.buf, Math.floor(t * P), this.pos + 4), this.pos += 8                },                writeSFixed64: function (t) {                    this.realloc(8), V(this.buf, t & -1, this.pos), V(this.buf, Math.floor(t * P), this.pos + 4), this.pos += 8                },                writeVarint: function (t) {                    return t = +t || 0, t > 268435455 || t < 0 ? void h(t, this) : (this.realloc(4), this.buf[this.pos++] = 127 & t | (t > 127 ? 128 : 0), void(t <= 127 || (this.buf[this.pos++] = 127 & (t >>>= 7) | (t > 127 ? 128 : 0), t <= 127 || (this.buf[this.pos++] = 127 & (t >>>= 7) | (t > 127 ? 128 : 0), t <= 127 || (this.buf[this.pos++] = t >>> 7 & 127)))))                },                writeSVarint: function (t) {                    this.writeVarint(t < 0 ? 2 * -t - 1 : 2 * t)                },                writeBoolean: function (t) {                    this.writeVarint(Boolean(t))                },                writeString: function (t) {                    t = String(t), this.realloc(4 * t.length), this.pos++;                    var i = this.pos;                    this.pos = S(this.buf, t, this.pos);                    var e = this.pos - i;                    e >= 128 && f(i, e, this), this.pos = i - 1, this.writeVarint(e), this.pos += e                },                writeFloat: function (t) {                    this.realloc(4), B.write(this.buf, t, this.pos, !0, 23, 4), this.pos += 4                },                writeDouble: function (t) {                    this.realloc(8), B.write(this.buf, t, this.pos, !0, 52, 8), this.pos += 8                },                writeBytes: function (t) {                    var i = t.length;                    this.writeVarint(i), this.realloc(i);                    for (var e = 0; e < i; e++) this.buf[this.pos++] = t[e]                },                writeRawMessage: function (t, i) {                    this.pos++;                    var e = this.pos;                    t(i, this);                    var r = this.pos - e;                    r >= 128 && f(e, r, this), this.pos = e - 1, this.writeVarint(r), this.pos += r                },                writeMessage: function (t, i, e) {                    this.writeTag(t, r.Bytes), this.writeRawMessage(i, e)                },                writePackedVarint: function (t, i) {                    this.writeMessage(t, d, i)                },                writePackedSVarint: function (t, i) {                    this.writeMessage(t, p, i)                },                writePackedBoolean: function (t, i) {                    this.writeMessage(t, w, i)                },                writePackedFloat: function (t, i) {                    this.writeMessage(t, c, i)                },                writePackedDouble: function (t, i) {                    this.writeMessage(t, l, i)                },                writePackedFixed32: function (t, i) {                    this.writeMessage(t, F, i)                },                writePackedSFixed32: function (t, i) {                    this.writeMessage(t, b, i)                },                writePackedFixed64: function (t, i) {                    this.writeMessage(t, v, i)                },                writePackedSFixed64: function (t, i) {                    this.writeMessage(t, g, i)                },                writeBytesField: function (t, i) {                    this.writeTag(t, r.Bytes), this.writeBytes(i)                },                writeFixed32Field: function (t, i) {                    this.writeTag(t, r.Fixed32), this.writeFixed32(i)                },                writeSFixed32Field: function (t, i) {                    this.writeTag(t, r.Fixed32), this.writeSFixed32(i)                },                writeFixed64Field: function (t, i) {                    this.writeTag(t, r.Fixed64), this.writeFixed64(i)                },                writeSFixed64Field: function (t, i) {                    this.writeTag(t, r.Fixed64), this.writeSFixed64(i)                },                writeVarintField: function (t, i) {                    this.writeTag(t, r.Varint), this.writeVarint(i)                },                writeSVarintField: function (t, i) {                    this.writeTag(t, r.Varint), this.writeSVarint(i)                },                writeStringField: function (t, i) {                    this.writeTag(t, r.Bytes), this.writeString(i)                },                writeFloatField: function (t, i) {                    this.writeTag(t, r.Fixed32), this.writeFloat(i)                },                writeDoubleField: function (t, i) {                    this.writeTag(t, r.Fixed64), this.writeDouble(i)                },                writeBooleanField: function (t, i) {                    this.writeVarintField(t, Boolean(i))                }            }        }, {            ieee754: 2        }],        2: [function (t, i, e) {            e.read = function (t, i, e, r, s) {                var n, o, h = 8 * s - r - 1,                    a = (1 << h) - 1,                    u = a >> 1,                    f = -7,                    d = e ? s - 1 : 0,                    p = e ? -1 : 1,                    c = t[i + d];                for (d += p, n = c & (1 << -f) - 1, c >>= -f, f += h; f > 0; n = 256 * n + t[i + d], d += p, f -= 8);                for (o = n & (1 << -f) - 1, n >>= -f, f += r; f > 0; o = 256 * o + t[i + d], d += p, f -= 8);                if (0 === n) n = 1 - u;                else {                    if (n === a) return o ? NaN : (c ? -1 : 1) * (1 / 0);                    o += Math.pow(2, r), n -= u                }                return (c ? -1 : 1) * o * Math.pow(2, n - r)            }, e.write = function (t, i, e, r, s, n) {                var o, h, a, u = 8 * n - s - 1,                    f = (1 << u) - 1,                    d = f >> 1,                    p = 23 === s ? Math.pow(2, -24) - Math.pow(2, -77) : 0,                    c = r ? 0 : n - 1,                    l = r ? 1 : -1,                    w = i < 0 || 0 === i && 1 / i < 0 ? 1 : 0;                for (i = Math.abs(i), isNaN(i) || i === 1 / 0 ? (h = isNaN(i) ? 1 : 0, o = f) : (o = Math.floor(Math.log(i) / Math.LN2), i * (a = Math.pow(2, -o)) < 1 && (o--, a *= 2), i += o + d >= 1 ? p / a : p * Math.pow(2, 1 - d), i * a >= 2 && (o++, a /= 2), o + d >= f ? (h = 0, o = f) : o + d >= 1 ? (h = (i * a - 1) * Math.pow(2, s), o += d) : (h = i * Math.pow(2, d - 1) * Math.pow(2, s), o = 0)); s >= 8; t[e + c] = 255 & h, c += l, h /= 256, s -= 8);                for (o = o << s | h, u += s; u > 0; t[e + c] = 255 & o, c += l, o /= 256, u -= 8);                t[e + c - l] |= 128 * w            }        }, {}]    }, {}, [1])(1)});"}}(),function(){var w=function(e,h,i,r,a,o,s,n){this.points=e,this.vecs=[];this.vecs=function(e){for(var i=[e[0]],r=0;r<e.length-1;r++){for(var a=e[r],o=e[r+1],s=Math.sqrt((o.x-a.x)*(o.x-a.x)+(o.y-a.y)*(o.y-a.y)),n=Math.round(s/h),l=1;l<n;l++)per=l/n,x=parseFloat((o.x-a.x)*per)+parseFloat(a.x),y=parseFloat((o.y-a.y)*per)+parseFloat(a.y),t={x:x,y:y},i.push(t);i.push(o)}return i}(this.points),this.lCount=i,this.vertices=[],this.colors=[],this.sizes=[],this.length=this.vecs.length,this.index=Math.floor(Math.random()*this.length);for(var l=NPMap.Utils.MapUtil.gradientColor(a,r,i,!0),p=0,u=this.index;u<this.index+i;u++)u<this.length?this.vertices.push(this.vecs[u].x,this.vecs[u].y,0):this.vertices.push(this.vecs[u%this.length].x,this.vecs[u%this.length].y,0),this.colors.push(l[p].r/256,l[p].g/256,l[p].b/256),this.sizes.push(s+p*(o-s)/i),p++;this.update=function(){this.index+=n,this.vertices=[],this.index>=this.length&&(this.index=0);for(var t=this.index;t<this.index+i;t++)t<this.length?this.vertices.push(this.vecs[t].x,this.vecs[t].y,0):this.vertices.push(this.vecs[t%this.length].x,this.vecs[t%this.length].y,0)}},e=BigScreenMap.MapSpriteLine2D=function(t,e,i){this._map=t,this._spriteLines=[],this._layerID=NPMap.Utils.BaseUtils.uuid(),this._startColor=i&&i.startColor?i.startColor:"#FF0000",this._endColor=i&&i.endColor?i.endColor:"#FFFF00",this._splitLength=i&&i.splitLength?i.splitLength:5,this._startSize=i&&i.startSize?i.startSize:40,this._endSize=i&&i.endSize?i.endSize:2,this._count=i&&i.count?i.count:20,this._minZoom=i&&i.minZoom?i.minZoom:11,this._lineWidth=i&&i.lineWidth?i.lineWidth:2,this._lineColor=i&&i.lineColor?i.lineColor:"#FFFFFF",this._isLineShow=!i||void 0===i.isLineShow||i.isLineShow,this._step=i&&i.step?i.step:3,this.tb=e,this.timer=null;this.geometry=null;var r=NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/spark1.png";this.material=new THREE.ShaderMaterial({uniforms:{color:{value:new THREE.Color(16777215)},fogColor:{value:this.tb.scene.fog.color},fogNear:{value:this.tb.scene.fog.near},fogFar:{value:this.tb.scene.fog.far},texture:{value:(new THREE.TextureLoader).load(r)}},vertexShader:"#include <fog_pars_vertex>\nattribute float size;attribute vec3 customColor;varying vec3 vColor;void main() {vColor = customColor;vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );gl_PointSize = size * ( 300.0 / -mvPosition.z );gl_Position = projectionMatrix * mvPosition;\n\t#include <fog_vertex>\n\t}",fragmentShader:"#include <fog_pars_fragment>\nuniform vec3 color;uniform sampler2D texture;varying vec3 vColor;void main() {gl_FragColor = vec4( color * vColor, 1.0 );gl_FragColor = gl_FragColor * texture2D( texture, gl_PointCoord );\n\t#include <fog_fragment>\n\t}",blending:THREE.AdditiveBlending,depthTest:!0,fog:!0,transparent:!0,alphaTest:.5,side:THREE.DoubleSide})};e.prototype.addLines=function(t){t&&0==t.length||(this.timer&&clearTimeout(this.timer),this.geometry=new THREE.BufferGeometry,this._spriteLines=[],this._init(t),this._update())},e.prototype._init=function(t){for(var e=[],i=[],r=[],a=t[0].getPath()[0],o=a=NPMap.T.setPoint(this._map,a),s=this.tb.utils.projectToWorld([a.lon,a.lat,0]),n=new THREE.LineMaterial({color:this._lineColor,linewidth:5e-4*this._lineWidth,dashed:!1,opacity:1,depthTest:!1,wireframe:!1}),l=0;l<t.length;l++){for(var h=t[l].getPath(),p=[],u=[],c=0;c<h.length;c++){var d=NPMap.T.setPoint(this._map,h[c]),f=this.tb.utils.projectToWorld([d.lon,d.lat,0]);p.push({x:f.x-s.x,y:f.y-s.y}),u.push(f.x-s.x,f.y-s.y,0)}var m=new w(p,this._splitLength,this._count,this._startColor,this._endColor,this._startSize,this._endSize,this._step);for(c=0;c<m.vertices.length;c++)e.push(m.vertices[c]);for(c=0;c<m.colors.length;c++)i.push(m.colors[c]);for(c=0;c<m.sizes.length;c++)r.push(m.sizes[c]);if(this._isLineShow){var v=new THREE.LineGeometry;v.setPositions(u);var g=new THREE.Line2(v,n);t[l].isBloom&&g.layers.set(1),m.line=g}this._spriteLines.push(m)}var y=new Float32Array(e.length);y.set(e);var x=new Float32Array(i.length);x.set(i);var _=new Float32Array(r.length);_.set(r),this.geometry.setAttribute("position",new THREE.BufferAttribute(y,3)),this.geometry.setAttribute("customColor",new THREE.BufferAttribute(x,3)),this.geometry.setAttribute("size",new THREE.BufferAttribute(_,1));var b=new THREE.Points(this.geometry,this.material);for(l=0;l<this._spriteLines.length;l++)this.tb.addAtCoordinate(this._spriteLines[l].line,[o.lon,o.lat,2]);this.tb.addAtCoordinate(b,[o.lon,o.lat])},e.prototype._update=function(){if(this._map.getZoom()<this._minZoom)this.material.visible=!1,this.geometry.setAttribute("position",new THREE.Float32BufferAttribute([],3));else{for(var t=[],e=0;e<this._spriteLines.length;e++){this._spriteLines[e].update();for(var i=0;i<this._spriteLines[e].vertices.length;i++)t.push(this._spriteLines[e].vertices[i])}this.geometry.setAttribute("position",new THREE.Float32BufferAttribute(t,3)),this.material.visible=!0,this._map._obj.repaint=!0}this.timer=setTimeout(this._update.bind(this),40)},e.prototype.destory=function(){if(this.timer&&clearTimeout(this.timer),this._map&&this._map._obj.getLayer(this._layerID)){this._map._obj.removeLayer(this._layerID);for(var t=0;t<this._spriteLines.length;t++)this.tb.remove(this._spriteLines[t].points),this._spriteLines[t].geometry=null,this._spriteLines[t].points=null;this._spriteLines.length=0,this.tb=null}}}(),function(){var f=function(t,e,i,r,a,o,s,n,l){this.point=t,this.lCount=e,this.vertices=[],this.colors=[],this.sizes=[],this.length=Math.ceil(n/s),this.index=Math.floor(Math.random()*this.length);for(var h=NPMap.Utils.MapUtil.gradientColor(r,i,e,!0),p=0,u=this.index;u<this.index+e;u++)u<this.length?this.vertices.push(this.point.x,this.point.y,u*s):this.vertices.push(this.point.x,this.point.y,(u-this.length)*s),this.colors.push(h[p].r/256,h[p].g/256,h[p].b/256),this.sizes.push(o+p*(a-o)/e),p++;this.update=function(){this.index+=l,this.vertices=[],this.index>=this.length&&(this.index=0);for(var t=this.index;t<this.index+e;t++)t<this.length?this.vertices.push(this.point.x,this.point.y,t*s):this.vertices.push(this.point.x,this.point.y,(t-this.length)*s)}},t=BigScreenMap.MapSpringLine=function(t,e,i){this._map=t,this._springLines=[],this._layerID=NPMap.Utils.BaseUtils.uuid(),this._startColor=i&&i.startColor?i.startColor:"#FF0000",this._endColor=i&&i.endColor?i.endColor:"#FFFF00",this._numberColor=i&&i.numberColor?i.numberColor:"#FFFFFF",this._splitLength=i&&i.splitLength?i.splitLength:.4,this._startSize=i&&i.startSize?i.startSize:40,this._endSize=i&&i.endSize?i.endSize:2,this._speed=i&&i.speed?i.speed:.5,this._count=i&&i.count?i.count:20,this._lineHeight=i&&i.lineHeight?i.lineHeight:100,this._minZoom=i&&i.minZoom?i.minZoom:11,this._step=i&&i.step?i.step:3,this.tb=e,this.timer=null,this.markerLines=[],this.geometry=null,this._moveCount=Math.floor(this._lineHeight/this._speed);var r=NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/spark1.png";this.material=new THREE.ShaderMaterial({uniforms:{color:{value:new THREE.Color(16777215)},fogColor:{value:this.tb.scene.fog.color},fogNear:{value:this.tb.scene.fog.near},fogFar:{value:this.tb.scene.fog.far},texture:{value:(new THREE.TextureLoader).load(r)}},vertexShader:"#include <fog_pars_vertex>\nattribute float size;attribute vec3 customColor;varying vec3 vColor;void main() {vColor = customColor;vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );gl_PointSize = size * ( 300.0 / -mvPosition.z );gl_Position = projectionMatrix * mvPosition;\n\t#include <fog_vertex>\n\t}",fragmentShader:"#include <fog_pars_fragment>\nuniform vec3 color;uniform sampler2D texture;varying vec3 vColor;void main() {gl_FragColor = vec4( color * vColor, 1.0 );gl_FragColor = gl_FragColor * texture2D( texture, gl_PointCoord );\n\t#include <fog_fragment>\n\t}",blending:THREE.AdditiveBlending,depthTest:!0,fog:!0,transparent:!0}),this._update()};t.prototype.addPoints=function(t){t&&0==t.length||(this.geometry=new THREE.BufferGeometry,this._springLines=[],this._init(t))},t.prototype._initMarkerNumberMaterial=function(t){var e=(new THREE.TextureLoader).load(NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/fly0101_03.png");e.wrapS=e.wrapT=THREE.RepeatWrapping,e.repeat.set(1,1);var i=(new THREE.TextureLoader).load(NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/alpha_gradient.png");i.repeat.set(1,1);var r=new THREE.MeshBasicMaterial({color:t,side:THREE.DoubleSide,transparent:!0,depthWrite:!1,blending:THREE.NormalBlending});this.markerNumberMaterial={material:r};var a=this;r.onBeforeCompile=function(t){t.uniforms.iTime={value:0},t.uniforms.numTex={value:e},t.uniforms.alphaTex={value:i},t.uniforms.offset={value:0},t.vertexShader=t.vertexShader.replace("#include <uv2_pars_vertex>","#include <uv2_pars_vertex>\nuniform float iTime;varying vec2 nvUv;varying vec2 vtUv;mat3 setUvTransform(float tx, float ty, float sx, float sy, float rotation, float cx, float cy) {float c = cos( rotation );float s = sin( rotation );return mat3(sx * c, sx * s, - sx * ( c * cx + s * cy ) + cx + tx,- sy * s, sy * c, - sy * ( - s * cx + c * cy ) + cy + ty,tx, ty, 1);}"),t.vertexShader=t.vertexShader.replace("#include <uv2_vertex>","\n                #include <uv2_vertex>\n\n                mat3 uvMat3 = setUvTransform(0.0, iTime, 1.0, 5.0, 0.0, 0.0, 0.0);\n                vec2 tUv = ( uvMat3 * vec3( uv, 1. ) ).xy;\n\n                nvUv = uv;\n                vtUv = tUv;\n                "),t.fragmentShader=t.fragmentShader.replace("uniform float opacity;","uniform float opacity;uniform sampler2D numTex;uniform float offset;uniform sampler2D alphaTex;varying vec2 nvUv;varying vec2 vtUv;"),t.fragmentShader=t.fragmentShader.replace("gl_FragColor = vec4( outgoingLight, diffuseColor.a );"," vec4 resultColor = vec4(outgoingLight, opacity);vec2 vtUv2 = vec2(vtUv.x,vtUv.y-offset);vec4 nTexture = texture2D(numTex, vtUv2);resultColor *= nTexture;resultColor.a *= texture2D(alphaTex, nvUv).g;gl_FragColor = vec4( resultColor );"),a.markerNumberMaterial.sharder=t}},t.prototype.addMarkerNumberLine=function(t){this.markerNumberMaterial||this._initMarkerNumberMaterial(this._numberColor);var e=t[0],i=new THREE.Object3D,r=this;e=NPMap.T.setPoint(this._map,e);for(var a=0;a<t.length;a++){var o=NPMap.T.setPoint(this._map,t[a]),s=this.tb.utils.projectToWorld([o.lon,o.lat,150+t[a].height]),n=new THREE.PlaneBufferGeometry(6/25.4,300/25.4,1,1),l=new THREE.Mesh(n,this.markerNumberMaterial.material);l.rotateX(Math.PI/2),l.rotateY(Math.PI),l.onBeforeRender=function(t){this.rotation.y=r._map.getBearing()/180*Math.PI},l.position.set(s.x,s.y,s.z),i.add(l)}this.tb.addAtCoordinate(i,[0,0])},t.prototype._init=function(t){for(var e=[],i=[],r=[],a=this.tb.utils.projectToWorld([t[0].lon,t[0].lat,0]),o=t[0],s=0;s<t.length;s++){for(var n=this.tb.utils.projectToWorld([t[s].lon,t[s].lat,0]),l=new f({x:n.x-a.x,y:n.y-a.y},this._count,this._startColor,this._endColor,this._startSize,this._endSize,this._splitLength,this._lineHeight,this._step),h=0;h<l.vertices.length;h++)e.push(l.vertices[h]);for(h=0;h<l.colors.length;h++)i.push(l.colors[h]);for(h=0;h<l.sizes.length;h++)r.push(l.sizes[h]);this._springLines.push(l)}var p=new Float32Array(e.length);p.set(e);var u=new Float32Array(i.length);u.set(i);var c=new Float32Array(r.length);c.set(r),this.geometry.setAttribute("position",new THREE.BufferAttribute(p,3)),this.geometry.setAttribute("customColor",new THREE.BufferAttribute(u,3)),this.geometry.setAttribute("size",new THREE.BufferAttribute(c,1));var d=new THREE.Points(this.geometry,this.material);this.tb.addAtCoordinate(d,[o.lon,o.lat])},t.prototype._update=function(){if(this.geometry)if(this._map.getZoom()<this._minZoom)this.material.visible=!1,this.geometry.setAttribute("position",new THREE.Float32BufferAttribute([],3));else{for(var t=[],e=0;e<this._springLines.length;e++){this._springLines[e].update();for(var i=0;i<this._springLines[e].vertices.length;i++)t.push(this._springLines[e].vertices[i])}this.geometry.setAttribute("position",new THREE.Float32BufferAttribute(t,3)),this.material.visible=!0,this._map._obj.repaint=!0}if(this.markerNumberMaterial&&this.markerNumberMaterial.sharder){var r=this.markerNumberMaterial.sharder.uniforms.offset.value;1<(r+=.02)&&(r=0),this.markerNumberMaterial.sharder.uniforms.offset.value=r,this.markerNumberMaterial.sharder.needsUpdate=!0}this.timer=setTimeout(this._update.bind(this),40)},t.prototype.destory=function(){if(this.timer&&clearTimeout(this.timer),this._map&&this._map._obj.getLayer(this._layerID)){this._map._obj.removeLayer(this._layerID);for(var t=0;t<this._springLines.length;t++)this.tb.remove(this._springLines[t].points),this._springLines[t].geometry=null,this._springLines[t].points=null;this._springLines.length=0,this.tb=null}}}(),function(){var t=BigScreenMap.WaterEffectManager=function(t,e){initWater(),this._map=t,this.tb=e;var i=function(){t&&t._obj&&(t._obj.repaint=!0,requestAnimationFrame(i))};i()};t.prototype.addWaterRegion=function(t,e){t.type="WaterEffect";var i=t.getPath(),r=i[0],a=!1;r instanceof Array&&(r=i[0][0],a=!0),r=NPMap.T.setPoint(this._map,r);var o=this.tb.utils.projectToWorld([r.lon,r.lat,0]);a||(i=[i]);for(var s=new THREE.Shape,n=0;n<i.length;n++){for(var l=[],h=0;h<i[n].length;h++){var p=i[n][h];p=NPMap.T.setPoint(this._map,p);var u=this.tb.utils.projectToWorld([p.lon,p.lat,0]),c=[u.x-o.x,u.y-o.y,0];l.push([c[0],c[1],0])}var d=[];if(0==n)for(h=0;h<l.length;h++)0==h?s.moveTo(l[h][0],l[h][1]):s.lineTo(l[h][0],l[h][1]),d.push(l[h][0],l[h][1],0);else{var f=new THREE.Path;for(h=0;h<l.length;h++)0==h?f.moveTo(l[h][0],l[h][1]):f.lineTo(l[h][0],l[h][1]),d.push(l[h][0],l[h][1],0);s.holes.push(f)}}var m=new THREE.ShapeBufferGeometry(s),v="#ffffff",g=4,y=0,x=0,_=new THREE.Water(m,{color:v,scale:g,flowDirection:new THREE.Vector2(y,x),textureWidth:1024,textureHeight:1024});(t.waterEffect=_).parentObj=t,this.tb.addAtCoordinate(_,[r.lon,r.lat,e])},t.prototype.removeWaterEffect=function(t){t&&t.waterEffect&&(this.tb.remove(t.waterEffect),t.waterEffect=null)}}(),function(){var t=BigScreenMap.WaterFountain=function(t){this.type="WaterFountain",this.loop=!0,this.particleCount=t&&t.particleCount?t.particleCount:500,this.dX=t&&t.dX?t.dX:4,this.dY=t&&t.dY?t.dY:4,this.dZ=t&&t.dZ?t.dZ:4,this.dX1=t&&t.dX1?t.dX1:-2,this.dY1=t&&t.dY1?t.dY1:20,this.dZ1=t&&t.dZ1?t.dZ1:-2,this.material=new THREE.PointCloudMaterial({size:t&&t.size?t.size:10,blending:THREE.AdditiveBlending,transparent:!0,depthTest:!0,side:THREE.DoubleSide,color:t&&t.color?t.color:15856629}),this.spawnBehavior=function(){var t,e,i,r=new THREE.Vector3(0,0,0);return e=Math.random()*this.dY1+0,t=Math.random()*this.dX+this.dX1,i=Math.random()*this.dZ+this.dZ1,r.velocity=new THREE.Vector3(t,e,i),r},this.frameBehavior=function(t){var e,i,r;(t.x+=t.velocity.x,t.y+=t.velocity.y,t.z+=t.velocity.z,t.velocity.y-=.5,t.y<0)&&(t.x=t.y=t.z=0,i=Math.random()*this.dY+this.dY1,e=Math.random()*this.dX+this.dX1,r=Math.random()*this.dZ+this.dZ1,t.velocity=new THREE.Vector3(e,i,r))},this._obj=this.initWaterFountain();var e=this._obj.parentObj=this;this.updateParticles=function(){if(e.frameBehavior&&"function"==typeof e.frameBehavior){for(var t=0;t<e._obj.geometry.vertices.length;t++)e.frameBehavior(e._obj.geometry.vertices[t]);e._obj.geometry.verticesNeedUpdate=!0,e._obj.geometry.colorsNeedUpdate=!0}},this.render=function(){e.loop&&(e.updateParticles(),requestAnimationFrame(e.render))},requestAnimationFrame(e.render)};t.prototype.scale=function(t,e,i){this._obj.scale.set(t,e,i)},t.prototype.initWaterFountain=function(){var t=this.createPoints(),e=new THREE.PointCloud(t,this.material);return e.rotateX(Math.PI/2),e},t.prototype.createPoints=function(){for(var t=new THREE.Geometry,e=0;e<this.particleCount;e++)t.vertices.push(this.spawnBehavior());return t},t.prototype.setParams=function(t){t&&t.dX&&(this.dX=t.dX),t&&t.dY&&(this.dY=t.dY),t&&t.dZ&&(this.dZ=t.dZ),t&&t.dX1&&(this.dX1=t.dX1),t&&t.dY1&&(this.dY1=t.dY1),t&&t.dZ1&&(this.dZ1=t.dZ1),t&&t.size&&(this.material.size=t.size),t&&t.color&&this.material.color.set(t.color)}}(),function(){var t=BigScreenMap.WaterFountainManager=function(t,e){this._map=t,this.tb=e;var i=function(){t&&t._obj&&(t._obj.repaint=!0,requestAnimationFrame(i))};i()};t.prototype.addWaterFountain=function(t,e,i){var r=NPMap.T.setPoint(this._map,e),a=i&&i.height?i.height:0;this.tb.addAtCoordinate(t._obj,[r.lon,r.lat,a]);var o=this.tb.utils.projectedUnitsPerMeter(r.lat);t._obj.scale.set(o*o,o*o,o*o)},t.prototype.setWaterFountainCoord=function(t,e,i){var r=NPMap.T.setPoint(this._map,e),a=i&&i.height?i.height:0;this.tb.moveToCoordinate(t._obj,[r.lon,r.lat,a])},t.prototype.removeWaterFountain=function(t){t.loop=!1,this.tb.remove(t._obj)}}(),(BigScreenMap.Fire=function(i){this.type="Fire",this.loop=!0;var _=this,r=["#include <fog_pars_vertex>","attribute vec4 orientation;","attribute vec3 offset;","attribute vec2 scale;","attribute float life;","attribute float random;","varying vec2 vUv;","varying float vRandom;","varying float vAlpha;","float range(float oldValue, float oldMin, float oldMax, float newMin, float newMax) {","    float oldRange = oldMax - oldMin;","    float newRange = newMax - newMin;","    return (((oldValue - oldMin) * newRange) / oldRange) + newMin;","}","float pcurve(float x, float a, float b) {","    float k = pow(a + b, a + b) / (pow(a, a) * pow(b, b));","    return k * pow(x, a) * pow(1.0 - x, b);","}","void main() {","    vUv = uv;","    vRandom = random;","    vAlpha = pcurve(life, 1.0, 2.0);","    vec3 pos = position;","    pos.xy *= scale * vec2(range(pow(life, 1.5), 0.0, 1.0, 1.0, 0.6), range(pow(life, 1.5), 0.0, 1.0, 0.6, 1.2));","    vec4 or = orientation;","    vec3 vcV = cross(or.xyz, pos);","    pos = vcV * (2.0 * or.w) + (cross(or.xyz, vcV) * 2.0 + pos);","    vec4 mvPosition = modelViewMatrix * vec4( pos, 1.0 );","    gl_Position = projectionMatrix * mvPosition;","    #include <fog_vertex>","}"].join("\n"),a=["#include <fog_pars_fragment>","uniform sampler2D uMap;","uniform vec3 uColor1;","uniform vec3 uColor2;","uniform float uTime;","varying vec2 vUv;","varying float vAlpha;","varying float vRandom;","void main() {","vec2 uv = vUv;","float spriteLength = 10.0;","uv.x /= spriteLength;","float spriteIndex = mod(uTime * 0.1 + vRandom * 2.0, 1.0);","uv.x += floor(spriteIndex * spriteLength) / spriteLength;","vec4 map = texture2D(uMap, uv);","gl_FragColor.rgb = mix(uColor2, uColor1, map.r);","gl_FragColor.a = vAlpha * map.a;","#include <fog_fragment>","}"].join("\n");function b(t,e,i){var r=Math.pow(10,i);return Math.round((t+Math.random()*(e-t))*r)/r}this._obj=function(){var n,l,t,h,e,p=30,u=new THREE.Vector3(1,0,0),c=new THREE.Vector3(0,1,0),d=new THREE.Vector3(0,0,1),f=new THREE.Vector3,m=new THREE.Vector3,v=new THREE.Vector3,g=new THREE.Quaternion,y=new THREE.Quaternion;return function(){(n=new THREE.InstancedBufferGeometry).maxInstancedCount=p;var t=new THREE.PlaneBufferGeometry(1,1);t.translate(0,.4,0);var e=t.attributes;n.addAttribute("position",new THREE.BufferAttribute(new Float32Array(e.position.array),3)),n.addAttribute("uv",new THREE.BufferAttribute(new Float32Array(e.uv.array),2)),n.addAttribute("normal",new THREE.BufferAttribute(new Float32Array(e.normal.array),3)),n.setIndex(new THREE.BufferAttribute(new Uint16Array(t.index.array),1)),t.dispose()}(),function(){for(var t=new THREE.InstancedBufferAttribute(new Float32Array(4*p),4),e=new THREE.InstancedBufferAttribute(new Float32Array(p),1),i=new THREE.InstancedBufferAttribute(new Float32Array(2*p),2),r=new THREE.InstancedBufferAttribute(new Float32Array(p),1),a=0;a<p;a++)t.setXYZW(a,0,0,0,1),r.setX(a,a/p+1);n.addAttribute("orientation",t),n.addAttribute("scale",i),n.addAttribute("life",r),n.addAttribute("random",e)}(),e={uMap:{type:"t",value:null},uColor1:{type:"c",value:new THREE.Color(9836544)},uColor2:{type:"c",value:new THREE.Color(11909307)},uTime:{type:"f",value:0},fogColor:{value:i?i.scene.fog.color:16777215},fogNear:{value:i?i.scene.fog.near:500},fogFar:{value:i?i.scene.fog.far:2e3}},l=new THREE.ShaderMaterial({uniforms:e,vertexShader:r,fragmentShader:a,blending:THREE.AdditiveBlending,transparent:!0,depthWrite:!1,fog:!0,side:THREE.DoubleSide}),(new THREE.TextureLoader).load(NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/flame.png",function(t){l.uniforms.uMap.value=t}),h=new THREE.Group,(t=new THREE.Mesh(n,l)).renderOrder=100,t.frustumCulled=!1,t.rotateX(Math.PI/2),h.add(t),_fire=h,requestAnimationFrame(x),t;function x(t){if(_.loop){requestAnimationFrame(x),l.uniforms.uTime.value=.001*t;for(var e=n.attributes.life,i=n.attributes.orientation,r=n.attributes.scale,a=n.attributes.random,o=0;o<p;o++){var s=e.array[o];1<(s+=.02)&&(s-=1,g.setFromAxisAngle(c,b(0,3.14,3)),y.setFromAxisAngle(u,.1*b(-1,1,2)),g.multiply(y),y.setFromAxisAngle(d,.3*b(-1,1,2)),g.multiply(y),i.setXYZW(o,g.x,g.y,g.z,g.w),r.setXY(o,b(.8,1.2,3),b(.8,1.2,3)),a.setX(o,b(0,1,3))),e.setX(o,s)}e.needsUpdate=!0,i.needsUpdate=!0,r.needsUpdate=!0,a.needsUpdate=!0,f.copy(h.position),f.y+=.4,m.lerp(f,.1),v.copy(m),v.sub(h.position),h.quaternion.setFromUnitVectors(c,v.normalize())}}}(),this._obj.parentObj=this}).prototype.scale=function(t,e,i){this._obj.scale.set(t,e,i)},function(){var t=BigScreenMap.FireManager=function(t,e){this._map=t,this.tb=e;var i=function(){t&&t._obj&&(t._obj.repaint=!0,requestAnimationFrame(i))};i()};t.prototype.addFire=function(t,e,i){t.position=e;var r=NPMap.T.setPoint(this._map,e),a=i&&i.height?i.height:0;t.height=a,this.tb.addAtCoordinate(t._obj,[r.lon,r.lat,a])},t.prototype.setFireCoord=function(t,e,i){t.position=e;var r=NPMap.T.setPoint(this._map,e),a=i&&i.height?i.height:0;t.height=a,this.tb.moveToCoordinate(t._obj,[r.lon,r.lat,a])},t.prototype.removeFire=function(t){t.loop=!1,this.tb.remove(t._obj)}}(),function(){var t=BigScreenMap.ExplosionCircle=function(t,e){this._map=t,this.tb=e,this.timer=null,this.lightmanager=new BigScreenMap.LightManager(this._map,this.tb),this.lights=[],this.loop=!0,this._update()};t.prototype.addCircle=function(t){if(t.center){var e=t.center,i=t&&t.radius?t.radius:200,r=t&&t.height?t.height:300,a=t&&t.name?t.name:"PointLight2",o=t&&t.color?t.color:16777215,s=t&&t.intensity?t.intensity:1500,n=t&&t.expandMaxR?t.expandMaxR:15e3,l=t&&t.intervalLength?t.intervalLength:100,h=this.lightmanager.addLight({lightType:"PointLight2",name:a,color:o,intensity:s,decay:.9,distance:0,position:e,height:r}).light;this.lights.push({startRadius:i,endRadius:n,intervalLength:l,light:h})}},t.prototype._update=function(){if(this.loop){for(var t=0;t<this.lights.length;t++){var e=this.lights[t].light;if(e.realDistance||(e.realDistance=e.distance),e.realDistance+=this.lights[t].intervalLength,e.realDistance>this.lights[t].endRadius&&(e.realDistance=this.lights[t].startRadius),this.tb){var i=this.tb.cameraSync.state.projectWorldSize*map._obj.transform.scale*this.tb.cameraSync.state.worldSizeRatio;e.distance=e.realDistance*i}}this._map._obj.repaint=!0,requestAnimationFrame(this._update.bind(this))}},t.prototype.removeCircle=function(t){for(var e=0;e<this.lights.length;e++)if(this.lights[e].light.name===t)return this.lightmanager.removeLight(t),void this.lights.splice(e,1)},t.prototype.destory=function(){this.timer&&clearTimeout(this.timer),this.loop=!1,this.timer=null,delete this.timer;for(var t=0;t<this.lights.length;t++)this.tb.remove(this.lights[t].light);this.lights.length=0,this.lights=null,this.lightmanager=null,delete this.lightmanager}}(),(BigScreenMap.MapTerrain=function(t,e,i){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=i&&i.minZoom?i.minZoom:11,this.tb=e,this.materials=new THREE.MeshBasicMaterial({vertexColors:THREE.VertexColors,transparent:!0,wireframe:!1,opacity:1})}).prototype.addDem=function(t,e,i,r,a){var o=NPMap.T.setExtent(this._map,e),s={lon:(o.left+o.right)/2,lat:(o.bottom+o.top)/2},n=this.tb.utils.projectToWorld([o.left,o.bottom,0]),l=this.tb.utils.projectToWorld([o.right,o.top,0]),h=new THREE.PlaneBufferGeometry(Math.abs(n.x-l.x),Math.abs(n.y-l.y),i-1,r-1);h.rotateZ(Math.PI);var p=a&&a.baseColor?a.baseColor:"#171f01",u=a&&a.topColor?a.topColor:"#ffffff",c=a&&a.baseHeight?a.baseHeight:500,d=a&&a.topHeight?a.topHeight:3800,f=h.attributes.position.array,m=this.tb.utils.projectedUnitsPerMeter(s.lat),v=[];u=NPMap.Utils.MapUtil.colorRgb(u),p=NPMap.Utils.MapUtil.colorRgb(p);for(var g=0,y=0,x=f.length;g<x;g++,y+=3){var _=0<t[g]-c?t[g]-c:0,b=(p[0]+(u[0]-p[0])/(d-c)*_)/255,w=(p[1]+(u[1]-p[1])/(d-c)*_)/255,M=(p[2]+(u[2]-p[2])/(d-c)*_)/255;0==_?v.push(0,0,0):v.push(b,w,M),_*=m,f[y+2]=1.3*_}var E=new Float32Array(v.length);E.set(v),h.setAttribute("color",new THREE.BufferAttribute(E,3));var T=new THREE.Mesh(h,this.materials);this.tb.addAtCoordinate(T,[s.lon,s.lat])},function(){var t=BigScreenMap.Wall=function(t,e,i){this.type="Wall",this.width_height=[1,1],this.polyline=t,this.height=e||3,i||(i={}),i&&i.width_height&&(this.width_height=i.width_height),this.imageURL=i.imageURL;var r=t.getPath();this.position=r[0];var a=-1<this.imageURL.indexOf("/")?this.imageURL:MapPlatForm.dataServiceURL+"resources/img/texture/wall/"+this.imageURL;this.texture=(new THREE.TextureLoader).load(a),this.texture.wrapS=this.texture.wrapT=THREE.RepeatWrapping,this.texture.rotation=-Math.PI/2,this.texture.repeat.set(1,1),this.buildSideMaterial=new THREE.MeshStandardMaterial({map:this.texture,transparent:!0,alphaTest:.3,side:THREE.DoubleSide})};t.prototype.init=function(t,e){this.map=t,this.threebox=e,this.buildBufferGeometry=new THREE.BufferGeometry,this.verticesData=this._setPolyLine(t,e,this.polyline,this.height);var i=new Float32Array(this.verticesData.vertices.length);i.set(this.verticesData.vertices),this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(i,3));var r=new Float32Array(this.verticesData.normals.length);r.set(this.verticesData.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(r,3));var a=new Float32Array(this.verticesData.uvs.length);a.set(this.verticesData.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(a,2)),this.mesh=new THREE.Mesh(this.buildBufferGeometry,this.buildSideMaterial)},t.prototype.update=function(t,e,i){if(this.height=e||this.height,this.polyline=t||this.polyline,i&&i.width_height&&(this.width_height=i.width_height),this.map&&this.threebox){this.verticesData=this._setPolyLine(this.map,this.threebox,this.polyline,this.height);var r=new Float32Array(this.verticesData.vertices.length);r.set(this.verticesData.vertices),this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(r,3));var a=new Float32Array(this.verticesData.normals.length);a.set(this.verticesData.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(a,3));var o=new Float32Array(this.verticesData.uvs.length);o.set(this.verticesData.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(o,2)),this.map.repaint()}if(i&&i.imageURL){this.imageURL=i.imageURL;var s=-1<this.imageURL.indexOf("/")?this.imageURL:MapPlatForm.dataServiceURL+"resources/img/texture/wall/"+this.imageURL;texture=(new THREE.TextureLoader).load(s),texture.wrapS=this.texture.wrapT=THREE.RepeatWrapping,texture.rotation=-Math.PI/2,texture.repeat.set(1,1),this.buildSideMaterial.map=texture,this.buildSideMaterial.map.needsUpdate=!0,this.map.repaint()}},t.prototype._setPolyLine=function(t,e,i,r){var a=i.getPath(),o=a[0];o=NPMap.T.setPoint(t,o);for(var s=e.utils.projectToWorld([o.lon,o.lat,r||this.height]),n=[],l=0;l<a.length;l++){var h=NPMap.T.setPoint(t,new NPMap.Geometry.Point(a[l].lon,a[l].lat)),p=e.utils.projectToWorld([h.lon,h.lat,0]),u=[p.x-s.x,p.y-s.y,0];n.push(u)}return this._initVecs(n,s.z)},t.prototype._initVecs=function(t,e){for(var i,r,a={vertices:[],normals:[],uvs:[]},o=0;o<t.length-1;o++){var s=o,n=s+1,l=Math.sqrt((t[n][0]-t[s][0])*(t[n][0]-t[s][0])+(t[n][1]-t[s][1])*(t[n][1]-t[s][1]))/e*this.width_height[1]/this.width_height[0],h=(i=t[n][0]-t[o][0],{x:(r=t[n][1]-t[o][1])/Math.sqrt(i*i+r*r),y:-i/Math.sqrt(i*i+r*r),z:0});a.vertices.push(t[o][0],t[o][1],t[o][2]+e),a.vertices.push(t[n][0],t[n][1],t[n][2]),a.vertices.push(t[o][0],t[o][1],t[o][2]),a.normals.push(h.x,h.y,h.z),a.normals.push(h.x,h.y,h.z),a.normals.push(h.x,h.y,h.z),a.uvs.push(this.width_height[1],l),a.uvs.push(0,0),a.uvs.push(0,l),a.vertices.push(t[o][0],t[o][1],t[o][2]+e),a.vertices.push(t[n][0],t[n][1],t[n][2]+e),a.vertices.push(t[n][0],t[n][1],t[n][2]),a.normals.push(h.x,h.y,h.z),a.normals.push(h.x,h.y,h.z),a.normals.push(h.x,h.y,h.z),a.uvs.push(this.width_height[1],l),a.uvs.push(this.width_height[1],0),a.uvs.push(0,0)}return a}}(),function(){var t=BigScreenMap.WallManager=function(t,e){this._map=t,this.tb=e};t.prototype.addWall=function(t){t.init(this._map,this.tb);var e=NPMap.T.setPoint(this._map,t.position);t.mesh.parentObj=t,this.tb.addAtCoordinate(t.mesh,[e.lon,e.lat,0])},t.prototype.removeWall=function(t){t.mesh&&(this.tb.remove(t.mesh),t.buildBufferGeometry=null,t.mesh=null)}}(),function(){var t=BigScreenMap.Ground=function(t,e,i,r){this.type="Ground",this.landUV=[1,1],this.grassUV=[1,1],this.polygon=t,this.grassPolygons=e,this.height=.2,this.hasHoles=r;var a=t.getPath();if(this.position=a[0],i||(i={}),i.land||(i.land={}),i.grass||(i.grass={}),i.land.uv&&(this.landUV=i.land.uv),i.grass.uv&&(this.grassUV=i.grass.uv),i.grass.aloneUV&&(this.aloneUV=!0),this.landImageURL=i.land.imageURL,this.landImageURL){this.landImageRoatation=i.land.rotation?i.land.rotation:0;var o=-1<this.landImageURL.indexOf("/")?this.landImageURL:MapPlatForm.dataServiceURL+"resources/img/texture/land/"+this.landImageURL;this.landTexture=(new THREE.TextureLoader).load(o),this.landTexture.wrapS=this.landTexture.wrapT=THREE.RepeatWrapping,this.landTexture.repeat.set(this.landUV[0],this.landUV[1]),this.landTexture.rotation=this.landImageRoatation/180*Math.PI,this.landMaterial=new THREE.MeshStandardMaterial({map:this.landTexture,transparent:!0,side:THREE.DoubleSide})}else this.landMaterial=new THREE.MeshStandardMaterial({color:i.land.color?i.land.color:2236962,transparent:!0,side:THREE.DoubleSide});if(!this.hasHoles&&e.length){this.grassImageURL=i.grass.imageURL;o=-1<this.grassImageURL.indexOf("/")?this.grassImageURL:MapPlatForm.dataServiceURL+"resources/img/texture/grass/"+this.grassImageURL;this.grassTexture=(new THREE.TextureLoader).load(o),this.grassTexture.wrapS=this.grassTexture.wrapT=THREE.RepeatWrapping,this.grassTexture.repeat.set(this.grassUV[0],this.grassUV[1]),this.grassTexture.rotation=0}this.grassMaterial=new THREE.MeshStandardMaterial({map:this.grassTexture,transparent:!0,side:THREE.DoubleSide}),this.hasHoles||(this.roadsideMaterial=new THREE.MeshStandardMaterial({color:16776960,transparent:!0,side:THREE.DoubleSide}))};t.prototype.init=function(t,e){this.map=t,this.threebox=e,this.groundBufferGeometry=new THREE.BufferGeometry,this._setPolygon(t,e,this.polygon,this.grassPolygons,this.height),this.mesh=new THREE.Mesh(this.groundBufferGeometry,[this.landMaterial,this.grassMaterial,this.roadsideMaterial])},t.prototype.update=function(t,e,i){if(this.polygon=t||this.polygon,this.grassPolygons=e||this.grassPolygons,(t||e.length)&&(this._setPolygon(this.map,this.threebox,this.polygon,this.grassPolygons,this.height),this.mesh.geometry=this.groundBufferGeometry),i&&i.land){if(i.land.imageURL){this.landImageURL=i.land.imageURL;var r=-1<this.landImageURL.indexOf("/")?this.landImageURL:MapPlatForm.dataServiceURL+"resources/img/texture/land/"+this.landImageURL;this.landImageRoatation=void 0===i.land.rotation?i.land.rotation:this.landImageRoatation,(a=(new THREE.TextureLoader).load(r)).wrapS=a.wrapT=THREE.RepeatWrapping,a.repeat.set(this.landUV[0],this.landUV[1]),a.rotation=this.landImageRoatation,this.landMaterial.map=a}i.land.uv&&(this.landUV=i.land.uv,this.landMaterial.map.repeat.set(this.landUV[0],this.landUV[1])),this.landMaterial.map.needsUpdate=!0}if(i&&i.grass){if(i.grass.imageURL){this.grassImageURL=i.grass.imageURL;var a;r=-1<this.grassImageURL.indexOf("/")?this.grassImageURL:MapPlatForm.dataServiceURL+"resources/img/texture/grass/"+this.grassImageURL;(a=(new THREE.TextureLoader).load(r)).wrapS=a.wrapT=THREE.RepeatWrapping,a.repeat.set(this.grassUV[0],this.grassUV[1]),this.grassMaterial.map=a}i.grass.uv&&(this.grassUV=i.grass.uv,this.grassMaterial.map.repeat.set(this.grassUV[0],this.grassUV[1])),this.grassMaterial.map.needsUpdate=!0}this.map.repaint()},t.prototype._setPolygon=function(t,e,i,r,a){var o=i.getPath(),s=o[0];s=NPMap.T.setPoint(t,s);var n=e.utils.projectToWorld([s.lon,s.lat,a||this.height]),l=i.getExtent();l=NPMap.T.setExtent(t,l);for(var h=e.utils.projectToWorld([l.left,l.bottom,0]),p=e.utils.projectToWorld([l.right,l.top,0]),u=p.x-n.x,c=h.x-n.x,d=p.y-n.y,f=h.y-n.y,m=[],v=[],g=0;g<o.length-1;g++){var y=NPMap.T.setPoint(t,new NPMap.Geometry.Point(o[g].lon,o[g].lat)),x=e.utils.projectToWorld([y.lon,y.lat,0]),_=new THREE.Vector2(x.x-n.x,x.y-n.y);m.push(_)}for(g=0;g<r.length;g++){for(var b=r[g].getPath(),w=[],M=0;M<b.length-1;M++){y=NPMap.T.setPoint(t,new NPMap.Geometry.Point(b[M].lon,b[M].lat)),x=e.utils.projectToWorld([y.lon,y.lat,0]),_=new THREE.Vector2(x.x-n.x,x.y-n.y);w.push(_)}v.push(w)}return this._initVecs(m,v,n.z,u,d,c,f)},t.prototype._initVecs=function(t,e,i,r,a,o,s){this.groundBufferGeometry.clearGroups();for(var n=o-r,l=s-a,h=Math.max(n,l),p=[],u=[],c=[],d=[],f=0;f<t.length;f++)d.push(t[f]);for(f=0;f<e.length;f++)for(var m=0;m<e[f].length;m++)d.push(e[f][m]);var v,g,y=THREE.ShapeUtils.triangulateShape(t,e);for(f=0;f<y.length;f++){var x=y[f];p.push(d[x[0]].x,d[x[0]].y,this.hasHoles?0:i),p.push(d[x[1]].x,d[x[1]].y,this.hasHoles?0:i),p.push(d[x[2]].x,d[x[2]].y,this.hasHoles?0:i),u.push(0,0,1),u.push(0,0,1),u.push(0,0,1),c.push((d[x[0]].x-r)/h,(d[x[0]].y-a)/h),c.push((d[x[1]].x-r)/h,(d[x[1]].y-a)/h),c.push((d[x[2]].x-r)/h,(d[x[2]].y-a)/h)}if(this.groundBufferGeometry.addGroup(0,p.length/3,0),!this.hasHoles){var _=p.length/3;for(m=0;m<e.length;m++){var b=e[m],w=[];this.aloneUV&&(w=(b[0].x-b[1].x)*(b[0].x-b[1].x)+(b[0].y-b[1].y)*(b[0].y-b[1].y)>(b[2].x-b[1].x)*(b[2].x-b[1].x)+(b[2].y-b[1].y)*(b[2].y-b[1].y)?[[0,0],[1,0],[1,1],[0,1]]:[[0,1],[0,0],[1,0],[1,1]]);var M=THREE.ShapeUtils.triangulateShape(b,[]);for(f=0;f<M.length;f++){x=M[f];p.push(b[x[0]].x,b[x[0]].y,i),p.push(b[x[1]].x,b[x[1]].y,i),p.push(b[x[2]].x,b[x[2]].y,i),u.push(0,0,1),u.push(0,0,1),u.push(0,0,1),this.aloneUV?(c.push(w[x[0]][0],w[x[0]][1]),c.push(w[x[1]][0],w[x[1]][1]),c.push(w[x[2]][0],w[x[2]][1])):(c.push((b[x[0]].x-r)/h,(b[x[0]].y-a)/h),c.push((b[x[1]].x-r)/h,(b[x[1]].y-a)/h),c.push((b[x[2]].x-r)/h,(b[x[2]].y-a)/h))}this.groundBufferGeometry.addGroup(_,p.length/3-_,1),_=p.length/3}t.push(t[0]);for(f=0;f<t.length-1;f++){var E=f+1,T=(v=t[E].x-t[f].x,{x:(g=t[E].y-t[f].y)/Math.sqrt(v*v+g*g),y:-v/Math.sqrt(v*v+g*g),z:0});p.push(t[f].x,t[f].y,0+i),p.push(t[E].x,t[E].y,0),p.push(t[f].x,t[f].y,0),u.push(T.x,T.y,T.z),u.push(T.x,T.y,T.z),u.push(T.x,T.y,T.z),c.push(1,1),c.push(0,0),c.push(0,1),p.push(t[f].x,t[f].y,0+i),p.push(t[E].x,t[E].y,0+i),p.push(t[E].x,t[E].y,0),u.push(T.x,T.y,T.z),u.push(T.x,T.y,T.z),u.push(T.x,T.y,T.z),c.push(1,1),c.push(1,0),c.push(0,0)}this.groundBufferGeometry.addGroup(_,p.length/3-_,2)}var R=new Float32Array(p.length);R.set(p),this.groundBufferGeometry.setAttribute("position",new THREE.BufferAttribute(R,3));var P=new Float32Array(u.length);P.set(u),this.groundBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(P,3));var S=new Float32Array(c.length);S.set(c),this.groundBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(S,2))},t.prototype.toJson=function(){var t={},e=WKT.write(this.polygon);t.landGeometry=e,t.grassGeometries=[];for(var i=0;i<this.grassPolygons.length;i++){var r=WKT.write(this.grassPolygons[i]);t.grassGeometries.push(r)}return t.landImage={imageURL:this.landImageURL,uv:this.landUV},t.grassImage={imageURL:this.grassImageURL,uv:this.grassUV},t}}(),function(){var t=BigScreenMap.GroundManager=function(t,e){this._map=t,this.tb=e};t.prototype.addGround=function(t){t.init(this._map,this.tb);var e=NPMap.T.setPoint(this._map,t.position);t.mesh.parentObj=t,this.tb.addAtCoordinate(t.mesh,[e.lon,e.lat,0])},t.prototype.removeGround=function(t){t.mesh&&(this.tb.remove(t.mesh),t.buildBufferGeometry&&t.buildBufferGeometry.dispose(),t.buildBufferGeometry=null,t.mesh=null)}}(),function(){var t=BigScreenMap.TreeCollect=function(t,e,i,r,a,o){this.type="Tree",this.arrayObjs=t,this.type=e,this.split=i,this.treeModels=r,this.side=a,this.mode=o||"tree_out"};t.prototype._getWorkerContent=function(){return"self.onmessage = mainFunc;function mainFunc(e) {    let treeMeshes = e.data['treeMeshes'];    let points = e.data['points'];    var bufferGeometry =  e.data['bufferGeometry'];    var materials = [];    var geometryV = {};    var geometryN = {};    var geometryUV = {};    for (var i = 0; i < points.length; i++) {        var meshesMap = treeMeshes[i % treeMeshes.length];        for (var j = 0, keys = Object.keys(meshesMap); j < keys.length; j++) {            var key = keys[j];            for (var k = 0; k < meshesMap[key].geometry.length; k++) {               var geometry = meshesMap[key].geometry[k];               geometry.rotateX(-Math.PI / 2);               geometry.rotateY(Math.PI);               if (points[i].angle) {                   geometry.rotateZ(points[i].angle);               }               var scale = points[i].scale ? points[i].scale : 1;               geometry.scale(scale / 25.4, scale / 25.4, scale / 25.4);               geometry.translate(points[i].x, points[i].y, points[i].z);               var geoPosArray = geometry.getAttribute('position').array;               var geoNorArray = geometry.getAttribute('normal').array;               if (!geometryV[key]) {                   geometryV[key] = [];                   materials.push(meshesMap[key].material);               }               if (!geometryN[key]) {                   geometryN[key] = [];               }               if (!geometryUV[key]) {                   geometryUV[key] = [];               }               var geoUVArray = null;               if (geometry.getAttribute('uv')) {                    geoUVArray = geometry.getAttribute('uv').array;               }               var geometryIndexs = geometry.index.array;               for (var m = 0; m < geometryIndexs.length; m++) {                   geometryV[key].push(geoPosArray[geometryIndexs[m] * 3], geoPosArray[geometryIndexs[m] * 3 + 1], geoPosArray[geometryIndexs[m] * 3 + 2]);                   geometryN[key].push(geoNorArray[geometryIndexs[m] * 3], geoNorArray[geometryIndexs[m] * 3 + 1], geoNorArray[geometryIndexs[m] * 3 + 2]);                   if (geoUVArray) {                       geometryUV[key].push(geoUVArray[geometryIndexs[m] * 2], geoUVArray[geometryIndexs[m] * 2 + 1]);                   } else {                       geometryUV[key].push(0, 0);                   }               }          }       }   }   var positions = [];   var normals = [];   var uv = [];   var index = 0;   var mindex = 0;   for (var i = 0, keys = Object.keys(geometryV); i < keys.length; i++) {       var key = keys[i];       for (var j = 0; j < geometryV[key].length; j++) {           positions.push(geometryV[key][j]);      }       for (var j = 0; j < geometryN[key].length; j++) {           normals.push(geometryN[key][j]);       }       for (var j = 0; j < geometryUV[key].length; j++) {           uv.push(geometryUV[key][j]);       }       bufferGeometry.addGroup(index, positions.length / 3 - index, mindex);       mindex++;       index = positions.length / 3;   }   var vers = new Float32Array(positions.length);   vers.set(positions);   bufferGeometry.setAttribute('position', new THREE.BufferAttribute(vers, 3));   var ns = new Float32Array(normals.length);   ns.set(normals);   this.bufferGeometry.setAttribute('normal', new THREE.BufferAttribute(ns, 3));   var uvs = new Float32Array(uv.length);   uvs.set(uv);   bufferGeometry.setAttribute('uv', new THREE.BufferAttribute(uvs, 2));   postMessage({       geometry:bufferGeometry,       materials:materials,   });}"},t.prototype.init=function(t,e){this.map=t,this.threebox=e,this.points=this.createPoints(this.arrayObjs,this.type),this.position=this.points[0],this._createGeometry(this.points)},t.prototype._createGeometry2=function(){for(var t=[],e=0;e<this.treeModels.length;e++){for(var i=this._getMeshforObj(this.treeModels[e]),r={},a=0;a<i.length;a++)r[i[a].material.uuid]?r[i[a].material.uuid].geometry.push(i[a].geometry):r[i[a].material.uuid]={material:i[a].material,geometry:[i[a].geometry]};t.push(r)}this.bufferGeometry=new THREE.BufferGeometry,this.points=this.createPoints(this.arrayObjs,this.type),this.position=this.points[0];var o=NPMap.T.setPoint(this.map,this.position);o=this.threebox.projectToWorld([o.lon,o.lat,0]);var s=[];for(e=0;e<this.points.length;e++){var n=NPMap.T.setPoint(this.map,this.points[e]),l=(n=this.threebox.projectToWorld([n.lon,n.lat,0])).x-o.x,h=n.y-o.y,p=n.z-o.z;s.push({x:l,y:h,z:p})}this.main.onmessage=function(t){console.log(t)},this.main.postMessage({points:s,treeMeshes:t,bufferGeometry:this.bufferGeometry})},t.prototype.updata=function(t,e,i,r,a){this.arrayObjs=t||this.arrayObjs,this.type=e||this.type,this.split=i||this.split,this.treeModels=r||this.treeModels,this.side=void 0!==a?a:this.side,this.mesh&&this.threebox&&(this.threebox.remove(this.mesh),this.mesh.geometry.dispose(),this.mesh.geometry=null,this.mesh=null),this.init(this.map,this.threebox);var o=NPMap.T.setPoint(this.map,this.position);(this.mesh.parentObj=this).threebox.addAtCoordinate(this.mesh,[o.lon,o.lat,0]),this.map.repaint()},t.prototype._getMeshforObj=function(t){var r=[];return function t(e){if(e.children.length)for(var i=0;i<e.children.length;i++)"Mesh"===e.children[i].type?r.push(e.children[i]):t(e.children[i])}(t),r},t.prototype._createGeometry=function(t){this.bufferGeometry=new THREE.BufferGeometry;for(var e=[],i=0;i<this.treeModels.length;i++){for(var r=this._getMeshforObj(this.treeModels[i]),a={},o=0;o<r.length;o++)a[r[o].material.uuid]?a[r[o].material.uuid].geometry.push(r[o].geometry):a[r[o].material.uuid]={material:r[o].material,geometry:[r[o].geometry]};e.push(a)}var s=[],n={},l={},h={},p=NPMap.T.setPoint(this.map,this.position);p=this.threebox.projectToWorld([p.lon,p.lat,0]);for(i=0;i<t.length;i++)for(var u=NPMap.T.setPoint(this.map,t[i]),c=(u=this.threebox.projectToWorld([u.lon,u.lat,0])).x-p.x,d=u.y-p.y,f=u.z-p.z,m=(a=e[i%e.length],o=0,Object.keys(a));o<m.length;o++)for(var v=m[o],g=0;g<a[v].geometry.length;g++){var y=a[v].geometry[g].clone();y.rotateX(-Math.PI/2),y.rotateY(Math.PI),t[i].angle&&y.rotateZ(t[i].angle);var x=t[i].scale?t[i].scale:1;y.scale(x/25.4,x/25.4,x/25.4),y.translate(c,d,f);var _=y.getAttribute("position").array,b=y.getAttribute("normal").array;n[v]||(n[v]=[],s.push(a[v].material)),l[v]||(l[v]=[]),h[v]||(h[v]=[]);var w=null;y.getAttribute("uv")&&(w=y.getAttribute("uv").array);for(var M=y.index.array,E=0;E<M.length;E++)n[v].push(_[3*M[E]],_[3*M[E]+1],_[3*M[E]+2]),l[v].push(b[3*M[E]],b[3*M[E]+1],b[3*M[E]+2]),w?h[v].push(w[2*M[E]],w[2*M[E]+1]):h[v].push(0,0)}var T=[],R=[],P=[],S=0,C=0;for(i=0,m=Object.keys(n);i<m.length;i++){for(v=m[i],o=0;o<n[v].length;o++)T.push(n[v][o]);for(o=0;o<l[v].length;o++)R.push(l[v][o]);for(o=0;o<h[v].length;o++)P.push(h[v][o]);this.bufferGeometry.addGroup(S,T.length/3-S,C),C++,S=T.length/3}var I=new Float32Array(T.length);I.set(T),this.bufferGeometry.setAttribute("position",new THREE.BufferAttribute(I,3));var F=new Float32Array(R.length);F.set(R),this.bufferGeometry.setAttribute("normal",new THREE.BufferAttribute(F,3));var L=new Float32Array(P.length);L.set(P),this.bufferGeometry.setAttribute("uv",new THREE.BufferAttribute(L,2)),this.mesh=new THREE.Mesh(this.bufferGeometry,s)},t.prototype.createPoints=function(t,e){if("POINT"===e)return t;if("POLYLINE"===e){for(var i=[],r=0;r<t.length;r++){var a=t[r].getPath();i.push.apply(i,this._calculateSeg(a))}return i}for(i=[],r=0;r<t.length;r++)i.push.apply(i,this._calculateSeg2(t[r]));return i},t.prototype._calculateSeg=function(t){for(var e=[],i=0,r=0;r<t.length-1;r++){var a=t[r],o=t[r+1],s=NPMap.T.helper.webMoctorJW2PM(a.lon,a.lat),n=NPMap.T.helper.webMoctorJW2PM(o.lon,o.lat);angle=Math.atan((n.lat-s.lat)/(n.lon-s.lon)),n.lon-s.lon<0&&(angle-=Math.PI),angle+=Math.PI/2,this.side&&(angle+=Math.PI);var l=Math.sqrt((s.lon-n.lon)*(s.lon-n.lon)+(s.lat-n.lat)*(s.lat-n.lat));if(l<i)i-=l;else{s.lon=(n.lon-s.lon)*i/l+s.lon,s.lat=(n.lat-s.lat)*i/l+s.lat,l=Math.sqrt((s.lon-n.lon)*(s.lon-n.lon)+(s.lat-n.lat)*(s.lat-n.lat));for(var h=Math.floor(l/this.split),p=0;p<h;p++){per=p/h,x=parseFloat((n.lon-s.lon)*per)+parseFloat(s.lon),y=parseFloat((n.lat-s.lat)*per)+parseFloat(s.lat);var u=NPMap.T.helper.inverseMercator(x,y);u.angle=angle,e.push(u)}}}return e},t.prototype._calculateSeg2=function(t){var e,i,r,a=[],o=t.getExtent(),s=NPMap.T.helper.webMoctorJW2PM(o.left,o.bottom),n=NPMap.T.helper.webMoctorJW2PM(o.right,o.top),l=Math.abs(n.lon-s.lon)*Math.abs(n.lat-s.lat)/this.split/this.split,h=turf.random("points",l,{bbox:[o.left,o.bottom,o.right,o.top]}),p=t.getPath(),u=[];for(var c=0;c<p.length;c++)u.push([p[c].lon,p.lat]);for(var d=0;d<h.features.length;d++){var f=(void 0,i=10-(e=5),r=Math.random(),(e+Math.round(r*i))/10),m=h.features[d].geometry.coordinates;NPMap.Utils.MapUtil.ContainsPoint(p,{lon:m[0],lat:m[1]})&&a.push({lon:m[0],lat:m[1],scale:f})}return a}}(),function(){var t=BigScreenMap.TreeManager=function(t,e){this._map=t,this.tb=e};t.prototype.addTreeCollect=function(t){t.init(this._map,this.tb);var e=NPMap.T.setPoint(this._map,t.position);t.mesh.parentObj=t,this.tb.addAtCoordinate(t.mesh,[e.lon,e.lat,0])},t.prototype.removeTreeCollect=function(t){t.mesh&&(this.tb.remove(t.mesh),t.bufferGeometry.dispose(),t.bufferGeometry=null,t.mesh=null)}}(),function(){var a=function(t){var e=a.SkyShader;t||(t=NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/sky.png");var i=(new THREE.TextureLoader).load(t),r=new THREE.ShaderMaterial({fragmentShader:e.fragmentShader,vertexShader:e.vertexShader,uniforms:{map:{value:i}},side:THREE.DoubleSide});THREE.Mesh.call(this,new THREE.SphereBufferGeometry(6378137e3,64,64,0,2*Math.PI,0,Math.PI/2),r)};a.prototype=Object.create(THREE.Mesh.prototype),a.SkyShader={vertexShader:["precision lowp float;","precision lowp int;","varying vec2 vUv;","void main() {","   vUv = uv;","   gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","}"].join("\n"),fragmentShader:["precision lowp float;","precision lowp int;","varying vec2 vUv;","uniform sampler2D map;","void main() {","   gl_FragColor = texture2D(map, vUv);","}"].join("\n")},(BigScreenMap.MapSkyBox2=function(t){this.sky=new a(t),this.sky.rotateX(Math.PI/2),this.sky.rotateY(Math.PI)}).prototype.addToMap=function(t){(this.threeLayer=t).tb.scene.add(this.sky)}}(),function(){var d=function(){var t=d.SkyShader,e=new THREE.ShaderMaterial({fragmentShader:t.fragmentShader,vertexShader:t.vertexShader,uniforms:THREE.UniformsUtils.clone(t.uniforms),side:THREE.BackSide});THREE.Mesh.call(this,new THREE.BoxBufferGeometry(1,1,1),e)};d.prototype=Object.create(THREE.Mesh.prototype),d.SkyShader={uniforms:{luminance:{value:1},turbidity:{value:2},rayleigh:{value:1},mieCoefficient:{value:.005},mieDirectionalG:{value:.8},sunPosition:{value:new THREE.Vector3},up:{value:new THREE.Vector3(0,1,0)}},vertexShader:["uniform vec3 sunPosition;","uniform float rayleigh;","uniform float turbidity;","uniform float mieCoefficient;","uniform vec3 up;","varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","const float e = 2.71828182845904523536028747135266249775724709369995957;","const float pi = 3.141592653589793238462643383279502884197169;","const vec3 lambda = vec3( 680E-9, 550E-9, 450E-9 );","const vec3 totalRayleigh = vec3( 5.804542996261093E-6, 1.3562911419845635E-5, 3.0265902468824876E-5 );","const float v = 4.0;","const vec3 K = vec3( 0.686, 0.678, 0.666 );","const vec3 MieConst = vec3( 1.8399918514433978E14, 2.7798023919660528E14, 4.0790479543861094E14 );","const float cutoffAngle = 1.6110731556870734;","const float steepness = 1.5;","const float EE = 1000.0;","float sunIntensity( float zenithAngleCos ) {","\tzenithAngleCos = clamp( zenithAngleCos, -1.0, 1.0 );","\treturn EE * max( 0.0, 1.0 - pow( e, -( ( cutoffAngle - acos( zenithAngleCos ) ) / steepness ) ) );","}","vec3 totalMie( float T ) {","\tfloat c = ( 0.2 * T ) * 10E-18;","\treturn 0.434 * c * MieConst;","}","void main() {","\tvec4 worldPosition = modelMatrix * vec4( position, 1.0 );","\tvWorldPosition = worldPosition.xyz;","\tvWorldPosition.y = worldPosition.z;","\tvWorldPosition.z = worldPosition.y;","\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );","\tgl_Position.z = gl_Position.w;","\tvSunDirection = normalize( sunPosition );","\tvSunE = sunIntensity( dot( vSunDirection, up ) );","\tvSunfade = 1.0 - clamp( 1.0 - exp( ( sunPosition.y / 450000.0 ) ), 0.0, 1.0 );","\tfloat rayleighCoefficient = rayleigh - ( 1.0 * ( 1.0 - vSunfade ) );","\tvBetaR = totalRayleigh * rayleighCoefficient;","\tvBetaM = totalMie( turbidity ) * mieCoefficient;","}"].join("\n"),fragmentShader:["varying vec3 vWorldPosition;","varying vec3 vSunDirection;","varying float vSunfade;","varying vec3 vBetaR;","varying vec3 vBetaM;","varying float vSunE;","uniform float luminance;","uniform float mieDirectionalG;","uniform vec3 up;","const vec3 cameraPos = vec3( 0.0, 0.0, 0.0 );","const float pi = 3.141592653589793238462643383279502884197169;","const float n = 1.0003;","const float N = 2.545E25;","const float rayleighZenithLength = 8.4E3;","const float mieZenithLength = 1.25E3;","const float sunAngularDiameterCos = 0.999956676946448443553574619906976478926848692873900859324;","const float THREE_OVER_SIXTEENPI = 0.05968310365946075;","const float ONE_OVER_FOURPI = 0.07957747154594767;","float rayleighPhase( float cosTheta ) {","\treturn THREE_OVER_SIXTEENPI * ( 1.0 + pow( cosTheta, 2.0 ) );","}","float hgPhase( float cosTheta, float g ) {","\tfloat g2 = pow( g, 2.0 );","\tfloat inverse = 1.0 / pow( 1.0 - 2.0 * g * cosTheta + g2, 1.5 );","\treturn ONE_OVER_FOURPI * ( ( 1.0 - g2 ) * inverse );","}","const float A = 0.15;","const float B = 0.50;","const float C = 0.10;","const float D = 0.20;","const float E = 0.02;","const float F = 0.30;","const float whiteScale = 1.0748724675633854;","vec3 Uncharted2Tonemap( vec3 x ) {","\treturn ( ( x * ( A * x + C * B ) + D * E ) / ( x * ( A * x + B ) + D * F ) ) - E / F;","}","void main() {","\tfloat zenithAngle = acos( max( 0.0, dot( up, normalize( vWorldPosition - cameraPos ) ) ) );","\tfloat inverse = 1.0 / ( cos( zenithAngle ) + 0.15 * pow( 93.885 - ( ( zenithAngle * 180.0 ) / pi ), -1.253 ) );","\tfloat sR = rayleighZenithLength * inverse;","\tfloat sM = mieZenithLength * inverse;","\tvec3 Fex = exp( -( vBetaR * sR + vBetaM * sM ) );","\tfloat cosTheta = dot( normalize( vWorldPosition - cameraPos ), vSunDirection );","\tfloat rPhase = rayleighPhase( cosTheta * 0.5 + 0.5 );","\tvec3 betaRTheta = vBetaR * rPhase;","\tfloat mPhase = hgPhase( cosTheta, mieDirectionalG );","\tvec3 betaMTheta = vBetaM * mPhase;","\tvec3 Lin = pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * ( 1.0 - Fex ), vec3( 1.5 ) );","\tLin *= mix( vec3( 1.0 ), pow( vSunE * ( ( betaRTheta + betaMTheta ) / ( vBetaR + vBetaM ) ) * Fex, vec3( 1.0 / 2.0 ) ), clamp( pow( 1.0 - dot( up, vSunDirection ), 5.0 ), 0.0, 1.0 ) );","\tvec3 direction = normalize( vWorldPosition - cameraPos );","\tfloat theta = acos( direction.y ); // elevation --\x3e y-axis, [-pi/2, pi/2]","\tfloat phi = atan( direction.z, direction.x ); // azimuth --\x3e x-axis [-pi/2, pi/2]","\tvec2 uv = vec2( phi, theta ) / vec2( 2.0 * pi, pi ) + vec2( 0.5, 0.0 );","\tvec3 L0 = vec3( 0.1 ) * Fex;","\tfloat sundisk = smoothstep( sunAngularDiameterCos, sunAngularDiameterCos + 0.00002, cosTheta );","\tL0 += ( vSunE * 19000.0 * Fex ) * sundisk;","\tvec3 texColor = ( Lin + L0 ) * 0.04 + vec3( 0.0, 0.0003, 0.00075 );","\tvec3 curr = Uncharted2Tonemap( ( log2( 2.0 / pow( luminance, 4.0 ) ) ) * texColor );","\tvec3 color = curr * whiteScale;","\tvec3 retColor = pow( color, vec3( 1.0 / ( 1.2 + ( 1.2 * vSunfade ) ) ) );","\tgl_FragColor = vec4( retColor, 1.0 );","}"].join("\n")};var t=BigScreenMap.MapSkyBox=function(){};t.prototype.addToMap=function(t){var e=new d;this.threeLayer=t,e.scale.setScalar(45e4),t.tb.scene.add(e),sunSphere=new THREE.Mesh(new THREE.SphereBufferGeometry(2e4,16,8),new THREE.MeshBasicMaterial({color:16777215})),sunSphere.position.y=-7e5,sunSphere.visible=!1,t.tb.scene.add(sunSphere);var i=10,r=2,a=.005,o=.8,s=1,n=.3,l=.25,h=!1,p=e.material.uniforms;p.turbidity.value=i,p.rayleigh.value=r,p.mieCoefficient.value=a,p.mieDirectionalG.value=o,p.luminance.value=s;var u=Math.PI*(n-.5),c=2*Math.PI*(l-.5)-Math.PI/4;sunSphere.position.x=4e5*Math.cos(c),sunSphere.position.y=4e5*Math.sin(c)*Math.sin(u),sunSphere.position.z=4e5*Math.sin(c)*Math.cos(u),sunSphere.visible=h,p.sunPosition.value.copy(sunSphere.position),this.skyMaterial=e.material,this.sunSphere=sunSphere,this.sky=e},t.prototype.positionRotatef=function(t,e,i,r,a){var o=Math.cos(e),s=Math.sin(e),n=t.x,l=t.y,h=t.z;return{x:(i*i*(1-o)+o)*n+(i*r*(1-o)-a*s)*l+(i*a*(1-o)+r*s)*h,y:(r*i*(1-o)+a*s)*n+(r*r*(1-o)+o)*l+(r*a*(1-o)-i*s)*h,z:(i*a*(1-o)-r)*n+(r*a*(1-o)+i*s)*l+(a*a*(1-o)+o)*h}},t.prototype.updateSunEffect=function(t){var e={turbidity:10,rayleigh:2,mieCoefficient:.005,mieDirectionalG:.8,luminance:1,inclination:.5,azimuth:.25,sun:!1};for(k in t)e[k]=t[k];var i=this.skyMaterial.uniforms;i.turbidity.value=e.turbidity,i.rayleigh.value=e.rayleigh,i.mieCoefficient.value=e.mieCoefficient,i.mieDirectionalG.value=e.mieDirectionalG,i.luminance.value=e.luminance;var r=Math.PI*(e.inclination-.5),a=2*Math.PI*(e.azimuth-.5);this.sunSphere.position.x=4e5*Math.cos(a)*Math.cos(r),this.sunSphere.position.y=4e5*Math.sin(a)*Math.sin(r),this.sunSphere.position.z=4e5*Math.sin(a)*Math.cos(r),this.sunSphere.visible=e.sun,i.sunPosition.value.copy(this.sunSphere.position)}}(),function(){var t=BigScreenMap.CylinderSpread=function(t,e,i){this.radius=t||100,this.height=e||50,this.color=i||"#ffffff",this.loop=!0,this._init()};t.prototype._init=function(){var t=new THREE.CylinderBufferGeometry(this.radius/25.4,this.radius/25.4,this.height/25.4,32,1,!0,0,2*Math.PI),e=NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/scale1.png",i=(new THREE.TextureLoader).load(e);i.wrapS=THREE.RepeatWrapping,i.wrapT=THREE.RepeatWrapping;var r=new THREE.MeshBasicMaterial({color:this.color,side:THREE.DoubleSide,transparent:!0,depthWrite:!1,blending:THREE.AdditiveBlending}),a=["#include <uv2_pars_vertex>","varying vec2 nvUv;","mat3 setUvTransform(float tx, float ty, float sx, float sy, float rotation, float cx, float cy) {","float c = cos( rotation );","float s = sin( rotation );","return mat3(","    sx * c, sx * s, - sx * ( c * cx + s * cy ) + cx + tx,","    - sy * s, sy * c, - sy * ( - s * cx + c * cy ) + cy + ty,","    tx, ty, 1",");","}"].join("\n"),o=["#include <uv2_vertex>","nvUv = uv;","mat3 uvMat3 = setUvTransform(0.0, -0.03, 1.0, 1.0, 0.0, 0.0, 0.0);","nvUv = ( uvMat3 * vec3( uv, 1. ) ).xy;"].join("\n"),s=["uniform float opacity;","uniform float lightIntensity;","uniform sampler2D texture1;","varying vec2 nvUv;"].join("\n"),n=["vec4 resultColor = vec4(outgoingLight, opacity);","vec4 texCol = texture2D(texture1, nvUv);","resultColor *= texCol * lightIntensity;","gl_FragColor = vec4( resultColor );"].join("\n"),l=this;r.onBeforeCompile=function(t){t.uniforms.lightIntensity={value:1},t.uniforms.texture1={value:i},t.vertexShader=t.vertexShader.replace("#include <uv2_pars_vertex>",a),t.vertexShader=t.vertexShader.replace("#include <uv2_vertex>",o),t.fragmentShader=t.fragmentShader.replace("uniform float opacity;",s),t.fragmentShader=t.fragmentShader.replace("gl_FragColor = vec4( outgoingLight, diffuseColor.a );",n),l.sharder=t},this.cincture=new THREE.Mesh(t,r),this.cincture.rotateX(Math.PI/2),this.cincture.rotateY(Math.PI),this.objects=new THREE.Object3D,this.objects.add(this.cincture)},t.prototype._update=function(){this.loop&&(this.cincture.scale.x=this.cincture.scale.x+.02,1<this.cincture.scale.x&&(this.cincture.scale.x=.4),this.cincture.scale.z=this.cincture.scale.x,this.sharder&&(this.sharder.uniforms.lightIntensity.value=1.3-this.cincture.scale.x),this._map._obj.repaint=!0,requestAnimationFrame(this._update.bind(this)))},t.prototype.addToMap=function(t,e){this.tb=t.tb,this._map=t._map;var i=NPMap.T.setPoint(this._map,e);this.tb.addAtCoordinate(this.objects,[i.lon,i.lat,e.height]),this._update()},t.prototype.destory=function(){this.timer&&clearTimeout(this.timer),this.loop=!1,this.timer=null,delete this.timer;for(var t=0;t<this.lights.length;t++)this.tb.remove(this.lights[t].light);this.lights.length=0,this.lights=null,this.lightmanager=null,delete this.lightmanager}}();