<template>
  <div class="layout">
    <ui-tabs :list="showTabList" :selectLi="currentTab" @change="tabChange"/>
    <div class="container">
      <!-- 操作栏 -->
      <div class="operate">
        <div class="control-title">推荐列表</div>
      </div>
      <ul class="list_tab">
        <li class="components_list" v-for='(item, index) in modelTasks[currentTab].modelList' :key="index">
          <img :src="modelTasks[currentTab].imgUrl" alt="">
          <div class="header">
            <p class="components_list_title ellipsis"> {{ item.name }} </p>
            <i class="iconfont icon-shezhi3" title="参数编辑" @click.stop="handleParams(item)"/>
          </div>
          <div class="components_list_switch">
            <i-switch title="开启/停止" :loading="switchLoading" :value="item.task && item.task.taskEnableState == 1?true:false" :before-change="() => handleSwitch(item)"/>
            <span class="ml-10">订阅已{{item.task && item.task.taskEnableState == 1?'开启':'关闭'}}</span>
          </div>
          <p class="components_list_subtitle ellipsis">
            <template v-if="item.task && item.task.taskResult">
              共发现{{ item.task.taskResult.total }}{{ item.task.taskResult.totalRelation == 'gte' ? '+' : '' }}个疑似目标
            </template>
          </p>
          <div class="archives-item-info">
            <div class="item">
              <div class="label title-color">最新发现时间：</div>
              <div class="value text-color ellipsis mb-5">{{ item.task && item.task.taskEndTime || ''}}</div>
            </div>
            <div class="item">
              <div class="label title-color">时间范围：</div>
              <div class="value text-color ellipsis">
                {{ item.task && item.task.taskResult && item.task.taskResult.queryStartTime | timeFormat('') }}
                <span v-if="item.task && item.task.taskResult && item.task.taskResult.queryEndTime"> - </span>
                {{ item.task && item.task.taskResult && item.task.taskResult.queryEndTime | timeFormat('') }}</div>
            </div>
          </div>
          <div class="viewStatus" v-if="item.task && item.task.readStatus == 0"></div>
          <div class="operate-bar" @click.stop="">
            <p class="operate-content">
              <span class="button" @click="handleView(item)">推荐结果</span>
              <span class="line"></span>
              <span class="button" @click="handleQuery(item)">手工研判</span>
            </p>
          </div>
        </li>
        <template v-if="modelTasks[currentTab].modelList.length % 5">
          <li class="card-tab" v-for="(item, index) of 5 - (modelTasks[currentTab].modelList.length % 5)" :key="index + 'demo'"></li>
        </template>
        <ui-loading v-if="loading" />
      </ul>
    </div>

    <!-- 新增/编辑 -->
    <AddModal ref="addModal" @updated="handleRefresh"/>

    <!-- 查看详情 -->
    <fullModal :title="viewModal.title" :footer-hide="true" ref="fullModal">
      <component :is="dynamicComponent" :taskParams="taskParams"/>
    </fullModal>
  </div>
</template>
<script>
  import uiTabs from '@/components/ui-tabs.vue'
  import AddModal from './components/add-modal.vue'
  import { tabList } from './tabList.js'
  import { recommendList, recommendStart, recommendStop, recommendDel, recommendRead } from '@/api/recommend.js'
  import fullModal from '@/components/full-modal/index.vue'
  export default {
    name: 'recommendation-center',
    components: { 
      uiTabs,
      AddModal,
      fullModal
    },
    data() {
      return {
        showTabList: [],
        currentTab: undefined,
        modelTasks: {},
        loading: false,
        switchLoading: false,
        tabList: tabList,
        viewModal: {
          title: '查看详情'
        },
        dynamicComponent: null,
        taskParams: {}
      }
    },
    created() {
      this.tabList.forEach(v => {
        v.modelList = v.modelList.filter(i => {
          return this.$_has([i.type])
        })
      })
      this.showTabList = this.tabList.filter(v => v.modelList.length)
      this.showTabList.forEach(v => {
        this.modelTasks[v.value] = v
      })
      this.currentTab = this.showTabList[0] ? this.showTabList[0].value : ''
      this.getList()
    },
    methods: {
      tabChange(row) {
        this.currentTab = row.value
      },
      getList() {
        this.loading = true
        recommendList({
          pageNumber: 1,
          pageSize: 999
        }).then(res => {
          this.setModelList(res.data.entities || [])
        }).finally(res => {
          this.loading = false
        })
      },
      handleRefresh() {
        this.getList()
      },
      setModelList(array) {
        console.log(array, 'array')
        array.forEach(v => {
          const type = v.taskType.match(/[^_]+/)[0]
          if (this.modelTasks[type] && this.modelTasks[type].modelList.length) {
            let item = this.modelTasks[type].modelList.find(i => i.type == v.taskType)
            if (v.taskResult) v.taskResult = JSON.parse(v.taskResult)
            item.task = v
          }
        })

      },
      add(item) {
        this.$refs.addModal.show(1, item)
      },
      edit(item) {
        this.$refs.addModal.show(2, item)
      },
      handleParams(item) {
        if (item.task && item.task.id) {
          // 编辑
          this.edit(item)
        } else {
          // 新增
          this.add(item)
        }
      },
      handleSwitch(row) {
        return new Promise((resolve) => {
          this.switchLoading = true
          if (!row.task || !row.task.id) {
            // 新增
            this.switchLoading = false
            this.add(row)
            return
          }else if (row.task.taskEnableState != 1) {
            // 开启
            recommendStart(row.task.id).then(res =>{
              this.switchLoading = false
              resolve()
              this.handleRefresh()
            })
          } else {
            // 关闭
            recommendStop(row.task.id).then(res =>{
              this.switchLoading = false
              resolve()
              this.handleRefresh()
            })
          }
        })
      },
      handleView(item) {
        if (!item.task || !item.task.id) {
          this.$Message.warning('订阅未开启,可以选择手工研判')
          return
        }
        if (!item.component) {
          this.$Message.warning('开发中。。。')
          return
        }
        if (!item.task.taskResult) {
          this.$Message.warning('暂无推荐结果,可以选择手工研判')
          return
        }
        recommendRead(item.task.id)
        this.viewModal.title = `【${item.name}】详情`
        // 动态引入组件
        this.dynamicComponent = item.component
        this.taskParams = {
          modelType: item.type,
          queryStartTime: item.task && item.task.taskResult && item.task.taskResult.queryStartTime || '',
          queryEndTime: item.task && item.task.taskResult && item.task.taskResult.queryEndTime || '',
          taskResult: item.task && item.task.taskResult,
          params: item.task && item.task.taskParam ? JSON.parse(item.task.taskParam) : {}
        }
        this.$refs.fullModal.open();
        
        this.handleRefresh()
      },
      handleQuery(item) {
        if (item.noManual) {
          this.$Message.warning('该推荐不支持手工研判')
          return
        }
        if (!item.component) {
          this.$Message.warning('开发中。。。')
          return
        }
        this.viewModal.title = `【${item.name}】详情`
        // 动态引入组件
        this.dynamicComponent = item.component
        this.taskParams = {}
        this.$refs.fullModal.open();
        
        this.handleRefresh()
      }
    }
  }
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .mr_20 {
    margin-right: 20px;
  }
  .tabs {
    height: 100px;
  }
  .container {
    flex: 1;
    display: flex;
		flex-direction: column;
		overflow: hidden;
    .operate {
			display: flex;
			justify-content: space-between;
			margin-bottom: 12px;
			.control-title {
				font-size: 16px;
				font-weight: 600;
				line-height: 34px;
        color: rgba(0, 0, 0, 0.9);
        position: relative;
        padding-left: 10px;
        &::after {
          content: '';
          display: block;
          width: 5px;
          background: #2C86F8;
          position: absolute;
          top: 7px;
          left: 0;
          height: 20px;
        }
			}
		}
  }
  .list_tab {
    display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
    position: relative;
    margin-bottom: 20px;
    .card-tab {
      width: 19%;
      height: 249px;
      list-style: none;
    }
    .components_list {
      overflow: hidden;
      position: relative;
			width: 19%;
			height: 249px;
			background: #f9f9f9;
			box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
			border-radius: 4px;
			position: relative;
			margin-bottom: 30px;
			padding-left: 15px;
      list-style: none;
      display: flex;
      flex-direction: column;
			&:hover {
				border: 2px solid #2C86F8;
        .operate-bar {
          bottom: 0;
        }
			}
			img {
				width: 120px;
        height: 120px;
        position: absolute;
        top: 50px;
        right: 20px;
			}
      &_switch {
        margin: 10px 0;
      }
      .header {
        display: flex;
        align-items: baseline;
        .iconfont {
          margin-right: 8px;
          cursor: pointer;
        }
      }
			&_title {
				font-size: 18px;
				font-weight: bold;
				color: rgba(0, 0, 0, 0.9);
				margin-top: 15px;
			}
			&_subtitle {
				font-size: 14px;
				color: #2C86F8;
				width: calc(~'100% - 160px');
        font-weight: bold;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: end;
			}
      .archives-item-info {
        width: 100%;
        padding-top: 10px;
        padding-bottom: 15px;
        .item {
          display: flex;
          flex-direction: column;
          .label {
            font-size: 14px;
            line-height: 20px;
            white-space: nowrap;
            color: rgba(0, 0, 0, 0.6);
          }
          .value {
            font-size: 12px;
            line-height: 20px;
            height: 20px;
            color: rgba(0, 0, 0, 0.9);
          }
        }
      }
      .viewStatus {
        height: 10px;
        width: 10px;
        border-radius: 10px;
        background: red;
        position: absolute;
        right: 10px;
        top: 10px;
      }
      .operate-bar {
        height: 30px;
        background: #2C86F8;
        width: 100%;
        left: 0;
        bottom: -30px;
        position: absolute;
        transition: all .3s;
        .operate-content {
          height: 100%;
          display: flex;
          align-items: center;
          color: #fff;
          justify-content: space-around;
          .line {
            height: 20px;
            width: 1px;
            background: #fff;
          }
          .button {
            cursor: pointer;
            flex: 1;
            text-align: center;
          }
        }
      }
		}
  }
}
.ivu-switch:after {
  background-color: #D3D7DE;
}
.ivu-switch-checked:after {
  background-color: #fff;
}
</style>