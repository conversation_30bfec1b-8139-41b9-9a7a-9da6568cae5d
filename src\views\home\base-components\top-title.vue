<template>
  <div class="top-title">
    <div id="top-lottie"></div>
    <span class="time f-14 time-color">{{ curtime }}</span>
    <div class="nav-logo">
      <img class="logo inline vt-middle" :src="systemConfig.logoUrl || require('@/assets/img/login/nav-title.png')" />
      <span class="title inline vt-middle">{{ systemConfig.applicationName }}</span>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import topJson from './json/top-lottie';
import lottie from 'lottie-web';
export default {
  name: 'top-title',
  components: {},
  props: {},
  data() {
    return {
      curtime: '',
      intervalID: null,
    };
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
  watch: {},
  filter: {},
  created() {
    this.currentTime();
    this.intervalID = setInterval(() => {
      this.currentTime();
    }, 1000);
  },
  mounted() {
    this.lottieInit();
  },
  beforeDestroy() {
    clearInterval(this.intervalID);
    this.intervalID = null;
  },
  methods: {
    lottieInit() {
      // 初始化渲染数仓中间gift图
      const params = {
        container: document.getElementById('top-lottie'),
        renderer: 'svg',
        loop: true,
        autoplay: true,
        animationData: topJson,
      };
      lottie.loadAnimation(params);
    },
    currentTime() {
      let date = new Date();
      let curYear = date.getFullYear();
      let curMonth = this.zeroFill(date.getMonth() + 1);
      let curDate = this.zeroFill(date.getDate());
      let curHours = this.zeroFill(date.getHours());
      let curMinutes = this.zeroFill(date.getMinutes());
      let curSeconds = this.zeroFill(date.getSeconds());
      let curtime = `${curYear} 年 ${curMonth} 月 ${curDate} 日 ${curHours} : ${curMinutes} : ${curSeconds} ${this.getWeek()}`;
      this.curtime = curtime;
    },
    zeroFill(i) {
      if (i >= 0 && i <= 9) {
        return '0' + i;
      } else {
        return i;
      }
    },
    getWeek() {
      let week = new Date().getDay();
      let weekArr = ['日', '一', '二', '三', '四', '五', '六'];
      return '星期' + weekArr[week];
    },
  },
};
</script>

<style lang="less" scoped>
.top-title {
  position: absolute;
  height: 75px;
  width: 100%;
  top: 0.8%;
  #top-lottie {
    width: 100%;
    position: absolute;
    top: -14px;
  }
  /*display: flex;
  justify-content: center;
  align-items: center;*/
  .title {
    font-size: 30px;
    font-weight: bold;
    color: #fff;
  }
  .time {
    position: absolute;
    left: 195px;
    top: 25px;
  }
  .nav-logo {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    .logo {
      margin-right: 20px;
      height: 50px;
      width: 50px;
    }
  }
  .time-color {
    color: #bee2fb;
  }
}
</style>
