<template>
  <ui-modal v-model="visible" :title="title" width="1200" :styles="styles" :footerHide="true" class="all-accuracy">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">相同轨迹数量：{{ searchData.totalCount }}</span>
          <span class="fl text_left">轨迹小图url: {{ this.dataMsg.trackImage }}</span>
        </div>

        <div class="list auto-fill">
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #identityPhoto="{ row }">
              <div @click="viewBigPic(row)" class="ui-images">
                <ui-image :src="!row.identityPhoto ? noImg : row.identityPhoto" />
              </div>
            </template>
            <template #trackImage="{ row }">
              <div @click="viewBigPic(row)" class="ui-images">
                <ui-image :src="!row.trackImage ? noImg : row.trackImage" />
              </div>
            </template>
          </ui-table>

          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  data() {
    return {
      visible: false,
      title: '查看全部轨迹',
      styles: {
        width: '9rem',
      },
      minusTable: 600,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage', tooltip: true, minWidth: 150 },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '档案照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
      ],
      tableData: [],
      loading: false,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        resultId: '',
        trackImage: '',
        indexId: '',
        taskId: '',
        params: {
          pageNum: 1,
          pageSize: 20,
        },
      },
      dataMsg: {},
    };
  },
  methods: {
    info(item) {
      this.searchData = {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
        resultId: '',
        trackImage: '',
        indexId: '',
        taskId: '',
        params: {
          pageNum: 1,
          pageSize: 20,
        },
      };
      this.dataMsg = item;
      this.getList();
      this.visible = true;
    },
    viewBigPic(item) {
      this.$emit('viewBigPicShow', item.trackLargeImage);
      // if (!item.trackLargeImage) {
      //   this.$Message.warning('大图URL缺失')
      //   return
      // }
      // this.imgList = [item.trackLargeImage]
      // this.bigPictureShow = true
    },
    getList() {
      this.searchData.resultId = this.dataMsg.resultId;
      this.searchData.trackImage = this.dataMsg.trackImage;
      this.searchData.indexId = this.dataMsg.indexId;
      this.searchData.taskId = this.dataMsg.taskId;
      this.$http.post(governanceevaluation.repeatList, this.searchData).then((res) => {
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
      });
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.searchData.params.pageNum = val;
      this.getList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.searchData.params.pageSize = val;
      this.getList();
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.all-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: @bg-blue-block;
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        display: flex;
        // background-color: #239df9;
        span:nth-child(1) {
          margin-right: 50px;
        }
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      height: 600px;
      overflow-y: auto;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      img {
        width: 56px;
        height: 56px;
      }
      @{_deep}.ivu-table-cell-slot {
        margin-top: 10px;
      }
    }
  }
  /deep/.base-search {
    margin: 15px 0 0;
  }
  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;
    .ui-image {
      min-height: 56px !important;
      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
