import global from '@/util/global';
import {
  qualifiedColorConfig,
  defaultIconStaticsList,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  ...defaultIconStaticsList,
  {
    name: '',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'start-end-num',
    label: '录像缺失时长',
    startKey: 'hiatusTimeBegin',
    endKey: 'hiatusTimeEnd',
  },
  {
    type: 'start-end-num',
    label: '当日录像缺失时长',
    startKey: 'hiatusTimeCurBegin',
    endKey: 'hiatusTimeCurEnd',
  },
];
export const tableColumns = [
  { type: 'index', width: 70, title: '序号', align: 'center' },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',

    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  { title: '组织机构', key: 'orgName', tooltip: true, minWidth: 120 },
  { title: '点位类型', key: 'sbdwlxText', tooltip: true, minWidth: 120 },
  { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
  { title: '存储类型', key: 'storageTypeName', tooltip: true, minWidth: 120 },
  { title: '录像缺失', slot: 'reason', align: 'center', tooltip: true, minWidth: 120 },
  { title: '当日录像缺失', key: 'hiatusTimeCurText', tooltip: true, minWidth: 120 },
  { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
  { title: '检测时间', key: 'startTime', tooltip: true, width: 200 },
  { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
  { title: '操作', slot: 'option', fixed: 'right', width: 100, align: 'center' },
];
