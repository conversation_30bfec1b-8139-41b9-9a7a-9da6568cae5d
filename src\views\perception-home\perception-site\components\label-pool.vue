<template>
  <div class="label-pool">
    <div class="label-pool-container">
      <div class="label-pool-head">
        <div class="head-left-line"></div>
        <div class="head-left-rect"></div>
        <div class="head-text">标签池</div>
        <div class="head-right-rect"></div>
        <div class="head-right-line"></div>
      </div>
      <div class="label-pool-wrap">
        <wordCloud-echart v-if="labelList.length" :label-list = "labelList"/>
      </div>
    </div>
  </div>
</template>
<script>
  import WordCloudEchart from './echarts/d3-cloud.vue'
  export default {
    components: {
      WordCloudEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelList: []
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelList = this.updataLabelList(this.statisticsList)
        },
        immediate: true
      }
    },
    mounted () {
      this.labelList = this.updataLabelList(this.statisticsList)
    },
    methods: {
      updataLabelList (list) {
        const labelList = []
        const documentWidth = document.body.clientWidth
        let fontValue = 16
        let paddingValue = [6, 20, 3, 20]
        if (documentWidth <1700 && documentWidth > 1500) {
          fontValue = 14
          paddingValue = [6, 16, 3, 16]
        } else if (documentWidth < 1500) {
          fontValue = 12
          paddingValue = [6, 14, 3, 14]
        }
        list.forEach((v, i) => {
          labelList.push({
            name: v.name.length > 8 ? v.name.substring(0, 8) + '...' : v.name,
            value: i,
            labelId: v.id,
            labelName: v.name,
            color: v.color
            // textStyle: {
            //   fontSize: fontValue,
            //   // lineHeight: 22,
            //   backgroundColor: this.$util.common.colorRgb(v.color, 0.15),
            //   borderColor: v.color,
            //   color: '#fff',
            //   padding: paddingValue
            // },
            // label: {
            //   formatter: function (params) {
            //     return `{leftOut|}{leftInside|}{name|${params.name.length > 8 ? params.name.substring(0, 8) + '...' : params.name}}{rightInside|}{rightOut|}`
            //   },
            //   rich: {
            //     name: {
            //       color: '#fff',
            //       backgroundColor: this.$util.common.colorRgb(v.color, 0.20),
            //       borderWidth: 0.5,
            //       borderColor: v.color,
            //       borderRadius: 2,
            //       padding: paddingValue,
            //       fontSize: fontValue,
            //       lineHeight: 20,
            //       margin: [i * 5, 0, 0, 0]
            //     },
            //     leftOut: {
            //       width: 1.5,
            //       height: 14,
            //       backgroundColor: v.color,
            //       padding: [0, 0, 0, 1.5]
            //     },
            //     leftInside: {
            //       height: 14,
            //       backgroundColor: v.color,
            //       padding: [0, 1.5, 0, -3]
            //     },
            //     rightInside: {
            //       height: 14,
            //       backgroundColor: v.color,
            //       padding: [0, -3, 0, 1.5]
            //     },
            //     rightOut: {
            //       width: 1.5,
            //       height: 14,
            //       backgroundColor: v.color,
            //       padding: [0, 1.5, 0, 0]
            //     }
            //   }
            // }
          })
        })
        return labelList
      }
    }
  }
</script>
<style lang="less" scoped>
  .label-pool {
    width: 100%;
    display: flex;
    flex: 1;
    padding-top: 24px;
    overflow: hidden;
    .label-pool-container {
      display: flex;
      flex: 1;
      flex-direction: column;
      background: url('../../../assets/img/home/<USER>') no-repeat left/contain,
                  url('../../../assets/img/home/<USER>') no-repeat bottom/contain,
                  url('../../../assets/img/home/<USER>') no-repeat right/contain;
      .label-pool-head {
        width: 100%;
        height: 30px;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        .head-left-line, .head-right-line {
          width: 200px;
          height: 2px;
          background: linear-gradient(270deg, rgba(47, 155, 209, 1), rgba(21, 98, 164, 0));
        }
        .head-right-line {
          transform: rotate(180deg);
        }
        .head-left-rect, .head-right-rect {
          width: 8px;
          height: 8px;
          background: #FFB261;
          margin: 0 36px 0 30px;
        }
        .head-right-rect {
          margin: 0 30px 0 36px;
        }
        .head-text {
          font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
          font-size: 24px;
          line-height: 30px;
          font-weight: bold;
          color: #fff;
          letter-spacing: 8px;
        }
      }
      .label-pool-wrap {
        display: flex;
        flex: 1;
        padding-bottom: 24px;
        align-items: center;
      }
    }
  }
</style>