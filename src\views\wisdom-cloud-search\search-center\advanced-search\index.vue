<template>
  <section class="my-container">
    <div class="result-content">
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
        v-if="!$route.query.noSearch"
      >
        <MenuItem
          v-for="(item, index) in menuList"
          :key="index"
          :name="item.name"
          v-permission="item.permission"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
        </MenuItem>
      </Menu>
      <keep-alive>
        <component
          :is="sectionName"
          ref="menuRef"
          @dataAboveMapHandler="dataAboveMapHandler"
        >
          <template #mutilAction>
            <!-- -->
            <Button
              @click="multiAnalysisHandler"
              size="small"
              style="float: right; margin-right: 10px"
            >
              多模态解析
            </Button>
          </template>
        </component>
      </keep-alive>
    </div>
  </section>
</template>
<script>
import searchPictures from "../components/search-pictures";
import policeServiceContent from "./pages/police-service-content.vue";
import faceContent from "./pages/face-content";
import faceContents from "./pages/face-contents";
import humanBodyContent from "./pages/human-body-content.vue";
import nonmotorVehicleContent from "./pages/nonmotor-vehicle-content.vue";
import vehicleContent from "./pages/vehicle-content";
import wifiContent from "./pages/wifi-content.vue";
import RFIDContent from "./pages/RFID-content.vue";
import electricContent from "./pages/electric-content.vue";
import deviceContent from "./pages/device-content.vue";
import gpsContent from "./pages/gps-content.vue";
import etcContent from "./pages/etc-content.vue";
import gaitContent from "./pages/gait-content.vue";
import { mapActions, mapGetters, mapMutations } from "vuex";

export default {
  name: "advancedSearch",
  components: {
    searchPictures,
    policeServiceContent, //警务模块
    faceContent:
      developmentEnvironment == "binhai" ? faceContent : faceContents, //人脸模块
    vehicleContent, //车辆模块
    humanBodyContent, //人体
    nonmotorVehicleContent, //非机动车
    wifiContent, //wifi模块
    RFIDContent, //rfid模块
    electricContent, //电围模块
    deviceContent, //设备模块
    gpsContent, //GPS
    etcContent, //etc
    gaitContent, //步态
  },
  data() {
    return {
      visible: false,
      sectionName: "",
      menuList: [
        {
          label: "警务",
          value: 1,
          iconName: "icon-anjian",
          name: "policeServiceContent",
          permission: ["search-police"],
        },
        {
          label: "人像",
          value: 2,
          iconName: "icon-renlian",
          name: "faceContent",
          permission: ["search-face"],
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicleContent",
          permission: ["search-vehicle"],
        },
        {
          label: "人体",
          value: 8,
          iconName: "icon-renti",
          name: "humanBodyContent",
          permission: ["search-humanBody"],
        },
        // TODO 原来这里value是8，和人体重复了，这个value好像也没啥用，用个10吧暂时
        {
          label: "步态",
          value: 10,
          iconName: "icon-a-lianhe322",
          name: "gaitContent",
          permission: ["search-gait"],
        },
        {
          label: "非机动车",
          value: 9,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicleContent",
          permission: ["search-nonmotorVehicle"],
        },
        {
          label: "MAC",
          value: 4,
          iconName: "icon-wifi",
          name: "wifiContent",
          permission: ["search-mac"],
        },
        {
          label: "RFID",
          value: 5,
          iconName: "icon-RFID",
          name: "RFIDContent",
          permission: ["search-rfid"],
        },
        {
          label: "电围",
          value: 6,
          iconName: "icon-ZM-dianwei",
          name: "electricContent",
          permission: ["search-electric"],
        },
        {
          label: "GPS",
          value: 7,
          iconName: "icon-gps",
          name: "gpsContent",
          permission: ["search-gps"],
        },
        {
          label: "ETC",
          value: 8,
          iconName: "icon-a-ETC1x",
          name: "etcContent",
          permission: ["search-etc"],
        },
        // { label: '设备', value: 7, iconName: 'icon-shebeizichan', name: 'deviceContent' }
      ],
    };
  },
  watch: {
    $route: {
      handler(to, from) {
        let { sectionName, deviceType } = to.query;
        let permissionList = this.menuList.filter((d) =>
          this.$_has(d.permission)
        );
        let hasSectionName = permissionList.some((d) => d.name === sectionName);
        this.sectionName = hasSectionName
          ? sectionName
          : permissionList.length > 0
          ? permissionList[0].name
          : "";
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      picData: "common/getWisdomCloudSearchData",
    }),
  },
  activated() {
    // 用于人像搜索条件显示
    this.$store.commit("common/setPageType", 1);
    this.setLayoutNoPadding(true);
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
  },
  created() {
    this.getDictData();
    this.setSum();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
      setSum: "countCoverage/setSum",
    }),
    ...mapMutations({
      setLayoutNoPadding: "admin/layout/setLayoutNoPadding",
    }),
    pictureSearchHandle() {
      this.visible = !this.visible;
    },
    selectItemHandle(sectionName) {
      this.sectionName = sectionName;
    },
    // 数据上图
    dataAboveMapHandler(mapData) {
      const { href } = this.$router.resolve({
        name: "map-track",
        query: {
          mapData: JSON.stringify(mapData),
        },
      });
      window.open(href, "_blank");
    },
    // 多模态文件解析
    multiAnalysisHandler() {
      const dataList = this.$refs.menuRef.getSelectDataList();
      if (dataList?.length > 0) {
        const imgeList = dataList.map((item) => item.sceneImg);
        window.sessionStorage.setItem(
          "fileAnalysisImageList",
          JSON.stringify(imgeList)
        );
        const { href } = this.$router.resolve({
          path: "/multimodal-analysis/file-analysis",
          query: {
            addTask: true,
          },
        });
        window.open(href, "_blank");
      } else {
        this.$Message.warning("请选择数据");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.my-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  /deep/ .search {
    margin-bottom: 0 !important;
  }
  /deep/ .table-container {
    margin-top: 10px !important;
  }
}
.top-operate-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
  .title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    position: relative;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #2c86f8;
      margin-right: 10px;
    }
  }
  .btn-return {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
    .iconfont {
      font-size: 16px;
      margin-right: 8px;
    }
  }
}
.result-content {
  background-color: #fff;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  border-radius: 4px;
  //   overflow: hidden;
  flex: 1;
  height: calc(~"100% - 34px");
  display: flex;
}
</style>
