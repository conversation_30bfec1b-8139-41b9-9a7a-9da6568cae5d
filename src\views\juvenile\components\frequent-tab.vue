<template>
  <div class="alarm">
    <div v-if="list.length > 0" class="my-swiper-container">
      <swiper
        ref="mySwiper"
        :options="swiperOption"
        class="my-swiper"
        id="frequent-swiper"
      >
        <template v-for="(item, index) in list">
          <swiper-slide :key="index">
            <div class="swiper-item" @click="() => handleDetailFn(item, index)">
              <FrequentAlarm :data="item"></FrequentAlarm>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="frequentLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="frequentRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail ref="videoDetail"></CaptureDetail>
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
import FrequentAlarm from "./collect/frequent-alarm.vue";
import CaptureDetail from "./detail/capture-detail.vue";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    FrequentAlarm,
    CaptureDetail,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      swiperOption: {
        slidesPerView: "3",
        // slidesPerGroup: 3,
        speed: 1000,
        navigation: {
          nextEl: "#frequentRight",
          prevEl: "#frequentLeft",
        },
        observer: true,
        observeParents: true,
      },
      total: 0,
      detailShow: false,
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      selectIdCard: "",
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    // 详情
    handleDetailFn(item, index) {
      this.selectIdCard = item.idCardNo;
      this.$refs.videoDetail.queryInfo(1, item.idCardNo);
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;

  span {
    color: #2c86f8;
  }
}
.alarm {
  width: 100%;
  height: 100%;
}
.my-swiper-container {
  padding: 0 30px;
  position: relative;
  height: 100%;
  .my-swiper {
    margin: auto;
    height: 100%;
    .swiper-item {
      width: 100%;
      height: 100%;
    }
  }
  /deep/ .swiper-container {
    overflow: unset;
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}

.swiper-button-next {
  right: 20px;
}
</style>
