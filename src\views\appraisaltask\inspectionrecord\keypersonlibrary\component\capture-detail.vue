<template>
  <ui-modal v-model="captureVisible" :title="title" :footerHide="true" :width="width">
    <Row>
      <Col span="4">
        <div class="contentLeft" v-if="personObj">
          <ui-image style="height: 100px" :src="personObj.identityPhoto" alt="" />
          <div>姓名：{{ personObj.name }}</div>
          <div>身份证号：{{ personObj.idCard }}</div>
          <div v-if="personObj.personType">
            <tags-more
              :personTypeList="personTypeList"
              :tagList="personObj.personType ? personObj.personType.split(',') : []"
              :defaultTags="10"
              placement="left-start"
              bgColor="#2D435F"
            ></tags-more>
            <!-- <ul>
              <li v-for="item in personObj.personType.split(',')" :key="item + 3">
                {{ item | filterType(personTypeList) }}
              </li>
            </ul> -->
          </div>
        </div>
      </Col>
      <Col span="20" style="overflow: hidden">
        <div class="content">
          <div class="topSum">
            <span>抓拍总量：{{ modular == 1 ? personObj.total : personObj.catchCount }}</span>
            <span style="margin-left: 60px">
              {{ modular == 1 ? '准确性存疑轨迹数量' : '上传超时轨迹数量' }}
              ：{{ modular == 1 ? personObj.abnormal : personObj.delayCount }}
            </span>
          </div>
          <Search
            v-if="captureVisible"
            ref="faceSearchRef"
            :modular="modular"
            :determineResult="true"
            @startSearch="startSearch"
          />
          <div>
            <div class="styleScroll6">
              <div class="carItem" v-for="(item, index) in tableData" :key="item.id">
                <div class="item">
                  <!-- <div class="num" v-if="item.similarity">{{item.similarity}}%</div> -->
                  <div class="num" v-if="item.similarity">
                    {{ similarityVal(item.similarity) }}
                  </div>
                  <div class="img" @click="viewBigPic(index, item.trackLargeImage)">
                    <ui-image :src="item.trackImage" />
                    <p
                      class="shadow-box"
                      style="z-index: 11"
                      title="查看检测结果"
                      v-if="item.synthesisResult == 0 || item.delayStatus == '2'"
                    >
                      <i
                        class="icon-font icon-yichang search-icon mr-xs base-text-color"
                        @click.stop="captureDetail(item)"
                      ></i>
                    </p>
                  </div>
                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.bigImageShotTime}`">
                      <i class="icon-font icon-shijian"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.shotTime }}</span>
                    </p>
                    <p :title="item.deviceName">
                      <i class="icon-font icon-dizhi"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis">{{
                        item.catchPlace ? item.catchPlace : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <ui-page
              class="page menu-content-background"
              :page-data="pageData"
              @changePage="changePage"
              @changePageSize="changePageSize"
            ></ui-page>
          </div>
        </div>
        <trajectory-modal ref="trajectoryModal" />
        <upload-modal ref="uploadModal" />
        <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
      </Col>
    </Row>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/inspectionrecord';
export default {
  name: 'captureDetail',
  props: ['taskObj', 'orgCode'],
  data() {
    return {
      width: '89%',
      title: '抓拍详情',
      captureVisible: false,
      tableData: [],
      searchData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageNumber: 1,
        pageSize: 20,
      },
      modular: 1, // 1: 轨迹准确性检测     2: 上传及时性检测
      bigPictureShow: false,
      imgList: [],
      personObj: {}, // 人员信息对象,
      loading: false,
    };
  },
  async created() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    show(type, item) {
      this.modular = type;
      this.personObj = item;
      this.captureVisible = true;
      this.init();
    },
    init() {
      this.loading = true;
      var url = '';
      var param = {
        idCard: this.personObj.idCard,
        batchId: this.personObj.batchId,
        taskIndexId: this.personObj.taskIndexId,
        orgCode: this.orgCode,
        ...this.searchData,
        params: this.pageData,
      };

      if (this.modular == 1) {
        url = api.pageListGroupDetailc;
      } else {
        url = api.queryFocusTrackRealDetailListPage;
      }

      param.delayStatus = param.synthesisResult;
      if (param.synthesisResult == 0) {
        param.delayStatus = 2;
      }

      param.beginTime = param.startTime;
      try {
        this.$http.post(url, param).then((res) => {
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    viewBigPic(index, item) {
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 分页
    changePage(val) {
      // this.searchData.params.pageNumber = val
      this.pageData.pageNum = val;
      this.pageData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      // this.searchRefalsh()
      // this.searchData.params.pageSize = val
      this.pageData.pageSize = val;
      this.init();
    },
    captureDetail(item) {
      if (this.modular == 1) {
        this.$refs.trajectoryModal.show(this.personObj, item);
      } else {
        this.$refs.uploadModal.show(item);
      }
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData.params = { pageNumber: 1, pageSize: 20 };
    },
    // 检索
    startSearch(searchData) {
      this.searchData = searchData;
      // this.searchData = Object.assign(this.searchData, searchData)
      this.init();
    },
    similarityVal(val) {
      return (val * 100).toFixed(2) + '%';
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.captureVisible = val;
    },
  },
  components: {
    TagsMore: require('../../components/tags-more').default,
    Search: require('./searchList').default,
    trajectoryModal: require('./trajectory-modal').default,
    uploadModal: require('./upload-modal').default,
    uiImage: require('@/components//ui-image').default,
  },
};
</script>

<style lang="less" scoped>
.contentLeft {
  color: #fff;
  padding: 0 30px;
  div {
    margin-top: 12px;
  }

  img {
    width: 100%;
  }

  ul {
    li {
      float: left;
      padding: 6px 10px;
      background: #1a447b;
      border-radius: 4px;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
}
.content {
  height: 600px;
  color: #fff;

  .topSum {
    height: 36px;
  }
}

/deep/ .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}

.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  display: inline-block;
  // max-width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: #0f2f59;
    .num {
      position: absolute;
      right: 0;
      z-index: 100;
      padding: 10px;
      border-radius: 5px;
      background: rgba(42, 95, 175, 0.6);
    }
    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
    }
  }
}

.base-search {
  padding-bottom: 0;
}

/deep/ .ivu-row {
  align-content: start;
}
.styleScroll6 {
  position: relative;
  width: 100%;
  height: 660px;
  overflow-y: auto;
}
</style>
