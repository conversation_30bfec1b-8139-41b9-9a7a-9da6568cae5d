<!--
    * @FileDescription: 落脚地分析
    * @Author: H
    * @Date: 2023/01/13
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="foothold">
        <!-- 地图 -->
        <mapCustom ref="mapBase"
            mapType="foothold"
            :currentClickIndex.sync="currentClickIndex"
            @chooseMapItem="chooseMapItem"
            :castPoints='castPoints' 
            :sectionName="sectionName"/>
        <!-- 左面信息展示框 -->
        <left-box
            ref="leftbox"
            @reset="handleReset"  
            @searchAnalyse="handleSearchAnalyse"
            @cutAnalyse="handleCutAnalyse"></left-box>
        <rightBox ref='rightBox' v-show="rightShowList" @details="handleDetails" @cancel="handleCancel"></rightBox>
        <detailsBox ref='detailsBox' v-if="detailsShow" @goback="handleGoback"></detailsBox>
    </div>
</template>

<script>
import rightBox from './components/rightBox.vue';
import leftBox from './components/leftBox.vue';
import detailsBox from './components/details-box.vue';
import mapCustom from '../../components/map/index.vue';
import { mapMutations } from 'vuex';
export default {
    name: 'foothold',
    components:{
        leftBox,
        rightBox,
        detailsBox,
        mapCustom
    },
    data () {
        return {
            rightShowList: false,
            detailsShow: false,
            castPoints: [],
            currentClickIndex: -1,
            sectionName:''
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    activated() {
        this.setLayoutNoPadding(true)
    },
    deactivated(){
        this.setLayoutNoPadding(false)
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        async handleSearchAnalyse(item, tabIndex) {
            this.rightShowList = true;
            this.detailsShow = false;
            this.$nextTick(() => {
                this.$refs.rightBox.init(item);
            })
            let res = {};
            if(tabIndex.tab == 0 ) {
                res = [
                    {
                        'archiveNo': "371002200108212050",
                        'xm': "钱组上",
                        'gmsfhm' : '',
                        'geoPoint': {lat: 31.970031425117856, lon: 118.74651236859219},
                        'sceneImg': require('@/assets/img/mock/face.png'),
                        'traitImg': require('@/assets/img/mock/face.png'),
                        'ageName': '青年',
                        'eyeglass': '',
                        'isCap': '',
                        'mask': '',
                        'absTime': '',
                        'deviceName': '',
                        'deviceId': '12345',
                        'id': 'q123'
                    },
                    {
                        'archiveNo': "150825200208191257",
                        'xm': "陶昊名",
                        'gmsfhm' : '',
                        'geoPoint': {lat: 31.975021, lon: 118.758414},
                        'sceneImg': require('@/assets/img/mock/face.png'),
                        'traitImg': require('@/assets/img/mock/face.png'),
                        'ageName': '青年',
                        'eyeglass': '',
                        'isCap': '',
                        'mask': '',
                        'absTime': '',
                        'deviceName': '',
                        'deviceId': '34567',
                        'id': 'q234'
                    },
                ];
            } else { 
                res = [
                    {
                        'archiveNo': "371002200108212050",
                        'xm': "钱组上",
                        'gmsfhm' : '',
                        'geoPoint': {lat: 31.970031425117856, lon: 118.74651236859219},
                        'sceneImg': require('@/assets/img/mock/vehicle.png'),
                        'traitImg': require('@/assets/img/mock/vehicle.png'),
                        'vehicleSpeed': '100km/h',
                        'absTime': '',
                        'moveDirection': '',
                        'deviceName': '',
                        'deviceId': '12345',
                        'id': 'q123'
                    },
                    {
                        'archiveNo': "150825200208191257",
                        'xm': "陶昊名",
                        'gmsfhm' : '',
                        'geoPoint': {lat: 31.975021, lon: 118.758414},
                        'sceneImg': require('@/assets/img/mock/vehicle.png'),
                        'traitImg': require('@/assets/img/mock/vehicle.png'),
                        'vehicleSpeed': '100km/h',
                        'absTime': '',
                        'moveDirection': '',
                        'deviceName': '',
                        'deviceId': '34567',
                        'id': 'q234'
                    },
                ];
            }
            this.castPoints = res;
            tabIndex.tab == 0 ? this.sectionName = 'faceMap' : this.sectionName = 'vehicleMap';
        },
        // 落脚点详情
        handleDetails(item) {
            this.detailsShow = true;
            this.rightShowList = false;
            this.$nextTick(() => {
                this.$refs.detailsBox.init(item);
            })
        },
        // 返回落脚点
        handleGoback() {
            this.detailsShow = false;
            this.rightShowList = true;
        },
        // 落脚点隐藏
        handleCancel() {
            this.rightShowList = false;
        },
        handleReset() {
            this.rightShowList = false;
            this.detailsShow = false;
            this.$refs.mapBase.resetPointMarker()
        },
        handleCutAnalyse() {
            this.handleReset();
        },
        // 左侧选中项 点击普通搜索列表
        chooseMapItem (index, tab = '') {
            this.currentClickIndex = index;
        },
    }
}
</script>

<style lang='less' scoped>
.foothold{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
