<template>
  <div>
    <ui-modal ref="modal" :title="title" :footerHide="true" v-model="modalvisible" :width="width">
      <ui-table class="ui-table" :table-columns="tableColumns" :table-data="tableData" :loading="loading"> </ui-table>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/inspectionrecord';
export default {
  name: 'yichang',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      width: 680,
      title: '车辆属性判定',
      modalvisible: false,
      loading: false,
      algorithm: {},
      oldResuit: {},
      index: null,
      tableColumns: [],
      tableData: [],
    };
  },
  created() {},
  methods: {
    async init(id, i, tableColumns) {
      this.index = i;
      this.modalvisible = true;
      this.tableData = [];
      try {
        this.loading = true;
        let res = await this.$http.get(api.vehicleDetail + id);
        let { details, ...rest } = res.data.data || {};
        this.tableData = [{ ...rest, algorithmTitle: '原始值' }, ...details, { ...rest, algorithmTitle: '结论' }];
        this.tableColumns = tableColumns(this.tableData);
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .red {
  color: #e44f22;
}
@{_deep} .green {
  color: green;
}
@{_deep} .ui-table {
  height: 250px !important;
}
</style>
