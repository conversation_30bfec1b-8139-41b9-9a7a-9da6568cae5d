<template>
  <!-- 视频图像设备位置类型完整率 -->
  <div class="video-image-device" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true" :isIconBg="true"> </statistics>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="">
          <i class="icon-font icon-weizhileixingshangbaoqingkuangtongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-xs">位置类型上报情况统计</span>
        </div>
        <div class="export">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="list navBarWrap">
        <ui-table
          :maxHeight="contentClientHeight"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
        >
          <!-- <template slot="actualReportCount" slot-scope="{ row }">
            <span @click="showActualt" class="underLine video-green">
              {{ row.actualReportCount }}
            </span>
          </template> -->
          <template slot="notReportCount" slot-scope="{ row }">
            <span @click="showReport(row)" class="underLine video-red">
              {{ row.notReportCount }}
            </span>
          </template>
        </ui-table>
        <loading v-if="loading"></loading>
      </div>
    </div>
    <video-unreported
      v-model="videoUnreported"
      v-if="videoUnreported"
      :tag="'BASIC_EMPHASIS_LOCATION_NOT'"
      :params-data="paramsList"
      :report-list="reportList"
      :title="unreportedTitle"
    ></video-unreported>
    <video-table
      ref="videoTable"
      v-model="videoTable"
      v-if="videoTable"
      :tag="'BASIC_EMPHASIS_LOCATION_REPORT'"
      :params-data="paramsList"
    ></video-table>
  </div>
</template>

<style lang="less" scoped>
.video-image-device {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .navBarWrap {
    position: sticky !important;
    top: 100px;
    width: 100%;
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 540px) !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: calc(100% - 340px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 15px 13px;

      .statistics-ul {
        width: calc(100% - 340px);
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        li {
          position: relative;
        }
        .icon-budabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
        .icon-dabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
      }
    }

    .information-ranking {
      width: 340px;
      background: var(--bg-sub-content);
      height: 100%;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .information-main {
    position: relative;
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 15px 0;
      //margin: 0 20px 15px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      //.no-data {
      //  position: absolute;
      //  top: 50%;
      //  left: 50%;
      //}
      .underLine {
        cursor: pointer;
        text-decoration: underline !important;
      }
      .video-red {
        color: #bc3c19;
      }
      .video-green {
        color: var(--color-primary);
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'video-image-device',
  data() {
    return {
      videoUnreported: false,
      unreportedTitle: '',
      videoTable: false,
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankLoading: false,
      taskType: '',
      statisticsList: [
        {
          name: '应上报位置类型',
          value: 0,
          icon: 'icon-yingshangbaoweizhileixing',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          divBg: 'div-bg1',
          textColor: 'color1',
          key: 'belShouldReportCount',
          type: 'number',
        },
        {
          name: '实际上报位置类型',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang1',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          divBg: 'div-bg2',
          textColor: 'color2',
          key: 'belActualReportCount',
          type: 'number',
        },
        {
          name: '未上报位置类型',
          value: 0,
          icon: 'icon-weishangbaoweizhileixing',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          textColor: 'color4',
          key: 'belNotReportCount',
          type: 'number',
        },
        {
          name: '位置类型完整率',
          value: 0,
          icon: 'icon-weizhileixingwanzhengshuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          textColor: 'color3',
          type: 'percentage',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '行政区划', key: 'regionName' },
        { title: '应上报位置类型字典数', key: 'shouldReportCount' },
        { title: '实际上报位置类型字典数', key: 'actualReportCount' },
        {
          title: '未上报位置类型字典数',
          key: 'notReportCount',
          slot: 'notReportCount',
        },
        { title: '全量目录完整率', key: 'resultValue' },
      ],
      tableData: [],
      searchData: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      reasonLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      errorMessages: [],
      unreportedList: [],
      reportList: {},
      contentClientHeight: 0,
    };
  },

  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 40 * proportion : 0;
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },

    async getTableData() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        this.tableData = datas.emphasisLocationReport;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    showReport(row) {
      this.reportList = row;
      this.videoUnreported = true;
    },
    showActualt() {
      this.videoTable = true;
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
    videoTitle: '',
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getTableData(); //表格
        }
      },
      deep: true,
      immediate: true,
    },
    videoTitle: {
      handler(val) {
        if (val) {
          this.unreportedTitle = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    videoUnreported: require('@/views/governanceevaluation/evaluationoverview/components/basics/video-unreported.vue')
      .default,
    videoTable: require('@/views/governanceevaluation/evaluationoverview/components/basics/video-table.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    lineTitle: require('@/views/governanceevaluation/evaluationoverview/components/basics/line-title.vue').default,
    countTo: require('vue-count-to').default,
  },
};
</script>
