.warpper-box{
    padding: 10px;
    width: 360px;
    height: calc(~'100% - 56px');
    overflow: hidden;
    overflow-y: auto;
}
.btn-list {
    margin: 10px;
    display: flex;
    justify-content: space-between;
  }
// ----------------------------------------列表部分 start--------------------------------
.box-item{
    height: 123px;
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    cursor: pointer;
    padding: 10px 0;
    .header{
        display: flex;
        justify-content: space-between;
        // height: 42px;
        align-items: center;
        padding-left: 10px;
        &-left {
            display: flex;
            align-items: center;
            line-height: 42px;
            .header-deviceid {
                color: #f29f4c !important;
                font-size: 14px !important;
                vertical-align: center;
                font-weight: 700;
            }
            .serialNumber {
                position: relative;
                display: inline-block;
                width: 32px;
                height: 32px;
                margin-right: 6px;
                color: black;
                background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
                > span {
                    position: absolute;
                    top: -10px;
                    width: 32px;
                    font-size: 12px;
                    color: #ea4a36;
                    text-align: center;
                }
            }
            .activeNumber {
                background: url('~@/assets/img/map/trajectory-blue.png') no-repeat !important;
                > span {
                    color: #2c86f8 !important;
                }
            }
            .similarity {
                display: inline-block;
                // width: 41px;
                height: 22px;
                padding: 0 6px;
                line-height: 22px;
                background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
                border-radius: 4px;
                color: #ffffff;
                font-size: 12px;
                text-align: center;
            }
        }
        &-name {
            width: 300px;
            font-size: 14px;
            font-weight: bold;
            color: rgba(0,0,0,0.9);
            // overflow: hidden;
            // text-overflow: ellipsis;
        }
    }
    .content {
        display: flex;
        padding: 0 10px;
        margin-top: 9px;
        &-left {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 60px;
            height: 60px;
            border: 1px solid #d3d7de;
            > img {
                max-width: 100%;
                max-height: 100%;
            }
            .iconfont {
                font-size: 24px;
            }
        }
        &-right {
            margin-left: 10px;
            display: flex;
            flex-direction: column;
            width: 230px;
            .iconfont {
                margin-right: 10px;
            }
            .score{
                color: #2C86F8;
                font-weight: bold;
            }
        }
    }
}
.active {
    background: rgba(44, 134, 248, 0.1);
}
// --------各页面单独start---------

// --------各页面单独end---------
// ----------------------------------------列表部分 end--------------------------------