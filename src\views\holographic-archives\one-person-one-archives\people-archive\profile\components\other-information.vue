<template>
  <ui-card :title="title" class="other-information">
    <div class="other-information-content">
      <Tabs
        type="card"
        :animated="false"
        class="btn-tabs"
        @on-click="tabsClick"
      >
        <TabPane label="户口变动记录" name="accountChangeSearchData">
          <ui-table
            ref="accountChangeTable"
            :columns="accountChangeColumns"
            :data="accountChangeTableData"
            :loading="accountChangeLoading"
          >
            <template #accountType="{ row }">
              {{
                row.accountType | commonFiltering(householdRegistrationTypeList)
              }}
            </template>
            <template #qyldyydm="{ row }">
              {{ row.qyldyydm | commonFiltering(migrationReasonList) }}
            </template>
          </ui-table>
          <ui-page
            :current="pageList.pageNumber"
            :page-size="pageList.pageSize"
            :total="accountChangeTotal"
            @pageChange="handlePageChange($event, 0)"
            @pageSizeChange="handlePageSizeChange($event, 0)"
          >
          </ui-page>
        </TabPane>
        <TabPane label="出入境记录" name="entryExitSearchData">
          <ui-table
            ref="entryExitTable"
            :columns="entryExitColumns"
            :data="entryExitTableData"
            :loading="entryExitLoading"
          ></ui-table>
          <ui-page
            :current="pageList.pageNumber"
            :page-size="pageList.pageSize"
            :total="entryExitTotal"
            @pageChange="handlePageChange($event, 1)"
            @pageSizeChange="handlePageSizeChange($event, 1)"
          ></ui-page>
        </TabPane>
        <TabPane label="工作经历" name="workExperienceSearchData">
          <ui-table
            ref="workExperienceTable"
            :columns="workExperienceColumns"
            :data="workExperienceTableData"
            :loading="workExperienceLoading"
          ></ui-table>
          <ui-page
            :current="pageList.pageNumber"
            :page-size="pageList.pageSize"
            :total="workExperienceTotal"
            @pageChange="handlePageChange($event, 2)"
            @pageSizeChange="handlePageSizeChange($event, 2)"
          ></ui-page>
        </TabPane>
      </Tabs>
    </div>
  </ui-card>
</template>
<script>
import { mapGetters } from "vuex";
import {
  getAccountChangePageList,
  getImmigrationRecordsPageList,
  getWorkExperiencePageList,
} from "@/api/realNameFile";
import uiBtnTip from "../../../../../../components/ui-btn-tip.vue";
export default {
  components: { uiBtnTip },
  props: {
    title: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      archiveNo: "",
      accountChangeLoading: false,
      accountChangeSearchData: {
        isRequest: false,
      },
      accountChangeTotal: 0,
      accountChangeColumns: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "户口类型", slot: "accountType" },
        { title: "户号", key: "hh" },
        { title: "迁移时间", key: "blsj", tooltip: true },
        { title: "迁移原因", slot: "qyldyydm", tooltip: true },
        { title: "迁入/迁出地址", key: "qcd", tooltip: true },
      ],
      accountChangeTableData: [],
      entryExitLoading: false,
      entryExitSearchData: {
        isRequest: false,
      },
      entryExitTotal: 0,
      entryExitColumns: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "护照号", key: "qzhm" },
        { title: "出入境类型", key: "crPdbz" },
        { title: "出入境时间", key: "crsj" },
      ],
      entryExitTableData: [],
      workExperienceLoading: false,
      workExperienceSearchData: {
        isRequest: false,
      },
      workExperienceTotal: 0,
      workExperienceColumns: [
        { type: "index", title: "序号" },
        { title: "工作单位", key: "dwmc", tooltip: true },
        { title: "入职时间", key: "dbdwrq" },
      ],
      workExperienceTableData: [],
      pageList: {
        pageNumber: 1,
        pageSize: 10,
      },
    };
  },
  computed: {
    ...mapGetters({
      householdRegistrationTypeList:
        "dictionary/getHouseholdRegistrationTypeList", //户口类型
      migrationReasonList: "dictionary/getMigrationReasonList", //迁移类型
    }),
  },
  created() {
    let { archiveNo } = this.$route.query;
    this.archiveNo = archiveNo;
    this.accountChangeList();
  },
  methods: {
    tabsClick(name) {
      if (!this[name].isRequest) {
        this.pageList = {
          pageNumber: 1,
          pageSize: 10,
        };
        if (name === "accountChangeSearchData") this.accountChangeList();
        else if (name === "entryExitSearchData") this.entryExitList();
        else this.workExperienceList();
      }
    },
    accountChangeList() {
      // 户口变动记录
      this.accountChangeLoading = true;
      getAccountChangePageList({
        archiveNo: this.archiveNo,
        notSearchTotal: true,
        ...this.pageList,
        searchValue: "",
      })
        .then((res) => {
          let { total, entities } = res.data;
          this.accountChangeTotal = total;
          this.accountChangeTableData = entities;
        })
        .catch(() => {})
        .finally(() => {
          this.accountChangeLoading = false;
          this.accountChangeSearchData.isRequest = true;
        });
    },
    entryExitList() {
      // 出入境记录
      this.entryExitLoading = true;
      getImmigrationRecordsPageList({
        archiveNo: this.archiveNo,
        notSearchTotal: true,
        ...this.pageList,
        searchValue: "",
      })
        .then((res) => {
          let { total, entities } = res.data;
          this.entryExitTotal = total;
          this.entryExitTableData = entities;
        })
        .catch(() => {})
        .finally(() => {
          this.entryExitLoading = false;
          this.entryExitSearchData.isRequest = true;
        });
    },
    workExperienceList() {
      // 工作经历
      this.workExperienceLoading = true;
      getWorkExperiencePageList({
        archiveNo: this.archiveNo,
        notSearchTotal: true,
        ...this.pageList,
        searchValue: "",
      })
        .then((res) => {
          let { total, entities } = res.data;
          this.workExperienceTotal = total;
          this.workExperienceTableData = entities;
        })
        .catch(() => {})
        .finally(() => {
          this.workExperienceLoading = false;
          this.workExperienceSearchData.isRequest = true;
        });
    },
    // 页面
    handlePageChange(number, type) {
      this.pageList.pageNumber = number;
      type == 0
        ? this.accountChangeList()
        : type == 1
        ? this.entryExitList()
        : this.workExperienceList();
    },
    // 条数
    handlePageSizeChange(size, type) {
      this.pageList.pageSize = size;
      this.pageList.pageNumber = 1;
      type == 0
        ? this.accountChangeList()
        : type == 1
        ? this.entryExitList()
        : this.workExperienceList();
    },
  },
};
</script>
<style lang="less" scoped>
.other-information {
  /deep/ .card-content {
    padding-bottom: 0 !important;
  }
  /deep/ .ui-table {
    height: 296px;
  }
  .other-information-content {
    width: 100%;
  }
}
</style>
