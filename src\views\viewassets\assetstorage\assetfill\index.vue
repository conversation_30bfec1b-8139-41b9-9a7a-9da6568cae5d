<template>
  <div class="assetfill auto-fill">
    <search-module
      ref="searchModule"
      :active="active"
      :device-type-list="deviceTypeList"
      :device-status-list="deviceStatusList"
      :statistic="statistic"
      @search="search"
      @reset="reset"
      @changeType="changeType"
      @changeStatus="changeStatus"
      @addDevice="addDevice"
      @paramsConfig="paramsConfig"
      @exportModule="exportModule"
      @dealError="dealError"
    >
      <template>
        <component
          :is="searchComponent"
          :sbcjqy-list="checkedTreeData"
          @clickArea="clickArea"
          ref="searchComponent"
        ></component>
      </template>
    </search-module>
    <table-module
      ref="tableModule"
      :active="active"
      :search-params="searchData"
      @exportDevice="exportModule"
      @synchroResultModalShow="synchroResultModalShow"
      @edit="edit"
      @differenceDetails="differenceDetails"
      @abnormal="abnormal"
      @auditRecords="auditRecords"
      @operationRecord="operationRecord"
      @batchEdit="batchEdit"
    ></table-module>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checked-tree-data-list="checkedTreeData"
    ></area-select>
    <device-detail
      v-model="deviceDetailShow"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-url="viewUrl"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      :save-url="saveUrl"
      :save-method="saveMethod"
      :check-fun="checkFun"
      :check-data="checkData"
      :has-multiView="false"
      @update="search"
    >
    </device-detail>
    <difference
      v-model="differenceShow"
      :field-list="differenceList"
      :sign="sign"
      :check-fun="checkFun"
      :save-fun="saveDifference"
    >
      <template #filter>
        <RadioGroup class="fl" v-model="sign">
          <Radio label="differ">标记差异字段</Radio>
          <Radio class="ml-lg" label="unqualified">标记不合格字段</Radio>
        </RadioGroup>
        <Checkbox v-show="sign === 'differ'" class="fr" v-model="onlyDiff" @on-change="changeOnlyDiff">
          只显示差异字段
        </Checkbox>
        <Checkbox
          v-show="sign === 'unqualified'"
          class="fr"
          v-model="onlyUnqualified"
          @on-change="changeOnlyUnqualified"
        >
          只显示不合格字段
        </Checkbox>
      </template>
    </difference>
    <upload-error
      v-model="uploadErrorVisible"
      :error-data="errorData"
      :error-columns="errorColumns"
      :footer-hide="footerHide"
      @cancel="cancelCompulsory"
    >
      <template #footer>
        <Button @click="cancelCompulsory" class="mr-lg"> 取 消</Button>
        <Button type="primary" :loading="compulsoryLoading" @click="compulsoryStorage">
          {{ deviceDetailAction === 'add' ? '强制入库' : '强制保存' }}
        </Button>
      </template>
    </upload-error>
    <info-synchro-result ref="InfoSynchroResult"></info-synchro-result>
    <config v-model="configShow" :all-property-list="allPropertyList"></config>
    <operation-record
      v-model="recordShow"
      :title="recordTitle"
      :record-type="recordType"
      :table-columns="recordColumns"
      :table-data="recordData"
      :fetch-method="recordFetch"
    ></operation-record>
    <customize-filter
      v-model="customFilter"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :left-disabled="leftDisabled"
      :right-disabled="rightDisabled"
    >
      <template #footer="{ tagList }">
        <Tooltip content="只会导出选中字段的模板，不会导出设备数据" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'module')" :loading="exportModuleLoading"
            >{{ exportModuleLoading ? '下载中' : '模板导出' }}
          </Button>
        </Tooltip>
        <Tooltip class="ml-sm" content="会导出选中字段以及列表中搜索出的设备" placement="top" max-width="350">
          <Button type="primary" class="plr-30" @click="exportExcel(tagList, 'device')" :loading="exportDataLoading"
            >{{ exportDataLoading ? '下载中' : '数据导出' }}
          </Button>
        </Tooltip>
      </template>
    </customize-filter>
    <view-detection-field v-model="fieldShow" :view-data="fieldData"></view-detection-field>
    <batch-edit
      v-model="batchEditShow"
      :batch-data="batchEditData"
      :search-params="batchParams"
      @init="init"
    ></batch-edit>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import downLoadTips from '@/mixins/download-tips';
const checkErrorColumns = [
  {
    title: '序号',
    type: 'index',
    width: 70,
    align: 'center',
  },
  {
    title: '不合格原因',
    key: 'errorMessage',
    tooltip: true,
  },
  {
    title: '检测规则名称',
    key: 'checkRuleName',
    tooltip: true,
  },
  {
    title: '不合格字段',
    key: 'propertyName',
    tooltip: true,
  },
  { title: '异常类型', key: 'errorType', tooltip: true },
  {
    title: '实际结果',
    key: 'propertyValue',
    tooltip: true,
  },
];
export default {
  name: 'assetfill',
  props: {},
  mixins: [downLoadTips],
  data() {
    return {
      active: 'pendingStorage',
      deviceTypeList: [
        {
          label: '待入库设备',
          value: 'pendingStorage',
        },
        {
          label: '已入库设备',
          value: 'inStock',
        },
      ],
      deviceStatusList: [
        {
          label: '全部设备',
          value: '0',
        },
        {
          label: '新增设备',
          value: '1',
        },
        {
          label: '修订设备',
          value: '2',
        },
        {
          label: '根据流水自动新增',
          value: '3',
        },
        {
          label: '根据流水自动修改功能类型',
          value: '4',
        },
      ],
      statistic: {
        qualified: 0,
        unqualified: 0,
      },
      searchComponent: 'pendingStorage',
      searchData: {},
      type: '0',
      areaSelectModalVisible: false,
      checkedTreeData: [],
      deviceDetailTitle: '修改入库数据',
      deviceDetailAction: 'edit',
      deviceDetailShow: false,
      viewDeviceId: 0,
      deviceCode: '',
      viewUrl: null,
      saveUrl: null,
      saveMethod: 'post',
      deviceUnqualified: null,
      // 下载模板、导出
      customFilter: false,
      exportModuleLoading: false,
      exportDataLoading: false,
      exportUrl: equipmentassets.exportFill,
      checkedData: [],
      customizeAction: {
        title: '选择导出设备包含字段',
        leftContent: '设备所有字段',
        rightContent: '已选择字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: `3.125rem`,
      },
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      leftDisabled: () => {},
      rightDisabled: () => {},
      // 差异
      differenceShow: false,
      differData: [],
      differenceList: [],
      sign: 'differ',
      onlyDiff: true,
      onlyUnqualified: false,
      // 上传检测
      uploadErrorVisible: false,
      footerHide: false,
      checkData: null,
      errorData: [],
      errorColumns: [],
      compulsoryLoading: false,
      compulsoryResolve: null,
      compulsoryReject: null,
      // 配置
      configShow: false,
      // 记录
      recordShow: false,
      recordTitle: '审核记录',
      recordType: 'audit-records',
      recordData: [],
      recordColumns: [],
      recordFetch: null,
      // 异常原因
      fieldShow: false,
      fieldData: {},
      // 批量修改
      batchEditShow: false,
      batchEditData: [],
      batchParams: {},
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.routerEnterParams(to.query);
    });
  },
  created() {
    this.getPropertyList();
  },
  mounted() {},
  methods: {
    changeType(item) {
      this.active = item.value;
      this.searchComponent = this.active;
      if (this.active === 'pendingStorage') {
        this.exportUrl = equipmentassets.exportFill;
      } else if (this.active === 'inStock') {
        this.exportUrl = equipmentassets.exportDevice;
      }
    },
    changeStatus(index) {
      this.type = this.deviceStatusList[index].value;
    },
    async getStatistic() {
      try {
        if (this.active === 'pendingStorage') {
          const res = await this.$http.post(equipmentassets.countFillInNum, this.searchData);
          this.statistic = res.data.data;
        } else {
          const res = await this.$http.post(equipmentassets.statisticsDevice, this.searchData);
          this.statistic = res.data.data;
        }
      } catch (err) {
        console.log(err);
      }
    },
    init() {
      this.$refs.tableModule.init();
    },
    search() {
      if (this.active === 'pendingStorage') {
        this.searchData = Object.assign(
          {
            type: this.type,
          },
          this.$refs.searchComponent.searchData,
          this.$refs.searchModule.searchData,
        );
      } else {
        this.searchData = Object.assign({}, this.$refs.searchComponent.searchData, this.$refs.searchModule.searchData);
        this.searchData.sbcjqyList = this.checkedTreeData;
      }
      this.getStatistic();
    },
    reset() {
      this.$refs.tableModule.reset();
      this.$refs.searchComponent.reset();
      this.checkedTreeData = [];
      this.search();
    },
    confirmArea(data) {
      this.checkedTreeData = data;
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
      } catch (e) {
        console.log(e);
      }
    },
    addDevice() {
      this.deviceDetailTitle = '新增入库设备';
      this.deviceDetailAction = 'add';
      this.deviceDetailShow = true;
      this.saveUrl = equipmentassets.fillAdd;
    },
    // 检测方法
    checkFun(deviceData) {
      this.checkData = null;
      return new Promise(async (resolve, reject) => {
        try {
          this.compulsoryResolve = resolve;
          this.compulsoryReject = reject;
          if (this.deviceDetailAction === 'add') {
            await this.fillAddCheck(deviceData);
          } else {
            await this.fillUploadCheck(deviceData);
          }
        } catch (err) {
          console.log(err);
          reject(false);
        }
      });
    },
    // 差异详情保存
    async saveDifference(deviceData) {
      try {
        const res = await this.$http.post(
          equipmentassets.updateFill,
          Object.assign(
            {
              accuracyVo: this.checkData,
              id: this.viewDeviceId,
            },
            deviceData,
          ),
        );
        this.$Message.success(res.data.msg);
        this.differenceShow = false;
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    // 强制入库
    compulsoryStorage() {
      this.uploadErrorVisible = false;
      this.compulsoryResolve(true);
    },
    cancelCompulsory() {
      this.uploadErrorVisible = false;
      this.compulsoryReject(false);
    },
    // 原始库检测
    async fillAddCheck(deviceData) {
      try {
        await this.$http.post(equipmentassets.fillAddCheck, deviceData);
        this.compulsoryResolve(true);
      } catch (err) {
        console.log(err, 'err');
        switch (err.data.code) {
          // 检测不合格
          case 9006400:
            this.checkError(err);
            break;
          // 参数错误
          case 9006401:
            this.compulsoryReject(false);
            break;
          default:
            this.compulsoryReject(false);
            break;
        }
      }
    },
    // 原始库更新检测
    async fillUploadCheck(deviceData) {
      // 如果为已入库设备，则需传入deviceInfoId并且将id为空
      let params = {};
      if (this.active === 'inStock') {
        params = {
          id: null,
          deviceInfoId: deviceData.id,
        };
      } else {
        params = {
          id: this.viewDeviceId,
        };
      }
      try {
        // step 调用updateCheck使用，1- 校验 设备编码 2-校验检测规则。修改原始库设备信息时，两次调用updateCheck接口，第一次传1 第二次传2
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign({ step: 1 }, deviceData, params));
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign({ step: 2 }, deviceData, params));
        this.compulsoryResolve(true);
      } catch (err) {
        console.log(err, 'err');
        switch (err.data.code) {
          // 检测出错误
          case 9006400:
            this.checkError(err);
            break;
          // 库中已有该数据
          case 9006402:
            this.$UiConfirm({
              content: err.data.msg,
              title: '警告',
            })
              .then(async () => {
                try {
                  await this.$http.post(
                    equipmentassets.updateCheckFill,
                    Object.assign({ step: 2 }, deviceData, params),
                  );
                  this.compulsoryResolve(true);
                } catch (err) {
                  console.log(err);
                  switch (err.data.code) {
                    case 9006400:
                      this.checkError(err);
                      break;
                    default:
                      this.compulsoryReject(false);
                      break;
                  }
                }
              })
              .catch((res) => {
                console.log(res);
                this.compulsoryReject(false);
              });
            break;
          default:
            this.compulsoryReject(false);
            break;
        }
      }
    },
    // 检测错误信息
    checkError(err) {
      this.checkData = err.data.data;
      this.errorData = JSON.parse(this.checkData.checkResultJson).filter((row) => !row.isSuccess);
      this.errorColumns = checkErrorColumns;
      this.uploadErrorVisible = true;
      this.footerHide = false;
    },
    // 差异详情
    async differenceDetails(row) {
      try {
        const res = await this.$http.get(equipmentassets.queryDifferDetail, {
          params: {
            id: row.id,
          },
        });
        this.viewDeviceId = row.id;
        this.differData = res.data.data;
        this.differenceList = res.data.data;
        this.deviceDetailAction = 'edit';
        this.differenceShow = true;
        this.onlyDiff = true;
        this.differenceList = this.differData.filter((row) => row.isDiffer === '1');
      } catch (err) {
        console.log(err);
      }
    },
    changeOnlyDiff(val) {
      if (val) {
        this.differenceList = this.differData.filter((row) => row.isDiffer === '1');
      } else {
        this.differenceList = this.differData;
      }
    },
    changeOnlyUnqualified(val) {
      if (val) {
        this.differenceList = this.differData.filter((row) => row.isUnqualified === '1');
      } else {
        this.differenceList = this.differData;
      }
    },
    synchroResultModalShow(row) {
      this.$refs.InfoSynchroResult.init(row);
    },
    paramsConfig() {
      this.configShow = true;
    },
    edit(device) {
      if (this.active === 'pendingStorage') {
        this.viewUrl = equipmentassets.viewFill;
      } else {
        this.viewUrl = null;
      }
      this.saveUrl = equipmentassets.updateFill;
      this.viewDeviceId = device.id;
      this.deviceCode = device.deviceId;
      this.deviceDetailTitle = '修改入库设备';
      this.deviceDetailAction = 'edit';
      this.deviceDetailShow = true;
    },
    batchEdit(batchData, searchData) {
      this.batchEditShow = true;
      this.batchEditData = batchData;
      this.batchParams = searchData;
    },
    exportModule(checkedData) {
      this.customFilter = true;
      this.defaultCheckedList = [
        'deviceId',
        'id',
        'orgCode',
        'civilCode',
        'deviceName',
        'sbgnlx',
        'sbdwlx',
        'ipAddr',
        'macAddr',
        'longitude',
        'latitude',
        'sbcjqy',
        'phyStatus',
        'putMark',
        'cascadeReportStatus',
      ];
      this.checkedData = checkedData || [];
    },
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
        this.defaultCheckedList = [
          'deviceId',
          'id',
          'deviceName',
          'sbgnlx',
          'sbdwlx',
          'ipAddr',
          'macAddr',
          'longitude',
          'latitude',
          'sbcjqy',
          'phyStatus',
        ];
        this.leftDisabled = (item) => {
          return item.propertyName === 'deviceId' || item.propertyName === 'id';
        };
        this.rightDisabled = (item) => {
          return !(item.propertyName === 'deviceId' || item.propertyName === 'id');
        };
      } catch (err) {
        console.log(err);
      }
    },
    async exportExcel(propertyList, type) {
      try {
        if (type === 'module') {
          this.exportModuleLoading = true;
        } else {
          this.$_openDownloadTip();
          this.exportDataLoading = true;
        }
        let params = {
          fieldList: propertyList.map((row) => row.propertyName),
          isTemplate: type === 'module',
          ids: this.checkedData.map((item) => item.id),
        };
        Object.assign(params, this.searchData);
        const res = await this.$http.post(this.exportUrl, params);
        await this.$util.common.transformBlob(res.data.data);
        this.customFilter = false;
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportModuleLoading = false;
        this.exportDataLoading = false;
      }
    },
    // 查看异常原因
    async abnormal(row) {
      try {
        if (this.active === 'pendingStorage') {
          const res = await this.$http.get(equipmentassets.queryUnqualifiedDetail, {
            params: {
              id: row.id,
            },
          });
          this.errorColumns = checkErrorColumns;
          this.uploadErrorVisible = true;
          this.footerHide = true;
          this.errorData = res.data.data;
        } else {
          this.fieldShow = true;
          this.fieldData = row;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 查看审核记录
    auditRecords(row) {
      this.recordType = 'audit-records';
      this.recordTitle = '审核记录';
      this.recordShow = true;
      this.recordColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '审核人',
          key: 'examineUser',
          align: 'left',
        },
        {
          title: '组织机构',
          key: 'orgNames',
          align: 'left',
        },
        {
          title: '联系电话',
          key: 'phone',
          align: 'left',
        },
        {
          title: '审核结果',
          key: 'examineStatusText',
          align: 'left',
        },
        {
          title: '审核时间',
          key: 'examineTime',
          align: 'left',
        },
        {
          title: '审核说明',
          key: 'remark',
          align: 'left',
        },
      ];
      this.recordFetch = async (searchData) => {
        try {
          const res = await this.$http.get(equipmentassets.queryExamineListByFillInId, {
            params: Object.assign(
              {
                fillInId: row.id,
              },
              searchData,
            ),
          });
          this.recordData = res.data.data;
        } catch (err) {
          console.log(err);
        }
      };
    },
    // 查看操作记录
    operationRecord(row) {
      this.recordType = 'fill-record';
      this.recordTitle = '操作记录';
      this.recordShow = true;
      this.recordColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '填报/同步人',
          key: 'userName',
          align: 'left',
        },
        {
          title: '填报/同步时间',
          key: 'modifyTime',
          align: 'left',
        },
        {
          title: '审核/入库人',
          key: 'examineUser',
          align: 'left',
        },
        {
          title: '审核/入库时间',
          key: 'examineTime',
          align: 'left',
        },
        {
          title: '修改内容',
          key: 'content',
          align: 'left',
          toolTip: true,
        },
      ];
      this.recordFetch = async (searchData) => {
        try {
          const res = await this.$http.post(
            equipmentassets.getDeviceRecord,
            Object.assign(
              {
                deviceInfoId: row.id,
              },
              searchData,
            ),
          );
          this.recordData = res.data.data.entities;
        } catch (err) {
          console.log(err);
        }
      };
    },
    dealError(error) {
      this.errorData = JSON.parse(error);
      this.uploadErrorVisible = true;
      this.footerHide = true;
      this.errorColumns = [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '设备编码',
          key: 'deviceId',
          align: 'left',
        },
        {
          title: '错误原因',
          key: 'errorReason',
          align: 'left',
        },
      ];
    },
    //从消息跳转而来，依次选中deviceType待入库设备、type全部设备、examineStatus审核状态不通过
    routerEnterParams(query) {
      if (!Object.keys(query)?.length) {
        return;
      }
      let keys = Object.keys(query);
      if (keys.includes('deviceType')) {
        this.changeType({ value: query['deviceType'] });
      }
      //若包含审核状态examineStatus
      if (keys.includes('examineStatus')) {
        //状态是否为数字
        let type = typeof query['type'] == 'number' ? query['type'] : Number(query['type']);
        this.changeStatus(type);
        this.$refs.searchModule.$refs.tagView.curTag = type || 0;
        //审核状态 '2'审核不通过
        let examineStatus =
          typeof query['examineStatus'] == 'string' ? query['examineStatus'] : query['examineStatus'].toString();
        this.$refs.searchComponent.searchData.examineStatus = examineStatus || '0';
      }
      this.search();
    },
  },
  watch: {},
  components: {
    SearchModule: require('./components/search-module.vue').default,
    TableModule: require('./components/table-module.vue').default,
    PendingStorage: require('./components/pending-storage.vue').default,
    InStock: require('./components/in-stock.vue').default,
    AreaSelect: require('@/components/area-select').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    Difference: require('@/views/viewassets/components/difference.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    InfoSynchroResult: require('@/views/datagovernance/onlinegovernance/information/info-synchro-result.vue').default,
    Config: require('./components/config/index.vue').default,
    OperationRecord: require('./components/operation-record/index.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    ViewDetectionField: require('@/views/viewassets/components/view-detection-field.vue').default,
    BatchEdit: require('./components/batch-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
.assetfill {
  background-color: var(--bg-content);
}
</style>
