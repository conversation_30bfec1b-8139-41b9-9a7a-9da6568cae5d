<template>
  <div class="dataanalysis-box" v-ui-loading="{ loading: isInitLoading }">
    <!-- 左侧树 -->
    <div class="dataanalysis-box-menu">
      <template v-if="menuList.length > 0">
        <Button class="add-task" type="primary" v-if="menuList.length" @click="addTaskFn">
          <i class="icon-font icon-tianjia mr-xs f-12" /> 新建分析任务
        </Button>
        <menu-list
          class="menu-list"
          ref="menuTree"
          :show-filter-input="true"
          :show-children-num="true"
          :menu-list="menuList"
          :default-expanded-keys="defaultExpandedKeys"
          :loading="treeLoading"
          :default-props="defaultProps"
          :accordion="true"
          @clickTreeNode="clickTreeNode"
        >
          <!-- 状态标记 -->
          <template #textRight="{ data }">
            <span
              v-if="[0, 1].includes(data.storageStatus) && data.parentId === '0'"
              class="tip-span"
              :class="{ 'tip-draft': data.storageStatus === 0, 'tip-pause': data.storageStatus === 1 }"
            >
              {{ data.storageStatus === 0 ? `草稿` : '暂停' }}
            </span>
          </template>
          <!-- ...下拉按钮列表 -->
          <template #dropdownBtn="{ data }">
            <Dropdown transfer transfer-class-name="tree-dropdown-box" v-if="data.parentId === '0'">
              <i class="icon-font icon-gengduo icon-more" />
              <template #list>
                <DropdownMenu>
                  <DropdownItem
                    v-for="(btn, index) in getDropdownList(data)"
                    :key="index"
                    @click.native="clickDropdownItem(btn, data)"
                  >
                    {{ btn.name }}
                  </DropdownItem>
                </DropdownMenu>
              </template>
            </Dropdown>
          </template>
        </menu-list>
      </template>
      <template v-else>
        <div class="no-data-box">
          <span>暂无创建的任务列表</span>
          <span>请点击右边创建</span>
        </div>
      </template>
    </div>

    <!-- 右侧view -->
    <div class="dataanalysis-box-content ml-sm auto-fill">
      <div class="no-data-box content-no-data" v-if="menuList.length === 0">
        <img src="~@/assets/img/dataanalysis/no-data.png" alt="" />
        <Button type="primary" class="no-button" @click="addTaskFn">创建分析任务</Button>
      </div>
      <component v-else :is="componentName" :menu-list="menuList" :detection-time-list="detectionTimeList"></component>
    </div>

    <!-- 分析任务 弹框 -->
    <task-modal
      v-if="taskVisible"
      :entry-type="entryType"
      :node-data="currentNodeData"
      @input="taskVisible = false"
      @saveSuccessCb="getTreeData"
    ></task-modal>
  </div>
</template>

<script>
import { mapActions } from 'vuex';
import dataAnalysis from '@/config/api/dataAnalysis.js';
import { dropdownBtnList } from './utils/menuConfig';
export default {
  name: 'dataanalysis',
  components: {
    MenuList: require('./components/menu-list.vue').default,
    TaskModal: require('./components/task-modal.vue').default,
    ActiveAnalysis: require('./analysis-modules/ActiveAnalysis/index.vue').default,
  },
  data() {
    return {
      componentName: '',
      isInitLoading: true,
      treeLoading: false,
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      menuList: [],
      defaultExpandedKeys: [],
      // 分析任务弹框
      taskVisible: false,
      entryType: 'add',
      currentNodeData: {},
      detectionTimeList: [],
    };
  },
  computed: {},
  created() {
    this.initTreeData();
  },
  mounted() {
    // 分析内容列表
    this.setFactorList();
    // 影响因素列表
    this.setInfluenceInfo();
  },
  methods: {
    ...mapActions({
      setFactorList: 'dataAnalysis/getFactorInfo',
      setInfluenceInfo: 'dataAnalysis/getInfluenceInfo',
    }),
    getDropdownList(data) {
      return dropdownBtnList(data.storageStatus);
    },
    async initTreeData() {
      this.isInitLoading = true;
      await this.getTreeData();
      this.isInitLoading = false;
    },
    async getTreeData() {
      try {
        this.treeLoading = true;
        let res = await this.$http.get(dataAnalysis.getTree);
        this.menuList = this.$util.common.arrayToJson(res.data.data || [], 'id', 'parentId');

        // 路径存在 treeId，需要默认选中
        let { treeId, cptname } = this.$route.query;
        if (treeId && res.data.data && res.data.data.some((item) => item.id === treeId)) {
          this.detectionTimeList.length === 0 ? this.getHistoryBatchInfoByCustom(treeId) : null;
          this.defaultExpandedKeys = [treeId];
          this.componentName = this.getCompoment(cptname);
          this.$nextTick(() => {
            this.$refs.menuTree.$refs.tree.setCurrentKey(treeId);
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    // 新增分析任务
    addTaskFn() {
      this.taskVisible = true;
      this.entryType = 'add';
      this.currentNodeData = {};
    },
    // 获取检测时间
    async getHistoryBatchInfoByCustom(id) {
      try {
        let res = await this.$http.get(`${dataAnalysis.getHistoryBatchInfoByCustom}?id=${id}`);
        this.detectionTimeList = res.data.data || [];
      } catch (error) {
        console.log(error);
      }
    },
    async clickTreeNode(data) {
      let { id, factorCode, componentName } = data;
      await this.getHistoryBatchInfoByCustom(id);
      const name = this.$route.name;
      this.$router.push({
        name: name,
        query: {
          code: factorCode,
          cptname: componentName,
          treeId: id || '',
          examineTime: this.detectionTimeList[0]?.date || '',
          batchId: this.detectionTimeList[0]?.options[0]?.batchId || '',
        },
      });
      this.componentName = null;
      this.$nextTick(() => {
        this.componentName = this.getCompoment(componentName);
      });
    },
    getCompoment(componentName) {
      return require(`./analysis-modules/${componentName}/index.vue`).default;
    },
    clickDropdownItem(btnItem, data) {
      switch (btnItem.key) {
        case 'edit':
          this.editFn(data);
          return;
        case 'analyze':
          this.$UiConfirm({ content: `您确定要立即执行${btnItem.name}【${data.name}】任务吗？` }).then(() => {
            this.analyzeFn(data, btnItem);
          });
          return;
        case 'del':
          this.$UiConfirm({ content: `您确定要${btnItem.name}【${data.name}】任务吗？` }).then(() => {
            this.delFn(data, btnItem);
          });
          return;
        case 'records':
          this.recordsFn(data);
          return;
        case 'pause':
        case 'start':
          this.$UiConfirm({ content: `您确定要${btnItem.name}【${data.name}】任务吗？` }).then(() => {
            this.pauseStartFn(data, btnItem);
          });
          return;
        default:
          return;
      }
    },
    // 编辑
    editFn(data) {
      this.entryType = 'edit';
      this.taskVisible = true;
      this.currentNodeData = { ...data };
    },
    // 分析
    async analyzeFn(data) {
      try {
        await this.$http.get(`${dataAnalysis.triggerIndexJob}/${data.id}`);
        this.$Message.success('执行成功');
        this.getTreeData();
      } catch (error) {
        console.log(error);
      }
    },
    // 删除
    async delFn(data, btnItem) {
      try {
        await this.$http.get(`${dataAnalysis.removeTask}/${data.id}`);
        this.$Message.success(`${btnItem.name}成功`);
        // 若当前选择节点的删除
        let treeId = this.$route.query.treeId || '';
        let ids = treeId.split('_');
        if (treeId && ids[0] === data.id) {
          this.detectionTimeList = [];
          this.defaultExpandedKeys = [];
          this.componentName = null;
          this.$router.push({
            name: this.$route.name,
            query: {},
          });
        }
        this.getTreeData();
      } catch (error) {
        console.log(error);
      }
    },
    // 记录
    recordsFn() {},
    // 暂停 || 启动
    async pauseStartFn(data, btnItem) {
      try {
        await this.$http.get(`${dataAnalysis.pauseStartIndexJob}/${data.id}`);
        this.$Message.success(`${btnItem.name}成功`);
        this.getTreeData();
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.dataanalysis-box {
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
  .dataanalysis-box-menu {
    width: 300px;
    height: 100%;
    padding: 16px;
    background: var(--bg-content);
    border-radius: 4px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    .menu-list {
      height: 100%;
    }
    .add-task {
      width: 100%;
      margin-bottom: 16px;
    }
    .icon-gengduo {
      transform: rotate(180deg);
      margin: 0 5px;
      // color: #ffffff;
    }
    .tip-span {
      display: inline-block;
      width: 36px;
      height: 17px;
      border-radius: 100px;
      padding: 0 2px;
      color: #ffffff;
      font-size: 12px;
      text-align: center;
      &.tip-draft {
        background: linear-gradient(245deg, #c1c7d2 0%, #808da7 100%);
      }
      &.tip-pause {
        background: linear-gradient(246deg, #ffcc65 0%, #e77811 98%);
      }
    }
  }
  .dataanalysis-box-content {
    flex: 1;
    height: 100%;
    padding-bottom: 11px;
    background: var(--bg-content);
    position: relative;
    .add-task-nodata {
      width: 300px;
      height: 100px;
      line-height: 100px;
      cursor: pointer;
      background: #f5f4f4;
      font-size: 18px;
      text-align: center;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .no-data-box {
    height: 100%;
    width: 100%;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--color-no-data-text);
  }
  .content-no-data {
    .no-button {
      margin: 34px 0 25px 0;
      width: 187px;
    }
  }
}
</style>
