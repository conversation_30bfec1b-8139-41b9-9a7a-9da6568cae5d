<template>
  <div
    class="ui-tab-pane"
    :class="active ? 'active' : ''"
    @click="tabClick"
    @contextmenu.prevent="openMenu($event)"
    ref="ui-tab-pane"
  >
    <slot name="label">
      <span class="ui-tab-label ellipsis" :title="label">{{ label }}</span>
    </slot>
    <i class="icon-font icon-dacha tab-close" v-if="hasClosed" @click.stop="closeRouter"></i>
  </div>
</template>
<script>
export default {
  name: 'ui-tab-pane',
  inject: ['rootTabs'],
  props: {
    label: {
      type: String,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
    },
    closable: {
      type: Boolean,
    },
    /**
     * 存在组件标签页时会有此参数传入
     * componentName：'组件名称',
     * routeName: '此组件所在路由'
     */
    meta: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    tabClick() {
      this.rootTabs.tabsValue = this.name;
      this.rootTabs.$emit('on-click', this.name, this.meta);
    },
    closeRouter() {
      this.rootTabs.$emit('on-tab-remove', this.name, this.meta);
    },
    openMenu(even) {
      this.rootTabs.openMenu(this.name, even);
      this.rootTabs.$emit('on-contextmenu', this.name, even);
    },
  },
  watch: {},
  computed: {
    hasClosed() {
      return this.closable || this.rootTabs.closable;
    },
    active() {
      return this.rootTabs.tabsValue === this.name;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.ui-tab-pane {
  padding: 0 20px;
  height: 36px;
  line-height: 36px;
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-tab-pane);
  position: relative;
  .ui-tab-label {
    display: inline-block;
    vertical-align: middle;
    max-width: 120px;
  }
  &:hover {
    background-color: var(--bg-tab-pane-active);
    color: var(--color-tab-pane-active);
  }
  &.active {
    background-color: var(--bg-tab-pane-active);
    color: var(--color-tab-pane-active);
    border-bottom: 3px solid var(--color-primary);
  }
  .tab-close {
    font-size: 12px;
    margin-left: 10px;
    transform: scale(0.7);
  }
}
</style>
