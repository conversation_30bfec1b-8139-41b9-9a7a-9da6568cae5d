<template>
  <div class="transfer-table">
    <div class="transfer-table-wrap">
      <div class="transfer-table-left">
        <div class="transfer-table-title">{{ title1 }}</div>
        <div class="transfer-table-left-content col-flex">
          <div class="transfer-table-left-content-header" v-if="hasHeader">
            <div class="transfer-table-left-content-header-label">接入数据表</div>
            <Select v-model="access" @on-change="onchange" placeholder="请选择数据表">
              <Option v-for="(item, index) in accessData" :key="index" :value="item.id">{{
                item.kafkaTopicName
              }}</Option>
            </Select>
          </div>
          <ui-table
            class="flex-1"
            :tableColumns="columns1"
            :tableData="tableData1"
            @selectTable="selectTable"
            style="flex: 1"
          ></ui-table>
          <loading v-if="leftLoading"></loading>
        </div>
      </div>
      <div class="transfer-table-center">
        <div class="transfer-table-center-icon">
          <connecting-arrow :connectingOptions="connectingOptions"></connecting-arrow>
        </div>
      </div>
      <div class="transfer-table-right">
        <div class="transfer-table-title">
          {{ title2 }} <span class="tips" v-if="tip">({{ tip }})</span>
        </div>
        <div class="transfer-table-right-content col-flex">
          <ui-table class="flex-1" :tableColumns="columns2" :tableData="tableData2">
            <template slot="header"></template>
          </ui-table>
          <loading v-if="rightLoading"></loading>
        </div>
      </div>
    </div>
    <div class="transfer-table-footer">
      <Button type="primary" @click="save">保&nbsp;存</Button>
    </div>
  </div>
</template>
<script>
import UiTable from '../../../../components/ui-table.vue';
import ConnectingArrow from './connecting-arrow.vue';
export default {
  props: {
    hasHeader: {
      type: Boolean,
      default: false,
    },
    tip: {
      type: String,
      default: '',
    },
    title1: {
      type: String,
      default: '',
    },
    title2: {
      type: String,
      default: '',
    },
    columns1: {
      type: Array,
      default: () => [],
    },
    columns2: {
      type: Array,
      default: () => [],
    },
    tableData1: {
      type: Array,
      default: () => [],
    },
    tableData2: {
      type: Array,
      default: () => [],
    },
    accessData: {
      type: Array,
      default: () => [],
    },
    accessId: {
      type: [Number, String],
      default: '',
    },
    // minusHeight1: {
    //   type: Number,
    //   default: 553,
    // },
    leftLoading: {
      type: Boolean,
      default: false,
    },
    rightLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      connectingOptions: {
        width: '1.6%',
        height: '0.05rem',
        top: '45%',
        // left: "10px",
      },
      access: null,
    };
  },
  created() {},
  mounted() {
    this.access = this.accessId;
  },
  methods: {
    onchange(val) {
      this.$emit('handleChange', val);
    },
    selectTable(selection) {
      this.$emit('selectionChange', selection);
    },
    save() {
      this.$emit('handleSave');
    },
  },
  watch: {
    accessId() {
      this.access = this.accessId;
    },
  },
  components: { ConnectingArrow, UiTable },
};
</script>
<style lang="less" scoped>
.justify-flex {
  display: flex;
  justify-content: center;
}
.col-flex {
  display: flex;
  flex-direction: column;
}
.flex-1 {
  flex: 1;
}
.transfer-table {
  width: 100%;
  height: 100%;
  padding: 20px 41px 23px;
  @{_deep} .ivu-select {
    outline-style: none;
    &-selection {
      border: none;
      background: transparent;
      outline-style: none;
      box-shadow: none;
    }
  }
  @{_deep} .ivu-select-arrow {
    color: var(--color-primary);
  }
  &-wrap {
    display: flex;
  }
  &-footer {
    width: 100%;
    margin-top: 24px;
    .justify-flex;
  }
  &-left {
    flex: 1;
    &-content {
      position: relative;
      min-height: 448px;
      padding: 14px 10px;
      border: 1px solid var(--border-modal-footer);
      // overflow: auto;
      &-header {
        width: 100%;
        margin-bottom: 13px;
        display: flex;
        align-items: center;
        font-size: 14px;
        &-label {
          width: 110px;
          color: var(--color-label);
        }
        @{_deep} .ivu-select {
          .ivu-select {
            &-selection {
              width: 380px;
              height: 34px;
              border-color: var(--color-primary);
              border: 1px solid var(--color-primary);
              border-radius: 4px;
              line-height: 34px;
            }
            &-dropdown {
              min-width: 380px !important;
            }
          }
        }
      }
    }
  }
  &-title {
    margin-bottom: 18px;
    font-size: 14px;
    color: var(--color-content);
  }
  &-center {
    width: 42px;
    display: flex;
    align-items: center;
    &-icon {
      .justify-flex;
      width: 100%;
      cursor: pointer;
    }
  }
  &-right {
    flex: 1;
    &-content {
      position: relative;
      min-height: 448px;
      padding: 14px 10px;
      border: 1px solid var(--border-modal-footer);
      // overflow: auto;
    }
  }
  .tips {
    margin-left: 10px;
    font-size: 12px;
    color: var(--color-tips);
  }
  @{_deep} .ivu-select-single .ivu-select-input {
    color: var(--color-input) !important;
  }
}
</style>
<style lang="less">
.transfer-table-select {
  background: #1c325a !important;
  li {
    color: var(--color-select-item);
    &:hover {
      background: var(--bg-select-item-hover) !important;
    }
  }
  .ivu-select-item-selected {
    background: var(--bg-select-item-active) !important;
  }
  .ivu-select-item-focus {
    background: transparent !important;
  }
}
</style>
