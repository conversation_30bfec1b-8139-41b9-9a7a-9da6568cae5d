<template>
  <div class="device-content" :class="{ isClose: !isOpen }">
    <ui-loading v-if="loading && isOpen" />
    <div class="device-content-header">
      <div class="label">
        <div class="label-text">资源列表：</div>
        <div
          class="tree-operators-icon-wrap"
          @mouseenter="showTreeFilter"
          @mouseleave="hideTreeFilter"
        >
          <i class="iconfont color-bule icon-loudou"></i>
          <div class="tree-filter-wrap" v-if="isTreeFilter">
            <div class="triangle"></div>
            <div class="tree-filter-wrap-content">
              <div class="tree-filter-wrap-title">
                <span>状态</span>
              </div>
              <div class="tree-filter-check-wrap">
                <Checkbox v-model="stuVAll" @on-change="changeStatusAll"
                  >全选</Checkbox
                >
                <CheckboxGroup v-model="stuV" @on-change="changeStatus">
                  <Checkbox label="在线"></Checkbox>
                  <Checkbox label="离线"></Checkbox>
                </CheckboxGroup>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="operator"
        @click="
          () => {
            $emit('update:isOpen', !isOpen);
          }
        "
      >
        {{ isOpen ? "收起面板" : "点击展开" }}
      </div>
    </div>

    <xn-tree
      class="deviceTree"
      :ref="'tree'"
      :option="option"
      :label="labelFn"
      :fileOpe="fileOpe"
      @clickNode="handleNodeSingleClick"
      @dblclickNode="dblclickNode"
    ></xn-tree>

    <div class="general-search-footer" v-if="isOpen && searchPrams.keyWords">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="pageInfo.pageNumber"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>

    <!-- 加入分组 -->
    <add-to-group ref="addToGroup"></add-to-group>
  </div>
</template>

<script>
import { mapMutations, mapGetters } from "vuex";
import {
  queryDeviceOrgTree,
  queryDeviceAndKeys,
  queryMyVideoGroupList,
  addVideoToGroup,
  getTreeAncestors,
} from "@/api/player";
import { copyText } from "@/util/modules/common";
import xnTree from "@/components/xn-tree/index.vue";
import addToGroup from "@/views/video-application/video-play/components/add-to-group.vue";
export default {
  components: {
    xnTree,
    addToGroup,
  },
  props: {
    //搜索条件
    searchPrams: {
      type: Object,
      default: () => {},
    },
    playingIds: {
      type: Array,
      default: () => [],
    },
    isOpen: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      deviceList: [],
      currentTabIndex: 0,
      deviceType: "1",
      loading: false,
      groupList: [],
      node: null,
      resolveFunc: null,
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 19,
      },
      isTreeFilter: false,
      stuVAll: true,
      stuV: ["在线", "离线"],
      treeData: [],
      lastPlayingIds: [],
      option: {
        id: "id",
        pId: false,
        hideIcon: true,
        selectType: null, //radio,null,checkbox
        canMove: false,
        lazyLoad: true,
        expandOnClick: true,
        autoOpen: function (d, level) {
          return level <= 2;
        },
        on: {
          loadData: async (node) => {
            this.loading = true;
            let roleParam = {
              roleId: this.userInfo.roleVoList[0].id,
              filter:
                this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
              socialResources: this.individuation.hideCommunity ? 0 : 1,
              excludeDeviceTypes: this.individuation.hideDeviceTypes
                ? this.individuation.hideDeviceTypes
                : [],
            };
            return new Promise((resolve) => {
              queryDeviceOrgTree({
                orgCode: node.orgCode,
                isOnlineStatus: this.isOnlineStatus,
                ...roleParam,
              }).then((res) => {
                console.log("sssssssssssssssssssssssssss");
                this.loading = false;
                resolve(this._formatDeviceOrgList(res));
              });
            });
          },
        },
      },
      fileOpe: [
        {
          label: "查看视频",
          show: this.$_has(["video-realTime"]),
          clickFn: (item) => {
            this.dblclickNode(item);
          },
        },
        {
          label: "查看档案",
          show: true,
          clickFn: (item) => {
            /**
             * 1 摄像机
             * 2 车辆卡口
             * 3 WIFI设备
             * 5 RIFD设备
             * 4 电子围栏
             * 11 人脸抓拍机
             * 16 ETC
             *
             * 跳转设备档案，需要携带设备类型
             * 1. 感知设备：跳转路径为device-archives，参数名为deviceType
             * 2. 视图设备：跳转未经为device-archive
             * 根据deviceType来进行区分，目前视图设备按照原来的从档案列表跳转档案详情，携带的是sbgnlx，参数名为type，对应关系如下：
             * sbgnlx: 1 - deviceType: 1
             * sbgnlx: 2 - deviceType: 2
             * sbgnlx: 3 - deviceType: 11
             *
             * 如果跳转不对，那么是数据的问题，请找后端
             */
            let name = "";
            let query = { archiveNo: item.deviceGbId };
            if (!item.deviceType) {
              this.$Message.warning("缺少设备类型");
              return;
            }
            if (["1", "2", "11"].includes(item.deviceType)) {
              name = "device-archive";
              query = {
                ...query,
                type: item.deviceType == "3" ? "11" : item.deviceType,
              };
            } else {
              name = "device-archives";
              query = {
                ...query,
                deviceType: item.deviceType,
              };
            }
            const { href } = this.$router.resolve({
              name,
              query,
            });
            window.open(href, "_blank");
          },
        },
        {
          label: "加入分组",
          show: true,
          clickFn: (item) => {
            this.$refs.addToGroup.show(item.deviceId);
          },
        },
        {
          label: "查看目录",
          show: true,
          clickFn: (item) => {
            getTreeAncestors(item.deviceId).then((res) => {
              if (res.code == 200) {
                let list = res.data.data.deviceOrgList;
                let text = list.map((v) => v.orgName).join("/");
                this.$Message.info({ content: text, duration: 5 });
              } else {
                this.$Message.error(res.msg);
              }
            });
          },
        },
        {
          label: "复制名称",
          show: true,
          clickFn: (item) => {
            copyText(item.deviceName);
          },
        },
        // ,{
        //   label: '发送工单',
        //   show: true,
        //   clickFn: (item) => {

        //   }
        // },{
        //   label: '查询工单',
        //   show: true,
        //   clickFn: (item) => {

        //   }
        // }
      ],
    };
  },
  computed: {
    ...mapGetters({
      getClickObj: "map/getClickObj",
      userInfo: "userInfo",
      individuation: "individuation",
      ignoreDevices: "player/getIgnoreDevices",
    }),
    isOnlineStatus() {
      if (this.stuVAll) {
        return [];
      } else {
        return this.stuV.map((v) => {
          if (v == "在线") {
            return 0;
          } else {
            return 1;
          }
        });
      }
    },
    onlyOffLine() {
      return this.stuV.length == 1 && this.stuV[0] == "离线" ? true : false;
    },
  },
  watch: {
    isOnlineStatus: {
      handler(val) {
        this.searchName(true);
      },
    },
    playingIds: {
      handler(list) {
        if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
        // 地图点击点位自动展开目录
        let nList = list.filter((v) => v);
        let newId = nList.filter((v) => !this.lastPlayingIds.includes(v))[0];
        let lastId = newId || nList[nList.length - 1];
        if (lastId && this.$refs.tree.xnTree && !this.searchPrams.keyWords) {
          getTreeAncestors(lastId).then((res) => {
            if (res.code == 200) {
              let arr = res.data.data.deviceOrgList;
              this.$refs.tree.xnTree.closeAll();
              this.$refs.tree.xnTree.loadDataByOrgList(arr, () => {
                this.$refs.tree.xnTree.scrollToById(lastId);
              });
            }
          });
        }
        this.lastPlayingIds = nList;
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    ...mapMutations({
      setCollectJudge: "map/setCollectJudge",
      setClickObj: "map/setClickObj",
    }),
    init() {
      this.getParentData();
    },
    labelFn(data) {
      let titleClass = data.deviceId && data.isOnline == "1" ? "offline" : "";
      let playingClass =
        data.deviceId && this.playingIds.includes(data.deviceId)
          ? "playing"
          : "";
      let iconClass =
        data.deviceId &&
        data.deviceType == "1" &&
        this.playingIds.includes(data.deviceId)
          ? "playing-icon"
          : !data.deviceId
          ? "icon-fenju"
          : this.getDeviceIcon(data);
      let iconColor = this.getDeviceIconColor(data);
      let ignoreClass = this.ignoreDevices.includes(data.deviceId)
        ? "ignore"
        : "";
      let onLine = `<span class="color-bule">${data.onlineTotal || 0}</span>`;
      let offLine = `<span class="color-grey">${data.offlineTotal || 0}</span>`;
      let statistics = !data.deviceId
        ? `(${this.onlyOffLine ? offLine : onLine}/${data.allTotal || 0})`
        : "";
      let operate = "";
      let html = `<div class="node-title ${titleClass} ${playingClass} ${ignoreClass}" dtype="${data.deviceType}">
          <i class="iconfont color-bule ${iconClass}" style="color: ${iconColor}"></i>
          <span class="label">${data.label}</span>
          <span class="statistics">${statistics}</span>
        </div>
        <div class="operateBtns">${operate}</div>
        `;
      return html;
    },
    getParentData() {
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
        socialResources: this.individuation.hideCommunity ? 0 : 1,
        excludeDeviceTypes: this.individuation.hideDeviceTypes
          ? this.individuation.hideDeviceTypes
          : [],
      };
      this.loading = true;
      let params = {
        searchKey: this.searchPrams.keyWords,
        isOnlineStatus: this.isOnlineStatus,
        ...roleParam,
      };
      if (this.searchPrams.keyWords) params = { ...params, ...this.pageInfo };
      if (this.searchPrams.keyWords == "") {
        queryDeviceOrgTree(params).then((res) => {
          this.total = res.data.total || 0;
          this.loading = false;
          this.treeData = this._formatDeviceOrgList(res);
          //   console.log("treeData ", this.treeData);
          this.$refs.tree.initTree(this.treeData);
        });
      } else {
        queryDeviceAndKeys(params).then((res) => {
          this.total = res.data.total || 0;
          this.loading = false;
          this.treeData = this._formatDeviceOrgList(res);
          //   console.log("treeData ", this.treeData);
          this.$refs.tree.initTree(this.treeData);
        });
      }
    },
    // 选中左侧树节点
    dblclickNode(item) {
      if (item.deviceId) {
        if (!item.latitude || !item.longitude) {
          this.$Message.info("该设备没有点位坐标信息！");
        } else {
          this.setClickObj(item);
          this.$emit("openDeviceDom", item);
        }
      }
    },
    // 单击
    handleNodeSingleClick(item) {
      if (item.deviceId) {
        if (!item.latitude || !item.longitude) {
          this.$Message.info("该设备没有点位坐标信息！");
        } else {
          this.$emit("mapSelectDevice", item);
        }
      }
    },
    getDeviceIcon(item) {
      return Toolkits.getDeviceIconType(item);
    },
    getDeviceIconColor(item) {
      return Toolkits.getDeviceIconColor(item);
    },
    getGroupList() {
      queryMyVideoGroupList({ userId: this.userInfo.id }).then((res) => {
        this.groupList = res.data.grouplist;
      });
    },
    // 格式化设备、组织、统计数据
    _formatDeviceOrgList(deviceOrgResult) {
      let deviceList = deviceOrgResult.data.deviceList
        ? deviceOrgResult.data.deviceList.map((v) => {
            v.id = v.deviceId;
            v.label = v.deviceName;
            (v.isLeaf = true),
              (v.ptzType = v.deviceChildType ? v.deviceChildType : v.ptzType); // 基线视频应用这的枪机球机根据ptzType判断，蚌埠根据deviceChildType
            return v;
          })
        : [];
      let deviceOrgList = deviceOrgResult.data.deviceOrgList
        ? deviceOrgResult.data.deviceOrgList.map((v) => {
            v.label = v.orgName;
            return v;
          })
        : [];
      deviceOrgList = deviceOrgList.filter((v) => v.allTotal > 0);
      return [...deviceOrgList, ...deviceList];
    },
    joinGroup(groupId, item) {
      if (!groupId) return;
      addVideoToGroup({
        groupId: groupId,
        deviceId: item.deviceId,
      }).then((res) => {
        if (res.data.code == 200) {
          this.$Message.success("添加成功");
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
    searchName(isReset) {
      if (this.loading) return;
      if (isReset) this.pageInfo.pageNumber = 1;
      this.getParentData();
    },
    pageChange(pageNumber) {
      this.pageInfo.pageNumber = pageNumber;
      this.searchName();
    },
    // 展示筛选列表
    showTreeFilter() {
      this.isTreeFilter = true;
    },
    // 隐藏筛选列表
    hideTreeFilter() {
      this.isTreeFilter = false;
    },
    changeStatusAll() {
      if (this.stuVAll) {
        this.stuV = ["在线", "离线"];
      } else {
        this.stuV = [];
      }
    },
    changeStatus(data) {
      if (data.length == 2) {
        this.stuVAll = true;
      } else {
        this.stuVAll = false;
      }
    },
  },
};
</script>

<style lang="less" scope>
.device-content {
  height: 100%;
  position: relative;
  padding: 10px;
  padding-top: 10px !important;
  display: flex;
  flex-direction: column;
  &.isClose {
    height: 40px !important;
  }
  &-header {
    height: 22px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
    .label {
      font: 14px;
      color: #333333;
      display: flex;
      align-items: center;
      .label-text {
        font-weight: 600;
      }
    }
    .operator {
      cursor: pointer;
      &:hover {
        color: #2c86f8;
      }
    }
    /deep/ .ivu-checkbox-wrapper {
      color: #fff;
    }
    .tree-operators-icon-wrap {
      position: relative;
      .iconfont {
        vertical-align: -webkit-baseline-middle;
      }
      .tree-filter-wrap {
        position: absolute;
        top: 25px;
        left: -70px;
        z-index: 9;
        width: 325px;
        background: rgba(44, 48, 51, 0.9);
        border-radius: 3px;
        color: var(--font-white-color);
        padding: 5px 10px;
        .triangle {
          width: 0;
          height: 0;
          overflow: hidden;
          font-size: 0;
          line-height: 0;
          border-width: 5px;
          border-style: solid;
          border-color: transparent transparent rgba(44, 48, 51, 0.9)
            transparent;
          position: absolute;
          left: 75px;
          top: -10px;
        }
        .tree-filter-wrap-content {
          .tree-filter-wrap-title {
            color: #fff;
            height: 22px;
            font-size: 14px;
            line-height: 25px;
          }
          .ivu-checkbox-wrapper {
            color: #fff;
          }
          .tree-filter-check-wrap {
            display: flex;
          }
          .xui-checkbox-group {
            display: flex;
            flex-wrap: wrap;
            .xui-checkbox {
              margin: 0 15px 0 0 !important;
              height: 25px;
              line-height: 22px;
            }
          }
        }
      }
    }
  }
  .tree-box {
    height: calc(~"100% - 32px");
    overflow-y: auto;
    position: initial;
  }

  .group-list {
    max-height: 300px;
    overflow-y: auto;
  }
  /deep/.el-tree.is-dragging.is-drop-not-allow .el-tree-node__content {
    cursor: pointer;
  }
  .deviceTree {
    height: calc(~"100% - 120px");
    margin-top: 5px;
  }
}
</style>
