<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" :title="title" width="66.14rem" :footerHide="true" @onCancel="reset">
      <transfer-table
        title1="字段拾取"
        title2="字段名列表"
        tip="注:不能为空"
        :columns1="columns1"
        :columns2="columns2"
        :tableData1="propertyList"
        :tableData2="targetList"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @selectionChange="selectionChange"
        @handleSave="handleSave"
      ></transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    title: {
      type: String,
      default: '空值检测',
    },
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { type: 'selection', width: 60 },
        { title: '字段名', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
      ],
      propertyList: [],
      columns2: [
        { title: '字段名', key: 'checkColumnName' },
        { title: '注释', key: 'checkColumnValue' },
      ],
      targetList: [], // 字段名列表
      checkedList: [],
      checkedIds: [],
      topicComponentId: '',
      leftLoading: false,
      rightLoading: false,
    };
  },
  methods: {
    async init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getPropertyList();
      this.getDevice();
    },
    // 获取已勾选的数据
    async getPropertyList() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicPropertyCheckList, { params });
        this.checkedList = res.data.data.map((item) => {
          let obj = {};
          obj.checkColumnName = item.checkColumnName;
          obj.checkColumnValue = item.checkColumnValue;
          // item.column = `${item.checkColumnName} (${item.checkColumnValue})`
          this.checkedIds.push(item.checkColumnName);
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 字段拾取列表
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        // obj.column = `${item.propertyColumn} (${item.propertyName})`;
        return obj;
      });
      this.targetList = selection;
    },
    // 保存
    async handleSave() {
      // if (!this.targetList.length) {
      //   return false;
      // }
      try {
        let params = {
          topicComponentId: this.topicComponentId,
          propertyCheckList: this.targetList,
        };
        await this.$http.post(governancetheme.updateTopicPropertyCheck, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('空值检测配置成功！');
        // this.$Message["success"]({
        //   background: true,
        //   content: "空值检测配置成功！",
        // });
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
  },
  watch: {},
  components: {
    TransferTable: require('../../components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
