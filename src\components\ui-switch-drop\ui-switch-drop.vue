<template>
  <div class="ui-switch-tab">
    <ul class="list" v-if="list.length > 0">
      <li
        v-for="(item, index) in list"
        :key="index"
        :class="{ active: !!item.active }"
        class="ivu-tabs-tab"
        @click="tabClick(item)"
      >
        <span>{{ item[tabProps.label] }}</span>
        <span v-if="item.active">({{ item[tabProps.total] || 0 }})</span>
      </li>
      <slot name="right-content"></slot>
    </ul>
    <switch-select
      v-if="maxTagCount && list.length > 0"
      v-bind="$props"
      ref="switchSelect"
      @on-change="onChangeSwitchSelect"
    ></switch-select>
    <div v-if="list.length === 0" class="font-blue empty">暂无数据</div>
  </div>
</template>
<script>
export default {
  name: 'ui-switch-tab',
  props: {
    /**
     * {label:'', value:''}
     */
    tabList: {
      required: true,
    },
    tabProps: {
      type: Object,
      default: () => {
        return {
          label: 'label',
          value: 'value',
          total: 'total',
        };
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    value: {
      type: [Array, String, Number],
    },
    //最多显示的 tag 数量，超出后折叠。
    maxTagCount: {
      default: 6,
    },
  },
  data() {
    return {
      list: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    tabClick(item) {
      // 抛出改变之前选中的数值
      this.$emit('beforeChangeTab', this.value, item);
      // 更新绑定的数值
      if (this.multiple) {
        this.$set(item, 'active', !item.active);
        let selectList = [];
        this.list.forEach((row) => {
          if (row.active) {
            selectList.push(row[this.tabProps.value]);
          }
        });
        this.$emit('input', selectList);
      } else {
        this.$emit('input', item[this.tabProps.value]);
      }
      this.$set(item, this.tabProps.total, 0);
      // 抛出更改之后的数值
      this.$emit('changeTab', item[this.tabProps.value]);
      this.$refs.switchSelect.reset();
    },
    onChangeSwitchSelect(item) {
      this.$emit('input', item[this.tabProps.value]);
      this.$emit('changeTab', item[this.tabProps.value]);
    },
    setActive() {
      this.list.forEach((row) => {
        if (Array.isArray(this.value)) {
          let index = this.value.findIndex((rw) => rw === row[this.tabProps.value]);
          this.$set(row, 'active', index === -1);
        } else {
          this.$set(row, 'active', this.value === row[this.tabProps.value]);
          this.$emit('on-change', row);
        }
      });
    },
    reset() {},
  },
  watch: {
    tabList: {
      handler(val) {
        if (!val) return;
        this.list = this.maxTagCount ? this.tabList.slice(0, this.maxTagCount) : this.tabList;
        this.setActive();
      },
      immediate: true,
    },
    value() {
      this.setActive();
    },
  },
  computed: {},
  components: {
    switchSelect: require('./switch-select.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ui-switch-tab {
  height: 34px;
  line-height: 34px;
  font-size: 14px;
  .list {
    display: inline-block;
    li {
      margin-right: 10px;
      border-radius: 4px;
      cursor: pointer;
      display: inline-block;
      padding: 0 15px;
      position: relative;
    }
    @{_deep} .ivu-tabs-tab {
      height: 34px;
    }
  }
  .empty {
    line-height: 34px;
    position: relative;
    width: 100%;
    text-align: center;
  }
}
[data-theme='dark'] {
  .ui-switch-tab {
    li {
      background: var(--bg-content);
      color: #8797ac;
      border: 1px solid #085c8a;
      &:hover {
        border: 1px solid var(--color-primary);
        background: #17497e;
        color: #fff;
      }
    }
    .active {
      border: 1px solid var(--color-primary);
      background: #17497e;
      color: #fff;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .ui-switch-tab {
    li {
      color: rgba(0, 0, 0, 0.6);
      border: 1px solid #d3d7de;
      background-color: #f9f9f9;
      &:hover {
        border: 1px solid var(--color-primary);
        background: rgba(69, 151, 255, 0.1);
        color: var(--color-primary);
      }
    }
    .active {
      border: 1px solid var(--color-primary);
      background: rgba(26, 116, 231, 0.1);
      color: var(--color-primary);
    }
  }
}
</style>
