<template>
  <div>
    <div class="result-title">
      <p class="result-title-p f-18">
        <span>{{ activeIndexItem.indexName }}</span>
        <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm icon-details" @click="openDetailsDialog"></i>
      </p>
      <slot name="mid"></slot>
      <div>
        <div class="inline result-title-label mr-sm" v-if="isShowCanPlay">
          <Checkbox v-model="canPlay" true-value="1" false-value="2" @on-change="changeCanPlay"
            >排除取流(截图)失败设备</Checkbox
          >
        </div>
        <ui-label class="inline result-title-label mr-sm" label="行政区划" v-if="statisticType === 'REGION'">
          <api-area-tree
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            :filter-tree-code="activeIndexItem.taskConfig.selfRegionCode"
          ></api-area-tree>
        </ui-label>
        <ui-label class="inline result-title-label mr-sm" label="组织机构" v-else>
          <api-organization-tree
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            :filter-tree-code="activeIndexItem.taskConfig.selfOrgCode"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline result-title-label" label="检测时间">
          <!-- 结果对比 -->
          <DatePicker
            v-if="isShowDatePicker"
            v-model="examineTime"
            type="month"
            placeholder="请选择月"
            @on-change="changeDatePicker"
          ></DatePicker>
          <Select
            v-else
            class="width-md"
            v-model="batchId"
            placeholder="请选择时间"
            @on-change="toChangeBatchId"
            filterable
          >
            <Option v-for="(item, index) in batchInfoList" :value="item.batchId" :label="item.examineTime" :key="index">
            </Option>
          </Select>
        </ui-label>
      </div>
    </div>
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>
<script>
import dealSearch from '@/mixins/deal-search';
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters, mapMutations } from 'vuex';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';

export default {
  mixins: [dealSearch, evaluationoResultMixin],
  props: {
    testTime: { type: String, default: '' },
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
    // 默认行政区划或者组织机构字段
    defaultCodeKey: {
      type: String,
      default: '',
    },
    // 默认行政区划或者组织机构
    defaultCode: {
      type: String,
      default: '',
    },
    // 检测时间 是否显示为 DatePicker 组件
    isShowDatePicker: {
      type: Boolean,
      default: false,
    },
    // 是否显示  排除取流(截图)失败设备
    isShowCanPlay: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      currentRow: {},
      indexVisible: false,
      selectTree: {
        regionCode: '',
        orgCode: null,
      },
      selectOrgTree: {
        orgCode: null,
      },
      batchInfoList: [],
      batchId: '',
      examineTime: '',
      canPlay: '2', // 1 - 排除   2 - 不排除
    };
  },
  activated() {
    this.setIsRefresh(false);
    let index = this.batchInfoList.findIndex((item) => item.batchId === this.$route.query.batchId);
    if (index === -1) {
      this.setIsRefresh(true);
      this.getHistoryTask();
    }
  },
  async created() {
    await this.setAreaList();
    await this.setOrganizationList();
    this.setIsRefresh(false);
    await this.setDefaultOrgRegionCode();
    this.batchId = this.activeIndexItem.batchId;
    //重新改变 url
    this.toChangeRouterUrl({
      regionCode: this.selectTree.regionCode,
      orgCode: this.selectOrgTree.orgCode,
    });
    this.getHistoryTask();
  },
  methods: {
    ...mapMutations({
      setIsRefresh: 'governanceevaluation/setIsRefresh',
    }),
    ...mapActions({
      setAreaList: 'common/setAreaList',
      setOrganizationList: 'common/setOrganizationList',
    }),
    setDefaultOrgRegionCode() {
      let { regionCode, orgCode, statisticType } = this.$route.query;
      let { initialAreaList, initialOrgList } = this.getInitialAreaOrgList(); //evaluationoResultMixin.js
      if (statisticType === 'REGION') {
        let areaIndex = initialAreaList.findIndex((item) => item.regionCode === regionCode);
        if (areaIndex === -1) {
          this.selectTree.regionCode = initialAreaList[0]['regionCode'] || '';
        } else {
          this.selectTree.regionCode = regionCode;
          this.selectOrgTree.orgCode = '';
        }
      } else {
        let orgIndex = initialOrgList.findIndex((item) => item.orgCode === orgCode);
        if (orgIndex === -1) {
          this.selectOrgTree.orgCode = initialOrgList[0]['orgCode'] || '';
        } else {
          this.selectOrgTree.orgCode = orgCode;
          this.selectTree.regionCode = '';
        }
      }
    },
    /**
     *
     * @param val null
     * @description 抛出 on-change-code 事件 解决 defaultCode 没有变化导致watch不生效的问题
     */
    selectedOrgTree(val) {
      this.$emit('on-change-code', val.orgCode, 'orgCode');
      this.toChangeRouterUrl({ orgCode: val.orgCode });
    },
    /**
     *
     * @param val null
     * @description 抛出 on-change-code 事件 解决 defaultCode 没有变化导致watch不生效的问题
     */
    selectedArea(area) {
      this.$emit('on-change-code', area.regionCode, 'regionCode');
      this.toChangeRouterUrl({ regionCode: area.regionCode });
    },
    toChangeBatchId() {
      this.toChangeRouterUrl({
        batchId: this.batchId,
      });
    },
    toChangeRouterUrl(params) {
      // 设置默认初始值（为了给其余组件默认）- 放到route
      let defaultQuery = {
        ...this.$route.query,
      };
      Object.assign(defaultQuery, params);
      if (this.isShowCanPlay) {
        defaultQuery.canPlay = this.canPlay;
      }
      // this.$route.query.statisticType === 'REGION' ?
      // defaultQuery.regionCode =  '' :
      // defaultQuery.orgCode = null
      this.$router.push({
        name: this.$route.name,
        query: defaultQuery,
      });
    },
    async getHistoryTask() {
      let { showResult } = this.$route.query;
      let params = {
        indexId: this.activeIndexItem.indexId,
        batchId: this.activeIndexItem.batchId,
        access: 'REPORT_MODE',
        uuid: this.activeIndexItem.batchId,
        showResult: showResult ? null : 1, // 直接打开页面，query中不存在showResult字段值，接口入参需要该字段
      };
      this.$route.statisticType === 'REGION'
        ? (params.orgRegionCode = this.$route.query.regionCode)
        : this.$route.query.orgCode;
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getHistoryBatchInfo, params);
        this.batchInfoList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    // 指标详情
    openDetailsDialog() {
      let { indexType, indexName, indexId } = this.activeIndexItem;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
      this.indexVisible = true;
    },
    getMonthDate() {
      this.examineTime = this.$util.common.formatDate(new Date(), 'yyyy-MM');
    },
    changeDatePicker(time) {
      this.examineTime = time;
      this.toChangeRouterUrl({
        examineTime: this.examineTime,
      });
    },
    changeCanPlay(val) {
      this.toChangeRouterUrl({
        canPlay: val,
      });
    },
  },
  watch: {
    defaultCode: {
      handler(val) {
        if (!val || !this.defaultCodeKey) return false;
        const selectTreeKey = this.defaultCodeKey === 'regionCode' ? 'selectTree' : 'selectOrgTree';
        this.$set(this[selectTreeKey], this.defaultCodeKey, val);
        const obj = {};
        obj[this.defaultCodeKey] = val;
        this.toChangeRouterUrl(obj);
      },
      immediate: true,
    },
    isShowDatePicker: {
      handler(val) {
        if (val) {
          this.getMonthDate();
        } else {
          // 移除 examineTime
          if (this.$route.query?.examineTime) {
            let defaultQuery = {
              ...this.$route.query,
            };
            delete defaultQuery.examineTime;
            this.$router.push({
              name: this.$route.name,
              query: defaultQuery,
            });
          }
        }
      },
      deep: true,
    },
  },
  computed: {
    ...mapGetters({
      getInitialOrgList: 'common/getInitialOrgList',
      getInitialAreaList: 'common/getInitialAreaList',
      getUserInfo: 'user/getUserInfo',
      getIsRefresh: 'governanceevaluation/getIsRefresh',
    }),
    // 默认展示行政区划
    statisticType() {
      return this.$route.query.statisticType ? this.$route.query.statisticType : 'REGION';
    },
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
  },
};
</script>
<style lang="less" scoped>
.result-title {
  display: flex;
  justify-content: space-between;
  height: 55px;
  line-height: 55px;
  padding: 0 10px;
  align-items: center;
  border-bottom: 1px solid var(--border-color);

  .result-title-p {
    color: var(--color-display-title);
    position: relative;
    padding-left: 10px;
    font-weight: bold;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      position: absolute;
      width: 5px;
      height: 18px;
      background: var(--color-display-title-before);
      top: 50%;
      transform: translateY(-50%);
      left: 0;
    }
    .icon-details {
      width: 18px;
    }
  }

  .result-title-label {
    height: 55px;
    line-height: 55px;
  }
}
</style>
