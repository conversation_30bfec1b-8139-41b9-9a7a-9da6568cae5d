<!--
    * @FileDescription: 时空碰撞 - 分析结果
    * @Author: H
    * @Date: 2023/04/3
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="right_box" :class="{'rightBox-pack': packUpDown}">
        <div class="title">
            <p>分析结果</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="hint_title">
            共<span> {{ list.length }} </span> 条结果
        </div>
        <ul class="box-content">
            <li class="box-list" v-for="(item, index) in list" :key='index'>
                <div class="content-top">
                    <div class="content-top-img">
                        <img v-lazy="item[tabTitle[0].sceneImg]" alt="" />
                    </div>
                    <div class="content-top-right">
                        <div class="content-top-right-name">
                            <span class="ellipsis flex">
                                <ui-icon :type="listIcon[tabTitle[0].label][0]" :size="14"></ui-icon>
                                <span class="block">{{ item[tabTitle[0].first] || '--' }}</span>
                            </span>
                            <p class="list_title" @click="handlePeer($event,item)">
                                <span>{{ item.count || 0 }}</span>条
                            </p>
                        </div>
                        <span class="ellipsis">
                            <ui-icon :type="listIcon[tabTitle[0].label][1]" :size="14"></ui-icon>
                            <span class="bule" :class="{'block': !item[tabTitle[0].second]}" v-if="tabTitle[0].label == 'face'">{{ item[tabTitle[0].second] ? hiddenId(item[tabTitle[0].second],3,3) : '--' }}</span>
                            <span class="bule" :class="{'block': !item[tabTitle[0].second]}" v-else>{{ item[tabTitle[0].second] || '--' }}</span>
                        </span>
                        <span class="ellipsis">
                            <ui-icon :type="listIcon[tabTitle[0].label][2]" :size="14"></ui-icon>
                            <span class="orange" :class="{'block': !item[tabTitle[0].thirdly]}" v-if="tabTitle[0].label !== 'face'">{{ item[tabTitle[0].thirdly] ? hiddenId(item[tabTitle[0].thirdly],3,3) : '--' }}</span>
                            <span class="orange" :class="{'block': !item[tabTitle[0].thirdly]}" v-else>{{ item[tabTitle[0].thirdly] || '--' }}</span>
                        </span>
                    </div>
                </div>
                <!-- <div class="content-bottom">
                    <div class="iconList">
                        <opera-floor iconSec="icon-dangan2"></opera-floor>
                    </div>
                </div> -->
            </li>
            <ui-empty v-if="list.length === 0 && loading == false"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
        </ul>
        <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ packUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
import { querySpaceCollisionList } from '@/api/modelMarket';
import { myMixins } from '../../../mixins/index.js';
export default {
    name: '',
    mixins: [myMixins],
    components:{
            
    },
    props: {
        tablist: {
            type: Array,
            default: () => []
        },

    },
    data () {
        return {
            list: [],
            tabIndex: 0,
            listIcon:{
                'face': ['xingming', 'shenfenzheng', 'camera'],
                'vehicle': ['chepai', 'xingming', 'shenfenzheng']
            },
            loading: false,
            detailsList: {},
            searchData: [],
            tabTitle: []
        }
    },
    watch:{
            
    },
    computed:{
        // tabTitle() {
        //     return this.tablist
        // },
        
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(params) {
            this.loading = true;
            this.searchData = params;
            querySpaceCollisionList(params)
            .then(res => {
                this.detailsList = res.data;
                this.tabTitle = this.tablist.map((item, index) => {
                    return {...item, 'num': this.detailsList[item.tab]}
                })
                this.list = this.detailsList[this.tabTitle[0].tab];
            })
            .finally(()=>{
                this.loading = false;
            })
        },
        handleCancel() {
            this.$emit('cancel')
        },
        handlePeer($event, item) {
            $event.stopPropagation();
            let params = {
                'collisionFormList':  this.searchData.collisionFormList,
                queryId: item[this.tabTitle[0].transfer]
            }
            this.$emit('spaceTime', params, this.tabTitle[0].label)
        }
    }
}
</script>

<style lang='less' scoped>
@import './style/index';
.right_box{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    background: #fff;
    height: calc( ~'100% - 20px' );
    transition: height 0.2s ease-out;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    .hint_title{
        padding: 15px 20px 0 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0,0,0,0.6);
        span{
            color: rgba(44, 134, 248, 1);
        }
    }
    .box-content{
        padding: 0 15px;
        height: calc(~' 100% - 110px');
        overflow-y: auto;
        position: relative;
        .box-list{
            background: #F9F9F9;
            padding: 5px 10px 0px 5px;
            margin-top: 10px;
            &:hover{
                background: rgba(44, 134, 248, 0.1);
            }
            .content-top{
                display: flex;
                &-img{
                    width: 80px;
                    height: 80px;
                    background: #F9F9F9;
                    border: 1px solid #d3d7de;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img{
                        width: auto;
                        height: auto;
                        max-height: 80px;
                        max-width: 80px;
                    }
                }
                .content-top-right{
                    margin-top: 3px;
                    margin-left: 11px;
                    font-size: 14px;
                    width: calc( ~'100% - 91px' );
                    /deep/ .iconfont{
                        margin-right: 5px;
                    }
                    .bule{
                        color: #2C86F8;
                    }
                    .orange{
                        color: #F29F4C;
                    }
                    .block{
                        color: #000000;
                    }
                    &-name{
                        display: flex;
                        margin-bottom: 8px;
                        .flex{
                            flex:1;
                        }
                        .list_title{
                            font-size: 12px;
                            color: rgba(0,0,0,0.6);
                            cursor: pointer;
                            span{
                                color: rgba(44, 134, 248, 1);
                            }
                        }
                    }
                }
            }
            .content-bottom{
                display: flex;
                justify-content: space-between;
                margin-top: 5px;
                .iconList{
                    width: 80px;
                }
                .analyseIcon{
                    font-size: 12px;
                    color: #5584FF;
                    cursor: pointer;
                }
            }
        }
    }
    .footer{
        // color: #000000;
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translate(-50%, 0px);
        background: #fff;
        width: 100%;
    }
}
.rightBox-pack{
    height: 80px;
    transition: height 0.2s ease-out;
    overflow: hidden;
}
</style>
