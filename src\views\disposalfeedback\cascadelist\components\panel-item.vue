<template>
  <div class="panel-item">
    <p class="panel-title" :class="{ active: item.active }">
      <i class="icon-font icon-gonganju-01"></i>
      <span>{{ item.orgName }}</span>
    </p>
    <div class="panel-statics f-16" :class="{ active: item.active }">
      <ul class="panel-statics-ul">
        <li>
          <p class="f-12">问题</p>
          <p class="number f-14">{{ item.issueCount }}</p>
        </li>
        <li>
          <p class="f-12">已下发</p>
          <p class="number f-14">{{ item.publishCount }}</p>
        </li>
        <li>
          <p class="f-12">已处理</p>
          <p class="number f-14">{{ item.handleCount }}</p>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    item: {},
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.panel-item {
  width: 243px;
  border: 1px solid #1664b4;
  border-radius: 5px;
  cursor: pointer;
  .icon-gonganju-01 {
    // color: #19d5f6;
    margin-right: 5px;
    vertical-align: top;
    font-size: 22px;
    background-image: -webkit-linear-gradient(bottom, #19dff6, #3680d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .panel-title {
    height: 30px;
    line-height: 30px;
    background: #1d518d;
    color: #f5f5f5;
    padding: 0 10px;
    &.active {
      background: var(--color-primary);
      border-bottom: 1px solid #5daafb;
    }
  }
  .panel-statics {
    background: #042346;
    &.active {
      background: var(--color-primary);
      color: #fff;
    }
    .panel-statics-ul {
      padding: 10px;
      display: flex;
      justify-content: space-between;
      > li {
        color: #f5f5f5;
        text-align: center;
        .number {
          color: #19d5f6;
        }
      }
    }
  }
}
</style>
