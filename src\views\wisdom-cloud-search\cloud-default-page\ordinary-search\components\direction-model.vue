<template>
	<ui-modal v-model="modalShow" :r-width="dialogData.rWidth" :title="dialogData.title" :footer-hide="true">
		<div class="map-container">
			<MapBase ref='map' class="map" v-if="positionPoints.length>0" cutIcon='track' :mapLayerConfig="{ showLatestLocation: true , } " :positionPoints="positionPoints" />
		</div>
	</ui-modal>
</template>
<script>
	import MapBase from '@/components/map/search-center-position.vue'

	export default {
		components: {
			MapBase
		},
		props: {

		},
		data() {
			return {
				modalShow: false,
				dialogData: {
					title: "定位",
					rWidth: 1200,
				},
				positionPoints: []
			};
		},
		created() {
		},
		mounted() {
		},
		methods: {
			show(val) {
				this.positionPoints = []
				let pointObj = val
				pointObj.active = 'red-position'
				this.positionPoints.push(pointObj)
				this.modalShow = true;
			},
		},
	};
</script>
<style lang="less" scoped>
	.map-container {
		width: 100%;
		height: 60vh;
	}
</style>
