<template>
  <div class="history-container">
    <div class="history-wrapper height-full">
      <div class="header mb-md">
        <div>
          <i class="icon-font icon-shujujiance f-18 vt-middle"></i>
          <span class="ml-sm f-16 font-white vt-middle"><slot>联网平台对账</slot></span>
        </div>
        <div>
          <ui-label class="inline" label="行政区划">
            <api-area-tree
              :select-tree="selectTree"
              @selectedTree="selectedArea"
              :filter-tree-code="filterCivilCode"
              placeholder="请选择行政区划"
            ></api-area-tree>
          </ui-label>
        </div>
      </div>
      <div class="table-wrapper">
        <ui-table class="ui-table" :table-columns="tableColumns" :table-data="tableData" :loading="tableLoading">
          <template #deviceTotal="{ row, index }">
            <span>{{ row.deviceTotal || 0 }}</span>
            <span
              :class="['ml-sm',row.differenceValueByDeviceTotal >=0 ?'color-up': 'color-down' ]">
              ({{`${row.differenceValueByDeviceTotal >0 ? `+${row.differenceValueByDeviceTotal}` : row.differenceValueByDeviceTotal }`}})
            </span>
          </template>
          <template #advice="{ row, index }">
            <div>
              <ui-venn :options="vennOptions"></ui-venn>
            </div>
          </template>
          <template #action="{ row, index }">
            <create-tabs
              :componentName="themDataDetail.componentName"
              :importantTabName="themDataDetail.title"
              :tabs-text="themDataDetail.text"
              @selectModule="selectModule"
              :tabs-query="{
                batchId: row.batchId,
                id: row.id,
                civilCode: row.civilCode,
              }"
              class="inline mr-md"
            >
              <ui-btn-tip icon="icon-shebeimingxi mr-md" content="明细"></ui-btn-tip>
            </create-tabs>
          </template>
        </ui-table>

        <ui-table
          class="ui-table table-total auto-fill"
          ref="tableTotal"
          :show-header="false"
          :table-columns="totalTableColumns"
          :table-data="totalTableData"
          :loading="tableLoading"
          :span-method="handleSpan"
        >
        </ui-table>
      </div>
    </div>
  </div>
</template>
<script>
import assetcomparison from '@/config/api/assetcomparison';
import { TAB_LIST, VIDEO_DEVICE_REVOCATION } from '@/views/viewassets/assetcompare/modules/enum';
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  name: 'history',
  data() {
    return {
      //create-tab
      componentName: null,
      componentLevel: 0,
      themDataDetail: {
        componentName: 'SupSubComparisonResult', // 需要跳转的组件名
        text: '对账明细', // 跳转页面标题
        title: '上下级对账-对账明细',
        type: 'view',
      },

      selectTree: {
        regionCode: '',
      },
      searchData: {
        civilCode: '',
      },
      tableLoading: false,
      tableData: [
      ],
      totalTableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'center' },
        {
          title: '比对时间',
          key: 'remark',
          minWidth: 100,
          tooltip: true,
          align: 'center',
          className: 'total-score f-16',
        },
        { title: '设备总量', key: 'deviceTotal', minWidth: 100, tooltip: true, align: 'left', className: 'total-score f-16' },
        { title: '撤销 ', key: 'revocationQuantity', minWidth: 100, tooltip: true, align: 'left', className: 'total-score f-16' },
        { title: '新增', key: 'addQuantity', minWidth: 100, tooltip: true, align: 'left', className: 'total-score f-16' },
        { title: '修改', key: 'updateQuantity', minWidth: 100, tooltip: true, align: 'left', className: 'total-score f-16' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          align: 'center',
        },
      ],
      totalTableData: [],
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'center' },
        { title: '比对时间', key: 'comparisonDate', minWidth: 100, tooltip: true, align: 'left' },
        { title: '设备总量', slot: 'deviceTotal', minWidth: 100, tooltip: true, align: 'left' },
        { title: '撤销 ', key: 'revocationQuantity', minWidth: 100, tooltip: true, align: 'left' },
        { title: '新增', key: 'addQuantity', minWidth: 100, tooltip: true, align: 'left' },
        { title: '修改', key: 'updateQuantity', minWidth: 100, tooltip: true, align: 'left' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          align: 'center',
        },
      ],
    };
  },
  computed: {
    tabName() {
      let { type } = this.$route.query;
      let find = TAB_LIST.find((item) => item.value === type);
      if (!type || !find) return '';
      return find.label;
    },
    filterCivilCode() {
      return this.$route.query.civilCode || '';
    },
  },
  created() {
    this.getStatInfo()
    this.setDefaultRegion()
  },
  methods: {
    setDefaultRegion() {
      let { civilCode } = this.$route.query;
      this.searchData.civilCode = civilCode;
      this.selectTree.regionCode = civilCode;
    },

    handleSpan({ column, rowIndex, columnIndex }) {
      if (rowIndex === 0 && columnIndex === 1) {
        return [1, 2];
      } else if (rowIndex === 0 && columnIndex === 0) {
        return [0, 0];
      }
      if (rowIndex === 0 && columnIndex === 5) {
        return [1, 2];
      } else if (rowIndex === 0 && columnIndex === 6) {
        return [0, 0];
      }
    },
    selectModule(name) {
      if (!!this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
      this.$emit('on-change-component', this.componentName);
    },

    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
      this.getStatInfo();
    },
    async getStatInfo() {
      let { batchId, civilCode} = this.$route.query
      this.tableLoading = true;
      let params = {
        access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
        batchId: batchId, // 任务执行批次id
        displayType: 'REGION', // 显示方式:ORG,组织机构;REGION,行政区划,可用值:ORG,REGION
        indexId: VIDEO_DEVICE_REVOCATION.indexId,
        orgRegionCode: civilCode,
        // "indexId": "4029",
        // "batchId": "D#4936#0000000014",
        // "access": "TASK_RESULT",
        // "displayType": "REGION",
        // "pageNumber": 1,
        // "pageSize": 20,
        // "orgRegionCode": "320000"
      }
      try {
        let {data: {data}} = await this.$http.post(evaluationoverview.getDetailData, params)
        if (data.entities && data.entities.length > 2) {
          this.tableData = data.entities.slice(0, data.entities.length-2)
          this.totalTableData = data.entities.slice(data.entities.length-1, data.entities.length)
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.tableLoading = false;
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style scoped lang="less">
.history-container {
  position: relative;
  height: 100%;
  background: #071b39;

  .history-wrapper {
    padding: 15px 20px;

    .header {
      display: flex;
      justify-content: space-between;
      padding-bottom: 15px;
      border-bottom: 1px solid #074277;

      .icon-font {
        background: linear-gradient(180deg, #19dff6 0%, #3680d4 100%);
        -webkit-background-clip: text;
        color: #0000;
      }
    }

    .table-wrapper {
      position: relative;
      height: calc(~'100% - 110px');
      .ui-table {
        height: 100%;
        @{_deep} .target-only {
          color: #d66418;
        }

        @{_deep} .source-only {
          color: #b1b836;
        }

        @{_deep} .diff {
          color: #bc3c19;
        }

        @{_deep} .color-up {
          color: #0E8F0E;
        }
        @{_deep} .color-down {
          color: #BC3C19;
        }

        @{_deep} .icon-shebeimingxi,
        .icon-lishijilu-01 {
          color: #2b84e2 !important;
        }

        @{_deep} .icon-lishijilu-01 {
          font-size: 16px !important;
        }

        @{_deep} .link {
          color: #2b84e2;
          cursor: pointer;
          text-decoration: underline;
        }

        @{_deep} .dis-link {
          text-decoration: none;
        }

        @{_deep}.total {
          font-size: 22px;
        }

        @{_deep}.total-score {
          color: #05fef5;
        }

        @{_deep}.ivu-table-wrapper {
          height: calc(~'100% - 1px') !important;
        }
        @{_deep} .ivu-table {
          .ivu-table-body {
            overflow-x: hidden !important;
            border-right: 0;
          }
        }
        @{_deep}.ivu-table-overflowX {
          & ~ .ivu-table-fixed {
            height: 100%;
            > .ivu-table-fixed-body {
              // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
              height: calc(~'100% - 150px - 10px') !important;
            }
          }
        }

        @{_deep}.ivu-table-fixed {
          height: 100%;
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度
            height: calc(~'100% - 150px - 10px') !important;
          }
        }
      }

      .table-total {
        @{_deep}.ivu-table-overflowX {
          & ~ .ivu-table-fixed {
            height: calc(~'100% - 11px');
            > .ivu-table-fixed-body {
              // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
              height: 100% !important;
            }
          }
        }

        @{_deep}.ivu-table-fixed {
          height: 100%;
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度
            height: 100% !important;
          }
        }
      }
    }
  }
}
</style>
