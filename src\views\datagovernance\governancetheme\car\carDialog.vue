<template>
  <div>
    <ui-modal ref="modal" :title="title">
      <!-- 数据输入 -->
      <div v-if="curItem == 1">
        <p>请选择原始待治理数据：</p>
        <RadioGroup>
          <Radio label="人工确认车牌号"></Radio>
          <Radio label="多算法自动选择"></Radio>
        </RadioGroup>
        <p>治理方式：</p>
        <RadioGroup>
          <Radio label="统一流程治理"></Radio>
          <Radio label="分流程治理"></Radio>
        </RadioGroup>
      </div>

      <!-- 车辆结构化 2  -->
      <!-- 图像模糊检测 3-->
      <!-- 车辆结构化属性完整性与准确检性测优化 6 -->
      <!-- 车牌识别准确性检测优化配置 5-->
      <div v-if="curItem == 2 || curItem == 3 || curItem == 5 || curItem == 6">
        <p v-if="curItem == 2 || curItem == 3" class="p">请选择车辆结构化算法：</p>
        <CheckboxGroup v-model="selectFactrys" v-if="curItem == 2 || curItem == 3">
          <Row>
            <Col span="12" v-for="(item, index) in factryList" :key="index">
              <Checkbox :label="item.label"></Checkbox>
            </Col>
          </Row>
        </CheckboxGroup>
        <div class="quality" v-if="curItem == 3">
          图片质量分阈值
          <Input v-model="value" placeholder="值" style="width: 100px" size="small" />
        </div>

        <div class="explain" v-if="curItem == 3 || curItem == 5">
          说明：如果选择的多种算法提取图片特征值的质量分均低于阈值，则图像模糊
        </div>

        <div v-if="curItem == 6">
          <!-- <p class="p">请配置需结构化属性：</p> -->
          <!-- <uiTag :data="configList" @close="needClose" @add="needAdd" /> -->
          <!-- <ul>
            <li v-for="(item, index) in 3" :key="index">
              <img class="close" src="@/assets/img/close.png" alt="" srcset="" />
              车辆品牌{{index}}
            </li>
            <li class="add">
              <Icon type="ios-add" /> 新增
            </li>
          </ul> -->
          <p class="p" style="margin-bottom: 12px">请配置需检测准确性的结构化属性：</p>
          <uiTag :data="configList" @close="testingClose" @add="testingAdd" />
          <!-- <ul>
            <li v-for="(item, index) in 3" :key="index">
              <img class="close" src="@/assets/img/close.png" alt="" srcset="" />
              车辆品牌{{index}}
            </li>
            <li class="add">
              <Icon type="ios-add" /> 新增
            </li>
          </ul> -->
          <div class="explain">说明：如果选择的算法有多数算法检测属性值一致，则属性正确</div>
        </div>

        <div class="quality" v-if="curItem == 5 || curItem == 6">
          车牌号存疑的图像处理方式：
          <RadioGroup>
            <!-- <Radio label="人工确认车牌号"></Radio> -->
            <Radio label="多算法自动选择"></Radio>
          </RadioGroup>
        </div>
      </div>

      <!-- 图像上传及时性配置 -->
      <div v-if="curItem == 4">
        <div class="quality">
          重点人脸卡口数据时延：不超过
          <Input v-model="value" placeholder="值" style="width: 100px" size="small" />
          <Select v-model="model1" style="width: 80px; margin-left: 10px" size="small">
            <Option v-for="item in timeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div>
        <div class="quality">
          普通人脸卡口数据时延：不超过
          <Input v-model="value" placeholder="值" style="width: 100px" size="small" />
          <Select v-model="model1" style="width: 80px; margin-left: 10px" size="small">
            <Option v-for="item in timeList" :value="item.value" :key="item.value">{{ item.label }}</Option>
          </Select>
        </div>
      </div>
      <template slot="footer">
        <Button type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>

    <!-- <ui-modal> -->
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import { carSelectList, configList } from './car.js';
import api from '@/config/api/car-threm.js';
import uiTag from '../components/ui-tag';
export default {
  name: 'carDialog',
  props: {},
  data() {
    return {
      itemList: [], // 页面所有块集合
      currentItem: null, // 当前操作的块
      factryList: [], // 厂商列表
      selectFactryList: [], // 已选中厂商列表
      selectFactrys: [], // 厂商v-model
      title: '',
      curItem: 1,
      carSelectList: carSelectList,
      configList: configList,
      timeList: [
        { value: 1, label: '时' },
        { value: 2, label: '分' },
        { value: 3, label: '秒' },
      ],
    };
  },
  async created() {
    await this.queryCarList();
    if (this.algorithmList.length == 0) await this.getAlldicData();
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    showModal(val) {
      switch (val) {
        case 1:
          this.title = '数据输入';
          break;
        case 2:
          this.title = '车辆结构化';
          break;
        case 3:
          this.title = '图像模糊检测';
          break;
        case 4:
          this.title = '图像上传及时性配置';
          break;
        case 5:
          this.title = '车牌识别准确性检测优化配置';
          break;
        case 6:
          this.title = '车辆结构化属性完整与准确检性测优化';
          break;
      }
      if (val == 2) {
        this.currentItem = this.itemList.filter((item) => {
          return item.componentName == '车辆结构化';
        })[0];
        this.queryFactryList();
      }

      this.curItem = val;
      this.$refs.modal.modalShow = true;
    },

    // 请配置需结构化属性
    needAdd() {},

    // 请配置需结构化属性
    needClose() {},

    // 请配置需检测准确性的结构化属性
    testingAdd() {},

    // 请配置需检测准确性的结构化属性
    testingClose() {},

    async queryCarList() {
      await this.$http
        .get(api.queryCarList + '3')
        .then((res) => {
          if (res.data.code == 200) {
            this.itemList = res.data.data;
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    queryFactryList() {
      this.$http
        .get(api.queryCsList + this.currentItem.componentParam)
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data;

            list.forEach((item) => {
              var obj = {
                algorithmType: item.algorithmType,
                algorithmVendorType: item.algorithmVendorType,
                label: this.getAlgorithmLabel(item.algorithmVendorType),
              };

              this.factryList.push(obj);
            });
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },
  },
  watch: {},
  components: { uiTag },
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}
ul {
  overflow: hidden;
  li {
    position: relative;
    float: left;
    font-size: 14px;
    padding: 6px 16px;
    background: var(--color-primary);
    color: #fff;
    border-radius: 4px;
    margin-right: 10px;
    margin-top: 10px;
  }

  .add {
    border: 1px solid #037cbe;
    background: transparent;
    padding: 6px 16px;
    cursor: pointer;
  }

  .close {
    position: absolute;
    right: -3px;
    top: -3px;
    opacity: 0.8;
    cursor: pointer;
  }
  .close:hover {
    opacity: 1;
  }
}
</style>
