<template>
  <div class="index-container f-14">
    <slot name="front"></slot>
    <Collapse v-model="activeCollapse" class="dropdown" accordion>
      <Panel :name="items[fileNameObject.id]" v-for="items in indexList" :key="items[fileNameObject.id]">
        <span class="icon-font f-14 inline" :class="items.iconName"></span>
        <span class="ml-sm ellipsis">{{ items[fileNameObject.name] }}</span>
        <template #content>
          <div
            :class="activeValue == item[fileNameObject.id] ? 'active' : ''"
            @click="clickItem(item)"
            class="content f-14 ellipsis dis-select"
            v-for="(item, index) in items[fileNameObject.children] || []"
            :key="`${item[fileNameObject.id]}-${index}`"
            :title="item[fileNameObject.name]"
          >
            {{ item[fileNameObject.name] }}
          </div>
        </template>
      </Panel>
    </Collapse>
  </div>
</template>

<script>
export default {
  name: 'index-select',
  components: {},
  props: {
    data: {
      required: true,
      default: () => {
        return {};
      },
    },
    // 传入字段名称
    fileNameObject: {
      default: () => {
        return {
          id: 'indexId',
          name: 'indexName',
          children: 'children',
        };
      },
    },
    defaultActiveCollapse: {
      default: '',
    },
    value: {},
  },
  data() {
    return {
      activeCollapse: '',
      activeValue: '',
      indexList: [],
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.activeValue = val;
      },
    },
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.indexList = val;
      },
    },
    defaultActiveCollapse(val) {
      this.activeCollapse = val;
    },
  },
  filter: {},
  created() {},
  methods: {
    clickItem(val) {
      /*if(!val.batchId){
        return this.$Message.warning('暂无检测结果')
      }*/
      if (this.activeValue === val[this.fileNameObject.id]) return;
      this.activeValue = val[this.fileNameObject.id];
      this.$emit('change', val);
      this.$emit('input', val[this.fileNameObject.id]);
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .index-container {
    @{_deep} .ivu-collapse {
      .ivu-collapse-item {
        .icon-font {
          background: var(--color-primary);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .ivu-collapse-header {
          color: rgba(0, 0, 0, 0.8);
          background: #ffffff;
        }
        &.ivu-collapse-item-active .ivu-collapse-header {
          background: #f2f6fc;
        }
        .ivu-collapse-content {
          background: transparent;
        }
        .content {
          color: rgba(0, 0, 0, 0.8);
          background: #f2f6fc;
          &:hover {
            background: var(--color-primary);
            color: #ffffff;
          }
        }
        .active {
          background: var(--color-primary);
          color: #ffffff;
        }
        &.ivu-collapse-item-active {
          //background: var(--color-primary);
          //color: #FFFFFF;
        }
      }
    }
  }
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.index-container {
  position: relative;
  display: inline-block;
  height: 100%;
  overflow: scroll;
  .dropdown {
    width: 220px;
    overflow-y: auto;
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        i {
          float: right;
          line-height: 38px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: #0a254d;
        .ivu-collapse-content-box {
          padding: 0;
        }
      }
      .content {
        height: 42px;
        line-height: 42px;
        cursor: pointer;
        padding: 0 16px;
        color: #ffffff;
        &:hover {
          background: var(--color-primary);
        }
      }
      &.ivu-collapse-item-active {
        background: #0a254d;
      }
    }
  }
  @{_deep}.icon-font {
    width: 20px;
  }
  .active {
    background: var(--color-primary);
  }
}
</style>
