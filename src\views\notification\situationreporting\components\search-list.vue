<template>
  <div>
    <div class="search-list">
      <ui-label class="inline right-margin mb-lg" label="设备编码">
        <Input
          v-model="searchData.deviceId"
          class="input-width"
          :placeholder="`请输入设备编码`"
          @on-blur="changeBlur"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="设备名称">
        <Input
          v-model="searchData.deviceName"
          class="input-width"
          :placeholder="`请输入设备名称`"
          clearable
          @on-blur="changeBlur"
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbdwlx">
        <Select
          class="input-width"
          v-model="searchData.sbdwlxList"
          :placeholder="`请选择`"
          clearable
          multiple
          :max-tag-count="1"
          @on-change="changeBlur"
        >
          <Option
            v-for="(item, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlxList' + bdindex"
            :value="item.dataKey"
            >{{ item.dataValue }}</Option
          >
        </Select>
      </ui-label>

      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbgnlx">
        <Select
          class="input-width"
          v-model="searchData.sbgnlxList"
          :placeholder="`请选择`"
          clearable
          multiple
          :max-tag-count="1"
          @on-change="changeBlur"
        >
          <!-- <Option
						value="1"
						v-if="[4001, 4002, 4003, 4004, 4006, 4007, 4008, 4009, 4010, 4011, 4012].includes(moduleAction.indexId)"
					>视频监控</Option> -->
          <Option
            v-for="(item, bdindex) in dictData['sxjgnlx_receive']"
            :key="'sbgnlxList' + bdindex"
            :value="item.dataKey"
            >{{ item.dataValue }}</Option
          >
        </Select>
      </ui-label>
      <!-- 设备状态 -->
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.phyStatus">
        <Select
          class="input-width"
          v-model="searchData.phyStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
          @on-change="changeBlur"
        >
          <Option v-for="(item, index) in dictData['phystatusList']" :key="index" :value="item.dataKey">
            {{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="上报状态">
        <Select
          @on-change="changeBlur"
          class="input-width"
          v-model="searchData.cascadeReportStatus"
          placeholder="请选择上报状态"
          clearable
        >
          <Option key="0" value="0">未上报</Option>
          <Option key="1" value="1">已上报</Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="是否检测">
        <Select
          @on-change="changeBlur('isCheck')"
          class="input-width"
          v-model="searchData.isCheck"
          placeholder="请选择"
          clearable
        >
          <Option value="">请选择</Option>
          <Option value="1">是</Option>
          <Option value="0">否</Option>
        </Select>
      </ui-label>
      <!-- 基础信息状态 -->
      <ui-label class="inline right-margin mb-lg" label="基础信息状态">
        <Select
          class="width-sm leftSelect"
          v-model="searchData.baseCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :disabled="searchData.isCheck != '1'"
          @on-change="changeBlur('base')"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          @on-change="changeBlur"
          :disabled="searchData.isCheck !== '1' || searchData.baseCheckStatus !== '1'"
          class="width-sm rightSelect"
          v-model="searchData.baseErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.basic" :key="item + index">
            {{ item }}
          </Option>
        </Select>
      </ui-label>
      <!-- 视频流数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视频流数据状态">
        <Select
          class="width-sm leftSelect"
          v-model="searchData.videoCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :disabled="searchData.isCheck != '1'"
          @on-change="changeBlur('view')"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          :disabled="searchData.isCheck !== '1' || searchData.videoCheckStatus !== '1'"
          @on-change="changeBlur"
          class="width-sm rightSelect"
          v-model="searchData.videoErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.video" :key="item + index">
            {{ item }}
          </Option>
        </Select>
      </ui-label>
      <!-- 视图数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视图数据状态">
        <Select
          class="width-sm leftSelect"
          v-model="searchData.viewCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :disabled="searchData.isCheck != '1'"
          @on-change="changeBlur('video')"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          class="width-sm rightSelect"
          v-model="searchData.viewErrorMessageList"
          placeholder="请选择错误类别"
          multiple
          clearable
          :disabled="searchData.isCheck !== '1' || searchData.viewCheckStatus !== '1'"
          @on-change="changeBlur"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.view" :key="item + index">{{ item }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-sm" label="设备重点类型">
        <Select
          @on-change="changeBlur"
          class="input-width"
          v-model="searchData.isImportant"
          placeholder="请选择设备重点类型"
          clearable
        >
          <Option key="0" value="0">普通设备</Option>
          <Option key="1" value="1">重点设备</Option>
        </Select>
      </ui-label>
      <ui-label label="报备状态" class="inline mb-sm">
        <Select class="width-md" v-model="searchData.examReportStatus" placeholder="请选择报备状态" clearable>
          <Option value="0">未报备</Option>
          <Option value="1">已报备</Option>
        </Select>
      </ui-label>
      <br />
      <div class="data-list mb-sm">
        <div class="select-tabs mr-lg">
          <span class="fl mr-sm">标签类型</span>
          <ui-select-tabs
            class="ui-select-tabs"
            :list="tagList"
            @selectInfo="selectInfo"
            ref="uiSelectTabs"
          ></ui-select-tabs>
        </div>
        <div class="inline">
          <Button type="primary" class="mr-sm" @click="search">查询</Button>
          <Button type="default" @click="reset">重置</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
export default {
  name: 'search-list',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
    searchConditions: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        deviceId: '', // 设备编码
        deviceName: '', // 设备名称
        sbdwlxList: [], // 设备点位类型
        sbgnlxList: [], // 设备功能类型
        phyStatus: '',
        cascadeReportStatus: '',
        tagIds: [],
        isCheck: '',
        baseCheckStatus: '',
        viewCheckStatus: '',
        videoCheckStatus: '',
        baseErrorMessageList: [],
        viewErrorMessageList: [],
        videoErrorMessageList: [],
        isImportant: '',
        examReportStatus: '',
      },
      tagList: [],
      BASICSErrorList: [],
      IMAGEErrorList: [],
      VIDEOErrorList: [],
      errorMessageMap: {
        basic: [],
        view: [],
        video: [],
      },
      errTypes: ['BASICS', 'IMAGE', 'VIDEO'],
      // errTypes: ['basic', 'video', 'view'],
    };
  },
  async mounted() {
    await this.copySearchDataMx(this.searchData);
    await this.getTagList();
    this.errTypes.forEach((item) => {
      this.queryErrorList(item);
    });
  },
  methods: {
    search() {
      this.$emit('search', this.searchData);
    },
    reset() {
      this.checkStatuses = [];
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('reset', this.searchData);
      });
      this.$refs.uiSelectTabs.reset();
    },
    // 请求3个异常列表
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((item) => item.id);
      this.search();
    },
    // 标签类型选中状态回显
    setTagsStatus(tagids) {
      this.tagList = this.tagList.map((item) => {
        if (tagids.includes(item.id)) {
          this.$set(item, 'select', true);
        }
        return item;
      });
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        this.tagList = this.$util.common.deepCopy(
          this.allTagList.map((row) => {
            return {
              name: row.tagName,
              id: row.tagId,
            };
          }),
        );
      } catch (err) {
        console.log(err);
      }
    },
    checkChange() {
      this.baseCheckStatus = '';
      this.viewCheckStatus = '';
      this.videoCheckStatus = '';
      this.search();
    },
    // 同时请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.errorMessageMap.basic = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.errorMessageMap.view = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.errorMessageMap.video = res3.data.data;
    },
    initSearch() {},
    changeBlur(optionType) {
      if (optionType) {
        if (optionType && !['1'].includes(this.searchData[optionType])) {
          if (optionType === 'isCheck') {
            this.searchData = {
              ...this.searchData,
              baseCheckStatus: '',
              viewCheckStatus: '',
              videoCheckStatus: '',
              baseErrorMessageList: [],
              viewErrorMessageList: [],
              videoErrorMessageList: [],
            };
          } else {
            this.searchData[`${optionType}ErrorMessageList`] = [];
          }
        }
      }
      this.$nextTick(() => {
        this.search();
      });
    },
  },

  watch: {
    searchConditions(val) {
      if (val) {
        Object.keys(this.searchData).forEach((key) => {
          this.searchData[key] = val[key];
        });
        if (!!this.searchData.tagIds && this.searchData.tagIds.length) {
          this.setTagsStatus(this.searchData.tagIds);
        }
      }
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
.right-margin {
  margin-right: 30px;
}
.search-list {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.right-margin {
  margin-right: 15px;
}
.input-width {
  width: 140px;
}
.select-input-width {
  width: 163px;
}
.align-flex {
  display: flex;
  align-items: center;
}
.leftSelect {
  width: 120px !important;
}
.rightSelect {
  width: 240px !important;
}
.data-list {
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-label);
  font-size: 14px;
  //   .ui-select-tabs {
  //     margin-left: 70px;
  //   }
}
.select-tabs {
  flex: 1;
  display: flex;
  align-items: center;
}
.ui-select-tabs {
  flex: 1;
}
.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
