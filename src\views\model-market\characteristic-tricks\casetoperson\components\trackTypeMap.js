const loadImg = (url) => require(`@/assets/img/default-img/${url}.png`);

const creatElementSubwayCardUI = (h, { data, marketData, item }) => {
  const showLabel = baseTrackTypeMap[item.trackType].showLabel || {};
  const labelMap = showLabel[marketData.label] || {};
  const showBg = item.name.indexOf(labelMap.label) > -1;
  return h(
    "span",
    {
      style: showBg
        ? {
            color: "#fff",
            background: "#f29f4c",
            lineHeight: 1.5,
          }
        : {},
    },
    [
      h(
        "span",
        { style: showBg ? {} : { color: "rgba(0, 0, 0, 0.6)" } },
        `${item.name}：`
      ),
      `${data[item.value] || ""}`,
    ]
  );
};

export const baseTrackTypeMap = {
  face: {
    name: "人脸抓拍",
    detailAttr: "faceCaptureVo",
    defaultImg: loadImg("image_error"),
  },
  vehicle: {
    name: "车辆抓拍",
    detailAttr: "vehicleCaptureVo",
    defaultImg: loadImg("vehicle_default"),
  },
  humanBody: {
    name: "人体抓拍",
    detailAttr: "",
    defaultImg: loadImg("humanBody_default"),
  },
  nonMotor: {
    name: "非机动车抓拍",
    detailAttr: "",
    defaultImg: loadImg("nonMotor_default"),
  },
  hotel: {
    name: "住宿",
    detailAttr: "hotelAccommodationVo",
    defaultImg: loadImg("hotel_default"),
    showLabel: {
      HOTEL_IN: {
        label: "入住",
        color: "#2C86F8",
      },
      HOTEL_OFF: {
        label: "退房",
        color: "#1FAF81",
      },
    },
  },
  internetAccess: {
    name: "上网",
    detailAttr: "netBarUserInfoVo",
    defaultImg: loadImg("internetAccess_default"),
    showLabel: {
      NET_BAR_IN: {
        label: "上机",
        color: "#2C86F8",
      },
      NET_BAR_OFF: {
        label: "下机",
        color: "#1FAF81",
      },
    },
  },
  subwayCardSwiping: {
    name: "地铁刷卡",
    detailAttr: "subwayCardVo",
    defaultImg: loadImg("subwayCardSwiping_default"),
    showLabel: {
      SUBWAY_IN: {
        label: "上车",
        color: "#2C86F8",
      },
      SUBWAY_OFF: {
        label: "下车",
        color: "#1FAF81",
      },
    },
  },
};

const detailMap = {
  internetAccess: [
    {
      label: "网吧名称",
      value: "wbmccn",
    },
    {
      label: "用户代码",
      value: "yhid",
    },
    {
      label: "姓名",
      value: "xm",
    },
    {
      label: "身份证号",
      value: "sfzh",
    },
    {
      label: "民族",
      value: "mz",
    },
    {
      label: "上网人员编号",
      value: "swrybh",
    },
    {
      label: "卡号",
      value: "kh",
    },
    {
      label: "",
      name: "上机时间",
      value: "sjsj",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "",
      name: "下机时间",
      value: "xjsj",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "IP地址",
      value: "ip",
    },
    {
      label: "机器号",
      value: "jqh",
    },
  ],
  hotel: [
    {
      label: "旅客姓名",
      value: "name",
    },
    {
      label: "入住房号",
      value: "roomId",
    },
    {
      label: "详细地址",
      value: "address",
    },
    {
      label: "",
      name: "入住时间",
      value: "checkInTime",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "",
      name: "退房时间",
      value: "departTime",
      renderItem: creatElementSubwayCardUI,
    },
  ],
  subwayCardSwiping: [
    {
      label: "姓名",
      value: "xm",
    },
    {
      label: "身份证号",
      value: "sfzh",
    },
    {
      label: "卡号",
      value: "kh",
    },
    {
      label: "",
      name: "上车站名",
      value: "getOnStationName",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "",
      name: "上车时间",
      value: "getOnTime",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "",
      name: "下车站名",
      value: "getOffStationName",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "",
      name: "下车时间",
      value: "getOffTime",
      renderItem: creatElementSubwayCardUI,
    },
    {
      label: "上车地铁坐标",
      value: "getOnGeoPoint",
      renderItem(h, { data }) {
        const getOnGeoPoint = data.getOnGeoPoint || { lon: "", lat: "" };
        return h("span", {}, `${getOnGeoPoint.lon},${getOnGeoPoint.lat}`);
      },
    },
    {
      label: "下车地铁坐标",
      value: "getOffGeoPoint",
      renderItem(h, { data }) {
        const getOffGeoPoint = data.getOffGeoPoint || { lon: "", lat: "" };
        return h("span", {}, `${getOffGeoPoint.lon},${getOffGeoPoint.lat}`);
      },
    },
  ],
};

/**
 * @description: 獲取撒點數據詳情字段
 * @param { String} type 類型
 * @return {*} 詳情字段數組
 */
export const getTrackTypeDeatilMap = (type = 1) => {
  return {
    ...baseTrackTypeMap[type],
    list: (detailMap[type] || []).map((item) => ({ ...item, trackType: type })),
  };
};
