<template>
  <div class="camera-module">
    <ui-modal v-model="visible" :title="title" :styles="style">
      <Row class="camera-content">
        <Col :span="6" class="tree">
          <ui-search-tree
            ref="uiSearchTree"
            class="ui-search-tree"
            placeholder="请输入组织机构名称"
            :check-strictly="true"
            :show-checkbox="true"
            :scroll="380"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-keys="defaultExpandedKeys"
            :default-checked-keys="defaultCheckedKeys"
            :node-key="nodeKey"
            @check="check"
          >
          </ui-search-tree>
        </Col>
        <Col :span="12" class="camera-list" v-ui-loading="{ loading: loading }">
          <div class="select-top">
            <span class="fl base-text-color">
              人员列表（<span class="font-red">{{ peopleList.length }}</span
              >人）
            </span>
            <div class="fr">
              <span>
                <Input
                  search
                  placeholder="检索-请输入名称"
                  class="width-md"
                  v-model="searchText"
                  @on-search="searchAdd()"
                  @on-enter="searchAdd"
                />
              </span>
              <!-- <span class="ml-sm">
                <Checkbox v-model="unchooseChecked" @on-change="chooseUnSearch">
                  <span class="base-text-color">排除</span>
                </Checkbox>
              </span> -->
              <span class="ml-lg">
                <Checkbox v-model="mainChecked" @on-change="chooseChecked" :disabled="isView">
                  <span class="base-text-color">全选</span>
                </Checkbox>
              </span>
            </div>
          </div>
          <div class="pt-sm select-content" v-scroll="370">
            <div
              v-for="(item, index) in peopleList"
              :key="index"
              class="mb-sm base-text-color fl selected-item ellipsis"
            >
              <Checkbox v-model="item.checked" @on-change="selectPeople(item)" :disabled="isView"></Checkbox>
              {{ item.name }}
            </div>
          </div>
        </Col>
        <Col :span="6" class="selected-camera">
          <div class="selected-title over-flow">
            <span class="base-text-color fl">
              已选（<span class="font-red">{{ selectedPeopleList.length }}</span
              >人）
            </span>
            <span v-if="!isView" class="base-text-color fr pointer" @click="unSelectAll">清空</span>
          </div>
          <div class="selected-list" v-scroll="350">
            <div v-for="(item, index) in selectedPeopleList" :key="index">
              <div class="over-flow selected-item base-text-color" v-if="item.checked">
                <span class="inline selected-orgname ellipsis vt-middle mr-sm" :title="item.orgName">{{
                  item.orgName
                }}</span>
                <span>
                  <span class="inline name ellipsis vt-middle" :title="item.name">{{ item.name }}</span>
                  <span v-if="!!item.phoneNumber" class="inline vt-middle">（{{ item.phoneNumber }}）</span>
                </span>

                <span v-if="!isView" class="close fr font-red inline vt-middle" @click="unSelect(item, index)"
                  >移除</span
                >
              </div>
            </div>
          </div>
        </Col>
      </Row>
      <template slot="footer">
        <Button v-if="!isView" type="primary" @click="query" class="plr-30">确 定</Button>
        <Button type="default" @click="cancel" class="plr-30">取 消</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.camera-module {
  @{_deep} .ivu-modal {
    width: 1400px !important;
  }
  @{_deep} .ivu-modal-body {
    padding: 0;
  }

  .camera-content {
    height: 100%;
    border-top: 1px solid var(--border-modal-footer);
    .tree {
      padding-right: 20px;
      padding-left: 16px;
    }
    .camera-list {
      border-left: 1px solid var(--border-modal-footer);
      border-right: 1px solid var(--border-modal-footer);
      .select-top {
        overflow: hidden;
        padding: 0 20px;
        line-height: 50px;
        border-bottom: 1px solid var(--border-modal-footer);
      }
    }
  }
  .select-content {
    padding: 20px;
    .selected-item {
      width: 210px;
    }
  }

  .selected-camera {
    .selected-title {
      padding: 0 20px;
      height: 50px;
      line-height: 50px;
      border-bottom: 1px solid var(--border-modal-footer);
    }
    .selected-list {
      padding: 5px 0;
      .close {
        cursor: pointer;
      }

      .selected-item {
        padding: 5px 20px;
        &:hover {
          background-color: var(--border-modal-footer);
        }
        .selected-orgname {
          width: 100px;
        }
        .name {
          max-width: 80px;
        }
      }
    }
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
import user from '@/config/api/user';

export default {
  props: {
    value: {},
    /**
     * 默认数据中应该为 [{orgList: [xxx], id: xxx}]
     * orgList 为org的id集合，id 为人员id
     * 由于查询用户接口需要传入的为org的id所以orgList为org的id集合
     */
    defaultPeopleList: {
      type: Array,
    },
    title: {
      default: '报警接收人',
    },
    // 是否查看
    isView: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      nodeKey: 'id',
      defaultCheckedKeys: [],
      peopleList: [],
      selectedPeopleList: [], //右边选中摄像头列表
      viewCameraList: [], //当摄像头过多的时候不要显示所有的摄像头只显示该数组的摄像头
      initPeopleList: [], //搜索时摄像头临时存储列表 当搜空的时候 返回所有的摄像头
      // areaList: [],       // 一维的 arealist
      style: {
        top: 0,
      },
      mainChecked: false,
      searchText: '',
      unchooseChecked: false,
      cameraSliceNum: 200,
    };
  },
  created() {},
  mounted() {},
  methods: {
    muchPeople() {
      //这里处理如果列表过多，则显示前200个，否则dom过多显示会卡死
      if (this.peopleList.length > 200) {
        this.listenScroll();
      }
      this.cameraSliceNum = 200;
      this.viewCameraList = this.peopleList.slice(0, this.cameraSliceNum);
    },
    //监听滑动距离加载隐藏的列表
    listenScroll() {
      let box = this.$refs.cameraBox;
      box.addEventListener(
        'scroll',
        () => {
          // console.log(box.scrollTop + box.clientHeight === box.scrollHeight);
          //  判断是否滚动到底部，如果滚动到底部则则再加载200个书
          if (box.scrollTop + box.clientHeight === box.scrollHeight) {
            this.cameraSliceNum += 200;
            //到底部要做的操作
            this.viewCameraList = this.peopleList.slice(0, this.cameraSliceNum);
          }
        },
        false,
      );
    },
    //------------------全选所有数据函数---------------------------------
    chooseChecked(val) {
      this.peopleList.forEach((row) => {
        row.checked = val;
        this.selectPeople(row);
      });
    },
    //-----------------根据名字检索数据函数---------------------------
    searchAdd() {
      let searchArr = [];
      if (this.searchText !== '') {
        for (let i = 0; i < this.initPeopleList.length; i++) {
          let str = this.initPeopleList[i].name;
          if (str.indexOf(this.searchText) !== -1) {
            searchArr.push(this.initPeopleList[i]);
          }
        }
        this.peopleList = searchArr;
      } else {
        this.peopleList = this.initPeopleList;
      }
      this.muchPeople();
    },
    //--------------------检索之后，排除功能函数-----------------------------
    chooseUnSearch() {
      let searchArr = [];
      if (this.searchText !== '') {
        if (this.unchooseChecked) {
          for (let i = 0; i < this.initPeopleList.length; i++) {
            let str = this.initPeopleList[i].deviceName;
            if (str.indexOf(this.searchText) === -1) {
              searchArr.push(this.initPeopleList[i]);
            }
          }
          this.peopleList = searchArr;
        } else {
          this.searchAdd();
        }
      } else {
        this.peopleList = this.initPeopleList;
      }
    },
    async getOrgPeople(checkedData) {
      try {
        this.loading = true;
        let res = await this.$http.post(user.queryUserList, {
          orgIdList: checkedData,
        });
        /**
         * 由于此接口返回的orgList中是org的id
         * 但业务系统中的使用的是orgCode
         * 这里需要通过id查询到对应的orgCode
         * */
        this.peopleList = res.data.data.map((row) => {
          let orgCodeList = [];
          row.orgList.forEach((rw) => {
            const org = this.initialOrgList.find((org) => org.id === rw);
            if (org) {
              orgCodeList.push(org.orgCode);
            }
          });
          this.$set(row, 'orgCode', orgCodeList.join(','));
          return row;
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async check(checkedData) {
      if (checkedData.length === 0) {
        this.peopleList = [];
      } else {
        await this.getOrgPeople(checkedData);
      }
      //提取一个对象减少for循环
      let tempObj2 = {};
      this.selectedPeopleList.forEach((row) => {
        tempObj2[row.id] = row.id;
      });
      //初始化中间摄像机列表时，要跟右边已选中摄像机列表做对比，选中摄像机
      this.peopleList = this.peopleList.map((row) => {
        if (tempObj2[row.id]) {
          this.$set(row, 'checked', true);
        } else {
          this.$set(row, 'checked', false);
        }
        return row;
      });
      //当摄像头过多时进行处理不显示所有摄像头只先显示200个
      this.muchPeople();
      this.initPeopleList = this.peopleList;
      this.isCheckAll();
    },
    //中间摄像头列表选中
    selectPeople(item) {
      //这里因为不想要中间的摄像头列表影响到右边选中的摄像头列表所以需要深度克隆
      let temp = this.$util.common.deepCopy(item);
      let index = this.selectedPeopleList.findIndex((row) => {
        return temp.id === row.id;
      });
      if (temp.checked) {
        let orgName = [];
        temp.orgList.forEach((row) => {
          const org = this.initialOrgList.find((rw) => rw.id === row);
          if (org) {
            orgName.push(org.orgName);
          }
        });
        this.$set(temp, 'orgName', orgName.join(','));
        if (index === -1) {
          this.selectedPeopleList.push(temp);
        }
      } else {
        this.selectedPeopleList.splice(index, 1);
      }
      this.isCheckAll();
    },
    // 检查是否全选所有中间摄像机列表
    isCheckAll() {
      if (this.peopleList.length === 0) {
        this.mainChecked = false;
        return false;
      }
      this.mainChecked = true;
      this.peopleList.forEach((row) => {
        if (!row.checked) this.mainChecked = false;
      });
    },
    unSelect(item, index) {
      item.checked = false;
      let temp = this.$util.common.deepCopy(item);
      let obj = this.peopleList.find((row) => {
        return temp.id === row.id;
      });
      if (obj) {
        obj.checked = item.checked;
      }
      this.selectedPeopleList.splice(index, 1);
    },
    unSelectAll() {
      this.peopleList.forEach((row) => {
        row.checked = false;
      });
      this.selectedPeopleList = [];
      this.mainChecked = false;
    },
    query() {
      this.visible = false;
      this.$emit('pushPeople', this.selectedPeopleList);
    },
    cancel() {
      this.visible = false;
    },
    //初始化选中
    async setDefault() {
      let checkTreeKey = [];
      // 初始化选中默认组织机构并且查询出组织机构下的人员
      this.defaultPeopleList.forEach((row) => {
        if (row.orgList) {
          checkTreeKey = [...checkTreeKey, ...row.orgList];
        } else {
          checkTreeKey.push(row.orgCode);
        }
      });
      checkTreeKey = Array.from(new Set(checkTreeKey));
      this.defaultCheckedKeys = checkTreeKey;
      // this.$refs.uiSearchTree.setCheckedKeys(checkTreeKey);
      this.$nextTick(async () => {
        const checkedKeys = this.$refs.uiSearchTree.getCheckedKeys();
        await this.check(checkedKeys);
        // 选中人员
        this.defaultPeopleList.forEach((row) => {
          const people = this.peopleList.find((rw) => rw.id === row.id);
          if (people) {
            this.$set(people, 'checked', true);
            this.selectPeople(people);
          }
        });
      });
    },
  },
  watch: {
    value(val) {
      // 关闭该弹框时清空已选中的树列表
      if (!val) {
        this.$refs.uiSearchTree.setCheckedKeys([]);
        this.peopleList = [];
        this.selectedPeopleList = [];
      } else {
        this.setDefault();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      initialOrgList: 'common/getInitialOrgList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
