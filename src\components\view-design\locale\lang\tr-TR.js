import setLang from '../lang';

const lang = {
    i: {
        locale: 'tr-TR',
        select: {
            placeholder: '<PERSON><PERSON>',
            noMatch: '<PERSON><PERSON>le<PERSON><PERSON> veri yok',
            loading: 'yükleme'
        },
        table: {
            noDataText: 'Veri Yok',
            noFilteredDataText: '<PERSON><PERSON><PERSON><PERSON>len veri yok',
            confirmFilter: 'Onay<PERSON>',
            resetFilter: '<PERSON>ı<PERSON><PERSON>rl<PERSON>',
            clearFilter: 'Hepsi',
            sumText: 'Sum'
        },
        datepicker: {
            selectDate: 'Ta<PERSON>h seç',
            selectTime: '<PERSON><PERSON> seç',
            startTime: 'Başlangıç',
            endTime: 'Bitişe',
            clear: 'Temizle',
            ok: 'Tamam',
            datePanelLabel: '[mmmm] [yyyy]',
            month: '',
            month1: 'Ocak',
            month2: 'Şubat',
            month3: 'Mart',
            month4: 'Nisan',
            month5: 'Mayıs',
            month6: 'Ha<PERSON>ran',
            month7: 'Temmuz',
            month8: 'Ağust<PERSON>',
            month9: 'Eyl<PERSON>l',
            month10: 'Ekim',
            month11: 'Kasım',
            month12: 'Aralık',
            year: '',
            weekStartDay: '0',
            weeks: {
                sun: 'Paz',
                mon: 'Pzt',
                tue: 'Sal',
                wed: 'Çar',
                thu: 'Per',
                fri: 'Cum',
                sat: 'Cmt'
            },
            months: {
                m1: 'Oca',
                m2: 'Şub',
                m3: 'Mar',
                m4: 'Nis',
                m5: 'May',
                m6: 'Haz',
                m7: 'Tem',
                m8: 'Ağu',
                m9: 'Eyl',
                m10: 'Ekm',
                m11: 'Kas',
                m12: 'Ara'
            }
        },
        transfer: {
            titles: {
                source: 'Kaynak',
                target: 'Hedef'
            },
            filterPlaceholder: 'Arama yapın',
            notFoundText: 'Bulunamadı'
        },
        modal: {
            okText: 'Tamam',
            cancelText: 'İptal'
        },
        poptip: {
            okText: 'Tamam',
            cancelText: 'İptal'
        },
        page: {
            prev: 'Önceki',
            next: 'Sonraki',
            total: 'Toplam',
            item: 'öğe',
            items: 'öğeler',
            prev5: 'Önceki 5 Sayfa',
            next5: 'Sonraki 5 Sayfa',
            page: '/sayfa',
            goto: 'Git',
            p: ''
        },
        rate: {
            star: 'Yıldız',
            stars: 'Yıldız'
        },
        time: {
            before: ' önce',
            after: ' sonra',
            just: 'hemen şimdi',
            seconds: ' saniye',
            minutes: ' dakika',
            hours: ' saat',
            days: ' gün'
        },
        tree: {
            emptyText: 'Veri Yok'
        }
    }
};

setLang(lang);

export default lang;
