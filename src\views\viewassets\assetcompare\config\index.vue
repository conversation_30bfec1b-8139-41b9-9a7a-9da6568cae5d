<template>
  <ui-modal class="configuration" v-model="visible" title="配置管理" :styles="styles">
    <underline-menu
      v-model="activeMenu"
      :data="MENU_LIST"
      @on-change='onChangeMenu'
      class="mb-sm"
    ></underline-menu>
    <tag-view class="mb-sm" :list="tagList" @tagChange="onChangeCompareTarget" ref="tagView" :default-active='defaultActiveTag'></tag-view>
    <div class="config-wrap">
      <components :is="component" @save="save" ref="configRef" :property-list='propertyList' :config='config'></components>
    </div>
    <template #footer>
      <Button @click="handleReset" class="plr-30"> 取 消 </Button>
      <Button type="primary" @click="handleSubmit" class="plr-30" :loading="saveLoading"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
import assetcomparison from '@/config/api/assetcomparison.js'
import {
  THIS_LEVEL_BY_GB28181,
  THIS_LEVEL_BY_GAT1400_VEHICLE,
  THIS_LEVEL_BY_GAT1400_FACE,
  SUPSUB_BY_GB28181,
  CROSS_NET_BY_DEVICE_INFO,
  CROSS_NET_BY_GB28181,
  CROSS_NET_BY_GAT1400_FACE, CROSS_NET_BY_GAT1400_VEHICLE, TAB_LIST, MENU_LIST,
} from '@/views/viewassets/assetcompare/modules/enum.js';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  props: {
    value: {
      type: Boolean,
    },
    /**
     * 默认菜单 如本级对账 配置默认选择本级对账
     */
    defaultMenu: {
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.5rem',
      },
      activeMenu: '',
      component: '',
      defaultActiveTag: 0,
      MENU_LIST,
      // configInfo: {},
      propertyList: [],
      saveLoading: false,
      tagList: [],
      config: {},
    };
  },
  computed: {
    ...mapGetters({
      configInfo: 'assetcompare/getConfigInfo',
    }),
  },
  created() {
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 批量获取字典值
      getConfig: 'assetcompare/getConfig', //
    }),
    validateRegionCode(form, configName) {
      if (!form.regionCode) {
        this.$Message.warning(`${configName}检测对象不能为空`);
        return false;
      }
      return true;
    },
    validateCron(form, configName) {
      if (!form.cronType) {
        return false
      }
      if (form.cronType === "1" && !form.cronData.length  ){
        this.$Message.warning(`${configName}对账计划不能为空`)
        return false;
      }
      if (form.cronType !== "1" && (!form.timePoints.length || !form.cronData.length)){
        this.$Message.warning(`${configName}对账计划不能为空`)
        return false;
      }
      return true;
    },
    validateCompareField(form, configName) {
      if (!form.compareField || !form.compareField.length) {
        this.$Message.warning(`${configName}比对字段不能为空`);
        return false
      }
      const index = form.compareField.findIndex(item => item.fieldName === 'deviceId');
      if (index === -1) {
        this.$Message.warning(`${configName}比对字段deviceId必选`);
        return false
      }
      return true
    },
    validator() {
      return new Promise((resolve, reject) => {
        const allValid = Object.keys(this.configInfo).every(key => {
          const item = this.configInfo[key];
          const { desc } = TAB_LIST.find(item => item.value === key)
          if (!this.validateRegionCode(item, desc) || !this.validateCompareField(item, desc) || !this.validateCron(item, desc)) {
            reject('验证失败');
            return false;
          }
          return true
        })
        if (allValid) {
          resolve("验证成功");
        }
      })
    },
    onChangeCompareTarget(index) {
      this.$refs.configRef.save();
      this.getPropertyList();
      this.setCompareTarget(index);
    },
    async getPropertyList() {
      try {
        const { propertyType } = this.tagList[this.defaultActiveTag];
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType,
        });
        this.propertyList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async updateConfig() {
      try {
        let params = {
         ...this.configInfo
        }
        let { data } = await this.$http.post(assetcomparison.updateConfig, params);
        this.$Message.success(data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    onChangeMenu(val) {
      this.$refs.configRef.save();
      this.setMenu(val);
      this.getPropertyList();
      this.setCompareTarget(0);
    },
    setMenu(val) {
      const index = this.MENU_LIST.findIndex(item => item.code === val);
      this.tagList = this.MENU_LIST[index]['tagList'];
    },
    setCompareTarget(index) {
      const { component } = this.tagList[index];
      this.component = component;
      this.defaultActiveTag = index;
      const { id } = this.tagList[index];
      this.config = this.configInfo[id] || {};
    },
    /**
     * 保存当前菜单的数据到this.configInfo上
     */
    save(config) {
      const { id } = this.tagList[this.defaultActiveTag];
      this.configInfo[id] = config;
    },
    async handleSubmit() {
      try {
        this.saveLoading = true;
        this.$refs.configRef.save();
        await this.validator();
        await this.updateConfig();
        this.getConfig();
        this.visible = false;
      } catch (err) {
        console.log(err, 'err');
      } finally {
        this.saveLoading = false;
      }
    },
    handleReset() {
      this.visible = false;
    },
  },
  watch: {
    async value(val) {
      this.visible = val;
      if (val) {
        await this.getConfig();
        const { code } = this.MENU_LIST[this.defaultMenu || 0];
        this.activeMenu = code;
        await this.$nextTick();
        this.setMenu(this.activeMenu);
        this.setCompareTarget(0);
        await this.getPropertyList();
      } else {
        this.activeMenu = '';
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    PlatformCompare: require('./platform-compare.vue').default,
    FaceCompare: require('./face-compare.vue').default,
    VehicleCompare: require('./vehicle-compare.vue').default,
    SupSubCompare: require('@/views/viewassets/assetcompare/config/sup-sub-compare.vue').default,
    CurrentDomainAsset: require('@/views/viewassets/assetcompare/config/current-domain-asset.vue').default,
    CurrentDomainCrossNet: require('@/views/viewassets/assetcompare/config/current-domain-cross-net.vue').default,
    CurrentDomainFaceLib: require('@/views/viewassets/assetcompare/config/current-domain-face-lib.vue').default,
    CurrentDomainVehicleLib: require('@/views/viewassets/assetcompare/config/current-domain-vehicle-lib.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
};
</script>
<style lang="less" scoped>
.configuration {
  @{_deep} .ivu-modal-body {
    display: flex;
    flex-direction: column;
  }
  .config-wrap {
    height: 600px;
    overflow-y: auto;
  }
}
</style>
