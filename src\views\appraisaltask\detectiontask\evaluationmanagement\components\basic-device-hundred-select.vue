<template>
  <ui-modal v-model="visible" title="设置百人达标占比率" :styles="styles" class="ui-modal" @query="query">
    <div class="area-filter mb-md ml-sm">
      <ui-label required class="inline" label="行政区划" :width="70">
        <Select class="width-sm" v-model="form.regionType" placeholder="请选择行政区划">
          <Option :value="item.value" v-for="(item, index) in regionTypeOptions" :key="`${item.value}-${index}`">
            {{ item.label }}
          </Option>
        </Select>
      </ui-label>
      <ui-label required class="inline ml-lg" label="数据类型" :width="70">
        <Select class="width-md" v-model="form.numberType" placeholder="请选择数据类型">
          <Option v-for="(opItem, opIndex) in assessmentItems" :key="'opIndex' + opIndex" :value="opItem.key"
            >{{ opItem.optionLabel }}
          </Option>
        </Select>
      </ui-label>
      <ui-label required class="inline ml-lg" :label="placeholder" :width="70">
        <InputNumber v-model="form.number" class="width-md" :placeholder="`请输入${placeholder}`"> </InputNumber>
      </ui-label>
      <Button class="ml-md" type="primary" @click="clickBatchInput">批量填入</Button>
    </div>
    <div class="area-container">
      <div class="tree-wrapper">
        <common-capture-area-select :data="areaTreeData">
          <template #configName>
            <span class="width-md">人口数量</span>
            <span class="width-md">百人达标占比率</span>
          </template>
          <template #label="{ data }">
            <div class="width-md" v-show="data.check">
              <Input v-model="data.v1" class="width-sm vt-middle mr-100" :placeholder="`请输入人口数量`"> </Input>
            </div>
            <div class="width-md" v-show="data.check">
              <Input v-model="data.p1" max="100" class="width-sm vt-middle mr-100" :placeholder="`请输入占比率`">
              </Input>
              <span class="ml-sm">%</span>
            </div>
          </template>
        </common-capture-area-select>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>
<script>
export default {
  name: 'basic-device-hundred-select',
  props: {
    loading: {},
    //行政区划treeData
    data: {},
    styles: {
      type: Object,
      default: () => {
        return {
          width: '7.6rem',
        };
      },
    },
  },
  components: {
    CommonCaptureAreaSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-capture-area-select.vue')
        .default,
  },
  mixins: [],
  data() {
    return {
      visible: false,
      form: {
        regionType: 'province',
        numberType: 'v1', // v1 p1
        number: null,
      },
      regionTypeOptions: [
        { value: 'province', label: '省' },
        { value: 'city', label: '市' },
        { value: 'region', label: '区县' },
        { value: 'policeStation', label: '派出所' },
      ],
      assessmentItems: [
        { title: '人口数量', key: 'v1', optionLabel: '人口数量' },
        { title: '占比率', key: 'p1', optionLabel: '占比率' },
      ],
      areaTreeData: [],
    };
  },
  computed: {
    placeholder() {
      let { optionLabel } = this.assessmentItems.find((item) => item.key === this.form.numberType);
      return optionLabel;
    },
  },
  created() {
    this.visible = true;
  },
  mounted() {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    /**
     * sbgnlx     --  1-视频监控，2-车辆识别，3-人员识别
     * key        --组织机构code
     * name        --组织机构名称
     * v1        --人口数量
     * p1        --百分制 p1 （百人达标占比比率）
     */
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.areaTreeData = val;
      },
    },
  },
  methods: {
    clickBatchInput() {
      if (!this.form.number) {
        return this.$Message.error('请输入达标数量');
      }
      this.batchInput(this.areaTreeData);
    },
    /*
    1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所
    * */
    batchInput(data) {
      data.forEach((item) => {
        if (
          (item.regionType === '1' && this.form.regionType === 'province') ||
          (['2', '3', '4', '5'].includes(item.regionType) && this.form.regionType === 'city') ||
          (['6', '7', '8'].includes(item.regionType) && this.form.regionType === 'region') ||
          (item.regionType === '9' && this.form.regionType === 'policeStation')
        ) {
          this.$set(item, this.form.numberType, this.form.number);
        }
        if (item.children) {
          this.batchInput(item.children);
        }
      });
    },
    getCheckedNodes(data) {
      data.forEach((item) => {
        if (item.check) {
          this.checkedTreeData.push({
            key: item.regionCode,
            name: item.regionName,
            v1: item.v1 || '',
            p1: item.p1 || '',
          });
        }
        if (item.children) {
          this.getCheckedNodes(item.children);
        }
      });
    },
    validateCheckedNodes(data) {
      if (!data.length) {
        this.$Message.error('请选择行政区划并设置百人达标占比率');
        return false;
      }
      for (let i = 0; i < data.length; i++) {
        let item = data[i];
        let v1 = item.v1;
        let p1 = item.p1;
        let reg = /^\d+(\.\d{1,})$|(^[1-9]\d*$)/;
        if (!v1 || !p1) {
          this.$Message.error(`${item.name}人口数量或占比率不能为空`);
          return false;
        }
        if (!reg.test(v1)) {
          this.$Message.error(`${item.name}人口数量格式不正确`);
          return false;
        }
        if (!reg.test(p1) || Number(p1) > 100) {
          this.$Message.error(`${item.name}占比率格式不正确`);
          return false;
        }
      }
      return true;
    },
    query() {
      this.checkedTreeData = [];
      this.getCheckedNodes(this.areaTreeData);
      let result = this.validateCheckedNodes(this.checkedTreeData);
      if (result) {
        this.$emit('query', this.checkedTreeData);
        this.visible = false;
      }
    },
  },
};
</script>

<style scoped lang="less">
.area-container {
  .tree-title {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 48px;
    line-height: 48px;
    padding: 0 15px;
    background: var(--bg-table-header-th);
  }
}
</style>
