<template>
  <div class="header mb-xs">
    <i class="div-block"></i>
    <div>
      <span>{{ title }}</span>
    </div>
  </div>
</template>
<style lang="less" scoped>
.header {
  width: 100%;
  height: 16px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  .div-block {
    width: 4px;
    height: 16px;
    background: #0cd4e6;
  }
  div {
    height: 16px;
    width: 100%;
    span {
      display: inline-block;
      height: 16px;
      line-height: 16px;
      font-size: 16px;
      color: #85daf2;
      padding: 0 5px;
      vertical-align: top;
    }
  }
}
</style>
<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {},
  computed: {},
  props: {
    title: {},
  },
  components: {},
};
</script>
