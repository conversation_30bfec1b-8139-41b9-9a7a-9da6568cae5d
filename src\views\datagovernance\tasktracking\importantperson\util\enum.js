export default {
  aggregateCodeEnums: {
    '数据输入': '1001',
    '字段映射': '2001',
    '字典映射': '2002',
    '正式入库': '3001',
    '空值检测': '4001',
    '重复检测': '4002',
    '数据输出': '1002',
    '图像上传及时性检测': '8009',
    '人脸结构化': '6002',
    '人员轨迹准确性检测优化': '10001',
    '人员数据输出': '1003',
  },
  aggregateOptions: [
    {
      addVisible: false,
      top: '1.4rem',
      left: '2%',
      datas: [
        {
          name: 'dataaccessPopup',
          icon: 'icon-zu16191',
          title: '数据输入',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '接入总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '今日增量', num: 0, color: '#F18A37', fileName: 'todayAccessDataCount' },
          ],
        },
      ],
      connectingOptions: {
        width: '5.5%',
        height: '0.04rem',
        top: '1.65rem',
        left: '16.6%',
      },
    },
    {
      addVisible: false,
      top: '0.8rem',
      left: '22.5%',
      datas: [
        {
          name: 'ringsPopupRef',
          icon: 'icon-ziduanyingshe',
          title: '字段映射',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '映射总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '映射成功', num: 0, color: '#F18A37', fileName: 'successData' },
            { title: '转化率', num: '0%', color: '#13B13D', fileName: 'successDataRate' },
          ],
        },
        {
          name: 'ringsPopupRef',
          icon: 'icon-zidianyingshe',
          title: '字典映射',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '映射总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '映射成功', num: 0, color: '#F18A37', fileName: 'successData' },
            { title: '转化率', num: '0%', color: '#13B13D', fileName: 'successDataRate' },
          ],
        },
      ],
      connectingOptions: {
        width: '5.5%',
        top: '1.65rem',
        height: '0.04rem',
        left: '37.1%',
      },
    },
    {
      addVisible: false,
      top: '1.3rem',
      left: '43%',
      datas: [
        {
          name: 'ringsPopupRef',
          icon: 'icon-linshiruku',
          title: '正式入库',
          subTitle: '入库失败',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '入库总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '入库数据', num: 0, color: '#F18A37', fileName: 'successData' },
            { title: '入库率', num: '0%', color: '#13B13D', fileName: 'successDataRate' },
          ],
        },
      ],
      connectingOptions: {
        width: '5.9%',
        height: '0.04rem',
        top: '1.65rem',
        left: '57.8%',
      },
    },
    {
      addVisible: false,
      top: '0.8rem',
      left: '64%',
      datas: [
        {
          name: 'columnRingRef',
          icon: 'icon-kongzhijiance',
          title: '空值检测',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
        },
        {
          name: 'columnRingRef',
          icon: 'icon-zhongfujiance',
          title: '重复检测',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
        },
      ],
      connectingOptions: {
        width: '4.7%',
        height: '0.04rem',
        top: '1.65rem',
        left: '78.8%',
      },
    },
    {
      addVisible: false,
      top: '1.15rem',
      left: '84%',
      datas: [
        {
          name: 'columnRingRef',
          icon: 'icon-zu1665',
          title: '数据输出',
          subTitle: '人员基础信息',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
            { title: '治理优化', num: 0, color: '#F18A37', fileName: '' },
            { title: '优化占比', num: '0%', color: '#13B13D', fileName: '' },
          ],
        },
      ],
      // connectingOptions: {
      //     width: "8.5%",
      //     height: "0.04rem",
      //     top: "1.8rem",
      //     left: "5%",
      //     angle: 90,
      // },
    },
  ],
  faceAggregateOptions: [
    {
      addVisible: false,
      top: '1.15rem',
      left: '8%',
      datas: [
        {
          name: 'warehousePopup',
          icon: 'icon-zu16191',
          title: '数据输入',
          left: '10px',
          iconPass: true,
          iconSetting: false,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '接入总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '今日增量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
          ],
        },
      ],
      connectingOptions: {
        width: '9%',
        height: '0.04rem',
        top: '1.4rem',
        left: '22.8%',
      },
    },
    {
      addVisible: false,
      top: '1rem',
      left: '32%',
      datas: [
        {
          name: 'warehousePopup',
          icon: 'icon-tuxiangshangchuanjishixingjiance',
          title: '图像上传及时性检测',
          left: '10px',
          iconPass: true,
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '上传超时', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
        },
      ],
      connectingOptions: {
        width: '8.6%',
        height: '0.04rem',
        top: '1.4rem',
        left: '46.8%',
      },
    },
    // {
    //     addVisible: false,
    //     top: "2.25rem",
    //     left: "22.5%",
    //     datas: [
    //         {
    //             name: "standardconversePopup",
    //             icon: "icon-renlianjiegouhua",
    //             title: "人脸结构化",
    //             left: "10px",
    //             iconSetting: false,
    //             iconView: 'icon-chakanjiancejieguo',
    //             list: [
    //                 {title: '算法数量', num: 0, color: '#05FEF5',fileName: 'algorithmCount'},
    //                 {title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount'},
    //                 {title: '结构化数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount'},
    //                 {title: '转化率', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate'},
    //             ]
    //         },
    //     ],
    //     connectingOptions: {
    //         width: "5%",
    //         height: "0.04rem",
    //         top: "2.6rem",
    //         left: "37.5%",
    //     },
    // },
    {
      addVisible: false,
      top: '0.9rem',
      left: '56%',
      datas: [
        {
          name: 'warehousePopup',
          icon: 'icon-renyuanguijizhunquexingjianceyouhua1',
          title: '人员轨迹准确性检测优化',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '检测总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
            { title: '治理优化', num: 0, color: '#F18A37', fileName: '' },
            { title: '优化占比', num: '0%', color: '#13B13D', fileName: '' },
          ],
        },
      ],
      connectingOptions: {
        width: '8.8%',
        height: '0.04rem',
        top: '1.4rem',
        left: '70.6%',
      },
    },
    {
      addVisible: false,
      top: '1rem',
      left: '80%',
      datas: [
        {
          name: 'exportDataPopup',
          icon: 'icon-zu1665',
          title: '数据输出',
          subTitle: '人员轨迹数据',
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '异常数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '异常占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
        },
      ],
      // connectingOptions: {
      //     width: "0.07rem",
      //     height: "0.04rem",
      //     top: "2.28rem",
      //     left: "6.19rem",
      // },
    },
  ],
  emptyTableColumns: [
    { type: 'index', width: 70, title: '序号' },
    { title: '不合格原因', key: 'errorMessage' },
    { title: '不合格字段', key: 'propertyName' },
    { title: '检测规则', key: 'componentName' },
    { title: '实际结果', key: 'result' },
  ],
  baseDisqulifyTableColumn: [
    { type: 'index', width: 70, title: '序号' },
    { title: '不合格原因', key: 'errorMessage' },
    { title: '不合格字段', key: 'propertyName' },
    { title: '检测规则', key: 'componentName' },
    { title: '实际结果', key: 'result' },
  ],
  ringColorEnum: {
    greenColor: 'greenColor',
    redColor: 'redColor',
    yellowColor: 'yellowColor',
    blueColor: 'blueColor',
  },
  timeEnum: {
    '1': '小时',
    '2': '分',
    '3': '秒',
  },
};
