<template>
  <div class="ptz-control">
    <Modal ref="ptzModal" v-model="isShow" draggable sticky :z-index="10000" :mask="false" title="云台控制" footer-hide width="280" :styles="{ top: 0, width: 'auto' }">
      <div class="direction">
        <div class="direction-l">
          <div class="circle1"></div>
          <div class="circle2"></div>
          <div class="circle3"></div>
          <ul class="pie">
            <li class="slice-one slice" @mousedown="ptzMousedown('down')" @mouseup="ptzMouseup('down')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-two slice" @mousedown="ptzMousedown('downleft')" @mouseup="ptzMouseup('downleft')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-three slice" @mousedown="ptzMousedown('left')" @mouseup="ptzMouseup('left')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-four slice" @mousedown="ptzMousedown('upleft')" @mouseup="ptzMouseup('upleft')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-five slice" @mousedown="ptzMousedown('up')" @mouseup="ptzMouseup('up')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-six slice" @mousedown="ptzMousedown('upright')" @mouseup="ptzMouseup('upright')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-seven slice" @mousedown="ptzMousedown('right')" @mouseup="ptzMouseup('right')"><i class="iconfont icon-yuntaijiantou"></i></li>
            <li class="slice-eight slice" @mousedown="ptzMousedown('downright')" @mouseup="ptzMouseup('downright')"><i class="iconfont icon-yuntaijiantou"></i></li>
          </ul>
        </div>
        <div class="direction-r">
          <div class="direction-r-item">
            <span class="mybutton" @mousedown="ptzMousedown('in')" @mouseup="ptzMouseup('in')">+</span>
            <span>变倍</span>
            <span class="mybutton" @mousedown="ptzMousedown('out')" @mouseup="ptzMouseup('out')">-</span>
          </div>
          <div class="direction-r-item">
            <span class="mybutton" @mousedown="ptzMousedown('far')" @mouseup="ptzMouseup('far')">+</span>
            <span>变焦</span>
            <span class="mybutton" @mousedown="ptzMousedown('near')" @mouseup="ptzMouseup('near')">-</span>
          </div>
          <div class="direction-r-item">
            <span class="mybutton" @mousedown="ptzMousedown('open')" @mouseup="ptzMouseup('open')">+</span>
            <span>光圈</span>
            <span class="mybutton" @mousedown="ptzMousedown('close')" @mouseup="ptzMouseup('close')">-</span>
          </div>
        </div>
      </div>
      <div class="speed">
        <div class="label">控制速度：</div>
        <div class="speed-item">
          <el-slider v-model="speed" :min="1" :max="255"></el-slider>
          <span>{{ speed }}</span>
        </div>
      </div>
      <div class="cruise">
        <div class="label">巡航：</div>
        <div class="cruise-buttons">
          <ButtonGroup>
            <Button type="primary" size="small" @click="ptzMousedown('cruise-start')">开始</Button>
            <Button size="small" @click="ptzMousedown('cruise-stop')">停止</Button>
          </ButtonGroup>
          <Button type="primary" size="small" @click="ptzMousedown('preset-goto')">预置位</Button>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
import { Modal } from 'view-design'

export default {
  props: {
    ...Modal.props,
    videoObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      isShow: false,
      isFirstOpen: true,
      speed: 100
    }
  },
  watch: {
    isShow(val) {
      this.$emit('input', val)
    },
    value: {
      handler(val) {
        this.isShow = val
        if (val && this.isFirstOpen) {
          this.isFirstOpen = false
          this.$nextTick(() => {
            let playerDom = document.querySelector('.video-play') || document.querySelector('.video-div')
            let dragDom = this.$refs.ptzModal.$el.querySelector('.ivu-modal-content-drag')
            let left = playerDom.offsetLeft
            let top = playerDom.offsetTop + playerDom.offsetHeight - dragDom.offsetHeight
            dragDom.style.left = left + 'px'
            dragDom.style.top = top + 'px'
          })
        }
      },
      immediate: true
    }
  },
  beforeDestroy() {
    this.isShow = false
  },
  deactivated() {
    this.isShow = false
  },
  methods: {
    ptzMousedown(cmd) {
      if (liveType == 'pvg67' || liveType == 'pvgplus') {
        let pvgCmd = this.getPvgCmd(cmd)
        let pvgSpeed = this.getPvgSpeed(cmd)
        this.videoObj.ptzControl(this.videoObj.focusIndex, pvgCmd, pvgSpeed)
      } else {
        this.$emit('setPtzControl', cmd, this.speed)
      }
    },
    ptzMouseup(cmd) {
      if (liveType == 'pvg67' || liveType == 'pvgplus') {
        let pvgCmd = this.getPvgCmd(cmd)
        this.videoObj.ptzControl(this.videoObj.focusIndex, pvgCmd, 0)
      } else {
        this.$emit('setPtzControl', 'stop', this.speed)
      }
    },
    getPvgCmd(cmd) {
      let obj = {
        'right': 0,
        'upright': 1,
        'up': 2,
        'upleft': 3,
        'left': 4,
        'downleft': 5,
        'down': 6,
        'downright': 7,
        'in': 12,
        'out': 12,
        'far': 11,
        'near': 11,
        'open': 10,
        'close': 10,
        'preset-goto': 13
      }
      let item = obj[cmd]
      return item ? item : cmd
    },
    getPvgSpeed(cmd) {
      let speed = parseInt((this.speed / 255) * 15)
      if (cmd == 'out' || cmd == 'near' || cmd == 'close') {
        speed = 0 - speed
      }
      if (cmd == 'preset-goto') speed = 1 // 预置位ID
      return speed
    }
  }
}
</script>
<style lang="less" scoped>
.direction {
  display: flex;
  .direction-l {
    height: 130px;
    width: 130px;
    margin-right: 16px;
    background: linear-gradient(169deg, #dbe8ff 0%, #dcf2ff 99%);
    border-radius: 50%;
    position: relative;
    .circle1,
    .circle2,
    .circle3 {
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .circle1 {
      width: 120px;
      height: 120px;
      background: linear-gradient(180deg, #ffffff 0%, #eeeeee 100%);
    }
    .circle2 {
      width: 112px;
      height: 112px;
      background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    }
    .circle3 {
      width: 48px;
      height: 48px;
      background: linear-gradient(180deg, #eaeaea 3%, #ffffff 100%);
      box-shadow: 0px 1px 10px 0px #f1f1f1, inset 0px 1px 2px 0px rgba(255, 255, 255, 0.79);
    }
    .pie {
      width: 20px;
      height: 20px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      align-items: center;
      .slice {
        position: absolute;
        i {
          transform: rotateZ(180deg);
          display: block;
          cursor: pointer;
          color: #cecfd0;
          font-size: 12px;
          &:hover {
            color: #2c86f8;
          }
        }
      }
      .slice-one {
        transform: rotateZ(0deg) translateY(42px);
      }

      .slice-two {
        transform: rotateZ(45deg) translateY(42px);
      }

      .slice-three {
        transform: rotateZ(90deg) translateY(42px);
      }

      .slice-four {
        transform: rotateZ(135deg) translateY(42px);
      }

      .slice-five {
        transform: rotateZ(180deg) translateY(42px);
      }

      .slice-six {
        transform: rotateZ(225deg) translateY(42px);
      }

      .slice-seven {
        transform: rotateZ(270deg) translateY(42px);
      }

      .slice-eight {
        transform: rotateZ(315deg) translateY(42px);
      }
    }
  }
  .direction-r {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    .direction-r-item {
      display: flex;
      justify-content: space-around;
    }
  }
  .mybutton {
    display: block;
    width: 24px;
    height: 24px;
    border-radius: 4px;
    opacity: 1;
    border: 1px solid #2c86f8;
    cursor: pointer;
    text-align: center;
    color: #2c86f8;
    font-size: 14px;
    font-weight: 700;
    &:hover {
      background-color: #2c86f8;
      color: #fff;
    }
  }
}
.speed {
  .label {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
    line-height: 28px;
  }
  .speed-item {
    display: flex;
    align-items: center;
    .el-slider {
      flex: 1;
      margin-right: 10px;
    }
  }
}
.cruise {
  .label {
    font-weight: 700;
    color: rgba(0, 0, 0, 0.6);
    line-height: 28px;
  }
  .cruise-buttons {
    display: flex;
    justify-content: space-between;
  }
}
/deep/.ivu-modal {
  .ivu-modal-close {
    top: 0;
  }
  .ivu-modal-header {
    padding: 8px 15px;
    .ivu-modal-header-inner {
      font-weight: 700;
      color: rgba(0, 0, 0, 0.8);
      &::before {
        content: '';
        height: 100%;
        width: 3px;
        display: inline-block;
        background-color: #2c86f8;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
  }
}
</style>
