<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-08-26 15:36:13
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-16 17:46:32
 * @Description: 
-->
<template>
  <div class="container">
    <!-- 搜索 -->
    <searchForm
      class="search"
      ref="searchFormRef"
      @query="query"
      @reset="reset"
      type="task"
      :radioList="radioList"
    >
      <query ref="slotQuery" />
    </searchForm>

    <!-- 操作栏 -->
    <div class="operate">
      <div class="control-title">布控列表</div>
      <Button class="margin" type="primary" @click="add()">新增布控</Button>
    </div>

    <!-- 列表 -->
    <div class="card-content">
      <div class="list">
        <taskCard
          class="alarnRow"
          v-for="item in tableList"
          :taskInfo="item"
          :key="item.taskId"
          :compareType="compareType"
          @remove="handleRemove"
          @dele="handleDele(item)"
          @status="handleStatus"
          @edit="handleEdit(item)"
          @click.native="detail(item)"
        />
        <!-- 空占位，保证数据居左 -->
        <div
          class="alarnRow"
          v-for="(item, index) of 5 - (7 % 5)"
          :key="index"
        ></div>
        <ui-empty v-if="tableList.length === 0"></ui-empty>
      </div>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
  </div>
</template>

<script>
import searchForm from "../../components/search-form.vue";
import taskCard from "../components/task-card.vue";
import query from "../components/query.vue";
import {
  taskPageList,
  taskRemove,
  batchEdit,
  taskApply,
} from "@/api/target-control";
export default {
  name: "vehicleControlTask",
  components: {
    searchForm,
    query,
    taskCard,
  },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    // 布控状态列表
    this.radioList = Object.freeze([
      { key: 99, value: "全部" },
      { key: 0, value: "未执行" },
      { key: 1, value: "执行中" },
    ]);
    return {
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableList: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * @description: 获取查询参数，并进行调整
     */
    getQueryParams() {
      let data = {
        params: this.params,
        ...this.$refs.searchFormRef.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
        compareType: this.compareType,
      };
      // 布控审核状态：0：未提交 1未审核 2：审核通过 3：驳回 4：已删除 5:布控到期
      if (data.operationType == 0) {
        data.taskStatusList = [0, 1, 3, 4, 5];
      } else if (data.operationType == 1) {
        data.taskStatus = 2;
      } else {
        data.operationType = null;
      }
      return data;
    },

    /**
     * @description: 初始化数据
     */
    init() {
      taskPageList(this.getQueryParams()).then((res) => {
        this.tableList = res.data.entities;
        this.total = res.data.total;
        this.tableList.forEach((item) => {
          if (item.taskType == 2) {
            let libs = "";
            item.libVoList.forEach((ite, index) => {
              if (index > 0) libs += ", ";
              libs += ite.libName;
            });
            item.libs = libs;
          }
        });
      });
    },

    /**
     * @description: 查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.init();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },

    /**
     * @description: 改变页码
     * @param {number} pageNumber 页码
     */
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.init();
    },

    /**
     * @description: 改变每页数量
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.init();
    },

    /**
     * @description: 新增布控任务
     * @return {*}
     */
    add() {
      this.$router.push({
        name: "control-task-addVehicle",
        query: {
          compareType: this.compareType,
          noMenu: this.$route.query.noMenu,
        },
      });
    },

    /**
     * @description: 编辑布控任务
     * @param {object} item 布控任务数据
     */
    handleEdit(item) {
      this.$router.push({
        name: "control-task-editVehicle",
        query: {
          taskId: item.taskId,
          compareType: this.compareType,
          noMenu: this.$route.query.noMenu,
        },
      });
    },

    /**
     * @description: 撤控布控任务
     * @param {object} row 布控任务数据
     */
    handleRemove(row) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定撤控该布控信息？`,
        onOk: () => {
          let params = {
            taskId: row.taskId,
            checkUser: this.userInfo.username,
            taskStatus: 0,
          };
          taskApply(params).then((res) => {
            this.$Message.success(`撤控代办成功`);
            this.init();
          });
        },
      });
    },

    /**
     * @description: 删除布控任务，执行中的不可删除
     * @param {object} item 布控任务数据
     */
    handleDele(item) {
      if (item.taskStatus == 1) {
        this.$Message.warning(`执行中任务不可删除,请停用后,再删除数据！`);
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该布控信息？`,
        onOk: () => {
          taskRemove(item.taskId).then((res) => {
            this.init();
          });
        },
      });
    },

    /**
     * @description: 启用 | 停用布控任务，该功能暂时不用
     * @param {number} num 布控任务状态
     * @param {object} row 布控任务数据
     */
    handleStatus(num, row) {
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定${num == 1 ? "启用" : "停用"}要吗？`,
        onOk: () => {
          let params = {
            taskStatus: num,
            taskIds: [row.taskId],
          };
          batchEdit(params).then((res) => {
            this.init();
          });
        },
      });
    },

    /**
     * @description: 布控任务详情
     * @param {object} item 布控任务数据
     */
    detail(item) {
      this.$router.push({
        name: "control-task-detailVehicle",
        query: {
          id: item.taskId,
          alarmCount: item.alarmCount,
          compareType: this.compareType,
          noMenu: this.$route.query.noMenu,
        },
      });
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  margin-bottom: 0 !important;
}
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    .control-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 34px;
    }
  }
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    margin: 0 -0.02604rem;
    align-content: flex-start;
    position: relative;
  }
  .list {
    flex: 1;
    display: flex;
    /* flex-direction: row; */
    flex-flow: wrap;
    justify-content: space-between;
    .alarnRow {
      // width: 20%;
      // width: calc( 20% - 10px);
      width: ~"calc(20% - 10px)";
      height: 244px;
      margin-bottom: 12px;
    }
  }
  /deep/ .more-search {
    padding-left: 0 !important;
  }
}
</style>
