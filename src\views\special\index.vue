<template>
  <div class="container">
    <div class="title title-top">
      <p class="english">Thematic application</p>
      <p class="Chinese">专题应用</p>
    </div>
    <div class="special-box">
      <swiper ref="mySwiper" :options="swiperOption" class="list_tab">
        <template v-for="(item, index) in fuseList">
          <swiper-slide :ref="'swiperSlide' + index" :key="index">
            <div class="components_list-box">
              <div class="components_list">
                <i-link :to="goPage(item.address)" target="_blank">
                  <p class="components_list_title">
                    {{ item.applicationName }}
                  </p>
                  <!-- <p class="components_list_subtitle">{{ "" }}</p> -->
                  <div class="components_list_line"></div>
                  <img :src="getImgUrl(item, index)" alt="" />
                </i-link>
              </div>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div>
        <div
          class="swiper-button-prev snap-prev"
          slot="button-prev"
          id="frequentLeft"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div
          class="swiper-button-next snap-next"
          slot="button-next"
          id="frequentRight"
        >
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <img
      class="childPic"
      v-show="child"
      src="@/assets/img/model/child.png"
      alt=""
      srcset=""
      @click="child = false"
    />
  </div>
</template>

<script>
import { getToken } from "@/libs/configuration/util.common";
import { mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
// 等比数列求和
const powerSeriesMath = (base, n) => {
  if (base === 1) return n; // 1的任何次方都是1，累加就是n
  return (base * (Math.pow(base, n) - 1)) / (base - 1);
};
export default {
  name: "",
  components: { swiper, swiperSlide },
  data() {
    const that = this;
    const baseScale = 0.9; // 基础缩放比例
    const baseWidth = 300; // 卡片原始宽度
    const setSwiperSlide = () => {
      const length = that.mySwiper?.slides?.length || 0;
      // 获取 activeIndex 是第几个
      const activeIndex = that.mySwiper?.activeIndex || 0;
      for (let i = 0; i < length; i++) {
        const slide = that.mySwiper?.slides[i];
        // 距离中间是第几个
        const progress = Math.abs(activeIndex - i);
        // 缩放比
        const scale = Math.pow(baseScale, progress);
        // 位移的长度 贴合左右两侧的元素 300 卡片原始宽度
        const X =
          progress == 0
            ? 0
            : baseWidth *
              (progress - 1 - powerSeriesMath(baseScale, progress - 1));
        // 由于缩放原点不同 缩放和位移不能放在同一个卡片上
        const slideScaleChild = slide.children[0].children[0];
        const slideXChild = slide.children[0];
        // 要是需要有点缝隙 就注释transformOrigin
        if (activeIndex - i < 0) {
          slideScaleChild.style.transformOrigin = "0% 50%";
          slideXChild.style.transform = `translateX(${-X}px)`;
        } else {
          slideScaleChild.style.transformOrigin = "100% 50%";
          slideXChild.style.transform = `translateX(${X}px)`;
        }
        slideScaleChild.style.transform = "scale(" + scale + ")";
        // 修改宽度是为了在flex布局下左右贴合
        slideXChild.style.width = baseScale * scale + "px";
        // 层级 避免位移计算精度不够会有覆盖的问题
        const zIndex = length - progress;
        slide.style.zIndex = zIndex;
      }
    };
    const setTransition = () => {
      // that.mySwiper?.slides  不是枚举值 不能forEach
      const length = that.mySwiper?.slides?.length || 0;
      for (let i = 0; i < length; i++) {
        const slide = that.mySwiper?.slides[i];
        slide.children[0].style.transition = "all 1s";
      }
    };
    return {
      child: false,
      mySwiper: null,
      swiperOption: {
        speed: 1000,
        initialSlide: 2,
        slidesPerView: 6,
        centeredSlides: true,
        on: {
          init: function () {
            // 获取swiper实例
            that.mySwiper = this;
            setSwiperSlide();
            setTimeout(() => {
              // 等元素都设置好初始位置时，添加transition
              setTransition();
            }, 500);
          },
          slideNextTransitionStart: setSwiperSlide, // 向左切换的开始
          slidePrevTransitionStart: setSwiperSlide, // 向右切换的开始
          touchMove: setSwiperSlide, // 触摸移动结束时变换
        },
        navigation: {
          nextEl: "#frequentRight",
          prevEl: "#frequentLeft",
        },
      },
      mySwiper: null, // 最原始的swiper的对象
    };
  },
  watch: {},
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
    fuseList() {
      const result =
        this.userInfo.sysApplicationVoList?.filter(
          (item) => item.address?.indexOf("monographic-") > -1
        ) || [];
      // 修改初始展示的卡片 目的卡片左右对称展示
      this.swiperOption.initialSlide =
        parseInt(result.length / 2) > 4 ? 3 : parseInt(result.length / 2) - 1;
      return result;
    },
    jumpToken() {
      return getToken();
    },
  },
  created() {},
  mounted() {},
  methods: {
    goPage(address) {
      // /结尾的正则表达式
      return address + "?refresh_token=" + this.jumpToken;
    },
    getImgUrl(item, index) {
      const specialName = item.address.split("monographic-")[1];
      const imageName = "monographic-" + specialName.split("/")[0];
      let image = null;
      try {
        image = require(`@/assets/img/model/${imageName}.png`);
      } catch (e) {
        image = require(`@/assets/img/model/monographic-community.png`);
      }
      return image;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  position: relative;
  background: url("~@/assets/img/model/dot.png") no-repeat, #fff;
  background-position: bottom;
  overflow: hidden;
  .special-box {
    width: 1800px;
    height: 500px;
    margin: 0 auto;
  }
}
.childPic {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.title {
  font-size: 26px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.9);
  text-align: center;
  position: relative;
  .english {
    font-size: 40px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.3);
    color: #d3d7de;
    opacity: 0.3;
    text-transform: uppercase;
  }
  .Chinese {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
}
.title-top {
  margin: 15px 0 61px;
}
.title-sec {
  margin: 0px 0 41px;
}
.img {
  background: url("~@/assets/img/model/pic1.png") no-repeat;
}
.list_tab {
  width: 100%;
  // margin: auto;
  height: 500px;
  padding-top: 20px;
  /deep/ .swiper-slide {
    // 如果一开始就设置这个 卡片出现会位移
    // transition: all 1s;
    .components_list-box {
      transition: inherit;
      .components_list {
        transition: inherit;
      }
    }
    &:hover {
      transform: translateY(-10px);
      // box-shadow: 0px 8px 12px 0px rgba(147, 171, 206, 0.7);
    }
  }
  .components_list {
    width: 300px;
    height: 460px;
    background: #f9f9f9;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    position: relative;
    margin-bottom: 50px;
    padding-left: 20px;
    cursor: pointer;
    a {
      display: inline-block;
      width: 100%;
      height: 100%;
    }
    &:hover {
      box-shadow: 0px 8px 12px 0px rgba(147, 171, 206, 0.7);
    }
    img {
      width: 216px;
      height: 225px;
      position: absolute;
      right: 0;
      bottom: 20px;
    }
    &_title {
      font-size: 20px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      margin-top: 30px;
    }
    &_subtitle {
      font-size: 10px;
      color: rgba(0, 0, 0, 0.6);
      margin: 10px 0 7px;
    }
    &_line {
      width: 40px;
      height: 6px;
      background: #2c86f8;
    }
    .child_ul {
      display: flex;
      margin-top: 15px;
      .child_li {
        font-size: 14px;
        color: #2c86f8;
        margin-right: 20px;
        &:hover {
          color: #4597ff;
        }
      }
    }
  }
}
.card-tab {
  width: 300px;
  height: 150px;
}
.swiper-button-prev,
.swiper-button-next {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 70px;
  margin-top: -15px;

  .iconfont {
    color: #fff;
    font-size: 40px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 40px;
}

.swiper-button-next {
  right: 40px;
}
</style>
