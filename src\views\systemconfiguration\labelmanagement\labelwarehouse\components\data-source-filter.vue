<template>
  <div>
    <Button type="primary" @click="changeCheckboxVisible">
      <span class="inline vt-middle mr-xs">{{ title }}</span>
      <i
        class="icon-font icon-shujushaixuan-xiangshang-line f-14"
        :class="checkboxVisible ? '' : 'active-checkbox'"
      ></i>
    </Button>
    <div class="checkbox-div mt-sm" v-show="checkboxVisible" ref="checkboxRef">
      <CheckboxGroup v-model="searchData.source" v-if="questionList.length !== 0">
        <Checkbox
          v-for="(item, index) in questionList"
          :key="index"
          :label="item[filterAction.field.id]"
          :title="item[filterAction.field.value]"
        >
          {{ item[filterAction.field.value] }}
        </Checkbox>
      </CheckboxGroup>
      <p class="t-center checkbox-nodata" v-else>暂无数据</p>
    </div>
    <p class="attention">
      {{ filterAction.contentOne }}
      <span class="font-blue">{{ filterAction.contentActive }}</span>
      {{ filterAction.contentTwo }}
    </p>
  </div>
</template>

<script>
export default {
  props: {
    questionList: {
      default: () => [],
    },
    minusTable: {
      default: 0,
    },
    title: {
      default: '通过数据来源筛选',
    },
    filterAction: {
      default: () => {
        return {
          contentOne: '注：在重复记录列表中可通过',
          contentTwo: '快速筛选或针对目标字段选择性勾选为要进行处理的待检测数据',
          contentActive: '“数据来源”',
          field: {
            id: 'sourceId',
            value: 'sourceName',
          },
        };
      },
    },
  },
  data() {
    return {
      checkboxVisible: false,
      searchData: {
        source: [],
      },
    };
  },
  components: {},

  mounted() {},

  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    changeCheckboxVisible() {
      this.checkboxVisible = !this.checkboxVisible;
      this.$nextTick(() => {
        let changeHeight = this.checkboxVisible ? this.$refs.checkboxRef.offsetHeight + 10 : 0;
        this.$emit('changeCheckboxVisible', this.minusTable + changeHeight);
      });
    },
    /* ----------------------------------------- 自定义方法 ----------------------------------------- */
  },
  watch: {
    'searchData.source': {
      handler(val) {
        this.$emit('dataSourceChange', val);
      },
    },
  },
};
</script>
<style lang="less" scoped>
.checkbox-div {
  padding: 10px 20px;
  border: 1px solid var(--color-primary);
  @{_deep}.ivu-checkbox-group-item {
    max-width: 153px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .checkbox-nodata {
    color: #fff;
    font-size: 14px;
  }
}
.attention {
  font-size: 12px;
  height: 40px;
  color: #fff;
  line-height: 40px;
}
</style>
