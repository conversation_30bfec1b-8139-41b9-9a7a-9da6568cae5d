<template>
	<div>
		<ul class="search_tab">
			<li v-for="(item, index) in tablist" :key="index" class="tabslist" @click="tabChange(item)" :class="{ active: compareType == item.compareType }">
				{{ item.name }}
			</li>
		</ul>
		<template v-if="compareType == 1">
			<Table class="auto-fill table" :columns="peopleColumns" border :data="peopleTableData" max-height="580">
				<template #name="{ index }">
					<Input placeholder="请输入" v-model="peopleTableData[index].name" maxlength="50" class="input-wid"></Input>
				</template>
				<template #idCardNo="{ index }">
					<Input placeholder="请输入" v-model="peopleTableData[index].idCardNo" maxlength="50" class="input-wid"></Input>
				</template>
				<template #national="{ index }">
					<Select placeholder="请选择" v-model="peopleTableData[index].national" transfer :disabled="peopleTableData[index].idCardNo == '' || peopleTableData[index].idCardNo == null || peopleTableData[index].idCardNo.trim() == ''">
						<Option v-for="item in nationList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
							{{ item.dataValue }}
						</Option>
					</Select>
				</template>
				<template #sex="{ index }">
					<Select placeholder="请选择" v-model="peopleTableData[index].sex" transfer :disabled="peopleTableData[index].idCardNo == '' || peopleTableData[index].idCardNo == null || peopleTableData[index].idCardNo.trim() == ''">
						<Option v-for="item in genderList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
							{{ item.dataValue }}
						</Option>
					</Select>
				</template>
				<template #photoList="{ index }">
					<uiUploadImg ref="uploadImg" uploadUlr v-model="peopleTableData[index].images" size="mini2" :algorithmType="1" />
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(1, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(1)" v-if="peopleTableData.length < 10">
					<Icon type="md-add" />
			</div>
		</template>
		<template v-if="compareType == 2">
			<Table class="auto-fill table" :columns="vehicleColumns" border :data="vehicleTableData" max-height="580">
				<template #name="{ index }">
					<Input placeholder="请输入" v-model="vehicleTableData[index].name" maxlength="50" class="input-wid"></Input>
				</template>
				<template #plateColor="{ index }">
					<Select placeholder="请选择" v-model="vehicleTableData[index].plateColor" transfer>
						<Option v-for="item in licensePlateColorList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
							{{ item.dataValue }}
						</Option>
					</Select>
				</template>
				<template #plateNo="{ index }">
					<Input placeholder="请输入" v-model="vehicleTableData[index].plateNo" maxlength="50" class="input-wid"></Input>
				</template>

				<template #vehicleType="{ index }">
					<el-select placeholder="请选择" size="small" v-model="vehicleTableData[index].vehicleType" filterable @on-change="selectChange(vehicleTableData[index])">
							<el-option v-for="item in vehicleClassTypeList" :value="item.dataKey" :label="item.dataValue" :key="item.dataKey"></el-option>
					</el-select>
				</template>
				<template #vehicleColor="{ index }">
					<Select placeholder="请选择" v-model="vehicleTableData[index].vehicleColor" transfer>
						<Option v-for="item in bodyColorList" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
							{{ item.dataValue }}
						</Option>
					</Select>
				</template>
				<template #vehicleBrand="{ index }">
					<div class="select-tag-button" @click="selectBrandHandle(index)">{{vehicleTableData[index].vehicleBrand ? vehicleBrandInfo(vehicleTableData[index].vehicleBrand) : '车辆品牌'}}</div>
				</template>
				<template #photoList="{ index }">
					<carUploadImg ref="uploadImg" uploadUlr v-model="vehicleTableData[index].images" size="mini2" :algorithmType="2" />
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(2, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(2)" v-if="vehicleTableData.length < 10">
				<Icon type="md-add" />
			</div>
		</template>
		<template v-if="compareType == 3">
			<Table class="auto-fill table" :columns="wifiColumns" border :data="wifiTableData" max-height="580">
				<template #mac="{ index }">
					<Input placeholder="请输入" v-model="wifiTableData[index].mac" maxlength="50" class="input-wid"></Input>
				</template>
				<template #name="{ index }">
					<Input placeholder="请输入" v-model="wifiTableData[index].name" maxlength="50" class="input-wid"></Input>
				</template>
				<template #idCardNo="{ index }">
					<Input placeholder="请输入" v-model="wifiTableData[index].idCardNo" maxlength="50" class="input-wid"></Input>
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(3, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(3)" v-if="wifiTableData.length < 10">
				<Icon type="md-add" />
			</div>
		</template>
		<template v-if="compareType == 5">
			<Table class="auto-fill table" :columns="rfidColumns" border :data="rfidTableData" max-height="580">
				<template #rfidCode="{ index }">
					<Input placeholder="请输入" v-model="rfidTableData[index].rfidCode" maxlength="50" class="input-wid"></Input>
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(5, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(5)" v-if="rfidTableData.length < 10">
				<Icon type="md-add" />
			</div>
		</template>
		<template v-if="compareType == 4">
			<Table class="auto-fill table" :columns="electricColumns" border :data="electricTableData" max-height="580">
				<template #imsi="{ index }">
					<Input placeholder="请输入" v-model="electricTableData[index].imsi" maxlength="50" class="input-wid"></Input>
				</template>
				<template #imei="{ index }">
					<Input placeholder="请输入" v-model="electricTableData[index].imei" maxlength="50" class="input-wid"></Input>
				</template>
				<template #name="{ index }">
					<Input placeholder="请输入" v-model="electricTableData[index].name" maxlength="50" class="input-wid"></Input>
				</template>
				<template #idCardNo="{ index }">
					<Input placeholder="请输入" v-model="electricTableData[index].idCardNo" maxlength="50" class="input-wid"></Input>
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(4, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(4)" v-if="electricTableData.length < 10">
				<Icon type="md-add" />
			</div>
		</template>
		<template v-if="compareType == 6">
			<Table class="auto-fill table" :columns="etcColumns" border :data="etcTableData" max-height="580">
				<template #obuId="{ index }">
					<Input placeholder="请输入" v-model="etcTableData[index].obuId" maxlength="50" class="input-wid"></Input>
				</template>
				<template #dictionaryCode="{ row, index }">
					<div class="btn-tips">
						<ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(6, row, index)" />
					</div>
				</template>
			</Table>
			<div class="add-from" @click="addForm(6)" v-if="etcTableData.length < 10">
				<Icon type="md-add" />
			</div>
		</template>

		<!-- 选择品牌 -->
		<BrandModal ref="brandModal" @on-change="selectBrand" />
	</div>
</template>
<script>
	import uiUploadImg from '@/components/ui-upload-img-control-task/index'
	import carUploadImg from '@/components/ui-upload-img/index'
	import { mapGetters, mapActions } from 'vuex'
	import BrandModal from '@/views/target-control/components/brand-modal.vue'
	export default {
		components: {
			uiUploadImg,
			carUploadImg,
			BrandModal
		},
		props: {
			//对应字典
			dictTypedata: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				compareType: 1,
				tablist: [
          { name: '人员', compareType: 1},
          { name: '车辆', compareType: 2},
          { name: 'WIFI', compareType: 3},
          { name: 'RFID', compareType: 5},
          { name: '电围', compareType: 4},
          { name: 'ETC', compareType: 6},
        ],
				peopleColumns: [
					{ title: '姓名（必填）', slot: 'name', width: 160 },
					{ title: '身份证号（必填）', slot: 'idCardNo' },
					{ title: '民族', slot: 'national', width: 140 },
					{ title: '性别（必填）', slot: 'sex', width: 160 },
					{ title: '上传照片（必填）', slot: 'photoList', width: 240 },
					{ title: '操作', slot: 'dictionaryCode', width: 70 },
				],
				peopleTableData: [{}],
				vehicleColumns: [
					{ title: '车牌号码（必填）', slot: 'plateNo', width: 150 },
					{ title: '车牌颜色（必填）', slot: 'plateColor', width: 150 },
					{ title: '车主姓名', slot: 'name' },
					{ title: '车辆类型', slot: 'vehicleType' },
					{ title: '车辆颜色', slot: 'vehicleColor' },
					{ title: '车辆品牌', slot: 'vehicleBrand' },
					{ title: '上传照片', slot: 'photoList', width: 220 },
					{ title: '操作', slot: 'dictionaryCode', width: 70 },
				],
				vehicleTableData: [{ selectVehicle: false }],
				wifiColumns: [
					{ title: "Mac地址（必填）", slot: "mac" },
					{ title: "身份证号", slot: "idCardNo" },
					{ title: "姓名", slot: "name" },
					{ title: "操作", slot: "dictionaryCode", width: 70 },
				],
				wifiTableData: [],
				rfidColumns: [
					{ title: "Rfid地址（必填）", slot: "rfidCode" },
					{ title: "操作", slot: "dictionaryCode", width: 70 },
				],
				rfidTableData: [],
				electricColumns: [
					{ title: "IMSI编码（必填）", slot: "imsi" },
					{ title: "国际移动台设备识别码", slot: "imei" },
					{ title: "身份证号", slot: "idCardNo" },
					{ title: "姓名", slot: "name" },
					{ title: "操作", slot: "dictionaryCode", width: 70 },
				],
				electricTableData: [],
				etcColumns: [
					{ title: "Etc编号（必填）", slot: "obuId" },
					{ title: "操作", slot: "dictionaryCode", width: 70 },
				],
				etcTableData: [],
			}
		},
		computed: {
			...mapGetters({
				searchTypeList: 'dictionary/getSearchTypeList', //检索类型
				genderList: 'dictionary/getGenderList', //性别
				nationList: 'dictionary/getNationList', //民族
				licensePlateColorList: 'dictionary/getLicensePlateColorList', //车牌颜色
				bodyColorList: 'dictionary/getBodyColorList', //车辆颜色
				vehicleBrandList: 'dictionary/getVehicleBrandList', //车辆品牌
				vehicleClassTypeList: 'dictionary/getVehicleTypeList', //车辆类型
				vehicleUseStatus: 'dictionary/getVehicleUseStatus', //车辆使用状态
				vehicleUseNature: 'dictionary/getVehicleUseNature', //车辆使用性质
			})
		},
		watch: {},
		async created() {
			await this.getDictData();
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			tabChange(row) {
        this.compareType = row.compareType
      },
			// 删除
			handleDele(compareType, row, index) {
				let list = []
				switch (compareType) {
					case 1:
						list = this.peopleTableData
						break
					case 2:
						list = this.vehicleTableData
						break
					case 3:
						list = this.wifiTableData
						break
					case 5:
						list = this.rfidTableData
						break
					case 4:
						list = this.electricTableData
						break
					case 6:
						list = this.etcTableData
						break
				}
				if (list.length > 0) {
					list.splice(index, 1)
				}
			},
			addForm(compareType) {
				switch (compareType) {
					case 1:
						this.peopleTableData.push({})
						break
					case 2:
						this.vehicleTableData.push({ selectVehicle: false })
						break
					case 3:
						this.wifiTableData.push({})
						break
					case 5:
						this.rfidTableData.push({})
						break
					case 4:
						this.electricTableData.push({})
						break
					case 6:
						this.etcTableData.push({})
						break
				}
			},
			selectChange(row) {
				console.log('---row:',this.vehicleClassTypeList, row)
				row.selectVehicle = true;
			},
			vehicleBrandInfo(key) {
				var obj = this.vehicleBrandList.find(item => item.dataKey == key)
				return obj.dataValue
			},
			// 选择品牌
			selectBrandHandle(index) {
				this.$refs.brandModal.show(index)
			},
			/**
			 * 车辆品牌已选择，返回数据
			 */
			selectBrand(arr, index, name) {
				this.$nextTick(() => {
					this.$set(this.vehicleTableData[index], 'vehicleBrand', name)
				})
			},
		}
	}
</script>
<style lang="less" scoped>
	.config {
		/deep/ .ivu-table-body {
			min-height: 220px;
		}
		/deep/.ivu-table-tbody tr td {
			background-color: #f9f9f9 !important;
		}
		/deep/ .ivu-table-border th {
			border-right: 1px solid #d3d7de !important;
		}
		/deep/.ivu-table td,
		.ivu-table th {
			border-bottom: 1px solid #d3d7de;
		}
		/deep/.ivu-input,
		.ivu-select {
			width: 110px;
		}
	}
	.add-from {
		background: #f9f9f9;
		margin-top: 3px;
		border: 1px solid #e8eaec;
		text-align: center;
		cursor: pointer;
		/deep/ .ivu-icon-md-add {
			color: #909399;
		}
	}
	.btn-tips {
		text-align: center;
	}
	.select-tag-button {
		width: auto;
		max-width: 80px;
		padding: 0 2px;
	}

	.filterable {
		/deep/ .ivu-select-input {
			width: 270px;
			margin-left: -80px;
		}
	}
	.search_tab {
		display: flex;
		justify-content: space-evenly;
		border-bottom: 1px solid #d3d7de;
		margin-bottom: 15px;
		.tabslist {
			font-size: 14px;
			color: rgba(0, 0, 0, 0.6);
			width: 25%;
			text-align: center;
			padding: 7px 0;
			cursor: pointer;
		}
		.active {
			color: #2c86f8;
			border-bottom: 3px solid #2c86f8;
		}
	}
</style>
