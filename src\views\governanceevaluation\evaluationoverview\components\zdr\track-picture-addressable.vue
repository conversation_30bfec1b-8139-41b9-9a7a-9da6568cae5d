<!-- 轨迹图片可访问率 -->
<template>
  <div class="track-picture-addressable" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"> </statistics>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <tagView class="tagView fr" ref="tagView" :list="['图像模式', '聚档模式']" @tagChange="tagChange1" />
      </div>
      <!-- 设备模式 -->
      <TableList
        class="list"
        v-if="modelTag == 0 && tableColumns.length > 0"
        ref="infoList"
        :contentClientHeight="contentClientHeight"
        :columns="tableColumns"
        :loadData="loadDataList"
      >
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <SearchCard :searchNum="3" :checkStatus="checkStatus" @startSearch="startSearch"></SearchCard>
          <!-- <Button type="primary" class="btn_search">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button> -->
        </div>
        <!-- 表格操作 -->
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
            <ui-image :src="row.trackImage" />
          </div>
        </template>
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image :src="row.identityPhoto" alt="" />
          </div>
        </template>
        <template #urlAvailableStatus="{ row }">
          <span style="color: #19c176" v-if="row.urlAvailableStatus == 1">URL可访问</span>
          <span style="color: #c43d2c" v-else>URL不可访问</span>
        </template>
        <template #thumbnailStatus="{ row }">
          <span style="color: #19c176" v-if="row.thumbnailStatus == 1">可用</span>
          <span style="color: #c43d2c" v-else-if="row.thumbnailStatus == 2">不可用</span>
          <span v-else>--</span>
        </template>
        <template #largeStatus="{ row }">
          <span style="color: #19c176" v-if="row.largeStatus == 1">可用</span>
          <span style="color: #c43d2c" v-else-if="row.largeStatus == 2">不可用</span>
          <span v-else>--</span>
        </template>
        <template #algResult="{ row }">
          <span v-if="row.algResult">
            <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
              <span>{{ item.algorithmType }}:</span>
              <span>{{ item.score }}%</span>
            </div>
          </span>
          <span v-else>--</span>
        </template>
      </TableList>
      <!-- 图像模式 -->
      <TableCard
        ref="infoCard"
        class="card-list"
        :loadData="loadDataCard"
        :cardInfo="cardInfo"
        v-if="modelTag == 1"
        :contentClientHeight="contentClientHeight"
      >
        <div slot="search" class="hearder-title">
          <SearchCard :is-image-model="false" @startSearch="startSearch"></SearchCard>
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <InfoCard
            class="card1"
            :list="row"
            :personTypeList="personTypeList"
            :cardInfo="cardInfo"
            @detail="detailInfo(row)"
          >
          </InfoCard>
        </template>
      </TableCard>
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
      <person-detail
        ref="captureDetail"
        :resultId="{
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        }"
        :interFaceName="getSecondaryPopUpData"
        :staticsList="personList"
      >
        <template #searchList>
          <SearchCard
            :searchNum="3"
            :checkStatus="checkStatus"
            class="mb-sm track-search"
            @startSearch="startDetailSearch"
          ></SearchCard>
        </template>
      </person-detail>
    </div>
  </div>
</template>

<style lang="less" scoped>
.track-picture-addressable {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 460px !important;
    max-height: calc(100vh - 460px);
  }
  .information-header {
    margin-top: 10px;
    display: flex;
    .information-statistics {
      display: flex;
      width: 100%;
      height: 100%;
      background: var(--bg-sub-content);
      padding: 15px;
      margin-right: 0px !important;
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 19.54% !important;
      margin-bottom: 0px !important;
      &:nth-child(5) {
        margin-right: 0;
      }
      &:nth-child(6) {
        margin-top: 10px;
      }
      .monitoring-data {
        margin-left: 30px;
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      .ui-images {
        width: 56px !important;
        height: 56px !important;
      }
    }
    .hearder-title {
      position: relative;
      padding: 10px 0;

      .btn_search {
        position: absolute;
        right: 0px;
        top: 10px;
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'track-picture-addressable',
  data() {
    return {
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      rankLoading: false,
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage' },
        { title: '抓拍时间', key: 'shotTime', width: 170 },
        { title: '抓拍点位', key: 'catchPlace' },
        { title: '档案照', key: 'identityPhoto', slot: 'identityPhoto' },
        { title: '姓名', key: 'name' },
        { title: '证件号', key: 'idCard' },
        {
          title: '小图URL是否可用',
          key: 'thumbnailStatus',
          slot: 'thumbnailStatus',
        },
        { title: '大图URL是否可用', key: 'largeStatus', slot: 'largeStatus' },
        {
          title: '检测结果',
          key: 'urlAvailableStatus',
          slot: 'urlAvailableStatus',
        },
      ],
      tableData: [],
      searchData: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      statisticalList: {},
      rankData: [],
      paramsList: {},
      loadDataList: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getDetailData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            evaluationoverview.getPolyData,
            Object.assign(
              parameter,
              {
                indexId: this.paramsList.indexId,
                batchId: this.paramsList.batchId,
                access: this.paramsList.access,
                displayType: this.paramsList.displayType,
                orgRegionCode: this.paramsList.orgRegionCode,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      modelTag: 0, // 设备模式,图像模式
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'total' },
        { name: '异常轨迹：', value: 'abnormal', color: '#BC3C19' },
      ],
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')], // 大图图片
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      currentRow: {},
      checkStatus: [
        { name: 'url可访问', checkKey: 1 },
        { name: 'URL不可访问', checkKey: 2 },
        { name: '小图URL不可访问', checkKey: 3 },
        { name: '大图URL不可访问', checkKey: 4 },
      ],
      personList: [
        {
          label: '抓拍总量',
          count: 0,
          filedName: 'catchAmount',
        },
        {
          label: 'URL不可访问轨迹数量',
          count: 0,
          filedName: 'unqualifiedAmount',
        },
        {
          label: '大图URL不可访问轨迹数量',
          count: 0,
          filedName: 'largeNotAvailableAmount',
        },
        {
          label: '小图URL不可访问轨迹数量',
          count: 0,
          filedName: 'smallNotAvailableAmount',
        },
      ],
      statisticsList: [
        // {
        //   name: 'ZRD人员总量',
        //   value: 0,
        //   icon: 'icon-ZRDzongliang',
        //   iconColor: 'icon-bg1',
        //   liBg: 'li-bg1',
        //   type: 'number',
        //   textColor: 'color1',
        // key:'personAmount'
        // },
        {
          name: 'ZRD人像轨迹总量',
          value: 0,
          icon: 'icon-guijizhunqueshuai',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'trackAmount',
        },
        {
          name: '检测轨迹数量',
          value: 0,
          icon: 'icon-jianceguijishuliang',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          textColor: 'color5',
          key: 'detectionAmount',
        },
        {
          name: 'URL不可访问轨迹数量',
          value: 0,
          icon: 'icon-urlbukefangwenguijishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          textColor: 'color6',
          key: 'impeachAmount',
        },
        {
          name: '大图URL不可访问轨迹数量',
          value: 0,
          icon: 'icon-URLkeyongtupianshuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'largeUrlNotAvailableAmount',
        },
        {
          name: '小图URL不可访问轨迹数量',
          value: 0,
          icon: 'icon-xiaotuurlbukefangwenguijishuliang1',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          type: 'number',
          textColor: 'color8',
          key: 'smallUrlNotAvailableAmount',
        },
        {
          name: '轨迹图片可访问率',
          value: '0.0%',
          icon: 'icon-guijitupiankefangwenshuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          textColor: 'color3',
          key: 'resultValue',
        },
      ],
      contentClientHeight: 0,
    };
  },
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 103 * proportion : 0;
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },

    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async init() {
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },

    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(info);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    statistics: require('@/components/icon-statistics').default,
    TableList: require('./components/tableList.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    SearchCard: require('./components/searchCard.vue').default,
    InfoCard: require('./components/ui-gather-card.vue').default,
    tagView: require('./components/tags.vue').default,
    personDetail: require('./components/person-track.vue').default,
    LookScene: require('@/components/look-scene').default,
  },
};
</script>
