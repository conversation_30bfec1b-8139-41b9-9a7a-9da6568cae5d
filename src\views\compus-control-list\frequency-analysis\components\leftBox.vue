<!--
    * @FileDescription: 频次分析
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <div class="search-box">
            <div class="title">
                <p>频次分析</p>
            </div>
            <div class="search_condition">
                <div class="search_form">
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">学校</p>
                        </div>
                        <div class="search_content">
                            <Select v-model="queryParams.schoolId" filterable placeholder="请选择校园" class="input-200">
                                <Option v-for="(item, $index) in schoolList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
                            </Select>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">出现频次</p>
                        </div>
                        <div class="search_content">
                            <InputNumber v-model="queryParams.appearCount" class="wrapper-input"></InputNumber>
                            <span class="ml-10">次及以上</span>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">开始时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.startDate" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="开始时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">结束时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.endDate" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="结束时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">身份证号</p>
                        </div>
                        <div class="search_content">
                            <Input v-model="queryParams.idNumber" placeholder="请输入身份证号" class="wrapper-input"></Input>
                        </div>
                    </div>
                    <div class="btn-group">
                        <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import selectNonMotorIllegalDevice from '@/components/select-modal/select-nonMotorIllegal-device.vue'
export default {
    name: '',
    data () {
        return {
            queryParams:{
                idNumber: '',
                appearCount: 5,
                schoolId: ''
            }
        }
    },
    computed: {
      ...mapGetters({
        schoolList: 'dictionary/getSchoolList', // 学校
        strangerFlagList: 'dictionary/getStrangerFlagList', // 检测状态
      })
    },
    async created() {
        this.queryParams.startDate = this.getAgoDay(30);
        this.queryParams.endDate = this.getAgoDay(0);
        await this.getDictData();
        this.queryParams.schoolId = this.schoolList[0].dataKey
    },
    methods: {
        ...mapActions({
            getDictData: 'dictionary/getDictAllData'
        }),
        // 查询
        handleSearch() {
            this.queryParams.startDate = this.$dayjs(this.queryParams.startDate).format('YYYY-MM-DD HH:mm:ss');
            this.queryParams.endDate = this.$dayjs(this.queryParams.endDate).format('YYYY-MM-DD HH:mm:ss');
            this.$emit('search', this.queryParams)
        },
        // 时间判断
        getAgoDay(n){
            let date= new Date();
            let newDate = new Date(date.getTime() - n*24*60*60*1000);
            var Y = newDate.getFullYear() + '-';
            var M = (newDate.getMonth()+1 < 10 ? '0'+(newDate.getMonth()+1) : newDate.getMonth()+1) + '-';
            var D = newDate.getDate() < 10 ? '0'+ newDate.getDate() + ' ' : newDate.getDate() + ' ';
            var h = newDate.getHours() < 10 ? '0'+ newDate.getHours() + ':' : newDate.getHours() + ':';
            var m = newDate.getMinutes() < 10 ? '0'+ newDate.getMinutes() + ':' : newDate.getMinutes() + ':';
            var s = newDate.getSeconds() < 10 ? '0'+ newDate.getSeconds() : newDate.getSeconds();
            return Y+M+D+h+m+s;
        },
    }
}
</script>

<style lang='less' scoped>
@import './style/index';

/deep/ .ivu-date-picker{
    width: 100%;
}
/deep/ .ivu-tag-select-option{
    margin-right: 5px !important;
}
</style>
