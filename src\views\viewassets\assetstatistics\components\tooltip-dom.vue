<template>
  <div class="tooltip-dom" v-if="!!toolTipItem">
    <i class="icon-font vt-middle mr-xs" :class="toolTipItem?.nameIcon ?? 'icon-dingwei1'"></i>
    <div class="tooltip-dom-wrapper">
      <p class="tooltip-dom-title mb-xs f-14">
        {{ toolTipItem?.name ?? '未知' }}
        <!-- 补充标题 -->
        <slot name="supplement"></slot>
      </p>
      <div class="tooltip-dom-content" v-if="!!toolTipItem?.list && !!toolTipItem.list.length">
        <p class="tooltip-dom-content-p f-12" v-for="(item, index) in toolTipItem.list" :key="index">
          <span class="label inline ellipsis mr-sm" title="">
            <i class="point mr-xs" :style="{ background: item?.color }"></i>
            {{ item.label }}
          </span>
          <span class="num inline ellipsis mr-sm" title="" :style="{ color: item?.numColor ?? 'var(--color-content)' }"
            >{{ item.num }}
          </span>
          <template v-if="'slotName' in item">
            <slot :name="item.slotName"></slot>
          </template>
        </p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'tooltipss-dom',
  props: {},
  data() {
    return {
      // toolTipItem: {
      //   name: '测试',
      //   list: [
      //     { label: '1111', color: 'red', slotName: 'slotA' }
      //   ]
      // },
      toolTipItem: null,
    };
  },
  mounted() {},
  methods: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tooltip-dom {
  border-radius: 8px 8px 8px 8px;
  display: flex;
  // min-width: 230px;
  padding: 0 5px;
  .tooltip-dom-title {
    line-height: 25px;
    height: 25px;
  }
  .icon-font {
    color: var(--color-primary);
  }
  .tooltip-dom-content {
    .point {
      width: 8px;
      height: 8px;
      background: #1b85ff;
    }
    .tooltip-dom-content-p {
      display: flex;
    }
  }
}
</style>
