<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" :title="title" width="66.14rem" @onCancel="reset" @query="handleSave">
      <transfer-table
        @onLeftToRight="selectionChange"
        :left-loading="leftLoading"
        :right-loading="rightLoading"
        :left-table-columns="columns1"
        :right-table-columns="columns2"
        :left-table-data="propertyList"
        :right-table-data="targetList"
      >
        <template #left-title>
          <span class="base-text-color mb-md">请确定比对字段：</span>
        </template>
        <template #right-title>
          <span class="base-text-color mb-md">需比对字段：</span>
        </template>
      </transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/assetcomparison';
export default {
  props: {
    title: {
      type: String,
      default: '配置比对字段',
    },
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { title: '字段名', key: 'field' },
        { title: '注释', key: 'note' },
      ],
      propertyList: [],
      columns2: [
        { title: ' ', key: '', width: 14 },
        { title: '字段名', key: 'field' },
        { title: '注释', key: 'note' },
      ],
      targetList: [], // 字段名列表
      checkedList: [],
      checkedIds: [],
      indexRuleId: '',
      leftLoading: false,
      rightLoading: false,
    };
  },
  methods: {
    async init() {
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      this.getTableList();
    },
    //
    getTableList() {
      this.$http.post(api.listAll).then((res) => {
        this.propertyList = res.data.data;
        this.propertyList.forEach((item) => {
          if (item.isConfig == 1) {
            item._checked = true;
          }

          // 初始化必须为选中状态且不能修改 isConfig = 1
          if (item.id == 1) {
            item._disabled = true;
          }
        });
        this.targetList = res.data.data.filter((item) => {
          return item.isConfig == 1;
        });
        this.leftLoading = false;
        this.rightLoading = false;
      });
    },

    selectionChange(selection) {
      // debugger
      // selection = selection.map((item) => {
      //   let obj = {};
      //   obj.field = item.field;
      //   obj.note = item.note;
      //   // obj.column = `${item.propertyColumn} (${item.propertyName})`;
      //   return obj;
      // });
      this.targetList = selection;
    },
    // 保存
    async handleSave() {
      if (this.targetList.length == 0) {
        this.$Message.error('请选择比对字段！');
        return;
      }
      try {
        var ids = '';
        this.targetList.forEach((item) => {
          ids += item.id + ',';
        });

        await this.$http.put(api.update + ids.substring(0, ids.length - 1));
        this.reset();
        this.$emit('render');
        this.$Message.success('配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
  },
  watch: {},
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal {
  .ivu-modal-body {
    height: 450px;
  }
}

@{_deep} .transfer-table-wrapper {
  .left-table,
  .right-table {
    height: calc(~'100% - 35px') !important;
    margin-top: 10px;
  }
}

.repeat-empty {
  @{_deep} .ivu-modal-body {
    padding: 0 50px 16px 50px;
  }
}
</style>
