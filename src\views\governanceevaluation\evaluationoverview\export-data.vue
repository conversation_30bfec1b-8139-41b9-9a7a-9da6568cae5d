<template>
  <ui-modal v-model="visible" title="导出设备信息" :styles="styles">
    <div class="export-data">
      <Form ref="modalData" :model="searchData" class="form-content" :label-width="160">
        <FormItem label="组织机构" v-if="type == 'ORG'">
          <select-organization-tree
            v-if="visible"
            :defaultCheckedKeys="defaultCheckedKeys"
            :tree-list="treeData"
            @check="checkTree"
          ></select-organization-tree>
        </FormItem>
        <FormItem label="行政区划" v-else>
          <select-area-tree
            v-if="visible"
            :defaultCheckedKeys="defaultCheckedKeys"
            :tree-list="treeData"
            @check="checkTree"
          ></select-area-tree>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" @click="exportAdd" class="plr-30">确 定</Button>
      <Button @click="visible = false" class="plr-30">取 消</Button>
    </template>
  </ui-modal>
</template>

<style lang="less" scoped>
.export-data {
  @{_deep} .ivu-modal {
    width: 520px;
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px 20px;
    }
    .export-content {
      height: 420px;
      overflow: auto;
      position: relative;
    }
  }
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'export-data',
  props: {
    // 级联清单单独处理
    cascadeId: {},
    superiorToken: {},
    // paramsData: {
    //   type: Object,
    //   default: () => {},
    // },
    // reportList: {
    //   type: Object,
    //   default: () => {},
    // },
    // tag: {},
  },
  data() {
    return {
      visible: false,
      styles: { width: '4rem' },
      searchData: { regionCodes: [] },
      treeData: [],
      defaultCheckedKeys: [],
      treeList: [],
      type: '',
    };
  },
  mounted() {},

  methods: {
    init(val, code) {
      this.visible = true;
      this.type = val;
      this.getOrg(val, code);
    },
    hide() {
      this.visible = false;
      this.searchData.regionCodes = [];
      this.treeData = [];
    },
    async getOrg(val, code) {
      try {
        if (val == 'ORG') {
          let res = await this.$http.get(evaluationoverview.getAllChildOrgByOrgCode, {
            params: { orgCode: code },
          });
          let tempArr = this.$util.common.deepCopy(res.data.data);
          this.treeData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId');
        } else {
          let res = await this.$http.get(evaluationoverview.getChildRegionByCode, {
            params: { regionCode: code },
          });
          let tempArr = this.$util.common.deepCopy(res.data.data);
          this.treeData = this.$util.common.arrayToJson(tempArr, 'regionCode', 'parentCode');
        }
      } catch (error) {
        console.log(error);
      }
    },
    checkTree(list) {
      this.searchData.regionCodes = list;
    },
    exportAdd() {
      this.$emit('exportAdd', this.searchData);
    },
  },
  watch: {},
  components: {
    selectOrganizationTree: require('@/api-components/select-organization-tree.vue').default,
    selectAreaTree: require('@/api-components/select-area-tree.vue').default,
  },
};
</script>
