
import { mapGetters } from "vuex";
import {
    entitySearch,
    entityList 
  } from "@/api/number-cube";
let lableList = [];
export const entitySearchMixins = {
  computed: {
    ...mapGetters({
        relationObj: "systemParam/relationObj",
      }),
  },
  methods: {
    async getLableList(){
        const graphInfo = this.relationObj.graphInfo;
        const res = await entityList({
          graphIdList: graphInfo?.map((item) => item.graphId),
        });
        lableList = res?.data?.map(item => item.name)
    },
    async getEntityList(params) {
        if (!lableList.length) await this.getLableList();
        for (const name of lableList) {
            const res = await entitySearch({ name, ...params });
            const entities = res?.data?.entities;
            if (entities?.length > 0) return entities.slice(0, 1);
        }
        return [];
    },
  }
}
