<template>
  <div class="problem-detail-row d_flex" :class="rowClass ? rowClass : ''">
    <label ref="label" class="label" :class="labelClass">
      <span class="font-color f-14">{{ label }}</span>
    </label>
    <span v-if="content" class="font-color f-14">{{ content }}</span>
    <template v-if="imgList?.length">
      <div v-for="(imgURl, index) in imgList" :key="index" class="img-item pointer">
        <img :src="imgURl" @click="viewBigPic(index, imgURl)" alt="" />
      </div>
      <look-scene v-model="bigPictureShow" :img-list="bigImgList"></look-scene>
    </template>
    <slot></slot>
  </div>
</template>
<script>
export default {
  data() {
    return {
      bigPictureShow: false,
      bigImgList: [],
    };
  },
  props: {
    // 绑定的行class
    rowClass: {
      type: String,
      default: 'mb-sm',
    },
    //lebel名称
    label: {
      required: true,
    },
    //给label定义的类,设置和内容的边距
    labelClass: {
      type: String,
      default: '', //mr-sm
    },
    //label对齐方式
    labelAlign: {
      default: 'left',
    },
    //内容的宽度
    contentWidth: {
      type: [String, Number],
      default: '',
    },
    //传递的内容
    content: {
      type: String,
      default: '',
    },
    //若传递内容为图片
    imgList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  components: {},
  methods: {
    //查看大图
    viewBigPic(index, item) {
      this.bigImgList = [item];
      this.bigPictureShow = true;
    },
  },
  watch: {},
  mounted() {
    this.$refs.label.style.textAlign = this.labelAlign;
  },
};
</script>
<style lang="less" scoped>
.problem-detail-row {
  .label {
    min-width: 70px;
  }
  .img-item {
    img {
      width: 100px;
      height: 100px;
      margin-right: 10px;
    }
  }
  .font-color {
    color: var(--color-content);
  }
}
</style>
