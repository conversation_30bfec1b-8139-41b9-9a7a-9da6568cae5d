// 视频预案
import request from '@/libs/request'
import { icbdService } from './Microservice'

// 根据条件查询预案
export function getPlan(data) {
  return request({
      url: icbdService + '/inspectionTour/getPlanList',
      method: 'post',
      data
  })
}
// 删除预案信息
export function deletePlan(id) {
  return request({
      url: icbdService + `/inspectionTour/deleteInspectionTour/${id}`,
      method: 'delete'
  })
}
// 同一个用户下统计预案名称是否存在
export function getPlanName(data) {
  return request({
      url: icbdService + '/inspectionTour/checkPlanName',
      method: 'post',
      data
  })
}
// 新增监巡预案
export function addPlan(data) {
  return request({
      url: icbdService + '/inspectionTour/addInspectionTour',
      method: 'post',
      data
  })
}
// 修改监巡预案自启动状态
export function enableAutoStart(data) {
  return request({
      url: icbdService + '/inspectionTour/enableAutoStart',
      method: 'post',
      data
  })
}

// 获取当前用户的分屏信息列表
export function getScreenList() {
  return request({
      url: icbdService + '/splitScreen/getDataList',
      method: 'get'
  })
}