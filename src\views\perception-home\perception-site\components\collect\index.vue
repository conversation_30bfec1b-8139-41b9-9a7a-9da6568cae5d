<template>
  <div>
    <selectTag class="menu" :list="collectTypeList" @selectItem="selectItem" />
    <div class="middle-swiper-container">
      <swiper class="middle-swiper" ref="mySwiper" :options="swiperOption">
        <swiper-slide
          :class="customSwiperClass"
          v-for="(item, index) in cradList"
          :key="index"
        >
          <!-- <div class="card-item" :class="item.type === 'people' ? 'people-card' : 'video-card'"> -->
          <!-- <UiListCard :type="item.type" :data="item" :isChange="item.realNameArchiveNo ? true : false" @archivesDetailHandle="archivesDetailHandle(item)" /> -->
          <!-- </div> -->
          <!-- <two-swiper :cradObj="item" /> -->
          <div
            class="card-itemRest"
            :class="{
              'card-item': [
                '1',
                '2',
                '3',
                '4',
                '14',
                '15',
                '18',
                '19',
                '20',
              ].includes(favoriteObjectType),
            }"
          >
            <component
              :is="sectionName"
              @archivesDetailHandle="archivesDetailHandle(item)"
              :type="item.type"
              :deviceType="deviceType"
              :childSource="childSource"
              :collType="favoriteObjectType"
              @collection="collection"
              @faceDetailFn="faceDetailFn(item, index)"
              @vehicDetailFn="vehicDetailFn(item, index)"
              @grabDetailFn="grabDetailFn($event, index)"
              @personnelAlarm="personnelAlarm($event, index)"
              @vehicleAlarm="vehicleAlarm(item, index)"
              :data="item"
              :index="index"
            >
            </component>
          </div>
        </swiper-slide>
      </swiper>
      <div
        v-show="cradList.length > 0"
        class="swiper-button-prev peer-prev"
        slot="button-prev"
      >
        <i class="iconfont icon-caret-right"></i>
      </div>
      <div
        v-show="cradList.length > 0"
        class="swiper-button-next peer-next"
        slot="button-next"
      >
        <i class="iconfont icon-caret-right"></i>
      </div>
    </div>
    <ui-empty v-if="cradList.length === 0 && loading == false"></ui-empty>
    <ui-loading v-if="loading"></ui-loading>
    <!-- 人像抓拍 -->
    <details-face-modal
      v-if="videoShow"
      ref="videoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="videoShow = false"
    ></details-face-modal>
    <!-- 车辆抓拍 -->
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehiclePhotoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="vehicleShow = false"
    ></details-vehicle-modal>
    <details-modal
      v-if="humanShow"
      ref="humanbody"
      @collection="detailsCollection"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="humanShow = false"
    ></details-modal>
    <!-- 人员布控 -->
    <peopleAlarmDetail
      v-if="detailShow"
      :tableList="cradList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      @collection="detailsCollection"
      ref="alarmDetail"
      @close="detailShow = false"
    />
    <!-- 车辆布控 -->
    <vehicleAlarmDetail
      v-if="vehicleAlarmShow"
      :tableList="cradList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      @collection="detailsCollection"
      ref="vehicleDetail"
      @close="vehicleAlarmShow = false"
    />
  </div>
</template>

<script>
import { queryMyFavoritePageList } from "@/api/home";
import twoSwiper from "../two-swiper.vue";
import selectTag from "../select-tag.vue";
import deviceContent from "./deviceContent.vue";
import facePhotograph from "./facePhotograph.vue";
import vehicPhotograph from "./vehicPhotograph.vue";
import deviceShoot from "./deviceShoot.vue";
import personnelAlarm from "./personnelAlarm.vue";
import vehicleAlarm from "./vehicleAlarm.vue";
import humanBody from "./humanBody.vue";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import faceDetail from "@/components/detail/face";
import vehicleDetail from "@/components/detail/vehicle";
import { mapActions, mapGetters } from "vuex";
import { addCollection, deleteMyFavorite } from "@/api/user";
import UiListCard from "@/components/ui-list-card";
import { queryParamDataByKeys, pageList } from "@/api/config";
import detailsModal from "@/components/detail/details-modal.vue";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import peopleAlarmDetail from "@/views/target-control/alarm-manager/people/components/alarm-detail.vue";
import vehicleAlarmDetail from "@/views/target-control/alarm-manager/vehicle/components/vehicle-detail.vue";
export default {
  name: "",
  components: {
    twoSwiper,
    selectTag,
    facePhotograph,
    swiper,
    swiperSlide,
    deviceContent,
    vehicPhotograph,
    deviceShoot,
    faceDetail,
    vehicleDetail,
    UiListCard,
    personnelAlarm,
    vehicleAlarm,
    humanBody,
    detailsModal,
    detailsFaceModal,
    detailsVehicleModal,
    peopleAlarmDetail,
    vehicleAlarmDetail,
  },
  data() {
    return {
      videoShow: false,
      vehicleShow: false,
      humanShow: false,
      loading: false,
      detailShow: false,
      vehicleAlarmShow: false,
      alarmInfo: {},
      tableIndex: 0,
      cradList: [],
      collectTypeList: [],
      favoriteObjectType: "1",
      swiperOption: {
        slidesPerView: "auto",
        // slidesPerColumn: 2,
        // spaceBetween: 20,
        // pagination: {
        //     el: '.swiper-pagination',
        //     clickable: true
        // },
        observer: true, //修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, //修改swiper的父元素时，自动初始化swiper
        navigation: {
          nextEl: ".peer-next",
          prevEl: ".peer-prev",
        },
      },
      sectionName: "UiListCard",
      deviceType: "2",
      swiperWidth: "50%",
      customSwiperClass: "custom-swiper-slide-rec",
      childSource: 2,
      alarmConfigInfo: {},
    };
  },
  watch: {},
  computed: {
    swiper() {
      return this.$refs.mySwiper.swiper;
    },
    ...mapGetters({
      workbenchObj: "systemParam/workbenchObj",
    }),
  },
  async mounted() {
    await this.getSystemAllData();
    await this.init();
    this.getDictData();
    let params = {
      dataKey: "",
      dataValue: "",
      params: {
        pageNumber: 1,
        pageSize: 50,
      },
      typeKey: "ipbd_my_favorite_type",
    };
    const res = await pageList(params);
    res.data.entities.map((item) => {
      if (this.workbenchObj.indexOf(item.dataKey) > -1) {
        this.$set(
          this.collectTypeList,
          this.workbenchObj.indexOf(item.dataKey),
          { name: item.dataValue, value: item.dataKey }
        );
      }
    });
    // this.favoriteObjectType = this.collectTypeList[0].value;
    this.selectItem(this.collectTypeList[0]);
    this.$emit("selectItem", this.collectTypeList[0]);
    // this.collectQuery();
  },
  methods: {
    init() {
      console.log("报警页面2");
      var param = ["ICBD_TARGET_CONTROL"];
      queryParamDataByKeys(param).then((res) => {
        if (res.data.length > 0) {
          this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
        }
      });
    },
    ...mapActions({
      getSystemAllData: "systemParam/getSystemAllData",
      getDictData: "dictionary/getDictAllData",
    }),
    selectItem(item, index) {
      this.favoriteObjectType = item.value;
      if (["1", "2", "3", "4", "18", "19", "20"].includes(item.value)) {
        this.sectionName = "UiListCard";
        this.customSwiperClass = "custom-swiper-slide-rec";
      } else if (["5", "13", "24"].includes(item.value)) {
        this.sectionName = "facePhotograph";
        this.childSource = item.value == 5 || item.value == 24 ? 2 : 1;
        this.customSwiperClass = "custom-swiper-slide-face";
      } else if (["6", "25"].includes(item.value)) {
        this.sectionName = "vehicPhotograph";
        this.customSwiperClass = "custom-swiper-slide-face";
      } else if (["7", "8", "9"].includes(item.value)) {
        this.sectionName = "deviceContent";
        this.deviceType = item.value == 7 ? 2 : item.value == 8 ? 3 : 4;
        this.customSwiperClass = "custom-swiper-slide-deviceContent";
      } else if (["10", "11", "12"].includes(item.value)) {
        this.sectionName = "deviceShoot";
        this.deviceType = item.value == 10 ? 1 : item.value == 11 ? 2 : 3;
        this.customSwiperClass = "custom-swiper-slide-device";
      } else if (item.value == "14") {
        this.sectionName = "personnelAlarm";
        this.customSwiperClass = "custom-swiper-slide-rec";
      } else if (item.value == "15") {
        this.sectionName = "vehicleAlarm";
        this.customSwiperClass = "custom-swiper-slide-rec";
      } else if (["16", "17", "26", "27"].includes(item.value)) {
        this.sectionName = "humanBody";
        this.customSwiperClass = "custom-swiper-slide-face";
      }
      this.collectQuery();
      this.$emit("selectItem", item);
    },
    // 收藏
    collectQuery() {
      this.cradList = [];
      this.loading = true;
      let paramsf = {
        favoriteObjectId: "",
        // "favoriteObjectIdList": [],
        favoriteObjectType: this.favoriteObjectType,
        // "id": '',
        // "userId": "",
        pageNumber: 1,
        pageSize: 5,
      };
      queryMyFavoritePageList(paramsf)
        .then((res) => {
          if (res.data.entities) {
            res.data.entities.map((item) => {
              let type = "",
                openName = "";
              switch (this.favoriteObjectType) {
                case "1":
                  type = "people";
                  openName = "people-archive";
                  break;
                case "2":
                  type = "video";
                  openName = "video-archive";
                  break;
                case "3":
                  type = "vehicle";
                  openName = "vehicle-archive";
                  break;
                case "4":
                  type = "device";
                  openName = "device-archive";
                  break;
                case "19":
                  type = "zdr";
                  openName = "people-archive";
                  break;
                case "20":
                  type = "place";
                  openName = "place-archive";
                  break;
                case "18":
                  type = "non-motor-vehicle";
                  openName = "non-motor-archive";
                  break;
              }
              item.type = type;
              item.openName = openName;
            });
            this.cradList = res.data.entities;
            if (["14", "15"].includes(this.favoriteObjectType)) {
              this.cradList.forEach((item) => {
                var info = this.alarmConfigInfo.alarmLevelConfig.find(
                  (ite) => ite.alarmLevel == item.taskLevel
                );
                item.bgIndex = Number(info.alarmColour);
              });
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 收藏/取消收藏
    collection(params, flag) {
      if (
        !["1", "2", "3", "4", "18", "19", "20"].includes(
          this.favoriteObjectType
        )
      ) {
        if (flag == 1) {
          addCollection(params).then((res) => {
            this.$Message.success("收藏成功");
            this.collectQuery();
          });
        } else {
          deleteMyFavorite(params).then((res) => {
            this.$Message.success("取消收藏成功");
            this.collectQuery();
          });
        }
      } else {
        this.collectQuery();
      }
    },
    // 人体抓拍，非机动车抓拍 取消收藏
    detailsCollection() {
      this.humanShow = false;
      this.detailShow = false;
      this.vehicleAlarmShow = false;
      this.collectQuery();
    },
    faceDetailFn(row, index) {
      this.videoShow = true;
      this.$nextTick(() => {
        this.$refs.videoDetail.init(row, this.cradList, index, 5, 1);
      });
      // this.faceShow = true
      // this.$nextTick(() => {
      //     this.$refs.faceDetail.init(row)
      // })
    },
    vehicDetailFn(row, index) {
      this.vehicleShow = true;
      this.$nextTick(() => {
        this.$refs.vehiclePhotoDetail.init(row, this.cradList, index, 1);
      });
      // this.vehicleShow = true
      // this.$refs.vehicleDetail.init(row)
    },
    prePage() {},
    nextPage() {},
    grabDetailFn(row, index) {
      this.humanShow = true;
      let type = this.favoriteObjectType == "16" || "26" ? 1 : 2;
      this.$nextTick(() => {
        this.$refs.humanbody.init(row, this.cradList, index, type);
      });
    },
    // 管控报警详情
    personnelAlarm(row, index) {
      this.alarmInfo = row;
      this.tableIndex = index;
      this.detailShow = true;
    },
    vehicleAlarm(row, index) {
      this.alarmInfo = row;
      this.tableIndex = index;
      this.vehicleAlarmShow = true;
    },
    // 跳转详情
    archivesDetailHandle(item) {
      let params = {};
      if (item.type === "device") {
        params = {
          archiveNo: item.deviceId,
        };
      } else if (item.type === "vehicle") {
        params = {
          archiveNo: JSON.stringify(item.archiveNo),
          source: "car",
          plateNo: JSON.stringify(item.plateNo),
          idcardNo: item.idcardNo,
        };
      } else if (item.type === "non-motor-vehicle") {
        params = {
          archiveNo: JSON.stringify(item.archiveNo),
          plateNo: JSON.stringify(item.plateNo),
          source: "non-motor-vehicle",
          idcardNo: item.idcardNo,
        };
      } else if (item.type === "place") {
        params = {
          archiveNo: item.id,
          source: "place",
        };
      } else {
        params = {
          archiveNo: item.archiveNo,
          source: item.type,
          initialArchiveNo: item.archiveNo,
        };
      }
      const { href } = this.$router.resolve({
        name: item.openName,
        query: params,
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
@import "style/swiper";
.middle-swiper-container {
  margin-top: 4px;
}
.menu {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
}
.card-itemRest {
  width: 100%;
}
.custom-swiper-slide {
  width: 33.3% !important;
}
.custom-swiper-slide-rec {
  width: 50% !important;
}
.custom-swiper-slide-face {
  width: 25% !important;
}
.custom-swiper-slide-vehic {
  width: 25% !important;
}
.custom-swiper-slide-deviceContent {
  width: 33.3% !important;
}
.custom-swiper-slide-device {
  width: 33.3% !important;
}
.faceDetail {
  position: fixed !important;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 666;
}
.vehicleDetail {
  position: fixed !important;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 66;
}
.card-item {
  width: 345px;
  margin-left: 4px;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  .list-card {
    width: 100%;
    backface-visibility: hidden;
    height: 198px;
    margin-bottom: 0;
    /deep/.list-card-content-body {
      height: 115px;
    }
    /deep/.content-img {
      width: 115px;
      height: 115px;
    }
    /deep/.tag-wrap {
      margin-top: 7px;
      .ui-tag {
        margin: 0 5px 0 0 !important;
      }
    }
  }
}
.swiper-button-disabled {
  cursor: not-allowed;
  pointer-events: auto;
}
</style>
