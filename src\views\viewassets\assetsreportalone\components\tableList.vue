<template>
  <div class="table-list auto-fill">
    <slot name="search"></slot>
    <div class="auto-fill">
      <ui-table
        @selectTable="selectAction"
        @onSelectAllTable="onSelectAllTable"
        @cacelAllSelectTable="cacelAllSelectTable"
        class="ui-table auto-fill"
        :loading="loading"
        :table-columns="columns"
        :table-data="tableData"
        v-if="columns"
        ref="table"
      >
        <template v-for="(item, index) in slotList" #[item.slot]="{ row }">
          <slot :name="item.slot" :row="row" :index="index"></slot>
        </template>
      </ui-table>
    </div>
    <ui-page
      v-if="paging"
      class="page"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    ></ui-page>
  </div>
</template>
<script>
export default {
  name: 'basedField',
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableData: [],
      selectRows: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        const res = await this.loadData(this.searchData);
        this.tableData = res.data[this.listKey];
        this.pageData.totalCount = res.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    // 选中项发生改变时触发
    selectAction(rows) {
      this.selectRows = rows || [];
      this.$emit('selectAction', this.selectRows);
    },
    // 全选
    onSelectAllTable() {
      this.$emit('onSelectAllTable');
    },
    // 取消选中
    cacelAllSelectTable() {
      this.$emit('cacelAllSelectTable');
    },
  },
  watch: {},
  computed: {
    slotList() {
      let array = [];
      this.columns.map((val) => {
        if (val.slot) {
          array.push(val);
        }
      });
      return array;
    },
  },
  props: {
    columns: {
      // 表头
      type: Array,
      default() {
        return [];
      },
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
    paging: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.table-list {
  padding: 0 20px;
  .page {
    padding-right: 0;
  }
}
</style>
