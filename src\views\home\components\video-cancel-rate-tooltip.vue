<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box">
      <p>{{ toolTipData[0].axisValue }}</p>
    </div>
    <div class="tooltip-content-box">
      <p class="legend-box" v-if="toolTipData[1]">
        <span> 视频监控设备撤销率： </span>
        <span :style="{ color: toolTipData[1].color }" class="font-num">
          {{ toolTipData[0].data?.resultValueFormat }}
        </span>
      </p>
    </div>
    <div class="tooltip-content-box">
      <p class="legend-box">
        <span> 月末设备总量： </span>
        <span :style="{ color: toolTipData[0]['data'].color }" class="font-num">
          {{ toolTipData[0].data?.detail?.allRevocationCount }}
        </span>
      </p>
    </div>
    <div class="tooltip-content-box">
      <p class="legend-box">
        <span> 当月撤销数量： </span>
        <span :style="{ color: toolTipData[0]['data'].color }" class="font-num">
          {{ toolTipData[0].value }}
        </span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #ffffff;
    .font-vs {
      font-weight: 600;
      color: #1b86ff;
      margin: 0 15px;
    }
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .block {
    display: inline-block;
    width: 6px;
    height: 6px;
    margin-right: 19px;
  }
  .line {
    display: inline-block;
    height: 2px;
    width: 15px;
    margin-right: 10px;
  }
  .font-num {
    font-weight: 600;
  }
}
</style>
