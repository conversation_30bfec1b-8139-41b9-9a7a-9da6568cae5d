<template>
  <div class="margin-wrapper-flow height-full">
    <div class="equipmentimport auto-fill">
      <slide-unit-tree @selectOrgCode="selectOrgCode" :select-key="selectKey"></slide-unit-tree>
      <div class="search-module">
        <ui-label class="inline rigth-margin bottom-margin" label="关键词" :width="50">
          <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入设备名称/设备编码"></Input>
        </ui-label>
        <ui-label class="inline rigth-margin bottom-margin" label="数据来源" :width="65">
          <Select class="width-md" placeholder="请选择数据来源" clearable v-model="searchData.type">
            <Option v-for="(item, index) in dataSourceList" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline rigth-margin bottom-margin" label="数据状态" :width="65">
          <Select class="width-md" placeholder="请选择数据状态" clearable v-model="searchData.generateStatus">
            <Option v-for="(item, index) in dataStatuList" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline rigth-margin bottom-margin" label="检测时间" :width="65">
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.detectDateStart"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'detectDateStart')"
            placeholder="请选择开始时间"
          ></DatePicker>
          <span> — </span>
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.detectDateEnd"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'detectDateEnd')"
            placeholder="请选择结束时间"
          ></DatePicker>
        </ui-label>
        <div>
          <ui-label class="inline rigth-margin bottom-margin" label="相同设备数量" :width="95">
            <Input v-model="searchData.sameDeviceCount" class="width-num" placeholder="请输入"></Input>
            <span class="base-text-color ml-sm">及以上</span>
          </ui-label>
          <div class="inline">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="resetSearchDataMx(searchData, search)">重置</Button>
          </div>
          <div class="fr">
            <Button type="primary" class="mr-sm" @click="download">
              <i class="icon-font icon-daochu"></i>
              <span class="inline vt-middle ml-sm">导出</span>
            </Button>
            <Upload
              action="/ivdg-work-order-service/device/task/importDevice"
              ref="upload"
              class="inline"
              :show-upload-list="false"
              :headers="headers"
              :before-upload="beforeUpload"
              :on-success="importSuccess"
              :on-error="importError"
            >
              <Button type="primary" class="mr-sm">
                <i class="icon-font icon-daoruwentishebei f-12"></i>
                <span class="vt-middle ml-sm">导入问题设备</span>
              </Button>
            </Upload>

            <Button type="primary" @click="createQuestion" :loading="createLoading">
              <i class="icon-font icon-shengchengwentiqingdan f-12"></i>
              <span class="vt-middle ml-sm">生成问题清单</span>
            </Button>
          </div>
        </div>
      </div>
      <!-- <div class="over-flow total-box f-14">
      
    </div> -->
      <div class="table-module auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #deviceId="{ row }">
            <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
              row.deviceId
            }}</span>
          </template>
          <template #action="{ row }">
            <div :class="row.rowClass">
              <Button type="text" @click="deleteRow(row)">删除</Button>
            </div>
          </template>
        </ui-table>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <loading v-if="importLoading"> 导入中，请勿刷新页面，请耐心等待 </loading>
    </div>
  </div>
</template>
<script>
import governancetask from '@/config/api/governancetask';
import user from '@/config/api/user';
import { mapGetters } from 'vuex';
export default {
  name: 'equipmentimport',
  props: {},
  data() {
    return {
      loading: false,
      selectKey: '0',
      dataSourceList: [],
      searchData: {
        orgCode: '',
        keyWord: '',
        type: '',
        detectDateStart: '',
        detectDateEnd: '',
        sameDeviceCount: '',
        generateStatus: '0',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      choosedOrg: {},
      tableData: [],
      tableColumns: [
        { title: '序号', width: 70, type: 'index' },
        {
          width: 230,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
        },
        {
          width: 230,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
        },
        {
          width: 150,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
        },
        {
          width: 150,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
        },
        { minWidth: 150, title: '设备类型', key: 'sbgnlxText', align: 'left' },
        { minWidth: 100, title: '数据来源', key: 'typeText', align: 'left' },
        { minWidth: 120, title: '异常原因', key: 'reason', align: 'left' },
        { minWidth: 150, title: '检测时间', key: 'detection', align: 'left' },
        {
          width: 100,
          title: '操作',
          slot: 'action',
          align: 'left',
          fixed: 'right',
        },
      ],
      deleteData: [],
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      createLoading: false,
      dataStatuList: [],
    };
  },
  created() {},
  async activated() {
    this.dataSourceList = await this.initSource('data_source');
    this.dataStatuList = await this.initSource('DEVICE_DETECTION_GENERATE_STATUS');
    this.selectKey = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.init();
  },
  methods: {
    async initSource(typekey) {
      try {
        let res = await this.$http.get(user.queryByTypeKey, {
          params: { typekey: typekey },
        });
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    selectOrgCode(data) {
      this.searchData.orgCode = data.orgCode;
      this.choosedOrg = data;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(governancetask.listPageOutlineDevice, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    deleteRow(row) {
      this.$UiConfirm({
        content: `您将要删除${row.deviceName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteData.push(row.id);
          this.deleteInit();
        })
        .catch((res) => {
          console.log(res);
        });
    },
    deleteInit() {
      this.$http
        .post(governancetask.logicDelete, { ids: this.deleteData })
        .then(() => {
          this.deleteData.length = 0;
          this.$Message.success('删除成功');
          this.search();
        })
        .catch((err) => {
          console.log(err);
        });
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess() {
      this.importLoading = false;
      this.search();
    },
    importError() {
      this.importLoading = false;
      this.$Message.error('导入文件失败!');
    },
    async download() {
      try {
        let res = await this.$http.get(governancetask.getDeviceExcelTemplate, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
    async createQuestion() {
      try {
        this.createLoading = true;
        await this.$http.post(governancetask.createDeviceOrder, this.searchData);
        this.$Message.success('生成问题清单成功');
      } catch (err) {
        console.log(err);
      } finally {
        this.createLoading = false;
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    SlideUnitTree: require('@/components/slide-unit-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.rigth-margin {
  margin-right: 20px;
}
.bottom-margin {
  margin-bottom: 15px;
}
.equipmentimport {
  overflow: hidden;
  position: relative;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .search-module {
    // padding: 20px 20px 5px;
    .width-num {
      width: 80px;
    }
  }
  .total-box {
    // padding: 10px 20px;
    p {
      line-height: 32px;
    }
  }
  .table-module {
    // padding: 0 20px;
  }
  @media screen and (max-width: 1366px) {
    .ml-lg {
      margin-left: 25px;
    }
  }
}
</style>
