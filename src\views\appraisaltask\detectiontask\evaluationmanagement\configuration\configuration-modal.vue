<template>
  <div>
    <ui-modal class="configuration-modal" :title="configurationAction.title" v-model="visible" :styles="styles">
      <div class="indicator-hearder">
        <div class="ml-md fl label-content label-text">
          <div class="fl">
            <i class="icon-font f-16 icon-renwushuliang-01 inline"></i>
            <span class="base-text-color">
              任务名称:
              <span class="label-text">
                {{ taskName }}
              </span>
            </span>
          </div>
          <div class="ml_lg fl">
            <i class="icon-font f-16 icon-pingcefangan-01 inline"></i>
            <span class="base-text-color">
              评测方案:
              <span class="label-text">
                {{ schemeName }}
              </span>
            </span>
          </div>
        </div>
        <div class="mr-md fr">
          <p class="fl base-text-color f-14">
            共 <span class="color-failed f-14">{{ totalCount }}</span>
            个指标（已配置
            <span class="color-failed f-14">{{ alreadyConfigCount }}</span>
            &nbsp;条）
          </p>
        </div>
      </div>
      <div class="indicator_frame">
        <div class="fl left_con">
          <ui-card
            ref="uiCard"
            v-model="activeIndex"
            :data="indexTypeList"
            @on-change="handleClickCard"
            :countShow="true"
          ></ui-card>
        </div>
        <div class="fr right_con">
          <div class="search-box d_flex mb-md">
            <ui-label label="关键词:">
              <Input
                v-model="searchData.searchValue"
                class="width-md"
                placeholder="请输入关键词"
                clearable
                @on-enter="handleSearch"
              >
              </Input>
            </ui-label>
            <Button type="primary" @click="handleSearch" class="ml-lg">查询</Button>
            <Button @click="handleReset" class="ml-sm">重置</Button>
          </div>
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableList" :loading="loading">
            <template #indexName="{ row }">
              <div class="text-icon-box">
                <span>{{ row.indexName }}</span>
                <i
                  class="icon-font icon-wenhao vt-middle icon-warning ml-sm icon-details"
                  @click="openDetailsDialog(row)"
                ></i>
              </div>
            </template>
            <template #indexModule="{ row }">
              {{ getIndexModuleName(row) }}
            </template>
            <template #option="{ row, index }">
              <Button type="text" @click="getConfiguration('configuration', row, index)">{{
                row.config ? '已配置' : '配置'
              }}</Button>
            </template>
          </ui-table>
          <loading v-if="loading"></loading>
        </div>
      </div>
      <template slot="footer">
        <Button type="primary" @click="visible = false" class="plr-30">关闭</Button>
      </template>
      <!--  配置 -->
      <BasicInformation ref="BasicInformation" :module-action="moduleAction" :modalData="modalData" @updata="upb" />
    </ui-modal>
    <!-- 指标配置详情 -->
    <common-model ref="model" v-model="indexVisible" formModel="view" :formData="currentRow"> </common-model>
  </div>
</template>
<style scoped lang="less">
.configuration-modal {
  @{_deep}.ivu-modal-body {
    padding: 0;

    margin-top: 20px;
  }
}

.text_hid {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.indicator-hearder {
  height: 50px;
  line-height: 50px;
  .label-text {
    color: var(--color-primary);
    font-size: 14px;
  }
  .label-content {
    height: 50px;
    line-height: 50px;
    span {
      vertical-align: middle;
    }
  }
  .ml_lg {
    margin-left: 50px;
  }
}
.indicator_frame {
  height: 650px;
  display: inline-block;
  border-top: 1px solid var(--border-modal-footer);
  width: 100%;

  .left_con {
    display: inline-block;
    width: 300px;
    height: 100%;
    border-right: 1px solid var(--border-modal-footer);

    padding: 20px 20px 0;
  }
  .right_con {
    width: calc(~'100% - 300px');
    height: 100%;
    padding: 20px 20px 0;
    position: relative;
    display: flex;
    flex-direction: column;
    .page {
      width: 100%;
      height: 50px;
    }
    .reset-el-table {
      @{_deep}.el-table__header {
        .el-table-column--selection {
          .cell {
            padding-left: 0.072917rem;
            padding-right: 0.072917rem;
          }
        }
        .is-leaf {
          border-bottom: none !important;
        }
      }
    }
  }
  .tab-indicators {
    .ivu-select-input {
      height: 30px !important;
      line-height: 30px !important;
    }
    .ivu-input {
      height: 30px !important;
    }
  }
}

.ml-65 {
  margin-left: 65px;
}
.text-icon-box {
  display: flex;
  align-items: center;
  flex-direction: row;
  .icon-details {
    width: 18px;
    display: none;
  }
}
/deep/ .ivu-table-row:hover {
  .icon-details {
    display: block;
  }
}
</style>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
import configModalData from './config-modal-data';
import { checkConfigmodule, detectionRules } from './static-field';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    configurationAction: {
      type: Object,
      default: () => {},
    }, // 行数据
    modalId: {},
    index_data: {},
    itemList: {},
  },
  data() {
    return {
      currentRow: {},
      indexVisible: false,
      ...configModalData.data(),
      searchData: {
        searchValue: '',
      },
    };
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    getIndexModuleName(row) {
      let indexModule = this.global.indexTypeList.find((item) => item.id == row.indexModule);
      return indexModule && indexModule.title;
    },
    getConfiguration(action, row) {
      this.modalData = JSON.parse(JSON.stringify(row));
      const { regionCode, taskSchemeId, schemeType, statisticalModelBo } = this.configurationAction; // 检测对象code
      const metaType = {
        configuration: () => {
          this.moduleAction = {
            title: row.indexName + '配置',
            regionCode,
            taskSchemeId,
            schemeType, // schemeType 方案类型
            statisticalModelBo: statisticalModelBo || this.defaultStatisticalModelBo,
            ...row,
          };
          if (checkConfigmodule.includes(this.moduleAction.indexType)) {
            this.moduleAction.checkConfig = detectionRules[this.moduleAction.indexType]
              ? detectionRules[this.moduleAction.indexType]
              : detectionRules.routine;
          }
          this.$refs.BasicInformation.init({ ...this.moduleAction });
        },
      };
      metaType[action]();
    },
    //切换指标
    handleClickCard(row) {
      //切换指标清空搜索条件
      this.clearSearch();
      this.storeTableList = this.tableData.filter((item) => {
        if (row.id === 'all') {
          return true;
        } else {
          return item.indexModule == row.id;
        }
      });

      this.tableList = this.$util.common.deepCopy(this.storeTableList);
    },
    async getindexShow() {
      this.tableData = [];
      try {
        this.loading = true;
        let params = {
          taskSchemeId: this.configurationAction.taskSchemeId,
        };
        let res = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        this.alreadyConfigCount = res.data.data.alreadyConfigCount;
        this.tableData = res.data.data.indexConfigData || [];
        this.totalCount = this.tableData.length;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },

    async upb() {
      await this.getindexShow();
      await this.handleClickCard(this.indexTypeList.find((item) => item.id == this.activeIndex) || {});
    },
    handConfig(val) {
      const flag = {
        true: '已配置',
        false: '配置',
      };
      return flag[val];
    },
    // 指标详情
    openDetailsDialog(row) {
      let { indexType, indexName, indexId } = row;
      this.currentRow = {
        indexType: indexType,
        indexName: indexName,
        id: indexId,
      };
      this.indexVisible = true;
    },
    clearSearch() {
      this.searchData.searchValue = '';
    },
    //检索任务时展示
    async getShowTableList() {
      try {
        let params = {
          taskSchemeId: this.configurationAction.taskSchemeId,
          searchValue: this.searchData.searchValue,
          indexModule: this.activeIndex === 'all' ? '' : this.activeIndex, //indexModule和activeIndex一致
        };
        let res = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        this.tableList = res.data.data.indexConfigData || [];
      } catch (err) {
        console.log(err);
      }
    },
    //检索指标
    handleSearch() {
      if (this.searchData.searchValue === '') {
        this.tableList = this.storeTableList;
        return;
      }
      this.getShowTableList();
    },
    handleReset() {
      this.searchData.searchValue = '';
      this.handleSearch();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      this.visible = val;
      this.rowData = this.configurationAction.data;
      this.taskName = this.configurationAction.taskName;
      this.schemeName = this.configurationAction.schemeName;
      if (val) {
        this.clearSearch();
        await this.getindexShow();
        await this.handleClickCard(
          this.indexTypeList.find((item) => item.id == this.activeIndex) || this.indexTypeList[0],
        );
      }
    },
  },
  created() {},
  computed: {
    ...mapGetters({
      algorithmVendorType: 'algorithm/algorithmVendorType',
    }),
    indexTypeList() {
      this.global.indexTypeList.map((it) => {
        let list = this.tableData.filter((item) => {
          return item.indexModule == it.id;
        });
        it.count = list.length;
      });
      let all = [
        {
          id: 'all',
          title: '全部指标',
          icon: 'icon-quanbu',
          count: this.tableData.length,
        },
      ];
      return [...all, ...this.global.indexTypeList];
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    uiCard: require('@/views/appraisaltask/components/ui-card.vue').default,
    BasicInformation:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/basics/basic-information.vue')
        .default,
    CommonModel: require('@/views/appraisaltask/indexmanagement/common-model.vue').default,
    uiLabel: require('@//components/ui-label.vue').default,
  },
};
</script>
