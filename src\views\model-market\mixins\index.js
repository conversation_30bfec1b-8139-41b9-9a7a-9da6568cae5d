/*
    * @FileDescription: 针对收缩、展开
    * @Author: H
    * @Date: 2023/02/03
    * @LastEditors: 
    * @LastEditTime: 
*/
import { getMapLayerByType, queryPlaceInfoPageList } from '@/api/operationsOnTheMap';
import { mapGetters } from 'vuex';
import { getSimpleDeviceList } from '@/api/operationsOnTheMap';
export const myMixins = {
    data() {
        return {
            allCameraList: [],
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            packUpDown: false,
            siteListFlat: []
        }
    },
    computed: {
        ...mapGetters({
            individuation: "individuation"
        }),
    },
    mounted() {

    },
    async created() {
        await this.getMapLayerByTypeSite();   
    },
    methods: {
        // 进入页面查询所有点位信息
        async getMapLayerByType () {
            this.allCameraList = [];
            const params = {
                typeKeys: {
                map_device: 'all',
                map_place: 'all',
                map_police: 'all'
                }
            }
            let  res = await queryPlaceInfoPageList({});
            let { data } = await getMapLayerByType(params);
            res.data.entities.map((item, index) => {
                this.$set(this.allCameraList, index, item)
            })
            let len = this.allCameraList.length - 1;
            data.map((item, index) => {
                this.$set(this.allCameraList, len + index, item)
            })
        },
        // 撒点数据
        async getMapLayerByTypeSite() {
            let deviceTypes = [1, 2, 3, 4, 5, 11]
            this.allCameraList = [];
            let roleParam = {
                roleId: this.userInfo.roleVoList[0].id, 
                filter: this.userInfo.roleVoList[0].initFlag == '1' ? false : true, 
                socialResources: this.individuation.hideCommunity ? 0 : 1, 
                excludeDeviceTypes: this.individuation.hideDeviceTypes ? this.individuation.hideDeviceTypes : []
            };
            let { data } = await getSimpleDeviceList(roleParam)
            data = data.filter(v => deviceTypes.includes(v[2]))
            this.allCameraList = [...data]
        },
        // 加密身份证
        hiddenId(str, frontLen, endLen) {
            var len = str.length-frontLen-endLen;
            var xing = '';
            for (var i=0;i<len;i++) {
                xing+='*';
            };
            return str.substring(0,frontLen)+xing+str.substring(str.length-endLen);
        },
        // 伸缩
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        },
    }
}