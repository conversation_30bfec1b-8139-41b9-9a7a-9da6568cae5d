/*
 * @Date: 2025-02-12 14:39:11
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-03-31 18:15:53
 * @FilePath: \icbd-view\src\router\routers\juvenile-target-control.js
 */
/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from "@/layouts/basic-layout";
import ArchivesLayout from "@/layouts/archives-layout";

export default [
  {
    path: "/importantPerson-target-control2",
    name: "importantPerson-target-control2",
    component: BasicLayout,
    children: [
      {
        path: "/importantPerson-control-task/criminal-record/add",
        name: "importantPerson-control-task-add",
        tabShow: true,
        parentName: "importantPerson-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/importantPerson-target-control/control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "新增布控",
        },
      },
      {
        path: "/importantPerson-control-task/criminal-record/edit",
        name: "importantPerson-control-task-edit",
        tabShow: true,
        parentName: "importantPerson-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/importantPerson-target-control/control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "编辑布控",
        },
      },
      {
        path: "/importantPerson-control-task/criminal-record/detail",
        name: "importantPerson-control-task-detail",
        tabShow: true,
        parentName: "importantPerson-target-control/control-task",
        component: (resolve) =>
          require([
            "@/views/importantPerson-target-control/control-task/criminal-record/detail.vue",
          ], resolve),
        meta: {
          title: "布控详情",
        },
      },
    ],
  },
  {
    path: "/importantPerson-data-storage2",
    name: "importantPerson-data-storage2",
    component: BasicLayout,
    children: [
      {
        path: "/importantPerson-data-storage/special-library/personnel-thematic-database",
        name: "importantPerson-personnel-thematic-database",
        tabShow: true,
        parentName: "importantPerson-data-storage/special-library",
        component: (resolve) =>
          require([
            "@/views/juvenile-data-storage/special-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "专题库-人像库",
        },
      },
    ],
  },
  {
    path: "/importantPerson-archive",
    name: "importantPerson-archive",
    component: ArchivesLayout,
    redirect: "/importantPerson-archive/people-dashboard",
    meta: {
      title: "重点人档案",
      icon: "icon-shouye",
      show: true,
    },
    children: [
      {
        path: "/importantPerson-archive/people-dashboard",
        name: "importantPerson-people-dashboards",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/dashboard"
          ),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/importantPerson-archive/people-profile",
        name: "importantPerson-people-profile",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/profile"
          ),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/importantPerson-archive/people-activity-track",
        name: "importantPerson-people-activity-track",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/activity-track"
          ),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/importantPerson-archive/people-relationship-map",
        name: "importantPerson-people-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
];
