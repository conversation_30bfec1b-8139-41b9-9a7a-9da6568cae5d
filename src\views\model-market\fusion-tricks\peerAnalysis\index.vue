<!--
    * @FileDescription: 同行分析
    * @Author: H
    * @Date: 2023/01/11
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
  <div class="peer-analysis">
    <!-- 地图 -->
    <mapCustom
      ref="mapBase"
      :sectionName="mapName"
      mapType="peer"
      :coincideList="coincideList"
      :peerList="peerPointList"
      :lintList="lintPointList"
    />
    <!-- 左面信息展示框 -->
    <left-box
      ref="leftbox"
      :selectTabIndex="selectTabIndex"
      @reset="handleReset"
      @searchAnalyse="handleSearchAnalyse"
      @cutAnalyse="handleCutAnalyse"
    ></left-box>
    <!-- 右面 检索结果-->
    <rightBox
      ref="rightBox"
      v-show="rightShowList"
      :list="facePeerCount"
      @peerNumber="handlePeer"
      @cancel="handleRightCancel"
      @queryFaceCameraPoint="queryFaceCameraPoint"
    >
    </rightBox>
    <!-- 展示同行 -->
    <peerList
      ref="peerList"
      v-if="rightPeerList"
      @peer="handleCoinPeer"
      @back="handleBack"
      @coincide="coincide"
    ></peerList>
    <!-- 底部提示 -->
    <bottomhint></bottomhint>
    <!-- <faceModal></faceModal> -->
  </div>
</template>

<script>
import bottomhint from "./components/bottomHint.vue";
import leftBox from "./components/leftBox.vue";
import rightBox from "./components/rightBox.vue";
import peerList from "./components/peerList.vue";
import {
  queryVehicleCameraPoint,
  queryFaceCameraPoint,
} from "@/api/modelMarket";
import faceModal from "../../components/detail-dom/face-modal";
import mapCustom from "../../components/map/index.vue";
import { mapMutations } from "vuex";
export default {
  name: "peerAnalysis",
  components: {
    bottomhint,
    leftBox,
    rightBox,
    peerList,
    faceModal,
    mapCustom,
  },
  props: {
    // 默认选中的类型
    selectTabIndex: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      searchType: "同行分析",
      showList: false,
      rightShowList: false,
      searchPage: {
        pageNumber: 1,
        pageSize: 10,
      },
      peerPage: {
        pageNumber: 1,
        pageSize: 10,
      },
      facePeerCount: [],
      rightPeerList: false,
      dyHeight: 0,
      peerCondition: {},
      typaTag: {},
      leftObject: {},
      request: {
        0: queryFaceCameraPoint, // 人脸
        1: queryVehicleCameraPoint, // 车辆
      },
      coincideList: [],
      mapName: "",
      peerPointList: [],
      lintPointList: [],
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  activated() {
    this.setLayoutNoPadding(true);
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    handleReset() {
      this.resetPoint(0);
      this.rightShowList = false;
      this.rightPeerList = false;
    },
    // 同行分析
    handleSearchAnalyse(val, item, tabIndex) {
      this.typaTag = tabIndex;
      this.rightShowList = true;
      this.rightPeerList = false;
      this.resetPoint(0);
      this.peerCondition = val;
      this.$refs.rightBox.init({ ...val }, this.typaTag);
      this.leftObject = item;
      this.queryFaceCameraPoint(val);
    },
    queryFaceCameraPoint(params) {
      // 开始分析的撒点
      this.request[this.typaTag.tab](params).then((res) => {
        res.data.map((item) => {
          item.lat = item.geoPoint.lat;
          item.lon = item.geoPoint.lon;
          item.faceCaptureList =
            item.faceCaptureList.length > 100
              ? item.faceCaptureList.slice(0, 100)
              : item.faceCaptureList;
        });
        this.lintPointList = res.data;
        this.tabName(this.typaTag.tab);
      });
    },
    // 清除点位
    // 清除点位0 ：开始分析点位 1：同行次数点位
    resetPoint(index) {
      // this.$emit('resetMapPoint', index);
      this.$refs.mapBase.clearModalBox();
      // 修改地图放一起
      index == 0
        ? this.$refs.mapBase.resetMarker()
        : this.$refs.mapBase.resetMarkerRight();
    },
    handleCutAnalyse() {
      this.rightShowList = false;
      this.rightPeerList = false;
      this.resetPoint(0);
    },
    // 打开同行次数窗口
    handlePeer(val) {
      this.rightPeerList = true;
      this.rightShowList = false;
      let idlist = ["faceCaptureIdList", "vehicleCaptureIdList"];

      let recordIdList = [];
      //   if (val.peerDetails) {
      //     val.peerDetails.forEach((item) => {
      //       recordIdList.push(item.peer.captureId);
      //     });
      //   }
      if (val.peerCaptureIdList) {
        val.peerCaptureIdList.forEach((item) => {
          recordIdList.push(item);
        });
      }
      let params = {
        dateType: this.peerCondition.dateType,
        endDate: this.peerCondition.endDate,
        // "recordIdList": val[idlist[this.typaTag.tab]],
        recordIdList: recordIdList,
        startDate: this.peerCondition.startDate,
      };
      let peerParams = {};
      let pointParams = {};
      if (this.typaTag.tab == 0) {
        //人员
        if (this.typaTag.secTab == 0) {
          peerParams = {
            // 'archiveNo': val.vid,
            // 'vid': val.vid,
            archiveNo: this.peerCondition.archiveNo,
          };
          if (val.idCardNo) {
            pointParams = {
              archiveNo: val.idCardNo,
            };
          } else {
            pointParams = {
              vid: val.vid,
            };
          }
        } else {
          //人脸
          peerParams = {
            // 'vid': val.vid,
            vid: this.peerCondition.vid,
          };
          pointParams = {
            vid: val.vid,
          };
        }
      } else if (this.typaTag.tab == 1) {
        //车辆
        peerParams = {
          //   'plateNo': val.plateNo,
          plateNo: this.peerCondition.plateNo,
        };
        pointParams = {
          plateNo: val.plateNo,
        };
      }
      let point = {
        ...params,
        ...peerParams,
        peerMinNumber: this.peerCondition.peerMinNumber,
        peerSecond: this.peerCondition.peerSecond,
      };
      let tabParams = {
        ...pointParams,
        ...params,
        peerMinNumber: this.peerCondition.peerMinNumber,
        peerSecond: this.peerCondition.peerSecond,
      };
      //同行的撒点
      this.request[this.typaTag.tab](tabParams).then((res) => {
        res.data.map((item) => {
          item.lat = item.geoPoint.lat;
          item.lon = item.geoPoint.lon;
          item.faceCaptureList =
            item.faceCaptureList.length > 100
              ? item.faceCaptureList.slice(0, 100)
              : item.faceCaptureList;
        });
        // 同行次数的撒点
        // this.$emit('peerList', res.data, this.coincideList, this.typaTag.tab);
        // 修改地图放一起
        this.peerPointList = res.data;
        this.tabName(this.typaTag.tab);
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.mapBase.peerPoint(this.coincideList);
          }, 10);
        });

        // this.$emit('coincideList', this.coincideList, this.typaTag.tab); //放在最后撒点
      });
      this.$nextTick(() => {
        this.$refs.peerList.init(point, this.typaTag, this.leftObject);
      });
    },
    // 同行重合撒点
    coincide(list) {
      this.coincideList = [];
      list.map((item, index) => {
        this.$set(this.coincideList, index, item);
      });
    },
    // 点击某条同行重合
    handleCoinPeer(item, index, type) {},
    // 关闭同行次数窗口
    handleBack() {
      this.rightPeerList = false;
      this.rightShowList = true;
      this.resetPoint(1);
    },
    // 左侧
    handleLeftCancel() {
      this.rightShowList = false;
      this.rightPeerList = false;
      this.showList = false;
      this.resetPoint(0);
    },
    // 右侧检索结果关闭
    handleRightCancel() {
      this.rightShowList = false;
      this.resetPoint(0);
    },
    tabName(index) {
      switch (index) {
        case 0:
          this.mapName = "face";
          break;
        case 1:
          this.mapName = "vehicle";
          break;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.peer-analysis {
  padding: 0;
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
