<template>
  <div class="label-property">
    <Form class="form" ref="form" autocomplete="off" label-position="right">
      <FormItem :required="isRequired('sxjdwsc')" class="left-item" label="摄像机点位俗称">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.sxjdwsc"
          placeholder="请输入摄像机点位俗称"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.sxjdwsc || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sxjdwsc">
          {{ errorData.sxjdwsc }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sxjwzlx')" class="left-item" label="摄像机位置标签">
        <Select v-if="!isView" v-model="formCustom.sxjwzlx" class="width-lg" placeholder="请选择摄像机位置标签">
          <Option v-for="(item, index) in positionList" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sxjwzlxText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sxjwzlx">
          {{ errorData.sxjwzlx }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sxjajlx')" class="left-item" label="摄像机案件标签">
        <Input
          v-if="!isView"
          type="text"
          class="width-lg"
          v-model="formCustom.sxjajlx"
          placeholder="请输入摄像机案件标签"
          :maxlength="100"
        ></Input>
        <span v-else class="base-text-color">{{ formCustom.sxjajlx || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sxjajlx">
          {{ errorData.sxjajlx }}
        </div>
      </FormItem>
      <FormItem :required="isRequired('sxjdypc')" class="left-item" label="摄像机调用频次">
        <Select v-if="!isView" v-model="formCustom.sxjdypc" class="width-lg" placeholder="请选择摄像机调用频次">
          <Option
            v-for="(item, index) in frequencyList"
            :key="index"
            :label="item.dataValue"
            :value="`${item.dataKey}`"
          >
          </Option>
        </Select>
        <span v-else class="base-text-color">{{ formCustom.sxjdypcText || '未知' }}</span>
        <div class="error-tip ellipsis" :title="errorData.sxjdypc">
          {{ errorData.sxjdypc }}
        </div>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    defaultForm: {
      type: Object,
    },
    modalAction: {
      type: String,
    },
    errorData: {
      type: Object,
    },
    allDicData: {
      type: Object,
    },
    // 校验必填字段
    isRequired: {
      type: Function,
    },
  },
  data() {
    return {
      positionList: [
        // { label: "学校周边", value: "1" },
        // { label: "政府周边", value: "2" },
      ],
      frequencyList: [
        // { label: "高频调用", value: 1 },
        // { label: "正常调用", value: 2 },
        // { label: "低频调用", value: 3 },
        // { label: "几乎不用", value: 4 },
      ],
      formCustom: {
        sxjdwsc: '',
        sxjwzlx: '',
        sxjwzlxText: '',
        sxjajlx: '',
        sxjdypc: '',
        sxjdypcText: '',
      },
      formError: {},
    };
  },
  methods: {
    validate() {
      // this.$refs.form.validate((valid)=>{
      //   if (valid) {

      //   } else {

      //   }
      // })
      this.$emit('putData', this.formCustom);
      return true;
    },
    resetFields() {
      this.$refs.form.resetFields();
      this.formCustom = {
        sxjdwsc: '',
        sxjwzlx: '',
        sxjwzlxText: '',
        sxjajlx: '',
        sxjdypc: '',
        sxjdypcText: '',
      };
    },
  },
  watch: {
    defaultForm: {
      handler(val) {
        let length = Object.keys(val).length;
        this.$nextTick(() => {
          this.resetFields();
          if (length > 0) {
            Object.keys(val).forEach((key) => {
              if (this.formCustom.hasOwnProperty(key)) {
                this.formCustom[key] = val[key];
              }
            });
          }
        });
      },
      immediate: true,
    },
    errorData: {
      handler(val) {
        let length = Object.keys(val).length;
        if (length > 0) {
          Object.keys(val).forEach((key) => {
            if (this.formCustom.hasOwnProperty(key)) {
              this.formError[key] = val[key];
            }
          });
        } else {
          this.formError = {};
        }
      },
      immediate: true,
      deep: true,
    },
    allDicData: {
      handler(val) {
        for (let i in val) {
          if (this.hasOwnProperty(i)) {
            this[i] = val[i];
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    isView() {
      return this.modalAction === 'view';
    },
  },
};
</script>
<style lang="less" scoped>
@leftMargin: 400px;
@inputWidth: 240px;
.label-property {
  height: 586px;
  margin-top: 20px;
}
.left-item {
  @{_deep} .ivu-form-item-error-tip {
    margin-left: @leftMargin;
  }
  @{_deep}.ivu-form-item-label {
    float: left;
    width: 400px;
  }
}
.error-tip {
  position: absolute;
  top: 100%;
  left: 0;
  line-height: 1;
  padding-top: 5px;
  color: #ed4014;
  margin-left: @leftMargin;
  width: @inputWidth;
}
</style>
