<!--
    * @FileDescription: 昼伏夜出活动搜索
    * @Author: H
    * @Date: 2023/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="bottomBox">
        <div class="title">
            <p>活动规律</p>
        </div>
        <!-- <div ref="echart" class="chart"></div> -->
        <div class="chart-box">
            <BarEchart :title='{}' :yAxis="yAxis" :grid='grid' :xAxis="activeNumXAxis" :series="activeNumSeries"/>
        </div>
        
    </div>
</template>

<script>
import BarEchart from '@/components/echarts/bar-echart';
import * as echarts from 'echarts';
export default {
    name: '',
    components:{
        BarEchart   
    },
    props:{
        echartDate: {
            type: Array,
            default: ()=> []
        }
    },
    data () {
        return {
            activeNumXAxis: { 
                name:'日期',
                data: this.echartDate,
                axisLine: {
                    lineStyle: {
                        color: '#D3D7DE'
                    }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.35)',
                    // rotate: 40
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        color: '#D3D7DE'
                    }
                }
            },
            yAxis:{
                name:'单位:抓拍数量',
                axisLine: {
                    show: true,
                    lineStyle: {
                        type: 'dashed',
                        color: '#D3D7DE'
                    }
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.35)'
                },
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        color: '#D3D7DE'
                    }
                }
            },
            activeNumSeries: [
                {
                    name: '晚上',
                    type: 'bar',
                    stack: 'one',
                    data: [1, 1, 1, 1,],
                    barWidth: '30%',
                    itemStyle: {
                        color: '#F29F4C'
                    }
                }
            ],
            grid: {
                top: '14%',
                left: '1%',
                right: '4%',
                bottom: '8%',
                containLabel: true,
            },
        }
    },
    watch:{
        echartDate:{
            handler(val) {
                // this.activeNumXAxis.data = val
            }
        }
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
        // this.init();
    },
    methods: {
        init() {
            let option = {
                tooltip: {},
                grid: {
                    top: '14%',
                    left: '1%',
                    right: '4%',
                    bottom: '8%',
                    containLabel: true,
                },
                xAxis: [{
                    name:'日期',
                    type: 'category', 
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(211, 215, 222, 1)',
                        },
                    },
                    axisLabel: {
                        margin: 20,
                        textStyle: {
                            color: 'rgba(0, 0, 0, 0.35)',
                        },
                    },
                    axisTick: {
                        show: false,
                    },
                    data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', 
                        '11','12', '13', '14', '15', '16', '17', '18', '19','20',
                        '21','22', '23', '24', '25', '26', '27', '28', '29', '30', '31']
                }],
                yAxis: [{
                    name:'单位:抓拍数量',
                    type: 'value',
                    axisLine: {
                        show: true,
                        lineStyle: {
                            color: 'rgba(211, 215, 222, 1)',
                        },
                    },
                    axisLabel: {
                        margin: 20,
                        textStyle: {
                            color: 'rgba(0, 0, 0, 0.35)',
                        },
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type:'dashed',
                            color: 'rgba(211, 215, 222, 1)'
                        }
                    },
                    axisTick: {
                        show: false,
                    },
                }],
                series: [{
                    type: 'line',
                    smooth: true, //是否平滑曲线显示
                    symbol:'none',  // 默认是空心圆（中间是白色的），改成实心圆
                    itemStyle:{
                        normal: {
                            lineStyle: {
                                color:"rgba(44, 134, 248, 1)"
                            }
                        }
                    },
                    areaStyle: { //区域填充样式
                        normal: {
                            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
                                    offset: 0,
                                    color: 'rgba(44, 134, 248, 0.3)'
                                },
                                {
                                    offset: 1,
                                    color: 'rgba(44, 134, 248, 0.1)'
                                }
                            ], false),
                            shadowColor: 'rgba(44, 134, 248, 0.1)', //阴影颜色
                            shadowBlur: 20 //shadowBlur设图形阴影的模糊大小。配合shadowColor,shadowOffsetX/Y, 设置图形的阴影效果。
                        }
                    },
                    data: [42, 26, 29, 32, 36, 38, 42, 47,50,53,57,59,62,64,67,69,73,75,77,79,81,84,87, 64,76,84,34,96,99,49,84]
                }]
            };
            this.myEchart = echarts.init(this.$refs.echart);
            this.myEchart.setOption(option)
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
.bottomBox{
    position: absolute;
    bottom: 10px;
    left: 10px;
    width: 1440px;
    height: 310px;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    border-radius: 4px;
    filter: blur(0px);
    .chart{
        width: 100%;
        height: calc(~'100% - 40px');
    }
    .chart-box{
        width: 100%;
        height: calc(~'100% - 40px');
    }
}
</style>
