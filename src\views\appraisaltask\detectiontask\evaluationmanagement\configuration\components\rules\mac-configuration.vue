<template>
  <ui-modal v-model="visible" title="MAC地址检测配置" width="35%" @query="handleSubmit">
    <div class="device-container">
      <div class="site-content mb-sm">
        <span class="base-text-color">1、MAC地址中的字母允许的格式：</span>
        <Checkbox v-model="extraParam.allowUpcase">大写</Checkbox>
        <Checkbox v-model="extraParam.allowLowercase">小写</Checkbox>
      </div>
      <div class="separator-content mb-sm">
        <div class="base-text-color mb-sm">2、MAC地址只能由如下分隔符分隔：</div>
        <div class="mac-box">
          <div class="mb-sm" v-for="(item, index) in extraParam.spiltList" :key="index">
            <Input class="w-34" v-model="extraParam.spiltList[index]"></Input>
            <i
              v-if="index === extraParam.spiltList.length - 1"
              @click="handleAdd"
              class="icon-font icon-tree-add f-16 ml-sm color-primary"
            ></i>
            <i
              v-if="index !== 0"
              @click="handleRemove(index)"
              class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'mac-configuration',
  components: {},
  props: {},
  data() {
    return {
      visible: false,
      extraParam: {
        spiltList: [],
        allowUpcase: false,
        allowLowercase: false,
      },
      indexConfig: {},
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    validateForm() {
      if (!this.extraParam.allowLowercase && !this.extraParam.allowUpcase) {
        this.$Message.error('MAC地址中的字母允许的格式不能为空');
        return false;
      }
      for (let i = 0; i < this.extraParam.spiltList.length; i++) {
        let item = this.extraParam.spiltList[i];
        if (!item) {
          this.$Message.error('分隔符不能为空');
          return false;
        }
      }
      return true;
    },
    async handleSubmit() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        if (!this.validateForm()) return;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(this.extraParam),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    handleAdd() {
      this.extraParam.spiltList.push('');
    },
    handleRemove(index) {
      if (this.extraParam.spiltList.length < 2) {
        return this.$Message.error('请至少保留一个配置项');
      }
      this.extraParam.spiltList.splice(index, 1);
    },
    async init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      await this.getDeviceNameConfig();
    },
    async getDeviceNameConfig() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        this.extraParam = JSON.parse(
          data.extraParam || '{"spiltList":[":","-"],"allowUpcase":true,"allowLowercase":false}',
        );
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.flex-content {
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
.color-primary {
  color: var(--color-primary);
}
.w-34 {
  width: 34px;
}
.dis-select {
  user-select: none;
}
.ml20 {
  margin-left: 20px;
}
@{_deep} .ivu-modal {
  width: 600px !important;
  .ivu-modal-body {
    padding: 0 50px 50px 50px !important;
  }
}
.add-icon {
  font-size: 26px;
  color: var(--color-primary);
}
.mac-box {
  max-height: 500px;
  overflow-y: auto;
}
</style>
