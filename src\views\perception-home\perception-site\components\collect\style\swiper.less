@swiper-width: 50%;
// swiper
/deep/ .swiper-wrapper {
    width: 728px !important;
}
/deep/ .swiper-slide {
    // width: 100% !important;
    // height: 180px;
    display: flex;
    align-items: center;
}
.middle-swiper-container {
    width: 100%;
    padding: 3px 46px 0;
    display: flex;
    align-items: center;
    height: 100%;
    .middle-swiper {
        // width: 100%;
        margin-left: auto;
        margin-right: auto;
        height: 198px;
        // padding-bottom: 36px;
        .swiper-slide {
            // width: 33.3%;
            width: @swiper-width;
            &:hover {
                z-index: 100;
            }
        }
    }
    .swiper-pagination {
        bottom: 0;
        /deep/ .swiper-pagination-bullet {
            width: 70px;
            height: 6px;
            border-radius: 0;
            background: rgba(255, 255, 255, 0.3);
            opacity: 1;
            &.swiper-pagination-bullet-active {
                background: #2c86f8;
            }
        }
    }
}
/deep/ .swiper-slide-content {
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    height: 104px;
    .item-header {
        display: flex;
        align-items: center;
        font-weight: bold;
        border-bottom: 1px solid #d3d7de;
        padding: 2px 6px;
        font-size: 16px;
        height: 42px;
        .title {
            display: flex;
            align-items: center;
            margin-right: 10px;
            color: rgba(0, 0, 0, 0.9);
            font-size: 16px;
            font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
            .icon {
                width: 30px;
                height: 30px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                margin-right: 10px;
                &.icon_anjian {
                    background-image: url('~@/assets/img/archives/icons/icon_anjian.png');
                }
                &.icon_baojing {
                    background-image: url('~@/assets/img/archives/icons/icon_baojing.png');
                }
                &.icon_changqudi {
                    background-image: url('~@/assets/img/archives/icons/icon_changqudi.png');
                }
                &.icon_cheliang {
                    background-image: url('~@/assets/img/archives/icons/icon_cheliang.png');
                }
                &.icon_guanxi {
                    background-image: url('~@/assets/img/archives/icons/icon_guanxi.png');
                }
                &.icon_tongxing {
                    background-image: url('~@/assets/img/archives/icons/icon_tongxing.png');
                }
                &.icon_yichang {
                    background-image: url('~@/assets/img/archives/icons/icon_yichang.png');
                }
                &.icon_zhuapai {
                    background-image: url('~@/assets/img/archives/icons/icon_zhuapai.png');
                }
            }
        }
        .text,
        .number {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .number {
            font-size: 24px;
        }
    }
    .info-content {
        padding: 10px;
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        overflow: hidden;
        border-radius: 4px;
        height: 64px;
        position: relative;
        &.has-hover {
            &:after {
            content: '';
                position: absolute;
                bottom: -6px;
                right: -6px;
                width: 0;
                height: 0;
                border-bottom: 8px solid transparent;
                border-top: 8px solid #d8d8d8;
                border-left: 8px solid transparent;
                border-right: 8px solid transparent;
                transform-origin: center center;
                transform: rotate(-45deg);
            }
            &:hover {
                height: auto;
                &:after {
                    transform: rotate(135deg);
                    bottom: 6px;
                    right: 6px;
                }
            }
        }
        .info-item {
            width: 100%;
            display: flex;
            background: #fff;
            align-items: center;
            line-height: 25px;
            font-size: 12px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            .iconfont {
                margin-right: 4px;
                font-size: 12px;
            }
        }
        &.img-content {
            display: flex;
            justify-content: space-between;
            .empty {
                width: 44px;
            }
            .img-item {
                width: 44px;
                height: 44px;
                overflow: hidden;
                position: relative;
                border-radius: 4px;
                border: 1px solid #d3d7de;
                img {
                    width: 100%;
                    height: 100%;
                }
                .badge {
                    position: absolute;
                    width: 21px;
                    height: 21px;
                    background: #2c86f8;
                    border-radius: 4px;
                    left: -3px;
                    top: -3px;
                    color: #fff;
                    font-size: 12px;
                    line-height: 21px;
                    text-align: center;
                    transform: scale(0.8);
                }
                p {
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 13px;
                    background: rgba(0, 0, 0, 0.8);
                    text-align: center;
                    line-height: 13px;
                    span {
                        color: #fff;
                        font-size: 12px;
                        float: left;
                        width: 100%;
                        transform: scale(0.8);
                    }
                }
            }
        }
    }
    .no-content {
        width: 100%;
        padding-top: 10px;
        text-align: center;
        font-size: 14px;
        color: #d3d7de;
        display: flex;
        align-items: center;
        justify-content: center;
        .iconfont {
            margin-right: 10px;
            font-size: 14px;
        }
    }
}
  
// 图标样式
.swiper-button-prev,
.swiper-button-next {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.5);
    text-align: center;
    line-height: 30px;
    margin-top: -15px;
    .iconfont {
        color: #fff;
        font-size: 18px;
    }
    &:hover {
        background: rgba(0, 0, 0, 0.7);
    }
    &:active {
        background: rgba(0, 0, 0, 1);
    }
}
.swiper-button-prev {
    transform: rotate(180deg);
    left: 30px;
    top: 50%;
}
.swiper-button-next {
    right: 30px;
    top: 50%;
}