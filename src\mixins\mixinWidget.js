import { mapMutations } from "vuex"

export default {
  methods: {
    ...mapMutations('control', ['setPersonControl']),
    ...mapMutations('control', ['setVehicleControl']),
    // 实名档档案详情
    archivesDetailHandle(archiveNo) {
      const { href } = this.$router.resolve({
        name: 'people-archive',
        query: {
          archiveNo:archiveNo,
          source: 'people',
          initialArchiveNo: archiveNo
        }
      })
      window.open(href, '_blank')
    },
    // 车辆档案详情
    vehicleArchivesDetailHandle(plateNo) {
      const { href } = this.$router.resolve({
        name: 'vehicle-archive',
        query: {
          archiveNo: JSON.stringify(plateNo),
          plateNo: JSON.stringify(plateNo),
          source: 'car',
        }
      })
      window.open(href, '_blank')
    },
    /**
     *人脸布控
     * @param row
     * @param photoUrl 图片url
     * @returns {Promise<void>}
     */
    async onPeopleControl(row, photoUrl) {
      if (!photoUrl) return this.$Message.error("图片不存在")
      this.setPersonControl({
        ...row,
        photoUrl
      })
      this.$router.push("/target-control/control-task/add")
    },/**
     *车辆布控
     * @param row
     * @param photoUrl 图片url
     * @returns {Promise<void>}
     */
    async onVehicleControl(row, photoUrl) {
      if (!photoUrl) return this.$Message.error("图片不存在")
      this.setVehicleControl({
        ...row,
        photoUrl
      })
      this.$router.push("/target-control/control-task/addVehicle")
    },
    /**
     * 以图搜图
     * @param row
     * @param photoUrl 图片url
     * @param algorithmType 1人脸 2 车辆 3 人体 4 非机动车
     * @returns {Promise<void>}
     */
    async onSearchImage(row, photoUrl, algorithmType) {
      if (!photoUrl) return this.$Message.error("图片不存在")
      let picData = {
        algorithmType: algorithmType, //1人脸 2 车辆 3 人体 4 非机动车
        similarity: 75,// 相似度
      }
      this.$refs.searchImage.searchImage(picData, photoUrl)
    },
    onSubmit({algorithmType, similarity, urlList}){
      let params = {
        keyWords: '',
        algorithmType: algorithmType,
        urlList: [urlList]
      }
      this.$router.push({
        path: '/wisdom-cloud-search/cloud-default-page',
        query:{
          'params': params,
          'type': 2
        }
      })
    },
  }
}
