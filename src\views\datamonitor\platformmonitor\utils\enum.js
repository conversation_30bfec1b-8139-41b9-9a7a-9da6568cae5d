// 监控类型
export const MONITORTYPE_FACE = '2'; //人脸
export const MONITORTYPE_VEHICLE = '1'; //车辆
export const MONITOR_TYPES = [
  {
    value: '2',
    label: '人脸视图库监测',
  },
  {
    value: '1',
    label: '车辆视图库监测',
  },
];

// 统计类型
export const STATISTIC_TYPE_OFFLINE = '1'; //离线监测
export const STATISTIC_TYPE_COUNTING = '2'; //数量统计
export const STATISTICS_TYPES = [
  {
    value: '1',
    label: '离线监测',
  },
  {
    value: '2',
    label: '数量统计',
  },
];

export const DISABLED_AFTER_NOW = {
  disabledDate(date) {
    return date.getTime() > Date.now();
  },
};

export const statusEnums = {
  '1': {
    styleClass: '',
    text: '在线',
  },
  '2': {
    styleClass: 'text-error',
    text: '离线',
  },
  '3': {
    styleClass: 'text-warning',
    text: '报备',
  },
};

// 离线监测表格
export const offlineTableColumns = (emitTableFunc) => {
  return [
    {
      title: ' ',
      key: '',
      width: 20,
    },
    {
      title: '序号',
      type: 'index',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'name',
      tooltip: true,
      sortable: true,
    },
    {
      title: '当前状态',
      key: 'status',
      render: (h, { row }) => {
        return (
          <span class={['2', '3'].includes(row.status) ? statusEnums[row.status].styleClass : 'font-green'}>
            {statusEnums[row.status]?.text || ''}
          </span>
        );
      },
    },
    {
      title: '最近接收数据时间',
      key: 'firstIntoViewTimeText',
      sortable: true,
      render: (h, { row, column }) => {
        return <span class={statusEnums[row.status]?.styleClass || ''}>{row[column.key]}</span>;
      },
    },
    {
      title: '持续无数据时间',
      key: 'durationWithoutTime',
      sortable: true,
      render: (h, { row, column }) => {
        return (
          <span
            class={['link', statusEnums[row.status]?.styleClass || '']}
            onClick={() => {
              emitTableFunc({ row }, 'viewContinuousNoData');
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
    {
      title: '报备时间',
      key: 'reportTime',
      tooltip: true,
    },
  ];
};

// 数量统计表格
export const countingTableColumns = (emitTableFunc) => {
  return [
    {
      title: ' ',
      key: '',
      width: 20,
    },
    {
      title: '序号',
      type: 'index',
      width: 50,
    },
    {
      title: '行政区划',
      key: 'name',
      tooltip: true,
    },
    {
      title: '本月抓拍数量',
      key: 'captureNumByMonth',
      render: (h, { row, column }) => {
        return (
          <span
            class="link"
            onClick={() => {
              emitTableFunc({ row }, 'viewThisMonthCatch');
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
    {
      title: '今日抓拍',
      key: 'captureNumByDay',
      render: (h, { row, column }) => {
        return (
          <span
            class="link"
            onClick={() => {
              emitTableFunc({ row }, 'viewTodayCatchNum');
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
    {
      title: '日平均抓拍',
      key: 'captureAvgByDay',
      tooltip: true,
    },
    {
      title: '去年同月抓拍数量',
      key: 'captureNumByLastYear',
      render: (h, { row, column }) => {
        return (
          <span
            class="link"
            onClick={() => {
              emitTableFunc({ row }, 'viewMonthCatchInLastYear');
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
    {
      title: '变化数量',
      key: 'changeQuantity',
      render: (h, { row, column }) => {
        return <span>{row[column.key]}</span>;
      },
    },
    {
      title: '变化率',
      key: 'changeRate',
      tooltip: true,
    },
    {
      title: '本月累计离线时长',
      key: 'durationTimeByMonth',
      render: (h, { row, column }) => {
        return (
          <span
            class="link text-error"
            onClick={() => {
              emitTableFunc({ row }, 'viewMonthOfflineTime');
            }}
          >
            {row[column.key]}
          </span>
        );
      },
    },
  ];
};
