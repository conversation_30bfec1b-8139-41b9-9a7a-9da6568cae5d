<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">
      {{ title.text }}
    </div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">
      {{ title.subtext }}
    </div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
import * as echarts from "echarts";
export default {
  props: {
    title: {
      type: Object,
      default: () => {
        return {
          show: true,
          text: "",
          subtext: "单位：次",
        };
      },
    },
    legend: {
      type: Object,
      default: () => {
        return {
          show: false,
        };
      },
    },
    visualMap: {
      type: Array,
      default: () => {
        return [
          {
            show: false,
            calculable: false,
            type: "piecewise",
            pieces: [
              { gte: 5, color: "#48BAFF" },
              { gte: 1, lt: 4, color: "#F9F9F9" },
              { value: 0, color: "#F29F4C" },
            ],
          },
        ];
      },
    },
    tooltip: {
      type: Object,
      default: () => {
        return {
          position: "top",
          formatter: function (params) {
            return `${params.value[2]}`;
          },
        };
      },
    },
    grid: {
      type: Object,
      default: () => {
        return {
          height: "76%",
          top: "15%",
          left: "3%",
          right: "1%",
        };
      },
    },
    yAxis: {
      type: Object,
      default: () => {
        return {
          name: "",
          type: "category",
          data: [],
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
              color: "#D3D7DE",
            },
          },
          splitArea: {
            show: true,
            areaStyle: {
              color: "#F9F9F9",
            },
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: "#D3D7DE",
            },
          },
        };
      },
    },
    xAxis: {
      type: Object,
      default: () => {
        return {
          type: "category",
          data: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
            "24",
          ],
          splitArea: {
            show: true,
          },
          minorTick: {
            splitNumber: 24,
          },
          axisLine: {
            lineStyle: {
              color: "rgba(0, 0, 0, 0.35)",
            },
          },
        };
      },
    },
    series: {
      type: Array,
      default: () => {
        return [
          {
            type: "heatmap",
            data: [],
            label: {
              show: true,
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: "#F9F9F9",
              },
            },
          },
        ];
      },
    },
  },
  data() {
    return {
      myEchart: null,
    };
  },
  watch: {
    series: {
      deep: true,
      handler(val) {
        this.init();
      },
    },
  },
  mounted() {
    this.init();
  },
  deactivated() {
    this.removeResizeFun();
  },
  beforeDestroy() {
    this.removeResizeFun();
  },
  methods: {
    init() {
      this.myEchart = echarts.init(this.$refs.echart);
      let option = {
        legend: this.legend,
        tooltip: this.tooltip,
        grid: this.grid,
        visualMap: this.visualMap,
        yAxis: this.yAxis,
        xAxis: this.xAxis,
        series: this.series,
      };
      this.myEchart.setOption(option);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    removeResizeFun() {
      window.removeEventListener("resize", () => this.myEchart.resize());
    },
  },
};
</script>
<style lang="less" scoped>
.echart-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .echart-text {
    font-size: 14px;
    font-family: "MicrosoftYaHei-Bold";
    font-weight: bold;
    line-height: 20px;
    text-align: center;
  }
  .echart-subtext {
    margin-top: 8px;
    font-size: 12px;
    line-height: 18px;
  }
  .echart-content {
    flex: 1;
  }
}
</style>
