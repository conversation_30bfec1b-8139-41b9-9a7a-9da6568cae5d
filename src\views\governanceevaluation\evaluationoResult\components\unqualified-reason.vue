<template>
  <div class="unqualified-reason">
    <div class="ranking-title f-14">
      <span class="icon-font icon-buhegeyuanyinfenbutongji mr-xs"></span>
      <span>不合格原因分布统计</span>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length"
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartLoading"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'unqualified-reason',
  data() {
    return {
      propertyEchart: {},
      echartData: [],
      series: [],
      echartLoading: false,
    };
  },
  props: {},
  mixins: [dealWatch],
  async mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getGraphsInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async getGraphsInfo() {
      const { regionCode, orgCode, statisticType, access, indexId, batchId, canPlay } = this.$route.query;
      this.echartLoading = true;
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'TASK_RESULT',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      };
      if (canPlay && canPlay !== '2') {
        Object.assign(params, {
          customParameters: {
            canPlay: canPlay,
          },
        });
      }
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getCircularGraphsInfo, params);
        this.echartData = data?.seriesData || [];
        this.initRing(this.echartData);
      } catch (err) {
        console.log(err);
      } finally {
        this.echartLoading = false;
      }
    },
    initRing() {
      let data = this.echartData.map((item) => {
        return { value: item.count, name: `${item.reason} ${item.count}` };
      });
      let center = ['20%', '50%'];
      let legendRight = '10%';
      let radius = ['65%', '80%'];
      if (data.length > 6) {
        center = ['8%', '50%'];
        legendRight = '0%';
        radius = ['50%', '65%'];
      }
      let opts = {
        legend: {
          right: legendRight,
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            center: center,
            radius: radius,
            color: [
              $var('--color-orange-1'),
              $var('--color-purple-3'),
              $var('--color-green-3'),
              $var('--color-cyan-1'),
              $var('--color-blue-4'),
              $var('--color-yellow-15'),
              $var('--color-green-15'),
              $var('--color-pink-5'),
              $var('--color-pink-6'),
              $var('--color-purple-18'),
              $var('--color-orange-9'),
              $var('--color-green-16'),
              $var('--color-gray-6'),
              $var('--color-cyan-4'),
              $var('--color-dark-green-2'),
              $var('--color-pink-7'),
              $var('--color-orange-10'),
              $var('--color-yellow-16'),
              $var('--color-cyan-5'),
            ],
            left: 30,
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              disabled: true,
            },
            labelLine: {
              show: false,
            },
            data: data,
          },
        ],
      };
      this.propertyEchart = this.$util.doEcharts.ReviewConsequencePie(opts);
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.unqualified-reason {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  border-radius: 4px;
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
