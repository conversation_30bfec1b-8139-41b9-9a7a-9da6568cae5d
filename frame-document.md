# **标签系统前端框架搭建说明文档**

**说明  **** `**

本框架按照规范文档开发，基于ivdg、系统管理系统前端款架， 主要是对其目录结构、文件命名、插件选取等进行规范，加入了Eslint代码检测工具并且加入browserslit工具，重新封装了axios插件，路由权限控制功能以及导航系统沿用了数据网关系统，本框架采用less工具进行css编译工作。

**目录结构**

目录结构安装规范文档进行重新调整，详情如下

├── doker                      # 自动打包相关

├── public                     # 静态资源

│   │── favicon.ico            # favicon图标

│   └── index.html             # html模板

├── src                        # 源代码

│   ├── api                    # 所有请求

│   ├── assets                 # 主题 字体等静态资源

│   ├── components             # 全局公用组件

│   ├── directive              # 全局指令

│   ├── icons                  # 项目所有 svg icons

│   ├── layout                 # 全局 layout

│   ├── libs                   # 全局公用方法(框架权限，配置相关)

        ├── settings.js           # 全局设置

│   ├── mixins                 # 全局混入对象

│   ├── router                 # 路由

         └── router-permission.js          # 权限管理（路由权限控制）

│   ├── store                  # 全局 store管理

│   ├── styles                 # 全局样式

│   ├── util                   # 全局公共方法(业务)
    
    ├── filters                # 全局 filter

    ├── modules                # 全局 modules

│   ├── views                  # views 所有页面

│   ├── App.vue                # 入口页面

│   ├── main.js                # 入口文件 加载组件 初始化等
  
├── .babelrc                   # babel-loader 配置

├── .travis.yml                # 自动化CI配置

├── vue.config.js              # vue-cli 配置

├── postcss.config.js          # postcss 配置

└── package.json               # package.json

**安装**

按照正常操作安装

# 克隆项目 (注意连接SVN)

git clone http://192.168.1.123:10080/platform/qsdi/qsdi-cloud/qsdi-cloud-label/qsdi-cloud-label-view/qsdi-cloud-label-view

# 进入项目目录 cd qsdi-cloud-label-view

# 安装依赖 npm install

# 本地开发 启动项目 npm run serve

**依赖库说明**

**1**** 、 ****view-design**

为了保持UI库统一性，目前设定整个框架只用此ui库，如需引用其他ui库组件，请单独复制包到components下进行引用，如element ui 中的Tree组件

文档地址：[https://iviewui.com/docs/introduce](https://iviewui.com/docs/introduce)

**2**** 、 ****axios**

axios 已经参照系统管理中的方式进行封装，不再使用ivdg中的方式，demo如下

// 在src/api 文件中定义好接口方法

// 获取用详情

export function getUserInfo (data) {

return request({

url: service + &#39;/user/info&#39;,

method: &#39;get&#39;

})

}

// 在需要请求接口的组件中导入该接口方法

import { getUserInfo } from &#39;@/api/user&#39;

// 使用该方法

getUserInfoFun ({ state, commit }) {

getUserInfo().then(res =\&gt; {

// Todo...

}).catch(err =\&gt; {

// Todo...

})

}

**3**** 、 ****day.js**

由于之前moment.js 体积较大，且考虑到项目中不会用到过于复杂的时间操作，因此更换为较轻的day.js ,使用方法基本跟moment.js一致，已安装到全局，可使用this.$dayjs来调用。

demo：this.$dayjs().format(&#39;YY-MM-DD HH:ss:mm&#39;)

参考文档：[https://dayjs.fenxianglu.cn/category/](https://dayjs.fenxianglu.cn/category/)

**4**** 、 ****lodash**

lodash 是一个 JavaScript 实用工具库，提供一致性，及模块化、性能和配件等功能，消除了处理数组的麻烦，从而简化了 JavaScript、 数字、对象、字符串等。具体方法请查看[文档](https://www.lodashjs.com/)

参考文档：[https://www.lodashjs.com/](https://www.lodashjs.com/)

**5**** 、 ****echarts echarts-gl**

官方文档：[https://echarts.apache.org/zh/index.html](https://echarts.apache.org/zh/index.html)

**6**** 、 ****@antv/g6**

参考文档：[http://antv-2018.alipay.com/zh-cn/g6/3.x/index.html](http://antv-2018.alipay.com/zh-cn/g6/3.x/index.html)

**7**** 、 ****screenfull** [**全屏插件**](https://www.npmjs.com/package/screenfull)X

改插件需要单独导入，没有放入全局，可根据el 局部操作

import screenfull from &#39;screenfull&#39;;

$(&#39;img&#39;).on(&#39;click&#39;, event =\&gt; {

if (screenfull.isEnabled) {

screenfull.toggle(event.target);

}

});

**8**** 、 ****vue-cout-to** [**数字滚动**](https://www.npmjs.com/package/vue-count-to)X

**9**** 、 ****vue-lazyload** [**图片懒加载**](https://www.npmjs.com/package/vue-lazyload)

// 图片懒加载配置

Vue.use(VueLazyload, {

preLoad: 1.3,

error: require(&#39;@/assets/img/default-img/image_error.png&#39;),

loading: require(&#39;@/assets/img/imgloading.png&#39;),

attempt: 1

})

//组件中使用

\&lt;div style=&quot;width: 800px;height: 500px;overflow: scroll&quot;\&gt;

\&lt;img v-lazy=&quot;img&quot; v-for=&quot;img in imgs&quot;/\&gt;

\&lt;img v-lazy:background-image=&quot;img&quot; v-for=&quot;img in imgs&quot;/\&gt;

\&lt;/div\&gt;

**全局函数**

Vue.prototype.$dayjs = dayjs // 时间处理库

Vue.prototype.$util = util // 自定义工具库

Vue.prototype.checkPermission = util.permission.checkPermission // 权限函数

**权限验证**

封装了一个全局指令权限，能简单快速的实现按钮级别的权限判断，使用如下

\&lt;div class=&quot;depending-hover&quot; v-permission=&quot;[&#39;dsserver\_edit&#39;, &#39;dsserver\_delete&#39;]&quot;\&gt;

\&lt;i class=&quot;iconedit iconfont&quot; v-permission=&quot;[&#39;dsserver\_edit&#39;]&quot; @click.stop=&quot;addOrUpdateHandle(item)&quot;\&gt;\&lt;/i\&gt;

\&lt;i class=&quot;iconshanchu iconfont&quot; v-perm·ission=&quot;[&#39;dsserver\_delete&#39;]&quot; @click.stop=&quot;deleteList(item)&quot;\&gt;\&lt;/i\&gt;

\&lt;/div\&gt;

**Vue CLI**  **脚手架配置**

**一、**** browserslist **** 自动 ****添加的**  **CSS**** 浏览器前缀。**

1. 详细请参照 vue-cli 官网 [https://cli.vuejs.org/zh/guide/browser-compatibility.html](https://cli.vuejs.org/zh/guide/browser-compatibility.html)
2. 体验网址 [https://autoprefixer.github.io/?](https://autoprefixer.github.io/?)
3. can I use [https://caniuse.com/](https://caniuse.com/)

**1.**  **在项目根目录下创建****.browserslistrc **** **** 文件或者在 ****package.json****   ****文件里的添加**** browserslist ****（数组）**** 字段。**

\&gt; 0.5%

last 7 versions

not ie \&lt;= 8

ios \&gt;= 8

android \&gt;= 4.0

not dead

**2.**  **根目录**  **postcss.config.js**** 文件**

module.exports = {

plugins: {

    &#39;autoprefixer&#39;: {}

}

}

**3.**** 测试一下**

![](RackMultipart20211206-4-1aqqzhj_html_c7580f139e044770.jpg)

① **找到测试文件 添加如下**** css **** 样式。**

display:grid;

user-select:none;

② **打包项目 执行**

npm run build

③ **找到打包后的**** css **** 文件，看是否生效**

![](RackMultipart20211206-4-1aqqzhj_html_30f893b4e3430670.jpg)

**二、**** ESLint **** 代码风格检查配置**

Vue.js 风格指南：[https://cn.vuejs.org/v2/style-guide/](https://cn.vuejs.org/v2/style-guide/)

ESlint 中文网：[https://eslint.bootcss.com/docs/rules/](https://eslint.bootcss.com/docs/rules/)

Vue ESLint : [http://eslint-plugin-vue](http://eslint-plugin-vue/)

**1.**  **安装依赖**

npm install @vue/cli-plugin-eslint @vue/eslint-config-standard babel-eslint --save-dev

**2.**** 配置格式化 ****:**** 例如缩进样式、选项卡宽度、行尾字符以及编码等**

项目根目录下创建 **.editorconfig** 文件并下载vscode编辑器插件 **：**** EditorConfig for VS Code**

root = true

[\*]

charset = utf-8

indent\_style = space

indent\_size = 2

end\_of\_line = lf

insert\_final\_newline = true

trim\_trailing\_whitespace = true

EditorConfig扩展的作用是读取第一步创建的editorconfig文件中定义的规则，并覆盖user/workspace settings中的对应配置

**3.**** 根目录下创建 ****.eslintignore**  **文件**

src/assets

src/libs

dist

**4.**  **根目录下创建****.eslintrc ****文件**

module.exports = {

root: true,

parserOptions: {

    parser: &#39;babel-eslint&#39;,  // 此项是用来指定eslint解析器的，解析器必须符合规则，babel-eslint解析器是对babel解析器的包装使其与ESLint解析

    sourceType: &#39;module&#39;

},

env: {

    browser: true,

    node: true,

    es6: true,

},

extends: [&#39;plugin:vue/recommended&#39;, &#39;eslint:recommended&#39;],

// 规则值：

// &quot;off&quot;或者0      // 关闭规则关闭

// &quot;warn&quot;或者1     // 在打开的规则作为警告（不影响退出代码）

// &quot;error&quot;或者2    // 把规则作为一个错误（退出代码触发时为1）

rules: {

    &quot;vue/no-parsing-error&quot;: [2, { &quot;x-invalid-end-tag&quot;: false }], // iview 自闭合组件，改为成对出现标签 或者 vscode里面选择 设置   搜索 vetur.validation.template 把对勾去掉

    &quot;vue/no-use-v-if-with-v-for&quot;: [&quot;error&quot;, {             // v-if 和v-for 不能同时使用

      &quot;allowUsingIterationVar&quot;: false

    }],

    &quot;vue/name-property-casing&quot;: [&quot;error&quot;, &quot;PascalCase&quot;],

    &quot;vue/no-v-html&quot;: &quot;off&quot;,

    &#39;vue/order-in-components&#39;: [2, {       // 组件的属性必须为一定的顺序

      &quot;order&quot;: [

        &quot;el&quot;,

        &quot;name&quot;,

        &quot;parent&quot;,

        &quot;functional&quot;,

        &quot;delimiters&quot;,

        [&quot;components&quot;, &quot;directives&quot;, &quot;filters&quot;],

        [&quot;extends&quot;, &quot;mixins&quot;],

        [&quot;model&quot;, &quot;props&quot;, &quot;propsData&quot;],

        [&quot;data&quot;, &quot;computed&quot;],

        &quot;watch&quot;,

        &quot;LIFECYCLE\_HOOKS&quot;,

        &quot;methods&quot;,

        [&quot;template&quot;, &quot;render&quot;, &quot;renderError&quot;]

      ]

    }],

    &#39;accessor-pairs&#39;: 2,                 // 如果定义了setter，也必须定义相应的 getter

    &#39;arrow-spacing&#39;: [2, {               // 强制箭头函数的箭头前后使用一致的空格

      &#39;before&#39;: true,

      &#39;after&#39;: true

    }],

    &#39;block-spacing&#39;: [2, &#39;always&#39;],      // 禁止或强制在代码块中开括号前和闭括号后有空格

    &#39;brace-style&#39;: [2, &#39;1tbs&#39;, {         // 大括号风格要求

      &#39;allowSingleLine&#39;: true

    }],

    &#39;camelcase&#39;: [0, {                   // 强制使用骆驼拼写法命名约定

      &#39;properties&#39;: &#39;always&#39;

    }],

    &#39;comma-dangle&#39;: [2, &#39;never&#39;],        // 定义数组或对象最后多余的逗号

    &#39;comma-spacing&#39;: [2, {

      &#39;before&#39;: false,                   // 强制在逗号前后使用一致的空格

      &#39;after&#39;: true

    }],

    &#39;comma-style&#39;: [2, &#39;last&#39;],          // 强制使用一致的逗号风格

    &#39;object-curly-spacing&#39;: [2, &#39;always&#39;, {                       // 强制在花括号中使用一致的空格

      objectsInObjects: false

    }],

    &#39;array-bracket-spacing&#39;: [2, &#39;never&#39;]                          // 强制数组方括号中使用一致的空格

},

&#39;overrides&#39;: [

    {

      &#39;files&#39;: [&#39;\*.vue&#39;],

      &#39;rules&#39;: {

        &#39;indent&#39;: &#39;off&#39;

      }

    }

]

}

**5.**** 设置 ****vscode**** 设置 ****setting.json**

ctrl + p 搜索 setting.json

{

&quot;editor.fontFamily&quot;: &quot;Fira Code&quot;, // vscode 自定义字体 Fira Code，需要电脑本地字体下载好,根据个人喜欢配置

&quot;editor.fontLigatures&quot;: true, //这个控制是否启用字体连字，true启用，false不启用，这里选择启用

&quot;editor.fontWeight&quot;: &quot;normal&quot;,

&quot;editor.detectIndentation&quot;: false,

&quot;editor.tabSize&quot;: 2, // 重新设定tabsize

&quot;editor.fontSize&quot;: 14, //设置字体大小，这个不多说都明白

&quot;editor.wordWrapColumn&quot;: 100,

&quot;editor.quickSuggestions&quot;: {

    &quot;strings&quot;: true

},

&quot;[css]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;michelemelluso.code-beautifier&quot;

},

&quot;editor.suggestSelection&quot;: &quot;first&quot;,

&quot;vsintellicode.modify.editor.suggestSelection&quot;: &quot;automaticallyOverrodeDefaultValue&quot;,

&quot;[html]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;esbenp.prettier-vscode&quot;

},

// 本地配备eslint   暂统一代码格式，不做格式报错校验

//自动保存的时候，eslint自动修复一些格式上的错误

&quot;eslint.alwaysShowStatus&quot;: true,

// 每次保存的时候将代码按eslint格式进行修复

&quot;editor.codeActionsOnSave&quot;: {

    &quot;source.fixAll.eslint&quot;: true

},

&quot;eslint.autoFixOnSave&quot;: true,

&quot;eslint.options&quot;: {

    &quot;extensions&quot;: [&quot;.js&quot;, &quot;.vue&quot;]

},

&quot;eslint.validate&quot;: [&quot;javascript&quot;, &quot;javascriptreact&quot;, &quot;html&quot;, &quot;vue&quot;],

&quot;emmet.includeLanguages&quot;: {

    &quot;javascript&quot;: &quot;html&quot;,

    &quot;wxml&quot;: &quot;html&quot;

},

&quot;git.autofetch&quot;: true,

&quot;[vue]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;octref.vetur&quot;

},

&quot;workbench.colorTheme&quot;: &quot;Default Dark+&quot;,

&quot;workbench.colorCustomizations&quot;: {},

// 关闭eslint

&quot;eslint.enable&quot;: true,

// tab一键成元素html标签

&quot;emmet.triggerExpansionOnTab&quot;: true,

&quot;files.associations&quot;: {

    &quot;\*.cjson&quot;: &quot;jsonc&quot;,

    &quot;\*.wxss&quot;: &quot;css&quot;,

    &quot;\*.wxs&quot;: &quot;javascript&quot;

},

&quot;minapp-vscode.disableAutoConfig&quot;: true,

&quot;[json]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;esbenp.prettier-vscode&quot;

},

&quot;editor.formatOnSave&quot;: false, // 当保存代码的时候，会自动格式化

// 不添加分号

&quot;prettier.semi&quot;: false,

// 使用单引号

&quot;prettier.singleQuote&quot;: true,

&quot;[javascript]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;vscode.typescript-language-features&quot;

},

&quot;javascript.format.insertSpaceBeforeFunctionParenthesis&quot;: true,

&quot;vetur.completion.autoImport&quot;: true,

// 解决Vue换行问题

&quot;vetur.format.defaultFormatter.html&quot;: &quot;prettier&quot;,

&quot;vetur.format.defaultFormatter.js&quot;: &quot;vscode-typescript&quot;, //解决配置默认格式化 js换行问题

&quot;vetur.format.defaultFormatterOptions&quot;: {

    &quot;prettyhtml&quot;: {

      &quot;printWidth&quot;: 100,

      &quot;singleQuote&quot;: true,

      &quot;sortAttributes&quot;: false

    },

    &quot;wrap\_attributes&quot;: &quot;force-aligned&quot;

},

&quot;[typescript]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;esbenp.prettier-vscode&quot;

},

&quot;[jsonc]&quot;: {

    &quot;editor.defaultFormatter&quot;: &quot;esbenp.prettier-vscode&quot;

},

&quot;vetur.validation.template&quot;: false,

&quot;auto-close-tag.activationOnLanguage&quot;: [

    &quot;xml&quot;,

    &quot;php&quot;,

    &quot;blade&quot;,

    &quot;ejs&quot;,

    &quot;jinja&quot;,

    &quot;javascript&quot;,

    &quot;javascriptreact&quot;,

    &quot;typescript&quot;,

    &quot;typescriptreact&quot;,

    &quot;plaintext&quot;,

    &quot;markdown&quot;,

    &quot;vue&quot;,

    &quot;liquid&quot;,

    &quot;erb&quot;,

    &quot;lang-cfml&quot;,

    &quot;cfml&quot;,

    &quot;HTML (EEx)&quot;,

    &quot;HTML (Eex)&quot;,

    &quot;plist&quot;

],

&quot;tabnine.experimentalAutoImports&quot;: true

}

**三、查看打包后文件的大小占比**

**1.**** 安装依赖**

npm install webpack-bundle-analyzer --save-dev

**2. **** 配置 ****vue.config.js**

chainWebpack: config =\&gt; {

// 查看打包文件体积大小

config

.plugin(&#39;webpack-bundle-analyzer&#39;)

.use(require(&#39;webpack-bundle-analyzer&#39;).BundleAnalyzerPlugin)

}

**3.**  **在**** package.json ****文件中 在**  **build**** 后面加上一个 **** --report**

&quot;scripts&quot;: {

    &quot;serve&quot;: &quot;vue-cli-service serve&quot;,

    &quot;build&quot;: &quot;vue-cli-service build --report&quot;

},

**4.**** npm run build**

![](RackMultipart20211206-4-1aqqzhj_html_53653c634e50d9d5.jpg)

![](RackMultipart20211206-4-1aqqzhj_html_9fcab838e9275c6d.jpg)
