<template>
  <div class="statistic-list">
    <ul>
      <li v-for="(item, index) in statisticList" :key="index" :style="{ background: item.liBgColor }" class="mr-sm">
        <Tooltip
          class="text vt-middle ellipsis"
          placement="bottom"
          :disabled="!item?.juniorList || !item?.juniorList.length"
        >
          <div class="statistic-list-content statistic-list_bg">
            <em class="icon-font f-50 ml-lg" :class="[item.icon]"></em>
            <span>
              <p>{{ item.name }}</p>
              <p class="statistic-num">
                <countTo ref="countTo" :start-val="0" :end-val="parseInt(item.value ?? 0)" :duration="1000"> </countTo>
              </p>
            </span>
          </div>
          <template #content v-if="item?.juniorList && !!item?.juniorList.length">
            <p v-for="(one, oneIndex) in item?.juniorList" :key="oneIndex">
              <span>{{ one.name }} ：</span>
              <span>{{ one.num }}</span>
            </p>
          </template>
        </Tooltip>
      </li>
    </ul>
  </div>
</template>
<script>
import countTo from 'vue-count-to';
export default {
  name: 'statisticlist',
  props: {
    statisticList: {
      default: () => {
        return [
          {
            name: '设备总量',
            value: 0,
            icon: 'icon-shebeizongliang',
            liBgColor: 'var(--bg-card-gradient-light-blue)',
          },
          {
            name: '视频监控',
            vlaue: 0,
            icon: 'icon-ivdg-shipinjiankong',
            liBgColor: 'var(--bg-card-gradient-light-green)',
          },
          {
            name: '人脸卡口',
            vlaue: 0,
            icon: 'icon-renliankakou',
            liBgColor: 'var(--bg-card-gradient-blue-purple)',
          },
          {
            name: '车辆卡口',
            vlaue: 0,
            icon: 'icon-cheliangkakou',
            liBgColor: 'var(--bg-card-gradient-dark-blue)',
          },
          {
            name: '一类点',
            vlaue: 0,
            icon: 'icon-yileidian1',
            liBgColor: 'var(--bg-card-gradient-light-orange)',
          },
          {
            name: '二三类点  ',
            vlaue: 0,
            icon: 'icon-ersanleidian1',
            liBgColor: 'var(--bg-card-gradient-light-purple)',
          },
          {
            name: '内部监控',
            vlaue: 0,
            icon: 'icon-neibujiankong',
            liBgColor: 'var(--bg-card-gradient-blue-green)',
          },
        ];
      },
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  components: {
    countTo,
  },
};
</script>
<style lang="less" scoped>
.statistic-list {
  width: 100%;
  height: 108px;
  ul {
    width: 100%;
    height: 108px;
    display: flex;
    justify-content: center;
    align-items: center;
    li {
      // flex: 1;
      width: 100% / 7;
      height: 108px;
      display: flex;
      align-items: center;
      border-radius: 10px;
      .track-data {
        border-right: none;
      }
      .ivu-tooltip {
        width: 100%;
        height: 100%;
        display: flex;
      }
      .statistic-list-content {
        height: 100%;
        flex: 1;
        border-radius: 10px;
        line-height: 56px;
        display: flex;
        justify-content: center;
        align-items: center;

        em {
          display: inline-block;
          height: 73px;
          width: 73px;
          line-height: 73px;
          text-align: center;
          border-radius: 50%;
          background: rgba(218, 245, 248, 0.2);
        }
        .f-50 {
          font-size: 30px;
          color: #fff;
        }
        > span {
          display: inline-block;
          height: 50px;
          flex: 1;
          margin-left: 20px;
          text-align: left;
          p {
            white-space: nowrap;
            font-style: normal;
            height: 25px;
            line-height: 25px;
            color: #f5f5f5;
            font-size: 12px;
          }
          .statistic-num {
            font-size: 24px;
            color: #f5f5f5;
            -webkit-text-stroke: 1 rgba(0, 0, 0, 0);
            opacity: 1;
            font-family: 'Microsoft YaHei';
          }
        }
      }
    }
    .statistic-list_bg {
      background: url('~@/assets/img/evaluationoverview/list-bg.png') no-repeat;
      background-size: contain;
      background-position: right;
    }
  }
}
</style>
