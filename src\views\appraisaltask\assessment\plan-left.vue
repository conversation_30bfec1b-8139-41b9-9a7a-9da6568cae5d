<template>
  <!-- <div> -->
  <!--  -->
  <div class="assessment-list" v-infinite-scroll="handleReachBottom" :infinite-scroll-immediate="false">
    <Card
      v-for="(item, index) in moduleList"
      :key="index"
      :class="{ active: item.id === activeModuleMessageId }"
      class="card-box"
    >
      <div class="collapse-content-p pointer" @click="selectModule(item, index)">
        <div class="card-desc">
          <!-- <span class="icon-font icon-zidingyizhilifangan f-14 vt-middle"></span> -->
          <span class="vt-middle" :title="item.schemeName">{{ item.schemeName }}</span>
        </div>
        <div class="operationBox">
          <span class="kpi-text">共 {{ item.count || 0 }} 项考核内容</span>
          <span
            title="编辑"
            class="icon-font icon-bianji fr operationicon btn-defalut f-14"
            @click.stop="edit(item)"
          ></span>
          <!-- 目前新建不做删除，item.schemeSource === '2' -->
          <!-- <span
            title="删除"
            class="icon-font icon-shanchu2 fr f-14"
            @click.stop="deleteItem(item)"
            :class="
              item.schemeSource !== '2' && item.schemeSource !== '1'
                ? 'btn-defalut'
                : 'btn-disable' && item.id === activeModuleMessageId
                ? 'btn-del-focus'
                : 'btn-disable'
            "
          ></span> -->
          <span
            title="删除"
            class="icon-font icon-shanchu2 fr f-14"
            @click.stop="deleteItem(item)"
            :class="item.schemeSource !== '1' ? 'btn-defalut' : 'btn-disable'"
          ></span>
        </div>
      </div>
    </Card>
    <div v-show="loading">
      <img src="@/assets/img/common/loading.gif" alt="" />
    </div>
  </div>
  <!-- </div> -->
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';
export default {
  components: {},
  props: {},
  data() {
    return {
      loading: false,
      moduleList: [],
      total: '',
      activeModuleMessageId: '',
      schemeData: { params: { pageNumber: 1, pageSize: 20 } },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {
    this.get_left(true);
  },
  mounted() {},
  methods: {
    async handleReachBottom() {
      if (!(this.schemeData.params.pageNumber * this.schemeData.params.pageSize >= this.total)) {
        this.schemeData.params.pageNumber++;
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.schemePageList, this.schemeData);
        let scheme = this.setSchemeListPageInfo(data);
        this.moduleList.push(...scheme);
        this.loading = false;
      }
    },
    resetSearchData() {
      this.schemeData = { params: { pageNumber: 1, pageSize: 20 } };
    },
    async get_left(boolen) {
      this.resetSearchData();
      //左侧列表的接口
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.schemePageList, this.schemeData);
        let scheme = this.setSchemeListPageInfo(data);
        this.moduleList = scheme;
        this.total = data.total;
        if (boolen && this.moduleList && this.moduleList.length > 0) {
          this.$emit('selectModule', this.moduleList[0]);
          this.activeModuleMessageId = this.moduleList[0].id;
        }
      } catch (err) {
        console.log(err);
      }
    },
    setSchemeListPageInfo(data) {
      return data.entities.map((item) => {
        item.pageNumber = data.pageNumber;
        item.pageSize = data.pageSize;
        return item;
      });
    },
    // 切换左侧导航菜单
    selectModule(item) {
      this.activeModuleMessageId = item.id;
      this.$emit('selectModule', item);
    },
    // 编辑
    edit(item) {
      this.$emit('edit', item);
    },
    // 删除
    deleteItem(item) {
      if (item.schemeSource === '1') {
        return;
      }
      // if (item.schemeSource === '2') {
      this.$UiConfirm({
        content: `您要删除${item.schemeName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$http.get(governanceevaluation.schemeDel, { params: { id: item.id } }).then((res) => {
            this.$Message.success('删除成功！');
            this.get_left(true);
          });
        })
        .catch((res) => {
          console.log(res);
        });
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.assessment-list {
  position: relative;
  overflow-y: auto;
  padding-left: 10px;
  padding-right: 10px;
  @{_deep} .ivu-card {
    margin-bottom: 10px;
    border: 1px solid var(--border-vertical-tab);
    border-radius: 4px;
    background: var(--bg-vertical-tab);
    .collapse-content-p {
      color: var(--color-vertical-tab);
    }
    &:hover {
      background-color: var(--bg-vertical-tab-hover);
      .collapse-content-p {
        color: var(--color-vertical-tab-hover);
        .btn-defalut {
          color: var(--color-vertical-hover-tab-btn);
        }
        .btn-disable {
          color: var(--color-vertical-hover-tab-btn-disabled);
          cursor: no-drop;
        }
        .operationBox {
          border-top: 1px dashed var(--devider-vertical-tab-hover);
        }
      }
    }
    .ivu-card-body {
      padding: 0px !important;
    }

    &.active {
      background: var(--bg-vertical-tab-active);
      .collapse-content-p {
        color: var(--color-vertical-tab-active);
        .btn-defalut {
          color: var(--color-vertical-active-tab-btn);
          &:hover {
            color: var(--color-vertical-active-tab-btn-hover);
          }
        }
        .btn-disable {
          color: var(--color-vertical-active-tab-btn-disabled);
        }
        .operationBox {
          border-top: 1px dashed var(--devider-vertical-tab-active);
        }
      }
    }
    .collapse-content-p {
      color: var(--color-vertical-tab);
      // padding: 10px;
      .card-desc {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 10px;
      }

      .operationBox {
        display: flex;
        padding: 10px;
        border-top: 1px dashed var(--devider-vertical-tab);
      }
      .btn-text {
        height: 22px !important;
        line-height: 22px !important;
      }
      .kpi-text {
        line-height: 22px;
        font-size: 12px;
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .operationicon {
        display: inline-block;
        margin-right: 12px;
      }
      .btn-defalut {
        color: var(--color-vertical-tab-btn);
        &:hover {
          color: var(--color-vertical-hover-tab-btn-hover);
        }
      }
      .btn-disable {
        color: var(--color-vertical-tab-btn-disabled);
      }
      .btn-del-focus {
        color: #757f8a;
        cursor: no-drop;
      }
    }
  }
}
</style>
