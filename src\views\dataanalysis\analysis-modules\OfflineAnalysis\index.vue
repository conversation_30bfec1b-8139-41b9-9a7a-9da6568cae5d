<template>
  <div class="auto-fill">
    <Titlebar :tab-list="tabList" v-bind="$attrs" @on-change="onChangeTab"></Titlebar>
    <component :is="componentName" class="content-view"></component>
  </div>
</template>

<script>
export default {
  name: 'OfflineAnalysis',
  components: {
    Titlebar: require('../../components/title-bar.vue').default,
    AbnormalView: require('./AbnormalView.vue').default,
    InfluenceView: require('./InfluenceView.vue').default,
  },
  props: {},
  data() {
    return {
      activeTab: '1',
      tabList: [
        {
          id: '1',
          label: '异常分析',
          componentName: 'AbnormalView',
        },
        {
          id: '2',
          label: '影响分析',
          componentName: 'InfluenceView',
        },
      ],
      componentName: 'AbnormalView',
    };
  },
  methods: {
    onChangeTab(tabItem) {
      let { id, componentName } = tabItem;
      this.activeTab = id;
      this.componentName = componentName;
    },
  },
};
</script>

<style lang="less" scoped>
.content-view {
  padding: 0 20px;
}
</style>
