<template>
  <div class="searchForm">
    <Form :inline="true" :label-width="75">
      <!-- <FormItem label="身份证号:">
        <Input v-model="formData.idCardNo" placeholder="请输入" maxlength="20" style="width: 220px"></Input>
      </FormItem> -->
      <FormItem label="报警时间:">
        <hl-timerange ref="timerange" :captureTimePeriod="captureTimePeriod" @onceChange="handleTimeChange"
          @change="handleTimeChange" :reflectValue="formData.perceiveDate" :reflectTime="{
            startDate: formData.startDate,
            endDate: formData.endDate,
          }">
        </hl-timerange>
      </FormItem>
      <FormItem prop="vehicleType" label="车辆类型:">
        <Select v-model="formData.vehicleType" clearable filterable placeholder="请选择车辆类型" class="input-200">
          <Option v-for="(item, $index) in vehicleClassTypeList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
        </Select>
      </FormItem>
      <!-- <FormItem label="状态:">
        <Select v-model="formData.associationIdCardStatus" placeholder="请选择" style="width: 220px" clearable>
          <Option :value="item.dataKey" v-for="(item, index) in associationIdCardStatusList" :key="index">{{
            item.dataValue }}</Option>
        </Select>
      </FormItem> -->
    </Form>
    <div class="btn-group ml-20">
      <Button type="primary" @click="searchHandle">查询</Button>
      <Button @click="resetHandle">重置</Button>
    </div>
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex';
  export default {
    name: "",
    data() {
      return {
        formData: {
          perceiveDate: "24小时",
          startDate: "",
          endDate: "",
          idCardNo: "",
          vehicleType: "",
          associationIdCardStatus: undefined,
        },
        captureTimePeriod: [
          { name: "24小时", value: "1" },
          { name: "近7天", value: "2" },
          { name: "近30天", value: "3" },
          { name: "自定义", value: "4" },
        ],
      };
    },
    computed: {
      ...mapGetters({
        associationIdCardStatusList: 'dictionary/getAssociationIdCardStatusList', // 状态
        vehicleClassTypeList: 'dictionary/getVehicleTypeList', //车辆类型
      })
    },
    watch: {},
    async created() {
      await this.getDictData();
      this.searchHandle();
    },
    methods: {
      ...mapActions({
        getDictData: 'dictionary/getDictAllData'
      }),
      searchHandle() {
        this.$emit("search", this.formData);
      },
      resetHandle() {
        this.formData = {
          perceiveDate: "24小时",
          startDate: "",
          endDate: "",
          idCardNo: "",
          vehicleType: "",
          associationIdCardStatus: undefined,
        };
        this.$refs.timerange.clearChecked(false);
      },
      handleTimeChange(obj) {
        this.formData.perceiveDate = obj.timeSlot;
        this.formData.startDate = obj.startDate;
        this.formData.endDate = obj.endDate;
      }
    },
  };
</script>

<style lang='less' scoped>
  .searchForm {
    flex: 1;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #D3D7DE;
    margin-bottom: 20px;
    
    .ivu-form-item {
      margin-bottom: 15px;

      /deep/.ivu-form-item-content {
        height: 34px;
        display: flex;
        align-items: center;
      }
    }
  }
</style>