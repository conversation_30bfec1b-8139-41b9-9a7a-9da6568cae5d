<template>
  <div class="search">
    <div class="inline">
      <ui-label label="组织机构" :width="70" class="inline mr-lg mb-sm">
        <api-organization-tree
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label label="行政区划" :width="70" class="inline mr-lg mb-sm">
        <api-area-tree :select-tree="selectTree" @selectedTree="selectedArea" placeholder="请选择行政区划">
        </api-area-tree>
      </ui-label>
      <ui-label label="设备编码" :width="70" class="inline mr-lg">
        <Input v-model="searchData.deviceId" class="width-lg" clearable placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label label="设备名称" :width="70" class="inline mr-lg">
        <Input v-model="searchData.deviceName" class="width-lg" clearable placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label label="设备状态" :width="70" class="inline mr-lg">
        <Select class="width-md" v-model="searchData.phyStatus" clearable placeholder="请选择设备状态">
          <Option :value="1" label="可用"></Option>
          <Option :value="2" label="不可用"></Option>
        </Select>
      </ui-label>
      <ui-label label="设备类型" :width="70" class="inline mr-lg mb-lg">
        <Select class="width-md" v-model="searchData.isImportant" clearable placeholder="请选择设备类型">
          <Option label="普通设备" :value="0"></Option>
          <Option label="重点设备" :value="1"></Option>
        </Select>
      </ui-label>
      <slot name="day" :searchData="searchData">
        <ui-label label="离线天数" :width="70" class="inline">
          <InputNumber v-model="searchData.startCount" placeholder="请输入开始天数" class="width-xs"></InputNumber>
          <span class="font-blue f-14 ml-xs mr-xs">—</span>
          <InputNumber v-model="searchData.endCount" placeholder="请输入结束天数" class="width-xs"></InputNumber>
          <span class="base-text-color f-14 ml-sm">天</span>
        </ui-label>
      </slot>
    </div>

    <ui-label :width="1" class="inline mb-sm search-button" label="">
      <Button type="primary" @click="search">查询</Button>
      <Button class="ml-lg" @click="resetForm">重置</Button>
    </ui-label>
    <Button type="primary" class="button-export ml-lg" @click="getExport" :loading="exportLoading">
      <i class="icon-font icon-daochu font-white mr-xs"></i>
      <span class="ml-xs">导出</span>
    </Button>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'search-bar',
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
  props: {
    searchData: {},
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      selectTree: {
        regionCode: null,
      },
      exportLoading: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.copySearchDataMx(this.searchData);
  },
  activated() {},
  methods: {
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          ...this.searchData,
        };
        let res = await this.$http.post(
          governanceevaluation.postEvaluationDeviceOnlineStatisticsDayExportList,
          params,
          {
            responseType: 'blob',
          },
        );
        await this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    search() {
      this.$emit('search', this.searchData);
    },
    resetForm() {
      this.resetSearchDataMx(this.searchData, () => {});
      this.selectOrgTree.orgCode = null;
      this.$set(this.searchData, 'orgCode', '');
      this.$emit('search', this.searchData);
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;

  .search-button {
    white-space: nowrap;
  }

  @{_deep} .ivu-input-icon {
    color: var(--color-primary) !important;
  }
}
</style>
