/*
 * @Author: zhengmingming <EMAIL>
 * @Date: 2025-06-20 14:14:05
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-07-15 16:45:08
 * @FilePath: \icbd-viewc:\Users\<USER>\Desktop\项目\ivdg-view\src\mixins\logout-mixin.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import user from '@/config/api/user';
import thirdPartyConfig from '@/config/thirdParty.config';

import { mapActions, mapGetters } from 'vuex';
const mixin = {
  data() {
    return {
      //logOutTimer: null
    };
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
      removeWebsocket: 'websocket/removeWebsocket',
    }),
    async logOutMx() {
      try {
        // 关闭当前窗口
         window.closeAppWindow();
        // await this.$http.delete(user.logout);
        // window.sessionStorage.clear();
        // this.setCacheRouterList([]);
        // this.removeWebsocket();
        // location.href = logoutForPage + window.location.href;
        // 刷新页面 
        // location.reload();
        // this.$router.push({ name: 'login' });
      } catch (err) {
        console.log(err);
      }
    },
  },
  computed: {
    ...mapGetters({
      getInitialStoreState: 'getInitialStoreState',
    }),
  },
};
export default mixin;
