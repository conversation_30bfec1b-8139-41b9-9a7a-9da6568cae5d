<template>
  <div class="filter-tool">
    <div class="block">
      <div class="label">
        <span>亮度</span>
        <span class="num">{{ bright }}%</span>
      </div>
      <input type="range" class="range" min="0" max="100" step="1" v-model="bright" :style="{ backgroundSize: `${bright}% 100%` }" @change="changefilter('bright')" />
    </div>
    <div class="block">
      <div class="label">
        <span>对比度</span>
        <span class="num">{{ contrast }}%</span>
      </div>
      <input type="range" class="range" min="0" max="100" step="1" v-model="contrast" :style="{ backgroundSize: `${contrast}% 100%` }" @change="changefilter('contrast')" />
    </div>
    <div class="block">
      <div class="label">
        <span>饱和度</span>
        <span class="num">{{ saturate }}%</span>
      </div>
      <input type="range" class="range" min="0" max="100" step="1" v-model="saturate" :style="{ backgroundSize: `${saturate}% 100%` }" @change="changefilter('saturate')" />
    </div>
    <div class="block">
      <div class="label">
        <span>色调</span>
        <span class="num">{{ tone }}%</span>
      </div>
      <input type="range" class="range" min="0" max="100" step="1" v-model="tone" :style="{ backgroundSize: `${tone}% 100%` }" @change="changefilter('tone')" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'videoFilterTool',
  props: {
    filterObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      bright: 50,
      contrast: 50,
      saturate: 50,
      tone: 50
    }
  },
  created() {
    this.bright = this.filterObj.bright || 50
    this.contrast = this.filterObj.contrast || 50
    this.saturate = this.filterObj.saturate || 50
    this.tone = this.filterObj.tone || 50
  },
  methods: {
    changefilter(type) {
      this.$emit('changefilter', type, { bright: this.bright, contrast: this.contrast, saturate: this.saturate, tone: this.tone })
    }
  }
}
</script>

<style lang="less" scoped>
.filter-tool {
  width: 110px;
  padding: 10px;
  input[type='range'] {
    -webkit-appearance: none;
    background: -webkit-linear-gradient(#2c86f8, #5ba3ff) no-repeat, #d3d7de;
    background-size: 50% 100%;
    &::-webkit-slider-runnable-track {
      height: 4px;
      border-radius: 2px; /*将轨道设为圆角的*/
    }
    &:focus {
      outline: none; /*原始的控件获取到焦点时，会显示包裹整个控件的边框，所以还需要把边框取消。*/
    }
    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      height: 8px;
      width: 8px;
      margin-top: -2px; /*使滑块超出轨道部分的偏移量相等*/
      background: #ffffff;
      border-radius: 50%; /*外观设置为圆形*/
      border: 1px solid #2c86f8; /*设置边框*/
      cursor: grab;
      &:hover {
        transform: scale(2);
      }
    }
  }
  .block {
    .label {
      color: rgba(255, 255, 255, 0.6);
      display: flex;
      justify-content: space-between;
      margin: 5px 0 2px 0;
    }
    .num {
      color: rgba(255, 255, 255, 1);
    }
    input {
      width: 100%;
    }
  }
}
</style>
