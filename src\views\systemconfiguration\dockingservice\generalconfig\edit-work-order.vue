<template>
  <ui-modal v-model="visible" title="新增工单配置" :styles="styles" @onCancel="onCancel" @query="onQuery">
    <div class="work-order-config-container">
      <Select
        v-model="formData.taskSchemeId"
        placeholder="全量检测任务"
        class="width-lg"
        @on-change="onChangeTask"
      >
        <Option v-for="(item, index) in taskList" :key="index" :value="item.taskSchemeId">{{ item.taskName }} </Option>
      </Select>
      <ui-search-tree
        ref="uiTree"
        class="ui-search-tree"
        no-search
        show-checkbox
        node-key="indexId"
        :tree-data="treeData"
        :default-props="defaultProps"
        :expand-all="true"
        :treeLoading="treeLoading"
        :default-keys="checkedTreeDataList"
        :default-checked-keys="checkedTreeDataList"
        @check-change="handleCheckChange"
      >
      </ui-search-tree>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';

export default {
  name: 'edit-work-order',
  props: {
    value: {},
    //配置
    data: {
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '45%',
      },
      formData: {
        taskSchemeId: '',
      },
      indexList: [],
      taskList: [],
      treeData: [],
      expandAll: [],
      checkedTreeDataList: [],
      defaultProps: {
        label: (data, node) => {
          if (node.level === 1) {
            return data.indexModuleName;
          } else {
            return data.indexName;
          }
        },
        children: 'children',
      },
      configData: [],
      treeLoading: false,
    };
  },
  watch: {
    async value(val) {
      this.visible = val;
      if (val) {
        this.treeData = [];
        this.indexList = [];
        await this.getEvaluationPageList();
        this.setDefaultTask();
        await this.getTaskIndexPageList();
        this.getTreeData();
        await this.$nextTick();
        this.configData = JSON.parse(JSON.stringify(this.data));
        this.getCheckedTreeDataList();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {},
  mounted() {},
  methods: {
    onCancel() {
      this.visible = false;
    },
    onQuery() {
      this.visible = false;
      this.$emit(
        'on-submit',
        this.configData.filter((item) => item.taskSchemeId),
      );
    },
    setDefaultTask() {
      if (!this.taskList.length) return;
      this.formData.taskSchemeId = this.taskList[0]['taskSchemeId'];
    },
    async onChangeTask() {
      this.treeData = [];
      this.indexList = [];
      await this.getTaskIndexPageList();
      this.getTreeData();
      await this.$nextTick();
      this.getCheckedTreeDataList();
    },
    getCheckedTreeDataList() {
      this.checkedTreeDataList = this.configData
        .filter((item) => item.taskSchemeId === this.formData.taskSchemeId)
        .map((item) => item.indexId);
    },
    async getEvaluationPageList() {
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, {
          pageNumber: 1,
          pageSize: 1000,
        });
        this.taskList = data.entities || [];
      } catch (err) {
        console.log(err);
      }
    },
    async getTaskIndexPageList() {
      try {
        this.treeLoading = true;
        let params = {
          taskSchemeId: this.formData.taskSchemeId,
          pageNumber: 1,
          pageSize: 1000,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskIndexPageListByWork, params);
        this.indexList = data.entities || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.treeLoading = false;
      }
    },
    getTreeData() {
      this.indexList.forEach((item) => {
        const index = this.treeData.findIndex((value) => value.indexModule === item.indexModule);
        if (index !== -1) {
          this.treeData[index]['children'].push(item);
        } else {
          this.treeData.push({
            id: item.indexModule,
            indexModule: item.indexModule,
            indexModuleName: item.indexModuleName,
            children: [item],
          });
        }
      });
    },
    handleCheckChange(data, checked) {
      const { taskSchemeId, taskName, indexId, indexName } = data;
      const index = this.configData.findIndex(
        (item) => item.taskSchemeId === this.formData.taskSchemeId && item.indexId === data.indexId,
      );
      if (checked && index === -1) {
        this.configData.push({
          taskSchemeId,
          taskName,
          indexId,
          indexName,
        });
      }
      if (!checked && index !== -1) {
        this.configData.splice(index, 1);
      }
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree').default,
  },
};
</script>
<style lang="less" scoped>
.work-order-config-container {
  border: 1px solid var(--border-color);
  padding: 20px;
  @{_deep} .ui-search-tree {
    height: 500px;
  }
}
</style>
