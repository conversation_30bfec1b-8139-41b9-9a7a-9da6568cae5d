<template>
  <div class="alarm" :class="'border'+alarmInfo.bgIndex" @click="$emit('detailFn' ,alarmInfo)">
    <div class="top" :class="'bg'+alarmInfo.bgIndex">
      <div>
        <!-- <ui-btn-tip class="collection-icon" content="取消收藏" icon="icon-jingqing" /> -->
        <span v-if="alarmInfo.taskLevel == 1">一级警报</span>
        <span v-else-if="alarmInfo.taskLevel == 2">二级警报</span>
        <span v-else>三级警报</span>
      </div>
      <div class="allDel" @click.stop="allDel()">一键关闭</div>
      <i class="ivu-icon ivu-icon-ios-close ivu-tabs-close" @click.stop="$emit('delAlarm')"></i>
    </div>
    <div class="contrast">
      <div class="block border">
                <div class="desc">报警照片</div>
        <ui-image :src="alarmInfo.traitImg"></ui-image>
        <!-- <ui-image src="@/assets/img/target/one-level.png"></ui-image> -->
      </div>
      <div class="block">
        <!-- <ui-image :src="round"></ui-image> -->
        <!-- <ui-image class="animation" :src="'c'+alarmInfo.bgIndex"></ui-image> -->
        <ui-image v-if="alarmInfo.bgIndex == 1" class="animation" :src="c1"></ui-image>
        <ui-image v-else-if="alarmInfo.bgIndex == 2" class="animation" :src="c2"></ui-image>
        <ui-image v-else-if="alarmInfo.bgIndex == 3" class="animation" :src="c3"></ui-image>
        <ui-image v-else-if="alarmInfo.bgIndex == 4" class="animation" :src="c4"></ui-image>
        <ui-image v-else class="animation" :src="c5"></ui-image>
        <div class="num"
          :class="{
            'c1': alarmInfo.bgIndex == 1,
            'c2': alarmInfo.bgIndex == 2,
            'c3': alarmInfo.bgIndex == 3,
            'c4': alarmInfo.bgIndex == 4,
            'c5': alarmInfo.bgIndex == 5,
          }"  
          >{{(alarmInfo.simScore.toFixed(4)*100).toString().substring(0,5)}}%</div>
      </div>
      <div class="block border">
                <div class="desc">布控照片</div>
        <ui-image :src="alarmInfo.photoUrl"></ui-image>
      </div>
    </div>
    <div class="info">
      <div class="left">
        <!-- <div class="p" v-for="item in 6">
          <div class="title">布控目标：</div>
          <div class="val">布控人员</div>
        </div> -->
        <div class="line">
          <div class="name">{{ alarmInfo.name }}</div>
          <div class="lib">（{{ alarmInfo.taskType == '1' ? '单体布控' : alarmInfo.libName }}）</div>
          <!-- <div class="title">布控目标:</div>
          <div class="val">{{ alarmInfo.name }}</div> -->
        </div>
        <!-- <div class="p">
          <div class="title">布控来源:</div>
          <div class="val">{{ alarmInfo.taskType == '1' ? '单体布控' : '库布控' }}</div>
        </div> -->
        <div class="p">
          <div class="title">报警时间:</div>
          <div class="val">{{ alarmInfo.alarmTime }}</div>
        </div>
        <div class="p">
          <div class="title">报警设备:</div>
          <div class="val">{{ alarmInfo.deviceName }}</div>
        </div>
        <!-- <div class="p">
          <div class="title">所属任务:</div>
          <div class="val">{{ alarmInfo.taskName }}</div>
        </div> -->
      </div>
    </div>
    <!-- <div class="detail" @click="$emit('detailFn' ,alarmInfo)">查看详情</div> -->
    
    
  </div>
</template>
<script>
import round from '@/assets/img/target/round.png'
import valid from '@/assets/img/target/valid.png'
import c1 from '@/assets/img/target/c-one.png'
import c2 from '@/assets/img/target/c-two.png'
import c3 from '@/assets/img/target/c-three.png'
import c4 from '@/assets/img/target/c-four.png'
import c5 from '@/assets/img/target/c-five.png'
  export default {
    props: {
      alarmInfo: {
        type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        c1, c2, c3, c4, c5,
        single: false,
        round,
        valid,
      }
    },
    computed: {},
    activated() {},
    mounted() {},
    methods: {
      collection() {

      },
      cardBox (e) {
        console.log('---------------', e)
      },
      allDel() {
        this.$emit('allDel')
      }
    }
  }
</script>
<style lang="less" scoped>
  .alarm {
    width: 380px;
    height: 240px;
    position: fixed;
    z-index: 9;
    // bottom: 20px;
    // right: 20px;
    background: #fff;
    box-shadow: 0 1px 3px #d9d9d9;
    overflow: hidden;
    border-radius: 3px;
    right: 14px;
    bottom: 14px;
    
    .top {
      position: relative;
      display: flex;
      justify-content: space-between;
      padding: 0 0 0 20px;
      height: 40px;
      line-height: 40px;
      margin-bottom: 12px;
      color: #fff;
      .level {
        position: absolute;
        left: 50%;
        margin-left: -46px;
      }
      .level {
        display: flex;
        justify-content: space-between;
        // height: 30px;
      }
    }
    
    .bg1{
      background: linear-gradient(210deg, #FF7B56 0%, #EA4A36 100%);
    }
    .bg2{
      background: linear-gradient(210deg, #FFAF65 0%, #FC770B 100%);
    }
    .bg3{
      background: linear-gradient(210deg, #FFD752 0%, #FFC300 100%);
    }
    .bg4{
      background: linear-gradient(262deg, #27D676 8%, #36BE7F 89%);
    }
    .bg5{
      background: linear-gradient(263deg, #5BCAFF 2%, #2C86F8 97%);
    }

    .contrast {
      height: 100px;
      display: flex;
      justify-content: space-between;
      padding: 0px 30px 0;
      .block {
        position: relative;
        width: 100px;
        .num {
          position: absolute;
          width: 100%;
          height: 100%;
          top: 0;
          left: 0;
          align-items: center;
          display: flex;
          justify-content: center;
          color: #2c86f8;
        }
        .desc {
              position: absolute;
              z-index: 9;
              background: rgba(0,0,0,0.5);
              color: #fff;
              padding: 0 6px;
            }
        .c1{
          color: #EA4A36;
        }
        .c2{
          color: #E77811;
        }
        .c3{
          color: #EE9F00;
        }
        .c4{
          color: #36BE7F;
        }
        .c5{
          color: #2C86F8;
        }
      }
      .border {
        border: 1px solid #ebebeb;
      }
    }

    .info {
      display: flex;
      padding: 12px 30px;
      .left {
        flex: 1;
        width: 0;
        .p {
          display: flex;
          .title {
            color: #999;
            margin-right: 10px;
            line-height: 22px;
          }
          .val {
            flex: 1;
            overflow: hidden;    
            text-overflow:ellipsis;    
            white-space: nowrap;
          }
        }
        .line{
          display: flex;
          line-height: 24px;
          .name {
            color: #191919;
            font-size: 14px;
            font-weight: 600;
          }
          .lib{
            color: #8C8C8C;
            font-size: 12px;
          }
        }
      }
     
    }

    .detail {
      padding: 0 30px;
      color: #2C86F8;
      cursor: pointer;
    }
  }
  .border1 {
    border: 1px solid #EA4A36;
  }
  .border2 {
    border: 1px solid #FC770B;
  }
  .border3 {
    border: 1px solid #FFC300;
  }
  .border4 {
    border: 1px solid #36BE7F;
  }
  .border5 {
    border: 1px solid #2C86F8;
  }

  .collection-icon {
    /deep/ .iconfont {
        font-size: 14px;
        color: #fff;
    }
    .ivu-tooltip-rel {
        // margin-top: 3px;
    }
    /deep/ .icon-shoucang {
        color: #f29f4c !important;
        text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
        color: #f29f4c !important;
    }
}

</style>
<style lang="less">
  .ivu-poptip-popper {
    width: 450px !important;
    .block {
      width: 3px;
      background: #2c86f8;
      height: 16px;
      float: left;
      margin-top: 3px;
      margin-right: 6px;
    }
  }

  .ivu-timeline-item {
    .timeContent {
      display: flex;
      justify-content: space-between;
    }
    .content1 {
      .p {
        display: flex;
        margin-top: 10px;
        span {
          width: 80px;
        }
        div {
          flex: 1;
        }
      }
    }
  }

/deep/ .ivu-checkbox-input {
  z-index: 999;
}

.allDel {
  position: absolute;
  right: 50px;
  cursor: pointer;
}

</style>