<template>
    <lineEchart :title="{}" :legend="legend" :grid="grid" :xAxis="xAxis" :yAxis="yAxis" :series="series"></lineEchart>
</template>
<script>
import * as echarts from 'echarts'
import lineEchart from '@/components/echarts/line-echart'
export default {
    components: { lineEchart },
    props: {
        alarmData: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    data() {
        return {
            legend: {
                show: false
            },
            grid: {
                left: '0',
                top: '5%',
                right: '0.1%',
                bottom: '0',
                containLabel: true
            },
            xAxis: {
                data: ['1', '2', '3', '4', '5'],
                axisLine: {
                    lineStyle: {
                        color: '#D3D7DE'
                    }
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.35)'
                },
                splitLine: {
                    show: false,
                    lineStyle: {
                        type: 'dashed',
                        color: '#D3D7DE'
                    }
                }
            },
            yAxis: {
                axisLabel: {
                    color: 'rgba(0, 0, 0, 0.35)'
                },
                splitLine: {
                    lineStyle: {
                        type: 'solid',
                        color: '#D3D7DE'
                    }
                }
            },
            series: [
                {
                    type: 'line',
                    smooth: true,
                    showAllSymbol: true,
                    symbol: 'circle',
                    symbolSize: 12,
                    lineStyle: {
                        normal: {
                            color: '#2C86F8',
                        },
                    },
                    itemStyle: {
                        color: '#2C86F8',
                        borderWidth: 3,
                        borderColor: '#f3f3f3',
                    },
                    data: [0, 0, 0, 0, 0, 0, 0]
                }
            ]
        }
    },
    mounted() {},
    methods: {
        init(list) {
            let dateList = list.map(item => {
                return item.date
            })
            let count = list.map(item => {
                return item.count
            })
            this.xAxis.data = dateList;
            this.series[0].data = count
        }
    }
}
</script>
<style lang="less" scoped></style>
