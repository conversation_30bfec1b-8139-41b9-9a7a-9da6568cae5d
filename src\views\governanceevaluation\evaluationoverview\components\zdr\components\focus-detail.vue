<template>
  <!-- 轨迹重复率 全部轨迹 -->
  <ui-modal v-model="modalShow" title="查看全部轨迹" :footerHide="true" width="55%">
    <div class="f-16 base-text-color mb-sm">
      <span class="color-filter">相同轨迹数量 </span>{{ pageData.totalCount }}
      <span class="color-filter ml-lg">轨迹小图URL </span>{{ currentItem.trackImage }}
    </div>
    <ui-table
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="500"
      v-ui-loading="{ loading, tableData }"
    >
      <template #trackImage="{ row }">
        <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
          <ui-image :src="row.trackImage" />
        </div>
      </template>
      <template #identityPhoto="{ row }">
        <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
          <ui-image :src="row.identityPhoto" />
        </div>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>

    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';
export default {
  name: 'focusDetail',
  props: {
    paramsList: {},
  },
  data() {
    return {
      bigPictureShow: false,
      modalShow: false,
      tableData: [],
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage', tooltip: true, minWidth: 100 },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '档案照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          tooltip: true,
          minWidth: 150,
        },
        { title: '姓名', key: 'name', tooltip: true, minWidth: 150 },
        { title: '证件号', key: 'idCard', tooltip: true, minWidth: 150 },
      ],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      currentItem: {},
    };
  },
  methods: {
    show(item) {
      this.currentItem = item;
      this.modalShow = true;
      this.infoList();
    },
    async infoList() {
      try {
        this.loading = true;
        let { batchId, trackImage } = this.currentItem;
        let { indexId, displayType } = this.paramsList;
        let { pageNum, pageSize } = this.pageData;
        let params = {
          pageNumber: pageNum,
          pageSize,
          indexId,
          batchId,
          displayType,
          customParameters: {
            trackImage,
          },
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getPopUpData, params);
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.infoList();
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    LookScene: require('@/components/look-scene').default,
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  //height: 500px;
}
.ui-images {
  width: 56px !important;
  height: 56px !important;
}
.color-filter {
  color: rgba(43, 132, 226, 1);
  vertical-align: middle;
}
</style>
