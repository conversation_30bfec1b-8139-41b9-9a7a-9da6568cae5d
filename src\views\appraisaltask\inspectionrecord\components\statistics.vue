<template>
  <div class="filter">
    <div class="left-container">
      <span class="f-16 col-blue vt-middle">{{ $parent.currentTree.name }}</span>
      <!--  ++ 检测概况不显示时间    -->
      <span v-if="![201, 301, 401].includes($parent.currentTree.id)">
        <span class="icon-font icon-shijian f-12 col-gay vt-middle ml-lg"></span>
        <span class="f-12 col-gay vt-middle ml-xs">最新检测时间： {{ taskObj.recentlyTime || '暂无数据' }}</span></span
      >
    </div>
    <div class="right-container">
      <ui-label label="检测任务" :width="70" class="fl">
        <Select class="width-260" v-model="task" @on-change="handleChangeTask" filterable placeholder="请选择检测任务">
          <Option v-for="(item, index) in optionList" :value="item.taskSchemeId" :key="index">{{
            item.taskName
          }}</Option>
        </Select>
      </ui-label>
      <!-- 暂时做不了     <ui-label :width="0" label=" " class="fr">
        <Button type="primary" class="fr ml-lg">
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle"> </i>
          <span class="vt-middle ">数据导出</span>
        </Button>
      </ui-label>-->
    </div>
  </div>
</template>

<script>
export default {
  name: 'statistics',
  props: {
    data: {},
  },
  data() {
    return {
      task: '',
      optionList: [],
      taskObj: {},
    };
  },
  methods: {
    handleChangeTask(val) {
      this.taskObj = this.optionList.find((item) => item.taskSchemeId === val) || {};
      this.$emit('on-statistics-change', this.taskObj);
    },
  },
  watch: {
    data: {
      immediate: true,
      handler: function (val) {
        this.optionList = val;
        this.$nextTick(() => {
          if (val.length) {
            this.taskObj = val[0];
            this.task = val[0].taskSchemeId;
            this.$emit('on-statistics-change', [this.taskObj, this.taskObj]);
          } else {
            this.task = '';
          }
        });
      },
      deep: true,
    },
  },
};
</script>

<style lang="less" scoped>
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 53px;
}

.col-blue {
  color: var(--color-primary);
}

.col-gay {
  color: #8797ac;
}

.width-260 {
  width: 260px;
}
</style>
