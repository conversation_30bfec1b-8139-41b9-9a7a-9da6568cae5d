<template>
  <div>
    <div class="search-header">
      <div class="tab">
        <div
          v-for="(item, index) in deviceTypeList"
          :key="index"
          :class="['tab-item', active === item.value ? 'active' : '']"
          @click="changeType(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="btn-list">
        <Button type="default" class="cyan-btn" @click="addDevice">
          <i class="icon-font icon-xinzengshebei-01"></i>
          <span class="ml-xs">新增设备</span>
        </Button>
        <Upload
          action="/ivdg-asset-app/assert/device/fillIn/import"
          ref="upload"
          class="inline"
          :show-upload-list="false"
          :headers="headers"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
        >
          <Button type="default" class="cyan-btn ml-sm" :loading="importLoading">
            <i class="icon-font icon-piliangdaoru-01 f-14"></i>
            <span class="vt-middle ml-sm">批量导入</span>
          </Button>
        </Upload>
        <Button type="text" class="ml-sm" @click="exportModule">
          <span class="link">下载模板</span>
        </Button>
        <span
          v-show="active === 'pendingStorage'"
          class="ml-sm line"
          v-permission="{
            route: 'assetfill',
            permission: 'updateConfig',
          }"
        ></span>
        <Button
          type="text"
          class="ml-sm"
          v-show="active === 'pendingStorage'"
          @click="paramsConfig"
          v-permission="{
            route: 'assetfill',
            permission: 'updateConfig',
          }"
        >
          <i class="icon-font icon-canshupeizhi"></i>
          <span class="ml-xs inline vt-middle link">参数配置</span>
        </Button>
      </div>
      <div class="status over-flow mt-sm">
        <div class="fl tag-change" v-show="active === 'pendingStorage'">
          <tag-view :list="tagList" @tagChange="changeStatus" ref="tagView"></tag-view>
          <Tooltip class="ml-sm" placement="right-start" :max-width="480">
            <i class="icon-font icon-wenhao"></i>
            <template #content>
              <div class="mb-mini">
                <span class="tip">【新增设备】</span>
                <span>：新增或者导入资产库不存在的设备。</span>
              </div>
              <div class="mb-mini">
                <span class="tip">【修订设备】</span>
                <span>：对资产库的设备进行编辑，或者导入资产库已有设备。</span>
              </div>
              <div class="mb-mini">
                <span class="tip">【根据流水自动新增设备】</span>
                <span>：系统实时监测抓拍流水，自动发现有流水但未注册到资产库的设备。</span>
              </div>
              <div class="mb-mini">
                <span class="tip">【根据流水自动修改功能类型】</span>
                <span>
                  ：系统实时监测抓拍流水，有人脸抓拍数据的设备，自动追加【人脸识别】功能类型；有车辆抓拍数据的设备，自动追加【车辆识别】功能类型。
                </span>
              </div>
            </template>
          </Tooltip>
        </div>

        <div class="fr status-num">
          <div class="inline">
            <i class="icon-font icon-hege"></i>
            <span class="inline vt-middle">
              <span class="ml-xs">合格：</span>
              <span class="qualified">{{ statistic.qualified | formatNum }}</span>
            </span>
          </div>
          <div class="inline ml-lg">
            <i class="icon-font icon-buhege1"></i>
            <span class="inline vt-middle">
              <span class="ml-xs">不合格：</span>
              <span class="unqualified">{{ statistic.unqualified | formatNum }}</span>
            </span>
          </div>
        </div>
      </div>

      <div class="search-content mt-sm">
        <ui-label class="inline mr-lg" label="组织机构">
          <api-organization-tree
            ref="apiOrgTree"
            :custorm-node="true"
            :custorm-node-data="custormNodeData"
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline mr-lg" label="行政区划">
          <api-area-tree
            ref="apiAreaTree"
            :select-tree="selectTree"
            :custorm-node="true"
            :custorm-node-data="custormAreaNodeData"
            @selectedTree="selectedArea"
            placeholder="请选择行政区划"
          ></api-area-tree>
        </ui-label>
        <slot></slot>
        <div class="inline">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="reset">重置</Button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    active: {
      type: String,
      default: 'pendingStorage',
    },
    deviceTypeList: {
      type: Array,
      defualt: [],
    },
    deviceStatusList: {
      type: Array,
      defualt: [],
    },
    statistic: {
      type: Object,
      default: () => {
        return {
          qualified: 0,
          unqualified: 0,
        };
      },
    },
  },
  data() {
    return {
      importLoading: false,
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
        {
          label: '组织机构匹配异常',
          orgCode: '-2',
        },
      ],
      custormAreaNodeData: [
        {
          label: '未分配行政区划',
          regionCode: '-1',
        },
        {
          label: '行政区划匹配异常',
          regionCode: '-2',
        },
      ],
      tagList: [],
      selectTree: {
        regionCode: '',
      },
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      searchData: {
        orgCode: '',
        civilCode: '',
      },
      insertExist: 0,
      continueLoading: false,
    };
  },
  created() {
    this.tagList = this.deviceStatusList.map((row) => row.label);
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.getAlldicData();
  },
  mounted() {
    this.search();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    changeType(item) {
      this.$emit('changeType', item);
      this.$nextTick(() => {
        this.reset();
      });
    },
    changeStatus(index) {
      this.$emit('changeStatus', index);
      this.search();
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        this.$Message.success(res.msg);
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else if (res.code === 9006402) {
        this.$UiConfirm({
          loading: this.continueLoading,
          render: () => {
            return (
              <div class="mt-sm">
                <i class="icon-font icon-jinggao warning"></i>
                <span class="content ml-xs inline vt-middle">
                  提示：有{res.data.failCount}
                  条设备正在审核入库，是否继续导入？
                </span>
                <Button
                  type="text"
                  {...{
                    on: {
                      click: async () => {
                        const downloadRes = await this.$http.get(equipmentassets.downloadExistUpload, {
                          params: {
                            redisKey: res.data.existRedisKey,
                          },
                          responseType: 'blob',
                        });
                        this.$util.common.exportfile(downloadRes);
                      },
                    },
                  }}
                >
                  查看相同设备
                </Button>
                <div class="mb-sm mt-sm">
                  <RadioGroup vModel={this.insertExist}>
                    <Radio label={0}>舍弃</Radio>
                    <Radio label={1}>导入</Radio>
                  </RadioGroup>
                </div>
                <div class="font-D66418">备注：继续导入将覆盖已有设备数据！</div>
              </div>
            );
          },
          title: '警告',
        })
          .then(() => {
            this.uploadSave(res);
          })
          .catch((res) => {
            console.log(res);
          });
      } else {
        console.log(res, 'res');
        this.$Message.error(res.msg);
      }
    },
    dealError(error) {
      this.$emit('dealError', error);
    },
    async uploadSave(res) {
      try {
        this.continueLoading = true;
        const saveRes = await this.$http.post(equipmentassets.uploadSave, {
          insertExist: this.insertExist,
          existRedisKey: res.data.existRedisKey,
          insertRedisKey: res.data.insertRedisKey,
        });
        this.$Message.success(saveRes.data.msg);
        this.search();
      } catch (err) {
        console.log(err);
      } finally {
        this.continueLoading = false;
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    search() {
      this.copySearchDataMx(this.searchData);
      this.$emit('search');
    },
    reset() {
      this.$refs.tagView.curTag = 0;
      this.$refs.apiOrgTree.reset();
      this.$refs.apiAreaTree.reset();
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.resetSearchDataMx(this.searchData, this.search);
      this.$emit('reset');
    },
    addDevice() {
      this.$emit('addDevice');
    },
    paramsConfig() {
      this.$emit('paramsConfig');
    },
    exportModule() {
      this.$emit('exportModule');
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .search-header {
    .btn-list {
      .cyan-btn {
        background: #fff;
        border: 1px solid var(--color-primary);
        color: var(--color-primary);
      }
      .link {
        color: var(--color-primary);
      }
      .icon-canshupeizhi {
        color: var(--color-primary);
      }
    }
  }
}
.modal {
  .warning {
    color: var(--color-warning);
  }
  .content {
    color: var(--color-content);
  }
}
.search-header {
  margin: 10px 20px;
  position: relative;
  display: inline-block;
  width: calc(100% - 40px);
  .tab {
    display: flex;
    border-bottom: 1px solid var(--devider-line);
    margin-top: 10px;

    .tab-item {
      padding-top: 3px;
      cursor: pointer;
      width: 110px;
      height: 34px;
      line-height: 34px;
      color: var(--color-switch-tab);
      font-size: 14px;
      position: relative;
      text-align: center;
      &.active {
        color: var(--color-switch-tab-active);
        border-left: 1px solid var(--devider-line);
        border-right: 1px solid var(--devider-line);
        &::before {
          position: absolute;
          left: 0;
          top: 0;
          content: '';
          display: inline-block;
          width: 100%;
          height: 3px;
          background-color: var(--color-switch-tab-active);
        }
        &::after {
          position: absolute;
          left: 0;
          bottom: -1px;
          content: '';
          display: inline-block;
          width: 100%;
          height: 1px;
          background-color: var(--bg-content);
        }
      }
    }
  }
  .btn-list {
    position: absolute;
    right: 0;
    top: 0;
    .cyan-btn {
      background: #02162b;
      border: 1px solid #174f98;
      color: var(--color-bluish-green-text);
    }
    .line {
      height: 18px;
      width: 2px;
      display: inline-block;
      background-color: var(--devider-line);
      vertical-align: middle;
    }
    .link {
      text-decoration: underline;
    }
  }
  .status {
    width: 100%;
    height: 35px;
  }
  .status-num {
    color: var(--color-input);
  }
  .tag-change {
    display: flex;
    align-items: center;
    .tip {
      color: var(--color-primary);
    }
    .icon-wenhao {
      background-image: linear-gradient(to bottom, #f58d3d, #b8580d);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    @{_deep} .ivu-tooltip-inner {
      max-width: none;
    }
  }
  .icon-hege {
    background-image: linear-gradient(to bottom, #3ac53a, var(--color-success));
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-buhege1 {
    background-image: linear-gradient(to bottom, #e56542, var(--color-warning));
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .qualified {
    color: var(--color-success);
  }
  .unqualified {
    color: var(--color-warning);
  }
  .search-content {
    @{_deep}.select-width {
      width: 200px;
    }
  }
}
</style>
