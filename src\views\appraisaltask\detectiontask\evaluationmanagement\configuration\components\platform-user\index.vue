<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
export default {
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        // 分布式身份确认接口稳定性
        case 'DISTRIBUTED_IDENTITY_API_STABILITY':
          return 'identityconfirmation';
        case 'PORTRAIT_TRACK_API_STABILITY':
          return 'portraittrack';
        case 'VEHICLE_TRACK_API_STABILITY':
          return 'vehicletrack';
        case 'FACE_MONITOR_API_STABILITY':
          return 'facecontrol';
        case 'VEHICLE_MONITOR_API_STABILITY':
          return 'vehiclecontrol';
        case 'VIDEO_PLATFORM_ONLINE_RATE':
          return 'platformonline';
        default:
          return 'original';
      }
    },
  },
  components: {
    Original: require('./original.vue').default,
    identityconfirmation: require('./interfacestability/identityconfirmation').default,
    portraittrack: require('./interfacestability/portraittrack').default,
    vehicletrack: require('./interfacestability/vehicletrack').default,
    facecontrol: require('./interfacestability/facecontrol').default,
    vehiclecontrol: require('./interfacestability/vehiclecontrol').default,
    platformonline: require('./interfacestability/platformonline').default,
  },
};
</script>
<style lang="less" scoped></style>
