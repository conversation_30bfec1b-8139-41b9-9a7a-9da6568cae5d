/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-07-08 09:51:55
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-14 15:38:58
 * @Description: 场所档案相关接口
 */
import request from "@/libs/request";
import { holographicArchives, service } from "./Microservice";

// 场所档案列表
export function getPlaceArchivesListAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/pageList`,
    method: "post",
    data,
  });
}

// 获取区县
export function getCityByParentCodeAPI(params) {
  return request({
    url: `${holographicArchives}/placeArchive/region/queryByPcodeForList`,
    method: "get",
    params,
  });
}
// 获取父级区县市省
export function queryReginNameforCode(params) {
  return request({
    url: `${service}/system/region/queryReginNameforCode`,
    method: "get",
    params,
  });
}

// 获取场所档案详情
export function getPlaceArchiveDetailInfoAPI(id) {
  return request({
    url: `${holographicArchives}/placeArchive/view/${id}`,
    method: "get",
  });
}

// 获取场所设备
export function getDevicesInPlaceAPI(id) {
  return request({
    url: `${holographicArchives}/placeArchive/getDeviceById/${id}`,
    method: "get",
  });
}

// 人员统计
export function getPersonParseAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/getPersonCount`,
    method: "post",
    data,
  });
}

// 车辆统计
export function getVehicleParseAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/getCarCount`,
    method: "post",
    data,
  });
}

// 抓拍 - 月
export function getCaptureParseMonthAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/queryDeviceOfPlaceSnapAnalysisMonth`,
    method: "POST",
    data: data,
  });
}
// 抓拍 - 天
export function getCaptureParseDayAPI(data) {
  return request({
    url: `${holographicArchives}/placeArchive/queryDeviceOfPlaceSnapAnalysisDay`,
    method: "POST",
    data: data,
  });
}

// 场所类型新增
export function placeTypeAdd(data) {
  return request({
    url: `${holographicArchives}/place/type/add`,
    method: "POST",
    data: data,
  });
}

// 查询场所列表
export function getPlaceTypeList(data) {
  return request({
    url: `${holographicArchives}/place/type/list`,
    method: "POST",
    data: data,
  });
}

// 场所类型删除
export function deletePlaceType(ids) {
  return request({
    url: `${holographicArchives}/place/type/remove/${ids}`,
    method: "DELETE",
  });
}

// 场所类型编辑
export function updatePlaceType(data) {
  return request({
    url: `${holographicArchives}/place/type/update`,
    method: "PUT",
    data,
  });
}
