<template>
<div class="areaconfig-manger">
    <div class="areaconfig-mange-container">
        <!-- 左侧资源列表 -->
        <div class="left-resource">
            <div class="search-top">
              <Input placeholder="请输入" v-model="searchInput" clearable @keydown.enter.native="refreshAreaList" @on-clear="refreshAreaList">
              <template #append>
                <Button icon="ios-search" @click.prevent="refreshAreaList"></Button>
              </template>
              </Input>
            </div>
            <div class="total-add">
                <span>共<span class="total">{{pageParams.totalCount || 0}}</span>个区域</span>
                <Button type="primary" size="small" @click="handleAdd">框选</Button>
            </div>
            <div v-scroll class="ignorelist-wrap" v-show="dataList.length > 0">
                <li v-for="(item,index) in dataList" :key="index" @click="handleDetail(item)" class="ignorelist" :class="{'active':current.id===item.id}">
                    <img :src="item.coverUrl" v-viewer @click.stop=""/>
                    <div class="right ml-5">
                      <div class="content">
                        <span class="ignore-name ellipsis" :title="item.name">{{item.name}} </span>
                      </div>
                      <span class="toolbar">
                        <i class="iconfont icon-shanchu" title="删除区域" @click.stop="handleDel(item)"/>
                      </span>
                    </div>
                </li>
            </div>
            <div class="empty" v-show="dataList.length < 1">暂无数据</div>
            <Page size="small" v-show="dataList.length > 0" :total="pageParams.totalCount" :current="pageParams.pageNumber" :page-size="pageParams.pageSize" transfer class="page" @on-change="pageChange" />
        </div>
        <div class="main-wrap">
            <!-- 地图模式 -->
            <mapBase ref="mapBase" 
              :disableScroll="false" 
              :mapLayerConfig="{ ...mapLayerConfig }" 
              frameSelectionResultName="AreaFrameSelectionResult"
              :siteListFlat="siteListFlat" 
              @closeMapTool="mapToolVisibleHandlebar"
              @clear="current={}"
              @refreshAreaList="refreshAreaList"/>
        </div>
    </div>
</div>
</template>

<script>
import mapBase from '@/components/map/index.vue'
import { mapGetters } from 'vuex'
import { getSimpleDeviceList } from '@/api/operationsOnTheMap'
import { regionalScopeList, delRegionalScope } from '@/api/config'

export default {
    name: "areaconfig",
    components: {
        mapBase
    },
    data() {
        return {
            searchInput: '',
            dataList: [], //列表
            current: {},
            siteListFlat: [],
            // 地图配置信息
            mapLayerConfig: {
              mapToolVisible: false, // 底部框选操作栏
            },
            pageParams: {
                pageSize: 10,
                pageNumber: 1,
                totalCount: 0
            }
        }
    },
    computed: {
      ...mapGetters({
				userInfo: "userInfo"
			}),
    },
    created() {
      this.getMapLayerByTypeSite()
      this.queryList()
		},
    methods: {
        // 撒点数据
        async getMapLayerByTypeSite() {
          this.siteListFlat = [];
          let roleParam = {roleId: this.userInfo.roleVoList[0].id, filter: this.userInfo.roleVoList[0].initFlag == '1' ? false : true, socialResources: 1, excludeDeviceTypes: [1, 2]}
          let { data } = await getSimpleDeviceList(roleParam)
          this.siteListFlat = [...data]
        },
        refreshAreaList() {
          this.pageParams = {
              pageSize: 10,
              pageNumber: 1,
              totalCount: 0
          }
          this.queryList()
        },
        queryList() {
          regionalScopeList({name: this.searchInput, pageNumber: this.pageParams.pageNumber, pageSize: this.pageParams.pageSize}).then(res => {
            this.dataList = res.data.entities.map(v => {
              v.deviceParam = v.deviceParam.trim() ? JSON.parse(v.deviceParam) : []
              return v
            })
            this.pageParams.totalCount = res.data.total
          })
        },
        pageChange(val) {
            if(this.pageParams.pageNumber == val) return;
            this.pageParams.pageNumber = val;
            this.queryList();
        },
        // 控制底部工具栏显示与隐藏
        mapToolVisibleHandlebar(val) {
          this.$refs['mapBase'].cancelMeasure();
          this.mapLayerConfig = {
            ...this.mapLayerConfig,
            mapToolVisible: !!val,
            selectionResult: !!val
          }
        },
        handleAdd() {
          this.current = {}
          this.$refs.mapBase.clearDraw()
          this.$refs.mapBase.clearSelectDraw()
          this.mapToolVisibleHandlebar(true)

        },
        handleDel(record) {
          this.$Modal.confirm({
            title: '提示',
            closable: true,
            content: `确定要删除该区域吗？`,
            onOk: () => {
              delRegionalScope(record.id).then(res => {
                this.refreshAreaList()
              })
            }
          })
        },
        handleDetail(record) {
          this.current = record
          this.$refs.mapBase.showSelectDraw(record, false)
        }
    }
};
</script>

<style lang="less">
.areaconfig-manger {
    height: 100%;
    width: 100%;
    position: relative;
}
.areaconfig-mange-container {
    display: flex;
    height: 100%;

    .left-resource {
        width: 350px;
        height: 100%;
        padding: 10px;
        background: #ffffff;
        position: relative;
        margin-right: 10px;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        flex-shrink: 0;
        .search-top {
          margin-bottom: 10px;
          .ivu-btn-default .ivu-icon, .ivu-btn-dashed .ivu-icon{
            color: #ffffff;
          }
        }
        .empty {
            text-align: center;
            margin-top: 50px;
            color: #b1b5bf;
        }
    }

    .total-add {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px 10px;

        .total {
            color: #3f73f7;
            margin: 0 2px;
        }
    }
    .ignorelist-wrap {
        margin-bottom: 10px;
        width: 100%;
        height: calc(~'100% - 115px');
    }
    .right {
      height: 64px;
      flex: 1;
      .content {
        width: calc(~'100% - 30px');
        .ignore-name {
            max-width: 100%;
            display: inline-block;
            vertical-align: middle;
            font-size: 13px;
            font-weight: bold;
        }
      }
    }
    .ignorelist {
        padding-right: 5px;
        display: flex;
        align-items: center;
        img{
          width: 100px;
          height: 64px;
        }
    }
    .main-wrap {
        position: relative;
        flex:1 1 auto;
        background: #ffffff;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
    }

    li {
        position: relative;
        cursor: pointer;
        border-bottom: 1px dashed rgba(167,172,184,0.3);
        padding: 5px;
    }

    li:hover {
        background: rgba(70,121,250,0.1);
        color: #666666;
        .toolbar {
            display: inline-block;
        }
    }

    li.active {
        background: rgba(70,121,250,0.1);
    }

    .toolbar {
        display: inline-block;
        position: absolute;
        top: 0;
        right: 8px;
        display: none;
        i {
          color: #2C86F8;
          margin: 0 5px;
        }
    }
    .ellipsis {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
}
</style>
