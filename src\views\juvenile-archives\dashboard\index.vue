<template>
  <section class="content">
    <div class="content-left">
      <ui-card title="基本信息" class="m-b20 info-msg">
        <div class="info-content">
          <p>
            <span class="label">姓名</span
            ><b class="weight">{{ baseInfo.xm || "--" }}</b>
          </p>
          <p>
            <span class="label">年龄</span
            ><b class="half">{{ baseInfo.age || "--" }}</b>
            <span class="label">性别</span
            ><b>{{ baseInfo.xbdm | commonFiltering(genderList) }}</b>
          </p>
          <p>
            <span class="label">民族</span
            ><b class="half">{{
              baseInfo.mzdm | commonFiltering(nationList)
            }}</b>
            <span class="label">联系电话</span>
            <b>{{ baseInfo.lxdh || "--" }}</b>
          </p>
          <p>
            <span class="label">婚姻状态</span
            ><b class="half">{{
              baseInfo.hyzkdm | commonFiltering(marriageList)
            }}</b
            ><span class="label">职业</span
            ><b :title="baseInfo.zy" class="ellipsis zy-width">{{
              baseInfo.zy || "--"
            }}</b>
          </p>
          <p>
            <span class="label">户籍地址</span
            ><b :title="baseInfo.hjdzDzmc" class="ellipsis address-width">{{
              baseInfo.hjdzDzmc || "--"
            }}</b>
          </p>
          <p>
            <span class="label">居住地址</span
            ><b :title="baseInfo.xzzMlxxdz" class="ellipsis address-width">{{
              baseInfo.xzzMlxxdz || "--"
            }}</b>
          </p>
        </div>
      </ui-card>

      <div class="ui-card m-b20">
        <div class="card-head">
          <p class="capture-title face-capture capture-active">
            <span>最新报警</span>
          </p>
          <div class="capture-menu">
            <Menu
              mode="horizontal"
              theme="light"
              :active-name="selectAlarmKind.title"
              @on-select="selectHandler"
            >
              <MenuItem
                v-for="(item, index) in alarmKindList"
                :key="item.key"
                :name="item.title"
                :title="item.title"
              >
                {{ item.title }}
              </MenuItem>
            </Menu>
          </div>
          <div class="extra-right">
            <span class="more" @click="handleAlarmMore()"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <div class="card-content">
          <keep-alive>
            <component
              :is="selectAlarmKind.component"
              :key="selectAlarmKind.key"
              :idCardNo="archiveNo"
            />
          </keep-alive>
        </div>
      </div>

      <div class="ui-card">
        <div class="card-head">
          <p class="capture-title face-capture capture-active">
            <span>最近抓拍</span>
          </p>
          <div class="extra-right">
            <span class="more" @click="handleMore(1)"
              >更多 <i class="iconfont icon-more"></i
            ></span>
          </div>
        </div>
        <div class="card-content">
          <snapRecord
            type="video"
            :list="captureList"
            :loading="recordLoading"
          />
        </div>
      </div>
    </div>
    <div class="content-middle">
      <!--标签图谱-->
      <label-cloud-view
        class="label-cloud"
        :labels="baseInfo.labels || []"
        :type="1"
        :info="baseInfo"
        :photoUrl="
          baseInfo.photos && baseInfo.photos.length > 0
            ? baseInfo.photos[0].photoUrl
            : ''
        "
      />
      <!--底部swiper-->
      <middleSwiper
        class="middle-swiper"
        :relationship="relationship"
        type="people"
      />
    </div>
    <div class="content-right">
      <ui-card title="关系统计" class="m-b20" :padding="0" v-if="graphObj">
        <div
          slot="extra"
          class="play-btn mr-20 color-primary cursor-p"
          @click="toRelationGraph"
        >
          关系图谱
        </div>
        <!-- 关系图谱 -->
        <graph
          v-if="hasGraphData"
          @finish="graphLoaded"
          class="right"
          :relation-can-operate="false"
        ></graph>
        <ui-empty v-else></ui-empty>
      </ui-card>
      <!--行为规律-->
      <law-behaviour :dataType="1" :archiveNo="archiveNo"></law-behaviour>
      <!-- 活动轨迹-->
      <activityTrack :archiveNo="archiveNo"></activityTrack>
    </div>
  </section>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "@/views/holographic-archives/components/snap-record.vue";
import middleSwiper from "./components/middle-swiper.vue";
import lawBehaviour from "../components/law-behaviour.vue";
import labelCloudView from "@/views/holographic-archives/components/label-cloud-view/index.vue";
import CollapseExpand from "@/components/relation-graph/collapse-expand.vue";
import activityTrack from "./components/activity-track.vue";
import AlarmTab from "./components/alarm-tab.vue";
import FrequentTab from "./components/frequent-tab.vue";
import SchoolOutTab from "./components/school-out-tab.vue";
import NightOutTab from "./components/night-out.vue";
import TogetherTab from "./components/together-tab.vue";
import imgloading from "@/assets/img/car1.webp";
import { getPortraitCapture, relationshipCard } from "@/api/realNameFile";
import { getRelationshipCard } from "@/api/monographic/base.js";
import { MenuItem } from "view-design";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
export default {
  mixins: [relativeGraphMixin],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    swiper,
    swiperSlide,
    snapRecord,
    lawBehaviour,
    labelCloudView,
    middleSwiper,
    activityTrack,
    CollapseExpand,
    AlarmTab,
    FrequentTab,
    SchoolOutTab,
    NightOutTab,
    TogetherTab,
    MenuItem,
  },
  props: {
    // 基本信息
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      recordLoading: false,
      archiveNo: "", //人员档案id
      carLoading: false,
      carType: 1, //1驾乘/2名下车辆
      carType2: 1, //1最近抓拍/2最新报警
      carList: [], //驾乘车辆/名下车辆
      trackingStatistics: {}, //活动轨迹
      captureList: [], //抓拍记录
      trackingLoading: false,
      list: [1, 2, 3, 4],
      photoUrl: "", //标签图片
      relationship: {}, //关系卡片
      alarmInfo: {
        total: 0,
      },
      options: {
        disableDragNode: true,
        allowShowMiniToolBar: false,
        // disableZoom: true,
        defaultExpandHolderPosition: "hide",
        checkSelect: false,
        fontSize: 12,
        layouts: [
          {
            label: "中心",
            layoutName: "center",
            layoutClassName: "seeks-layout-center",
            // 节点间距
            distance_coefficient: 0.5,
          },
        ],
      },
      atlasList: [
        {
          id: 1,
          text: "苏A 29999",
          img: imgloading,
          children: [
            // { id: 2, num: 2, text: '同抓拍' },
            // { id: 3, num: 2, text: '同涉案' },
            // { id: 4, num: 2, text: '驾乘人员' },
            // { id: 5, num: 2, text: '同车主' },
            // { id: 6, num: 2, text: '同车主' },
            // { id: 7, num: 2, text: '同车主' }
          ],
        },
      ],
      alarmKindList: [
        {
          title: "前科人员发现",
          key: 1,
          component: AlarmTab,
        },
        {
          title: "频繁出入娱乐场所",
          key: 2,
          component: FrequentTab,
        },
        {
          title: "上学时间校外出现",
          key: 3,
          component: SchoolOutTab,
        },
        {
          title: "深夜时间出行",
          key: 4,
          component: NightOutTab,
        },
        {
          title: "与违法前科人同行",
          key: 5,
          component: TogetherTab,
        },
      ],
      selectAlarmKind: {
        title: "前科人员发现",
        key: 1,
        component: AlarmTab,
      },
      selectLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      genderList: "dictionary/getGenderList", //检索类型
      marriageList: "dictionary/getMarriageList", //婚姻状态
      nationList: "dictionary/getNationList", //民族
      professionList: "dictionary/getProfession", //职业
      relationList: "number-cube/getPeopleRelationList", //职业
      graphObj: "systemParam/graphObj", // 是否有图谱
      placeSecondLevelList: "dictionary/getPlaceSecondLevelList", // 场所二级分类
    }),
  },
  async created() {
    let query = this.$route.query;
    // 防止因为Anchor锚点导致的路由query参数丢失，跳档案都先跳到这个页面，先把参数存storage
    sessionStorage.setItem("query", JSON.stringify(query));
    this.archiveNo = query.archiveNo;
    this.getPortraitCapture();
    this.getCard();
  },
  mounted() {
    var list = [];
    this.relationList.forEach((item, index) => {
      var obj = {
        id: index + 2,
        num: item.count,
        text: item.nameCn,
      };
      list.push(obj);
    });
    this.atlasList[0].children = list;
  },
  watch: {
    baseInfo: {
      deep: true,
      handler(val) {
        if (val.photos && val.photos.length > 0) {
          this.atlasList[0].img = val.photos[0].photoUrl;
        }
      },
    },
    relationList: {
      deep: true,
      handler(val) {
        var list = [];
        val.forEach((item, index) => {
          var obj = {
            id: index + 2,
            num: item.count,
            text: item.nameCn,
          };
          list.push(obj);
        });
        this.atlasList[0].children = list;
        this.$refs.CollapseExpand.showSeeksGraph();
      },
    },
  },

  methods: {
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    selectHandler(value) {
      this.selectAlarmKind = this.alarmKindList.find(
        (item) => item.title == value
      );
    },
    /**
     * @description: 关系内容
     * @param {number} val 1：管控报警 2：抓拍记录 3：常去地 4：关联案件 5：同行人员 6：同户籍 7：违法违章 8：异常行为
     */
    async relationshipCard(val) {
      let data = {
        archiveNo: this.archiveNo,
        type: val,
      };
      let res = await relationshipCard(data);
      return res.data;
    },
    async alarmRelationShipCard() {
      let res = await getRelationshipCard(this.archiveNo);
      return res.data;
    },
    // 循环关系卡片
    async getCard() {
      this.relationship = {};
      // 抓拍记录
      let capture = await this.relationshipCard(2);
      // 常去地
      let place = await this.relationshipCard(3);
      // 管控报警
      let alarm = await this.alarmRelationShipCard();
      this.relationship = {
        capture: capture || {},
        place: place || {},
        alarm: alarm || {},
        alarmInfo: this.alarmInfo,
      };
    },
    // 抓拍记录 / 最近抓拍
    getPortraitCapture() {
      this.recordLoading = true;
      let data = {
        archiveNo: this.archiveNo,
        dataType: 1, //1实名2视频
        dataSize: 5, //取5条
      };
      getPortraitCapture(data)
        .then((res) => {
          this.captureList = res.data || [];
        })
        .catch(() => {})
        .finally(() => {
          this.recordLoading = false;
        });
    },
    // 更多跳转
    handleMore() {},
    // 报警更多
    handleAlarmMore() {
      const { href } = this.$router.resolve({
        name: "juvenile-alarm-manager",
        query: {
          // 直接跳到人员报警
          compareType: this.selectAlarmKind.key,
          idCardNo: this.archiveNo,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  display: flex;
  height: 100%;
  overflow: auto;
  min-height: 760px;
  padding: 16px 20px 20px;
  box-sizing: border-box;
}

.content-left,
.content-right {
  width: 500px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .ui-card,
  .info-msg {
    flex: 1;
    max-height: calc(~"33.3% - 20px");
  }

  /deep/ .card-content {
    height: calc(~"100% - 30px");
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  /deep/ .ivu-tabs-content {
    height: 234px;
  }

  .info-msg {
    /deep/ .card-content {
      padding: 22px 30px 36px !important;
    }
  }
}

.info-content {
  p {
    display: flex;
    font-size: 14px;
    padding: 1% 0;
    color: rgba(0, 0, 0, 0.9);

    .label {
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      width: 56px;
      font-weight: bold;
      white-space: nowrap;
      color: #2c86f8;
      margin-right: 15px;
    }

    b {
      font-weight: normal;
      word-break: break-all;

      &.half {
        width: 110px;
      }

      &.weight {
        font-size: 20px;
        line-height: 1;
        font-weight: bold;
      }

      &.zy-width {
        width: 190px;
      }

      &.address-width {
        width: 420px;
      }

      &.car-address {
        width: 160px;
      }
    }
  }
}

.capture-title {
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  background: #d3d7de;
  color: #666;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;

  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}

.capture-menu {
  overflow: hidden;
  height: 30px;
  width: 300px;

  /deep/ .ivu-menu-horizontal {
    display: flex;
    height: 30px;
    line-height: 30px;

    .ivu-menu-item {
      padding: 0;
      width: 60px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &::after {
      background: transparent;
    }
  }
}

.extra-right {
  flex: 1;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.35);
  text-align: right;
  padding-right: 20px;
  cursor: pointer;
  line-height: 20px;
}

.face-capture {
  position: relative;
}

.car-capture {
  position: absolute;
  left: 120px;
}

.capture-active {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
}

.card-content {
  padding: 20px;
}

.alarm-content {
  padding: 20px 0;
}

.content-middle {
  overflow: hidden;
  flex: 1;
  display: flex;
  width: 100%;
  flex-direction: column;
  position: relative;
  .middle-swiper {
    position: absolute;
    bottom: 60px;
  }
}

.movelink {
  cursor: pointer;
}
</style>
