<template>
  <div class="word-cloud">
    <span v-for="(item, $index) in labelList" :key="$index" :style="{...contentEle[$index].style}" class="word-cloud-tag" @click="labelHandle(item)">
      <div class="label-text">
        <!-- {{ item.name.length>6 ?item.name.substring(0, 6)+'...':item.name }} -->
        标签池一类点
      </div>
      <span :style="{backgroundColor: contentEle[$index].style.borderColor}" class="left-icon"></span>
      <span :style="{backgroundColor: contentEle[$index].style.borderColor}" class="right-icon"></span>
    </span>
  </div>
</template>
<script>
  export default {
    props: {
      labelList: {
        type: Array,
        default: () => []
      },
      clientWidth: {
        type: Number,
        default: 0
      },
      clientHeight: {
        type: Number,
        default: 0
      }
    },
    data () {
      return {
        docWidth: 0,
        docHeight: 0,
        contentEle: [],
        direction: '-1',
        speed: 1
      }
    },
    mounted () {
      this.docWidth = 1000
      this.docHeight = 430
      this.init()
    },
    methods: {
      init () {
        const radiusX = (this.docWidth - 50) / 2
        const radiusY = (this.docHeight - 50) / 2
        this.contentEle = []
        this.labelList.forEach((v, i) => {
          const k = -1 + (2 * (i + 1) - 1) / this.labelList.length
          const a = Math.acos(k)
          const b = a * Math.sqrt(this.labelList.length * Math.PI)
          const x = radiusX * Math.sin(a) * Math.cos(b)
          const y = radiusY * Math.sin(a) * Math.sin(b)
          const z = radiusX * Math.cos(a)
          const singleEle = {
            name: v.name,
            borderColor: v.color,
            backgroundColor: this.$util.common.colorRgb(v.color, 0.20),
            x, y, z, style: {}
          }
          this.contentEle.push(singleEle)
        })
        this.animate()
      },
      animate () {
        this.rotateX()
        this.rotateY()
        this.move()
        // window.requestAnimationFrame(this.animate)
      },
      rotateX () {
        const angleX = ['-1', '-1'].includes(this.direction) ? Math.PI / Infinity : Math.PI / ((Number(this.direction) / 2) * Number(this.speed))
        const cos = Math.cos(angleX)
        const sin = Math.sin(angleX)
        this.contentEle = this.contentEle.map(v => {
          const y1 = v.y * cos - v.z * sin
          const z1 = v.z * cos + v.y * sin
          return {
            ...v,
            y: y1,
            z: z1
          }
        })
      },
      rotateY () {
        const angleY = ['-2', '-2'].includes(this.direction) ? Math.PI / Infinity : Math.PI / ((Number(this.direction) / 2) * Number(this.speed))
        const cos = Math.cos(angleY)
        const sin = Math.sin(angleY)
        console.log(angleY, cos, sin)

        this.contentEle = this.contentEle.map(v => {
          const x1 = v.x * cos - v.z * sin
          const z1 = v.z * cos + v.x * sin
          console.log(x1, z1)
          return {
            ...v,
            x: x1,
            z: z1
          }
        })
      },
      move () {
        const CX = this.docWidth / 2
        const CY = this.docHeight / 2
        this.contentEle = this.contentEle.map((singleEle, i) => {
          const { x, y, z, borderColor, backgroundColor } = singleEle
          const left = `${x + CX - 15}px`
          const top = `${y + CY - 15}px`
          const transform = `translate(${left}, ${top})`
          const style = {
            ...singleEle.style,
            borderColor,
            backgroundColor,
            transform
          }
          return { x, y, z, style }
        })
      },
      labelHandle (item) {
        const query = `id=${item.labelId}&curName=${item.name}`
        window.open('/#/label-management/label-info?' + query, '_blank')
      }
    }
  }
</script>
<style lang="less" scoped>
  .word-cloud {
    width: 100%;
    height: 100%;
    position: relative;
      @{_deep} .ivu-tooltip {
        display: inline-flex;
        width: 100%;
      }
    .word-cloud-tag {
      display: block;
      position: absolute;
      left: 0px;
      top: 0px;
      color: #fff;
      font-size: 16px;
      padding: 2px 20px;
      border-radius: 2px;
      border: 1px solid #fff;
      line-height: 24px;
      cursor: pointer;
      .label-text {
        width: 100%;
      }
      .left-icon, .right-icon {
        position: absolute;
        width: 3px;
        height: 14px;
        left: -2px;
        top: 50%;
        transform: translate(0, -50%);
      }
      .right-icon {
        left: unset;
        right: -2px;
      }
    }
  }
</style>