import { allIndexType } from '@/views/governanceevaluation/evaluationoResult/util/IndexTypeConfig.js';

export default {
  methods: {
    /**
     * 判断指标是否存在
     * @param indexType 指标indexType
     * @returns true 存在 false 不存在
     */
    isExistIndex(indexType) {
      if (!indexType) {
        return false;
      }
      let index = Object.values(allIndexType)
        .flat()
        .find((item) => {
          return item.indexType === indexType && (item.componentName || item.target);
        });
      return !!index;
    },
    /**
     * 获取 行政区划和组织机构并按 regionType 排序
     * regionType: 0默认 1省 2直辖市 3省会市 4单列市 5其他地市 6直辖市的区 7直辖市的县 8其他各区县 9各派出所 10其他
     * @returns {{initialAreaList: *, initialOrgList: *}}
     */
    getInitialAreaOrgList() {
      let initialAreaList = this.getInitialAreaList
        .filter((item) => !item.disabled)
        .sort((a, b) => Number.parseInt(a.regionType) - Number.parseInt(b.regionType));
      let initialOrgList = this.getInitialOrgList
        .filter((item) => !item.disabled)
        .sort((a, b) => Number.parseInt(a.regionType) - Number.parseInt(b.regionType));
      return {
        initialAreaList,
        initialOrgList,
      };
    },
    isSpecialassessment(indexType) {
      if (!indexType) {
        return false;
      }
      let index = Object.values(allIndexType)
        .flat()
        .find((item) => {
          return item.indexType === indexType && item.target;
        });
      return !!index;
    },
    getComponentName(indexType) {
      if (this.isExistIndex(indexType) && !this.isSpecialassessment(indexType)) {
        return 'evaluationoResult';
      } else if (this.isExistIndex(indexType)) {
        return 'specialassessment';
      }
    },
    jump(query) {
      if (!query.noShowResult) {
        query.showResult = 1;
      }
      this.$router.push({
        name: this.getComponentName(query.indexType),
        query: {
          ...query,
        },
      });
    },
  },
};
