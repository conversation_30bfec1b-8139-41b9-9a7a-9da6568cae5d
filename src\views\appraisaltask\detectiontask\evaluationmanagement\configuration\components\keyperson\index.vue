<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    componentName() {
      switch (this.moduleAction.indexType) {
        case 'FOCUS_TRACK':
          return 'FocusTrack';
        case 'FOCUS_TRACK_URL_AVAILABLE':
          return 'FocusTrackUrlAvailable';
        default:
          return 'Original';
      }
    },
  },
  components: {
    Original: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/keyperson.vue')
      .default,
    FocusTrack: require('./components/focus-track.vue').default,
    FocusTrackUrlAvailable: require('./components/focus-track-url-available.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
