var TradeFlowChart = function (graphVis, option) {
  this.defaultOption = {
    ranksep: 180, // 左右层间隔
    nodesep: 10, // 节点上下间隔
    nodeTagWidth: 120, // 节点顶部tag区域的宽度
    nodeTagHeight: 26, // 节点顶部tag区域的高度
    appendRelationData: function (node, type) { }, // 给节点动态追加数据回调
    contractCallback: function (node, type, graphData) { }, // 收起节点的回调事件
    onClickNode: function (node) { }, // 点击节点
    tagIconReset: function (node) { // 根据节点动态设置节点提示的图标
      return ''
    },
    nodeIconReset: function (node) { // 根据节点动态设置节点右侧的icon图标
      return ''
    }
  }
  this.option = this.deepExtend(this.defaultOption, option, true, true)
  this.graphVis = graphVis

  this.init()
}

// 初始化画布及图数据
TradeFlowChart.prototype.init = function () {
  // 注册节点的自定义绘制方法
  this.graphVis.definedNodePaintFunc(this.utils().paintNode)
  // 注册节点的选中算法
  this.graphVis.definedNodeInBoundFunc(this.utils().checkNodeInBound)

  // 注册连线的自定义绘制方法
  this.graphVis.definedLinkPaintFunc(this.utils().paintLink)
}

// 重新加载指定数据
TradeFlowChart.prototype.loadData = function (graphData = { nodes: [], links: [] }) {
  this.graphVis.clearAll()

  var center = this.graphVis.getViewCenter();
  (graphData.nodes || []).forEach(node => {
    node.style = {
      ...node.style,
      x: center.x,
      y: center.y
    }
  })

  this.graphVis.addGraph(graphData)
  this.runLayout(true)
}

// 执行布局计算
TradeFlowChart.prototype.runLayout = function (isInit = false) {
  const nodeTagHeight = this.option.nodeTagHeight
  const ranksep = this.option.ranksep
  const nodesep = this.option.nodesep

  var dagre = this.graphVis.getDarge()
  var g = new dagre.graphlib.Graph()
    .setGraph({
      multigraph: false,
      rankdir: 'LR', // 节点的延伸排列方向 TB、LR、RL、BT 默认是'TB'（从上到下）
      align: '', // UL（上左）, UR（上右）, DL（下左）, 或者 DR（下右），默认是 undefined
      nodesep: nodesep, // 相同层级中 node 的间距 默认 50
      ranksep: ranksep, // 层间距 相邻层级之间的间距
      ranker: 'tight-tree' // tight-tree、network-simplex、longest-path
    })
    .setDefaultEdgeLabel(() => ({}))

  var nodes = this.graphVis.nodes
  var nodeIdMapNode = new Map()

  var rootVisNode = null // 记录根节点
  nodes.forEach(node => {
    nodeIdMapNode.set(`${node.id}`, node)
    if (node.properties.tag) {
      g.setNode(`${node.id}`, { shape: 'rect', width: node.width, height: node.height + nodeTagHeight })
    } else {
      g.setNode(`${node.id}`, { shape: 'rect', width: node.width, height: node.height })
    }

    if (node.properties.isRootNode) {
      rootVisNode = node
    }
  })

  this.graphVis.links.forEach(link => {
    g.setEdge(`${link.source.id}`, `${link.target.id}`, { weight: 1 })
  })

  dagre.layout(g)

  var center = this.graphVis.getViewCenter()
  var allVisNodes = []
  let coords = []
  var coord = null
  var thisRootCoord = { x: center.x, y: center.y }
  g.nodes().forEach((nodeId) => {
    coord = g.node(nodeId)
    coords.push({ x: coord.x, y: coord.y })

    if (nodeId == rootVisNode.id) {
      thisRootCoord = { x: coord.x, y: coord.y }
    }
  })

  var dx = thisRootCoord.x - rootVisNode.x
  var dy = thisRootCoord.y - rootVisNode.y

  if (isInit) {
    dx += (rootVisNode.width / 2)
  }

  g.nodes().forEach(d => {
    const node = g.node(d)
    var visNode = nodeIdMapNode.get(d)
    if (visNode) {
      visNode.finishedX = node.x - dx
      visNode.finishedY = node.y - dy

      if (visNode.properties.tag) {
        visNode.finishedY = visNode.finishedY + nodeTagHeight / 2
      }
      allVisNodes.push(visNode)
    }
  })

  var that = this
  that.graphVis.createBaseAnimate({
    targets: allVisNodes, // 执行带动画的对象集
    duration: 800, // 持续时间
    easing: 'easeOutQuad', // 动画类型
    x: function (el) {
      return el.finishedX
    },
    y: function (el) {
      return el.finishedY
    },
    begin: function (anim) {
      that.graphVis.switchAnimate(true) // 开启高性能动画模式
    }
  }).finished.then(function () {
    that.graphVis.switchAnimate(false) // 关闭高性能动画模式
  })

  this.resetNodesIcon() // 统一设置节点图标
}

// 点击节点
TradeFlowChart.prototype.clickNode = function (node) {
  var self = this
  var appendRelationDataFunc = function () { }
  var onClick = function () { }
  if (self.option.hasOwnProperty('appendRelationData')) {
    var appendRelationDataFunc_ = self.option['appendRelationData']
    if (typeof (appendRelationDataFunc_) === 'function') {
      appendRelationDataFunc = appendRelationDataFunc_
    }
  }

  if (self.option.hasOwnProperty('onClickNode')) {
    var onClickNodeFunc_ = self.option['onClickNode']
    if (typeof (onClickNodeFunc_) === 'function') {
      onClick = onClickNodeFunc_
    }
  }

  // 如果选中的是控制点
  var that = this
  if (node.properties.isRootNode) {
    if (node.selLeftCtrlNode) {
      if ((node.inLinks || []).length == 0) {
        appendRelationDataFunc(node, 'left')
      } else {
        that.contractLeftNodes(node)
      }
    } else if (node.selRightCtrlNode) {
      if ((node.outLinks || []).length == 0) {
        appendRelationDataFunc(node, 'right')
      } else {
        that.contractRightNodes(node)
      }
    } else {
      onClick(node)
    }
  } else {
    if (node.selCtrlNode) {
      if (node.properties.nodeAlign == 'right') {
        if ((node.outLinks || []).length == 0) {
          appendRelationDataFunc(node, 'right')
        } else {
          that.contractRightNodes(node)
        }
      } else {
        if ((node.inLinks || []).length == 0) {
          appendRelationDataFunc(node, 'left')
        } else {
          that.contractLeftNodes(node)
        }
      }
    } else {
      onClick(node)
    }
  }
}

// 收起右侧节点
TradeFlowChart.prototype.contractRightNodes = function (node) {
  var allChildNodes = []
  this.utils().deepFindChildNode(node, allChildNodes, 'out')

  var links = new Set()
  allChildNodes.forEach(n => {
    (n.inLinks || []).forEach(link => links.add(link));
    (n.outLinks || []).forEach(link => links.add(link))
  })

  links = Array.from(links)
  this.graphVis.deleteLinks(links)
  this.graphVis.deleteNodes(allChildNodes)

  this.contractNodeCallBack(node, 'right', { nodes: allChildNodes, links: links })

  this.runLayout()
}

TradeFlowChart.prototype.contractLeftNodes = function (node) {
  var allChildNodes = []
  this.utils().deepFindChildNode(node, allChildNodes, 'in')

  var links = new Set()
  allChildNodes.forEach(n => {
    (n.inLinks || []).forEach(link => links.add(link));
    (n.outLinks || []).forEach(link => links.add(link))
  })
  links = Array.from(links)
  this.graphVis.deleteLinks(links)
  this.graphVis.deleteNodes(allChildNodes)

  this.contractNodeCallBack(node, 'left', { nodes: allChildNodes, links: links })

  this.runLayout()
}

// 执行收起回调
TradeFlowChart.prototype.contractNodeCallBack = function (node, type, graphData) {
  if (this.option.hasOwnProperty('contractCallback')) {
    var contractCallback = this.option['contractCallback']
    if (typeof (contractCallback) === 'function') {
      var _nodes = graphData.nodes.map(node => {
        return {
          id: node.id,
          label: node.label,
          type: node.type,
          style: {
            fillColor: node.fillColor,
            shape: node.shape,
            width: node.width,
            height: node.height
          },
          properties: {
            ...node.properties
          }
        }
      })

      var _links = graphData.links.map(link => {
        return {
          id: link.id,
          source: link.source.id,
          target: link.target.id,
          type: link.type,
          style: {
            lineWidth: link.lineWidth,
            strokeColor: link.strokeColor,
            lineDash: link.lineDash
          },
          properties: {
            ...link.properties
          }
        }
      })

      var serilizeData = { nodes: _nodes, links: _links }

      contractCallback(node, type, serilizeData)
    }
  }
}

// 扩展节点数据
TradeFlowChart.prototype.expendNodes = function (node, graphData, nodeAlign = 'right') {
  (graphData.nodes || []).forEach((item) => {
    item.style = {
      ...item.style,
      x: node.x,
      y: node.y
    }
  })

  this.graphVis.addGraph(graphData)

  this.runLayout()
}

// 重设节点图标
TradeFlowChart.prototype.resetNodesIcon = function () {
  var that = this
  var tagIconReset = this.option.tagIconReset
  var nodeIconReset = this.option.nodeIconReset
  var iconPath = null

  this.graphVis.nodes.forEach(_node => {
    iconPath = tagIconReset(_node)
    if (iconPath) {
      that.setAttrIcon(_node, 'tagIcon', iconPath, () => { that.graphVis.refreshView() })// 设置图标
    }

    iconPath = nodeIconReset(_node)
    if (iconPath) {
      that.setAttrIcon(_node, 'nodeIcon', iconPath, () => { that.graphVis.refreshView() })// 设置图标
    }
  })
}

TradeFlowChart.prototype.setAttrIcon = function (node, attr = 'iconImage', imgS, callback) {
  if (typeof imgS === 'string') {
    var img = new Image()
    img.setAttribute('crossOrigin', 'Anonymous')
    img.src = imgS
    img.onload = function () {
      node[attr] = img
      if (typeof callback === 'function') {
        callback()
      }
    }
  }
}

TradeFlowChart.prototype.utils = function () {
  var nodeTagHeight = this.option.nodeTagHeight
  var nodeTagWidth = this.option.nodeTagWidth

  const drawRect = (ctx, x, y, width, height, r) => {
    var r2d = Math.PI / 180
    ctx.save()
    ctx.beginPath()
    ctx.moveTo(x + r, y)
    ctx.lineTo(x + width - r, y)
    ctx.arc(x + width - r, y + r, r, r2d * 270, r2d * 360, false)
    ctx.lineTo(x + width, y + height - r)
    ctx.arc(x + width - r, y + height - r, r, 0, r2d * 90, false)
    ctx.lineTo(x + r, y + height)
    ctx.arc(x + r, y + height - r, r, r2d * 90, r2d * 180, false)
    ctx.lineTo(x, y + r)
    ctx.arc(x + r, y + r, r, r2d * 180, r2d * 270, false)
    ctx.closePath()
  }

  // 绘制节点文字
  const paintNodeText = (ctx, node) => {
    ctx.save()
    ctx.font = node.font
    ctx.fillStyle = `rgba(${node.fontColor},${node.alpha})`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(node.label, 0, 2)
    ctx.restore()
  }

  const paintControlBarNode = (ctx, node) => {
    if (node.properties.isRootNode) {
      var leftExpendFlag = (node.inLinks || []).length == 0
      var rightExpendFlag = (node.outLinks || []).length == 0

      paintControlBarForPos(ctx, node, 'left', leftExpendFlag)
      paintControlBarForPos(ctx, node, 'right', rightExpendFlag)
    } else {
      if (node.properties.showBar) {
        var nodeAlign = node.properties.nodeAlign
        var expendFlag = false
        if (nodeAlign == 'left') {
          expendFlag = (node.inLinks || []).length == 0
        } else {
          expendFlag = (node.outLinks || []).length == 0
        }

        paintControlBarForPos(ctx, node, nodeAlign, expendFlag)
      }
    }
  }

  const paintControlBarForPos = (ctx, node, position = 'left', contract) => {
    var fillStyle = 'rgba(255,255,255,1)'
    var strokeStyle = 'rgba(150,150,150,1)'

    if (node.showSelected) {
      strokeStyle = 'rgba(100,100,250,1)'
    }

    var offsetX = node.width / 2
    var barBorderWidth = 2
    var barSize = 8
    var barOffset = barSize + barBorderWidth * 2

    if (position == 'left') {
      offsetX = -node.width / 2
      barOffset = -barOffset + barBorderWidth
    } else {
      barOffset = barSize + barBorderWidth
    }

    ctx.save()
    ctx.beginPath()
    ctx.arc(offsetX + barOffset, 0, barSize, 0, Math.PI * 2, false)
    ctx.closePath()
    ctx.lineWidth = 1
    ctx.strokeStyle = strokeStyle
    ctx.fillStyle = fillStyle
    ctx.fill()
    ctx.stroke()

    ctx.font = 'normal 20px Arial'
    ctx.textBaseline = 'middle'
    ctx.textAlign = 'center'
    ctx.fillStyle = strokeStyle
    ctx.fillText(contract ? '+' : '-', offsetX + barOffset, contract ? 1.5 : 0)// +=1.5

    ctx.restore()
  }

  // 绘制提示节点区域
  const paintTipRect = (ctx, node, x, y) => {
    var nodeTag = node.properties.tag
    if (nodeTag) {
      var width = nodeTagWidth, height = nodeTagHeight
      var tipX = x, tipY = y - (height + 3)
      drawRect(ctx, tipX, tipY, width, height, 3)
      ctx.fillStyle = `rgba(${nodeTag.color || '217,244,66'},${node.alpha})`
      ctx.fill()
      ctx.restore()

      var tipIconSize = 10, margin = 3
      // 文字
      ctx.save()
      ctx.font = 'normal 11px Yahei'
      ctx.fillStyle = `rgba(${nodeTag.fontColor || '80,80,80'},${node.alpha})`
      ctx.textAlign = 'left'
      ctx.textBaseline = 'middle'
      ctx.fillText(nodeTag.text, tipX + ((tipIconSize + margin) * 2), tipY + height / 2 + 2)
      ctx.restore()

      // 绘制提示的图标
      drawImageIcon(ctx, node, 'tagIcon', tipX, tipY - 2)
    }
  }

  const drawImageIcon = (ctx, node, attr, x, y) => {
    var image = node[attr]
    if (image) {
      var globleAlpha = ctx.globalAlpha
      ctx.save()
      ctx.globalAlpha = node.alpha
      ctx.beginPath()
      ctx.rect(x, y + 6, 24, 20)
      ctx.closePath()
      ctx.clip()
      ctx.drawImage(image, x, y + 6, 24, 20) // y+image.height/2
      ctx.globalAlpha = globleAlpha
      ctx.restore()
    }
  }

  function paintNode (ctx) {
    var width = this.width
    var height = this.height
    var x = -width / 2, y = -height / 2

    // 绘制节点矩形区域
    drawRect(ctx, x, y, width, height, this.borderRadius)
    if ((this.showSelected || this.selected) && this.selectedBorderWidth > 0) {
      ctx.lineWidth = this.borderWidth + this.selectedBorderWidth
      ctx.strokeStyle = `rgba(${this.selectedBorderColor},${this.alpha})`
      ctx.stroke()
    }

    if (!this.selected && !this.showSelected && this.borderWidth > 0) {
      ctx.lineWidth = this.borderWidth
      ctx.strokeStyle = `rgba(${this.borderColor},${this.alpha})`
      ctx.stroke()
    }

    if (this.fillColor) {
      ctx.fillStyle = `rgba(${this.fillColor},${this.alpha})`
      ctx.fill()
    }
    ctx.restore()

    // 绘制左右图标
    drawImageIcon(ctx, this, 'nodeIcon', x + this.width - 24, y + 2)
    // drawImageIcon(ctx,this,'leftIcon',x+2,y+2);

    paintNodeText(ctx, this)

    // 绘制提示文字
    paintTipRect(ctx, this, x, y)

    // 绘制控制bar
    paintControlBarNode(ctx, this)
  }

  // 绘制连线
  function paintLink (ctx) {
    var source = this.source,
      target = this.target
    var sourceY = source.cy
    var sourceX = source.x + source.width
    var targetX = target.x, targetY = target.cy

    var x3 = (sourceX + targetX) * 0.5,
      y3 = sourceY,
      x4 = (sourceX + targetX) * 0.5,
      y4 = targetY

    // 箭头特殊绘制
    var margin = 3
    if (target.properties.nodeAlign == 'left' || target.properties.isRootNode) {
      margin = 21
    }

    ctx.beginPath()
    this.setLineStyle(ctx)
    ctx.moveTo(sourceX, sourceY)
    ctx.bezierCurveTo(x3, y3, x4, y4, targetX - margin - 1, targetY)
    ctx.stroke()

    this.path = []
    this.bezierPoints = [sourceX, sourceY, x3, y3, x4, y4, targetX - margin, targetY]

    if (this.showArrow) {
      this.paintSpecialArrow(ctx, {
        x: target.x - margin,
        y: target.cy
      }, {
        x: target.x - margin + 2,
        y: target.cy
      })
    }

    // 绘制连线的文字
    paintLineText(ctx, this)
  }

  const paintLineText = (ctx, link) => {
    var source = link.source,
      target = link.target
    var targetX = target.x,
      targetY = target.cy

    var margin = 10
    var textHeight = 13
    ctx.save()
    ctx.font = link.font
    ctx.fillStyle = `rgba(${link.fontColor},1)`
    if (target.properties.isRootNode || target.properties.nodeAlign == 'left') {
      targetX = source.x + source.width + margin
      targetY = source.y + textHeight

      ctx.textAlign = 'left'
    } else if (target.properties.nodeAlign == 'right') {
      targetX = target.x - margin
      targetY = target.y + textHeight
      ctx.textAlign = 'right'
    }

    ctx.textBaseline = 'middle'
    ctx.fillText(`${link.properties.records}/${link.properties.amount}`, targetX, targetY)
    ctx.fillText(`${link.properties.rate}`, targetX, targetY + textHeight)
    ctx.restore()
  }

  function checkNodeInBound (x, y) {
    if (this.properties.showBar) {
      this.selCtrlNode = false
      this.selLeftCtrlNode = false
      this.selRightCtrlNode = false

      if (this.properties.isRootNode) {
        var pos = findControlPosition(this, 'left')
        if (((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y)) < (pos.radius * pos.radius)) {
          this.selLeftCtrlNode = true
          return true
        }

        pos = findControlPosition(this, 'right')
        if (((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y)) < (pos.radius * pos.radius)) {
          this.selRightCtrlNode = true
          return true
        }
      } else {
        var pos = findControlPosition(this, this.properties.nodeAlign)
        if (((x - pos.x) * (x - pos.x) + (y - pos.y) * (y - pos.y)) < (pos.radius * pos.radius)) {
          this.selCtrlNode = true
          return true
        }
      }
    }
    return x > this.cx - this.width / 2 && x < this.cx + this.width / 2 && y > this.cy - this.height / 2 && y < this.cy + this.height / 2
  }

  const findControlPosition = (node, nodeAlign) => {
    var offsetX = node.width / 2
    var barBorderWidth = 2
    var barSize = 8
    var barOffset = barSize + barBorderWidth * 2
    if (nodeAlign == 'left') {
      offsetX = -offsetX
      barOffset = -barOffset + barBorderWidth
    } else {
      barOffset = barSize + barBorderWidth
    }
    return {
      x: node.cx + offsetX + barOffset,
      y: node.cy,
      radius: 8
    }
  }

  const findAllPreLevelNodes = (node, preLevelNodes = []) => {
    (node.inLinks || []).forEach((link) => {
      preLevelNodes.push(link.source)
      findAllPreLevelNodes(link.source, preLevelNodes)
    })
  }

  // 查找下级子节点
  const deepFindChildNode = (node, allChildNodes, type = 'out') => {
    var childNodes = []
    if (type == 'out') {
      childNodes = (node.outLinks || []).map((link) => {
        return link.target
      })
    } else if (type == 'in') {
      childNodes = (node.inLinks || []).map((link) => {
        return link.source
      })
    }

    childNodes.forEach((n) => {
      allChildNodes.push(n)
    })
    childNodes.forEach((n) => {
      if (!n.contract) {
        deepFindChildNode(n, allChildNodes, type)
      }
    })
  }

  return {
    drawRect: drawRect,
    paintNodeText: paintNodeText,
    paintControlBarNode: paintControlBarNode,
    paintControlBarForPos: paintControlBarForPos,
    paintTipRect: paintTipRect,
    drawImageIcon: drawImageIcon,
    paintNode: paintNode,
    paintLink: paintLink,
    paintLineText: paintLineText,
    checkNodeInBound: checkNodeInBound,
    findControlPosition: findControlPosition,
    findAllPreLevelNodes: findAllPreLevelNodes,
    deepFindChildNode: deepFindChildNode
  }
}

TradeFlowChart.prototype.deepExtend = function (a, b, protoExtend, allowDeletion) {
  for (var prop in b) {
    if (b.hasOwnProperty(prop) || protoExtend === true) {
      if (b[prop] && b[prop].constructor === Object) {
        if (a[prop] === undefined) {
          a[prop] = {}
        }
        if (a[prop].constructor === Object) {
          this.deepExtend(a[prop], b[prop], protoExtend)
        } else {
          if ((b[prop] === null) && a[prop] !== undefined && allowDeletion === true) {
            delete a[prop]
          } else {
            a[prop] = b[prop]
          }
        }
      } else if (Array.isArray(b[prop])) {
        a[prop] = []
        for (let i = 0; i < b[prop].length; i++) {
          a[prop].push(b[prop][i])
        }
      } else {
        if ((b[prop] === null) && a[prop] !== undefined && allowDeletion === true) {
          delete a[prop]
        } else {
          a[prop] = b[prop]
        }
      }
    }
  }
  return a
}

export default TradeFlowChart
