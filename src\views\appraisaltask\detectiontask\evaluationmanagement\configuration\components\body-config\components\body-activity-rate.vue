<!-- 人体卡口设备活跃率 -->
<template>
  <div class="face-body-activity-rate">
    <common-form
      :label-width="labelWidth"
      class="common-form"
      ref="commonForm"
      :module-action="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="labelWidth">
      <FormItem label="时间范围" class="right-item mb-sm" prop="name">
        <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer>
          <Option value="5">昨天</Option>
          <Option value="1">当天</Option>
          <Option value="2">最近两天</Option>
          <Option value="3">最近一周</Option>
          <Option value="4">最近30天</Option>
          <Option value="6"> 自定义 </Option>
        </Select>
      </FormItem>
      <FormItem
        label=" "
        :class="[formData.lastHours && formData.lastHours < 24 ? 'mb-md' : 'mb-lg']"
        v-show="formData.timeDelay === '6'"
        prop="lastHours"
        :rules="[{ validator: validateLastHours, trigger: 'change', required: false }]"
      >
        <div class="lastHours-input base-text-color">
          最近<InputNumber
            :min="0"
            :step="1"
            :active-change="false"
            :precision="1"
            v-model="formData.lastHours"
            class="mr-sm ml-sm"
            placeholder="请输入"
          ></InputNumber>
          小时
        </div>
      </FormItem>
      <FormItem label="上传人脸数量">
        <span class="color-white mr-sm">不少于</span>
        <InputNumber v-model="formData.imageNum" :disabled="isView"></InputNumber>
        <span class="base-text-color ml-sm">张</span>
        <p class="label-color">说明：该时间范围内如果有足够数量的抓拍数据，则设备联网。</p>
      </FormItem>
      <FormItem label="多目设备检测" class="right-item mb-sm">
        <Checkbox v-model="formData.isMulti" :true-value="1" :false-value="0"
          >多目设备合并成一个设备进行检测统计</Checkbox
        >
      </FormItem>
      <FormItem
        label="多目设备定义"
        class="right-item mb-sm"
        prop="multiType"
        :rules="[{ validator: validateDayByMultiType, trigger: 'change', required: formData.isMulti ? true : false }]"
      >
        <RadioGroup class="mb-sm" v-model="formData.multiType">
          <Radio :label="1" class="mr-lg" :disabled="!formData.isMulti">IP地址相同</Radio>
          <Radio :label="2" class="mr-lg" :disabled="!formData.isMulti">MAC地址相同</Radio>
          <Radio :label="3" :disabled="!formData.isMulti">IP地址相同和MAC地址相同</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      labelWidth: 180,
      formData: {
        timeDelay: null,
        dateRang: null,
        lastHours: null, //选中自定义，最近x小时
        imageNum: null,
        isMulti: 0,
        multiType: null,
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: null,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
      },
      ruleCustom: {},
      validateLastHours: (rule, value, callback) => {
        let { timeDelay, lastHours } = this.formData;
        if (timeDelay == '6' && !lastHours) {
          callback(new Error('请输入自定义时间'));
        } else if (timeDelay == '6' && lastHours > 24) {
          callback(new Error('最近时间不能超过24小时'));
        } else {
          callback();
        }
      },
      validateDayByMultiType: (rule, value, callback) => {
        let { isMulti, multiType } = this.formData;
        if (isMulti === 1 && !multiType) {
          callback(new Error('请选择多目设备定义'));
        } else {
          callback();
        }
      },
    };
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            timeDelay: null,
          };
        }
      },
      immediate: true,
    },
    // 多目设备检测
    'formData.isMulti': {
      handler(val) {
        if (val === 0) {
          this.formData.multiType = null;
        }
      },
    },
  },
  async created() {},
  methods: {
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      this.handleParams();

      let modalDataValid = await this.$refs.modalData.validate();
      let commonFormValid = await this.$refs['commonForm'].handleSubmit();
      return commonFormValid && modalDataValid;
    },
    //处理参数
    handleParams() {
      //时间范围自定义则清空dateRang，其他清空lastHours。后端逻辑优先取dateRang字段
      if (this.formData.timeDelay != 6) {
        this.formData.lastHours = null;
      } else {
        this.formData.dateRang = [{ hourStart: null, hourEnd: null }];
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .lastHours-input .ivu-input-number {
    @{_deep}.ivu-input-number-handler-wrap {
      border-left: 1px solid #10457e;
      border-bottom: 1px solid #10457e;
      a {
        background: #02162b;
        color: var(--color-primary);
        .ivu-input-number-handler-down-inner,
        .ivu-input-number-handler-up-inner {
          color: var(--color-primary);
        }
      }
      .ivu-input-number-handler-down,
      .ivu-input-number-handler-up {
        border-color: #10457e;
      }
    }
  }
}
.face-body-activity-rate {
  @{_deep}.select-width,
  .input-width {
    width: 380px;
  }
  .label-color {
    color: #e44f22;
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .width-picker {
    width: 174px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .inspection {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  .notCheck {
    color: #56789c;
  }
  .lastHours-input .ivu-input-number {
    width: 100px;
    @{_deep}.ivu-input-number-handler-wrap {
      display: inline-block;
    }
  }
  @{_deep}.drop-tree {
    width: 100% !important;
  }
}
</style>
