<!--
    * @FileDescription: 车辆报警
    * @Author: H
    * @Date: 2023/05/09
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="vehicle-alarm" @click="detailFn">
        <div class="box-top">
            <div class="level-title">
                <img class="level" :src="levelImg[data.bgIndex]" alt />
                <div class="num">
                    {{ 
                        data.taskLevel == 1 ? '一级' :
                        data.taskLevel == 2 ? '二级' : '三级'
                    }}
                </div>
            </div>
            <div class="collection favorite">
                <ui-btn-tip v-if="data.myFavorite == 1" class="collection-icon" content="取消收藏" icon="icon-yishoucang" transfer @click.stop.native="collection(data, 2)" />
                <ui-btn-tip v-else class="collection-icon"  content="收藏" icon="icon-shoucang" transfer @click.stop.native="collection(data, 1)" />
            </div>
        </div>
        <div class="contrast">
            <div class="contrast-left">
                <div class="vehicle-img">
                    <ui-image :src="data.traitImg"></ui-image>
                </div>
                <ui-plate-number :plateNo="data.plateNo" :color="data.plateColor"></ui-plate-number>
            </div>
            <div class="contrast-right">
                <div class="box-wrapper" v-for="(item, index) in infoList" :key="index">
                    <div class="title">{{ item.title }}</div>
                    <div class="box-val" v-if="item.field == 'taskType'">{{ data[item.field] == '1' ? '单体布控' : '库布控' }} </div>
                    <div class="box-device" v-show-tips v-else-if="item.field == 'deviceName'">{{ data[item.field] }} </div>
                    <div class="box-val" v-else>{{ data[item.field] || '未知' }} </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    props: {
        data: {
            type: Object,
            default: () => {
                return {
                    bgIndex: 1,
                    taskLevel: 1,
                    myFavorite: 1
                }
            }
        }
    },
    data () {
        return {
            infoList: [
                {'title': '布控来源:', 'field': 'taskType', },
                {'title': '车主姓名:', 'field': 'name', },
                {'title': '报警时间:', 'field': 'alarmTime', },
                {'title': '报警设备:', 'field': 'deviceName',},
                {'title': '所属任务:', 'field': 'taskName', },
            ],
            levelImg: {
                1: require('@/assets/img/target/title1.png'),
                2: require('@/assets/img/target/title2.png'),
                3: require('@/assets/img/target/title3.png'),
                4: require('@/assets/img/target/title4.png'),
                5: require('@/assets/img/target/title5.png'),
            },
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
    },
    mounted(){
            
    },
    methods: {
        collection(data, flag) { 
            var param = {
                favoriteObjectId: data.alarmId,
                favoriteObjectType: 15,
            }
            this.$emit('collection', param, flag)
        },
        detailFn() {
            this.$emit('vehicleAlarm')
        }
    }
}
</script>

<style lang='less' scoped>
@import 'style/alarm';
.vehicle-alarm{
    height: 190px;
    background: url('../../../../../assets/img/target/unproces.png') no-repeat bottom right, #fff;
    box-shadow: 0 1px 3px #d9d9d9;
    overflow: hidden;
    border-radius: 3px;
    border: 1px solid #D3D7DE;
    width: 100%;
    cursor: pointer;
    .contrast{
        display: flex;
        padding: 10px 0 0 20px;
        &-left{

            .vehicle-img{
                width: 90px;
                height: 90px; 
                margin-bottom: 5px;
                border: 1px solid #D3D7DE;
            }
        }
        &-right{
            margin-left: 10px;
            .box-wrapper{
                margin-bottom: 8px;
                .title{
                    font-size: 14px;
                    color: rgba(0,0,0,0.6);
                }
                .box-val{
                    font-size: 14px;
                    color: rgba(0,0,0,0.9);
                    flex: 1;
                }
                .box-device{
                    width: 90px;
                    font-size: 14px;
                    color: rgba(0,0,0,0.9);
                }
            }
        }
    }
    .collection{
        /deep/ .icon-shoucang {
            color: #888888 !important;
            text-shadow: 0px 1px 0px #e1e1e1;
        }
        /deep/ .icon-yishoucang {
            color: #f29f4c !important;
        }
    }
}
</style>
