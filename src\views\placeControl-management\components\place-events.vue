<template>
  <div style="height: 100%; width: 100%">
    <div class="darktime-header">
      <div class="darktime-item">
        <span>排名</span>
        <span class="place">场所名称</span>
        <span>场所类型</span>
        <span>总积分</span>
        <span>关注度</span>
      </div>
    </div>
    <div class="darktime-out" v-if="list.length > 0">
      <div
        class="darktime-item"
        v-for="(item, index) in list"
      >
        <span class="no"><span>{{ index + 1 }}</span></span>
        <span class="place ellipsis" :title="item.placeName">{{ item.placeName }}</span>
        <span>{{ item.placeFirstType }}</span>
        <span class="primary">{{ item.score }}</span>
        <span :style="{color: item.attention=='高'?'#EA4A36':item.attention=='中'?'#F29F4C':'#1FAF81',fontWeight:700}">{{ item.attention }}</span>
      </div>
    </div>
    <ui-empty v-if="list.length == 0"></ui-empty>
    <ui-loading v-if="loading" />
  </div>
</template>

<script>
export default {
  name: "nightOut",
  components: {
  },
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
     
    };
  },
  methods: {
    
  },
};
</script>

<style lang="less" scoped>
.darktime-out {
  height: 100%;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  .darktime-item:nth-child(odd) {
    background: #f9f9f9;
  }
  .darktime-item {
    .no {
      span {
        display: block;
        width: 24px;
        text-align: center;
      }
    }
    &:first-of-type {
      .no span {
        background: url("~@/assets/img/community-management/no1.png") center / contain no-repeat;
        color: transparent;
      }
    }
    &:nth-of-type(2) {
      .no span {
        background: url("~@/assets/img/community-management/no2.png") center / contain no-repeat;
        color: transparent;
      }
    }
    &:nth-of-type(3) {
      .no span {
        background: url("~@/assets/img/community-management/no3.png") center / contain no-repeat;
        color: transparent;
      }
    }
  }
}
.darktime-header {
  padding-right: 10px;
  margin-bottom: 10px;
}
.darktime-header, .darktime-item {
    font-size: 14px;
    color: rgba(0,0,0,0.75);
    display: flex;
    width: 100%;
    span {
      flex: 1;
      padding: 0 5px;
      white-space: nowrap;
    }
    .place {
      flex: 2.5;
    }
    .primary {
      font-weight: 700;
    }
  }
</style>
