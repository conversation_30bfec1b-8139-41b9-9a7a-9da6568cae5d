<template>
  <div class="dom-wrapper">
    <div
      class="dom no-page-height"
      @click="($event) => $event.stopPropagation()"
    >
    <div
        v-if="tableList.length > 1 && tableIndex > 0"
        class="leftIcon icon"
        @click="switchItem(-1)"
      >
        <Icon type="md-arrow-dropleft" />
      </div>
      <div
        v-if="tableList.length > 1 && tableIndex < tableList.length - 1"
        class="rightIcon icon"
        @click="switchItem(1)"
      >
        <Icon type="md-arrow-dropright" />
      </div>
      <header>
        <span>解析详情</span>
        <Icon type="md-close" size="14" @click.native="() => $emit('close')" />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="main-info-box">
              <div
                class="main-item"
                v-for="(item, index) in mainLabel"
                :key="index"
              >
                <div class="title">
                  <span>{{ item.label }}</span
                  ><span>&nbsp;：</span>
                </div>
                <div
                  class="content"
                  :title="
                    item.rule == null
                      ? detailsInfo[item.key]
                      : item.rule(detailsInfo[item.key], detailsInfo)
                  "
                >
                  {{
                    item.rule == null
                      ? detailsInfo[item.key]
                      : item.rule(detailsInfo[item.key], detailsInfo)
                  }}
                </div>
              </div>
            </div>
            <div style="text-align: center">
              <img
                class="level"
                :src="operationTypeSrc(detailsInfo.operationType)"
                alt=""
              />
              <div class="opinion">
                <Input
                  v-model="alarmInfo.remark"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入处理意见"
                />
              </div>

              <div class="status">
                <div class="tip">处理状态：</div>
                <RadioGroup v-model="alarmInfo.operationType">
                  <Radio :label="1">有效</Radio>
                  <Radio style="margin-left: 20px" :label="2">无效</Radio>
                </RadioGroup>
                <div class="history" v-if="alarmInfo.operationType !== 0">
                  <Poptip
                    trigger="hover"
                    transfer
                    word-wrap
                    @on-popper-show="showHistory()"
                  >
                    <i class="iconfont icon-lishijilu"></i>历史处理
                    <div slot="title">
                      <div class="block"></div>
                      <i>历史处理</i>
                    </div>
                    <div slot="content">
                      <Timeline>
                        <TimelineItem
                          v-for="item in historyList"
                          :key="item.creatorId"
                        >
                          <div class="time">
                            <div class="timeContent">
                              <div>{{ item.handleTime }}</div>
                              <div>操作人：{{ item.creatorName }}</div>
                            </div>
                          </div>
                          <div class="content">
                            <div class="content1">
                              <div class="p">
                                <span>处理操作：</span>
                                <div>
                                  <span
                                    >设为{{
                                      item.operation == 1 ? '"有效"' : '"无效"'
                                    }}</span
                                  >
                                </div>
                              </div>
                              <div class="p">
                                <span>处理意见：</span>
                                <div>{{ item.remark || "--" }}</div>
                              </div>
                            </div>
                          </div>
                        </TimelineItem>
                      </Timeline>
                    </div>
                  </Poptip>
                </div>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <div class="level-title">
              <img
                v-if="detailsInfo.bgIndex"
                class="level"
                :src="
                  require(`@/assets/img/target/title${detailsInfo.bgIndex}.png`)
                "
                alt
              />
              <div class="num">
                {{
                  detailsInfo.taskLevel == 1
                    ? "一级"
                    : detailsInfo.taskLevel == 2
                    ? "二级"
                    : "三级"
                }}
              </div>
            </div>
            <details-largeimg
              boxSeleType="rect"
              :info="{ ...detailsInfo }"
              sceneImgKey="picUrl"
              :btnJur="['tp', 'rl', 'ytst', 'ss', 'lx', 'fd', 'sx', 'xz']"
            >
            </details-largeimg>
          </div>
        </div>
      </section>
      <footer>
        <Button @click="$emit('close')">取消</Button>
        <Button
          class="margin"
          style="margin-left: 20px"
          type="primary"
          @click="save()"
          >保存</Button
        >
      </footer>
    </div>
  </div>
</template>

<script>
import { alarmBatchHandle,getAlarmHandleList } from "@/api/semantic-placement"
import detailsLargeimg from "@/components/detail/details-largeimg.vue";
import { getTaskTypeName } from "./enum";

const operationTypeSrcMap = {
  0: "unproces",
  1: "valid",
  2: "invalid",
};
export default {
  name: "detailMutiAnalysis",
  // 这边递归调用了
  components: { detailsLargeimg },
  props: {
    detailsInfo: {
      type: Object,
      default: () => ({}),
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    iniTableIndex: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      tableIndex :0,
      mainLabel: [
        { label: "时间", key: "alarmTime", rule: null },
        {
          label: "地点",
          key: "location",
          rule: (val, data) => {
            return data?.deviceInfo?.name || data?.resourceInfo?.resourceName;
          },
        },
        {
          label: "任务类型",
          key: "taskParsingType",
          rule: (val) => {
            return getTaskTypeName(val);
          },
        },
        { label: "任务名称", key: "taskName", rule: null },
        {
          label: "算法名称",
          key: "algorithmName",
        },
      ],
      alarmInfo: {
        remark: "", // 处理意见
        operationType: "",
      },
      historyList: [],
    };
  },
  mounted() {
    this.tableIndex = this.iniTableIndex;
    this.initAlarmInfo();
  },
  methods: {
    initAlarmInfo(){
      this.alarmInfo.remark = "";
      this.alarmInfo.operationType = Number(this.detailsInfo.operationType || 1);
    },
    operationTypeSrc(operationType) {
      return require(`@/assets/img/target/${
        operationTypeSrcMap[operationType] || "unproces"
      }2.png`);
    },
    /**
     * 处理历史
     */
    showHistory() {
      getAlarmHandleList(this.detailsInfo.alarmId).then((res) => {
        this.historyList = res?.data || [];
      });
    },
    switchItem(index){
      this.tableIndex += index;
      this.$emit("update");
      // async 语法糖
      this.$emit("update:detailsInfo", this.tableList[this.tableIndex]);
      this.initAlarmInfo();
    },
    save() {
      if (this.alarmInfo.operationType === 0) {
        this.$Message.warning("请选择处理状态");
        return;
      }
      const param = {
        alarmIds: [this.detailsInfo.alarmId],
        operationType: this.alarmInfo.operationType,
        remark: this.alarmInfo.remark,
      };
      alarmBatchHandle(param).then((res) => {
        if(res.code == 200){
          this.$Message.success("处理成功");
          this.$emit("close", true);
        } 
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "~@/components/detail/style/index";
.no-page-height {
  height: fit-content !important;
}
.dom-content {
  padding: 0 20px 10px 20px !important;
}
.dom{
  .icon {
    position: absolute;
    top: 46%;
    z-index: 999;
    width: 50px;
    height: 50px;
    border-radius: 50px;
    /* text-align: center; */
    background: rgba(0, 0, 0, 0.5);
    /* line-height: 50px; */
    font-size: 30px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }
  .leftIcon {
    left: -70px;
    z-index: 9;
  }
  .rightIcon {
    right: -70px;
    z-index: 9;
  }
}
.info-box-left {
  width: 300px !important;
  margin-right: 10px;
  margin-top: 20px;
  .main-info-box {
    border-bottom: 1px dashed #d3d7de;
    .main-item {
      display: flex;
      padding-bottom: 12px;
    }
  }
  .label-info-box {
    padding: 15px 0 10px 0;
    border-bottom: 1px dashed #d3d7de;
    .labels-list {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      padding-top: 10px;
      .label-item {
        background: #ecf4ff;
        border-radius: 2px 2px 2px 2px;
        font-size: 12px;
        color: #2c86f8;
        padding: 1px 6px;
      }
    }
  }
  .image-text-box {
    padding: 15px 0 10px 0;
    .texts-list {
      .text-item {
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.9);
        padding-bottom: 6px;
      }
    }
  }
  .primary-title {
    display: flex;
    align-items: center;
    .point {
      width: 5px;
      height: 5px;
      background: #2c86f8;
      margin-right: 8px;
    }
    span {
      font-weight: bold;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .status {
    margin-top: 8px;
    display: flex;
    .history {
      color: #2c86f8;
    }
    .tip {
      color: #999;
      width: 70px;
      text-align: right;
      line-height: 28px;
    }
  }
}
.info-box-right {
  width: 1260px;
  flex-direction: column;
  .level-title {
    height: 19px;
    position: relative;
    .num {
      position: absolute;
      width: 88px;
      top: 0;
      text-align: center;
      color: #fff;
    }
  }
  .description-box {
    width: 100%;
    height: 100px;
    background: #ebedf1;
    padding: 13px 20px;
    overflow: auto;
    display: flex;
    border: 1px solid #d3d7de;
    border-top: none;
    span {
      flex: 1;
      display: inline-block;
      white-space: nowrap;
      height: 100%;
      font-weight: 700;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
    }
    .desc-content {
      word-break: break-all;
    }
  }
}
.title {
  white-space: nowrap;
  span:first-child {
    font-size: 14px;
    width: 67px;
    color: rgba(0, 0, 0, 0.6);
    display: inline-block;
    white-space: nowrap;
    text-align: justify;
    text-align-last: justify;
    text-justify: inter-ideograph;
  }
}
.content {
  font-weight: bold;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
