<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="重复检测" width="66.14rem" :footerHide="true" @onCancel="reset">
      <transfer-table
        title1="字段拾取"
        title2="字段名列表"
        tip="注:不能为空"
        :columns1="columns1"
        :columns2="columns2"
        :tableData1="propertyList"
        :tableData2="targetList"
        @selectionChange="selectionChange"
        @handleSave="handleSave"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
      ></transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { type: 'selection', width: 60 },
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      propertyList: [],
      columns2: [
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
        {
          title: '重复次数',
          key: 'address',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                value: row.repeatCount,
                number: true,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.repeatCount = val;
                  this.targetList.splice(index, 1, row);
                },
              },
            });
          },
        },
      ],
      targetList: [],
      checkedList: [],
      checkedIds: [],
      topicComponentId: '',
      leftLoading: false,
      rightLoading: false,
    };
  },
  mounted() {},
  methods: {
    async init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getPropertyList();
      this.getDevice();
    },
    async getPropertyList() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicPropertyCheckList, { params });
        this.checkedList = res.data.data.map((item) => {
          let obj = {};
          obj.checkColumnName = item.checkColumnName;
          obj.checkColumnValue = item.checkColumnValue;
          obj.repeatCount = item.repeatCount;
          // item.column = `${item.checkColumnName} (${item.checkColumnValue})`
          this.checkedIds.push(item.checkColumnName);
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        obj.repeatCount = item.repeatCount;
        this.checkedList.forEach((checkedItem) => {
          if (item.propertyName === checkedItem.checkColumnName) {
            obj.repeatCount = checkedItem.repeatCount;
          }
        });
        // obj.column = `${item.propertyColumn} (${item.propertyName})`;
        return obj;
      });
      this.targetList = selection;
    },
    async handleSave() {
      // if (!this.targetList.length) {
      //   return false;
      // }
      try {
        let valid = this.targetList.filter((item) => {
          let reg = /^[1-9]\d*$/;
          if (!reg.test(item.repeatCount)) {
            // this.$Message["error"]({
            //   background: true,
            //   content: "重复次数仅支持输入大于0整数!",
            // });
            this.$Message.error('重复次数仅支持输入大于0整数!');
            return item;
          } else if (!item.repeatCount || typeof item.repeatCount !== 'number') {
            // this.$Message["error"]({
            //   background: true,
            //   content: "请输入重复次数!",
            // });
            this.$Message.error('请输入重复次数!');
            return item;
          }
        });
        if (valid.length) {
          return false;
        }
        let params = {
          topicComponentId: this.topicComponentId,
          propertyCheckList: this.targetList,
        };
        await this.$http.post(governancetheme.updateTopicPropertyCheck, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('重复检测配置成功！');
        // this.$Message["success"]({
        //   background: true,
        //   content: "重复检测配置成功！",
        // });
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
  },
  watch: {},
  components: {
    TransferTable: require('../../components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
