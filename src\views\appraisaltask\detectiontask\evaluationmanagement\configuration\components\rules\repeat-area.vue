<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="重复检测" @onCancel="reset" @query="handleSave">
      <p class="repeat-title f-16 mb-sm">1、请确定待检测字段</p>
      <transfer-repeat
        title1="字段拾取"
        title2="字段名列表"
        tip="注:不能为空"
        :columns1="columns1"
        :columns2="columns2"
        :tableData1="propertyList"
        :tableData2="targetList"
        @selectionChange="selectionChange"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
      ></transfer-repeat>
      <p class="repeat-title f-16 mb-sm mt-sm">2、请确定待检测范围</p>
      <p v-for="(item, index) in areaList" :key="index" class="mb-xs">
        <Checkbox v-if="item.key !== 'mode'" v-model="extraParam[item.key]" :true-value="1" :false-value="0">
          <span class="dis-select base-text-color">{{ `${index + 1}、${item.lable}` }}</span>
        </Checkbox>
        <Checkbox
          v-if="item.key === 'mode'"
          v-model="extraParam.detectionRange.isDetect"
          :true-value="1"
          :false-value="0"
        >
          <span class="dis-select base-text-color">
            {{ `${index + 1}、` }}只在所属【<RadioGroup v-model="extraParam.detectionRange.mode">
              <Radio label="province">省</Radio>
              <Radio label="city">市</Radio>
              <Radio label="area">区</Radio> </RadioGroup
            >】的全部设备中检测重复</span
          >
        </Checkbox>
      </p>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { type: 'selection', width: 50, align: 'center' },
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      propertyList: [],
      columns2: [
        { title: ' ', key: '', width: 20, align: 'center' },
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
        {
          title: '重复次数',
          key: 'address',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                value: row.repeatCount,
                number: true,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.repeatCount = val;
                  this.targetList.splice(index, 1, row);
                },
              },
            });
          },
        },
      ],
      targetList: [],
      checkedList: [],
      checkedIds: [],
      indexRuleId: '',
      taskSchemeId: '',
      leftLoading: false,
      rightLoading: false,
      extraParam: { detectionRange: { isDetect: '0', mode: 'all' } },
      indexConfig: {},
      defaultRep: [
        { checkColumnName: 'deviceId', checkColumnValue: '设备编码', repeatCount: 2 },
        { checkColumnName: 'macAddr', checkColumnValue: 'MAC地址', repeatCount: 2 },
      ],
    };
  },
  computed: {
    areaList() {
      let face = [{ lable: '【人脸卡口】类型的设备只在全部人脸卡口设备中检测重复', key: 'isface' }];
      let vehicle = [{ lable: '【车辆卡口】类型的设备只在全部车辆卡口设备中检测重复', key: 'isVehicle' }];
      let video = [{ lable: '【视频监控】类型的设备只在全部视频监控设备中检测重复', key: 'isVideo' }];
      let defaultArea = [
        { key: 'mode' },
        { lable: '排除物理状态为【不可用】的设备', key: 'isSbwlzt' },
        { lable: '排除白名单设备', key: 'isWhiteList' },
      ];
      let { indexType } = this.indexConfig;

      if (indexType === 'VIDEO_VALID_SUBMIT_QUANTITY' || indexType === 'VIDEO_ACCURACY') {
        return [...video, ...defaultArea];
      } else if (indexType === 'VEHICLE_VALID_SUBMIT_QUANTITY' || indexType === 'VEHICLE_ACCURACY') {
        return [...vehicle, ...defaultArea];
      } else if ((indexType === 'FACE_VALID_SUBMIT_QUANTITY') | (indexType === 'FACE_ACCURACY')) {
        return [...face, ...defaultArea];
      } else {
        return [...face, ...vehicle, ...video, ...defaultArea];
      }
    },
  },
  methods: {
    async init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getPropertyList();
      this.getDevice();
    },
    async getPropertyList() {
      let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
      try {
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let res = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        this.setDefaultValue(res.data.data.rulePropertyList || []);
        this.extraParam = JSON.parse(res.data.data.extraParam || '{"detectionRange":{"isDetect":"0","mode":"all"}}');
      } catch (error) {
        console.log(error);
      }
    },
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        obj.repeatCount = item.repeatCount;
        this.targetList.forEach((checkedItem) => {
          if (item.propertyName === checkedItem.checkColumnName) {
            obj.repeatCount = checkedItem.repeatCount;
          }
        });
        return obj;
      });
      this.targetList = selection;
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let valid = this.targetList.filter((item) => {
          let reg = /^[1-9]\d*$/;
          if (!reg.test(item.repeatCount)) {
            this.$Message.error('重复次数仅支持输入大于0整数!');
            return item;
          } else if (!item.repeatCount || typeof item.repeatCount !== 'number') {
            this.$Message.error('请输入重复次数!');
            return item;
          }
        });
        if (valid.length) {
          return false;
        }
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          rulePropertyList: this.targetList,
          extraParam: JSON.stringify(this.extraParam),
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('重复检测配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
    //基础数据检测规则-空值检测，默认加上资产库的10个字段 2984
    setDefaultValue(val) {
      if (val.length === 0) {
        this.checkedList = JSON.parse(JSON.stringify(this.defaultRep));
        this.checkedIds = this.defaultRep.map((item) => item.checkColumnName);
        return;
      }
      this.checkedIds = val.map((item) => item.checkColumnName);
      this.checkedList = val.map((item) => {
        return {
          checkColumnName: item.checkColumnName,
          checkColumnValue: item.checkColumnValue,
          repeatCount: item.repeatCount,
        };
      });
    },
  },
  watch: {},
  components: {
    TransferRepeat: require('@/components/transfer-repeat').default,
  },
};
</script>
<style lang="less" scoped>
.repeat-title {
  color: var(--color-primary);
}
.repeat-empty {
  @{_deep} .transfer-table {
    height: 500px !important;
    padding: 0;
  }
  @{_deep} .ivu-modal {
    width: 1200px !important;
    .ivu-modal-body {
      height: 600px;
      overflow-y: auto;
      padding: 0 50px 16px 50px;
    }
  }
}
</style>
