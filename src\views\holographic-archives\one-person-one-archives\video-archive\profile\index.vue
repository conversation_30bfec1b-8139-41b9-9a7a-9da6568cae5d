<template>
  <div class="people-archive-container">
    <div class="content">
      <!-- 基础信息 -->
      <BasicInformation :labelType="2" type="video" :baseInfo="baseInfo" />
      <div class="main-information">
        <!-- 推荐信息 -->
        <RecommendedInformationCard
          :id="'recommended_information' + routeParams"
          title="推荐信息"
          :list="recommendedList"
          :loading="recommendedLoading"
          class="m-b10"
        />
        <!-- 人像抓拍 -->
        <PortraitCapture
          :id="'portrait_capture' + routeParams"
          title="人像抓拍"
          :baseInfo="baseInfo"
          :archiveNo="archiveNo"
          :list="portraitCaptureList"
          :loading="portraitCaptureLoading"
          class="m-b10"
        />
        <!-- <Peers
          class="m-b10"
          :archiveNo="archiveNo"
          :id="'peers' + routeParams"
          title="同行人员"
          :get-data-list="getPeerList"
          type="people"
        >
        </Peers> -->
        <!--        <Alarm
          class="m-b10"
          :id="'latest_alarm' + routeParams"
          title="最新报警"
          type="people"
          :archiveNo="archiveNo">
        </Alarm>-->
        <!-- 人车同拍 -->
        <PeopleCarCapture
          :id="'people_car_capture' + routeParams"
          title="人车同拍"
          :list="peopleCarCaptureList"
          :loading="peopleCarCaptureLoading"
          class="m-b10"
        />
        <!-- 位置信息 -->
        <PositionInformation
          :id="'position_information' + routeParams"
          title="位置信息"
          :list="latestLocationList"
          :latestLocationLoading="latestLocationLoading"
          :oftenGoList="oftenGoList"
          :oftenGoLoading="oftenGoLoading"
          :positionPoints="positionPoints"
          :heatData="heatData"
          class="m-b10"
          @on-change="frequentedList"
        />
        <!-- 行为规律 -->
        <LawBehavior
          :id="'law_behavior' + routeParams"
          title="行为规律"
          :timeSlotSeries="timeSlotSeries"
          :activeNumXAxis="activeNumXAxis"
          :activeNumSeries="activeNumSeries"
          :timeSlotLoading="timeSlotLoading"
          :activeNumLoading="activeNumLoading"
          class="m-b10"
          @on-change="lawBehavior"
        />
        <!-- 关系信息 -->
        <RelationshipInfomation
          v-if="graphObj"
          :id="'relationship_information' + routeParams"
          title="关系信息"
          class="m-b10"
          :baseInfo="baseInfo"
          :loading1="peopleTogetherLoading"
          :list1="peopleTogetherList"
          :loading2="byCarTogetherLoading"
          :list2="byCarTogetherList"
        />
      </div>
      <div class="anchor-point-infomation">
        <!-- <Button type="primary" class="export-btn">导出</Button> -->
        <!-- 锚点 -->
        <UiAnchor :anchorLinkList="sumAnchorLinkList" />
      </div>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import mixinWidget from "@/mixins/mixinWidget.js";
import SearchImage from "@/components/search-image/index.vue";
import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information";
import RecommendedInformationCard from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/recommended-information-card";
import PortraitCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/portrait-capture";
import PeopleCarCapture from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/people-car-capture";
import PositionInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/position-information";
import LawBehavior from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/law-behavior";
import RelationshipInfomation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/relationship-infomation.vue";
import Alarm from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/alarm.vue";
import Peers from "@/views/holographic-archives/one-person-one-archives/component/peers.vue";
import UiAnchor from "@/components/ui-anchor";
import {
  getRecommendedInformationList,
  getPortraitCaptureList,
  getPeopleCarCaptureList,
  getLatestLocationList,
  getFrequentedList,
  getBehavioralRulesStatics,
  getActivitiesNumStatics,
  getRelationshipStatistics,
  getPeopleTogether,
  getByCarTogether,
} from "@/api/realNameFile";
import {
  queryFacePeerCountPageListByTimeliness,
  queryNonMotorPeerAnalysisPageList,
} from "@/api/modelMarket";
import { mapGetters } from "vuex";

export default {
  props: {
    baseInfo: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    SearchImage,
    BasicInformation,
    RecommendedInformationCard,
    PortraitCapture,
    PeopleCarCapture,
    PositionInformation,
    LawBehavior,
    RelationshipInfomation,
    UiAnchor,
    Alarm,
    Peers,
  },
  mixins: [mixinWidget],
  data() {
    return {
      archiveNo: "",
      routeParams: "",
      anchorLinkList: [
        { href: "#recommended_information", title: "推荐信息" },
        { href: "#portrait_capture", title: "人像抓拍" },
        { href: "#people_car_capture", title: "人车同拍" },
        // { href: '#peers', title: '同行人员' },
        // { href: '#latest_alarm', title: '最新报警' },
        { href: "#position_information", title: "位置信息" },
        { href: "#law_behavior", title: "行为规律" },
        { href: "#relationship_information", title: "关系信息" },
      ],
      recommendedList: [],
      recommendedLoading: false,
      portraitCaptureList: [],
      portraitCaptureLoading: false,
      peopleCarCaptureList: [],
      peopleCarCaptureLoading: false,
      latestLocationList: [],
      latestLocationLoading: false,
      oftenGoList: [],
      oftenGoLoading: false,
      latestLocationPoints: [],
      positionPoints: [],
      heatData: [],
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
      timeSlotLoading: false,
      activeNumXAxis: {
        data: [1, 2, 3, 4, 5, 6, 7],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
          // rotate: 40
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      activeNumSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [5, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#2C86F8",
          },
        },
        {
          name: "晚上",
          type: "bar",
          stack: "one",
          data: [1, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#F29F4C",
          },
        },
      ],
      activeNumLoading: false,
      peopleTogetherList: [],
      peopleTogetherLoading: false,
      byCarTogetherList: [],
      byCarTogetherLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
    sumAnchorLinkList() {
      if (this.graphObj) {
        return this.anchorLinkList;
      } else {
        return this.anchorLinkList.filter(
          (e) => e.href !== "#relationship_information"
        );
      }
    },
  },
  created() {
    let { archiveNo, source, initialArchiveNo } = this.$route.query;
    this.archiveNo = archiveNo;
    this.routeParams = `?archiveNo=${archiveNo}&source=${source}&initialArchiveNo=${initialArchiveNo}`;
    this.sumAnchorLinkList.forEach((v) => {
      v.href = v.href + this.routeParams;
    });
    // 推荐信息
    this.recommendedLoading = true;
    getRecommendedInformationList({
      archiveNo: this.archiveNo,
      dataType: 2,
    })
      .then((res) => {
        if (res.data && res.data.length) {
          this.recommendedList = res.data;
          this.recommendedList.forEach((v) => {
            if (v.type === "1") {
              // 居住地址
              v.icon = "icon-fangzi";
              v.headerBg = "linear-gradient(144deg, #3CD2AA 0%, #1FAF8A 100%)";
            } else if (v.type === "2") {
              // 手机号码
              v.icon = "icon-shouji1";
              v.headerBg = "linear-gradient(237deg, #EC9240 0%, #F7B93D 100%)";
            } else if (v.type === "4") {
              // 常用车辆
              v.icon = "icon-qiche";
              v.headerBg = "linear-gradient(75deg, #50CED0 0%, #3EA6E5 100%)";
            } else {
              // 关联IMSI
              v.icon = "icon-imsi";
              v.headerBg = "linear-gradient(90deg, #7764E5 0%, #4558BF 100%)";
            }
          });
        }
      })
      .catch(() => {})
      .finally(() => {
        this.recommendedLoading = false;
      }),
      // 人像抓拍
      (this.portraitCaptureLoading = true);
    getPortraitCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 2,
    })
      .then((res) => {
        this.portraitCaptureList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.portraitCaptureLoading = false;
      }),
      // 人车同拍
      (this.peopleCarCaptureLoading = true);
    getPeopleCarCaptureList({
      archiveNo: this.archiveNo,
      dataSize: 5,
      dataType: 2,
    })
      .then((res) => {
        this.peopleCarCaptureList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.peopleCarCaptureLoading = false;
      }),
      // 最新位置
      (this.latestLocationLoading = true);
    getLatestLocationList({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 2,
    })
      .then((res) => {
        this.latestLocationList = res.data;
        this.latestLocationPoints = this.latestLocationList.map((v) => {
          return {
            ...v,
            //   ...v.geoPoint
          };
        });
        // 将最新位置点位和常去地点位合并
        this.positionPoints = this.positionPoints.concat(
          this.latestLocationPoints
        );
        this.frequentedList(1);
      })
      .catch(() => {})
      .finally(() => {
        this.latestLocationLoading = false;
      });
    this.lawBehavior(1);
    // 人人同行
    this.peopleTogetherLoading = true;
    getPeopleTogether({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 2,
      gmsfhm: "12345678952",
      vid: "",
    })
      .then(() => {
        let res = {
          data: [
            {
              myselfPic: require("@/assets/img/demo/face/07.jpg"),
              tigetherName: "张晓丽",
              tigetherPic: require("@/assets/img/demo/face/03.jpg"),
              times: 2,
            },
          ],
        };
        this.peopleTogetherList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.peopleTogetherLoading = false;
      });
    // 乘车同行
    this.byCarTogetherLoading = true;
    getByCarTogether({
      archiveNo: this.archiveNo,
      dataSize: 7,
      dataType: 2,
      gmsfhm: "12345678952",
      vid: "",
    })
      .then(() => {
        let res = {
          data: [
            {
              myselfPic: require("@/assets/img/demo/face/07.jpg"),
              tigetherName: "张晓丽",
              tigetherPic: require("@/assets/img/demo/face/02.jpg"),
              times: 2,
            },
            {
              myselfPic: require("@/assets/img/demo/face/07.jpg"),
              tigetherName: "张晓丽",
              tigetherPic: require("@/assets/img/demo/face/03.jpg"),
              times: 2,
            },
          ],
        };
        this.byCarTogetherList = res.data;
      })
      .catch(() => {})
      .finally(() => {
        this.byCarTogetherLoading = false;
      });
  },
  methods: {
    async getPeerList(params) {
      try {
        return await queryFacePeerCountPageListByTimeliness({
          ...params,
          vid: this.archiveNo,
        });
      } catch (e) {
        console.log(e);
      } finally {
      }
    },
    frequentedList(val, startDate = "", endDate = "") {
      this.oftenGoLoading = true;
      getFrequentedList({
        archiveNo: this.archiveNo,
        dataRange: val,
        dataType: 2,
        startDate: startDate,
        endDate: endDate,
      })
        .then((res) => {
          let { locationList, heatmapList } = res.data;
          this.oftenGoList = locationList;
          // 常去地点位
          let positionPoints = this.oftenGoList.map((v) => {
            if (v.type === 1) {
              // 人脸抓拍位置
              return {
                ...v,
                //   ...v.geoPoint,
                type: "face",
                markerIconUrl: require("@/assets/img/archives/marker_face.png"),
              };
            } else if (v.type === 2) {
              // 车辆抓拍位置
              return {
                ...v,
                ...v.geoPoint,
                type: "vehicle",
                markerIconUrl: require("@/assets/img/archives/marker_vehicle.png"),
              };
            } else if (v.type === 3) {
              // IMSI感知数据位置
              return {
                ...v,
                ...v.geoPoint,
                type: "imsi",
                markerIconUrl: require("@/assets/img/archives/marker_imsi.png"),
              };
            }
          });
          // 将最新位置点位和常去地点位合并
          this.positionPoints =
            this.latestLocationPoints.concat(positionPoints);
          // 常去地热力图
          this.heatData = heatmapList.map((v) => {
            return {
              ...v.geoPoint,
              numCount: v.times,
            };
          });
        })
        .catch(() => {})
        .finally(() => {
          this.oftenGoLoading = false;
        });
    },
    lawBehavior(val, startDate, endDate) {
      // 活动时间段
      this.timeSlotLoading = true;
      getBehavioralRulesStatics({
        archiveNo: this.archiveNo,
        dataType: 2,
        type: val,
        startDate,
        endDate,
      })
        .then((res) => {
          this.timeSlotSeries[0].data = [];
          // 后端返回白天、晚上开始时间格式为"白天开始时间-晚上开始时间"
          let dayStart = res.data.daytimeRange.split("-")[0];
          let dayEnd = res.data.daytimeRange.split("-")[1];
          let timeList = res.data.x;
          let dataList = res.data.y;
          dataList.forEach((v, i) => {
            // 当前时间》=白天开始时间且《晚上开始时间则为白天，否则为晚上
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#F29F4C",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
      // 活动次数
      this.activeNumLoading = true;
      getActivitiesNumStatics({
        archiveNo: this.archiveNo,
        dataType: 2,
        type: val,
        startDate,
        endDate,
      })
        .then((res) => {
          if (val === 2) {
            this.activeNumXAxis.axisLabel.rotate = 0;
          } else {
            this.activeNumXAxis.axisLabel.rotate = 40;
          }
          this.activeNumXAxis.data = res.data.x;
          this.activeNumSeries[0].data = res.data.day;
          this.activeNumSeries[1].data = res.data.night;
        })
        .catch(() => {})
        .finally(() => {
          this.activeNumLoading = false;
        });
    },
    beforeFaceControl(row) {
      let photoUrl = row.photo || row.bigImageUrl;
      if (!photoUrl) return this.$Message.error("图片不存在");
      this.onPeopleControl(row, photoUrl);
    },
    beforeFaceSearchImage(row) {
      let photoUrl = row.photo || row.bigImageUrl;
      if (!photoUrl) return this.$Message.error("图片不存在");
      this.onSearchImage(row, photoUrl, "1");
    },
  },
};
</script>
<style lang="less" scoped>
.m-b10 {
  margin-bottom: 10px;
}
.people-archive-container {
  padding: 16px 10px 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
  .content {
    display: flex;
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
    flex: 1;
    .main-information {
      width: 1442px;
      height: min-content;
      padding: 0 10px;
      margin-left: 350px;
      /deep/ .ui-card {
        overflow: unset !important;
        .card-head {
          overflow: hidden;
          border-top-left-radius: 4px;
        }
      }
    }
    .anchor-point-infomation {
      width: 100px;
      position: fixed;
      top: 78px;
      right: 18px;
      z-index: 9;
      .export-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
