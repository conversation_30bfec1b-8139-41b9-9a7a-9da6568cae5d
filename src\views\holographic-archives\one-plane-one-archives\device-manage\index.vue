<template>
  <div class="map-container">
    <MapBase ref='map' class="map" :mapLayerConfig="{ showLatestLocation: true , } " />

    <device-tree class="device-tree" @deviceClick="deviceClick"></device-tree>
  </div>
</template>

<script>
import { mapActions, mapMutations, mapGetters } from 'vuex'
import MapBase from '@/components/map/map-device-manage.vue'
import deviceTree from './components/device-tree.vue'
import { getDeviceById } from '@/api/frame'

export default {
  components: {
    MapBase,
    deviceTree
  },
  activated() {
    this.$store.commit('common/setPageType', 1);
    this.setLayoutNoPadding(true)
  },
  deactivated() {
    this.setLayoutNoPadding(false)
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false)
  },
  methods: {
		...mapMutations('admin/layout', ['setLayoutNoPadding']),
    async deviceClick(item) {
      let res = await getDeviceById(item.deviceId)
      if (res.code == 200) this.$refs.map.addMarkers(res.data)
    }
  }
}
</script>

<style lang="less" scope>
.map-container {
  height: 100%;
  width: 100%;
  position: relative;
  .device-tree {
    position: absolute;
    top: 20px;
    left: 20px;
    width: 400px;
  }
}
</style>