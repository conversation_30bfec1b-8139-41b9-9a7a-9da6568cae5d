<template>
  <div class="btn-bar">
    <Button v-if="queryType === '1'" type="primary" class="ml-sm" @click="handleClick('add')">
      <i class="icon-font icon-tianjia f-14"></i>
      <span class="vt-middle ml-sm">新增报备</span>
    </Button>
    <Button
      v-if="queryType === '2'"
      :disabled="!batchIds.length"
      type="primary"
      class="ml-sm"
      @click="handleClick('audit')"
    >
      <i class="icon-font icon-shanchu f-14"></i>
      <span class="vt-middle ml-sm">批量审核</span>
    </Button>
    <Button type="primary" class="ml-sm" :loading="exportLoading" @click="handleClick('export')">
      <i class="icon-font icon-daochu f-14"></i>
      <span class="vt-middle ml-sm">导出</span>
    </Button>
    <Button
      v-if="queryType === '1'"
      :disabled="!canHandleIds.length"
      type="primary"
      class="ml-sm"
      @click="handleClick('delete')"
    >
      <i class="icon-font icon-shanchu f-14"></i>
      <span class="vt-middle ml-sm">批量删除</span>
    </Button>
  </div>
</template>

<script>
export default {
  name: 'btn-bar',
  props: {
    queryType: {
      type: String,
      default: '1',
    },
    exportLoading: {
      type: Boolean,
      default: false,
    },
    batchIds: {
      type: Array,
      default: () => [],
    },
    canHandleIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  methods: {
    handleClick(type) {
      this.$emit('handleBtnClick', type);
    },
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
</style>
