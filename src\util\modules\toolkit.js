"use strict";
import axios from "axios";
import dayjs from "dayjs";
import { Message } from "view-design";
import { fetchVod, getChannel, getCipherChannel } from "@/api/player";
import { generateUserToken, getResourceId } from "@/api/work";
import util from "@/libs/configuration/util.js";
import store from "@/store";

window.Toolkits = {};
var /**
   * 格式化长度
   * @param x - 带格式化的值
   * @param len - 要格式化的长度
   * @returns {string|*}
   * @private
   **/
  _formatLenth = function (x, len) {
    x = "" + x;
    len = len || 2;
    while (x.length < len) {
      x = "0" + x;
    }
    return x;
  };
/**
 * 获取文件类型
 * @param fileName - 文件类型名字，转换为对应的标示数值
 * @returns {number} - 返回标示数值
 */
Toolkits.getFileType = function (fileName) {
  //0:video 1:img 2:word 3:zip 4:other
  var fileType = fileName.split(".")[1];
  fileType = fileType.toLowerCase();
  if (
    fileType == "mp4" ||
    fileType == "mp3" ||
    fileType == "avi" ||
    fileType == "rm" ||
    fileType == "asf" ||
    fileType == "wmv" ||
    fileType == "mov" ||
    fileType == "rmvb" ||
    fileType == "flv" ||
    fileType == "mbf"
  ) {
    return 0;
  } else if (
    fileType == "png" ||
    fileType == "jpg" ||
    fileType == "jpeg" ||
    fileType == "gif" ||
    fileType == "bmp"
  ) {
    return 1;
  } else if (
    fileType == "txt" ||
    fileType == "doc" ||
    fileType == "xls" ||
    fileType == "pdf" ||
    fileType == "ppt" ||
    fileType == "docx" ||
    fileType == "xlsx" ||
    fileType == "pptx"
  ) {
    return 2;
  } else if (fileType == "zip" || fileType == "rar") {
    return 3;
  } else {
    return 4;
  }
};

//时间戳格式化
Toolkits.mills2datetime = function (num) {
  var date = new Date(num);
  return (
    date.getFullYear() +
    "-" +
    _formatLenth(date.getMonth() + 1) +
    "-" +
    _formatLenth(date.getDate()) +
    " " +
    _formatLenth(date.getHours()) +
    ":" +
    _formatLenth(date.getMinutes()) +
    ":" +
    _formatLenth(date.getSeconds())
  );
};

//http解析 特殊符号转义
Toolkits.replacehttpparam = function (param) {
  param = param.replace(/\%/g, "%25"); //替换%
  param = param.replace(/\+/g, "%2B"); //替换+
  param = param.replace(/[ ]/g, "%20"); //替换空格
  param = param.replace(/\?/g, "%3F"); //替换？
  param = param.replace(/\&/g, "%26"); //替换&
  param = param.replace(/\=/g, "%3D"); //替换=
  param = param.replace(/\#/g, "%23"); //替换#
  return param;
};
Toolkits.replaceparamtohttp = function (param) {
  param = param.replace(/\%25/g, "%"); //替换%
  param = param.replace(/\%2B/g, "+"); //替换+
  param = param.replace(/\%20/g, " "); //替换空格
  param = param.replace(/\%3F/g, "?"); //替换？
  param = param.replace(/\%26/g, "&"); //替换&
  param = param.replace(/\%3D/g, "="); //替换=
  param = param.replace(/\%23/g, "#"); //替换#
  return param;
};
/**
 * 资源下载
 * @param type - 下载文件类型（pfs,lis,pvg）
 * @param fileType - 下载资源可识别类型（video、image、word、zip）
 * @param path - 下载资源路径（举例：pfs文件：/video/2CADD9F0-C873-451B-ADC3-2DA90D0D526E.mbf;lis文件：http://**************:11111/images
                                 /changzhouAll/changzhou197/0/2/20151208.195541.苏DE1390.999.*********.62131348.46.jpg）
    * @param fileName - 资源名称(如果下载录像，则接受的文件名参数带.mbf,目前ocx只支持.mbf文件)
    * @param fileData - 其他相关资源信息
    *例子:fileData = {beginTime : data.ocxObj.beginTime,endTime : data.ocxObj.endTime,ip:Toolkits.URLencode(data.ocxObj.ip),password:Toolkits.URLencode(data.ocxObj.passwd), port:data.ocxObj.port,username:Toolkits.URLencode(data.ocxObj.user)};

    */
Toolkits.ocxUpDownHttp = async function (
  type,
  fileType,
  path,
  fileName,
  fileData,
  id,
  progressType,
  timeout,
  speed,
  pfsIp
) {
  var self = this,
    storagemodeType = 0,
    param = {},
    pfsPrams = "",
    pathTemp = "",
    downType = type;
  try {
    if (path.split(":")[0].toUpperCase() == "PFSB") {
      storagemodeType = 1;
    }
    if (path.split(":")[0].toUpperCase() == "PFS") {
      storagemodeType = 0;
    }
    // 如果路径为全路径方式，则为lis方式
    if (!Toolkits.isProxy(path)) downType = "lis";
    if (downType == "lis") {
      // let paths = path.split("/");
      //图片名如果存在中文乱码，通过utf-8转码
      // pathTemp = `${paths[0]}//${paths[2]}/${paths[3]}/${paths[4]}/${encodeURIComponent(path.split("/")[5])}`;
      param = {
        src: {
          type: downType,
          filetype: fileType, //可识别的类型 video、image、word、zip
          url: path.replace(/[\u4e00-\u9fa5]/g, (a, b, c) => {
            return encodeURIComponent(a);
          }),
        },
        filename: fileName,
      };
    } else if (type == "pfs") {
      pfsPrams = window.pfsInfo;
      pathTemp = Toolkits.urlDownload(path); //去掉前缀pfsb和pfs
      param = {
        src: {
          type: type,
          filetype: fileType, //可识别的类型 video、image、word、zip
          ip: pfsIp ? pfsIp : pfsPrams.server.ip,
          port: parseInt(pfsPrams.server.port),
          username: pfsPrams.server.userName,
          password: pfsPrams.server.password,
          path: pathTemp,
          temppath: pathTemp + "..temp",
          storagemode: storagemodeType,
        },
        filename: fileName,
        id: id,
        progressType: progressType,
        timeout: timeout,
        setspeed: speed,
      };
      if (fileData) {
        param.src.temppath = fileData.tempFilePath;
      }
    } else if (downType === "pvg" || downType === "pvgplus") {
      pathTemp = path;
      param = {
        src: {
          type: downType, //或者 "pvgplus"
          filetype: fileType,
          ip: fileData.ip,
          port: parseInt(fileData.port),
          username: fileData.username,
          password: fileData.password,
          path: pathTemp,
          vodtype: fileData.vodType || 0,
          enabledes: true,
          begintime: fileData.beginTime,
          endtime: fileData.endTime,
        },
        filename: fileName, //目前仅支持MBF
      }
    } else if (type === "minio") {
      param = {
        src: {
          type: type, //minio
          ip: fileData.ip,
          port: parseInt(fileData.port),
          accessKey: fileData.accessKey,
          secretKey: fileData.secretKey,
          url: fileData.url
        },
        filename: fileName,
        // downloadpath: downloadpath
      }
    }
    // $log("systemlog", "systemlog_download", $log.TYPES.DOWNLOAD, `下载资源：【${fileName}】`)
    //console.log("m16", "下载资源：" + fileName + "，下载方式（pfs,lis-本地,pvg）：" + downType);
  } catch (err) {
    console.log(err, "err");
  }
  //console.log(param, "vpupload");
  //var tempParam = downType === "pvg" ? JSON.stringify(param) : Toolkits.replacehttpparam(JSON.stringify(param))
  var tempParam = Toolkits.replacehttpparam(JSON.stringify(param));
  var startdownload =
    "http://127.0.0.1:9124/download/startdownload?param=" + tempParam;
  //下载资源
  Toolkits.jsonpAjax(startdownload, "", function (data) {}.bind(this));
};

/**
 * [url去掉前缀pfs或者PFSB]
 * @param  {[type]} url [description]
 * @return {[type]}      [description]
 */
Toolkits.urlDownload = function (url) {
  try {
    if (url) {
      var result = "";
      if (url.indexOf("http") !== -1) {
        result = url.split("=")[1].split(":")[1];
      } else {
        if (url.indexOf(":") !== -1) {
          result = url.split(":")[1];
        } else {
          result = url;
        }
      }
      return result;
    } else {
      return null;
    }
  } catch (err) {
    return null;
  }
};

/**
 * [通过OCX获取复制目标，实现复制功能]
 * @param  {[type]} data [参数]
 * @return {[type]}      [description]
 */
Toolkits.jsonpAjax = function (url, param, cbFn) {
  axios.get(url, {}).then((res) => {
    cbFn && cbFn(res);
  });
};

// 支持设置scriptCharset,因为服务端编码为gb2312,需要设置以防止返回的文件名乱码
Toolkits.jsonpAjaxChar = function (url, param, scriptCharset) {
  return new Promise((resolve, reject) => {
    axios({
      method: "post",
      url,
      data: param,
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
    }).then((res) => {
      resolve(res.data);
    });
  });
};

/**
 * [通过上传下载工具组件获取文件相关进度]
 * @param  {[type]} data [参数] param={"filename":  filename,"taskid": "E11DAAD4-3F0F-4EFA-BA0F-CD368DAA9D71"}
 * @return {[type]}      [description]url[ip:port/download/getdownloadstatus]
 */
Toolkits.jsonpAjaxStatus = function (url, param, cbFn) {
  axios.get(url, {}).then((res) => {
    cbFn && cbFn(res);
  });
};

/**
 * [url地址] -是否代理
 * @param  {[String]} url   [地址]
 * @return {[string]} [是否代理]
 */
Toolkits.isProxy = function (url) {
  return url.indexOf("http") === -1 ? true : false;
};

/**
 * 获取文件类型
 * @param videoObj - 播放器实例
 * @param deviceGbId - 国标id
 * @param searchTime - 录像时间数组
 * @returns {List} - 返回录像数组
 */
Toolkits.searchVodList = function (videoObj, deviceGbId, searchTime) {
  return new Promise((resolve) => {
    let endTime = "",
      startTime = "";
    if (searchTime.length > 0) {
      startTime = searchTime[0];
      endTime = searchTime[1];
    }
    if (vodType == "pvg67" || vodType == "pvgplus") {
      let requestUrl = desencode == 1 ? getCipherChannel : getChannel;
      requestUrl(deviceGbId).then((res) => {
        if (res.code != 200 || !res.data) {
          resolve([]);
          return;
        }
        let { channelNo, ip, port, userName, password } = res.data;
        let record = {
          begintime: dayjs(startTime).format("YYYY-MM-DD HH:mm:ss:SSS"),
          channel: channelNo,
          desencode: desencode,
          devicetype: vodType,
          endtime: dayjs(endTime).format("YYYY-MM-DD HH:mm:ss:SSS"),
          ip: ip,
          password: password,
          port: parseInt(port),
          streamtype: "vod",
          user: userName,
          vod: vodStorage == "device" ? 1 : 0,
        };
        videoObj.queryRecord(record, (res) => {
          try {
            if (res.error != 0) {
              Message.error(
                "查询录像失败！" + videoObj.errorCodeToString(res.error)
              );
              resolve([]);
              return;
            }
            let obj = JSON.parse(res.info);
            if (!obj) {
              resolve([]);
              return;
            }
            resolve(obj);
          } catch (e) {}
        });
      });
    } else {
      let params = {
        endtime: endTime,
        starttime: startTime,
        channel: deviceGbId,
        download: true,
        storage: vodStorage,
      };
      fetchVod(params).then((res) => {
        if (res.code === 200) {
          resolve(res.RecordList || []);
        }
      });
    }
  });
};

/**
 *  函数节流
 *  函数节流的要点是，声明一个变量当标志位，记录当前代码是否在执行。
 *  如果空闲，则可以正常触发方法执行。
 *  如果代码正在执行，则取消这次方法执行，直接return。
 * */
Toolkits.FnByFlow = function (timer, callback) {
  if (!Toolkits.canRun) {
    return;
  }
  Toolkits.canRun = false;
  setTimeout(function () {
    Toolkits.canRun = true;
    callback && callback();
  }, timer);
};

Toolkits.ShakeTimer = null;
/**
 *  函数防抖
 *  函数节流的要点，也是需要一个setTimeout来辅助实现。延迟执行需要跑的代码。
 *  如果方法多次触发，则把上次记录的延迟执行代码用clearTimeout清掉，重新开始。
 *  如果计时完毕，没有方法进来访问触发，则执行代码。
 * */
Toolkits.FnByShake = function (timer, callback) {
  //清除已经存在的延迟执行定时器
  if (Toolkits.ShakeTimer) clearTimeout(Toolkits.ShakeTimer);
  Toolkits.ShakeTimer = setTimeout(function () {
    callback && callback();
  }, timer);
};
/**
 * 判断对象是否为空
 * @param obj - 待判断的对象
 * @returns {boolean} - true为空，false不为空
 */
Toolkits.isEmptyObject = function (obj) {
  for (var name in obj) {
    if (obj.hasOwnProperty(name)) {
      return false; //返回false，不为空对象
    }
  }
  return true; //返回true，为空对象
};

/**
 * 判断对象除了某些属性外是否为空
 * @param obj - 待判断的对象
 * @param key - 需排除掉的属性集合
 * @returns {boolean} - true为空，false不为空
 */
Toolkits.isEmptyObjectExKeys = function (obj, keys = []) {
  for (var name in obj) {
    if (!keys.includes(name) && obj.hasOwnProperty(name)) {
      return false; //返回false，不为空对象
    }
  }
  return true; //返回true，为空对象
};

/**
 * 工单-新增跳转
 * @param  {[String]} account   [用户名]
 * @param  {[String]} gbid [gbid]
 */
Toolkits.toServiceCatalog = async function (account, gbid) {
  let token = await generateUserToken(account);
  const resId = await getResourceId(gbid);
  if (!token.data) {
    Message.error("免密登录失败");
    return;
  }
  if (!resId.data || !resId.data.length) {
    Message.error("未获取到对应内码");
    return;
  }
  let targetUrl = `https://34.54.66.110/iomwos/orderService/orders/-/new?resId=${resId.data[0].resourceId}`;
  let url = `https://34.54.66.110:443/ctm01freesso/v1/login?token=${token.data}&target=${targetUrl}`;
  console.log(url);
  util.openNewPage(url, "_blank");
};

/**
 * 工单-列表跳转
 * @param  {[String]} account   [用户名]
 * @param  {[String]} channelId [gbid]
 */
Toolkits.toProcessOrder = async function (account, gbid) {
  let token = await generateUserToken(account); // 340302199706180823
  const resId = await getResourceId(gbid);
  if (!token.data) {
    Message.error("免密登录失败");
    return;
  }
  if (!resId.data || !resId.data.length) {
    Message.error("未获取到对应内码");
    return;
  }
  let targetUrl = `https://34.54.66.110/iomwos/orderService/my-orders?resId=${resId.data[0].resourceId}`;
  let url = `https://34.54.66.110:443/ctm01freesso/v1/login?token=${token.data}&target=${targetUrl}`;
  console.log(url);
  util.openNewPage(url, "_blank");

  // const height = window.screen.height
  // const width = window.screen.width
  // window.open(
  //     url,
  //     '_blank',
  //     `width=${width*0.6},height=${height*0.6},top=${height*0.2},left=${width*0.2},toolbar=no,menubar=no,scrollbars=yes,resizable=yes,location=no,status=no,depended=true`
  // );
};

/**
 * 根据设备类型获取设备图标
 * [deviceInfo] - 设备信息
 */
Toolkits.getDeviceIconType = function (deviceInfo) {
  const individuation = store.getters.individuation;
  let icon = "icon-gps";
  if (deviceInfo.deviceType == "1") {
    // 摄像机
    if (
      deviceInfo.deviceChildType == "1" ||
      deviceInfo.deviceChildType == "2"
    ) {
      icon = individuation?.deviceIcons?.qiuji?.className || "icon-qiuji";
    } else {
      icon =
        individuation?.deviceIcons?.qiangji?.className || "icon-shebeizichan";
    }
  } else if (deviceInfo.deviceType == "2") {
    // 车辆卡口
    icon = "icon-qiche1";
  } else if (deviceInfo.deviceType == "3") {
    // wifi
    icon = "icon-mac";
  } else if (deviceInfo.deviceType == "4") {
    // 电子围栏
    icon = "icon-dianwei1";
  } else if (deviceInfo.deviceType == "5") {
    // rfid
    icon = "icon-rfid";
  } else if (deviceInfo.deviceType == "11") {
    // 人脸抓拍机
    icon = "icon-renlian1";
  }
  return icon;
};

/**
 * 根据设备类型获取设备图标颜色
 * [deviceInfo] - 设备信息
 */
Toolkits.getDeviceIconColor = function (deviceInfo) {
  let color = "#2c86f8";
  const individuation = store.getters.individuation;
  if (deviceInfo.deviceType == "1") {
    // 摄像机
    if (
      deviceInfo.deviceChildType == "1" ||
      deviceInfo.deviceChildType == "2"
    ) {
      color = individuation?.deviceIcons?.qiuji?.color || "#2c86f8";
    } else {
      color = individuation?.deviceIcons?.qiangji?.color || "#2c86f8";
    }
  } else {
    // 其他设备
    color = "#2c86f8";
  }
  return color;
};

/**
 * 图片拖拽功能
 * @param imgId - img元素的id
 * @param parentId - img元素的父元素id
 * @param isLimitedDrag - 是否限制边框拖动
 */
Toolkits.drag = function (
  imgId,
  parentId,
  isLimitedDrag,
  startCallBack,
  endCallBack
) {
  var locked = false;
  var lastObj = undefined;
  var $obj = document.getElementById(imgId);
  var $parentObj = isLimitedDrag
    ? document.body
    : document.getElementById(parentId) ||
      document.getElementsByClassName(parentId)[0];
  var tempX = 0;
  var tempY = 0;
  var x = 0;
  var y = 0;
  $obj.onmousedown = function (e) {
    e = e ? e : window.event;
    if (!window.event) {
      e.preventDefault();
    }
    /* 阻止标注浏览器下拖动a,img的默认事件 */
    if (isLimitedDrag) {
      if (
        window.getComputedStyle(e.target, null).getPropertyValue("cursor") ===
        "move"
      ) {
        locked = true;
      }
    } else {
      locked = true;
    }

    if (lastObj && lastObj != $obj) {
      /* 多元素拖动时候需要恢复上一次元素的状态 */
      lastObj.style.zIndex = "1";
    }
    lastObj = $obj;
    tempX = $obj.offsetLeft;
    tempY = $obj.offsetTop;
    x = e.clientX;
    y = e.clientY;
    startCallBack && startCallBack();
    $parentObj.onmousemove = function (e) {
      e = e ? e : window.event;
      if (locked == false) return false;

      if (!isLimitedDrag) {
        $obj.style.position = "absolute";
        $obj.style.zIndex = "100";
        $obj.style.cursor = "move";
        $obj.style.transition = "all 0s ease 0s";
      }

      // 限制拖拽范围
      var tmpObjLeft = tempX + e.clientX - x,
        tmpObjTop = tempY + e.clientY - y;

      if (isLimitedDrag) {
        $obj.style.marginLeft = "auto";
        $obj.style.marginTop = "auto";
        $obj.style.bottom = "auto";
        $obj.style.right = "auto";
        if (tmpObjTop <= 0) {
          tmpObjTop = 0;
        } else if (tmpObjTop + $obj.offsetHeight >= $parentObj.offsetHeight) {
          tmpObjTop = $parentObj.offsetHeight - $obj.offsetHeight;
        }
        if (tmpObjLeft <= 0) {
          tmpObjLeft = 0;
        } else if (tmpObjLeft + $obj.offsetWidth >= $parentObj.offsetWidth) {
          tmpObjLeft = $parentObj.style.offsetWidth - $obj.style.offsetWidth;
        }
      }

      $obj.style.left = tmpObjLeft + "px";
      $obj.style.top = tmpObjTop + "px";
      if (window.event) {
        e.returnValue = false;
      }
      /* 阻止ie下a,img的默认事件 */
    };

    $parentObj.onmouseup = function () {
      locked = false;
      $parentObj.onmousemove = null;
      $parentObj.onmouseup = null;
      if (!isLimitedDrag) {
        $obj.style.cursor = "auto";
      }
      endCallBack && endCallBack();
    };
  };
};
