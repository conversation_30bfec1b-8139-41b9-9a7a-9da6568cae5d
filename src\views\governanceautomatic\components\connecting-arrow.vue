<template>
  <div
    class="connecting-arrow"
    :style="{
      width: connectingOptions ? connectingOptions.width : '0',
      top: connectingOptions ? connectingOptions.top : 0,
      bottom: connectingOptions ? connectingOptions.bottom : 0,
      left: connectingOptions ? connectingOptions.left : 0,
      transform: `rotate(${connectingOptions ? connectingOptions.angle : 0}deg)`,
    }"
  >
    <div
      class="rect"
      :style="{
        height: connectingOptions ? connectingOptions.height : '0',
      }"
    ></div>
    <div
      class="triangle"
      :style="{
        borderLeft: `${connectingOptions ? connectingOptions.height : '0'} solid var(--color-primary)`,
        borderTop: `${connectingOptions ? connectingOptions.height : '0'} solid transparent`,
        borderBottom: `${connectingOptions ? connectingOptions.height : '0'} solid transparent`,
      }"
    ></div>
  </div>
</template>
<script>
export default {
  props: {
    connectingOptions: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.connecting-arrow {
  position: absolute;
  display: flex;
  .rect {
    width: 100%;
    background: var(--color-primary);
  }
  .triangle {
    width: 0px;
    height: 0px;
    margin-top: -4px;
  }
}
</style>
