<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="空值检测" width="66.14rem" @onCancel="resetHandle" @query="handleSave">
      <transfer-table
        class="tt"
        :left-table-columns="columns1"
        :right-table-columns="columns2"
        :left-table-data="propertyList"
        :right-table-data="targetList"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @onLeftToRight="selectionChange"
      >
        <template #left-title>
          <div class="mb-sm">
            <span class="base-text-color">字段拾取</span>
          </div>
        </template>
        <template #right-title>
          <div class="mb-sm">
            <span class="base-text-color">字段名列表</span><span class="font-red ml-sm">(注:不能为空)</span>
          </div>
        </template>
      </transfer-table>
    </ui-modal>
  </div>
</template>
<script>
import cascade from '@/config/api/cascade';

export default {
  data() {
    return {
      visible: false,
      formData: {
        list: [],
        ruleId: '',
      },
      columns1: [
        { title: '字段名', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
      ],

      columns2: [
        { title: ' ', key: '', width: 14 },
        { title: '字段名', key: 'checkColumnName' },
        { title: '注释', key: 'checkColumnValue' },
      ],
      propertyList: [],
      targetList: [], // 字段名列表
      indexRuleId: '',
      leftLoading: false,
      rightLoading: false,
    };
  },
  methods: {
    async init({ indexRuleId }) {
      this.indexRuleId = indexRuleId;
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      this.getInitData();
    },
    // 获取原始数据
    async getInitData() {
      try {
        let params = { ruleId: this.indexRuleId };
        let {
          data: { data },
        } = await this.$http.post(cascade.configQueryAllData, params);
        let leftMap = data.leftMap;
        let dataRight = data.right;
        this.formData.list = dataRight;
        this.targetList = dataRight.filter((item) => item.id);
        this.propertyList = Object.keys(leftMap).map((key) => {
          let _checked = dataRight.some((item) => item.checkColumnName === key);
          return {
            propertyName: key,
            _checked,
            propertyColumn: leftMap[key],
          };
        });
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.error(error);
      }
    },
    selectionChange(selection) {
      this.targetList = selection.map((item) => {
        return {
          checkColumnName: item.propertyName,
          checkColumnValue: item.propertyColumn,
        };
      });
    },
    // 保存
    async handleSave() {
      try {
        let resultFromData = { ...this.formData };
        resultFromData.list = this.targetList;
        resultFromData.ruleId = this.indexRuleId;
        await this.$http.post(cascade.configUpdateAllData, resultFromData);
        this.$Message.success('空值检测配置成功');
        this.visible = false;
      } catch (e) {
        console.error(e);
      }
    },
    resetHandle() {
      this.visible = false;
      this.propertyList = [];
      this.targetList = [];
    },
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .transfer-table-wrapper {
  .left-table,
  .right-table {
    height: calc(~'100% - 35px') !important;
    margin-top: 10px;
  }
}

.repeat-empty {
  @{_deep} .ivu-modal-body {
    height: 500px;
    padding: 0 50px 16px 50px;
  }
}
</style>
