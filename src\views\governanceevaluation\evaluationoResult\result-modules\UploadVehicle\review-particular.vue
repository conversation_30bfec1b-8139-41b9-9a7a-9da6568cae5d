<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      v-bind="customizedAttrs"
      @handlePageSize="handlePageSize"
      @handlePage="handlePage"
      @startSearch="startSearch"
      @changeMode="changeMode"
    >
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
      <!-- 表格插槽 -->
      <template #qualifiedRate="{ row }">
        <span>{{ row.qualifiedRate || 0 }}%</span>
      </template>
      <template slot="outcome" slot-scope="{ row }">
        <Tag v-if="row.outcome in qualifiedColorConfig" :color="qualifiedColorConfig[row.outcome].color">
          {{ qualifiedColorConfig[row.outcome].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-chakantupian"
          content="查看图片"
          @click.native="clickRow(row)"
          class="mr-sm"
        ></ui-btn-tip>
      </template>
    </Particular>
    <CheckPicture
      v-model="checkPicture"
      class="picture"
      :list="currentRow"
      :resultId="CheckPictureParams"
      :tagList="tagList"
      :interface="interface"
    ></CheckPicture>
    <ArtificialReview
      v-model="artificialVisible"
      :artificial-row="artificialRow"
      @update="showRecountBtn"
    ></ArtificialReview>
    <!-- 导出   -->
    <export-data ref="exportModule" :exportLoading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
// 外层公共配置
import {
  qualifiedColorConfig,
  iconStaticsImgList,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { normalFormData, imgFormData, VehicleTableColumns, iconStaticsList } from './util/enum/ReviewParticular.js';
export default {
  mixins: [particularMixin, dealWatch],
  props: {},
  data() {
    return {
      tabList: ['设备模式', '图片模式'],
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: [],
      },
      formItemData: normalFormData,
      tableColumns: Object.freeze(VehicleTableColumns),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      currentRow: {},
      checkPicture: false, //查看图片
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      // 1 - 合格、2 - 不合格 3 - 无法检测
      tagList: Object.keys(qualifiedColorConfig).map((key) => {
        return {
          label: qualifiedColorConfig[key].dataValue,
          outcome: key,
          value: key,
        };
      }),
      cardList: [],
      statisticShow: false,
      activeMode: 'device',
      artificialVisible: false,
      artificialRow: {},
      CheckPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      exportLoading: false,
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async interface(params) {
      try {
        const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          ...params,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, data);
        let entities = res.data.data.entities || [];
        let cardList = entities.map((item) => {
          // item.hasArtificial = true - 人工复核展示
          // qualified为1展示红色
          let tipColor = item.outcome != 1 ? 'var(--color-failed)' : 'var(--color-success)';
          this.$set(item, 'fileList', [
            { label: '抓拍:', value: item.shotTime },
            { label: '接收:', value: item.firstIntoViewTime },
            { label: '', value: item.reason, style: { color: tipColor } },
          ]);
          return item;
        });
        return { cardList, total: res.data.data.total };
      } catch (err) {
        console.log(err);
      }
    },
    initAll() {
      // 获取列表
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
        // 图片模式统计赋值
        iconStaticsImgList.forEach((item) => {
          return (item.count = data[item.fileName]);
        });
      });
    },
    // 获取不合格原因下拉列表
    // mode 1:设备模式，2:图片模式
    getQualificationList(mode = 1) {
      // 异常原因
      this.MixinDisQualificationList(mode).then((data) => {
        if (mode === 1) {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
          findErrorCodes.options = data.map((item) => {
            // 嘉鹏说: 设备模式查询需转换数字模式
            return { value: Number(item.key), label: item.value };
          });
        } else {
          let findErrorCodes = this.formItemData.find((item) => item.key === 'causeErrors');
          findErrorCodes.options = data.map((item) => {
            return { value: item.key, label: item.value };
          });
        }
      });
    },
    // 人工复核
    clickArtificialReview(row) {
      this.artificialRow = row;
      this.artificialVisible = true;
    },
    // 切换模式
    changeMode(type) {
      this.activeMode = type;
      this.totalCount = 0;
      this.pageData.pageNum = 1;
      if (type === 'device') {
        this.iconList = iconStaticsList;
        this.formItemData = normalFormData;
        this.formData = {
          deviceId: '',
          deviceName: '',
          outcome: '',
          errorCodes: [],
        };
        this.getQualificationList();
      } else {
        this.formItemData = imgFormData;
        this.iconList = iconStaticsImgList;
        this.formData = {
          startTime: '',
          endTime: '',
          outcome: '',
          deviceIds: [],
          causeErrors: [],
        };
        this.getQualificationList(2);
      }
      this.startSearch(this.formData);
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.activeMode === 'device' ? this.getTableData() : this.getImageList();
    },
    // 获取列表[mixin的方法]
    selfConfigGetListTotal() {
      this.activeMode === 'device' ? this.getTableDataTotal() : '';
    },
    getImageList() {
      // 处理图片模式列表数据
      this.MixinGetImageList().then((data) => {
        this.cardList = data.entities.map((item) => {
          // item.hasArtificial = true - 人工复核展示
          // qualified为1展示红色
          let tipColor = item.outcome != 1 ? 'var(--color-failed)' : 'var(--color-success)';
          this.$set(item, 'fileList', [
            { label: '抓拍:', value: item.shotTime },
            { label: '接收:', value: item.firstIntoViewTime },
            { label: '', value: item.reason, style: { color: tipColor } },
          ]);
          return item;
        });
        this.totalCount = data.total;
      });
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      if (this.activeMode === 'device') {
        this.formData = {
          deviceId: params.deviceId,
          deviceName: params.deviceName,
          outcome: params.outcome,
          errorCodes: params.errorCodes,
        };
      } else {
        this.formData = {
          startTime: params.startTime,
          endTime: params.endTime,
          outcome: params.outcome,
          deviceIds: params.deviceIds,
          causeErrors: params.causeErrors,
        };
      }
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
      this.formData = params;
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities;
        // this.totalCount = data.total
      });
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.totalCount = data;
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceInfoId },
      });
      window.open(routeData.href, '_blank');
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    // 导出
    onExport: function () {
      if (this.activeMode === 'device') {
        // 设备模式
        this.$refs.exportModule.init(this.$route.query.batchId);
      } else if (this.activeMode === 'image') {
        // 图片模式
        this.MixinGetSecondExport();
      }
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
        iconStaticsImgList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        cardList: this.cardList,
        tabList: this.tabList,
        needResetCopySearch: this.activeMode, // 设备、图片模式切换时，重新 拷贝搜索条件
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('@/views/governanceevaluation/evaluationoResult/ui-pages/particular.vue').default,
    CheckPicture: require('@/views/governanceevaluation/evaluationoResult/common-pages/check-picture/index.vue')
      .default,
    ArtificialReview: require('@/views/governanceevaluation/evaluationoResult/common-pages/artificial-review.vue')
      .default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped></style>
