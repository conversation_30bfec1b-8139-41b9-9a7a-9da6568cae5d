<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card">
    <div style="width: 100%">
      <div class="img">
        <div @click="viewBigPic(list.imageUrl)">
          <ui-image :src="list.imageUrl" class="ui-image-card" :key="list.imageUrl" />
        </div>
        <!-- <ui-image :src="list.imageUrl" class="ui-image-card" />
        <p class="shadow-box" style="z-index:11" title="查看大图">
          <i
            class="icon-font icon-yichang search-icon mr-xs base-text-color"
            @click="viewBigPic(list.imageUrl)"
          ></i>
        </p> -->
        <p v-if="!!list.objectSnapshotId" class="shadow-copy" style="z-index: 11" :title="list.objectSnapshotId">
          <i class="base-text-color f-14">ID：</i>
          <span class="base-text-color id-num ellipsis f-14">{{ list.objectSnapshotId }}</span>
          <span class="copy-text f-14" v-clipboard="list.objectSnapshotId" v-clipboard:callback="copy">复制</span>
        </p>
        <p
          class="shadow-artificial font-blue"
          style="z-index: 11"
          title="人工复核"
          @click="artificialReview(list)"
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
        >
          <i class="font-blue icon-font icon-xiajikaohedefen vt-middle f-14 mr-xs"></i>
          <span class="artificial-text f-14 font-blue">人工复核</span>
        </p>
      </div>

      <div class="ui-gather-card-image-item" v-for="(item, index) in cardInfo" :key="index">
        <p v-if="!item.type" style="display: flex; flex-wrap: nowrap">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-value"
            :style="{
              color: list[item.outcomeType] ? (list[item.outcomeType] === '1' ? '#ffffff' : '#C43D2C') : '#ffffff',
            }"
            >{{ list[item.value] || '缺失' }}</span
          >
        </p>
        <p v-else-if="item.type === 'time'" style="display: flex; flex-wrap: nowrap">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-value"
            :style="{
              color: list[item.outcomeType] ? (list[item.outcomeType] === '1' ? '#ffffff' : '#C43D2C') : '#ffffff',
            }"
            >{{ list[item.value] ? `${list[item.value]} 毫秒` : '缺失' }}</span
          >
        </p>
        <p v-else-if="item.type === 'reason'" style="display: flex; flex-wrap: nowrap">
          <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-reason"
            :class="list.outcome === '1' ? 'font-green' : 'font-red'"
            >{{ list[item.value] ? `${list[item.value]}` : '缺失' }}</span
          >
        </p>
        <p v-else style="display: flex; flex-wrap: nowrap">
          <span
            :title="list[item.value] || '缺失'"
            class="ui-gather-card-right-item-type"
            :style="{
              color: list[item.outcomeType] ? (list[item.outcomeType] === '1' ? '#ffffff' : '#C43D2C') : '#ffffff',
            }"
            >{{ list[item.value] || '缺失' }}</span
          >
        </p>
      </div>
    </div>
    <!-- 人工复核 -->
    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <div class="artificial-data">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <Radio label="1">图片可用 </Radio>
            <Radio label="2" class="ml-lg">图片不可用 </Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block check-list" label="" :width="0" v-if="artificialData.qualified === '2'">
          <CheckboxGroup v-model="artificialData.errorCode">
            <Checkbox v-for="(item, index) in checkList" :key="index" :label="item.key">
              <span class="check-text">{{ item.value }}</span>
            </Checkbox>
          </CheckboxGroup>
        </ui-label>
        <ui-label class="block mt-sm" label="" :width="0">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>

      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
    checkData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      imgList: [],
      bigPictureShow: false,
      artificialStyles: {
        width: '3rem',
      },
      checkList: [],
      artificialData: { qualified: '1', reason: '', errorCode: [] },
      artificialVisible: false,
      artificialRow: {},
      paramsList: {},
    };
  },
  computed: {},
  created() {},
  methods: {
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialData.errorCode = [];
      this.artificialRow = row;
      this.artificialVisible = true;
    },

    async artificial() {
      let data = {};
      if (this.artificialData.qualified == '1') {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            deviceDetailId: this.artificialRow.evaluationDeviceDetailId,
          },
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
        };
      } else {
        data = {
          data: {
            id: this.artificialRow.id,
            qualified: this.artificialData.qualified,
            reason: this.artificialData.reason,
            type: 'detail',
            errorCode: this.artificialData.errorCode.toString(),
            deviceDetailId: this.artificialRow.evaluationDeviceDetailId,
          },
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
        };
        if (this.artificialData.errorCode == '') {
          this.$Message.error('请选择异常原因');
          return;
        }
      }
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.artificialVisible = false;
        this.$emit('recount');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
    copy() {
      this.$Message.success('复制成功');
    },
  },
  watch: {
    checkData: {
      handler(val) {
        if (val) {
          this.checkList = val;
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        this.paramsList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: var(--bg-info-card);
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    &-item {
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: var(--color-info-card-label);
      }
      &-value {
        color: var(--color-info-card-content);
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: var(--color-success);
    color: #ffffff;
    border-radius: 4px;
  }
}
.ui-gather-card-image-item {
  font-size: 14px;
  margin: 4px 0 4px 0;
}
.ui-image-card {
  height: 190px;
  cursor: pointer;
  .tileImage {
    height: 190px;
  }
}
// .ui-image-card {
//   height: 190px;
//   // width: 190px;
//   cursor: pointer;
// }
// @{_deep}.ui-image {
//   z-index: initial;
//   .ui-image-div {
//     .tileImage {
//       height: 190px !important;
//       width: 190px !important;
//     }
//   }
// }
.ui-gather-card-right-item-type {
  white-space: nowrap;
  width: 240px;
  overflow: hidden;
  display: inline-block;
  text-overflow: ellipsis;
}
.shadow-copy {
  height: 28px;
  line-height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 60px;
  display: none;
  padding-left: 10px;
  .id-num {
    width: 150px;
    vertical-align: top;
    display: inline-block;
  }

  i {
    vertical-align: top;
  }
  .copy-text {
    color: var(--color-primary);
    cursor: pointer;
    vertical-align: sub;
  }
  @media screen and (max-width: 1366px) {
    .id-num {
      width: 100px; /*no*/
    }
    .copy-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
.check-list {
  // width: 460px;
  margin-left: 80px;
  margin-top: 10px;
}
.check-text {
  display: inline-block;
}
.desc {
  margin-left: 80px;
  width: 80%;
}
.artificial-data {
  padding: 0 50px;
}
.shadow-artificial {
  height: 28px;
  line-height: 28px;
  width: 100%;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  bottom: 0px;
  display: none;
  padding-left: 10px;

  .artificial-text {
    color: var(--color-primary);
    cursor: pointer;
    vertical-align: middle;
  }
  @media screen and (max-width: 1366px) {
    .artificial-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
  }
}
.img:hover {
  position: relative;
  .shadow-copy {
    display: block;
  }
  .shadow-artificial {
    display: block;
  }
}
.ui-gather-card-right-item-label {
  white-space: nowrap;
  display: inline-block;
  color: #8797ac;
}
.ui-gather-card-right-item-value {
  display: inline-block;
  width: 62%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ui-gather-card-right-item-reason {
  display: inline-block;
  width: fit-content;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
// @{_deep} .ivu {
//   &-tag{
//     &:hover{
//       background: var(--color-primary) !important;
//     }
//   }
// }
</style>
