<template>
  <transfer-table
    class="transfer-table"
    :left-table-columns="columns1"
    :right-table-columns="columns2"
    :left-table-data="newPropertyList"
    :right-table-data="targetList"
    :leftLoading="leftLoading"
    :rightLoading="rightLoading"
    @onLeftToRight="selectionChange"
  >
    <template #left-title><slot name="left-title"></slot></template>
    <template #right-title><slot name="right-title"></slot></template>
  </transfer-table>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    propertyList: {
      default: () => [],
    },
    // 默认配置
    defaultParams: {},
  },
  data() {
    return {
      columns1: Object.freeze([
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ]),
      columns2: Object.freeze([
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
        {
          title: '入库策略',
          key: 'addType',
          render: (h, params) => {
            return h(
              'i-select',
              {
                style: {
                  'text-align': 'left',
                },
                props: {
                  value: params.row.addType,
                  disabled: params.row.disabled,
                  placeholder: '请选择',
                  transfer: true,
                },
                on: {
                  'on-change': (val) => {
                    let Item = this.targetList.find((item) => item.checkColumnName === params.row.checkColumnName);
                    this.$set(Item, 'addType', val);
                    this.handleStrageParams(this.targetList);
                  },
                },
              },
              this.aysc_storage_type.map((row) => {
                return h('i-option', {
                  props: {
                    value: row.dataKey,
                    label: row.dataValue,
                  },
                  style: {
                    'background-color': '#1C325A',
                    color: '#ffffff',
                  },
                });
              }),
            );
          },
        },
        {
          title: '操作',
          key: 'action',
          width: 60,
          align: 'center',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {
                props: {
                  row: row,
                },
                class: ['font-active-color', 'pointer'],
                on: {
                  click: () => {
                    if (this.getLabel(row)[0] === '--') {
                      return;
                    }
                    // 右侧删除
                    let Index = this.targetList.findIndex((item) => {
                      return item.checkColumnName === row.checkColumnName;
                    });
                    this.targetList.splice(Index, 1);
                    // 处理左侧的选中
                    let Item = this.newPropertyList.find((item) => {
                      return item.propertyName === row.checkColumnName;
                    });
                    this.$set(Item, '_checked', false);
                    this.handleStrageParams(this.targetList);
                  },
                },
              },
              this.getLabel(row),
            );
          },
        },
      ]),
      targetList: [],
      leftLoading: false,
      rightLoading: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    getLabel(row) {
      const nameArr = ['deviceId', 'sourceId'];
      let columnContent = '';
      columnContent = nameArr.includes(row.checkColumnName) ? '--' : '移除';
      return [columnContent];
    },
    handleIsDefault() {
      // if (['deviceId','sourceId'].includes(item.propertyName)) {
      //       this.$set(item, '_checked', true)
      //       this.$set(item, '_disabled', true)
      //     }
      // let storageList = JSON.parse(this.defaultParams)
      let storageList = this.defaultParams ? JSON.parse(this.defaultParams) : [];
      let storageListObject = {};
      storageList.map((item) => {
        storageListObject[item.fieldName] = item;
      });
      if (!('deviceId' in storageListObject)) {
        storageList.unshift({
          addType: '1',
          fieldName: 'deviceId',
          fieldRemark: '设备编码',
        });
      }
      if (!('sourceId' in storageListObject)) {
        storageList.unshift({
          addType: '2',
          fieldName: 'sourceId',
          fieldRemark: '数据来源',
        });
      }
      let targetListObject = {};
      // 处理右侧数据
      this.targetList = storageList.map((item) => {
        targetListObject[item.fieldName] = item;
        let one = {};
        one.checkColumnName = item.fieldName;
        one.checkColumnValue = item.fieldRemark;
        // 改字典后，现场已经有数据了，只能前端做一些奇怪的兼容
        if (['1', '2', '3'].includes(item.addType)) {
          one.addType = item.addType;
        } else {
          one.addType = '1';
        }
        this.$set(one, 'disabled', false);
        if (one.checkColumnName === 'deviceId') {
          this.$set(one, 'disabled', true);
        }
        return one;
      });
      // 处理左侧的选中
      this.newPropertyList.forEach((item) => {
        if (item.propertyName in targetListObject) {
          this.$set(item, '_checked', true);
        } else {
          this.$set(item, '_checked', false);
        }
        if (['deviceId', 'sourceId'].includes(item.propertyName)) {
          this.$set(item, '_checked', true);
          this.$set(item, '_disabled', true);
        }
      });
    },
    selectionChange(selection) {
      const targetListObject = {};
      this.targetList.forEach((item) => {
        targetListObject[item.checkColumnName] = item;
      });
      selection.forEach((item) => {
        item.checkColumnName = item.propertyName;
        item.checkColumnValue = item.propertyColumn;
        item.checkColumnName in targetListObject
          ? this.$set(item, 'addType', targetListObject[item.checkColumnName].addType)
          : this.$set(item, 'addType', '1');
        // 默认值是覆盖
      });
      this.targetList = selection;
      this.handleStrageParams(selection);
      // 刷新左侧选中数据
      const selectionObject = {};
      selection.forEach((item) => {
        selectionObject[item.checkColumnName] = item;
      });
      this.newPropertyList.forEach((item) => {
        item.propertyName in selectionObject ? this.$set(item, '_checked', true) : this.$set(item, '_checked', false);
      });
    },
    handleStrageParams(selection) {
      let storageParam = selection.map((item) => {
        let obj = {};
        obj.fieldName = item.checkColumnName;
        obj.fieldRemark = item.checkColumnValue;
        obj.addType = item.addType;
        return obj;
      });
      this.$emit('selectionChange', storageParam);
    },
  },
  computed: {
    ...mapGetters({
      aysc_storage_type: 'assets/aysc_storage_type',
    }),
  },
  watch: {
    propertyList: {
      handler(val) {
        this.newPropertyList = this.$util.common.deepCopy(val);
        val.length ? this.handleIsDefault() : null;
      },
      immediate: true,
    },
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.transfer-table {
  height: 500px;
  @{_deep} .left-table {
    height: calc(100% - 40px) !important;
  }
  @{_deep} .right-table {
    height: calc(100% - 40px) !important;
  }
  .ivu-select {
    background: #02162b;
    border: 1px solid #10457e;
    border-radius: 5px;
  }
}
</style>
