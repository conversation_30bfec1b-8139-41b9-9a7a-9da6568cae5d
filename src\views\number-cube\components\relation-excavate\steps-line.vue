<template>
  <div class="step-box">
    <div class="step-item" v-for="(item, index) in stepList" :key="index">
      <div
        class="step-item-num"
        :class="index <= current ? 'step-num-active' : ''"
      >
        {{ index + 1 }}
      </div>
      <div
        class="step-item-label"
        :class="index <= current ? 'step-label-active' : ''"
      >
        {{ item }}
      </div>
      <div class="step-line" v-if="index < 3"></div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    current: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      stepList: [
        "目标实体类型选择",
        "基础关系选择",
        "挖掘规则设置",
        "关系挖掘执行",
      ],
    };
  },
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.step-box {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 80px;
  .step-item {
    width: 25%;
    color: rgba(0, 0, 0, 0.35);
    position: relative;
    .step-item-num {
      text-align: center;
      width: 30px;
      height: 30px;
      font-size: 18px;
      margin: 0 auto;
      border-radius: 30px;
      border: 1px solid #d3d7de;
    }
    .step-item-label {
      text-align: center;
      font-size: 14px;
      margin-top: 10px;
    }
    .step-line {
      width: calc(~"100% - 60px");
      height: 0;
      border: 1px solid #d3d7de;
      position: absolute;
      top: 13px;
      left: 160px;
    }
  }
  .step-num-active {
    color: #fff;
    background: #2c86f8;
    position: relative;
  }
  .step-num-active::before {
    content: "";
    width: 44px;
    height: 44px;
    background: #2c86f8;
    opacity: 0.3;
    border-radius: 22px;
    display: block;
    position: absolute;
    left: -8px;
    top: -7px;
  }
  .step-label-active {
    color: #2c86f8;
  }
}
</style>
