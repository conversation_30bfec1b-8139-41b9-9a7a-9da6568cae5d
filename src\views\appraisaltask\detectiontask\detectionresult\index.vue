<template>
  <div class="page-evaluationmanagement auto-fill height-full">
    <div class="content auto-fill">
      <div class="container" v-show="!componentName">
        <div class="left-content auto-fill">
          <div class="record-title font-active-color">
            <span class="name base-text-color f-14">检测任务</span>
          </div>
          <div class="record-search">
            <Input
              v-model="schemeData.taskName"
              :clearable="true"
              @on-clear="infoSearch"
              @on-enter="infoSearch"
              placeholder="请输入任务名称"
            >
              <i class="icon-font icon-sousuo" @on-click="infoSearch" slot="suffix"></i>
            </Input>
          </div>
          <div
            class="assessment-list"
            v-infinite-scroll="handleReachBottom"
            :infinite-scroll-immediate="false"
            infinite-scroll-distance="10"
          >
            <Card
              v-for="(item, index) in moduleList"
              :key="index"
              :class="{
                active: item.taskSchemeId === activeModuleMessageId,
              }"
              class="card-box"
            >
              <div class="collapse-content-p pointer" @click="selectLeftModule(item, index)">
                <div class="card-desc">
                  <span :class="['icon-font', 'f-14', 'vt-middle', getIconByType(item.schemeType)]"></span>
                  <span class="ml-sm vt-middle" :title="item.taskName">{{ item.taskName }}</span>
                </div>
                <div class="operationBox">
                  <tag :type="item.schemeType" class="mr-sm">{{ item.schemeTypeText }}</tag>
                  <span class="kpi-text">共{{ item.totalCount }}个考核指标</span>
                </div>
              </div>
            </Card>
            <div v-show="loading">
              <img src="@/assets/img/common/loading.gif" alt="" />
            </div>
          </div>
        </div>
        <div class="right-content auto-fill">
          <srogramme-table
            ref="childTable"
            :active-module-message-id="activeModuleMessageId"
            :version-item="versionItem"
            :task-scheme-id="taskSchemeId"
            @selectModule="selectModule"
          ></srogramme-table>
        </div>
      </div>
      <keep-alive>
        <component :is="componentName"></component>
      </keep-alive>
    </div>
  </div>
</template>

<style lang="less" scoped>
.page-evaluationmanagement {
  overflow: hidden;
  height: 100%;
  .content {
    overflow: hidden;
    background: var(--bg-content);
    background-size: 100% 100%;
    height: 100%;
    position: relative;
    padding-left: 10px;
    .container {
      height: 100%;
    }
  }
  .left-content {
    border-right: 1px solid var(--border-color);
    float: left;
    width: 300px;
    background: var(--bg-content);
    height: 100%;
    padding: 10px;

    .record-title {
      height: 34px;
      line-height: 34px;
      margin: 0 10px 10px;

      .name {
        position: relative;
        font-weight: bold;
      }

      .btn_add {
        .icon-dianjishangchuantupian-line {
          float: left;
        }

        span {
          float: right;
        }
      }
    }
    .record-search {
      padding-left: 10px;
      padding-right: 10px;
      margin-bottom: 10px;
      @{_deep} .ivu-input-suffix .icon-font {
        color: var(--color-primary);
      }
    }
    .assessment-list {
      position: relative;
      overflow-y: auto;
      padding-left: 10px;
      padding-right: 10px;
      @{_deep} .ivu-card {
        margin-bottom: 10px;
        border: 1px solid var(--border-vertical-tab);
        border-radius: 4px;
        background: var(--bg-vertical-tab);
        &:hover {
          background-color: var(--bg-vertical-tab-hover);
          .collapse-content-p {
            color: var(--color-vertical-tab-hover);
            .operationBox {
              border-top: 1px dashed var(--devider-vertical-tab-hover);
            }
          }
        }
        .ivu-card-body {
          padding: 0px !important;
        }

        &.active {
          background: var(--bg-vertical-tab-active);
          .collapse-content-p {
            color: var(--color-vertical-tab-active);
            .card-desc {
              .icon-font {
                color: var(--color-vertical-tab-active);
              }
            }
            .operationBox {
              border-top: 1px dashed var(--devider-vertical-tab-active);
            }
          }
        }

        .collapse-content-p {
          color: var(--color-vertical-tab);
          .card-desc {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 10px;
            .icon-font {
              color: var(--color-vertical-tab-icon);
            }
          }
          .operationBox {
            display: flex;
            padding: 10px;
            border-top: 1px dashed var(--devider-vertical-tab);
          }

          .btn-text {
            height: 22px !important;
            line-height: 22px !important;
          }
          .kpi-text {
            line-height: 22px;
            font-size: 12px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .right-content {
    float: right;
    width: calc(~'100% - 300px');
    height: 100%;
    padding: 20px 20px 0 20px;
    background: var(--bg-content);

    .tab {
      padding: 10px;
    }
  }
  .assessment-list {
    .icon-sousuo {
      color: var(--color-btn-text);
    }
    .ivu-input-wrapper {
      margin-bottom: 20px;
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'detectionresult',
  mixins: [dealWatch],
  data() {
    return {
      componentName: null,
      componentLevel: 0,
      moduleList: [],
      loading: false,
      activeModuleMessageId: '0',
      taskSchemeId: '0',
      activeVersion: 0,
      schemeValue: 0,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '指标名称', key: 'indexName' },
        { title: '设备合格率', key: 'passrate' },
        { title: '计算方法', key: 'compmethod' },
        { title: '评价标准', key: 'pingjia' },
        { title: '操作', slot: 'option' },
      ],
      tableData: [],
      minusTable: 260,
      schemeData: { taskName: '', pageNumber: 1, pageSize: 20 },
      visible: false,
      formData: {},
      modalAction: {
        title: '编辑信息',
      },
      idx: '',
      moduleShow: false, //控制弹窗出现变量
      moduleAction: {},
      moduleId: {},
      edit_item: {}, //点击编辑存储数据
      versionItem: 0,
      total: 0,
      done: false,
      currentScheme: {},
    };
  },
  created() {
    this.getParams();
    this.get_left();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },

  provide() {
    return {
      programThis: this,
    };
  },
  components: {
    SrogrammeTable: require('./srogramme-table.vue').default,
    detectionToOverview: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    // AddCheckModule: require('./add-check-module.vue').default,
    tag: require('./tag').default,
    FaceCarOnlineNum: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-online-num.vue')
      .default,
    FaceCarTopOnline: require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-top-online.vue')
      .default,
    FaceCarReportRate:
      require('@/views/governanceevaluation/evaluationoverview/components/face/face-car-report-rate.vue').default,
    VideoHistoryOnline:
      require('@/views/governanceevaluation/evaluationoverview/components/video/video-history-online.vue').default,
  },
  methods: {
    infoSearch() {
      this.get_left();
    },
    getIconByType(type) {
      switch (type) {
        case '1':
          return 'icon-quanliangshujujiance';
        case '2':
          return 'icon-shangbaokaohejiance';
        case '3':
          return 'icon-linshijiance';
        default:
          'icon-zidingyizhilifangan';
      }
    },
    resetSearchData() {
      this.schemeData.pageSize = 20;
      this.schemeData.pageNumber = 1;
    },
    async handleReachBottom() {
      if (!(this.schemeData.pageNumber * this.schemeData.pageSize >= this.total)) {
        this.schemeData.pageNumber++;
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, this.schemeData);
        let scheme = this.setSchemeListPageInfo(data.entities);
        this.moduleList.push(...scheme);
        this.loading = false;
      }
    },
    async get_left() {
      this.resetSearchData();
      //左侧列表的接口
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, this.schemeData);
        let scheme = this.setSchemeListPageInfo(data.entities);
        this.moduleList = scheme;
        this.total = data.total;
        this.setSelectScheme(this.moduleList);
      } catch (err) {
        console.log(err);
      }
    },
    setSchemeListPageInfo(data) {
      return data.map((item) => {
        item.pageNumber = data.pageNumber;
        item.pageSize = data.pageSize;
        return item;
      });
    },
    async refreshSchemeCount() {
      let { pageNumber, pageSize } = this.currentScheme;
      let {
        data: { data },
      } = await this.$http.post(governanceevaluation.evaluationPageList, {
        ...this.schemeData,
        pageNumber,
        pageSize,
      });
      this.setSchemeListCount(data.entities);
    },
    setSchemeListCount(scheme) {
      scheme.map((item) => {
        this.moduleList.map((ele) => {
          if (item.id === ele.id) {
            ele.indexCount = item.indexCount;
          }
        });
      });
    },
    setSelectScheme(data = []) {
      if (data.length > 0) {
        this.selectLeftModule(data[0]);
      }
    },
    // 切换左侧导航菜单
    selectLeftModule(item) {
      this.currentScheme = item;
      this.activeModuleMessageId = item.taskSchemeId;
      this.taskSchemeId = item.taskSchemeId;
    },
    // 左侧导航编辑
    showAddModule(action, item) {
      switch (action) {
        case 'add':
          this.moduleAction = {
            title: '新建方案',
            action: 'add',
          };
          break;
        case 'edit':
          this.moduleAction = {
            title: '编辑方案',
            action: 'edit',
          };
          this.edit_item = item;
          // this.moduleId = {
          //   examineModelId : item.examineModelId
          // };
          break;
      }
      this.moduleShow = true;
    },

    // 左侧导航删除

    update() {
      this.resetSearchData();
      this.get_left();
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'detectionToOverview' ? 0 : 1;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
  },
  watch: {},
};
</script>
