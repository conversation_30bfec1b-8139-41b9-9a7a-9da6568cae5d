import MapDom from '../base-components/map-dom.vue';
import MapDom2 from '../components/map-dom.vue';
import magBg from '@/assets/img/map-bg.png';
import Vue from 'vue';
import { fontSize } from '@/util/module/common.js';

export const options = (mapData, homePageConfig, callback) => {
  let lineData = lineDataFunc(mapData, homePageConfig);
  return new Promise((resolve, reject) => {
    try {
      resolve({
        tooltip: {
          trigger: 'item',
          //alwaysShowContent: true,
          show: false,
          enterable: true,
        },
        // 最底层 - 黑色阴影地图
        geo: [
          {
            map: 'map',
            aspectScale: 1.2,
            top: fontSize(40),
            bottom: fontSize(20),
            silent: true,
            roam: false,
            itemStyle: {
              areaColor: '#0E2F6B',
              shadowColor: 'rgba(0, 0, 0, 1)',
              shadowOffsetX: 0,
              shadowOffsetY: 15,
              shadowBlur: 15,
            },
          },
          {
            map: 'map',
            aspectScale: 1.2,
            top: fontSize(20),
            bottom: fontSize(40),
            silent: true,
            roam: false,
            itemStyle: {
              areaColor: '#08E0FF',
              shadowColor: '#08E0FF',
              shadowBlur: 5,
              borderColor: '#08E0FF',
              borderWidth: 4,
            },
          },
        ],
        series: [
          {
            top: fontSize(20),
            bottom: fontSize(40),
            type: 'map',
            zoom: 0,
            roam: false,
            map: 'map',
            aspectScale: 1.2,
            tooltip: {
              show: true,
              trigger: 'item', // 触发类型
              padding: 0, //提示框浮层内边距
              backgroundColor: 'rgba(50, 50, 50, 0.7)',
              borderColor: 'transparent',
              // position: 'right',
              position: (point) => {
                return [point[0] + 8, point[1] + 8];
              },
              enterable: true,
              formatter: function (params) {
                // ToolTipCard为vue组件，直接import , 第二个参数为props
                let GovernTendencyTooltip1 = Vue.extend(MapDom);
                let _this = new GovernTendencyTooltip1({
                  el: document.createElement('div'),
                  data() {
                    return {
                      toolTipData: params.data.data ?? { regionName: params.data.name },
                      homePageConfig: homePageConfig,
                    };
                  },
                  mounted() {
                    window.jump = () => {
                      callback(this.toolTipData, params.data);
                    };
                  },
                });
                return _this.$el.outerHTML;
              },
            },
            label: {
              show: false,
              color: '#fff',
            },
            select: {
              disabled: true,
            },
            emphasis: {
              label: {
                show: false,
              },
              itemStyle: {
                areaColor: {
                  // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
                  type: 'linear',
                  x: 0.5,
                  y: 0.5,
                  r: 0.5,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#01C6E3', // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#00E6DE', // 100% 处的颜色
                    },
                  ],
                  global: false, // 缺省为 false
                },
                borderColor: '#fff',
                borderWidth: 2,
                shadowColor: 'rgba(11, 39, 100, 1)',
                shadowOffsetX: 0,
                shadowOffsetY: 15,
                shadowBlur: 15,
              },
            },
            itemStyle: {
              borderColor: '#08D7F8',
              borderWidth: 1,
              color: '#D2E3FF',
              areaColor: {
                // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
                type: 'linear',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                  {
                    offset: 0,
                    color: '#11218B', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#004EC2', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
            data: mapData,
          },
          {
            // 波纹
            tooltip: {
              show: false,
            },
            zlevel: 999,
            type: 'effectScatter',
            coordinateSystem: 'geo',
            rippleEffect: {
              scale: 10,
              brushType: 'stroke',
            },
            showEffectOn: 'render',
            itemStyle: {
              shadowColor: '#1AFBD9',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              color: function (params) {
                return params.data.color;
              },
            },
            symbol: 'circle',
            symbolSize: [10, 5],
            symbolOffset: [0, -20],
            data: mapData,
          },
          // 柱状体的主干
          {
            type: 'lines',
            geoIndex: 1,
            zlevel: 999,
            coordinateSystem: 'geo',
            effect: {
              show: false,
              // period: 4, //箭头指向速度，值越小速度越快
              // trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
              // symbol: 'arrow', //箭头图标
              // symbol: imgDatUrl,
            },
            lineStyle: {
              width: fontSize(10), // 尾迹线条宽度
              color: (params) => {
                return {
                  // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0.1,
                      color: params.data.color[0],
                    },
                    {
                      offset: 0.6,
                      color: params.data.color[1],
                    },
                    {
                      offset: 1,
                      color: params.data.color[2],
                    },
                  ],
                  global: false, // 缺省为 false
                };
              },
              opacity: 1, // 尾迹线条透明度
              curveness: 0, // 尾迹线条曲直度
              animation: true,
            },
            label: {
              show: 0,
              position: 'start',
              formatter: '245',
            },
            silent: true,
            data: lineData,
          },
          // 柱状体的顶部
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 1,
            zlevel: 1000,
            label: {
              show: true,
              formatter: function (params) {
                return params.data.num;
              },
              fontSize: fontSize(14),
              distance: fontSize(-20),
              fontFamily: 'YouSheBiaoTiHei',
              position: 'top',
              color: '#fff',
              opacity: 1,
            },
            symbol: 'circle',
            symbolSize: [0.1, 0.1],
            silent: true,
            data: scatterTopData(mapData, homePageConfig.regionCode),
          },
          // 柱状体的底部
          {
            type: 'scatter',
            coordinateSystem: 'geo',
            geoIndex: 1,
            label: {
              show: true,
              // 这儿是处理的
              formatter: '{b}',
              position: 'bottom',
              distance: fontSize(10),
              fontSize: fontSize(12),
              color: '#D2E3FF',
              fontFamily: 'YouSheBiaoTiHei',
              fontWeight: 'normal',
            },
            symbol: 'circle',
            symbolSize: [0.1, 0.1],
            itemStyle: {
              color: 'rgb(22,255,255, 1)',
              opacity: 1,
            },
            silent: true,
            data: scatterBottomData(mapData),
          },
        ],
      });
    } catch (err) {
      reject(err);
    }
  });
};

export const options2 = (mapData, homePageConfig, callback) => {
  let lineData = lineDataFunc(mapData, homePageConfig);
  return {
    tooltip: {
      trigger: 'item',
      //alwaysShowContent: true,
      show: false,
      enterable: true,
    },
    // 最底层 - 黑色阴影地图
    geo: [
      {
        map: 'map',
        aspectScale: 1.2,
        top: fontSize(40),
        bottom: fontSize(20),
        silent: true,
        roam: false,
        itemStyle: {
          areaColor: '#0E2F6B',
          shadowColor: 'rgba(0, 0, 0, 1)',
          shadowOffsetX: 0,
          shadowOffsetY: 15,
          shadowBlur: 15,
        },
      },
      {
        map: 'map',
        aspectScale: 1.2,
        top: fontSize(20),
        bottom: fontSize(40),
        silent: true,
        roam: false,
        itemStyle: {
          areaColor: '#08E0FF',
          shadowColor: '#08E0FF',
          shadowBlur: 5,
          borderColor: '#08E0FF',
          borderWidth: 4,
        },
      },
    ],
    series: [
      {
        top: fontSize(20),
        bottom: fontSize(40),
        type: 'map',
        zoom: 0,
        roam: false,
        map: 'map',
        aspectScale: 1.2,
        tooltip: {
          show: true,
          trigger: 'item', // 触发类型
          padding: 0, //提示框浮层内边距
          backgroundColor: 'transparent',
          borderColor: 'transparent',
          // position: 'right',
          position: (point) => {
            return [point[0] + 8, point[1] + 8];
          },
          enterable: true,
          formatter: function (params) {
            // ToolTipCard为vue组件，直接import , 第二个参数为props
            let GovernTendencyTooltip1 = Vue.extend(MapDom2);
            let _this = new GovernTendencyTooltip1({
              el: document.createElement('div'),
              data() {
                return {
                  toolTipData: params.data.data ?? { regionName: params.data.name },
                  homePageConfig: homePageConfig,
                };
              },
              mounted() {
                window.jump = () => {
                  callback(this.toolTipData, params.data);
                };
              },
            });
            return _this.$el.outerHTML;
          },
        },
        label: {
          show: false,
          color: '#fff',
        },
        select: {
          disabled: true,
        },
        emphasis: {
          label: {
            show: false,
          },
          itemStyle: {
            areaColor: {
              // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
              type: 'linear',
              x: 0.5,
              y: 0.5,
              r: 0.5,
              colorStops: [
                {
                  offset: 0,
                  color: '#01C6E3', // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#00E6DE', // 100% 处的颜色
                },
              ],
              global: false, // 缺省为 false
            },
            borderColor: '#fff',
            borderWidth: 2,
            shadowColor: 'rgba(11, 39, 100, 1)',
            shadowOffsetX: 0,
            shadowOffsetY: 15,
            shadowBlur: 15,
          },
        },
        itemStyle: {
          borderColor: '#B8FFFF',
          borderWidth: 1,
          areaColor: {
            image: magBg,
            repeat: 'repeat',
          },
          shadowColor: '#B8FFFF',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 1,
        },
        data: mapData,
      },
      {
        // 波纹
        tooltip: {
          show: false,
        },
        zlevel: 999,
        type: 'effectScatter',
        coordinateSystem: 'geo',
        rippleEffect: {
          scale: 10,
          brushType: 'stroke',
        },
        showEffectOn: 'render',
        itemStyle: {
          shadowColor: '#1AFBD9',
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowOffsetY: 0,
          color: function (params) {
            return params.data.color;
          },
        },
        symbol: 'circle',
        symbolSize: [10, 5],
        symbolOffset: [0, -20],
        data: mapData,
      },
      // 柱状体的主干
      {
        type: 'lines',
        geoIndex: 1,
        zlevel: 999,
        coordinateSystem: 'geo',
        effect: {
          show: false,
          // period: 4, //箭头指向速度，值越小速度越快
          // trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
          // symbol: 'arrow', //箭头图标
          // symbol: imgDatUrl,
        },
        lineStyle: {
          width: fontSize(10), // 尾迹线条宽度
          color: (params) => {
            return {
              // 径向渐变，前三个参数分别是圆心 x, y 和半径，取值同线性渐变
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0.1,
                  color: params.data.color[0],
                },
                {
                  offset: 0.6,
                  color: params.data.color[1],
                },
                {
                  offset: 1,
                  color: params.data.color[2],
                },
              ],
              global: false, // 缺省为 false
            };
          },
          opacity: 1, // 尾迹线条透明度
          curveness: 0, // 尾迹线条曲直度
          animation: true,
        },
        label: {
          show: 0,
          position: 'start',
          formatter: '245',
        },
        silent: true,
        data: lineData,
      },
      // 柱状体的顶部
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        geoIndex: 1,
        zlevel: 1000,
        label: {
          show: true,
          formatter: function (params) {
            return params.data.num;
          },
          fontSize: fontSize(14),
          distance: fontSize(-20),
          fontFamily: 'YouSheBiaoTiHei',
          position: 'top',
          color: '#fff',
          opacity: 1,
        },
        symbol: 'circle',
        symbolSize: [0.1, 0.1],
        silent: true,
        data: scatterTopData(mapData, homePageConfig.regionCode),
      },
      // 柱状体的底部
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        geoIndex: 1,
        label: {
          show: true,
          // 这儿是处理的
          formatter: '{b}',
          position: 'bottom',
          distance: fontSize(10),
          fontSize: fontSize(12),
          color: '#D2E3FF',
          fontFamily: 'YouSheBiaoTiHei',
          fontWeight: 'normal',
        },
        symbol: 'circle',
        symbolSize: [0.1, 0.1],
        itemStyle: {
          color: 'rgb(22,255,255, 1)',
          opacity: 1,
        },
        silent: true,
        data: scatterBottomData(mapData),
      },
    ],
  };
};
// 动态计算柱形图的高度（定一个max）
let percentMax = 0;
// let colorTypeObject = {
//   green: ['rgba(1, 239, 119, 0.1)', 'rgba(1, 239, 119, 0.6)', 'rgba(1, 239, 119, 1)'],
//   yellow: ['rgba(255, 167, 0, 0.1)', 'rgba(255, 167, 0, 0.6)', 'rgba(255, 167, 0, 1)'],
//   red: ['rgba(198, 2, 53, 0.1)', 'rgba(198, 2, 53,0.6)', 'rgba(198, 2, 53, 1)'],
// };
// 柱状体的主干
function lineDataFunc(mapData, homePageConfig) {
  let data = [];
  let latiAll = [];
  mapData.map((item) => {
    let params = item?.data ?? [];
    // 不合格指标的占比
    // let unqualifiedPercent = Math.floor(
    //   !!params ? (params.unqualifiedByExamineIndex * 100) / params.totalByExamineIndex : 0,
    // );
    latiAll.push(item.value[1]);
    // 占比不同，颜色不同
    // let colorType = 'green';
    // if (unqualifiedPercent <= 20) {
    //   colorType = 'green';
    // } else if (50 < unqualifiedPercent < 20) {
    //   colorType = 'yellow';
    // } else {
    //   colorType = 'red';
    // }
    // mapData也添加颜色 - 波纹来获取颜色
    let colorConfig = handleColorType(params);
    item.color = colorConfig[2];
    let reallyValue = handleSpecialPosition(homePageConfig.regionCode, item.value);
    let one = {
      coords: [reallyValue, [reallyValue[0], reallyValue[1] + percentMax]],
    };
    one.color = colorConfig;
    data.push(one);
  });
  let laMin = latiAll.sort()[0];
  let laMax = latiAll.sort()[latiAll.length - 1];
  percentMax = (laMax - laMin) * 0.1;
  // 只有一个区，需要自己写死一个小的值
  if (latiAll.length < 2) {
    percentMax = 0.01;
  }
  percentMax = handleSpecialMax(homePageConfig.regionCode, percentMax);
  return data;
}
// 柱状体的顶部
function scatterTopData(mapData, regionCode) {
  return mapData.map((item) => {
    let reallyValue = handleSpecialPosition(regionCode, item.value);
    return {
      name: item.name,
      num: item.data?.unqualifiedByExamineIndex ?? 0,
      value: [reallyValue[0], reallyValue[1] + percentMax],
    };
  });
}
// 柱状体的底部
function scatterBottomData(mapData) {
  return mapData.map((item) => {
    return {
      name: item.name,
      value: item.value,
    };
  });
}
/**
 * 一些地图需要单独处理经纬度
 * @param {*} regionCode
 * @param {*} value
 * @returns
 */
function handleSpecialPosition(regionCode, value) {
  let reallyValue = value;
  // 海南单独处理（因为有南沙群岛）
  if (regionCode === '460000') {
    reallyValue = [value[0] + 0.04, value[1] + 0.03];
  }
  return reallyValue;
}
/**
 * 特殊处理柱子的长度
 * @param {*} regionCode
 * @param {*} percentMax
 * @returns
 */
function handleSpecialMax(regionCode, percentMax) {
  let specialPercentMax = percentMax;
  // 海南单独处理（因为有南沙群岛）
  if (regionCode === '460000') {
    specialPercentMax = 0.2;
  }
  return specialPercentMax;
}

function handleColorType(params) {
  let colorTypeObject = {
    green: ['rgba(1, 239, 119, 0.1)', 'rgba(1, 239, 119, 0.6)', 'rgba(1, 239, 119, 1)'],
    yellow: ['rgba(255, 167, 0, 0.1)', 'rgba(255, 167, 0, 0.6)', 'rgba(255, 167, 0, 1)'],
    red: ['rgba(198, 2, 53, 0.1)', 'rgba(198, 2, 53,0.6)', 'rgba(198, 2, 53, 1)'],
  };
  // 不合格指标的占比
  let unqualifiedPercent = Math.floor(
    params ? (params.unqualifiedByExamineIndex * 100) / params.totalByExamineIndex : 0,
  );
  // 占比不同，颜色不同
  let colorType = 'green';
  if (unqualifiedPercent <= 20) {
    colorType = 'green';
  } else if (20 < unqualifiedPercent && unqualifiedPercent < 50) {
    colorType = 'yellow';
  } else if (unqualifiedPercent >= 50) {
    colorType = 'red';
  }
  return colorTypeObject[colorType];
}
