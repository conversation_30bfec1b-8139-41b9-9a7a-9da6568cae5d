<template>
  <div>
    <ui-modal ref="modal" width="660" title="车辆结构化属性完整性与准确检性测优化配置">
      <p class="p" style="margin-bottom: 12px">请配置需检测完整性和准确性的结构化的数据：</p>
      <uiTag :data="configList" @close="testingClose" @add="testingAdd" />
      <div class="explain">说明：如果选择的算法有多数算法检测属性值一致，则属性正确</div>

      <div class="quality">
        车辆属性存疑的图像处理方式：
        <RadioGroup v-model="moreSelect">
          <Radio label="多算法自动选择"></Radio>
        </RadioGroup>
      </div>

      <template slot="footer">
        <Button @click="submit" type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>

    <ui-modal ref="childModal" width="660" title="配置需结构化属性">
      <p class="p" style="margin-bottom: 12px">
        配置需结构化属性 <span>(已勾选{{ selectPropertys.length }}个)</span>
      </p>

      <CheckboxGroup v-model="selectPropertys">
        <Row>
          <Col span="8" v-for="(item, index) in allPropertys" :key="index">
            <Checkbox :label="item.propertyColumn"></Checkbox>
          </Col>
        </Row>
      </CheckboxGroup>

      <template slot="footer">
        <Button @click="configSubmit" type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
import uiTag from '../components/ui-tag';
export default {
  name: 'carDialog',
  props: {},
  data() {
    return {
      configList: [],
      allPropertys: [], // 全部字段信息
      selectPropertys: [], // 已选中字段信息
      moreSelect: '多算法自动选择',
    };
  },
  async created() {},
  computed: {},
  methods: {
    showModal() {
      this.$refs.modal.modalShow = true;
      this.init();
    },

    init() {
      this.$http
        .get(api.queryCarThremSelect + '36')
        .then((res) => {
          if (res.data.code == 200) {
            this.configList = res.data.data;
            this.selectPropertys = [];
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    testingClose(item) {
      this.selectPropertys = this.selectPropertys.filter((ite) => {
        return ite != item.checkColumnValue;
      });
      this.configList = this.configList.filter((ite) => {
        return ite.id != item.id;
      });
    },

    testingAdd() {
      this.selectPropertys = [];
      this.configList.forEach((item) => {
        this.selectPropertys.push(item.checkColumnValue);
      });

      this.$refs.childModal.modalShow = true;

      var param = {
        keyWord: '',
        propertyType: '3',
      };

      this.$http
        .post(api.queryPropertySearch, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.allPropertys = res.data.data;
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    submit() {
      this.$http
        .post(api.addCarThrem, this.configList)
        .then((res) => {
          if (res.data.code == 200) {
            this.$refs.modal.modalShow = false;
            this.$Message.success('配置成功');
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    configSubmit() {
      this.configList = [];
      this.selectPropertys.forEach((item) => {
        var arr = this.allPropertys.filter((ite) => {
          return item == ite.propertyColumn;
        });
        var obj = {
          id: arr[0].id,
          topicComponentId: 36,
          checkColumnValue: arr[0].propertyColumn,
          checkColumnName: arr[0].propertyName,
        };

        this.configList.push(obj);
      });

      this.$refs.childModal.modalShow = false;
    },
  },
  watch: {},
  components: { uiTag },
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.quality {
  padding: 10px 0;
  color: #fff;
}

.explain {
  color: #c76d28;
}
.p {
  margin-top: 10px;
  color: #fff;
}
@{_deep} .ivu-modal-body {
  padding: 20px 51px 37px;
}
</style>
