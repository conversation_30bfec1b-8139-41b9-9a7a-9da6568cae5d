<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt='' /><span class="title-text">标签资产统计</span>
    </div>
    <div class="statistics-ul">
      <div v-for="(item, $index) in labelAssetList" :key="$index" class="statistics-item" @click="labelPoolHandle(item)">
        <img :src="item.img" class="statistics-item-icon" alt='' />
        <div class="statistics-item-con">
          <div class="statistics-item-name">{{ item.name }}</div>
          <count-to
            :start-val="0"
            :end-val="item.value || 0"
            :duration="3000"
            class="statistics-item-num"
          ></count-to>
        </div>
      </div>
    </div>
  </ui-card>
</template>
<script>
  import CountTo from 'vue-count-to'
  import { mapGetters } from 'vuex'
  export default {
    components: {
      CountTo
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelAssetList: [
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') },
          { name: '', value: 0, img: require('@/assets/img/home/<USER>') }
        ]
      }
    },
    computed: {
      ...mapGetters({
        getLabelPropertyList: 'dictionary/getLabelPropertyList' // 业务属性
      })
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelTypeList = this.updataLabelAsset(this.statisticsList)
        },
        immediate: true
      }
    },
    methods: {
      updataLabelAsset (list) {
        this.getLabelPropertyList.forEach((v, i) => {
          this.labelAssetList[i].name = v.dataValue
          const item = list.find(n => {
            return n.property === v.dataKey
          })
          switch (v.dataKey) {
            case '1':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            case '2':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            case '3':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            case '4':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            case '5':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            case '6':
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
            default:
              this.labelAssetList[i].img = require('@/assets/img/home/<USER>')
              break
          }
          this.labelAssetList[i].value = item ? item.count : 0
          this.labelAssetList[i].property = v.dataKey
        })
      },
      labelPoolHandle (item) {
        const query = {
          params: 'labelList',
          property: item.property
        }
        this.$router.push({
          path: '/label-management/label-pool',
          query: {
            tabActive: 'labelList',
            labelInfo: JSON.stringify(query)
          }
        })
      }
    }
  }
</script>
<style lang="less" scoped>
  .statistics-ul {
    display: flex;
    justify-content: space-between;
    align-content: space-between;
    box-sizing: border-box;
    flex-wrap: wrap;
    .statistics-item {
      // width: 120px;
      height: 45px;
      display: flex;
      align-items: center;
      cursor: pointer;
      .statistics-item-icon {
        width: 52px;
        height: 45px;
        margin-right: 6px;
      }
      .statistics-item-con {
        display: flex;
        height: 100%;
        flex-direction: column;
        justify-content: space-between;
        .statistics-item-num {
          font-family: 'MicrosoftYaHei-Bold, MicrosoftYaHei';
          font-size: 18px;
          font-weight: bold;
          line-height: 24px;
          color: #fff;
        }
        .statistics-item-name {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.6);
          line-height: 18px;
        }
      }
    }
  }
</style>