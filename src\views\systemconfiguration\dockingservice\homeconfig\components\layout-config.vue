<template>
  <div class="layout-config-container">
    <div class="left-box" :class="{ 'left-box-edit': isEdit }">
      <div class="bg">
        <tag-view
          class="tag-view"
          :list="tagList"
          :default-active="defaultActive"
          @tagChange="changeStatus"
          ref="tagView"
        >
          <template #default="{ item, index }">
            <span class="inline vt-middle sign" v-if="getHomeStyleType - 1 === index">
              <i class="icon-font icon-chenggong1"></i>
            </span>
            <span class="inline vt-middle ml-xs">{{ item.label }}</span>
          </template>
        </tag-view>
        <visual-config
          class="visual-config"
          ref="visualConfigRef"
          :is-edit="isEdit"
          :style-type="styleType"
          :initial-list="collapseList"
          @deleteVisual="deleteVisual"
        ></visual-config>
      </div>
    </div>
    <div class="right-box">
      <div class="right-title">图表库</div>
      <Input
        v-model="searchText"
        search
        enter-button
        placeholder="请输入图表名称"
        class="ipt-box"
        @on-search="searchEchartsFn"
        @on-enter="searchEchartsFn"
      />
      <draggable
        class="collapse-box"
        v-model="readCollapseList"
        :move="onMove"
        :disabled="!isEdit"
        chosen-class="chosen-class"
        ghost-class="ghost-class"
        filter=".undraggable"
        @start="startMove"
        @end="endMove"
        :group="{
          name: 'home', //组名为itxst
          pull: true, //是否允许拖出当前组
          put: false, //是否允许拖入当前组
        }"
      >
        <div
          v-for="(item, index) in readCollapseList"
          :key="item.name"
          class="collapse-item"
          :class="{ 'collapse-item-drag': isEdit && !item?.visiualShow, 'undraggable': !isEdit || item?.visiualShow }"
          :draggable="isEdit && !item?.visiualShow ? 'true' : 'false'"
          :data-longechart="item.isLongEchart ? true : false"
          @click.stop="clickCollapseItem(item, index)"
        >
          <div
            class="collapse-title"
            :class="{ 'collapse-title-active': activeIndex === index, 'collapse-title-undrop': item?.visiualShow }"
          >
            <Icon class="c-item" :type="activeIndex === index ? 'md-arrow-dropdown' : 'md-arrow-dropright'" />
            <span class="c-title">{{ item.name }}</span>
            <span class="c-undrop" v-if="item?.visiualShow"> ( 已用 )</span>
            <span
              v-if="isEdit"
              class="icon-font-modify icon-font icon-bianji2 mr-xs base-text-color fr pointer undraggable"
              @click.stop="openModifyDialog(item, index)"
            ></span>
          </div>
          <div v-show="activeIndex === index" class="sample-img" :class="{ 'really-hidden': activeIndex !== index }">
            <img class="c-img" :src="getImageUrl(item)" draggable="false" alt="示例图" />
          </div>
        </div>
      </draggable>
    </div>
    <ui-modal title="编辑图表名称" :width="545" v-model="visible" @query="submitModal">
      <ui-label class="d_flex modal-context" label="图表名称">
        <div>
          <Input v-model="echartsName" clearable placeholder="请输入图表名称" :class="{ 'ipt-error': showError }">
          </Input>
          <div class="tip f-14" :class="{ 'tip-error': showError }">
            提示：字数限制6字以内，名称不可重复，名称不可为空。
          </div>
        </div>
      </ui-label>
    </ui-modal>
  </div>
</template>

<script>
import home from '@/config/api/home';
import draggable from 'vuedraggable';
import { defaultComponentList } from '@/views/home/<USER>/utils/componentConfig.js';
import { mapGetters, mapActions } from 'vuex';
export default {
  components: {
    TagView: require('@/components/tag-view.vue').default,
    VisualConfig: require('../visual-config/index.vue').default,
    draggable,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchText: '',
      activeIndex: -1,
      readCollapseList: [],
      collapseList: [],
      // 弹框
      visible: false,
      showError: false,
      echartsName: '',
      modalShowIndex: -1, // 对应总列表collapseList的索引
      // 拖拽
      activeDraggedItem: null, // 正在拖拽的元素
      activeToItem: null, // 经过的目标元素
      activeToContext: null,
      // 风格切换
      defaultActive: 0,
      styleType: '1',
      tagList: [
        {
          label: '风格1',
          value: 'style1',
          disabled: false,
        },
        {
          label: '风格2',
          value: 'style2',
          disabled: false,
        },
      ],
    };
  },
  watch: {
    getHomeStyleType(val) {
      this.styleType = val;
      this.defaultActive = val - 1;
    },
  },
  computed: {
    ...mapGetters({
      longEchartBoxShowNum: 'home/getLongEchartBoxShowNum',
      normalEchartBoxShowNum: 'home/getNormalEchartBoxShowNum',
      getHomeStyleType: 'systemconfiguration/getHomeStyleType',
    }),
  },
  created() {
    this.collapseList = this.$util.common.deepCopy(defaultComponentList);
    this.viewByParamKey();
    this.setHomeStyleType();
  },
  methods: {
    ...mapActions({
      setHomeStyleType: 'systemconfiguration/setHomeStyleType',
    }),
    getImageUrl(item) {
      return item[`imgUrl${this.styleType}`] || item.imgUrl;
    },
    changeStatus(index) {
      this.defaultActive = index;
      this.styleType = `${index + 1}`;
    },
    searchEchartsFn() {
      this.readCollapseList = this.collapseList.filter((item) => {
        return item.name.includes(this.searchText);
      });
    },
    clickCollapseItem(item, index) {
      this.activeIndex = this.activeIndex === index ? -1 : index;
    },
    openModifyDialog(item) {
      this.echartsName = item.name;
      this.modalShowIndex = this.collapseList.findIndex((col) => item.name === col.name);
      this.visible = true;
      this.showError = false;
    },
    submitModal() {
      let obj = this.validateFn();
      this.showError = false;
      if (obj.flag) {
        this.showError = true;
        this.$Message.warning(obj.message);
        return;
      }
      this.collapseList[this.modalShowIndex].name = this.echartsName;
      let index = this.readCollapseList.findIndex(
        (i) => i.componentId === this.collapseList[this.modalShowIndex].componentId,
      );
      this.readCollapseList[index].name = this.echartsName;
      this.visible = false;
      // 操作视图 - 修改名称
      this.$refs.visualConfigRef.outControlName(this.collapseList[this.modalShowIndex]);
    },
    // 修改图表名称时 校验
    validateFn() {
      let flag = this.collapseList.some((item, index) => {
        return item.name === this.echartsName && index !== this.modalShowIndex;
      });
      let messge = '';
      if (this.echartsName.length > 6) {
        messge = '无法保存，名称已超字数限制';
      } else if (!this.echartsName) {
        messge = '无法保存，名称不能为空';
      } else if (flag) {
        messge = '无法保存，名称已重复';
      }
      let tip = this.echartsName.length > 6 || !this.echartsName || flag;

      return {
        flag: tip,
        message: messge,
      };
    },
    // 重置
    resetFn() {
      this.readCollapseList = this.collapseList = this.$util.common.deepCopy(defaultComponentList);
      this.$refs.visualConfigRef?.resetBaseHomePosition();
      this.$Message.success('已重置');
    },
    // 保存按钮触发
    async updateByParamKey() {
      try {
        let componentsPositionList = this.$refs.visualConfigRef.componentsPositionList;
        let componentsList = [];
        componentsPositionList.forEach((one) => {
          componentsList.push(...one.componentList);
        });
        // isValidate 红色边框，保存验证失败
        let isValidate = true;
        componentsPositionList.forEach((item) => {
          if (!item.componentList.length) {
            isValidate = false;
            item.isValidate = false;
          } else {
            item.isValidate = true;
          }
        });
        if (!isValidate) {
          this.$Message.warning('无法保存，请配置每个框至少满足有1个图表');
          return;
        }
        //右侧没有 拖拽到首页布局的也应该 保存起来
        let noLayArr = this.readCollapseList.filter((r) =>
          componentsList.every((c) => c.componentId !== r.componentId),
        );
        let params = {
          paramKey: 'HOME_PAGE_VISIUAL_CONFIG',
          paramValue: JSON.stringify([...componentsList, ...noLayArr]),
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params);
        let params2 = {
          paramKey: 'HOME_STYLE',
          paramValue: this.styleType,
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params2);
        this.setHomeStyleType();
        // await this.$http.post(home.refreshIndexDeviceByConfig, params);
        this.$Message.success('保存成功');
        this.$emit('changeBtnList', 'save');
      } catch (e) {
        console.log(e);
      }
    },
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_VISIUAL_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        // 没有保存到 后端的，也要在 左侧列表显示
        if (!!data && 'paramValue' in data) {
          let arr = JSON.parse(data.paramValue) || [];
          let comArr = {};
          arr.forEach((item) => {
            comArr[item.componentId] = item;
          });
          let copyArr = this.$util.common.deepCopy(defaultComponentList);
          copyArr = copyArr.map((item) => {
            if (!comArr[item.componentId]) {
              item = { ...item, visiualShow: false, styleIndex: -1 };
              arr.push(item);
            } else {
              item = { ...item, ...comArr[item.componentId] };
            }
            return item;
          });
          this.readCollapseList = copyArr;
          this.collapseList = arr;
        } else {
          this.readCollapseList = this.collapseList;
        }
      } catch (e) {
        console.log(e);
      }
    },
    onMove({ relatedContext, draggedContext }, $event) {
      this.activeDraggedItem = null;
      this.activeToItem = null;
      this.activeToContext = null;
      // 禁止本列表移动，移入的元素有imgUrl，说明是本列表
      if (relatedContext.element?.imgUrl) {
        return false;
      }
      this.activeDraggedItem = draggedContext.element;
      this.activeToItem = relatedContext.element;
      this.activeToContext = $event;
      return false;
    },
    startMove(e) {
      // 长图表，需要占据 两个dom  --- 添加边框提示
      if (e.item?.dataset?.longechart) {
        this.$refs.visualConfigRef?.addClassFn();
      }
    },
    endMove(e) {
      // 移除边框提示
      this.$refs.visualConfigRef?.removeClassFn();

      // e.originalEvent.cancelable ： false --拖拽完成时，不在目标区域内
      if (!this.activeDraggedItem || !this.activeToItem || !e.originalEvent.cancelable) return;
      // 图表已展示，不能拖拽成功
      if (this.activeDraggedItem.visiualShow) {
        this.$Message.warning('已有此图表,请拖拽调整位置');
        return;
      }

      // 长图表 只能 拖到  3.4区域
      if (this.activeDraggedItem?.isLongEchart && !['3', '4'].includes(this.activeToItem.styleIndex)) return;

      /**
       * 如果有这个字段[存的组件名称]，代表替换组件
       * -1 代表移到了（可添加1个图表的文字）上
       */
      let toReplaceTab = this.activeToContext.srcElement.id;

      // 普通图表：如果已经有2个图表提示  长图表： 3 个则提示  （其中： 排除调  正常图表 《--- 转换---》长图表，这个过程是直接清空 ）
      let maxLen =
        this.activeDraggedItem?.isLongEchart || this.activeToItem?.isLongEchartBox
          ? this.longEchartBoxShowNum
          : this.normalEchartBoxShowNum;
      let flag =
        (!this.activeDraggedItem?.isLongEchart && this.activeToItem?.isLongEchartBox) ||
        (this.activeDraggedItem?.isLongEchart && !this.activeToItem?.isLongEchartBox);
      if ((!toReplaceTab || toReplaceTab === '-1') && this.activeToItem.componentList.length >= maxLen && !flag) {
        this.$Message.warning('图表已满，请移至标题替换');
        return;
      }
      // 移过去，修改visiualShow状态为true,修改styleIndex位置
      let styleIndex = this.activeDraggedItem?.isLongEchart ? '3' : this.activeToItem.styleIndex;
      this.activeDraggedItem.visiualShow = true;
      this.activeDraggedItem.styleIndex = styleIndex;
      // 操作视图
      this.$refs.visualConfigRef.outControlComponent(
        styleIndex,
        this.activeDraggedItem,
        this.activeToContext,
        this.activeToItem,
      );
    },
    deleteVisual(componentId) {
      let one = this.readCollapseList.find((item) => item.componentId === componentId);
      one.visiualShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.layout-config-container {
  display: flex;
  flex-direction: row;
  .sign {
    background-color: #fff;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    .icon-chenggong1 {
      color: #0dbf0d;
    }
  }

  .tag-view {
    position: absolute;
    left: 37.8%;
    top: 8.5%;
  }
  .visual-config {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 4%;
    left: -8.5%;
    transform: scale(0.8);
  }
}
.left-box {
  width: 100%;
  height: 100%;
  padding: 5px;
  background: #0d192b;
  .bg {
    background: url(~@/assets/img/home/<USER>/bg.png);
  }
}
.left-box-edit {
  background: #1a2534;
  .bg {
    background: url(~@/assets/img/home/<USER>/bg.png);
  }
}
.bg {
  width: 100%;
  height: 100%;
}

.right-box {
  width: 360px;
  height: 100%;
  border-radius: 0px 0px 4px 0px;
  background: var(--bg-content);
  display: flex;
  flex-direction: column;
  align-items: center;
  .right-title {
    width: 100%;
    height: 34px;
    padding: 5px 0;
    background: var(--bg-navigation);
    font-size: 16px;
    color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ipt-box {
    padding: 10px 20px;
    /deep/ .ivu-input-group-append {
      width: 36px !important;
      height: 34px;
      padding: 0 !important;
    }
  }
  .collapse-box {
    width: 100%;
    height: 100%;
    padding: 0 20px;
    color: var(--color-label);
    font-size: 14px;
    overflow: hidden;
    overflow-y: auto;
    // 拖拽元素 - 阴影
    @{_deep}.chosen-class {
      .sample-img {
        display: block !important;
      }
    }
    @{_deep}.ghost-class {
      .really-hidden {
        &.sample-img {
          display: none !important;
        }
      }
    }
    .collapse-item-drag {
      cursor: move;
    }
    .icon-font-modify {
      font-size: 15px !important;
      display: none;
    }
    .collapse-title-active,
    .collapse-title:hover {
      color: #fff !important;
      background: var(--color-primary);
      .c-undrop {
        color: #fff !important;
      }
      .icon-font-modify {
        display: block;
      }
    }
    .collapse-title-undrop {
      color: var(--color-label);
    }
    .collapse-title {
      height: 34px;
      line-height: 34px;
      .c-item {
        padding: 0 5px;
      }
      .c-undrop {
        color: #d1651c;
        font-size: 12px;
      }
    }
    .sample-img {
      background: #041129;
      padding: 10px;
      .c-img {
        max-width: 100%;
        height: auto;
      }
    }
  }
}
.modal-context {
  padding: 10px 35px;
  .tip {
    color: #c76d28;
  }
  .ipt-error /deep/ .ivu-input,
  .ipt-error /deep/ .ivu-input:focus {
    border: 1px solid #bc3c19;
  }
  .tip-error {
    color: #bc3c19;
  }
}
</style>
