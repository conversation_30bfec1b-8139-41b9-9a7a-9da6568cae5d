<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #detectionMode="{ row }">
        {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
      </template>
      <template #phyStatus="{ row }">
        <span>
          {{ !row.phyStatusText ? '--' : row.phyStatusText }}
        </span>
      </template>
      <template slot="qualified" slot-scope="{ row, column }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
        <Tooltip
          transfer
          placement="bottom"
          v-if="
            !column.noShowTooltip &&
            row.detectionMode != null &&
            (row.additionalImageText != null || row.areaImageText != null || row.dateImageText != null)
          "
        >
          <i class="icon-font icon-wenhao ml-xs f-12" :style="{ color: 'var(--color-warning)' }"> </i>
          <div slot="content">
            <json-viewer
              :expand-depth="5"
              v-if="row.dateImageText != null"
              :value="JSON.parse(row.dateImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.additionalImageText != null"
              :value="JSON.parse(row.additionalImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
            <json-viewer
              :expand-depth="5"
              v-if="row.areaImageText != null"
              :value="JSON.parse(row.areaImageText)"
              theme="my-awesome-json-theme"
            ></json-viewer>
          </div>
        </Tooltip>
      </template>
      <template #reason="{ row }">
        <Tooltip :content="row.reason" transfer max-width="150">
          {{ row.reason }}
        </Tooltip>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          icon="icon-bofangshipin"
          content="播放视频"
          @click.native="clickRow(row)"
          class="mr-sm"
          :styles="{ color: 'var(--color-primary)' }"
        ></ui-btn-tip>
        <ui-btn-tip
          icon="icon-chakanxiangqing"
          content="查看详情"
          :styles="{ color: 'var(--color-primary)' }"
          @handleClick="showModal(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <video-player
      :playDeviceCode="playDeviceCode"
      v-model="videoVisible"
      :video-url="videoUrl"
      @onCancel="videoUrl = ''"
    ></video-player>
    <review-and-detail
      v-model="reviewDetailVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="osdDetailData"
      :table-data="tableData"
      :qualified-list="qualifiedList"
      :styles="{ width: detailModelWidth, height: detailModelHeight }"
      :title="reviewDetailTitle"
      :review-visible="reviewVisible"
      :is-get-detail-byapi="isGetDetailByapi"
      :get-detail-info="getDetailInfo"
    ></review-and-detail>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import vedio from '@/config/api/vedio-threm';
export default {
  name: 'video-osd',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    detailModelWidth: {
      type: String,
      default: '',
    },
    detailModelHeight: {
      type: String,
      default: '',
    },
    searchData: {
      type: Object,
      default: () => {},
    },
    isGetDetailByapi: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.45rem',
      },
      formData: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      reviewDetailVisible: false,
      reviewVisible: false,
      reviewDetailTitle: '',
      osdDetailData: {},
      qualifiedList: [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ],
    };
  },
  methods: {
    //视频播放
    async clickRow(row) {
      try {
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    showModal(row) {
      // row.detectionMode === 1 ? 'OCR' : 'SDK'
      if (row.qualified !== '1' && (row.isScreenShot !== 1 || !row.screenShot) && row.detectionMode === 1) {
        let text = row.dataMode === '3' ? '人工复核不合格' : row.errorCodeName;
        this.$Message.error(text);
        return;
      }
      this.reviewDetailVisible = true;
      this.reviewVisible = false;
      this.reviewDetailTitle = '查看详情';
      this.osdDetailData = { ...row };
    },
    async getDetailInfo(params) {
      try {
        const {
          data: { data },
        } = await this.$http.get(evaluationoverview.queryOsdDetailById + params.id);
        const rowType = [
          { dataKey: 'clockDetail', dataValue: '时钟信息' },
          { dataKey: 'areaInfo', dataValue: '区划和地址' },
          { dataKey: 'deviceInfo', dataValue: '摄像机信息' },
        ];
        let detialTableData = [];
        rowType.forEach((item) => {
          if (!!data[item.dataKey] && !!data[item.dataKey].isDetect) {
            let rowData = {
              colTitle: item.dataKey,
              colTitleText: item.dataValue,
              ...data[item.dataKey],
            };
            detialTableData.push(rowData);
          }
        });
        if (data.qualified) {
          detialTableData.push({ resQualified: data.qualified });
        }
        return {
          detialTableData,
          imageUrl: data.imageUrl,
          info: data,
        };
      } catch (e) {
        console.log(e);
        return {
          detialTableData: [],
          imageUrl: '',
          info: {},
        };
      }
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/index.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
