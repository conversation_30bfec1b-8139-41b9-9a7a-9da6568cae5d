<template>
  <section class="flex">
    <aside class="menu-container" v-if="menuLoading === false">
      <div class="search-content">
        <Input placeholder="在结果中搜索" v-model="resourceName">
          <Icon
            type="ios-search"
            class="font-16 cursor-p"
            slot="suffix"
            maxlength="50"
            @click.prevent="searchName(resourceName)"
          />
        </Input>
      </div>
      <div v-if="menuList.length > 0" class="menu">
        <Menu
          class="menu-content"
          width="auto"
          :active-name="menuList[0].resourceList[0].resourceName"
          :open-names="[0]"
          @on-select="handleSelect"
        >
          <Submenu
            v-for="(item, index) of searchMenuList"
            :name="index"
            :key="index"
          >
            <template slot="title">
              <i class="iconfont icon-shujuyuan color-primary"></i>
              {{ item.catalogName }}
            </template>
            <template v-for="(it, i) of item.resourceList">
              <MenuItem v-show="it.num > 0" :key="i" :name="it.resourceName">
                <!-- <MenuItem v-if="it.num > 0" :key="i" :name="it.resourceName" @click.native="handleNodeClick(it)"> -->
                <span class="text-name ellipsis">{{ it.resourceNameCn }}</span>
                <b class="color-primary">{{ it.num }}</b>
              </MenuItem>
            </template>
            <!-- <el-tree :data="item.resourceList" :props="defaultProps" :highlight-current="true" node-key="id" :current-node-key="menuList[0].resourceList[0].resourceName" @node-click="handleNodeClick">
              <div class="custom-tree-node" slot-scope="{ data }">
                <span>{{ data.resourceNameCn }}</span>
                <b class="color-primary">{{ data.num }}</b>
              </div>
            </el-tree> -->
          </Submenu>
        </Menu>
      </div>
      <ui-empty v-if="menuList.length === 0"></ui-empty>
    </aside>
    <section class="main-container" v-if="menuLoading === false">
      <!-- 档案 -->
      <div
        class="person"
        v-if="Object.values(ARCHIVE_TYPE).includes(selectMenuItemName)"
      >
        <!-- 一机一档查询 -->
        <SearchDevice
          ref="search"
          v-if="selectMenuItemName === ARCHIVE_TYPE.de"
          @searchForm="exactsSearch"
        />
        <!-- 一车一档查询 -->
        <SearchVehicle
          ref="search"
          v-if="selectMenuItemName === ARCHIVE_TYPE.ve"
          @searchForm="exactsSearch"
          :searchText="'更多条件'"
        />
        <!-- 一人一档精准检索查询 -->
        <SearchPerson
          ref="search"
          v-if="[ARCHIVE_TYPE.vi, ARCHIVE_TYPE.rn].includes(selectMenuItemName)"
          :dataType="selectMenuItemName"
          :page="'cloudSearch'"
          :searchText="'更多条件'"
          @searchForm="exactsSearch"
        />
        <div class="card-content">
          <div
            v-for="(item, index) in list"
            :key="index"
            :class="item.type === 'people' ? 'people-card' : 'video-card'"
            class="card-item"
          >
            <UiListCard
              v-if="loading === false"
              :type="item.type"
              :data="item"
              :isChange="item.realNameArchiveNo ? true : false"
              :index="index"
              @archivesDetailHandle="archivesDetailHandle"
              @on-change="changeCardHandle(item, index)"
              @collection="collection"
            />
          </div>
          <ui-empty v-if="list.length === 0"></ui-empty>
        </div>
        <!-- 分页 -->
        <ui-page
          class="page"
          :current="params.pageNumber"
          :total="total"
          countTotal
          :page-size="params.pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        />
      </div>
      <!-- 人/车 -->
      <div class="table-container" v-else>
        <SearchMessage
          v-if="searchList && searchList.length > 0"
          ref="search"
          :search-list="searchList"
          @searchForm="exactsSearch"
        />
        <div class="list-content">
          <PoliceOther
            v-if="list && list.length > 0"
            :list="list"
            @checkDetail="checkDetail"
          />
          <ui-empty v-if="list.length === 0"></ui-empty>
        </div>
        <!-- 分页 -->
        <ui-page
          class="page"
          :current="params.pageNumber"
          :total="total"
          countTotal
          :page-size="params.pageSize"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        />
        <!-- 详情 -->
        <PoliceDetail ref="detail" />
      </div>
      <ui-loading v-if="loading"></ui-loading>
    </section>
    <ui-loading v-if="menuLoading"></ui-loading>
  </section>
</template>
<script>
import UiListCard from "@/components/ui-list-card";
import SearchMessage from "../../ordinary-search/components/search-message.vue";
import PoliceDetail from "../../ordinary-search/components/police-detail.vue";
import PoliceOther from "../../ordinary-search/components/police-other.vue";
import SearchPerson from "@/views/holographic-archives/one-person-one-archives/real-name-file/components/search.vue";
import SearchVehicle from "@/views/holographic-archives/one-vehicle-one-archives/components/search.vue";
import SearchDevice from "@/views/holographic-archives/one-plane-one-archives/components/search.vue";
import { personBaseInfo } from "@/api/realNameFile";
import {
  selectCatalog,
  realRecordSearch,
  querySearch,
  queryPoliceData,
  getEveryTypeArchiveTotalApi,
  getEsIndexStatisticsListApi,
} from "@/api/wisdom-cloud-search";
import { isArray } from "util";
import { mapActions } from "vuex";

const ARCHIVE_TYPE = {
  vi: "videoArchive", // 视频档案
  rn: "realNameArchive", // 实名档案
  de: "deviceArchive", // 设备档案
  ve: "vehicleArchive", // 机动车档案
};

export default {
  name: "policeServiceContent",
  components: {
    UiListCard,
    SearchPerson,
    SearchVehicle,
    SearchDevice,
    SearchMessage,
    PoliceDetail,
    PoliceOther,
  },
  data() {
    this.ARCHIVE_TYPE = Object.freeze(ARCHIVE_TYPE);
    return {
      selectMenuItemName: null,
      formRight: {},
      menuLoading: false,
      searchLoading: false, //资源查询条件
      resourceId: "", //信息表需要的资源id,
      resourceName: "", //左侧树查询值
      searchList: [], //信息表查询条件
      menuList: [],
      cardType: "people", //卡片默认类型
      list: [], //列表
      loading: false,
      //档案搜索项
      seachForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      defaultProps: {
        children: "children",
        label: "label",
      },
      buttonProps: {
        type: "default",
        size: "small",
      },
      queryRecord: {}, // 菜单查询记录
    };
  },
  computed: {
    // ...mapGetters({
    //   pageType: 'common/getPageType' // 页面类型
    // }),
    searchMenuList() {
      var searchList = [];
      if (this.resourceName == "") {
        searchList = JSON.parse(JSON.stringify(this.menuList));
        return searchList;
      }
      this.menuList.forEach((item) => {
        var arr = item.resourceList.filter((ite) => {
          return ite.resourceNameCn.includes(this.resourceName);
        });
        if (arr.length > 0) {
          var row = JSON.parse(JSON.stringify(item));
          row.resourceList = arr;
          searchList.push(row);
        }
      });
      return searchList;
    },
  },
  async created() {
    await this.selectCatalog();
    this.getDictData();
    // console.log(this.searchMenuList, 'searchList')
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 左侧树
    async selectCatalog() {
      // this.menuLoading = true
      let data = {
        treeResourceName: this.resourceName,
      };
      let res = await selectCatalog(data);
      this.menuList = res.data || [];
      // 获取第一个资源列表
      this.getInfo(this.menuList[0].resourceList[0].resourceName);
      // 树循环获取对应资源列表
      if (this.menuList.length > 0) {
        let res1 = await getEveryTypeArchiveTotalApi();
        let res2 = await getEsIndexStatisticsListApi();
        let data = [...res1.data, ...res2.data];
        for (const e of this.menuList) {
          if (e.resourceList && e.resourceList.length > 0) {
            // 循环资源,添加资源个数
            for (let item of e.resourceList) {
              let num = data.find(
                (e) => e.archivesType == item.resourceName
              ).archivesCount;
              this.$set(item, "num", num);
            }
          }
        }
      }
      this.menuLoading = false;
    },
    // 列表查询
    async realRecordSearch(val, id) {
      let data = {
        ...this.seachForm,
        ...this.params,
        treeResourceName: val,
        resourceId: id,
      };
      if (val === ARCHIVE_TYPE.rn) {
        data.dataType = 1;
      } else if (val === ARCHIVE_TYPE.vi) {
        data.dataType = 2;
      } else if (
        ![ARCHIVE_TYPE.vi, ARCHIVE_TYPE.rn, ARCHIVE_TYPE.de].includes(val)
      ) {
        data.resourceId = this.resourceId || id;
      }

      if (
        this.selectMenuItemName &&
        this.queryRecord[this.selectMenuItemName]
      ) {
        data.params = this.queryRecord[this.selectMenuItemName].params;
      }

      if (data.labelIds && data.labelIds.length > 0) {
        data.labelIds = data.labelIds.filter((item) => {
          return item != null;
        });
      }
      let res = await realRecordSearch(data);
      if (res.data) {
        const {
          entities = [],
          pageNumber = 1,
          pageSize = 20,
          total = 0,
        } = res.data;
        let params = {
          pageNumber: pageNumber,
          pageSize: pageSize,
        };
        return [total, entities, params];
      } else {
        return [0, [], { pageNumber: 1, pageSize: 20 }];
      }
    },
    // 渲染列表的返回值
    async getInfo(val) {
      this.loading = true;
      this.selectMenuItemName = val;
      let list = await this.realRecordSearch(val);
      if (Object.values(ARCHIVE_TYPE).includes(val)) {
        // 档案添加卡片type类型
        if (list[1] && list[1].length > 0) {
          let type = "";
          switch (val) {
            case ARCHIVE_TYPE.rn:
              type = "people";
              break;
            case ARCHIVE_TYPE.vi:
              type = "video";
              break;
            case ARCHIVE_TYPE.de:
              type = "device";
              break;
            default:
              type = "vehicle";
          }
          list[1].map((item) => (item.type = type));
        }
      }
      this.list = list[1];
      this.params = list[2];
      this.total = list[0];
      this.loading = false;
    },
    // 警务信息表检索条件
    async querySearch() {
      this.searchLoading = true;
      let res = await querySearch(this.resourceId);
      this.searchList = res.data;
      this.searchLoading = false;
    },
    // 警务左侧树查询
    searchName(val) {
      this.resourceName = val;
      this.selectCatalog();
    },
    // 跳转档案详情
    archivesDetailHandle(item) {
      let query = {};
      let name = "";
      if (
        [ARCHIVE_TYPE.vi, ARCHIVE_TYPE.rn].includes(this.selectMenuItemName)
      ) {
        // 实名和视频跳转参数处理
        name = item.type === "video" ? "video-archive" : "people-archive";
        let type = item.type === "people" ? "people" : "video";
        let archiveNo =
          this.selectMenuItemName === ARCHIVE_TYPE.rn
            ? item.archiveNo
            : item.type === "people"
            ? item.realNameArchiveNo
            : item.archiveNo;
        query = {
          archiveNo: archiveNo,
          source: type,
          initialArchiveNo: archiveNo,
        };
      } else if (this.selectMenuItemName === ARCHIVE_TYPE.de) {
        // 设备档案跳转参数
        name = "device-archive";
        query = {
          archiveNo: item.deviceId,
        };
      } else if (this.selectMenuItemName === ARCHIVE_TYPE.ve) {
        name = "vehicle-archive";
        query = {
          archiveNo: JSON.stringify(item.archiveNo),
          plateNo: JSON.stringify(item.plateNo),
          source: "car",
          idcardNo: item.idcardNo,
        };
      }
      const { href } = this.$router.resolve({
        name: name,
        query: query,
      });
      window.open(href, "_blank");
    },
    // 左侧树切换
    handleSelect(e) {
      this.seachForm = {};
      this.menuList.forEach((item) => {
        return item.resourceList.forEach((row) => {
          if (row.resourceName == e) {
            this.handleNodeClick(row);
            return;
          }
        });
      });
    },
    // 左侧树切换
    handleNodeClick(e) {
      this.selectMenuItemName = e.resourceName;
      this.resourceId = e.id;
      if (Object.values(ARCHIVE_TYPE).includes(e.resourceName)) {
        this.$nextTick(() => {
          // this.$refs.search.clearLabel()
          this.$refs.search.formData.labelIds = [];
          this.$refs.search.setData(this.queryRecord[this.selectMenuItemName]);
          // this.$refs.search.resetHandle()
        });
      } else {
        this.querySearch();
        this.getInfo(e.resourceName);
      }
    },
    /**
     * 点击收藏返回方法
     */
    collection() {
      // this.$refs.search.setData(this.queryRecord[this.selectMenuItemName])
    },
    // 精准查询
    exactsSearch(form) {
      var labelIds = [];
      let formData = { ...form };
      if (form.labelIds && form.labelIds.length > 0) {
        form.labelIds.forEach((item) => {
          if (item && item != undefined) {
            labelIds.push(item.id);
          }
        });
        formData.labelIds = labelIds;
      }
      this.queryRecord[this.selectMenuItemName] = form; // 用于反显的数据
      if (this.selectMenuItemName === ARCHIVE_TYPE.rn) {
        //实名
        this.seachForm = {
          ...formData,
          similarity: ((formData.similarity || 0) / 100).toFixed(2),
        };
        this.queryRecord[this.selectMenuItemName] = {
          ...form,
          similarity: this.seachForm.similarity,
        };
      } else if (this.selectMenuItemName === ARCHIVE_TYPE.vi) {
        //视频
        this.seachForm = {
          archiveNo: formData.idcardNo,
          features: formData.features,
          similarity: ((formData.similarity || 0) / 100).toFixed(2),
          labelIds: formData.labelIds,
          labelType: formData.labelType,
        };
        this.queryRecord[this.selectMenuItemName] = {
          ...form,
          similarity: this.seachForm.similarity,
          archiveNo: this.seachForm.archiveNo,
          features: this.seachForm.features,
        };
      } else if (this.selectMenuItemName === ARCHIVE_TYPE.ve) {
        // 车辆
        this.seachForm = {
          ...formData,
        };
        delete this.seachForm.bodyTypeobj;
      } else {
        this.seachForm = {
          ...formData,
        };
      }
      this.params.pageNumber = 1;
      this.getInfo(this.selectMenuItemName);
    },
    // 视频卡片切实名获取基本信息
    async personBaseInfo(val) {
      let info = {};
      let data = {
        archiveNo: val,
        dataType: 1,
      };
      await personBaseInfo(data).then((res) => {
        info = res.data;
        info.type = "people";
      });
      return info;
    },
    // 视频卡片切换
    async changeCardHandle(item, index) {
      if (this.list[index].type === "video") {
        let obj = await this.personBaseInfo(item.realNameArchiveNo);
        this.list[index].type = "people";
        this.list[index].xm = obj.xm;
        this.list[index].xbdm = obj.xbdm;
        this.list[index].mzdm = obj.mzdm;
        this.list[index].jgDzmc = obj.jgDzmc;
        this.list[index].gmsfhm = obj.gmsfhm;
        this.list[index].photos = obj.photos;
        // this.list[index].lableIds = obj.lableIds
      } else {
        this.list[index].type = "video";
      }
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getInfo(this.selectMenuItemName);
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getInfo(this.selectMenuItemName);
    },
    // 人/车详情
    checkDetail(item) {
      var param = {
        // resourceId: 18,
        // treeResourceName: "ipbd_ga_czrkhkqyxx",
        // params: {
        //   id: {
        //     searchValue: "nnT_QoEBPDtQ_v5NUaRn",
        //     searchType: 1
        //   }
        // }
        resourceId: this.resourceId,
        treeResourceName: this.selectMenuItemName,
        params: {
          id: {
            searchValue: item.id,
            searchType: 1,
          },
        },
      };
      queryPoliceData(param).then((res) => {
        this.$refs.detail.show(res.data);
      });
    },
    getCurrentMenuInfo() {
      if (
        this.selectMenuItemName &&
        this.queryRecord[this.selectMenuItemName]
      ) {
        return this.queryRecord[this.selectMenuItemName].params;
      } else {
        return null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
.menu-container {
  border-right: 1px solid #d3d7de;
  width: 280px;
  padding: 16px 0 0;
  z-index: 10;
  position: relative;
  .search-content {
    padding: 0 15px;
    margin-bottom: 10px;
  }
  .menu {
    height: calc(~"100% - 50px");
    overflow: auto;
  }
  .menu-content {
    &:after {
      display: none;
    }
    /deep/ .ivu-menu-submenu {
      .ivu-menu-submenu-title {
        background: #f9f9f9;
        box-shadow: inset 0px -1px 0px 0px #d3d7de;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        font-size: 16px;
        padding-left: 16px;
        padding-top: 10px;
        padding-bottom: 10px;
        padding-right: 19px;
      }
      .ivu-menu-item {
        display: flex;
        justify-content: space-between;
        padding-top: 7px;
        padding-bottom: 7px;
        &:after {
          display: none;
        }
        b {
          color: #2c86f8;
        }
        &.ivu-menu-item-active {
          background: #2c86f8;
          color: #fff;
          b {
            color: #fff;
          }
        }
      }
    }
    .text-name {
      width: 180px;
      display: inline-block;
    }
  }
}

/deep/.el-tree {
  .el-tree-node__content {
    padding-top: 6px;
    padding-bottom: 6px;
    height: auto;
    & > .el-tree-node__expand-icon {
      margin-left: 15px;
      color: #888888;
      &.is-leaf {
        color: transparent;
      }
    }
  }
  .el-tree-node {
    &.is-current {
      & > .el-tree-node__content {
        background-color: #2c86f8;
        .el-tree-node__expand-icon {
          color: #fff;
          &.is-leaf {
            color: transparent;
          }
        }
        .custom-tree-node {
          span {
            color: #fff;
            flex: 1;
          }
          b {
            color: #fff;
          }
        }
      }
    }
  }
  .custom-tree-node {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 20px;
    span {
      color: rgba(0, 0, 0, 0.8);
      flex: 1;
    }
  }
}
.main-container {
  height: 100%;
  width: 100%;
  .person {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 16px 20px 0 20px;
    position: relative;
    .card-content {
      display: flex;
      flex-wrap: wrap;
      overflow: auto;
      flex: 1;
      margin: 0 -5px;
      align-content: flex-start;
      position: relative;
      .card-item {
        width: 25%;
        padding: 0 5px;
        box-sizing: border-box;
        // margin-bottom: 10px;
        transform-style: preserve-3d;
        transition: transform 0.6s;
        .list-card {
          width: 100%;
          backface-visibility: hidden;
          height: 198px;
          /deep/.list-card-content-body {
            height: 115px;
          }
          /deep/.content-img {
            width: 115px;
            height: 115px;
          }
          /deep/.tag-wrap {
            margin-top: 7px;
            .ui-tag {
              margin: 0 5px 0 0 !important;
            }
          }
        }
      }
    }
  }
  .table-container {
    height: 100%;
    .list-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      height: 100%;
      overflow-y: auto;
      position: relative;
      padding-top: 20px;
    }
  }
  .page {
    padding: 0px 20px 0px 19px;
  }
}
/deep/ .ivu-icon-md-arrow-dropdown {
  color: #888888;
}
/deep/ .ivu-menu-submenu-title-icon {
  right: 9px;
}
</style>
