<template>
  <div>
    <ui-modal :title="title" v-model="visible" :styles="styles">
      <div class="wrap">
        <div class="bar">
          <div class="title">
            {{ transferTitles[0] }}
          </div>
          <ui-search-tree
            placeholder="请输入组织机构名称"
            nodeKey="id"
            ref="tree"
            :check-strictly="true"
            :show-checkbox="true"
            :scroll="370"
            :tree-data="treeData"
            :default-props="defaultProps"
            @check="check"
          >
          </ui-search-tree>
        </div>
        <div class="button-div">
          <div
            class="mb-sm sign"
            :class="[targetCheckData && targetCheckData.length != 0 ? 'btn_color' : '']"
            @click="deleteKey"
          >
            <Icon type="ios-arrow-back" class="sign-icon" />
          </div>
          <div class="sign" :class="[checkData && checkData.length != 0 ? 'btn_color' : '']" @click="add">
            <Icon type="ios-arrow-forward" class="sign-icon" />
          </div>
        </div>
        <div class="bar">
          <div class="title">
            {{ transferTitles[1] }}
          </div>
          <ui-search-tree
            placeholder="请输入组织机构名称"
            nodeKey="id"
            ref="targetTree"
            :show-checkbox="true"
            :scroll="370"
            :tree-data="targetData"
            :default-props="defaultTargetProps"
            :default-keys="defaultTargetKeys"
            @check="checkTarget"
          >
          </ui-search-tree>
        </div>
      </div>
      <template slot="footer">
        <Button type="primary" @click="query" class="plr-30">确 定</Button>
        <Button type="text" @click="cancel" class="plr-30">取 消</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.wrap {
  display: flex;
  justify-content: space-around;
  align-items: center;
  .bar {
    width: 400px;
    border: 1px solid #2e4e65;
    .title {
      line-height: 30px;
      background: #2e4e65;
      color: #55c5f2;
      padding-left: 10px;
      margin-top: 80px;
    }
    .target {
      padding: 10px;
    }
  }
  .button-div {
    .sign {
      background: #959ba8;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      .sign-icon {
        font-size: 20px;
        color: #fff;
      }
    }
    .btn_color {
      background: #55c5f2;
    }
  }
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      styles: {
        top: '0.4rem',
        width: '6rem',
      },
      visible: false,
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      defaultKeys: [],
      targetData: [], //右边树结构数据
      checkData: [],
      defaultTargetProps: {
        label: 'name',
        children: 'targetChild',
      },
      defaultTargetKeys: [], //默认选中数据
      targetCheckData: [], //右边选中数据
      targetCheckedKeys: [], //右边选中key
    };
  },
  created() {},
  mounted() {},
  methods: {
    check(checkedKeys, data, checkData) {
      this.checkData = this.$util.common.deepCopy(checkData.checkedNodes);
    },
    add() {
      let tempArr = this.$util.common.deepCopy(this.checkData);
      // 组装树结构数据
      this.targetData = this.$util.common.arrayToJson(tempArr, 'id', 'parentId', 'targetChild');
      this.checkData = [];
    },
    checkTarget(checkedKeys, data, checkData) {
      this.targetCheckedKeys = checkedKeys;
      this.targetCheckData = checkData.checkedNodes;
    },
    deleteKey() {
      this.targetCheckedKeys.forEach((row) => {
        let node = this.$refs.targetTree.getNode(row);
        let parent = node.parent;
        let children = parent.data.targetChild || parent.data;
        let index = children.findIndex((d) => d.orgCode === row);
        children.splice(index, 1);
        this.$refs.tree.setChecked(row, false);
      });
      this.targetCheckData = [];
    },
    query() {
      this.$emit('putCheckedKeys', this.$refs.tree.getCheckedKeys());
      this.visible = false;
    },
    cancel() {
      this.visible = false;
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    permissionTargetKeys(val) {
      // 选中左侧树
      this.$refs.tree.setCheckedKeys(val);
      // 获取选中的节点node
      let checkedNodes = this.$refs.tree.getCheckedNodes();
      this.checkData = this.$util.common.deepCopy(checkedNodes);
      this.add();
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
    }),
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    title: {
      default: '设备权限',
    },
    transferTitles: {
      required: true,
      type: Array,
    },
    permissionTargetKeys: {
      required: true,
      type: Array,
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
