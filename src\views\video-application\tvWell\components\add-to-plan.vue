<template>
  <ui-modal
    v-model="visible"
    title="保存为上墙预案"
    :r-width="450"
    @onOk="comfirmHandle"
    @onCancel="comfirmCancle"
  >
    <div class="list">
      <div class="label">预案名称:</div>
      <Input v-model="planName" :maxlength="20" placeholder="请输入" />
    </div>
  </ui-modal>
</template>
<script>
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      visible: false,
      planName: "",
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  methods: {
    // 初始化
    show() {
      this.visible = true;
      this.planName = "";
    },
    // 确认提交
    comfirmHandle() {
      if (!this.planName) {
        this.$Message.error("请输入预案名称");
        return;
      }
      this.$emit("addPlan", this.planName);
      this.visible = false;
    },
    comfirmCancle() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.list {
  overflow-y: auto;
  .label {
    margin-bottom: 10px;
  }
}
</style>
