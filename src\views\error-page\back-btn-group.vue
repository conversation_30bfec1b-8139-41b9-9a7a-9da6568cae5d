<template>
  <div>
    <Button type="primary" @click="backHome">返回首页</Button>
    <Button type="primary" @click="backPrev">返回上一页</Button>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getToken } from "@/libs/configuration/util.common";
import "./error.less";
export default {
  name: "BackBtnGroup",
  data() {
    return {
      // second: 5,
      // timer: null
    };
  },
  mounted() {},
  beforeDestroy() {},
  computed: {
    ...mapGetters({ addRouters: "addRouters" }),
  },
  methods: {
    backHome() {
      // 登录失效或404刷新后跳转
      if (getToken() && this.addRouters && this.addRouters.length > 0) {
        this.$router.replace({
          path: this.addRouters[0].path,
        });
      } else {
        this.$router.replace({
          name: "Login",
        });
      }
    },
    backPrev() {
      this.$router.go(-1);
    },
  },
};
</script>
