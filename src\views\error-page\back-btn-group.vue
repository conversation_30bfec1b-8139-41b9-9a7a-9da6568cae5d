<template>
  <div>
    <Button type="primary" @click="backHome">返&nbsp;回</Button>
    <!-- <Button type="primary" @click="backPrev">返回上一页</Button> -->
  </div>
</template>

<script>
import './error.less';
export default {
  name: 'backBtnGroup',
  data() {
    return {
      // second: 5,
      // timer: null
    };
  },
  methods: {
    backHome() {
      // this.$router.replace({
      //   name: this.$config.homeName,
      // });
      this.$router.push('/login');
    },
    backPrev() {
      this.$router.go(-1);
    },
  },
  mounted() {
    // this.timer = setInterval(() => {
    //   // if (this.second === 0) this.backPrev()
    //   // else this.second--
    // }, 1000)
  },
  beforeDestroy() {
    // clearInterval(this.timer)
  },
};
</script>
