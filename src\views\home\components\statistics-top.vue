<template>
  <div class="statistics-container" :class="getFullscreen ? 'full-screen-container' : ''">
    <div class="statistics-wrapper" v-for="(item, index) in queryAccessData" :key="index">
      <top-star class="top-star"></top-star>
      <span :class="['icon-font', 'fs34', 'vt-middle', 'icon-color', 'fl', 'mr-sm', item.icon]"></span>
      <p class="count ellipsis" :style="{ color: item.color }">
        <countTo :startVal="0" :endVal="item[item.key] || 0" :duration="3000"></countTo>
      </p>
      <p class="name">{{ item.label }}</p>
    </div>
    <!--    <div class="statistics-wrapper mr-md">-->
    <!--      <top-star class="top-star"></top-star>-->
    <!--      <span-->
    <!--        class="icon-font icon-shitujichushuju fs34 vt-middle icon-color fl mr-md"-->
    <!--      ></span>-->
    <!--      <p class="count color1 ellipsis">-->
    <!--        <countTo-->
    <!--          :startVal="0"-->
    <!--          :endVal="queryAccessDataCount.deviceTotalAmount || 0"-->
    <!--          :duration="3000"-->
    <!--        ></countTo>-->
    <!--      </p>-->
    <!--      <p class="name">设备总量</p>-->
    <!--    </div>-->
    <!--    <div class="statistics-wrapper mr-md">-->
    <!--      <top-star class="top-star"></top-star>-->
    <!--      <span-->
    <!--        class="icon-font icon-renliankakou fs34 vt-middle icon-color fl mr-md"-->
    <!--      ></span>-->
    <!--      <p class="count color2 ellipsis">-->
    <!--        <countTo-->
    <!--          :startVal="0"-->
    <!--          :endVal="queryAccessDataCount.faceViewAmount || 0"-->
    <!--          :duration="3000"-->
    <!--        ></countTo>-->
    <!--      </p>-->
    <!--      <p class="name">人脸抓拍总量</p>-->
    <!--    </div>-->
    <!--    <div class="statistics-wrapper mr-md">-->
    <!--      <top-star class="top-star"></top-star>-->
    <!--      <span-->
    <!--        class="icon-font icon-cheliangkakou fs34 vt-middle icon-color fl mr-md"-->
    <!--      ></span>-->
    <!--      <p class="count color3 ellipsis">-->
    <!--        <countTo-->
    <!--          :startVal="0"-->
    <!--          :endVal="queryAccessDataCount.vehicleViewAmount || 0"-->
    <!--          :duration="3000"-->
    <!--        ></countTo>-->
    <!--      </p>-->
    <!--      <p class="name">车辆抓拍总量</p>-->
    <!--    </div>-->
    <!--    <div class="statistics-wrapper">-->
    <!--      <top-star class="top-star"></top-star>-->
    <!--      <span-->
    <!--        class="icon-font icon-keypersonlibrary fs34 vt-middle icon-color fl mr-md"-->
    <!--      ></span>-->
    <!--      <p class="count color4 ellipsis">-->
    <!--        <countTo-->
    <!--          :startVal="0"-->
    <!--          :endVal="queryAccessDataCount.zdrTrackAmount || 0"-->
    <!--          :duration="3000"-->
    <!--        ></countTo>-->
    <!--      </p>-->
    <!--      <p class="name ellipsis">ZDR人像轨迹总量</p>-->
    <!--    </div>-->
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'statistics-top',
  components: {
    countTo: require('vue-count-to').default,
    TopStar: require('./top-star').default,
  },
  props: {
    queryAccessData: {
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  watch: {},
  filter: {},
  created() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.fs34 {
  font-size: 34px;
}
.color1 {
  color: #25f3f1;
}
.color2 {
  color: #bf89f7;
}
.color3 {
  color: #f5af55;
}
.color4 {
  color: #ea5252;
}
.statistics-container {
  width: 880px;
  position: absolute;
  left: 50%;
  top: 10px;
  display: flex;
  transform: translateX(-50%);
  .statistics-wrapper {
    position: relative;
    overflow: hidden;
    padding: 5px 10px;
    // width: 210px;
    flex: 1;
    height: 60px;
    box-shadow: 0 0 20px #0769ce inset;
    &:not(&:last-child) {
      margin-right: 10px;
    }
    .icon-color {
      background: linear-gradient(0deg, #0647b2, #01b4ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .count {
      font-size: 20px;
    }
    .name {
      color: #e1f2fd;
      font-size: 14px;
    }
    .top-star {
      position: absolute;
      top: 0px;
      left: 0px;
    }
  }
  @keyframes flash {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
}
.full-screen-container {
  top: 9%;
}
</style>
