<template>
  <div class="synthesize-left">
    <div class="evaluate-tendency">
      <div class="statistic-list">
        <ul>
          <li>
            <div class="monitoring-data">
              <i class="icon-font icon-shitujichushuju f-50 pl20 icon-bg1"></i>
              <span>
                <p>视频监控</p>
                <p class="statistic-num">
                  {{ statisticList.videoSurveillanceAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
          <li>
            <div class="face-data">
              <i class="icon-font icon-renliankakou f-50 pl20 icon-bg2"></i>
              <span>
                <p>人脸卡口</p>
                <p class="statistic-num">
                  {{ statisticList.faceSwanAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
          <li>
            <div class="car-data">
              <i class="icon-font icon-cheliangkakou f-50 pl20 icon-bg3"></i>
              <span>
                <p>车辆卡口</p>
                <p class="statistic-num">
                  {{ statisticList.vehicleBayonetAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
          <li>
            <div class="face-captured">
              <i class="icon-font icon-renlianzhuapaishuju f-50 pl20 icon-bg4"></i>
              <span>
                <p>人脸抓拍数据</p>
                <p class="statistic-num">
                  {{ statisticList.faceViewAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
          <li>
            <div class="car-captured">
              <i class="icon-font icon-cheliangzhuapaishuju f-50 pl20 icon-bg5"></i>
              <span>
                <p>车辆抓拍数据</p>
                <p class="statistic-num">
                  {{ statisticList.vehicleViewAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
          <li>
            <div class="track-data">
              <i class="icon-font icon-keypersonlibrary f-50 pl20 icon-bg6"></i>
              <span>
                <p>ZDR人像轨迹数据</p>
                <p class="statistic-num">
                  {{ statisticList.zdrTrackAmount || 0 | separateNum }}
                </p>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="evaluate-reach">
      <div class="reach-box">
        <div class="reach-title">
          <title-content :title="indexTitle + '评价统计'"></title-content>
          <reach-tabs
            class="reach-tabs"
            ref="reach-tabs"
            :tab-list="tabList"
            :tab-num="1"
            @changeTab="changeTab"
          ></reach-tabs>
        </div>
        <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
          <draw-echarts
            v-if="echartData.length != 0"
            :echart-option="echartRing"
            :echart-style="ringStyle"
            ref="evaluateReachChart"
            @echartClick="echartClick"
            class="charts"
            :echarts-loading="echartsLoading"
          ></draw-echarts>
          <span class="next-echart" v-if="echartData.length > comprehensiveConfig.comprehensiveNum">
            <i
              class="icon-font icon-youjiantou1 f-12"
              @click="scrollRight('evaluateReachChart', echartData, [], comprehensiveConfig.comprehensiveNum)"
            ></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.evaluate-tendency {
  height: 130px;
  width: 100%;
  background-color: var(--bg-sub-content);
  margin-bottom: 10px;
  position: relative;
  .reach-title {
    text-align: center;
    padding-top: 20px;
    width: 100%;
  }
  .statistic-list {
    width: 100%;
    // margin-top: 10px;
    height: 130px;
    ul {
      width: 100%;
      height: 130px;
      display: flex;
      justify-content: center;
      align-items: center;
      li {
        flex: 1;
        height: 130px;
        display: flex;
        align-items: center;
        .track-data {
          border-right: none;
        }
        div {
          height: 56px;
          width: 100%;
          border-right: 1px solid #094a8a;
          line-height: 56px;
          display: flex;
          justify-content: center;
          align-items: center;

          i {
            display: inline-block;
            height: 100%;
          }
          .f-50 {
            font-size: 50px;
          }
          .pl20 {
            margin-left: 20px;
          }
          span {
            display: inline-block;
            height: 50px;
            flex: 1;
            margin-left: 20px;
            text-align: left;
            p {
              white-space: nowrap;
              font-style: normal;
              height: 25px;
              line-height: 25px;
              color: #fff;
              font-size: 12px;
            }
            .statistic-num {
              font-size: 16px;
              color: #19d5f6;
              -webkit-text-stroke: 1 rgba(0, 0, 0, 0);
              opacity: 1;
            }
          }
          .icon-bg1 {
            background: linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg2 {
            background: linear-gradient(360deg, #52049f 0%, #9f5ce4 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg3 {
            background: linear-gradient(360deg, #1641ee 0%, #67acfb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg4 {
            background: linear-gradient(180deg, #b58e0f 0%, #bb6603 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg5 {
            background: linear-gradient(180deg, #d811cc 0%, #7710aa 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .icon-bg6 {
            background: linear-gradient(360deg, #780f0f 0%, #ec5353 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
      }
    }
  }
}
.evaluate-reach {
  height: 470px;
  width: 100%;
  background-color: var(--bg-sub-content);
  margin-bottom: 10px;
  position: relative;
  .no-box {
    width: 100%;
    height: 100%;
    position: absolute;
  }
  .reach-box {
    height: 470px;
    width: 100%;
    background-color: var(--bg-sub-content);
    margin-bottom: 10px;
    .reach-title {
      text-align: center;
      position: relative;
      padding-top: 20px;
      width: 100%;
      .reach-tabs {
        position: absolute;
        right: 30px;
        top: 20px;
      }
    }
    .echarts-box {
      height: 420px;
      width: 100%;
      position: relative;
      .charts {
        display: inline-block;
        width: 100%;
      }
    }
  }
  .next-echart {
    width: 24px;
    height: 24px;
    background: #02162b;
    border: 1px solid #10457e;
    opacity: 1;
    border-radius: 4px;
    top: 200px;
    right: 35px;
    position: absolute;
    text-align: center;
    line-height: 24px;

    .icon-youjiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
    &:active {
      width: 24px;
      height: 24px;
      background: #02162b;
      border: 1px solid var(--color-primary);
      opacity: 1;
      border-radius: 4px;
      .icon-youjiantou1 {
        color: #4e9ef2;
        font-size: 12px;
        vertical-align: top !important;
      }
    }
    &:hover {
      width: 24px;
      height: 24px;
      background: #02162b;
      border: 1px solid #146ac7;
      opacity: 1;
      border-radius: 4px;
      .icon-youjiantou1 {
        color: var(--color-primary);
        font-size: 12px;
        vertical-align: top !important;
      }
    }
  }
}
</style>
<script>
import evaluationreport from '@/config/api/evaluationreport';
import dataZoom from '@/mixins/data-zoom';
import { mapGetters } from 'vuex';
export default {
  name: 'synthesizeLeft',
  mixins: [dataZoom],
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '98%',
        height: '410px',
      },
      finishStatus: '',
      echartData: [],
      echartList: [],
      echart1: [],
      indexTitle: '',
      indexList: {},
      indexVal: '',
      echartsLoading: false,
      regionCode: '',
      targetDataTypeDesc: '',
    };
  },

  mounted() {},
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    echartClick(params) {
      this.$emit('echartClick', params);
    },

    async getSyntheticStatistics(val, list) {
      this.echartsLoading = true;
      let data = {
        indexModule: val,
        rootResultIds: list.rootResultIds,
        dataType: list.targetDataType,
        selfRegionCode: list.selfRegionCode,
      };

      try {
        let res = await this.$http.post(evaluationreport.getSyntheticStatistics, data);
        // res.data.data = res.data.data.map((item) => {
        //   if (item.regionName.endsWith('市') || item.regionName.endsWith('区'))
        //     item.regionName = item.regionName.substring(0, item.regionName.length - 1)
        //   // item.regionName = item.regionName.replace(/市|区/g, '')
        //   return item
        // })
        this.echartData = res.data.data;
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          regionName: row.regionName,
          regionCode: row.regionCode,
          value: row.standardsValue,
          standardsIndexAmount: row.standardsIndexAmount,
          unStandardsIndexAmount: row.unStandardsIndexAmount,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.regionName),
        data: this.barData,
        targetDataTypeDesc: this.targetDataTypeDesc,
      };
      this.echartRing = this.$util.doEcharts.evaluationReportColumn(opts);
      setTimeout(() => {
        this.setDataZoom('evaluateReachChart', [], this.comprehensiveConfig.comprehensiveNum);
      });
    },

    async changeTab(val, value, list) {
      this.indexTitle = value;
      this.indexList = list;
      this.indexVal = val;
      this.targetDataTypeDesc = list.targetDataTypeDesc;
      await this.getSyntheticStatistics(val, list);
      this.$emit('getData', val, value, list);
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },

  props: {
    tabList: {
      required: true,
      default() {
        return [];
      },
    },
    statisticList: {},
  },

  components: {
    titleContent: require('@/views/governanceevaluation/evaluationoverview/components/title-content.vue').default,
    reachTabs: require('@/views/governanceevaluation/evaluationoverview/components/reach-tabs.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
