// vue.config.js
const path = require('path');
// const target = 'http://*************'; //南京服务器
// const CopyWebpackPlugin = require('copy-webpack-plugin');
const CompressionPlugin = require('compression-webpack-plugin');
const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;
const isProd = process.env.NODE_ENV === 'production';

const webpack = require('webpack');

const resolve = (dir) => {
  return path.join(__dirname, dir);
};

// const SpeedMeasureWebpackPlugin = require('speed-measure-webpack-plugin');
// const HardSourceWebpackPlugin = require('hard-source-webpack-plugin');

module.exports = {
  productionSourceMap: false, // 关闭生产环境的 source map
  lintOnSave: isProd ? false : 'warning',
  configureWebpack: {
    plugins: [
      new webpack.optimize.LimitChunkCountPlugin({
        maxChunks: 60, // 限制chunk的最大数量
      }),
      new webpack.optimize.MinChunkSizePlugin({
        minChunkSize: 200000, // 合并小于 200kb 的chunk
      }),
      // new SpeedMeasureWebpackPlugin(),
      // new HardSourceWebpackPlugin(),
      //       new CopyWebpackPlugin([{
      //         from: 'node_modules/@liveqing/liveplayer/dist/component/crossdomain.xml'
      //       },
      //       {
      //         from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer.swf'
      //       },
      //       {
      //         from: 'node_modules/@liveqing/liveplayer/dist/component/liveplayer-lib.min.js',
      //         to: 'js/'
      //       },
      //       ])
      /*   new CompressionWebpackPlugin({
            threshold: 10240, // 只有大小大于该值的资源会被处理10240
            test: /\.(js|css)(\?.*)?$/i, // 需要压缩的文件,
            deleteOriginalAssets: false // 删除原文件
          })*/
    ],
  },
  chainWebpack: (config) => {
    config.resolve.alias.set('@', resolve('src'));
    if (process.env.NODE_ENV === 'production') {
      config.plugin('compressionPlugin').use(
        new CompressionPlugin({
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: productionGzipExtensions,
          threshold: 10240,
          minRatio: 0.8,
          deleteOriginalAssets: false,
        }),
      );
    }

    config.optimization.splitChunks({
      chunks: 'all', //块的范围，有三个可选值：initial/async动态异步加载/all全部块(推荐)，默认为async;
      minSize: 200000, // 代码分割的最小值，默认30k；
      maxSize: 500000,
      minChunks: 1, //模块被引用次数多少时才会进行代码分割，默认为1；
      maxAsyncRequests: 30, //最大的按需(异步)加载次数，默认为5
      maxInitialRequests: 30, //最大的初始化加载次数，默认为3；
      // automaticNameDelimiter: '~',
      // automaticNameMaxLength: 30,
      name: true, //拆分出来块的名字(Chunk Names)，默认由块名和hash值自动生成；
      cacheGroups: {
        //缓存组
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10, //优先级
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true, //复用之前的打包模块
        },
        elementUI: {
          name: 'chunk-element',
          priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          reuseExistingChunk: true,
        },
        iview: {
          name: 'chunk-iview',
          test: /[\\/]node_modules[\\/]view-design[\\/]/,
          priority: 30,
          reuseExistingChunk: true,
        },
        // 下述配置可以合并多个chunk css
        // styles: {
        //   name: 'styles',
        //   test: /.(css|less|scss|sass)$/,
        //   chunks: 'all',
        //   // priority: 50,
        //   // reuseExistingChunk: true,
        //   enforce: true,
        // },
      },
    });

  },
  publicPath: '/',
  devServer: {
    port: 8083,
    proxy: {
      // '/ivdg-evaluation-app/': {
      //   target: `http://192.168.2.36:9012`,
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '/ivdg-evaluation-app/': ''
      //   }
      // },
      // '/ivdg-work-order-service': {
      //     target: `http://192.168.2.36:9003`,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '/ivdg-work-order-service': ''
      //     }
      // },
      '/json': {
        target: `http://192.168.1.111`,
        changeOrigin: true,
      },
      '/temp': {
        target: `http://192.168.1.111`,
        changeOrigin: true,
      },
      '/qsdi': {
        target: `http://192.168.1.111:8888`,
        changeOrigin: true,
      },
      '/ivdg': {
        target: `http://192.168.1.111:8888`,
        changeOrigin: true,
      },
    },
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [path.resolve(__dirname, './src/style/variable.less')],
    },
  },
};
