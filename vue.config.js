// vue.config.js
const path = require("path");
const CopyWebpackPlugin = require("copy-webpack-plugin");
// const CompressionPlugin = require('compression-webpack-plugin')
const HardSourceWebpackPlugin = require("hard-source-webpack-plugin");

// 拼接路径
// const resolve = (dir) => require('path').join(__dirname, dir)
// let url = "192.168.1.143"; // ICBD1.2
let url = "*************";
// let url = "192.168.1.146"; // 南京开发
// let url = '*************'; // 蚌埠现场
// 更改代理地址需要同时修改文件  config/api/websocket.js 的地址
module.exports = {
  publicPath: process.env.BASE_URL || "/",
  // webpack-dev-server 相关配置
  pages: {
    page1: {
      entry: "./src/main.js",
      template: "./public/index.html",
      filename: "index.html",
    },
  },
  devServer: {
    proxy: {
      // 设置代理
      "/ivcp": {
        target: `http://${url}:8888/`,
        changeOrigin: true,
      },
      // "/ipbd": {
      //   target: `http://${url}:8888/`,
      //   changeOrigin: true,
      // },
      "/iras": {
        target: `http://${url}:8888/`,
        changeOrigin: true,
      },
      "/qsdi": {
        target: `http://${url}:8888/`,
        changeOrigin: true,
      },
      "/ivdg": {
        target: `http://${url}:8888/`,
        changeOrigin: true,
      },
      "/icbd": {
        target: `http://${url}:8888/`,
        changeOrigin: true,
      },
      "/npgisdataservice": {
        target: `http://*************:7777/`,
        changeOrigin: true,
        pathRewrite: {
          "/npgisdataservice": "/netposa",
        },
      },
      "/v1": {
        target: "https://developer.jointpilot.com",
        changeOrigin: true,
        // pathRewrite: {
        //   '/v1': ''
        // }
      },
      "/qsdi-knowledge-center-service": {
        target: `http://192.168.124:8888/`,
        changeOrigin: true,
      },
      // '/WangJianService': {
      //   target: 'http://*************:10012/',
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '/WangJianService': '',
      //   },
      // },
    },
  },
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  productionSourceMap: false,
  chainWebpack: (config) => {
    const less = config.module.rule("less").toConfig();
    const useable = { ...less.oneOf[3], test: /\-useable\.less$/ };
    useable.use = [...useable.use];
    useable.use[0] = {
      loader: "style-loader",
      options: { injectType: "lazyStyleTag" },
    };
    config.module.rule("less").merge({ oneOf: [useable] });

    if (process.env.NODE_ENV === "production") {
      // config.output
      //   .filename("[name].[contenthash].bundle.js")
      //   .chunkFilename("[name].[contenthash].chunk.js");

      // 为生产环境修改配置...
      // config.plugin("hardSource").use(HardSourceWebpackPlugin, [
      //   {
      //     // 配置缓存路径
      //     cacheDirectory: "../node_modules/.cache/hard-source/[hash]",
      //     // 只缓存加载时间
      //     recordsPath: "../node_modules/.cache/hard-source/records.json",
      //   },
      // ]);

      config.plugin("copy").tap((args) => {
        if (!process.env.npm_config_tools) args[0][0].ignore.push("**/*.exe");
        return args;
      });
    } else {
      config.watchOptions({ ignored: [/node_modules/] });
      // config.module.noParse(/node_modules\//);
    }
  },
  pluginOptions: {
    "style-resources-loader": {
      preProcessor: "less",
      patterns: [
        // path.resolve(__dirname, './src/style/index.less')
      ],
    },
  },
};
