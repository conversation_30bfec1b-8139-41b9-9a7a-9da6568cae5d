// 【批量复检】 按钮的逻辑
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  data() {
    return {
      isRecheck: false, // 复检中...
    };
  },
  created() {
    this.getBatchRecheckState();
  },
  methods: {
    async getBatchRecheckState() {
      try {
        let data = await this.$http.get(evaluationoverview.getReinspectStatusByBatchId, {
          params: { batchId: this.$route.query.batchId },
        });
        this.isRecheck = data.data?.data === '1' ? true : false;
      } catch (error) {
        // console.log(error);
      }
    },
  },
};
