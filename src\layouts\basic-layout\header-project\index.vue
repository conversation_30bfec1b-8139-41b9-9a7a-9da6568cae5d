<template>
  <span class="i-layout-header-trigger i-layout-header-trigger-min">
    <Dropdown class="i-layout-header-user" @on-click="handleClick" transfer>
      <ui-icon
        @click.native="changeTheme"
        type="qiehuanzhizidingyicaidan"
        :size="20"
        :color="'#fff'"
      ></ui-icon>
      <DropdownMenu
        v-for="item in otherSysApplicationVoList"
        slot="list"
        :key="item.id"
      >
        <i-link
          :to="item.address + '?refresh_token=' + jumpToken"
          target="_blank"
        >
          <DropdownItem>
            <span>{{ item.applicationName }}</span>
          </DropdownItem>
        </i-link>
      </DropdownMenu>
    </Dropdown>
  </span>
</template>
<script>
import { mapGetters } from "vuex";
import { getToken } from "@/libs/configuration/util.common";
import Setting from "@/libs/configuration/setting";
import { getAuthorNoticeDay } from "@/api/user";
export default {
  name: `iHeaderUser`,
  components: {
    AuthorPoptip: require("./author-poptip").default,
  },
  data() {
    return {
      effectiveDays: 0,
      noticeDay: 0,
      avatarImgError: false,
    };
  },
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
    otherSysApplicationVoList() {
      return this.userInfo.sysApplicationVoList.filter(
        (item) => item.applicationCode !== applicationCode && !!item.address
      );
    },
    jumpToken() {
      return getToken();
    },
  },
  mounted() {
    getAuthorNoticeDay()
      .then((res) => {
        if (res.data) {
          this.noticeDay = res.data.paramValue;
        }
      })
      .catch(() => {})
      .finally(() => {});
    this.effectiveDays = this.$store.getters["common/getAuthorInfo"]
      ? this.$store.getters["common/getAuthorInfo"].effectiveDays
      : 0;
  },
  methods: {
    handleClick(name) {
      if (name === "logout") {
        this.logout({
          confirm: this.logoutConfirm,
          vm: this,
        });
      }
    },
    link(path) {
      this.$router.push({
        path: path,
      });
    },
    avatarImgErrorHandler() {
      this.avatarImgError = true;
    },
  },
};
// 就是这么feel 倍爽
</script>
<style lang="less" scoped>
.i-layout-header-trigger {
  display: flex;
  margin-right: 20px;
}
</style>
