<template>
  <div>
    <ui-modal :title="title" v-model="visible" :width="640">
      <div class="basedField-addOrEdit">
        <Form ref="modalData" :model="modalData" :rules="modalDataValidate" :label-width="120">
          <FormItem label="字典项名称" prop="dictName" class="left-item">
            <Input
              type="text"
              v-model="modalData.dictName"
              placeholder="请输入字典项名称"
              class="width-input"
              :maxlength="50"
            ></Input>
          </FormItem>
          <FormItem label="字典项代码" prop="dictCode" class="left-item">
            <Input
              type="text"
              v-model="modalData.dictCode"
              placeholder="请输入字典项代码"
              class="width-input"
              :maxlength="50"
            ></Input>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Row>
          <Col :span="24" align="center">
            <Button type="primary" class="plr-30" @click="save">保 存</Button>
          </Col>
        </Row>
      </template>
    </ui-modal>
  </div>
</template>

<script>
import metadatamanagement from '@/config/api/metadatamanagement';
export default {
  data() {
    return {
      modalData: {},
      visible: false,
      title: '新增数据',
      accessTypeList: [],
      modalDataValidate: {
        dictName: [{ required: true, message: '请输入字典项名称', trigger: 'blur' }],
        dictCode: [{ required: true, message: '请输入字典项代码', trigger: 'blur' }],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    open(row, title) {
      this.title = title;
      this.visible = true;
      this.modalData = row;
    },
    async save() {
      if (this.validate('modalData') === 'error') {
        return false;
      }
      if (this.title === '新增数据') {
        try {
          let res = await this.$http.post(metadatamanagement.detailAdd, this.modalData);
          if (res.data.code === 200) {
            this.visible = false;
            this.$emit('search');
          }
        } catch (err) {
          console.log(err);
        }
      } else {
        try {
          let res = await this.$http.put(metadatamanagement.detailUpload, this.modalData);
          if (res.data.code === 200) {
            this.visible = false;
            this.$emit('search');
          }
        } catch (err) {
          console.log(err);
        }
      }
    },
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          message = 'error';
        }
      });
      return message;
    },
  },
  computed: {},
  props: {},
  components: {},
};
</script>
<style lang="less" scoped>
.basedField-addOrEdit {
  padding-left: 30px;
  .width-input {
    width: 380px;
  }
}
</style>
