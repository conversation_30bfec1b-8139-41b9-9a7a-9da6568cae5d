<!--
    * @FileDescription: 人员报警，车辆报警
    * @Author: H
    * @Date: 2023/05/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-20 14:05:54
 -->
<template>
  <div class="alarm">
    <p class="alarm-title" v-if="showCount">
      {{ carType == 1 ? "人员" : "车辆" }}报警: <span>{{ total }}</span>
    </p>
    <div v-if="tableList.length > 0" class="my-swiper-container" id="mySwiper">
      <swiper
        ref="mySwiper"
        :options="swiperOption[carType - 1]"
        class="my-swiper"
      >
        <template v-for="(item, index) in tableList">
          <swiper-slide
            :key="index"
            @click.native="handleDetailFn(item, index)"
          >
            <div class="swiper-item">
              <personnel-alarm
                @collection="collection"
                v-if="carType == 1"
                :data="item"
              ></personnel-alarm>
              <vehicle-alarm
                v-else
                @collection="collection"
                :data="item"
              ></vehicle-alarm>
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div v-if="carType == 1">
        <div class="swiper-button-prev snap-prev" slot="button-prev">
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div class="swiper-button-next snap-next" slot="button-next">
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
      <div v-else>
        <div class="swiper-button-prev snap-prev-two" slot="button-prev">
          <i class="iconfont icon-caret-right"></i>
        </div>
        <div class="swiper-button-next snap-next-two" slot="button-next">
          <i class="iconfont icon-caret-right"></i>
        </div>
      </div>
    </div>
    <ui-empty v-else></ui-empty>
    <ui-loading v-if="loading" />
    <!-- 人员布控 -->
    <peopleAlarmDetail
      v-if="detailShow"
      :tableList="tableList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="alarmDetail"
      @close="detailShow = false"
    />
    <!-- 车辆布控 -->
    <vehicleAlarmDetail
      v-if="vehicleAlarmShow"
      :tableList="tableList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="vehicleDetail"
      @close="vehicleAlarmShow = false"
    />
  </div>
</template>

<script>
import { queryParamDataByKeys } from "@/api/config";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import personnelAlarm from "./collect/personnelAlarm.vue";
import vehicleAlarm from "./collect/vehicleAlarm.vue";
import { alarmPageList, vehiclePageList } from "@/api/target-control";
import peopleAlarmDetail from "@/views/target-control/alarm-manager/people/components/alarm-detail.vue";
import vehicleAlarmDetail from "@/views/target-control/alarm-manager/vehicle/components/vehicle-detail.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
    personnelAlarm,
    vehicleAlarm,
    peopleAlarmDetail,
    vehicleAlarmDetail,
  },
  props: {
    idCardNo: {
      type: String,
      default: null,
    },
    vid: {
      type: String,
      default: null,
    },
    plateNo: {
      type: String,
      default: null,
    },
    carType: {
      type: Number,
      default: 1,
    },
    showCount: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      swiperOption: [
        {
          effect: "coverflow",
          slidesPerView: 1.235,
          centeredSlides: true,
          initialSlide: 2,
          speed: 1000,
          coverflowEffect: {
            rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
            stretch:
              (((420 / 1920) * window.innerWidth) / 1.235) *
              0.806 *
              (window.innerWidth / 1920), // 每个slide之间的拉伸值，越大slide靠得越紧。
            depth: 150, // slide的位置深度。值越大z轴距离越远，看起来越小。
            modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
            slideShadows: true, // 开启slide阴影。默认 true。
          },
          navigation: {
            nextEl: ".snap-next",
            prevEl: ".snap-prev",
          },
          observer: true,
          observeParents: true,
        },
        {
          effect: "coverflow",
          slidesPerView: 1.235,
          centeredSlides: true,
          initialSlide: 2,
          speed: 1000,
          coverflowEffect: {
            rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
            stretch:
              (((420 / 1920) * window.innerWidth) / 1.235) *
              0.806 *
              (window.innerWidth / 1920), // 每个slide之间的拉伸值，越大slide靠得越紧。
            depth: 150, // slide的位置深度。值越大z轴距离越远，看起来越小。
            modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
            slideShadows: true, // 开启slide阴影。默认 true。
          },
          navigation: {
            nextEl: ".snap-next-two",
            prevEl: ".snap-prev-two",
          },
          observer: true,
          observeParents: true,
        },
      ],

      alarmConfigInfo: {},
      tableList: [],
      total: 0,
      detailShow: false,
      vehicleAlarmShow: false,
      alarmInfo: {},
      tableIndex: 0,
    };
  },
  watch: {
    // carType:{
    //     handler(val) {
    //         if(val == 1) {
    //             this.personnelList();
    //         }else {
    //             this.vehicleList();
    //         }
    //     },
    //     immediate: true
    // }
  },
  computed: {},
  async created() {
    await this.init();
    this.handleTabList1();
  },
  mounted() {
    window.addEventListener("resize", () => {
      if (this.$refs.mySwiper && this.$refs.mySwiper.swiper) {
        // this.$refs.mySwiper.swiper.params.coverflowEffect.stretch = window.innerWidth*window.innerWidth*0.0000743
        this.$refs.mySwiper.swiper.params.coverflowEffect.stretch =
          (((420 / 1920) * window.innerWidth) / 1.235) *
          0.806 *
          (window.innerWidth / 1920);
        this.$refs.mySwiper.swiper.update();
      }
    });
  },
  methods: {
    async init() {
      var param = ["ICBD_TARGET_CONTROL"];
      return queryParamDataByKeys(param).then((res) => {
        if (res.data.length > 0) {
          this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
        }
      });
    },
    // 详情
    handleDetailFn(item, index) {
      this.tableIndex = index;
      if (this.carType == 1) {
        this.detailShow = true;
      } else {
        this.vehicleAlarmShow = true;
      }
      this.alarmInfo = item;
    },
    handleTabList1() {
      this.loading = true;
      let type = this.carType;
      if (type == 1) {
        this.personnelList();
      } else {
        this.vehicleList();
      }
    },
    handleTabList(type = 1) {
      this.loading = true;
      if (type == 1) {
        this.personnelList();
      } else {
        this.vehicleList();
      }
    },
    // 人员报警
    personnelList() {
      this.tableList = [];
      let params = {
        pageNumber: 1,
        pageSize: 3,
        simScore: 0.8,
        idCardNo: this.idCardNo,
        vid: this.vid,
        operationType: null,
      };
      alarmPageList(params)
        .then((res) => {
          this.tableList = res.data.entities || [];
          this.total = res.data.total;
          this.tableList.forEach((item) => {
            var info = this.alarmConfigInfo.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    vehicleList() {
      this.tableList = [];
      let params = {
        pageNumber: 1,
        pageSize: 3,
        plateNo: this.plateNo,
        simScore: 0.8,
        operationType: null,
      };
      vehiclePageList(params)
        .then((res) => {
          this.tableList = res.data.entities || [];
          this.total = res.data.total;
          this.tableList.forEach((item) => {
            var info = this.alarmConfigInfo.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
          this.$forceUpdate();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    collection(params, flag) {
      if (flag == 1) {
        addCollection(params).then((res) => {
          this.$Message.success("收藏成功");
          if (this.carType == 1) {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      } else {
        deleteMyFavorite(params).then((res) => {
          this.$Message.success("取消收藏成功");
          if (this.carType == 1) {
            this.personnelList();
          } else {
            this.vehicleList();
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.alarm-title {
  font-size: 14px;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.75);
  margin: 10px 0 25px 30px;
  span {
    color: #2c86f8;
  }
}
.my-swiper-container {
  padding: 0 40px;
  position: relative;
  .my-swiper {
    margin: auto;
    .swiper-item {
      width: 100%;
      height: 200px;
    }
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;
  .iconfont {
    color: #fff;
    font-size: 18px;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }
  &:active {
    background: rgba(0, 0, 0, 1);
  }
}
.swiper-button-prev {
  transform: rotate(180deg);
  left: 20px;
}
.swiper-button-next {
  right: 20px;
}
</style>
