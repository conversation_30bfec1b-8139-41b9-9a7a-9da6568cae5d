<!--
    * @FileDescription: 人体搜索条件
    * @Author: H
    * @Date: 2024/2/26
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-11-20 16:05:45
 -->
<template>
  <div class="search card-border-color">
    <Form
      :inline="true"
      :class="visible ? 'advanced-search-show' : ''"
      @submit.native.prevent
    >
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              :algorithmType="3"
              v-model="queryParam.urlList"
              @imgUrlChange="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract(0)"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(1)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
              <FormItem label="任务类型:">
                <Select
                  v-model="queryParam.selectType"
                  @on-change="handleSeleChange"
                  clearable
                >
                  <Option :value="1" placeholder="请选择">实时结构化</Option>
                  <Option :value="2" placeholder="请选择">历史结构化</Option>
                  <Option :value="3" placeholder="请选择">文件结构化</Option>
                </Select>
              </FormItem>
              <FormItem label="任务名称:" v-if="queryParam.selectType">
                <div class="select-tag-button" @click="selectTask()">
                  选择任务（{{ queryParam.selectTaskList.length }}）
                </div>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem
                  label="设备资源:"
                  v-if="
                    queryParam.selectType == 1 || queryParam.selectType == 2
                  "
                >
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="文件选择:" v-if="queryParam.selectType == 3">
                  <div class="select-tag-button" @click="selectFile()">
                    选择文件/已选（{{ queryParam.selectFileList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <hl-timerange
                    ref="timerange"
                    @onceChange="handleTimeOnce"
                    @change="handleTimeChange"
                    :reflectValue="queryParam.timeSlot"
                    :reflectTime="{
                      startDate: queryParam.startDate,
                      endDate: queryParam.endDate,
                    }"
                  >
                  </hl-timerange>
                </FormItem>
              </div>
              <div class="btn-group">
                <span
                  class="advanced-search-text primary"
                  @click="advancedSearchHandle($event)"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span>
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div class="advanced-search">
        <section class="search-container">
          <div class="search-item-container" id="searchItemContainer">
            <div class="classify-content">
              <span class="classify-name">通用</span>
              <div class="items">
                <div class="advanced-search-item card-border-color">
                  <FormItem label="数据来源:">
                    <dataSource :currentIndex="2" />
                  </FormItem>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">服饰</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="上身纹理:">
                        <selectTag
                          ref="upperTexture"
                          :list="upperBodyTextureList"
                          vModel="upperTexture"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="上身袖子类型:">
                        <selectTag
                          ref="sleeveStyle"
                          :list="upperSleeveTypeList"
                          vModel="sleeveStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
                <div class="advanced-search-list card-border-color">
                  <FormItem label="上身颜色:">
                    <ui-tag-select
                      ref="upperColor"
                      @input="
                        (e) => {
                          handleInput(e, 'upperColor');
                        }
                      "
                    >
                      <ui-tag-select-option
                        v-for="(item, $index) in recognitionColorList"
                        :key="$index"
                        effect="dark"
                        :name="item.dataKey"
                      >
                        <div
                          v-if="staticBodyColorList[item.dataKey]"
                          :style="{
                            borderColor:
                              staticBodyColorList[item.dataKey].borderColor,
                          }"
                          class="plain-tag-color"
                        >
                          <div
                            :style="staticBodyColorList[item.dataKey].style"
                          ></div>
                        </div>
                      </ui-tag-select-option>
                    </ui-tag-select>
                  </FormItem>
                </div>
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="下身类型:">
                        <selectTag
                          ref="lowerStyle"
                          :list="lowerBodyType"
                          vModel="lowerStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="下身颜色:">
                        <ui-tag-select
                          ref="lowerColor"
                          @input="
                            (e) => {
                              handleInput(e, 'lowerColor');
                            }
                          "
                        >
                          <ui-tag-select-option
                            v-for="(item, $index) in recognitionColorList"
                            :key="$index"
                            effect="dark"
                            :name="item.dataKey"
                          >
                            <div
                              v-if="staticBodyColorList[item.dataKey]"
                              :style="{
                                borderColor:
                                  staticBodyColorList[item.dataKey].borderColor,
                              }"
                              class="plain-tag-color"
                            >
                              <div
                                :style="staticBodyColorList[item.dataKey].style"
                              ></div>
                            </div>
                          </ui-tag-select-option>
                        </ui-tag-select>
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">鞋子</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="鞋子类别:">
                        <selectTag
                          ref="shoesStyle"
                          :list="shoeCategory"
                          vModel="shoesStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="鞋子颜色:">
                        <ui-tag-select
                          ref="shoesColor"
                          @input="
                            (e) => {
                              handleInput(e, 'shoesColor');
                            }
                          "
                        >
                          <ui-tag-select-option
                            v-for="(item, $index) in recognitionColorList"
                            :key="$index"
                            effect="dark"
                            :name="item.dataKey"
                          >
                            <div
                              v-if="staticBodyColorList[item.dataKey]"
                              :style="{
                                borderColor:
                                  staticBodyColorList[item.dataKey].borderColor,
                              }"
                              class="plain-tag-color"
                            >
                              <div
                                :style="staticBodyColorList[item.dataKey].style"
                              ></div>
                            </div>
                          </ui-tag-select-option>
                        </ui-tag-select>
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
            <div class="classify-content">
              <span class="classify-name">其他</span>
              <div class="items">
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="性别:">
                        <selectTag
                          ref="gender"
                          :list="genderList"
                          vModel="gender"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="发型:">
                        <selectTag
                          ref="hairStyle"
                          :list="hairStyleList"
                          vModel="hairStyle"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
                <div class="advanced-search-list card-border-color">
                  <Row>
                    <Col span="11">
                      <FormItem label="行为:">
                        <selectTag
                          ref="behavior"
                          :list="behaviorList"
                          vModel="behavior"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                    <Col span="13">
                      <FormItem label="附属物:">
                        <selectTag
                          ref="appendix"
                          :list="appendantList"
                          vModel="appendix"
                          @selectItem="selectItem"
                        />
                      </FormItem>
                    </Col>
                  </Row>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        :checkedLabels="checkedLabels"
        @selectData="selectData"
      />
      <select-file ref="selectFile" @selectData="selectFileData"></select-file>
      <select-task
        ref="selectTask"
        :type="queryParam.selectType"
        @selectData="selectTaskData"
      ></select-task>
    </Form>
  </div>
</template>
<script>
import { queryTaskSelect } from "@/api/viewAnalysis";
import {
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { mapGetters, mapMutations } from "vuex";
import uiUploadImg from "@/components/ui-upload-new-img/index";
import { staticBodyColorList } from "@/libs/system";
import selectTag from "@/views/wisdom-cloud-search/search-center/components/select-tag.vue";
import dataSource from "@/views/wisdom-cloud-search/search-center/components/data-source.vue";
export default {
  props: {
    queryParam: {
      type: Object,
      default: () => {
        return {};
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    uiUploadImg,
    selectTag,
    dataSource,
  },
  data() {
    return {
      staticBodyColorList,
      checkedLabels: [], // 已选择的标签
    };
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      parsingLibrarySearchData: "common/getParsingLibrarySearchData", //查询数据
    }),
  },
  async mounted() {
    let { type, taskId, jobId, imgUrl, startDate, endDate } = this.$route.query;
    if (type) this.queryParam.selectType = Number(type);
    if (taskId) this.queryParam.selectTaskList = JSON.parse(taskId);
    if (type && jobId) {
      let res = await queryTaskSelect({ type, parentId: jobId });
      this.queryParam.selectTaskList = res.data || [];
    }
    // if (imgUrl) {
    //   let fileData = new FormData();
    //   fileData.append("algorithmType", 3);
    //   fileData.append("fileUrl", imgUrl);
    //   let result = await picturePick(fileData);
    //   if (result.data.length == 0) {
    //     this.$Message.error("未识别出人体!");
    //     return;
    //   }
    //   let params = result.data[0];
    //   let urlObj = {
    //     fileUrl: "data:image/jpeg;base64," + params.imageBase,
    //     feature: params.feature,
    //     imageBase: params.imageBase,
    //   };
    //   this.urlImgList([urlObj, ""], 1);
    // }
    if (imgUrl) {
      let fileData = new FormData();
      fileData.append("algorithmType", 3);
      fileData.append("fileUrl", imgUrl);
      await picturePick(fileData).then(async (res) => {
        if (res.data && res.data.length > 0) {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };
          this.urlImgList([urlList, ""], 1);
        } else {
          this.$Message.warning("未提取到特征，暂无查询结果");
        }
      });
    }
    if (startDate && endDate) {
      this.queryParam.timeSlot = "自定义";
      this.queryParam.startDate = this.$dayjs(Number(startDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.queryParam.endDate = this.$dayjs(Number(endDate)).format(
        "YYYY-MM-DD HH:mm:ss"
      );
    }
    if (this.globalObj.searchForPicturesDefaultSimilarity)
      this.queryParam.similarity =
        this.globalObj.searchForPicturesDefaultSimilarity - 0;
    let {
      selectType,
      selectDeviceList,
      selectTaskList,
      selectFileList,
      timeSlot,
      searchSelect,
    } = this.parsingLibrarySearchData[2];
    if (searchSelect == 1) {
      this.queryParam.selectType = selectType;
      this.queryParam.selectDeviceList = selectDeviceList;
      this.queryParam.selectTaskList = selectTaskList;
      this.queryParam.selectFileList = selectFileList;
      this.queryParam.timeSlot = timeSlot;
      this.queryParam.startDate = this.parsingLibrarySearchData[2].startDate;
      this.queryParam.endDate = this.parsingLibrarySearchData[2].endDate;
    }
    this.$emit("search");
    this.setParsingLibrarySearchData({...this.queryParam, menuIndex:2});
  },
  methods: {
    ...mapMutations("common", ["setParsingLibrarySearchData"]),
    searchHandle() {
      this.$emit("search");
      this.setParsingLibrarySearchData({...this.queryParam, menuIndex:2});
    },
    resetHandle() {
      this.resetClear();
      this.$emit("reset");
      this.setParsingLibrarySearchData({...this.queryParam, menuIndex:2});
      this.visible = false;
    },
    resetClear() {
      // 清空组件选中状态
      this.$refs.timerange.clearChecked(false);
      let searchList = {
        upperTexture: "",
        sleeveStyle: "",
        upperColor: "",
        lowerStyle: "",
        lowerColor: "",
        shoesStyle: "",
        shoesColor: "",
        gender: "",
        hairStyle: "",
        behavior: "",
        appendix: "",
      };
      for (let key in searchList) {
        this.$refs[key].clearChecked();
        this.queryParam[key] = "";
      }
      this.queryParam.similarity =
        this.globalObj.searchForPicturesDefaultSimilarity - 0;
      this.queryParam.urlList = [];
      this.queryParam.features = [];
      this.queryParam.imageBases = [];
      this.queryParam.selectDeviceList = [];
      this.queryParam.selectTaskList = [];
      this.queryParam.selectFileList = [];
      this.queryParam.selectType = "";
      this.$forceUpdate();
    },
    advancedSearchHandle($event) {
      $event.stopPropagation();
      if (this.visible) {
        this.$emit("update:visible", false);
        // this.visible = false
      } else {
        this.$emit("update:visible", true);
        // this.visible = true
      }
    },
    handleSeleChange() {
      this.queryParam.selectTaskList = [];
    },
    /**
     * 选择任务
     */
    selectTask() {
      this.$refs.selectTask.show(this.queryParam.selectTaskList);
    },
    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.selectFile.show(this.queryParam.selectFileList);
    },
    /**
     * 选择设备
     */
    selectDevice() {
      let keyWords = this.$route.query.keyWords
        ? JSON.parse(this.$route.query.keyWords)
        : "";
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList, keyWords);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      this.$forceUpdate();
    },
    selectFileData(list) {
      this.queryParam.selectFileList = list;
    },
    selectTaskData(list) {
      this.queryParam.selectTaskList = list;
    },
    // 时间
    handleTimeOnce(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    handleTimeChange(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    urlImgList(list, index = 0) {
      if (index == 1) {
        this.queryParam.urlList = [...list];
        this.imgUrlChange([...list]);
      } else if (index == 2) {
        let arr = [...this.queryParam.urlList, ...list].filter((item) => {
          return item;
        });
        this.queryParam.urlList = [...arr, ""];
        this.imgUrlChange([...arr]);
      } else {
        this.queryParam.urlList.unshift(...list);
        this.imgUrlChange([...list]);
      }
    },
    /**
     * 图片上传结果返回
     */
    imgUrlChange(list) {
      // 以图搜图字段
      let features = [];
      let imageBases = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
          imageBases.push(item.imageBase);
        }
      });
      this.queryParam.features = features;
      this.queryParam.imageBases = imageBases;
    },
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },
    /**
     * 选中tag赋值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.dataKey;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
@import "../../../wisdom-cloud-search/search-center/advanced-search/components/search-vehicle";
.advanced-search-list {
  width: 100%;
  padding: 10px 0;
  border-bottom: 1px dashed #fff;
  &:last-child {
    border-bottom: 0;
  }
}
</style>
