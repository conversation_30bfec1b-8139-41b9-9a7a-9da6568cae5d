<!--
 * @FileDescription: 电围碰撞设备采集信息结果
 * @Author: H
 * @Date: 2023/5/16
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-20 11:45:24
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>采集列表</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="table-content">
            <ui-table :columns="columns" :data="captureList"> </ui-table>
          </div>
          <ui-empty
            v-if="captureList.length === 0 && loading == false"
          ></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
        </div>
      </section>
      <footer></footer>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  // mixins: [commonMixins, cutMixins], //全局的mixin
  components: {},
  props: {
    captureList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      columns: [
        {
          title: "序号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "开始时间", key: "absTime" },
        { title: "IMSI编码", key: "imsi" },
        { title: "国际移动台设备识别码", key: "deviceId" },
      ],
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    pageChange() {},
    pageSizeChange() {},
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/modal.less";
.dom-content {
  display: flex;
  flex-direction: column;
  .info-box {
    flex: 1;
    overflow-y: auto;
    flex-wrap: wrap;
  }
  .box-1 {
    width: 19%;
  }
}
</style>
