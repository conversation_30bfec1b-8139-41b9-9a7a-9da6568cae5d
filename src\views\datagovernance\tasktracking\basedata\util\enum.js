import global from '@/util/global';
export const aggregateEnums = {
  dataAccess: '数据输入',
  //stardandChange: '标准转换',
  filedMapping: '字段映射',
  dicMapping: '字典映射',
  formalStore: '正式入库',
  temporateStore: '临时入库',
  nullTest: '空值检测',
  repeatCheck: '重复检测',
  IpTest: 'IP地址格式检测',
  MacTest: 'MAC地址格式检测',
  orgTest: '行政区划格式检测',
  deviceIdTest: '设备编码格式检测',
  spaceTest: '空间信息检测',
  timerTest: '时钟信息检测',
  dataExport: '数据输出',
};

export const aggregateCodeEnums = {
  '数据输入': '1001',
  '字段映射': '2001',
  '字典映射': '2002',
  '正式入库': '3001',
  '空值检测': '4001',
  '重复检测': '4002',
  'IP地址格式检测': '7001',
  'MAC地址格式检测': '7002',
  '行政区划格式检测': '7003',
  '设备编码格式检测': '7004',
  '空间信息检测': '7005',
  '时钟信息检测': '7006',
  '数据输出': '1002',
};
export const aggregateOptions = [
  {
    addVisible: false,
    top: '1.2rem',
    left: '1.25%',
    datas: [
      {
        name: 'dataaccessPopup',
        icon: 'icon-zu16191',
        title: '数据输入',
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
        list: [
          {
            title: '接入总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '今日增量',
            num: 0,
            color: '#F18A37',
            fileName: 'todayAccessDataCount',
          },
        ],
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1.42rem',
      left: '13.6%',
    },
  },
  {
    addVisible: false,
    top: '0.6rem',
    left: '15%',
    datas: [
      {
        name: 'baseringPopup',
        icon: 'icon-ziduanyingshe',
        title: '字段映射',
        subTitle: '',
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
        list: [
          {
            title: '映射总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '映射成功',
            num: 0,
            color: '#F18A37',
            fileName: 'successData',
          },
          {
            title: '转化率',
            num: '0%',
            color: '#13B13D',
            fileName: 'successDataRate',
          },
        ],
      },
      {
        name: 'baseringPopup',
        icon: 'icon-zidianyingshe',
        title: '字典映射',
        subTitle: '',
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
        list: [
          {
            title: '映射总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '映射成功',
            num: 0,
            color: '#F18A37',
            fileName: 'successData',
          },
          {
            title: '转化率',
            num: '0%',
            color: '#13B13D ',
            fileName: 'successDataRate',
          },
        ],
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1rem',
      left: '28.5%',
    },
  },
  {
    addVisible: false,
    top: '0.6rem',
    left: '29.8%',
    datas: [
      {
        name: 'baseringPopup',
        icon: 'icon-linshiruku',
        title: '正式入库',
        subTitle: '入库失败',
        list: [
          {
            title: '入库总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '入库数据',
            num: 0,
            color: '#F18A37',
            fileName: 'successData',
          },
          {
            title: '入库率',
            num: '0%',
            color: '#13B13D',
            fileName: 'successDataRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1.9rem',
      left: '42.1%',
    },
  },
  {
    addVisible: false,
    top: '1.62rem',
    left: '29.8%',
    datas: [
      {
        name: 'baseringPopup',
        icon: 'icon-linshiruku',
        title: '数据范围选择',
        list: [
          {
            title: '数据总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '治理数据',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '治理占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: false,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
    connectingOptions: {
      width: '2%',
      height: '0.04rem',
      top: '1.48rem',
      left: '35%',
      angle: 90,
    },
  },
  {
    addVisible: false,
    top: '0.6rem',
    left: '43.6%',
    datas: [
      {
        name: 'basecolunmringPopup',
        icon: 'icon-kongzhijiance',
        title: '空值检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
      {
        name: 'basecolunmringPopup',
        icon: 'icon-zhongfujiance',
        title: '重复检测',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1.4rem',
      left: '57.2%',
    },
  },
  {
    addVisible: false,
    top: '0.1rem',
    left: '58.7%',
    datas: [
      {
        name: 'baseringPopup',
        icon: 'icon-IPdizhigeshijiance',
        title: 'IP地址格式检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
      {
        name: 'baseringPopup',
        icon: 'icon-MACdizhigeshijiance',
        title: 'MAC地址格式检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
      {
        name: 'baseringPopup',
        icon: 'icon-hangzhengquhuageshijiance',
        title: '行政区划格式检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
      {
        name: 'baseringPopup',
        icon: 'icon-shebeibianmageshijiance',
        title: '设备编码格式检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
      {
        name: 'basecolunmringPopup',
        icon: 'icon-kongjianxinxijiance',
        title: '空间信息检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1.4rem',
      left: '72.4%',
    },
  },
  {
    addVisible: false,
    top: '0.93rem',
    left: '73.9%',
    datas: [
      {
        name: 'baseringPopup',
        icon: 'icon-shizhongxinxijianceshezhi',
        title: '时钟信息检测',
        subTitle: '异常数据',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
          {
            title: '治理优化',
            num: 0,
            color: '#F18A37',
            fileName: '',
          },
          {
            title: '优化占比',
            num: '0%',
            color: '#13B13D',
            fileName: '',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
    connectingOptions: {
      width: '1.2%',
      height: '0.04rem',
      top: '1.4rem',
      left: '86.3%',
    },
  },
  {
    addVisible: false,
    top: '0.93rem',
    left: '87.5%',
    datas: [
      {
        name: 'exportDataPopup',
        icon: 'icon-zu1665',
        title: '数据输出',
        list: [
          {
            title: '检测总量',
            num: 0,
            color: '#05FEF5',
            fileName: 'accessDataCount',
          },
          {
            title: '异常数量',
            num: 0,
            color: '#F18A37',
            fileName: 'existingExceptionCount',
          },
          {
            title: '异常占比',
            num: '0%',
            color: '#13B13D',
            fileName: 'existingExceptionRate',
          },
          {
            title: '治理优化',
            num: 0,
            color: '#F18A37',
            fileName: '',
          },
          {
            title: '优化占比',
            num: '0%',
            color: '#13B13D',
            fileName: '',
          },
        ],
        left: '10px',
        iconSetting: true,
        iconView: 'icon-chakanjiancejieguo',
      },
    ],
  },
];

export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    fixed: 'left',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    width: 200,
    fixed: 'left',
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '组织机构',
    key: 'orgName',
    width: 180,
  },
  {
    title: `${global.filedEnum.longitude}`,
    key: 'longitude',
    width: 150,
  },
  {
    title: `${global.filedEnum.latitude}`,
    key: 'latitude',
    width: 150,
  },
  {
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    width: 180,
  },
  {
    title: 'IPV4地址',
    key: 'ipAddr',
    width: 180,
  },
  {
    title: '安装地址',
    key: 'address',
    width: 200,
  },
  {
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlx',
    width: 130,
  },
  {
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlx',
    width: 150,
  },
  {
    title: '摄像机位置类型',
    key: 'postionType',
    width: 150,
  },
  {
    title: `${global.filedEnum.phyStatus}`,
    key: 'sblwzt',
    width: 180,
  },
];
export const areaTableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    fixed: 'left',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    width: 200,
    fixed: 'left',
  },
  {
    title: '设备名称',
    key: 'deviceName',
    width: 200,
    fixed: 'left',
  },
  {
    title: '组织机构',
    key: 'orgName',
    width: 180,
  },
  {
    title: '行政区划',
    key: 'civilName',
    width: 180,
  },
  {
    title: '经度',
    key: 'longitude',
    width: 150,
  },
  {
    title: '纬度',
    key: 'latitude',
    width: 150,
  },
  {
    title: 'MAC地址',
    key: 'macAddr',
    width: 180,
  },
  {
    title: 'IPV4地址',
    key: 'ipAddr',
    width: 180,
  },
  {
    title: '安装地址',
    key: 'address',
    width: 200,
  },
  {
    title: '监控点位类型',
    key: 'sbdwlx',
    width: 130,
  },
  {
    title: '摄像机功能类型',
    key: 'sbgnlx',
    width: 150,
  },
  {
    title: '摄像机位置类型',
    key: 'postionType',
    width: 150,
  },
  {
    title: '设备状态',
    key: 'sblwzt',
    width: 180,
  },
];
export const actionTableColumns = [
  ...tableColumns,
  {
    title: '操作',
    fixed: 'right',
    slot: 'action',
  },
];

export const checkTableColumns = [
  ...tableColumns,
  {
    title: '数据来源',
    key: 'sourceId',
    width: 150,
  },
  {
    title: '不合格原因',
    key: 'errorMessage',
    width: 150,
  },
];

export const clockTableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    width: 200,
    key: 'deviceId',
  },
  {
    title: '设备名称',
    width: 200,
    key: 'deviceName',
  },
  {
    title: '组织机构',
    key: 'orgName',
  },
  {
    title: '经度',
    key: 'longitude',
  },
  {
    title: '纬度',
    key: 'latitude',
  },
  {
    title: '安装地址',
    key: 'address',
  },
  {
    title: 'IP地址',
    key: 'ipAddr',
  },
  {
    title: '端口号',
    key: 'port',
  },
  {
    title: '账号',
    key: 'user_id',
  },
  {
    title: '密码',
    key: 'password',
  },
  // { title: "操作", key: "errorMessage" },
];
export const ringColorEnum = {
  greenColor: 'greenColor',
  redColor: 'redColor',
  yellowColor: 'yellowColor',
  blueColor: 'blueColor',
};
