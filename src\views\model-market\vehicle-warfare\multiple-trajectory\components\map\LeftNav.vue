<template>
    <div class="left-nav">
        <header class="header">
            <span class="back-icon" @click="backToAimPage">
                <i class="iconfont iconfont icon-return"></i>
            </span>
            <span class="title">轨迹碰撞分析</span>
        </header>
        <article class="content">
            <card v-for="(item, index) in aimParams.captureList" :key="index" :style="`background: ${cardColor[index]}`" :data="item" @change-check="(val)=>{checedRecord(val,item)}" @get-obj="handleOpenOrClose" :moreTrajectory="item.moreTrajectory">
            </card>
            <coincidence-container :data="aimParams.coincidentPointList"></coincidence-container>
        </article>
    </div>
</template>
<script>
import Card from './card'
import CoincidenceContainer from './CoincidenceContainer'
export default {
    name: 'leftNav',
    components: {
        Card,
        CoincidenceContainer,
    },
    props: {
        aimParams: {
            default:()=>({}),
            type: Object
        }
    },
    data() {
        return {
            cardColor: ['#2d87f9', '#1cc688', '#ff8d32', '#a367f4', '#f14a4b']
        }
    },
    mounted(){
        // $pubsub.publish("aim-checked",this.aimParams.captureList.filter(item=>!!item.checked),this.aimParams)
    },
    methods: {
        handleOpenOrClose(val) {
            const status = val.moreTrajectory;
            this.aimParams.captureList.map(item=>{
                this.$set(item,"moreTrajectory",false)
            })
            !status && this.$set(val,"moreTrajectory",true)
        },
        backToAimPage() {
            $pubsub.publish("backto-aimpage")
        },
        checedRecord(val,item){
            // item.checked=val;
            // $pubsub.publish("aim-checked",this.aimParams.captureList.filter(item=>!!item.checked),this.aimParams)
        }
    }
}
</script>
<style lang="less" scoped>
.left-nav {
    overflow-x: hidden;
    overflow-y: auto;
    width: 340px;
    height: 87vh;
    // background: #fff;
    position: absolute;
    top: 10px;
    left: 10px;

    .header {
        line-height: 40px;
        width: 340px;
        height: 40px;
        background: #2d87f9;
        color: #fff;
        font-size: 14px;
        font-weight: bold;

        .back-icon {
            display: inline-block;
            width: 48px;
            text-align: center;
            border-right: 0.5px solid #fff;
            cursor: pointer;
        }

        .title {
            padding-left: 10px;
        }
    }

    .content {
        padding: 5px 0;
    }
}
</style>