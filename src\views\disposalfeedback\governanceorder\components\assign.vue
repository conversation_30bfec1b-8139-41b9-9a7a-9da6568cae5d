<template>
  <div :class="['assign', isBatch ? '' : '']">
    <ui-label label="指派给：" class="block">
      <RadioGroup v-model="assignMode" @on-change="onChangeAssignMode">
        <Radio :label="0">自定义指派</Radio>
        <Radio :label="1">自动指派</Radio>
      </RadioGroup>
    </ui-label>
    <ui-label v-if="assignMode === 0" label="指派规则" class="block">
      <div class="camera ml-sm" v-if="assignMode === 0" @click="selectPeople">
        <span class="receiver-color" v-if="!searchForm.people">请选择接收人</span>
        <span v-else class="receiver-color">{{ searchForm.people.name }}</span>
      </div>
    </ui-label>
    <ui-label v-if="assignMode === 1" label="指派规则：" class="block">
      <Select class="width-md" v-model="searchForm.assignMode" placeholder="请选择指派规则">
        <Option label="自动指派维护单位人" :value="1"> </Option>
        <Option label="自动指派组织机构人" :value="2"> </Option>
      </Select>
      <Button type="text" class="ml-sm" v-if="searchForm.assignMode === 2" @click="onClickAutoAssignOrg"
        >配置各单位工单接收人</Button
      >
    </ui-label>
    <select-people ref="selectpeople" v-model="peopleShow" @putPeople="putPeople"></select-people>
    <select-receiver
      ref="selectpeopleConfig"
      v-model="selectPeopleConfigVisible"
      @on-select-receiver="OnSelectReceiver"
    >
    </select-receiver>
  </div>
</template>
<script>
export default {
  props: {
    isBatch: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      peopleShow: false,
      selectPeopleConfigVisible: false,
      assignMode: 0,
      searchForm: {
        people: null,
        assignMode: 0,
        assignList: [],
      },
    };
  },
  updated() {
    this.$emit('updateDisabled', this.searchForm);
  },
  methods: {
    OnSelectReceiver(val) {
      this.searchForm.assignList = val.map((item) => {
        return {
          assignId: item.username,
          assignName: item.name,
          orgCode: item.orgCode,
          orgName: item.orgName,
          orgId: item.orgId,
        };
      });
    },
    onChangeAssignMode(val) {
      // 后端不存 自动指派
      if (val === 0) {
        this.searchForm.assignMode = 0;
      } else {
        this.searchForm.assignMode = 1;
      }
    },
    onClickAutoAssignOrg() {
      this.selectPeopleConfigVisible = true;
    },
    selectPeople() {
      this.peopleShow = true;
    },
    putPeople(item) {
      this.searchForm.people = item;
      this.peopleShow = false;
      this.$emit('updateDisabled', item);
    },
    reset() {
      this.searchForm.people = null;
      this.peopleShow = false;
      this.assignMode = 0;
      this.searchForm = {
        assignMode: 0,
        assignList: [],
      };
      this.$refs.selectpeople.reset();
    },
  },
  watch: {},
  components: {
    SelectPeople: require('@/api-components/select-people/select-people.vue').default,
    selectReceiver: require('@/views/disposalfeedback/governanceorder/components/select-receiver.vue').default,
  },
};
</script>
<style lang="less" scoped>
.assign {
  width: 100%;
  //// padding: 0 20px;
  //display: flex;
  //align-items: center;
  &-label {
    width: 65px;
    // padding: 0 20px;
    text-align: left;
    color: #ffffff;
  }
}
.single {
  display: flex;
  justify-content: center;
  padding: 50px 0;
}
.receiver-color {
  color: var(--color-btn-text);
}
</style>
