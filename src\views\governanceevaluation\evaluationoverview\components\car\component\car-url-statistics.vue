<template>
  <div class="information-statistics">
    <ul class="statistics-ul" ref="ulRef">
      <li ref="statisticBox" v-for="(item, index) in statisticsList" :key="index" class="mb-sm" :class="[item.liBg]">
        <div class="monitoring-data">
          <p class="monitoring-icon" :class="{ 'monitoring-data-icon': isIconBg }">
            <i class="icon-font f-40" :class="[item.icon, item.iconColor]"></i>
          </p>
          <span class="ml-md">
            <p class="base-text-color" :title="item.name">{{ item.name }}</p>
            <p class="statistic-num" :class="item.textColor" v-if="item.type === 'number'" ref="statisticNumRef">
              <countTo :startVal="0" :endVal="item.value || 0" :duration="3000"></countTo>
            </p>
            <p
              class="statistic-num"
              :class="item.qualified === true ? item.textColor : 'red'"
              v-else-if="item.type === 'percentage'"
              ref="statisticNumRef"
            >
              {{ item.value || 0 }}
            </p>
          </span>
          <slot :name="item.key" :row="item"></slot>
        </div>
        <span
          :class="['icon-font', item.qualified ? 'icon-dabiao' : 'icon-budabiao']"
          v-if="item.qualified === true || item.qualified === false"
        ></span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'statistics',
  components: {
    countTo: require('vue-count-to').default,
  },
  props: {
    // 图标是否有圆圈背景
    isIconBg: {
      default: false,
    },
    statisticsList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
};
</script>

<style lang="less" scoped>
.information-statistics {
  position: relative;
  display: flex;
  margin-right: 10px;
  width: 100%;
  height: 100%;
  .statistics-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    margin-left: 23.5%;
    margin-right: 20%;
    justify-content: flex-start;
    align-content: flex-start;
    li:first-child {
      height: 210px !important ;
      width: 22% !important;
      position: absolute;
      left: 15px;
      .monitoring-data span .statistic-num {
        font-size: 30px !important;
      }
    }
    li:last-child {
      height: 210px !important;
      width: 22% !important;
      position: absolute;
      right: 15px;
      .monitoring-data span .statistic-num {
        font-size: 30px !important;
      }
      .monitoring-data {
        span {
          p {
            width: 210px;
          }
        }
      }
    }
    li {
      position: relative;
      align-items: center;
      margin-right: 10px;
      background: #ebb225;
      height: 100px;
      width: 30%;
      display: flex;
      padding: 30px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      .monitoring-data-icon {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 80px;
      }
      .monitoring-data {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        i {
          display: inline-block;
        }
        .f-40 {
          font-size: 40px;
        }

        span {
          display: inline-block;
          flex: 1;
          text-align: left;
          p {
            // width: 180px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            font-style: normal;
            font-size: 14px;
          }
          .statistic-num {
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: bold;
          }
        }
      }
    }
    .icon-budabiao,
    .icon-dabiao {
      color: #bc3c19;
      font-size: 16px;
      line-height: 16px;
      position: absolute;
      top: 180px;
      right: 10px;
    }
    .icon-dabiao {
      color: #0e8f0e;
    }
    .icon-bg1 {
      background: linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg2 {
      background: linear-gradient(360deg, #b58e0f 0%, #bb6603 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg3 {
      background: linear-gradient(360deg, #26d82c 0%, #127d0a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .red {
      color: #bc3c19;
    }
    .icon-bg4 {
      background: linear-gradient(180deg, #f24e2c 0%, #772c0a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg6 {
      background: linear-gradient(180deg, #c122f8 0%, #7428cb 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg7 {
      background: linear-gradient(180deg, #5b46f5 0%, #4e48e5 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg8 {
      background: linear-gradient(180deg, #b4c519 0%, #66770a 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg9 {
      background: linear-gradient(180deg, #19b6f2 0%, #0e7f9b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg10 {
      background: linear-gradient(180deg, #a3aaf8 0%, #767cb9 75%, #676da4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .color1 {
      color: #1bafd5;
    }
    .color2 {
      color: #b58e0f;
    }
    .color3 {
      color: #26d82c;
    }
    .color4 {
      color: #f24e2c;
    }
    .color5 {
      color: #4ba0f5;
    }
    .color6 {
      color: #c122f8;
    }
    .color7 {
      color: #5b46f5;
    }
    .color8 {
      color: #b4c519;
    }
    .color9 {
      color: #19b6f2;
    }
    .color10 {
      color: #a3aaf8;
    }
    .li-bg1 {
      background: rgba(16, 165, 170, 0.21);
    }
    .li-bg2 {
      background: rgba(235, 178, 37, 0.21);
    }
    .li-bg3 {
      background: rgba(32, 182, 35, 0.21);
    }
    .li-bg4 {
      background: rgba(198, 64, 49, 0.21);
    }
    .li-bg5 {
      background: rgba(15, 167, 245, 0.21);
    }
    .li-bg6 {
      background: rgba(154, 24, 149, 0.21);
    }
    .li-bg7 {
      background: rgba(43, 66, 220, 0.21);
    }
    .li-bg8 {
      background: rgba(248, 229, 58, 0.21);
    }
    .li-bg9 {
      background: rgba(15, 167, 245, 0.21);
    }
    .li-bg10 {
      background: rgba(199, 127, 201, 0.21);
    }
  }
}
</style>
