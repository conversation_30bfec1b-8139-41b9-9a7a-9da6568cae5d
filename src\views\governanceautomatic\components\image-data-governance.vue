<template>
  <div class="image-data-governance-container" v-ui-loading="{ loading }">
    <div class="based-field-header">
      <i :class="['icon-font', 'font-blue', 'icon-renlianshitushuju']"></i>
      <div class="title-header">人脸图片治理（当日）</div>
    </div>
    <div class="image-governance">
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输入</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">设备数量：</span>
            <!--            <span class="color-green">{{data.faceInputDeviceNum}}</span>-->
            <countTo
              class="color-green"
              :startVal="data.faceInputDeviceNum - 200"
              :endVal="data.faceInputDeviceNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">图片数据量：</span>
            <!--            <span class="color-green">{{data.faceInputImageNum}}</span>-->
            <countTo
              class="color-green"
              :startVal="data.faceInputImageNum - 200"
              :endVal="data.faceInputImageNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="1" :connectingOptions="faceArrowsInfo1"></connecting-arrow>
      <div class="data-item-multiple">
        <div class="item-desc custom-shadow mb-sm">
          <p class="text-shadow-blue color-green-1">模糊图像清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.faceBlurDataNum - 200"
              :endVal="data.faceBlurDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.faceBlurClearDataNum - 200"
              :endVal="data.faceBlurClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">重复图像清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.faceRepeatDataNum - 200"
              :endVal="data.faceRepeatDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.faceRepeatClearDataNum - 200"
              :endVal="data.faceRepeatClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="2" :connectingOptions="faceArrowsInfo2"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">小图非唯一人脸图片清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.faceNonuniqueDataNum - 200"
              :endVal="data.faceNonuniqueDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.faceNonuniqueClearDataNum - 200"
              :endVal="data.faceNonuniqueClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="3" :connectingOptions="faceArrowsInfo3"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输出</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">合格总量：</span>
            <countTo
              class="color-green"
              :startVal="data.faceOutPutDataNum - 200"
              :endVal="data.faceOutPutDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理总量：</span>
            <countTo
              class="color-red"
              :startVal="data.faceOutPutClearDataNum - 200"
              :endVal="data.faceOutPutClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
    </div>
    <div class="based-field-header">
      <i :class="['icon-font', 'font-blue', 'icon-cheliangshitushuju']"></i>
      <div class="title-header">车辆图片治理（当日）</div>
    </div>
    <div class="image-governance car-image-governance">
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输入</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">设备数量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleInputDeviceNum - 200"
              :endVal="data.vehicleInputDeviceNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">图片数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleInputImageNum - 200"
              :endVal="data.vehicleInputImageNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="4" :connectingOptions="faceArrowsInfo1"></connecting-arrow>
      <div class="data-item-multiple">
        <div class="item-desc custom-shadow mb-sm">
          <p class="text-shadow-blue color-green-1">模糊图像清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleBlurDataNum - 200"
              :endVal="data.vehicleBlurDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.vehicleBlurClearDataNum - 200"
              :endVal="data.vehicleBlurClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">重复图像清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleRepeatDataNum - 200"
              :endVal="data.vehicleRepeatDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.vehicleRepeatClearDataNum - 200"
              :endVal="data.vehicleRepeatClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="5" :connectingOptions="faceArrowsInfo2"></connecting-arrow>
      <div class="data-item-multiple">
        <div class="item-desc custom-shadow mb-sm">
          <p class="text-shadow-blue color-green-1">车牌补全校准</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehiclePlateDataNum - 200"
              :endVal="data.vehiclePlateDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">补全校准：</span>
            <countTo
              class="color-yellow"
              :startVal="data.vehiclePlateCompletionDataNum - 200"
              :endVal="data.vehiclePlateCompletionDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.vehiclePlateClearDataNum - 200"
              :endVal="data.vehiclePlateClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">其他属性补全校准</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">数据总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleOtherDataNum - 200"
              :endVal="data.vehicleOtherDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">补全校准：</span>
            <countTo
              class="color-yellow"
              :startVal="data.vehicleOtherCompletionDataNum - 200"
              :endVal="data.vehicleOtherCompletionDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.vehicleOtherClearDataNum - 200"
              :endVal="data.vehicleOtherClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="6" :connectingOptions="faceArrowsInfo3"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输出</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">合格总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleOutPutPassDataNum - 200"
              :endVal="data.vehicleOutPutPassDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理总量：</span>
            <countTo
              class="color-green"
              :startVal="data.vehicleOutPutClearDataNum - 200"
              :endVal="data.vehicleOutPutClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">补全校准：</span>
            <countTo
              class="color-yellow"
              :startVal="data.vehicleOutPutCompletionDataNum - 200"
              :endVal="data.vehicleOutPutCompletionDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
    </div>
    <div class="based-field-header">
      <i :class="['icon-font', 'font-blue', 'icon-zhongdianrenyuanshuju']"></i>
      <div class="title-header">ZDR轨迹治理（当日）</div>
    </div>
    <div class="image-governance">
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输入</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">轨迹总量：</span>
            <countTo
              class="color-green"
              :startVal="data.zdrInputDataNum - 200"
              :endVal="data.zdrInputDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="7" :connectingOptions="faceArrowsInfo1"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">重复轨迹清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">轨迹总量：</span>
            <countTo
              class="color-green"
              :startVal="data.zdrRepeatDataNum - 200"
              :endVal="data.zdrRepeatDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.zdrRepeatClearDataNum - 200"
              :endVal="data.zdrRepeatClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="8" :connectingOptions="faceArrowsInfo2"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">错误轨迹清理</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">轨迹总量：</span>
            <countTo
              class="color-green"
              :startVal="data.zdrTrackDataNum - 200"
              :endVal="data.zdrTrackDataNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理数量：</span>
            <countTo
              class="color-red"
              :startVal="data.zdrTrackClearDataNum - 200"
              :endVal="data.zdrTrackClearDataNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
      <connecting-arrow key="9" :connectingOptions="faceArrowsInfo3"></connecting-arrow>
      <div class="data-item">
        <div class="item-desc custom-shadow">
          <p class="text-shadow-blue color-green-1">数据输出</p>
          <!--          <p class="color-blue">20+质量检测模型，多算法交叉验证</p>-->
          <p>
            <span class="item-label">合格总量：</span>
            <countTo
              class="color-green"
              :startVal="data.zdrOutPutNum - 200"
              :endVal="data.zdrOutPutNum"
              :duration="3000"
            ></countTo>
          </p>
          <p>
            <span class="item-label">清理总量：</span>
            <countTo
              class="color-red"
              :startVal="data.zdrOutPutClearNum - 200"
              :endVal="data.zdrOutPutClearNum"
              :duration="3000"
            ></countTo>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Mock from 'mockjs';
/*Mock.setup({
  timeout: 200
})*/
export default {
  name: 'image-data-governance',
  props: {},
  data() {
    return {
      data: {
        faceInputDeviceNum: 0,
        faceInputImageNum: 6000000 + Mock.Random.integer(10416, 20833),
        faceBlurDataNum: 0,
        faceBlurClearDataNum: 0,
        faceRepeatDataNum: 0,
        faceRepeatClearDataNum: 0,
        faceNonuniqueDataNum: 0,
        faceNonuniqueClearDataNum: 0,
        faceOutPutDataNum: 0,
        faceOutPutClearDataNum: 0,

        vehicleInputDeviceNum: 0,
        vehicleInputImageNum: 18000000 + Mock.Random.integer(31250, 62500),
        vehicleBlurDataNum: 0,
        vehicleBlurClearDataNum: 0,
        vehicleRepeatDataNum: 0,
        vehicleRepeatClearDataNum: 0,
        vehiclePlateDataNum: 0,
        vehiclePlateCompletionDataNum: 0,
        vehiclePlateClearDataNum: 0,
        vehicleOtherDataNum: 0,
        vehicleOtherCompletionDataNum: 0,
        vehicleOtherClearDataNum: 0,
        vehicleOutPutPassDataNum: 0,
        vehicleOutPutClearDataNum: 0,
        vehicleOutPutCompletionDataNum: 0,

        zdrInputDataNum: 200000 + Mock.Random.integer(50, 100),
        zdrRepeatDataNum: 0,
        zdrRepeatClearDataNum: 0,
        zdrTrackDataNum: 0,
        zdrTrackClearDataNum: 0,
        zdrOutPutNum: 0,
        zdrOutPutClearNum: 0,
      },

      faceArrowsInfo1: {
        width: '9.5%',
        left: '23%',
        height: '7px',
      },
      faceArrowsInfo2: {
        width: '9.5%',
        left: '45.2%',
        height: '7px',
      },
      faceArrowsInfo3: {
        width: '9.5%',
        left: '67.4%',
        height: '7px',
      },
      interval: null,
      loading: false,
    };
  },
  methods: {
    generateRandomData() {
      this.data = {
        faceInputDeviceNum: 18592,
        faceInputImageNum: this.data.faceInputImageNum + Mock.Random.integer(2000, 3000),
        faceBlurDataNum: this.data.faceInputImageNum - Mock.Random.integer(1000, 2000),
        faceBlurClearDataNum: this.data.faceInputImageNum * 0.05,
        faceRepeatDataNum: this.data.faceInputImageNum - Mock.Random.integer(1000, 2000),
        faceRepeatClearDataNum: this.data.faceInputImageNum * 0.02,
        faceNonuniqueDataNum: this.data.faceRepeatDataNum - Mock.Random.integer(1000, 2000),
        faceNonuniqueClearDataNum: this.data.faceInputImageNum * 0.01,
        faceOutPutDataNum:
          this.data.faceNonuniqueDataNum -
          (this.data.faceBlurClearDataNum + this.data.faceRepeatClearDataNum + this.data.faceNonuniqueClearDataNum),
        faceOutPutClearDataNum:
          this.data.faceBlurClearDataNum + this.data.faceRepeatClearDataNum + this.data.faceNonuniqueClearDataNum,

        vehicleInputDeviceNum: 3640,
        vehicleInputImageNum: this.data.vehicleInputImageNum + Mock.Random.integer(6000, 9000),
        vehicleBlurDataNum: this.data.vehicleInputImageNum - Mock.Random.integer(1000, 2000),
        vehicleBlurClearDataNum: this.data.vehicleInputImageNum * 0.05,
        vehicleRepeatDataNum: this.data.vehicleInputImageNum - Mock.Random.integer(1000, 2000),
        vehicleRepeatClearDataNum: this.data.vehicleInputImageNum * 0.02,
        vehiclePlateDataNum: this.data.vehicleBlurDataNum - Mock.Random.integer(1000, 2000),
        vehiclePlateCompletionDataNum: this.data.vehicleInputImageNum * 0.012,
        vehiclePlateClearDataNum: this.data.vehicleInputImageNum * 0.02,
        vehicleOtherDataNum: this.data.vehicleBlurDataNum - Mock.Random.integer(1000, 2000),
        vehicleOtherCompletionDataNum: this.data.vehicleInputImageNum * 0.012,
        vehicleOtherClearDataNum: this.data.vehicleInputImageNum * 0.01,
        vehicleOutPutPassDataNum:
          this.data.vehicleInputImageNum -
          (this.data.vehicleBlurClearDataNum +
            this.data.vehicleRepeatClearDataNum +
            this.data.vehiclePlateClearDataNum +
            this.data.vehicleOtherClearDataNum),
        vehicleOutPutClearDataNum:
          this.data.vehicleBlurClearDataNum +
          this.data.vehicleRepeatClearDataNum +
          this.data.vehiclePlateClearDataNum +
          this.data.vehicleOtherClearDataNum,
        vehicleOutPutCompletionDataNum:
          this.data.vehiclePlateCompletionDataNum + this.data.vehicleOtherCompletionDataNum,

        zdrInputDataNum: this.data.zdrInputDataNum + Mock.Random.integer(50, 100),
        zdrRepeatDataNum: this.data.zdrInputDataNum - Mock.Random.integer(50, 100),
        zdrRepeatClearDataNum: this.data.zdrInputDataNum * 0.02,
        zdrTrackDataNum: this.data.zdrRepeatDataNum - Mock.Random.integer(50, 100),
        zdrTrackClearDataNum: this.data.zdrInputDataNum * 0.06,
        zdrOutPutNum: this.data.zdrTrackDataNum - (this.data.zdrRepeatClearDataNum + this.data.zdrTrackClearDataNum),
        zdrOutPutClearNum: this.data.zdrRepeatClearDataNum + this.data.zdrTrackClearDataNum,
      };
    },
  },
  created() {
    this.generateRandomData();
    this.generateRandomData();
    this.generateRandomData();
    this.loading = true;
    setTimeout(() => {
      this.loading = false;
    }, 2 * 1000);

    this.interval = setInterval(() => {
      try {
        this.loading = true;
        this.generateRandomData();
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    }, 10 * 1000);
  },
  beforeDestroy() {
    clearInterval(this.interval);
  },
  components: {
    countTo: require('vue-count-to').default,
    ConnectingArrow: require('./connecting-arrow.vue').default,
  },
};
</script>
<style lang="less" scoped>
.custom-shadow {
  box-shadow: 0 0 20px rgba(8, 138, 213, 0.6) inset;
}

.text-shadow-blue {
  text-shadow: 0 0 10px #267df3;
}

.color-blue {
  color: var(--color-primary);
}

.color-gay {
  color: #7c89a2;
}

.color-green {
  color: var(--color-bluish-green-text);
}
.color-green-1 {
  color: #00f8ff;
}
.color-yellow {
  color: rgb(241, 138, 55);
}

.color-red {
  color: #bc3c19;
}

.image-data-governance-container {
  overflow: hidden;
  position: relative;
  padding: 0 10px 10px 10px;
  height: 100%;
  background-color: var(--bg-content);

  .based-field-header {
    display: flex;
    margin: 0 5px 10px;
    border-bottom: 1px solid var(--border-modal-footer);
    align-items: center;

    .icon-font {
      font-size: 20px;
      color: #fff;
      margin-right: 6px;
    }

    .title-header {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
      line-height: 20px;
    }
  }
  .image-governance {
    height: calc((100% / 3) - 45px);
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .data-item {
      position: relative;
      height: 130px;
      width: 230px;
      border: 1px dashed rgba(0, 248, 255, 0.4);
      padding: 10px;
      display: flex;
      justify-content: space-between;

      .item-desc {
        height: 100%;
        width: 100%;
        border-radius: 8px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
    .data-item-multiple {
      position: relative;
      overflow: hidden;
      height: 130px * 2 +30px;
      width: 230px;
      border: 1px dashed rgba(0, 248, 255, 0.4);
      padding: 10px;

      .item-desc {
        height: 100%/2;
        width: 100%;
        border-radius: 8px;
        padding: 15px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
  .car-image-governance {
    height: 35%;
  }
}
</style>
