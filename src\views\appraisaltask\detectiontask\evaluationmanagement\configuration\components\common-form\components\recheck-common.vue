<template>
  <div>
    <FormItem label="111是否需要复检">
      <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
        <Radio label="1">是</Radio>
        <Radio label="2">否</Radio>
      </RadioGroup>
      <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
    </FormItem>
    <FormItem label="复检设备" v-if="isRecheck === '1'">
      <RadioGroup v-model="formData.reinspect.model">
        <Radio label="UNQUALIFIED">检测不合格设备</Radio>
      </RadioGroup>
    </FormItem>
    <FormItem label="复检时间" v-if="isRecheck === '1'">
      <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
        <Radio label="INTERVAL">时间间隔</Radio>
        <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
      </RadioGroup>
      <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
        <span class="params-suffix">检测结束</span>
        <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"> </InputNumber>
        <Select class="custom-width100" transfer v-model="formData.reinspect.scheduleKey">
          <Option :value="1">时</Option>
          <Option :value="2">分</Option>
        </Select>
      </div>
      <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
        <Select class="custom-width100 mr-sm" transfer v-model="formData.reinspect.scheduleKey">
          <Option :value="3">当天</Option>
          <Option :value="4">第二天</Option>
          <Option :value="5">第三天</Option>
        </Select>
        <TimePicker
          :value="formData.reinspect.scheduleValue"
          transfer
          format="HH:mm"
          placeholder="请选择"
          style="width: 112px"
          @on-change="handleChangeTime"
        ></TimePicker>
      </div>
    </FormItem>
  </div>
</template>

<script>
export default {
  name: 'RecheckCommon',
  props: {
    captureNum: {},
  },
  data() {
    return {
      isRecheck: '2',
      formData: {
        reinspect: null,
      },
    };
  },
  created() {},
  methods: {
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
  },
  watch: {
    formData: {
      handler(val) {
        this.$emit('updateForm', val);
      },
      deep: true,
    },
    captureNum: {
      handler(val) {
        this.formData.captureNum = val;
      },
      immediate: true,
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.input-width {
  width: 380px;
}
</style>
