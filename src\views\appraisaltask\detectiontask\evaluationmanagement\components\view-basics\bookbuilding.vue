<template>
  <ui-modal class="bookbuilding" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-if="Object.keys(indexList).length">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button>
          </div>
        </div>
        <div class="scheme_header">
          <div class="scheme_line"></div>
          <div class="scheme_title">
            <span>检测结果统计</span>
          </div>
        </div>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="faceEchartRing"
                :echart-style="ringStyle"
                ref="faceZdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="carEchartRing"
                :echart-style="ringStyle"
                ref="carZdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <div class="btns">
          <Button class="btn_jk" :class="{ active: curBtn === 1 }" @click="changeBtn(1)"> 未建档视频监控 </Button>
          <Button class="btn_kk" :class="{ active: curBtn === 2 }" @click="changeBtn(2)"> 未建档人脸卡口 </Button>
          <Button class="btn_k" :class="{ active: curBtn === 3 }" @click="changeBtn(3)"> 未建档车辆卡口库 </Button>
        </div>
        <div class="list auto-fill">
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #longitude="{ row }">
              <span>{{ row.longitude | filterLngLat }}</span>
            </template>
            <template #latitude="{ row }">
              <span>{{ row.latitude | filterLngLat }}</span>
            </template>
          </ui-table>
          <!-- <div class="no-data" v-if="!tableData.length">
            <img src="@/assets/img/common/nodata.png" alt />
          </div> -->
          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
    <div class="no-box" v-else>
      <div class="no-data">
        <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt />
        <img v-else src="@/assets/img/common/nodata-light.png" alt />
      </div>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.bookbuilding {
  //     .no-data_str {
  //   transform: translate(-24%, -50%);
  // }
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 10px;
    }
  }
  .no-box {
    width: 1860px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .btns {
        margin-top: 10px;
        .btn_jk {
          border-right: none !important;
        }
        .btn_kk {
          border-right: none !important;
        }
        .btn_k {
        }
        button {
          color: #fff;
          background-color: #12294e;
          border-radius: 0;
        }
        .active {
          color: #fff;
          background-color: #2d8cf0;
        }
        button:hover {
          color: #fff;
          background-color: #2d8cf0;
        }
      }
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 410px;
          display: flex;
          justify-content: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
    }
    .ui-table {
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
    .page {
      position: absolute;
      bottom: 5px;
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';
export default {
  props: [''],
  data() {
    return {
      echartRing: {},
      faceEchartRing: {},
      carEchartRing: {},
      curBtn: 1,
      ringStyle: {
        width: '400px',
        height: '180px',
      },
      moduleData: {
        rate: '建档率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      zdryChartObj: {
        showData: [
          { name: '已建档', value: 0 },
          { name: '未建档', value: 0 },
        ],
        count: 0,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      faceChartObj: {
        showData: [
          { name: '已建档', value: 0 },
          { name: '未建档', value: 0 },
        ],
        count: 0,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      carChartObj: {
        showData: [
          { name: '已建档', value: 0 },
          { name: '未建档', value: 0 },
        ],
        count: 0,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },

      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          minWidth: 150,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 150,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', minWidth: 120, tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 120,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          width: 150,
        },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr', width: 120 },
        {
          title: '数据来源',
          key: 'examineResult',
          minWidth: 150,
          tooltip: true,
        },
        { title: '安装地址', key: 'address', minWidth: 150, tooltip: true },
        // { title: "操作", slot: "option" },
      ],
      tableData: [],
      minusTable: 500,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9.7rem',
      },
      visible: true,
      loading: false,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
      exportLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  async mounted() {
    await this.getEvaluationRecord();
    await this.initRing();
    await this.faceRing();
    await this.carRing();
  },

  methods: {
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };

        let res = await this.$http.get(governanceevaluation.exportInputIndex, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },

    async getEvaluationRecord() {
      try {
        let data = {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        };
        let res = await this.$http.post(governanceevaluation.queryEvaluationBasicRecord, data);
        if (!res.data.data) return;
        this.indexList = res.data.data;
        this.moduleData.rateValue = this.indexList.resultValue; //建档率
        this.moduleData.priceValue = this.indexList.standardsValue; //达标值
        this.moduleData.resultValue = this.indexList.qualifiedDesc; //考核结果
        this.moduleData.remarkValue = this.indexList.remark; //提示
      } catch (err) {
        console.log(err);
      }
    },
    initRing() {
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '已建档') {
          item.value = this.indexList.bookBuildingSxjCount;
        } else {
          item.value = this.indexList.bookBuildingSxjStockCount - this.indexList.bookBuildingSxjCount;
        }
      });
      let showData = this.zdryChartObj.showData;
      this.zdryChartObj.count = this.indexList.bookBuildingSxjStockCount;
      let formatData = {
        seriesName: '视频监控设备',
        showData: showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    faceRing() {
      this.faceChartObj.showData.map((item) => {
        if (item.name === '已建档') {
          item.value = this.indexList.bookBuildingRlkkCount;
        } else {
          item.value = this.indexList.bookBuildingRlkkStockCount - this.indexList.bookBuildingRlkkCount;
        }
      });
      let showData = this.faceChartObj.showData;
      this.faceChartObj.count = this.indexList.bookBuildingRlkkStockCount;
      let formatData = {
        seriesName: '人脸卡口设备',
        showData: showData,
        count: this.faceChartObj.count,
        color: this.faceChartObj.color,
      };
      this.faceChartObj.formatData = formatData;
      this.faceEchartRing = this.$util.doEcharts.evaluationPageRin(this.faceChartObj.formatData);
    },
    carRing() {
      this.carChartObj.showData.map((item) => {
        if (item.name === '已建档') {
          item.value = this.indexList.bookBuildingClkkCount;
        } else {
          item.value = this.indexList.bookBuildingClkkStockCount - this.indexList.bookBuildingClkkCount;
        }
      });
      let showData = this.carChartObj.showData;
      this.carChartObj.count = this.indexList.bookBuildingClkkStockCount;
      let formatData = {
        seriesName: '车辆卡口设备',
        showData: showData,
        count: this.carChartObj.count,
        color: this.carChartObj.color,
      };
      this.carChartObj.formatData = formatData;
      this.carEchartRing = this.$util.doEcharts.evaluationPageRin(this.carChartObj.formatData);
    },

    changeBtn(val) {
      this.curBtn = val;
    },

    changePage(val) {
      this.searchData.pageNum = val;
      //   this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      //   this.init();
    },
    operationTask() {},
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
  },
};
</script>
