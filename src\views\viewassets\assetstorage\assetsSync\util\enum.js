import global from '@/util/global';
export const stateOptions = [
  {
    label: '全部设备',
    value: '',
  },
  {
    label: '新增设备',
    value: '0',
    word: '新',
    color: 'var(--color-bluish-green-text)',
  },
  {
    label: '相同设备',
    value: '1',
  },
  {
    label: '差异设备',
    value: '2',
    word: '异',
    color: '#E85128',
  },
  {
    label: '删除设备',
    value: '3',
  },
];
export const stateOptionsObject = {};
export const stateOptionsValueObject = {};
stateOptions.forEach((item) => {
  stateOptionsObject[item.label] = item;
  stateOptionsValueObject[item.value] = item;
});
export const tabsList = [
  {
    label: '共享联网平台',
    value: '2',
    interfaceMiddle: 'assertDeviceAsycNet', // 接口中间地址
  },
  {
    label: '视图库',
    value: '3',
    interfaceMiddle: 'assertDeviceAsycView',
  },
  {
    label: '一机一档',
    value: '4',
    interfaceMiddle: 'assertDeviceAsycArchives',
  },
];
export const tabsListObject = {};
tabsList.forEach((item) => (tabsListObject[item.value] = item.label));
export const tableColumns = [
  {
    type: 'selection',
    width: 50,
    align: 'center',
  },
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 40,
  },
  {
    width: 200,
    title: `${global.filedEnum.deviceId}`,
    slot: 'deviceId',
    align: 'left',
    tree: true,
  },
  {
    width: 190,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: '所属单位',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.longitude}`,
    key: 'longitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.latitude}`,
    key: 'latitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 130,
    title: global.filedEnum.ipAddr,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlxText',
  },

  {
    minWidth: 110,
    title: `${global.filedEnum.sbcjqy}`,
    key: 'sbcjqyText',
    tooltip: true,
  },
  {
    minWidth: 100,
    title: `${global.filedEnum.phyStatus}`,
    slot: 'phyStatus',
    align: 'center',
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '原始目录',
    key: 'channelName',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '数据来源',
    key: 'sourceIdText',
    align: 'left',
    tooltip: true,
  },
  {
    width: 160,
    title: '同步时间',
    key: 'createTime',
  },
  {
    width: 160,
    title: '入库人',
    key: 'storageCreator',
    sortable: true,
    // sortMethod: (a, b, type) => {
    //   this.initSort(type.toUpperCase(), 'RESULT_VALUE')
    // }
  },
  {
    width: 160,
    title: '入库时间',
    key: 'storageTime',
    sortable: true,
  },
  {
    width: 100,
    title: '基础信息状态',
    slot: 'checkStatus',
    align: 'center',
    fixed: 'right',
  },
  {
    width: 100,
    title: '比对状态',
    slot: 'compareStatus',
    align: 'center',
    fixed: 'right',
  },
  {
    width: 100,
    title: '入库状态',
    fixed: 'right',
    slot: 'storageStatus',
    align: 'center',
  },
  {
    width: 160,
    title: '操作',
    slot: 'action',
    align: 'center',
    fixed: 'right',
    className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
  },
];
export const rightTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    width: 200,
    title: `${global.filedEnum.deviceId}`,
    slot: 'deviceId',
    align: 'left',
    tree: true,
  },
  {
    width: 190,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: '所属单位',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.longitude}`,
    key: 'longitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.latitude}`,
    key: 'latitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 130,
    title: global.filedEnum.ipAddr,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlx',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlx',
  },

  {
    minWidth: 110,
    title: `${global.filedEnum.sbcjqy}`,
    key: 'sbcjqyText',
    tooltip: true,
  },
  {
    minWidth: 100,
    title: `${global.filedEnum.phyStatus}`,
    key: 'phyStatus',
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '原始目录',
    key: 'errorType',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 150,
    title: '数据来源',
    key: 'sourceIdText',
    align: 'left',
    tooltip: true,
  },
];
export const leftTableColumns = [
  {
    type: 'selection',
    width: 50,
    align: 'center',
  },
  ...rightTableColumns,
];
export const errorColumns = [
  {
    title: '序号',
    type: 'index',
    width: 70,
    align: 'center',
  },
  {
    title: '不合格原因',
    key: 'errorMessage',
    tooltip: true,
  },
  {
    title: '检测规则名称',
    key: 'checkRuleName',
    tooltip: true,
  },
  {
    title: '不合格字段',
    key: 'propertyName',
    tooltip: true,
  },
  {
    title: '异常类型',
    key: 'errorType',
    tooltip: true,
  },
  {
    title: '实际结果',
    key: 'propertyValue',
    tooltip: true,
  },
];

/**
 * 第一次配置没有数据，前端的默认配置
 */
export const configDefaultParams = {
  addStorageParam:
    '[{"fieldName":"deviceId","fieldRemark":"设备编码"},{"fieldName":"sourceId","fieldRemark":"数据来源"},{"fieldName":"sbgnlx","fieldRemark":"摄像机功能类型"},{"fieldName":"deviceName","fieldRemark":"设备名称"},{"fieldName":"ipAddr","fieldRemark":"IP地址"},{"fieldName":"civilCode","fieldRemark":"行政区域代码"}]',
  compareAddSource: '',
  compareAddType: '',
  compareDelSource: '',
  compareDelType: '',
  compareParam: '[{"fieldName":"deviceId","fieldRemark":"设备编码"}]',
  fieldNotNull: '',
  fieldNull: '',
  isStorage: '1',
  ruleParam:
    '[{"ruleId":1,"isConfigure":false,"ruleName":"空值检测","ruleD,esc":"检测字段是否为空","ruleCode":"1001"},{"ruleId":2,"isConfigure":false,"ruleName":"重复检测","ruleDesc":"检测字段是否重复","ruleCode":"1002"},{"ruleId":3,"isConfigure":false,"ruleName":"IP地址格式检测","ruleDesc":"检测IP地址格式是否正确","ruleCode":"1003"},{"ruleId":4,"isConfigure":false,"ruleName":"MAC地址格式检测","ruleDesc":"检测MAC地址格式是否正确","ruleCode":"1004"},{"ruleId":5,"isConfigure":false,"ruleName":"行政区划格式检测","ruleDesc":"需符合《GB/T 2260 中华人民共和国行政区划代码》规定","ruleCode":"1005"},{"ruleId":6,"isConfigure":false,"ruleName":"设备编码格式检测","ruleDesc":"需符合《GB/T 28181 2016》中关于设备编码的规定","ruleCode":"1006"},{"ruleId":7,"isConfigure":false,"ruleName":"经纬度精度检测","ruleDesc":"经纬度精度检测","ruleCode":"1007"},{"ruleId":8,"isConfigure":false,"ruleName":"经纬度越界检测","ruleDesc":"经纬度越界检测","ruleCode":"1008"},{"ruleId":9,"isConfigure":false,"ruleName":"经纬度偏移检测","ruleDesc":"经纬度偏移检测","ruleCode":"1009"},{"ruleId":60,"isConfigure":false,"ruleName":"设备名称检测","ruleDesc":"设备名称长度和特殊字符检测","ruleCode":"1060"},{"ruleId":62,"isConfigure":false,"ruleName":"设备(物理)状态检测","ruleDesc":"检测结果为有生产数据的设备状态却为不可用，判定该设备为设备状态异常","ruleCode":"1047"},{"ruleId":90,"isConfigure":false,"ruleName":"数据格式校验","ruleDesc":"检测是否满足数据项定义，如类型、长度、精度、字典规范等","ruleCode":"1064"},{"ruleId":91,"isConfigure":false,"ruleName":"自定义规则检测","ruleDesc":"自定义规则检测","ruleCode":"1065"}]',
  storageCondition: '',
  storageParam:
    '[{"fieldName":"sourceId","fieldRemark":"数据来源","addType":"1"},{"fieldName":"deviceId","fieldRemark":"设备编码","addType":"1"}]',
};
