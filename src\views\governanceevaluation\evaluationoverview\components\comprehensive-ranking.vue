<template>
  <div class="synthesize-right" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
    <div class="synthesize-title">
      <title-content title="评价综合排名"></title-content>
    </div>
    <div class="synthesize-content">
      <ul>
        <li v-for="(item, index) in rankData" :key="index">
          <div class="content-firstly">
            <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
            <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
            <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
            <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
              item.rank
            }}</span>
          </div>
          <Tooltip class="content-second" transfer :content="item.regionName">
            <div>
              <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
              <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
              <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
              <!-- <span>{{ item.regionName }}</span> -->
              <span v-if="item.rank == 1">{{ item.regionName }}</span>
              <span v-if="item.rank == 2">{{ item.regionName }}</span>
              <span v-if="item.rank == 3">{{ item.regionName }}</span>
              <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                item.regionName
              }}</span>
            </div>
          </Tooltip>
          <div class="content-thirdly">
            <span class="thirdly">{{ item.standardsValue }}分</span>
          </div>
          <div class="content-fourthly">
            <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
            <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
            <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
            <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise }}</span>
            <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{ item.rankRise }}</span>
            <span class="plus color-sheng ml-sm" v-else>{{ item.rankRise }}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>
<style lang="less" scoped>
.synthesize-right {
  flex: 1;
  margin-left: 10px;
  background-color: var(--bg-sub-content);
  .synthesize-title {
    text-align: center;
    position: relative;
    padding-top: 20px;
    width: 100%;
    padding-bottom: 15px;
  }
  .synthesize-content {
    display: inline-block;
    width: 100%;
    height: 534px;
    overflow-y: scroll;
    ul {
      padding: 0 10px;
      width: 100%;
      overflow: hidden;
      li {
        display: flex;
        padding-top: 20px;
        align-items: center;
        .content-fourthly {
          display: inline-flex;
          font-size: 14px;
          position: relative;
        }
        div {
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 14px;
          position: relative;
        }

        .content-firstly {
          // width: 60px;
          flex: 1;
          margin-left: 10px;
        }
        .content-thirdly {
          // width: 90px;
          flex: 1;
        }
        .content-fourthly {
          // width: 90px;
          flex: 1;
        }
        .content-second {
          color: #fff;
          // width: 150px;
          flex: 1;

          img {
            vertical-align: middle;
          }

          span {
            // width: calc(100% - 80px);
            width: 75px;
            padding-left: 10px;
            display: inline-block;
            // text-align: center;
            vertical-align: middle;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .rankText {
            margin-left: 20px;
          }
        }

        .bg_color {
          min-width: 21px;
          min-height: 21px;
          text-align: center;
          font-weight: bold;
          color: #fff;
          font-size: 14px;
        }
        .firstly1 {
          background-color: #f1b700;
        }
        .firstly2 {
          background-color: #eb981b;
        }
        .firstly3 {
          background-color: #ae5b0a;
        }
        .firstly4 {
          background-color: var(--color-primary);
        }

        .thirdly {
          overflow: hidden;
          color: var(--color-primary);
        }
        .color-sheng {
          color: #0e8f0e;
          font-size: 14px;
        }
        .color-jiang {
          color: #bc3c19;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
<script>
export default {
  name: 'comprehensiveRanking',
  data() {
    return {};
  },

  props: {
    // 默认选中的区域
    rankData: {
      required: true,
      default() {
        return [];
      },
    },
    rankLoading: {
      type: Boolean,
    },
  },
  components: {
    titleContent: require('@/views/governanceevaluation/evaluationoverview/components/title-content.vue').default,
  },
};
</script>
