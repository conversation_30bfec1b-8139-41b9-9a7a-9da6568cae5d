<template>
  <div>
    <div class="breadcrumb-container">
      <span class="box"></span>
      <span class="title">
        <i>详细信息</i>
      </span>
    </div>
    <div class="relationship-content-list">
      <Timeline>
        <TimelineItem v-for="(item, index) in dataList" :key="item.type + index" v-if="item.length > 0">
          <div v-if="[''].includes(type)">
            <span v-for="(keys, index) in options[type].infoHeader" :key="index">
              <span class="pr20" v-if="Object.keys(keys)[0] === 'time'">{{ item[Object.keys(keys)[0]] }}</span>
              <span v-else>
                <span class="header-title">{{ Object.values(keys)[0] }}: </span>
                <span class="pr20" :style="{ color: keys.color }">{{ item[Object.keys(keys)[0]] }}</span>
              </span>
            </span>
          </div>
          <div class="about-layout" v-if="type == 'ipbd_face_capture_vid'">
            <div class="row" v-for="(i, $index) in item" :key="$index">
              <div class="img">
                <ui-image :src="i.sceneImg ? i.sceneImg : i.sceneImg" alt="图片" viewer :round="false"/>
              </div>
              <div class="rig">
                <p>设备编号：{{i.deviceId}}</p>
                <p>抓拍时间：{{i.absTime}}</p>
              </div>
            </div>
          </div>
          <div class="about-layout" v-else-if="type == 'ipbd_vehicle_capture_features'">
            <div class="row" v-for="(i, $index) in item" :key="$index">
              <div class="img">
                <ui-image :src="i.sceneImg ? i.sceneImg : i.sceneImg" alt="图片" viewer :round="false"/>
              </div>
              <div class="rig">
                <p>车牌号：{{i.plateNo}}</p>
                <p>时间：{{i.absTime}}</p>
              </div>
            </div>
          </div>
          <div v-else class="lsit-content-item">
            <div class="inlineb" v-for="item1 in item.list" :key="item1.name + 'qa' + index">
              <div class="inlineb pr30" v-for="keys1 in options[type].info" :key="item1.name + keys1">
                <span class="header-title1">{{ Object.values(keys1)[0] }}：</span>
                <span>{{ item1[Object.keys(keys1)[0]] }}</span>
              </div>
            </div>
          </div>
        </TimelineItem>
      </Timeline>
    </div>
    <!-- <div class="relationship-content-list">
      <Timeline>
        <TimelineItem v-for="(item, index) in data.listInfo" :key="item.type + index">
          <div>
            <span v-for="(keys, index) in options[type].infoHeader" :key="index">
              <span class="pr20" v-if="Object.keys(keys)[0] === 'time'">{{ item[Object.keys(keys)[0]] }}</span>
              <span v-else>
                <span class="header-title">{{ Object.values(keys)[0] }}: </span>
                <span class="pr20" :style="{ color: keys.color }">{{ item[Object.keys(keys)[0]] }}</span>
              </span>
            </span>
          </div>
          <div class="lsit-content-item">
            <div class="inlineb" v-for="item1 in item.list" :key="item1.name + 'qa' + index">
              <div class="inlineb pr30" v-for="keys1 in options[type].info" :key="item1.name + keys1">
                <span class="header-title1">{{ Object.values(keys1)[0] }}：</span>
                <span>{{ item1[Object.keys(keys1)[0]] }}</span>
              </div>
            </div>
          </div>
        </TimelineItem>
      </Timeline>
    </div> -->
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default() {
        return []
      }
    },
    dataList: {
      type: Array,
      default() {
        return []
      }
    },
    type: {
      type: String,
      default: 'tonghuoche'
    }
  },
  data() {
    return {
      options: {
        tonghuoche: {
          infoHeader: [{ time: '时间' }, { car: '列车', color: '#2c86f8' }, { chexiang: '车厢', color: 'rgba(0, 0, 0, 0.8)' }],
          info: [{ name: '姓名' }, { zuoweihao: '座位号' }, { shangche: '上车地' }, { mudi: '目的地' }, { jinzhan: '进站时间' }, { chuzhan: '出站时间' }]
        },
        ipbd_face_capture_vid: {
          infoHeader: [{ time: '时间' }, { car: '航班号', color: '#2c86f8' }],
          info: [{ name: '姓名' }, { shangche: '始发地' }, { mudi: '目的地' }, { jinzhan: '出发时间' }, { chuzhan: '到达时间' }]
        },
        tongqiche: {
          infoHeader: [{ time: '时间' }, { carId: '车牌号码', color: '#2c86f8' }],
          info: [{ name: '姓名' }, { shangche: '始发地' }, { mudi: '目的地' }, { jinzhan: '出发时间' }, { chuzhan: '到达时间' }]
        },
        tongjiudian: {
          infoHeader: [{ time: '时间' }, { jiudian: '酒店名称', color: '#2c86f8' }, { jiudiandizhi: '酒店地址', color: '#2c86f8' }],
          info: [{ name: '姓名' }, { zuoweihao: '房号' }, { jinzhan: '入住时间' }, { chuzhan: '退房时间' }]
        },
        tongjiacheng: {
          infoHeader: [{ time: '时间' }, { carId: '车牌号码', color: '#2c86f8' }],
          info: [{ name: '姓名' }, { zuoweihao: '位置' }, { jinzhan: '视频身份' }, { chuzhan: '抓拍地点' }]
        },
        tongwangba: {
          infoHeader: [{ time: '时间' }, { jiudian: '网吧名称', color: '#2c86f8' }, { jiudiandizhi: '网吧地址', color: '#2c86f8' }],
          info: [{ name: '姓名' }, { zuoweihao: '终端IP地址' }, { jinzhan: '开始时间' }, { chuzhan: '结束时间' }]
        },
        tongshigu: {
          infoHeader: [{ time: '时间' }],
          info: [{ name: '事故名称' }, { jiudiandizhi: '发生地点' }]
        },
        wifi: {
          infoHeader: [{ time: '时间' }],
          info: [{ name: 'Wi-Fi编号' }, { jinzhan: '感知时间' }, { chuzhan: '感知地点' }]
        },
        rfid: {
          infoHeader: [{ time: '时间' }],
          info: [{ name: 'RFID编号' }, { jinzhan: '感知时间' }, { chuzhan: '感知地点' }]
        },
        IMISS: {
          infoHeader: [{ time: '时间' }],
          info: [{ name: 'IMSI编号' }, { jinzhan: '感知时间' }, { chuzhan: '感知地点' }]
        }
      }
    }
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},

  methods: {}
}
</script>
<style lang="less" scoped>
.box {
  display: inline-block;
  width: 3px;
  height: 20px;
  background: #2b84e2;
}
.breadcrumb-container {
  padding-top: 16px;
  padding-bottom: 20px;
  .title {
    position: relative;
    display: inline-block;
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    top: -3px;
    padding-left: 8px;
  }
}
.relationship-content-list {
  max-height: 314px;
  overflow-y: auto;
  overflow-x: hidden;

  .header-title {
    font-size: 14px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
  }
  .lsit-content-item {
    margin-top: 10px;
    background: rgba(221, 225, 234, 0.3);
    border-radius: 4px;
    padding: 10px 10px 0 10px;
    & > div {
      padding-bottom: 10px;
    }
    .header-title1 {
      font-size: 12px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.8);
    }
  }
  // 时间轴样式
  /deep/ .ivu-timeline-item-head {
    width: 14px;
    height: 14px;
    background: #2c86f8;
    border: 3px solid #ffffff;
    left: -1px;
  }
}
.pl20 {
  padding-left: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pr6 {
  padding-right: 6px;
}
.pr30 {
  padding-right: 30px;
}
.inlineb {
  display: inline-block;
}

.about-layout {
  width: 100%;
  height: 80px;
  display: flex;
  justify-content: space-between;
  flex-flow: wrap;
  .row {
    width: 49%;
    height: 100%;
    background-color: #f9f9f9;
    display: flex;
    margin-top: 10px;
    .img {
      width: 80px;
      padding: 6px;
    }
    .rig {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding-left: 6px;
    }
  }
}
</style>
