<!--
    * @FileDescription: 无车牌分析
    * @Author: H
    * @Date: 2024/05/28
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-11 14:33:56
-->
<template>
  <div class="analyse-wrap">
    <div class="search_box">
      <div class="title">
        <p>无牌车分析</p>
      </div>
      <div class="form-box">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="80"
        >
          <FormItem label="处理状态:" prop="handleStatus">
            <Select v-model="formData.handleStatus" placeholder="请选择">
              <Option value="0">全部</Option>
              <Option value="1">未处理</Option>
              <Option value="2">无效</Option>
              <Option value="3">有效</Option>
            </Select>
          </FormItem>
          <FormItem label="分析类型:" prop="noPlateType">
            <Select v-model="formData.noPlateType" placeholder="请选择">
              <Option value="0">全部</Option>
              <Option
                v-for="item in filterPlateOcclusion"
                :key="item.dataKey"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="开始时间:" prop="st">
            <DatePicker
              v-model="formData.st"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间:" prop="et">
            <DatePicker
              v-model="formData.et"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="结束时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="通行设备:">
            <ul class="search_content">
              <li class="active-area-sele" @click="handleSelemodel">
                选择设备/已选({{ formData.deviceList.length }})
              </li>
            </ul>
          </FormItem>
          <FormItem label="车辆颜色:" prop="vehicleColor">
            <Select
              v-model="formData.vehicleColor"
              clearable
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in bodyColorList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆类型:" prop="vehicleType">
            <Select
              v-model="formData.vehicleType"
              filterable
              clearable
              placeholder="请选择车辆类型"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleTypeList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆品牌:" prop="vehicleBrand">
            <Select
              v-model="formData.vehicleBrand"
              filterable
              clearable
              placeholder="请选择"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleBrandLists"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆型号:" prop="vehicleSubBrand">
            <Input
              v-model="formData.vehicleSubBrand"
              placeholder="请输入"
              clearable
            ></Input>
          </FormItem>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div class="table_box">
      <div class="data-export">
        <Button
          class="mr"
          :type="queryParam.order == 0 ? 'primary' : 'default'"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button class="mr" @click.stop="exportShow = true" size="small">
          <ui-icon type="daoru" color="#2C86F8"></ui-icon>
          导出
        </Button>
        <export-box
          ref="exportbox"
          v-if="exportShow"
          :wholePage="true"
          @confirm="confirm"
          @cancel="hideExportModal"
        />
      </div>
      <div class="table_content">
        <div class="list-card" v-for="(item, index) in dataList" :key="index">
          <!-- 手工研判 -->
          <div class="custom-handle">
            <div class="current-plate-cover">
              {{ item.plateCover | commonFiltering(plateOcclusionList) }}
            </div>
            <ul class="handle-list">
              <li
                v-for="dict in filterPlateOcclusion"
                :key="dict.dataKey"
                :value="dict.dataKey"
                @click="handlePlate(item, dict.dataKey)"
              >
                {{ dict.dataValue }}
              </li>
            </ul>
          </div>
          <!-- 处理状态 -->
          <div
            :class="[
              'handle-status',
              setStatusClassName(item.noPlateHandle.handleStatus),
            ]"
          >
            {{ setStatusName(item.noPlateHandle.handleStatus) }}
          </div>
          <div class="handle-status-btns">
            <div
              v-show="[1, 2].includes(item.noPlateHandle.handleStatus)"
              @click="handleStatusFn(item, 3)"
            >
              设为有效
            </div>
            <div
              v-show="[1, 3].includes(item.noPlateHandle.handleStatus)"
              @click="handleStatusFn(item, 2)"
            >
              设为无效
            </div>
            <div v-show="[2, 3].includes(item.noPlateHandle.handleStatus)">
              <Poptip
                trigger="hover"
                transfer
                word-wrap
                @on-popper-show="showHistory(item)"
                popper-class="history-list-poptip"
              >
                历史处理
                <div slot="title">
                  <div class="block"></div>
                  <i>历史处理</i>
                </div>
                <div slot="content">
                  <ui-loading v-if="historyLoading"></ui-loading>
                  <template v-else>
                    <Timeline v-if="historyList.length">
                      <TimelineItem v-for="item in historyList" :key="item">
                        <div class="time">
                          <div class="timeContent">
                            <div>{{ item.handleTime }}</div>
                            <div>操作人：{{ item.creator }}</div>
                          </div>
                        </div>
                        <div class="content">
                          <div class="content1">
                            <div class="p">
                              <span>处理操作：</span>
                              <div>
                                <span
                                  >设为{{
                                    item.operation == 3 ? '"有效"' : '"无效"'
                                  }}</span
                                >
                              </div>
                            </div>
                            <div class="p">
                              <span>处理意见：</span>
                              <div>{{ item.remark || "--" }}</div>
                            </div>
                          </div>
                        </div>
                      </TimelineItem>
                    </Timeline>
                    <ui-empty v-else></ui-empty>
                  </template>
                </div>
              </Poptip>
            </div>
          </div>
          <div class="img-content">
            <img
              v-viewer
              :src="item.traitImg"
              alt=""
              @click="handleDetail(item, index)"
            />
            <!-- <div class="treatment-state">未处理</div> -->
          </div>
          <div class="bottom-info">
            <time>
              <Tooltip
                content="抓拍时间"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime || "--" }}
            </time>
            <p>
              <Tooltip
                content="抓拍地点"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{
                item.deviceName || "--"
              }}</span>
            </p>
          </div>
        </div>
      </div>
      <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[28, 56, 84, 112]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <!-- 选择品牌 -->
    <BrandModal ref="brandModal" @on-change="selectBrand" />
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />

    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(false, true)"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="warnModalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(true, false)"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import {
  getNoPlateVehicleList,
  setNoPlateStatus,
  handleNoPlateStatus,
  handleNoPlateStatusHistoryList,
  exportNoPlateData,
} from "@/api/modelMarket";
import { getVehicleBrandList } from "@/api/vehicleArchives";
import { getDateTime } from "@/util/modules/common";
import BrandModal from "@/components/ui-brand-modal.vue";
import ExportBox from "@/views/wisdom-cloud-search/search-center/components/export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
import { taskView } from "@/api/wisdom-cloud-search";
export default {
  components: {
    BrandModal,
    ExportBox,
    hlModal,
  },
  data() {
    return {
      formData: {
        handleStatus: "",
        noPlateType: "",
        vehicleColor: "",
        vehicleType: "",
        vehicleBrand: "",
        vehicleSubBrand: "",
        deviceList: [],
        st: "",
        et: "",
      },
      queryParam: {
        order: 1,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 28,
      },
      dataList: [],
      vehicleBrandLists: [],
      total: 0,
      timeUpDown: false,
      loading: false,
      selectDeviceList: [],
      ruleValidate: {},
      checkedLabels: [], // 已选择的标签
      historyList: [], // 历史处理列表
      historyLoading: false,
      exportShow: false, // 导出弹框
      modalShow: false, // 导出loading弹框
      warnModalShow: false, // 中断导出提示框
      downTaskId: "", // 下载任务
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0, // 预计时间
    };
  },
  computed: {
    ...mapGetters({
      plateOcclusionList: "dictionary/getPlateOcclusionList",
    }),
    filterPlateOcclusion() {
      // 目前只需要这4种
      return this.plateOcclusionList.filter((item) =>
        ["1", "2", "4", "6"].includes(item.dataKey)
      );
    },
  },
  async created() {
    window.addEventListener("click", this.hideExportModal);
    await this.getDictData();
    this.handleVehicleBrand();
    this.$nextTick(() => {
      this.formData.st = getDateTime(-6);
      this.formData.et = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
    });
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
    window.removeEventListener("click", this.hideExportModal);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 关闭导出框
     */
    hideExportModal() {
      this.exportShow = false;
    },

    /**
     * @description: 导出提示框
     * @param {boolean} modalShow 导出loading弹框
     * @param {boolean} warnModalShow 中断导出提示框
     */
    modalStatus(modalShow, warnModalShow) {
      this.modalShow = modalShow;
      this.warnModalShow = warnModalShow;
    },

    /**
     * @description: 中断下载
     */
    onOk() {
      this.modalStatus(false, false);
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },

    /**
     * @description: 轮询导出数据
     */
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },

    /**
     * @description: 确认导出
     * @param {object} param 导出配置
     */
    confirm(param) {
      let params = {
        ids: [],
        downloadPics: param.downloadPics,
        downloadSize: null,
        downNeed: true,
        ...this.formData,
        st: this.$dayjs(this.formData.st).format("YYYY-MM-DD HH:mm:ss"),
        et: this.$dayjs(this.formData.et).format("YYYY-MM-DD HH:mm:ss"),
        vehicleBrand: "",
        ...this.queryParam,
      };
      // type: 1导出选中数据
      if (param.type == "1") {
        params.ids = this.dataList.map((e) => e.recordId);
        if (!params.ids.length) {
          this.$Message.warning("当前页无数据！");
          return;
        }
      } else {
        params.downloadSize = param.downloadSize;
      }
      this.hideExportModal();
      this.modalShow = true;
      exportNoPlateData(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {
          // this.$refs.exportbox.handleEnd();
        });
    },
    // 查询
    handleSearch() {
      if (this.formData.st > this.formData.et) {
        this.$Message.wraning("结束时间不能大于开始时间！");
        return;
      }
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    queryList() {
      this.loading = true;
      this.dataList = [];
      let params = {
        ...this.formData,
        st: this.$dayjs(this.formData.st).format("YYYY-MM-DD HH:mm:ss"),
        et: this.$dayjs(this.formData.et).format("YYYY-MM-DD HH:mm:ss"),
        // deviceGbId:this.formData.deviceList,
        vehicleBrand: "",
        ...this.queryParam,
        ...this.pageInfo,
      };
      getNoPlateVehicleList(params)
        .then((res) => {
          this.dataList = res.data.entities;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置
    handleReset() {
      this.$refs.form.resetFields();
      this.pageInfo.pageNumber = 1;
      this.formData.st = getDateTime(-6);
      this.formData.et = this.$dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
      // 清空选择的设备
      this.$refs.selectDevice.removeAllHandle();
      this.queryList();
    },
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? 0 : 1;
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    handleVehicleBrand() {
      this.vehicleBrandLists = [];
      getVehicleBrandList("")
        .then((res) => {
          this.vehicleBrandLists = res.data;
        })
        .catch(() => {})
        .finally(() => {});
    },
    handleDetail(row, index) {
      console.log(row, index, "row, index");
    },
    /**
     * 选择设备
     */
    handleSelemodel() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.formData.deviceList = list.map((item) => item.deviceGbId);
    },
    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },
    /**
     * 车辆品牌已选择，返回数据
     */
    selectBrand(list) {
      this.formData.vehicleBrand = list;
    },
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    pageSizeChange(page) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = page;
      this.queryList();
    },

    /**
     * @description: 手动处理车牌遮挡
     * @param {object} item 单子信息
     * @param {string} status 要处理的状态，后端要number类型，字典里存的string
     */
    handlePlate(item, status) {
      let params = {
        absTime: item.absTime,
        recordId: item.recordId,
        noPlateType: Number(status),
      };
      setNoPlateStatus(params).then((res) => {
        if (res.code === 200) {
          this.$Message.success("修改成功");
          this.queryList();
        }
      });
    },

    /**
     * @description: 当前状态类名
     * @param {number} status 状态值
     */
    setStatusClassName(status) {
      return status == 2 ? "no-valid" : status == 3 ? "valid" : "no-handle";
    },

    /**
     * @description: 当前状态中文名
     * @param {number} status 状态值
     */
    setStatusName(status) {
      return status == 2 ? "无效" : status == 3 ? "有效" : "未处理";
    },

    /**
     * @description: 有效状态处理
     * @param {object} item 单子信息
     * @param {string} status 要处理的状态
     */
    handleStatusFn(item, status) {
      handleNoPlateStatus({
        absTime: item.absTime,
        recordId: item.recordId,
        handleStatus: status,
      }).then((res) => {
        if (res.code === 200) {
          this.$Message.success("修改成功");
          this.queryList();
        }
      });
    },

    /**
     * @description: 展示历史处理信息
     * @param {object} item 单子信息
     */
    showHistory(item) {
      this.historyLoading = true;
      handleNoPlateStatusHistoryList({
        absTime: item.absTime,
        recordId: item.recordId,
        handleStatus: item.noPlateHandle.handleStatus,
      })
        .then((res) => {
          this.historyList = res.data || [];
        })
        .finally(() => {
          this.historyLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
@import "../../style/index";
@import "../../style/vehicle";
.list-card {
  overflow: hidden;
  &:hover {
    border: 1px solid #2c86f8 !important;
    .handle-status-btns {
      bottom: 0;
      transition: 0.3s;
    }
  }
  .handle-status-btns {
    position: absolute;
    width: calc(~"100% + 1px"); // 隐藏右侧边框
    bottom: 0;
    z-index: 1;
    background: #2c86f8;
    color: #fff;
    height: 30px;
    padding: 5px 0;
    right: -1px;
    bottom: -30px;
    transition: 0.3s;
    display: flex;
    & > div {
      display: inline-block;
      flex: 1;
      text-align: center;
      cursor: pointer;
      border-right: 1px solid #d1cbcb;
    }
  }
  .handle-status {
    position: absolute;
    z-index: 1;
    border-radius: 4px;
    line-height: 18px;
    top: 6px;
    right: 6px;
    color: #fff;
    padding: 2px 5px;
    font-family: Microsoft YaHei;
    font-size: 12px;
    font-weight: normal;
  }
  .valid {
    background: #1faf81;
  }
  .no-valid {
    background: #78858e;
  }
  .no-handle {
    background: #f29f4c;
  }
}
.custom-handle {
  position: absolute;
  z-index: 1;
  left: 6px;
  top: 6px;
  color: #fff;
  .current-plate-cover {
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    border-radius: 4px;
    line-height: 18px;
  }
  &:hover {
    .handle-list {
      display: block;
    }
  }
  .handle-list {
    display: none;
    background: rgba(0, 0, 0, 0.7);
    li {
      line-height: 20px;
      &:hover {
        color: #2c86f8;
      }
    }
  }
  .current-plate-cover,
  li {
    padding: 2px 5px;
    font-family: Microsoft YaHei;
    font-size: 12px;
    font-weight: normal;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.history-list-poptip {
  .ivu-poptip-body {
    min-height: 200px;
    max-height: 300px;
    position: relative;
  }
}
</style>
