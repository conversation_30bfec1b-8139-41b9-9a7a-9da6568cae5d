<template>
  <ui-card title="异常行为" class="m-b20">
    <div slot="extra" class="btn-group">
      <Button
        v-for="(item, index) in timeList"
        :key="index"
        :type="item.value === daysNum ? 'primary' : 'default'"
        size="small"
        @click="() => (daysNum = item.value)"
        >{{ item.label }}
      </Button>
    </div>
    <div class="behaviour-content">
      <div class="bar-content">
        <div class="bar-item bar-day">
          <span class="num">{{ nightNum }}</span>
          <p class="bar" :style="{ height: nightNum + 'px' }"></p>
          <span>深夜时间</span>
        </div>
        <div class="bar-item bar-night">
          <span class="num">{{ schoolNum }}</span>
          <p class="bar" :style="{ height: schoolNum + 'px' }"></p>
          <span>上学时间</span>
        </div>
      </div>
      <PolarBarEchart :title="{}" class="polar-bar" :series="timeSlotSeries" />
      <ui-loading v-if="timeSlotLoading" />
    </div>
  </ui-card>
</template>
<script>
import PolarBarEchart from "@/components/echarts/polar-bar-echart";
import { faceTrajectoryAbnormalStatisticsByHour } from "@/api/monographic/juvenile.js";
import { getConfigDate } from "@/util/modules/common";
export default {
  components: { PolarBarEchart },
  props: {
    // 卡片标题
    title: {
      type: String,
      default: "",
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20,
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false,
    },
    //1实名档案/2路人档案/3车辆档案
    dataType: {
      type: Number,
      default: 1,
    },
    // 档案id
    archiveNo: {
      type: String,
      default: "",
    },
    // 档案id
    plateNo: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      timeSlotLoading: false,
      appearInLateNight: {},
      classTimeOutside: {},
      daysNum: 1,
      timeList: [
        { value: 1, label: "一天" },
        { value: 7, label: "一周" },
        { value: 30, label: "一月" },
      ],
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
    };
  },
  mounted() {
    this.behavioralJuvenils();
  },
  computed: {
    // 深夜时间
    nightNum() {
      let sum = 0;
      Object.keys(this.appearInLateNight).forEach((key) => {
        sum += this.appearInLateNight[key] || 0;
      });
      return sum;
    },
    // 上学时间
    schoolNum() {
      let sum = 0;
      Object.keys(this.classTimeOutside).forEach((key) => {
        sum += this.classTimeOutside[key] || 0;
      });
      return sum;
    },
  },
  watch: {
    daysNum: {
      handler() {
        this.behavioralJuvenils();
      },
    },
  },
  methods: {
    /**
     * 行为规律获取
     * @param type 异常行为类型: classTimeOutside 上学时间校外出现 | appearInLateNight 深夜出现
     */
    behavioralJuvenils(type = ["classTimeOutside", "appearInLateNight"]) {
      this.timeSlotLoading = true;
      let [startTime, endTime] = getConfigDate(-this.daysNum);
      let param = {
        abnormalTypes: type,
        idCardNo: this.archiveNo,
        endTime: endTime + " 23:59:59",
        startTime: startTime + " 00:00:00",
      };
      faceTrajectoryAbnormalStatisticsByHour(param)
        .then((res) => {
          const { appearInLateNight, classTimeOutside } = res.data;
          this.appearInLateNight = appearInLateNight;
          this.classTimeOutside = classTimeOutside;
          this.timeSlotSeries[0].data = [];
          for (let i = 0; i < 24; i++) {
            if (this.appearInLateNight[i]) {
              this.timeSlotSeries[0].data.push({
                name: `深夜时间-${i}点`,
                value: this.appearInLateNight[i],
                itemStyle: {
                  color: "#2C86F8",
                },
              });
              continue;
            }
            if (this.classTimeOutside[i]) {
              this.timeSlotSeries[0].data.push({
                name: `上学时间-${i}点`,
                value: this.classTimeOutside[i],
                itemStyle: {
                  color: "#EC9240",
                },
              });
              continue;
            }
            this.timeSlotSeries[0].data.push({
              name: ``,
              value: 0,
              itemStyle: {
                color: "#EC9240",
              },
            });
          }
        })
        .finally(() => {
          this.timeSlotLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.btn-group {
  padding-top: 10px;
  padding-right: 20px;
  button + button {
    margin-left: 10px;
  }
  .ivu-btn-small {
    border-color: #d3d7de;
    height: 24px;
    padding: 0 10px;
    color: rgba(0, 0, 0, 0.6);
    border-radius: 2px;
    &.ivu-btn-primary {
      color: #fff;
      border-color: #4597ff;
    }
  }
}
.behaviour-content {
  height: 100%;
  display: flex;
  align-items: flex-end;
  font-size: 16px;
  .bar-content {
    width: 320px;
    display: flex;
    justify-content: space-around;
    height: 100%;
    padding: 20px 30px;
    .bar-item {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      align-items: center;
      span {
        color: rgba(0, 0, 0, 0.6);
        margin-top: 6px;
      }
      .num {
        color: rgba(0, 0, 0, 0.9);
      }
      .bar {
        width: 16px;
        background: linear-gradient(180deg, #5bc0ff 0%, #2c86f8 100%);
        border-radius: 8px;
        transition: all 0.3s;
      }
      &.bar-night {
        .bar {
          background: linear-gradient(207deg, #f7b93d 0%, #ec9240 100%);
        }
      }
    }
  }
}
</style>
