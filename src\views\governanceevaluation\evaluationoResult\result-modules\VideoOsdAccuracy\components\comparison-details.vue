<template>
  <ui-modal title="比对详情" :footer-hide="true" v-model="visible" width="90%" @onCancel="visible = false">
    <div class="compare-result">
      <div class="result-wrapper">
        <div class="left-rusult">
          <div class="left-statistics-wrapper base-text-color">
            <div class="base-text-color mb-sm">
              检测批次：{{ activeRow.contrastExtDataBoList?.[0].detectDate || '暂无' }}
            </div>
            <statistical-quantity-box
              :loading="abnormalListLoading"
              :info-list="statisticalQuantityList"
              typeModel="left"
            ></statistical-quantity-box>
          </div>
          <div class="result-tabs mt-sm">
            <tag-view :list="tabList" @tagChange="changeMode" ref="tagView" class="tag-view mt-xs"></tag-view>
          </div>
          <div class="ui-table-wrapper">
            <ui-table
              ref="leftTable"
              class="ui-table auto-fill mt-sm mr-sm"
              :loading="leftTableLoading"
              :table-columns="tableColumns"
              :table-data="leftTableData"
              @selectTable="leftSelectTable"
            >
              <template #qualified="{ row }">
                <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
                  {{ qualifiedColorConfig[row.qualified].dataValue }}
                </Tag>
              </template>
            </ui-table>
          </div>
          <ui-page
            class="page menu-content-background"
            :page-data="leftPageData"
            @changePage="leftHandlePage"
            @changePageSize="leftHandlePageSize"
          >
          </ui-page>
        </div>
        <div class="right-rusult">
          <div class="right-statistics-wrapper base-text-color">
            <div class="base-text-color mb-sm">
              检测批次：{{ activeRow.contrastExtDataBoList?.[1].detectDate || '暂无' }}
            </div>
            <statistical-quantity-box
              :loading="abnormalListLoading"
              :info-list="statisticalQuantityList"
              typeModel="right"
            ></statistical-quantity-box>
          </div>
          <div class="result-tabs mt-sm">
            <!-- <Checkbox v-model="isAll" @on-change="changeCheckbox">全部</Checkbox> -->
            <Button type="primary" @click="onExport" :loading="exportLoading">
              <i class="icon-font icon-daochu mr-sm"></i>导出
            </Button>
          </div>
          <div class="ui-table-wrapper">
            <ui-table
              ref="rightTable"
              class="ui-table auto-fill mt-sm pl-sm"
              :loading="rightTableLoading"
              :table-columns="tableColumns"
              :table-data="rightTableData"
              @selectTable="rightSelectTable"
            >
              <template #qualified="{ row }">
                <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
                  {{ qualifiedColorConfig[row.qualified].dataValue }}
                </Tag>
              </template>
            </ui-table>
          </div>
          <ui-page
            class="page menu-content-background"
            :page-data="rightPageData"
            @changePage="rightHandlePage"
            @changePageSize="rightHandlePageSize"
          >
          </ui-page>
        </div>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export default {
  components: {
    TagView: require('@/components/tag-view.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    StatisticalQuantityBox: require('./statistical-quantity-box.vue').default,
  },
  mixins: [downLoadTips],
  props: {
    value: {},
    activeRow: {},
    isStatisticRow: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      stateInfo: {},
      abnormalListLoading: false,
      statisticalQuantityList: [
        {
          name: '检测总量',
          leftCount: 0,
          rightCount: 0,
          color: '#22C326',
          icon: 'icon-jianceshebeishuliang',
          leftKey: 'detectTotal',
          rightKey: 'detectTotal',
        },
        {
          name: '合格数量',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--color-primary)',
          icon: 'icon-xinxixiangtongshebei',
          leftKey: 'qualifiedQuantity',
          rightKey: 'qualifiedQuantity',
        },
        {
          name: '不合格数量',
          leftCount: 0,
          rightCount: 0,
          color: '#DD4826',
          icon: 'icon-xinxichayishebei',
          leftKey: 'unqualifiedQuantity',
          rightKey: 'unqualifiedQuantity',
        },
      ],
      currentTabIndex: 0,
      tabList: [
        {
          label: '两次不合格',
          value: 'twiceOfflineQuantity',
        },
        {
          label: '新增不合格',
          value: 'newOfflineQuantity',
        },
        {
          label: '恢复合格',
          value: 'restoreOnlineQuantity',
        },
      ],
      leftTableData: [],
      leftTableLoading: false,
      leftPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      rightPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        // {
        //   type: 'selection',
        //   align: 'center',
        //   width: 50,
        // },
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          title: this.global.filedEnum.deviceId,
          key: 'deviceId',
          minWidth: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.deviceName,
          key: 'deviceName',
          minWidth: 200,
          ellipsis: true,
          tooltip: true,
        },
        {
          title: '检测结果',
          key: 'qualified',
          slot: 'qualified',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
        },
      ],
      rightTableData: [],
      rightTableLoading: false,
      leftSelection: [],
      rightSelection: [],
      exportLoading: false,
      isAll: false,
      initTime: '',
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    activeRow: {
      handler(obj) {
        this.setStatisticalQuantityList();
        this.tabList.forEach((item) => {
          item.label = `${item.label}(${obj[item.value] || 0})`;
        });
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    this.initTime = this.$util.common.formatDate(new Date(), 'yyyy-MM');
    this.visible = true;
    this.initLeftList();
    this.initRightList();
  },
  async mounted() {},
  methods: {
    changeCheckbox(val) {
      this.rightTableData.map((item) => {
        this.$set(item, '_disabled', val);
      });
      this.leftTableData.map((item) => {
        this.$set(item, '_disabled', val);
      });
    },
    // 处理 统计总量
    setStatisticalQuantityList() {
      let { contrastExtDataBoList } = this.activeRow;
      this.statisticalQuantityList.forEach((item) => {
        item.leftCount = contrastExtDataBoList?.[0]?.[item.leftKey] || 0;
        item.rightCount = contrastExtDataBoList?.[1]?.[item.rightKey] || 0;
      });
    },
    // 导出
    async onExport() {
      try {
        let { batchId, contrastType } = this.activeRow.contrastExtDataBoList?.[0] || {};
        if (!batchId) return;
        this.exportLoading = true;
        let { indexId, statisticType, regionCode, orgCode, examineTime } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: statisticType,
          yearAndMonth: examineTime || this.initTime,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: {
            contrastType: contrastType,
            type: this.currentTabIndex + 1, // 1两次离线 2新增离线 3恢复在线
            contrastDetectDetailId: this.activeRow.id,
          },
        };
        this.$_openDownloadTip();
        let res = await this.$http.post(evaluationoverview.exportContrastResultDetail, data);
        this.$util.common.transformBlob(res.data.data);
      } catch (e) {
        console.log(e);
      } finally {
        this.exportLoading = false;
      }
    },
    leftSelectTable(selection) {
      this.leftSelection = selection;
    },
    rightSelectTable(selection) {
      this.rightSelection = selection;
    },
    async getTableData(str) {
      try {
        let { batchId, contrastType } = this.activeRow.contrastExtDataBoList?.[str === 'left' ? 0 : 1] || {};
        if (!batchId) return {};
        let { indexId, statisticType, regionCode, orgCode, examineTime } = this.$route.query;
        let data = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: statisticType,
          yearAndMonth: examineTime || this.initTime,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: {
            contrastType: contrastType,
            type: this.currentTabIndex + 1, // 1两次离线 2新增离线 3恢复在线
            contrastDetectDetailId: this.activeRow.id,
          },
          pageNumber: str === 'left' ? this.leftPageData.pageNum : this.rightPageData.pageNum,
          pageSize: str === 'left' ? this.leftPageData.pageSize : this.rightPageData.pageSize,
        };
        let res = await this.$http.post(evaluationoverview.getContrastResultDetail, data);
        if (this.isAll && res?.data?.data?.entities.length) {
          res.data.data.entities.map((item) => {
            item._disabled = true;
          });
        }
        return res;
      } catch (e) {
        return {};
      }
    },

    async initLeftList() {
      try {
        this.leftTableLoading = true;
        this.leftTableData = [];
        this.leftPageData.totalCount = 0;
        let res = await this.getTableData('left');
        this.leftPageData.totalCount = res?.data?.data?.total || 0;
        this.leftTableData = res?.data?.data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.leftTableLoading = false;
      }
    },
    async initRightList() {
      try {
        this.rightTableLoading = true;
        this.rightTableData = [];
        this.rightPageData.totalCount = 0;
        let res = await this.getTableData('right');
        this.rightPageData.totalCount = res?.data?.data?.total || 0;
        this.rightTableData = res?.data?.data?.entities || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.rightTableLoading = false;
      }
    },
    leftHandlePage(val) {
      this.leftPageData.pageNum = val;
      this.initLeftList();
    },
    leftHandlePageSize(val) {
      this.leftPageData.pageNum = 1;
      this.leftPageData.pageSize = val;
      this.initLeftList();
    },
    rightHandlePage(val) {
      this.rightPageData.pageNum = val;
      this.initRightList();
    },
    rightHandlePageSize(val) {
      this.rightPageData.pageNum = 1;
      this.rightPageData.pageSize = val;
      this.initRightList();
    },
    changeMode(index) {
      if (this.currentTabIndex === index) return;
      this.currentTabIndex = index;
      this.rightPageData.pageNum = 1;
      this.leftPageData.pageNum = 1;
      this.initLeftList();
      this.initRightList();
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  //height: 800px;
  overflow: hidden;
}
.compare-result {
  position: relative;
  .result-wrapper {
    display: flex;
    .result-tabs {
      padding: 10px;
      height: 48px;
      background: var(--bg-sub-content);
      display: flex;
      align-items: center;
    }
    .ui-table-wrapper {
      position: relative;
      height: 500px;
      .ui-table {
        height: 100%;
      }
    }
    .left-rusult {
      max-width: 50%;
      flex: 1;
      .left-statistics-wrapper {
        padding-right: 10px;
      }
    }
    .right-rusult {
      max-width: 50%;
      flex: 1;
      .result-tabs {
        justify-content: flex-end;
      }
      .right-statistics-wrapper {
        padding-left: 10px;
        border-left: 1px solid var(--devider-line);
      }
      .ui-table-wrapper {
        padding-left: 10px;
        border-left: 1px solid var(--devider-line);
      }
    }
  }
}
</style>
