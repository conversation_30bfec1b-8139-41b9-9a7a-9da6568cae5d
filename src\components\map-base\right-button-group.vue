<template>
  <div class="button-group">
    <div>
      <i class="icon-font icon-guiwei" @click="pointMap('homing')"></i>
    </div>
    <div>
      <i class="icon-font icon-suoxiao" @click="pointMap('narrow')"></i>
    </div>
    <div>
      <i class="icon-font icon-fangda1" @click="pointMap('enlarge')"></i>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
    pointMap(type) {
      this.$emit('pointMap', type);
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .button-group {
    > div {
      background-color: #ffffff;
      border: 0;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
      .icon-font {
        color: #888888;
        font-size: 18px;
      }
      .icon-suoxiao {
        border-bottom: 1px solid #d3d7de;
      }
    }
  }
}
.button-group {
  position: absolute;
  right: 30px;
  top: 382px;
  > div {
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #092e68;
    border: 1px solid #2967c8;
    .icon-font {
      color: #f5f5f5;
      font-size: 18px;
    }
  }
  > div:first-child {
    margin-bottom: 10px;
  }
  > div:last-child {
    border-top: none;
  }
}
</style>
