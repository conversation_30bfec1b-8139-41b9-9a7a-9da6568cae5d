<template>
  <!-- 人脸卡口在线率 -->
  <div class="face-online" ref="contentScroll">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="true" :isIconBg="true"> </statistics>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div class="export">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="search-wrapper">
        <ui-label class="inline" label="设备编码" :width="70">
          <Input class="width-md" placeholder="请输入设备编码" v-model="searchData.deviceId"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="设备名称" :width="70">
          <Input class="width-md" placeholder="请输入设备名称" v-model="searchData.deviceName"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.sbdwlx" :width="100">
          <Select
            class="width-sm"
            v-model="searchData.sbdwlx"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
            clearable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" :label="global.filedEnum.phyStatus" :width="70">
          <Select v-model="searchData.outcome" clearable placeholder="请选择" class="input-width">
            <Option :value="1">在线</Option>
            <Option :value="2">离线</Option>
          </Select>
        </ui-label>
        <ui-label :width="0" class="inline ml-lg" label="">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
        </ui-label>
      </div>
      <div class="tableList">
        <slot name="search"></slot>
        <div class="left-div">
          <ui-table
            class="ui-table"
            :maxHeight="contentClientHeight"
            :loading="loading"
            :table-columns="tableColumns"
            :table-data="tableData"
            ref="table"
          >
            <template #description="{ row }">
              <span :class="row.outcome == '1' ? 'sucess' : 'error'">{{ row.description }}</span>
            </template>
            <template #outcome="{ row }">
              <span v-if="row.outcome == '1'" :class="row.outcome == '1' ? 'sucess' : 'error'">{{
                row.qualifiedNum
              }}</span>
              <span v-if="row.outcome == '2'" :class="row.outcome == '1' ? 'sucess' : 'error'">{{
                row.unqualifiedNum
              }}</span>
            </template>
            <template #tagNames="{ row }">
              <tags-more :tag-list="row.tagList || []"></tags-more>
            </template>
            <template #option="{ row }">
              <ui-btn-tip
                class="mr-md"
                icon="icon-chakanjietu"
                content="查看抓拍图片"
                @click.native="checkReason(row)"
              ></ui-btn-tip>
              <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addTags(row)"></ui-btn-tip>
            </template>
          </ui-table>
          <ui-page
            class="page"
            :page-data="searchData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
      </div>
    </div>
    <face-pictute
      v-model="pictureShow"
      v-if="pictureShow"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
      title="人脸抓拍图片"
    ></face-pictute>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="deviceTagData"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>

<style lang="less" scoped>
.face-online {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: calc(100% - 340px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 15px 13px;

      .statistics-ul {
        width: calc(100% - 340px);
        height: 100%;
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-start;
        li {
          position: relative;
        }
        .icon-budabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
        .icon-dabiao {
          position: absolute;
          top: 192px;
          right: 15px;
          line-height: 0;
          font-size: 14px;
          cursor: default;
          display: inline;
        }
      }
    }

    .information-ranking {
      width: 340px;
      background: var(--bg-sub-content);
      height: 100%;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .input-width {
        width: 176px;
      }
    }
    .tableList {
      height: 100%;
      width: 100%;
      background: var(--bg-content);
      .left-div {
        position: relative;
        width: 100%;
        min-height: 1px;
      }
      .ui-table {
        width: 100%;
      }
      .page {
        padding-right: 0;
      }
      .ui-table-scroll-nodata {
        /deep/ .ivu-table-tip {
          overflow-x: auto;
        }
      }
    }
  }
  .sucess {
    color: @color-success;
  }
  .error {
    color: @color-failed;
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { mapGetters, mapActions } from 'vuex';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';
import taganalysis from '@/config/api/taganalysis';
export default {
  mixins: [downLoadTips],
  name: 'face-online',
  data() {
    return {
      rankLoading: false,
      statisticsList: [
        {
          name: '人脸卡口总量',
          value: 0,
          icon: 'icon-renliankakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          textColor: 'color1',
          key: 'total',
        },
        {
          name: '实际检测设备数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
          key: 'deviceDataTotal',
        },
        {
          name: '在线设备',
          value: 0,
          icon: 'icon-zaixianshebei',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          textColor: 'color9',
          key: 'passDeviceDataTotal',
        },
        {
          name: '离线设备',
          value: 0,
          icon: 'icon-lixianshebei',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'notPassDeviceDataTotal',
        },
        {
          name: '人脸卡口在线率',
          value: 0,
          icon: 'icon-renliankakoulianwangshuai2',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          textColor: 'color3',
          type: 'percentage',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId', minWidth: 100 },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName', minWidth: 100 },
        { title: '所属单位', key: 'orgName' },
        { title: '点位类型', key: 'sbdwlxText' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr' },
        { title: '抓拍数量', key: 'outcome', slot: 'outcome' },
        { title: '设备状态', key: 'description', slot: 'description' },
        {
          minWidth: 150,
          title: '设备标签',
          slot: 'tagNames',
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],

      exportLoading: false,
      rankData: [],
      paramsList: {},
      statisticalList: {},
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      searchData: {
        deviceId: '',
        deviceName: '',
        sbdwlx: '',
        outcome: '',
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      deviceInfoId: '',
      reasonLoading: false,
      pictureShow: false,
      deviceIdNum: '',
      contentClientHeight: 0,
      customSearch: false,
      chooseOne: {
        tagList: [],
      },
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
    };
  },
  async mounted() {
    this.getTagList();
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 165 * proportion : 0;
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
      getTagList: 'governanceevaluation/getTagList',
    }),
    addTags(row) {
      this.chooseOne = row;
      this.customSearch = true;
      this.customizeAction = {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      };
      let tagList = row.tagList || [];
      this.defaultCheckedList = tagList.map((item) => {
        return item.tagId;
      });
    },
    async confirmFilter(val) {
      const tagIds = val.map((item) => {
        return item.tagId;
      });
      const params = {
        deviceId: this.chooseOne.deviceId,
        tagIds: tagIds,
      };
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.customSearch = false;
        this.getTableData();
      } catch (err) {
        console.log(err);
      }
    },
    search() {
      this.searchData.pageNumber = 20;
      this.searchData.pageNum = 1;
      this.getTableData();
    },

    async getReason() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.deviceIdNum,
          },
          pageSize: this.reasonPage.pageSize,
          pageNumber: this.reasonPage.pageNum,
        };
        this.reasonLoading = true;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getSecondaryPopUpData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params)
        const datas = res.data.data;
        this.reasonTableData = datas.entities || [];
        this.reasonPage.totalCount = datas.total;
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    checkReason(row) {
      this.deviceIdNum = row.deviceId;
      this.getReason(row);
      this.pictureShow = true;
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    async getTableData() {
      try {
        this.loading = true;
        this.tableData = [];
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            sbdwlx: this.searchData.sbdwlx,
            outcome: this.searchData.outcome,
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        this.copySearchDataMx(this.searchData);
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        // let res = await this.$http.post(evaluationoverview.getDetailData, params)
        const datas = res.data.data;
        this.tableData = datas.entities || [];
        this.searchData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableData();
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            deviceId: this.searchData.deviceId,
            deviceName: this.searchData.deviceName,
            sbdwlx: this.searchData.sbdwlx,
            outcome: this.searchData.outcome,
          },
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getTableData(); //表格
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    facePictute: require('./components/face-pictute.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    statistics: require('@/components/icon-statistics').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
