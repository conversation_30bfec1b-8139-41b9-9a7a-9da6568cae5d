<template>
  <ui-modal width="450" v-model="modalShow" title="保存" class="relation-graph-save" @onOk="onOk">
    <Form ref="formCustom" :model="formCustom" :label-width="80">
      <FormItem label="名称：" prop="name">
        <Input v-model.trim="formCustom.name"></Input>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    graphName: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      modalShow: false,
      formCustom: { name: '' }
    }
  },
  computed: {},
  watch: {
    modalShow(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.modalShow = val
    },
    graphName(val) {
      this.formCustom.name = val
    },
  },
  filter: {},
  created() {},
  mounted() {
    this.formCustom.name = this.$route.query.name
  },
  methods: {
    onOk() {
      if (!this.formCustom.name || this.formCustom.name == '') {
        this.$Message.warning('请输入画布名称')
        return
      }

      this.$emit('graphSave', this.formCustom.name)
    }
  }
}
</script>
<style lang="less" scoped>
.relation-graph-save {
  /deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
    min-height: 0;
  }
  /deep/ .ivu-form-item {
    margin-bottom: 0px;
  }
}
</style>
