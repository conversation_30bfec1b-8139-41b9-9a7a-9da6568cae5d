<template>
  <choose-device
    ref="chooseDevice"
    :is-allow-close="isAllowClose"
    :search-conditions="selectDeviceParams"
    :table-columns="leftTableColumns"
    :load-data="tableData"
    :selected-list="formValidate.deviceIds"
    :check-device-flag="checkDeviceFlag"
    :save-loading="saveLoading"
    @getOrgCode="getOrgCode"
    @getDeviceIdList="getDeviceIdList"
  >
    <template #search-header>
      <ui-label class="inline right-margin mb-lg" label="设备编码" :width="65">
        <Input
          v-model="searchData.deviceId"
          class="input-width"
          placeholder="请输入设备编码"
          @on-blur="changeBlur"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="设备名称" :width="65">
        <Input
          v-model="searchData.deviceName"
          class="input-width"
          placeholder="请输入设备名称"
          clearable
          @on-blur="changeBlur"
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbdwlx" :width="95">
        <Select class="input-width" v-model="searchData.sbdwlx" placeholder="请选择" clearable @on-change="changeBlur">
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbgnlx" :width="110">
        <Select class="input-width" v-model="searchData.sbgnlx" placeholder="请选择" clearable @on-change="changeBlur">
          <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">
            {{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="检测状态" :width="65">
        <Select
          @on-change="changeBlur"
          class="select-input-width"
          v-model="searchData.checkStatuses"
          multiple
          placeholder="请选择检测状态"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in checkStatus" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="是否检测" :width="65">
        <Select
          @on-change="changeBlur"
          class="input-width"
          v-model="searchData.isCheck"
          placeholder="请选择"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择</Option>
          <Option value="1">是</Option>
          <Option value="0">否</Option>
        </Select>
      </ui-label>
      <!-- 基础信息状态 -->
      <ui-label class="inline right-margin mb-lg" label="基础信息状态" :width="93">
        <Select
          :disabled="searchData.isCheck != '1'"
          @on-change="changeBlur"
          class="width-md leftSelect"
          v-model="searchData.baseCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择</Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          @on-change="changeBlur"
          :disabled="searchData.baseCheckStatus != '1'"
          class="width-md rightSelect"
          v-model="searchData.baseErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in baseErrorMessageList" :key="item + index">{{ item }} </Option>
        </Select>
      </ui-label>
      <!-- 视图数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视图数据状态" :width="93">
        <Select
          @on-change="changeBlur"
          :disabled="searchData.isCheck != '1'"
          class="width-md leftSelect"
          v-model="searchData.viewCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择</Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          @on-change="changeBlur"
          :disabled="searchData.viewCheckStatus != '1'"
          class="width-md rightSelect"
          v-model="searchData.viewErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in viewErrorMessageList" :key="item + index">{{ item }} </Option>
        </Select>
      </ui-label>
      <!-- 视频流数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视频流数据状态" :width="108">
        <Select
          @on-change="changeBlur"
          :disabled="searchData.isCheck != '1'"
          class="width-md leftSelect"
          v-model="searchData.videoCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择</Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          :disabled="searchData.videoCheckStatus != '1'"
          @on-change="changeBlur"
          class="width-md rightSelect"
          v-model="searchData.videoErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in videoErrorMessageList" :key="item + index">{{ item }} </Option>
        </Select>
      </ui-label>
      <div class="tag-content">
        <div class="data-list mr-lg">
          <span class="fl mt-md mr-sm">标签类型</span>
          <ui-select-tabs class="ui-select-tabs" :list="allTagList" @selectInfo="selectTabHandle" ref="uiSelectTabs" />
        </div>
        <!-- 摄像机采集区域 -->
        <ui-label class="inline mb-10 mr-lg" :label="`${global.filedEnum.sbcjqy}列表`" :width="140">
          <Button type="dashed" class="area-btn" @click="clickArea"
            >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length}个` }}</Button
          >
        </ui-label>
        <div class="inline mb-lg">
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button class="ml-sm" @click="resetSearch">重置</Button>
        </div>
      </div>
    </template>
    <!-- 经纬度保留8位小数-->
    <template #longitude="{ row }">
      <span>{{ row.longitude | filterLngLat }}</span>
    </template>
    <template #latitude="{ row }">
      <span>{{ row.latitude | filterLngLat }}</span>
    </template>
    <template #baseCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.baseCheckStatus == 1 ? '不合格' : row.baseCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
    <template #viewCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.viewCheckStatus == 1 ? '不合格' : row.viewCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
    <template #videoCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.videoCheckStatus == 1 ? '不合格' : row.videoCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
    <!--选择采集区域-->
    <area-select v-model="areaSelectModalVisible" @confirm="confirmArea" :checkedTreeDataList="checkedTreeData" />
  </choose-device>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import { mapActions, mapGetters } from 'vuex';

export default {
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    chooseDevice: require('@/components/choose-device/choose-device.vue').default,
    AreaSelect: require('@/components/area-select').default,
  },
  data() {
    return {
      saveLoading: false,
      areaSelectModalVisible: false, //采集区域 选择框
      checkedTreeData: [],
      isAllowClose: false,
      allTagList: [],
      errorList: [
        { name: '测试', id: '1' },
        { name: '测试2', id: '2' },
      ],
      baseErrorMessageList: [],
      viewErrorMessageList: [],
      videoErrorMessageList: [],
      selectDeviceParams: {},
      leftTableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: '检测状态',
          slot: 'checkStatusText',
          align: 'left',
          width: 90,
          minWidth: 200,
          tooltip: true,
        },
        {
          width: 100,
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
        },
        {
          width: 100,
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
        },
      ],
      formValidate: {
        objectType: '1', // 选择数据对象
        orgCodes: [],
        sbdwlxs: [],
        sbgnlxs: [],
        choiceType: '2',
        osdModel: '', // 检测方式
        isScreenshots: '0', // 视频截图
        tagIds: [], // 设备标签
        errorMessageList: [], //异常原因
        deviceIds: [],
        importantAreaIds: [], // 重点场所目录ID
        customAreaIds: [], // 自定义目录名称ID,
        customAreaNames: [], //
        detectionMode: ['1'], //检测方式
        indexDetectionMode: '', //指标取值
        detectionTimeOut: 10, // 完好率时间
      },
      tableData: (parameter, getTotal) => {
        let params = getTotal
          ? parameter
          : Object.assign(this.searchData, parameter, {
              queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
            });
        return this.$http.post(equipmentassets.queryDeviceInfoPageList, params).then((res) => {
          return res.data;
        });
      },
      checkDeviceFlag: '0',
      searchData: {
        checkStatuses: [],
        sbcjqyList: [],
        isImportant: '0',
      },
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
    }),
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
    await this.getTagList();
    this.queryErrorList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    init() {
      this.formValidate.deviceIds = [];
      this.handleStatusDict();
      this.$refs.chooseDevice.init();
    },
    handleStatusDict() {
      this.statusObj = {};
      this.checkStatus.forEach((item) => {
        this.statusObj[item.dataKey] = item.dataValue;
      });
    },
    searchHandle() {
      this.$refs.chooseDevice.search();
    },
    // 重置
    resetSearch() {
      this.searchData = {
        checkStatuses: [],
        sourceId: this.selectDeviceParams.sourceId,
        sbcjqyList: [],
      };
      this.$refs.chooseDevice.search();
      this.clearTagsStatus();
    },
    // 标签类型选中状态清除
    clearTagsStatus() {
      this.allTagList.forEach((item) => {
        if (item.select) {
          item.select = false;
        }
      });
    },
    // 选择标签
    selectTabHandle(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.searchHandle();
    },
    // 选择异常原因
    selectErrorHandle(infoList) {
      this.searchData.errorMessageList = infoList.map((row) => {
        return row.id;
      });
      this.searchHandle();
    },

    // 获取所有设备标签
    async getTagList() {
      let res = await this.$http.post(taganalysis.getDeviceTag, {
        isPage: false,
      });
      this.allTagList = res.data.data.map((item) => {
        return { name: item.tagName, id: item.tagId };
      });
    },
    // 选择组织机构
    getOrgCode(orgCodeList) {
      this.searchData = {
        checkStatuses: [],
        sbcjqyList: [],
        isImportant: '0',
      };
      this.selectDeviceParams.orgCodeList = orgCodeList;
      this.$refs.chooseDevice.search();
    },
    // 获取设备
    getDeviceIdList(data) {
      let params = {};
      if (data.checkDeviceFlag === '0' && data.chooseIds.length === 0) {
        this.$Message.warning('请选择设备');
        return false;
      }
      this.copySearchDataMx(this.searchData);
      Object.assign(params, this.searchData);
      this.formValidate = Object.assign(this.formValidate, this.searchData);
      this.formValidate.deviceIds = data.chooseIds;
      this.formValidate.ids = data.chooseIds;
      this.formValidate.checkDeviceFlag = data.checkDeviceFlag;
      this.totalCount = data.selectedDevNum;
      this.checkDeviceFlag = data.checkDeviceFlag;
      this.saveLoading = true;
      this.$http
        .post(equipmentassets.addImportDevices, this.formValidate)
        .then((res) => {
          this.saveLoading = false;
          this.$Message.success(res.data.msg);
          this.$emit('refreshList');
          this.isAllowClose = true;
        })
        .catch((err) => {
          console.log(err, 'err');
          this.saveLoading = false;
        });
    },
    // 请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.baseErrorMessageList = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.viewErrorMessageList = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.videoErrorMessageList = res3.data.data;
    },
    // 选择采集区域
    clickArea() {
      console.log(3234);
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.searchData.sbcjqyList || [];
    },
    // 采集区域确定
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    changeBlur() {
      if (this.searchData.isCheck == '0' || this.searchData.isCheck == '') {
        this.searchData.baseCheckStatus = '';
        this.searchData.viewCheckStatus = '';
        this.searchData.videoCheckStatus = '';
        this.searchData.baseErrorMessageList = [];
        this.searchData.viewErrorMessageList = [];
        this.searchData.videoErrorMessageList = [];
      }
      if (this.searchData.baseCheckStatus == '0' || this.searchData.baseCheckStatus == '') {
        this.searchData.baseErrorMessageList = [];
      }
      if (this.searchData.viewCheckStatus == '0' || this.searchData.viewCheckStatus == '') {
        this.searchData.viewErrorMessageList = [];
      }
      if (this.searchData.videoCheckStatus == '0' || this.searchData.videoCheckStatus == '') {
        this.searchData.videoErrorMessageList = [];
      }
      this.$nextTick(() => {
        this.$refs.chooseDevice.search();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.device-content {
  display: flex;
  min-height: 150px;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-top: -20px;
  .totalCount {
    color: #fff;
  }
}
.right-margin {
  margin-right: 15px;
}
.input-width {
  width: 140px;
}
.select-input-width {
  width: 163px;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
@{_deep} .ivu-form-item-content {
  .ivu-dropdown {
    .ivu-dropdown-rel {
      .ivu-select-selection {
        height: 32px;
        line-height: 32px;
        .select-content {
          height: 32px;
          line-height: 32px;
          .ivu-select-placeholder {
            height: 32px;
            line-height: 32px;
          }
          .ivu-select-selected-value {
            height: 32px;
            line-height: 32px;
          }
        }
      }
    }
  }
}
@{_deep} .api-organization-tree {
  width: 100% !important;
}
@{_deep} .ivu-dropdown {
  width: 100% !important;
}
@{_deep} .select-width {
  width: 100% !important;
}

@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-6px, 3px) scale(0.45) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    height: 0.135417rem;
    line-height: 0.135417rem;
    padding: 0 6px;
    font-size: 12px !important;
    color: #ffffff;
    border-bottom: none !important;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
    .ivu-icon-ios-arrow-forward {
      font-size: 12px !important;
      color: #239df9;
      transform: rotate(-90deg) scale(0.4);
      &:before {
        font-family: 'icon-font';
        content: '\e7a3';
      }
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      padding-bottom: 0 !important;
    }
  }
}

@{_deep} .el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep} .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.retest {
  @{_deep} .ivu-form-item-content {
    line-height: 0.166667rem !important;
  }
}
@{_deep} .ivu-modal .title {
  z-index: 15;
}
.data-object {
  @{_deep} .ivu-modal-body {
    padding: 20px 45px;
    min-height: 150px;
    height: 100%;
    max-height: 450px;
    margin-top: 20px;
    .ivu-form-item {
      .cascader {
        position: relative;
      }
      .tree {
        margin-right: 0;
      }
      .loading {
        width: 356px;
      }
    }
  }
}
@{_deep} .ivu-select-selection {
  & > div {
    white-space: nowrap;
  }
}
@{_deep} .ivu-select-multiple .ivu-tag {
  max-width: 68%;
}
.customize-filter {
  @{_deep} .ivu-modal-body {
    padding: 20px 45px;
    max-height: 100% !important;
    height: 100% !important;
  }
}
.statustag {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}

.leftSelect {
  width: 130px !important;
}
.rightSelect {
  width: 198px !important;
}
.tag-content {
  display: flex;
  justify-content: center;
  width: 100%;
  .data-list {
    flex: 1;
    margin-bottom: 15px;
    color: var(--color-content);
    display: flex;
    font-size: 14px;
    .ui-select-tabs {
      flex: 1;
    }
  }
}

.check-status {
  padding: 0 8px;
}

.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
