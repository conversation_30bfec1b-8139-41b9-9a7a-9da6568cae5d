<!--
    * @FileDescription: 非机动车
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="main-container">
        <div class="search-bar">
            <search-nonmotor ref="searchBar"
                @search="searchHandle"
                @reset="resetHandle" />
        </div>
        <div class="table-container">
            <div class="data-above" v-if="mapOnData">
                <Checkbox @on-change="checkAllHandler" v-model="checkAll">全选</Checkbox>
                <Button @click="dataAboveMapHandler">
                    <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
                    数据上图
                </Button>
            </div>
            <div class="btn-list">
                <Button class="mr" @click="handleSort('absTime')" size="small">
                    <Icon type="md-arrow-round-down" v-if="!timeUpDown"/> 
                    <Icon type="md-arrow-round-up" v-else/>
                    时间排序
                </Button>
            </div>
            <div class="table-content">
                <div class="list-card box-1" v-for="(item, index) in dataList" :key="index" :class="{ checked: item.isChecked }">
                    <div class="collection paddingIcon">
                        <div class="bg"></div>
                        <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
                        <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
                    </div>
                    <Checkbox class="check-box" v-if="mapOnData" v-model="item.isChecked" @on-change="e => checkHandler(e, index)"></Checkbox>
                     <p class="img-content">
                        <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
                        <ui-image :src="item.traitImg" alt="动态库" @click.native="handleDetail(item, index)" />
                        <!-- <img :src="item.traitImg" alt="" v-viewer /> -->
                    </p>
                    <div class="bottom-info">
                        <time>
                            <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                <i class="iconfont icon-time"></i>
                            </Tooltip>
                            {{ item.absTime }}
                        </time>
                        <p>
                            <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                <i class="iconfont icon-location"></i>
                            </Tooltip>
                            <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
                        </p>
                    </div>
                    <!-- <div class="operate-bar">
                        <p class="operate-content">
                            <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
                            <ui-btn-tip content="分析" icon="icon-fenxi" />
                            <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
                        </p>
                    </div> -->
                </div>
                <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
            </div>
            <ui-empty v-if="dataList.length === 0"></ui-empty>
            <ui-loading v-if="listLoading"></ui-loading>
            <!-- 分页 -->
            <ui-page :current="pageInfo.pageNumber" 
                :total="total" 
                countTotal
                :page-size="pageInfo.pageSize" 
                :page-size-opts="[27, 54, 81, 108]"
                @pageChange="pageChange" 
                @pageSizeChange="pageSizeChange">
            </ui-page>
        </div>
        <details-modal 
            v-show="humanShow" 
            ref='nonmotor'
            @prePage="prePage"
            @nextPage="nextPage"  
            @close="humanShow = false"></details-modal>
    </div>
</template>

<script>
import detailsModal from '@/components/detail/details-modal.vue'
import searchNonmotor from '../components/search-nonmotor.vue'; 
import { queryNonmotorRecordSearch } from '@/api/wisdom-cloud-search';
import { myMixins } from '../../components/mixin/index.js';
import { mapMutations, mapGetters } from 'vuex';
import { addCollection, deleteMyFavorite } from '@/api/user'
export default {
    name: '',
    components:{
        searchNonmotor,
        detailsModal 
    },
    props:{
        // 首页参数
        indexSearchData: {
            type: Object,
            default: () => { }
        },
        mapOnData: {
            type: Boolean,
            default: false
        }
    },
    mixins: [myMixins], //全局的mixin
    data () {
        return {
            dataList: [],
            listLoading: false,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize : 27
            },
            humanShow: false,
            queryParam: {},
            checkAll: false,
            timeUpDown: false
        }
    },
    watch:{
            
    },
    computed:{
        ...mapGetters({
            getMaxLayer: 'countCoverage/getMaxLayer',
            getNum: 'countCoverage/getNum',
            getNewAddLayer: 'countCoverage/getNewAddLayer',
            getListNum: 'countCoverage/getListNum',
        }), 
    },
    activated() {
        if(this.$route.query.deviceInfo){
            this.$nextTick(() =>{
                let deviceInfo = JSON.parse(this.$route.query.deviceInfo)
                this.$refs.searchBar.selectData([{...deviceInfo, select: true}])
            })
        }
        this.$nextTick(() =>{
            if(this.queryParam.keyWords || (this.queryParam.features && this.queryParam.features.length > 0)){
                this.queryList()
            }
        })
    },
    created() {
        this.$nextTick(() => {
            this.queryList()
        })
    },
    mounted(){
            
    },
    methods: {
        searchHandle() {
            this.pageInfo.pageNumber = 1;
            this.queryList();
        },
        queryList(page= 0) {
            let queryParam = this.$refs.searchBar.queryParam;
            queryParam = {  ...this.indexSearchData, ...this.pageInfo, ...queryParam, };
            if(queryParam.similarity &&  queryParam.similarity > 1){
                queryParam.similarity = queryParam.similarity /100
            }
            let deviceIds = queryParam.selectDeviceList.map(item => {
                return item.deviceId
            })
            this.queryParam = queryParam;
            this.dispTime()
            // 抓拍时段时间处理
            let { startDate, endDate } = this.timeTransition(this.queryParam.timeSlot, this.queryParam.timeSlotArr);
            this.queryParam.startDate = startDate;
            this.queryParam.endDate = endDate;
            this.queryParam.deviceIds = deviceIds;
            delete this.queryParam.selectDeviceList
            delete this.queryParam.urlList
            this.getDataList(page);
        },
        resetHandle() {
            this.pageInfo = {
                pageNumber: 1,
                pageSize : 27
            };
            let queryParam = this.$refs.searchBar.queryParam;
            
            this.queryParam = { ...queryParam, ...this.$parent.indexSearchData, ...this.pageInfo };
            this.dispTime()
            // 精准搜索处理
            if (this.$store.getters['common/getPageType'] == 1) {
                this.queryParam.similarity = this.queryParam.similarity / 100
            }
            this.checkAll = false;
            delete this.queryParam.selectDeviceList
            delete this.queryParam.urlList
            this.getDataList();
        },
        async getDataList(page = 0) {
            this.listLoading = true;
            queryNonmotorRecordSearch(this.queryParam)
            .then(res => {
                this.total = res.data.total;
                this.dataList = res.data.entities;
                if(page == 1) {
                    this.$refs.nonmotor.prePage(this.dataList)
                }else if(page == 2) {
                    this.$refs.nonmotor.nextPage(this.dataList)
                }
            })
            .finally(() => {
                this.listLoading = false
            })
        },
        // 详情
        handleDetail(row, index) {
            this.humanShow = true;
            this.$refs.nonmotor.init(row, this.dataList, index, 2, this.pageInfo.pageNumber)
        },
        collection(data, flag) {
            var param = {
                favoriteObjectId: data.recordId,
                favoriteObjectType: 17,
            }
            if(flag == 1){
                addCollection(param).then(res => {
                    this.$Message.success("收藏成功");
                    this.getDataList()
                })
            }else{
                deleteMyFavorite(param).then(res => {
                    this.$Message.success("取消收藏成功");
                    this.getDataList()
                })
            }
        },
        
        checkAllHandler(val) {
            this.dataList = this.dataList.map(e => {
                return {
                    ...e,
                    isChecked: val
                }
            })
        },
        checkHandler(e, i) {
            this.dataList[i].isChecked = e
            this.checkAll = this.dataList.filter(e => e.isChecked).length === this.dataList.length ? true : false
        },
        ...mapMutations({
            setNum:'countCoverage/setNum',
            setList: 'countCoverage/setList',
        }),
        dataAboveMapHandler() {
            const list = this.dataList.filter(e => e.isChecked);
            if (!list.length) {
                this.$Message.warning('请选择上图数据');
                return
            }
            let seleNum = this.dataList.filter(e => e.isChecked);
            let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
            let newNumPoints = this.getNum.pointsNum + this.getNewAddLayer.pointsInLayer + seleNum.length; //点位
            if(Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
                this.$Message.warning('已达到图层最大创建数量')
                return
            }
            if(Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
                this.$Message.warning('已达到上图最大点位总量')
                return
            }
            let num = JSON.stringify(this.getListNum);
            this.setList(num++)
            this.setNum({'layerNum': newNumLayer, 'pointsNum': newNumPoints})
            seleNum.map(item => {
                item.delePoints = true;
                item.deleType = 'nonmotorVehicle';
            })
            this.$emit('dataAboveMapHandler', { type: 'nonmotorVehicle', list,  deleIdent: 'nonmotorVehicle-' + this.getListNum})
        },
        archivesPage() {

        },
        pageChange(size) {
            this.pageInfo.pageNumber = size;
            this.queryList()
        },
        pageSizeChange(size) {
            this.pageInfo.pageNumber = 1
            this.pageInfo.pageSize = size
            this.queryList()
        },
         /**
         * 首页调用方法
         */
        indexSearchHandle () {
            this.pageInfo.pageNumber = 1
            this.queryParam = {
                keyWords: this.indexSearchData.keyWords
            }
            this.timeAssig()
            this.indexSearchData.features = []
            this.$refs.searchBar.resetHandle()
        },
        /**
         * 上一个
         */
        prePage(pageNum) {
            if(pageNum < 1) {
                this.$Message.warning("已经是第一个了")
                return
            } else {
                this.pageInfo.pageNumber = pageNum;
                this.queryList(1)
            }
        },
        /**
         * 下一个
         */
        async nextPage(pageNum) {
            this.pageInfo.pageNumber = pageNum;
            let num = this.pageInfo.pageNumber;
            let size = this.pageInfo.pageSize;
            if(this.total <= num*size) {
                this.$Message.warning("已经是最后一个了")
                return
            }else{
                this.queryList(2)
            }
        },
    }
}
</script>

<style lang='less' scoped>
@import 'style/index';
.data-above {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}
.check-box {
    position: absolute;
    left: 4px;
    top: 4px;
    display: none;
    z-index: 2;
}
.box-1 {
    &:hover {
        border-color: #2c86f8;
        .check-box {
            display: inline-block;
        }
    }

}
.table-content{
    .checked{
        // border: 2px solid #2C86F8;
        // padding: 9px;
        .check-box{
            display: inline-block;
        }
    }
}
.isChecked{
    // border-color: #2C86F8!important;
    .img-content{
        .check-box{
            display: inline-block;
        }
    }
}
</style>
