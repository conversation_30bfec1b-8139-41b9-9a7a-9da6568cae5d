<template>
  <div class="reporting-rate auto-fill">
    <div class="content auto-fill">
      <div class="evaluation-header">
        <div class="filtrate">
          <span class="f-16 color-filter ml-sm">{{ paramsData.regionName }}-有效上报率</span>
        </div>
        <div>
          <span class="evaluation-time">
            <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsData.examineTime || '未知' }}
          </span>
        </div>
      </div>
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <info-statics-list :statistics-list="statisticsList"></info-statics-list>
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">异常数据列表</span>
        </div>
        <div>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-chakanyichangxiangqing"
            content="不合格原因"
            @click.native="viewRecord(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <nonconformance
      ref="nonconformance"
      title="查看不合格原因"
      :tableColumns="reasonTableColumns"
      :tableData="reasonTableData"
      :reasonPage="reasonPage"
      :reasonLoading="reasonLoading"
      @handlePageChange="handlePageChange"
      @handlePageSizeChange="handlePageSizeChange"
    ></nonconformance>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { reporRateTableColumns, reasonTableColumns } from './staticfields';

export default {
  name: 'reporting-rate',
  mixins: [downLoadTips],
  data() {
    return {
      statisticsList: [
        {
          name: '视频监控建设总量',
          value: 80,
          icon: '',
          color: '#1bafd5',
          iconBgColor: 'linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%)',
          bgColor: 'rgba(16, 165, 170, 0.21)',
        },
        {
          name: '视频监控检测数量',
          value: 80,
          icon: '',
          color: '#b58e0f',
          iconBgColor: 'linear-gradient(360deg, #b58e0f 0%, #bb6603 100%)',
          bgColor: 'rgba(235, 178, 37, 0.21)',
        },
        {
          name: '检测合格设备数',
          value: 80,
          icon: '',
          color: '#4ba0f5',
          iconBgColor: 'linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%)',
          bgColor: 'rgba(15, 167, 245, 0.21)',
        },
        {
          name: '检测不合格设备数',
          value: 80,
          icon: '',
          color: '#f24e2c',
          iconBgColor: 'linear-gradient(180deg, #f24e2c 0%, #772c0a 100%)',
          bgColor: 'rgba(198, 64, 49, 0.21)',
        },
        {
          name: '有效上报率',
          value: '80%',
          type: 'percentage',
          icon: '',
          color: '#26d82c',
          iconBgColor: 'linear-gradient(360deg, #26d82c 0%, #127d0a 100%',
          bgColor: 'rgba(32, 182, 35, 0.21)',
        },
      ],
      statisticalList: {
        qualified: 1,
      },
      exportLoading: false,
      loading: false,
      examineTime: '',
      tableColumns: [],
      tableData: [{}],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      reasonLoading: false,
      reasonTableColumns: [],
      reasonTableData: [{}],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      paramsData: {},
    };
  },
  created() {
    this.paramsData = this.$route.query;
    this.tableColumns = reporRateTableColumns;
    this.reasonTableColumns = reasonTableColumns;
  },
  methods: {
    // 导出
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsData.indexId,
        batchId: this.paramsData.batchId,
        access: this.paramsData.access,
        displayType: this.paramsData.displayType,
        orgRegionCode: this.paramsData.orgRegionCode,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    async getTableList() {
      try {
        this.loading = true;
        let params = {
          orgRegionCode: this.paramsData.orgRegionCode,
          displayType: this.paramsData.displayType,
          indexId: this.paramsData.indexId,
          batchId: this.paramsData.batchId,
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
          customParameters: this.searchData,
        };
        let res = await this.$http.post(evaluationoverview.getPolyData, params);
        const { data = [] } = res.data;
        this.tableData = data.entities;
        this.searchData.totalCount = data.total;
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableList();
    },
    viewRecord() {
      this.$refs.nonconformance.init();
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      //   this.getTableData();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      //   this.getTableData();
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    InfoStaticsList: require('./components/info-statics-list.vue').default,
  },
};
</script>
<style lang="less" scoped>
.reporting-rate {
  background: @bg-blue-block;
  background-size: 100% 100%;
  height: 100%;
  padding: 0 15px;

  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
      cursor: pointer;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }

  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
}
</style>
