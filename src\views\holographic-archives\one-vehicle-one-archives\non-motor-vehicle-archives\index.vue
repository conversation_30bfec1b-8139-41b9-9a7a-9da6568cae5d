<template>
  <div class="container">
    <div class="person">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        type="non-motor-vehicle"
        @searchForm="searchForm"
        :searchText="'高级检索'"
      >
        <template #statistics>
          <!--          <statistics :data='statisticsData' :columns='statisticsColumns'></statistics>-->
        </template>
      </Search>
      <div class="card-content">
        <div v-for="(item, index) in dataList" :key="index" class="card-item">
          <UiListCard
            type="non-motor-vehicle"
            :showBar="false"
            :data="item"
            @archivesDetailHandle="archivesDetailHandle(item)"
            @collection="init"
            @on-search-image="onSearchImage"
            @on-archive="archivesDetailHandle"
          />
        </div>
        <ui-empty v-if="dataList.length < 1 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <!-- <ui-page :current="pageForm.pageNumber" :total="13" :page-size="pageForm.pageSize"></ui-page> -->
      <!-- 分页 -->
      <ui-page
        :showElevator="true"
        countTotal
        :current="pageInfo.pageNumber"
        :total="total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <search-image ref="searchImage" @on-submit="onSubmit"></search-image>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import Search from "@/views/holographic-archives/one-vehicle-one-archives/non-motor-vehicle-archives/components/search.vue";
import UiListCard from "@/components/ui-list-card";
import {
  getVehicleBaseInfoByplateNo,
  queryNonMotorVehicleList,
} from "@/api/vehicleArchives";
import Statistics from "@/views/holographic-archives/components/statistics.vue";
import SearchImage from "@/components/search-image/index.vue";

export default {
  components: { Search, UiListCard, Statistics, SearchImage },
  props: {},
  data() {
    return {
      loading: false,
      dataList: [],
      statisticsColumns: [
        {
          iconStyle: "icon-bg-blue",
          icon: "icon-danganwenwu",
          key: "nonmotorCount",
          desc: "档案总量",
          descStyle: "color-blue",
        },
        {
          iconStyle: "icon-bg-green",
          icon: "icon-shebeizichan",
          value: "",
          desc: "今日活跃",
          descStyle: "color-green",
        },
        {
          iconStyle: "icon-bg-yellow",
          icon: "icon-jinrixinzeng",
          value: "",
          desc: "今日新增",
          descStyle: "color-yellow",
        },
      ],
      pageForm: {},
      statisticsData: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
    };
  },
  async created() {
    await this.getDictData();
    this.init();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("common", ["setWisdomCloudSearchData"]),
    async onSearchImage(src) {
      if (!src) {
        return this.$Message.error("图片不存在");
      }
      let picData = {
        algorithmType: "4", //1人脸 2 车辆 3 人体 4 非机动车
        similarity: 75, // 相似度
      };
      this.$refs.searchImage.searchImage(picData, src);
    },
    onSubmit({ algorithmType, similarity, urlList }) {
      let params = {
        keyWords: "",
        algorithmType: algorithmType,
        urlList: [urlList],
      };
      this.$router.push({
        path: "/wisdom-cloud-search/cloud-default-page",
        query: {
          params: params,
          type: 2,
        },
      });
    },
    init() {
      this.loading = true;
      var param = Object.assign(this.pageInfo, this.pageForm);
      delete param.bodyTypeobj;
      var labelIds = [];
      if (param.labelIds && param.labelIds.length > 0) {
        param.labelIds.forEach((item) => {
          if (item && item != undefined) {
            labelIds.push(item.id);
          }
        });
        param.labelIds = labelIds;
      }
      queryNonMotorVehicleList(param)
        .then((res) => {
          this.dataList = res.data.entities;
          this.total = res.data.total;
          // 重新获取页码后回到顶部
          document.querySelector(".card-content").scrollTop = 0;
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 档案详情
    archivesDetailHandle(row) {
      let query = {
        archiveNo: JSON.stringify(row.archiveNo),
        plateNo: JSON.stringify(row.plateNo),
        source: "non-motor-vehicle",
        idcardNo: row.idcardNo,
      };
      const { href } = this.$router.resolve({
        name: "non-motor-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
    // 查询
    searchForm(form) {
      this.pageInfo.pageNumber = 1;
      this.pageForm = form;
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
// @import "~@/views/holographic-archives/style/page-hide.less";
.person {
  display: flex;
  flex: 1;
  height: 100%;
  flex-direction: column;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
