<template>
  <div class="face-content-wrapper">
    <div class="face-content warpper-box">
      <div
        v-for="(e, i) in faceList"
        :key="i"
        class="face-item box-item"
        :class="{ active: currentClickIndex == i }"
        @click="chooseMapItem($event, i)"
      >
        <div class="header">
          <div class="header-left">
            <span
              class="serialNumber"
              :class="{ activeNumber: currentClickIndex == i }"
            >
              <span>{{ i + 1 }}</span>
            </span>
            <!-- <span class="header-deviceid">{{ e.vid }}</span> -->
          </div>
          <div class="header-name" v-show-tips>
            {{ e.id || "--" }}
          </div>
          <operate-bar
            :list="e"
            :tabType="{ type: 5 }"
            @collection="collection($event, e, i)"
          ></operate-bar>
        </div>
        <div class="content">
          <div class="content-left">
            <img v-lazy="e.traitImg" alt="" />
          </div>
          <div class="content-right">
            <span class="ellipsis">
              <ui-icon type="time" :size="14"></ui-icon>
              <span>{{ e.absTime || "--" }}</span>
            </span>
            <span class="ellipsis">
              <ui-icon type="location" :size="14"></ui-icon>
              <span>{{ e.deviceName || "--" }}</span>
            </span>
            <!-- <span v-if="e.vid">
                            <ui-icon type="camera" :size="14"></ui-icon>
                            <span>{{ e.vid }}</span>
                        </span> -->
            <!-- <span v-if="e.score">{{ e.score }}%</span> -->
            <span v-if="e.idScore">
              <ui-icon type="wenjianxiangsidupeizhi" :size="14"></ui-icon>
              <span>相似度：</span>
              <span class="score">{{ `${e.idScore}%` || "--" }}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="!loading && !faceList.length" />
    <div class="general-search-footer">
      <ui-page
        :simple="true"
        :show-elevator="false"
        :show-sizer="false"
        :total="total"
        countTotal
        :current="pageInfo.pageNumber"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        size="small"
        show-total
      >
      </ui-page>
    </div>
  </div>
</template>

<script>
import { queryFaceRecordSearch } from "@/api/operationsOnTheMap";
import operateBar from "@/components/mapdom/operate-bar.vue";
export default {
  components: {
    operateBar,
  },
  props: {
    //搜索条件
    searchPrams: {
      type: Object,
      default: () => {},
    },
    // 当前点击顺序
    currentClickIndex: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      faceList: [],
      total: 0,
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      loading: false,
    };
  },
  watch: {
    currentClickIndex: {
      handler(newVal) {
        if (newVal > -1) {
          let list = document.querySelectorAll(".face-item");
          list[newVal].scrollIntoView(false);
        }
      },
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.queryFaceRecordSearch();
    },
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    // 收藏/取消收藏
    collection($event, item, index) {
      this.$set(this.faceList[index], "myFavorite", $event);
      // item.myFavorite = $event;
    },
    queryFaceRecordSearch() {
      const { pageInfo, searchPrams } = this;
      const params = { ...pageInfo, ...searchPrams, dataSource: "2" };
      params.similarity = params.similarity / 100;
      this.loading = true;
      queryFaceRecordSearch(params)
        .then((res) => {
          if (res.code === 200) {
            const {
              data: { entities = [], total = 0 },
            } = res;
            this.faceList = entities || [];

            this.total = total;
            this.$emit("mapResultHandler", this.faceList, 1);
          }
        })
        .catch(() => {
          this.faceList = [];
          this.$emit("mapResultHandler", []);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    pageChange(pageNumber) {
      this.pageInfo.pageNumber = pageNumber;
      this.queryFaceRecordSearch();
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/index";
.face-content {
  .score {
    color: #2c86f8 !important;
    font-weight: bold;
  }
  .header-name {
    width: 300px;
    font-size: 14px;
    font-weight: bold;
    color: #f29f4c;
  }
}
.face-footer {
  padding: 0 10px;
  display: flex;
  height: 56px;
  align-items: center;
  .pages {
    height: 56px;
    .total {
      font-size: 12px !important;
    }
  }
}
</style>
