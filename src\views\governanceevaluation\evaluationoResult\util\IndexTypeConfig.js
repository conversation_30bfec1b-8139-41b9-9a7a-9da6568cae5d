/**
 * 所有指标的详情组件配置
 *
 * 1.创建一个新的指标详情
 * 2.在result-modules文件中创建文件，已配置动态引入result-modules下的index.vue文件（不用手动注册组件）
 * 3.在此文件修改对应指标的对象中修改componentName属性值
 * componentName: 指标对应详情的组件名称【对应result-modules文件夹中的文件名称】
 */
const allIndexType = {
  '视图基础数据': [
    {
      'indexId': '1001',
      'indexName': '填报准确率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_ACCURACY',
      'componentName': 'BasicAccuracy',
    },
    {
      'indexId': '1002',
      'indexName': '建档率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_INPUT',
      'componentName': 'BasicInput',
    },
    {
      'indexId': '1003',
      'indexName': '全量目录完整率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_FULL_DIR',
    },
    {
      'indexId': '1004',
      'indexName': '数量达标率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_QUANTITY_STANDARD',
    },
    {
      'indexId': '1005',
      'indexName': '视频图像设备位置类型完整率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_EMPHASIS_LOCATION',
    },
    {
      'indexId': '1006',
      'indexName': '重点位置类型视频图像设备数量达标率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_EMPHASIS_QUANTITY',
    },
    {
      'indexId': '1008',
      'indexName': '视频图像采集区域数量达标率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_CJQY_QUANTITY_STANDARD',
      'componentName': 'BasicQuantityStandard',
    },
    {
      'indexId': '1009',
      'indexName': '设备百人占比率',
      'indexModule': '1',
      'indexModuleName': '视图基础数据',
      'indexType': 'BASIC_DEVICE_HUNDRED',
      'componentName': 'BasicDeviceHundred',
    },
  ],
  '视频流数据': [
    {
      'indexId': '4001',
      'indexName': '重点实时视频可调阅率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_PLAYING_ACCURACY',
      'componentName': 'VideoPlayingAccuracy',
    },
    {
      'indexId': '4002',
      'indexName': '重点历史视频可调阅率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_HISTORY_ACCURACY',
      'componentName': 'VideoHistoryPlayingAccuracy',
    },
    {
      'indexId': '4003',
      'indexName': '重点字幕标注合规率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_OSD_ACCURACY',
      'componentName': 'VideoOsdAccuracy',
    },
    {
      'indexId': '4004',
      'indexName': '重点时钟准确率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_CLOCK_ACCURACY',
      'componentName': 'VideoClockAccuracy',
    },
    {
      'indexId': '4006',
      'indexName': '普通时钟准确率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_GENERAL_CLOCK_ACCURACY',
      'componentName': 'VideoClockAccuracy',
    },
    {
      'indexId': '4007',
      'indexName': '普通字幕标注合规率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_GENERAL_OSD_ACCURACY',
      'componentName': 'VideoOsdAccuracy',
    },
    {
      'indexId': '4008',
      'indexName': '普通历史视频可调阅率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_GENERAL_HISTORY_ACCURACY',
      'componentName': 'VideoHistoryPlayingAccuracy',
    },
    {
      'indexId': '4009',
      'indexName': '普通实时视频可调阅率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_GENERAL_PLAYING_ACCURACY',
      'componentName': 'VideoPlayingAccuracy',
    },
    {
      'indexId': '4010',
      'indexName': '历史录像完整率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_HISTORY_COMPLETE_ACCURACY',
      'componentName': 'VideoHistoryComplete',
    },
    {
      'indexId': '4011',
      'indexName': '视频监控联网率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_DEVICE_CONNECT_INTERNET',
      // 'componentName': 'VideoDeviceConnectInternet'
    },
    {
      'indexId': '4012',
      'indexName': '视频流质量合格率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_QUALITY_PASS_RATE',
      'componentName': 'VideoQualityPassRate',
    },
    {
      'indexId': '4013',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '联网数量提升率',
      'indexType': 'VIDEO_NETWORKING_PROMOTION_RATE',
      'componentName': 'VideoMonitorGrowthRate',
    },
    {
      'indexId': '4014',
      'indexName': '可调阅提升率',
      'indexModule': '4',
      'indexType': 'VIDEO_READ_PROMOTION_RATE',
      'componentName': 'VideoReadPromotionRate',
    },
    {
      'indexId': '4015',
      'indexName': '字幕标注合规率提升率',
      'indexModule': '4',
      'indexType': 'VIDEO_OSD_ACCURACY_PROMOTION_RATE',
      'componentName': 'videoOsdAccuracyPromotionRate',
    },
    {
      'indexId': '4016',
      'indexName': '时钟准确率提升率',
      'indexModule': '4',
      'indexType': 'VIDEO_CLOCK_ACCURACY_PROMOTION_RATE',
      'componentName': 'videoClockAccuracyPromotionRate',
    },
    {
      'indexId': '4017',
      'indexName': '视频监控在线率提升',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_MONITOR_ONLINE_RATE_PROMOTE',
    },
    {
      'indexId': '4018',
      'indexName': '视频监控有效报送数量达标率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_VALID_SUBMIT_QUANTITY',
    },
    {
      'indexId': '4019',
      'indexName': '视频监控填报准确率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_ACCURACY',
      'componentName': 'BasicAccuracy',
    },
    {
      'indexId': '4020',
      'indexName': '重点历史录像完整率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN',
      'componentName': 'VideoHistoryComplete',
    },
    {
      'indexId': '4021',
      'indexName': '重点指挥图像在线率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN',
      'componentName': 'VideoCommandImageOnlineRate',
    },
    {
      'indexId': '4022',
      'indexName': '视频监控资产匹配率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_MONITOR_ASSET_MATCH_RATE_SICHUAN',
    },
    {
      'indexId': '4023',
      'indexName': '视频监控位置类型完整率',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_EMPHASIS_LOCATION',
    },
    {
      'indexId': '4024',
      'indexName': '视频流质量合格率（人工复核）',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexType': 'VIDEO_QUALITY_PASS_RATE_RECHECK',
      'componentName': 'VideoQualityPassRate',
    },
    {
      'indexId': '4025',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '视频监控数量达标率',
      'indexType': 'VIDEO_QUANTITY_STANDARD',
      'componentName': 'ValidSubmitQuantity',
    },
    {
      'indexId': '4026',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '（重点）字幕标注合规性与时钟准确性',
      'indexType': 'VIDEO_OSD_CLOCK_ACCURACY',
      'componentName': 'VideoOsdClockAccuracy',
    },
    {
      'indexId': '4027',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '字幕标注合规性与时钟准确性',
      'indexType': 'VIDEO_GENERAL_OSD_CLOCK_ACCURACY',
      'componentName': 'VideoOsdClockAccuracy',
    },
    {
      'indexId': '4028',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '视频监控数量增长率',
      'indexType': 'VIDEO_QUANTITY_INCREASE_RATE',
      'componentName': 'VideoMonitorGrowthRate',
    },
    {
      'indexId': '4029',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '视频监控设备撤销率',
      'indexType': 'VIDEO_DEVICE_REVOCATION',
      'componentName': 'VideoDeviceRevocation',
    },
    {
      'indexId': '4030',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '实时视频离线统计',
      'indexType': 'VIDEO_OFFLINE_STAT',
      'componentName': 'VideoOfflineStat',
    },
    {
      'indexId': '4031',
      'indexModule': '4',
      'indexModuleName': '视频流数据',
      'indexName': '视频流编码规范率',
      'indexType': 'VIDEO_CODE_STANDARD_RATE',
      'componentName': 'VideoCodeStandardRate',
    },
  ],
  '车辆视图数据': [
    {
      'indexId': '3001',
      'indexName': '车辆卡口设备抓拍数据完整率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_FULL_INFO',
      'componentName': 'VehicleFullInfo',
    },
    {
      'indexId': '3002',
      'indexName': '重点车辆卡口设备抓拍数据完整率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_FULL_INFO_IMPORTANT',
      'componentName': 'VehicleFullInfo',
    },
    {
      'indexId': '3003',
      'indexName': '车辆卡口设备过车数据准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_INFO_PASS',
      'componentName': 'VehicleInfoPass',
    },
    {
      'indexId': '3004',
      'indexName': '重点车辆卡口设备过车数据准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_INFO_PASS_IMPORTANT',
      'componentName': 'VehicleInfoPass',
    },
    {
      'indexId': '3005',
      'indexName': '车辆卡口设备时钟准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_CLOCK',
      'componentName': 'ClockVehicle',
    },
    {
      'indexId': '3006',
      'indexName': '重点车辆卡口设备主要属性准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_MAIN_PROP',
      'componentName': 'VehicleMainProp',
    },
    {
      'indexId': '3007',
      'indexName': '重点车辆卡口设备类型属性识别准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_TYPE_PROP',
      'componentName': 'VehicleTypeProp',
    },
    {
      'indexId': '3008',
      'indexName': '车辆卡口设备及时上传率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_UPLOAD',
      'componentName': 'UploadVehicle',
    },
    {
      'indexId': '3009',
      'indexName': '重点车辆卡口设备及时上传率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_UPLOAD_IMPORTANT',
      'componentName': 'UploadVehicle',
    },
    {
      'indexId': '3010',
      'indexName': '车辆卡口设备过车图片地址可用率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_URL_AVAILABLE',
      'componentName': 'UrlAvailableVehicle',
    },
    {
      'indexId': '3011',
      'indexName': '重点车辆卡口设备过车图片地址可用率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_URL_AVAILABLE_IMPORTANT',
      'componentName': 'UrlAvailableVehicle',
    },
    {
      'indexId': '3012',
      'indexName': '车辆卡口联网率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_DEVICE_CONNECT_INTERNET',
      'componentName': 'BayonetConnectionRate',
    },
    {
      'indexId': '3013',
      'indexName': '车辆卡口在线率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_ONLINE_RATE',
      'componentName': 'BayonetOnlineRate',
    },
    {
      'indexId': '3018',
      'indexName': '车辆抓拍数据合理性',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_CAPTURE_RATIONALITY',
      'componentName': 'CaptureRationality',
    },
    {
      'indexId': '3019',
      'indexName': '车辆卡口有效报送数量达标率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_VALID_SUBMIT_QUANTITY',
      // 'componentName': 'ValidSubmitQuantity'
    },
    {
      'indexId': '3020',
      'indexName': '车辆卡口在线率提升',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_ONLINE_RATE_ADVANCE',
    },
    {
      'indexId': '3021',
      'indexName': '车辆卡口填报准确率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_ACCURACY',
      'componentName': 'BasicAccuracy',
    },
    {
      'indexId': '3022',
      'indexName': '车辆卡口位置类型完整率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_EMPHASIS_LOCATION',
    },
    {
      'indexId': '3023',
      'indexName': '联网车辆卡口目录一致率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_CATALOGUE_SAME',
      'componentName': 'VehicleNetworkConsistencyRate',
    },
    {
      'indexId': '3037',
      'indexName': '车辆卡口抓拍数据一致性检测',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_CAPTURE_DATA_CONSISTENCY',
    },
    {
      'indexId': '3024',
      'indexName': '车辆卡口设备过车图片地址可用率（人工复核）',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_URL_AVAILABLE_RECHECK',
    },
    {
      'indexId': '3025',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexName': '车辆卡口数量达标率',
      'indexType': 'VEHICLE_QUANTITY_STANDARD',
      'componentName': 'ValidSubmitQuantity',
    },
    {
      'indexId': '3036',
      'indexName': '车辆卡口资产注册率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_ASSET_REGISTER',
      'componentName': 'AssetRegistrationRate',
    },
    {
      'indexId': '3016',
      'indexName': '车辆卡口设备图片存储时长达标率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_IMAGE_STORE_PASS',
      'componentName': 'StorePass',
    },
    {
      'indexId': '3035',
      'indexName': '车辆卡口离线统计',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_OFFLINE_STAT',
      'componentName': 'FaceOfflineStat',
    },
    {
      'indexId': '3038',
      'indexName': '车辆抓拍图片质量合格率',
      'indexModule': '3',
      'indexModuleName': '车辆视图数据',
      'indexType': 'VEHICLE_QUALITY_PASS_RATE',
      'componentName': 'FaceCaptureQualityRate',
    },
  ],
  '平台可用性指标': [
    {
      'indexId': '7001',
      'indexName': '接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'PLATFORM_API_STABILITY',
    },
    {
      'indexId': '7002',
      'indexName': '人脸视图库在线率',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'FACE_PLATFORM_ONLINE_RATE',
      'componentName': 'ViewLibOnline',
    },
    {
      'indexId': '7003',
      'indexName': '车辆视图库在线率',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'VEHICLE_PLATFORM_ONLINE_RATE',
      'componentName': 'ViewLibOnline',
    },
    {
      'indexId': '7004',
      'indexName': '共享联网平台在线率',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'VIDEO_PLATFORM_ONLINE_RATE',
      'componentName': 'PlatformResult',
    },
    {
      'indexId': '7005',
      'indexName': '人脸布控接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'FACE_MONITOR_API_STABILITY',
      'componentName': 'InterfaceStability',
    },
    {
      'indexId': '7006',
      'indexName': '车辆布控接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'VEHICLE_MONITOR_API_STABILITY',
      'componentName': 'InterfaceStability',
    },
    {
      'indexId': '7007',
      'indexName': '分布式身份确认接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'DISTRIBUTED_IDENTITY_API_STABILITY',
      'componentName': 'ControlStability',
    },
    {
      'indexId': '7008',
      'indexName': '人像轨迹查询接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'PORTRAIT_TRACK_API_STABILITY',
      'componentName': 'ControlStability',
    },
    {
      'indexId': '7009',
      'indexName': '车辆轨迹查询接口稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'VEHICLE_TRACK_API_STABILITY',
      'componentName': 'ControlStability',
    },
    {
      'indexId': '7010',
      'indexName': '人脸抓拍数量上传稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'FACE_CAPTURE_STABILITY',
      'componentName': 'CaptureStability',
    },
    {
      'indexId': '7011',
      'indexName': '车辆抓拍数量上传稳定性',
      'indexModule': '7',
      'indexModuleName': '平台可用性指标',
      'indexType': 'VEHICLE_CAPTURE_STABILITY',
      'componentName': 'CaptureStability',
    },
  ],
  '人脸视图数据': [
    {
      'indexId': '2001',
      'indexName': '人脸卡口设备图片地址可用率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_URL_AVAILABLE',
      'componentName': 'UrlAvailableFace',
    },
    {
      'indexId': '2002',
      'indexName': '重点人脸卡口设备图片地址可用率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_FOCUS_URL_AVAILABLE',
      'componentName': 'UrlAvailableFace',
    },
    {
      'indexId': '2003',
      'indexName': '人脸卡口设备抓拍图片合格率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CAPTURE_PASS',
      'componentName': 'FaceCapturePass',
    },
    {
      'indexId': '2004',
      'indexName': '人脸卡口设备及时上传率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_UPLOAD',
      'componentName': 'UploadFace',
    },
    {
      'indexId': '2005',
      'indexName': '重点人脸卡口设备及时上传率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_FOCUS_UPLOAD',
      'componentName': 'UploadFace',
    },
    {
      'indexId': '2006',
      'indexName': '人脸卡口设备时钟准确率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CLOCK',
      'componentName': 'ClockFace',
    },
    {
      'indexId': '2007',
      'indexName': '人脸卡口联网率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_DEVICE_CONNECT_INTERNET',
      'componentName': 'BayonetConnectionRate',
    },
    {
      'indexId': '2008',
      'indexName': '人脸卡口在线率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_ONLINE_RATE',
      'componentName': 'BayonetOnlineRate',
    },
    {
      'indexId': '2009',
      'indexName': '人脸数据一致性',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_DATA_CONSISTENCY',
      'componentName': '',
    },
    {
      'indexId': '2011',
      'indexName': '人脸卡口设备图片存储时长达标率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_IMAGE_STORE_PASS',
      'componentName': 'StorePass',
    },
    {
      'indexId': '2012',
      'indexName': '人脸抓拍数据上传完整率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CAPTURE_COMPLETENESS_RATE',
      'componentName': 'CaptureComplete',
    },
    {
      'indexId': '2013',
      'indexName': '人脸抓拍数据上传合规率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_DATA_COMPLY_RULE_RATE',
      'componentName': 'CaptureComplete',
    },
    {
      'indexId': '2014',
      'indexName': '人脸抓拍数据合理性',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CAPTURE_RATIONALITY',
      'componentName': 'CaptureRationality',
    },
    {
      'indexId': '2023',
      'indexName': '人脸卡口抓拍率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CAPTURE_RATE',
      'componentName': 'CaptureRationality',
    },
    {
      'indexId': '2015',
      'indexName': '人脸卡口有效报送数量达标率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_VALID_SUBMIT_QUANTITY',
      // 'componentName': 'ValidSubmitQuantity'
    },
    {
      'indexId': '2016',
      'indexName': '人脸卡口在线率提升',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_ONLINE_RATE_ADVANCE',
      'componentName': '',
    },
    {
      'indexId': '2017',
      'indexName': '人脸卡口填报准确率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_ACCURACY',
      'componentName': 'BasicAccuracy',
    },
    {
      'indexId': '2018',
      'indexName': '人脸卡口位置类型完整率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_EMPHASIS_LOCATION',
      'componentName': '',
    },
    {
      'indexId': '2019',
      'indexName': '联网人脸卡口目录一致率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CATALOGUE_SAME',
      'componentName': 'NetworkConsistencyRate',
    },
    {
      'indexId': '2022',
      'indexName': '人脸卡口抓拍数据一致性',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_CAPTURE_DATA_CONSISTENCY',
      'componentName': '',
    },
    {
      'indexId': '2020',
      'indexName': '人脸卡口数量达标率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_QUANTITY_STANDARD',
      'componentName': 'ValidSubmitQuantity',
    },
    {
      'indexId': '2021',
      'indexName': '人脸卡口资产注册率',
      'indexModule': '2',
      'indexModuleName': '人脸视图数据',
      'indexType': 'FACE_ASSET_REGISTER',
      'componentName': 'AssetRegistrationRate',
    },
    {
      'indexId': '2024',
      'indexName': '人脸卡口离线统计',
      'indexModule': '2',
      'indexType': 'FACE_OFFLINE_STAT',
      'indexModuleName': '人脸视图数据',
      'componentName': 'FaceOfflineStat',
    },
    {
      'indexId': '2025',
      'indexName': '人脸抓拍图片质量合格率',
      'indexModule': '2',
      'indexType': 'FACE_QUALITY_PASS_RATE',
      'indexModuleName': '人脸视图数据',
      'componentName': 'FaceCaptureQualityRate',
    },
    {
      'indexId': '2026',
      'indexName': '人脸抓拍图片评分率',
      'indexModule': '2',
      'indexType': 'FACE_CAPTURE_SCORE',
      'indexModuleName': '人脸视图数据',
      'componentName': 'FaceCaptureScore',
    },
    {
      'indexId': '2027',
      'indexName': '人脸卡口设备小图地址可用率',
      'indexModule': '2',
      'indexType': 'FACE_SMALL_URL_AVAILABLE',
      'indexModuleName': '人脸视图数据',
      'componentName': 'UrlSmallAvailableFace',
    },
  ],
  '档案数据': [
    {
      'indexId': '6001',
      'indexName': '人像档案置信率',
      'indexModule': '6',
      'indexModuleName': '档案数据',
      'indexType': 'ARCHIVES_PORTRAIT_CONFIDENCE_RATE',
    },
    {
      'indexId': '6002',
      'indexName': '人像档案准确率',
      'indexModule': '6',
      'indexModuleName': '档案数据',
      'indexType': 'ARCHIVES_PORTRAIT_ACCURACY',
    },
  ],
  '重点人员数据': [
    {
      'indexId': '5001',
      'indexName': '基础信息准确率',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_ACCURACY',
    },
    {
      'indexId': '5002',
      'indexName': '轨迹准确率',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_TRACK',
      'componentName': 'TrackAccuracy',
    },
    {
      'indexId': '5003',
      'indexName': '实时轨迹上传及时性',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_TRACK_REAL',
      'componentName': 'TrackUploadTimely',
    },
    {
      'indexId': '5004',
      'indexName': '聚档可用性',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_POLY_USABLE',
    },
    {
      'indexId': '5005',
      'indexName': '轨迹图片可访问率',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_TRACK_URL_AVAILABLE',
      'componentName': 'TrajectoryUrl',
    },
    {
      'indexId': '5006',
      'indexName': '轨迹设备关联率',
      'indexModule': '5',
      'indexModuleName': '重点人员数据',
      'indexType': 'FOCUS_DEVICE_RELATED_RATE',
    },
  ],
  '场所数据指标': [
    {
      'indexId': '8001',
      'indexName': '场所填报准确率',
      'indexModule': '8',
      'indexModuleName': '场所数据指标',
      'indexType': 'SITE_PLACE_ACCURACY',
      // 'componentName': 'BasicAccuracy',
    },
  ],
  '综合指标': [
    {
      // "indexName": "新增指标",
      // "indexModule": "9",
      // "indexModuleName": "新增指标",
      'indexId': 'COMPOSITE_INDEX',
      'indexType': 'COMPOSITE_INDEX',
      'componentName': 'NewIndicators',
    },
  ],
  '人体视图数据': [
    {
      'indexId': '9001',
      'indexName': '人体卡口设备及时上传率',
      'indexModule': '9',
      'indexModuleName': '人体视图数据',
      'indexType': 'BODY_UPLOAD',
      'componentName': 'BodyUpload',
    },
    {
      'indexId': '9002',
      'indexName': '人体卡口设备时钟准确率',
      'indexModule': '9',
      'indexModuleName': '人体视图数据',
      'indexType': 'BODY_CLOCK',
      'componentName': 'BodyClock',
    },
    {
      'indexId': '9003',
      'indexName': '人体卡口设备在线率',
      'indexModule': '9',
      'indexModuleName': '人体视图数据',
      'indexType': 'BODY_ONLINE_RATE',
      'componentName': 'BodyOnlineRate',
    },
    {
      'indexId': '9004',
      'indexName': '人体卡口设备活跃率',
      'indexModule': '9',
      'indexModuleName': '人体视图数据',
      'indexType': 'BODY_ACTIVE',
      // 'componentName': 'BayonetConnectionRate',
      'componentName': 'BodyActive',
    },
  ],
};
/**
 * 以indexId 为唯一标识和key
 * 来寻找componentName
 */
const allIndexTypeObject = {};
Object.values(allIndexType)
  .flat()
  .forEach((item) => {
    allIndexTypeObject[item.indexId] = item;
  });
export { allIndexTypeObject, allIndexType };
