<template>
  <div class="faceconfig">
    <!-- <ui-modal
      v-model="visible"
      :title="title"
      width="95rem"
      @onCancel="handleReset"
    > -->
    <div class="content-wrap">
      <aggre-connect
        v-for="(item, index) in procedureDatas"
        :key="index"
        :propData="item"
        :dWidth="dWidth"
        :bWidth="bWidth"
      ></aggre-connect>
    </div>
    <loading v-if="loading"></loading>
    <!-- </ui-modal> -->
    <data-input ref="dataInput"></data-input>
    <imgtest ref="Imgtest" :propData="imgData" :topicId="topicId" @render="getView"></imgtest>
    <face-structure ref="FaceStructure" title="人脸结构化" type="FaceStructure" @render="getView"></face-structure>
    <face-structure
      ref="UniqueDetection"
      title="图像模糊检测"
      tips="说明：如果选择的多家算法均只检测一张人脸，则人脸唯一。"
      @render="getView"
    ></face-structure>
    <repeattest ref="Repeattest" :repeatForm="repeatForm" @render="getView"></repeattest>
    <urltest ref="urltest" :repeatForm="repeatForm" @render="getView"></urltest>
  </div>
</template>
<script>
import { faceData } from '../face';
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {},
  data() {
    return {
      visible: false,
      title: '新增检测任务-确定治理流程',
      aggregateOptions: [],
      imgData: {
        items: [
          {
            key1: 'important',
            key2: 'importantTime',
            value1: '',
            value2: '1',
            label: '重点人脸卡口数据时延：',
          },
          {
            key1: 'general',
            key2: 'generalTime',
            value1: '',
            value2: '1',
            label: '普通人脸卡口数据时延：',
          },
        ],
      },
      procedure: [],
      procedureDatas: [],
      queryId: '',
      topicType: '',
      topicId: '',
      repeatForm: {
        items: [
          {
            label1: '间隔时间',
            key: 'interval',
            value: '',
            label2: '秒内',
          },
          {
            label1: '同一目标抓拍数量阈值',
            key: 'capture',
            value: '',
            label2: '次',
          },
          {
            label1: '同一目标相似度阈值',
            key: 'same',
            value: '',
            label2: '%',
          },
        ],
      },
      loading: false,
      dWidth: '13.82%',
      bWidth: '15.23%',
    };
  },
  created() {
    this.aggregateOptions = faceData;
  },
  mounted() {},
  methods: {
    init() {
      this.visible = true;
      this.loading = true;
      this.getView();
    },
    async getView() {
      try {
        let res = await this.$http.get(governancetheme.view, {
          params: { id: 2 },
        });
        this.topicType = res.data.data.topicType;
        this.topicId = res.data.data.id;
        this.procedure = res.data.data.componentList;
        this.procedureDatas = this.handleData(this.procedure);
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handleData(proData) {
      let group = [];
      proData.forEach((item) => {
        group.push(item.sort);
      });
      group = Array.from(new Set(group));
      let newDatas = group.map((item) => {
        let arr = [];
        proData.forEach((dItem) => {
          if (dItem.sort === item) {
            arr.push(dItem);
          }
        });
        return arr;
      });
      return this.aggregateOptions.map((item, index) => {
        let obj = JSON.parse(JSON.stringify(item));
        obj.datas = item.datas.map((childItem, childIndex) => {
          childItem = { ...newDatas[index][childIndex], ...childItem };
          childItem['isConfigure'] = newDatas[index][childIndex]['isConfigure'];
          return childItem;
        });
        return obj;
      });
    },
    handleReset() {},
  },
  watch: {},
  components: {
    DataInput: require('@/views/datagovernance/governancetheme/faceprocess/components/dataInput.vue').default,
    AggreConnect: require('@/views/datagovernance/governancetheme/components/aggre-connect.vue').default,
    Imgtest: require('@/views/datagovernance/governancetheme/faceprocess/components/imgtest.vue').default,
    urltest: require('@/views/datagovernance/governancetheme/faceprocess/components/urltest.vue').default,
    FaceStructure: require('@/views/datagovernance/governancetheme/faceprocess/components/face-structure.vue').default,
    Repeattest: require('@/views/datagovernance/governancetheme/faceprocess/components/repeattest.vue').default,
  },
};
</script>
<style lang="less" scoped>
.faceconfig {
  width: 100%;
  // height: 100%;
  height: 460px;
  // position: absolute;
  .content-wrap {
    width: 100%;
    height: 100%;
    // height: 600px;
    position: relative;
  }
  @{_deep} .ivu {
    &-modal-body {
      // position: relative;
      padding: 50px;
      // height: 100%;
      // height: 630px;
    }
    &-poptip-popper {
      min-width: auto !important;
    }
  }
}
</style>
