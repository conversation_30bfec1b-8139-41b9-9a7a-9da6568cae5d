<template>
  <div class="face height-full auto-fill">
    <div class="search-module" :style="searchWidth">
      <div class="mb-lg">
        <ui-label class="inline" label="抓拍时间" :width="70">
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.startTime"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
            placeholder="请选择开始时间"
          ></DatePicker>
          <span> — </span>
          <DatePicker
            class="width-md"
            type="datetime"
            v-model="searchData.endTime"
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
            placeholder="请选择结束时间"
          ></DatePicker>
        </ui-label>
        <ui-label class="inline ml-lg vt-middle" label="请选择摄像机" :width="100">
          <select-camera @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
        </ui-label>
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </div>
      </div>
      <ui-select-tabs
        :list="errorList"
        :need-expand="false"
        @selectInfo="selectInfo"
        ref="uiSelectTabs"
      ></ui-select-tabs>
    </div>

    <div class="statistic" ref="statistic">
      <p class="statistic-title">
        {{ statistic.title }}
      </p>
      <p class="statistic-total">
        {{ statistic.totalNum | formatNum }}
      </p>
      <p class="statistic-title">今日新增</p>
      <p class="statistic-today">
        {{ statistic.todayNum | formatNum }}
      </p>
    </div>
    <div class="table-module auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
      <div class="group-unaggregated">
        <div class="group-item" v-for="(item, index) in tableData" :key="index">
          <div class="group-left">
            <div class="group-img" @click="lookScence(index)">
              <ui-image :src="item.facePath" />
              <p class="shadow-box" title="查看检测结果">
                <i class="icon-font icon-yichang search-icon mr-xs base-text-color" @click.stop="checkReason(item)"></i>
              </p>
            </div>
            <div class="group-message">
              <p class="mb-sm" :title="`抓拍时间：${item.logTime ? item.logTime : '未知'}`">
                <i class="icon-font icon-shijian"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{
                  item.logTime ? item.logTime : '未知'
                }}</span>
              </p>
              <p :title="`抓拍地址：${item.address ? item.address : '未知'}`">
                <i class="icon-font icon-dizhi"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{
                  item.address ? item.address : '未知'
                }}</span>
              </p>
            </div>
          </div>
        </div>
        <div class="empty-item" v-for="(emptyitem, eIndex) in 8" :key="'empty' + eIndex"></div>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <export-disqualify v-model="disqualifyShow" :disqualify-item="disqualifyItem"></export-disqualify>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {},
  data() {
    return {
      loading: false,
      errorList: [],
      searchWidth: 0,
      statistic: {
        title: '人脸视图数据',
        totalNum: 0,
        todayNum: 0,
      },
      searchData: {
        orgCode: null,
        reasons: [],
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      disqualifyShow: false,
      disqualifyItem: {},
      options: {},
    };
  },
  created() {
    this.initErrorList();
    this.initStatistic();
    this.search();
  },
  mounted() {},
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    selectInfo(infoList) {
      this.searchData.reasons = infoList.map((row) => {
        return row.name;
      });
      this.search();
    },
    initSearchWidth() {
      this.$nextTick(() => {
        this.searchWidth = `width: calc(100% - ${this.$refs.statistic.clientWidth}px)`;
      });
    },
    async initStatistic() {
      try {
        let res = await this.$http.post(equipmentassets.queryFaceLibAbnormalCount, {
          orgCode: this.searchData.orgCode,
        });
        this.statistic.totalNum = res.data.data.allCount;
        this.statistic.todayNum = res.data.data.curCount;
        this.initSearchWidth();
      } catch (err) {
        console.log(err);
      }
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(equipmentassets.queryErrorMessages);
        this.errorList = res.data.data.map((row) => {
          return {
            name: row,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      this.loading = true;
      this.tableData = [];
      try {
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.queryFaceLibAbnormalPageList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
        this.imgList = this.tableData.map((row) => row.scenePath);
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    clear() {
      this.$refs.uiSelectTabs.reset();
      this.resetSearchDataMx(this.searchData, this.search);
    },
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
    checkReason(item) {
      this.disqualifyShow = true;
      this.disqualifyItem = item;
    },
  },
  watch: {},
  computed: {},
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    SelectCamera: require('@/components/select-camera.vue').default,
    uiImage: require('@/components/ui-image').default,
    ExportDisqualify: require('@/views/datagovernance/tasktracking/facedata/popup/export-disqualify.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  float: left;
  padding: 20px 20px 10px 20px;
  .keyword-input {
    width: 300px;
  }
}
.statistic {
  float: right;
  padding-right: 20px;
  padding-top: 10px;
  position: absolute;
  top: 0;
  right: 0;
  .statistic-title {
    color: #fff;
  }
  .statistic-total {
    color: var(--color-bluish-green-text);
    font-size: 20px;
    margin-bottom: 15px;
  }
  .statistic-today {
    color: #f18a37;
    font-size: 20px;
  }
}
.table-module {
  clear: both;
  margin-left: 20px;
  position: relative;
  border-top: 1px solid var(--border-color);
  overflow-x: hidden;
  overflow-y: auto;
  .group-unaggregated {
    overflow: hidden;
    position: absolute;
    padding-top: 10px;
    padding-right: 10px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .group-item {
      width: 177px;
      height: 236px;
      margin-bottom: 14px;
      padding: 10px 0;
      overflow: hidden;
      position: relative;
      background: #0f2f59;
      border-radius: 4px;
      border: 2px solid transparent;
      &:hover {
        border: 2px solid var(--color-primary);
      }
    }
    .empty-item {
      width: 177px;
    }
    .group-left {
      .group-img {
        position: relative;
        width: 150px;
        height: 150px;
        cursor: pointer;
        margin: 0 auto 10px;
        img {
          width: 100%;
          height: 100%;
        }
        .shadow-box {
          height: 28px;
          width: 100%;
          background: rgba(0, 0, 0, 0.3);
          position: absolute;
          bottom: 0;
          display: none;
          padding-left: 10px;
          z-index: 10;
          > i:hover {
            color: var(--color-primary);
          }
        }
        &:hover {
          .shadow-box {
            display: block;
          }
        }
      }
      .group-message {
        margin: 0 auto;
        padding: 0 15px;
        color: #afbcd4;
        .group-text {
          width: 124px;
        }
        i {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
