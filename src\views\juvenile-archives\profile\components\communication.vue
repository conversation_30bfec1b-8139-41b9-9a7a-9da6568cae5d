<!--
 * @Date: 2025-02-17 15:05:33
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-19 16:57:31
 * @FilePath: \icbd-view\src\views\juvenile-archives\profile\components\communication.vue
-->
<template>
  <ui-card :title="title" class="communication-card">
    <div class="communication-box">
      <div class="small-card phone-box" v-if="groupList.phoneArr.length > 0">
        <div class="head">
          <span><i class="iconfont icon-shouji"></i>联系电话 </span>
          <span class="head-num">{{ groupList.phoneArr.length }}</span>
        </div>
        <div class="content">
          <div
            class="phone-item"
            v-for="(item, index) in groupList.phoneArr"
            :key="index"
          >
            <div>
              <div class="num">{{ index + 1 }}</div>
              <span class="info" :title="item.communication">{{
                item.communication
              }}</span>
            </div>
            <div>
              <span class="label">来源：</span>
              <span class="info" :title="item.dataSource">{{
                item.dataSource
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <div
        class="small-card address-box"
        v-if="groupList.addressArr.length > 0"
      >
        <div class="head">
          <span><i class="iconfont icon-fangzi"></i>联系地址</span>
          <span class="head-num">{{ groupList.addressArr.length }}</span>
        </div>
        <div class="content">
          <div
            class="phone-item"
            v-for="(item, index) in groupList.addressArr"
            :key="index"
          >
            <div>
              <div class="num">{{ index + 1 }}</div>
              <span class="info" :title="item.communication">{{
                item.communication
              }}</span>
            </div>
            <div>
              <span class="label">来源：</span>
              <span class="info" :title="item.dataSource">{{
                item.dataSource
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <ui-loading v-if="loading" />
      <ui-empty v-if="(!list || !list.length) && !loading" />
    </div>
  </ui-card>
</template>

<script>
export default {
  name: "communication",
  data() {
    return {};
  },
  props: {
    title: {
      type: String,
      default: "通联方式",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    groupList() {
      let phoneArr = [];
      let addressArr = [];
      this.list.forEach((item) => {
        if (item.communicationType == 1) {
          phoneArr.push(item);
        }
        if (item.communicationType == 2) {
          addressArr.push(item);
        }
      });
      return {
        phoneArr,
        addressArr,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.communication-card {
  width: 100%;
}
.communication-box {
  height: 155px;
  display: flex;
  gap: 20px;
  .small-card {
    width: 450px;
    background: #f9f9f9;
    .head {
      display: flex;
      justify-content: space-between;
      border-radius: 4px 4px 0px 0px;
      padding: 0 15px;
      height: 40px;
      line-height: 40px;
      i {
        margin-right: 15px;
      }
      span {
        color: #ffffff;
        font-size: 14px;
        font-weight: bold;
      }
      .head-num {
        font-size: 24px;
      }
    }
    .content {
      border-radius: 0px 0px 4px 4px;
      border: 1px solid #d3d7de;
      overflow: scroll;
      height: 115px;
      .phone-item {
        display: flex;
        height: 40px;
        padding: 0 15px;
        line-height: 40px;
        width: 100%;
        justify-content: space-between;

        > div {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .num {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: #2c86f8;
          border-radius: 2px 2px 2px 2px;
          line-height: 20px;
          font-size: 12px;
          color: #ffffff;
          text-align: center;
          margin-right: 10px;
        }
        .info {
          font-weight: 700;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
  }
  .phone-box {
    .head {
      background: linear-gradient(237deg, #ec9240 0%, #f7b93d 100%);
    }
  }
  .address-box {
    .head {
      background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
    }
  }
}
</style>
