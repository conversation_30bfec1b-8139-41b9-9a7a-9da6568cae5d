<template>
  <div class="keypersonlibrary auto-fill">
    <slot name="search"></slot>
    <div class="keypersonlibrary-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="keypersonlibrary-content-wrap">
        <template v-for="(item, index) in cardList">
          <slot name="card" :row="item" :index="index"></slot>
        </template>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    cardInfo: {
      type: Array,
      default() {},
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
  },
  data() {
    return {
      loading: false,
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      cardList: [],
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 此方法在父组件调用表格方法
    async info(boolen) {
      this.loading = true;
      if (boolen) {
        this.reset();
      }
      const result = this.loadData(this.searchData);
      if (typeof result == 'object' && typeof result.then == 'function') {
        result
          .then((res) => {
            if (res) {
              this.cardList = res.data[this.listKey];
              this.pageData.totalCount = res.data.total;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.cardList = [];
        this.pageData.totalCount = 0;
        this.loading = false;
      }
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = { pageNumber: 1, pageSize: 20 };
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.info();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.info();
    },
    // 详情
    detail(info) {
      this.$emit('detailInfo', info);
    },
  },
  watch: {},
  components: {
    // UiGatherCard: require('./ui-gather-card.vue').default,
  },
};
</script>
<style lang="less" scoped>
.keypersonlibrary {
  // padding-top: 15px;
  background-color: var(--bg-content);
  height: calc(100vh - 340px);
  &-content {
    position: relative;
    height: 100%;
    overflow-y: auto;
    &-wrap {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      // display: -webkit-flex;
      // flex-wrap: wrap;
      height: 500px;
      overflow-y: auto;
      .ui-gather-card {
        margin-right: 10px;
        width: 188px;
        height: 230px;
      }
      .ui-gather-card:nth-child(9n + 0) {
        margin-right: 0px;
      }
    }
  }
  .page {
    padding-right: 0;
  }
}
</style>
