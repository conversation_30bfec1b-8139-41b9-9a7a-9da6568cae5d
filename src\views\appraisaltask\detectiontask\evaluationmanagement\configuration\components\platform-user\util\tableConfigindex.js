export const filterList = (params) => {
  return [
    {
      type: 'input',
      key: 'deviceId',
      label: '设备编码',
    },
    {
      type: 'input',
      key: 'deviceName',
      label: '设备名称',
    },
    {
      type: 'select',
      key: 'sbdwlxList',
      label: '监控点位类型',
      selectMutiple: true, // 多选
      maxTagCount: 1,
      options: params.propertySearchLbdwlx,
    },
    {
      type: 'select',
      key: 'sbgnlxList',
      label: '摄像机功能类型',
      selectMutiple: true, // 多选
      maxTagCount: 1,
      options: params.propertySearchLbgnlx,
      disabled: () => {
        return params.moduleAction.indexModule !== '1';
      },
    },
    {
      type: 'select',
      key: 'phyStatus',
      label: '设备状态',
      options: params.phystatusList,
      width: 150,
    },
    {
      type: 'select',
      key: 'cascadeReportStatus',
      label: '上报状态',
      options: [
        {
          value: '0',
          label: '未上报',
        },
        {
          value: '1',
          label: '已上报',
        },
      ],
    },
    {
      type: 'select',
      key: 'isImportant',
      label: '重点类型',
      options: [
        {
          value: 0,
          label: '普通设备',
        },
        {
          value: 1,
          label: '重点设备',
        },
      ],
    },
    {
      type: 'select',
      key: 'sourceId',
      label: '数据来源',
      options: params.sourceList,
    },
  ];
};

export const tableColumns = (params) => {
  let comHeader = [
    {
      type: 'selection',
      align: 'center',
      width: 50,
    },
    {
      title: '序号',
      type: 'index',
      align: 'center',
      width: 60,
    },
    {
      title: '设备编码',
      key: 'deviceId',
      align: 'left',
      minWidth: 180,
      tooltip: true,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      align: 'left',
      minWidth: 180,
      tooltip: true,
    },
    {
      title: '所属单位',
      key: 'orgName',
      align: 'left',
      minWidth: 150,
      tooltip: true,
    },
    {
      title: '行政区划',
      key: 'civilName',
      align: 'left',
      width: 120,
      tooltip: true,
    },
    {
      title: '摄像机功能类型',
      key: 'sbgnlxText',
      align: 'left',
      minWidth: 130,
      tooltip: true,
    },
    {
      title: '监控点位类型',
      key: 'sbdwlxText',
      align: 'left',
      minWidth: 130,
    },
    {
      title: '重点类型',
      slot: 'isImportant',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
    {
      title: '设备状态',
      slot: 'phyStatus',
      align: 'left',
      tooltip: true,
      minWidth: 130,
    },
  ];
  // 导入文件 的 表格  不显示 设备状态
  return params.type === 'import'
    ? [...comHeader]
    : [
        ...comHeader,
        {
          title: '设备标签',
          slot: 'tagNames',
          align: 'left',
          minWidth: 120,
        },
      ];
};
