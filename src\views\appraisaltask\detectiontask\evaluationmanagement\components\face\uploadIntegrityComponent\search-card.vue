<template>
  <div class="base-search">
    <ui-label class="inline" label="抓拍时间：" :width="70">
      <div class="date-picker-box">
        <DatePicker
          class="input-width"
          v-model="searchData.startTime"
          type="datetime"
          placeholder="请选择开始时间"
          confirm
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          v-model="searchData.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          confirm
        ></DatePicker>
      </div>
    </ui-label>
    <ui-label class="inline ml-lg" label="抓拍设备：" :width="70">
      <select-camera class="select-camera" @pushCamera="pushCamera" :device-ids="searchData.deviceIds"></select-camera>
    </ui-label>

    <ui-label class="inline ml-lg" label="检测结果：" :width="70">
      <Select
        v-if="type === 'fieldQualified'"
        v-model="searchData.outcome"
        class="width-sm"
        :clearable="true"
        placeholder="请选择检测结果"
      >
        <Option v-for="(item, index) in faceSearchList" :value="item.dataKey" :key="index">{{ item.dataValue }}</Option>
      </Select>
      <Select v-else v-model="searchData.qualified" class="width-sm" :clearable="true" placeholder="请选择检测结果">
        <Option v-for="(item, index) in cardSearchList" :value="item.dataKey" :key="index">{{ item.dataValue }}</Option>
      </Select>
    </ui-label>
    <ui-label :width="0" class="inline ml-lg" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
  props: {
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchData: {
        startTime: '',
        endTime: '',
        deviceIds: [],
        outcome: '',
      },
      cardSearchList: [
        {
          dataKey: '1',
          dataValue: '字段完整',
        },
        {
          dataKey: '2',
          dataValue: '字段缺失',
        },
      ],
      faceSearchList: [
        {
          dataKey: '1',
          dataValue: '字段合规',
        },
        {
          dataKey: '2',
          dataValue: '字段不合规',
        },
      ],
    };
  },
  methods: {
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      let params = JSON.parse(JSON.stringify(this.searchData));
      if (params.startTime) params.startTime = this.$util.common.formatDate(this.searchData.startTime);
      if (params.endTime) params.endTime = this.$util.common.formatDate(this.searchData.endTime);
      this.$emit('startSearch', params);
    },
    resetSearchDataMx1() {
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        outcome: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
};
</script>
<style lang="less" scoped>
.base-search {
  overflow: hidden;
  display: flex;
  margin-bottom: 16px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
}
</style>
