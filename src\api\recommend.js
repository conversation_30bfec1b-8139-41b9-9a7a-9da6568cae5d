import request from '@/libs/request'
import { recommendService } from './Microservice'

/**
 * 推荐中心
 */
// 查询推荐中心任务分页列表
export function recommendList (data) {
  return request({
    url: recommendService + '/recommend/task/pageList',
    method: 'post',
    data
  })
}

// 推荐中心任务新增
export function recommendAdd (data) {
  return request({
    url: recommendService + '/recommend/task/add',
    method: 'post',
    data
  })
}

// 推荐中心任务删除
export function recommendDel (ids) {
  return request({
    url: recommendService + `/recommend/task/remove/${ids}`,
    method: 'delete',
  })
}

// 推荐中心任务编辑
export function recommendUpdate (data) {
  return request({
    url: recommendService + '/recommend/task/update',
    method: 'put',
    data
  })
}

// 开始任务
export function recommendStart (id) {
  return request({
    url: recommendService + `/recommend/task/enable/${id}`,
    method: 'get'
  })
}

// 停止任务
export function recommendStop (id) {
  return request({
    url: recommendService + `/recommend/task/disabled/${id}`,
    method: 'get'
  })
}

// 获取推荐中心任务详细信息
export function recommendView (id) {
  return request({
    url: recommendService + `/recommend/task/view/${id}`,
    method: 'get'
  })
}

// 阅读消息
export function recommendRead (id) {
  return request({
    url: recommendService + `/recommend/task/read/${id}`,
    method: 'get'
  })
}

// 非机动车违法查询
export function nonMotorIllegalSearchList (data) {
  return request({
    url: recommendService + '/recommend/abnormalPerson/nonMotorIllegalSearch',
    method: 'post',
    data
  })
}

// 关联身份证信息
export function associationIdentity (data) {
  return request({
    url: recommendService + '/recommend/abnormalPerson/associationIdentity',
    method: 'post',
    data
  })
}

// 获取频繁违法人员列表
export function getFrequentIllegalPersonList (data) {
  return request({
    url: recommendService + '/recommend/abnormalPerson/getFrequentIllegalPersonList',
    method: 'post',
    data
  })
}

// 获取疑似失控人员信息  
export function getOutOfControlPersonList (data) {
  return request({
    url: recommendService + '/recommend/abnormalPerson/getOutOfControlPersonList',
    method: 'post',
    data
  })
}

// 非机动车违法-设备选择-设备树
export function getNonMotorIllegalDeviceTree (data) {
  return request({
    url: recommendService + "/jjdd/device/root/query",
    method: "post",
    data,
  });
}
// 非机动车违法-设备选择-设备
export function getNonMotorIllegalDevice (data) {
  return request({
    url: recommendService + "/jjdd/device/query/pageList",
    method: "post",
    data,
  });
}

// 疑似骑电动车不带头盔高频违法地点推荐
export function getNoMotorFrequentIllegalRegion(data) {
    return request({
        url: recommendService + "/recommend/regional/getNoMotorFrequentIllegalRegion",
        method: "post",
        data,
    });
}

// 区域推荐
// 疑似关注人员高频活动区域推荐
export function personHighFreqRegional(data) {
    return request({
        url: recommendService + "/recommend/regional/personHighFreqRegional",
        method: "post",
        data,
    });
}

// 疑似人员居住区域推荐
export function residential (data) {
  return request({
    url: recommendService + "/recommend/regional/suspected/person/residential",
    method: "post",
    data,
  });
}

// 疑似人员工作区域推荐
export function personWorkList (data) {
  return request({
    url: recommendService + "/recommend/regional/suspected/person/work",
    method: "post",
    data,
  });
}

// 疑似人-车辆关系绑定推荐
export function vidVehicleBindingList(data) {
    return request({
        url: recommendService + "/recommend/suspected/perception/human/vehicle/binding",
        method: "post",
        data,
    });
  }
  
  // 疑似人-车辆关系绑定推荐详情
  export function vidVehicleBindingTrail(data) {
    return request({
        url: recommendService + "/recommend/suspected/perception/human/vehicle/binding/trail",
        method: "post",
        data,
    });
  }
  
  // 疑似娱乐场所从业人员推荐
  export function getSuspectedEntertainmentVenuesPersonList(data) {
    return request({
        url: recommendService + "/recommend/abnormalPerson/getSuspectedEntertainmentVenuesPersonList",
        method: "post",
        data,
    });
  }
  
  // 疑似娱乐场所从业人员推荐详情
  export function getSuspectedEntertainmentVenuesPersonTrail(data) {
    return request({
        url: recommendService + "/recommend/abnormalPerson/getSuspectedEntertainmentVenuesPersonTrail",
        method: "post",
        data,
    });
  }
  
  // 多维感知推荐
  // 疑似人-RFID关系绑定推荐
  export function vidFindRFIDList(data) {
    return request({
        url: recommendService + "/recommend/suspected/perception/vidFindRFIDList",
        method: "post",
        data,
    });
  }
  
  // 疑似人与RFID关系详细信息查询
  export function vidFindRFIDAlongPointList(data) {
    return request({
        url: recommendService + "/recommend/suspected/perception/vidFindRFIDAlongPointList",
        method: "post",
        data,
    });
  }