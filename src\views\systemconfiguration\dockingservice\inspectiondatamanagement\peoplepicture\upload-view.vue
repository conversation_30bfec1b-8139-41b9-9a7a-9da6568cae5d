<template>
  <ui-modal v-model="visible" title="新增照片" :styles="styles">
    <upload-img multiple :multiple-num="1" @successPut="successPut" ref="upload"></upload-img>
    <template #footer>
      <Button @click="visible = false"> 取 消</Button>
      <Button class="ml-lg" type="primary" :loading="saveLoading" @click="query"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      styles: {
        width: '4rem',
      },
      visible: false,
      saveLoading: false,
      uploadData: [],
    };
  },
  created() {},
  methods: {
    successPut(list, item) {
      this.uploadData.push({
        name: item.originalFilename,
        type: 2,
        url: item.fileUrl,
      });
    },
    async query() {
      try {
        this.saveLoading = true;
        const res = await this.$http.post(equipmentassets.batchInsertManagementData, this.uploadData);
        this.visible = false;
        this.$Message.success(res.data.msg);
        this.$emit('update');
      } catch (err) {
        console.log(err);
      } finally {
        this.saveLoading = false;
      }
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.$refs.upload.clear();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UploadImg: (resolve) => {
      require(['@/components/upload-img'], resolve);
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  max-height: 500px;
  overflow-y: auto;
}
</style>
