<template>
  <div class="page-evaluationoverview auto-fill">
    <div class="evaluat_div height-full" v-show="!componentName">
      <div class="head-title">
        <div class="bread-crumb fl ml-sm f-16">
          <ui-breadcrumb :data="breadcrumbData" @change="handleChange" :disabled="breadcrumbDisabled"></ui-breadcrumb>
        </div>
        <div class="fr mr-md">
          <div class="btns">
            <span class="f-14" :class="{ active: overBtn === 1 }" @click="overviewBtn(1)"> 统计模式 </span>
            <span class="f-14" :class="{ active: overBtn === 2 }" @click="overviewBtn(2)"> 报表模式 </span>
          </div>
          <el-popover
            popper-class="reference-popover"
            visible-arrow="false"
            placement="bottom"
            v-model="visible"
            trigger="click"
          >
            <div class="form-content">
              <ui-label class="mb-lg" label="检测任务" :width="65">
                <Select v-model="taskType" placeholder="请选择检测任务" class="width-input" filterable>
                  <Option v-for="(item, index) in taskList" :key="index" :label="item.taskName" :value="item.id">
                    {{ item.taskName }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label class="mb-lg" label="统计方式" :width="65">
                <Select v-model="statisticType" placeholder="请选择" class="width-input">
                  <Option v-for="(item, index) in statisticsList" :key="index" :label="item.name" :value="item.type">
                    {{ item.name }}
                  </Option>
                </Select>
              </ui-label>

              <div class="mb-md t-center">
                <Button class="ml-sm" @click="visible = false">取 消</Button>
                <Button class="ml-sm" type="primary" @click="submit">确 定</Button>
              </div>
            </div>
            <i class="icon-font icon-shaixuan f-16 screen" slot="reference"></i>
          </el-popover>
        </div>
      </div>
      <div class="evaluationoverview">
        <div class="content-box auto-fill">
          <div class="overview-content" :style="overBtn === 2 ? styles1 : styles2">
            <!-- 报表模式 -->
            <template>
              <model-table
                v-show="overBtn === 2"
                :statistic-type="statisticType"
                :task-scheme-id="taskType"
                :is-deep-active-item="isDeepActiveItem"
                :uuid="uuid"
                @changeBreadcrumb="changeBreadcrumb"
              ></model-table>
            </template>
            <!-- 统计模式 -->
            <div class="evaluate-tendency" v-if="overBtn == 1">
              <div class="statistic-list">
                <ul>
                  <li v-for="(item, index) in statisticList" :key="index" :class="item.listBg">
                    <div class="monitoring-data">
                      <em class="icon-font f-50 pl20" :class="[item.icon, item.iconBg]"></em>
                      <span>
                        <p>{{ item.name }}</p>
                        <p class="statistic-num">
                          {{ item.value || 0 | separateNum }}
                        </p>
                      </span>
                    </div>
                  </li>
                </ul>
              </div>
            </div>
            <capture-box
              v-show="overBtn === 1"
              ref="captureEvaluation"
              :tabList="tabList"
              :statisticType="statisticType"
            ></capture-box>
            <template v-if="overBtn == 1 && systemConfig.distinguishVersion === '2'">
              <div class="overview-title">
                <span class="fl"> <i class="icon-font f-14 icon-pingcezhibiaozonglan mr-sm"></i>问题处理总览</span>
              </div>
              <ProblemSolving
                ref="problemSolving"
                :task-type="taskType"
                :statisticType="statisticType"
                :uuid="uuid"
              ></ProblemSolving>
            </template>
          </div>

          <export-data ref="exportModule" @exportAdd="getExport"></export-data>
        </div>
      </div>
    </div>
    <keep-alive v-show="componentName">
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<style lang="less">
.reference-popover {
  margin-top: 0 !important;
  position: absolute !important;
  top: 166px !important;
  left: 1540px !important;
  z-index: 99999 !important;
  width: 350px !important;
  height: 200px !important;
  border: none !important;
  padding: 28px 20px 20px !important;
  background: var(--bg-select-dropdown) !important;
  .popper__arrow {
    display: none !important;
  }
}
</style>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .btns {
    span {
      color: var(--bg-btn-primary);
      background-color: #ffffff;
      border: 1px solid var(--bg-btn-primary-active);
    }
    .active {
      color: #ffffff;
      background-color: var(--bg-btn-primary-active);
    }

    span:hover {
      color: #ffffff;
      background-color: var(--bg-btn-primary-active);
    }
  }

  .evaluate-tendency {
    background-color: transparent;
    .statistic-list {
      ul {
        .list-bg1 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-1.png') no-repeat;
        }
        .list-bg2 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-2.png') no-repeat;
        }
        .list-bg3 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-3.png') no-repeat;
        }
        .list-bg4 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-4.png') no-repeat;
        }
        .list-bg5 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-5.png') no-repeat;
        }
        .list-bg6 {
          background: url('~@/assets/img/evaluationoverview/list-bg-blue-6.png') no-repeat;
        }
      }
    }
  }
}

.width-input {
  width: 210px;
}
.page-evaluationoverview {
  padding: 15px 15px 0;
  background-color: var(--bg-content);
  position: relative;
  .head-title {
    height: 56px;
    line-height: 56px;
    background: var(--bg-title-card);
    position: relative;
    // opacity: 0.63;
    text-align: center;

    .screen {
      color: var(--color-el-tree-node__expand-icon);
      opacity: 1;
    }
    .bread-crumb {
      @{_deep} .breadcrumb-container {
        .box {
          height: 18px !important;
        }
        .address {
          font-size: 18px !important;
        }
      }
    }
  }
  .evaluationoverview {
    height: calc(100% - 50px);
    width: 100%;
    .content-box {
      margin-top: 15px;
      height: 100%;
    }
  }
  .overview-content {
    position: relative;
    width: auto;
    &::-webkit-scrollbar {
      display: none; /* Chrome Safari */
    }
  }
}
.btns {
  position: absolute;
  right: 50px;
  top: 0px;
  cursor: pointer;

  span {
    // display: inline-block;
    padding: 4px 10px;
    color: #56789c;
    font-family: 'regular';
    background-color: #12294e;
    border-radius: 0;
    border: 1px solid #174f98;
  }
  .active {
    color: #fff;
    background-color: #2d8cf0;
  }

  span:hover {
    // display: inline-block;
    color: #fff;
    background-color: #2d8cf0;
  }

  span:first-child {
    border-radius: 4px 0 0 4px;
  }
  span:last-child {
    border-radius: 0 4px 4px 0;
  }
}
.overview-title {
  height: 50px;
  line-height: 50px;
  background: linear-gradient(180deg, rgba(13, 93, 197, 0.6) 0%, rgba(13, 49, 97, 0.6) 100%);
  position: relative;
  text-align: center;
  font-size: 16px;
  padding: 0 20px;
  color: #fff;
  opacity: 1;

  .active {
    color: #fff;
    background-color: #2d8cf0;
  }
}
.evaluate-tendency {
  height: 108px;
  width: 100%;
  background-color: var(--bg-sub-content);
  margin-bottom: 10px;
  position: relative;
  .reach-title {
    text-align: center;
    padding-top: 20px;
    width: 100%;
  }
  .statistic-list {
    width: 100%;
    // margin-top: 10px;
    height: 108px;
    ul {
      width: 100%;
      height: 108px;
      display: flex;
      justify-content: center;
      align-items: center;
      li {
        flex: 1;
        height: 108px;
        display: flex;
        align-items: center;
        border-radius: 10px;
        .track-data {
          border-right: none;
        }
        div {
          height: 56px;
          width: 100%;
          // border-right: 1px solid #094a8a;
          line-height: 56px;
          display: flex;
          justify-content: center;
          align-items: center;

          em {
            display: inline-block;
            height: 56px;
            width: 56px;
            line-height: 56px;
            text-align: center;
            border-radius: 50%;
            background: rgba(218, 245, 248, 0.2);
          }
          .f-50 {
            font-size: 30px;
            color: #fff;
          }
          .pl20 {
            margin-left: 20px;
          }
          span {
            display: inline-block;
            height: 50px;
            flex: 1;
            margin-left: 20px;
            text-align: left;
            p {
              white-space: nowrap;
              font-style: normal;
              height: 25px;
              line-height: 25px;
              color: #f5f5f5;
              font-size: 12px;
            }
            .statistic-num {
              font-size: 24px;
              color: #f5f5f5;
              -webkit-text-stroke: 1 rgba(0, 0, 0, 0);
              opacity: 1;
              font-family: 'Microsoft YaHei';
            }
          }
        }
      }
      .list-bg1 {
        width: 100%;
        height: 100%;
        margin-right: 10px;
        background: url('~@/assets/img/evaluationoverview/list-bg1.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
      .list-bg2 {
        width: 100%;
        height: 100%;
        margin-right: 10px;
        background: url('~@/assets/img/evaluationoverview/list-bg2.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
      .list-bg3 {
        width: 100%;
        height: 100%;
        margin-right: 10px;
        background: url('~@/assets/img/evaluationoverview/list-bg3.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
      .list-bg4 {
        width: 100%;
        height: 100%;
        margin-right: 10px;
        background: url('~@/assets/img/evaluationoverview/list-bg4.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
      .list-bg5 {
        width: 100%;
        height: 100%;
        margin-right: 10px;
        background: url('~@/assets/img/evaluationoverview/list-bg5.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
      .list-bg6 {
        width: 100%;
        height: 100%;
        background: url('~@/assets/img/evaluationoverview/list-bg6.png') no-repeat;
        background-size: cover;
        opacity: 1;
      }
    }
  }
}
</style>

<script>
import evaluationreport from '@/config/api/evaluationreport';
import evaluationoverview from '@/config/api/evaluationoverview';
import dealWatch from '@/mixins/deal-watch';
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import downLoadTips from '@/mixins/download-tips';
import { mapGetters } from 'vuex';
import staticTableMixin from '@/views/governanceevaluation/evaluationoverview/modules/model-table/utils/mixins.js';

export default {
  name: 'evaluationoverview',
  mixins: [dealWatch, downLoadTips, evaluationoResultMixin, staticTableMixin],
  data() {
    return {
      breadcrumbDisabled: false,
      isActive: 1,
      isDeepTable: false,
      breadcrumbData: [],
      // [报表模式]判断是第一层 or 第二层
      isDeepActiveItem: {
        id: null,
        isFirstLevel: true,
        parentId: null,
        level: 1
      },
      handleHeight: 530,
      taskList: [],
      statisticsList: [
        { name: '按组织机构统计', type: 'ORG' },
        { name: '按行政区划统计', type: 'REGION' },
      ],
      styles1: { height: '98%', overflow: 'auto', paddingRight: '3px' },
      componentName: null,
      id: 2,
      resultData: {
        componentName: 'overviewEvaluation', // 需要跳转的组件名
        text: '评测详情', // 跳转页面标题
        title: '评测详情',
        type: 'view',
        // title: "视图基础数据治理主题",
      },
      componentLevel: 0,
      hintShow: false,
      disabledHover: false,
      isDisabled: false,
      tableLoading: false,
      countObj: {},
      countChildObj: {},
      columns14: [],
      tableData: [],
      visible: false,
      rankLoading: false,
      curBtn: 1,
      overBtn: 2,
      taskType: 1,
      statisticType: 'REGION',
      code: '00000',
      tabList: [],
      task: 0,
      titleName: '',
      regionCode: '',
      statisticList: [
        {
          name: '视频监控',
          vlaue: 0,
          icon: 'icon-shitujichushuju',
          iconBg: 'icon-bg1',
          listBg: 'list-bg1',
        },
        {
          name: '人脸卡口',
          vlaue: 0,
          icon: 'icon-renliankakou',
          iconBg: 'icon-bg2',
          listBg: 'list-bg2',
        },
        {
          name: '车辆卡口',
          vlaue: 0,
          icon: 'icon-cheliangkakou',
          iconBg: 'icon-bg3',
          listBg: 'list-bg3',
        },
        {
          name: '人脸抓拍数据',
          vlaue: 0,
          icon: 'icon-renlianzhuapaishuju',
          iconBg: 'icon-bg4',
          listBg: 'list-bg4',
        },
        {
          name: '车辆抓拍数据',
          vlaue: 0,
          icon: 'icon-cheliangzhuapaishuju',
          iconBg: 'icon-bg5',
          listBg: 'list-bg5',
        },
        {
          name: 'ZDR人像轨迹数据',
          vlaue: 0,
          icon: 'icon-keypersonlibrary',
          iconBg: 'icon-bg6',
          listBg: 'list-bg6',
        },
      ],
      informationList: {},
      queryAccessDataCount: {},
      year: '',
      month: '',
      administrative: '',
      selfRegionCode: '',
      uuid: '',
      orgRegeionCode: '',
    };
  },
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    overviewEvaluation: require('@/views/governanceevaluation/evaluationoverview/overview-evaluation/index.vue')
      .default,
    UiBreadcrumb: require('@/views/appraisaltask/inspectionrecord/components/ui-breadcrumb').default,
    captureBox: require('@/views/governanceevaluation/evaluationoverview/components/capture-box.vue').default,
    hintModalShow: require('@/views/governanceevaluation/evaluationoverview/components/hint-modal-show.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    exportData: require('./export-data.vue').default,
    ModelTable: require('@/views/governanceevaluation/evaluationoverview/modules/model-table/index.vue').default,
    ProblemSolving: require('@/views/governanceevaluation/evaluationoverview/components/problem-solving.vue').default,
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
      comprehensiveConfig: 'common/getComprehensiveConfig',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      systemConfig: 'common/getSystemConfig',
    }),
    styles2() {
      let style = {
        height: '87%',
      };
      if (this.systemConfig.distinguishVersion === '2') {
        style = { height: '100%', overflow: 'auto', paddingRight: '3px' };
      }
      return style;
    },
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  async created() {
    await this.getListTaskSchemes();
    this.submit();
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    handleChange(val) {
      this.isDeepActiveItem = {
        id: val.id,
        // 判断是第一层 or 第二层
        isFirstLevel: val.level === 1 ? true : false,
        parentId: val.parentId,
        level: val.level
      };
    },
    // 点击进入第二层
    changeBreadcrumb(item) {
      this.initLevelData(false, item);
    },
    jump({ batchId, indexId, indexType, regionCode, orgCode }) {
      this.$router.push({
        name: 'evaluationoResult',
        query: {
          orgCode: orgCode,
          regionCode: regionCode,
          statisticType: this.statisticType,
          taskSchemeId: this.taskType,
          indexId: `${indexId}`,
          indexType: indexType,
          batchId: batchId,
        },
      });
    },
    async getListTaskSchemes() {
      try {
        this.taskList = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.taskList = data || [];
        if (this.taskList.length > 0) {
          this.taskType = this.taskList[0]['id'];
        }
      } catch (e) {
        console.log(e);
      }
    },
    hintModalShow() {
      this.hintShow = true;
    },
    async submit() {
      await this.getOptionalResultsByTaskType();
      this.queryIndexDeviceOverview();
      this.queryDeviceOfAccessDataCount();
      if (
        !this.tabList.batchIds &&
        !this.tabList.selfRegionCode &&
        !this.tabList.targetDataType &&
        !this.tabList.statisticType
      ) {
        this.$Message.warning('检测任务未执行，暂无统计数据！');
        return;
      } else {
        this.tabList = this.tabList;
      }
      if (this.overBtn === 1) {
        this.$nextTick(() => {
          if (
            (this.tabList.indexModules[0].key === '-1' && this.systemConfig.distinguishVersion === '1') ||
            (this.taskType === 2 && this.systemConfig.distinguishVersion === '2')
          ) {
            this.$refs.captureEvaluation.changeTab(
              this.tabList.indexModules[1].key,
              this.tabList.indexModules[1].value,
              this.tabList,
            );
          } else {
            this.$refs.captureEvaluation.changeTab(
              this.tabList.indexModules[0].key,
              this.tabList.indexModules[0].value,
              this.tabList,
            );
          }
        });
        if (this.systemConfig.distinguishVersion === '2') {
          this.$refs.problemSolving.init(this.tabList);
        }
      } else {
        //this.getIndexOverviewData();
      }

      this.visible = false;
    },
    async overviewBtn(val) {
      this.breadcrumbDisabled = val === 1 ? true : false;
      // 不是第一层，要重置面包屑
      if (!this.isDeepActiveItem.isFirstLevel) {
        this.initLevelData();
      }
      this.overBtn = val;
      if (val == 1) {
        this.$nextTick(() => {
          if (
            (this.tabList.indexModules[0].key === '-1' && this.systemConfig.distinguishVersion === '1') ||
            (this.taskType === 2 && this.systemConfig.distinguishVersion === '2')
          ) {
            this.$refs.captureEvaluation.changeTab(
              this.tabList.indexModules[1].key,
              this.tabList.indexModules[1].value,
              this.tabList,
            );
          } else {
            this.$refs.captureEvaluation.changeTab(
              this.tabList.indexModules[0].key,
              this.tabList.indexModules[0].value,
              this.tabList,
            );
          }
          if (this.systemConfig.distinguishVersion === '2') {
            this.$refs.problemSolving.init(this.tabList);
          }
        });
      }
    },
    // 下拉根据考核任务查询
    async getOptionalResultsByTaskType() {
      this.tabList = [];
      try {
        let res = null;
        // 直接打开页面，query中不存在showResult字段值，接口入参需要该字段
        let { showResult } = this.$route.query;
        if (showResult) {
          res = await this.$http.get(evaluationreport.getOptionalBatchIdsByTaskSchemeId, {
            params: {
              taskSchemeId: this.taskType,
            },
          });
        } else {
          res = await this.$http.post(evaluationreport.getOptionalBatchIdsByTaskSchemeIdV2, {
            taskSchemeId: this.taskType,
            showResult: 1,
          });
        }
        this.tabList = res.data.data;
        this.titleName = res.data.data.selfRegionName;
        this.selfRegionCode = this.tabList.selfRegionCode;
        this.administrative = this.selfRegionCode;
        this.year = this.tabList.year;
        this.month = this.tabList.month;
        this.initLevelData();
      } catch (err) {
        console.log(err);
      }
    },
    // 头部统计(前面三个)
    async queryIndexDeviceOverview() {
      let param = {};
      if (this.statisticType == 'REGION') {
        param = {
          orgRegionFlag: this.statisticType,
          orgRegeionCode: this.tabList.selfRegionCode || this.getHomeConfig.regionCode,
        };
        if (!this.tabList.selfRegionCode && !this.getHomeConfig.regionCode) return;
      } else {
        param = {
          orgRegionFlag: this.statisticType,
          orgRegeionCode: this.tabList.selfOrgCode || this.getHomeConfig.regionCode,
        };
        if (!this.tabList.selfOrgCode && !this.getHomeConfig.regionCode) return;
      }
      try {
        let { data } = await this.$http.get(evaluationoverview.getDeviceOverview, {
          params: param,
        });
        this.informationList = data.data.data || {};
        this.statisticList[0].value = this.informationList.videoSurveillanceAmount;
        this.statisticList[1].value = this.informationList.faceSwanAmount;
        this.statisticList[2].value = this.informationList.vehicleBayonetAmount;
      } catch (err) {
        console.log(err);
      }
    },
    // 头部统计(后面三个)
    async queryDeviceOfAccessDataCount() {
      let param = {};
      if (this.statisticType == 'REGION') {
        param = {
          orgRegionFlag: this.statisticType,
          orgRegeionCode: this.tabList.selfRegionCode || this.getHomeConfig.regionCode,
        };
        if (!this.tabList.selfRegionCode && !this.getHomeConfig.regionCode) return;
      } else {
        param = {
          orgRegionFlag: this.statisticType,
          orgRegeionCode: this.tabList.selfOrgCode || this.getHomeConfig.regionCode,
        };
        if (!this.tabList.selfOrgCode && !this.getHomeConfig.regionCode) return;
      }
      try {
        let { data } = await this.$http.get(evaluationoverview.getAccessDataCount, {
          params: param,
        });
        this.queryAccessDataCount = data.data.data || {};
        this.statisticList[3].value = this.queryAccessDataCount.faceViewAmount;
        this.statisticList[4].value = this.queryAccessDataCount.vehicleViewAmount;
        this.statisticList[5].value = this.queryAccessDataCount.zdrTrackAmount;
      } catch (e) {
        console.log(e);
      }
    },
    // 导出
    async getExport(val) {
      let data = [];
      if (this.statisticType == 'REGION') {
        val.regionCodes.map((item) => {
          data.push(item.regionCode);
        });
      } else {
        val.regionCodes.map((item) => {
          data.push(item.orgCode);
        });
      }
      let params = {
        selfRegionCode: this.tabList.selfRegionCode,
        batchIds: this.tabList.batchIds,
        dataType: this.tabList.targetDataType,
        displayType: this.statisticType,
        regionCodes: data,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportIndexOverviewReportData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.$refs.exportModule.hide();
      } catch (err) {
        console.log(err);
      }
    },
    selectModule(name) {
      if (name) {
        const nameArr = name.split('-');
        this.componentLevel = nameArr[nameArr.length - 1] === 'overviewEvaluation' ? 0 : 1;
      }
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
  },
  watch: {},
};
</script>
