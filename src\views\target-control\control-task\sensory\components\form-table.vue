<template>
  <div>
    <Table
      class="auto-fill table"
      :columns="columns"
      border
      :loading="loading"
      :data="tableData"
      max-height="580"
    >
      <template #mac="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].mac"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #imsi="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].imsi"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #imei="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].imei"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #rfidCode="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].rfidCode"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #obuId="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].obuId"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #name="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].name"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #idCardNo="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].idCardNo"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>

      <template #dictionaryCode="{ row, index }">
        <div class="btn-tips">
          <ui-btn-tip
            content="删除"
            icon="icon-shanchu"
            class="primary"
            @click.native="handleDele(row, index)"
          />
        </div>
      </template>
    </Table>
    <div class="add-from" @click="addForm()" v-if="tableData.length < 10">
      <Icon type="md-add" />
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    compareType: {
      type: [Number, String],
      default: () => "",
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      resourceId: "",
      tableData: [{}],
    };
  },
  computed: {
    columns() {
      let columns = [{ title: "操作", slot: "dictionaryCode", width: 70 }];
      if (this.compareType == "3" || this.compareType == "4") {
        columns.unshift({ title: "身份证号", slot: "idCardNo" });
        columns.unshift({ title: "姓名", slot: "name" });
      }
      let row = "";
      if (this.compareType == "3")
        row = { title: "Mac地址（必填）", slot: "mac" };
      if (this.compareType == "4") {
        row = { title: "IMSI编码（必填）", slot: "imsi" };
        columns.unshift({
          title: "国际移动台设备识别码（必填）",
          slot: "imei",
        });
      }
      if (this.compareType == "5")
        row = { title: "Rfid地址（必填）", slot: "rfidCode" };
      if (this.compareType == "6")
        row = { title: "Etc编号（必填）", slot: "obuId" };
      if (row) columns.unshift(row);
      return columns;
    },
  },
  watch: {},
  async created() {},
  methods: {
    // 删除
    handleDele(row, index) {
      if (this.tableData.length == 1) {
        this.$Message.warning("最少保留一条数据");
        return;
      }
      this.tableData.splice(index, 1);
    },
    addForm() {
      this.tableData.push({});
    },
  },
};
</script>
<style lang="less" scoped>
.config {
  /deep/ .ivu-table-body {
    min-height: 220px;
  }
  /deep/.ivu-table-tbody tr td {
    background-color: #f9f9f9 !important;
  }
  /deep/ .ivu-table-border th {
    border-right: 1px solid #d3d7de !important;
  }
  /deep/.ivu-table td,
  .ivu-table th {
    border-bottom: 1px solid #d3d7de;
  }
  /deep/.ivu-input,
  .ivu-select {
    width: 110px;
  }
}
.add-from {
  background: #f9f9f9;
  margin-top: 3px;
  border: 1px solid #e8eaec;
  text-align: center;
  cursor: pointer;
  /deep/ .ivu-icon-md-add {
    color: #909399;
  }
}
.btn-tips {
  text-align: center;
}
.select-tag-button {
  width: auto;
  max-width: 80px;
  padding: 0 2px;
}

.filterable {
  /deep/ .ivu-select-input {
    width: 270px;
    margin-left: -80px;
  }
}
</style>
