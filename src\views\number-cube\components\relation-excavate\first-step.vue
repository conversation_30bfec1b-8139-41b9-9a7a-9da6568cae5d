<template>
  <div class="first-step-box">
    <div class="top-tips">注：将需要挖掘的实体类型拖入左边加号中</div>
    <div class="step-content">
      <div
        class="left-box"
        id="mountNode"
        @dragover="dragover"
        @drop="drop"
      ></div>
      <div class="right-box">
        <div class="title">目标实体类型选择</div>
        <div class="nodes-box">
          <div
            class="node-item"
            v-for="(item, index) in nodeTypeList"
            :key="index"
            draggable="true"
            @dragend="dragend(index, $event)"
            @dragstart="dragstart"
          >
            <img
              :class="selectNodeIndex == index ? 'active' : ''"
              :src="item.img"
              alt=""
            />
            <div>{{ item.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import G6 from "@antv/g6";
export default {
  components: {},
  props: {
    sourceNode: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: () => true,
    },
    targetNodeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      graph: null,
      graphData: {
        nodes: [],
        edges: [],
      },
      nodeTypeList: [
        // { id: "1", name: "人员", img: require("./img/people.png") },
        // { id: "2", name: "车辆", img: require("./img/vehicle.png") },
        // { id: "3", name: "场所", img: require("./img/place.png") },
        // { id: "4", name: "设备", img: require("./img/device.png") },
      ],
      selectNodeObj: {
        id: "0",
        label: "+",
        x: 500,
        y: 220,
        size: [60, 60],
        style: {
          fill: "#F9F9F9",
          stroke: "#D3D7DE",
        },
        labelCfg: {
          style: {
            fill: "#333",
            lineWidth: 2,
            fontSize: 13,
          },
        },
      },
      nodeDelObj: {
        id: "node-del",
        label: "",
        x: 500,
        y: 220,
        size: [60, 60],
        style: {
          fill: "rgba(0,0,0,0.5)",
          stroke: "rgba(0,0,0,0 )",
          lineWidth: 40,
        },
        labelCfg: {
          style: {
            fill: "#fff",
            lineWidth: 2,
            fontSize: 20,
          },
        },
        icon: {
          show: true,
          img: require("./img/del.png"),
          //   width: 40,
          //   height: 40
        },
      },
      selectNodeIndex: -1,
      isDrop: false, //拖拽元素是否可放置
    };
  },
  mounted() {
    this.graph = new G6.Graph({
      container: "mountNode",
      width: 646,
      height: 465,
      fitView: true,
      fitViewPadding: [0, 100, 0, 100],
      modes: {
        default: ["drag-canvas", "zoom-canvas", "'drag-node'"],
      },
    });
  },
  methods: {
    initGraph() {
      this.nodeTypeList = [];
      //处理目标实体类型数据
      let tarNode = {};
      tarNode = JSON.parse(this.targetNodeData.diggableEntities);
      for (var i = 0; i < Object.keys(tarNode).length; i++) {
        var key = Object.keys(tarNode)[i];
        var value = tarNode[key];
        this.nodeTypeList[i] = {
          id: i + 1 + "",
          name:
            key == "people"
              ? "人员"
              : key == "vehicle"
              ? "车辆"
              : key == "place"
              ? "场所"
              : "设备",
          img: require(`./img/${key}.png`),
          nodeType: value,
          targetNodeInfo: this.targetNodeData,
        };
      }
      this.graphData = {
        nodes: [],
        edges: [],
      };
      let sourceNodeObj = { ...this.sourceNode };
      sourceNodeObj.x = 280;
      sourceNodeObj.y = 220;
      this.graphData.nodes = [sourceNodeObj, this.selectNodeObj];
      this.graphData.edges = [
        {
          source: sourceNodeObj.id,
          target: this.selectNodeObj.id,
          type: "line",
          style: {
            lineDash: [5, 5],
          },
          label: "",
          labelCfg: {
            refY: 5,
            style: {
              fill: "#D8D8D8",
              background: {
                fill: "#ffffff",
                stroke: "#9EC9FF",
                padding: [2, 2, 2, 2],
                radius: 2,
              },
            },
          },
        },
      ];
      this.graph.data(this.graphData);
      this.graph.render();
      let that = this;
      this.graph.on("node:mouseover", (evt) => {
        if (evt.item._cfg && evt.item._cfg.model.type == "image") {
          that.graph.addItem("node", that.nodeDelObj);
        }
      });
      this.graph.on("node:mouseleave", (evt) => {
        if (evt.item._cfg && evt.item._cfg.model.id == "node-del") {
          that.graph.removeItem(evt.item);
        }
      });
      this.graph.on("node:click", (evt) => {
        if (evt.item._cfg && evt.item._cfg.model.id == "node-del") {
          that.graph.removeItem(evt.item);
          that.resetNode();
          that.initGraph();
        }
      });
      if (this.selectNodeIndex > -1) {
        this.graphData.nodes[1].label =
          this.nodeTypeList[this.selectNodeIndex].name;
        this.graphData.nodes[1].nodeType =
          this.nodeTypeList[this.selectNodeIndex].nodeType;
        this.graphData.nodes[1].targetNodeInfo =
          this.nodeTypeList[this.selectNodeIndex].targetNodeInfo;
        this.graphData.nodes[1].labelCfg.style.fill = "#333";
        this.graphData.nodes[1].labelCfg.style.fontSize = 13;
      }
      this.$emit("getFirstData", this.graphData);
    },
    dragstart(e) {
      this.isDrop = false;
    },
    dragend(index, e) {
      if (this.isDrop) {
        this.selectNodeIndex = index;
        this.selectNodeObj.label = this.nodeTypeList[index].name;
        this.selectNodeObj.type = "image";
        this.selectNodeObj.id = this.nodeTypeList[index].id;
        this.selectNodeObj.img = this.nodeTypeList[index].img;
        this.initGraph();
      }
    },
    dragover() {
      return false;
    },
    drop(e) {
      this.isDrop = true;
    },
    resetNode() {
      this.selectNodeIndex = -1;
      this.selectNodeObj.id = "0";
      this.selectNodeObj.label = "+";
      this.selectNodeObj.type = "";
      this.selectNodeObj.img = "";
    },
  },
  watch: {
    sourceNode(val) {
      this.initGraph();
    },
    visible(val) {
      console.log(val, "val");

      if (!val) {
        this.resetNode();
        this.initGraph();
      }
    },
    immediate: true,
  },
};
</script>
<style lang="less" scoped>
.first-step-box {
  width: 100%;
  height: 100%;
  .top-tips {
    width: 100%;
    text-align: right;
    font-size: 14px;
    margin-bottom: 5px;
    color: #f29f4c;
  }
  .step-content {
    width: 100%;
    height: calc(~"100% - 21px");
    display: flex;
    background: #ffffff;
    #mountNode {
      width: 60%;
      height: 100%;
      border: 1px solid #d3d7de;
      margin-right: 10px;
    }
    .right-box {
      flex: 1;
      height: 100%;
      border: 1px solid #d3d7de;
      .title {
        height: 40px;
        line-height: 40px;
        color: rgba(0, 0, 0, 0.9);
        font-weight: 700;
        padding-left: 10px;
        background: #f9f9f9;
      }
      .nodes-box {
        width: 100%;
        height: calc(~"100% - 40px");
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: center;
        .node-item {
          width: 50%;
          text-align: center;
          cursor: move;
          img {
            width: 100px;
            height: 100px;
          }
          .active {
            border-radius: 50px;
            border: 3px solid #4597ff;
          }
          div {
            color: #3d3d3d;
          }
        }
        .node-item:hover {
          img {
            border-radius: 50px;
            border: 3px solid #4597ff;
          }
        }
      }
    }
  }
}
</style>
