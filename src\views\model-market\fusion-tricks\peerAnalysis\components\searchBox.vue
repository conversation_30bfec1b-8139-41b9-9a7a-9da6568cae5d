/** * 所有搜索条件 */
<template>
  <div class="search_box">
    <div class="title">
      <p>{{ title }}</p>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <ul class="search_tab">
        <li
          v-for="(item, index) in tablist"
          :key="index"
          class="tabslist"
          @click="handleClickTab(index)"
          :class="{ active: tabIndex == index }"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="search_form">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="tabIndex == 4 ? 60 : 85"
        >
          <template v-if="tabIndex == 0">
            <ul class="search_form_type">
              <li
                v-for="(item, index) in typeList"
                :key="index"
                class="typeTab"
                :class="{ typeActive: typeIndex == index }"
                @click="handleTypeClick(index)"
              >
                {{ item.name }}
              </li>
            </ul>
            <template v-if="typeIndex == 0">
              <FormItem label="身份证号:" prop="searchContent">
                <Input
                  v-model="formData.searchContent"
                  placeholder="请输入"
                ></Input>
              </FormItem>
            </template>
            <template v-else>
              <ui-upload-img
                ref="uploadImg"
                :algorithmType="algorithmType"
                :value="urlList"
                size="mini"
                @imgUrlChange="imgUrlChange"
              ></ui-upload-img>
              <div class="slider-content">
                <span class="similarity">相似度:</span>
                <Slider v-model="similarity"></Slider>
                <span>{{ similarity }}%</span>
              </div>
            </template>
          </template>
          <template v-if="tabIndex == 1">
            <FormItem label="车辆号码:" prop="plateNo">
              <Input v-model="formData.plateNo" placeholder="请输入"></Input>
            </FormItem>
          </template>
          <template v-if="tabIndex == 2">
            <FormItem label="RFID编码:" prop="rfidCode">
              <Input v-model="formData.rfidCode" placeholder="请输入"></Input>
            </FormItem>
          </template>
          <template v-if="tabIndex == 3">
            <FormItem label="MAC地址:" prop="macCode">
              <Input v-model="formData.macCode" placeholder="请输入"></Input>
            </FormItem>
          </template>
          <template v-if="tabIndex == 4">
            <FormItem label="IMSI:" prop="imsiCode">
              <Input v-model="formData.imsiCode" placeholder="请输入"></Input>
            </FormItem>
          </template>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div
      class="footer"
      :class="{ packArrow: packUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
    </div>
  </div>
</template>

<script>
import uiUploadImg from "@/components/ui-upload-img/index";
import { mapActions, mapGetters } from "vuex";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "同行分析",
    },
    // 默认选中的类型
    selectTabIndex: {
      type: Number,
      default: -1,
    },
  },
  components: {
    uiUploadImg,
  },
  data() {
    return {
      tablist: [
        { name: "人员" },
        { name: "车辆" },
        { name: "RFID" },
        { name: "Wi-Fi" },
        { name: "电围" },
      ],
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      tabIndex: 0,
      typeList: [{ name: "身份证号" }, { name: "人脸照片" }],
      typeIndex: 0,
      formData: {
        searchContent: "",
        plateNo: "",
        rfidCode: "",
        macCode: "",
        imsiCode: "",
      },
      ruleValidate: {
        searchContent: [{ required: true, message: "请输入", trigger: "blur" }],
        plateNo: [{ required: true, message: "请输入", trigger: "blur" }],
        rfidCode: [{ required: true, message: "请输入", trigger: "blur" }],
        macCode: [{ required: true, message: "请输入", trigger: "blur" }],
        imsiCode: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      packUpDown: false,
      similarity: 85,
      algorithmType: "1",
      urlList: [],
    };
  },
  watch: {
    selectTabIndex: {
      handler(val) {
        this.tabIndex = val;
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
  },
  created() {},
  mounted() {
    // this.handleSimilar();
  },
  methods: {
    handleClickTab(index) {
      this.tabIndex = index;
      this.algorithmType = index + 1 + "";
      this.handleReset();
    },
    handleTypeClick(index) {
      this.urlList = [];
      this.formData = {
        searchContent: "",
        plateNo: "",
        rfidCode: "",
        macCode: "",
        imsiCode: "",
      };
      this.typeIndex = index;
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
      this.$emit("packBox");
      // console.log(document.querySelector('.search_box').scrollHeight, 222)
    },
    imgUrlChange(list) {
      this.urlList = list;
      this.$emit("imgUrlChange", this.urlList);
    },
    handleSearch() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.packUpDown = true;
          let params = { ...this.formData };
          let arr = [];
          if (this.urlList.length > 0) {
            this.urlList.forEach((ele) => {
              if (ele) {
                arr.push(ele.feature);
              }
            });
            params = {
              ...this.formData,
              features: arr,
              similarity: this.similarity,
            };
          }
          if (this.tabIndex == 0 && this.typeIndex == 1 && arr.length == 0) {
            //人员、人脸
            this.$Message.warning("请上传人脸照片！");
            return;
          }
          this.$emit("searchList", params, this.tabIndex, this.typeIndex);
        }
      });
    },
    // 获取相似度
    handleSimilar() {
      const { searchForPicturesDefaultSimilarity } = this.globalObj;
      this.similarity = Number(searchForPicturesDefaultSimilarity);
    },
    // 重置
    handleReset() {
      this.formData = {
        searchContent: "",
        plateNo: "",
        rfidCode: "",
        macCode: "",
        imsiCode: "",
      };
      this.urlList = [];
      this.handleSimilar();
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.search_box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  // min-height: 254px;
  overflow: hidden;
  // filter: blur(0px);
  .search_condition {
    // padding-bottom: 10px;
    max-height: 260px;
    transition: max-height 0.2s ease-out;
    overflow: hidden;
    .search_tab {
      display: flex;
      justify-content: space-evenly;
      border-bottom: 1px solid #d3d7de;
      margin-bottom: 15px;
      .tabslist {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        width: 25%;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;
      }
      .active {
        color: #2c86f8;
        border-bottom: 3px solid #2c86f8;
      }
    }
    .search_form {
      padding: 0 15px;
      &_type {
        display: flex;
        border: 1px solid #2c86f8;
        border-radius: 4px;
        margin-bottom: 15px;
        .typeTab {
          background: #ffffff;
          color: #2c86f8;
          width: 170px;
          height: 34px;
          text-align: center;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
        }
        .typeActive {
          background: #2c86f8;
          color: #fff;
        }
      }
      .btn-group {
        .btnwidth {
          width: 258px;
        }
      }
      /deep/ .ivu-form .ivu-form-item-label {
        font-size: 13px;
      }
    }
    .slider-content {
      margin: 15px 0;
      .similarity {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .search_condition-pack {
    max-height: 0px;
    transition: max-height 0.2s ease-out;
  }
  .footer {
    display: flex;
    justify-content: center;
    cursor: pointer;
    // margin: 10px 0;
    img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
  .packArrow {
    img {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
  /deep/ .ivu-form-item {
    margin-bottom: 25px;
  }
  /deep/ .ivu-slider {
    flex: 1;
  }
}
</style>
