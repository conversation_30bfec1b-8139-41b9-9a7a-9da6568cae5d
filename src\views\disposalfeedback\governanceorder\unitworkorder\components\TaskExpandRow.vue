<template>
  <div class="task-expand-box">
    <ui-table
      :show-header="false"
      :stripe="false"
      :table-columns="expandTableColumns"
      :table-data="taskList"
      disabled-hover
    >
      <template slot="rate" slot-scope="{ row }">
        <div class="flex-aic progress-box">
          <Progress :percent="returnPercent(row)" hide-info stroke-color="#2d8cf0" :stroke-width="6" class="mr-sm" />
          <span>{{ row.rate }}</span>
        </div>
      </template>
      <template slot="action" slot-scope="{ row }">
        <template v-if="row.operationList?.length">
          <ui-btn-tip
            v-for="(item, index) in row.operationList"
            :key="index"
            class="operatbtn"
            :icon="item.iconName"
            :content="item.name"
            @click.native="item.func(row)"
          ></ui-btn-tip>
        </template>
      </template>
    </ui-table>
  </div>
</template>
<script>
import { getTaskModeTableColumns } from '../enum';
export default {
  name: 'taskExpandRow',
  props: {
    taskList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      expandTableColumns: [],
    };
  },
  methods: {
    setExpandTableColumn() {
      this.expandTableColumns = getTaskModeTableColumns(this, true).filter((item) => item.isShow);
    },
    //返回进度条组件所需要的格式化掉百分号的数
    returnPercent(row) {
      if (!row.rate) {
        return 0;
      }
      if (typeof row.rate === 'string') {
        return row.rate.slice(0, -1) * 1;
      }
      if (typeof row.rate === 'number') {
        return row.rate * 100;
      }
    },
  },
  mounted() {
    this.setExpandTableColumn();
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.task-expand-box {
  @{_deep} .ivu-table {
    .ivu-table-overflowX {
      overflow-x: hidden;
    }
  }
  .progress-box {
    .ivu-progress {
      width: 140px;
    }
    @{_deep} .ivu-progress-inner {
      border: 1px solid var(--color-progress-default) !important;
      background: var(--bg-table-body-td) !important;
      padding: 2px;
    }
  }
  .operatbtn:not(:last-child) {
    margin-right: 15px;
  }
}
</style>
