<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />

    <TableCard ref="infoCard" :loadData="loadDataCard">
      <div slot="search" class="hearder-title">
        <SearchCard
          ref="search"
          style="margin: 0"
          @startSearch="startSearch"
          :selectList="[
            { id: 0, label: '轨迹未重复' },
            { id: 1, label: '轨迹重复' },
          ]"
        />
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <div class="carItem">
          <div class="item">
            <div class="img" @click="viewBigPic(row.trackLargeImage)">
              <ui-image :src="row.trackImage" />
              <div class="percentage" v-if="row.sameAmount">
                {{ similarityVal(row.sameAmount) }}
              </div>
              <p class="shadow-box" @click.stop="captureDetail(row)" style="z-index: 11" title="查看轨迹">
                <i class="icon-font icon-jianceguijishuliang search-icon mr-xs base-text-color"></i>
              </p>
            </div>
            <div class="group-message">
              <p class="marginP" :title="`抓拍时间：${row.shotTime || '暂无数据'}`">
                <i class="icon-font icon-shijian"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{ row.shotTime }}</span>
              </p>
              <p :title="row.catchPlace">
                <i class="icon-font icon-dizhi"></i>
                <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                  row.catchPlace ? row.catchPlace : '暂无数据'
                }}</span>
              </p>
            </div>
          </div>
        </div>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- 查看轨迹 -->
    <RepeatLocusMoadl ref="repeatLocusMoadl" />
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    SearchCard: require('@/components/track-detail-search.vue').default,
    ChartsContainer: require('../components/chartsContainer').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('../components/tableCard.vue').default,
    RepeatLocusMoadl: require('./component/repeat-locus-moadl.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      bigPictureShow: false,
      treeData: [],
      imgList: [],
      selectKey: '', // 机构树
      infoObj: {},
      abnormalCount: [
        {
          title: 'ZDR人员总量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
        },
        {
          title: 'ZDR人像轨迹总量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
        },
        {
          title: '检测轨迹数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
        },
        {
          title: '重复轨迹重复数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
        },
        {
          title: '未重复轨迹重复数量',
          icon: 'icon-a-guijizhunqueshuaijiance2',
          count: 0,
        },
      ],
      loadDataCard: (parameter) => {
        if (!this.taskObj.rootId || !this.taskObj.taskId) {
          console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              governanceevaluation.repeatList,
              Object.assign(
                parameter,
                {
                  resultId: this.taskObj.rootId,
                  indexId: this.currentTree.indexId,
                  taskId: this.taskObj.taskId,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
      },
    },
  },
  filter: {},
  created() {},
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    this.info();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    async info() {
      await this.static();
      await this.$refs.infoCard.info(true);
    },
    static() {
      if (!this.taskObj.rootId) {
        console.log('%c!!!重要。 由于rootId为必输项，如果为空，固不发出请求。', 'color:red;');
        return;
      }
      this.$http
        .post(governanceevaluation.repeatStatistics, {
          resultId: this.taskObj.rootId,
          indexId: this.currentTree.indexId,
          taskId: this.taskObj.taskId,
        })
        .then((res) => {
          let data = res.data.data || {};
          this.abnormalCount = [
            {
              title: 'ZDR人员总量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.importPersonNumbr || 0,
            },
            {
              title: 'ZDR人像轨迹总量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.importPersonTrajectoryNumbr || 0,
            },
            {
              title: '检测轨迹数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.detectionAmount || 0,
            },
            {
              title: '重复轨迹重复数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.repeatAmount || 0,
            },
            {
              title: '未重复轨迹重复数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.detectionAmount - data.repeatAmount || 0,
            },
          ];
        });
    },
    similarityVal(val) {
      return Number(val) > 100 ? '99+' : val;
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.selectKey = searchData.orgCodeList;
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
    // 查看全部轨迹
    captureDetail(item) {
      this.$refs.repeatLocusMoadl.info(item);
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
.carItem {
  height: 230px;
  margin: 10px 10px 0px 0;
  width: 13.5%;
  display: inline-block;
  .percentage {
    // width: 32px;
    height: 18px;
    padding: 0 4px;
    line-height: 18px;
    background: #2cabc6;
    color: #fff;
    text-align: center;
    position: absolute;
    top: 12px;
    right: -2px;
    z-index: 11;
  }
  .item {
    position: relative;
    height: 100%;
    background: #0f2f59;
    .num {
      position: absolute;
      right: 0;
      z-index: 100;
      padding: 10px;
      border-radius: 5px;
      background: rgba(42, 95, 175, 0.6);
    }
    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 8px;
      color: #8797ac;
    }
  }
}
</style>
