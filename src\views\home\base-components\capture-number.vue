<template>
  <!-- 人脸||车辆抓拍数量 -->
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: tendencyEchartData }">
    <!-- 在线变化趋势图 -->
    <draw-echarts :echart-style="echartStyle" :echart-option="echartOption" ref="videoOnlineEchart"></draw-echarts>
  </div>
</template>
<script>
import Vue from 'vue';
import CapturerNumberTooltip from '@/views/home/<USER>/capture-number-tooltip.vue';
import commonStyle from '@/views/home/<USER>/module/common-style';

import home from '@/config/api/home';
import { mapGetters } from 'vuex';

export default {
  name: 'CapturerNumber',
  props: {
    year: {
      default: '',
    },
    evaluationIndexResult: {},
    componentName: {
      default: '',
    },
    cuptureMonthOrDay: {
      default: 'month',
    },
    styleType: {},
  },

  data() {
    return {
      echartStyle: {
        width: '1000%',
        height: '1000%',
      },
      echartsLoading: false,
      echartOption: {},
      tendencyEchartData: [],
      tooltipFormatter: (data) => {
        let _t = this;
        let capturerNumberTooltip = Vue.extend(CapturerNumberTooltip);
        let _this = new capturerNumberTooltip({
          el: document.createElement('div'),
          data() {
            return {
              data,
              year: _t.year, // 格式：'2023-06'
              cuptureMonthOrDay: _t.cuptureMonthOrDay, //  month: 月趋势  date: 日趋势
            };
          },
          mounted() {
            window.openDetails = () => {
              _t.detailsCallback(data);
            };
          },
        });
        return _this.$el.outerHTML;
      },
      typeObj: {
        month: '1', // 月趋势
        date: '2', // 日趋势
      },
      deviceTypeObj: {
        VehicleCapturerNumber: '1', // 车辆卡口
        FaceCapturerNumber: '2', // 人脸卡口
      },
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  watch: {
    evaluationIndexResult: {
      handler(val) {
        if (!val?.length) return;
        this.initAll();
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    getIndexItem(indexModule) {
      return this.evaluationIndexResult.find((item) => item.indexModule === indexModule);
    },
    initAll() {
      this.initEchartsOption();
    },
    // 趋势图 数据
    async initEchartsOption() {
      try {
        let civilCode = this.evaluationIndexResult?.[0].civilCode;
        if (!civilCode) return;
        this.echartsLoading = true;
        let data = {
          type: this.typeObj[this.cuptureMonthOrDay],
          deviceType: this.deviceTypeObj[this.componentName],
          date: this.year,
          regionCode: civilCode, // 行政区划代码
        };

        let res = await this.$http.post(home.queryAllCaptureStatistics, data);
        let obj = res?.data?.data || {};
        this.tendencyEchartData = Object.values(obj);
        this.setEchartOption();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    // 处理 趋势图的 options
    setEchartOption() {
      let seriesData = [];
      this.tendencyEchartData.forEach((t) => {
        seriesData.push({
          ...t,
          value: t.num,
        });
      });
      let opts = {
        seriesData: seriesData,
        tooltipFormatter: this.tooltipFormatter,
        filterTime: this.year,
        cuptureMonthOrDay: this.cuptureMonthOrDay,
        tooltipBg: this.commonStyle.tooltipBg,
      };
      this.echartOption = this.$util.doEcharts.baseHomeCapturerNumber(opts);
    },
    // 月抓拍数量 详情   --  调转到对应的  日趋势 图表
    detailsCallback(data) {
      this.$emit('on-capture-number-details', data[0].data);
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
