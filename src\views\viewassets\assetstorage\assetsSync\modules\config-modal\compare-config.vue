<template>
  <div class="compare-config">
    <ui-label label="是否自动比对：" class="mb-lg">
      <RadioGroup v-model="searchParams.autoCompare">
        <Radio class="mr-lg" label="1">是</Radio>
        <Radio label="0">否</Radio>
      </RadioGroup>
      <span class="ml-lg explain">
        说明：设备同步后（注册到资产库前），系统自动按【设备编码】为主键和【资产库】中的设备进行比对！
      </span>
    </ui-label>
    <section class="compare-config-box">
      <span class="base-text-color">1、【新增设备、相同设备、差异设备】比对条件：</span>
      <p class="mt-md mb-md ml-sp">
        <ui-label class="inline mr-lg" label="功能类型">
          <Select
            v-model="compareAddType"
            placeholder="请选择功能类型"
            class="width-md"
            multiple
            clearable
            @on-change="($event) => handleSelectSearch('compareAddType')"
          >
            <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="来源标识">
          <Select v-model="searchParams.compareAddSource" placeholder="请选择来源标识" class="width-md" clearable>
            <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
          </Select>
        </ui-label>
      </p>
    </section>
    <section class="compare-config-box">
      <span class="base-text-color">2、【删除设备】比对条件：</span>
      <p class="mt-md mb-md ml-sp">
        <ui-label class="inline mr-lg" label="功能类型">
          <Select
            v-model="compareDelType"
            placeholder="请选择功能类型"
            class="width-md"
            clearable
            multiple
            @on-change="($event) => handleSelectSearch('compareDelType')"
          >
            <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline mr-lg" label="来源标识">
          <Select v-model="searchParams.compareDelSource" placeholder="请选择来源标识" class="width-md" clearable>
            <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
          </Select>
        </ui-label>
      </p>
    </section>
    <p class="mb-md ml-sp base-text-color">说明：本次同步的设备和资产库中满足以上比对条件的设备进行对比。</p>
    <div class="color-title mb-sm">请确定比对字段</div>
    <transfer-table
      class="transfer-table"
      :left-table-columns="columns1"
      :right-table-columns="columns2"
      :left-table-data="newPropertyList"
      :right-table-data="targetList"
      @onLeftToRight="selectionChange"
      :leftLoading="leftLoading"
      :rightLoading="rightLoading"
    >
    </transfer-table>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    propertyList: {
      default: () => [],
    },
    // 默认配置
    defaultParams: {},
  },
  data() {
    return {
      searchParams: {
        autoCompare: '0',
        compareAddSource: '',
        compareAddType: '',
        compareDelSource: '',
        compareDelType: '',
        compareParam: '',
      },
      compareAddType: [],
      compareDelType: [],
      columns1: [
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      columns2: [
        { title: ' ', align: 'center', width: 20 },
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
      ],
      targetList: [],
      leftLoading: false,
      rightLoading: false,
      extraParam: { detectionRange: { isDetect: '0', mode: 'all' } },
      indexConfig: {},
      defaultRep: [
        { checkColumnName: 'deviceId', checkColumnValue: '设备编码', repeatCount: 2 },
        { checkColumnName: 'macAddr', checkColumnValue: 'MAC地址', repeatCount: 2 },
      ],
      newPropertyList: [],
    };
  },
  created() {
    if (!this.propertySearchLbdwlx.length) this.getAlldicData();
    this.newPropertyList = this.$util.common.deepCopy(this.propertyList);
    // 默认deviceId必须选择
    let Item = this.newPropertyList.find((item) => {
      return item.propertyName === 'deviceId';
    });
    this.targetList.push({
      checkColumnName: Item.propertyName,
      checkColumnValue: Item.propertyColumn,
    });
    this.$set(Item, '_checked', true);
    this.$set(Item, '_disabled', true);
    this.selectionChange([Item]);
    this.defaultParams ? this.handleIsDefault() : null;
  },
  activated() {},
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    selectionChange(selection) {
      this.targetList = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        return obj;
      });
      this.searchParams.compareParam = JSON.stringify(
        selection.map((item) => {
          let obj = {};
          obj.fieldName = item.propertyName;
          obj.fieldRemark = item.propertyColumn;
          return obj;
        }),
      );
    },
    handleSelectSearch(type) {
      this.searchParams[type] = this[type].join(',');
    },
    handleIsDefault() {
      // 处理默认配置
      this.searchParams.compareAddSource = this.defaultParams.compareAddSource || '';
      this.searchParams.compareAddType = this.defaultParams.compareAddType || '';
      this.searchParams.compareDelSource = this.defaultParams.compareDelSource || '';
      this.searchParams.compareDelType = this.defaultParams.compareDelType || '';
      this.searchParams.autoCompare = this.defaultParams.autoCompare || '0';
      this.compareAddType = this.defaultParams.compareAddType.split(',');
      this.compareDelType = this.defaultParams.compareDelType.split(',');
      if (!this.defaultParams.compareParam) return;
      let compareParamList = JSON.parse(this.defaultParams.compareParam);
      let targetListObject = {};
      // 处理右侧数据
      this.targetList = compareParamList.map((item) => {
        targetListObject[item.fieldName] = item;
        let one = {};
        one.checkColumnName = item.fieldName;
        one.checkColumnValue = item.fieldRemark;
        return one;
      });
      // 处理参数
      this.searchParams.compareParam = JSON.stringify(
        compareParamList.map((item) => {
          // let obj = {}
          // obj.fieldName = item.propertyName
          // obj.fieldRemark = item.propertyColumn
          return item;
        }),
      );
      // 处理左侧的选中
      this.newPropertyList.forEach((item) => {
        if (item.propertyName in targetListObject) {
          this.$set(item, '_checked', true);
        }
      });
    },
  },
  watch: {
    searchParams: {
      handler() {
        this.searchParams.compareAddType = this.compareAddType.join(',');
        this.searchParams.compareDelType = this.compareDelType.join(',');
        let params = {
          ...this.searchParams,
        };
        this.searchParams.compareAddSource === undefined ? (params.compareAddSource = '') : null;
        this.searchParams.compareDelSource === undefined ? (params.compareDelSource = '') : null;
        this.$emit('updateParams', params);
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ml-sp {
  margin-left: 22px;
}
.compare-config {
  padding: 0 20px;
  height: 705px;
  &-box {
    color: #fff;
  }
  .transfer-table {
    height: 500px;
    @{_deep} .left-table {
      height: calc(100% - 40px) !important;
    }
    @{_deep} .right-table {
      height: calc(100% - 40px) !important;
    }
  }
}
.explain {
  color: rgb(226, 135, 43);
}
.color-title {
  color: var(--color-display-title);
}
</style>
