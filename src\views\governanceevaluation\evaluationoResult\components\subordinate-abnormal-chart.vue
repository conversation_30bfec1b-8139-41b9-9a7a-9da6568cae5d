<template>
  <div class="subordinate-chart">
    <div class="ranking-title f-14">
      <span class="icon-font icon-xiajipaihang mr-xs"></span>
      <span>下级地市异常统计</span>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: resultData }">
      <draw-echarts
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="resultData.length > 15">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('propertyChart', resultData, [], 15)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import dataZoom from '@/mixins/data-zoom';
import detectionResult from '@/config/api/detectionResult';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'subordinate-chart',
  mixins: [dataZoom, dealWatch],
  data() {
    return {
      propertyEchart: {},
      echartsLoading: false,
      echartData: [[], [], []],
      colorList: {
        errorNoData: $var('--color-blue-25'),
        errorTodayNoData: $var('--color-blue-26'),
        errorTooLessData: $var('--color-blue-27'),
        errorDataSwoop: $var('--color-purple-17'),
      },
      lengName: [],
      echartList: [],
      xAxis: [],
      series: [],
      sortField: false,
      reasonLoading: false,
      rankInfoList: [],
      resultData: [],
    };
  },
  props: {},
  mounted() {
    this.startWatch(
      '$route.query',
      async () => {
        await this.getStatisticalList();
        this.typeRing();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async getStatisticalList() {
      this.echartsLoading = true;
      const { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
      let params = {
        // access: 'REPORT_MODE', // 查询入口:查询入口TASK_RESULT,任务入口;REPORT_MODE:评测概览,可用值:TASK_RESULT,EXAM_RESULT,REPORT_MODE
        batchId: batchId,
        indexId: indexId,
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        pageNumber: 1,
        pageSize: 999,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(detectionResult.getStatInfoList, params);
        this.resultData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    typeRing() {
      const { statisticType } = this.$route.query;
      let xAxis = [];
      let lengName = {
        errorNoData: '历史无抓拍',
        errorTodayNoData: '昨日无抓拍',
        errorTooLessData: '抓拍数量过少',
        errorDataSwoop: '抓拍数量突降',
      };
      let echartData = {
        errorNoData: [],
        errorTodayNoData: [],
        errorTooLessData: [],
        errorDataSwoop: [],
      };
      this.resultData.forEach((item) => {
        xAxis.push(statisticType === 'REGION' ? item.civilName : item.orgName);
        Object.keys(echartData).forEach((key) => {
          echartData[key].push(item.detail[key]);
        });
      });
      let series = Object.keys(lengName).map((key) => {
        return {
          name: lengName[key],
          data: echartData[key],
          barWidth: '16px',
          barCategoryGap: '3%',
          type: 'bar',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[key] },
        };
      });
      let opts = {
        xAxis: xAxis,
        series: series,
        lengName: Object.values(lengName),
      };
      this.propertyEchart = this.$util.doEcharts.SubordinateAbnormalChart(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], 15);
      });
    },
  },
  computed: {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.subordinate-chart {
  position: relative;
  height: 100%;
  box-shadow: var(--shadow-sub-echarts-content);
  background: var(--bg-sub-echarts-content);
  border-right: 4px;
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
