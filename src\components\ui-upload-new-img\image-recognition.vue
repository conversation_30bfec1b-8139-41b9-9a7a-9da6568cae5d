<template>
  <!-- 新增编辑数据源 -->
  <ui-modal
    v-model="modalShow"
    :title="dialogData.title"
    :r-width="dialogData.rWidth"
    ref="iModal"
    :loading="btnLoading"
    @onOk="submitHandle"
    @onCancel="destroyModal"
  >
    <p v-if="copperList.length > 1" class="tips color-warning">
      检测出图片有多个目标，请选择要识别的目标！
    </p>
    <p v-else-if="copperList.length == 0" class="tips color-error">
      未检测出图片目标！
    </p>
    <div class="img-container">
      <img
        :src="tempUrl"
        alt=""
        id="nodeBox"
        v-if="tempUrl"
        @load="loadImage"
      />
      <span
        v-for="(item, index) of copperList"
        class="select-preview"
        :class="selectedIndex === index && 'selected'"
        :key="index"
        @click="selectPreviewHandle(item, index)"
        :style="{
          left: item.x + 'px',
          top: item.y + 'px',
          width: item.width + 'px',
          height: item.height + 'px',
        }"
      >
        <Icon type="ios-checkmark" />
      </span>
    </div>
  </ui-modal>
</template>
<script>
import html2canvas from "html2canvas";
import { cloneDeep } from "lodash";
import { uploadPicToMinio } from "@/api/wisdom-cloud-search";

export default {
  name: "SearchPictures",
  props: {
    // 关闭
    close: {
      type: Function,
      default: () => {},
    },
    // 识别列表
    dataCopper: {
      type: Array,
      default: () => [],
    },
    tempUrl: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      btnLoading: false,
      copperList: [],
      selectedIndex: -1,
      targetCopperData: null,
      modalShow: true,
      dialogData: {
        title: "选择目标",
        rWidth: 1000,
      },
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.modalShow = true;
      this.selectedIndex = -1;
      this.targetCopperData = null;
    },
    loadImage() {
      const imgBox = document.getElementById("nodeBox");
      const nw = imgBox.naturalWidth;
      const nh = imgBox.naturalHeight;
      const w = parseInt(window.getComputedStyle(imgBox).width);
      const h = parseInt(window.getComputedStyle(imgBox).height);
      const rateW = w / nw;
      const rateH = h / nh;
      let cloneData = cloneDeep(this.dataCopper);
      // 设置了#nodeBox的max-length为600，宽度最宽为960
      if (nh < 600 && nw < 960) {
        this.copperList = cloneData.map((item) => {
          return item;
        });
      } else {
        this.copperList = cloneData.map((item) => {
          item.x = item.x * rateW;
          item.y = item.y * rateH;
          item.width = item.width * rateW;
          item.height = item.height * rateH;
          return item;
        });
        this.$forceUpdate();
      }

      // 只有1个目标，默认选中
      if (this.copperList.length == 1) {
        this.targetCopperData = this.copperList[0];
        this.selectedIndex = 0;
      }
    },
    search() {
      const { value, uploadList } = this;
      const params = {
        value,
        uploadList,
      };
    },
    /**
     * 选中图片
     */
    selectPreviewHandle(data, index) {
      this.targetCopperData = data;
      this.selectedIndex = index;
    },
    setImage() {
      const targetCopperData = this.targetCopperData;
      if (!targetCopperData) {
        this.$Message.warning("请选择识别的目标");
        return false;
      }
      this.modalShow = false;
      this.destroyModal();
      this.$emit("getMinImgUrl", targetCopperData.initData);
      //   const that = this
      //   this.btnLoading = true
      //   const cropperW = targetCopperData.width
      //   const cropperH = targetCopperData.height
      //   const cropperX = targetCopperData.x
      //   const cropperY = targetCopperData.y
      //   html2canvas(document.getElementById('nodeBox'))
      // .then((canvas) => {
      //     var fileUrl = canvas.toDataURL('image/jpeg')
      //     let aLink = document.createElement("a");
      //         aLink.style.display = "none";
      //         aLink.href = fileUrl;
      //     window.localStorage.setItem("src", aLink.href);
      // })
      // html2canvas配置项
      //     html2canvas(document.getElementById('nodeBox'), {
      //         width: cropperW,
      //         height: cropperH,
      //         scale: 2,
      //         x: cropperX,
      //         y: cropperY,
      //         useCORS: true
      //   }).then(function (canvas) {
      // that.modalShow = false
      // that.btnLoading = false
      // 图片转base64方式
      // var fileUrl = canvas.toDataURL('image/jpeg', 1)
      // that.$emit('getMinImgUrl', {
      //     fileUrl, feature: targetCopperData.feature,imageBase: targetCopperData.imageBase
      // })
      // 接口上传方式
      // const fileBlob = that.getBlob(canvas)
      //   const fileData = new FormData()
      //   fileData.append('file', fileBlob)
      //   uploadPicToMinio(fileData)
      //     .then(res => {
      //       const fileUrl = res.data.fileUrl
      //       that.$emit('getMinImgUrl', { fileUrl, feature: targetCopperData.feature })
      //       that.modalShow = false
      //     })
      //     .finally(() => {
      //       that.btnLoading = false
      //     })
      // })
    },
    getBlob(canvas) {
      //获取blob对象
      var data = canvas.toDataURL("image/jpeg", 1);
      data = data.split(",")[1];
      data = window.atob(data);
      var ia = new Uint8Array(data.length);
      for (var i = 0; i < data.length; i++) {
        ia[i] = data.charCodeAt(i);
      }
      return new Blob([ia], {
        type: "image/jpeg",
      });
    },
    submitHandle() {
      this.setImage();
    },
    // 销毁弹框组件，因为在loadImage中使用id获取了元素，不销毁会导致存在id相同的元素
    destroyModal() {
      this.$emit("destroy");
    },
  },
};
</script>
<style lang="less" scoped>
.img-container {
  position: relative;
  float: left;
  left: 50%;
  transform: translateX(-50%);
  img {
    display: block;
    max-width: 100%;
    height: auto;
  }
  .select-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: 90px;
    height: 90px;
    background: rgba(242, 230, 76, 0.2);
    border-radius: 4px;
    border: 2px dashed #f2e64c;
    .ivu-icon {
      color: #fff;
      font-size: 20px;
      background: #2c86f8;
      border-radius: 50%;
      margin-top: 5px;
      margin-left: 5px;
      opacity: 0;
    }
    &:hover {
      border: 2px solid #2c86f8;
      background: rgba(44, 134, 248, 0.3);
    }
    &.selected {
      background: rgba(44, 134, 248, 0.3);
      border: 2px solid #2c86f8;
      .ivu-icon {
        opacity: 1;
      }
    }
  }
}
.tips {
  font-size: 16px;
  margin-bottom: 15px;
  text-align: center;
}
#nodeBox {
  width: auto;
  max-height: 600px;
}
/deep/ .ivu-modal-body {
  overflow: auto;
}
</style>
