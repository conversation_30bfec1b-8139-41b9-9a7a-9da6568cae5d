import { getParamDataByKeys, queryParamDataByKeys } from "@/api/config";
import { getTargetObjParamkeyMap } from "@/api/monographic/base.js";

const dictStateValList = {
  ICBD_RELATION_ENTITY_CARD: "relationEntityCard",
  ICBD_GLOBAL_CONFIG: "globalObj",
  ICBD_MAP_CONFIG: "mapObj",
  ICBD_ARCHIVES_CONFIG: "archiveObj",
  ICBD_TARGET_CONTROL: "targetObj",
  ICBD_RELATION_CONFIG: "relationObj",
  ICBD_SEARCH_CONFIG: "searchObj",
  ICBD_WORKBENCH_CONFIG: "workbenchObj",
  ICBD_TACTICAL_CONFIG: "faceObj",
  ICBD_WITH_LABEL: "labelsObj",
  ICBD_WITH_GRAPH: "graphObj",
  ICBD_WITH_LLM: "xzObj",
  ICBD_LLM_CONFIG: "xzLLMObj",
  ICBD_LLM_TASK_CONFIG: "semanticObj",
  ICBD_RELATION_QUERY_CONFIG:'relationQueryObj'
};
const targetObjKey = getTargetObjParamkeyMap();
if (targetObjKey) dictStateValList[targetObjKey] = "targetObj";

export default {
  namespaced: true,
  state: {
    relationEntityCard: {}, // 数智立方-实体详情字段展示顺序
    globalObj: {}, // 全局设置
    mapObj: {}, // 图上作战
    archiveObj: {}, // 全息档案
    targetObj: {}, // 目标管控
    relationObj: {}, // 数智立方
    searchObj: {}, // 智慧云搜
    workbenchObj: [], // 我的收藏
    faceObj: {}, // 模型集市
    labelsObj: {}, // 档案标签
    graphObj: {}, //图谱
    xzObj: {}, //小智
    xzLLMObj: {}, // 小智llm
    semanticObj: {}, // 语义布控
  },
  getters: {
    relationEntityCard(state) {
      return state.relationEntityCard;
    },
    globalObj(state) {
      return state.globalObj;
    },
    mapObj(state) {
      return state.mapObj;
    },
    archiveObj(state) {
      return state.archiveObj;
    },
    targetObj(state) {
      return state.targetObj;
    },
    relationObj(state) {
      return state.relationObj;
    },
    searchObj(state) {
      return state.searchObj;
    },
    workbenchObj(state) {
      return state.workbenchObj;
    },
    faceObj(state) {
      return state.faceObj;
    },
    labelsObj(state) {
      return state.labelsObj == "1";
    },
    graphObj(state) {
      return state.graphObj == "1";
    },
    xzObj(state) {
      return state.xzObj;
    },
    getXzLLMObj(state) {
      return state.xzLLMObj;
    },
    getSemanticObj(state) {
      return state.semanticObj;
    },
    getRelationQueryObj(state) {
      return state.relationQueryObj;
    },
  },

  mutations: {
    setAllDictData(state, { data }) {
      data.map((val) => {
        state[dictStateValList[val.paramKey]] = val.paramValue
          ? JSON.parse(val.paramValue)
          : null;
      });
    },
    setDictData(state, { data, typeKey }) {
      state[dictStateValList[typeKey]] = data;
    },
  },
  actions: {
    /**
     * 批量获取字典值
     * @param {*} param0
     * @param {*} status  0: 默认， 1: 强制再次请求
     */
    getSystemAllData({ state, commit }, status = 0) {
      return new Promise((resolve, reject) => {
        // const isRequested = Object.keys(dictStateValList).some(key => state[dictStateValList[key]].length > 0)
        // 判断如果 请求过 不在进行接口请求，强制再次请求除外
        if (state.globalObj.watermarkSwitch && status == 0) {
          resolve();
          return false;
        }
        const dictCodeList = Object.keys(dictStateValList);
        queryParamDataByKeys(dictCodeList)
          .then((res) => {
            commit("setAllDictData", { data: res.data });
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    },
    // 设置单个字典值
    async getSystemData({ state, commit }, typekey) {
      const isRequested = state[dictStateValList[typekey]].length > 0;
      if (isRequested) {
        return false;
      } // 判断如果 请求过 不在进行接口请求
      let { data } = await getParamDataByKeys(typekey);
      state[dictStateValList[typekey]] = data.paramValue
        ? JSON.parse(data.paramValue)
        : null;
      return data;
    },
  },
};
