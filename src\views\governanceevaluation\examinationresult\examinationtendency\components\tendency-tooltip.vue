<template>
  <div class="tendency-tooltip">
    <p class="f-14 mb-md">{{ civilName }}</p>
    <p class="column-width mb-sm">
      <span class="span-item">月份</span>
      <span class="span-item">分数</span>
      <span>全省排名</span>
    </p>
    <div v-for="(item, index) in childrenDatas" :key="index" class="column-width mb-sm f-12">
      <div class="month-block flex-aic">
        <span class="color-box mr-xs" :style="{ background: colors[index] }"></span>
        <span>{{ item.month }}月</span>
      </div>
      <span class="span-item">{{ item.score }}分</span>
      <span>{{ item.rank }}名</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'tendency-tooltip',
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.tendency-tooltip {
  color: var(--color-content);
  padding-left: 10px;
  font-size: 12px;
  max-height: 260px;
  padding-right: 20px;
  overflow: auto;
  .color-box {
    display: inline-block;
    height: 12px;
    width: 12px;
  }
  .column-width {
    > span {
      display: inline-block;
    }
    > span:not(:last-child) {
      width: 110px;
    }
    > .month-block {
      width: 110px;
      display: inline-block;
    }
  }
}
</style>
