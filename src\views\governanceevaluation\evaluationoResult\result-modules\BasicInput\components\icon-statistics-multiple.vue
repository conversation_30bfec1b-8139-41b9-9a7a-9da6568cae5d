<template>
  <div class="information-statistics">
    <ul class="statistics-ul" ref="ulRef">
      <li v-for="(item, index) in statisticsList" :key="index" class="mb-sm" :class="[item.liBg]" :style="nowListyle">
        <div class="monitoring-data">
          <!--          <div class="square"></div>-->
          <!--          <div class="square-desc base-text-color" v-html="item.name"></div>-->
          <p class="monitoring-icon" :class="{ 'monitoring-data-icon': isIconBg }">
            <i class="icon-font f-40 f-55" :class="[item.icon, item.iconColor]"></i>
          </p>
          <div class="ml-md base-text-color statistics-detail">
            <p v-for="val in item.detail" :key="val.name" class="detail-wrapper">
              <span class="mr-xs name">{{ val.name }}</span>
              <span :class="item.textColor">{{ val.value || 0 }}</span>
            </p>
          </div>
          <slot :name="item.key" :row="item"></slot>
        </div>
        <span
          :class="['icon-font', item.qualified ? 'icon-dabiao' : 'icon-budabiao']"
          v-if="item.qualified === true || item.qualified === false"
        ></span>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'statistics',
  components: {},
  props: {
    // isflexfix为true就不传
    listyle: {
      default: () => {
        return {
          height: `${100 / 192}rem`,
          width: `${235 / 192}rem`,
        };
      },
    },
    // 设置一行内展示不折行
    isflexfix: {
      default: true,
    },
    // 图标是否有圆圈背景
    isIconBg: {
      default: false,
    },
    /**
     * statisticsList: [
     {
          name: '应检测设备数量',
          value: 0,
          icon: 'icon-wufajianceshebeishuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',// number数字 percentage 百分比
        },
     ]
     */
    statisticsList: {
      type: Array,
      required: true,
      default: () => [],
    },
  },
  data() {
    return {
      nowListyle: {
        height: '100px',
        width: '235px',
      },
    };
  },
  computed: {},
  watch: {
    statisticsList: {
      handler(val) {
        this.nowListyle = this.listyle;
        if (this.isflexfix) {
          this.$nextTick(() => {
            this.nowListyle.width = `${(this.$refs.ulRef.offsetWidth - (val.length - 1) * 10) / val.length}px`;
            this.nowListyle.height = '100%';
            // 不是一行展示，需要展示大字
            this.$refs.statisticNumRef.forEach((item) => {
              item.style.fontSize = `${30 / 192}rem`;
            });
          });
        }
      },
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.information-statistics {
  display: flex;
  margin-right: 10px;
  width: 100%;
  height: 100%;
  .statistics-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    li {
      position: relative;
      align-items: center;
      margin-right: 10px;
      background: #ebb225;
      display: flex;
      padding: 0 30px;
      border-radius: 4px;
      &:nth-last-child(1) {
        margin-right: 0;
      }
      .monitoring-data-icon {
        // background: rgba(200, 200, 200, 0.1);
        background: rgba(158, 217, 216, 0.08);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 80px;
      }
      .monitoring-data {
        // height: 48px;
        width: 100%;
        // border-right: 1px solid #094a8a;
        // line-height: 48px;
        display: flex;
        // display: -webkit-box;
        //justify-content: center;
        align-items: center;
        i {
          display: inline-block;
          // height: 48px;
          // width: 48px;
          // line-height: 48px;
          // text-align: center;
          // border-radius: 50%;
          // background: rgba(218, 245, 248, 0.2);
        }
        .f-40 {
          font-size: 40px;
        }
        span {
          display: inline-block;
          flex: 1;
          text-align: left;
          p {
            white-space: nowrap;
            font-style: normal;
            font-size: 14px;
          }
          .statistic-num {
            font-size: 20px;
            font-family: Microsoft YaHei;
            font-weight: bold;
          }
        }
        .square {
          position: absolute;
          width: 0;
          height: 0;
          border: 80px/2 solid transparent;
          border-top: 80px/2 solid #8943d0;
          transform: rotate(136deg);
          left: -39px;
          top: -41px;
        }
        .square-desc {
          position: absolute;
          top: 2px;
          left: 2px;
        }
      }
      .statistics-detail {
        width: 100%;
        .detail-wrapper {
          // display: flex;
        }
        .name {
          min-width: 50px; /*no*/
        }
      }
    }
    .icon-budabiao,
    .icon-dabiao {
      color: var(--color-failed);
      font-size: 16px;
      line-height: 16px;
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
    .icon-dabiao {
      color: var(--color-success);
    }
    .icon-bg1 {
      background: var(--icon-card-gradient-cyan);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg2 {
      background: var(--icon-card-gradient-orange);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg3 {
      background: var(--icon-card-gradient-green);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg4 {
      background: var(--icon-card-gradient-light-pink);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg5 {
      background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg6 {
      background: var(--icon-card-gradient-deep-purple);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg7 {
      background: var(--icon-card-gradient-purple);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg8 {
      background: var(--bg-card-grass-green);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg9 {
      background: linear-gradient(180deg, #19b6f2 0%, #0e7f9b 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg10 {
      background: linear-gradient(180deg, #a3aaf8 0%, #767cb9 75%, #676da4 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg11 {
      background: var(--icon-card-gradient-blue);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-bg13 {
      background: linear-gradient(180deg, #b58e0f 0%, #bb6603 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .color1 {
      color: var(--font-card-cyan);
    }
    .color2 {
      color: var(--font-card-orange);
    }
    .color3 {
      color: var(--font-card-green);
    }
    .color4 {
      color: var(--font-card-light-pink);
    }
    .color5 {
      color: #4ba0f5;
    }
    .color6 {
      color: var(--font-card-deep-purple);
    }
    .color7 {
      color: var(--font-card-purple);
    }
    .color8 {
      color: #b4c519;
    }
    .color9 {
      color: #19b6f2;
    }
    .color10 {
      color: #a3aaf8;
    }
    .color11 {
      color: var(--font-card-blue);
    }
    .color13 {
      color: #ebb225;
    }
    .li-bg1 {
      background: var(--bg-card-cyan);
      box-shadow: var(--bg-card-cyan-shadow);
      .monitoring-data-icon {
        background: #1c506d;
      }
    }
    .li-bg2 {
      background: var(--bg-card-orange);
      box-shadow: var(--bg-card-orange-shadow);
      .monitoring-data-icon {
        background: #464e49;
      }
    }
    .li-bg3 {
      background: var(--bg-card-green);
      box-shadow: var(--bg-card-green-shadow);
      .monitoring-data-icon {
        background: #175150;
      }
    }
    .li-bg4 {
      background: var(--bg-card-light-pink);
      box-shadow: var(--bg-card-light-pink-shadow);
      .monitoring-data-icon {
        background: #3f374f;
      }
    }
    .li-bg5 {
      background: var(--bg-card-blue);
      box-shadow: var(--bg-card-blue-shadow);
      .monitoring-data-icon {
        background: #10497a;
      }
      .statistic-num {
        color: var(--color-primary);
      }
    }
    .li-bg6 {
      background: var(--bg-card-deep-purple);
      box-shadow: var(--bg-card-deep-purple-shadow);
      .monitoring-data-icon {
        background: #332871;
      }
    }
    .li-bg7 {
      background: var(--bg-card-purple);
      box-shadow: var(--bg-card-purple-shadow);
    }
    .li-bg8 {
      background: var(--icon-card-gradient-grass-green);
      box-shadow: var(--bg-card-grass-green-shadow);
    }
    .li-bg9 {
      background: rgba(15, 167, 245, 0.21);
    }
    .li-bg10 {
      background: rgba(199, 127, 201, 0.21);
    }
    .li-bg11 {
      background: var(--bg-card-blue);
      box-shadow: var(--bg-card-blue-shadow);
    }
    .f-red {
      background: linear-gradient(180deg, #f24e2c 0%, #772c0a 100%) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }
    .f-green {
      background: linear-gradient(360deg, #26d82c 0%, #127d0a 100%) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
    }
  }
}
</style>
