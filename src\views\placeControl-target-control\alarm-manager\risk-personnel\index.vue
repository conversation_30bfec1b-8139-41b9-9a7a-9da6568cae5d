<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:43:20
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\active-people\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="riskyBehavior"
      :radioList="[]"
      noMore
    >
      <query ref="slotQuery" />
    </searchForm>

    <!-- 列表 -->
    <div class="list">
      <div v-for="(item, index) in tableList" :key="index">
        <CardItem
          class="together-alarm"
          :data="item"
          @click.native="handleDetailFn(item, index)"
        ></CardItem>
      </div>
      <ui-empty v-if="tableList.length === 0"></ui-empty>
      <ui-loading v-if="tableLoading"></ui-loading>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>

    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="tableList"
    ></CaptureDetail>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import { abnormalBehaviorPageList } from "@/api/monographic/place.js";
import CardItem from "./components/card-item.vue";
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
export default {
  name: "",
  components: { searchForm, query, CardItem, CaptureDetail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
    },
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {
    this.query();
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableLoading: false,
    };
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 获取查询参数，并进行调整
     */
    getQueryParams() {
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
      };
      data = {
        ...data,
        libBizType: data.operationType == "" ? 0 : data.operationType,
      };
      delete data.operationType;
      return data;
    },
    /**
     * @description: 获取报警列表
     */
    tableListFn(param = {}) {
      this.tableLoading = true;
      let data = this.getQueryParams();
      abnormalBehaviorPageList({ ...data, ...param, ...this.params })
        .then((res) => {
          this.total = res.data?.total;
          this.tableList =
            res.data?.entities?.map(({ faceCapture, ...item }) => ({
              ...item,
              ...faceCapture,
            })) || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    // 跳转档案
    goArchivesInfo(item) {
      const { href } = this.$router.resolve({
        path: "/community-archive/people-dashboard",
        query: {
          archiveNo: item.idCardNo,
          source: "people",
          initialArchiveNo: item.idCardNo,
        },
      });
      window.open(href, "_blank");
    },
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.showList(index);
    },
  },
};
</script>

<style lang="less" scoped>
.primary {
  color: #2c86f8;
}
.warning {
  color: #ea4a36;
}
.click-point {
  cursor: pointer;
}
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    overflow: scroll;
    margin-top: 20px;
    gap: 10px;
    position: relative;
    .card-item {
      cursor: pointer;
    }
  }
}
</style>
