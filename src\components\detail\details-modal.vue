<!--
    * @FileDescription: 人体详情 非机动车
    * @Author: H
    * @Date: 2023/5/16
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-23 14:09:56
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="thumbnail">
              <img
                v-lazy="datailsInfo.traitImg || datailsInfo.sceneImg"
                alt=""
              />
              <span class="similarity" v-if="datailsInfo.similarity"
                >{{ datailsInfo.similarity }}%</span
              >
            </div>
            <div class="record-title" v-if="detailType == 1">
              <span class="active">抓拍记录</span>
            </div>
            <div class="record-title" v-else>
              <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)"
                >通过记录</span
              >
              <span
                class="record-right"
                :class="{ active: checkIndex == 1 }"
                @click="tabsChange(1)"
                >人车关联</span
              >
            </div>
            <div class="through-record" v-if="checkIndex == 0">
              <div class="wrapper-content">
                <span class="label">{{
                  detailType == 1 ? "抓拍地点" : "通过地点"
                }}</span
                >：
                <!-- <span class="message" v-show-tips>{{ datailsInfo.deviceName || '--'}}</span> -->
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-html="datailsInfo.deviceName || datailsInfo.captureAddress"
                  @click="handlePlace(datailsInfo)"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : datailsInfo.captureAddress
                  "
                ></span>
              </div>
              <div class="wrapper-content">
                <span class="label">{{
                  detailType == 1 ? "抓拍时间" : "通过时间"
                }}</span
                >：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in showList"
                :key="index"
              >
                <span
                  class="label"
                  :class="{ maxLabel: item.title.length > 4 }"
                  >{{ item.title }}</span
                ><span>：</span>
                <span class="message" v-if="item.dictionary == 'sbgnlxList'">
                  <span
                    v-if="!Array.isArray(facilitySplit(datailsInfo[item.key]))"
                  >
                    {{
                      datailsInfo[item.key]
                        | commonFiltering(translate(item.dictionary))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(datailsInfo[item.key])"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate(item.dictionary))
                    }}{{
                      inde + 1 < facilitySplit(datailsInfo[item.key]).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </span>
                <span class="message" v-else-if="item.type == 'filter'">{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
                <span class="message" v-else-if="item.type == 'wear'">{{
                  handleWear(datailsInfo[item.key])
                }}</span>
                <span class="message" v-else>{{
                  datailsInfo[item.key] || "--"
                }}</span>
              </div>
            </div>
            <div class="through-record" v-else>
              <div class="rcgl">
                <div class="title">关联人脸:</div>
                <span
                  class="imgBox"
                  v-for="item in linkFaces"
                  :key="item.id"
                  @click="toSearch(item, 'face')"
                >
                  <img v-lazy="item.traitImg" alt="" />
                </span>
                <div class="title">关联人体:</div>
                <span
                  class="imgBox"
                  v-for="item in linkHumans"
                  :key="item.id"
                  @click="toSearch(item, 'body')"
                >
                  <img v-lazy="item.traitImg" alt="" />
                </span>
              </div>
            </div>
            <!-- <div class="structuring">
                            <p class="structuring-title">结构化信息:</p>
                            <ul class="struct-box-ul">
                                <li v-for="( item, index) in showList" :key="index" class="struct-box-list">
                                    <p class="struct-title">{{ item.title }}</p><span>:</span>
                                    <p class="struct-content" v-if="item.type == 'filter'">{{ datailsInfo[item.key] | commonFiltering(translate(item.dictionary)) }}</p>
                                    <p class="struct-content" v-else-if="item.type == 'wear'">{{ handleWear(datailsInfo[item.key]) }}</p>
                                    <p class="struct-content" v-else>{{ datailsInfo[item.key] || '--' }}</p>
                                </li>
                            </ul>
                        </div> -->
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleLeft"
            ></i>
            <details-largeimg
              :boxSeleType="collectionType == 16 ? 'rect' : 'vehicleRect'"
              :info="datailsInfo"
              @collection="collection"
              :collectionType="collectionType"
              :algorithmType="collectionType == 16 ? 3 : 4"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                'sc',
                'dmt',
              ]"
              @onAction="multilAnalysisHandler"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleRight"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleLeft"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="index"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg || item.sceneImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleRight"
        ></i>
      </footer>
    </div>
    <detailMutiAnalysis
      v-if="isShowMutilAnalysis"
      ref="detailMutiAnalysisRef"
      :tableList="multiDataList"
      :isNoPage="true"
      @close="isShowMutilAnalysis = false"
    ></detailMutiAnalysis>
  </div>
</template>

<script>
import detailsLargeimg from "./details-largeimg.vue";
import { homanBodyList, nonmotorList } from "./structuring.js";
import { commonMixins } from "@/mixins/app.js";
import { cutMixins } from "./mixins.js";
import { getNonmotorCaptureLink } from "@/api/wisdom-cloud-search";
import { mutilMixins } from "./mutil-mixins.js";
import detailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
export default {
  name: "",
  mixins: [commonMixins, cutMixins, mutilMixins], //全局的mixin
  components: {
    detailsLargeimg,
    detailMutiAnalysis,
  },
  data() {
    return {
      cutList: [],
      activeIndex: 0,
      showList: {},
      homanBodyList,
      nonmotorList,
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("../../assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      collectionType: 0,
      detailType: 1, //1 人脸 2非机动车
      checkIndex: 0,
      linkFaces: [],
      linkHumans: [],
    };
  },
  watch: {},
  computed: {
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    // 开始数据
    init(row, list, index, type, page) {
      this.checkIndex = 0;
      this.detailType = type;
      this.cutList = [];
      this.datailsInfo = {};
      this.activeIndex = 0;
      if (type == 1) {
        this.showList = this.homanBodyList;
        this.collectionType = 16;
      } else {
        this.showList = this.nonmotorList;
        this.collectionType = 17;
      }
      this.cutList = list;
      this.activeIndex = index;
      this.datailsInfo = row;
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = 0;
      this.setPageInfo({
        startPage: page,
        endPage: page,
      });
      this.num = index;
      this.fetchNonmotorCaptureLink();
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(index);
      });
    },
    // 切换
    handleTab(item, index) {
      this.checkIndex = 0;
      this.resetData();
      this.datailsInfo = item;
      this.fetchNonmotorCaptureLink();
      this.activeIndex = index;
      this.play(index);
    },
    // 人车关联
    fetchNonmotorCaptureLink() {
      this.linkFaces = [];
      this.linkHumans = [];
      if (
        this.detailType != 1 &&
        (this.datailsInfo.linkFaceRid || this.datailsInfo.linkHumanRid)
      ) {
        getNonmotorCaptureLink({
          absTime: this.datailsInfo.absTime,
          recordId: this.datailsInfo.recordId,
        }).then((res) => {
          this.linkFaces = res.data.linkFaces ? res.data.linkFaces : [];
          this.linkHumans = res.data.linkHumans ? res.data.linkHumans : [];
        });
      }
    },
    scrollIntoViews() {
      this.$nextTick(() => {
        let ul = document.querySelector(".list-wrapper");
        let li = [...ul.childNodes][this.activeIndex];
        li.scrollIntoView({
          behavior: "smooth",
        });
      });
    },
    tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
    },
    collection(flag) {
      this.cutList[this.activeIndex].myFavorite = flag;
      this.datailsInfo = this.cutList[this.activeIndex];
      this.$emit("collection", flag);
      this.$forceUpdate();
    },
    toSearch(item, type) {
      const sectionName = type == "body" ? "humanBodyContent" : "faceContent";
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${sectionName}&noMenu=1`,
        query: {
          imgUrl: item.traitImg,
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    handlePlace(row) {
      if (row.taskType === "3") return;
      const sectionName =
        this.detailType === 1 ? "humanBodyContent" : "nonmotorVehicleContent";
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${sectionName}&noMenu=1`,
        query: {
          deviceInfo: JSON.stringify({ ...row, deviceGbId: row.deviceId }),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.device-click {
  cursor: pointer;
  text-decoration: underline;
}
.record-title {
  span {
    cursor: pointer;
  }
}
.rcgl {
  .title {
    color: #2c86f8;
  }
  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    img {
      width: 100%;
    }
  }
}
</style>
