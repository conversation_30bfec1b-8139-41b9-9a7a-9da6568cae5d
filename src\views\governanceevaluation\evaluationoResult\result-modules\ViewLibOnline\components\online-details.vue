<template>
  <ui-modal v-model="visible" :styles="styles" footer-hide title="查看详情">
    <div class="detail-title">
      <Button type="primary" :loading="exportLoading" @click="onExport">
        <i class="icon-font icon-daochu"></i>
        <span class="ml-sm">导出</span>
      </Button>
    </div>
    <icon-statics :icon-list="iconList"> </icon-statics>
    <nopage-calender
      v-ui-loading="{ loading: loading }"
      :start-date="detailData.startDate"
      :end-date="detailData.endDate"
      :hiatus="hiatus"
      :normal="normal"
      :legend-data="legendData"
    ></nopage-calender>
  </ui-modal>
</template>
<script>
import { iconStaticsList } from '../util/enum/ReviewParticular.js';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
export default {
  mixins: [particularMixin],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    code: {},
  },
  data() {
    return {
      styles: {
        width: '4.5rem',
      },
      echartStyle: {
        width: '1000px',
        height: '500px',
      },
      visible: false,
      loading: false,
      exportLoading: false,
      iconList: iconStaticsList,
      detailData: {},
      pageData: {
        pageNum: 1,
        pageSize: 100,
      },
      hiatus: [],
      normal: [],
      legendData: [
        { text: '在线', color: 'var(--color-success)', key: 'success' },
        { text: '离线', color: 'var(--color-failed)', key: 'hiatus' },
        { text: '报备', color: '#2B84E2', key: 'normal' },
      ],
    };
  },
  created() {},
  methods: {
    async getData() {
      try {
        // 获取详情
        this.hiatus = [];
        this.normal = [];
        this.loading = true;
        const data = await this.MixinGetImageList({ code: this.code });
        this.detailData = data;
        this.hiatus = this.detailData.calendarList.filter((row) => row.state === 0).map((row) => row.time);
        this.normal = this.detailData.calendarList.filter((row) => row.state === 2).map((row) => row.time);
        // 获取统计
        const statInfo = await this.MixinGetStatInfo({ code: this.code });
        // 设备模式统计
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, statInfo.qualified);
          }
          item.count = statInfo[item.fileName] || 0;
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    cancel() {
      this.visible = false;
    },
    onExport() {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = this.code;
      this.MixinGetExport(params);
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.getData();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    NopageCalender:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/components/nopage-calender.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 0;
  min-height: 400px;
  .detail-title {
    display: flex;
    justify-content: right;
    margin: 0 50px 10px 50px;
  }
  .icon-ul {
    background-color: var(--bg-card-1);
    height: 50px;
    padding: 0 50px;
  }
  .echarts {
    padding: 0 50px;
  }
  .nopage-calender {
    width: 760px;
    margin: 0 auto;
    margin-top: 10px;
  }
}
</style>
