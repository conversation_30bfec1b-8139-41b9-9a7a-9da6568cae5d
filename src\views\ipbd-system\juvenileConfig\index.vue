<template>
  <div class="layout">
    <Anchor class="anchor" show-ink container=".content" :scroll-offset="30">
      <AnchorLink href="#module1" title="未成年人保护专题" />
    </Anchor>
    <!-- <div class="button">
        <Button type="primary" @click="save()">保存</Button>
        </div> -->
    <div class="content">
      <div class="module" id="module1">
        <div class="title">
          <h2>未成年人保护专题</h2>
          <Button type="primary" @click="saveJuvenileConfig">保存</Button>
        </div>
        <div class="row">
          <div class="left-text">上学时间校外出现</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">上午</span>
                <TimePicker
                  type="timerange"
                  v-model="juvenileConfig.classTime.morningTimeRange"
                  placement="bottom-end"
                  placeholder="选择时间段"
                  class="w-220"
                ></TimePicker>
              </div>
              <div class="childRight">
                <span class="span">下午</span>
                <TimePicker
                  type="timerange"
                  v-model="juvenileConfig.classTime.afternoonTimeRange"
                  placement="bottom-end"
                  placeholder="选择时间段"
                  class="w-220"
                ></TimePicker>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft">
                <span class="span">暑假</span>
                <DatePicker
                  type="daterange"
                  :value="juvenileConfig.classTime.summerVacationRange"
                  placement="bottom-end"
                  placeholder="选择日期段"
                  class="w-220"
                  @on-change="
                    (value) =>
                      (juvenileConfig.classTime.summerVacationRange = value)
                  "
                ></DatePicker>
              </div>
              <div class="childRight">
                <span class="span">寒假</span>
                <DatePicker
                  type="daterange"
                  :value="juvenileConfig.classTime.winterVacationRange"
                  placement="bottom-end"
                  placeholder="选择日期段"
                  class="w-220"
                  @on-change="
                    (value) =>
                      (juvenileConfig.classTime.winterVacationRange = value)
                  "
                ></DatePicker>
              </div>
            </div>
            <div class="padding-10 right">
              <span class="span">其他</span>
              <div style="display: flex">
                <div class="more-datepicke">
                  <div
                    v-for="(item, index) in juvenileConfig.classTime
                      .otherExcludeRanges"
                    style="margin-bottom: 5px"
                  >
                    <DatePicker
                      type="daterange"
                      :value="item"
                      placement="bottom-end"
                      placeholder="选择日期段"
                      @on-change="
                        (value) =>
                          (juvenileConfig.classTime.otherExcludeRanges[index] =
                            value)
                      "
                      :key="index"
                      class="w-220"
                    ></DatePicker>
                    <i
                      class="iconfont icon-shanchu1"
                      v-if="
                        juvenileConfig.classTime.otherExcludeRanges.length > 1
                      "
                      style="margin-left: 5px; cursor: pointer"
                      @click="
                        juvenileConfig.classTime.otherExcludeRanges.splice(
                          index,
                          1
                        )
                      "
                    ></i>
                  </div>
                </div>
                <i
                  class="iconfont icon-jia"
                  style="margin-left: 5px; cursor: pointer"
                  @click="juvenileConfig.classTime.otherExcludeRanges.push([])"
                ></i>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">深夜时间出行</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">白天时间</span>
                <TimePicker
                  type="timerange"
                  v-model="juvenileConfig.appearInNight.dayTimeRange"
                  placement="bottom-end"
                  placeholder="选择时间段"
                  class="w-220"
                ></TimePicker>
              </div>
              <div class="childRight">
                <span class="span">关注区域</span>
                <div class="dashed_line w-220" @click="selectListDevice">
                  选择设备/已选({{ selectDeviceList.length }})
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">频繁出入娱乐场所</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">频次≥</span>
                <div class="right-input">
                  <InputNumber
                    placeholder="请输入"
                    v-model="juvenileConfig.minAppearInRecreation"
                    class="w-180"
                    :min="1"
                    :precision="0"
                  >
                  </InputNumber>
                  <div class="position">次</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">前科活跃人员分析</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">发现报警</span>
                <InputNumber
                  placeholder="请输入1-5之间的数"
                  v-model="juvenileConfig.activistsScoreConfig.alarmScore"
                  :min="1"
                  :max="5"
                  :precision="0"
                  class="w-180"
                >
                </InputNumber>
              </div>
              <div class="childRight">
                <span class="span">频繁出入娱乐场所</span>
                <InputNumber
                  placeholder="请输入1-5之间的数"
                  v-model="
                    juvenileConfig.activistsScoreConfig.appearInRecreationScore
                  "
                  :min="1"
                  :max="5"
                  :precision="0"
                  class="w-180"
                >
                </InputNumber>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft">
                <span class="span">深夜时间出现</span>
                <InputNumber
                  placeholder="请输入1-5之间的数"
                  v-model="
                    juvenileConfig.activistsScoreConfig.appearInNightScore
                  "
                  :min="1"
                  :max="5"
                  :precision="0"
                  class="w-180"
                >
                </InputNumber>
              </div>
              <div class="childRight">
                <span class="span">校外出现</span>
                <InputNumber
                  placeholder="请输入1-5之间的数"
                  v-model="
                    juvenileConfig.activistsScoreConfig.classTimeOutsideScore
                  "
                  :min="1"
                  :max="5"
                  :precision="0"
                  class="w-180"
                >
                </InputNumber>
              </div>
            </div>
            <div class="right2">
              <div class="childLeft">
                <span class="span">与违法前科人员同行</span>
                <InputNumber
                  placeholder="请输入1-5之间的数"
                  v-model="juvenileConfig.activistsScoreConfig.travelAlongScore"
                  :min="1"
                  :max="5"
                  :precision="0"
                  class="w-180"
                >
                </InputNumber>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">与违法前科人同行</div>
          <div class="right-more">
            <div class="right2">
              <div class="childLeft">
                <span class="span">同行次数≥</span>
                <div class="right-input">
                  <InputNumber
                    placeholder="请输入"
                    v-model="juvenileConfig.travelAlong.minCount"
                    class="w-180"
                    :min="1"
                    :precision="0"
                  >
                  </InputNumber>
                  <div class="position">次</div>
                </div>
              </div>
              <div class="childRight">
                <span class="span">时间间隔≤</span>
                <div class="right-input">
                  <InputNumber
                    placeholder="请输入"
                    v-model="juvenileConfig.travelAlong.maxIntervalSeconds"
                    class="w-180"
                    :min="1"
                    :precision="0"
                  >
                  </InputNumber>
                  <div class="position">秒</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">娱乐场所范围设置</div>
          <div class="right">
            <div class="dashed_line w-220" @click="selectPlaceHandler">
              选择场所/已选({{ selectPlaceList.length }})
            </div>
          </div>
        </div>
        <div class="row">
          <div class="left-text">前科人员发现报警</div>
          <div class="table">
            <alarmTable
              type="people"
              :tableData="juvenileConfig.alarmLevelConfig"
              @colorRefrsh="colorRefrsh"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 选择场所 -->
    <SelectPlace ref="selectPlace" @selectPlace="selectPlaceConfirm">
    </SelectPlace>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      @selectData="selectData"
    />
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import {
  getJuvenilesConfig,
  updateJuvenilesConfig,
} from "@/api/monographic/juvenile";
import alarmTable from "./components/alarm-table.vue";
import SelectPlace from "@/components/select-modal/select-place.vue";
import { queryDevicePageList } from "@/api/target-control.js";
import { getPlaceArchivesListAPI } from "@/api/placeArchive";
import { deepCopy } from "@/util/modules/common";
import { JuenileformData } from "@/views/ipbd-system/juvenileConfig/juenileForm.js";
export default {
  name: "ipbd-system",
  components: {
    SelectPlace,
    alarmTable,
  },
  props: {},
  data() {
    return {
      juvenileConfig: { ...JuenileformData }, // 未成年人
      selectPlaceList: [],
      selectDeviceList: [],
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  async created() {},
  mounted() {
    // 未成年人配置
    this.queryJuvenilesConfig();
  },
  methods: {
    async queryJuvenilesConfig() {
      const res = await getJuvenilesConfig();
      let juvenilesData = res?.data?.paramValue || "{}";
      let json = JSON.parse(juvenilesData);
      Object.keys(json).forEach((item) => {
        this.juvenileConfig[item] = json[item];
      });
      console.log(deepCopy(this.juvenileConfig), "this.juvenileConfig");
      // 前科人员发现
      if (this.juvenileConfig?.alarmLevelConfig?.length == 0) {
        const defaultValue = {
          alarmLevelName: "",
          isNotification: "1",
          alarmColour: "1",
        };
        // 给默认值
        this.$set(this.juvenileConfig, "alarmLevelConfig", [
          { alarmLevel: 1, ...defaultValue },
          { alarmLevel: 2, ...defaultValue },
          { alarmLevel: 3, ...defaultValue },
        ]);
      }
      this.$forceUpdate();
      // 选择的设备/场所回填
      let deviceIds = this.juvenileConfig.appearInNight.deviceIds;
      // 回填设备信息
      const { data } = await queryDevicePageList({
        deviceIds: deviceIds,
        pageNumber: 1,
        pageSize: 9999,
      });
      this.selectDeviceList =
        data?.entities?.map((item) => {
          return {
            ...item,
            deviceName: item.name,
            deviceId: item.id,
            deviceGbId: item.gbId,
            deviceType: item.type,
            select: true,
          };
        }) || [];
      let placeIds = this.juvenileConfig.recreationGrounds[0].placeIds;
      // 回填场所信息
      const { data: placeData } = await getPlaceArchivesListAPI({
        ids: placeIds,
      });
      this.selectPlaceList =
        placeData?.entities?.map((item) => {
          return {
            ...item,
          };
        }) || [];
    },
    saveJuvenileConfig() {
      updateJuvenilesConfig(this.juvenileConfig).then((res) => {
        this.$Message.success("修改" + res.msg);
      });
    },
    // 场所选择
    selectPlaceHandler() {
      this.$refs.selectPlace.show(this.selectPlaceList);
    },
    selectPlaceConfirm(value) {
      this.selectPlaceList = value;
      this.juvenileConfig.recreationGrounds[0].placeIds = value.map(
        (item) => item.id
      );
    },
    // 设备选择
    selectListDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectData(list) {
      this.selectDeviceList = list;
      this.juvenileConfig.appearInNight.deviceIds = list.map(
        (item) => item.deviceId
      );
    },
    colorRefrsh(index, name, type) {
      this.juvenileConfig.alarmLevelConfig[index].alarmColour = name;
      this.$forceUpdate();
    },
  },
};
</script>
<style lang="less" scoped>
.w-220 {
  width: 220px;
}
.w-180 {
  width: 180px;
}
.padding-10 {
  padding: 10px !important;
}

.layout {
  position: relative;
  width: 100%;
  background: #fff;
  display: flex;
  justify-content: center;
  // overflow-y: auto;
  padding-bottom: 20px;

  .button {
    position: fixed;
    right: 30px;
    top: 110px;
    cursor: pointer;
  }

  .content {
    width: calc(~"50% + 64px");
    overflow: auto;
    scrollbar-width: none;

    .module {
      padding: 0 32px 20px;
      margin-top: 35px;
      border-bottom: 1px dashed #d3d7de;

      .title {
        display: flex;
        justify-content: space-between;
      }

      .row {
        display: flex;
        justify-content: start;
        font-size: 14px;
        min-height: 50px;
        // line-height: 50px;
        margin-top: 20px;
        gap: 15px;
        .left {
          min-width: 80px;
        }

        .left-text {
          width: 70px;
          text-align: right;
        }

        .right-more {
          display: flex;
          flex-direction: column;
          gap: 10px;
          flex: 1;

          /deep/ .ivu-input {
            width: 100%;
          }
        }

        .right-input {
          display: flex;
          align-items: center;
          position: relative;

          .position {
            position: absolute;
            right: 30px;
          }
        }

        .right {
          flex: 1;
          background: #f9f9f9;
          padding: 0 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;

          .span {
            float: left;
            margin-right: 20px;
            display: inline-block;
          }

          .ivu-checkbox-group {
            float: left;
          }

          .ivu-select {
            flex: 1;
          }

          .btn {
            position: relative;
            display: flex;
            align-items: center;

            .position {
              position: absolute;
              right: 10px;
            }
          }

          .more-datepicke {
            display: flex;
            flex-direction: column;
          }
        }

        .justContent {
          justify-content: space-around;
        }

        .right2 {
          flex: 1;
          display: flex;
          min-height: 50px;
          line-height: 50px;
          justify-content: space-between;

          .childLeft {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;

            .position {
              position: absolute;
              right: 30px;
            }
          }

          .childRight {
            position: relative;
            width: 49%;
            background: #f9f9f9;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;

            .position {
              position: absolute;
              right: 30px;
            }
          }
        }

        .dashed_line {
          border: 1px dashed #3c8bef;
          height: 36px;
          line-height: 36px;
          padding: 0 10px;
          border-radius: 6px;
          color: #2c86f8;
          cursor: pointer;
          background: #ebf4ff;
          text-align: center;
        }
      }

      .aotoHeight {
        height: auto;

        .right {
          padding: 0;
        }
      }
    }

    .module:last-of-type {
      border: none;
      padding-bottom: 40px;
    }
  }
}

.content::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.preview {
  position: relative;
  width: 100%;

  .seleList {
    position: absolute;
    z-index: 9;
    display: inline-block;
    font-size: 12px;
    line-height: 22px;
    // pointer-events: none;
    margin-left: 5px;
    top: 50%;
    transform: translate(0, -50%);
    margin-right: 30px;

    .seletabs {
      border: 1px solid #e8eaec;
      background: #f7f7f7;
      padding: 0 6px;
      display: inline-block;
      font-size: 14px;
      border-radius: 5px;
      margin: 3px 4px 3px 0;
      position: relative;
      cursor: move;

      span {
        margin-right: 14px;
      }

      .icon-close {
        cursor: pointer;
        color: #66666675;
        font-size: 14px;
        position: absolute;
        right: 6px;
        top: 2px;
      }
    }
  }
}

/deep/ .ivu-input-group-append {
  width: 30px;
}

/deep/ .ivu-input {
  width: 150px;
}

.people-case {
  display: flex;
  margin-top: 10px;

  .left {
    align-self: center;
  }

  .right {
    margin-left: 20px;
    flex: 1;
  }

  .row {
    width: 100%;
    margin-top: 0px !important;
    justify-content: space-between !important;
    background: #f9f9f9;
    padding: 0 10px;
  }
}

.table {
  flex: 1;

  /deep/ .ivu-input {
    width: 120px;
  }
}

.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}

/deep/ .ivu-select-item-disabled {
  color: #c5c8ce;
}

/deep/ .icon-tishi {
  color: #f29f4c;
}

.anchor-point-infomation {
  width: 100px;
  position: fixed;
  top: 78px;
  right: 18px;
  z-index: 9;

  .export-btn {
    margin-bottom: 10px;
  }
}

.anchor {
  width: 120px;
  position: absolute;
  right: 17%;
  margin-top: 30px;

  /deep/ .ivu-anchor-wrapper {
    box-shadow: 0 0 !important;
  }
}

h2 {
  color: #000;
  font-size: 24px;
}
</style>
