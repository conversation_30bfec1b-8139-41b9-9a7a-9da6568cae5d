<template>
  <div class="container">
    <!-- 搜索 -->
    <searchForm
      :timeExist="false"
      @query="handleQuery"
      @reset="reset"
      type="task"
      :radioList="radioList"
      :timeList="timeList"
    >
      <query ref="slotQuery" />
    </searchForm>

    <!-- 列表 -->
    <div class="card-content">
      <div class="list">
        <ui-table :columns="columns" :data="tableList">
          <template #taskType="{ row }">
            <div>
              {{ row.taskType == 1 ? "单体布控" : "库布控（" + row.libs + ")" }}
            </div>
          </template>
          <template #taskTimeType="{ row }">
            <div v-if="row.taskTimeType == 1">
              {{ row.taskTimeB }}-{{ row.taskTimeE }}
            </div>
            <div v-else>永久</div>
          </template>
          <template #taskLevel="{ row }">
            <div>
              {{
                row.taskLevel == 1
                  ? "一级"
                  : row.taskLevel == 2
                  ? "二级"
                  : "三级"
              }}
            </div>
          </template>
          <template #taskStatus="{ row }">
            <div>
              {{
                row.taskStatus == 0
                  ? "未提交"
                  : row.taskStatus == 1
                  ? "待审核"
                  : row.taskStatus == 2
                  ? "执行中"
                  : row.taskStatus == 3
                  ? "驳回"
                  : row.taskStatus == 4
                  ? "已删除"
                  : "布控到期"
              }}
            </div>
          </template>
          <template #action="{ row }">
            <div class="btn-tips">
              <ui-btn-tip
                :content="row.taskStatus == 1 ? '审批' : '详情'"
                :icon="row.taskStatus == 1 ? 'icon-bianji' : 'icon-xiangqing'"
                class="mr-20 primary"
                @click.native="openDetail(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                v-if="row.taskStatus == 2"
                content="撤控待办"
                icon="icon-chexiao"
                class="mr-20 primary"
                @click.native="handleRemove(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                v-if="![1, 2, 4, 5].includes(row.taskStatus)"
                content="删除"
                icon="icon-shanchu"
                class="mr-20 primary"
                @click.native="handleDele(row)"
              ></ui-btn-tip>
            </div>
          </template>
        </ui-table>
      </div>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>

    <ui-modal
      v-model="detailVisible"
      title="布控详情"
      :r-width="1600"
      footer-hide
    >
      <div class="detail-box">
        <detail
          v-if="detailVisible"
          :taskId="taskId"
          :compareType="compareType"
          :alarmCount="alarmCount"
        ></detail>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import {
  taskPageList,
  taskCount,
  taskRemove,
  batchEdit,
  taskApply,
} from "@/api/target-control";
import searchForm from "../../components/search-form.vue";
import query from "../../control-task/components/query.vue";
import detail from "../../control-task/sensory/detail.vue";
import { mapGetters, mapActions } from "vuex";
export default {
  components: { searchForm, query, detail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      radioList: [
        { key: 2, value: "待审核", number: 0 },
        { key: 99, value: "全部", number: 0 },
        // { key: 0, value: '未执行', number: 0 },
        // { key: 1, value: '执行中', number: 0 },
      ],
      queryParam: {
        selectDeviceList: [],
      },
      timeList: [
        { name: "永久", value: "0" },
        // { name: '周期', value: '2' },
        { name: "自定义", value: "-1" },
      ],
      levelList: [
        { name: "一级", value: "1" },
        { name: "二级", value: "2" },
        { name: "三级", value: "3" },
      ],
      visible: false,
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableList: [],
      columns: [
        // { type: 'selection', width: 70, align: 'center' },
        { title: "布控名称", key: "taskName" },
        { title: "布控类型", slot: "taskType", width: 200 },
        { title: "布控时间", slot: "taskTimeType", width: 200 },
        { title: "布控级别", slot: "taskLevel", width: 100 },
        { title: "创建人", key: "creator", width: 100 },
        { title: "创建时间", key: "createTime", width: 200 },
        { title: "状态", slot: "taskStatus", width: 100 },
        { title: "申请理由", key: "requestReason", width: 200 },
        { title: "审核内容", key: "applyReason", width: 200 },
        { title: "操作", slot: "action", width: 120 },
      ],
      detailVisible: false,
      taskId: "",
      alarmCount: "",
    };
  },
  computed: {},
  watch: {
    "$route.query"(val) {
      if (val.refresh) {
        this.init();
        // this.count()
      }
    },
  },
  async activated() {
    await this.getDictData();
  },
  mounted() {
    if (this.radioList[0].key == 0) {
      this.queryParam.taskStatuss = [0, 1, 3, 4, 5];
    } else if (this.radioList[0].key == 1) {
      this.queryParam.taskStatus = 2;
    } else if (this.radioList[0].key == 2) {
      this.queryParam.taskStatus = 1;
    }
    // this.init()
    // this.count()
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    init() {
      var params = {
        params: this.params,
        ...this.queryParam,
        dataPermission: false,
        compareType: this.compareType,
      };
      if (params.operationType == 99) {
        params.operationType = null;
      }
      taskPageList(params).then((res) => {
        this.tableList = res.data.entities;
        this.total = res.data.total;
        this.tableList.forEach((item) => {
          if (item.taskType == 2) {
            var libs = "";
            item.libVoList.forEach((ite, index) => {
              if (index > 0) libs += ", ";
              libs += ite.libName;
            });
            item.libs = libs;
          }
        });
      });
    },
    reset(form) {
      this.$refs.slotQuery.reset();
      this.queryParam = {};
      if (form.operationType == 0) {
        this.queryParam.taskStatuss = [0, 1, 3, 4, 5];
      } else if (form.operationType == 1) {
        this.queryParam.taskStatus = 2;
      } else if (form.operationType == 2) {
        this.queryParam.taskStatus = 1;
      }
      this.init();
    },
    count() {
      taskCount({}).then((res) => {
        var row = res.data;
        this.radioList.forEach((item) => {
          item.number = row[item.key];
        });
      });
    },
    radioChange(row) {},
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.queryParam[key] = e;
      this.$forceUpdate();
    },
    taskChange() {},
    selectDevice() {},
    handleQuery(form) {
      if (form.timeSlot == "-1") this.queryParam.taskTimeType = 1;
      if (form.timeSlot == "0") this.queryParam.taskTimeType = 0;
      if (form.timeSlot == null) this.queryParam.taskTimeType = null;
      // if (form.timeSlot == '-1') {
      //   this.queryParam.taskTimeB = form.timeSlotArr[0]
      //   this.queryParam.taskTimeE = form.timeSlotArr[1]
      // }
      // if (form.timeSlot == '0') {
      //   this.queryParam.taskTimeType = 0
      // }
      // if (form.timeSlot == undefined) {
      //   this.queryParam.taskTimeB = null
      //   this.queryParam.taskTimeE = null
      // }

      // console.log('--- index page fromData', form, this.$refs.slotQuery.queryParam)
      this.queryParam = {
        ...this.queryParam,
        ...form,
        ...this.$refs.slotQuery.queryParam,
      };

      if (
        this.queryParam.createTimes &&
        this.queryParam.createTimes.length > 0
      ) {
        this.queryParam.createTimeB = this.queryParam.createTimes[0];
        this.queryParam.createTimeE = this.queryParam.createTimes[1];
      }

      if (this.queryParam.operationType == 0) {
        this.queryParam.taskStatuss = [0, 1, 3, 4, 5];
        delete this.queryParam.taskStatus;
        delete this.queryParam.operationType;
      } else if (this.queryParam.operationType == 1) {
        this.queryParam.taskStatus = 2;
        delete this.queryParam.taskStatuss;
      } else if (this.queryParam.operationType == 2) {
        this.queryParam.taskStatus = 1;
        delete this.queryParam.taskStatuss;
      } else {
        delete this.queryParam.taskStatus;
        delete this.queryParam.taskStatuss;
      }
      this.init();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.init();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.init();
    },
    handleRemove(row) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定撤控该布控信息？`,
        onOk: () => {
          let params = {
            taskId: row.taskId,
            checkUser: this.userInfo.username,
            taskStatus: 0,
          };
          taskApply(params).then((res) => {
            this.$Message.success(`撤控待办成功`);
            this.init();
          });
        },
      });
    },
    // 删除
    handleDele(item) {
      if (item.taskStatus == 1) {
        this.$Message.warning(`执行中任务不可删除,请停用后,再删除数据！`);
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该布控信息？`,
        onOk: () => {
          taskRemove(item.taskId).then((res) => {
            this.init();
          });
        },
      });
    },
    handleStatus(num, row) {
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定${num == 1 ? "启用" : "停用"}要吗？`,
        onOk: () => {
          var params = {
            taskStatus: num,
            taskIds: [row.taskId],
          };
          batchEdit(params).then((res) => {
            this.init();
          });
        },
      });
    },
    // 编辑
    openDetail(item) {
      this.taskId = item.taskId;
      this.alarmCount = item.alarmCount;
      this.detailVisible = true;
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
    .control-title {
      font-size: 18px;
      font-weight: 600;
      line-height: 34px;
    }
  }
  .card-content {
    width: 100%;
    display: flex;
    flex: 1;
    margin: 0 -0.02604rem;
    position: relative;
  }
  .list {
    flex: 1;
    display: flex;
  }
}
/deep/ .more-search {
  padding-left: 0 !important;
}

/deep/ .ivu-modal-body {
  padding: 0 !important;
  overflow-y: auto;
}
</style>