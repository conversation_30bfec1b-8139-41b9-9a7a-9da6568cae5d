<template>
  <div class="assetsmonitor-box auto-fill">
    <div class="search-module mb-sm">
      <div class="d_flex">
        <ui-label class="label-box inline mr-lg" label="监测时间：">
          <ui-radio-time
            :time-radio="timeRadio"
            :set-start-time-option="setStartTimeOption"
            :set-end-time-option="setEndTimeOption"
            @selectTime="selectTime"
          ></ui-radio-time>
        </ui-label>
        <div class="inline">
          <Button type="primary" @click="init">查询</Button>
          <Button class="ml-sm" @click="reset">重置</Button>
        </div>
      </div>
      <div>
        <Button type="primary" :loading="exportLoading" @click="onExport">
          <i class="icon-font icon-daochu font-white mr-xs"></i> 导出
        </Button>
      </div>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template v-for="item in slotList" #[item.slot]="{ row }">
        <span
          v-if="item.hasClick"
          :key="item.slot"
          :class="[row[item.slot] ? 'statistic-num' : '']"
          @click="openDetailDialog(row, item.slot)"
        >
          {{ getText(row[item.slot], item.isDel) }}
        </span>
        <template v-else>
          <div :key="item.slot" class="change-num-box">
            <div class="box-item">
              <span class="num-span num-l">{{ getFomatNum(row[item.startKey]) }}</span>
              <i class="icon-font icon-jiantou"></i>
              <span class="num-span num-r">{{ getFomatNum(row[item.endKey]) }}</span>
            </div>
            <div class="icon-box">
              <i
                v-if="getDiffNum(row[item.startKey], row[item.endKey]) > 0"
                class="icon-font icon-xinzeng1 icon-i mr-xs"
              ></i>
              <i
                v-if="getDiffNum(row[item.startKey], row[item.endKey]) < 0"
                class="icon-font icon-jianshao icon-i mr-xs"
              ></i>
              <span
                class="icon-num"
                :class="[
                  getDiffNum(row[item.startKey], row[item.endKey]) > 0 ? 'font-green' : 'font-red',
                  getDiffNum(row[item.startKey], row[item.endKey]) === 0 ? 't-a' : '',
                ]"
              >
                {{ getDiffNumText(row[item.startKey], row[item.endKey]) }}
              </span>
            </div>
          </div>
        </template>
      </template>
    </ui-table>
    <detail v-model="showDetail" :detail-data="detailData" :title="dialogTitle"></detail>
  </div>
</template>
<script>
import { toThousands } from '@/util/module/common';
import viewassets from '@/config/api/viewassets';
import { tableColumns } from './tableConfig';
export default {
  components: {
    UiRadioTime: require('@/components/ui-radio-time.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    Detail: require('./detail.vue').default,
  },
  props: {},
  data() {
    return {
      loading: false,
      timeRadio: {
        title: '',
        value: 'week',
        timeList: [
          { label: '近两日', value: 'twoDays' },
          { label: '近7天', value: 'week' },
          { label: '近30天', value: 'month' },
          { label: '自定义', value: 'defined' },
        ],
        startTime: '',
        endTime: '',
        timePickerShow: false,
        comType: 'date',
        format: 'yyyy-MM-dd',
      },
      setStartTimeOption: {
        disabledDate: (date) => {
          return date && date.valueOf() > Date.now() - 86400000;
        },
      },
      setEndTimeOption: {
        disabledDate: (date) => {
          return date && date.valueOf() > Date.now() - 86400000;
        },
      },
      searchData: {
        startTime: '',
        endTime: '',
      },
      tableColumns: tableColumns,
      tableData: [],
      defaultTableData: [],
      detailData: {},
      slotList: [],
      showDetail: false,
      dialogTitle: '',
      exportLoading: false,
    };
  },
  created() {
    this.slotList = this.tableColumns.filter((item) => item.slot);
    // 处理默认的检测时间
    this.$util.common.quickDate(this.timeRadio);
    this.selectTime(this.timeRadio);
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  watch: {
    'timeRadio.startTime'(val) {
      this.setEndTimeOption = {
        disabledDate: (date) => {
          if (val) {
            let ti = new Date(val).getTime();
            return date && (date.valueOf() > Date.now() - 86400000 || date.valueOf() < ti);
          } else {
            return date && date.valueOf() > Date.now() - 86400000;
          }
        },
      };
    },
    'timeRadio.endTime'(val) {
      this.setStartTimeOption = {
        disabledDate: (date) => {
          if (val) {
            let ti = new Date(val).getTime();
            return date && date.valueOf() > ti;
          } else {
            return date && date.valueOf() > Date.now() - 86400000;
          }
        },
      };
    },
    // 组件内 是包含今天的，因此特殊处理下 结束时间为前一天
    'timeRadio.timePickerShow'(val) {
      if (!val) return;
      this.timeRadio.endTime = this.getYesterday();
      this.selectTime(this.timeRadio);
    },
  },
  methods: {
    getDiffNum(startNum, endNum) {
      return endNum - startNum;
    },
    getDiffNumText(startNum, endNum) {
      let diff = endNum - startNum;
      return `${diff > 0 ? '+' : ''}${toThousands(diff)}`;
    },
    getFomatNum(val) {
      return toThousands(val);
    },
    getText(val, type = false) {
      return val > 0 ? `${type ? '-' : '+'}${val}` : val;
    },
    // 注意： 结束时间不能为 包括今天 + 未来时间  ---  由于组件 和 接口，但界面又不需要显示 时分秒，因此特殊处理下 时间格式为yyyy-MM-dd hh:mm:ss
    selectTime(timeRadio) {
      this.searchData.startTime = timeRadio.startTime.includes('00:00:00')
        ? timeRadio.startTime
        : `${timeRadio.startTime} 00:00:00`;
      if (!this.timeRadio.timePickerShow) {
        this.searchData.endTime = this.getYesterday();
      } else {
        this.searchData.endTime = timeRadio.endTime.includes('00:00:00')
          ? timeRadio.endTime
          : `${timeRadio.endTime} 00:00:00`;
      }
    },
    async init() {
      try {
        let { startTime, endTime } = this.searchData;
        if (!startTime || !endTime) {
          this.$Message.error(!startTime ? '请选择开始时间' : '请选择结束时间');
          return;
        }
        this.loading = true;
        const res = await this.$http.post(viewassets.getStatistics, this.searchData);
        this.tableData = res.data.data || [];
        this.defaultTableData = this.$util.common.deepCopy(this.tableData);
      } catch (err) {
        this.tableData = [];
        this.defaultTableData = [];
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.timeRadio.value = 'week';
      this.timeRadio.timePickerShow = false;
      this.$util.common.quickDate(this.timeRadio);
      this.selectTime(this.timeRadio);
      this.copySearch = null;
      this.copySearchDataMx(this.searchData);
      this.resetSearchDataMx(this.searchData, this.init);
    },
    openDetailDialog(row, filed) {
      if (row[filed] === 0) return;
      let isAdd = filed.includes('Add') ? true : false;
      let t1 = '';
      let t2 = isAdd ? '新增设备' : '撤销设备';
      let t3 = '';
      if (filed.includes('video')) {
        t1 = '视频监控';
        t3 = 'video';
      } else if (filed.includes('face')) {
        t1 = '人脸卡口';
        t3 = 'face';
      } else if (filed.includes('vehicle')) {
        t1 = '车辆卡口';
        t3 = 'vehicle';
      }
      this.detailData = {
        ...row,
        type: isAdd ? 'add' : 'cancel',
        ...this.searchData,
        entryType: t3,
      };
      this.dialogTitle = `${row.civilCodeName}-${t1}-${t2}`;
      this.showDetail = true;
    },
    // 排序
    onSortChange({ column, order }) {
      switch (order) {
        case 'asc':
        case 'desc':
          this.tableData.sort((a, b) => {
            return order === 'desc' ? b[column.slot] - a[column.slot] : a[column.slot] - b[column.slot];
          });
          break;
        default:
          this.tableData = this.$util.common.deepCopy(this.defaultTableData);
          break;
      }
    },
    // 当前时间的 前一天
    getYesterday() {
      let today = new Date();
      let yesterday = new Date(today);
      yesterday.setDate(today.getDate() - 1);
      return `${yesterday.getFullYear()}-${yesterday.getMonth() + 1}-${yesterday.getDate()} 00:00:00`;
    },
    // 导出
    async onExport() {
      try {
        this.exportLoading = true;
        let res = await this.$http.post(viewassets.statisticsExport, this.searchData, { responseType: 'blob' });
        this.$util.common.exportfile(res);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.assetsmonitor-box {
  background-color: var(--bg-content);
  padding: 20px;
  .search-module {
    display: flex;
    justify-content: space-between;
    .label-box {
      display: flex;
      align-items: center;
      @{_deep} .label {
        margin-right: 0 !important;
      }
    }
  }
  .statistic-num {
    cursor: pointer;
    text-decoration: underline;
  }
  .change-num-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
    .box-item {
      display: flex;
      align-items: center;
      width: 100%;
      .icon-jiantou {
        opacity: 0.3;
      }
      .num-span {
        display: inline-block;
        flex: 1;
      }
      .num-l {
        text-align: right;
      }
      .num-r {
        text-align: left;
      }
    }
    .icon-box {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: center;
      .icon-i {
        width: 48%;
        text-align: right;
      }
      .icon-xinzeng1 {
        color: var(--color-success);
      }
      .icon-jianshao {
        color: var(--color-failed);
      }
      .icon-num {
        flex: 1;
        text-align: left;
      }
      .t-a {
        text-align: center;
      }
    }
  }
}
</style>
