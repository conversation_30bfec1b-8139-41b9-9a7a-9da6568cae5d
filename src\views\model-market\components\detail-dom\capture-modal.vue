<template>
    <div class="dom-wrapper">
        <div class="dom-box">
            <header>
                <span>抓拍详情</span>
                <ui-icon type="close" :size="14" @click.native="() => $emit('close')"></ui-icon>
            </header>
            <section class="dom-content">
                <div class="dom-info">
                    <div class="dom-info-left" style="width: 200px">
                        <div class="smallImg" style="width: 200px; height: inherit;">
                            <img v-lazy="vehicleInfo.faceImg || vehicleInfo.traitImg" alt="" />
                        </div>
                        <div class="title">
                            <span class="active">抓拍记录</span>
                        </div>
                        <div class="traffic-record">
                            <div class="dom-content-p">
                                <span class="label">抓拍地点</span><span>：</span>
                                <ui-textOver-tips class="message" refName="captureAddress" :content="vehicleInfo.deviceName || '--'"></ui-textOver-tips>
                            </div>
                            <div class="dom-content-p">
                                <span class="label">通行时间</span><span>：</span>
                                <span class="message">{{ vehicleInfo.absTime || '--' }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="dom-info-right">
                        <ui-image :src="vehicleInfo.illegalImg || vehicleInfo.sceneImg" alt="静态库" viewer />
                    </div>
                </div>
            </section>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        positionPoints: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            vehicleInfo: {},
        }
    },
    methods: {
        /**
         * pointItem: 本条数据
         */
        init(pointItem) {
            return new Promise((resolve) => {
                this.vehicleInfo = {...pointItem};
                resolve({ data: true })
            })
        },
    }
}
</script>

<style lang='less' scoped>
@import './index';
.dom-wrapper {
    .dom-box {
        width: 840px;
        height: 480px;
        .dom-content {
            height: calc(~'100% - 40px');
            .dom-info-right {
                flex: 1;
            }
        }
    }
}
</style>
