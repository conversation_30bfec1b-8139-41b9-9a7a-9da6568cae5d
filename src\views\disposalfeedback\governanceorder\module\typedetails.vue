<template>
  <div class="work-order-statistics-container">
    <work-order-statistics
      ref="workOrderStatistics"
      v-bind="$attrs"
      v-on="$listeners"
      :table-columns="computedShowColumns"
      :backstage-data="backstageData"
      :getColumnLoading="getColumnLoading"
      :search-data="searchData"
      :commonSearchData="commonSearchData"
      @certainChangeColumn="getColumnsAfterSelectMx"
    >
      <template #search>
        <ui-label class="inline mr-lg" label="工单类型">
          <Select
            v-model="searchData.indexModule"
            placeholder="请选择工单类型"
            filterable
            clearable
            class="width-md"
            @on-change="onChangeIndexModule"
          >
            <Option v-for="(item, index) in indexModules" :key="index" :value="item.dataKey">{{
              item.dataValue
            }}</Option>
          </Select>
          <span class="ml-xs mr-xs font-blue">-</span>
          <Select
            v-model="searchData.indexId"
            placeholder="请选择指标"
            filterable
            clearable
            class="width-md"
            :disabled="!searchData.indexModule"
          >
            <Option v-for="(item, index) in filterIndexData" :key="index" :value="item.id">
              {{ item.indexName }}</Option
            >
          </Select>
        </ui-label>
        <div class="inline">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="resetSearchDataMx(searchData, search)">重置</Button>
        </div>
      </template>
    </work-order-statistics>
  </div>
</template>
<script>
import fillReportMixin from '@/views/disposalfeedback/governanceorder/util/fillReportMixin';
import statisticsColumnMixin from '@/views/disposalfeedback/governanceorder/util/statisticsColumnMixin';
export default {
  props: {
    commonSearchData: {},
  },
  mixins: [fillReportMixin, statisticsColumnMixin],
  data() {
    return {
      searchData: {
        indexModule: '',
        indexId: '',
        statisticsType: 2,
      },
    };
  },
  async created() {
    await this.getColumnsMx(); //获取展示的列
    this.getDictData(); //fillReportMixin.js
    this.getEvaluationIndex(); //fillReportMixin.js
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    search() {
      this.spliceColumnsMx();
      this.$refs.workOrderStatistics.initList();
    },
    onChangeIndexModule(val) {
      this.searchData.indexId = '';
      this.getEvaluationIndexByIndexModule(val); //fillReportMixin.js
    },
  },
  components: {
    WorkOrderStatistics: require('@/views/disposalfeedback/governanceorder/module/work-order-statistics.vue').default,
  },
};
</script>
<style lang="less" scoped>
.work-order-statistics-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
}
</style>
