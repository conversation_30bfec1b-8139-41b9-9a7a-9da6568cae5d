<template>
  <ui-modal v-model="visible" :styles="styles" :title="modalAction.title">
    <div class="addor-modal" v-ui-loading="{ loading: formLoading }">
      <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="110">
        <FormItem label="任务名称" class="left-item" prop="taskName">
          <Input
            :disabled="isEdit && this.formData.schemeType != '3'"
            type="text"
            v-model="formData.taskName"
            placeholder="请输入任务名称"
            class="width-input"
            :maxlength="50"
          ></Input>
        </FormItem>
        <FormItem label="评测对象" class="left-item" prop="regionCode">
          <ApiAreaTree
            :disabled="isEdit && this.modalData.regionCode != ''"
            class="width-input area-tree"
            :select-tree="selectTree"
            @selectedTree="selectedArea"
            placeholder="请选择评测对象"
          ></ApiAreaTree>
        </FormItem>
        <FormItem label="评测方案" class="left-item" prop="schemeId">
          <Select
            v-if="isAdd"
            class="width-input"
            placeholder="请选择评测方案"
            clearable
            transfer
            filterable
            v-model="formData.schemeId"
            @on-change="setSchemeId"
          >
            <Option v-for="(item, index) in moduleList" :key="index" :value="item.id">{{ item.name }}</Option>
          </Select>
          <Select
            v-if="isEdit && this.formData.schemeType == '3'"
            class="width-input"
            placeholder="请选择考核方案"
            clearable
            transfer
            filterable
            v-model="formData.schemeId"
            @on-change="setSchemeId"
            :disabled="isEdit && this.formData.schemeType == '3'"
          >
            <Option v-for="(item, index) in moduleList" :key="index" :value="item.id">{{ item.name }}</Option>
          </Select>
          <Select
            class="width-input"
            v-if="isEdit && this.formData.schemeType != '3'"
            v-model="formData.schemeId"
            placeholder="请选择考核方案"
            :disabled="isEdit && this.formData.schemeType != '3'"
          >
            <Option v-if="formData.schemeId" :value="formData.schemeId">{{ formData.schemeName }}</Option>
          </Select>
        </FormItem>
        <FormItem label="任务权限" class="left-item" prop="taskPermissionList">
          <select-organization-tree
            class="width-input"
            ref="tree"
            v-if="visible"
            check-strictly
            :tree-list="noLimitTreeList"
            :flat-tree-list="noLimitFlatTreeList"
            :default-checked-keys="formData.taskPermissionList"
            @check="checkTree"
          >
          </select-organization-tree>
        </FormItem>
        <FormItem label="统计模式" prop="statisticalModelBo.statisticalModel">
          <CheckboxGroup
            :value="formData.statisticalModelBo.statisticalModel"
            class="flex-aic"
            @on-change="changeStatisticalModalGroup"
          >
            <Checkbox :label="SMODE_REGION" class="mr-md">行政区划</Checkbox>
            <Checkbox :label="SMODE_ORG" class="mr-md">组织机构</Checkbox>
            <Checkbox :label="SMODE_DEVICETAG" class="flex-aic">
              <span class="mr-sm">设备标签</span>
              <span class="font-gray" @click.prevent="addStatisticTag" v-if="formData.statisticalModelBo.tagIds?.length"
                >（已选择{{ formData.statisticalModelBo.tagIds.length }}标签）</span
              >
              <i class="icon-font icon-peizhibiduiziduan f-14 font-gray" @click.prevent="addStatisticTag"></i>
            </Checkbox>
          </CheckboxGroup>
        </FormItem>
      </Form>
    </div>
    <template slot="footer">
      <Button @click="visible = false" class="plr-30"> 取 消 </Button>
      <Button type="primary" class="plr-30" :loading="loading" @click="handleSubmit('modalData', formData)">
        确 定
      </Button>
    </template>
    <!--    关联标签弹窗-->
    <customize-filter
      v-model="addTagVisible"
      :customize-action="tagFilterAttrs.customizeAction"
      :content-style="tagFilterAttrs.contentStyle"
      :field-name="tagFilterAttrs.fieldName"
      :checkbox-list="tagFilterAttrs.allTagList"
      :default-checked-list="formData.statisticalModelBo.tagIds"
      :show-clear-all="true"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <!-- 统计模式有变更确认弹窗 -->
    <statisticmode-save-modal
      v-model="statisticeModeSaveVisible"
      :index-config-data="indexConfigData"
      @saveStatisticIndex="saveStatisticIndex"
    >
    </statisticmode-save-modal>
  </ui-modal>
</template>
<style lang="less" scoped>
.addor-modal {
  // padding-left: 70px;
  .width-input {
    width: 400px;
  }
  .width-addition {
    width: 60px;
  }
  .width-time {
    flex: 1;
  }
  .width-picker {
    width: 120px;
  }
  .form-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  .assessTime {
    @{_deep}.ivu-form-item-label {
      &::before {
        content: '*';
        display: inline-block;
        margin-right: 0.020833rem;
        line-height: 1;
        font-family: SimSun;
        font-size: 0.072917rem;
        color: #ed4014;
      }
    }
  }
  .inspection {
    max-height: 200px;
    overflow-y: auto;
  }
}
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
}
@{_deep}.ivu-modal-body {
  padding: 10px 50px !important;
}
.area-tree {
  @{_deep}.ivu-dropdown {
    width: 400px;
    .ivu-dropdown-rel {
      .select-width {
        width: 400px;
      }
    }
    .ivu-select-dropdown {
      height: 240px !important;
      padding: 10px;
      .ivu-dropdown-menu {
        height: 220px;
        .el-tree {
          height: 100% !important;
        }
      }
    }
  }
}

@{_deep}.ivu-date-picker {
  width: 100%;
}
</style>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';
import common from '@/config/api/common';
export default {
  props: ['org', 'value', 'modalAction', 'modalData'],

  data() {
    return {
      SMODE_DEVICETAG: this.global.STATISTICAL_MODAL.SMODE_DEVICETAG, //统计模式：设备标签
      SMODE_REGION: this.global.STATISTICAL_MODAL.SMODE_REGION, //统计模式：行政区划
      SMODE_ORG: this.global.STATISTICAL_MODAL.SMODE_ORG, //统计模式：组织机构
      loading: false,
      test: '1',
      visible: false,
      moduleList: [],
      formData: {
        taskName: '',
        regionCode: '',
        schemeId: null,
        taskSchemeId: '',
        taskPermissionList: [],
        statisticalModelBo: {
          statisticalModel: [this.global.STATISTICAL_MODAL.SMODE_ORG, this.global.STATISTICAL_MODAL.SMODE_REGION], //统计模式 2.行政区划 1.组织机构 3.设备标签
          tagIds: [],
          isChanged: 0,
        },
      },
      schemeList: [],
      ruleCustom: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
          },
        ],
        // regionCode: [{ validator: validatePass, trigger: 'change' }],
        regionCode: [
          {
            required: true,
            message: '请选择治理内容',
            trigger: 'change',
          },
        ],
        schemeId: [
          {
            required: true,
            message: '请选择方案',
            type: 'number',
            trigger: 'change',
          },
        ],
        taskPermissionList: [
          {
            required: true,
            message: '请选择任务权限',
            type: 'array',
            trigger: 'change',
          },
        ],
        'statisticalModelBo.statisticalModel': [
          {
            required: true,
            type: 'array',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择至少一个统计模式'));
              } else if (value.includes(this.SMODE_DEVICETAG) && this.formData.statisticalModelBo.tagIds.length === 0) {
                callback(new Error('请选择待统计的设备标签'));
              } else {
                callback();
              }
            },
          },
        ],
      },
      selectTree: {
        regionCode: '',
      },

      styles: {
        width: '3.5rem',
      },
      //权限
      defaultCheckedKeys: [],
      treeData: [],
      nodeKey: 'orgCode',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      treePlaceholder: '请选择组织机构',
      //选择标签
      addTagVisible: false,
      tagFilterAttrs: {
        allTagList: [],
        customizeAction: {
          title: '添加设备标签',
          leftContent: '选择设备标签及排序',
          rightContent: '设备标签显示',
          moduleStyle: {
            width: '70%',
          },
        },
        contentStyle: {
          height: `${500 / 192}rem`,
        },
        fieldName: {
          id: 'tagId',
          value: 'tagName',
        },
      },
      // 统计模式确认弹窗
      statisticeModeSaveVisible: false,
      indexConfigData: [], //此任务下所有指标
      updateTagIdsByIndex: [], //需要更新标签配置的指标集合
      // 保存原始任务对象
      formLoading: false,
      comparedStatisticalModelBo: {
        //用于判断统计模式是否第一次变动，修改isChange字段
        statisticalModel: [this.global.STATISTICAL_MODAL.SMODE_ORG, this.global.STATISTICAL_MODAL.SMODE_REGION],
        tagIds: [],
        isChanged: 0,
      },
    };
  },
  created() {},

  methods: {
    checkTree(orgList) {
      this.formData.taskPermissionList = orgList.map((org) => org.orgCode);
    },
    async initModuleList() {
      try {
        let res = await this.$http.post(governanceevaluation.schemeList, {});
        this.moduleList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async getOrg() {
      try {
        let res = await this.$http.get(common.getOrganizationListByRegionCode, {
          params: {
            recursionChild: true,
            regionCode: this.formData.regionCode,
          },
        });
        this.treeData = res.data.data || [];
      } catch (error) {
        console.log(error);
      }
    },
    /**
     * 设置默认值
     * 默认选中全部
     * @param data 组织机构
     */
    setDefaultOrgRegionCode(data) {
      let codes = data.map((item) => item.orgCode);
      this.formData.taskPermissionList = codes;
      this.defaultCheckedKeys = codes;
    },
    setSchemeId(e) {
      this.formData.schemeId = e;
    },
    async handleSubmit(name) {
      try {
        let validate = await this.$refs[name].validate();
        if (!validate) return;
        if (this.judgeSelectTagLength()) {
          this.$Message.error('请选择待统计的设备标签');
          return;
        }
        //判断统计模式以及标签是否发生变化
        const statisticalChanged = this.judgeStatisticModeChange();
        if (statisticalChanged && this.isEdit) {
          // isChanged是否改变过 0没有 1有, 没有不弹出保存统计模式的确认弹窗
          this.formData.statisticalModelBo.isChanged = 1;
          await this.getindexShow();
          //该任务未关联指标不显示弹窗
          if (this.indexConfigData.length !== 0) {
            this.statisticeModeSaveVisible = true;
          }
          return;
        }
        await this.saveForm(name);
      } catch (err) {
        console.log(err);
      }
    },
    //保存任务配置
    async saveForm(name) {
      const { statisticalModel, tagIds } = this.formData.statisticalModelBo;
      try {
        const addParams = {
          regionCode: this.formData.regionCode,
          taskName: this.formData.taskName,
          schemeId: this.formData.schemeId,
          taskSchemeId: this.formData.taskSchemeId,
          taskPermissionList: this.formData.taskPermissionList,
          statisticalModelBo: {
            ...this.formData.statisticalModelBo,
            tagIds: !statisticalModel.includes(this.SMODE_DEVICETAG) ? [] : tagIds, //未选择设备标签传参要清空tagIds
          },
          updateTagIdsByIndex: this.updateTagIdsByIndex,
        };
        this.loading = true;
        let res = await this.$http.post(governanceevaluation.addTaskScheme, addParams);
        this.$Message.success(res.data.msg);
        this.$refs[name].resetFields();
        this.visible = false;
        this.$emit('update');
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    async selectedArea(area) {
      this.formData.regionCode = area.regionCode;
      await this.getOrg();
      this.setDefaultOrgRegionCode(this.treeData);
    },
    resetFormData() {
      this.formData = {
        taskName: '',
        regionCode: '',
        schemeId: null,
        taskSchemeId: '',
        taskPermissionList: [],
        codes: [],
        statisticalModelBo: {
          statisticalModel: [this.SMODE_ORG, this.SMODE_REGION],
          tagIds: [],
          isChanged: 0,
        },
      };
      this.selectTree.regionCode = '';
      this.updateTagIdsByIndex = [];
    },
    async getTaskById() {
      try {
        let params = {
          id: this.modalData.taskSchemeId,
        };
        let res = await this.$http.get(governanceevaluation.getTaskPermissionList, { params });
        this.comparedStatisticalModelBo =
          res.data.data.statisticalModelBo || this.$util.common.deepCopy(this.formData.statisticalModelBo);
        this.formData = {
          ...this.formData,
          ...res.data.data,
          statisticalModelBo: this.$util.common.deepCopy(this.comparedStatisticalModelBo),
        };
        this.selectTree.regionCode = this.formData.regionCode;
        this.defaultCheckedKeys = this.formData.taskPermissionList;
      } catch (e) {
        console.log(e);
      }
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagFilterAttrs.allTagList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    // 添加统计标签
    addStatisticTag() {
      this.addTagVisible = true;
    },
    async confirmFilter(val) {
      try {
        const tagIdList = val.map((item) => item.tagId);
        this.formData.statisticalModelBo.tagIds = this.$util.common.deepCopy(tagIdList);
        this.addTagVisible = false;
      } catch (err) {
        console.log(err);
      }
    },
    //判定：统计模式包含设备标签&&标签选择数量不为0，返回fasle
    judgeSelectTagLength() {
      let flag = false;
      // statisticalModel:[3]设备标签
      if (this.formData.statisticalModelBo.statisticalModel.includes(this.SMODE_DEVICETAG)) {
        flag = this.formData.statisticalModelBo.tagIds.length === 0;
      }
      return flag;
    },
    //若变更了统计模式则弹窗让用户确认，以同步到指标
    async getindexShow() {
      try {
        this.loading = true;
        let params = {
          taskSchemeId: this.modalData.taskSchemeId,
        };
        let res = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        this.indexConfigData = res.data.data.indexConfigData || [];
      } catch (err) {
        this.indexConfigData = [];
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    //保存需要同步统计模式的指标
    saveStatisticIndex(list) {
      this.updateTagIdsByIndex = list;
      this.saveForm('modalData');
    },
    //判定统计模式是否有变化
    judgeStatisticModeChange() {
      let tagChangedArr = this.$util.common.isObjectChanged(
        this.formData.statisticalModelBo.tagIds,
        this.comparedStatisticalModelBo.tagIds,
      );
      let sModelChangedArr = this.$util.common.isObjectChanged(
        this.formData.statisticalModelBo.statisticalModel,
        this.comparedStatisticalModelBo.statisticalModel,
      );
      if (tagChangedArr.length === 0 && sModelChangedArr.length === 0) {
        return false;
      } else {
        return true;
      }
    },
    //第一次勾选设备标签默认打开选择标签弹窗
    changeStatisticalModalGroup(group) {
      const oldStatisticalModel = [...this.formData.statisticalModelBo.statisticalModel];
      this.formData.statisticalModelBo.statisticalModel = group;
      let isChangeDeviceTag =
        group.includes(this.SMODE_DEVICETAG) && !oldStatisticalModel.includes(this.SMODE_DEVICETAG);
      if (isChangeDeviceTag && !this.formData.statisticalModelBo.tagIds?.length) {
        this.addStatisticTag();
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    async value(val) {
      this.visible = val;
      this.resetFormData();
      this.$refs.modalData.resetFields();
      if (val) {
        try {
          this.formLoading = true;
          await this.getTagList();
          this.initModuleList();
          if (this.isEdit) {
            await this.getTaskById();
            await this.getOrg();
          }
        } catch (err) {
          console.log(err);
        } finally {
          this.formLoading = false;
        }
      }
    },
    'formData.statisticalModelBo.tagIds': {
      handler(val) {
        if (val.length > 0) {
          this.$nextTick(() => {
            this.$refs['modalData'].validateField('statisticalModelBo.statisticalModel');
          });
        }
      },
    },
  },
  computed: {
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isEdit() {
      return this.modalAction.action === 'edit';
    },
    noLimitFlatTreeList() {
      return this.treeData.map((item) => {
        return {
          ...item,
          disabled: false,
        };
      });
    },
    noLimitTreeList() {
      return this.$util.common.arrayToJson(this.noLimitFlatTreeList, 'id', 'parentId');
    },
  },
  components: {
    SelectOrganizationTree: require('@/api-components/select-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    StatisticmodeSaveModal: require('./components/statisticmode-save-modal.vue').default,
  },
};
</script>
