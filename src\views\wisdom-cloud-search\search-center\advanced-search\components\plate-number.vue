<template>
    <div class="plate-number-content">
      <span>{{plateNo}}</span>
    </div>
</template>
<script>
import plateNumber from './plate-number'
export default {
  props: {
    plateNo: {
      type: String,
      default: ''
    },
    plateType: {
      type: String,
      default: 'car'
    }
  },
  components:{plateNumber},
  data () {
    return {
      options: {

      }
    }
  },
  methods: {
    changeHandle () {
      this.$emit('on-change', '')
    }
  }
}
</script>
<style lang="less" scoped>
  .plate-number-content{
    width: 88px;
    margin: auto;
    height: 22px;
    background: #2379F9;
    border-radius: 2px;
    color: #fff;
    padding:2px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    span{
      height: 18px;
      width: 100%;
      border-radius: 2px;
      border: 1px solid #FFFFFF;
      white-space: nowrap;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
    }
  }
</style>
