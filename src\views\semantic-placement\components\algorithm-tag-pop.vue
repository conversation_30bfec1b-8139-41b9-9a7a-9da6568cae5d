<template>
  <div ref="tagWrapRef" class="tag-wrap">
    <Poptip
      trigger="hover"
      placement="top"
      transfer
      transfer-class-name="algorithm-tag-poptip"
      :disabled="data.length <= 1"
    >
      <div class="view-tags">
        <div class="text-overflow label-item">
          {{ data[0] }}
        </div>
      </div>
      <div v-show="data.length > 1" ref="tagEllipsis" class="tag-ellipsis">
        <span
          v-for="(item, $index) in 3"
          :key="$index"
          class="tag-ellipsis-item"
        ></span>
      </div>
      <div slot="content">
        <div class="pop-box">
          <div class="label-item" v-for="(item, $index) in data">
            {{ item }}
          </div>
        </div>
      </div>
    </Poptip>
  </div>
</template>
<script>
import UiTag from "@/components/ui-tag.vue";
export default {
  name: "AlgorithmTagPop",
  components: {
    UiTag,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    // 显示几行，默认一行
    row: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      showEllipsis: false,
    };
  },
  watch: {},
  mounted() {},
  beforeDestroy() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.tag-wrap {
  width: 100%;
  // overflow: hidden;
  display: flex;
  align-items: center;
  overflow: hidden;
  /deep/ .ivu-poptip {
    width: 100%;
  }
  .view-tags {
    // flex: 1;
    display: flex;
    flex-wrap: nowrap;
    margin-right: 10px;
    // width: 100%;
    overflow: hidden;
    // gap: 5px;
  }
  .ui-tag {
    margin-right: 5px;
  }
  /deep/ .ivu-poptip-rel {
    display: flex;
    align-items: center;
    height: 26px;
  }
  /deep/ .ui-tag-item {
    padding: 2px 6px;
    .ui-tag-text {
      font-size: 12px;
    }
  }
  .tag-ellipsis {
    display: inline-flex;
    flex-direction: row;
    cursor: pointer;
    .tag-ellipsis-item {
      width: 4px;
      height: 4px;
      background: #888;
      display: inline-block;
      border-radius: 50%;
    }
    & > span:nth-child(2) {
      margin: 0 4px;
    }
  }
}
.pop-box {
  width: 100%;
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}
.label-item {
  max-width: 150px;
  display: inline-block;
  border-radius: 2px;
  font-size: 12px;
  color: rgb(237, 64, 20);
  padding: 0 6px;
  height: 20px;
  line-height: 20px;
  border: 1px solid rgb(237, 64, 20);
  overflow: unset;
}
</style>

<style lang="less">
.algorithm-tag-poptip.ivu-poptip-popper {
  width: auto !important;
  max-width: 500px;
  min-width: unset;
  .ivu-poptip-body {
    height: auto !important;
    .ivu-poptip-body-content {
      overflow: unset;
    }
  }
}
</style>
