<template>
  <div class="external-docking auto-fill">
    <div class="header">
      <span>外部对接异常</span>
      <i-switch v-model="formData['0101'].markEnable" :disabled="!isEdit" :true-value="1" :false-value="0" />
    </div>
    <div class="content auto-fill">
      <ui-label label="对接类型：">
        <CheckboxGroup v-model="formData['0101'].source">
          <Checkbox label="010101" :disabled="!isOpen">
            <span>GB/T 28181_联网平台对接</span>
          </Checkbox>
          <Checkbox class="ml-lg" label="010102" :disabled="!isOpen">
            <span>GB/T 1400_视图库对接</span>
          </Checkbox>
        </CheckboxGroup>
      </ui-label>
      <ui-label label="通知条件：">
        <CheckboxGroup v-model="formData['0101'].trigger">
          <Checkbox label="010101" :disabled="!isOpen">
            <span>注册失败</span>
          </Checkbox>
          <Checkbox class="ml-lg" label="010102" :disabled="!isOpen">
            <span>保活异常</span>
          </Checkbox>
          <Checkbox class="ml-lg" label="010103" :disabled="!isOpen">
            <span>设备同步异常</span>
          </Checkbox>
        </CheckboxGroup>
      </ui-label>
      <ui-label label="通知接收人：">
        <span :class="['font-active-color', isOpen ? 'pointer' : 'not-allowed', 'f-14']" @click="selectNotification">
          <i class="icon-font icon-xuanzetongzhiduixiang"></i>
          <span class="ml-xs notification">请选择通知对象</span>
        </span>
      </ui-label>
      <div class="table-module auto-fill mt-sm">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="formData['0101'].receiveConfig">
          <template #people="{ row }">
            <div class="base-text-color">{{ row.name }}（{{ row.username }}）</div>
          </template>
          <template #phone="{ row, index }">
            <Input
              v-if="isOpen"
              class="width-md"
              placeholder="请输入联系电话"
              v-model="formData['0101'].receiveConfig[index].phone"
            ></Input>
            <span v-else>{{ row.phone }}</span>
          </template>
        </ui-table>
      </div>
      <div class="mt-sm">
        <ui-label label="通知方式：">
          <CheckboxGroup class="notification-method" v-model="formData['0101'].template">
            <Checkbox label="system" :disabled="true">
              <span>系统消息</span>
              <span class="message-content" ref="systemTemplateRef">
                【<span>#对接类型#_#平台类型#</span>】 【<span>#平台编码#（#平台名称#）</span>】
                <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
              </span>
            </Checkbox>
            <Checkbox label="dialog" :disabled="!isOpen">
              <span>系统弹框</span>
              <span class="message-content" ref="dialogTemplateRef">
                【<span>#对接类型#_#平台类型#</span>】 【<span>#平台编码#（#平台名称#）</span>】
                <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
              </span>
            </Checkbox>
            <Checkbox label="home" :disabled="!isOpen">
              <span>首页推送</span>
              <span class="message-content" ref="homeTemplateRef">
                【<span>#对接类型#_#平台类型#</span>】 【<span>#平台编码#（#平台名称#）</span>】
                <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
              </span>
            </Checkbox>
            <Checkbox label="sms" :disabled="!isOpen">
              <span>短信通知</span>
              <span class="message-content" ref="smsTemplateRef">
                【<span>#对接类型#_#平台类型#</span>】 【<span>#平台编码#（#平台名称#）</span>】
                <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
              </span>
            </Checkbox>
          </CheckboxGroup>
        </ui-label>
      </div>
    </div>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
  </div>
</template>
<script>
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          title: '通知对象',
          minWidth: 200,
          slot: 'people',
        },
        {
          title: '所属单位',
          key: 'orgName',
        },
        {
          width: 400,
          title: '联系电话',
          slot: 'phone',
        },
      ],
      formData: {
        '0101': {
          markEnable: 0,
          source: [], //对接类型
          trigger: [], //通知条件
          template: [], //通知方式
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
      },
      defaultPeopleList: [],
    };
  },
  created() {
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     */
    this.initMx(['0101']);
  },
  methods: {
    selectNotification() {
      if (this.isEdit) {
        this.defaultPeopleList = this.getDefaultPeopleMx(this.formData['0101'].receiveConfig);
        this.peopleSelectShow = true;
      }
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      this.formData['0101'].receiveConfig = peopleList;
    },
    reset() {
      this.resetMx();
    },
    async save() {
      try {
        this.formData['0101'].systemTemplate = this.$refs.systemTemplateRef.innerText;
        this.formData['0101'].dialogTemplate = this.$refs.dialogTemplateRef.innerText;
        this.formData['0101'].homeTemplate = this.$refs.homeTemplateRef.innerText;
        this.formData['0101'].smsTemplate = this.$refs.smsTemplateRef.innerText;
        await this.saveMx(['0101']);
        // this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    isEdit() {
      return this.action === 'edit';
    },
    isOpen() {
      return this.formData['0101'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
.external-docking {
  padding: 0 20px 20px 20px;
  .header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--bg-collapse-item);
    color: var(--color-navigation-title);
    font-size: 16px;
    padding: 0 20px;
    font-weight: 900;
  }
  .content {
    padding: 15px 20px;
    .notification {
      text-decoration: underline;
    }
    .not-allowed {
      cursor: not-allowed;
    }
    .notification-method {
      label {
        display: block;
        margin-bottom: 5px;
      }
      &:nth-child(n + 2) {
        margin-left: 80px;
      }
    }
    .message-content {
      margin-left: 10px;
      display: inline-block;
      border: 1px solid var(--border-input);
      padding: 0 10px;
      background-color: var(--bg-input);
      width: 760px;
      vertical-align: text-top;
      border-radius: 4px;
      span {
        color: var(--color-primary);
      }
    }
  }
}
</style>
