<template>
  <div>
    <Table :columns="tableColumns" :data="tableData">
      <template #errorMessage="{ row }">
        <ui-btn-tip
          v-if="row.apiStatus == 200"
          icon="icon-chakanxiangqing"
          content="查看"
          @handleClick="show(row.id, row.apiIndex)"
        ></ui-btn-tip>
        <Tooltip class="errorInfo" v-else :content="row.errorMessage" :transfer="true" max-width="300">
          <div class="error">{{ row.errorMessage }}</div>
        </Tooltip>
      </template>
      <template #apiStatus="{ row }">
        <span v-if="row.apiStatus == 200" class="success">成功</span>
        <span v-else class="error">失败</span>
      </template>
    </Table>
    <detailData ref="detailDatas"></detailData>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  components: {
    detailData: require('./detailData.vue').default,
  },
  props: {
    row: Object,
  },
  data() {
    return {
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '调用时间', key: 'apiCheckTime', tooltip: true },
        { title: '是否成功', slot: 'apiStatus', tooltip: true },
        { title: '耗时(ms)', key: 'apiDuration', tooltip: true },
        { title: '详情', width: 170, slot: 'errorMessage' },
      ],
      tableData: [],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    show(id, apiIndex) {
      this.$refs.detailDatas.init(id, apiIndex);
    },
    async init() {
      this.visible = true;
      let data = {
        batchId: this.row.batchId,
        indexId: this.row.indexId,
        orgCode: this.row.orgCode,
        apiIndex: this.row.apiIndex,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.detailPageList, data);
        this.tableData = res.data;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.expand-row {
  margin-bottom: 16px;
}

.success {
  color: #0e8f0e;
}

.error {
  color: #bc3c19;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/deep/ .errorInfo.ivu-tooltip {
  width: 100%;
}
</style>
