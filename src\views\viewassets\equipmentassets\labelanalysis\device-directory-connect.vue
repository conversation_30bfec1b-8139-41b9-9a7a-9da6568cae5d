<template>
  <ui-modal v-model="visible" title="导入设备关联标签" :styles="styles">
    <div class="content-wrap" v-if="visible">
      <div class="right-module">
        <div class="select-directory-content">
          <span>选择待关联的设备目录</span>
          <choose-catalogue class="catalogue" :category-obj="categoryObj" @getDevCategoryData="getDevCategoryData">
          </choose-catalogue>
        </div>
        <div class="search-module betw-flex">
          <span class="base-text-color">
            标签列表<span class="font-red">{{ tagData.length }}</span
            >条
          </span>
          <Input v-model="tagName" placeholder="请输入标签名称" search class="width-md ml-lg" @on-search="searchTag" />
        </div>
        <div class="tag-list" v-ui-loading="{ loading: tagLoading, tableData: tagData }">
          <div>
            <Checkbox
              class="ml-sm mb-sm"
              v-for="(item, index) in tagData"
              :key="index"
              :label="item.tagId"
              v-model="item.checked"
              @on-change="checkTag($event, item)"
            >
              <span class="tag-item ellipsis" :title="item.tagName">
                {{ item.tagName }}
              </span>
            </Checkbox>
          </div>
        </div>
        <div class="preview-tag">
          <ui-tag v-for="(item, index) in checkedTagData" @close="handleCloseTag(index)" :closeable="true" :key="index">
            {{ item.tagName }}
          </ui-tag>
          <div class="no-data" v-if="checkedTagData.length === 0">
            <img v-if="themeType === 'dark'" class="no-data-img" src="@/assets/img/common/nodata.png" />
            <img v-else class="no-data-img" src="@/assets/img/common/nodata-light.png" />
            <div class="null-data-text">暂无数据</div>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="confirmHandle" :loading="confirmLoading" class="plr-30"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      importFailShow: false,
      visible: false,
      categoryObj: {},
      directoryIds: [],
      styles: {
        width: '4rem',
      },
      confirmLoading: false,
      searchData: {
        url: '',
        file: [],
      },
      tagName: '',
      initTagData: [],
      tagData: [],
      tagLoading: false,
      checkedTagData: [],
    };
  },
  methods: {
    init() {
      this.visible = true;
      this.initTagList();
      this.checkedTagData = [];
      this.directoryIds = [];
    },
    // 查询所有标签
    async initTagList() {
      try {
        this.tagLoading = true;
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagData = res.data.data.map((row) => {
          this.$set(row, 'checked', false);
          return row;
        });
        this.initTagData = this.tagData;
      } catch (err) {
        console.error(err);
      } finally {
        this.tagLoading = false;
      }
    },
    searchTag() {
      this.tagLoading = true;
      let searchArr = [];
      if (this.tagName !== '') {
        for (let i = 0, len = this.initTagData.length; i < len; i++) {
          let str = this.initTagData[i].tagName;
          if (str.indexOf(this.tagName) !== -1) {
            searchArr.push(this.initTagData[i]);
          }
        }
        this.tagData = searchArr;
      } else {
        this.tagData = this.initTagData;
      }
      this.tagLoading = false;
    },
    checkTag(isCheck, item) {
      if (isCheck) {
        this.checkedTagData.push(item);
      } else {
        const index = this.checkedTagData.findIndex((row) => row.tagId === item.tagId);
        this.checkedTagData.splice(index, 1);
      }
    },
    handleCloseTag(index) {
      let tag = this.tagData.find((row) => row.tagId === this.checkedTagData[index].tagId);
      this.$set(tag, 'checked', false);
      this.checkedTagData.splice(index, 1);
    },
    // 按目录选择
    getDevCategoryData(categoryObj) {
      this.directoryIds = categoryObj.categoryIds;
    },
    async confirmHandle() {
      if (this.directoryIds.length === 0) {
        this.$Message.warning('请选择设备目录');
        return false;
      }
      if (this.checkedTagData.length === 0) {
        this.$Message.warning('请选择标签');
        return false;
      }
      this.confirmLoading = true;
      const formData = {
        directoryIds: this.directoryIds,
        tagIds: this.checkedTagData.map((item) => item.tagId),
      };
      this.$http
        .post(taganalysis.linkTagByDirectoryIds, formData)
        .then((res) => {
          let { data } = res;
          // let result = data.data;
          this.$Message.success(data.msg);
          this.$emit('refreshList');
          this.visible = false;
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      themeType: 'common/getThemeType',
    }),
  },
  components: {
    UiModal: require('@/components/ui-modal.vue').default,
    UiTag: require('@/components/ui-tag').default,
    ChooseCatalogue: require('@/business-components/choose-catalogue.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 20px 0 0 0;
}
.mr-tp20 {
  margin-top: 20px;
}
.flex-1 {
  flex: 1;
}
.betw-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-wrap {
  width: 100%;
  max-height: 750px;
  display: flex;
}

.right-module {
  width: 100%;
  border-top: 1px solid var(--border-modal-footer);
  .search-module {
    padding: 20px;
    //border-bottom: 1px solid var(--border-color);
    @{_deep} .ivu-input-icon {
      color: var(--color-primary) !important;
    }
  }
  .tag-list {
    width: 100%;
    max-height: 260px;
    min-height: 200px;
    padding: 0 20px 20px;
    overflow-x: hidden;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-modal-footer);
    .tag-item {
      display: inline-block;
      vertical-align: middle;
      width: 80px;
    }
  }
  .preview-tag {
    padding: 20px;
    max-height: 210px;
    min-height: 180px;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    .no-data-img {
      width: 100%;
      height: 100%;
    }
  }
  .select-directory-content {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-modal-footer);
    span {
      color: var(--color-content);
      margin-right: 20px;
    }
    .catalogue {
      flex: 1;
    }
  }
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
</style>
