<template>
  <div class="title-container">
    <span class="icon-font color-green f-16" :class="icon"></span>
    <span class="base-text-color color-green ml-xs f-16">
      <slot></slot>
    </span>
    <span class="color-green f-14 fr">
      <slot name="filter"></slot>
    </span>
  </div>
</template>

<script>
/**
 * @example
 * <HomeTitle icon="icon-shebeizichan">
 设备资产
 <template #filter>
 test
 </template>
 </HomeTitle>
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    icon: {},
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.color-green {
  color: #30e9f5;
}
.title-container {
  position: relative;
  padding: 0 10px 0 10px;
  background: linear-gradient(90deg, rgba(64, 225, 254, 0.36) 0%, rgba(64, 225, 254, 0) 100%);
  height: 32px;
  line-height: 32px;
  width: 100%;
}
</style>
