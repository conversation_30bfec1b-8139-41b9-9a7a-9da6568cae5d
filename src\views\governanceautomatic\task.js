import global from '@/util/global';
let typeResource = [
  {
    name: '视图基础数据',
    value: '0',
  },
  {
    name: '人脸视图数据',
    value: '1',
  },
  {
    name: '车辆视图数据',
    value: '2',
  },
  {
    name: '视频流数据',
    value: '3',
  },
  {
    name: 'ZDR人像轨迹数据',
    value: '4',
  },
];
let tableColumns = [
  {
    type: 'selection',
    align: 'center',
    width: 50,
  },
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    align: 'left',
    width: 170,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    align: 'left',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    width: 120,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.ipAddr}`,
    key: 'ipAddr',
    align: 'left',
    minWidth: 130,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.longitude}`,
    slot: 'longitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.latitude}`,
    slot: 'latitude',
    align: 'left',
    width: 110,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlxText',
    align: 'left',
    width: 130,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    align: 'left',
    width: 130,
    tooltip: true,
  },
  // {
  //   title: '检测状态',
  //   slot: 'checkStatusText',
  //   align: 'left',
  //   width: 90,
  //   minWidth: 200,
  //   tooltip: true,
  // },
  {
    width: 100,
    title: '基础信息状态',
    slot: 'baseCheckStatus',
    align: 'left',
  },
  {
    width: 100,
    title: '视图数据状态',
    slot: 'viewCheckStatus',
    align: 'left',
  },
  {
    width: 100,
    title: '视频数据状态',
    slot: 'videoCheckStatus',
    align: 'left',
  },
];
export default {
  typeResource,
  tableColumns,
};
