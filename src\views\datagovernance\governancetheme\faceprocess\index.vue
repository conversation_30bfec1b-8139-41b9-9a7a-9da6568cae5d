<template>
  <div id="faceprocess">
    <div class="faceprocess-left">
      <div class="faceprocess-left-optbtn">
        <Button type="primary" @click="start">开始运行</Button>
        <!-- <Button type="primary">暂停</Button> -->
      </div>
      <div v-for="(item, index) in procedureDatas" :key="index">
        <aggre-connect :propData="item" :dWidth="dWidth" :bWidth="bWidth"></aggre-connect>
        <!-- <aggregate :aggregateOptions="item"></aggregate>
        <connecting-arrow
          :connectingOptions="item.connectingOptions"
        ></connecting-arrow> -->
      </div>
    </div>
    <div class="faceprocess-right">
      <component-package></component-package>
    </div>
    <data-input ref="dataInput"></data-input>
    <imgtest ref="Imgtest" :propData="imgData" :topicId="topicId" @render="getView"></imgtest>
    <face-structure ref="FaceStructure" title="人脸结构化" type="FaceStructure" @render="getView"></face-structure>
    <!-- <face-structure
      ref="FuzzyDetection"
      title="图像模糊检测"
      quality="图片质量分阈值"
      tips="说明：如果选择的多种算法提取图片特征值的质量分均低于阈值，则图像模糊"
    ></face-structure> -->
    <face-structure
      ref="UniqueDetection"
      title="图像模糊检测"
      tips="说明：如果选择的多家算法均只检测一张人脸，则人脸唯一。"
      @render="getView"
    ></face-structure>
    <repeattest ref="Repeattest" :repeatForm="repeatForm" @render="getView"></repeattest>
    <urltest ref="urltest" :repeatForm="repeatForm" @render="getView"></urltest>
    <loading v-if="loading"></loading>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import { faceData } from './faceoptions';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'faceprocess',
  mixins: [dealWatch],
  props: {},
  data() {
    return {
      aggregateOptions: [],
      imgData: {
        items: [
          {
            key1: 'important',
            key2: 'importantTime',
            value1: '',
            value2: '1',
            label: '重点人脸卡口数据时延：',
          },
          {
            key1: 'general',
            key2: 'generalTime',
            value1: '',
            value2: '1',
            label: '普通人脸卡口数据时延：',
          },
        ],
      },
      procedure: [],
      procedureDatas: [],
      queryId: '',
      topicType: '',
      topicId: '',
      repeatForm: {
        items: [
          {
            label1: '间隔时间',
            key: 'interval',
            value: '',
            label2: '秒内',
          },
          {
            label1: '同一目标抓拍数量阈值',
            key: 'capture',
            value: '',
            label2: '次',
          },
          {
            label1: '同一目标相似度阈值',
            key: 'same',
            value: '',
            label2: '%',
          },
        ],
      },
      loading: false,
      dWidth: '13.82%',
      bWidth: '15.23%',
    };
  },
  created() {
    this.aggregateOptions = faceData;
  },
  mounted() {
    this.loading = true;
    this.getView();
  },
  methods: {
    async getView() {
      try {
        const id = this.$route.query.tab;
        let res = await this.$http.get(governancetheme.view, {
          params: { id: id },
        });
        this.topicType = res.data.data.topicType;
        this.topicId = res.data.data.id;
        this.procedure = res.data.data.componentList;
        this.procedureDatas = this.handleData(this.procedure);
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    handleData(proData) {
      let group = [];
      proData.forEach((item) => {
        group.push(item.sort);
      });
      group = Array.from(new Set(group));
      let newDatas = group.map((item) => {
        let arr = [];
        proData.forEach((dItem) => {
          if (dItem.sort === item) {
            arr.push(dItem);
          }
        });
        return arr;
      });
      return this.aggregateOptions.map((item, index) => {
        let obj = JSON.parse(JSON.stringify(item));
        obj.datas = item.datas.map((childItem, childIndex) => {
          childItem = { ...newDatas[index][childIndex], ...childItem };
          childItem['isConfigure'] = newDatas[index][childIndex]['isConfigure'];
          return childItem;
        });
        return obj;
        // item.datas = item.datas.map((childItem, childIndex) => {
        //   childItem = { ...childItem, ...newDatas[index][childIndex] };
        //   childItem["isConfigure"] = newDatas[index][childIndex]["isConfigure"]
        //   // childItem = Object.assign(newDatas[index][childIndex], childItem)
        //   return childItem;
        // });
        // return item;
      });
    },
    // 开始运行
    async start() {
      try {
        if (this.loading) {
          return false;
        }
        this.loading = true;
        let res = await this.$http.post(governancetheme.faceLibInfoCheck);
        if (res.data.code == '200') {
          this.loading = false;
          this.$Message.success('运行成功！');
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    $route() {
      if (!this.$route.query.tab) {
        return false;
      }
      this.getView();
    },
  },
  components: {
    // ConnectingArrow: require('@/views/datagovernance/governancetheme/components/connecting-arrow.vue').default,
    // Aggregate: require('@/views/datagovernance/governancetheme/components/aggregate.vue').default,
    DataInput: require('./components/dataInput.vue').default,
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    AggreConnect: require('../components/aggre-connect.vue').default,
    Imgtest: require('./components/imgtest.vue').default,
    urltest: require('./components/urltest.vue').default,
    FaceStructure: require('./components/face-structure.vue').default,
    Repeattest: require('./components/repeattest.vue').default,
  },
};
</script>
<style lang="less" scoped>
#faceprocess {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  // padding: 24px;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  .faceprocess-left {
    position: relative;
    flex: 1;
    height: 100%;
    overflow: auto;
    // border-right: 1px solid var(--border-color);
    &-optbtn {
      position: absolute;
      top: 20px;
      right: 28px;
      // button:first-child {
      //   margin-right: 20px;
      // }
    }
  }
  .faceprocess-right {
    // position: absolute;
    // right: 0;
    // overflow: auto;
    width: 243px;
    height: 100%;
    background-color: var(--bg-content);
  }
}
</style>
<style lang="less">
.faceprocess {
  .ivu-input {
    border-color: var(--color-primary) !important;
  }
}
</style>
