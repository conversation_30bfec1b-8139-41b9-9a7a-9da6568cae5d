<template>
  <ui-modal v-model="trajectoryVisible" :title="title" :footerHide="true" :width="width">
    <div class="content" v-if="trajectoryVisible">
      <div class="top">
        <div class="left">
          <ui-image class="img fl" :src="personObj.identityPhoto" alt="" />
          <div class="fr left-content">
            <p class="mb-sm">姓名：{{ personObj.name }}</p>
            <p class="mb-sm">证件号：{{ personObj.idCard }}</p>
            <tags-more
              v-if="personObj.personTypes"
              :tagList="personObj.personTypes"
              :defaultTags="4"
              placement="left-start"
              bgColor="#2D435F"
            ></tags-more>
          </div>
        </div>
        <div class="center">
          <div class="f-12">图像对比</div>
          <i class="icon-font icon-shuangjiantou"></i>
        </div>
        <div class="right">
          <ui-image class="img fl" :src="item.trackImage" alt="" />
          <div class="fr right-content">
            <p class="mb-sm">{{ item.shotTime }}</p>
            <p class="mb-sm">
              {{ item.catchPlace ? item.catchPlace : '暂无数据' }}
            </p>
          </div>
        </div>
        <div class="clear"></div>
      </div>
      <div>
        <ul>
          <li v-for="(item, index) in resultList" :key="index">
            <div v-if="item.name !== '综合判定'">
              <div>{{ item.name }}</div>
              <div>{{ item.value }}</div>
            </div>
            <div v-if="item.name === '综合判定'" class="nthetic-judgment">
              <div class="nthetic-name">{{ item.name }}</div>
              <div>
                <i class="icon-font icon-shitongyiren-01" v-if="item.value == 1"></i>
              </div>
              <div>
                <i class="icon-font icon-bushitongyiren-01" v-if="item.value != 1"></i>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  name: 'captureDetail',
  props: {},
  data() {
    return {
      width: 1500,
      title: '轨迹准确性存疑原因',
      trajectoryVisible: false,
      resultList: [],
      personObj: null,
      item: null,
    };
  },
  async created() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  methods: {
    init() {
      // this.resultList = [
      //   { name: '检测算法', value: '识别结果'},
      //   // { name: '原始算法', value: this.item.similarity*100+'%'},
      //   { name: '商汤人脸结构化算法名称', value: '90%'},
      //   { name: '海康人脸结构化算法名称', value: '90%'},
      //   { name: '综合判定', value: '不是同一人'},
      // ]
      // var obj = { name: '检测算法', value: '识别结果'};
      var num = (this.item.similarity * 100).toFixed(2);
      this.resultList = [
        { name: '检测算法', value: '识别结果' },
        { name: '原始算法', value: num + '%' },
      ];
      if (this.item.algResult == '') return;
      var arr = JSON.parse(this.item.algResult);
      console.log(arr, 'arrr');
      if (arr) {
        arr.forEach((it) => {
          var obj2 = {
            name: it.algorithmType,
            value: (it.score || 0) + '%',
          };

          this.resultList.push(obj2);
        });
      }
      var obj1 = {
        name: '综合判定',
        value: this.item.synthesisResult,
      };
      this.resultList.push(obj1);
    },
    show(personObj, item) {
      console.log(personObj, item, 'personObj, item');
      this.personObj = personObj;
      this.item = item;
      this.init();
      this.trajectoryVisible = true;
    },
  },
  watch: {
    trajectoryVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.trajectoryVisible = val;
    },
  },
  components: { TagsMore: require('./tags-more.vue').default },
};
</script>

<style lang="less" scoped>
.content {
  height: 560px;
  color: #fff;
  padding-left: 0px;
  .top {
    height: 200px;
    display: flex;
    .left,
    .right {
      position: relative;
      width: 40%;
      height: 100%;
      flex: 1;
      background: #0b2a52;
      img,
      .img {
        width: 150px;
        height: 150px;
        margin: 25px;
      }
      .left-content,
      .right-content {
        width: calc(100% - 200px);
        height: 100%;
        padding-top: 50px;
      }
    }
    .center {
      width: 10%;
      text-align: center;
      div {
        margin-top: 60px;
      }
      .icon-shuangjiantou {
        color: var(--color-primary);
        font-size: 24px;
      }
    }
  }

  ul {
    margin-top: 30px;
    font-size: 14px;
    li {
      height: 36px;
      line-height: 36px;
      div {
        float: left;
        width: 100%;
        padding: 0 20px;
        div {
          width: 50%;
        }
      }
      .nthetic-name {
        color: var(--color-primary);
      }
      .nthetic-judgment {
        height: 100px;
        line-height: 100px;
        width: 100%;
        background: #092955;
        .icon-shitongyiren-01 {
          font-size: 90px;
          color: var(--color-success);
        }
        .icon-bushitongyiren-01 {
          font-size: 90px;
          color: var(--color-failed);
        }
      }
      &:nth-child(odd) {
        background: #092955;
      }

      &:nth-child(even) {
        background: #062042;
      }
    }
    li:first-child {
      border-bottom: 1px solid #092f69;
    }
  }
}
.clear {
  clear: both;
}
</style>
