import { MapAdapter } from './core/MapAdapter';
import { ClusterMarkerFactory, ClusterMarkerParamsNameType } from './core/model/cluster.factory';
import { InfoWindowFactory } from './core/model/infowindow.factory';
import { OverlayFactory } from './core/model/overlay.factory';
import { TraceAnalyzeFactory } from './core/model/trace.analyze.factory';
import { MapToolBar } from './business/MapToolBar';
import { MapBaseLayerUtil } from './core/utils/MapBaseLayerUtil';
import ConvertModelUtil from './core/utils/ConvertModelUtil';
import coordHelper from './core/utils/projUtil';
// import map from "@/config/api/map";
// import axios from '@/config/http/http';

const CLUSTER_LAYER_NAME = '聚合图层';
const CLUSTER_LAYER_NAME1 = '所有标注聚合图层';

export class NPGisMapMain {
  map = null;
  mapId = null;
  mapContainer;
  clusterMarkerFactory = null;
  mapAdapter;
  infoWindowFactory = null;
  locateMouseEventUtil;
  traceAnalyzeFactory;
  mapTools;
  mapGeometry;
  circleContainTexts;
  heatMapLayer;
  overlay;
  helper;
  overlays;
  defaultOptions = {
    //是否添加鹰眼
    overviewShow: true,
    //是否导航条
    navigationShow: false,
    //是否比例尺
    scaleShow: true,
  };
  mapLayerChangeUtil;
  mapGeoRegion;
  localStorageTheme;

  init(mapId, _mapConfig, _mapStyle, callBack) {
    this.localStorageTheme = localStorage.getItem('theme') || 'dark';
    this.mapId = mapId;
    this.mapContainer = document.getElementById(mapId);
    let mapConfig = _mapConfig;
    // 初始化图层
    // 初始化地图
    this.mapLayerChangeUtil = new MapPlatForm.Base.MapConfig();
    var resultJson = this.mapLayerChangeUtil.createMap(this.mapContainer, mapConfig);
    this.map = resultJson.map;
    this.map.setMapStyle({
      styleJson: !!_mapStyle ? _mapStyle : [],
    });
    // this.map = new NPMapLib.Map(this.mapContainer, mapConfig.mapOpts);
    this.mapAdapter = new MapAdapter(this.map);
    this.clusterMarkerFactory = new ClusterMarkerFactory(this.mapAdapter);
    this.infoWindowFactory = new InfoWindowFactory();
    this.traceAnalyzeFactory = new TraceAnalyzeFactory(this.mapAdapter);
    this.mapTools = new MapToolBar(new MapPlatForm.Base.MapTools(this.map), this.mapAdapter);
    this.helper = new coordHelper();
    // var ctrl = new NPMapLib.Controls.MousePositionControl();
    // this.mapAdapter.addControl(ctrl);
    this.mapGeometry = new MapPlatForm.Base.MapGeometry(this.map);
    // 加载地图展示用的图层
    // MapBaseLayerUtil.initMaplayer(this.map, mapConfig);
    // if (mapConfig.mapOpts && mapConfig.mapOpts.centerPoint && mapConfig.mapOpts.centerPoint[0] && mapConfig.mapOpts.centerPoint[1]) {
    //     this.mapAdapter.centerAndZoom(this.mapAdapter.getPoint(mapConfig.mapOpts.centerPoint[0], mapConfig.mapOpts.centerPoint[1]),
    //         mapConfig.vectorLayer &&
    //         mapConfig.vectorLayer[0] &&
    //         mapConfig.vectorLayer[0].layerOpt &&
    //         mapConfig.vectorLayer[0].layerOpt.layerInfo &&
    //         mapConfig.vectorLayer[0].layerOpt.layerInfo.defaultZoom);
    // }
    // 绑定地图右键方法, 点击右键时, 用于取消一些地图绘制操作
    // this.mapAdapter.addEventListener(NPMapLib.MAP_EVENT_RIGHT_CLICK, () => {
    //     this.cancelDraw();
    // });
    // this.unEnableMouseRightClick();
  }

  cancelDraw() {
    this.mapTools.cancelDraw();
  }

  /**
   * 获得select操作选中的点位
   * @param geometry
   * @returns {Array}
   */
  getSelectPoints(geometry) {
    let points = this.clusterMarkerFactory.getPoints() || [];
    let i,
      len,
      result = [];
    let overlayLayer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    //console.debug("getBounds()",geometry.getBounds(), geometry.getExtent());
    console.log(overlayLayer);
    overlayLayer.containFeatures(geometry, (marker) => {
      let temp = ConvertModelUtil._convertClusterMarkerEx2MapPointModel(marker);
      if (temp != null) {
        result.push(temp);
      }
    });
    return result;
  }

  selectLine(callBackMethod, style) {
    this.mapTools.drawLine((extent, geometry) => {
      //TODO 因npgis本身api不支持, 故未实现线选
      console.error('线选功能暂未实现.');
    }, style);
  }

  /**
   *
   * @param {*} callBackMethod
   * @param {*} style
   * @param anleEdit 用来控制是否可以编辑该区域
   * 这里增加了区域返回getPosition by：zml
   */
  selectPolygon(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawPolygon(
      (extent, geometry) => {
        if (callBackMethod) {
          console.log(geometry, 'geometry');
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'polygon'));
        }
      },
      anleEdit,
      style,
    );
  }

  selectRectangle(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawRectangle(
      (extent, geometry) => {
        if (callBackMethod) {
          console.log(geometry, 'geometry');
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'rectangle'));
        }
      },
      anleEdit,
      style,
    );
  }

  selectCircle(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawCircle(
      (extent, geometry) => {
        console.log(geometry, 'geometry');
        if (callBackMethod) {
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'circle'));
        }
      },
      anleEdit,
      style,
    );
  }

  /**
   * 根据不同的框处理框选后的位置
   */
  getPosition(geometry, type) {
    let position = {
      points: '',
      type: type,
    };
    switch (type) {
      case 'rectangle':
      case 'polygon':
        position.center = geometry.getCentroid();
        position.points = geometry._points;
        break;
      case 'circle':
        position.center = geometry.getCenter();
        position.radius = geometry.getRadius();
        break;
      default:
        return '暂不支持此方法';
    }
    return position;
  }

  // 画出定位信息
  /**
   *
   * @param {*} position 该区域所在定位信息
   * @param {*} anleEdit 该区域是否可以编辑
   * @param {*} callBackMethod 回调函数
   */
  drawPolygon(position, anleEdit = false, callBackMethod) {
    let points = [];
    points = position.points.map((row) => {
      return new NPMapLib.Geometry.Point(row.lon, row.lat);
    });
    this.overlay = new NPMapLib.Geometry.Polygon(points, {
      color: this.localStorageTheme === 'dark' ? '#07fefb' : '#2C86F8', //颜色
      fillColor: this.localStorageTheme === 'dark' ? '#07fefb' : 'rgba(44,134,248,0.2)', //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.34, //填充的透明度，取值范围0 - 1
    });

    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
        if (callBackMethod) {
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'polygon'));
        }
      });
    }
  }

  drawRectangle(position, anleEdit = false, callBackMethod) {
    let points = [];
    points = position.points.map((row) => {
      return new NPMapLib.Geometry.Point(row.lon, row.lat);
    });
    let overlayStyle = {
      color: this.localStorageTheme === 'dark' ? '#3289fd' : '#2C86F8', //颜色
      fillColor: this.localStorageTheme === 'dark' ? 'rgb(50, 137, 253)' : 'rgba(44,134,248,0.2)', //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.33, //填充的透明度，取值范围0 - 1
    };
    this.overlay = new NPMapLib.Geometry.Polygon(points, overlayStyle);
    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
        if (callBackMethod) {
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'rectangle'));
        }
      });
    }
  }

  drawCircle(position, anleEdit = false, callBackMethod) {
    let center = position.center;
    let radius = position.radius;
    let overlayStyle = {
      color: this.localStorageTheme === 'dark' ? 'blue' : '#2C86F8', //颜色
      fillColor: this.localStorageTheme === 'dark' ? 'rgb(50, 137, 253)' : 'rgba(44,134,248,0.2)', //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.5, //填充的透明度，取值范围0 - 1
    };
    this.overlay = new NPMapLib.Geometry.Circle(center, radius, overlayStyle);
    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
        if (callBackMethod) {
          callBackMethod(this.getSelectPoints(geometry), this.getPosition(geometry, 'circle'));
        }
      });
    }
  }

  // 清除定位信息
  removeRectangle() {
    if (this.overlay) {
      this.overlay.disableEditing();
    }
    this.mapAdapter.removeOverlay(this.overlay);
  }

  showHeatMap() {
    if (this.heatMapLayer) {
      this.heatMapLayer.show();
      return;
    }
    console.log('this.heatMapLayer 未初始化渲染');
  }

  hideHeatMap() {
    if (this.heatMapLayer) {
      this.heatMapLayer.hide();
      return;
    }
    console.log('this.heatMapLayer 未初始化渲染');
  }

  updateHeatMapLayer(heatMapLayer) {
    this.heatMapLayer = heatMapLayer;
    this.heatMapLayer.show();
  }

  removeHeatMap() {
    if (this.heatMapLayer) {
      let dataset = {
        max: 0,
        data: [],
      };
      this.heatMapLayer.setDataset(dataset);
      this.map.removeLayerByName(this.heatMapLayer.getName());
      this.heatMapLayer = null;
    }
  }

  /**
   *  渲染显示热力图
   * @time: 2017-11-23 10:39:27
   * @params:
   * @return:
   */
  renderHeatMap(dataList, radius) {
    // 只存在一个 热力图
    let _heatMapName = 'heatLayer';
    let opt = {
      //   isBaseLayer: false,
      opacity: 1.0,
      projection: 'EPSG:900913',
      visible: true,
      radius: radius,
      name: _heatMapName,
    };
    let heatlayer;
    if (this.heatMapLayer) {
      heatlayer = this.heatMapLayer;
    } else {
      heatlayer = new NPMapLib.Layers.HeatMapLayer(_heatMapName, opt);
    }

    // max 数据的 获取
    let countMax = 0;
    let countMin = 0;
    let currentCount = 0;
    let _dataList = dataList.map((val, index) => {
      currentCount = val.count || 0;
      countMax = Math.max(countMax, currentCount);
      countMin = Math.min(countMin, currentCount);
      return {
        lat: val.lat,
        lon: val.lon,
        count: currentCount,
      };
    });
    let dataset = {
      max: countMax,
      min: countMin,
      data: _dataList,
    };
    if (!this.heatMapLayer) {
      this.map.addLayers([heatlayer]);
    }
    heatlayer.setDataset(dataset);
    this.updateHeatMapLayer(heatlayer);
  }

  /**
   * 清理地图上的一些无用遮罩层
   * 由于在界面上起了一些悬浮框等，因为各种原因, 事件没有监听好, 导致悬浮框没有正常清掉
   * 故加上此方法，在一些容易引起问题的操作上调用，确保地图界面干净
   * @private
   */
  _cleanMap() {
    this.hideMouseOverDom();
  }

  hideMouseOverDom() {
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
  }

  showMouseOverDom(marker) {
    // 在地图上展示一个悬浮框, 用于显示当前点位的文字信息
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
    let name = marker.title,
      point = marker.getPosition(),
      pixel = this.mapAdapter.pointToPixel(point),
      _container,
      _offset,
      _body,
      _scrollTop,
      _scrollLeft,
      mouseLeft,
      mouseTop,
      mouseOverDom,
      _left,
      _top;

    if (pixel == null) return;
    _container = document.getElementById(this.mapId);
    _body = document.body;
    _scrollTop = _body.scrollTop;
    _scrollLeft = _body.scrollLeft;
    _left = pixel.x + _container.getBoundingClientRect().left;
    _top = pixel.y + _container.getBoundingClientRect().top;
    // 这里已经算出正确的坐标点位了, 然后还需要根据长度高度进行微调, 让底部箭头刚好指在点位上方
    mouseOverDom = document.createElement('div');
    mouseOverDom.classList.add('u-map-hover');
    mouseOverDom.style.opacity = 0;
    mouseOverDom.innerHTML =
      "<div class='hover-container'>" +
      "<div class='hover-content'>" +
      name +
      '</div>' +
      '</div>' +
      "<div class='hover-bottom'></div>";
    _body.appendChild(mouseOverDom);
    _body = null;
    let width = mouseOverDom.offsetWidth;
    let height = mouseOverDom.offsetHeight;
    // 减去本身dom偏移和点位图标的偏移
    _top -= height + 12;
    _left -= Math.floor(width / 2);
    mouseOverDom.style.top = _top + 'px';
    mouseOverDom.style.left = _left + 'px';
    mouseOverDom.style.opacity = 1;
    this.mouseOverDom = mouseOverDom;
  }

  /**
   * 加载坐标点位到地图中但不显示
   * 注意: 此方法每个地图实例应当只调用一次
   * @param points
   */
  addMarkers(points, opts) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME);
    }
    // 地图图层加载时 需要经纬度为数字类型 否则如果为字符串类型则会报错  X *******错误
    let arr = points.map((row) => {
      row.Lon = Number(row.Lon);
      row.Lat = Number(row.Lat);
      return row;
    });
    this.clusterMarkerFactory.setPoints(arr);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(arr);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME,
      true,
      OverlayFactory.getClusterOverlayOpt(opts),
    );
    this.map.addLayer(overlayLayer);
    overlayLayer.hide();
    overlayLayer.addOverlay(clusterPoints);
  }

  /**
   * 加载坐标点位到地图中并且显示
   * 注意: 此方法每个地图实例应当只调用一次
   * @param points
   *  @param customMarker 自定义图标
   */
  renderMarkers(points, opts, notCluster, customMarker = {}) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME);
    }
    this.clusterMarkerFactory.setPoints(points);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(points);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME,
      true,
      OverlayFactory.getClusterOverlayOpt(opts, customMarker),
    );
    // TODO:这里取消聚合图层的显示临时解决，找不到聚合图层不加载的函数，以后如果找到可以删除isCluster判断
    // notCluster && (overlayLayer.maxZoom = 10);
    // this.map.addLayer(overlayLayer);
    // TODO:现版本不需要聚合数据 resolve: zml
    this.map.addLayer(overlayLayer);
    // let size = new NPMapLib.Geometry.Size(20, 20);
    // let icon = new NPMapLib.Symbols.Icon(require('@/assets/img/map/map-camera-normal.png?v=1'), size);
    // points.forEach(row => {
    //         let pt = new NPMapLib.Geometry.Point(row.Lon, row.Lat);
    //         let marker = new NPMapLib.Symbols.Marker(pt);
    //         marker.setIcon(icon);
    //         marker.setTitle(row.Name);
    //         overlayLayer.addOverlay(marker);
    // marker.addEventListener('mouseover', () => {
    //     this.showMouseOverDom(marker);
    // })
    // marker.addEventListener('mouseout', () => {
    //     this.hideMouseOverDom();
    // })
    // })
    overlayLayer.addOverlay(clusterPoints);
    overlayLayer.setZIndex(500);
  }

  renderAddMarkers(points, opts, notCluster) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME1);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME1);
    }
    this.clusterMarkerFactory.setPoints(points);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(points);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME1,
      true,
      OverlayFactory.getClusterOverlayOpt(opts),
    );
    // TODO:这里取消聚合图层的显示临时解决，找不到聚合图层不加载的函数，以后如果找到可以删除isCluster判断
    // notCluster && (overlayLayer.maxZoom = 10);
    this.map.addLayer(overlayLayer);
    //console.log(overlayLayer, 'overlayLayer')
    // TODO:现版本不需要聚合数据 resolve: zml
    // let size = new NPMapLib.Geometry.Size(20, 20);
    // let icon = new NPMapLib.Symbols.Icon(require('@/assets/img/map/map-camera-normal.png?v=1'), size);
    // points.forEach(row => {
    //         let pt = new NPMapLib.Geometry.Point(row.Lon, row.Lat);
    //         let marker = new NPMapLib.Symbols.Marker(pt);
    //         marker.setIcon(icon);
    //         marker.setTitle(row.Name);
    //         overlayLayer.addOverlay(marker);
    //         marker.addEventListener('click', (e) => {
    //             // this.showMouseOverDom(marker);
    //             console.log(5552000,e)
    //         })
    //         marker.addEventListener('mouseout', () => {
    //             this.hideMouseOverDom();
    //         })
    //         marker.addEventListener('mouseout', () => {
    //             this.hideMouseOverDom();
    //         })
    //     })
    // console.log('clusterPoints',clusterPoints)
    overlayLayer.addOverlay(clusterPoints);
    overlayLayer.setZIndex(500);
  }

  convertSystemPointArr2MapPoint(datas, getNameFunc) {
    return ConvertModelUtil.convertSystemPointArr2MapPoint(datas, getNameFunc);
  }

  /**
   * 创建一个窗口, 返回窗口的唯一编码
   * @param lon
   * @param lat
   * @param eventOpt
   * @returns {uuid: string}
   */
  createInfoWindow(lon, lat, infoWindowOpt) {
    let infoWindow = InfoWindowFactory.getInfoWindow(
      new NPMapLib.Geometry.Point(lon, lat),
      null,
      null,
      InfoWindowFactory.getInfoWindowOpts(infoWindowOpt),
    );
    let uuid = this.infoWindowFactory.addInfoWindow(infoWindow);
    return uuid;
  }

  closeInfoWindow(winId) {
    this.mapAdapter.closeInfoWindow(this.infoWindowFactory.removeInfoWindow(winId));
  }

  openInfoWindow(winId, domHtml, eventOpt) {
    let win = this.infoWindowFactory.getById(winId);
    this.infoWindowFactory.addEventListener(win, winId, eventOpt);
    this.mapAdapter.addOverlay(win);
    win.setContentDom(domHtml);
    win.open();
  }

  /**
   * 此方法用于对前端传来的聚合点位配置进行一点加工
   * @param opts
   */
  cookOverlayLayerOptsEx(opts) {
    // 截取mouseover, 设置默认的地图mouseover方法, 并在默认的方法后调用业务层绑定的方法
    let mouseoverFunc = opts.mouseover;
    let mouseoutFunc = opts.mouseout;
    let _click = opts.click;
    opts.mouseover = (marker) => {
      // this.showMouseOverDom(marker);
      if (typeof mouseoverFunc === 'function') {
        mouseoverFunc(marker);
      }
    };

    opts.mouseout = (marker) => {
      this.hideMouseOverDom();
      if (typeof mouseoutFunc === 'function') {
        mouseoutFunc(marker);
      }
    };

    opts.click = (marker) => {
      this.hideMouseOverDom();
      if (typeof _click === 'function') {
        _click(marker);
      }
    };
  }

  /**
   * 添加geoJSON给地区描边
   * @param {地图geoJSON} geoRegions
   * @param {点击区域回调} callBack
   */
  addGeoRegions(geoRegions, callBack) {
    if (!!geoRegions && geoRegions.length > 0) {
      this.mapGeoRegion = new MapPlatForm.Base.MapGeoRegion(this.map, {
        mouseOver: (region) => {
          region.oldFillColor = region.fillColor;
          region.setStyle({
            fillColor: region.oldFillColor.replace('.3', '.2'),
            strokeColor: '#4394ff',
          });
          this.mapGeoRegion.refresh();
        },
        mouseOut: (region) => {
          region.resetStyle();
          this.mapGeoRegion.refresh();
        },
        click: (region) => {
          if (callBack && typeof callBack === 'function') {
            callBack(region.name);
          }
        },
        fontAvoid: false,
        minZoom: 5,
        maxZoom: 17,
      });
      const regionsList = this.processGeoRegions(geoRegions);
      this.mapGeoRegion.addGeoRegions(regionsList);
    }
  }

  /**
   * 修改geoJSON更改样式以及转换经纬度坐标系
   * @param {地图geoJSON} geoRegions
   */
  processGeoRegions(geoRegions) {
    // 如果已经转换过不再转换
    if (geoRegions[0]?.properties?.strokeColor) {
      return geoRegions;
    }
    let i = 0;
    const regionOpt = {
      fillColorList: [
        'rgba(0, 168, 255, .3)',
        'rgba(147, 67, 23, .3)',
        'rgba(126, 167, 73, .3)',
        'rgba(182, 16, 82, .3)',
        'rgba(24, 11, 201, .3)',
        'rgba(210, 77, 243, .3)',
        'rgba(182, 16, 82, .3)',
        'rgba(18, 239, 216, .3)',
        'rgba(103, 41, 196, .3)',
        'rgba(178, 139, 19, .3)',
        'rgba(174, 179, 30, .3)',
        'rgba(253, 149, 98, .3)',
        'rgba(18, 202, 105, .3)',
        'rgba(18, 210, 239, .3)',
        'rgba(109, 63, 243, .3)',
      ],
      strokeColor: 'rgba(185, 255, 255, 1)',
    };
    return geoRegions.map((row) => {
      row.properties.font = '14px Microsoft YaHei';
      row.properties.fontColor = 'transparent';
      row.properties.fontFillColor = this.localStorageTheme === 'dark' ? '#fff' : 'rgba(0, 0, 0, 0.6)';
      row.properties.fillColor = regionOpt.fillColorList[i];
      row.properties.strokeColor = regionOpt.strokeColor;
      i === 15 ? (i = 0) : i++;
      row.geometry.coordinates.forEach((rw) => {
        if (row.geometry.type === 'MultiPolygon') {
          rw[0].forEach((r) => {
            let gcj = this.helper.gcj2wgs(r[0], r[1]);
            r[0] = gcj.lon;
            r[1] = gcj.lat;
          });
        } else {
          rw.forEach((r) => {
            let gcj = this.helper.gcj2wgs(r[0], r[1]);
            r[0] = gcj.lon;
            r[1] = gcj.lat;
          });
        }
      });
      return row;
    });
  }
  /**
   *
   * @param {*} fun 监听地图缩放更改回调方法
   */
  listenerZoomChange(fun) {
    this.map.addEventListener(NPMap.MAP_EVENT_ZOOMCHANGE, fun);
  }

  removeListenerZoomChange() {
    this.map.removeEventListener(NPMap.MAP_EVENT_ZOOMCHANGE);
  }
  /**
   *
   * @param {*} fun 监听地图拖拽结束回调
   */
  listenerDragEnd(fun) {
    this.map.addEventListener(NPMap.MAP_EVENT_DRAG_END, fun);
  }

  removeListenerDragEnd() {
    this.map.removeEventListener(NPMap.MAP_EVENT_DRAG_END);
  }

  /**
   *
   * @returns 获取可视区域经纬度
   */
  getExtent() {
    return this.map.getExtent();
  }

  setCenter(val) {
    this.map.setCenter(val);
  }

  /**
   * 移除单个点位
   * @param point
   */
  removeMarker1(objectId) {
    if (!objectId) return;
    this.clusterMarkerFactory.removePointByParams('ObjectID', objectId);
    this.refreshMarker1();
  }

  refreshMarker1() {
    // 由于gis地图有bug, 不能动态增加删除聚合点位, 所以在布点的时候 需要移除聚合对象再进行聚合的显示
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME1);
    if (layer != null) {
      layer.removeAllOverlays();
      layer.addOverlay(this.clusterMarkerFactory.getClusterPoints(this.clusterMarkerFactory.getPoints()));
    }
  }

  clearDraw() {
    this.cancelDraw();
    let overlays = this.mapTools.getSelectGeometrys();
    if (!overlays) return;
    let i, len;
    for (i = 0, len = overlays.length; i < len; i++) {
      overlays[i].disableEditing();
      this.mapAdapter.removeOverlay(overlays[i]);
    }
    this.mapTools.resetSelectGeometrys();
  }

  /**
   * 清理地图上的一些无用遮罩层
   * 由于在界面上起了一些悬浮框等，因为各种原因, 事件没有监听好, 导致悬浮框没有正常清掉
   * 故加上此方法，在一些容易引起问题的操作上调用，确保地图界面干净
   * @private
   */
  _cleanMap() {
    this.hideMouseOverDom();
  }

  hideMouseOverDom() {
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
  }

  removeHeatMap() {
    if (this.heatMapLayer) {
      let dataset = {
        max: 0,
        data: [],
      };
      this.heatMapLayer.setDataset(dataset);
      this.map.removeLayerByName(this.heatMapLayer.getName());
      this.heatMapLayer = null;
    }
  }

  removeCircleContainTexts() {
    if (this.circleContainTexts && this.circleContainTexts.length > 0) {
      this.map.removeOverlays(this.circleContainTexts);
      this.circleContainTexts = null;
    }
  }

  destoryGeoRegion() {
    !!this.mapGeoRegion && this.mapGeoRegion.destory();
  }

  /**
   * 销毁地图
   */
  destroy() {
    if (this.mapAdapter != null) {
      this.removeCircleContainTexts();
      this.removeHeatMap();
      this._cleanMap();
      this.destoryGeoRegion();
      this.mapAdapter.destroyMap();
      this.map = null;
      this.mapLayerChangeUtil = null;
      this.mapContainer.oncontextmenu = null;
      this.mapContainer = null;
      this.mapAdapter = null;
      this.clusterMarkerFactory = null;
      this.infoWindowFactory = null;
      this.locateMouseEventUtil = null;
      this.mapTools = null;
      if (this.traceAnalyzeFactory) {
        this.traceAnalyzeFactory.destroy();
      }
      this.traceAnalyzeFactory = null;
      this.mouseOverDom = null;
    }
  }

  // unEnableMouseRightClick() {
  //     function handler(event) {
  //         event = event || window.event;

  //         if (event.stopPropagation)
  //             event.stopPropagation();

  //         event.cancelBubble = true;
  //         return false;
  //     }

  //     this.mapContainer.oncontextmenu = function() {
  //         return false;
  //     }
  // }

  /**
   *回到可视区域
   */
  zoomToExtend(extent) {
    this.map.zoomToExtent(extent);
  }

  /**
   * 监听地图事件
   */
  addMapEventListener(type, callBackMethod) {
    this.map.addEventListener(NPMap[type], (event) => {
      if (callBackMethod) {
        callBackMethod(event);
      }
    });
  }

  // 监听maker标注事件
  addMarkerEventListener(marker, type, callBackMethod) {
    marker.addEventListener(NPMapLib[type], (event) => {
      if (callBackMethod) {
        callBackMethod(event);
      }
    });
  }
  // 移除监听maker标注事件
  removeMarkerEventListener(marker, type) {
    marker.removeEventListener(type);
  }

  /**
   *  移除监听事件
   */
  removeEvent(type) {
    this.map.removeEventListener(NPMap[type]);
  }

  //清空layers数组
  clearLayers() {
    this.overlays?.length
      ? this.overlays.forEach((item) => {
          this.mapAdapter.removeOverlay(item);
        })
      : '';
    this.overlays = [];
  }

  //绘制多个多边形
  drawMultiPolygon(pps) {
    this.clearLayers();
    for (var i = pps.length - 1; i >= 0; i--) {
      var ps = this.mapGeometry.getGeometryByGeoJson(pps[i].geometry, this.map);
      ps.setStyle({
        color: this.localStorageTheme === 'dark' ? '#07fefb' : '#2C86F8', //颜色
        fillColor: this.localStorageTheme === 'dark' ? '#07fefb' : 'rgba(44,134,248,0.2)', //填充颜色
        weight: 2, //宽度，以像素为单位
        opacity: 1, //透明度，取值范围0 - 1
        fillOpacity: 0, //填充的透明度，取值范围0 - 1
      });
      this.overlays.push(ps);
      this.mapAdapter.addOverlay(ps);
    }
  }
}
