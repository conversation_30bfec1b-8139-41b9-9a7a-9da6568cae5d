import { MapAdapter } from "./core/MapAdapter";
import {
  ClusterMarkerFactory,
  ClusterMarkerParamsNameType,
} from "./core/model/cluster.factory";
import { InfoWindowFactory } from "./core/model/infowindow.factory";
import { OverlayFactory } from "./core/model/overlay.factory";
import { TraceAnalyzeFactory } from "./core/model/trace.analyze.factory";
import { MapToolBar } from "./business/MapToolBar";
import { MapBaseLayerUtil } from "./core/utils/MapBaseLayerUtil";
import ConvertModelUtil from "./core/utils/ConvertModelUtil";
// import map from "@/config/api/map";
// import axios from '@/config/http/http';
import Vue from "vue";
import setBuffArea from "@/components/map/set-buff-area.vue";

const CLUSTER_LAYER_NAME = "聚合图层";
const CLUSTER_LAYER_NAME1 = "所有标注聚合图层";

export class NPGisMapMain {
  map = null;
  mapId = null;
  mapContainer;
  clusterMarkerFactory = null;
  mapAdapter;
  infoWindowFactory = null;
  locateMouseEventUtil;
  traceAnalyzeFactory;
  mapTools;
  mapService;
  mapGeometry;
  circleContainTexts;
  heatMapLayer;
  overlay;

  defaultOptions = {
    //是否添加鹰眼
    overviewShow: true,
    //是否导航条
    navigationShow: false,
    //是否比例尺
    scaleShow: true,
  };
  mapLayerChangeUtil;
  mapGeoRegion;
  skillsLayer;
  buffPolygon;
  buffDis = 25; //设置缓冲距离
  editBuffPosition = {}; //缓冲弹框位置
  buffInfoWindow;
  activeBuffObj = {};

  buffStyle = {
    color: "#1976ED",
    fillColor: "#1976ED", //填充颜色
    opacity: 0.1,
    weight: 1,
    fillOpacity: 0.25, //填充的透明度，取值范围0 - 1
  };

  //选择框样式
  selectContentStyle = {
    width: "auto",
    height: "24px",
    border: "0px",
    padding: "5px 0 5px 2px",
    "text-align": "center",
    lineHeight: "24x",
    "font-size": "12px",
  };

  init(mapId, _mapConfig, _mapStyle, callBack) {
    this.mapId = mapId;
    this.mapContainer = document.getElementById(mapId);
    let mapConfig = _mapConfig;
    // 初始化图层
    // 初始化地图
    this.mapLayerChangeUtil = new MapPlatForm.Base.MapConfig();
    var resultJson = this.mapLayerChangeUtil.createMap(
      this.mapContainer,
      mapConfig
    );
    this.map = resultJson.map;
    const isPngMap  = mapConfig.vectorLayer.findIndex(el => el.layerOpt?.layerInfo?.type === "png");
    if(isPngMap === -1)  this.map.setMapStyle({
      styleJson: !!_mapStyle ? _mapStyle : [],
    });
    // this.map = new NPMapLib.Map(this.mapContainer, mapConfig.mapOpts);
    this.mapAdapter = new MapAdapter(this.map);
    this.skillsLayer = new NPMap.Layers.OverlayLayer("skills-layer");
    this.map.addLayer(this.skillsLayer);
    this.clusterMarkerFactory = new ClusterMarkerFactory(this.mapAdapter);
    this.infoWindowFactory = new InfoWindowFactory();
    this.traceAnalyzeFactory = new TraceAnalyzeFactory(this.mapAdapter);
    this.mapTools = new MapToolBar(
      new MapPlatForm.Base.MapTools(this.map),
      this.mapAdapter
    );
    // 地图服务
    this.mapService = new MapPlatForm.Base.MapService(this.map);
    // var ctrl = new NPMapLib.Controls.MousePositionControl();
    // this.mapAdapter.addControl(ctrl);
    this.mapGeometry = new MapPlatForm.Base.MapGeometry(this.map);
    // 加载地图展示用的图层
    // MapBaseLayerUtil.initMaplayer(this.map, mapConfig);
    // if (mapConfig.mapOpts && mapConfig.mapOpts.centerPoint && mapConfig.mapOpts.centerPoint[0] && mapConfig.mapOpts.centerPoint[1]) {
    //     this.mapAdapter.centerAndZoom(this.mapAdapter.getPoint(mapConfig.mapOpts.centerPoint[0], mapConfig.mapOpts.centerPoint[1]),
    //         mapConfig.vectorLayer &&
    //         mapConfig.vectorLayer[0] &&
    //         mapConfig.vectorLayer[0].layerOpt &&
    //         mapConfig.vectorLayer[0].layerOpt.layerInfo &&
    //         mapConfig.vectorLayer[0].layerOpt.layerInfo.defaultZoom);
    // }
    // 绑定地图右键方法, 点击右键时, 用于取消一些地图绘制操作
    // this.mapAdapter.addEventListener(NPMapLib.MAP_EVENT_RIGHT_CLICK, () => {
    //     this.cancelDraw();
    // });
    // this.unEnableMouseRightClick();
  }

  //设置鼠标跟随样式
  setMouseStyle(text, style = {}) {
    this.map.activateMouseContext(text);
    style.width = (text || "").length * 13 + "px";
    this.map.setMouseContextStyle(
      Object.assign({}, this.selectContentStyle, style)
    );
    this.map.addEventListener(
      NPMap.MAP_EVENT_RIGHT_CLICK,
      this.cancelDraw.bind(this)
    );
  }

  cancelDraw() {
    this.mapTools.cancelDraw();
    this.skillsLayer.removeAllOverlays();
    if (this.buffInfoWindow) {
      this.closeInfoWindow(this.buffInfoWindow);
      this.buffInfoWindow = null;
    }
  }

  /**
   * 获得select操作选中的点位
   * @param geometry
   * @returns {Array}
   */
  getSelectPoints(geometry) {
    // let points = this.clusterMarkerFactory.getPoints() || [];
    let i,
      len,
      result = [];
    let overlayLayer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    if (!overlayLayer) {
      return [];
    }
    //console.debug("getBounds()",geometry.getBounds(), geometry.getExtent());
    overlayLayer.containFeatures(geometry, (marker) => {
      let temp = ConvertModelUtil._convertClusterMarkerEx2MapPointModel(marker);
      if (temp != null) {
        result.push(temp);
      }
    });
    return result;
  }

  selectLine(callBackMethod, rightClickHandler, anleEdit = false, style) {
    this.mapTools.drawLine((extent, geometry) => {
      if (!geometry) {
        return;
      }
      this.map.deactivateMouseContext();
      this.addBuff(geometry, callBackMethod, extent);
      this.addBuffDisInfoWindow(geometry);
    }, style);
    //激活鼠标文字跟踪
    this.setMouseStyle("单击开始绘制，双击结束，右键取消绘制。", {
      color: "#fff",
      "background-color": "#2c3033e6",
    });
  }

  /**
   *
   * @param {*} callBackMethod
   * @param {*} style
   * @param anleEdit 用来控制是否可以编辑该区域
   * 这里增加了区域返回getPosition by：zml
   */
  selectPolygon(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index
  ) {
    this.mapTools.drawPolygon(
      (extent, geometry) => {
        if (callBackMethod) {
          callBackMethod(
            this.getSelectPoints(geometry),
            this.getPosition(geometry, "polygon"),
            extent,
            geometry
          );
        }
      },
      rightClickHandler,
      anleEdit,
      style,
      index
    );
  }

  selectRectangle(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index
  ) {
    this.mapTools.drawRectangle(
      (extent, geometry) => {
        if (callBackMethod) {
          callBackMethod(
            this.getSelectPoints(geometry),
            this.getPosition(geometry, "rectangle"),
            extent,
            geometry
          );
        }
      },
      rightClickHandler,
      anleEdit,
      style,
      index
    );
  }
  /**
   * index: 框选框位置
   */
  selectCircle(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index
  ) {
    this.mapTools.drawCircle(
      (extent, geometry) => {
        if (callBackMethod) {
          callBackMethod(
            this.getSelectPoints(geometry),
            this.getPosition(geometry, "circle"),
            extent,
            geometry
          );
        }
      },
      rightClickHandler,
      anleEdit,
      style,
      index
    );
  }
  selectDrawCircleByDiameter(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index
  ) {
    this.mapTools.drawCircleByDiameter(
      (extent, geometry) => {
        if (callBackMethod) {
          callBackMethod(
            this.getSelectPoints(geometry),
            this.getPosition(geometry, "circle"),
            extent,
            geometry
          );
        }
      },
      rightClickHandler,
      anleEdit,
      style,
      index
    );
  }
  /**
   * 根据不同的框处理框选后的位置
   */
  getPosition(geometry, type) {
    let position = {
      points: "",
      type: type,
    };
    switch (type) {
      case "rectangle":
      case "polygon":
        position.center = geometry.getCentroid();
        position.points = geometry._points;
        break;
      case "circle":
        position.center = geometry.getCenter();
        position.radius = geometry.getRadius();
        break;
      default:
        return "暂不支持此方法";
    }
    return position;
  }

  // 画出定位信息
  /**
   *
   * @param {*} position 该区域所在定位信息
   * @param {*} anleEdit 该区域是否可以编辑
   * @param {*} callBackMethod 回调函数
   */
  drawPolygon(position, anleEdit = false, callBackMethod) {
    let points = [];
    points = position.points.map((row) => {
      return new NPMapLib.Geometry.Point(row.lon, row.lat);
    });
    this.overlay = new NPMapLib.Geometry.Polygon(points, {
      color: "#2B84E2", //颜色
      fillColor: "rgb(10, 64, 122)", //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.34, //填充的透明度，取值范围0 - 1
    });

    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(
        NPMap.POLYGON_EVENT_DRAG_END,
        (geometry) => {
          if (callBackMethod) {
            callBackMethod(
              this.getSelectPoints(geometry),
              this.getPosition(geometry, "polygon")
            );
          }
        }
      );
    }
  }

  drawRectangle(position, anleEdit = false, callBackMethod) {
    let points = [];
    points = position.points.map((row) => {
      return new NPMapLib.Geometry.Point(row.lon, row.lat);
    });
    let overlayStyle = {
      color: "#3289fd", //颜色
      fillColor: "rgb(50, 137, 253)", //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.33, //填充的透明度，取值范围0 - 1
    };
    this.overlay = new NPMapLib.Geometry.Polygon(points, overlayStyle);
    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(
        NPMap.POLYGON_EVENT_DRAG_END,
        (geometry) => {
          if (callBackMethod) {
            callBackMethod(
              this.getSelectPoints(geometry),
              this.getPosition(geometry, "rectangle")
            );
          }
        }
      );
    }
  }

  drawCircle(position, anleEdit = false, callBackMethod) {
    let center = position.center;
    let radius = position.radius;
    let overlayStyle = {
      color: "blue", //颜色
      fillColor: "rgb(50, 137, 253)", //填充颜色
      weight: 2, //宽度，以像素为单位
      opacity: 1, //透明度，取值范围0 - 1
      fillOpacity: 0.5, //填充的透明度，取值范围0 - 1
    };
    this.overlay = new NPMapLib.Geometry.Circle(center, radius, overlayStyle);
    this.mapAdapter.addOverlay(this.overlay);
    if (anleEdit) {
      this.overlay.enableEditing(NPMap.ModifyFeature_RESIZE);
      this.overlay.addEventListener(
        NPMap.POLYGON_EVENT_DRAG_END,
        (geometry) => {
          if (callBackMethod) {
            callBackMethod(
              this.getSelectPoints(geometry),
              this.getPosition(geometry, "circle")
            );
          }
        }
      );
    }
  }

  //添加缓冲区
  addBuff(geometry, callBackMethod, extent) {
    this.activeBuffObj = { geometry, callBackMethod, extent };
    this.mapService.getGeometryBuffer(geometry, this.buffDis, (result) => {
      if (this.buffPolygon) {
        this.skillsLayer.removeOverlay(this.buffPolygon);
      }
      this.buffPolygon = new NPMap.Geometry.Polygon(result.rings);
      this.buffPolygon.setStyle(this.buffStyle);
      this.skillsLayer.addOverlay(this.buffPolygon);
      if (callBackMethod) {
        callBackMethod(
          this.getSelectPoints(this.buffPolygon),
          this.getPosition(this.buffPolygon, "polygon"),
          extent,
          geometry
        );
      }
    });
  }

  //修改缓冲区
  changeBuff(buffDis) {
    this.mapService.getGeometryBuffer(
      this.activeBuffObj.geometry,
      buffDis,
      (result) => {
        if (this.buffPolygon) {
          this.skillsLayer.removeOverlay(this.buffPolygon);
        }
        this.buffPolygon = new NPMap.Geometry.Polygon(result.rings);
        this.buffPolygon.setStyle(this.buffStyle);
        this.skillsLayer.addOverlay(this.buffPolygon);
        // debugger;
        if (this.activeBuffObj.callBackMethod) {
          this.activeBuffObj.callBackMethod(
            this.getSelectPoints(this.buffPolygon),
            this.getPosition(this.buffPolygon, "polygon"),
            this.activeBuffObj.extent,
            this.activeBuffObj.geometry
          );
        }
      }
    );
  }

  //设置缓冲区窗体，获取位置信息
  addBuffDisInfoWindow(geometry) {
    var point = geometry.getStartPoint();
    this.editBuffPosition = point;
    var data = {
      content: this.createBuffComponent().$el,
      size: {
        width: 82,
        height: 30,
      },
      offset: new NPMapLib.Geometry.Size(-40, -40),
      isAdaptation: true,
      iscommon: true,
    };
    if (this.buffInfoWindow) {
      this.closeInfoWindow(this.buffInfoWindow);
      this.buffInfoWindow = null;
    }
    this.buffInfoWindow = this.createInfoWindow(point.lon, point.lat, data);
    this.openInfoWindow(this.buffInfoWindow, data.content, {});
  }

  createBuffComponent() {
    const self = this;
    const buffComponent = new Vue({
      render: (h) =>
        h(setBuffArea, {
          props: {
            editBuffPosition: this.editBuffPosition,
          },
          on: {
            changeBuffDis: (buffDis) => {
              self.changeBuff(buffDis);
            },
          },
        }),
    });
    const element = document.createElement("div");
    buffComponent.$mount(element);
    return buffComponent;
  }

  // 清除定位信息
  removeRectangle() {
    if (this.overlay) {
      this.overlay.disableEditing();
    }
    this.mapAdapter.removeOverlay(this.overlay);
  }

  showHeatMap() {
    if (this.heatMapLayer) {
      this.heatMapLayer.show();
      return;
    }
  }

  hideHeatMap() {
    if (this.heatMapLayer) {
      this.heatMapLayer.hide();
      return;
    }
  }

  updateHeatMapLayer(heatMapLayer) {
    this.heatMapLayer = heatMapLayer;
    this.heatMapLayer.show();
  }

  removeHeatMap() {
    if (this.heatMapLayer) {
      let dataset = {
        max: 0,
        data: [],
      };
      this.heatMapLayer.setDataset(dataset);
      this.map.removeLayerByName(this.heatMapLayer.getName());
      this.heatMapLayer = null;
    }
  }

  /**
   *  渲染显示热力图
   * @time: 2017-11-23 10:39:27
   * @params:
   * @return:
   */
  renderHeatMap(dataList, radius) {
    // 只存在一个 热力图
    let _heatMapName = "heatLayer";
    let opt = {
      //   isBaseLayer: false,
      opacity: 1.0,
      projection: "EPSG:900913",
      visible: true,
      radius: radius,
      name: _heatMapName,
    };
    let heatlayer;
    if (this.heatMapLayer) {
      heatlayer = this.heatMapLayer;
    } else {
      heatlayer = new NPMapLib.Layers.HeatMapLayer(_heatMapName, opt);
    }

    // max 数据的 获取
    let countMax = 0;
    let countMin = 0;
    let currentCount = 0;
    let _dataList = dataList.map((val, index) => {
      currentCount = val.count || 0;
      countMax = Math.max(countMax, currentCount);
      countMin = Math.min(countMin, currentCount);
      return {
        lat: val.lat,
        lon: val.lon,
        count: currentCount,
      };
    });
    let dataset = {
      max: countMax,
      min: countMin,
      data: _dataList,
    };
    if (!this.heatMapLayer) {
      this.map.addLayers([heatlayer]);
    }
    heatlayer.setDataset(dataset);
    this.updateHeatMapLayer(heatlayer);
  }

  /**
   * 清理地图上的一些无用遮罩层
   * 由于在界面上起了一些悬浮框等，因为各种原因, 事件没有监听好, 导致悬浮框没有正常清掉
   * 故加上此方法，在一些容易引起问题的操作上调用，确保地图界面干净
   * @private
   */
  _cleanMap() {
    this.hideMouseOverDom();
  }

  hideMouseOverDom() {
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
  }

  showMouseOverDom(marker) {
    // 只在图上作战位置显示正确
    // 在地图上展示一个悬浮框, 用于显示当前点位的文字信息
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
    let name = marker.title || marker.ext.deviceName,
      point = marker.getPosition(),
      pixel = this.mapAdapter.pointToPixel(point),
      _container,
      _offset,
      _body,
      _scrollTop,
      _scrollLeft,
      mouseLeft,
      mouseTop,
      mouseOverDom,
      _left,
      _top;

    if (pixel == null) return;
    _container = document.getElementById(this.mapId);
    _body = document.body;
    _scrollTop = _body.scrollTop;
    _scrollLeft = _body.scrollLeft;
    _left = pixel.x + _container.getBoundingClientRect().left;
    _top = pixel.y + _container.getBoundingClientRect().top;
    // 这里已经算出正确的坐标点位了, 然后还需要根据长度高度进行微调, 让底部箭头刚好指在点位上方
    mouseOverDom = document.createElement("div");
    mouseOverDom.classList.add("u-map-hover");
    mouseOverDom.style.opacity = 0;
    mouseOverDom.innerHTML =
      "<div class='hover-container'>" +
      "<div class='hover-content'>" +
      name +
      "</div>" +
      "</div>" +
      "<div class='hover-bottom'></div>";
    _body.appendChild(mouseOverDom);
    // _body = null;
    let hoverDom = document.querySelector(".u-map-hover");
    hoverDom.style.width = "300px";
    let width = mouseOverDom.offsetWidth;
    let height = mouseOverDom.offsetHeight;
    // 减去本身dom偏移和点位图标的偏移
    _top -= height + 19;
    _left -= Math.floor(width / 2) + 8;
    mouseOverDom.style.position = "absolute";
    mouseOverDom.style.top = _top + "px";
    mouseOverDom.style.left = _left + "px";
    mouseOverDom.style.opacity = 1;
    this.mouseOverDom = mouseOverDom;
  }

  /**
   * 加载坐标点位到地图中但不显示
   * 注意: 此方法每个地图实例应当只调用一次
   * @param points
   */
  addMarkers(points, opts) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME);
    }
    // 地图图层加载时 需要经纬度为数字类型 否则如果为字符串类型则会报错  X *******错误
    let arr = points.map((row) => {
      row.Lon = Number(row.Lon);
      row.Lat = Number(row.Lat);
      return row;
    });
    this.clusterMarkerFactory.setPoints(arr);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(arr);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME,
      true,
      OverlayFactory.getClusterOverlayOpt(opts)
    );
    this.map.addLayer(overlayLayer);
    overlayLayer.hide();
    overlayLayer.addOverlay(clusterPoints);
  }

  /**
   * 加载坐标点位到地图中并且显示
   * 注意: 此方法每个地图实例应当只调用一次
   * @param points
   */
  renderMarkers(points, opts, notCluster = false) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME);
    }
    let newPoints = notCluster ? points : this.formatClusterPoints(points) ;
    this.clusterMarkerFactory.setPoints(newPoints);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(newPoints);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME,
      true,
      OverlayFactory.getClusterOverlayOpt(opts)
    );
    // TODO:这里取消聚合图层的显示临时解决，找不到聚合图层不加载的函数，以后如果找到可以删除isCluster判断
    // notCluster && (overlayLayer.maxZoom = 10);
    // this.map.addLayer(overlayLayer);
    // TODO:现版本不需要聚合数据 resolve: zml
    this.map.addLayer(overlayLayer);
    // let size = new NPMapLib.Geometry.Size(20, 20);
    // let icon = new NPMapLib.Symbols.Icon(require('@/assets/img/map/map-camera-normal.png?v=1'), size);
    // points.forEach(row => {
    //         let pt = new NPMapLib.Geometry.Point(row.Lon, row.Lat);
    //         let marker = new NPMapLib.Symbols.Marker(pt);
    //         marker.setIcon(icon);
    //         marker.setTitle(row.Name);
    //         overlayLayer.addOverlay(marker);
    // marker.addEventListener('mouseover', () => {
    //     this.showMouseOverDom(marker);
    // })
    // marker.addEventListener('mouseout', () => {
    //     this.hideMouseOverDom();
    // })
    // })
    overlayLayer.addOverlay(clusterPoints);
    overlayLayer.setZIndex(500);
  }
  renderAddMarkers(points, opts, notCluster) {
    // TODO 这里只是为了模拟不同点位类型用, 等摄像机点位真正有其他类型时使用的时候需要注释掉下面的代码 resolve: wyr
    // points = this.mockPoints(points);
    //清除图层
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME1);
    if (layer != null) {
      this.map.removeLayerByName(CLUSTER_LAYER_NAME1);
    }
    this.clusterMarkerFactory.setPoints(points);
    let clusterPoints = this.clusterMarkerFactory.getClusterPoints(points);
    // 渲染到地图中
    opts = opts || {};
    this.cookOverlayLayerOptsEx(opts);
    // TODO 由于distance和maxZoom是固定值, 所以只能每种objectType定义一个overlayLayer了
    let overlayLayer = new NPMapLib.Layers.OverlayLayer(
      CLUSTER_LAYER_NAME1,
      true,
      OverlayFactory.getClusterOverlayOpt(opts)
    );
    // TODO:这里取消聚合图层的显示临时解决，找不到聚合图层不加载的函数，以后如果找到可以删除isCluster判断
    // notCluster && (overlayLayer.maxZoom = 10);
    this.map.addLayer(overlayLayer);
    //console.log(overlayLayer, 'overlayLayer')
    // TODO:现版本不需要聚合数据 resolve: zml
    // let size = new NPMapLib.Geometry.Size(20, 20);
    // let icon = new NPMapLib.Symbols.Icon(require('@/assets/img/map/map-camera-normal.png?v=1'), size);
    // points.forEach(row => {
    //         let pt = new NPMapLib.Geometry.Point(row.Lon, row.Lat);
    //         let marker = new NPMapLib.Symbols.Marker(pt);
    //         marker.setIcon(icon);
    //         marker.setTitle(row.Name);
    //         overlayLayer.addOverlay(marker);
    //         marker.addEventListener('click', (e) => {
    //             // this.showMouseOverDom(marker);
    //             console.log(5552000,e)
    //         })
    //         marker.addEventListener('mouseout', () => {
    //             this.hideMouseOverDom();
    //         })
    //         marker.addEventListener('mouseout', () => {
    //             this.hideMouseOverDom();
    //         })
    //     })
    // console.log('clusterPoints',clusterPoints)
    overlayLayer.addOverlay(clusterPoints);
    overlayLayer.setZIndex(500);
  }

  formatClusterPoints(points) {
    var valueCounter = {};
    var noPoleGroupData = [];
    var poleGroupData = [];

    points
      .filter(
        (item) => item.Lon && item.Lon !== "0" && item.Lon && item.Lon !== "0"
      )
      .forEach((item) => {
        let Lat = item.Lat; //.substring(0, 7)
        let Lon = item.Lon; //.substring(0, 8)
        valueCounter[`${Lon}-${Lat}`] =
          (valueCounter[`${Lon}-${Lat}`] || 0) + 1;
      });
    points
      .filter(
        (item) => item.Lon && item.Lon !== "0" && item.Lon && item.Lon !== "0"
      )
      .forEach((item) => {
        let Lat = item.Lat; //.substring(0, 7)
        let Lon = item.Lon; //.substring(0, 8)
        if (valueCounter[`${Lon}-${Lat}`] >= 2) {
          poleGroupData.push(item);
        } else {
          noPoleGroupData.push(item);
        }
      });

    this.poleGroupPoints = poleGroupData.map((v) => {
      v.isPoleGroupPoint = true;
      return v;
    });

    return this.poleGroupPoints.concat(noPoleGroupData);
  }

  convertSystemPoint2MapPoint(data, getNameFunc) {
    return ConvertModelUtil.convertSystemPoint2MapPoint(data, getNameFunc);
  }

  convertSystemPointArr2MapPoint(datas, getNameFunc) {
    return ConvertModelUtil.convertSystemPointArr2MapPoint(datas, getNameFunc);
  }
  //资源图层数据
  convertSystemPointArr3MapPoint(datas, getNameFunc) {
    return ConvertModelUtil.convertSystemPointArr3MapPoint(datas, getNameFunc);
  }
  // 未成年人场所图层数据
  convertSystemPoint3MapArrPointJuvenile(datas, getNameFunc) {
    return ConvertModelUtil.convertSystemPoint3MapArrPointJuvenile(
      datas,
      getNameFunc
    );
  }
  // 未成年人报警图层数据
  convertSystemPoint3MapArrPointJuvenileAlarm(datas, getNameFunc) {
    return ConvertModelUtil.convertSystemPoint3MapArrPointJuvenile(
      datas,
      getNameFunc
    );
  }
  /**
   * 创建一个窗口, 返回窗口的唯一编码
   * @param lon
   * @param lat
   * @param eventOpt
   * @returns {uuid: string}
   */
  createInfoWindow(lon, lat, infoWindowOpt) {
    let infoWindow = InfoWindowFactory.getInfoWindow(
      new NPMapLib.Geometry.Point(lon, lat),
      null,
      null,
      InfoWindowFactory.getInfoWindowOpts(infoWindowOpt)
    );
    let uuid = this.infoWindowFactory.addInfoWindow(infoWindow);
    return uuid;
  }

  closeInfoWindow(winId) {
    this.mapAdapter.closeInfoWindow(
      this.infoWindowFactory.removeInfoWindow(winId)
    );
  }

  openInfoWindow(winId, domHtml, eventOpt) {
    let win = this.infoWindowFactory.getById(winId);
    this.infoWindowFactory.addEventListener(win, winId, eventOpt);
    this.mapAdapter.addOverlay(win);
    win.setContentDom(domHtml);
    win.open();
  }

  /**
   * 此方法用于对前端传来的聚合点位配置进行一点加工
   * @param opts
   */
  cookOverlayLayerOptsEx(opts) {
    // 截取mouseover, 设置默认的地图mouseover方法, 并在默认的方法后调用业务层绑定的方法
    let mouseoverFunc = opts.mouseover;
    let mouseoutFunc = opts.mouseout;
    let _click = opts.click;
    opts.mouseover = (marker) => {
      // this.showMouseOverDom(marker);
      if (typeof mouseoverFunc === "function") {
        mouseoverFunc(marker);
      }
    };
    opts.mouseout = (marker) => {
      // this.hideMouseOverDom();
      if (typeof mouseoutFunc === "function") {
        mouseoutFunc(marker);
      }
    };
    opts.click = (marker) => {
      this.hideMouseOverDom();
      if (typeof _click === "function") {
        _click(marker);
      }
    };
  }

  /**
   *
   * @param {地图geoJSON} geoRegions
   * @param {点击区域回调} callBack
   */
  addGeoRegions(geoRegions, callBack) {
    if (!!geoRegions && geoRegions.length > 0) {
      this.mapGeoRegion = new MapPlatForm.Base.MapGeoRegion(this.map, {
        mouseOver: (region) => {
          region.oldFillColor = region.strokeColor;
          region.setStyle({
            // fillColor: "rgba(255,0,0,1)"
            strokeColor: "#4394ff",
          });
          this.mapGeoRegion.refresh();
        },
        mouseOut: (region) => {
          region.resetStyle();
          this.mapGeoRegion.refresh();
        },
        click: (region) => {
          if (callBack && typeof callBack === "function") {
            callBack(region.name);
          }
        },
        fontAvoid: false,
        minZoom: 5,
        maxZoom: 17,
      });
      this.mapGeoRegion.addGeoRegions(geoRegions);
    }
  }
  /**
   *
   * @param {*} fun 监听地图缩放更改回调方法
   */
  listenerZoomChange(fun) {
    this.map.addEventListener(NPMap.MAP_EVENT_ZOOMCHANGE, fun);
  }

  removeListenerZoomChange() {
    this.map.removeEventListener(NPMap.MAP_EVENT_ZOOMCHANGE);
  }
  /**
   *
   * @param {*} fun 监听地图拖拽结束回调
   */
  listenerDragEnd(fun) {
    this.map.addEventListener(NPMap.MAP_EVENT_DRAG_END, fun);
  }

  removeListenerDragEnd() {
    this.map.removeEventListener(NPMap.MAP_EVENT_DRAG_END);
  }

  /**
   *
   * @returns 获取可视区域经纬度
   */
  getExtent() {
    return this.map.getExtent();
  }

  setCenter(val) {
    this.map.setCenter(val);
  }
  /**
   * 移除单个点位
   * @param point
   */
  removeMarker1(objectId) {
    if (!objectId) return;
    this.clusterMarkerFactory.removePointByParams("ObjectID", objectId);
    this.refreshMarker1();
  }

  refreshMarker1() {
    // 由于gis地图有bug, 不能动态增加删除聚合点位, 所以在布点的时候 需要移除聚合对象再进行聚合的显示
    let layer = this.map.getLayerByName(CLUSTER_LAYER_NAME1);
    if (layer != null) {
      layer.removeAllOverlays();
      layer.addOverlay(
        this.clusterMarkerFactory.getClusterPoints(
          this.clusterMarkerFactory.getPoints()
        )
      );
    }
  }

  clearDraw() {
    this.cancelDraw();
    let overlays = this.mapTools.getSelectGeometrys();
    if (!overlays) return;
    let i, len;
    for (i = 0, len = overlays.length; i < len; i++) {
      overlays[i].disableEditing();
      this.mapAdapter.removeOverlay(overlays[i]);
    }
    this.mapTools.resetSelectGeometrys();
  }
  clearModelDraw(index = -1) {
    // this.cancelDraw();
    let overlays = this.mapTools.getOnlyGeometrys();
    debugger;
    if (index < 0) {
      if (!overlays) return;
      overlays.forEach((item, ind) => {
        if (item) {
          overlays[ind].disableEditing();
          this.mapAdapter.removeOverlay(overlays[ind]);
          this.mapTools.resetSelectAppoint(ind);
        }
      });
    } else {
      if (!overlays || !overlays[index]) return;
      overlays[index].disableEditing();
      this.mapAdapter.removeOverlay(overlays[index]);
      this.mapTools.resetSelectAppoint(index);
    }
  }
  // 隐藏画框
  hideModelDraw(index) {
    let overlays = this.mapTools.getOnlyGeometrys();
    console.log(overlays, "overlays");
    if (!overlays || !overlays[index]) return;
    overlays[index].hide();
    // this.mapAdapter.removeOverlay(overlays[index]);
    // this.mapTools.resetSelectAppoint(index);
  }
  // 显示画框
  showModelDraw(index) {
    let overlays = this.mapTools.getOnlyGeometrys();
    if (!overlays || !overlays[index]) return;
    overlays[index].show();
    // this.mapAdapter.removeOverlay(overlays[index]);
    // this.mapTools.resetSelectAppoint(index);
  }
  // 删除无数据画框
  removeNullModelDraw(index) {
    let overlays = this.mapTools.getOnlyGeometrys();
    if (!overlays || !overlays[index]) return;
    let points = this.getSelectPoints(overlays[index]);
    if (points.length == 0) {
      overlays[index].disableEditing();
      this.mapAdapter.removeOverlay(overlays[index]);
      this.mapTools.resetSelectAppoint(index);
    }
  }
  /**
   * 清理地图上的一些无用遮罩层
   * 由于在界面上起了一些悬浮框等，因为各种原因, 事件没有监听好, 导致悬浮框没有正常清掉
   * 故加上此方法，在一些容易引起问题的操作上调用，确保地图界面干净
   * @private
   */
  _cleanMap() {
    this.hideMouseOverDom();
  }

  hideMouseOverDom() {
    if (this.mouseOverDom) {
      this.mouseOverDom.remove();
      this.mouseOverDom = null;
    }
  }

  removeHeatMap() {
    if (this.heatMapLayer) {
      let dataset = {
        max: 0,
        data: [],
      };
      this.heatMapLayer.setDataset(dataset);
      this.map.removeLayerByName(this.heatMapLayer.getName());
      this.heatMapLayer = null;
    }
  }

  removeCircleContainTexts() {
    if (this.circleContainTexts && this.circleContainTexts.length > 0) {
      this.map.removeOverlays(this.circleContainTexts);
      this.circleContainTexts = null;
    }
  }

  destoryGeoRegion() {
    !!this.mapGeoRegion && this.mapGeoRegion.destory();
  }

  /**
   * 销毁地图
   */
  destroy() {
    if (this.mapAdapter != null) {
      this.removeCircleContainTexts();
      this.removeHeatMap();
      this._cleanMap();
      this.destoryGeoRegion();
      this.mapAdapter.destroyMap();
      this.map = null;
      this.mapLayerChangeUtil = null;
      this.mapContainer.oncontextmenu = null;
      this.mapContainer = null;
      this.mapAdapter = null;
      this.clusterMarkerFactory = null;
      this.infoWindowFactory = null;
      this.locateMouseEventUtil = null;
      this.mapTools = null;
      if (this.traceAnalyzeFactory) {
        this.traceAnalyzeFactory.destroy();
      }
      this.traceAnalyzeFactory = null;
      this.mouseOverDom = null;
    }
  }

  // unEnableMouseRightClick() {
  //     function handler(event) {
  //         event = event || window.event;

  //         if (event.stopPropagation)
  //             event.stopPropagation();

  //         event.cancelBubble = true;
  //         return false;
  //     }

  //     this.mapContainer.oncontextmenu = function() {
  //         return false;
  //     }
  // }

  /**
   *回到可视区域
   */
  zoomToExtend(extent) {
    this.map.zoomToExtent(extent);
  }
  /**
   *测量距离
   */
  measuringDistance() {
    this.mapTools.measureDistance();
  }
  cancelMeasure() {
    // this.mapTools.cancel()
    this.mapTools.cancelMeasure();
  }
  /**
   * 缩放区域
   * @param {Array} points [坐标集合]
   */
  zoomToPoints(points) {
    let maxLon, maxLat, minLon, minLat, extent;
    points.forEach((pt) => {
      if (pt.longitude > maxLon || !maxLon) {
        maxLon = pt.longitude;
      }
      if (pt.longitude < minLon || !minLon) {
        minLon = pt.longitude;
      }
      if (pt.latitude > maxLat || !maxLat) {
        maxLat = pt.latitude;
      }
      if (pt.latitude < minLat || !minLat) {
        minLat = pt.latitude;
      }
    });
    if (minLon && minLat && maxLon && maxLat) {
      extent = new NPMapLib.Geometry.Extent(minLon, minLat, maxLon, maxLat);
      this.map.zoomToExtent(extent);
    }
  }
}
