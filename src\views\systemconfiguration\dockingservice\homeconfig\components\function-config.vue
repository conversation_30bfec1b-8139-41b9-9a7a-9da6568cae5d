<template>
  <div class="function-config-container">
    <div class="form">
      <div class="title">
        <span class="main-title">首页功能配置</span>
        <!-- <span class="subtitle">CONFIGURATION</span> -->
      </div>
      <p class="mb-lg font-p">
        <span class="font-color f-14">1、<span class="font-requried">请选择数据统计对象：</span></span>
        <api-area-tree
          class="tree-select"
          :class="{ 'tree-select-error': validateValue.regionCode && !formData.regionCode }"
          :disabled="!isEdit"
          :select-tree="formData"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </p>
      <p class="mb-lg font-p">
        <span class="font-color f-14">2、<span class="font-requried">请选择考核任务：</span></span>
        <Select
          v-model="formData.assessTask"
          :disabled="!isEdit"
          clearable
          placeholder="请选择考核任务"
          class="width-lg"
          :class="{ 'select-error-2': validateValue.assessTask && !formData.assessTask }"
        >
          <Option v-for="(item, index) in taskPageList" :value="item.id + ''" :key="index"> {{ item.taskName }}</Option>
        </Select>
      </p>
      <p class="mb-lg font-p">
        <span class="font-color f-14">3、<span class="font-requried">请选择检测任务：</span></span>
        <Select
          v-model="formData.taskSchemeId"
          :disabled="!isEdit"
          clearable
          placeholder="请选择检测任务"
          class="width-lg"
        >
          <Option v-for="(item, index) in listTaskSchemes" :key="index" :label="item.taskName" :value="item.id">{{
            item.taskName
          }}</Option>
        </Select>
      </p>
    </div>
  </div>
</template>

<script>
import home from '@/config/api/home';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'home-config',
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree').default,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {
        regionName: '',
        regionCode: '',
        assessTask: '',
        taskSchemeId: '',
      },
      taskPageList: [], //考核任务
      listTaskSchemes: [], //检测任务
      validateValue: {},
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {
    this.viewByParamKey();
    this.getTaskPageList();
    this.getListTaskSchemes();
  },
  methods: {
    selectedArea({ regionCode, regionName }) {
      this.formData.regionCode = regionCode;
      this.formData.regionName = regionName;
    },
    validateForm() {
      Object.keys(this.formData).map((key) => {
        this.$set(this.validateValue, key, false);
      });
      if (!this.formData.regionCode) {
        this.$Message.error('统计对象不能为空');
        this.validateValue.regionCode = true;
        return false;
      }
      if (!this.formData.assessTask) {
        this.$Message.error('考核任务不能为空');
        this.validateValue.assessTask = true;
        return false;
      }
      return true;
    },
    // 保存按钮触发
    async updateByParamKey() {
      try {
        if (!this.validateForm()) return;
        let params = {
          paramKey: 'HOME_PAGE_CONFIG',
          paramValue: JSON.stringify(this.formData),
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params);
        await this.$http.post(home.refreshIndexDeviceByConfig, params);
        this.$Message.success('保存成功');
        this.$emit('changeBtnList', 'save');
      } catch (e) {
        console.log(e);
      }
    },
    async viewByParamKey() {
      try {
        let params = {
          key: 'HOME_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this.$http.get(home.viewByParamKey, { params });
        this.formData = JSON.parse(data.paramValue);
      } catch (e) {
        console.log(e);
      }
    },
    async getTaskPageList() {
      let params = {
        pageNumber: 1,
        pageSize: 10000,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskPageList, params);
        this.taskPageList = data.entities || [];
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .function-config-container {
    .form {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-dark.png') no-repeat;
      }
    }
  }
}
[data-theme='light'] {
  .function-config-container {
    .form {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-light.png') no-repeat;
      }
    }
  }
}
[data-theme='deepBlue'] {
  .function-config-container {
    .form {
      .title {
        background: url('~@/assets/img/home/<USER>/home-config-title-deepBlue.png') no-repeat;
      }
    }
  }
}
.function-config-container {
  position: relative;
  .form {
    position: absolute;
    left: 50%;
    top: 40%;
    transform: translate(-50%, -50%);
    .title {
      position: relative;
      height: 60px;
      width: 427px;
      background-size: cover;
      z-index: 1;
      margin-bottom: 20px;
      .main-title {
        display: inline-block;
        font-size: 26px;
        font-weight: bold;
        line-height: 27px;
        color: #ffffff;
        text-shadow: 0 0 10px #60a3f9;
        padding: 25px 0 6px 109px;
        z-index: 2;
      }
      .subtitle {
        display: inline-block;
        font-size: 26px;
        font-weight: bold;
        line-height: 20px;
        color: #04102a;
        position: absolute;
        left: 27%;
        top: 57%;
        z-index: -2;
      }
    }
    .font-p {
      display: flex;
      justify-content: space-between;
      margin-right: 15px;
      align-items: center;
      .font-color {
        color: var(--color-input);
      }
    }
    .font-requried::before {
      content: '*';
      color: #ed4014;
      margin-right: 2px;
    }
  }
}
.tree-select {
  @{_deep} .el-tree {
    max-height: 350px;
  }
  @{_deep} .select-width {
    width: 230px;
  }
}
.tree-select-error /deep/ .ivu-select-selection,
.select-error-1 /deep/ .ivu-select-selection,
.select-error-2 /deep/ .ivu-select-selection {
  border-color: #ed4014;
}
</style>
