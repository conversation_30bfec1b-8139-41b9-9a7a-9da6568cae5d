<template>
  <map-pgis
    ref="deviceMap"
    class="map-base"
    :camera-list="cameraList"
    :map-position="mapPosition"
    :choose-map-item="chooseMapItem"
    :need-query-device-info="true"
    :right-button-group="true"
    :geo-regions="geoRegions"
    :center="center"
    map-dom-component-name="EditFormBubble"
    :mapDomOption="{ width: 300 }"
    @queryDeviceInfo="queryDeviceInfo"
    @handleMapClick="handleMapClick"
    @handleUpdatePointInfo="handleUpdatePointInfo"
  ></map-pgis>
</template>

<script>
export default {
  name: 'pgis-map',
  props: {
    cameraList: {
      type: Array,
      default: () => [],
    },
    bubbleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      chooseMapItem: {},
      mapPosition: {
        center: {},
      },
      geoRegions: [],
      center: {},
      allCameraList: [],
    };
  },
  methods: {
    async handleMapClick({ ev, locationActive, chooseMapItem, locationLayerShow }) {
      if (locationActive) {
        if (!locationLayerShow) {
          const uIcon = new Icon();
          uIcon.image = require('@/assets/img/device-map/location.png');
          uIcon.height = 32;
          uIcon.width = 32;
          const point = new Point(ev.mapPoint.x, ev.mapPoint.y);
          const marker = new Marker(point, uIcon, null, 'location');
          this.$refs.deviceMap.addMarker(marker);
        }
        this.$refs.deviceMap.featureSelected((point) => {
          this.chooseMapItem = Object.assign(chooseMapItem, {
            longitude: point.x,
            latitude: point.y,
          });
        });
      }
    },
    queryDeviceInfo() {
      this.$emit('handleQueryDeviceInfo');
    },
  },
  watch: {
    bubbleData: {
      handler(val) {
        if (!this.bubbleData.latitude || !this.bubbleData.longitude) {
          this.$Message.error('经纬度信息不全！');
          return false;
        }
        this.chooseMapItem = this.$util.common.deepCopy(val);
      },
    },
  },
  components: {
    MapPgis: require('@/components/map-pgis/index.vue').default,
  },
};
</script>

<style scoped></style>
