/**
 *    name: string,  // 分析内容
 *    component: string, // 组件名
 *    detailsComponent: string, // 设备详情组件
 *    factorCode: [],  // 具体的分析内容项的factorCode值
 */
const menuConfig = [
  // 人脸|车辆 卡口活跃分析
  {
    name: '活跃分析',
    component: 'ActiveAnalysis',
    detailsComponent: 'ActiveDetails',
    factorCode: ['FACE_ACTIVE_ANALYSE', 'VEHICLE_ACTIVE_ANALYSE'],
  },
  // 人脸|车辆 卡口离线分析
  {
    name: '离线分析',
    component: 'OfflineAnalysis',
    detailsComponent: 'OfflineDetails',
    factorCode: ['FACE_OFFLINE_ANALYSE', 'VEHICLE_OFFLINE_ANALYSE'],
  },
  // 人脸|车辆 图像质量分析
  {
    name: '图像质量分析',
    component: 'QualityAnalysis',
    detailsComponent: '',
    factorCode: ['FACE_GRAPH_QUALITY_ANALYSE', 'VEHICLE_GRAPH_QUALITY_ANALYSE'],
  },
  // 视频流
  {
    name: '实时视频流分析',
    component: '',
    detailsComponent: '',
    factorCode: ['VIDEO_PLAYING_ANALYSE'],
  },
  {
    name: '历史视频流分析',
    component: '',
    detailsComponent: '',
    factorCode: ['VIDEO_HISTORY_ANALYSE'],
  },
  // 其他
  {
    name: '设备时钟分析',
    component: '',
    detailsComponent: '',
    factorCode: ['OTHER_CLOCK_ANALYSE'],
  },
];

const btnList = [
  { name: '编辑', key: 'edit' },
  { name: '分析', key: 'analyze' },
  { name: '删除', key: 'del' },
  // { name: '记录', key: 'records' },
];
const dropdownBtnList = (storageStatus) => {
  let arr = [];
  switch (storageStatus) {
    // 草稿任务
    case 0:
      arr = [
        { name: '编辑', key: 'edit' },
        { name: '删除', key: 'del' },
      ];
      break;
    // 已暂停的任务
    case 1:
      arr = [...btnList, { name: '启动', key: 'pause' }];
      break;
    // 已启动的任务
    case 2:
      arr = [...btnList, { name: '暂停', key: 'start' }];
      break;

    default:
      break;
  }
  return arr;
};

export { menuConfig, dropdownBtnList };
