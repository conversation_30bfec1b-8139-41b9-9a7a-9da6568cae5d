/*
 * @Author: du<PERSON>en
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: zhengmingming zhengmingming
 * @LastEditTime: 2024-08-29 15:54:11
 */
import { mapGetters } from "vuex";
/**
 * 获取关系图谱数据
 * */
export default {
  props: {},
  data() {
    return {
      MixinRoot: {},
      // 只有组的信息
      MixinReativeStat: [
        {
          children: [],
        },
      ],
      MixinEntityGroups: [],
      hasGraphData: true,
    };
  },
  created() {},
  methods: {
    /**
     * @description: 图片加载完成
     * @param {object} data 关系图片数据
     */
    graphLoaded(data) {
      this.hasGraphData =
        data && (data.entityGroups || data.entitys || data.relations);
    },
    // 关系统计 - 关系图谱跳转
    MixinToRelationGraph() {
      let Item = this.filterSider.find((item) => {
        return item.name.includes("map");
      });
      let { archiveNo, source, idcardNo } = this.$route.query;
      this.$router.push({
        name: Item.name,
        query: {
          archiveNo: archiveNo,
          idcardNo: idcardNo,
          source: source,
        },
      });
    },
    // 处理relationMap请求接口
    handleDataByRelationMap(infos) {
      let { data, root } = this.handleParams(infos);
      let atlasList = {};
      atlasList.rootId = root.id;
      // 节点
      atlasList.nodes = [...data.entityGroups, ...data.entitys];
      // 关系
      atlasList.links = data.relations.map((item) => {
        let times = "";
        if ("properties" in item && !!item.properties) {
          if ("times" in item.properties) {
            times = `${item.properties.times}次`;
          }
        }
        return {
          from: item.sourceId,
          to: item.targetId,
          text: times,
          fontColor: "#2C86F8",
        };
      });
      this.atlasList = atlasList;
      this.MixinRoot = root;
      this.MixinReativeStat[0].id = root.id;
      this.MixinReativeStat[0].img = root.propertyIcon || root.icon;
      // 重置关系信息右侧 卡片信息
      this.MixinEntityGroups = data; // data.entityGroups
      this.MixinReativeStat[0].children = data.entityGroups.map((item) => {
        item.text = item.labelCn;
        return {
          id: item.id,
          num: item.count,
          text: item.labelCn,
        };
      });
    },
    // 处理一些兼容情况
    handleParams(data) {
      // 后端返回null的情况
      !data.entitys ? (data.entitys = []) : null;
      !data.entityGroups ? (data.entityGroups = []) : null;
      !data.relations ? (data.relations = []) : null;
      // 找到root节点 - 设置
      let root = data.entitys.find((item) => {
        return !!item.isCenter;
      });
      !root
        ? (root = {
            id: this.baseInfo.archiveNo,
            displayField: this.baseInfo.archiveNo,
            ...this.baseInfo,
          })
        : null;
      // 图片字段名字都不一样，哪个有取哪个
      const imageStragety = {
        photo: () => {
          root.propertyIcon = this.baseInfo.photo;
        },
        photos: () => {
          if (Array.isArray(this.baseInfo.photos)) {
            root.propertyIcon = this.baseInfo.photos[0].photoUrl;
          }
        },
        photoUrl: () => {
          root.propertyIcon = this.baseInfo.photoUrl;
        },
      };
      if (!("propertyIcon" in root) || !root.propertyIcon) {
        for (let key in imageStragety) {
          if (key in this.baseInfo && !!this.baseInfo[key]) {
            imageStragety[key]();
            break;
          }
        }
      }
      if (!data.entitys.length) data.entitys.push(root);
      return {
        data,
        root,
      };
    },
    MixinToNumCube() {
      const { entityId, metadata } = this.MixinRoot;
      let query = {
        ids: JSON.stringify(entityId),
        entityIdInstanceIdMap: JSON.stringify({
          [entityId]: metadata.qsdi_graph_instance_id,
        }),
        maxDepth: 1,
        type: "add",
        isRelation: true,
        source: this.$route.query.source,
      };
    //   this.$router.push({ name: "number-cube-info", query: query });
      this.$util.common.openNewWindow({
        name: "number-cube-info", query: query
      });
    },
  },
  watch: {},
  computed: {
    ...mapGetters("admin/menu", ["filterSider"]),
  },
};
