<template>
  <div class="governan-cecofig-box auto-fill">
    <div class="search-module">
      <ui-label label="治理项名称" class="inline">
        <Input
          class="width-lg"
          v-model="searchData.label"
          clearable
          placeholder="请输入治理项名称"
          @keyup.enter.native="search"
        >
        </Input>
      </ui-label>
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetSearch">重置</Button>
      </div>
    </div>
    <div class="table-box auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #status="{ row, index }">
          <i-switch class="iswitch" :value="row.status" @on-change="onChange($event, index)" />
        </template>
        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-chakanxiangqing"
            class="mr-md"
            :class="{ 'btn-disabled': !row.status }"
            content="查看"
            @click.native="openPage(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <!-- <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    ></ui-page> -->
  </div>
</template>

<script>
import home from '@/config/api/home';
import { defaultConfig } from './util';
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      loading: false,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '治理项名称', key: 'label' },
        { title: '功能描述', key: 'description', minWidth: 200 },
        { title: '启用状态', slot: 'status' },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          fixed: 'right',
          width: 90,
        },
      ],
      tableData: [],
      allTableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        label: '',
      },
    };
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    showTableList() {
      return this.tableData.filter((item) => item.label.includes(this.searchData.label));
    },
  },
  created() {
    this.getConfigList();
  },
  methods: {
    ...mapActions({
      getTabListByApi: 'governanceconfig/getTabListByApi',
    }),
    async getConfigList() {
      try {
        this.loading = true;
        this.allTableData = await this.getTabListByApi();
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
        if (this.allTableData.length === 0) {
          this.allTableData = this.$util.common.deepCopy(defaultConfig);
        } else {
          // 差值 -- 主要处理 新增项
          let arr = defaultConfig.filter((item) => {
            return !this.allTableData.some((t) => t.code === item.code);
          });
          this.allTableData = [...this.allTableData, ...arr];
        }
        this.getTableData();
      }
    },
    // changePage(val) {
    //   this.pageData.pageNum = val;
    //   this.getTableData();
    // },
    // changePageSize(val) {
    //   this.pageData.pageNum = 1;
    //   this.pageData.pageSize = val;
    //   this.getTableData();
    // },
    // 检索
    search() {
      this.getTableData();
    },
    resetSearch() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        label: '',
      };
      this.getTableData();
    },
    getTableData() {
      this.tableData = this.allTableData.filter((item) => item.label.includes(this.searchData.label));
    },
    async onChange(val, index) {
      try {
        this.tableData[index].status = val;
        let params = {
          paramKey: 'GOVERNANCE_PAGE_CONFIG',
          paramValue: JSON.stringify(this.tableData),
          paramType: 'ivdg',
        };
        await this.$http.put(home.updateByParamKey, params);
        this.$Message.success('成功');
        // 更新
        this.getTabListByApi();
      } catch (error) {
        console.log(error);
      }
    },
    openPage({ code, status }) {
      if (!status) return;
      this.$router.push({ path: '/governanceautomatic', query: { tab: code } });
    },
  },
};
</script>

<style lang="less" scoped>
.governan-cecofig-box {
  background: var(--bg-content);
  .search-module {
    padding: 20px;
  }
  .table-box {
    padding: 0 20px;
  }
  @{_deep}.btn-disabled i {
    cursor: not-allowed;
    color: var(--color-btn-primary-disabled);
  }
}
</style>
