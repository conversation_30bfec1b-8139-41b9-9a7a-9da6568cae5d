<template>
  <ui-modal v-model="visible" :title="title" @onCancel="reset" :styles="styles" :footer-hide="isResult">
    <div class="question-handle">
      <Form ref="formRef" :label-width="80" :model="formData" :rules="rules">
        <FormItem label="处理结果" prop="handleStatus">
          <RadioGroup v-model="formData.handleStatus" v-if="!isResult">
            <div class="inline" v-for="(item, index) in handleStatusList" :key="index">
              <!-- 单独处理 -->
              <Radio class="mr-sm" :label="item.dicKey" v-if="item.dicKey !== '1'">{{ item.dicValue }}</Radio>
            </div>
          </RadioGroup>
          <span v-else class="base-text-color">{{
            handleStatus[formData.handleStatus] && handleStatus[formData.handleStatus].text
          }}</span>
        </FormItem>
        <FormItem label="处理说明" prop="handleMessage">
          <Input
            v-model="formData.handleMessage"
            class="remark"
            type="textarea"
            :disabled="isResult"
            :autosize="{ minRows: 3, maxRows: 3 }"
            placeholder="请填写处理说明"
          ></Input>
        </FormItem>
        <FormItem prop="files" label="上传附件">
          <ui-upload
            v-model="formData.files"
            :accept="accept"
            :format="format"
            :upload-url="uploadUrl"
            :get-upload-params="getUploadParams"
            @delUpload="delUpload"
            :isView="isResult"
            uploadPlaceholder="上传附件"
          >
          </ui-upload>
        </FormItem>
        <div v-if="isResult">
          <FormItem label="处理人">
            <span class="base-text-color">{{ `${formData.handleUserName} (${formData.handleOrgName})` }}</span>
          </FormItem>
          <FormItem label="处理时间">
            <span class="base-text-color">{{ formData.handleTime }}</span>
          </FormItem>
        </div>
      </Form>
    </div>
    <template #footer>
      <Button class="plr-30" type="primary" @click="submitResult">提交处理结果</Button>
    </template>
  </ui-modal>
</template>
<script>
import unified from '@/config/api/unified.js';
import cascadeList from '@/config/api/cascadeList';
import { HANDLESTATUS, HANDLESTATUSLIST } from '../util/enum';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    isResult: {
      default: false,
    },
    defaultForm: {},
    activeItem: {},
    configData: {},
    // 上级下发查看处理结果需要替换接口
    isSuperior: {
      default: false,
    },
  },
  data() {
    const validateStatus = (rule, value, callback) => {
      if (!value || value === '1') {
        callback(new Error('请选择处理结果'));
      } else {
        callback();
      }
    };
    return {
      rules: {
        handleStatus: [{ required: true, validator: validateStatus, trigger: 'change' }],
        handleMessage: [{ required: true, message: '请选择处理说明', trigger: 'blur' }],
      },
      handleStatus: {},
      handleStatusList: [],
      formData: {
        handleStatus: '2',
        handleMessage: '',
        files: '',
      },
      visible: false,
      styles: {
        width: '5.2rem',
      },
      uploadUrl: unified.fileUpload,
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      accept: '.bmp,.jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
    };
  },
  created() {},
  updated() {},
  methods: {
    getUploadParams(uploadData) {
      let uploadParams = [];
      uploadData.forEach((row) => {
        uploadParams.push(row.url || row.response.data.fileUrl);
      });
      return uploadParams.join(',');
    },
    //清除文件的回调
    delUpload() {
      this.formData.files = '';
    },
    reset() {
      this.formData = {
        handleStatus: '',
        handleMessage: '',
      };
      this.$refs['formRef'].resetFields();
    },
    async submitResult() {
      this.$refs['formRef'].validate(async (valid) => {
        if (valid) {
          try {
            let { id: userId, username, orgVoList } = this.userInfo;
            let { id, orgCode, handleStatus, handleMessage, files } = this.formData;
            let params = {
              id: id,
              handleOrgName: orgVoList.map((item) => item.orgName).join(','),
              handleUserId: userId,
              handleUserName: username,
              orgCode: orgCode,
              handleStatus,
              handleMessage,
              files,
            };
            await this.$http.post(cascadeList.postCascadeIssue, params, {
              headers: {
                token: this.configData.superiorToken,
                orgCode: this.configData.deployOrgCode,
                flag: 'visitor',
              },
            });
            this.$emit('updateList');
          } catch (error) {
            console.log(error);
          }
        }
      });
    },
  },
  computed: {
    ...mapGetters({
      userInfo: 'user/getUserInfo',
    }),
    title() {
      return this.isResult ? '查看处理结果' : '问题处理';
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.handleStatus = {
          ...HANDLESTATUS,
        };
        this.handleStatusList = [...HANDLESTATUSLIST];
        this.formData = { ...this.formData, ...this.activeItem };
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
    defaultForm(val) {
      Object.assign(this.formData, val);
    },
  },
  components: {
    UiUpload: require('@/components/ui-upload.vue').default,
  },
};
</script>
<style lang="less" scoped>
.question-handle {
  width: 100%;
}
.remark {
  width: 100%;
}
</style>
