<template>
  <div class="examine-info">
    <Form :model="detailData" label-position="right" :label-width="120" :label-colon="true">
      <FormItem label="审核结果">
        <span :style="{ color: statusTextColor }">{{ getStatusText }}</span>
      </FormItem>
      <FormItem label="审核人">
        <span class="font-color">{{ !!detailData ? detailData.reviewUserName : '' }}</span>
      </FormItem>
      <FormItem label="审核人单位">
        <span class="font-color">{{ !!detailData ? detailData.reviewUserCivilName : '' }}</span>
      </FormItem>
      <FormItem label="审核人联系方式">
        <span class="font-color">{{ !!detailData ? detailData.reviewPhoneNumber : '' }}</span>
      </FormItem>
      <FormItem label="审核时间">
        <span class="font-color">{{ !!detailData ? detailData.reviewTime : '' }}</span>
      </FormItem>
      <FormItem label="审核备注">
        <span class="font-color">{{ !!detailData ? detailData.reviewRemark : '' }}</span>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import { statusArr } from '.././situationreporting.js';
export default {
  name: 'examine-info',
  props: {
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statusObj: {
        '1': '待审核',
      },
      statusTextColor: '',
    };
  },
  computed: {
    getStatusText() {
      if (!this.detailData || !this.detailData.status) return '';
      const statusObj = statusArr.find((item) => {
        if (item.value === this.detailData.status) {
          this.statusTextColor = item.color;
          return item;
        }
      });
      return statusObj.text;
    },
  },
};
</script>

<style lang="less" scoped>
.examine-info {
  font-size: 14px;
  .download-text {
    color: #438cff;
    cursor: pointer;
  }
  @{_deep} .ivu-form-item {
    margin-bottom: 5px !important;
  }
  .font-color {
    color: var(--color-content);
  }
}
</style>
