<template>
  <IndexSelect ref="indexSelectRef" v-model="selectData" :data="sourceData" @change="handleChangeIndex"></IndexSelect>
</template>
<script>
export default {
  name: 'index-choose',
  props: {
    sourceData: {
      default: () => {},
    },
  },
  data() {
    return {
      selectData: [],
    };
  },
  computed: {},
  mounted() {
    // 解决全屏screenfull下，一些transfer弹框不展示问题
    let bigParent = this.$refs.indexSelectRef?.$parent?.$parent?.$parent?.$parent?.$el;
    let dropdownTransfer = document.querySelectorAll('.ivu-dropdown-transfer');
    if (!bigParent || !dropdownTransfer) return;
    dropdownTransfer.forEach((dom) => {
      bigParent.appendChild(dom);
    });
  },
  methods: {
    async handleChangeIndex() {
      this.$emit('chooseSelectData', this.selectData);
    },
  },
  components: {
    IndexSelect: require('@/views/home/<USER>/index-select.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
