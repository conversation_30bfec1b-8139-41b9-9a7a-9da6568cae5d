
let appRouter = []
try {
  const context = require.context('./routers', true, /\/.*\.js$/)
  // const context = require.context('./router', true, /sysconf.js$/)
  context.keys().forEach((key) => {
    const appConf = context(key).default
    if (appConf instanceof Array) {
      appRouter = [...appRouter, ...appConf]
    } else {
      appRouter.push(appConf)
    }
  })
} catch (e) {
  console.error(e)
}

export { appRouter as asyncRoutes }