<template>
  <div class="over-flow upload-img">
    <!-- 编辑 -->
    <template v-if="isEdit">
      <div
        v-for="(item, index) in successData"
        :key="index"
        class="upload-item"
      >
        <div
          :class="
            activeIndex === index
              ? 'card-border-active-color'
              : 'card-border-color'
          "
          class="upload-list"
          @click="chooseHandle(item, index)"
        >
          <ui-image :src="item.photoUrl"></ui-image>
          <div
            v-if="deleted"
            :class="choosed ? 'upload-list-radius' : 'upload-list-cover'"
          >
            <ui-icon
              type="shanchu"
              @click.native="handleRemove(index, $event)"
              color="#FFFFFF"
            ></ui-icon>
          </div>
        </div>
      </div>
    </template>
    <!-- 新增 -->
    <template v-else>
      <div
        v-for="(item, index) in successData"
        :key="index"
        class="upload-item"
      >
        <div
          :class="
            activeIndex === index
              ? 'card-border-active-color'
              : 'card-border-color'
          "
          class="upload-list"
          @click="chooseHandle(item, index)"
        >
          <ui-image :src="item.photoUrl || item"></ui-image>
          <div
            v-if="deleted"
            :class="choosed ? 'upload-list-radius' : 'upload-list-cover'"
          >
            <ui-icon
              type="shanchu"
              @click.native="handleRemove(index, $event)"
              color="#FFFFFF"
            ></ui-icon>
          </div>
        </div>
      </div>
    </template>
    <Upload
      v-if="successData.length < 1"
      ref="upload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :format="['jpg', 'jpeg', 'png']"
      :max-size="20480"
      :on-format-error="handleFormatError"
      :on-exceeded-size="handleMaxSize"
      :before-upload="handleBeforeUpload"
      :show-upload-list="false"
      :headers="{ Authorization: 'Bearer ' + $store.state.user.token }"
      type="drag"
      name="file"
      class="upload-item"
      action="/qsdi-system-service/file/upload"
    >
      <div v-if="!loading" class="upload-label card-bg">
        <Icon type="ios-add" />
        <span class="upload-text f-12 label-color">上传图片</span>
      </div>
      <div v-else class="loader-box">
        <ui-loading></ui-loading>
      </div>
    </Upload>
  </div>
</template>
<script>
import { deepCopy } from "@/util/modules/common";
import UiIcon from "./ui-icon.vue";
export default {
  name: "UploadImgStaticLibrary",
  components: { UiIcon },
  props: {
    // 最大上传图片张数
    multipleNum: {
      type: Number,
      default: () => {
        return 3;
      },
    },
    // 是否可以选中
    choosed: {
      type: Boolean,
      default: false,
    },
    // 选中的下标
    choosedIndex: {
      type: String | Number,
      default: "",
    },
    // 是否可以删除
    deleted: {
      type: Boolean,
      default: false,
    },
    // 默认已经上传的照片
    defaultList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    // 是否编辑，false-新增, true-编辑
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      successData: [],
      delImageList: [], // 删除图片保存，修改表单时候用
      loading: false,
      activeIndex: "",
      // headers: {
      //   Authorization: `Bearer ${lo.token}`
      // }
    };
  },
  computed: {},
  watch: {
    defaultList(val) {
      if (
        this.defaultList.length > 0 &&
        this.defaultList[0].photoUrl == undefined
      ) {
        let arr = this.defaultList.map((item) => {
          return {
            photoUrl: item,
          };
        });
        this.successData = deepCopy(arr);
        return;
      }
      this.successData = this.defaultList;
    },
    choosedIndex(val) {
      this.activeIndex = val;
    },
  },
  created() {},
  mounted() {
    this.successData = this.defaultList;
  },
  methods: {
    handleBeforeUpload(file) {
      this.loading = true;
    },
    handleError() {
      this.loading = false;
      this.$Message.error("上传图片失败");
    },
    handleFormatError() {
      this.loading = false;
      this.$Message.error("请上传以下文件类型:jpg、jpeg、png");
    },
    handleMaxSize() {
      this.loading = false;
      this.$Message.error("上传图片大于20M请选择合适的图片");
    },
    handleSuccess(val) {
      console.log("图片上传成功", val);
      if (val.code === 200) {
        if (!this.successData.length) {
          this.$emit("on-choose", val.data.fileUrl);
          this.activeIndex = 0;
        }

        if (this.isEdit) {
          // 编辑
          this.successData.push({
            delStatus: 1,
            photoUrl: val.data.fileUrl,
          });
        } else {
          // 新增
          this.successData.push(val.data.fileUrl);
        }
        this.loading = false;
        this.$emit("successPut", this.successData);
      } else {
        this.$Message.error("上传图片失败");
      }
    },
    /**
     * 删除图片
     */
    handleRemove(index, even) {
      if (this.isEdit) {
        // 编辑
        var row = this.successData[index];
        console.log("删除得对象", row);
        row.delStatus = "2";
        this.delImageList.push(row);
        // if (row.id) {
        //   this.delImageList.push(row)
        // }
      }

      even.stopPropagation();
      this.successData.splice(index, 1);
      this.$emit("successPut", this.successData);
      if (this.activeIndex === index && !this.successData.length) {
        this.activeIndex = "";
      } else if (
        this.activeIndex === index &&
        (this.successData.length === 1 ||
          this.activeIndex === this.successData.length)
      ) {
        this.activeIndex = 0;
      }
      this.$emit("on-choose", this.successData[this.activeIndex]);
    },
    clear() {
      this.successData = [];
    },
    // 选中
    chooseHandle(item, index) {
      this.activeIndex = index;
      this.$emit("on-choose", item);
    },
  },
};
</script>
<style lang="less" scoped>
.upload-img {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.ivu-icon-ios-add {
  font-size: 30px;
  color: #888;
}
.upload-item {
  width: 40px !important;
  height: 40px !important;
  margin-left: 10px;
  position: relative;
  margin-bottom: 10px;
  &:first-child {
    margin-left: 0;
  }
  &:nth-child(6) {
    margin-left: 0;
  }
  /deep/ .ivu-upload {
    height: 100%;
    width: 100%;
    &-drag {
      border-radius: 0;
    }
  }
}
.upload-img:nth-child(6) {
  margin-left: 0;
}

.upload-label {
  width: 100%;
  height: 100%;
  border: 1px dashed #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0 !important;
}

.upload-list {
  width: 100%;
  height: 100%;
  line-height: 1;
  border: 1px solid transparent;
  box-sizing: border-box;
  position: relative;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}
.upload-text {
  font-size: 12px;
}

.upload-icon {
  display: inline-block;
  width: 48px;
  height: 40px;
}

.upload-list:hover .upload-list-cover,
.upload-list:hover .upload-list-radius {
  display: flex;
}

.upload-list-cover,
.upload-list-radius {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 11;
}

.upload-list-radius {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  top: -10px;
  right: -10px;
  left: unset;
  margin: 0;
}

.upload-list-cover i,
.upload-list-radius i {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}
.upload-list-radius i {
  font-size: 18px;
}

.loader-box {
  height: 100%;
}

.loader {
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 1em;
  display: inline-block;
  vertical-align: top;
  z-index: 5;
  svg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>
