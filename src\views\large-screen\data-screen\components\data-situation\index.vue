<!--
    * @FileDescription: 数据专题大屏-数据态势
    * @Author: H
    * @Date: 2024/04/25
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="感知数据统计" class="screen-box">
                <ul class="tab">
                    <li class="tab-btn" :class="{'tab-btn-active': perIndex == 0}" @click="handleper(0)">视图数据</li>
                    <li class="tab-btn" :class="{'tab-btn-active': perIndex == 1}" @click="handleper(1)">物联数据</li>
                </ul>
                <ul class="data-box-ul">
                    <li class="data-box" v-for="(item, index) in dataList" :key="index">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <div class="data-box-top">
                            <div class="box-top-left">
                                <p class="data-box-name">{{ item.name }}</p>
                                <count-to :start-val="0" :end-val="item.totalCount" :duration="1000" class="data-box-num"></count-to>
                            </div>
                            <img class="icon-type" :src="item.iconUrl" alt="">
                        </div>
                        <p class="data-box-add">今日新增</p>
                        <div class="box-add">
                            <div class="box-add-left">{{ item.todayCount }}</div>
                            <div class="box-add-right">
                                <p>+5</p>
                                <img :src="getImgUrl('up.png')" alt="">
                                <!-- <img :src="getImgUrl('down.png')" alt=""> -->
                            </div>
                        </div>
                    </li>
                </ul>
            </card>
            <card title="数据月度趋势" class="screen-box">
                <month-chart></month-chart>
            </card>
            <card title="数据时段分布" class="screen-box">
                <time-frame-chart></time-frame-chart>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        视图数据
                    </div>
                    <div class="view-box-top"> 
                        <p>总量</p>
                        <p class="gross dinpro">58.18 <span>亿</span> </p>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-bot"> 
                        <div class="new-today">
                            <p>今日新增</p> 
                            <count-to :start-val="0" :end-val="7039.244" :duration="1000" class="dinpromini"></count-to>
                        </div>
                        <div class="chain-system">
                            <p>环比</p>
                            <div class="chain-system-num">
                                <span class="dinpromini"> +5.11%</span>
                                <img :src="getImgUrl('up.png')" alt="">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-box-right view-box">
                    <div class="title-img bigtitle">
                        物联数据
                    </div>
                    <div class="view-box-top"> 
                        <p>总量</p>
                        <p class="gross dinpro">8.28 <span>亿</span> </p>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-bot"> 
                        <p class="new-today">今日新增 
                            <count-to :start-val="0.000" :end-val="1915.223" :duration="1000" class="dinpromini"></count-to>
                        </p>
                        <div class="chain-system">
                            <p>环比 </p>
                            <div class="chain-system-num">
                                <span class="dinpromini">2.11%</span>
                                <img :src="getImgUrl('up.png')" alt="">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <map-chart ref='mapChart' class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="价值数据有效率" class="screen-box">
                <pie-chart ref="costchart" :picData="costList" title="解析数据总量" :totalCount="costCount" :urlType="0"></pie-chart>
            </card>
            <card title="警情信息有效率" class="screen-box">
                <pie-chart ref="particulchart" :picData="particulList" title="警情信息总量" :totalCount="particulCount" :urlType="1"></pie-chart>
            </card>
            <card title="推荐信息有效率" class="screen-box">
                <pie-chart ref="recomchart" :picData="recomList" title="推荐信息总量" :totalCount="recomCount" :urlType="2"></pie-chart>
            </card>
        </div>
    </div>
</template>
<script>
import { accessTotalDataStat, efficiency, regionStat } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import monthChart from './month-tendency-chart.vue';
import pieChart from './pie-chart.vue';
import mapChart from './map-chart.vue';
import timeFrameChart from './time-frame-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        monthChart,
        pieChart,
        mapChart,
        timeFrameChart,
        CountTo
    },
    data() {
        return {
            perIndex: 0,
            face:  require(`@/assets/img/screen/face.png`),
            titleImg: require(`@/assets/img/screen/midtitle.png`),
            dataList: [],
            viewList: [
                {
                    name: '人脸抓拍总数',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/face.png`),
                    type: 'face',
                },
                {
                    name: '车辆抓拍总数',
                    totalCount: 5125033123,
                    todayCount: 12916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/car.png`),
                    type: 'vehicle',
                },
                {
                    name: '非机动车抓拍总数',
                    totalCount: 1224012924,
                    todayCount: 12916,
                    speed: -6,
                    iconUrl: require(`@/assets/img/screen/fj.png`),
                    type: 'nonmotor',
                },
                {
                    name: '人体抓拍总数',
                    totalCount: 825713924,
                    todayCount: 3234,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/rt.png`),
                    type: 'humanBody',
                },
            ],
            objectList: [
                {
                    name: 'ETC采集总量',
                    totalCount: 324321789,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/etc.png`),
                    type: 'etc',
                },
                {
                    name: '电磁采集总量',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/dianci.png`),
                    type: 'electric',
                },
                {
                    name: 'GPS采集总量',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/gps.png`),
                    type: 'gps',
                },
                {
                    name: 'RFID采集总量',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/rfid.png`),
                    type: 'rfid',
                },
                {
                    name: 'MAC采集总量',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/mac.png`),
                    type: 'rfid',
                },
                {
                    name: 'IMSI采集总量',
                    totalCount: 7625012924,
                    todayCount: 512916,
                    speed: 5,
                    iconUrl: require(`@/assets/img/screen/imsi.png`),
                    type: 'rfid',
                },
            ],
            costList: [
                { value: '0', name: '有效', type: 'valid' },
                { value: '0', name: '无效', type: 'invalid' },
            ],
            particulList: [
                { value: '42', name: '有效', type: 'valid' },
                { value: '25', name: '无效', type: 'invalid' },
                { value: '33', name: '未处理', type: 'unprocessed' },
            ],
            recomList: [
                { value: '26', name: '有效', type: 'valid' },
                { value: '38', name: '无效', type: 'invalid'},
                { value: '36', name: '未处理', type: 'unprocessed' },
            ],
            costCount: 0,
            particulCount: 0,
            recomCount: 0,
            statisticsData: { },
        }
    },
    created() {
        this.dataList = this.viewList;
        this.init();
        this.queryEffective();
        // this.queryMap();
    },
    methods: {
        init() {
            accessTotalDataStat()
            .then(res => {
                this.statisticsData = res.data;
                this.dataList.forEach(item => {
                    item.totalCount = this.statisticsData[item.type].totalCount;
                    item.todayCount = this.statisticsData[item.type].todayCount;
                })
            }) 
        },
        queryMap() {
            regionStat(1)
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        },
        queryEffective() {
            efficiency()
            .then(res => {
                res.data.forEach(item => {
                    if(item.type == 1) {
                        this.costCount = item.total;
                        this.costList.forEach(ite => {
                            if(ite.type == 'valid') {
                                ite.value = item.valid;
                            }else{
                                ite.value = item.invalid;
                            }
                        })
                        this.$refs.costchart.init(this.costList);
                    }else if(item.type == 2) {
                        this.particulCount = item.total;
                        this.particulList.forEach(ite => {
                            if(ite.type == 'valid') {
                                ite.value = item.valid;
                            }else if(ite.type == 'invalid'){
                                ite.value = item.invalid;
                            }else{
                                ite.value = item.unprocessed;
                            }
                        })
                        this.$refs.particulchart.init(this.particulList)
                    }else if(item.type == 3){
                        this.recomCount = item.total;
                        this.recomList.forEach(ite => {
                            if(ite.type == 'valid') {
                                ite.value = item.valid;
                            }else if(ite.type == 'invalid'){
                                ite.value = item.invalid;
                            }else{
                                ite.value = item.unprocessed;
                            }
                        })
                        this.$refs.recomchart.init(this.recomList)
                    }
                })
                this.$forceUpdate()
            })
        },
        handleper(index) {
            this.perIndex = index;
            if(index == 0) {
                this.dataList = this.viewList;
            } else {
                this.dataList = this.objectList;
            }
            this.dataList.forEach(item => {
                item.totalCount = this.statisticsData[item.type].totalCount;
                item.todayCount = this.statisticsData[item.type].todayCount;
            })
        },
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../common/style.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    .screen-left, 
    .screen-right{
        width: 420px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .screen-box{
            height: calc( ~'33% - 5px');
            position: relative;
            .tab{
                display: flex;
                position: absolute;
                top: 10px;
                right: 6px;
                .tab-btn{
                    padding: 2px 4px;
                    background: rgba(0, 150, 255, .1);
                    color: #7195D0;
                    font-size: 12px;
                    font-weight: 400;
                    margin-left: 5px;
                    cursor: pointer;
                }
                .tab-btn-active{
                    color: #ffffff;
                    background: linear-gradient(180deg, rgba(8,224,255,0) 0%, #08E0FF 100%);
                }
            }
            .data-box-ul{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                overflow-y: auto;
            }
            .data-box{
                background: rgba(24, 98, 187, 0.1);
                width: 195px;
                height: calc( ~'50% - 5px');
                padding: 10px 10px 5px 10px;
                margin-bottom: 5px;
                position: relative;
                .data-box-top{
                    display: flex;
                    justify-content: space-between;
                    .box-top-left{
                        .data-box-name{
                            font-size: 14px;
                            font-weight: 400;
                            color: #ffffff;
                        }
                        .data-box-num{
                            font-family: 'DINPro';
                            font-size: 20px;
                            color: #03A9FF;
                        }
                    }
                    .icon-type{
                        width: 30px;
                        height: 30px;
                    }
                }
                .data-box-add{
                    font-size: 12px;
                    font-weight: 400;
                    color: #ffffff;
                    margin-top: 3%;
                }
                .box-add{
                    display: flex;
                    .box-add-left{
                        width: 100px;
                        font-size: 14px;
                        color: #2DDF5C;
                    }
                    .box-add-right{
                        font-size: 14px;
                        color: #2DDF5C;
                        display: flex;
                        align-items: center;
                        img{
                            width: 12px;
                            height: 14px;
                            margin-left: 10px;
                        }
                    }
                }
            }
            .data-box:last-child{
                margin-bottom: 0;
            }
            .data-box:nth-last-child(2){
                margin-bottom: 0;
            }
        }
    }
    .screen-main{
        flex: 1;
        position: relative;
        padding: 10px 20px 0 20px;
        height: 100%;
        background: url('~@/assets/img/screen/screen-quan.png') no-repeat;
        background-position: center;
        background-size: auto;
        display: flex;
        flex-direction: column;
        .content-top{
            display: flex;
            justify-content: space-around;
            .view-box{
                width: 411px;
                height: 170px;
                background: url('~@/assets/img/screen/midboxbg.png') no-repeat;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 44px;
                .title-img{
                    width: 150px;
                    height: 40px;
                    position: absolute;
                    background: url('~@/assets/img/screen/midtitle.png') no-repeat;
                    left: 50%;
                    transform: translateX(-50%);
                    top: -20px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 20px;
                }
                .view-box-top{
                    color: #fff;
                    font-size: 14px;
                    font-weight: 400;
                    .gross{
                        font-size: 28px;
                        color: #F1FCFF;
                        margin-top: 3px;
                        span{
                            font-size: 14px;
                            color: #03A9FF;
                        }
                    }
                }
                .view-box-bot{
                    
                    .new-today{
                        font-size: 14px;
                        color: #ffffff;
                        display: flex;
                        p{
                            width:60px;
                            text-align: right;
                        }
                        span{
                            font-size: 14px;
                            color: #2DDF6C;
                            margin-left: 10px;
                        }
                    }
                    .chain-system{
                        display: flex;
                        margin-top: 23px;
                        font-size: 14px;
                        color: #ffffff;
                        p{
                            width: 60px;
                            text-align: right;
                        }
                        .chain-system-num{
                            display: flex; 
                            align-items: center;
                            margin-left: 10px;
                            span{
                                font-size: 14px;
                                color: #03A9FF;
                                margin-right: 5px;
                            }
                            img{
                                width: 12px;
                                height: 14px;
                            }
                        }
                    }
                }

            }
            .view-data{
                
            }
            .content-box-right{

            }
        }
        .map-chart{
            flex:1;
        }
    }
    .data-box-ul ::-webkit-scrollbar {
        width: 1px;
    }
    ::-webkit-scrollbar{
        width: 4px;
        height: 10px;
        background-color: #02162b;
    }
    ::-webkit-scrollbar-track{
        box-shadow: inset 0 0 6px rgba(0,0,0, 0.3);
        border-radius: 10px;
        background-color: transparent;
    }
    ::-webkit-scrollbar-thumb{
        box-shadow: inset 0 0 6px rgba(0,0,0, 0.1);
        background-color: rgba(25,143,226, 0.58);
        border-radius: 10px;
    }
    ::-webkit-scrollber-corner{
        background-color: rgba(0, 0, 0, 0.4);
    }
}
</style>