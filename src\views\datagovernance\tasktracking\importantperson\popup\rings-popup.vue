<template>
  <ui-modal :title="ringTitle" v-model="visible" :styles="styles" :footer-hide="true">
    <div class="ring-box">
      <base-search @startSearch="startBaseSearch" :tagTypeList="tagTypeList">
        <p class="search-box fr">
          <Button type="primary" class="ml-lg button-blue" @click="exportExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </p>
      </base-search>
      <line-title title-name="检测结果统计"></line-title>
      <div class="echarts-wrapper mt-sm">
        <draw-echarts
          v-for="(item, index) of echartList"
          :key="index"
          :echart-option="item.echartRingOption"
          :echart-style="ringStyle"
          :echarts-loading="echartsLoading"
          :ref="'zdryChart' + index"
          class="charts"
        >
        </draw-echarts>
      </div>
      <div class="table-wrapper">
        <ui-table
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :minus-height="minusTable"
          :loading="tableLoading"
        >
          <template #faceImg="{ item }">
            <ui-image :src="item.identityPhoto" class="image" />
            <!-- <img src="@/assets/img/common/nodata.png" class="face-image"> -->
          </template>
          <!-- <template #action>
                        <span class="font-table-action pointer" @click="$emit('openStandardList')">重新转换</span>
                    </template> -->
        </ui-table>
        <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import importantEnum from '../util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
    tagTypeList: {
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '95%',
      },
      activeRouterName: null,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '人员照片', slot: 'faceImg' },
        { title: '姓名', key: 'name' },
        { title: '性别', key: 'genderText' },
        { title: '民族', key: 'nation' },
        { title: '证件类型', key: 'cardTypeText' },
        { title: '证件号', key: 'idCard' },
        { title: '重点人员类型', key: 'personTypeText' },
        { title: '居住地址', key: 'homeAddress' },
        // { title: "操作", slot: "action" },
      ],
      minusTable: 520,
      ringStyle: {
        height: '200px',
        width: '500px',
      },
      ringTitle: '',
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      echartList: [{ echartRingOption: {} }, { echartRingOption: {} }, { echartRingOption: {} }],
      tableData: [],
      echartsLoading: false,
      tableLoading: false,
      popUpOption: {},
      searchData: {
        personType: '',
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    init(item) {
      this.echartList = [{ echartRingOption: {} }, { echartRingOption: {} }, { echartRingOption: {} }];
      this.tableData = [];
      this.searchData = {
        personType: '',
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.visible = true;
      this.ringTitle = item.title;
      if (item.subTitle) {
        this.ringTitle = `${item.title} - 入库失败`;
      }
      this.popUpRingGetData(item);
    },
    startBaseSearch(searchData) {
      Object.assign(this.searchData, searchData);
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.getRingsList();
    },
    popUpRingGetData(option) {
      this.popUpOption = option;
      this.echartList = [{ echartRingOption: {} }, { echartRingOption: {} }, { echartRingOption: {} }];
      const staticFuncObject = {
        // 字段映射
        [importantEnum.aggregateCodeEnums['字段映射']]: () => this.querypersonTransferStatistics(),
        // 字典映射
        [importantEnum.aggregateCodeEnums['字典映射']]: () => this.querypersonTransferStatistics(),
        // 正式入库
        [importantEnum.aggregateCodeEnums['正式入库']]: () => this.queryPersonInitFailStatistics(),
      };
      staticFuncObject[this.popUpOption.filedData.componentCode]();
      this.getRingsList();
    },
    // 不同弹窗获取列表的接口不同，需要处理
    getRingsList() {
      const listFuncObject = {
        // 字段映射
        字段映射: () => this.queryPersonInitTransferList(),
        // 字典映射
        字典映射: () => this.queryPersonInitTransferList(),
        // 正式入库
        正式入库: () => this.queryPersonInitFailList(),
      };
      listFuncObject[this.popUpOption.title]();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.getRingsList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.getRingsList();
    },
    // 获取人员转换失败列表
    async queryPersonInitTransferList() {
      try {
        this.tableLoading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonInitTransferList, this.searchData);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.tableLoading = false;
      } catch (err) {
        this.tableLoading = false;
        console.log(err);
      }
    },
    // 获取人员转换失败统计
    async querypersonTransferStatistics() {
      try {
        this.echartsLoading = true;
        let { data } = await this.$http.get(tasktracking.querypersonTransferStatistics);
        let allData = data.data;
        let successData = {
          name: '映射成功',
          value: allData.goodCount,
          color: importantEnum.ringColorEnum.greenColor,
        };
        let failData = {
          name: '映射失败',
          value: allData.failCount,
          color: importantEnum.ringColorEnum.redColor,
        };
        this.echartList = [
          {
            echartRingOption: this.handleRingsEcharts([successData, failData], '映射总量', allData.allCount),
          },
          {
            echartRingOption: this.handleRingsEcharts([successData], '映射成功', allData.goodCount),
          },
          {
            echartRingOption: this.handleRingsEcharts([failData], '映射失败', allData.failCount),
          },
        ];
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },

    // 获取人员入库失败统计
    async queryPersonInitFailStatistics() {
      try {
        this.echartsLoading = true;
        let { data } = await this.$http.get(tasktracking.queryPersonInitFailStatistics);
        let allData = data.data;
        let successData = {
          name: '入库成功',
          value: allData.goodCount,
          color: importantEnum.ringColorEnum.greenColor,
        };
        let failData = {
          name: '入库失败',
          value: allData.failCount,
          color: importantEnum.ringColorEnum.redColor,
        };
        this.echartList = [
          {
            echartRingOption: this.handleRingsEcharts([successData, failData], '入库总量', allData.allCount),
          },
          {
            echartRingOption: this.handleRingsEcharts([successData], '入库成功', allData.goodCount),
          },
          {
            echartRingOption: this.handleRingsEcharts([failData], '入库失败', allData.failCount),
          },
        ];
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    // 获取正式入库列表
    async queryPersonInitFailList() {
      try {
        this.tableLoading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonInitFailList, this.searchData);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.tableLoading = false;
      } catch (err) {
        this.tableLoading = false;
        console.log(err);
      }
    },
    handleRingsEcharts(data, text, subtext) {
      let options = {
        data: data,
        text: text,
        subtext: subtext,
        legendData: data.map((item) => item.name),
      };
      return this.$util.doEcharts.taskTrackingRing(options);
    },
    async exportExcel() {
      try {
        let params = {
          ...this.searchData,
          topicComponentId: this.popUpOption.filedData.topicComponentId,
        };
        let res = await this.$http.get(tasktracking.downloadPersonAbnormalData, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    BaseSearch: require('../components/base-search.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  background: var(--bg-sub-content);
  margin: 10px 0;
  width: 100%;
  height: 200px;
  .charts {
    flex: 1;
  }
}
.table-wrapper {
  position: relative;
  .image {
    width: 40px;
    height: 40px;
  }
}
</style>
