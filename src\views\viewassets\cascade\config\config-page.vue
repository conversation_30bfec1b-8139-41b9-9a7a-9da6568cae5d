<template>
  <ui-modal v-model="modalShow" title="接收配置" :styles="styles" @query="submitHandle">
    <loading v-if="dataLoading" />
    <div class="rule-container">
      <div class="params-item mb-sm cb">
        <div class="params-label fl">设备接收配置</div>
        <div class="params-content">
          <div v-for="(item, index) in deviceReceiveConfigList" :key="index">
            <Checkbox :disabled="isView" v-model="item.status" true-value="1" false-value="2">
              <span class="dis-select">{{ item.ruleName }}</span>
            </Checkbox>
            <i
              @click="clickRule(item)"
              v-if="isConfig.includes(item.ruleCode) && !isView"
              class="icon-font icon-systemmanagement f-14 setting dis-select"
            ></i>
          </div>
        </div>
      </div>
      <div class="params-item mb-sm cb">
        <div class="params-label fl">检测规则设置</div>
        <div class="params-content">
          <div v-for="(item, index) in checkRuleConfigList" :key="index">
            <Checkbox :disabled="isView" v-model="item.status" true-value="1" false-value="2">
              <span class="dis-select">{{ `${item.ruleName}：${item.ruleDesc}` }}</span>
            </Checkbox>
            <i
              @click="clickRule(item)"
              v-if="isConfig.includes(item.ruleCode) && !isView"
              class="icon-font icon-systemmanagement f-14 setting dis-select"
            ></i>
          </div>
        </div>
      </div>
      <!-- 空值检测   -->
      <empty ref="Empty" />
      <!-- 重复检测   -->
      <repeat ref="Repeat" />
      <!--  经纬度检测  -->
      <longitude-latitude ref="longitudeLatitude" />
      <!--ip检测-->
      <ip-address ref="ipAddress" />
      <!--设备编码-->
      <device-code ref="deviceCode" />
    </div>
  </ui-modal>
</template>

<script>
import cascade from '@/config/api/cascade';

export default {
  props: {
    // /**
    //  * 模式： edit 编辑 view 查看 add 新增
    //  */
    // formModel: {
    //   required: true,
    //   type: String,
    //   default: "edit",
    // },
    // /* 表单 */
    // formData: {
    //   required: true,
    //   type: Object,
    //   default: ()=>{
    //     return {};
    //   }
    // },
  },
  components: {
    IpAddress: require('./ip-address').default,
    Empty: require('./empty').default,
    Repeat: require('./repeat').default,
    LongitudeLatitude: require('./longitude-latitude').default,
    deviceCode: require('./device-code').default,
  },
  data() {
    return {
      modalShow: false,
      dataLoading: false,
      styles: {
        width: '4rem',
      },
      isConfig: ['1001', '1002', '1003', '1006', '1007', '1009'],
      isView: false,
      deviceReceiveConfigList: [],
      checkRuleConfigList: [],
    };
  },
  watch: {
    value(val) {
      if (val) {
        this.searchData.deviceCode = this.viewData.deviceId;
        this.copySearch = this.$util.common.deepCopy(this.searchData);
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    show() {
      this.modalShow = true;
      this.init();
    },
    init() {
      this.dataLoading = true;
      try {
        this.$http.post(cascade.configQueryReceive).then((res) => {
          this.deviceReceiveConfigList = res.data.data;
          this.$http.post(cascade.configQueryReceiveDetection).then((res) => {
            this.checkRuleConfigList = res.data.data;
            this.dataLoading = false;
          });
        });
      } catch (err) {
        this.dataLoading = false;
      }
    },
    clickRule(item) {
      let map = {
        //空值检测
        1001: () => {
          this.$refs.Empty.init({ indexRuleId: item.id });
        },
        //重复检测
        1002: () => {
          this.$refs.Repeat.init({ indexRuleId: item.id });
        },
        //IP地址格式检测
        1003: () => {
          this.$refs.ipAddress.init({ indexRuleId: item.id });
        },
        //设备编码格式检测
        1006: () => {
          this.$refs.deviceCode.init({ indexRuleId: item.id });
        },
        //经纬度精度检测
        1007: () => {
          this.$refs.longitudeLatitude.init({ indexRuleId: item.id });
        },
        //经纬度越界检测
        1009: () => {
          this.$refs.longitudeLatitude.init({ indexRuleId: item.id });
        },
      };
      map[item['ruleCode']]();
    },
    async submitHandle() {
      try {
        let resultFromData = this.deviceReceiveConfigList
          .concat(this.checkRuleConfigList)
          .filter((item) => item.status === '1');
        await this.$http.post(cascade.configSaveReceive, resultFromData);
        this.$Message.success('成功');
        this.modalShow = false;
      } catch (e) {
        console.error(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import url('./common.less');

.dis-select {
  user-select: none;
}

.setting {
  color: var(--color-primary);
}
</style>
