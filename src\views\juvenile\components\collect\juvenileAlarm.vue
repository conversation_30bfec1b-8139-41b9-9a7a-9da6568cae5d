<!--
    * @FileDescription: 前科人员报警
    * @Author: H
    * @Date: 2023/05/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 11:36:38
 -->
<template>
  <div class="personnel-alarm" @click="detailFn(data)">
    <div class="box-top">
      <div class="check-box">
        <Checkbox
          v-if="isShowCheckBox"
          v-model="data.checked"
          @click.stop.native="() => {}"
        />
      </div>

      <div class="level-title">
        <img class="level" :src="levelImg[data.bgIndex]" alt="" />
        <div class="num">
          {{
            data.taskLevel == 1 ? "一级" : data.taskLevel == 2 ? "二级" : "三级"
          }}
        </div>
      </div>
      <!-- <div class="collection favorite">
        <ui-btn-tip
          class="collection-icon"
          v-if="data.myFavorite == 1"
          content="取消收藏"
          icon="icon-yishoucang"
          transfer
          @click.stop.native="collection(data, 2)"
        />
        <ui-btn-tip
          class="collection-icon"
          v-else
          content="收藏"
          icon="icon-shoucang"
          transfer
          @click.stop.native="collection(data, 1)"
        />
      </div> -->
    </div>
    <div class="contrast">
      <div class="contrast-left">
        <ui-image :src="data.traitImg"></ui-image>
        <div class="contrast-title">报警照片</div>
      </div>
      <div class="contrast-content">
        <ui-image class="animation" :src="percent[data.bgIndex]"></ui-image>
        <div
          class="num"
          :class="{
            c1: data.bgIndex == 1,
            c2: data.bgIndex == 2,
            c3: data.bgIndex == 3,
            c4: data.bgIndex == 4,
            c5: data.bgIndex == 5,
          }"
        >
          {{
            data.simScore
              ? (data.simScore.toFixed(4) * 100).toString().substring(0, 5)
              : 0
          }}%
        </div>
      </div>
      <div class="contrast-right">
        <ui-image :src="data.photoUrl"></ui-image>
        <div class="contrast-title">布控照片</div>
      </div>
    </div>
    <div class="box-info">
      <div class="box-info-left">
        <div class="box-wrapper">
          <div class="title">布控目标:</div>
          <div class="box-val w-200">
            <span class="primary" style="font-weight: 700">{{
              data.name || "未知"
            }}</span>
            <span class="info-color" v-show-tips>
              &nbsp;&nbsp;
              <span v-if="isAge">{{ data.age }}岁&nbsp;&nbsp;</span>
              {{ data.idCardNo }}&nbsp;&nbsp;</span
            >
          </div>
        </div>
        <div class="box-wrapper">
          <div class="title">前科类型:</div>
          <div class="box-val error-color" v-show-tips>
            {{ data.bizLabels?.join(",") || "--" }}
          </div>
        </div>
        <div class="box-wrapper">
          <div class="title">报警时间:</div>
          <div class="box-val info-color">{{ data.alarmTime }}</div>
        </div>
        <div class="box-wrapper">
          <div class="title">报警设备:</div>
          <div class="box-val info-color" v-show-tips>
            {{ data.deviceName }}
          </div>
        </div>
      </div>
      <div class="box-info-right">
        <ui-image class="img" :src="typeIcon[data.operationType]"></ui-image>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    isAge: {
      type: Boolean,
      default: false,
    },
    isShowCheckBox: {
      type: Boolean,
      default: true,
    },
  },
  components: {},
  data() {
    return {
      levelImg: {
        1: require("@/assets/img/target/title1.png"),
        2: require("@/assets/img/target/title2.png"),
        3: require("@/assets/img/target/title3.png"),
        4: require("@/assets/img/target/title4.png"),
        5: require("@/assets/img/target/title5.png"),
      },
      percent: {
        1: require("@/assets/img/target/c-one.png"),
        2: require("@/assets/img/target/c-two.png"),
        3: require("@/assets/img/target/c-three.png"),
        4: require("@/assets/img/target/c-four.png"),
        5: require("@/assets/img/target/c-five.png"),
      },
      typeIcon: {
        0: require("@/assets/img/target/unproces.png"),
        1: require("@/assets/img/target/valid.png"),
        2: require("@/assets/img/target/invalid.png"),
      },
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    // 收藏
    collection(data, flag) {
      var param = {
        favoriteObjectId: data.alarmTopId,
        favoriteObjectType: 14,
      };
      this.$emit("collection", param, flag);
    },
    detailFn(row) {
      this.$emit("personnelAlarm", row);
    },
  },
};
</script>

<style lang="less" scoped>
@import "style/alarm";
.check-box {
  position: absolute;
  top: 0;
  left: 10px;
}
.personnel-alarm {
  box-shadow: 0 1px 3px #d9d9d9;
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #d3d7de;
  width: 340px;
  height: 100%;
  background: #f9f9f9;
  cursor: pointer;
  .contrast {
    height: 40.65%;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .contrast-left {
      width: 100px;
      position: relative;
      border: 1px solid #d3d7de;
    }
    .contrast-right {
      width: 100px;
      position: relative;
      border: 1px solid #d3d7de;
    }
    /deep/ img {
      width: auto;
      max-height: 100%;
    }
    .contrast-title {
      position: absolute;
      top: 0px;
      left: 0px;
      height: 18px;
      width: 64px;
      background: rgba(0, 0, 0, 0.45);
      border-radius: 0px 0px 8px 0px;
      font-size: 12px;
      color: #fff;
      text-align: center;
    }
    .contrast-content {
      position: relative;
    }
    .num {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      align-items: center;
      display: flex;
      justify-content: center;
      color: #2c86f8;
    }
    .c1 {
      color: #ea4a36;
    }
    .c2 {
      color: #e77811;
    }
    .c3 {
      color: #ee9f00;
    }
    .c4 {
      color: #36be7f;
    }
    .c5 {
      color: #2c86f8;
    }
  }
  .box-info {
    display: flex;
    padding: 6px 12px;
    position: relative;
    .box-info-left {
      flex: 1;
      width: 0;
    }
    .box-info-right {
      position: absolute;
      right: 0;
      bottom: 11px;
      flex: auto;
      top: 10px;
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      /deep/ .ui-image-div {
        border: none;
      }
    }
  }
}
.collection {
  /deep/ .icon-shoucang {
    color: #888888 !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}
.w-200 {
  width: 200px !important;
}
.error-color {
  color: #ea4a36 !important;
}
</style>
