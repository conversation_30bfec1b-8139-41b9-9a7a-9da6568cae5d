<template>
  <div class="simple-form" :id="id">
    <div class="edit-form-header">
      <slot name="closeBubble"></slot>
    </div>
    <div class="simple-form-content">
      <ui-label class="flex" label="设备编码:" :width="66">
        <div class="msgcontent mr-10px">{{ formValidate.deviceId }}</div>
        <div class="check-status ellipsis" :class="[formValidate.checkStatus === '1' ? 'bg-success' : 'bg-color-grey']">
          离线
        </div>
      </ui-label>
      <ui-label class="flex" label="设备名称:" :width="66">
        <div class="msgcontent ellipsis">{{ formValidate.deviceName }}</div>
      </ui-label>
      <ui-label class="flex" label="所属单位:" :width="66">
        <div class="msgcontent ellipsis">{{ formValidate.orgCode }}</div>
      </ui-label>
      <ui-label class="flex" label="经       度:" :width="66">
        <div class="msgcontent">{{ formValidate.longitude }}</div>
      </ui-label>
      <ui-label class="flex" label="纬       度:" :width="66">
        <div class="msgcontent">{{ formValidate.latitude }}</div>
      </ui-label>
      <ui-label class="flex" label="安装地址:" :width="66">
        <div class="msgcontent ellipsis">{{ formValidate.address }}</div>
      </ui-label>
      <slot name="errType"></slot>
      <div></div>
    </div>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    bubbleData: {
      type: Object,
      default: () => {},
    },
    id: {
      type: String,
    },
  },
  data() {
    return {
      formValidate: {
        deviceId: '',
        deviceName: '',
        orgCode: '',
        longitude: '',
        latitude: '',
        address: '',
        checkStatus: '',
      },
    };
  },
  methods: {
    // 通过设备id查询设备详情
    async getDeviceDetails(id) {
      try {
        let res = await this.$http.get(equipmentassets.getDeviceById + `?id=${id}`);
        return res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    bubbleData: {
      handler() {
        this.$nextTick(async () => {
          const list = await this.getDeviceDetails(this.bubbleData.id);
          Object.keys(this.formValidate).forEach((key) => {
            this.formValidate[key] = list[key];
          });
        });
      },
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
  //justify-content: center;
  //align-items: center;
}
.flex-1 {
  flex: 1;
}
.msgcontent {
  flex: 1;
}
.simple-form {
  position: relative;
  color: #ffffff;
  &-content {
    padding: 15px;
    max-width: 380px;
    min-height: 200px;
  }

  .mr-10px {
    margin-right: 10px;
  }
}
@{_deep} .ivu-tooltip {
  width: 100% !important;
}
</style>
