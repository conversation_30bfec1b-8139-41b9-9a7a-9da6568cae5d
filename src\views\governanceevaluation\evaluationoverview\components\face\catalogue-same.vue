<!--联网车辆/人脸卡口目录一致率（四川）-->
<template>
  <div class="place-accuracy height-full auto-fill">
    <div ref="contentScroll">
      <div class="statistics-container">
        <div class="statistics">
          <icon-statistics :statistics-list="abnormalCount" :isflexfix="false">
            <template #rate="{ row }">
              <span
                class="icon-font position f-14"
                :class="row.qualified === '1' ? 'icon-dabiao warning' : 'icon-budabiao success'"
              ></span>
            </template>
          </icon-statistics>
        </div>
      </div>
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">异常数据列表</span>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="base-text-color mb-sm mt-sm">
        <ui-label class="inline" label="设备编码" :width="70">
          <Input class="width-md" placeholder="请输入设备编码" v-model="searchData.deviceId"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="设备名称" :width="70">
          <Input class="width-md" placeholder="请输入设备名称" v-model="searchData.deviceName"></Input>
        </ui-label>
        <ui-label class="inline ml-lg" label="检测结果" :width="70">
          <Select v-model="searchData.qualified" clearable placeholder="请选择检测结果" class="width-md">
            <Option :value="1">合格</Option>
            <Option :value="2">不合格</Option>
          </Select>
        </ui-label>
        <ui-label :width="70" class="inline" label=" ">
          <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
          <Button class="mr-sm" @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
        </ui-label>
      </div>
    </div>
    <div class="auto-fill">
      <ui-table :table-columns="columns" :table-data="tableData" :loading="loading" class="auto-fill">
        <template #qualified="{ row }">
          <span
            class="tag"
            :style="{
              background: row.qualified === 1 ? '#0E8F0E' : '#BC3C19',
            }"
            >{{ checkStatusList(row.qualified) }}</span
          >
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';

export default {
  mixins: [downLoadTips],
  name: 'face-car-report-rate',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    IconStatistics: require('@/components/icon-statistics').default,
  },
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        qualified: '',
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      faceAbnormalCount: [
        {
          key: 'beanReportedNum',
          name: '已上报视图库人脸卡口总量',
          value: 0,
          icon: 'icon-yishangbaoshitukurenliankakouzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'unReportedNum',
          name: '未上报视图库人脸卡口数量',
          value: 0,
          icon: 'icon-weishangbaoshitukurenliankakouzongliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'rate',
          name: '联网人脸卡口目录一致率',
          value: 0,
          icon: 'icon-lianwangrenliankakoumuluyizhishuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage', // number数字 percentage 百分比
          textColor: 'color3',
        },
      ],
      carAbnormalCount: [
        {
          key: 'beanReportedNum',
          name: '已上报视图库车辆卡口总量',
          value: 0,
          icon: 'icon-yishangbaoshitukucheliangkakouzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'unReportedNum',
          name: '未上报视图库车辆卡口数量',
          value: 0,
          icon: 'icon-weishangbaoshitukucheliangkakouzongliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'rate',
          name: '联网车辆卡口目录一致率',
          value: 0,
          icon: 'icon-lianwangcheliangkakoumuluyizhishuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage', // number数字 percentage 百分比
          textColor: 'color3',
        },
      ],
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
          minWidth: 150,
        },
        { title: '行政区划', key: 'civilName', tooltip: true, minWidth: 150 },
        { title: '检测结果', slot: 'qualified', tooltip: true, minWidth: 150 },
        { title: '错误原因', key: 'reason', tooltip: true, minWidth: 150 },
        { title: '检测时间', key: 'createTime', tooltip: true, minWidth: 150 },
      ],
      tableData: [],
      exportLoading: false,
      statisticalList: {},
      paramsList: {},
      contentClientHeight: 0,
      navbarTitle: '',
    };
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.initList();
        }
      },
      deep: true,
      immediate: true,
    },
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    abnormalCount() {
      let { indexId } = this.paramsList;
      // FACE_CATALOGUE_SAME、FACE_CAPTURE_DATA_CONSISTENCY、
      if ([2019, 2022].includes(indexId)) {
        return this.faceAbnormalCount;
      }
      return this.carAbnormalCount;
    },
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 370 * proportion : 0;
  },
  methods: {
    search() {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.initList();
    },
    statistical() {
      Object.keys(this.statisticalList).map((key) => {
        this.abnormalCount.map((item) => {
          if (key === item.key) {
            item.value = this.statisticalList[key];
          }
        });
      });
    },
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: {
          ...this.searchData,
        },
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            ...this.searchData,
          },
          pageSize: this.pageData.pageSize,
          pageNumber: this.pageData.pageNum,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total || 0;
      } catch (e) {
        // console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 统计参数填充
    checkStatusList(checkStatus) {
      return checkStatus === 1 ? '合格' : '不合格';
    },
  },
};
</script>
<style lang="less" scoped>
.place-accuracy {
  position: relative;
  overflow-y: auto;
  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }
}

.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.statistics-container {
  position: relative;
  width: 100%;
  .statistics {
    margin-top: 10px;
    @{_deep} .information-statistics {
      background: var(--bg-sub-content);
      .statistics-ul {
        padding: 20px 20px 10px 20px;
        .mb-sm {
          flex: 1;
        }
      }
    }
  }

  .information-echart {
    flex: 1;
    height: 252px;
    background: var(--bg-sub-content);
    margin-top: 10px;

    .echarts-box {
      width: 100%;
      height: 100% !important;

      .charts {
        width: 100%;
        height: 100% !important;
      }
    }
  }
}

.abnormal-title {
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(7, 66, 119, 1);

  .color-filter {
    color: rgba(43, 132, 226, 1);
    vertical-align: middle;
  }
}

.success {
  color: #0e8f0e;
}

.warning {
  color: #bc3c19;
}

.position {
  position: absolute;
  right: 10px;
  top: 10px;
}
</style>
