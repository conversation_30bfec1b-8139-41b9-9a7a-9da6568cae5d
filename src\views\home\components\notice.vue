<template>
  <div class="notice-box" v-if="!!noticeList.length">
    <i class="icon-font icon-tongzhi f-14 ml-sm mr-sm"></i>
    <div class="notice-box-div" ref="noticeBoxDivRef" :style="{ width: noticeWidth }">
      <ul
        class="notice-box-ul has-scroll"
        :class="{ 'has-scroll': hasScroll }"
        :style="{ animation: animationConfig }"
        ref="noticeUlRef"
      >
        <li class="notice-box-li pointer inline" v-for="(item, index) of noticeList" :key="index" :title="item.content">
          {{ item.content }}
          <!-- <create-tabs
            style="display: inline;"
            component-name="NoticeDetail"
            tabs-text="公告详情"
            :tabs-query="{ id: item.id }"
            @selectModule="selectModule"
          >
            <span> {{ item.content }}</span>
          </create-tabs> -->
        </li>
      </ul>
    </div>
    <slot class="ml-sm"></slot>
  </div>
</template>
<script>
import systemconfig from '@/config/api/systemconfig';
export default {
  props: {
    width: {
      type: Number,
      default: 680,
    },
  },
  data() {
    return {
      noticeList: [],
      hasScroll: false,
      componentLevel: 0,
      animationConfig: '',
    };
  },
  mounted() {},
  methods: {
    // home/index.vue调用
    async initFunc() {
      await this.getEfficientNotify();
      if (!this.noticeList.length) return;
      this.$nextTick(() => {
        // 获取所有的li总宽度 - 计算滚动距离
        let allLiWidth = 0;
        let liArray = Array.from(this.$refs.noticeUlRef.children);
        let noticeBoxDivWidth = this.$refs.noticeBoxDivRef.offsetWidth;
        liArray.forEach((ele) => {
          allLiWidth = allLiWidth + ele.offsetWidth;
        });
        let noticeUlWidth = allLiWidth + 10 * liArray.length;
        // ul的宽度没有超过外面盒子的宽度就不滚动
        if (noticeUlWidth <= noticeBoxDivWidth) return;
        let copyList = this.$util.common.deepCopy(this.noticeList);
        this.noticeList = [...this.noticeList, ...copyList];
        let cssRules = document.styleSheets[0].cssRules;
        let rules = ` @keyframes noticeMove {
          0% {
            transform: translate(0);
          }
          100% {
            transform: translate(-${noticeUlWidth}px);
          }
          }`;
        if (cssRules.name !== 'noticeMove') {
          document.styleSheets[0].insertRule(rules);
        }
        // 动态设置animation的执行时间
        this.animationConfig = `noticeMove ${parseInt(noticeUlWidth / 25)}s linear 200ms infinite`;
        this.hasScroll = true;
      });
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    async getEfficientNotify() {
      try {
        let params = {
          top: '0',
          status: '0',
          pageNumber: 1,
          pageSize: 10000,
        };
        let { data } = await this.$http.post(systemconfig.notifyPageList, params);
        this.noticeList = data.data.entities || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
  computed: {
    noticeWidth() {
      return this.width / parseFloat(document.documentElement.style.fontSize) + 'rem';
    },
  },
  watch: {
    // $route: 'getParams',
  },
  components: {
    // CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.notice-box {
  // background-image: linear-gradient(to right, rgba(22, 64, 108, 1), rgba(22, 64, 108, 0));
  color: #eb825a;
  display: flex;
  height: 35px;
  line-height: 35px;
  .notice-box-div {
    overflow: hidden;
  }
  .notice-box-ul {
    display: flex;
    width: 10000%;
    .notice-box-li {
      margin-right: 10px;
    }
    &.has-scroll {
      &:hover {
        animation-play-state: paused !important;
      }
    }
  }
}
</style>
