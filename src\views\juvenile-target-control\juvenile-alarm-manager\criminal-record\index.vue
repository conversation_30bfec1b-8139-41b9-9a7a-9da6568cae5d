<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:20:26
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\criminal-record\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="queryInfo"
      @reset="reset"
      type="alarm"
      :compareType="compareType"
      :radioList="radioList"
      :taskList="taskList"
    >
      <query ref="slotQuery" />
    </searchForm>
    <!-- 操作栏 -->
    <div class="operate">
      <Checkbox
        v-model="allCheckStatus"
        :indeterminate="indeterminate"
        @click.native="allChecked()"
        >全选</Checkbox
      >
      <Dropdown style="margin-left: 20px" @on-click="batchStatus">
        <Button type="primary">
          批量处理
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="1">设为有效</DropdownItem>
          <DropdownItem :name="2">设为无效</DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
    <!-- 列表 -->
    <div class="list">
      <div class="list-box" v-for="(item, index) in queryList" :key="item.id">
        <JuvenileAlarm
          class="list-item"
          isShowCheckBox
          :data="item"
          :isAge="$route.path.includes('juvenile')"
          @click.native="goDetailInfo(item, index)"
        ></JuvenileAlarm>
      </div>

      <ui-empty v-if="queryList.length === 0"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <AlarmDetail
      v-if="detailShow"
      :tableList="queryList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      :isAge="$route.path.includes('juvenile')"
      :compareType="compareType"
      ref="alarmDetail"
      @close="close"
    ></AlarmDetail>
  </div>
</template>

<script>
import searchForm from "../../components/search-form.vue";
import JuvenileAlarm from "@/views/juvenile/components/collect/juvenileAlarm.vue";
import AlarmDetail from "@/views/juvenile/components/detail/alarm-detail.vue";
import {
  getFaceAlarmPageList,
  getTaskPageList,
} from "@/api/monographic/juvenile.js";
import { batchHandle } from "@/api/target-control";
import { mapGetters } from "vuex";
import { deepCopy } from "@/util/modules/common";
import query from "./components/query.vue";
export default {
  name: "criminalRecord",
  components: { JuvenileAlarm, AlarmDetail, searchForm, query },
  props: {
    // 1 人员报警
    compareType: {
      type: [String, Number],
      default: 1,
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
    },
  },
  data() {
    return {
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      queryList: [],
      loading: false,
      allCheckStatus: false,
      indeterminate: false,
      alarmConfigInfo: {},
      alarmInfo: {},
      detailShow: false,
      tableIndex: -1,
      taskList: [],
    };
  },
  watch: {
    queryList: {
      handler(val) {
        let checkNum = val.filter((item) => item.checked).length;
        let tableNum = val.length;

        if (checkNum == tableNum && checkNum.length != 0) {
          this.allCheckStatus = true;
          this.indeterminate = false;
        } else {
          if (checkNum == 0) {
            this.allCheckStatus = false;
            this.indeterminate = false;
          } else {
            this.allCheckStatus = true;
            this.indeterminate = true;
          }
        }
      },
      deep: true,
      // immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      targetObj: "systemParam/targetObj",
    }),
  },
  async mounted() {
    await getTaskPageList({ compareType: this.compareType }).then((res) => {
      this.taskList = res.data.entities;
    });
    this.queryInfo({});
  },
  methods: {
    /**
     * @description: 全选 | 全不选
     * @return {*}
     */
    allChecked() {
      this.queryList.forEach((item) => {
        item.checked = !this.allCheckStatus;
      });
    },

    /**
     * @description: 批量设置状态
     * @param {number} name 状态
     */
    batchStatus(name) {
      let list = this.queryList.filter((item) => item.checked);
      let arr = list.map((item) => {
        return { alarmTime: item.alarmTime, alarmTopId: item.alarmTopId };
      });
      let param = {
        alarmRecordSimpleForms: arr,
        operationType: name,
      };
      batchHandle(param).then((res) => {
        this.$Message.success(res.data);
        this.queryInfo();
      });
    },
    queryInfo(param = {}) {
      this.loading = true;
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
        compareType: this.compareType,
        ...param,
      };
      // 这些字段不需要删掉 避免有影响
      delete data.endAlarmTime;
      delete data.startAlarmTime;
      delete data.minActiveScore;
      delete data.minCount;
      delete data.minCount;
      delete data.idCard;
      if (data.simScore > 1) {
        data.simScore = data.simScore / 100;
      }
      getFaceAlarmPageList(data)
        .then((res) => {
          this.total = res.data.total;
          this.queryList = res?.data?.entities || [];
          this.queryList.forEach((item) => {
            this.$set(item, "checked", false);
            let info = this.targetObj.alarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 详细信息
    goDetailInfo(item, index) {
      this.alarmInfo = deepCopy(item);
      this.tableIndex = index;
      this.detailShow = true;
    },
    close() {
      this.detailShow = false;
    },
    reset() {
      this.$refs.slotQuery.reset();
      this.queryInfo();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.queryInfo();
    },
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.queryInfo();
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
  }
  .list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: scroll;
    align-content: flex-start;
    position: relative;
    .list-box {
      width: ~"calc(20% - 10px)";
      height: 260px;
    }
    .list-item {
      width: 100%;
    }
  }
}
</style>
