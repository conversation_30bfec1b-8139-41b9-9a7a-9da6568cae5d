<template>
  <div class="problem-container auto-fill">
    <search-module @startSearch="startSearch" @resetSearch="resetSearch"></search-module>
    <section class="flex-row mt-sm search">
      <div class="flex-aic">
        <Checkbox v-model="isCheckAll" :disabled="!tableData.length" @on-change="changeCheckAll">全选</Checkbox>
        <span v-if="isCheckAll || selectData?.length" class="font-color"
          >已选择 <span class="font-warning">{{ isCheckAll ? pageData.totalCount : selectData?.length }}</span> 条</span
        >
      </div>

      <Button type="primary" @click="handleOption('confirm', true)" :loading="buttonLoadings.batchConfirm">
        <i class="icon-font icon-rengongqueren vt-middle mr-sm"></i>
        批量确认
      </Button>
    </section>
    <problem-table
      ref="tableref"
      :isCheckAll="isCheckAll"
      :tableData="tableData"
      :tableLoading="tableLoading"
      @selectTable="selectTable"
      @handleOption="handleOption"
      @handleDelete="handleDelete"
    ></problem-table>
    <div class="page-box" v-if="tableData.length">
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
    <component
      :is="componentName"
      v-model="visible"
      :tableRowObj="tableRowObj"
      :isCheckAll="isCheckAll"
      :selectData="selectData"
      :isBatch="isBatch"
      :searchParams="searchParams"
      :tableData="tableData"
      :dataPageRes="dataPageRes"
      @changeModalVisible="changeModalVisible"
      @update="getFeedbackList"
      @changePageNum="changePage"
    >
    </component>
  </div>
</template>

<script>
import feedbackApi from '@/config/api/feedback';
export default {
  data() {
    return {
      tableData: [],
      tableLoading: false,
      searchParams: {},
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableRowObj: {}, //操作的表格单条对象
      visible: false,
      componentName: '',
      isCheckAll: false,
      isBatch: false,
      selectData: [],
      buttonLoadings: {
        batchConfirm: false,
      },
      dataPageRes: {}, //包含列表上页下页等信息，用于切换确认上下页
    };
  },
  name: 'problemFeedback',
  components: {
    SearchModule: require('./search-module.vue').default,
    ProblemTable: require('./problem-table.vue').default,
    CreateOrder: require('./components/create-order.vue').default,
    ProblemDetail: require('./components/problem-detail.vue').default,
    DisposeFeedback: require('./components/dispose-feeback.vue').default,
    ProblemComfirm: require('./components/problem-confirm.vue').default,
  },
  computed: {},
  methods: {
    //获取反馈列表
    async getFeedbackList(isClearChcekAll = true) {
      const params = {
        ...this.searchParams,
        pageNumber: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
        loginType: 1, //登陆类型 1用户登录 2手机号登录 3警号登录
      };
      this.tableLoading = true;
      this.tableData = [];
      try {
        let { data } = await this.$http.post(feedbackApi.feedbackList, params);
        this.pageData.totalCount = data.data.total;
        this.tableData = data.data.entities;
        this.dataPageRes = data.data;
      } catch (error) {
        console.log(error);
      } finally {
        //是否清空选中内容
        if (isClearChcekAll) {
          this.clearCheckAll();
        }
        this.tableLoading = false;
      }
    },
    //点击查询
    startSearch(searchForm) {
      this.searchParams = searchForm;
      this.getFeedbackList();
    },
    //点击重置
    resetSearch(searchForm) {
      this.searchParams = searchForm;
      this.pageData.pageNum = 1;
      this.getFeedbackList();
    },
    async changePage(val) {
      this.pageData.pageNum = val;
      await this.getFeedbackList(false);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getFeedbackList(false);
    },
    /**
     * 表格事件
     */
    //点击删除
    handleDelete(obj) {
      this.$UiConfirm({
        content: '您要删除这一项，是否确认?',
        title: '警告',
      })
        .then(async () => {
          try {
            let { data } = await this.$http.get(feedbackApi.delFeedback + `?id=${obj.id}`);
            this.$Message.success(data.msg);
          } catch (err) {
            console.log(err);
          } finally {
            //删除后请求数据
            await this.getFeedbackList();
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    /**
     * 处理事件
     * @param {*} optionName 对应事件
     * @param {*} isBatch 是否批量
     * @param {*} params
     */
    handleOption(optionName, isBatch = false, rowObj) {
      const componentMap = {
        confirm: 'problemComfirm',
        deal: 'disposeFeedback',
        view: 'ProblemDetail',
        createOrder: 'CreateOrder'
      };
      this.componentName = componentMap[optionName] || this.componentName;
      this.isBatch = isBatch;
      this.tableRowObj = rowObj || {};
      if (this.isBatch && !this.isCheckAll && !this.selectData.length) {
        //批量但未选中数据
        this.$Message.warning('请选择问题');
        return;
      }
      this.visible = true;
    },
    changeModalVisible(val) {
      this.visible = val;
    },
    //选择全部
    changeCheckAll() {
      this.selectData = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isCheckAll);
        this.$set(item, '_disabled', this.isCheckAll);
        return item;
      });
    },
    //更换搜索条件时清空全选
    clearCheckAll() {
      this.isCheckAll = false;
      this.$refs.tableref?.clearCheckAll();
      this.changeCheckAll();
    },
    selectTable(arr) {
      this.selectData = arr || this.$refs.tableref.selectData;
    },
  },
  async mounted() {
    await this.getFeedbackList();
  },
};
</script>
<style lang="less" scoped>
.problem-container {
  overflow-y: auto;
  height: 100%;
  padding: 20px 20px 0;
  background-color: var(--bg-content);
  .font-color {
    color: var(--color-content);
  }
  .search {
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
  }
}
</style>
