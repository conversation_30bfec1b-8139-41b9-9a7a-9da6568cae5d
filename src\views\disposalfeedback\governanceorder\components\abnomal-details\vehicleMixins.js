import evaluationoverview from '@/config/api/evaluationoverview';
import governancetask from '@/config/api/governancetask';

export default {
  props: {
    editViewAction: {
      default: () => {
        return {
          type: 'view',
          row: {},
        };
      },
    },
  },
  methods: {
    //车辆
    async interVehicle(params) {
      try {
        let data = this.returnInterVehicleParams(params);
        const requestUrl =
          this.entryType == 'deviceInfo'
            ? evaluationoverview.getPolyData
            : governancetask.orderVehicleDetailGetPopUpData;
        let res = await this.$http.post(requestUrl, data);
        let entities = res.data.data.entities || [];
        let cardList = entities.map((item, index) => {
          // 不同指标，不同的处理
          this.handlerDataByVehicle(item, index);
          return item;
        });
        return { cardList, total: res.data.data.total };
      } catch (err) {
        console.log(err);
        return { cardList: [], total: 0 };
      }
    },
    returnInterVehicleParams(params) {
      let data = {};
      if (this.entryType == 'deviceInfo') {
        const { indexId, batchId, currentTaskReceiverOrgCode, createOrgCode } = this.editViewAction.row;
        data = {
          indexId: indexId,
          batchId: batchId,
          displayType: 'ORG',
          orgRegionCode: currentTaskReceiverOrgCode || createOrgCode,
          ...params,
          customParameters: {
            ...params.customParameters,
            faceDeviceDetailId: '',
          },
        };
      }
      if (this.entryType == 'table') {
        const { id, deviceId } = this.entryRowObj;
        data = {
          workOrderFlowId: id,
          deviceId: deviceId,
          pageNumber: params.pageNumber,
          pageSize: params.pageSize,
          outcome: params.customParameters.outcome || '2',
          causeErrors: params.customParameters.causeErrors || [],
        };
      }
      return data;
    },
    // 根据 各自的 评测结果页面复制，确保 逻辑一致
    handlerDataByVehicle(item, index) {
      let { indexId } = this.editViewAction.row;
      let tipColor = item.outcome !== '1' ? '#bc3c19' : '#0e8f0e';
      item.imageUrl = item.imageUrl || item.smallImagePath; //车辆小图，可能会出现没有imageUrl的情况
      switch (indexId) {
        case '3010': // 车辆卡口设备过车图片地址可用率
        case '3011': // 重点车辆卡口设备过车图片地址可用率
          item._index = index;
          // 处理图片模式字段展示
          this.$set(item, 'fileList', [
            { value: item.shotTime, iconName: 'icon-shijian' },
            { value: item.address, iconName: 'icon-dizhi' },
            { value: item.reason, iconName: 'icon-yichangyuanyin', style: { color: tipColor } },
          ]);
          // 重点车辆卡口设备过车图片地址可用率 不显示“访问时间”
          if (item.indexId == 3010) {
            item.fileList = [
              ...item.fileList,
              {
                render: () => {
                  return `${item.imageUrlVisitTime || 0}毫秒`;
                },
                iconName: 'icon-jishi-time',
              },
            ];
          }
          break;
        case '3008': // 车辆卡口设备及时上传率
        case '3009': // 重点车辆卡口设备及时上传率
          this.$set(item, 'fileList', [
            { label: '抓拍:', value: item.shotTime },
            { label: '接收:', value: item.firstIntoViewTime },
            { label: '', value: item.reason, style: { color: tipColor } },
          ]);
          break;
        case '3005': // 车辆卡口设备时钟准确率
          tipColor = item.outcome === '1' ? '#0e8f0e' : '#bc3c19';
          this.$set(item, 'fileList', [
            { label: '抓拍：', value: item.shotTime || '缺失' },
            { label: '接收：', value: item.firstIntoViewTime || '缺失' },
            { label: '', value: item.timeAccuracyDesc, style: { color: tipColor } },
          ]);
          break;
      }
    },
  },
};
