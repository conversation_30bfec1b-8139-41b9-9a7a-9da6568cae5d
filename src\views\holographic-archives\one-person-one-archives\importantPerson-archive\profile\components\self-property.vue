<template>
  <ui-card :title="title" class="self-property-card">
    <div class="self-property-box">
      <div v-for="(item, index) in list" :key="index" class="card-item">
        <UiListCard
          type="propertyCard"
          :showBar="false"
          :data="item"
          @archivesDetailHandle="archivesDetailHandle(item)"
        />
      </div>
      <ui-loading v-if="loading" />
      <ui-empty v-if="(!list || !list.length) && !loading" />
    </div>
  </ui-card>
</template>

<script>
import UiListCard from "@/components/ui-list-card";
export default {
  name: "SelfProperty",
  components: { UiListCard },
  props: {
    title: {
      type: String,
      default: "名下资产",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  methods: {
    archivesDetailHandle(row) {
      let query = {
        archiveNo: JSON.stringify(row.archiveNo),
        plateNo: JSON.stringify(row.plateNo),
        source: "car",
        idcardNo: row.idcardNo,
      };
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query,
      });
      // 防止因为Anchor锚点导致的路由query参数丢失
      sessionStorage.setItem("query", JSON.stringify(query));
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.self-property-card {
  height: 250px;
  .self-property-box {
    width: 100%;
    overflow: auto;
    display: flex;
    gap: 10px;
  }
}
</style>
