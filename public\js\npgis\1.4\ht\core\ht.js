!function(G,<PERSON>,j){"use strict";var k="ht";if(!G[k]){!function(){for(var A=0,V=["ms","moz","webkit","o"],T=0;T<V.length&&!G.requestAnimationFrame;++T)G.requestAnimationFrame=G[V[T]+"RequestAnimationFrame"],G.cancelAnimationFrame=G[V[T]+"CancelAnimationFrame"]||G[V[T]+"CancelRequestAnimationFrame"];G.requestAnimationFrame||(G.requestAnimationFrame=function(H){var k=(new Date).getTime(),b=Math.max(0,16-(k-A)),n=G.setTimeout(function(){H(k+b)},b);return A=k+b,n}),G.cancelAnimationFrame||(G.cancelAnimationFrame=function(X){E(X)})}();var A,W,e,s,$=G.document,f=null,y=G[k]={},L=G[k+"config"],u=y.Default=L&&L.Default||{},R=y.Style=L&&L.Style||{},I=y.Color=L&&L.Color||{},Y=y.IsGetter=L&&L.IsGetter||{},B=Math,K=B.round,T=B.floor,a=B.ceil,F=B.sqrt,b=B.max,O=B.min,J=B.abs,N=B.cos,H=B.acos,n=B.sin,l=B.pow,X=B.asin,p=B.PI,z=2*p,v=p/2,i=B.tan,q=B.atan2,m=B.random,c=G.parseInt||global.parseInt,V="2015-10-01",x=function(Y){return Y*Y},g=G.setTimeout,E=G.clearTimeout,C=G.navigator?G.navigator.userAgent.toLowerCase():"",D=function(G){return G.test(C)},h=D(/msie/)||D(/trident/),d=D(/msie 10/),t=D(/firefox/),P=D(/mac/),w=u.isTouchable===j?$?"ontouchend"in $:!1:u.isTouchable,r="default",U="single",Z="multiple",Q="front",o="back",S="left",_="right",xi="top",Kh="bottom",Hb="center",Xd="eye",Ch="middle",Cr="east",Uk="west",Up="north",Wd="none",Yl="px",Dq="absolute",nc="border",Uh="triangle",ik="rect",mo="circle",We="cylinder",xh="shape",fh="items",Pr="normal",Yi="remove",on="clear",Le="width",Em="height",Rm="ingroup",rc="check",eh="uncheck",_o="radio",jg="radioOn",jj="radioOff",Tq="points",ld="values",Lj="series",ss="body",Jm="label",Ve="label2",Rg="note",Mi="note2",Hq="icons",id="labelFont",bg="labelColor",Jf="labelSelectColor",Id="note.expanded",_r="note2.expanded",oe="edge.expanded",pg="edge.points",hq="edge.type",Qg="rotation",we="getRotation",Ip="setRotation",$n="hidden",$g="visible",ts="tuv",Aj="no",gb="select",Tg="currentSubGraph",Zq="selectBackground",ic="autoMakeVisible",cn="autoHideScrollBar",zs="scrollBarColor",kd="scrollBarSize",Pp="indent",ve="rowHeight",le="columnLineColor",Cq="rowLineColor",po="columnLineVisible",He="rowLineVisible",bp="visibleFunc",Oh="expandIcon",gg="collapseIcon",fp="checkMode",An="sortFunc",Xq="editable",Ce="batchEditable",ii="tristate",Ec="asc",Gk="desc",Dj="position",_f="elevation",Ck="children",$l="translateX",Cg="translateY",ar="dataModel",of="shape3d",ji="shape3d.resolution",Mf="shape3d.visible",Yg="shape3d.from.visible",Kb="shape3d.to.visible",lf="shape3d.top.visible",hk="shape3d.bottom.visible",_j="repeat.uv.length",Qc="serializeValue",Ei="deserializeValue",Ed="centerUniform",Dm="rgba(255,255,255,0)",Mn="style",Mc="attr",Un="field",Hm="string",_m="boolean",pk="color",hr="int",Gp="number",vp="ew-resize",Gc="ns-resize",Vi="pointer",Qm="auto",eg="mousedown",df="mousemove",sg="mouseup",ds="mouseout",_n="touchstart",Wm="touchmove",wp="touchend",di="keydown",Fc="keyup",ef=t?"DOMMouseScroll":"mousewheel",kf=w?[_n,Wm,wp]:[di,Fc,eg,df,sg,ds,ef,"contextmenu"],Zr=f,Yr=f,fd=f,om=sg.length,Bp=function(){fd&&(E(fd.timeout),fd=f)},Aq=function(){fd&&Kq(fd.e,fd.info)};G.addEventListener&&(w||(G.addEventListener(ds,function(){Bp()},!1),G.addEventListener(ef,function(){mn()},!1)),G.addEventListener(Fc,function($){91===$.keyCode?If={}:delete If[$.keyCode]},!0),G.addEventListener(di,function(v){If[v.keyCode]=!0},!0));var Cd,jn,Yj,Yf=!1,gm=f,Eh=f,ph={},xn={},xk={},mp={},co={},If={},me={},mk={},Pd={},Zm=[],Ln={},hp=f,Ie=f,Zk=function(){},Kg=function(){throw"Oops!"},oh=[0,0,0],Np=[0,0,0,0],Df={x:0,y:0,width:0,height:0},zh=f,Yp=f,Zd=f,Zi=function(m){Yp&&!m._72O&&(Zd||(Zd={}),Zd[m._72O=Xn()]=m),Cd!=f?ho()<.05&&s&&!jn&&(lk=Bp):lk=wc},qd=function(q,M,w,t){Yp||(Yp={});var A=Yp[q];if(A){if(A.url===M)return;A.image.onload=Zk,A.image.onerror=Zk}var L=new Image;Yp[q]={image:L,url:M},L.onload=function(){w&&(L.width=w),t&&(L.height=t),u.handleImageLoaded(q,L),Lh(q,L)},L.onerror=function(){Lh(q,u.handleUnfoundImage(q,M)||f)},L.src=M},Lh=function(K,Q){if(xn[K]=Q,delete Yp[K],vj(Yp)&&(Yp=f,Zd)){for(var V in Zd){var v=Zd[V];v.invalidateAll&&v.invalidateAll(),v.redraw&&v.redraw(),v.iv(),delete v._72O}Zd=f}if(Q&&Zd)for(var V in Zd){var v=Zd[V];v.invalidateAll&&v.invalidateAll(),v.redraw&&v.redraw(),v.iv()}},vb=function(){return G.performance&&G.performance.now?G.performance.now():Date.now()},yq=function(V,N){N?E(V):G.cancelAnimationFrame(V)},Gh=function(w,z,N){return{width:w,height:z,comps:Ji(N)?N:[N]}},Xe=function(K,h){return{type:mo,rect:[K,h,1.6,1.6],borderWidth:1,borderColor:_e,gradient:Qe,gradientColor:En,background:_e}},hh=function($,E){return Gh(16,16,{type:Uh,rect:[4,4,10,8],background:$,rotation:E?1.57:3.14})},bd=function(v,X){return Gh(16,16,{type:Uh,rect:[4,4,8,7],background:v,rotation:X?3.14:0})},Ci=function(k){var W=k._orientation;return"horizontal"===W||"h"===W},vd=function(A){y.z=W=A},Sm=function(E){var L=2,X=0;for(var k in E)k.length===L&&c(k,32)===ur&&(hp=Ie=E[k]),X++;return X},Io=function(Q,y,C){return C?{x:Q-C,y:y-C,width:2*C+1,height:2*C+1}:w?{x:Q-5,y:y-5,width:11,height:11}:{x:Q-1,y:y-1,width:3,height:3}},dd=function(l){return(/ble$/.test(l)||/ed$/.test(l)||Y[l]?"is":"get")+l.charAt(0).toUpperCase()+l.slice(1)},Rp=function(n){return"set"+n.charAt(0).toUpperCase()+n.slice(1)},sl=function(v){return typeof v===Hm||v instanceof String},sh=function(k){return typeof k===Gp},wc=function(M){return typeof M===_m},ze=function(H){return H&&"object"==typeof H},ll=function(G){return"function"==typeof G},Ji=function(x){return x instanceof Array},El=function(W){return W instanceof Xc},Sc=function(J){return Ji(J)?new Xc(J):J},Qr=function(D){return D instanceof Ir},oo=function(s){return s instanceof gk},sj=function(z){return z instanceof Jj},Ok=function(u){return u&&"IFRAME"===u.tagName},pm=function(P){return P==f?P:parseFloat(P.toFixed(2))},ul=function(d,I,G){var w,n=I.length;if(G)for(var j=0;G>j;j++)if(3===n)d.push(I[0]),d.push(I[1]),d.push(I[2]);else for(w=0;n>w;w++)d.push(I[w]);else if(3===n)d.push(I[0]),d.push(I[1]),d.push(I[2]);else for(w=0;n>w;w++)d.push(I[w])},Bq=function(g){return g?ze(g)?g:{}:!1},Ym=function(E,G,J){var $,L=ze(E)?E:E.prototype;for($ in G)J&&L[$]!==j||(L[$]=G[$]);return E},kk=function(k){return String.fromCharCode(k)},zq=function(d){for(var K,G=0,u="";G<d.length;G++)K=d[d.length-1-G],"%"===K?K="'":"a"===K?K='"':"]"===K&&(K="\\"),u+=kk(K.charCodeAt(0)-1);return u},Wn=function(d,D,L){d.superClass.constructor.apply(D,L)},vj=function(k){for(var D in k)return!1;return!0},Ur=function(n){return n?0===n.length:!0},nn=function(S,X){return S.x===X.x&&S.y===X.y&&S.width===X.width&&S.height===X.height},Wo=function(X,A,H){return A>X?A:X>H?H:X},ho=function(){var h=1e4*n(om++);return h-T(h)},bk=function(e,V,z){return Qd(e.x,e.y,V.x,V.y,z.x,z.y,z.x+z.width,z.y,!0)||Qd(e.x,e.y,V.x,V.y,z.x+z.width,z.y,z.x+z.width,z.y+z.height,!0)||Qd(e.x,e.y,V.x,V.y,z.x+z.width,z.y+z.height,z.x,z.y+z.height,!0)||Qd(e.x,e.y,V.x,V.y,z.x,z.y+z.height,z.x,z.y,!0)},Qd=function(s,n,F,D,d,V,m,Y,M){var A=(m-d)*(n-V)-(Y-V)*(s-d),q=(Y-V)*(F-s)-(m-d)*(D-n);if(0!==q){var J=A/q,Z=s+J*(F-s),r=n+J*(D-n);return M&&(Z+ne<O(s,F)||Z-ne>b(s,F)||Z+ne<O(d,m)||Z-ne>b(d,m)||r+ne<O(n,D)||r-ne>b(n,D)||r+ne<O(V,Y)||r-ne>b(V,Y))?f:[Z,r]}return f},ym=function(S,B,U){if(S&&U)if(B){if(B===Mn)return S.getStyle(U);if(B===Mc)return S.getAttr(U);if(B===Un)return S[U]}else if(U=dd(U),S[U])return S[U]();return j},$o=function(i,r,B,X){if(i&&B)if(r){if(r===Mn)i.s(B,X);else if(r===Mc)i.a(B,X);else if(r===Un){var K=i[B];i[B]=X,i.fp("f:"+B,K,X)}}else B=Rp(B),i[B]&&i[B](X)},um=function($,Y,r,V,J){Y&&$i($,r,V,1,J,Y)},wi=function(N,J,P,I){var U=1-N;return U*U*J+2*N*U*P+N*N*I},Bd=function(Y,t,_,q,n){var J=1-Y;return J*J*J*t+3*J*J*Y*_+3*J*Y*Y*q+Y*Y*Y*n},vf=function(B){var w,p,i,l,H=0;return B.forEach(function(E){if(w=E.length,w>0)for(p=E[0],l=1;w>l;l++)i=E[l],H+=nm(p,i),p=i}),H},Ib=function(e,l,Z){var u="__"+e,v=function(w){Z["handle_"+e](w)};Z[u]||(Z[u]=v,l.addEventListener(e,v,!1))},Vk=function(E,r,m){var O="__"+E,A=m[O];A&&(r.removeEventListener(E,A,!1),delete m[O])},hb=function(i,Q){var C="_"+Q;i[dd(Q)]=function(){return this[C]},i[Rp(Q)]=function(D){var m=this[C];this[C]=D,this.fp(Q,m,D)}},es=function(P){return M.create(P)},Oj=function(E){if(!E.element){var d,D;(d=E.textField)?D=vk(bn.TextField,d):(d=E.textArea)?D=vk(bn.TextArea,d):(d=E.button)?D=vk(bn.Button,d):(d=E.comboBox)?D=vk(bn.ComboBox,d):(d=E.checkBox)?D=vk(bn.CheckBox,d):(d=E.radioButton)?D=vk(bn.RadioButton,d):(d=E.slider)?D=vk(bn.Slider,d):(d=E.colorPicker)?D=vk(bn.ColorPicker,d):(d=E.image)&&(D=vk(bn.Image,d)),D&&(E.element=D)}},Xb=function(I){var C=$.createElement("div"),i=C.style;return C.tabIndex=-1,C.onkeydown=np,i.msTouchAction=Wd,i.cursor=r,Kp(C,f,0),w&&i.setProperty("-webkit-tap-highlight-color","rgba(0, 0, 0, 0)",f),I&&(i.overflow=$n),C},Pf=function(D){var q=$.createElement("canvas"),_=q.style;return _.msTouchAction=Wd,_.pointerEvents=Wd,Kp(q,f,0),D&&nj(D,q),q},Kp=function(h,x,b){var c=h.style;c.border=x?x+" solid 1px":0,c.outline=0,c.padding=b?"0 "+b+Yl:0,he(h)},he=function(p){var W=p.style;W.position=Dq,W.margin=0,W.setProperty("box-sizing","border-box",f),W.setProperty("-moz-box-sizing","border-box",f)},Gr=function(l,D,A,g){g||(g=ql),D!=f&&(l.width=D*g,l.style.width=D+Yl),A!=f&&(l.height=A*g,l.style.height=A+Yl)},nj=function(A,k,K){A.appendChild(k),K&&(k.style.position=Dq)},Xl=function(F,L){L.split||(L+="");for(var Q,J=L.split("\n"),s=0,I=J.length,B=0;I>B;B++){var e=Hn(F.font,J[B]);e.width>s&&(s=e.width),Q||(Q=e.height)}return F.ss=J,{width:s,height:Q*I}},Lr=function(D,L,B,i,m,a){var V=L.length;if(1===V)Gm(D,L[0],B,i,m);else for(var X=B.height/V,R={x:B.x,y:B.y,width:B.width,height:X},l=0;l<L.length;l++)a?tj(D,L[l],i,m,R.x,R.y,R.width,R.height,a):Gm(D,L[l],R,i,m),R.y+=X},Gm=function(V,I,q,k,x){V.font=k?k:Yh,V.fillStyle=x?x:Ol,V.textAlign=Hb,V.textBaseline=Ch;var o,n;q?q.width===j?(o=q.x,n=q.y):(o=q.x+q.width/2,n=q.y+q.height/2):(o=0,n=0),V.fillText(I,K(o),K(n))},Fh=function(D){D.getView&&(D=D.getView());var y=D.offsetWidth||D.scrollWidth;return!y&&D.style.width&&(y=c(D.style.width)),y},Pc=function(d){d.getView&&(d=d.getView());var j=d.offsetHeight||d.scrollHeight;return!j&&d.style.height&&(j=c(d.style.height)),j},xe=function(){var n=function(O){np(O),O.stopPropagation()},k=w?[_n]:[di,eg,ef];return function(j){var I=Xb(),F=I.style;return F.backgroundColor=u.disabledBackground,j&&(F.backgroundImage="url("+j+")",F.backgroundPosition="50% 50%",F.backgroundRepeat="no-repeat no-repeat"),k.forEach(function(N){I.addEventListener(N,n)}),I}}(),cs=function(R){var u=R.getContext("2d");return u.save(),u.lineCap=Tj,u.lineJoin=Xh,u},lk=function(k,o,f,s){On(k,o*ql,f*ql),s*=ql,1!==s&&k.scale(s,s)},On=function(b,f,z){b.translate(f,z)},qr=function(t,s){t.rotate(s)},ub=function(x){if($.activeElement!==x)if(w)x.focus();else{var d=Wl(),i=d.target;x.focus(),i.scrollLeft=d.left,i.scrollTop=d.top}},aq=function(e){return e&&e.getView?e.getView():e},tf=function(h,S,A,U,d){2===arguments.length&&(A=S.y,U=S.width,d=S.height,S=S.x);var q=aq(h),g=q.style;($.fullscreenElement||$.mozFullScreenElement||$.webkitFullscreenElement||$.msFullscreenElement)!==q&&(S!==j&&(g.left=S+Yl),A!==j&&(g.top=A+Yl),U!==j&&(g.width=U+Yl),d!==j&&(g.height=d+Yl)),h.endEditing&&!w&&h.endEditing(),h.redraw&&h.redraw(),h.invalidate&&h.invalidate(),h.onLayouted&&h.onLayouted(S,A,U,d),h._41O&&h._41O("layout")},Ae=function(T){var m=T.touches[0];return m?m:T.changedTouches[0]},xf=function(e){u.popup&&u.popup.close(),u.popup=e},Bl=f,Wr=function(m){Bl.handleWindowTouchMove(m)},Ak=function(N){Bl.handleWindowTouchEnd(N),G.removeEventListener(Wm,Wr,!1),G.removeEventListener(wp,Ak,!1),Bl=f},Zb=function(y){Bl.handleWindowMouseMove(y)},Nb=function(_){Bl.handleWindowMouseUp(_),G.removeEventListener(df,Zb,!1),G.removeEventListener(sg,Nb,!1),Bl=f},bo=function(K){return 1===nq(K)},Qb=function(x,i){return i?i.keyCode===x:If[x]},ui=function(n){return Sq(n)&&Qb(65,n)},un=function(C){return Qb(46,C)||Qb(8,C)},Xo=function(m){return function(u){return u?u.keyCode===m:If[m]}},ur=573,_g=[65,83,68,87,37,38,39,40,32,13,27],ij=Xo(_g[0]),ac=Xo(_g[1]),Zo=Xo(_g[2]),si=Xo(_g[3]),qn=Xo(_g[4]),so=Xo(_g[5]),zk=Xo(_g[6]),uq=Xo(_g[7]),td=Xo(_g[8]),Kn=Xo(_g[9]),dn=Xo(_g[10]),Zj={65:1,83:1,68:1,87:1,37:1,38:1,39:1,40:1},oq=[2,2,2,2,2,2,2,2,2,2,3,3,3,5,5,5,5,5,7,7,11,31],se=f,fg=f,Ij=function(L,e){se||(se=Pf()),Gr(se,L,e,1);var I=cs(se);return I.clearRect(0,0,L,e),I},xd=function(){var s,v={};return function(p){var T,Q=v[p];return Q||(s||(s=Pf(),Gr(s,1,1,1)),T=s.getContext("2d"),T.clearRect(0,0,1,1),$i(T,0,0,1,1,p),Q=T.getImageData(0,0,1,1).data,Q=v[p]=[Q[0],Q[1],Q[2],Q[3]]),Q}}(),Yd=function(h){if(!sl(h))return h;var r=xd(h);return r.CA||(r.CA=[r[0]/255,r[1]/255,r[2]/255,r[3]/255]),r.CA},Mm=function(Z,l,X,d){var _=Pf();_.width=X,_.height=d;var m=_.getContext("2d");m.drawImage(Z,0,0,X,d);try{for(var J=m.getImageData(0,0,X,d),f=J.data,I=0,u=f.length;u>I;I+=4){var y=f[I+0],q=f[I+1],C=f[I+2];f[I+0]=l[0]*y,f[I+1]=l[1]*q,f[I+2]=l[2]*C}m.putImageData(J,0,0)}catch(i){return Z}return _},Hh=function(i,z,X){return z&&(z="miter"===X?8*z+20:z+1,i&&In(i,z)),z},zf=function(I,Y){if(Y){var s=new pf(Y),D=I.width/2,P=I.height/2,r=ai([s.tf(-D,-P),s.tf(D,-P),s.tf(D,P),s.tf(-D,P)]);return r.x+=I.x+D,r.y+=I.y+P,r}return I},qh=function(w,r,k,U){var p=!1;if(w.beginPath?w.beginPath():p=!0,El(r)&&(r=r._as),El(k)&&(k=k._as),k&&k.length){for(var y,v,n,q,Z=0,D=0,A=k.length;A>D;D++)y=k[D],1===y?(v=r[Z++],w.moveTo(v.x,p?-v.y:v.y)):2===y?(v=r[Z++],w.lineTo(v.x,p?-v.y:v.y)):3===y?(v=r[Z++],n=r[Z++],w.quadraticCurveTo(v.x,p?-v.y:v.y,n.x,p?-n.y:n.y)):4===y?(v=r[Z++],n=r[Z++],q=r[Z++],w.bezierCurveTo(v.x,p?-v.y:v.y,n.x,p?-n.y:n.y,q.x,p?-q.y:q.y)):5===y&&w.closePath();U&&5!==y&&w.closePath()}else{var P,T,d,b=r.length;if(b>0){for(P=r[0],w.moveTo(P.x,p?-P.y:P.y),T=1;b>T;T++)d=r[T],w.lineTo(d.x,p?-d.y:d.y);U&&w.closePath()}}},Rr=function(U,A,j,v){if(El(U)&&(U=U._as),El(A)&&(A=A._as),A&&A.length){j=j||rq;for(var d,R,T,E,Z,M,k=[],W=f,n=0,K=0,S=A.length;S>K;K++)if(d=A[K],1===d)k.push(W=[]),W.push(U[n++]);else if(2===d)W.push(U[n++]);else if(3===d)for(R=W[W.length-1],T=U[n++],E=U[n++],M=1;j>=M;M++)W.push({x:wi(M/j,R.x,T.x,E.x),y:wi(M/j,R.y,T.y,E.y)});else if(4===d)for(R=W[W.length-1],T=U[n++],E=U[n++],Z=U[n++],M=1;j>=M;M++)W.push({x:Bd(M/j,R.x,T.x,E.x,Z.x),y:Bd(M/j,R.y,T.y,E.y,Z.y)});else 5===d&&W.push(W[0]);return v&&5!==d&&W&&W.length>2&&W.push(W[0]),k}return v&&U.length>2&&(U=U.slice(),U.push(U[0])),U?[U]:[]},Xj=function(H,q,W,s,y,Q){var w,B,L,f=xd(q),i=s,b=f[0],V=f[1],c=f[2];if(W){var p=xd(W);w=p[0]-b,B=p[1]-V,L=p[2]-c}else w=255-b,B=255-V,L=255-c;for(Q||(Q=s>10?1:.5,Q=1>y?O(Q/y,2):Q);(s-=Q)>0;){var e=1-s/i,d=b+w*e,_=V+B*e,N=c+L*e;d=O(a(d),255),_=O(a(_),255),N=O(a(N),255),H.strokeStyle="rgb("+d+","+_+","+N+")",H.lineWidth=s,H.stroke()}},fi=function(){var T={};return function(y,i){var k=y+"-"+i,M=T[k];if(M)return M;var Y,q,I,g=xd(y);return 0>i?(i=(100+i)/100,Y=a(g[0]*i),q=a(g[1]*i),I=a(g[2]*i)):(i/=100,Y=g[0],q=g[1],I=g[2],Y+=(255-Y)*i,q+=(255-q)*i,I+=(255-I)*i,Y=O(a(Y),255),q=O(a(q),255),I=O(a(I),255)),T[k]="rgb("+Y+","+q+","+I+")"}}(),$i=function(x,A,$,z,S,P){P&&(x.fillStyle=P),x.beginPath(),x.rect(A,$,z,S),x.fill()},Zn=function(r,F,M,b,t,i,G){r.beginPath(),jh[F](r,M.x,M.y,M.width,M.height,b,t,i,G)},Yk=function(S,i,v,o,U){var A=jh[v];S.fillStyle=A?A(S,i,o?o:En,U.x,U.y,U.width,U.height):i},pc=function(b,W){b.fillStyle=b.createPattern(W,"repeat")},uo=function(x,X,H,V,i,c,I,b){var d,u,t,K,e,q,w,h,Q,W,o;if(J(i)>2*p&&(i=2*p),e=a(J(i)/(p/4)),d=i/e,u=-d,t=-V,e>0){q=X+N(V)*c,w=H+n(-V)*I,b?x.lineTo(q,w):x.moveTo(q,w);for(var y=0;e>y;y++)t+=u,K=t-u/2,h=X+N(t)*c,Q=H+n(t)*I,W=X+N(K)*(c/N(u/2)),o=H+n(K)*(I/N(u/2)),x.quadraticCurveTo(W,o,h,Q)}},nd=function(v,E,I,P,l,m,f,n,M){6===arguments.length&&(f=m,n=m,M=m);var e=E+P,t=I+l,d=l>P?2*P:2*l;m=d>m?m:d,f=d>f?f:d,n=d>n?n:d,M=d>M?M:d;var N=.29*M,$=.58*M;v.moveTo(e,t-M),v.quadraticCurveTo(e,t-$,e-N,t-N),v.quadraticCurveTo(e-$,t,e-M,t),N=.29*n,$=.58*n,v.lineTo(E+n,t),v.quadraticCurveTo(E+$,t,E+N,t-N),v.quadraticCurveTo(E,t-$,E,t-n),N=.29*m,$=.58*m,v.lineTo(E,I+m),v.quadraticCurveTo(E,I+$,E+N,I+N),v.quadraticCurveTo(E+$,I,E+m,I),N=.29*f,$=.58*f,v.lineTo(e-f,I),v.quadraticCurveTo(e-$,I,e-N,I+N),v.quadraticCurveTo(e,I+$,e,I+f),v.lineTo(e,t-M)},mb=function(I,v,S,y,O,k,J){v&&(S=K(S),y=K(y),J||(J=1),I.fillStyle=v,I.beginPath(),I.rect(S,y,J,k),I.rect(S,y,O,J),I.rect(S,y+k-J,O,J),I.rect(S+O-J,y,J,k),I.fill())},Um=function(C,w,x,b){var H=b.x,s=b.y,R=b.width,v=b.height;if(!(!w||!x||0>=R||0>=v)){var o,E=Ue(w),i=Fm(w),a=x>0;1===x||-1===x?(C.fillStyle=a?E:i,C.beginPath(),C.rect(H,s,1,v),C.rect(H,s,R,1),C.fill(),C.fillStyle=a?i:E,C.beginPath(),C.rect(H,s+v-1,R,1),C.rect(H+R-1,s,1,v),C.fill()):(x=O(J(x),O(R/2,v/2)),o=C.createLinearGradient(H,s,H+x,s),o.addColorStop(0,a?E:i),o.addColorStop(1,w),C.fillStyle=o,C.beginPath(),C.moveTo(H,s),C.lineTo(H+x,s+x),C.lineTo(H+x,s+v-x),C.lineTo(H,s+v),C.lineTo(H,s),C.fill(),o=C.createLinearGradient(H,s,H,s+x),o.addColorStop(0,a?E:i),o.addColorStop(1,w),C.fillStyle=o,C.beginPath(),C.moveTo(H,s),C.lineTo(H+x,s+x),C.lineTo(H+R-x,s+x),C.lineTo(H+R,s),C.lineTo(H,s),C.fill(),o=C.createLinearGradient(H,s+v,H,s+v-x),o.addColorStop(0,a?i:E),o.addColorStop(1,w),C.fillStyle=o,C.beginPath(),C.moveTo(H,s+v),C.lineTo(H+x,s+v-x),C.lineTo(H+R-x,s+v-x),C.lineTo(H+R,s+v),C.lineTo(H,s+v),C.fill(),o=C.createLinearGradient(H+R,s,H+R-x,s),o.addColorStop(0,a?i:E),o.addColorStop(1,w),C.fillStyle=o,C.beginPath(),C.moveTo(H+R,s),C.lineTo(H+R-x,s+x),C.lineTo(H+R-x,s+v-x),C.lineTo(H+R,s+v),C.lineTo(H+R,s),C.fill())}},_i=function(Z,f,h,U,d,y,H){var u=Z.createLinearGradient(U,d,y,H);return u.addColorStop(0,h),u.addColorStop(1,f),u},Ek=function(U,f,h,E,k,D,l){var v=U.createLinearGradient(E,k,D,l);return v.addColorStop(0,f),v.addColorStop(.5,h),v.addColorStop(1,f),v},Ho=function(E,D,S,V,W,c,e){var A=E.createLinearGradient(V,W,c,e);return A.addColorStop(0,D),A.addColorStop(1/3,S),A.addColorStop(2/3,D),A.addColorStop(1,S),A},ae=function(V,f,P,G,M,J,c,N,a){var h=V.createRadialGradient(G+J*N,M+c*a,O(J,c)/24,G+J/2,M+c/2,b(J,c)/2);return h.addColorStop(0,P),h.addColorStop(1,f),h},jh={polygon:function(i,A,Y,D,e,E){(E==f||3>E)&&(E=6);for(var x,X,d=O(D,e)/2,B=A+D/2,w=Y+e/2,a=0,u=2*p/E,Q=0;E>Q;Q++)x=B+N(a)*d,X=w+n(a)*d,0===Q?i.moveTo(x,X):i.lineTo(x,X),a+=u;i.closePath()},arc:function(Q,J,G,i,X,q,$,n,B){q==f&&(q=p),$==f&&($=z),n==f&&(n=!0);var e=J+i/2,b=G+X/2;n&&Q.moveTo(e,b),B?(q=-q,$=-$,uo(Q,e,b,q,$-q,i/2,X/2,!0)):Q.arc(e,b,O(i,X)/2,q,$),n&&Q.closePath()},rect:function(o,V,d,k,O){o.rect(V,d,k,O)},circle:function(H,l,N,j,e){H.arc(l+j/2,N+e/2,O(j,e)/2,0,z,!0)},oval:function(k,p,R,j,c){uo(k,p+j/2,R+c/2,0,z,j/2,c/2,!1)},roundRect:function(V,L,N,S,b,_){_==f&&(_=O(O(S,b)/4,8)),nd(V,L,N,S,b,_)},star:function(n,V,P,Z,c){var a=2*Z,S=2*c,W=V+Z/2,N=P+c/2;n.moveTo(W-a/4,N-S/12),n.lineTo(V+.306*Z,P+.579*c),n.lineTo(W-a/6,N+S/4),n.lineTo(V+Z/2,P+.733*c),n.lineTo(W+a/6,N+S/4),n.lineTo(V+.693*Z,P+.579*c),n.lineTo(W+a/4,N-S/12),n.lineTo(V+.611*Z,P+.332*c),n.lineTo(W+0,N-S/4),n.lineTo(V+.388*Z,P+.332*c),n.closePath()},triangle:function(b,y,F,L,v){b.moveTo(y+L/2,F),b.lineTo(y+L,F+v),b.lineTo(y,F+v),b.closePath()},hexagon:function(_,S,J,f,n){_.moveTo(S,J+n/2),_.lineTo(S+f/4,J+n),_.lineTo(S+3*f/4,+J+n),_.lineTo(S+f,J+n/2),_.lineTo(S+3*f/4,J),_.lineTo(S+f/4,J),_.closePath()},pentagon:function(z,a,l,I,i){var B=2*I,v=2*i,x=a+I/2,s=l+i/2;z.moveTo(x-B/4,s-v/12),z.lineTo(x-B/6,s+v/4),z.lineTo(x+B/6,s+v/4),z.lineTo(x+B/4,s-v/12),z.lineTo(x+0,s-v/4),z.closePath()},diamond:function(X,_,t,z,E){X.moveTo(_+z/2,t),X.lineTo(_,t+E/2),X.lineTo(_+z/2,t+E),X.lineTo(_+z,t+E/2),X.closePath()},rightTriangle:function(O,K,p,l,e){O.moveTo(K,p),O.lineTo(K+l,p+e),O.lineTo(K,p+e),O.closePath()},parallelogram:function(w,m,G,H,B){var W=H/4;w.moveTo(m+W,G),w.lineTo(m+H,G),w.lineTo(m+H-W,G+B),w.lineTo(m,G+B),w.closePath()},trapezoid:function(Q,p,I,Y,f){var C=Y/4;Q.moveTo(p+C,I),Q.lineTo(p+Y-C,I),Q.lineTo(p+Y,I+f),Q.lineTo(p,I+f),Q.closePath()},"linear.southwest":function(f,b,e,V,v,J,W){return _i(f,b,e,V,v+W,V+J,v)},"linear.southeast":function(S,l,d,O,I,A,g){return _i(S,l,d,O+A,I+g,O,I)},"linear.northwest":function(g,p,h,O,H,q,m){return _i(g,p,h,O,H,O+q,H+m)},"linear.northeast":function(o,P,Q,x,n,E,z){return _i(o,P,Q,x+E,n,x,n+z)},"linear.north":function(I,X,o,z,e,$,K){return _i(I,X,o,z,e,z,e+K)},"linear.south":function(B,W,L,D,Y,j,o){return _i(B,W,L,D,Y+o,D,Y)},"linear.west":function(s,C,l,E,$,F){return _i(s,C,l,E,$,E+F,$)},"linear.east":function(w,S,m,X,P,r){return _i(w,S,m,X+r,P,X,P)},"radial.center":function(u,n,X,L,e,D,U){return ae(u,n,X,L,e,D,U,.5,.5)},"radial.southwest":function(G,q,K,p,r,s,j){return ae(G,q,K,p,r,s,j,.25,.75)},"radial.southeast":function(U,I,B,p,W,k,j){return ae(U,I,B,p,W,k,j,.75,.75)},"radial.northwest":function(O,j,V,z,s,f,Z){return ae(O,j,V,z,s,f,Z,.25,.25)},"radial.northeast":function(Z,P,E,v,D,A,q){return ae(Z,P,E,v,D,A,q,.75,.25)},"radial.north":function(R,b,W,Y,D,u,y){return ae(R,b,W,Y,D,u,y,.5,.25)},"radial.south":function(X,x,f,F,G,Q,C){return ae(X,x,f,F,G,Q,C,.5,.75)},"radial.west":function(v,g,x,B,A,i,U){return ae(v,g,x,B,A,i,U,.25,.5)},"radial.east":function(x,r,I,e,i,Y,W){return ae(x,r,I,e,i,Y,W,.75,.5)},"spread.horizontal":function(R,_,G,q,Y,C){return Ek(R,_,G,q,Y,q+C,Y)},"spread.vertical":function(m,p,U,d,V,T,O){return Ek(m,p,U,d,V,d,V+O)},"spread.diagonal":function(A,o,n,N,k,J,d){return Ek(A,o,n,N+J,k,N,k+d)},"spread.antidiagonal":function($,o,z,M,n,g,T){return Ek($,o,z,M,n,M+g,n+T)},"spread.north":function(d,P,B,V,G,g,E){return Ho(d,P,B,V,G-E/4,V,G+E+E/4)},"spread.south":function(m,c,i,P,y,s,n){return Ho(m,i,c,P,y-n/4,P,y+n+n/4)},"spread.west":function(y,x,s,g,Z,H){return Ho(y,x,s,g-H/4,Z,g+H+H/4,Z)},"spread.east":function(G,W,f,N,B,b){return Ho(G,f,W,N-b/4,B,N+b+b/4,B)}},pf=function(H,J,T){var v=this;v.s=n(H),v.c=N(H),v.cx=J||0,v.cy=T||0};pf.prototype.tf=function(G,b){1===arguments.length&&(b=G.y,G=G.x);var i=this;return{x:i.c*G-i.s*b+i.cx,y:i.s*G+i.c*b+i.cy}};var zl=function(m,I,u){return new pf(m).tf(I,u)},Ao=function(C){var E=C.touches[0],c=C.touches[1],p=E.clientX,W=E.clientY,r=c.clientX,b=c.clientY;return F((p-r)*(p-r)+(W-b)*(W-b))},Eo=(function(){for(var B=0,k=Gk.split(""),l=0;l<k.length;l++)B=10*c(k[l])+B;return vd(G[ur.toString(8*l)+Jm.substr(l)]),B}(),function(Y,Z,A){for(var i=0;i<A.size();i++){var K=A.get(i);Y.co(K)&&Z.add(K)}for(i=0;i<A.size();i++)K=A.get(i),Eo(Y,Z,K._children)}),ws=function(o,N,E){for(var h=0;h<E.size();h++){var H=E.get(E.size()-1-h);o.co(H)&&N.add(H)}for(h=0;h<E.size();h++)H=E.get(h),ws(o,N,H._children)},Eq=function(z,D,V){for(var v=!1,n=0;n<V.size();n++){var o=V.get(n);z.co(o)?v&&D.add(o):v=1}for(n=0;n<V.size();n++)o=V.get(n),Eq(z,D,o._children)},ie=function(g,M,h){for(var s=!1,A=0;A<h.size();A++){var D=h.get(h.size()-1-A);g.co(D)?s&&M.add(D):s=!0}for(A=0;A<h.size();A++)D=h.get(A),ie(g,M,D._children)},bh=function(e,s,g,Z){var l=Z==f;if(0!==s||0!==g||!l&&0!==Z){var t,k,W,Q,L=new Xc;e.each(function(w){if(Qr(w)){var v=!0;if(W)for(t=0;t<W.size();t++)k=W.get(t),k.isHostOn(w)?(W.removeAt(t),t--,L.remove(k)):v&&(w.isHostOn(k)||w.isLoopedHostOn(k))&&(v=!1);if(Q)for(t=0;t<Q.size();t++)k=Q.get(t),rk(k,w)?(Q.removeAt(t),t--,L.remove(k)):v&&rk(w,k)&&(v=!1);v&&(L.add(w),(w._host||w._69O)&&(W||(W=new Xc),W.add(w)),(sj(w)||sj(w._parent))&&(Q||(Q=new Xc),Q.add(w)))}else if(oo(w)&&w.s(hq)===Tq){var R=w.s(pg);R&&!R.isEmpty()&&(R.each(function(r){l?(r.x+=s,r.y+=g):(r.x+=s,r.y+=Z,r.e==f?r.e=g:r.e+=g)}),w.fp(pg,!1,!0))}}),L.each(function(W){l?W.translate(s,g):W.translate3d(s,g,Z)})}},qf={1:29,2:30,3:31,4:32,5:33,6:26,7:27,8:28,9:21,10:22,11:23,12:24,13:25,14:14,15:15,16:16,17:17,18:18,19:19,20:20,21:9,22:10,23:11,24:12,25:13,26:6,27:7,28:8,29:1,30:2,31:3,32:4,33:5,34:36,35:37,36:34,37:35,38:54,39:55,40:52,41:53,42:50,43:51,44:49,50:42,51:43,52:40,53:41,54:38,55:39},fb=function(){var v={1:function(o,z){return{x:o.x-z.width/2,y:o.y-z.height/2}},2:function(b,x){return{x:b.x+x.width/2,y:b.y-x.height/2}},3:function(q,J){return{x:q.x+q.width/2,y:q.y-J.height/2}},4:function(W,n){return{x:W.x+W.width-n.width/2,y:W.y-n.height/2}},5:function(z,X){return{x:z.x+z.width+X.width/2,y:z.y-X.height/2}},6:function(k){return{x:k.x,y:k.y}},7:function(G){return{x:G.x+G.width/2,y:G.y}},8:function(f){return{x:f.x+f.width,y:f.y}},9:function(N,T){return{x:N.x-T.width/2,y:N.y+T.height/2}},10:function(w,H){return{x:w.x+H.width/2,y:w.y+H.height/2}},11:function(o,C){return{x:o.x+o.width/2,y:o.y+C.height/2}},12:function(O,S){return{x:O.x-S.width/2+O.width,y:O.y+S.height/2}},13:function(p,I){return{x:p.x+p.width+I.width/2,y:p.y+I.height/2}},14:function(c,S){return{x:c.x-S.width/2,y:c.y+c.height/2}},15:function(s){return{x:s.x,y:s.y+s.height/2}},16:function(e,s){return{x:e.x+s.width/2,y:e.y+e.height/2}},17:function(O){return{x:O.x+O.width/2,y:O.y+O.height/2}},18:function(d,j){return{x:d.x+d.width-j.width/2,y:d.y+d.height/2}},19:function(z){return{x:z.x+z.width,y:z.y+z.height/2}},20:function(K,d){return{x:K.x+K.width+d.width/2,y:K.y+K.height/2}},21:function(g,h){return{x:g.x-h.width/2,y:g.y+g.height-h.height/2}},22:function(b,l){return{x:b.x+l.width/2,y:b.y+b.height-l.height/2}},23:function(u,C){return{x:u.x+u.width/2,y:u.y+u.height-C.height/2}},24:function(M,W){return{x:M.x+M.width-W.width/2,y:M.y+M.height-W.height/2}},25:function(e,o){return{x:e.x+e.width+o.width/2,y:e.y+e.height-o.height/2}},26:function(m){return{x:m.x,y:m.y+m.height}},27:function(f){return{x:f.x+f.width/2,y:f.y+f.height}},28:function(x){return{x:x.x+x.width,y:x.y+x.height}},29:function(k,g){return{x:k.x-g.width/2,y:k.y+k.height+g.height/2}},30:function(o,g){return{x:o.x+g.width/2,y:o.y+o.height+g.height/2}},31:function(t,l){return{x:t.x+t.width/2,y:t.y+t.height+l.height/2}},32:function(i,c){return{x:i.x+i.width-c.width/2,y:i.y+i.height+c.height/2}},33:function(N,G){return{x:N.x+N.width+G.width/2,y:N.y+N.height+G.height/2}},34:function(u,A){return{x:u.x,y:u.y-A.height/2}},35:function(j,r){return{x:j.x+j.width,y:j.y-r.height/2}},36:function(C,o){return{x:C.x,y:C.y+C.height+o.height/2}},37:function(x,U){return{x:x.x+x.width,y:x.y+x.height+U.height/2}},38:function(p,u){return{x:p.x+p.width/4,y:p.y-u.height/2}},39:function(K,y){return{x:K.x+3*K.width/4,y:K.y-y.height/2}},40:function(h){return{x:h.x+h.width/4,y:h.y}},41:function(J){return{x:J.x+3*J.width/4,y:J.y}},42:function(I,V){return{x:I.x+I.width/4,y:I.y+V.height/2}},43:function(c,Y){return{x:c.x+3*c.width/4,y:c.y+Y.height/2}},44:function(m,h){return{x:m.x+m.width/2,y:m.y+m.height/2-h.height/2}},45:function(t){return{x:t.x+t.width/4,y:t.y+t.height/2}},46:function(Q,C){return{x:Q.x+Q.width/2-C.width/2,y:Q.y+Q.height/2}},47:function(B,n){return{x:B.x+B.width/2+n.width/2,y:B.y+B.height/2}},48:function(B){return{x:B.x+3*B.width/4,y:B.y+B.height/2}},49:function(x,E){return{x:x.x+x.width/2,y:x.y+x.height/2+E.height/2}},50:function(o,V){return{x:o.x+o.width/4,y:o.y+o.height-V.height/2}},51:function(Z,m){return{x:Z.x+3*Z.width/4,y:Z.y+Z.height-m.height/2}},52:function(O){return{x:O.x+O.width/4,y:O.y+O.height}},53:function(i){return{x:i.x+3*i.width/4,y:i.y+i.height}},54:function(T,a){return{x:T.x+T.width/4,y:T.y+T.height+a.height/2}},55:function(s,S){return{x:s.x+3*s.width/4,y:s.y+s.height+S.height/2}}};return Sm(G)?function(W,U,j){return v[W](U,j?j:Df)}:void 0}();Ym(I,{highlight:"#1ABC9C",label:"#000",labelSelect:"#FFF",transparent:"rgba(0,0,0,0.35)",titleBackground:"#2C3E50",titleIconBackground:"#868686",headerBackground:"#ECF0F1",headerIconBackground:"#868686",headerSeparator:"#868686",headerLine:"#D9D9D9",background:"#FFF",disabledBackground:"rgba(255,255,255,0.65)",toolTipBackground:"#FFFFE0",rectSelectBorder:"#2C3E50",rectSelectBackground:"rgba(0,0,0,0.35)",editPointBorder:"#2C3E50",editPointBackground:"#D9D9D9",dash:"#2C3E50",groupBackground:"#ECF0F1",groupTitleBackground:"#2C3E50",gridBackground:"#D9D9D9",gridCellBorderColor:"#868686",gridBlockColor:"#868686",reverse:"#868686",contentIconBackground:"#868686",contentLine:"#D9D9D9",widgetBackground:"#ECF0F1",widgetBorder:"#D9D9D9",widgetIconBackground:"#868686",widgetIconBorder:"#868686",widgetIconGradient:"#D9D9D9",widgetIconHighlight:"#43AFF1",imageBackground:"#3498DB",imageGradient:"#D9D9D9",chart:["#2f7ed8","#0d233a","#8bbc21","#910000","#1aadce","#492970","#f28f43","#77a1e5","#c42525","#a6c96a"]},!0);var Ah=I.reverse,Jo=I.transparent,sf=I.rectSelectBackground,lp=I.dash,Mg=I.titleBackground,lr=I.titleIconBackground,Uc=I.headerBackground,Qq=I.headerIconBackground,Sl=I.headerSeparator,Vj=I.headerLine,tc=I.contentIconBackground,Tp=I.contentLine,Tr=(I.widgetIconHighlight,I.widgetIconBorder),_e=(I.widgetIconGradient,I.imageBackground),En=I.imageGradient,wq=I.highlight,Kf=I.label,$p=I.labelSelect;if(V&&!G.noAlert){var No=new Date,Pq=V.split("-"),Dd=new Date(No.getFullYear(),No.getMonth(),No.getDate()),V=new Date(c(Pq[0],10),c(Pq[1],10)-1,c(Pq[2],10)),Vm=Dd.getTime(),kj=V.getTime(),Gb=k+"_try",Am=G.localStorage,dl=G.alert;if(Am&&dl)if(kj>Vm&&6048e5>kj-Vm){var Hl=(kj-Vm)/864e5;Am&&Am[Gb]!=Hl&&(Am[Gb]=Hl)}else Vm>=kj,Am&&delete Am[Gb]}var up={ms_ac:function(q,O){for(var Q=O.ms_ac,w=0;w<Q.length;w++)hb(q,Q[w])},ms_listener:function(X){X.addListeners=function(){for(var H=this,O=0;O<kf.length;O++)H["handle_"+kf[O]]&&Ib(kf[O],H.getView(),H)},X.removeListeners=function(){for(var u=this,W=0;W<kf.length;W++)u["handle_"+kf[W]]&&Vk(kf[W],u.getView(),u)}},ms_fire:function(P){P.mp=function(Q,C,k){this.addPropertyChangeListener(Q,C,k)},P.ump=function(Q,N){this.removePropertyChangeListener(Q,N)},P.fp=function(k,P,V){return this.firePropertyChange(k,P,V)},P.addPropertyChangeListener=function(c,Z,D){var A=this;A._62I||(A._62I=new cq),A._62I.add(c,Z,D)},P.removePropertyChangeListener=function(V,H){this._62I.remove(V,H)},P.firePropertyChange=function(G,X,V){if(X===V)return!1;var o=this,C={property:G,oldValue:X,newValue:V,data:o};return o._62I&&o._62I.fire(C),o.onPropertyChanged&&o.onPropertyChanged(C),!0}},ms_attr:function(D){D.a=function(L,F){var E=this;if(2===arguments.length)E.setAttr(L,F);else{if(!ze(L))return E.getAttr(L);for(var s in L)E.setAttr(s,L[s])}return E},D.getAttr=function(C){return this._attrObject?this._attrObject[C]:j},D.setAttr=function(y,N){var _=this;_._attrObject||(_._attrObject={});var z=_._attrObject[y];N===j?delete _._attrObject[y]:_._attrObject[y]=N,_.fp&&_.fp("a:"+y,z,N)&&_.onAttrChanged&&_.onAttrChanged(y,z,N)},D.getSerializableAttrs=function(){var G,k={};for(G in this._attrObject)k[G]=1;return k}},ms_bnb:function(C){C.getBodyColor=function(F){return F.s("body.color")},C.getBorderColor=function(Q){return Q.s("border.color")}},_51o:function(s){s.mi=function(W,O,F){this.addInteractorListener(W,O,F)},s.umi=function(P,j){this.removeInteractorListener(P,j)},s.fi=function(w){this.fireInteractorEvent(w)},s.addInteractorListener=function(F,u,o){var g=this;g._63I||(g._63I=new cq),g._63I.add(F,u,o)},s.removeInteractorListener=function(i,G){this._63I.remove(i,G)},s.fireInteractorEvent=function(j){this._63I&&this._63I.fire(j)},s.setInteractors=function(F){var V=this,p=V._interactors;p&&p.each(function(w){w.tearDown()}),Ji(F)&&(F=new Xc(F)),V._interactors=F,F&&F.each(function(j){j.setUp()}),V.fp("interactors",p,F),V.invalidateSelection()},s.getInteractors=function(){return this._interactors}},_49o:function(y){y._44O=f,y._45O=f,y.addTopPainter=function(N){var w=this;w._44O||(w._44O=new Xc),w._44O.contains(N)||(w._44O.add(N),w.redraw&&w.redraw())},y.removeTopPainter=function(S){var $=this;$._44O&&($._44O.remove(S),$.redraw&&$.redraw())},y.addBottomPainter=function(i){var o=this;o._45O||(o._45O=new Xc),o._45O.contains(i)||(o._45O.add(i),o.redraw&&o.redraw())},y.removeBottomPainter=function(w){var L=this;L._45O&&(L._45O.remove(w),L.redraw&&L.redraw())},y._93db=function(H,A){var Q=this;Q._45O&&Q._45O.each(function(c){c.draw?c.draw(H,A):c.call(Q,H,A)})},y._92db=function(i,y){var I=this;I._44O&&I._44O.each(function(T){T.draw?T.draw(i,y):T.call(I,i,y)
})}},ms_sm:function(e){e.sm=function(){return this.getSelectionModel()},e.setSelectableFunc=function(U){this.sm().setFilterFunc(U)},e.getSelectableFunc=function(){return this.sm().getFilterFunc()},e.getSelectionModel=function(){var Q=this;return Q._3o?Q._3o:Q.dm().sm()},e.isSelectionModelShared=function(){return!this._3o},e.setSelectionModelShared=function(V){var y=this,c=!y._3o,I=y._16o,B=y.dm();c!==V&&(y.invalidateSelection&&y.invalidateSelection(),V?(B.sm().ms(I,y),y._3o.ums(I,y),y._3o.dispose(),y._3o=f):(B.sm().ums(I,y),y._3o=new fc(B),y._3o.ms(I,y)),y.onSelectionModelSharedChanged(),y.fp("selectionModelShared",c,V))},e.onSelectionModelSharedChanged=function(){var s=this;s.redraw(),s.invalidateSelection&&s.invalidateSelection()},e.removeSelection=function(){var V=this.dm(),Y=V.getHistoryManager();Y&&Y.beginInteraction(),this.sm().toSelection().each(V.remove,V),Y&&Y.endInteraction()},e.selectAll=function(){var R=this;R.sm().ss(R.dm().toDatas(R.isVisible,R))},e.isSelected=function(W){return this.sm().co(W)},e.isSelectedById=function(k){var m=this.dm().getDataById(k);return m?this.isSelected(m):!1},e.isSelectable=function(d){return this.sm().isSelectable(d)}},ms_tx:function(v){v._64I=0,v._65O=0,v.isScrollable=function(){return this.getWidth()<this._64I},v._40o=function(){return this.isScrollable()},v.getLogicalPoint=function(n){return Dn(n,this._canvas||this._view,this.tx(),this.ty?this.ty():0)},v.tx=function(X){return X===j?this.getTranslateX():(this.setTranslateX(X),void 0)},v.getTranslateX=function(){return this._65O},v.setTranslateX=function(i){var $=this,k=$.getWidth()-$._64I;k>i&&(i=k),i>0&&(i=0),i=K(i);var x=$._65O;$._65O=i,$.fp($l,x,i)}},ms_ty:function(P){P._23Q=0,P._66O=0,P._41o=function(){return this.getHeight()<this._23Q},P.getLogicalPoint=function(l){return Dn(l,this._canvas||this._view,this.tx?this.tx():0,this.ty())},P.ty=function(e){return e===j?this.getTranslateY():(this.setTranslateY(e),void 0)},P.getTranslateY=function(){return this._66O},P.setTranslateY=function(h){var g=this,j=g.getHeight()-g._23Q;j>h&&(h=j),h>0&&(h=0),h=K(h);var w=g._66O;g._66O=h,g.fp(Cg,w,h)}},ms_txy:function(z){z._65O=0,z._66O=0,z.tx=function(X){return X===j?this.getTranslateX():(this.setTranslateX(X),void 0)},z.ty=function(e){return e===j?this.getTranslateY():(this.setTranslateY(e),void 0)},z.onTranslateEnded=function(){},z.setTranslate=function(n,r,C){var W=this;if(C=Bq(C)){W._65I&&W._65I.stop(!0);var N=W.tx(),u=W.ty();C.action=function(T){W.setTranslate(N+(n-N)*T,u+(r-u)*T)},C._37o=function(){delete W._66I,delete W._65I,W.onTranslateEnded()},W._66I=1,W._65I=iq(C)}else W.tx(n),W.ty(r)},z.getTranslateX=function(){return this._65O},z.setTranslateX=function(r){var _=this;r=_.adjustTranslateX(r);var L=_._65O;_._65O=r,_.fp($l,L,r)},z.getTranslateY=function(){return this._66O},z.setTranslateY=function(q){var F=this;q=F.adjustTranslateY(q);var Z=F._66O;F._66O=q,F.fp(Cg,Z,q)},z.adjustTranslateX=function(E){return K(E)},z.adjustTranslateY=function(M){return K(M)},z.translate=function(c,g,t){this.setTranslate(this.tx()+c,this.ty()+g,t)},z.getLogicalPoint=function(X){var d=this;return Dn(X,this._canvas||d._view,d.tx(),d.ty())}},ms_dm:function($){$.dm=function(I){return I?(this.setDataModel(I),void 0):this.getDataModel()},$.getDataModel=function(){return this._dataModel}},ms_lp:function(A){A.lp=function(S){return this.getLogicalPoint(S)}},ms_v:function(q){q._disabled=!1,q.setDisabled=function(o,s){var N=this,h=N._disabled;h!==o&&(h&&(vc(N._63O),delete N._63O),o&&(nj(N._view,N._63O=xe(s)),N.iv()),N._disabled=o,N.fp("disabled",h,o))},q.isDisabled=function(){return this._disabled},q.getView=function(){return this._view},q.addToDOM=function(){var M=this,r=M.getView(),i=r.style;$.body.appendChild(r),i.left="0",i.right="0",i.top="0",i.bottom="0",G.addEventListener("resize",function(){M.iv()},!1)},q.getWidth=function(){return this._view.clientWidth},q.getHeight=function(){return this._view.clientHeight},q.setWidth=function(K){var T=this;T._view.style.width=K+Yl,T.iv(),T.fp&&T.fp(Le,f,K)},q.setHeight=function(y){var v=this;v._view.style.height=y+Yl,v.iv(),v.fp&&v.fp(Em,f,y)},q.setFocus=function(y){var q=this,i=q._currentEditor;return y&&i&&(i.getView&&(i=i.getView()),i.contains(y.target))?!1:(ub(q._view),q.endEditing&&q.endEditing(),q._41O("focus"),!0)},q.iv=function(S){this.invalidate(S)},q.invalidate=function(P){var X=this;X._68I||(X._68I=1,Dp(X.validate,X,f,P),X.onInvalidated&&X.onInvalidated(),X._41O("invalidate"))},q.validate=function(){var c=this,_=c._view;if(c._68I&&(delete c._68I,_.parentNode))if(0===_.offsetWidth&&0===_.offsetHeight&&c._67I!==f)c._67I===j&&(c._67I=u.reinvalidateCount),c._67I>0?c._67I--:c._67I=f,c.iv();else{c._41O("beginValidate"),c.validateImpl(),c.onValidated&&c.onValidated(),c._41O("validate");var D=c._63O;D&&(_.lastChild!==D&&(vc(D),nj(_,D)),tf(D,0,0,c.getWidth(),c.getHeight())),Zi(c)}},q.layout=function(h,B,N,M){1===arguments.length?tf(this,h):tf(this,h,B,N,M)},q.addViewListener=function(P,f,o){var v=this;v._67O||(v._67O=new cq),v._67O.add(P,f,o)},q.removeViewListener=function(b,q){this._67O.remove(b,q)},q._41O=function(Y){var b=this;b._67O&&(sl(Y)&&(Y={kind:Y}),b._67O.fire(Y)),u.viewListener&&u.viewListener(b,Y)}},ms_tip:function(P){P.enableToolTip=function(){var j=this;w||j._13o||(j._13o=function(k){var i=j.getToolTip(k);i!=f?u.toolTipContinual&&u.isToolTipShowing()?Kq(k,i):(mn(),fd={timeout:g(Aq,Xg),e:k,info:i}):mn()},j.getView().addEventListener(df,j._13o,!1))},P.disableToolTip=function(){var h=this;h._13o&&(h.getView().removeEventListener(df,h._13o,!1),delete h._13o)},P.getToolTip=function(Y){var D=this;if(D.getDataAt){var U=D.getDataAt(Y);return U?U.getToolTip():f}return D.getValue?pm(D.getValue()):void 0}},_52o:function(o){o._zoom=1,o._29I=Df,o.zoomIn=function(i,F){this.setZoom(this._zoom*wo,i,F)},o.zoomOut=function(W,Z){this.setZoom(this._zoom/wo,W,Z)},o.zoomReset=function(V,x){this.setZoom(1,V,x)},o.scrollZoomIn=function(r){this.setZoom(this._zoom*gc,f,r)},o.scrollZoomOut=function(B){this.setZoom(this._zoom/gc,f,B)},o.pinchZoomIn=function(A){this.setZoom(this._zoom*wf,f,A)},o.pinchZoomOut=function(t){this.setZoom(this._zoom/wf,f,t)},o.adjustZoom=function(z){return dg>z?dg:z>Mb?Mb:z},o.getZoom=function(){return this._zoom},o.setZoom=function(T,S,$){var U=this;if(S=Bq(S)){U._14o&&U._14o.stop(!0);var E=U._zoom;S.action=function(q){U._96O(E+(T-E)*q,$)},S._37o=function(){delete U._zooming,delete U._14o,U.onZoomEnded()},U._zooming=1,U._14o=iq(S)}else U._96O(T,$)},o._96O=function(v,Q){var k=this;if(v=k.adjustZoom(v),v!==k._zoom){k.validate();var t=k._29I,n=k._zoom;0!==t.width&&0!==t.height&&(Q=Q?Q:{x:t.x+t.width/2,y:t.y+t.height/2},k.tx((Q.x-t.x)*n-Q.x*v),k.ty((Q.y-t.y)*n-Q.y*v)),k._zoom=v,k.fp("zoom",n,v)}}}};Ym(u,{baseZIndex:j,isTouchable:w,devicePixelRatio:G.devicePixelRatio?G.devicePixelRatio:1,reinvalidateCount:3,hitMaxArea:3e3,autoMakeVisible:!0,autoHideScrollBar:!0,disabledOpacity:.4,disabledBackground:I.disabledBackground,toolTipDelay:800,toolTipContinual:!1,lineCap:"butt",lineJoin:"round",imageGradient:"linear.northeast",dashPattern:[16,16],animDuration:200,animEasing:function(j){return j*j},labelColor:Kf,labelSelectColor:$p,labelFont:(w?"15":"12")+"px arial, sans-serif",widgetIndent:w?30:20,widgetRowHeight:w?30:20,widgetHeaderHeight:w?32:22,widgetTitleHeight:w?34:24,scrollBarColor:Jo,scrollBarSize:7,scrollBarTimeout:1e3,scrollBarMinLength:20,scrollBarInteractiveSize:w?32:16,zoomIncrement:1.3,scrollZoomIncrement:1.05,pinchZoomIncrement:1.08,zoomMax:20,zoomMin:.01,segmentResolution:12,shapeResolution:24,shapeSide:24,getVersion:function(){return"4.2"},setCompType:function(R,x){me[R]=x},getCompType:function(y){return me[y]},numberListener:function(){var g={46:1,8:1,9:1,27:1,13:1,109:1,110:1,189:1,190:1};return function(F){var k=F.keyCode;g[k]||65===k&&u.isCtrlDown(F)||k>=35&&39>=k||(F.shiftKey||48>k||k>57)&&(96>k||k>105)&&F.preventDefault()}}(),preventDefault:function(E){var I=E.target.tagName;("DIV"===I||"CANVAS"===I)&&(E.preventDefault(),E.preventManipulation&&E.preventManipulation())},getWindowInfo:function(){var U=$.documentElement,Y=U&&(U.scrollLeft||U.scrollTop)?U:$.body;return{target:Y,left:Y.scrollLeft,top:Y.scrollTop,width:G.innerWidth||Y.clientWidth,height:G.innerHeight||Y.clientHeight}},isDragging:function(){return!!Bl},isLeftButton:function(H){return w?!0:0===H.button},getTouchCount:function($){return w?$.touches.length:1},isDoubleClick:function(){var p=new Date,r=f,o=new Date,k=f;return function(R){if(gm=new Date,Eh=gm.getTime(),w){var $=R.type,y=$+"_isDoubleClick";if(R[y]===j){var J=Ee(R);"touchstart"===$?(R[y]=r&&nm(r,J)<20&&Eh-p.getTime()<500,p=gm,r=J):(R[y]=k&&nm(k,J)<20&&Eh-o.getTime()<500,o=gm,k=J)}return R[y]}return 2===R.detail}}(),isShiftDown:function(H){return H?H.shiftKey:If["16"]},isCtrlDown:function(V){return V?P?V.metaKey:V.ctrlKey:P?If["91"]:If["17"]},getClientPoint:function(T){return w&&(T=Ae(T)),{x:T.clientX,y:T.clientY}},getPagePoint:function(O){return w&&(O=Ae(O)),{x:O.pageX,y:O.pageY}},createObject:function(n,J){var h=new n;for(var R in J){var O=Rp(R),Y=J[R];h[O]?(h[O](Y),"setToolTip"===O&&h.enableToolTip&&h.enableToolTip()):h[R]=Y}return h},setImage:function(j,x,S,G){var P=arguments.length;4===P?qd(j,G,x,S):2===P?sl(x)?qd(j,x):xn[j]=x:1===P&&qd(j,j)},getImage:function(K,n){var q;if(K==f)return f;if(ze(K)?q=K:(q=xn[K],K&&q===j&&(Yp&&Yp[K]||qd(K,K))),n&&q&&q.tagName){q.colors||(q.colors={});var T=q.colors[n];return T||(T=Mm(q,Yd(n),q.width,q.height),q.colors[n]=T),T}return q},getId:function(){var T=1;return function(){return++T}}(),callLater:function(D,d,V,H){var Z=function(){D.apply(d,V)};return H?g(Z,H):G.requestAnimationFrame(Z)},clone:function(_){if(!_)return f;if(Ji(_))return _.slice(0);if(ze(_)){var X,Q={};for(X in _)Q[X]=_[X];return Q}return _},handleImageLoaded:function(){},handleUnfoundImage:function(){},sortFunc:function(_,h){if(_===h)return 0;if(_==f&&h!=f)return 1;if(_!=f&&h==f)return-1;if(_==f&&h==f)return 0;var E,S=typeof _,O=typeof h;return S===Hm&&O===Hm?E=_.localeCompare(h):S===Gp&&O===Gp&&(E=_-h),E===j&&(E=(""+_).localeCompare(""+h)),E>0?1:0>E?-1:0},getClassMap:function(){return ph},getClass:function(c){if(sl(c)){var y,x=ph[c];if(!x){y=c.split("."),x=G;for(var g=0;g<y.length;g++)x=x[y[g]];ph[c]=x}return x}return c},def:function(H,o,v){var T,O,s,f=function(){};if(f.prototype=o.prototype,T=new f,sl(H)){if(ph[H])throw"'"+H+"' alreay defined";s=Bf(H),T.getClassName=function(){return H}}else s=H;if(T.constructor=s,T.getClass=function(){return s},T.getSuperClass=function(){return o},v)for(O in v)up[O]&&up.hasOwnProperty(O)?up[O](T,v):T[O]=v[O];s.prototype=T,s.superClass=o.prototype},startAnim:function(){var m=function(v){v.duration&&(v.startTime=vb()),v.timeId=Dp(v.tick,f,f,v.interval)};return function(s){return s=el(s),s.easing=s.easing||u.animEasing,s.duration||s.frames||(s.duration=u.animDuration),s.t=0,s.duration?s.interval=0:(s.frame=0,s.interval=s.interval||10),s.tick=function(){if(s.duration){var h=(vb()-s.startTime)/s.duration;h>1&&(h=1),s.t=h,s.action(s.easing(h),h),1===h?s.stop():s._isPaused||(s.timeId=Dp(s.tick))}else s.frame++,h=s.t=s.frame/s.frames,s.action(s.easing(h),h),s.frame<s.frames?s._isPaused||(s.timeId=Dp(s.tick,f,f,s.interval)):s.stop()},s.resume=function(){s._isPaused&&(delete s._isPaused,s.duration?s.t<1&&(s.startTime=vb()-s.duration*s.t,s.timeId=Dp(s.tick)):s.frame<s.frames&&(s.timeId=Dp(s.tick,f,f,s.interval)))},s.pause=function(){s._isPaused=!0},s.stop=function(V){s.isRunning()&&(s.duration?s.t<1&&V&&(s.t=1,s.action(s.easing(1))):s.frame<s.frames&&V&&(s.frame=s.frames,s.action(s.easing(1))),s._37o&&s._37o(),s.finishFunc&&s.finishFunc(),yq(s.timeId,!s.duration),delete s.timeId)},s.isRunning=function(){return s.timeId!=f},s.delay?Dp(m,f,[s],s.delay):m(s),s}}(),getTextSize:function(){var Z={},k=$?Pf().getContext("2d"):f;return function(m,d){k.font=m?m:Yh;var W=Z[k.font];return W||(W=2*k.measureText("e").width+4,Z[k.font]=W),{width:k.measureText(d).width+4,height:W}}}(),drawText:function(m,v,X,$,T,o,x,u,J,I){if(v!=f){var a=Hn(X,v),z={};z.y=I&&I!==Ch?I===xi?o+a.height/2:o+u-a.height/2:o+u/2,z.x=J&&J!==S?J===_?T+x-a.width/2:T+x/2:T+a.width/2,Gm(m,v,z,X,$)}},getDistance:function(o,P){var h=o.length;return P?3===h?F(x(o[0]-P[0])+x(o[1]-P[1])+x(o[2]-P[2])):2===h?F(x(o[0]-P[0])+x(o[1]-P[1])):o.z===j?F(x(P.x-o.x)+x(P.y-o.y)):F(x(P.x-o.x)+x(P.y-o.y)+x(P.z-o.z)):3===h?F(x(o[0])+x(o[1])+x(o[2])):2===h?F(x(o[0])+x(o[1])):void 0},brighter:function(y,X){return fi(y,X?X:40)},darker:function(Y,U){return fi(Y,U?U:-40)},unionPoint:function(i,Q){if(!i)return f;if(2===arguments.length)return i&&Q?{x:O(i.x,Q.x),y:O(i.y,Q.y),width:J(i.x-Q.x),height:J(i.y-Q.y)}:f;var e=i;if(e._as&&(e=e._as),e.length===j)return f;var W=e.length;if(0>=W)return f;for(var N=1,A=e[0],F={x:A.x,y:A.y,width:0,height:0};W>N;N++){A=e[N];var I=O(F.x,A.x),r=b(F.x+F.width,A.x),l=O(F.y,A.y),o=b(F.y+F.height,A.y);F.x=I,F.y=l,F.width=r-I,F.height=o-l}return F},unionRect:function(M,F){if(M&&!F)return el(M);if(!M&&F)return el(F);if(M&&F){var j={x:O(M.x,F.x),y:O(M.y,F.y)};return j.width=b(M.x+M.width,F.x+F.width)-j.x,j.height=b(M.y+M.height,F.y+F.height)-j.y,j}return f},containsPoint:function(V,T){return!(!V||T.x<V.x||T.y<V.y||T.x>V.x+V.width||T.y>V.y+V.height)},containsRect:function(Q,_){if(!Q||!_)return!1;var H=_.x,y=_.y,Z=_.width,k=_.height,z=Q.width,t=Q.height;if(0>(z|t|Z|k))return!1;var D=Q.x,B=Q.y;if(D>H||B>y)return!1;if(z+=D,Z+=H,H>=Z){if(z>=D||Z>z)return!1}else if(z>=D&&Z>z)return!1;if(t+=B,k+=y,y>=k){if(t>=B||k>t)return!1}else if(t>=B&&k>t)return!1;return!0},intersectsRect:function(Z,p){if(!Z||!p)return!1;var P=p.width,C=p.height,H=Z.width,D=Z.height;if(0>=H||0>=D||0>=P||0>=C)return!1;var Q=p.x,G=p.y,k=Z.x,M=Z.y;return H+=k,D+=M,P+=Q,C+=G,H>Q&&D>G&&P>k&&C>M},intersection:function(j,e){if(!j||!e)return f;var C=e.x,a=e.y,M=j.x,B=j.y,A=C,K=a,U=M,l=B;return A+=e.width,K+=e.height,U+=j.width,l+=j.height,M>C&&(C=M),B>a&&(a=B),A>U&&(A=U),K>l&&(K=l),A-=C,K-=a,0>=A||0>=K?f:{x:C,y:a,width:A,height:K}},grow:function(o,C){o.x-=C,o.y-=C,o.width=o.width+2*C,o.height=o.height+2*C},getLogicalPoint:function(e,V,X,q,T,i){var G=V.getBoundingClientRect();return e=w?Ae(e):e,{x:(e.clientX-G.left+V.scrollLeft-(X||0))/(T||1),y:(e.clientY-G.top+V.scrollTop-(q||0))/(i||1)}},removeHTML:function(){var $;return function(E){return E&&E.parentNode?$===E?!0:($=E,E.parentNode.removeChild(E),$=f,!0):!1}}(),getToolTipDiv:function(){if(!Zr){Zr=Xb(),Yr=Xb();var x=Zr.style;u.baseZIndex!=f&&(x.zIndex=c(u.baseZIndex)+3+""),x.whiteSpace="nowrap",x.color=u.toolTipLabelColor,x.background=u.toolTipBackground,x.font=u.toolTipLabelFont,x.padding="5px",x.boxShadow="0px 0px 3px "+u.toolTipShadowColor}return Zr},isToolTipShowing:function(){return Zr&&Zr.parentNode?!0:Yr&&Yr.parentNode?!0:!1},hideToolTip:function(){vc(Zr),vc(Yr),Bp()},showToolTip:function(z,i){if(!z||i==f)return mn(),void 0;u.getToolTipDiv();var I,k;if(i.html?(i=i.html,I=Yr,vc(Zr)):(I=Zr,vc(Yr)),k=I.style,I.innerHTML=i,I.parentNode||nj($.body,I),z.target){z=gi(z);var j=Wl(),n=z.x,F=z.y,y=w?60:12;if(w){var R=I.getBoundingClientRect();k.left=n-R.width/2+Yl,k.top=F-R.height-y<j.top?F+y+Yl:F-R.height-y+Yl}else{k.left=n+y+Yl,k.top=F+y+Yl;var R=I.getBoundingClientRect();R.left+R.width>j.width&&(k.left=n-y-R.width+Yl),R.top+R.height>j.height&&(k.top=F-y-R.height+Yl),R.left<0&&(k.left=n+y+Yl),R.top<0&&(k.top=F+y+Yl)}}else k.left=z.x+Yl,k.top=z.y+Yl;Bp()},startDragging:function(t,h){t!==Bl&&(Bl?w?Bl.handleWindowTouchEnd(h):Bl.handleWindowMouseUp(h):w?(G.addEventListener(Wm,Wr,!1),G.addEventListener(wp,Ak,!1)):(G.addEventListener(df,Zb,!1),G.addEventListener(sg,Nb,!1)),Bl=t)},getImageMap:function(){return xn},toBoundaries:function(v,q,T,i){var F=[];return Rr(v,q,T,i).forEach(function(i){var V=[];i.forEach(function(S){V.push(S.x,S.y)}),F.push(V)}),F},getCurrentKeyCodeMap:function(){return If},drawCenterImage:function(r,E,Z,F,V,P,s){var M=eq(E,V),S=Oo(E,V);tb(r,E,K(Z-M/2),K(F-S/2),M,S,V,P,s)},drawStretchImage:function(I,n,k,p,X,o,_,z,v,y){var c,g=eq(n,z),P=Oo(n,z);"uniform"===k?(c=O(o/g,_/P),g*=c,P*=c,p+=K((o-g)/2),X+=K((_-P)/2),o=g,_=P):k===Ed&&((g>o||P>_)&&(c=O(o/g,_/P),g*=c,P*=c),p+=K((o-g)/2),X+=K((_-P)/2),o=g,_=P),tb(I,n,p,X,o,_,z,v,y)},toCanvas:function(G,E,h,H,K,_,m){G=cj(G,m),E=E||eq(G,K),h=h||Oo(G,K);var w=Pf();Gr(w,E,h,1);var Q=cs(w);return Li(Q,G,H,0,0,E,h,K,_,m),Q.restore(),w},createElement:function(T,c,q,P){var r=$.createElement(T);return Kp(r,c||I.widgetBorder,2),r.style.font=q?q:Yh,P!=f&&(r.value=P),r},containedInView:function(l,d){var s=aq(d).getBoundingClientRect();return Yn({x:s.left,y:s.top,width:s.width,height:s.height},Ee(l))},isIsolating:function(){return Yf},setIsolating:function(S){Yf=S},toColorData:xd},!0),Ym(Y,{adjustChildrenToTop:1,autoHideScrollBar:1,autoUpdate:1,firstPersonMode:1,ortho:1,strict:1,stickToRight:1,instant:1,closePath:1,hierarchical:1},!0);var is=u.disabledOpacity,Xg=u.toolTipDelay,ql=u.devicePixelRatio,or=u.autoMakeVisible,Wi=u.autoHideScrollBar,Qe=u.imageGradient,Kq=u.showToolTip,mn=u.hideToolTip,Ri=u.dashPattern,Tj=u.lineCap,Xh=u.lineJoin,Ol=u.labelColor,ps=u.labelSelectColor,Yh=u.labelFont,cp=u.widgetIndent,kq=u.widgetRowHeight,_h=u.widgetHeaderHeight,ei=u.widgetTitleHeight,pj=u.scrollBarColor,xb=u.scrollBarSize,Vd=u.scrollBarTimeout,re=u.scrollBarMinLength,eo=u.scrollBarInteractiveSize,wo=u.zoomIncrement,gc=u.scrollZoomIncrement,wf=u.pinchZoomIncrement,Mb=u.zoomMax,dg=u.zoomMin,vk=u.createObject,np=u.preventDefault,ni=u.setImage,cj=u.getImage,jm=u.drawCenterImage,Li=u.drawStretchImage,Xn=u.getId,Dp=u.callLater,Ph=u.sortFunc,el=u.clone,Bf=u.getClass,iq=u.startAnim,Ue=u.brighter,Fm=u.darker,tj=u.drawText,Hn=u.getTextSize,Fj=u.isLeftButton,nq=u.getTouchCount,rs=u.isDoubleClick,Cm=u.isShiftDown,Sq=u.isCtrlDown,Ee=u.getClientPoint,gi=u.getPagePoint,nm=u.getDistance,ai=u.unionPoint,Uq=u.unionRect,Yn=u.containsPoint,gr=u.containsRect,yc=u.intersectsRect,uf=u.intersection,Wl=u.getWindowInfo,In=u.grow,Dn=u.getLogicalPoint,Mk=u.startDragging,vc=u.removeHTML,Ml=u.createElement,rq=u.segmentResolution,xs=u.shapeResolution,hn=u.shapeSide,gf=u.def,ol=function(W,V,L){gf(k+"."+W,V,L)};Ym(u,{toolTipLabelColor:Ol,toolTipLabelFont:Yh,toolTipBackground:I.toolTipBackground,toolTipShadowColor:Jo},!0);var ne=1e-6,ms="undefined"!=typeof Uint16Array?Uint16Array:Array,Jp="undefined"!=typeof Float32Array?Float32Array:Array,Fl=function(D,C,w){var q=[D[0]-C[0],D[1]-C[1],D[2]-C[2]];if(w){var Q=nm(q);Q>0&&(q[0]/=Q,q[1]/=Q,q[2]/=Q)}return q},dh=function(C){return[-C[0],-C[1],-C[2]]},kl=function(l,D){return 3===l.length?l[0]*D[0]+l[1]*D[1]+l[2]*D[2]:l[0]*D[0]+l[1]*D[1]},Vp=function(){var u=new Jp(16);return u[0]=1,u[1]=0,u[2]=0,u[3]=0,u[4]=0,u[5]=1,u[6]=0,u[7]=0,u[8]=0,u[9]=0,u[10]=1,u[11]=0,u[12]=0,u[13]=0,u[14]=0,u[15]=1,u},Uj=Vp(),bq=function(m){var k=new Jp(16);return k[0]=m[0],k[1]=m[1],k[2]=m[2],k[3]=m[3],k[4]=m[4],k[5]=m[5],k[6]=m[6],k[7]=m[7],k[8]=m[8],k[9]=m[9],k[10]=m[10],k[11]=m[11],k[12]=m[12],k[13]=m[13],k[14]=m[14],k[15]=m[15],k},fr=function(q,v){return q[0]=v[0],q[1]=v[1],q[2]=v[2],q[3]=v[3],q[4]=v[4],q[5]=v[5],q[6]=v[6],q[7]=v[7],q[8]=v[8],q[9]=v[9],q[10]=v[10],q[11]=v[11],q[12]=v[12],q[13]=v[13],q[14]=v[14],q[15]=v[15],q},Ge=function(I){return I[0]=1,I[1]=0,I[2]=0,I[3]=0,I[4]=0,I[5]=1,I[6]=0,I[7]=0,I[8]=0,I[9]=0,I[10]=1,I[11]=0,I[12]=0,I[13]=0,I[14]=0,I[15]=1,I},zj=function(D,s){var p=D[0],o=D[1],T=D[2];return D[0]=s[0]*p+s[4]*o+s[8]*T+s[12],D[1]=s[1]*p+s[5]*o+s[9]*T+s[13],D[2]=s[2]*p+s[6]*o+s[10]*T+s[14],D},Ke=function(W,R){var p=W[0],s=W[1],y=W[2],c=W[3];return W[0]=R[0]*p+R[4]*s+R[8]*y+R[12]*c,W[1]=R[1]*p+R[5]*s+R[9]*y+R[13]*c,W[2]=R[2]*p+R[6]*s+R[10]*y+R[14]*c,W[3]=R[3]*p+R[7]*s+R[11]*y+R[15]*c,W},Lk=function(){var R,k,S,A,i=kk(_g[1]+_g[7]),T=kk(_g[0]+_g[3]-_g[10]),b=kk(_g[8]+2),c=function(){return k=S.charAt(R),R+=1,k},e=function(){var E="";if(k===b)for(;c();){if(k===b)return c(),E;E+=k}else c()},o=function(){for(;k&&" ">=k;)c()},F=function(){var M,v={};if(k===i){if(c(),o(),k===T)return c(),v;for(;k;){if(M=e(),o(),c(),v[M]=A(),o(),k===T)return c(),v;c(),o()}}};return A=function(){switch(o(),k){case i:return F();default:return e()}},function(T){if(s={},T){var l;if(S=T,R=0,k=" ",l=A(),o(),!k)return l}}}(),_b=function(j,d){if(d){var F=n(d),m=N(d),X=j[4],S=j[5],T=j[6],l=j[7],u=j[8],f=j[9],s=j[10],M=j[11];j[4]=X*m+u*F,j[5]=S*m+f*F,j[6]=T*m+s*F,j[7]=l*m+M*F,j[8]=u*m-X*F,j[9]=f*m-S*F,j[10]=s*m-T*F,j[11]=M*m-l*F}},fj=function(L,M){if(M){var y=n(M),m=N(M),c=L[0],h=L[1],x=L[2],I=L[3],W=L[8],d=L[9],A=L[10],w=L[11];L[0]=c*m-W*y,L[1]=h*m-d*y,L[2]=x*m-A*y,L[3]=I*m-w*y,L[8]=c*y+W*m,L[9]=h*y+d*m,L[10]=x*y+A*m,L[11]=I*y+w*m}},Bn=function(I,M){if(M){var J=n(M),O=N(M),u=I[0],E=I[1],o=I[2],b=I[3],R=I[4],r=I[5],q=I[6],j=I[7];I[0]=u*O+R*J,I[1]=E*O+r*J,I[2]=o*O+q*J,I[3]=b*O+j*J,I[4]=R*O-u*J,I[5]=r*O-E*J,I[6]=q*O-o*J,I[7]=j*O-b*J}},Sj=function(D,F,o){return lq(f,o===!1?f:D.s3(),D.r3(),D.getRotationMode(),D.p3(),f,F)},lq=function(u,c,E,y,I,C,b){return C||(C=Vp()),I&&zg(C,I),fs(C,E,y),b&&gh(C,C,b),c&&_k(C,c),u&&gh(C,C,u),C},Mh=function(A,b,g){b=c(b),g=c(g);var U=this;U.g=A,U._84O=b,U._85O=g,U._70I=!0,U.F=0,U._83O=b+g,U.pen={x:0,y:0}},Br="lineDashOffset",ir="setLineDash",pp=function(R){for(var v in R)1===v.length&&(Br=R[v]);return v?1:0},ad=function(r,E,t){return Ur(E)?r:r[ir]?(r[ir](E),t&&(r.lineDashOffset=t),r):new Mh(r,E[0],E.length>1?E[1]:E[0])},Hd=function(o,C){!Ur(C)&&o[ir]&&(o[ir](Zm),o.lineDashOffset=0)};gf(Mh,M,{_69I:6,moveTo:function(m,s){var L=this,N=L.pen;N.x=m,N.y=s,L.g.moveTo(m,s),L.start||(L.start={x:m,y:s})},lineTo:function(h,c){var Y=this,k=Y.pen,y=h-k.x,x=c-k.y,p=q(x,y),D=N(p),m=n(p),K=Y._23O(k.x,k.y,h,c),G=Y._85O,U=Y._84O,b=Y._83O;if(Y.F){if(Y.F>K)return Y._70I?Y._72I(h,c):Y.moveTo(h,c),Y.F-=K,void 0;if(Y._70I?Y._72I(k.x+D*Y.F,k.y+m*Y.F):Y.moveTo(k.x+D*Y.F,k.y+m*Y.F),K-=Y.F,Y.F=0,Y._70I=!Y._70I,!K)return}var E=T(K/b);if(E){for(var l=D*U,r=m*U,M=D*G,O=m*G,w=0;E>w;w++)Y._70I?(Y._72I(k.x+l,k.y+r),Y.moveTo(k.x+M,k.y+O)):(Y.moveTo(k.x+M,k.y+O),Y._72I(k.x+l,k.y+r));K-=b*E}Y._70I?K>U?(Y._72I(k.x+D*U,k.y+m*U),Y.moveTo(h,c),Y.F=G-(K-U),Y._70I=!1):(Y._72I(h,c),K===U?(Y.F=0,Y._70I=!Y._70I):(Y.F=U-K,Y.moveTo(h,c))):K>G?(Y.moveTo(k.x+D*G,k.y+m*G),Y._72I(h,c),Y.F=U-(K-G),Y._70I=!0):(Y.moveTo(h,c),K===G?(Y.F=0,Y._70I=!Y._70I):Y.F=G-K)},quadraticCurveTo:function(I,G,n,X){var t,g=this,A=g.pen,V=A.x,e=A.y,f=g._22O(V,e,I,G,n,X),S=0,$=0,d=g._85O,L=g._84O;if(g.F){if(g.F>f)return g._70I?g._71I(I,G,n,X):g.moveTo(n,X),g.F-=f,void 0;if(S=g.F/f,t=g._20O(V,e,I,G,n,X,S),g._70I?g._71I(t[2],t[3],t[4],t[5]):g.moveTo(t[4],t[5]),g.F=0,g._70I=!g._70I,!f)return}var R=f-f*S,u=T(R/g._83O),E=L/f,k=d/f;if(u)for(var q=0;u>q;q++)g._70I?($=S+E,t=g._21O(V,e,I,G,n,X,S,$),g._71I(t[2],t[3],t[4],t[5]),S=$,$=S+k,t=g._21O(V,e,I,G,n,X,S,$),g.moveTo(t[4],t[5])):($=S+k,t=g._21O(V,e,I,G,n,X,S,$),g.moveTo(t[4],t[5]),S=$,$=S+E,t=g._21O(V,e,I,G,n,X,S,$),g._71I(t[2],t[3],t[4],t[5])),S=$;R=f-f*S,g._70I?R>L?($=S+E,t=g._21O(V,e,I,G,n,X,S,$),g._71I(t[2],t[3],t[4],t[5]),g.moveTo(n,X),g.F=d-(R-L),g._70I=!1):(t=g._19O(V,e,I,G,n,X,S),g._71I(t[2],t[3],t[4],t[5]),f===L?(g.F=0,g._70I=!g._70I):(g.F=L-R,g.moveTo(n,X))):R>d?($=S+k,t=g._21O(V,e,I,G,n,X,S,$),g.moveTo(t[4],t[5]),t=g._19O(V,e,I,G,n,X,$),g._71I(t[2],t[3],t[4],t[5]),g.F=L-(R-d),g._70I=!0):(g.moveTo(n,X),R===d?(g.F=0,g._70I=!g._70I):g.F=d-R)},bezierCurveTo:function(){var l=arguments;this.pen={x:l[4],y:l[5]},this.g.bezierCurveTo(l[0],l[1],l[2],l[3],l[4],l[5])},arc:function(A,C,W,n,O,h){h||(n=-n,O=-O),uo(this,A,C,n,O-n,W,W,!1)},rect:function(r,E,D,p){var Q=this;Q.pen={x:r,y:E},Q.moveTo(r,E),Q.lineTo(r,E+p),Q.lineTo(r+D,E+p),Q.lineTo(r+D,E),Q.lineTo(r,E)},beginPath:function(){this.g.beginPath()},closePath:function(){this.lineTo(this.start.x,this.start.y)},_23O:function(a,K,M,x){var O=M-a,p=x-K;return F(O*O+p*p)},_22O:function(d,z,U,L,H,x,G){for(var e,C,I,F,Y,o,p,k=0,n=d,E=z,j=G>0?G:this._69I,O=1;j>=O;O++)I=O/j,F=1-I,Y=F*F,o=2*I*F,p=I*I,e=Y*d+o*U+p*H,C=Y*z+o*L+p*x,k+=this._23O(n,E,e,C),n=e,E=C;return k},_21O:function(R,u,M,L,U,T,s,d){var J=this;if(0===s)return J._20O(R,u,M,L,U,T,d);if(1===d)return J._19O(R,u,M,L,U,T,s);var B=J._20O(R,u,M,L,U,T,d);return B.push(s/d),J._19O.apply(J,B)},_20O:function(M,U,C,f,K,V,u){if(1!==u){var g=C+(K-C)*u,$=f+(V-f)*u;C=M+(C-M)*u,f=U+(f-U)*u,K=C+(g-C)*u,V=f+($-f)*u}return[M,U,C,f,K,V]},_19O:function(R,l,M,u,t,w,q){if(1!==q){var $=R+(M-R)*q,j=l+(u-l)*q;M+=(t-M)*q,u+=(w-u)*q,R=$+(M-$)*q,l=j+(u-j)*q}return[R,l,M,u,t,w]},_72I:function(h,p){var O=this.pen;(h!==O.x||p!==O.y)&&(O.x=h,O.y=p,this.g.lineTo(h,p))},_71I:function(s,t,Y,A){var $=this.pen;(s!==Y||t!==A||Y!==$.x||A!==$.y)&&($.x=Y,$.y=A,this.g.quadraticCurveTo(s,t,Y,A))}});var Jg=I.chart,xj=u.compStack=[],Jq=/^style@/,Mp=/^attr@/,Mr=/^field@/,eq=function(_,V){return _?Fe(_.width,V):0},Oo=function(G,O){return G?Fe(G.height,O):0},Fe=function(U,g,H){if(!U||!U.func)return U;var I,L=U.func,y=U.value;return I=ll(L)?L(g,H):g?Jq.test(L)?g.s(L.slice(6)):Mp.test(L)?g.a(L.slice(5)):Mr.test(L)?g[L.slice(6)]:g[L]?g[L](H):y:y,y!==j&&I==f?y:I},tb=u.drawImage=function(){var r,s,U,i,w,S=function(_,K,Y){var z=_[K];return z&&z.func?(z=Fe(z,s,U),Y&&(z=Y(z))):Y&&(z=_[K]=Y(z)),z},j=function(l,U){var v=S(l,U);if(w&&v){var M=Yd(w);return v=xd(v),"rgba("+T(v[0]*M[0])+","+T(v[1]*M[1])+","+T(v[2]*M[2])+","+v[3]+")"}return v},E=function(P){if(Ji(P)){for(var A=new Xc,D=P.length,Q=0;D>Q;Q+=2)A.add({x:P[Q],y:P[Q+1]});P=A}return P},h=function(X,d){var C=S(X,ik);if(Ji(C)){var P=C.length,j=S(X,"relative"),l=d.width,t=d.height;if(4===P)C={x:C[0],y:C[1],width:C[2],height:C[3]},j&&(C.x*=l,C.y*=t,C.width*=l,C.height*=t);else if(3===P){var B=C[0];C={width:C[1],height:C[2]},j&&(C.width*=l,C.height*=t),B=fb(B,d,C),C.x=B.x-C.width/2,C.y=B.y-C.height/2}var E=S(X,"offsetX");E&&(C.x+=E),E=S(X,"offsetY"),E&&(C.y+=E)}return C},J=function(M){return Ji(M)?new Xc(M):M},A=function(u,E){if(u){var O=E.x+E.width/2,F=E.y+E.height/2;r.save(),On(r,O,F),qr(r,u),On(r,-O,-F)}},p=function(s){s&&r.restore()},W=function(F,k,q){var Z=S(F,Tq,E),B=S(F,Qg),s=k===xh,G=f;if(!q&&s&&(q=F.unionRect,q||(q=ai(Z),F.points.func||(F.unionRect=q)),G=q),q){s?G||(G=F.unionRect,G||(G=ai(Z),F.points.func||(F.unionRect=G))):G=q;var C=nn(G,q);if(C)A(B,G);else{var o=G.width,Q=G.height,X=q.width,W=q.height,g=X/o,x=W/Q;r.save(),On(r,q.x+X/2,q.y+W/2),r.scale(g,x),B&&qr(r,B),On(r,-G.x-o/2,-G.y-Q/2)}var b,H,c,V,u=S(F,"borderPattern"),z=ad(r,u),_=j(F,"background"),N=cj(S(F,"repeatImage"),w),M=j(F,"borderColor"),P=S(F,"borderWidth"),T=S(F,"segments",J),t=S(F,"gradient"),Y=j(F,"gradientColor"),n=S(F,"border3d"),R=S(F,"border3dColor"),h=S(F,"border3dAccuracy"),U=S(F,"closePath"),y=r.lineJoin,L=r.lineCap;if(r.lineJoin=S(F,"borderJoin")||Xh,r.lineCap=S(F,"borderCap")||Tj,s?_||N?(N?pc(r,N):Yk(r,_,t,Y,G),qh(r,Z,T,U),r.fill(),z!==r&&qh(z,Z,T,U)):qh(z,Z,T,U):("roundRect"===k?b=S(F,"cornerRadius"):"polygon"===k?b=S(F,"polygonSide"):"arc"===k&&(b=S(F,"arcFrom"),H=S(F,"arcTo"),c=S(F,"arcClose"),V=S(F,"arcOval")),_||N?(N?pc(r,N):Yk(r,_,t,Y,G),Zn(r,k,G,b,H,c,V),r.fill(),r!==z&&Zn(z,k,G,b,H,c,V)):Zn(z,k,G,b,H,c,V)),P&&M&&(r.lineWidth=P,r.strokeStyle=M,r.stroke(),n&&Xj(r,M,R,P,i,h)),Hd(r,u),S(F,"dash")){var v=S(F,"dashWidth")||P;if(v>0){u=S(F,"dashPattern")||Ri;var z=ad(r,u,S(F,"dashOffset")),K=S(F,"dashColor")||lp;z!==r&&(s?qh(z,Z,T,U):Zn(z,k,G,b,H,c,V)),r.strokeStyle=K,r.lineWidth=v,r.stroke(),S(F,"dash3d")&&Xj(r,K,S(F,"dash3dColor"),v,i,S(F,"dash3dAccuracy")),Hd(r,u)}}k===ik&&Um(r,_,S(F,"depth"),G),r.lineJoin=y,r.lineCap=L,C?p(B):r.restore()}},P=function(F,B){var x=w,c=w||S(F,pk),E=cj(S(F,"name"),c);E&&(Li(r,E,S(F,"stretch"),B.x,B.y,B.width,B.height,s,U,c),w=x)},d=function(Q,D){var l=S(Q,"text");l!=f&&tj(r,l,S(Q,"font"),j(Q,pk),D.x,D.y,D.width,D.height,S(Q,"align"),S(Q,"vAlign"))},_=function(Y,k){mb(r,j(Y,pk),k.x,k.y,k.width,k.height,S(Y,"width"))},m=function(i,P){var Y=S(i,ld),u=0;if(Y&&(Y.forEach(function(I){u+=I}),u>0)){for(var E=S(i,"colors")||Jg,j=S(i,"startAngle")||0,k=S(i,"hollow"),$=S(i,Jm),X=S(i,id),K=S(i,bg),M=$?new Xc:f,U=P.x,g=P.y,e=P.width,x=P.height,t=U+e/2,o=g+x/2,l=O(e,x)/2,V=0,p=0;p<Y.length;p++){var m=Y[p],_=z*m/u,y=j+_;if(r.fillStyle=E[V++],V===E.length&&(V=0),r.beginPath(),k){var a=t+N(j)*l/2,F=o+n(j)*l/2,L=t+N(y)*l,J=o+n(y)*l;r.moveTo(a,F),r.arc(t,o,l/2,j,y,!1),r.lineTo(L,J),r.arc(t,o,l,y,j,!0)}else r.moveTo(t,o),r.arc(t,o,l,y,j,!0);M&&(_=(j+y)/2,M.add({text:ll($)?$(m,p,u,s):m,x:t+.75*N(_)*l,y:o+.75*n(_)*l})),r.closePath(),r.fill(),j=y}M&&M.each(function(H){tj(r,H.text,X,K,H.x,H.y,0,0,Hb)})}},x=function(c,q){var H=S(c,Lj);if(H&&H.length>0){var K=H.length,m=S(c,Jm),I=S(c,id),j=S(c,bg),X=m?new Xc:f,D=S(c,"minValue")||0,a=S(c,"maxValue");if(a==f&&(a=0,H.forEach(function(W){W.values.forEach(function(Y){a=b(a,Y)})})),D===a)return;for(var n=q.height/(a-D),w=q.y+a*n,Z=S(H[0],ld).length,G=q.width/(3*Z+1),N=2*G/K,k=0,F=0;K>F;F++)for(var y=H[F],U=S(y,pk),J=S(y,"colors"),z=S(y,ld),h=0;Z>h;h++){J?r.fillStyle=J[h]:U?r.fillStyle=U:(r.fillStyle=Jg[k++],k===Jg.length&&(k=0));var V=z[h],M=V*n,T=q.x+(1+3*h)*G+F*N;if($i(r,T,w-M,N,M),X){var t=ll(m)?m(V,h,y,s):V,g=Hn(I,t).height;X.add({x:T,y:w-M-g,width:N,height:g,text:t})}}X&&X.each(function(S){tj(r,S.text,I,j,S.x,S.y,S.width,S.height,Hb)})}},o=function(w,Q){var Z=S(w,Lj);if(Z&&Z.length>0){var q=Z.length,d=S(Z[0],ld).length,F=Q.width/(3*d+1),O=0,n=S(w,"maxValue"),E=S(w,Jm),N=S(w,id),A=S(w,bg),l=E?new Xc:f;if(n==f){n=0;for(var z=0;d>z;z++){for(var B=0,o=0;q>o;o++)B+=S(Z[o],ld)[z];n=b(n,B)}}if(n>0){for(var z=0;d>z;z++)for(var p=Q.y+Q.height,o=0;q>o;o++){var H=Z[o],k=S(H,pk),t=S(H,ld)[z],u=t/n*Q.height;k?r.fillStyle=k:(r.fillStyle=Jg[O++],O===Jg.length&&(O=0)),p-=u;var $={x:Q.x+(1+3*z)*F,y:p,width:2*F,height:u};$i(r,$.x,$.y,$.width,$.height),l&&($.text=ll(E)?E(t,z,H,s):t,l.add($))}l&&l.each(function(H){tj(r,H.text,N,A,H.x,H.y,H.width,H.height,Hb)})}}},M=function(D,k){var o=S(D,Lj);if(o&&o.length>0){for(var K=o.length,c=S(o[0],ld).length,Q=k.width/(3*c+1),C=0,F=S(D,Jm),y=S(D,id),b=S(D,bg),g=F?new Xc:f,q=0;c>q;q++){for(var p=0,Z=0;K>Z;Z++)p+=S(o[Z],ld)[q];if(p>0){var N=k.y+k.height;for(Z=0;K>Z;Z++){var t=o[Z],M=S(t,pk),Y=S(t,ld)[q],d=Y/p*k.height;M?r.fillStyle=M:(r.fillStyle=Jg[C++],C===Jg.length&&(C=0)),N-=d;var J={x:k.x+(1+3*q)*Q,y:N,width:2*Q,height:d};$i(r,J.x,J.y,J.width,J.height),g&&(J.text=ll(F)?F(Y,q,t,s):Y,g.add(J))}}}g&&g.each(function(o){tj(r,o.text,y,b,o.x,o.y,o.width,o.height,Hb)})}},V=function(o,W){var c=S(o,Lj);if(c&&c.length>0){var d=c.length,T=S(o,"minValue")||0,H=S(o,"maxValue");if(H==f&&(H=0,c.forEach(function(L){L.values.forEach(function(j){H=b(H,j)})})),T===H)return;for(var R=W.height/(H-T),F=W.y+H*R,p=S(c[0],ld).length,D=W.width/(3*p+1),K=0,a=S(o,"lineWidth")||2,h=S(o,"line3d"),G=S(o,"linePoint"),w=S(o,Jm),C=S(o,id),e=S(o,bg),_=0;d>_;_++){var y=c[_],g=S(y,pk),j=S(y,ld);g?r.strokeStyle=g:(g=r.strokeStyle=Jg[K++],K===Jg.length&&(K=0)),r.beginPath();for(var k=0;p>k;k++){var v=W.x+(2+3*k)*D,t=F-j[k]*R;0===k?r.moveTo(v,t):r.lineTo(v,t)}if(r.lineWidth=a,r.stroke(),h&&Xj(r,g,f,a,i),G||w){var X,E=a/2+2;for(k=0;p>k;k++){var x=j[k];if(v=W.x+(2+3*k)*D,t=F-x*R,ll(G)?G(r,v,t,g,k,y,s):G&&(r.fillStyle=g,r.beginPath(),r.arc(v,t,E,0,z,!0),r.fill()),ll(w)?X=w(x,k,y,s):w&&(X=x),X){var M=Hn(C,X).height,I=r.shadowBlur;if(I){var U=r.shadowOffsetX,V=r.shadowOffsetY,n=r.shadowColor;r.shadowOffsetX=0,r.shadowOffsetY=0,r.shadowBlur=0,r.shadowColor=f}tj(r,X,C,e,v,t-M-E+2,0,M,Hb),I&&(r.shadowOffsetX=U,r.shadowOffsetY=V,r.shadowColor=n,r.shadowBlur=I)}}}}}},L={border:_,image:P,text:d,pieChart:m,columnChart:x,stackedColumnChart:o,percentageColumnChart:M,lineChart:V};return function(R,k,$,t,O,Y,v,g,N){if(k&&O&&Y){if(r=R,s=v,U=g,i=U?U._zoom?U._zoom:1:1,w=N,k.tagName)return r.drawImage(k,$,t,O,Y),void 0;if(S(k,"visible")!==!1){w||(w=Fe(k.color,s,U));var E=eq(k,s),D=Oo(k,s),M={x:0,y:0,width:E,height:D},x=S(k,"clip"),F=S(k,"opacity");r.save(),On(r,$,t),(E!==O||D!==Y)&&r.scale(O/E,Y/D),x&&(ll(x)?x(r,E,D,s,U,k):(r.beginPath(),r.rect(0,0,E,D),r.clip())),F!=f&&(r.globalAlpha*=F),S(k,"comps").forEach(function(Z){if(S(Z,$g)!==!1){xj.splice(0,0,Z);var V=S(Z,"opacity"),b=S(Z,"shadow"),q=S(Z,"type"),k=h(Z,M);if(V!=f){var N=r.globalAlpha;
r.globalAlpha*=V}if(b){var $=r.shadowOffsetX,i=r.shadowOffsetY,C=r.shadowBlur,n=r.shadowColor,E=S(Z,"shadowOffsetX"),t=S(Z,"shadowOffsetY"),j=S(Z,"shadowBlur"),x=S(Z,"shadowColor");r.shadowOffsetX=E==f?3:E,r.shadowOffsetY=t==f?3:t,r.shadowBlur=j==f?6:j,r.shadowColor=x||Jo}if(ll(q))q(r,k,Z,s,U);else if(u.getCompType(q))u.getCompType(q)(r,k,Z,s,U);else if(jh[q]||q===xh)W(Z,q,k);else if(L[q]&&k){var B=S(Z,Qg);A(B,k),L[q](Z,k),p(B)}b&&(r.shadowOffsetX=$,r.shadowOffsetY=i,r.shadowBlur=C,r.shadowColor=n),V!=f&&(r.globalAlpha=N),xj.splice(0,1)}}),r.restore()}}}}();u.getCurrentComp=function(){return xj[0]},u.getParentComp=function(){return xj[1]},u.getInternal=function(){return{isEnter:Kn,isEsc:dn,isSpace:td,isLeft:qn,isUp:so,isRight:zk,isDown:uq,addMethod:Ym,superCall:Wn,toPointsArray:Rr,translateAndScale:lk,appendArray:ul,createWorldMatrix:lq,vec3TransformMat4:zj,setCanvas:Gr,createDiv:Xb,createCanvas:Pf,createImage:Gh,initContext:cs,layout:tf,fillRect:$i,Mat:pf,drawBorder:mb,isString:sl,setBorder:Kp,getPropertyValue:ym,setPropertyValue:$o,drawVerticalLine:um,draw3DRect:Um,getPinchDist:Ao,isSameRect:nn,getPosition:fb,intersectionLineRect:bk,getNodeRect:Og,getImageWidth:eq,getImageHeight:Oo,formatNumber:pm,initItemElement:Oj,drawPoints:qh,createG2:ad,closePopup:xf,isH:Ci,createAnim:Bq,createNormalMatrix:en,createNormals:ib,toFloatArray:Jn,glMV:Be,batchShape:Nl,createNodeMatrix:Sj,getFaceInfo:Ql,transformAppend:dk,drawFaceInfo:ls,to3dPointsArray:Ag,setGLDebugMode:function(e){Jr=e},cube:function(){return{vs:ud,is:rp,uv:jr}},ui:function(){return{DataUI:cm,NodeUI:ao,EdgeUI:Ul,GroupUI:zd,ShapeUI:Ub,GridUI:Hf,Data3dUI:jp,Node3dUI:lo,Shape3dUI:mm}},getInternalVersion:function(){return"U2FsdGVkX1/s9dwSs/i4K7L/SNxf7h30Hz6Oh8+N8ExaBP4qOsMpM4iwkzbsuAxY"},getDragger:function(){return Bl},addMSMap:function(o){Ym(up,o)},k:A}},function(u){function g(E,d){E!=f&&(d==f&&Hm!=typeof E?this._54O(E,256):this._54O(E,d))}function C(){return new g(f)}function s(o,K,m,P,W,j){for(;--j>=0;){var R=K*this[o++]+m[P]+W;W=T(R/67108864),m[P++]=67108863&R}return W}function w(y,j,C,x,V,U){for(var m=32767&j,O=j>>15;--U>=0;){var J=32767&this[y],i=this[y++]>>15,v=O*J+i*m;J=m*J+((32767&v)<<15)+C[x]+(1073741823&V),V=(J>>>30)+(v>>>15)+O*i+(V>>>30),C[x++]=1073741823&J}return V}function H(t,A,u,F,w,q){for(var R=16383&A,o=A>>14;--q>=0;){var d=16383&this[t],J=this[t++]>>14,S=o*d+J*R;d=R*d+((16383&S)<<14)+u[F]+w,w=(d>>28)+(S>>14)+o*J,u[F++]=268435455&d}return w}function M(x){return Mi.charAt(x)}function L(w,T){var g=Tf[w.charCodeAt(T)];return g==f?-1:g}function $(u){for(var R=this.t-1;R>=0;--R)u[R]=this[R];u.t=this.t,u.s=this.s}function t(w){this.t=1,this.s=0>w?-1:0,w>0?this[0]=w:-1>w?this[0]=w+this.DV:this.t=0}function y(Z){var B=C();return B._58O(Z),B}function Z(R,B){var i,H=this;if(16==B)i=4;else if(8==B)i=3;else if(256==B)i=8;else if(2==B)i=1;else if(32==B)i=5;else{if(4!=B)return H.fromRadix(R,B),void 0;i=2}H.t=0,H.s=0;for(var h=R.length,J=!1,W=0;--h>=0;){var v=8==i?255&R[h]:L(R,h);0>v?"-"==R.charAt(h)&&(J=!0):(J=!1,0==W?H[H.t++]=v:W+i>H.DB?(H[H.t-1]|=(v&(1<<H.DB-W)-1)<<W,H[H.t++]=v>>H.DB-W):H[H.t-1]|=v<<W,W+=i,W>=H.DB&&(W-=H.DB))}8==i&&0!=(128&R[0])&&(H.s=-1,W>0&&(H[H.t-1]|=(1<<H.DB-W)-1<<W)),H._57O(),J&&g.ZERO._78O(H,H)}function X(){for(var k=this,x=k.s&k.DM;k.t>0&&k[k.t-1]==x;)--k.t}function v(W){var b=this;if(b.s<0)return"-"+b._85O()[mf](W);var i;if(16==W)i=4;else if(8==W)i=3;else if(2==W)i=1;else if(32==W)i=5;else{if(4!=W)return b.toRadix(W);i=2}var Z,x=(1<<i)-1,F=!1,T="",H=b.t,V=b.DB-H*b.DB%i;if(H-->0)for(V<b.DB&&(Z=b[H]>>V)>0&&(F=!0,T=M(Z));H>=0;)i>V?(Z=(b[H]&(1<<V)-1)<<i-V,Z|=b[--H]>>(V+=b.DB-i)):(Z=b[H]>>(V-=i)&x,0>=V&&(V+=b.DB,--H)),Z>0&&(F=!0),F&&(T+=M(Z));return F?T:"0"}function P(){var G=C();return g.ZERO._78O(this,G),G}function W(){return this.s<0?this._85O():this}function j(T){var R=this,v=R.s-T.s;if(0!=v)return v;var n=R.t;if(v=n-T.t,0!=v)return R.s<0?-v:v;for(;--n>=0;)if(0!=(v=R[n]-T[n]))return v;return 0}function o(k){var _,L=1;return 0!=(_=k>>>16)&&(k=_,L+=16),0!=(_=k>>8)&&(k=_,L+=8),0!=(_=k>>4)&&(k=_,L+=4),0!=(_=k>>2)&&(k=_,L+=2),0!=(_=k>>1)&&(k=_,L+=1),L}function V(){var t=this;return t.t<=0?0:t.DB*(t.t-1)+o(t[t.t-1]^t.s&t.DM)}function G(O,b){var Y;for(Y=this.t-1;Y>=0;--Y)b[Y+O]=this[Y];for(Y=O-1;Y>=0;--Y)b[Y]=0;b.t=this.t+O,b.s=this.s}function B(m,Z){for(var S=m;S<this.t;++S)Z[S-m]=this[S];Z.t=b(this.t-m,0),Z.s=this.s}function J(Y,Z){var Q,l=this,g=Y%l.DB,o=l.DB-g,D=(1<<o)-1,r=T(Y/l.DB),f=l.s<<g&l.DM;for(Q=l.t-1;Q>=0;--Q)Z[Q+r+1]=l[Q]>>o|f,f=(l[Q]&D)<<g;for(Q=r-1;Q>=0;--Q)Z[Q]=0;Z[r]=f,Z.t=l.t+r+1,Z.s=l.s,Z._57O()}function i(Z,r){var Y=this;r.s=Y.s;var q=T(Z/Y.DB);if(q>=Y.t)return r.t=0,void 0;var C=Z%Y.DB,e=Y.DB-C,u=(1<<C)-1;r[0]=Y[q]>>C;for(var j=q+1;j<Y.t;++j)r[j-q-1]|=(Y[j]&u)<<e,r[j-q]=Y[j]>>C;C>0&&(r[Y.t-q-1]|=(Y.s&u)<<e),r.t=Y.t-q,r._57O()}function r(f,e){for(var m=this,J=0,a=0,z=O(f.t,m.t);z>J;)a+=m[J]-f[J],e[J++]=a&m.DM,a>>=m.DB;if(f.t<m.t){for(a-=f.s;J<m.t;)a+=m[J],e[J++]=a&m.DM,a>>=m.DB;a+=m.s}else{for(a+=m.s;J<f.t;)a-=f[J],e[J++]=a&m.DM,a>>=m.DB;a-=f.s}e.s=0>a?-1:0,-1>a?e[J++]=m.DV+a:a>0&&(e[J++]=a),e.t=J,e._57O()}function Y(d,X){var I=this.abs(),O=d.abs(),p=I.t;for(X.t=p+O.t;--p>=0;)X[p]=0;for(p=0;p<O.t;++p)X[p+I.t]=I.am(0,O[p],X,p,0,I.t);X.s=0,X._57O(),this.s!=d.s&&g.ZERO._78O(X,X)}function D(t){for(var f=this.abs(),M=t.t=2*f.t;--M>=0;)t[M]=0;for(M=0;M<f.t-1;++M){var Z=f.am(M,f[M],t,2*M,0,1);(t[M+f.t]+=f.am(M+1,2*f[M],t,2*M+1,Z,f.t-M-1))>=f.DV&&(t[M+f.t]-=f.DV,t[M+f.t+1]=1)}t.t>0&&(t[t.t-1]+=f.am(M,f[M],t,2*M,0,1)),t.s=0,t._57O()}function p(x,i,U){var n=x.abs(),u=this;if(!(n.t<=0)){var J=u.abs();if(J.t<n.t)return i!=f&&i._58O(0),U!=f&&u._77O(U),void 0;U==f&&(U=C());var H=C(),c=u.s,b=x.s,m=u.DB-o(n[n.t-1]);m>0?(n._44O(m,H),J._44O(m,U)):(n._77O(H),J._77O(U));var O=H.t,q=H[O-1];if(0!=q){var S=q*(1<<u.F1)+(O>1?H[O-2]>>u.F2:0),N=u.FV/S,y=(1<<u.F1)/S,h=1<<u.F2,X=U.t,G=X-O,L=i==f?C():i;for(H._59O(G,L),U._52O(L)>=0&&(U[U.t++]=1,U._78O(L,U)),g.ONE._59O(O,L),L._78O(H,H);H.t<O;)H[H.t++]=0;for(;--G>=0;){var I=U[--X]==q?u.DM:T(U[X]*N+(U[X-1]+h)*y);if((U[X]+=H.am(0,I,U,G,0,O))<I)for(H._59O(G,L),U._78O(L,U);U[X]<--I;)U._78O(L,U)}i!=f&&(U._45O(O,i),c!=b&&g.ZERO._78O(i,i)),U.t=O,U._57O(),m>0&&U._46O(m,U),0>c&&g.ZERO._78O(U,U)}}}function q(M){var A=C();return this.abs()._49O(M,f,A),this.s<0&&A._52O(g.ZERO)>0&&M._78O(A,A),A}function K(m){this.m=m}function Q(M){return M.s<0||M._52O(this.m)>=0?M.mod(this.m):M}function R(D){return D}function E(b){b._49O(this.m,f,b)}function S(N,g,H){N._47O(g,H),this._74O(H)}function U(q,u){q._48O(u),this._74O(u)}function x(){if(this.t<1)return 0;var r=this[0];if(0==(1&r))return 0;var U=3&r;return U=15&U*(2-(15&r)*U),U=255&U*(2-(255&r)*U),U=65535&U*(2-(65535&(65535&r)*U)),U=U*(2-r*U%this.DV)%this.DV,U>0?this.DV-U:-U}function h(Y){var K=this;K.m=Y,K.mp=Y._50O(),K.mpl=32767&K.mp,K.mph=K.mp>>15,K.um=(1<<Y.DB-15)-1,K.mt2=2*Y.t}function I(u){var X=C();return u.abs()._59O(this.m.t,X),X._49O(this.m,f,X),u.s<0&&X._52O(g.ZERO)>0&&this.m._78O(X,X),X}function _(u){var I=C();return u._77O(I),this._74O(I),I}function d(q){for(var P=this;q.t<=P.mt2;)q[q.t++]=0;for(var H=0;H<P.m.t;++H){var Z=32767&q[H],g=Z*P.mpl+((Z*P.mph+(q[H]>>15)*P.mpl&P.um)<<15)&q.DM;for(Z=H+P.m.t,q[Z]+=P.m.am(0,g,q,H,0,P.m.t);q[Z]>=q.DV;)q[Z]-=q.DV,q[++Z]++}q._57O(),q._45O(P.m.t,q),q._52O(P.m)>=0&&q._78O(P.m,q)}function Kq(C,S){C._48O(S),this._74O(S)}function eh(t,H,s){t._47O(H,s),this._74O(s)}function Pl(){return 0==(this.t>0?1&this[0]:this.s)}function rm(H,S){if(H>4294967295||1>H)return g.ONE;var i=C(),M=C(),_=S._73O(this),h=o(H)-1;for(_._77O(i);--h>=0;)if(S._76O(i,M),(H&1<<h)>0)S._75O(M,_,i);else{var J=i;i=M,M=J}return S.revert(i)}function Fb(H,B){var x;return x=256>H||B._51O()?new K(B):new h(B),this.exp(H,x)}function Xj(){var v=this;if(v.s<0){if(1==v.t)return v[0]-v.DV;if(0==v.t)return-1}else{if(1==v.t)return v[0];if(0==v.t)return 0}return(v[1]&(1<<32-v.DB)-1)<<v.DB|v[0]}function ne(b,N){return new g(b,N)}function Vp(){var p=this;p.n=f,p.e=0,p.d=f,p.p=f,p.q=f,p.dmp1=f,p.dmq1=f,p._10A=f}function gr(F){return F._53O(this.e,this.n)}function Oe(J){var j,P,A,H="",w=0;for(j=0;j<J.length&&J.charAt(j)!=Kr;++j)A=As.indexOf(J.charAt(j)),0>A||(0==w?(H+=M(A>>2),P=3&A,w=1):1==w?(H+=M(P<<2|A>>4),P=15&A,w=2):2==w?(H+=M(P),H+=M(A>>2),P=3&A,w=3):(H+=M(P<<2|A>>4),H+=M(15&A),w=0));return 1==w&&(H+=M(P<<2)),H}function Tm(_){var I=_.split(""),z=Oe(_);return I.forEach(function(n){var T=n.length;T>0&&z&&(Cd+=c(n))}),z}function lo(J,T){var c=Lp._4O.Util._56O(J,"ss"),L={},P=0;return L.v=c==T,L.t=1,L.s=0>P?-1:0,jn=L.v,P>0?L[0]=P:-1>P?L[0]=P+L.DV:L.t=0,[c,L]}function fp(W){var f=38,q=W.substring(0,f);if(q&&30==q.indexOf("05000420")){var l=["ss",W.substring(f)];return l}return[]}function no(t,E){E=E.replace(tg,""),E=E.replace(/[ \n]+/g,"");var u=ne(E,16);if(u._55O()>this.n._55O())return 0;var Y=this._37O(u),v=Y[mf](16).replace(/^1f+00/,""),j=fp(v);if(0==j.length)return!1;for(var y,R,w,x=.5,s=hn,P=[0,.5,.75,.875,.9375],C=[],W=[],r=[],M=z/s,H=j[1],p=lo(t,H)[0],l=0,I=0;l<P.length;l++){var J=0===l%2?0:.5;for(y=0;s>=y;y++)R=(y+J)*M,w=1-P[l],C.push(N(R)*x*w,-x+2*P[l]*x,-n(R)*x*w),W.push((y+J)/s,w)}for(l=0;l<P.length-1;l++){var B=l*(s+1),f=(l+1)*(s+1);for(y=0;s>y;y++)r.push(B+y,f+y+1,f+y,B+y,B+y+1,f+y+1)}return r.forEach(function(H){I+=H}),H==p&&I>10}var up,ki=0xdeadbeefcafe,mo=15715070==(16777215&ki),mf="toString",pe="",Zb="nat",Mo=function(){};e=u["D"+11182[mf](l(2,5))];var Id=g.prototype;Aj+=Uk.substr(0,1);var Ho=u.navigator?u.navigator.appName:"";mo&&"Microsoft Internet Explorer"==Ho?(Id.am=w,up=30):mo&&"Netscape"!=Ho?(Id.am=s,up=26):(Id.am=H,up=28),Id.DB=up,Id.DM=(1<<up)-1,Id.DV=1<<up;var vr=52;Id.FV=l(2,vr),Id.F1=vr-up,Id.F2=2*up-vr;var ai,oo,Mi="0123456789abcdefghijklmnopqrstuvwxyz",Tf=[],fh=function(C){return String.fromCharCode(C)};for(ai="0".charCodeAt(0),oo=0;9>=oo;++oo)Tf[ai++]=oo;for(ai="a".charCodeAt(0),oo=10;36>oo;++oo)Tf[ai++]=oo;for(ai="A".charCodeAt(0),oo=10;36>oo;++oo)Tf[ai++]=oo;var Zc=K.prototype;Zc._73O=Q,Zc.revert=R,Zc._74O=E,Zc._75O=S,Zc._76O=U;var Pf=h.prototype;Pf._73O=I,Pf.revert=_,Pf._74O=d,Pf._75O=eh,Pf._76O=Kq,Id._77O=$,Id._58O=t,Id._54O=Z,Id._57O=X,Id._59O=G,Id._45O=B,Id._44O=J,Id._46O=i,Id._78O=r,Id._47O=Y,Id._48O=D,Id._49O=p,Id._50O=x,Id._51O=Pl,Id.exp=rm,Id.toString=v,Id._85O=P,Id.abs=W,Id._52O=j,Id._55O=V,Id.mod=q,Id._53O=Fb,g.ZERO=y(0),g.ONE=y(1),Id._86O=Xj;var Tg=function(k,Y){var W=this;W.isPublic=!0,typeof k!==Hm?(W.n=k,W.e=Y):k!=f&&Y!=f&&k.length>0&&Y.length>0&&(W.n=ne(k,16),W.e=c(Y,16))};fg=function(){var L,U,p=Tm(ag),I=p.substr(0,4),t=p.substr(4,2),a=p.substr(6,2),b=1,H=!b,y=A,d=[],f=Gc.charAt(7);if(e&&(e[mf]().indexOf(Zb)<0||e[Aj][mf]().indexOf(Zb)<0||!p?L=Lk(hp[f]):(p=new e(I-0,t-b,a-0),U=p.setHours(9),e[Aj]()>U?L=Lk(hp[f]):H=!0)),p&&L&&y){for(var F in L)d.push(F);var z,R=L[d[0]],M=L[d[1]],G=L[d[2]],P=L[d[4]],J=L[d[5]],h=L[d[6]],r=ws._27O(y);r&&h&&(z=R+M+G+P+""+J,z&&r._31O(z,h)&&(H=!0))}return H||(Zn=Ur),f};var js=Vp.prototype;js._37O=gr,js._38O=Tg;var As="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Kr="=",Mc=25,lr=10,ag="IBUQAQ==",Lr=Lr||function(V,I){var x={},h=x._7A={},H=h._6A=function(){function t(){}return{_80O:function(L){t.prototype=this;var n=new t;return L&&n._5A(L),n.hasOwnProperty("_82O")||(n._82O=function(){n.$super._82O.apply(this,arguments)}),n._82O.prototype=n,n.$super=this,n},_3A:function(){var z=this._80O();return z._82O.apply(z,arguments),z},_82O:function(){},_5A:function(Y){for(var d in Y)Y.hasOwnProperty(d)&&(this[d]=Y[d]);Y.hasOwnProperty(mf)&&(this.toString=Y.toString)},_88O:function(){return this._82O.prototype._80O(this)}}}(),y=h._39O=H._80O({_82O:function(c,t){c=this._84O=c||[],this._65O=t!=I?t:4*c.length},toString:function(N){return(N||u).stringify(this)},_89O:function(W){var k=this._84O,B=W._84O,M=this._65O,q=W._65O;if(this._57O(),M%4)for(var G=0;q>G;G++){var Y=255&B[G>>>2]>>>24-8*(G%4);k[M+G>>>2]|=Y<<24-8*((M+G)%4)}else if(B.length>65535)for(var G=0;q>G;G+=4)k[M+G>>>2]=B[G>>>2];else k.push.apply(k,B);return this._65O+=q,this},_57O:function(){var j=this._84O,N=this._65O;j[N>>>2]&=4294967295<<32-8*(N%4),j.length=a(N/4)},_88O:function(){var K=H._88O.call(this);return K._84O=this._84O.slice(0),K},_87O:function(g){for(var D=[],z=0;g>z;z+=4)D.push(0|4294967296*m);return new y._82O(D,g)}}),K=x._2A={},u=K._69O={stringify:function(J){var P,j=J._84O,p=J._65O,i=[];for(P=0;p>P;P++){var L=255&j[P>>>2]>>>24-8*(P%4);i.push((L>>>4).toString(16)),i.push((15&L).toString(16))}return i.join("")},_68O:function(g){for(var r=g.length,R=[],H=0;r>H;H+=2)R[H>>>3]|=c(g.substr(H,2),16)<<24-4*(H%8);return new y._82O(R,r/2)}},U=K._8A={stringify:function(c){var x,s=c._84O,l=c._65O,Z=[];for(x=0;l>x;x++){var h=255&s[x>>>2]>>>24-8*(x%4);Z.push(fh(h))}return Z.join("")},_68O:function(i){var C,B=i.length,c=[];for(C=0;B>C;C++)c[C>>>2]|=(255&i.charCodeAt(C))<<24-8*(C%4);return new y._82O(c,B)}},$=K._9A={stringify:function(W){try{return decodeURIComponent(escape(U.stringify(W)))}catch(q){throw new Error("")}},_68O:function(V){return U._68O(unescape(encodeURIComponent(V)))}},F=h._32O=H._80O({_1A:function(){this._83O=new y._82O,this._23O=0},_33O:function(Y){typeof Y==Hm&&(Y=$._68O(Y)),this._83O._89O(Y),this._23O+=Y._65O},_25O:function(E){var Q=this._83O,C=Q._84O,F=Q._65O,o=this._79O,z=4*o,d=F/z;d=E?a(d):b((0|d)-this._22O,0);var $=d*o,B=O(4*$,F);if($){for(var x=0;$>x;x+=o)this._20O(C,x);var P=C.splice(0,$);Q._65O-=B}return new y._82O(P,B)},_88O:function(){var t=H._88O.call(this);return t._83O=this._83O._88O(),t},_22O:0});h._66O=F._80O({cfg:H._80O(),_82O:function(f){this.cfg=this.cfg._80O(f),this._1A()},_1A:function(){F._1A.call(this),this._34O()},_81O:function(y){return this._33O(y),this._25O(),this},_72O:function(n){n&&this._33O(n);var O=this._21O();return O},_79O:16,_26O:function(x){return function(P,Y){return new x._82O(Y)._72O(P)}},_19O:function(P){return function(Z,K){return new d._71O._82O(P,K)._72O(Z)}}});var d=x._67O={};return x}();!function(){var U=Lr,A=U._7A,n=A._39O,V=A._66O,c=U._67O,y=[],P=[];!function(){function h(u){for(var r=F(u),l=2;r>=l;l++)if(!(u%l))return!1;return!0}function L(m){return 0|4294967296*(m-(0|m))}for(var G=2,U=0;64>U;)h(G)&&(8>U&&(y[U]=L(l(G,.5))),P[U]=L(l(G,1/3)),U++),G++}();var Q=[],h=c._41O=V._80O({_34O:function(){this._64O=new n._82O(y.slice(0))},_20O:function(j,h){for(var W=this._64O._84O,O=W[0],D=W[1],c=W[2],N=W[3],F=W[4],V=W[5],k=W[6],n=W[7],C=0;64>C;C++){if(16>C)Q[C]=0|j[h+C];else{var S=Q[C-15],y=(S<<25|S>>>7)^(S<<14|S>>>18)^S>>>3,t=Q[C-2],l=(t<<15|t>>>17)^(t<<13|t>>>19)^t>>>10;Q[C]=y+Q[C-7]+l+Q[C-16]}var X=F&V^~F&k,$=O&D^O&c^D&c,e=(O<<30|O>>>2)^(O<<19|O>>>13)^(O<<10|O>>>22),Z=(F<<26|F>>>6)^(F<<21|F>>>11)^(F<<7|F>>>25),q=n+Z+X+P[C]+Q[C],i=e+$;n=k,k=V,V=F,F=0|N+q,N=c,c=D,D=O,O=0|q+i}W[0]=0|W[0]+O,W[1]=0|W[1]+D,W[2]=0|W[2]+c,W[3]=0|W[3]+N,W[4]=0|W[4]+F,W[5]=0|W[5]+V,W[6]=0|W[6]+k,W[7]=0|W[7]+n},_21O:function(){var Z=this._83O,K=Z._84O,p=8*this._23O,U=8*Z._65O;return K[U>>>5]|=128<<24-U%32,K[(U+64>>>9<<4)+14]=T(p/4294967296),K[(U+64>>>9<<4)+15]=p,Z._65O=4*K.length,this._25O(),this._64O},_88O:function(){var p=V._88O.call(this);return p._64O=this._64O._88O(),p}});U._41O=V._26O(h),U._42O=V._19O(h)}();var tg=new RegExp("");tg.compile("[^0-9a-f]","gi"),Vp._28O=-1,Vp._29O=-2,js._31O=no,js._63O=no,Vp._43O=-2;var mh=new function(){var Z=this;Z._5O=function(d,R){if("8"!=d.substring(R+2,R+3))return 1;var H=c(d.substring(R+3,R+4));return 0==H?-1:H>0&&10>H?H+1:-2},Z._13O=function(E,P){var a=Z._5O(E,P);return 1>a?"":E.substring(P+2,P+2+2*a)},Z._12O=function(t,M){var F=Z._13O(t,M);if(""==F)return-1;var s;return s=c(F.substring(0,1))<8?new g(F,16):new g(F.substring(2),16),s._86O()},Z._6O=function(E,c){var U=Z._5O(E,c);return 0>U?U:c+2*(U+1)},Z._11O=function(L,t){var i=Z._6O(L,t),G=Z._12O(L,t);return L.substring(i,i+2*G)},Z._10O=function(V,B){var w=Z._6O(V,B),e=Z._12O(V,B);return w+2*e},Z._7O=function(j,t){var s=[],P=Z._6O(j,t);s.push(P);for(var T=Z._12O(j,t),h=P,u=0;;){var v=Z._10O(j,h);if(v==f||v-P>=2*T)break;if(u>=200)break;s.push(v),h=v,u++}return s}};if(mh._90O=fh(lr+24),mh._91O=k+fh(lr*lr+8),Lp==f||!Lp)var Lp={};Lp._4O!=f&&Lp._4O||(Lp._4O={}),Lp._4O.Util=new function(){var K=this;K._56O=function(q){var j=new Lp._4O._3O;return j._30O(q)},K._4A=function(t){var S=new Lp._4O._3O;return S._36O(t)}},Lp._4O._3O=function(){var m=this;m._8O=function(K,$){if("ss"==K&&"cj"==$){try{m.md=Lr._67O._41O._3A()}catch(o){Mo(pe)}m._24O=function(K){m.md._81O(K)},m._35O=function(K){var X=Lr._2A._69O._68O(K);m.md._81O(X)},m._60O=function(){var X=m.md._72O();return X[mf](Lr._2A._69O)},m._30O=function(r){return m._24O(r),m._60O()},m._36O=function(g){return m._35O(g),m._60O()}}},m._24O=function(){Mo(pe)},m._35O=function(){Mo(pe)},m._60O=function(){Mo(pe)},m._30O=function(){Mo(pe)},m._36O=function(){Mo(pe)},m._8O("ss","cj")},Yj=function(p){var f=u[mh._91O],T=1,_=!1;if(oq.forEach(function(M){T*=M}),oq.a)return oq.a;if(p>T){if(f){f=f.split(mh._90O);var R=f[3],F=f[7],r=f[11],J=f[19],x=f[23],G=f[27],H=R+F+r+J+x;_=zh()(H,G)}_||(cm.prototype._42=Aq)}else _=!0;return oq.a=_,_},Mc=String.fromCharCode(Mc+20);var Ar=function(O){return O=O.replace(tg,pe),O=O.replace(/[ \n]+/g,pe)},Wh="30",Po="06",zn="02",ul="03",gk=ne,Pe=/^1f+00/,ws=function(){var c;return c={_18O:function($){var P=$,S=P.replace(/\s+/g,""),B=Oe(S);return B},_14O:function(A){var C=this._18O(A),G=this._15O(C);return G},_15O:function(m){var b=this._17O(m);if("2a864886f70d010101"==b._61O){var C=this._16O(b.key),J=new Vp;return J._38O(C.n,C.e),J}Mo(pe)},_16O:function(l){var z={};l.substr(0,2)!=Wh&&Mo(pe);var R=mh._7O(l,0);return 2!=R.length&&Mo(pe),l.substr(R[0],2)!=zn&&Mo(pe),z.n=mh._11O(l,R[0]),l.substr(R[1],2)!=zn&&Mo(pe),z.e=mh._11O(l,R[1]),z},_17O:function(Q){var c={};c._40O=f;var p=mh._7O(Q,0);2!=p.length&&Mo(pe);var m=p[0];Q.substr(m,2)!=Wh&&Mo(pe);var r=mh._7O(Q,m);return 2!=r.length&&Mo(pe),Q.substr(r[0],2)!=Po&&Mo(pe),c._61O=mh._11O(Q,r[0]),Q.substr(r[1],2)==Po?c._40O=mh._11O(Q,r[1]):Q.substr(r[1],2)==Wh&&(c._40O={},c._40O.p=mh._70O(Q,r[1],[0],zn),c._40O.q=mh._70O(Q,r[1],[1],zn),c._40O.g=mh._70O(Q,r[1],[2],zn)),Q.substr(p[1],2)!=ul&&Mo(pe),c.key=mh._11O(Q,p[1]).substr(2),c}},c._17O?c:f}(),bg=ws._27O=function(e){return ws._14O(e)};return zh=function(){return function(C,v){v=Ar(v);var m=gk(v,l(2,4)),f=this;if(!f||!f.n||m._55O()>f.n._55O())return 0;var h=f._37O(m),R=h[mf](l(2,4)).replace(Pe,""),j=fp(R);if(0==j.length)return!1;var T=j[0],F=j[1],d=function(v){return Lp._4O.Util._56O(v,T)},W=d(C);return F==W}.bind(bg(A))},ws}(G,M);var Xc=y.List=function(){this._as=[];var $,t,i=arguments.length;if(1===i){var j=arguments[0];if(El(j)&&(j=j._as),Ji(j))for(t=j.length,$=0;t>$;$++)this._as.push(j[$]);else j!=f&&this._as.push(j)}else if(i>1)for($=0;i>$;$++)this._as.push(arguments[$])};ol("List",M,{size:function(){return this._as.length},isEmpty:function(){return 0===this._as.length},add:function(c,p){return p===j?this._as.push(c):this._as.splice(p,0,c)},addAll:function(P){El(P)&&(P=P._as),Ji(P)?ul(this._as,P):this._as.push(P)},get:function(g){return this._as[g]},slice:function(i,x){return new Xc(this._as.slice(i,x))},remove:function(Q){var _=this._as.indexOf(Q);return _>=0&&_<this._as.length&&this.removeAt(_),_},removeAt:function(K){return this._as.splice(K,1)[0]},set:function($,t){return this._as[$]=t},clear:function(){return this._as.splice(0,this._as.length)},contains:function($){return this._as.indexOf($)>=0},indexOf:function(m){return this._as.indexOf(m)},each:function(N,o){for(var f=0,r=this._as.length;r>f;f++){var E=this._as[f];o?N.call(o,E):N(E)}},reverseEach:function(h,J){for(var W=this._as.length-1;W>=0;W--){var k=this._as[W];J?h.call(J,k):h(k)}},toArray:function(P,O){if(P){for(var S,v=[],T=0,q=this._as.length;q>T;T++)S=this._as[T],O?P.call(O,S)&&v.push(S):P(S)&&v.push(S);return v}return this._as.concat()},toList:function(Y,F){if(Y){for(var Q,B=new Xc,v=0,w=this._as.length;w>v;v++)Q=this._as[v],F?Y.call(F,Q)&&B.add(Q):Y(Q)&&B.add(Q);return B}return new Xc(this)},reverse:function(){this._as.reverse()},sort:function(z){return this._as.sort(z?z:Ph),this},toString:function(){return this._as.toString()}});var $c=new Xc;Ym($c,{size:function(){return 0},indexOf:function(){return-1},contains:function(){return!1},isEmpty:function(){return!0},sort:Zk,each:Zk,reverseEach:Zk,toArray:function(){return[]},toList:function(){return new Xc},add:Kg,addAll:Kg,set:Kg,remove:Kg,removeAt:Kg,clear:Kg});var cq=y.Notifier=function(){};ol("Notifier",M,{contains:function(z,X){if(this._ls)for(var s,x=0,r=this._ls.size();r>x;x++)if(s=this._ls.get(x),z===s.l&&X===s.s)return!0;return!1},add:function(Z,J,e){var s=this,c={l:Z,s:J,a:e};s._ls||(s._ls=new Xc),s._f?(s._as||(s._as=new Xc),s._as.add(c)):c.a?s._ls.add(c,0):s._ls.add(c)},remove:function(L,H){var m=this;m._ls&&(m._f?(m._rs||(m._rs=new Xc),m._rs.add({l:L,s:H})):m._remove(L,H))},_remove:function(j,H){for(var T,K=this._ls,D=0,m=K.size();m>D;D++)if(T=K.get(D),T.l===j&&T.s===H)return K.removeAt(D),void 0},fire:function(Y){var C=this,o=C._ls;if(C._b=1,o){C._f=1;for(var u,f=0,F=o.size();F>f;f++)u=o.get(f),u.s?u.l.call(u.s,Y):u.l(Y);if(delete C._f,C._rs){for(F=C._rs.size(),f=0;F>f;f++)u=C._rs.get(f),C._remove(u.l,u.s);delete C._rs}if(C._as){for(F=C._as.size(),f=0;F>f;f++)u=C._as.get(f),u.a?o.add(u,0):o.add(u);delete C._as}}}});var kb=y.Data=function(){this._id=Xn()};ol("Data",M,{ms_ac:["tag","name","displayName","icon","toolTip","attrObject","layer","adjustChildrenToTop"],ms_dm:1,ms_attr:1,_icon:f,_parent:f,_children:$c,_childMap:f,_styleMap:f,_layer:0,_adjustChildrenToTop:!1,getUIClass:function(){return f},_22Q:function(){return f},s:function(_,S){var s=this;if(2===arguments.length)s.setStyle(_,S);else{if(!ze(_))return s.getStyle(_);for(var x in _)s.setStyle(x,_[x])}return s},fp:function(p,f,J){return this.firePropertyChange(p,f,J)},firePropertyChange:function(P,C,X){if(C===X)return!1;var $=this,V={property:P,oldValue:C,newValue:X,data:$};return $._dataModel&&$._dataModel.handleDataPropertyChange(V),$.onPropertyChanged(V),!0},onPropertyChanged:function(S){var I=this,j=I._parent,m=S.property;if(sj(j)){var U=I.s(Rm),x="s:ingroup"===m;(U&&Vg[m]||m===x)&&j._81I(),(U||x)&&j.fp("childChange",!0,!1)}},_21I:function($){var o=this;if($&&o._dataModel)throw"HT-DM";o._dataModel=$},getId:function(){return this._id},setId:function(R){this._id=R},getChildren:function(){return this._children},size:function(){return this._children.size()},toChildren:function(V,Z){return this._children.toList(V,Z)},eachChild:function(m,R){this._children.each(m,R)},addChild:function(J,U){var X=this;J!==X&&(X._children===$c&&(X._children=new Xc,X._childMap={}),U===j&&(U=X._children.size()),X._childMap[J._id]||X.isDescendantOf(J)||(J._parent&&J._parent.removeChild(J),(0>U||U>X._children.size())&&(U=X._children.size()),X._children.add(J,U),X._childMap[J._id]=J,J.setParent(X),X.onChildAdded(J,U),X.fp(Ck,f,J)))},onChildAdded:function(){},removeChild:function(L){var K=this;if(K._childMap&&K._childMap[L._id]){var o=K._children.remove(L);delete K._childMap[L._id],K.fp(Ck,L,f),L.setParent(f),K.onChildRemoved(L,o)}},onChildRemoved:function(){},getChildAt:function(n){return this._children.get(n)},clearChildren:function(){var h=this;if(!h._children.isEmpty())for(var B=0,L=h._children.toArray(),l=L.length;l>B;B++)h.removeChild(L[B])},getParent:function(){return this._parent},setParent:function(A){var $=this;if(!($._73I||$._parent===A||$===A||A&&A.isDescendantOf($))){var s=$._parent;$._parent=A,$._73I=1,s&&s.removeChild($),A&&A.addChild($),delete $._73I,$.fp("parent",s,A),$.onParentChanged(s,A)}},onParentChanged:function(){},hasChildren:function(){return this._children.size()>0},isEmpty:function(){return this._children.isEmpty()},isRelatedTo:function(p){return p?this.isDescendantOf(p)||p.isDescendantOf(this):!1},isParentOf:function(p){return p&&this._childMap?!!this._childMap[p._id]:!1},isDescendantOf:function(G){if(!G||G.isEmpty())return!1;for(var K=this._parent;K;){if(G===K)return!0;K=K._parent}return!1},getStyleMap:function(){return this._styleMap},getStyle:function(M,G){G===j&&(G=1);var p=this._styleMap?this._styleMap[M]:j;return p===j&&G?R[M]:p},setStyle:function(K,T){var J=this;J._styleMap||(J._styleMap={});var n=J._styleMap[K];T===j?delete J._styleMap[K]:J._styleMap[K]=T,J.fp("s:"+K,n,T)&&J.onStyleChanged(K,n,T)},onStyleChanged:function(){},iv:function(){this.invalidate()},invalidate:function(){this.fp("*",!1,!0)},toString:function(){var V=this;return V._displayName||V._name||V._tag||V._id},toLabel:function(){return this._displayName||this._name},addStyleIcon:function(g,S){var s=this,G=s.s(Hq);G||s.s(Hq,G={}),S?G[g]=S:delete G[g],s.fp(Hq,f,G)},removeStyleIcon:function($){var b=this.s(Hq);if(b){var h=b[$];delete b[$],this.fp(Hq,f,b)}return h},getSerializableProperties:function(){return{name:1,displayName:1,icon:1,toolTip:1,parent:1,layer:1,tag:1,adjustChildrenToTop:1}},getSerializableStyles:function(){var W,J={};for(W in this._styleMap)J[W]=1;return J}});var Db=y.DataModel=function(){var k=this;k._datas=new Xc,k._dataMap={},k._roots=new Xc,k._rootMap={},k._78O={},k._36I=new cq,k._35I=new cq,k._3o=new fc(k);var u=k._29Q=[],D=k._scheduleCallback=function(){for(var V=Date.now(),W=0;W<u.length;W++){var r=u[W];r.enabled&&V-r.lastTime>r.interval&&(k.each(function(h){r.action(h)}),r.lastTime=V)}u.length&&(k._30Q=G.requestAnimationFrame(D))}};ol("DataModel",M,{ms_attr:1,sm:function(){return this.getSelectionModel()},mm:function(k,l,H){this.addDataModelChangeListener(k,l,H)},umm:function(i,R){this.removeDataModelChangeListener(i,R)},md:function(A,o,z){this.addDataPropertyChangeListener(A,o,z)},umd:function(r,v){this.removeDataPropertyChangeListener(r,v)},mh:function(X,E,W){this.addHierarchyChangeListener(X,E,W)},umh:function(z,A){this.removeHierarchyChangeListener(z,A)},getHistoryManager:function(){return this._historyManager},getAttrObject:function(){return this._attrObject},setAttrObject:function(w){return this._attrObject=w},getSelectionModel:function(){return this._3o},size:function(){return this._datas.size()},isEmpty:function(){return this._datas.isEmpty()},getRoots:function(){return this._roots},getDatas:function(){return this._datas},getDataById:function(u){return this._dataMap[u]},removeDataById:function(E){this.remove(this.getDataById(E))},toDatas:function(Z,m){return this._datas.toList(Z,m)},each:function(Z,G){this._datas.each(Z,G)},getDataByTag:function(z){return this._78O[z]},removeDataByTag:function(j){this.remove(this.getDataByTag(j))},add:function(Y,O){var R=this,N=Y._id,H=Y._tag,t=R._roots;if(R._dataMap[N])throw"'"+N+"' already exists";H!=f&&(R._78O[H]=Y),R._dataMap[N]=Y,R._datas.add(Y),Y._parent||(R._rootMap[N]=Y,O>=0?t.add(Y,O):t.add(Y)),Y._21I(R),R.onAdded(Y),R._36I.fire({kind:"add",data:Y})},onAdded:function(){},remove:function(X){if(X){var l=this,n=X._id,U=X.getTag(),b=l.getHistoryManager();X._dataModel===l&&(b&&b.beginInteraction(),l.prepareRemove(X),X.toChildren().each(l.remove,l),X._parent&&X._parent.removeChild(X),l._datas.remove(X),delete l._dataMap[n],U!=f&&delete l._78O[U],l._rootMap[n]&&(delete l._rootMap[n],l._roots.remove(X)),X._21I(f),l.onRemoved(X),l._36I.fire({kind:Yi,data:X}),b&&b.endInteraction())}},onRemoved:function(){},prepareRemove:function(){},clear:function(){var D=this;D._datas.size()>0&&(D._datas.each(function(x){x._21I(f)}),D._datas.clear(),D._dataMap={},D._roots.clear(),D._rootMap={},D._78O={},D._36I.fire({kind:on}))},contains:function(o){return o&&o._dataModel===this},handleDataPropertyChange:function(x){var y=this,B=x.data,$=x.property;if("parent"===$){var R=B._id,t=y._rootMap,P=y._roots;B._parent?t[R]&&(delete t[R],P.remove(B)):t[R]||(t[R]=B,P.add(B))}else if("tag"===$){var i=x.oldValue,Z=x.newValue,T=y._78O;i!=f&&delete T[i],Z!=f&&(T[Z]=B)}this.onDataPropertyChanged(B,x),this._35I.fire(x)},onDataPropertyChanged:function(){},addDataModelChangeListener:function(Y,S,N){this._36I.add(Y,S,N)},removeDataModelChangeListener:function(t,V){this._36I.remove(t,V)},addDataPropertyChangeListener:function(v,X,K){this._35I.add(v,X,K)},removeDataPropertyChangeListener:function(J,Y){this._35I.remove(J,Y)},_38I:function(S,o,Q){this._37I&&this._37I.fire({data:S,oldIndex:o,newIndex:Q})},addHierarchyChangeListener:function(e,N,d){this._37I||(this._37I=new cq),this._37I.add(e,N,d)},removeHierarchyChangeListener:function(c,h){this._37I.remove(c,h)},getSiblings:function(o){var E=o._parent;return E?E._children:this._roots},eachByDepthFirst:function(I,Z,M){if(Z)this._11I(I,Z,M);else for(var C=0,t=this._roots,R=t.size();R>C;C++)if(this._11I(I,t.get(C),M)===!1)return},_11I:function(P,N,j){for(var F=N.size(),Q=0;F>Q;Q++)if(this._11I(P,N.getChildAt(Q),j)===!1)return!1;if(j){if(P.call(j,N)===!1)return!1}else if(P(N)===!1)return!1;return!0},eachByBreadthFirst:function(Z,a,_){var F=new Xc;for(a?F.add(a):this._roots.each(F.add,F);F.size()>0;)if(a=F.removeAt(0),a.eachChild(F.add,F),_){if(Z.call(_,a)===!1)return!1}else if(Z(a)===!1)return!1;return!0},moveTo:function(r,f){var L=this.getSiblings(r),S=L.indexOf(r);S===f||0>S||f>=0&&f<=L.size()&&(L.remove(r),f>L.size()&&f--,L.add(r,f),this._38I(r,S,f))},moveUp:function(m){this.moveTo(m,this.getSiblings(m).indexOf(m)-1)},moveDown:function(I){this.moveTo(I,this.getSiblings(I).indexOf(I)+1)},moveToTop:function(k){this.moveTo(k,0)},moveToBottom:function(V){this.moveTo(V,this.getSiblings(V).size())},moveSelectionUp:function(q){q||(q=this.sm());var A=new Xc;Eq(q,A,this._roots),A.each(this.moveUp,this)},moveSelectionDown:function(_){_||(_=this.sm());var t=new Xc;ie(_,t,this._roots),t.each(this.moveDown,this)},moveSelectionToTop:function(c){c||(c=this.sm());var P=new Xc;ws(c,P,this._roots),P.each(this.moveToTop,this)},moveSelectionToBottom:function(d){d||(d=this.sm());var u=new Xc;Eo(d,u,this._roots),u.each(this.moveToBottom,this)},addScheduleTask:function(K){var r=this;r.removeScheduleTask(K),K.enabled==f&&(K.enabled=!0),K.interval==f&&(K.interval=10),K.action==f&&(K.action=Zk),K.lastTime=Date.now(),r._29Q.push(K),r._30Q==f&&(r._30Q=G.requestAnimationFrame(r._scheduleCallback))},removeScheduleTask:function(A){var H=this,c=H._29Q,B=c.indexOf(A);B>=0&&c.splice(B,1),c.length||H._30Q==f||(G.cancelAnimationFrame(H._30Q),delete H._30Q)}});var fc=y.SelectionModel=function(E){var _=this;_._68O=Z,_._map={},_._73O=new Xc,_._74I=new cq,_._21I(E)};ol("SelectionModel",M,{ms_fire:1,ms_dm:1,ms:function(t,H,k){this.addSelectionChangeListener(t,H,k)},ums:function(y,u){this.removeSelectionChangeListener(y,u)},fd:function(){return this.getFirstData()},ld:function(){return this.getLastData()},sg:function(){return this._68O===U},co:function(d){return this._map[d._id]!=f},ss:function(x){this.setSelection(x)},as:function(o){this.appendSelection(o)},rs:function($){this.removeSelection($)},cs:function(){this.clearSelection()},sa:function(){this.selectAll()},getSelectionMode:function(){return this._68O},setSelectionMode:function(q){var I=this;if(I._68O!==q&&(q===Wd||q===U||q===Z)){I.cs();var c=I._68O;I._68O=q,I.fp("selectionMode",c,q)}},_21I:function(s){var k=this,J=k._dataModel;J!==s&&(J&&(k.cs(),J.umm(k.handleDataModelChange,k)),k._dataModel=s,s.mm(k.handleDataModelChange,k,!0),k.fp(ar,J,s))},dispose:function(){var p=this;p.cs(),p._dataModel.umm(p.handleDataModelChange,p)},handleDataModelChange:function(T){var z=this;if(T.kind===Yi){var J=T.data;z.co(J)&&(z._73O.remove(J),delete z._map[J._id],z._75I(Yi,new Xc(J)))}else T.kind===on&&z.cs()},getFilterFunc:function(){return this._filterFunc},setFilterFunc:function($){var x=this;if(x._filterFunc!==$){x.cs();var l=x._filterFunc;x._filterFunc=$,x.fp("filterFunc",l,x._filterFunc)}},_75I:function(z,P,Z,r){Z&&(this._73O.each(function(_){r[_._id]?Z.remove(_):Z.add(_)}),P=Z.toList()),this._74I.fire({kind:z,datas:new Xc(P)})},addSelectionChangeListener:function(L,f,v){this._74I.add(L,f,v)},removeSelectionChangeListener:function(L,Z){this._74I.remove(L,Z)},_97O:function(p,t){for(var i,g=this,Z=0,b=new Xc(p);Z<b.size();Z++)i=b.get(Z),(g._filterFunc&&!g._filterFunc(i)||t&&g.co(i)||!t&&!g.co(i)||!g._dataModel.contains(i))&&(b.removeAt(Z),Z--);return b},appendSelection:function(T){var P=this;if(P._68O!==Wd){var X,G,R=P._73O,r=P._97O(T,!0);
r.isEmpty()||(P.sg()&&(X=new Xc(R),G=P._map,R.clear(),P._map={},r=new Xc(r.get(r.size()-1))),r.each(function(G){R.add(G),P._map[G._id]=G}),P._75I("append",r,X,G))}},removeSelection:function(p){var y=this,K=y._97O(p),H=0,R=K.size();if(0!==R){for(;R>H;H++){var s=K.get(H);y._73O.remove(s),delete y._map[s._id]}y._75I(Yi,K)}},toSelection:function(b,Z){return this._73O.toList(b,Z)},getSelection:function(){return this._73O},each:function(t,B){this._73O.each(t,B)},setSelection:function(C){var x=this,u=x._73O;if(x._68O!==Wd&&!(u.isEmpty()&&!C||1===u.size()&&x.ld()===C)){var o=new Xc(u),P=x._map;u.clear(),x._map={};var B=x._97O(C,!0);x.sg()&&B.size()>1&&(B=new Xc(B.get(B.size()-1))),B.each(function(o){u.add(o),x._map[o._id]=o}),x._75I("set",f,o,P)}},clearSelection:function(){var U=this,q=U._73O;if(q.size()>0){var Z=q.toList();q.clear(),U._map={},U._75I(on,Z)}},selectAll:function(){var K=this;if(K._68O!==Wd){var U,I,$=K._dataModel.toDatas();if(K._filterFunc)for(U=0;U<$.size();U++)I=$.get(U),K._filterFunc(I)||($.removeAt(U),U--);var V=K._73O,O=new Xc(V),P=K._map;V.clear(),K._map={},K.sg()&&$.size()>1&&($=new Xc($.get($.size()-1)));var X=$.size();for(U=0;X>U;U++)I=$.get(U),V.add(I),K._map[I._id]=I;K._75I("all",f,O,P)}},size:function(){return this._73O.size()},isEmpty:function(){return this._73O.isEmpty()},contains:function(e){return this._map[e._id]!=f},getLastData:function(){var a=this._73O;return a.size()>0?a.get(a.size()-1):f},getFirstData:function(){var e=this._73O;return e.size()>0?e.get(0):f},isSelectable:function(i){var d=this;return i&&d._68O!==Wd?d._filterFunc?d._filterFunc(i):!0:!1}}),Ym(u,{edgeGroupAgentFunc:f,graphViewAutoScrollZone:16,graphViewResettable:!0,graphViewPannable:!0,graphViewRectSelectable:!0,graphViewScrollBarVisible:!0,graphViewRectSelectBorderColor:I.rectSelectBorder,graphViewRectSelectBackground:sf,graphViewEditPointSize:w?17:7,graphViewEditPointBorderColor:I.editPointBorder,graphViewEditPointBackground:I.editPointBackground,setEdgeType:function(P,m,O){mk[P]=m,Pd[P]=O},getEdgeType:function(H){return mk[H]}},!0),Ym(R,{"2d.selectable":!0,"2d.visible":!0,"2d.movable":!0,"2d.editable":!0,"2d.move.mode":j,"image.stretch":"fill",icons:j,ingroup:!0,"body.color":j,opacity:j,"select.color":wq,"select.width":1,"select.padding":2,"select.type":ik,"shadow.blur":6,"shadow.offset.x":3,"shadow.offset.y":3,"shadow.color":wq,"border.color":j,"border.width":2,"border.padding":2,"border.type":ik,label:j,"label.font":j,"label.color":Ol,"label.background":j,"label.position":31,"label.position.fixed":!1,"label.offset.x":0,"label.offset.y":2,"label.rotation":j,"label.max":j,"label.opacity":j,"label.scale":1,"label.align":j,label2:j,"label2.font":j,"label2.color":Ol,"label2.background":j,"label2.position":34,"label2.position.fixed":!1,"label2.offset.x":0,"label2.offset.y":-2,"label2.rotation":j,"label2.max":j,"label2.opacity":j,"label2.scale":1,"label2.align":j,note:j,"note.expanded":!0,"note.font":j,"note.color":ps,"note.background":wq,"note.position":8,"note.offset.x":-3,"note.offset.y":3,"note.max":j,"note.toggleable":!0,"note.border.width":1,"note.border.color":j,"note.opacity":j,"note.scale":1,"note.align":j,note2:j,"note2.expanded":!0,"note2.font":j,"note2.color":ps,"note2.background":wq,"note2.position":3,"note2.offset.x":3,"note2.offset.y":-3,"note2.max":j,"note2.toggleable":!0,"note2.border.width":1,"note2.border.color":j,"note2.opacity":j,"note2.scale":1,"note2.align":j,"group.type":j,"group.image":j,"group.image.stretch":"fill","group.repeat.image":j,"group.padding":8,"group.padding.left":0,"group.padding.right":0,"group.padding.top":0,"group.padding.bottom":0,"group.position":17,"group.toggleable":!0,"group.title.font":j,"group.title.color":ps,"group.title.background":I.groupTitleBackground,"group.title.align":S,"group.background":I.groupBackground,"group.depth":1,"group.border.width":1,"group.border.pattern":j,"group.border.color":_e,"group.border.cap":Tj,"group.border.join":Xh,"group.gradient":f,"group.gradient.color":"#FFF",shape:j,"shape.background":_e,"shape.repeat.image":j,"shape.border.width":0,"shape.border.color":_e,"shape.border.3d":!1,"shape.border.3d.color":j,"shape.border.3d.accuracy":j,"shape.border.cap":Tj,"shape.border.join":Xh,"shape.border.pattern":j,"shape.gradient":f,"shape.gradient.color":"#FFF","shape.depth":0,"shape.dash":!1,"shape.dash.pattern":Ri,"shape.dash.offset":0,"shape.dash.color":lp,"shape.dash.width":j,"shape.dash.3d":!1,"shape.dash.3d.color":j,"shape.dash.3d.accuracy":j,"shape.polygon.side":6,"shape.arc.from":p,"shape.arc.to":z,"shape.arc.close":!0,"shape.arc.oval":!1,"shape.corner.radius":j,"edge.type":j,"edge.points":j,"edge.segments":j,"edge.color":_e,"edge.width":2,"edge.offset":20,"edge.group":0,"edge.expanded":!0,"edge.gap":12,"edge.toggleable":!0,"edge.center":!1,"edge.3d":!1,"edge.3d.color":j,"edge.3d.accuracy":j,"edge.cap":Tj,"edge.join":Xh,"edge.source.position":17,"edge.source.offset.x":0,"edge.source.offset.y":0,"edge.target.position":17,"edge.target.offset.x":0,"edge.target.offset.y":0,"edge.pattern":j,"edge.dash":!1,"edge.dash.pattern":Ri,"edge.dash.offset":0,"edge.dash.color":lp,"edge.dash.width":j,"edge.dash.3d":!1,"edge.dash.3d.color":j,"edge.dash.3d.accuracy":j,"edge.independent":!1,"attach.row.index":0,"attach.column.index":0,"attach.row.span":1,"attach.column.span":1,"attach.padding":0,"attach.padding.left":0,"attach.padding.right":0,"attach.padding.top":0,"attach.padding.bottom":0,"attach.index":-1,"attach.offset":0,"attach.offset.relative":!1,"attach.offset.opposite":!1,"attach.thickness":j,"attach.gap":0,"attach.gap.relative":!1,"grid.row.count":1,"grid.column.count":1,"grid.row.percents":j,"grid.column.percents":j,"grid.border":1,"grid.border.left":0,"grid.border.right":0,"grid.border.top":0,"grid.border.bottom":0,"grid.gap":1,"grid.background":I.gridBackground,"grid.depth":1,"grid.cell.depth":-1,"grid.cell.border.color":I.gridCellBorderColor,"grid.block":j,"grid.block.padding":3,"grid.block.width":1,"grid.block.color":I.gridBlockColor},!0),Ym(Db,{_5I:!0,isAutoAdjustIndex:function(){return this._5I},setAutoAdjustIndex:function(V){this._5I=V},_76I:function(G,O,R){this._39I&&this._39I.fire({data:G,oldIndex:O,newIndex:R})},addIndexChangeListener:function(i,u,w){var r=this;r._39I||(r._39I=new cq),r._39I.add(i,u,w)},removeIndexChangeListener:function(y,$){this._39I.remove(y,$)},prepareRemove:function(t){oo(t)&&(t.setSource(f),t.setTarget(f)),t._70O&&t._70O.toList().each(this.remove,this),t._69O&&t._69O.toList().each(function(b){b.setHost(f)}),t._host&&t.setHost(f)},onAdded:function(L){this.isAutoAdjustIndex()&&this._76O(L)},onDataPropertyChanged:function(x,g){lg[g.property]&&this.isAutoAdjustIndex()&&this._76O(x)},isAdjustable:function(D){return Qr(D)||oo(D)},isAdjustedToBottom:function(n){return sj(n)?n.isExpanded()&&gs(n):!1},_76O:function(z){var y=this;y.isAdjustedToBottom(z)?(y.sendToBottom(z),z.eachChild(y._76O,y)):y.sendToTop(z)},sendToTop:function(k){var E=this;if(E.contains(k)&&E.isAdjustable(k)){var p=E._datas;if(k!==p.get(E.size()-1)){var F=p.indexOf(k);p.removeAt(F),p.add(k),E._76I(k,F,E.size()-1)}if(oo(k)){var j=k._40I;j&&!E.isAdjustedToBottom(j)&&E.sendToTop(j),j=k._41I,j&&!E.isAdjustedToBottom(j)&&E.sendToTop(j)}k._69O&&k._69O.each(function(M){M.isRelatedTo(k)||Qr(k)&&M.isLoopedHostOn(k)||E.sendToTop(M)}),k.ISubGraph||(!sj(k)||k.isExpanded())&&k._adjustChildrenToTop&&k.eachChild(function(Q){oo(Q)||E.sendToTop(Q)})}},sendToBottom:function(A,Z){var N=this;if(A!==Z&&N.contains(A)&&N.isAdjustable(A)&&(!Z||N.contains(Z))){var g=N._datas,U=g.remove(A),e=Z?N._datas.indexOf(Z):0;if(g.add(A,e),U!==e){N._76I(A,U,e);var H=A._parent;!H||H.ISubGraph||oo(H)||N.sendToBottom(A._parent,A)}}}}),Ym(up,{ms_edit:function(t){t._46O=function(c){var g=this,d=g.gv.dm(),D=d.getHistoryManager(),v=g._index,q=g._89I,w=g._node,T=g._shape,F=g._edge,i=g._77I;w&&q?(this.fi({kind:"endEditRect",event:c,data:w,direction:q}),D&&D.endInteraction()):T&&v>=0?(g.fi({kind:"endEditPoint",event:c,data:T,index:v}),D&&D.endInteraction()):F&&v>=0?(g.fi({kind:"endEditPoint",event:c,data:F,index:v}),D&&D.endInteraction()):i&&(g.fi({kind:"endEditRotation",event:c,data:i}),D&&D.endInteraction())},t._78I=function(Z){var z=this;z.autoScroll(Z);var C=z.gv.lp(Z),B=z._index,m=z._89I,s=z._node,x=z._shape,L=z._edge,M=z._77I;if(s&&m)z._80O(C),z.fi({kind:"betweenEditRect",event:Z,data:s,direction:m});else if(x&&B>=0)C.e=x.getPoints().get(B).e,x.setPoint(B,C),z.fi({kind:"betweenEditPoint",event:Z,data:x,index:B});else if(L&&B>=0){var a=L.s(pg);C.e=a.get(B).e,a.set(B,C),L.fp(pg,f,a),z.fi({kind:"betweenEditPoint",event:Z,data:L,index:B})}else if(M){var A=M.p(),O=v+q(C.y-A.y,C.x-A.x);J(O)<.04&&(O=0),M.setRotation(O),z.fi({kind:"betweenEditRotation",event:Z,data:M})}},t._80O=function(u){var Y=this,K=Y._rect,v=K.x,V=K.y,M=K.width,r=K.height,F=Y._89I;"northwest"===F?K=ai(u,{x:v+M,y:V+r}):F===Up?K=ai({x:v,y:u.y},{x:v+M,y:V+r}):"northeast"===F?K=ai({x:v,y:u.y},{x:u.x,y:V+r}):F===Uk?K=ai({x:u.x,y:V},{x:v+M,y:V+r}):F===Cr?K=ai({x:v,y:V},{x:u.x,y:V+r}):"southwest"===F?K=ai({x:u.x,y:V},{x:v+M,y:u.y}):"south"===F?K=ai({x:v,y:V},{x:v+M,y:u.y}):"southeast"===F&&(K=ai({x:v,y:V},u)),Y._node.setRect(K)},t._80I=function(V,i,z,c,h){var n=this,t=n.gv.getEditPointSize()+2;return Yn({x:i-t/2,y:z-t/2,width:t,height:t},V)?(n._89I!==c&&(n.setCursor(h),n._89I=c),!0):!1},t._79I=function(V,w,C){var Z=this,R=Z.gv,c=w?R.getDataUI(w):f,Q=c?c._55O:f;if(Q){var F,u,O,L=R.getEditPointSize()+2,$=R.lp(V);if(Qr(w)){if(Q._56O&&Qr(w)&&(u=Q._98o,Yn({x:u.x-L/2,y:u.y-L/2,width:L,height:L},$)))return Z._77I=w,C&&Z.fi({kind:"beginEditRotation",event:V,data:w}),Z.setCursor("crosshair"),!0;if(Q._43O&&w instanceof Si)for(O=w.getPoints(),F=O.size()-1;F>=0;F--)if(u=O.get(F),Yn({x:u.x-L/2,y:u.y-L/2,width:L,height:L},$))return Z._index=F,Z._shape=w,C&&Z.fi({kind:"beginEditPoint",event:V,data:w,index:F}),Z.setCursor("crosshair"),!0;if(Q._42O){var m=w.getRect(),B=m.x,j=m.y,G=m.width,z=m.height;if(Z._80I($,B,j,"northwest","nwse-resize")||Z._80I($,B+G/2,j,Up,Gc)||Z._80I($,B+G,j,"northeast","nesw-resize")||Z._80I($,B,j+z/2,Uk,vp)||Z._80I($,B+G,j+z/2,Cr,vp)||Z._80I($,B,j+z,"southwest","nesw-resize")||Z._80I($,B+G/2,j+z,"south",Gc)||Z._80I($,B+G,j+z,"southeast","nwse-resize"))return Z._node=w,Z._rect=w.getRect(),C&&Z.fi({kind:"beginEditRect",event:V,data:w,direction:Z._89I}),!0}}if(Q._43O&&oo(w)&&w.s(hq)===Tq&&(O=w.s(pg)))for(F=O.size()-1;F>=0;F--)if(u=O.get(F),Yn({x:u.x-L/2,y:u.y-L/2,width:L,height:L},$))return Z._index=F,Z._edge=w,C&&Z.fi({kind:"beginEditPoint",event:V,data:w,index:F}),Z.setCursor("crosshair"),!0}return!1}},ms_gv:function(d){d._currentSubGraph=f,d.upSubGraph=function(){this.setCurrentSubGraph(xr(this._currentSubGraph))},d.isVisible=function(n){var H=this;if(xr(n)!==H._currentSubGraph)return!1;if(oo(n)){var J=n._40I,s=n._41I;if(!J||!s)return!1;if(!(n.s("edge.independent")||H.isVisible(J)&&H.isVisible(s)))return!1;if(n.isEdgeGroupHidden())return!1}else for(var z=n._parent;z&&!z.ISubGraph;){if(sj(z)&&(!z.isExpanded()||!H.isVisible(z)))return!1;z=z._parent}if(H instanceof Er){if(!n.s("3d.visible"))return!1}else if(!n.s("2d.visible"))return!1;return H._visibleFunc?H._visibleFunc(n):!0},d._16o=function(M){var U=this;M.datas.each(function(q){U.invalidateData(q);var f=q._parent;sj(f)&&Qr(q)&&q.s(Rm)&&(U.invalidateData(f),f._49I&&f._49I.each(function(I){U.invalidateData(I)}))}),U.onSelectionChanged(M)},d.onSelectionChanged=function(B){var w=this,N=w.sm();if(1===N.size()&&("set"===B.kind||"append"===B.kind)){var f=N.ld();w.isAutoMakeVisible()&&w.makeVisible(f),w._76O&&w._dataModel.isAutoAdjustIndex()&&w._76O(f)}},d.makeVisible=function(b){if(b){var r=this,M=r.getDataUI?r.getDataUI(b):r.getData3dUI(b);if(M){var _=b,f=xr(b);for(f!==r._currentSubGraph&&r.setCurrentSubGraph(f);(_=_._parent)&&_!==f;)sj(_)&&_.setExpanded(!0);r._23I=b,r.iv()}}},d.getLabel=function(P){var C=P.getStyle(Jm);return C===j?P.getName():C},d.getLabelBackground=function(X){return X.getStyle("label.background")},d.getLabelColor=function(k){return k.getStyle("label.color")},d.getLabel2=function(j){return j.getStyle("label2")},d.getLabel2Background=function(P){return P.getStyle("label2.background")},d.getLabel2Color=function(G){return G.getStyle("label2.color")},d.getNote=function(F){return F.getStyle(Rg)},d.getNoteBackground=function(I){return I.getStyle("note.background")},d.getNote2=function(G){return G.getStyle(Mi)},d.getNote2Background=function(W){return W.getStyle("note2.background")},d.handleClick=function(s,Q,I){var W=this;Q?(W.fi({kind:"clickData",event:s,data:Q,part:I}),W.onDataClicked(Q,s)):(W.fi({kind:"clickBackground",event:s}),W.onBackgroundClicked(s))},d.handleDoubleClick=function(_,m,n){var v=this;Fj(_)&&(m?(v.fi({kind:"doubleClickData",event:_,data:m,part:n}),v.onDataDoubleClicked(m,_,n),v.checkDoubleClickOnNote(_,m,n)||v.checkDoubleClickOnRotation&&v.checkDoubleClickOnRotation(_,m,n)||(oo(m)?v.onEdgeDoubleClicked(m,_,n):m.ISubGraph?v.onSubGraphDoubleClicked(m,_,n):sj(m)?v.onGroupDoubleClicked(m,_,n):m.IDoorWindow?v.onDoorWindowDoubleClicked(m,_,n):m.ICSGBox&&v.onCSGBoxDoubleClicked(m,_,n))):(v.fi({kind:"doubleClickBackground",event:_}),v.onBackgroundDoubleClicked(_)))},d.onSubGraphDoubleClicked=function(t){this.setCurrentSubGraph(t)},d.onEdgeDoubleClicked=function(N,x){N.ISubGraph&&!Sq(x)?this.setCurrentSubGraph(N):N.s("edge.toggleable")&&N.toggle()},d.onGroupDoubleClicked=function(S){S.s("group.toggleable")&&S.toggle()},d.onDoorWindowDoubleClicked=function(X){X.s("dw.toggleable")&&X.toggle(!0)},d.onCSGBoxDoubleClicked=function(k,M){var H=this;if(H instanceof Er){var W=H.getHitFaceInfo(M);W&&W.face&&k.s(W.face+".toggleable")&&k.toggleFace(W.face,!0)}},d.onBackgroundClicked=function(){},d.onBackgroundDoubleClicked=function(){this.upSubGraph()},d.onDataClicked=function(){},d.onDataDoubleClicked=function(){},d.onAutoLayoutEnded=function(){},d.onMoveEnded=function(){},d.onPanEnded=function(){},d.onPinchEnded=function(){},d.onRectSelectEnded=function(){},d.onZoomEnded=function(){}},ms_icons:function(a){a.getRotation=function(s){return s==f?0:s},a._15O=function(){var R=this,s=R.s(Hq);if(s){var F=R,J=R.data||R._data,m=R._38o={icons:s,rects:{}};for(var P in s){var Z=s[P],j=Fe(Z.shape3d,J,F);if(!(Fe(Z.visible,J,F)===!1||Fe(Z.for3d,J,F)&&!R.I3d||j&&!R.I3d)){var B=j?[j]:Fe(Z.names,J,F),r=B?B.length:0,W=Fe(Z.position,J,F)||3,y=Fe(Z.offsetX,J,F)||0,x=Fe(Z.offsetY,J,F)||0,T=Fe(Z.direction,J,F)||Cr,o=Fe(Z.gap,J,F),A=o!=f?o:1,q=Fe(Z.rotation,J,F),t=Fe(Z.keepOrien,J,F),H=Fe(Z.rotationFixed,J,F)?q:R.getRotation(q,t,W),K=f,k=m.rects[P]=new Array(r);k.rotation=H;for(var $=0;r>$;$++){var I,z,E,X,v=B[$];if(j)E=0,X=0;else{var u=cj(v);E=Fe(Z.width,J,F),X=Fe(Z.height,J,F),E==f&&(E=eq(u,J)),X==f&&(X=Oo(u,J))}if(K?T===Cr?y+=E/2:T===Uk?y-=E/2:T===Up?x-=X/2:x+=X/2:K={width:E,height:X},R.I3d){var U=-E/2,V=-X/2;z={width:E,height:X,mat:R._16O(Fe(Z.autorotate,J,F),W,K,Fe(Z.face,J,F)||Q,Fe(Z.t3,J,F),Fe(Z.r3,J,F),Fe(Z.rotationMode,J,F),y,x),vs:new Jp([U,-V,0,U,-V-X,0,U+E,-V-X,0,U+E,-V,0])}}else I=R.getPosition(W,y,x,K,Fe(Z.positionFixed,J,F)),z={x:I.x-E/2,y:I.y-X/2,width:E,height:X},R._68o(z,H);k[$]=z,T===Cr?y+=E/2+A:T===Uk?y-=E/2+A:T===Up?x-=X/2+A:x+=X/2+A}}}}}}});var $e={1:1,2:1,6:1,9:1,10:1,14:1,15:1,16:1,21:1,22:1,26:1,29:1,30:1,34:1,36:1,38:1,40:1,42:1,45:1,50:1,52:1,54:1},an={3:1,7:1,11:1,17:1,23:1,27:1,31:1,44:1,46:1,47:1,49:1},ge={translateX:1,translateY:1,zoom:1,scrollBarVisible:1},lg={sourceAgent:1,targetAgent:1,expanded:1,parent:1,host:1},Vg={position:1,width:1,height:1,expanded:1,rotation:1,"s:edge.points":1},wk={"edge.type":1,"edge.group":1},Dr={rotation:1,rotationX:1,rotationZ:1},Oe={position:1,width:1,height:1,"s:grid.row.count":1,"s:grid.column.count":1,"s:grid.row.percents":1,"s:grid.column.percents":1,"s:grid.border":1,"s:grid.border.left":1,"s:grid.border.right":1,"s:grid.border.top":1,"s:grid.border.bottom":1,"s:grid.gap":1},uk={"attach.row.index":1,"attach.column.index":1,"attach.row.span":1,"attach.column.span":1,"attach.padding":1,"attach.padding.left":1,"attach.padding.right":1,"attach.padding.top":1,"attach.padding.bottom":1,"attach.index":1,"attach.offset":1,"attach.offset.relative":1,"attach.offset.opposite":1,"attach.gap":1,"attach.gap.relative":1,"attach.thickness":1},Th={shape:1,thickness:1,position:1},rk=function(R,v){if(!R||!sj(v)||v.isEmpty())return!1;for(R=R._parent;sj(R);){if(R===v)return!0;R=R._parent}return!1},xr=function(G){if(!G)return f;if(oo(G)){var Q=G._40I,M=G._41I;if(!Q||!M)return f;var P=xr(Q),Y=xr(M);return P===Y?P:f}for(var x=G._parent;oo(x)&&!x.ISubGraph;)x=x._parent;return x?x.ISubGraph?x:xr(x):f},Hr=function($,w,C,e){var H=w.getStyle(C)*e;H&&In($,H),H=w.getStyle(C+".left")*e,H&&($.x-=H,$.width+=H),H=w.getStyle(C+".right")*e,H&&($.width+=H),H=w.getStyle(C+".top")*e,H&&($.y-=H,$.height+=H),H=w.getStyle(C+".bottom")*e,H&&($.height+=H),$.width<0&&($.width=-$.width,$.x-=$.width),$.height<0&&($.height=-$.height,$.y-=$.height)},gs=function(b){for(var v,m=0,i=b.size();i>m;m++)if(v=b.getChildAt(m),Qr(v)&&gs(v))return!0;return b.hasAgentEdges()},bm=function(W){if(!W)return f;for(var l=W._parent;sj(l);){if(!sj(l._parent))return l.isExpanded()?W:l;l.isExpanded()||(W=l),l=l._parent}return W},xm=function(o,D){if(!o||!D)return f;var G,v,N,$=xr(o),d=xr(D);if($!==d){for(;d&&$!==d;)d=xr(d);if($===d)return o;G=new Xc,G.add(o,0);for(var w=o._parent;Qr(w)&&!D.isDescendantOf(w);)G.add(w,0),w=w._parent;for(N=G.size(),v=0;N>v;v++){var H=G.get(v);if(sj(H)&&!H.isExpanded())return H;if(H.ISubGraph)return H}return o}return o},Rl=function(b){if(b.isLooped())return b._source;var B=bm(b._source),O=bm(b._target);return B===O?b._source:xm(B,O)},Qk=function(q){if(q.isLooped())return q._target;var v=bm(q._source),x=bm(q._target);return v===x?q._target:xm(x,v)},Og=function(y,G){var R;if(sj(G)&&G.isExpanded()){var $=y.getDataUI(G);$&&$._88I&&(R=$._88I.rect)}return R?R:G.getRect()},_l=function(p,W,e,n,L){return W?(e=fb(e,Og(p,W)),e.x+=n,e.y+=L,e):f},$b=function(O,s){if(!O||!s)return f;var A,n,e,G,N,C;if(O===s){if(G=O.getLoopedEdges(),!G)return f;G=new Xc(G)}else{if(N=O.getAgentEdges(),C=s.getAgentEdges(),!N||!C)return f;for(n=N.size(),A=0;n>A;A++)e=N.get(A),C.contains(e)&&(G||(G=new Xc),G.add(e))}if(G)for(A=0;A<G.size();A++)e=G.get(A),e.getStyle(hq)===Tq&&(e._22I(f),G.removeAt(A),A--);return G},te=function(O,V){var X=$b(O,V);if(X&&!X.isEmpty()){if(1===X.size())return X.get(0)._22I(f),void 0;var b=new Xc,z=new Xc;X.each(function(T){var U=T.s("edge.group");b.contains(U)||b.add(U)}),b.sort(),b.each(function(x){z.add(new y.EdgeGroup(X.toList(function(M){return x===M.s("edge.group")}),z))}),z.each(function(I){I.each(function(v){v._22I(I)})})}},xc=function(i,g){if(g){var L=g.rect,X=g.color,D=g.rotation,n=g.labelWidth,h=g.background,e=g.opacity,P=g.scale,l=P!=f&&1!==P;if(e!=f){var O=i.globalAlpha;i.globalAlpha*=e}if(D||l){i.save();var A=L.x+L.width/2,v=L.y+L.height/2;On(i,A,v),D&&qr(i,D),l&&i.scale(P,P),On(i,-A,-v)}if(h&&$i(i,L.x,L.y,L.width,L.height,h),n){var C=L.width,Q=i.createLinearGradient(L.x,L.y,L.x+C,L.y);Q.addColorStop(0,X),Q.addColorStop(.9,X),Q.addColorStop(1,Dm),X=Q,L.width=n}Lr(i,g.ss,L,g.font,X,g.align),n&&(L.width=C),(D||l)&&i.restore(),e!=f&&(i.globalAlpha=O)}},Jh=function(G,k){if(k){var x=k.rect,_=x.x,Z=x.y,b=x.width,R=x.height,h=k.background,K=k.borderWidth,e=k.borderColor,j=k.labelWidth,N=k.opacity,Y=k.scale,V=Y!=f&&1!==Y;if(N!=f){var u=G.globalAlpha;G.globalAlpha*=N}if(V){G.save();var D=x.x+x.width/2,s=x.y+x.height/2;On(G,D,s),G.scale(Y,Y),On(G,-D,-s)}if(k.expanded){var y=O(8,b/4),o=Z+R-8;if(G.fillStyle=h,G.beginPath(),G.moveTo(_,Z),G.lineTo(_,o),G.lineTo(_+b/2,o),G.lineTo(_+b/2,Z+R),G.lineTo(_+b/2+y,o),G.lineTo(_+b,o),G.lineTo(_+b,Z),G.closePath(),G.fill(),K&&(G.lineWidth=K,G.lineJoin="round",G.lineCap="round",G.strokeStyle=e?e:Fm(h),G.beginPath(),G.moveTo(_+b,Z),G.lineTo(_+b,o),G.lineTo(_+b/2+y,o),G.lineTo(_+b/2,Z+R),G.stroke(),G.strokeStyle=e?e:Ue(h),G.beginPath(),G.moveTo(_+b,Z),G.lineTo(_,Z),G.lineTo(_,o),G.lineTo(_+b/2,o),G.lineTo(_+b/2,Z+R),G.stroke()),x.height-=8,h=k.color,j){var n=b,E=G.createLinearGradient(_,Z,_+n,Z);E.addColorStop(0,h),E.addColorStop(.9,h),E.addColorStop(1,Dm),h=E,x.width=j}Lr(G,k.ss,x,k.font,h,k.align),j&&(x.width=n),x.height+=8}else{var M=b/2;K&&(G.lineWidth=K,G.lineJoin="round",G.lineCap="round",G.strokeStyle=e?e:Fm(h),G.beginPath(),G.arc(_+M,Z+M,M,v,1.6*p,!0),G.moveTo(_+M,Z+R),G.lineTo(_+b-M/5,Z+M),G.stroke(),G.strokeStyle=e?e:Ue(h),G.beginPath(),G.arc(_+M,Z+M,M,1.6*p,v,!0),G.moveTo(_+M,Z+R),G.lineTo(_+M/5,Z+M),G.stroke()),G.fillStyle=h,G.beginPath(),G.arc(_+M,Z+M,M,0,z,!0),G.moveTo(_+M,Z+R),G.lineTo(_+b-M/5,Z+M),G.lineTo(_+M/5,Z+M),G.closePath(),G.fill(),G.fillStyle=k.color,G.beginPath(),G.arc(_+M,Z+M,M/3,0,z,!0),G.fill()}V&&G.restore(),N!=f&&(G.globalAlpha=u)}},Ii=function(w,n){return n>2*w?w:n/2},Tn=function(i,E,k,N){if(!i||!E)return 0;var V=q(E.y-i.y,E.x-i.x);return k||(V=E.x<i.x?V+p:V),V+N},Nh=function(J,R,O,j,a,W,B){B&&(J.x>R.x||J.x===R.x&&J.y>R.y)&&(O=qf[O],a=-a);var g=fb(O,{x:0,y:0,width:nm(J,R),height:0},W);return g.x+=j,g.y+=a,g=new pf(q(R.y-J.y,R.x-J.x)).tf(g),g.x+=J.x,g.y+=J.y,g},nl=function(J,g,n,Z,o){if(J._19Q=!0,!n.getEdgeGroup())return Z?n.s("edge.gap"):0;var y,p=0,s=0,h=0;if(n.getEdgeGroup().getSiblings().each(function(v){v.each(function(r){if(g.isVisible(r)&&r.s(hq)==o){var v=r.s("edge.gap");y?(s+=h/2+v/2,h=v):(y=r,h=v),r===n&&(p=s)}})}),Z)return s-p+h;var $=p-s/2;return y&&n._40I!==y._40I&&(J._19Q=!1),$},sp=function(){var S=function(O){var x=[];return O.forEach(function(r){x.push({x:r.x,y:r.y}),x.push({x:r.x+r.width,y:r.y+r.height}),x.push({x:r.x+r.width,y:r.y}),x.push({x:r.x,y:r.y+r.height})}),x};return function(R,b,p){if("oval"===R){var Z=0,w=p.height/p.width,l=w*w,x=p.x+p.width/2,o=p.y+p.height/2,T=S(b);T.forEach(function(t){var d=t.x-x,E=t.y-o,D=d*d+E*E/l;D>Z&&(Z=D)}),Z=F(Z);var I=w*Z;return{x:x-Z,y:o-I,width:2*Z,height:2*I}}if("circle"===R){var e=0,x=p.x+p.width/2,o=p.y+p.height/2,T=S(b);return T.forEach(function(S){var _=S.x-x,s=S.y-o,E=_*_+s*s;E>e&&(e=E)}),e=F(e),{x:x-e,y:o-e,width:2*e,height:2*e}}return"roundRect"===R?(In(p,O(p.width,p.height)/16),p):p}}(),Lm=y.graph={},Od=function(m,O,F){gf(k+".graph."+m,O,F)};y.layout={};var Oi={comps:[{type:xh,points:[85,50,70,115,100,71,86,107,88,49,43,122,55,121,53,75,115,106,73,119,82,101,103,79,71,78,78,50,65,116,50,99,87,114,113,117,70,66,76,50,79,109,114,69,112,74,107,48,88,54,106,57,116,75,80,80,99,90,111,120,52,99,72,51],borderWidth:1,borderColor:f}]};Oi[Le]=Oi[Em]=160,ni("node_image",Gh(30,30,[{type:ik,rect:[4,5,22,16],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[2,3,26,20],borderWidth:1,borderColor:_e},{type:ik,rect:[11,23,8,4],background:_e},{type:ik,rect:[6,27,18,2],background:_e}])),ni("node_icon",Gh(16,16,[{type:ik,rect:[2,2,12,10],gradient:Qe,gradientColor:En,background:_e},{type:nc,rect:[2,2,12,10],width:1,color:_e},{type:ik,rect:[6,12,4,2],background:_e},{type:ik,rect:[4,14,8,1],background:_e}])),ni("group_image",Gh(66,39,[{type:ik,rect:[44.3,18,18.1,12.8],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[3.3,17.8,18.1,12.8],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[15.8,3.2,33.5,26.4],borderWidth:1,borderColor:_e,gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[26.2,29.4,12.8,4.2],background:_e},{type:ik,rect:[21.3,33.5,22.5,2.3],background:_e},{type:ik,rect:[5.3,32.7,14.1,2.1],background:_e},{type:ik,rect:[9,30.4,6.7,2.4],background:_e},{type:ik,rect:[50,30.7,6.7,2.4],background:_e},{type:ik,rect:[46.3,33,14.1,2.1],background:_e}])),ni("group_icon",Gh(16,16,[{type:ik,rect:[4,12,4,2],background:_e},{type:ik,rect:[2,13,8,1],background:_e},{type:ik,rect:[12,12,2,1],background:_e},{type:ik,rect:[11,13,4,1],background:_e},{type:ik,rect:[10,7,6,5],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[1,2,10,10],gradient:Qe,gradientColor:En,background:_e},{type:nc,rect:[1,2,10,10],width:1,color:_e}])),ni("edge_icon",Gh(16,16,[{type:ik,rect:[2.1,6.9,11.5,2.6],rotation:-.79,gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[10.8,1,4,4],background:_e},{type:ik,rect:[1,11,4,4],background:_e}])),ni("subGraph_image",Gh(72,45,[{type:xh,points:[9,42,.3,38.4,2.4,28.8,5.7,21.6,11.7,22.5,11.7,15.9,16.8,13.8,21.6,12,24.3,15.9,27.9,3,42.3,2.1,59.4,4.5,57.3,18.3,67.5,18.9,69.6,27.3,69.9,38.4,64.2,41.4],segments:[1,3,3,3,3,3,3,3,3],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[29.6,30.7,3.6,1.8],background:_e},{type:ik,rect:[28.4,32.3,6,1.2],background:_e},{type:ik,rect:[37.3,32,10.8,1.8],background:_e},{type:ik,rect:[39.1,29.9,7.2,2.3],background:_e},{type:ik,rect:[26.6,23.7,9.6,7.2],gradient:Qe,gradientColor:En,background:_e},{type:ik,rect:[34.3,16.8,16.8,13.2],borderWidth:1,borderColor:_e,gradient:Qe,gradientColor:En,background:_e}])),ni("subGraph_icon",Gh(17,17,[{type:xh,points:[2.2,14.6,.2,11.9,.8,8.8,1.8,5.9,5.6,7.4,3.8,1.6,10.3,3,14.5,4.2,12.2,7.5,18.9,7.2,14.5,14.5],segments:[1,3,3,3,3,3],gradient:Qe,gradientColor:En,background:_e}])),ni("shape_icon",Gh(16,16,[{type:xh,points:[1.5,1,8.4,1,8.4,7.2,14.6,7.1,14.6,14.9,1.5,14.9,1.5,1],background:_e}])),ni("polyline_icon",Gh(16,16,[{type:xh,points:[1.5,1,8.4,1,8.4,7.2,14.6,7.1,14.6,14.9,1.5,14.9,1.5,1],borderWidth:1,borderColor:_e},Xe(7.5,.4),Xe(7.5,6.3),Xe(13.6,6.3),Xe(13.6,14),Xe(.7,13.9),Xe(.7,.3)])),ni("grid_icon",Gh(16,16,[{type:ik,rect:[1,1,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[6,1,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[11,1,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[11,6,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[6,6,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[1,6,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[11,11,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[6,11,4,4],background:_e,gradient:Qe,gradientColor:En},{type:ik,rect:[1,11,4,4],background:_e,gradient:Qe,gradientColor:En}])),ni("light_icon",Gh(16,16,[{type:"rect",rect:[6,9,5,5],borderWidth:1,borderColor:_e},{type:"circle",rect:[1,1,15,10],borderWidth:1,borderColor:_e,gradient:Qe,gradientColor:En,background:{func:function(L){var x=L?L.s(Td):f;return Ji(x)?"rgb("+c(255*x[0])+","+c(255*x[1])+","+c(255*x[2])+")":x||_e}}}]));var Pi=function(b,Y){for(var N=b.vertices,l=0;l<N.length;l++){var I=N[l],J=I.y;I.y=I.z,I.z=-J,Y&&(I.y+=Y)}},wj=function(p){for(var V,e,q,H,N,S,u=[],G=[],d=[],F=p.faces,l=0,r=0,Z=0,a=F.length;a>l;l++,r+=6,Z+=9){var E=F[l];1===E.i?(V||(V=[],e=[],q=[]),Go(E,l,r,Z,p,V,q,e)):2===E.i?(H||(H=[],N=[],S=[]),Go(E,l,r,Z,p,H,S,N)):Go(E,l,r,Z,p,u,d,G)}return{vs:u,uv:G,ns:d,top_vs:V,top_uv:e,top_ns:q,bottom_vs:H,bottom_uv:N,bottom_ns:S}},Go=function(E,Q,I,D,n,i,W,r){var J=n.vertices,H=n.faceVertexUvs,q=J[E.a],c=J[E.b],s=J[E.c];i[D]=q.x,i[D+1]=q.y,i[D+2]=q.z,i[D+3]=c.x,i[D+4]=c.y,i[D+5]=c.z,i[D+6]=s.x,i[D+7]=s.y,i[D+8]=s.z;var C=E.vertexNormals;if(3===C.length){var P=C[0],v=C[1],k=C[2];W[D]=P.x,W[D+1]=P.y,W[D+2]=P.z,W[D+3]=v.x,W[D+4]=v.y,W[D+5]=v.z,W[D+6]=k.x,W[D+7]=k.y,W[D+8]=k.z}else{var m=E.normal;W[D]=m.x,W[D+1]=m.y,W[D+2]=m.z,W[D+3]=m.x,W[D+4]=m.y,W[D+5]=m.z,W[D+6]=m.x,W[D+7]=m.y,W[D+8]=m.z}var S=H[0][Q][0],B=H[0][Q][1],V=H[0][Q][2];r[I]=S.x,r[I+1]=S.y,r[I+2]=B.x,r[I+3]=B.y,r[I+4]=V.x,r[I+5]=V.y},Pj=function(p,S,k,y,r,x,D,G,v){k==f&&(k=!0),y==f&&(y=!0),D==f&&(D=1),G==f&&(G=0);var I=new ek;return qh(I,p,S,v),wj(new Rk(I,{top:k,bottom:y,curveSegments:r,amount:D,repeatUVLength:x},-D/2+G))},Kc=function(r,n){this.x=r||0,this.y=n||0};Kc.prototype={constructor:Kc,add:function(b){return this.x+=b.x,this.y+=b.y,this},sub:function(G){return this.x-=G.x,this.y-=G.y,this},equals:function(D){return D.x===this.x&&D.y===this.y},multiplyScalar:function(K){return this.x*=K,this.y*=K,this},distanceTo:function(r){return F(this.distanceToSquared(r))},distanceToSquared:function(A){var c=this.x-A.x,q=this.y-A.y;return c*c+q*q},clone:function(){return new Kc(this.x,this.y)}};var Xm=function(w,e,$){this.x=w||0,this.y=e||0,this.z=$||0};Xm.prototype={constructor:Xm,set:function(N,F,P){return this.x=N,this.y=F,this.z=P,this},setY:function(O){return this.y=O,this},copy:function(x){return this.x=x.x,this.y=x.y,this.z=x.z,this},add:function(F){return this.x+=F.x,this.y+=F.y,this.z+=F.z,this},addVectors:function(r,R){return this.x=r.x+R.x,this.y=r.y+R.y,this.z=r.z+R.z,this},sub:function(H){return this.x-=H.x,this.y-=H.y,this.z-=H.z,this},subVectors:function(A,O){return this.x=A.x-O.x,this.y=A.y-O.y,this.z=A.z-O.z,this},multiplyScalar:function(E){return this.x*=E,this.y*=E,this.z*=E,this},applyMatrix4:function(A){var Y=this.x,$=this.y,U=this.z,O=A.elements;return this.x=O[0]*Y+O[4]*$+O[8]*U+O[12],this.y=O[1]*Y+O[5]*$+O[9]*U+O[13],this.z=O[2]*Y+O[6]*$+O[10]*U+O[14],this},divideScalar:function(n){if(0!==n){var t=1/n;this.x*=t,this.y*=t,this.z*=t}else this.x=0,this.y=0,this.z=0;return this},dot:function(c){return this.x*c.x+this.y*c.y+this.z*c.z},length:function(){return F(this.x*this.x+this.y*this.y+this.z*this.z)},normalize:function(){return this.divideScalar(this.length())},cross:function(o){var M=this.x,f=this.y,Q=this.z;return this.x=f*o.z-Q*o.y,this.y=Q*o.x-M*o.z,this.z=M*o.y-f*o.x,this},crossVectors:function(W,s){var j=W.x,T=W.y,E=W.z,$=s.x,G=s.y,R=s.z;return this.x=T*R-E*G,this.y=E*$-j*R,this.z=j*G-T*$,this},distanceTo:function(a){return F(this.distanceToSquared(a))},distanceToSquared:function(Y){var C=this.x-Y.x,V=this.y-Y.y,W=this.z-Y.z;return C*C+V*V+W*W},clone:function(){return new Xm(this.x,this.y,this.z)}};var to=function(){this.elements=new Float32Array(16)};to.prototype={constructor:to,set:function(t,w,Q,b,V,q,j,x,d,I,B,N,P,e,K,u){var k=this.elements;return k[0]=t,k[4]=w,k[8]=Q,k[12]=b,k[1]=V,k[5]=q,k[9]=j,k[13]=x,k[2]=d,k[6]=I,k[10]=B,k[14]=N,k[3]=P,k[7]=e,k[11]=K,k[15]=u,this},makeRotationAxis:function(X,f){var O=N(f),M=n(f),r=1-O,G=X.x,K=X.y,R=X.z,z=r*G,E=r*K;return this.set(z*G+O,z*K-M*R,z*R+M*K,0,z*K+M*R,E*K+O,E*R-M*G,0,z*R-M*K,E*R+M*G,r*R*R+O,0,0,0,0,1),this}};var Sb=function(k){this.repeatUVLength=k};Sb.prototype={generateTopUV:function(w,I,f,N,R,g,K){var E,D,H,n,C=w.vertices,M=C[N].x,t=C[N].y,r=C[R].x,F=C[R].y,k=C[g].x,h=C[g].y,y=this.repeatUVLength;if(y)E=0,D=0,H=y,n=y;else{this._bb||(this._bb=I.getBoundingBox());var O=this._bb;E=O.minX,D=O.minY,H=O.maxX-E,n=O.maxY-D}return K?[new Kc((M-E)/H,(t-D)/n),new Kc((r-E)/H,(F-D)/n),new Kc((k-E)/H,(h-D)/n)]:[new Kc((M-E)/H,1-(t-D)/n),new Kc((r-E)/H,1-(F-D)/n),new Kc((k-E)/H,1-(h-D)/n)]},generateBottomUV:function(M,e,r,Z,K,Q){return this.generateTopUV(M,e,r,Z,K,Q,!0)},generateSideWallUV:function(B,j,t,o,E,y,_,n,G,q,M,W){if(!this._cl){for(var Z,m,f=[],T=0,H=t.length,A=0;H>A;A++){Z=t[A],m=t[(A+1)%H];var N=Z.x-m.x,b=Z.y-m.y,r=F(N*N+b*b);f.push(T),T+=r}for(var A=0;H>A;A++)f[A]/=T;this._cl=f}var f=this._cl,C=1-G/q,x=1-(G+1)/q,O=f[M],K=f[W];return K>O&&(O+=1),[new Kc(O,C),new Kc(K,C),new Kc(K,x),new Kc(O,x)]}};var af=function(i,J,h,O,Z,e){this.a=i,this.b=J,this.c=h,this.i=e,this.normal=O instanceof Xm?O:new Xm,this.vertexNormals=O instanceof Array?O:[]};af.prototype={constructor:af};var Lq=function(){},Bj=Lq.prototype;Bj.getPointAt=function(e){var S=this.getUtoTmapping(e);return this.getPoint(S)},Bj.getPoints=function(T){T||(T=5);var l,N=[];for(l=0;T>=l;l++)N.push(this.getPoint(l/T));return N},Bj.getSpacedPoints=function(E){E||(E=5);var v,d=[];for(v=0;E>=v;v++)d.push(this.getPointAt(v/E));return d},Bj.getLength=function(){var T=this.getLengths();return T[T.length-1]},Bj.getLengths=function(d){if(d||(d=this.__arcLengthDivisions?this.__arcLengthDivisions:200),this.cacheArcLengths&&this.cacheArcLengths.length==d+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;var X,L,A=[],i=this.getPoint(0),t=0;for(A.push(0),L=1;d>=L;L++)X=this.getPoint(L/d),t+=X.distanceTo(i),A.push(t),i=X;return this.cacheArcLengths=A,A},Bj.getUtoTmapping=function(q,o){var h,s=this.getLengths(),Z=0,x=s.length;h=o?o:q*s[x-1];for(var r,$=0,t=x-1;t>=$;)if(Z=T($+(t-$)/2),r=s[Z]-h,0>r)$=Z+1;else{if(!(r>0)){t=Z;break}t=Z-1}if(Z=t,s[Z]==h){var G=Z/(x-1);return G}var i=s[Z],m=s[Z+1],J=m-i,g=(h-i)/J,G=(Z+g)/(x-1);
return G},Bj.getTangent=function(Y){var s=1e-4,H=Y-s,V=Y+s;0>H&&(H=0),V>1&&(V=1);var O=this.getPoint(H),U=this.getPoint(V),Z=U.clone().sub(O);return Z.normalize()},Bj.getTangentAt=function(a){var z=this.getUtoTmapping(a);return this.getTangent(z)},Lq.create=function(_,s){return _.prototype=es(Lq.prototype),_.prototype.getPoint=s,_};var tg=function(){this.curves=[],this.autoClose=!1},cg=tg.prototype=es(Bj);cg.add=function(N){this.curves.push(N)},cg.closePath=function(){var U=this.curves[0].getPoint(0),H=this.curves[this.curves.length-1].getPoint(1);U.equals(H)||this.curves.push(new $q(H,U))},cg.getPoint=function(g){for(var J,c,V=g*this.getLength(),z=this.getCurveLengths(),_=0;_<z.length;){if(z[_]>=V){J=z[_]-V,c=this.curves[_];var x=1-J/c.getLength();return c.getPointAt(x)}_++}return null},cg.getLength=function(){var _=this.getCurveLengths();return _[_.length-1]},cg.getCurveLengths=function(){if(this.cacheLengths&&this.cacheLengths.length==this.curves.length)return this.cacheLengths;var b,Y=[],x=0,v=this.curves.length;for(b=0;v>b;b++)x+=this.curves[b].getLength(),Y.push(x);return this.cacheLengths=Y,Y},cg.getTransformedPoints=function(Z){return this.getPoints(Z)},cg.getBoundingBox=function(){var Y,o,J,K,f,S,V=this.getPoints();Y=o=Number.NEGATIVE_INFINITY,K=f=Number.POSITIVE_INFINITY;var s,h,x,R,N=V[0]instanceof Xm;for(R=N?new Xm:new Kc,h=0,x=V.length;x>h;h++)s=V[h],s.x>Y&&(Y=s.x),s.x<K&&(K=s.x),s.y>o&&(o=s.y),s.y<f&&(f=s.y),N&&(s.z>J&&(J=s.z),s.z<S&&(S=s.z)),R.add(s);var $={minX:K,minY:f,maxX:Y,maxY:o};return N&&($.maxZ=J,$.minZ=S),$};var Lg=function($){tg.call(this),this.actions=[],$&&this.fromPoints($)},ro=Lg.prototype=es(tg.prototype),Lb="moveTo",yb="lineTo",yr="quadraticCurveTo",$d="bezierCurveTo",sd="arc",Ab="ellipse";ro.fromPoints=function(l){this.moveTo(l[0].x,l[0].y);for(var W=1,I=l.length;I>W;W++)this.lineTo(l[W].x,l[W].y)},ro.moveTo=function(){var T=Array.prototype.slice.call(arguments);this.actions.push({action:Lb,args:T})},ro.lineTo=function(R,O){var r=Array.prototype.slice.call(arguments),s=this.actions[this.actions.length-1].args,A=s[s.length-2],Q=s[s.length-1],p=new $q(new Kc(A,Q),new Kc(R,O));this.curves.push(p),this.actions.push({action:yb,args:r})},ro.quadraticCurveTo=function(v,R,l,P){var d=Array.prototype.slice.call(arguments),i=this.actions[this.actions.length-1].args,N=i[i.length-2],Q=i[i.length-1],h=new Vl(new Kc(N,Q),new Kc(v,R),new Kc(l,P));this.curves.push(h),this.actions.push({action:yr,args:d})},ro.bezierCurveTo=function(n,Z,L,A,C,_){var e=Array.prototype.slice.call(arguments),J=this.actions[this.actions.length-1].args,B=J[J.length-2],w=J[J.length-1],X=new Bs(new Kc(B,w),new Kc(n,Z),new Kc(L,A),new Kc(C,_));this.curves.push(X),this.actions.push({action:$d,args:e})},ro.arc=function(H,W,h,D,s,E){var x=this.actions[this.actions.length-1].args,v=x[x.length-2],P=x[x.length-1];this.absarc(H+v,W+P,h,D,s,E)},ro.absarc=function(n,p,N,D,V,A){this.absellipse(n,p,N,N,D,V,A)},ro.ellipse=function(v,Z,D,$,o,N,S){var c=this.actions[this.actions.length-1].args,U=c[c.length-2],O=c[c.length-1];this.absellipse(v+U,Z+O,D,$,o,N,S)},ro.absellipse=function(a,u,G,w,V,p,M){var n=Array.prototype.slice.call(arguments),y=new yj(a,u,G,w,V,p,M);this.curves.push(y);var A=y.getPoint(1);n.push(A.x),n.push(A.y),this.actions.push({action:Ab,args:n})},ro.getSpacedPoints=function(X){X||(X=40);for(var c=[],O=0;X>O;O++)c.push(this.getPoint(O/X));return c},ro.getPoints=function(t,B){t=t||12;var y,A,R,l,f,i,G,X,I,O,H,p,E,k,m,K,q,V,M=[];for(y=0,A=this.actions.length;A>y;y++)switch(R=this.actions[y],l=R.action,f=R.args,l){case Lb:M.push(new Kc(f[0],f[1]));break;case yb:M.push(new Kc(f[0],f[1]));break;case yr:for(i=f[2],G=f[3],O=f[0],H=f[1],M.length>0?(k=M[M.length-1],p=k.x,E=k.y):(k=this.actions[y-1].args,p=k[k.length-2],E=k[k.length-1]),m=1;t>=m;m++)K=m/t,q=Md.b2(K,p,O,i),V=Md.b2(K,E,H,G),M.push(new Kc(q,V));break;case $d:for(i=f[4],G=f[5],O=f[0],H=f[1],X=f[2],I=f[3],M.length>0?(k=M[M.length-1],p=k.x,E=k.y):(k=this.actions[y-1].args,p=k[k.length-2],E=k[k.length-1]),m=1;t>=m;m++)K=m/t,q=Md.b3(K,p,O,X,i),V=Md.b3(K,E,H,I,G),M.push(new Kc(q,V));break;case sd:var a,Q=f[0],D=f[1],$=f[2],u=f[3],W=f[4],Y=!!f[5],C=W-u,g=2*t;for(m=1;g>=m;m++)K=m/g,Y||(K=1-K),a=u+K*C,q=Q+$*N(a),V=D+$*n(a),M.push(new Kc(q,V));break;case Ab:var a,Q=f[0],D=f[1],e=f[2],z=f[3],u=f[4],W=f[5],Y=!!f[6],C=W-u,g=2*t;for(m=1;g>=m;m++)K=m/g,Y||(K=1-K),a=u+K*C,q=Q+e*N(a),V=D+z*n(a),M.push(new Kc(q,V))}var P=M[M.length-1],x=1e-10;return J(P.x-M[0].x)<x&&J(P.y-M[0].y)<x&&M.splice(M.length-1,1),B&&M.push(M[0]),M},ro.toShapes=function(K,q){function u(I){var Y,h,v,d,F,g=[],w=new Lg;for(Y=0,h=I.length;h>Y;Y++)v=I[Y],F=v.args,d=v.action,d==Lb&&0!=w.actions.length&&(g.push(w),w=new Lg),w[d].apply(w,F);return 0!=w.actions.length&&g.push(w),g}function n(v){for(var l=[],k=0,d=v.length;d>k;k++){var W=v[k],q=new ek;q.actions=W.actions,q.curves=W.curves,l.push(q)}return l}function M(V,d){for(var k=1e-10,K=d.length,Z=!1,U=K-1,L=0;K>L;U=L++){var D=d[U],X=d[L],O=X.x-D.x,p=X.y-D.y;if(J(p)>k){if(0>p&&(D=d[L],O=-O,X=d[U],p=-p),V.y<D.y||V.y>X.y)continue;if(V.y==D.y){if(V.x==D.x)return!0}else{var q=p*(V.x-D.x)-O*(V.y-D.y);if(0==q)return!0;if(0>q)continue;Z=!Z}}else{if(V.y!=D.y)continue;if(X.x<=V.x&&V.x<=D.x||D.x<=V.x&&V.x<=X.x)return!0}}return Z}var c=u(this.actions);if(0==c.length)return[];if(q===!0)return n(c);var a,p,S,r=[];if(1==c.length)return p=c[0],S=new ek,S.actions=p.actions,S.curves=p.curves,r.push(S),r;var O=!ek.Utils.isClockWise(c[0].getPoints());O=K?!O:O;var B,w=[],b=[],v=[],E=0;b[E]=j,v[E]=[];var X,Q;for(X=0,Q=c.length;Q>X;X++)p=c[X],B=p.getPoints(),a=Md.isClockWise(B),a=K?!a:a,a?(!O&&b[E]&&E++,b[E]={s:new ek,p:B},b[E].s.actions=p.actions,b[E].s.curves=p.curves,O&&E++,v[E]=[]):v[E].push({h:p,p:B[0]});if(!b[0])return n(c);if(b.length>1){for(var V=!1,$=[],z=0,F=b.length;F>z;z++)w[z]=[];for(var z=0,F=b.length;F>z;z++){b[z];for(var G=v[z],C=0;C<G.length;C++){for(var e=G[C],d=!0,N=0;N<b.length;N++)M(e.p,b[N].p)&&(z!=N&&$.push({froms:z,tos:N,hole:C}),d?(d=!1,w[N].push(e)):V=!0);d&&w[z].push(e)}}$.length>0&&(V||(v=w))}var R,k,A;for(X=0,Q=b.length;Q>X;X++)for(S=b[X].s,r.push(S),R=v[X],k=0,A=R.length;A>k;k++)S.holes.push(R[k].h);return r};var ek=function(){Lg.apply(this,arguments),this.holes=[]},hd=ek.prototype=es(ro);hd.getPointsHoles=function(j){var w,I=this.holes.length,Q=[];for(w=0;I>w;w++)Q[w]=this.holes[w].getTransformedPoints(j);return Q},hd.extractAllPoints=function(H){return{shape:this.getTransformedPoints(H),holes:this.getPointsHoles(H)}},hd.extractPoints=function(a){return this.extractAllPoints(a)};var Md={triangulateShape:function(D,u){function y(w,G,r){return w.x!=G.x?w.x<G.x?w.x<=r.x&&r.x<=G.x:G.x<=r.x&&r.x<=w.x:w.y<G.y?w.y<=r.y&&r.y<=G.y:G.y<=r.y&&r.y<=w.y}function k(u,n,v,j,E){var C=1e-10,i=n.x-u.x,f=n.y-u.y,L=j.x-v.x,P=j.y-v.y,Z=u.x-v.x,I=u.y-v.y,m=f*L-i*P,R=f*Z-i*I;if(J(m)>C){var D;if(m>0){if(0>R||R>m)return[];if(D=P*Z-L*I,0>D||D>m)return[]}else{if(R>0||m>R)return[];if(D=P*Z-L*I,D>0||m>D)return[]}if(0==D)return!E||0!=R&&R!=m?[u]:[];if(D==m)return!E||0!=R&&R!=m?[n]:[];if(0==R)return[v];if(R==m)return[j];var G=D/m;return[{x:u.x+G*i,y:u.y+G*f}]}if(0!=R||P*Z!=L*I)return[];var h=0==i&&0==f,o=0==L&&0==P;if(h&&o)return u.x!=v.x||u.y!=v.y?[]:[u];if(h)return y(v,j,u)?[u]:[];if(o)return y(u,n,v)?[v]:[];var c,M,w,V,N,s,b,g;return 0!=i?(u.x<n.x?(c=u,w=u.x,M=n,V=n.x):(c=n,w=n.x,M=u,V=u.x),v.x<j.x?(N=v,b=v.x,s=j,g=j.x):(N=j,b=j.x,s=v,g=v.x)):(u.y<n.y?(c=u,w=u.y,M=n,V=n.y):(c=n,w=n.y,M=u,V=u.y),v.y<j.y?(N=v,b=v.y,s=j,g=j.y):(N=j,b=j.y,s=v,g=v.y)),b>=w?b>V?[]:V==b?E?[]:[N]:g>=V?[N,M]:[N,s]:w>g?[]:w==g?E?[]:[c]:g>=V?[c,M]:[c,s]}function o(h,t,l,N){var r=1e-10,Y=t.x-h.x,W=t.y-h.y,b=l.x-h.x,H=l.y-h.y,V=N.x-h.x,X=N.y-h.y,f=Y*H-W*b,z=Y*X-W*V;if(J(f)>r){var T=V*H-X*b;return f>0?z>=0&&T>=0:z>=0||T>=0}return z>0}function R(A,N){function u(q,V){var R=Q.length-1,t=q-1;0>t&&(t=R);var I=q+1;I>R&&(I=0);var T=o(Q[q],Q[t],Q[I],E[V]);if(!T)return!1;var l=E.length-1,S=V-1;0>S&&(S=l);var v=V+1;return v>l&&(v=0),T=o(E[V],E[S],E[v],Q[q]),T?!0:!1}function r(Z,d){var R,i,j;for(R=0;R<Q.length;R++)if(i=R+1,i%=Q.length,j=k(Z,d,Q[R],Q[i],!0),j.length>0)return!0;return!1}function M(f,z){var Z,v,K,J,P;for(Z=0;Z<Y.length;Z++)for(v=N[Y[Z]],K=0;K<v.length;K++)if(J=K+1,J%=v.length,P=k(f,z,v[K],v[J],!0),P.length>0)return!0;return!1}for(var E,J,X,F,B,m,t,U,z,C,x,Q=A.concat(),Y=[],R=[],q=0,v=N.length;v>q;q++)Y.push(q);for(var I=0,c=2*Y.length;Y.length>0&&(c--,!(0>c));)for(X=I;X<Q.length;X++){F=Q[X],J=-1;for(var q=0;q<Y.length;q++)if(m=Y[q],t=F.x+":"+F.y+":"+m,R[t]===j){E=N[m];for(var K=0;K<E.length;K++)if(B=E[K],u(X,K)&&!r(F,B)&&!M(F,B)){J=K,Y.splice(q,1),U=Q.slice(0,X+1),z=Q.slice(X),C=E.slice(J),x=E.slice(0,J+1),Q=U.concat(C).concat(x).concat(z),I=X;break}if(J>=0)break;R[t]=!0}if(J>=0)break}return Q}for(var W,r,f,c,z,Q,F={},X=D.concat(),N=0,i=u.length;i>N;N++)Array.prototype.push.apply(X,u[N]);for(W=0,r=X.length;r>W;W++)z=X[W].x+":"+X[W].y,F[z]!==j,F[z]=W;var x=R(D,u),v=Vh.Triangulate(x,!1);for(W=0,r=v.length;r>W;W++)for(c=v[W],f=0;3>f;f++)z=c[f].x+":"+c[f].y,Q=F[z],Q!==j&&(c[f]=Q);return v.concat()},isClockWise:function(h){return Vh.Triangulate.area(h)<0},b2p0:function(N,D){var T=1-N;return T*T*D},b2p1:function(K,a){return 2*(1-K)*K*a},b2p2:function(l,O){return l*l*O},b2:function(A,Y,t,j){return this.b2p0(A,Y)+this.b2p1(A,t)+this.b2p2(A,j)},b3p0:function(J,Q){var b=1-J;return b*b*b*Q},b3p1:function(D,l){var e=1-D;return 3*e*e*D*l},b3p2:function(v,r){var W=1-v;return 3*W*v*v*r},b3p3:function(I,v){return I*I*I*v},b3:function($,W,v,j,D){return this.b3p0($,W)+this.b3p1($,v)+this.b3p2($,j)+this.b3p3($,D)}},Vh={faces:{},face:"helvetiker",weight:"normal",style:"normal",size:150,divisions:10,getFace:function(){try{return this.faces[this.face][this.weight][this.style]}catch(B){throw"The font "+this.face+" with "+this.weight+" weight and "+this.style+" style is missing."}},loadFace:function(A){var n=A.familyName.toLowerCase(),k=this;return k.faces[n]=k.faces[n]||{},k.faces[n][A.cssFontWeight]=k.faces[n][A.cssFontWeight]||{},k.faces[n][A.cssFontWeight][A.cssFontStyle]=A,k.faces[n][A.cssFontWeight][A.cssFontStyle]=A,A},drawText:function(D){var C,B=this.getFace(),T=this.size/B.resolution,r=0,h=String(D).split(""),f=h.length,L=[];for(C=0;f>C;C++){var d=new Lg,P=this.extractGlyphPoints(h[C],B,T,r,d);r+=P.offset,L.push(P.path)}var u=r/2;return{paths:L,offset:u}},extractGlyphPoints:function($,c,U,h,y){var F,m,n,P,T,I,i,k,D,w,R,O,B,N,e,_,W,b,X,H=[],V=c.glyphs[$]||c.glyphs["?"];if(V){if(V.o)for(P=V._cachedOutline||(V._cachedOutline=V.o.split(" ")),I=P.length,i=U,k=U,F=0;I>F;)switch(T=P[F++]){case"m":D=P[F++]*i+h,w=P[F++]*k,y.moveTo(D,w);break;case"l":D=P[F++]*i+h,w=P[F++]*k,y.lineTo(D,w);break;case"q":if(R=P[F++]*i+h,O=P[F++]*k,e=P[F++]*i+h,_=P[F++]*k,y.quadraticCurveTo(e,_,R,O),X=H[H.length-1])for(B=X.x,N=X.y,m=1,n=this.divisions;n>=m;m++){var L=m/n;Md.b2(L,B,e,R),Md.b2(L,N,_,O)}break;case"b":if(R=P[F++]*i+h,O=P[F++]*k,e=P[F++]*i+h,_=P[F++]*k,W=P[F++]*i+h,b=P[F++]*k,y.bezierCurveTo(e,_,W,b,R,O),X=H[H.length-1])for(B=X.x,N=X.y,m=1,n=this.divisions;n>=m;m++){var L=m/n;Md.b3(L,B,e,W,R),Md.b3(L,N,_,b,O)}}return{offset:V.ha*U,path:y}}}};Vh.generateShapes=function(k,o){o=o||{};var D=o.size!==j?o.size:100,K=o.curveSegments!==j?o.curveSegments:4,d=o.font!==j?o.font:"helvetiker",M=o.weight!==j?o.weight:"normal",i=o.style!==j?o.style:"normal";Vh.size=D,Vh.divisions=K,Vh.face=d,Vh.weight=M,Vh.style=i;for(var r=Vh.drawText(k),Q=r.paths,V=[],q=0,x=Q.length;x>q;q++)Array.prototype.push.apply(V,Q[q].toShapes());return V},function(Q){var R=1e-10,a=function(D,x){var R=D.length;if(3>R)return null;var m,b,p,n=[],h=[],t=[];if(z(D)>0)for(b=0;R>b;b++)h[b]=b;else for(b=0;R>b;b++)h[b]=R-1-b;var o=R,E=2*o;for(b=o-1;o>2;){if(E--<=0)return x?t:n;if(m=b,m>=o&&(m=0),b=m+1,b>=o&&(b=0),p=b+1,p>=o&&(p=0),g(D,m,b,p,o,h)){var r,F,B,Y,f;for(r=h[m],F=h[b],B=h[p],n.push([D[r],D[F],D[B]]),t.push([h[m],h[b],h[p]]),Y=b,f=b+1;o>f;Y++,f++)h[Y]=h[f];o--,E=2*o}}return x?t:n},z=function(B){for(var r=B.length,o=0,V=r-1,E=0;r>E;V=E++)o+=B[V].x*B[E].y-B[E].x*B[V].y;return.5*o},g=function(q,Z,H,l,S,i){var h,K,W,a,B,L,e,O,P;if(K=q[i[Z]].x,W=q[i[Z]].y,a=q[i[H]].x,B=q[i[H]].y,L=q[i[l]].x,e=q[i[l]].y,R>(a-K)*(e-W)-(B-W)*(L-K))return!1;var g,_,U,v,u,G,f,V,A,d,D,z,x,s,k;for(g=L-a,_=e-B,U=K-L,v=W-e,u=a-K,G=B-W,h=0;S>h;h++)if(O=q[i[h]].x,P=q[i[h]].y,!(O===K&&P===W||O===a&&P===B||O===L&&P===e)&&(f=O-K,V=P-W,A=O-a,d=P-B,D=O-L,z=P-e,k=g*d-_*A,x=u*V-G*f,s=U*z-v*D,k>=-R&&s>=-R&&x>=-R))return!1;return!0};return Q.Triangulate=a,Q.Triangulate.area=z,Q}(Vh),G._typeface_js={faces:Vh.faces,loadFace:Vh.loadFace};var bc=function(){this.vertices=[],this.faces=[],this.faceVertexUvs=[[]]},op=bc.prototype={constructor:bc,computeFaceNormals:function(){for(var C=new Xm,q=new Xm,P=0,m=this.faces.length;m>P;P++){var a=this.faces[P],$=this.vertices[a.a],R=this.vertices[a.b],z=this.vertices[a.c];C.subVectors(z,R),q.subVectors($,R),C.cross(q),C.normalize(),a.normal.copy(C)}},computeVertexNormals:function(r){var z,R,O,I,V,T;for(T=new Array(this.vertices.length),z=0,R=this.vertices.length;R>z;z++)T[z]=new Xm;if(r){var h,i,N,j=new Xm,t=new Xm;for(O=0,I=this.faces.length;I>O;O++)V=this.faces[O],h=this.vertices[V.a],i=this.vertices[V.b],N=this.vertices[V.c],j.subVectors(N,i),t.subVectors(h,i),j.cross(t),T[V.a].add(j),T[V.b].add(j),T[V.c].add(j)}else for(O=0,I=this.faces.length;I>O;O++)V=this.faces[O],T[V.a].add(V.normal),T[V.b].add(V.normal),T[V.c].add(V.normal);for(z=0,R=this.vertices.length;R>z;z++)T[z].normalize();for(O=0,I=this.faces.length;I>O;O++)V=this.faces[O],V.vertexNormals[0]=T[V.a].clone(),V.vertexNormals[1]=T[V.b].clone(),V.vertexNormals[2]=T[V.c].clone()},mergeVertices:function(){var W,w,$,z,f,X,s,g,r={},E=[],G=[],U=4,D=l(10,U);for($=0,z=this.vertices.length;z>$;$++)W=this.vertices[$],w=K(W.x*D)+"_"+K(W.y*D)+"_"+K(W.z*D),r[w]===j?(r[w]=$,E.push(this.vertices[$]),G[$]=E.length-1):G[$]=G[r[w]];var I=[];for($=0,z=this.faces.length;z>$;$++){f=this.faces[$],f.a=G[f.a],f.b=G[f.b],f.c=G[f.c],X=[f.a,f.b,f.c];for(var i=0;3>i;i++)if(X[i]==X[(i+1)%3]){I.push($);break}}for($=I.length-1;$>=0;$--){var L=I[$];for(this.faces.splice(L,1),s=0,g=this.faceVertexUvs.length;g>s;s++)this.faceVertexUvs[s].splice(L,1)}var O=this.vertices.length-E.length;return this.vertices=E,O}},yj=function(L,Q,d,v,q,c,u){this.aX=L,this.aY=Q,this.xRadius=d,this.yRadius=v,this.aStartAngle=q,this.aEndAngle=c,this.aClockwise=u};yj.prototype=es(Bj),yj.prototype.getPoint=function(R){var V,a=this.aEndAngle-this.aStartAngle;0>a&&(a+=z),a>z&&(a-=z),V=this.aClockwise===!0?this.aEndAngle+(1-R)*(z-a):this.aStartAngle+R*a;var P=this.aX+this.xRadius*N(V),j=this.aY+this.yRadius*n(V);return new Kc(P,j)};var $q=function(e,V){this.v1=e,this.v2=V},So=$q.prototype=es(Bj);So.getPoint=function(e){var I=this.v2.clone().sub(this.v1);return I.multiplyScalar(e).add(this.v1),I},So.getPointAt=function(Y){return this.getPoint(Y)},So.getTangent=function(){var v=this.v2.clone().sub(this.v1);return v.normalize()};var Vl=function(w,l,g){this.v0=w,this.v1=l,this.v2=g};Vl.prototype=es(Bj),Vl.prototype.getPoint=function(B){var G,Y;return G=Md.b2(B,this.v0.x,this.v1.x,this.v2.x),Y=Md.b2(B,this.v0.y,this.v1.y,this.v2.y),new Kc(G,Y)};var Bs=function(v,d,H,y){this.v0=v,this.v1=d,this.v2=H,this.v3=y};Bs.prototype=es(Bj),Bs.prototype.getPoint=function(m){var p,u;return p=Md.b3(m,this.v0.x,this.v1.x,this.v2.x,this.v3.x),u=Md.b3(m,this.v0.y,this.v1.y,this.v2.y,this.v3.y),new Kc(p,u)},Lq.create(function(G,D){this.v1=G,this.v2=D},function(q){var Y=new Xm;return Y.subVectors(this.v2,this.v1),Y.multiplyScalar(q),Y.add(this.v1),Y});var gn=Lq.create(function(a,H,b){this.v0=a,this.v1=H,this.v2=b},function(M){var m,x,X;return m=Md.b2(M,this.v0.x,this.v1.x,this.v2.x),x=Md.b2(M,this.v0.y,this.v1.y,this.v2.y),X=Md.b2(M,this.v0.z,this.v1.z,this.v2.z),new Xm(m,x,X)}),_c=Lq.create(function(y,L,E,J){this.v0=y,this.v1=L,this.v2=E,this.v3=J},function(G){var J,R,X;return J=Md.b3(G,this.v0.x,this.v1.x,this.v2.x,this.v3.x),R=Md.b3(G,this.v0.y,this.v1.y,this.v2.y,this.v3.y),X=Md.b3(G,this.v0.z,this.v1.z,this.v2.z,this.v3.z),new Xm(J,R,X)}),Pm=function(d,p,k,v,g,f,S,W){bc.call(this),v=v!==j?v:.5,g=g!==j?g:.5,W=W!==j?W:1,d=d||8,f=f||0,S=S||z;var P,x,V=1,K=W/2,h=[],U=[],B=this.vertices,L=this.faces,E=this.faceVertexUvs;for(x=0;V>=x;x++){var b=[],e=[],a=x/V,o=a*(g-v)+v;for(P=0;d>=P;P++){var C=P/d,I=new Xm,Z=-(C*S+f);I.z=o*n(Z),I.y=-a*W+K,I.x=o*N(Z),B.push(I),b.push(B.length-1),e.push(new Kc(C,a))}h.push(b),U.push(e)}var Y,s,D=(g-v)/W;for(P=0;d>P;P++)for(0!==v?(Y=B[h[0][P]].clone(),s=B[h[0][P+1]].clone()):(Y=B[h[1][P]].clone(),s=B[h[1][P+1]].clone()),Y.setY(F(Y.x*Y.x+Y.z*Y.z)*D).normalize(),s.setY(F(s.x*s.x+s.z*s.z)*D).normalize(),x=0;V>x;x++){var q=h[x][P],R=h[x+1][P],m=h[x+1][P+1],T=h[x][P+1],H=Y.clone(),l=Y.clone(),r=s.clone(),t=s.clone(),M=U[x][P].clone(),_=U[x+1][P].clone(),y=U[x+1][P+1].clone(),c=U[x][P+1].clone();L.push(new af(q,R,T,[H,l,t])),E[0].push([M,_,c]),L.push(new af(R,m,T,[l.clone(),r,t.clone()])),E[0].push([_.clone(),y,c.clone()])}if(p&&v>0)for(this.vertices.push(new Xm(0,K,0)),P=0;d>P;P++){var q=h[0][P],R=h[0][P+1],m=this.vertices.length-1,H=new Xm(0,1,0),l=new Xm(0,1,0),r=new Xm(0,1,0),M=U[0][P].clone(),_=U[0][P+1].clone(),y=new Kc(_.x,0);L.push(new af(q,R,m,[H,l,r],null,1));var J=P/d*z,$=N(J),O=n(J),G=(P+1)/d*z,A=N(G),Q=n(G);E[0].push([new Kc(.5+.5*$,.5+.5*O),new Kc(.5+.5*A,.5+.5*Q),new Kc(.5,.5)])}if(k&&g>0)for(this.vertices.push(new Xm(0,-K,0)),P=0;d>P;P++){var q=h[x][P+1],R=h[x][P],m=B.length-1,H=new Xm(0,-1,0),l=new Xm(0,-1,0),r=new Xm(0,-1,0),M=U[x][P+1].clone(),_=U[x][P].clone();L.push(new af(q,R,m,[H,l,r],null,2));var J=P/d*z,$=N(J),O=n(J),G=(P+1)/d*z,A=N(G),Q=n(G);E[0].push([new Kc(.5+.5*A,.5-.5*Q),new Kc(.5+.5*$,.5-.5*O),new Kc(.5,.5)])}};Pm.prototype=es(op);var tn=function(A,Q,K,W,B,u,R){bc.call(this),R=R||.5,A=A||16,Q=Q||16,K=(K!==j?K:0)-p,W=W!==j?W:z,B=B!==j?B:0,u=u!==j?u:p;var i,E,O=[],y=[],Z=this.vertices,D=this.faces,P=this.faceVertexUvs;for(E=0;Q>=E;E++){var d=[],e=[];for(i=0;A>=i;i++){var o=i/A,l=E/Q,b=new Xm;b.x=-R*N(K+o*W)*n(B+l*u),b.y=R*N(B+l*u),b.z=R*n(K+o*W)*n(B+l*u),Z.push(b),d.push(Z.length-1),e.push(new Kc(o,l))}O.push(d),y.push(e)}for(E=0;Q>E;E++)for(i=0;A>i;i++){var I=O[E][i+1],h=O[E][i],k=O[E+1][i],G=O[E+1][i+1],F=Z[I].clone().normalize(),q=Z[h].clone().normalize(),x=Z[k].clone().normalize(),g=Z[G].clone().normalize(),L=y[E][i+1].clone(),s=y[E][i].clone(),X=y[E+1][i].clone(),v=y[E+1][i+1].clone();J(Z[I].y)===R?(L.x=(L.x+s.x)/2,D.push(new af(I,k,G,[F,x,g])),P[0].push([L,X,v])):J(Z[k].y)===R?(X.x=(X.x+v.x)/2,D.push(new af(I,h,k,[F,q,x])),P[0].push([L,s,X])):(D.push(new af(I,h,G,[F,q,g])),P[0].push([L,s,v]),D.push(new af(h,k,G,[q.clone(),x,g.clone()])),P[0].push([s.clone(),X,v.clone()]))}};tn.prototype=es(op);var Qf=function(q,g,Z,b,t,W){bc.call(this),q=q||.33,g=g||.17,b=b||8,Z=Z||6,t=t||0,W=W||z;for(var O=new Xm,x=[],D=[],I=0;b>=I;I++)for(var T=I/b*z+p,U=0;Z>=U;U++){var e=U/Z*W+t;O.x=q*N(e),O.z=-q*n(e);var C=new Xm,S=q+g*N(T);C.x=S*N(e),C.z=-S*n(e),C.y=g*n(T),this.vertices.push(C),x.push(new Kc(U/Z,1-I/b)),D.push(C.clone().sub(O).normalize())}for(var I=1;b>=I;I++)for(var U=1;Z>=U;U++){var P=(Z+1)*I+U-1,G=(Z+1)*(I-1)+U-1,w=(Z+1)*(I-1)+U,J=(Z+1)*I+U,k=new af(P,G,J,[D[P].clone(),D[G].clone(),D[J].clone()]);this.faces.push(k),this.faceVertexUvs[0].push([x[P].clone(),x[G].clone(),x[J].clone()]),k=new af(G,w,J,[D[G].clone(),D[w].clone(),D[J].clone()]),this.faces.push(k),this.faceVertexUvs[0].push([x[G].clone(),x[w].clone(),x[J].clone()])}this.computeFaceNormals()};Qf.prototype=es(op);var Rk=function(t,E,Z){return t?(bc.call(this),t=t instanceof Array?t:[t],this.addShapeList(t,E),Pi(this,Z),this.computeFaceNormals(),void 0):(t=[],void 0)},Zp=Rk.prototype=es(bc.prototype);Zp.addShapeList=function(n,O){for(var X=n.length,x=0;X>x;x++){var t=n[x];this.addShape(t,O)}},Zp.addShape=function(X,K){function J(){if(K.bottom)for(var J=0;g>J;J++)c=S[J],l(c[2],c[1],c[0],!0);if(K.top)for(J=0;g>J;J++)c=S[J],l(c[0]+Y*H,c[1]+Y*H,c[2]+Y*H,!1)}function t(){var y=0;for(E(b,y),y+=b.length,h=0,p=k.length;p>h;h++)Q=k[h],E(Q,y),y+=Q.length}function E(h,g){for(var D,m,P=h.length;--P>=0;){D=P,m=P-1,0>m&&(m=h.length-1);var b=0,G=H;for(b=0;G>b;b++){var q=Y*b,K=Y*(b+1),R=g+D+q,k=g+m+q,F=g+m+K,o=g+D+K;f(R,k,F,o,h,b,G,D,m)}}}function F(x,s,i){P.vertices.push(new Xm(x,s,i))}function l(m,V,x,w){m+=r,V+=r,x+=r,P.faces.push(new af(m,V,x,null,null,w?2:1));var A=w?z.generateBottomUV(P,X,K,m,V,x):z.generateTopUV(P,X,K,m,V,x);P.faceVertexUvs[0].push(A)}function f(S,s,Q,i,J,x,M,A,O){S+=r,s+=r,Q+=r,i+=r,P.faces.push(new af(S,s,i)),P.faces.push(new af(s,Q,i));var l=z.generateSideWallUV(P,X,J,K,S,s,Q,i,x,M,A,O);P.faceVertexUvs[0].push([l[0],l[1],l[3]]),P.faceVertexUvs[0].push([l[1],l[2],l[3]])}var w,W,A,O,N,$=K.amount,e=K.curveSegments||xs,H=K.steps||1,m=K.extrudePath,I=!1,z=new Sb(K.repeatUVLength);m&&(w=m.getSpacedPoints(H),I=!0,W=K.frames!==j?K.frames:new ks.FrenetFrames(m,H,!1),A=new Xm,O=new Xm,N=new Xm);var Q,h,p,P=this,r=this.vertices.length,U=X.extractPoints(e),o=U.shape,k=U.holes,y=!Md.isClockWise(o);if(y){for(o=o.reverse(),h=0,p=k.length;p>h;h++)Q=k[h],Md.isClockWise(Q)&&(k[h]=Q.reverse());y=!1}var S=Md.triangulateShape(o,k),b=o;for(h=0,p=k.length;p>h;h++)Q=k[h],o=o.concat(Q);for(var i,c,Y=o.length,g=S.length,D=0;Y>D;D++)i=o[D],I?(O.copy(W.normals[0]).multiplyScalar(i.x),A.copy(W.binormals[0]).multiplyScalar(i.y),N.copy(w[0]).add(O).add(A),F(N.x,N.y,N.z)):F(i.x,i.y,0);var u;for(u=1;H>=u;u++)for(D=0;Y>D;D++)i=o[D],I?(O.copy(W.normals[u]).multiplyScalar(i.x),A.copy(W.binormals[u]).multiplyScalar(i.y),N.copy(w[u]).add(O).add(A),F(N.x,N.y,N.z)):F(i.x,i.y,$/H*u);J(),t()};var ks=function(E,p,J,K,c){function q(m,z,b){return f.vertices.push(new Xm(m,z,b))-1}bc.call(this),p=p||64,J=J||1,K=K||8,c=c||!1;var j,r,Q,C,o,$,A,m,L,t,R,W,y,T,Y,k,F,S,V,Z,M=[],f=this,l=p+1,d=new Xm,x=new ks.FrenetFrames(E,p,c),e=x.tangents,s=x.normals,u=x.binormals;for(this.tangents=e,this.normals=s,this.binormals=u,L=0;l>L;L++)for(M[L]=[],C=L/(l-1),m=E.getPointAt(C),j=e[L],r=s[L],Q=u[L],t=0;K>t;t++)o=t/K*z,$=-J*N(o),A=J*n(o),d.copy(m),d.x+=$*r.x+A*Q.x,d.y+=$*r.y+A*Q.y,d.z+=$*r.z+A*Q.z,M[L][t]=q(d.x,d.y,d.z);for(L=0;p>L;L++)for(t=0;K>t;t++)R=c?(L+1)%p:L+1,W=(t+1)%K,y=M[L][t],T=M[R][t],Y=M[R][W],k=M[L][W],F=new Kc(L/p,t/K),S=new Kc((L+1)/p,t/K),V=new Kc((L+1)/p,(t+1)/K),Z=new Kc(L/p,(t+1)/K),this.faces.push(new af(y,T,k)),this.faceVertexUvs[0].push([F,S,Z]),this.faces.push(new af(T,Y,k)),this.faceVertexUvs[0].push([S.clone(),V,Z.clone()]);this.computeFaceNormals(),this.computeVertexNormals()};ks.prototype=es(op),ks.FrenetFrames=function(Q,m,n){function C(){W[0]=new Xm,p[0]=new Xm,i=Number.MAX_VALUE,D=J(w[0].x),y=J(w[0].y),k=J(w[0].z),i>=D&&(i=D,x.set(1,0,0)),i>=y&&(i=y,x.set(0,1,0)),i>=k&&x.set(0,0,1),u.crossVectors(w[0],x).normalize(),W[0].crossVectors(w[0],u),p[0].crossVectors(w[0],W[0])}var l,i,D,y,k,f,U,x=new Xm,w=[],W=[],p=[],u=new Xm,o=new to,g=m+1,h=1e-4;for(this.tangents=w,this.normals=W,this.binormals=p,f=0;g>f;f++)U=f/(g-1),w[f]=Q.getTangentAt(U),w[f].normalize();for(C(),f=1;g>f;f++)W[f]=W[f-1].clone(),p[f]=p[f-1].clone(),u.crossVectors(w[f-1],w[f]),u.length()>h&&(u.normalize(),l=H(Wo(w[f-1].dot(w[f]),-1,1)),W[f].applyMatrix4(o.makeRotationAxis(u,l))),p[f].crossVectors(w[f],W[f]);if(n)for(l=H(Wo(W[0].dot(W[g-1]),-1,1)),l/=g-1,w[0].dot(u.crossVectors(W[0],W[g-1]))>0&&(l=-l),f=1;g>f;f++)W[f].applyMatrix4(o.makeRotationAxis(w[f],l*f)),p[f].crossVectors(w[f],W[f])};var _p=function(g,W,T,C){bc.call(this),W=W||18,T=T||0,C=C==f?z:C;for(var b=1/(g.length-1),j=1/W,O=0,k=W;k>=O;O++)for(var q=T+O*j*C,x=N(q),D=n(q),o=0,Y=g.length;Y>o;o++){var t=g[o],i=new Xm;i.x=x*t.x-D*t.y,i.y=D*t.x+x*t.y,i.z=t.z,this.vertices.push(i)}for(var Z=g.length,O=0,k=W;k>O;O++)for(var o=0,Y=g.length-1;Y>o;o++){var m=o+Z*O,_=m,p=m+Z,x=m+1+Z,y=m+1,d=O*j,v=o*b,B=d+j,Q=v+b;this.faces.push(new af(_,y,p)),this.faceVertexUvs[0].push([new Kc(d,v),new Kc(d,Q),new Kc(B,v)]),this.faces.push(new af(p,y,x)),this.faceVertexUvs[0].push([new Kc(B,v),new Kc(d,Q),new Kc(B,Q)])}this.mergeVertices(),Pi(this),this.computeFaceNormals(),this.computeVertexNormals()};_p.prototype=es(op);var pq=function(C,Y){Y=Y||{};var e=Vh.generateShapes(C,Y);Y.amount=Y.height!==j?Y.height:50,Rk.call(this,e,Y)};pq.prototype=es(Zp);var Ir=y.Node=function(){Wn(Ir,this)},Cl={X:_b,Y:fj,Z:Bn},kp={xyz:"XYZ",xzy:"XZY",yxz:"YXZ",yzx:"YZX",zxy:"ZXY",zyx:"ZYX"},Tk="xzy",fs=function(y,z,o){if(z){var e=z[0],W=z[1],I=z[2];"xzy"===o?(fj(y,W),Bn(y,I),_b(y,e)):"xyz"===o?(Bn(y,I),fj(y,W),_b(y,e)):"yxz"===o?(Bn(y,I),_b(y,e),fj(y,W)):"yzx"===o?(_b(y,e),Bn(y,I),fj(y,W)):"zxy"===o?(fj(y,W),_b(y,e),Bn(y,I)):"zyx"===o?(_b(y,e),fj(y,W),Bn(y,I)):(fj(y,W),Bn(y,I),_b(y,e))}};ol("Node",kb,{ms_ac:["rotationMode","tall"],_adjustChildrenToTop:!0,_icon:"node_icon",_image:"node_image",_rotationMode:Tk,_64O:0,_rotationX:0,_53O:0,_host:f,_position:{x:0,y:0},_tall:20,_54O:0,getUIClass:function(){return ao},_22Q:function(){return lo},p:function(){return 0===arguments.length?this.getPosition():(this.setPosition.apply(this,arguments),this)},p3:function(){return 0===arguments.length?this.getPosition3d():(this.setPosition3d.apply(this,arguments),this)},s3:function(){return 0===arguments.length?this.getSize3d():(this.setSize3d.apply(this,arguments),this)},r3:function(){return 0===arguments.length?this.getRotation3d():(this.setRotation3d.apply(this,arguments),this)},t3:function(){return this.translate3d.apply(this,arguments),this},translate3dBy:function(Z,w){zj(Z,lq(f,f,this.r3(),this.getRotationMode())),this.translate3d(Z[0]*w,Z[1]*w,Z[2]*w)},translateFront:function(j){this.translate3dBy([0,0,1],j)},translateBack:function(U){this.translate3dBy([0,0,-1],U)},translateLeft:function(S){this.translate3dBy([-1,0,0],S)},translateRight:function(V){this.translate3dBy([1,0,0],V)},translateTop:function(J){this.translate3dBy([0,1,0],J)},translateBottom:function(d){this.translate3dBy([0,-1,0],d)},getPosition3d:function(){return[this._position.x,this._54O,this._position.y]},setPosition3d:function(e,Q,p){1===arguments.length&&(Q=e[1],p=e[2],e=e[0]),this.p(e,p),this.setElevation(Q)},translate3d:function(d,M,H){1===arguments.length&&(M=d[1],H=d[2],d=d[0]),this.translate(d,H),this.setElevation(this._54O+M)},getSize3d:function(){return[this.getWidth(),this.getTall(),this.getHeight()]},setSize3d:function(l,t,z){1===arguments.length&&(t=l[1],z=l[2],l=l[0]),this.setSize(l,z),this.setTall(t)},getRotation3d:function(){return[this._rotationX,-this._64O,this._53O]},setRotation3d:function(T,W,R){1===arguments.length&&(W=T[1],R=T[2],T=T[0]),this.setRotationX(T),this.setRotation(-W),this.setRotationZ(R)},setRotationY:function(N){this.setRotation(-N)},getRotationY:function(){return-this._64O},lookAt:function(u,k){k=k||Q;var K=this,C=Fl(u,K.p3()),g=nm(C);k===Q?(K.r3([-X(C[1]/g),-q(C[2],C[0])+v,0]),K.setRotationMode("xzy")):k===_?(K.r3(0,-q(C[2],C[0]),X(C[1]/g)),K.setRotationMode("zyx")):k===S?(K.r3(0,-q(C[2],C[0])+p,-X(C[1]/g)),K.setRotationMode("zyx")):k===xi?(K.r3([-X(C[1]/g)+v,-q(C[2],C[0])+v,0]),K.setRotationMode("xzy")):k===Kh&&(K.r3([-X(C[1]/g)-v,-q(C[2],C[0])+v,0]),K.setRotationMode("xzy")),k===o&&(K.r3([-X(C[1]/g)+p,-q(C[2],C[0])+v,p]),K.setRotationMode("zxy"))},getLoopedEdges:function(){return this._45I},getEdges:function(){return this._70O},getAgentEdges:function(){return this._49I},getHost:function(){return this._host},setHost:function(q){var H=this;if(H!==q&&H._host!==q){var X=H._host;X&&X._removeAttach(H),H._host=q,H._host&&H._host._addAttach(H),H.fp("host",X,q),H.onHostChanged(X,q)}},getAttaches:function(){return this._69O},_addAttach:function(Z){var C=this;C._69O||(C._69O=new Xc),C._69O.add(Z),C.fp("attaches",f,Z)},_removeAttach:function(s){var w=this;w._69O.remove(s),w._69O.isEmpty()&&delete w._69O,w.fp("attaches",s,f)},getSourceEdges:function(){return this._42I},getTargetEdges:function(){return this._43I},_2I:function(h){var c=this;c._44I||(c._44I=new Xc),c._42I||(c._42I=new Xc),c._44I.add(h),c._42I.add(h),c._20I()},_4I:function(i){var t=this;t._44I||(t._44I=new Xc),t._43I||(t._43I=new Xc),t._44I.add(i),t._43I.add(i),t._20I()},_16I:function(Z){var y=this;y._44I.remove(Z),y._42I.remove(Z),y._44I.isEmpty()&&delete y._44I,y._42I.isEmpty()&&delete y._42I,y._20I()},_18I:function(V){var o=this;o._44I.remove(V),o._43I.remove(V),o._44I.isEmpty()&&delete o._44I,o._43I.isEmpty()&&delete o._43I,o._20I()},_20I:function(){var X=this;if(delete X._45I,!X._44I||X._44I.isEmpty())return delete X._70O,void 0;var p;X._44I.each(function(n){n.isLooped()&&(p||(p={}),p[n._id]||(X._45I||(X._45I=new Xc),X._45I.add(n),p[n._id]=n))}),p?(X._70O=new Xc,X._44I.each(function(O){p[O._id]?"A"===p[O._id]||(p[O._id]="A",X._70O.add(O)):X._70O.add(O)})):X._70O=X._44I},hasAgentEdges:function(){return!!this._49I&&!this._49I.isEmpty()},getSourceAgentEdges:function(){return this._46I},getTargetAgentEdges:function(){return this._47I},_1I:function(Z){var O=this;O._46I||(O._46I=new Xc),O._48I||(O._48I=new Xc),O._46I.add(Z),O._48I.add(Z),O._19I()},_3I:function(P){var G=this;G._47I||(G._47I=new Xc),G._48I||(G._48I=new Xc),G._47I.add(P),G._48I.add(P),G._19I()},_15I:function(u){var n=this;n._46I.remove(u),n._48I.remove(u),n._46I.isEmpty()&&delete n._46I,n._48I.isEmpty()&&delete n._48I,n._19I()},_17I:function(A){var M=this;M._47I.remove(A),M._48I.remove(A),M._47I.isEmpty()&&delete M._47I,M._48I.isEmpty()&&delete M._48I,M._19I()},_19I:function(){var R=this;delete R._49I;var S=R._48I;if(S&&!S.isEmpty()){var k={};S.each(function(G){k[G._id]?R._49I||(R._49I=new Xc):k[G._id]=G}),R._49I?S.each(function(I){k[I._id]&&(R._49I.add(I),delete k[I._id])}):R._49I=S}},getImage:function(){return this._image},setImage:function(N){var F=this,i=F._image,Y=F.getWidth(),H=F.getHeight();F._image=N,F.fp("image",i,N),F.fp(Le,Y,F.getWidth()),F.fp(Em,H,F.getHeight())},getElevation:function(){return this._54O},setElevation:function(W){var u=this;if(!u._50O){u._50O=1;var E=u._54O;u._54O=W,u.fp(_f,E,W),delete this._50O}},getRotation:function(){return this._64O},setRotation:function(t){var Z=this;if(!Z._49O){Z._49O=1;var S=Z._64O;Z._64O=t,Z.fp(Qg,S,t),delete Z._49O}},getRotationX:function(){return this._rotationX},setRotationX:function(l){var _=this;if(!_._51O){_._51O=1;var d=_._rotationX;_._rotationX=l,_.fp("rotationX",d,l),delete _._51O}},getRotationZ:function(){return this._53O},setRotationZ:function(Y){var J=this;if(!J._52O){J._52O=1;var a=J._53O;J._53O=Y,J.fp("rotationZ",a,Y),delete J._52O}},getPosition:function(){return this._position},setPosition:function(q,K){var l=this;if(!l._50I){l._50I=1;var V;if(V=2===arguments.length?{x:q,y:K}:q,V.x!==l._position.x||V.y!==l._position.y){var H=l._position;l._position=V,l.fp(Dj,H,V)}delete l._50I}},translate:function($,O){var p=this._position;this.p(p.x+$,p.y+O)},getWidth:function(){var d=this;if(d._width>=0)return d._width;var O=cj(d._image);return O?eq(O,d):20},setWidth:function(U){var g=this,_=g._width;g._width=U,g.fp(Le,_,U)},getHeight:function(){var L=this;if(L._height>=0)return L._height;var B=cj(L._image);return B?Oo(B,L):20},setHeight:function(K){var Q=this,h=Q._height;Q._height=K,Q.fp(Em,h,K)},setSize:function(g,j){var x=this;2===arguments.length?(x.setWidth(g),x.setHeight(j)):(x.setWidth(g.width),x.setHeight(g.height))},getSize:function(){return{width:this.getWidth(),height:this.getHeight()}},setRect:function(S,o,b,q){var h=this;1===arguments.length?(h.p(S.x+S.width/2,S.y+S.height/2),h.setWidth(S.width),h.setHeight(S.height)):(h.p(S+b/2,o+q/2),h.setWidth(b),h.setHeight(q))},getRect:function(){var A=this,C=A.getWidth(),x=A.getHeight();return zf({x:A._position.x-C/2,y:A._position.y-x/2,width:C,height:x},A._64O)},getCorners:function(I,R){I==f&&(I=0),R==f&&(R=I);var Z=this,O=Z._position,c=Z.getWidth()/2+I,u=Z.getHeight()/2+R,X=new pf(Z._64O,O.x,O.y);return[X.tf(-c,-u),X.tf(c,-u),X.tf(c,u),X.tf(-c,u)]},rotateAt:function(S,L,d){var z=this,u=z._position,y=z._64O,I=new pf(y,u.x,u.y).tf(S,L),p=F(S*S+L*L),X=q(u.y-I.y,u.x-I.x)+d;z.setRotation(y+d),z.p(I.x+p*N(X),I.y+p*n(X))},onParentChanged:function(){Ir.superClass.onParentChanged.apply(this,arguments),this._8I()},_8I:function(){this._70O&&this._70O.each(function(i){i._7I()
})},onPropertyChanged:function(v){var Z=this;Ir.superClass.onPropertyChanged.call(Z,v),Z._69O&&Z._69O.each(function(Q){Q.handleHostPropertyChange(v)}),Z._49I&&Z._49I.each(function(b){b.fp("agentChange",!0,!1)})},onHostChanged:function(){this.updateAttach()},handleHostPropertyChange:function(c){this.updateAttach(c)},onStyleChanged:function(c){Ir.superClass.onStyleChanged.apply(this,arguments),uk[c]&&this.updateAttach()},updateAttach:function(S){var Z=this;Z._51I||Yf||(Z._51I=1,Z._71O(S),delete Z._51I)},_71O:function(J){var i,x,S,z,e,R,h=this,X=h._host,L=J?J.property:f,I=J?J.oldValue:f,H=J?J.newValue:f;if(X instanceof qe){if(L===_f)h.setElevation(h._54O+H-I);else if(!J||Oe[L]){if(i=h.s("attach.row.index"),x=h.s("attach.column.index"),S=X.getCellRect(i,x),!S)return;z=h.s("attach.row.span"),e=h.s("attach.column.span"),(1!==z||1!==e)&&(S=Uq(S,X.getCellRect(i+z-1,x+e-1))),Hr(S,h,"attach.padding",1),h.setRect(S)}}else if(X instanceof Si&&(R=h.s("attach.index"))>=0&&(!J||Th[L])){var G=h.s("attach.thickness");G!=f&&h.setHeight(X.getThickness()*G);var g=h.s("attach.offset"),j=X.getPoints(),d=j.size();if(d>R){var u=j.get(R),t=d===R+1?j.get(0):j.get(R+1),K=[t.x-u.x,t.y-u.y],k=nm(K);if(k){h.s("attach.offset.relative")&&(g*=k),h.s("attach.offset.opposite")&&(g=k-g);var _={x:u.x+K[0]/k*g,y:u.y+K[1]/k*g},C=h.s("attach.gap");C&&(h.s("attach.gap.relative")&&(C*=X.getThickness()),_=Nm(f,_,t,C)),h.p(_),h.setRotation(q(K[1],K[0]))}}}else J&&(L===Dj?h.translate(H.x-I.x,H.y-I.y):L===_f?h.setElevation(h._54O+H-I):Dr[L]&&h._11Q(X,L,H-I))},_11Q:function(U,T,$){var O=this,K=U.p3(),Q=Fl(O.p3(),K),E=Vp(),j=kp[O.getRotationMode()],t=j[0],f=j[1],D=j[2],F=Cl[t],S=Cl[f],q=Cl[D];T===Qg&&(T="rotationY",$=-$),T===Qg+t?(q(E,U[we+D]()),S(E,U[we+f]()),F(E,$),S(E,-U[we+f]()),q(E,-U[we+D]()),O[Ip+t](O[we+t]()+$)):T===Qg+f?(q(E,U[we+D]()),S(E,$),q(E,-U[we+D]()),O[Ip+f](O[we+f]()+$)):T===Qg+D&&(q(E,$),O[Ip+D](O[we+D]()+$)),zj(Q,E),O.p3(K[0]+Q[0],K[1]+Q[1],K[2]+Q[2])},isHostOn:function(w){var Y=this;if(Y._host&&w&&w._69O)for(var A={},b=Y._host;b&&b!==Y&&!A[b._id];){if(b===w)return!0;A[b._id]=b,b=b._host}return!1},isLoopedHostOn:function($){return this.isHostOn($)&&$.isHostOn(this)},getSerializableProperties:function(){var r=Ir.superClass.getSerializableProperties.call(this);return Ym(r,{image:1,host:1,rotation:1,rotationX:1,rotationZ:1,rotationMode:1,position:1,width:1,height:1,tall:1,elevation:1}),r}});var gk=y.Edge=function(N,w){var q=this;Wn(gk,q),q.setSource(N),q.setTarget(w)};ol("Edge",kb,{_icon:"edge_icon",getUIClass:function(){return Ul},_22Q:function(){return Ll},getSource:function(){return this._source},getTarget:function(){return this._target},getSourceAgent:function(){return this._40I},getTargetAgent:function(){return this._41I},setSource:function(A){var B=this;if(B._source!==A){var P=B._source;B._source=A,P&&P._16I(B),A&&A._2I(B),B._7I(),B.fp("source",P,A)}},setTarget:function(N){var t=this;if(t._target!==N){var j=t._target;t._target=N,j&&j._18I(t),N&&N._4I(t),t._7I(),t.fp("target",j,N)}},isLooped:function(){var Y=this;return Y._source===Y._target&&!!Y._source&&!!Y._target},_7I:function(){var C,Y=this,p=Rl(Y);Y._40I!==p&&(C=this._40I,C&&C._15I(Y),Y._40I=p,p&&p._1I(Y),Y.fp("sourceAgent",C,p),te(C,Y._41I),te(p,Y._41I));var o=Qk(Y);Y._41I!==o&&(C=Y._41I,C&&C._17I(Y),Y._41I=o,o&&o._3I(Y),Y.fp("targetAgent",C,o),te(C,Y._40I),te(o,Y._40I))},_22I:function(E){this._52I=E,this.fp("edgeGroup",!0,!1)},getEdgeGroup:function(){return this._52I},isEdgeGroupHidden:function(){var s=this;return s._52I&&s._52I.get(0)!==s&&!s.getStyle(oe)},getEdgeGroupSize:function(){return this._52I?this._52I.size():1},getEdgeGroupIndex:function(){return this._52I?this._52I.indexOf(this):0},isEdgeGroupAgent:function(){var S=this,T=S._52I;return T&&!S.getStyle(oe)&&T.size()>1&&S===T.get(0)},toggle:function(){var U=this._52I,$=!this.s(oe);U&&U.size()>1&&(U.each(function(r){r.s(oe,$)}),U.getSiblings().each(function(l){l!==U&&l.each(function(J){J.fp("edgeGroup",f,l)})}))},setStyle:function(r,o){(r===pg||"edge.segments"===r)&&(o=Sc(o)),gk.superClass.setStyle.call(this,r,o)},onStyleChanged:function(T){gk.superClass.onStyleChanged.apply(this,arguments),wk[T]&&te(this._41I,this._40I)},getSerializableProperties:function(){var u=gk.superClass.getSerializableProperties.call(this);return Ym(u,{source:1,target:1}),u}});var Jj=y.Group=function(){Wn(Jj,this)};ol("Group",Ir,{_image:"group_image",_icon:"group_icon",_57O:!1,getUIClass:function(){return zd},onChildAdded:function(){Jj.superClass.onChildAdded.apply(this,arguments),this._81I()},onChildRemoved:function(){Jj.superClass.onChildRemoved.apply(this,arguments),this._81I()},_81I:function(){var k=this;if(!k._54I&&!Yf){var c=k.getChildrenRect();c&&(k._53I=1,k.p(fb(k.s("group.position"),c,k.getSize())),delete k._53I)}},getChildrenRect:function(){var x,b=this;return b.eachChild(function($){x=Uq(x,b.getChildRect($))}),x},getChildRect:function(L){var m;return Qr(L)&&L.s(Rm)&&(sj(L)&&L.isExpanded()&&L.eachChild(function($){m=Uq(m,L.getChildRect($))}),!m&&L.getRect&&(m=L.getRect())),m},setPosition:function(Z,e){var $=this;if(!$._54I){var m;m=2===arguments.length?{x:Z,y:e}:Z,Yf||$._53I||($._54I=1,bh($._children,m.x-$._position.x,m.y-$._position.y),delete $._54I),Jj.superClass.setPosition.call(this,m)}},toggle:function(){this.setExpanded(!this.isExpanded())},isExpanded:function(){return this._57O},setExpanded:function(c){var B=this;if(B._57O!==c){var M=B._57O;B._57O=c,B.fp("expanded",M,B._57O),B._8I()}},_8I:function(){Jj.superClass._8I.call(this),this.eachChild(function(H){Qr(H)&&H._8I()})},onStyleChanged:function(H){Jj.superClass.onStyleChanged.apply(this,arguments),"group.position"===H&&this._81I()},getSerializableProperties:function(){var l=Jj.superClass.getSerializableProperties.call(this);return l.expanded=1,l}});var qe=y.Grid=function(){Wn(qe,this)};ol("Grid",Ir,{IRotatable:!1,_icon:"grid_icon",_image:f,getUIClass:function(){return Hf},setRotation:function(){},getCellRect:function(e,$){var S=this,Q=S.s("grid.row.count"),h=S.s("grid.column.count");if(0>=Q||0>=h||0>e||e>=Q||0>$||$>=h)return f;var u,z,I,L=S.getRect(),d=S.s("grid.row.percents"),v=S.s("grid.column.percents");if(Hr(L,S,"grid.border",-1),d&&d._as&&(d=d._as),v&&v._as&&(v=v._as),d&&d.length===Q){for(I=0,u=0;e>u;u++)I+=L.height*d[u];L.y+=I,L.height=L.height*d[e]}else L.height=L.height/Q,L.y+=L.height*e;if(v&&v.length===h){for(z=0,u=0;$>u;u++)z+=L.width*v[u];L.x+=z,L.width=L.width*v[$]}else L.width=L.width/h,L.x+=L.width*$;return In(L,-S.s("grid.gap")),L}});var Si=y.Shape=function(){Wn(Si,this),this._59O=new Xc};Si.__de__=Oi,ol("Shape",Ir,{ms_ac:["thickness","closePath"],_icon:"shape_icon",_thickness:10,_closePath:!1,getUIClass:function(){return Ub},_22Q:function(){return mm},getLength:function(m){return vf(Rr(this._59O,this._58O,m,this._closePath))},getSegments:function(){return this._58O},toSegments:function(){var w=this._58O;return w?new Xc(w._as.slice(0)):w},setSegments:function(M){var G=this._58O;M=M?Ji(M)?new Xc(M.slice(0)):new Xc(M._as.slice(0)):new Xc,(G&&M&&G._as.join(",")!==M._as.join(",")||G!==M)&&(this._58O=M,this.fp("segments",G,M))},getPoints:function(){return this._59O},toPoints:function(){var I=this._59O;return new Xc(I._as.slice(0))},setPoints:function(Q){var G=this._59O;Q=Q?Ji(Q)?new Xc(Q.slice(0)):new Xc(Q._as.slice(0)):new Xc,(G&&Q&&G._as.join(",")!==Q._as.join(",")||G!==Q)&&(this._59O=Q,this.fs(),this.fp("points",G,Q))},addPoint:function(Z,w){var b=this.toPoints();b.add(Z,w),this.setPoints(b)},setPoint:function(F,g){var Z=this.toPoints();Z.set(F,g),this.setPoints(Z)},removePointAt:function(e){var O=this.toPoints();O.removeAt(e),this.setPoints(O)},setWidth:function(a){var F=this;if(1>a&&(a=1),!F._55I&&!Yf&&F.getWidth()){F._55I=1;var D=F._position.x,g=a/F.getWidth(),t=F.toPoints(),n=new Xc;t.each(function(u){n.add({x:(u.x-D)*g+D,y:u.y,e:u.e})}),F.setPoints(n),F.fs(),delete F._55I}Si.superClass.setWidth.call(F,a)},setHeight:function(B){var G=this;if(1>B&&(B=1),!G._55I&&!Yf&&G.getHeight()){G._55I=1;var n=G._position.y,t=B/G.getHeight(),F=G.toPoints(),U=new Xc;F.each(function(R){U.add({x:R.x,y:(R.y-n)*t+n,e:R.e})}),G.setPoints(U),G.fs(),delete G._55I}Si.superClass.setHeight.call(G,B)},setPosition:function(J,_){var R,d=this;if(!d._28Q){if(d._28Q=1,!d._55I&&!Yf){R=2===arguments.length?{x:J,y:_}:J;var o=R.x-d._position.x,Y=R.y-d._position.y;if(0===o&&0===Y)return delete d._28Q,void 0;d._55I=1;var c=d.toPoints(),m=new Xc;c.each(function(Q){m.add({x:Q.x+o,y:Q.y+Y,e:Q.e})}),d.setPoints(m),d.fs(),delete d._55I}Si.superClass.setPosition.apply(d,arguments),delete d._28Q}},fs:function(){this.fireShapeChange()},fireShapeChange:function(){var a=this;if(!a._55I&&!Yf){var G=ai(a._59O);G&&(a._55I=1,a.setRect(G),delete a._55I)}a.fp(xh,!1,!0)},getSerializableProperties:function(){var K=Si.superClass.getSerializableProperties.call(this);return K.segments=1,K.points=1,K.thickness=1,K.closePath=1,K}});var nh=y.Polyline=function(){Wn(nh,this),this.s({"shape.background":null,"shape.border.width":2})};ol("Polyline",Si,{IRotatable:!1,_icon:"polyline_icon",_22Q:function(){return dj},getUIClass:function(){return cl},setRotationX:function(){},setRotation:function(){},setRotationZ:function(){},setClosePath:function(){},setTall:function(W){var n=this;if(!n._24Q&&!Yf&&n.getTall()){n._24Q=1;var i=n._54O,b=W/n.getTall();n._59O.each(function(L){L.e=L.e==f?i:(L.e-i)*b+i}),n.fs(),delete n._24Q}nh.superClass.setTall.call(n,W)},setElevation:function(A){var O=this;if(!O._24Q&&!Yf){O._24Q=1;var q=A-O._54O;O._59O.each(function(F){F.e==f?F.e=A:F.e+=q}),O.fs(),delete O._24Q}nh.superClass.setElevation.apply(O,arguments)},fireShapeChange:function(){var p=this,X=p._59O;if(!p._24Q&&!Yf){var B=X.size();if(B){var S=1,W=X.get(0),J=p._54O;W.e==f&&(W.e=J);for(var x=W.e,C=0;B>S;S++){W=X.get(S),W.e==f&&(W.e=J);var I=O(x,W.e),U=b(x+C,W.e);x=I,C=U-I}p._24Q=1,p.setTall(C),p.setElevation(x+C/2),delete p._24Q}}nh.superClass.fireShapeChange.apply(p,arguments)}});var br=y.SubGraph=function(){Wn(br,this)};ol("SubGraph",Ir,{ISubGraph:1,_image:"subGraph_image",_icon:"subGraph_icon",_8I:function(){br.superClass._8I.call(this),this.eachChild(function(F){Qr(F)&&F._8I()})}}),y.EdgeGroup=function(s,U){this._70O=s,this._siblings=U;for(var r,z,S=0,H=s.size(),J=R[oe];H>S;S++)if(r=s.get(S).getStyle(oe,!1),r!=f){J=r;break}J==f&&(J=!0);var P=u.edgeGroupAgentFunc;for(P&&(z=P(s),z&&z!==s.get(0)&&(s.remove(z),s.add(z,0))),S=0;H>S;S++)s.get(S).s(oe,J)},ol("EdgeGroup",M,{getEdges:function(){return this._70O},size:function(){return this._70O.size()},get:function(L){return this._70O.get(L)},indexOf:function(c){return this._70O.indexOf(c)},each:function(V,S){this._70O.each(V,S)},getSiblings:function(){return this._siblings},eachSiblingEdge:function(S,H){this._siblings.each(function(q){q._70O.each(S,H)})}});var hg=y.JSONSerializer=function(B,R){this.dm=this._dataModel=B,this._hierarchical=!!R};ol("JSONSerializer",M,{ms_ac:["hierarchical"],serialize:function(s){return JSON.stringify(this.toJSON(),function(V,R){return sh(R)?pm(R):R},s==f?2:s)},toJSON:function(){var U=this,N=U.dm,I=N.getRoots(),t=U.json={v:u.getVersion(),d:[],a:{}},B=N.getSerializableAttrs();for(var s in B){var X=N.a(s);X!==j&&U[Qc](s,X,t.a)}return vj(t.a)&&delete t.a,U._hierarchical?I.each(U.serializeData,this):N.each(function(a){U.serializeData(a)}),t},isSerializable:function(){return!0},getProperties:function(w){return w.getSerializableProperties()},getStyles:function(h){return h.getSerializableStyles()},getAttrs:function(N){return N.getSerializableAttrs()},serializeData:function(S){var T=this;if(T.isSerializable(S)){var O,g,u,h,Y=S.getClass(),d=new Y,V={c:S.getClassName(),i:S.getId(),p:{},s:{},a:{}};T.json.d.push(V),O=T.getProperties(S);for(g in O)h=dd(g),S[h]&&(u=S[h](),u!==d[h]()&&T[Qc](g,u,V.p));O=T.getStyles(S);for(g in O)u=S.s(g),u!==d.s(g)&&T[Qc](g,u,V.s);O=T.getAttrs(S);for(g in O)u=S.a(g),u!==d.a(g)&&T[Qc](g,u,V.a);vj(V.p)&&delete V.p,vj(V.s)&&delete V.s,vj(V.a)&&delete V.a}T._hierarchical&&S.getChildren().each(T.serializeData,T)},serializeValue:function(f,k,P){El(k)?k={__a:k._as}:k instanceof kb&&(k={__i:k.getId()}),P[f]=k},deserialize:function(m,g,h){Yf=!0;var I=this;m=I.json=sl(m)?JSON.parse(m):m,I._82I={};for(var X=I.dm,F=new Xc,J=new Xc,A=0,M=m.d.length;M>A;A++){var y=m.d[A],K=Bf(y.c),H=new K,R=y.i;h&&R!=f&&(H._id=R),I._82I[R]=H,F.add(H),J.add(y)}for(A=0;M>A;A++)I.deserializeData(F.get(A),J.get(A));F.each(function(p){g&&!p.getParent()&&p.setParent(g),X.add(p)});for(var U in m.a)X.a(U,I[Ei](m.a[U]));return Yf=!1,F},deserializeData:function(v,Z){for(var c in Z.p)v[Rp(c)](this[Ei](Z.p[c]));for(c in Z.s)v.s(c,this[Ei](Z.s[c]));for(c in Z.a)v.a(c,this[Ei](Z.a[c]))},deserializeValue:function(G){if(ze(G)){var k=G.__i;if(k!=f)return this._82I[k];if(k=G.__a,Ji(k))return new Xc(k)}return G}}),Ym(Db,{serialize:function(q,R){return new hg(this,R).serialize(q)},toJSON:function(F){return new hg(this,F).toJSON()},deserialize:function(W,T,d){return new hg(this).deserialize(W,T,d)}}),Lm.GraphView=function(o){var O=this;O._24I={},O._34I=new Xc,O._25I={},O._56I={},O._view=Xb(1),O._canvas=Pf(O._view),O.dm(o?o:new Db),O.setEditable(!1),O.setScrollBarVisible(u.graphViewScrollBarVisible)},Od("GraphView",M,{ms_v:1,ms_gv:1,ms_bnb:1,ms_tip:1,ms_dm:1,ms_lp:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,_52o:1,_51o:1,ms_ac:["layers",zs,kd,ic,cn,"resettable","editInteractor",Tg,"pannable","rectSelectable","autoScrollZone",bp,"movableFunc","editableFunc","pointEditableFunc","rectEditableFunc","rotationEditableFunc","rectSelectBackground","rectSelectBorderColor","editPointSize","editPointBorderColor","editPointBackground"],_resettable:u.graphViewResettable,_pannable:u.graphViewPannable,_rectSelectable:u.graphViewRectSelectable,_autoScrollZone:u.graphViewAutoScrollZone,_rectSelectBackground:u.graphViewRectSelectBackground,_rectSelectBorderColor:u.graphViewRectSelectBorderColor,_editPointSize:u.graphViewEditPointSize,_editPointBorderColor:u.graphViewEditPointBorderColor,_editPointBackground:u.graphViewEditPointBackground,_scrollBarColor:pj,_scrollBarSize:xb,_autoHideScrollBar:Wi,_autoMakeVisible:or,setEditable:function(j){var M=this,X=Lm.XEditInteractor;j?M.setInteractors([new pb(M),X?new X(M):new $f(M),new fe(M),new Ug(M),new Wk(M,{editable:!X})]):M.setInteractors([new pb(M),new fe(M),new Ug(M),new Wk(M,{editable:!1})])},getCanvas:function(){return this._canvas},_33I:function(X){var Y=X.getUIClass();return Y?new Y(this,X):f},getDataUI:function(r){var v=this,Q=v._25I[r._id];return Q===j&&(Q=v._33I(r),v._25I[r._id]=Q),Q},invalidateAll:function(S){var J=this;if(S){for(var i in J._25I){var O=J._25I[i];O&&O.dispose()}J._25I={},J._56I={},J._24I={},J._34I.clear(),J.redraw()}else J.dm().each(function(b){J.invalidateData(b)})},invalidateSelection:function(){var Z=this;Z.sm().each(function(I){Z.invalidateData(I)})},invalidateData:function(U){var f=this;f._24I[U._id]=U,f._21Q(U),f.iv()},_21Q:function(e){var Y=this,t=Y._24I;if(oo(e)&&Pd[e.s(hq)]){var a=e.getSourceAgent();a&&a.getAgentEdges().each(function(G){t[G._id]=G}),a=e.getTargetAgent(),a&&a.getAgentEdges().each(function(E){t[E._id]=E}),Y.iv()}},redraw:function(C){var r=this;r._32I||(C?r._34I.add(C):(r._32I=1,r._34I.clear()),r.iv())},each:function(b,z){var l,F,j,v=0,e=this._layers,k=this._dataModel._datas._as,w=k.length;if(e)for(var d=e.length;d>v;v++){j=e[v];for(var y=0;w>y;y++)if(l=k[y],l._layer===j&&(F=z?b.call(z,l):b(l),F===!1))return}else for(;w>v;v++)if(l=k[v],F=z?b.call(z,l):b(l),F===!1)return},reverseEach:function(v,S){var U,E,o,u,D=this._layers,d=this._dataModel._datas._as,F=d.length;if(D)for(U=D.length-1;U>=0;U--){u=D[U];for(var f=F-1;f>=0;f--)if(E=d[f],E._layer===u&&(o=S?v.call(S,E):v(E),o===!1))return}else for(U=F-1;U>=0;U--)if(E=d[U],o=S?v.call(S,E):v(E),o===!1)return},getViewRect:function(){return this._29I},getContentRect:function(){var t=this,l=t._84I;return l||(t.each(function(j){t.isVisible(j)&&(l=Uq(l,t.getDataUIBounds(j)))}),t._84I=l?l:Df),t._84I},getScrollRect:function(){return Uq(this.getContentRect(),this._29I)},fitData:function(s,M,$,L,m){var A=this;if(!A.getWidth()||!A.getHeight())return m||Dp(A.fitData,A,[s,M,$,L,!0],200),void 0;A.makeVisible(s),A.validate();var b=A.getDataUIBounds(s);b&&(b=el(b),In(b,$==f?20:$),A.fitRect(b,M,L))},fitContent:function(h,N,Z,n){var b=this;if(!b.getWidth()||!b.getHeight())return n||Dp(b.fitContent,b,[h,N,Z,!0],200),void 0;b.validate();var i=el(b.getContentRect());In(i,N==f?20:N),b.fitRect(i,h,Z)},fitRect:function(d,I,T){var i=this,a=i.getWidth(),Z=i.getHeight(),N=d.x+d.width/2,W=d.y+d.height/2,q=O(a/d.width,Z/d.height),F=i._zoom,f=-N*F+a/2,y=-W*F+Z/2;0===q||isNaN(q)||(T&&(q=O(1,q)),I?i.setTranslate(f,y,{finishFunc:function(){i.setZoom(q,I)}}):(i.setTranslate(f,y),i.setZoom(q)))},toCanvas:function(D){this.validateImpl();var o=this,b=o.getContentRect(),$=Pf(),X=o._zoom,p=b.x*X,K=b.y*X,y=b.width*X,j=b.height*X;Gr($,y,j,1);var U=cs($);return D&&$i(U,0,0,y,j,D),On(U,-p,-K),U.scale(X,X),o._42(U),U.restore(),$},toDataURL:function(t,o){return this.toCanvas(t).toDataURL(o||"image/png",1)},getClipBounds:function(){return this._74O},_42:function(v,s){var X,T,O=this;O._93db(v,s),O.each(function(i){O._56I[i._id]&&(X=O.getDataUI(i),X&&(T=X._79o(),y.HtmlNode&&i instanceof y.HtmlNode?X._42(v):(!s||yc(s,T))&&X._42(v)))}),O._92db(v,s)},validateImpl:function(){var u,Q,p,q,N,X=this,_=X.tx(),K=X.ty(),x=X._zoom,G=X._canvas,g=this.getWidth(),Z=this.getHeight(),o=X._29I,l={x:-_/x,y:-K/x,width:g/x,height:Z/x},z={},r=X._34I,J=X._24I,B=X._32I,E=X._23I;(g!==G.clientWidth||Z!==G.clientHeight)&&(Gr(G,g,Z),B=1),B||nn(l,o)||(B=1),X._29I=l,X.each(function(d){Q=d._id;var W=z[Q]=X.isVisible(d);W!==X._56I[Q]&&(J[Q]=d,p=X.getDataUI(J[Q]),p&&p._84o(W),X._84o(d,W))},X);for(Q in J)p=X.getDataUI(J[Q]),p&&(!B&&X._56I[Q]&&(N=p._79o(),N&&r.add(N)),p.invalidate()),X._83I=1;if(X._56I=z,!B)for(Q in J)z[Q]&&(p=X.getDataUI(J[Q]),p&&(N=p._79o(),N&&r.add(N)));if(B?q=l:(r.each(function(T){yc(l,T)&&(q=Uq(q,T))}),q&&(In(q,b(1,1/x)),q.x=T(q.x*x)/x,q.y=T(q.y*x)/x,q.width=a(q.width*x)/x,q.height=a(q.height*x)/x,q=uf(l,q))),X._74O=q,q&&(u=cs(G),lk(u,_,K,x),u.beginPath(),u.rect(q.x,q.y,q.width,q.height),u.clip(),u.clearRect(q.x,q.y,q.width,q.height),X._42(u,q),u.restore(),delete X._74O),X._24I={},r.clear(),delete X._32I,E&&g>0&&Z>0){var p=X.getDataUI(E);if(p){var N=p._79o(),d=X._29I,t=d.x,F=d.y,U=d.width,H=d.height,x=X._zoom;N&&!yc(N,d)&&(N.x+N.width<t&&X.tx(-N.x*x),N.x>t+U&&X.tx(-(N.x+N.width-U)*x),N.y+N.height<F&&X.ty(-N.y*x),N.y>F+H&&X.ty(-(N.y+N.height-H)*x))}delete X._23I}X._83I&&(delete X._83I,delete X._84I),X._98O()},isScrollBarVisible:function(){return!!this._79O},setScrollBarVisible:function($){var O=this;$!==O.isScrollBarVisible()&&($?(nj(O._view,O._79O=Xb()),nj(O._79O,O._27I=Xb()),nj(O._79O,O._28I=Xb())):(vc(O._79O),delete O._79O,delete O._27I,delete O._28I),O.fp("scrollBarVisible",!$,$))},showScrollBar:function(){var $=this;$._79O&&($._85I||(g(function(){$._86I()},Vd),$.iv()),$._85I=new Date)},_86I:function(){var V=this;if(V._85I){var o=new Date,a=o.getTime();a-V._85I.getTime()>=Vd?(delete V._85I,V.iv()):g(function(){V._86I()},Vd)}},_98O:function(){var U=this,D=this._27I,B=this._28I;if(U._79O){if(U._autoHideScrollBar&&!U._85I)return D.style.visibility=$n,B.style.visibility=$n,void 0;var p=U.getScrollBarColor(),A=U._zoom,E=U.getScrollBarSize(),W=U.getViewRect(),n=U.getScrollRect(),o=W.height*A,N=n.height*A,R=W.width*A-E-2,i=o*((W.y-n.y)*A/N),P=o*(o/N),M=D.style;N-.5>o?(re>P&&(i=i+P/2-re/2,0>i&&(i=0),i+re>o&&(i=o-re),P=re),tf(D,R,i,E,P),M.visibility=$g,M.background=p,M.borderRadius=E/2+Yl):M.visibility=$n;var w=W.width*A,l=n.width*A,i=W.height*A-E-2,R=w*((W.x-n.x)*A/l),f=w*(w/l),M=B.style;l-.5>w?(re>f&&(R=R+f/2-re/2,0>R&&(R=0),R+re>w&&(R=w-re),f=re),tf(B,R,i,f,E),M.visibility=$g,M.background=p,M.borderRadius=E/2+Yl):M.visibility=$n}},setDataModel:function(f){var L=this,r=L._dataModel,H=L._3o;r!==f&&(r&&(r.umm(L.handleDataModelChange,L),r.umd(L.handleDataPropertyChange,L),r.removeIndexChangeListener(L._75O,L),H||r.sm().ums(L._16o,L)),L._dataModel=f,f.mm(L.handleDataModelChange,L),f.md(L.handleDataPropertyChange,L),f.addIndexChangeListener(L._75O,L),H?H._21I(f):f.sm().ms(L._16o,L),L.invalidateAll(!0),L.fp(ar,r,f))},handleDataPropertyChange:function(S){this.invalidateData(S.data)},onPropertyChanged:function(B){var O=this,Z=B.property;O.redraw(),ge[Z]?O.showScrollBar():Z===Tg&&O.onCurrentSubGraphChanged(B)},onCurrentSubGraphChanged:function(){this.reset()},handleDataModelChange:function(I){var S=this,v=I.kind,s=I.data;if(S._83I=1,"add"===v)S.invalidateData(s),oo(s)&&s.getEdgeGroup()&&s.getEdgeGroup().eachSiblingEdge(S.invalidateData,S);else if(v===Yi){S._21Q(s);var p=s._id,G=S._25I[p];if(G){if(S._56I[p]){var D=G._79o();D&&S.redraw(D)}G.dispose(),delete S._25I[p],delete S._24I[p],delete S._56I[p]}s===S._currentSubGraph&&S.setCurrentSubGraph(f)}else v===on&&(S.invalidateAll(!0),S.setCurrentSubGraph(f))},_75O:function(N){this.invalidateData(N.data)},_76O:function(F){var A=this;if(A.isVisible(F)){for(var S=F;S._parent&&A.isVisible(S._parent);)S=S._parent;S&&S!==F&&A._dataModel._76O(S),A._dataModel._76O(F)}},isMovable:function(S){var b=this;return oo(S)&&S.getStyle(hq)!==Tq?!1:S.s("2d.movable")?b._movableFunc?b._movableFunc(S):!0:!1},isSelectable:function(M){return M.s("2d.selectable")&&this.sm().isSelectable(M)},isEditable:function(E){var D=this;if(!D._editInteractor||!D.isSelected(E))return!1;if(sj(E)){var G=D.getDataUI(E);if(!G||G._88I)return!1}return oo(E)&&E.getStyle(hq)!==Tq?!1:E.s("2d.editable")?D._editableFunc?D._editableFunc(E):!0:!1},handleDelete:function(){this._editInteractor&&this.removeSelection()},isPointEditable:function(V){return Qr(V)&&0!==V.getRotation()?!1:this._pointEditableFunc?this._pointEditableFunc(V):!0},isRectEditable:function(f){return Qr(f)&&0!==f.getRotation()?!1:this._rectEditableFunc?this._rectEditableFunc(f):!0},isRotationEditable:function(c){return c.setRotation&&c.IRotatable!==!1?this._rotationEditableFunc?this._rotationEditableFunc(c):!0:!1},getRotationPoint:function(I){var d=zl(I.getRotation(),0,-I.getHeight()/2-(w?32:16)),y=I.p();return d.x+=y.x,d.y+=y.y,d},getLogicalPoint:function(d){var c=this;return Dn(d,c._canvas,c.tx(),c.ty(),c._zoom,c._zoom)},getSelectedDataAt:function($){var M=this;return this.getDataAt($,function(R){return M.isSelected(R)})},getDataAt:function(J,Y,y){J.target&&(J=this.lp(J));var n;return this.reverseEach(function(f){return(Y?Y(f):this.isSelectable(f))&&this.rectIntersects(f,Io(J.x,J.y,y))?(n=f,!1):void 0},this),n},getIconInfoAt:function(a,G){var p=this;if(a.target&&(a=p.lp(a)),G||(G=p.getDataAt(a)),G){var x=p.getDataUI(G);if(x&&x._38o){var l,E=x._38o,z=new Xc;for(l in E.icons)z.add(l);for(var V=z.size()-1;V>=0;V--){l=z.get(V);for(var O=E.rects[l],A=O.rotation,M=O.length-1;M>=0;M--){var I=O[M],T=I.width,r=I.height,W={x:a.x-I.x-T/2,y:a.y-I.y-r/2};A!=f&&(W=zl(-A,W.x,W.y));var I={x:-T/2,y:-r/2,width:T,height:r};if(Yn(I,W))return{key:l,index:M,name:E.icons[l].names[M],rect:O[M],point:a,rotation:A,relativeRect:I,relativePoint:W,data:G}}}}}return f},getDatasInRect:function(M,a,I){I===j&&(I=1);var N=this,c=new Xc;return N.reverseEach(function(h){I&&!N.isSelectable(h)||(a?N.rectIntersects(h,M):N.rectContains(h,M))&&c.add(h)}),c},moveSelection:function(M,E){var W,q=this,Y=q.dm();Y&&(W=Y.getHistoryManager()),W&&W.beginInteraction(),bh(q.sm().toSelection(q.isMovable,q),M,E),W&&W.endInteraction()},getDataUIBounds:function(q){var S=this.getDataUI(q);return S?S._79o():f},getBoundsForGroup:function(Q){return Q.s(Rm)?this.getDataUIBounds(Q):f},rectIntersects:function(R,X){this.validate();var I=f;if(this._56I[R._id]){var b=this._25I[R._id];if(b){var p=b._79o();if(gr(X,p))I=!0;else if((X=uf(X,p))&&(b.rectIntersects&&(I=b.rectIntersects(X)),I==f)){var C=X.x,Q=X.y,g=X.width,D=X.height,U=1,A=u.hitMaxArea,y=g*D;y>A&&(U=A/y);var r=Ij(g*U,D*U);On(r,-C*U,-Q*U),r.scale(U,U),b._42(r);try{for(var i=0,B=r.getImageData(0,0,g*U,D*U).data;i<B.length;i+=4)if(0!==B[i+3]){I=!0;break}r.restore()}catch(v){se=f,I=!0}}}}return I?!0:!1},rectContains:function(i,I){if(this._56I[i._id]){var h=this._25I[i._id];if(h)return gr(I,h._79o())}return!1},reset:function(){this.setZoom(1),this.setTranslate(0,0)},handleKeyDown:function(_){var L=this,d=L._focusData,V=d&&L._25I[d._id],Z=0,b=L._dataModel._datas;d&&d._enabled&&d._editable&&V&&V.onKeyDown&&V.onKeyDown(_)===!0||(9===_.keyCode&&(d&&(Z=b.indexOf(d),Cm(_)?-1===--Z&&(Z=0):++Z===b.size()&&(Z=0)),L._focusData=d=b.get(Z),L.sm().setSelection(d)),ui(_)?L.selectAll():td(_)&&L.isResettable()&&L.reset(),L.sm().isEmpty()||(un(_)&&L.handleDelete(_),qn(_)&&(L.moveSelection(-1,0),L.fi({kind:"moveLeft"})),so(_)&&(L.moveSelection(0,-1),L.fi({kind:"moveUp"})),zk(_)&&(L.moveSelection(1,0),L.fi({kind:"moveRight"})),uq(_)&&(L.moveSelection(0,1),L.fi({kind:"moveDown"}))))},handleScroll:function(Z,p){np(Z);var U=this.lp(Z);p>0?this.scrollZoomIn(U):0>p&&this.scrollZoomOut(U)},handlePinch:function(d,t,l){t>l?this.pinchZoomIn(d):this.pinchZoomOut(d)},checkDoubleClickOnNote:function(c,T){var u=this,b=u.lp(c),E=u.getDataUI(T),w=E.note2Info;return w&&T.s("note2.toggleable")&&Yn(w.clickRect,b)?(T.s(_r,!T.s(_r)),u.fi({kind:"toggleNote2",event:c,data:T}),!0):(w=E.noteInfo,w&&T.s("note.toggleable")&&Yn(w.clickRect,b)?(T.s(Id,!T.s(Id)),u.fi({kind:"toggleNote",event:c,data:T}),!0):!1)},_84o:function(){},isNoteVisible:function(){return this._zoom>.15},isLabelVisible:function(){return this._zoom>.15},isSelectVisible:function(){return this._zoom>.15},isEditVisible:function(){return this._zoom>.15},autoScroll:function(a,L){var Q=this,J=Q.getAutoScrollZone(),n=J/Q.getZoom(),P=J/4,M=Q._29I,V=Q.lp(a),X={x:Q.tx(),y:Q.ty()};return V&&J>0&&M&&(V.x-M.x<n?Q.translate(P,0):M.x+M.width-V.x<n&&Q.translate(-P,0),V.y-M.y<n?Q.translate(0,P):M.y+M.height-V.y<n&&Q.translate(0,-P)),X.x=Q.tx()-X.x,X.y=Q.ty()-X.y,L&&(L.x+=X.x,L.y+=X.y),X},getMoveMode:function(S,I){var M=I.s("2d.move.mode");return M?M:If["88"]?"x":If["89"]?"y":"xy"}});var cm=function(B,c){var f=this;f.gv=B,f.s=function(s){return c.getStyle(s)},f._data=c,f._87I=new Xc};gf(cm,M,{_6I:f,ms_icons:1,_84o:function(){},dispose:function(){},isShadowed:function(){return this.gv.isSelected(this._data)&&"shadow"===this.s("select.type")},_2o:function(){var V=this,U=V.gv.isSelected(V._data);return!U||V.isShadowed()?0:this.s("select.width")},getBodyColor:function(J){var F=this._data,i=this.gv.getBodyColor(F);return i?i:J?F.getStyle(J):f},_2Q:function(z){return this.s(z)},_1Q:function(p,o,c,x){var L=this.s,e=L(o+".dash.color");p.strokeStyle=e,p.lineWidth=c,p.stroke(),L(o+".dash.3d")&&Xj(p,e,L(o+".dash.3d.color"),c,this.gv._zoom,L(o+".dash.3d.accuracy")),Hd(p,x)},invalidate:function(){this._6I=f},_79o:function(){var I=this;if(!I._6I){I.labelInfo=I.label2Info=I.noteInfo=I.note2Info=I._38o=f,I._87I.clear(),I._3O();var w=I._data,$=I.gv;I._55O=$.isEditable(w)?{_42O:$.isRectEditable(w),_43O:$.isPointEditable(w),_56O:$.isRotationEditable(w)}:f,I._6I=I._81o()}return I._6I},_3O:function(){},getPosition:function(){return Df},_68o:function(r,N){r&&this._87I.add(zf(r,N))},_81o:function(){var i=this,r=i.s;i._24O(Jm,"getLabel"),i._24O(Ve,"getLabel2"),i._26O(Rg,"getNote"),i._26O(Mi,"getNote2"),i._15O(),i._55O&&i._48O();var Y;if(i._87I.each(function(l){Y=Uq(Y,l)}),Y&&i.isShadowed()){var o=el(Y);o.x+=r("shadow.offset.x"),o.y+=r("shadow.offset.y"),In(o,r("shadow.blur")),Y=Uq(Y,o)}return i._87I.clear(),Y},_42:function(T){var A=this,i=A._data,b=A.gv,W=A.s,O=W("opacity"),p=this.isShadowed();if(p){var U=T.shadowOffsetX,_=T.shadowOffsetY,L=T.shadowBlur,e=T.shadowColor;T.shadowOffsetX=W("shadow.offset.x"),T.shadowOffsetY=W("shadow.offset.y"),T.shadowBlur=W("shadow.blur"),T.shadowColor=W("shadow.color")}if(O!=f){var D=T.globalAlpha;T.globalAlpha=O}A._80o(T),b.isLabelVisible(i)&&(xc(T,A.labelInfo),xc(T,A.label2Info)),b.isNoteVisible(i)&&(Jh(T,A.noteInfo),Jh(T,A.note2Info)),A._99O(T),O!=f&&(T.globalAlpha=D),p&&(T.shadowOffsetX=U,T.shadowOffsetY=_,T.shadowBlur=L,T.shadowColor=e),A._55O&&b.isEditVisible(i)&&A._47O(T)},_80o:function(){},_47O:function(){},_24O:function(j,a){var s=this,z=s._data,Y=s.gv,y=s.s,d=Y[a](z);if(d!=f){var q=y(j+".scale"),Q=y(j+".max"),U=y(j+".position"),x=s[j+"Info"]={label:d,scale:q,color:Y[a+"Color"](z),font:y(j+".font"),opacity:y(j+".opacity"),align:y(j+".align"),rotation:s.getRotation(y(j+".rotation"),!1,U),background:Y[a+"Background"](z)},h=Xl(x,d);Q>0&&Q<h.width&&(x.labelWidth=h.width,h.width=Q),1!==q&&(h.width*=q,h.height*=q);var l=s.getPosition(U,y(j+".offset.x"),y(j+".offset.y"),h,y(j+".position.fixed"));if(h.x=l.x-h.width/2,h.y=l.y-h.height/2,s._68o(x.rect=h,x.rotation),1!==q){var N=h.width/q,m=h.height/q;x.rect={x:l.x-N/2,y:l.y-m/2,width:N,height:m}}}},_26O:function(D,q){var w=this,y=w.gv,d=w._data,X=w.s,p=y[q](d);if(p!=f){var i,b,F=X(D+".scale"),n=w[D+"Info"]={note:p,scale:F,expanded:X(D+".expanded"),font:X(D+".font"),color:X(D+".color"),opacity:X(D+".opacity"),align:X(D+".align"),borderWidth:X(D+".border.width"),borderColor:X(D+".border.color"),background:y[q+"Background"](d)},e=w.getPosition(X(D+".position"),X(D+".offset.x"),X(D+".offset.y")),Q=e.x,c=e.y;if(n.expanded){var j=X(D+".max"),s=Xl(n,p);s.width+=6,s.height+=2,j>0&&j<s.width&&(n.labelWidth=s.width,s.width=j),i=s.width,b=s.height+8,n.clickRect={x:Q-i*F/2,y:c-b*F,width:i*F,height:b*F*s.height/b}}else i=12,b=18,n.clickRect={x:Q-i*F/2,y:c-b*F,width:i*F,height:b*F};n.rect={x:Q-i/2,y:c-b*F/2-b/2,width:i,height:b};var R=a(n.borderWidth/2)*F;w._68o({x:Q-i*F/2-R,y:c-b*F-R,width:i*F+2*R,height:b*F+2*R})}},_48O:function(){},_99O:function(x){var Q=this,l=Q._38o;if(l){var q=Q.gv,G=Q._data,A=l.icons;for(var S in A){var K=A[S],R=l.rects[S];if(R){var b=Fe(K.opacity,G,q),v=Fe(K.names,G,q),g=v?v.length:0,B=R.rotation;if(b!=f){var e=x.globalAlpha;x.globalAlpha*=b}for(var o=0;g>o;o++){var X=v[o],I=cj(X),s=R[o];if(B){var u=s.x+s.width/2,C=s.y+s.height/2;x.save(),On(x,u,C),qr(x,B),On(x,-u,-C)}Li(x,I,Fe(K.stretch,G,q),s.x,s.y,s.width,s.height,Q._data,Q.gv),B&&x.restore()}b!=f&&(x.globalAlpha=e)}}}}});var ao=function(W,Q){Wn(ao,this,[W,Q])};gf(ao,cm,{_40O:function(H,p){var E=this,N=E.s,j=H.rect;(H.borderColor=E.gv.getBorderColor(E._data))&&(H.borderType=N("border.type"),H.borderWidth=N("border.width"),H.borderPadding=N("border.padding"),p=b(p,H.borderPadding+H.borderWidth/2)),(H.selectWidth=E._2o())&&(H.selectType=N("select.type"),H._97o=N("select.color"),H.selectPadding=N("select.padding"),p=b(p,H.selectPadding+H.selectWidth/2)),p>0&&(j=el(j),In(j,p)),E._68o(j)},_39O:function(E,d){var i=d.rect;if(d.borderWidth>0){var q=d.borderPadding;E.strokeStyle=d.borderColor,E.lineWidth=d.borderWidth,Zn(E,d.borderType,{x:i.x-q,y:i.y-q,width:i.width+2*q,height:i.height+2*q}),E.stroke()}d.selectWidth>0&&(q=d.selectPadding,E.strokeStyle=d._97o,E.lineWidth=d.selectWidth,Zn(E,d.selectType,{x:i.x-q,y:i.y-q,width:i.width+2*q,height:i.height+2*q}),E.stroke())},_3O:function(){var U=this,t=U.s,Y=U._data,i=Y.getStyle(xh),H=U.getBodyColor(),A=U._83o=i?{shape:i,_53o:U.getBodyColor("shape.background"),_27Q:cj(t("shape.repeat.image"),H),_54o:t("shape.border.width"),_55o:t("shape.border.color"),_56o:t("shape.border.3d"),_57o:t("shape.border.3d.color"),_58o:t("shape.border.3d.accuracy"),shapeGradient:t("shape.gradient"),_59o:t("shape.gradient.color"),_60o:t("shape.border.pattern"),_61o:t("shape.border.cap"),_62o:t("shape.border.join")}:{img:cj(Y.getImage(),H),bodyColor:H,stretch:t("image.stretch")};A.rect=Y.getRect(),A.position=Y.p(),A.rotation=Y.getRotation(),i===ik&&(A._63o=t("shape.depth")),U._40O(A,i?Hh(f,A._54o/2,A._62o):0)},getPosition:function(M,Q,Y,f){var A=fb(M,this._83o.rect,f);return A.x+=Q,A.y+=Y,A},_80o:function(v){var j=this,B=j.s,F=j.gv,T=j._data,X=j._83o,I=X.rect,n=I,i=X.position,A=X.rotation,s=X.shape;if(I.width>0&&I.height>0){if(A&&(v.save(),On(v,i.x,i.y),qr(v,A),On(v,-i.x,-i.y),n=T.getSize(),n.x=i.x-n.width/2,n.y=i.y-n.height/2),j.freeDraw)j.freeDraw(v,n,X);else if(s){var k,P,g,J,R=X._60o,U=ad(v,R),d=X._53o,q=X._27Q,Y=X._54o,l=X._55o,x=v.lineJoin,Q=v.lineCap;if("roundRect"===s?k=B("shape.corner.radius"):"polygon"===s?k=B("shape.polygon.side"):"arc"===s&&(k=B("shape.arc.from"),P=B("shape.arc.to"),g=B("shape.arc.close"),J=B("shape.arc.oval")),v.lineJoin=X._62o,v.lineCap=X._61o,d||q?(q?pc(v,q):Yk(v,d,X.shapeGradient,X._59o,n),Zn(v,s,n,k,P,g,J),v.fill(),v!==U&&Zn(U,s,n,k,P,g,J)):Zn(U,s,n,k,P,g,J),Y>0&&(v.lineWidth=Y,v.strokeStyle=l,v.stroke(),X._56o&&Xj(v,l,X._57o,Y,F._zoom,X._58o)),Hd(v,R),B("shape.dash")){var b=B("shape.dash.width")||Y;
if(b>0){R=B("shape.dash.pattern");var U=ad(v,R,j._2Q("shape.dash.offset"));U!==v&&Zn(U,s,n,k,P,g,J),j._1Q(v,"shape",b,R)}}Um(v,d,X._63o,n),v.lineJoin=x,v.lineCap=Q}else Li(v,X.img,X.stretch,n.x,n.y,n.width,n.height,T,F,X.bodyColor);A&&v.restore()}j._39O(v,X)},_48O:function(){var _,u=this,x=u._data,k=u.gv,R=u._55O,$=k.getEditPointSize()/2+2;if(R._42O&&(_=x.getRect(),In(_,$)),R._56O){var y=R._98o=k.getRotationPoint(x);_=Uq(_,{x:y.x-$,y:y.y-$,width:2*$,height:2*$})}u._68o(_)},_47O:function(o){var B=this,w=B.gv,C=B._55O,A=B._data.getRect(),b=w.getEditPointSize(),V=C._98o;C._42O&&(o.fillStyle=w.getEditPointBackground(),o.strokeStyle=w.getEditPointBorderColor(),o.lineWidth=1,[{x:A.x,y:A.y},{x:A.x+A.width/2,y:A.y},{x:A.x+A.width,y:A.y},{x:A.x,y:A.y+A.height/2},{x:A.x+A.width,y:A.y+A.height/2},{x:A.x,y:A.y+A.height},{x:A.x+A.width/2,y:A.y+A.height},{x:A.x+A.width,y:A.y+A.height}].forEach(function(d){$i(o,d.x-b/2,d.y-b/2,b,b),o.stroke()})),C._56O&&(o.fillStyle=w.getEditPointBorderColor(),o.strokeStyle=w.getEditPointBackground(),o.lineWidth=1,o.beginPath(),o.arc(V.x,V.y,b/2,0,z,!0),o.fill(),o.stroke())}});var Ul=function(I,$){Wn(Ul,this,[I,$])},vo=function(h,S,M,z){var N=nm(h,S);return M=z?O(M,N):Ii(M,N),N?M/=N:M=0,{x:h.x+(S.x-h.x)*M,y:h.y+(S.y-h.y)*M}};gf(Ul,cm,{_3O:function(){var Z,l=this,L=l._data,C=l.gv,X=l.s,Y=X(hq),J=L.isLooped(),_=X("edge.width"),v=X("edge.center"),E=X("edge.offset"),o=C.getBorderColor(L),t=o?X("border.width"):0,F=l._2o(),z=L._40I,U=L._41I,n=l._78o=z&&U?{looped:J,type:Y,width:_,center:v,color:l.getBodyColor("edge.color"),borderColor:o,borderWidth:t,_97o:F?X("select.color"):f,selectWidth:F,pattern:X("edge.pattern"),cap:X("edge.cap"),join:X("edge.join"),is3d:X("edge.3d"),_67o:X("edge.3d.color"),_66o:X("edge.3d.accuracy")}:f;if(n){var P=u.getEdgeType(Y);if(P){var w=P(L,nl(l,C,L,J,Y),C,l._19Q);w.points&&w.points.size()>1&&(n._4O=w,Z=ai(n._4O.points))}else{var $=_l(C,z,X("edge.source.position"),X("edge.source.offset.x"),X("edge.source.offset.y")),m=_l(C,U,X("edge.target.position"),X("edge.target.offset.x"),X("edge.target.offset.y"));if(Y===Tq){var c=n.points=X(pg)||$c,a=c.size();if(n.segments=X("edge.segments"),!v)if(E){var e=vo($,a?c.get(0):m,E,a),A=vo(m,a?c.get(a-1):$,E,a);$=e,m=A}else{var s=Og(C,z),V=Og(C,U),r=bk($,a?c.get(0):m,s);r&&($={x:r[0],y:r[1]}),r=bk(a?c.get(a-1):$,m,V),r&&(m={x:r[0],y:r[1]})}Z=Uq(ai(n.points),ai($,m))}else{var T=nl(l,C,L,J,Y);if(l._19Q||(T=-T),J)$=el($),$.x-=z.getWidth()/2,n.radius=T,Z={x:$.x-T,y:$.y-T,width:2*T,height:2*T};else{var M=nm($,m),i=Ii(E,M),e={x:i,y:T},A={x:M-i,y:T},h=q(m.y-$.y,m.x-$.x),k=n.mat=new pf(h);if(n.orienAngle=h,n.angle=m.x<$.x?h+p:h,n.rect={x:e.x,y:e.y,width:A.x-e.x,height:0},n.origin=$,e=k.tf(e),e.x+=$.x,e.y+=$.y,A=k.tf(A),A.x+=$.x,A.y+=$.y,v){var H={x:M,y:0};H=k.tf(H),H.x+=$.x,H.y+=$.y,Z=ai([$,e,A,H]),n.c1=$,n.c2=H}else Z=ai(e,A);$=e,m=A}}n._69o=$,n._70o=m}Hh(Z,_/2+t+F,n.join),l._68o(Z)}},getRotation:function(b,y,u){b=b||0;var G=this._78o;if(G){var v,l=G.angle,s=G.points,H=G._4O;if(l!=f)return y?G.orienAngle+b:l+b;if(H){var E=H.points;return v=E.size(),an[u]?v&&0===v%2?Tn(E.get(v/2-1),E.get(v/2),y,b):b:$e[u]?Tn(E.get(0),E.get(1),y,b):Tn(E.get(v-2),E.get(v-1),y,b)}if(s)return v=s.size(),an[u]?v&&0===v%2?Tn(s.get(v/2-1),s.get(v/2),y,b):b:$e[u]?Tn(G._69o,v?s.get(0):G._70o,y,b):Tn(v?s.get(v-1):G._69o,G._70o,y,b)}return b},getPosition:function(n,R,N,A,$){var K=this._78o;if(K){var w,f=K.type,D=K.points,I=K._4O,U=K._69o,Y=K._70o;if(!f)return K.looped?{x:U.x-K.radius+R,y:U.y+N}:($&&U&&Y&&(U.x>Y.x||U.x===Y.x&&U.y>Y.y)&&(n=qf[n],N=-N),w=fb(n,K.rect,A),w.x+=R,w.y+=N,w=K.mat.tf(w),w.x+=K.origin.x,w.y+=K.origin.y,w);if(D){var w,B=D.size();if(an[n]){if(B){var v=B%2;if(0===v)return Nh(D.get(B/2-1),D.get(B/2),n,R,N,A,$);w=D.get((B-v)/2)}else w={x:(U.x+Y.x)/2,y:(U.y+Y.y)/2};return w=fb(n,{x:w.x,y:w.y,width:0,height:0},A),w.x+=R,w.y+=N,w}return $e[n]?Nh(U,B?D.get(0):Y,n,R,N,A,$):Nh(B?D.get(B-1):U,Y,n,R,N,A,$)}if(I){var u=I.points,B=u.size();if(an[n]){var v=B%2;return 0===v?Nh(u.get(B/2-1),u.get(B/2),n,R,N,A,$):(w=u.get((B-v)/2),w=fb(n,{x:w.x,y:w.y,width:0,height:0},A),w.x+=R,w.y+=N,w)}return $e[n]?Nh(u.get(0),u.get(1),n,R,N,A,$):Nh(u.get(B-2),u.get(B-1),n,R,N,A,$)}}return Df},_42:function(h){this._78o&&Ul.superClass._42.call(this,h)},drawPath:function(P,k){P.beginPath();var R=k.type,S=k.points,h=k.segments,$=k._4O;if(!R||S){var Y=k._69o,s=Y.x,Q=Y.y,q=k._70o,H=q.x,U=q.y;if(R)if(h){var y=new Xc({x:s,y:Q});y.addAll(S),y.add({x:H,y:U}),qh(P,y,h)}else P.moveTo(s,Q),S.each(function(D){P.lineTo(D.x,D.y)}),P.lineTo(H,U);else k.looped?P.arc(s,Q,k.radius,0,z,!0):k.center?(P.moveTo(k.c1.x,k.c1.y),P.lineTo(s,Q),P.lineTo(H,U),P.lineTo(k.c2.x,k.c2.y)):(P.moveTo(s,Q),P.lineTo(H,U))}else $&&qh(P,$.points,$.segments)},_80o:function(P){var U=this,x=U.s,s=U._78o,r=s.width,n=s.selectWidth,d=s.borderWidth,i=s.color,A=P.lineJoin,o=P.lineCap,B=s.pattern;if(P.lineJoin=s.join,P.lineCap=s.cap,U.drawPath(ad(P,B),s),n&&(P.strokeStyle=s._97o,P.lineWidth=r+2*(d+n),P.stroke()),d&&(P.strokeStyle=s.borderColor,P.lineWidth=r+2*d,P.stroke()),P.strokeStyle=i,P.lineWidth=r,P.stroke(),s.is3d&&Xj(P,i,s._67o,r,U.gv._zoom,s._66o),Hd(P,B),x("edge.dash")){B=x("edge.dash.pattern");var Q=ad(P,B,U._2Q("edge.dash.offset"));Q!==P&&U.drawPath(Q,s),U._1Q(P,"edge",x("edge.dash.width")||r,B)}P.lineJoin=A,P.lineCap=o},_48O:function(){var v=this,H=v._78o;if(v._55O._43O&&H&&H.points){var j=ai(H.points);j&&(In(j,v.gv.getEditPointSize()/2+2),v._68o(j))}},_47O:function($){var E=this,b=E.gv,f=E._78o;if(E._55O._43O&&f&&f.points){var a=b.getEditPointSize()/2;$.fillStyle=b.getEditPointBackground(),$.strokeStyle=b.getEditPointBorderColor(),$.lineWidth=1,f.points.each(function(P){$.beginPath(),$.arc(P.x,P.y,a,0,z,!0),$.fill(),$.stroke()})}},_71o:function(G,h){var I=this,C=I._data;if(I._19Q=!0,!C.getEdgeGroup())return G?C.s("edge.gap"):0;var i,z=0,$=0,O=0;if(C.getEdgeGroup().getSiblings().each(function(U){U.each(function(q){if(I.gv.isVisible(q)&&q.s(hq)==h){var W=q.s("edge.gap");i?($+=O/2+W/2,O=W):(i=q,O=W),q===C&&(z=$)}})}),G)return $-z+O;var L=z-$/2;return i&&C._40I!==i._40I&&(I._19Q=!1),L}});var zd=function(F,y){Wn(zd,this,[F,y])};gf(zd,ao,{_3O:function(){var J,X,l=this,M=l.s,w=l._data,v=l.gv;if(l._88I=f,w.isExpanded()&&w.eachChild(function(N){var K=v.getBoundsForGroup(N);K&&(X||(X=[]),X.push(K),J=Uq(J,K))}),J){var t=v.getLabel(w),R=M("group.type");J=sp(R,X,J),Hr(J,w,"group.padding",1);var a=l._88I={type:R,rect:J,_64o:J};if(!R&&t!=f){var m,G=l.labelInfo={label:t,color:M("group.title.color"),font:M("group.title.font")},d=Xl(G,t),B=d.width,N=d.height,x=M("group.title.align");B>J.width&&(J.width=B),G.rect={y:J.y-N,width:d.width,height:N},m=x===S?J.x:x===_?J.x+J.width-B:J.x+J.width/2-B/2,G.rect.x=m,a.titleRect={x:J.x,y:J.y-N,width:J.width,height:N+1},a.rect={x:J.x,y:J.y-N,width:J.width,height:J.height+N}}l._40O(a,M("group.border.width")/2)}else zd.superClass._3O.call(l)},getPosition:function(k,G,q,r){var l=this._88I;if(l){var p=fb(k,l.rect,r);return p.x+=G,p.y+=q,p}return zd.superClass.getPosition.apply(this,arguments)},_24O:function(Q,T){var f=this._88I;(!f||f.type||"label2"===Q)&&zd.superClass._24O.call(this,Q,T)},_80o:function(Y){var B=this,t=B._88I;if(t){var C=B._data,J=B.s,j=B.gv,q=t.type,l=t.rect,F=t._64o,R=t.titleRect,v=B.getBodyColor(),P=cj(J("group.image"),v),$=J("group.image.stretch"),N=B.getBodyColor("group.background"),I=cj(J("group.repeat.image"),v),u=J("group.gradient"),X=J("group.gradient.color"),h=J("group.depth");if(q){var s=J("group.border.pattern"),M=J("group.border.width"),K=Y.lineJoin,w=Y.lineCap;if(Y.lineJoin=J("group.border.join"),Y.lineCap=J("group.border.cap"),P){if(Y.save(),Zn(Y,q,l),Y.clip(),Li(Y,P,$,l.x,l.y,l.width,l.height,C,j,v),Y.restore(),M>0){var i=ad(Y,s);Zn(i,q,l),Y.lineWidth=M,Y.strokeStyle=J("group.border.color"),Y.stroke(),Hd(Y,s)}}else{var i=ad(Y,s);N||I?(I?pc(Y,I):Yk(Y,N,u,X,l),Zn(Y,q,l),Y.fill(),Y!==i&&Zn(i,q,l)):Zn(i,q,l),M>0&&(Y.lineWidth=M,Y.strokeStyle=J("group.border.color"),Y.stroke()),Hd(Y,s),q===ik&&Um(Y,N,h,l)}Y.lineJoin=K,Y.lineCap=w}else if(P?Li(Y,P,$,F.x,F.y,F.width,F.height,C,j,B.getBodyColor()):(N||I)&&(I?pc(Y,I):Yk(Y,N,u,X,F),Zn(Y,ik,F),Y.fill(),Um(Y,N,h,F)),R){var x=J("group.title.background");$i(Y,R.x,R.y,R.width,R.height,x),Um(Y,x,h,R)}B._39O(Y,t)}else zd.superClass._80o.call(B,Y)}});var Ub=function(k,K){Wn(Ub,this,[k,K])};gf(Ub,ao,{_3O:function(){var O=this,l=O._data,d=O.s,r=O.gv,I=l.getPoints(),D=r.getBorderColor(l),_=D?d("border.width"):0,k=O._2o(),E=d("shape.border.width"),Z=l.getRect(),L=r.getBodyColor(l),Y=O._99o=I.isEmpty()?f:{rect:Z,rotation:l.getRotation(),position:l.p(),points:I,segments:l.getSegments(),bodyColor:L,borderColor:D,borderWidth:_,_94o:d("shape.border.3d"),_95o:d("shape.border.3d.color"),_96o:d("shape.border.3d.accuracy"),_97o:k?d("select.color"):f,selectWidth:k,_53o:d("shape.background"),_27Q:cj(d("shape.repeat.image"),L),_54o:E,_55o:d("shape.border.color"),shapeGradient:d("shape.gradient"),_59o:d("shape.gradient.color"),_60o:d("shape.border.pattern"),_61o:d("shape.border.cap"),_62o:d("shape.border.join")};if(Y){var e=E/2+_+k;e&&(Z=el(Z),Hh(Z,e,Y._62o)),O._68o(Z)}},getPosition:function(B,j,k,F){var Y=this._99o;if(Y){var U=fb(B,Y.rect,F);return U.x+=j,U.y+=k,U}return Df},_42:function(n){this._99o&&Ub.superClass._42.call(this,n)},_80o:function(S){var e,$=this,Z=$.s,l=$._99o,O=l.position,d=l.rotation,J=l.bodyColor,V=l.borderWidth,Y=l.selectWidth,m=l._53o,M=l._27Q,k=l._54o,s=l.points,w=l.segments,p=$._data.isClosePath();d&&(S.save(),On(S,O.x,O.y),qr(S,d),On(S,-O.x,-O.y));var i=l._60o,h=ad(S,i),j=S.lineJoin,T=S.lineCap;if(S.lineJoin=l._62o,S.lineCap=l._61o,m||M?(M?pc(S,M):(e=J?J:m,Yk(S,e,l.shapeGradient,l._59o,l.rect)),qh(S,s,w,p),S.fill(),h!==S&&qh(h,s,w,p)):qh(h,s,w,p),Y&&(S.strokeStyle=l._97o,S.lineWidth=k+2*(V+Y),S.stroke()),V&&(S.strokeStyle=l.borderColor,S.lineWidth=k+2*V,S.stroke()),k&&(e=l._55o,!m&&J&&(e=J),S.strokeStyle=e,S.lineWidth=k,S.stroke(),l._94o&&Xj(S,e,l._95o,k,$.gv._zoom,l._96o)),Hd(S,i),Z("shape.dash")){var Q=Z("shape.dash.width")||k;if(Q>0){i=Z("shape.dash.pattern");var h=ad(S,i,$._2Q("shape.dash.offset"));h!==S&&qh(h,s,w,p),$._1Q(S,"shape",Q,i)}}S.lineJoin=j,S.lineCap=T,d&&S.restore()},_48O:function(){var v=this;if(Ub.superClass._48O.call(v),v._55O._43O){var T=v._data.getRect();In(T,v.gv.getEditPointSize()/2+2),v._68o(T)}},_47O:function(S){var a=this;if(Ub.superClass._47O.call(a,S),a._55O._43O){var y=a.gv,K=y.getEditPointSize()/2;S.fillStyle=y.getEditPointBackground(),S.strokeStyle=y.getEditPointBorderColor(),S.lineWidth=1,a._data.getPoints().each(function(p){S.beginPath(),S.arc(p.x,p.y,K,0,z,!0),S.fill(),S.stroke()})}}});var cl=function(N,c){Wn(cl,this,[N,c])};gf(cl,Ub,{getRotation:function(x,l,e){x=x||0;var D=this._data.getPoints(),I=D.size();return I>1?an[e]?I&&0===I%2?Tn(D.get(I/2-1),D.get(I/2),l,x):x:$e[e]?Tn(D.get(0),D.get(1),l,x):Tn(D.get(I-2),D.get(I-1),l,x):x},getPosition:function(o,Z,Y,s,y){var G=this._data.getPoints(),l=G.size();if(l>1){if(an[o]){var V=l%2;if(0===V)return Nh(G.get(l/2-1),G.get(l/2),o,Z,Y,s,y);var h=G.get((l-V)/2),I={x:h.x,y:h.y,width:0,height:0};return h=fb(o,I,s),h.x+=Z,h.y+=Y,h}return $e[o]?Nh(G.get(0),G.get(1),o,Z,Y,s,y):Nh(G.get(l-2),G.get(l-1),o,Z,Y,s,y)}return Df}});var Hf=function(H,z){Wn(Hf,this,[H,z])};gf(Hf,ao,{_3O:function(){var s=this;Hf.superClass._3O.call(s);var U=s.s,J=s._83o;s._82o=J.img||J.shape?f:{background:s.getBodyColor("grid.background"),depth:U("grid.depth"),rect:J.rect,_88o:U("grid.cell.depth"),cellBorderColor:U("grid.cell.border.color"),_89o:U("grid.row.count"),_90o:U("grid.column.count"),block:U("grid.block"),_91o:U("grid.block.color"),_92o:U("grid.block.padding"),_93o:U("grid.block.width")}},_80o:function(j){var i=this,k=i._82o;if(!k)return Hf.superClass._80o.call(i,j),void 0;var S,v,g=i._data,U=k.background,Z=k.rect,r=k.block,x=k._91o,n=k._92o,d=k._93o,s=k._88o,G=k.cellBorderColor,F=k._89o,w=k._90o;if(U)if($i(j,Z.x,Z.y,Z.width,Z.height,U),Um(j,U,k.depth,Z),s)for(S=0;F>S;S++)for(v=0;w>v;v++)Z=g.getCellRect(S,v),Z&&Um(j,U,s,Z);else if(G){for(j.beginPath(),S=0;F>S;S++)for(v=0;w>v;v++)Z=g.getCellRect(S,v),Z&&j.rect(Z.x,Z.y,Z.width,Z.height);j.strokeStyle=G,j.lineWidth=1,j.stroke()}if("h"===r)for(S=0;F>S;S++)Z=Uq(g.getCellRect(S,0),g.getCellRect(S,w-1)),In(Z,n),mb(j,x,Z.x,Z.y,Z.width,Z.height,d);else if("v"===r)for(v=0;w>v;v++)Z=Uq(g.getCellRect(0,v),g.getCellRect(F-1,v)),In(Z,n),mb(j,x,Z.x,Z.y,Z.width,Z.height,d);i._39O(j,i._83o)}});var ee=Lm.Interactor=function(p){this.gv=this._graphView=p};Od("Interactor",M,{ms_listener:1,getView:function(){return this.gv.getView()},setUp:function(){this.addListeners()},tearDown:function(){this.removeListeners(),this.clear()},clear:function(){},fi:function(R){this.gv.fi(R)},setCursor:function(q){w||(this.getView().style.cursor=q)},startDragging:function(F){var g=this;g._lastClientPoint=Ee(F),g._lastLogicalPoint=g.gv.lp(F),Mk(g,F)},clearDragging:function(){var X=this;X._lastClientPoint=X._lastLogicalPoint=X._logicalPoint=f},autoScroll:function(G){return this.gv.autoScroll(G,this._lastClientPoint)}});var Ug=Lm.DefaultInteractor=function(s){Wn(Ug,this,[s])};Od("DefaultInteractor",ee,{handle_mousedown:function(l){np(l);var G=this,s=G.gv,D=s.getDataAt(l);s.setFocus(l)&&!s._editing&&(rs(l)?s.handleDoubleClick(l,D):s.handleClick(l,D),s.isPannable()&&!D&&Fj(l)&&!Sq(l)&&(G._tx=s.tx(),G._ty=s.ty(),G.startDragging(l)))},handleWindowMouseUp:function(w){var o=this,E=o.gv;E._panning&&(delete E._panning,E.onPanEnded(),o.fi({kind:"endPan",event:w})),delete o._tx,delete o._ty,o.clearDragging()},handleWindowMouseMove:function(c){var b=this,f=b.gv,o=b._lastClientPoint;b.fi({kind:f._panning?"betweenPan":"beginPan",event:c}),f._panning=1,f.setTranslate(b._tx+c.clientX-o.x,b._ty+c.clientY-o.y)},handle_mousewheel:function(S){this.gv.handleScroll(S,S.wheelDelta)},handle_DOMMouseScroll:function(A){2===A.axis&&this.gv.handleScroll(A,-A.detail)},handle_keydown:function(h){this.gv.handleKeyDown(h)}});var pb=Lm.SelectInteractor=function(B){Wn(pb,this,[B])};Od("SelectInteractor",ee,{_42:function(){var c=this,K=c.gv,k=K.getZoom(),j=c.mark,z=c.div;z||(z=c.div=Xb(),nj(c.getView(),z));var B={};B.x=j.x*k+K.tx(),B.y=j.y*k+K.ty(),B.width=j.width*k,B.height=j.height*k,tf(z,B),this.intersects()?(z.style.border="",z.style.background=K.getRectSelectBackground()):(z.style.background="",z.style.border="1px solid "+K.getRectSelectBorderColor())},handle_mousedown:function(x){var n=this,E=n.gv;if(n._57I=f,!E._editing){var q=E.getDataAt(x),w=E.sm();q?Sq(x)?w.co(q)?w.rs(q):w.as(q):w.co(q)||w.ss(q):Sq(x)||!E.isPannable()?Fj(x)&&(Sq(x)||w.cs(),E.isRectSelectable()&&(n.startDragging(x),E._77O=1)):n._57I=Ee(x)}},handle_mouseup:function(M){var x=this,J=x._57I;J&&(nm(J,Ee(M))<=1&&x.gv.sm().cs(),x._57I=f)},handleWindowMouseUp:function(O){this.clear(O)},handleWindowMouseMove:function(_){var i=this,W=i.gv;i._logicalPoint=W.lp(_),i.mark?(i.fi({kind:"betweenRectSelect",event:_}),i.autoScroll(_),i.redraw()):i.fi({kind:"beginRectSelect",event:_}),i.mark=ai(i._lastLogicalPoint,i._logicalPoint),i.redraw()},intersects:function(){var Q=this,y=Q._lastLogicalPoint,E=Q._logicalPoint;return y.x>E.x||y.y>E.y},clear:function(E){var z=this,U=z.gv,s=z.mark;if(z._57I=f,z._lastLogicalPoint){if(s){if(0!==s.width&&0!==s.height){var j=U.getDatasInRect(s,z.intersects());if(!j.isEmpty()){var T=U.sm(),c=T.toSelection();j.each(function(i){T.co(i)?c.remove(i):c.add(i)}),T.ss(c)}}vc(z.div),delete z.div,delete z.mark,z.redraw(),z.fi({kind:"endRectSelect",event:E}),U.onRectSelectEnded()}z.clearDragging(),delete U._77O}},redraw:function(){var y=this;y._draw||(y._draw=1,g(function(){y.mark&&y._42(),delete y._draw},16))}});var fe=Lm.MoveInteractor=function(j){Wn(fe,this,[j])};Od("MoveInteractor",ee,{handle_mousedown:function(Q){var z=this,p=z.gv;if(Fj(Q)&&!p._editing){var X=p.getSelectedDataAt(Q);X?(z._data=X,p.handleMouseDown&&p.handleMouseDown(Q,X),z.startDragging(Q),p.isMovable(X)&&(p._moving=1)):p._focusData=f}},handleWindowMouseUp:function(x){var o=this,V=o.gv;V.handleMouseUp&&V.handleMouseUp(x,o._data),o.clear(x)},handleWindowMouseMove:function(J){var Y=this,d=Y.gv,w=Y._data,l=d.getDataModel(),z=l.getHistoryManager();if((!d._93O||!d._93O(J,w))&&d._moving){var q=Y._logicalPoint?"betweenMove":"beginMove",B={kind:q,event:J},R=Y._logicalPoint=d.lp(J);z&&"beginMove"===q&&z.beginInteraction();var m=R.x-Y._lastLogicalPoint.x,P=R.y-Y._lastLogicalPoint.y,j=d.getMoveMode(J,w);j&&("x"===j?P=0:"y"===j?m=0:"xy"!==j&&(m=P=0)),d.moveSelection(m,P),Y._lastLogicalPoint=R,Y.autoScroll(J),Y.fi(B)}},clear:function(c){var z=this,M=z.gv,P=M.getDataModel(),C=P.getHistoryManager();z._lastLogicalPoint&&(z._lastLogicalPoint=z._data=M._moving=f,z._logicalPoint&&(z.fi({kind:"endMove",event:c}),M.onMoveEnded(),C&&C.endInteraction()),z.clearDragging())}});var $f=Lm.EditInteractor=function(u){Wn($f,this,[u])};Od("EditInteractor",ee,{ms_edit:1,setUp:function(){var M=this;$f.superClass.setUp.call(M),w||M.gv.setEditInteractor(M)},tearDown:function(){$f.superClass.tearDown.call(this),w||this.gv.setEditInteractor(f)},clear:function(){var $=this,M=$.gv;M._editing&&(M._editing=$._77I=$._node=$._edge=$._shape=$._rect=$._89I=$._index=f,$.clearDragging(),$.setCursor(r))},handle_mousedown:function(o){var u=this,U=u.gv.dm(),M=U.getHistoryManager(),p=u._index,s=u._node,S=u._shape,k=u._edge,E=u._77I,w=u._89I;Fj(o)&&u.gv._editing&&(s&&w?(u._rect=s.getRect(),u.startDragging(o),M&&M.beginInteraction(),u.fi({kind:"beginEditRect",event:o,data:s,direction:w})):S&&p>=0?(u.startDragging(o),M&&M.beginInteraction(),u.fi({kind:"beginEditPoint",event:o,data:S,index:p})):k&&p>=0?(u.startDragging(o),M&&M.beginInteraction(),u.fi({kind:"beginEditPoint",event:o,data:k,index:p})):E&&(u.startDragging(o),M&&M.beginInteraction(),u.fi({kind:"beginEditRotation",event:o,data:E})))},handleWindowMouseUp:function(w){this._46O(w),this.clear()},handleWindowMouseMove:function(m){this._78I(m)},handle_mousemove:function(e){if(!Bl){var l=this,p=l.gv;l._79I(e,p.getSelectedDataAt(e))?p._editing=1:l.clear()}}});var Wk=Lm.TouchInteractor=function(k,C){C=C||{},C.selectable===j&&(C.selectable=!0),C.movable===j&&(C.movable=!0),C.pannable===j&&(C.pannable=!0),C.pinchable===j&&(C.pinchable=!0),C.editable===j&&(C.editable=!0),this.params=C,Wn(Wk,this,[k])};Od("TouchInteractor",ee,{ms_edit:1,setUp:function(){var h=this;Wk.superClass.setUp.call(h),w&&h.params.editable&&h.gv.setEditInteractor(h)},tearDown:function(){var z=this;Wk.superClass.tearDown.call(z),w&&z.params.editable&&z.gv.setEditInteractor(f)},clear:function(a){var d=this,B=d.gv,x=B.dm().getHistoryManager();B._moving&&(d.fi({kind:"endMove",event:a}),delete B._moving,B.onMoveEnded(),x&&x.endInteraction()),B._panning&&(d.fi({kind:"endPan",event:a}),delete B._panning,B.onPanEnded()),B._pinching&&(d.fi({kind:"endPinch",event:a}),delete B._pinching,B.onPinchEnded()),B._editing&&(d._46O(a),d._77I=d._node=d._edge=d._shape=d._rect=d._89I=d._index=B._editing=f),d._moving=d._panning=d._pinching=d._editing=d._57I=d._data=d._beginHistory=f,d.clearDragging()},handle_touchstart:function(h){var j=this;if(!j.gv._editing){np(h),j._57I=f;var E=j.params,K=j.gv,J=K.sm(),c=K.getDataAt(h),t=nq(h);if(1===t)rs(h)?K.handleDoubleClick(h,c):(K.handleClick(h,c),c&&(K.handleMouseDown&&K.handleMouseDown(h,c),j._data=c)),E.selectable||(c=f),c?(J.co(c)||J.ss(c),E.editable&&K.isEditable(c)&&j._79I(h,c,!0)?(j._editing=1,j.startDragging(h)):E.movable&&K.isMovable(c)&&(j._moving=1,j.startDragging(h))):(j._57I=Ee(h),E.pannable&&K.isPannable()&&(j._panning=1,j.startDragging(h),j._translate={x:K.tx(),y:K.ty()}));else if(E.pinchable&&2===t){j._pinching=1,j.startDragging(h);var T=K.getView(),a=K.getZoom(),q=T.getBoundingClientRect(),L=h.touches[0],M=h.touches[1],H={x:(L.clientX+M.clientX)/2-q.left,y:(L.clientY+M.clientY)/2-q.top};H.x-=K.tx(),H.y-=K.ty(),H.x/=a,H.y/=a,j._p=H,j._d=Ao(h)}}},handle_touchend:function(M){var x=this,w=x.gv,W=x._57I,S=x._data;W&&(nm(W,Ee(M))<=1&&w.sm().cs(),x._57I=f),S&&w.handleMouseUp&&w.handleMouseUp(M,S)},handleWindowTouchEnd:function(T){this.clear(T)},handleWindowTouchMove:function(c){var F=this,G=F.gv,P=G.dm().getHistoryManager(),v=nq(c);if(1===v){if(F._editing&&(P&&!F._beginHistory&&(F._beginHistory=1,P.beginInteraction()),G._editing=1,F._78I(c)),F._moving)P&&!F._beginHistory&&(F._beginHistory=1,P.beginInteraction()),F.handleMove(c);else if(F._panning){var Z=Ee(c);G.setTranslate(F._translate.x+Z.x-F._lastClientPoint.x,F._translate.y+Z.y-F._lastClientPoint.y),F.fi({kind:G._panning?"betweenPan":"beginPan",event:c}),G._panning=1}}else if(2===v&&F._pinching){var l=Ao(c);G.handlePinch(F._p,l,F._d),F._d=l,F.fi({kind:G._pinching?"betweenPinch":"beginPinch",event:c}),G._pinching=1}},handleMove:function(n){var f=this,T=f.gv,i=T.lp(n);T._93O&&T._93O(n,f._data)||(T.moveSelection(i.x-f._lastLogicalPoint.x,i.y-f._lastLogicalPoint.y),f._lastLogicalPoint=i,f.autoScroll(n),f.fi({kind:T._moving?"betweenMove":"beginMove",event:n}),T._moving=1)}});var Kj="directional",Vc="point",$k="spot",Td="light.color";Ym(u,{graph3dViewAttributes:f,graph3dViewFirstPersonMode:!1,graph3dViewMouseRoamable:!0,graph3dViewMoveStep:15,graph3dViewRotateStep:p/60,graph3dViewPannable:!0,graph3dViewRotatable:!0,graph3dViewWalkable:!0,graph3dViewResettable:!0,graph3dViewZoomable:!0,graph3dViewRectSelectable:!0,graph3dViewRectSelectBackground:sf,graph3dViewGridVisible:!1,graph3dViewGridSize:50,graph3dViewGridGap:50,graph3dViewGridColor:[.4,.75,.85,1],graph3dViewOriginAxisVisible:!1,graph3dViewCenterAxisVisible:!1,graph3dViewAxisXColor:[1,0,0,1],graph3dViewAxisYColor:[0,1,0,1],graph3dViewAxisZColor:[0,0,1,1],graph3dViewEditSizeColor:[1,1,0,1],graph3dViewOrtho:!1,graph3dViewOrthoWidth:2e3,graph3dViewFovy:p/4,graph3dViewNear:10,graph3dViewFar:1e4,graph3dViewEye:[0,300,1e3],graph3dViewCenter:[0,0,0],graph3dViewUp:[0,1,-1e-7],graph3dViewHeadlightRange:0,graph3dViewHeadlightColor:[1,1,1,1],graph3dViewHeadlightIntensity:1,graph3dViewHeadlightDisabled:!1,graph3dViewFogDisabled:!0,graph3dViewFogColor:"white",graph3dViewFogNear:1,graph3dViewFogFar:2e3,graph3dViewDashDisabled:!0,graph3dViewBatchBlendDisabled:!0,graph3dViewBatchBrightnessDisabled:!0,graph3dViewBatchColorDisabled:!1,setShape3dModel:function($,m){mp[$]=m},getShape3dModel:function(P){return mp[P]},createMatrix:function(g,_){Ji(g)||(g=[g]);for(var Q=g.length-1;Q>=0;Q--){var e=g[Q];_=lq(e.mat,e.s3,e.r3,e.rotationMode,e.t3,_)}return _},transformVec:function(S,U){return zj(S,U)},createBoxModel:function(){return{vs:ud,ns:cr,uv:jr,is:rp}},createRoundRectModel:function(P,g){return Gg.roundRect(P,g)},createStarModel:function(U,o){return Gg.star(U,o)},createRectModel:function(j,b){return Gg.rect(j,b)},createTriangleModel:function(k,L){return Gg.triangle(k,L)},createRightTriangleModel:function(M,T){return Gg.rightTriangle(M,T)},createParallelogramModel:function(h,R){return Gg.parallelogram(h,R)},createTrapezoidModel:function(d,v){return Gg.trapezoid(d,v)},createSmoothSphereModel:function(g,R,b,q,W,I,Y){return wj(new tn(g,R,b,q,W,I,Y))},createSphereModel:function(L,n,A,S,N,H){return L?ys(L,n,A,S,N,H):u.createSmoothSphereModel()},createSmoothConeModel:function(H,T,N,O,I){return qm(H,T,N,O,I)},createConeModel:function(K,R,$,I,v,u){return K?Fg(K,R,$,I,v,u):qm(u)},createSmoothCylinderModel:function(v,M,W,X,V,R,L,U){return wj(new Pm(v,M,W,X,V,R,L,U))},createCylinderModel:function(R,T,m,b,Y,h,j){return R?km(R,T,m,b,Y,h,j):js(h,j)},createSmoothTorusModel:function(O,z,m,I,k,V){return wj(new Qf(O,z,m,I,k,V))},createTorusModel:function(I,j,L,f,k,r,S){return I?Ef(I,j,L,f,k,r,S):Je(r,S)},createExtrusionModel:function(L,f,S,x,v,k,M,U){return ah(L,f,S,x,v,k,M,U)},createSmoothRingModel:function($,I,c,R,h,M){for(var g=[],H=0;H<$.length-1;H+=2)g.push({x:$[H],y:$[H+1]});for($=Rr(g,I,c)[0],g=[],H=0;H<$.length;H++){var w=$[H];g.push(new Xm(w.x,0,w.y))}return wj(new _p(g,M,R,h))},createRingModel:function(h,q,P,$,T,i,m,A,b,o){for(var W=[],X=0;X<h.length-1;X+=2)W.push({x:h[X],y:h[X+1]});i=i||u.shapeSide,m=m||0,A=A||i;var D,g,y,R,p,I,U,C,a,Y,X,O,S,F,Z,k,s,H,E=[],v=[],d=b?[]:f,J=b?[]:f,l=o?[]:f,B=o?[]:f,t=$?[]:f,K=$?[]:f,M=T?[]:f,L=T?[]:f,h=Rr(W,q,P),G=vf(h),w=0,j=2*Math.PI/i;return h.forEach(function(h){if(y=h.length,y>1){if(R=h[0],$)for(I=R.x,C=R.y,X=m;A>X;X++)O=X+1,S=X*j,F=O*j,Z=N(S),k=n(S),s=N(F),H=n(F),t.push(Z*I,C,-k*I,s*I,C,-H*I,0,C,0),K.push(.5-.5*Z,.5-.5*k,.5-.5*s,.5-.5*H,.5,.5);for(Y=0;y>Y;Y++){for(p=h[Y],I=R.x,U=p.x,C=R.y,a=p.y,D=w/G,w+=nm(R,p),g=w/G,X=m;A>X;X++)O=X+1,S=X*j,F=O*j,Z=N(S),k=n(S),s=N(F),H=n(F),E.push(Z*U,a,-k*U,s*U,a,-H*U,Z*I,C,-k*I,s*U,a,-H*U,s*I,C,-H*I,Z*I,C,-k*I),v.push(X/i,g,O/i,g,X/i,D,O/i,g,O/i,D,X/i,D),b&&X===m&&(d.push(0,C,0,0,a,0,Z*U,a,-k*U,Z*U,a,-k*U,Z*I,C,-k*I,0,C,0),J.push(0,.5-C,0,.5-a,2*U,.5-a,2*U,.5-a,2*I,.5-C,0,.5-C)),o&&O===A&&(l.push(0,C,0,s*U,a,-H*U,0,a,0,s*U,a,-H*U,0,C,0,s*I,C,-H*I),B.push(1,.5-C,1-2*U,.5-a,1,.5-a,1-2*U,.5-a,1,.5-C,1-2*I,.5-C));R=p}if(T)for(I=R.x,C=R.y,X=m;A>X;X++)O=X+1,S=X*j,F=O*j,Z=N(S),k=n(S),s=N(F),H=n(F),M.push(s*I,C,-H*I,Z*I,C,-k*I,0,C,0),L.push(.5-.5*s,.5+.5*H,.5-.5*Z,.5+.5*k,.5,.5)}}),{vs:E,uv:v,bottom_vs:M,bottom_uv:L,top_vs:t,top_uv:K,from_vs:d,from_uv:J,to_vs:l,to_uv:B}}},!0),Ym(R,{"3d.move.mode":j,"3d.selectable":!0,"3d.visible":!0,"3d.movable":!0,"3d.editable":!0,"shape.border.gradient.color":j,"edge.gradient.color":j,"edge.source.t3":j,"edge.target.t3":j,"light.type":Vc,"light.center":[0,0,0],"light.color":[1,1,1,1],"light.disabled":!1,"light.angle":p/4,"light.range":0,"light.exponent":1,"light.intensity":1,"wf.visible":!1,"wf.width":1,"wf.color":wq,"wf.short":!1,"wf.mat":j,batch:j,"transparent.mask":!1,brightness:j,"select.brightness":.7,"repeat.uv.length":j,"label.face":Q,"label.t3":j,"label.r3":j,"label.texture.scale":2,"label.rotationMode":Tk,"label.light":!1,"label.blend":j,"label.reverse.flip":!1,"label.reverse.color":Ah,"label.reverse.cull":!1,"label.transparent":!1,"label.autorotate":!1,"label2.face":Q,"label2.t3":j,"label2.r3":j,"label2.texture.scale":2,"label2.rotationMode":Tk,"label2.light":!1,"label2.blend":j,"label2.reverse.flip":!1,"label2.reverse.color":Ah,"label2.reverse.cull":!1,"label2.transparent":!1,"label2.autorotate":!1,"note.face":Q,"note.t3":j,"note.r3":j,"note.texture.scale":2,"note.rotationMode":Tk,"note.light":!1,"note.blend":j,"note.reverse.flip":!1,"note.reverse.color":Ah,"note.reverse.cull":!1,"note.transparent":!1,"note.autorotate":!1,"note2.face":Q,"note2.t3":j,"note2.r3":j,"note2.texture.scale":2,"note2.rotationMode":Tk,"note2.light":!1,"note2.blend":j,"note2.reverse.flip":!1,"note2.reverse.color":Ah,"note2.reverse.cull":!1,"note2.transparent":!1,"note2.autorotate":!1,shape3d:j,"shape3d.color":_e,"shape3d.top.color":j,"shape3d.bottom.color":j,"shape3d.from.color":j,"shape3d.to.color":j,"shape3d.image":j,"shape3d.top.image":j,"shape3d.bottom.image":j,"shape3d.from.image":j,"shape3d.to.image":j,"shape3d.light":!0,"shape3d.side":0,"shape3d.side.from":j,"shape3d.side.to":j,"shape3d.visible":!0,"shape3d.from.visible":!0,"shape3d.to.visible":!0,"shape3d.top.visible":!0,"shape3d.bottom.visible":!0,"shape3d.torus.radius":.17,"shape3d.resolution":0,"shape3d.blend":j,"shape3d.opacity":j,"shape3d.reverse.flip":!1,"shape3d.reverse.color":Ah,"shape3d.reverse.cull":!1,"shape3d.transparent":!1,"shape3d.uv.offset":j,"shape3d.uv.scale":j,"shape3d.top.uv.offset":j,"shape3d.top.uv.scale":j,"shape3d.bottom.uv.offset":j,"shape3d.bottom.uv.scale":j,"shape3d.from.uv.offset":j,"shape3d.from.uv.scale":j,"shape3d.to.uv.offset":j,"shape3d.to.uv.scale":j,"shape3d.top.cap":j,"shape3d.bottom.cap":j,"shape3d.start.angle":0,"shape3d.discard.selectable":!0,"shape3d.top.discard.selectable":!0,"shape3d.bottom.discard.selectable":!0,"shape3d.from.discard.selectable":!0,"shape3d.to.discard.selectable":!0,"shape3d.scaleable":!0,"all.light":!0,"all.visible":!0,"all.color":_e,"all.image":j,"all.blend":j,"all.opacity":j,"all.reverse.flip":!1,"all.reverse.color":Ah,"all.reverse.cull":!1,"all.transparent":!1,"all.uv":j,"all.uv.offset":j,"all.uv.scale":j,"all.discard.selectable":!0,mat:j,"left.mat":j,"right.mat":j,"top.mat":j,"bottom.mat":j,"front.mat":j,"back.mat":j},!0);var Jr,yk=!1,Oc=[0,0,0,1/255],Ne={left:[1/255,0,0,1],right:[2/255,0,0,1],top:[3/255,0,0,1],bottom:[4/255,0,0,1],front:[5/255,0,0,1],back:[6/255,0,0,1],m:{1:S,2:_,3:xi,4:Kh,5:Q,6:o}},Ye=function(G,v,f,a){if(v.getFaceVisible(G,f)){Be(v,Sj(G,v.getFaceMat(G,f)||v.getMat(G)));var s=v._26I;v.getFaceReverseCull(G,f)?s.enable(s.CULL_FACE):s.disable(s.CULL_FACE),ap(s,v._prg.uFixPickReverseColor,Ne[f]),ke(s,a,6),lc(v)}},Zg=function(q){return q instanceof As},Al=function(Q){return[Q.x,Q.e||0,Q.y]},pe=function(f,j,W){for(var j=Yd(j),k=j[0],x=j[1],Q=j[2];W--;)f.push(k,x,Q)},Se=function(w,m,j){for(var m=Yd(m),D=m[0],C=m[1],R=m[2],T=m[3];j--;)w.push(D,C,R,T)},dk=function(O,x,n){if(x)for(var j,b,W,I=x[0],K=x[1],f=x[2],S=x[4],P=x[5],c=x[6],R=x[8],Y=x[9],z=x[10],Q=x[12],p=x[13],h=x[14],s=n.length,w=0,w=0;s>w;w+=3)j=n[w],b=n[w+1],W=n[w+2],O.push(I*j+S*b+R*W+Q,K*j+P*b+Y*W+p,f*j+c*b+z*W+h);else ul(O,n)},dq=function(W,s){var c=s.s("light.intensity"),n=Yd(s.s(Td)),b=n[0],z=n[1],r=n[2];1!==c&&(b*=c,z*=c,r*=c),W.push(b,z,r,s.s("light.disabled")?1:0)},Cj=function(f,T,v){ap(T,v.uHeadlightRange,f._headlightRange);var p=f._headlightIntensity,s=Yd(f._headlightColor);1!==p&&(s=[s[0]*p,s[1]*p,s[2]*p]),ap(T,v.uHeadlightColor,[s[0],s[1],s[2],f._headlightDisabled?1:0]);var $=f._59O,b=f._spots,I=f._dirs;if($.length){var H=[],A=[],o=[];$.forEach(function(R){dq(H,R),ul(A,R.p3()),o.push(R.s("light.range"))}),T.uniform4fv(v.uPointColor,H),T.uniform1fv(v.uPointRange,o),T.uniform3fv(v.uPointPosition,A)}if(b.length){var X=[],l=[],z=[],m=[],S=[],Z=[];b.forEach(function(K){dq(X,K),ul(l,K.p3()),ul(z,K.s("light.center")),m.push(K.s("light.range")),S.push(N(K.s("light.angle")/2)),Z.push(K.s("light.exponent"))}),T.uniform4fv(v.uSpotColor,X),T.uniform1fv(v.uSpotRange,m),T.uniform1fv(v.uSpotAngle,S),T.uniform1fv(v.uSpotExponent,Z),T.uniform3fv(v.uSpotPosition,l),T.uniform3fv(v.uSpotCenter,z)}if(I.length){var D=[],q=[];I.forEach(function(Q){dq(D,Q),ul(q,Q.p3())}),T.uniform4fv(v.uDirColor,D),T.uniform3fv(v.uDirPosition,q)}},Ad=function(V){return V>0&&0===(V-1&V)},lb=function(v){return v=b(0,O(p,v)),v=b(ne,O(p-ne,v))},Wq=function(){return.05+m()/2},xq=function(F,v,n,V){var W,O=F.getEye();return F.isOrtho()?(W=Fl(F.getCenter(),O),W[0]+=V[0],W[1]+=V[1],W[2]+=V[2]):W=O,vg(v,n,V,W)},vg=function(d,S,$,k){var Y=kl(d,S),i=Fl(k,$,!0),V=kl(i,S);if(J(V)<ne)return f;var o=(Y-kl($,S))/V;return[$[0]+i[0]*o,$[1]+i[1]*o,$[2]+i[2]*o]},Sr=function(m,J){return{x:2*m.x-J.x,y:2*m.y-J.y}},Nm=function(y,v,j,A){var Y,x;if(!y)return Y=q(v.y-j.y,j.x-v.x),{x:v.x+A*n(Y),y:v.y+A*N(Y)};if(!j)return Y=q(y.y-v.y,v.x-y.x),{x:v.x+A*n(Y),y:v.y+A*N(Y)};var i=Fl([y.x,y.y,0],[v.x,v.y,0],!0),t=Fl([j.x,j.y,0],[v.x,v.y,0],!0),C=-(i[0]+t[0])/2,d=-(i[1]+t[1])/2,Z=F(C*C+d*d);return ne>Z?(Y=q(y.y-v.y,v.x-y.x),{x:v.x+A*n(Y),y:v.y+A*N(Y)}):(Y=H(kl(i,t))/2,x=i[1]*t[0]-i[0]*t[1]>0?-1:1,C/=Z,d/=Z,Z=A/n(Y),{x:v.x+x*Z*C,y:v.y+x*Z*d})},de=function(C){var u=C[1],m=C[2],d=C[3],K=C[6],D=C[7],t=C[11];C[1]=C[4],C[2]=C[8],C[3]=C[12],C[4]=u,C[6]=C[9],C[7]=C[13],C[8]=m,C[9]=K,C[11]=C[14],C[12]=d,C[13]=D,C[14]=t},ip=function(o){var F=o[0],i=o[1],g=o[2],C=o[3],H=o[4],M=o[5],B=o[6],A=o[7],q=o[8],I=o[9],Q=o[10],P=o[11],b=o[12],J=o[13],y=o[14],w=o[15],s=F*M-i*H,U=F*B-g*H,x=F*A-C*H,G=i*B-g*M,u=i*A-C*M,W=g*A-C*B,E=q*J-I*b,k=q*y-Q*b,L=q*w-P*b,m=I*y-Q*J,T=I*w-P*J,$=Q*w-P*y,z=s*$-U*T+x*m+G*L-u*k+W*E;return z?(z=1/z,o[0]=(M*$-B*T+A*m)*z,o[1]=(g*T-i*$-C*m)*z,o[2]=(J*W-y*u+w*G)*z,o[3]=(Q*u-I*W-P*G)*z,o[4]=(B*L-H*$-A*k)*z,o[5]=(F*$-g*L+C*k)*z,o[6]=(y*x-b*W-w*U)*z,o[7]=(q*W-Q*x+P*U)*z,o[8]=(H*T-M*L+A*E)*z,o[9]=(i*L-F*T-C*E)*z,o[10]=(b*u-J*x+w*s)*z,o[11]=(I*x-q*u-P*s)*z,o[12]=(M*k-H*m-B*E)*z,o[13]=(F*m-i*k+g*E)*z,o[14]=(J*U-b*G-y*s)*z,o[15]=(q*G-I*U+Q*s)*z,void 0):f},zg=function(l,Q){if(Q){var a=Q[0],E=Q[1],S=Q[2];l[12]=l[0]*a+l[4]*E+l[8]*S+l[12],l[13]=l[1]*a+l[5]*E+l[9]*S+l[13],l[14]=l[2]*a+l[6]*E+l[10]*S+l[14],l[15]=l[3]*a+l[7]*E+l[11]*S+l[15]}},_k=function(U,Y){if(Y){var D=Y[0],v=Y[1],L=Y[2];U[0]=U[0]*D,U[1]=U[1]*D,U[2]=U[2]*D,U[3]=U[3]*D,U[4]=U[4]*v,U[5]=U[5]*v,U[6]=U[6]*v,U[7]=U[7]*v,U[8]=U[8]*L,U[9]=U[9]*L,U[10]=U[10]*L,U[11]=U[11]*L
}},gh=function(P,r,Z){var L=r[0],J=r[1],c=r[2],t=r[3],n=r[4],d=r[5],B=r[6],i=r[7],O=r[8],Q=r[9],C=r[10],M=r[11],k=r[12],$=r[13],m=r[14],T=r[15],j=Z[0],o=Z[1],U=Z[2],v=Z[3];return P[0]=j*L+o*n+U*O+v*k,P[1]=j*J+o*d+U*Q+v*$,P[2]=j*c+o*B+U*C+v*m,P[3]=j*t+o*i+U*M+v*T,j=Z[4],o=Z[5],U=Z[6],v=Z[7],P[4]=j*L+o*n+U*O+v*k,P[5]=j*J+o*d+U*Q+v*$,P[6]=j*c+o*B+U*C+v*m,P[7]=j*t+o*i+U*M+v*T,j=Z[8],o=Z[9],U=Z[10],v=Z[11],P[8]=j*L+o*n+U*O+v*k,P[9]=j*J+o*d+U*Q+v*$,P[10]=j*c+o*B+U*C+v*m,P[11]=j*t+o*i+U*M+v*T,j=Z[12],o=Z[13],U=Z[14],v=Z[15],P[12]=j*L+o*n+U*O+v*k,P[13]=j*J+o*d+U*Q+v*$,P[14]=j*c+o*B+U*C+v*m,P[15]=j*t+o*i+U*M+v*T,P},Fn=function(b,j,u,V){var m,C,v,B,k,i,E,x,D,e,t=j[0],P=j[1],l=j[2],H=V[0],M=V[1],K=V[2],q=u[0],f=u[1],y=u[2];return J(t-q)<ne&&J(P-f)<ne&&J(l-y)<ne?Ge(b):(E=t-q,x=P-f,D=l-y,e=1/F(E*E+x*x+D*D),E*=e,x*=e,D*=e,m=M*D-K*x,C=K*E-H*D,v=H*x-M*E,e=F(m*m+C*C+v*v),e?(e=1/e,m*=e,C*=e,v*=e):(m=0,C=0,v=0),B=x*v-D*C,k=D*m-E*v,i=E*C-x*m,e=F(B*B+k*k+i*i),e?(e=1/e,B*=e,k*=e,i*=e):(B=0,k=0,i=0),b[0]=m,b[1]=B,b[2]=E,b[3]=0,b[4]=C,b[5]=k,b[6]=x,b[7]=0,b[8]=v,b[9]=i,b[10]=D,b[11]=0,b[12]=-(m*t+C*P+v*l),b[13]=-(B*t+k*P+i*l),b[14]=-(E*t+x*P+D*l),b[15]=1,b)},bj=function(y,k,h,Y,C){var B=1/i(k/2),l=1/(Y-C);return y[0]=B/h,y[1]=0,y[2]=0,y[3]=0,y[4]=0,y[5]=B,y[6]=0,y[7]=0,y[8]=0,y[9]=0,y[10]=(C+Y)*l,y[11]=-1,y[12]=0,y[13]=0,y[14]=2*C*Y*l,y[15]=0,y},Gl=function(d,Y,h,C,P,z,b){var O=1/(Y-h),k=1/(C-P),j=1/(z-b);return d[0]=-2*O,d[1]=0,d[2]=0,d[3]=0,d[4]=0,d[5]=-2*k,d[6]=0,d[7]=0,d[8]=0,d[9]=0,d[10]=2*j,d[11]=0,d[12]=(Y+h)*O,d[13]=(P+C)*k,d[14]=(b+z)*j,d[15]=1,d},bi=function(H,c){return Fn(c?c:Vp(),H._eye,H._center,H._up)},Jb=function(e){var H=e.getAspect(),D=e._near,V=e._far,n=Vp();if(e._ortho){var i=e._orthoWidth/2,z=i/H;Gl(n,-i,i,-z,z,D,V)}else bj(n,e._fovy,H,D,V);return n},ib=function(x,c){if(!x)return f;var a=0,K=1,q=2,m=[],R=0,w=x.length,p=w/3;if(c){for(;w>R;R++)m[R]=0;for(R=0;R<c.length;R+=3){var $=[],W=[],k=[];$[a]=x[3*c[R+1]+a]-x[3*c[R]+a],$[K]=x[3*c[R+1]+K]-x[3*c[R]+K],$[q]=x[3*c[R+1]+q]-x[3*c[R]+q],W[a]=x[3*c[R+2]+a]-x[3*c[R+1]+a],W[K]=x[3*c[R+2]+K]-x[3*c[R+1]+K],W[q]=x[3*c[R+2]+q]-x[3*c[R+1]+q],k[a]=$[K]*W[q]-$[q]*W[K],k[K]=$[q]*W[a]-$[a]*W[q],k[q]=$[a]*W[K]-$[K]*W[a];for(var y=0;3>y;y++)m[3*c[R+y]+a]+=k[a],m[3*c[R+y]+K]+=k[K],m[3*c[R+y]+q]+=k[q]}}else for(R=0;p>R;R+=3){var $=[],W=[],k=[];$[a]=x[3*(R+1)+a]-x[3*R+a],$[K]=x[3*(R+1)+K]-x[3*R+K],$[q]=x[3*(R+1)+q]-x[3*R+q],W[a]=x[3*(R+2)+a]-x[3*(R+1)+a],W[K]=x[3*(R+2)+K]-x[3*(R+1)+K],W[q]=x[3*(R+2)+q]-x[3*(R+1)+q],k[a]=$[K]*W[q]-$[q]*W[K],k[K]=$[q]*W[a]-$[a]*W[q],k[q]=$[a]*W[K]-$[K]*W[a];for(var y=0;3>y;y++)m[3*(R+y)+a]=k[a],m[3*(R+y)+K]=k[K],m[3*(R+y)+q]=k[q]}for(R=0;w>R;R+=3){var B=[];B[a]=m[R+a],B[K]=m[R+K],B[q]=m[R+q];var C=F(B[a]*B[a]+B[K]*B[K]+B[q]*B[q]);0===C&&(C=ne),B[a]=B[a]/C,B[K]=B[K]/C,B[q]=B[q]/C,m[R+a]=B[a],m[R+K]=B[K],m[R+q]=B[q]}return new Jp(m)},Sh=function(e,o,t){if(t||(t=e.createTexture()),o){var D=e.TEXTURE_2D,R=e.LINEAR,n=e.REPEAT,Z=e.CLAMP_TO_EDGE,r=e.TEXTURE_MIN_FILTER;Eb(e,t),e.texImage2D(D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,o),Cc(e,e.TEXTURE_MAG_FILTER,R),Ad(o.width)&&Ad(o.height)?(Cc(e,e.TEXTURE_WRAP_S,n),Cc(e,e.TEXTURE_WRAP_T,n),Cc(e,r,e.LINEAR_MIPMAP_LINEAR),e.generateMipmap(D)):(Cc(e,e.TEXTURE_WRAP_S,Z),Cc(e,e.TEXTURE_WRAP_T,Z),Cc(e,r,R)),Eb(e,f)}return t},Nn=function(H){H._26I&&H._prg&&(H._26I.deleteProgram(H._prg),H._prg=f)},ko=function(X,Z,u,K){var O=X.createShader(u);return X.shaderSource(O,K),X.compileShader(O),X.attachShader(Z,O),O},Hp=function(d){return d.createBuffer()},Eb=function(I,$){I.bindTexture(I.TEXTURE_2D,$)},Qi=function(g,W){g.bindFramebuffer(g.FRAMEBUFFER,W)},Cc=function(U,g,I){U.texParameteri(U.TEXTURE_2D,g,I)},Iq=function(l,q){if(q){var q=Yd(q);l.clearColor(q[0],q[1],q[2],q[3])}},mh=function(c,H){ap(c,H.uFix,!0),Ob(c,H.aNormal)},Ap=function(V,X){ap(V,X.uFix,!1),Ld(V,X.aNormal)},Tc=function(e,d,k,R,o,P,F,H){e._picking||(k?(ap(e,d.uBlend,!0),ap(e,d.uBlendColor,k)):ap(e,d.uBlend,!1),ap(e,d.uLight,R==f?!0:R),sh(o)&&1!==o&&ap(e,d.uPartOpacity,o),ap(e,d.uReverseFlip,P==f?!1:P),ap(e,d.uFixPickReverseColor,F||Ah)),H?e.enable(e.CULL_FACE):e.disable(e.CULL_FACE)},Re=function(S,i){S._picking||ap(S,i.uPartOpacity,1)},lm=function(U,E,P,q,$,w,B,s){if(P){ap(U,E.uTexture,!0),ap(U,E.uDiscardSelectable,wc(q)?q:!0),Ld(U,E.aUv),Ih(U,$,w,E.aUv,2),U.activeTexture(U.TEXTURE0),Eb(U,P),U.uniform1i(E.uSampler,0);var p=[0,0,1,1];B&&(p[0]=B[0],p[1]=B[1]),s&&(p[2]=s[0],p[3]=s[1]),ap(U,E.uOffsetScale,p)}},os=function(q,H,C){C&&(Eb(q,f),Ob(q,H.aUv),ap(q,H.uTexture,!1))},Nr=function(d,l,t,G,Z,m){t&&!h&&d.lineWidth(t),G&&!d._picking&&ap(d,l.uFixPickReverseColor,G),Z&&Ih(d,Z,m,l.aPosition)},$j=function(C,v,k,Y){Jr&&(Y=C[Jr]),C.drawArrays(Y==f?C.TRIANGLES:Y,v,k)},ke=function(p,o,g,R){Jr&&(R=p[Jr]),p.drawElements(R==f?p.TRIANGLES:R,g,p.UNSIGNED_SHORT,o==f?0:2*o)},Ih=function(H,U,s,y,r){var Y=H.ARRAY_BUFFER;H.bindBuffer(Y,U),s&&H.bufferData(Y,s,H.STATIC_DRAW),y!=f&&H.vertexAttribPointer(y,r?r:3,H.FLOAT,!1,0,0)},zp=function(H,B,m){var a=H.ELEMENT_ARRAY_BUFFER;H.bindBuffer(a,B),m&&H.bufferData(a,m,H.STATIC_DRAW)},ap=function(X,G,K){if(K!=f&&G!=f){K=Yd(K);var q=K.length;3===q?X.uniform3fv(G,K):4===q?X.uniform4fv(G,K):16===q?X.uniformMatrix4fv(G,!1,K):wc(K)?X.uniform1i(G,K?1:0):2===q?X.uniform2fv(G,K):X.uniform1f(G,K)}},Ld=function(M,Q){Q!=f&&Q>=0&&M.enableVertexAttribArray(Q)},Ob=function(q,C){C!=f&&C>=0&&q.disableVertexAttribArray(C)},lc=function(E,F){var l=E.getGL(),u=E._prg;E._7O=E._8O.pop(),F&&(ap(l,u.uMVMatrix,E._7O),E._6O=F,ap(l,u.uNMatrix,F))},en=function(K,l){return l||(l=Vp()),fr(l,K),ip(l),de(l),l},Be=function(X,g){var T=X.getGL(),Z=X._prg,A=X._7O;g&&(X._8O.push(bq(A)),gh(A,A,g)),ap(T,Z.uMVMatrix,A),ap(T,Z.uNMatrix,en(A,X._6O))},Nf=function(r,c,h,j,I,f){if(h){if(j){var Y=h.value++,F=[(255&Y>>24)/255,(255&Y>>16)/255,(255&Y>>8)/255,(255&Y)/255];h[Y]={data:I,part:f}}else F=Oc;return ap(r,c.uFixPickReverseColor,F),F}},To=function(){var r={center:function(L,w,Z,f,s){var v=w[1]/2,r={x:-w[0]/2,y:v,width:w[0],height:w[1]},M=fb(L,r,Z);return[M.x+f,2*v-M.y-s,Wq()]},front:function(c,w,h,q,A){var S=w[1]/2,f={x:-w[0]/2,y:S,width:w[0],height:w[1]},n=fb(c,f,h);return[n.x+q,2*S-n.y-A,w[2]/2+Wq()]},back:function(R,D,Q,m,r){var J=D[0]/2,y=D[1]/2,F={x:J,y:y,width:D[0],height:D[1]},p=fb(R,F,Q);return[2*J-p.x-m,2*y-p.y-r,-D[2]/2-Wq()]},left:function(o,v,p,F,N){var u=v[1]/2,B={x:-v[2]/2,y:u,width:v[2],height:v[1]},k=fb(o,B,p);return[-v[0]/2-Wq(),2*u-k.y-N,k.x+F]},right:function(S,T,C,$,W){var j=T[2]/2,F=T[1]/2,Y={x:j,y:F,width:T[2],height:T[1]},c=fb(S,Y,C);return[T[0]/2+Wq(),2*F-c.y-W,2*j-c.x-$]},top:function(D,X,E,K,k){var Y={x:-X[0]/2,y:-X[2]/2,width:X[0],height:X[2]},u=fb(D,Y,E);return[u.x+K,X[1]/2+Wq(),u.y+k]},bottom:function(y,q,$,X,J){var i=q[2]/2,E={x:-q[0]/2,y:i,width:q[0],height:q[2]},L=fb(y,E,$);return[L.x+X,-q[1]/2-Wq(),2*i-L.y-J]}};return function(D,$,M,z,Y,L){return r[z](D,$,M,Y||0,L||0)}}(),Xf=function(f,e,y,h,l,w,V,Z,N,O){if(l&&(e[0]+=l[0],e[1]+=l[1],e[2]+=l[2]),zg(f,y),Z){var P=Vp();if(fs(P,N,O),zj(e,P),zg(f,e),f.auto=Z,f.pos=zj([0,0,0],f),w){var $=f.mat2=Vp();fs($,w,V)}}else fs(f,N,O),zg(f,e),h===S?fj(f,-v):h===_?fj(f,v):h===xi?_b(f,-v):h===Kh?_b(f,v):h===o&&fj(f,p),fs(f,w,V);return f},sr=function(M,L){var s=L.auto,O=L.mat2,$=M.gv;if(s){var g=Fl(M.gv.getEye(),L.pos),k=Vp();fj(k,p/2+q(-g[2],g[0])),"y"!==s&&_b(k,-q(g[1],F(g[2]*g[2]+g[0]*g[0]))),O&&gh(k,k,O),k=gh(Vp(),L,k),Be($,k)}else Be($,L)},aj=function(E,B,s,T,N){var t=E.s,D=E.gv,n=D.getGL(),P=D._prg,M=D._buffer,x=D._1O;Sh(n,se,x),sr(E,s),Tc(n,P,t(B+".blend"),t(B+".light"),t(B+".opacity"),t(B+".reverse.flip"),t(B+".reverse.color"),t(B+".reverse.cull")),lm(n,P,x,N,M.uv,Zh),Ih(n,M.vs,T,P.aPosition),Ih(n,M.ns,Qn,P.aNormal),zp(n,M.is,fo),ke(n,0,fo.length),os(n,P,x),Re(n,P),lc(D)},Ql=function(Y,l,s){var O=Y.gv,k=Y.data,V=Y[l]={blend:O.getBodyColor(k)||O.getFaceBlend(k,l),light:O.getFaceLight(k,l),color:O.getFaceColor(k,l),opacity:O.getFaceOpacity(k,l),transparent:O.getFaceTransparent(k,l),reverseFlip:O.getFaceReverseFlip(k,l),reverseColor:O.getFaceReverseColor(k,l),reverseCull:O.getFaceReverseCull(k,l),texture:O.getFaceImage(k,l),discardSelectable:O.getFaceDiscardSelectable(k,l)};if("csg"!==l){V.uv=O.getFaceUv(k,l)||s&&s[l+"Uv"],V.uvScale=O.getFaceUvScale(k,l)||s&&s[l+"UvScale"],V.uvOffset=O.getFaceUvOffset(k,l)||s&&s[l+"UvOffset"];var E=O.getFaceMat(k,l);E&&(V.mat=Sj(k,E))}return V},ls=function(C,W,T,o,E){if(o){if(!E(o.transparent))return;var k=C.data,N=C.gv,$=N._buffer,n=N.getTexture(o.texture,k);lm(W,T,n,o.discardSelectable,$.uv,o.tuv,o.uvOffset,o.uvScale),Tc(W,T,o.blend,o.light,o.opacity,o.reverseFlip,o.reverseColor,o.reverseCull),ap(W,T.uDiffuse,o.color),Ih(W,$.vs,o.vs,T.aPosition),Ih(W,$.ns,o.ns,T.aNormal),$j(W,0,o.vs.length/3),Re(W,T),os(W,T,n)}},tm=function(X,O,k,S,n,R){var t,F,e,P,N,J,f,c,g,A,u,w=[S-O,n-k],z=X?X.length:0;for(e=0;z>e;e++){for(P=2,t=X[e],N=t[0],J=t[1];P+1<t.length;){if(f=t[P],c=t[P+1],F=Qd(O,k,S,n,N,J,f,c,!0)){g=[f-N,c-J],A=nm(g),g[0]/=A,g[1]/=A,A=kl(w,g),A=A>0?R:-R,u=[g[0]*A,g[1]*A];break}N=f,J=c,P+=2}if(u)break}if(u)for(S=O+u[0],n=k+u[1],e=0;z>e;e++)for(P=2,t=X[e],N=t[0],J=t[1];P+1<t.length;){if(f=t[P],c=t[P+1],F=Qd(O,k,S,n,N,J,f,c,!0))return[0,0];N=f,J=c,P+=2}return u?u:[S-O,n-k]},Zf=y.graph3d={},zi=function(j,B,K){gf(k+".graph3d."+j,B,K)},ce="~<yfusfw!+!yjsubNQv!>!opjujtpQ`mh!gjeof$!~<fdobutjEfojMb!>!fdobutjEfojMw|*itbEv)gj!ITBE!gfegj$!!gjeof$!~<ttfouihjsCidubCb!>!ttfouihjsCidubCw|*ttfouihjsCidubCv)gj!TTFOUIHJSCIDUBC!gfegj$!!gjeof$!~<eofmCidubCb!>!eofmCidubCw|*eofmCidubCv)gj!EOFMCIDUBC!gfegj$!!gjeof$!~<spmpDidubCb!>!spmpDidubCw|*spmpDidubCv)gj!SPMPDIDUBC!gfegj$!~<x{/fmbdTuftggPv!+!wVb!,!zy/fmbdTuftggPv!>!wVw|*fsvuyfUv)gj~!gjeof$!~~<*^j]opjujtpQupqTw!.!sfuofd)f{jmbnspo!>!^j]opjudfsjEupqTw<**1/2!-^j]sfuofDupqTv)5dfw!+!yjsubNxfjWv)4dfw!>!sfuofd!4dfw<**1/2!-^j]opjujtpQupqTv)5dfw!+!yjsubNxfjWv)4dfw!>!^j]opjujtpQupqTw|*1/1!>>!x/^j]spmpDupqTv)gj|!*,,j!<UPQT`YBN!=!j!<1>j!uoj)spg!1!?!UPQT`YBN!gj$!!gjeof$!~~<**1/2!-^j]opjujtpQuojpQv)5dfw!+!yjsubNxfjWv)4dfw!>!^j]opjujtpQuojpQw|*1/1!>>!x/^j]spmpDuojpQv)gj|!*,,j!<UOJPQ`YBN!=!j!<1>j!uoj)spg!1!?!UOJPQ`YBN!gj$!!gjeof$!~~<*opjujtpQsje!.!sfuofd)f{jmbnspo!>!^j]opjudfsjEsjEw<**1/2!-1/1!-1/1!-1/1)5dfw!+!yjsubNxfjWv)4dfw!>!sfuofd!4dfw<**1/2!-^j]opjujtpQsjEv)5dfw!+!yjsubNxfjWv)4dfw!>!opjujtpQsje!4dfw|*1/1!>>!x/^j]spmpDsjEv)gj|!*,,j!<SJE`YBN!=!j!<1>j!uoj)spg!1!?!SJE`YBN!gj$!<*yfusfw)4dfw!>!yfusfWw<**1/2!-mbnspOb)5dfw!+!yjsubNOv)4dfw!>!mbnspOw|*ldjQva!%%!yjGva)gj<*1/2!-opjujtpQb)5dfw!+!yjsubNWNv!>!yfusfw!5dfw|!*ejpw)ojbn!ejpw!gjeof$!<fdobutjEfojMw!ubpmg!hojzsbw<fdobutjEfojMb!ubpmg!fuvcjsuub<itbEv!mppc!nspgjov!ITBE!gfegj$!!gjeof$!<^UPQT`YBN]opjudfsjEupqTw!4dfw!hojzsbw<^UPQT`YBN]opjujtpQupqTw!4dfw!hojzsbw<^UPQT`YBN]sfuofDupqTv!4dfw!nspgjov<^UPQT`YBN]opjujtpQupqTv!4dfw!nspgjov<^UPQT`YBN]spmpDupqTv!5dfw!nspgjov!1!?!UPQT`YBN!gj$!!gjeof$!<^UOJPQ`YBN]opjujtpQuojpQw!4dfw!hojzsbw<^UOJPQ`YBN]opjujtpQuojpQv!4dfw!nspgjov<^UOJPQ`YBN]spmpDuojpQv!5dfw!nspgjov!1!?!UOJPQ`YBN!gj$!!gjeof$!<^SJE`YBN]opjudfsjEsjEw!4dfw!hojzsbw<^SJE`YBN]opjujtpQsjEv!4dfw!nspgjov<^SJE`YBN]spmpDsjEv!5dfw!nspgjov!1!?!SJE`YBN!gj$!!gjeof$!<eofmCidubCw!4dfw!hojzsbw<eofmCidubCb!4dfw!fuvcjsuub<eofmCidubCv!mppc!nspgjov!EOFMCIDUBC!gfegj$!!gjeof$!<ttfouihjsCidubCw!ubpmg!hojzsbw<ttfouihjsCidubCb!ubpmg!fuvcjsuub<ttfouihjsCidubCv!mppc!nspgjov!TTFOUIHJSCIDUBC!gfegj$!!gjeof$!<spmpDidubCw!5dfw!hojzsbw<spmpDidubCb!5dfw!fuvcjsuub<spmpDidubCv!mppc!nspgjov!SPMPDIDUBC!gfegj$!<yfusfWw!4dfw!hojzsbw<mbnspOw!4dfw!hojzsbw<yjGv!mppc!nspgjov<ldjQv!mppc!nspgjov<yjsubNxfjWv!5ubn!nspgjov<yjsubNOv!5ubn!nspgjov<yjsubNQv!5ubn!nspgjov<yjsubNWNv!5ubn!nspgjov<opjujtpQb!4dfw!fuvcjsuub<mbnspOb!4dfw!fuvcjsuub<wVw!3dfw!hojzsbw<wVb!3dfw!fuvcjsuub<fmbdTuftggPv!5dfw!nspgjov<fsvuyfUv!mppc!nspgjov!YJGFSQ^#CBRBEJxTT87hJFI,,bX1XrIwcn3UM{ZeZ,M6HEN6nLxe2Z72s2:3johr6foKrpqwxNKYkdbJ2CN[mBbf[wXU,T0oGuSEd190ohLZkEn8IuCe[OHoWeKz:Sf,VS7xdSLw23W1YJLU:jdv2{hiDbhDzNNEh9tp3xsuEp94Sl4c3wRzqwShMDRhCLRjCDEBOH5BBVRBCFRE4cJTHrTDH1BNgHJN",Sp="~~<eJ!>!spmpDhbsG`mh!gjeof$!~<ttfouihjsCidubCw!>+!chs/eJ!!!|*ttfouihjsCidubCv)gj!TTFOUIHJSCIDUBC!gfegj$!~<ttfouihjsCv!>+!chs/eJ!!!|*1/2!>a!ttfouihjsCv)gj~~<zujdbqPusbQv!>+!b/eJ!!!|*1/2!>a!zujdbqPusbQv)gj~!gjeof$!<*spudbGhpg!-*x/eJ!-chs/spmpDhpGv)5dfw!-eJ)yjn!>!eJ<*iuqfe!-sbGhpGv!-sbfOhpGv)qfutiuppnt!>!spudbGhpg!ubpmg<x/esppDhbsG`mh0{/esppDhbsG`mh!>!iuqfe!ubpmg!HPG!gfegj$!!gjeof$!~~~~<chs/^j]spmpDupqTv!+!udfggFupqt!+!fhobSm!+!*1/1!-*mbnspOm.!-O)upe)ybn!>,!chs/eJ<*1/1!-*^j]uofopqyFupqTv!-udfggFupqt)xpq)ybn!>!udfggFupqt|*^j]fmhoBupqTv!?!udfggFupqt)gj<*mbnspOm!-^j]opjudfsjEupqTw)upe!>!udfggFupqt!ubpmg<*spudfWm)f{jmbnspo!>!mbnspOm!4dfw|*1/1!?!fhobSm)!gj~<*1/2!-*^j]fhobSupqTv!0!*spudfWm)iuhofm))ojn!.!1/2!>!fhobSm|*1/1!?!^j]fhobSupqTv)!gj<1/2!>!fhobSm!ubpmg<^j]opjujtpQupqTw!.!yfusfWw!>!spudfWm!4dfw|*1/1!>>!x/^j]spmpDupqTv)gj|!*,,j!<UPQT`YBN!=!j!<1>j!uoj)spg!1!?!UPQT`YBN!gj$!!gjeof$!~~~<chs/^j]spmpDuojpQv!+!fhobSm!+!*1/1!-**spudfWm)f{jmbnspo.!-O)upe)ybn!>,!chs/eJ|*1/1!?!fhobSm)!gj~<*1/2!-*^j]fhobSuojpQv!0!*spudfWm)iuhofm))ojn!.!1/2!>!fhobSm|*1/1!?!^j]fhobSuojpQv)!gj<1/2!>!fhobSm!ubpmg<^j]opjujtpQuojpQw!.!yfusfWw!>!spudfWm!4dfw|*1/1!>>!x/^j]spmpDuojpQv)gj|!*,,j!<UOJPQ`YBN!=!j!<1>j!uoj)spg!1!?!UOJPQ`YBN!gj$!!gjeof$!~~<chs/^j]spmpDsjEv!+!*1/1!-**^j]opjudfsjEsjEw)f{jmbnspo.!-O)upe)ybn!>,!chs/eJ|*1/1!>>!x/^j]spmpDsjEv)gj|!*,,j!<SJE`YBN!=!j!<1>j!uoj)spg!1!?!SJE`YBN!gj$!~<chs/spmpDuihjmebfIv!+!nsfUusfcnbm!>+!chs/eJ~<*1/2!-fhobSuihjmebfIv0*yfusfWw)iuhofm)ojn!.!1/2!!>+!nsfUusfcnbm|*1/1!?!fhobSuihjmebfIv)gj<*1/1!-6/1!,!6/1!+!*M.!-O)upe)ybn!>!nsfUusfcnbm!ubpmg|*1/1!>>!x/spmpDuihjmebfIv)gj|*uihjMv)gj~!gjeof$!~<spmpDeofmCv!>+!eJ|*eofmCv)gj!ftmf$!~<spmpDeofmCv!>+!eJ|*eofmCv)gj!ftmf~<eofmCidubCw!>+!chs/eJ|*eofmCidubCv)gj!EOFMCIDUBC!gfegj$!~<spmpd!>!eJ|ftmf~<spmpDwv!>!eJ|*fsvuyfUv)gj|!ftmf~<spmpDftsfwfSldjQyjGv!>!eJ|*qjmGftsfwfSva!%%!ldbCtj)gj~<fvsu!>!ldbCtj<O.!>!O|*1/1!=!*O!-F)upe)gj<ftmbg!>!ldbCtj!mppc<F.!>!M!4dfw<*yfusfWw.)f{jmbnspo!>!F!4dfw<*mbnspOw)f{jmbnspo!>!O!4dfw!gjeof$!<ftvggjEv!>!spmpd!ftmf$!~<ftvggjEv!>!spmpd|ftmf~<spmpDidubCw!>!spmpd|*spmpDidubCv)gj!SPMPDIDUBC!gfegj$!<spmpd!5dfw|!ftmf~!gjeof$!~~<ftvggjEv!>!eJ|ftmf~<esbdtje|*1/1!>>!x/ftvggjEv)gj|!*!fdobutjEitbEv!?!*!fdobutjEqbHitbEv!-fdobutjEfojMw!)epn!%%!itbEv)!gj!ITBE!gfegj$!!gjeof$!<spmpDftsfwfSldjQyjGv!>!eJ!ftmf$!~<spmpDftsfwfSldjQyjGv!>!eJ|ftmf~<spmpDidubCw!>!eJ|*spmpDidubCv)gj!SPMPDIDUBC!gfegj$!|*yjGv)gj<eJ!5dfw|ftmf~!gjeof$!<spmpDftsfwfSldjQyjGv!>!spmpDhbsG`mh!ftmf$!~<spmpDftsfwfSldjQyjGv!>!spmpDhbsG`mh|ftmf~<spmpDidubCw!>!spmpDhbsG`mh|*spmpDidubCv)gj!SPMPDIDUBC!gfegj$!|*ldjQv)gj~~~<1/2!>!b/spmpDwv|ftmf~<esbdtje|*5/1!=!b/spmpDwv)gj|ftmf~~<esbdtje|*1/1!>>!b/spmpDwv)gj|*uofsbqtobsUv)gj<*wVw!-sfmqnbTv)E3fsvuyfu!>!spmpDwv|**fmcbudfmfTesbdtjEv!%%!ldjQv)a!%%!fsvuyfUv)gj<spmpDwv!5dfw|!*ejpw)ojbn!ejpw!gjeof$!<fdobutjEfojMw!ubpmg!hojzsbw<fdobutjEqbHitbEv!ubpmg!nspgjov<fdobutjEitbEv!ubpmg!nspgjov<itbEv!mppc!nspgjov!ITBE!gfegj$!!gjeof$!<sbGhpGv!ubpmg!nspgjov<sbfOhpGv!ubpmg!nspgjov<spmpDhpGv!5dfw!nspgjov!HPG!gfegj$!!gjeof$!<^UPQT`YBN]opjudfsjEupqTw!4dfw!hojzsbw<^UPQT`YBN]opjujtpQupqTw!4dfw!hojzsbw<^UPQT`YBN]fhobSupqTv!ubpmg!nspgjov<^UPQT`YBN]fmhoBupqTv!ubpmg!nspgjov<^UPQT`YBN]uofopqyFupqTv!ubpmg!nspgjov<^UPQT`YBN]spmpDupqTv!5dfw!nspgjov!1!?!UPQT`YBN!gj$!!gjeof$!<^UOJPQ`YBN]opjujtpQuojpQw!4dfw!hojzsbw<^UOJPQ`YBN]fhobSuojpQv!ubpmg!nspgjov<^UOJPQ`YBN]spmpDuojpQv!5dfw!nspgjov!1!?!UOJPQ`YBN!gj$!!gjeof$!<^SJE`YBN]opjudfsjEsjEw!4dfw!hojzsbw<^SJE`YBN]spmpDsjEv!5dfw!nspgjov!1!?!SJE`YBN!gj$!!gjeof$!<eofmCidubCw!4dfw!hojzsbw<eofmCidubCv!mppc!nspgjov!EOFMCIDUBC!gfegj$!!gjeof$!<ttfouihjsCidubCw!ubpmg!hojzsbw<ttfouihjsCidubCv!mppc!nspgjov!TTFOUIHJSCIDUBC!gfegj$!!gjeof$!<spmpDidubCw!5dfw!hojzsbw<spmpDidubCv!mppc!nspgjov!SPMPDIDUBC!gfegj$!<spmpDuihjmebfIv!5dfw!nspgjov<fhobSuihjmebfIv!ubpmg!nspgjov<yfusfWw!4dfw!hojzsbw<mbnspOw!4dfw!hojzsbw<ftvggjEv!5dfw!nspgjov<zujdbqPusbQv!ubpmg!nspgjov<ttfouihjsCv!ubpmg!nspgjov<uihjMv!mppc!nspgjov<spmpDeofmCv!5dfw!nspgjov<eofmCv!mppc!nspgjov<sfmqnbTv!E3sfmqnbt!nspgjov<wVw!3dfw!hojzsbw<fsvuyfUv!mppc!nspgjov<spmpDftsfwfSldjQyjGv!5dfw!nspgjov<qjmGftsfwfSv!mppc!nspgjov<ldjQv!mppc!nspgjov<yjGv!mppc!nspgjov<fmcbudfmfTesbdtjEv!mppc!nspgjov<uofsbqtobsUv!mppc!nspgjov!YJGFSQ!gjeof$!!gjeof$!!<ubpmg!qnvjefn!opjtjdfsq!!ftmf$!!<ubpmg!qihji!opjtjdfsq!!IHJI`OPJTJDFSQ`UOFNHBSG`MH!gfegj$!!TF`MH!gfegj$!";Ym(u,{setBatchInfo:function(a,q){xk[a]=q},getBatchInfo:function(u){return xk[u]}},!0);var tq=[1,1,1],gq=[1,0,0,0,0,1,0,1,1,1,1,0],Ar=[-.5,.5,.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,.5,.5],Xi=[.5,.5,-.5,.5,.5,.5,.5,-.5,.5,.5,-.5,.5,.5,-.5,-.5,.5,.5,-.5],Mj=[.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,.5,.5,.5,.5,.5,-.5],Ko=[.5,-.5,.5,-.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,-.5,.5,-.5,.5],Ro=[.5,.5,.5,-.5,.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,.5,.5],hm=[-.5,.5,-.5,.5,.5,-.5,.5,-.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5],Or=function(f,w){$h(f,w),Wf(f,w,"_wireframeModelMap","_wireframeIndexMap"),Wf(f,w,"_polylineModelMap","_polylineIndexMap")},$h=function(G,U){if(U){var C,S,L=U._id,$=G._33O,j=G._34O,b=j[L];if(b){S=b.batch,C=$[S];var d=b.begin,w=b.size,Z=3*d,M=3*w,e=4*d,c=4*w,u=C.vs,k=C.uv,B=C.cs,J=C.bs,P=C.rs,F=C.ds,D=b.index;if(C.invalidate=!0,delete j[L],F.splice(D,1),Ur(F))delete $[S];else{for(;D<F.length;D++)b=j[F[D]._id],b.index--,b.begin-=w;u.splice(Z,M),C.ns.splice(Z,M),C.ps.splice(e,c),k&&k.splice(2*d,2*w),B&&B.splice(e,c),J&&J.splice(Z,M),P&&P.splice(d,w)}}}else G._34O={},G._33O={}},Wf=function(c,K,$,s){if(K){var F,Y,O=K._id,t=c[$],G=c[s],S=G[O];if(S){Y=S.batch,F=t[Y];var k=S.begin,q=S.size,j=3*k,p=3*q,T=4*k,H=4*q,h=F.ds,A=S.index;if(F.invalidate=!0,delete G[O],h.splice(A,1),Ur(h))delete t[Y];else{for(;A<h.length;A++)S=G[h[A]._id],S.index--,S.begin-=q;F.vs.splice(j,p),F.cs.splice(T,H),F.ps.splice(T,H),F.ls&&F.ls.splice(k,q)}}}else c[$]={},c[s]={}},fq=function(V,e,s,p){if(!p)return f;var E=e[p];if(!E){var D=xk[p]||Ln,G=D.image?[]:f;E=e[p]={vs:[],ns:[],uv:G,cs:G?f:D.color?f:[],bs:D.blend?[]:f,ps:[],rs:D.brightness?[]:f,ds:[]}}return E.invalidate=!0,V[s._id]={index:E.ds.length,begin:E.vs.length/3,batch:p},E.ds.push(s),E},ci=function(A,H){return H!=f&&1!==H?[A[0]*H,A[1]*H,A[2]*H,A[3]]:A},Yc=function(q,g,h){var z=q.getBrightness(g),J=Yd(h.color||"white"),U=J[3]<1,C=Math.ceil(h.width)||1,X=(U?"T":"O")+C,i=q._wireframeIndexMap,w=q._wireframeModelMap,c=w[X];c||(c=w[X]={vs:[],cs:[],ps:[],ds:[],T:U,W:C}),c.invalidate=!0;var M=c.vs,S=c.cs,e=c.ds,k=i[g._id]={index:e.length,begin:M.length/3,batch:X};e.push(g),dk(M,Sj(g,h.mat),h.short?pr:kc);var y=k.size=M.length/3-k.begin;Se(S,ci(J,z),y)},Nc=function(M,V,p,C,Q,T,g,d,W){var F=M.getBrightness(V);F==f&&(F=1);var c=ci(Yd(C),F),t=c[3]<1;T=Math.ceil(T)||1;var w=(t?"T":"O")+T;if(g){var R=g[0],z=(g[1]||R)+R;w+="-"+R+"-"+z,W&&(w+="-"+xd(W)+"-"+F,W=ci(Yd(W),F))}var o=M._polylineIndexMap,Z=M._polylineModelMap,A=Z[w];A||(A=Z[w]={vs:[],cs:[],ps:[],ds:[],ls:g?[]:f,T:t,W:T},g&&(A.D=R,A.G=z,A.A=W)),A.invalidate=!0;var i,D=A.vs,m=A.cs,O=A.ds,J=A.ls,U=o[V._id]={index:O.length,begin:D.length/3,batch:w};O.push(V),ul(D,p);var h,N=U.size=D.length/3-U.begin,X=p.length,K=0,s=[];if(g||Q)for(i=0;X>i;i+=6)h=nm([p[i],p[i+1],p[i+2]],[p[i+3],p[i+4],p[i+5]]),s.push(h),K+=h;if(K&&Q){var k,P=ci(Yd(Q),F),y=[P[0]-c[0],P[1]-c[1],P[2]-c[2],P[3]-c[3]],L=0;for(i=0;X>i;i+=6)k=L/K,Se(m,[c[0]+y[0]*k,c[1]+y[1]*k,c[2]+y[2]*k,c[3]+y[3]*k],1),L+=s[i/6],k=L/K,Se(m,[c[0]+y[0]*k,c[1]+y[1]*k,c[2]+y[2]*k,c[3]+y[3]*k],1)}else Se(m,c,N);if(g)for(L=d||0,i=0;X>i;i+=6)J.push(L),L+=s[i/6],J.push(L)},nr=function(g,Z,D){if(Z)for(var H=4*Z.begin,Y=D[Z.batch].ps,y=0;y<Z.size;y++)Y[H++]=g[0],Y[H++]=g[1],Y[H++]=g[2],Y[H++]=g[3]},yf=function(V,j){var o=j+["32"],v=V[j],s=V[o];v?s&&s.length===v.length?s.set(v):V[o]=new Jp(v):delete V[o]},tp=function(y,E,z,U,S,T,M,O,a){if(Ji(z))z.forEach(function(K){tp(y,E,K,U,S,T,M,O,a)});else if(sl(z))tp(y,E,$r(U,z),U,S,T,M,O,a);else if(ze(z)){var I,b=Fe(z.mat,E,y),R=Fe(z.s3,E,y),t=Fe(z.t3,E,y),i=Fe(z.r3,E,y);if((R||i||t||b)&&(I=lq(b,R,i,Fe(z.rotationMode,E,y),t),T.push(I)),z.shape3d)tp(y,E,z.shape3d,U,S,T,M,O,z);else{a=a||Ln;var Q=T[0],e=T.length,V=U("shape3d.color",z.color,a.color);if(S||(S=U("shape3d.blend",z.blend,a.blend)),e>1){Q=bq(Q);for(var s=1;e>s;s++)gh(Q,Q,T[s])}z.vs&&U(Mf,z.visible,a.visible)&&hs(Q,M,O,z.vs,z.uv,z.is,S,V),z.top_vs&&U(lf,z.topVisible,a.topVisible)&&hs(Q,M,O,z.top_vs,z.top_uv,z.top_is,S,U("shape3d.top.color",z.topColor,a.topColor)||V),z.bottom_vs&&U(hk,z.bottomVisible,a.bottomVisible)&&hs(Q,M,O,z.bottom_vs,z.bottom_uv,z.bottom_is,S,U("shape3d.bottom.color",z.bottomColor,a.bottomColor)||V),z.from_vs&&U(Yg,z.fromVisible,a.fromVisible)&&hs(Q,M,O,z.from_vs,z.from_uv,z.from_is,S,U("shape3d.from.color",z.fromColor,a.fromColor)||V),z.to_vs&&U(Kb,z.toVisible,a.toVisible)&&hs(Q,M,O,z.to_vs,z.to_uv,z.to_is,S,U("shape3d.to.color",z.toColor,a.toColor)||V)}I&&T.pop()}},hs=function(i,Z,B,o,R,s,w,d){var K,O=Z.cs,C=Z.uv,W=Z.bs;if(s){K=s.length;for(var a=0;K>a;a++){var E=s[a];dk(B,i,[o[3*E],o[3*E+1],o[3*E+2]]),C&&ul(C,[R[2*E],R[2*E+1]])}}else K=o.length/3,dk(B,i,o),C&&ul(C,R);O&&Se(O,d,K),W&&(w?pe(W,w,K):ul(W,tq,K))},Nl=function(A,H,h,m){var b=[];m?tp(A.gv,A.data,m,A.s,A.getBodyColor(),[H],h,b):(xl(A,S,H,h,b),xl(A,_,H,h,b),xl(A,Q,H,h,b),xl(A,o,H,h,b),xl(A,xi,H,h,b),xl(A,Kh,H,h,b),xl(A,"csg",H,h,b)),b.length&&(ul(h.vs,b),ul(h.ns,ib(b)))},xl=function(w,j,E,B,P){var s=w[j];if(s){var X=s.tuv;if(X){var p,J,F,V,v=s.uvScale,k=s.uvOffset;if(v)for(p=X.length,J=v[0],F=v[1],V=0;p>V;V+=2)X[V]*=J,X[V+1]*=F;if(k)for(p=X.length,J=k[0],F=k[1],V=0;p>V;V+=2)X[V]+=J,X[V+1]+=F}hs(E,B,P,s.vs,X,f,s.blend,s.color)}},il=function(B,T,I,G,b,H,M,L){var $,n=I.cs,E=I.uv,R=I.bs;if(B.getFaceVisible(T,H)){var r=B.getFaceMat(T,H);if(r&&(b=Sj(T,r)),dk(M,b,L),n&&Se(n,B.getFaceColor(T,H),6),E){var U=B.getFaceUv(T,H)||G[H+"Uv"],p=B.getFaceUvScale(T,H)||G[H+"UvScale"],d=B.getFaceUvOffset(T,H)||G[H+"UvOffset"];U=U?[U[6],U[7],U[0],U[1],U[2],U[3],U[2],U[3],U[4],U[5],U[6],U[7]]:gq;var y=U[0],D=U[1],q=U[2],X=U[3],x=U[4],v=U[5],g=U[6],Q=U[7],h=U[8],K=U[9],t=U[10],l=U[11];if(p){var i=p[0],w=p[1];y*=i,D*=w,q*=i,X*=w,x*=i,v*=w,g*=i,Q*=w,h*=i,K*=w,t*=i,l*=w}if(d){var o=d[0],J=d[1];y+=o,D+=J,q+=o,X+=J,x+=o,v+=J,g+=o,Q+=J,h+=o,K+=J,t+=o,l+=J}E.push(y,D,q,X,x,v,g,Q,h,K,t,l)}R&&($=B.getBodyColor(T)||B.getFaceBlend(T,H),$?pe(R,$,6):ul(R,tq,6))}},jb=function(A,F,u,c){var x=A.getGL(),X=A._prg,$=A._buffer;if(!vj(F)){Be(A),mh(x,X);for(var O in F){var M=F[O];if(!(c&&!M.T||!c&&M.T)){M.invalidate?(M.invalidate=!1,yf(M,"vs"),yf(M,"cs"),yf(M,"ls"),yf(M,"ps")):u&&yf(M,"ps");var v=M.vs32,R=u?M.ps32:M.cs32;Nr(x,X,M.W,f,$.vs,v);var m=M.D,g=!A._dashDisabled&&m;g&&(ap(x,X.uDash,!0),ap(x,X.uDashDistance,m),ap(x,X.uDashGapDistance,M.G),Ld(x,X.aLineDistance),Ih(x,$.lineDistance,M.ls32,X.aLineDistance,1),ap(x,X.uDiffuse,M.A||Np));var C=!A._batchColorDisabled;C&&(ap(x,X.uBatchColor,!0),Ld(x,X.aBatchColor),Ih(x,$.batchColor,R,X.aBatchColor,4)),$j(x,0,v.length/3,x.LINES),C&&(ap(x,X.uBatchColor,!1),Ob(x,X.aBatchColor)),g&&(ap(x,X.uDash,!1),Ob(x,X.aLineDistance))}}Ap(x,X)}},Km=function(Z,M,v){var U,O,i,u,q,k,a,r,o,H,N,A=Z.getGL(),$=Z._prg,s=Z._buffer,y=Z._33O;if(!vj(y)){Be(Z);for(U in y)if(O=xk[U]||Ln,!(v&&!O.transparent||!v&&O.transparent)&&(i=y[U],M||!O.transparentMask)){var Y=v&&O.autoSort!==!1&&(Z._33Q||i.invalidate);if(i.invalidate?(i.invalidate=!1,yf(i,"vs"),yf(i,"ns"),yf(i,"cs"),yf(i,"rs"),yf(i,"ps"),yf(i,"bs"),yf(i,"uv")):M&&yf(i,"ps"),q=i.vs32,k=M?i.ps32:i.cs32,o=i.uv32,a=i.bs32,r=i.rs32,N=q.length/3,Y&&(i.is=dr(q,Z.getEye())),N){u=O.light,H=Z.getTexture(O.image),Tc(A,$,f,O.light,O.opacity,O.reverseFlip,O.reverseColor,O.reverseCull),k?Z._batchColorDisabled||(ap(A,$.uBatchColor,!0),Ld(A,$.aBatchColor),Ih(A,s.batchColor,k,$.aBatchColor,4)):ap(A,$.uDiffuse,O.color);var F=a&&!Z._batchBlendDisabled;F&&(ap(A,$.uBatchBlend,!0),Ld(A,$.aBatchBlend),Ih(A,s.batchBlend,a,$.aBatchBlend));var p=r&&!Z._batchBrightnessDisabled;p&&(ap(A,$.uBatchBrightness,!0),Ld(A,$.aBatchBrightness),Ih(A,s.batchBrightness,r,$.aBatchBrightness,1)),o&&lm(A,$,H,O.discardSelectable,s.uv,o,O.uvOffset,O.uvScale),Ih(A,s.vs,q,$.aPosition),Ih(A,s.ns,i.ns32,$.aNormal),i.is?(zp(A,s.is,i.is),ke(A,0,N)):$j(A,0,N),o&&os(A,$,H),k&&!Z._batchColorDisabled&&(ap(A,$.uBatchColor,!1),Ob(A,$.aBatchColor)),F&&(ap(A,$.uBatchBlend,!1),Ob(A,$.aBatchBlend)),p&&(ap(A,$.uBatchBrightness,!1),Ob(A,$.aBatchBrightness)),Re(A,$)}}}},dr=function(m,f){for(var h=m.length/3,W=new Array(h),c=h/3,r=new Array(c),s=0;c>s;s++)r[s]=s;r.sort(function(v,U){var G=9*v,i=[(m[G]+m[G+3]+2*m[G+6])/4,(m[G+1]+m[G+4]+2*m[G+7])/4,(m[G+2]+m[G+5]+2*m[G+8])/4];G=9*U;var E=[(m[G]+m[G+3]+2*m[G+6])/4,(m[G+1]+m[G+4]+2*m[G+7])/4,(m[G+2]+m[G+5]+2*m[G+8])/4],I=nm(f,i)-nm(f,E);return I>0?-1:0>I?1:0});for(var s=0;c>s;s++){var V=3*s,Q=3*r[s];W[V]=Q,W[V+1]=Q+1,W[V+2]=Q+2}return new ms(W)},Zh=new Jp([0,0,0,1,1,1,1,0]),Qn=new Jp([0,0,1,0,0,1,0,0,1,0,0,1]),fo=new ms([0,1,2,2,3,0]),ud=new Jp([-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,.5,.5,-.5,.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,.5,.5,.5,.5,.5,.5,-.5,.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,.5,.5,.5,.5,.5,-.5,-.5,-.5,.5,-.5,-.5,-.5,.5,-.5,-.5,.5,-.5,.5]),rp=new ms([0,1,2,0,2,3,4,5,6,4,6,7,8,9,10,8,10,11,12,13,14,12,14,15,16,17,18,16,18,19,20,21,22,20,22,23]),jr=new Jp([0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0]),Bb=new Jp([0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0]),cr=ib(ud,rp),kc=(new Jp([-.5,.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,.5,.5,.5,-.5,-.5,.5,.5,-.5,-.5,-.5,-.5,-.5,.5,-.5]),new ms([0,1,2,3,0,7,5,4,6,7,5,3,2,4,6,1]),[-.5,.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,-.5,.5,.5,.5,.5,.5,.5,.5,-.5,.5,.5,-.5,.5,-.5,-.5,-.5,-.5,-.5,-.5,-.5,.5,-.5,-.5,.5,-.5,-.5,.5,.5,-.5,.5,.5,-.5,-.5,.5,-.5,.5,.5,.5,.5,.5,-.5,.5,-.5,.5,.5,-.5,-.5,-.5,.5,.5,-.5,.5,-.5,-.5,-.5,.5,-.5,-.5,-.5]),pr=[-.5,.5,.5,-.4,.5,.5,-.5,.5,.5,-.5,.4,.5,-.5,.5,.5,-.5,.5,.4,.5,.5,.5,.4,.5,.5,.5,.5,.5,.5,.4,.5,.5,.5,.5,.5,.5,.4,-.5,-.5,.5,-.4,-.5,.5,-.5,-.5,.5,-.5,-.4,.5,-.5,-.5,.5,-.5,-.5,.4,.5,-.5,.5,.4,-.5,.5,.5,-.5,.5,.5,-.4,.5,.5,-.5,.5,.5,-.5,.4,-.5,.5,-.5,-.4,.5,-.5,-.5,.5,-.5,-.5,.4,-.5,-.5,.5,-.5,-.5,.5,-.4,.5,.5,-.5,.4,.5,-.5,.5,.5,-.5,.5,.4,-.5,.5,.5,-.5,.5,.5,-.4,-.5,-.5,-.5,-.4,-.5,-.5,-.5,-.5,-.5,-.5,-.4,-.5,-.5,-.5,-.5,-.5,-.5,-.4,.5,-.5,-.5,.4,-.5,-.5,.5,-.5,-.5,.5,-.4,-.5,.5,-.5,-.5,.5,-.5,-.4],ys=function(v,Q,P,K,E,j){j=j||16,v=v||16,Q=Q||0,P=P||v;var $,R,C,k,d,c,A,U,h=.5,u=[],w=[],X=z/v,l=p/j;for($=0;j>$;$++)for(d=$+1,k=$*l,U=d*l,R=Q;P>R;R++)c=R+1,C=R*X,A=c*X,u.push(h*n(k)*N(C),h*N(k),-h*n(k)*n(C),h*n(U)*N(A),h*N(U),-h*n(U)*n(A),h*n(k)*N(A),h*N(k),-h*n(k)*n(A),h*n(k)*N(C),h*N(k),-h*n(k)*n(C),h*n(U)*N(C),h*N(U),-h*n(U)*n(C),h*n(U)*N(A),h*N(U),-h*n(U)*n(A)),w.push(R/v,$/j,c/v,d/j,c/v,$/j,R/v,$/j,R/v,d/j,c/v,d/j);if(K)for(C=Q*X,$=0;j>$;$++)d=$+1,k=$*l,U=d*l,u.push(0,0,0,h*n(U)*N(C),h*N(U),-h*n(U)*n(C),h*n(k)*N(C),h*N(k),-h*n(k)*n(C)),w.push(0,.5,Q/v,d/j,Q/v,$/j);if(E)for(C=P*X,$=0;j>$;$++)d=$+1,k=$*l,U=d*l,u.push(0,0,0,h*n(k)*N(C),h*N(k),-h*n(k)*n(C),h*n(U)*N(C),h*N(U),-h*n(U)*n(C)),w.push(1,.5,P/v,$/j,P/v,d/j);return{vs:u,uv:w}},Je=function(I,l,w){I=I||.17,l=l||12,w=w||18,0>I?I=0:I>.25&&(I=.25);var T,d,r,o,g,K,Q,C=.5,F=[],$=[],H=[],W=z/w,a=z/l,c=C-I;for(d=0;l>=d;d++)for(o=-p+d*a,K=N(o),g=n(o),T=0;w>=T;T++)r=T*W,Q=c+I*K,F.push(N(r)*Q,g*I,-n(r)*Q),$.push(T/w,1-d/l);for(d=0;l>d;d++){var e=d*(w+1),f=(d+1)*(w+1);for(T=0;w>T;T++)H.push(e+T,f+T+1,f+T,e+T,e+T+1,f+T+1)}return{vs:F,uv:$,is:H}},Ef=function(g,C,A,O,H,t,L){L=L||12,g=g||18,C=C||0,A=A||g,t=t||.17,0>t?t=0:t>.25&&(t=.25);var v,Z,X,d,s,_,T,Q,j,F,E=.5,x=[],K=[],G=z/g,W=z/L,B=E-t;for(X=0;L>X;X++)for(d=X+1,T=-p+X*W,Q=-p+d*W,v=C;A>v;v++)Z=v+1,s=v*G,_=Z*G,j=B+t*N(T),F=B+t*N(Q),x.push(N(s)*j,n(T)*t,-n(s)*j,N(_)*j,n(T)*t,-n(_)*j,N(_)*F,n(Q)*t,-n(_)*F,N(s)*j,n(T)*t,-n(s)*j,N(_)*F,n(Q)*t,-n(_)*F,N(s)*F,n(Q)*t,-n(s)*F),K.push(v/g,1-X/L,Z/g,1-X/L,Z/g,1-d/L,v/g,1-X/L,Z/g,1-d/L,v/g,1-d/L);if(O){var i=[],o=[];for(X=0;L>X;X++)d=X+1,T=-p+X*W,Q=-p+d*W,s=C*G,j=B+t*N(T),F=B+t*N(Q),i.push(N(s)*j,n(T)*t,-n(s)*j,N(s)*F,n(Q)*t,-n(s)*F,N(s)*B,0,-n(s)*B),o.push(.5+.5*N(T),.5-.5*n(T),.5+.5*N(Q),.5-.5*n(Q),.5,.5)}if(H){var l=[],b=[];for(X=0;L>X;X++)d=X+1,T=-p+X*W,Q=-p+d*W,s=A*G,j=B+t*N(T),F=B+t*N(Q),l.push(N(s)*j,n(T)*t,-n(s)*j,N(s)*B,0,-n(s)*B,N(s)*F,n(Q)*t,-n(s)*F),b.push(.5-.5*N(T),.5-.5*n(T),.5,.5,.5-.5*N(Q),.5-.5*n(Q))}return{vs:x,uv:K,from_vs:i,from_uv:o,to_vs:l,to_uv:b}},js=function(q,g){for(var k,p,u,l=.5,R=16,y=[],b=[],L=[],M=z/R,w=0;R>=w;w++)k=w*M,p=N(k)*l,u=n(k)*l,y.push(p,-l,u,p,l,u),b.push(1-w/R,1,1-w/R,0);for(w=0;R>w;w++)L.push(2*w,2*w+1,2*w+3,2*w,2*w+3,2*w+2);if(g){var P=[],x=[],t=[];for(P.push(0,-l,0),x.push(.5,.5),w=0;R>=w;w++)k=w*M,p=N(k),u=n(k),P.push(p*l,-l,-u*l),x.push(.5+.5*p,.5+.5*u);for(w=0;R>w;w++)t.push(0,w+2,w+1)}if(q){var f=[],H=[],T=[];for(f.push(0,l,0),H.push(.5,.5),w=0;R>=w;w++)k=w*M,p=N(k),u=n(k),f.push(p*l,l,-u*l),H.push(.5+.5*p,.5-.5*u);for(w=0;R>w;w++)T.push(0,w+1,w+2)}return{vs:y,uv:b,is:L,bottom_vs:P,bottom_uv:x,bottom_is:t,top_vs:f,top_uv:H,top_is:T}},km=function(a,w,U,O,t,X,P){a=a||12,w=w||0,U=U||a;for(var M,W,$,i,E,R,u,Q,_=.5,I=[],V=[],D=z/a,k=w;U>k;k++)M=k+1,W=k*D,$=M*D,i=N(W)*_,E=n(W)*_,R=N($)*_,u=n($)*_,I.push(i,-_,-E,R,-_,-u,i,_,-E,R,-_,-u,R,_,-u,i,_,-E),V.push(k/a,1,M/a,1,k/a,0,M/a,1,M/a,0,k/a,0);if(P){var G=[],c=[];for(k=w;U>k;k++)W=D*k,$=D*(k+1),i=N(W),E=n(W),R=N($),u=n($),G.push(i*_,-_,-E*_,0,-_,0,R*_,-_,-u*_),c.push(.5+.5*i,.5+.5*E,.5,.5,.5+.5*R,.5+.5*u)}if(X){var l=[],f=[];for(k=w;U>k;k++)W=D*k,$=D*(k+1),i=N(W),E=n(W),R=N($),u=n($),l.push(i*_,_,-E*_,R*_,_,-u*_,0,_,0),f.push(.5+.5*i,.5-.5*E,.5+.5*R,.5-.5*u,.5,.5)}return O&&(W=D*w,i=N(W)*_,E=n(W)*_,Q=w/a,I.push(0,_,0,0,-_,0,i,-_,-E,i,-_,-E,i,_,-E,0,_,0),V.push(0,0,0,1,Q,1,Q,1,Q,0,0,0)),t&&(W=D*U,i=N(W)*_,E=n(W)*_,Q=U/a,I.push(0,-_,0,0,_,0,i,_,-E,i,_,-E,i,-_,-E,0,-_,0),V.push(1,1,1,0,Q,0,Q,0,Q,1,1,1)),{vs:I,uv:V,bottom_vs:G,bottom_uv:c,top_vs:l,top_uv:f}},qm=function($,T,s,k,P){T=T||18,s=s||0,k=k==f?z:k,P=P==f?.5:P;for(var V,S,t,p,g,R,b=[0,.5,.75,.875,.9375],q=[],i=[],a=[],_=k/T,I=0;I<b.length;I++){var H=0===I%2?0:.5;for(V=0;T>=V;V++)S=(V+H)*_+s,t=1-b[I],q.push(N(S)*P*t,-P+2*b[I]*P,-n(S)*P*t),i.push((V+H)/T,t)}for(I=0;I<b.length-1;I++){var Z=I*(T+1),l=(I+1)*(T+1);for(V=0;T>V;V++)a.push(Z+V,l+V+1,l+V,Z+V,Z+V+1,l+V+1)}for(R=q.length/3-(T+1),V=0;T>V;V++)q.push(0,P,0),i.push((V+.5)/T,0);for(V=0;T>V;V++)a.push(R+V,R+V+1,R+(T+1)+V);if($){var h=[],u=[],X=[];for(h.push(0,-P,0),u.push(.5,.5),V=0;T>=V;V++)S=V*_+s,p=N(S),g=n(S),h.push(p*P,-P,-g*P),u.push(.5+.5*p,.5+.5*g);for(V=0;T>V;V++)X.push(0,V+2,V+1)}return{vs:q,uv:i,is:a,bottom_vs:h,bottom_uv:u,bottom_is:X}},Fg=function(w,d,t,m,p,E){w=w||16,d=d||0,t=t||w;for(var u=.5,U=[],W=[],v=z/w,L=d;t>L;L++){var H=L+1,B=v*L,Y=v*H;U.push(N(B)*u,-u,-n(B)*u,N(Y)*u,-u,-n(Y)*u,0,u,0),W.push(L/w,1,H/w,1,(L+H)/2/w,0)}if(E){var S=[],P=[];for(L=d;t>L;L++){B=v*L,Y=v*(L+1);var Q=N(B),k=n(B),e=N(Y),M=n(Y);S.push(Q*u,-u,-k*u,0,-u,0,e*u,-u,-M*u),P.push(.5+.5*Q,.5+.5*k,.5,.5,.5+.5*e,.5+.5*M)}}return m&&(B=v*d,U.push(0,u,0,0,-u,0,N(B)*u,-u,-n(B)*u),W.push(0,0,0,1,d/w,1)),p&&(B=v*t,U.push(0,-u,0,0,u,0,N(B)*u,-u,-n(B)*u),W.push(1,1,1,0,t/w,1)),{vs:U,uv:W,bottom_vs:S,bottom_uv:P}},ah=function(g,r,I,i,s,e,N,D){for(var $=[],F=0;F<g.length-1;F+=2)$.push({x:g[F],y:g[F+1]});return Pj($,r,I,i,s,e,N,D,!1)},Gg={roundRect:function(n,E){return ah([.5,.4,.5,.442,.471,.471,.442,.5,.4,.5,-.4,.5,-.442,.5,-.471,.471,-.5,.442,-.5,.4,-.5,-.4,-.5,-.442,-.471,-.471,-.442,-.5,-.4,-.5,.4,-.5,.442,-.5,.471,-.471,.5,-.442,.5,-.4,.5,.4],[1,3,3,2,3,3,2,3,3,2,3,3,2],n,E,3)},star:function(v,A){return ah([.193,.079,.333,.5,0,.233,-.333,.5,-.194,.079,-.5,-.167,-.112,-.167,0,-.5,.111,-.167,.5,-.167],[1,2,2,2,2,2,2,2,2,2,5],v,A)},rect:function(a,u){return ah([.5,-.5,.5,.5,-.5,.5,-.5,-.5],[1,2,2,2,5],a,u)},triangle:function(B,L){return ah([0,-.5,.5,.5,-.5,.5],[1,2,2,5],B,L)},rightTriangle:function(E,R){return ah([.5,.5,-.5,.5,-.5,-.5],[1,2,2,5],E,R)},parallelogram:function(o,h){return ah([.5,-.5,.25,.5,-.5,.5,-.25,-.5],[1,2,2,2,5],o,h)
},trapezoid:function(P,E){return ah([.25,-.5,.5,.5,-.5,.5,-.25,-.5],[1,2,2,2,5],P,E)}},hl={sphere:1,cylinder:1,cone:1,torus:1},Xr={torus:1,sphere:1},Hg=Ym(el(Gg),{cylinder:1}),fn=Ym(el(Gg),{cylinder:1,cone:1}),wh=function(X,q,C){var R,o=Fe(X.mat,C,q),y=Fe(X.s3,C,q),H=Fe(X.t3,C,q),Y=Fe(X.r3,C,q);return(y||Y||H||o)&&(R=bq(q._6O),Be(q,lq(o,y,Y,Fe(X.rotationMode,C,q),H))),R},Gq=function(p,J,X,E,n,B,Y){if(Ji(X))X.forEach(function(h){Gq(p,J,h,E,n,B,Y)});else if(sl(X))Gq(p,J,$r(E,X),E,n,B,Y);else if(ze(X))if(X.shape3d){var k=wh(X,p,J);Gq(p,J,X.shape3d,E,n,B,X),k&&lc(p,k)}else Ep(p,J,X,E,n,B,Y)},Ep=function(Z,R,d,D,n,T,P){if(P=P||Ln,!T||T(D("shape3d.transparent",d.transparent,P.transparent))){var H=wh(d,Z,R);vn(d);var G=Z._26I,V=Z._prg,w=Z._buffer,M=D("shape3d.color",d.color,P.color);n||(n=D("shape3d.blend",d.blend,P.blend)),Tc(G,V,n,D("shape3d.light",d.light,P.light),D("shape3d.opacity",d.opacity,P.opacity),D("shape3d.reverse.flip",d.reverseFlip,P.reverseFlip),D("shape3d.reverse.color",d.reverseColor,P.reverseColor),D("shape3d.reverse.cull",d.reverseCull,P.reverseCull)),d.vs&&D(Mf,d.visible,P.visible)&&uh(G,V,w,M,Z.getTexture(D("shape3d.image",d.image,P.image),R),D("shape3d.discard.selectable",d.discardSelectable,P.discardSelectable),d.vs,d.uv,d.ns,d.is,D("shape3d.uv.offset",d.uvOffset,P.uvOffset),D("shape3d.uv.scale",d.uvScale,P.uvScale)),d.top_vs&&D(lf,d.topVisible,P.topVisible)&&uh(G,V,w,D("shape3d.top.color",d.topColor,P.topColor)||M,Z.getTexture(D("shape3d.top.image",d.topImage,P.topImage),R),D("shape3d.top.discard.selectable",d.topDiscardSelectable,P.topDiscardSelectable),d.top_vs,d.top_uv,d.top_ns,d.top_is,D("shape3d.top.uv.offset",d.topUvOffset,P.topUvOffset),D("shape3d.top.uv.scale",d.topUvScale,P.topUvScale)),d.bottom_vs&&D(hk,d.bottomVisible,P.bottomVisible)&&uh(G,V,w,D("shape3d.bottom.color",d.bottomColor,P.bottomColor)||M,Z.getTexture(D("shape3d.bottom.image",d.bottomImage,P.bottomImage),R),D("shape3d.bottom.discard.selectable",d.bottomDiscardSelectable,P.bottomDiscardSelectable),d.bottom_vs,d.bottom_uv,d.bottom_ns,d.bottom_is,D("shape3d.bottom.uv.offset",d.bottomUvOffset,P.bottomUvOffset),D("shape3d.bottom.uv.scale",d.bottomUvScale,P.bottomUvScale)),d.from_vs&&D(Yg,d.fromVisible,P.fromVisible)&&uh(G,V,w,D("shape3d.from.color",d.fromColor,P.fromColor)||M,Z.getTexture(D("shape3d.from.image",d.fromImage,P.fromImage),R),D("shape3d.from.discard.selectable",d.fromDiscardSelectable,P.fromDiscardSelectable),d.from_vs,d.from_uv,d.from_ns,d.from_is,D("shape3d.from.uv.offset",d.fromUvOffset,P.fromUvOffset),D("shape3d.from.uv.scale",d.fromUvScale,P.fromUvScale)),d.to_vs&&D(Kb,d.toVisible,P.toVisible)&&uh(G,V,w,D("shape3d.to.color",d.toColor,P.toColor)||M,Z.getTexture(D("shape3d.to.image",d.toImage,P.toImage),R),D("shape3d.to.discard.selectable",d.toDiscardSelectable,P.toDiscardSelectable),d.to_vs,d.to_uv,d.to_ns,d.to_is,D("shape3d.to.uv.offset",d.toUvOffset,P.toUvOffset),D("shape3d.to.uv.scale",d.toUvScale,P.toUvScale)),Re(G,V),H&&lc(Z,H)}},uh=function(t,b,S,N,g,J,c,K,u,P,E,m){c&&(ap(t,b.uDiffuse,N),K&&lm(t,b,g,J,S.uv,K,E,m),Ih(t,S.vs,c,b.aPosition),Ih(t,S.ns,u,b.aNormal),P?(zp(t,S.is,P),ke(t,0,P.length)):$j(t,0,c.length/3),K&&os(t,b,g))},$r=function(s,E){if(E||(E=s(of)),!E)return f;if(ze(E))return E;var x=mp[E];if(x)return x;var P=E;if(hl[E]){var M=s("shape3d.side"),C=s("shape3d.side.from"),F=s("shape3d.side.to"),Z=s(Yg),m=s(Kb);3>M?(M=0,C=0,F=0,Z=!1,m=!1):((C==f||0>C)&&(C=0),(F==f||F>M)&&(F=M),0===C&&F===M&&(Z=!1,m=!1)),P+="-"+M+"-"+C+"-"+F+"-"+Z+"-"+m}if(Hg[E]){var n=s(lf);P+="-"+n}if(fn[E]){var z=s(hk);P+="-"+z}if("torus"===E){var g=s("shape3d.torus.radius");0>g?g=0:g>.25&&(g=.25),P+="-"+g}if(Xr[E]){var t=s(ji);P+="-"+t}return x=co[P],x||("box"===E?x=u.createBoxModel():Gg[E]?x=Gg[E](n,z):"sphere"===E?x=u.createSphereModel(M,C,F,Z,m,t):E===We?x=u.createCylinderModel(M,C,F,Z,m,n,z):"cone"===E?x=u.createConeModel(M,C,F,Z,m,z):"torus"===E&&(x=u.createTorusModel(M,C,F,Z,m,g,t)),co[P]=x),x},vn=function(){var U=["vs","ns","uv","top_vs","top_ns","top_uv","bottom_vs","bottom_ns","bottom_uv","from_vs","from_ns","from_uv","to_vs","to_ns","to_uv","er","al"],D=["is","top_is","bottom_is","from_is","to_is"];return A=zq(ce.substr(ce.indexOf("^#")+2)),pp(u[dd(hr+U[15]+"n"+U[16])]())&&fg()?function(F){F&&!F._complete_&&(F._complete_=!0,F.vs&&Ur(F.ns)&&(F.ns=ib(F.vs,F.is)),F.top_vs&&Ur(F.top_ns)&&(F.top_ns=ib(F.top_vs,F.top_is)),F.bottom_vs&&Ur(F.bottom_ns)&&(F.bottom_ns=ib(F.bottom_vs,F.bottom_is)),F.from_vs&&Ur(F.from_ns)&&(F.from_ns=ib(F.from_vs,F.from_is)),F.to_vs&&Ur(F.to_ns)&&(F.to_ns=ib(F.to_vs,F.to_is)),U.forEach(function(Q){var z=F[Q];Ji(z)&&(F[Q]=new Jp(z))}),D.forEach(function(K){var q=F[K];Ji(q)&&(F[K]=new ms(q))}))}:void 0}(),Jn=function(Z,y){var H=Z[y];Ji(H)&&(Z[y]=new Jp(H))};Ym(up,{_25Q:function(k){k._16O=function(k,L,Z,W,F,_,a,c,V){var d=this.info;if(d){var D,h,s,t,j,I,p=Vp();if(an[L]?(D=d.p3,h=d.c1,s=d.c2):$e[L]?(h=d.s1||d.c1,s=d.s2||d.c2):(h=d.t1||d.c1,s=d.t2||d.c2),D)I=To(L,oh,Z,W,c,V);else{var l=Fl(s,h),N=nm(h,s);t=[0,-q(l[2],l[0]),X(l[1]/N)],j="zyx",D=[(h[0]+s[0])/2,(h[1]+s[1])/2,(h[2]+s[2])/2],I=To(L,[N,0,0],Z,W,c,V)}return Xf(p,I,D,W,F,_,a,k,t,j)}return Uj},k._80o=function(R,v,e){var O=this,A=O.shapeModel;if(A){var C=O.gv;Be(C),Gq(C,O.data,A,O.s,O.getBodyColor(),e)}},k.createLineModel=function(n,r,N,c,q){for(var d=this,P=d.s,M=Ag(n,r,P(ji)),u=[],A=0;A<M.length;A++){var j=M[A],v=j.length;if(v>1){var x=j[0];u.push(x.x,x.y,x.z);for(var g=1;v-1>g;g++)x=j[g],u.push(x.x,x.y,x.z),u.push(x.x,x.y,x.z);x=j[v-1],u.push(x.x,x.y,x.z)}}var Z,X,w,Y=P(c+".color"),W=P(q),E=0;return W&&(X=P(q+".color"),Z=P(q+".pattern"),E=P(q+".offset"),w=Y),Nc(d.gv,d.data,u,X||Y,w?f:P(c+".gradient.color"),N,Z||P(c+".pattern"),E,w),M},k.createTubeModel=function(Q,V,j,g){for(var d=this,B=d.s,y=B("shape3d.side")||hn,O=B("shape3d.start.angle"),M=B(lf)?B("shape3d.top.cap"):f,K=B(hk)?B("shape3d.bottom.cap"):f,h="flat"===M,$="flat"===K,F=h&&(g?g.uv:B("shape3d.top.image")),s=$&&(g?g.uv:B("shape3d.bottom.image")),L=g?g.uv:B("shape3d.image"),t={vs:[],uv:L?[]:f,top_vs:h?[]:f,top_uv:F?[]:f,bottom_vs:$?[]:f,bottom_uv:s?[]:f},W=Ag(Q,V,B(ji),j),C=0,z=W.length;z>C;C++)vr(t,W[C],B(_j),j,y,O,M,K);if(g){var N=[];tp(d.gv,d.data,t,B,d.getBodyColor(),[],g,N),N.length&&(ul(g.vs,N),ul(g.ns,ib(N)))}else d.shapeModel=t;return W},k.getCache=function(){var C=this.info;if(C){var P=C.list;if(P){var I=C.cache;return I||(I=C.cache=Ig(P)),I}}return f}}});var th=function(J,I){for(var w,H=0,D=J.length,o=0,U=D-1;U>=o;)if(H=T(o+(U-o)/2),w=J[H].length-I,0>w)o=H+1;else{if(!(w>0)){U=H;break}U=H-1}H=U;var b=J[H],z=b.point;return H===D-1||b.length===I||(z=(new Xm).subVectors(J[H+1].point,z).normalize().multiplyScalar(I-b.length).add(z)),{point:z,tangent:b.tangent}},Ig=function(W){for(var f,P,t=[],p=0,H=0;H<W.length;H++){for(var h=W[H],D=0;D<h.length;D++){f=h[D],P&&(p+=P.distanceTo(f));var w=new Xm,N=h[D+1];N?w.subVectors(N,f):P?w.subVectors(f,P):w.x=1,w.normalize(),t.push({point:f,length:p,tangent:w}),P=f}P=null}return t},Ag=function(b,l,V,S){V=V||rq;for(var g=[],C=0,F=b.size();F>C;C++){var c=b.get(C);g.push(new Xm(c.x,c.e||0,c.y))}El(l)&&(l=l._as);for(var D,X,y,u,J,E=[],d=0,h=0,j=l?l.length:F;j>h;h++)if(J=l?l[h]:0===h?1:2,1===J)E.push(y=[]),y.push(g[d++]);else if(2===J||5===J)if(u=2===J?g[d++]:y[0],S){var i=l?l[h+1]:j>h+1?2:f;if(2===i||5===i){var L=y[y.length-1],p=2===i?g[d]:y[0],a=(new Xm).subVectors(L,u),A=(new Xm).subVectors(p,u),n=a.length(),s=A.length();if(S>n/2&&S>s/2)y.push(u);else for(a.multiplyScalar(O(S,n/2)/n).add(u),A.multiplyScalar(O(S,s/2)/s).add(u),D=new gn(a,u,A).getPoints(V),X=0;V>=X;X++)y.push(D[X])}else y.push(u)}else y.push(u);else if(3===J)for(D=new gn(y[y.length-1],g[d++],g[d++]).getPoints(V),X=1;V>=X;X++)y.push(D[X]);else if(4===J)for(D=new _c(y[y.length-1],g[d++],g[d++],g[d++]).getPoints(V),X=1;V>=X;X++)y.push(D[X]);return E},ak=function(S){for(var $,_,j=new Xm,B=[],C=[],M=[],F=0,c=S.length;c>F;F++)$=S[F],_=S[F+1],_?j.subVectors(_,$):j.subVectors($,S[F-1]),B.push(j.normalize().clone());var n,G,h,Y,R=new Xm,o=new Xm,D=new to,s=1e-4,z=Number.MAX_VALUE,P=B[0],Q=C[0]=new Xm,e=M[0]=new Xm;for(G=J(P.x),h=J(P.y),Y=J(P.z),z>=G&&(z=G,R.set(1,0,0)),z>=h&&(z=h,R.set(0,1,0)),z>=Y&&R.set(0,0,1),o.crossVectors(P,R).normalize(),Q.crossVectors(P,o),e.crossVectors(P,Q),F=1;c>F;F++)C[F]=C[F-1].clone(),M[F]=M[F-1].clone(),o.crossVectors(B[F-1],B[F]),o.length()>s&&(o.normalize(),n=H(Wo(B[F-1].dot(B[F]),-1,1)),C[F].applyMatrix4(D.makeRotationAxis(o,n))),M[F].crossVectors(B[F],C[F]);return{B:M,T:B,N:C}},vr=function(){var c=function(s){for(var v=1;v<arguments.length;v++){var I=arguments[v];s.push(I.x,I.y,I.z)}},m=function(i){for(var u=1;u<arguments.length;u++){var l=arguments[u].uv;i.push(l[0],l[1])}},g=function(o,$,x,u,X){var p=-u*N(X),I=u*n(X);return new Xm(o.x+p*x.x+I*$.x,o.y+p*x.y+I*$.y,o.z+p*x.z+I*$.z)},t=function(e,y,i,W,l,b,M){for(var T=[],P=z/l,F=0;l>=F;F++){var k=g(e,y,i,W,F*P+b);T.push(k),M!=f&&(k.uv=[M,F/l])}return T},Q=function(M,m,o,f,u,X,k,r){var x=m?M.top_vs:M.bottom_vs;if(x)for(var b=m?M.top_uv:M.bottom_uv,Q=z/k,i=0;k>i;i++){var O,Y;m?(O=i*Q+r,Y=(i+1)*Q+r):(Y=i*Q+r,O=(i+1)*Q+r),c(x,g(o,f,u,X,O),g(o,f,u,X,Y),o),b&&b.push(.5-.5*N(O),.5-.5*n(O),.5-.5*N(Y),.5-.5*n(Y),.5,.5)}};return function(U,w,O,Y,v,R,o,H){var k=w.length;if(k>1){var S,I,J,i,T,r,x,N=ak(w),z=N.T,s=N.N,M=N.B;"flat"===o&&Q(U,!0,w[0],M[0],s[0],Y,v,R),"flat"===H&&Q(U,!1,w[k-1],M[k-1],s[k-1],Y,v,R);var E,Z,D=[],h=a(v/2),e="round"===o,G="round"===H,L=w[0],g=z[0],b=M[0],A=s[0],p=w[k-1],W=z[k-1],l=M[k-1],q=s[k-1];if(e){for(J=1;h>=J;J++)s.splice(0,0,A),M.splice(0,0,b),z.splice(0,0,g),Z=-J/h*Y,w.splice(0,0,g.clone().multiplyScalar(Z).add(L)),D[h-J]=F(Y*Y-Z*Z);k+=h}if(G){for(J=1;h>=J;J++)s.push(q),M.push(l),z.push(W),Z=J/h*Y,w.push(W.clone().multiplyScalar(Z).add(p)),D[w.length-1]=F(Y*Y-Z*Z);k+=h}if(U.uv){var u=0,n=0,y=[];for(O&&(u=O),T=w[0],y[0]=0,J=1;k>J;J++)i=w[J],y[J]=T.distanceTo(i),T=i,O||(u+=y[J]);for(E=[],J=0;k>J;J++)n+=y[J],E[J]=u?n/u:0}for(J=0;k>J;J++){if(i=w[J],r=M[J],x=s[J],S=t(i,r,x,D[J]===j?Y:D[J],v,R,E?E[J]:f),I)for(var V=0;v>V;V++){var C=I[V],X=I[V+1]||I[0],B=S[V],K=S[V+1]||S[0];c(U.vs,X,C,B,B,K,X),U.uv&&m(U.uv,X,C,B,B,K,X)}I=S}}}}();Ym(u,{getLineLength:function(d){return d[d.length-1].length},getLineOffset:function($,A){return th($,A)},getLineCacheInfo:function(t,E,z,h){return Ig(Ag(t,E,z,h))}});var As=y.Light=function(){Wn(As,this),this.s(Td,R[Td]),this.s(of,"sphere"),this.s3(20,20,20)};ol("Light",Ir,{_image:"light_icon",_icon:"light_icon",onStyleChanged:function(q,r,J){As.superClass.onStyleChanged.apply(this,arguments),q===Td&&this.s("shape3d.color",J)}});var Er=Zf.Graph3dView=function(M,Y){var I=this;I._attributes=Y||u.graph3dViewAttributes,I._25I={},I._view=Xb(1);var R=I._canvas=Pf(I._view);R.addEventListener("webglcontextlost",function($){$.preventDefault(),Nn(I),I._26I=f,I._1o.texture=f,I._35O=!0},!1),R.addEventListener("webglcontextrestored",function(){I._35O=!1,I.iv()},!1),I._34O={},I._33O={},I._wireframeIndexMap={},I._wireframeModelMap={},I._polylineIndexMap={},I._polylineIndexMap={},I._8O=[],I._7O=Vp(),I._6O=Vp(),I._1o=new Bm(I),I._30O=new io(I),I._31O=new mi(I),I._32O=new vm(I),I._33Q=!0,I._eye=el(u.graph3dViewEye),I._center=el(u.graph3dViewCenter),I._up=el(u.graph3dViewUp),I._lightChanged=!1,I._59O=[],I._spots=[],I._dirs=[],I.dm(M?M:new Db),I.setInteractors([new yg(I)])},Fb={fogDisabled:1,dashDisabled:1,batchColorDisabled:1,batchBlendDisabled:1,batchBrightnessDisabled:1};zi("Graph3dView",M,{ms_v:1,ms_tip:1,ms_gv:1,ms_dm:1,ms_lp:1,ms_fire:1,ms_sm:1,_51o:1,ms_ac:["devicePixelRatio","boundaries","moveStep","rotateStep","sizeEditableFunc","rotationEditableFunc","editableFunc","rotatable","zoomable","pannable","walkable","resettable","mouseRoamable",Tg,ic,"firstPersonMode",bp,"movableFunc","gridVisible","gridSize","gridGap","gridColor","originAxisVisible","centerAxisVisible","axisXColor","axisYColor","axisZColor","editSizeColor","rectSelectable","rectSelectBackground","headlightRange","headlightColor","headlightIntensity","headlightDisabled","ortho","orthoWidth","fovy","near","far",Xd,Hb,"up","aspect","fogDisabled","fogColor","fogNear","fogFar","dashDisabled","batchColorDisabled","batchBlendDisabled","batchBrightnessDisabled"],_editable:!1,_devicePixelRatio:j,_boundaries:j,_moveStep:u.graph3dViewMoveStep,_rotateStep:u.graph3dViewRotateStep,_pannable:u.graph3dViewPannable,_rotatable:u.graph3dViewRotatable,_walkable:u.graph3dViewWalkable,_resettable:u.graph3dViewResettable,_zoomable:u.graph3dViewZoomable,_firstPersonMode:u.graph3dViewFirstPersonMode,_mouseRoamable:u.graph3dViewMouseRoamable,_gridVisible:u.graph3dViewGridVisible,_gridSize:u.graph3dViewGridSize,_gridGap:u.graph3dViewGridGap,_gridColor:u.graph3dViewGridColor,_originAxisVisible:u.graph3dViewOriginAxisVisible,_centerAxisVisible:u.graph3dViewCenterAxisVisible,_axisXColor:u.graph3dViewAxisXColor,_axisYColor:u.graph3dViewAxisYColor,_axisZColor:u.graph3dViewAxisZColor,_ortho:u.graph3dViewOrtho,_orthoWidth:u.graph3dViewOrthoWidth,_fovy:u.graph3dViewFovy,_near:u.graph3dViewNear,_far:u.graph3dViewFar,_headlightColor:u.graph3dViewHeadlightColor,_headlightIntensity:u.graph3dViewHeadlightIntensity,_headlightRange:u.graph3dViewHeadlightRange,_headlightDisabled:u.graph3dViewHeadlightDisabled,_rectSelectable:u.graph3dViewRectSelectable,_rectSelectBackground:u.graph3dViewRectSelectBackground,_editSizeColor:u.graph3dViewEditSizeColor,_autoMakeVisible:or,_fogDisabled:u.graph3dViewFogDisabled,_fogColor:u.graph3dViewFogColor,_fogNear:u.graph3dViewFogNear,_fogFar:u.graph3dViewFogFar,_dashDisabled:u.graph3dViewDashDisabled,_batchColorDisabled:u.graph3dViewBatchColorDisabled,_batchBlendDisabled:u.graph3dViewBatchBlendDisabled,_batchBrightnessDisabled:u.graph3dViewBatchBrightnessDisabled,setEye:function(d,u,B){1===arguments.length&&(u=d[1],B=d[2],d=d[0]);var F=this._eye;F[0]=d,F[1]=u,F[2]=B,this.fp(Xd,f,F)},setCenter:function(K,R,$){1===arguments.length&&(R=K[1],$=K[2],K=K[0]);var y=this._center;y[0]=K,y[1]=R,y[2]=$,this.fp(Hb,f,y)},setUp:function(d,Z,U){1===arguments.length&&(Z=d[1],U=d[2],d=d[0]);var E=this._up;E[0]=d,E[1]=Z,E[2]=U,this.fp("up",f,E)},isTransparentMask:function(J){return J.s("transparent.mask")},getAspect:function(){var r=this,T=r._aspect;return T?T:r.getWidth()/r.getHeight()},getCanvas:function(){return this._canvas},setDataModel:function(W){var K=this,z=K._dataModel,U=K._3o;z!==W&&(z&&(z.umm(K.handleDataModelChange,K),z.umd(K.handleDataPropertyChange,K),U||z.sm().ums(K._16o,K)),K._dataModel=W,W.mm(K.handleDataModelChange,K),W.md(K.handleDataPropertyChange,K),U?U._21I(W):W.sm().ms(K._16o,K),K.invalidateAll(!0),K.invalidateLight(),K.fp(ar,z,W))},handleDataPropertyChange:function(Z){var l=Z.data;this.invalidateData(l),Zg(l)&&"s:light.type"===Z.property&&this.invalidateLight()},invalidateLight:function(){this._lightChanged||(this._lightChanged=!0,this.iv())},onPropertyChanged:function(i){var J=this,E=i.property;J.iv(),J._18Q=f,"eye"===E?J._33Q=!0:"devicePixelRatio"===E?J._42(f,J._devicePixelRatio||ql):Fb[E]&&Nn(J)},_5O:function(a){var P=a._22Q();return P?new P(this,a):f},getData3dUI:function(l){var o=this._25I[l._id];return o===j&&(o=this._5O(l),this._25I[l._id]=o),o},invalidateAll:function(h){var n=this;if(h){for(var z in n._25I){var $=n._25I[z];$&&$.dispose()}n._25I={},n.iv(),Or(n)}else n.dm().each(function(j){n.invalidateData(j)})},invalidateSelection:function(){var H=this;H.sm().each(function(M){H.invalidateData(M)})},invalidateData:function(r){var B=this,o=B.getData3dUI(r);o&&(o.iv(),B.iv()),Or(B,r)},invalidateBatch:function(G){var p=this,f=p._33O,r=f[G];r&&(r.ds.forEach(function(j){var U=p.getData3dUI(j);U&&U.iv(),delete p._34O[j._id]}),delete f[G],p.iv())},handleDataModelChange:function($){var R=this,k=$.kind,q=$.data;if("add"===k)R.invalidateData(q),oo(q)&&q.getEdgeGroup()&&q.getEdgeGroup().eachSiblingEdge(R.invalidateData,R),Zg(q)&&R.invalidateLight();else if(k===Yi){var M=q._id,l=R._25I[M];l&&(l.dispose(),delete R._25I[M],R.iv()),q===R._currentSubGraph&&R.setCurrentSubGraph(f),Or(R,q),Zg(q)&&R.invalidateLight()}else k===on&&(R.invalidateAll(!0),R.setCurrentSubGraph(f),Or(R),R.invalidateLight())},toCanvas:function(m){var G=this,e=G.getGL();if(G.validate(),m){var A=e.getParameter(e.COLOR_CLEAR_VALUE);Iq(e,m)}G._42(f,1);var P=G.getWidth(),$=G.getHeight(),N=new Uint8Array(4*P*$),b=Pf(),U=b.getContext("2d");e.readPixels(0,0,P,$,e.RGBA,e.UNSIGNED_BYTE,N),Gr(b,P,$,1);for(var _=U.getImageData(0,0,P,$),D=_.data,l=0;l<D.length;l+=4){var W=l/4,t=T(W/P),X=W-t*P;W=4*(($-1-t)*P+X),D[W]=N[l],D[W+1]=N[l+1],D[W+2]=N[l+2],D[W+3]=N[l+3]}return U.putImageData(_,0,0),m&&Iq(e,A),G._42(f,G._devicePixelRatio||ql),b},toDataURL:function(Y,U){var W=this,x=W.getGL();if(W.validate(),Y){var z=x.getParameter(x.COLOR_CLEAR_VALUE);Iq(x,Y)}W._42(f,1);var n=W._canvas.toDataURL(U||"image/png",1);return Y&&Iq(x,z),W._42(f,W._devicePixelRatio||ql),n},getGL:function(){var U=this,Z=U._26I,C=U._prg;if(!Z)try{U._2O={},Z=U._26I=U._canvas.getContext("webgl",U._attributes)||U._canvas.getContext("experimental-webgl",U._attributes),U._buffer={vs:Hp(Z),ns:Hp(Z),is:Hp(Z),uv:Hp(Z),batchColor:Hp(Z),batchBlend:Hp(Z),batchBrightness:Hp(Z),lineDistance:Hp(Z)},U._1O=Sh(Z);var v=U._cube={vs:Hp(Z),ns:Hp(Z),is:Hp(Z),uv:Hp(Z)};Ih(Z,v.vs,ud),Ih(Z,v.ns,cr),Ih(Z,v.uv,jr),zp(Z,v.is,rp)}catch(u){}if(Z&&!C){if(C=U._prg=Z.createProgram(),!C)return f;var Y=U._dirs.length,S=U._spots.length,M=U._59O.length,s=["uPMatrix","uMVMatrix","uNMatrix","uViewMatrix","aNormal","aUv","uOffsetScale","uDiffuse","uBlend","uBlendColor","uBrightness","uPartOpacity","uTransparent","uTexture","uSampler","uDiscardSelectable","uFix","uPick","uReverseFlip","uFixPickReverseColor","uBatchBrightness","aBatchBrightness","uBatchColor","aBatchColor","uBatchBlend","aBatchBlend","uDash","aLineDistance","uDashDistance","uDashGapDistance","uLight","uHeadlightRange","uHeadlightColor","uFogColor","uFogNear","uFogFar"];Y&&s.push("uDirColor","uDirPosition"),S&&s.push("uSpotColor","uSpotRange","uSpotAngle","uSpotExponent","uSpotPosition","uSpotCenter"),M&&s.push("uPointColor","uPointRange","uPointPosition"),Iq(Z,[0,0,0,0]),Z.clearDepth(1),Z.enable(Z.DEPTH_TEST),Z.depthFunc(Z.LEQUAL),Z.enable(Z.BLEND),Z.blendFunc(Z.SRC_ALPHA,Z.ONE_MINUS_SRC_ALPHA),yk||(ce=zq(ce.substring(0,ce.indexOf("^#"))),Sp=zq(Sp),yk=!0);var y=["#define MAX_DIR "+Y,"#define MAX_SPOT "+S,"#define MAX_POINT "+M,U._fogDisabled?"":"#define FOG",U._dashDisabled?"":"#define DASH",U._batchColorDisabled?"":"#define BATCHCOLOR",U._batchBlendDisabled?"":"#define BATCHBLEND",U._batchBrightnessDisabled?"":"#define BATCHBRIGHTNESS",""].join("\n"),A=ko(Z,C,Z.VERTEX_SHADER,ce.replace("PREFIX",y)),q=ko(Z,C,Z.FRAGMENT_SHADER,Sp.replace("PREFIX",y));h?s.push("aPosition"):(C.aPosition=0,Z.bindAttribLocation(C,0,"aPosition")),Z.linkProgram(C),s.forEach(function(b){C[b]=/^u/.test(b)?Z.getUniformLocation(C,b):Z.getAttribLocation(C,b)}),Z.useProgram(C),Z.deleteShader(A),Z.deleteShader(q)}return Z},getBrightness:function(Q){var x=Q.s("brightness"),e=this.isSelected(Q)?Q.s("select.brightness"):f;return x==f?e:e==f?x:x*e},getWireframe:function(x){var C=x.s("wf.visible");return C===!0||"selected"===C&&this.isSelected(x)?{color:x.s("wf.color"),width:x.s("wf.width"),"short":x.s("wf.short"),mat:x.s("wf.mat")}:void 0},getBodyColor:function(j){return j.s("body.color")},getMat:function(E){return E.getMat?E.getMat():E.s("mat")},getFaceMat:function(N,y){return N.getFaceMat?N.getFaceMat(y):N.s(y+".mat")},getFaceBlend:function(P,n){return P.s(n+".blend")||P.s("all.blend")},getFaceColor:function(E,u){return E.s(u+".color")||E.s("all.color")},getFaceImage:function(n,X){return n.s(X+".image")||n.s("all.image")},getFaceDiscardSelectable:function(K,y){var J=K.s(y+".discard.selectable");return J==f?K.s("all.discard.selectable"):J},getFaceUv:function(U,S){return U.s(S+".uv")||U.s("all.uv")},getFaceUvOffset:function(n,L){return n.s(L+".uv.offset")||n.s("all.uv.offset")},getFaceUvScale:function(i,z){return i.s(z+".uv.scale")||i.s("all.uv.scale")},getFaceLight:function(H,I){var h=H.s(I+".light");return h==f?H.s("all.light"):h},getFaceVisible:function(T,C){var U=T.s(C+".visible");return U==f?T.s("all.visible"):U},getFaceOpacity:function(j,R){var g=j.s(R+".opacity");return g==f?j.s("all.opacity"):g},getFaceTransparent:function(z,O){var v=z.s(O+".transparent");return v==f?z.s("all.transparent"):v},getFaceReverseColor:function($,j){return $.s(j+".reverse.color")||$.s("all.reverse.color")},getFaceReverseFlip:function(q,e){var n=q.s(e+".reverse.flip");return n==f?q.s("all.reverse.flip"):n},getFaceReverseCull:function(K,J){var Q=K.s(J+".reverse.cull");return Q==f?K.s("all.reverse.cull"):Q},getTextureMap:function(){return this._2O},deleteTexture:function($){var y=this,o=y._2O[$];o&&(delete y._2O[$],y.getGL().deleteTexture(o))},getTexture:function(x,o){if(!x)return f;var R=this,B=R.getGL(),W=R._2O[x];if(!W){var O=cj(x);if(O)if(O.tagName){if(O.dynamic)return Sh(B,O,R._1O);W=R._2O[x]=Sh(B,O)}else{var a=eq(O,o),k=Oo(O,o);if(a>=1&&k>=1){var K=Ij(a,k);return tb(K,O,0,0,a,k,o,R),K.restore(),Sh(B,se,R._1O)}}}return W},redraw:function(){this.iv()},validateImpl:function(){var p=this;if(p._lightChanged){p._lightChanged=!1;var Q=[],S=[],e=[];p.dm().each(function(H){if(Zg(H)){var M=H.s("light.type");M===Vc?Q.push(H):M===$k?S.push(H):M===Kj&&e.push(H)}}),(Q.length!==p._59O.length||S.length!==p._spots.length||e.length!==p._dirs.length)&&Nn(p),p._59O=Q,p._spots=S,p._dirs=e}p._42(),p._1o.iv()},_42:function(I,p){var k=this;if(!k._35O&&(!Eh||Yj(Eh))){var i,$;I&&(I instanceof Ir?$=I:i=I);var P=k._canvas,r=k.getWidth(),L=k.getHeight(),A=k.getGL(),X=k._prg;if(A){if(p?I||Gr(P,r,L,p):(p=k._devicePixelRatio||ql,I||r===P.clientWidth&&L===P.clientHeight||Gr(P,r,L,p)),A.viewport(0,0,r*p,L*p),k._81O=f,A.clear(A.COLOR_BUFFER_BIT|A.DEPTH_BUFFER_BIT),ap(A,X.uBrightness,1),ap(A,X.uOpacity,1),ap(A,X.uPartOpacity,1),A._picking=!!I,ap(A,X.uPick,!!I),ap(A,X.uTexture,!1),ap(A,X.uFix,!1),ap(A,X.uTransparent,!1),ap(A,X.uBatchColor,!1),ap(A,X.uBatchBlend,!1),ap(A,X.uBatchBrightness,!1),I||(Cj(k,A,X),k._fogDisabled||(ap(A,X.uFogColor,k._fogColor),ap(A,X.uFogNear,k._fogNear),ap(A,X.uFogFar,k._fogFar))),Ld(A,X.aPosition),Ld(A,X.aNormal),Ob(A,X.aUv),Ob(A,X.aBatchColor),Ob(A,X.aBatchBlend),Ob(A,X.aBatchBrightness),Ob(A,X.aLineDistance),ap(A,X.uPMatrix,Jb(k)),ap(A,X.uViewMatrix,bi(k,k._7O)),$){var c=A.getParameter(A.COLOR_CLEAR_VALUE);A.clearColor(0,0,0,0),A.disable(A.BLEND),Ob(A,X.aNormal),Ih(A,k._cube.vs,f,X.aPosition),zp(A,k._cube.is),Ye($,k,S,0),Ye($,k,Q,6),Ye($,k,_,12),Ye($,k,o,18),Ye($,k,xi,24),Ye($,k,Kh,30),Ld(A,X.aNormal),Iq(A,c)}else if(i){i.value=2;var c=A.getParameter(A.COLOR_CLEAR_VALUE);A.clearColor(0,0,0,0),A.disable(A.BLEND),k._95I(A,X,i),Km(k,i),jb(k,k._polylineModelMap,i),jb(k,k._wireframeModelMap,i),ap(A,X.uTransparent,!0),k._95I(A,X,i,!0),Km(k,i,!0),jb(k,k._polylineModelMap,i,!0),jb(k,k._wireframeModelMap,i,!0),ap(A,X.uTransparent,!1),A.disable(A.DEPTH_TEST),k._30O._42(A,X,i),A.enable(A.DEPTH_TEST),Iq(A,c)}else A.disable(A.BLEND),k._31O._42(A,X),k._95I(A,X),Km(k),jb(k,k._polylineModelMap),jb(k,k._wireframeModelMap),A.enable(A.BLEND),A.depthMask(!1),ap(A,X.uTransparent,!0),k._95I(A,X,f,!0),Km(k,f,!0),jb(k,k._polylineModelMap,f,!0),jb(k,k._wireframeModelMap,f,!0),ap(A,X.uTransparent,!1),A.depthMask(!0),A.disable(A.BLEND),A.disable(A.DEPTH_TEST),k._32O._42(A,X),k._30O._42(A,X),A.enable(A.DEPTH_TEST);Ih(A,f),zp(A,f),k._33Q=!1}}},_95I:function(N,j,H,P){var h=this,y=function(J){return P?J:!J};h.dm().each(function(f){if(h.isVisible(f)){if(!H&&h.isTransparentMask(f))return;var L=h.getData3dUI(f);L&&L._42(N,j,H,y)}})},getLogicalPoint:function(K){return Dn(K,this._canvas)},getHitFaceInfo:function(v){v.target&&(v=this.lp(v));var n=this.getDataInfoAt(v);if(n){var q=this._1o.face(n.data,Io(v.x,v.y));if(q)return{data:n.data,face:q}}return f},getDataAt:function(O){var k=this.getDataInfoAt(O);return k?k.data:f},getDataInfoAt:function(Y,S){return Y.target&&(Y=this.lp(Y)),this._1o.get(Io(Y.x,Y.y,S),!0)},getDatasInRect:function(P){return this._1o.get(P)},setEditable:function(N){var F=this,x=F._editable;F._editable=N,this.fp(Xq,x,N)},isEditable:function(J){var V=this;return V._editable?Qr(J)?J.s("3d.editable")?V._editableFunc?V._editableFunc(J):!0:!1:!1:!1},isSelectable:function(u){return u.s("3d.selectable")&&this.sm().isSelectable(u)},isMovable:function(U){var N=this;return oo(U)&&U.getStyle(hq)!==Tq?!1:U.s("3d.movable")?N._movableFunc?N._movableFunc(U):!0:!1},isSizeEditable:function(Q){return Qr(Q)?this._sizeEditableFunc?this._sizeEditableFunc(Q):!0:!1},isRotationEditable:function(B){return Qr(B)&&B.IRotatable!==!1?this._rotationEditableFunc?this._rotationEditableFunc(B):!0:!1},handleDelete:function(){this._editable&&this.removeSelection()},zoomIn:function(A){this.setZoom(wo,A)},zoomOut:function(T){this.setZoom(1/wo,T)},setZoom:function(W,F){if(1!==W){var Q=this;if(Q._ortho)return Q.setOrthoZoom(W,F),void 0;Q._14o&&Q._14o.stop(!0);var w=1/W,K=Q._eye,V=Q._center,b=V[0]+(K[0]-V[0])*w-K[0],p=V[1]+(K[1]-V[1])*w-K[1],H=V[2]+(K[2]-V[2])*w-K[2];if(!(nm(K,V)<Q._moveStep&&1>w)){if(F=Bq(F)){var S=el(K);return F.action=function(u){Q.fi({kind:Q._zooming?"betweenZoom":"beginZoom"}),Q._zooming=1,K[0]=S[0]+b*u,K[1]=S[1]+p*u,K[2]=S[2]+H*u,Q.fp(Xd,f,K)},F._37o=function(){delete Q._14o,delete Q._zooming,Q.fi({kind:"endZoom"}),Q.onZoomEnded()},Q._14o=iq(F)}K[0]+=b,K[1]+=p,K[2]+=H,Q.fp(Xd,f,K)}}},setOrthoZoom:function(q,_){if(1!==q){var P=this;P._14o&&P._14o.stop(!0);var k=P._orthoWidth,j=k/q-k;if(!(k<P._moveStep&&q>1))return(_=Bq(_))?(_.action=function(s){P.fi({kind:P._zooming?"betweenZoom":"beginZoom"}),P._zooming=1,P.setOrthoWidth(k+j*s)},_._37o=function(){delete P._14o,delete P._zooming,P.fi({kind:"endZoom"}),P.onZoomEnded()},P._14o=iq(_)):(P.setOrthoWidth(k/q),void 0)}},getPositionInfo:function(B){var L=this,d=L._eye,n=L._center,a=L.getAspect(),c=B?kl(Fl(n,d,!0),Fl(B,d)):nm(d,n);if(L._ortho){var l=L._orthoWidth;return{length:c,height:l/a,width:l}}var O=2*i(L._fovy/2)*c;return{length:c,height:O,width:O*a}},getCenterInfo:function(){var y=this;return y._81O||(y._81O=y.getPositionInfo()),el(y._81O)},rotate:function(u,o,j,X){var t=this;if(u||o){X==f&&(X=t._firstPersonMode),t._88O&&t._88O.stop(!0);var M=t._center,T=t._eye,R=t.getCenterInfo().length,d=X?M:T,v=X?T:M,x=Fl(d,v),G=q(x[0],x[2]),C=q(F(x[0]*x[0]+x[2]*x[2]),x[1]),z=X?Hb:Xd;return X&&(o=-o),(j=Bq(j))?(j.action=function(e){t.fi({kind:t._rotating?"betweenRotate":"beginRotate"}),t._rotating=1;var V=G+u*e,T=C+o*e;T=lb(T),x[0]=R*n(T)*n(V),x[1]=R*N(T),x[2]=R*n(T)*N(V),d[0]=v[0]+x[0],d[1]=v[1]+x[1],d[2]=v[2]+x[2],t.fp(z,f,d)},j._37o=function(){delete t._88O,delete t._rotating,t.fi({kind:"endRotate"}),t.onRotateEnded()},t._88O=iq(j)):(G+=u,C+=o,C=lb(C),x[0]=R*n(C)*n(G),x[1]=R*N(C),x[2]=R*n(C)*N(G),d[0]=v[0]+x[0],d[1]=v[1]+x[1],d[2]=v[2]+x[2],t.fp(z,f,d),void 0)}},pan:function(V,u,e,C){if(V||u){var v=this;C==f&&(C=v._firstPersonMode),v._89O&&v._89O.stop(!0);var E=bi(v),X=[E[0]*V,E[4]*V,E[8]*V],j=[E[1]*u,E[5]*u,E[9]*u],a=X[0]+j[0],g=X[1]+j[1],h=X[2]+j[2],i=v._center,R=v._eye;if(C){var w=tm(v.getBoundaries(),R[0],R[2],R[0]+a,R[2]+h,F(a*a+h*h));a=w[0],h=w[1]}if(e=Bq(e)){var H=el(R),M=el(i);return e.action=function(e){v.fi({kind:v._panning?"betweenPan":"beginPan"}),v._panning=1,i[0]=M[0]+a*e,i[1]=M[1]+g*e,i[2]=M[2]+h*e,R[0]=H[0]+a*e,R[1]=H[1]+g*e,R[2]=H[2]+h*e,v.fp(Xd,f,R),v.fp(Hb,f,i)},e._37o=function(){delete v._89O,delete v._panning,v.fi({kind:"endPan"}),v.onPanEnded()},v._89O=iq(e)}i[0]+=a,i[1]+=g,i[2]+=h,R[0]+=a,R[1]+=g,R[2]+=h,v.fp(Xd,f,R),v.fp(Hb,f,i)}},walk:function(I,r,w){if(I){var v=this;w==f&&(w=v._firstPersonMode),v._90O&&v._90O.stop(!0);var q=v._center,T=v._eye,s=Fl(q,T,!0);if(w){var $=tm(v.getBoundaries(),T[0],T[2],T[0]+s[0]*I,T[2]+s[2]*I,J(I));if(I=nm($),!I)return;s=[$[0]/I,0,$[1]/I]}if(r=Bq(r)){var _=el(T),A=el(q);return r.action=function(o){v.fi({kind:v._walking?"betweenWalk":"beginWalk"}),v._walking=1;var F=I*o;T[0]=_[0]+s[0]*F,T[1]=_[1]+s[1]*F,T[2]=_[2]+s[2]*F,q[0]=A[0]+s[0]*F,q[1]=A[1]+s[1]*F,q[2]=A[2]+s[2]*F,v.fp(Xd,f,T),v.fp(Hb,f,q)},r._37o=function(){delete v._90O,delete v._walking,v.fi({kind:"endWalk"}),v.onWalkEnded()},v._90O=iq(r)}T[0]+=s[0]*I,T[1]+=s[1]*I,T[2]+=s[2]*I,q[0]+=s[0]*I,q[1]+=s[1]*I,q[2]+=s[2]*I,v.fp(Xd,f,T),v.fp(Hb,f,q)}},handleScroll:function(z,c){z.preventDefault();var D=this,O=D._moveStep;D.isFirstPersonMode()?D.isPannable()&&D.pan(0,c>0?O:-O):D.isZoomable()&&D.setZoom(0>c?1/gc:gc)},handlePinch:function(d,K){this.isZoomable()&&this.setZoom(K>d?1/wf:wf)},reset:function(){this.setCenter(u.graph3dViewCenter),this.setEye(u.graph3dViewEye),this.setUp(u.graph3dViewUp)},moveSelection:function(D,C,V){var M,B=this,o=B.dm();o&&(M=o.getHistoryManager()),M&&M.beginInteraction(),bh(this.sm().toSelection(this.isMovable,this),D,C,V),M&&M.endInteraction()},getMoveMode:function(N,X){var A=X.s("3d.move.mode");if(A)return A;var Z="88",I="89",J="90";return Cm(N)||If[Z]&&If[I]&&If[J]?"xyz":If[Z]&&If[I]?"xy":If[Z]&&If[J]?"xz":If[I]&&If[J]?"yz":If[Z]?"x":If[I]?"y":If[J]?"z":"xz"},handleTick:function(){var U=this,N=!1,w=U._moveStep,x=w,W=!1,S=!0,q=U._rotateStep*(S?-1:1);if(U.isWalkable()||(x=0),U.isPannable()||(w=0),U.isRotatable()||(q=0),U._32Q){var t=(vb()-U._32Q)/50;x*=t,w*=t,q*=t}U._31Q&&(yq(U._31Q),delete U._31Q,delete U._32Q),ij()&&(N=!0,U.pan(-w,0,W,S)),Zo()&&(N=!0,U.pan(w,0,W,S)),si()&&(N=!0,Cm()?U.pan(0,w,W,S):U.walk(x,W,S)),ac()&&(N=!0,Cm()?U.pan(0,-w,W,S):U.walk(-x,W,S)),qn()&&(N=!0,U.rotate(-q,0,W,S)),zk()&&(N=!0,U.rotate(q,0,W,S)),so()&&(N=!0,U.rotate(0,-q/2,W,S)),uq()&&(N=!0,U.rotate(0,q/2,W,S)),N&&(U._32Q=vb(),U._31Q=Dp(U.handleTick,U))},handleKeyDown:function(c){var k=this;!Sq(c)&&Zj[c.keyCode]?k.handleTick():ui(c)?k.selectAll():un(c)?k.handleDelete(c):td(c)&&this.isResettable()&&k.reset()},checkDoubleClickOnNote:function(U,Z,Q){var P=this;if(Q===Rg){if(Z.s("note.toggleable"))return Z.s(Id,!Z.s(Id)),P.fi({kind:"toggleNote",event:U,data:Z,part:Q}),!0}else if(Q===Mi&&Z.s("note2.toggleable"))return Z.s(_r,!Z.s(_r)),P.fi({kind:"toggleNote2",event:U,data:Z,part:Q}),!0;return!1},checkDoubleClickOnRotation:function(Z,g,W){return W===pd?(g.setRotationX(0),!0):W===Nj?(g.setRotationY(0),!0):W===Ff?(g.setRotationZ(0),!0):!1},onRotateEnded:function(){},onWalkEnded:function(){},toViewPosition:function(X){var e=this,r=e.getWidth()/2,u=e.getHeight()/2,b=e._18Q;return b||(b=Jb(e),e._18Q=gh(b,b,bi(e))),X=Ke([X[0],X[1],X[2],1],b),{x:r+r*X[0]/X[3],y:u-u*X[1]/X[3]}},getHitPosition:function(q,a,Z){var r=this,J=r.getWidth(),x=r.getHeight(),Y=q.target?r.lp(q):q,z=Y.x-J/2,P=Y.y-x/2,V=r.getCenterInfo(),W=bi(r);a=a?a:[0,0,0],Z=Z?Z:[0,1,0],z=z/J*V.width,P=-1*P/x*V.height;var e=[W[0]*z,W[4]*z,W[8]*z],g=[W[1]*P,W[5]*P,W[9]*P],L=[e[0]+g[0],e[1]+g[1],e[2]+g[2]],D=r.getCenter(),G=xq(r,a,Z,[L[0]+D[0],L[1]+D[1],L[2]+D[2]]);return G?G:[0,0,0]},getLineLength:function(D){this.validate();var O=this.getData3dUI(D);if(O&&O.getCache){var m=O.getCache();if(m)return m[m.length-1].length}return 0},getLineOffset:function(I,D){this.validate();var E=this.getData3dUI(I);if(E&&E.getCache){var s=E.getCache();if(s)return th(s,D)}return f}});var Pe=Zf.Interactor=function(I){this.gv=I};zi("Interactor",M,{ms_listener:1,getView:function(){return this.gv.getView()},setUp:function(){this.addListeners()},tearDown:function(){this.removeListeners()}});var yg=Zf.DefaultInteractor=function(O){Wn(yg,this,[O])};zi("DefaultInteractor",Pe,{handle_contextmenu:function(Q){np(Q)},handle_mousewheel:function(M){this.gv.handleScroll(M,M.wheelDelta/40)},handle_DOMMouseScroll:function(Y){2===Y.axis&&this.gv.handleScroll(Y,-Y.detail)},handle_keydown:function(p){this.gv.handleKeyDown(p)},handle_mousedown:function(H){this.handle_touchstart(H)},handle_touchstart:function(B){np(B);var P=this,I=P.gv,u=Fj(B),j=I.getDataInfoAt(B),Z=j?j.data:f,W=j?j.part:f,a=I.sm(),e=bo(B),E=nq(B);I.setFocus(B)&&(P._62O=f,P._57I=f,Z?Sq(B)?a.co(Z)?a.rs(Z):a.as(Z):a.co(Z)||a.ss(Z):u&&(Sq(B)?I.isRectSelectable()&&(P._62O=gb):P._57I=Ee(B)),P._31Q&&(yq(P._31Q),delete P._31Q,delete P._32Q),P._62O||(I.isFirstPersonMode()&&(Cm(B)||E>2?P._62O="pan":I.isMouseRoamable()||w?(P._62O="roaming",P.foward=e&&Fj(B),P._32Q=vb(),P._31Q=Dp(P.tick,P)):u||(P._62O="roaming")),P._62O||(u&&e&&Tm[W]?(P._62O=W,P.p3=Z.p3()):u&&e&&Qr(Z)&&I.isSelected(Z)&&I.isMovable(Z)?(P._62O="move",P.p3=Z.p3(),P.movedata=Z):w&&(E>2?P._62O="pan":2===E&&(P.dist=Ao(B),P._62O="pinch")))),P.point=I.lp(B),Mk(P,B),rs(B)?I.handleDoubleClick(B,Z,W):I.handleClick(B,Z,W))
},tick:function(){var f=this,F=f.gv,y=F._moveStep;f.point&&F.isWalkable()&&(f._32Q&&(y*=(vb()-f._32Q)/50),F.walk(f.foward?y:-y),f._32Q=vb(),f._31Q=Dp(f.tick,f))},handle_mouseup:function(c){this.handle_touchend(c)},handle_touchend:function(A){var H=this._57I;H&&(nm(H,Ee(A))<=1&&this.gv.sm().cs(),this._57I=f)},handleWindowMouseMove:function(e){this.handleWindowTouchMove(e)},handleWindowTouchMove:function($){var I,V,h=this,w=h.gv,Q=h._62O,p=h.point,e=w.dm(),R=e.getHistoryManager(),g=w.lp($),Y=g.x-p.x,T=g.y-p.y,B=-z*Y/w.getWidth(),A=-z*T/w.getHeight();if("roaming"===Q)h.rotate($,B/2,A/2);else if(Q===pd||Q===Nj||Q===Ff)R&&!w._86O&&R.beginInteraction(),w.fi({kind:w._86O?"betweenEditRotation":"beginEditRotation",event:$}),w._86O=1,w.sm().each(function(r){if(Qr(r)&&w.isRotationEditable(r)){var V=J(B)>J(A)?B:A;Q===pd?r.setRotationX(r.getRotationX()+V):Q===Nj?r.setRotationY(r.getRotationY()+V):Q===Ff&&r.setRotationZ(r.getRotationZ()+V)}});else if("move"===Q||Tm[Q]){Q===Kk||Q===yh||Q===Yb?(R&&!w._87O&&R.beginInteraction(),w.fi({kind:w._87O?"betweenEditSize":"beginEditSize",event:$}),w._87O=1):(R&&!w._moving&&R.beginInteraction(),w.fi({kind:w._moving?"betweenMove":"beginMove",event:$}),w._moving=1);var M,j=h.p3,k=w.getPositionInfo(j),F=bi(w);M=Q===rn?"x":Q===tl?"y":Q===wb?"z":Q===Kk?"sx":Q===yh?"sy":Q===Yb?"sz":w.getMoveMode($,h.movedata),Y=Y/w.getWidth()*k.width,T=-1*T/w.getHeight()*k.height;var X,U,C=[F[0]*Y,F[4]*Y,F[8]*Y],y=[F[1]*T,F[5]*T,F[9]*T],E=C[0]+y[0],L=C[1]+y[1],P=C[2]+y[2],m=[j[0]+E,j[1]+L,j[2]+P],G=Fl(w.getEye(),m,!0);if("xyz"===M?(X=m,w.moveSelection(E,L,P)):"xz"===M?(X=xq(w,j,[0,1,0],m),X&&w.moveSelection(X[0]-j[0],0,X[2]-j[2])):"xy"===M?(X=xq(w,j,[0,0,1],m),X&&w.moveSelection(X[0]-j[0],X[1]-j[1],0)):"yz"===M?(X=xq(w,j,[1,0,0],m),X&&w.moveSelection(0,X[1]-j[1],X[2]-j[2])):"x"===M||"sx"===M?(G[0]=0,X=xq(w,j,G,m),X&&(U=X[0]-j[0],"x"===M?w.moveSelection(U,0,0):w.sm().each(function(e){Qr(e)&&w.isSizeEditable(e)&&(I=b(ne,e.getWidth()+U),V=I/e.getWidth(),e.setWidth(I),Cm($)&&(e.setHeight(e.getHeight()*V),e.setTall(e.getTall()*V)))}))):"y"===M||"sy"===M?(G[1]=0,X=xq(w,j,G,m),X&&(U=X[1]-j[1],"y"===M?w.moveSelection(0,U,0):w.sm().each(function(z){Qr(z)&&w.isSizeEditable(z)&&(I=b(ne,z.getTall()+U),V=I/z.getTall(),z.setTall(I),Cm($)&&(z.setHeight(z.getHeight()*V),z.setWidth(z.getWidth()*V)))}))):("z"===M||"sz"===M)&&(G[2]=0,X=xq(w,j,G,m),X&&(U=X[2]-j[2],"z"===M?w.moveSelection(0,0,U):w.sm().each(function(n){Qr(n)&&w.isSizeEditable(n)&&(I=b(ne,n.getHeight()+U),V=I/n.getHeight(),n.setHeight(I),Cm($)&&(n.setTall(n.getTall()*V),n.setWidth(n.getWidth()*V)))}))),!X)return;h.p3=X}else if(Q===gb){var H=h.div;H||(H=h.div=Xb(),nj(h.getView(),H),H.op=p,H.style.background=w.getRectSelectBackground()),w.fi({kind:w._rectSelecting?"betweenRectSelect":"beginRectSelect",event:$}),w._rectSelecting=1,H.rect=ai(H.op,g),tf(H,H.rect)}else if("pinch"===Q&&2===nq($)){w.fi({kind:w._pinching?"betweenPinch":"beginPinch",event:$}),w._pinching=1;var U=Ao($);w.handlePinch(U,h.dist),h.dist=U}else"pan"===Q||Cm($)?h.pan($,Y,T):w.isFirstPersonMode()||(Fj($)?h.rotate($,B,A):(h.pan($,Y,0),w.isWalkable()&&(w.fi({kind:w._walking?"betweenWalk":"beginWalk",event:$}),w._walking=1,w.walk(T/w.getHeight()*w.getCenterInfo().height))));h.point=g},pan:function(I,q,V){var O=this.gv;if(O.isPannable()){var u=O.getCenterInfo(),n=q/O.getWidth()*u.width,E=-1*V/O.getHeight()*u.height;O.fi({kind:O._panning?"betweenPan":"beginPan",event:I}),O._panning=1,O.pan(-n,-E)}},rotate:function(u,N,a){var s=this.gv;s.isRotatable()&&(s.fi({kind:s._rotating?"betweenRotate":"beginRotate",event:u}),s._rotating=1,s.rotate(N,a))},handleWindowMouseUp:function(J){this.handleWindowTouchEnd(J)},handleWindowTouchEnd:function(W){var H=this,F=H.gv,l=F.dm(),u=l.getHistoryManager(),o=H.div;if(o){var k=F.getDatasInRect(o.rect);if(!k.isEmpty()){var $=F.sm(),V=$.toSelection();k.each(function(H){$.co(H)?V.remove(H):V.add(H)}),$.ss(V)}vc(o)}F._moving&&(delete F._moving,F.fi({kind:"endMove",event:W}),F.onMoveEnded(),u&&u.endInteraction()),F._panning&&(delete F._panning,F.fi({kind:"endPan",event:W}),F.onPanEnded()),F._rotating&&(delete F._rotating,F.fi({kind:"endRotate",event:W}),F.onRotateEnded()),F._86O&&(delete F._86O,F.fi({kind:"endEditRotation",event:W}),u&&u.endInteraction()),F._87O&&(delete F._87O,F.fi({kind:"endEditSize",event:W}),u&&u.endInteraction()),F._pinching&&(delete F._pinching,F.fi({kind:"endPinch",event:W}),F.onPinchEnded()),F._rectSelecting&&(delete F._rectSelecting,F.fi({kind:"endRectSelect",event:W}),F.onRectSelectEnded()),F._walking&&(delete F._walking,F.fi({kind:"endWalk",event:W}),F.onWalkEnded()),H.dist=H.point=H._62O=H.p3=H.movedata=H.div=H._57I=H._32Q=H._31Q=H.foward=f}});var Bm=function(A){this.gv=A};gf(Bm,M,{_iv:!0,iv:function(){this._iv=!0},face:function(Y,U){var O=this,l=O.gv.getGL();if(!l||!Qr(Y))return f;var o=U.x,E=U.width,Q=U.height,r=O.height-U.y-Q,g=T(E/2),n=new Uint8Array(4*E*Q),W=0;for(O.iv(),O.validate(Y),Qi(l,O.frame),l.readPixels(o,r,E,Q,l.RGBA,l.UNSIGNED_BYTE,n),Qi(l,f),O.iv();g>=W;W++)for(var u=g-W,q=g+W,P=u;q>=P;P++)for(var h=u;q>=h;h++)if(P===u||P===q||h===u||h===q){var x=Ne.m[n[4*(P*E+h)]];if(x)return x}},get:function(F,C){this.validate();var U=this,o=U.gv.getGL();if(!o)return f;var E,v,H=F.x,A=F.width,c=F.height,Q=U.height-F.y-c,q=T(A/2),X=new Uint8Array(4*A*c),n=0,g=X.length,m=C?f:new Xc,e=C?f:{};if(Qi(o,U.frame),o.readPixels(H,Q,A,c,o.RGBA,o.UNSIGNED_BYTE,X),Qi(o,f),C){for(;q>=n;n++)for(var W=q-n,d=q+n,Y=W;d>=Y;Y++)for(var l=W;d>=l;l++)if((Y===W||Y===d||l===W||l===d)&&(E=U.info(X,4*(Y*A+l))))return E}else for(;g>n;n+=4)E=U.info(X,n),E&&(v=E.data,e[v._id]||(m.add(v),e[v._id]=v));return m},info:function(W,c){return this.colorMap[(W[c]<<24)+(W[c+1]<<16)+(W[c+2]<<8)+W[c+3]]},validate:function(r){var O=this,k=O.gv,T=k.getGL();if(T){var S=k.getWidth(),M=k.getHeight(),$=T.RGBA,L=T.TEXTURE_2D,Q=T.RENDERBUFFER,l=T.FRAMEBUFFER;O.texture||(O.texture=Sh(T),O.render=T.createRenderbuffer(),O.frame=T.createFramebuffer()),(O.width!==S||O.height!==M)&&(Eb(T,O.texture),T.texImage2D(L,0,$,S,M,0,$,T.UNSIGNED_BYTE,f),Cc(T,T.TEXTURE_MIN_FILTER,T.LINEAR),T.bindRenderbuffer(Q,O.render),T.renderbufferStorage(Q,T.DEPTH_COMPONENT16,S,M),Qi(T,O.frame),T.framebufferTexture2D(l,T.COLOR_ATTACHMENT0,L,O.texture,0),T.framebufferRenderbuffer(l,T.DEPTH_ATTACHMENT,Q,O.render),Eb(T,f),T.bindRenderbuffer(Q,f),Qi(T,f),O.width=S,O.height=M),O._iv&&(O._iv=!1,Qi(T,O.frame),k._42(r||(O.colorMap={}),1),Qi(T,f))}}});var mi=function(B){this.gv=B};gf(mi,M,{gap:0,size:0,_42:function(o,e){var u=this,V=u.gv,i=V._gridGap,j=V._gridSize;if(V._gridVisible){if(u.gap!==i||u.size!==j){for(var m=[],h=V._gridSize/2,T=i*h,g=0;2*h+1>g;g++){var $=6*g,K=6*(2*h+1)+$;m[$]=-T,m[$+1]=0,m[$+2]=-T+g*i,m[$+3]=T,m[$+4]=0,m[$+5]=-T+g*i,m[K]=-T+g*i,m[K+1]=0,m[K+2]=-T,m[K+3]=-T+g*i,m[K+4]=0,m[K+5]=T}u.vs=new Jp(m),u.gap=i,u.size=j}Be(V),mh(o,e),Nr(o,e,1,V._gridColor,V._buffer.vs,u.vs),$j(o,0,u.vs.length/3,o.LINES),Ap(o,e)}else u.vs=u.gap=u.size=f}});var vm=function(v){this.gv=v};gf(vm,M,{_42:function(o,A){var k=this.gv,j=k._buffer.vs,$=k._axisXColor,S=k._axisYColor,F=k._axisZColor,s=k._originAxisVisible,v=k._centerAxisVisible;if(s||v){var t=h?o.TRIANGLES:o.TRIANGLE_FAN,R=o.LINES;if(Be(k),mh(o,A),s){var d=k.getCenterInfo(),D=O(d.width,d.height)/5,N=.8*D,Q=.05*D;Nr(o,A,1.5,$,j,new Jp([0,0,0,D,0,0,N,Q,0,N,0,Q,N,-Q,0,N,0,-Q,N,Q,0,0,0,0,0,D,0,Q,N,0,0,N,Q,-Q,N,0,0,N,-Q,Q,N,0,0,0,0,0,0,D,Q,0,N,0,Q,N,-Q,0,N,0,-Q,N,Q,0,N])),$j(o,0,2,R),$j(o,1,6,t),Nr(o,A,f,S),$j(o,7,2,R),$j(o,8,6,t),Nr(o,A,f,F),$j(o,14,2,R),$j(o,15,6,t)}if(v){var P=k._center,w=P[0],r=P[1],K=P[2],d=k.getPositionInfo(P);D=O(d.width,d.height)/20,Nr(o,A,1.5,$,j,new Jp([w,r,K,w+D,r,K,w,r,K,w,r+D,K,w,r,K,w,r,K+D])),$j(o,0,2,R),Nr(o,A,f,S),$j(o,2,2,R),Nr(o,A,f,F),$j(o,4,2,R)}Ap(o,A)}}});var io=function(Q){this.gv=Q},rn="edit_tx",tl="edit_ty",wb="edit_tz",pd="edit_rx",Nj="edit_ry",Ff="edit_rz",Kk="edit_sx",yh="edit_sy",Yb="edit_sz",Tm={};Tm[rn]=1,Tm[tl]=1,Tm[wb]=1,Tm[pd]=1,Tm[Nj]=1,Tm[Ff]=1,Tm[Kk]=1,Tm[yh]=1,Tm[Yb]=1,gf(io,M,{_42:function(a,b,G){var h=this,f=h.gv,l=f.sm().ld();if(f.isEditable(l)&&Qr(l)&&(!f.isFirstPersonMode()||!f.isMouseRoamable()&&!w)){Be(f);var W,y,E=f.isMovable(l),Y=f.isRotationEditable(l),A=f.isSizeEditable(l),j=f._axisXColor,S=f._axisYColor,P=f._axisZColor,v=f._editSizeColor,u=f.getCenterInfo(),k=O(u.width,u.height)/10,Z=k/(G?5:10),z=.7*k,V=.4*z,i=l.p3(),o=i[0],F=i[1],$=i[2];W=[0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0],E&&h._17O(a,b,l,G,rn,j,W,[o,F-Z,$,o+z,F-Z,$,o+z,F,$,o+z,F,$,o,F,$,o,F-Z,$,o,F-Z,$+Z,o+z,F-Z,$+Z,o+z,F-Z,$,o+z,F-Z,$,o,F-Z,$,o,F-Z,$+Z]),y=o+z,A&&h._17O(a,b,l,G,Kk,v,W,[y,F-Z,$,y+V,F-Z,$,y+V,F,$,y+V,F,$,y,F,$,y,F-Z,$,y,F-Z,$+Z,y+V,F-Z,$+Z,y+V,F-Z,$,y+V,F-Z,$,y,F-Z,$,y,F-Z,$+Z]),y+=V,Y&&h._17O(a,b,l,G,pd,j,W,[y,F-Z,$,y+V,F-Z,$,y+V,F,$,y+V,F,$,y,F,$,y,F-Z,$,y,F-Z,$+Z,y+V,F-Z,$+Z,y+V,F-Z,$,y+V,F-Z,$,y,F-Z,$,y,F-Z,$+Z]),W=[0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0],E&&h._17O(a,b,l,G,tl,S,W,[o,F,$,o,F+z,$,o-Z,F+z,$,o-Z,F+z,$,o-Z,F,$,o,F,$,o,F,$,o,F,$-Z,o,F+z,$-Z,o,F+z,$-Z,o,F+z,$,o,F,$]),y=F+z,A&&h._17O(a,b,l,G,yh,v,W,[o,y,$,o,y+V,$,o-Z,y+V,$,o-Z,y+V,$,o-Z,y,$,o,y,$,o,y,$,o,y,$-Z,o,y+V,$-Z,o,y+V,$-Z,o,y+V,$,o,y,$]),y+=V,Y&&h._17O(a,b,l,G,Nj,S,W,[o,y,$,o,y+V,$,o-Z,y+V,$,o-Z,y+V,$,o-Z,y,$,o,y,$,o,y,$,o,y,$-Z,o,y+V,$-Z,o,y+V,$-Z,o,y+V,$,o,y,$]),W=[1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0],E&&h._17O(a,b,l,G,wb,P,W,[o,F,$,o,F,$+z,o,F-Z,$+z,o,F-Z,$+z,o,F-Z,$,o,F,$,o,F-Z,$,o-Z,F-Z,$,o-Z,F-Z,$+z,o-Z,F-Z,$+z,o,F-Z,$+z,o,F-Z,$]),y=$+z,A&&h._17O(a,b,l,G,Yb,v,W,[o,F,y,o,F,y+V,o,F-Z,y+V,o,F-Z,y+V,o,F-Z,y,o,F,y,o,F-Z,y,o-Z,F-Z,y,o-Z,F-Z,y+V,o-Z,F-Z,y+V,o,F-Z,y+V,o,F-Z,y]),y+=V,Y&&h._17O(a,b,l,G,Ff,P,W,[o,F,y,o,F,y+V,o,F-Z,y+V,o,F-Z,y+V,o,F-Z,y,o,F,y,o,F-Z,y,o-Z,F-Z,y,o-Z,F-Z,y+V,o-Z,F-Z,y+V,o,F-Z,y+V,o,F-Z,y])}},_17O:function(r,C,y,U,I,q,n,Z){var o=this.gv._buffer;Nf(r,C,U,!0,y,I),Tc(r,C,f,!0,f,!0,f,!1),ap(r,C.uDiffuse,q),Ih(r,o.vs,new Jp(Z),C.aPosition),Ih(r,o.ns,new Jp(n),C.aNormal),$j(r,0,12),Re(r,C)}});var jp=function(N,W){this.gv=N,this.s=function(i,s,Z){return s==f&&(s=Z),s==f?W.getStyle(i):Fe(s,W,N)},this.data=W};gf(jp,M,{I3d:!0,ms_icons:1,_iv:!0,iv:function(){this._iv=!0},_42:function(r,w,K,d){var W,H,V,v,P,T,b,k=this,s=k.gv,O=k.data,l=O._id,j=k.s("batch"),n=s._34O,B=s._33O,X=s.isSelectable(O),M=s.getBrightness(O),S=M!=f&&1!==M;if(k._iv){if(V=fq(n,B,O,j),k.validate(V,j?xk[j]||Ln:f),V&&(v=n[l],P=v.size=V.vs.length/3-v.begin,b=V.rs))for(M=S?M:1,T=0;P>T;T++)b.push(M);if(Qr(O)){var h=s.getWireframe(O);h&&Yc(s,O,h)}k.labelInfo=k.label2Info=k.noteInfo=k.note2Info=k._38o=f,k._24O(Jm,"getLabel"),k._24O(Ve,"getLabel2"),k._26O(Rg,"getNote"),k._26O(Mi,"getNote2"),k._15O(),k._iv=!1}S&&ap(r,w.uBrightness,M),H=Nf(r,w,K,X,O,ss),H&&(nr(H,n[l],B),nr(H,s._polylineIndexMap[l],s._polylineModelMap),nr(H,s._wireframeIndexMap[l],s._wireframeModelMap)),n[l]||k._80o(r,w,d),(W=k.labelInfo)&&(Nf(r,w,K,X,O,Jm),k._28O(W,Jm,d)),(W=k.label2Info)&&(Nf(r,w,K,X,O,Ve),k._28O(W,Ve,d)),(W=k.noteInfo)&&(Nf(r,w,K,X,O,Rg),k._29O(W,Rg,d)),(W=k.note2Info)&&(Nf(r,w,K,X,O,Mi),k._29O(W,Mi,d)),(W=k._38o)&&k._99O(r,w,W,K,X,d),S&&ap(r,w.uBrightness,1)},validate:function(){},_16O:function(){return Uj},_80o:function(){},dispose:function(){},getBodyColor:function(S){var F=this.data,e=this.gv.getBodyColor(F);return e?e:S?F.getStyle(S):f},_24O:function(y,d){var g=this,W=g.data,$=g.gv,D=g.s,A=$[d](W);if(A!=f){var L=D(y+".scale"),F=D(y+".max"),c=g[y+"Info"]={label:A,textureScale:D(y+".texture.scale"),color:$[d+"Color"](W),font:D(y+".font"),align:D(y+".align"),background:$[d+"Background"](W)},w=c.rect=Xl(c,A);F>0&&F<w.width&&(c.labelWidth=w.width,w.width=F),w.x=w.y=0,w.width*=L,w.height*=L,c.mat=g._16O(D(y+".autorotate"),D(y+".position"),w,D(y+".face"),D(y+".t3"),D(y+".r3"),D(y+".rotationMode"));var r=w.width/2,h=w.height/2;c.vs=new Jp([-r,h,0,-r,-h,0,r,-h,0,r,h,0]),w.width/=L,w.height/=L}},_26O:function(E,$){var w=this,U=w.data,S=w.gv,c=w.s,I=S[$](U);if(I!=f){var T=c(E+".scale"),P=this[E+"Info"]={note:I,textureScale:c(E+".texture.scale"),expanded:c(E+".expanded"),font:c(E+".font"),color:c(E+".color"),align:c(E+".align"),borderWidth:c(E+".border.width"),borderColor:c(E+".border.color"),background:S[$+"Background"](U)};if(P.expanded){var v=c(E+".max"),D=Xl(P,I);D.width+=6,D.height+=2,v>0&&v<D.width&&(P.labelWidth=D.width,D.width=v);var O={x:-D.width/2,y:-8-D.height,width:D.width,height:D.height+8}}else O={x:-6,y:-18,width:12,height:18};P.mat=w._16O(c(E+".autorotate"),c(E+".position"),f,c(E+".face"),c(E+".t3"),c(E+".r3"),c(E+".rotationMode")),P.rect=O,1!==T&&(O=el(O),O.x*=T,O.height*=T,O.y=-O.height,O.width*=T);var M=O.x,d=O.y,p=O.width,W=O.height;P.vs=new Jp([M,-d,0,M,-d-W,0,M+p,-d-W,0,M+p,-d,0])}},_28O:function(s,R,B){if(B(this.s(R+".transparent"))){var i=s.rect,J=s.textureScale,y=i.width*J,p=i.height*J;if(y>=1&&p>=1){var G=Ij(y,p);1!==J&&(G.translate(G,i.x,i.y),G.scale(J,J),G.translate(G,-i.x,-i.y)),xc(G,s),G.restore(),aj(this,R,s.mat,s.vs,!0)}}},_29O:function(y,n,o){if(o(this.s(n+".transparent"))){var w=y.rect,t=y.textureScale,b=w.x,r=w.y,V=w.width*t,l=w.height*t;if(V>=1&&l>=1){w.x=w.y=0;var e=Ij(V,l);1!==t&&e.scale(t,t),Jh(e,y),e.restore(),w.x=b,w.y=r,aj(this,n,y.mat,y.vs,!1)}}},_99O:function(C,w,H,F,j,$){if(H){var k=this,S=k.gv,n=k.data,q=S._buffer,c=S._1O,l=H.icons;for(var t in l){var v=l[t],y=H.rects[t];if(y&&$(Fe(v.transparent,n,S))){Nf(C,w,F,j,n,t);var R=Fe(v.shape3d,n,S),A=R?[R]:Fe(v.names,n,S),h=A?A.length:0,m=Fe(v.textureScale,n,S)||1,Z=Fe(v.light,n,S);Z==f&&(Z=R?!0:!1),Tc(C,w,Fe(v.blend,n,S),Z,Fe(v.opacity,n,S),Fe(v.reverseFlip,n,S),Fe(v.reverseColor,n,S),Fe(v.reverseCull,n,S));for(var W=0;h>W;W++){var T=A[W],J=y[W];if(sr(k,J.mat),R)Gq(S,n,$r(k.s,R),k.s);else{var K=cj(T);if(K){var d=J.width*m,N=J.height*m;if(d>=1&&N>=1){var o=Ij(d,N);Li(o,K,Fe(v.stretch,n,S),0,0,d,N,n,S),o.restore(),Sh(C,se,c),lm(C,w,c,Fe(v.discardSelectable,n,S),q.uv,Zh),Ih(C,q.vs,J.vs,w.aPosition),Ih(C,q.ns,Qn,w.aNormal),zp(C,q.is,fo),ke(C,0,fo.length),os(C,w,c)}}}lc(S)}Re(C,w)}}}}});var lo=function(F,C){Wn(lo,this,[F,C])};gf(lo,jp,{_16O:function(o,c,Q,m,h,d,q,M,u){var K=this.data,V=To(c,K.s3(),Q,m,M,u);return Xf(Vp(),V,K.p3(),m,h,d,q,o,K.r3(),K.getRotationMode())},clear:function(){var $=this;$.faceMat=$.mat=$.shapeModel=$.left=$.right=$.front=$.back=$.top=$.bottom=$.csg=f},validate:function(C,y){var n=this,i=n.gv,O=n.data,A=$r(n.s),v=Sj(O,i.getMat(O),A?n.s("shape3d.scaleable"):!0);if(n.clear(),C){var z=[];A?tp(i,O,A,n.s,n.getBodyColor(),[v],C,z):(il(i,O,C,y,v,S,z,Ar),il(i,O,C,y,v,_,z,Xi),il(i,O,C,y,v,xi,z,Mj),il(i,O,C,y,v,Kh,z,Ko),il(i,O,C,y,v,Q,z,Ro),il(i,O,C,y,v,o,z,hm)),z.length&&(ul(C.vs,z),ul(C.ns,ib(z)))}else n.mat=v,(n.shapeModel=A)||(n.vf(S),n.vf(_),n.vf(Q),n.vf(o),n.vf(xi),n.vf(Kh))},vf:function(C){var J=this;if(J.gv.getFaceVisible(J.data,C)){var k=Ql(J,C);return k.mat&&(J.faceMat=!0),k}},_80o:function(Y,z,s){var o=this,g=o.gv,N=o.data,W=g._cube,m=o.shapeModel;Be(g,o.mat),m?Gq(g,N,m,o.s,o.getBodyColor(),s):(Ih(Y,W.vs,f,z.aPosition),Ih(Y,W.ns,f,z.aNormal),zp(Y,W.is),o._18O(Y,z,o.left,0,s),o._18O(Y,z,o.front,6,s),o._18O(Y,z,o.right,12,s),o._18O(Y,z,o.back,18,s),o._18O(Y,z,o.top,24,s),o._18O(Y,z,o.bottom,30,s)),lc(g),o.faceMat&&(o._18O(Y,z,o.left,0,s,!0),o._18O(Y,z,o.front,6,s,!0),o._18O(Y,z,o.right,12,s,!0),o._18O(Y,z,o.back,18,s,!0),o._18O(Y,z,o.top,24,s,!0),o._18O(Y,z,o.bottom,30,s,!0))},_18O:function(E,m,R,A,q,Z){if(R){if(!q(R.transparent))return;if(Z&&!R.mat||!Z&&R.mat)return;Z&&Be(this.gv,R.mat);var g=this,b=g.data,Q=g.gv,u=Q.getTexture(R.texture,b),D=R.uv,k=R.uvScale,K=R.uvOffset,v=R.discardSelectable;if(u)if(D){for(var $=8*(A/6),Y=0;8>Y;Y++)Bb[$+Y]=D[Y];lm(E,m,u,v,Q._buffer.uv,Bb,K,k)}else lm(E,m,u,v,Q._cube.uv,f,K,k);Tc(E,m,R.blend,R.light,R.opacity,R.reverseFlip,R.reverseColor,R.reverseCull),ap(E,m.uDiffuse,R.color),ke(E,A,6),Re(E,m),os(E,m,u),Z&&lc(Q)}}});var Ll=function(S,M){Wn(Ll,this,[S,M])};gf(Ll,jp,{_25Q:1,validate:function(e){var y=this,N=y.gv,l=y.data,Y=y.s,H=Y("edge.width"),X=l._40I,h=l._41I;if(y.shapeModel=y.info=f,X&&h){var Z,T,B,I,d,S,L,i,j,Q,F=l.isLooped(),t=Y(hq),z=u.getEdgeType(t);if(z){var r=z(l,nl(y,N,l,F,t),N,y._19Q);if(!r.points||r.points.isEmpty())return;S=y.info={},L=r.segments,d=r.points,i=d.size();for(var P=X.getElevation(),D=h.getElevation(),b=0;i>b;b++){var G=d.get(b);G.e==f&&(G.e=P+(D-P)*b/(i-1))}j=Al(d.get(0)),Q=Al(d.get(i-1));var m=i%2;0===m?(S.c1=Al(d.get(i/2-1)),S.c2=Al(d.get(i/2))):S.p3=Al(d.get((i-m)/2)),S.s1=j,S.s2=Al(d.get(1)),S.t1=Al(d.get(i-2)),S.t2=Q}else{d=new Xc,S=y.info={};var p=Y("edge.offset"),v=Y("edge.center"),A=Y("edge.source.t3"),W=Y("edge.target.t3"),V=X.p3(),M=h.p3();if(A&&(V[0]+=A[0],V[1]+=A[1],V[2]+=A[2]),W&&(M[0]+=W[0],M[1]+=W[1],M[2]+=W[2]),t===Tq){L=Y("edge.segments");var k=S.points=Y(pg)||$c;if(i=k.size()){j=Al(k.get(0)),Q=Al(k.get(i-1)),!v&&p&&(B=Fl(j,V,!0),p=O(p,nm(V,j)),V=[V[0]+B[0]*p,V[1]+B[1]*p,V[2]+B[2]*p],B=Fl(M,Q,!0),p=O(p,nm(Q,M)),M=[M[0]-B[0]*p,M[1]-B[1]*p,M[2]-B[2]*p]);var m=i%2;0===m?(Z=Al(k.get(i/2-1)),T=Al(k.get(i/2))):S.p3=Al(k.get((i-m)/2)),S.s1=V,S.s2=j,S.t1=Q,S.t2=M}else!v&&p&&(B=Fl(M,V,!0),I=nm(V,M),p=Ii(p,I),V=[V[0]+B[0]*p,V[1]+B[1]*p,V[2]+B[2]*p],M=[M[0]-B[0]*p,M[1]-B[1]*p,M[2]-B[2]*p]),Z=V,T=M;d.add({x:V[0],y:V[2],e:V[1]}),d.addAll(k),d.add({x:M[0],y:M[2],e:M[1]})}else{var E=nl(y,N,l,F,t);if(y._19Q||(E=-E),F){var a=V[0],K=V[1],C=V[2],o=X.getTall()/2+E;Z=[a-E,K+o,C],T=[a+E,K+o,C],d.add({x:a-E,y:C,e:K}),d.add({x:a-E,y:C,e:K+o}),d.add({x:a+E,y:C,e:K+o}),d.add({x:a+E,y:C,e:K})}else{B=Fl(M,V,!0),I=nm(V,M);var x={x:V[0],y:V[2]},w={x:M[0],y:M[2]},g=Nm(f,x,w,E),U=g.x-x.x,s=g.y-x.y;p=Ii(p,I),B[0]*=p,B[1]*=p,B[2]*=p,Z=[V[0]+B[0]+U,V[1]+B[1],V[2]+B[2]+s],T=[M[0]-B[0]+U,M[1]-B[1],M[2]-B[2]+s],v?(d.add({x:V[0],y:V[2],e:V[1]}),d.add({x:Z[0],y:Z[2],e:Z[1]}),d.add({x:T[0],y:T[2],e:T[1]}),d.add({x:M[0],y:M[2],e:M[1]})):(d.add({x:Z[0],y:Z[2],e:Z[1]}),d.add({x:T[0],y:T[2],e:T[1]}))}}S.c1=Z,S.c2=T}S.list=Y(of)===We?y.createTubeModel(d,L,H/2,e):y.createLineModel(d,L,H,"edge","edge.dash")}}});var mm=function(P,A){Wn(mm,this,[P,A])};gf(mm,lo,{_80o:function(U,y,f){var Y=this,P=Y.gv,k=Y.shapeModel;Be(P,Y.mat),k?Gq(P,Y.data,k,Y.s,Y.getBodyColor(),f):(ls(Y,U,y,Y.left,f),ls(Y,U,y,Y.front,f),ls(Y,U,y,Y.right,f),ls(Y,U,y,Y.back,f),ls(Y,U,y,Y.top,f),ls(Y,U,y,Y.bottom,f)),lc(P)},validate:function(K,x){var I,T,c=this,y=c.s,q=c.data,g=q.p3(),E=q._thickness/2,l=q.isClosePath(),V=q.getPoints(),O=q.getSegments(),n=y(ji),$=K&&K.uv;if(c.clear(),0>E)T=c.shapeModel=Pj(V,O,y(lf),y(hk),n,y(_j),q.getTall(),q.getElevation(),l),y(Mf)?y("shape3d.image")||delete T.uv:(delete T.vs,delete T.ns,delete T.uv),y("shape3d.top.image")||delete T.top_uv,y("shape3d.bottom.image")||delete T.bottom_uv;else if(y(of)===We)I=Rr(V,O,n,l),T=c.shapeModel={vs:[]},y("shape3d.image")&&(T.uv=[],T.sum=y(_j)||vf(I)||1,T.len=0),y(lf)&&!l&&(T.top_vs=[],y("shape3d.top.image")&&(T.top_uv=[])),y(hk)&&!l&&(T.bottom_vs=[],y("shape3d.bottom.image")&&(T.bottom_uv=[])),c._12O(I,E);else{var a,P,i,s,d,J,j,A,B,M;I=Rr(V,O,n,l),d=c.vf(Q,$,!1,x),E&&(J=c.vf(o,$,!1,x),j=l?f:c.vf(S,$,!1,x),A=l?f:c.vf(_,$,!1,x),B=c.vf(xi,$,!1,x),M=c.vf(Kh,$,!1,x)),E?c._12O(I,E):d&&I.forEach(function(b){if(s=b.length,s>0)for(a=b[0],i=1;s>i;i++)P=b[i],c.addV(d.vs,a,P),a=P}),c._20Q(I),K||(d&&(d.ns=ib(d.vs),Jn(d,"vs"),Jn(d,ts)),E&&(J&&(J.ns=ib(J.vs),Jn(J,"vs"),Jn(J,ts)),j&&(j.ns=ib(j.vs),Jn(j,"vs"),Jn(j,ts)),A&&(A.ns=ib(A.vs),Jn(A,"vs"),Jn(A,ts)),B&&(B.ns=ib(B.vs),Jn(B,"vs"),Jn(B,ts)),M&&(M.ns=ib(M.vs),Jn(M,"vs"),Jn(M,ts))))}var G=c.mat=Vp();zg(G,g),fs(G,q.r3(),q.getRotationMode()),zg(G,dh(g)),K&&(Nl(c,G,K,T),c.clear())},_20Q:function(D){var R,v,b,Q,F,x,z,n=this,Z=n.front,W=n.back,i=n.top,t=n.bottom,g=Z?Z.tuv:f,l=W?W.tuv:f,j=i?i.tuv:f,A=t?t.tuv:f,h=0;(g||l||j||A)&&(R=n.s(_j)||vf(D)||1,D.forEach(function(u){if(v=u.length,v>0)for(x=u[0],b=1;v>b;b++)z=u[b],Q=h/R,h+=nm(x,z),F=h/R,n._14O(Z,Q,F),n._14O(W,1-F,1-Q),n._14O(i,Q,F),n._14O(t,Q,F),x=z}))},_14O:function(w,p,D){if(w){var O=w.uv,H=w.tuv;if(H){if(O)var b=O[0]+(O[6]-O[0])*p,F=O[1]+(O[7]-O[1])*p,v=O[2]+(O[4]-O[2])*p,C=O[3]+(O[5]-O[3])*p,y=O[2]+(O[4]-O[2])*D,S=O[3]+(O[5]-O[3])*D,g=O[0]+(O[6]-O[0])*D,I=O[1]+(O[7]-O[1])*D;else b=p,F=0,v=p,C=1,y=D,S=1,g=D,I=0;H.push(v,C,y,S,g,I,g,I,b,F,v,C)}}},_13O:function(X){var M=X.uv,d=X.tuv;d&&(M?d.push(M[2],M[3],M[4],M[5],M[6],M[7],M[6],M[7],M[0],M[1],M[2],M[3]):d.push(0,1,1,1,1,0,1,0,0,0,0,1))},_12O:function(T,E){var n,L,p,o,z,q=this;T.forEach(function(M){if(z=M.length,z>0)for(n={p:M[0],n:!0},o=1;z>o;o++)L=M[o],p=z-1>o?M[o+1]:f,q.addPoint(n,L,p,E,M)})},addPoint:function(E,Q,W,s,i){var T,u,k,V,Y,J,p,b,m,n=this,o=E.p,I=E.f,M=E.b,z=n.s("shape3d.side")||hn,x=n.s("shape3d.start.angle"),d=n.shapeModel,G=n.data.isClosePath();if(E.n&&(E.n=!1,I=G&&i.length>2?Nm(i[i.length-2],o,Q,s):Nm(f,o,Q,s),M=Sr(o,I),d?d.top_vs&&n._10O(M,I,z,x,d.top_vs,d.top_uv):(T=n.left)&&(n.addV(T.vs,M,I),n._13O(T))),u=W?Nm(o,Q,W,s):G&&i.length>2?Nm(o,Q,i[1],s):Nm(o,Q,f,s),k=Sr(Q,u),d){var S,U,N=d.vs,R=d.uv,c=d.sum;for(R&&(S=d.len/c,d.len+=nm(o,Q),U=d.len/c),J=n._9O(M,I,z,x),p=n._9O(k,u,z,x),b=0;z>b;b++)m=b+1,ul(N,J[m]),ul(N,p[b]),ul(N,J[b]),ul(N,p[b]),ul(N,J[m]),ul(N,p[m]),R&&(V=b/z,Y=m/z,R.push(S,Y,U,V,S,V,U,V,S,Y,U,Y));!W&&d.bottom_vs&&n._10O(k,u,z,x,d.bottom_vs,d.bottom_uv,!0)}else!W&&(T=n.right)&&(n.addV(T.vs,u,k),n._13O(T)),(T=n.front)&&n.addV(T.vs,I,u),(T=n.back)&&n.addV(T.vs,k,M),(T=n.top)&&n.addH(T.vs,I,u,k,M,!0),(T=n.bottom)&&n.addH(T.vs,M,k,u,I);E.p=Q,E.f=u,E.b=k},_10O:function($,o,P,D,t,r,V){var J,p,M=this,s=M.data,a=M._9O($,o,P,D),U=($.x+o.x)/2,T=($.y+o.y)/2,l=s.getElevation(),R=0;for(R=0;P>R;R++)V?(ul(t,a[R]),ul(t,a[R+1])):(ul(t,a[R+1]),ul(t,a[R])),ul(t,[U,l,T]),r&&(V?(J=z*(R+1)/P+D,p=z*R/P+D):(J=z*R/P+D,p=z*(R+1)/P+D),r.push(.5-.5*N(p),.5-.5*n(p),.5-.5*N(J),.5-.5*n(J),.5,.5))},_9O:function(e,m,P,p){for(var i,L,$=this,E=$.data,X=[],I=(e.x+m.x)/2,C=(e.y+m.y)/2,f=E.getTall()/2,V=E.getElevation(),R=0;P>=R;R++)i=z*R/P+p,L=N(i),X.push([I+(e.x-I)*L,V+f*n(i),C+(e.y-C)*L]);return X},addV:function(a,h,u){var H=h.x,Z=h.y,q=u.x,D=u.y,V=this.data,m=V.getElevation(),s=V.getTall()/2;a.push(H,m-s,Z,q,m-s,D,q,m+s,D,q,m+s,D,H,m+s,Z,H,m-s,Z)},addH:function(L,b,C,P,F,E){var Z=this.data,U=Z.getTall()/2,s=Z.getElevation()+(E?U:-U);L.push(b.x,s,b.y,C.x,s,C.y,P.x,s,P.y,P.x,s,P.y,F.x,s,F.y,b.x,s,b.y)},vf:function(p,N,E,M){var z,b=this,C=b.gv.getFaceVisible(b.data,p);return(E||C)&&(z=Ql(b,p,M),z.vs=[],z.tuv=C&&(z.texture||N)?[]:f,z.visible=C),z}});var dj=function(J,w){Wn(dj,this,[J,w])};gf(dj,jp,{_25Q:1,validate:function(m){var Y=this,V=Y.data,E=Y.s,K=V.getPoints(),R=K.size();if(Y.shapeModel=Y.info=f,R>1){var Q=Y.info={},G=V.getSegments(),I=V.getThickness(),N=Al(K.get(0)),e=Al(K.get(R-1)),l=R%2;0===l?(Q.c1=Al(K.get(R/2-1)),Q.c2=Al(K.get(R/2))):Q.p3=Al(K.get((R-l)/2)),Q.s1=N,Q.s2=Al(K.get(1)),Q.t1=Al(K.get(R-2)),Q.t2=e,Q.list=E(of)===We?Y.createTubeModel(K,G,I/2,m):Y.createLineModel(K,G,I,"shape.border","shape.dash")}}}),Ym(u,{accordionViewExpandIcon:hh(lr),accordionViewCollapseIcon:hh(lr,!0),accordionViewLabelColor:ps,accordionViewLabelFont:Yh,accordionViewTitleBackground:Mg,accordionViewSelectBackground:wq,accordionViewSelectWidth:3,accordionViewSeparatorColor:j,splitViewDividerSize:1,splitViewDividerBackground:Mg,splitViewDragOpacity:.5,splitViewToggleIcon:{width:16,height:16,comps:[{type:Uh,rect:[2,2,12,12],background:lr}]},propertyViewLabelColor:Ol,propertyViewLabelSelectColor:ps,propertyViewLabelFont:Yh,propertyViewExpandIcon:hh(Qq),propertyViewCollapseIcon:hh(Qq,!0),propertyViewBackground:Uc,propertyViewRowLineVisible:!0,propertyViewColumnLineVisible:!0,propertyViewRowLineColor:Tp,propertyViewColumnLineColor:Tp,propertyViewSelectBackground:wq,listViewLabelColor:Ol,listViewLabelSelectColor:ps,listViewLabelFont:Yh,listViewRowLineVisible:!1,listViewRowLineColor:Tp,listViewSelectBackground:wq,treeViewLabelColor:Ol,treeViewLabelSelectColor:ps,treeViewLabelFont:Yh,treeViewExpandIcon:hh(tc),treeViewCollapseIcon:hh(tc,!0),treeViewRowLineVisible:!1,treeViewRowLineColor:Tp,treeViewSelectBackground:wq,tableViewLabelColor:Ol,tableViewLabelSelectColor:ps,tableViewLabelFont:Yh,tableViewRowLineVisible:!0,tableViewColumnLineVisible:!0,tableViewRowLineColor:Tp,tableViewColumnLineColor:Tp,tableViewSelectBackground:wq,treeTableViewLabelColor:Ol,treeTableViewLabelSelectColor:ps,treeTableViewLabelFont:Yh,treeTableViewExpandIcon:hh(tc),treeTableViewCollapseIcon:hh(tc,!0),treeTableViewRowLineVisible:!0,treeTableViewColumnLineVisible:!0,treeTableViewRowLineColor:Tp,treeTableViewColumnLineColor:Tp,treeTableViewSelectBackground:wq,tableHeaderLabelColor:Ol,tableHeaderLabelFont:Yh,tableHeaderColumnLineVisible:!0,tableHeaderColumnLineColor:Vj,tableHeaderBackground:Uc,tableHeaderMoveBackground:Jo,tableHeaderInsertColor:wq,tableHeaderSortDescIcon:bd(Qq,!0),tableHeaderSortAscIcon:bd(Qq),tabViewTabGap:1,tabViewLabelColor:ps,tabViewLabelFont:Yh,tabViewTabBackground:Mg,tabViewSelectWidth:3,tabViewSelectBackground:wq,tabViewMoveBackground:Jo,tabViewInsertColor:wq,toolbarLabelColor:Ol,toolbarLabelSelectColor:ps,toolbarLabelFont:Yh,toolbarBackground:Uc,toolbarSelectBackground:wq,toolbarItemGap:8,toolbarSeparatorColor:Sl},!0);var yi={translateX:1,sortColumn:1},oc={sortable:1,sortOrder:1,sortFunc:1},Zl={focusData:1},qj={dataModel:1,sortColumn:1,sortFunc:1,visibleFunc:1,rootData:1,rootVisible:1},Rq={dataModel:1,sortFunc:1,visibleFunc:1,categorizable:1},Fp=function(x,o){o.add(x),x.hasChildren()&&x.eachChild(function(U){Fp(U,o)})},Rh=function(S,E,L,p,$,Z,r,j,X,o,c){var T,i=L.getValueType(),_=L.getAlign();if(L.getEnumValues()){var G=L.toEnumLabel(E),J=cp||0,g=0,V=Z,l=u.getTextSize(p,G).width;return T=cj(L.toEnumIcon(E)),T&&(g=Z+cp/2,"center"===_?g+=(j-(J+l))/2:"right"===_&&(g=g+j-(J+l)),jm(S,T,g,r+X/2),V=g+cp/2),G!=f&&(T?tj(S,G,p,$,V,r,j-(V-Z),X):tj(S,G,p,$,Z,r,j,X,_)),void 0}return E=L.formatValue(E),E!=f?i===pk?($i(S,Z,r,j,X,E),void 0):i===_m||!i&&wc(E)?(T=cj(E?rc:eh),jm(S,T,Z+j/2,r+X/2,o,c),void 0):(tj(S,E,p,$,Z,r,j,X,_),void 0):void 0},ln=function(A,j){var V=j.view,Q=V.getDataModel().getHistoryManager(),X=aq(A),r=j.column||j.property;Q&&Q.beginInteraction(),j.editor=A,A.info=j,V.setCurrentEditor(A),V.getView().insertBefore(X,V._79O),tf(A,j.editorRect),A.setFocus?A.setFocus():ub(A),A.commitValue=function(){A._17Q&&(A=A._17Q),V.setValue(j.data,r,A.getValue?A.getValue():A.value),A.close&&A.close(),vc(X)},r.onEditorCreated&&r.onEditorCreated(j)},hi=function(J,n,U,T,V){var G=Ml(J,U),M=n.value,S=n.view,L=V.getValueType();return G.onblur=G.onchange=function(){S.endEditing()},"input"===J?(M=V.formatValue(M),M!=f&&(G.value=M),G.onkeydown=function(n){Kn(n)&&S.endEditing()},(L===hr||L===Gp)&&G.addEventListener(di,u.numberListener,!1)):J===gb&&T.forEach(function(k){var h=$.createElement("option");h.innerHTML=V.toEnumLabel(k),h.value=k,V.isEnumEqual(M,k)&&(h.selected=!0),nj(G,h)}),ln(G,n),G},bn=y.widget={},Wb=function(W,R,v){gf(k+".widget."+W,R,v)};Ym(up,{ms_value:function(W){W.getValue=function(p){var Z=this.getItemById(p),u=Z.element;return u?u.getValue?u.getValue():u.value:Z.selected},W.setValue=function(C,V){var X=this.getItemById(C),Q=X.element;Q?Q.setValue?Q.setValue(V):Q.value=V:(X.selected=V,this.iv())},W.v=function(F,m){var C=this;if(2===arguments.length)C.setValue(F,m);else{if(!ze(F))return C.getValue(F);for(var Q in F)C.setValue(Q,F[Q])}return C}},_46o:function(X){X._icon=f,X._accessType=f,X._valueType=f,X._editable=!1,X._batchEditable=!0,X._align=S,X._nullable=!0,X._emptiable=!1,X.setParent=Kg,X.formatValue=function(O){var r=this,H=r._valueType;if(r.getEnumValues())return r.toEnumLabel(O);if(H===_m)return!!O;if(O!=f){if(H===hr)return K(O);if(sh(O))return pm(O)}return O},X.setEnum=function(p,e,H,Z,v,h){var b=this;p&&p.values&&(Z=p.editable,H=p.icons,e=p.labels,v=p.strict,h=p.maxHeight,p=p.values),El(p)&&(p=p._as),El(e)&&(e=e._as),El(H)&&(H=H._as),b._enumValues=p,b._enumLabels=e,b._enumIcons=H,b._enumEditable=Z,b._enumStrict=v==f?!0:v,b._enumMaxHeight=h,p&&p.length&&sh(p[0])&&(b._valueType=Gp),b.fp("enum",!1,!0)},X.getEnumMaxHeight=function(){return this._enumMaxHeight},X.isEnumEditable=function(){return this._enumEditable},X.getEnumValues=function(){return this._enumValues},X.getEnumLabels=function(){return this._enumLabels},X.getEnumIcons=function(){return this._enumIcons},X.isEnumStrict=function(){return this._enumStrict},X.isEnumEqual=function(Q,y){return this._enumStrict?Q===y:Q==y},X.toEnumLabel=function(O){var R=this,q=R._enumValues,m=R._enumLabels;if(q&&m)for(var T=0;T<q.length;T++)if(R.isEnumEqual(O,q[T]))return m[T];return O},X.toEnumIcon=function(V){var c=this,l=c._enumValues,d=c._enumIcons;if(l&&d)for(var B=0;B<l.length;B++)if(c.isEnumEqual(V,l[B]))return d[B];return j}},_45o:function(Y){Y._87o=function(x,O,k,d,L,v){if(x){var X=this,q=X._90I,T=Xb(1);X._columnLineVisible&&(L-=1),X._rowLineVisible&&(v-=1),0>=L||0>=v||(X._90I||(q=X._90I={}),q[O]||(q[O]=new Xc),tf(T,X.tx()+k,X.ty()+d,L,v),nj(T,x),X._view.insertBefore(T,X._79O),x.onParentAdded&&x.onParentAdded(T),q[O].add(T))}},Y._76o=function(){var r=this,e=r._90I;if(e){for(var y in e)e[y].each(function(z){vc(z)});delete r._90I}},Y._77o=function(J){var y=this;if(y._90I){var T=y._90I[J];T&&(T.each(function(_){vc(_)}),delete y._90I[J])}}},_47o:function(F){F.getValue=function(W,w){return w.getValue?w.getValue(W,w,this):ym(W,w.getAccessType(),w.getName())},F.setValue=function($,n,V){if(n.isEmptiable()||""!==V||(V=j),n.isNullable()||V!=f){var K=this,x=n.getName(),t=n.getAccessType(),S=n.getValueType();S===hr&&sl(V)?V=c(V):S===Gp&&sl(V)?V=parseFloat(V):S===_m&&sl(V)&&(V="true"===V),K._batchEditable&&n._batchEditable&&K.isSelected($)?K.sm().each(function(u){n.setValue?n.setValue(u,n,V,K):$o(u,t,x,V)}):n.setValue?n.setValue($,n,V,K):$o($,t,x,V)}},F.setCurrentEditor=function(T){this.endEditing(),this._currentEditor=T,this.redraw()},F.isEditing=function(h,v){var E=this,s=E._currentEditor;if(!s)return!1;if(v){var y=s.info;return y?(y.column||y.property)===v&&y.data===h:!1}return!0},F.endEditing=function(){var V=this,W=V._currentEditor,d=V.getDataModel().getHistoryManager();W&&(delete V._currentEditor,W.commitValue&&W.commitValue(W.info),V.redraw(),d&&d.endInteraction())},F.beginEditing=function(Z){this.endEditing();var B=this,P=Z.column||Z.property;if(P.beginEditing)P.beginEditing(Z);else{var V=Z.data,p=Z.value,T=B.getSelectBackground(V),t=P.getEnumValues(),M=P.getSlider(),a=P.getColorPicker();if(M){var X=vk(bn.Slider,M);return X.setValue(p),X.getView().onblur=function(){B.endEditing()},X.isInstant()&&(X.onValueChanged=function(){B.setValue(V,P,X.getValue())}),ln(X,Z),void 0}if(a||P.getValueType()===pk){var l=vk(bn.ColorPicker,a);return l.setValue(p),l.onClosed=function(){B.endEditing()},l.isInstant()&&(l.onValueChanged=function(Z,d){B.setValue(V,P,d)}),ln(l,Z),l.open(),void 0}if(t){if(bn.ComboBox){var r=new bn.ComboBox;r.setValue(p),r.setValues(t),r.setLabels(P.getEnumLabels()),r.setIcons(P.getEnumIcons()),r.setEditable(P.isEnumEditable()),r.setStrict(P.isEnumStrict()),r.setMaxHeight(P.getEnumMaxHeight()),r.onClosed=function(){B.endEditing()},ln(r,Z),r.open()}else hi(gb,Z,T,t,P);return}var d=P.getValueType();if(d===_m||wc(p))return B.setValue(V,P,!p),void 0;if(P.getItemEditor()){var y=Bf(P.getItemEditor()),Q=new y(V,P,B,Z),G=Q.getView();return G._17Q=Q,Q.setValue(p),he(G),ln(G,Z),Q.editBeginning&&Q.editBeginning(),void 0}hi("input",Z,T,f,P)}}},_44o:function(g){g.init=function(P){var F=this,c=F.th=new Rc(P),s=F._view=Xb(1);F.tv=F._tableView=P,nj(s,c.getView()),nj(s,P.getView()),c.mp(function(I){I.property===Em&&F.iv()}),F.iv()},g.getTableView=function(){return this.tv},g.getTableHeader=function(){return this.th},g.getDataModel=function(){return this.tv.dm()},g.getColumnModel=function(){return this.tv.getColumnModel()},g.setColumns=function(U){this.tv.setColumns(U)},g.addColumns=function(r){this.tv.addColumns(r)},g.endEditing=function(){this.tv.endEditing()},g.validateImpl=function(){var D=this,k=D.th,y=Pc(k),J={x:0,y:0,width:D.getWidth(),height:y};tf(k,J),J.y=y,J.height=b(0,D.getHeight()-y),tf(D.tv,J)}},ms_vs:function(j){j._41o=function(){return this._29I.height<this._59I},j._43o=function(){var v=this;
v._41o()&&(v._58I||(g(function(){v._94O()},Vd),v.iv()),v._58I=new Date)},j._94O=function(){var L=this;if(L._58I){var U=new Date;U.getTime()-L._58I.getTime()>=Vd?(delete L._58I,L.iv()):g(function(){L._94O()},Vd)}},j._93I=function(){var m=this,w=m._27I;if(m._58I||!m._autoHideScrollBar){w||nj(m._79O,w=m._27I=Xb());var z=m._29I,Q=z.height,k=m._59I,Y=m.getScrollBarSize(),C=z.width-Y-2,W=Q*(-m.ty()/k),g=Q*(Q/k),O=w.style;k>Q?(re>g&&(W=W+g/2-re/2,0>W&&(W=0),W+re>Q&&(W=Q-re),g=re),O.visibility=$g,O.background=m.getScrollBarColor(),O.borderRadius=Y/2+Yl,tf(w,C,W,Y,g)):O.visibility=$n}else w&&(w.style.visibility=$n)}},ms_hs:function(q){q._40o=function(){return this._29I.width<this._91I},q._42o=function(){var H=this;H._40o()&&(H._95O||(g(function(){H._94I()},Vd),H.iv()),H._95O=new Date)},q._94I=function(){var E=this;if(E._95O){var Z=new Date;Z.getTime()-E._95O.getTime()>=Vd?(delete E._95O,E.iv()):g(function(){E._94I()},Vd)}},q._92I=function(){var w=this,v=w._28I;if(w._95O||!w._autoHideScrollBar){v||nj(w._79O,v=w._28I=Xb());var P=w._29I,y=P.width,S=w._91I,A=w.getScrollBarSize(),c=P.height-A-2,o=y*(-w.tx()/S),x=y*(y/S),I=v.style;S>y?(re>x&&(o=o+x/2-re/2,0>o&&(o=0),o+re>y&&(o=y-re),x=re),I.visibility=$g,I.background=w.getScrollBarColor(),I.borderRadius=A/2+Yl,tf(v,o,c,x,A)):I.visibility=$n}else v&&(v.style.visibility=$n)}}}),ni(rc,Gh(16,16,[{type:ik,rect:[1,1,14,14],background:wq},{type:nc,rect:[1,1,14,14],width:1,color:Tr},{type:xh,points:[13,3,7,12,4,8],borderWidth:2,borderColor:"#FFF"}])),ni(eh,Gh(16,16,{type:nc,rect:[1,1,14,14],width:1,color:Tr})),ni(jg,Gh(16,16,[{type:mo,rect:[2,2,12,12],borderWidth:1,borderColor:Tr,background:"#FFF"},{type:mo,rect:[4,4,8,8],background:wq}])),ni(jj,Gh(16,16,{type:mo,rect:[2,2,12,12],borderWidth:1,borderColor:Tr})),up._15Q=function(R){R._29I=Df,R._59I=0,R._91I=0,R._5o=function(d){var A=this;A._30I=new Xc,A._rows=new Xc,A._rowMap={},A._31I=0,A._14I=0,A._view=Xb(1),A._canvas=Pf(A._view),nj(A._view,A._79O=Xb()),A.dm(d?d:new Db)},R.getCheckIcon=function(R){var N=this.sm(),x=N.co(R);return N.sg()?cj(x?jg:jj):cj(x?rc:eh)},R.checkData=function(n){var p=this.sm(),B=p.co(n);p.sg()&&B||(this._32o=1,B?p.rs(n):p.as(n),delete this._32o)},R.getDataAt=function(W){W.target&&(W=this.lp(W));var L=T(W.y/this._rowHeight),K=this._rows;return 0>L||L>=K.size()?f:K.get(L)},R.onDataDoubleClicked=function(){},R.onDataClicked=function(){},R.adjustTranslateX=function(){return 0},R.adjustTranslateY=function(q){var v=this.getHeight()-this._59I;return v>q&&(q=v),q>0?0:K(q)},R.onPropertyChanged=function(r){var $=this,_=r.property;qj[_]?$.ivm():Zl[_]||$.redraw(),_===$l?$._42o():_===Cg&&$._43o()},R.getLabel=function(e){return e.toLabel()},R.getLabelFont=function(){return this._labelFont},R.getLabelColor=function(V){var v=this;if(v.isCheckMode()){if(v._focusData===V)return v._labelSelectColor}else if(v.isSelected(V))return v._labelSelectColor;return v._labelColor},R.getStartRowIndex=function(){return this._31I},R.getEndRowIndex=function(){return this._14I},R.getRowDatas=function(){return this._rows},R.getRowIndex=function(d){return this._rowMap[d._id]},R.getRowSize=function(){return this._rows.size()},R.getViewRect=function(){return el(this._29I)},R.isVisible=function(l){return this._visibleFunc?this._visibleFunc(l):!0},R.getCurrentSortFunc=function(){return this._sortFunc},R.setDataModel=function(v){var z=this,U=z._dataModel,O=z._3o;U!==v&&(U&&(U.umm(z.handleDataModelChange,z),U.umd(z.handleDataPropertyChange,z),U.umh(z._15o,z),O||U.sm().ums(z._16o,z)),z._dataModel=v,v.mm(z.handleDataModelChange,z),v.md(z.handleDataPropertyChange,z),v.mh(z._15o,z),O?O._21I(v):v.sm().ms(z._16o,z),z.fp("dataModel",U,v))},R.validateModel=function(){var d=this;d._rows.clear(),d._rowMap={},d.buildChildren(d._dataModel._roots);var M=d._rows=d._rows.toList(d.isVisible,d),i=0,l=d.getCurrentSortFunc(),A=M.size();for(l&&M.sort(l);A>i;i++)d._rowMap[M.get(i)._id]=i},R.buildChildren=function(Q){var y=this;Q.each(function(Y){y._rows.add(Y),y.buildChildren(Y._children)})},R.handleDataModelChange=function(){this.ivm()},R.handleDataPropertyChange=function(q){"parent"===q.property?this.ivm():this.invalidateData(q.data)},R._15o=function(){this.ivm()},R._16o=function(D){D.datas.each(this.invalidateData,this),this.onSelectionChanged(D)},R.onSelectionChanged=function(p){var i=this,l=i.sm();!i.isAutoMakeVisible()||1!==l.size()||"set"!==p.kind&&"append"!==p.kind||i._32o||i.makeVisible(l.ld())},R.makeVisible=function(j){j&&(this._23I=j,this.iv())},R.scrollToIndex=function(V){var q=this,h=q._29I,D=h.height,N=q._rowHeight,T=N*V;V>=0&&V<q._rows.size()&&D>0&&(T+N>h.y+D?q.ty(-T+D-N):T<h.y&&q.ty(-T))},R.ivm=function(){this.invalidateModel()},R.invalidateModel=function(){var u=this;u._96I||(u._96I=1,u._32I=1,delete u._24I,u.iv())},R.redraw=function(){var u=this;u._32I||(u._32I=1,delete u._24I,u.iv())},R.invalidateData=function(Z){var I=this;d?I.redraw():I._32I||(I._24I||(I._24I={}),I._24I[Z._id]=Z,I.iv())},R.getFocusData=function(){return this._focusData},R.setFocusDataById=function(F){this.setFocusData(this.dm().getDataById(F))},R.setFocusData=function(D){var b=this,c=b._focusData;c!==D&&(b._focusData=D,b.fp("focusData",c,D),c&&b.invalidateData(c),D&&(b.invalidateData(D),b.isAutoMakeVisible()&&b.makeVisible(D)))},R.drawRowBackground=function(G,I,U,c,$,u,P){var v=this,z=v.isCheckMode();(I===v._focusData&&z||U&&!z)&&$i(G,c,$,u,P,v.getSelectBackground(I))},R.drawData=function(h,H,R){var z=this,n=z._rowHeight,_=n*R,D=z._29I,q=D.x,L=D.width;h.save(),h.beginPath(),h.rect(q,_,L,n),h.clip(),z._87o(z.drawRow(h,H,z.isSelected(H),q,_,L,n),R,q,_,L,n),h.restore(),z._rowLineVisible&&$i(h,q,_+n-1,L,1,z._rowLineColor)},R._12I=function(q){var Y=this,P=Y._31I,s=Y._29I,X=s.x,t=s.y,B=s.width,E=s.height;for(q.beginPath(),q.rect(X,t,B,E),q.clip(),q.clearRect(X,t,B,E),Y._76o(),Y._93db(q);P<Y._14I;P++)Y.drawData(q,Y._rows.get(P),P);Y._92db(q)},R._13I=function(_){for(var Q,z=this,t=z._rowHeight,f=z._29I,V=f.x,K=f.width,$=z._31I,c=z._30I;$<z._14I;$++)Q=z._rows.get($),z._24I[Q._id]&&c.add({data:Q,index:$});c.isEmpty()||(_.beginPath(),c.each(function(g){_.rect(V,g.index*t,K,t)}),_.clip(),c.each(function(S){_.clearRect(V,S.index*t,K,t)}),c.each(function(K){z._77o(K.index)}),z._93db(_),c.each(function(G){z.drawData(_,G.data,G.index)}),z._92db(_),c.clear())},R.validateImpl=function(){var P=this,d=P._canvas,j=P.getWidth(),G=P.getHeight(),A=P._rowHeight,U=P._32I;(j!==d.clientWidth||G!==d.clientHeight)&&(Gr(d,j,G),U=1),P._96I&&P.validateModel();var t=P._29I,u={x:-P.tx(),y:-P.ty(),width:j,height:G},J=P._rows.size(),E=cs(d),W=P._23I;U||nn(u,t)||(U=1),P._29I=u,P._59I=J*A,P._31I=T(u.y/A),P._14I=a((u.y+u.height)/A),P._31I<0&&(P._31I=0),P._14I>J&&(P._14I=J),P._99I&&U&&P._99I(),lk(E,P.tx(),P.ty(),1),U?P._12I(E):P._24I&&P._13I(E),P._93I(),P._92I(),E.restore(),P._32I=P._24I=P._96I=f,W&&(P.scrollToIndex(P.getRowIndex(W)),delete P._23I),P.tx(P.tx()),P.ty(P.ty())}},up._48o=function(G){G._rootVisible=!0,G._rootData=f,G._35o=function(){this._expandMap={},this._levelMap={}},G.validateModel=function(){var e=this,D=e._rootData;e._rows.clear(),e._levelMap={},e._rowMap={},e._currentLevel=0,D?e._rootVisible?e.isVisible(D)&&e.buildData(D):e.buildChildren(D):e.buildChildren(),delete e._currentLevel},G.buildData=function(n){var s=this,X=n._id,S=s._rows;s._rowMap[X]=S.size(),S.add(n),s._levelMap[X]=s._currentLevel,s.isExpanded(n)&&(s._currentLevel++,s.buildChildren(n),s._currentLevel--)},G.buildChildren=function(X){var i=this,d=X?X._children:i._dataModel._roots,q=i.getCurrentSortFunc();q&&i.ischildrenortable(X)?d.toList(i.isVisible,i).sort(q).each(i.buildData,i):d.each(function(S){i.isVisible(S)&&i.buildData(S)})},G.getLevel=function(j){return this._levelMap[j._id]},G.getToggleIcon=function(O){var i=this,T=i._loader,t=i._collapseIcon;return T&&!T.isLoaded(O)?t:O.hasChildren()?i.isExpanded(O)?i._expandIcon:t:f},G.isCheckMode=function(){return this._checkMode!=f},G.ischildrenortable=function(){return!0},G.handleDataModelChange=function(q){var j=this;q.kind===Yi?delete j._expandMap[q.data._id]:q.kind===on&&(j._expandMap={}),j.ivm()},G.toggle=function(w){var S=this;S.isExpanded(w)?S.collapse(w):S.expand(w)},G.isExpanded=function(c){return 1===this._expandMap[c._id]},G.expand=function(v){var g=this,N=g._loader;g.isExpanded(v)||(N&&!N.isLoaded(v)&&N.load(v),g._expandMap[v._id]=1,g.ivm(),g.onExpanded(v))},G.onExpanded=function(){},G.collapse=function(C){var Q=this;Q.isExpanded(C)&&(delete Q._expandMap[C._id],Q.ivm(),Q.onCollapsed(C))},G.onCollapsed=function(){},G.expandAll=function(){var s=this;s._dataModel.each(function(E){E.hasChildren()&&(s._expandMap[E._id]=1)}),s.ivm()},G.collapseAll=function(){this._expandMap={},this.ivm()},G.makeVisible=function(i){if(i){var w=this;if(!w._rootData||i.isDescendantOf(w._rootData)){for(var N=i._parent;N;)w.expand(N),N=N._parent;w._23I=i,w.iv()}}},G.checkData=function(I){var c,o=this,T=o._checkMode,P=o.sm(),Z=P.co(I);if(!P.sg()||!Z){if(o._32o=1,T===r)Z?P.rs(I):P.as(I);else if(T===Ck)c=new Xc(I),c.addAll(I._children);else if("descendant"===T)c=new Xc,Fp(I,c);else if("all"===T&&(c=new Xc,Fp(I,c),!Z))for(var Y=I._parent;Y;)c.add(Y),Y=Y._parent;c&&(Z?P.rs(c):P.as(c)),delete o._32o}},G._97I=function(Z,K,S,I,f,y,C){var M=this,P=M._indent,B=M._levelMap[K._id],z=M.getIconWidth(K),e=cj(M.getToggleIcon(K));e?(I+=P*B,jm(Z,e,I+P/2,f+C/2,K,M),I+=P):I+=P*(B+1),M._checkMode&&(jm(Z,M.getCheckIcon(K),I+P/2,f+C/2,K,M),I+=P),M.drawIcon(Z,K,I,f,z,C),M.drawLabel(Z,K,I+z,f,C)}},up._14Q=function(z){z.getIcon=function(V){return V.getIcon()},z.getIconWidth=function(W){return this.getIcon(W)?this._indent:0},z.drawIcon=function(G,L,a,v,Q,N){if(Q){var T=this,K=T.getBodyColor(L),M=cj(T.getIcon(L),K);M&&(N-=T.isRowLineVisible()?1:0,Li(G,M,Ed,a,v,Q,N,L,T,K),mb(G,T.getBorderColor(L),a,v,Q,N))}},z.drawLabel=function(D,S,E,H,B){var Y=this;tj(D,Y.getLabel(S),Y.getLabelFont(S),Y.getLabelColor(S),E,H,0,B)}},up._50o=function(r){r._98I=function(){var P=this,g=P._39o=new Db;P._60I=new Xc,g.mm(P._17o,P),g.md(P._18o,P),g.mh(P._19o,P)},r.setColumns=function(W){this._39o.clear(),this.addColumns(W)},r.addColumns=function(Q){var J=this._39o;Q.forEach(function(W){if(!(W instanceof Gf)){var b=Bf(W.className);W=vk(b?b:Gf,W)}J.add(W)})},r.onColumnClicked=function(){},r.onCheckColumnClicked=function(){},r._3Q=function(Q){for(var R,r=0,B=this._60I,Z=B.size();Z>r;r++)if(R=B.get(r),R.column===Q)return R;return f},r.getColumnAt=function(x){var Z=this._4Q(x);return Z?Z.column:f},r._4Q=function(n){for(var $=this,Z=n.target?$.lp(n).x:n.x,t=$._60I,q=0;q<t.size();q++){var k=t.get(q),E=k.startX;if(Z>=E&&Z<E+k.column.getWidth())return k}return f},r.getToolTip=function(E){var Z=this,j=Z.getDataAt(E),M=Z.getColumnAt(E);return j&&M?M.getToolTip(j,Z):f},r.adjustTranslateX=function(d){var Q=this.getWidth()-this._91I;return Q>d&&(d=Q),d>0?0:K(d)},r._99I=function(){var e=this,Y=e._29I,b=e._60I;b.clear(),e._91I=0,e._39o._roots.each(function(v){if(v.isVisible()){var W=e._91I+v.getWidth();e._91I<=Y.x+Y.width&&W>=Y.x&&b.add({column:v,startX:e._91I}),e._91I=W}})},r.drawData=function(v,s,N){var n=this,r=n._rowHeight,u=r*N,E=n.isSelected(s),p=n._29I,h=p.x,P=p.width;n.drawRowBackground(v,s,E,h,u,P,r),n._60I.each(function(Y){var K=Y.column,f=Y.startX,D=K.getWidth();D>0&&!n.isEditing(s,K)&&(v.save(),v.beginPath(),v.rect(f,u,D,r),v.clip(),n._87o(n.drawCell(v,s,E,K,f,u,D,r),N,f,u,D,r),n._columnLineVisible&&$i(v,f+D-1,u,1,r,n._columnLineColor),v.restore())}),n._rowLineVisible&&$i(v,h,u+r-1,P,1,n._rowLineColor)},r.drawCell=function(U,B,j,x,Q,W,w,T){var J=this;if(x.drawCell)return x.drawCell(U,B,j,x,Q,W,w,T,J);var t=J.getValue(B,x);Rh(U,t,x,J.getLabelFont(B,x,t),J.getLabelColor(B,x,t),Q,W,w,T,B,J)},r.getColumnModel=function(){return this._39o},r._17o=function(){this.redraw()},r._18o=function(k){var m=this;k.data===m._sortColumn&&oc[k.property]?m.ivm():(m._42o(),m.redraw())},r._19o=function(){this.redraw()},r.getCurrentSortFunc=function(){var H=this,B=H._sortColumn;if(B&&B.isSortable()){var V=B.getSortFunc(),I=Ec===B.getSortOrder()?1:-1;return V||(V=Ph),function(G,N){return V.call(H,H.getValue(G,B),H.getValue(N,B),G,N)*I}}return H._sortFunc},r.isCellEditable=function(a,r){return r.isEditable()&&this.isEditable()},r._37O=function(Y,T){if(Fj(T))for(var g=this,o=g.lp(T),S=g._60I,d=g._rowHeight,j=g._29I,s=j.x,N=j.y,R=j.width,c=j.height,J=0;J<S.size();J++){var A=S.get(J),K=A.startX,w=A.column,i=w.getWidth();if(w!==g._31o&&w!==g._4o&&g.isCellEditable(Y,w)&&o.x>=K&&o.x<K+i){var G={x:K,y:g.getRowIndex(Y)*d,width:i,height:d},I={x:G.x+g.tx(),y:G.y+g.ty(),width:G.width,height:G.height},E=0,q=0;return G.x<s?E=G.x-s:G.x+G.width>s+R&&(E=G.x+G.width-s-R),E&&(g.tx(g.tx()-E),I.x-=E),G.y<N?q=G.y-N:G.y+d>N+c&&(q=G.y+d-N-c),q&&(g.ty(g.ty()-q),I.y-=q),g.beginEditing({data:Y,column:w,value:g.getValue(Y,w),event:T,rect:G,editorRect:I,view:g}),void 0}}}},bn.BaseItemEditor=function(T,O,m,l){this._data=T,this._column=O,this._master=m,this._editInfo=l},Wb("BaseItemEditor",M,{ms_ac:["data","column","master","editInfo"],editBeginning:function(){},getView:function(){},getValue:function(){},setValue:function(){}});var mg=y.Tab=function(){Wn(mg,this)};ol("Tab",kb,{ms_ac:["view","closable","disabled"],_icon:f,_closable:!1,_disabled:!1,setParent:Kg});var Gf=y.Column=function(){Wn(Gf,this)};ol("Column",kb,{_46o:1,ms_ac:["accessType","valueType",$g,Xq,Ce,Le,"align",pk,"sortOrder",An,"sortable","nullable","emptiable","slider","colorPicker","itemEditor"],_visible:!0,_width:80,_sortOrder:Ec,_sortFunc:f,_sortable:!0,setWidth:function(F){16>F&&(F=16);var o=this._width;this._width=F,this.fp(Le,o,F)},getToolTip:function(r,A){return this.formatValue(A.getValue(r,this))}});var bl=y.Property=function(){Wn(bl,this)};ol("Property",kb,{_46o:1,ms_ac:["accessType","valueType",Xq,Ce,"categoryName",pk,"align","nullable","emptiable","slider","itemEditor","colorPicker"],_categoryName:f,getToolTip:function(M,j,I){var L=this;return j?L.formatValue(I.getValue(M,L)):I.getPropertyName(L)}}),bn.AccordionView=function(){var r=this;r._20o={},r._21o=new Xc,r._10o=f,r._9o=f,r._11o=f,r._view=Xb(),r.iv()},Wb("AccordionView",M,{ms_v:1,ms_fire:1,ms_ac:[Oh,gg,"titleHeight",bg,id,"titleBackground","selectWidth",Zq,"orientation","separatorColor"],_expandIcon:u.accordionViewExpandIcon,_collapseIcon:u.accordionViewCollapseIcon,_titleHeight:ei,_labelColor:u.accordionViewLabelColor,_labelFont:u.accordionViewLabelFont,_titleBackground:u.accordionViewTitleBackground,_selectBackground:u.accordionViewSelectBackground,_selectWidth:u.accordionViewSelectWidth,_orientation:"v",_separatorColor:u.accordionViewSeparatorColor,onPropertyChanged:function(){this.iv()},getView:function(){return this._view},getTitles:function(){return this._21o},getCurrentTitle:function(){return this._10o},add:function(K,i,E,c){var s=this,k=Xb();if(s._20o[K])throw K+" already exists";k.onmousedown=np,k.style.cursor=Vi,k.addEventListener(w?_n:eg,function(J){np(J)},!1),k.addEventListener(w?wp:sg,function(N){np(N),Fj(N)&&(s._10o===K?s.collapse():s.expand(K))},!1),nj(s._view,k),s._20o[K]={content:i,div:k,canvas:Pf(k),icon:c},s._21o.add(K),E&&s.expand(K),s.iv()},remove:function(k){var Z=this,m=Z._20o[k];m&&(vc(m.div),delete Z._20o[k],Z._21o.remove(k),Z.iv())},clear:function(){var Y=this;Y._20o={},Y._21o.clear(),Y.iv()},isExpanded:function(l){return this._10o===l},expand:function(b){var B=this;B._20o[b]&&B._10o!==b&&(B._10o=b,B.onExpanded(b),B.iv())},onExpanded:function(){},collapse:function(){var l=this;l._10o&&(l.onCollapsed(l._10o),delete l._10o,l.iv())},onCollapsed:function(){},initCanvas:function(h,p,A){Gr(h,p,A);var w=cs(h);return lk(w,0,0,1),w.clearRect(0,0,p,A),w},drawTitle:function(p,A,L,w,s){var n=this,k=cj(s.icon),g=n.isExpanded(A),t=n._titleHeight,l=n._titleBackground,q=n._selectWidth,G=n._separatorColor,S=cj(g?n._expandIcon:n._collapseIcon),W=q+4;$i(p,0,0,L,w,l),g&&q&&$i(p,0,0,q,w,n._selectBackground),(g||n._21o.get(n._21o.size()-1)!==A)&&$i(p,0,w-1,L,1,G?G:Ue(l)),k&&(jm(p,k,W+eq(k)/2,t/2),W+=eq(k)+2),tj(p,A,n.getLabelFont(A),n.getLabelColor(A),W,0,0,t),S&&jm(p,S,L-eq(S)/2-4,t/2)},validateImpl:function(){var x=this,u=x._view,N=0,g=0,O=x.getWidth(),f=x.getHeight(),r=x._titleHeight,U=x._21o.size()*r,t=x._11o,q=x._9o;delete x._11o,delete x._9o,x._21o.each(function(h){var c,B,z=x._20o[h],p=z.content,d=x._10o===h;Ci(x)?(tf(z.div,N,0,r,f),c=x.initCanvas(z.canvas,r,f),On(c,0,f),qr(c,-v),x.drawTitle(c,h,f,r,z),c.restore(),d?(B=b(0,O-U),p&&(x._11o=p,x._9o=aq(p),tf(p,N+r,0,B,f)),N+=r+B):N+=r):(tf(z.div,0,g,O,r),c=x.initCanvas(z.canvas,O,r),x.drawTitle(c,h,O,r,z),c.restore(),d?(B=b(0,f-U),p&&(x._11o=p,x._9o=aq(p),tf(p,0,g+r,O,B)),g+=r+B):g+=r)});var D=x._9o;t&&t!==x._11o&&t.endEditing&&t.endEditing(),D&&D!==q&&nj(u,D),q&&q!==D&&vc(q)}}),bn.SplitView=function(U,j,Q,M){var A=this,i=A._dividerDiv=Xb(),q=A._60O=Pf(),C=A._61O=Pf(),u=q.style,v=C.style;A._view=Xb(1),nj(A._view,i),u.msTouchAction=Qm,u.pointerEvents=Qm,u.cursor=Vi,v.msTouchAction=Qm,v.pointerEvents=Qm,v.cursor=Vi,U&&A.setLeftView(U),j&&A.setRightView(j),Q&&A.setOrientation(Q),M!=f&&A.setPosition(M),A.setStatus(Pr),new Ud(A)},Wb("SplitView",M,{ms_v:1,ms_fire:1,ms_ac:["dividerSize","dividerBackground","toggleIcon","togglable","dragOpacity","orientation","draggable","status"],_position:.5,_togglable:!0,_orientation:"h",_draggable:!0,_dividerSize:u.splitViewDividerSize,_dividerBackground:u.splitViewDividerBackground,_dragOpacity:u.splitViewDragOpacity,_toggleIcon:u.splitViewToggleIcon,onPropertyChanged:function(){this.iv()},getDividerDiv:function(){return this._dividerDiv},getPosition:function(){return this._position},setPosition:function(L,F){var o=this,a=o._position;isNaN(L)||(F||(o._82O=0>L?-1:L>1?1:0),o._position=L,o.fp(Dj,a,L))},getLeftView:function(){return this._leftView},setLeftView:function(m){var E=this,X=E._leftView,M=E._view;if(E._leftView!==m){if(X&&X!==E._rightView){var G=aq(X);G.parentNode===M&&vc(G)}if(E._leftView=m,m){var j=aq(m);j.parentNode!==M&&M.insertBefore(j,E._dividerDiv)}E.fp("leftView",X,m)}},getRightView:function(){return this._rightView},setRightView:function(r){var Z=this,i=Z._rightView,F=Z._view;if(Z._rightView!==r){if(i&&i!==Z._leftView){var H=aq(i);H.parentNode===F&&vc(H)}if(Z._rightView=r,r){var t=aq(r);t.parentNode!==F&&F.insertBefore(t,Z._dividerDiv)}Z.fp("rightView",i,r)}},validateImpl:function(){var L=this,c=L._draggable,Z=L._position,k=L.getWidth(),i=L.getHeight(),U=L._dividerSize,j=L._dividerBackground,r=w?18:8,R=L._dividerDiv,W=L._82O,V=L._60O,m=L._61O,H=L._toggleIcon,d=L._status,N=R.style,S=null,n=w?20:4,$=H.comps[0];U>=r||0===U?L._coverDiv&&(vc(L._coverDiv),delete L._coverDiv):L._coverDiv||(L._coverDiv=Xb(),h&&(L._coverDiv.style.background=Dm),nj(R,L._coverDiv)),L._togglable?V.parentNode||(nj(R,V),nj(R,m)):(vc(V),vc(m));var t=L._coverDiv,M=L._leftView,g=L._rightView;if(Ci(L)){if(U>k&&(U=k),d===Pr)if(1===W)var q=Z,e=b(0,k-U-q);else-1===W?(e=-Z,q=b(0,k-U-e)):(q=K((k-U)*Z),e=b(0,k-U-q));else"cl"===d?(q=0,e=b(0,k-U)):"cr"===d&&(e=0,q=b(0,k-U));M&&tf(M,0,0,q,i),g&&tf(g,q+U,0,e,i),tf(R,q,0,U,i),L._22o=q,t&&(tf(t,U/2-r/2,0,r,i),t.style.cursor=c?vp:""),R.style.cursor=c?vp:"";var P=R.clientHeight/2,A=R.clientWidth;Gr(V,A,A),tf(V,0,P-A-n,A,A),S=cs(V),lk(S,0,0,1),$.rotation=-v,tb(S,H,0,0,A,A),S.restore(),Gr(m,A,A),tf(m,0,P+n,A,A),S=cs(m),lk(S,0,0,1),$.rotation=v,tb(S,H,0,0,A,A),S.restore()}else{if(U>i&&(U=i),d===Pr)if(1===W)var F=Z,D=b(0,i-U-F);else-1===W?(D=-Z,F=b(0,i-U-D)):(F=K((i-U)*Z),D=b(0,i-U-F));else"cl"===d?(F=0,D=b(0,i-U)):"cr"===d&&(D=0,F=b(0,i-U));M&&tf(M,0,0,k,F),g&&tf(g,0,F+U,k,D),tf(R,0,F,k,U),L._22o=F,t&&(tf(t,0,U/2-r/2,k,r),t.style.cursor=c?Gc:""),N.cursor=c?Gc:"";var x=R.clientWidth/2,A=R.clientHeight;Gr(V,A,A),tf(V,x-A-n,0,A,A),S=cs(V),lk(S,0,0,1),$.rotation=0,tb(S,H,0,0,A,A),S.restore(),Gr(m,A,A),tf(m,x+n,0,A,A),S=cs(m),lk(S,0,0,1),$.rotation=p,tb(S,H,0,0,A,A),S.restore()}N.background=j}});var Ud=function(S){this.sv=S,this.addListeners()};gf(Ud,M,{ms_listener:1,getView:function(){return this.sv.getView()},handle_touchstart:function(v){var $=this,W=$.sv,w=W._dividerDiv,V=W._60O,A=W._61O,q=W._status,y=v.target;if(y===V)q===Pr?W.setStatus("cl"):"cr"===q&&W.setStatus(Pr);else if(y===A)q===Pr?W.setStatus("cr"):"cl"===q&&W.setStatus(Pr);else if(bo(v)&&(y===w||y===W._coverDiv)&&(np(v),W.isDraggable())){$.resizeDiv=Xb();var r=$.resizeDiv.style,B=w.style;r.left=B.left,r.top=B.top,r.width=B.width,r.height=B.height,r.opacity=W.getDragOpacity(),r.background=W.getDividerBackground(),$._85o=$._86o=Ci(W)?Ee(v).x:Ee(v).y,nj($.getView(),$.resizeDiv),$._clientPoint=Ee(v),Mk($,v)}},handleWindowTouchMove:function(u){if(bo(u)){var r=this,e=r.sv,M=e._dividerSize,I=r.resizeDiv.style,Q=e.getWidth(),S=e.getHeight(),a=e._22o-r._86o;Ci(e)?(r._85o=Ee(u).x,a+=r._85o,a>Q-M&&(a=Q-M),0>a&&(a=0),I.left=a+Yl):(r._85o=Ee(u).y,a+=r._85o,a>S-M&&(a=S-M),0>a&&(a=0),I.top=a+Yl)}},handleWindowTouchEnd:function(z){var E=this,J=E.sv,_=J._22o-E._86o+E._85o,b=J._dividerSize,h=Ci(J)?J.getWidth():J.getHeight(),L=J._82O,H=nm(E._clientPoint,Ee(z));H>1&&(J.setStatus(Pr),0>_&&(_=0),_>h-b&&(_=h-b),h!==b&&(1===L?J.setPosition(_,1):-1===L?J.setPosition(_-h+b,1):J.setPosition(_/(h-b),1))),vc(E.resizeDiv),E.resizeDiv=E._85o=E._clientPoint=f},handle_mousedown:function(i){Fj(i)&&this.handle_touchstart(i)},handleWindowMouseMove:function(j){this.handleWindowTouchMove(j)},handleWindowMouseUp:function(x){this.handleWindowTouchEnd(x)}}),bn.TabView=function(){var o=this,x=o._view=Xb(1),v=o._91O=Xb(1),K=o._tabModel=new Db,W=K.sm(),p=o.invalidate;o._7o=new Xc,o._canvas=Pf(v),nj(x,v),nj(x,o._92O=Xb(1)),W.setSelectionMode(U),W.ms(p,o),K.mm(p,o),K.md(p,o),K.mh(p,o),new De(o),o.iv()},Wb("TabView",M,{ms_v:1,ms_fire:1,ms_tx:1,ms_ty:1,ms_lp:1,ms_ac:["movable","tabGap","tabHeight","tabPosition",bg,id,"tabBackground","selectWidth",Zq,"moveBackground","insertColor"],_tabPosition:xi,_tabHeight:ei,_tabGap:u.tabViewTabGap,_labelColor:u.tabViewLabelColor,_labelFont:u.tabViewLabelFont,_tabBackground:u.tabViewTabBackground,_selectWidth:u.tabViewSelectWidth,_selectBackground:u.tabViewSelectBackground,_moveBackground:u.tabViewMoveBackground,_insertColor:u.tabViewInsertColor,_movable:!0,getContentDiv:function(){return this._92O},getTitleDiv:function(){return this._91O},getTabModel:function(){return this._tabModel},add:function(g,G,N){var F=new mg,a=this._tabModel;return F.setName(g),F.setView(G),a.add(F),N&&a.sm().ss(F),F},getLabel:function(g){return g.toLabel()},onPropertyChanged:function(){this.iv()},_7Q:function(O){this._23o=O,this.iv()},get:function(N){var H=this,O=H._tabModel;if(sh(N))return O._roots.get(N);if(sl(N)){var n;return O.each(function(h){N===H.getLabel(h)&&(n=h)}),n}return N instanceof mg?N:f},select:function($){this._tabModel.sm().ss(this.get($))},remove:function(M){var v=this;if(M=v.get(M)){var y=v._tabModel,P=y._roots.indexOf(M);v._tabModel.remove(M),v._12Q(--P)}},getCurrentTab:function(){return this._8o},hideTabView:function(k,A){A.parentNode===this._92O&&(Ok(A)?(A.style.display=Wd,A._tab_=k):vc(A),mn())},showTabView:function(l,P){Ok(P)&&(P.style.display="block",P._tab_=l),P.parentNode!==this._92O&&(nj(this._92O,P),mn())},_24o:function(){var q,n=this,z=n._8o,t=n._9o,D=n._tabModel.sm().ld();D&&(q=aq(D.getView())),q!==t&&(t&&(z.getView().endEditing&&z.getView().endEditing(),n.hideTabView(z,t)),q&&n.showTabView(D,q)),n._8o=D,n._9o=q,z!==D&&n.onTabChanged(z,D)},onTabChanged:function(){},onTabClosed:function(Q,U){this._12Q(--U)},_12Q:function(t){var w=this,D=w._tabModel,y=D.size();if(y&&!D.sm().ld()){t==f&&(t=0),t>=y&&(t=y-1),0>t&&(t=0);for(var J=t;J>=0;J--){var k=w.get(J);if(!k.isDisabled())return w.select(k),k}for(J=t+1;y>J;J++)if(k=w.get(J),!k.isDisabled())return w.select(k),k}},_9Q:function(z){var S=4,Q=cj(z.getIcon());Q&&(S+=eq(Q,z)+4);var d=this.getLabel(z);return d&&(S+=Hn(this.getLabelFont(z),d).width+4),z.isClosable()&&(S+=10),S},_10Q:function(D,s,b,Y,I,q,h){var f,X=this,C=X._tabPosition,F=b+4,E=X._selectWidth,w=cj(s.getIcon()),Q=s.isDisabled(),x=X.getLabelColor(s),m=X.getLabelFont(s),y=X.getLabel(s),L=X._selectBackground,U=C===S+"-vertical",N=C===_+"-vertical";(U||N)&&(F=Y+4),Q&&(D.globalAlpha=is),$i(D,b,Y,I,q,h),s===X._8o&&E&&(C===xi?$i(D,b,Y+q-E,I,E,L):C===S?$i(D,b+I-E,Y,E,q,L):C===_?$i(D,b,Y,E,q,L):U?$i(D,b+I-E,Y,E,q,L):N?$i(D,b,Y,E,q,L):$i(D,b,Y,I,E,L));var i=I/2;if(U&&(On(D,i,Y+q/2),qr(D,p),On(D,-i,-Y-q/2)),w){var A=Oo(w,s),V=eq(w,s);if(U||N){var H=b+I/2,k=F+A/2;On(D,H,k),qr(D,v),On(D,-H,-k),jm(D,w,b+I/2,F+A/2,s,X),On(D,H,k),qr(D,-v),On(D,-H,-k),F+=A+4}else jm(D,w,F+V/2,Y+q/2,s,X),F+=V+4}return U||N?(On(D,I/2,F+I/2),qr(D,v),On(D,-I/2,-F-I/2),tj(D,y,m,x,b,F,q,I),On(D,I/2,F+I/2),qr(D,-v),On(D,-I/2,-F-I/2)):tj(D,y,m,x,F,Y,I,q),U&&(On(D,i,Y+q/2),qr(D,-p),On(D,-i,-Y-q/2)),s.isClosable()&&(D.strokeStyle=x,D.lineWidth=1,D.beginPath(),N?(D.moveTo(b+I-10,Y+q-4),D.lineTo(b+I-4,Y+q-10),D.moveTo(b+I-4,Y+q-4),D.lineTo(b+I-10,Y+q-10),D.stroke(),f={x:b+I-12,y:Y+q-12,width:12,height:12}):(D.moveTo(b+I-10,Y+4),D.lineTo(b+I-4,Y+10),D.moveTo(b+I-4,Y+4),D.lineTo(b+I-10,Y+10),D.stroke(),f={x:b+I-12,y:Y,width:12,height:12})),Q&&(D.globalAlpha=1),f},validateImpl:function(){var Y=this;Y._24o();var W,q=Y._canvas,n=Y._tabPosition,$=Y._91O,e=Y._92O,J=Y._tabModel,k=Y.getWidth(),l=Y.getHeight(),T=Y._tabHeight,O=Y._7o,g=Y._tabGap,t=n===xi,Z=n===S,B=n===Kh,x=n===_,V=n===S+"-vertical",H=n===_+"-vertical",U=Y._23o,v=0;if((Z||x)&&J._roots.each(function(x){v=b(Y._9Q(x),v)}),t?(tf($,0,0,k,T),W={x:0,y:T,width:k,height:b(0,l-T)}):Z?(tf($,0,0,v,l),W={x:v,y:0,width:b(0,k-v),height:l}):x?(tf($,k-v,0,v,l),W={x:0,y:0,width:b(0,k-v),height:l}):V?(tf($,0,0,T,l),W={x:T,y:0,width:b(0,k-T),height:l}):H?(tf($,k-T,0,T,l),W={x:0,y:0,width:b(0,k-T),height:l}):(tf($,0,l-T,k,T),W={x:0,y:0,width:k,height:b(0,l-T)}),tf(e,W),V||H){Y._9o&&(W.x=0,tf(Y._8o.getView(),W)),Gr(q,T,l);var C=cs(q),w=0;if(lk(C,0,Y.ty(),1),C.clearRect(0,0,T,l),O.clear(),J._roots.each(function(S){var G,q=Y._9Q(S);U&&U.tab===S||(G=Y._10Q(C,S,0,w,T,q,Y._tabBackground),G&&H&&(G.x=k-12)),O.add({_75o:G,tab:S,startY:w,endY:w+q,height:q}),w+=q+g}),Y._23Q=b(0,w-g),U){var X=U.position;Y._10Q(C,U.tab,0,U.startY,T,U.height,Y._moveBackground),$i(C,0,X,T,1,Y._insertColor)}C.restore(),Y.ty(Y.ty())}else if(Z||x){Y._9o&&(W.x=0,tf(Y._8o.getView(),W)),Gr(q,v,l);var C=cs(q),w=0;if(lk(C,0,Y.ty(),1),C.clearRect(0,0,v,l),O.clear(),J._roots.each(function(V){var R;U&&U.tab===V||(R=Y._10Q(C,V,0,w,v,T,Y._tabBackground),R&&x&&(R.x=k-12)),O.add({_75o:R,tab:V,startY:w,endY:w+T,height:T}),w+=T+g}),Y._23Q=b(0,w-g),U){var X=U.position;Y._10Q(C,U.tab,0,U.startY,v,U.height,Y._moveBackground),$i(C,0,X,v,1,Y._insertColor)}C.restore(),Y.ty(Y.ty())}else{Y._9o&&(W.y=0,tf(Y._8o.getView(),W)),Gr(q,k,T);var C=cs(q),h=0;if(lk(C,Y.tx(),0,1),C.clearRect(0,0,k,T),O.clear(),J._roots.each(function(G){var r,w=Y._9Q(G);U&&U.tab===G||(r=Y._10Q(C,G,h,0,w,T,Y._tabBackground),r&&B&&(r.y=l-T)),O.add({_75o:r,tab:G,startX:h,endX:h+w,width:w}),h+=w+g}),Y._64I=b(0,h-g),U){var X=U.position;Y._10Q(C,U.tab,U.startX,0,U.width,T,Y._moveBackground),um(C,Y._insertColor,X,0,T)}C.restore(),Y.tx(Y.tx())}for(var y=[],s=e.children,D=0;D<s.length;D++){var I=s[D],u=I._tab_;u&&!J.contains(u)&&y.push(I)}y.forEach(function(H){e.removeChild(H)})}});var De=function(c){this.tv=c,this.addListeners()};gf(De,M,{ms_listener:1,getView:function(){return this.tv._91O},handle_mousewheel:function(A){this.handleScroll(A,10*(A.wheelDelta/40))},handle_DOMMouseScroll:function(H){this.handleScroll(H,10*-H.detail)},handleScroll:function(i,K){np(i);var v=this.tv,O=v._tabPosition;!v._40o()||O!==xi&&O!==Kh||v.tx(this.tv.tx()+K),!v._41o()||O!==S&&O!==_&&O!==S+"-vertical"&&O!==_+"-vertical"||v.ty(this.tv.ty()+K)},_8Q:function(H){var D,t,b=this.tv,z=b._tabPosition,o=b._7o;if(z===xi||z===Kh){var v=b.lp(H).x;for(D=0;D<o.size();D++)if(t=o.get(D),t.startX<=v&&v<=t.endX)return t}else if(z===S||z===_||z===S+"-vertical"||z===_+"-vertical"){var n=b.lp(H).y;for(D=0;D<o.size();D++)if(t=o.get(D),t.startY<=n&&n<=t.endY)return t}return f},isClickable:function(K){var f=this.tv,D=f._tabPosition,M=this._73o=this._8Q(K);return!f._40o()||D!==xi&&D!==Kh?!f._41o()||D!==S&&D!==_&&D!==S+"-vertical"&&D!==_+"-vertical"?M&&(!M.tab.isDisabled()||f.isMovable()):!0:!0},handle_mousemove:function(f){var Y=this;Bl?Y._74o=Y._8Q(f):Y.getView().style.cursor=Y.isClickable(f)?Vi:""},handle_mousedown:function(Q){this.handle_mousemove(Q),this.handle_touchstart(Q)},handle_touchstart:function(g){var E=this,B=E.tv,y=B._tabPosition;np(g),E.isClickable(g)&&(y===xi||y===Kh?(E.x=Ee(g).x,E.lp=B.lp(g),E.tx=B.tx()):(E.y=Ee(g).y,E.lp=B.lp(g),E.ty=B.ty()),Mk(E,g))},handleWindowMouseMove:function(P){this.handleWindowTouchMove(P)},handleWindowTouchMove:function(m){var A,y=this,k=y.tv,S=k._tabPosition,v=y._73o;if(S===xi||S===Kh){if(A=Ee(m).x-y.x,!y._25o&&!y.moving&&J(A)>2&&(k._40o()&&!Sq(m)?y._25o=1:v&&k.isMovable()&&(y.moving=1)),y._25o)k.tx(y.tx+A);else if(y.moving){var x=y.lp.x+A,j=k._tabGap/2;k._7o.each(function(t){var L=t.endX,w=x-t.startX<L-x;x>=t.startX&&L>=x&&k._7Q({tab:v.tab,startX:v.startX+A,width:v.width,front:w,insertTab:t.tab,position:w?b(0,t.startX-j):O(k._64I,L+j)})})}}else if(A=Ee(m).y-y.y,!y._25o&&!y.moving&&J(A)>2&&(k._41o()&&!Sq(m)?y._25o=1:v&&k.isMovable()&&(y.moving=1)),y._25o)k.ty(y.ty+A);else if(y.moving){var a=y.lp.y+A,j=k._tabGap/2;k._7o.each(function(X){var i=X.endY,h=a-X.startY<i-a;a>=X.startY&&i>=a&&k._7Q({tab:v.tab,startY:v.startY+A,height:v.height,front:h,insertTab:X.tab,position:h?b(0,X.startY-j):O(k._23Q,i+j)})})}},handleWindowMouseUp:function(z){this.handleWindowTouchEnd(z)},handleWindowTouchEnd:function(){var j=this,$=j.tv,q=$._tabPosition,Z=$.getTabModel(),G=Z._roots,o=j._73o;if(j.moving){var h=$._23o;if(h&&h.insertTab!==h.tab){var u=h.tab,m=G.remove(u),x=G.indexOf(h.insertTab);x>=0&&(h.front||x++,x<=G.size()&&(G.add(u,x),Z._38I(u,m,x)))}$._7Q(f),delete j.moving}else if(!j._25o&&o){u=o.tab;var a=j._74o;if(!a||a.tab===u)if(!u.isDisabled()&&Yn(o._75o,j.lp)){var N=G.indexOf(u);Z.remove(u),$.onTabClosed(u,N)}else u.isDisabled()||$._8o===u||Z.sm().ss(u)}j._25o=j._73o=j._74o=q===xi||q===Kh?j.x=j.lp=j.tx=f:j.y=j.lp=j.ty=f}}),bn.PropertyView=function(e){var o=this;o._view=Xb(1),o._canvas=Pf(o._view),nj(o._view,o._79O=Xb()),o._rows=new Xc,o._28o=new Xc,o._26o={},o._26Q={};var E=o._propertyModel=new Db,t=o.ivm;E.mm(t,o),E.md(t,o),E.mh(t,o),o.dm(e?e:new Db),new sn(o)},Wb("PropertyView",M,{ms_ac:[bg,Jf,id,Xq,Ce,"categorizable",Pp,An,bp,Oh,gg,zs,kd,cn,"selectRowIndex",Zq,"background",ve,He,Cq,po,le],ms_v:1,ms_dm:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,ms_lp:1,ms_vs:1,_45o:1,_47o:1,ms_tip:1,_29I:Df,_59I:0,_9I:0,_selectRowIndex:-1,_editable:!0,_batchEditable:!0,_categorizable:!0,_indent:cp,_background:u.propertyViewBackground,_expandIcon:u.propertyViewExpandIcon,_collapseIcon:u.propertyViewCollapseIcon,_scrollBarColor:pj,_scrollBarSize:xb,_autoHideScrollBar:Wi,_selectBackground:u.propertyViewSelectBackground,_rowHeight:kq,_rowLineVisible:u.propertyViewRowLineVisible,_rowLineColor:u.propertyViewRowLineColor,_10I:.5,_columnLineVisible:u.propertyViewColumnLineVisible,_columnLineColor:u.propertyViewColumnLineColor,_labelColor:u.propertyViewLabelColor,_labelSelectColor:u.propertyViewLabelSelectColor,_labelFont:u.propertyViewLabelFont,getRows:function(){return this._rows},getColumnPosition:function(){return this._10I},setColumnPosition:function(e){0>e&&(e=0),e>1&&(e=1);var N=this,o=N._10I;N._10I=e,N.fp("columnPosition",o,e)},getPropertyName:function(p){return p.toLabel()},getLabelFont:function(){return this._labelFont},getLabelColor:function(t,K,N){return N===this._selectRowIndex?this._labelSelectColor:this._labelColor},getPropertyFont:function(){return this._labelFont},getPropertyColor:function(c,K){return K===this._selectRowIndex?this._labelSelectColor:c.getColor()||this._labelColor},getCategoryFont:function(){return this._labelFont},getCategoryColor:function(){return this._labelColor},adjustTranslateX:function(){return 0},adjustTranslateY:function(P){var z=this.getHeight()-this._59I;return z>P&&(P=z),P>0?0:K(P)},isExpanded:function(d){if(!d)return!0;var I=this._26o[d];return I?I.isExpanded:!(this._26Q[d]===!1)},toggle:function(C){var K=this;
K.isExpanded(C)?K.collapse(C):K.expand(C)},expandAll:function(){this.validate();for(var G in this._26o)this.expand(G)},expand:function(l){if(l&&l!==Wd){var P=this,K=P._26o[l];K&&!K.isExpanded&&(K.isExpanded=!0,P.onExpanded(l),P.ivm())}},onExpanded:function(){},collapseAll:function(){this.validate();for(var D in this._26o)this.collapse(D)},collapse:function(l){if(l&&l!==Wd){var r=this,q=r._26o[l];q&&q.isExpanded&&(q.isExpanded=!1,r.onCollapsed(l),r.ivm())}},onCollapsed:function(){},getCategoryName:function(E){if(!this.isCategorizable())return Wd;var q=E.getCategoryName();return q?q:Wd},getPropertyModel:function(){return this._propertyModel},setDataModel:function(N){var O=this,d=O._dataModel;d!==N&&(d&&(d.umd(O.handlePropertyChange,O),O._3o||d.sm().ums(O.ivm,O)),O._dataModel=N,N.md(O.handlePropertyChange,O),O._3o?O._3o._21I(N):N.sm().ms(O.ivm,O),O.fp(ar,d,N))},isVisible:function(o){return this._visibleFunc?this._visibleFunc(o):!0},onPropertyChanged:function(m){var K=this,B=m.property;Rq[B]?K.ivm():K.iv(),B===Cg&&K._43o()},getCurrentData:function(){return this._27o},updateCurrentData:function(){this._27o=this.sm().ld()},getRawProperties:function(){return this._27o?this._propertyModel._roots:$c},handlePropertyChange:function(o){this._27o===o.data&&this.iv()},ivm:function(){this.invalidateModel()},invalidateModel:function(){var c=this;c._96I||(c.setSelectRowIndex(-1),c._96I=1,c.iv())},redraw:function(){this.iv()},validateModel:function(){var r=this,L=r._rows,W=r._28o,q={},G=new Xc,B=r._27o;r.updateCurrentData(),B!==r._27o&&r.endEditing(),L.clear(),W.clear(),r.getRawProperties().each(function(T){if(r.isVisible(T)){G.add(T);var o=r.getCategoryName(T);q[o]||(W.add(o,o===Wd?0:j),q[o]={isExpanded:r.isExpanded(o),properties:new Xc})}}),r._sortFunc&&G.sort(r._sortFunc);for(var m in r._26o)r._26Q[m]=r.isExpanded(m);r._26o=q,W.each(function(D){D!==Wd&&L.add(D);var b=q[D];b.isExpanded&&G.each(function(p){r.getCategoryName(p)===D&&(b.properties.add(p),L.add({property:p,data:r._27o}))},r)})},validateImpl:function(){var E=this;E._76o(),E._96I&&(E.validateModel(),delete E._96I);var K=E._canvas,d=E.getWidth(),x=E.getHeight(),H=-E.ty(),C=E._rowHeight,D=E._indent,o=d-D,Y=E._rows,u=Y.size(),X=E._9I=D+o*E._10I,U=E._59I=u*C;Gr(K,d,x),E._29I={x:0,y:H,width:d,height:x},E._31I=T(H/C),E._14I=a((H+x)/C),E._31I<0&&(E._31I=0),E._14I>u&&(E._14I=u);var q,V=cs(K),A=E._background;lk(V,0,-H,1),V.beginPath(),V.rect(0,H,d,x),V.clip(),V.clearRect(0,H,d,x),E._93db(V),A&&$i(V,0,0,D,U,A);for(var z=E._31I;z<E._14I;z++){var e=Y.get(z),H=C*z;if(sl(e))A&&$i(V,D,H,o,C,A),q=cj(E.isExpanded(e)?E._expandIcon:E._collapseIcon),q&&Li(V,q,Ed,0,H,D,C),V.save(),V.beginPath(),V.rect(D,H,o,C),V.clip(),E.drawCategoryName(V,e,z,D,H,o,C),V.restore();else{var v=e.property,g=e.data,q=cj(v.getIcon()),w=E._selectRowIndex===z?E.getSelectBackground():f;if(q&&Li(V,q,Ed,0,H,D,C),w&&$i(V,D,H,o,C,w),V.save(),V.beginPath(),V.rect(D,H,X-D,C),V.clip(),E.drawPropertyName(V,v,z,D,H,X-D,C),V.restore(),!E.isEditing(g,v)){var O=X+1,Q=d-X-1;V.save(),V.beginPath(),V.rect(O,H,Q,C),V.clip(),E._87o(E.drawPropertyValue(V,v,E.getValue(g,v),z,O,H,Q,C,g),z,O,H,Q,C),V.restore()}}E._rowLineVisible&&$i(V,D,H+C-1,o,1,E._rowLineColor)}E._columnLineVisible&&($i(V,X,0,1,U,E._columnLineColor),$i(V,d-1,0,1,U)),E._92db(V),E._93I(),V.restore(),E.ty(E.ty())},drawCategoryName:function(K,w,H,c,U,Y,f){tj(K,w,this.getCategoryFont(w),this.getCategoryColor(w),c,U,Y,f)},drawPropertyName:function(w,f,d,S,D,N,E){return f.drawPropertyName?(f.drawPropertyName(w,f,d,S,D,N,E,this),void 0):(tj(w,this.getPropertyName(f),this.getPropertyFont(f,d),this.getPropertyColor(f,d),S,D,N,E),void 0)},drawPropertyValue:function(J,b,r,l,q,m,P,M,B){return b.drawPropertyValue?b.drawPropertyValue(J,b,r,l,q,m,P,M,B,this):(Rh(J,r,b,this.getLabelFont(b,r,l),this.getLabelColor(b,r,l),q,m,P,M,B,this),void 0)},isPropertyEditable:function(c){return c.isEditable()&&this.isEditable()},setProperties:function($){this._propertyModel.clear(),this.addProperties($)},addProperties:function(k){if(k){var E=this._propertyModel;k.forEach(function(Z){if(!(Z instanceof bl)){var H=Bf(Z.className);Z=vk(H?H:bl,Z)}E.add(Z)})}},getRowIndexAt:function(V){var Z=this,N=T(Z.lp(V).y/Z._rowHeight);return N>=0&&N<Z._rows.size()?N:-1},getPropertyAt:function(q){var z=this,U=z.getRowIndexAt(q);return U>=0?z._rows.get(U).property:f},getToolTip:function(L){var V=this,d=V.getPropertyAt(L),H=V._27o;return d&&H?d.getToolTip(H,V._9I<V.lp(L).x,V):f}});var sn=function(Q){this.pv=Q,this.addListeners()};gf(sn,M,{ms_listener:1,getView:function(){return this.pv._view},setCursor:function(r){this.getView().style.cursor=r},clear:function(){var n=this;n._62O=n.cp=n.ty=n.p=f,n.setCursor(r)},handle_mousedown:function(V){this.handle_touchstart(V)},handle_touchstart:function(e){var q=this,g=q.pv;np(e),g.setFocus(e)&&(Fj(e)?(q.cp=Ee(e),q.ty=g.ty(),q.p=g.getColumnPosition(),q.handle_touchmove(e)):g.setSelectRowIndex(g.getRowIndexAt(e)))},handleWindowMouseUp:function(){this.clear()},handleWindowTouchEnd:function(){this.clear()},handle_mouseup:function(R){this.handle_touchend(R)},handle_touchend:function(S){var l=this;if(l.cp&&!l._62O){var Q=l.pv,A=Q.lp(S),p=A.x,u=A.y,b=Q._indent,a=Q.getRowIndexAt(S),E=Q._9I;if(a>=0){var Z=Q._rowHeight,y=Z*a,h=Q._rows.get(a),r=h.property;if(sl(h))cj(Q.isExpanded(h)?Q._expandIcon:Q._collapseIcon)&&p>=0&&b>=p&&u>=y&&y+Z>=u?Q.toggle(h):rs(S)&&Q.toggle(h);else if(p>E&&Q.isPropertyEditable(r)){var z={x:E+1,y:y,width:Q.getWidth()-E-1,height:Z},L={x:z.x+Q.tx(),y:z.y+Q.ty(),width:z.width,height:z.height},C=0,g=Q._29I;z.y<g.y?C=z.y-g.y:z.y+Z>g.y+g.height&&(C=z.y+Z-g.y-g.height),C&&(Q.ty(Q.ty()-C),L.y-=C),Q.beginEditing({data:h.data,property:r,value:Q.getValue(h.data,r),event:S,rect:z,editorRect:L,view:Q})}}Q.setSelectRowIndex(a)}l.clear()},handleWindowMouseMove:function(q){this.handleWindowTouchMove(q)},handleWindowTouchMove:function(N){var J=this,m=J.pv,R=J.ty,A=J.cp,u=Ee(N),n=J._62O;if("p"===n)m.setTranslateY(R+u.y-A.y);else if("c"===n){var k=m.getWidth()-m._indent;if(k>16){var M=J.p-(A.x-u.x)/k,F=16/k;F>M&&(M=F),M>1-F&&(M=1-F),m.setColumnPosition(M)}}else"s"===n&&m.setTranslateY(R+(A.y-u.y)*m._59I/m._29I.height)},handle_mousemove:function(u){this.handle_touchmove(u)},handle_touchmove:function(a){if(!Bl&&Fj(a)){var I=this,p=I.pv,P=J(p.lp(a).x-p._9I)<=(w?8:3);I.cp?I._62O||(P?(I._62O="c",Mk(I)):J(Ee(a).y-I.cp.y)>=2&&(I._62O=I.isV(a)?"s":"p",Mk(I))):(P?I.setCursor(vp):I.setCursor(r),I.isV(a)&&p._43o())}},isV:function(N){var L=this.pv,g=L._29I;return L._41o()&&g.x+g.width-L.lp(N).x<eo},handle_mousewheel:function(Z){this.handleScroll(Z,Z.wheelDelta/40)},handle_DOMMouseScroll:function(o){2===o.axis&&this.handleScroll(o,-o.detail)},handleScroll:function(q,O){var m=this.pv;np(q),m.endEditing(),m.translate(0,O*m.getRowHeight())},handle_keydown:function(s){var R=this.pv,K=R._rows.size(),Q=R._selectRowIndex+(so(s)?-1:1);(so(s)||uq(s))&&(0>Q&&(Q=0),Q>=K&&(Q=K-1),R.setSelectRowIndex(Q))}}),bn.ListView=function(Y){this._5o(Y),new Ik(this)},Wb("ListView",M,{ms_ac:[bg,Jf,id,ve,Pp,He,Cq,An,bp,zs,kd,cn,ic,Zq],ms_v:1,ms_bnb:1,ms_tip:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,ms_lp:1,ms_vs:1,ms_hs:1,_15Q:1,_14Q:1,ms_dm:1,_45o:1,_checkMode:!1,_indent:cp,_rowHeight:kq,_rowLineVisible:u.listViewRowLineVisible,_rowLineColor:u.listViewRowLineColor,_scrollBarColor:pj,_scrollBarSize:xb,_autoMakeVisible:or,_autoHideScrollBar:Wi,_selectBackground:u.listViewSelectBackground,_labelColor:u.listViewLabelColor,_labelSelectColor:u.listViewLabelSelectColor,_labelFont:u.listViewLabelFont,isCheckMode:function(){return this._checkMode},setCheckMode:function(r){var d=this,h=d._checkMode;d._checkMode=r,d.fp(fp,h,r)},drawRow:function(P,U,F,I,C,q,A){var b=this,a=0,V=b._indent,J=b.getIconWidth(U);b.drawRowBackground(P,U,F,I,C,q,A),b._checkMode&&(jm(P,b.getCheckIcon(U),a+V/2,C+A/2,U,b),a+=V),b.drawIcon(P,U,a,C,J,A),b.drawLabel(P,U,a+J,C,A)}});var Ik=function(O){this.list=O,this.addListeners()};gf(Ik,M,{ms_listener:1,getView:function(){return this.list._view},clear:function(j){var l=this;j&&"d"===l._62O&&l.list.handleDragAndDrop(j,"end"),l._62O=l.cp=l.tx=l.ty=f},handle_mousedown:function(y){this.handle_touchstart(y)},handle_touchstart:function(V){var Q=this,$=Q.list;if(np(V),$.setFocus(V))if(Q.cp=Ee(V),Q.tx=$.tx(),Q.ty=$.ty(),Fj(V))Q.isV(V)||Q.isH(V)||!$.handleDragAndDrop||$.handleDragAndDrop(V,"prepare");else{var X=$.getDataAt(V);X&&Q._33o(V,X),Q.clear(V)}},handleWindowMouseUp:function(l){this.clear(l)},handleWindowTouchEnd:function(m){this.clear(m)},handle_mouseup:function(n){this.handle_touchend(n)},handle_touchend:function(K){var t=this,z=t.list;if(t.cp&&!t._62O){var d=z.getDataAt(K);d&&(z.isCheckMode()?t._34o(K,d):t._33o(K,d),rs(K)?z.onDataDoubleClicked(d,K):z.onDataClicked(d,K))}t.clear(K)},handleWindowMouseMove:function(E){this.handleWindowTouchMove(E)},handleWindowTouchMove:function(O){var M=this,d=M.list,p=M._62O,L=M.tx,E=M.ty,Q=M.cp,_=Ee(O),z=d._29I;"p"===p?d.setTranslate(L+_.x-Q.x,E+_.y-Q.y):"v"===p?d.ty(E+(Q.y-_.y)*d._59I/z.height):"h"===p?d.tx(L+(Q.x-_.x)*d._91I/z.width):"d"===p&&d.handleDragAndDrop(O,"between")},handle_mousemove:function(g){this.handle_touchmove(g)},handle_touchmove:function(n){if(!Bl&&Fj(n)){var t=this,F=t.list,q=t.isV(n),A=t.isH(n);if(t.cp){if(!t._62O){if(nm(Ee(n),t.cp)<2)return;q?t._62O="v":A?t._62O="h":F.handleDragAndDrop?(t._62O="d",F.handleDragAndDrop(n,"begin")):t._62O="p",Mk(t)}}else q&&F._43o(),A&&F._42o()}},isV:function(J){var j=this.list,E=j._29I;return j._41o()&&E.x+E.width-j.lp(J).x<eo},isH:function(H){var k=this.list,u=k._29I;return k._40o()&&u.y+u.height-k.lp(H).y<eo},handle_mousewheel:function(N){this.handleScroll(N,N.wheelDelta/40,N.wheelDelta!==N.wheelDeltaX)},handle_DOMMouseScroll:function(i){this.handleScroll(i,-i.detail,1)},handleScroll:function($,J,B){var N=this.list;np($),N.endEditing&&N.endEditing(),B&&N._41o()?N.translate(0,J*N.getRowHeight()):N._40o()&&N.translate(10*J,0)},handle_keydown:function(e){var A,_=this.list,r=_.sm(),d=_._rows,C=d.size();if(ui(e))_.selectAll();else if(un(e))_.handleDelete&&_.handleDelete(e);else if(td(e))_.isCheckMode()&&(A=_.getFocusData(),A&&_.checkData(A));else if(so(e)||uq(e)){var h=_.isCheckMode();if(A=h?_.getFocusData():r.ld()){var T=_.getRowIndex(A);T>=0&&(so(e)?0!==T&&(A=d.get(T-1),h?_.setFocusData(A):r.ss(A)):T!==C-1&&(A=d.get(T+1),h?_.setFocusData(A):r.ss(A)))}else C>0&&(A=d.get(0),h?_.setFocusData(A):r.ss(A))}},_34o:function($,l){var r=this.list,M=r.lp($).x;return M>=0&&M<=r._indent?(r.checkData(l),void 0):(r.setFocusData(l),void 0)},_33o:function(y,i){var J=this.list,f=J.sm(),_=f.ld();if(Sq(y))J.isSelected(i)?f.rs(i):f.as(i);else if(Cm(y))if(_)for(var l=J.getRowIndex(_),c=J.getRowIndex(i);l!==c;)l+=c>l?1:-1,f.as(J._rows.get(l));else f.ss(i);else(Fj(y)||!f.contains(i))&&f.ss(i)}}),bn.TreeView=function(B){var t=this;t._35o(),t._5o(B),new nb(t)},Wb("TreeView",M,{ms_ac:[bg,Jf,id,"rootVisible",fp,"rootData",An,bp,zs,kd,cn,Pp,ve,He,Cq,Oh,gg,ic,Zq,"loader"],ms_v:1,ms_bnb:1,ms_tip:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,ms_lp:1,ms_vs:1,ms_hs:1,_15Q:1,ms_dm:1,_48o:1,_14Q:1,_45o:1,_checkMode:f,_indent:cp,_rowHeight:kq,_rowLineVisible:u.treeViewRowLineVisible,_rowLineColor:u.treeViewRowLineColor,_scrollBarColor:pj,_scrollBarSize:xb,_autoHideScrollBar:Wi,_expandIcon:u.treeViewExpandIcon,_collapseIcon:u.treeViewCollapseIcon,_autoMakeVisible:or,_selectBackground:u.treeViewSelectBackground,_labelColor:u.treeViewLabelColor,_labelSelectColor:u.treeViewLabelSelectColor,_labelFont:u.treeViewLabelFont,drawRow:function(j,y,a,D,U,Y,R){var S=this;S.drawRowBackground(j,y,a,D,U,Y,R),S._97I(j,y,a,0,U,Y,R)}});var nb=function(h){Wn(nb,this,[h])};gf(nb,Ik,{toggle:function(U,S,L,K){var u=this.list,E=u.lp(U).x;if(cj(u.getToggleIcon(S))){var Y=L*K;if(E>=Y&&Y+L>=E)return u.toggle(S),!0}return rs(U)?(u.toggle(S),!0):!1},_34o:function(v,$){var q=this.list,V=q.lp(v).x,c=q._levelMap[$._id],l=q._indent,L=l*(c+1);return V>=L&&L+l>=V?(q.checkData($),void 0):(this.toggle(v,$,l,c)||q.setFocusData($),void 0)},_33o:function(m,c){var G=this,b=G.list;G.toggle(m,c,b._indent,b.getLevel(c))||nb.superClass._33o.call(G,m,c)},handle_keydown:function(k){if(qn(k)||zk(k)){var I=this.list,V=I._rows,Y=I.isCheckMode(),R=I.sm(),m=Y?I.getFocusData():R.ld();m?m.hasChildren()&&(qn(k)?I.collapse(m):I.expand(m)):V.size()>0&&(m=V.get(0),Y?I.setFocusData(m):R.ss(m))}else nb.superClass.handle_keydown.call(this,k)}});var cd=bn.TableView=function(T){this._98I(),this._5o(T),new cc(this)};Wb("TableView",M,{ms_ac:[bg,Jf,id,"sortMode",Xq,Ce,ve,He,Cq,po,le,"sortColumn",An,bp,zs,kd,cn,ic,Zq],ms_v:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,ms_lp:1,ms_vs:1,ms_hs:1,_15Q:1,ms_dm:1,ms_tip:1,_50o:1,_45o:1,_47o:1,_sortMode:ii,_editable:!0,_batchEditable:!1,_rowHeight:kq,_rowLineVisible:u.tableViewRowLineVisible,_rowLineColor:u.tableViewRowLineColor,_columnLineVisible:u.tableViewColumnLineVisible,_columnLineColor:u.tableViewColumnLineColor,_scrollBarColor:pj,_scrollBarSize:xb,_autoHideScrollBar:Wi,_autoMakeVisible:or,_selectBackground:u.tableViewSelectBackground,_labelColor:u.tableViewLabelColor,_labelSelectColor:u.tableViewLabelSelectColor,_labelFont:u.tableViewLabelFont,getCheckColumn:function(){var _=this;if(!_._31o){var E=_._31o=new Gf;E.setEditable(!0),E.setWidth(40),E.getValue=_.getCheckColumValue,E.drawCell=_.drawCheckColumnCell}return _._31o},isCheckMode:function(){return this._39o.contains(this._31o)},setCheckMode:function(N){var Y=this,E=Y._39o,x=Y.getCheckColumn();N!==Y.isCheckMode()&&(N?E.add(x,0):E.remove(x),Y.fp(fp,!N,N))},getCheckColumValue:function(I,i,O){return O.isSelected(I)},drawCheckColumnCell:function(q,e,a,j,u,A,n,K,$){jm(q,$.getCheckIcon(e),u+n/2,A+K/2,e,$)}});var cc=function(J){Wn(cc,this,[J])};gf(cc,Ik,{_34o:function(L,o){var $=this.list,u=$._31o;if($.isCellEditable(o,u)){var M=$._3Q(u),Y=$.lp(L).x;if(M&&Y>=M.startX&&Y<M.startX+u.getWidth())return $.checkData(o),void 0}$.setFocusData(o),$._37O(o,L)},_33o:function(y,w){cc.superClass._33o.apply(this,arguments),this.list._37O(w,y)}});var Eg=bn.TreeTableView=function(x){var a=this,U=a._4o=new Gf;a._35o(),a._98I(),a._5o(x),U.setDisplayName("Name"),U.setEditable(!0),U.setWidth(180),U.drawCell=Jl,U.getValue=Vb,a._39o.add(U),new sm(a)},Jl=function(V,l,E,F,L,J,_,S,X){X._97I(V,l,E,L,J,_,S)},Vb=function(C,X,D){return D.getLabel(C)};Wb("TreeTableView",M,{ms_ac:[bg,Jf,id,"sortMode",Xq,Ce,Pp,fp,"rootData","rootVisible",An,bp,"sortColumn",Oh,gg,zs,kd,cn,ic,Cq,ve,He,po,le,Zq,"loader"],ms_v:1,ms_bnb:1,ms_fire:1,ms_sm:1,_49o:1,ms_txy:1,ms_lp:1,ms_vs:1,ms_hs:1,_15Q:1,ms_dm:1,ms_tip:1,_50o:1,_48o:1,_14Q:1,_45o:1,_47o:1,_sortMode:ii,_checkMode:f,_editable:!0,_batchEditable:!1,_indent:cp,_rowHeight:kq,_rowLineVisible:u.treeTableViewRowLineVisible,_rowLineColor:u.treeTableViewRowLineColor,_columnLineVisible:u.treeTableViewColumnLineVisible,_columnLineColor:u.treeTableViewColumnLineColor,_expandIcon:u.treeTableViewExpandIcon,_collapseIcon:u.treeTableViewCollapseIcon,_scrollBarColor:pj,_scrollBarSize:xb,_autoHideScrollBar:Wi,_autoMakeVisible:or,_selectBackground:u.treeTableViewSelectBackground,_labelColor:u.treeTableViewLabelColor,_labelSelectColor:u.treeTableViewLabelSelectColor,_labelFont:u.treeTableViewLabelFont,getTreeColumn:function(){return this._4o}});var sm=function(P){Wn(sm,this,[P])};gf(sm,Ik,{_34o:function(G,C){var F=this.list,M=F._4o,D=F._3Q(M),Z=F.lp(G).x;if(D){var L=F._indent,J=D.startX+L*F.getLevel(C);if(cj(F.getToggleIcon(C))&&Z>=J&&J+L>=Z)return F.toggle(C),void 0;if(F.isCellEditable(C,M)&&(J+=L,Z>=J&&J+L>=Z))return F.checkData(C),void 0}F.setFocusData(C),F._37O(C,G)},_33o:function(l,S){var Z=this.list,h=Z.lp(l).x;if(cj(Z.getToggleIcon(S))){var u=Z._3Q(Z._4o);if(u){var A=Z._indent,e=u.startX+A*Z.getLevel(S);if(h>=e&&e+A>=h)return Z.toggle(S),void 0}}sm.superClass._33o.apply(this,arguments),Z._37O(S,l)}});var Rc=bn.TableHeader=function(Y){var r=this,h=r._view=Xb(1),N=r._39o=Y.getColumnModel(),s=r.iv;r.tv=r._tableView=Y,r._60I=new Xc,r._canvas=Pf(h),h.style.background=u.tableHeaderBackground||"",h.style.height=_h+Yl,N.mm(s,r),N.md(s,r),N.mh(s,r),Y.mp(function(I){yi[I.property]&&r.iv()},r),new wn(r),r.iv()};Wb("TableHeader",M,{ms_v:1,ms_lp:1,ms_fire:1,ms_ac:["checkIcon","sortDescIcon","sortAscIcon",bg,id,Pp,"moveBackground","insertColor",po,le,"resizable","movable"],_checkIcon:eh,_movable:!0,_resizable:!0,_labelColor:u.tableHeaderLabelColor,_labelFont:u.tableHeaderLabelFont,_columnLineColor:u.tableHeaderColumnLineColor,_columnLineVisible:u.tableHeaderColumnLineVisible,_sortDescIcon:u.tableHeaderSortDescIcon,_sortAscIcon:u.tableHeaderSortAscIcon,_moveBackground:u.tableHeaderMoveBackground,_insertColor:u.tableHeaderInsertColor,_indent:cp,getCheckIcon:function(){return this._checkIcon},getTableView:function(){return this.tv},getLabel:function(U){return U.toLabel()},getLabelFont:function(){return this._labelFont},getLabelColor:function(H){return H.getColor()||this._labelColor},getLabelAlign:function(u){return u._align},onPropertyChanged:function(){this.iv()},_5Q:function(R){this._61I=R,this.iv()},getLogicalPoint:function(p){return Dn(p,this._canvas,this.tv.tx())},validateImpl:function(){var j=this,M=j._canvas,N=j.getWidth(),$=j.getHeight(),E=j.tv,V=j._60I,v=j._61I,n=-E.tx(),T=0;(N!==M.clientWidth||$!==M.clientHeight)&&Gr(M,N,$),V.clear(),j._39o._roots.each(function(W){if(W.isVisible()){var j=T+W.getWidth();n+N>=T&&j>=n&&V.add({column:W,startX:T}),T=j}});var w=cs(M);if(lk(w,-n,0,1),w.beginPath(),w.rect(n,0,N,$),w.clip(),w.clearRect(n,0,N,$),V.each(function(A){var F=A.column,Y=A.startX,o=F.getWidth();o>0&&(w.save(),w.beginPath(),w.rect(Y,0,o,$),w.clip(),v&&v.column===F||j.drawColumn(w,F,Y,0,o,$),j._columnLineVisible&&$i(w,Y+o-1,0,1,$,j._columnLineColor),w.restore())}),v){var R=v.column,T=v.startX,A=v.position,D=R.getWidth();w.save(),w.beginPath(),w.rect(T,0,D,$),w.clip(),w.fillStyle=j._moveBackground,w.fill(),j.drawColumn(w,R,T,0,D,$),w.restore(),um(w,j._insertColor,A,0,$)}w.restore()},_6Q:function(B){var R=this.tv;return R._31o===B&&R.sm().getSelectionMode()===Z},drawColumn:function(c,H,Y,E,A,x){var s=this,Z=s.tv,f=cj(H.getIcon()),M=s.getLabelAlign(H);if(s._6Q(H)){var G=cj(s._checkIcon);jm(c,G,Y+A/2,E+x/2,H,s)}else{var T=s.getLabel(H),I=s.getLabelFont(H),F=s.getLabelColor(H),d=Hn(I,T).width,m=f?s._indent:0;M===S?(f&&Li(c,f,Ed,Y,E,m,x),tj(c,T,I,F,Y+m,E,A,x,S)):M===_?(f&&Li(c,f,Ed,Y+A-d-m,E,m,x),tj(c,T,I,F,Y,E,A,x,_)):(f&&Li(c,f,Ed,Y+(A-d-m)/2,E,m,x),tj(c,T,I,F,Y+(A-d+m)/2,E,0,x,S))}if(H.isSortable()&&Z.getSortColumn()===H&&(f=cj(H.getSortOrder()===Ec?s._sortAscIcon:s._sortDescIcon))){var h=eq(f,H)/2+2;jm(c,f,M===_?Y+h:Y+A-h,E+x/2,H,Z)}}});var wn=function(n){var m=this;m.th=n,m.tv=n._tableView,m.addListeners()};gf(wn,M,{ms_listener:1,getView:function(){return this.th.getView()},setCursor:function(I){this.getView().style.cursor=I},handle_mousemove:function(O){if(!Bl){var e=this;delete e._29o,e.setCursor(r);for(var h=e.th,j=h._60I,E=h.lp(O).x,M=j.size()-1;M>=0;M--){var $=j.get(M),R=$.column,s=$.startX+R.getWidth();if(h.isResizable()&&J(s-E)<=(w?10:3))return e._29o=$,e.setCursor(vp),void 0;(R.isSortable()||h.isMovable()||R===e.tv.getCheckColumn())&&E>=$.startX&&s>=E&&R.getWidth()>0&&(e._29o=$,e.setCursor(Vi))}}},handle_mousedown:function(n){this.handle_touchstart(n)},handle_touchstart:function(R){var E=this;np(R),E.tv.endEditing(),E.handle_mousemove(R),E._29o&&(E.x=Ee(R).x,E.lx=E.th.lp(R).x,E.w=E._29o.column.getWidth(),Mk(E,R))},handleWindowMouseMove:function(f){this.handleWindowTouchMove(f)},handleWindowTouchMove:function(h){var d=this,N=d.th,z=d.getView().style.cursor,v=d._29o,Y=Ee(h).x-d.x;if(d.resizing||d.moving||(z===vp?d.resizing=1:N.isMovable()&&z===Vi&&J(Y)>2&&(d.moving=1)),d.resizing)v.column.setWidth(d.w+Y);else if(d.moving){var m=d.lx+Y;N._60I.each(function(u){var z=u.startX,J=z+u.column.getWidth();if(m>=z&&J>=m){var B={column:v.column,startX:v.startX+Y,front:J-m>m-z,insertColumn:u.column};B.position=B.front?z:J,N._5Q(B)}})}},_16Q:function(G,R){var w=this,a=w.tv,F=w.th,W=F._checkIcon;if(F._6Q(G)){var l=cj(W),h=w.lx,d=G.getWidth(),j=eq(l,G);if(h>=R+d/2-j&&R+d/2+j>=h){F.setCheckIcon(W===rc?eh:rc);var v=a.sm(),V=a._rows;return W===rc?v.rs(V):v.ss(V),a.onCheckColumnClicked(G),!0}}return!1},handleWindowMouseUp:function(M){this.handleWindowTouchEnd(M)},handleWindowTouchEnd:function(K){var $=this,t=$.tv,N=$.th,z=$._29o;if($.moving){var D=N._61I;if(D&&D.insertColumn!==D.column){var R=D.column,e=t.getColumnModel()._roots,v=e.remove(R),k=e.indexOf(D.insertColumn);k>=0&&(D.front||k++,k<=e.size()&&(e.add(R,k),t.getColumnModel()._38I(R,v,k)))}N._5Q(f),delete $.moving}else if(!$.resizing&&z){R=z.column;var c=N.lp(K).x,Z=z.startX,S=!0;if(N.onColumnClicked){var U=N.onColumnClicked(R,K);U===!1&&(S=!1)}if(S&&c>=Z&&c<=Z+R.getWidth()&&!$._16Q(R,Z)){if(R.isSortable()){var L=t.getSortMode(),J=R.getSortOrder();L===ii?t.getSortColumn()===R?(J===Gk&&t.setSortColumn(f),R.setSortOrder(J===Ec?Gk:Ec)):t.setSortColumn(R):"bistate"===L&&(t.getSortColumn()===R?R.setSortOrder(J===Ec?Gk:Ec):t.setSortColumn(R))}t.onColumnClicked(R,K)}}$._29o=$.resizing=$.x=$.lx=$.w=f}}),bn.TablePane=function(R){this.init(new cd(R))},Wb("TablePane",M,{ms_v:1,_44o:1}),bn.TreeTablePane=function(O){this.init(new Eg(O))},Wb("TreeTablePane",M,{ms_v:1,_44o:1}),bn.Toolbar=function(j){var K=this,d=K._view=Xb(1),F=d.style;F.background=u.toolbarBackground||"",F.height=ei+Yl,K._canvas=Pf(d),K._30o=new Xc,K._90I=new Xc,K.setItems(j||[]),d.handleGroupSelectedChanged=function(n){if(n.isSelected()){var p=n.getGroupId();null!=p&&K._items.forEach(function(o){var Q=o.element;Q&&Q!==n&&Q.setSelected&&Q.getGroupId&&Q.getGroupId()===p&&Q.setSelected(!1)})}},new qo(K)},Wb("Toolbar",M,{ms_v:1,ms_fire:1,ms_tx:1,ms_lp:1,ms_tip:1,ms_value:1,ms_ac:[fh,bg,id,Jf,Zq,"itemGap","separatorColor","currentItem","stickToRight"],_labelColor:u.toolbarLabelColor,_labelSelectColor:u.toolbarLabelSelectColor,_labelFont:u.toolbarLabelFont,_selectBackground:u.toolbarSelectBackground,_itemGap:u.toolbarItemGap,_separatorColor:u.toolbarSeparatorColor,_stickToRight:!1,getSumWidth:function(){return this._64I},getToolTip:function(_){var q=this.getItemInfoAt(_);return q?q.item.toolTip:f},getLabelColor:function(E){return E.selected&&E.type!==rc&&E.type!==_o?this._labelSelectColor:this._labelColor},onPropertyChanged:function(b){this.iv(),b.property===fh&&this._items.forEach(function(T){Oj(T)})},redraw:function(){this.iv()},addItem:function(p,F){var E=this._items;F==f?E.push(p):E.splice(F,0,p),this.fp(fh,f,E)},removeItem:function(E){if(E)for(var Y=this._items,A=0;A<Y.length;A++)E===Y[A]&&(Y.splice(A,1),this.fp(fh,f,Y))},removeItemById:function(E){if(E!=f)for(var D=this._items,C=0;C<D.length;C++)E===D[C].id&&(D.splice(C,1),this.fp(fh,f,D))},getItemById:function(S){if(S!=f)for(var s=this._items,X=0;X<s.length;X++){var g=s[X];if(S===g.id)return g}},getItemInfos:function(){return this._30o},getItemInfoAt:function(F){var a=this,Z=0,S=a._30o,N=a.lp(F),w=N.x,U=N.y;if(U>=0&&U<=a.getHeight())for(;Z<S.size();Z++){var t=S.get(Z);if(t.startX<=w&&w<=t.endX)return t}return f},drawItem:function(p,h,g,C,m){if(h.visible===!1)return 0;var e=this,G=h.disabled;G&&(p.globalAlpha=is);var J=e.drawItemImpl(p,h,g,C,m),F=e._itemGap;return G&&(p.globalAlpha=1),this._currentItem!==h||"separator"===h||h.unfocusable||mb(p,e._separatorColor,g-F/2,0,J+F,C),J},drawItemImpl:function(b,H,K,A,d){var l,R=this,o=R._view,C=R._itemGap,z=A/2,w=H.type,t=H.label,r=R.getLabelFont(H),e=R.getLabelColor(H),T=H.selected,p=cj(H.icon),x=eq(p,H),Q=0,y=H.element,O=x+(t?Hn(r,t).width:0);if("separator"===H)return um(b,R._separatorColor,K,A/4,z),1;if(y){p&&jm(b,p,K+x/2,z),tj(b,t,r,e,K+x,0,0,A);var S=aq(y);d||R._90I.add(S),S.parentNode!==o&&nj(o,S),y.iv&&y.iv(),y.validate&&y.validate();var j=S.getBoundingClientRect(),c=j.width,i=S.style;return he(S),i.left=R.tx()+K+O+Yl,i.top=(A-j.height)/2+Yl,O+c}return w===_o?l=cj(T?jg:jj):w===rc&&(l=cj(T?rc:eh)),l?(Q=eq(l,H),jm(b,l,K+Q/2,z),K+=Q,O+=Q):T&&$i(b,K-C/2,0,O+C,A,R.getSelectBackground(H)),p&&jm(b,p,K+x/2,z),tj(b,t,r,e,K+x,0,0,A),O},validateImpl:function(){var n=this,m=n._canvas,z=n.getWidth(),O=n.getHeight(),_=n._30o,L=n._items;Gr(m,z,O);var V=cs(m),W=n._itemGap,l=W/2;lk(V,n.tx(),0,1),V.clearRect(0,0,z,O);var y=n._90I;n._90I=new Xc,_.clear(),L.forEach(function(X){var Q=n.drawItem(V,X,l,O);_.add({item:X,startX:l,endX:l+Q,width:Q}),Q&&(l+=Q+W)}),y.each(function(H){n._90I.contains(H)||vc(H)}),n._64I=b(0,l),V.restore(),n._stickToRight?(n._65O=0,V=cs(m),l=z-n._64I+W,lk(V,0,0,1),V.clearRect(0,0,z,O),_.clear(),L.forEach(function(a){var G=n.drawItem(V,a,l,O,!0);_.add({item:a,startX:l,endX:l+G,width:G}),G&&(l+=G+W)}),V.restore()):n.tx(n.tx())},handleClick:function(x){var k=this,Z=x.type,N=x.action,W=x.groupId,a=x.selected;x.disabled||(W!=f?a||(x.selected=!0,this._items.forEach(function(v){v.groupId===W&&x!==v&&(v.selected=!1)}),N&&x.action(x,k)):Z===rc||"toggle"===Z?(x.selected=!a,N&&x.action(x,k)):N&&x.action(x,k)),mn(),k.iv()}});var qo=function(w){this.tb=w,this.addListeners()};gf(qo,M,{ms_listener:1,getView:function(){return this.tb._view},handle_mousewheel:function(G){this.handleScroll(G,10*(G.wheelDelta/40))},handle_DOMMouseScroll:function(C){this.handleScroll(C,10*-C.detail)},handleScroll:function(U,t){np(U);var P=this.tb;P.isScrollable()&&!P._stickToRight&&(P.tx(P.tx()+t),xf())},handle_mousemove:function(X){var i=this;Bl?i.info2=i.tb.getItemInfoAt(X):i.setItem(X)},handle_mouseout:function(M){var G=this;M.target===G.getView()?G.tb.setCurrentItem(f):G.handle_mousemove(M)},handle_mousedown:function(C){this.handle_mousemove(C),this.handle_touchstart(C)},handle_touchstart:function(O){var b=this,B=b.tb,G=O.target;(G===b.getView()||G===B._canvas)&&(np(O),B.setFocus(O)&&(this.setItem(O),(B.isScrollable()||b.info&&!b.info.item.disabled)&&(b.x=Ee(O).x,b.tx=B.tx(),Mk(b,O))))},handleWindowMouseMove:function(L){this.handleWindowTouchMove(L)},handleWindowTouchMove:function(H){var g=this,T=g.tb;if(!T._stickToRight){var t=Ee(H).x-g.x;!g._25o&&J(t)>2&&T.isScrollable()&&(g._25o=1),g._25o&&T.tx(g.tx+t)}},handleWindowMouseUp:function(A){this.handleWindowTouchEnd(A)},handleWindowTouchEnd:function(){var z=this,K=z.tb,Q=z.info,b=z.info2;if(!z._25o&&Q){var e=Q.item;b&&b.item!==e||K.handleClick(e)}z._25o=z.x=z.tx=z.info2=f,z.setItem()},setItem:function(b){var z=this,E=z.tb,h=z.info=b?E.getItemInfoAt(b):f;E.setCurrentItem(h?h.item:f)}}),bn.BorderPane=function(){this._view=Xb(1),this.iv()},Wb("BorderPane",M,{ms_v:1,ms_ac:["topHeight","bottomHeight","leftWidth","rightWidth"],ms_fire:1,getLeftView:function(){return this._leftView},setLeftView:function(W,t){this._12o("leftView",W),t!=f&&this.setLeftWidth(t)},getRightView:function(){return this._rightView},setRightView:function(G,i){this._12o("rightView",G),i!=f&&this.setRightWidth(i)},getTopView:function(){return this._topView},setTopView:function(p,A){this._12o("topView",p),A!=f&&this.setTopHeight(A)},getBottomView:function(){return this._bottomView},setBottomView:function(b,t){this._12o("bottomView",b),t!=f&&this.setBottomHeight(t)},getCenterView:function(){return this._centerView},setCenterView:function(I){this._12o("centerView",I)},_12o:function(X,g){var h=this,Y="_"+X,I=h._view,F=h[Y];F!==g&&(F&&(F.getView?vc(F.getView()):vc(F)),h[Y]=g,g&&(g.getView?nj(I,g.getView(),1):nj(I,g,1)),h.fp(X,F,g))},onPropertyChanged:function(){this.iv()},validateImpl:function(){var C=this,q=C._topView,e=C._bottomView,j=C._leftView,k=C._rightView,S=C._centerView,d=C.getWidth(),l=C.getHeight(),w=0,h=0,T=d,Y=l,D=0,g=0,E=0,p=0;q&&(D=C._topHeight==f?Pc(q):C._topHeight,h=D),e&&(g=C._bottomHeight==f?Pc(e):C._bottomHeight,Y=l-g),j&&(E=C._leftWidth==f?Fh(j):C._leftWidth,w=E),k&&(p=C._rightWidth==f?Fh(k):C._rightWidth,T=d-p);var G=b(0,T-w),t=b(0,Y-h);q&&tf(q,0,0,d,D),e&&tf(e,0,Y,d,g),j&&tf(j,0,h,E,t),k&&tf(k,T,h,p,t),S&&tf(S,w,h,G,t)}})}}(this,Object);