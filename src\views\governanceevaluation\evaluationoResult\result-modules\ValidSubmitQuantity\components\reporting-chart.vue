<template>
  <!-- 下级地市情况 -->
  <div class="reporting-chart">
    <div class="ranking-title f-14">
      <i class="icon-font icon-quxian mr-xs"></i>
      <span>区县上报情况</span>
      <Checkbox v-model="sortField" class="fr" @on-change="onChangeSortField">
        <slot name="rank-title">按达标率排序</slot>
      </Checkbox>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: rankInfoList }">
      <draw-echarts
        v-if="rankInfoList.length"
        :echart-option="propertyEchart"
        ref="propertyChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="rankInfoList.length > 20">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('propertyChart', rankInfoList, [], 20)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import dataZoom from '@/mixins/data-zoom';
// import evaluationoverview from '@/config/api/evaluationoverview'
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'reporting-chart',
  mixins: [dataZoom, dealWatch],

  data() {
    return {
      propertyEchart: {},
      echartData: [],
      colorList: [`${$var('--color-green-8')}`, `${$var('--color-orange-5')}`],
      lengName: [],
      // rankList: [],
      echartList: [],
      xAxis: [],
      series: [],
      sortField: false,
      rankInfoLoading: false,
      // rankInfoList:[],
    };
  },
  props: {
    activeIndexItem: {},
    rankInfoList: {
      type: Array,
      default: () => [],
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onChangeSortField(val) {
      this.$emit('changeAreaSortField', val);
    },
    typeRing() {
      this.echartData = [];
      let qualifiedAreaCount = [];
      let unqualifiedAreaCount = [];
      this.lengName = ['达标区县', '不达标区县', '区县达标率'];

      this.rankInfoList.forEach((item) => {
        qualifiedAreaCount.push(item.qualifiedAreaCount || 0);
        unqualifiedAreaCount.push(item.unqualifiedAreaCount || 0);
      });

      let series = [
        {
          name: '达标区县',
          data: qualifiedAreaCount,
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[0] },
        },
        {
          name: '不达标区县',
          data: unqualifiedAreaCount,
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          itemStyle: { color: this.colorList[1] },
        },
        {
          name: '区县达标率',
          type: 'line',
          itemStyle: {
            color: $var('--color-green-7'),
          },
          yAxisIndex: 1,
          data: this.rankInfoList.map((item) => item.standardsValue),
        },
      ];
      let opts = {
        splitLineShow: false,
        xAxis: this.rankInfoList.map((item) => item.regionName),
        series: series,
        lengName: this.lengName,
      };
      this.propertyEchart = this.$util.doEcharts.ReviewConsequenceEcharts(opts);
      setTimeout(() => {
        this.setDataZoom('propertyChart', [], 20);
      });
    },
  },
  watch: {
    rankInfoList: {
      handler() {
        this.typeRing();
      },
      deep: true,
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.reporting-chart {
  position: relative;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 44.5px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.next-echart {
  top: 50%;
  right: 0;
  position: absolute;

  .icon-zuojiantou1 {
    color: rgba(45, 190, 255, 1);
    font-size: 12px;
    vertical-align: top !important;
  }
  &:active {
    .icon-zuojiantou1 {
      color: #4e9ef2;
      font-size: 12px;
      vertical-align: top !important;
    }
  }
  &:hover {
    .icon-zuojiantou1 {
      color: var(--color-primary);
      font-size: 12px;
      vertical-align: top !important;
    }
  }
}
</style>
