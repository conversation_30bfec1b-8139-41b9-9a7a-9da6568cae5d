<template>
  <div class="rule-container">
    <div class="params-content">
      <div v-for="(item, index) in ruleList" :key="index" class="rule-item">
        <Checkbox v-model="item.isConfigure" true-value="1" false-value="0"> </Checkbox>
        <p class="dis-select">
          <span class="base-text-color">{{ `${item.ruleName}：${item.ruleDesc}` }}</span>
          <i
            @click="clickRule(item)"
            v-if="isConfigData.includes(item.ruleCode)"
            class="icon-font icon-systemmanagement f-14 setting dis-select ml-sm"
          ></i>
        </p>
      </div>
    </div>
    <!-- 空值检测   -->
    <empty ref="Empty" :topicType="topicType" @render="getView"></empty>
    <!-- 重复检测   -->
    <RepeatArea ref="RepeatArea" :topicType="topicType" @render="getView"></RepeatArea>
    <repeat ref="Repeat" :topicType="topicType" @render="getView"></repeat>
    <!--  经纬度检测  -->
    <longitude-latitude-precision
      ref="longitudeLatitudePrecision"
      :formData="formData"
      :indexRule="indexRule"
    ></longitude-latitude-precision>
    <ip-address ref="ipAddress"></ip-address>
    <!-- mac -->
    <mac-configuration ref="macConfiguration"></mac-configuration>
    <device-code ref="deviceCode"></device-code>
    <device-name ref="deviceName"></device-name>
    <longitude-latitude-offset
      ref="longitudeLatitudeOffset"
      :formData="formData"
      :indexRule="indexRule"
    ></longitude-latitude-offset>
    <device-status ref="deviceStatus"></device-status>
    <longitude-latitude-out-of-bounds
      ref="longitudeLatitudeOutOfBounds"
      :formData="formData"
      :indexRule="indexRule"
    ></longitude-latitude-out-of-bounds>
    <custom-rule ref="customRule" :formData="formData" :indexRule="indexRule"></custom-rule>
    <data-type-rule ref="dataTypeRule" :formData="formData" :indexRule="indexRule"></data-type-rule>
    <camera-function-type ref="cameraFunctionType" :formData="formData" :indexRule="indexRule"></camera-function-type>
  </div>
</template>

<script>
export default {
  name: 'rule-list',
  components: {
    IpAddress: require('./ip-address').default,
    Empty: require('./empty').default,
    RepeatArea: require('./repeat-area').default,
    LongitudeLatitudePrecision: require('./longitude-latitude-precision').default,
    deviceCode: require('./device-code').default,
    MacConfiguration: require('./mac-configuration').default,
    deviceName: require('./device-name').default,
    Repeat: require('./repeat').default,
    LongitudeLatitudeOffset: require('./longitude-latitude-offset').default,
    DeviceStatus: require('./device-status').default,
    LongitudeLatitudeOutOfBounds: require('./longitude-latitude-outof-bounds').default,
    CustomRule: require('./custom-rule/index').default,
    DataTypeRule: require('./data-type-rule').default,
    CameraFunctionType: require('./camera-function-type').default,
  },
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    moduleAction: {},
    // 配置
    indexRuleList: {
      required: true,
      default: () => {
        [];
      },
    },
    //需要配置的规则
    isConfigData: {
      default: () => [
        '1001',
        '1002',
        '1003',
        '1004',
        '1006',
        '1007',
        '1008',
        '1009',
        '1060',
        '1047',
        '1065', //自定义规则检测
        '1064', //数据格式校验
        '1066', //摄像机功能类型检测
      ],
    },
    topicType: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      fruit: [],
      modalVisible1001: false,
      modalVisible1002: false,
      indexRule: {},
      ruleList: [],
    };
  },
  computed: {},
  watch: {
    indexRuleList: {
      handler: function (val) {
        this.ruleList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    //保存
    async getView() {},
    clickRule(item) {
      this.indexRule = item;
      let processsOptions = {
        indexRuleId: item.ruleId,
        ...this.moduleAction,
      };
      let map = {
        //空值检测
        1001: () => {
          this.$refs.Empty.init(processsOptions);
        },
        //重复检测
        1002: () => {
          if (
            this.moduleAction.indexType === 'SITE_PLACE_ACCURACY' ||
            this.moduleAction.indexType === 'FOCUS_ACCURACY'
          ) {
            return this.$refs.Repeat.init(processsOptions);
          }
          this.$refs.RepeatArea.init(processsOptions);
        },
        //IP地址格式检测
        1003: () => {
          this.$refs.ipAddress.init(processsOptions);
        },
        //填报
        1004: () => {
          this.$refs.macConfiguration.init(processsOptions);
        },
        //设备编码格式检测
        1006: () => {
          this.$refs.deviceCode.init(processsOptions);
        },
        //经纬度精度检测
        1007: () => {
          this.$refs.longitudeLatitudePrecision.init(processsOptions);
        },
        //经纬度越界检测
        1008: () => {
          this.$refs.longitudeLatitudeOutOfBounds.init(processsOptions);
        },
        //经纬度偏移检测
        1009: () => {
          this.$refs.longitudeLatitudeOffset.init(processsOptions);
        },
        //设备名称检测
        1060: () => {
          this.$refs.deviceName.init(processsOptions);
        },
        //设备物理状态检测
        1047: () => {
          this.$refs.deviceStatus.init(processsOptions);
        },
        //自定义规则检测
        1065: () => {
          this.$refs.customRule.init(processsOptions);
        },
        //数据格式校验
        1064: () => {
          this.$refs.dataTypeRule.init(processsOptions);
        },
        //摄像机功能类型检测
        1066: () => {
          this.$refs.cameraFunctionType.init(processsOptions);
        },
      };
      map[item['ruleCode']]();
    },
  },
};
</script>

<style lang="less" scoped>
@import url('~@/views/appraisaltask/components/common.less');
.rule-item {
  display: flex;
}

.setting {
  color: var(--color-primary);
}
</style>
