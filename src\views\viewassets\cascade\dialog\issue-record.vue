<template>
  <div>
    <ui-modal v-model="visible" title="下发记录" :styles="styles" footer-hide>
      <div class="search-module">
        <div>
          <ui-label class="inline" label="组织机构" :width="65">
            <api-organization-tree
              class="tree-style"
              :select-tree="selectOrgTree"
              @selectedTree="selectedOrgTree"
              placeholder="请选择组织机构"
            />
          </ui-label>
          <ui-label class="inline ml-md" :label="`${global.filedEnum.deviceId}`" :width="65">
            <Input v-model="searchData.deviceId" class="width-md" :placeholder="`${global.filedEnum.deviceId}`"></Input>
          </ui-label>
          <ui-label class="inline ml-md" :label="`${global.filedEnum.deviceName}`" :width="65">
            <Input
              v-model="searchData.deviceName"
              class="width-md"
              :placeholder="`${global.filedEnum.deviceName}`"
            ></Input>
          </ui-label>
          <ui-label class="inline ml-md" label="行政区划" :width="65">
            <api-area-tree
              :select-tree="selectTree"
              @selectedTree="selectedArea"
              placeholder="请选择行政区划"
            ></api-area-tree>
          </ui-label>
          <ui-label class="inline ml-md" label="下发时间" :width="65">
            <DatePicker
              class="w160"
              v-model="searchData.startTime"
              type="datetime"
              placeholder="请选择开始时间"
              :options="startTimeOption"
              confirm
            ></DatePicker>
            <span class="ml-sm mr-sm">--</span>
            <DatePicker
              class="w160"
              v-model="searchData.endTime"
              type="datetime"
              placeholder="请选择结束时间"
              :options="endTimeOption"
              confirm
            ></DatePicker>
          </ui-label>
          <div class="inline ml-lg">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="resetHandle">重置</Button>
          </div>
        </div>
      </div>
      <!-- <div class="operate-bar t-right"> <Button  type="text" @click="removeHandle">清空</Button></div> -->
      <div style="margin-top: 20px">
        <ui-table
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :minus-height="minusTable"
          :loading="loading"
        >
          <template #deviceId="{ row }">
            <a>{{ row.deviceId }}</a>
          </template>
          <template #errorMessage="{ row }">
            <span class="font-red">{{ row.errorMessage }}</span>
          </template>
          <template #sbgnlx="{ row }">
            <span>{{ row.sbgnlx | filterType(propertySearchLbgnlx) }}</span>
          </template>
          <template #sbdwlx="{ row }">
            <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
          </template>
          <template #deviceStatus="{ row }">
            <span>{{ row.deviceStatus == 1 ? '可用' : '不可用' }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip
              class="mr-md"
              icon="icon-chakanxiangqing"
              content="信息详情"
              @click.native="recordModalShow(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              icon="icon-chakanyichangxiangqing"
              content="异常原因"
              @handleClick="viewRecord(row)"
            />
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </ui-modal>
    <!--异常原因-->
    <view-detection-field ref="unqualifiedModel" v-model="unqualifiedVisible" />
  </div>
</template>
<style lang="less" scoped>
.success {
  background: @color-success;
}
.error {
  background: @color-failed;
}
.w160 {
  width: 160px;
}
</style>
<script>
import cascade from '@/config/api/cascade';
import { mapActions, mapGetters } from 'vuex';
export default {
  watch: {
    value(val) {
      if (val) {
        // this.searchData.deviceCode = this.viewData.deviceId
        // this.copySearch = this.$util.common.deepCopy(this.searchData);
        this.searchData.configOrgCode = this.$parent.searchData.configOrgCode;
        this.selectOrgTree.orgCode = this.$parent.searchData.configOrgCode;
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg', // 默认组织机构
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
    }),
    tableColumns() {
      let columns = [];
      columns.push(
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          width: 180,
          key: 'deviceId',
          fixed: 'left',
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', tooltip: true },
        { title: '行政区划', key: 'civilName', tooltip: true },
        // { title: '行政区划', key: 'civilCode', tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          width: 150,
          key: 'macAddr',
          tooltip: true,
        },
        { title: this.global.filedEnum.sbgnlx, slot: 'sbgnlx', tooltip: true },
        { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx' },
        { title: '采集区域', key: 'sbcjqyText', tooltip: true },
        {
          width: 120,
          title: this.global.filedEnum.phyStatus,
          slot: 'deviceStatus',
        },
        // { width: 120, title: '异常原因', key: 'errorMsg' },
        {
          width: 150,
          title: '下发时间',
          key: 'createTime',
          tooltip: true /*// slot: 'azsj'*/,
        },
      );
      if (this.needOption) {
        columns.push({
          title: '操作',
          slot: 'option',
          width: 80,
          fixed: 'right',
          align: 'center',
        });
      }
      return columns;
    },
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    viewData: {
      required: true,
      type: Object,
    },
    needOption: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    UiTable: require('@/components/ui-table').default,
    apiOrganizationTree: require('@/api-components/api-organization-tree').default,
    ViewDetectionField: require('./view-detection-field.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree').default,
  },
  data() {
    return {
      visible: false,
      unqualifiedVisible: false,
      styles: { width: '9.15rem' },
      selectTree: { regionCode: '' },
      selectOrgTree: { orgCode: '' },
      searchData: {
        deviceCode: null,
        deviceId: '',
        deviceName: '',
        civilCode: '', // 行政区划
        page: true,
        pageNumber: 1,
        pageSize: 20,
        configOrgCode: '', // 组织结构
        startTime: '',
        endTime: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      minusTable: 400,
      tableData: [],
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  activated() {},
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(cascade.queryDistributeDetailPageList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    checkColor(row) {
      switch (row.checkStatus) {
        case 2:
          return 'success';
        case 3:
          return 'error';
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    recordModalShow(row) {
      this.$emit('recordModalShow', row);
      // this.$emit('recordModalShow', this.viewData, row.checkColumnName)
    },
    resetHandle() {
      this.selectTree.regionCode = '';
      this.selectOrgTree.orgCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    // 清空
    removeHandle() {
      let ids = this.tableData.map((item) => item.id).join();
      this.$UiConfirm({
        content: '清空后不可恢复，确定清空吗？',
        title: '提示',
      })
        .then(() => {
          this.$http.delete(cascade.deviceDetailRemove + ids).then((res) => {
            this.$Message.success(res.data.msg);
            this.init();
          });
        })
        .catch((res) => {
          console.error(res);
        });
    },
    // 选择组织机构
    selectedOrgTree(val) {
      this.searchData.configOrgCode = val.orgCode;
    },

    // 查看不合格原因
    viewRecord(row) {
      this.unqualifiedVisible = true;
      this.$refs.unqualifiedModel.init(row);
    },
  },
};
</script>
