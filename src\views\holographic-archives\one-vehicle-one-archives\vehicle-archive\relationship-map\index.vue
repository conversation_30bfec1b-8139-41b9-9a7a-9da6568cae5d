<template>
  <div class="trajectory">
    <div class="left">
      <BasicInformation
        :labelType="6"
        :baseInfo="basicInfo"
        type="car"
        :imgSrc="imgSrc"
      />
      <!-- <ui-card title="基础信息 " class="m-b20" :padding="20"> -->
      <!-- 信息表格 -->
      <!-- <Card type="car" :baseInfo="vehicleInfo" /> -->
      <!-- </ui-card> -->
    </div>
    <div class="right">
      <graph
        v-if="graphObj && hasGraphData"
        class="graph"
        :has-link="true"
        :has-toolbar="true"
        :layout-data="{
          linkDistance: 200,
        }"
        @finish="graphLoaded"
      ></graph>
      <ui-empty v-else></ui-empty>
    </div>
  </div>
</template>
<script>
import BasicInformation from "@/views/holographic-archives/one-person-one-archives/people-archive/profile/components/basic-information";

import { getVehicleBaseInfo } from "@/api/vehicleArchives";

import { mapGetters } from "vuex";
import relativeGraphMixin2 from "@/views/holographic-archives/mixins/relativeGraphMixin2";

export default {
  mixins: [relativeGraphMixin2],
  components: {
    graph: require("@/views/holographic-archives/components/graph").default,
    BasicInformation,
    Card: require("@/views/holographic-archives/components/relationship-map/basic-information")
      .default,
  },
  props: {},
  data() {
    return {
      graphData: {}, // 图谱数据
      imgSrc: require("@/assets/img/demo/vehicle.png"),
      vehicleInfo: {}, // 车主信息
      basicInfo: {}, // 车主信息
    };
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },

  mounted() {
    const { archiveNo, plateNo, idcardNo } = this.$route.query;
    this.basicInfoFn(JSON.parse(archiveNo));
  },
  methods: {
    basicInfoFn(archiveNo) {
      // 基本信息
      getVehicleBaseInfo(archiveNo).then((res) => {
        console.log("基本信息", res);
        this.basicInfo = res.data;

        // if(res.data.idcardNo) {
        //   // 车主信息
        //   var param = {
        //     archiveNo: res.data.idcardNo,
        //     dataType: 1
        //   }
        //   personBaseInfo(param).then(res2 =>{
        //     console.log('车主信息', res2)
        //     this.vehicleInfo = res2.data || {}
        //     this.basicInfo = [...this.basicInfo, ...this.vehicleInfo]
        //     // this.basicInfo = Object.assign(this.basicInfo, res2.data)
        //     console.log('***', this.basicInfo)
        //     if(this.vehicleInfo.photos.length > 0) {
        //       this.vehicleInfo.imgUrl = this.vehicleInfo.photos[0].photoUrl
        //     }
        //   })

        // }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.trajectory {
  display: flex;
  flex: 1;
  height: 100%;
  padding: 10px;
  padding-top: 16px;
  /deep/.card-content {
    height: calc(~"(100% - 30px)");
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 0 !important;
  }
  /deep/ .ui-card {
    height: 100%;
  }
  .left {
    margin-right: 10px;
    height: 100%;
    width: 350px;
  }
  .right {
    width: calc(~"100% - 360px");
    background: #fff;
    position: relative;
    .graph {
      position: relative;
    }
  }
}
/deep/ .rel-node {
  background-color: transparent !important;
  border: 0 !important;
}
</style>
