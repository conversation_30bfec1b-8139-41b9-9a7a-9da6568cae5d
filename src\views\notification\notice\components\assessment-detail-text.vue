<template>
  <div class="assessmentResult" v-if="assessmentTextObj">
    <!-- <span class="title">#考核月份时间区间#</span>，全省公安视频图像数据治理考核工作正常推进。 -->
    <div v-html="assessmentTextObj.themeContent"></div>
    <!-- 整体来看，本月共考核<span class="title">#指标数#</span>项指标，其中<span class="title">#行政区划（多个行政区划用顿号隔开）#</span>全部达标。
      <span class="title">X</span>项及以上不达标有<span class="title">#行政区划（多个行政区划用顿号隔开）#</span>，希望加强治理。
      另外，<span class="title">#行政区划（多个行政区划用顿号隔开）#</span>有若干指标低于<span class="title">#设置的通报值#</span>，要加速整改。
      <span class="title">#行政区划（多个行政区划用顿号隔开）#</span>本月考核进步最大，较上月排名提升了<span class="title">X</span>名。 -->
    <div v-html="assessmentTextObj.overviewContent"></div>
    <div v-if="!!assessmentTextObj.overviewContentDetail && assessmentTextObj.overviewContentDetail.length">
      <div>以下为有指标不达标的地市：</div>
      <div class="cityList">
        <div v-for="(item, index) in assessmentTextObj.overviewContentDetail" :key="index" v-html="item"></div>
        <!-- <span class="title">地市{{index+1}}</span>：有<span class="title">{{index+1}}</span>个指标考核不达标，其中<span class="title">XXXX</span>指标考核值最低，为<span class="title">45.87%</span>； -->
      </div>
      <div v-if="!!assessmentTextObj.customContent">
        <!-- 此处显示自定义内容 -->
        {{ assessmentTextObj.customContent }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'assessment-detail-text',
  props: {
    assessmentTextObj: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.assessmentResult {
  color: var(--color-content);
  line-height: 26px;
  text-indent: 24px;
  .cityList {
    text-indent: 0;
    padding-left: 24px;
  }
}
</style>
<style lang="less">
.assessmentResult {
  color: var(--color-content);
  line-height: 26px;
  text-indent: 24px;
  .title {
    color: var(--color-title) !important;
    margin: 0 2px;
  }
  .cityList {
    text-indent: 0;
    padding-left: 24px;
  }
}
</style>
