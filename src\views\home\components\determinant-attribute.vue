<template>
  <!-- 关键属性检测 -->
  <div class="determinant-attribute" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-guanjianshuxingjiance" v-model="activeValue" :data="tabData"> </HomeTitle>
    <!-- v-ui-loading="{ loading: echartsLoading, tableData: echartData }" -->
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length != 0"
        :echart-option="determinantEchart"
        :echart-style="ringStyle"
        ref="attributeChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
    </div>
  </div>
</template>
<style lang="less" scoped>
.determinant-attribute {
  position: absolute;
  bottom: 10px;
  width: 25%;
  background: rgba(0, 104, 183, 0.13);
  height: 33%;
  .determinant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 100%;
    height: calc(100% - 32px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
.full-screen-container {
  height: 30.6%;
  margin-left: 10px;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'determinant-attribute',

  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '230px',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      activeValue: 'property',
      tabData: [{ label: '关键属性检测', id: 'property' }],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  mounted() {
    this.queryDeviceCheckColumnReports();
  },

  methods: {
    async queryDeviceCheckColumnReports() {
      this.echartsLoading = true;
      try {
        let res = await this.$http.get(home.queryDeviceCheckColumnReports);
        this.echartData = res.data.data;
        this.initRing();
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyColumn,
          value: row.total,
          deviceRate: row.deviceRate,
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyColumn),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.determinantColumn(opts);
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
  },
};
</script>
