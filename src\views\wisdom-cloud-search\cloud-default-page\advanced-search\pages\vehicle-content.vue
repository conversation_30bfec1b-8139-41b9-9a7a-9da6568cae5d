<template>
  <section class="main-container">
    <div class="search-bar">
      <search-vehicle ref="searchBar" @search="searchHandle" @reset="resetHandle" />
    </div>
    <div class="table-container">
      <div class="data-above" v-if="mapOnData">
        <Checkbox @on-change="checkAllHandler" v-model="checkAll">全选</Checkbox>
        <Button @click="dataAboveMapHandler">
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
        <div class="list-card box-1" v-for="(item, index) in dataList" :key="index" :class="{ checked: item.isChecked }">
          <div class="collection paddingIcon">
            <div class="bg"></div>
            <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
          </div>
          <Checkbox class="check-box" v-if="mapOnData" v-model="item.isChecked" @on-change="e => checkHandler(e, index)">{{ undefined }}</Checkbox>
          <p class="img-content">
            <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
            <ui-image :src="item.traitImg" alt="动态库" @click.native="faceDetailFn(item, index)" />
            <b class="shade vehicle">
              <!-- <plate-number :plateNo="item.plateNo"></plate-number> -->
              <ui-plate-number :plateNo="item.plateNo" :color="item.plateColor" size='mini'></ui-plate-number>
            </b>
          </p>
          <div class="bottom-info">
            <time>
              <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime }}
            </time>
            <p>
              <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
            </p>
          </div>
          <!-- <div class="operate-bar">
            <p class="operate-content">
              <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
              <ui-btn-tip content="分析" icon="icon-fenxi" />
              <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
            </p>
          </div> -->
        </div>
        <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
      </div>
      <ui-empty v-if="dataList.length === 0 && listLoading == false"></ui-empty>
      <ui-loading v-if="listLoading"></ui-loading>
      <!-- 分页 -->
      <ui-page :current="pageInfo.pageNumber" :total="pageInfo.total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[27, 54, 81, 108]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
    </div>
    <!-- 车辆详情 -->
    <!-- <vehicle-detail v-show="vehicleShow" class="vehicleDetail" ref="vehicleDetail" @close="vehicleShow = false" 
    @preDetial="preDetial" @nextDetail="nextDetail"/> -->
    <details-vehicle-modal 
        v-show="vehicleShow" 
        ref='vehicleDetail' 
        @prePage="prePage"
        @nextPage="nextPage" 
        @close="vehicleShow = false"></details-vehicle-modal>
  </section>
</template>
<script>
import vehicleDetail from '@/components/detail/vehicle'
import detailsVehicleModal from '@/components/detail/details-vehicle-modal.vue'
import searchVehicle from '../components/search-vehicle.vue'
import plateNumber from '../components/plate-number'
import { vehicleRecordSearchEx } from '@/api/wisdom-cloud-search'
import { getVehicleBaseInfoByplateNo } from '@/api/vehicleArchives'
import { addCollection, deleteMyFavorite } from '@/api/user'
import { mapMutations, mapGetters } from 'vuex'
import { myMixins } from '../../components/mixin/index.js';
export default {
  name: 'faceContent',
  components: {
    plateNumber, vehicleDetail, searchVehicle, detailsVehicleModal
  },
  props: {
    mapOnData: {
      type: Boolean,
      default: false
    }
  },
  mixins: [myMixins], //全局的mixin
  data() {
    return {
      currentIndex: 0,
      vehicleShow: false,
      listLoading: false,
      selectMenuItemId: null,
      formRight: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
        total: 0
      },
      dataList: [],
      checkAll: false
    }
  },
  activated() {
    if(this.$route.query.deviceInfo){
        this.$nextTick(() =>{
            let deviceInfo = JSON.parse(this.$route.query.deviceInfo)
            this.$refs.searchBar.selectData([{...deviceInfo, select: true}])
            this.queryList()
        })
    }
  },
  created() {
    this.$nextTick(() => {
      this.queryList()
    })
  },
  computed:{
    ...mapGetters({
        getMaxLayer: 'countCoverage/getMaxLayer',
        getNum: 'countCoverage/getNum',
        getNewAddLayer: 'countCoverage/getNewAddLayer',
        getListNum: 'countCoverage/getListNum',
    }),
  },
  mounted() {
    let { plateNo, deviceId } = this.$route.query
    // 一车一档，车辆档案跳转
    if (plateNo) {
      this.$refs.searchBar.queryParam.plateNo = plateNo
    }

    // 一机一档跳转
    if (deviceId) {
      this.$refs.searchBar.queryParam.devices = [deviceId]
    }
  },
  methods: {
    getDataList(page = 0) {
      this.listLoading = true
      vehicleRecordSearchEx(this.queryParam)
        .then(res => {
          const { total, entities } = res.data
          this.pageInfo.total = total
          this.dataList = entities;
          if(page == 1) {
            this.$refs.vehicleDetail.prePage(this.dataList)
          }else if(page == 2) {
            this.$refs.vehicleDetail.nextPage(this.dataList)
          }
        })
        .catch(err => {
          console.error(err)
        })
        .finally(() => {
          this.listLoading = false
          this.isActivated = true
        })
    },
    searchHandle() {
        this.pageInfo.pageNumber = 1;
        this.queryList();
    },
    queryList(page = 0) {
        this.checkAll = false
        let queryParam = this.$refs.searchBar.queryParam
        queryParam = { ...queryParam, ...this.pageInfo }
        // 以图搜图字段vscode-file://vscode-app/d:/Users/<USER>/AppData/Local/Programs/Microsoft%20VS%20Code/resources/app/out/vs/code/electron-sandbox/workbench/workbench.html
        var features = []
        queryParam.urlList.forEach(item => {
            if (item) {
                features.push(item.feature)
            }
        })
        queryParam.features = features
        queryParam.similarity = queryParam.similarity / 100
        this.queryParam = queryParam
        this.dispTime()
        // 处理已选择设备
        var ids = []
        this.queryParam.selectDeviceList.forEach(item => {
            ids.push(item.deviceId)
        })
        this.queryParam.devices = ids
        this.getDataList(page)
    },
    resetHandle() {
        this.pageInfo = {
            pageNumber: 1,
            pageSize : 27
        };
        let queryParam = this.$refs.searchBar.queryParam
        queryParam = { ...queryParam, ...this.pageInfo }
        this.queryParam = queryParam
        this.dispTime()
        this.checkAll = false;
        this.getDataList()
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map(e => {
        return {
          ...e,
          isChecked: val
        }
      })
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e
      this.checkAll = this.dataList.filter(e => e.isChecked).length === this.dataList.length ? true : false
    },
    ...mapMutations({
        setNum:'countCoverage/setNum',
        setList: 'countCoverage/setList',
    }),
    dataAboveMapHandler() {
        const list = this.dataList.filter(e => e.isChecked);
        if (!list.length) {
            this.$Message.warning('请选择上图数据');
            return
        }
        let seleNum = this.dataList.filter(e => e.isChecked);
        let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
        let newNumPoints = this.getNum.pointsNum + this.getNewAddLayer.pointsInLayer + seleNum.length; //点位
        if(Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
            this.$Message.warning('已达到图层最大创建数量')
            return
        }
        if(Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
            this.$Message.warning('已达到上图最大点位总量')
            return
        }
        let num = JSON.stringify(this.getListNum);
        this.setList(num++)
        this.setNum({'layerNum': newNumLayer, 'pointsNum': newNumPoints})
        seleNum.map(item => {
            item.delePoints = true;
            item.deleType = 'vehicle'
        })
        this.$emit('dataAboveMapHandler', { type: 'vehicle', list,  deleIdent: 'vehicle-' + this.getListNum})
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size
      this.queryList()
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1
      this.pageInfo.pageSize = size
      this.queryList()
    },
    faceDetailFn(row, index) {
      this.currentIndex = index
      this.vehicleShow = true
       this.$refs.vehicleDetail.init(row, this.dataList, index, this.pageInfo.pageNumber)
    },
    /**
     * 跳转到一车一档页面
     */
    archivesPage(row) {
      getVehicleBaseInfoByplateNo(row.plateNo).then(res => {
        if(res.data.archiveNo){
          const { href } = this.$router.resolve({
            name: 'vehicle-archive',
            query: { 
              archiveNo: JSON.stringify(res.data.archiveNo),
              plateNo: JSON.stringify(row.plateNo),
              source: 'car' 
            }
          })
          window.open(href, '_blank')
        }else{
          this.$Message.error('尚未查询到该辆车的档案信息')
        }
      })
    },
    /**
     * 收藏
     */
    collection( data, flag ) {
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: 6,
      }
      if(flag == 1){
        addCollection(param).then(res => {
          this.$Message.success("收藏成功");
          this.getDataList()
        })
      }else{
        deleteMyFavorite(param).then(res => {
          this.$Message.success("取消收藏成功");
          this.getDataList()
        })
      }
    },
    /**
     * 上一个
     */
     preDetial() {
      if (this.currentIndex == 0 ) {
        if (this.pageInfo.pageNumber == 1) {
          this.$Message.warning("已经是第一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber-1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[(this.dataList.length-1)], (this.dataList.length-1))
          }, 200)
        }
      }else {
        this.faceDetailFn(this.dataList[(this.currentIndex-1)], (this.currentIndex-1))
      } 
    },
    prePage(pageNum) {
        if(pageNum < 1) {
            this.$Message.warning("已经是第一个了")
            return
        } else {
            this.pageInfo.pageNumber = pageNum;
            this.queryList(1)
        }
    },
    /**
     * 下一个
     */
     async nextPage(pageNum) {
        this.pageInfo.pageNumber = pageNum;
        let num = this.pageInfo.pageNumber;
        let size = this.pageInfo.pageSize;
        if(this.total <= num*size) {
            this.$Message.warning("已经是最后一个了")
            return
        }else{
            this.queryList(2)
        }
    },
    /**
     * 下一个
     */
    nextDetail() {
      if(this.currentIndex == (this.dataList.length - 1)){
        var num = this.pageInfo.pageNumber
        var size = this.pageInfo.pageSize
        var total = this.pageInfo.total
        if (total <= num*size) {
          this.$Message.warning("已经是最后一个了")
          return
        }else{
          this.pageChange(this.pageInfo.pageNumber+1)
          setTimeout(()=> {
            this.faceDetailFn(this.dataList[0], 0)
          }, 200)
        }
      }else{
        this.faceDetailFn(this.dataList[(this.currentIndex+1)], (this.currentIndex+1))
      }
    },
  }
}
</script>
<style lang="less" scoped>
@import 'style/index';
.vehicleDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0,0,0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
</style>
