<template>
  <div class="testing">
    <ui-modal v-model="visible" title="经纬度与地址检测" width="39.06rem">
      <Form ref="formValidate" :model="lngLatData" :label-width="0">
        <FormItem
          label=""
          prop="point"
          :rules="[
            {
              required: lngLatData.isPoint,
              message: '请输入数字',
              trigger: 'blur',
              type: 'number',
            },
          ]"
        >
          <div class="testing-item">
            <Checkbox v-model="lngLatData.isPoint" @on-change="validChange('point')"></Checkbox>
            <p>
              <span>经纬度满足</span>
              <Input ref="point" number v-model="lngLatData.point" style="width: 118px" />
              <span>位小数</span>
            </p>
          </div>
        </FormItem>
        <FormItem label="">
          <div class="testing-item">
            <Checkbox v-model="lngLatData.isCross"></Checkbox>
            <p>经纬度越界：超出所属行政区划</p>
          </div>
        </FormItem>
        <FormItem
          label=""
          prop="rice"
          :rules="[
            {
              required: lngLatData.isDeviation,
              message: '请输入数字',
              trigger: 'blur',
              type: 'number',
            },
          ]"
        >
          <div class="testing-item">
            <Checkbox v-model="lngLatData.isDeviation"></Checkbox>
            <p>
              <span>经纬度偏移：经纬度与安装地址偏移距离不超过</span>
              <Input number v-model="lngLatData.rice" style="width: 118px" />
              m
            </p>
          </div>
        </FormItem>
      </Form>
      <template slot="footer">
        <Button type="primary" @click="handleSave">保存&nbsp;</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {},
  data() {
    return {
      visible: false,
      single: '',
      lngLatData: {},
      topicComponentId: '',
      formValidate: {
        name: '',
      },
      ruleValidate: {
        // point: ,
        isDeviation: [
          {
            required: true,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.getLngLat();
    },
    async getLngLat() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicComponentOfLngLat, { params });
        this.lngLatData = res.data.data;
        this.lngLatData.isPoint = this.lngLatData.isPoint === '1' ? true : false;
        this.lngLatData.isCross = this.lngLatData.isCross === '1' ? true : false;
        this.lngLatData.isDeviation = this.lngLatData.isDeviation === '1' ? true : false;
      } catch (error) {
        console.log(error);
      }
    },
    handleSave() {
      //   if(!this.lngLatData.isCross || !this.lngLatData.isDeviation || !this.lngLatData.isPoint){
      //     this.$Message.error("请将信息填写完整！");
      //     return false
      //   }
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.update();
        } else {
          this.$Message.error('请将信息填写完整！');
        }
      });
    },
    async update() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
          isPoint: this.lngLatData.isPoint ? '1' : '0',
          point: this.lngLatData.point,
          isCross: this.lngLatData.isCross ? '1' : '0',
          isDeviation: this.lngLatData.isDeviation ? '1' : '0',
          rice: this.lngLatData.rice,
        };
        await this.$http.post(governancetheme.updateTopicComponentOfLngLat, params);
        this.visible = false;
        this.$emit('render');
        // this.$Message["success"]({
        //   background: true,
        //   content: "空间信息检测配置成功！",
        // });
        this.$Message.success('空间信息检测配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    validChange(name) {
      this.$nextTick(() => {
        this.$refs.formValidate.validateField(name);
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.testing-item {
  width: 100%;
  margin-bottom: 16px;
  display: flex;
  align-items: baseline;
  p {
    font-size: 14px;
    color: #ffffff;
    span {
      display: inline-block;
    }
  }
}
@{_deep} .ivu-modal-body {
  padding: 20px 50px !important;
}
</style>
<style lang="less">
.testing {
  .ivu-input {
    text-align: center !important;
  }
  .ivu-input-wrapper {
    margin: 0 10px !important;
  }
  .ivu-checkbox-group-item {
    display: flex !important;
  }
  .ivu-checkbox-checked .ivu-checkbox-inner {
    background: var(--color-primary);
  }
  .ivu-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
