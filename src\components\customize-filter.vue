<template>
  <div class="customize-filter">
    <ui-modal :title="customizeAction.title" v-model="visible" width="800" :styles="customizeAction.moduleStyle">
      <div class="modal-content mb-lg" v-if="visible">
        <div class="content-left auto-fill">
          <div ref="titleRef" class="content-header">
            <div v-if="customizeAction.title === '自定义检索'" class="base-text-color d_flex mb-md">
              {{ customizeAction.leftContent }}
              <p>（<span class="font-red"> 请选择4-8个检索条件 </span>）</p>
            </div>
            <div class="search-input-div mb-md" v-if="supportSearch">
              <Input placeholder="请输入搜索字段" v-model="keyWord" @on-enter="startFilter">
                <template #suffix>
                  <div class="search-icon-box">
                    <i class="icon-font icon-sousuo f-12" @click="startFilter"></i>
                  </div>
                </template>
              </Input>
            </div>
          </div>
          <div class="content-box auto-fill" :style="contentStyle">
            <slot name="leftSlot" :origin-checked-list="checkedList">
              <Checkbox
                :indeterminate="indeterminate"
                :value="checkAll"
                class="mb-sm"
                @click.prevent.native="handleCheckAll"
                >全选</Checkbox
              >
              <CheckboxGroup v-model="checkedList" @on-change="changeCheckAll">
                <template v-if="!!newCheckboxList.length">
                  <Checkbox
                    class="check-box ellipsis"
                    v-for="(item, index) in newCheckboxList"
                    :label="item[fieldName.id]"
                    :key="index + item[fieldName.id]"
                    :title="item[fieldName.value]"
                    :disabled="leftDisabled(item)"
                  >
                    {{ item[fieldName.value] }}
                  </Checkbox>
                  <span class="empty-item" v-for="(emptyitem, eIndex) in 3" :key="'empty' + eIndex"></span>
                </template>
              </CheckboxGroup>
            </slot>
          </div>
        </div>
        <div class="icon-box">
          <i class="icon-font icon-youjiantou"></i>
        </div>
        <div class="content-right auto-fill">
          <div class="flex-row content-header mb-md">
            <p class="base-text-color">
              {{ customizeAction.rightContent }}（<span class="color-failed"> 可拖动先后顺序 </span>）
            </p>
            <Button type="text" v-if="showClearAll" @click="handleClearArr">
              <span class="f-14">清空</span>
            </Button>
          </div>
          <div class="content-box" :style="contentStyle">
            <draggable class="dragg-box" v-model="tagList" @change="update" v-scroll="700" v-if="!!tagList.length">
              <transition-group>
                <ui-tag
                  v-for="(element, index) in tagList"
                  @close="handleCloseTag(index)"
                  :closeable="rightDisabled(element)"
                  :key="element[fieldName.id]"
                >
                  {{ element[fieldName.value] }}
                </ui-tag>
              </transition-group>
            </draggable>
          </div>
        </div>
      </div>
      <template slot="footer">
        <slot name="footer" :tag-list="tagList">
          <Button @click="visible = false" class="plr-30">取 消</Button>
          <Button class="plr-30" type="primary" @click="confirmFilter" :loading="saveModalLoading">确 定</Button>
        </slot>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .modal-content {
    .content-left,
    .content-right {
      .content-box {
        background: #f9f9f9;
      }
    }
    .search-input-div {
      .search-icon-box {
        background: var(--bg-ivu-input-group-append);
      }
    }
  }
}
.modal-content {
  height: 600px;
  display: flex;
  .content-left,
  .content-right {
    flex: 1;
    .content-header {
      min-height: 40px;
    }
    .content-box {
      flex: 1;
      background: #0e2246;
      padding: 20px;
      overflow-y: auto;
      height: 300px;
    }
  }
  .content-right {
    .dragg-box {
      overflow: visible !important;
      height: 90% !important;
    }
    .content-header {
      margin-bottom: 10px;
    }
  }
  .check-box-wrapper {
    height: 200px;
  }
  .empty-item {
    width: 200px;
  }
  @{_deep}.ivu-checkbox-group {
    overflow-x: hidden;
    overflow-y: auto;
    .ivu-checkbox-wrapper {
      margin-right: 0;
    }
  }
  .check-box {
    width: 220px;
  }
  .icon-box {
    position: relative;
    width: 50px;
    .icon-youjiantou {
      font-size: 30px;
      color: var(--color-primary);
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      padding: 0 10px;
    }
  }
  .search-input-div {
    position: relative;
    width: 258px;
    height: 35px;
    .search-icon-box {
      height: 100%;
      background: linear-gradient(180deg, #2b84e2 0%, #1553a1 98%);
      border-radius: 0 4px 4px 0;
    }
    .icon-sousuo {
      color: #fff;
    }
  }
}
</style>
<script>
import draggable from 'vuedraggable';
export default {
  data() {
    return {
      visible: false,
      saveModalLoading: false,
      tagList: [],
      checkedList: [], // 选中id的checkbox
      keyWord: '',
      newCheckboxList: [], // 筛选的左侧
      checkAll: false,
      indeterminate: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false;
      } else {
        this.checkAll = !this.checkAll;
      }
      this.indeterminate = false;
      if (this.checkAll) {
        this.checkedList = this.newCheckboxList.map((item) => item[this.fieldName.id]);
      } else {
        this.checkedList = [];
      }
    },
    changeCheckAll() {
      if (this.checkedList.length === this.newCheckboxList.length) {
        this.indeterminate = false;
        this.checkAll = true;
      } else if (this.checkedList.length > 0) {
        this.indeterminate = true;
        this.checkAll = false;
      } else {
        this.indeterminate = false;
        this.checkAll = false;
      }
    },
    update() {},
    confirmFilter() {
      this.$emit('confirmFilter', this.tagList);
    },
    startFilter() {
      if (!this.keyWord) {
        this.newCheckboxList = this.$util.common.deepCopy(this.checkboxList);
        return;
      }
      let has = this.checkboxList.filter((item) => {
        return item[this.fieldName.value].indexOf(this.keyWord) !== -1;
      });
      this.newCheckboxList = this.$util.common.deepCopy(has);
    },
    handleCloseTag(index) {
      this.checkedList.splice(index, 1);
      this.changeCheckAll();
    },
    changeCheckout() {
      this.tagList = [];
      this.checkedList.map((id) => {
        let index = this.checkboxList.findIndex((item) => item[this.fieldName.id] === id);
        if (index !== -1) {
          this.tagList.push(this.checkboxList[index]);
        }
      });
    },
    //清除全部
    handleClearArr() {
      this.checkedList = [];
      this.indeterminate = false;
      this.checkAll = false;
    },
  },
  watch: {
    visible(val) {
      if (val) {
        this.keyWord = '';
        this.indeterminate = false;
        this.checkAll = false;
        this.startFilter();
      }
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    // 默认选中的id~
    defaultCheckedList: {
      handler(val) {
        if (!val.length) {
          this.checkedList = [];
        } else {
          this.checkedList = val;
          this.$nextTick(() => {
            this.$emit('defaultChoose', this.tagList);
          });
        }
        this.changeCheckAll();
      },
      deep: true,
      immediate: true,
    },
    checkedList: {
      handler(val) {
        if (!val.length) {
          this.tagList = [];
        } else {
          this.changeCheckout();
        }
      },
      deep: true,
      immediate: true,
    },
    checkboxList: {
      deep: true,
      immediate: true,
      handler(val) {
        this.newCheckboxList = this.$util.common.deepCopy(val);
        this.changeCheckout();
      },
    },
  },
  computed: {},
  props: {
    value: {},
    // 标题以及文字之类的展示 + module样式修改
    customizeAction: {
      default: () => {
        return {
          title: '自定义标签',
          moduleStyle: {
            top: '0.52rem',
          },
          leftContent: '选择设备标签及排序',
          rightContent: '设备标签显示',
        };
      },
    },
    // 左右列表高度（用于scroll计算）
    contentStyle: {
      default: () => {
        return {
          height: '1.56rem', // 样式高度必传
        };
      },
    },
    // 列表字段名称
    fieldName: {
      default: () => {
        return {
          id: 'id',
          value: 'value',
        };
      },
    },
    // 标签是否支持删除
    closable: {
      default: true,
    },
    // 默认选中 id 数组
    defaultCheckedList: {
      default: () => [],
    },
    // 左侧所有的选项
    checkboxList: {
      default: () => [],
    },
    // 支持搜索
    supportSearch: {
      default: true,
    },
    leftDisabled: {
      type: Function,
      default: (item) => {
        return item.disabled;
      },
    },
    rightDisabled: {
      type: Function,
      default: (item) => {
        return !item.disabled;
      },
    },
    showClearAll: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    draggable,
    UiTag: require('@/components/ui-tag').default,
  },
};
</script>
