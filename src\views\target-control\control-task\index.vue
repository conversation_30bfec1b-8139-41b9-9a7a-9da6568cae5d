<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-09-04 14:49:32
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-05 16:07:32
 * @Description: 
-->
<template>
  <div class="layout">
    <tabsPage :list="alarmList" v-model="selectLi" />
    <keep-alive>
      <component :is="componentName" :key="selectLi" :compareType="selectLi" />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import people from "./people/index.vue";
import vehicle from "./vehicle/index.vue";
import sensory from "./sensory/index.vue";
import multi from "./multi/index.vue";
export default {
  name: "control-task",
  components: {
    tabsPage,
    people,
    vehicle,
    sensory,
    multi,
  },
  data() {
    // 布控类别tab
    this.alarmList = Object.freeze([
      { label: "人员布控", value: 1 },
      { label: "车辆布控", value: 2 },
      { label: "WIFI布控", value: 3 },
      { label: "RFID布控", value: 5 },
      { label: "电围布控", value: 4 },
      { label: "ETC布控", value: 6 },
      { label: "多维布控", value: 7 },
    ]);
    return {
      selectLi: 1, // 当前选中的tab
    };
  },

  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
  },

  computed: {
    componentName() {
      if (1 === this.selectLi) {
        return "people";
      }
      if (2 === this.selectLi) {
        return "vehicle";
      }
      if (7 === this.selectLi) {
        return "multi";
      }
      return "sensory";
    },
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
