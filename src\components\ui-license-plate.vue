<template>
  <div :style="licensePlateColorArray[plateColor].style" class="license-plate">
    <div :style="{ borderColor: licensePlateColorArray[plateColor].borderColor }" class="license-plate-line">
      <slot></slot>
    </div>
  </div>
</template>
<script>
  import { licensePlateColorArray } from '@/libs/system/index'
  export default {
    props: {
      // 车牌颜色
      plateColor: {
        type: String,
        default: '5'
      }
    },
    data() {
      return {
        licensePlateColorArray: licensePlateColorArray,
      }
    }
  }
</script>
<style lang="less" scoped>
  .license-plate {
    background: #2379F9;
    border-radius: 2px;
    font-weight: bold;
    font-size: 14px;
    line-height: 14px;
    font-family: 'MicrosoftYaHei-Bold';
    padding: 2px;
    color: #fff;
    .license-plate-line {
      border: 1px solid transparent;
      border-radius: 2px;
      padding: 2px 5px;
      box-sizing: border-box;
    }
  }
</style>