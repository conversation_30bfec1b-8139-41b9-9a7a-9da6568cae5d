<template>
  <ui-modal class="look-pictute" v-model="visible" :title="title" :styles="styles" :footer-hide="true">
    <div class="content auto-fill" v-ui-loading="{ loading: reasonLoading, tableData: tableData }">
      <div class="table-data auto-fill">
        <div class="fail-picture">
          <div
            v-for="(item, index) in tableData"
            :key="index"
            class="fail-picture-container"
            :class="(index + 1) % itemNum === 0 ? '' : 'mr-sm'"
          >
            <div class="fail-picture-item" @click="lookScence(index)">
              <ui-image :src="indexType === 'VEHICLE_ONLINE_RATE' ? item.smallImagePath : item.facePath" />
            </div>
            <div class="fail-picture-date">
              <span class="icon-font icon-shijian f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle" placement="bottom" :content="item.shotTime || '未知'">
                <div class="vt-middle ellipsis">
                  {{ item.shotTime || '未知' }}
                </div>
              </Tooltip>
            </div>
            <div class="fail-picture-address">
              <span class="icon-font icon-dizhi f-12 mr-xs vt-middle"></span>
              <Tooltip class="text vt-middle" placement="bottom" :content="item.address || '未知'">
                <div class="checkbox-item ellipsis">
                  {{ item.address || '未知' }}
                </div>
              </Tooltip>
            </div>
          </div>
        </div>

        <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
      </div>
      <ui-page class="page" :page-data="reasonPage" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
@{_deep} .ivu-modal {
  .ivu-modal-header {
    margin-bottom: 0;
  }
  .ivu-modal-body {
    padding: 0 20px;
  }
}
.look-pictute {
  .content {
    height: 700px;
    .table-data {
      height: 100%;
      .fail-picture {
        display: flex;
        flex-wrap: wrap;
        //height: 100%;
        overflow-y: auto;
        .fail-picture-container {
          width: calc(calc(100% - 60px) / 7);
          color: #8797ac;
          padding: 15px;
          margin-bottom: 15px;
          .fail-picture-item {
            height: 162px;
            width: 100%;
            margin-bottom: 10px;
            cursor: pointer;
          }
          .fail-picture-date,
          .fail-picture-address {
            display: flex;
          }
          .fail-picture-date {
            //line-height: 18px;
            font-size: 12px;
            //width: 160px;
          }
          .fail-picture-address {
            //line-height: 18px;
            font-size: 12px;
            //width: 160px;
            .result-icon {
              font-size: 20px;
            }
            .text {
              width: 122px;
              display: inline-block;
              cursor: pointer;
            }
          }
        }
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        .img-text {
          color: #385074;
        }
      }
    }
  }
}
.text {
  width: 122px;
  display: inline-block;
  cursor: pointer;
}
.no-box {
  width: 1726px;
  min-height: 860px;
  max-height: 860px;
}
[data-theme='dark'] {
  .fail-picture-container {
    background: var(--bg-info-card);
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .fail-picture-container {
    background: #f9f9f9;
    border: 1px solid #d3d7de;
    border-radius: 4px;
    .fail-picture-item {
      border: 1px solid #d3d7de;
    }
  }
}
</style>
<script>
export default {
  props: {
    value: {},
    title: {
      type: String,
      default: '',
    },
    reasonLoading: {
      type: Boolean,
      default: false,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    reasonPage: {
      type: Object,
      default: () => {},
    },
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      styles: { width: '7.4rem' },
      visible: false,
      loading: false,
      itemNum: 7,
      indexType: '',
    };
  },
  mounted() {},

  methods: {
    changePage(val) {
      this.$emit('handlePageChange', val);
    },
    changePageSize(val) {
      this.$emit('handlePageSizeChange', val);
    },
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
  },
  watch: {
    tableData: {
      handler() {
        this.imgList = this.tableData.map((row) => {
          if (this.activeIndexItem.indexType === 'VEHICLE_ONLINE_RATE') {
            return row.bigImagePath;
          }
          return row.scenePath;
        });
      },
      // immediate: true,
      // deep: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (!val) return false;
      this.indexType = this.activeIndexItem.indexType;
    },
  },
  components: {},
};
</script>
