<template>
  <!-- 卡口在线数量 -->
  <div
    class="body-container"
    v-ui-loading="{ loading: echartsLoading, tableData: showBack ? barDetailsData : tendencyEchartData }"
  >
    <!-- 趋势图 -->
    <draw-echarts
      v-show="!showBack && tendencyEchartData.length"
      :echart-style="echartStyle"
      :echart-option="echartOption"
      ref="onlineTrendEchart"
    ></draw-echarts>
    <!-- 详情 - 柱状图 -->
    <draw-echarts
      v-show="showBack && barDetailsData.length"
      class="echarts"
      :echart-style="echartStyle"
      :echart-option="detailBarEchartOption"
      ref="detailBarEchartRef"
    ></draw-echarts>
    <span class="next-echart" v-if="showNextIcon">
      <i class="icon-font icon-zuojiantou1 f-12" @click="changeShowData"></i>
    </span>
  </div>
</template>
<script>
import Vue from 'vue';
import home from '@/config/api/home';
import dataZoom from '@/mixins/data-zoom';
import xaxisLabelWorkNum from '@/mixins/xaxis-label-work-num';
import BayonetsOnlineNumberTooltip from '@/views/home/<USER>/bayonets-online-number-tooltip.vue';
import bayonetsOnlineNumber from '@/views/home/<USER>/module/bayonets-online-number';
import commonStyle from '@/views/home/<USER>/module/common-style';

export default {
  name: 'BayonetsOnlineNumber',
  mixins: [dataZoom, xaxisLabelWorkNum],
  props: {
    year: {
      type: String,
      default: '',
    },
    homePageConfig: {},
    styleType: {},
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartsLoading: false,
      showBack: false, // 返回
      echartOption: {},
      detailBarEchartOption: {},
      tendencyEchartData: [],
      barDetailsData: [],
      legendList: [],
      colorListGradient: [],
      xAxisData: [],
      colorList: [],
      echartNum: 31,
      tooltipFormatter: (data) => {
        let _t = this;
        let bayonetsOnlineNumberTooltip = Vue.extend(BayonetsOnlineNumberTooltip);
        let _this = new bayonetsOnlineNumberTooltip({
          el: document.createElement('div'),
          data() {
            return {
              data,
              year: _t.year, // 格式：'2023-06'
              legendList: _t.legendList,
            };
          },
          mounted() {
            // 【详情】 点击时触发
            window.openDetails = () => {
              _t.initBarDetailsData(data);
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  computed: {
    showNextIcon() {
      return (
        (!this.showBack && this.tendencyEchartData.length > this.echartNum) ||
        (this.showBack && this.barDetailsData.length > this.echartNum)
      );
    },
    homeStyle() {
      return bayonetsOnlineNumber[`style${this.styleType}`] || bayonetsOnlineNumber.style1;
    },
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  created() {
    this.legendList = [
      { name: '人脸卡口', key: 'faceNum', color: this.homeStyle.faceColor },
      { name: '车辆卡口', key: 'vehicleNum', color: this.homeStyle.vehicleColor },
    ];
    this.colorListGradient = this.homeStyle.colorListGradient;
    this.colorList = this.homeStyle.colorList;
    this.initAll();
  },
  watch: {
    showBack(val) {
      if (!val) {
        this.initEchartsOption();
      }
    },
  },
  mounted() {},
  methods: {
    changeShowData() {
      if (this.showBack) {
        this.scrollRight('detailBarEchartRef', this.barDetailsData, [], this.echartNum);
      } else {
        this.scrollRight('onlineTrendEchart', this.tendencyEchartData, [], this.echartNum);
      }
    },
    initAll() {
      this.showBack = false;
      this.initEchartsOption();
    },
    // 趋势图 数据
    async initEchartsOption() {
      try {
        this.echartsLoading = true;
        this.tendencyEchartData = [];

        // 只要重新请求数据， 都需要 重置 到第一页
        this.viewEchartIndex = 0;

        let data = {
          type: '1',
          date: this.year,
          regionCode: this.homePageConfig.regionCode || '',
        };
        let res = await this.$http.post(home.queryFaceVehicleOnlineQuantity, data);
        let dayInfo = res?.data?.data || {};
        // 处理 series、 x轴 数据
        let seriesData = this.handlerData(dayInfo);
        this.tendencyEchartData = seriesData;
        this.setEchartOption();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    /**
     * 可能存在 某天并没有检测，接口不返回， 前端需要特殊处理下  series中的data，最后返回 格式为： [{...}, {...}, null, ...]
     */
    handlerData(dayInfo) {
      // 每月 天数
      let xAxisMonth = () => {
        let date = new Date(this.year);
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let days = new Date(year, month, 0).getDate();

        let data = [];
        for (var i = 1; i <= days; i++) {
          data.push(i);
        }
        return data;
      };
      this.xAxisData = xAxisMonth();

      let hasDays = Object.keys(dayInfo);
      let seriesData = [];
      if (hasDays.length > 0) {
        this.xAxisData.forEach((xItem) => {
          let arrX = hasDays.filter((item) => Number(item) === xItem);
          if (arrX.length > 0) {
            // faceData: 人脸卡口    vehicleData: 车辆卡口
            seriesData.push({
              faceInfo: dayInfo[arrX[0]]?.faceData || null,
              faceNum: dayInfo[arrX[0]]?.faceData ? dayInfo[arrX[0]]?.faceData.num : null,
              vehicleInfo: dayInfo[arrX[0]]?.vehicleData || null,
              vehicleNum: dayInfo[arrX[0]]?.vehicleData ? dayInfo[arrX[0]]?.vehicleData.num : null,
              day: dayInfo[arrX[0]]?.faceData?.day || dayInfo[arrX[0]]?.vehicleData?.day,
            });
          } else {
            seriesData.push(null);
          }
        });
      }

      return seriesData;
    },
    // 处理 趋势图的 options
    setEchartOption() {
      let series = this.legendList.map((item, index) => {
        let seriesData = [];
        this.tendencyEchartData.forEach((echartItem) => {
          seriesData.push({
            ...echartItem,
            value: echartItem?.[item.key],
            color: item['color'][0],
          });
        });
        return {
          name: item.name,
          type: 'line',
          data: seriesData,
          // showSymbol: false,
          // smooth: true,
          lineStyle: {
            width: this.$util.common.fontSize(2),
            color: this.colorList[index % this.colorList.length], //线条颜色
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 1,
            shadowOffsetY: 1,
          },
          areaStyle: {
            //区域填充样式
            //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
            color: new this.$echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: this.colorListGradient[index][0],
                },
                {
                  offset: 1,
                  color: this.colorListGradient[index][1],
                },
              ],
              false,
            ),
          },
          itemStyle: {
            color: this.colorList[index % this.colorList.length],
            borderColor: this.colorList[index % this.colorList.length],
            borderWidth: 2,
            shadowColor: this.colorList[index % this.colorList.length],
            shadowBlur: 5,
            shadowOffsetX: 0,
            shadowOffsetXY: 0,
          },
        };
      });

      let opts = {
        data: series,
        xAxisData: this.xAxisData,
        tooltipFormatter: this.tooltipFormatter,
        tooltipBg: this.commonStyle.tooltipBg,
      };

      this.echartOption = this.$util.doEcharts.baseHomeBayonetsOnlineNumber(opts);
      setTimeout(() => {
        this.setDataZoom('onlineTrendEchart', [], this.echartNum);
      });
    },
    // 详情 -- 柱状图数据
    async initBarDetailsData(itemData) {
      try {
        this.showBack = true;
        this.echartsLoading = true;
        this.barDetailsData = [];

        // 只要重新请求数据， 都需要 重置 到第一页
        this.viewEchartIndex = 0;

        // 处理 人车batchId
        let batchIds = [];
        if (itemData[0].data?.faceInfo?.batchId) {
          batchIds.push(itemData[0].data?.faceInfo?.batchId);
        }
        if (itemData[0].data?.vehicleInfo?.batchId) {
          batchIds.push(itemData[0].data?.vehicleInfo?.batchId);
        }

        let data = {
          type: '2',
          regionCode: this.homePageConfig.regionCode || '',
          batchIds: batchIds,
        };
        let res = await this.$http.post(home.queryFaceVehicleOnlineQuantity, data);

        let info = res?.data?.data || {};
        let arr = [];
        Object.keys(info).forEach((key) => {
          arr.push({
            orgRegionName: key,
            faceInfo: info[key]?.faceData || null,
            faceNum: info[key]?.faceData ? info[key]?.faceData.num : null,
            vehicleInfo: info[key]?.vehicleData || null,
            vehicleNum: info[key]?.vehicleData ? info[key]?.vehicleData.num : null,
          });
        });
        this.barDetailsData = arr;

        this.setBarDetailsEchartOption(itemData);
      } catch (error) {
        // console.log(error);
      } finally {
        this.echartsLoading = false;
      }
    },
    setBarDetailsEchartOption(itemData) {
      try {
        let xAxisData = [];
        let allSeriesData = {
          faceNum: [], // 人脸卡口
          vehicleNum: [], // 车辆卡口
        };
        this.barDetailsData.forEach((item) => {
          allSeriesData.faceNum.push({
            name: item.orgRegionName,
            value: item.faceNum,
            // 动态设置边框
            // itemStyle: {
            //   borderWidth: item.faceNum > 0 ? 1 : 0,
            //   borderColor: '#1CCF78',
            // },
          });
          allSeriesData.vehicleNum.push({
            name: item.orgRegionName,
            value: item.vehicleNum,
          });
          xAxisData.push(item.orgRegionName);
        });

        // 处理 默认选中的图例
        let selectedLegend = {};
        this.legendList.forEach((lengendItem) => {
          if (!itemData.some((item) => item.seriesName === lengendItem.name)) {
            selectedLegend[lengendItem.name] = false; // 默认都是选中状态，若存在不需要，则需要设置为 false
          }
        });
        let opts = {
          selectedLegend: selectedLegend,
          xAxisData: xAxisData,
          seriesData: this.legendList.map((item) => {
            return {
              name: item.name,
              color: item.color,
              data: allSeriesData[item.key],
            };
          }),
          currentDay: itemData[0].data.day,
        };

        this.detailBarEchartOption = this.$util.doEcharts.baseHomeBayonetsOnlineNumberDetails(opts);
        setTimeout(() => {
          this.setDataZoom('detailBarEchartRef', [], this.echartNum);
          this.resetXaxisLabelNumFn(
            'detailBarEchartRef',
            this.detailBarEchartOption,
            this.barDetailsData.length > this.echartNum ? this.echartNum : this.barDetailsData.length,
          );
        });
      } catch (error) {
        // console.log(error);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  .echarts {
    height: 100% !important;
    width: 100% !important;
  }
}
</style>
