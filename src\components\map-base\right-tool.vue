<template>
  <div>
    <ul class="index-map-tool">
      <li
        v-for="(item, index) in toolMap"
        :title="item.title"
        :class="{ active: item.selected }"
        @click="item.fun(item)"
        :key="index"
      >
        <i class="icon-font icon-img" :class="item.icon"></i>
      </li>
    </ul>
  </div>
</template>
<style lang="less" scoped>
.index-map-tool {
  width: 40px;
  li {
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: 1px solid var(--color-primary);
    border-radius: 5px;
    background: rgba(10, 26, 58, 0.6);
    &:hover {
      background-color: var(--color-primary);
    }
    .icon-img {
      font-size: 18px;
      color: #fff;
    }
  }
  .active {
    background: var(--color-primary);
  }
}
</style>
<script>
export default {
  data() {
    return {
      toolMap: [
        {
          title: '全屏',
          icon: 'icon-suofang-jline',
          fun: this.satelliteMap,
          selected: false,
        },
        {
          title: '操作栏',
          icon: 'icon-caozuo',
          fun: this.setting,
          selected: true,
        },
        {
          title: '摄像头',
          icon: 'icon-qianduanshebeijiankong',
          fun: this.cameraShow,
          selected: true,
        },
        {
          title: '热力图',
          icon: 'icon-relitu',
          fun: this.heatShow,
          selected: false,
        },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    backHomePage() {
      this.$router.push({ name: 'datamap' });
    },
    setting(item) {
      item.selected = !item.selected;
      this.$emit('showMapTool', item.selected);
    },
    satelliteMap(item) {
      item.selected = !item.selected;
      this.$emit('initSatelliteMap', item.selected);
    },
    cameraShow(item) {
      item.selected = !item.selected;
      this.$emit('cameraShow', item.selected);
    },
    heatShow(item) {
      item.selected = !item.selected;
      this.$emit('heatShow', item.selected);
    },
  },
  watch: {},
  computed: {},
  props: {},
  components: {},
};
</script>
