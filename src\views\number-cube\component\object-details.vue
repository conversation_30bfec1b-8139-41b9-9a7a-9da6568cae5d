<template>
  <div class="object-details" :class="visible ? 'show-card' : 'none-card'">
    <!-- 头部 -->
    <div v-if="visible">
      <div class="object-details-header">
        <div class="breadcrumb-container">
          <span class="box"></span>
          <span class="title">
            <i>对象详情</i>
          </span>
          <Icon v-if="!isExcavate" type="md-close" @click="visible = false" />
        </div>
      </div>
      <!-- 内容 -->
      <div class="object-details-content">
        <div class="details-title">对象信息</div>
        <UiCard
          class="mt10 mb20"
          :card-data="detailObjInfo"
          :property-list="currentNodePropertyList"
        />
        <div class="details-title">关系信息</div>
        <Row>
          <Col :span="16" class="fs14 pd10"> 关系类型 </Col>
          <Col :span="8" class="fs14 pd10"> 节点数量 </Col>
        </Row>
        <div class="collapse-scroll">
          <el-collapse v-model="activeNames">
            <el-collapse-item
              v-show="item.data.length > 0"
              :name="index"
              v-for="(item, index) in detailRelationList"
              :key="index"
            >
              <template slot="title">
                <Row style="width: 100%">
                  <Col :span="16" class="fs14 pd10">
                    <span class="title primary ft14 fw ml4">{{
                      subStr(item.name)
                    }}</span>
                  </Col>
                  <Col
                    :span="6"
                    class="fs14 pd10 number primary ft14 fw"
                    style="text-align: center"
                    >{{ item.data.length }}</Col
                  >
                  <Col
                    :span="2"
                    class="fs14 pd10 number primary ft14 fw"
                    style="text-align: center"
                  >
                    <el-checkbox
                      v-if="isExcavate"
                      v-model="item.isCheckAll"
                      @change="checkAll"
                    />
                  </Col>
                </Row>
              </template>
              <div
                class="content-list"
                v-for="(row, index) in item.data"
                :key="index"
              >
                <div class="img">
                  <ui-image
                    :src="row.ext.propertyIcon || row.img"
                    :default-icon="row.ext.icon"
                    alt="图片"
                  />
                </div>
                <div class="content-list-info ellipsis1">
                  <p class="ellipsis">{{ row.label }}</p>
                </div>
                <el-checkbox
                  v-if="isExcavate"
                  v-model="row.isCheck"
                  @change="checkItem"
                />
                <i
                  v-if="row.show"
                  class="iconfont icon-eye"
                  @click="showNode(row)"
                ></i>
                <i
                  v-else
                  class="iconfont icon-eye-close"
                  @click="showNode(row)"
                ></i>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapActions } from "vuex";
export default {
  components: {
    UiCard: require("./ui-card").default,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    // 字段属性列表
    propertyList: {
      type: Array,
      default: () => [],
    },
    detailRelationList: {
      type: Array,
      default: () => [],
    },
    detailObjInfo: {
      type: Object,
      default: () => {},
    },
    isExcavate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      activeNames: "",
      currentNodePropertyList: [],
      selectIds: [],
    };
  },
  computed: {
    propertyNameObject() {
      let propertyNameObject = {};
      this.propertyList.forEach((ele) => {
        propertyNameObject[ele.name] = ele;
      });
      return propertyNameObject;
    },
  },
  watch: {
    visible(val) {
      this.$emit("input", val);
    },
    value(val) {
      this.visible = val;
      this.detailRelationList.forEach((item) => {
        item.isCheckAll = true;
        item.data.forEach((subItem) => {
          subItem.isCheck = true;
        });
      });
      this.checkAll(true);
    },
    detailObjInfo: {
      deep: true,
      handler(val) {
        console.log("--------------- watch detailObjInfo", val);
      },
    },
  },
  filter: {},
  created() {},
  mounted() {
    this.getDictAllData();
  },
  methods: {
    ...mapActions({
      getDictAllData: "dictionary/getDictAllData",
    }),
    /**
     * 显示/隐藏实体
     * @param {*} row
     */
    showNode(row) {
      this.$emit("relationDetailModal", row, this.isExcavate);
    },
    subStr(name) {
      if (!name) return "";
      const num = name.indexOf("(");
      if (num == -1) return name;
      return name.substr(0, num);
    },
    // 外部调用，更新节点信息
    async refRefrenshDetail(currentNode) {
      const ext = currentNode.ext;
      this.currentNodePropertyList = [];
      Object.keys(ext.properties).forEach((key) => {
        const propertyObject = this.propertyNameObject[key];
        // 数据来源不展示
        if (propertyObject) {
          this.currentNodePropertyList.push({
            label: propertyObject.nameCn,
            propertyDicType: propertyObject.dicType,
            value: ext.properties[key],
            key,
          });
        }
      });
    },
    checkAll(val) {
      this.selectIds = [];
      this.detailRelationList.forEach((item) => {
        item.isCheckAll = val;
        item.data.forEach((subItem) => {
          if (item.isCheckAll) {
            subItem.isCheck = true;
          } else {
            subItem.isCheck = false;
          }
          this.selectIds.push({ id: subItem.id, isCheck: subItem.isCheck });
        });
      });
      this.$forceUpdate();
      this.isExcavate && this.$emit("excavateShow", this.selectIds);
    },
    checkItem(val) {
      this.selectIds = [];
      this.detailRelationList.forEach((item) => {
        item.isCheckAll = val;
        item.data.forEach((subItem) => {
          this.selectIds.push({ id: subItem.id, isCheck: subItem.isCheck });
        });
      });
      this.$forceUpdate();
      this.isExcavate && this.$emit("excavateShow", this.selectIds);
    },
  },
};
</script>
<style lang="less" scoped>
.object-details {
  width: 370px;
  height: calc(~"(100% - 205px)");
  position: fixed;
  right: 0;
  top: 194px;
  z-index: 899;
  box-shadow: 0 3px 5px 0 rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  background: #fff;

  & > div {
    height: 100%;
  }
  .object-details-header {
    height: 40px;
    box-shadow: inset 0 -1px 0 0 #d3d7de;
    padding-left: 15px;
    .breadcrumb-container {
      padding-top: 11px;
      .title {
        position: relative;
        display: inline-block;
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        top: -3px;
        padding-left: 8px;
      }
      .box {
        display: inline-block;
        width: 3px;
        height: 20px;
        background: #2b84e2;
      }
    }
    .ivu-icon {
      float: right;
      font-size: 16px;
      margin-right: 18px;
      margin-top: 2px;
      cursor: pointer;
    }
  }
  .object-details-content {
    padding: 10px 15px;
    height: calc(~"(100% - 40px)");
    .title {
      float: left;
    }
    .number {
      float: right;
      text-align: center;
    }
    .content-list {
      display: flex;
      flex-wrap: nowrap;
      width: 100%;
      height: 60px;
      margin-top: 10px;
      position: relative;
      .img {
        width: 60px;
        height: 60px;
        margin-right: 10px;
        border: 1px solid #d3d7de;
        border-radius: 30px;
        overflow: hidden;
        img {
          width: 100% !important;
          height: 100% !important;
        }
        /deep/ .ui-image-div {
          img {
            width: 100% !important;
            height: 100% !important;
          }
        }
      }
      .content-list-info {
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
      }
      .icon-eye {
        position: absolute;
        right: 6px;
        margin-top: 16px;
        cursor: pointer;
      }
      .icon-eye-close {
        position: absolute;
        right: 6px;
        margin-top: 16px;
      }
      /deep/ .el-checkbox {
        position: absolute;
        right: 23px;
        margin-top: 18px;
      }
    }
    /deep/ .el-collapse-item__header {
      height: 30px;
      line-height: 30px;
      border: none;
      background: rgba(211, 215, 222, 0.3);
      margin-bottom: 4px;
    }
    .collapse-scroll {
      height: calc(~"(100% - 320px)");
      overflow-y: auto;
    }
    /deep/ .el-icon-arrow-right:before {
      content: "";
    }
  }
  .details-title {
    font-size: 14px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    margin-left: 7px;
    position: relative;
    &::before {
      content: "";
      width: 5px;
      height: 5px;
      background: #2c86f8;
      position: absolute;
      left: -12px;
      top: 9px;
    }
  }
}
.show-card {
  transition: 0.5s;
  transform: translateX(-10px);
}
.none-card {
  transition: 0.5s;
  transform: translateX(370px);
}

.ellipsis1 {
  overflow: hidden;
  //   text-overflow: ellipsis;
  white-space: nowrap;
  width: 190px;
}

/deep/ .ui-image-div {
  border: 0;
}
</style>
