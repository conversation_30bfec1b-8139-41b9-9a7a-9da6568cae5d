export class MapToolBar {
  mapTools;
  mapAdapter;
  cacheSelectGeometry;

  constructor(mapTools, mapAdapter) {
    this.mapTools = mapTools;
    this.mapAdapter = mapAdapter;
  }

  /**
   * 缓存图形
   * @param overlay
   */
  addSelectGeometry(overlay) {
    if (!this.cacheSelectGeometry) {
      this.cacheSelectGeometry = new Array();
    }
    this.cacheSelectGeometry.push(overlay);
  }

  getSelectGeometrys() {
    return this.cacheSelectGeometry;
  }

  resetSelectGeometrys() {
    this.cacheSelectGeometry = null;
  }

  measureDistance() {
    this.mapTools.measureDistance();
  }

  measureArea() {
    this.mapTools.measureArea();
  }

  cancelMeasure() {
    this.mapTools.cancelMeasure();
  }

  drawLine(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawLine((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Polyline(geometry.getPath(), style);
      }
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawPolygon(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawPolygon((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Polygon(geometry.getPath(), style);
      }
      geometry.setZIndex(100);
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawRectangle(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawRectangle((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Polygon(geometry.getPath(), style);
      }
      geometry.setZIndex(100);
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawCircle(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawCircle((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Circle(geometry.getCenter(), geometry.getRadius(), style);
      }
      // console.log(888555,geometry)
      // this.mapAdapter.removeOverlay(geometry);
      // let layerOut = this.mapAdapter.getLayerByName("featureLayer");
      // if(layerOut != null){
      //     this.mapAdapter.removeLayerByName("featureLayer");
      // }
      this.mapAdapter.addOverlay(geometry);
      geometry.isEnableEdit = true;
      this.addSelectGeometry(geometry);
      if (false) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawCircleByDiameter(callBackMethod) {}

  cancelDraw() {
    this.mapTools.cancelDraw();
  }
}
