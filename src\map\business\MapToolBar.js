import { index } from "d3";

export class MapToolBar {
  mapTools;
  mapAdapter;
  cacheSelectGeometry;
  cacheOnlyGeometry;
  constructor(mapTools, mapAdapter) {
    this.mapTools = mapTools;
    this.mapAdapter = mapAdapter;
  }

  /**
   * 缓存图形
   * @param overlay
   */
  addSelectGeometry(overlay) {
    if (!this.cacheSelectGeometry) {
      this.cacheSelectGeometry = new Array();
    }
    this.cacheSelectGeometry.push(overlay);
  }
  /**
   * 缓存图形(模型集市)
   * @param overlay
   */
  addOnlyGeometry(overlay, index) {
    if (!this.cacheOnlyGeometry) {
      this.cacheOnlyGeometry = new Array(5).fill("");
    }
    this.cacheOnlyGeometry[index] = overlay;
  }
  getSelectGeometrys() {
    return this.cacheSelectGeometry;
  }
  getOnlyGeometrys() {
    return this.cacheOnlyGeometry;
  }
  // 全部
  resetSelectGeometrys() {
    this.cacheSelectGeometry = null;
  }
  //   指定
  resetSelectAppoint(index) {
    this.cacheOnlyGeometry.splice(index, 1);
  }
  measureDistance() {
    this.mapTools.measureDistance();
  }

  measureArea() {
    this.mapTools.measureArea();
  }

  cancelMeasure() {
    this.mapTools.cancelMeasure();
  }

  drawLine(callBackMethod, anleEdit = false, style) {
    this.mapTools.drawLine((extent, geometry) => {
      if (!geometry) {
        return;
      }
      if (style) {
        geometry = new NPMapLib.Geometry.Polyline(geometry.getPath(), style);
      }
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawPolygon(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index = -1
  ) {
    this.mapTools.drawPolygon((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Polygon(geometry.getPath(), style);
      }
      geometry.setZIndex(100);
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (index > -1) {
        this.addOnlyGeometry(geometry, index);
      } else {
        this.addSelectGeometry(geometry);
      }
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      } else {
        geometry.addEventListener(NPMap.POLYGON_EVENT_CLICK, (geometry) => {
          if (rightClickHandler) {
            rightClickHandler(extent, geometry);
          }
        });
      }
      // 点击鼠标右键(多边形)
      geometry.addEventListener(NPMap.POLYGON_EVENT_RIGHT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry, "right");
        }
      });

      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }

  drawRectangle(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index = -1
  ) {
    this.mapTools.drawRectangle((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Polygon(geometry.getPath(), style);
      }
      geometry.setZIndex(100);
      this.mapAdapter.addOverlay(geometry);
      this.addSelectGeometry(geometry);
      if (index > -1) {
        this.addOnlyGeometry(geometry, index);
      } else {
        this.addSelectGeometry(geometry);
      }
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        // 重新选择范围
        geometry.addEventListener(NPMap.POLYGON_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      } else {
        geometry.addEventListener(NPMap.POLYGON_EVENT_CLICK, (geometry) => {
          if (rightClickHandler) {
            rightClickHandler(extent, geometry);
          }
        });
      }
      // 点击鼠标右键(矩形)
      geometry.addEventListener(NPMap.POLYGON_EVENT_RIGHT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry, "right");
        }
      });
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }
  // 画圆
  drawCircle(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index = -1
  ) {
    this.mapTools.drawCircle((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Circle(
          geometry.getCenter(),
          geometry.getRadius(),
          style
        );
      }
      this.mapAdapter.addOverlay(geometry);
      geometry.isEnableEdit = true;
      this.addSelectGeometry(geometry);
      if (index > -1) {
        this.addOnlyGeometry(geometry, index);
      } else {
        this.addSelectGeometry(geometry);
      }
      geometry.addEventListener(NPMap.CIRCLE_EVENT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry);
        }
      });
      // 点击鼠标右键(圆)
      geometry.addEventListener(NPMap.CIRCLE_EVENT_RIGHT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry, "right");
        }
      });
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        // 重新选择范围
        geometry.addEventListener(NPMap.CIRCLE_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    });
  }
  // 两点直径画圆
  drawCircleByDiameter(
    callBackMethod,
    rightClickHandler,
    anleEdit = false,
    style,
    index = -1
  ) {
    this.mapTools.drawCircleByDiameter((extent, geometry) => {
      if (style) {
        geometry = new NPMapLib.Geometry.Circle(
          geometry.getCenter(),
          geometry.getRadius(),
          style
        );
      }
      this.mapAdapter.addOverlay(geometry);
      geometry.isEnableEdit = true;
      if (index > -1) {
        this.addOnlyGeometry(geometry, index);
      } else {
        this.addSelectGeometry(geometry);
      }
      geometry.addEventListener(NPMap.CIRCLE_EVENT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry);
        }
      });
      // 点击鼠标右键(圆)
      geometry.addEventListener(NPMap.CIRCLE_EVENT_RIGHT_CLICK, (geometry) => {
        if (rightClickHandler) {
          rightClickHandler(extent, geometry, "right");
        }
      });
      if (anleEdit) {
        geometry.enableEditing(NPMap.ModifyFeature_RESIZE);
        // 重新选择范围
        geometry.addEventListener(NPMap.CIRCLE_EVENT_DRAG_END, (geometry) => {
          if (callBackMethod) {
            callBackMethod(extent, geometry);
          }
        });
      }
      if (callBackMethod) {
        callBackMethod(extent, geometry);
      }
    }, style);
  }

  cancelDraw() {
    this.mapTools.cancelDraw();
  }
}
