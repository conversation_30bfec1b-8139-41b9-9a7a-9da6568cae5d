export const searchCenter = { 
    //------------人脸---------------
    'similarity': {
        'keyName': 'similarity',
        'label': '相似度',
    },
    'algorithmVendorType': { 
        'keyName': 'algorithmVendorType',
        'label': '算法选择',
        'dic': 'firmList'
    },
    'device': {
        'label': '设备范围',
        'type': 'device'
    },
    'startDate': {
        'keyName': 'startDate',
        'label': '开始时间',
    },
    'endDate': {
        'keyName': 'endDate',
        'label': '结束时间',
    },
    'glasses': {
        'keyName': 'glasses',
        'label': '眼镜',
        'dic': 'captureGlassesList'
    },
    'gender': {
        'keyName': 'gender',
        'label': '性别',
        'dic': 'genderList'
    },
    'cap': {
        'keyName': 'cap',
        'label': '帽子',
        'dic': 'captureCapList'
    },
    'faceMask': {
        'keyName': 'faceMask',
        'label': '口罩',
        'dic': 'captureMaskList'
    },
    'age': {
        'keyName': 'age',
        'label': '年龄段',
        'dic': 'ipbdFaceCaptureAge'
    },
    //------------人脸end---------------
    //------------车辆---------------
    'plateNo':{
        'keyName': 'plateNo',
        'label': '车牌号码',
    },
    // 'pointOfView': {
    //     'keyName': 'pointOfView',
    //     'label': '角度',
    //     'dic': 'pointViewList',
    // },
    // 'plateClass':{
    //     'keyName': 'plateClass',
    //     'label': '车牌类型',
    //     'dic': 'plateClassList',
    // },
    // 'plateColor': {
    //     'keyName': 'plateColor',
    //     'label': '车牌颜色',
    //     'dic': 'licensePlateColorList',
    // },
    // 'cover':{
    //     'keyName': 'cover',
    //     'label': '遮挡',
    //     'dic': 'plateOcclusionList',
    // },
    // 'bodyType': {
    //     'keyName': 'bodyType',
    //     'label': '车身类型',
    //     'dic': 'plateOcclusionList',
    // },
    // 'bodyColor':{
    //     'keyName': 'bodyColor',
    //     'label': '车身颜色', 
    //     'dic': 'plateColorIpbdList',
    // },
    // 'plateBrands': {
    //     'keyName': 'plateBrands',
    //     'label': '车辆品牌',
    // },
    // 'specialPlate':{
    //     'keyName': 'specialPlate',
    //     'label': '特殊车辆',
    //     'dic': 'specialVehicleList',
    // },
    // 'carRoof': {
    //     'keyName': 'carRoof',
    //     'label': '车顶物件',
    //     'dic': 'roofItemsList',
    // },
    // 'inspectAnnually': {
    //     'keyName': 'inspectAnnually',
    //     'label': '年检标',
    //     'dic': 'annualInspectionNumList',
    // },
    // 'markerList': {
    //     'keyName': 'markerList',
    //     'label': '标志物',
    //     'dic': 'markerTypeList',
    // },
    // 'ifCoDriverPeople': {
    //     'keyName': 'ifCoDriverPeople',
    //     'label': '副驾有人',
    //     'dic': 'markerTypeList',
    // },
    // 'facialOcclusion': {
    //     'keyName': 'facialOcclusion',
    //     'label': '面部遮挡',
    // },
    // 'sunVisorStatus': {
    //     'keyName': 'sunVisorStatus',
    //     'label': '遮阳板',
    // },
}