<!--
    * @FileDescription: 建设态势专题大屏-IT资源态势
    * @Author: H
    * @Date: 2024/06/11
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="视频网链路信息" class="screen-box">
                <div class="maintain-box">
                    <div class="maintain-box-top">
                        <img :src="getImgUrl('fzl.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ videoLineData.loadRate || 0 }}%</p>
                            <p class="detail-li-title">负载率</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <div class="detail-li">
                            <p class="detail-li-num">{{ videoLineData.maxSpeed || 0 }} Gbps</p>
                            <p class="detail-li-title">最大传输速率</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot">
                        <div class="angle">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <div class="detail-li">
                            <p class="detail-li-num">{{ videoLineData.realSpeed || 0 }}Gbps/s</p>
                            <p class="detail-li-title">实时吞吐量</p>
                        </div>
                    </div>
                </div>
            </card>
            <card title="机房基本信息" class="screen-box">
                <div class="machine-room">
                    <div class="machine-room-left">
                        <div class="device-box-msg">
                            <img :src="getImgUrl('fwq.png')" alt="">
                            <div class="device-box-right">
                                <div class="device-box-type device-title">服务器数量</div>
                                <count-to :start-val="0.000" :end-val="roomInfoData.serverCount" :duration="1000" class="dinpro device-box-num"></count-to>
                            </div>
                        </div>
                        <div class="device-box-msg">
                            <img :src="getImgUrl('jhj.png')" alt="">
                            <div class="device-box-right">
                                <div class="device-box-type device-title">交换机数量</div>
                                <count-to :start-val="0.000" :end-val="roomInfoData.switchCount" :duration="1000" class="dinpro device-box-num"></count-to>
                            </div>
                        </div>
                    </div>
                    <div class="box-line"></div>
                    <div class="machine-room-right">
                        <p class="room-right-title"> 存储容量 <span class="dinpro dinpro1">{{ roomInfoData.storageCapacitySize }}</span><span class="dinpro">TB</span> </p>
                        <pie-chart ref="pieChart" name="存储使用率" :colorData="['rgba(45,255,220,1)', 'rgba(14,255,154,1)']"></pie-chart>
                    </div>
                </div>
            </card>
            <card title="机房交换机信息" class="screen-box">
                <div class="table">
                    <div class="table-header">
                        <div class="th-column table-column-xh">序号</div>
                        <div class="th-column table-column-yt">用途</div>
                        <div class="th-column table-column-pp">品牌</div>
                        <div class="th-column table-column-jxh">型号</div>
                        <div class="th-column table-column-zt">状态</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in switchList" :key="index">
                            <div class="tr-column table-column-xh">
                                {{ item.index }}
                            </div>
                            <div class="tr-column table-column-yt">{{ item.purpose }}</div>
                            <div class="tr-column table-column-pp">
                                <p class="time bomColor">CPU:{{ item.brand }}</p>
                            </div>
                            <div class="tr-column table-column-jxh">
                                <p>{{ item.model }}</p>
                            </div>
                            <div class="tr-column table-column-zt">
                                <p class="online" v-if="item.status==0">在线</p>
                                <p class="unline" v-else>离线</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        运营商机房信息
                    </div>
                    <div class="table">
                        <div class="table-header">
                            <div class="th-column table-column-xh">序号</div>
                            <div class="th-column table-column-jfmc">机房名称</div>
                            <div class="th-column table-column-jfmj">机房面积</div>
                            <div class="th-column table-column-jfdz">机房地址</div>
                        </div>
                        <ul class="table-content">
                            <li class="table-row" v-for="(item, index) in operaList" :key="index">
                                <div class="tr-column table-column-xh">
                                    {{ item.index }}
                                </div>
                                <div class="tr-column table-column-jfmc">{{ item.name }}</div>
                                <div class="tr-column table-column-jfmj">
                                    <p class="time bomColor">{{ item.area }}m²</p>
                                </div>
                                <div class="tr-column table-column-jfdz">
                                    <p>{{ item.address }}</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        公安机房信息
                    </div>
                    <div class="table">
                        <div class="table-header">
                            <div class="th-column table-column-xh">序号</div>
                            <div class="th-column table-column-jfmc">机房名称</div>
                            <div class="th-column table-column-jfmj">机房面积</div>
                            <div class="th-column table-column-jfdz">机房地址</div>
                        </div>
                        <ul class="table-content">
                            <li class="table-row" v-for="(item, index) in policeList" :key="index">
                                <div class="tr-column table-column-xh">
                                    {{ item.index }}
                                </div>
                                <div class="tr-column table-column-jfmc">{{ item.name }}</div>
                                <div class="tr-column table-column-jfmj">
                                    <p class="time bomColor">{{ item.area }}m²</p>
                                </div>
                                <div class="tr-column table-column-jfdz">
                                    <p>{{ item.address }}</p>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="机房服务器信息" class="screen-bigbox">
                <div class="table">
                    <div class="table-header">
                        <div class="th-column table-column-yt">用途</div>
                        <div class="th-column table-column-sl">数量</div>
                        <div class="th-column table-column-pz">配置</div>
                        <div class="th-column table-column-xtcz">操作系统</div>
                        <div class="th-column table-column-zt">状态</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in macroomList" :key="index">
                            <div class="tr-column table-column-yt">
                                {{ item.purpose }}
                            </div>
                            <div class="tr-column table-column-sl">{{ item.count }}</div>
                            <div class="tr-column table-column-pz">
                                <p class="time bomColor">CPU:{{ item.config.cpuCoreNum || '' }}/{{ item.config.cpuThreadNum || '' }}</p>
                                <p class="time bomColor">内存:{{ item.config.memory }}</p>
                                <p class="time bomColor">硬盘:{{ item.config.disk }}</p>
                            </div>
                            <div class="tr-column table-column-xtcz">
                                <p>{{ item.os }}</p>
                            </div>
                            <div class="tr-column table-column-zt">
                                <p class="online" v-if="item.status == 0">在线</p>
                                <p class="unline" v-else>离线</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
        </div>
    </div>
</template>
<script>
import { engineRoomInfo,
    videoLinkInfo,
    itRegionStat, } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import mapChart from './map-chart.vue';
import pieChart from './pie-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        mapChart,
        pieChart,
        CountTo
    },
    data() {
        return {
            macroomList: [
                { num: 4, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '4369', type: 2},
                { num: 5, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '3831', type: 2},
                { num: 6, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '2790', type: 1},
                { num: 7, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '1490', type: 2},
                { num: 4, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '4369', type: 2},
                { num: 5, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '3831', type: 2},
                { num: 6, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '2790', type: 1},
                { num: 7, name: 'XXX分局', config: {cpuCoreNum: 0,cpuThreadNum:0 }, num: '1490', type: 2},
            ],
            switchList: [
                { num: 4, name: 'XXX分局', yt: '核心交换机', num: '4369', type: 2},
                { num: 5, name: 'XXX分局', yt: '核心交换机', num: '3831', type: 2},
                { num: 6, name: 'XXX分局', yt: '核心交换机', num: '2790', type: 1},
                { num: 7, name: 'XXX分局', yt: '核心交换机', num: '1490', type: 2},
                { num: 4, name: 'XXX分局', yt: '核心交换机', num: '4369', type: 2},
            ],
            operaList: [
                { name: 'XXX机房', area: '275ml', address: 'XX路XX号X层',},
                { name: 'XXX机房', area: '275ml', address: 'XX路XX号X层',},
                { name: 'XXX机房', area: '275ml', address: 'XX路XX号X层',},
                { name: 'XXX机房', area: '275ml', address: 'XX路XX号X层',},
            ],
            policeList: [],
            videoLineData: {},
            roomInfoData: {}
        }
    },
    created() {
        this.init();
        this.queryMap();
    },
    methods: {
        init() {
            engineRoomInfo()
            .then(res =>{
                this.roomInfoData = res.data;
                this.$nextTick(() => {
                    this.$refs.pieChart.init(res.data.storageUsageRate);
                })
                //机房交换机信息 switchInfoList
                this.switchList = res.data.switchInfoList;
                // 运营商机房信息 operatorEngineRoomInfoList
                this.operaList = res.data.operatorEngineRoomInfoList;
                // 公安机房信息 policeEngineRoomInfoList
                this.policeList = res.data.policeEngineRoomInfoList;
                // 机房服务器信息 serverInfoList
                this.macroomList = res.data.serverInfoList;
            })
            videoLinkInfo()
            .then(res =>{
                this.videoLineData = res.data;
            })
        },
        queryMap() {
            itRegionStat()
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        }, 
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../../../style/resetui.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    .screen-bigbox{
        height: 100%;
        position: relative;
    }
    .maintain-box{
        display:flex;
        flex-wrap: wrap;
        justify-content: space-between;
        height: 100%;
        .maintain-box-top{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
        }
        .maintain-box-bot{
            width: 49%;
            background: rgba(24,98, 187, 0.1);
            display: flex;
            justify-content: space-evenly;
            align-items: center;
            position: relative;
        }
        .detail-li{
            text-align: center;
            .detail-li-num{
                font-size: 30px;
                font-weight: 700;
                color: #F1FCFF;
            }
            .detail-li-title{
                font-size: 14px;
                font-weight: 400;
                color: #ffffff;
            }
        }
    }
    .content-top{
        .table{
            width: 100%;
            height: 120px;
        }
    }
    .machine-room{
        display: flex;
        justify-content: space-between;
        height: 100%;
        .machine-room-left{
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }
        .box-line{
            height: 200px;
        }
        .machine-room-right{
            width: 40%;
            position: relative;
            .room-right-title{
                position: absolute;
                left: 50%;
                transform: translate(-50%, 0);
                white-space: nowrap;
                font-size: 14px;
                color: #ffffff;
                .dinpro1{
                    font-size: 24px;
                }
            }
        }
    }
    .device-box-msg{
        display: flex;
        img{
            width: 70px;
            height: 56px;
        }
        .device-box-right{
            margin-left: 10px;
            .device-box-num{
                margin-left: 8px;
                color: #F1FCFF;
                font-size: 24px;    
            }
            .device-title{
                font-size: 14px;
                font-weight: bold;
                color: #fff;
                height: 20px;
                width: 90px;
                padding-left: 10px;
                position: relative;
                line-height: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                top: 0;
                z-index: 1;
                background: linear-gradient(270deg, rgba(3, 145,255, 0) 0%, rgba(3, 145,255, 0.4) 100%);
                &:before {
                    content: '';
                    position: absolute;
                    width: 2px;
                    height: 20px;
                    top: 50%;
                    transform: translateY(-50%);
                    left: 0px;
                    background: rgba(0, 204, 255, 1);
                }
            }
        }
    }
    .table{
        height: 100%;
        .table-header{
            display: flex;
            font-weight: 400;
            color: #ffffff;
            font-size: 14px;
            justify-content: space-between;
            padding: 0 !important;
            background: linear-gradient(180deg, rgba(7, 32, 70, 0) 0%, #064096 100%); 
            .table-column-xh{
                width: 50px;
            }
            .table-column-yt{
                width: 100px;
            }
            .table-column-pp{
                flex: 1;
            }
            .table-column-jxh{
                width: 80px;
            }
            .table-column-sl{
                width: 50px;
            }
            .table-column-pz{
                width: calc(~'100% - 260px');
            }
            .table-column-xtcz{
                width: 60px;
            }
            .table-column-zt{
                width: 50px;
            }
            .table-column-jfmc{
                width: 80px;
            }
            .table-column-jfmj{
                width: 80px;
            }
            .table-column-jfdz{
                flex: 1;
            }
            .th-column{
                text-align: center;
            }
        }
        .table-content{
            height: calc(~'100% - 20px'); 
            overflow: auto;
            .table-row{
                display: flex;
                justify-content: space-between;
                align-items: center;
                &:nth-child(odd){
                    padding: 3px 0;
                    background: linear-gradient(90deg, rgba(6,55,131, 0) 0%, rgba(6,55,131,0.5) 51%, rgba(6,55,131,0) 100%);
                }
                .tr-column{
                    text-align: center;
                    font-size: 14px;
                    font-weight: 400;
                    color: #D0DDE7;
                }
                .table-column-xh{
                    width: 50px;
                }
                .table-column-yt{
                    width: 100px;
                }
                .table-column-jxh{
                    width: 80px;
                }
                .table-column-sl{
                    width: 50px;
                }
                .table-column-pz{
                    width: calc(~'100% - 260px');
                    text-align: left;
                }
                .table-column-xtcz{
                    color: #FFC963;
                    width: 60px;
                }
                .table-column-jfmc{
                    width: 80px;
                }
                .table-column-jfmj{
                    width: 80px;
                }
                .table-column-jfdz{
                    flex: 1;
                }
                .table-column-zt{
                    width: 50px;
                    color: #D0DDE7;
                    .online{
                        color: #2DDF6C; 
                    }
                    .unline{
                        color: #045E9F;
                    }
                }
                
            }
        }
    }
}
</style>