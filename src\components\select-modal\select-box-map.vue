<!--
    * @FileDescription: 地图框选
    * @Author: H
    * @Date: 2024/9/2
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-10 14:17:44
 -->
<template>
  <div class="map-box">
    <mapBase
      ref="mapBase"
      :mapLayerConfig="mapLayerConfig"
      :searchBtn="false"
      :siteListFlat="siteListFlat"
      @selectlist="selectlist"
      :crashType="crashType"
      :placeFence="placeFence"
    />
  </div>
</template>
<script>
import mapBase from "@/components/map/index.vue";
import { getSimpleDeviceList } from "@/api/operationsOnTheMap";
import { mapGetters } from "vuex";
export default {
  components: {
    mapBase,
  },
  props: {
    // 场所围栏
    placeFence: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      mapLayerConfig: {
        tracing: false, // 是否需要刻画轨迹
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: true, // 框选操作栏
        selectionResult: false, // 是否显示框选结果弹框
        resultOrderIndex: false, // 搜索结果排序,
        showLatestLocation: false, // 显示地图最新位置
        selectType: true, // 默认设备框选并集/单选
      },
      siteListFlat: [],
      deviceIdList: [],
      crashType: {
        Camera: true,
        Camera_QiuJi: true,
        Camera_QiangJi: true,
        Camera_Face: true,
        Camera_Vehicle: true,
      },
    };
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
    }),
  },
  async created() {
    await this.getMapLayerByType();
  },
  mounted() {},
  methods: {
    selectType(type) {
      this.mapLayerConfig.selectType = type == 1 ? false : true;
    },
    selectlist(value) {
      console.log(value, "value");
      this.deviceIdList = value;
      this.$emit("deviceList", value);
    },
    // 进入页面查询所有点位信息
    async getMapLayerByType() {
      // 1摄像机,2车辆卡口,3WIFI设备,4电子围栏,5RIFD设备,6定位设备，7高空相机，11人脸抓拍机,13监视器，14微卡, 15卡口设备类型', 16ETC
      let deviceTypes = [1, 2, 11];
      this.siteListFlat = [];
      let roleParam = {
        roleId: this.userInfo.roleVoList[0].id,
        filter: this.userInfo.roleVoList[0].initFlag == "1" ? false : true,
        socialResources: 0,
        excludeDeviceTypes: [5, 6, 7],
      };
      let { data } = await getSimpleDeviceList(roleParam);
      data = data.filter((v) => deviceTypes.includes(v[2]));
      this.siteListFlat = [...data];
    },
    cleanBox() {
      this.$refs.mapBase.clearDraw();
    },
  },
};
</script>
<style lang="less" scoped>
.map-box {
  height: 100%;
  position: relative;
}
</style>
