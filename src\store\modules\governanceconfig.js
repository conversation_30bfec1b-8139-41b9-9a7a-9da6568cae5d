import home from '@/config/api/home';
import { defaultConfig } from '@/views/systemconfiguration/governanceconfig/util.js';

export default {
  namespaced: true,
  state: {
    tabList: [], // 视图治理中 可显示的项
    allTabList: [], // 治理项管理 显示列表
  },
  mutations: {
    setTabInfo(state, data) {
      state.allTabList = data;
      state.tabList = data.filter((item) => item.status);
    },
  },
  getters: {
    getTabList(state) {
      return state.tabList;
    },
    getAllTabList(state) {
      return state.allTabList;
    },
  },
  actions: {
    // 获取 治理项管理 显示列表
    async getTabListByApi({ commit }) {
      try {
        let params = {
          key: 'GOVERNANCE_PAGE_CONFIG',
        };
        let {
          data: { data },
        } = await this._vm.$http.get(home.viewByParamKey, { params });
        let arr = data?.paramValue ? JSON.parse(data.paramValue) : [];
        if (arr.length === 0) {
          arr = this._vm.$util.common.deepCopy(defaultConfig);
        }
        commit('setTabInfo', arr);
        return arr;
      } catch (error) {
        return [];
      }
    },
    // 是否有权限跳转到 【视图治理】
    async hasSkipPermission({ dispatch, state }, data) {
      let flag = true;
      try {
        let { to } = data;
        // 是否有权限跳转到 【视图治理】- 具体的 tab项
        if (to.name === 'governanceautomatic' && (to.params.governanceContent || to.query.tab)) {
          let arr = state.tabList;
          if (arr.length === 0) {
            arr = await dispatch('getTabListByApi');
          }
          let code = to.params.governanceContent || to.query.tab;
          flag = arr.some((item) => item.code === code);
        }
        return flag;
      } catch (error) {
        console.log(error);
        return flag;
      }
    },
  },
};
