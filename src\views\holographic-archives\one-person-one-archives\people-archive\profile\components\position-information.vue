<template>
  <ui-card :title="title" class="position-information m-b10">
    <div class="position-information-content">
      <div class="latest-location">
        <div class="header title-color">最新位置</div>
        <div class="latest-location-content">
          <div
            v-for="(item, $index) in formatList"
            :key="$index"
            class="latest-location-li card-border-color card-bg"
          >
            <div class="location-mark">
              <img
                src="@/assets/img/archives/mark-red.png"
                class="location-mark-icon"
                alt
              />
              <div class="location-mark-index error">{{ $index + 1 }}</div>
            </div>
            <div class="location-info">
              <div
                class="primary ellipsis cursor"
                @click="handleList(item, $index)"
              >
                {{
                  item.deviceName ||
                  item.captureAddress ||
                  item.detailAddress ||
                  "--"
                }}
              </div>
              <div class="location-content">
                <div class="capture-img card-border-color">
                  <i v-if="item.type === 3" class="iconfont icon-imsi"></i>
                  <ui-image
                    v-else
                    viewer
                    :type="item.type === 1 ? 'people' : 'vehicle'"
                    :src="item.capturePic || item.sceneImg"
                  />
                </div>
                <div class="location-content-info">
                  <div v-if="item.type === 1" class="sign ellipsis">
                    {{ item.vid }}
                  </div>
                  <div v-if="item.type === 2" class="sign ellipsis">
                    {{ item.plateNo }}
                  </div>
                  <!-- <div v-if="item.type === 2" class="input-color ellipsis">{{item.licensePlateNumber}}</div> -->
                  <div v-if="item.type === 3" class="input-color ellipsis">
                    {{ item.IMSINumber }}
                  </div>
                  <div class="auxiliary-color ellipsis">
                    {{ item.captureTime }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <ui-loading v-if="latestLocationLoading" />
          <ui-empty
            v-if="(!formatList || !formatList.length) && !latestLocationLoading"
          />
        </div>
      </div>
      <div class="map">
        <MapBase
          ref="map"
          isHeatData
          :center="mapCenter"
          :list="formatList"
          @mapModal="mapModal"
          :dotIndex="dotIndex"
          :heatData="heatData"
          @inited="inited"
        />
        <!-- :archivesPositionPoints="positionPoints" -->
      </div>
      <div class="frequent-place frequent-place-date-range">
        <div class="header title-color">
          <span>常去地</span>
          <div class="buttons">
            <ui-time-select
              :value="1"
              @on-change-date="activeHandle"
            ></ui-time-select>
          </div>
        </div>
        <div class="frequent-place-content">
          <div
            v-for="(item, $index) in oftenGoList"
            :key="$index"
            class="frequent-place-li card-border-color card-bg"
          >
            <div class="frequent-place-icon card-border-color">
              <img :src="exclamationPoint" alt="" />
              <!-- <div :class="item.type === 1 ? 'icon-renlianheyan':item.type === 2 ? 'icon-qiche':'icon-imsi'" class="iconfont primary"></div> -->
            </div>
            <div class="frequent-place-info">
              <div class="primary ellipsis">{{ item.captureAddress }}</div>
              <div class="title-color ellipsis">
                最近{{ newActive[active] }}出现<span class="primary">{{
                  item.nums
                }}</span
                >次
              </div>
            </div>
          </div>
          <ui-loading v-if="oftenGoLoading" />
          <ui-empty
            v-if="(!oftenGoList || !oftenGoList.length) && !oftenGoLoading"
          />
        </div>
      </div>
    </div>
    <locationModal
      ref="locationModal"
      @close="handleClose"
      v-if="locationShow"
    ></locationModal>
    <!-- 人 -->
    <details-face-modal
      v-if="faceShow"
      ref="faceDetailRef"
      @close="faceShow = false"
    ></details-face-modal>
    <!-- 车 -->
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehicleDetailRef"
      @close="vehicleShow = false"
    >
    </details-vehicle-modal>
    <!-- 非机动车 -->
    <details-modal
      v-if="nonMotorVehicleShow"
      ref="nonMotorRef"
      @close="nonMotorVehicleShow = false"
    >
    </details-modal>
  </ui-card>
</template>
<script>
import UiTimeSelect from "@/components/ui-time-select.vue";
import MapBase from "@/components/map/index.vue";
import locationModal from "./locationModal";
import getImageCropper from "@/mixins/mixinVehicleCrop";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import detailsModal from "@/components/detail/details-modal.vue";
export default {
  mixins: [getImageCropper],
  components: {
    MapBase,
    locationModal,
    UiTimeSelect,
    detailsFaceModal,
    detailsVehicleModal,
    detailsModal,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    // 最新位置列表
    list: {
      type: Array,
      default: () => [],
    },
    latestLocationLoading: {
      type: Boolean,
      default: false,
    },
    // 常去地列表
    oftenGoList: {
      type: Array,
      default: () => [],
    },
    // 常去地loading
    oftenGoLoading: {
      type: Boolean,
      default: false,
    },
    // 最新位置地图点位
    positionPoints: {
      type: Array,
      default: () => [],
    },
    // 常去地地图热力图
    heatData: {
      type: Array,
      default: () => [],
    },
    modelType: {
      type: String,
      default: "face",
    },
  },
  data() {
    return {
      active: 1,
      newActive: ["", "一周", "一月", "三月"],
      mapLayerConfig: {
        tracing: false, // 无需描边
        showStartPoint: false, // 是否显示起点终点图标
        mapToolVisible: false, // 框选
        resultOrderIndex: true, //marker label是否显示索引
      },
      locationShow: false,
      dotIndex: -1, //点击列表，相应的地图撒点重新渲染
      exclamationPoint: require("@/assets/img/map/exclamationPoint.png"),
      mapCenter: {},
      faceShow: false, // 人脸抓拍详情
      vehicleShow: false, // 车辆抓拍详情
      nonMotorVehicleShow: false, // 非机动车抓拍详情
    };
  },
  computed: {
    // 适配详情，小图展示用的traitImg字段，list中每一项不包含该字段
    formatList() {
      return this.list.map((v) => {
        return {
          ...v,
          traitImg: v.capturePic || v.sceneImg,
        };
      });
    },
  },
  watch: {
    positionPoints: {
      deep: true,
      immediate: true,
      handler(val) {
        this.setMapCenter(val);
      },
    },
  },
  methods: {
    inited() {
      this.$refs.map.resertMapPoint()
    },
    setMapCenter(positionPoints) {
      if (positionPoints.length > 0) {
        let { lon, lat, geoPoint } = positionPoints[0];
        this.mapCenter = {
          lon: lon || geoPoint.lon,
          lat: lat || geoPoint.lat,
        };
        this.$refs.map.mapCenter(this.mapCenter);
      }
    },
    handleList(row, index) {
      if (row.type == 1) {
        this.faceShow = true;
        this.$nextTick(() => {
          this.$refs.faceDetailRef.init(row, this.formatList, index, 5, 1);
        });
      } else {
        if (this.type == "non-motor-archive") {
          this.nonMotorVehicleShow = true;
          this.$nextTick(() => {
            this.$refs.nonMotorRef.init(row, this.formatList, index, 2, 1);
          });
        } else {
          this.vehicleShow = true;
          this.$nextTick(() => {
            this.$refs.vehicleDetailRef.init(row, this.formatList, index, 1);
          });
        }
      }
    },
    /* handleList(item, index) {
      this.locationShow = true;
      this.$nextTick(() => {
        this.$refs.locationModal.show(
          item,
          item.type == 1 ? "face" : "vehicle"
        );
      });
      this.dotIndex = index;
    }, */
    mapModal(item) {
      this.locationShow = true;
      this.$nextTick(() => {
        this.$refs.locationModal.show(
          item,
          item.type == 1 ? "face" : "vehicle"
        );
      });
    },
    handleClose() {
      this.locationShow = false;
    },
    activeHandle(dataRange, times) {
      const [startDate = "", endDate = ""] = times;
      this.active = dataRange;
      this.$emit("on-change", dataRange, startDate, endDate);
    },
  },
};
</script>
<style lang="less" scoped>
.position-information {
  width: 100%;
  .position-information-content {
    height: 438px;
    display: flex;
    .frequent-place-date-range {
      width: 33% !important;
    }
    .latest-location,
    .frequent-place {
      width: 22%;
      height: 100%;
      display: flex;
      flex-direction: column;
      .header {
        width: 100%;
        height: 30px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        font-size: 14px;
        line-height: 20px;
        padding: 0 10px;
        display: flex;
        align-items: center;
        background: rgba(211, 215, 222, 0.5);
        justify-content: space-between;
        .buttons {
          .card-btn,
          .card-active-btn {
            margin-left: 10px;
          }
        }
      }
      .latest-location-content,
      .frequent-place-content {
        width: 100%;
        position: relative;
        flex: 1;
        height: 410px;
        overflow-y: auto;
        .latest-location-li,
        .frequent-place-li {
          width: 100%;
          height: 126px;
          margin-top: 10px;
          padding: 10px 15px;
          display: flex;
          border: 1px solid #fff;
          .location-mark {
            margin-right: 15px;
            position: relative;
            .location-mark-icon {
              width: 32px;
              height: 32px;
            }
            .location-mark-index {
              position: absolute;
              font-size: 14px;
              line-height: 22px;
              top: 0;
              left: 50%;
              transform: translate(-50%, 0);
            }
          }
          .location-info {
            display: flex;
            flex: 1;
            flex-direction: column;
            margin-top: 6px;
            overflow: hidden;
            .primary {
              font-family: "MicrosoftYaHei-Bold";
              font-weight: bold;
              font-size: 14px;
              line-height: 20px;
              color: #2c86f8;
            }
            .cursor {
              cursor: pointer;
            }
            .location-content {
              display: flex;
              flex-direction: row;
              align-items: center;
              margin-top: 10px;
              .capture-img {
                width: 70px;
                min-width: 70px;
                height: 70px;
                margin-right: 10px;
                border: 1px solid #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                img {
                  width: 100%;
                  height: 100%;
                  object-fit: contain;
                  cursor: pointer;
                }
                .icon-imsi {
                  font-size: 30px;
                  color: #5ba3ff;
                }
              }
              .location-content-info {
                display: flex;
                flex-direction: column;
                .sign {
                  font-size: 14px;
                  color: rgba(0, 0, 0, 0.75);
                  margin-bottom: 5px;
                }
                .input-color {
                  font-size: 14px;
                  line-height: 20px;
                  margin-bottom: 5px;
                }
                .auxiliary-color {
                  font-size: 12px;
                  line-height: 18px;
                  color: rgba(0, 0, 0, 0.35);
                }
              }
            }
          }
          .frequent-place-icon {
            width: 50px;
            height: 100%;
            background: #ebf4ff;
            border-right: 1px solid #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            .iconfont {
              font-size: 28px;
            }
          }
          .frequent-place-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            padding: 0 20px;
            box-sizing: border-box;
            overflow: hidden;
            font-size: 14px;
            flex: 1;
            .primary {
              font-weight: bold;
              font-family: "MicrosoftYaHei-Bold";
              line-height: 20px;
              margin-bottom: 20px;
            }
            .title-color {
              .primary {
                margin: 0 2px;
              }
            }
          }
        }
        .frequent-place-li {
          padding: 0;
        }
      }
    }
    .map {
      width: 56%;
      height: 100%;
      padding: 0 10px;
    }
  }
}
</style>
