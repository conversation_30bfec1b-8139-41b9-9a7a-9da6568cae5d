<template>
  <Particular class="particular" v-bind="customizedAttrs" @handlePageSize="handlePageSize" @handlePage="handlePage">
    <template #otherButton>
      <div class="other-button fr mt-sm">
        <span class="font-active-color mr-sm pointer">更新统计结果</span>
        <Button type="primary" class="mr-sm"> <i class="icon-font icon-piliangfujian"></i> 批量复检 </Button>
        <Button type="primary"> <i class="icon-font icon-daochu"></i> 导出 </Button>
      </div>
    </template>
    <!-- 表格插槽 -->
    <template #detectionMode="{ row }">
      {{ row.detectionMode == 1 ? 'OCR' : 'SDK' }}
    </template>
    <template slot="qualified" slot-scope="{ row }">
      <Tag :color="qualifiedColorConfig[row.qualified].color">{{ qualifiedColorConfig[row.qualified].dataValue }} </Tag>
    </template>
    <template #phyStatus="{ row }">
      <span
        :style="{
          color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)',
        }"
        >{{ row.phyStatusText }}</span
      >
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template slot="action" slot-scope="{ row }">
      <ui-btn-tip
        v-for="(item, index) in row.operationList"
        :key="index"
        class="operatbtn mr-sm"
        :icon="item.iconName"
        :styles="{ 'color': item.iconColor, 'font-size': '16px!important' }"
        :content="item.name"
        @click.native="item.func(row)"
      ></ui-btn-tip>
    </template>
  </Particular>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
import {
  defaultIconStaticsList,
  qualifiedColorConfig,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export default {
  mixins: [particularMixin],
  inheritAttrs: false,
  props: {
    tableColumns: {
      default: () => [],
    },
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
          indexId: null,
        };
      },
    },
  },
  data() {
    return {
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      iconList: defaultIconStaticsList,
      formData: {
        deviceId: '',
        qualified: '',
        deviceName: '',
      },
      formItemData: [
        {
          type: 'input',
          key: 'deviceId',
          label: '设备编码',
        },
        {
          type: 'input',
          key: 'deviceName',
          label: '设备名称',
        },
        {
          type: 'select',
          key: 'qualified',
          label: '检测结果',
          placeholder: '请选择检测结果',
          options: Object.keys(qualifiedColorConfig).map((key) => {
            return { value: key, label: qualifiedColorConfig[key].dataValue };
          }),
        },
        {
          type: 'select',
          key: 'qualified',
          label: '不合格原因',
          placeholder: '请选择不合格原因',
          options: [
            { value: '1', label: '达标' },
            { value: '2', label: '不达标' },
          ],
        },
      ],
      // 表格操作按钮
      optionListObject: Object.freeze({
        detail: {
          name: '查看详情',
          func: (row) => this.viewDetail(row),
          iconName: 'icon-chakanxiangqing',
          iconColor: '#438CFF',
        },
        recheck: {
          name: '复检',
          func: (row) => this.recheck(row),
          iconName: 'icon-fujian',
          iconColor: '#269F26',
        },
        manualReview: {
          name: '人工复核',
          func: (row) => this.manualReview(row),
          iconName: 'icon-fuhejieguo',
          iconColor: '#DE990F ',
        },
        inspectionRecord: {
          name: '检测记录',
          func: (row) => this.inspectionRecord(row),
          iconName: 'icon-jiancejilu',
          iconColor: '#18AFCF',
        },
      }),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
    };
  },
  created() {
    this.getTableData();
    this.iconList = [
      ...defaultIconStaticsList,
      {
        name: `${this.activeIndexItem.indexName}`,
        count: '0',
        countStyle: {
          color: 'var(--color-bluish-green-text)',
        },
        style: {
          color: 'var(--color-bluish-green-text)',
        },
        iconName: 'icon-xinxichayishebei',
        fileName: 'resultValueFormat',
      },
    ];
    this.getStatInfo().then((data) => {
      this.iconList.forEach((item) => (item.count = data[item.fileName]));
    });
  },
  methods: {
    viewDetail() {},
    recheck() {},
    manualReview() {},
    inspectionRecord() {},
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    async getTableData() {
      try {
        this.tableLoading = true;
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.activeIndexItem.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
          customParameters: this.formData,
          ...this.pageData,
        };
        params.displayType === 'REGION'
          ? (params.orgRegionCode = this.$route.query.civilCode)
          : (params.orgRegionCode = this.$route.query.orgCode);
        let { data } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = data.data.entities;
        this.totalCount = data.data.total;
        this.handleTableValueAndAction();
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    handleEmptyValue() {},
    handleTableValueAndAction() {
      this.tableData.forEach((item) => {
        Object.keys(item).map((key) => {
          !item[key] ? (item[key] = '--') : null;
        });
        item.operationList = Object.values(this.optionListObject);
      });
    },
  },
  watch: {},
  computed: {
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        formItemData: this.formItemData,
        formData: this.formData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        // // 支持传过来的size覆盖默认的size
        // ...this.$attrs,
      };
    },
  },
  components: {
    Particular: require('../../ui-pages/particular.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
}
</style>
