<template>
  <ui-modal title="比对明细" :footer-hide="true" v-model="visible" width="90%" @onCancel="visible = false">
    <div class="compare-result">
      <div class="f-18 font-blue">对比明细</div>
      <div class="result-wrapper">
        <div class="left-rusult">
          <div class="left-statistics-wrapper base-text-color">
            <div class="base-text-color mb-sm">对比时间：{{ stateInfo.currentDate || '暂无' }}</div>
            <div class="statistics" v-ui-loading="{ loading: abnormalListLoading }">
              <div class="statistics-item" v-for="(item, index) in statisticalQuantityList" :key="index">
                <span class="icon-font f-37 vt-middle mr-sm" :class="item.icon" :style="{ color: item.color }"></span>
                <div class="inline vt-middle line">
                  <p>{{ item.name || item.leftName }}</p>
                  <p :style="{ color: item.color }">{{ item.leftCount }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="result-tabs mt-sm">
            <tag-view :list="tabList" @tagChange="changeMode" ref="tagView" class="tag-view mt-xs"></tag-view>
            <div class="left-title" v-show="currentMode === 1">
              <span class="f-16 font-blue vt-middle ml-lg">撤销设备</span>
            </div>
          </div>
          <div class="ui-table-wrapper" v-ui-loading="{ loading: leftTableLoading, tableData: leftTableData }">
            <!--  v-if 和 v-ui-loading 组合使用，保证两个组件同时渲染，防止render报错 -->
            <ui-table
              ref="leftTable"
              class="ui-table auto-fill mt-sm mr-sm"
              :table-columns="tableColumns"
              :table-data="leftTableData"
              :specialJsx="' '"
              @selectTable="leftSelectTable"
            >
            </ui-table>
          </div>
          <ui-page
            class="page menu-content-background"
            :page-data="leftPageData"
            @changePage="leftHandlePage"
            @changePageSize="leftHandlePageSize"
          >
          </ui-page>
        </div>
        <div class="right-rusult">
          <div class="right-statistics-wrapper base-text-color">
            <div class="base-text-color mb-sm">数据时间：{{ stateInfo.originDate || '暂无' }}</div>
            <div class="statistics" v-ui-loading="{ loading: abnormalListLoading }">
              <div class="statistics-item" v-for="(item, index) in statisticalQuantityList" :key="index">
                <span class="icon-font f-37 vt-middle mr-sm" :class="item.icon" :style="{ color: item.color }"></span>
                <div class="inline vt-middle line">
                  <p>{{ item.name || item.rightName }}</p>
                  <p :style="{ color: item.color }">{{ item.rightCount }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="result-tabs mt-sm">
            <div class="right-title" v-show="currentMode === 1">
              <span class="f-16 font-blue vt-middle">新增设备</span>
            </div>
            <span>
              <Checkbox v-model="isAll" @on-change="changeCheckbox">全部</Checkbox>
              <Button type="primary" @click="onExport" :loading="exportLoading">
                <i class="icon-font icon-daochu mr-sm"></i>导出
              </Button>
            </span>
          </div>
          <div class="ui-table-wrapper" v-ui-loading="{ loading: rightTableLoading, tableData: rightTableData }">
            <ui-table
              ref="rightTable"
              class="ui-table auto-fill mt-sm pl-sm"
              :table-columns="tableColumns"
              :table-data="rightTableData"
              :specialJsx="' '"
              @selectTable="rightSelectTable"
            >
            </ui-table>
          </div>

          <ui-page
            class="page menu-content-background"
            :page-data="rightPageData"
            @changePage="rightHandlePage"
            @changePageSize="rightHandlePageSize"
          >
          </ui-page>
        </div>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';

export default {
  components: {
    TagView: require('@/components/tag-view.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  mixins: [downLoadTips],
  props: {
    value: {},
    row: {},
    isStatisticRow: {},
  },
  data() {
    return {
      currentMode: 0,
      visible: false,
      leftTableData: [],
      leftTableLoading: false,
      leftPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      rightPageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tabList: Object.freeze(['修改', '独有']),

      tableColumns: [
        {
          type: 'selection',
          align: 'center',
          width: 50,
        },
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
        },
        {
          title: this.global.filedEnum.deviceId,
          key: 'deviceId',
          minWidth: 200,
          ellipsis: true,
          tooltip: true,
          render: this.render,
        },
        {
          title: this.global.filedEnum.deviceName,
          key: 'deviceName',
          minWidth: 200,
          ellipsis: true,
          tooltip: true,
          render: this.render,
        },
        {
          title: this.global.filedEnum.longitude,
          key: 'longitude',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.latitude,
          key: 'latitude',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.macAddr,
          key: 'macAddr',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.sbgnlx,
          key: 'sbgnlxText',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: this.global.filedEnum.sbcjqy,
          key: 'sbcjqyText',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: '所属单位',
          key: 'orgName',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
        {
          title: '行政区划',
          key: 'civilName',
          ellipsis: true,
          tooltip: true,
          minWidth: 150,
          render: this.render,
        },
      ],
      rightTableData: [],
      rightTableLoading: false,
      abnormalListLoading: false,
      stateInfo: {},
      statisticalQuantityList: [
        {
          name: '设备总量',
          leftCount: 0,
          rightCount: 0,
          color: '#22C326',
          icon: 'icon-jianceshebeishuliang',
          leftKey: 'originDeviceCount',
          rightKey: 'currentDeviceCount',
        },
        {
          name: '信息相同设备',
          leftCount: 0,
          rightCount: 0,
          color: 'var(--color-primary)',
          icon: 'icon-xinxixiangtongshebei',
          leftKey: 'sameCount',
          rightKey: 'sameCount',
        },
        {
          name: '信息差异设备',
          leftCount: 0,
          rightCount: 0,
          color: '#DD4826',
          icon: 'icon-xinxichayishebei',
          leftKey: 'differenceCount',
          rightKey: 'differenceCount',
        },
        {
          leftName: '撤销设备',
          rightName: '新增设备',
          leftCount: 0,
          rightCount: 0,
          color: '#19D5F6',
          icon: 'icon-duyoushebei',
          leftKey: 'originOnlyHaveCount',
          rightKey: 'currentOnlyHaveCount',
        },
      ],
      activeRow: {},
      leftSelection: [],
      rightSelection: [],
      exportLoading: false,
      isAll: false,
    };
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    row: {
      deep: true,
      immediate: true,
      handler(val) {
        this.activeRow = val;
      },
    },
  },
  async created() {
    this.visible = true;
    this.initLeftList();
    this.initRightList();
    this.getStatInfo();
  },
  async mounted() {},
  beforeDestroy() {
    this.$refs.rightTable &&
      this.$refs.rightTable.$refs.table.$refs.body.removeEventListener('scroll', this.asyncScroll);
    this.$refs.leftTable && this.$refs.leftTable.$refs.table.$refs.body.removeEventListener('scroll', this.asyncScroll);
  },
  methods: {
    changeCheckbox(val) {
      this.rightTableData.map((item) => {
        this.$set(item, '_disabled', val);
      });
      this.leftTableData.map((item) => {
        this.$set(item, '_disabled', val);
      });
    },
    handleScroll() {
      this.$refs.rightTable &&
        this.$refs.rightTable.$refs.table.$refs.body.addEventListener('scroll', this.asyncScroll);
      this.$refs.leftTable && this.$refs.leftTable.$refs.table.$refs.body.addEventListener('scroll', this.asyncScroll);
    },
    asyncScroll(event) {
      if (this.$refs.rightTable) {
        this.$refs.rightTable.$refs.table.$refs.body.scrollLeft = event.target.scrollLeft;
      }
      if (this.$refs.leftTable) {
        this.$refs.leftTable.$refs.table.$refs.body.scrollLeft = event.target.scrollLeft;
      }
    },
    setStatisticalQuantityList(statInfo) {
      this.statisticalQuantityList.forEach((item) => {
        item.leftCount = statInfo[item.leftKey] || 0;
        item.rightCount = statInfo[item.rightKey] || 0;
      });
    },
    async getStatInfo() {
      const { regionCode, orgCode, statisticType } = this.$route.query;
      let { batchId, indexId } = this.activeRow;
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        customParameters: {
          comparisonType: this.isStatisticRow ? 2 : 1,
        },
      };
      this.abnormalListLoading = true;
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.stateInfo = (data && data.revocationStatVo) || {};
        this.setStatisticalQuantityList(this.stateInfo);
      } catch (err) {
        console.log(err);
      } finally {
        this.abnormalListLoading = false;
      }
    },
    render(h, { row, index, column }) {
      if (
        this.currentMode === 0 &&
        this.leftTableData?.[index]?.[column.key] != this.rightTableData?.[index]?.[column.key]
      ) {
        return <span class="font-red">{row[column.key]}</span>;
      } else {
        return <span>{row[column.key]}</span>;
      }
    },
    async onExport({ customParameters }) {
      const { regionCode, orgCode, statisticType } = this.$route.query;
      let { batchId, indexId } = this.activeRow;
      let leftIds = this.leftSelection.map((item) => item.id);
      let rightIds = this.rightSelection.map((item) => item.id);
      let dataTypeMap = {
        [this.currentMode === 0 ? 'dataType_3' : 'dataType_1']: this.isAll ? [] : leftIds,
        [this.currentMode === 0 ? 'dataType_4' : 'dataType_2']: this.isAll ? [] : rightIds,
      };
      const params = {
        indexId: indexId,
        batchId: batchId,
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        exportWriteHandlerModel: 2,
        customParameters: {
          ...customParameters,
          comparisonType: this.isStatisticRow ? 2 : 1,
          dataTypeMap: {
            ...dataTypeMap,
          },
        },
      };
      this.exportLoading = true;
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportSecondModelData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    leftSelectTable(selection) {
      this.leftSelection = selection;
    },
    rightSelectTable(selection) {
      this.rightSelection = selection;
    },
    async initList({ customParameters, ...searchData }) {
      const { regionCode, orgCode, statisticType } = this.$route.query;
      let { batchId, indexId } = this.activeRow;
      const params = {
        indexId: indexId,
        batchId: batchId,
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        customParameters: {
          ...customParameters,
          comparisonType: this.isStatisticRow ? 2 : 1,
        },
        ...searchData,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getPolyData, params);
        if (this.isAll && data?.entities.length) {
          data.entities.map((item) => {
            item._disabled = true;
          });
        }
        return data;
      } catch (err) {
        console.log(err);
        return new Promise((resolve, reject) => reject);
      }
    },
    async initLeftList() {
      try {
        // 数据类型 1撤销数据 2新增数据 3修改前数据 4修改后数据
        this.leftTableLoading = true;
        let params = {
          customParameters: {
            dataType: this.currentMode === 0 ? 3 : 1,
          },
          pageNumber: this.leftPageData.pageNum,
          pageSize: this.leftPageData.pageSize,
        };
        let { entities, total } = await this.initList(params);
        this.leftTableData = entities;
        this.leftPageData.totalCount = total;
        this.$nextTick(this.handleScroll);
      } catch (e) {
        console.log(e);
      } finally {
        this.leftTableLoading = false;
      }
    },
    async initRightList() {
      try {
        this.rightTableLoading = true;
        let params = {
          customParameters: {
            dataType: this.currentMode === 0 ? 4 : 2,
          },
          pageNumber: this.rightPageData.pageNum,
          pageSize: this.rightPageData.pageSize,
        };
        let { entities, total } = await this.initList(params);
        this.rightTableData = entities;
        this.rightPageData.totalCount = total;
        this.$nextTick(this.handleScroll);
      } catch (e) {
        console.log(e);
      } finally {
        this.rightTableLoading = false;
      }
    },
    leftHandlePage(val) {
      this.leftPageData.pageNum = val;
      this.initLeftList();
    },
    leftHandlePageSize(val) {
      this.leftPageData.pageNum = 1;
      this.leftPageData.pageSize = val;
      this.initLeftList();
    },
    rightHandlePage(val) {
      this.rightPageData.pageNum = val;
      this.initRightList();
    },
    rightHandlePageSize(val) {
      this.rightPageData.pageNum = 1;
      this.rightPageData.pageSize = val;
      this.initRightList();
    },
    async changeMode(index) {
      if (this.currentMode === index) return;
      this.currentMode = index;
      await this.initLeftList();
      await this.initRightList();
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  //height: 800px;
  overflow: hidden;
}
.f-37 {
  font-size: 37px;
}

.compare-result {
  position: relative;
  .result-wrapper {
    display: flex;

    .left-rusult {
      max-width: 50%;
      flex: 1;
      .left-statistics-wrapper {
        padding-right: 10px;
        .statistics {
          height: 110px;
          background: var(--bg-sub-content);
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          .statistics-item {
            position: relative;
            .icon-font {
              margin-left: 30px;
            }
            &:first-child {
              .line::before {
                content: '';
                width: 0;
              }
              .icon-font {
                margin-left: 0;
              }
            }
            .line {
              &::before {
                position: absolute;
                left: 0;
                top: 10px;
                content: '';
                height: 40px;
                width: 1px;
                background: #1568ad;
              }
            }
          }
        }
      }
      .result-tabs {
        padding: 10px;
        height: 48px;
        background: var(--bg-sub-content);
        display: flex;
        align-items: center;
        .left-title {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .ui-table-wrapper {
        position: relative;
        height: 500px;
        .ui-table {
          height: 100%;
        }
      }
    }

    .right-rusult {
      max-width: 50%;
      flex: 1;
      .right-statistics-wrapper {
        padding-left: 10px;
        border-left: 1px solid var(--devider-line);
        .statistics {
          height: 110px;
          background: var(--bg-sub-content);
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          .statistics-item {
            position: relative;
            .icon-font {
              margin-left: 30px;
            }
            &:first-child {
              .line::before {
                content: '';
                width: 0;
              }
              .icon-font {
                margin-left: 0;
              }
            }
            .line {
              &::before {
                position: absolute;
                left: 0;
                top: 10px;
                content: '';
                height: 40px;
                width: 1px;
                background: #1568ad;
              }
            }
          }
        }
      }
      .result-tabs {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 48px;
        background: var(--bg-sub-content);
        .right-title {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .ui-table-wrapper {
        position: relative;
        padding-left: 10px;
        border-left: 1px solid var(--devider-line);
        height: 500px;
        .ui-table {
          height: 100%;
        }
      }
    }
  }
}
</style>
