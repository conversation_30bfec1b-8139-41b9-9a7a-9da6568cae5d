<template>
  <div class="slide-org-tree auto-fill">
    <Dropdown trigger="click" @on-click="changeMenu">
      <div class="tree-title f-14 pointer">
        {{ activeName }}
        <Icon type="ios-arrow-down" color="white"></Icon>
      </div>
      <DropdownMenu slot="list">
        <DropdownItem name="orgCode" class="base-text-color">系统组织目录</DropdownItem>
        <DropdownItem name="civilCode" class="base-text-color">系统行政目录</DropdownItem>
      </DropdownMenu>
    </Dropdown>
    <ui-search-tree
      ref="uiSearchTree"
      :key="activeName"
      placeholder="请输入组织机构名称"
      class="ui-search-tree mt-lg"
      :check-strictly="true"
      :show-checkbox="true"
      :tree-data="treeData"
      :default-props="defaultProps"
      :default-keys="defaultExpandedKeys"
      :node-key="nodeKey"
      @check="handleCheckChange"
    >
    </ui-search-tree>
    <div class="author-btns">
      <Button class="mr-sm" @click.stop="$emit('cancel')">取消</Button>
      <Button type="primary" @click.stop="$emit('moveSave', activeOrg)">确定</Button>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  name: 'exceptionlibrary',
  props: {},
  data() {
    return {
      nodeKey: 'id',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      treeData: [],
      selectKey: '',
      activeOrg: {
        orgCode: null,
        orgName: '',
      },
      activeName: '系统组织目录',
    };
  },
  async created() {
    await this.setOrganizationList();
    this.setAreaList();
    // this.selectKey = this.getDefaultSelectedOrg.orgCode
    this.treeData = this.getOrganizationList;
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
      setAreaList: 'common/setAreaList',
    }),
    changeMenu(name) {
      if (name === 'orgCode') {
        this.treeData = this.getOrganizationList;
        this.activeName = '系统组织目录';
        this.defaultProps = {
          label: 'orgName',
          children: 'children',
        };
        this.nodeKey = 'id';
      } else {
        this.treeData = this.getAreaList;
        this.activeName = '系统行政目录';
        this.defaultProps = {
          label: 'regionName',
          children: 'children',
        };
        this.nodeKey = 'regionCode';
      }
      console.log(name);
    },
    handleCheckChange(data, checkData) {
      this.clearTree();
      this.$nextTick(() => {
        this.$refs.uiSearchTree.$refs.uiTree.setCheckedKeys([checkData[this.nodeKey]]);
      });
      this.activeOrg = checkData;
    },
    // 共外面调用
    clearTree() {
      this.$refs.uiSearchTree.$refs.uiTree.setCheckedKeys([]);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getOrganizationList: 'common/getOrganizationList',
      getAreaList: 'common/getAreaList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .slide-org-tree {
    border: 1px solid var(--color-primary);
    @{_deep}.tree-title {
      background: var(--color-primary);
    }
  }
}
.slide-org-tree {
  position: absolute;
  background: var(--bg-content);
  border: 1px solid #2169b9;
  height: 100%;
  z-index: 200;
  right: 0;
  top: 0;
  @{_deep}.tree-title {
    background: #2169b9;
    height: 45px;
    line-height: 45px;
    color: #fff;
    text-align: center;
  }
  @{_deep}.ivu-dropdown-item {
    &:hover {
      background: var(--bg-select-item-active) !important;
    }
  }
  .ui-search-tree {
    height: calc(100% - 120px);
    padding: 0 20px;
  }
  .author-btns {
    margin: 10px auto;
  }
  @{_deep}.custom-tree-node {
    width: 160px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
