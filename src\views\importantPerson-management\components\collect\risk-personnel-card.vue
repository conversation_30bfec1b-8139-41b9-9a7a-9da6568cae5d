<!--
 * @Date: 2025-01-15 14:05:53
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-03 10:43:03
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\school-out-alarm.vue
-->
<template>
  <div class="risk-personnel-card">
    <div class="image-box">
      <!-- <img :src="data.faceCaptureVo.traitImg" alt="" /> -->
      <img :src="data.traitImg" alt="" />
    </div>
    <div class="list-box">
      <div class="box-tag">
        {{ getRiskyBehaviorName(data.riskyBehaviorType) }}
      </div>
      <div class="item" v-for="item in itemList" :key="item.param">
        <div class="info-name">{{ item.title }}:</div>
        <div class="info-content info-color-sub" :style="item.style">
          {{ data[item.param] || "--" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
export default {
  name: "risk-personnel-card",
  props: {
    // 数据
    data: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      itemList: [
        {
          icon: "icon-time",
          param: "absTime",
          title: "抓拍时间",
          style: {},
        },
        {
          icon: "icon-location",
          param: "deviceName",
          title: "抓拍地点",
          style: {},
        },
      ],
    };
  },
  methods: {
    getRiskyBehaviorName(val) {
      if (!val) return "--";
      return this.riskyBehaviorTypeList.find((item) => item.dataKey == val)
        ?.dataValue;
    },
  },
  computed: {
    ...mapGetters({
      riskyBehaviorTypeList: "dictionary/getRiskyBehaviorTypeList",
    }),
  },
};
</script>

<style lang="less" scoped>
.risk-personnel-card {
  width: 320px;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  padding: 10px;
  .image-box {
    width: 100%;
    height: 160px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .list-box {
    margin-top: 10px;
    .box-tag {
      font-weight: 700;
      font-size: 16px;
      color: #2c86f8;
      line-height: 20px;
    }
    .item {
      display: flex;
      gap: 8px;
      font-size: 14px;
      .info-content {
        flex: 1;
        font-weight: bold;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }
  }
}
</style>
