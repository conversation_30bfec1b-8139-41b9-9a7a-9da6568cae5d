<template>
  <div class="auto-fill">
    <div class="review-particular auto-fill">
      <div class="statistics mb-sm mt-sm">
        <icon-statics :icon-list="iconList"></icon-statics>
        <Button type="primary" class="fr" @click="openExportModal"> <i class="icon-font icon-daochu"></i> 导出 </Button>
      </div>
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
      >
        <template #qualified="{ row }">
          <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
            {{ row.qualifiedDesc || '--' }}
          </span>
        </template>

        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-duibimingxi"
            content="对比明细"
            class="vt-middle f-14"
            @click.native="compareDetail(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
    </div>
    <ui-table
      v-ui-loading="{ loading: tableLoading, tableData: statisticTableData }"
      class="ui-table-bottom"
      :table-columns="statisticTableColumns"
      :table-data="statisticTableData"
      :showHeader="false"
      :span-method="handleSpan"
    >
      <template #option="{ row, index }">
        <!--  最终变化 显示对比明细      -->
        <ui-btn-tip
          v-if="index === 1"
          icon="icon-duibimingxi"
          content="对比明细"
          class="vt-middle f-14"
          @click.native="statistiCompareDetail(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>

    <comparison-result
      v-if="resultVisible"
      v-model="resultVisible"
      :row="activeRow"
      :isStatisticRow="isStatisticRow"
    ></comparison-result>

    <!-- 导出 -->
    <export-data
      ref="exportModule"
      :export-loading="exportLoading"
      :org-region-code="codeKey"
      @handleExport="handleExport"
    >
    </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import {
  tableColumns,
  iconStaticsList,
} from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoDeviceRevocation/util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';
export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      customSearch: false,
      resultVisible: false,
      customizeAction: {
        title: '选择导出设备包含标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
      },
      defaultCheckedList: [],
      chooseOne: {
        tagList: [],
      },
      //
      iconList: iconStaticsList,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      tableLoading: false,
      tableColumns: [],
      indexDetectionModeMap: Object.freeze({
        '1': '在线状态',
        '2': '完好状态',
        '3': '可用状态',
        '4': '联网质量',
        null: '',
      }),
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      activeRow: {},
      isStatisticRow: false,
      checkPicture: false, //查看图片
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
      cardList: [],
      statisticShow: false,
      activeMode: 'device',
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      statisticalList: {},
      recheckVisible: false,
      moduleData: {},
      isBatchRecheck: false,
      artificialVisible: false,
      detailData: {},
      statisticTableColumns: [
        {
          title: '',
          align: 'center',
          width: 50,
        },
        {
          title: '对比时间',
          key: 'comparisonDate',
          align: 'left',
          tooltip: true,
          render: (h, { index }) => {
            if (index === 0) {
              return <span class="color-statistical-name">累加变化</span>;
            } else if (index === 1) {
              return <span class="color-statistical-name">最终变化</span>;
            }
          },
        },
        {
          title: '设备总量',
          key: 'deviceTotal',
          align: 'left',
          tooltip: true,
          // minWidth: 260, differenceValueByDeviceTotal
          render: (h, { row }) => {
            return <span class={['color-statistical-num', 'f-16']}>{row.deviceTotal || 0}</span>;
          },
        },
        {
          title: '撤销',
          key: 'revocationQuantity',
          align: 'left',
          tooltip: true,
          render: (h, { row }) => {
            return <span class={['color-statistical-num', 'f-16']}>{row.revocationQuantity || 0}</span>;
          },
        },
        {
          title: '新增',
          key: 'addQuantity',
          align: 'left',
          tooltip: true,
          render: (h, { row }) => {
            return <span class={['color-statistical-num', 'f-16']}>{row.addQuantity || 0}</span>;
          },
        },
        {
          title: '修改',
          key: 'updateQuantity',
          align: 'left',
          tooltip: true,
          render: (h, { row }) => {
            return <span class={['color-statistical-num', 'f-16']}>{row.updateQuantity || 0}</span>;
          },
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          tooltip: true,
          width: 60,
          fixed: 'right',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
        },
      ],
      statisticTableLoading: false,
      statisticTableData: [],
      exportLoading: false,
      codeKey: '',
    };
  },
  created() {
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    // 获取统计
    async getStatInfo() {
      let { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
      let params = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'REPORT_MODE',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        customParameters: {
          comparisonType: 1,
        },
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.statisticalList = data;
        this.tableColumns = tableColumns({
          statisticalList: this.statisticalList,
        });
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValueFormat') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      } catch (err) {
        console.log(err);
      }
    },
    compareDetail(row) {
      this.activeRow = row;
      this.resultVisible = true;
      this.isStatisticRow = false;
    },
    statistiCompareDetail() {
      /**
       *  取第一个
       */
      this.activeRow = this.tableData[0];
      this.resultVisible = true;
      this.isStatisticRow = true;
    },
    handleSpan() {},
    initAll() {
      this.codeKey = this.$route.query.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      // 获取列表
      this.getTableData();
      // 获取统计
      this.getStatInfo();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    startSearch(params) {
      this.getTableData();
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.getTableData();
    },
    getTableData() {
      this.MixinGetTableData().then((data) => {
        if (data.entities && data.entities.length > 2) {
          this.tableData = data.entities.slice(0, data.entities.length - 2);
          this.statisticTableData = data.entities.slice(data.entities.length - 2, data.entities.length);
        }
      });
    },
    // 导出
    openExportModal() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    async handleExport(val) {
      // 导出综合数据
      if (val.exportType === 'orgOrRegion') {
        this.exportAll(val);
      } else {
        const params = {
          displayType: this.$route.query.statisticType,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        this.MixinGetExport(params);
      }
    },
    // 导出综合数据
    async exportAll(val) {
      try {
        this.exportLoading = true;
        let { indexId, batchId, statisticType, regionCode, orgCode } = this.$route.query;
        let params = {
          indexId: indexId,
          batchId: batchId,
          customParameters: this.formData,
          displayType: statisticType,
          ...val.apiParams,
          taskIndexId: this.activeIndexItem.taskIndexId,
        };
        params.orgRegionCode = params.displayType === 'REGION' ? regionCode : orgCode;
        this.$_openDownloadTip();
        const res = await this.$http.post(detectionResult.exportSynthesisData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  computed: {
    customizedAttrs() {
      return {
        iconList: this.iconList,
        tableColumns: this.tableColumns,
        tableData: this.tableData,
        tableLoading: this.tableLoading,
        totalCount: this.totalCount,
        cardList: this.cardList,
        isPage: false,
      };
    },
  },
  components: {
    IconStatics: require('@/components/icon-statics.vue').default,
    ComparisonResult: require('./components/comparison-result.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    ExportData: require('./components/export-data.vue').default,
  },
};
</script>
<style lang="less" scoped>
.review-particular {
  .statistics {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }
}
.ui-table-bottom {
  height: 120px;
  @{_deep} .ivu-table-tbody {
    tr {
      border: 1px solid var(--border-color);
    }
  }
}
@{_deep} .icon-duibimingxi {
  color: #438cff;
}
@{_deep} .color-up {
  color: var(--color-success);
}
@{_deep} .color-down {
  color: var(--color-failed);
}
@{_deep} .color-statistical-num {
  color: var(--font-card-cyan);
}
@{_deep} .color-statistical-name {
  color: #a9bed9;
}
</style>
