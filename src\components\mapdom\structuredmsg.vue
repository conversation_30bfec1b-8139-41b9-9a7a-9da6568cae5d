<!--
    * @FileDescription: 结构化信息
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
  <div class="structuring">
    <span @mouseenter="handleMouseEnter($event)">结构化信息</span>
    <div class="struct-box">
      <p class="headline">结构化信息:</p>
      <div class="struct-box-content">
        <div
          v-for="(item, index) in strList"
          :key="index"
          class="struct-box-list"
        >
          <p class="struct-title">{{ item.title }}</p>
          <span>:</span>

          <span class="struct-content" v-if="item.dictionary == 'sbgnlxList'">
            <span v-if="!Array.isArray(facilitySplit(info[item.key]))">
              {{ info[item.key] | commonFiltering(translate(item.dictionary)) }}
            </span>
            <span
              v-else
              v-for="(ite, inde) in facilitySplit(info[item.key])"
              :key="inde"
            >
              {{ ite | commonFiltering(translate(item.dictionary))
              }}{{ inde + 1 < facilitySplit(info[item.key]).length ? "/" : "" }}
            </span>
          </span>
          <span class="struct-content" v-else-if="item.type == 'filter'">{{
            info[item.key] | commonFiltering(translate(item.dictionary))
          }}</span>
          <span class="struct-content" v-else-if="item.type == 'wear'">{{
            handleWear(info[item.key])
          }}</span>
          <span class="struct-content" v-else>{{
            info[item.key] || "--"
          }}</span>

          <!-- <p class="struct-content" v-if="item.type == 'filter'">{{ info[item.key] | commonFiltering(translate(item.dictionary)) }}</p>
                    <p class="struct-content" v-else-if="item.type == 'wear'">{{ handleWear(info[item.key]) }}</p>
                    <p class="struct-content" v-else>{{ info[item.key] || '--' }}</p> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  homanBodyList,
  nonmotorList,
  veicleList,
  faceList,
} from "@/components/detail/structuring.js";
import { commonMixins } from "@/mixins/app.js";
export default {
  name: "",
  components: {},
  mixins: [commonMixins], //全局的mixin
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: "1",
    },
  },
  data() {
    return {
      msgShow: {
        1: faceList,
        2: veicleList,
        3: homanBodyList,
        4: nonmotorList,
      },
      strList: [],
    };
  },
  watch: {},
  computed: {},
  async created() {
    await this.getDictData();
    this.strList = this.msgShow[this.type];
  },
  mounted() {},
  methods: {
    handleMouseEnter(event) {
      // console.log(event.clientY, 'clientY')
    },
  },
};
</script>

<style lang='less' scoped>
.structuring {
  margin-top: 10px;
  font-size: 12px;
  color: #2c86f8;
  position: relative;
  width: 60px;
  cursor: pointer;
  .struct-box {
    cursor: auto;
    position: absolute;
    width: 230px;
    // max-height: 463px;
    background: #ffffff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    left: 72px;
    bottom: -100px;
    padding: 15px 0 10px 20px;
    border-radius: 5px;
    display: none;
    z-index: 1;
    .headline {
      font-size: 12px;
      font-weight: bold;
      color: #2c86f8;
      margin-bottom: 5px;
    }
    .struct-box-content {
      max-height: 480px;
      overflow-y: auto;
    }
    &-list {
      display: flex;
      font-size: 12px;
      margin-bottom: 5px;
      .struct-title {
        color: rgba(0, 0, 0, 0.6);
        // width: 74px;
        width: 100px;
        min-width: 100px;
        // text-align: justify;
        // text-align-last: justify;
        // text-justify: inter-ideograph;
        text-align: right;
      }
      span {
        color: rgba(0, 0, 0, 0.6);
      }
      .struct-content {
        font-weight: bold;
        color: rgba(0, 0, 0, 0.8);
        margin-left: 9px;
        span {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
    // &:before,
    &:after {
      content: "";
      display: block;
      border-width: 8px;
      position: absolute;
      bottom: 100px;
      left: -16px;
      border-style: solid dashed dashed;
      border-color: transparent #ffffff transparent transparent;
      font-size: 0;
      line-height: 0;
      filter: drop-shadow(0px 0px 0px rgba(0, 0, 0, 0.3));
    }
  }
  &:hover {
    .struct-box {
      display: block;
    }
  }
}
</style>
