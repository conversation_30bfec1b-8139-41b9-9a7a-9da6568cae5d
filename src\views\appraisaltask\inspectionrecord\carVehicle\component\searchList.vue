<template>
  <div class="base-search">
    <div class="inline">
      <!-- <ui-label class="fl" label="关键词" :width="55">
      <Input
        v-model="searchData.keyWord"
        class="w150"
        placeholder="请输入设备名称/设备编码"
      ></Input>
    </ui-label> -->
      <ui-label label="设备名称" :width="70" class="inline mr-lg mb10">
        <Input v-model="searchData.deviceName" class="w150" placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label label="设备编码" :width="70" class="inline mr-lg mb10">
        <Input v-model="searchData.deviceId" class="w150" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb10" label="组织机构" :width="70">
        <ApiOrganizationTree
          style="width: auto !important"
          :treeData="treeData"
          :taskObj="taskObj"
          :select-tree="searchData"
          placeholder="请选择组织机构"
        >
        </ApiOrganizationTree>
      </ui-label>
      <ui-label class="inline mr-lg mb10" :label="global.filedEnum.sbdwlx" :width="100">
        <Select
          class="w150"
          v-model="searchData.sbdwlx"
          :clearable="true"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in propertySearchLbdwlx"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!-- 车辆 -->
      <ui-label
        class="inline mb10"
        style=""
        label=" "
        :width="30"
        v-if="[402, 403, 404, 405, 406].includes(currentTree.id)"
      >
        <Select v-model="searchData.column" style="margin-right: 15px; width: 120px" :clearable="true">
          <Option v-for="(sbdwlxItem, bdindex) in searchList" :key="'sbdwlx' + bdindex" :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
        <InputNumber
          class="input-number-list"
          v-model="searchData.miniCount"
          controls-outside
          :min="1"
          @on-blur="blurLimit(searchData.miniCount)"
          placeholder="请填写数量"
        ></InputNumber>
        <span class="ml-sm mr-sm">--</span>
        <InputNumber
          class="input-number-list"
          v-model="searchData.maxCount"
          :min="1"
          @on-blur="blurLimit(searchData.maxCount)"
          controls-outside
          placeholder="请填写数量"
        ></InputNumber>
      </ui-label>
      <!-- <ui-label
        class="fl ml-lg"
        style=""
        label="设备类型"
        :width="70"
        v-if="[407].includes(currentTree.id)"
      >
        <Select class="w150" v-model="searchData.deviceTagCategory" :clearable="true">
          <Option value="2">重点设备 </Option>
          <Option value="1">普通设备 </Option>
        </Select>
      </ui-label> -->
      <ui-label
        class="inline mr-lg mb10"
        style=""
        :label="lable"
        :width="width"
        v-if="![402, 403, 404, 405, 406].includes(currentTree.id)"
      >
        <InputNumber
          class="input-number-list"
          v-model="searchData.miniCount"
          controls-outside
          :min="1"
          @on-blur="blurLimit(searchData.miniCount)"
          placeholder="请填写数量"
        ></InputNumber>
        <span class="ml-sm mr-sm">--</span>
        <InputNumber
          class="input-number-list"
          v-model="searchData.maxCount"
          :min="1"
          @on-blur="blurLimit(searchData.maxCount)"
          controls-outside
          placeholder="请填写数量"
        ></InputNumber>
      </ui-label>
    </div>
    <ui-label :width="0" class="inline search-button" label="">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import user from '@/config/api/user';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    lable: {
      type: String,
      default: '时钟错误图片数量',
    },
    width: {
      type: Number,
      default: 155,
    },
    searchList: {
      type: Array,
      default() {
        return [];
      },
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      searchData: {
        keyWord: '',
        sbdwlx: '',
        miniCount: null,
        maxCount: null,
        orgCode: '',
      },
      cardSearchList: [],
      dictData: {},
    };
  },
  async created() {
    this.reashfalf();
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx',
    }),
  },
  mounted() {
    // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
    // this.searchData.orgCode = orgCode || ''
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    startSearch() {
      if (this.currentTree.id == 407) {
        this.searchData.column = 'clockErrCount';
      } else if (this.currentTree.id == 408 || this.currentTree.id == 409) {
        this.searchData.column = 'uploadTimeOutCount';
      } else if (this.currentTree.id == 410 || this.currentTree.id == 411) {
        this.searchData.column = 'urlNotUseCount';
      }
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx1(this.searchData);
    },
    resetSearchDataMx1() {
      this.reashfalf();
      this.$emit('startSearch', this.searchData);
    },
    reashfalf() {
      this.searchData = {
        keyWord: '',
        sbdwlx: '',
        miniCount: null,
        maxCount: null,
        orgCode: '',
      };
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.searchData.orgCode = orgCode || ''
    },
    blurLimit() {
      if (this.searchData.miniCount) {
        this.searchData.miniCount = Math.round(Number(this.searchData.miniCount));
      }
      if (this.searchData.maxCount) {
        this.searchData.maxCount = Math.round(Number(this.searchData.maxCount));
      }
      this.$forceUpdate();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    taskObj: {
      deep: true,
      handler: function () {
        this.reashfalf();
        this.$nextTick(() => {
          // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
          // this.searchData.orgCode = orgCode || ''
        });
      },
    },
  },
  components: {
    ApiOrganizationTree: require('../../components/api-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
  .ui-label {
    // line-height: 40px;
  }
  .search-button {
    white-space: nowrap;
  }
}
.mt4 {
  margin-top: 4px;
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
/deep/ .w150 {
  width: 150px;
}
.mb10 {
  margin-bottom: 10px;
}
</style>
