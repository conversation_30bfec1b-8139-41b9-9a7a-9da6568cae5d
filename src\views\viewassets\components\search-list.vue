<template>
  <div class="search-list">
    <div class="search-box">
      <ui-label class="inline mr-lg mb-lg" label="关键词">
        <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入设备名称/编码/IP/MAC"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbdwlx">
        <Select
          class="width-md"
          v-model="searchData.sbdwlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}</Option
          >
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" :label="global.filedEnum.sbgnlx">
        <Select
          class="width-md"
          v-model="searchData.sbgnlx"
          clearable
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
        >
          <Option
            v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
            :key="'sbgnlx' + bdindex"
            :value="sbgnlxItem.dataKey"
            >{{ sbgnlxItem.dataValue }}</Option
          >
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-lg" label="检测状态">
        <Select
          class="width-md"
          multiple
          :max-tag-count="1"
          v-model="searchData.checkStatuses"
          placeholder="请选择检测状态"
          clearable
        >
          <Option v-for="(item, index) in global.checkStatusList" :key="index" :value="item.value">{{
            item.label
          }}</Option>
        </Select>
      </ui-label>
      <div class="inline mb-lg">
        <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
        <Button type="default" @click="reset"> 重置 </Button>
      </div>
    </div>
    <div class="data-list mb-lg">
      <span class="mr-sm">异常原因</span>
      <ui-select-tabs
        class="ui-select-tabs"
        :list="errorList"
        @selectInfo="selectInfo"
        ref="uiSelectTabs"
      ></ui-select-tabs>
    </div>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'search-list',
  props: {
    dictData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        keyWord: '',
        sbdwlx: '', // 设备点位类型
        sbgnlx: '', // 设备功能类型
        checkStatuses: [], // 状态
        errorMessageList: [], //  异常原因
      },
      errorList: [],
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
    // await this.initErrorList()
  },
  methods: {
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.resetSearchDataMx(this.searchData, () => {
        this.$emit('startSearch', this.searchData);
      });
      this.$refs.uiSelectTabs.reset();
    },
    async initErrorList() {
      try {
        let res = await this.$http.get(equipmentassets.queryCheckErrorList);

        this.errorList = res.data.data.map((row) => {
          return {
            name: row,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    selectInfo(data) {
      const infoList = data.map((item) => item.name);
      this.searchData.errorMessageList = infoList;
      this.$emit('startSearch', this.searchData);
    },
    resetUiSelectTabs() {
      this.$refs.uiSelectTabs.reset();
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
.search-list {
  width: 100%;
  .search-box {
    width: 100%;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  .align-flex {
    display: flex;
    align-items: center;
  }
  .data-list {
    .align-flex;
    color: var(--color-content);
    font-size: 14px;
    .ui-select-tabs {
      flex: 1;
    }
  }
}
</style>
