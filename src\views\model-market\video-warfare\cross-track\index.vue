<template>
  <div class="track-page-container">
    <div class="top-search">
      <ul>
        <li>
          <span>查询目标：</span
          ><img :src="searchValue?.photoUrl" class="img" />
        </li>
        <li><span>起始点位：</span>{{ searchValue?.deviceName }}</li>
        <li>
          <span>范围选择：</span>
          <Input v-model="range" @on-change="change('range')" />米
        </li>
        <li class="startOrPause">
          <Button type="primary" size="small" @click="isPause = !isPause">{{
            isPause ? "恢复追踪" : "暂停追踪"
          }}</Button>
        </li>
      </ul>
    </div>
    <crossTrackMap ref="map" @inited="initmap"></crossTrackMap>
    <videoComponent
      :deviceInfo="deviceInfo"
      :range="range"
      class="show-av-list"
      ref="videoRefs"
    />
    <alarmComponent :alarmList="alarmList" class="show-pic-list" />
    <div class="alarm-pop" ref="facePopWin">
      <img :src="this.deviceInfo.traitImg" alt="" class="hightIndex" />
    </div>
    <webSocket
      ref="websocket"
      @sendInfo="sendInfo"
      v-if="deviceInfo.idCardNo"
    />
  </div>
</template>
<script>
import { mapMutations } from "vuex";
import videoComponent from "./components/video";
import alarmComponent from "./components/alarmList";
import crossTrackMap from "./components/crossTrackMap";
import { getNearResource } from "@/api/modelMarket";
import webSocket from "@/components/webSocket.vue";

let DISTANCERANGE = [200, 500]; //雷达范围
export default {
  name: "trackPage",
  data() {
    return {
      deviceInfo: {},
      range: 200,
      lon: "",
      lat: "",
      searchValue: null,
      alarmList: [],
      isPause: false,
      mapLoader: null,
    };
  },
  async created() {
    this.setLayoutNoPadding(true);
  },
  destroyed() {
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    async initmap(crossTrackMap) {
      this.crossTrackMap = crossTrackMap;
      this.Map = crossTrackMap.Map;
      this._mapGeometry = crossTrackMap._mapGeometry;
      this.inited = true;
      if (!Toolkits.isEmptyObject(this.deviceInfo)) {
        this.createMapComps(this.deviceInfo);
      }
      this.mapLoader = true;
      if (this.searchValue) this.crossTrackMapHook();
    },
    /**
     * 渲染依赖于地图的相关组件
     */
    createMapComps(point) {
      this.crossTrackMap.addSortMarker([point], true); //创建目标点位
      this.crossTrackMap.renderTrack(this.alarmList); //画轨迹
      this.crossTrackMap.createFaceWindow(point, this.$refs.facePopWin); //创建目标图
      this.getAroundDevice(); //周围设备撒点
      this.crossTrackMap.loadRadar(point, this.range); //添加雷达图
    },
    /**
     * 获取周围设备
     */
    getAroundDevice() {
      let _mapGeometery = new MapPlatForm.Base.MapGeometry(this.Map);
      let geometry = new NPMap.Geometry.Circle(
        new NPMap.Geometry.Point(
          this.deviceInfo.geoPoint.lon,
          this.deviceInfo.geoPoint.lat
        ),
        this.range
      );
      let geometryJson = _mapGeometery.getGGeoJsonByGeometry(geometry);
      let param = {
        geometry: geometryJson,
        excludeDeviceTypes: [3, 4, 5, 6, 7, 8],
      };
      getNearResource(param).then((res) => {
        this.deviceList = res.data;
        //渲染之前先清除周围设备
        this.Map && this.crossTrackMap.removeAllOverlays("roundLapMarkerLayer");
        this.crossTrackMap.addSortMarker(
          this.deviceList,
          false,
          "roundLapMarkerLayer"
        );
      });
    },
    /**
     * 检索条件；即时生效
     */
    change(type) {
      Toolkits.FnByShake(1000, () => {
        switch (type) {
          case "range":
            if (
              this.range < DISTANCERANGE[0] ||
              this.range > DISTANCERANGE[1]
            ) {
              this.$Message.warning("请输入大于200米小于500米的值");
              return;
            }
            this.getAroundDevice();
            this.crossTrackMap.loadRadar(this.deviceInfo, this.range); //添加雷达图
            break;
        }
      });
    },
    receiveMessage() {
      if (window.addEventListener) {
        this.handleCommon();
      }
      window.pageInitFinished = true;
    },
    handleCommon() {
      window.addEventListener("message", (even) => {
        if (Object.prototype.toString.call(even.data) === "[object String]") {
          let data = even.data && JSON.parse(even.data);
          this.searchValue = { ...data };
          if (this.mapLoader) this.crossTrackMapHook();
        }
      });
    },
    crossTrackMapHook() {
      const data = this.searchValue;
      // 当布控传递的点位 大于当前距离是 不渲染该点位
      let distance = this.crossTrackMap.isDistance(
        data.geoPoint.lon,
        data.geoPoint.lat
      );
      if (distance > this.range) {
        return;
      }
      this.deviceInfo = {
        ...data,
        longitude: data.geoPoint.lon,
        latitude: data.geoPoint.lat,
        icon: {
          normal: "CAMERA_QIANGJI_HOVER",
          hover: "CAMERA_QIANGJI_HOVER",
        },
      };
      this.alarmList.unshift(this.deviceInfo);
      if (this.inited) {
        this.createMapComps(this.deviceInfo);
      }
    },
    async sendInfo(row) {
      if (this.isPause) return;
      console.log(row, "布控消息");
      var rows = JSON.parse(row);
      // 当布控传递的点位 大于当前距离是 不渲染该点位
      if (this.crossTrackMap) {
        let distance = this.crossTrackMap.isDistance(
          rows.geoPoint.lon,
          rows.geoPoint.lat
        );
        if (distance > this.range) {
          console.log("该点位大于搜索范围距离", rows);
          return;
        }
      }

      if (
        rows.compareType == 1 &&
        !this.alarmList.includes(rows.alarmId) &&
        this.deviceInfo.idCardNo == rows.idCardNo
      ) {
        if (!rows.geoPoint.lon || !rows.geoPoint.lat) {
          return; //过滤坐标
        }
        this.deviceInfo = {
          ...rows,
          longitude: rows.geoPoint.lon,
          latitude: rows.geoPoint.lat,
          icon: {
            normal: "CAMERA_QIANGJI_HOVER",
            hover: "CAMERA_QIANGJI_HOVER",
          },
        };
        console.log("==========", this.deviceInfo);
        this.alarmList.unshift({ ...this.deviceInfo }); //添加报警图片;
        this.createMapComps({ ...this.deviceInfo }); //添加点位
      }
    },
  },
  mounted() {
    this.receiveMessage();
  },
  components: {
    videoComponent,
    alarmComponent,
    crossTrackMap,
    webSocket,
  },
};
</script>
<style lang="less">
.track-page-container {
  position: relative;
  height: 100%;
  width: 100%;
  .cross-track-map {
    height: 100%;
    width: 100%;
  }
  .xui-input .xui-input-inner {
    color: #fff;
    background: none;
  }
  .top-search {
    background: rgb(72, 72, 71);
    color: #fff;
    padding: 5px 40px;
    height: 50px;
    overflow: hidden;
    img {
      height: 30px;
    }
    li {
      display: inline-flex;
      /* width: 30%; */
      align-items: center;
      justify-content: center;
      padding: 5px 10px;
      margin-right: 40px;
      white-space: nowrap;
    }
    .ivu-input-wrapper {
      margin-right: 5px;
    }
    .ivu-input {
      background: transparent;
      color: #fff;
    }
    .startOrPause {
      float: right;
    }
  }
  .show-av-list {
    position: absolute;
    right: 0px;
    top: 60px;
    width: 500px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    overflow: hidden;
  }
  .show-pic-list {
    position: absolute;
    left: 0px;
    top: 50px;
    bottom: 0px;
    width: 220px;
    background: rgba(128, 128, 128, 0.3);
    padding: 10px;
  }
  .alarm-pop {
    position: relative;
    z-index: -1;
    visibility: hidden;
    height: 100%;
    width: 100%;
    overflow: hidden;
    border: 1px solid #2c86f8;
    .hightIndex {
      z-index: 2;
    }
    img,
    .canvas-container {
      position: absolute;
      height: 200px;
      width: 200px;
      left: 0px;
      right: 0px;
      top: 0px;
      bottom: 0px;
    }
  }
}
</style>
