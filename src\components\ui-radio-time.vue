<template>
  <div class="inline ui-radio">
    <ui-radio-group
      class="inline"
      v-model="timeRadio.value"
      :label="timeRadio.title"
      :radio-list="timeRadio.timeList"
      @change="timeSelect"
    >
    </ui-radio-group>
    <div class="inline ml-sm" v-if="timeRadio.timePickerShow">
      <DatePicker
        v-model="timeRadio.startTime"
        :format="timeRadio.format || 'yyyy-MM-dd HH:mm:ss'"
        :type="timeRadio.comType || 'datetime'"
        placeholder="开始时间"
        :options="startTimeOption"
        @on-change="startChangeTime"
      >
      </DatePicker>
      <span class="mr-xs ml-xs font-blue">--</span>
      <DatePicker
        v-model="timeRadio.endTime"
        :format="timeRadio.format || 'yyyy-MM-dd HH:mm:ss'"
        :type="timeRadio.comType || 'datetime'"
        placeholder="结束时间"
        :options="endTimeOption"
        @on-change="endChangeTime"
      >
      </DatePicker>
    </div>
  </div>
</template>
<style lang="less" scoped>
.ui-radio {
  @{_deep}.ivu-radio-group-button {
    .ivu-radio-wrapper-checked {
      border: none;
      box-shadow: none;
    }
    .ivu-radio-wrapper:first-child {
      border: none;
    }
  }
}
.close {
  vertical-align: middle;
  color: #5182a7;
  &:hover {
    cursor: pointer;
    color: #5bd1fe;
  }
}
</style>
<script>
export default {
  data() {
    return {
      startTime: {
        disabledDate: (date) => {
          let endTime = new Date(this.timeRadio.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTime: {
        disabledDate: (date) => {
          let startTime = new Date(this.timeRadio.startTime);
          if (startTime) {
            return date < startTime.getTime() - 86400000;
          }
          return false;
        },
      },
    };
  },
  created() {
    this.timeGet();
  },
  mounted() {},
  methods: {
    timeGet() {
      this.$util.common.quickDate(this.timeRadio);
    },
    // 点击按钮改变时间抛出的事件
    timeSelect() {
      this.timeGet();
      this.$emit('selectTime', this.timeRadio);
    },
    startChangeTime(time) {
      this.timeRadio.startTime = time;
      this.$emit('selectTime', this.timeRadio);
    },
    endChangeTime(time) {
      this.timeRadio.endTime = time;
      this.$emit('selectTime', this.timeRadio);
    },
  },
  watch: {},
  computed: {
    startTimeOption() {
      if (this.setStartTimeOption) {
        return this.setStartTimeOption;
      } else {
        return this.startTime;
      }
    },
    endTimeOption() {
      if (this.setEndTimeOption) {
        return this.setEndTimeOption;
      } else {
        return this.endTime;
      }
    },
  },
  props: {
    setStartTimeOption: {},
    setEndTimeOption: {},
    /*
      timeRadio.title:名称 默认不显示
      timeRadio.value: 选中的值
      timeRadio.timeList: 时间按钮列表
      timeRadio.startTime: 开始时间
      timeRadio.endTime: 结束时间
      timeRadio.timePickerShow: 自定义选择显示
      timeRadio.comType: 'datetime' 时间组件类型
      timeRadio.format: 'yyyy-MM-dd HH:mm:ss' 时间格式 
    */
    timeRadio: {
      type: Object,
      required: true,
    },
  },
  components: {
    UiRadioGroup: require('@/components/ui-radio-group.vue').default,
  },
};
</script>
