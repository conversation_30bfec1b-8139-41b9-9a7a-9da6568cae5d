<template>
  <!-- 数量达标率 （区） -->
  <div class="county-quantity-reach auto-fill">
    <div class="content">
      <div class="container auto-fill">
        <div class="abnormal-title">
          <div class="fl">
            <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
            <span class="f-16 color-filter ml-sm">检测结果统计</span>
          </div>
          <div class="export fr">
            <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs f-14">导出</span>
            </Button>
          </div>
        </div>
        <div class="list">
          <ui-table :tableColumns="tableColumns" :table-data="tableData" :loading="loading">
            <template #civilName="{ row }">
              <span class="civil" :title="row.civilName">{{ row.civilName || '--' }}</span>
            </template>
            <template #regionTypeName="{ row }">
              <span>{{ row.regionTypeName || '--' }}</span>
            </template>
            <template #sxjCount="{ row }">
              <span>{{ row.sxjCount }}</span>
            </template>
            <template #sxjReportCount="{ row }">
              <span>{{ row.sxjReportCount === null ? '--' : row.sxjReportCount }}</span>
            </template>

            <template #sxjStandardsValue="{ row }">
              <span
                >{{
                  row.sxjStandardsValue === null ? '--' : $util.common.financial(row.sxjStandardsValue * 100)
                }}%</span
              >
            </template>

            <template #rlkkCount="{ row }">
              <span>{{ row.rlkkCount === null ? '--' : row.rlkkCount }}</span>
            </template>
            <template #rlkkReportCount="{ row }">
              <span>{{ row.rlkkReportCount === null ? '--' : row.rlkkReportCount }}</span>
            </template>

            <template #rlkkStandardsValue="{ row }">
              <span
                >{{
                  row.rlkkStandardsValue === null ? '--' : $util.common.financial(row.rlkkStandardsValue * 100)
                }}%</span
              >
            </template>
            <template #clkkCount="{ row }">
              <span>{{ row.clkkCount === null ? '--' : row.clkkCount }}</span>
            </template>

            <template #clkkReportCount="{ row }">
              <span>{{ row.clkkReportCount === null ? '--' : row.clkkReportCount }}</span>
            </template>

            <template #clkkStandardsValue="{ row }">
              <span
                >{{
                  row.clkkStandardsValue === null ? '--' : $util.common.financial(row.clkkStandardsValue * 100)
                }}%</span
              >
            </template>
          </ui-table>
        </div>
        <table class="table_content">
          <tr class="con_one">
            <td class="td_one">
              <div>
                <span>分项达标率</span>
              </div>
            </td>
            <td class="td_two">
              <div>
                <span class="font-blue">{{
                  !indexList.quantityStandardSxjFormula ? '--' : indexList.quantityStandardSxjFormula
                }}</span>
              </div>
            </td>
            <td class="td_three">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardRlkkFormula === null ? '--' : indexList.quantityStandardRlkkFormula
                }}</span>
              </div>
            </td>
            <td class="td_four">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardClkkFormula === null ? '--' : indexList.quantityStandardClkkFormula
                }}</span>
              </div>
            </td>
          </tr>
          <tr class="con_one">
            <td class="td_one b_t">
              <div>
                <span>数量达标率</span>
              </div>
            </td>
            <td class="b_t td_text">
              <div>
                <span class="font-blue">{{
                  indexList.quantityStandardFormula === null ? '--' : indexList.quantityStandardFormula
                }}</span>
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
.county-quantity-reach {
  width: 100%;
  height: 250px;
  .content {
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
    position: relative;
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .list {
      height: 260px; /*no*/
      @{_deep}.ivu-table-wrapper {
        height: 260px !important; /*no*/
      }
      border-right: 1px solid var(--border-color) !important;
      border-left: 1px solid var(--border-color) !important;
      @{_deep}.ivu-table-header {
        tr {
          border-top: 1px solid var(--border-color) !important;
          th {
            border-left: 1px solid var(--border-color) !important;
            span {
              color: #a9bed9 !important;
            }
          }
          &:nth-child(1) {
            th {
              &:nth-child(1) {
                border-left: none !important;
              }
            }
          }
        }
      }

      @{_deep}.ivu-table-tbody {
        tr {
          border-top: 1px solid var(--border-color) !important;
          // border-right: 1px solid var(--border-color) !important;
          td {
            border-left: 1px solid var(--border-color) !important;
            border-bottom: 1px solid var(--border-color) !important;
            &:nth-child(1) {
              border-left: none !important;
            }
          }
        }
      }
      .ui-table {
        position: relative;
      }
    }

    @media screen and (max-width: 1366px) {
      .list {
        height: 180px; /*no*/
        @{_deep}.ivu-table-wrapper {
          height: 160px !important; /*no*/
        }
      }
      .table_content {
        height: 100px; /*no*/
        width: 100%; /*no*/
        border-bottom: 1px solid var(--border-color);

        .con_one {
          height: 50px; /*no*/
          width: 100%; /*no*/
          display: flex;
          border-top: 1px solid var(--border-color);
          border-right: 1px solid var(--border-color);

          td {
            text-align: center;
            line-height: 50px; /*no*/
            border-left: 1px solid var(--border-color);
            div {
              height: 100%;
              display: inline-block;
              width: 100%;
            }
          }

          .con_color {
            color: #0d477d;
          }
          .quantity_color {
            color: #bc3c19;
          }

          .td_one {
            width: 200px; /*no*/
            background-color: #092955;
            span {
              color: #a9bed9 !important;
              font-size: 14px;
            }
            // flex: 1;
          }
          .td_text {
            flex: 1;
            span {
              color: #19c176 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }

          .td_two {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
          .td_three {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
          .td_four {
            flex: 1;
            span {
              color: #19d5f6 !important;
              font-weight: bold;
              font-size: 18px;
            }
          }
        }
      }
    }
    .table_content {
      height: 100px;
      width: 100%;
      border-bottom: 1px solid var(--border-color);

      .con_one {
        height: 50px;
        width: 100%;
        display: flex;
        border-top: 1px solid var(--border-color);
        border-right: 1px solid var(--border-color);

        td {
          text-align: center;
          line-height: 50px;
          border-left: 1px solid var(--border-color);
          div {
            height: 100%;
            display: inline-block;
            width: 100%;
          }
        }

        .con_color {
          color: #0d477d;
        }
        .quantity_color {
          color: #bc3c19;
        }

        .td_one {
          width: 200px; /*no*/
          background-color: #092955;
          span {
            color: #a9bed9 !important;
            font-size: 14px;
          }
          // flex: 1;
        }
        .td_text {
          flex: 1;
          span {
            color: #19c176 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }

        .td_two {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
        .td_three {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
        .td_four {
          flex: 1;
          span {
            color: #19d5f6 !important;
            font-weight: bold;
            font-size: 18px;
          }
        }
      }
    }
  }
}
.civil {
  width: 60px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'county-quantity-reach',
  data() {
    return {
      tableColumns: [
        {
          title: '行政区域',
          slot: 'civilName',
          align: 'center',
          width: 100,
        },
        {
          title: '区域类型',
          key: 'civilTypeName',
          // slot: "regionTypeName",
          align: 'center',
          width: 100,
        },

        {
          title: '视频监控',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'sxjCount',
              slot: 'sxjCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'sxjReportCount',
              slot: 'sxjReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'sxjStandardsValue',
              slot: 'sxjStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
        {
          title: '人脸卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'rlkkCount',
              slot: 'rlkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'rlkkReportCount',
              slot: 'rlkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'rlkkStandardsValue',
              slot: 'rlkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },

        {
          title: '车辆卡口',
          align: 'center',
          children: [
            {
              title: '达标数量',
              key: 'clkkCount',
              slot: 'clkkCount',
              align: 'center',
              minWidth: 80,
            },
            {
              title: '实际建设',
              key: 'clkkReportCount',
              slot: 'clkkReportCount',
              align: 'center',
              minWidth: 80,
            },

            {
              title: '达标率',
              key: 'clkkStandardsValue',
              slot: 'clkkStandardsValue',
              align: 'center',
              minWidth: 80,
            },
          ],
        },
      ],
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '7.7rem',
      },
      visible: true,
      loading: false,
      detailInfo: {},
      detailList: [],
      modalId: { title: '测评结果' },
      indexList: {},
      exportLoading: false,
      paramsList: {},
    };
  },
  mounted() {},

  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    async getEvaluationRecord() {
      try {
        this.loading = true;
        this.tableData = [];
        let data = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        this.indexList = res.data.data || {};
        // this.tableData = this.indexList.quantityStandardRecordDetailVos
        this.tableData = this.$util.common.arrayToJson(
          this.indexList.quantityStandardRecordDetailVos,
          'selfCode',
          'parentCode',
        );
        this.tableData.forEach((item, index) => {
          this.$set(item, '_showChildren', true);

          if (index % 2 === 0) {
            this.$set(item, 'rowClass', 'even1');
          } else {
            this.$set(item, 'rowClass', 'odd1');
          }
        });
        this.sxjCount = this.indexList.quantityStandardSxjCount - this.indexList.quantityStandardSxjReportCount || '--';
        this.rlkkCount =
          this.indexList.quantityStandardRlkkCount - this.indexList.quantityStandardRlkkReportCount || '--';
        this.clkkCount =
          this.indexList.quantityStandardClkkCount - this.indexList.quantityStandardClkkReportCount || '--';
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getEvaluationRecord();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
