<template>
  <div style="width: 5rem; height: 0.78rem" id="echart_bar"></div>
</template>
<script>
export default {
  name: 'tasktracking',
  props: ['ListNum'],
  data() {
    return {};
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let myChart = this.$echarts.init(document.getElementById('echart_bar'));
      var option = {
        grid: {
          left: '6%',
          right: '7%',
          bottom: '10%',
          top: '30%',
          containLabel: true,
        },
        xAxis: {
          // data: dataAxis,
          name: '治理项',
          nameTextStyle: {
            //关键代码
            padding: [30, 30, 30, -5],
          },
          type: 'category',
          data: ['实时视频流不通畅', '历史视频流不通畅', 'OSD字幕不合格'],
          axisLabel: {
            color: '#fff',
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            //x轴刻度线
            show: true,
            lineStyle: {
              color: '#0375B4',
            },
          },
        },
        yAxis: {
          name: '单位：万',
          axisLabel: {
            color: '#fff',
          },
          splitLine: {
            //网格线
            show: false,
          },
          axisLine: {
            //y轴刻度线
            show: true,
            lineStyle: {
              color: '#0375B4',
            },
          },
        },
        dataZoom: [
          {
            type: 'inside',
          },
        ],
        series: [
          {
            type: 'bar',
            barWidth: 30,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#83bff6' },
                { offset: 0.5, color: '#188df0' },
                { offset: 1, color: '#188df0' },
              ]),
            },
            data: [
              this.ListNum[1].existingExceptionCount,
              this.ListNum[2].existingExceptionCount,
              this.ListNum[3].existingExceptionCount,
            ],
            // data: data,
          },
        ],
      };
      myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
