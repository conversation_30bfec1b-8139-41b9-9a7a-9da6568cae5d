<template>
  <div class="ui-label" :class="required ? 'required' : ''">
    <label ref="label" class="label fl" :class="{ 'mr-sm': !width }">
      <span class="label-text">{{ label }}</span>
    </label>
    <slot></slot>
  </div>
</template>
<style lang="less" scoped>
.ui-label {
  overflow: visible;
  line-height: 32px;
  .label {
    display: inline-block;
    font-size: 14px;
    white-space: pre;
    vertical-align: middle;
    color: var(--color-label);
    width: fit-content;
  }
}
.required {
  overflow: visible;
  .label {
    .label-text {
      position: relative;
      &:before {
        content: '*';
        left: -10px;
        display: inline-block;
        position: absolute;
        margin-right: 0.020833rem;
        font-size: 0.072917rem;
        color: #ed4014;
        font-family: SimSun;
      }
    }
  }
}
</style>
<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {
    if (this.width) {
      if (document.body.clientWidth <= 1366) {
        this.$refs.label.style.width = `${this.width}px`;
      } else {
        this.$refs.label.style.width = `${this.width / 192}rem`;
      }
    }
    this.$refs.label.style.textAlign = this.align;
  },
  methods: {},
  watch: {
    width() {
      if (this.width) {
        if (document.body.clientWidth <= 1366) {
          this.$refs.label.style.width = `${this.width}px`;
        } else {
          this.$refs.label.style.width = `${this.width / 192}rem`;
        }
      }
      this.$refs.label.style.textAlign = this.align;
    },
  },
  computed: {},
  props: {
    label: {
      required: true,
    },
    required: {
      type: Boolean,
      default: false,
    },
    width: {
      // default: 80,
    },
    align: {
      default: 'left',
    },
  },
  components: {},
};
</script>
