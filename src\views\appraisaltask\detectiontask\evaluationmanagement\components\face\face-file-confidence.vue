<template>
  <!-- 人像档案置信率弹框 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <!--  v-if="!!trackList && trackList.detectionAmount != 0" -->
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="已聚档实有人口"> </line-title>
        <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
        <TableCard ref="infoCard" :loadData="loadDataCard" :msg="msg" :cardInfo="cardInfo">
          <!-- <div class="search-wrapper" slot="search">
            <ui-label class="fl" label="姓名">
              <Input class="input-width" v-model="msg.name" placeholder="请输入姓名"></Input>
            </ui-label>
            <ui-label class="fl ml-lg" label="证件号">
              <Input class="input-width" placeholder="请输入证件号" v-model="msg.idCard"></Input>
            </ui-label>
            <ui-label :width="30" class="fl" label=" ">
              <Button type="primary" class="mr-sm" @click="search">
                查询
              </Button>
              <Button type="default" class="mr-sm" @click="reast">
                重置
              </Button>
            </ui-label>
          </div> -->
          <!-- 卡片 -->
          <template #card="{ row }">
            <UiGatherCard class="card" :list="row" :personTypeList="personTypeList" :cardInfo="cardInfo"></UiGatherCard>
          </template>
        </TableCard>
      </div>
    </div>
    <!-- 图像模式 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }

    .card {
      width: calc(calc(100% - 40px) / 4);
      margin: 0 5px 10px;
    }
    .hearder-title {
      color: #fff;
      margin-top: 10px;
      font-size: 14px;
      .mr20 {
        margin-right: 20px;
      }
      .blue {
        color: #19c176;
      }
    }
    .ui-images {
      width: 56px;
      height: 56px;
      margin-bottom: 9px;
      .ui-image {
        min-height: 56px !important;
        /deep/ .ivu-spin-text {
          img {
            width: 56px;
            height: 56px;
            margin-top: 5px;
          }
        }
      }
    }
  }
  /deep/.scheme_title {
    margin-bottom: 10px;
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '700px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['实名聚档人口数量', '未实名聚档人口数量'],
        showData: [
          { name: '实名聚档人口数量', value: 0 },
          { name: '未实名聚档人口数量', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '人像档案置信率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNumber: 1,
        pageNum: 1,
        pageSize: 10,
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},

      bigPictureShow: false,
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '抓拍总数: ', value: 'urlNum' },
      ],
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            governanceevaluation.personArchives,
            Object.assign(
              parameter,
              {
                resultId: this.$parent.row.resultId,
                indexId: this.$parent.row.indexId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      msg: {
        pageNumber: 1,
        pageSize: 10,
        name: '',
        idCard: '',
      },
    };
  },
  async mounted() {
    this.msg.resultId = this.$parent.row.resultId;
    this.msg.indexId = this.$parent.row.indexId;
    this.msg.name = this.name;
    this.msg.idCard = this.idCard;
    // this.$refs.infoCard.info(true)
    // if (this.personTypeList.length == 0) await this.getPersonTypeList()
    this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    handleCheckStatus(row) {
      const flag = {
        1: '可用',
        2: '不可用',
      };
      return flag[row];
    },
    ...mapActions({
      getPersonTypeList: 'algorithm/getPersonTypeList',
    }),
    async init() {
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    // search() {
    //   // this.searchData.name = this.name
    //   // this.searchData.idCard = this.idCard
    //   this.$refs.infoCard.info()
    // },
    // reast() {
    //   this.msg.pageNumber = 1
    //   this.msg.name = ''
    //   this.msg.idCard = ''
    //   // this.searchData = {}
    //   this.$refs.infoCard.info()
    // },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.post(governanceevaluation.personArchivesDetailsCount, {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
          orgCode: 320000,
          // orgCode: this.$parent.row.indexId,
        });
        this.trackList = res.data;
        this.moduleData.rateValue = this.$parent.row.resultValue || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '实名聚档人口数量') {
          item.value = this.trackList.confidenceRatCount;
        } else {
          if (this.trackList.count - this.trackList.confidenceRatCount < 0) {
            item.value = 0;
          } else {
            item.value = this.trackList.count - this.trackList.confidenceRatCount;
          }
        }
      });
      this.zdryChartObj.count = this.trackList.count;
      let formatData = {
        seriesName: '实有人口数量',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    startSearch(searchData) {
      this.searchData = searchData;
      this.$refs.infoCard.info(true);
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/personTypeList',
    }),
  },
  watch: {
    // '$parent.taskObj': {
    //   deep: true,
    //   handler: function(val) {
    //     this.info()
    //   },
    // },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('../emphasis/components/tableCard_str.vue').default,
    UiGatherCard: require('../emphasis/components/ui-gather-card.vue').default,
  },
};
</script>
