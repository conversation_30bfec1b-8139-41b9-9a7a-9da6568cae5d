<template>
  <div class="data-warehouse">
    <p>1、相同数据（所有字段一致）：不再接入</p>
    <p>2、新数据（主键不一致）：直接接入</p>
    <p>
      3、变更数据（主键一致，其他数据有变化）：
      <RadioGroup v-model="type" @on-change="handleChange">
        <Radio v-for="(item, index) in dicts" :key="index" :label="item.value" :disabled="!isEdit">{{
          item.name
        }}</Radio>
        <!-- <Radio label="2" disabled>舍弃</Radio>
        <Radio label="3">同时接入</Radio> -->
      </RadioGroup>
    </p>
  </div>
</template>
<script>
export default {
  props: {
    warehousData: {
      type: Object,
      default: () => {},
    },
    isEdit: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dicts: [
        { name: '覆盖', value: '1' },
        { name: '舍弃', value: '2' },
        // { name: '同时接入', value: '3' },
      ],
      type: '',
    };
  },
  mounted() {
    this.type = this.warehousData.dataHandle;
  },
  methods: {
    handleChange(val) {
      this.$emit('selectChange', val);
    },
  },
  watch: {
    'warehousData.dataHandle'() {
      this.type = this.warehousData.dataHandle;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.data-warehouse {
  padding: 18px 2px;
  p {
    font-size: 14px;
    color: var(--color-input);
  }
  p:not(:last-child) {
    margin-bottom: 20px;
  }
}
@{_deep} .ivu-radio {
  &-wrapper {
    font-size: 14px;
    margin-right: 30px;
  }
  &-inner {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    background: transparent;
    border: 1px solid var(--color-primary);
    &:after {
      width: 8px;
      height: 8px;
      background: var(--color-primary);
    }
  }
}
@{_deep} .ivu-radio-wrapper .ivu-radio.ivu-radio-checked.ivu-radio-disabled .ivu-radio-inner {
  border-color: var(--color-primary) !important;
  &:after {
    background-color: var(--color-primary) !important;
  }
}
</style>
