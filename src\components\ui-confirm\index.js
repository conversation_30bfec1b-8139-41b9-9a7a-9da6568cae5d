/**
 *
 * @description 代替 this.$Modal.confirm(config)
 * @example
 * this.$UiConfirm({content: "", title: ""}).then(res=>{
        console.log(res);
    }).catch(res=>{
        console.log(res);
    })
 * */
import UIConfirm from './index.vue';

let confirm = {};
let $vm = null;
confirm.install = function (Vue) {
  const confirmConstructor = Vue.extend(UIConfirm);
  $vm = new confirmConstructor({
    el: document.createElement('div'),
  });

  Vue.prototype.$UiConfirm = ({
    content = '您确定要删除吗?',
    title = '提示',
    hasLoading = false,
    okText = '确 定',
    render,
  } = {}) => {
    return new Promise((resolve, reject) => {
      $vm.visible = true;
      $vm.content = content;
      $vm.title = title;
      $vm.renderDom = render;
      $vm.okText = okText;
      document.body.appendChild($vm.$el);
      $vm.callback = (visible) => {
        if (!hasLoading) {
          $vm.visible = false;
          $vm.$el.parentNode.removeChild($vm.$el);
        }
        if (visible) {
          resolve($vm);
        } else {
          reject(visible);
          $vm.loading = false;
        }
      };
    });
  };
};

export default confirm;
