<template>
  <Modal
    v-model="visible"
    title="数据预览"
    class="add-resource"
    :width="1400"
    @on-cancel="close"
  >
    <Table
      ref="tableRef"
      :columns="columns"
      :data="resourceList"
      :loading="loading"
      height="540"
    />
    <div slot="footer"></div>
  </Modal>
</template>

<script>
import { viewDataApi } from "@/api/dataGovernance";
export default {
  name: "ViewDataModal",
  data() {
    return {
      visible: false,
      loading: false,
      resourceList: [],
    };
  },
  computed: {
    columns() {
      let arr = [];
      this.resourceList.forEach((item) => {
        let keys = arr.map((e) => e.key);
        for (let key in item) {
          if (!keys.includes(key)) {
            arr.push({
              title: key,
              key,
              width: 180,
              ellipsis: true,
              tooltip: true,
            });
          }
        }
      });
      return arr;
    },
  },
  methods: {
    /**
     * @description: 展示弹框
     * @param {number} resourceId 资源id
     */
    show(resourceId) {
      this.visible = true;
      this.loading = true;
      viewDataApi({ resourceId })
        .then((res) => {
          this.resourceList = res.data || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    close() {
      this.visible = false;
      this.$refs.tableRef.$el.querySelector(".ivu-table-body").scrollLeft = 0;
      this.$refs.tableRef.$el.querySelector(".ivu-table-body").scrollTop = 0;
    },
  },
};
</script>