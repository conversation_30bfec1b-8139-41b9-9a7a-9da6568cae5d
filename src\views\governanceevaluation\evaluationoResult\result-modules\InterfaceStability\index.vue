<!-- 填报准确率 -->
<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
    </result-title>
    <component :is="componentName" v-bind="handleProps()" @viewDetail="viewDetail"> </component>
    <result-detail v-model="resultDetailVisible"></result-detail>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'InterfaceStability',
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalResult',
      iconList: [],
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
      currentRow: null,
      resultDetailVisible: false,
      formItemData: [
        {
          type: 'select',
          key: 'qualified',
          label: '检测结果',
          placeholder: '请选择检测结果',
          options: [
            {
              value: '1',
              label: '合格',
            },
            {
              value: '2',
              label: '不合格',
            },
          ],
        },
      ],
      formData: {
        qualified: '',
      },
    };
  },
  created() {
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    // 动态组件 - 动态传参
    handleProps() {
      // 检测明细组件需要的参数 review-particular
      let props = {
        activeIndexItem: this.activeIndexItem,
        formItemData: this.formItemData,
        formData: this.formData,
      };
      // const stragetyObject = {
      //   'ReviewParticular': () => {

      //   },
      //   'StatisticalList': () => {
      //   },
      //   'StatisticalEcharts': () => {
      //   },
      // }
      // stragetyObject[this.componentName]()
      return props;
    },
    viewDetail(row) {
      this.currentRow = row;
      this.resultDetailVisible = true;
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
    }),
  },
  watch: {},
  components: {
    ResultDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/InterfaceStability/components/result-detail.vue')
        .default,
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    ReviewParticular: require('./review-particular.vue').default,
    StatisticalEcharts: require('./statistical-echarts').default,
    StatisticalResult: require('../../common-pages/statistical-results/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
}
</style>
