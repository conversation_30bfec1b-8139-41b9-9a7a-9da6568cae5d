import request from '@/libs/request'
import { holographicArchives } from './Microservice'
// 实名档案
// 人员基本信息
export function getPersonBaseInfo (data) {
  return request({
    url: holographicArchives + '/person/personBaseInfo',
    method: 'POST',
    data: data
  })
}
// 卡片查询列表
export function getPersonPageList (data) {
  return request({
    url: holographicArchives + '/person/personPageList',
    method: 'POST',
    data: data
  })
}
// 人员概览
//浏览次数
export function queryViews (data) {
  return request({
    url: holographicArchives + '/person/queryViews',
    method: 'GET',
  })
}
//添加浏览次数
export function addViews (data) {
  return request({
    url: holographicArchives + '/person/addViews',
    method: 'POST',
    data: data
  })
}
//基本信息
export function personBaseInfo (data) {
  return request({
    url: holographicArchives + '/person/personBaseInfo',
    method: 'POST',
    data: data
  })
}
//驾乘车辆
export function drivingVehicle (data) {
  return request({
    url: holographicArchives + '/person/drivingVehicle',
    method: 'POST',
    data: data
  })
}
// 名下车辆
export function ownedVehicles (id) {
  return request({
    url: holographicArchives + '/person/ownedVehicles?archiveNo=' + id,
    method: 'GET',

  })
}
//抓拍记录
export function getPortraitCapture (data) {
  return request({
    url: holographicArchives + '/person/getPortraitCapture',
    method: 'POST',
    data: data
  })
}

//活动轨迹
export function movementTtrackingStatistics (data) {
  return request({
    url: holographicArchives + '/person/movementTtrackingStatistics',
    method: 'POST',
    data
  })
}
//行为规律-时间段
export function behavioralRulesStatics (data) {
  return request({
    url: holographicArchives + '/person/behavioralRulesStatics',
    method: 'POST',
    data: data
  })
}
//关系卡片
export function relationshipCard (data) {
  return request({
    url: holographicArchives + '/person/relationshipCard',
    method: 'GET',
    params: data
  })
}


//视频档关系卡片
export function relationshipCardVideo (data) {
  return request({
    url: holographicArchives + '/videoPerson/relationshipCardVideo',
    method: 'GET',
    params: data
  })
}


// 推荐信息
export function getRecommendedInformationList (data) {
  return request({
    url: holographicArchives + '/person/getRecommendedInfo',
    method: 'POST',
    data: data
  })
}
// 名下资产
export function getUnderNameList (id) {
  return request({
    url: holographicArchives + '/person/ownedAssets?archiveNo=' + id,
    method: 'GET'
  })
}
// 人像抓拍
export function getPortraitCaptureList (data) {
  return request({
    url: holographicArchives + '/person/getPortraitCapture',
    method: 'POST',
    data: data
  })
}
// 人车同拍
export function getPeopleCarCaptureList (data) {
  return request({
    url: holographicArchives + '/person/peopleAndVehicleCapture',
    method: 'POST',
    data: data
  })
}
// 最新位置
export function getLatestLocationList (data) {
  return request({
    url: holographicArchives + '/person/latestLocation',
    method: 'POST',
    data: data
  })
}
// 常去地
export function getFrequentedList (data) {
  return request({
    url: holographicArchives + '/person/frequented',
    method: 'POST',
    data: data
  })
}
// 行为规律-活动时间段
export function getBehavioralRulesStatics (data) {
  return request({
    url: holographicArchives + '/person/behavioralRulesStatics',
    method: 'POST',
    data: data
  })
}
// 行为规律-活动次数
export function getActivitiesNumStatics (data) {
  return request({
    url: holographicArchives + '/person/activitiesNumStatics',
    method: 'POST',
    data: data
  })
}
// 涉案信息
export function getCasePageList (data) {
  return request({
    url: holographicArchives + '/person/casePageList',
    method: 'POST',
    data: data
  })
}
// 违法违章
export function getIllegalPageList (data) {
  return request({
    url: holographicArchives + '/person/illegalPageList',
    method: 'POST',
    data: data
  })
}
// 关系统计
export function getRelationshipStatistics (data) {
  return request({
    url: holographicArchives + '/person/relationshipStatistics',
    method: 'POST',
    data: data
  })
}
// 人人同行
export function getPeopleTogether (data) {
  return request({
    url: holographicArchives + '/person/peopleTogether',
    method: 'POST',
    data: data
  })
}
// 乘车同行
export function getByCarTogether (data) {
  return request({
    url: holographicArchives + '/person/byCarTogether',
    method: 'POST',
    data: data
  })
}
// 户口变动记录
export function getAccountChangePageList (data) {
  return request({
    url: holographicArchives + '/person/accountChangePageList',
    method: 'POST',
    data: data
  })
}
// 出入境记录
export function getImmigrationRecordsPageList (data) {
  return request({
    url: holographicArchives + '/person/immigrationRecordsPageList',
    method: 'POST',
    data: data
  })
}
// 工作经历
export function getWorkExperiencePageList (data) {
  return request({
    url: holographicArchives + '/person/workExperiencePageList',
    method: 'POST',
    data: data
  })
}

// 编辑人员标签
export function motifyRealNameLableIds (data) {
  return request({
    url: holographicArchives + '/person/motifyRealNameLableIds',
    method: 'POST',
    data
  })
}