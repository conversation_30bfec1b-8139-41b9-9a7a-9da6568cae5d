<template>
  <div>
    <!-- 搜周边： -->
    <!-- <template v-for="e in peripheryList"> -->
      <!-- <span class="periphery-item" :key="e" @click="searchAroundHandle(e)">{{ e }}</span> -->
    <!-- </template> -->
    <div class="pointSwitch">
      <span @click="$emit('preDetial')">
        <Icon type="md-arrow-dropleft" :size="18" />
      </span>
      <span @click="$emit('nextDetail')">
        <Icon type="md-arrow-dropright" :size="18" />
      </span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      peripheryList: ['摄像机', 'RFID设备', 'Wi-Fi设备', '电围设备', '位置']
    }
  },
  methods: {
    searchAroundHandle(e) {
      console.log(e)
    }
  }
}
</script>

<style lang="less" scoped>
.periphery-item {
    color: #2c86f8;
    margin-right: 20px;
    cursor: pointer;
}
.pointSwitch {
    float: right;
    display: flex;
    align-items: center;
    // height: 55px;
    justify-content: end;
    margin-top: 12px;
    > span {
        // display: inline-block;
        width: 24px;
        height: 24px;
        background: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 5px;
    }
    > span:hover {
        color: #2c86f8;
        border: 1px solid #2c86f8;
    }
}
</style>
