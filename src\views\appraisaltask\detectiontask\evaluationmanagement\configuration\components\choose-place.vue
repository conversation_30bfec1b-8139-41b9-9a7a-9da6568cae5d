<template>
  <div class="custom-select-wrap">
    <div class="camera fl" @click="clickPlace">
      <span class="font-blue camera-text" v-if="totalCount">已选择{{ totalCount }}条场所数据</span>
      <span v-else>
        <!-- <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i> -->
        <span class="font-blue camera-text">请选择场所数据</span>
      </span>
    </div>
    <choose-device
      ref="ChooseDevice"
      node-key="deviceId"
      :table-columns="tableColumns"
      :load-data="leftData"
      :search-conditions="dataObject"
      need-handle
      @getDeviceIdList="getDeviceIdList"
      @getOrgCode="getOrgCode"
      modalTitle="场所"
      :defaultProps="defaultProps"
      treeNodeKey="regionCode"
      :selected-list="dataObject.placeIds"
      nodeKey="id"
    >
      <template #search-header>
        <ui-label class="inline mr-lg mb-lg" label="地名名称" :width="65">
          <Input v-model="dataObject.placeName" class="width-lg" placeholder="请输入地名名称" clearable></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-lg" label="地名俗称" :width="65">
          <Input v-model="dataObject.placeAlias" class="width-lg" placeholder="请输入地名俗称" clearable></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-lg" label="采集区域类型" :width="110">
          <Button type="dashed" class="area-btn" @click="areaSelectModalVisible = true"
            >请选择采集区域 {{ `已选择 ${dataObject.sbcjqyList.length} 个` }}</Button
          >
        </ui-label>
        <div class="inline mb-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="resetSearch">重置</Button>
        </div>
      </template>
      >
    </choose-device>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="dataObject.sbcjqyList"
    ></area-select>
  </div>
</template>

<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'place-select',
  components: {
    ChooseDevice: require('@/components/choose-device/choose-device').default,
    AreaSelect: require('@/components/area-select').default,
  },
  props: {
    dataObject: {
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      totalCount: '',
      searchConditions: {},
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        { title: '行政区划', key: 'regionName', ellipsis: true, tooltip: true },
        { title: '场所名称', key: 'placeName', width: 100 },
        { title: '地点俗称', key: 'placeAlias', ellipsis: true, tooltip: true },
        {
          title: '地名编码',
          key: 'placeInternalCode',
          ellipsis: true,
          tooltip: true,
        },
      ],
      leftData: (parameter) => {
        return this.$http
          .post(equipmentassets.placeManagerList, {
            ...this.dataObject,
            ...parameter,
          })
          .then((res) => {
            return res.data;
          });
      },
      defaultProps: { label: 'regionName', children: 'children' },
      areaSelectModalVisible: false,
    };
  },
  created() {},
  methods: {
    resetSearch() {},
    // 选择采集区域查询
    confirmArea(val) {
      this.dataObject.sbcjqyList = val;
    },
    // 选择场所
    clickPlace() {
      this.$refs.ChooseDevice.init();
    },
    // 选择场所确定
    getDeviceIdList(val) {
      this.dataObject.placeIds = val.chooseIds;
      this.dataObject.checkDeviceFlag = val.checkDeviceFlag;
      this.$$emit('getPlaceList', this.dataObject.placeIds, this.dataObject.checkDeviceFlag);
    },
    // 选中左侧组织机构
    getOrgCode(val) {
      this.dataObject.regionCodeList = val;
      this.$refs.ChooseDevice.search();
    },
    search() {
      this.$refs.ChooseDevice.search();
    },
  },
};
</script>

<style lang="less" scoped>
.custom-select-wrap {
  // height: 58px;
  .camera {
    width: 230px;
    margin: 0 auto;
    padding: 0;
    height: 34px;
    line-height: 32px;
    background: rgba(43, 132, 226, 0.1);
    border: 1px dashed var(--color-primary);
    &:hover {
      background: rgba(43, 132, 226, 0.2);
      border: 1px dashed #3c90e9;
    }
    .icon-xuanzeshexiangji {
      line-height: 30px;
    }
  }
}
</style>
