<template>
  <div class="bar-echart-box">
    <div class="influence-title">
      <span class="title-rect"></span>
      <span class="ml-sm mr-sm title-span f-16">
        <slot name="title" :selectPlatformName="getSelectPlatform">
          【{{ getSelectPlatform }}】抓拍突降设备时间分布
        </slot>
      </span>
      <i class="icon-font icon-jinggao mr-xs tip-color" v-if="echartInfo.result"></i>
      <div class="f-14 tip-color tip-text ellipsis">
        <Tooltip v-if="echartInfo.result" placement="bottom" :content="echartInfo.result" :disabled="disabledTooltip">
          <span class="ellipsis" @mouseenter="handleTooltip">
            {{ echartInfo.result }}
          </span>
        </Tooltip>
      </div>
      <Select v-model="selectPlatform" class="select-item" @on-change="getEchartData">
        <Option v-for="item in platformList" :value="item.k" :key="item.k">{{ item.v }}</Option>
      </Select>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length"
        class="charts"
        :echart-option="propertyEchart"
        :echart-style="ringStyle"
        ref="echartRef"
        :echarts-loading="echartLoading"
      ></draw-echarts>
      <span class="next-echart" v-if="echartData.length > echartNum">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('echartRef', echartData, [], echartNum)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import dataZoom from '@/mixins/data-zoom';
import dataAnalysis from '@/config/api/dataAnalysis.js';

export default {
  name: 'bar-echart',
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    activeInfluenceItem: {
      type: Object,
      default: () => {},
    },
    activeTabId: {
      type: String,
    },
    subtext: {
      type: String,
      default: '突降设备数量',
    },
  },
  mixins: [dataZoom],
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      echartNum: 8,
      echartLoading: false,
      propertyEchart: {},
      echartData: [],
      selectPlatform: '',
      platformList: [],
      echartInfo: {},
      disabledTooltip: false,
    };
  },
  computed: {
    getSelectPlatform() {
      let platform = this.platformList.filter((item) => item.k === this.selectPlatform);
      return platform[0]?.v || '';
    },
  },
  mounted() {},
  watch: {
    activeInfluenceItem: {
      handler(val) {
        if (!val || !val.key) return;
        this.getPlatformList();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    handleTooltip(e) {
      this.disabledTooltip = e.target.scrollWidth > e.target.clientWidth ? false : true;
    },
    // 获取平台列表
    async getPlatformList() {
      try {
        this.platformList = [];
        this.selectPlatform = '';
        let { batchId } = this.$route.query;
        if (!batchId) return;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
          },
        };
        let res = await this.$http.post(dataAnalysis.getDropDownList, data);
        this.platformList = res.data.data || [];
        if (this.platformList.length > 0) {
          this.selectPlatform = this.platformList[0].k;
          this.getEchartData();
        }
      } catch (error) {
        console.log(error);
      }
    },
    async getEchartData() {
      try {
        if (!this.selectPlatform) return;
        let { batchId } = this.$route.query;
        this.echartLoading = true;
        let data = {
          batchId: batchId,
          customParameters: {
            inflKey: this.activeTabId,
            fieldKey: this.activeInfluenceItem.key,
            orgCode: this.selectPlatform,
          },
        };
        let res = await this.$http.post(dataAnalysis.getGraphBarInfo, data);
        this.echartInfo = res.data.data || {};
        this.echartData = res.data.data.distriList || [];
        this.initRing();
      } catch (error) {
        console.log(error);
      } finally {
        this.echartLoading = false;
      }
    },
    initRing() {
      let seriesData = [],
        xAxisData = [],
        scaleData = [];
      this.echartData.forEach((item) => {
        seriesData.push({ value: item.deviceNum, ...item });
        xAxisData.push(item.time);
        scaleData.push({ value: item.resultValue, ...item });
      });
      let opts = {
        subtext: this.subtext,
        seriesData: seriesData,
        xAxisData: xAxisData,
        scaleData: scaleData,
      };
      this.propertyEchart = this.$util.doEcharts.CaptureDownTimeBar(opts);
      setTimeout(() => {
        this.setDataZoom('echartRef', [], this.echartNum);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.bar-echart-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: var(--bg-sub-echarts-content);
  .influence-title {
    width: 100%;
    height: 46px;
    background: var(--bg-sub-echarts-title);
    display: flex;
    align-items: center;
    .title-rect {
      display: inline-block;
      width: 5px;
      height: 20px;
      margin-left: 20px;
      background: var(--bg-title-rect);
    }
    .title-span {
      color: var(--color-sub-title-inpage);
    }
    .tip-color {
      color: var(--color-warning);
    }
    .tip-text {
      flex: 1;
      @{_deep}.ivu-tooltip {
        max-width: 100%;
        .ivu-tooltip-rel {
          display: flex;
        }
        .ivu-tooltip-inner {
          white-space: initial;
          max-width: 700px;
          max-height: 400px;
          overflow: hidden;
          overflow-y: auto;
        }
      }
    }
    .select-item {
      width: 230px;
      margin: 0 20px;
    }
  }
  .echarts-box {
    width: 100%;
    padding-top: 5px;
    height: calc(100% - 46px) !important;
    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
