/**
 * 设备标签
 *
 */
export default {
  getDeviceTag: '/ivdg-asset-app/deviceTag/pageList', // 查询设备标签-分页展示
  addDeviceTag: '/ivdg-asset-app/deviceTag/add', // 新增设备标签
  updateDeviceTag: '/ivdg-asset-app/deviceTag/update', // 更新标签
  deleteDeviceTag: '/ivdg-asset-app/deviceTag/remove', // 删除设备标签
  queryConfigList: '/ivdg-asset-app/tagAnalysis/queryConfigList', //获取全部标签分析配置列表
  queryPropertySearchList: '/ivdg-asset-app/propertySearch/queryPropertySearchList', // 查询全部字段搜索条件 propertyType 1：视图；2：人脸；3：车辆；4：视频；5：重点人员

  updateDeviceInfoTag: '/ivdg-asset-app/deviceTagLink/updateDeviceInfoTag', // 更新设备标签
  startTagAnalysis: '/ivdg-asset-app/tagAnalysis/execute', //开始执行设备标签分析
  updatePropertyTagAnalysis: '/ivdg-asset-app/tagAnalysis/edit', //新增/修改分析标签配置
  batchUpdateDeviceInfoTag: '/ivdg-asset-app/deviceTagLink/batchUpdateDeviceInfoTag', //批量修改设备所属标签
  importAllDeviceInfo: '/ivdg-data-governance-service/deviceInfoInit/importAllDeviceInfo', //导出
  deviceStatistics: '/ivdg-asset-app/device/statistics', // 设备统计
  getExportDevice: '/ivdg-asset-app/deviceTagLink/exportDevice', // 标签分析导出
  downloadTemplate: '/ivdg-asset-app/device/tag/downloadTemplate', // 标签模板下载
  deviceLinkedList: '/ivdg-asset-app/deviceTagLink/deviceLinkedList', // 标签导入
  linkTagByDirectoryIds: '/ivdg-asset-app/deviceTagLink/linkTagByDirectoryIds', // 标签导入
  removeDeviceLinkedForFile: '/ivdg-asset-app/deviceTagLink/removeDeviceLinkedForFile', //导入文件删除标签
  batchRemoveDeviceLinked: '/ivdg-asset-app/deviceTagLink/batchRemove', //批量删除设备标签

  getAllDeviceTagList: '/ivdg-asset-app/deviceTag/getAllDeviceTagList', //获取所有标签，选择关联标签
  getDeviceTagListByTagIds: '/ivdg-asset-app/deviceTag/getDeviceTagListByTagIds', //查询批量设备标签
  deviceTagPageList: '/ivdg-asset-app/deviceTag/pageList', //查询设备标签分页列表
  deviceTagView: '/ivdg-asset-app/deviceTag/view', //获取设备标签详细信息

  tagModelDataQueryList: '/ivdg-asset-app/tag/model/data/queryList', //查询标签模型列表
  tagModelDataAdd: '/ivdg-asset-app/tag/model/data/add', //新增标签模型
  tagModelDataUpdate: '/ivdg-asset-app/tag/model/data/update', //修改标签模型
  tagModelDataSelectSchemeUrl: '/ivdg-asset-app/tag/model/data/selectSchemeUrl', //查看标签模型模型包下载地址
  tagModelDataRemove: '/ivdg-asset-app/tag/model/data/remove', //{ids}标签模型删除
  tagModelDataUpdateStatus: '/ivdg-asset-app/tag/model/data/updateStatus', //更新状态
  tagModelDataView: '/ivdg-asset-app/tag/model/data/view', //{id}获取标签模型信息
};
