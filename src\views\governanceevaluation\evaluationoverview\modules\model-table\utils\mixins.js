export default {
  methods: {
    /**
     * 动态添加面包屑
     * @param {*} isFirstLevel 点击第一层
     * @param {*} secondLevelItem 第二层数据传入
     * @returns
     */
    initLevelData(isFirstLevel = true, secondLevelItem) {
      if (isFirstLevel) {
        this.breadcrumbData = [
          {
            id: this.selfRegionCode,
            add: `${this.titleName}-数据质量评测统计`,
            parentId: null,
            level: 1,
          },
        ];
      }
      // 第二层 - 增加面包屑 并选中
      if (!isFirstLevel) {
        let len = this.breadcrumbData.length;
        this.breadcrumbData.push({
          id: secondLevelItem.key,
          add: `${secondLevelItem.title}-质量统计`,
          parentId: this.breadcrumbData[len - 1].id,
          level: len + 1,
        });
        this.isDeepActiveItem = {
          id: secondLevelItem.key,
          // 判断是第一层（true） or 第二层（false）
          isFirstLevel: false,
          parentId: this.breadcrumbData[len - 1].id,
          level: len + 1,
        };
        return;
      }
      // 选中第一层
      this.isDeepActiveItem = {
        id: this.selfRegionCode,
        isFirstLevel: isFirstLevel,
        parentId: null,
        level: 1,
      };
    },
  },
};
