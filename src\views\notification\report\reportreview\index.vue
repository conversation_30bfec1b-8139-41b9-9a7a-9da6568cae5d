<template>
  <div class="myreport auto-fill">
    <div class="search-module">
      <ui-label label="汇报标题" class="inline">
        <Input class="width-md" v-model="searchData.title" placeholder="请输入汇报标题"></Input>
      </ui-label>
      <ui-label label="提交人" class="ml-lg inline">
        <Input class="width-md" v-model="searchData.sendName" placeholder="请输入提交人"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="提交时间">
        <DatePicker
          class="width-md"
          v-model="searchData.startSendTime"
          type="date"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startSendTime')"
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="width-md"
          v-model="searchData.endSendTime"
          type="date"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endSendTime')"
        ></DatePicker>
      </ui-label>
      <ui-label class="ml-lg inline" label=" ">
        <Button type="primary" class="mr-sm" @click="search">查询</Button>
        <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, search)">重置</Button>
      </ui-label>
    </div>
    <div class="table-module auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #time="{ row }">
          <span>{{ row.startTime + '至' + row.endTime }}</span>
        </template>
        <template #receiveOrgCodeNameList="{ row }">
          <div class="width-percent inline ellipsis" :title="row.receiveOrgCodeNameList.join(',')">
            {{ row.receiveOrgCodeNameList.join(',') }}
          </div>
        </template>
        <template #modifyTime="{ row }">
          <span :class="{ sign: row.lookStatus === '0' || row.lookStatus === '2' }">{{ row.modifyTime }}</span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip icon="icon-chakanxiangqing" content="查看" @click.native="handleEdit(row)"></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
    <add-edit-report
      v-model="addEditShow"
      :modal-action="modalAction"
      :modal-data="modalData"
      @update="search"
    ></add-edit-report>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'reportreview',
  props: {},
  data() {
    return {
      loading: false,
      searchData: {
        title: '',
        sendName: '',
        startSendTime: '',
        endSendTime: '',
        type: 2, // 类型 1工作汇报 2汇报查阅
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 1,
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endSendTime = new Date(this.searchData.endSendTime);
          if (endSendTime) {
            return date > endSendTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startSendTime = new Date(this.searchData.startSendTime);
          if (startSendTime) {
            return date < startSendTime;
          }
          return false;
        },
      },
      tableColumns: [
        {
          type: 'selection',
          title: '序号',
          width: 50,
          align: 'center',
        },
        {
          title: '汇报标题',
          key: 'title',
        },
        {
          title: '汇报时间',
          slot: 'time',
        },
        {
          title: '汇报给',
          slot: 'receiveOrgCodeNameList',
          tooltip: true,
        },
        {
          title: '提交人',
          key: 'sendName',
        },
        {
          title: '提交时间',
          slot: 'modifyTime',
        },
        {
          title: '操作',
          slot: 'action',
        },
      ],
      tableData: [],
      addEditShow: false,
      modalAction: {
        title: '查看',
        action: 'view',
      },
      modalData: {},
    };
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.post(equipmentassets.workReportList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    async getDetail(id) {
      try {
        const res = await this.$http.get(equipmentassets.workReportView, {
          params: {
            type: 2,
            id: id,
          },
        });
        let modalData = res.data.data;
        if (res.data.data.fileVos) {
          modalData.uploadData = res.data.data.fileVos.map((row) => {
            return {
              name: row.originalName,
              url: row.fileUrl,
              response: {
                id: row.id,
              },
            };
          });
        }
        this.modalData = modalData;
      } catch (err) {
        console.log(err);
      }
    },
    async handleEdit(row) {
      await this.getDetail(row.id);
      this.modalAction = {
        title: '查看汇报',
        action: 'view',
      };
      this.addEditShow = true;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.search();
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddEditReport: require('../components/add-edit-report.vue').default,
  },
};
</script>
<style lang="less" scoped>
.myreport {
  position: relative;
  background: var(--bg-content);
  .search-module {
    padding: 20px;
  }
  .ui-table {
    padding: 0 20px;
  }
  .sign {
    color: var(--bg-markers);
  }
}
</style>
