<template>
  <div class="tablist">
    <div v-for="(item, index) in tabList" :key="index" :class="{ active: currentTabIndex == index }" class="ivu-tabs-tab" @click="tabClick(index)">
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    tabList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      currentTabIndex: -1
    }
  },
  methods: {
    tabClick(index) {
      this.currentTabIndex = index
    }
  }
}
</script>

<style lang="less" scoped>
.tablist {
  height: 28px;
  line-height: 28px;
  .ivu-tabs-tab {
    float: left;
    border: 1px solid #2c86f8;
    border-right: none;
    padding: 0 15px;
    color: #2c86f8;
    &:hover {
      background: #2c86f8;
      color: #ffffff;
    }
    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      border-right: none;
    }
    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-right: 1px solid #2c86f8;
    }
  }
  .active {
    background: #2c86f8;
    color: #fff;
  }
}
</style>
