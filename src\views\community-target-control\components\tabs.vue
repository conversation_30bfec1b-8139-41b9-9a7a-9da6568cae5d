<!--
 * @Date: 2025-01-24 14:47:29
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-06 11:44:09
 * @FilePath: \icbd-view\src\views\juvenile-target-control\components\tabs.vue
-->
<template>
  <ul class="tab">
    <li
      v-for="(item, index) in list"
      :key="item[config.value]"
      :class="{ select: item[config.value] == selectLi, li: true }"
      @click="change(item[config.value])"
    >
      {{ item[config.label] }}
      <div v-if="index < list.length - 1" class="line"></div>
    </li>
  </ul>
</template>
<script>
export default {
  components: {},
  props: {
    // 当前选中的tab
    selectLi: {
      type: Number | String,
    },
    // tab列表
    list: {
      type: Array,
      default: () => [],
    },
    // 自定义名称和值的字段
    config: {
      type: Object,
      default: () => {
        return {
          label: "label",
          value: "value",
        };
      },
    },
  },
  model: {
    prop: "selectLi",
  },
  methods: {
    /**
     * @description: 切换选中的tab
     * @param {number | string} value 选中的tab
     */
    change(value) {
      if (value === this.selectLi) return;
      this.$emit("input", value);
      this.$emit("change", value);
    },
  },
};
</script>
<style lang="less" scoped>
.tab {
  display: flex;
  background: #fff;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .li {
    position: relative;
    padding: 10px 30px;
    font-size: 16px;
    cursor: pointer;
    color: rgba(0, 0, 0, 0.9);
    .line {
      height: 50%;
      width: 1px;
      border-right: 1px solid #bfbdbd;
      position: absolute;
      right: 0;
      top: 25%;
    }
  }
  .select {
    font-weight: 600;
    color: #2c86f8;
  }
}
</style>
