<template>
  <div class="dom-wrapper">
    <div class="dom">
      <section class="dom-content">
        <p class="dom-content-p">
          <span class="label">{{ data[0].seriesName }}：</span>
          <span class="message">{{ data[0].value | numberInfo }}</span>
        </p>
      </section>
    </div>
    <div class="triangle"></div>
  </div>
</template>
<script>
export default {};
</script>
<style lang="less" scoped>
.dom {
  width: 150px;
  height: 52px;
  line-height: 52px;
  border-radius: 4px;
  position: relative;
  .dom-content {
    padding-bottom: 5px;
    font-size: 14px;
    .dom-content-p {
      vertical-align: text-top;
      display: flex;
      position: relative;
      .label {
        display: inline-block;
        color: var(--color-content);
        white-space: nowrap;
        margin-left: 8px;
        color: '#FFF';
      }
      .message {
        display: inline-block;
        color: var(--color-bluish-green-text);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}
</style>
