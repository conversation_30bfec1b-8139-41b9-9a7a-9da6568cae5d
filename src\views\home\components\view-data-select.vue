<template>
  <div class="view-container">
    <!--    参考 reach-tabs-->
    <i
      class="icon-font icon-renliankakou icon-color vt-middle f-16 fl mr-sm"
      :class="{ 'icon-selected': current === '2' }"
      @click="onClick('2')"
    ></i>
    <i
      class="icon-font icon-cheliangkakou icon-color vt-middle f-16 fl mr-sm"
      :class="{ 'icon-selected': current === '3' }"
      @click="onClick('3')"
    ></i>
    <i
      class="icon-font icon-keypersonlibrary icon-color vt-middle f-16 fl"
      :class="{ 'icon-selected': current === '5' }"
      @click="onClick('5')"
    ></i>
  </div>
</template>

<script>
export default {
  name: 'view-data-select',
  components: {},
  props: { value: {} },
  data() {
    return {
      current: '',
    };
  },
  computed: {},
  watch: {
    value: {
      immediate: true,
      deep: true,
      handler: function (val) {
        this.current = val;
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    onClick(val) {
      this.current = val;
      this.$emit('input', val);
      this.$emit('change', val);
    },
  },
};
</script>

<style lang="less" scoped>
.icon-color {
  color: #2287a4;
}
.icon-selected {
  //-webkit-background-clip: text;
  //color: #0000;
  //background: linear-gradient(0deg, #780F0F, #EC5353);
  color: #539aea;
}
.view-container {
  height: 100%;
  position: relative;
  display: inline-block;
}
</style>
