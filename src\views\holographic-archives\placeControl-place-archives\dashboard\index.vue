<template>
  <div class="place-archive-dashboard-container">
    <!-- 基本信息 -->
    <div class="base-info-wrapper">
      <BaseInfo :info="baseInfo" />
    </div>
    <!-- 标签和图片 -->
    <div class="label-image-wrapper">
      <LabelCloudView
        :labels="baseInfo.labels || []"
        :type="5"
        :info="baseInfo"
      />
    </div>
    <!-- 人员统计 -->
    <div class="statistics-wrapper">
      <PlacePeopleStatistics :archiveNo="baseInfo.id" />
    </div>
    <!-- 场所资源 -->
    <div class="place-resource-wrapper">
      <PlaceResource />
    </div>
    <!-- 最新报警 -->
    <div class="latest-alarm-wrapper">
      <PlaceLastestAlarm
        :alarmKindList="alarmKindList"
        :archiveNo="baseInfo.id"
        :type="'placeId'"
        @handleAlarmMore="handleAlarmMore"
      />
    </div>
    <!-- 图上位置 -->
    <div class="location-wrapper">
      <ui-module title="图上位置">
        <MapBase
          ref="mapRef"
          class="map"
          v-if="baseInfo.coord"
          :place-fence="placeFence"
        />
      </ui-module>
    </div>
    <!-- 抓拍 -->
    <div class="capture-wrapper">
      <CaptureBehavior />
    </div>
  </div>
</template>

<script>
import BaseInfo from "@/views/holographic-archives/place-archives/dashboard/components/base-info.vue";
import PlaceResource from "@/views/holographic-archives/place-archives/dashboard/components/place-resource.vue";
import DataParse from "@/views/holographic-archives/place-archives/dashboard/components/data-parse.vue";
import CaptureBehavior from "@/views/holographic-archives/place-archives/dashboard/components/capture-behavior";
import LabelCloudView from "@/views/holographic-archives/components/label-cloud-view/index";
import MapBase from "@/components/map/index";
import UiModule from "@/views/holographic-archives/place-archives/components/ui-module.vue";
import PlacePeopleStatistics from "./components/place-people-statistics.vue";
import PlaceLastestAlarm from "@/views/holographic-archives/one-person-one-archives/importantPerson-archive/dashboard/components/lastest-alarm.vue";
import AlarmTab from "@/views/juvenile/components/alarm-tab.vue";
import RiskPersonnel from "@/views/importantPerson-management/components/risk-personnel.vue";
import ImportantPersonClusterTab from "@/views/placeControl-management/components/important-person-cluster-tab.vue";
import PlaceEvents from "@/views/placeControl-management/components/place-events.vue";

import {
  getFaceAlarmPageList as getFaceAlarmList,
} from "@/api/monographic/juvenile.js";
import {
  abnormalBehaviorPageList,
  queryEmphasisPersonGatheredPlace,
  queryEventScore
} from "@/api/monographic/place.js";
const getFaceAlarmPageList = async (param) => {
  const date = new Date();
  const res = await getFaceAlarmList({
        pageNumber: 1,
        pageSize: 3,
        placeId: param.placeId,
        alarmTimeB: "2000-01-01 00:00:00",
        alarmTimeE: date.format("yyyy-MM-dd") + " 23:59:59",
      });
  return res;
};

const getAbnormalBehaviorPageList = async (param) => {
  const res = await abnormalBehaviorPageList(param);
  const arr =
    res?.data?.entities?.map(({ faceCapture, ...item }) => ({
      ...item,
      ...faceCapture,
    })) || [];
  return { data: { entities: arr } };
};
export default {
  name: "PlaceArchiveDashboard",
  components: {
    BaseInfo,
    LabelCloudView,
    MapBase,
    DataParse,
    CaptureBehavior,
    PlaceResource,
    UiModule,
    PlacePeopleStatistics,
    PlaceLastestAlarm,
    ImportantPersonClusterTab,
    PlaceEvents
  },
  props: {
    baseInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  computed: {
    // 场所围栏
    placeFence() {
      if (this.baseInfo.coord) {
        let placeFence = {
          code: "67021",
          name: "购物服务",
          color: "#F37A7A",
          layerType: "LayerType_" + this.baseInfo.secondLevel,
        };
        placeFence.data = [
          {
            properties: { ...this.baseInfo },
            geometry: JSON.parse(this.baseInfo.coord),
          },
        ];
        return placeFence;
      }
    },
  },
  data() {
    return {
      alarmKindList: [
        {
          title: "重点人发现报警",
          key: 1,
          component: AlarmTab,
          request: getFaceAlarmPageList,
        },
        {
          title: "疑似重点人聚集场所",
          key: 2,
          component: ImportantPersonClusterTab,
          request: queryEmphasisPersonGatheredPlace,
          style: {
            padding: "10px 20px",
          },
        },
        {
          title: "场所异常行为预警",
          key: 3,
          component: RiskPersonnel,
          request: getAbnormalBehaviorPageList,
          style: {
            padding: "10px 20px",
          },
        },
        // {
        //   title: "场所事件积分",
        //   key: 4,
        //   component: PlaceEvents,
        //   request: queryEventScore,
        //   style: {
        //     padding: "10px 20px",
        //   },
        // },
      ],
    };
  },
  methods: {
    handleAlarmMore(item) {
      const { href } = this.$router.resolve({
        path: "/place-control/alarm-manager",
        query: {
          // 直接跳到人员报警
          compareType: item.key,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.place-archive-dashboard-container {
  width: 100%;
  height: 100%;
  padding: 16px 20px 30px;
  display: grid;
  grid-template-rows: repeat(3, 1fr);
  grid-template-columns: 500px 1fr 500px;
  grid-gap: 20px;
  grid-template-areas:
    "a b c"
    "d b e"
    "f g g";
  & > div {
    // 让每项高度保持一样，不被自动撑开
    min-height: 0;
  }
  .base-info-wrapper {
    grid-area: a;
  }
  .label-image-wrapper {
    grid-area: b;
  }
  .statistics-wrapper {
    grid-area: c;
  }
  .place-resource-wrapper {
    grid-area: d;
  }
  .latest-alarm-wrapper {
    position: relative;
    grid-area: e;
  }
  .location-wrapper {
    grid-area: f;
  }
  .capture-wrapper {
    position: relative;
    grid-area: g;
  }
}
/deep/ .risk-personnel-card {
  width: 300px;
  .image-box {
    height: 105px;
  }
}
</style>
