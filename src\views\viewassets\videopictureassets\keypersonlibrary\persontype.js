const persontype = [
  { tagName: '在逃', tagColour: 'var(--color-warning)' },
  { tagName: '涉稳', tagColour: 'var(--color-warning)' },
  { tagName: '上访', tagColour: 'var(--color-warning)' },
  { tagName: '涉恐', tagColour: 'var(--color-warning)' },
  { tagName: '精神病', tagColour: 'var(--color-warning)' },
  { tagName: '涉毒', tagColour: 'var(--color-warning)' },
  { tagName: '宗教', tagColour: 'var(--color-warning)' },
  { tagName: '境外', tagColour: 'var(--color-warning)' },
  { tagName: '常住', tagColour: 'var(--color-success)' },
  { tagName: '临时', tagColour: 'var(--color-success)' },
  { tagName: '暂住', tagColour: 'var(--color-success)' },
  { tagName: '其他', tagColour: 'var(--color-success)' },
];
export default persontype;
