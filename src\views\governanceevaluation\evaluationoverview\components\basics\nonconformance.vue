<template>
  <ui-modal v-model="visible" :title="title" :footer-hide="true" :styles="styles">
    <div>
      <div class="table-box">
        <ui-table
          class="ui-table"
          :loading="reasonLoading"
          :table-columns="tableColumns"
          :table-data="tableData"
          :minus-height="minusTable"
          ref="table"
        >
        </ui-table>
      </div>
      <!-- <ui-page
        class="page"
        :page-data="reasonPage"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page> -->
    </div>
  </ui-modal>
</template>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 40px 20px 20px !important;
}
.table-box {
  position: relative;
  overflow: hidden;
  padding: 0 10px;
  .ui-table {
    width: 100%;
  }
}
</style>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    reasonLoading: {
      type: <PERSON>olean,
      default: false,
    },
    tableColumns: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    reasonPage: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      searchData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      visible: false,
      styles: {
        width: '8rem',
      },

      formData: {},
      minusTable: 350,
    };
  },
  created() {},
  mounted() {},
  methods: {
    changePage(val) {
      // this.searchData.pageNum = val;
      this.$emit('handlePageChange', val);
    },
    changePageSize(val) {
      // this.searchData.pageSize = val;
      this.$emit('handlePageSizeChange', val);
    },
    init() {
      this.visible = true;
    },
  },
  watch: {
    // value(val) {
    //   this.visible = val
    // },
    // visible(val) {
    //   this.$emit('input', val)
    // },
  },
  computed: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
