<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-expand="handleExpand"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #historyTime="{ row }">
            <div>
              {{ row.videoStartTime | timeFormat }} -
              {{ row.videoEndTime | timeFormat }}
            </div>
          </template>
          <template #pointNum="{ row }">
            <DevicePop :deviceList="row?.taskResourceList || []"></DevicePop>
          </template>
          <template #status="{ row }">
            <div>
              {{ taskStatusList.find((item) => item.key == row.status).label }}
            </div>
          </template>
          <template #taskType="{ row }">
            <div>
              {{ taskTypeList[row.taskType] }}
            </div>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <addModal
      v-if="isShowAdd"
      ref="addModal"
      :title="subTaskId ? '编辑历史解析任务' : '新增历史解析任务'"
      :subTaskType="subTaskType"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      @updated="jobUpdated"
      @close="handlerClose"
    ></addModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "../components/search.vue";
import addModal from "../components/add-modal.vue";
import expandRow from "../components/expandRow.vue";
import TableAction from "../components/table-action.vue";
import { multiModalTaskPageList } from "@/api/multimodal-analysis.js";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import DevicePop from "../components/device-pop.vue";
import { taskTypeList, taskStatusList } from "../enums/index.js";
import TaskHandler from "@/views/multimodal-analysis/mixins/taskHandler.js";
export default {
  name: "HistoryAnalysis",
  components: {
    Search,
    addModal,
    adjustPosition,
    DevicePop,
  },
  props: {},
  mixins: [TaskHandler],
  data() {
    return {
      taskTypeList,
      taskStatusList,
      subTaskType: "history",
      list: [],
      childList: [],
      loading: false,
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        structuredParsingType: 3, // 结构化解析类型 3历史解析
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const deviceInfoList = row.taskResourceList.map((item) => {
              const { deviceInfo, ...otherParam } = item;
              return {
                ...deviceInfo,
                ...otherParam,
                deviceId: deviceInfo?.id,
              };
            });
            return h(expandRow, {
              props: {
                tableList: deviceInfoList,
                columns: this.childColumns,
                currentJob: this.list[index],
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    ...val,
                    structuredParsingType: row.structuredParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  // 子任务删除
                  this.deleteTasks([val]);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 170 },
        { title: "历史时段", slot: "historyTime", width: 360 },
        { title: "处理类型", slot: "taskType" },
        { title: "解析类型", key: "structureAlgorithmName" },
        { title: "点位数量", slot: "pointNum" },
        { title: "任务状态", slot: "status" },
        { title: "创建时间", key: "createTime", width: 190 },
        { title: "创建人", key: "creator" },
        {
          title: "操作",
          width: 120,
          render: (h, { row, index }) => {
            return h(TableAction, {
              props: {
                row,
                subTaskType: this.subTaskType,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(val);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: val.id,
                    ...val,
                  });
                },
                mapLoaction: (val) => {
                  this.mapLoaction(val);
                },
                handleDel: (val) => {
                  this.deleteJobs([val]);
                },
              },
            });
          },
        },
      ],
      childColumns: [
        { title: "设备名称", slot: "name", align: "center" },
        { title: "任务状态", slot: "fileResourceStatus", align: "center" },
        { title: "处理进度", slot: "analysisSchedule" },
        { title: "处理时长", slot: "analysisTime" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  beforeDestroy() {},
  methods: {
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    handlerClose() {
      this.isShowAdd = false;
    },
    // 查询列表
    getList(otherParam = {}) {
      this.loading = true;
      let param = {
        ...this.$refs.searchRef.getSearchParam(),
        ...this.params,
        ...otherParam,
      };
      multiModalTaskPageList(param)
        .then((res) => {
          this.list = res?.data?.entities || [];
          this.total = res?.data?.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init();
      });
    },
    handleEdit(row, subDeviceId = "") {
      if (row.status != 0) {
        return;
      }
      this.subTaskId = row.id;
      this.subDeviceId = subDeviceId;
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init({ ...row });
      });
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const deviceInfoList = item.taskResourceList.map((item) => {
        return item.deviceInfo;
      });
      var arrayP = [];
      deviceInfoList &&
        deviceInfoList.length > 0 &&
        deviceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      }
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .data-above {
      .left-operater {
        display: flex;
      }
      .right-operater {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .rotate {
          transform: rotate(180deg);
        }
      }

      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      button {
        margin-right: 10px;
      }
    }
    .table-content {
      flex: 1;
      overflow: scroll;
      .ui-table {
        height: 100%;
        overflow: unset;
        /deep/ .ivu-table-wrapper {
          overflow: unset;
        }
      }
    }
  }
}
</style>
