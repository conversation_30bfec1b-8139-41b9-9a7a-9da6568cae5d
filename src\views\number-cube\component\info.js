import vehicle from '@/assets/img/vehicle-1.png'
import people from '@/assets/img/demo/avatar-1.png'
import housePic from '@/assets/img/number-cube/card_pic_house.png'
const data = {
  imsi: '6786786787798798',
  wifi: '67-86-78-67-87',
  rfid: '6786786787798798',
  addressT: '花园小区1栋2单元209号',
  name: '张大力',
  gender: '男',
  nation: '汉',
  householdRegister: '广西壮族自治区',
  time: '2020-03-10 13:12:10',
  address: '广西壮族自治区梧州市蒙山县毕升大道994号',
  zwppmc: '大众',
  jdccllxdm: '轿车',
  idNumber: '320102199003079556',
  jdchphm: '苏A 29999',
  fcxz: '商品房',
  djsj: '2020-03-10',
  iphone: '18806156779',
  fcdzDzmc: '青海省海东地区平安县张衡路742号',
  frequency: '6786786787798798',

  labels: [
    { name: '常住人员常住人员常住人员常住', color: '#1FAF8A' },
    { name: '常住人员', color: '#F8775C' },
    { name: '常住', color: '#E99E53' },
    { name: '常', color: '#1FAF8A' }
  ],
  listInfo: [
    {
      time: '2022.02.02 15:00:01', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    },
    {
      time: '2022.02.02 15:00:02', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    },
    {
      time: '2022.02.02 15:00:03', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    },
    {
      time: '2022.02.02 15:00:04', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    },
    {
      time: '2022.02.02 15:00:05', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    },
    {
      time: '2022.02.02 15:00:06', car: 'K2022', chexiang: '5', carId: '苏A66666',
      jiudian: '锦江都城', jiudiandizhi: '南京市雨花台区铁心桥架街道11号',
      list: [
        { name: '网三', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
        { name: '香玉', zuoweihao: '18号', shangche: '南京', mudi: '舟山', jinzhan: '2022.02.02 15:00:00', chuzhan: '2022.02.02 15:00:00' },
      ]
    }
  ]
}
export const list = [
  {
    type: 'tonghuoche',
    name: '同火车',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tonghangban',
    name: '同航班',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongqiche',
    name: '同汽车',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongjiudian',
    name: '同酒店同房间',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongxiang',
    name: '同乡',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongjiacheng',
    name: '同驾乘',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongjiacheng',
    name: '同驾乘',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongxing',
    name: '同行',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tongwangba',
    name: '同网吧',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'tonghuji',
    name: '同户籍',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
  },
  {
    type: 'cheliangsuoyou',
    name: '车辆所有人',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'car', src: vehicle }),
  },
  {
    type: 'jiache',
    name: '驾车车辆',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
  },
  {
    type: 'tongshigu',
    name: '同事故',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
  },
  {
    type: 'cheliagbansui',
    name: '车辆伴随',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
  },

  {
    type: 'tongjiashiren',
    name: '同驾驶人',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'vehicle', src: vehicle }),
  },
  {
    type: 'fangwu',
    name: '房屋归属',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'home', src: housePic, labels: [] }),
  },
  {
    type: 'shoujihao',
    name: '手机号归属',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'iphone', labels: [] }),
  },
  {
    type: 'IMIS',
    name: 'IMSI归属',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'IMSI', labels: [] }),
  },
  {
    type: 'IMISS',
    name: 'IMSI同行',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'IMSIS', labels: [] }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'IMSIS', labels: [] }),
  },
  {
    type: 'rfids',
    name: 'RFID归属',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'people', src: people }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'rfids', labels: [] }),
  },
  {
    type: 'wifi',
    name: 'Wi-Fi同行',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'wifi', labels: [] }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'wifi', labels: [] }),
  },
  {
    type: 'rfid',
    name: 'RFID同行',
    dataFrom: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'rfid', labels: [] }),
    dataTo: Object.assign(JSON.parse(JSON.stringify(data)), { type: 'rfid', labels: [] }),
  }
]