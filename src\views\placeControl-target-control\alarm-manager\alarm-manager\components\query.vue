<template>
  <Form ref="formData" :inline="true" :label-width="80">
    <FormItem label="布控目标:" prop="name">
      <Input v-model="queryParam.name" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="身份证号:" prop="idCardNo">
      <Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
    </FormItem>
    <FormItem label="布控类型:">
      <selectTag
        class="selectTag"
        :list="typeList"
        ref="taskType"
        vModel="taskType"
        @selectItem="selectItem"
      />
    </FormItem>
    <FormItem
      label="布控库:"
      prop="faceLibIds"
      v-if="queryParam.taskType == '2'"
    >
      <Select
        v-model="queryParam.faceLibIds"
        placeholder="请选择"
        multiple
        transfer
        :max-tag-count="1"
      >
        <Option :value="item.id" v-for="item in libList" :key="item.id">{{
          item.libName
        }}</Option>
      </Select>
    </FormItem>
    <FormItem label="报警设备:">
      <div class="select-tag-button" @click="selectDevice()">
        选择设备/已选（{{ queryParam.deviceIds.length }}）
      </div>
    </FormItem>
    <br />
    <FormItem label="布控级别:">
      <selectTag
        class="selectTag"
        :list="levelList"
        ref="taskLevel"
        vModel="taskLevel"
        @selectItem="selectItem"
      />
    </FormItem>
    <FormItem label="相似度:" class="slider-form-item">
      <div class="slider-content">
        <Slider v-model="queryParam.simScore"></Slider>
        <span>{{ queryParam.simScore }}%</span>
      </div>
    </FormItem>
    <FormItem label="算法选择:" class="slider-form-item">
      <CheckboxGroup v-model="queryParam.algorithmVendorList">
        <Checkbox
          :label="item.dataKey"
          v-for="(item, index) in algorithmTypeList"
          :key="index"
          ><span :class="[item.dataKey == 'GLST' ? 'gerling' : 'hk']">{{
            item.dataValue
          }}</span></Checkbox
        >
      </CheckboxGroup>
    </FormItem>
    <select-device
      ref="selectDevice"
      :showOrganization="true"
      @selectData="selectData"
    />
  </Form>
</template>
<script>
import selectTag from "../../../components/select-tag.vue";
import { queryFaceLibList } from "@/api/monographic/juvenile.js";
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { mapActions } from "vuex";
export default {
  components: { selectTag },
  data() {
    return {
      selectDeviceList: [], // 选中的设备
      queryParam: {
        name: "", // 布控目标
        idCardNo: "", // 身份证号
        taskType: null, // 布控类型：null - 全部
        taskLevel: null, // 布控级别：null - 全部
        simScore: 80, // 相似度
        deviceIds: [], // 选中设备的Id
        algorithmVendorList: [], // 算法选择
        faceLibIds: [], // 选择的布控库id
      },
      // 布控类型
      typeList: [
        { name: "单体布控", value: "1" },
        { name: "库布控", value: "2" },
      ],
      // 布控级别
      levelList: [
        { name: "一级", value: "1" },
        { name: "二级", value: "2" },
        { name: "三级", value: "3" },
      ],
      libList: [], // 布控库下拉列表
    };
  },
  computed: {
    keyWords() {
      return this.$route.query.keyWords || "";
    },
  },
  async created() {
    if (this.keyWords) {
      let params = {
        deviceName: this.keyWords,
        pageNumber: 1,
        pageSize: 100,
        filter: this.judgeUser,
        orgCodes: [],
      };
      let { data } = await queryDeviceList(params);
      data.entities.map((item) => {
        item.select = true;
      });
      this.selectData(data.entities);
    }
    if (this.$route.query.idCardNo) {
      this.queryParam.idCardNo = this.$route.query.idCardNo;
    }
    await this.getDictData();
    // 获取布控库列表 这边有可能要改 改成只查未成年人布控
    queryFaceLibList({
      controlStatus: 1,
      libSource: 2,
    }).then((res) => {
      this.libList = res.data;
    });
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 选择设备，打开弹框
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList, this.keyWords);
    },

    /**
     * @description: 初始化已选择的设备
     * @param {array} arr 已选择的设备
     */
    selectData(arr) {
      this.selectDeviceList = arr;
      this.queryParam.deviceIds = arr.map((e) => e.deviceGbId);
    },

    /**
     * @description: 重置
     */
    reset() {
      this.selectDeviceList = [];
      this.queryParam.name = "";
      this.queryParam.idCardNo = "";
      this.queryParam.taskType = null;
      this.queryParam.taskLevel = null;
      this.queryParam.simScore = 80;
      this.queryParam.deviceIds = [];
      this.queryParam.faceLibIds = [];
      this.$refs.taskType.currentIndex = -1;
      this.$refs.taskLevel.currentIndex = -1;
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },
    /**
     * @description: 获取查询参数，暴露给父组件
     * @return {object}
     */
    getQueryParams() {
      return this.queryParam;
    },
  },
};
</script>
<style lang="less" scoped>
.selectTag {
  margin-top: 4px;
}
.gerling {
  color: #f29f4c;
}
.hk {
  color: #2c86f8;
}
</style>
