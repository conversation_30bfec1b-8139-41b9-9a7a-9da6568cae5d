MapPlatForm={},MapPlatForm.Base={},MapPlatForm.ModdleMarker=0,MapPlatForm.BottomMarker=1,MapPlatForm.CustomMarker=2,MapPlatForm.VERSIONNUMBER="v1.8.13",MapPlatForm.dataServiceURL="/npgisdataservice/",function(){MapPlatForm.Base.MapConfig=function(a){this.CLASS_NAME="MapConfig",this.dataServiceURL=a?a:MapPlatForm.dataServiceURL,this.mapServiceURL="",this.mapInfo=null,this._mapJson=null},MapPlatForm.Base.MapConfig.prototype._addLayerToMap=function(a,b){var c=[];if(b&&b.length>0){for(var d,e,f,g=0,h=b.length;h>g;g++)d=b[g],f=d.layerType.split("."),e=new NPMapLib.Layers[f[f.length-1]](d.layerOpt.url,d.layerName,d.layerOpt),c.push(e);a.addLayers(c)}return c},MapPlatForm.Base.MapConfig.prototype.createMap=function(a,b){this.mapInfo=b;for(var c=new NPMapLib.Map(a,b.mapOpts),d=this._addLayerToMap(c,b.vectorLayer),e=this._addLayerToMap(c,b.sattilateLayer),f=e.length-1;f>=0;f--)e[f].hide();return this._mapJson={map:c,vectorLayer:d,sattilateLayer:e},this.showVectorLayer(),this._mapJson},MapPlatForm.Base.MapConfig.prototype.showVectorLayer=function(){if(this._mapJson&&this._mapJson.vectorLayer){if(this._mapJson.vectorLayer.length>1)for(var a=1;a<this._mapJson.vectorLayer.length;a++)this._mapJson.vectorLayer[a].show(),this._mapJson.vectorLayer[a].setZIndex(this._mapJson.vectorLayer[0].getZIndex()+a);if(this._mapJson.vectorLayer.length>0&&this._mapJson.vectorLayer[0].show(),this._mapJson.sattilateLayer.length>1)for(var a=this._mapJson.sattilateLayer.length-1;a>=1;a--)this._mapJson.sattilateLayer[a].hide();this._mapJson.sattilateLayer.length>0&&this._mapJson.sattilateLayer[0].hide(),this._mapJson.vectorLayer.length>0&&this._mapJson.map.setBaseLayer(this._mapJson.vectorLayer[0])}},MapPlatForm.Base.MapConfig.prototype.showSattilateLayer=function(){if(this._mapJson&&this._mapJson.sattilateLayer){if(this._mapJson.sattilateLayer.length>1)for(var a=this._mapJson.sattilateLayer.length-1;a>=1;a--)this._mapJson.sattilateLayer[a].show(),this._mapJson.sattilateLayer[a].setZIndex(this._mapJson.sattilateLayer[0].getZIndex()+a);if(this._mapJson.sattilateLayer.length>0&&this._mapJson.sattilateLayer[0].show(),this._mapJson.vectorLayer.length>1)for(var a=this._mapJson.vectorLayer.length-1;a>=1;a--)this._mapJson.vectorLayer[a].hide();this._mapJson.vectorLayer.length>0&&this._mapJson.vectorLayer[0].hide(),this._mapJson.sattilateLayer.length>0&&this._mapJson.map.setBaseLayer(this._mapJson.sattilateLayer[0])}}}(),function(){MapPlatForm.Base.MapGeometry=function(a){this.CLASS_NAME="MapPlatForm.Base.MapGeometry",this.map=a},MapPlatForm.Base.MapGeometry.prototype.getGeometryByGeoJson=function(a){var b=GeoJSON.read(a);return b},MapPlatForm.Base.MapGeometry.prototype.getGeometryByWKT=function(a){var b=WKT.read(a);return b},MapPlatForm.Base.MapGeometry.prototype.getFGeoJsonByGeometry=function(a){var b=GeoJSON.write(a,this.map);return b},MapPlatForm.Base.MapGeometry.prototype.getGGeoJsonByGeometry=function(a){var b=GeoJSON.write(a,this.map),c=JSON.parse(b),a=JSON.stringify(c.geometry);return a},MapPlatForm.Base.MapGeometry.prototype.getWKTByGeometry=function(a){var b=WKT.write(a,this.map);return b},MapPlatForm.Base.MapGeometry.prototype.getExtent2Polygon=function(a){var b=[];b.push(new NPMapLib.Geometry.Point(a.sw.lon,a.sw.lat)),b.push(new NPMapLib.Geometry.Point(a.ne.lon,a.sw.lat)),b.push(new NPMapLib.Geometry.Point(a.ne.lon,a.ne.lat)),b.push(new NPMapLib.Geometry.Point(a.sw.lon,a.ne.lat)),b.push(new NPMapLib.Geometry.Point(a.sw.lon,a.sw.lat));var c=new NPMapLib.Geometry.Polygon(b);return c},MapPlatForm.Base.MapGeometry.prototype.createMarker=function(a,b){markerType=b.markerType?b.markerType:MapPlatForm.ModdleMarker;var c=new NPMapLib.Symbols.Icon(b.url,new NPMapLib.Geometry.Size(b.size.width,b.size.height));markerType===MapPlatForm.ModdleMarker&&c.setAnchor(new NPMapLib.Geometry.Size(-b.size.width/2,-b.size.height/2)),markerType==MapPlatForm.CustomMarker&&c.setAnchor(new NPMapLib.Geometry.Size(-b.iconOffset.width,-b.iconOffset.height));var d=new NPMapLib.Symbols.Marker(a);if(d.setIcon(c),b.text){label=new NPMapLib.Symbols.Label(b.text),label.setStyle({Color:"#ffffff"}),b.labelOffset=b.labelOffset?b.labelOffset:{width:0,height:0};var e=new NPMapLib.Geometry.Size(b.labelOffset.width,b.labelOffset.height);label.setOffset(e),d.setLabel(label)}return d},MapPlatForm.Base.MapGeometry.prototype.createCircleSector=function(a,b,c,d){var e=60,f=NPMap.T.helper.webMoctorJW2PM(a.lon,a.lat);d=d*Math.PI/180;for(var g,h,i,j=c*Math.PI/180,k=[],l=[],m=0;e+1>m;m++)g=d+j/e*m,h=f.lon+b*Math.cos(g),i=f.lat+b*Math.sin(g),k.push(new NPMap.Geometry.Point(h,i));0!==j&&k.push(f);for(var m=0;m<k.length;m++){var n=NPMap.T.helper.inverseMercator(k[m].lon,k[m].lat);l.push(new NPMap.Geometry.Point(n.lon,n.lat))}return new NPMap.Geometry.Polygon(l)},MapPlatForm.Base.MapGeometry.prototype.getIconByParam=function(a){markerType=a.markerType?a.markerType:MapPlatForm.ModdleMarker;var b=new NPMapLib.Symbols.Icon(a.url,new NPMapLib.Geometry.Size(a.size.width,a.size.height));return markerType===MapPlatForm.ModdleMarker&&b.setAnchor(new NPMapLib.Geometry.Size(-a.size.width/2,-a.size.height/2)),markerType===MapPlatForm.CustomMarker&&b.setAnchor(new NPMapLib.Geometry.Size(-a.iconOffset.width,-a.iconOffset.height)),b},MapPlatForm.Base.MapGeometry.prototype.getExtentByOverlays=function(a){for(var b,c,d,e,f=a.length-1;f>=0;f--){var g=a[f].getExtent();b&&c&&d&&e?(b>g.left&&(b=g.left),c>g.bottom&&(c=g.bottom),d<g.right&&(d=g.right),e<g.top&&(e=g.top)):(b=g.left,c=g.bottom,d=g.right,e=g.top)}return new NPMapLib.Geometry.Extent(b,c,d,e)},MapPlatForm.Base.MapGeometry.prototype.sortingResourceByLine=function(a,b,c){c=c||50;for(var d=[],e=this._getLinePoints(b,c),f=this._getShortPointsInLine(a,b),g=0;g<e.length;g++){var h=null;h=g<e.length-1?e[g+1]:new NPMapLib.Geometry.Point(e[g].lon+(e[g].lon-e[g-1].lon)/2,e[g].lat+(e[g].lat-e[g-1].lat)/2);for(var i=this._findShortestMarker(f,e[g],h,c),j=i.length-1;j>=0;j--){for(var k=!1,l=0;l<d.length;l++)d[l]&&d[l].id===i[j].id&&(k=!0);!k&&i[j]&&d.push(i[j])}}for(var g=0;g<a.length;g++){for(var k=!1,l=0;l<d.length;l++)d[l].id===a[g].id&&(k=!0);k||d.push(a[g])}return d},MapPlatForm.Base.MapGeometry.prototype._getLinePoints=function(a,b){for(var c=a.getPath(),d=[],e=0;e<c.length-1;e++){s_points=this._splitPoints(c[e],c[e+1],b);for(var f=0;f<s_points.length;f++)d.push(s_points[f])}return d},MapPlatForm.Base.MapGeometry.prototype._splitPoints=function(a,b,c){var d=[a],e=this.map.getDistance(a,b,"4326"),f=0;c>0&&(f=Math.ceil(e/c));for(var g,h,i=1;f>i;i++){g=i/f;var j=parseFloat((b.lon-a.lon)*g)+parseFloat(a.lon),k=parseFloat((b.lat-a.lat)*g)+parseFloat(a.lat);h=new NPMapLib.Geometry.Point(j,k),d.push(h)}return d},MapPlatForm.Base.MapGeometry.prototype._getShortPointsInLine=function(a,b){for(var c=[],d=0;d<a.length;d++){var e=null;e=a[d].longitude&&a[d].latitude?new NPMapLib.Geometry.Point(a[d].longitude,a[d].latitude):a[d].getPosition();var f=b.getPath(),g=999999,h=null;sline=null;for(var i=0;i<f.length-1;i++){var j=this._getSibgleShortPointInLine(e.lon,e.lat,f[i].lon,f[i].lat,f[i+1].lon,f[i+1].lat),k=this._calculateEuclideanDistance(j.lon,j.lat,e.lon,e.lat);g>k&&(g=k,h=j,sline=new NPMapLib.Geometry.Polyline([f[i],f[i+1]]))}c.push({key:d,shortPoint:h,data:a[d]})}return c},MapPlatForm.Base.MapGeometry.prototype._findShortestMarker=function(a,b,c,d){for(var e=[],f=0;f<a.length;f++){var g=a[f].shortPoint,h=this.map.getDistance(g,b,"4326"),i=this.map.getDistance(g,c,"4326"),j=this.map.getDistance(b,c,"4326");d/2>h&&(i*i>h*h+j*j&&(h=0-h),e.push({distance:h,marker:a[f].data}))}e=e.sort(function(a,b){return a.distance-b.distance});for(var k=[],f=e.length-1;f>=0;f--)k.push(e[f].marker);return k},MapPlatForm.Base.MapGeometry.prototype._getSibgleShortPointInLine=function(a,b,c,d,e,f){var g,h,i;if(g=this._calculateEuclideanDistance(c,d,e,f),h=this._calculateEuclideanDistance(c,d,a,b),i=this._calculateEuclideanDistance(e,f,a,b),g===h+i)return new NPMapLib.Geometry.Point(a,b);if(1e-6>=g)return new NPMapLib.Geometry.Point(c,d);if(i*i>=g*g+h*h)return new NPMapLib.Geometry.Point(c,d);if(h*h>=g*g+i*i)return new NPMapLib.Geometry.Point(e,f);var j=(f-d)/(e-c),k=(a+(b-d)*j+j*j*c)/(j*j+1),l=j*k-j*c+d;return new NPMapLib.Geometry.Point(k,l)},MapPlatForm.Base.MapGeometry.prototype._calculateEuclideanDistance=function(a,b,c,d){var e=Math.sqrt((a-c)*(a-c)+(b-d)*(b-d));return e}}(),function(){MapPlatForm.Base.MapService=function(a,b){this.CLASS_NAME="MapService",this._currentService=null,this.map=a,this.mapConfig=new MapPlatForm.Base.MapConfig(b),this.mapGeometry=new MapPlatForm.Base.MapGeometry(this.map),this.routeService=null},MapPlatForm.Base.MapService.prototype.queryRoadByName=function(roadName,callBack){var url=this.mapConfig.dataServiceURL+"query/getRoadsByName",service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;params={roadName:roadName},this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadByNameService=service.query(url,params,function(result){for(var lines=[],i=result.length-1;i>=0;i--){var geometry=eval("("+result[i].feature+")"),obj={},line=GeoJSON.read(geometry);if(line instanceof Array)for(var j=line.length-1;j>=0;j--)line[j].data=result[i];else line.data=result[i];obj.name=result[i].name,obj.geometry=line,lines.push(obj)}callBack instanceof Function&&callBack(lines)})},MapPlatForm.Base.MapService.prototype.getGeometryBuffer=function(a,b,c){var d=this.mapConfig.dataServiceURL+"gis/buffer",e=new NPMapLib.Services.bufferParams;e.projection=this.map.getProjection(),e.distance=b,e.units="m",e.geometry=a;var f=new NPMapLib.Services.BufferService(this.map,NPMapLib.MAPTYPE_NPGIS);this.geometryBufferService&&(this.geometryBufferService.abort(),this.geometryBufferService=null),this.geometryBufferService=f.buffer(d,e,c)},MapPlatForm.Base.MapService.prototype.queryPOIByName=function(name,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;params.keyWord=name,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByNameService&&(this.queryPOIByNameService.abort(),this.queryPOIByNameService=null),this.queryPOIByNameService=service.query(url,params,function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMapLib.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result)})},MapPlatForm.Base.MapService.prototype.queryPOIByCoord=function(point,callBack){var url=this.mapConfig.dataServiceURL+"query/poicoord",service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;params={coord:point.lon+","+point.lat},this.queryPOIByCoordService&&(this.queryPOIByCoordService.abort(),this.queryPOIByCoordService=null),this.queryPOIByCoordService=service.query(url,params,function(result){var point;if(result&&result.geometry){var geometry=eval("("+result.geometry+")");("Point"===geometry.type||"point"===geometry.type)&&(point=new NPMapLib.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]),point.data=result)}callBack instanceof Function&&callBack(point)})},MapPlatForm.Base.MapService.prototype.addPOI=function(a,b){var c=this.mapConfig.dataServiceURL+"query/addPoi",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e={name:a.data.name,poiType:a.data.type,address:a.data.address,x:a.lon,y:a.lat},this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.addPOIService=d.updata(c,e,function(c){c?(a.data=c,b instanceof Function&&b(a)):b instanceof Function&&b(null)})},MapPlatForm.Base.MapService.prototype.updataPOI=function(a,b){var c=this.mapConfig.dataServiceURL+"query/updataPoi",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e={gid:a.data.gid,name:a.data.name,poiType:a.data.type,address:a.data.address,x:a.lon,y:a.lat},this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this.updataPOIService=d.updata(c,e,function(c){c?(a.data=c,b instanceof Function&&b(a)):b instanceof Function&&b(null)})},MapPlatForm.Base.MapService.prototype.queryPOIByGeometry=function(geometry,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.queryPOIByGeometryService=service.query(url,params,function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMapLib.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryPOIByFilter=function(filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.keyWord=fs[1],this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.queryPOIByFilterService=service.query(url,params,function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMapLib.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryPOIByGeometryAndFilter=function(geometry,filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),params=new NPMapLib.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.key=fs[1],this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryPOIByGeometryAndFilterService=service.query(url,params,function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMapLib.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryRoadCrossByName=function(a,b){var c=this.mapConfig.dataServiceURL+"query/getRoadCrossByName",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e.roadName=a,this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this.queryRoadCrossByNameService=d.query(c,e,function(a){for(var c=[],d=0;d<a.length;d++){var e=new NPMapLib.Geometry.Point(a[d].lon,a[d].lat);e.data=a[d],c.push(e)}b instanceof Function&&b(c)})},MapPlatForm.Base.MapService.prototype.queryRoadCrossByGeometry=function(a,b){var c=this.mapGeometry.getWKTByGeometry(a),d=this.mapConfig.dataServiceURL+"query/searchRoadCrossInBounds",e=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),f=new NPMapLib.Services.queryParams;f.wkt=c,this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryRoadCrossByGeometryService=e.query(d,f,function(a){for(var c=[],d=0;d<a.data.length;d++){var e=new NPMapLib.Geometry.Point(a.data[d].lon,a.data[d].lat);e.data=a.data,c.push(e)}b instanceof Function&&b(c)})},MapPlatForm.Base.MapService.prototype.queryAllFeaturesByName=function(a,b){var c=this.mapConfig.dataServiceURL+"query/getFOIByName",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e.keyWordString=a,this.queryFeaturesByNameService&&(this.queryFeaturesByNameService.abort(),this.queryFeaturesByNameService=null),this.queryFeaturesByNameService=d.query(c,e,function(a){for(var c=new MapPlatForm.Base.MapGeometry(this.map),d=[],e=0;e<a.length;e++){var f=null,g={};"road"===a[e].type?(g.address=a[e].address,f=c.getGeometryByGeoJson(a[e].feature)):"poi"===a[e].type?(g.address=a[e].address,f=c.getGeometryByGeoJson(a[e].wkt)):f=c.getGeometryByGeoJson(a[e].wkt),g.name=a[e].name,g.type=a[e].type,g.geometry=f,d.push(g)}b instanceof Function&&b(d)})},MapPlatForm.Base.MapService.prototype.addRoadCross=function(a){var b=this.mapConfig.dataServiceURL+"query/addRoadCross",c=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),d=new NPMapLib.Services.queryParams;d={name:a.data.name,x:a.lon,y:a.lat},this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.addRoadCrossService=c.updata(b,d,function(a){var b;a?(b.data=a,callBack instanceof Function&&callBack(b)):callBack instanceof Function&&callBack(null)})},MapPlatForm.Base.MapService.prototype.updataRoadCross=function(a){var b=this.mapConfig.dataServiceURL+"query/updataRoadCross",c=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),d=new NPMapLib.Services.queryParams;d={gid:a.data.gid,name:a.data.name,x:a.lon,y:a.lat},this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this._updataRoadCrossService=c.updata(b,d,function(a){var b;a?(b.data=a,callBack instanceof Function&&callBack(b)):callBack instanceof Function&&callBack(null)})},MapPlatForm.Base.MapService.prototype.searchRouteByCoor=function(a,b){var c=new NPMapLib.Services.RouteService(this.map,7),d=new NPMapLib.Services.routeParams;d.service="na",d.request="getroute",d.networkName="shanghai_roadnet_supermap",d.startStop=a.startStop,d.endStop=a.endStop,d.trafficModel=a.trafficModel,d.planRoadType=a.planRoadType,d.geoBarriers=[],d.algorithm="Dijkstra",this.routeService&&(this.routeService.abort(),this.routeService=null),this.routeService=c.route(this.mapConfig.dataServiceURL+"/gis/na",d,b)},MapPlatForm.Base.MapService.prototype.searchRouteByMultiPoints=function(a,b,c){for(var d=new NPMapLib.Services.RouteService(this.map,7),e=new NPMapLib.Services.routeParams,f="",g=0;g<a.length;g++)f+=a[g].lon+","+a[g].lat+";";f=f.substr(0,f.length-1),e.stops=f,this.routeMultiService&&(this.routeMultiService.abort(),this.routeMultiService=null),this.routeMultiService=d.routeByMultPoints(this.mapConfig.dataServiceURL+"/gis/routing",e,function(a){var c=[];if(a&&a instanceof Array)for(var d=0;d<a.length;d++){for(var e=[],f=a[d].expend.split(";"),g=0;g<f.length;g++){var h=f[g].split(","),i=parseFloat(h[0]),j=parseFloat(h[1]);e.push(new NPMapLib.Geometry.Point(i,j))}var k=new NPMapLib.Geometry.Polyline(e);k.setData({length:a[d].length,start:new NPMapLib.Geometry.Point(parseFloat(a[d].start.split(",")[0]),parseFloat(a[d].start.split(",")[1])),end:new NPMapLib.Geometry.Point(parseFloat(a[d].end.split(",")[0]),parseFloat(a[d].end.split(",")[1]))}),c.push(k)}b(c)},c)},MapPlatForm.Base.MapService.prototype.searchDistrictsByID=function(a,b){var c=this.mapConfig.dataServiceURL+"query/getRegionalBound",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e={addvcd:a},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var f=this;this._searchDistrictsService=d.query(c,e,function(a){if(a){if(a.geometry&&(a.geometry=f.mapGeometry.getGeometryByGeoJson(a.geometry)),a.districts)for(var c=0;c<a.districts.length;c++)a.districts[c].geometry&&(a.districts[c].geometry=f.mapGeometry.getGeometryByGeoJson(a.districts[c].geometry));b instanceof Function&&b(a)}else b instanceof Function&&b(null)})},MapPlatForm.Base.MapService.prototype.searchDistrictsByName=function(a,b){var c=this.mapConfig.dataServiceURL+"query/getRegionalBoundByName",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e={name:a},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var f=this;this._searchDistrictsService=d.query(c,e,function(a){if(a){if(a.geometry&&(a.geometry=f.mapGeometry.getGeometryByGeoJson(a.geometry)),a.districts)for(var c=0;c<a.districts.length;c++)a.districts[c].geometry&&(a.districts[c].geometry=f.mapGeometry.getGeometryByGeoJson(a.districts[c].geometry));b instanceof Function&&b(a)}else b instanceof Function&&b(null)})},MapPlatForm.Base.MapService.prototype.searchPanoramaByPoint=function(a,b,c){var d=this.mapConfig.dataServiceURL+"panorama/getConfigsByPosition",e=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),f=new NPMapLib.Services.queryParams;f={position:this.mapGeometry.getWKTByGeometry(a),distance:b},this._searchPanoramaByPoint&&(this._searchPanoramaByPoint.abort(),this._searchPanoramaByPoint=null);this._searchPanoramaByPoint=e.query(d,f,function(a){a?c instanceof Function&&c(a):c instanceof Function&&c(null)})},MapPlatForm.Base.MapService.prototype.searchSNPanoramaPoints=function(a){var b=this.mapConfig.dataServiceURL+"panorama/getAllSnPoints",c=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),d=new NPMapLib.Services.queryParams;d={},this._searchSNPanoramaPoints&&(this._searchSNPanoramaPoints.abort(),this._searchSNPanoramaPoints=null);this._searchSNPanoramaPoints=c.query(b,d,function(b){b?a instanceof Function&&a(b):a instanceof Function&&a(null)})},MapPlatForm.Base.MapService.prototype.searchSNPanoramaByPointID=function(a,b){var c=this.mapConfig.dataServiceURL+"panorama/getSnConfigsByParentId",d=new NPMapLib.Services.QueryService(NPMapLib.MAPTYPE_NPGIS),e=new NPMapLib.Services.queryParams;e={parentid:a},this._searchSNPanoramaByPointID&&(this._searchSNPanoramaByPointID.abort(),this._searchSNPanoramaByPointID=null);this._searchSNPanoramaByPointID=d.query(c,e,function(a){a?b instanceof Function&&b(a):b instanceof Function&&b(null)})},MapPlatForm.Base.MapService.prototype.cancelService=function(){this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this.routeService&&(this.routeService.abort(),this.routeService=null)}}(),function(){MapPlatForm.Base.MapTag=function(a,b){this.CLASS_NAME="MapTag",this.layer=b,this.map=a,this._activeMarker=null,this.callback=null,this._mapGeometry=new MapPlatForm.Base.MapGeometry(a),this.markerParam=null,this.layer||(this.layer=this.map.getDefaultLayer()),self=this,this.isclick=!1},MapPlatForm.Base.MapTag.prototype._clickCallBack=function(){if(self.isclick||!self._activeMarker)return void self.delAdrawMarker();self.isclick=!0;var a=self._mapGeometry.createMarker(self._activeMarker.getPosition(),self.markerParam);self.layer.addOverlay(a),self.delAdrawMarker(),self.callback&&self.callback instanceof Function&&self.callback(a)},MapPlatForm.Base.MapTag.prototype._moveCallBack=function(a){var b=a.object,c=b.getLonLatFromPixel(a.xy),d=new NPMapLib.Geometry.Point(c.lon,c.lat);d=NPMapLib.T.getPoint(self.map,d),self._activeMarker?self.isclick||self._activeMarker.setPosition(d):(self._activeMarker=self._mapGeometry.createMarker(d,self.markerParam),self.layer.addOverlay(self._activeMarker),self._activeMarker.addEventListener("click",self._clickCallBack),self._activeMarker.addEventListener("rightclick",function(){self._activeMarker&&(self.delAdrawMarker(),self.cancelCallback&&self.cancelCallback())}))},MapPlatForm.Base.MapTag.prototype._rigthClickCallBack=function(){self._activeMarker&&(self.delAdrawMarker(),self.cancelCallback&&self.cancelCallback())},MapPlatForm.Base.MapTag.prototype.adrawMarker=function(a,b,c,d,e){this.markerParam=a,this.isclick=!1,this.delAdrawMarker(),this.callback=b,this.cancelCallback=e,this.layer.removeOverlay(this._activeMarker),this._activeMarker=null,this._contextHeight="20px","EN"===NPMapLib.CULTURE?(this.map.activateMouseContext("Click on add annotations, right click to cancel"),this._contextHeight="34px"):this.map.activateMouseContext("点击添加标注,右键取消");var f=this.map.getMouseContextStyle();if(f.height=this._contextHeight,c&&this.map.activateMouseContext(c),d)for(var g in d)f[g]=d[g];self=this,this.map.addEventListener(NPMapLib.MAP_EVENT_MOUSE_MOVE,function(a){self._activeMarker?self._activeMarker.setPosition(a):(self._activeMarker=self._mapGeometry.createMarker(a,self.markerParam),self.layer.addOverlay(self._activeMarker),self._activeMarker.addEventListener("click",self._clickCallBack),self._activeMarker.addEventListener("rightclick",function(){self._activeMarker&&(self.delAdrawMarker(),this.cancelCallback&&this.cancelCallback())}))}),this.map.addEventListener(NPMapLib.MAP_EVENT_RIGHT_CLICK,function(){self._activeMarker&&(self.delAdrawMarker(),this.cancelCallback&&this.cancelCallback())}),this.map.addEventListener(NPMapLib.MAP_EVENT_CLICK,self._clickCallBack),this.map.getContainer().onmouseenter=function(){self._activeMarker&&self._activeMarker.show()},this.map.getContainer().onmouseleave=function(){self._activeMarker&&self._activeMarker.hide()}},MapPlatForm.Base.MapTag.prototype.delAdrawMarker=function(){this.map&&(this._activeMarker&&(this._activeMarker.removeEventListener("click"),this._activeMarker.removeEventListener("rightclick")),this.layer.removeOverlay(this._activeMarker),this.map.deactivateMouseContext(),this.map.removeEventListener(NPMapLib.MAP_EVENT_CLICK),this.map.removeEventListener(NPMapLib.MAP_EVENT_MOUSE_OVER),this.map.removeEventListener(NPMapLib.MAP_EVENT_MOUSE_OUT),this.map.removeEventListener(NPMapLib.MAP_EVENT_MOUSE_MOVE))}}(),function(){MapPlatForm.Base.MapTools=function(a,b){this.CLASS_NAME="MapTools",this.map=a,this.measureTool=null,this.drawTool=null,this.searchCircle=null,this.editMarker=null,this.layer=b},MapPlatForm.Base.MapTools.prototype._initMeasureTool=function(){this.measureTool=new NPMapLib.Tools.MeasureTool(this.map.id,{lengthUnit:NPMapLib.MAP_UNITS_METERS,areaUnit:NPMapLib.MAP_UNITS_SQUARE_KILOMETERS,mode:NPMapLib.MEASURE_MODE_DISTANCE})},MapPlatForm.Base.MapTools.prototype._getStyle=function(a){return a?a.cursor="crosshair":a={cursor:"crosshair"},a},MapPlatForm.Base.MapTools.prototype._initDrawTool=function(){this.drawTool=new NPMapLib.Tools.DrawingTool(this.map.id),this.map.MapTools=this},MapPlatForm.Base.MapTools.prototype.measureDistance=function(){this.measureTool||this._initMeasureTool(),this.cancelMeasure(),this.cancelDraw(),this.measureTool.startUp(),this.measureTool.setMode(NPMapLib.MEASURE_MODE_DISTANCE)},MapPlatForm.Base.MapTools.prototype.measureArea=function(){this.measureTool||this._initMeasureTool(),this.cancelMeasure(),this.cancelDraw(),this.measureTool.startUp(),this.measureTool.setMode(NPMapLib.MEASURE_MODE_AREA)},MapPlatForm.Base.MapTools.prototype.cancelMeasure=function(){if(this.measureTool)try{this.measureTool.stop()}catch(a){this.measureTool._stop()}},MapPlatForm.Base.MapTools.prototype.cancelDraw=function(){this.drawTool&&this.drawTool.cancel()},MapPlatForm.Base.MapTools.prototype.drawLine=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_POLYLINE,a,b)},MapPlatForm.Base.MapTools.prototype.drawRectangle=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_RECT,a,b)},MapPlatForm.Base.MapTools.prototype.drawCircle=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_CIRCLE,a,b)},MapPlatForm.Base.MapTools.prototype.drawPolygon=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_POLYLGON,a,b)},MapPlatForm.Base.MapTools.prototype.drawCircleByDiameter=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_CIRCLE_DIAMETER,a,b)},MapPlatForm.Base.MapTools.prototype.drawFreehand=function(a,b){this.drawTool||this._initDrawTool(),this.cancelMeasure,this.cancelDraw,this.drawTool.startUp(a),b=this._getStyle(b),this.drawTool.setMode(NPMapLib.DRAW_MODE_FREEHAND,a,b)},MapPlatForm.Base.MapTools.prototype.addCircleSearchControlByRadius=function(a,b,c,d,e,f){this.opts=f||{};var g=this.opts.showRadius?this.opts.showRadius:!1,h=this.opts.iconUrl?this.opts.iconUrl:OpenLayers.Util.getImageLocation("yuandian.png"),i=this.opts.lineColor?this.opts.lineColor:"#acb9d1",j=this.opts.lineWidth?this.opts.lineWidth:2,k=this.opts.lineStyle?this.opts.lineStyle:NPMap.LINE_TYPE_SOLID,l=this.opts.labelSize?this.opts.labelSize:14,m=this.opts.lineColor?this.opts.lineColor:"red",n=this.opts.fillColor?this.opts.fillColor:"#6980bc",o=this.opts.strokeColor?this.opts.strokeColor:"#acb9d1",p=this.opts.markerSize?this.opts.markerSize:[32,32],q=this,r=1e3,s="米";"EN"===NPMapLib.CULTURE&&(s="M"),c?r=c:c=500,d||(d=5e3),e&&e>=c&&d>=e&&(r=e);var t=b;this.searchCircle=new NPMapLib.Geometry.Circle(a,r,{color:o,fillColor:n,side:300,weight:2,opacity:1,fillOpacity:.2});var u=this.map.getDefaultLayer(),v=this.layer?this.layer:this.map.getDefaultLayer();v.addOverlay(this.searchCircle);var w=new NPMapLib.Geometry.Size(p[0],p[1]),x=new NPMapLib.Symbols.Icon(h,w),y=a.lon+(new NPMapLib.GisToolKit).getDistanceByProjection(r,this.map),z=new NPMapLib.Geometry.Point(y,a.lat);this.editMarker=new NPMapLib.Symbols.Marker(z),this.editMarker.setOffset(new NPMapLib.Geometry.Size(0,-p[0]/2)),this.editMarker.setIcon(x),this.map.enableEditing(),g&&(this.line=new NPMapLib.Geometry.Polyline([a,z],{color:i,weight:j,lineStyle:k}),u.addOverlay(this.line),this.label=new NPMapLib.Symbols.Label("r = "+r+s,{offset:new NPMapLib.Geometry.Size(0,20)}),this.label.setStyle({fontSize:l,color:m}),this.label.setPosition(new NPMapLib.Geometry.Point((a.lon+z.lon)/2,a.lat)),u.addOverlay(this.label)),this.editMarker.enableEditing(),this.editMarker.isEnableEdit=!0,u.addOverlay(this.editMarker),this.editMarker.addEventListener("featuremousedown",function(){A=!0,B=!0
});var A=!1,B=!1,C=!1;this.editMarker.addEventListener("mouseover",function(){C||(C=!0),B=!0}),this.editMarker.addEventListener("mouseout",function(){A||C&&(C=!1),B=!1}),this.editMarker.addEventListener("draging",function(b){var e=b.getPosition(),f=q.map.getDistance(e,a);if(c>f){var g=a.lon+(e.lon-a.lon)*c/f,h=a.lat+(e.lat-a.lat)*c/f;e=new NPMap.Geometry.Point(g,h),f=c}else if(f>d){var g=a.lon+(e.lon-a.lon)*d/f,h=a.lat+(e.lat-a.lat)*d/f;e=new NPMap.Geometry.Point(g,h),f=d}q.searchCircle.setRadius(f+2),q.searchCircle.refresh(),b.setPosition(e),b.refresh(),q.line&&q.line.setPositionAt(q.line._points.length-1,e),q.label&&(q.label.setContent("r = "+Math.round(f)+s),q.label.setPosition(new NPMapLib.Geometry.Point((a.lon+e.lon)/2,(a.lat+e.lat)/2)),q.label.refresh())}),this.editMarker.addEventListener("dragend",function(){B||C&&(C=!1),t&&t instanceof Function&&t(q.searchCircle),A=!1}),t&&t instanceof Function&&t(q.searchCircle)},MapPlatForm.Base.MapTools.prototype.disableEditingCicleSearch=function(){this.editMarker&&(this.editMarker.disableEditing(),this.editMarker.hide())},MapPlatForm.Base.MapTools.prototype.enableEditingCicleSearch=function(){this.editMarker&&(this.editMarker.enableEditing(),this.editMarker.show())},MapPlatForm.Base.MapTools.prototype.addCircleSearchControl=function(a,b,c,d,e){var f=this,g=1e3,h="米";"EN"===NPMapLib.CULTURE&&(h="M"),c?g=c:c=500,d||(d=5e3),e&&e>=c&&d>=e&&(g=e);var i=b;this.searchCircle=new NPMapLib.Geometry.Circle(a,g,{color:"#acb9d1",fillColor:"#6980bc",weight:2,opacity:1,fillOpacity:.2});var j=this.map.getDefaultLayer(),k=this.layer?this.layer:this.map.getDefaultLayer();k.addOverlay(this.searchCircle);var l=new NPMapLib.Geometry.Size(76,24),m=OpenLayers.Util.getImageLocation("editCircle.png"),n=new NPMapLib.Symbols.Icon(m,l);this.editMarker=new NPMapLib.Symbols.Marker(a),this.editMarker.setIcon(n),this.editMarker.setOffset(new NPMapLib.Geometry.Size(20,-12));var o=new NPMapLib.Symbols.Label(g+h,{offset:new NPMapLib.Geometry.Size(-2,-12)});o.setStyle({fontSize:12,fontFamily:"宋体",align:"left"}),this.editMarker.setLabel(o);var p=this.searchCircle.getCenter();p.lon=p.lon+(new NPMapLib.GisToolKit).getDistanceByProjection(g,this.map),this.editMarker.setPosition(p),this.map.enableEditing(),this.editMarker.enableEditing(),this.editMarker.isEnableEdit=!0,j.addOverlay(this.editMarker),this.editMarker.addEventListener("featuremousedown",function(){q=!0,r=!0});var q=!1,r=!1,s=!1;this.editMarker.addEventListener("mouseover",function(){s||(f.map.enableEditing(),s=!0),r=!0}),this.editMarker.addEventListener("mouseout",function(){q||s&&(f.map.disableEditing(),s=!1),r=!1});var t=new NPMapLib.GisToolKit;this.editMarker.addEventListener("draging",function(a){var b=f.searchCircle.getCenter(),e=a.getPosition().lon-f.searchCircle.getCenter().lon;e=(new NPMapLib.GisToolKit).getPlatDistanceByProjection(e,f.map),c>e?(b.lon=b.lon+t.getDistanceByProjection(c,f.map),e=c):e>d?(b.lon=b.lon+t.getDistanceByProjection(d,f.map),e=d):b.lon=a.getPosition().lon;var g=a.getLabel();g.setContent(Math.round(e)+h),a.setLabel(g),f.searchCircle.setRadius(e),f.searchCircle.refresh(),a.setPosition(b),a.refresh()}),this.editMarker.addEventListener("dragend",function(){r||s&&(f.map.disableEditing(),s=!1),i&&i instanceof Function&&i(f.searchCircle);var a=f.searchCircle.getExtent();f.map.zoomToExtent(a),q=!1}),this.map.disableEditing();var u=f.searchCircle.getExtent();this.map.zoomToExtent(u),i&&i instanceof Function&&i(f.searchCircle)},MapPlatForm.Base.MapTools.prototype.setCircleSearchControlRadius=function(a,b){if(this.searchCircle&&this.editMarker){var c="米";"EN"===NPMapLib.CULTURE&&(c="M");var d=this.searchCircle.getCenter();d.lon=d.lon+(new NPMapLib.GisToolKit).getDistanceByProjection(a,this.map),this.editMarker.setPosition(d),this.searchCircle.setRadius(a);var e=this.editMarker.getLabel();e.setContent(Math.round(a)+c),this.editMarker.setLabel(e),this.editMarker.refresh();var f=this.searchCircle.getExtent();this.map.zoomToExtent(f),b&&b instanceof Function&&b(self.searchCircle)}},MapPlatForm.Base.MapTools.prototype.removeCircleSearchControl=function(){this.editMarker&&(this.editMarker.removeEventListener("dragend"),this.editMarker.removeEventListener("draging"),this.editMarker.removeEventListener("mouseout"),this.editMarker.removeEventListener("mouseover"),this.editMarker.removeEventListener("featuremousedown"));var a=this.map.getDefaultLayer(),b=this.layer?this.layer:this.map.getDefaultLayer();this.label&&(a.removeOverlay(this.label),this.label=null),this.line&&(a.removeOverlay(this.line),this.line=null),this.editMarker&&(a.removeOverlay(this.editMarker),this.editMarker=null),this.searchCircle&&(b.removeOverlay(this.searchCircle),this.searchCircle=null),this.map.disableEditing()}}(),function(a,b){"use strict";var c=function(a){this._init(a),this._initCanvas()};c.prototype.drawLines=function(a){if(a){var b,c,d,e,f=0,g=a.length;for(this._data=new Array(g),this._pointsOnLines=[],f;g>f;f++)b=a[f],c=b.geometry.coordinates,d=this._adjustmentOfStyle(b.style),e=this._dataConversion([c],d.degree,d.lineCurve),this._data[f]={geometry:{coordinates:e},style:d};this._reDrawLines(),this._animatePoint()}},c.prototype.enableFlow=function(){if(this._animatePointFlag=!0,this._pointsOnLines&&0===this._pointsOnLines.length){var a,b,c=0,d=this._data.length;for(c;d>c;c++)a=this._data[c].geometry.coordinates[0],b=this._data[c].style,this._getPointsOnLines(b.lineCurve,a)}this._animatePoint()},c.prototype.disableFlow=function(){this._animatePointFlag=!1,this._anictx.clearRect(0,0,this._anictx.canvas.width,this._anictx.canvas.height)},c.prototype.remove=function(){this._clearDrawLines(),this._anictx.clearRect(0,0,this._anictx.canvas.width,this._anictx.canvas.height),a.cancelAnimationFrame(this._animatePointHandler),this._animatePointHandler=null,this._data=null,this._pointsOnLines=null},c.prototype.destroy=function(){this.remove(),this._baseMask.removeFromMap(),this._animateMask.removeFromMap(),this._endPointMask.removeFromMap(),this._removeMapEventFlag=!0},c.prototype._dataConversion=function(a,b,c){var d,e,f=0,g=a.length,h=new Array(g);for(f;g>f;f++)d=a[f],e=c?this._createShapePointsForCurve(d,b):this._createShapePoints(d),h[f]=e,this._animatePointFlag&&this._getPointsOnLines(c,e);return h},c.prototype._getPointsOnLines=function(a,b){this._pointsOnLines.push(a?this._getPointsOnCurve(b):this._getPointsOnLine(b)),this._aniIndex.push(0)},c.prototype._adjustmentOfStyle=function(a){var c=a&&a.line||{},d=a&&a.startPoint||{},e=a&&a.endPoint||{},f=!0,g=!0,h=!0;c.curve||c.curve===b||(f=c.curve),d.show||d.show===b||(g=d.show),e.show||e.show===b||(h=e.show);var i={startPointSize:d.size||4,startPointColor:d.color||"rgba(255,0,0,1)",startPointShow:g,endPointSize:e.size||4,endPointColor:e.color||"rgba(255,0,0,1)",endPointShow:h,lineColor:c.color||"rgba(255,0,0,1)",lineFillColor:c.fillColor||"rgba(255,0,0,1)",lineWidth:c.width||1,lineCurve:f,degree:c.degree||MAPPLATFORM_BASE_CURVE_ANIM_30};return this._flowColor=c.flowColor||"rgba(255,255,255,1)",this._flowSize=c.flowSize||2,i},c.prototype._doDrawLines=function(a,b){var c,d,e,f,g=a.length;for(this._ctx.beginPath(),this._setLineStyle(b),c=0;g>c;c++)d=a[c],e=d[0],f=this._trafficUtil.mercatorToPixel([e.lon,e.lat],this._ctx),this._ctx.moveTo(~~(.5+f.x),~~(.5+f.y)),b.lineCurve?this._doDrawCurve(d):this._doDrawLine(d),this._drawEndpoints(b,f,d);this._ctx.stroke(),this._endPointctx.stroke()},c.prototype._doDrawLine=function(a){var b,c,d=1,e=a.length;for(d;e>d;d++)b=a[d],c=this._trafficUtil.mercatorToPixel([b.lon,b.lat],this._ctx),this._ctx.lineTo(~~(.5+c.x),~~(.5+c.y))},c.prototype._doDrawCurve=function(a){var b=a[1],c=a[2],d=this._trafficUtil.mercatorToPixel([b.lon,b.lat],this._ctx),e=this._trafficUtil.mercatorToPixel([c.lon,c.lat],this._ctx);this._ctx.quadraticCurveTo(~~(.5+d.x),~~(.5+d.y),~~(.5+e.x),~~(.5+e.y))},c.prototype._drawEndpoints=function(a,b,c){var d,e;a.startPointShow&&this._drawEndpoint(b,a,"s"),a.endPointShow&&(d=c[c.length-1],e=this._trafficUtil.mercatorToPixel([d.lon,d.lat],this._ctx),this._drawEndpoint(e,a,"e"))},c.prototype._drawEndpoint=function(a,b,c){var d=4;"s"===c?(d=b.startPointSize,this._endPointctx.fillStyle=b.startPointColor,this._endPointctx.strokeStyle=b.startPointColor):(d=b.endPointSize,this._endPointctx.fillStyle=b.endPointColor,this._endPointctx.strokeStyle=b.endPointColor),this._endPointctx.beginPath(),this._endPointctx.arc(~~(.5+a.x),~~(.5+a.y),d/2,0,2*Math.PI,!0),this._endPointctx.closePath(),this._endPointctx.fill()},c.prototype._getPointsOnCurve=function(a){var b,c,d,e=[];for(b=0;1>=b;b+=.025)c=(1-b)*(1-b)*a[0].lon+2*(1-b)*b*a[1].lon+b*b*a[2].lon,d=(1-b)*(1-b)*a[0].lat+2*(1-b)*b*a[1].lat+b*b*a[2].lat,e.push([c,d]);return e},c.prototype._getPointsOnLine=function(a){var b=0,c=a.length,d=new Array(c);for(b;c>b;b++)d[b]=[a[b].lon,a[b].lat];return d},c.prototype._setLineStyle=function(a){this._ctx.strokeStyle=a.lineColor,this._ctx.fillStyle=a.lineFillColor,this._ctx.lineWidth=a.lineWidth},c.prototype._animatePoint=function(){if(this._animatePointFlag){var a,b,c,d,e,f=this._pointsOnLines&&this._pointsOnLines.length||0;for(this._anictx.clearRect(0,0,this._anictx.canvas.width,this._anictx.canvas.height),this._anictx.fillStyle=this._flowColor,a=0;f>a;a++)b=this._pointsOnLines[a],c=this._aniIndex[a],d=b[c],e=this._trafficUtil.mercatorToPixel(d,this._anictx),this._anictx.fillRect(e.x-1,e.y-1,this._flowSize,this._flowSize),this._aniIndex[a]++,this._aniIndex[a]>=b.length&&(this._aniIndex[a]=0);this._cycleAnimatePoint()}},c.prototype._cycleAnimatePoint=function(){var b=this;setTimeout(function(){b._animatePointHandler=a.requestAnimationFrame(function(){b._animatePoint()})},200)},c.prototype._init=function(a){this._initRaf(),this._npMap=a,this._olMap=a._mapAdapter.map,this._trafficUtil=new MapPlatForm.Base.TrafficUtil(a),this._animatePointHandler=0,this._data,this._pointsOnLines,this._aniIndex=[],this._animatePointFlag=!0,this._flowColor,this._flowSize,this._removeMapEventFlag=!1,this._bind()},c.prototype._initRaf=function(){for(var b=0,c=["webkit","moz"],d=0;d<c.length&&!a.requestAnimationFrame;++d)a.requestAnimationFrame=a[c[d]+"RequestAnimationFrame"],a.cancelAnimationFrame=a[c[d]+"CancelAnimationFrame"]||a[c[d]+"CancelRequestAnimationFrame"];a.requestAnimationFrame||(a.requestAnimationFrame=function(c){var d=(new Date).getTime(),e=Math.max(0,16-(d-b)),f=a.setTimeout(function(){c(d+e)},e);return b=d+e,f}),a.cancelAnimationFrame||(a.cancelAnimationFrame=function(a){clearTimeout(a)})},c.prototype._initCanvas=function(){this._baseMask=new MapPlatForm.Base.MapMask(this._npMap),this._ctx=this._baseMask.getContext(),this._ctx.translate(.5,.5),this._animateMask=new MapPlatForm.Base.MapMask(this._npMap),this._anictx=this._animateMask.getContext(),this._endPointMask=new MapPlatForm.Base.MapMask(this._npMap),this._endPointctx=this._endPointMask.getContext(),this._solveZoomLost()},c.prototype._solveZoomLost=function(){var a=this._trafficUtil.getBrowserType();if("IE"===a){var b=this;this._endPointctx.canvas.addEventListener("mousewheel",function(a){b._npMap._mapAdapter._navigator.handlers.wheel.onWheelEvent(a,!0)},!1)}},c.prototype._bind=function(){var a=this;this._npMap.addEventListener("movestart",function b(){a._clearDrawLines(),a._removeMapEventFlag&&a._npMap.removeEventListener("movestart",b)}),this._npMap.addEventListener("moveend",function c(){a._resizeCanvas(),a._reDrawLines(),a._removeMapEventFlag&&a._npMap.removeEventListener("moveend",c)}),this._npMap.addEventListener(NPMapLib.MAP_EVENT_ZOOM_START,function d(){a._clearDrawLines(),a._removeMapEventFlag&&a._npMap.removeEventListener("zoomstart",d)})},c.prototype._clearDrawLines=function(){this._ctx.clearRect(0,0,this._ctx.canvas.width,this._ctx.canvas.height),this._endPointctx.clearRect(0,0,this._endPointctx.canvas.width,this._endPointctx.canvas.height)},c.prototype._resizeCanvas=function(){var a=this._npMap.getSize();this._ctx.canvas.width=a.width,this._ctx.canvas.height=a.height,this._endPointctx.canvas.width=a.width,this._endPointctx.canvas.height=a.height,this._anictx.canvas.width=a.width,this._anictx.canvas.height=a.height},c.prototype._reDrawLines=function(){if(this._data){var a,b,c,d=0,e=this._data.length;for(d;e>d;d++)a=this._data[d],b=a.geometry.coordinates,c=a.style,this._doDrawLines(b,c)}},c.prototype._createShapePoints=function(a){var b,c=0,d=a.length,e=new Array(d);for(c;d>c;c++)b=this._trafficUtil.wgs84ToMercator(a[c]),e[c]=b;return e},c.prototype._createShapePointsForCurve=function(a,b){var c=new Array(3),d=a.length,e=this._trafficUtil.wgs84ToMercator(a[0]),f=this._trafficUtil.wgs84ToMercator(a[d-1]),g=this._createControlPoint(e,f,b);return c[0]=e,c[1]=g,c[2]=f,c},c.prototype._createControlPoint=function(a,b,c){var d=a.lon,e=a.lat,f=b.lon,g=b.lat,h=(d+f)/2,i=(e+g)/2,j=(e-g)*c+h,k=(f-d)*c+i;return{lon:j,lat:k}},a.MapPlatForm.Base.MapCurve=c}(window),function(a){"use strict";var b=function(a){this._npMap=a,this._olMap=a._mapAdapter.map};b.prototype.wgs84ToMercator=function(a){return NPMap.T.setPoint(this._npMap,new NPMapLib.Geometry.Point(a[0],a[1]))},b.prototype.mercatorToPixel=function(a,b){var c=this.getRes(),d=this._olMap.getCenter(),e=new NPMapLib.Geometry.Pixel(d.lon-b.canvas.width/2*c,d.lat+b.canvas.height/2*c),f=(a[0]-e.x)/c,g=(e.y-a[1])/c;return{x:f,y:g}},b.prototype.getRes=function(){var a=this._olMap.getZoom();return this._olMap.getResolutionForZoom(a)},b.prototype.getBrowserType=function(){var a=navigator.userAgent,b=a.indexOf("Opera")>-1;return b?"Opera":a.indexOf("Firefox")>-1?"FF":a.indexOf("Chrome")>-1?"Chrome":a.indexOf("Safari")>-1?"Safari":a.indexOf("compatible")>-1&&a.indexOf("MSIE")>-1&&!b?"IE":void 0},a.MapPlatForm.Base.TrafficUtil=b}(window),function(a){"use strict";var b=function(a){this._npMap=a,this._olMap=a._mapAdapter.map,this._canvas,this._addToMap()};b.prototype._addToMap=function(){this._canvas=this._createMapMask(),this._olMap.viewPortDiv.appendChild(this._canvas)},b.prototype._createMapMask=function(){var a=document.createElement("canvas"),b=this._npMap.getSize();return a.width=b.width,a.height=b.height,a.style.cssText="position:absolute;left:0;top:0;width:100%;height:100%;z-index:999;",a.style.pointerEvents="none",a},b.prototype.getCanvas=function(){return this._canvas},b.prototype.getContext=function(){return this._canvas.getContext?this._canvas.getContext("2d"):void alert("浏览器不支持 canvas!")},b.prototype.removeFromMap=function(){this._olMap.viewPortDiv.removeChild(this._canvas)},a.MapPlatForm.Base.MapMask=b}(window),MAPPLATFORM_BASE_CURVE_15=.1,MAPPLATFORM_BASE_CURVE_30=.2,MAPPLATFORM_BASE_CURVE_45=.3,MAPPLATFORM_BASE_CURVE_60=.4,MAPPLATFORM_BASE_CURVE_75=.5,MAPPLATFORM_BASE_CURVE_95=.6,MAPPLATFORM_BASE_CURVE_105=.7,MAPPLATFORM_BASE_CURVE_REVERSE_15=-.1,MAPPLATFORM_BASE_CURVE_REVERSE_30=-.2,MAPPLATFORM_BASE_CURVE_REVERSE_45=-.3,MAPPLATFORM_BASE_CURVE_REVERSE_60=-.4,MAPPLATFORM_BASE_CURVE_REVERSE_75=-.5,MAPPLATFORM_BASE_CURVE_REVERSE_95=-.6,MAPPLATFORM_BASE_CURVE_REVERSE_105=-.7,function(){MapPlatForm.Base.MapRoutePlan=function(a,b,c){this.CLASS_NAME="MapRoutePlan",this.map=a,this.layer=b?b:this.map.getDefaultLayer(),this.startMarker=null,this._planRoadType=1,this._currentPolyline=null,this.endMarker=null,this.editMarker=null,this._throughMarkerInfo={},this._routes=[],this._throughMarkerNum=0,this._routeIndex=0,this.dataServiceURL=c,this._mapService=new MapPlatForm.Base.MapService(a,c),this._mapGeometry=new MapPlatForm.Base.MapGeometry(a),this.result1=null,this.result2=null,this._researchCallback=function(){},this.routeGroup=this.layer.addGroup("route"),this.editGroup=this.layer.addGroup("route_edit"),this.defaultName="未知地址","EN"===NPMapLib.CULTURE&&(this.defaultName="Unknown Position"),window.getElementsByClassName=function(a){for(var b=[],c=document.getElementsByTagName("*"),d=0;d<c.length;d++)c[d].className===a&&(b[b.length]=c[d]);return b}},MapPlatForm.Base.MapRoutePlan.prototype._addPath=function(a){if(a.features.length<1||a.features.length<1)return void(this._errorCallBack&&this._errorCallBack instanceof Function&&this._errorCallBack("EN"===NPMapLib.CULTURE?"There is no suitable route, please re-drag！":"没有合适的路线，请重新拖动！"));var b=a.features[0];b.setStyle({color:"red",weight:5}),this.routeGroup.addOverlay(b),b.setZIndex(0);var c=this;b.addEventListener("featuremoving",function(a,b){var d=new MapPlatForm.Base.MapGeometry(map);c.editMarker?(c.editMarker.setPosition(b),c.editMarker.show()):(c.editMarker=d.createMarker(b,{url:c._crossMarkerStyle.editImageUrl,size:{width:c._crossMarkerStyle.width,height:c._crossMarkerStyle.height},markerType:0}),c.editGroup.addOverlay(c.editMarker),c.editMarker.setZIndex(100),c.editMarker.isEnableEdit=!0,c.editMarker.enableEditing())})},MapPlatForm.Base.MapRoutePlan.prototype.addEidtMarkerEvent=function(){this.editMarker&&this.editMarker.addEventListener(NPMapLib.MARKER_EVENT_DRAG_END,function(a){_afterDrag(a)})},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(a,b,c){var d=null,e=null,f=null;b?(e=b.startPosition,f=b.stopPosition):(d=_currentPolyline.getPath(),e=d[0],f=d[d.length-1]);var g={startStop:e,endStop:a,trafficModel:"car",planRoadType:this._planRoadType},h=this;_mapService.searchRouteByCoor(g,function(d){g={startStop:a,endStop:f,trafficModel:"car",planRoadType:_planRoadType},_mapService.searchRouteByCoor(g,function(g){return d.features.length<1||g.features.length<1?void(h._errorCallBack&&h._errorCallBack instanceof Function&&h._errorCallBack("EN"==NPMapLib.CULTURE?"There is no suitable route, please re-drag！":"没有合适的路线，请重新拖动！")):void _addRoutes(a,{result1:d,result2:g,startPosition:e,stopPosition:f,throughMarkerRelativeInfo:b,key:c})})})},MapPlatForm.Base.MapRoutePlan.prototype._addRoutes=function(a){this.routeGroup.addOverlay(a.features[0])},MapPlatForm.Base.MapRoutePlan.prototype._clearEditInfoOnMap=function(){this.editGroup&&(this.editGroup.removeAllOverlays(),this.editMarker=null),this.routeGroup&&(this.routeGroup.removeAllOverlays(),this.editMarker=null),this.map.closeAllInfoWindows()},MapPlatForm.Base.MapRoutePlan.prototype._queryRoute=function(){var a={startStop:this._startMarker.getPosition(),endStop:this._endMarker.getPosition(),trafficModel:"car",planRoadType:this._planRoadType},b=this;this._mapService.searchRouteByCoor(a,function(a){if(a.features.length<1)return void(b._errorCallBack&&b._errorCallBack instanceof Function&&b._errorCallBack("EN"===NPMapLib.CULTURE?"There is no suitable route, please re-drag！":"没有合适的路线，请重新拖动！"));var c=a.features[0];b._setPolylineStyle([c]),b.routeGroup.addOverlay(c),c.setData({index:b._routeIndex}),c.setZIndex(0),b._routeArray=[c],b._addEventToLines([c]),b._routes=[{index:b._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:c,routeInfo:a.messages}];var d={routes:b._routes,polyline:c},e=c.getPath(),f=[b._startMarker.getPosition(),e[0]],g=[e[e.length-1],b._endMarker.getPosition()],h=new NPMapLib.Geometry.Polyline(f,{strokeColor:b._lineStyle.color,weight:3,lineStyle:NPMapLib.LINE_TYPE_DASH}),i=new NPMapLib.Geometry.Polyline(g,{strokeColor:b._lineStyle.color,weight:3,lineStyle:NPMapLib.LINE_TYPE_DASH});b.routeGroup.addOverlays([h,i]),h.setZIndex(0),i.setZIndex(0),b._researchCallback(d)})},MapPlatForm.Base.MapRoutePlan.prototype._addEventToLines=function(a){var b=this;if(a&&a instanceof Array)for(var c=a.length,d=0;c>d;d++)a[d].addEventListener("featuremoving",function(a,c){b.editMarker?(b.editMarker.setPosition(c),b.editMarker.show()):(b.editMarker=b._mapGeometry.createMarker(c,{url:b._crossMarkerStyle.editImageUrl,size:{width:b._crossMarkerStyle.width,height:b._crossMarkerStyle.height},markerType:0}),b.editGroup.addOverlay(b.editMarker),b.editMarker.enableEditing(),b.editMarker.setZIndex(100),b._dragEdit()),b.editMarker.setData({line:a})})},MapPlatForm.Base.MapRoutePlan.prototype._removeEventToLines=function(a){if(a&&a instanceof Array)for(var b=a.length,c=0;b>c;c++)a[c].removeEventListener("featuremoving")},MapPlatForm.Base.MapRoutePlan.prototype._dragEdit=function(){if(this.editMarker){this.editMarker.isEnableEdit=!0,this.editMarker.addEventListener("featuremousedown",function(a){a.removeEventListener("mouseout")});var a=this;this.editMarker.addEventListener("dragend",function(b){b.hide(),b.addEventListener("mouseout",function(a){a.hide()}),a._currentPolyline=b.getData().line,a._afterDrag(b.getPosition())}),this.editMarker.addEventListener(NPMapLib.MARKER_EVENT_DRAG_START,function(){for(var b=a.routeGroup.getAllOverlayers(),c=[],d=0;d<b.length;d++)"NPMapLib.Geometry.Polyline"===b[d].CLASS_NAME&&c.push(b[d]);a._removeEventToLines(c)}),this.editMarker.addEventListener("mouseout",function(a){a.hide()})}},MapPlatForm.Base.MapRoutePlan.prototype._getRelativeRoute=function(a){for(var b=null,c=null,d=0,e=this._routes.length;e>d;d++)if(this._routes[d].dragIconIndex.icon1===parseInt(a,10)||this._routes[d].dragIconIndex.icon2===parseInt(a,10))if(b){if(!c){c=this._routes[d];break}}else b=this._routes[d];return{route1:b,route2:c}},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(a,b){var c=null,d=null,e=null,f="add";if(b){var g=this._getRelativeRoute(b),h=g.route1.route.getPath(),i=g.route2.route.getPath();d=h[0],e=i[i.length-1],f="edit"}else c=this._currentPolyline.getPath(),d=c[0],e=c[c.length-1],f="add";var j,k,l,m={startStop:d,endStop:a,trafficModel:"car",planRoadType:this._planRoadType},n=this;this._mapService.searchRouteByCoor(m,function(c){j=c,n._searchRouteByDrag(a,f,g,b,j,k,l)});var o={startStop:a,endStop:e,trafficModel:"car",planRoadType:this._planRoadType},p=new MapPlatForm.Base.MapService(this.map,this.dataServiceURL);p.searchRouteByCoor(o,function(c){k=c,n._searchRouteByDrag(a,f,g,b,j,k,l)});var q=new MapPlatForm.Base.MapService(this.map,this.dataServiceURL);q.queryPOIByCoord(a,function(c){c?l=c:(a.data={name:"未知地点"},l=c?c:a),n._searchRouteByDrag(a,f,g,b,j,k,l)})},MapPlatForm.Base.MapRoutePlan.prototype._searchRouteByDrag=function(a,b,c,d,e,f,g){if(e&&f&&g){for(var h=this.routeGroup.getAllOverlayers(),i=[],j=0;j<h.length;j++)"NPMapLib.Geometry.Polyline"===h[j].CLASS_NAME&&i.push(h[j]);return this._addEventToLines(i),e.features.length<1||f.features.length<1?void(this._errorCallBack&&this._errorCallBack instanceof Function&&this._errorCallBack("EN"===NPMapLib.CULTURE?"There is no suitable route, please re-drag！":"没有合适的路线，请重新拖动！")):void this._getAddressByCoor(a,{result1:e,result2:f,type:b,relativeRoutes:c,key:d},g)}},MapPlatForm.Base.MapRoutePlan.prototype._getAddressByCoor=function(a,b,c){"add"===b.type?this._afteDragAdd(a,b,c):this._afterDragEdit(a,b,c)},MapPlatForm.Base.MapRoutePlan.prototype._afteDragAdd=function(a,b,c){var d=b.result1.features[0],e=b.result2.features[0];this._setPolylineStyle([d,e]),d.setData({index:++this._routeIndex}),e.setData({index:++this._routeIndex}),this.routeGroup.addOverlay(d),this.routeGroup.addOverlay(e),d.setZIndex(1),e.setZIndex(1),this._addEventToLines([d,e]);var f=this._crossMarkerStyle.crossImageUrl,g=this._mapGeometry.createMarker(a,{url:f,size:{width:this._crossMarkerStyle.width,height:this._crossMarkerStyle.height},markerType:0});this.editGroup.addOverlay(g),g.setZIndex(100);var h=c.data.name,i=h?h:this.defaultName;this._throughMarkerNum++,i=this._throughMarkerNum+":"+i,g.setData({key:this._throughMarkerNum,name:i});var j=this._getRouteByIndex(this._currentPolyline.getData().index),k=this._addInfowin(i,this._throughMarkerNum,a);k.getBaseDiv().title=i,this._throughMarkerInfo[this._throughMarkerNum]={infoWindow:k,marker:g},g.isEnableEdit=!0;var l=this;g.addEventListener("dragend",function(a){var b=parseInt(a.getData().key,10),c=l._throughMarkerInfo[b];c.infoWindow.close(),l._afterDrag(a.getPosition(),b)}),b.result1.messages.startPointName=j.routeInfo.startPointName,b.result2.messages.startPointName=i,this._refreshRouteArray({route1:{index:d.getData().index,dragIconIndex:{icon1:j.dragIconIndex.icon1,icon2:this._throughMarkerNum},route:d,routeInfo:b.result1.messages},route2:{index:e.getData().index,dragIconIndex:{icon1:this._throughMarkerNum,icon2:j.dragIconIndex.icon2},route:e,routeInfo:b.result2.messages},key:b.key},b.type),this.routeGroup.removeOverlay(this._currentPolyline)},MapPlatForm.Base.MapRoutePlan.prototype._afterDragEdit=function(a,b,c){var d=b.relativeRoutes;this.routeGroup.removeOverlay(d.route1.route),this.routeGroup.removeOverlay(d.route2.route);var e=b.result1.features[0],f=b.result2.features[0];this._setPolylineStyle([e,f]),e.setData({index:d.route1.route.getData().index}),f.setData({index:d.route2.route.getData().index}),this.routeGroup.addOverlay(e),this.routeGroup.addOverlay(f),e.setZIndex(1),f.setZIndex(1),this._addEventToLines([e,f]);var g=c.data.name,h=g?g:this.defaultName;h=b.key+":"+h,this._throughMarkerInfo[b.key].marker.setData({key:b.key,name:h}),d.route1.route=e,d.route1.routeInfo=b.result1.messages,d.route2.route=f,d.route2.routeInfo=b.result2.messages,d.route2.routeInfo.startPointName=h;var i=this._addInfowin(h,b.key,a);this._throughMarkerInfo[b.key].infoWindow=i,this._refreshRouteArray({route1:d.route1,route2:d.route2,key:b.key},b.type)},MapPlatForm.Base.MapRoutePlan.prototype._getThroughInfo=function(a,b){var c=document.createElement("div");c.style.fontSize="13px",c.style.border="1px solid #dfdfdf",c.style.borderRadius="5px",c.style.webkitBorderRadius="5px",c.style.height="21px",c.style.backgroundColor="#fff";var d=document.createElement("span");d.style.maxWidth="100px",d.style.overflow="hidden",d.style.whiteSpace="nowrap",d.style.textOverflow="ellipsis",d.style.float="left",d.style.marginLeft="2px",d.style.display="inline-block",d.style.lineHeight="21px",d.innerText=a;var e=document.createElement("i");return e.style.width="14px",e.style.height="14px",e.style.float="left",e.style.display="inline-block",e.style.marginLeft="5px",e.style.marginTop="3px",e.style.cursor="pointer",e.style.background="url('"+OpenLayers.Util.getImageLocation("close.png")+"') no-repeat",e.className="infowindow-close",e.setAttribute("key",b),c.appendChild(d),c.appendChild(e),c},MapPlatForm.Base.MapRoutePlan.prototype._addInfowin=function(a,b,c){var d=this._getThroughInfo(a,b),e=new NPMapLib.Symbols.InfoWindow(c,"",d,{iscommon:!0,isAdaptation:!1,offset:new NPMapLib.Geometry.Size(7,-9)});this.map.addOverlay(e),e.open();for(var f=document.getElementsByClassName("infowindow-close"),g=this,h=f.length-1;h>=0;h--)f[h].onclick=function(){key=this.getAttribute("key");var a=g._getRelativeRoute(key),b=a.route1.route.getPath(),c=a.route2.route.getPath(),d={startStop:b[0],endStop:c[c.length-1],trafficModel:"car",planRoadType:g._planRoadType};g._mapService.searchRouteByCoor(d,function(b){if(b.features[0]){var c=g._throughMarkerInfo[key];c.infoWindow.close(),g.editGroup.removeOverlay(c.marker),g.routeGroup.removeOverlay(a.route1.route),g.routeGroup.removeOverlay(a.route2.route);var d=b.features[0],e=++g._routeIndex;d.setData({index:e}),g._setPolylineStyle([d]),g.routeGroup.addOverlay(d),d.setZIndex(0),b.messages.startPointName=a.route1.routeInfo.startPointName,g._refreshRouteArray({route:{index:e,dragIconIndex:{icon1:a.route1.dragIconIndex.icon1,icon2:a.route2.dragIconIndex.icon2},route:b.features[0],routeInfo:b.messages},key:key},"delete"),g._addEventToLines([b.features[0]])}else g._errorCallBack()})};return e},MapPlatForm.Base.MapRoutePlan.prototype._refreshRouteArray=function(a,b){var c,d=null,e=null;if("add"===b){for(var f=this._currentPolyline.getData().index,g=0,h=this._routes.length;h>g;g++)if(this._routes[g].index===parseInt(f,10)){c=g;break}this._routes.splice(c,1,a.route1),this._routes.splice(c+1,0,a.route2)}else if("edit"===b){for(var g=0,h=this._routes.length;h>g;g++)if(this._routes[g].dragIconIndex.icon1===parseInt(a.key,10)||this._routes[g].dragIconIndex.icon2===parseInt(a.key,10))if(d){if(!e){e=this._routes[g],this._routes.splice(g,1,a.route2);break}}else d=this._routes[g],this._routes.splice(g,1,a.route1)}else for(var g=0,h=this._routes.length;h>g;g++)if(this._routes[g].dragIconIndex.icon1===parseInt(a.key,10)||this._routes[g].dragIconIndex.icon2===parseInt(a.key,10))if(d){if(!e){e=this._routes[g],this._routes.splice(g,1);break}}else d=this._routes[g],this._routes.splice(g,1,a.route);var i,j=[];if(this._routes&&this._routes.length>0){for(var g=0,h=this._routes.length;h>g;g++)j=j.concat(this._routes[g].route.getPath());i=new NPMapLib.Geometry.Polyline(j)}var k={routes:this._routes,polyline:i};this._researchCallback(k)},MapPlatForm.Base.MapRoutePlan.prototype._setPolylineStyle=function(a){if(a&&a instanceof Array)for(var b=a.length,c=0;b>c;c++)a[c].setStyle(this._lineStyle),a[c].setZIndex(0)},MapPlatForm.Base.MapRoutePlan.prototype._getRouteByIndex=function(a){for(var b=null,c=0,d=this._routes.length;d>c;c++)if(this._routes[c].index===parseInt(a,10)){b=this._routes[c];break}return b},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfo=function(a,b){var c=b.startPosition,d=b.stopPosition;for(var e in this._throughMarkerInfo)if(a!==parseInt(e)){var f=this._throughMarkerInfo[e].startPosition,g=this._throughMarkerInfo[e].stopPosition,h=this._throughMarkerInfo[e].crossPoint;c.lat===f.lat&&c.lon===f.lon&&(this._throughMarkerInfo[e].startPosition=b.crossPoint,this._throughMarkerInfo[e].route1=b.route2),d.lat===g.lat&&d.lon===g.lon&&(this._throughMarkerInfo[e].stopPosition=b.crossPoint,this._throughMarkerInfo[e].route2=b.route1),c.lat===h.lat&&c.lon===h.lon&&(this._throughMarkerInfo[e].stopPosition=b.crossPoint,this._throughMarkerInfo[e].route2=b.route1),d.lat===h.lat&&d.lon===h.lon&&(this._throughMarkerInfo[e].startPosition=b.crossPoint,this._throughMarkerInfo[e].route1=b.route2)}},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfoDel=function(a,b,c){var d=b.crossPoint;for(var e in _throughMarkerInfo)if(a!==parseInt(e)){var f=this._throughMarkerInfo[e].startPosition,g=this._throughMarkerInfo[e].stopPosition;d.lat===f.lat&&d.lon===f.lon&&(this._throughMarkerInfo[e].startPosition=b.startPosition,this._throughMarkerInfo[e].route1=c),d.lat===g.lat&&d.lon===g.lon&&(this._throughMarkerInfo[e].stopPosition=b.stopPosition,this._throughMarkerInfo[e].route2=c)}},MapPlatForm.Base.MapRoutePlan.prototype.addRoutePlanControl=function(a,b,c){if(this._planRoadType=a.planRoadType,this._startMarker=a.startMarker,this._endMarker=a.endMarker,this._throughMarkerInfo={},this._routes=[],this._throughMarkerNum=0,this._routeIndex=0,this._lineStyle=a.lineStyle?a.lineStyle:{color:"#0080c0",weight:10,lineStyle:"1px solid #0080c0",opacity:.7},this._crossMarkerStyle=a.crossMarkerStyle?a.crossMarkerStyle:{crossImageUrl:OpenLayers.Util.getImageLocation("path-cross.png"),editImageUrl:OpenLayers.Util.getImageLocation("path-edit.png"),height:11,width:11},this._clearEditInfoOnMap(),this._researchCallback=b,this._errorCallBack=c,a.polyline){var d=a.polyline;this._setPolylineStyle([d]),this.routeGroup.addOverlay(d),d.setData({index:this._routeIndex}),this._routeArray=[d],this._addEventToLines([d]),this._routes=[{index:this._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:d,routeInfo:""}]}else this._queryRoute()},MapPlatForm.Base.MapRoutePlan.prototype.stopEdit=function(){for(var a=this.routeGroup.getAllOverlayers(),b=[],c=0;c<a.length;c++)"NPMapLib.Geometry.Polyline"===a[c].CLASS_NAME&&b.push(a[c]);this._removeEventToLines(b),this.editGroup.removeOverlay(this.editMarker),this.editMarker=null;for(var d in this._throughMarkerInfo)this._throughMarkerInfo[d].marker&&this._throughMarkerInfo[d].marker.disableEditing();for(var e=document.getElementsByClassName("infowindow-close"),c=e.length-1;c>=0;c--)e[c].onclick=null},MapPlatForm.Base.MapRoutePlan.prototype.startEdit=function(){for(var a=this.routeGroup.getAllOverlayers(),b=[],c=0;c<a.length;c++)"NPMapLib.Geometry.Polyline"===a[c].CLASS_NAME&&b.push(a[c]);this._addEventToLines(b);for(var d in this._throughMarkerInfo)this._throughMarkerInfo[d].marker&&this._throughMarkerInfo[d].marker.enableEditing();for(var e=document.getElementsByClassName("infowindow-close"),f=this,c=e.length-1;c>=0;c--)e[c].onclick=function(){key=this.getAttribute("key");var a=f._getRelativeRoute(key),b=a.route1.route.getPath(),c=a.route2.route.getPath(),d={startStop:b[0],endStop:c[c.length-1],trafficModel:"car",planRoadType:f._planRoadType};
f._mapService.searchRouteByCoor(d,function(b){if(b.features[0]){var c=f._throughMarkerInfo[key];c.infoWindow.close(),f.editGroup.removeOverlay(c.marker),f.routeGroup.removeOverlay(a.route1.route),f.routeGroup.removeOverlay(a.route2.route);var d=b.features[0],e=++f._routeIndex;d.setData({index:e}),f._setPolylineStyle([d]),f.routeGroup.addOverlay(d),d.setZIndex(0),b.messages.startPointName=a.route1.routeInfo.startPointName,f._refreshRouteArray({route:{index:e,dragIconIndex:{icon1:a.route1.dragIconIndex.icon1,icon2:a.route2.dragIconIndex.icon2},route:b.features[0],routeInfo:b.messages},key:key},"delete"),f._addEventToLines([b.features[0]])}else f._errorCallBack()})}}}(),function(){MapPlatForm.Base.MapGeometryAnim=function(a){this.CLASS_NAME="MapPlatForm.Base.MapGeometryAnim",this._npMap=a,this._map=a._mapAdapter.map,this._removeMapEventFlag=!1,this._trafficUtil=new MapPlatForm.Base.TrafficUtil(a),this._pointDefaultStyle={startColor:"#FF0000",endColor:"#FF0000",size:20},this._lineDefaultStyle={flowStep:3,flowElementSpace:3.5,flowCount:1,flowElementBgColor:"#FFFF72",flowElementMax:7,flowElementMin:0,flowElementCount:10,lineBorderWidth:5,lineBorderColor:"#FF0000",lineDash:!1,lineDashColor:"#00FF00",lineDashPattern:[16,16],lineDash3d:!1,curve:!1,degree:10},this._graphView=new ht.graph.GraphView,this._dm=this._graphView.dm(),this._view=this._graphView.getView(),this._graphView.setEditable(!0),this._canvas=this._graphView.getCanvas(),this._canvas.width=this._npMap.getSize().width,this._canvas.height=this._npMap.getSize().height,this._ctx=this._canvas.getContext("2d"),this._map.viewPortDiv.appendChild(this._view),this._initFun(),this._curve=!1,this._degree=MAPPLATFORM_BASE_MAPGEOMETRYANIM_30},MapPlatForm.Base.MapGeometryAnim.prototype.createAnimPoint=function(a,b){var c=this._objectExtend(b,this._pointDefaultStyle),d=NPMapLib.T.setPoint(this._npMap,a),e=this._map.getPixelFromLonLat(d),f=new ht.Node;f.s("endColor",c.endColor),f.s("startColor",c.startColor),f.setSize(c.size,c.size),f.setImage("bubble"),f.setPosition(e),f.lonLat=d,this._dm.add(f)},MapPlatForm.Base.MapGeometryAnim.prototype.createAnimLine=function(a,b){var c,d=new ht.Shape,e=this._objectExtend(b,this._lineDefaultStyle),f=e.curve||!1,g=e.degree||MAPPLATFORM_BASE_MAPGEOMETRYANIM_30;f&&(d.setSegments([1,3]),this._curve=f,this._degree=g),c=this._createShapePoints(a),d.setPoints(c),this._createShapeStyle(d,e),this._dm.add(d)},MapPlatForm.Base.MapGeometryAnim.prototype.clear=function(){this._dm.clear()},MapPlatForm.Base.MapGeometryAnim.prototype.hideView=function(){this._graphView._view.style.display="none"},MapPlatForm.Base.MapGeometryAnim.prototype.showView=function(){this._graphView._view.style.display="block"},MapPlatForm.Base.MapGeometryAnim.prototype.enableFlow=function(a){this._graphView.enableFlow(a)},MapPlatForm.Base.MapGeometryAnim.prototype.disableFlow=function(){this._graphView.disableFlow(),this._dm.disableAnimation()},MapPlatForm.Base.MapGeometryAnim.prototype.enablePointAnimation=function(){this._animPoint=!0},MapPlatForm.Base.MapGeometryAnim.prototype.disablePointAnimation=function(){this._animPoint=!1},MapPlatForm.Base.MapGeometryAnim.prototype.destroy=function(){this._map.viewPortDiv.removeChild(this._view),this.disableFlow(),this.disablePointAnimation(),this.clear(),this._trafficUtil=null,this._curve=null,this._degree=null,this._ctx=null,this._canvas=null,this._view=null,this._dm.clear,this._graphView=null,this._removeMapEventFlag=!0},MapPlatForm.Base.MapGeometryAnim.prototype._initFun=function(){this._initHT(),this._initInteractive(),this._setDefaultImg(),this._setDefaultCompType()},MapPlatForm.Base.MapGeometryAnim.prototype._initInteractive=function(){this._map.events.fallThrough=!0;this._map.events.register("movestart",this,this._mapOptStartCallback),this._map.events.register("moveend",this,this._mapOptEndCallback),this._map.events.register("zoomstart",this,this._mapOptStartCallback)},MapPlatForm.Base.MapGeometryAnim.prototype._mapOptStartCallback=function(){this._graphView&&(this._graphView.isVisible=function(){return!1})},MapPlatForm.Base.MapGeometryAnim.prototype._mapOptEndCallback=function(){this._graphView&&(this._view.style.opacity=1,this._resetPosition(),this._graphView.isVisible=function(){return!0})},MapPlatForm.Base.MapGeometryAnim.prototype._initHT=function(){this._view.className="olScrollable",this._graphView.disableToolTip(),this._graphView.setScrollBarVisible(!1),this._graphView.setAutoScrollZone(-1),this._graphView.handleScroll=function(){},this._graphView.handlePinch=function(){},this._animPoint=!0,this._graphView.enableFlow(60),this._begin();var a=this._graphView.getSelectionModel();a.setSelectionMode("none"),this._view.style.position="absolute",this._view.style.top="0",this._view.style.left="0",this._view.style.right="0",this._view.style.bottom="0",this._view.style.zIndex=999;var b=this;this._view.addEventListener("ontouchend"in document?"touchstart":"mousedown",function(a){var c=b._graphView.getDataAt(a);(c||a.metaKey||a.ctrlKey)&&a.stopPropagation()},!1)},MapPlatForm.Base.MapGeometryAnim.prototype._resetPosition=function(){this._graphView.tx(0),this._graphView.ty(0);var a=this;this._dm.each(function(b){b instanceof ht.Shape?a._resetShape(b):b instanceof ht.Node&&b.lonLat&&b.setPosition(a._trafficUtil.mercatorToPixel([b.lonLat.lon,b.lonLat.lat],a._ctx))}),this._graphView.validate(),this._view.style.display="block"},MapPlatForm.Base.MapGeometryAnim.prototype._resetShape=function(a){var b,c,d=new ht.List,e=a,f=a.getPoints(),g=0,h=f.size();for(g;h>g;g++)b=f.get(g),b.lonLat&&(c=this._trafficUtil.mercatorToPixel([b.lonLat.lon,b.lonLat.lat],this._ctx),c.lonLat=b.lonLat,d.add(c));e.setPoints(d)},MapPlatForm.Base.MapGeometryAnim.prototype._createShapeStyle=function(a,b){a.s("shape.border.width",b.lineBorderWidth),a.s("shape.border.color",b.lineBorderColor),a.s("shape.background",null),a.s("shape.dash",b.lineDash),a.s("shape.dash.color",b.lineDashColor),a.s("shape.dash.pattern",b.lineDashPattern),a.s("shape.dash.3d",b.lineDash3d),a.s("flow",!0),a.s("flow.step",b.flowStep),a.s("flow.count",b.flowCount),a.s("flow.element.space",b.flowElementSpace),a.s("flow.element.background",b.flowElementBgColor),a.s("flow.element.shadow.visible",!1),a.s("flow.element.max",b.flowElementMax),a.s("flow.element.min",b.flowElementMin),a.s("flow.element.count",b.flowElementCount)},MapPlatForm.Base.MapGeometryAnim.prototype._setDefaultImg=function(){ht.Default.setImage("bubble",{width:{func:function(a){return a._width||40}},height:{func:function(a){return a._height||40}},comps:[{cid:1,type:"bubble-shadow",relative:1,rect:[17,1,1]},{cid:2,type:"bubble-shadow",relative:1,rect:[17,1,1]}]})},MapPlatForm.Base.MapGeometryAnim.prototype._setDefaultCompType=function(){ht.Default.setCompType("bubble-shadow",function(a,b,c,d){if(null!=d.s("dia"+c.cid)){a.save(),a.beginPath(),a.globalAlpha=d.s("opacity"+c.cid);var e=b.x+b.width/2,f=b.y+b.height/2,g=d.s("dia"+c.cid),h=a.createRadialGradient(e,f,0,e,f,g);this._animPoint?(h.addColorStop(.1,d.s("startColor")),h.addColorStop(.8,d.s("endColor"))):(h.addColorStop(.1,d.s("startColor")),h.addColorStop(.8,d.s("startColor"))),a.fillStyle=h,a.arc(e,f,g/2,0,2*Math.PI),a.fill(),a.restore()}})},MapPlatForm.Base.MapGeometryAnim.prototype._begin=function(){var a=this;ht.Default.startAnim({duration:1e3,easing:ht.Default.getEasing("Linear"),action:function(b){a._animPoint||(b=1),a._dm.each(function(a){a.s("dia1",b*Math.min(a.getWidth(),a.getHeight()))})},finishFunc:function(){ht.Default.startAnim({duration:1e3,easing:ht.Default.getEasing("Linear"),action:function(b){a._animPoint||(b=0),a._dm.each(function(a){a.s("dia1",Math.min(a.getWidth(),a.getHeight())),a.s("opacity1",1-b)})},finishFunc:function(){a._dm.each(function(a){a.s("opacity1",1),a.s("dia1",0)}),a._begin()}}),ht.Default.startAnim({duration:1e3,easing:ht.Default.getEasing("Linear"),action:function(b){a._animPoint||(b=1),a._dm.each(function(a){a.s("dia2",b*Math.min(a.getWidth(),a.getHeight()))})},finishFunc:function(){ht.Default.startAnim({duration:1e3,easing:ht.Default.getEasing("Linear"),action:function(b){a._animPoint||(b=0),a._dm.each(function(a){a.s("dia2",Math.min(a.getWidth(),a.getHeight())),a.s("opacity2",1-b)})},finishFunc:function(){a._dm.each(function(a){a.s("opacity2",1),a.s("dia2",0)})}})}})}})},MapPlatForm.Base.MapGeometryAnim.prototype._objectExtend=function(a,b){a=a||{};for(var c in b)void 0===a[c]&&(a[c]=this._objectClone(b[c]));return a},MapPlatForm.Base.MapGeometryAnim.prototype._objectClone=function(a,b){if(null==a||"object"!=typeof a)return a;if(a.constructor!=Object&&a.constructor!=Array)return a;if(a.constructor==Date||a.constructor==RegExp||a.constructor==Function||a.constructor==String||a.constructor==Number||a.constructor==Boolean)return new a.constructor(a);b=b||new a.constructor;for(var c in a)b[c]="undefined"==typeof b[c]?this._objectClone(a[c],null):b[c];return b},MapPlatForm.Base.MapGeometryAnim.prototype._createShapePoints=function(a){var b=new ht.List;if(this._curve)b=this._createShapePointsForCurve(a);else{var c,d,e=0,f=a.length;for(e;f>e;e++)c=this._trafficUtil.wgs84ToMercator([a[e].lon,a[e].lat]),d=this._trafficUtil.mercatorToPixel([c.lon,c.lat],this._ctx),d.lonLat=c,b.add(d)}return b},MapPlatForm.Base.MapGeometryAnim.prototype._createShapePointsForCurve=function(a){var b=new ht.List,c=a.length,d=this._trafficUtil.wgs84ToMercator([a[0].lon,a[0].lat]),e=this._trafficUtil.wgs84ToMercator([a[c-1].lon,a[c-1].lat]),f=this._createControlPoint(d,e,this._degree),g=this._trafficUtil.mercatorToPixel([d.lon,d.lat],this._ctx),h=this._trafficUtil.mercatorToPixel([e.lon,e.lat],this._ctx),i=this._trafficUtil.mercatorToPixel([f.lon,f.lat],this._ctx);return g.lonLat=d,h.lonLat=e,i.lonLat=f,b.add(g),b.add(i),b.add(h),b},MapPlatForm.Base.MapGeometryAnim.prototype._createControlPoint=function(a,b,c){var d=a.lon,e=a.lat,f=b.lon,g=b.lat,h=(d+f)/2,i=(e+g)/2,j=(e-g)*c+h,k=(f-d)*c+i;return{lon:j,lat:k}},MAPPLATFORM_BASE_MAPGEOMETRYANIM_15=.1,MAPPLATFORM_BASE_MAPGEOMETRYANIM_30=.2,MAPPLATFORM_BASE_MAPGEOMETRYANIM_45=.3,MAPPLATFORM_BASE_MAPGEOMETRYANIM_60=.4,MAPPLATFORM_BASE_MAPGEOMETRYANIM_75=.5,MAPPLATFORM_BASE_MAPGEOMETRYANIM_NEGATIVE_15=-.1,MAPPLATFORM_BASE_MAPGEOMETRYANIM_NEGATIVE_30=-.2,MAPPLATFORM_BASE_MAPGEOMETRYANIM_NEGATIVE_45=-.3,MAPPLATFORM_BASE_MAPGEOMETRYANIM_NEGATIVE_60=-.4,MAPPLATFORM_BASE_MAPGEOMETRYANIM_NEGATIVE_75=-.5}(),function(){var a=function(a,b,c,d){var e=NPMapLib.T.setPoint(b,a);this.map=b,this.opts=d,this.point=a,this.lonLat=e,this.text=".",this.color="rgba(255,255,255,opa)",this.radius=1,this.opacity=Math.random(),this.getColor=function(){Math.random()},this.init=function(){this.getColor()},this.reset=function(a,b,c){this.x=b.x+(this.lonLat.lon-a.lon)/c,this.y=b.y-(this.lonLat.lat-a.lat)/c},this.draw=function(){this.opacity=Math.random();var a=.5,b=.1*this.radius+.1;c.beginPath(),c.arc(this.x,this.y,this.radius+a+b,0,2*Math.PI,!1),c.closePath();var d=c.createRadialGradient(this.x,this.y,0,this.x,this.y,this.radius+a+b);d.addColorStop(0,this.color.replace("opa",this.opacity)),d.addColorStop(this.radius/(this.radius+a+b),this.color.replace("opa",this.opacity)),d.addColorStop((this.radius+a)/(this.radius+a+b),this.color.replace("opa",.2*this.opacity)),d.addColorStop(1,this.color.replace("opa",0)),c.fillStyle=d,c.fill()}};MapPlatForm.Base.MapStar=function(a,b){this.CLASS_NAME="MapStar",this._map=a,this._opts=b?b:{starColor:"#303845",endColor:"white",freshTime:500},this._stars=[],this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.top="0",this.canvas.style.left="0",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=999,this._map._mapAdapter.map.viewPortDiv.appendChild(this.canvas),this.context=this.canvas.getContext("2d")},MapPlatForm.Base.MapStar.prototype._playStars=function(){for(var a=0;a<this._stars.length;a++)this._stars[a].show&&(this._stars[a].getColor(),this._stars[a].draw());var b=this,c=this._opts.freshTime?this._opts.freshTime:500;this.timeID=setTimeout(function(){b.context.clearRect(0,0,b.canvas.width,b.canvas.height),b._playStars()},c)},MapPlatForm.Base.MapStar.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("movestart",this,function(){window.clearTimeout(this.timeID),this.canvas.style.display="none"}),this._map._mapAdapter.map.events.register("moveend",this,function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.display="block";for(var a=this._map.getExtent(),b=this._map._mapAdapter.map.getCenter(),c=this._map._mapAdapter.map.resolution,d=this._map._mapAdapter.map.getPixelFromLonLat(b),e=0;e<this._stars.length;e++)a.containsPoint(this._stars[e].point)?(this._stars[e].reset(b,d,c),this._stars[e].draw(),this._stars[e].show=!0):this._stars[e].show=!1;this._playStars()}),this._map._mapAdapter.map.events.register("zoomstart",this,function(){this.canvas.style.display="none",window.clearTimeout(this.timeID)})},MapPlatForm.Base.MapStar.prototype.addStars=function(b){for(var c=this._map.getExtent(),d=this._map._mapAdapter.map.getCenter(),e=this._map._mapAdapter.map.resolution,f=this._map._mapAdapter.map.getPixelFromLonLat(d),g=0;g<b.length;g++){var h=new a(b[g],this._map,this.context,this._opts);c.containsPoint(h.point)&&(h.show=!0,h.init(),h.reset(d,f,e),h.draw()),this._stars.push(h)}this._initInteractive(),this._playStars()}}(),function(){MapPlatForm.Base.MapLineAnim=function(a){this.CLASS_NAME="MapLineAnim",this._map=a,this._lines=[],this._newLine=[],this._circleSize=10,this._targetPoint,this._targetPixel,this._targetStyle,this._lineStyle,this.stop,this._xy=[],this._flowXy=[],this.step=1,this.k=0,this.speed=.05,this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.top="0",this.canvas.style.left="0",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=999,this._map._mapAdapter.map.viewPortDiv.appendChild(this.canvas),this.context=this.canvas.getContext("2d"),window.requestAnimationFrame=window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame,window.requestAnimationFrame=window.requestAnimationFrame?window.requestAnimationFrame:function(a){window.setTimeout(a,300)},window.cancelAnimationFrame=window.cancelAnimationFrame?window.cancelAnimationFrame:function(a){window.clearTimeout(a)}},MapPlatForm.Base.MapLineAnim.prototype._drawAnimateMarker=function(){if(this._targetPixel){var a=this.context;a.beginPath(),this._circleSize+=1;var b=a.createRadialGradient(this._targetPixel.x,this._targetPixel.y,0,this._targetPixel.x,this._targetPixel.y,this._circleSize);b.addColorStop(0,this._targetStyle.color2),b.addColorStop(1,this._targetStyle.color),a.arc(this._targetPixel.x,this._targetPixel.y,this._circleSize,0,2*Math.PI),a.fillStyle=b,a.fill(),this._circleSize>35&&(this._circleSize=0)}},MapPlatForm.Base.MapLineAnim.prototype._drawFlowLine=function(){for(var a=this._flowXy.length-1;a>=0;a--)if(this._xy[this._flowXy[a]].obj[2]>.6){var b=Math.floor(this._xy[this._flowXy[a]].obj[2]*this._xy[this._flowXy[a]].points.length);b>this._xy[this._flowXy[a]].points.length-1&&(b=this._xy[this._flowXy[a]].points.length-1);for(var c=b;c<this._xy[this._flowXy[a]].points.length;c++){this.context.beginPath(),this.context.fillStyle="rgba(255, 255, 255,0.9)";var d=this._xy[this._flowXy[a]].points[c];this.context.arc(d.x,d.y,4,0,2*Math.PI,!0),this.context.fill()}}},MapPlatForm.Base.MapLineAnim.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("movestart",this,function(){this.canvas.style.display="none",window.cancelAnimationFrame(this.stop)}),this._map._mapAdapter.map.events.register("moveend",this,function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.display="block",this._resetLines(),this._drawLines()}),this._map._mapAdapter.map.events.register("zoomstart",this,function(){this.canvas.style.display="none",window.cancelAnimationFrame(this.stop)})},MapPlatForm.Base.MapLineAnim.prototype._drawLines=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this._drawFlowLine();for(var a=0;a<this._xy.length*this.step;a++){var b=this._xy[a].obj[0]+.5*(this._xy[a].obj[3]-this._xy[a].obj[0]),c=this._xy[a].obj[1]+.5*(this._xy[a].obj[4]-this._xy[a].obj[1]),d=(1-this._xy[a].obj[2])*(1-this._xy[a].obj[2])*this._xy[a].obj[0]+2*this._xy[a].obj[2]*(1-this._xy[a].obj[2])*b+this._xy[a].obj[2]*this._xy[a].obj[2]*this._xy[a].obj[3],e=(1-this._xy[a].obj[2])*(1-this._xy[a].obj[2])*this._xy[a].obj[1]+2*this._xy[a].obj[2]*(1-this._xy[a].obj[2])*c+this._xy[a].obj[2]*this._xy[a].obj[2]*this._xy[a].obj[4],f=(1-this._xy[a].obj[2])*(1-this._xy[a].obj[2])*d+2*this._xy[a].obj[2]*(1-this._xy[a].obj[2])*b+this._xy[a].obj[2]*this._xy[a].obj[2]*this._xy[a].obj[3],g=(1-this._xy[a].obj[2])*(1-this._xy[a].obj[2])*e+2*this._xy[a].obj[2]*(1-this._xy[a].obj[2])*c+this._xy[a].obj[2]*this._xy[a].obj[2]*this._xy[a].obj[4];this.context.beginPath();var h=this.context.createLinearGradient(this._xy[a].obj[0],this._xy[a].obj[1],f,g);h.addColorStop(0,this._lineStyle.color2),h.addColorStop(this._xy[a].obj[2],this._lineStyle.color),h.addColorStop(1,this._lineStyle.color2),this.context.strokeStyle=h,this.context.moveTo(this._xy[a].obj[0],this._xy[a].obj[1]);for(var i=this._getPointInBizer(this._xy[a].points,this._xy[a].obj[2]),j=1;j<this._xy[a].points.length;j++)this.context.lineTo(this._xy[a].points[j].x,this._xy[a].points[j].y);this.context.globalAlpha=this._xy[a].obj[2],this.context.stroke(),this.context.beginPath(),this.context.font="14px 宋体",this.context.fillStyle="rgba(255, 255, 255,"+this._xy[a].obj[2]+")",this.context.fillText(".",i.x,i.y),this._xy[a].obj[2]+=this.speed,this._xy[a].obj[2]>=1&&(this.k++,this._xy[a].obj[2]=1),this._xy[a].obj[2]<0&&(this.k++,this._xy[a].obj[2]=0)}this.k>this._lines.length*this.step&&(this.k=0,this.speed>0?this.step++:this.step--,(this.step>1||this.step<1)&&(this.step=this.step>1?1:1,this.speed=0-this.speed)),this._drawAnimateMarker();var k=this;this.stop=window.requestAnimationFrame(function(){k._drawLines()})},MapPlatForm.Base.MapLineAnim.prototype._getPointInBizer=function(a,b){var c=Math.floor(b*a.length)-1;return c=0>c?0:c,c=c>a.length-1?a.length:c,a[c]},MapPlatForm.Base.MapLineAnim.prototype._resetLines=function(a){this.k=0,this.step=1,this.speed=.05,this._xy=[],this._flowXy=[];for(var b=this._map._mapAdapter.map.getCenter(),c=this._map._mapAdapter.map.resolution,d=this._map._mapAdapter.map.getPixelFromLonLat(b),e=0;e<this._lines.length;e++){for(var f=this._lines[e].getPath(),g=[],h=a?[]:this._newLine[e],i=0;i<f.length;i++){if(a){var j=NPMapLib.T.setPoint(this._map,f[i]);h.push(j)}var k={x:d.x+(h[i].lon-b.lon)/c,y:d.y-(h[i].lat-b.lat)/c};g.push(k)}a&&this._newLine.push(h);var l=g[0],m=g[g.length-1],n={obj:[l.x,l.y,0,m.x,m.y],points:g};this._xy.push(n)}for(var e=30;e>=0;e--){var o=Math.floor(Math.random()*this._xy.length);this._flowXy.push(o)}this._targetPoint&&(this._targetPixel={x:d.x+(this._targetPoint.lon-b.lon)/c,y:d.y-(this._targetPoint.lat-b.lat)/c})},MapPlatForm.Base.MapLineAnim.prototype.addTargetPoint=function(a,b){if(a&&(this._targetPoint=NPMapLib.T.setPoint(this._map,a)),this._targetStyle=b,this._targetStyle?this._targetStyle.color=this._targetStyle.color?this._targetStyle.color:"rgba(255, 255, 255,0.9)":this._targetStyle={color:"rgba(255, 255, 255,0.9)"},this._targetStyle.color.indexOf(",")>-1)if(4===this._targetStyle.color.split(",").length){var c=this._targetStyle.color.lastIndexOf(","),d=this._targetStyle.color.lastIndexOf(")"),e=this._targetStyle.color.substring(c+1,d);this._targetStyle.color2=this._targetStyle.color.replace(e," 0")}else 3===this._targetStyle.color.split(",").length&&(this._targetStyle.color2=this._targetStyle.color.replace("rgb","rgba"),this._targetStyle.color2=this._targetStyle.color2.replace(")",", 0)"));else this._targetStyle.color2="rgba(255, 255, 255, 0)"},MapPlatForm.Base.MapLineAnim.prototype.addLines=function(a,b){if(this._lines=a,this._lineStyle=b,this._lineStyle?this._lineStyle.color=this._lineStyle.color?this._lineStyle.color:"rgba(255, 0, 0,0.3)":this._lineStyle={color:"rgba(255, 0, 0, 0.3)"},this._lineStyle.color.indexOf(",")>-1)if(4===this._lineStyle.color.split(",").length){var c=this._lineStyle.color.lastIndexOf(","),d=this._lineStyle.color.lastIndexOf(")"),e=this._lineStyle.color.substring(c+1,d);this._lineStyle.color2=this._lineStyle.color.replace(e," 0")}else 3===this._lineStyle.color.split(",").length&&(this._lineStyle.color2=this._lineStyle.color.replace("rgb","rgba"),this._lineStyle.color2=this._lineStyle.color2.replace(")",", 0)"));else this._lineStyle.color2="rgba(255, 255, 255, 0)";this._resetLines(!0),this._drawLines(),this._initInteractive()}}(),function(){var a=function(a,b,c,d){var e=NPMapLib.T.setPoint(b,a);this.map=b,this.opts=d?d:{},this.point=a,this.lonLat=e,this.visibile=!0,this.iconOffset={},this.labelOffset={},this.reset=function(a,b,c){this.x=b.x+(this.lonLat.lon-a.lon)/c,this.y=b.y-(this.lonLat.lat-a.lat)/c},this.draw=function(a){a&&(delete this.img,this.img=null,this.img=a),this.visibile&&(this.iconOffset=this.opts.iconOffset?this.opts.iconOffset:{x:0,y:0},c.drawImage(this.img,this.x+this.iconOffset.x,this.y+this.iconOffset.y,this.opts.width,this.opts.height),this.labelOffset=this.opts.labelOffset?this.opts.labelOffset:{x:0,y:0},this.opts.label&&(c.textAlign=this.opts.textAlign?this.opts.textAlign:"center",c.font=this.opts.fontSize?this.opts.fontSize:"12px",c.strokeStyle=this.opts.strokeColor?this.opts.strokeColor:"red",c.strokeText(this.opts.label,this.x+this.labelOffset.x,this.y+this.labelOffset.y),c.fillStyle=this.opts.fillColor?this.opts.fillColor:"#FFFFFF",c.fillText(this.opts.label,this.x+this.labelOffset.x,this.y+this.labelOffset.y)))},this.hide=function(){this.visibile=!1},this.show=function(){this.visibile=!0},this.setStyle=function(a){if(a)if(a.width&&(this.opts.width=a.width),a.height&&(this.opts.height=a.height),a.textAlign&&(this.opts.textAlign=text.textAlign),a.fontSize&&(this.opts.fontSize=text.fontSize),a.strokeColor&&(this.opts.strokeColor=text.strokeColor),a.fillColor&&(this.opts.fillColor=text.fillColor),a.url){var b=new Image;b.src=a.url;var c=this;b.onload=function(){c.draw(b)}}else this.draw()},this.setText=function(a){a&&(this.opts.label=a)}};MapPlatForm.Base.MapMultiMarkers=function(a,b){this.CLASS_NAME="MapMultiMarkers",this._map=a,this._markers=[],this._showMarkers=[],this._imgMarkers={},this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=999,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.acvtiveMarker=null,this.mouseOver=b?b.mouseOver:null,this.mouseOut=b?b.mouseOut:null,this.click=b?b.click:null},MapPlatForm.Base.MapMultiMarkers.prototype._redraw=function(){if("none"!==this.canvas.style.display){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";var a=this._map.getExtent(),b=this._map._mapAdapter.map.getCenter(),c=this._map._mapAdapter.map.resolution,d=this._map._mapAdapter.map.getPixelFromLonLat(b);this._showMarkers=[];for(var e=0;e<this._markers.length;e++)a.containsPoint(this._markers[e].point)&&(this._markers[e].reset(b,d,c),this._markers[e].draw(this._imgMarkers[this._markers[e].opts.url].img),this._markers[e].visibile&&this._showMarkers.push(this._markers[e]));this.visibile=!0,this.drag=!1}},MapPlatForm.Base.MapMultiMarkers.prototype._clear=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.visibile=!1},MapPlatForm.Base.MapMultiMarkers.prototype._dragStart=function(){this.drag=!0},MapPlatForm.Base.MapMultiMarkers.prototype._dragEnd=function(){this.drag=!1},MapPlatForm.Base.MapMultiMarkers.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("zoomstart",this,this._clear),this._map._mapAdapter.map.events.register("dragstart",this,this._dragStart),this._map._mapAdapter.map.events.register("dragend",this,this._dragEnd),this._map._mapAdapter.map.events.register("updatesize",this,this._redraw);var a=this;(this.mouseOut||this.mouseOver)&&(this._map.getContainer().onmousemove=function(b){if(a.visibile&&!a.drag){for(var c=a._showMarkers.length-1;c>=0;c--){var d=b.xy;if(d.x>=a._showMarkers[c].x&&d.x<=a._showMarkers[c].x+a._showMarkers[c].opts.width&&d.y>=a._showMarkers[c].y&&d.y<=a._showMarkers[c].y+a._showMarkers[c].opts.height)return void(a.acvtiveMarker!=a._showMarkers[c]&&(a.acvtiveMarker&&a.mouseOut&&a.mouseOut(a.acvtiveMarker),a._map.setCursor("pointer"),a.mouseOver&&(a.mouseOver(a._showMarkers[c]),a.acvtiveMarker=a._showMarkers[c])))}a._map.setCursor("default"),a.acvtiveMarker&&a.mouseOut&&a.mouseOut(a.acvtiveMarker),a.acvtiveMarker=null}}),this.click&&(this._map.getContainer().onmousedown=function(b){a.curentXY=b.xy},this._map.getContainer().onclick=function(b){if(a.visibile&&!a.drag&&b.xy.x==a.curentXY.x&&b.xy.y==a.curentXY.y)for(var c=a._showMarkers.length-1;c>=0;c--){var d=b.xy;if(d.x>=a._showMarkers[c].x&&d.x<=a._showMarkers[c].x+a._showMarkers[c].opts.width&&d.y>=a._showMarkers[c].y&&d.y<=a._showMarkers[c].y+a._showMarkers[c].opts.height)return void(a.click&&a.click(a._showMarkers[c]))}})},MapPlatForm.Base.MapMultiMarkers.prototype.preventFlash=function(){this._map._mapAdapter.map.useMultiMarkers=!0},MapPlatForm.Base.MapMultiMarkers.prototype.addMarkers=function(b){for(var c=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),d=this._map._mapAdapter.map.resolution,e=this._map._mapAdapter.map.getPixelFromLonLat(c),f=this,g=0;g<b.length;g++){var h=new a(b[g],this._map,this.context,b[g].opts);h.data=b[g].data,this._imgMarkers[b[g].opts.url]?this._imgMarkers[b[g].opts.url].points.push(h):(this._imgMarkers[b[g].opts.url]={},this._imgMarkers[b[g].opts.url].points=[h],this._imgMarkers[b[g].opts.url].img=new Image),this._markers.push(h)}this._showMarkers=[];for(var i in this._imgMarkers)this._imgMarkers.hasOwnProperty(i)&&(this._imgMarkers[i].img.src=i,this._imgMarkers[i].img.k=i,this._imgMarkers[i].img.onload=function(){for(var a=0;a<f._imgMarkers[this.k].points.length;a++)f._imgMarkers[this.k].points[a].reset(c,e,d),f._imgMarkers[this.k].points[a].draw(f._imgMarkers[this.k].img),f._imgMarkers[this.k].points[a].visibile&&f._showMarkers.push(f._imgMarkers[this.k].points[a])});this.visibile=!0,this._initInteractive()},MapPlatForm.Base.MapMultiMarkers.prototype.getMarkers=function(){return this._markers},MapPlatForm.Base.MapMultiMarkers.prototype.destory=function(){this._map.getContainer().onclick=null,this._map.getContainer().onmousemove=null,this._map.getContainer().onmousedown=null,this._map._mapAdapter.map.events.unregister("moveend",this,this._redraw),this._map._mapAdapter.map.events.unregister("zoomstart",this,this._clear),this._map._mapAdapter.map.events.unregister("dragstart",this,this._dragStart),this._map._mapAdapter.map.events.unregister("dragend",this,this._dragEnd),this._map._mapAdapter.map.events.unregister("updatesize",this,this._redraw),this.canvas.remove()},MapPlatForm.Base.MapMultiMarkers.prototype.refresh=function(){this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.context.clearRect(0,0,this.canvas.width,this.canvas.height);var a=this._map.getExtent(),b=this._map._mapAdapter.map.getCenter(),c=this._map._mapAdapter.map.resolution,d=this._map._mapAdapter.map.getPixelFromLonLat(b);this._showMarkers=[];for(var e=0;e<this._markers.length;e++)a.containsPoint(this._markers[e].point)&&(this._markers[e].reset(b,d,c),this._markers[e].draw(this._imgMarkers[this._markers[e].opts.url].img),this._markers[e].visibile&&this._showMarkers.push(this._markers[e]));this.visibile=!0},MapPlatForm.Base.MapMultiMarkers.prototype.setVisibile=function(a){a?(this.canvas.style.display="block",this._redraw()):this.canvas.style.display="none"}}(),function(){var a=function(a,b,c,d){var e=a.getPath();if(e[0].lon)this.lonLats=NPMapLib.T.setPoints(b,e);else{this.lonLats=[];for(var f=0;f<e.length;f++)this.lonLats.push(NPMapLib.T.setPoints(b,e[f]))}var g=function(){var a=Math.floor(256*Math.random()),b=255,c=Math.floor(256*Math.random());return"rgb("+a+","+b+","+c+")"};this.polygon=a,this.map=b,this.context=c,this.opts=d,this.visibile=!0,this.pixels=[],this.defaultStrokeColor=g(),this.strokeColor=this.defaultStrokeColor,this.fillColor=(this.strokeColor.substring(0,this.strokeColor.lastIndexOf(")"))+",0.5)").replace("rgb","rgba"),this.defaultFillColor=this.fillColor,this.font="14px 宋体",this.fontColor="#ffffff",this.fontFillColor="#000000",a.data&&a.data.center&&(this.center=GeoJSON.read(a.data.center),this.center=NPMapLib.T.setPoint(b,this.center)),a.data&&a.data.font&&(this.font=a.data.font),a.data&&a.data.fontColor&&(this.fontColor=a.data.fontColor),a.data&&a.data.fontFillColor&&(this.fontFillColor=a.data.fontFillColor),a.data&&a.data.name&&(this.name=a.data.name),a.data&&a.data.strokeColor&&(this.defaultStrokeColor=a.data.strokeColor,this.strokeColor=a.data.strokeColor),a.data&&a.data.fillColor&&(this.defaultFillColor=a.data.fillColor,this.fillColor=a.data.fillColor),this.resetStyle=function(){this.fillColor=this.defaultFillColor,this.strokeColor=this.defaultStrokeColor},this.reset=function(a,b,c){this.resetStyle(),this.pixels=[];for(var d=0,e=0,f=0,g=0;g<this.lonLats.length;g++)if(this.lonLats[0].lon)this.pixels.push({x:b.x+(this.lonLats[g].lon-a.lon)/c,y:b.y-(this.lonLats[g].lat-a.lat)/c}),d+=this.pixels[g].x,e+=this.pixels[g].y,f++;else{for(var h=[],i=0;i<this.lonLats[g].length;i++){{this.lonLats[g][i]}h.push({x:b.x+(this.lonLats[g][i].lon-a.lon)/c,y:b.y-(this.lonLats[g][i].lat-a.lat)/c}),d+=h[i].x,e+=h[i].y,f++}this.pixels.push(h)}this.centerXY={x:d/f,y:e/f}},this.draw=function(){if(0!=this.pixels.length)if("undefined"!=typeof this.pixels[0].x){this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var a=1;a<this.pixels.length;a++)this.context.lineTo(this.pixels[a].x,this.pixels[a].y);this.context.closePath(),this.context.strokeStyle=this.strokeColor,this.context.lineWidth=2,this.context.stroke(),this.context.fillStyle=this.fillColor,this.context.fill()}else for(var b=0;b<this.pixels.length;b++){var c=this.pixels[b];this.context.beginPath(),this.context.moveTo(c[0].x,c[0].y);for(var a=1;a<c.length;a++)this.context.lineTo(c[a].x,c[a].y);this.context.closePath(),this.context.strokeStyle=this.strokeColor,this.context.lineWidth=1,this.context.stroke(),this.context.fillStyle=this.fillColor,this.context.fill()}},this.drawTitle=function(){this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font=this.font,this.context.strokeStyle=this.fontColor,this.context.strokeText(this.name,this.centerXY.x,this.centerXY.y),this.context.fillStyle=this.fontFillColor,this.context.fillText(this.name,this.centerXY.x,this.centerXY.y))
},this.crossMul=function(a,b){return a.x*b.y-a.y*b.x},this.checkCross=function(a,b,c,d){var e={x:a.x-c.x,y:a.y-c.y},f={x:b.x-c.x,y:b.y-c.y},g={x:d.x-c.x,y:d.y-c.y},h=this.crossMul(e,g)*this.crossMul(f,g);return e={x:c.x-a.x,y:c.y-a.y},f={x:d.x-a.x,y:d.y-a.y},g={x:b.x-a.x,y:b.y-a.y},0>=h&&this.crossMul(e,g)*this.crossMul(f,g)<=0?!0:!1},this.containsPoint=function(a,b){var c,d,e,f;c=a,d={x:-1e5,y:a.y};for(var g=0,h=0;h<b.length-1;h++)e=b[h],f=b[h+1],this.checkCross(c,d,e,f)&&g++;return e=b[b.length-1],f=b[0],this.checkCross(c,d,e,f)&&g++,g%2==0?!1:!0},this.isContains=function(a){if("undefined"!=typeof this.pixels[0].x)return this.containsPoint(a,this.pixels);for(var b=0,c=0;c<this.pixels.length;c++)this.containsPoint(a,this.pixels[c])&&b++;return b%2==0?!1:!0},this.setStyle=function(a){a&&(a.fillColor&&(this.fillColor=a.fillColor),a.strokeColor&&(this.strokeColor=a.strokeColor))}};MapPlatForm.Base.MapGeoRegion=function(a,b){this.CLASS_NAME="MapGeoRegion",this._map=a,this._regions=[],this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.acvtiveRegion=null,this.mouseOver=b?b.mouseOver:null,this.mouseOut=b?b.mouseOut:null,this.fontAvoid=b&&b.fontAvoid===!1?!1:!0,this.click=b?b.click:null,this.minZoom=b?b.minZoom:0,this.maxZoom=b?b.maxZoom:22},MapPlatForm.Base.MapGeoRegion.prototype._redraw=function(){var a=this._map.getZoom();if(!(a<this.minZoom||a>this.maxZoom)){this.acvtiveRegion=null,this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var b=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),c=this._map._mapAdapter.map.resolution,d=this._map._mapAdapter.map.getPixelFromLonLat(b),e=0;e<this._regions.length;e++)this._regions[e].reset(b,d,c),this._regions[e].draw();for(var f=[],e=0;e<this._regions.length;e++){var g=!0;if(this.fontAvoid)for(var h=0;h<f.length;h++){var i=this.context.measureText(f[h].name);g=Math.abs(f[h].centerXY.x-this._regions[e].centerXY.x)>i.width&&Math.abs(f[h].centerXY.y-this._regions[e].centerXY.y)>14?!0:!1}g&&(f.push(this._regions[e]),this._regions[e].drawTitle())}this.drag=!1}},MapPlatForm.Base.MapGeoRegion.prototype._clear=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height)},MapPlatForm.Base.MapGeoRegion.prototype._dragStart=function(){this.drag=!0},MapPlatForm.Base.MapGeoRegion.prototype._dragEnd=function(){this.drag=!1},MapPlatForm.Base.MapGeoRegion.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("zoomstart",this,this._clear);var a=this;this._map.getContainer().onmousemove=function(b){if(!a.drag){var c=a._map.getZoom();if(!(c<a.minZoom||c>a.maxZoom)){for(var d=b.xy,e=!1,f=0;f<a._regions.length;f++)if(a._regions[f].isContains(d)){if(a._regions[f]==a.acvtiveRegion)return;e=!0,a.mouseOut&&a.acvtiveRegion&&a.mouseOut(a.acvtiveRegion),a.acvtiveRegion=a._regions[f],a.mouseOver&&a.mouseOver(a.acvtiveRegion);break}!e&&a.acvtiveRegion&&(a.mouseOut&&a.mouseOut(a.acvtiveRegion),a.acvtiveRegion=null)}}},this._map.getContainer().onmousedown=function(b){var c=a._map.getZoom();c<a.minZoom||c>a.maxZoom||(a.curentXY=b.xy)},this._map.getContainer().onclick=function(b){if(!a.drag){var c=a._map.getZoom();if(!(c<a.minZoom||c>a.maxZoom)&&b.xy.x==a.curentXY.x&&b.xy.y==a.curentXY.y)for(var d=0;d<a._regions.length;d++)if(a._regions[d].isContains(b.xy)){isContains=!0,a.click&&a.click(a._regions[d]);break}}}},MapPlatForm.Base.MapGeoRegion.prototype.preventFlash=function(){this._map._mapAdapter.map.useMultiMarkers=!0},MapPlatForm.Base.MapGeoRegion.prototype.addGeoRegions=function(b){for(var c=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),d=this._map._mapAdapter.map.resolution,e=this._map._mapAdapter.map.getPixelFromLonLat(c),f=0;f<b.length;f++){var g=b[f];"NPMap.Geometry.Polygon"!==b[f].CLASS_NAME&&(g=GeoJSON.read(b[f].geometry),b[f].properties&&(g.data=b[f].properties));var h=new a(g,this._map,this.context);h.reset(c,e,d),h.draw(),this._regions.push(h)}for(var i=[],j=0;j<this._regions.length;j++){var k=!0;if(this.fontAvoid)for(var f=0;f<i.length;f++){var l=this.context.measureText(i[f].name);k=Math.abs(i[f].centerXY.x-this._regions[j].centerXY.x)>l.width&&Math.abs(i[f].centerXY.y-this._regions[j].centerXY.y)>14?!0:!1}k&&(i.push(this._regions[j]),this._regions[j].drawTitle())}this._initInteractive()},MapPlatForm.Base.MapGeoRegion.prototype.destory=function(){this._map.getContainer().onclick=null,this._map.getContainer().onmousemove=null,this._map.getContainer().onmousedown=null,this._map._mapAdapter.map.events.unregister("moveend",this,this._redraw),this._map._mapAdapter.map.events.unregister("zoomstart",this,this._clear),this._regions=[],this.canvas.remove()},MapPlatForm.Base.MapGeoRegion.prototype.refresh=function(){var a=this._map.getZoom();if(!(a<this.minZoom||a>this.maxZoom)){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var b=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),c=(this._map._mapAdapter.map.resolution,this._map._mapAdapter.map.getPixelFromLonLat(b),0);c<this._regions.length;c++)this._regions[c].draw();for(var d=[],c=0;c<this._regions.length;c++){var e=!0;if(this.fontAvoid)for(var f=0;f<d.length;f++){var g=this.context.measureText(d[f].name);e=Math.abs(d[f].centerXY.x-this._regions[c].centerXY.x)>g.width&&Math.abs(d[f].centerXY.y-this._regions[c].centerXY.y)>14?!0:!1}e&&(d.push(this._regions[c]),this._regions[c].drawTitle())}}}}(),function(){var a=function(a,b,c,d){var e=a.getPath();this.lonLats=[];for(var f=0;f<e.length;f++)this.lonLats.push(NPMapLib.T.setPoints(b,e[f]));this.polygon=a,this.map=b,this.context=c,this.opts=d,this.visibile=!0,this.pixels=[],this.nears=[],this.defaultStrokeColor="rgb(255,255,255)",this.strokeColor=this.defaultStrokeColor,this.fillColor=(this.strokeColor.substring(0,this.strokeColor.lastIndexOf(")"))+",1)").replace("rgb","rgba"),this.defaultFillColor=this.fillColor,this.backOffset=[40,0],this.acvtiveOffset=[5,0],this.font="18px 微软雅黑",this.fontColor="#000000",this.value=1,this.mainValue=1e3,a.center&&(this.center=GeoJSON.read(a.center),this.center=NPMapLib.T.setPoint(b,this.center)),a.name&&(this.name=a.name),this.resetStyle=function(){this.fillColor=this.defaultFillColor,this.strokeColor=this.defaultStrokeColor},this.reset=function(a,b,c,d){this.resetStyle(),this.pixels=[];for(var e=0,f=0,g=0,h=0;h<this.lonLats.length;h++){for(var i=[],j=0;j<this.lonLats[h].length;j++){{this.lonLats[h][j]}i.push({x:b.x+(this.lonLats[h][j].lon-a.lon)/c,y:b.y-(this.lonLats[h][j].lat-a.lat)/c}),e+=i[j].x,f+=i[j].y,g++}this.pixels.push(i)}this.centerXY={x:e/g,y:f/g},this.center&&d&&(this.centerXY={x:b.x+(this.center.lon-a.lon)/c,y:b.y-(this.center.lat-a.lat)/c})},this.draw=function(){if(0!=this.pixels.length)for(var a=0;a<this.pixels.length;a++){var b=this.pixels[a];this.context.beginPath(),this.context.moveTo(b[0].x,b[0].y);for(var c=1;c<b.length;c++)this.context.lineTo(b[c].x,b[c].y);if(this.context.closePath(),this.gradientColor1&&this.gradientColor2){var d=this.context.createLinearGradient(this.context.canvas.width/2,0,this.context.canvas.width/2,this.context.canvas.height);d.addColorStop(0,this.gradientColor1),d.addColorStop(1,this.gradientColor2),this.context.fillStyle=d,this.context.fill()}else this.context.fillStyle=this.fillColor,this.context.fill()}},this.drawLine=function(){if(0!=this.pixels.length)for(var a=0;a<this.pixels.length;a++){var b=this.pixels[a];this.context.beginPath(),this.context.lineJoin="round",this.context.moveTo(b[0].x,b[0].y);for(var c=1;c<b.length;c++)this.context.lineTo(b[c].x,b[c].y);this.context.closePath(),this.context.strokeStyle=this.strokeColor,this.context.lineWidth=1,this.context.stroke()}},this.drawTJ=function(a){if("undefined"!=typeof this.value){var b=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value,this.centerXY.x-5,this.centerXY.y-20);a?(b.addColorStop(0,this._getOpacityColor(this.TJActivecolor)),b.addColorStop(1,this.TJActivecolor)):(b.addColorStop(0,this._getOpacityColor(this.TJcolor)),b.addColorStop(1,this.TJcolor)),this.context.fillStyle=b,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value,10,85*this.value),this.mainValue>0&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-20-.5,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=a?this.TJActivetextcolor:this.TJtextcolor,this.context.fillText(this.mainValue,this.centerXY.x,this.centerXY.y-85*this.value-30),this.context.restore())}},this._getOpacityColor=function(a){return a.indexOf("rgba")>-1?a=a.substring(0,a.lastIndexOf(","))+",0)":a.indexOf("rgb")>-1&&(a=a.replace("rgb","rgba"),a=a.substring(0,xx.indexOf(")"))+",0)"),a},this.drawTJ2=function(a){if("undefined"!=typeof this.value){var b=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value*a,this.centerXY.x-5,this.centerXY.y-20);b.addColorStop(0,this._getOpacityColor(this.TJcolor)),b.addColorStop(1,this.TJcolor),this.context.fillStyle=b,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value*a,10,85*this.value*a),this.mainValue>0&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-.5-20,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=this.TJtextcolor,this.context.fillText(Math.ceil(this.mainValue*a),this.centerXY.x,this.centerXY.y-85*this.value*a-30),this.context.restore())}},this.drawBack=function(){if(0!=this.pixels.length){for(var a=0;a<this.pixels.length;a++)for(var b=this.pixels[a],c=1;c<b.length-1;c++){this.context.save(),this.context.beginPath(),this.context.moveTo(b[c].x+this.backOffset[0],b[c].y+this.backOffset[1]),this.context.lineTo(b[c+1].x+this.backOffset[0],b[c+1].y+this.backOffset[1]),this.context.lineTo(b[c+1].x,b[c+1].y),this.context.lineTo(b[c].x,b[c].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(b[c].x+this.backOffset[0],b[c].y+this.backOffset[1]),this.context.lineTo(b[c+1].x+this.backOffset[0],b[c+1].y+this.backOffset[1]),this.context.lineTo(b[c+1].x,b[c+1].y),this.context.lineTo(b[c].x,b[c].y),this.context.closePath();var d=(b[c+1].x+b[c].x)/2,e=(b[c+1].y+b[c].y)/2,f=this.context.createLinearGradient(d,e,d+this.backOffset[0],e+this.backOffset[1]);f.addColorStop(0,this.backShaderColor),f.addColorStop(1,this.backShaderColor2),this.context.fillStyle=f,this.context.fill()}for(var a=0;a<this.pixels.length;a++){this.context.save();var b=this.pixels[a];this.context.beginPath(),this.context.moveTo(b[0].x,b[0].y);for(var c=1;c<b.length;c++)this.context.lineTo(b[c].x,b[c].y);this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore()}}},this.drawActive=function(){if(0!=this.pixels.length)if(this.JianBian)for(var a=0;a<this.pixels.length;a++){var b=this.pixels[a];this.context.beginPath(),this.context.moveTo(b[0].x,b[0].y);for(var c=1;c<b.length;c++)this.context.lineTo(b[c].x,b[c].y);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}else{for(var a=0;a<this.pixels.length;a++)for(var b=this.pixels[a],c=1;c<b.length-1;c++)this.context.save(),this.context.beginPath(),this.context.moveTo(b[c].x-this.acvtiveOffset[0],b[c].y-this.acvtiveOffset[1]),this.context.lineTo(b[c+1].x-this.acvtiveOffset[0],b[c+1].y-this.acvtiveOffset[1]),this.context.lineTo(b[c+1].x,b[c+1].y),this.context.lineTo(b[c].x,b[c].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(b[c].x-this.acvtiveOffset[0],b[c].y-this.acvtiveOffset[1]),this.context.lineTo(b[c+1].x-this.acvtiveOffset[0],b[c+1].y-this.acvtiveOffset[1]),this.context.lineTo(b[c+1].x,b[c+1].y),this.context.lineTo(b[c].x,b[c].y),this.context.closePath(),this.context.fillStyle=this.backShaderColor,this.context.fill();for(var a=0;a<this.pixels.length;a++){var b=this.pixels[a];this.context.beginPath(),this.context.moveTo(b[0].x-this.acvtiveOffset[0],b[0].y-this.acvtiveOffset[1]);for(var c=1;c<b.length;c++)this.context.lineTo(b[c].x-this.acvtiveOffset[0],b[c].y-this.acvtiveOffset[1]);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}}},this.drawTitle=function(){this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font=this.font,this.context.fillStyle=this.fontColor,this.context.fillText(this.name,this.centerXY.x,this.centerXY.y),this.context.restore())},this.crossMul=function(a,b){return a.x*b.y-a.y*b.x},this.checkCross=function(a,b,c,d){var e={x:a.x-c.x,y:a.y-c.y},f={x:b.x-c.x,y:b.y-c.y},g={x:d.x-c.x,y:d.y-c.y},h=this.crossMul(e,g)*this.crossMul(f,g);return e={x:c.x-a.x,y:c.y-a.y},f={x:d.x-a.x,y:d.y-a.y},g={x:b.x-a.x,y:b.y-a.y},0>=h&&this.crossMul(e,g)*this.crossMul(f,g)<=0?!0:!1},this.containsPoint=function(a,b){var c,d,e,f;c=a,d={x:-1e5,y:a.y};for(var g=0,h=0;h<b.length-1;h++)e=b[h],f=b[h+1],this.checkCross(c,d,e,f)&&g++;return e=b[b.length-1],f=b[0],this.checkCross(c,d,e,f)&&g++,g%2==0?!1:!0},this.isContains=function(a){if("undefined"!=typeof this.pixels[0].x)return this.containsPoint(a,this.pixels);for(var b=0,c=0;c<this.pixels.length;c++)this.containsPoint(a,this.pixels[c])&&b++;return b%2==0?!1:!0},this.setStyle=function(a){a&&(a.fillColor&&(this.fillColor=a.fillColor),a.strokeColor&&(this.strokeColor=a.strokeColor))}};MapPlatForm.Base.ShapMap=function(a,b,c){this.CLASS_NAME="MapPlatForm.Base.ShapMap",this.fullExtent=this._getExtent(b),this.scrollTop=0,this._map=this._createMap(a,c),this._regions=[],this._backRegion=null,this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.canvasActive=document.createElement("canvas"),this.canvasActive.style.position="absolute",this.canvasActive.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvasActive.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvasActive.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvasActive.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvasActive.style.pointerEvents="none",this.canvasActive.style.zIndex=206,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvasActive),this.canvasActive.setAttribute("class","olScrollable"),this.contextActive=this.canvasActive.getContext("2d"),this.points=c&&c.points?c.points:[],this.acvtiveRegion=null,this.legendColor=c&&c.legendColor?c.legendColor:{},this.legendValue=c&&c.legendValue?c.legendValue:{},this.maxValue=0,this.showAllTJ=c&&c.showAllTJ===!1?!1:!0,this.showAllNames=c&&c.showAllNames?!0:!1;for(var d in this.legendValue)this.legendValue[d]>this.maxValue&&(this.maxValue=this.legendValue[d]);this.maxValue=this.maxValue?this.maxValue:100,"{}"===JSON.stringify(this.legendColor)?(this.JianBian=!0,this.gradientColor1=c&&c.gradientColor1?c.gradientColor1:"#3f96c3",this.gradientColor2=c&&c.gradientColor2?c.gradientColor2:"#175579",this.hoverColor=c&&c.hoverColor?c.hoverColor:"#154e6e"):this.hoverColor=c&&c.hoverColor?c.hoverColor:null,this.backShaderColor=this.JianBian?c&&c.backShaderColor?c.backShaderColor:"#11a4b5":c&&c.backShaderColor?c.backShaderColor:"#bcdefd",this.backShaderColor2=c&&c.backShaderColor2?c.backShaderColor2:this.backShaderColor,this.strokeColor=this.JianBian?c&&c.strokeColor?c.strokeColor:"#15b1e5":c&&c.strokeColor?c.strokeColor:"#FFFFFF",this.TJcolor=c&&c.TJcolor?c.TJcolor:"rgba(255,198,0,255)",this.TJtextcolor=c&&c.TJtextcolor?c.TJtextcolor:this.TJcolor,this.TJActivecolor=c&&c.TJActivecolor?c.TJActivecolor:"rgba(242,19,75,255)",this.TJActivetextcolor=c&&c.TJActivetextcolor?c.TJActivetextcolor:this.TJActivecolor,this.backOffset=c&&c.backOffset?c.backOffset:[40,0],this.acvtiveOffset=c&&c.acvtiveOffset?c.acvtiveOffset:[5,0],this.font=c&&c.font?c.font:"14px 宋体",this.fontColor=c&&c.fontColor?c.fontColor:"#000000",this.shadowColor=c&&c.shadowColor?c.shadowColor:"rgba(61,154,235,0.2)",this.fillColors=["#7fc3ff","#3d9aeb","#2a7cc4","#215e94"],this.mouseOver=c?c.mouseOver:null,this.mouseOut=c?c.mouseOut:null,this.mouseMove=c?c.mouseMove:null,this.click=c?c.click:null,this.starRadio=c?c.starRadio:3,this.starColor=c?c.starColor:"rgba(255,255,255,0.8)",this._initPoints(),this.addGeoRegions(b)},MapPlatForm.Base.ShapMap.prototype._createMap=function(a,b){var c=new NPMapLib.Map(a,{minZoom:0,maxZoom:21}),d=a.clientWidth?a.clientWidth:2880,e=a.clientHeight?a.clientHeight:1800,f=b&&b.backOffset?b.backOffset:[40,40],g=null,h=new NPMapLib.Layers.ImageLayer(g,"BaseLayer",{size:[d-2*f[0],e-2*f[0]],fullExtent:[this.fullExtent.left,this.fullExtent.bottom,this.fullExtent.right,this.fullExtent.top]});return c.addLayer(h),b&&!b.mapControl&&c._mapAdapter._navigator.deactivate(),c},MapPlatForm.Base.ShapMap.prototype._redraw=function(){this.acvtiveRegion=null,this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvasActive.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";var a=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),b=this._map._mapAdapter.map.getResolution(),c=this._map._mapAdapter.map.getPixelFromLonLat(a);this._backRegion.reset(a,c,b),this._backRegion.drawBack(),this._resetAndDrawPoint(a,c,b),this.JianBian&&this._backRegion.draw(),this.v=0,this._anim(),this.drag=!1},MapPlatForm.Base.ShapMap.prototype._clear=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.contextActive.clearRect(0,0,this.canvasActive.width,this.canvasActive.height)},MapPlatForm.Base.ShapMap.prototype._dragStart=function(){this.drag=!0},MapPlatForm.Base.ShapMap.prototype._dragEnd=function(){this.drag=!1},MapPlatForm.Base.ShapMap.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("zoomstart",this,this._clear),this._map._mapAdapter.map.events.register("dragstart",this,this._dragStart),this._map._mapAdapter.map.events.register("dragend",this,this._dragEnd);var a=this;this._map.getContainer().onmousemove=function(b){if(!a.drag){var c=b.xy;c.y=c.y+a.scrollTop;for(var d=!1,e=0;e<a._regions.length;e++)if(a._regions[e].isContains(c)){if(a._regions[e]==a.acvtiveRegion)return void(a.mouseMove&&a.mouseMove(a.acvtiveRegion,c));d=!0,a.mouseOut&&a.acvtiveRegion&&(a.refresh(),a.mouseOut(a.acvtiveRegion)),a.acvtiveRegion=a._regions[e],a.mouseOver&&(a.refresh(a.acvtiveRegion),a.acvtiveRegion.drawTJ(!0),a.acvtiveRegion.drawTitle(),a.mouseOver(a.acvtiveRegion,c)),a.mouseMove&&a.mouseMove(a.acvtiveRegion,c);break}!d&&a.acvtiveRegion&&(a.mouseOut&&(a.refresh(),a.mouseOut(a.acvtiveRegion)),a.acvtiveRegion=null)}},this._map.getContainer().onmousedown=function(b){a.curentXY=b.xy},this._map.getContainer().onclick=function(b){if(!a.drag&&b.xy.x==a.curentXY.x&&b.xy.y==a.curentXY.y){var c=b.xy;c.y=c.y+a.scrollTop;for(var d=0;d<a._regions.length;d++)if(a._regions[d].isContains(c)){isContains=!0,a.click&&a.click(a._regions[d]);break}}}},MapPlatForm.Base.ShapMap.prototype.preventFlash=function(){this._map._mapAdapter.map.useMultiMarkers=!0},MapPlatForm.Base.ShapMap.prototype._initPoints=function(){for(var a=[],b=Math.ceil(this.points.length/1e3),c=0;c<this.points.length;c+=b)a.push(this.points[c]);this.points=a},MapPlatForm.Base.ShapMap.prototype._resetAndDrawPoint=function(a,b,c){for(var d=0;d<this.points.length;d++){var e=b.x+(this.points[d][0]-a.lon)/c,f=b.y-(this.points[d][1]-a.lat)/c;this.context.beginPath();var g=this.context.createRadialGradient(e,f,0,e,f,this.starRadio);g.addColorStop(0,this.starColor),g.addColorStop(1,"rgba(255,255,255,0)"),this.context.fillStyle=g,this.context.arc(e,f,this.starRadio,0,2*Math.PI,!0),this.context.closePath(),this.context.fill()}},MapPlatForm.Base.ShapMap.prototype.addGeoRegions=function(b){var c=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),d=this._map._mapAdapter.map.getResolution(),e=this._map._mapAdapter.map.getPixelFromLonLat(c),f=GeoJSON.read(b.geometry);if(f.name=b.name,f.center=b.center,this._backRegion=new a(f,this._map,this.context),this._backRegion.strokeColor=this.strokeColor,this._backRegion.backOffset=this.backOffset,this._backRegion.backShaderColor=this.backShaderColor,this._backRegion.backShaderColor2=this.backShaderColor2,this._backRegion.shadowColor=this.shadowColor,this._backRegion.reset(c,e,d),this._backRegion.drawBack(),this._resetAndDrawPoint(c,e,d),this.JianBian&&(this._backRegion.gradientColor1=this.gradientColor1,this._backRegion.gradientColor2=this.gradientColor2,this._backRegion.JianBian=this.JianBian,this._backRegion.draw()),b.districts)for(var g=0;g<b.districts.length;g++)if(b.districts[g]){var h=GeoJSON.read(b.districts[g].geometry);h.name=b.districts[g].name,h.center=b.districts[g].center,h.id=b.districts[g].code;var i=new a(h,this._map,this.contextActive);i.defaultStrokeColor=this.strokeColor,i.strokeColor=this.strokeColor,i.defaultFillColor=this.legendColor[h.name]?this.legendColor[h.name]:this.fillColors[g%4],i.fillColor=this.legendColor[h.name]?this.legendColor[h.name]:this.fillColors[g%4],i.hoverColor=this.hoverColor?this.hoverColor:i.fillColor,i.JianBian=this.JianBian,i.backShaderColor=this.backShaderColor,i.backShaderColor2=this.backShaderColor2,i.acvtiveOffset=this.acvtiveOffset,i.font=this.font,i.fontColor=this.fontColor,i.value="undefined"!=typeof this.legendValue[h.name]?this.legendValue[h.name]/this.maxValue:void 0,i.mainValue=this.legendValue[h.name]?this.legendValue[h.name]:0,i.TJcolor=this.TJcolor,i.TJtextcolor=this.TJtextcolor,i.TJActivecolor=this.TJActivecolor,i.TJActivetextcolor=this.TJActivetextcolor,i.reset(c,e,d),this._regions.push(i)}this.v=0,this._anim(),this._initInteractive(),this._map.zoomToExtent(this.fullExtent)},MapPlatForm.Base.ShapMap.prototype._anim=function(){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvasActive.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var a=0;a<this._regions.length;a++){var b=this._regions[a];this.JianBian?b.drawLine(!0):b.draw(!0),this.showAllNames&&b.drawTitle()}if(this.showAllTJ){this.v=this.v+.02;for(var c=0;c<this._regions.length;c++)this._regions[c].drawTJ2(this.v);var d=this;this.v<1&&setTimeout(function(){d._anim()},2)}},MapPlatForm.Base.ShapMap.prototype._getExtent=function(a){for(var b,c,d,e,f=a.geometry.coordinates,g=0;g<f.length;g++)for(var h=0;h<f[g].length;h++)for(var i=f[g][h],j=0;j<i.length;j++)0==h&&0==j&&0==g?(b=i[j][0],d=i[j][1],c=i[j][0],e=i[j][1]):(b>i[j][0]&&(b=i[j][0]),d<i[j][0]&&(d=i[j][0]),c>i[j][1]&&(c=i[j][1]),e<i[j][1]&&(e=i[j][1]));return new NPMapLib.Geometry.Extent(b,c,d,e)},MapPlatForm.Base.ShapMap.prototype.destory=function(){this._map.getContainer().onclick=null,this._map.getContainer().onmousemove=null,this._map.getContainer().onmousedown=null,this._map._mapAdapter.map.events.unregister("moveend",this,this._redraw),this._map._mapAdapter.map.events.unregister("zoomstart",this,this._clear),this._map._mapAdapter.map.events.unregister("dragstart",this,this._dragStart),this._map._mapAdapter.map.events.unregister("dragend",this,this._dragEnd),this._map&&(this._map.destroyMap(),this._map=null),this._regions=[],this._backRegion=null,this.canvas.remove(),this.canvasActive.remove()},MapPlatForm.Base.ShapMap.prototype.refresh=function(a){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvasActive.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var b=0;b<this._regions.length;b++){var c=this._regions[b];this.JianBian?c.drawLine(!0):c.draw(!0),this.showAllNames&&c.drawTitle()}if(a&&a.drawActive(),this.showAllTJ)for(var d=0;d<this._regions.length;d++)this._regions[d].drawTJ()},MapPlatForm.Base.ShapMap.prototype.updateSize=function(){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvasActive.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvasActive.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvasActive.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this._map.zoomToExtent(this.fullExtent,!1,!0),this._redraw()}}(),function(){var a=function(a,b,c,d){var e=a.getPath();if(e[0].lon)this.lonLats=NPMapLib.T.setPoints(b,e);else{this.lonLats=[];for(var f=0;f<e.length;f++)this.lonLats.push(NPMapLib.T.setPoints(b,e[f]))}var g=function(){var a=Math.floor(256*Math.random()),b=255,c=Math.floor(256*Math.random());return"rgb("+a+","+b+","+c+")"};this.polyline=a,this.map=b,this.context=c,this.opts=d,this.visibile=!0,this.pixels=[],this.defaultStrokeColor=g(),this.strokeColor=this.defaultStrokeColor,this.font="14px 宋体",this.fontColor="#000000",a.data&&a.data.strokeColor&&(this.defaultStrokeColor=a.data.strokeColor,this.strokeColor=a.data.strokeColor),this.reset=function(a,b,c){this.pixels=[];for(var d=0;d<this.lonLats.length;d++)if(this.lonLats[0].lon)this.pixels.push({x:b.x+(this.lonLats[d].lon-a.lon)/c,y:b.y-(this.lonLats[d].lat-a.lat)/c});else{for(var e=[],f=0;f<this.lonLats[d].length;f++){{this.lonLats[d][f]}e.push({x:b.x+(this.lonLats[d][f].lon-a.lon)/c,y:b.y-(this.lonLats[d][f].lat-a.lat)/c})}this.pixels.push(e)}},this.draw=function(){if(0!=this.pixels.length)if("undefined"!=typeof this.pixels[0].x){this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var a=1;a<this.pixels.length;a++)this.context.lineTo(this.pixels[a].x,this.pixels[a].y);this.context.strokeStyle=this.strokeColor,this.context.lineWidth=2,this.context.stroke()}else for(var b=0;b<this.pixels.length;b++){var c=this.pixels[b];this.context.beginPath(),this.context.moveTo(c[0].x,c[0].y);for(var a=1;a<c.length;a++)this.context.lineTo(c[a].x,c[a].y);this.context.strokeStyle=this.strokeColor,this.context.lineWidth=1,this.context.stroke()}},this.setStyle=function(a){a&&a.strokeColor&&(this.strokeColor=a.strokeColor)}};MapPlatForm.Base.MapGeoLine=function(a,b){this.CLASS_NAME="MapGeoLine",this._map=a,this._lines=[],this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.mouseOver=b?b.mouseOver:null,this.mouseOut=b?b.mouseOut:null,this.click=b?b.click:null},MapPlatForm.Base.MapGeoLine.prototype._redraw=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var a=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),b=this._map._mapAdapter.map.resolution,c=this._map._mapAdapter.map.getPixelFromLonLat(a),d=0;d<this._lines.length;d++)this._lines[d].reset(a,c,b),this._lines[d].draw();this.drag=!1},MapPlatForm.Base.MapGeoLine.prototype._clear=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height)},MapPlatForm.Base.MapGeoLine.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("zoomstart",this,this._clear)},MapPlatForm.Base.MapGeoLine.prototype.preventFlash=function(){this._map._mapAdapter.map.useMultiMarkers=!0},MapPlatForm.Base.MapGeoLine.prototype.addGeoLines=function(b){for(var c=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),d=this._map._mapAdapter.map.resolution,e=this._map._mapAdapter.map.getPixelFromLonLat(c),f=0;f<b.length;f++){var g=b[f];"NPMap.Geometry.Polyline"!==b[f].CLASS_NAME&&(g=b[f],b[f].properties&&(g.data=b[f].properties));var h=new a(g,this._map,this.context);h.reset(c,e,d),h.draw(),this._lines.push(h)}this._initInteractive()},MapPlatForm.Base.MapGeoLine.prototype.destory=function(){this._map.getContainer().onclick=null,this._map.getContainer().onmousemove=null,this._map.getContainer().onmousedown=null,this._map._mapAdapter.map.events.unregister("moveend",this,this._redraw),this._map._mapAdapter.map.events.unregister("zoomstart",this,this._clear),this._map._mapAdapter.map.events.unregister("dragstart",this,this._dragStart),this._map._mapAdapter.map.events.unregister("dragend",this,this._dragEnd),this._lines=[],this.canvas.remove()
},MapPlatForm.Base.MapGeoLine.prototype.refresh=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var a=(this._map.getExtent(),this._map._mapAdapter.map.getCenter()),b=(this._map._mapAdapter.map.resolution,this._map._mapAdapter.map.getPixelFromLonLat(a),0);b<this._lines.length;b++)this._lines[b].draw()}}(),function(){MapPlatForm.Base.MapVector=function(a){this.CLASS_NAME="MapVector",this._map=a,this._markers=[],this._showMarkers=[],this._imgMarkers={},this.vectorLayerDom=document.createElement("div"),this.vectorLayerDom.id="MapVectorLayer",this.vectorLayerDom.style.position="absolute",this.vectorLayerDom.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.vectorLayerDom.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.vectorLayerDom.style.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth+"px",this.vectorLayerDom.style.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight+"px",this.vectorLayerDom.style.pointerEvents="none",this.vectorLayerDom.style.zIndex=120,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.vectorLayerDom),this._map._mapAdapter.map.zoomTween=null,this._map._mapAdapter._navigator.dragPan.kinetic=null},MapPlatForm.Base.MapVector.prototype._redraw=function(){var a=this._map._mapAdapter.map;this.vectorLayerDom.style.left=0-parseFloat(a.layerContainerDiv.style.left.replace("px",""))+"px",this.vectorLayerDom.style.top=0-parseFloat(a.layerContainerDiv.style.top.replace("px",""))+"px",this.vectorLayerDom.style.width=a.viewPortDiv.offsetWidth+"px",this.vectorLayerDom.style.height=a.viewPortDiv.offsetHeight+"px";var b=a.getCenter(),c=NPMap.T.helper.inverseMercator(b.lon,b.lat);c=NPMap.T.helper.gcj2wgs(c.lon,c.lat);var d=this._map.getZoom();this.vectorLayer.setCenter({lng:c.lon,lat:c.lat}),this.vectorLayer.setZoom(d-1)},MapPlatForm.Base.MapVector.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("updatesize",this,this._redraw)},MapPlatForm.Base.MapVector.prototype.preventFlash=function(){this._map._mapAdapter.map.useMultiMarkers=!0},MapPlatForm.Base.MapVector.prototype.load=function(a){a||(a={});var b=(this._map.getExtent(),this._map._mapAdapter.map.getCenter());b=NPMap.T.helper.inverseMercator(b.lon,b.lat),b=NPMap.T.helper.gcj2wgs(b.lon,b.lat),this.vectorLayer=new d2c.map({container:this.vectorLayerDom.id,center:[b.lon,b.lat],style:a.style,zoom:this._map.getMinZoom()-1,maxZoom:this._map.getMaxZoom()-1,localIdeographFontFamily:a.localIdeographFontFamily});var c=this;a.buildLayer&&void 0!=a.height&&this.vectorLayer.on("style.load",function(){c.vectorLayer.style.setPaintProperty(a.buildLayer,"fill-extrusion-height",a.height)});var d=document.getElementsByClassName("d2cgl-canvas")[0];d.className=d.className+" olScrollable",this.vectorLayer.scrollZoom.isActive=!1,this.vectorLayer.dragPan.isActive=!1,this.vectorLayer.dragRotate.isActive=!1,this.oldCenter=this._map._mapAdapter.map.getCenter(),this._initInteractive()}}(),function(){var a=MapPlatForm.Base.MapCanvasLayer=function(a){this._map=a,this._overlays=[],this.pixels=[],this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px",this.canvas.width=this._map._mapAdapter.map.viewPortDiv.offsetWidth,this.canvas.height=this._map._mapAdapter.map.viewPortDiv.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,this._map._mapAdapter.map.layerContainerDiv.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this._initInteractive()};a.prototype.addOverlay=function(a){this._overlays.push(a),this.refresh()},a.prototype.addOverlays=function(a){for(var b=0;b<a.length;b++)this._overlays.push(a[b]);this.refresh()},a.prototype.hasOverlay=function(a){for(var b=0;b<this._overlays.length;b++)if(this._overlays[b]==a)return!0;return!1},a.prototype.removeAllOverlays=function(){this._overlays=[]},a.prototype.removeOverlay=function(a){for(var b=0;b<this._overlays.length;b++)if(this._overlays[b]==a){delete this._overlays[b],this._overlays.splice(b,1);break}},a.prototype._initInteractive=function(){this._map._mapAdapter.map.events.fallThrough=!0,this._map._mapAdapter.map.events.register("moveend",this,this._redraw),this._map._mapAdapter.map.events.register("updatesize",this,this._redraw)},a.prototype._redraw=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var a=0;a<this._overlays.length;a++)"NPMap.Geometry.Polyline"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a],this._overlays[a].opts),"NPMap.Geometry.Polygon"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a],this._overlays[a].opts),"NPMap.Symbols.Marker"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a],this._overlays[a].opts)},a.prototype.draw=function(a){this._reset(a),"NPMap.Geometry.Polyline"===a.CLASS_NAME&&this._drawPolyLine(a),"NPMap.Geometry.Polygon"===a.CLASS_NAME&&this._drawPolygon(a),"NPMap.Symbols.Marker"===a.CLASS_NAME&&this._drawMarker(a)},a.prototype._reset=function(a){if("NPMap.Symbols.Marker"===a.CLASS_NAME){var b=NPMap.T.setPoint(this._map,a._position),c=this._map._mapAdapter.map.getPixelFromLonLat(b);a.pixel=c}else{var d=this._map._mapAdapter.map.getCenter(),e=this._map._mapAdapter.map.resolution,f=this._map._mapAdapter.map.getPixelFromLonLat(d);a.mapPoints=NPMap.T.setPoints(this._map,a._points),a.pixels=[];for(var g=0;g<a.mapPoints.length;g++)a.pixels.push({x:f.x+(a.mapPoints[g].lon-d.lon)/e,y:f.y-(a.mapPoints[g].lat-d.lat)/e})}},a.prototype._drawPolyLine=function(a){this.context.beginPath(),this.context.moveTo(a.pixels[0].x,a.pixels[0].y);for(var b=1;b<a.pixels.length;b++)this.context.lineTo(a.pixels[b].x,a.pixels[b].y);this.context.strokeStyle=a._color,this.context.lineWidth=a._weight,this.context.stroke()},a.prototype._drawPolygon=function(a){this.context.beginPath(),this.context.rect(a.pixels[0].x,a.pixels[0].y,a.pixels[2].x-a.pixels[0].x,a.pixels[2].y-a.pixels[0].y),this.context.strokeStyle=a._color,this.context.fillStyle=a._fillColor,this.context.lineWidth=a._weight,this.context.fill(),this.context.stroke()},a.prototype._drawMarker=function(a){var b=document.createElement("img");b.src=a._icon._imageUrl,this.context.width=a.opts.width,this.context.height=a.opts.height,this.context.drawImage(b,a.pixel.x,a.pixel.y)},a.prototype.refresh=function(){this.context.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvas.style.left=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.left.replace("px",""))+"px",this.canvas.style.top=0-parseFloat(this._map._mapAdapter.map.layerContainerDiv.style.top.replace("px",""))+"px";for(var a=0;a<this._overlays.length;a++)"NPMap.Geometry.Polyline"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a]),"NPMap.Geometry.Polygon"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a]),"NPMap.Symbols.Marker"===this._overlays[a].CLASS_NAME&&this.draw(this._overlays[a])},a.prototype.destory=function(){this._map._mapAdapter.map.events.fallThrough=!1,this._map._mapAdapter.map.events.unregister("moveend",this,this._redraw),this._map._mapAdapter.map.events.unregister("updatesize",this,this._redraw),this.canvas.remove()}}(),function(){var a=MapPlatForm.Base.AnimationLine=function(a,b,c){this.CLASS_NAME="MapPlatForm.Base.AnimationLine",this.points,this.op_line,this.op_feature,this._layer,this.drawIndex=0,this.drawSpeed,this.isWait=!1,this.isAddPoint=!1,this.segArray,this.segIndex=0,this.panWay=-1,this.status,this.zoomStop=!1,this.interval=50,this.newStart=!0,this.events=new NPMap.Events(this,["preDraw","afterDraw","afterStep"]),this.headerMarker=null,this.isHeaderRotate=!0,this.timer=null,this.lineStyle={color:"green"},c&&(c.color&&(this.lineStyle.color=c.color),c.opacity&&(this.lineStyle.opacity=c.opacity),c.weight&&(this.lineStyle.weight=c.weight)),this.id=-1,this._points=new Array,this._listeners={},this.map=a,this.mapId=a.id,this.layerName="动画线",this.mapAdapter=NPMap.Adapter.AdapterFactory.getMapAdapter(this.mapId),this.points=b,this.points instanceof Array||(this.points=[]),this.drawSpeed=15,this.status=0,c&&(this.headerMarker=c.headerMarker,this._layer=c.layer,this.isHeaderRotate=c.isHeaderRotate===!1?!1:!0,c.renderFirst&&this._initInteractive()),this._init()};a.prototype._initInteractive=function(){var a=this;this.map._mapAdapter.map.events.fallThrough=!0,this.map._mapAdapter.map.events.register("zoomstart",this,function(){a.status>0&&(a.stop(),a.zoomStop=!0)}),this.map._mapAdapter.map.events.register("movestart",this,function(){a.status>0&&(a.stop(),a.zoomStop=!0)}),this.map._mapAdapter.map.events.register("moveend",this,function(){a.zoomStop&&(a.start(),a.zoomStop=!1)})},a.prototype.restart=function(){this.op_line&&this._layer.hasOverlay(this.op_line)&&this._layer.removeOverlay(this.op_line),this.stop(),this._init(),this.start()},a.prototype.start=function(){NPMap.isAnimat=!0,this.status>0||(this._triggerEvent(NPMap.ANIMATION_EVENT_START),this.status=1,this._layer||(this._layer=new MapPlatForm.Base.MapCanvasLayer(map)),this._layer.hasOverlay(this.op_line)||this._layer.addOverlay(this.op_line),this.panWay>=0&&this._suitView(this.points),this.newStart&&0==this.drawIndex&&this.points.length>0&&(this.headerMarker&&(this._rotateHeaderMarker(),this.headerMarker.moveTo(this.points[this.drawIndex])),this.events.triggerEvt("afterDraw",{point:this.points[this.drawIndex],index:this.drawIndex})),this.timer=window.setTimeout(this._startDraw.bind(this),this.interval))},a.prototype.pause=function(){NPMap.isAnimat=!1,this.status=-1},a.prototype.stop=function(){NPMap.isAnimat=!1,-1==this.status&&this._triggerEvent(NPMap.ANIMATION_EVENT_STOP),this.status=0,window.clearTimeout(this.timer)},a.prototype.setSpeed=function(a){0==a?(this.isWait=!0,this.isAddPoint=!1,this.drawSpeed=0):(this.isWait=!1,this._rotateHeaderMarker(),this.drawSpeed=a)},a.prototype.appendPoints=function(a){a instanceof Array&&!(a.length<=0)&&(this.points=this.points.concat(a),this.op_line||this._init(),this.panWay>=0&&this._suitView(a))},a.prototype.setStyle=function(a){this.lineStyle=this._overwriteObj(this.lineStyle,a),this.op_line&&this._layer&&(this.op_line.setStyle(this.lineStyle),this._redraw())},a.prototype._overwriteObj=function(a,b){if(!a||!b)return a;for(var c in b)c in a&&(a[c]=b[c]);return a},a.prototype._startDraw=function(){if(0==this.status)return void this._triggerEvent(NPMap.ANIMATION_EVENT_STOP);if(-1==this.status)return void this._triggerEvent(NPMap.ANIMATION_EVENT_PAUSE);if(!this.op_line)return void(this.status=0);var a=this._preDraw();return a?this.isWait?(this.isAddPoint||(this.headerMarker&&this.headerMarker.moveTo(this.points[this.drawIndex]),this.isAddPoint=!0,this.events.triggerEvt("afterDraw",{point:this.points[this.drawIndex],index:this.drawIndex})),void window.setTimeout(this._startDraw.bind(this),this.interval)):(this._addPoint(),void this._afterDraw()):void(this.status=0)},a.prototype.continus=function(){this.isAddPoint=!1,this.drawIndex++,this.start()},a.prototype._preDraw=function(){0==this.drawIndex&&(this.segArray=this._calculateSeg(this.drawIndex));var a=0==this.drawIndex&&0==this.segIndex,b=this.segArray.length;return this.segIndex=this.segIndex+1,a&&this.events.triggerEvt("preDraw",{point:this.points[this.drawIndex],index:this.drawIndex}),(a||this.segIndex>=b-1)&&this._rotateHeaderMarker(),this.drawIndex>=this.points.length-1?!1:(this.segIndex>=b&&(this.segIndex=0,this.segArray=this._calculateSeg(this.drawIndex)),this.panWay<=0?!0:(!this._inView(this.segArray[this.segIndex])&&this.panWay>0&&this._setCenter(this.segArray[this.segIndex]),!0))},a.prototype._addPoint=function(){this._moveHeaderMarker();var a=this.segArray[this.segIndex],b=this.op_line.getPath();if(0==this.segIndex){var c=this.points[this.drawIndex];b.push(c)}else b.length-1==this.drawIndex?b.push(a):b[b.length-1]=a;this.op_line.setPath(b),this.unRedraw||this._redraw()},a.prototype._afterDraw=function(){var a=(0==this.drawIndex&&(1==this.segIndex||0==this.segIndex),this.segIndex>=this.segArray.length-1);a&&(this.drawIndex++,this.events.triggerEvt("preDraw",{point:this.points[this.drawIndex],index:this.drawIndex})),this.drawIndex>0&&a&&(this.headerMarker&&this.headerMarker.moveTo(this.points[this.drawIndex]),this.events.triggerEvt("afterDraw",{point:this.points[this.drawIndex],index:this.drawIndex}));var b=this.segIndex;this.segIndex>this.segArray.length-2&&(b=0),this.events.triggerEvt("afterStep",{stepIndex:b,pointIndex:this.drawIndex,point:this.segArray[this.segIndex]}),this.segIndex>this.segArray.length-2&&this.drawIndex>this.points.length-2&&(this.status=0,NPMap.isAnimat=!1),this.timer=window.setTimeout(this._startDraw.bind(this),this.interval)},a.prototype.nextPosition=function(){this.newStart=!1,this.drawIndex>0&&this.drawIndex--,this.headerMarker&&this.headerMarker.moveTo(this.points[this.drawIndex]),this._rotateHeaderMarker(),this.segIndex=0,this.segArray=this._calculateSeg(this.drawIndex),this._setLine(this.drawIndex),this.events.triggerEvt("afterStep",{stepIndex:0,pointIndex:this.drawIndex,point:this.segArray[this.segIndex]})},a.prototype.afterPosition=function(){this.drawIndex<this.points.length-1&&(this.drawIndex++,this.headerMarker&&this.headerMarker.moveTo(this.points[this.drawIndex]),this._rotateHeaderMarker(),this.segIndex=0,this.segArray=this._calculateSeg(this.drawIndex),this._setLine(this.drawIndex),this.events.triggerEvt("afterStep",{stepIndex:0,pointIndex:this.drawIndex,point:this.segArray[this.segIndex]}))},a.prototype.slicePoints=function(a){this.points=this.points.slice(0,a+1)},a.prototype._setLine=function(a,b){if(this.op_line._apiObj){this.op_line._apiObj.geometry.components=[];for(var c=0;a+1>c;c++){var d=NPMap.T.setPoint(this.mapAdapter,this.points[c]),e=new OpenLayers.Geometry.Point(d.lon,d.lat);this.op_line._apiObj.geometry.components.push(e),e.parent=this.op_line._apiObj.geometry}if(b>0){var f=NPMap.T.setPoint(this.mapAdapter,this.segArray[b]),g=new OpenLayers.Geometry.Point(f.lon,f.lat);this.op_line._apiObj.geometry.components.push(g),g.parent=this.op_line._apiObj.geometry}this.op_line._apiObj.geometry.clearBounds(),this._redraw()}},a.prototype.setLayer=function(a){this._layer=a},a.prototype._init=function(){this.segArray=[],this.drawIndex=0;var a=[];this.points.length>0&&a.push(this.points[0]),this.op_line=new NPMap.Geometry.Polyline(a,this.lineStyle)},a.prototype._redraw=function(){this.op_line&&this._layer&&this._layer.refresh()},a.prototype._calculateSeg=function(a){var b=[];if(a+1>=this.points.length)return b.push(0),b;var c=this.points[a],d=this.points[a+1],e=this._getDistance(c,d),f=0;this.drawSpeed>0&&(f=Math.round(e/this.drawSpeed));for(var g,h,i=1;f>i;i++)g=i/f,x=parseFloat((d.lon-c.lon)*g)+parseFloat(c.lon),y=parseFloat((d.lat-c.lat)*g)+parseFloat(c.lat),h=new NPMap.Geometry.Point(x,y),b.push(h);return b.push(this.points[a+1]),b},a.prototype.getAllSegCount=function(){for(var a=0,b=0;b<this.points.length;b++){var c=this._calculateSeg(b);a+=c.length}return a},a.prototype._getDistance=function(a,b){var c=NPMap.T.helper.webMoctorJW2PM(a.lon,a.lat),d=NPMap.T.helper.webMoctorJW2PM(b.lon,b.lat);return Math.sqrt((c.lon-d.lon)*(c.lon-d.lon)+(c.lat-d.lat)*(c.lat-d.lat))},a.prototype._inView=function(a){var b=this.mapAdapter.getExtent(),c=this.mapAdapter.containsPoint(b,NPMap.T.setPoint(this.mapAdapter,a));return c},a.prototype._setCenter=function(a){this.mapAdapter.setCenter(NPMap.T.setPoint(this.mapAdapter,a))},a.prototype._pointToPixel=function(a){return this.mapAdapter.pointToPixel(NPMap.T.setPoint(this.mapAdapter,a))},a.prototype._pixelToPoint=function(a){return NPMap.T.getPoint(this.mapAdapter,this.mapAdapter.pixelToPoint(a))},a.prototype._setLayer=function(a){if(a&&a._useCluster)throw"can not use cluster layer";this._layer=a},a.prototype._suitView=function(a){a.length>1&&this._layer&&this._layer.map},a.prototype._rotateHeaderMarker=function(){if(this.headerMarker&&this.isHeaderRotate){var a=this._calculateAngle();a>=0&&this.headerMarker.rotate(a)}},a.prototype.setPosition=function(a,b){if(a>-1&&a<this.points.length){this.drawIndex=a;var c=this._calculateSeg(this.drawIndex);b>-1&&b<c.length?(this.segArray=c,this.segIndex=b):0>b?(this.segArray=c,this.segIndex=0):b>c.length-1&&(this.segArray=c,this.segIndex=c.length-2),1==c.length&&0==c[0]&&this.headerMarker&&this.headerMarker.moveTo(this.points[this.drawIndex]),this._rotateHeaderMarker(),0==this.segIndex?this.headerMarker.moveTo(this.points[this.drawIndex]):this._moveHeaderMarker(),this._setLine(this.drawIndex,this.segIndex)}},a.prototype._calculateAngle=function(){if(this.drawIndex>=this.points.length-1)return-1;var a=this.points[this.drawIndex],b=this.points[this.drawIndex+1];if(a.lon==b.lon&&a.lat==b.lat)return-1;var c=180*Math.atan2(b.lat-a.lat,b.lon-a.lon)/Math.PI;return c=c>0?360-c:0-c},a.prototype._moveHeaderMarker=function(){if(this.headerMarker){var a=this.segArray[this.segIndex];if(a.lon&&a.lat){var b=new NPMap.Geometry.Point(a.lon,a.lat);this.headerMarker.moveTo(b)}}},a.prototype.addEventListener=function(a,b){switch(a){case NPMap.ANIMATION_EVENT_START:this._listeners.start||(this._listeners.start=[]),this._listeners.start.push(b);break;case NPMap.ANIMATION_EVENT_PAUSE:this._listeners.pause||(this._listeners.pause=[]),this._listeners.pause.push(b);break;case NPMap.ANIMATION_EVENT_STOP:this._listeners.stop||(this._listeners.stop=[]),this._listeners.stop.push(b);break;case NPMap.ANIMATION_EVENT_MOVING:this._listeners.moving||(this._listeners.moving=[]),this._listeners.moving.push(b);break;case NPMap.ANIMATION_EVENT_MOVED:this._listeners.moved||(this._listeners.moved=[]),this._listeners.moved.push(b);break;case"preDraw":case"afterDraw":case"afterStep":this.events.register(a,b)}},a.prototype.removeEventListener=function(a,b){if("preDraw"===a||"afterDraw"===a||"afterStep"===a)return void this.events.unregiste(a);var c=this._listeners[a];if(c instanceof Array)for(var d=0;d<c.length;++d)if(c[d].toString()==b.toString()){c.pop(b);break}},a.prototype._triggerEvent=function(a,b){var c=this._listeners[a];if(c instanceof Array)for(var d=0;d<c.length;++d)c[d].apply(this,b)},a.prototype.remove=function(){this.stop(),this.op_line&&this._layer.hasOverlay(this.op_line)&&this._layer.removeOverlay(this.op_line)}}(),function(){var a=MapPlatForm.Base.AnimationLineManager=function(a){this._map=a,this.animationLineArr=[]};a.prototype.addAnimationLines=function(a){this.animationLineArr=a;for(var b,c=0,d=0;d<this.animationLineArr.length;d++){var e=this.animationLineArr[d].getAllSegCount();e>c&&(b=this.animationLineArr[d],c=e),this.animationLineArr[d].unRedraw=!0}b.unRedraw=!1},a.prototype.start=function(){for(var a=0;a<this.animationLineArr.length;a++)this.animationLineArr[a].start()},a.prototype.stop=function(){for(var a=0;a<this.animationLineArr.length;a++)this.animationLineArr[a].stop()},a.prototype.restart=function(){for(var a=0;a<this.animationLineArr.length;a++)this.animationLineArr[a].restart()},a.prototype.continus=function(){for(var a=0;a<this.animationLineArr.length;a++)this.animationLineArr[a].continus()},a.prototype.pause=function(){for(var a=0;a<this.animationLineArr.length;a++)this.animationLineArr[a].pause()}}();