export const tableColumns = [
  {
    width: 60,
    title: '序号',
    type: 'index',
    align: 'center',
  },
  {
    title: '行政区划',
    key: 'civilCodeName',
  },
  {
    minWidth: 180,
    title: '视频监控数量',
    slot: 'videoNum',
    align: 'center',
    startKey: 'videoSourceCount',
    endKey: 'videoTargetCount',
  },
  {
    title: '新增',
    sortable: 'custom',
    slot: 'videoAddCount',
    hasClick: true,
  },
  {
    title: '撤销',
    sortable: 'custom',
    slot: 'videoRemoveCount',
    hasClick: true,
    isDel: true,
  },
  {
    minWidth: 180,
    title: '人脸卡口数量',
    slot: 'faceNum',
    align: 'center',
    startKey: 'faceSourceCount',
    endKey: 'faceTargetCount',
  },
  {
    title: '新增',
    sortable: 'custom',
    slot: 'faceAddCount',
    hasClick: true,
  },
  {
    title: '撤销',
    sortable: 'custom',
    slot: 'faceRemoveCount',
    hasClick: true,
    isDel: true,
  },
  {
    minWidth: 180,
    title: '车辆卡口数量',
    slot: 'vehicleNum',
    align: 'center',
    startKey: 'vehicleSourceCount',
    endKey: 'vehicleTargetCount',
  },
  {
    title: '新增',
    sortable: 'custom',
    slot: 'vehicleAddCount',
    hasClick: true,
  },
  {
    title: '撤销',
    sortable: 'custom',
    slot: 'vehicleRemoveCount',
    hasClick: true,
    isDel: true,
  },
];

// 详情
export const filterList = [
  {
    type: 'region',
    key: 'regionCode',
    label: '行政区划',
  },
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
];

export const tableColumns_details = () => {
  return [
    {
      width: 60,
      title: '序号',
      type: 'index',
      align: 'center',
    },
    {
      title: '设备编码',
      key: 'deviceId',
      minWidth: 80,
      tooltip: true,
    },
    {
      title: '设备名称',
      key: 'deviceName',
      minWidth: 80,
      tooltip: true,
    },
    {
      title: '行政区划',
      key: 'civilName',
      // minWidth: 100,
      tooltip: true,
    },
    {
      title: '组织机构',
      key: 'orgName',
      // minWidth: 100,
      tooltip: true,
    },
    {
      title: '摄像机功能类型',
      key: 'sbgnlxText',
    },
    {
      title: '监控点位类型',
      key: 'sbdwlxText',
    },
    {
      title: '设备状态',
      key: 'phyStatusText',
    },
    {
      title: '设备状态拓展',
      key: 'phyStatusExtText',
    },
    {
      title: '数据来源',
      key: 'sourceIdText',
    },
    {
      title: '数据删除状态',
      slot: 'isDel',
    },
    // {
    //   title: '撤销人',
    //   key: 'operator',
    //   renderHeader: (h) => {
    //     let { type } = params;
    //     if (type === 'add') {
    //       return <span>新增人</span>;
    //     } else {
    //       return <span>撤销人</span>;
    //     }
    //   },
    // },
    // {
    //   title: '撤销时间',
    //   key: 'operateTime',
    //   minWidth: 80,
    //   renderHeader: (h) => {
    //     let { type } = params;
    //     if (type === 'add') {
    //       return <span>新增时间</span>;
    //     } else {
    //       return <span>撤销时间</span>;
    //     }
    //   },
    // },
  ];
};
