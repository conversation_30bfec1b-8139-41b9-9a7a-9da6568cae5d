const getCommonOptions = () => {
  return [
    {
      value: 0,
      label: '不合格',
    },
    {
      value: 1,
      label: '合格',
    },
    {
      value: 2,
      label: '未检测',
    },
  ];
};
/**
 *
 * @param {Array} showKeys 展示的搜索条件的key
 * @param {Object} params 搜索条件中的对象
 * @returns
 */
export const filterList = (showKeys = [], params = {}) => {
  return [
    {
      type: 'input',
      key: 'searchValue',
      label: '关键词',
      isShow: showKeys.includes('searchValue'),
    },
    {
      key: 'orgCode',
      label: '组织机构',
      type: 'org',
      isShow: showKeys.includes('orgCode'),
    },
    {
      key: 'civilCode',
      label: '行政区划',
      type: 'region',
      isShow: showKeys.includes('civilCode'),
    },
    {
      type: 'select',
      key: 'sbgnlxList',
      isShow: showKeys.includes('sbgnlxList'),
      label: '摄像机功能类型',
      selectMutiple: true, // 多选
      maxTagCount: 1,
      options: params.propertySearchSxjgnlx ? params.propertySearchSxjgnlx : [],
    },
    {
      type: 'select',
      key: 'cascadeReportStatus',
      label: '上报状态',
      isShow: showKeys.includes('cascadeReportStatus'),
      options: [
        {
          value: '0',
          label: '未上报',
        },
        {
          value: '1',
          label: '已上报',
        },
      ],
    },
    {
      type: 'select',
      key: 'phyStatus',
      label: '设备状态',
      isShow: showKeys.includes('phyStatus'),
      options: [
        {
          value: '1',
          label: '可用',
        },
        {
          value: '2',
          label: '不可用',
        },
      ],
    },
    {
      type: 'select',
      key: 'dataStatus',
      label: '合格状态',
      isShow: showKeys.includes('dataStatus'),
      options: [
        {
          value: '1',
          label: '合格',
        },
        {
          value: '0',
          label: '不合格',
        },
      ],
    },
    {
      type: 'select',
      key: 'fillStatusList',
      label: '填报状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('fillStatusList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'clockStatusList',
      label: '时钟状态',
      isShow: showKeys.includes('clockStatusList'),
      selectMutiple: true, // 多选
      options: getCommonOptions(),
    },
    //综合巡检清单
    {
      type: 'select',
      key: 'videoStatusList',
      label: '视频流数据状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('videoStatusList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'faceStatusList',
      label: '人脸数据状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('faceStatusList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'vehicleStatusList',
      label: '车辆数据状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleStatusList'),
      options: getCommonOptions(),
    },
    //视频监控
    {
      type: 'select',
      key: 'videoOnlineList',
      label: '实时视频可调阅',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('videoOnlineList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'videoPlayHistoryList',
      label: '历史视频可调阅',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('videoPlayHistoryList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'videoCompleteHistoryList',
      label: '历史录像完整',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('videoCompleteHistoryList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'videoOsdList',
      label: '字幕标注',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('videoOsdList'),
      options: getCommonOptions(),
    },
    //人脸卡口
    {
      type: 'select',
      key: 'faceOnlineList',
      label: '在线状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('faceOnlineList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'facePassList',
      label: '图片质量',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('facePassList'),
      options: getCommonOptions(),
    },
    {
      //人脸上传及时
      type: 'select',
      key: 'faceRealList',
      label: '上传及时性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('faceRealList'),
      options: getCommonOptions(),
    },
    {
      //人脸大图可用
      type: 'select',
      key: 'faceAvailableList',
      label: '大图可用性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('faceAvailableList'),
      options: getCommonOptions(),
    },
    //车辆
    {
      type: 'select',
      key: 'vehicleOnlineList',
      label: '在线状态',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleOnlineList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'vehicleCompleteList',
      label: '属性完整性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleCompleteList'),
      options: getCommonOptions(),
    },
    {
      type: 'select',
      key: 'vehicleAccuracyList',
      label: '属性准确性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleAccuracyList'),
      options: getCommonOptions(),
    },
    {
      //车辆上传及时性
      type: 'select',
      key: 'vehicleRealList',
      label: '上传及时性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleRealList'),
      options: getCommonOptions(),
    },
    {
      //车辆大图可用性
      type: 'select',
      key: 'vehicleAvailableList',
      label: '大图可用性',
      selectMutiple: true, // 多选
      isShow: showKeys.includes('vehicleAvailableList'),
      options: getCommonOptions(),
    },
  ];
};

// 综合清单搜索条件的key
export const comprehensiveSearchKeys = [
  'searchValue',
  'orgCode',
  'civilCode',
  'sbgnlxList',
  'cascadeReportStatus',
  'phyStatus',
  'dataStatus',
  'fillStatusList',
  'clockStatusList',
  'videoStatusList',
  'faceStatusList',
  'vehicleStatusList',
];
// 视频流搜索条件的key
export const videoSearchKeys = [
  'searchValue',
  'orgCode',
  'civilCode',
  'cascadeReportStatus',
  'phyStatus',
  'dataStatus',
  'fillStatusList',
  'clockStatusList',
  'videoOnlineList',
  'videoPlayHistoryList',
  'videoCompleteHistoryList',
  'videoOsdList',
];
// 人脸搜索条件的key
export const faceSearchKeys = [
  'searchValue',
  'orgCode',
  'civilCode',
  'cascadeReportStatus',
  'phyStatus',
  'dataStatus',
  'fillStatusList',
  'clockStatusList',
  'faceOnlineList',
  'facePassList',
  'faceAvailableList',
  'faceRealList',
];
// 车辆搜索条件的key
export const vehicleSearchKeys = [
  'searchValue',
  'orgCode',
  'civilCode',
  'cascadeReportStatus',
  'phyStatus',
  'dataStatus',
  'fillStatusList',
  'clockStatusList',
  'vehicleOnlineList',
  'vehicleCompleteList',
  'vehicleAccuracyList',
  'vehicleAvailableList',
  'vehicleRealList',
];
// 0不合格、1合格、2未检测
const getCommonRender = () => {
  let columnKeyClassMap = {
    0: { class: 'error-text', label: '不合格' },
    1: { class: 'success-text', label: '合格' },
    2: { class: 'unknown-text', label: '未检测' },
  };
  return (h, { row, column }) => {
    return (
      <span class={columnKeyClassMap[row[column.key]] ? columnKeyClassMap[row[column.key]].class : ''}>
        {columnKeyClassMap[row[column.key]]?.label || '-'}
      </span>
    );
  };
};
// 上报状态：'0'未上报、'1'已上报
const getCascadeReportStatusRender = () => {
  let columnKeyClassMap = {
    0: { class: 'error-text', label: '未上报' },
    1: { class: 'success-text', label: '已上报' },
  };
  return (h, { row, column }) => {
    return (
      <span class={columnKeyClassMap[row[column.key]] ? columnKeyClassMap[row[column.key]].class : ''}>
        {columnKeyClassMap[row[column.key]]?.label || '-'}
      </span>
    );
  };
};
// 设备状态: '1': 可用, '2': 不可用
const getPhyStatusRender = () => {
  let columnKeyClassMap = {
    1: { class: 'success-text', label: '可用' },
    2: { class: 'error-text', label: '不可用' },
  };
  return (h, { row, column }) => {
    return (
      <span class={columnKeyClassMap[row[column.key]] ? columnKeyClassMap[row[column.key]].class : ''}>
        {columnKeyClassMap[row[column.key]]?.label || '-'}
      </span>
    );
  };
};
const getIpRender = () => {
  return (h, { row }) => {
    return <span>{row.ipAddr || row.ipv6Addr || ''}</span>;
  };
};

//公共的列
export const commonTableColumns = [
  {
    type: 'selection',
    width: 50,
    align: 'center',
    fixed: 'left',
    isShow: true,
  },
  {
    title: '序号',
    type: 'index',
    width: 50,
    align: 'center',
    isShow: true,
  },
  {
    width: 120,
    title: '设备编码',
    key: 'deviceId',
    tooltip: true,
  },
  {
    width: 120,
    title: '所属单位',
    key: 'orgName',
    tooltip: true,
  },
  {
    width: 120,
    title: '行政区划',
    key: 'civilName',
    tooltip: true,
  },
  {
    width: 120,
    title: '经度',
    key: 'longitude',
    tooltip: true,
  },
  {
    width: 120,
    title: '纬度',
    key: 'latitude',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: 'IP地址',
    key: 'ipAddr',
    render: getIpRender(),
  },
  {
    width: 120,
    title: 'MAC地址',
    key: 'macAddr',
    tooltip: true,
  },
  {
    width: 120,
    title: '摄像机功能类型',
    key: 'sbgnlxText',
    tooltip: true,
  },
  {
    width: 120,
    title: '监控点位类型',
    key: 'sbdwlxText',
    tooltip: true,
  },
  {
    width: 120,
    title: '采集区域',
    key: 'sbcjqyText',
    tooltip: true,
  },
  {
    width: 120,
    title: '上报状态',
    key: 'cascadeReportStatus',
    render: getCascadeReportStatusRender(),
  },
  {
    width: 120,
    title: '设备状态',
    key: 'phyStatus',
    render: getPhyStatusRender(),
  },
  {
    width: 120,
    title: '合格状态',
    key: 'shortDataStatus',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '填报状态',
    key: 'fillStatus',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '时钟状态',
    key: 'clockStatus',
    render: getCommonRender(),
  },
];
//综合清单特有列
export const comprehensiveTableColumns = [
  {
    width: 120,
    title: '视频流数据状态',
    key: 'videoStatus',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '人脸数据状态',
    key: 'faceStatus',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '车辆数据状态',
    key: 'vehicleStatus',
    render: getCommonRender(),
  },
];
//视频流特有列
export const videoOnlyTableColumns = [
  {
    width: 120,
    title: '实时视频可调阅',
    key: 'videoOnline',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '历史视频可调阅',
    key: 'videoPlayHistory',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '历史录像完整',
    key: 'videoCompleteHistory',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '字幕标注',
    key: 'videoOsd',
    render: getCommonRender(),
  },
];
//人脸特有列
export const faceOnlyTableColumns = [
  {
    width: 120,
    title: '在线状态',
    key: 'faceOnline',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '图片质量',
    key: 'facePass',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '上传及时性',
    key: 'faceReal',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '大图可用性',
    key: 'faceAvailable',
    render: getCommonRender(),
  },
];
//车辆特有列
export const vehicleOnlyTableColumns = [
  {
    width: 120,
    title: '在线状态',
    key: 'vehicleOnline',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '属性完整性',
    key: 'vehicleComplete',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '属性准确性',
    key: 'vehicleAccuracy',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '上传及时性',
    key: 'vehicleReal',
    render: getCommonRender(),
  },
  {
    width: 120,
    title: '大图可用性',
    key: 'vehicleAvailable',
    render: getCommonRender(),
  },
];
