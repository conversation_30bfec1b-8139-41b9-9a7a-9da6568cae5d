<template>
  <div class="video-container">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="120">
      <Collapse simple v-model="collapse">
        <Panel name="1">
          基础信息
          <div slot="content" class="mt-md">
            <common-form
              :label-width="120"
              :form-data="formData"
              :form-model="formModel"
              ref="commonForm"
              :moduleAction="moduleAction"
              :task-index-config="taskIndexConfig"
              @updateFormData="updateFormData"
            >
              <div slot="waycondiction" class="mt-xs">
                <p>
                  <span class="base-text-color">检测条件：</span>
                  <Checkbox
                    class="mr-lg"
                    v-model="formData.deviceQueryForm.detectPhyStatus"
                    true-value="1"
                    false-value="0"
                    >设备可用</Checkbox
                  >
                  <Checkbox v-model="formData.deviceQueryForm.detectIsOnline" true-value="1" false-value="0"
                    >设备在线</Checkbox
                  >
                  <Tooltip placement="top-start" max-width="400">
                    <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                    <div slot="content">
                      <span>实时视频可调阅率指标检测合格则设备在线</span>
                    </div>
                  </Tooltip>
                </p>
                <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
              </div>
            </common-form>
          </div>
        </Panel>

        <Panel name="2">
          检测参数
          <div slot="content" class="mt-md">
            <FormItem label="检测方法" prop="osdModel" class="mb-md">
              <Select
                v-model="formData.osdModel"
                placeholder="请选择"
                transfer
                class="width-input"
                @on-change="onChangeOsdModel"
              >
                <template v-for="e in odsCheckModelList">
                  <Option :value="e.dataKey" :key="e.dataKey">{{ e.dataValue }} </Option>
                </template>
              </Select>
            </FormItem>
            <FormItem class="mb-md">
              <template #label>
                <Tooltip placement="top-start" class="tooltip-sample-graph">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    <img src="@/assets/img/datagovernance/subtitle-reset.png" alt="示例图" style="width: 100%" />
                  </div>
                  检测内容
                </Tooltip>
              </template>
            </FormItem>
            <detect-content
              ref="detectContent"
              v-bind="$props"
              :config-info="formDataCopy"
              :detect-content-list="formData.detectContent"
              v-if="formData.osdModel !== 'sdk'"
            >
            </detect-content>
            <detect-content-sdk
              ref="detectContentSdk"
              v-bind="$props"
              :config-info="formDataCopy"
              :detect-content-list="formData.detectContent"
              v-else
            >
            </detect-content-sdk>
          </div>
        </Panel>
        <Panel name="3">
          高级参数
          <div slot="content" class="mt-md">
            <FormItem v-if="formData.osdModel !== 'sdk'" class="mb-md" :label-width="170">
              <template #label>
                <Tooltip placement="top-start" max-width="200">
                  <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
                  <div slot="content">
                    如果设备当天有实时视频拉取成功记录，则直接取该次视频流截图进行OCR识别，不再重新拉流检测。
                  </div>
                  取最近缓存结果
                </Tooltip>
              </template>
              <RadioGroup v-model="formData.useRecentCache">
                <Radio label="true" class="mr-lg">是</Radio>
                <Radio label="false">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="更新视频字幕状态" class="mb-md" :label-width="170">
              <RadioGroup v-model="formData.isUpdatePhyStatus">
                <Radio :label="1" class="mr-lg">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="是否需要复检" class="mb-md" :label-width="170">
              <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
                <Radio label="1" class="mr-lg">是</Radio>
                <Radio label="2">否</Radio>
              </RadioGroup>
              <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
            </FormItem>
            <FormItem label="复检设备" v-if="isRecheck === '1'" :label-width="170" class="mb-md">
              <RadioGroup v-model="formData.reinspect.model">
                <Radio label="UNQUALIFIED">检测不合格设备</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="复检次数"
              prop="maxCount"
              :label-width="170"
              class="mb-md"
              v-if="isRecheck === '1' && needRecheckNumIndex.includes(indexType)"
            >
              <InputNumber
                v-model.number="formData.reinspect.maxCount"
                class="mr-xs"
                :max="5"
                :min="1"
                :precision="0"
              ></InputNumber>
              <span class="base-text-color">次</span>
            </FormItem>
            <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType" :label-width="170" class="mb-md">
              <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
                <Radio label="INTERVAL" class="mr-lg">时间间隔</Radio>
                <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
              </RadioGroup>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
                <span class="base-text-color">检测结束</span>
                <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
                <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="1">时</Option>
                  <Option :value="2">分</Option>
                </Select>
                <span class="base-text-color ml-md">后，开始复检</span>
              </div>
              <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
                <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
                  <Option :value="3">当天</Option>
                  <Option :value="4">第二天</Option>
                  <Option :value="5">第三天</Option>
                </Select>
                <TimePicker
                  :value="formData.reinspect.scheduleValue"
                  transfer
                  format="HH:mm"
                  placeholder="请选择"
                  class="width-xs"
                  @on-change="handleChangeTime"
                ></TimePicker>
              </div>
            </FormItem>
            <FormItem
              label="对历史检测结果比对分析"
              :label-width="170"
              class="mb-md"
              v-if="historyComparisonConfigModule.includes(indexType)"
            >
              <RadioGroup v-model="formData.isDetectContrast">
                <Radio :label="1">是</Radio>
                <Radio :label="0">否</Radio>
              </RadioGroup>
            </FormItem>
          </div>
        </Panel>
      </Collapse>
    </Form>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import {
  defaultDetectContent,
  defaultDetectContentSdk,
  historyComparisonConfigModule,
  needRecheckNumIndex,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
export default {
  name: 'video-osd-accuracy',
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    DetectContent:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content.vue')
        .default,
    DetectContentSdk:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/detect-content-sdk.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateDetectionModePass = (rule, value, callback) => {
      !value.length ? callback(new Error('请选择检测内容')) : callback();
    };
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      ruleCustom: {
        detectionMode: [{ validator: validateDetectionModePass, trigger: 'change', required: true }],
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      formData: {
        detectMode: '1',
        reinspect: null,
        selectType: 0,
        quantityConfig: [],
        deviceDetection: {},
        deviceQueryForm: {
          detectPhyStatus: '0',
        },
        detectContent: [],
        osdModel: 'sdk',
        delaySipTimeOut: null,
        delayStreamTimeOut: null,
        delayIdrTimeOut: null,
        detectionTimeOut: null,
        isScreenshots: '1',
        checkRegion: {}, //治理前可调阅率

        existsTime: false, //-是否在指定区域标注
        //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
        timeFormCheck: false,
        //（右上角）时间信息检测 --位置规范检测：
        timePositionCheck: false,
        //时钟、区划、地址信息需保持右对齐。 右边距: min
        timePositionCheckMin: null,
        //时钟、区划、地址信息需保持右对齐。 右边距: max
        timePositionCheckMax: null,
        //（右上角）时间信息检测 --准确性规则，
        timeDeviationCheck: false,
        //设备时间与标准时间允许偏差 XX 秒
        timeDeviationCheckValue: 0,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsArea: false,
        //（右下角）区划与地址信息检测 --位置规范检测：
        areaPositionCheck: false,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。
        areaPositionCheckValueLine: false,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: min
        areaPositionCheckMin: null,
        //时钟、区划、地址信息需保持右对齐。地址信息只能占用一行。 右边距: max
        areaPositionCheckMax: null,
        //地址信息只能占用一行
        areaPositionCheckLine: false,
        //（右下角）区划与地址信息检测 --准确性规则，
        areaDeviationCheck: false,
        // 省/市/区县、地点信息需与档案信息保持一致
        areaDeviationCheckValue: false,
        //队所（派出所）信息需标注准确（与组织机构表匹配）
        areaDeviationCheckLine: false,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsCamera: false,
        //准确性规则
        cameraDeviationCheck: false,
        isUpdatePhyStatus: 0,
        //精确匹配2、综合匹配1
        areaDeviationCheckValueType: 1,
        // 【省级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeProvince: false,
        // 【地市级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeCity: false,
        // 【区县级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeCounty: false,
        // 【地址】信息需与档案信息保持一致
        areaDeviationCheckValueTypeInfo: false,
      },
      formDataCopy: {},

      isRecheck: '2',
      timeLocation: false,
      areaLocation: false,
      cameraInfo: false,
      collapse: ['1', '2', '3'],
      historyComparisonConfigModule: historyComparisonConfigModule,
      needRecheckNumIndex: needRecheckNumIndex,
    };
  },
  computed: {
    ...mapGetters({
      odsCheckModelList: 'algorithm/ivdg_video_ods_check_model',
      treeData: 'common/getInitialAreaList',
    }),
  },
  filter: {},
  async created() {
    if (this.odsCheckModelList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    onChangeOsdModel() {
      if (this.formData.osdModel === 'sdk') {
        Object.keys(defaultDetectContent).forEach((key) => {
          delete this.formData[key];
        });
        this.formData = {
          ...this.formData,
          ...defaultDetectContentSdk,
        };
      } else {
        Object.keys(defaultDetectContentSdk).forEach((key) => {
          delete this.formData[key];
        });
        this.formData = {
          ...this.formData,
          ...defaultDetectContent,
        };
      }
    },
    async handleSubmit() {
      let modalDataValidate = await this.$refs['modalData'].validate();
      let validate = await this.$refs['commonForm'].handleSubmit();
      if (validate) {
        if (this.formData.osdModel === 'sdk') {
          let { formData, detectContent } = this.$refs.detectContentSdk.getFormData();
          this.formData = {
            ...this.formData,
            ...formData,
            detectContent: detectContent,
          };
        } else {
          let { formData, detectContent } = this.$refs.detectContent.getFormData();
          this.formData = {
            ...this.formData,
            ...formData,
            detectContent: detectContent,
          };
        }
      }

      let detectContentValid = true;
      if (this.$refs.detectContent) {
        detectContentValid = await this.$refs.detectContent.handleSubmit();
      }
      if (this.$refs.detectContentSdk) {
        detectContentValid = await this.$refs.detectContentSdk.handleSubmit();
      }

      return validate && modalDataValidate && detectContentValid;
    },
    onChangeDetectContent() {
      this.formData.detectContent = [];
      let detectContent = ['timeLocation', 'areaLocation'];
      detectContent.forEach((value) => {
        if (this[value]) {
          this.formData.detectContent.push(value);
        }
      });
    },
    setRecheck() {
      this.isRecheck = this.formData.reinspect ? '1' : '2';
    },
    setDetectContent() {
      if (this.formData.detectContent && this.formData.detectContent.length) {
        this.formData.detectContent.forEach((value) => {
          this[value] = true;
        });
      }
    },
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.formData.reinspect = {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
        };
        if (needRecheckNumIndex.includes(this.indexType)) {
          this.$set(this.formData.reinspect, 'maxCount', 1);
        }
        this.formData.reinspect.model = 'UNQUALIFIED';
        this.formData.reinspect.scheduleValue = null;
      }
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
          };
          if (this.formData.reinspect) {
            this.formData.reinspect.maxCount = this.formData.reinspect.maxCount || null;
          }
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.formDataCopy = this.$util.common.deepCopy(this.formData);
        this.setRecheck();
        this.setDetectContent();
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.width-input {
  width: 380px;
}
.video-container {
  margin: 20px;
}
@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    margin-bottom: 10px;
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: var(--bg-collapse-item);
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: var(--color-collapse-arrow);
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
      .ivu-collapse-content-box {
        padding: 0;
      }
    }
  }
}
</style>
