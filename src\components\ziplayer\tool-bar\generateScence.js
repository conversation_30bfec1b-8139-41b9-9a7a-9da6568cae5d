export default (res, ctx, isptz, iframes) => {
    let {
        videoTitle, 
        videoRate, 
        videoClose, 
        fullScreen,
        videoFilter,
        continuousShooting,
        videoPrintScreen,
        videoRatio,
        videoToMap,
        videoRealHover,
        videoHisNormal,
        instantReplay,
        sdHdPlay,
        videoZoom,
        videoFrameMark,
        searchPictures,
        ptzControl,
        videoProgress,
        videoCustomProgress,
        videoRealNormal,
        videoHisHover,
        slowPlay,
        stepBack,
        videoPlay,
        videoPause,
        stepPlay,
        fastPlay,
        curRate,
        fastBackward,
        fastForward,
        videoSearch,
        videoLock,
        videoUnlock,
        videoTime,
        customSlowPlay,
        customFastPlay,
        customFastBackward,
        customFastForward
        } = res;
    return {
        //[实时上，实时下，历史上，历史下，历史下（自定义进度条）]
        "nobar": [
            [],
            [],
            [],
            [],
            []
        ],
        "monitorVideo": [
            [videoTitle, videoRate, videoClose(ctx), fullScreen(ctx), videoFilter(ctx), continuousShooting(ctx), videoPrintScreen(ctx), videoRatio(ctx), videoToMap(ctx)],
            [videoRealHover, videoHisNormal(ctx), instantReplay, sdHdPlay(ctx), videoZoom, videoFrameMark(ctx), ptzControl(ctx, isptz), videoLock(ctx), videoUnlock(ctx)],
            [videoTitle, videoRate, videoClose(ctx), fullScreen(ctx), videoFilter(ctx), continuousShooting(ctx), videoPrintScreen(ctx), videoRatio(ctx), videoToMap(ctx)],
            [videoProgress(ctx, iframes), videoRealNormal(ctx), videoHisHover, slowPlay, stepBack(ctx), videoPlay, videoPause, stepPlay, fastPlay, curRate, fastBackward, fastForward, sdHdPlay(ctx), videoZoom, videoFrameMark(ctx), videoSearch(ctx), videoTime],
            [videoCustomProgress(ctx, iframes), videoRealNormal(ctx), videoHisHover, customSlowPlay(ctx), stepBack(ctx), videoPlay, videoPause, stepPlay, customFastPlay(ctx), curRate, customFastBackward(ctx), customFastForward(ctx), sdHdPlay(ctx), videoZoom, videoFrameMark(ctx), videoSearch(ctx), videoTime]
        ],
        "mapVideo": [
            [videoTitle, videoRate, videoPrintScreen(ctx), videoFilter(ctx), videoRatio(ctx)],
            [videoRealHover, videoHisNormal(ctx), videoZoom, videoFrameMark(ctx), ptzControl(ctx, isptz)],
            [videoTitle, videoRate, videoPrintScreen(ctx), videoFilter(ctx), videoRatio(ctx)],
            [videoProgress(ctx, iframes), videoRealNormal(ctx), videoHisHover, slowPlay, stepBack(ctx), videoPlay, videoPause, stepPlay, fastPlay, curRate, videoZoom, videoFrameMark(ctx), videoSearch(ctx), videoTime],
            [videoCustomProgress(ctx, iframes), videoRealNormal(ctx), videoHisHover, customSlowPlay(ctx), stepBack(ctx), videoPlay, videoPause, stepPlay, customFastPlay(ctx), curRate, videoZoom, videoFrameMark(ctx), videoSearch(ctx), videoTime]
        ],
        "singleVideo": [
            [videoTitle, videoRate, videoPrintScreen(ctx)],
            [],
            [videoTitle, videoRate, videoPrintScreen(ctx)],
            [videoProgress(ctx, iframes), slowPlay, stepBack(ctx), videoPlay, videoPause, stepPlay, fastPlay, curRate],
            [videoCustomProgress(ctx, iframes), customSlowPlay(ctx), stepBack(ctx), videoPlay, videoPause, stepPlay, customFastPlay(ctx), curRate]
        ]
    }
}