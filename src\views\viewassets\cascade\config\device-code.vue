<template>
  <ui-modal v-model="visible" title="设备编码格式检测" width="35%" @query="handleSubmit('extraParam')">
    <loading v-if="dataLoading" />
    <div class="ip-container">
      <p class="mb-md base-text-color">1、设备编码允许长度。</p>
      <Form class="base-form" ref="extraParam" :model="extraParam" :label-width="0">
        <FormItem
          v-for="(item, index) in extraParam.items"
          :key="index"
          class="flex-content"
          :rules="[
            {
              required: true,
              type: 'number',
              message: '设备编码长度不能为空',
              trigger: 'blur',
            },
          ]"
          :prop="'items.' + index + '.value'"
        >
          <InputNumber class="w160" v-model="item.value" placeholder="请输入设备编码长度"></InputNumber>
          <i @click="handleAdd" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove(index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </FormItem>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import cascade from '@/config/api/cascade';
export default {
  name: 'device-code',
  components: {},
  props: {},
  dataLoading: false,
  data() {
    return {
      indexRuleId: '',
      visible: false,
      dataLoading: false,
      formData: {
        list: [],
        ruleId: '',
      },
      extraParam: {
        items: [
          {
            value: null,
          },
        ],
      },
    };
  },
  methods: {
    init({ indexRuleId }) {
      this.visible = true;
      this.handleReset('extraParam');
      this.indexRuleId = indexRuleId;
      this.getInitData();
    },
    async getInitData() {
      this.dataLoading = true;
      try {
        let params = {
          ruleId: this.indexRuleId,
        };
        let {
          data: { data },
        } = await this.$http.post(cascade.configQueryAllData, params);
        let dataRight = data.right;
        let extraParamData = dataRight[0].extraParam;
        let deviceIdLength = extraParamData ? JSON.parse(extraParamData).deviceIdLength : [''];
        let items = deviceIdLength.map((item) => {
          return { value: item };
        });
        this.extraParam.items = items;
        this.formData.list = dataRight;
        this.extraParam.items = items;
        this.dataLoading = false;
      } catch (e) {
        console.error(e);
        this.dataLoading = true;
      }
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    handleAdd() {
      this.extraParam.items.push({
        value: null,
      });
    },
    handleRemove(index) {
      if (this.extraParam.items.length < 2) {
        return this.$Message.error('至少保留一个检测规则');
      }
      this.extraParam.items.splice(index, 1);
    },
    async handleSubmit(name) {
      try {
        let result = await this.$refs[name].validate();
        if (!result) return;
        let resultFromData = { ...this.formData };
        resultFromData.list[0].extraParam = JSON.stringify({
          deviceIdLength: this.extraParam.items.map((item) => item.value),
        });
        resultFromData.ruleId = this.indexRuleId;
        await this.$http.post(cascade.configUpdateAllData, resultFromData);
        this.$Message.success('成功');
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 0 50px 50px 50px !important;
}
.flex-content {
  /deep/ .ivu-form-item-content {
    display: flex;
  }
}
.color-primary {
  color: var(--color-primary);
}
.w160 {
  width: 160px;
}
.base-form {
  max-height: 550px;
  overflow-y: auto;
}
</style>
