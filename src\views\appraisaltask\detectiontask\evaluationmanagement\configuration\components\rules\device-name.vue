<template>
  <ui-modal v-model="visible" title="设备名称检测" width="35%" @query="handleSubmit()">
    <div class="device-container">
      <div class="device-item mb-sm">
        <Checkbox v-model="extraParam.isLength" @on-change="extraParam.length = null" :true-value="1" :false-value="0">
          <span class="dis-select"
            >1、设备名称长度不少于
            <InputNumber v-model="extraParam.length" class="ml-sm"></InputNumber>
            <span class="ml-sm">位字符</span>
          </span>
        </Checkbox>
      </div>
      <div class="device-item mb-sm">
        <Checkbox v-model="extraParam.isChar" :true-value="1" :false-value="0">
          <span class="dis-select">2、设备名称必须包含中文</span>
        </Checkbox>
      </div>
      <div class="device-item mb-sm">
        <Checkbox
          v-model="extraParam.isSpecialChar"
          @on-change="extraParam.specialChar = ['']"
          :true-value="1"
          :false-value="0"
        >
          <span class="dis-select">3、设备名称不能包含以下特殊字符</span>
        </Checkbox>
      </div>
      <div class="device-item ml20 add-box">
        <div class="mb-sm" v-for="(item, index) in extraParam.specialChar" :key="index">
          <Input class="w160" v-model="extraParam['specialChar'][index]"></Input>
          <i @click="handleAdd" class="icon-font icon-tree-add f-16 ml-sm color-primary"></i>
          <i
            v-if="index !== 0"
            @click="handleRemove(index)"
            class="icon-font icon-shanchu1 f-16 ml-sm color-primary"
          ></i>
        </div>
      </div>
      <div class="device-item mb-sm">
        <Checkbox
          v-model="extraParam.isOtherCheckRule"
          @on-change="extraParam.otherCheckRule = ''"
          :true-value="1"
          :false-value="0"
        >
          <span class="dis-select">4、其他检测规则</span>
        </Checkbox>
      </div>
      <div class="device-item ml20">
        <div class="mb-sm">
          <Input class="w300" v-model="extraParam.otherCheckRule"></Input>
        </div>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'ip-address',
  components: {},
  props: {},
  data() {
    return {
      indexConfig: {},
      visible: false,
      extraParam: {
        isLength: 1,
        isChar: 1,
        isSpecialChar: 1,
        length: 1,
        specialChar: [''],
        isOtherCheckRule: 1,
        otherCheckRule: '',
      },
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    validateForm() {
      if (this.extraParam.isLength === 1 && !this.extraParam['length']) {
        this.$Message.error('设备名称长度不能为空');
        return false;
      }
      if (this.extraParam.isSpecialChar === 1) {
        for (let i = 0; i < this.extraParam.specialChar.length; i++) {
          let item = this.extraParam.specialChar[i];
          if (!item) {
            this.$Message.error('特殊字符不能为空');
            return false;
          }
        }
      }
      if (this.extraParam.isOtherCheckRule === 1) {
        try {
          new RegExp(this.extraParam.otherCheckRule);
          //  eval(this.extraParam.otherCheckRule) instanceof RegExp
          return true;
        } catch (err) {
          this.$Message.error('请输入正确的正则表达式！');
          return false;
        }
      }
      return true;
    },
    async handleSubmit() {
      let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
      try {
        if (!this.validateForm()) return;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(this.extraParam),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
    handleAdd() {
      this.extraParam.specialChar.push('');
    },
    handleRemove(index) {
      if (this.extraParam.specialChar.length < 2) {
        return this.$Message.error('请至少保留一个配置');
      }
      this.extraParam.specialChar.splice(index, 1);
    },
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getDeviceNameConfig();
    },
    async getDeviceNameConfig() {
      let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
      try {
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        this.extraParam = {
          ...this.extraParam,
          ...JSON.parse(data.extraParam || '{}'),
        };
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.color-primary {
  color: var(--color-primary);
}
.w160 {
  width: 160px;
}
.w300 {
  width: 300px;
}
.ml20 {
  margin-left: 20px;
}
@{_deep} .ivu-modal {
  width: 600px !important;
  .ivu-modal-body {
    padding: 0 50px 50px 50px !important;
    margin-top: 0 !important;
  }
}
.add-box {
  max-height: 450px;
  overflow-y: auto;
}
</style>
