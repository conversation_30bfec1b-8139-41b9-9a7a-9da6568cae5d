.search-top {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    .ivu-input-wrapper {
      margin: 15px;
      margin-top: 0;
    }
    .add-group {
      cursor: pointer;
      font-size: 20px;
      border-radius: 4px;
      padding: 1px 5.5px;
      border: 1px solid #d3d7de;
    }
    /deep/ .ivu-btn {
      padding-left: 0;
      padding-right: 0;
      background: white;
      border: 1px solid rgb(211, 215, 222);
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-left: none;
      color: #808695;
      .ivu-icon {
        color: #808695;
      }
    }
  }
  .tree-box {
    height: calc(~'100% - 49px');
    overflow-y: auto;
    position: initial;
  }
  .deviceTree {
    flex: 1;
    /deep/ .offline {
      i {
        color: #888888 !important;
      }
    }
  }