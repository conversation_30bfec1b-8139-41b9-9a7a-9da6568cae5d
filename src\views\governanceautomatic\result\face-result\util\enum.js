export default {
  aggregateEnums: {
    accurrency: '图像抓拍时间准确性检测',
    timeliness: '图像上传及时性检测',
    bigPic: '图像URL检测',
    faceStructure: '人脸结构化',
    repeatImage: '重复图像识别处理',
    bigContactSmall: '大小图关联正确检测',
    smallOnly: '小图唯一人脸检测处理',
    dataexport: '数据输出',
  },
  aggregateOptions: [
    {
      addVisible: false,
      top: '1.6rem',
      left: '1.25%',
      datas: [
        {
          // name: "dataaccessPopup",
          icon: 'icon-zu16191',
          title: '数据输入',
          iconView: 'icon-chakanjiancejieguo',
          list: [
            { title: '接入总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '今日增量', num: 0, color: '#F18A37', fileName: 'todayAccessDataCount' },
          ],
          left: '10px',
          iconPass: true,
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '8.4%',
        height: '0.04rem',
        top: '1.8rem',
        left: '14.0%',
      },
    },
    {
      addVisible: false,
      top: '0.6rem',
      left: '23%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-tuxiangzhuapaishijianzhunquexingjiance',
          title: '抓拍时间异常图像清理',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '清理异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'imagecapturePopup',
          icon: 'icon-tuxiangshangchuanjishixingjiance',
          title: 'URL缺失图像清理',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '清理异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
        {
          name: 'imagecapturePopup',
          icon: 'icon-datuURLjiance',
          title: '重复图像清理',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '清理异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '7.4%',
        height: '0.04rem',
        top: '1.8rem',
        left: '37%',
      },
    },
    {
      addVisible: false,
      top: '1.5rem',
      left: '45%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-renlianjiegouhua',
          title: '人脸结构化',
          list: [
            { title: '算法数量', num: 0, color: '#05FEF5', fileName: 'algorithmCount' },
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '结构化数量', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '转化率', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: false,
        },
      ],
      connectingOptions: {
        width: '7.6%',
        height: '0.04rem',
        top: '1.8rem',
        left: '57.8%',
      },
    },
    {
      addVisible: false,
      top: '1.5rem',
      left: '66%',
      datas: [
        {
          name: 'imagecapturePopup',
          icon: 'icon-daxiaotuguanlianzhengquejiance',
          title: '小图非唯一人脸图片清理',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '清理异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
      connectingOptions: {
        width: '7%',
        height: '0.04rem',
        top: '1.8rem',
        left: '79%',
      },
    },
    {
      addVisible: false,
      top: '1.5rem',
      left: '86.6%',
      datas: [
        {
          name: 'exportDataPopup',
          icon: 'icon-zu1665',
          title: '数据输出',
          list: [
            { title: '数据总量', num: 0, color: '#05FEF5', fileName: 'accessDataCount' },
            { title: '清理异常', num: 0, color: '#F18A37', fileName: 'existingExceptionCount' },
            { title: '占比', num: '0%', color: '#13B13D', fileName: 'existingExceptionRate' },
          ],
          left: '10px',
          iconSetting: true,
          iconView: 'icon-chakanjiancejieguo',
        },
      ],
    },
  ],
};
