<template>
  <div class="detect-content">
    <div class="checkbox-wrapper base-text-color mb-md">
      <span>1、(右上角) 时间信息检测</span>
      <i-switch v-model="timeLocation" @on-change="onChangeDetectContent" />
    </div>
    <div class="detect-detail">
      <Checkbox class="block mb-md" v-model="formData.existsTime"> 是否标注 </Checkbox>
      <Checkbox class="block mb-md" v-model="formData.timeFormCheck">
        时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
      </Checkbox>
      <div class="mb-md">
        <Checkbox v-model="formData.timeDeviationCheck" class="block">
          准确性检测：标注时间与抓拍时间允许偏差
          <InputNumber
            v-model="formData.timeDeviationCheckValue"
            :min="0"
            :precision="0"
            class="mr-sm width-mini"
            :class="{ 'ipt-requried': isRequriedTimeDeviationTip() }"
          ></InputNumber>
          <span class="base-text-color">秒</span>
        </Checkbox>
        <p v-if="isRequriedTimeDeviationTip()" class="font-requried">请输入允许偏差</p>
      </div>
    </div>
    <div class="checkbox-wrapper base-text-color mb-md">
      <span>2、(右下角) 区划与地址信息检测</span>
      <i-switch v-model="areaLocation" @on-change="onChangeDetectContent" />
    </div>
    <div class="detect-detail">
      <Checkbox label="timeLocation" class="block mb-md" v-model="formData.existsArea"> 是否标注 </Checkbox>
      <Checkbox
        v-model="formData.areaDeviationCheck"
        class="block mb-md"
        @on-change="changeDetectCheckBox('areaDeviationCheck')"
      >
        准确性检测
      </Checkbox>
      <RadioGroup
        v-model="formData.areaDeviationCheckValueType"
        class="block mb-md detect-location"
        @on-change="onChangeAreaDeviationCheckValueType"
      >
        <Radio :label="1" class="mr-lg">综合匹配</Radio>
        <Radio :label="2">精确匹配</Radio>
      </RadioGroup>
      <div class="detect-location">
        <Checkbox
          v-if="formData.areaDeviationCheckValueType === 1"
          v-model="formData.areaDeviationCheckValue"
          @on-change="changeDetectChildCheckBox('areaDeviationCheckValue')"
          class="block mb-md"
        >
          省/市/区县、地点信息需与档案信息保持一致
        </Checkbox>
        <template v-if="formData.areaDeviationCheckValueType === 2">
          <Checkbox
            v-model="formData.areaDeviationCheckValueTypeProvince"
            @on-change="changeDetectChildCheckBox('areaDeviationCheckValueTypeProvince')"
            class="block mb-md"
          >
            【省级】信息需与档案信息保持一致
          </Checkbox>
          <Checkbox
            v-model="formData.areaDeviationCheckValueTypeCity"
            @on-change="changeDetectChildCheckBox('areaDeviationCheckValueTypeCity')"
            class="block mb-md"
          >
            【地市级】信息需与档案信息保持一致
          </Checkbox>
          <Checkbox
            v-model="formData.areaDeviationCheckValueTypeCounty"
            @on-change="changeDetectChildCheckBox('areaDeviationCheckValueTypeCounty')"
            class="block mb-md"
          >
            【区县级】信息需与档案信息保持一致
          </Checkbox>
          <Checkbox
            v-model="formData.areaDeviationCheckValueTypeInfo"
            @on-change="changeDetectChildCheckBox('areaDeviationCheckValueTypeInfo')"
            class="block mb-md"
          >
            【地址】信息需与档案信息保持一致
          </Checkbox>
        </template>
        <Checkbox
          v-model="formData.areaDeviationCheckLine"
          @on-change="changeDetectChildCheckBox('areaDeviationCheckLine')"
          class="block mb-md"
        >
          队所（派出所）信息需标注准确（与组织机构表匹配）
        </Checkbox>
      </div>
    </div>
    <div class="checkbox-wrapper base-text-color mb-md">
      <span>3、(左下角) 摄像机信息检测</span>
      <i-switch v-model="cameraInfo" @on-change="onChangeDetectContent" />
    </div>
    <div class="detect-detail">
      <Checkbox v-model="formData.existsCamera" class="block mb-md"> 是否在指定区域标注 </Checkbox>
      <Checkbox v-model="formData.cameraDeviationCheck" class="block mb-md"> 准确性检测 </Checkbox>
    </div>
  </div>
</template>
<script>
export default {
  name: 'detect-content-sdk',
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    /** 检测内容
     * detectContentList： ['timeLocation', 'areaLocation']
     */
    detectContentList: {},
  },
  data() {
    return {
      timeLocation: false,
      areaLocation: false,
      cameraInfo: false,
      collapse: ['1', '2', '3'],
      detectContent: [],
      formData: {
        existsTime: true, //-是否在指定区域标注
        //（右上角）时间信息检测 --时间格式规范检测：时间格式应该为YYYY-MM-DD hh:mm:ss，不能包含年月日星期等字样。
        timeFormCheck: false,
        //（右上角）时间信息检测 --准确性规则，
        timeDeviationCheck: true,
        //设备时间与标准时间允许偏差 XX 秒
        timeDeviationCheckValue: 60,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsArea: true,
        //（右下角）区划与地址信息检测 --准确性规则，
        areaDeviationCheck: true,
        // 省/市/区县、地点信息需与档案信息保持一致
        areaDeviationCheckValue: true,
        //队所（派出所）信息需标注准确（与组织机构表匹配）
        areaDeviationCheckLine: false,

        //（右下角）区划与地址信息检测 --是否在指定区域标注
        existsCamera: false,
        //准确性规则
        cameraDeviationCheck: false,
        //精确匹配2、综合匹配1
        areaDeviationCheckValueType: 1,
        // 【省级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeProvince: true,
        // 【地市级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeCity: true,
        // 【区县级】信息需与档案信息保持一致
        areaDeviationCheckValueTypeCounty: true,
        // 【地址】信息需与档案信息保持一致
        areaDeviationCheckValueTypeInfo: true,
      },
      superRelatedConfigs: {
        //上级联动下级的相关配置
        //(右下角) 区划与地址信息检测
        areaDeviationCheck: [
          'areaDeviationCheckValue',
          'areaDeviationCheckValueTypeProvince',
          'areaDeviationCheckValueTypeCity',
          'areaDeviationCheckValueTypeCounty',
          'areaDeviationCheckValueTypeInfo',
          'areaDeviationCheckLine',
        ],
      },
      childRelatedConfigs: {
        //下级联动上级
        //(右下角) 区划与地址信息检测
        areaDeviationCheck_1: ['areaDeviationCheckValue'],
        areaDeviationCheck_2: [
          'areaDeviationCheckValueTypeProvince',
          'areaDeviationCheckValueTypeCity',
          'areaDeviationCheckValueTypeCounty',
          'areaDeviationCheckValueTypeInfo',
        ],
        areaDeviationCheck: ['areaDeviationCheckLine'],
      },
    };
  },
  methods: {
    onChangeDetectContent() {
      this.detectContent = [];
      let detectContent = ['timeLocation', 'areaLocation', 'cameraInfo'];
      detectContent.forEach((value) => {
        if (this[value]) {
          this.detectContent.push(value);
        }
      });

      if (this.cameraInfo) {
        if (!this.formData.existsCamera && !this.formData.cameraDeviationCheck) {
          // 摄像机信息检测时，未勾选任何内容时选中是否在指定区域标注
          Object.assign(this.formData, { existsCamera: true });
        }
      }
    },
    setDetectContent() {
      this.detectContent = this.$util.common.deepCopy(this.detectContentList);
      if (this.detectContentList && this.detectContentList.length) {
        this.detectContentList.forEach((value) => {
          this[value] = true;
        });
      }
    },
    getFormData() {
      return {
        formData: this.formData,
        detectContent: this.detectContent,
      };
    },
    //标注时间与抓拍时间允许偏差
    isRequriedTimeDeviationTip() {
      return (
        this.formData.timeDeviationCheck &&
        !this.formData.timeDeviationCheckValue &&
        this.formData.timeDeviationCheckValue !== 0
      );
    },
    // 校验时间信息检测内容，开关打开且未勾选一级项不可提交
    validTimeLoacation() {
      //校验开关
      if (!this.timeLocation) {
        return true;
      }
      //校验内容
      if (this.formData.existsTime || this.formData.timeFormCheck || this.formData.timeDeviationCheck) {
        return true;
      }
      this.$Message.warning('请选择 1、(右上角) 时间信息检测内容！');
      return false;
    },
    // 校验区划与地址信息检测，开关打开且未勾选一级项不可提交
    validAreaLocation() {
      //校验开关
      if (!this.areaLocation) {
        return true;
      }
      //校验内容
      if (this.formData.existsArea || this.formData.areaDeviationCheck) {
        return true;
      }
      this.$Message.warning('请选择 2、(右下角) 区划与地址信息检测内容！');
      return false;
    },
    // 校验摄像机信息检测，开关打开且未勾选一级项不可提交
    validCameraInfo() {
      //校验开关
      if (!this.cameraInfo) {
        return true;
      }
      //校验内容
      if (this.formData.existsCamera || this.formData.cameraDeviationCheck) {
        return true;
      }
      this.$Message.warning('请选择 3、(左下角) 摄像机信息检测内容！');
      return false;
    },
    handleSubmit() {
      return (
        !this.isRequriedTimeDeviationTip() &&
        this.validTimeLoacation() &&
        this.validAreaLocation() &&
        this.validCameraInfo()
      );
    },
    //切换准确性检测： 1综合匹配 2精确匹配
    onChangeAreaDeviationCheckValueType(val) {
      //切换时需要默认勾选的选项
      this.childRelatedConfigs[`areaDeviationCheck_${val}`].forEach((item) => {
        this.formData[item] = true;
      });
      this.formData.areaDeviationCheck = true; //父级勾选
    },
    //准确性检测联动下级复选框
    changeDetectCheckBox(superValue) {
      if (!Object.keys(this.superRelatedConfigs).includes(superValue)) {
        return;
      }
      this.superRelatedConfigs[superValue].forEach((key) => {
        this.formData[key] = this.formData[superValue];
      });
    },
    //下级复选框联动上级复选框
    changeDetectChildCheckBox(childValue) {
      let superKey = ''; //下级所关联的上级复选框key
      Object.keys(this.childRelatedConfigs).forEach((itemKey) => {
        if (this.childRelatedConfigs[itemKey].includes(childValue)) {
          superKey = itemKey;
        }
      });
      //(右下角) 区划与地址信息检测=>准确性检测需要特殊处理，需要根据areaDeviationCheckValueType：1综合匹配 2精确匹配来判断
      if (superKey.includes('areaDeviationCheck')) {
        let childConfigKeys = [
          ...this.childRelatedConfigs['areaDeviationCheck'],
          ...this.childRelatedConfigs[`areaDeviationCheck_${this.formData.areaDeviationCheckValueType}`],
        ];
        let allChildBoolArr = childConfigKeys.map((key) => this.formData[key]);
        this.formData['areaDeviationCheck'] = allChildBoolArr.some((itemBool) => !!itemBool);
        return;
      }

      let allChildBoolArr = this.childRelatedConfigs[superKey].map((key) => this.formData[key]);
      this.formData[superKey] = allChildBoolArr.some((itemBool) => !!itemBool);
    },
  },
  watch: {
    configInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && this.formModel === 'edit') {
          Object.keys(this.formData).forEach((key) => {
            if (this.configInfo.hasOwnProperty(key)) {
              this.formData[key] = this.configInfo[key];
            }
          });
        }
      },
    },
    detectContentList: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val) {
          /**
           *  将 detectContent: ['timeLocation', 'areaLocation'] 转为
           *  timeLocation:true
           *  areaLocation: true
           */
          this.setDetectContent();
        }
      },
    },
    formData: {
      immediate: true,
      deep: true,
      handler(val) {
        this.$emit('on-change', val, this.detectContent);
      },
    },
    detectContent: {
      deep: true,
      immediate: true,
      handler(val) {
        this.$emit('on-change', this.formData, val);
      },
    },
  },
};
</script>
<style scoped lang="less">
.checkbox-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  background: var(--bg-form-item);
  padding: 0 20px 0 50px;
}
.detect-content {
  //margin-left: 50px;
  .detect-detail {
    margin-left: 50px;
    .detect-location {
      margin-left: 30px;
    }
    .detect-tooltip {
      position: relative;
      margin-left: -14px;
    }
    .font-requried {
      color: var(--color-failed);
    }
    @{_deep} .ipt-requried.ivu-input-number {
      border-color: var(--color-failed);
    }
  }
}
@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: var(--bg-collapse-item);
      border: none;
      color: var(--color-title);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: var(--color-collapse-arrow);
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
    }
  }
}
</style>
