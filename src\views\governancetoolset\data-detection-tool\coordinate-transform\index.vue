<template>
  <div class="governancetoolset-child">
    <div class="child-head">
      <div class="head"><i class="icon-font icon-shujujiancegongju"></i><span class="title">设备坐标系转换</span></div>
      <Button type="text" class="btn-back mr-sm" @click="backHandle">&lt;&lt; 返回</Button>
    </div>
    <div class="governancetoolset-child-container">
      <underline-menu
        v-model="chooseMenu.chooseType"
        :data="chooseMenu.chooseTypeList"
        class="mb-lg"
        @on-change="changeChooseType"
      ></underline-menu>
      <keep-alive>
        <single-trans v-if="chooseMenu.chooseType === 'default'"></single-trans>
        <multi-trans v-else></multi-trans>
      </keep-alive>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CoordinateTransform',
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    SingleTrans: require('./single-trans.vue').default,
    MultiTrans: require('./multi-trans.vue').default,
  },
  data() {
    return {
      /**
       *
       */
      chooseMenu: {
        chooseType: 'default',
        chooseTypeList: [
          {
            code: 'default',
            label: '单个转换',
          },
          {
            code: 'multi',
            label: '批量转换',
          },
        ],
      },
    };
  },
  watch: {},
  methods: {
    // 返回
    backHandle() {
      this.$router.push({ name: 'governancetoolset' });
    },
    //切换tab
    changeChooseType(val) {
      this.chooseMenu.chooseType = val;
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .governancetoolset-child {
    background: var(--bg-content) url('~@/assets/img/datagovernance/governancet_tool_set_child_bg2.png')
      no-repeat center/cover;
    .child-head .head {
      color: #19d5f6;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .governancetoolset-child {
    background: var(--bg-content) url('~@/assets/img/datagovernance/governancet_tool_set_child_bg2_light.png')
      no-repeat center/cover;
    .child-head .head {
      color: var(--color-primary);
    }
  }
}

.governancetoolset-child {
  display: flex;
  flex: 1;
  padding: 0 20px;
  box-sizing: border-box;
  flex-direction: column;
  height: 100%;
  position: relative;
  .child-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    height: 50px;
    .head {
      display: flex;
      align-items: center;
      .icon-font {
        font-size: 24px;
        margin-right: 5px;
      }
      .title {
        font-size: 16px;
        line-height: 20px;
        font-weight: bold;
        color: var(--color-display-title);
      }
    }
  }
  .governancetoolset-child-container {
    // flex: 1;
    margin-top: 6vh;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .set-form {
      .btns {
        display: flex;
        align-items: center;
      }
      .upload-btn {
        margin-right: 16px;
        display: flex;
        align-items: center;
        /deep/.icon-font {
          font-size: 20px;
          line-height: 20px;
        }
        /deep/span {
          height: 34px;
        }
        .btn-text {
          max-width: 200px;
          display: inline-block;
        }
      }
      .select-device {
        width: 270px;
        height: 34px;
        border: 1px dashed var(--color-active);
        background: rgba(43, 132, 226, 0.1);
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--color-active);
        .icon-font {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .select-device:hover {
        background: rgba(43, 132, 226, 0.2);
        border: 1px dashed #3c90e9;
      }
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          padding-right: 10px;
        }
        .ivu-form-item-content {
          display: flex;
        }
        .ivu-radio-group-item {
          margin-right: 30px;
          .ivu-radio {
            margin-right: 10px;
          }
        }
      }
      /deep/.label-width {
        .ivu-form-item {
          display: flex;
          .ivu-form-item-label {
            width: auto !important;
            padding-right: 0;
            margin-right: 10px;
          }
          .ivu-input {
            width: 85px;
          }
          .second-text {
            color: var(--color-content);
            white-space: nowrap;
            margin-left: 10px;
          }
        }
      }
      /deep/ .label-width > .ivu-form-item-content {
        flex-direction: column;
      }
      .form-item {
        display: flex;
        margin-bottom: 20px;
        /deep/ .ivu-form-item-label::before {
          display: none;
        }
      }
      /deep/ .ivu-checkbox-wrapper {
        margin-right: 10px;
      }
      .testing {
        .testing-text {
          height: 0;
          opacity: 0;
        }
      }
    }
    .submit-btn {
      margin-top: 10px;
      width: 200px;
    }
  }

  @{_deep} .underline-menu-wrapper {
    width: auto;
    background: none;
    transform: translateX(-142px);
  }
  .btn-back {
    color: var(--color-active) !important;
  }
}
</style>
