
.archives {
  &-layout {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #DDE1EA;
    background-image: url("~@/assets/img/archives/bg_point.png");
  }
  &-container {
    display: flex;
    flex: 1;
    overflow: hidden;
  }
  &-header-content {
    height: 62px;
    background: url("~@/assets/img/archives/bg_head.png") no-repeat center center;
    background-size: auto 100%;
    position: relative;
    display: flex;
    align-items: center;
    .switch-btn, .operate-bar {
      flex: 1;
      padding: 0 20px;
      margin-top: 18px;
      box-sizing: border-box;
    }
    .switch-btn {
      font-size: 14px;
      line-height: 20px;
      .iconfont {
        margin-right: 7px;
        color: #888888;
      }
      &:hover {
        color: #000;
      }
      a{
        color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
      }
      .video-ul {
        width: 250px;
        .video-li {
          display: flex;
          align-items: center;
          margin: 5px 0;
          cursor: pointer;
          padding: 0 10px;
          .warning {
            font-family: 'MicrosoftYaHei-Bold';
            font-weight: bold;
            margin-left: 10px;
            font-size: 16px;
            line-height: 22px;
          }
        }
      }
    }
    .operate-bar {
      display: flex;
    //   justify-content: space-between;
      justify-content: flex-end;
      .operate-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        .iconfont {
          margin-right: 3px;
          font-size: 12px;
        }
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        span {
          white-space: nowrap;
        }
        &:hover {
          color: #000;
        }
      }
    }
  }
  &-title {
    position: absolute;
    width: 200px;
    left: 50%;
    margin-left: -100px;
    top: 8px;
    color: #000;
    font-size: 28px;
    text-align: center;
  }
  &-menu-content {
    padding-top: 18px;
    width: 68%;
    margin: auto;
    .menu-list {
      display: flex;
    //   justify-content: space-between;
      .menu-item {
        float: left;
        width: 171px;
        height: 46px;
        line-height: 46px;
        position: relative;
        text-align: center;
        cursor: pointer;
        &:before {
          content: '';
          position: absolute;
          border-top: 3px solid #2C86F8;
          background: #CAD0D9;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          transform: skewX(24deg);
          z-index: 0;
        }
        span {
          position: relative;
          z-index: 1;
          color: #000;
          font-size: 20px;
          font-family: MicrosoftYaHei;
        }
        &:nth-child(2),&:nth-child(4) {
        //   margin-left: -40%;
            margin-left: 2%;
        }
        &:nth-child(3) {
        //   margin-right: -40%;
            margin-left: 44%;
        }
        &:nth-child(3), &:nth-child(4) {
          &:before {
            transform: skewX(-24deg);
          }
        }
        &.active {
          &:before {
            background: #2C86F8;
          }
          span {
            color: #fff;
            font-weight: bold;
          }
        }
      }
    }
  }
}
