<!--
    * @FileDescription: 我的云盘
    * @Author: H
    * @Date: 2023/11/14
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="table-page">
        <ui-table
            class="auto-fill table" 
            ref="table" 
            :columns="columns" 
            :data="tableData"
            :loading="loading"
        >
            <template slot="fileName" slot-scope="{ row, index }">
                <span class="img-name" :title="row.fileName" @click="handleView(row, index)"><Icon type="md-images" /> {{ row.fileName }}</span>
            </template>
            <template #action="{ row }">
                <div class="btn-tips">
                    <ui-btn-tip content="下载" icon="icon-download" class="primary" @click.native="handleDownload(row)"/>
                    <ui-btn-tip content="删除" icon="icon-shanchu" class="primary" @click.native="handleDele(row)"/>
                </div>
            </template>
        </ui-table>
    </div>
</template>
<script> 
import { diskRemove } from '@/api/home';
export default {
    props:{
        tableData: {
            type: Array,
            default: () => {
                return []
            }
        },
    },
    data() {
        return {
            loading: false,
            columns: [
                { title: '文件名', slot: 'fileName' },
                { title: '大小', key: 'fileSize' },
                { title: '创建日期', key: 'createTime' },
                { title: '操作', slot: 'action', width: 174 }
                
            ],
            cloudShow: false
        }
    },
    methods: {
        // 下载
        handleDownload(row) {
            Toolkits.ocxUpDownHttp(
                "lis",
                `image`,
                `${row.fileUrl}`,
                `${row.fileName}`
            );
        },
        // 删除
        handleDele(row) {
            diskRemove(row.id)
            .then(res => {
                this.$Message.success('删除成功')
                this.$emit('deleteRow')
            })
        },
        handleView(row, index) {
            console.log(row, 'row')
            this.$emit('cloudView', row, index)
        },
    }

}
</script>
<style lang='less' scoped>
.table-page{
    width: 100%;
    height: 100%;
    .table{
        height: 100%;
        display: flex;
    }
    /deep/ .ivu-icon-md-images{
        color: #ff8209;
        font-size: 18px;
    }
    .img-name{
        cursor: pointer;
    }
}
</style>