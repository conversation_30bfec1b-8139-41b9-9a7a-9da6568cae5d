<template>
  <Form ref="formData" :label-width="80" :model="formData" :rules="ruleFormData" class="subtitle-reset">
    <FormItem label="字幕OSD设置：" class="subtitle-lable" prop="zimu"> </FormItem>
    <div class="subtitle-reset-content">
      <Row>
        <Col :span="7">
          <img
            src="@/assets/img/datagovernance/subtitle-reset.png"
            alt="示例图"
            style="max-width: 100%; max-height: 100%"
          />
        </Col>
        <Col :span="17">
          <div style="color: #e44f22" class="mb15">说明：系统自动解析配置的字段内容进行字幕重设。</div>
          <FormItem label="省" class="mb15">
            <Select v-model="formData.province" placeholder="请选择" class="form-item-width">
              <Option v-for="item in area_type" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }} </Option>
            </Select>
          </FormItem>
          <FormItem label="市">
            <Select v-model="formData.city" placeholder="请选择" class="form-item-width">
              <Option v-for="item in area_type" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }} </Option>
            </Select>
          </FormItem>
          <FormItem label="区县" class="mb15">
            <Select v-model="formData.county" placeholder="请选择" class="form-item-width">
              <Option v-for="item in area_type" :value="item.dataKey" :key="item.dataKey">{{ item.dataValue }} </Option>
            </Select>
          </FormItem>
          <FormItem label="所队">
            <Select v-model="formData.team" placeholder="请选择" class="form-item-width">
              <Option v-for="item in camera_team_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="地址信息" class="mb15">
            <Select v-model="formData.device" placeholder="请选择" class="form-item-width">
              <Option v-for="item in address_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </FormItem>
          <FormItem label="摄像机信息">
            <Select v-model="formData.camera1" placeholder="请选择" class="form-item-width1">
              <Option v-for="item in camera_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
            <Select v-model="formData.camera2" placeholder="请选择" class="form-item-width1">
              <Option v-for="item in camera_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
            <Select v-model="formData.camera3" placeholder="请选择" class="form-item-width1">
              <Option v-for="item in camera_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
            <Select v-model="formData.camera4" placeholder="请选择" class="form-item-width1">
              <Option v-for="item in camera_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
            <Select v-model="formData.camera5" placeholder="请选择" class="form-item-width1">
              <Option v-for="item in camera_type" :value="item.dataKey" :key="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </FormItem>
          <!-- 新增摄像机信息 -->
          <FormItem label=" ">
            <div class="camera1" v-if="cameraListAddList.length === 0" @click="addCarmearList">
              <span>
                <i class="icon-font icon-tianjia inline font-blue f-14 mr-sm"></i>
                <span class="font-blue camera-text">新增摄像机信息</span>
              </span>
            </div>
            <div v-else class="camera-table">
              <div class="camera-table-button">
                <div class="camera1" @click="addCarmearList">
                  <span>
                    <i class="icon-font icon-tianjia inline font-blue f-14 mr-sm"></i>
                    <span class="font-blue camera-text">新增摄像机信息</span>
                  </span>
                </div>
              </div>
              <div class="camera-table-list">
                <div>
                  <div v-for="(item, index) in cameraListAddList" :key="index + 'add'">
                    摄像机信息&nbsp;&nbsp;
                    <Select v-model="item.camera_add_pos" placeholder="请选择" style="width: 60px">
                      <Option v-for="item in cameraDirectionList" :value="item.dataKey" :key="item.dataKey"
                        >{{ item.dataValue }}
                      </Option>
                    </Select>
                    &nbsp;&nbsp;添加&nbsp;&nbsp;
                    <Select v-model="item.camera_add_type" placeholder="请选择" class="form-item-width1">
                      <Option v-for="item in camera_add_type" :value="item.dataKey" :key="item.dataKey"
                        >{{ item.dataValue }}
                      </Option>
                    </Select>
                    的&nbsp;&nbsp;
                    <Select v-model="item.camera_add_seque" placeholder="请选择" style="width: 60px" class="mr10">
                      <Option v-for="item in cameraDirectionList" :value="item.dataKey" :key="item.dataKey"
                        >{{ item.dataValue }}
                      </Option>
                    </Select>
                    <InputNumber
                      class="input-number-list"
                      v-model="item.camera_add_digit"
                      controls-outside
                      :min="0"
                      :max="100"
                      @on-blur="blurLimit(index)"
                      placeholder="请填写数量"
                    ></InputNumber>
                    &nbsp;&nbsp;位&nbsp;
                    <i class="icon-font icon-shanchu3 font-blue f-14 mr10" @click="deleteList(index)"></i>
                  </div>
                </div>
              </div>
            </div>
          </FormItem>
        </Col>
      </Row>
    </div>
  </Form>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {},
  props: {
    osdSubtitles: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    const validateRuleData = (rule, value, callback) => {
      if (
        !this.formData.province ||
        !this.formData.city ||
        !this.formData.county ||
        !this.formData.device ||
        !this.formData.team ||
        !this.formData.camera1 ||
        !this.formData.camera2 ||
        !this.formData.camera3 ||
        !this.formData.camera4 ||
        !this.formData.camera5
      ) {
        callback(new Error('请配置字幕OSD设置'));
      } else {
        callback();
      }
    };
    return {
      cameraDirectionList: [
        { dataValue: '前', dataKey: '0' },
        { dataValue: '后', dataKey: '1' },
      ],
      selectDeviceParmas: {},
      searchDeviceParmas: {},
      visible: false,
      formData: {},
      searchData: {},
      currentComponent: '',
      governanceContents: [{ label: '重设OSD字幕', code: '1' }],
      cameraListAddList: [],
      // form表单校验
      ruleFormData: {
        // immediately: [{ required: true, validator: validateRuleImmediately, trigger: 'change' }],
        zimu: [{ required: true, validator: validateRuleData, trigger: 'change' }],
      },
    };
  },
  async created() {
    if (this.address_type.length <= 0) await this.getAlldicData1();
  },
  computed: {
    ...mapGetters({
      address_type: 'algorithm/address_type',
      camera_type: 'algorithm/camera_type',
      camera_add_type: 'algorithm/camera_add_type',
      camera_team_type: 'algorithm/camera_team_type',
      area_type: 'algorithm/area_type',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData1: 'algorithm/getAlldicData1',
    }),
    // 新增摄像机信息
    addCarmearList() {
      this.cameraListAddList.push({ camera_add_digit: 0 });
    },
    // 删除新增摄像机信息
    deleteList(index) {
      this.cameraListAddList.splice(index, 1);
    },
    blurLimit(index) {
      if (this.cameraListAddList[index].four) {
        this.cameraListAddList[index].four = Math.round(Number(this.cameraListAddList[index].four));
      }
    },
    // 处理保存参数
    saveObj() {
      let textOsd = [
        {
          enable: this.formData.province === '0' ? false : true,
          code: this.formData.province,
          name: 'province',
        }, // 省
        {
          enable: this.formData.city === '0' ? false : true,
          code: this.formData.city,
          name: 'city',
        }, // 市
        {
          enable: this.formData.county === '0' ? false : true,
          code: this.formData.county,
          name: 'county',
        }, // 区县
        {
          enable: this.formData.team === '0' ? false : true,
          code: this.formData.team,
          name: 'team',
        }, // 所队
        {
          enable: this.formData.device === '0' ? false : true,
          code: this.formData.device,
          name: 'device',
        }, // 地址信息
      ];
      let nameOsd = [
        {
          enable: this.formData.camera1 === '0' ? false : true,
          code: this.formData.camera1,
          index: 1,
        },
        {
          enable: this.formData.camera2 === '0' ? false : true,
          code: this.formData.camera2,
          index: 2,
        },
        {
          enable: this.formData.camera3 === '0' ? false : true,
          code: this.formData.camera3,
          index: 3,
        },
        {
          enable: this.formData.camera4 === '0' ? false : true,
          code: this.formData.camera4,
          index: 4,
        },
        {
          enable: this.formData.camera5 === '0' ? false : true,
          code: this.formData.camera5,
          index: 5,
        },
      ]; // 摄像机信息
      let addOsd = this.cameraListAddList; // 新增摄像机信息
      return { textOsd: textOsd, nameOsd: nameOsd, addOsd: addOsd };
    },
    // 表单校验
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          this.$Message.error('请将信息填写完整！');
          message = 'error';
        }
      });
      return message;
    },
    clearData() {
      Object.keys(this.formData).map((key) => {
        this.formData[key] = null;
      });
    },
  },
  watch: {
    osdSubtitles: {
      handler() {
        this.cameraListAddList = this.osdSubtitles.addOsd || [];
        this.osdSubtitles.textOsd?.map((val) => {
          this.formData[val.name] = val.code;
        });
        this.osdSubtitles.nameOsd?.map((val) => {
          this.formData['camera' + val.index] = val.code;
        });
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
.subtitle-reset {
  .subtitle-lable {
    margin-bottom: 6px;
    @{_deep} .ivu-form-item-label {
      width: 100% !important;
      text-align: left;
    }
  }
  @{_deep} .ivu-form-item-error-tip {
    margin-left: 36px;
    margin-top: 4px;
  }
  .subtitle-reset-content {
    width: 98%;

    background: var(--bg-sub-content);
    border-radius: 4px;
    padding: 20px;
    @{_deep} .ivu-form-item-label {
      width: 100px !important;
    }

    .ivu-row {
      max-height: 280px;
      overflow-y: scroll;
    }
    .form-item-width {
      width: 300px;
    }
    .form-item-width1 {
      width: 200px;
      margin-right: 10px;
    }
  }
  .mb15 {
    margin-bottom: 15px;
  }
  .mb30 {
    margin-bottom: 30px;
  }
  .ml30 {
    margin-left: 30px;
  }
  .ml10 {
    margin-left: 10px;
  }
  .mr10 {
    margin-right: 10px;
  }
  .input-number-list {
    padding: 0px;
    width: 100px;
    @{_deep} .ivu-input-number-controls-outside-btn {
      display: none;
    }
  }
  .icon-renwutianjia {
    position: relative;
    top: -2px;
  }
  .camera1 {
    width: 168px;
    height: 34px;
    background: var(--bg-btn-default);
    border: 1px solid var(--border-btn-default-active);
    color: var(--color-primary);
    display: flex;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    &:hover {
      background: rgba(43, 132, 226, 0.2);
      border: 1px solid #3c90e9;
    }
    .icon-tianjia {
      line-height: 30px;
    }
  }
  .camera-table {
    display: table;
    width: calc(100% - 100px);
    color: #f5f5f5;
    .camera-table-button {
      display: table-cell;
      width: 208px;
      border: 1px solid var(--border-color);
      padding: 0 20px;
      vertical-align: middle;
    }
    .camera-table-list {
      display: table-cell;
      border: 1px solid var(--border-color);
      border-left: none;
      width: 100%;

      & > div {
        padding: 10px 0 0 20px;
        max-height: 150px;
        overflow-y: scroll;
      }
      & > div > div {
        margin-bottom: 10px;
      }
    }
  }
}
.input-width {
  width: 140px;
}
.select-input-width {
  width: 140px;
}
.inline-block {
  display: inline-block;
}
.right-margin {
  margin-right: 15px;
}
</style>
