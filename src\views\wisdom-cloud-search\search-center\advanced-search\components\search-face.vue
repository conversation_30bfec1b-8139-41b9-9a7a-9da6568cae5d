<template>
  <div class="search card-border-color">
    <Form
      :inline="true"
      :class="visible ? 'advanced-search-show' : ''"
      @submit.native.prevent
    >
      <!-- 关键字搜索 -->
      <div class="general-search">
        <div class="input-content">
          <div class="upload-input-list">
            <uiUploadImg
              :algorithmType="1"
              v-model="queryParam.urlList"
              @imgUrlChange="imgUrlChange"
              size="small"
            />
          </div>
          <div class="other-search">
            <div class="other-search-top card-border-color">
              <FormItem label="相似度:" class="slider-form-item">
                <div class="slider-content">
                  <i
                    class="iconfont icon-jian add-subtract"
                    @click="addAndSubtract(0)"
                  ></i>
                  <Slider v-model="queryParam.similarity"></Slider>
                  <i
                    class="iconfont icon-jia add-subtract"
                    @click="addAndSubtract(1)"
                  ></i>
                  <span>{{ queryParam.similarity }}%</span>
                </div>
              </FormItem>
              <FormItem label="算法选择:" prop="algorithm">
                <CheckboxGroup
                  v-if="queryParam.features.length > 0"
                  v-model="queryParam.algorithmSelect"
                  @on-change="handleChangeAlgor"
                >
                  <Checkbox
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span
                      :class="[item.dataKey == 'GLST' ? 'gerling' : 'hk']"
                      >{{ item.dataValue }}</span
                    >
                  </Checkbox>
                </CheckboxGroup>
                <RadioGroup v-else v-model="queryParam.algorithmVendorType">
                  <Radio
                    :label="item.dataKey"
                    v-for="(item, index) in algorithmTypeList"
                    :key="index"
                  >
                    <span
                      :class="[item.dataKey == 'GLST' ? 'gerling' : 'hk']"
                      >{{ item.dataValue }}</span
                    >
                  </Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="身份证:" prop="idCardNo">
                <Input
                  v-model="queryParam.idCardNo"
                  placeholder="请输入身份证号"
                ></Input>
              </FormItem>
              <FormItem label="姓 名:" prop="name">
                <Input
                  v-model="queryParam.name"
                  placeholder="请输入姓名"
                ></Input>
              </FormItem>
              <FormItem label="去 重:" prop="isDeduplication">
                <Checkbox v-model="queryParam.isDeduplication"></Checkbox>
              </FormItem>
            </div>
            <div class="other-search-bottom">
              <div class="flex">
                <FormItem label="设备资源:">
                  <div class="select-tag-button" @click="selectDevice()">
                    选择设备/已选（{{ queryParam.selectDeviceList.length }}）
                  </div>
                </FormItem>
                <FormItem label="抓拍时段:">
                  <hl-timerange
                    ref="timerange"
                    @onceChange="handleTimeOnce"
                    @change="handleTimeChange"
                    :reflectValue="queryParam.timeSlot"
                    :reflectTime="{
                      startDate: queryParam.startDate,
                      endDate: queryParam.endDate,
                    }"
                  >
                  </hl-timerange>
                </FormItem>
                <FormItem label="视频身份:">
                  <Input
                    v-model="queryParam.videoIdentity"
                    placeholder="请输入"
                  ></Input>
                </FormItem>
              </div>
              <div class="btn-group">
                <span
                  class="advanced-search-text primary"
                  v-if="queryParam.features.length < 1"
                  @click="advancedSearchHandle($event)"
                >
                  更多条件 <i class="iconfont icon-jiantou"></i>
                </span>
                <Button type="primary" @click="searchHandle">查询</Button>
                <Button @click="resetHandle">重置</Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--更多搜索条件-->
      <div
        class="advanced-search"
        @click="($event) => $event.stopPropagation()"
      >
        <template>
          <div class="advanced-search-item card-border-color">
            <Row>
              <Col span="6">
                <FormItem label="眼       镜:">
                  <ui-tag-select
                    ref="glasses"
                    @input="
                      (e) => {
                        input(e, 'glasses');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureGlasses"
                      :key="$index"
                      :name="item.dataKey"
                      :label="item.dataValue"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="性       别:">
                  <ui-tag-select
                    ref="gender"
                    @input="
                      (e) => {
                        input(e, 'gender');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureGender"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
          <!-- v-if="queryParam.algorithmVendorType !== 'HK'" -->
          <div class="advanced-search-item card-border-color">
            <Row>
              <Col span="6">
                <FormItem label="帽       子:">
                  <ui-tag-select
                    ref="cap"
                    @input="
                      (e) => {
                        input(e, 'cap');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureCap"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
              <Col span="6">
                <FormItem label="口       罩:">
                  <ui-tag-select
                    ref="faceMask"
                    @input="
                      (e) => {
                        input(e, 'faceMask');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureMask"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="advanced-search-item">
            <Row>
              <Col span="6">
                <FormItem label="年  龄   段:">
                  <ui-tag-select
                    ref="age"
                    @input="
                      (e) => {
                        input(e, 'age');
                      }
                    "
                  >
                    <ui-tag-select-option
                      v-for="(item, $index) in ipbdFaceCaptureAge"
                      :key="$index"
                      :name="item.dataKey"
                    >
                      {{ item.dataValue }}
                    </ui-tag-select-option>
                  </ui-tag-select>
                </FormItem>
              </Col>
            </Row>
          </div>
        </template>
      </div>
      <!-- 选择设备 -->
      <select-device
        ref="selectDevice"
        showOrganization
        :checkedLabels="checkedLabels"
        @selectData="selectData"
      />
    </Form>
  </div>
</template>
<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import { getFaceLibNameByLibType } from "@/api/wisdom-cloud-search";
import uiUploadImg from "@/components/ui-upload-new-img/index";
import LabelModal from "@/views/holographic-archives/components/relationship-map/label-add";
export default {
  components: {
    LabelModal,
    uiUploadImg,
  },
  props: {},
  data() {
    return {
      visible: false,
      queryParam: {
        dataSource: 2, // 数据资源
        selectDeviceList: [], // 设备资源
        startDate: "", // 抓拍时段 - 开始时间
        endDate: "", // 抓拍时段 - 结束时间
        portraitType: "", // 动态库 - 类型
        urlList: [],
        similarity: 75,
        algorithmVendorType: "GLST",
        algorithmSelect: ["GLST"],
        timeSlot: "近一天",
        features: [],
        isDeduplication: false,
        videoIdentity: "",
      },
      checkedLabels: [], // 已选择的标签
      sourceList: [], // 来源
      captureTimePeriod: [
        { name: "近一天", value: "1" },
        { name: "近三天", value: "2" },
        { name: "近一周", value: "3" },
        { name: "自定义", value: "4" },
      ],
      keyword: "",
    };
  },
  async created() {
    // await this.getDictData();
    this.queryParam.similarity =
      this.globalObj.searchForPicturesDefaultSimilarity - 0;
    if (this.$route.query.archiveNo) {
      this.queryParam.plateNo = JSON.parse(this.$route.query.archiveNo);
    } else {
      // this.queryParam.plateNo = '';
    }
    this.tableSourceList();
    window.addEventListener("click", (e) => {
      this.visible = false;
    });
  },
  watch: {},
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      ipbdFaceCaptureClustering: "dictionary/getIpbdFaceCaptureClustering", // 聚类
      ipbdFaceCaptureGlasses: "dictionary/getIpbdFaceCaptureGlasses", // 眼镜
      ipbdFaceCaptureMask: "dictionary/getIpbdFaceCaptureMask", // 口罩
      ipbdFaceCaptureCap: "dictionary/getIpbdFaceCaptureCap", // 帽子
      classifySearchData: "common/getClassifySearchData", //查询数据
    }),
  },
  methods: {
    // ...mapActions({
    // 	getDictData: 'dictionary/getDictAllData'
    // }),
    ...mapMutations("common", ["setClassifySearchData"]),
    advancedSearchHandle($event) {
      $event.stopPropagation();
      if (this.visible) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    handleKeyword(val) {
      let reg = /^\d+$/;
      if (reg.test(this.keyword)) {
        //数字
      } else {
      }
    },
    dataSourceChange(e) {
      this.searchHandle();
    },
    // 相似度加减
    addAndSubtract(index) {
      if (index == 0) {
        this.queryParam.similarity -= 1;
      } else {
        this.queryParam.similarity += 1;
      }
    },
    // 算法选择
    handleChangeAlgor(value) {
      if (value.length == 0 && this.queryParam.features.length > 0) {
        this.queryParam.algorithmVendorType = this.algorithmTypeSelect[0];
        this.queryParam.algorithmSelect = this.algorithmTypeSelect[0];
      } else {
        this.queryParam.algorithmVendorType = value[0];
      }
    },
    // 时间
    handleTimeOnce(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
    },
    handleTimeChange(obj) {
      this.queryParam.timeSlot = obj.timeSlot;
      this.queryParam.startDate = obj.startDate;
      this.queryParam.endDate = obj.endDate;
      let params = {
        list: this.queryParam.selectDeviceList,
        timeSlot: obj.timeSlot,
        startDate: obj.startDate,
        endDate: obj.endDate,
        searchSelect: 1,
      };
      this.setClassifySearchData(params);
    },
    reflectTime(time) {
      this.handleTimeOnce(time);
    },
    // 搜索函数
    searchHandle() {
      this.visible = false;
      this.$parent.pageInfo.pageNumber = 1;
      // 以图搜索下算法为多选，至少选择一个
      if (
        !this.queryParam.algorithmSelect.length &&
        this.queryParam.features.length
      ) {
        this.$Message.warning("至少选择一个算法！");
        return;
      }
      this.$emit("search", this.queryParam);
    },
    /**
     * 重置，清空数据
     */
    resetHandle() {
      this.resetClear();
      this.$emit("reset");
      this.visible = false;
    },
    resetClear() {
      // 清空组件选中状态
      this.$refs.glasses.clearChecked();
      this.$refs.age.clearChecked();
      this.$refs.faceMask.clearChecked();
      this.$refs.cap.clearChecked();
      this.$refs.gender.clearChecked();
      this.queryParam = {
        timeSlot: "近一天",
        dataSource: 2, // 资源类型
        selectDeviceList: [], // 设备资源
        features: [], // 设备
        imageBases: [],
        algorithmVendorType: this.algorithmTypeSelect[0],
        algorithmSelect: [],
        // startDate: '', // 抓拍时段 - 开始时间
        // endDate: '', // 抓拍时段 - 结束时间
        portraitType: "", // 动态库 - 类型
        gender: "",
        age: "",
        cap: "",
        dateType: "",
        faceMask: "",
        glasses: "",
        idCardNo: "",
        name: "",
        similarity: this.globalObj.searchForPicturesDefaultSimilarity - 0,
        urlList: [],
        videoIdentity: "",
      };
      this.$refs.timerange.clearChecked(false);
      this.$forceUpdate();
    },
    checkedHandle(arr) {
      console.log(arr);
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.queryParam[key] = e;
      this.$forceUpdate();
    },
    /**
     * 选择设备
     */
    selectDevice() {
      let keyWords = this.$route.query.keyWords
        ? JSON.parse(this.$route.query.keyWords)
        : "";
      this.$refs.selectDevice.show(this.queryParam.selectDeviceList, keyWords);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.queryParam.selectDeviceList = list;
      let params = {
        list: this.queryParam.selectDeviceList,
        timeSlot: this.queryParam.timeSlot,
        startDate: this.queryParam.startDate,
        endDate: this.queryParam.endDate,
        searchSelect: 1,
      };
      this.setClassifySearchData(params);
      this.$forceUpdate();
    },
    urlImgList(list, index = 0) {
      if (index == 1) {
        this.queryParam.urlList = [...list];
        this.imgUrlChange([...list]);
      } else if (index == 2) {
        let arr = [...this.queryParam.urlList, ...list].filter((item) => {
          return item;
        });
        this.queryParam.urlList = [...arr, ""];
        this.imgUrlChange([...arr]);
      } else {
        this.queryParam.urlList.unshift(...list);
        this.imgUrlChange([...list]);
      }
    },
    /**
     * 图片上传结果返回
     */
    imgUrlChange(list, imgDetail) {
      console.log(this.queryParam.urlList, "this.queryParam.urlList");
      console.log(list, imgDetail, "list, imgDetail");
      let searchImage = {
        manual: true,
        image: {
          name: imgDetail.name,
          url: imgDetail.url,
        },
        position: imgDetail.position,
      };
      // 以图搜图字段
      let features = [];
      list.forEach((item) => {
        if (item) {
          features.push(item.feature);
        }
      });
      this.queryParam.features = features;
      if (this.queryParam.features > 0) {
        this.queryParam.glasses = "";
        this.queryParam.gender = "";
        this.queryParam.cap = "";
        this.queryParam.faceMask = "";
        this.queryParam.age = "";
      }
      this.$emit("imgChange", searchImage);
    },
    /**
     * 来源
     */
    tableSourceList() {
      getFaceLibNameByLibType({ libSource: 2 })
        .then((res) => {
          this.sourceList = res.data;
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {});
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index.less";
.btn-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .advanced-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin-right: 30px;
    font-size: 14px;

    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}

.search {
  padding: 10px 20px 0;
  border-bottom: 1px solid #ffff;
  display: flex;
  width: 100%;
  justify-content: space-between;
  position: relative;

  .ivu-form-inline {
    width: 100%;
  }

  .ivu-form-item {
    margin-bottom: 0;
    margin-right: 30px;
    display: flex;
    align-items: center;

    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
  }

  .general-search {
    display: flex;
    width: 100%;

    .input-content {
      flex: 1;
      display: flex;

      .upload-input-list {
        display: flex;
        max-width: 440px;
      }

      .other-search {
        display: flex;
        flex: 1;
        box-sizing: border-box;
        flex-direction: column;
        padding-left: 10px;

        .other-search-top {
          display: flex;
          border-bottom: 1px dashed #fff;
        }

        .ivu-form-item {
          display: flex;
          margin-bottom: 10px;

          /deep/ .ivu-form-item-label {
            padding-right: 10px;
          }
          .add-subtract {
            cursor: pointer;
          }
          .ivu-input-wrapper,
          .ivu-select {
            width: 200px;
          }
        }

        .other-search-bottom {
          display: flex;
          justify-content: space-between;
          padding-top: 10px;
          box-sizing: border-box;
          /deep/ .ivu-form-item-content {
            display: flex;
          }
          .slider-form-item {
            /deep/ .ivu-form-item-content {
              display: flex;
              align-items: center;
            }
          }
        }
      }
    }
    /deep/ .ivu-form-item-content {
      display: flex;
      align-items: center;
    }
  }

  .advanced-search {
    display: flex;
    position: absolute;
    width: 100%;
    padding: 0 20px;
    box-sizing: border-box;
    z-index: 11;
    max-height: 0px;
    top: 100%;
    left: 0;
    transition: max-height 0.3s;
    overflow: auto;
    flex-direction: column;

    .advanced-search-item {
      &.justify-content-normal {
        justify-content: normal;
      }

      //   display: flex;
      //   justify-content: flex-start;
      // justify-content: space-between;
      padding: 10px 0;
      border-bottom: 1px dashed #fff;

      //   align-items: center;
      &:first-child {
        border-top: 1px solid;
      }

      .ivu-form-item {
        margin-right: 30px;

        &:last-child {
          margin-right: 0;
        }

        &.percent-70 {
          width: 70%;
        }

        display: flex;

        .text-radio-group {
          margin-left: -10px;
        }
      }

      .btn-group {
        flex: 1;
        justify-content: flex-end;
      }

      /deep/ .ivu-form-item-content {
        display: flex;
        align-items: center;
      }
    }
  }

  .advanced-search-show {
    .advanced-search {
      max-height: 400px;
      transition: max-height 0.7s;
    }

    .advanced-search-text {
      /deep/ .icon-jiantou {
        transform: rotate(180deg);
        transition: transform 0.2s;
      }
    }
  }
}

.searchPading {
  padding: 10px 20px;
}
</style>
