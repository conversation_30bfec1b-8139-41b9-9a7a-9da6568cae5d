<template>
  <div class="data-warehouse-overview">
    <div class="overview-top">
      <div class="overview-top-left bg1">
        <img
          class="img"
          src="@/assets/img/data-warehouse/images/camora.png"
          alt=""
        />
        <div class="camare_c">
          <div class="f_16">摄像机</div>
          <div class="f_32 fw_b">
            <count-to
              :start-val="0"
              :end-val="deviceTypeDate[1]"
              :duration="1000"
              class="num"
            ></count-to>
          </div>
          <div class="dis_f f_14">
            <div class="left-box">
              <i class="iconfont icon-shebeizichan"></i>
              枪机
            </div>
            <div>
              <i class="iconfont icon-qiuji"></i>
              球机
            </div>
          </div>
          <div class="dis_f f_16 fw_b">
            <div class="left-box">
              <count-to
                :start-val="0"
                :end-val="cameraData[0]"
                :duration="1000"
                class="num"
              ></count-to>
            </div>
            <div>
              <count-to
                :start-val="0"
                :end-val="cameraData[1]"
                :duration="1000"
                class="num"
              ></count-to>
            </div>
          </div>
        </div>
        <div class="camare_r">
          <div class="f_14">
            <div class="circle circle_bg1"></div>
            一类点<span class="f_16 fw_b ml_10">
              <count-to
                :start-val="0"
                :end-val="monitorData[1]"
                :duration="1000"
                class="num"
              ></count-to>
            </span>
          </div>
          <div class="f_14">
            <div class="circle circle_bg2"></div>
            二类点<span class="f_16 fw_b ml_10">
              <count-to
                :start-val="0"
                :end-val="monitorData[2]"
                :duration="1000"
                class="num"
              ></count-to>
            </span>
          </div>
          <div class="f_14">
            <div class="circle circle_bg3"></div>
            三类点<span class="f_16 fw_b ml_10">
              <count-to
                :start-val="0"
                :end-val="monitorData[3]"
                :duration="1000"
                class="num"
              ></count-to>
            </span>
          </div>
        </div>
        <img
          class="img_bg"
          src="@/assets/img/data-warehouse/images/card_bg.png"
          alt=""
        />
      </div>
      <div class="overview-top-right">
        <div
          class="data-type ml_10"
          :class="item.bg"
          v-for="(item, index) in typeList"
          :key="index"
        >
          <img
            class="img"
            :src="
              require('@/assets/img/data-warehouse/images/' + item.imageUrl)
            "
            alt=""
          />
          <div>
            <div class="title f_16">{{ item.title }}</div>
            <div class="num f_32">
              <count-to
                :start-val="0"
                :end-val="item.num"
                :duration="1000"
                class="num"
              ></count-to>
            </div>
          </div>
          <img
            class="img_bg"
            src="@/assets/img/data-warehouse/images/card_bg.png"
            alt=""
          />
        </div>
      </div>
    </div>
    <div class="overview-container">
      <div class="face-card mr_10">
        <ui-card title="人脸抓拍数据统计">
          <div class="face-total">
            <total-card
              :title="'人脸抓拍总数'"
              :total="dataStatistics.face"
              :addNum="dayAddData.face"
              :addPercent="'+5%'"
              :isAdd="true"
              :imageUrl="'total1.png'"
            ></total-card>
          </div>
          <div class="pie-box">
            <div class="echarts-title">
              <div class="icon"></div>
              人脸抓拍数Top10
              <DatePicker
                type="date"
                size="small"
                @on-change="handleTimeChange($event, 1)"
                format="yyyy-MM-dd"
                v-model="faceDate"
                :options="timeOption"
                placeholder="请选择时间"
              ></DatePicker>
            </div>
            <div ref="pieChart" class="pie-chart"></div>
            <!-- <div ref="barChart" class="bar-chart"></div> -->
          </div>
        </ui-card>
      </div>
      <div class="car-card mr_10">
        <ui-card title="车辆抓拍数据统计">
          <div class="car-total">
            <total-card
              :title="'车辆抓拍总数'"
              :total="dataStatistics.vehicle"
              :addNum="dayAddData.vehicle"
              :addPercent="'-5%'"
              :isAdd="false"
              :imageUrl="'total2.png'"
            ></total-card>
          </div>
          <div class="pie-box">
            <div class="echarts-title">
              <div class="icon"></div>
              车辆抓拍数Top10
              <DatePicker
                type="date"
                size="small"
                @on-change="handleTimeChange($event, 2)"
                format="yyyy-MM-dd"
                v-model="vehicleDate"
                :options="timeOption"
                placeholder="请选择时间"
              ></DatePicker>
            </div>
            <div ref="barChart" class="bar-chart"></div>
          </div>
        </ui-card>
      </div>
      <div class="right-box">
        <div class="no-car-card mr_10">
          <ui-card title="非机动车数据统计">
            <div class="nocar-total">
              <total-card
                :title="'非机动车采集数量'"
                :total="dataStatistics.nonMotor"
                :addNum="dayAddData.nonMotor"
                :addPercent="'-5%'"
                :isAdd="false"
                :imageUrl="'total3.png'"
              ></total-card>
            </div>
          </ui-card>
        </div>
        <div class="body-car-card">
          <ui-card title="人体数据统计">
            <div class="body-total">
              <total-card
                :title="'人体抓拍总数'"
                :total="dataStatistics.human"
                :addNum="dayAddData.human"
                :addPercent="'+5%'"
                :isAdd="true"
                :imageUrl="'total4.png'"
              ></total-card>
            </div>
          </ui-card>
        </div>
        <div class="line-card">
          <div class="tabs">
            <div
              @click="tabsChange(item, index)"
              :class="curIndex == index ? 'tab-active' : ''"
              class="tabs-item"
              v-for="(item, index) in tabList"
              :key="index"
            >
              {{ item.name }}
            </div>
          </div>
          <ui-card title="数据接入趋势">
            <div ref="dataLine" class="line-chart"></div>
          </ui-card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import totalCard from "./components/total-card";
import * as echarts from "echarts";
import { statisticView, deviceAccessTop, capture } from "@/api/data-warehouse";
import { dateTime, getConfigDate } from "@/util/modules/common";
import CountTo from "vue-count-to";
export default {
  name: "data-warehouse-overview",
  components: {
    totalCard,
    CountTo,
  },
  data() {
    return {
      typeList: [
        {
          title: "人脸卡口",
          num: 0,
          type: "11",
          imageUrl: "faceka.png",
          bg: "bg2",
        },
        {
          title: "车辆卡口",
          num: 0,
          type: "2",
          imageUrl: "carka.png",
          bg: "bg3",
        },
        { title: "Wi-Fi", num: 0, type: "3", imageUrl: "wifi.png", bg: "bg4" },
        { title: "IMSI", num: 0, type: "4", imageUrl: "imsi.png", bg: "bg5" },
      ],
      curIndex: 0,
      tabList: [
        { name: "人脸", type: "face" },
        { name: "车辆", type: "vehicle" },
        { name: "人体", type: "human" },
        { name: "非机动车", type: "nonMotor" },
        { name: "MAC", type: "mac" },
        { name: "IMSI", type: "imsi" },
      ],
      myEchart: null,
      colors: [
        "#2C86F8",
        "#f29f4c",
        "#1962BF",
        "#1FAF8A",
        "#48BAFF",
        "#879DC6",
        "#4DC7D4",
        "#3be4b9",
      ],
      lineData: [3000, 3500, 3600, 3500, 3400, 3100, 3200],
      sevenDaylist: [],
      pieData: [
        { name: "海康", value: 46.54 },
        { name: "格灵深瞳", value: 53.46 },
      ],
      barData: [755, 788, 798, 805, 855, 865, 929, 938, 1027, 1214],
      capturelist: [],
      deviceTypeDate: {
        1: 0,
      },
      cameraData: {
        0: 0,
        1: 0,
      },
      monitorData: {
        1: 0,
        2: 0,
        3: 0,
      },
      dataStatistics: {
        face: 0,
        vehicle: 0,
        human: 0,
        nonMotor: 0,
      },
      dayAddData: {
        face: 0,
        vehicle: 0,
        human: 0,
        nonMotor: 0,
      },
      timeOption: {
        disabledDate(date) {
          return date.valueOf() > Date.now() - 86400000;
        },
      },
      vehicleDate: "",
      faceDate: "",
    };
  },
  mounted() {
    this.vehicleDate = getConfigDate(-1)[0];
    this.faceDate = getConfigDate(-1)[0];
    this.init();
    this.getPieData();
    this.getBarData();
    // this.getLineData()
  },
  methods: {
    init() {
      statisticView("ivcp_device").then((res) => {
        if (res.data && res.data.dataJson) {
          let allData = JSON.parse(res.data.dataJson);
          let deviceList = allData[0].countList;
          deviceList.forEach((item) => {
            if (this.deviceTypeDate[item.type] > -1) {
              this.deviceTypeDate[item.type] = item.count || 0;
            }
          });
          let devMap = new Map(deviceList.map((item) => [item.type, item]));
          this.typeList.forEach((item) => {
            if (devMap.get(item.type)) {
              item.num = Number(devMap.get(item.type).count);
            }
          });
          let chileList = allData[1].countList;
          chileList.forEach((item) => {
            if (this.cameraData[item.type] > -1) {
              this.cameraData[item.type] = Number(item.count) || 0;
            }
          });
          let monitorList = allData[2].countList;
          monitorList.forEach((item) => {
            if (this.monitorData[item.type] > -1) {
              this.monitorData[item.type] = Number(item.count) || 0;
            }
          });
        }
      });
      statisticView("ivcp_capture").then((res) => {
        if (res.data && res.data.dataJson) {
          let data = JSON.parse(res.data.dataJson);
          let datamap = new Map(data.map((item) => [item.type, item]));
          for (let key in this.dataStatistics) {
            if (datamap.get(key)) {
              this.dataStatistics[key] = datamap.get(key).count;
            }
          }
        }
      });
      statisticView("ivcp_capture_access_7day").then((res) => {
        if (res.data && res.data.dataJson) {
          let data = JSON.parse(res.data.dataJson);
          this.sevenDaylist = data;
          this.getLineData();
        }
      });
      capture().then((res) => {
        let data = res.data;
        let datamap = new Map(data.map((item) => [item.type, item]));
        for (let key in this.dayAddData) {
          if (datamap.get(key)) {
            this.dayAddData[key] = datamap.get(key).count;
          }
        }
      });
    },
    tabsChange(item, index) {
      this.curIndex = index;
      this.lineData.forEach((item, index) => {
        this.lineData[index] = item + Math.random() * 1000;
      });
      this.getLineData(item.type);
    },
    getLineData(type = "face") {
      let list = [];
      this.sevenDaylist.forEach((item) => {
        if (item.type == type) {
          list = item.list;
        }
      });
      let xAxis = [],
        data = [];
      list.forEach((item) => {
        xAxis.push(item.dateStr);
        data.push(item.count);
      });
      this.lineCharts(this.$refs.dataLine, data, xAxis);
    },
    getPieData() {
      let params = {
        dataTime: this.faceDate,
        dataType: 1,
      };
      this.getData(params);
    },
    handleTimeChange(value, index) {
      let params = {
        dataTime: value,
        dataType: index,
      };
      this.getData(params);
    },
    getBarData() {
      let params = {
        dataTime: this.vehicleDate,
        dataType: 2,
      };
      this.getData(params);
    },
    getData(params) {
      deviceAccessTop(params).then((res) => {
        this.barData = [];
        let ydata = [];
        if (res.data) {
          res.data.forEach((item) => {
            this.barData.push(item.accessNum);
            ydata.push(item.deviceName);
          });
          if (params.dataType == 1) {
            this.barCharts(
              this.$refs.pieChart,
              this.barData.reverse(),
              ydata.reverse()
            );
          } else {
            this.barCharts(
              this.$refs.barChart,
              this.barData.reverse(),
              ydata.reverse()
            );
          }
        }
      });
    },
    //柱状排名配置
    barCharts(el, data, ydata) {
      this.myEchart = echarts.init(el);
      let img1 =
        "data:image/png;base64,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";
      let img2 =
        "data:image/png;base64,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";
      let img3 =
        "data:image/png;base64,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";
      // let ydata = ['xxxxxx路口10wwwwww', 'xxxxxx路口9', 'xxxxxx路口8', 'xxxxxx路口7', 'xxxxxx路口6', 'xxxxxx路口5', 'xxxxxx路口4', 'xxxxxx路口3', 'xxxxxx路口2', 'xxxxxx路口1',];
      let option = {
        tooltip: {
          trigger: "axis",
        },
        grid: {
          left: "100",
          right: "20",
          bottom: "0",
          top: "20",
          containLabel: false,
        },
        xAxis: {
          type: "value",
          show: false,
        },
        yAxis: {
          type: "category",
          data: ydata,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 100,
            align: "left",
            color: "#e5e5e5",
            interval: 0,
            formatter: (name, index) => {
              let yname = "";
              if (name.length > 6) {
                yname = name.substr(0, 6) + "...";
              } else {
                yname = name;
              }
              if (data.length - index < 4) {
                return `{icon${data.length - index}|}` + `{name|${yname}}`;
              } else {
                return `{count|${data.length - index}}` + `{name|${yname}}`;
              }
            },
            rich: {
              icon1: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img1,
                },
              },
              icon2: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img2,
                },
              },
              icon3: {
                width: 20,
                height: 20,
                align: "center",
                borderRadius: 50,
                backgroundColor: {
                  image: img3,
                },
              },
              count: {
                padding: [2, 0, 0, 0],
                width: 20,
                height: 18,
                color: "rgba(0,0,0,0.35)",
                align: "center",
                fontSize: 12,
                fontFamily: "DIN",
                fontWeight: 500,
                // shadowColor: '#008AFF',
                // borderColor: '#008AFF',
                borderRadius: 50,
                borderWidth: 1,
                // backgroundColor: {
                // 	image: img4,
                // },
              },
              name: {
                width: 85,
                fontSize: 12,
                align: "left",
                color: "#515a6e",
                fontFamily: "Source Han Sans CN",
                fontWeight: 500,
              },
            },
          },
        },
        series: [
          {
            type: "bar",
            showBackground: false,
            label: {
              show: true,
              position: "right",
              color: "#2C86F8",
              formatter: "{c}",
            },
            barWidth: 10,
            itemStyle: {
              emphasis: {
                barBorderRadius: 7,
              },
              //颜色样式部分
              normal: {
                barBorderRadius: 8,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: "#5BC7FF" },
                  { offset: 1, color: "#2C86F8" },
                ]),
              },
            },
            data: data,
          },
        ],
      };
      this.myEchart.setOption(option, true);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    //饼图配置
    pieCharts(el, data) {
      this.myEchart = echarts.init(el);
      var img =
        "data:image/png;base64,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";
      var option = {
        color: this.colors,
        // grid: {
        // 	left: 0,
        // 	right: 0,
        // 	containLabel: true
        // },
        legend: {
          x: "center",
          y: "bottom",
          icon: "circle",
          itemHeight: 10,
          show: true,
        },
        graphic: {
          elements: [
            {
              type: "image",
              z: 3,
              style: {
                image: img,
                width: 60,
                height: 60,
              },
              left: "center",
              top: "center",
              position: [100, 100],
            },
          ],
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["40%", "60%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            zlevel: 1,
            labelLine: {
              normal: {
                show: true,
                length: 10,
                length2: 60,
              },
            },
            label: {
              normal: {
                formatter: "{d|{d}%}\n{b|{b}}",
                padding: [0, -60, 0, -60],
                rich: {
                  d: {
                    fontSize: 16,
                    fontWeight: 600,
                    // color: this.colors[0],
                    align: "center",
                    padding: [0, 0, 10, 0],
                  },
                  b: {
                    fontSize: 12,
                    color: "#969696",
                    align: "center",
                    padding: [0, 0, 0, 0],
                  },
                },
              },
            },
            data: data.map((item, index) => {
              return {
                label: {
                  color: this.colors[index],
                },
                ...item,
              };
            }),
          },
          {
            name: "内边框",
            type: "pie",
            hoverAnimation: false,
            center: ["50%", "50%"],
            radius: ["32%", "32%"],
            label: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 9,
                itemStyle: {
                  normal: {
                    borderWidth: 1,
                    borderColor: "#EAEBEE",
                    borderType: "solid",
                  },
                },
              },
            ],
          },
          {
            name: "内圆",
            type: "pie",
            hoverAnimation: false,
            center: ["50%", "50%"],
            radius: "27%",
            label: {
              normal: {
                show: false,
              },
            },
            data: [
              {
                value: 9,
                itemStyle: {
                  normal: {
                    color: "#F9F9F9",
                  },
                },
              },
            ],
          },
        ],
      };
      this.myEchart.setOption(option, true);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
    //曲线图配置
    lineCharts(el, lineData, xData) {
      this.myEchart = null;
      this.myEchart = echarts.init(el);
      var option = {
        legend: {
          x: "center",
          y: 10,
          icon: "rect",
          itemHeight: 2,
          show: false,
        },
        tooltip: {
          trigger: "axis",
        },
        grid: {
          top: "6%",
          left: "7%",
          right: "1%",
          bottom: "10%",
        },
        xAxis: [
          {
            type: "category",
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 10,
            },
            data: xData,
          },
        ],
        yAxis: [
          {
            type: "value",
            min: 0,
            // max: 140,
            splitNumber: 4,
            splitLine: {
              show: true,
              lineStyle: {
                color: "#e3e3e3",
                type: "dashed",
              },
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              rotate: 45,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: {
          // name: item.name,
          type: "line",
          smooth: true,
          showAllSymbol: true,
          symbol: "circle",
          symbolSize: 12,
          lineStyle: {
            normal: {
              color: "#2C86F8",
            },
          },
          itemStyle: {
            color: "#2C86F8",
            borderWidth: 3,
            borderColor: "#f3f3f3",
          },
          data: lineData,
        },
      };
      this.myEchart.setOption(option, true);
      window.addEventListener("resize", () => this.myEchart.resize());
    },
  },
};
</script>
<style lang="less" scoped>
.dis_f {
  display: flex;
}
.f_14 {
  font-size: 14px;
}
.f_16 {
  font-size: 16px;
}
.f_24 {
  font-size: 28px;
}
.f_28 {
  font-size: 16px;
}
.f_32 {
  font-size: 32px;
}
.fw_b {
  font-weight: bold;
}
.bg1 {
  background: linear-gradient(267deg, #39a1fc 0%, #2a76f5 100%);
}
.bg2 {
  background: linear-gradient(180deg, #36caef 0%, #3aa6e8 100%);
}
.bg3 {
  background: linear-gradient(180deg, #3be4b9 0%, #1faf8a 100%);
}
.bg4 {
  background: linear-gradient(180deg, #4da4d4 0%, #0283cd 100%);
}
.bg5 {
  background: linear-gradient(180deg, #4fd1df 0%, #41add1 100%);
}
.mr_10 {
  margin-right: 10px;
}
.ml_10 {
  margin-left: 10px;
}
.data-warehouse-overview {
  width: 100%;
  .overview-top {
    width: 100%;
    height: 180px;
    margin-bottom: 10px;
    display: flex;
    .overview-top-left {
      width: 26%;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      padding: 20px;
      color: #fff;
      border-radius: 4px;
      .img {
        width: 110px;
        margin-right: 20px;
      }
      .camare_c {
        width: 35%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .dis_f {
          width: 100%;
          .left-box {
            width: 50%;
          }
        }
      }
      .camare_r {
        width: 40%;
        height: 100%;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        border-left: 1px solid rgba(255, 255, 255, 0.3);
        padding-left: 20px;
        .f_14 {
          display: flex;
          align-items: center;
        }
        .circle {
          width: 10px;
          height: 10px;
          border-radius: 5px;
          border: 2px solid #fff;
          margin-right: 5px;
        }
        .circle_bg1 {
          background: rgb(0, 132, 255);
        }
        .circle_bg2 {
          background: rgb(132, 43, 190);
        }
        .circle_bg3 {
          background: rgb(197, 153, 31);
        }
      }
      .img_bg {
        position: absolute;
        width: 200px;
        right: 0;
        bottom: 0;
      }
    }
    .overview-top-right {
      width: 74%;
      height: 100%;
      display: flex;
      .data-type {
        width: 25%;
        border-radius: 4px;
        position: relative;
        color: #fff;
        display: flex;
        align-items: center;
        padding: 20px;
        .img {
          width: 110px;
          margin-right: 20px;
        }
        .img_bg {
          position: absolute;
          width: 200px;
          right: 0;
          bottom: 0;
        }
      }
    }
  }
  .overview-container {
    height: calc(~"100% - 190px");
    display: flex;
    /deep/.ui-card {
      width: 100%;
      height: 100%;
      .card-content {
        width: 100%;
        height: calc(~"100% - 30px");
      }
    }
    .face-card {
      width: 24%;
      height: 100%;
      .face-total {
        width: 100%;
        height: 33%;
      }
      .pie-box {
        width: 100%;
        height: 67%;
      }
    }
    .car-card {
      width: 24%;
      height: 100%;
      .car-total {
        width: 100%;
        height: 33%;
      }
      .pie-box {
        width: 100%;
        height: 67%;
      }
    }
    .right-box {
      // flex: 1;
      width: calc(~"52% - 20px");
      height: 100%;
      display: flex;
      flex-wrap: wrap;
      .no-car-card {
        width: calc(~"50% - 5px");
        height: 40%;
        margin-right: 10px;
        .nocar-total {
          height: 100%;
        }
      }
      .body-car-card {
        width: calc(~"50% - 5px");
        height: 40%;
        .body-total {
          height: 100%;
        }
      }
      .line-card {
        width: 100%;
        height: calc(~"60% - 10px");
        margin-top: 10px;
        position: relative;
        .line-chart {
          width: 100%;
          height: 320px;
        }
      }
    }
  }
  .tabs {
    position: absolute;
    right: 10px;
    top: 10px;
    display: flex;
    .tabs-item {
      height: 24px;
      padding: 0 10px;
      font-size: 14px;
      line-height: 24px;
      color: #000;
      margin-right: 10px;
      border-radius: 3px;
      border: 1px solid #d3d7de;
      cursor: pointer;
      z-index: 9999;
    }
    .tab-active {
      background-color: #2c86f8;
      color: #fff;
    }
  }
  .echarts-title {
    font-size: 14px;
    font-weight: bold;
    margin-top: 20px;
    display: flex;
    align-items: center;
    .icon {
      width: 6px;
      height: 6px;
      background: #2c86f8;
      margin-right: 5px;
    }
  }
  .pie-chart,
  .bar-chart {
    width: 100%;
    height: 360px;
  }
}
/deep/.ivu-input {
  height: 24px;
  padding: 1px 7px;
}
</style>
