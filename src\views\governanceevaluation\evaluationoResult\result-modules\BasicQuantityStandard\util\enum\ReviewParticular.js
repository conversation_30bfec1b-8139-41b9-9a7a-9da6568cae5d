export const iconStaticsList = [
  {
    name: '应上报采集区域类型数量',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-yingjianceshebeishuliang',
    fileName: 'standardCount',
  },
  {
    name: '采集区域类型达标数量',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'qualifiedStandardCount',
  },
  {
    name: '视频监控达标数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancehegeshebeishu',
    fileName: 'videoStandardCount',
  },
  {
    name: '人脸卡口达标数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancebuhegeshebeishu',
    fileName: 'faceStandardCount',
  },
  {
    name: '车辆卡口达标数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancebuhegeshebeishu',
    fileName: 'vehicleStandardCount',
  },
  {
    name: '视频图像采集区域数量达标率',
    count: '0',
    countStyle: { color: 'var(--color-bluish-green-text)' },
    style: { color: 'var(--color-bluish-green-text)' },
    iconName: 'icon-tianbaozhunqueshuai',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];

export const tableColumn = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  { title: '采集区域编码', key: 'cjqyCode', minWidth: 100, align: 'center' },
  { title: '采集区域名称', key: 'cjqyName', minWidth: 300, align: 'center', tooltip: true },
  { title: '类型', key: 'cjqyTypeName', minWidth: 120, align: 'center' },
  {
    title: '人脸卡口需上报数量',
    align: 'center',
    children: [
      { title: '应上报', key: 'faceConfigNum', minWidth: 100, align: 'center' },
      { title: '实际上报', slot: 'faceReportNum', minWidth: 100, align: 'center' },
    ],
  },
  {
    title: '车辆卡口需上报数量',
    align: 'center',
    children: [
      { title: '应上报', key: 'vehicleConfigNum', minWidth: 100, align: 'center' },
      { title: '实际上报', slot: 'vehicleReportNum', minWidth: 100, align: 'center' },
    ],
  },
  {
    title: '视频监控需上报数量',
    align: 'center',
    children: [
      { title: '应上报', key: 'videoConfigNum', minWidth: 100, align: 'center' },
      { title: '实际上报', slot: 'videoReportNum', minWidth: 100, align: 'center' },
    ],
  },
  {
    title: '达标情况',
    slot: 'qualified',
    align: 'center',
    minWidth: 100,
  },
];

export const formItemData = [
  {
    type: 'select',
    key: 'cjqyType',
    label: '采集区域类型',
    placeholder: '请选择采集区域类型',
    options: [
      { value: 0, label: '重点必报' },
      { value: 1, label: '其他必报' },
    ],
  },
  {
    type: 'select',
    key: 'qualified',
    label: '达标情况',
    placeholder: '请选择达标情况',
    options: [
      { value: '1', label: '达标' },
      { value: '2', label: '不达标' },
    ],
  },
];
