// vue.config.js
const path = require('path')
// const CompressionPlugin = require('compression-webpack-plugin')

// 拼接路径
// const resolve = (dir) => require('path').join(__dirname, dir)
// 更改代理地址需要同时修改文件  config/api/websocket.js 的地址
module.exports = {
  // webpack-dev-server 相关配置
  devServer: {
    proxy: {
      // 设置代理
      '/ivcp': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/ipbd': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/iras': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/qsdi': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/ivdg': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/icbd': {
        target: 'http://*************:8888/',
        changeOrigin: true
      },
      '/npgisdataservice': {
        target: 'http://*************:7777/',
        changeOrigin: true,
        pathRewrite: {
          '/npgisdataservice': '/netposa'
        }
      }
      // '/WangJianService': {
      //   target: 'http://*************:10012/',
      //   changeOrigin: true,
      //   pathRewrite: {
      //     '/WangJianService': '',
      //   },
      // },
    }
  },
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true
      }
    }
  },
  productionSourceMap: true,
  chainWebpack: config => {
    const less = config.module.rule('less').toConfig()
    const useable = { ...less.oneOf[3], test: /\-useable\.less$/ }
    useable.use = [...useable.use]
    useable.use[0] = { loader: 'style-loader', options: { injectType: 'lazyStyleTag' } }
    config.module.rule('less').merge({ oneOf: [useable] })
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        // path.resolve(__dirname, './src/style/index.less')
      ]
    }
  }
}
