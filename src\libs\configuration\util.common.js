import Vue from "vue";
import router from "@/router";
import axios from "axios";
import CryptoJS from "crypto-js";
const isServer = Vue.prototype.$isServer;

/**
 * 一维数组转树状数组
 * arr: 要转换的一维数组
 * id: 唯一识别
 * pid: 父级唯一识别
 */

export function arrayToJson(arr, id, pid, children = "children") {
  const tempArr = [];
  const tempObj = {};
  for (let i = 0, l = arr.length; i < l; i++) {
    tempObj[arr[i][id]] = arr[i];
  }
  for (let i = 0, l = arr.length; i < l; i++) {
    const key = tempObj[arr[i][pid]];
    if (key) {
      if (!key[children]) {
        key[children] = [];
        key[children].push(arr[i]);
      } else {
        key[children].push(arr[i]);
      }
    } else {
      tempArr.push(arr[i]);
    }
  }
  return tempArr;
}
import cookies from "./util.cookies";
const TOKEN_KEY = "ivcp-token";
const TOKEN_KEY_DEMO = "icbd-token";
/* 设置token*/
export const setToken = (token) => {
  // cookies.set(TOKEN_KEY, token)
  localStorage.setItem(TOKEN_KEY, token);
};
/* 获取token*/
export const getToken = () => {
  // const token = cookies.get(TOKEN_KEY)
  const token = localStorage.getItem(TOKEN_KEY);
  if (token) return token;
  else return false;
};
/* 设置token*/
export const setdemoToken = (domeToken) => {
  localStorage.setItem(TOKEN_KEY_DEMO, domeToken);
};
/* 获取token*/
export const getdemoToken = () => {
  const domeToken = localStorage.getItem(TOKEN_KEY_DEMO);
  if (domeToken) return domeToken;
  else return false;
};

// 获取授权信息
export const getAuthor = () => {
  return window.localStorage.getItem("authorInfo") || "";
};

/**
 * 下载
 * @param href
 * @param name
 */
export const download = (href, name) => {
  const eleLink = document.createElement("a");
  fetch(href)
    .then((res) => res.blob())
    .then((blob) => {
      // 将链接地址字符内容转变成blob地址
      eleLink.href = URL.createObjectURL(blob);
      eleLink.download = name || "";
      eleLink.click();
      eleLink.remove();
    });
};

export const jsonSort = (e, t) => {
  var n = Object.prototype.toString.call(e),
    i = {};
  if (n === "[object Object]") {
    var o = [];
    for (var r in e) o.push(r);
    o.sort(), t != 1 && o.reverse();
    for (var r in o) {
      r = o[r];
      var a = e[r];
      i[r] = jsonSort(a, t);
    }
    return i;
  }
  if (n === "[object Array]") {
    var s = [],
      n = Object.prototype.toString.call(e[0]);
    if (n === "[object String]" || n === "[object Number]") {
      return e.sort(), t != 1 && e.reverse(), e;
    }
    for (var l = 0; l < e.length; l++) s.push(jsonSort(e[l], t));
    return s;
  }
  return (i = e);
};

/**
 * @method colorRgb
 * @description 颜色值格式16进制转换成rgb/rgba
 * @param {String} [color] 16进制颜色值
 * @param {Number} [opacity] 如果时间格式为rgba格式需传入透明度
 * @return {String} 返回rgb/rgba
 */
export const colorRgb = (color, opacity) => {
  // 16进制颜色值的正则
  let reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  // 把颜色值变成小写
  let colorValue = color.toLowerCase();
  if (reg.test(color)) {
    // 如果只有三位的值，需变成六位，如：#fff => #ffffff
    if (colorValue.length === 4) {
      let colorNew = "#";
      for (let i = 1; i < 4; i += 1) {
        colorNew += colorValue
          .slice(i, i + 1)
          .concat(colorValue.slice(i, i + 1));
      }
      colorValue = colorNew;
    }
    // 处理六位的颜色值，转为RGB
    let colorChange = [];
    for (let i = 1; i < 7; i += 2) {
      colorChange.push(parseInt("0x" + colorValue.slice(i, i + 2)));
    }
    if (opacity) {
      return `RGBA(${colorChange.join(",")}, ${opacity})`;
    } else {
      return `RGB(${colorChange.join(",")})`;
    }
  } else {
    return colorValue;
  }
};

/**
 * @description 验证8-20位数字或字母密码
 * @param {*} pwd 密码
 */
export const checkPwd = (pwd) => {
  const re = /^[a-zA-Z]\w{7,19}$/;
  if (re.test(pwd)) return true;
  else return false;
};

/**
 * @description 验证身份证号
 * @param {*} cardId 身份证号
 */
export const checkCardId = (cardId) => {
  // const idcard_patter = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  const idcard_patter =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
  if (idcard_patter.test(cardId)) return true;
  else return false;
};

/**
 * @description 验证手机号码
 * @param {Number} phoneNumber 手机号
 */
export const checkPhone = (phoneNumber) => {
  const phone_number_patter = /^[1][3456789][0-9]{9}$/;
  if (phone_number_patter.test(phoneNumber)) return true;
  else return false;
};

/**
 * @description 验证邮箱
 * @param {*} email 邮箱
 */
export const checkEmail = (email) => {
  const email_patter =
    /^\w+((-\w+)|(\.\w+))*\@{1}\w+\.{1}\w{2,4}(\.{0,1}\w{2}){0,1}/gi;
  if (email_patter.test(email)) return true;
  else return false;
};

/**
 * @description 验证IP
 * @param {*} ip IP
 */
export const checkIp = (ip) => {
  const ip_patter =
    /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi;
  if (ip_patter.test(ip)) return true;
  else return false;
};

/**
 * @description 验证短号
 * @param {*} shortNumber 短号
 */
export const checkShortNumber = (shortNumber) => {
  const short_number_patter = /^\d{3,6}$/g;
  if (short_number_patter.test(shortNumber)) return true;
  else return false;
};

// des解码
export const decryptDes = (ciphertext, key) => {
  const keyHex = CryptoJS.enc.Latin1.parse(key);
  // direct decrypt ciphertext
  const decrypted = CryptoJS.DES.decrypt(
    {
      ciphertext: CryptoJS.enc.Base64.parse(ciphertext),
    },
    keyHex,
    {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }
  );
  return decrypted.toString(CryptoJS.enc.Utf8);
};

export function rafThrottle(fn) {
  let locked = false;
  return function (...args) {
    if (locked) return;
    locked = true;
    window.requestAnimationFrame((_) => {
      fn.apply(this, args);
      locked = false;
    });
  };
}

/* istanbul ignore next */
export const on = (function () {
  if (!isServer && document.addEventListener) {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.attachEvent("on" + event, handler);
      }
    };
  }
})();
/*
 *将Date/String类型,解析为String类型.
 *传入String类型,则先解析为Date类型
 *不正确的Date,返回 ''
 *如果时间部分为0,则忽略,只返回日期部分.
 *日期格式对应字符如下(年-yyyy,月-MM,日-dd,时-hh,分-mm,秒-ss,毫秒-S 字符区分大小写)
 */
export function formatDate(v, format) {
  if (!format) {
    format = "yyyy-MM-dd hh:mm:ss";
  }
  if (typeof v == "string") v = this.parseDate(v);
  if (!(v instanceof Date)) {
    return "";
  }
  var o = {
    "M+": v.getMonth() + 1, //month
    "d+": v.getDate(), //day
    "h+": v.getHours(), //hour
    "m+": v.getMinutes(), //minute
    "s+": v.getSeconds(), //second
    "q+": Math.floor((v.getMonth() + 3) / 3), //quarter
    // "S" : v.getMilliseconds() //millisecond
  };

  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (v.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }

  if (/(S+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      ("000" + v.getMilliseconds()).substr(("" + v.getMilliseconds()).length)
    );
  }

  for (var k in o) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return format;
}

/**
 * @description: 车牌号校验
 * @param {string} vehicleNumber 车牌号
 * @return {boolean} 是否为合规车牌号
 */
export function isVehicleNumber(vehicleNumber) {
  let xreg = /^[\u4e00-\u9fa5][A-Z][0-9A-Z]{6}$/;
  /* let xreg =
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DABCEFGHJK]$)|([DABCEFGHJK][A-HJ-NP-Z0-9][0-9]{4}$))/; */ // 2021年新能源车牌不止有DF

  let creg =
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;

  if (vehicleNumber.length == 7) {
    return creg.test(vehicleNumber);
  } else if (vehicleNumber.length == 8) {
    return xreg.test(vehicleNumber);
  } else {
    return false;
  }
}

/**
 * @description: 检测json字符串是否合法
 * @param {string} text
 * @return { boolean }
 */
export function isValidJSON(text) {
  try {
    JSON.parse(text);
    return true;
  } catch (error) {
    return false;
  }
}

/* istanbul ignore next */
export const off = (function () {
  if (!isServer && document.removeEventListener) {
    return function (element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false);
      }
    };
  } else {
    return function (element, event, handler) {
      if (element && event) {
        element.detachEvent("on" + event, handler);
      }
    };
  }
})();

/**
 * @description: 页面新开页签跳转，query.noMenu存在，则不展示菜单，query.noSearch存在，则不展示搜索条件
 * @param {object} routes 路由信息
 */
function openNewWindow(routes) {
  const { href } = router.resolve(routes);
  window.open(href, "_blank");
}

/**
 * @description: 获取路网信息
 * @param {array} points 地图坐标点
 */
function getRoadNet(points) {
  // 每次请求前先清除上一次的路网信息，避免因前一次数据多，新数据少，导致数据污染
  return new Promise((resolve, reject) => {
    let pointList = points.map((v) => v.lon + "," + v.lat).join(";");
    let data = new FormData();
    data.append("stops", pointList);
    axios
      .post("/npgisdataservice/gis/routing", data)
      .then((res) => {
        // 路网画线
        if (res.data?.length) {
          let roadPoints = [];
          res.data.forEach((item) => {
            if (!item.expend) return;
            // if (item.expend.indexOf("POINT") === -1) {    // 过滤到非路网点
            roadPoints = roadPoints.concat(
              item.expend.split(";").map((p) => {
                return {
                  lon: p.split(",")[0],
                  lat: p.split(",")[1],
                };
              })
            );
          });
          resolve(roadPoints);
        } else {
          resolve([]);
        }
      })
      .catch(() => {
        resolve([]);
      });
  });
}

export default {
  setToken,
  getToken,
  setdemoToken,
  getdemoToken,
  getAuthor,
  arrayToJson,
  download,
  jsonSort,
  colorRgb,
  decryptDes,
  isVehicleNumber,
  rafThrottle,
  on,
  off,
  formatDate,
  isValidJSON,
  openNewWindow,
  getRoadNet,
};
