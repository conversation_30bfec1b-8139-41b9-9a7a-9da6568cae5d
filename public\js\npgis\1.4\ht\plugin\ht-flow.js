!function(q){"use strict";var P="ht",V=q[P],u=<PERSON><PERSON>,s=Math,j=s.PI,l=2*j,$=s.sin,H=s.cos,v=s.atan2,t=s.sqrt,D=s.max,Y=s.floor,U=(s.round,s.ceil),T=s.abs,c=V.Shape,Q=V.Edge,J=V.List,y=V.Style,N=V.graph,o=u.getInternal(),R=o.ui(),L=null,h="__segmentLengths",b="__lineTotalLength",M="__linePoints",W="__distance0",f="flow.count",I="flow.step",a="flow.element.max",g="flow.element.count",w="flow.element.min",i="flow.element.space",E="flow.element.autorotate",O="flow.element.background",d="flow.element.shadow.max",z="flow.element.shadow.min",Z="flow.element.shadow.begincolor",k="flow.element.shadow.endcolor",B="flow.element.shadow.visible",r="flow.element.image",S="flow",F="prototype",n=N.GraphView[F],A=V.Data[F],G=R.DataUI[F],X=R.ShapeUI[F],_=R.EdgeUI[F],x=V.DataModel[F],e=X._80o,C=_._80o,p=x.prepareRemove,K=_._79o,m=X._79o,gp=n.setDataModel,ks=function(l){return document.createElement(l)},tc=function(r){for(var K=0,Q=r.length-1,B=0;Q>B;B++){var v=r[B],G=r[B+1],N=G.x-v.x,p=G.y-v.y,Z=t(N*N+p*p);K+=Z}return K},Sd=function(A,z){for(var p=0,l=0,C=0,B=A.length-1,r=0;B>r;r++){var u=A[r],I=A[r+1],c=I.x-u.x,f=I.y-u.y;if(C=t(c*c+f*f),p+=C,p>z){p-=C,l=r;break}}var o=A[l],S=A[l+1],M=v(S.y-o.y,S.x-o.x),E=z-p,f=$(M)*E,c=H(M)*E;return{x:o.x+c,y:o.y+f}},Dg=function(z,f,g,I){var k=H(I),s=$(I),E=k*f-s*g,h=s*f+k*g;return z?{x:z.x+E,y:z.y+h}:{x:E,y:h}},Zd=function(D,m){D[h]=D[b]=D[M]=m[W]=L},ip=function(Y,S,A,i,C,B){var o,I,E,Q,b,r,X,t,e,d,G,P=[];if(T(i)>l&&(i=l),b=U(T(i)/(j/4)),o=i/b,I=-o,E=-A,b>0){r=Y+H(A)*C,X=S+$(-A)*B,P.push({x:r,y:X});for(var V=0;b>V;V++)E+=I,Q=E-I/2,t=Y+H(E)*C,e=S+$(E)*B,d=Y+H(Q)*(C/H(I/2)),G=S+$(Q)*(B/H(I/2)),P.push({x:d,y:G}),P.push({x:t,y:e})}return P},Qk=function(s,z,A){if(z==L){var Z=s._data;if(Z instanceof c){if(z=Z.getPoints(),A=Z.getSegments(),(!A||0===A.size())&&z){A=new V.List([1]);for(var I=1;I<z.size();I++)A.add(2)}}else if(Z instanceof Q){var $=s._78o;if($){var y=$.type,D=$.points,H=$.segments,Y=$._4O;if(!y||D){var F=$._69o,T=F.x,t=F.y,i=$._70o,N=i.x,f=i.y;if(y)H?(z=new J({x:T,y:t}),z.addAll(D),z.add({x:N,y:f}),A=new J(H._as)):(z=new J({x:T,y:t}),A=new J([1]),D.each(function(s){z.add(s),A.add(2)}),z.add({x:N,y:f}),A.add(2));else if($.looped){z=new J(ip(T,t,0,l,$.radius,$.radius)),A=new J([1]);for(var I=0;I<(z.size()-1)/2;I++)A.add(3)}else z=new J,$.center?(z.add({x:$.c1.x,y:$.c1.y}),z.add({x:T,y:t}),z.add({x:N,y:f}),z.add({x:$.c2.x,y:$.c2.y}),A=new J([1,2,2,2])):(z.add({x:T,y:t}),z.add({x:N,y:f}),A=new J([1,2]))}else if(Y)if(z=new J(Y.points._as),Y.segments)A=new J(Y.segments._as);else{A=new J([1]);for(var I=1;I<Y.points.size();I++)A.add(2)}}}}if(z&&A){for(var e=o.toPointsArray(z._as,A._as,50),p=e.length,u=[],I=0;p>I;I++){var S=e[I];S.length>1&&u.push(e[I])}return u}},Zk=function(q){var l=q._data,x=Qk(q);if(x){l.s("flow.reverse")&&(x.reverse(),x.forEach(function(d){d.reverse()}));for(var k=0,g=[],I=x.length,C=0;I>C;C++){var $=x[C],S=tc($);k+=S,g.push(S)}if(l[h]=g,l[b]=k,l[M]=x,l instanceof Q){var f=u.unionPoint(x),Z=f.x+f.width/2,j=f.y+f.height/2;l.$10e={x:Z,y:j}}gj(q,!0)}},gj=(u.getPercentPositionOnPoints=function(t,r,O){if(t){if(Array.isArray(t)&&(t=new V.List(t)),"number"==typeof t.get(0)){for(var M=new V.List,c=0;c<t.size();c+=2)M.add({x:t.get(c),y:t.get(c+1)});t=M}if(!r){r=[];for(var c=0;c<t.size();c++)0===c?r.push(1):r.push(2)}Array.isArray(r)&&(r=new V.List(r));var s=Qk(L,t,r);if(s){var f;if(0===O)f=s[0][0];else if(100===O)s=s[s.length-1],f=s[s.length-1];else{for(var o=0,u=[],w=s.length,c=0;w>c;c++){var S=s[c],$=tc(S);o+=$,u.push($)}for(var h=o*O/100,D=Rh(h,u),A=0,Q=0;D>Q;Q++)A+=u[Q];h-=A,f=Sd(s[D],h)}return f}}},n.getPercentPosition=function(m,H){var B=this,g=B.getDataUI(m),X=Qk(g);if(X){var T;if(0===H)T=X[0][0];else if(100===H)X=X[X.length-1],T=X[X.length-1];else{for(var h=0,D=[],G=X.length,E=0;G>E;E++){var $=X[E],f=tc($);h+=f,D.push(f)}for(var e=h*H/100,i=Rh(e,D),P=0,r=0;i>r;r++)P+=D[r];e-=P,T=Sd(X[i],e)}return T}},function(c,_){var l=c._data,J=l[b],o=l.s(f),p=l.s(I),C=0,A=l[h],$=l.s(a),L=l.s(w),N=l.s(g),T=($-L)/(N-1),n=[];if(A){if(1===N)n.push($);else if(2===N)n.push($),n.push(L);else{if(!(N>2))return;n.push($);for(var y=N-2;y>0;y--)n.push(L+T*y);n.push(L)}var S=0,R=0;n.forEach(function(K){N-1>S&&(R+=l.getFlowElementSpace(K)),S++}),R+=($+L)/2,C=(J-o*R+R)/o;var E=c[W];for(null==E&&(E=0),_||(E+=p);E>J+R;){var t=c._overCount;t?t++:t=1,t>=o&&(t=null),c._overCount=t,l.s("flow.autoreverse")?t?E-=C+R:(E=0,l.s("flow.reverse",!l.s("flow.reverse"))):E-=C+R}c[W]=E}}),Ln=function(W){var i=W.data,A=this.dm();if(i&&"add"===W.kind){var r=A.$3e;r&&i.s(S)&&r.indexOf(i)<0&&r.push(i)}"clear"===W.kind&&(A.$3e=[])},Em=function(x){var d=x.property,b=x.data,F=x.newValue,Y=this.dm().$3e;if(Y&&"s:flow"===d)if(F)Y.indexOf(b)<0&&Y.push(b);else for(var n=Y.length,O=0;n>O;O++)if(Y[O]===b){Y.splice(O,1);break}},Rh=Rh=function(J,n){for(var r=0,e=n.length,d=0;e>d;d++){var M=n[d];if(r+=M,r>J)return d}return d},Cf=function(D){var u=this,k=u._data,q=k[b],G=k[h],T=k[M],Q=k.s(f),J=0,F=u[W],Z=k.s(a),A=k.s(w),X=k.s(g),y=k.s(z),V=k.s(d),o=k.s(E),c=(V-y)/(X-1),$=(Z-A)/(X-1),p=k.getRotation?k.getRotation():0,S=k.getPosition?k.p():k.$10e,K=[],m=[];if(F!=L){if(1===X)K.push(Z);else if(2===X)K.push(Z),K.push(A);else{if(!(X>2))return;K.push(Z);for(var Y=X-2;Y>0;Y--)K.push(A+$*Y);K.push(A)}if(1===X)m.push(V);else if(2===X)m.push(V),m.push(y);else{if(!(X>2))return;m.push(V);for(var Y=X-2;Y>0;Y--)m.push(y+c*Y);m.push(y)}var O=0,P=0;K.forEach(function(W){X-1>O&&(P+=k.getFlowElementSpace(W)),O++}),P+=(Z+A)/2,J=(q-Q*P+P)/Q,D.save();for(var Y=0;Q>Y;Y++){var C=F,r=0,N=u._overCount,i=0;k.s("flow.autoreverse")&&N&&N>Q-(Y+1)||(C-=Y*(J+P),O=0,K.forEach(function(n){var a=C-r;if(a>=0&&q>a){var W=!0,Z=Rh(a,G);i=0;for(var J=0;Z>J;J++)i+=G[J];if(a-=i,W){var w=Sd(T[Z],a),B=p;if(o){for(var F=T[Z],P=0,c=0,x=0;x<F.length-1;x++){var $=F[x],L=F[x+1],l=L.x-$.x,N=L.y-$.y,Y=t(l*l+N*N);if(P+=Y,P>a){c=x;break}}var E=F[c];B+=v(w.y-E.y,w.x-E.x)}p&&(w=Dg(S,w.x-S.x,w.y-S.y,p)),u.$5e(D,w,n,m[O],B)}}r+=k.getFlowElementSpace(K[O]),O++}))}D.restore()}},eo=function(){var o=this,J=o._data,f=J.s(a),k=!1,W=L;if(o._6I||(k=!0),W=J instanceof Q?K.call(o):m.call(o),J.s(S)&&k){var R=J.s(d),H=J.s(B);H&&R>f&&(f=R),f>0&&u.grow(W,U(f/2)),Zk(o)}return!J.s(S)&&k&&Zd(J,o),W};A.getFlowElementSpace=function(){return this.s(i)},X._79o=eo,_._79o=eo,y[a]==L&&(y[a]=7),y[w]==L&&(y[w]=0),y[f]==L&&(y[f]=1),y[I]==L&&(y[I]=3),y[g]==L&&(y[g]=10),y[i]==L&&(y[i]=3.5),y[E]==L&&(y[E]=!1),y[O]==L&&(y[O]="rgba(255, 255, 114, 0.4)"),y[Z]==L&&(y[Z]="rgba(255, 255, 0, 0.3)"),y[k]==L&&(y[k]="rgba(255, 255, 0, 0)"),y[B]==L&&(y[B]=1),y[d]==L&&(y[d]=22),y[z]==L&&(y[z]=4),x.prepareRemove=function(B){p.call(this,B);var n=B._dataModel,u=n.$3e;if(u)for(var t=u.length,s=0;t>s;s++)if(u[s]===B){u.splice(s,1);break}},n.setDataModel=function(z){var v=this,o=v._dataModel;if(o!==z){o&&(o.umm(Ln,v),o.umd(Em,v),o.$3e=[]),z.mm(Ln,v),z.md(Em,v);var P=z.$3e=[];z.each(function(V){V.s(S)&&P.indexOf(V)<0&&P.push(V)}),gp.call(v,z)}},n.setFlowInterval=function(J){var u=this,B=u.$11e;u.$11e=J,u.fp("flowInterval",B,J),u.$7e!=L&&(clearInterval(u.$7e),delete u.$7e,u.enableFlow(J))},n.getFlowInterval=function(){return this.$11e},n.$9e=function(){var j,f,$,E=this,L=E.tx(),Z=E.ty(),H=E.getZoom(),m=E.getWidth(),l=E.getHeight(),G={x:-L/H,y:-Z/H,width:m/H,height:l/H},d=E.dm().$3e,_=E._56I,y=new J;if(d.forEach(function(n){_[n.getId()]&&(j=E.getDataUI(n),j&&($=j._79o(),$&&y.add($)))}),0!==y.size()&&(y.each(function(B){u.intersectsRect(G,B)&&(f=u.unionRect(f,B))}),f&&(f&&(u.grow(f,D(1,1/H)),f.x=Y(f.x*H)/H,f.y=Y(f.y*H)/H,f.width=U(f.width*H)/H,f.height=U(f.height*H)/H,f=u.intersection(G,f)),f))){var P=E._canvas.getContext("2d");P.save(),P.lineCap=u.lineCap,P.lineJoin=u.lineJoin,o.translateAndScale(P,L,Z,H),P.beginPath(),P.rect(f.x,f.y,f.width,f.height),P.clip(),P.clearRect(f.x,f.y,f.width,f.height),E.$6e(P,f),P.restore()}},n.$6e=function(w,O){var s,j,z=this;z._93db(w),z.each(function(l){z._56I[l._id]&&(s=z.getDataUI(l),s&&(j=s._79o(),(!O||u.intersectsRect(O,j))&&s._42(w)))}),z._92db(w)},n.enableFlow=function(A){var U=this,J=U.dm(),b=J.$3e;U.$7e==L&&(b.forEach(function(V){var z=U.getDataUI(V);Zk(z)}),U.$7e=setInterval(function(){J.$3e.forEach(function(G){gj(U.getDataUI(G))}),U.$9e()},A||U.$11e||50))},n.disableFlow=function(){var E=this;clearInterval(E.$7e),delete E.$7e;var V=E.dm().$3e;V&&E.$9e()},G.$5e=function(h,q,p,n,W){var i=this,C=i._data,H=i.gv,V=C.s(O),Q=C.s(Z),K=C.s(k),D=C.s(B),m=H.$8e,J=C.s(r);if(m==L&&(m=H.$8e={}),h.beginPath(),J!=L){var G=u.getImage(J),N=p/2;h.translate(q.x,q.y),h.rotate(W),h.translate(-q.x,-q.y),u.drawImage(h,G,q.x-N,q.y-N,p,p,C),h.translate(q.x,q.y),h.rotate(-W),h.translate(-q.x,-q.y)}else h.fillStyle=V,h.arc(q.x,q.y,p/2,0,l,!0),h.fill();if(D){var F=22,T=F+"_"+Q+"_"+K,I=m[T];if(I==L){var $=ks("canvas");o.setCanvas($,F,F);var b=$.getContext("2d"),_=F/2,U=_,S=_;o.translateAndScale(b,0,0,1),b.beginPath();var z=b.createRadialGradient(U,S,0,U,U,_);z.addColorStop(0,Q),z.addColorStop(1,K),b.fillStyle=z,b.arc(U,S,_,0,l,!0),b.fill(),I=m[T]=$}var N=n/2;u.drawImage(h,I,q.x-N,q.y-N,n,n,C)}},_._80o=function(F){C.call(this,F);var X=this,B=X._data,H=X.gv;B.s(S)&&H.$7e!=L&&Cf.call(X,F)},X._80o=function(f){e.call(this,f);var P=this,J=P._data,k=P.gv;J.s(S)&&k.$7e!=L&&Cf.call(P,f)}}("undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:this,Object);