<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles">
    <div class="model-wrapper">
      <section class="left-content">
        <p class="content-title base-label-color mb-sm">
          所有设备列表（
          <span class="color-failed">{{ pageData.totalCount }}</span> )条
        </p>
        <ui-search-tree
          class="padding20"
          placeholder="请输入组织机构名称或组织机构编码"
          node-key="orgCode"
          :max-height="600"
          :tree-data="treeData"
          :default-props="defaultProps"
          :default-keys="defaultExpandedKeys"
          :current-node-key="currentNodeKey"
          @selectTree="selectTree"
        >
        </ui-search-tree>
      </section>
      <section class="middle-content">
        <div class="search-box content-title">
          <ui-label label="关键词" :width="60" class="inline search-input">
            <Input
              class="width-lg"
              suffix="ios-search"
              v-model="searchData.keyWord"
              placeholder="请输入设备名称或设备编码"
              @on-enter.native="searchkeyword"
              >></Input
            >
            <div class="ml-lg">
              <Button type="primary" class="mr-sm" @click="searchkeyword"> 查询 </Button>
              <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, searchkeyword)"> 重置 </Button>
            </div>
          </ui-label>
        </div>
        <div class="data-list">
          <span>标签类型</span>
          <ui-select-tabs class="ui-select-tabs" :list="allTagList" @selectInfo="selectTabHandle" ref="uiSelectTabs" />
        </div>
        <div class="table-box auto-fill">
          <ui-table
            class="ui-table auto-fill"
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="loading"
            @selectTable="selectLeftTable"
            @oneSelected="oneSelected"
            @cancelSelectTable="cancelSelectTable"
            @onSelectAllTable="onSelectAllTable"
            @cacelAllSelectTable="cacelAllSelectTable"
          >
            <template slot="deviceId" slot-scope="{ row }">
              <span class="font-active-color pointer" @click="deviceArchives(row)">
                {{ row.deviceId }}
              </span>
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page menu-content-background"
          :page-data="pageData"
          :simple-page="true"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </section>
      <div class="arrow-box">
        <double-arrow></double-arrow>
      </div>
      <section class="right-content auto-fill">
        <div class="content-title">
          <p class="base-label-color">
            关联入库目录名称：
            <span class="font-green-color">
              {{ handleRelativeName() }}
            </span>
          </p>
          <div class="base-label-color">
            已关联设备（<span class="color-failed">{{ chooseTableData.length }}</span
            >）
            <Button type="primary" @click="removeAllDevice">全部移除</Button>
          </div>
        </div>
        <ui-table
          class="ui-table auto-fill"
          :table-columns="rightTableColumns"
          :table-data="chooseTableData"
          @selectTable="selectTable"
        >
          <template slot="deviceId" slot-scope="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">
              {{ row.deviceId }}
            </span>
          </template>
          <template #actionSlot="{ row, index }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-yichu1"
              content="移除"
              @click.native="removeOneDevice(row, index)"
            ></ui-btn-tip>
            <!-- <i
              class="icon-font f-14 icon-yichu-01 icon-operation-color mr-sm"
              @click="removeOneDevice(row, index)"
            ></i> -->
          </template>
        </ui-table>
      </section>
    </div>
    <template #footer>
      <Button
        class="plr-30"
        type="primary"
        @click="$emit('addDeviceToCategory', chooseTableData, allDeviceChecked, searchData.orgCode)"
      >
        确 定
      </Button>
      <Button class="plr-30" @click="visible = false">取 消</Button>
    </template>
  </ui-modal>
</template>
<style lang="less" scoped>
.model-wrapper {
  display: flex;
  background-color: var(--bg-content);
  .content-title {
    height: 60px;
    line-height: 60px;
    padding-left: 20px;
  }
  .left-content {
    width: 250px;
    border: 1px solid var(--border-modal-footer);
    border-right-color: transparent;
  }
  .middle-content,
  .right-content {
    min-width: 715px;
    flex: 1;
    border: 1px solid var(--border-modal-footer);
  }
  .middle-content {
    .search-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .content-title {
      height: auto;
      line-height: normal;
      padding-top: 20px;
    }
    .table-box {
      height: 550px;
    }
    .search-input {
      position: relative;
      display: flex;
      .icon-sousuo {
        position: absolute;
        right: 10px;
      }
    }
    .ui-table {
      width: 100%;
    }
  }
  .data-list {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 10px 10px 10px 0;
    span {
      color: var(--color-label);
      padding-left: 20px;
    }
    .ui-select-tabs {
      flex: 1;
      padding-left: 5px;
    }
  }
  .right-content {
    .content-title {
      display: flex;
      justify-content: space-between;
      padding: 0 20px;
    }
  }
  .padding20 {
    padding: 0 20px;
  }
  .font-green-color {
    color: var(--color-bluish-green-text);
  }
  .arrow-box {
    height: 600px;
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
  @{_deep}.ivu-input-suffix i {
    color: var(--color-primary) !important;
  }
}
</style>
<script>
import catalogmanagement from '@/config/api/catalogmanagement';
import taganalysis from '@/config/api/taganalysis';
import { mapGetters, mapActions } from 'vuex';
import global from '@/util/global';

const tableColumns = [
  { type: 'selection', width: 50, align: 'center' },
  { title: '序号', width: 40, type: 'index', align: 'center' },
  {
    title: `${global.filedEnum.deviceId}`,
    slot: 'deviceId',
    align: 'left',
    width: 170,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    minWidth: 150,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '组织机构',
    minWidth: 120,
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '安装地址',
    minWidth: 150,
    key: 'address',
    align: 'left',
    tooltip: true,
  },
];
const rightTableColumns = [
  { title: '序号', width: 50, type: 'index', align: 'center' },
  {
    title: `${global.filedEnum.deviceId}`,
    slot: 'deviceId',
    align: 'left',
    width: 170,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    minWidth: 150,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '组织机构',
    minWidth: 120,
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    title: '安装地址',
    minWidth: 150,
    key: 'address',
    align: 'left',
    tooltip: true,
  },
  {
    title: '操作',
    key: 'action',
    slot: 'actionSlot',
    align: 'center',
    width: '50',
  },
];
export default {
  data() {
    return {
      allTagList: [],
      keyword: '',
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      tableColumns: Object.freeze(tableColumns),
      rightTableColumns: Object.freeze(rightTableColumns),
      tableData: [],
      chooseTableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      visible: false,
      styles: {
        width: '9.5rem',
      },
      loading: false,
      searchData: {
        keyWord: '',
        orgCode: null,
        pageNumber: 1,
        pageSize: 20,
        checkStatuses: [],
      },
      connectingOptions: {
        width: '1.2%',
        height: '0.04rem',
        top: '50%',
        left: '13.6%',
      },
      allDeviceChecked: false,
      currentNodeKey: '',
    };
  },
  mounted() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
    }),
    // 获取所有设备标签
    async getTagList() {
      let res = await this.$http.post(taganalysis.getDeviceTag, {
        isPage: false,
      });
      this.allTagList = res.data.data.map((item) => {
        return { name: item.tagName, id: item.tagId };
      });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
    removeOneDevice(row, index) {
      this.chooseTableData.splice(index, 1);
      let deleteIndex = this.tableData.findIndex((item) => {
        return item.id === row.id;
      });
      this.tableData[deleteIndex]._checked = false;
    },
    removeAllDevice() {
      this.chooseTableData = [];
      this.tableData.forEach((item) => {
        item._checked = false;
      });
    },
    selectLeftTable() {
      //   this.chooseTableData = selection
      //   selection.forEach((row) => {
      //     let roww = this.tableData.find( item => {
      //       return item.id === row.id
      //     })
      //     roww._checked = true
      //   });
    },
    // 选择标签
    selectTabHandle(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.initAllData();
    },
    handleData() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        if (selectDataObject.hasOwnProperty(row.id)) {
          this.$set(row, '_checked', true);
        } else {
          this.$set(row, '_checked', false);
        }
      });
    },
    oneSelected(selection, row) {
      this.chooseTableData.push(row);
      row._checked = true;
    },
    cancelSelectTable(selection, row) {
      let rowId = this.tableData.find((item) => {
        return item.id === row.id;
      });
      this.chooseTableData = this.chooseTableData.filter((item) => item.id !== rowId.id);
    },
    onSelectAllTable() {
      let selectDataObject = {};
      this.chooseTableData.forEach((item) => {
        //this.$set(item, "_checked", true);
        selectDataObject[item.id] = item;
      });
      this.tableData.forEach((row) => {
        // 没有就push进去
        if (!selectDataObject.hasOwnProperty(row.id)) {
          this.chooseTableData.push(row);
        }
      });
    },
    cacelAllSelectTable() {
      let tableDataIds = this.tableData.map((item) => {
        return item.id;
      });
      this.chooseTableData = this.chooseTableData.filter((row) => {
        return !tableDataIds.includes(row.id);
      });
    },
    selectTable(selection) {
      this.checkedData = selection.map((row) => {
        return row.id;
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.pageNumber = val;
      this.initAllData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.searchData.pageSize = val;
      this.initAllData();
    },
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.initAllData();
    },
    handleRelativeName() {
      if (!this.choosedCategory) return;
      if (this.choosedCategory.hasOwnProperty('name')) {
        return this.choosedCategory.name;
      } else if (this.choosedCategory.hasOwnProperty('deviceTagName')) {
        return this.choosedCategory.deviceTagName;
      } else {
        return this.choosedCategory.areaTreeName;
      }
    },
    searchkeyword() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.initAllData();
    },
    // 查询设备数据列表
    async initAllData() {
      this.loading = true;
      this.tableData = [];
      try {
        let res = await this.$http.post(catalogmanagement.getPageDeviceList, this.searchData);
        this.tableData = res.data.data.entities;
        this.handleData();
        this.pageData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.error('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    async visible(val) {
      this.$emit('input', val);
      if (val) {
        await this.setOrganizationList();
        this.selectTree(this.getDefaultSelectedOrg);
        this.getTagList();
        this.currentNodeKey = this.getDefaultSelectedOrg.orgCode;
      } else {
        this.currentNodeKey = '';
        this.allDeviceChecked = false;
        this.searchData = {
          keyWord: '',
          orgCode: null,
          pageNumber: 1,
          pageSize: 20,
          checkStatuses: [],
        };
        this.pageData = {
          pageNum: 1,
          pageSize: 20,
          totalCount: 0,
        };
        this.chooseTableData = [];
      }
    },
    choosedCategory() {
      //console.log('我选中的目录需要关联设备', val)
    },
  },
  computed: {
    isView() {
      return this.modalAction.action === 'view';
    },
    isAdd() {
      return this.modalAction.action === 'add';
    },
    isEdit() {
      return this.modalAction.action === 'edit';
    },
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  props: {
    value: {
      required: true,
      type: Boolean,
    },
    choosedCategory: {
      default: () => {},
    },
    modalAction: {
      default: () => {
        return { title: '关联设备', action: 'add' };
      },
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DoubleArrow: require('@/components/double-arrow.vue').default,
  },
};
</script>
