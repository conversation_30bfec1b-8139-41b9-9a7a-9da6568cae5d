<template>
  <div class="auto-fill inspection-table-box">
    <ui-table class="auto-fill" :loading="loading" :table-columns="showTableColumns" :table-data="showTableData">
    </ui-table>
  </div>
</template>
<script>
export default {
  name: 'inspectionTable',
  props: {
    showTableHeader: {
      type: Array,
      default: () => [],
    },
    showTableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showTableColumns: [],
    };
  },
  watch: {
    showTableHeader: {
      handler(val) {
        this.showTableColumns = val.map((item) => {
          if (item.key === 'sequence') {
            return { ...item, width: 50 };
          } else if (item.params) {
            return { ...item, minWidth: 150, render: this.getClickRender() };
          } else {
            return { ...item, minWidth: 150, render: this.getNormalRender() };
          }
        });
        this.showTableColumns.unshift({
          title: ' ',
          key: '',
          width: '20px',
        });
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getClickRender() {
      let render = (h, { row, column }) => {
        return (
          <span
            class={row[column.key] !== '-' ? 'link' : ''}
            onClick={() => {
              this.clickColumn(row, column);
            }}
          >
            {row[column.key] || row[column.key] === 0 ? row[column.key] : '-'}
          </span>
        );
      };
      return render;
    },
    getNormalRender() {
      let render = (h, { row, column }) => {
        return <span>{row[column.key] || row[column.key] === 0 ? row[column.key] : '-'}</span>;
      };
      return render;
    },
    //点击列
    clickColumn(row, column) {
      if (row[column.key] === '-') {
        return;
      }
      this.$emit('clickColumn', row, column);
    },
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.inspection-table-box {
  @{_deep} .link {
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
