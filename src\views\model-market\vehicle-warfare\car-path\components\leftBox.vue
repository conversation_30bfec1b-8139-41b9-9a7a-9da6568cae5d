<!--
    * @FileDescription: 行车轨迹
    * @Author: H
    * @Date: 2023/09/18
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-10-09 18:01:19
 -->
<template>
  <div class="leftBox">
    <div class="search-box">
      <div class="title">
        <p>行车轨迹</p>
      </div>
      <div class="search_condition">
        <div class="search_form">
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut"><span>*</span>车牌号码:</p>
            </div>
            <div class="search_content">
              <Input
                v-model="queryParams.plateNo"
                placeholder="请输入精确车牌号码"
                class="wrapper-input"
              ></Input>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车牌颜色:</p>
            </div>
            <div class="search_content">
              <ui-tag-select
                ref="bodyColor"
                @input="
                  (e) => {
                    input(e, 'plateColor');
                  }
                "
              >
                <ui-tag-select-option
                  v-for="(item, $index) in licensePlateColorList"
                  :key="$index"
                  effect="dark"
                  :name="item.dataKey"
                >
                  <div
                    v-if="licensePlateColorArray[item.dataKey]"
                    :title="item.dataValue"
                    :style="{
                      borderColor:
                        licensePlateColorArray[item.dataKey].borderColor,
                    }"
                    class="plain-tag-color"
                  >
                    <div
                      :style="licensePlateColorArray[item.dataKey].style"
                    ></div>
                  </div>
                </ui-tag-select-option>
              </ui-tag-select>
            </div>
          </div>
          <!-- <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">车辆品牌:</p>
                        </div>
                        <div class="search_content">
                            <div class="select-tag-button" @click="selectBrandHandle">选择车辆品牌/已选（{{queryParams.plateBrands.length}}）</div>
                        </div>
                    </div> -->
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">车辆颜色:</p>
            </div>
            <div class="search_content">
              <Select
                v-model="queryParams.bodyColor"
                placeholder="请选择"
                clearable
              >
                <Option
                  v-for="(item, $index) in bodyColorList"
                  :key="$index"
                  :value="item.dataKey"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">开始时间:</p>
            </div>
            <div class="search_content">
              <DatePicker
                v-model="queryParams.startDate"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="开始时间"
                transfer
              ></DatePicker>
            </div>
          </div>
          <div class="search_wrapper">
            <div class="search_title">
              <p class="search_strut">结束时间:</p>
            </div>
            <div class="search_content">
              <DatePicker
                v-model="queryParams.endDate"
                type="datetime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="结束时间"
                transfer
              ></DatePicker>
            </div>
          </div>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
          </div>
        </div>
      </div>
    </div>
    <!-- 选择品牌 -->
    <BrandModal ref="brandModal" @on-change="selectBrand" />
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import BrandModal from "@/components/ui-brand-modal.vue";
export default {
  name: "",
  components: {
    BrandModal,
  },
  data() {
    return {
      queryParams: {
        plateNo: "", //皖C82550
        startDate: "",
        endDate: "",
        bodyColor: "",
        plateColor: "",
        // plateBrands: []
      },
      licensePlateColorArray, // 车牌颜色
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
    }),
  },
  async created() {
    this.queryParams.startDate = this.getAgoDay(7);
    this.queryParams.endDate = this.getAgoDay(0);
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    input(e, key) {
      this.queryParams[key] = e;
    },
    // 查询
    handleSearch() {
      // console.log(new Date(), new Date('2023-09-20 12:13:23'),'121')
      // console.log(this.$dayjs(this.queryParams.startDate).format('YYYY-MM-DD HH:mm:ss'));
      if (!this.queryParams.plateNo) {
        this.$Message.warning("请输入精确车牌查询！");
        return;
      }
      // 新能源
      let xny =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([A-HJ-K][A-HJ-NP-Z0-9][0-9]{4}$))/;

      // 燃油车
      let ryc =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;

      if (this.queryParams.plateNo.length == 7) {
        if (!ryc.test(this.queryParams.plateNo)) {
          this.$Message.warning("请输入精确车牌查询！");
          return;
        }
      } else if (this.queryParams.plateNo.length == 8) {
        if (!xny.test(this.queryParams.plateNo)) {
          this.$Message.warning("请输入精确车牌查询！");
          return;
        }
      } else {
        this.$Message.warning("请输入精确车牌查询！");
        return;
      }
      this.queryParams.startDate = this.$dayjs(
        this.queryParams.startDate
      ).format("YYYY-MM-DD HH:mm:ss");
      this.queryParams.endDate = this.$dayjs(this.queryParams.endDate).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.$emit("search", this.queryParams);
    },
    /**
     * 车辆品牌已选择，返回数据
     */
    selectBrand(list) {
      this.queryParams.plateBrands = list;
    },
    // 选择品牌
    selectBrandHandle() {
      this.$refs.brandModal.show();
    },
    // 时间判断
    getAgoDay(n) {
      let date = new Date();
      let newDate = new Date(date.getTime() - n * 24 * 60 * 60 * 1000);
      var Y = newDate.getFullYear() + "-";
      var M =
        (newDate.getMonth() + 1 < 10
          ? "0" + (newDate.getMonth() + 1)
          : newDate.getMonth() + 1) + "-";
      var D =
        newDate.getDate() < 10
          ? "0" + newDate.getDate() + " "
          : newDate.getDate() + " ";
      var h =
        newDate.getHours() < 10
          ? "0" + newDate.getHours() + ":"
          : newDate.getHours() + ":";
      var m =
        newDate.getMinutes() < 10
          ? "0" + newDate.getMinutes() + ":"
          : newDate.getMinutes() + ":";
      var s =
        newDate.getSeconds() < 10
          ? "0" + newDate.getSeconds()
          : newDate.getSeconds();
      return Y + M + D + h + m + s;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";

/deep/ .ivu-date-picker {
  width: 100%;
}
/deep/ .ivu-tag-select-option {
  margin-right: 5px !important;
}
</style>
