<!--
    * @FileDescription: 车辆详情
    * @Author: H
    * @Date: 2022/12/13
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-21 17:55:54
 -->
<template>
  <div class="dom-wrapper">
    <div class="dom-box">
      <header>
        <span>抓拍详情</span>
        <ui-icon
          type="close"
          :size="14"
          @click.native="() => $emit('close')"
        ></ui-icon>
      </header>
      <carousel
        ref="carousel"
        :same-position-point="samePositionPoint"
        @changeVid="changeVid"
      ></carousel>
      <section class="dom-content">
        <div class="dom-info">
          <div class="dom-info-left" style="width: 200px">
            <div class="smallImg" style="width: 200px; height: inherit">
              <img v-lazy="vehicleInfo.traitImg" alt="" />
            </div>
            <span
              class="license-plate-small similarity"
              v-if="vehicleInfo.score"
              >{{ vehicleInfo.score }}%</span
            >
            <div class="title">
              <span
                :class="checkStatus ? 'active' : ''"
                @click="tabsChange(true)"
                >通行记录</span
              >
              <span
                :class="!checkStatus ? 'active' : ''"
                @click="tabsChange(false)"
                >车辆档案</span
              >
            </div>
            <div class="traffic-record" v-if="checkStatus">
              <div class="dom-content-p">
                <span class="label">通过地点</span><span>：</span>
                <!-- TODO v-show-tips指令在这里不生效 -->
                <div
                  class="message"
                  :title="
                    vehicleInfo.deviceName || vehicleInfo.captureAddress || '--'
                  "
                >
                  {{
                    vehicleInfo.deviceName || vehicleInfo.captureAddress || "--"
                  }}
                </div>
              </div>
              <div class="dom-content-p">
                <span class="label">通过时间</span><span>：</span>
                <div class="message">
                  {{ vehicleInfo.absTime || vehicleInfo.captureTime || "--" }}
                </div>
              </div>
              <!-- <div class="dom-content-p">
                <span class="label">设备类型</span><span>：</span>
                <div class="message" v-show-tips>
                  <span
                    v-if="!Array.isArray(facilitySplit(vehicleInfo.sbgnlx))"
                  >
                    {{
                      vehicleInfo.sbgnlx
                        | commonFiltering(translate("sbgnlxList"))
                    }}
                  </span>
                  <span
                    v-else
                    v-for="(ite, inde) in facilitySplit(vehicleInfo.sbgnlx)"
                    :key="inde"
                  >
                    {{ ite | commonFiltering(translate("sbgnlxList"))
                    }}{{
                      inde + 1 < facilitySplit(vehicleInfo.sbgnlx).length
                        ? "/"
                        : ""
                    }}
                  </span>
                </div>
              </div> -->
              <structuredmsg :info="vehicleInfo" type="2"></structuredmsg>
            </div>
            <div class="personnel-files" v-else>
              <div class="dom-content-p">
                <span class="label">车牌号码</span><span>：</span>
                <span
                  class="address"
                  :class="{ plateNo: vehicleInfo.plateNo }"
                  @click="toDetail(vehicleInfo)"
                  >{{ vehicleInfo.plateNo || "--" }}</span
                >
              </div>
              <div class="dom-content-p">
                <span class="label">车辆类型</span><span>：</span>
                <span class="message">
                  {{
                    vehicleArchives.vehicleType
                      | commonFiltering(vehicleTypeList)
                  }}
                </span>
              </div>
              <div class="dom-content-p">
                <span class="label">车辆品牌</span><span>：</span>
                <span class="message"
                  >{{
                    vehicleArchives.vehicleBrandCN
                      | commonFiltering(vehicleBrandList)
                  }}
                </span>
              </div>
              <div class="dom-content-p">
                <span class="label">机动车主</span><span>：</span>
                <span class="message">{{
                  vehicleArchives.motorists || "--"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">身份证号</span><span>：</span>
                <span class="address">{{
                  vehicleArchives.idcardNo || "--"
                }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">联系电话</span><span>：</span>
                <span class="message">{{ vehicleArchives.phone || "--" }}</span>
              </div>
              <div class="dom-content-p">
                <span class="label">登记地址</span><span>：</span>
                <span class="address">{{ vehicleArchives.djdz || "--" }}</span>
              </div>
            </div>
          </div>
          <div class="dom-info-right">
            <ui-image
              :src="vehicleInfo.sceneImg | imgProxyToHttps"
              alt="静态库"
              viewer
            />
          </div>
        </div>
      </section>
    </div>
    <footer>
      <!-- <search-around v-if="cutIcon" @preDetial="preDetial" @nextDetail="nextDetail"></search-around> -->
    </footer>
  </div>
</template>

<script>
import { getCarInfoByCarNo } from "@/api/operationsOnTheMap";
import { mapActions, mapGetters, mapMutations } from "vuex";
import carousel from "./carousel.vue";
import structuredmsg from "@/components/mapdom/structuredmsg.vue";
// import { commonMixins } from "@/mixins/app.js";
export default {
  name: "",
  // mixins: [commonMixins], //全局的mixin
  components: {
    carousel,
    structuredmsg,
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      vehicleInfo: {},
      vehicleArchives: {},
      checkStatus: true, // 抓拍记录，人员档案tab切换
      samePositionPoint: [],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      getPeerList: "map/getPeerList",
      getPeerData: "map/getPeerData",
    }),
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * pointItem: 本条数据
     * item：地图撒点数据集合（对象信息，检索结果）
     * type：是否点击列表
     */
    init(pointItem, item = [], type = "", menuType = "") {
      return new Promise((resolve) => {
        this.headtablist(pointItem, item, type, menuType);
        if (menuType == "track") {
          //轨迹分析
          this.vehicleInfo = { ...pointItem };
        } else {
          //同行分析
          this.vehicleInfo =
            type == "peer"
              ? { ...pointItem[this.getPeerData.type] }
              : { ...pointItem };
        }
        resolve({ data: true });
      });
    },
    // 获取头部列表
    /**
     * item: 本条数据
     * tabItem： 直接获取的列表
     * type: 同行分析中判断 人脸和车
     * menuType: 判断目录菜单（轨迹分析，同行分析）
     */
    headtablist(item, tabItem, type, menuType) {
      if (menuType == "track") {
        let list = Array.isArray(this.getPeerList)
          ? this.getPeerList
          : this.getPeerList["carTrajectory"];
        this.samePositionPoint = list.filter((ite, index) => {
          if (ite.deviceId === item.deviceId) {
            ite.Index = index + 1;
          }
          return ite.deviceId === item.deviceId;
        });
      } else {
        if (type == "peer" || type == "peerMap") {
          //同行次数
          let allList = this.getPeerList.filter((ite, index) => {
            if (ite.deviceId === item.deviceId) {
              ite.Index = index + 1;
            }
            return ite.deviceId === item.deviceId;
          });
          let tabIndex = 0;
          this.samePositionPoint = [];
          allList.map((ite, index) => {
            // 判断是直接点击地图，列表
            if (type == "peerMap") {
              //直接点击地图
              if (item.recordId == ite.vehiclePeerDetailVo.recordId) {
                tabIndex = index;
              }
              this.$set(this.samePositionPoint, index, {
                ...ite.vehiclePeerDetailVo,
                num: ite.num,
              });
            } else {
              //点击列表
              if (
                item[this.getPeerData.type].recordId ==
                ite[this.getPeerData.type].recordId
              ) {
                tabIndex = index;
              }
              this.$set(this.samePositionPoint, index, {
                ...ite[this.getPeerData.type],
                num: ite.num,
              });
            }
          });
          this.$refs.carousel.init(tabIndex);
        } else if (type == "frequency") {
          this.samePositionPoint = this.positionPoints;
          let tabIndex = 0;
          this.positionPoints.forEach((subItem, subIndex) => {
            if (subItem.recordId == item.recordId) {
              tabIndex = subIndex;
            }
            this.$set(this.samePositionPoint, subIndex, {
              ...subItem,
            });
          });
          this.$refs.carousel.init(tabIndex);
        } else {
          //对象信息、检索信息
          const { lon, lat } = item.geoPoint || item || {};
          let list = this.positionPoints.filter((ite) => {
            const { lon: lon1, lat: lat1 } = ite.geoPoint || {};
            return lat1 == lat && lon1 == lon;
          });
          // this.samePositionPoint = tabItem;
          this.samePositionPoint = list[0].faceCaptureList;
        }
      }
    },
    // 通行记录/车辆档案 切换
    async tabsChange(state) {
      if (this.checkStatus == state) {
        return;
      }
      this.checkStatus = state;
      if (!this.checkStatus) {
        if (!this.vehicleInfo.plateNo) {
          this.$Message.warning("档案不存在！");
          return;
        }
        try {
          let res = await getCarInfoByCarNo({
            // 这里需要携带车牌颜色
            carNo: this.vehicleInfo.plateNo + "_" + this.vehicleInfo.plateColor,
          });
          if (res.code === 200 && !!res.data) {
            this.vehicleArchives = res.data;
          } else {
            throw error;
          }
        } catch (error) {
          this.$Message.warning("档案暂无数据");
        }
      }
    },
    toDetail(item) {
      if (!item.plateNo) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "vehicle-archive",
        query: {
          archiveNo: JSON.stringify(this.vehicleArchives.archiveNo),
          plateNo: JSON.stringify(this.vehicleArchives.plateNo),
          source: "car",
          idcardNo: this.vehicleArchives.idcardNo,
        },
      });
      window.open(href, "_blank");
    },
    // 顶部切换
    changeVid(item) {
      this.$emit("changeListTab", { ...item }, "vehicle");
      this.vehicleInfo = { ...item };
      this.checkStatus = true;
    },
  },
};
</script>

<style lang="less" scoped>
@import "./index";
</style>
