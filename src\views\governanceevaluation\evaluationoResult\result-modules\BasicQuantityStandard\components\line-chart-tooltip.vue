<template>
  <div class="tooltip-container">
    <div v-for="(item, index) in data" :key="index">
      <span>{{ formatDateTime(item.data.startTime) }}</span>
      <div>
        <span> {{ item.data.indexName }}： </span>
        <span> {{ item.data.value || 0 }}% </span>
      </div>
      <div>
        <div>
          <span>应上报采集区域类型数量：</span>
          <span>
            {{ item.data.actualNum || 0 }}
          </span>
        </div>
        <div>
          <span>采集区域类型达标数量：</span>
          <span>
            {{ item.data.qualifiedNum || 0 }}
          </span>
        </div>
        <div>
          <span>采集区域类型不达标数量：</span>
          <span>
            {{ item.data.unqualifiedNum || 0 }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'line-chart-tooltip.vue',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {
    formatDateTime(dateTime) {
      if (!dateTime) {
        return '';
      }
      let dateObject = new Date(dateTime);
      let year = dateObject.getFullYear();
      let month = dateObject.getMonth() + 1 < 10 ? '0' + (dateObject.getMonth() + 1) : dateObject.getMonth() + 1;
      let date = dateObject.getDate() < 10 ? `0${dateObject.getDate()}` : dateObject.getDate();
      let hours = dateObject.getHours() < 10 ? `0${dateObject.getHours()}` : dateObject.getHours();
      let minutes = dateObject.getMinutes() < 10 ? `0${dateObject.getMinutes()}` : dateObject.getMinutes();
      let seconds = dateObject.getSeconds() < 10 ? `0${dateObject.getSeconds()}` : dateObject.getSeconds();
      return `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds}`;
    },
    getFetchType(fetchType) {
      if (fetchType === 'NEW') {
        return '最新值';
      } else if (fetchType === 'AVG') {
        return '平均值';
      }
      return '';
    },
  },
};
</script>

<style lang="less" scoped>
.tooltip-container {
  background: rgba(13, 53, 96, 0);
  border: none;
  opacity: 1;
  .block {
    display: inline-block;
    height: 6px;
    width: 6px;
    line-height: 14px;
  }
}
</style>
