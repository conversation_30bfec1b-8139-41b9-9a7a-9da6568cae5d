<template>
  <div :class="isResult ? 'governancetoolset-child-result' : 'governancetoolset-child'">
    <div class="child-head">
      <div class="head">
        <i class="icon-font icon-shujujiancegongju"></i><span class="title">国标编码检测管理</span>
      </div>
      <Button type="text" class="btn-back mr-sm" @click="backHandle">&lt;&lt; 返回</Button>
    </div>
    <div v-if="!isResult" class="governancetoolset-child-container">
      <Form
        ref="formData"
        :model="formData"
        :rules="ruleValidate"
        :label-width="210"
        label-position="left"
        class="set-form"
      >
        <FormItem prop="type" label="1、选择设备：">
          <RadioGroup v-model="uploadWay">
            <Radio label="1">离线文件</Radio>
            <Radio label="2">系统选择</Radio>
          </RadioGroup>
          <div class="btns" v-if="uploadWay === '1'">
            <FormItem
              prop="internationalCodeVoList"
              :rules="{
                required: true,
                type: 'array',
                message: '请上传文件',
                trigger: 'change',
              }"
            >
              <Upload
                action="/ivdg-device-data-service/governance/importExcel"
                :headers="headers"
                :data="uploadData"
                :show-upload-list="false"
                :beforeUpload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
                ref="upload"
              >
                <Button
                  custom-icon="icon-font icon-shangchuananmobantianxiedeexcelwenjian-01"
                  :loading="uploadLoading"
                  class="upload-btn"
                >
                  {{ fileName ? fileName : '上传按模板填写的excel文件' }}
                </Button>
              </Upload>
            </FormItem>
            <Button type="text" :loading="downloadLoading" @click="downloadHandle">下载模板</Button>
          </div>
          <FormItem
            prop="internationalCodeVoList"
            v-if="uploadWay === '2'"
            :rules="{
              required: true,
              type: 'array',
              message: '请选择设备',
              trigger: 'change',
            }"
          >
            <div class="select-device" @click="selectDeviceBtn">
              <i class="icon-font icon-shitujichushuju"></i
              ><span class="select-device-text">{{
                formData.internationalCodeVoList.length > 0
                  ? '已选择' + formData.internationalCodeVoList.length + '个设备'
                  : '请选择设备'
              }}</span>
            </div>
          </FormItem>
        </FormItem>
        <FormItem prop="deviceIdLengthString" label="2、设备编码允许长度：" class="label-width mb-10">
          <input-number-view v-model="deviceIdLengthString" :inputMaxNumber="10" />
        </FormItem>
        <FormItem prop="specialString" label="3、设备编码11-13位允许数字：" class="label-width mb-10">
          <input-number-view v-model="specialString" :inputMaxNumber="20" />
        </FormItem>
      </Form>
      <Button type="primary" class="submit-btn" @click="submitHandle('formData')">确定</Button>
    </div>
    <!-- 检测结果 -->
    <detection-result ref="detectionResult" v-else :data-obj="formData" />
    <!-- 选择设备 -->
    <select-device-modal
      ref="selectDeviceModle"
      v-if="selectModeVisible"
      :toolType="formData.type"
      :selectedDeviceList="selectedDeviceList"
      @setDeviceHandle="setDeviceHandle"
    />
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';

export default {
  name: 'GBCodeDetectionManagement',
  components: {
    'detection-result': require('./detection-result.vue').default,
    'select-device-modal': require('../components/select-device-modal').default,
    'input-number-view': require('./components/input-number-view.vue').default,
  },
  data() {
    return {
      formData: {
        type: '2',
        internationalCodeVoList: [],
        param: {
          specialStringList: [],
          deviceIdLength: [],
        },
      },
      deviceIdLengthString: '20,18',
      specialString: '131,132',
      selectModeVisible: false,
      selectedDeviceList: [],
      isResult: false,
      uploadWay: '1',
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      downloadLoading: false,
      uploadLoading: false,
      uploadData: {
        type: '2',
      },
      fileName: '',
      ruleValidate: {
        dd: [
          {
            required: true,
            type: 'string',
            message: '请输入允许时间误差',
            trigger: 'change',
          },
        ],
      },
    };
  },
  watch: {
    uploadWay() {
      this.formData.internationalCodeVoList = [];
      this.selectedDeviceList = [];
    },
  },
  methods: {
    // 配置确定
    submitHandle(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          try {
            const specialStringList = this.specialString.split(',').filter((item) => item !== '');
            const deviceIdLength = this.deviceIdLengthString
              .split(',')
              .filter((item) => item !== '')
              .map((d) => parseFloat(d));
            this.formData.param.specialStringList = specialStringList;
            this.formData.param.deviceIdLength = deviceIdLength;
            this.isResult = true;
            this.$nextTick(() => {
              this.$refs.detectionResult.init();
            });
          } catch (err) {
            console.error(err);
          }
        }
      });
    },
    // 返回
    backHandle() {
      if (this.isResult) {
        this.isResult = false;
      } else {
        this.$router.push({ name: 'governancetoolset' });
      }
    },
    // 选择设备
    selectDeviceHandle() {
      this.selectModeVisible = true;
    },
    // 下载模板
    async downloadHandle() {
      try {
        this.downloadLoading = true;
        let params = {
          type: '2',
        };
        let res = await this.$http.post(governancetoolset.downloadTemplate, params, { responseType: 'blob' });
        this.$util.common.exportfile(res, '国标编码检测设备模板');
        this.downloadLoading = false;
      } catch (err) {
        this.downloadLoading = false;
        console.log(err);
      }
    },
    // 上传文件之前
    beforeUpload(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        return false;
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!');
        return false;
      }
      this.uploadLoading = true;
    },
    // 上传成功
    importSuccess(file, response) {
      this.uploadLoading = false;
      if (!file || file.length === 0) {
        this.$Message.warning('文件数据有误，请检查后重新上传');
        return false;
      }
      this.fileName = response.name;
      this.formData.internationalCodeVoList = file.map((item) => {
        let { id, civilCode, deviceId } = item;
        item.id = parseFloat(id).toString();
        item.civilCode = parseFloat(civilCode).toString();
        item.deviceId = parseFloat(deviceId).toString();
        return item;
      });
    },
    // 上传失败
    importError() {
      this.uploadLoading = false;
      this.$Message.warning('上传失败，请重新上传。');
    },
    // 选择设备
    selectDeviceBtn() {
      this.selectModeVisible = true;
      this.$nextTick(() => {
        this.$refs.selectDeviceModle.init();
      });
    },
    // 设置设备
    setDeviceHandle(devices) {
      this.selectedDeviceList = devices;
      this.formData.internationalCodeVoList = devices;
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: var(--bg-content) url('~@/assets/img/datagovernance/governance_tool_set_child_bg.png') no-repeat
      center/cover;
    .child-head .head {
      color: #19d5f6;
    }
    .close-btn {
      background: #ffffff;
      &:after {
        background: #02162b;
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: #f1f3f6 url('~@/assets/img/datagovernance/governance_tool_set_child_bg_light.png') no-repeat
      center/cover;
    .child-head .head {
      color: var(--color-primary);
    }
    .close-btn {
      background: #ffffff;
      border: 1px solid var(--color-primary);
      &:after {
        background: var(--color-primary);
      }
    }
  }
}

.governancetoolset-child,
.governancetoolset-child-result {
  display: flex;
  flex: 1;
  padding: 0 20px;
  box-sizing: border-box;
  flex-direction: column;
  height: 100%;
  position: relative;
  .child-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    height: 50px;
    .head {
      display: flex;
      align-items: center;
      .icon-font {
        font-size: 24px;
        margin-right: 5px;
      }
      .title {
        font-size: 16px;
        line-height: 20px;
        font-weight: bold;
      }
    }
  }
  .governancetoolset-child-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .set-form {
      .btns {
        display: flex;
        align-items: center;
      }
      .upload-btn {
        margin-right: 16px;
        /deep/ .icon-font {
          font-size: 20px;
          line-height: 20px;
        }
      }
      .select-device {
        width: 270px;
        height: 34px;
        border: 1px dashed var(--color-active);
        background: rgba(43, 132, 226, 0.1);
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--color-active);
        .icon-font {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .select-device:hover {
        background: rgba(43, 132, 226, 0.2);
        border: 1px dashed #3c90e9;
      }
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          padding-right: 10px;
        }
        .ivu-form-item-content {
          display: flex;
        }
        .ivu-radio-group-item {
          margin-right: 30px;
          .ivu-radio {
            margin-right: 10px;
          }
        }
      }
      /deep/ .label-width {
        &.mb-10 {
          margin-bottom: 10px;
        }
        .ivu-form-item {
          display: flex;
          .ivu-form-item-label {
            width: auto !important;
            padding-right: 0;
          }
          .ivu-input-number {
            width: 85px;
            margin: 0 10px;
          }
          .second-text {
            color: var(--color-content);
          }
        }
      }
      /deep/ .label-width > .ivu-form-item-content {
        flex-direction: column;
      }
      .form-item {
        display: flex;
        margin-bottom: 20px;
        align-items: center;
        .ivu-form-item {
          position: relative;
          margin-right: 10px;
          &:hover {
            .close-btn {
              display: block;
            }
          }
          .close-btn {
            position: absolute;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            top: -7px;
            right: -7px;
            text-align: center;
            display: none;
            cursor: pointer;
            &:after {
              content: '';
              position: absolute;
              width: 8px;
              height: 1px;
              top: 7px;
              left: 3px;
            }
          }
        }
        .ivu-input-number {
          margin: 0 !important;
        }
      }
      /deep/ .ivu-checkbox-wrapper {
        margin-right: 10px;
      }
    }
    .submit-btn {
      margin-top: 10px;
      width: 200px;
    }
  }
}
.add-item-btn {
  width: 15px;
  height: 15px;
  border: 1px solid var(--color-active);
  line-height: 1;
}
.btn-back {
  color: var(--color-active) !important;
}
.governancetoolset-child-result {
  background: var(--bg-content);
}
</style>
