<template>
  <ui-modal class="subtitle-reset" v-model="visible" title="OSD字幕重设" width="64%">
    <div>
      <Form :label-width="88">
        <FormItem :label="global.filedEnum.deviceId" class="mb15">
          <Input type="text" v-model="formData.deviceId" disabled class="form-item-width" />
        </FormItem>
        <FormItem :label="`${global.filedEnum.ipAddr}`">
          <Input v-model="formData.deviceIp" disabled type="text" class="form-item-width" :rows="7" />
        </FormItem>
        <FormItem label="字幕设置" class="subtitle-lable" prop="name">
          <div class="subtitle-reset-content">
            <Row>
              <Col :span="10">
                <img
                  src="@/assets/img/datagovernance/subtitle-reset.png"
                  alt="示例图"
                  style="max-width: 100%; max-height: 100%"
                />
              </Col>
              <Col :span="14">
                <FormItem label="省" class="mb15">
                  <Input type="text" v-model="formData.province" class="form-item-width" placeholder="请输入" />
                </FormItem>
                <FormItem label="市">
                  <Input v-model="formData.city" type="text" class="form-item-width" placeholder="请输入" />
                </FormItem>
                <FormItem label="区县" class="mb15">
                  <Input type="text" v-model="formData.county" class="form-item-width" placeholder="请输入" />
                </FormItem>
                <FormItem label="所队">
                  <Input v-model="formData.team" type="text" class="form-item-width" placeholder="请输入" />
                </FormItem>
                <FormItem label="地址信息" class="mb15">
                  <Input type="text" v-model="formData.device" class="form-item-width" placeholder="请输入" />
                </FormItem>
                <FormItem label="摄像机信息">
                  <Input v-model="formData.camera" type="text" class="form-item-width" placeholder="请输入" />
                </FormItem>
              </Col>
            </Row>
          </div>
        </FormItem>
      </Form>
    </div>
    <template #footer>
      <Button type="primary" @click="query">重设OSD字幕</Button>
    </template>
  </ui-modal>
</template>
<script>
import governanceautomatic from '@/config/api/governanceautomatic';
export default {
  props: {},
  data() {
    return {
      visible: false,
      formData: {},
    };
  },
  created() {},
  methods: {
    init(row) {
      this.visible = true;
      this.formData = {};
      this.$http.get(governanceautomatic.getDeviceView + '?id=' + row.id).then((res) => {
        this.formData = res.data.data;
        this.formData.camera = res.data.data.nameOsd.text || '';
        if (this.formData.textOsd) {
          this.formData.textOsd.map((val) => {
            this.formData[val.name] = val.text;
          });
        }
      });
    },
    // 重设字幕，后端格式要求[{"enable": true, "text": "string",name:'string'}]，同自动治理
    query() {
      let textOsd = [
        { enable: true, text: this.formData.province, name: 'province' }, // 省
        { enable: true, text: this.formData.city, name: 'city' }, // 市
        { enable: true, text: this.formData.county, name: 'county' }, // 区县
        { enable: true, text: this.formData.team, name: 'team' }, // 所队
        { enable: true, text: this.formData.device, name: 'device' }, // 地址信息
      ];
      let nameOsd = { enable: true, text: this.formData.camera, name: 'camera' }; // 摄像机信息

      this.$http
        .post(governanceautomatic.deviceResetSub, {
          textOsd: textOsd,
          nameOsd: nameOsd,
          deviceId: this.formData.deviceId,
        })
        .then(() => {
          this.$Message.success('操作成功');
          this.visible = false;
        });
      //
    },
    cancel() {
      this.visible = false;
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-form-item {
  margin-bottom: 20px;
}
.subtitle-reset {
  .form-item-width {
    width: 380px;
  }
  .subtitle-lable {
    margin-top: 26px;
    .ivu-form-item-label {
      padding-top: 0px;
    }
  }
  .subtitle-reset-content {
    width: 98%;
    height: 338px;
    background: var(--bg-sub-content);
    border-radius: 4px;
    padding: 20px;
    img {
      width: 100%;
      height: 298px;
    }
    .form-item-width {
      width: 300px;
    }
    .ivu-form-item {
      margin-left: 32px;
    }
  }
  .mb15 {
    margin-bottom: 15px;
  }
}
</style>
