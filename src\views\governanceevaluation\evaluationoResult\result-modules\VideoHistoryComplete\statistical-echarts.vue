<template>
  <div class="statistical-echarts">
    <div class="statistical-echarts-bar">
      <statistical-bar :statics-list="statisticsList" :qualified-val="qualifiedVal" :isEqual="false"></statistical-bar>
    </div>
    <div class="subordinate-wrapper">
      <div class="city mr-sm">
        <subordinate-chart :active-index-item="activeIndexItem">
          <template #rank-title>
            <span>按完整率排名</span>
          </template>
        </subordinate-chart>
      </div>
      <div class="rank">
        <result-rank></result-rank>
      </div>
    </div>
    <div class="history-wrapper mt-sm">
      <line-chart :activeIndexItem="activeIndexItem" class="line-chart"></line-chart>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import AbnormalListConfig from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/AbnormalListConfig';
export default {
  name: 'statistical-echarts',
  mixins: [dealWatch],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticsList: [],
      dataType: '',
      citySortField: null,
      areaSortField: null,
      cityChartsData: {
        xData: [],
        qualifiedData: [],
        unqualifiedData: [],
      },
      rankList: [],
      rankInfoList: [],
      qualifiedVal: '1',
      dateType: 'DAY',
      tagList: ['日', '月'],
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      lineData: {},
      numRankInfoLoadig: false,
      barGraphRankInfoLoading: false,
      trendLoading: false,
      paramsList: {},
      codeKey: '',
    };
  },
  created() {
    let informationStatistics = AbnormalListConfig.find((item) => item.indexId === this.activeIndexItem.indexId) || {};
    this.statisticsList = informationStatistics.abnormalList || [];
  },
  mounted() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true, deep: true },
    );
  },
  methods: {
    async init() {
      this.getDate();
      await this.getStatInfo();
      await this.getNumRankInfo();
    },
    async getStatInfo() {
      try {
        const params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getStatInfo, params);
        this.dataType = data.dataType;
        this.statisticsList = this.statisticsList.map((item) => {
          item[item.key] = data[item.key];
          return item;
        });
        this.qualifiedVal = data.qualified;
      } catch (e) {
        console.log(e);
      }
    },
    // 地市上报情况
    async getNumRankInfo() {
      this.numRankInfoLoadig = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.statisticType,
        orgRegionCode: this.paramsList[this.codeKey],
        sortField: this.citySortField ? 'ACTUAL_NUM' : null,
      };
      this.cityChartsData = {
        xData: [],
        qualifiedData: [],
        unqualifiedData: [],
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getNumRankInfo, params);
        if (!data || !data.length) return false;
        this.cityChartsData.xData = data.map((item) => {
          if (item.qualified === '1') {
            this.cityChartsData.qualifiedData.push(item.actualNum);
            this.cityChartsData.unqualifiedData.push(0);
          } else {
            this.cityChartsData.unqualifiedData.push(item.actualNum);
            this.cityChartsData.qualifiedData.push(0);
          }
          return item.regionName;
        });
      } catch (err) {
        console.log(err);
      } finally {
        this.numRankInfoLoadig = false;
      }
    },
    // 区县上报情况
    async getBarGraphRankInfo() {
      this.barGraphRankInfoLoading = true;
      let params = {
        access: this.paramsList.access || 'REPORT_MODE',
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.statisticType,
        orgRegionCode: this.paramsList[this.codeKey],
        sortField: this.areaSortField ? 'AREA_RESULT_VALUE' : null,
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.getBarGraphRankInfo, params);
        this.rankInfoList = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.barGraphRankInfoLoading = false;
      }
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 变化趋势
    async getDayCaptureStatistics() {
      try {
        this.trendLoading = true;
        let params = {
          taskSchemeId: this.paramsList.taskSchemeId,
          indexId: this.paramsList.indexId,
          indexType: this.paramsList.indexType,
          dateType: this.dateType,
          year: this.year,
          month: this.monthIndex,
          displayType: this.paramsList.statisticType,
          orgRegionCode: this.paramsList[this.codeKey],
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getHistoryTrend, params);
        this.lineData = data || {};
      } catch (err) {
        console.log(err);
      } finally {
        this.trendLoading = false;
      }
    },
    changeCitySortField(val) {
      this.citySortField = val;
      this.getNumRankInfo();
    },
    changeAreaSortField(val) {
      this.areaSortField = val;
      this.getBarGraphRankInfo();
    },
    /**
     * DAY/MONTH
     * @param index
     * @param item
     */
    changeStatus(index, item) {
      if (item === '日') {
        this.dateType = 'DAY';
        this.getDate();
      } else if (item === '月') {
        this.month = null;
        this.monthIndex = 0;
        this.dateType = 'MONTH';
      }
      this.getDayCaptureStatistics();
    },
    statusChange(type) {
      if (type === 'DAY') {
        this.getDate();
      }
      this.getDayCaptureStatistics();
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear(val) {
      const yearArr = val.split('年');
      this.year = yearArr[0];
      this.getDayCaptureStatistics();
    },
    getParams() {
      this.paramsList = this.$route.query;
      if (!this.paramsList || !this.paramsList.indexId || !this.paramsList.batchId) return false;
      this.codeKey = this.paramsList.statisticType === 'REGION' ? 'regionCode' : 'orgCode';
      this.init();
    },
  },
  components: {
    StatisticalBar: require('./components/statistical-bar').default,
    SubordinateChart: require('@/views/governanceevaluation/evaluationoResult/components/subordinate-chart.vue')
      .default,
    ResultRank: require('@/views/governanceevaluation/evaluationoResult/components/result-rank.vue').default,
    LineChart: require('@/views/governanceevaluation/evaluationoResult/components/line-chart.vue').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-echarts {
  padding: 10px;
  overflow-y: auto;
  &-bar {
    width: 100%;
  }
  .subordinate-wrapper {
    height: 260px;
    display: flex;
    position: relative;
    .city {
      height: 100%;
      width: calc(100% - 349px);
    }
    .rank {
      height: 100%;
      width: 349px;
    }
  }
  .history-wrapper {
    width: 100%;
    height: 260px;
    .line-chart {
      height: 100%;
      width: 100%;
    }
  }
  @{_deep} .info-statics-list ul {
    justify-content: space-between;
    .custormWidth {
      height: 102px;
    }
    li:nth-child(1),
    li:nth-child(2),
    li:nth-child(3) {
      width: calc(calc(100% - 20px) / 3) !important;
    }
    li:nth-child(4),
    li:nth-child(5),
    li:nth-child(6),
    li:nth-child(7) {
      width: calc(calc(100% - 30px) / 4) !important;
    }
  }
}
</style>
