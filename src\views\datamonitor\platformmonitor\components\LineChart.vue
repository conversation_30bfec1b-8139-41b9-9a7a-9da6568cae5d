<template>
  <div class="auto-fill echarts-box" v-ui-loading="{ loading: echartsLoading }">
    <draw-echarts
      ref="drawEchartsRef"
      class="charts"
      :echart-option="echartOption"
      :echart-style="echartStyle"
    ></draw-echarts>
  </div>
</template>
<script>
import LineChartsTooltip from './LineChartsTooltip.vue';
import Vue from 'vue';
export default {
  name: 'MonitorL<PERSON>C<PERSON>',
  props: {
    cuptureMonthOrDay: {
      type: String,
      default: 'month',
    },
    year: {
      type: String,
      default: '2023-12-01',
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
    echartStyle: {
      type: Object,
      default: () => {
        return {
          width: '100%',
          height: '325px',
        };
      },
    },
    //数据
    tendencyEchartData: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      echartOption: {},
      tooltipFormatter: (param) => {
        let _this = this;
        let LineChartsTooltipVue = Vue.extend(LineChartsTooltip);
        let _that = new LineChartsTooltipVue({
          el: document.createElement('div'),
          data() {
            return {
              data: param,
              year: _this.year, // 格式：'2023-06'
              cuptureMonthOrDay: _this.cuptureMonthOrDay, //  month: 月趋势  date: 日趋势
            };
          },
        });
        return _that.$el.outerHTML;
      },
    };
  },
  watch: {
    tendencyEchartData: {
      handler() {
        this.setEchartOption();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 处理 趋势图的 options
    setEchartOption() {
      let seriesData = [];
      if (this.tendencyEchartData?.length) {
        seriesData = this.tendencyEchartData.map((item) => {
          return { ...item, value: item.sum };
        });
        seriesData.sort((a, b) => a.time - b.time);
      }
      let opts = {
        seriesData,
        tooltipFormatter: this.tooltipFormatter,
        filterTime: this.year,
        cuptureMonthOrDay: this.cuptureMonthOrDay,
      };

      this.echartOption = this.$util.doEcharts.dataMonitorMonthCapLine(opts);
    },
    resizeEchart() {
      this.$refs.drawEchartsRef?.resize();
    },
  },
  mounted() {},
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-box {
  background: var(--bg-info-card);
  height: 100% !important;
  width: 100%;
  padding: 15px;
}
</style>
