<template>
  <div class="fieldmapping">
    <ui-modal v-model="visible" title="字段映射" width="81.25rem" :footerHide="true" @onCancel="reset">
      <div class="fieldmapping-wrap">
        <modal-left @newMapping="newMapping">
          <ul>
            <li :class="active === index ? 'active' : ''" v-for="(item, index) in mappedData" :key="index">
              <div class="table-name">{{ item.kafkaTopicName }}</div>
              <div class="edit-icon" @click="edit(item, index)">
                <i class="icon-font icon-bianji"></i>
              </div>
            </li>
          </ul>
        </modal-left>
        <transfer-table
          v-if="isHandle"
          title1="字段拾取"
          title2="字段映射"
          :hasHeader="true"
          :columns1="columns1"
          :columns2="columns2"
          :tableData1="kafkaPropertyList"
          :tableData2="transferFieldList"
          :accessData="accessData"
          :accessId="accessId"
          :leftLoading="leftLoading"
          :rightLoading="rightLoading"
          style="flex: 1"
          @handleChange="handleChange"
          @selectionChange="selectionChange"
          @handleSave="handleSave"
        ></transfer-table>
        <div v-if="!isHandle" class="empty-wrap">
          <div class="no-data">
            <i class="no-data-img icon-font icon-zanwushuju1"></i>
            <div class="null-data-text">暂无数据</div>
          </div>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import ModalLeft from '../../components/modal-left.vue';
import TransferTable from '../../components/transfer-table.vue';
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      title: '字段映射',
      active: '',
      columns1: [
        { type: 'selection', width: 50, align: 'center' },
        { title: '字段名', key: 'propertyName' },
        { title: '注释', key: 'propertyColumn' },
        // { title: "预览数据", key: "order" },
      ],
      columns2: [
        { title: '原始字段', key: 'sourcePropertyName' },
        {
          title: '标准字段',
          key: 'address',
          render: (h, params) => {
            return h(
              'i-select',
              {
                style: {
                  // width: "216px",
                },
                props: {
                  value: params.row.targetPropertyName + ',' + params.row.targetPropertyColumn,
                  // placeholder: "DeviceID（设备编码）",
                  transfer: true,
                  'transfer-class-name': 'transfer-table-select',
                  filterable: true,
                },
                on: {
                  'on-change': (val) => {
                    if (!val) return false;
                    const arr = val.split(',');
                    this.transferFieldList[params.index].targetPropertyName = arr[0];
                    this.transferFieldList[params.index].targetPropertyColumn = arr[1];
                    this.transferFieldList[params.index].newtargetPropertyName = arr[0];
                    this.transferFieldList[params.index].newtargetPropertyColumn = arr[1];
                    // render函数没有自动更新，需手动强制刷新。
                    this.$forceUpdate();
                  },
                },
              },
              this.deviceData.map((row) => {
                return h('i-option', {
                  props: {
                    value: row.propertyName + ',' + row.propertyColumn,
                    label: row.propertyName + ',' + row.propertyColumn,
                  },
                  style: {
                    'background-color': '#1C325A',
                    color: '#ffffff',
                  },
                });
              }),
            );
          },
          // renderHeader: (h, params) => {
          //   return h(
          //     "i-select",
          //     {
          //       style: {
          //         width: "216px",
          //       },
          //       props: {
          //         transfer: true,
          //         "transfer-class-name": "transfer-table-select",
          //       },
          //     }
          //     this.test.map((row) => {
          //       return h("i-option", {
          //         props: {
          //           value: row.value,
          //           label: row.label,
          //         },
          //         style: {
          //           // "background-color": "#1C325A",
          //           color: "#ffffff",
          //         },
          //       });
          //     })
          //   );
          // },
        },
      ],
      kafkaPropertyList: [], // 字段拾取数据表
      transferFieldList: [], // 字段映射数据表
      rawTransferFieldList: [], // 字段映射原始数据表
      isHandle: false, // 是否点击了新建或编辑
      mappedData: [], // 已映射数据表
      accessData: [], // 接入数据表
      checkedNames: [], // 编辑时已选中的数据
      accessId: '', // 接入数据表已选中ID
      deviceData: [],
      leftLoading: false,
      rightLoading: false,
    };
  },
  created() {},
  methods: {
    async init() {
      this.reset();
      this.visible = true;
      await this.getMappedList('1');
      this.getDevice();
    },
    // 编辑
    async edit(data, index) {
      this.active = index; // 当前激活
      this.isHandle = true;
      this.accessId = data.id;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getTransferFieldList();
      await this.getMappedList('0');
      this.getKafkaPropertyList(data.id);
    },
    // 获取已映射列表
    async getMappedList(isMapping) {
      try {
        let params = {
          topicId: this.topicId, // 主题ID
          isMapping: isMapping, // 是否映射
        };
        let res = await this.$http.get(governancetheme.getTopicTransferByMapping, { params });
        if (isMapping == '1') {
          this.mappedData = res.data.data;
        } else {
          this.accessData = res.data.data;
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取字段映射列表
    async getTransferFieldList() {
      try {
        let params = {
          topicTransferId: this.accessId, // 主题传输id
        };
        let res = await this.$http.get(governancetheme.queryTopicTransferFieldList, { params });
        const datas = res.data.data.map((item) => {
          item.newtargetPropertyName = item.targetPropertyName;
          item.newtargetPropertyColumn = item.targetPropertyColumn;
          // this.checkedIds.push(item.sourcePropertyId);
          this.checkedNames.push(item.sourcePropertyName);
          return item;
        });
        this.rawTransferFieldList = datas;
        // this.transferFieldList = datas;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取字段拾取列表
    async getKafkaPropertyList(topic) {
      try {
        let params = {
          topicTransferId: topic,
        };
        let res = await this.$http.post(governancetheme.queryKafkaPropertyList, params);
        this.kafkaPropertyList = res.data.data.map((item) => {
          if (this.checkedNames.length) {
            this.checkedNames.forEach((checkName) => {
              if (item.propertyName == checkName) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.transferFieldList = this.rawTransferFieldList;
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 新建映射
    newMapping() {
      this.active = '';
      this.isHandle = true;
      this.accessId = '';
      this.getMappedList('0');
      this.checkedNames = [];
      this.kafkaPropertyList = [];
      this.transferFieldList = [];
    },
    // 接收接入数据表字段
    handleChange(val) {
      if (val !== undefined) {
        this.accessId = val;
        this.getKafkaPropertyList(val);
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {
          newtargetPropertyName: '',
          newtargetPropertyColumn: '',
        };
        // obj.sourcePropertyId = item.id;
        obj.sourcePropertyName = item.propertyName;
        obj.sourcePropertyColumn = item.propertyColumn;
        this.transferFieldList.forEach((fieldItem) => {
          if (item.propertyName === fieldItem.sourcePropertyName) {
            obj.targetPropertyName = fieldItem.targetPropertyName;
            obj.targetPropertyColumn = fieldItem.targetPropertyColumn;
            obj.newtargetPropertyName = fieldItem.targetPropertyName;
            obj.newtargetPropertyColumn = fieldItem.targetPropertyColumn;
          }
        });
        return obj;
      });
      this.transferFieldList = selection;
    },
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.deviceData = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    async handleSave() {
      let list = [];
      if (!this.transferFieldList.length) {
        return false;
      }
      let errValid = this.transferFieldList.filter((item) => {
        const updateObj = {
          sourcePropertyName: item.sourcePropertyName,
          sourcePropertyColumn: item.sourcePropertyColumn,
          targetPropertyName: item.targetPropertyName,
          targetPropertyColumn: item.targetPropertyColumn,
          // sourcePropertyId: item.sourcePropertyId,
        };
        list.push(updateObj);
        return !item.newtargetPropertyColumn;
      });
      if (errValid.length) {
        this.$Message['error']({
          background: true,
          content: '请将信息填写完整!',
        });
        this.$Message.error('请将信息填写完整!');
        return false;
      }
      try {
        let params = {
          id: this.accessId, // 主题传输ID
          transferFieldList: list,
        };
        await this.$http.post(governancetheme.mappingEdit, params);
        this.$Message.success('字段映射配置保存成功！');
        // this.$Message["success"]({
        //   background: true,
        //   content: "字段映射配置保存成功！",
        // });
        this.getMappedList('1');
        this.reset();
        this.$emit('render');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.active = '';
      this.isHandle = false;
      this.checkedNames = [];
      this.kafkaPropertyList = [];
      this.transferFieldList = [];
    },
  },
  watch: {},
  components: { ModalLeft, TransferTable },
};
</script>
<style lang="less" scoped>
.align-flex {
  display: flex;
  align-items: center;
}
.fieldmapping {
  &-wrap {
    width: 100%;
    height: 630px;
    display: flex;
  }
  ul {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    li {
      padding: 7px 20px;
      .align-flex;
      justify-content: space-between;
      word-break: break-all;
      &:hover,
      &:visited {
        background: #184f8d;
        .edit-icon {
          color: #ffffff;
        }
      }
      .table-name {
        flex: 1;
        padding-right: 5px;
        color: #ffffff;
        font-size: 14px;
      }
      .edit-icon {
        width: 13px;
        font-size: 12px;
        color: #174f98;
        text-align: right;
      }
    }
    .active {
      background: #184f8d;
      .edit-icon {
        color: #ffffff;
      }
    }
  }
}
.empty-wrap {
  flex: 1;
  height: 100%;
  position: relative;
  .no-data {
    .null-data-text {
      color: var(--color-primary);
      font-size: 16px;
      line-height: 1.5;
      text-align: center;
    }
  }
}
@{_deep} .ivu-modal {
  &-body {
    min-height: 630px !important;
    padding: 0 !important;
  }
  &-header {
    margin-bottom: 0 !important;
    padding: 0 !important;
  }
}
</style>
