<template>
  <Icon type="md-copy" @click.stop="copy" title="复制" class="copy secondary"></Icon>
</template>

<style lang="less" scoped>
.copy {
  cursor: pointer;
  font-size: 15px;
  &:hover {
    color: #55c5f2;
  }
}
</style>

<script>
export default {
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {
    copy() {
      const input = document.createElement('input');
      document.body.appendChild(input);
      input.setAttribute('value', this.target);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      this.$Message.success('复制成功');
    },
  },
  watch: {},
  computed: {},
  props: {
    target: {
      required: true,
    },
  },
  components: {},
};
</script>
