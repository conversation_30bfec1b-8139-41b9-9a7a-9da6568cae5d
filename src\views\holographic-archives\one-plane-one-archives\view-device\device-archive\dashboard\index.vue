<template>
  <section class="content">
    <div
      class="switch-btn"
      @click="changeType"
      v-if="
        sbgnlxType.includes('1') &&
        (sbgnlxType.includes('3') || sbgnlxType.includes('2'))
      "
    >
      <i class="iconfont icon-ji<PERSON><PERSON>_zuoy<PERSON>uan"></i>
      <span>{{ type === "video" ? "抓拍" : "视频" }}</span>
    </div>
    <div class="content-top m-b20">
      <!-- 左侧 -->
      <div class="content-left">
        <!-- 基本信息 -->
        <BasicInformation
          :type="type"
          :baseInfo="baseInfo"
          @videoPlay="videoPlay"
        />
      </div>
      <div class="content-middle">
        <!--标签图谱-->
        <label-cloud-view
          :labels="baseInfo.labels || []"
          :type="4"
          :info="baseInfo"
          :photoUrl="
            baseInfo.imageUrls && baseInfo.imageUrls.length > 0
              ? baseInfo.imageUrls[0].photoUrl
              : ''
          "
        />
      </div>
      <!-- 右侧 -->
      <div class="content-right">
        <template v-if="type === 'capture'">
          <!-- 展示关系图谱 -->
          <ui-card title="关系统计" class="m-b20" :padding="0" v-if="graphObj">
            <div
              slot="extra"
              class="play-btn mr-20 color-primary cursor-p"
              @click="toRelationGraph"
            >
              关系图谱
            </div>
            <!-- 关系图谱 -->
            <graph
              v-if="hasGraphData"
              @finish="graphLoaded"
              class="right"
              :relation-can-operate="false"
            ></graph>
            <ui-empty v-else></ui-empty>
          </ui-card>
          <div class="capture">
            <div class="ui-card">
              <div class="card-head">
                <template
                  v-if="sbgnlxType.includes('2') && sbgnlxType.includes('3')"
                >
                  <p
                    class="capture-title face-capture"
                    :class="captureType == 3 ? 'capture-active' : ''"
                    @click="changeTab(3)"
                  >
                    <span>人脸抓拍记录</span>
                  </p>
                  <p
                    class="capture-title car-record"
                    :class="captureType == 2 ? 'capture-active' : ''"
                    @click="changeTab(2)"
                  >
                    <span>车辆抓拍记录</span>
                  </p>
                </template>
                <p class="capture-title face-capture capture-active" v-else>
                  <span v-if="sbgnlxType.includes('3')">人脸抓拍记录</span>
                  <span v-if="sbgnlxType.includes('2')">车辆抓拍记录</span>
                </p>
              </div>
              <div class="card-content">
                <snapRecord
                  :type="captureType"
                  :list="captureList"
                  v-if="captureList.length !== 0"
                ></snapRecord>
                <ui-loading v-if="captureLoading" />
                <ui-empty v-if="captureList.length === 0"></ui-empty>
              </div>
            </div>
            <span
              class="more-btn mr-10 more-box"
              v-show="captureList.length"
              @click.stop="captureMore"
            >
              更多<i class="iconfont icon-more"></i>
            </span>
          </div>
        </template>
        <template v-else>
          <ui-card title="历史录像分析" class="m-b20">
            <HistoricRecords
              :loading="historyVideoLoading"
              :list="historyVideo"
              @changeBtntime="
                (e) => {
                  changeBtntime(1, e);
                }
              "
            />
          </ui-card>
          <ui-card title="视频流质量分析">
            <VideoQuality
              :loading="videoQualityLoading"
              :list="videoQuality"
              @changeBtntime="
                (e) => {
                  changeBtntime(2, e);
                }
              "
            />
          </ui-card>
        </template>
      </div>
    </div>
    <!-- 底部布局 -->
    <div class="content-bottom">
      <!-- 左侧 -->
      <div class="content-left">
        <ui-card title="图上位置">
          <MapBase
            ref="map"
            class="map"
            v-if="baseInfo.geoPoint"
            :mapLayerConfig="{ showLatestLocation: true }"
            :chooseMapPosition="{
              lat: baseInfo.geoPoint.lat,
              lon: baseInfo.geoPoint.lon,
              address: baseInfo.detailAddress,
            }"
          />
        </ui-card>
      </div>
      <!-- 右侧 -->
      <div class="content-right">
        <ui-card title="在线情况分析" v-if="type === 'video'">
          <OnlineSituation :deviceId="deviceId" v-if="deviceId" />
        </ui-card>
        <!-- 抓拍分析 -->
        <div class="ui-card" v-else>
          <div class="card-head">
            <template
              v-if="sbgnlxType.includes('3') && sbgnlxType.includes('2')"
            >
              <p
                class="capture-title face-capture"
                :class="tabType == 3 ? 'capture-active' : ''"
                @click="handleTab(3)"
              >
                <span>人脸抓拍</span>
              </p>
              <p
                class="capture-title car-capture"
                :class="tabType == 2 ? 'capture-active' : ''"
                @click="handleTab(2)"
              >
                <span>车辆抓拍</span>
              </p>
            </template>
            <p class="capture-title face-capture capture-active" v-else>
              <span v-if="sbgnlxType.includes('3')">人脸抓拍</span>
              <span v-if="sbgnlxType.includes('2')">车辆抓拍</span>
            </p>
          </div>
          <div class="card-content">
            <CaptureBehavior
              ref="behavior"
              :sbgnlxType="sbgnlxType"
              :lineLoading="lineLoading"
              :HeatYAxis="HeatYAxis"
              :HeatSeries="HeatSeries"
              :heatLoading="heatLoading"
              :analysisMonth="analysisMonth"
              @overviewBtn="overviewBtn"
              @handleMonth="handleMonth"
            />
          </div>
        </div>
      </div>
    </div>
    <ui-modal
      v-model="videoVisible"
      title="视频播放"
      :r-width="750"
      footerHide
      @onCancel="handleCancel"
      @onOk="handleCancel"
    >
      <h5-player
        ref="H5Player"
        sceneFrom="mapVideo"
        :options="{ layout: '1*1' }"
        :deviceObj="deviceObj"
        dbFullScreen
        v-if="videoVisible"
      />
    </ui-modal>
    <!-- 抓拍更多弹框 -->
    <div class="more-capture">
      <ui-modal v-model="showMoreCapture" width="90%" :footerHide="true">
        <component :is="sectionName" :searchFields="searchFields" />
      </ui-modal>
    </div>
  </section>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "./components/snap-record";
import BasicInformation from "./components/basic-information.vue";
import labelCloudView from "@/views/holographic-archives/components/label-cloud-view/index";
import activityTrack from "./components/activity-track";
import CaptureBehavior from "./components/capture-behavior";
import OnlineSituation from "./components/online-situation";
import HistoricRecords from "./components/historic-records";
import VideoQuality from "./components/video-quality.vue";
import MapBase from "@/components/map/index";
import faceContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/face-contents.vue";
import vehicleContent from "@/views/wisdom-cloud-search/search-center/advanced-search/pages/vehicle-content";
import relativeGraphMixin from "@/views/holographic-archives/mixins/relativeGraphMixin2.js";
// import easyPlayer from '@/components/easy-player.vue'
import {
  queryDeviceSnapRecordList,
  queryDeviceSnapAnalysisMonth,
  queryDeviceSnapAnalysisDay,
  queryHistoryVideo,
  queryVideoQuality,
} from "@/api/device";
export default {
  mixins: [relativeGraphMixin],
  components: {
    swiper,
    swiperSlide,
    snapRecord,
    labelCloudView,
    activityTrack,
    CaptureBehavior,
    OnlineSituation,
    HistoricRecords,
    VideoQuality,
    MapBase,
    // easyPlayer,
    BasicInformation,
    faceContent,
    vehicleContent,
    graph: require("@/views/holographic-archives/components/graph").default,
  },
  props: {
    // 基本信息
    baseInfo: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      showMoreCapture: false,
      sectionName: "",
      searchFields: {},
      deviceObj: {},
      captureLoading: false, //抓拍记录加载
      lineLoading: false, //月分析折线图加载
      heatLoading: false, //日分析加载
      deviceId: "", //设备id
      sbgnlxType: [], //摄像机功能类型
      type: "capture", //1/抓拍,2/视频
      videoVisible: false, //播放视频弹框
      videoUrl: "", //视频播放路径
      tabType: 3, //3人脸2车辆抓拍分析
      captureType: 3, //3人脸2车辆抓拍记录
      captureList: [], //车辆抓拍/人脸抓拍记录
      analysisMonth: {}, //抓拍分析月
      analysisDay: {}, //抓拍日分析
      timeType: 1, //1月份2日
      historyVideoLoading: false,
      videoQualityLoading: false,
      onlineVideoLoading: false,
      historyVideo: [], //历史录像
      videoQuality: [], //视频流质量分析
      onlineInfo: {}, //在线情况分析
      dataObj: {
        imgUrl: require("@/assets/img/device/ctsxt.png"),
      },
      // 抓拍日分析Y轴日期
      HeatYAxis: {
        name: "",
        type: "category",
        data: [],
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: "#F9F9F9",
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: "rgba(0, 0, 0, 0.35)",
          },
        },
      },
      // 抓拍日分析值
      HeatSeries: [
        {
          name: "",
          type: "heatmap",
          data: [],
          label: {
            show: true,
            normal: {
              show: true,
              formatter: function (params) {
                //根据值判断不同字体颜色
                if (params.data["2"] == 0) {
                  return "{a|" + params.data["2"] + "}";
                } else if (params.data["2"] >= 1 && params.data["2"] < 2001) {
                  return "{b|" + params.data["2"] + "}";
                } else if (params.data["2"] > 2000) {
                  return "{a|" + params.data["2"] + "}";
                }
              },
              rich: {
                a: {
                  color: "#fff",
                },
                b: {
                  color: "#000",
                },
              },
            },
          },
          itemStyle: {
            borderColor: "#D3D7DE",
            borderType: "dashed",
            borderWidth: 1,
          },
        },
      ],
      labelList: [
        { id: "1", name: "枪机", color: "#F29F4C" },
        { id: "2", name: "频繁出没", color: "#1FAF8A" },
        { id: "3", name: "近3天无抓拍", color: "#1FAF8A" },
        { id: "4", name: "枪机", color: "#F29F4C" },
        { id: "11", name: "涉黄场所", color: "#EA4A36" },
        { id: "21", name: "涉黄场所", color: "#EA4A36" },
        { id: "31", name: "高频调用", color: "#48BAFF" },
        { id: "41", name: "枪机", color: "#F29F4C" },
        { id: "12", name: "高频调用", color: "#48BAFF" },
        { id: "22", name: "一类点", color: "#48BAFF" },
        { id: "32", name: "枪机", color: "#F29F4C" },
        { id: "42", name: "一级点", color: "#48BAFF" },
        { id: "13", name: "近3天无抓拍", color: "#1FAF8A" },
        { id: "33", name: "高频调用", color: "#48BAFF" },
        { id: "18", name: "人脸抓拍", color: "#48BAFF" },
        { id: "72", name: "关联19个案件", color: "#EA4A36" },
        { id: "63", name: "枪机", color: "#F29F4C" },
      ],
    };
  },
  computed: {
    ...mapGetters({
      graphObj: "systemParam/graphObj", // 是否有图谱
    }),
  },
  watch: {
    baseInfo: {
      handler(val) {
        if (val.deviceId) {
          this.sbgnlxType = val.sbgnlx.split("/");
          if (this.sbgnlxType.includes("2") || this.sbgnlxType.includes("3")) {
            this.type = "capture";
            this.tabType = this.sbgnlxType.find((v) => v != "1");
            this.captureType = this.sbgnlxType.find((v) => v != "1");
          } else {
            this.type = "video";
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.getDictData();
    this.deviceId = this.$route.query.archiveNo;
    this.captureType = this.$route.query.type ? this.$route.query.type : "3";
    this.tabType = this.$route.query.type ? this.$route.query.type : "3";
    this.queryDeviceSnapRecordList();
    this.queryDeviceSnapAnalysisMonth();
    this.queryHistoryVideo();
    this.queryVideoQuality();
    this.$nextTick(() => {
      this.$refs.map.mapidlerWheel();
    });
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    // 抓拍记录
    queryDeviceSnapRecordList() {
      this.captureLoading = true;
      this.captureList = [];
      let data = {
        address: this.baseInfo.address,
        deviceId: this.deviceId,
        sbgnlx: this.captureType, //2车辆/3人脸
      };
      queryDeviceSnapRecordList(data)
        .then((res) => {
          this.captureList = res.data || [];
        })
        .catch(() => {})
        .finally(() => {
          this.captureLoading = false;
        });
    },
    // 抓拍分析-月份
    queryDeviceSnapAnalysisMonth() {
      this.lineLoading = true;
      let time = this.$refs.behavior.month.replace("年", "-");
      let months = time.replace("月", "");
      let data = {
        deviceId: this.deviceId,
        sbgnlx: this.tabType, //3人脸2车辆
        snapTime: months,
      };
      queryDeviceSnapAnalysisMonth(data)
        .then((res) => {
          this.analysisMonth = res.data;
        })
        .catch(() => {})
        .finally(() => {
          this.lineLoading = false;
        });
    },
    // 抓拍分析近7天
    queryDeviceSnapAnalysisDay() {
      let date = this.$refs.behavior.getDate();
      let time = date.replace("年", "-");
      let months = time.replace("月", "");
      this.heatLoading = true;
      let data = {
        deviceId: this.deviceId,
        sbgnlx: this.tabType,
        snapTime: months,
      };
      queryDeviceSnapAnalysisDay(data)
        .then((res) => {
          let obj = res.data;
          let y = [];
          let dataArr = [];
          if (obj) {
            Object.keys(obj).forEach((item, index) => {
              // y轴日期
              y.push(item.slice(8, 10) + "日");
              // 每个小时对应的抓拍值
              obj[item].Y.forEach((it, i) => {
                //遍历对应的y值
                let arr = [];
                arr[0] = it; //把y轴对应的每个值转化为数组
                arr.unshift(index); //添加Y轴对应的坐标值
                arr.unshift(i); //添加X轴对应的坐标值
                dataArr.push(arr);
              });
            });
          }
          this.HeatYAxis.name = this.$refs.behavior.getDate();
          this.HeatYAxis.data = y;
          this.HeatSeries[0].data = dataArr;
        })
        .catch(() => {})
        .finally(() => {
          this.heatLoading = false;
        });
    },
    //  历史录像分析
    queryHistoryVideo(val) {
      this.historyVideoLoading = true;
      let data = {
        deviceId: this.deviceId,
        dateType: val ? val : 1, //1：近一月，2：近三月
      };
      queryHistoryVideo(data)
        .then((res) => {
          let obj = res.data;
          let arr = [];
          // 遍历返回值，返回新数组
          Object.keys(obj).forEach((item) => {
            arr.push({
              name: item,
              value: obj[item],
            });
          });
          this.historyVideo = arr;
        })
        .catch(() => {})
        .finally(() => {
          this.historyVideoLoading = false;
        });
    },
    //视频质量分析
    queryVideoQuality(val) {
      this.videoQualityLoading = true;
      let data = {
        deviceId: this.deviceId,
        dateType: val ? val : 1, //1：近一月，2：近三月
      };
      queryVideoQuality(data)
        .then((res) => {
          let obj = res.data;
          let arr = [];
          Object.keys(obj).forEach((item) => {
            arr.push({
              name: item,
              value: obj[item],
            });
          });
          this.videoQuality = arr;
        })
        .catch(() => {})
        .finally(() => {
          this.videoQualityLoading = false;
        });
    },
    // 历史录像和视频质量时间切换
    changeBtntime(type, val) {
      if (type === 1) {
        this.queryHistoryVideo(val);
      } else {
        this.queryVideoQuality(val);
      }
    },
    // 人脸抓拍/车辆抓拍月份切换
    handleTab(val) {
      this.tabType = val;
      let type = this.$refs.behavior.typeBtn;
      if (type === 1) {
        this.queryDeviceSnapAnalysisMonth();
      } else {
        this.queryDeviceSnapAnalysisDay();
      }
    },
    // 折线图选择月份
    handleMonth(val) {
      if (val) {
        this.queryDeviceSnapAnalysisMonth();
      }
    },
    // 抓拍日分析/月分析切换
    overviewBtn(val) {
      if (val) {
        this.timeType = val;
        this.queryDeviceSnapAnalysisDay();
      }
    },
    // 抓拍设备和视频监控切换
    changeType() {
      if (this.type === "capture") {
        this.type = "video";
      } else {
        this.type = "capture";
      }
    },
    // 点击视频播放
    async videoPlay() {
      this.videoVisible = true;
      this.playVideo();
      // 接口请求地址
      // const { deviceId } = this.baseInfo;
      // let res = await this.$http.post(playing, { deviceId })
      // getCipherChannel(deviceId).then((res) => {
      //   console.log(res);
      //   // if (res.data.msg != '成功') {
      //   //   this.$Message.error(res.data.msg)
      //   // }
      //   this.videoUrl = res.data.ws_hls;
      // });
    },
    playVideo() {
      const { deviceId, deviceName, geoPoint, ptzType, orgCode } =
        this.baseInfo;
      this.deviceObj = {
        deviceGbId: deviceId,
        deviceName,
        geoPoint,
        ptzType,
        orgCode,
        devicetype: liveType,
        playType: "live",
      };
      this.queryLog({
        muen: "设备档案",
        name: "视频播放",
        type: "4",
        remark: `查看【${deviceName}】实时视频`,
      });
    },
    onVideoPlayCancel() {
      // this.$http.post(vedio.stop + this.info.deviceId)
    },
    // 停止视频播放
    handleCancel() {
      this.videoVisible = false;
    },
    // 抓拍记录切换
    changeTab(val) {
      this.captureType = val;
      this.queryDeviceSnapRecordList();
    },
    // 点击更多到详情
    captureMore() {
      if (this.captureType == 2) {
        this.sectionName = "vehicleContent";
      } else {
        this.sectionName = "faceContent";
      }
      this.searchFields = {
        deviceInfo: JSON.stringify(this.baseInfo),
        noSearch: 1,
      };
      this.showMoreCapture = true;
    },
  },
};
</script>
<style lang="less" scoped>
.switch-btn {
  position: absolute;
  top: 30px;
  left: 20px;
  font-size: 14px;
  cursor: pointer;
  span {
    margin-left: 3px;
  }
}
.content {
  width: 100%;
  height: 100%;
  padding: 16px 20px 20px;
  .content-top {
    width: 100%;
    display: flex;
    height: 65%;
    .content-left,
    .content-right {
      width: 500px;
      display: flex;
      flex-direction: column;
      height: 100%;
      .capture {
        position: relative;
        .more-box {
          position: absolute;
          top: 10px;
          right: 10px;
        }
      }
      .ui-card,
      .ui-tabs {
        flex: 1;
      }
      /deep/ .card-content {
        height: 234px;
      }
      /deep/ .ivu-tabs-content {
        height: 236px;
      }
    }
    // .info-content {
    //   width: 100%;
    //   p {
    //     display: flex;
    //     font-size: 14px;
    //     line-height: 36px;
    //     color: rgba(0, 0, 0, 0.9);
    //     .label {
    //       text-align-last: justify;
    //       text-align: justify;
    //       text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    //       width: 58px;
    //       font-weight: bold;
    //       white-space: nowrap;
    //       color: #2c86f8;
    //       margin-right: 15px;
    //     }
    //     .branch {
    //       width: 91px;
    //     }
    //     b {
    //       font-weight: normal;
    //       word-break: break-all;
    //       width: 400px;
    //       &.weight {
    //         font-size: 20px;
    //         font-weight: bold;
    //       }
    //     }
    //   }
    //   .bg-vedio {
    //     margin: 0 auto 15px;
    //     height: 170px;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     width: 360px;
    //     background: url('~@/assets/img/device/vedio.png') center/cover;
    //     span {
    //       display: inline-block;
    //       width: 118px;
    //       height: 36px;
    //       display: flex;
    //       justify-content: center;
    //       align-items: center;
    //       border-radius: 18px;
    //       font-size: 14px;
    //       i {
    //         font-size: 18px;
    //         margin-right: 5px;
    //       }
    //     }
    //   }
    // }
    .content-middle {
      overflow: hidden;
      flex: 1;
      display: flex;
      width: 100%;
      flex-direction: column;
    }
  }
  .content-bottom {
    width: 100%;
    height: 33%;
    display: flex;
    .content-left {
      margin-right: 20px;
      width: 26.6%;
      height: 100%;
      .ui-card {
        flex: 1;
        height: 100%;
      }
      /deep/ .card-content {
        height: 236px;
      }
    }
    .content-right {
      width: 72.4%;
      height: 100%;
      display: flex;
      position: relative;
      .ui-card,
      .ui-tabs {
        flex: 1;
      }
      .ui-tabs {
        /deep/.data-time {
          left: 18% !important;
        }
        /deep/.online-search {
          left: 22%;
        }
      }
      /deep/ .card-content {
        height: 246px;
        padding: 20px !important;
      }
      /deep/ .ivu-tabs-content {
        height: 246px;
        padding: 20px 0 10px;
      }
    }
  }
  .play-btn {
    margin-top: 5px;
    margin-right: 15px;
    font-size: 14px;
    cursor: pointer;
    line-height: 20px;
    display: flex;
    align-items: center;
    i {
      font-size: 16px;
    }
  }
  .capture-title {
    font-size: 16px;
    cursor: pointer;
    line-height: 30px;
    text-align: center;
    background: #d3d7de;
    color: #666;
    transform: skewX(-18deg);
    padding: 0 23px;
    left: -6px;
    span {
      transform: skewX(18deg);
      display: inline-block;
    }
  }
  .face-capture {
    position: relative;
  }
  .car-capture {
    position: absolute;
    left: 100px;
  }
  .car-record {
    position: absolute;
    left: 135px;
  }
  .capture-active {
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    color: #fff;
    font-weight: bold !important;
  }
}
/deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
  height: 440px;
}

.more-capture {
  /deep/ .ivu-modal {
    height: 90%;
    .ivu-modal-content {
      height: 100%;
      .ivu-modal-header {
        height: 42px;
        line-height: 42px;
      }
      .ivu-modal-body {
        max-height: none !important;
        height: calc(~"100% - 42px");
        .main-container {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
