export const config = {
  node: { // 节点的默认配置
    label: { // 标签配置
      show: true, // 是否显示
      color: '120,120,120', // 字体颜色
      font: 'normal 12px Arial', // 字体大小及类型
      textPosition: 'Bottom_Center'// 文字位置 Middle_Center,Bottom_Center
    },
    shape: 'circle', // 节点形状 circle
    color: '50,155,230', // 节点颜色
    borderColor: '255,255,255', // 边框颜色
    borderWidth: 0, // 边框宽度,
    size: 60, // 节点大小
    selected: { // 选中时的样式设置
      borderColor: '44,134,248', // 选中时边框颜色
      borderAlpha: 1, // 选中时的边框透明度
      borderWidth: 6, // 选中是的边框宽度
      showShadow: true, // 是否展示阴影
      shadowBlur:12, //阴影范围大小
      shadowColor: '50,80,250'// 选中是的阴影颜色
    }
  },
  link: { // 连线的默认配置
    label: { // 连线标签
      show: true, // 是否显示
      color: '50,120,230', // 字体颜色
      font: 'normal 12px Arial',// 字体大小及类型
      fontColor: '50,120,230',
      background:'250,250,250' //文字背景色（设置后文字居中,一般与画布背景色一致）
    },
    lineType: 'straight', // 连线类型
    color: '185,185,185', // 连线颜色
    lineWidth: 1, // 连线宽度
    showArrow: false, // 显示箭头
    selectedColor: '50,120,230', // 选中时的颜色
    selected: { // 选中时的样式设置
      lineWidth: 1, // 连线宽度
      color: '50,120,230', // 选中时的颜色
    }
  },
  highLightNeiber: false, // 相邻节点高亮开关
  wheelZoom: 0.8// 滚轮缩放开关，不使用时不设置[0,1]
}

//获取默认的配置
export const getDefaultConfig = () =>{

  return Object.assign({},config);
};
