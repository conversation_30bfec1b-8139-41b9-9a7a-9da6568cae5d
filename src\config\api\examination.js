export default {
  addReport: '/ivdg-examination-service/examination/reportReview/add', // 报备新增
  getStatInfo: '/ivdg-examination-service/examination/reportReview/getStatInfo', // 报备/审核(新版) 获取统计信息
  pageList: '/ivdg-examination-service/examination/reportReview/pageList', // 查询 报备/审核(新版) 分页列表
  remove: '/ivdg-examination-service/examination/reportReview/remove', // 批量删除报备/审核 信息
  update: '/ivdg-examination-service/examination/reportReview/update', // 报备编辑
  getView: '/ivdg-examination-service/examination/reportReview/view', // 根据ID 获取 报备/审核 详情
  getCorrelationIndexList: '/ivdg-examination-service/examination/reportReview/getCorrelationIndexList', // 根据报备类型获取关联指标信息
  reportExport: '/ivdg-examination-service/examination/reportReview/export', // 根据报备类型获取关联指标信息
  reviewData: '/ivdg-examination-service/examination/reportReview/reviewData', // 根据报备类型获取关联指标信息
  downloadTemplate: '/ivdg-examination-service/examination/reportReview/downloadTemplate', //下载模板
  deviceListExport: '/ivdg-examination-service/examination/reportReview/deviceListExport', // 情况报备 详情弹框导出
};
