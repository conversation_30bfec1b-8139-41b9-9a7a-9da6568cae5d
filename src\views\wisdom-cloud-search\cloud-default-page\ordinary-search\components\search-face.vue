<template>
	<div class="search card-border-color" :class="{'searchPading': pageType == 0}">
		<Form :inline="true" :class="visible ? 'advanced-search-show' : ''">
			<!-- 关键字搜索 -->
			<div class="general-search">
				<div class="input-content" v-if="pageType == 0">
					<FormItem label="数据资源:">
						<Select v-model="queryParam.dataSource" placeholder="请选择" @on-change="dataSourceChange">
							<Option :value="1">静态库</Option>
							<Option :value="2">抓拍库</Option>
						</Select>
					</FormItem>
					<!-- <FormItem label="视频身份:" prop="videoIdentity" v-if="queryParam.dataSource == 2">
                        <Input v-model="queryParam.videoIdentity" placeholder="请输入多条用逗号(,)分隔"></Input>
                    </FormItem> -->
					<FormItem label="身份证号:" prop="idCardNo" v-if="queryParam.dataSource == 1">
						<Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
					</FormItem>
					<FormItem label="抓拍时段:">
						<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input=" e => { input(e, 'timeSlot')}">
							<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
								{{ item.name }}
							</ui-tag-select-option>
						</ui-tag-select>
						<DatePicker v-if="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" @on-ok="handleDateOk" @on-change="dateChange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择抓拍时间段" transfer
							style="width: 350px"></DatePicker>
					</FormItem>
				</div>
				<div class="input-content" v-else>
					<div class="upload-input-list">
						<uiUploadImg :algorithmType="1" v-model="queryParam.urlList" @imgUrlChange="imgUrlChange" size="small" />
					</div>
					<div class="other-search">
						<div class="other-search-top card-border-color">
							<FormItem label="相似度:" class="slider-form-item">
								<div class="slider-content">
									<Slider v-model="queryParam.similarity"></Slider>
									<span>{{ queryParam.similarity }}%</span>
								</div>
							</FormItem>
							<FormItem label="算法选择:" prop="algorithm">
								<!-- <CheckboxGroup v-model="queryParam.algorithm">
									<Checkbox label="0">海康</Checkbox>
									<Checkbox label="1"><span class="gerling">格灵深瞳</span></Checkbox>
								</CheckboxGroup> -->
                                <Select v-model="queryParam.algorithmVendorType">
                                    <Option value="GLST" placeholder="请选择">格灵深瞳</Option>
                                    <Option value="HK" placeholder="请选择">海康</Option>
                                </Select>
							</FormItem>
						</div>
						<div class="other-search-bottom">
							<div class="flex">
								<FormItem label="设备资源:">
									<div class="select-tag-button" @click="selectDevice()">
										选择设备/已选（{{ queryParam.selectDeviceList.length }}）</div>
								</FormItem>
								<FormItem label="抓拍时段:">
									<ui-tag-select ref="tagSelect1" :hideCheckAll='true' :value="captureTimePeriod[0].name" @input=" e => { input(e, 'timeSlot')}">
										<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
											{{ item.name }}
										</ui-tag-select-option>
									</ui-tag-select>
									<DatePicker v-if="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" @on-ok="handleDateOk" @on-change="dateChange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择抓拍时间段" transfer
										style="width: 350px"></DatePicker>
								</FormItem>
								<!-- <FormItem label="数据资源1:">
									<Select v-model="queryParam.dataSource" placeholder="请选择" @on-change="dataSourceChange" style="width: 200px">
										<Option :value="1">静态库</Option>
										<Option :value="2">动态库</Option>
									</Select>
								</FormItem> -->
								<!-- <FormItem label="视频身份:" v-if="queryParam.dataSource == 2">
                                    <Input v-model="queryParam.videoIdentity" placeholder="请输入"></Input>
                                </FormItem> -->
								<FormItem label="身份证号:" prop="idCardNo" v-if="queryParam.dataSource == 1">
									<Input v-model="queryParam.idCardNo" placeholder="请输入"></Input>
								</FormItem>
							</div>
							<div class="btn-group">
								<span class="advanced-search-text primary" @click="advancedSearchHandle($event)"> 更多条件
									<i class="iconfont icon-jiantou"></i> </span>
								<Button type="primary" @click="searchHandle">查询</Button>
								<Button @click="resetHandle">重置</Button>
							</div>
						</div>
					</div>
				</div>
				<div class="btn-group" v-if="pageType == 0">
					<span class="advanced-search-text primary" @click="advancedSearchHandle($event)"> 更多条件 <i class="iconfont icon-jiantou"></i> </span>
					<Button type="primary" @click="searchHandle">查询</Button>
					<Button @click="resetHandle">重置</Button>
				</div>
			</div>
			<!--更多搜索条件-->
			<div class="advanced-search" @click="($event) => $event.stopPropagation()">
				<template v-if="queryParam.dataSource == 2">
					<!-- <div class="advanced-search-item card-border-color">
						<Row>
							<Col span="6">
							<FormItem label="设备资源:">
								<div class="select-tag-button" @click="selectDevice()">选择设备/已选（{{ queryParam.selectDeviceList.length }}）</div>
							</FormItem>
							</Col>
							<Col span="10">
							<FormItem label="抓拍时段:">
								<ui-tag-select ref="tagSelect1" @input=" e => { input(e, 'timeSlot')}">
									<ui-tag-select-option v-for="(item, $index) in captureTimePeriod" :key="$index" :name="item.name">
										{{ item.name }}
									</ui-tag-select-option>
								</ui-tag-select>
								<DatePicker v-if="queryParam.timeSlot == '自定义'" v-model="queryParam.timeSlotArr" @on-ok="handleDateOk" @on-change="dateChange" type="datetimerange" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择抓拍时间段" transfer
									style="width: 350px"></DatePicker>
							</FormItem>
							</Col>
						</Row>
					</div> -->
					<div class="advanced-search-item card-border-color">
						<Row>
							<Col span="6">
                                <FormItem label="眼       镜:">
                                    <ui-tag-select ref="glasses" @input=" e => { input(e, 'glasses')}">
                                        <ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureGlasses" :key="$index" :name="item.dataKey">
                                            {{ item.dataValue }}
                                        </ui-tag-select-option>
                                    </ui-tag-select>
                                </FormItem>
							</Col>
							<Col span="6">
                                <FormItem label="性       别:">
                                    <ui-tag-select ref="gender" @input="e => {input(e, 'gender')}">
                                        <ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureGender" :key="$index" :name="item.dataKey">
                                            {{ item.dataValue }}
                                        </ui-tag-select-option>
                                    </ui-tag-select>
                                </FormItem>
							</Col>
						</Row>
					</div>
					<div class="advanced-search-item card-border-color" v-if="queryParam.algorithmVendorType !== 'HK'">
						<Row>
							<Col span="6">
                                <FormItem label="帽       子:">
                                    <ui-tag-select ref="cap" @input=" e => {input(e, 'cap')}">
                                        <ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureCap" :key="$index" :name="item.dataKey">
                                            {{ item.dataValue }}
                                        </ui-tag-select-option>
                                    </ui-tag-select>
                                </FormItem>
							</Col>
							<Col span="6">
                                <FormItem label="口       罩:">
                                    <ui-tag-select ref="faceMask" @input=" e => { input(e, 'faceMask')}">
                                        <ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureMask" :key="$index" :name="item.dataKey">
                                            {{ item.dataValue }}
                                        </ui-tag-select-option>
                                    </ui-tag-select>
                                </FormItem>
							</Col>
						</Row>
					</div>
					<div class="advanced-search-item card-border-color">
						<Row>
                            <Col span="6">
                                <FormItem label="年  龄   段:">
                                    <ui-tag-select ref="age" @input=" e => { input(e, 'age')}">
                                        <ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureAge" :key="$index" :name="item.dataKey">
                                            {{ item.dataValue }}
                                        </ui-tag-select-option>
                                    </ui-tag-select>
                                </FormItem>
							</Col>
						</Row>
					</div>
				</template>
				<template v-if="queryParam.dataSource === 1">
					<div class="advanced-search-item card-border-color">
						<Row>
							<Col span="4">
							<FormItem label="姓 名:">
								<Input v-model="queryParam.name" placeholder="请输入"></Input>
							</FormItem>
							</Col>
							<Col span="4">
							<FormItem label="来 源:">
								<Select v-model="queryParam.faceLibId" placeholder="请选择" transfer>
									<Option :value="item.id" v-for="(item, index) in sourceList" :key="index">
										{{ item.libName }}</Option>
								</Select>
							</FormItem>
							</Col>
							<Col span="5">
							<FormItem label="性别:">
								<ui-tag-select ref="gender" @input=" e => {input(e, 'gender')} ">
									<ui-tag-select-option v-for="(item, $index) in ipbdFaceCaptureGender" :key="$index" :name="item.dataKey">
										{{ item.dataValue }}
									</ui-tag-select-option>
								</ui-tag-select>
							</FormItem>
							</Col>
							<!-- <Col span="5">
                            <FormItem label="标签:">
                                <div class="select-tag-button" @click="selectLabelHandle">请选择标签{{ queryParam.labelIds.length > 0 ? `{已选（${queryParam.labelIds.length}）}` : '' }}</div>
                            </FormItem>
                        </Col> -->
						</Row>
					</div>
				</template>
			</div>
			<!-- 选择标签 -->
			<LabelModal :labelType='2' @setCheckedLabel="setCheckedLabel" ref="labelModal" />
			<!-- 选择设备 -->
			<select-device ref="selectDevice" :checkedLabels="checkedLabels" @selectData="selectData" />
		</Form>
	</div>
</template>
<script>
	import { mapActions, mapGetters } from 'vuex'
	import { getFaceLibNameByLibType } from '@/api/wisdom-cloud-search'
	import uiUploadImg from '@/components/ui-upload-new-img/index'
	import LabelModal from '@/views/holographic-archives/components/relationship-map/label-add'
	export default {
		components: {
			LabelModal,
			uiUploadImg
		},
		props: {},
		data() {
			return {
				visible: false,
				queryParam: {
					dataSource: 2, // 数据资源
					selectDeviceList: [], // 设备资源
					timeSlotArr: [], // 自定义抓拍时段
					deviceIds: [], // 设备
					startDate: '', // 抓拍时段 - 开始时间
					endDate: '', // 抓拍时段 - 结束时间
					portraitType: '', // 动态库 - 类型
					urlList: [],
					similarity: 60,
                    algorithmVendorType:'GLST',
					labelIds: [],
					timeSlot: '近一天',
				},
				checkedLabels: [], // 已选择的标签
				sourceList: [], // 来源
				captureTimePeriod: [
					{ name: '近一天', value: '1' },
					{ name: '近三天', value: '2' },
					{ name: '近一周', value: '3' },
					{ name: '自定义', value: '4' }
				]
			}
		},
		async created() {
			if (this.$route.query.archiveNo) {
				this.queryParam.plateNo = JSON.parse(this.$route.query.archiveNo)
			} else {
				// this.queryParam.plateNo = '';
			}
			await this.getDictData()
			this.tableSourceList()
			if (typeof this.globalObj.searchForPicturesDefaultSimilarity === 'string') {
				this.queryParam.similarity = Number(this.globalObj.searchForPicturesDefaultSimilarity);
			} else {
				this.queryParam.similarity = this.globalObj.searchForPicturesDefaultSimilarity;
			}
			window.addEventListener("click", (e) => {
				this.visible = false;
			})
		},
		watch: {
			'queryParam.dataSource'(newVal, oldVal) {
				// console.log(newVal, oldVal)
			},
			pageType: {
				handler(val) {
					// console.log(val, 'pageType')
				},
				immediate: true
			}
		},
		computed: {
			...mapGetters({
				globalObj: 'systemParam/globalObj',
				pageType: 'common/getPageType', // 页面类型
				ipbdFaceCaptureClustering: 'dictionary/getIpbdFaceCaptureClustering', // 聚类
				ipbdFaceCaptureGender: 'dictionary/getIpbdFaceCaptureGender', // 性别
				ipbdFaceCaptureAge: 'dictionary/getIpbdFaceCaptureAge', // 年龄段
				ipbdFaceCaptureGlasses: 'dictionary/getIpbdFaceCaptureGlasses', // 眼镜
				ipbdFaceCaptureMask: 'dictionary/getIpbdFaceCaptureMask', // 口罩
				ipbdFaceCaptureCap: 'dictionary/getIpbdFaceCaptureCap' // 帽子
			})
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			advancedSearchHandle($event) {
				$event.stopPropagation()
				if (this.visible) {
					this.visible = false
				} else {
					this.visible = true
				}
			},
			// 选择标签
			selectLabelHandle() {
				this.$refs.labelModal.init(this.queryParam.labelIds)
			},
			// 已选标签
			setCheckedLabel(val) {
				this.queryParam.labelIds = val;
			},
			dataSourceChange(e) {
				this.searchHandle()
			},
			handleDateOk() {
				// let timeEnd = this.$dayjs(this.queryParam.timeSlotArr[1]).format('YYYY-MM-DD HH:mm:ss').split(' ');

				// let timeStart =  this.$dayjs(this.queryParam.timeSlotArr[0]).format('YYYY-MM-DD HH:mm:ss');
				// this.queryParam.timeSlotArr = [timeStart, timeEnd.join(' ')];
			},
			dateChange(start, end) {
				if (start[1].slice(-8) === '00:00:00') {
					start[1] = start[1].slice(0, -8) + '23:59:59'
				}
				this.queryParam.timeSlotArr = [start[0], start[1]];
			},
			// 搜索函数
			searchHandle() {
				this.visible = false;
				// if (this.queryParam.timeSlotArr.length > 0 && this.queryParam.timeSlotArr[0] != '') {
				// 	this.queryParam.perceiveBeginTime = this.$dayjs(this.queryParam.timeSlotArr[0]).format('YYYY-MM-DD HH:mm:ss')
				// 	this.queryParam.perceiveEndTime = this.$dayjs(this.queryParam.timeSlotArr[1]).format('YYYY-MM-DD HH:mm:ss')
				// }
				// 处理控件本身的清空
				// if (this.queryParam.timeSlotArr[0] == '') {
				// 	this.queryParam.perceiveBeginTime = ''
				// 	this.queryParam.perceiveEndTime = ''
				// }
				this.$parent.pageInfo.pageNumber = 1
				this.$emit('search', this.queryParam)
			},
			/**
			 * 重置，清空数据
			 */
			resetHandle() {
				// 清空组件选中状态
				if (this.queryParam.dataSource == 2) {

					this.$refs.glasses.clearChecked()
					this.$refs.age.clearChecked()
					this.$refs.faceMask.clearChecked()
					this.$refs.cap.clearChecked()
				}
				this.$refs.gender.clearChecked()
				this.$refs.tagSelect1.clearChecked(false)
				this.queryParam = {
					dataSource: 2, // 资源类型
					selectDeviceList: [], // 设备资源
					timeSlotArr: [], // 自定义抓拍时段
					deviceIds: [], // 设备
					features: [], // 设备
                    imageBases: [],
					startDate: '', // 抓拍时段 - 开始时间
					endDate: '', // 抓拍时段 - 结束时间
					portraitType: '', // 动态库 - 类型
					gender: '',
					age: '',
					cap: '',
					carryThings: '',
					characteristicsOfCoat: '',
					dateType: '',
					faceMask: '',
					glasses: '',
					hairStyle: '',
					idCardNo: '',
					name: '',
					notSearchTotal: '',
					searchValue: '',
					shoes: '',
					videoIdentity: '',
					similarity: this.globalObj.searchForPicturesDefaultSimilarity - 0,
					urlList: [],
					labelIds: [],
					timeSlot: '近一天',
				}
				this.visible = false
				this.$emit('reset')
			},
			checkedHandle(arr) {
				console.log(arr)
			},
			/**
			 * 选择接口返回数据
			 */
			input(e, key) {
				this.queryParam[key] = e
				this.$forceUpdate()
			},
			/**
			 * 选择设备
			 */
			selectDevice() {
				this.$refs.selectDevice.show(this.queryParam.selectDeviceList)
			},
			/**
			 * 已选择设备数据返回
			 */
			selectData(list) {
				this.queryParam.selectDeviceList = list
			},
			/**
			 * 图片上传结果返回
			 */
			imgUrlChange(list) {
				// 以图搜图字段
				let features = [];
                let imageBases = [];
				list.forEach(item => {
					if (item) {
						features.push(item.feature)
                        imageBases.push(item.imageBase)
					}
				})
				this.queryParam.features = features;
                this.queryParam.imageBases = imageBases;
			},
			/**
			 * 来源
			 */
			tableSourceList() {
				getFaceLibNameByLibType({ libSource: 2 })
					.then(res => {
						this.sourceList = res.data
					})
					.catch(err => {
						console.error(err)
					})
					.finally(() => { })
			}
		}
	}
</script>
<style lang="less" scoped>
	.btn-group {
		display: flex;
		align-items: center;
		margin-bottom: 10px;

		.advanced-search-text {
			display: flex;
			align-items: center;
			cursor: pointer;
			margin-right: 30px;

			.icon-jiantou {
				margin-left: 2px;
				font-size: 18px;
				transform: rotate(0deg);
				transition: transform 0.2s;
			}
		}
	}

	.search {
		padding: 10px 20px 0;
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;

		.ivu-form-inline {
			width: 100%;
		}

		.ivu-form-item {
			margin-bottom: 0;
			margin-right: 30px;
			display: flex;
			align-items: center;

			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				width: 72px;
				text-align-last: justify;
				text-align: justify;
				text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
			}
		}

		.general-search {
			display: flex;
			width: 100%;

			.input-content {
				flex: 1;
				display: flex;

				.upload-input-list {
					display: flex;
					max-width: 440px;
				}

				.other-search {
					display: flex;
					flex: 1;
					box-sizing: border-box;
					flex-direction: column;
					padding-left: 10px;

					.other-search-top {
						display: flex;
						border-bottom: 1px dashed #fff;
					}

					.ivu-form-item {
						display: flex;
						margin-bottom: 10px;

						/deep/ .ivu-form-item-label {
							padding-right: 10px;
						}

						.ivu-input-wrapper,
						.ivu-select {
							width: 200px;
						}
					}

					.other-search-bottom {
						display: flex;
						justify-content: space-between;
						padding-top: 10px;
						box-sizing: border-box;
						/deep/ .ivu-form-item-content {
							display: flex;
						}
						.slider-form-item {
							/deep/ .ivu-form-item-content {
								display: flex;
								align-items: center;
							}
						}
					}
				}
			}
			/deep/ .ivu-form-item-content {
				display: flex;
				align-items: center;
			}
		}

		.advanced-search {
			display: flex;
			position: absolute;
			width: 100%;
			padding: 0 20px;
			box-sizing: border-box;
			z-index: 11;
			max-height: 0px;
			top: 100%;
			left: 0;
			transition: max-height 0.3s;
			overflow: auto;
			flex-direction: column;

			.advanced-search-item {
				&.justify-content-normal {
					justify-content: normal;
				}

				//   display: flex;
				//   justify-content: flex-start;
				// justify-content: space-between;
				padding: 10px 0;
				border-bottom: 1px dashed #fff;

				//   align-items: center;
				&:first-child {
					border-top: 1px solid;
				}

				.ivu-form-item {
					margin-right: 30px;

					&:last-child {
						margin-right: 0;
					}

					&.percent-70 {
						width: 70%;
					}

					display: flex;

					.text-radio-group {
						margin-left: -10px;
					}
				}

				.btn-group {
					flex: 1;
					justify-content: flex-end;
				}

				/deep/ .ivu-form-item-content {
					display: flex;
					align-items: center;
				}
			}
		}

		.advanced-search-show {
			.advanced-search {
				max-height: 400px;
				transition: max-height 0.7s;
			}

			.advanced-search-text {
				/deep/ .icon-jiantou {
					transform: rotate(180deg);
					transition: transform 0.2s;
				}
			}
		}
	}

	.searchPading {
		padding: 10px 20px;
	}

	.gerling {
		color: #f29f4c;
	}
</style>