<template>
  <section class="my-container">
    <div class="top-operate-content">
      <h2 class="title">精准检索</h2>
      <router-link :to="'cloud-default-page'" class="btn-return"
        ><i class="iconfont icon-return color-primary"></i>返回首页</router-link
      >
    </div>
    <div class="result-content">
      <!-- :width="50 / 192 + 'rem'" -->
      <Menu
        :active-name="sectionName"
        :width="50 / 192 + 'rem'"
        @on-select="selectItemHandle"
        class="search-menu"
      >
        <MenuItem
          v-for="(item, index) in menuList"
          :key="index"
          :name="item.name"
        >
          <Tooltip :content="item.label" placement="right" theme="light">
            <i class="iconfont" :class="item.iconName"></i>
          </Tooltip>
        </MenuItem>
      </Menu>
      <keep-alive>
        <component :is="sectionName"></component>
      </keep-alive>
    </div>
  </section>
</template>
<script>
import searchPictures from "../components/search-pictures";
import policeServiceContent from "./pages/police-service-content.vue";
import faceContent from "../ordinary-search/pages/face-content";
import humanBodyContent from "./pages/human-body-content.vue";
import nonmotorVehicleContent from "../ordinary-search/pages/nonmotor-vehicle-content.vue";
import vehicleContent from "./pages/vehicle-content";
import wifiContent from "./pages/wifi-content";
import RFIDContent from "./pages/RFID-content";
import electricContent from "./pages/electric-content";
import deviceContent from "./pages/device-content";
import { mapActions, mapMutations } from "vuex";
export default {
  name: "advancedSearch",
  components: {
    searchPictures,
    policeServiceContent, //警务模块
    faceContent, //人脸模块
    vehicleContent, //车辆模块
    humanBodyContent, //人体
    nonmotorVehicleContent, //非机动车
    wifiContent, //wifi模块
    RFIDContent, //rfid模块
    electricContent, //电子模块
    deviceContent, //设备模块
  },
  data() {
    return {
      visible: false,
      sectionName: "",
      menuList: [
        {
          label: "警务",
          value: 1,
          iconName: "icon-anjian",
          name: "policeServiceContent",
        },
        {
          label: "人像",
          value: 2,
          iconName: "icon-renlian",
          name: "faceContent",
        },
        {
          label: "车辆",
          value: 3,
          iconName: "icon-cheliang",
          name: "vehicleContent",
        },
        {
          label: "人体",
          value: 8,
          iconName: "icon-renti",
          name: "humanBodyContent",
        },
        {
          label: "非机动车",
          value: 9,
          iconName: "icon-feijidongche",
          name: "nonmotorVehicleContent",
        },
        {
          label: "Wi-Fi",
          value: 4,
          iconName: "icon-wifi",
          name: "wifiContent",
        },
        { label: "RFID", value: 5, iconName: "icon-RFID", name: "RFIDContent" },
        {
          label: "电围",
          value: 6,
          iconName: "icon-ZM-dianwei",
          name: "electricContent",
        },
        {
          label: "设备",
          value: 7,
          iconName: "icon-shebeizichan",
          name: "deviceContent",
        },
      ],
    };
  },
  watch: {
    $route: {
      handler(to, from) {
        let { sectionName } = to.query;
        let hasSectionName = this.menuList.some((d) => d.name === sectionName);
        this.sectionName = hasSectionName ? sectionName : this.menuList[0].name;
      },
      immediate: true,
      // 深度观察监听
      deep: true,
    },
  },
  activated() {
    // 用于人像搜索条件显示
    this.$store.commit("common/setPageType", 1);
    this.setLayoutNoPadding(true);
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
  },
  created() {
    this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    pictureSearchHandle() {
      this.visible = !this.visible;
    },
    selectItemHandle(sectionName) {
      const path = this.$route.fullPath;
      this.$router.push({ path, query: { sectionName } });
    },
  },
};
</script>
<style lang="less" scoped>
.my-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
}
.top-operate-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 10px;
  .title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    position: relative;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 20px;
      background: #2c86f8;
      margin-right: 10px;
    }
  }
  .btn-return {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.6);
    .iconfont {
      font-size: 16px;
      margin-right: 8px;
    }
  }
}
.result-content {
  background-color: #fff;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  border-radius: 4px;
  //   overflow: hidden;
  flex: 1;
  height: calc(~"100% - 34px");
  display: flex;
}
</style>
