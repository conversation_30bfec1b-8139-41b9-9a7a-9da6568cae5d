<template>
  <div>
    <Steps>
      <Step
        v-for="(item, index) in stepList"
        :key="index"
        :title="item.title"
        :status="current > index ? 'process' : 'wait'"
      />
    </Steps>
  </div>
</template>
<script>
export default {
  props: {
    current: {
      type: Number,
      default: 1
    },
    stepList: {
      type: Array,
      default () {
        return []
      }
    },
    showTotal: {
      type: Boolean,
      default: true
    }
  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
/deep/ .ivu-steps {
  &-item {
    display: flex;
    justify-content: center;
    flex-direction: column;
    position: relative;
    &:not(:first-child) {
      .ivu-steps-head {
        margin-left: 0;
        padding-left: 0;
      }
    }
  }
  &-tail {
    top: 22px;
    left: 54px;
    right: -10px;
    width: auto;
    & > i {
      background-color: #07355e !important;
      &:after {
        background: #07355e;
      }
    }
  }
  &-main {
    width: 80%;
  }
  &-head {
    background: transparent;
    width: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    &-inner {
      color: #fff;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: transparent;
      border: none;
      margin-right: 0;
      span {
        border: 1px solid #23a8f9;
        color: #23a8f9;
        margin: 0;
        width: 30px;
        height: 30px;
        font-size: 18px;
        line-height: 30px;
        border-radius: 50%;
      }
    }
  }
  &-title {
    background: transparent;
    font-size: 14px;
    width: 64px;
    text-align: center;
    white-space: nowrap;
    padding: 0;
  }
  &-status {
    &-process {
      .ivu-steps-head {
        &-inner {
          background: rgba(35, 168, 249, 0.3);
          span {
            background: #23a8f9;
          }
        }
      }
      .ivu-steps-title {
        color: #fff;
      }
    }
    &-wait {
      .ivu-steps-head {
        &-inner {
          background-color: transparent;
          span {
            color: #23a8f9;
          }
        }
      }
      .ivu-steps-title {
        color: #037cbe;
      }
    }
  }
}
</style>
