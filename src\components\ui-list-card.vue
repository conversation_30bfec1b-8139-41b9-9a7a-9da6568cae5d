<template>
  <!-- 凡是使用了v-html的地方，均是为了适配【全景智搜】高亮的展示效果 -->
  <div
    class="list-card card-box list-card-hover"
    :style="{ backgroundImage: 'url( ' + options[type].cardBg + ')' }"
  >
    <!-- 卡片头部 -->
    <div class="list-card-head">
      <div class="head-left">
        <!-- 卡片image -->
        <!-- 实名档案 | 重点人档案 -->
        <template v-if="['people', 'zdr'].includes(type)">
          <div
            :class="['header-icon', type === 'zdr' ? 'bg-error' : 'bg-primary']"
          >
            <div class="icon-text">身</div>
          </div>
        </template>
        <!-- 视频档案 -->
        <div
          v-else-if="type === 'video' || type === 'prostitute'"
          class="header-icon bg-warning"
        >
          <div class="icon-text">视</div>
        </div>
        <!-- 人员档案-视频档案 -->
        <div v-else-if="type === 'record'" class="header-icon bg-primary">
          <div class="icon-text">档</div>
        </div>
        <!-- 一机一档 -->
        <div v-else-if="type === 'device'" class="header-icon">
          <div class="icon-text">
            <img src="@/assets/img/device/device-icon.png" alt="" />
          </div>
        </div>
        <!-- 场所档案 -->
        <div v-else-if="type === 'place'" class="header-icon bg-blue">
          <div class="icon-text">
            <ui-icon type="hotel" color="#fff"></ui-icon>
          </div>
        </div>
        <!-- 全景智搜 -->
        <div v-else-if="type === 'cloudDevice'" class="header-icon">
          <div class="icon-text">
            <img src="@/assets/img/device/device-icon.png" alt="" />
          </div>
        </div>
        <!-- 其他 -->
        <div v-else class="header-icon bg-primary">
          <i :class="options[type].icon" class="iconfont"></i>
        </div>
        <!-- 卡片title -->
        <div class="head-con">
          <div
            v-if="type === 'people'"
            class="text primary ellipsis"
            v-html="data.gmsfhm"
          ></div>
          <div v-if="type === 'zdr'" class="text primary ellipsis">
            {{ data.gmsfhm || data.archiveNo }}
          </div>
          <div v-if="type === 'record'" class="text primary ellipsis">
            {{ data.id }}
          </div>
          <ui-plate-number
            v-else-if="
              ['vehicle', 'car', 'non-motor-vehicle', 'propertyCard'].includes(
                type
              )
            "
            :plateNo="data.plateNo"
            :color="data.plateColor"
          ></ui-plate-number>
          <div v-else-if="type === 'home'" class="text primary ellipsis"></div>
          <div
            v-else-if="type === 'video'"
            class="text warning ellipsis"
            v-html="data.archiveNo"
          ></div>
          <div v-else-if="type === 'device'" class="text color-green ellipsis">
            {{ data.deviceId }}
          </div>
          <div
            v-else-if="type === 'place'"
            class="text color-blue ellipsis"
            v-html="data.name"
          ></div>
          <div
            v-else-if="type === 'cloudDevice'"
            class="text color-green ellipsis"
            v-html="data.deviceId"
          ></div>
          <div v-else-if="type === 'prostitute'" class="text warning ellipsis">
            {{ data.vid }}
          </div>
        </div>
      </div>
      <div
        v-if="['people', 'zdr', 'video'].includes(type) && isChange"
        :class="{ 'change-btn': true, 'warning-change-btn': type === 'video' }"
        @click.stop="changeHandle"
      >
        <i class="iconfont icon-jiantou_zuoyouqiehuan"></i>
      </div>
    </div>
    <!-- 卡片主要信息 -->
    <div class="list-card-content cursor-p" @click="archivesDetailHandle">
      <div class="list-card-content-body">
        <div class="content-img card-border-color" @click.stop="">
          <div class="collection" v-if="collectIcon">
            <ui-btn-tip
              class="collection-icon"
              v-if="data.myFavorite == '1'"
              content="取消收藏"
              icon="icon-yishoucang"
              transfer
              @click.native="collection(data, 2)"
            />
            <ui-btn-tip
              class="collection-icon"
              v-else
              content="收藏"
              icon="icon-shoucang"
              transfer
              @click.native="collection(data, 1)"
            />
          </div>
          <template
            v-if="
              ['people', 'device', 'record', 'zdr'].includes(type) &&
              data[options[type].photos] &&
              data[options[type].photos].length > 0
            "
          >
            <swiper
              ref="mySwiper"
              :options="swiperOption"
              class="my-swiper"
              :id="`swipe${index + 1}`"
            >
              <swiper-slide
                v-for="(item, index2) in data[options[type].photos]"
                :key="index2"
              >
                <ui-image
                  v-if="type == 'record'"
                  :src="item.storagePath"
                  viewer
                  :type="type"
                ></ui-image>
                <ui-image v-else :src="item.photoUrl" viewer :type="type" />
                <div v-if="item.score && isPercentage" class="similarity">
                  <span>{{ getScore(item.score) }}%</span>
                </div>
              </swiper-slide>
            </swiper>
            <div
              class="swiper-pagination"
              :id="`swipe${index + 1}`"
              :class="
                data[options[type].photos].length < 2
                  ? 'my-pagination-hidden'
                  : ''
              "
            ></div>
          </template>
          <ui-image
            v-else
            viewer
            :type="options[type].pictureType || type"
            :src="data[options[type].photo]"
          />
          <div v-if="data.score" class="similarity">
            <span>{{ getScore(data.score) }}%</span>
          </div>
        </div>
        <div class="content-info">
          <div
            v-for="(item, $index) in options[type].info"
            :key="$index"
            class="info-li"
          >
            <div class="info-name">
              <Tooltip
                :content="Object.values(item)[0]"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont auxiliary-color mr-5" :class="item.icon"></i>
              </Tooltip>
            </div>
            <div class="info-value">
              <!-- 性别 -->
              <div v-if="Object.keys(item)[0] === 'xbdm'" class="ellipsis">
                {{ data[Object.keys(item)[0]] | commonFiltering(genderList) }}
              </div>
              <!-- 民族 -->
              <div v-else-if="Object.keys(item)[0] === 'mzdm'" class="ellipsis">
                {{ data[Object.keys(item)[0]] | commonFiltering(nationList) }}
              </div>
              <!-- 车辆类型 -->
              <div
                v-else-if="Object.keys(item)[0] === 'vehicleType'"
                class="ellipsis"
              >
                {{
                  data[Object.keys(item)[0]] | commonFiltering(vehicleTypeList)
                }}
              </div>
              <div
                v-else-if="Object.keys(item)[0] === 'nonVehicleType'"
                class="ellipsis"
              >
                {{
                  data[Object.keys(item)[2]]
                    | commonFiltering(nonmotorVehicleTypeList)
                }}
              </div>
              <!-- 驾乘方式 -->
              <div
                v-else-if="Object.keys(item)[0] === 'driverFlag'"
                class="ellipsis"
              >
                {{
                  data[Object.keys(item)[0]] | commonFiltering(driverFlagList)
                }}
              </div>
              <!-- 车身颜色 -->
              <div
                v-else-if="Object.keys(item)[0] === 'jdccsysdm'"
                class="ellipsis"
              >
                {{
                  data[Object.keys(item)[0]] | commonFiltering(bodyColorList)
                }}
              </div>
              <!-- 摄像机功能类型 -->
              <div
                v-else-if="Object.keys(item)[0] === 'sbgnlx'"
                :title="
                  data[Object.keys(item)[0]] | commonFiltering(sbgnlxList)
                "
                class="ellipsis"
              >
                {{ data[Object.keys(item)[0]] | commonFiltering(sbgnlxList) }}
              </div>
              <!-- 云搜设备名称 -->
              <div
                v-else-if="
                  Object.keys(item)[0] === 'deviceName' &&
                  type === 'cloudDevice'
                "
                :title="
                  data[Object.keys(item)[0]]
                    ? data[Object.keys(item)[0]].replace(/(<\/?span.*?>)/gi, '')
                    : ''
                "
                v-html="data[Object.keys(item)[0]]"
                class="ellipsis"
              ></div>
              <div
                v-else
                :title="type !== 'place' ? data[Object.keys(item)[0]] : ''"
                v-html="data[Object.keys(item)[0]]"
                :class="
                  Object.keys(item)[0] === 'propertyAddress' ||
                  Object.keys(item)[0] === 'address' ||
                  Object.keys(item)[0] === 'jgDzmc' ||
                  Object.keys(item)[0] === 'detailAddress'
                    ? 'two-ellipsis'
                    : 'ellipsis'
                "
              ></div>
              <!-- {{ data[Object.keys(item)[0]] }} -->
            </div>
          </div>
          <!-- 视频已置信标识 -->
          <p
            v-if="data.realNameArchiveNo || !!data.idNumber"
            class="believed-flag"
          >
            已置信
          </p>
          <p v-if="data.registerFlag == 1" class="believed-flag">已登记</p>
        </div>
      </div>
      <ui-tag-poptip
        v-if="data.labels && data.labels.length !== 0 && labelsObj"
        :data="data.labels"
      />
    </div>
    <!-- 卡片操作 -->
    <div class="operate-bar" v-if="showBar">
      <p class="operate-content">
        <ui-btn-tip
          content="档案"
          icon="icon-dangan2"
          @click.native.stop="archivesPage(data)"
        />
        <!-- <ui-btn-tip v-if="data.myFavorite == '1'" content="收藏" icon="icon-yishoucang" transfer @click.native.stop="collection(data, 2)" />
                <ui-btn-tip v-else content="收藏" icon="icon-shoucang" transfer @click.native.stop="collection(data, 1)" /> -->
        <ui-btn-tip
          v-if="type != 'vehicle'"
          content="以图搜图"
          icon="icon-fenxi"
          @click.native.stop="handleSearchBar(data)"
        />
        <ui-btn-tip
          v-if="type != 'vehicle'"
          content="布控"
          icon="icon-dunpai"
          transfer
          @click.native.stop=""
        />
      </p>
    </div>
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import UiImages from "@/components/ui-images.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { getVehicleBaseInfoByplateNo } from "@/api/vehicleArchives";

export default {
  components: { swiper, swiperSlide, UiImages },
  props: {
    type: {
      type: String,
      default: "people",
    },
    data: {
      type: Object,
      default: () => {},
    },
    // 是否显示切换按钮
    isChange: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number,
      default: 0,
    },
    // 是否显示bar
    showBar: {
      type: Boolean,
      default: false,
    },
    // 是否显示收藏图标
    collectIcon: {
      type: Boolean,
      default: true,
    },
    // 是否显示百分比
    isPercentage: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      options: {
        people: {
          gmsfhm: "",
          photos: "photos",
          cardBg: require("@/assets/img/card-bg/idcard.png"),
          info: [
            { xm: "姓名", icon: "icon-shenfenzheng" },
            { xbdm: "性别", icon: "icon-xingbie2" },
            { mzdm: "民族", icon: "icon-minzu1" },
            { jgDzmc: "户籍", icon: "icon-location" },
          ],
          key: "photo",
          lables: "标签",
        },
        record: {
          gmsfhm: "",
          photos: "photos",
          cardBg: require("@/assets/img/card-bg/idcard.png"),
          info: [
            { profileId: "文件", icon: "icon-juhe" },
            { lastCaptureTime: "时间", icon: "icon-time" },
            { deviceAddress: "地址", icon: "icon-location" },
          ],
          key: "photo",
          lables: "标签",
        },
        zdr: {
          gmsfhm: "",
          photos: "photos",
          cardBg: require("@/assets/img/card-bg/idcard.png"),
          info: [
            { xm: "姓名", icon: "icon-shenfenzheng" },
            { xbdm: "性别", icon: "icon-xingbie2" },
            { mzdm: "民族", icon: "icon-minzu1" },
            { jgDzmc: "户籍", icon: "icon-location" },
          ],
          key: "photo",
          lables: "标签",
        },
        // 名下资产的车辆不显示标签
        car: {
          plateNo: "车牌号码",
          //   photo: 'photoUrl',
          photo: "sceneImg",
          icon: "icon-qiche",
          cardBg: require("@/assets/img/card-bg/car.png"),
          info: [
            // { vehicleType: '车辆类型', icon: 'icon-leixing' },
            // { vehicleBrandCN: '车辆品牌', icon: 'icon-gongjuxiang' },
            // { jdccsysdm: '车身颜色', icon: 'icon-yanse' },
            // { djsj: '注册时间', icon: 'icon-time' },
            { driverFlag: "驾乘方式", icon: "icon-leixing" },
            { absTime: "最近时间", icon: "icon-time" },
            { captureAddress: "最近地点", icon: "icon-location" },
          ],
        },
        // 列表的车辆显示标签 - （一车一档）
        vehicle: {
          carId: "苏A 29999",
          icon: "icon-qiche",
          photo: "photoUrl",
          zp: "照片",
          cardBg: require("@/assets/img/card-bg/car.png"),
          info: [
            // { ownerName: '车主', icon: 'icon-xingming' },
            { vehicleBrandCN: "品牌", icon: "icon-gongjuxiang" },
            { vehicleType: "类型", icon: "icon-leixing" },
            { lastCaptureTime: "最近抓拍时间", icon: "icon-time" },
            { lastSanpAddress: "最近抓拍地点", icon: "icon-location" },
          ],
          lables: "标签",
        },
        // 名下资产车辆
        propertyCard: {
          carId: "苏A 29999",
          icon: "icon-qiche",
          photo: "photoUrl",
          pictureType: "vehicle",
          zp: "照片",
          cardBg: require("@/assets/img/card-bg/car.png"),
          info: [
            // { ownerName: '车主', icon: 'icon-xingming' },
            { vehicleBrandCN: "品牌", icon: "icon-gongjuxiang" },
            { vehicleType: "类型", icon: "icon-leixing" },
            { djsj: "登记时间", icon: "icon-time" },
            { vehicleLocation: "登记地点", icon: "icon-location" },
          ],
        },
        "non-motor-vehicle": {
          carId: "苏A 29999",
          icon: "icon-diandongche",
          photo: "traitImg",
          zp: "照片",
          cardBg: require("@/assets/img/card-bg/no-motor-vehicle.png"),
          info: [
            // { ownerName: '车主', icon: 'icon-xingming' },
            // { vehicleBrandCN: '品牌', icon: 'icon-gongjuxiang' },
            {
              nonVehicleType: "类型",
              icon: "icon-leixing",
              vehicleType: "类型",
            },
            { absTime: "最近抓拍时间", icon: "icon-time" },
            { lastSanpAddress: "最近抓拍地点", icon: "icon-location" },
          ],
          lables: "标签",
        },
        home: {
          icon: "icon-fangzi",
          photo: "housePic",
          cardBg: require("@/assets/img/card-bg/home.png"),
          info: [
            { fcxz: "房屋性质", icon: "icon-home1" },
            { cqrXm: "房主信息", icon: "icon-xingming" },
            { djsj: "登记时间", icon: "icon-time" },
            { fcdzDzmc: "房产地址", icon: "icon-location" },
          ],
        },
        video: {
          vid: "320102199003079556",
          photo: "photo",
          cardBg: require("@/assets/img/card-bg/video.png"),
          info: [
            { clusteringImagesNum: "聚类图像数量", icon: "icon-juhe" },
            { recentCaptureTime: "最近抓拍时间", icon: "icon-time" },
            { recentCaptureLocation: "最近抓拍地点", icon: "icon-location" },
          ],
          lables: [
            { name: "常住人员常住人员常住人员常住", color: "#1FAF8A" },
            { name: "常住人员", color: "#F8775C" },
            { name: "常住", color: "#E99E53" },
            { name: "常", color: "#1FAF8A" },
          ],
        },
        device: {
          cardBg: require("@/assets/img/card-bg/device.png"),
          photos: "imageUrls",
          info: [
            { deviceName: "设备名称", icon: "icon-camera" },
            { sbgnlx: "功能类型", icon: "icon-leixing" },
            { detailAddress: "安装地址", icon: "icon-location" },
          ],
          lables: [
            { name: "常用设备", color: "#1FAF8A" },
            { name: "重点区域", color: "#F8775C" },
            { name: "机构单位", color: "#E99E53" },
            { name: "常用设备", color: "#1FAF8A" },
          ],
          key: "photo",
        },
        place: {
          cardBg: require("@/assets/img/card-bg/place.png"),
          photo: "image",
          info: [
            { firstLevelName: "场所类型", icon: "icon-leixing" },
            { adname: "地区", icon: "icon-quxian" },
            { address: "地址", icon: "icon-location" },
          ],
        },
        cloudDevice: {
          cardBg: require("@/assets/img/card-bg/device.png"),
          photo: "imageUrls",
          info: [
            { deviceName: "设备名称", icon: "icon-camera" },
            { sbgnlx: "功能类型", icon: "icon-leixing" },
            { detailAddress: "安装地址", icon: "icon-location" },
          ],
          lables: [
            { name: "常用设备", color: "#1FAF8A" },
            { name: "重点区域", color: "#F8775C" },
            { name: "机构单位", color: "#E99E53" },
            { name: "常用设备", color: "#1FAF8A" },
          ],
          key: "photo",
        },
        prostitute: {
          photo: "faceImg",
          cardBg: require("@/assets/img/card-bg/video.png"),
          info: [
            { lastCaptureTime: "最近抓拍时间", icon: "icon-time" },
            { lastDeviceName: "最近抓拍地点", icon: "icon-location" },
            { appearRegionCount: "出现区域", icon: "icon-guiji" },
          ],
        },
      },
      swiperOption: {
        direction: "horizontal",
      },
    };
  },
  computed: {
    ...mapGetters({
      genderList: "dictionary/getGenderList", //性别
      nationList: "dictionary/getNationList", //民族
      vehicleClassTypeList: "dictionary/getVehicleClassTypeList", //车牌类型
      bodyColorList: "dictionary/getLicensePlateColorList", //车辆颜色
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      nonmotorVehicleTypeList: "dictionary/getNonmotorVehicleType", //非机动车车辆类型
      sbgnlxList: "dictionary/getSbgnlxList", //摄像机功能类型
      driverFlagList: "dictionary/getDriverFlagList",
      labelsObj: "systemParam/labelsObj",
    }),
  },
  watch: {
    data: {
      handler(val) {
        let arr = [];
        if (val.photos == null) {
          val.photos = [];
        }
        if (val.photos && val.photos.length > 0) {
          arr = val.photos;
        }
        if (val.imageUrls && val.imageUrls.length > 0) {
          arr = val.imageUrls;
        }
        this.swiperOption.pagination = {
          el:
            arr.length > 1
              ? `#swipe${this.index + 1}` + ".swiper-pagination"
              : null, //控制分页显示隐藏
          clickable: true, //点击切换
        };
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },
    // 卡片切换
    changeHandle() {
      this.$emit("on-change", "");
    },
    // 详情跳转
    archivesDetailHandle() {
      this.$emit("archivesDetailHandle", this.data);
    },
    /**
     * 收藏
     */
    collection(data, flag) {
      var type = null;
      var id = null;
      switch (this.type) {
        case "people":
          type = 1;
          id = data.archiveNo;
          break;
        case "video":
          type = 2;
          id = data.archiveNo;
          break;
        case "vehicle":
          type = 3;
          id = data.archiveNo;
          break;
        case "device":
          type = 4;
          id = data.deviceId;
          break;
        case "zdr":
          type = 19;
          id = data.archiveNo;
          break;
        case "non-motor-vehicle":
          type = 18;
          id = data.archiveNo;
          break;
        // 场所
        case "place":
          type = 20;
          id = data.id;
        default:
      }
      console.log("---", this.type, data);
      var param = {
        favoriteObjectId: id,
        favoriteObjectType: type,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$set(data, "myFavorite", "1"); // 用于不让页面刷新
          this.$emit("collection");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$set(data, "myFavorite", "0");
          this.$emit("collection");
        });
      }
    },
    // 以图搜图
    handleSearchBar(row) {},
    /**
     * 跳转到一车一档页面
     */
    archivesPage(row) {
      getVehicleBaseInfoByplateNo(row.plateNo).then((res) => {
        if (res.data.archiveNo) {
          const { href } = this.$router.resolve({
            name: "vehicle-archive",
            query: {
              archiveNo: JSON.stringify(res.data.archiveNo),
              plateNo: JSON.stringify(row.plateNo),
              source: "car",
            },
          });
          window.open(href, "_blank");
        } else {
          this.$Message.error("尚未查询到该辆车的档案信息");
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.list-card {
  width: 316px;
  // height: 166px;
  background-repeat: no-repeat;
  background-position: bottom right;
  border-radius: 4px;
  .list-card-head {
    height: 30px;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: space-between;
    overflow: hidden;
    border-top-right-radius: 4px;
    .head-left {
      display: flex;
      align-items: center;
      overflow: hidden;
      .header-icon {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        margin-right: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        .iconfont {
          font-size: 14px;
          line-height: 20px;
        }
        .icon-text {
          font-size: 12px;
          line-height: 12px;
          img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .head-con {
        display: flex;
        flex: 1;
        align-items: center;
        overflow: hidden;
      }
      .text {
        font-size: 16px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        line-height: 22px;
      }
    }
    .change-btn {
      width: 40px;
      height: 100%;
      margin-right: -18px;
      transform: skewX(-18deg);
      display: flex;
      align-items: center;
      padding-left: 8px;
      box-sizing: border-box;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
  .list-card-content {
    padding: 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    .list-card-content-body {
      display: flex;
      .content-img {
        width: 116px;
        height: 116px;
        border: 1px solid #fff;
        position: relative;
        .swiper-container {
          width: 100%;
          height: 100%;
          .swiper-wrapper .swiper-slide {
            width: 116px;
            height: 116px;
            text-align: center;
            & > img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              cursor: pointer;
            }
          }
        }
        .swiper-pagination {
          bottom: 0px;
          width: 100%;
        }
        /deep/.swiper-pagination-bullet {
          width: 6px;
          height: 6px;
          margin-left: 5px;
          background: #fff;
          opacity: 1;
        }
        /deep/.swiper-pagination-bullet-active {
          background: #2c86f8;
        }
        .similarity {
          position: absolute;
          left: 0;
          top: 0;
          span {
            padding: 2px 5px;
            background: #4597ff;
            color: #fff;
          }
        }
        .video-icon {
          position: absolute;
          top: -3px;
          right: 0;
          .video-icon-top {
            width: 24px;
            height: 4px;
            background: #ffbd7a;
            transform: skewX(-42deg);
            transform-origin: left top;
          }
          .video-icon-top::after {
            content: "";
            position: absolute;
            top: 0.5px;
            right: -1px;
            width: 0;
            height: 0;
            border-bottom: 2px solid #cf7820;
            border-left: 2px solid transparent;
            transform: rotate(-50deg);
          }
          .video-icon-bottom {
            color: #fff;
            font-size: 12px;
            line-height: 16px;
            background: #f29f4c;
            width: 24px;
            height: 16px;
            text-align: center;
            position: absolute;
            left: -3px;
            top: 3px;
          }
          .video-icon-bottom::before {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-right: 12px solid transparent;
            position: absolute;
            left: 0;
            top: 16px;
          }
          .video-icon-bottom::after {
            content: "";
            width: 0;
            height: 0;
            border-top: 6px solid #f29f4c;
            border-left: 12px solid transparent;
            position: absolute;
            right: 0;
            top: 16px;
          }
        }
      }
      .content-info {
        width: 170px;
        padding-left: 10px;
        flex: 1;
        box-sizing: border-box;
        position: relative;
        .info-li {
          display: flex;
          // align-items: center;
          margin-bottom: 5px;
          .info-name {
            font-size: 12px;
            line-height: 18px;
            color: #181818;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            white-space: nowrap;
          }
          .info-value {
            font-size: 14px;
            line-height: 20px;
            color: #484847;
            width: 180px;
          }
        }
        .believed-flag {
          position: absolute;
          right: -10px;
          top: -5px;
          width: 64px;
          height: 24px;
          text-align: center;
          line-height: 24px;
          color: #fff;
          background: linear-gradient(144deg, #3cd2aa 0%, #1faf8a 100%);
          border-radius: 100px 0px 0px 100px;
        }
      }
    }
  }
}

.list-card {
  position: relative;
  background-color: #f9f9f9;
  margin-bottom: 10px;
  height: calc(~"100% - 10px");
  overflow: hidden;
  box-sizing: border-box;
  .operate-bar {
    height: 30px;
    background: linear-gradient(
      90deg,
      rgba(87, 187, 252, 0.8) 0%,
      #2c86f8 100%
    );
    border-radius: 0px 0px 4px 0px;
    position: absolute;
    right: -100%;
    transition: all 0.3s;
    bottom: 0;
    transform: skewX(-20deg);
    .operate-content {
      padding: 0 5px;
      transform: skewX(20deg);
      height: 100%;
      display: flex;
      align-items: center;
      color: #fff;
      /deep/ .ivu-tooltip-rel {
        padding: 6px;
      }
    }
  }
  &:hover {
    border: 1px solid #2c86f8;
    // padding: 9px;
    .operate-bar {
      right: -6px;
      bottom: -1px;
    }
    &:before {
      border-color: #2c86f8;
    }
    .img-content {
      .check-box {
        display: inline-block;
      }
    }
  }
}

.collection {
  width: 30px;
  height: 30px;
  position: absolute;
  // background: #F29F4C;
  z-index: 99;
  top: 3px;
  right: 3px;
  .bg {
    width: 0px;
    height: 0px;
    border-left: 30px solid transparent;
    border-top: 30px solid #f29f4c;
  }
  .collection-icon {
    position: absolute;
    top: -1px;
    right: 1px;
    /deep/ .iconfont {
      font-size: 14px;
      color: #fff;
    }
    .ivu-tooltip-rel {
      margin-top: 3px;
    }
    /deep/ .icon-shoucang {
      color: #888888 !important;
      text-shadow: 0px 1px 0px #e1e1e1;
    }
    /deep/ .icon-yishoucang {
      color: #f29f4c !important;
    }
  }
}
</style>
