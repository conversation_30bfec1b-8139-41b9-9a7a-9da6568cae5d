<template>
  <!--车辆详情 -->
  <div class="carInfo" ref="contentScroll">
    <div class="information-header">
      <!-- 统计 -->
      <carUrlStatistics
        v-if="[3010, 3011, 3024].includes(indexId)"
        :statistics-list="statisticsList"
        :isflexfix="true"
      ></carUrlStatistics>
      <carStatistics v-else :statistics-list="statisticsList" :isflexfix="true" :indexId="indexId"></carStatistics>
      <!-- 排行 -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                  item.rank
                }}</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class="" v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                  <img class="" v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                  <img class="" v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="item.rank == 1">{{ item.regionName }}</span>
                  <span v-if="item.rank == 2">{{ item.regionName }}</span>
                  <span v-if="item.rank == 3">{{ item.regionName }}</span>
                  <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                    item.regionName
                  }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
              </div>

              <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                  item.rankRise || 0
                }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- 模式切换 ---------------------------------------------------------------------------------------------------- -->
    <div class="abnormal-title flex-row">
      <div>
        <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
        <span class="f-16 color-filter ml-sm">检测结果详情</span>
      </div>
      <tagView class="tagView" ref="tagView" :list="['设备模式', '图片模式']" @tagChange="tagChange1" />
    </div>
    <!-- 设备模式 ---------------------------------------------------------------------------------------------------- -->
    <TableList
      v-if="modelTag == 0 && columns.length > 0"
      ref="infoList"
      class="auto-fill"
      :columns="columns"
      :loadData="loadDataList"
      :contentClientHeight="contentClientHeight"
    >
      <!-- 检索 -->
      <div slot="search" class="hearder-title">
        <SearcList :exportLoading="exportLoading" ref="search" @startSearch="startSearch" @getExport="getExport" />
      </div>
      <!-- 表格操作 -->
      <template #qualifiedRate="{ row }">
        <span>{{ row.qualifiedRate || 0 }}%</span>
      </template>
      <template #outcome="{ row }">
        <span
          class="check-status"
          :title="row.description"
          :class="[
            row.outcome === '1' ? 'bg-success' : '',
            row.outcome === '2' ? 'bg-failed' : '',
            row.outcome === '3' ? 'bg-D66418' : '',
          ]"
        >
          {{ handleCheckStatus(row.outcome) }}
        </span>
        <span
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
          class="ml-sm"
          v-if="row.dataMode === '3'"
        >
          (人工)
        </span>
      </template>
      <template #action="{ row }">
        <ui-btn-tip
          icon="icon-chakantupian"
          content="查看图片"
          @click.native="checkImg(row)"
          class="mr-sm"
        ></ui-btn-tip>
        <ui-btn-tip
          v-if="[3010, 3011, 3024].includes(indexId) && !isFormCascade()"
          icon="icon-rengongfujian"
          content="人工复核"
          @click.native="artificialReview(row)"
          v-permission="{
            route: $route.name,
            permission: 'artificialreviewr',
          }"
        ></ui-btn-tip>
      </template>
      <!-- 点位类型 -->
      <template #sbdwlx="{ row }">
        <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
      </template>
    </TableList>
    <!-- 图片模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard
      ref="infoCard"
      class="card-list auto-fill"
      :loadData="loadDataCard"
      :cardInfo="cardInfo"
      v-if="modelTag == 1"
    >
      <div slot="search" class="hearder-title">
        <SearchCard
          ref="search"
          :resultId="{
            indexId: paramsList.indexId,
          }"
          :check-data="checkList"
          :cardSearchList="cardSearchList"
          @startSearch="startSearch"
        />
        <Button type="primary" class="btn_search" @click="getSecondExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs f-14">导出</span>
        </Button>
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard
          v-if="cardInfo.length > 0 && [3001, 3003, 3006, 3007].includes(indexId)"
          class="card"
          :list="row"
          :cardInfo="cardInfo"
          @bigImageUrl="bigImageUrl"
        ></UiGatherCard>
        <UiGatherCard1
          v-else-if="cardInfo.length > 0 && [3002].includes(indexId)"
          class="card2"
          :list="row"
          :cardInfo="cardInfo"
          @bigImageUrl="bigImageUrl"
        ></UiGatherCard1>
        <InfoCard
          :paramsData="paramsList"
          v-else
          class="card1"
          :list="row"
          :cardInfo="cardInfo"
          @bigImageUrl="bigImageUrl"
          :check-data="checkList"
          @recount="recount"
        >
        </InfoCard>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- 检测详情 ---------------------------------------------------------------------------------------------------- -->
    <CheckPicture
      v-model="checkPicture"
      :list="currentRow"
      :tagList="cardSearchList"
      :cardInfo="cardInfo"
      :paramsList="paramsList"
      :check-data="checkList"
      @recount="recount"
    >
    </CheckPicture>
    <!-- 人工复核 -->
    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <div class="artificial-data">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{ item.key }}</Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block mt-sm" label="" :width="0">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>
      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { mapActions, mapGetters } from 'vuex';
import carFieldData from './carFields';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';

export default {
  mixins: [downLoadTips],
  name: 'carVehicle',
  props: {
    indexName: {
      type: String,
      default: '',
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      indexId: null, //指标
      exportLoading: false,
      checkPicture: false, //查看图片
      currentRow: {},
      bigPictureShow: false, // 大图展示
      imgList: [], // 大图图片
      rankLoading: false,
      rankData: [],
      paramsList: {},
      abnormalCountMa: [],
      modelTag: 0, // 聚档模式,图像模式
      searchData: {},
      minusHeight: 488, // 表格
      addColumns: [],
      columns: [], // 表头
      cardInfo: [], // 卡片展示参数，
      cardSearchList: [], // 卡片下拉框参数
      statisticsList: [], //统计列表
      statisticalList: {}, //统计值
      time: '',
      column: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 190,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '组织机构',
          key: 'deviceOrgCodeName',
          tooltip: true,
          width: 120,
        },
        { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlx', width: 100 },
        { title: '检测图片数量', key: 'testPictureCount', width: 100 },
      ],
      statis: [
        {
          name: '车辆卡口总量',
          value: 0,
          icon: 'icon-cheliangkakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'total',
          textColor: 'color1',
        },
        {
          name: '检测设备数量',
          value: 0,
          icon: 'icon-jianceshebeishuliang1',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'deviceDataTotal',
          textColor: 'color2',
        },
        {
          name: '检测合格设备数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          key: 'passDeviceDataTotal',
          textColor: 'color9',
        },
        {
          name: '检测不合格设备数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'notPassDeviceDataTotal',
          textColor: 'color4',
        },
        {
          name: '检测图片数量',
          value: 0,
          icon: 'icon-tupianzhiliangjiance',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'faceDataTotal',
          textColor: 'color6',
        },
      ],
      // 设备模式接口
      loadDataList: (parameter) => {
        return superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          {
            pageNumber: parameter.params.pageNumber,
            pageSize: parameter.params.pageSize,
            orgRegionCode: this.paramsList.orgRegionCode,
            indexId: this.indexId,
            batchId: this.paramsList.batchId,
            displayType: this.paramsList.displayType,
            customParameters: this.searchData,
          },
          'post',
          this.$route.query.cascadeId,
          {},
        ).then((res) => {
          return res.data;
        });
      },
      // 卡片接口
      loadDataCard: (parameter) => {
        return superiorinjectfunc(
          this,
          evaluationoverview.getPolyData,
          {
            pageNumber: parameter.params.pageNumber,
            pageSize: parameter.params.pageSize,
            indexId: this.indexId,
            batchId: this.paramsList.batchId,
            orgRegionCode: this.paramsList.orgRegionCode,
            displayType: this.paramsList.displayType,
            customParameters: this.searchData,
          },
          'post',
          this.$route.query.cascadeId,
          {},
        ).then((res) => {
          return res.data;
        });
      },
      contentClientHeight: 0,
      //人工复核
      artificialStyles: {
        width: '3rem',
      },
      qualifiedList: [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ],
      artificialData: { qualified: '1', reason: '', list: [] },
      artificialVisible: false,
      artificialRow: {},
      checkList: [],
    };
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData(); // 点位类型
    if (this.colorType.length == 0) {
      this.getcolorType();
    }
    if (this.vehicleBandType.length == 0) {
      this.getvehicleBandType();
    }
    if (this.vehicleClassType.length == 0) {
      this.getvehicleClassType();
    }
    if (this.plateClassType.length == 0) {
      this.getplateClassType();
    }
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
  },
  methods: {
    isFormCascade() {
      //级联清单 跳转 不需要 人工复核
      return this.$route.name === 'cascadelist';
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法检测',
      };
      return flag[row];
    },
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialRow = row;
      this.artificialVisible = true;
    },
    async getUrlIndexErrorCode() {
      try {
        let res = await this.$http.get(evaluationoverview.getUrlIndexErrorCode);
        this.checkList = res.data.data || [];
      } catch (error) {
        console.log(error);
      }
    },
    recount() {
      this.$emit('update');
    },
    async artificial() {
      let data = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
          type: 'device',
        },
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.artificialUpdate();
        this.artificialVisible = false;
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    // 人工复核更成功后更新表格数据不跳转页码
    artificialUpdate() {
      try {
        this.modelTag = 0;
        this.$nextTick(() => {
          if (this.$refs.tagView) {
            this.$refs.tagView.curTag = 0;
          }
          this.$refs.infoList.handleUpdateArtificialTable();
          if (this.indexId !== this.$route.query.indexId) {
            this.$refs.search.reashfalf();
          }
        });
      } catch (e) {
        console.log(e);
      }
    },
    // 初始化
    init() {
      this.modelTag = 0;
      this.infoData();
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
        this.$refs.infoList.info(true);
        if (this.indexId !== this.$route.query.indexId) {
          this.$refs.search.reashfalf();
        }
      });
    },
    ...mapActions({
      getcolorType: 'algorithm/getcolorType',
      getvehicleBandType: 'algorithm/getvehicleBandType',
      getvehicleClassType: 'algorithm/getvehicleClassType',
      getplateClassType: 'algorithm/getplateClassType',
      getAlldicData: 'algorithm/getAlldicData', // 点位类型
    }),
    // 导出
    async getExport() {
      this.exportLoading = true;
      let params = {
        orgRegionCode: this.paramsList.orgRegionCode,
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        displayType: this.paramsList.displayType,
        customParameters: this.searchData,
      };
      try {
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        this.exportLoading = false;
      }
    },
    async getSecondExport() {
      try {
        this.exportLoading = true;
        let params = {
          orgRegionCode: this.paramsList.orgRegionCode,
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          displayType: this.paramsList.displayType,
          customParameters: this.searchData,
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportSecondModelData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportSecondModelData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 检测详情
    checkImg(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    //统计
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
    // 判断当前指标改变参数  卡片样式不同分为两个组件处理
    infoData() {
      this.indexId = Number(this.indexId);
      // const importantTypes = ['VEHICLE_FULL_INFO_IMPORTANT', 'VEHICLE_MAIN_PROP', 'VEHICLE_TYPE_PROP', 'VEHICLE_UPLOAD_IMPORTANT', 'VEHICLE_URL_AVAILABLE_IMPORTANT']
      const importantIds = [3002, 3006, 3007, 3009, 3011];
      if (importantIds.includes(this.indexId)) {
        this.statis[0].name = '重点车辆卡口总量';
      } else {
        this.statis[0].name = '车辆卡口总量';
      }
      const filedData = this.$util.common.deepCopy(carFieldData.find((item) => item.indexId.includes(this.indexId)));
      this.abnormalCountMa = filedData.abnormalCountMa;
      this.statisticsList = [...this.statis, ...filedData.abnormalCountMa];
      this.cardSearchList = filedData.cardSearchList;
      this.cardInfo = filedData.cardInfo;
      this.columns = [...this.column, ...filedData.addColumns];
      // 车辆卡口设备及时上传率、重点车辆卡口设备及时上传率等指标需要对表格columns 【 uploadTimeOutCount 】列进行处理
      const dealColumnIds = [3008, 3009];
      if (dealColumnIds.includes(this.indexId)) {
        this.columns = this.columns.map((item) => {
          if (item.key === 'uploadTimeOutCount') {
            this.$set(item, 'title', `上传超时(${this.time || 0})图片数量`);
          }
          return item;
        });
      }
    },
  },
  computed: {
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.time = val.time;
          if (this.columns.length != 0) {
            for (let i of this.columns) {
              if (i.key === 'uploadTimeOutCount') {
                this.$set(i, 'title', `上传超时(${this.time || 0})图片数量`);
              }
            }
          }
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        this.rankData = val;
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;
          this.indexId = val.indexId;
          if (this.indexId != this.$route.query.indexId) {
            this.$nextTick(() => {
              this.$refs.search.reashfalf();
            });
          }
          this.init();
          this.getUrlIndexErrorCode();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    CheckPicture: require('./component/check-picture.vue').default,
    carUrlStatistics: require('./component/car-url-statistics.vue').default,
    carStatistics: require('./component/car-statistics.vue').default,
    TableList: require('@/views/appraisaltask/inspectionrecord/components/tableList.vue').default,
    TableCard: require('@/views/appraisaltask/inspectionrecord/components/tableCard.vue').default,
    tagView: require('@/views/appraisaltask/inspectionrecord/components/tags').default,
    SearchCard: require('./component/searchCard.vue').default,
    SearcList: require('./component/searchList.vue').default,
    UiGatherCard: require('./component/ui-gather-card.vue').default,
    UiGatherCard1: require('./component/ui-gather-card1.vue').default,
    InfoCard: require('./component/infoCard.vue').default,
    LookScene: require('@/components/look-scene').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }

  // @{_deep} .ivu-table {
  //   &-tip,
  //   &-overflowX,
  //   &-body {
  //     min-height: 290px !important;
  //   }
  // }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 460px !important;
  }
  .desc {
    margin-left: 80px;
    width: 80%;
  }
  .artificial-data {
    padding: 0 50px;
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;

    .information-statistics {
      display: flex;
      width: calc(100% - 370px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }

    .information-ranking {
      width: 370px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;

      .ranking-title {
        height: 30px;
        text-align: center;
      }

      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;

        ul {
          width: 100%;
          height: 100%;

          li {
            display: flex;
            padding-top: 15px;
            align-items: center;

            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }

            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }

            .content-thirdly {
              justify-content: center;
              flex: 1;
            }

            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }

            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }

              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }

            .firstly1 {
              background-color: #f1b700;
            }

            .firstly2 {
              background-color: #eb981b;
            }

            .firstly3 {
              background-color: #ae5b0a;
            }

            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }

            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }

            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .abnormal-title {
    margin-top: 10px;
    padding-right: 2px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }

  .hearder-title {
    color: #fff;
    margin-top: 10px;
    //padding: 0 10px;
    font-size: 14px;
    position: relative;

    .btn_search {
      position: absolute;
      right: 0px;
      top: 0px;
    }
  }

  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
    padding-bottom: 8px;
  }

  .card1 {
    width: calc(calc(100% - 72px) / 7);
    margin: 0 5px 10px;
  }

  .card2 {
    width: calc(calc(100% - 40px) / 3);
    margin: 0 5px 10px;
  }

  /deep/ .ivu-table-body .font-red {
    color: #bc3c19 !important;
  }
}
</style>
