<template>
  <div class="video-play">
    <div class="video-left mr-10">
      <div class="title">设备列表</div>
      <div class="search-top">
        <Input
          placeholder="请输入"
          v-model="searchInput"
          @keydown.enter.native="searchName()"
        >
          <Icon
            type="ios-search"
            class="font-16 cursor-p"
            slot="suffix"
            maxlength="50"
            @click.prevent="searchName()"
          />
        </Input>
      </div>
      <div class="label">
        共<span class="color-blue">{{ deviceList.length }}</span
        >个摄像机
      </div>
      <div class="deviceList">
        <div
          v-for="(item, index) in deviceList"
          :key="index"
          @dblclick="handleNodeClick(item)"
        >
          <span
            class="node-title"
            :class="{ playing: playingDeviceGbIds.includes(item.deviceGbId) }"
          >
            <!-- 在线状态0：在线，1：离线 -->
            <span :class="{ offline: item.Status == '1' }">
              <i
                v-if="playingDeviceGbIds.includes(item.deviceGbId)"
                class="playing-icon"
              ></i>
              <i
                v-else
                class="iconfont color-blue mr-5"
                :class="
                  item.LayerType == 'Camera_QiuJi'
                    ? 'icon-qiuji'
                    : 'icon-shebeizichan'
                "
              ></i>
              <span :title="item.deviceName" class="ellipsis name-width">{{
                item.deviceName
              }}</span>
            </span>
          </span>
        </div>
      </div>
    </div>
    <div class="right-content">
      <div class="top">
        <zi-player ref="ziPlayer" @changeWinList="getWinList"></zi-player>
      </div>
    </div>
    <div class="toolbar" :class="{ 'toolbar-hide': !isShowToolBar }">
      <div
        class="toggle"
        :class="{ hide: !isShowToolBar }"
        @click="isShowToolBar = !isShowToolBar"
      >
        <Tooltip
          :always="!isShowToolBar"
          content="工具栏"
          placement="bottom"
          max-width="18"
        >
          <i class="iconfont icon-doubleright"></i>
        </Tooltip>
      </div>
      <div class="toolbar-content">
        <span :style="{ 'white-space': 'nowrap' }">{{
          !inspectObj.inspecting
            ? "轮巡设置"
            : inspectObj.parse
            ? "轮巡暂停"
            : "轮巡中"
        }}</span>
        <Button
          v-if="inspectObj.inspecting && !inspectObj.parse"
          type="primary"
          size="small"
          @click="parseInspect"
          >暂停轮巡</Button
        >
        <Button
          v-if="inspectObj.inspecting && inspectObj.parse"
          type="primary"
          size="small"
          @click="parseInspect"
          >继续轮巡</Button
        >
        <Button
          v-if="inspectObj.inspecting"
          type="primary"
          size="small"
          @click="stopInspect"
          >停止轮巡</Button
        >
        <Select
          v-if="!inspectObj.inspecting"
          size="small"
          v-model="currentLayout"
          @on-change="changeLayout($event)"
          :style="{ width: '100px' }"
        >
          <Option
            :value="item.value"
            v-for="(item, index) in layoutOptions"
            :key="index"
            >{{ item.label }}</Option
          >
        </Select>
        <div class="item" v-if="!inspectObj.inspecting">
          <div class="label" :style="{ 'white-space': 'nowrap' }">
            间隔时间:
          </div>
          <div class="value">
            <Input v-model="inspectTime" size="small">
              <template #suffix>秒</template>
            </Input>
          </div>
        </div>
        <Button
          v-if="!inspectObj.inspecting"
          type="primary"
          size="small"
          @click="inspectStart"
          >开始轮巡</Button
        >
        <i
          class="iconfont icon-quanping_xian"
          title="全屏"
          @click="setFullScreen"
        ></i>
      </div>
    </div>
  </div>
</template>

<script>
import { queryDeviceList } from "@/api/device";
export default {
  name: "videoPlay",
  data() {
    return {
      isShowToolBar: false,
      playingDeviceGbIds: [],
      searchInput: "",
      deviceList: [],
      allDeviceList: [],
      inspectObj: {
        timer: null,
        parse: false,
        inspecting: false,
        currentIndex: 0,
      },
      checkedIndexs: [0, 1, 2, 3],
      inspectTime: 10,
      currentLayout: "2*2",
      layoutOptions: [
        {
          label: "单分屏",
          value: "1*1",
          count: 1,
        },
        {
          label: "三分屏",
          value: "1A2",
          count: 3,
        },
        {
          label: "三分屏",
          value: "2A1",
          count: 3,
        },
        {
          label: "四分屏",
          value: "2*2",
          count: 4,
        },
        {
          label: "六分屏",
          value: "1A5",
          count: 6,
        },
        {
          label: "八分屏",
          value: "1A7",
          count: 8,
        },
        {
          label: "九分屏",
          value: "3*3",
          count: 9,
        },
        {
          label: "十六分屏",
          value: "4*4",
          count: 16,
        },
      ],
    };
  },
  mounted() {},
  beforeDestroy() {
    this.stopInspect();
  },
  methods: {
    async init(list) {
      this.deviceList = list;
      this.allDeviceList = list;
      this.receiveMessage();
      this.inspectStart();
      //   await this.queryDeviceList()
    },
    queryDeviceList() {
      this.deviceList = [];
      this.allDeviceList = [];

      var param = {
        deviceName: "",
      };
      queryDeviceList(param).then((res) => {
        console.log("---设备列表", res);
        res.data.forEach((item) => {
          var obj = {
            deviceId: item[0],
            deviceGbId: item[1],
            deviceName: item[2],
            deviceType: item[3],
            deviceChildType: item[4],
            detailAddress: item[5],
            dwlx: item[6],
            Status: item[7],
            Lon: item[8],
            Lat: item[9],
          };
          this.deviceList.push(obj);
          this.allDeviceList.push(obj);
        });
        this.receiveMessage();
        this.inspectStart();
      });
    },
    receiveMessage() {
      if (window.addEventListener) {
        this.handleCommon();
      }
      window.pageInitFinished = true;
    },
    handleCommon() {
      let _this = this;
      window.addEventListener("message", (even) => {
        if (Object.prototype.toString.call(even.data) === "[object String]") {
          let data = even.data && JSON.parse(even.data);
          this.allDeviceList = this.deviceList = data || [];
          let timer = requestAnimationFrame(function fn() {
            if (_this.$refs.ziPlayer.videoObj) {
              cancelAnimationFrame(timer);
              timer = null;
              _this.inspectStart();
            } else {
              timer = requestAnimationFrame(fn);
            }
          });
        }
      });
    },
    searchName() {
      this.deviceList = this.allDeviceList.filter((v) =>
        v.deviceName.includes(this.searchInput)
      );
    },
    handleNodeClick(item) {
      const { deviceId, deviceGbId, deviceName, Lat, Lon, ptzType, orgCode } =
        item;
      this.$refs.ziPlayer.playStream(
        {
          deviceId,
          deviceGbId,
          deviceName,
          geoPoint: { lat: Lat, lon: Lon },
          ptzType,
          orgCode,
          devicetype: liveType,
        },
        "live"
      );
    },
    // 切换布局
    changeLayout(layout) {
      this.currentLayout = layout;
      let item = this.layoutOptions.find((v) => v.value == layout);
      this.checkedIndexs = Array.from(
        { length: item.count },
        (_, index) => index
      );
      this.$refs.ziPlayer.changeLayout(layout);
    },
    // 扩展屏
    moveExtendedScreen() {
      this.$refs.ziPlayer.moveExtendedScreen();
    },
    // 全屏
    setFullScreen() {
      this.$refs.ziPlayer.setFullScreen();
    },
    closeAll() {
      this.$refs.ziPlayer.closeAll();
    },
    getWinList(val) {
      this.playingDeviceGbIds = val.map((v) => v.deviceGbId);
    },
    inspectStart() {
      let _this = this;
      this.inspectObj.parse = false;
      this.inspectObj.inspecting = true;
      this.inspectObj.currentIndex = 0;
      const timerFn = function () {
        if (!_this.inspectObj.parse) {
          let devices = _this.allDeviceList
            .slice(
              _this.inspectObj.currentIndex,
              _this.inspectObj.currentIndex + _this.checkedIndexs.length
            )
            .map((v) => v);
          _this.inspectObj.currentIndex += _this.checkedIndexs.length;
          // 超出后从头开始
          if (_this.inspectObj.currentIndex >= _this.allDeviceList.length) {
            _this.inspectObj.currentIndex =
              _this.inspectObj.currentIndex - _this.allDeviceList.length;
            let deviceAdd = _this.allDeviceList
              .slice(0, _this.inspectObj.currentIndex)
              .map((v) => v);
            devices = [...devices, ...deviceAdd];
          }
          _this.inspectPlay({ devices, indexs: _this.checkedIndexs });
        }
      };
      timerFn();
      this.inspectObj.timer = setInterval(timerFn, this.inspectTime * 1000);
      this.queryLog({
        muen: "时空分析",
        name: "框选设备",
        type: "4",
        remark: `轮巡播放`,
      });
    },
    // 轮巡
    inspectPlay({ devices, indexs }) {
      devices.forEach((item, index) => {
        const { deviceId, deviceGbId, deviceName, Lat, Lon, ptzType, orgCode } =
          item;
        this.$refs.ziPlayer.playStream(
          {
            deviceId,
            deviceGbId,
            deviceName,
            geoPoint: { lat: Lat, lon: Lon },
            ptzType,
            orgCode,
            devicetype: liveType,
          },
          "live",
          indexs[index]
        );
      });
    },
    parseInspect() {
      this.inspectObj.parse = !this.inspectObj.parse;
    },
    stopInspect() {
      clearInterval(this.inspectObj.timer);
      this.inspectObj.timer = null;
      this.inspectObj.parse = false;
      this.inspectObj.inspecting = false;
      this.closeAll();
    },
  },
};
</script>
<style lang="less" scoped>
.video-play {
  height: 100%;
  width: 100%;
  min-height: 700px;
  padding: 10px;
  display: flex;
  .color-blue {
    color: #2c86f8;
  }
  .video-left {
    width: 316px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    .search-top {
      margin: 0 10px;
    }
    .title {
      line-height: 40px;
      padding: 0 10px;
      font-weight: 600;
    }
    .label {
      height: 30px;
      line-height: 30px;
      margin: 0 12px;
    }
    .deviceList {
      height: calc(~"100% - 104px");
      overflow-y: auto;
      .node-title {
        cursor: pointer;
        padding: 5px 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.playing {
          color: #ff8d33;
          .name-width {
            font-weight: bold;
          }
        }
        i.playing-icon {
          width: 18px;
          height: 18px;
          margin-right: 5px;
          background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
        }
        .statistics {
          margin-left: 5px;
        }
        .name-width {
          display: block;
          flex: 1;
        }
        span {
          display: flex;
          align-items: center;
          overflow: hidden;
        }
        .offline {
          i {
            color: #888888;
          }
        }
        &:hover {
          color: #2c86f8;
          background-color: rgba(61, 183, 255, 0.1);
        }
      }
    }
  }
  .right-content {
    flex: 1;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    position: relative;
    .top {
      flex: 1;
      padding: 7px;
    }
  }
  .toolbar {
    position: absolute;
    top: 50px;
    right: 0;
    width: 450px;
    background: #fff;
    box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
    border-radius: 4px;
    z-index: 10;
    transition: all 0.5s;
    &-hide {
      right: -450px;
    }
    .toolbar-content {
      width: 100%;
      text-align: center;
      line-height: 44px;
      font-size: 14px;
      font-weight: 700;
      color: #3d3d3d;
      border-bottom: 1px solid #d3d7de;
      display: flex;
      align-items: center;
      justify-content: space-around;
      padding: 0 10px;
      .color-blue {
        color: #2c86f8 !important;
      }
      .iconfont {
        font-size: 22px;
        color: #484847;
        cursor: pointer;
        &:hover {
          color: #2c86f8;
        }
      }
      .item {
        display: flex;
        align-items: center;
        .label {
          margin-right: 5px;
          color: rgba(0, 0, 0, 0.4);
        }
        .value {
          width: 80px;
          /deep/ .ivu-input {
            height: 22px;
          }
        }
      }
    }
    .toggle {
      position: absolute;
      top: 4px;
      left: -18px;
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
      cursor: pointer;
      width: 18px;
      height: 36px;
      background: #2c86f8;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &.hide {
        i {
          transform: rotate(180deg);
        }
      }
    }
  }
}
/deep/ .ivu-tooltip-inner {
  padding: 3px;
  font-size: 12px;
  border-radius: 2px;
}
/deep/ .ivu-tooltip-popper {
  left: 0 !important;
}
/deep/ .ivu-input-suffix {
  display: flex;
  align-items: center;
}
</style>
