<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box">
      <p>{{ getDate('left') }}</p>
      <span class="font-vs">vs</span>
      <p>{{ getDate('right') }}</p>
    </div>
    <div v-for="(item, index) in data" :key="index" class="tooltip-content-box">
      <p class="legend-box">
        <span class="block" :style="{ 'background-color': item.color }"> </span>
        <span> {{ item.seriesName }}： </span>
        <span :style="{ color: item.color }" class="font-num">
          {{ item.value }}
        </span>
      </p>
      <p v-if="index == 1 && getDate('left')" class="pointer link-text-box" onclick="window.openDetails()">详情</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  methods: {
    getDate(str = '') {
      let index = str === 'left' ? 0 : 1;
      let text = this.data[0].data?.contrastExtDataBoList?.[index].detectDate;
      return text;
    },
  },
};
</script>

<style lang="less" scoped>
.tooltip-container {
  background: rgba(0, 10, 41, 0.7);
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    display: flex;
    align-items: center;
    flex-direction: row;
    color: #ffffff;
    .font-vs {
      font-weight: 600;
      color: #1b86ff;
      margin: 0 15px;
    }
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .block {
    display: inline-block;
    height: 2px;
    width: 15px;
    margin-right: 10px;
  }
  .font-num {
    font-weight: 600;
  }
}
</style>
