import global from '@/util/global';

export const statisticalQuantityList = [
  {
    name: '设备总量',
    leftCount: 0,
    rightCount: 0,
    color: 'var(--font-card-green)',
    icon: 'icon-jianceshebeishuliang',
    key: 'total',
  },
  {
    name: '信息相同设备',
    leftCount: 0,
    rightCount: 0,
    color: 'var(--color-primary)',
    icon: 'icon-xinxixiangtongshebei',
    key: 'sameCount',
  },
  {
    name: '信息差异设备',
    leftCount: 0,
    rightCount: 0,
    color: 'var(--font-card-light-pink)',
    icon: 'icon-xinxichayishebei',
    key: 'differentCount',
  },
  {
    name: '独有设备',
    leftCount: 0,
    rightCount: 0,
    color: 'var(--font-card-cyan)',
    icon: 'icon-duyoushebei',
    key: 'uniqueCount',
  },
];
export const rightTableColumns = [
  {
    title: '序号',
    type: 'index',
    align: 'center',
    width: 50,
  },
  {
    width: 180,
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    align: 'left',
    tree: true,
  },
  {
    width: 190,
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: '所属单位',
    key: 'orgName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 100,
    title: '行政区划',
    key: 'civilName',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.longitude}`,
    key: 'longitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 120,
    title: `${global.filedEnum.latitude}`,
    key: 'latitude',
    align: 'left',
    tooltip: true,
  },
  {
    width: 130,
    title: global.filedEnum.ipAddr,
    key: 'ipAddr',
    align: 'left',
    tooltip: true,
  },
  {
    width: 150,
    title: `${global.filedEnum.macAddr}`,
    key: 'macAddr',
    align: 'left',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbgnlx}`,
    key: 'sbgnlxText',
    tooltip: true,
  },
  {
    minWidth: 120,
    title: `${global.filedEnum.sbdwlx}`,
    key: 'sbdwlxText',
  },

  {
    minWidth: 110,
    title: `${global.filedEnum.sbcjqy}`,
    key: 'sbcjqyText',
    tooltip: true,
  },
];
export const leftTableColumns = [
  {
    type: 'selection',
    width: 50,
    align: 'center',
  },
  ...rightTableColumns,
];
