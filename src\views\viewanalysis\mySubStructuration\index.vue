<template>
  <div class="layout">
    <tabsPage :list="tabList" :selectLi="selectLi" @change="tabChange" />
    <div class="container">
      <!-- 搜索 -->
      <searchForm
        class="search"
        ref="searchForm"
        :structureJobType="selectLi"
        @searchForm="searchForm"
      >
      </searchForm>

      <!-- 列表 -->
      <div class="card-content">
        <div class="list">
          <ui-table
            :columns="columns"
            :loading="loading"
            :data="tableList"
            @on-expand="handleExpand"
          >
            <template #totalTask="{ row }">
              <div>总数：{{ row.totalTask }}</div>
            </template>
            <template #createTime="{ row }">
              <div>{{ row.createTime | timeFormat }}</div>
            </template>
            <template #applyStatus="{ row }">
              <div>
                {{
                  row.applyStatus == 1
                    ? "通过"
                    : row.applyStatus == 2
                    ? "驳回"
                    : "未审核"
                }}
              </div>
            </template>
            <template #opreate="{ row }">
              <div class="btn-tips">
                <ui-btn-tip
                  :content="row.applyStatus == 0 ? '审批' : '详情'"
                  :icon="
                    row.applyStatus == 0 ? 'icon-bianji' : 'icon-xiangqing'
                  "
                  class="mr-20 primary"
                  @click.native="openDetail(row)"
                ></ui-btn-tip>
                <ui-btn-tip
                  content="删除"
                  icon="icon-shanchu"
                  class="mr-20 primary"
                  @click.native="handleDel(row)"
                ></ui-btn-tip>
              </div>
            </template>
          </ui-table>
        </div>
      </div>

      <!-- 分页 -->
      <ui-page
        :current="params.pageNumber"
        :total="total"
        :page-size="params.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>

      <ui-modal
        v-model="detailVisible"
        title="解析审核详情"
        :r-width="1600"
        footer-hide
      >
        <div class="detail-box">
          <component
            :is="componentName"
            v-model="detailVisible"
            isApproval
            :subTaskType="subTaskType"
            :subTaskId="subTaskId"
            :taskId="subTaskId"
          ></component>
        </div>
        <div class="apply-detail">
          <div class="detail-title">
            <div class="basic">审核意见</div>
          </div>
          <Input
            v-model="applyReason"
            :disabled="isView"
            type="textarea"
          ></Input>
        </div>
        <div class="footer" v-if="!isView">
          <Button class="mr-10" type="primary" @click="comfirmHandle('1')"
            >同意</Button
          >
          <Button class="mr-10" @click="comfirmHandle('2')">驳回</Button>
        </div>
      </ui-modal>
    </div>
  </div>
</template>
<script>
import tabsPage from "./tabs.vue";
import searchForm from "./search-form.vue";
import expandRow from "@/components/ui-table-expandRow/expandRow.vue";
import addModal from "../components/add-modal.vue";
import fileAddModal from "../components/file-add-modal.vue";
import {
  getStructureJobList,
  getStructureRealTaskList,
  delTask,
  delJob,
  startJob,
  stopJob,
  startTask,
  stopTask,
  applyJob,
} from "@/api/viewAnalysis";

export default {
  name: "mySubStructuration",
  components: {
    tabsPage,
    searchForm,
    expandRow,
    addModal,
    fileAddModal,
  },
  data() {
    return {
      loading: false,
      tabList: [
        { name: "实时结构化", value: 1 },
        { name: "历史结构化", value: 2 },
        { name: "文件结构化", value: 3 },
      ],
      selectLi: 1,
      params: {
        pageNumber: 1,
        pageSize: 20,
        sort: "desc",
      },
      total: 0,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                taskList: this.childList[params.index],
                columns: this.childColumns,
                currentJob: this.tableList[params.index],
                subTaskType: this.subTaskType,
              },
            });
          },
        },
        { title: "任务名称", key: "name", width: 240 },
        { title: "解析类型", slot: "totalTask" },
        { title: "创建时间", slot: "createTime" },
        { title: "创建人", key: "creatorName" },
        { title: "审核状态", slot: "applyStatus", width: 100 },
        { title: "申请理由", key: "requestReason", width: 200 },
        { title: "审核内容", key: "applyReason", width: 200 },
        { title: "操作", slot: "opreate", width: 100 },
      ],
      childColumns: [
        { title: "任务名称", slot: "name", width: 315, align: "center" },
        { title: "状态", slot: "vehicle" },
      ],
      tableList: [],
      childList: [],
      subTaskId: "", //添加编辑页面的任务ID
      componentName: "addModal",
      detailVisible: false,
      applyReason: "",
      isView: false,
    };
  },
  computed: {
    subTaskType() {
      return this.selectLi == 1 ? "real" : this.selectLi == 2 ? "his" : "file";
    },
  },
  mounted() {},
  methods: {
    tabChange(row) {
      this.selectLi = row.value;
      this.childList = [];
      this.$refs.searchForm.query();
    },
    searchForm(pageForm) {
      var param = Object.assign(pageForm, this.params, {
        pageNumber: this.params.pageNumber - 1,
        structureJobType: this.selectLi,
      });
      this.loading = true;
      getStructureJobList(param)
        .then((res) => {
          const { list, total } = res.data;
          this.tableList = list || [];
          this.total = total;
        })
        .finally((res) => {
          this.loading = false;
        });
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.$refs.searchForm.query();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.$refs.searchForm.query();
    },
    handleExpand(row, status) {
      let index = this.tableList.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.tableList[index]._expanded = true;
        getStructureRealTaskList({
          structureJobId: row.jobId,
          structureJobType: this.selectLi,
          sort: this.params.sort,
          pageNumber: 0,
          pageSize: 999,
        }).then((res) => {
          const { list } = res.data;
          this.$set(this.childList, index, list || []);
        });
      } else {
        this.tableList[index]._expanded = false;
      }
    },
    // 编辑
    openDetail(row) {
      this.subTaskId = row.jobId;
      this.componentName = this.selectLi == 3 ? "fileAddModal" : "addModal";
      this.isView = row.applyStatus == 1 || row.applyStatus == 2 ? true : false;
      this.applyReason = "";
      this.detailVisible = true;
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    //删除---删除任务&批量删除任务
    deleteJobs(data) {
      if (!data || !data.length) {
        this.$Message.warning("当前未选中任务");
        return;
      }
      const ids = data.map((item) => item.jobId).join(",");
      const names = data.map((item) => item.name).join(",");
      if (ids === "") {
        this.$Message.warning("当前未选中任务");
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `您确认删除【${names}】结构化任务吗?`,
        onOk: () => {
          delJob(ids).then((res) => {
            this.queryLog({
              muen: "解析审核",
              name: "解析审核",
              type: "3",
              remark: `删除【${names}】结构化任务。`,
            });
            res
              ? this.$Message.success("任务删除成功")
              : this.$Message.success("任务删除失败");
            this.$refs.searchForm.query();
          });
        },
      });
    },
    comfirmHandle(status) {
      applyJob({
        id: this.subTaskId,
        applyStatus: status,
        applyReason: this.applyReason,
      }).then((res) => {
        this.detailVisible = false;
        this.$Message.success("审核成功");
        this.$refs.searchForm.query();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  //   height: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
  .container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .card-content {
      width: 100%;
      display: flex;
      flex: 1;
      margin: 0 -0.02604rem;
      position: relative;
    }
    .list {
      flex: 1;
      display: flex;
    }
  }
}
/deep/ .ivu-modal-body {
  padding: 0 !important;
  max-height: fit-content !important;
  .detail-box {
    height: 75vh;
    position: relative;
  }
  .apply-detail {
    padding: 0 10px 10px 10px;
  }
  .footer {
    height: 50px;
    text-align: center;
    padding-top: 5px;
  }
}
.detail-title {
  position: relative;
  // height: 22px;
  line-height: 22px;
  padding: 3px 0;
  padding-left: 10px;
  margin: 0 0 10px;
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(
    to right,
    rgba(233, 243, 255, 1),
    rgba(233, 243, 255, 0)
  );
  .basic {
    font-size: 14px;
  }
}
.detail-title::after {
  content: "";
  width: 3px;
  height: 22px;
  background: #2c86f8;
  position: absolute;
  left: 0;
}
</style>
