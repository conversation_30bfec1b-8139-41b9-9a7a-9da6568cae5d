.search {
    padding: 10px 20px 0;
    border-bottom: 1px solid #ffff;
    display: flex;
    width: 100%;
    justify-content: space-between;
    position: relative;
    .ivu-form-inline {
        width: 100%;
    }
    .ivu-form-item {
        margin-bottom: 0;
        margin-right: 30px;
        display: flex;
        align-items: center;
        /deep/ .ivu-form-item-label {
            white-space: nowrap;
            width: 72px;
            text-align-last: justify;
            text-align: justify;
            text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
        }
    }
    .general-search{
        display: flex;
        width: 100%;
        .input-content{
            flex: 1;
            display: flex;
            .upload-input-list {
                display: flex;
                max-width: 440px;
            }
            .other-search{
                display: flex;
                flex: 1;
                box-sizing: border-box;
                flex-direction: column;
                padding-left: 10px;
                .other-search-top {
                    display: flex;
                    border-bottom: 1px dashed #fff;
                    .select-type{
                        margin-left: 10px;
                    }
                }
                .ivu-form-item {
                    display: flex;
                    margin-bottom: 10px;

                    /deep/ .ivu-form-item-label {
                        padding-right: 10px;
                    }
                    .add-subtract{
                        cursor: pointer;
                    }
                    .ivu-input-wrapper,
                    .ivu-select {
                        width: 200px;
                    }
                }
                .other-search-bottom {
                    display: flex;
                    justify-content: space-between;
                    padding-top: 10px;
                    box-sizing: border-box;
                    /deep/ .ivu-form-item-content {
                        display: flex;
                    }
                    .slider-form-item {
                        /deep/ .ivu-form-item-content {
                            display: flex;
                            align-items: center;
                        }
                    }
                }
            }
        }
        /deep/ .ivu-form-item-content {
            display: flex;
            align-items: center;
        }
    }
    .advanced-search {
        display: flex;
        position: absolute;
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        z-index: 11;
        max-height: 0px;
        top: 100%;
        left: 0;
        transition: max-height 0.3s;
        overflow: auto;
        flex-direction: column;
        .advanced-search-item {
            &.justify-content-normal {
                justify-content: normal;
            }
            //   display: flex;
            //   justify-content: flex-start;
            // justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px dashed #fff;
            //   align-items: center;
            &:first-child {
                border-top: 1px solid;
            }
            .ivu-form-item {
                margin-right: 30px;
                &:last-child {
                    margin-right: 0;
                }
                &.percent-70 {
                    width: 70%;
                }
                display: flex;
                .text-radio-group {
                    margin-left: -10px;
                }
            }
            .btn-group {
                flex: 1;
                justify-content: flex-end;
            }
            /deep/ .ivu-form-item-content {
                display: flex;
                align-items: center;
            }
        }
    }
    .advanced-search-show {
        .advanced-search {
            max-height: 400px;
            transition: max-height 0.7s;
        }
        .advanced-search-text {
            /deep/ .icon-jiantou {
                transform: rotate(180deg);
                transition: transform 0.2s;
            }
        }
    }

}
.btn-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .advanced-search-text {
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-right: 30px;
        font-size: 14px;

        .icon-jiantou {
            margin-left: 2px;
            font-size: 18px;
            transform: rotate(0deg);
            transition: transform 0.2s;
        }
    }
}