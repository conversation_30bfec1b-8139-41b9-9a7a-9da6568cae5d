<template>
  <div class="platform-monitor-container auto-fill">
    <div v-if="!tabComponentName" class="platform-box auto-fill">
      <!-- 监控类型 -->
      <ui-tabs-wrapped
        :data="MONITOR_TYPES"
        :value="searchData.activeMoinitorType"
        @input="changeMonitorType"
      ></ui-tabs-wrapped>
      <!-- 统计模式+时间筛选 -->
      <div class="statistics-box flex-row mt-md">
        <tag-view
          :list="STATISTICS_TYPES"
          :default-active="defaultStatisticsTypeIndex"
          :no-active="STATISTICS_TYPES.length === 0"
          @tagChange="changeStatisticType"
        ></tag-view>
        <div class="d_flex">
          <DatePicker
            v-if="searchData.activeStatisticsType === STATISTIC_TYPE_COUNTING"
            type="month"
            format="yyyy-MM"
            placeholder="请选择统计月份"
            v-model="searchData.searchTime"
            @on-change="changeSearchDate"
            :options="DISABLED_AFTER_NOW"
          ></DatePicker>
        </div>
      </div>
      <!-- 离线检测、数量统计table -->
      <component
        class="mt-md auto-fill"
        ref="tableComponentRef"
        :is="tableComponentName"
        :table-loading="tableLoading"
        :table-data="tableData"
        :common-search-data="searchData"
        @tableItemClick="tableItemClick"
      ></component>
      <!-- 持续无数据弹窗 -->
      <continuous-nodata-modal v-model="continuousNoDataVisible" v-bind="computedCommonAttr"></continuous-nodata-modal>
      <!-- 离线详情弹窗 -->
      <offline-detail-modal v-model="offlineDetailVisible" v-bind="computedCommonAttr"></offline-detail-modal>
      <!-- 日抓拍弹窗 -->
      <day-capture-modal v-model="dayCaptureVisible" v-bind="computedCommonAttr"></day-capture-modal>
      <!-- 月抓拍详情 -->
      <create-tabs
        ref="createTabsRef"
        :componentName="themData.componentName"
        :tabs-text="themData.text"
        :tabs-query="themData.tabsQuery"
        @selectModule="selectModule"
        class="inline btn-text-default"
      ></create-tabs>
    </div>
    <!-- 月抓拍情况 -->
    <keep-alive>
      <component :is="tabComponentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import {
  MONITOR_TYPES,
  MONITORTYPE_FACE,
  MONITORTYPE_VEHICLE,
  STATISTICS_TYPES,
  STATISTIC_TYPE_OFFLINE,
  STATISTIC_TYPE_COUNTING,
  DISABLED_AFTER_NOW,
} from './utils/enum';
import dealWatch from '@/mixins/deal-watch';
import datamonitorApi from '@/config/api/datamonitor';
import { mapGetters, mapActions } from 'vuex';
import { chartsFactory } from './utils/chartsFactory';

export default {
  name: 'platformmonitor',
  mixins: [dealWatch],
  data() {
    return {
      MONITOR_TYPES, //监控类型：人脸、车辆视图库监测
      MONITORTYPE_FACE, //人脸
      MONITORTYPE_VEHICLE, //车辆
      STATISTICS_TYPES, //统计类型: 离线监测、数量统计
      STATISTIC_TYPE_OFFLINE, //离线监测
      STATISTIC_TYPE_COUNTING, //数量统计的值
      DISABLED_AFTER_NOW, //禁用当前时间之后的日期
      defaultStatisticsTypeIndex: 0,
      searchData: {
        activeMoinitorType: MONITORTYPE_FACE,
        activeStatisticsType: STATISTIC_TYPE_OFFLINE,
        searchTime: new Date(),
        regionCode: '',
      },
      tableComponentName: 'offlineTable',
      tableData: [],
      tableLoading: false,
      themData: {
        componentName: 'monthDetail', // 需要跳转的组件名
        text: '月抓拍', // 跳转页面标题
        type: 'view',
        tabsQuery: {},
      },
      tabComponentName: '',
      //弹窗
      showModalRow: {},
      continuousNoDataVisible: false,
      offlineDetailVisible: false,
      dayCaptureVisible: false,
      echartsFactory: {},
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedArea: 'common/getDefaultSelectedArea',
    }),
    computedCommonAttr() {
      return {
        modalRowData: this.showModalRow,
        echartsFactory: this.echartsFactory,
        commonSearchData: this.searchData,
      };
    },
  },
  methods: {
    ...mapActions({
      setAreaList: 'common/setAreaList',
    }),
    //切换人车监测类型
    changeMonitorType(val) {
      this.searchData.activeMoinitorType = val;
      this.getTableData();
    },
    //切换统计类型
    changeStatisticType(index, item) {
      this.defaultStatisticsTypeIndex = index;
      this.searchData.activeStatisticsType = item.value;
      this.tableComponentName = item.value === STATISTIC_TYPE_COUNTING ? 'countingTable' : 'offlineTable';
      this.getTableData();
    },
    // 切换日期
    changeSearchDate() {
      this.getTableData();
    },
    /**
     * 点击表格项
     * handlerType: 设定的操作标识，用于区分操作
     */
    tableItemClick({ row }, handlerType = null) {
      if (!handlerType) {
        console.error('未添加操作类型');
        return;
      }
      this.showModalRow = row;
      switch (handlerType) {
        case 'viewContinuousNoData':
          //持续无数据
          this.continuousNoDataVisible = true;
          break;
        case 'viewThisMonthCatch':
          // 本月抓拍数量
          let thisMonthTime = this.searchData.searchTime || new Date();
          let formatThisMonthTime = this.$util.common.formatDate(thisMonthTime, 'yyyyMM');
          Object.assign(this.themData.tabsQuery, {
            regionCode: row.code, //地区编码
            time: formatThisMonthTime,
            activeMoinitorType: this.searchData.activeMoinitorType,
            name: row.name, //行政区划名称
            typeField: 'thisYearMonth',
          });
          this.$refs.createTabsRef.create();
          break;
        case 'viewTodayCatchNum':
          // 今日抓拍
          this.searchData.name = row.name;
          this.dayCaptureVisible = true;
          break;
        case 'viewMonthCatchInLastYear':
          // 去年同月抓拍数量
          let monthTime = this.searchData.searchTime || new Date();
          let month = monthTime.getMonth() + 1;
          let lastYearMonthTime = `${monthTime.getFullYear() - 1}${month > 10 ? month.toString() : '0' + month}`;
          Object.assign(this.themData.tabsQuery, {
            regionCode: row.code, //地区编码
            time: lastYearMonthTime,
            activeMoinitorType: this.searchData.activeMoinitorType,
            name: row.name, //行政区划名称
            typeField: 'thisYearMonth',
          });
          this.$refs.createTabsRef.create();
          break;
        case 'viewMonthOfflineTime':
          // 本月累计离线时长
          this.offlineDetailVisible = true;
          break;
      }
    },
    //获取表格数据
    async getTableData() {
      this.searchData.activeStatisticsType === STATISTIC_TYPE_OFFLINE
        ? await this.getOffLineTableData()
        : await this.getCountingTableData();
    },
    //离线统计
    async getOffLineTableData() {
      this.tableLoading = true;
      try {
        const params = {
          type: this.searchData.activeMoinitorType,
          regionCode: this.searchData.regionCode,
        };
        const {
          data: { data },
        } = await this.$http.get(datamonitorApi.offlineMonitor, { params });
        this.tableData = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.tableLoading = false;
      }
    },
    //数量统计
    async getCountingTableData() {
      try {
        this.tableLoading = true;
        const formatSearchTime = this.$util.common.formatDate(this.searchData.searchTime, 'yyyyMM');
        const params = {
          regionCode: this.searchData.regionCode,
          time: formatSearchTime,
          type: this.searchData.activeMoinitorType,
        };
        const {
          data: { data },
        } = await this.$http.get(datamonitorApi.getQuantityStatistics, { params });
        this.tableData = data;
      } catch (err) {
        console.log(err);
      } finally {
        this.tableLoading = false;
      }
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.tabComponentName = name;
      } else {
        this.tabComponentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
  },
  created() {
    this.getParams();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  async mounted() {
    await this.setAreaList();
    this.searchData.regionCode = this.defaultSelectedArea.regionCode;
    this.echartsFactory = new chartsFactory();
    await this.getTableData();
  },
  destroyed() {
    this.echartsFactory = null;
  },
  components: {
    UiTabsWrapped: require('@/components/ui-tabs-wrapped.vue').default,
    TagView: require('@/components/tag-view').default,
    offlineTable: require('./modules/offline-table.vue').default,
    countingTable: require('./modules/counting-table.vue').default,
    ContinuousNodataModal: require('./components/continuous-nodata-modal.vue').default,
    OfflineDetailModal: require('./components/offline-detail-modal.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    monthDetail: require('./modules/month-detail/index.vue').default,
    dayCaptureModal: require('./components/day-capture-modal.vue').default,
  },
};
</script>
<style lang="less" scoped>
.platform-monitor-container {
  background-color: var(--bg-content);
  border-radius: 10px;
  .platform-box {
    padding: 10px 20px;
    @{_deep} .ui-tabs-wrapped-container {
      .tab-item {
        width: 140px;
      }
    }
  }

  @{_deep} .link {
    cursor: pointer;
    text-decoration: underline;
  }

  @{_deep} .text-warning {
    color: var(--color-warning);
  }
  @{_deep} .text-error {
    color: var(--color-failed);
  }
}
</style>
