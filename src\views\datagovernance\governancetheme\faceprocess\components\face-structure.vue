<template>
  <div class="face-structure">
    <ui-modal v-model="visible" :title="title" width="43rem">
      <Form v-if="quality" ref="formValidate" :model="formValidate">
        <div class="quality">
          <!-- <span>{{ quality }}</span> -->
          <FormItem
            :label="quality"
            prop="percent"
            :rules="[
              {
                required: true,
                message: '请输入数字',
                trigger: 'blur',
                type: 'number',
              },
            ]"
          >
            <Input v-model="formValidate.percent" number style="width: 118px" />
          </FormItem>
          <span><slot></slot></span>
        </div>
      </Form>
      <selection-algorithm
        v-if="!quality"
        ref="algorithm"
        :propData="dictData"
        :algorithmVendors="algorithmVendors"
        @update="update"
      >
      </selection-algorithm>
      <div v-if="tips" class="tips">
        {{ tips }}
      </div>
      <template slot="footer">
        <Button type="primary" :disabled="isDisabled && !quality" @click="save">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import user from '@/config/api/user';
import governancetheme from '@/config/api/governancetheme';
import algorithm from '@/config/api/algorithm';
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    quality: {
      type: String,
      default: '',
    },
    tips: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      formValidate: {
        percent: '',
      },
      dictData: [],
      algorithmVendors: [],
      isDisabled: true,
      dicts: [],
    };
  },
  created() {},
  methods: {
    async init(processOptions) {
      this.visible = true;
      this.topicComponentId = processOptions.topicComponentId;
      if (!this.quality) {
        await this.getAlgorithmField();
        await this.getDictData();
        this.getAlgorithmList();
      } else {
        this.getUploadTime();
      }
    },
    async getAlgorithmField() {
      try {
        const url = algorithm.algorithmselect + '3';
        let res = await this.$http.get(url);
        this.dicts = res.data.data.map((item) => {
          return item.algorithmVendorType;
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 查询字典数据（获取算法厂商）
    async getDictData() {
      try {
        const params = {
          typekey: 'algorithmVendorType',
        };
        let res = await this.$http.get(user.queryByTypeKey, { params });
        this.dictData = res.data.data.filter((item) => {
          return this.dicts.includes(item.dataKey);
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getAlgorithmList() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicAlgorithmList, { params });
        const datas = res.data.data;
        if (!datas.length) {
          this.isDisabled = true;
          return false;
        } else {
          this.isDisabled = false;
        }
        this.algorithmVendors = datas.map((item) => {
          return item.algorithmVendorType;
        });
      } catch (error) {
        console.log(error);
      }
    },
    save() {
      let params = {
        topicComponentId: this.topicComponentId,
      };
      let url = '';
      if (this.quality) {
        this.$refs['formValidate'].validate((valid) => {
          if (valid) {
            let extraParam = JSON.stringify(this.formValidate);
            params.extraParam = extraParam;
            url = 'updateTopicComponentOfUploadTime';
          } else {
            this.$Message.error('请将信息填写完整!');
          }
        });
      } else {
        let algorithmData = this.$refs.algorithm.algorithmData;
        if (!algorithmData.length) {
          this.isDisabled = true;
          return false;
        }
        this.isDisabled = true;
        let algorithmList = algorithmData.map((item) => {
          let obj = {};
          obj.algorithmVendorType = item;
          return obj;
        });
        params.algorithmList = algorithmList;
        url = 'updateTopicAlgorithm';
      }
      this.updateAlgorithm(params, url);
    },
    async updateAlgorithm(params, url) {
      try {
        await this.$http.post(governancetheme[url], params);
        this.visible = false;
        this.$emit('render');
        this.$Message.success('配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    update(datas) {
      this.isDisabled = datas.length ? false : true;
    },
    async getUploadTime() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicComponentOfUploadTime, { params });
        const datas = res.data.data;
        if (datas.extraParam) {
          let extraParam = JSON.parse(datas.extraParam);
          this.formValidate.percent = extraParam.percent;
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {},
  components: {
    SelectionAlgorithm: require('../../components/selection-algorithm.vue').default,
  },
};
</script>
<style lang="less" scoped>
.quality {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 14px;
  color: #ffffff;
  span:first-child {
    margin-right: 10px;
  }
  span:last-child {
    margin-left: 10px;
  }
}
.tips {
  margin-top: 13px;
  font-size: 14px;
  color: #c76d28;
}
@{_deep} .ivu-modal-body {
  padding: 20px 51px 37px;
}
@{_deep} .ivu-form-item {
  margin-bottom: 0 !important;
  &-content {
    display: flex;
  }
}
</style>
