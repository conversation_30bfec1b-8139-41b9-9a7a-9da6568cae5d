<template>
  <!-- 实体类型筛选 -->
  <Collapse
    simple
    class="check-select"
    :class="{ 'check-close-style': collapseValue && collapseValue[0] !== '1' }"
    v-model="collapseValue"
  >
    <div class="check-select-close">
      <Icon type="ios-close" @click="handleCancel" />
    </div>
    <Panel name="1" hide-arrow>
      <Checkbox
        v-model="checkAll"
        @on-change="handleCheckAll"
        style="width: 80%"
        >{{ type === "node" ? "全部实体" : "全部关系" }}</Checkbox
      >
      <div class="check-select-content" slot="content">
        <CheckboxGroup v-model="checkAllGroup" @on-change="changeCheck">
          <div
            v-for="(item, index) in groupList"
            :key="index"
            class="check-select-item"
          >
            <Checkbox :label="item.label">
              {{ item.labelCn }} ( {{ item.num }} )
            </Checkbox>
          </div>
        </CheckboxGroup>
      </div>
    </Panel>
  </Collapse>
</template>
<script>
export default {
  components: {},
  props: {
    type: {
      type: String,
      default: "node",
    },
    graphData: {
      type: Object,
    },
    groupList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      checkAll: true,
      checkAllGroup: [],
      collapseValue: "1",
    };
  },
  created() {},
  mounted() {},
  methods: {
    getGroup() {
      this.checkAllGroup = [];
      this.checkAll = true;
      this.groupList.forEach((row) => {
        this.checkAllGroup.push(row.label);
      });
    },
    // 全选
    handleCheckAll(val) {
      if (val) {
        this.checkAllGroup = this.groupList.map((row) => row.label);
      } else {
        this.checkAllGroup = [];
      }
      this.$emit("dataFilter", this.checkAllGroup);
    },
    // 单选
    changeCheck(checkArray) {
      if (checkArray.length === this.groupList.length) {
        this.checkAll = true;
      } else {
        this.checkAll = false;
      }
      this.$emit("dataFilter", this.checkAllGroup);
    },
    getFilterDataIds() {
      const checkGroupIds = [];
      this.groupList.forEach((item) => {
        if (this.checkAllGroup.includes(item.label)) {
          checkGroupIds.push(...item.ids);
        }
      });
      return checkGroupIds;
    },
    // 关闭列表
    handleCancel() {
      this.$emit("closeDataFilter");
    },
  },
  watch: {
    groupList(val) {
      this.getGroup(val);
    },
  },
};
</script>
<style lang="less" scoped>
.check-select {
  position: relative;
  // position: absolute;
  // top: 250px;
  width: 246px;
  // left: 22px;
  height: 100%;
  min-height: 100%;
  z-index: 11;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  .check-select-close {
    position: absolute;
    right: 5px;
    top: -5px;
    font-size: 30px;
    z-index: 1;
    color: #a3a3a3;
    cursor: pointer;
    &:hover {
      color: #656666;
    }
  }
  .check-select-content {
    height: 100%;
  }
  .check-select-item {
    margin-top: 20px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);
  }
  .ivu-checkbox-group {
    height: 100%;
    overflow: auto;
  }
  /deep/ .ivu-collapse-header {
    border-bottom: 1px solid #d3d7de !important;
    .ivu-icon {
      float: right;
      position: relative;
      top: 9px;
      right: 20px;
      font-size: 20px;
      &::before {
        content: "\F33D";
      }
    }
  }
  /deep/ .ivu-collapse-item-active > .ivu-collapse-header > i {
    transform: rotate(180deg);
  }

  /deep/ .ivu-collapse-item {
    height: 100%;
    .ivu-collapse-content {
      height: 88%;
      padding: 0;
      padding-left: 16px;
      display: block !important;
      .ivu-collapse-content-box {
        height: 100%;
      }
    }
  }
}
.check-close-style {
  height: 38px;
  overflow: hidden;
}
</style>
