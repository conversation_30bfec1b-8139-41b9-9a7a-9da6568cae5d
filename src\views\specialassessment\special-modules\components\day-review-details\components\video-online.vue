<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #completeDay="{ row }">
        <span class="qualified-color">{{ row.completeDay }}</span>
      </template>
      <template #hiatusDay="{ row }">
        <Tooltip
          v-if="!!row.hiatusDay"
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color">{{ row.hiatusDay }}</span>
          <div slot="content">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.checkStartDate"
              :end-date="row.checkEndDate"
              :hiatus="JSON.parse(row.timeDayJsonStr)"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #phyStatus="{ row }">
        <span v-if="row.phyStatusText" :class="row.phyStatus === '1' ? 'qualified-color' : 'unqualified-color'">
          {{ row.phyStatusText }}
        </span>
        <span v-else>--</span>
      </template>
      <template #delaySipMillSecond="{ row }">
        <span>
          {{ !row.delaySipMillSecond ? '--' : row.delaySipMillSecond }}
        </span>
      </template>
      <template #delayStreamMillSecond="{ row }">
        <span>
          {{ !row.delayStreamMillSecond ? '--' : row.delayStreamMillSecond }}
        </span>
      </template>
      <template #delayIdrMillSecond="{ row }">
        <span>
          {{ !row.delayIdrMillSecond ? '--' : row.delayIdrMillSecond }}
        </span>
      </template>
      <template slot="qualified" slot-scope="{ row }">
        <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
          {{ qualifiedColorConfig[row.qualified].dataValue }}
        </Tag>
        <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
      <template #option="{ row }">
        <ui-btn-tip
          class="mr-sm play-btn-color"
          icon="icon-bofangshipin"
          content="播放视频"
          :styles="{ color: 'var(--color-primary)' }"
          @click.native="clickRow(row)"
        ></ui-btn-tip>
        <ui-btn-tip
          icon="icon-chakanjietu"
          content="查看截图"
          :styles="{ color: 'var(--color-primary)' }"
          :disabled="!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot"
          @click.native="viewResult(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <slot name="page"></slot>
    <video-player
      v-model="videoVisible"
      :video-url="videoUrl"
      :play-device-code="playDeviceCode"
      @onCancel="videoUrl = ''"
    ></video-player>
    <result-model ref="resultRef"></result-model>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';
import vedio from '@/config/api/vedio-threm';

export default {
  name: 'video-online',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      videoUrl: '',
      playDeviceCode: '',
      videoVisible: false,
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  methods: {
    //视频播放
    async clickRow(row) {
      try {
        this.playDeviceCode = row.deviceId;
        this.videoVisible = true;
        let data = {};
        data.deviceId = row.deviceId;
        let res = await this.$http.post(vedio.getplay, data);
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    // 查看截图
    viewResult(row) {
      if (!row.additionalImage && !row.areaImage && !row.dateImage && !row.screenShot) return;
      this.$refs.resultRef.showModal(row);
    },
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    VideoPlayer: require('@/views/governanceevaluation/evaluationoResult/components/video-player.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
    resultModel: require('@/components/result-model.vue').default,
    NopageCalender:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/components/nopage-calender.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}

.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }
}
.unqualified-color {
  color: #ea4a36;
}
.qualified-color {
  color: #1faf81;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
<style lang="less">
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }

  .ivu-tooltip-arrow {
    border-bottom-color: rgba(0, 21, 41, 0.15) !important;
  }
}
</style>
