<!--
    * @FileDescription: 频次分析=>列表
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="detailsBox">
        <div class="details-list">
            <div class="box-hint">
                <Icon type="ios-undo" @click="handleback" />
                <span @click="handleback">频次分析 > 查询结果</span>
            </div>
            <div class="table-box">
                <Scroll :on-reach-bottom="handleReachBottom" height="100%" :loading-text="loadingText" :distance-to-edge="10">
                    <li class="freno-item" style="display: flex" :class="[{'freno-item-hover': currentIndex === index}]" v-for="(item, index) in dataList" :key="index">
                        <ui-image class="mr-20" viewer :src="item.photo" @click.stop=""/>
                        <div class="detail" @click.stop="showDetail(item, index)">
                            <span class="plate-number vehicle-plate">{{ item.name }}</span>
                            <span class="plate-number vehicle-plate">{{ item.idNumber }}</span>
                            <p class="nightOut-count">出现次数: &nbsp;&nbsp;{{ item.appearCount }} 次</p>
                        </div>
                    </li>
                </Scroll>
                <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </div>
        </div>
    </div>
</template>

<script>
import { getFrequentStrangerPersonList } from '@/api/monographic/compus-control'

export default {
    name: '',
    data () {
        return {
            dataList: [],
            loading: false,
            detailsParams: { },
            loadingText: '加载中',
            isLast: false,
            currentIndex: -1,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            }
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        handleList(param) {
            this.resetAll()
            this.detailsParams = {...param};
            this.detailsParams.startDate = this.$dayjs(this.detailsParams.startDate).format('YYYY-MM-DD HH:mm:ss');
            this.detailsParams.endDate = this.$dayjs(this.detailsParams.endDate).format('YYYY-MM-DD HH:mm:ss');
            this.queryList()
        },
        queryList(){
            this.loading = true;
            let params = {...this.detailsParams, ...this.pageInfo}
            getFrequentStrangerPersonList(params)
            .then(res => {
                let list = res.data || []
                this.dataList = this.dataList.concat(list)
                if (list && list.length) {
                    this.pageInfo.pageNumber += 1
                } else {
                    this.isLast = true
                }
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleReachBottom () {
            this.loadingText = '加载中'
            if (this.isLast) {
                this.loadingText = '已经是最后一页了'
                return
            }
            return this.queryList()
        },
        handleback() {
            this.$emit('backSearch')
        },
        resetAll() {
            this.currentIndex = -1
            this.isLast = false
            this.pageInfo.pageNumber = 1
            this.dataList = [];
        },
        showDetail(item, index) {
            this.currentIndex = index
            this.$emit("cutAnalyse", { ...this.detailsParams, ...item });
        }
    }
}
</script>

<style lang='less' scoped>
.detailsBox{
    position: absolute;
    top: 10px;
    left: 10px;
    height: calc(~'100% - 20px');
    .details-list{
        height: 100%;
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        filter: blur(0px);
        position: relative;
        .box-hint{
            width: 370px;
            height: 40px;
            background: #2c86f8;
            border-bottom: 1px solid #D3D7DE;
            color: #fff;
            font-size: 14px;
            line-height: 40px;
            padding-left: 14px;
            display: flex;
            align-items: center;
            .icon-jiantou{
                transform: rotate(90deg);
                display: inline-block;
                cursor: pointer;
            }
            .ivu-icon-ios-undo{
                font-size: 20px;
                cursor: pointer;
            }
            span{
                font-size: 14px;
                cursor: pointer;
                margin-left: 10px;
            }
        }
    }
    .table-box {
        height: calc(~"100% - 40px");
        position: relative;
        /deep/ .ivu-scroll-wrapper {
            height: 100%;
            width: 100%;
            position: initial;
        .ivu-scroll-container {
            overflow: auto;
            height: 100%;
        }
    }
    }
    .freno-item {
        position: relative;
        list-style: none;
        padding-left: 20px;
        padding-right: 20px;
        height: 100px;
        cursor: pointer;
        display: flex;
        align-items: center;
        .ui-image {
            width: 80px;
            height: 80px;
        }
        &:hover {
            background-color: rgba(44, 134, 248, 0.2);
            .record-tools {
                display: block;

            }
        }
        .nightOut-count {
            color: #666666;
        }
        .detail {
            height: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .plate-number {
            font-size: 14px;
            font-weight: normal;
            display: inline-block;
        }
        .record-tools {
            // position: absolute;
            // right: 5px;
            margin-top: 22px;
            margin-left: 30px;
            display: none;
        }
    }
    .freno-item-hover {
        background-color: rgba(44, 134, 248, 0.2);
        .record-tools {
            display: block;
        }
    }
}
</style>
