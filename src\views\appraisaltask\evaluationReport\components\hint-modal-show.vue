<template>
  <ui-modal v-model="visible" :title="title" :footer-hide="true" :styles="styles">
    <div class="hint-head">
      <div class="hint-title">
        <span class="hint-text">全国公安视频图像数据治理工作省级评价指标</span>
      </div>
    </div>
    <div class="hint-table">
      <img src="@/assets/img/equipmentfiles/hint-img.png" alt="" />
    </div>
  </ui-modal>
</template>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  height: 700px;
}
.hint-head {
  width: 100%;
  background-color: var(--bg-sub-content);
  padding-top: 20px;
}
.hint-title {
  width: 1273px;
  margin-left: 17px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #10325d;
  border: 1px solid #085aa7;
  text-align: center;
  .hint-text {
    font-size: 26px;
    font-family: PingFang SC;
    font-weight: 800;
    color: #7bb2eb;
    line-height: 15px;
    text-shadow: 0px 2px 1px #051f4e;
  }
}
.hint-table {
  width: 100%;
  height: calc(100% - 80px);
  overflow-y: auto;
  img {
    display: inline-block;
    width: 100%;
  }
}
</style>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      visible: true,
      styles: {
        width: '7rem',
      },
    };
  },
  created() {},
  mounted() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  computed: {},
  components: {},
};
</script>
