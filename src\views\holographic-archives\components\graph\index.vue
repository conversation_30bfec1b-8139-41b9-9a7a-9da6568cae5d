<template>
  <div class="graph-box">
    <div style="height: 100%">
      <!-- 关系图谱 -->
      <AntVG6
        class="antv-g6"
        ref="antVG6Ref"
        :graph-data="graphData"
        :layout-data="{
          ...defaultLayoutData,
          ...layoutData,
        }"
        @expandGroup="expandGroup"
        @wheelzoom="wheelzoom"
        :relation-can-operate="relationCanOperate"
      />
      <ToolbarRight
        v-if="hasToolbar"
        ref="toolbarRightRef"
        @changeZoom="changeZoom"
        @fitView="fitView"
      ></ToolbarRight>
    </div>
    <div
      v-if="hasLink && hasRelations"
      class="play-btn mr-20 color-primary cursor-p"
      @click="linkToNumCube"
    >
      数智立方
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import { getRelationMap } from "@/api/number-cube";
import AntVG6 from "@/components/antv-g6/index.vue";
export default {
  props: {
    layoutData: {},
    hasToolbar: {
      type: Boolean,
      default: false,
    },
    hasLink: {
      type: Boolean,
      default: false,
    },
    relationCanOperate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      data: {},
      graphData: {}, // 图谱数据
      mainNode: {},
      hasRelations: false, // 是否有关系，控制是否可以跳转数智立方
      defaultLayoutData: {
        type: "force2",
        linkDistance: 80,
        preventOverlap: true, // 可选，必须配合 nodeSize
        nodeSize: 60, // 可选
      },
    };
  },
  async created() {
    await this.getDictData();
    if (this.relationObj && this.relationObj.graphInfo && !this.isArchive) {
      this.getRelationMap();
    }
    if (
      this.archiveObj &&
      (this.archiveObj.videoGraphInfo ||
        this.archiveObj.realNameGraphInfo ||
        this.archiveObj.vehicleGraphInfo ||
        this.archiveObj.placeGraphInfo ||
        this.archiveObj.deviceGraphInfo) &&
      this.isArchive
    ) {
      this.getRelationMap();
    }
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    async getRelationMap() {
      try {
        let { source, archiveNo } = this.$route.query;
        /**
         * 判断在哪个功能中使用图谱，路径中包含-archive，表示档案中使用的图谱
         * 1. 档案中使用系统配置 - 全息档案配置的图谱
         * 2. 数智立方中使用系统配置 - 数智立方配置的图谱
         */

        const params = {
          name: this.archiveGraphInfo.entityInfo
            ? this.archiveGraphInfo.entityInfo.name
            : "real_name_archive",
          // 后端不统一，一会需要转一会不需要
          searchKey: source === "car" ? JSON.parse(archiveNo) : archiveNo,
          instanceId: this.archiveGraphInfo.instanceId,
        };
        let res = await getRelationMap(params);
        // 有关系数据时，可以跳到数智立方
        if (res.data.relations && res.data.entitys) {
          this.hasRelations = true;
        }

        // 关系信息展示
        this.$emit("finish", res.data);
        const data = this.dealRelationData(res);
        this.mainNode = res.data.entitys.find((item) => item.isCenter);
        this.data = data;
        this.graphData = {
          nodes: [
            ...data.nodes.filter((item) => item.ext.isCenter),
            ...data.groups,
          ],
          edges: data.edges.filter(
            (item) => item.source === this.mainNode.entityId
          ),
        };

        this.$nextTick(() => {
          // if (this.hasToolbar)
          //   data.nodes.forEach((row) => {
          //     if (row.id !== this.mainNode.entityId) {
          //       this.$refs.antVG6Ref.visibleItemById(row.id, false);
          //     }
          //   });
          // else
          if (!this.hasToolbar && data.groups?.length > 1)
            requestIdleCallback(() => {
              this.$refs.antVG6Ref.fitView(10);
            });
        });
      } catch (error) {
        console.log(error);
      } finally {
        this.$refs.antVG6Ref.loadingShow({ visible: false });
      }
    },
    dealRelationData(res) {
      const groups = res.data.entityGroups.map((row) => {
        return {
          id: row.id,
          label: row.labelCn,
          group: row.label,
          groupCn: row.labelCn,
          isGroup: true,
          isExpand: false,
          ext: row,
          firstRender: true,
        };
      });
      const nodes = res.data.entitys.map((row) => {
        return {
          id: row.entityId,
          label: row.displayField,
          img: row.propertyIcon || row.icon,
          group: row.label,
          groupCn: row.labelCn,
          ext: row,
        };
      });
      const edges = res.data.relations.map((row) => {
        return {
          source: row.sourceId,
          target: row.targetId,
          label: row.labelCn
            ? `${row.labelCn} ${row.items ? `${row.items}次` : ""}`
            : null,
          ext: row,
        };
      });
      return {
        groups,
        nodes,
        edges,
      };
    },
    expandGroup(node) {
      if (node.firstRender) {
        this.$refs.antVG6Ref.updateItemById(node.id, {
          isExpand: !node.isExpand,
          firstRender: false,
        });
        return this.getGroupExpandData(node);
      }
      const nodes = this.$refs.antVG6Ref.findNeighborsNodes(node.id, "target");
      nodes.forEach((row) => {
        this.$refs.antVG6Ref.visibleItemById(row.id, !node.isExpand);
      });
      this.$refs.antVG6Ref.updateItemById(node.id, {
        isExpand: !node.isExpand,
      });
    },
    getGroupExpandData(groupNode) {
      const edges = this.data.edges.filter((edge) =>
        [edge.source, edge.target].includes(groupNode.id)
      );
      const nodes = this.data.nodes.filter((node) =>
        edges.some((edge) => [edge.source, edge.target].includes(node.id))
      );
      const newData = this.$refs.antVG6Ref.getAddNodes({
        nodes,
        edges,
      });
      this.$refs.antVG6Ref.addNodes(newData);
      this.$refs.antVG6Ref.layoutSetting.updateLayout("gForce");

      //       this.$refs.antVG6Ref.layoutSetting.updateLayout("avoidlap");
      // newData.nodes.push(groupNode);
      // this.$refs.antVG6Ref.subgraphLayout(newData, {
      //   layoutName: "radial",
      //   node: groupNode,
      //   level: 1,
      // });
      // requestIdleCallback(() => {
      //   this.$refs.antVG6Ref.layoutSetting.updateLayout("avoidlap");
      // });
    },
    changeZoom(zoom) {
      this.$refs.antVG6Ref.zoomTo(zoom / 100);
    },
    wheelzoom(zoom) {
      if (!this.hasToolbar) return;
      this.$refs.toolbarRightRef.zoom = zoom.toFixed(1) * 100;
    },
    fitView() {
      this.$refs.antVG6Ref.fitView();
    },
    linkToNumCube() {
      const { entityId, metadata } = this.mainNode;
      let query = {
        ids: JSON.stringify(this.mainNode.entityId),
        entityIdInstanceIdMap: JSON.stringify({
          [entityId]: metadata.qsdi_graph_instance_id,
        }),
        maxDepth: 1,
        type: "add",
        isRelation: true,
        source: this.$route.query.source,
      };
      this.$router.push({ name: "number-cube-info", query: query });
    },
  },
  computed: {
    ...mapGetters("systemParam", ["relationObj", "archiveObj"]),
    // 是否是档案页面
    isArchive() {
      return this.$route.path.includes("-archive");
    },
    archiveGraphInfo() {
      if (!this.isArchive) {
        return this.relationObj.graphInfo;
      }
      let { source } = this.$route.query;
      if (source === "car") {
        return this.archiveObj.vehicleGraphInfo;
      } else if (source === "video") {
        return this.archiveObj.videoGraphInfo;
      } else if (source === "place") {
        return this.archiveObj.placeGraphInfo;
      } else if (source === "people") {
        return this.archiveObj.realNameGraphInfo;
      } else {
        return this.archiveObj.deviceGraphInfo;
      }
    },
  },
  // TODO 使用watch会在页面刷新时调用两次接口，watch的目的是获取到relationObj和archiveObj，直接在created中使用async/await，测试无误后删除这里
  /* watch: {
    relationObj: {
      handler(val) {
        if (val && val.graphInfo && !this.isArchive) {
          this.getRelationMap();
        }
      },
      immediate: true,
    },
    archiveObj: {
      handler(val) {
        if (
          val &&
          (val.videoGraphInfo ||
            val.realNameGraphInfo ||
            val.vehicleGraphInfo) &&
          this.isArchive
        ) {
          this.getRelationMap();
        }
      },
      immediate: true,
    },
  }, */
  components: {
    AntVG6,
    ToolbarRight: require("@/views/number-cube/components/toolbar-right.vue")
      .default,
  },
};
</script>
<style lang="less" scoped>
.graph-box {
  width: 100%;
  height: 100%;

  /deep/.card-content {
    overflow: hidden;
    height: 100% !important;
  }
  /deep/ .ui-card {
    height: 100%;
    .card-head {
      display: none;
    }
  }
  .play-btn {
    position: absolute;
    font-size: 14px;
    top: 20px;
    right: 20px;
    z-index: 10;
  }
}
</style>
