<template>
  <div class="layout-container">
    <div class="layout-left fl" v-ui-loading="{ loading: treeLoading, tableData: treeData }">
      <ui-search-tree
        v-show="treeData.length"
        expandAll
        node-key="id"
        no-search
        :max-height="800"
        :currentNodeKey="101"
        :tree-data="treeData"
        :default-props="defaultProps"
        @selectTree="selectTree"
      >
        <template #label="{ data }">
          <span v-if="data.name === '检测概况'">
            <span class="icon-font icon-shebeilishizhuapaifenxi f-14"></span>
            {{ data.name }}
          </span>
          <span v-else>{{ data.name }}</span>
        </template>
      </ui-search-tree>
    </div>
    <div class="layout-right auto-fill" v-ui-loading="{ loading: loading, tableData: treeData }">
      <!--  导出和执行时间只有 设备检测概况 暂未封装组件    -->
      <device-statistics
        v-if="currentTree.id === 101"
        :data="statisticsData"
        @on-statistics-change="onStatisticsChange"
      >
      </device-statistics>
      <statistics v-else :data="statisticsData" @on-statistics-change="onStatisticsChange"></statistics>
      <div class="layout-right-content auto-fill">
        <!-- zdr人像轨迹 -->
        <keypersonlibrary
          :taskObj="taskData"
          :currentTree="currentTree"
          v-if="[501, 502, 503, 504, 505].includes(currentTree.id)"
        />
        <testing-overview v-if="currentTree.id === 101" :taskObj="taskData"></testing-overview>
        <international-encoding-detection
          :taskObj="taskData"
          :currentTree="currentTree"
          v-if="[102, 103, 104, 105, 106, 107, 108, 109, 110, 111].includes(currentTree.id)"
          :key="currentTree.id"
        ></international-encoding-detection>

        <!-- 车辆 -->
        <carVehicle
          :currentTree="currentTree"
          v-if="[411, 402, 403, 404, 405, 406, 407, 408, 409, 410].includes(currentTree.id)"
          :taskObj="taskObj"
          ref="inspectionrecordContent"
        />
        <!-- 车辆检测概况 -->
        <testingSituation
          :currentTree="currentTree"
          v-if="[401].includes(currentTree.id)"
          :taskObj="taskObj"
          ref="inspectionrecordContent"
        />
        <!-- 车辆新增指标 车辆卡口联网检测 车辆卡口在线监测-->
        <BayonetDetection
          :currentTree="currentTree"
          v-if="[413, 414].includes(currentTree.id)"
          :taskObj="taskObj"
          :treeData="treeData"
        />
        <!-- 档案数据检测 -->
        <FileData :currentTree="currentTree" v-if="[601, 602, 603, 604].includes(currentTree.id)" :taskObj="taskObj" />
        <!-- 视频 -->
        <videoPage
          :taskObj="taskObj"
          v-if="[201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 212].includes(currentTree.id)"
          :currentTree="currentTree"
          :key="currentTree.id"
        ></videoPage>
        <faceViewData
          :taskObj="taskData"
          :currentTree="currentTree"
          :key="currentTree.id"
          v-if="[302, 303, 304, 305, 306, 307].includes(currentTree.id)"
        ></faceViewData>
        <faceTestingOverview
          :taskObj="taskData"
          :currentTree="currentTree"
          :key="currentTree.id"
          v-if="[301].includes(currentTree.id)"
        ></faceTestingOverview>
        <capture-rationality
          :taskObj="taskObj"
          :currentTree="currentTree"
          :key="currentTree.id"
          v-if="[309, 412].includes(currentTree.id)"
        >
        </capture-rationality>
        <network-inspection
          :taskObj="taskObj"
          :currentTree="currentTree"
          :key="currentTree.id"
          v-if="[211].includes(currentTree.id)"
        >
        </network-inspection>
      </div>
    </div>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'inspectionrecord',
  components: {
    DeviceStatistics: require('./components/device-statistics').default,
    Statistics: require('./components/statistics').default,
    InternationalEncodingDetection: require('./viewbaseinfo/international-enconding-datection').default,
    TestingOverview: require('./viewbaseinfo/testing-overview').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    carVehicle: require('./carVehicle/index.vue').default,
    videoPage: require('./video').default,
    testingSituation: require('./carVehicle/testingSituation.vue').default,
    faceViewData: require('./faceviewdata').default,
    faceTestingOverview: require('./faceviewdata/face-testing-overview').default,
    CaptureRationality: require('./faceviewdata/capture-rationality').default,
    NetworkInspection: require('./video/network-inspection').default,
    BayonetDetection: require('./carVehicle/bayonetDetection').default,
    FileData: require('./fileData').default,
    keypersonlibrary: require('./keypersonlibrary').default,
  },
  props: {},
  provide() {
    return {
      obj: { id: 1 }, // 当前选中参数，参数名随意定义的可更改
    };
  },
  data() {
    return {
      //左侧树结构
      defaultProps: {
        label: 'name',
        children: 'childList',
      },
      treeData: [],
      selectKey: '0',
      currentTree: {},
      statisticsData: [],
      taskObj: {},
      loading: false,
      treeLoading: false,
      taskData: {},
    };
  },
  methods: {
    onStatisticsChange([data, { indexResults }]) {
      this.taskData = data;
      if (this.currentTree.id && this.currentTree.id !== 201 && this.currentTree.id !== 401) {
        if (indexResults && indexResults.length) {
          this.taskObj = indexResults[0];
        } else {
          this.taskObj = {
            batchId: '',
            indexId: '',
            orgCode: '',
            taskIndexId: '',
          };
        }
      } else if ((this.currentTree.id && this.currentTree.id === 201) || this.currentTree.id === 401) {
        if (indexResults && indexResults.length) {
          let batchIds = [];
          indexResults.forEach((e) => {
            batchIds.push(e.batchId);
          });
          this.taskObj = {
            ...indexResults[0],
            batchIds,
            batchId: undefined,
          };
        } else {
          this.taskObj = {
            batchIds: [],
            orgCode: '',
          };
        }
      }
    },
    selectTree(data) {
      if (data.id === this.currentTree.id) return; //防止重复点击
      this.currentTree = {}; // 同一页面触发v-if
      this.statisticsData = [];
      this.loading = true;
      this.getStatisticsList(data)
        .then(() => {
          this.currentTree = data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    async getEvaluationStatisticsTypeList() {
      try {
        this.treeLoading = true;
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getEvaluationStatisticsType);
        this.treeData = data;
        //最外层禁用
        this.treeData.map((item) => {
          this.$set(item, 'disabled', true);
        });
        this.selectTree(data[0].childList[0]);
      } catch (e) {
        console.log(e);
      } finally {
        this.treeLoading = false;
      }
    },
    async getStatisticsList(currentTree) {
      let params = { recordTypeId: currentTree.id };
      let {
        data: { data },
      } = await this.$http.get(governanceevaluation.getEvaluationRecentlyTaskList, { params });
      this.statisticsData = data;
    },
    registerModule() {
      let moduleA = {
        namespaced: true,
        state: {
          name: 123,
        },
        getters: {
          getName(state) {
            return state.name;
          },
        },
      };
      this.$store.registerModule('moduleA', moduleA);
    },
  },
  created() {
    this.getEvaluationStatisticsTypeList();
  },
};
</script>
<style lang="less" scoped>
.layout-container {
  position: relative;
  height: 100%;
  background: var(--bg-content);

  .layout-left {
    height: 100%;
    width: 250px;
    border-right: 1px solid var(--border-color);
    padding: 10px;
    @{_deep}.ui-search-tree {
      padding-top: 0;
      .el-tree {
        max-height: 100% !important;
      }
    }
  }

  .layout-right {
    position: relative;
    height: 100%;
    margin-left: 265px;
    margin-right: 20px;

    .search-container {
      display: flex;
      justify-content: space-between;
    }
  }

  .layout-right-content {
  }
}
</style>
