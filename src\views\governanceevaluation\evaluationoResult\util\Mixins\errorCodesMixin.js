import detectionResult from '@/config/api/detectionResult';

export default {
  data() {
    return {
      errorCodesOptions: [],
      //不合格原因筛选 中间变量 处理后保存在 formData.errorCodes中
      tempErrorCodes: {
        desc: [],
        reason: [],
      },
    };
  },
  created() {
    this.copySearchDataMx(this.formData);
    this.startWatch(
      '$route.query',
      () => {
        this.getQualificationList();
      },
      { deep: true, immediate: true },
    );
    this.startWatch(
      'tempErrorCodes',
      () => {
        this.watchErrorCodes();
      },
      { deep: true },
    );
  },
  methods: {
    // 获取不合格原因下拉列表
    async getQualificationList(moduleData = null) {
      try {
        let indexId = '',
          batchId = '',
          displayType = '',
          regionCode = '',
          orgCode = '';
        if (moduleData?.batchId) {
          indexId = moduleData.indexId;
          batchId = moduleData.batchId;
          displayType = moduleData.displayType;
          regionCode = moduleData.regionCode;
          orgCode = moduleData.orgCode;
        } else {
          indexId = this.$route.query.indexId;
          batchId = this.$route.query.batchId;
          displayType = this.$route.query.statisticType;
          regionCode = this.$route.query.regionCode;
          orgCode = this.$route.query.orgCode;
        }
        if (!batchId) return;
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: displayType,
          customParameters: moduleData?.batchId ? {} : this.formData,
        };
        params.orgRegionCode = params.displayType === 'REGION' ? regionCode : orgCode;
        let res = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, params);
        this.errorCodesOptions = res.data.data || [];
        this.watchErrorCodes();
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 不合格原因特殊处理
     * https://vo52iz.axshare.com/#g=1&p=%E5%AD%97%E5%B9%95%E6%A0%87%E6%B3%A8%E5%90%88%E8%A7%84%E7%8E%87_%E6%99%AE%E9%80%9A_%E9%87%8D%E7%82%B9_
     * 1、
     */
    watchErrorCodes() {
      if (this.tempErrorCodes.reason.length > 0 && this.tempErrorCodes.desc.length === 0) {
        this.formData.errorCodes = [];
        this.errorCodesOptions.forEach((item) => {
          let { code, reason } = item;
          if (this.tempErrorCodes.reason.includes(reason)) {
            this.formData.errorCodes.push(code);
          }
        });
      } else if (this.tempErrorCodes.desc.length > 0 && this.tempErrorCodes.reason.length > 0) {
        this.formData.errorCodes = this.tempErrorCodes.desc;
      } else if (this.tempErrorCodes.desc.length === 0 && this.tempErrorCodes.reason.length === 0) {
        this.formData.errorCodes = [];
      }
    },
    onChangeReason() {
      this.tempErrorCodes.desc = [];
    },
    search() {
      this.startSearch && this.startSearch(this.formData);
    },
    reset() {
      this.resetSearchDataMx(this.formData, this.search);
      this.tempErrorCodes = {
        desc: [],
        reason: [],
      };
    },
  },
  computed: {
    errorCodesOptionsReason() {
      let options = [];
      this.errorCodesOptions.forEach((item) => {
        if (!options.includes(item.reason)) {
          options.push(item.reason);
        }
      });
      return options;
    },
    errorCodesOptionsDesc() {
      let options = [];
      this.errorCodesOptions.forEach((item) => {
        if (this.tempErrorCodes.reason.includes(item.reason)) {
          options.push(item);
        }
      });
      return options;
    },
  },
};
