<template>
  <div class="card-pattern auto-fill">
    <dynamic-condition :form-item-data="formItemData" :form-data="formData" @search="startSearch" @reset="startSearch">
      <template #otherButton>
        <slot name="otherButton"></slot>
      </template>
    </dynamic-condition>
    <div class="card-pattern-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="card-pattern-content-wrap">
        <template v-for="(item, index) in cardList">
          <ui-gather-card
            class="card1"
            :list="item"
            :personTypeList="personTypeList"
            :cardInfo="cardInfo"
            :key="index"
            @detail="openDetail(item)"
          >
          </ui-gather-card>
        </template>
      </div>
    </div>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="handlePage"
      @changePageSize="handlePageSize"
    >
    </ui-page>
    <person-detail ref="captureDetail" :resultId="checkPictureParams" :interFaceName="getSecondaryPopUpData">
      <template #searchList>
        <SearchCard
          :searchNum="3"
          :checkStatus="checkStatus"
          class="mb-sm track-search"
          @startSearch="startDetailSearch"
        ></SearchCard>
      </template>
    </person-detail>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'card-pattern',
  props: {
    formItemData: {
      type: Array,
      default: () => [],
    },
    formData: {
      type: Object,
      default: () => {},
    },
    resultData: {
      type: Object,
      default: () => {},
    },
    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
    checkPictureParams: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      searchData: {},
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'total' },
        { name: '异常轨迹：', value: 'abnormal', color: 'var(--color-failed)' },
      ],
      checkStatus: [
        { name: 'url可访问', checkKey: 1 },
        { name: 'URL不可访问', checkKey: 2 },
        { name: '小图URL不可访问', checkKey: 3 },
        { name: '大图URL不可访问', checkKey: 4 },
      ],
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,
    };
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 详情
    startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    getTableList() {
      this.loading = true;
      let params = {
        searchData: this.searchData,
        pageData: {
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        },
      };
      this.$emit('startSearch', params);
    },
    handlePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    handlePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    openDetail(row) {
      this.$refs.captureDetail.show(row);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    resultData: {
      handler(val) {
        this.cardList = val[this.listKey] || [];
        this.pageData.totalCount = val.total;
        this.loading = false;
      },
      immediate: true,
    },
  },
  components: {
    UiGatherCard: require('../../components/ui-gather-card.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    personDetail: require('./person-detail.vue').default,
    SearchCard: require('./searchCard.vue').default,
  },
};
</script>

<style lang="less" scoped>
.card-pattern {
  // padding-top: 15px;
  background-color: var(--bg-content);
  //max-height: calc(100vh - 380px);
  &-content {
    position: relative;
    // margin: 20px 0 0;
    height: 100%;
    overflow-y: auto;
    &-wrap {
      width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }
  .page {
    padding-right: 0;
  }
  @{_deep} .ui-gather-card {
    width: calc(calc(100% - 30px) / 4);
  }
}
</style>
