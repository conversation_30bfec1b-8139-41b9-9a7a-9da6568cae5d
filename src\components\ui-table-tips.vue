<template>
  <div class="tips-box" ref="tipsRef">
    <p class="title mb-xs">
      <i class="icon-font icon-jinggao orange-color mr-xs f-12"></i>
      <span class="orange-color">温馨提示</span>
      <i class="icon-guanbi icon-font close fr pointer" @click="closeTips"></i>
    </p>
    <p class="">还有更多隐藏内容,</p>
    <p class="">请左右滑动查看哦！</p>
  </div>
</template>
<script>
export default {
  props: {
    xclose: Function,
  },
  data() {
    return {};
  },
  methods: {
    closeTips() {
      this.xclose();
    },
  },
  watch: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .tips-box {
    border: 1px solid var(--color-primary);
    box-shadow: none;
    .title {
      &::before {
        border-color: transparent transparent transparent var(--color-primary);
      }
      &::after {
        border-color: transparent transparent transparent var(--bg-table-tips);
      }
    }
    .orange-color {
      color: #e2872b;
    }
  }
}
.tips-box {
  position: absolute;
  top: 50%;
  width: 150px;
  transform: translateY(-50%);
  border: none;
  box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
  z-index: 999;
  background: var(--bg-table-tips);
  padding: 10px;
  color: var(--color-input);
  border-radius: 5px;
  .text {
    width: 108px;
  }
  .title {
    position: relative;
    .close {
      position: absolute;
      top: 0px;
      right: 0px;
      font-size: 12px;
      color: #768192;
    }
    &::before {
      position: absolute;
      content: '';
      display: inline-block;
      border: 10px solid;
      border-color: transparent;
      top: 20px;
      right: -29px;
    }
    &::after {
      position: absolute;
      content: '';
      display: inline-block;
      border: 10px solid;
      right: -28px;
      top: 20px;
      border-color: transparent transparent transparent var(--bg-table-tips);
    }
  }
  .orange-color {
    color: #e99e53;
  }
}
</style>
