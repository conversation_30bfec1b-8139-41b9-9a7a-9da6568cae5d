<template>
  <ui-modal title="重设记录" v-model="visible" :footer-hide="true">
    <ul class="f-14 base-text-color result-ul">
      <li class="mt-md">
        <span class="mr-sm"> <i class="icon-font icon-yuanshizhi f-12 mr-xs"></i>原始值:</span>
        <span class="font-active-color">{{ activeJson.oldValue }}</span>
      </li>
      <li class="mt-md">
        <span class="mr-sm"><i class="icon-font icon-gengxinzhi f-12 mr-xs"></i>更新值:</span>
        <span class="font-warning">{{ activeJson.newValue }}</span>
      </li>
      <li class="mt-md">
        <span class="mr-sm"><i class="icon-font icon-gengxinyiju f-12 mr-xs"></i>更新依据:</span>
        <span class="font-active-color">{{ activeJson.message }}</span>
      </li>
    </ul>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {},
    activeRecord: {
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      activeJson: {
        newValue: '',
        oldValue: '',
        message: '',
      },
    };
  },
  computed: {},
  mounted() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (val) {
        let activeJson = JSON.parse(this.activeRecord.updatePropertyJson);
        if (activeJson) Object.assign(this.activeJson, activeJson);
      } else {
        this.activeJson = {
          newValue: '',
          oldValue: '',
          message: '',
        };
      }
    },
    value(val) {
      console.log(this.activeRecord, 'this.activeRecord');
      this.visible = val;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.result-ul {
  padding: 0 34px 40px;
}
</style>
