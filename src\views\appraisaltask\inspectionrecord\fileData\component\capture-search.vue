<template>
  <div class="base-search">
    <div v-if="isImageModel">
      <ui-label class="fl" label="抓拍时间" :width="70">
        <div class="date-picker-box">
          <DatePicker
            class="input-width mb-md"
            v-model="imageSearchData.beginTime"
            type="datetime"
            placeholder="请选择开始时间"
            :options="startTimeOption"
            confirm
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, imageSearchData, 'beginTime')"
          ></DatePicker>
          <span class="ml-sm mr-sm">--</span>
          <DatePicker
            class="input-width"
            v-model="imageSearchData.endTime"
            type="datetime"
            placeholder="请选择结束时间"
            :options="endTimeOption"
            confirm
            @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, imageSearchData, 'endTime')"
          ></DatePicker>
        </div>
      </ui-label>

      <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
        <select-camera @pushCamera="pushCamera" :device-ids="imageSearchData.deviceIds"></select-camera>
      </ui-label>
      <ui-label class="fl ml-lg" label="抓拍类型" :width="70">
        <Select v-model="imageSearchData.urlAvailableStatus" style="width: 200px">
          <Option value="">全部</Option>
          <Option value="1">URL可访问</Option>
          <Option value="2">大图URL不可访问</Option>
          <Option value="3">小图URL不可访问</Option>
        </Select>
      </ui-label>
    </div>
    <div class="file-model" v-else>
      <ui-label class="inline mr-lg fl" label="关键词" :width="55">
        <Input class="input-width" placeholder="请输入姓名/证件号" v-model="fileSearchData.keyword"></Input>
      </ui-label>
      <ui-label class="inline mr-lg fl" label="轨迹总数" :width="70">
        <Input class="small-width" placeholder="" v-model="fileSearchData.trackTotalStart"></Input>
        -
        <Input class="small-width" placeholder="" v-model="fileSearchData.trackTotalEnd"></Input>
      </ui-label>
      <ui-label class="inline mr-lg fl" label="异常轨迹数量" :width="100">
        <Input class="small-width" placeholder="" v-model="fileSearchData.trackUnusualStart"></Input>
        -
        <Input class="small-width" placeholder="" v-model="fileSearchData.trackUnusualEnd"></Input>
      </ui-label>
    </div>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button
        type="default"
        class="mr-lg"
        @click="resetSearchDataMx(isImageModel ? imageSearchData : fileSearchData, startSearch)"
        >重置</Button
      >
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    isImageModel: {
      default: () => true,
    },
    modular: {
      default: 1,
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      imageSearchData: {
        deviceIds: [],
        beginTime: '',
        endTime: '',
        urlAvailableStatus: '',
      },
      fileSearchData: {
        keyword: '',
        trackTotalEnd: '',
        trackTotalStart: '',
        trackUnusualEnd: '',
        trackUnusualStart: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.imageSearchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.imageSearchData.beginTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    this.copySearchDataMx(this.isImageModel ? this.imageSearchData : this.fileSearchData);
  },
  methods: {
    pushCamera(list) {
      this.imageSearchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.$emit('startSearch', this.isImageModel ? this.imageSearchData : this.fileSearchData);
    },
  },
  watch: {},
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.small-width {
  width: 70px;
}
.base-search {
  overflow: hidden;
  padding-bottom: 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
</style>
