<template>
  <common-form v-bind="$props" ref="formData">
    <template #after>
      <div class="color-failed">
        <span
          >备注：手动新增的指标仅用于统计已有指标（一个或多个）的检测结果，在【评测任务】环节选择检测任务和检测指标，系统把多个指标的检测结果融合分析统计，得到最终结果！</span
        >
      </div>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'basic-full-dir',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {};
  },
  methods: {
    async validate() {
      try {
        return await this.$refs.formData.validate();
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less"></style>
