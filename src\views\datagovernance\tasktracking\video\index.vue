<template>
  <div class="view">
    <div class="flexUi" v-if="list.length > 0">
      <div class="item item1">
        <span :class="'icon-font icon-' + video[0].icon1"></span>
        <i class="icon-font icon-chenggong success"></i>
        <p class="title">{{ video[0].title }}</p>
        <p class="desc">
          最近检测：
          <span class="cyan">{{ list[0].accessDataCount || 0 }}</span>
        </p>
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <!-- <div class="arrows">
        <img class="arrow" src="@/assets/img/arrow.png" alt="" srcset="" />
      </div> -->

      <div class="group1">
        <div class="groupItem">
          <span :class="'icon-font icon-' + video[1][0].icon1" style="font-size: 25px"></span>
          <p class="title">{{ video[1][0].title }}</p>
          <p class="desc">
            检测数量：
            <span class="cyan">{{ list[1].accessDataCount || 0 }}</span>
          </p>
          <p class="desc">
            异常数据：
            <span class="red">{{ list[1].existingExceptionCount }}</span>
          </p>
          <p class="desc">
            异常占比：
            <span class="red">{{ (list[1].existingExceptionRate * 100).toFixed(2) }}%</span>
          </p>
          <Poptip class="config" trigger="hover" content="查看检测结果" placement="top">
            <i class="icon-font icon-chakanjiancejieguo" @click="showModal(1)"></i>
          </Poptip>
        </div>
        <div class="groupItem">
          <span :class="'icon-font icon-' + video[1][1].icon1" style="font-size: 18px"></span>
          <p class="title">{{ video[1][1].title }}</p>
          <p class="desc">
            检测数量：
            <span class="cyan">{{ list[2].accessDataCount || 0 }}</span>
          </p>
          <p class="desc">
            异常数据：
            <span class="red">{{ list[2].existingExceptionCount }}</span>
          </p>
          <p class="desc">
            异常占比：
            <span class="red">{{ (list[2].existingExceptionRate * 100).toFixed(2) }}%</span>
          </p>
          <Poptip class="config" trigger="hover" content="查看检测结果" placement="top">
            <i class="icon-font icon-chakanjiancejieguo" @click="showModal(2)"></i>
          </Poptip>
        </div>
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <div class="item item3">
        <i class="icon-font icon-shujushuru leftIcon"></i>
        <span :class="'icon-font icon-' + video[2].icon1"></span>
        <p class="title">{{ video[2].title }}</p>
        <p class="desc">
          检测数量：
          <span class="cyan">{{ list[3].accessDataCount || 0 }}</span>
        </p>
        <p class="desc">
          异常数量：
          <span class="red">{{ list[3].existingExceptionCount }}</span>
        </p>
        <p class="desc">
          异常占比：
          <span class="red">{{ (list[3].existingExceptionRate * 100).toFixed(2) }}%</span>
        </p>
        <Poptip class="config" trigger="hover" content="查看检测结果" placement="top">
          <i class="icon-font icon-chakanjiancejieguo" @click="showModal(3)"></i>
        </Poptip>
      </div>
      <connecting-arrow :connectingOptions="arrowsInfo"></connecting-arrow>

      <div class="item item4">
        <span :class="'icon-font icon-' + video[3].icon1"></span>
        <p class="title">{{ video[3].title }}</p>
        <p class="desc">
          检测数量：
          <span class="cyan">{{ list[4].accessDataCount || 0 }}</span>
        </p>
        <p class="desc">
          异常数量：
          <span class="red">{{ list[4].existingExceptionCount }}</span>
        </p>
        <p class="desc">
          异常占比：
          <span class="red">{{ (list[4].existingExceptionRate * 100).toFixed(2) }}%</span>
        </p>
        <Poptip class="config" trigger="hover" content="查看检测结果" placement="top">
          <i class="icon-font icon-chakanjiancejieguo" @click="showModal(4)"></i>
        </Poptip>
      </div>
    </div>

    <!-- <dialogView ref="modal" /> -->
    <now-test ref="nowtest"></now-test>
    <history-test ref="historyView"></history-test>
    <subtitle-test ref="subtitletest"></subtitle-test>
    <out-put ref="outputView"></out-put>
  </div>
</template>
<script>
import { video } from './video';
import api from '@/config/api/vedio-threm.js';
// import dialog from './dialog';
export default {
  name: 'videoView',
  props: {},
  data() {
    return {
      video: video,
      list: [],
      arrowsInfo: {
        width: '170px',
        height: '8px',
      },
    };
  },
  created() {},
  async mounted() {
    await this.init();
    // 任务主题环形图中心文字点击跳转会直接打开数据输出弹窗
    if (`${this.$route.query.isExport}` === 'true' && this.$route.query.id == 4) {
      this.showModal(4);
    }
  },
  methods: {
    init() {
      this.$http.get(api.queryComponentStatisticsByTopicId + '4').then((res) => {
        if (res.data.code == 200) {
          this.list = res.data.data;
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
    //获取视频列表接口
    getVedioPage() {
      let data = {};
      data.reasonTypes = 6;
      data.pageNumber = this.searchData.pageNum;
      data.pageSize = this.searchData.pageSize;
      this.$http.post('api.queryVideoResultPageList', data).then((res) => {
        console.log(res);
      });
    },
    showModal(val) {
      // this.$refs.modal.showModal(val);
      if (val == 1) {
        //
        this.$refs.nowtest.showModal(val);
      }

      if (val == 2) {
        //
        this.$refs.historyView.showModal(val);
      }

      if (val == 3) {
        //
        this.$refs.subtitletest.showModal(val);
      }
      if (val == 4) {
        //
        this.$refs.outputView.showModal(val);
      }
    },
  },
  watch: {
    $route: {
      handler() {
        // 任务主题环形图中心文字点击跳转会直接打开数据输出弹窗
        if (`${this.$route.query.isExport}` === 'true' && this.$route.query.id == 4) {
          this.showModal(4);
        }
      },
      deep: true,
    },
  },
  components: {
    // dialogView: dialog,
    NowTest: require('./components/now-test').default,
    HistoryTest: require('./components/history').default,
    SubtitleTest: require('./components/ods').default,
    OutPut: require('./components/output').default,
    ConnectingArrow: require('./connecting-arrow').default,
  },
};
</script>
<style lang="less" scoped>
.view {
  position: relative;
  height: 100%;
  padding: 0 60px;
  .flexUi {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 50%;
    transform: translateY(-75%);

    .item {
      position: relative;
      width: calc(22.5% - 100px);
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 20px 10px 20px 50px;
      color: #fff;
    }
    .groupItem {
      position: relative;
      min-height: 92px;
      background: #0f2f59;
      border: 1px solid var(--color-primary);
      border-radius: 4px;
      padding: 10px 10px 20px 50px;
      color: #fff;
      margin-top: 10px;
    }
    .leftIcon {
      position: absolute;
      color: var(--color-primary);
      font-size: 30px;
      left: 12px;
      top: 16px;
    }
    .success {
      position: absolute;
      color: #43bf00;
      font-size: 14px;
      right: 10px;
      top: 10px;
    }

    .config {
      font-size: 10px;
      color: #56789c;
      position: absolute;
      right: 10px;
      bottom: 10px;
      .icon-font {
        font-size: 10px;
      }
    }

    .title {
      font-size: 14px;
    }

    .desc {
      margin-top: 5px;
      font-size: 14px;
      color: #99a4af;
    }

    .group1 {
      width: calc(22% - 100px);
      padding: 0 10px 10px 10px;
      border: 1px solid var(--color-primary);
    }
  }
}

/deep/ .ivu-poptip-popper {
  width: auto;
  min-width: auto;
  left: -37px;
}
/deep/ .ivu-poptip-body-content-inner {
  color: #fff !important;
}

.arrows {
  position: relative;
  width: 150px;
  height: 7.5px;
  background: var(--color-primary);
  margin-left: -6px;
  .arrow {
    position: absolute;
    right: -10px;
    top: -4.4px;
  }
}

span.icon-font {
  position: absolute;
  background-image: linear-gradient(#0f84e9, #12a4c9);
  background-clip: text;
  left: 16px;
  font-size: 20px;
  -webkit-text-fill-color: transparent;
}
.red {
  color: #bc3c19;
}
.green {
  color: #13b13d;
}
.orange {
  color: #f18a37;
}
.cyan {
  color: var(--color-bluish-green-text);
}

.icon-renwuzhuizong:hover {
  color: #56789c !important;
  &:before {
    content: '\e7a5';
  }
}
</style>
