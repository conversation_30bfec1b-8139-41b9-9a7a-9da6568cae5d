import { getPersonTrajectoryDetail } from "@/api/modelMarket";
import { baseTrackTypeMap } from "../../../characteristic-tricks/casetoperson/components/trackTypeMap";

export default async function getPersonTrajectoryDetailHOC({
  dataId,
  dataType,
}) {
  const res = await getPersonTrajectoryDetail({ dataId, dataType });
  const attr = baseTrackTypeMap[dataType].detailAttr;
  if (!attr) return {};
  return res.data[attr] || {};
}
