<!--
    * @FileDescription: 地图-资源图层-浮动
    * @Author: H
    * @Date: 2023/03/21
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="dom-wrapper">
        <div class="dom">
            <div class="hover-content">{{ name }}</div>
            <div class='hover-bottom'></div>
        </div>
    </div>
</template>
<script>
export default {
    components: {  },
    props: {
        name: {
            type: String,
            default: '暂未挂载'
        }
    },
    watch: {
    },
    computed: {
    },

    data () {
        return {
        }
    },
    methods: {
    }
}
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
//   padding: 10px 28px 25px 0;
padding: 10px 28px 0px 0;
  height: 100%;
}
.dom {
  width: 300px;
  height: 40px;
  position: relative;
  display: flex;
    justify-content: center;
    align-items: end;
    .hover-content{
        background: #fff;
        padding: 5px;
        border-radius: 5px;
        box-shadow: 0px 3px 5px 0px rgba(147,171,206,0.7);
        display: inline-block;
    }
    .hover-bottom{
        background: #fff;
    }
    .hover-bottom:before,
    .hover-bottom:after {
        content: '';
        display: block;
        border-width: 8px;
        position: absolute;
        bottom: -16px;
        left: 146px;
        border-style: solid dashed dashed;
        border-color: #ffffff transparent transparent;
        font-size: 0;
        line-height: 0;
    }
}

// .dom:before,
// .dom:after {
//   content: '';
//   display: block;
//   border-width: 8px;
//   position: absolute;
//   bottom: -16px;
//   left: 146px;
//   border-style: solid dashed dashed;
//   border-color: #ffffff transparent transparent;
//   font-size: 0;
//   line-height: 0;
// }
</style>
