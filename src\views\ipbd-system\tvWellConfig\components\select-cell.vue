<template>
    <div class="tvwall-set-condition">
        <div class="tsc-cur" @click="handleSelect">
            <span class="tsc-cur-title">当前规格: {{ atPresentMsg }}</span>
            <span class="tsc-cur-right" :class="{'tsc-cur-unflod': selectCell}">
                <i class="iconfont icon-jiantou"></i>
            </span>
        </div>
        <div class="tsc-options" v-show="selectCell">
            <div class="tsc-option" v-for="(item, index) in conditionList" :key="index" @click="handleSelectCell(item,index)">
                <span>{{item.row + '×' + item.col }}</span>
                <i class="iconfont icon-shanchu" @click="handleDeleCell(item,index)"></i>
            </div>
            <div class="tsc-option tsc-option-add">
                <span @click="handleAddCell"><i class="iconfont icon-jia"></i>添加规格</span>
            </div>
        </div>
        <ui-modal v-model="addModal" title="添加规格" @onOk="handleOnok" @onCancel="handleCancel">
            <div class="content">
                <Form ref="formline" :model="formInfo" :rules="ruleInline">
                    <FormItem label="行数" :label-width="60" prop="row">
                        <el-input-number v-model="formInfo.row" :min="1" :max="10"></el-input-number>
                    </FormItem>
                    <FormItem label="列数" :label-width="60" prop="col">
                        <el-input-number v-model="formInfo.col" :min="1" :max="10"></el-input-number>
                    </FormItem>
                </Form>
            </div>
        </ui-modal>
        <ui-modal v-model="hintModal" title="提示" @onOk="handleSure" @onCancel="handleCancel">
            <p><Icon type="md-alert" />是否将新建的规格应用到当前模版？（切换规格后将会丢失当前的模版配置）</p>
        </ui-modal>
    </div>
</template>
<script>
export default {
    data() {
        return {
            selectCell: false,
            addModal: false,
            hintModal: false,
            formInfo:{
                row: '',
                col: '' 
            },
            ruleInline: {
                row: [{ required: true, message: '请输入行数', trigger: 'blur' }],
                col: [{ required: true, message: '请输入列数', trigger: 'blur' }]
            },
            conditionList: [
                {'row': 2, 'col': 2}
            ],
            atPresentMsg: '2×2'
        }
    },
    methods: {
        init(data) {
            let addOrNot = true;
            this.conditionList.forEach(item => {
                if(item.row == data.row && item.col == data.col){
                    addOrNot = false
                }
            })
            if(addOrNot){
                this.conditionList.push(
                    {'row': data.row, 'col': data.col}
                )
            }
            this.atPresentMsg = data.row + '×' + data.col;
        },
        handleSelect() {
            this.selectCell = !this.selectCell;
        },
        handleAddCell() {
            this.addModal = true;
        },
        handleOnok() {
            this.addModal = false;
            this.hintModal = true;
        },
        handleSure() {
            this.conditionList.push(
                {'row': this.formInfo.row, 'col': this.formInfo.col}
            )
            this.$emit('cellShow', {'row': this.formInfo.row, 'col': this.formInfo.col})
            this.hintModal = false;  
        },
        handleSelectCell(item, index) { 
            this.atPresentMsg = item.row + '×' + item.col;
            this.$emit('cellShow', {'row': item.row, 'col': item.col})
        },
        handleDeleCell(item, index) {
            this.conditionList.splice(index, 1)
        },
        handleCancel() {

        }
    }
}
</script>
<style lang="less" scoped>
.tvwall-set-condition{
    width: 140px;
    height: 30px;
    z-index: 99;
    .tsc-cur{
        width: 140px;
        height: 30px;
        border: 1px solid #a5b0b64f;
        display: flex;
        cursor: pointer;
        .tsc-cur-title{
            margin-left: 5px;
            border-right: 1px solid #a5b0b64f;
            height: 100%;
            display: inline-block;
            line-height: 30px;
            width: 80%;
        }
        .tsc-cur-right{
            height: 100%;
            display: inline-block;
            padding: 0 5px;
            .icon-jiantou {
                font-size: 20px;
            }
        }
        .tsc-cur-unflod{
            transform: rotate(180deg);
        }
    }
    .tsc-options{
        width: 137px;
        border: 1px solid #a5b0b64f;
        border-top: none;
        box-shadow: 5px 5px 0 rgba(34, 34, 34, 0.1);
        z-index: 99;
        .tsc-option{
            height: 35px;
            width: 100%;
            background: #ffffff;
            border-bottom: 1px solid #a5b0b64f;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 10px;
            .icon-shanchu{
                display: none;
            }
            &:hover{
                color: #38aef3;
                .icon-shanchu{
                    display: block;
                }
            }
        }
        .tsc-option-add{
            color: #38aef3;
            cursor: pointer;
        }
    }
}
/deep/ .ivu-modal{
    width: auto !important;
}
/deep/ .ivu-icon-md-alert{
    color: #F29F4C;
    font-size: 16px;
}
</style>
