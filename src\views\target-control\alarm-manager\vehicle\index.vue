<template>
  <div class="container">
    <!-- 搜索 -->
    <searchForm
      v-show="!$route.query.noSearch"
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="alarm"
      :compareType="compareType"
      :radioList="radioList"
      :taskList="taskList"
    >
      <query ref="slotQuery" />
    </searchForm>

    <!-- 操作栏 -->
    <div class="operate" v-show="tableList.length">
      <Checkbox
        v-model="allCheckStatus"
        :indeterminate="indeterminate"
        @click.native="allChecked()"
        >全选</Checkbox
      >
      <Dropdown style="margin-left: 20px" @on-click="batchStatus">
        <Button type="primary">
          批量处理
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="1">设为有效</DropdownItem>
          <DropdownItem :name="2">设为无效</DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>

    <!-- 列表 -->
    <div class="list">
      <vehicleCard
        class="alarnRow"
        v-for="(item, index) in tableList"
        :key="index"
        :compareType="compareType"
        @click.native="detailFn(item, index)"
        @collection="collection"
        @refresh="tableListFn()"
        :alarmInfo="item"
        @singleChecked="singleChecked"
      />

      <!-- 空占位，保证数据居左 -->
      <div
        class="alarnRow"
        v-for="(item, index) of 5 - (15 % 5)"
        :key="index + 'a'"
      ></div>
      <ui-loading v-if="loading"></ui-loading>
      <ui-empty v-if="tableList.length === 0 && !loading"></ui-empty>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <vehicleDetail
      v-if="detailShow"
      :tableList="tableList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      ref="vehicleDetail"
      @close="close"
      :compareType="compareType"
    />
    <webSocket @sendInfo="sendInfo" />
  </div>
</template>
<script>
import { queryParamDataByKeys } from "@/api/config";
import {
  taskQueryList,
  vehiclePageList,
  batchHandleVehicle,
} from "@/api/target-control";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import vehicleCard from "./components/vehicle-card.vue";
import vehicleDetail from "./components/vehicle-detail.vue";
import webSocket from "@/components/webSocket.vue";
import { commonMixins } from "@/mixins/app.js";
import { vehicleAlarmSearch } from "@/api/wisdom-cloud-search";

export default {
  components: {
    searchForm,
    query,
    vehicleCard,
    vehicleDetail,
    webSocket,
  },
  mixins: [commonMixins], //全局的mixin
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    // 是否从全景智叟进入
    isCloudSearch: {
      type: Boolean,
      default: false,
    },
    // 全景智叟 搜索关键词
    keyWords: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      radioList: [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
      queryParam: {
        selectDeviceList: [],
      },
      status: "",
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      detailShow: false,
      taskList: [],
      tableList: [],
      tableIndex: 0,
      allCheckStatus: false,
      indeterminate: false,
      alarmInfo: {},
      alarmConfigInfo: {},
      loading: true,
    };
  },
  watch: {
    tableList: {
      handler(val) {
        let checkNum = val.filter((item) => item.checked).length;
        let tableNum = val.length;

        if (checkNum == tableNum && checkNum.length != 0) {
          this.allCheckStatus = true;
          this.indeterminate = false;
        } else {
          if (checkNum == 0) {
            this.allCheckStatus = false;
            this.indeterminate = false;
          } else {
            this.allCheckStatus = true;
            this.indeterminate = true;
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  async mounted() {
    //#region 初始化配置信息，警情级别 & 布控任务
    await queryParamDataByKeys(["ICBD_TARGET_CONTROL"]).then((res) => {
      if (res.data.length) {
        this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
      }
    });
    await taskQueryList({ compareType: this.compareType }).then((res) => {
      this.taskList = res.data;
    });
    //#endregion
    this.tableListFn();
  },
  methods: {
    /**
     * @description: 获取查询参数，并进行调整
     */
    getQueryParams() {
      /**
       * 参数来源：
       * 1. searchForm组件，普通搜索条件：this.$refs.searchForm.getQueryParams()
       * 2. query组件，高级检索条件：this.$refs.slotQuery.getQueryParams()
       * 3. $route.query，路由携带的参数
       * 4. 当前组件的参数：页码信息：this.params
       */
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
      };
      if (data.operationType == 99) {
        data.operationType = null;
      }
      data.simScore = data.simScore / 100;
      // 从工作台-报警过来不需要时间查询
      if (this.$route.query.noDate) {
        data.alarmTimeB = null;
        data.alarmTimeE = null;
      }
      return data;
    },

    /**
     * @description: 获取报警列表
     */
    tableListFn() {
      this.loading = true;
      this.tableList = [];
      // 全景智叟来的搜索
      if (this.isCloudSearch) {
        // 拿一下时间信息
        let param = this.getQueryParams();
        let params = {
          et: param.alarmTimeE,
          keywords: this.keyWords,
          pageNumber: param.pageNumber,
          pageSize: param.pageSize,
          st: param.alarmTimeB,
        };
        vehicleAlarmSearch(params)
          .then((res) => {
            this.tableList = res.data.entities;
            this.total = res.data.total;
            this.tableList.forEach((item) => {
              this.$set(item, "checked", false);
              let info = this.alarmConfigInfo.alarmLevelConfig.find(
                (ite) => ite.alarmLevel == item.taskLevel
              );
              item.bgIndex = Number(info.alarmColour);
            });
          })
          .finally(() => {
            this.loading = false;
          });
        return;
      }
      vehiclePageList(this.getQueryParams())
        .then((res) => {
          this.tableList = res.data.entities;
          this.total = res.data.total;
          this.tableList.forEach((item) => {
            this.$set(item, "checked", false);
            let info = this.alarmConfigInfo.vehicleAlarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * @description: 收藏
     */
    collection() {
      this.tableListFn();
    },

    sendInfo(row) {
      // this.tableListFn()
    },

    /**
     * @description: 全选 | 全不选
     * @return {*}
     */
    allChecked() {
      this.tableList.forEach((item) => {
        item.checked = !this.allCheckStatus;
      });
    },

    /**
     * @description: 批量设置状态
     * @param {number} name 状态
     */
    batchStatus(name) {
      let list = this.tableList.filter((item) => item.checked);
      let arr = list.map((item) => {
        return { alarmTime: item.alarmTime, alarmId: item.alarmId };
      });
      let param = {
        alarmRecordSimpleForms: arr,
        operationType: name,
      };
      batchHandleVehicle(param).then((res) => {
        this.$Message.success(res.data);
        this.tableListFn();
      });
    },

    // 单选
    singleChecked(flag) {},

    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.$refs.slotQuery.reset();
      this.query();
    },

    /**
     * @description: 改变页码
     * @param {number} pageNumber 页码
     */
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },

    /**
     * @description: 改变每页数量
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },

    /**
     * @description: 打开详情弹框
     * @param {object} row 当前报警信息
     * @param {number} index 当前报警排序
     */
    detailFn(row, index) {
      this.tableIndex = index;
      this.detailShow = true;
      this.alarmInfo = row;
    },

    /**
     * @description: 关闭详情弹框
     */
    close() {
      this.detailShow = false;
      this.tableListFn();
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
  }
  .list {
    flex: 1;
    display: flex;
    /* flex-direction: row; */
    flex-flow: wrap;
    // justify-content: flex-start;
    justify-content: space-between;
    overflow: auto;
    position: relative;
    .alarnRow {
      // width: 20%;
      // width: calc( 20% - 10px);
      width: ~"calc(20% - 10px)";
      margin-bottom: 12px;
      cursor: pointer;
      // margin-right: 10px;
    }
  }
}
</style>
