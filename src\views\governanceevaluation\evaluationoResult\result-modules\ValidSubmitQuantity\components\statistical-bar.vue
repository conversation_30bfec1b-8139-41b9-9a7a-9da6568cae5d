<template>
  <div class="statistical-bar">
    <div
      v-for="(item, index) in staticsList"
      :key="index"
      :class="['statistical-bar-item', 'base-text-color', !!item.name ? 'flex-center' : '']"
      :style="{ 'background-color': item.liBg, 'box-shadow': item.boxShadow }"
    >
      <template v-if="!!item.name">
        <div class="statistical-bar-info">
          <div class="mr-20">
            <i :class="['icon-font', 'f-50', 'icon-bg', item.icon]" :style="{ background: item.iconColor }"></i>
          </div>
          <div>
            <p class="f-12 mr-xs">{{ item.name }}</p>
            <p class="f-18 statistical-bar-info-rate" :style="{ color: item.textColor }">{{ item.value }}</p>
            <p v-if="item.type == 'percentage'">
              <i
                :class="[
                  'icon-font',
                  qualifiedVal === '1' ? 'icon-dabiao' : 'icon-budabiao',
                  qualifiedVal === '1' ? 'font-green' : 'font-red',
                ]"
              ></i>
            </p>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="left mr-20">
          <i :class="['icon-font', 'f-50', 'icon-bg', item.icon]" :style="{ background: item.iconColor }"></i>
        </div>
        <div class="center">
          <p v-for="(countItem, countIndex) in item.counts" :key="'countIndex' + countIndex">
            <span class="mr-20 count-item-label f-12">{{ countItem.label }}</span>
            <countTo :startVal="0" :endVal="countItem.value || 0" :duration="3000"></countTo>
          </p>
        </div>
        <div class="split-line"></div>
        <div class="right">
          <p class="mb-sm f-12">{{ item.res.title }}</p>
          <p class="f-16" :style="{ color: item.res.color[item.res.qualified - 1] }">{{ item.res.value }}</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'statistical-bar',
  props: {
    staticsList: {
      type: Array,
      default: () => [],
    },
    qualifiedVal: {
      type: String,
      default: '1',
    }, // 判断是否达标
  },
  components: {
    countTo: require('vue-count-to').default,
  },
};
</script>

<style lang="less" scoped>
.statistical-bar {
  width: 100%;
  display: flex;
  justify-content: flex-start;
  &-item {
    position: relative;
    display: flex;
    align-items: center;
    width: calc(calc(100% - 30px) / 4);
    //height: 102px;
    margin-right: 10px;
    padding: 15px;
    border-radius: 4px;
    &:not(&:last-child) {
      margin-right: 10px;
    }
  }
  &-info {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    &-rate {
      font-weight: bold;
    }
  }
  .center {
    flex: 1;
    p:not(p:last-child) {
      margin-bottom: 10px;
    }
    //&:after{
    //  content: '';
    //  display: inline-block;
    //  padding: 10px 1px;
    //  background-color: #1D727C;
    //}
  }
  .count-item-label {
    display: inline-block;
    width: 102px;
  }
  .split-line {
    width: 1px;
    height: calc(100% - 20px);
    margin: 10px 10px 10px 25px;
    //padding: 10px 0;
    background-color: var(--border-color);
    //border-right: 1px solid #1D727C;
  }
  .icon-bg {
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .mr-20 {
    margin-right: 20px;
  }
  .f-50 {
    font-size: 50px;
  }
  .flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .icon-budabiao,
  .icon-dabiao {
    position: absolute;
    right: 10px;
    font-size: 14px;
    cursor: default;
  }
}
</style>
