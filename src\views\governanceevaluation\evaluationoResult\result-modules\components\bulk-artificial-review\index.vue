<template>
  <ui-modal v-model="visible" :title="title" :styles="{ width: '8rem' }" footer-hide>
    <div class="detail-wrap" v-ui-loading="{ loading: tableLoading, tableData: tableData }">
      <div class="detail-wrap-content" v-if="visible">
        <bulk-videos :device-list="tableData" :visible="visible" :showReviewBox="true" v-bind="$attrs" ref="bulkVideos">
        </bulk-videos>
      </div>
      <div class="detail-footer" v-if="!!tableData.length">
        <Button v-if="showPreButton" type="primary" @click="preReview">上一批</Button>
        <Button type="primary" class="ml-lg" @click="artificial('cur')" :loading="saveLoading">确定复核结果</Button>
        <Button type="primary" class="ml-lg" @click="artificial('next')" v-if="showNextButton" :loading="saveLoading"
          >确定复核结果并下一条</Button
        >
        <Button v-if="showNextButton" type="primary" class="ml-lg" @click="nextReview">下一批</Button>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import vedio from '@/config/api/vedio-threm';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'base-review',
  props: {
    title: {
      type: String,
      default: '人工复核',
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      pageData: {
        pageNum: 1,
        pageSize: 8,
      },
      tableData: [],
      tableLoading: false,
      totalCount: 0,
      renderFunc: null,
      saveLoading: false,
    };
  },
  computed: {
    showPreButton() {
      return this.pageData.pageNum > 1;
    },
    showNextButton() {
      return this.pageData.pageNum < Math.ceil(this.totalCount / this.pageData.pageSize);
    },
  },
  methods: {
    async getTableData() {
      this.tableLoading = true;
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        let { data } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.totalCount = data.data.total;
        this.tableData = data.data.entities;
      } catch (error) {
        console.log(error);
      } finally {
        this.tableLoading = false;
      }
    },
    async preReview() {
      this.pageData.pageNum--;
      this.getTableData();
    },
    nextReview() {
      this.pageData.pageNum++;
      this.getTableData();
    },
    async artificial(str = '') {
      this.saveLoading = true;
      let { reviewVideoObj } = this.$refs.bulkVideos;
      // 只要 reviewVideoObj[item.id] 对象中  qualified ||  reason有值，则代表 需要调接口更改信息
      let ids = [];
      Object.keys(reviewVideoObj).map((key) => {
        let { qualified, reason } = reviewVideoObj[key];
        if (qualified || reason) {
          ids.push(key);
        }
      });
      let chnageArr = this.tableData.filter((item) => {
        return ids.includes(String(item.id));
      });
      let data = {
        dataList: chnageArr.map((item) => {
          return {
            id: item.id,
            qualified: reviewVideoObj[item.id].qualified,
            reason: reviewVideoObj[item.id].reason,
            taskIndexId: item.taskIndexId,
            deviceId: item.deviceId,
            indexType: this.$route.query.indexType,
          };
        }),
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheckBatch, data);
        this.$emit('update');
        this.$Message.success(res.data.msg);
        // 确定复核并下一条  触发
        if (str) {
          str === 'next' ? this.nextReview() : this.getTableData();
        }
      } catch (err) {
        console.log(err);
      } finally {
        this.saveLoading = false;
      }
    },
    // 清除视频流
    async clearVideo() {
      // 重点|普通 - 历史|实时 - 视频可调阅率  关闭窗口时，需要调用
      let indexTypeArr = [
        'VIDEO_HISTORY_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_PLAYING_ACCURACY',
      ];
      if (!indexTypeArr.includes(this.$route.query.indexType)) return;
      let arr = this.$util.common.deepCopy(this.tableData) || [];
      if (arr.length === 0) return;
      try {
        arr.forEach(async (item) => {
          await this.$http.post(vedio.stop + item.deviceId);
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        this.clearVideo();
        this.tableData = [];
        this.pageData = {
          pageNum: 1,
          pageSize: 8,
        };
      } else {
        this.getTableData();
      }
    },
  },
  components: {
    BulkVideos: require('./components/bulk-videos').default,
  },
};
</script>

<style lang="less" scoped>
.detail-wrap {
  width: 100%;
  height: 820px;
  .detail-wrap-content {
    width: 100%;
    height: calc(100% - 50px);
  }
}
.detail-footer {
  display: flex;
  margin-top: 10px;
  justify-content: center;
  align-items: center;
}
@{_deep}.ivu-btn.ivu-btn-loading {
  .ivu-icon-ios-loading {
    position: absolute;
    left: 4px;
    top: 5px;
  }
  .ivu-icon + span {
    margin-left: 0 !important;
  }
}
</style>
