<template>
  <div class="base-text-color">
    <Form :label-width="100" ref="formValidateRef" class="formInfo" :rules="ruleValidate" :model="formValidate">
      <FormItem class="mr-40" label="治理方式：" prop="letter">
        MAC地址中的字母转换为：
        <RadioGroup class="inline" v-model="formValidate.letter">
          <Radio label="1" class="mr-lg">大写</Radio>
          <Radio label="2">小写</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
  </div>
</template>
<script>
export default {
  props: {
    editInfos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formValidate: {
        letter: '1',
      },
      ruleValidate: {
        letter: [
          {
            required: true,
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {
    if (this.editInfos.propertyJson) {
      const propertyJson = JSON.parse(this.editInfos.propertyJson);
      this.formValidate.letter = propertyJson.letter || '1';
    }
    this.emitForm();
  },
  updated() {
    this.emitForm();
  },
  methods: {
    emitForm() {
      let data = {};
      data.propertyJson = JSON.stringify(this.formValidate);
      this.$emit('getInfos', data);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
