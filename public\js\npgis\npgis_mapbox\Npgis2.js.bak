MapPlatForm={Base:{},ModdleMarker:0,BottomMarker:1,CustomMarker:2,VERSIONNUMBER:"v3.0.1",ServiceType:"npgis",AK:"",dataServiceURL:"/npgisdataservice/"},MapPlatForm.Base.MapConfig=function(t,e){this.CLASS_NAME="MapConfig",this.dataServiceURL=t||MapPlatForm.dataServiceURL,this.otherServiceURL=e,this.mapServiceURL="",this.mapInfo=null,this._mapJson=null},MapPlatForm.Base.MapConfig.prototype._addLayerToMap=function(t,e,i){var o=[];if(e&&0<e.length){for(var r,a,s,n=0,l=e.length;n<l;n++){s=(r=e[n]).layerType.split(".");var h=r.layerOpt;"ArcgisTileLayer"==s[s.length-1]&&(h=r.layerOpt.layerInfo),a=new NPMap.Layers[s[s.length-1]](r.layerOpt.url,r.layerName,h),o.push(a)}1<o.length?t.addLayers(o,!0):(i&&o[0].setStyle(i),t.addLayers(o))}return o},MapPlatForm.Base.MapConfig.prototype.createMap=function(t,e,i){this.mapInfo=e,this.styleJson=i;var o=new NPMap.Map(t,e.mapOpts),r=this._addLayerToMap(o,e.vectorLayer,this.styleJson);return this._mapJson={map:o,vectorLayer:r},this._mapJson},MapPlatForm.Base.MapConfig.prototype.showVectorLayer=function(){if(this._mapJson&&!this._mapJson.vectorLayer){if(this._mapJson.sattilateLayer){for(var t=0;t<this._mapJson.sattilateLayer.length;t++)this._mapJson.map.removeLayer(this._mapJson.sattilateLayer[t]);this._mapJson.sattilateLayer=null}var e=this._addLayerToMap(this._mapJson.map,this.mapInfo.vectorLayer);this._mapJson.vectorLayer=e}},MapPlatForm.Base.MapConfig.prototype.showSattilateLayer=function(){if(this._mapJson&&!this._mapJson.sattilateLayer){if(this._mapJson.vectorLayer){for(var t=0;t<this._mapJson.vectorLayer.length;t++)this._mapJson.map.removeLayer(this._mapJson.vectorLayer[t]);this._mapJson.vectorLayer=null}var e=this._addLayerToMap(this._mapJson.map,this.mapInfo.sattilateLayer);this._mapJson.sattilateLayer=e}},MapPlatForm.Base.MapGeometry=function(t){this.CLASS_NAME="MapPlatForm.Base.MapGeometry",this.map=t},MapPlatForm.Base.MapGeometry.prototype.getGeometryByGeoJson=function(t){return GeoJSON.read(t)},MapPlatForm.Base.MapGeometry.prototype.getGeometryByWKT=function(t){return WKT.read(t)},MapPlatForm.Base.MapGeometry.prototype.getFGeoJsonByGeometry=function(t){return GeoJSON.write(t,this.map)},MapPlatForm.Base.MapGeometry.prototype.getGGeoJsonByGeometry=function(t){var e=GeoJSON.write(t,this.map),i=JSON.parse(e);return t=JSON.stringify(i.geometry)},MapPlatForm.Base.MapGeometry.prototype.getWKTByGeometry=function(t){return WKT.write(t,this.map)},MapPlatForm.Base.MapGeometry.prototype.getExtent2Polygon=function(t){var e=[];return e.push(new NPMap.Geometry.Point(t.sw.lon,t.sw.lat)),e.push(new NPMap.Geometry.Point(t.ne.lon,t.sw.lat)),e.push(new NPMap.Geometry.Point(t.ne.lon,t.ne.lat)),e.push(new NPMap.Geometry.Point(t.sw.lon,t.ne.lat)),e.push(new NPMap.Geometry.Point(t.sw.lon,t.sw.lat)),new NPMap.Geometry.Polygon(e)},MapPlatForm.Base.MapGeometry.prototype.createMarker=function(t,e){markerType=e.markerType?e.markerType:MapPlatForm.ModdleMarker;var i=new NPMap.Symbols.Icon(e.url,new NPMap.Geometry.Size(e.size.width,e.size.height));markerType===MapPlatForm.BottomMarker&&(i.setAnchor(new NPMap.Geometry.Size(0,-e.size.height/2)),e.labelOffset=e.labelOffset?e.labelOffset:{x:0,y:-e.size.height/2}),markerType==MapPlatForm.CustomMarker&&i.setAnchor(new NPMap.Geometry.Size(-e.iconOffset.width,-e.iconOffset.height)),e.showInMap&&(i._showInMap=e.showInMap);var o=new NPMap.Symbols.Marker(t);if(o.setIcon(i),e.text){label=new NPMap.Symbols.Label(e.text),label.setStyle({color:e.color?e.color:"#FFFFFF"}),e.labelOffset=e.labelOffset?e.labelOffset:{x:0,y:0};var r=new NPMap.Geometry.Size(e.labelOffset.x,e.labelOffset.y);label.setOffset(r),o.setLabel(label)}return e.title&&o.setTitle(e.title),o},MapPlatForm.Base.MapGeometry.prototype.createCircleSector=function(t,e,i,o){var r,a,s,n=NPMap.T.helper.webMoctorJW2PM(t.lon,t.lat),l=[],h=[];o=o*Math.PI/180,i=i*Math.PI/180,l.push(new NPMap.Geometry.Point(n.lon,n.lat));for(var p=0;p<61;p++)r=o+i/60*p,a=n.lon+e*Math.cos(r),s=n.lat+e*Math.sin(r),l.push(new NPMap.Geometry.Point(a,s));for(l.push(new NPMap.Geometry.Point(n.lon,n.lat)),p=0;p<l.length;p++){var c=NPMap.T.helper.inverseMercator(l[p].lon,l[p].lat);c=new NPMap.Geometry.Point(c.lon,c.lat),h.push(c)}return new NPMap.Geometry.Polygon(h)},MapPlatForm.Base.MapGeometry.prototype.getIconByParam=function(t){markerType=t.markerType?t.markerType:MapPlatForm.ModdleMarker;var e=new NPMap.Symbols.Icon(t.url,new NPMap.Geometry.Size(t.size.width,t.size.height));return markerType===MapPlatForm.BottomMarker&&e.setAnchor(new NPMap.Geometry.Size(0,-t.size.height/2)),markerType===MapPlatForm.CustomMarker&&e.setAnchor(new NPMap.Geometry.Size(-t.iconOffset.width,-t.iconOffset.height)),e},MapPlatForm.Base.MapGeometry.prototype.getExtentByOverlays=function(t){for(var e,i,o,r,a=t.length-1;0<=a;a--){var s=t[a].getExtent();e&&i&&o&&r?(e>s.left&&(e=s.left),i>s.bottom&&(i=s.bottom),o<s.right&&(o=s.right),r<s.top&&(r=s.top)):(e=s.left,i=s.bottom,o=s.right,r=s.top)}return new NPMap.Geometry.Extent(e,i,o,r)},MapPlatForm.Base.MapGeometry.prototype.sortingResourceByLine=function(t,e,i){i=i||50;for(var o=[],r=this._getLinePoints(e,i),a=this._getShortPointsInLine(t,e),s=0;s<r.length;s++){var n=null;n=s<r.length-1?r[s+1]:new NPMap.Geometry.Point(r[s].lon+(r[s].lon-r[s-1].lon)/2,r[s].lat+(r[s].lat-r[s-1].lat)/2);for(var l=this._findShortestMarker(a,r[s],n,i),h=l.length-1;0<=h;h--){for(var p=!1,c=0;c<o.length;c++)o[c]&&o[c].id===l[h].id&&(p=!0);!p&&l[h]&&o.push(l[h])}}for(s=0;s<t.length;s++){for(p=!1,c=0;c<o.length;c++)o[c].id===t[s].id&&(p=!0);p||o.push(t[s])}return o},MapPlatForm.Base.MapGeometry.prototype._getLinePoints=function(t,e){for(var i=t.getPath(),o=[],r=0;r<i.length-1;r++){s_points=this._splitPoints(i[r],i[r+1],e);for(var a=0;a<s_points.length;a++)o.push(s_points[a])}return o},MapPlatForm.Base.MapGeometry.prototype._splitPoints=function(t,e,i){var o=[t],r=this.map.getDistance(t,e,"4326");if(r<=i)return[t,e];var a,s,n=0;0<i&&(n=Math.ceil(r/i));for(var l=1;l<n;l++){a=l/n;var h=parseFloat((e.lon-t.lon)*a)+parseFloat(t.lon),p=parseFloat((e.lat-t.lat)*a)+parseFloat(t.lat);s=new NPMap.Geometry.Point(h,p),o.push(s)}return o},MapPlatForm.Base.MapGeometry.prototype._getShortPointsInLine=function(t,e){for(var i=[],o=0;o<t.length;o++){var r=null;r=t[o].longitude&&t[o].latitude?new NPMap.Geometry.Point(t[o].longitude,t[o].latitude):t[o].getPosition();var a=e.getPath(),s=999999,n=null;sline=null;for(var l=0;l<a.length-1;l++){var h=this._getSibgleShortPointInLine(r.lon,r.lat,a[l].lon,a[l].lat,a[l+1].lon,a[l+1].lat),p=this._calculateEuclideanDistance(h.lon,h.lat,r.lon,r.lat);p<s&&(s=p,n=h,sline=new NPMap.Geometry.Polyline([a[l],a[l+1]]))}i.push({key:o,shortPoint:n,data:t[o]})}return i},MapPlatForm.Base.MapGeometry.prototype._findShortestMarker=function(t,e,i,o){for(var r=[],a=0;a<t.length;a++){var s=t[a].shortPoint,n=this.map.getDistance(s,e,"4326"),l=this.map.getDistance(s,i,"4326"),h=this.map.getDistance(e,i,"4326");n<o/2&&(n*n+h*h<l*l&&(n=0-n),r.push({distance:n,marker:t[a].data}))}var p=[];for(a=(r=r.sort(function(t,e){return t.distance-e.distance})).length-1;0<=a;a--)p.push(r[a].marker);return p},MapPlatForm.Base.MapGeometry.prototype._getSibgleShortPointInLine=function(t,e,i,o,r,a){var s,n,l;if((s=this._calculateEuclideanDistance(i,o,r,a))===(n=this._calculateEuclideanDistance(i,o,t,e))+(l=this._calculateEuclideanDistance(r,a,t,e)))return new NPMap.Geometry.Point(t,e);if(s<=1e-6)return new NPMap.Geometry.Point(i,o);if(s*s+n*n<=l*l)return new NPMap.Geometry.Point(i,o);if(s*s+l*l<=n*n)return new NPMap.Geometry.Point(r,a);if(i==r)return new NPMap.Geometry.Point(i,e);var h=(a-o)/(r-i),p=(t+(e-o)*h+h*h*i)/(h*h+1),c=h*p-h*i+o;return new NPMap.Geometry.Point(p,c)},MapPlatForm.Base.MapGeometry.prototype._calculateEuclideanDistance=function(t,e,i,o){return Math.sqrt((t-i)*(t-i)+(e-o)*(e-o))},function(){MapPlatForm.Base.MapService=function(t,e,i){this.CLASS_NAME="MapService",this._currentService=null,this.map=t,this.mapConfig=new MapPlatForm.Base.MapConfig(e,i),this.mapGeometry=new MapPlatForm.Base.MapGeometry(this.map),this.routeService=null},MapPlatForm.Base.MapService.prototype.queryRoadByName=function(roadName,callBack){var url=this.mapConfig.dataServiceURL+"query/getRoadsByName",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params={roadName:roadName},this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadByNameService=service.query(url,params,function(result){for(var lines=[],i=result.length-1;0<=i;i--){var geometry=eval("("+result[i].feature+")"),obj={},line=GeoJSON.read(geometry);if(line instanceof Array)for(var j=line.length-1;0<=j;j--)line[j].data=result[i];else line.data=result[i];obj.name=result[i].name,obj.geometry=line,lines.push(obj)}callBack instanceof Function&&callBack(lines)})},MapPlatForm.Base.MapService.prototype.getGeometryBuffer=function(t,e,i){var o=this.mapConfig.dataServiceURL+"gis/buffer",r=new NPMap.Services.bufferParams;r.projection=this.map.getProjection(),r.distance=e,r.units="m",r.geometry=t;var a=new NPMap.Services.BufferService(this.map,NPMap.MAPTYPE_NPGIS);this.geometryBufferService&&(this.geometryBufferService.abort(),this.geometryBufferService=null),this.geometryBufferService=a.buffer(o,r,i)},MapPlatForm.Base.MapService.prototype.queryPOIByName=function(name,callBack,maxResult,rowIndex){if(this.queryPOIByNameService&&(this.queryPOIByNameService.abort(),this.queryPOIByNameService=null),"gaode"!=MapPlatForm.ServiceType)if("minemap"!=MapPlatForm.ServiceType){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.keyWord=name,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByNameService=service.query(url,params,function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result)})}else{var tempUrl=this.mapConfig.otherServiceURL+"search/keyword";this.queryPOIByNameService=queryPOIByMinMapServer(tempUrl,{key:name,searchType:"poi",source:3,pageCount:maxResult,pageNumber:rowIndex,token:MapPlatForm.AK},function(t,e){callBack(t,e)})}else{var tempUrl=this.mapConfig.otherServiceURL+"search/poi";this.queryPOIByNameService=queryPOIByGaodeServer(tempUrl,{query:name,region:"全国",page_size:maxResult,page_num:rowIndex,ak:MapPlatForm.AK},function(t,e){callBack(t,e)})}},MapPlatForm.Base.MapService.prototype.queryPOIByCoord=function(point,callBack){if(this.queryPOIByCoordService&&(this.queryPOIByCoordService.abort(),this.queryPOIByCoordService=null),"gaode"!=MapPlatForm.ServiceType)if("minemap"!=MapPlatForm.ServiceType){var url=this.mapConfig.dataServiceURL+"query/poicoord",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params={coord:point.lon+","+point.lat},this.queryPOIByCoordService=service.query(url,params,function(result){var point;if(result&&result.geometry){var geometry=eval("("+result.geometry+")");"Point"!==geometry.type&&"point"!==geometry.type||(point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]),point.data=result)}callBack instanceof Function&&callBack(point)})}else{var tempUrl=this.mapConfig.otherServiceURL+"coder/reverseGeocoding";this.queryPOIByCoordService=queryGeoByMinMapServer(tempUrl,{location:point.lon+","+point.lat,type:1,radius:1e3,roadRadius:1e3,kind:0,source:3,token:MapPlatForm.AK},function(t){callBack(t)})}else{var tempUrl=this.mapConfig.otherServiceURL+"rgeo";this.queryPOIByCoordService=queryGeoByGaodeServer(tempUrl,{location:point.lon+","+point.lat,pois:1,ak:MapPlatForm.AK},function(t){callBack(t)})}},MapPlatForm.Base.MapService.prototype.addPOI=function(e,i){var t=this.mapConfig.dataServiceURL+"query/addPoi",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={name:e.data.name,poiType:e.data.type,address:e.data.address,x:e.lon,y:e.lat},this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.addPOIService=o.updata(t,r,function(t){t?(e.data=t,i instanceof Function&&i(e)):i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.updataPOI=function(e,i){var t=this.mapConfig.dataServiceURL+"query/updataPoi",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={gid:e.data.gid,name:e.data.name,poiType:e.data.type,address:e.data.address,x:e.lon,y:e.lat},this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this.updataPOIService=o.updata(t,r,function(t){t?(e.data=t,i instanceof Function&&i(e)):i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.queryPOIByGeometry=function(geometry,callBack,maxResult,rowIndex){if("gaode"==MapPlatForm.ServiceType){for(var tempUrl=this.mapConfig.otherServiceURL+"search/poi",geoStr="",points=geometry.getPath(),i=0;i<points.length;i++)geoStr+=points[i].lon+","+points[i].lat+";";return geoStr=geoStr.substring(0,geoStr.length-1),void queryPOIByGaodeServer(tempUrl,{query:name,regionType:"polygon",bounds:geoStr,page_size:maxResult,page_num:rowIndex,ak:MapPlatForm.AK},function(t,e){callBack(t,e)})}var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.queryPOIByGeometryService=service.query(url,params,function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryPOIByFilter=function(filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/poiname",service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.keyWord=fs[1],this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.queryPOIByFilterService=service.query(url,params,function(result){for(var points=[],i=0;i<result.features.length;i++){var geometry=eval("("+result.features[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.features[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryPOIByGeometryAndFilter=function(geometry,filter,callBack,maxResult,rowIndex){var url=this.mapConfig.dataServiceURL+"query/searchInBounds",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,maxResult&&(params.maxResult=maxResult),rowIndex&&(params.rowIndex=rowIndex);var fs=filter.split("=");params.type=fs[0],params.key=fs[1],params.requestType="post",this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryPOIByGeometryAndFilterService=service.query(url,params,function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points,result.pageCount,result.totalCount)})},MapPlatForm.Base.MapService.prototype.queryRoadInterByGeometry=function(geometry,callBack){var url=this.mapConfig.dataServiceURL+"query/roadInterByGeo",wktGeo=this.mapGeometry.getWKTByGeometry(geometry),service=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),params=new NPMap.Services.queryParams;params.wkt=wktGeo,params.requestType="post",this.queryRoadInterByGeometryService&&(this.queryRoadInterByGeometryService.abort(),this.queryRoadInterByGeometryService=null),this.queryRoadInterByGeometryService=service.query(url,params,function(result){for(var points=[],i=0;i<result.data.length;i++){var geometry=eval("("+result.data[i].geometry+")");if("Point"===geometry.type||"point"===geometry.type){var point=new NPMap.Geometry.Point(geometry.coordinates[0],geometry.coordinates[1]);point.data=result.data[i],points.push(point)}}callBack instanceof Function&&callBack(points)})},MapPlatForm.Base.MapService.prototype.queryRoadCrossByName=function(t,r){var e=this.mapConfig.dataServiceURL+"query/getRoadCrossByName",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o.roadName=t,this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this.queryRoadCrossByNameService=i.query(e,o,function(t){for(var e=[],i=0;i<t.length;i++){var o=new NPMap.Geometry.Point(t[i].lon,t[i].lat);o.data=t[i],e.push(o)}r instanceof Function&&r(e)})},MapPlatForm.Base.MapService.prototype.queryRoadCrossByGeometry=function(t,r){var e=this.mapGeometry.getWKTByGeometry(t),i=this.mapConfig.dataServiceURL+"query/searchRoadCrossInBounds",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a=new NPMap.Services.queryParams;a.wkt=e,this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryRoadCrossByGeometryService=o.query(i,a,function(t){for(var e=[],i=0;i<t.data.length;i++){var o=new NPMap.Geometry.Point(t.data[i].lon,t.data[i].lat);o.data=t.data,e.push(o)}r instanceof Function&&r(e)})},MapPlatForm.Base.MapService.prototype.queryAllFeaturesByName=function(t,s){if(this.queryFeaturesByNameService&&(this.queryFeaturesByNameService.abort(),this.queryFeaturesByNameService=null),"gaode"!=MapPlatForm.ServiceType){var e=this.mapConfig.dataServiceURL+"query/getFOIByName",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o.keyWordString=t,this.queryFeaturesByNameService=i.query(e,o,function(t){for(var e=new MapPlatForm.Base.MapGeometry(this.map),i=[],o=0;o<t.length;o++){var r=null,a={};r="road"===t[o].type?(a.address=t[o].address,e.getGeometryByGeoJson(t[o].feature)):("poi"===t[o].type&&(a.address=t[o].address),e.getGeometryByGeoJson(t[o].wkt)),a.name=t[o].name,a.type=t[o].type,a.geometry=r,i.push(a)}s instanceof Function&&s(i)})}else{var r=this.mapConfig.otherServiceURL+"search/poi";this.queryFeaturesByNameService=queryPOIByGaodeServer(r,{query:t,region:"全国",page_size:10,page_num:1,ak:MapPlatForm.AK},function(t,e){for(var i=[],o=0;o<t.length;o++){var r={};r.name=t[o].data.name,r.address=t[o].data.address,r.type="poi",r.geometry=t[o],i.push(r)}s(i)})}},MapPlatForm.Base.MapService.prototype.addRoadCross=function(t){var e=this.mapConfig.dataServiceURL+"query/addRoadCross",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={name:t.data.name,x:t.lon,y:t.lat},this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.addRoadCrossService=i.updata(e,o,function(t){t?((void 0).data=t,callBack instanceof Function&&callBack(void 0)):callBack instanceof Function&&callBack(null)})},MapPlatForm.Base.MapService.prototype.updataRoadCross=function(t){var e=this.mapConfig.dataServiceURL+"query/updataRoadCross",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={gid:t.data.gid,name:t.data.name,x:t.lon,y:t.lat},this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this._updataRoadCrossService=i.updata(e,o,function(t){t?((void 0).data=t,callBack instanceof Function&&callBack(void 0)):callBack instanceof Function&&callBack(null)})},MapPlatForm.Base.MapService.prototype.searchRouteByCoor=function(t,e){var i=null,o=new NPMap.Services.routeParams,r=this.mapConfig.dataServiceURL+"/gis/na";"gaode"==MapPlatForm.ServiceType?(r=this.mapConfig.otherServiceURL,i=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_ARCGISTILE),o.extendURL="route/",o.ak=MapPlatForm.AK):(i=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_GEOSERVER),o.service="na",o.request="getroute",o.networkName="shanghai_roadnet_supermap",o.geoBarriers=[],o.algorithm="Dijkstra"),o.startStop=t.startStop,o.endStop=t.endStop,o.trafficModel=t.trafficModel,o.planRoadType=t.planRoadType,this.routeService&&(this.routeService.abort(),this.routeService=null),this.routeService=i.route(r,o,e)},MapPlatForm.Base.MapService.prototype.searchRouteByMultiPoints=function(t,p,e){for(var i=new NPMap.Services.RouteService(this.map,NPMap.MAPTYPE_GEOSERVER),o=new NPMap.Services.routeParams,r="",a=0;a<t.length;a++)r+=t[a].lon+","+t[a].lat+";";r=r.substr(0,r.length-1),o.stops=r,this.routeMultiService&&(this.routeMultiService.abort(),this.routeMultiService=null),this.routeMultiService=i.routeByMultPoints(this.mapConfig.dataServiceURL+"/gis/routing",o,function(t){var e=[];if(t&&t instanceof Array)for(var i=0;i<t.length;i++){for(var o=[],r=t[i].expend.split(";"),a=0;a<r.length;a++){var s=r[a].split(","),n=parseFloat(s[0]),l=parseFloat(s[1]);o.push(new NPMap.Geometry.Point(n,l))}var h=new NPMap.Geometry.Polyline(o);h.setData({length:t[i].length,start:new NPMap.Geometry.Point(parseFloat(t[i].start.split(",")[0]),parseFloat(t[i].start.split(",")[1])),end:new NPMap.Geometry.Point(parseFloat(t[i].end.split(",")[0]),parseFloat(t[i].end.split(",")[1]))}),e.push(h)}p(e)},e)},MapPlatForm.Base.MapService.prototype.searchDistrictsByID=function(t,i){var e=this.mapConfig.dataServiceURL+"query/getRegionalBound",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={addvcd:t},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var a=this;this._searchDistrictsService=o.query(e,r,function(t){if(t){if(t.geometry&&(t.geometry=a.mapGeometry.getGeometryByGeoJson(t.geometry)),t.districts)for(var e=0;e<t.districts.length;e++)t.districts[e].geometry&&(t.districts[e].geometry=a.mapGeometry.getGeometryByGeoJson(t.districts[e].geometry));i instanceof Function&&i(t)}else i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.searchDistrictsByName=function(t,i){var e=this.mapConfig.dataServiceURL+"query/getRegionalBoundByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={name:t},this._searchDistrictsService&&(this._searchDistrictsService.abort(),this._searchDistrictsService=null);var a=this;this._searchDistrictsService=o.query(e,r,function(t){if(t){if(t.geometry&&(t.geometry=a.mapGeometry.getGeometryByGeoJson(t.geometry)),t.districts)for(var e=0;e<t.districts.length;e++)t.districts[e].geometry&&(t.districts[e].geometry=a.mapGeometry.getGeometryByGeoJson(t.districts[e].geometry));i instanceof Function&&i(t)}else i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.searchPanoramaByPoint=function(t,e,i){var o=this.mapConfig.dataServiceURL+"panorama/getConfigsByPosition",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),a=new NPMap.Services.queryParams;a={position:this.mapGeometry.getWKTByGeometry(t),distance:e},this._searchPanoramaByPoint&&(this._searchPanoramaByPoint.abort(),this._searchPanoramaByPoint=null);this._searchPanoramaByPoint=r.query(o,a,function(t){t?i instanceof Function&&i(t):i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.searchSNPanoramaPoints=function(e){var t=this.mapConfig.dataServiceURL+"panorama/getAllSnPoints",i=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),o=new NPMap.Services.queryParams;o={},this._searchSNPanoramaPoints&&(this._searchSNPanoramaPoints.abort(),this._searchSNPanoramaPoints=null);this._searchSNPanoramaPoints=i.query(t,o,function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)})},MapPlatForm.Base.MapService.prototype.searchSNPanoramaByPointID=function(t,e){var i=this.mapConfig.dataServiceURL+"panorama/getSnConfigsByParentId",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r=new NPMap.Services.queryParams;r={parentid:t},this._searchSNPanoramaByPointID&&(this._searchSNPanoramaByPointID.abort(),this._searchSNPanoramaByPointID=null);this._searchSNPanoramaByPointID=o.query(i,r,function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)})},MapPlatForm.Base.MapService.prototype.queryRoomFloor=function(t,e,i){var o=this.mapConfig.dataServiceURL+"indoormap/getFloorByPidAndName",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS);params={pid:t,floorName:e},this._queryRoomFloor&&(this._queryRoomFloor.abort(),this._queryRoomFloor=null),this._queryRoomFloor=r.query(o,params,function(t){t?i instanceof Function&&i(t):i instanceof Function&&i(null)})},MapPlatForm.Base.MapService.prototype.queryRoomFloorsByGeometry=function(t,e){var i="";if(t instanceof NPMap.Geometry.Polygon){i=WKT.write(t);var o=this.mapConfig.dataServiceURL+"indoormap/listFloorByBounds",r=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS);params={bounds:i},this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryRoomFloorsByExtent=r.query(o,params,function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)})}},MapPlatForm.Base.MapService.prototype.queryBuildingRoomList=function(t,e){var i=this.mapConfig.dataServiceURL+"indoormap/listBuilding",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS);t={pageSize:(t=t||{}).pageSize,recordIndex:t.pageIndex},this._queryBuildingRoomList&&(this._queryBuildingRoomList.abort(),this._queryBuildingRoomList=null),this._queryBuildingRoomList=o.query(i,t,function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)})},MapPlatForm.Base.MapService.prototype.queryBuildingByName=function(t,e){var i=this.mapConfig.dataServiceURL+"indoormap/getBuildingByName",o=new NPMap.Services.QueryService(NPMap.MAPTYPE_NPGIS),r={name:t};this._queryBuildingByName&&(this._queryBuildingByName.abort(),this._queryBuildingByName=null),this._queryBuildingByName=o.query(i,r,function(t){t?e instanceof Function&&e(t):e instanceof Function&&e(null)})},MapPlatForm.Base.MapService.prototype.cancelService=function(){this.queryPOIByFilterService&&(this.queryPOIByFilterService.abort(),this.queryPOIByFilterService=null),this.addRoadCrossService&&(this.addRoadCrossService.abort(),this.addRoadCrossService=null),this.queryPOIByGeometryService&&(this.queryPOIByGeometryService.abort(),this.queryPOIByGeometryService=null),this.updataPOIService&&(this.updataPOIService.abort(),this.updataPOIService=null),this._updataRoadCrossService&&(this._updataRoadCrossService.abort(),this._updataRoadCrossService=null),this.addPOIService&&(this.addPOIService.abort(),this.addPOIService=null),this.queryRoadByNameService&&(this.queryRoadByNameService.abort(),this.queryRoadByNameService=null),this.queryRoadCrossByGeometryService&&(this.queryRoadCrossByGeometryService.abort(),this.queryRoadCrossByGeometryService=null),this.queryPOIByGeometryAndFilterService&&(this.queryPOIByGeometryAndFilterService.abort(),this.queryPOIByGeometryAndFilterService=null),this.queryRoadCrossByNameService&&(this.queryRoadCrossByNameService.abort(),this.queryRoadCrossByNameService=null),this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryRoomFloor&&(this._queryRoomFloor.abort(),this._queryRoomFloor=null),this._queryRoomFloorsByExtent&&(this._queryRoomFloorsByExtent.abort(),this._queryRoomFloorsByExtent=null),this._queryBuildingByName&&(this._queryBuildingByName.abort(),this._queryBuildingByName=null),this.routeService&&(this.routeService.abort(),this.routeService=null),this.queryRoadInterByGeometryService&&(this.queryRoadInterByGeometryService.abort(),this.queryRoadInterByGeometryService=null)};var queryPOIByGaodeServer=function(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),points=[],features=[];if(result.results)for(var i=0;i<result.results.length;i++){var obj=result.results[i],point=new NPMap.Geometry.Point(parseFloat(obj.location.lng),parseFloat(obj.location.lat)),feature={gid:obj.uid,address:obj.address,name:obj.name,telephone:obj.telephone,geometry:'{"type":"Point","coordinates":['+obj.location.lng+","+obj.location.lat+"]}"};point.data=feature,points.push(point)}successCallback(points,{totalCount:result.total,features:features})}},function(t){"function"==typeof errorCallback&&errorCallback(t)})},queryGeoByGaodeServer=function(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),point;if(result.results){var obj=result.results[0],point=new NPMap.Geometry.Point(parseFloat(obj.location.lng),parseFloat(obj.location.lat)),name=obj.formatted_address;obj.pois&&0<obj.pois.length&&(name=obj.pois[0].name);var feature={address:obj.formatted_address,name:name,geometry:'{"type":"Point","coordinates":['+obj.location.lng+","+obj.location.lat+"]}"};point.data=feature}successCallback(point)}},function(t){"function"==typeof errorCallback&&errorCallback(t)})},queryPOIByMinMapServer=function(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),points=[],features=[];if(result=result.data,result.rows)for(var i=0;i<result.rows.length;i++){var obj=result.rows[i],point=new NPMap.Geometry.Point(parseFloat(obj.geom.coordinates[0]),parseFloat(obj.geom.coordinates[1])),feature={gid:obj.id,address:obj.address,name:obj.name,telephone:obj.tel,geometry:'{"type":"Point","coordinates":['+obj.geom.coordinates[0]+","+obj.geom.coordinates[1]+"]}"};point.data=feature,points.push(point)}successCallback(points,{totalCount:result.total,features:features})}},function(t){"function"==typeof errorCallback&&errorCallback(t)})},queryGeoByMinMapServer=function(url,params,successCallback,errorCallback){return NPMap.Utils.Request.Get(url,params,function(reply){if("function"==typeof successCallback){var text=reply.replace("'",""),result=eval("("+text+")"),point,xy=params.location.split(",");if(result.data){var obj=result.data,point=new NPMap.Geometry.Point(parseFloat(xy[0]),parseFloat(xy[1])),name=obj.restName,feature={address:obj.city+obj.dist+obj.town+obj.village+obj.restName,name:name,geometry:'{"type":"Point","coordinates":['+params.location+"]}"};point.data=feature}successCallback(point)}},function(t){"function"==typeof errorCallback&&errorCallback(t)})}}(),function(){var n;MapPlatForm.Base.MapTag=function(t,e){this.CLASS_NAME="MapTag",this.layer=e,this.map=t,this._activeMarker=null,this.callback=null,this._mapGeometry=new MapPlatForm.Base.MapGeometry(t),this.markerParam=null,this.layer||(this.layer=this.map.getDefaultLayer()),(n=this).isclick=!1},MapPlatForm.Base.MapTag.prototype._clickCallBack=function(){if(!n.isclick&&n._activeMarker){n.isclick=!0;var t=n._mapGeometry.createMarker(n._activeMarker.getPosition(),n.markerParam);n.layer.addOverlay(t),n.delAdrawMarker(),n.callback&&n.callback instanceof Function&&n.callback(t)}else n.delAdrawMarker()},MapPlatForm.Base.MapTag.prototype._moveCallBack=function(t){var e=t.object.getLonLatFromPixel(t.xy),i=new NPMap.Geometry.Point(e.lon,e.lat);i=NPMap.T.getPoint(n.map,i),n._activeMarker?n.isclick||n._activeMarker.setPosition(i):(n._activeMarker=n._mapGeometry.createMarker(i,n.markerParam),n.layer.addOverlay(n._activeMarker),n._activeMarker.addEventListener("click",n._clickCallBack),n._activeMarker.addEventListener("rightclick",n._rigthClickCallBack))},MapPlatForm.Base.MapTag.prototype._rigthClickCallBack=function(){n._activeMarker&&(n.delAdrawMarker(),n.cancelCallback&&n.cancelCallback())},MapPlatForm.Base.MapTag.prototype._mouseMoveCallBack=function(t){n._activeMarker?n._activeMarker.setPosition(t):(n._activeMarker=n._mapGeometry.createMarker(t,n.markerParam),n.layer.addOverlay(n._activeMarker),n._activeMarker.addEventListener("click",n._clickCallBack),n._activeMarker.addEventListener("rightclick",n._rigthClickCallBack))},MapPlatForm.Base.MapTag.prototype.adrawMarker=function(t,e,i,o,r){this.markerParam=t,this.isclick=!1,this.delAdrawMarker(),this.callback=e,this.cancelCallback=r,this.layer.removeOverlay(this._activeMarker),this._activeMarker=null,this._contextHeight="20px","EN"===NPMap.CULTURE?(this.map.activateMouseContext("Click on add annotations, right click to cancel"),this._contextHeight="34px"):this.map.activateMouseContext("点击添加标注,右键取消");var a=this.map.getMouseContextStyle();if(a.height=this._contextHeight,i&&this.map.activateMouseContext(i),o)for(var s in o)a[s]=o[s];this.map.addEventListener(NPMap.MAP_EVENT_MOUSE_MOVE,n._mouseMoveCallBack),this.map.addEventListener(NPMap.MAP_EVENT_RIGHT_CLICK,n._rigthClickCallBack),this.map.addEventListener(NPMap.MAP_EVENT_CLICK,n._clickCallBack),this.map.getContainer().onmouseenter=function(){n._activeMarker&&n._activeMarker.show()},this.map.getContainer().onmouseleave=function(){n._activeMarker&&n._activeMarker.hide()}},MapPlatForm.Base.MapTag.prototype.delAdrawMarker=function(){this.map&&(this._activeMarker&&(this._activeMarker.removeEventListener("click",n._clickCallBack),this._activeMarker.removeEventListener("rightclick",n._rigthClickCallBack)),this.layer.removeOverlay(this._activeMarker),this.map.deactivateMouseContext(),this.map.removeEventListener("click",n._clickCallBack),this.map.removeEventListener("rightclick",n._rigthClickCallBack),this.map.removeEventListener("mousemove",n._mouseMoveCallBack))}}(),function(){var y;MapPlatForm.Base.MapTools=function(t,e){this.CLASS_NAME="MapTools",this.map=t,this.measureTool=null,this.drawTool=null,this.searchCircle=null,this.editMarker=null,this.layer=e,y=this},MapPlatForm.Base.MapTools.prototype._initMeasureTool=function(){this.measureTool=new NPMap.Tools.MeasureTool(this.map)},MapPlatForm.Base.MapTools.prototype._getStyle=function(t){return t?t.cursor="crosshair":t={cursor:"crosshair"},t},MapPlatForm.Base.MapTools.prototype._initDrawTool=function(){this.drawTool=new NPMap.Tools.DrawingTool(this.map),this.map.MapTools=this},MapPlatForm.Base.MapTools.prototype.measureDistance=function(t,e){this.measureTool||this._initMeasureTool(),this.cancelDraw(),this.measureTool.setMode(NPMap.MEASURE_MODE_DISTANCE,t,e)},MapPlatForm.Base.MapTools.prototype.measureArea=function(t,e){this.measureTool||this._initMeasureTool(),this.cancelDraw(),this.measureTool.setMode(NPMap.MEASURE_MODE_AREA,t,e)},MapPlatForm.Base.MapTools.prototype.cancelMeasure=function(){this.measureTool&&this.measureTool.cancleMeasure()},MapPlatForm.Base.MapTools.prototype.cancelDraw=function(){this.drawTool&&this.drawTool.cancleDraw()},MapPlatForm.Base.MapTools.prototype.drawLine=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_POLYLINE,t,e)},MapPlatForm.Base.MapTools.prototype.drawRectangle=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_RECT,t,e)},MapPlatForm.Base.MapTools.prototype.drawCircle=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_CIRCLE,t,e)},MapPlatForm.Base.MapTools.prototype.drawPolygon=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_POLYLGON,t,e)},MapPlatForm.Base.MapTools.prototype.drawCircleByDiameter=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_CIRCLE_DIAMETER,t,e)},MapPlatForm.Base.MapTools.prototype.drawFreehand=function(t,e){this.drawTool||this._initDrawTool(),this.cancelMeasure(),this.cancelDraw(),e=this._getStyle(e),this.drawTool.setMode(NPMap.DRAW_MODE_FREEHAND,t,e)},MapPlatForm.Base.MapTools.prototype.addCircleSearchControl=function(t,e,i,o,r){var a=1e3,s="米";"EN"===NPMap.CULTURE&&(s="M"),i?a=i:i=500,o||(o=5e3),r&&i<=r&&r<=o&&(a=r);var n=e;this.searchCircle=new NPMap.Geometry.Circle(t,a,{color:"#acb9d1",fillColor:"#6980bc",weight:2,opacity:1,fillOpacity:.2});var l=this.map.getDefaultLayer();(this.layer?this.layer:this.map.getDefaultLayer()).addOverlay(this.searchCircle);var h=new NPMap.Geometry.Size(76,24);this.map.addImages([["editCircle",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/editCircle.png"]]);var p=new NPMap.Symbols.Icon("editCircle",h);this.editMarker=new NPMap.Symbols.Marker(t),l.addOverlay(this.editMarker),this.editMarker.setIcon(p),this.editMarker.setOffset(new NPMap.Geometry.Size(25,-12));var c=new NPMap.Symbols.Label(a+s,{offset:new NPMap.Geometry.Size(45/14,-12/14)});c.setStyle({fontSize:12,fontFamily:"宋体",align:"left"}),this.editMarker.setLabel(c);var u=this.searchCircle.getCenter(),m=NPMap.T.helper.webMoctorJW2PM(u.lon,u.lat);m.lon=m.lon+a,u=NPMap.T.helper.inverseMercator(m.lon,m.lat),this.editMarker.setPosition(u),this.editMarker.isEnableEdit=!0,this._resetControlMarker(),this.editMarker.addEventListener("draging",this._updateCircle(i,o,s)),this.editMarker.addEventListener("dragend",this._callback(n)),this.map.addEventListener("moving",this._resetControlMarker),e&&e instanceof Function&&e(y.searchCircle)},MapPlatForm.Base.MapTools.prototype._updateCircle=function(s,n,l){return function(){var t=y.searchCircle.getCenter(),e=NPMap.T.helper.webMoctorJW2PM(t.lon,t.lat),i=y.editMarker.getPosition(),o=NPMap.T.helper.webMoctorJW2PM(i.lon,i.lat),r=Math.sqrt(Math.pow(o.lon-e.lon,2)+Math.pow(o.lat-e.lat,2));y.map._obj.transform.pitch=0,r<s?r=s:n<r&&(r=n);var a=y.editMarker.getLabel();a.setContent(Math.round(r)+l),y.editMarker.setLabel(a),y.searchCircle.setRadius(r),y._resetControlMarker()}},MapPlatForm.Base.MapTools.prototype._callback=function(t){return function(){y._resetControlMarker(),t&&t instanceof Function&&t(y.searchCircle)}},MapPlatForm.Base.MapTools.prototype._resetControlMarker=function(){for(var t,e,i=y.searchCircle.getPath(),o=[],r=0;r<i.length;r++){var a=y.map.pointToPixel(i[r]);o.push(a)}t=o[0].x,e=o[0];for(r=1;r<o.length;r++)t<o[r].x&&(e=o[r],t=o[r].x);var s=y.map.pixelToPoint({x:e.x,y:e.y});y.editMarker.setPosition(s)},MapPlatForm.Base.MapTools.prototype.setCircleSearchControlRadius=function(t,e){if(this.searchCircle&&this.editMarker){var i="米";"EN"===NPMap.CULTURE&&(i="M");var o=this.searchCircle.getCenter(),r=NPMap.T.helper.webMoctorJW2PM(o.lon,o.lat);r.lon=r.lon+t,o=NPMap.T.helper.inverseMercator(r.lon,r.lat),this.editMarker.setPosition(o),this.editMarker.setPosition(o),this.searchCircle.setRadius(t);var a=this.editMarker.getLabel();a.setContent(Math.round(t)+i),this.editMarker.setLabel(a),this.editMarker.refresh();var s=this.searchCircle.getExtent();this.map.zoomToExtent(s),e&&e instanceof Function&&e(self.searchCircle)}},MapPlatForm.Base.MapTools.prototype.removeCircleSearchControl=function(){this.editMarker&&(this.editMarker.removeEventListener("dragend",y._callback),this.editMarker.removeEventListener("draging",y._updateCircle),this.map.removeEventListener("moving",y._resetControlMarker));var t=this.map.getDefaultLayer(),e=this.layer?this.layer:this.map.getDefaultLayer();t.removeOverlay(this.editMarker),e.removeOverlay(this.searchCircle),this.map.disableEditing()}}(),function(){var u;MapPlatForm.Base.MapRoutePlan=function(t,e,i,o){this.CLASS_NAME="MapRoutePlan",this.map=t,this.layer=e||this.map.getDefaultLayer(),this.startMarker=null,this._planRoadType=1,this._trafficModel="car",this._currentPolyline=null,this.endMarker=null,this.editMarker=null,this._throughMarkerInfo={},this._routes=[],this._throughMarkerNum=0,this._routeIndex=0,this.dataServiceURL=i,this.otherServiceURL=o,this._mapService=new MapPlatForm.Base.MapService(t,i,o),this._mapGeometry=new MapPlatForm.Base.MapGeometry(t),this.result1=null,this.result2=null,this._researchCallback=function(){},this.routeGroup=this.layer.addGroup("route"),this.editGroup=this.layer.addGroup("route_edit"),this.defaultName="未知地址",u=this,"EN"===NPMap.CULTURE&&(this.defaultName="Unknown Position"),window.getElementsByClassName=function(t){for(var e=[],i=document.getElementsByTagName("*"),o=0;o<i.length;o++)i[o].className===t&&(e[e.length]=i[o]);return e}},MapPlatForm.Base.MapRoutePlan.prototype._addPath=function(t){if(t.features.length<1||t.features.length<1)this._errorCallBack&&this._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?this._errorCallBack("There is no suitable route, please re-drag！"):this._errorCallBack("没有合适的路线，请重新拖动！"));else{var e=t.features[0];e.setStyle({color:"red",weight:5}),this.routeGroup.addOverlay(e);var o=this;e.addEventListener("mousemove",function(t,e){var i=new MapPlatForm.Base.MapGeometry(map);o.editMarker?(o.editMarker.setPosition(e),o.editMarker.show()):(o.editMarker=i.createMarker(e,{url:o._crossMarkerStyle.editImageUrl,size:{width:o._crossMarkerStyle.width,height:o._crossMarkerStyle.height},markerType:2}),o.editGroup.addOverlay(o.editMarker),o.editMarker.isEnableEdit=!0,o.editMarker.enableEditing())})}},MapPlatForm.Base.MapRoutePlan.prototype.addEidtMarkerEvent=function(){this.editMarker&&this.editMarker.addEventListener(NPMap.MARKER_EVENT_DRAG_END,function(t){_afterDrag(t)})},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(i,o,r){var t=null,a=null,s=null;s=o?(a=o.startPosition,o.stopPosition):(t=_currentPolyline.getPath(),a=t[0],t[t.length-1]);var n={startStop:a,endStop:i,trafficModel:this._trafficModel,planRoadType:this._planRoadType},l=this;_mapService.searchRouteByCoor(n,function(e){n={startStop:i,endStop:s,trafficModel:this._trafficModel,planRoadType:this._planRoadType},_mapService.searchRouteByCoor(n,function(t){e.features.length<1||t.features.length<1?l._errorCallBack&&l._errorCallBack instanceof Function&&("EN"==NPMap.CULTURE?l._errorCallBack("There is no suitable route, please re-drag！"):l._errorCallBack("没有合适的路线，请重新拖动！")):_addRoutes(i,{result1:e,result2:t,startPosition:a,stopPosition:s,throughMarkerRelativeInfo:o,key:r})})})},MapPlatForm.Base.MapRoutePlan.prototype._addRoutes=function(t){this.routeGroup.addOverlay(t.features[0])},MapPlatForm.Base.MapRoutePlan.prototype._clearEditInfoOnMap=function(){this.editGroup&&(this.editGroup.removeAllOverlays(),this.editMarker=null),this.routeGroup&&(this.routeGroup.removeAllOverlays(),this.editMarker=null),this.map.closeAllInfoWindows()},MapPlatForm.Base.MapRoutePlan.prototype._queryRoute=function(){var t={startStop:this._startMarker.getPosition(),endStop:this._endMarker.getPosition(),trafficModel:this._trafficModel,planRoadType:this._planRoadType},o=this;this._mapService.searchRouteByCoor(t,function(t){if(t.features.length<1)o._errorCallBack&&o._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?o._errorCallBack("There is no suitable route, please re-drag！"):o._errorCallBack("没有合适的路线，请重新拖动！"));else{var e=t.features[0];o._setPolylineStyle([e]),o.routeGroup.addOverlay(e),e.setData({index:o._routeIndex}),o._routeArray=[e],o._addEventToLines([e]),o._routes=[{index:o._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:e,routeInfo:t.messages}];var i={routes:o._routes,polyline:e};o._researchCallback(i)}})},MapPlatForm.Base.MapRoutePlan.prototype._addEventToLines=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].addEventListener("mousemove",this._addMarker),t[i].addEventListener("mouseout",this._removeMarker)},MapPlatForm.Base.MapRoutePlan.prototype._removeMarker=function(){u.editGroup.removeOverlay(u.editMarker),u.editMarker=null},MapPlatForm.Base.MapRoutePlan.prototype._addMarker=function(t){if(!this.flag){var e=t.srcObject,i=new NPMap.Geometry.Point(t.lon,t.lat);u.editMarker?(u.editMarker.setPosition(i),u.editMarker.show()):(u.editMarker=u._mapGeometry.createMarker(i,{url:u._crossMarkerStyle.editImageUrl,size:{width:u._crossMarkerStyle.width,height:u._crossMarkerStyle.height},markerType:0}),u.editGroup.addOverlay(u.editMarker),u._dragEdit()),u.editMarker.setData({line:e})}},MapPlatForm.Base.MapRoutePlan.prototype._removeEventToLines=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].removeEventListener("mousemove",this._addMarker)},MapPlatForm.Base.MapRoutePlan.prototype._dragEdit=function(){if(this.editMarker){this.editMarker.isEnableEdit=!0;var t=this;this.map.ModifyFeatureControl&&!this.map.ModifyFeatureControl._active&&(this.map.ModifyFeatureControl.disableEdit(),this.map.ModifyFeatureControl=null),this.map.ModifyFeatureControl||(this.map.ModifyFeatureControl=new NPMap.Controls.ModifyFeatureControl("",this.editMarker)),this.editMarker.addEventListener("dragstart",function(){this.flag=!0}),this.editMarker.addEventListener("dragend",function(){this.flag=!1,t._currentPolyline=t.editMarker.getData().line,t._afterDrag(t.editMarker.getPosition()),t._removeMarker()})}},MapPlatForm.Base.MapRoutePlan.prototype._getRelativeRoute=function(t){for(var e=null,i=null,o=0,r=this._routes.length;o<r;o++)if(this._routes[o].dragIconIndex.icon1===parseInt(t,10)||this._routes[o].dragIconIndex.icon2===parseInt(t,10))if(e){if(!i){i=this._routes[o];break}}else e=this._routes[o];return{route1:e,route2:i}},MapPlatForm.Base.MapRoutePlan.prototype._afterDrag=function(e,i){var t=null,o=null,r=null,a="add";if(i){var s=this._getRelativeRoute(i),n=s.route1.route.getPath(),l=s.route2.route.getPath();o=n[0],r=l[l.length-1],a="edit"}else o=(t=this._currentPolyline.getPath())[0],r=t[t.length-1],a="add";var h,p,c,u={startStop:o,endStop:e,trafficModel:this._trafficModel,planRoadType:this._planRoadType},m=this;this._mapService.searchRouteByCoor(u,function(t){h=t,m._searchRouteByDrag(e,a,s,i,h,p,c)});var y={startStop:e,endStop:r,trafficModel:this._trafficModel,planRoadType:this._planRoadType};new MapPlatForm.Base.MapService(this.map,this.dataServiceURL,this.otherServiceURL).searchRouteByCoor(y,function(t){p=t,m._searchRouteByDrag(e,a,s,i,h,p,c)}),new MapPlatForm.Base.MapService(this.map,this.dataServiceURL,this.otherServiceURL).queryPOIByCoord(e,function(t){c=t,m._searchRouteByDrag(e,a,s,i,h,p,c)})},MapPlatForm.Base.MapRoutePlan.prototype._searchRouteByDrag=function(t,e,i,o,r,a,s){if(r&&a&&s){for(var n=this.routeGroup.getAllOverlayers(),l=[],h=0;h<n.length;h++)"NPMap.Geometry.Polyline"===n[h].CLASS_NAME&&l.push(n[h]);this._addEventToLines(l),r.features.length<1||a.features.length<1?this._errorCallBack&&this._errorCallBack instanceof Function&&("EN"===NPMap.CULTURE?this._errorCallBack("There is no suitable route, please re-drag！"):this._errorCallBack("没有合适的路线，请重新拖动！")):this._getAddressByCoor(t,{result1:r,result2:a,type:e,relativeRoutes:i,key:o},s)}},MapPlatForm.Base.MapRoutePlan.prototype._getAddressByCoor=function(t,e,i){"add"===e.type?this._afteDragAdd(t,e,i):this._afterDragEdit(t,e,i)},MapPlatForm.Base.MapRoutePlan.prototype._afteDragAdd=function(t,e,i){var o=e.result1.features[0],r=e.result2.features[0];this._setPolylineStyle([o,r]),o.setData({index:++this._routeIndex}),r.setData({index:++this._routeIndex}),this.routeGroup.addOverlay(o),this.routeGroup.addOverlay(r),this._addEventToLines([o,r]);var a=this._crossMarkerStyle.crossImageUrl,s=this._mapGeometry.createMarker(t,{url:a,size:{width:this._crossMarkerStyle.width,height:this._crossMarkerStyle.height},markerType:0});this.editGroup.addOverlay(s);var n=i.data.name,l=n||this.defaultName;this._throughMarkerNum++,s.setData({key:this._throughMarkerNum,name:l});var h=this._getRouteByIndex(this._currentPolyline.getData().index),p=this._addInfowin(l,this._throughMarkerNum,t);p.getBaseDiv().title=l,this._throughMarkerInfo[this._throughMarkerNum]={infoWindow:p,marker:s},s.isEnableEdit=!0;var c=this;s.addEventListener("mouseover",function(){u.map.disableInertialDragging()}),s.addEventListener("mouseout",function(){u.map.enableInertialDragging()}),s.addEventListener("dragend",function(){var t=parseInt(s.getData().key,10);c._throughMarkerInfo[t].infoWindow.close(),c._afterDrag(s.getPosition(),t)}),e.result1.messages.startPointName=h.routeInfo.startPointName,e.result2.messages.startPointName=l,this._refreshRouteArray({route1:{index:o.getData().index,dragIconIndex:{icon1:h.dragIconIndex.icon1,icon2:this._throughMarkerNum},route:o,routeInfo:e.result1.messages},route2:{index:r.getData().index,dragIconIndex:{icon1:this._throughMarkerNum,icon2:h.dragIconIndex.icon2},route:r,routeInfo:e.result2.messages},key:e.key},e.type),this.routeGroup.removeOverlay(this._currentPolyline)},MapPlatForm.Base.MapRoutePlan.prototype._afterDragEdit=function(t,e,i){var o=e.relativeRoutes;this.routeGroup.removeOverlay(o.route1.route),this.routeGroup.removeOverlay(o.route2.route);var r=e.result1.features[0],a=e.result2.features[0];this._setPolylineStyle([r,a]),r.setData({index:o.route1.route.getData().index}),a.setData({index:o.route2.route.getData().index}),this.routeGroup.addOverlay(r),this.routeGroup.addOverlay(a),this._addEventToLines([r,a]);var s=i.data.name,n=s||this.defaultName;this._throughMarkerInfo[e.key].marker.setData({key:e.key,name:n}),o.route1.route=r,o.route1.routeInfo=e.result1.messages,o.route2.route=a,o.route2.routeInfo=e.result2.messages,o.route2.routeInfo.startPointName=n;var l=this._addInfowin(n,e.key,t);this._throughMarkerInfo[e.key].infoWindow=l,this._refreshRouteArray({route1:o.route1,route2:o.route2,key:e.key},e.type)},MapPlatForm.Base.MapRoutePlan.prototype._getThroughInfo=function(t,e){var i=document.createElement("div");i.style.fontSize="13px",i.style.border="1px solid #dfdfdf",i.style.borderRadius="5px",i.style.webkitBorderRadius="5px",i.style.height="21px",i.style.backgroundColor="#fff";var o=document.createElement("span");o.style.maxWidth="100px",o.style.overflow="hidden",o.style.whiteSpace="nowrap",o.style.textOverflow="ellipsis",o.style.float="left",o.style.marginLeft="2px",o.style.display="inline-block",o.style.lineHeight="21px",o.innerText=t;var r=document.createElement("i");return r.style.width="14px",r.style.height="14px",r.style.float="left",r.style.display="inline-block",r.style.marginLeft="5px",r.style.marginTop="3px",r.style.cursor="pointer",r.style.background="url("+NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/close1.png) no-repeat",r.className="infowindow-close",r.setAttribute("key",e),i.appendChild(o),i.appendChild(r),i},MapPlatForm.Base.MapRoutePlan.prototype._addInfowin=function(t,e,i){var o=this._getThroughInfo(t,e),r=new NPMap.Symbols.InfoWindow(i,"",o,{iscommon:!0,closeButton:!1,offset:new NPMap.Geometry.Size(7,-9)});this.map.addOverlay(r),r.open(),r.getBaseDiv().style.padding="2px 5px 2px";for(var a=document.getElementsByClassName("infowindow-close"),s=this,n=a.length-1;0<=n;n--)a[n].onclick=function(){key=this.getAttribute("key");var r=s._getRelativeRoute(key),t=r.route1.route.getPath(),e=r.route2.route.getPath(),i={startStop:t[0],endStop:e[e.length-1],trafficModel:s._trafficModel,planRoadType:s._planRoadType};s._mapService.searchRouteByCoor(i,function(t){if(t.features[0]){var e=s._throughMarkerInfo[key];e.infoWindow.close(),s.editGroup.removeOverlay(e.marker),s.routeGroup.removeOverlay(r.route1.route),s.routeGroup.removeOverlay(r.route2.route);var i=t.features[0],o=++s._routeIndex;i.setData({index:o}),s._setPolylineStyle([i]),s.routeGroup.addOverlay(i),t.messages.startPointName=r.route1.routeInfo.startPointName,s._refreshRouteArray({route:{index:o,dragIconIndex:{icon1:r.route1.dragIconIndex.icon1,icon2:r.route2.dragIconIndex.icon2},route:t.features[0],routeInfo:t.messages},key:key},"delete"),s._addEventToLines([t.features[0]])}else s._errorCallBack()})};return r},MapPlatForm.Base.MapRoutePlan.prototype._refreshRouteArray=function(t,e){var i,o=null,r=null;if("add"===e){for(var a=this._currentPolyline.getData().index,s=0,n=this._routes.length;s<n;s++)if(this._routes[s].index===parseInt(a,10)){i=s;break}this._routes.splice(i,1,t.route1),this._routes.splice(i+1,0,t.route2)}else if("edit"===e){for(s=0,n=this._routes.length;s<n;s++)if(this._routes[s].dragIconIndex.icon1===parseInt(t.key,10)||this._routes[s].dragIconIndex.icon2===parseInt(t.key,10))if(o){if(!r){r=this._routes[s],this._routes.splice(s,1,t.route2);break}}else o=this._routes[s],this._routes.splice(s,1,t.route1)}else for(s=0,n=this._routes.length;s<n;s++)if(this._routes[s].dragIconIndex.icon1===parseInt(t.key,10)||this._routes[s].dragIconIndex.icon2===parseInt(t.key,10))if(o){if(!r){r=this._routes[s],this._routes.splice(s,1);break}}else o=this._routes[s],this._routes.splice(s,1,t.route);var l,h=[];if(this._routes&&0<this._routes.length){for(s=0,n=this._routes.length;s<n;s++)h=h.concat(this._routes[s].route.getPath());l=new NPMap.Geometry.Polyline(h)}var p={routes:this._routes,polyline:l};this._researchCallback(p)},MapPlatForm.Base.MapRoutePlan.prototype._setPolylineStyle=function(t){if(t&&t instanceof Array)for(var e=t.length,i=0;i<e;i++)t[i].setStyle(this._lineStyle)},MapPlatForm.Base.MapRoutePlan.prototype._getRouteByIndex=function(t){for(var e=null,i=0,o=this._routes.length;i<o;i++)if(this._routes[i].index===parseInt(t,10)){e=this._routes[i];break}return e},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfo=function(t,e){var i=e.startPosition,o=e.stopPosition;for(var r in this._throughMarkerInfo)if(t!==parseInt(r)){var a=this._throughMarkerInfo[r].startPosition,s=this._throughMarkerInfo[r].stopPosition,n=this._throughMarkerInfo[r].crossPoint;i.lat===a.lat&&i.lon===a.lon&&(this._throughMarkerInfo[r].startPosition=e.crossPoint,this._throughMarkerInfo[r].route1=e.route2),o.lat===s.lat&&o.lon===s.lon&&(this._throughMarkerInfo[r].stopPosition=e.crossPoint,this._throughMarkerInfo[r].route2=e.route1),i.lat===n.lat&&i.lon===n.lon&&(this._throughMarkerInfo[r].stopPosition=e.crossPoint,this._throughMarkerInfo[r].route2=e.route1),o.lat===n.lat&&o.lon===n.lon&&(this._throughMarkerInfo[r].startPosition=e.crossPoint,this._throughMarkerInfo[r].route1=e.route2)}},MapPlatForm.Base.MapRoutePlan.prototype._refreshRelativeThroughInfoDel=function(t,e,i){var o=e.crossPoint;for(var r in _throughMarkerInfo)if(t!==parseInt(r)){var a=this._throughMarkerInfo[r].startPosition,s=this._throughMarkerInfo[r].stopPosition;o.lat===a.lat&&o.lon===a.lon&&(this._throughMarkerInfo[r].startPosition=e.startPosition,this._throughMarkerInfo[r].route1=i),o.lat===s.lat&&o.lon===s.lon&&(this._throughMarkerInfo[r].stopPosition=e.stopPosition,this._throughMarkerInfo[r].route2=i)}},MapPlatForm.Base.MapRoutePlan.prototype.addRoutePlanControl=function(t,e,i){if(this._planRoadType=t.planRoadType?t.planRoadType:this._planRoadType,this._trafficModel=t.trafficModel?t.trafficModel:this._trafficModel,this._startMarker=t.startMarker,this._endMarker=t.endMarker,this._lineStyle=t.lineStyle?t.lineStyle:{color:"green",weight:10,opacity:.7},this.map.addImages([["path-cross",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/path-cross.png"],["path-edit",NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/path-edit.png"]]),this._crossMarkerStyle=t.crossMarkerStyle?t.crossMarkerStyle:{crossImageUrl:"path-cross",editImageUrl:"path-edit",height:22,width:22},this._clearEditInfoOnMap(),this._researchCallback=e,this._errorCallBack=i,t.polyline){var o=t.polyline;this._setPolylineStyle([o]),this.routeGroup.addOverlay(o),o.setData({index:this._routeIndex}),this._routeArray=[o],this._addEventToLines([o]),this._routes=[{index:this._routeIndex,dragIconIndex:{icon1:0,icon2:0},route:o,routeInfo:""}]}else this._queryRoute()},MapPlatForm.Base.MapRoutePlan.prototype.stopEdit=function(){for(var t=this.routeGroup.getAllOverlayers(),e=[],i=0;i<t.length;i++)"NPMap.Geometry.Polyline"===t[i].CLASS_NAME&&e.push(t[i]);for(var o in this._removeEventToLines(e),this.editGroup.removeOverlay(this.editMarker),this.editMarker=null,this._throughMarkerInfo)this._throughMarkerInfo[o].marker&&this._throughMarkerInfo[o].marker.disableEditing();var r=document.getElementsByClassName("infowindow-close");for(i=r.length-1;0<=i;i--)r[i].onclick=null},MapPlatForm.Base.MapRoutePlan.prototype.startEdit=function(){for(var t=this.routeGroup.getAllOverlayers(),e=[],i=0;i<t.length;i++)"NPMap.Geometry.Polyline"===t[i].CLASS_NAME&&e.push(t[i]);for(var o in this._addEventToLines(e),this._throughMarkerInfo)this._throughMarkerInfo[o].marker&&this._throughMarkerInfo[o].marker.enableEditing();var r=document.getElementsByClassName("infowindow-close"),a=this;for(i=r.length-1;0<=i;i--)r[i].onclick=function(){key=this.getAttribute("key");var r=a._getRelativeRoute(key),t=r.route1.route.getPath(),e=r.route2.route.getPath(),i={startStop:t[0],endStop:e[e.length-1],trafficModel:a._trafficModel,planRoadType:a._planRoadType};a._mapService.searchRouteByCoor(i,function(t){if(t.features[0]){var e=a._throughMarkerInfo[key];e.infoWindow.close(),a.editGroup.removeOverlay(e.marker),a.routeGroup.removeOverlay(r.route1.route),a.routeGroup.removeOverlay(r.route2.route);var i=t.features[0],o=++a._routeIndex;i.setData({index:o}),a._setPolylineStyle([i]),a.routeGroup.addOverlay(i),t.messages.startPointName=r.route1.routeInfo.startPointName,a._refreshRouteArray({route:{index:o,dragIconIndex:{icon1:r.route1.dragIconIndex.icon1,icon2:r.route2.dragIconIndex.icon2},route:t.features[0],routeInfo:t.messages},key:key},"delete"),a._addEventToLines([t.features[0]])}else a._errorCallBack()})}}}(),function(){var t=MapPlatForm.Base.AnimationLineManager=function(t){this._map=t,this.animationLineArr=[]};t.prototype.addAnimationLines=function(t){this.animationLineArr=t;for(var e,i=0,o=0;o<this.animationLineArr.length;o++){1<this.animationLineArr.length&&(this.animationLineArr[o].isSetCenter=!1);var r=this.animationLineArr[o].getAllSegCount();i<r&&(e=this.animationLineArr[o],i=r),this.animationLineArr[o].unRedraw=!0}e.unRedraw=!1},t.prototype.start=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].start()},t.prototype.stop=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].stop()},t.prototype.restart=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].restart()},t.prototype.continus=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].continus()},t.prototype.pause=function(){for(var t=0;t<this.animationLineArr.length;t++)this.animationLineArr[t].pause()}}(),function(o){"use strict";var t=function(t,e,i){if(this._line_layer_name="my-line-layer",this._line_layer_source="my-line-source",this._point_animate_layer_name="my-point-animate-layer",this._line_animate_layer_source="my-line-animate-source",this._line_animate_layer_name="my-line-animate-layer",this._flowIdentityBackFiled="flowIdentityBack",this._flowIdentityValueLength=1,this._flowIdentityPlayHandler=0,this._map=t._obj,this._opts=this._mergeOpts(i),this.__animateFps=1e3/this._opts.flowFps,this._animageLineSegLength=this._opts.flowLineSegLength,this._data=this._processedData(t,e),this._animatePointHandler=0,this._animatePointFlag=!0,this._animateLineHandler=0,this._animateLineFlag=!0,this._lineItemlengthArr=[],this._lineItemlengthArrBack=[],this._publisher=new MapPlatForm.Base.publisher,this._map.loaded())this._addLayers();else{var o=this;this._map.on("load",function(){o._addLayers()})}};t.prototype.subscribePlayTopic=function(t){this._publisher.subscribe("spriteLine-play","identity",t)},t.prototype.cancelPlayTopic=function(){this._publisher.cancel("spriteLine-play","identity")},t.prototype.destroy=function(){if("visible"===this._opts.lineVisibility&&(this._map.removeLayer(this._line_layer_name),this._map.removeSource(this._line_layer_source)),this._opts.flow){this.removeFlowIdentityPlay();var i=this;if(this._data.map(function(t){var e=t.properties;return void 0!==e[i._flowIdentityBackFiled]&&(e[i._opts.flowIdentity]=e[i._flowIdentityBackFiled],e[i._flowIdentityBackFiled]=void 0,t.properties=e),t}),"point"===this._opts.flowType)this._map.removeLayer(this._point_animate_layer_name),this._map.removeSource(this._point_animate_layer_name),this._animatePointFlag=!1,o.cancelAnimationFrame(this._animatePointHandler);else{for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.removeLayer(this._line_animate_layer_name+"_"+t);this._map.removeSource(this._line_animate_layer_source),this._animateLineFlag=!1,o.cancelAnimationFrame(this._animateLineHandler)}}},t.prototype.show=function(){if("visible"===this._opts.lineVisibility&&this._map.setLayoutProperty(this._line_layer_name,"visibility","visible"),this._opts.flow)if("point"===this._opts.flowType)this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","visible");else for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","visible")},t.prototype.hide=function(){if("visible"===this._opts.lineVisibility&&this._map.setLayoutProperty(this._line_layer_name,"visibility","none"),this._opts.flow)if("point"===this._opts.flowType)this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","none");else for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","none")},t.prototype.disableFlow=function(){if(this._opts.flow)if("point"===this._opts.flowType)this._animatePointFlag&&(this._animatePointFlag=!1,this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","none"));else if(this._animateLineFlag){this._animateLineFlag=!1;for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","none")}},t.prototype.enableFlow=function(){if(this._opts.flow)if("point"===this._opts.flowType)this._animatePointFlag||(this._animatePointFlag=!0,this._map.setLayoutProperty(this._point_animate_layer_name,"visibility","visible"),this._animatePoint());else if(!this._animateLineFlag){this._animateLineFlag=!0;for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.setLayoutProperty(this._line_animate_layer_name+"_"+t,"visibility","visible");this._animateLine()}},t.prototype.addFlow=function(){if(!this._opts.flow)if(this._opts.flow=!0,this._opts.flowIdentityPlay=!1,"point"===this._opts.flowType){this._animatePointFlag=!0;for(var t=JSON.parse('{"type":"FeatureCollection","features":[]}'),e=[],i=0,o=this._data.length;i<o;i++){var r={type:"Feature"},a={type:"Point",coordinates:(l=(p=this._data[i].geometry).coordinates)[0]};r.geometry=a,e.push(r)}t.features=e,this._addAnimatePointLayer(t)}else{this._animateLineFlag=!0;var s=JSON.parse('{"type":"FeatureCollection","features":[]}'),n=[];for(i=0,o=this._data.length;i<o;i++){var l,h={type:"Feature"},p=this._data[i].geometry,c=this._data[i].properties,u={type:"LineString",coordinates:[(l=p.coordinates)[l.length-1]]};h.geometry=u,h.properties=c,n.push(h)}s.features=n,this._addAnimateLineLayer(s)}},t.prototype.removeFlow=function(){if(this._opts.flow){if(this.removeFlowIdentityPlay(),"point"===this._opts.flowType)this._map.removeLayer(this._point_animate_layer_name),this._map.removeSource(this._point_animate_layer_name),this._animatePointFlag=!1,o.cancelAnimationFrame(this._animatePointHandler);else{for(var t=0,e=this._opts.animateLineLayerFilters.length;t<e;t++)this._map.removeLayer(this._line_animate_layer_name+"_"+t);this._map.removeSource(this._line_animate_layer_source),this._animateLineFlag=!1,o.cancelAnimationFrame(this._animateLineHandler)}this._opts.flow=!1}},t.prototype.addFlowIdentityPlay=function(){if(!this._opts.flow)throw"flow 属性必须为 true !";if(!this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength){var t=this._map.getSource(this._line_animate_layer_source)._data;this._cycleUpdateAnimateLineSourceFlowIdentityValue(t),this._opts.flowIdentityPlay=!0}},t.prototype.removeFlowIdentityPlay=function(){if(this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength){o.clearInterval(this._flowIdentityPlayHandler);var t=this._map.getSource(this._line_animate_layer_source)._data;this._updateAnimateLineSourceFlowIdentityValue(t,0),this._opts.flowIdentityPlay=!1}},t.prototype._mergeOpts=function(t){var e={lineColor:"#ff0000",lineWidth:1,lineOpacity:1,lineDasharray:[-1,-1],lineBlur:0,lineVisibility:"visible",flow:!0,flowType:"point",flowFps:60,flowIdentity:void 0,flowIdentityPlay:!1,flowIdentityPlayInterval:3e4,flowLineSegLength:15,flowLineSegStepCount:5,flowLineSegStep:1,flowLineColor:"#ffffff",flowLineWidth:2,flowLineOpacity:1,flowLineCap:"round",flowLinejoin:"round",flowLineDasharray:[-1,-1],flowLineBlur:0,flowPointColor:"#ffffff",flowPointSize:2,flowPointBlur:0,flowPointOpacity:1,flowPointStrokeWidth:0,flowPointStrokeColor:"#ffffff",flowPointStrokeOpacity:1};if(t)for(var i in t)t.hasOwnProperty(i)&&void 0!==t[i]&&(e[i]=t[i]);if(void 0!==e.flowLineGradient&&void 0===e.flowIdentity)throw"flowIdentity 属性不正确!";var o={flow:e.flow,flowType:e.flowType,flowFps:e.flowFps,flowIdentity:e.flowIdentity,flowIdentityPlay:e.flowIdentityPlay,flowIdentityPlayInterval:e.flowIdentityPlayInterval,flowLineSegLength:e.flowLineSegLength,flowLineSegStepCount:e.flowLineSegStepCount,flowLineSegStep:e.flowLineSegStep};o.lineLayerPaint={"line-color":e.lineColor,"line-width":e.lineWidth,"line-dasharray":e.lineDasharray,"line-opacity":e.lineOpacity,"line-blur":e.lineBlur},o.lineVisibility=e.lineVisibility;var r=this._animateLineLayerPaintAndFilterBuilder(e);return o.animateLineLayerPaints=r.animateLineLayerPaints,o.animateLineLayerFilters=r.animateLineLayerFilters,o.animateLineLayerLayout={"line-cap":e.flowLineCap,"line-join":e.flowLinejoin},o.animatePointLayerPaint={"circle-radius":e.flowPointSize,"circle-color":e.flowPointColor,"circle-blur":e.flowPointBlur,"circle-opacity":e.flowPointOpacity,"circle-stroke-width":e.flowPointStrokeWidth,"circle-stroke-color":e.flowPointStrokeColor,"circle-stroke-opacity":e.flowPointStrokeOpacity},o},t.prototype._animateLineLayerPaintAndFilterBuilder=function(t){var e=[],i=[],o=t.flowLineGradient;if(void 0!==o&&0<o.length)for(var r=o.length,a=0;a<r;a++){var s=o[a];if(2!==s.length)throw"flowLineGradient 格式错误!";var n,l=s[0],h=s[1];(n={})["line-width"]=t.flowLineWidth,n["line-opacity"]=t.flowLineOpacity,n["line-blur"]=t.flowLineBlur,void 0!==h?n["line-gradient"]=this._paintLineGradientBuilder(h):n["line-dasharray"]=t.flowLineDasharray,i[a]=n;var p,c=o[a+1];p=void 0===c?Number.MAX_VALUE:c[0],e[a]=["all",[">=",t.flowIdentity,l],["<",t.flowIdentity,p]]}else(n={})["line-color"]=t.flowLineColor,n["line-width"]=t.flowLineWidth,n["line-opacity"]=t.flowLineOpacity,n["line-blur"]=t.flowLineBlur,i[0]=n,e[0]=["==","$type","LineString"];return{animateLineLayerPaints:i,animateLineLayerFilters:e}},t.prototype._paintLineGradientBuilder=function(t){var e=["interpolate",["linear"],["line-progress"]],i=t.length;if(0<i)for(var o=0,r=i;o<r;o++){var a=1/r;e.push(a*o),e.push(t[o])}else e.push(0),e.push("blue"),e.push(1),e.push("red");return e},t.prototype._processedData=function(t,e){var i=this._convertData(t,e);return this._calculateLinesSeg(i)},t.prototype._convertData=function(i,t){for(var e=0,o=t.length;e<o;e++)t[e].geometry.coordinates=t[e].geometry.coordinates.map(function(t){var e=NPMap.T.setPoint(i,{lon:t[0],lat:t[1]});return[e.lon,e.lat]});return t},t.prototype._calculateLinesSeg=function(t){for(var e=0,i=t.length;e<i;e++){for(var o=t[e].geometry.coordinates,r=[],a=0,s=o.length;a<s;a++){var n=this._calculateLineSeg(o,a);r=r.concat(n)}t[e].geometry.coordinates=r}return t},t.prototype._calculateLineSeg=function(t,e){var i=[];if(e+1>=t.length)return i.push(0),i;var o,r=t[e],a=t[e+1],s=this._getDistance(r,a),n=0;0<this._animageLineSegLength&&(n=Math.round(s/this._animageLineSegLength));for(var l=1;l<n;l++){o=l/n;var h=parseFloat((a[0]-r[0])*o)+parseFloat(r[0]),p=parseFloat((a[1]-r[1])*o)+parseFloat(r[1]);i.push([h,p])}return i.push(t[e+1]),i},t.prototype._getDistance=function(t,e){var i=NPMap.T.helper.webMoctorJW2PM(t[0],t[1]),o=NPMap.T.helper.webMoctorJW2PM(e[0],e[1]);return Math.sqrt((i.lon-o.lon)*(i.lon-o.lon)+(i.lat-o.lat)*(i.lat-o.lat))},t.prototype._cycleAnimatePoint=function(){var t=this;o.setTimeout(function(){t._animatePointHandler=o.requestAnimationFrame(function(){t._animatePoint()})},this.__animateFps)},t.prototype._animatePoint=function(){if(this._animatePointFlag){for(var t=this._map.getSource(this._point_animate_layer_name)._data,e=0,i=this._data.length;e<i;e++){var o=this._data[e].geometry.coordinates,r=this._lineItemlengthArr[e];0===r&&(r=this._lineItemlengthArr[e]=this._lineItemlengthArrBack[e]),t.features[e].geometry.coordinates=o[r],this._lineItemlengthArr[e]=this._lineItemlengthArr[e]-1}this._map.getSource(this._point_animate_layer_name).setData(t),this._cycleAnimatePoint()}},t.prototype._cycleAnimateLine=function(){var t=this;o.setTimeout(function(){t._animateLineHandler=o.requestAnimationFrame(function(){t._animateLine()})},this.__animateFps)},t.prototype._animateLine=function(){if(this._animateLineFlag){for(var t=this._map.getSource(this._line_animate_layer_source)._data,e=0,i=this._data.length;e<i;e++){var o=this._data[e].geometry.coordinates,r=this._lineItemlengthArr[e];r<0&&(r=this._lineItemlengthArr[e]=this._lineItemlengthArrBack[e],t.features[e].geometry.coordinates=[]);var a=t.features[e].geometry.coordinates;a.length===this._opts.flowLineSegStepCount&&a.shift(),a.push(o[r]),t.features[e].geometry.coordinates=a,this._lineItemlengthArr[e]=this._lineItemlengthArr[e]-this._opts.flowLineSegStep}this._map.getSource(this._line_animate_layer_source).setData(t),this._cycleAnimateLine()}},t.prototype._addLayers=function(){for(var t=JSON.parse('{"type":"geojson","data":{"type":"FeatureCollection","features":[]}}'),e=JSON.parse('{"type":"FeatureCollection","features":[]}'),i=JSON.parse('{"type":"FeatureCollection","lineMetrics":true,"features":[]}'),o=[],r=[],a=[],s=0,n=this._data.length;s<n;s++){var l={type:"Feature"},h={type:"Feature"},p={type:"Feature"},c=this._data[s].geometry,u=c.coordinates;"visible"===this._opts.lineVisibility&&(l.geometry=c,o.push(l));var m=u.length-1;this._lineItemlengthArr.push(m),this._lineItemlengthArrBack.push(m);var y={type:"Point",coordinates:u[0]};h.geometry=y,r.push(h);var d={type:"LineString",coordinates:[]};p.geometry=d,p.properties=this._processAnimateLineSourceProperties(this._data[s].properties),a.push(p)}t.data.features=o,e.features=r,i.features=a,"visible"===this._opts.lineVisibility&&this._addLineLayer(t),"line"===this._opts.flowType?(this._addAnimateLineLayer(i),this._opts.flow&&this._opts.flowIdentityPlay&&1!==this._flowIdentityValueLength&&this._cycleUpdateAnimateLineSourceFlowIdentityValue(i)):this._addAnimatePointLayer(e)},t.prototype._processAnimateLineSourceProperties=function(t){var e=t[this._opts.flowIdentity];return e instanceof Array&&(t[this._opts.flowIdentity]=e[0],t[this._flowIdentityBackFiled]=e,this._flowIdentityValueLength=e.length),t},t.prototype._updateAnimateLineSourceFlowIdentityValue=function(t,i){if(void 0!==t){var o=this;t.features.map(function(t){var e=t.properties;return e[o._opts.flowIdentity]=e[o._flowIdentityBackFiled][i],t.properties=e,t}),this._publisher.publish({name:"spriteLine-play",callback:"identity"},i)}},t.prototype._cycleUpdateAnimateLineSourceFlowIdentityValue=function(t){var e=0,i=this;this._flowIdentityPlayHandler=o.setInterval(function(){i._opts.flowIdentityPlay&&(e>i._flowIdentityValueLength-1&&(e=0),i._updateAnimateLineSourceFlowIdentityValue(t,e),e++)},this._opts.flowIdentityPlayInterval)},t.prototype._addLineLayer=function(t){this._map.addSource(this._line_layer_source,t);var e=this._getBuildingLayerId();this._map.addLayer({id:this._line_layer_name,type:"line",source:this._line_layer_source,paint:this._opts.lineLayerPaint,filter:["==","$type","LineString"]},e)},t.prototype._addAnimateLineLayer=function(t){if(this._opts.flow){this._map.addSource(this._line_animate_layer_source,{type:"geojson",lineMetrics:!0,data:t});for(var e=this._getBuildingLayerId(),i=0,o=this._opts.animateLineLayerFilters.length;i<o;i++)this._map.addLayer({id:this._line_animate_layer_name+"_"+i,type:"line",source:this._line_animate_layer_source,filter:this._opts.animateLineLayerFilters[i],layout:this._opts.animateLineLayerLayout,paint:this._opts.animateLineLayerPaints[i]},e);this._animateLine()}},t.prototype._addAnimatePointLayer=function(t){if(this._opts.flow){var e=this._getBuildingLayerId();this._map.addLayer({id:this._point_animate_layer_name,type:"circle",source:{type:"geojson",data:t},paint:this._opts.animatePointLayerPaint},e),this._animatePoint()}},t.prototype._getBuildingLayerId=function(){return void 0===this._map.getLayer("city_normal_building_id")?null:"city_normal_building_id"},o.MapPlatForm.Base.SpriteLine=t}(window),function(){"use strict";var t=function(){this.callbacks={},this.preCallbacks={}};t.prototype={subscribe:function(t,e,i){t&&e&&i&&(this.callbacks[t]||(this.callbacks[t]={}),this.callbacks[t][e]=i,this.preCallbacks[t]&&this.preCallbacks[t][e]&&this.preCallbacks[t][e].length&&(i.apply(this,this.preCallbacks[t][e][0]),delete this.preCallbacks[t][e]))},cancel:function(t,e){t&&this.callbacks[t]&&(e?delete this.callbacks[t][e]:this.callbacks[t]=[])},publish:function(){var t=Array.prototype.shift.call(arguments);if(t&&t.name){var e=arguments;if(this.callbacks[t.name])if(t.callback)this.callbacks[t.name][t.callback]&&this.callbacks[t.name][t.callback].apply(this,e);else for(var i in this.callbacks[t.name])this.callbacks[t.name][i].apply(this,e);else this.preCallbacks[t.name]||(this.preCallbacks[t.name]={}),this.preCallbacks[t.name][t.callback]||(this.preCallbacks[t.name][t.callback]=[]),this.preCallbacks[t.name][t.callback][0]=e}}},window.MapPlatForm.Base.publisher=t}(window),MapPlatForm.Base.MapShapRegion=function(t,e,i){this.geoRegion=e;var o=++NPMap.LayerIndex;this._layerID="RegionLayer"+o,this.CLASS_NAME="MapPlatForm.Base.MapShapRegion",this._map=t;var r=GeoJSON.read(e.geometry);this.fullExtent=r.getExtent(),this._layers=[],this._regions=[],this.acvtiveLayer=null,this.acvtiveRegion=null,this.opts=i||{},this.fillColor=this.opts.fillColor?this.opts.fillColor:"#1a65fb",this.lineColor=this.opts.lineColor?this.opts.lineColor:"#FFFFFF",this.sharderFillColor=this.opts.sharderFillColor?this.opts.sharderFillColor:"#3961b1",this.sharderLineColor=this.opts.sharderLineColor?this.opts.sharderLineColor:"#1040a0",this.opacity=this.opts.opacity?this.opts.opacity:.5,this.hoverColor=this.opts.hoverColor?this.opts.hoverColor:"#3778fb",this.hoverStrokeColor=this.opts.hoverStrokeColor?this.opts.hoverStrokeColor:"#FFFFFF",this.font=this.opts.font?this.opts.font:"14px 宋体",this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.sharderWidth=this.opts.sharderWidth?this.opts.sharderWidth:20,this.minZoom=this.opts.minZoom?this.opts.minZoom:7,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:12,this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:function(){},this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:function(){},this.click=this.opts.click?this.opts.click:function(){},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this.labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":1},filter:["==","$type","Point"]},this.addGeoRegions(e)},MapPlatForm.Base.MapShapRegion.prototype._getFeature=function(t){if(12<=this._map.getZoom())return[];for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf(this._layerID+"_polygon")){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&0<o.length)return[o[0]]}return[]},MapPlatForm.Base.MapShapRegion.prototype._mouseout=function(){this.mouseOut&&this.acvtiveRegion&&(this._map._obj.getCanvas().style.cursor="",this.mouseOut(this.acvtiveRegion),this.acvtiveRegion=null,this.refresh())},MapPlatForm.Base.MapShapRegion.prototype._initInteractive=function(t){this._map._events.through=!0;var a=this;!this._moveFun&&(this._moveFun=function(t){var e=a._map._obj.getBearing(),i=a._map._obj.getPitch()*a.sharderWidth/60,o=0-Math.sin(e/180*Math.PI)*i,r=Math.cos(e/180*Math.PI)*i;a._map._obj.setPaintProperty(a._layerID+"_sharderPolygon","fill-translate",[o,r])}),!this._mouseMoveFun&&(this._mouseMoveFun=function(t){var e=a._getFeature(t);0!=e.length?12<=a._map.getZoom()||a.acvtiveRegion&&e[0].properties.id==a.acvtiveRegion.id||(a._mouseout(),e&&0<e.length&&(a._map._obj.getCanvas().style.cursor="pointer",a.acvtiveRegion=a._regions[e[0].properties.index],a.mouseOver&&a.acvtiveRegion&&(a.refresh(),a.mouseOver(a.acvtiveRegion)))):a._mouseout()}),!this._clickFun&&(this._clickFun=function(){a.acvtiveRegion&&a.click(a.acvtiveRegion)}),t?(this._map._obj.off("move",this._moveFun),this._map._obj.off("mousemove",this._mouseMoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("move",this._moveFun),this._map._obj.on("mousemove",this._mouseMoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapShapRegion.prototype.addGeoRegions=function(t){if(t.districts){for(var e=0;e<t.districts.length;e++)if(t.districts[e]){var i=GeoJSON.read(t.districts[e].geometry);t.districts[e].id=i.id,t.districts[e].center=[i.getCenter().lon,i.getCenter().lat],this._regions.push(t.districts[e]);var o={type:"Feature",geometry:{type:t.districts[e].geometry.type,coordinates:i._getCoordinates(this._map)},properties:{id:i.id,index:e,children:1}},r={type:"Feature",geometry:{type:"Point",coordinates:[i.getCenter().lon,i.getCenter().lat]},properties:{id:i.id,title:t.districts[e].name,"text-offset":[0,1],"text-opacity":1,"text-size":12,"text-color":this.fontColor,visible:!0,index:e}};this._source.data.features.push(o),this._source.data.features.push(r)}var a=GeoJSON.read(t.geometry),s={type:"Feature",geometry:{type:t.geometry.type,coordinates:a._getCoordinates(this._map)},properties:{id:a.id,children:0}};this._source.data.features.push(s),this._map._obj.addSource(this._layerID+"_source",this._source),this._addLayers()}this._initInteractive()},MapPlatForm.Base.MapShapRegion.prototype._addLayers=function(){var t=this._map._obj.getBearing(),e=this._map._obj.getPitch()*this.sharderWidth/60,i=0-Math.sin(t/180*Math.PI)*e,o=Math.cos(t/180*Math.PI)*e,r={id:this._layerID+"_sharderPolygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.sharderFillColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0],"fill-translate-anchor":"map","fill-translate":[i,o]},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(r),this._layers.push(r);var a={id:this._layerID+"_polygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.fillColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.8,this.maxZoom,0],"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1]]};this._map._obj.addLayer(a),this._layers.push(a);var s={id:this._layerID+"_Line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.lineColor,"line-width":1,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",1]]};this._map._obj.addLayer(s),this._layers.push(s);var n={id:this._layerID+"_parentLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.lineColor,"line-width":4,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",0]]};this._map._obj.addLayer(n),this._layers.push(n),this.acvtiveLayer={id:this._layerID+"_polygonActive",type:"fill",source:this._layerID+"_source",paint:{"fill-color":this.hoverColor,"fill-opacity":["interpolate",["linear"],["zoom"],this.minZoom,.5,this.maxZoom,0],"fill-translate-anchor":"map"},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]},this._map._obj.addLayer(this.acvtiveLayer),this._layers.push(this.acvtiveLayer);var l={id:this._layerID+"_activeLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this.hoverStrokeColor,"line-width":4,"line-opacity":["interpolate",["linear"],["zoom"],this.minZoom,1,this.maxZoom,0]},filter:["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]};this._map._obj.addLayer(l),this._layers.push(l),this._map._obj.addLayer(this.labelLayer),this._layers.push(this.labelLayer)},MapPlatForm.Base.MapShapRegion.prototype.destory=function(){if(this._map){this._initInteractive(!0);for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map._obj.removeSource(this._layerID+"_source"),this._map=null}this._regions=[],this._layers=[],this.acvtiveRegion=null},MapPlatForm.Base.MapShapRegion.prototype.refresh=function(){this.acvtiveRegion?(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1],["!=","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",this.acvtiveRegion.id]])):(this._map._obj.setFilter(this._layerID+"_polygon",["all",["==","$type","Polygon"],["==","children",1]]),this._map._obj.setFilter(this._layerID+"_polygonActive",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]),this._map._obj.setFilter(this._layerID+"_activeLine",["all",["==","$type","Polygon"],["==","children",1],["==","id",""]]))},MapPlatForm.Base.MapMultiMarkers=function(t,e){this.CLASS_NAME="MapMultiMarkers",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._markers=[],this._showMarkers=[],this._imgMarkers={},this.acvtivePoint=null,this.activeFeature=null,this._points={},this.mouseOver=e?e.mouseOver:null,this.mouseOut=e?e.mouseOut:null,this.click=e?e.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._markerLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"icon-image":["get","icon"],"text-field":"{title}","text-font":["Microsoft YaHei"],"text-offset":["get","text-offset"],"icon-offset":["get","icon-offset"],"icon-size":["get","icon-size"],"text-size":["get","text-size"],"icon-allow-overlap":!0,"text-allow-overlap":!0,"icon-ignore-placement":!0,"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"],"icon-opacity":["get","icon-opacity"]},filter:["==","$type","Point"]},this._activeMarkerLayer={id:this._layerID+"_activeSymbol",type:"symbol",source:this._layerID+"_source",layout:{"icon-image":["get","icon"],"text-field":"{title}","text-font":["Microsoft YaHei"],"text-offset":["get","text-offset"],"icon-offset":["get","icon-offset"],"icon-size":["get","icon-size"],"text-size":["get","text-size"],"icon-allow-overlap":!0,"text-allow-overlap":!0,"icon-ignore-placement":!0,"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"],"icon-opacity":["get","icon-opacity"]},filter:["==","$type","Point"]}},MapPlatForm.Base.MapMultiMarkers.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf(this._layerID)){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&0<o.length)return[o[0]]}return[]},MapPlatForm.Base.MapMultiMarkers.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activePoint&&this.mouseOut(this.activePoint),this.activeFeature=null,this.activePoint=null},MapPlatForm.Base.MapMultiMarkers.prototype._initInteractive=function(t){var i=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var e=i._getFeature(t);0!=e.length?i.activeFeature&&e[0].properties.id==i.activeFeature.properties.id||(i._mouseout(),e&&0<e.length&&(i._map._obj.getCanvasContainer().style.cursor="pointer",i.activeFeature=e[0],i.activePoint=i._points[e[0].properties.id],i.mouseOver&&i.activePoint&&(i.mouseOver(i.activePoint),i._map._obj.setFilter(i._activeMarkerLayer.id,["==","id",i.activePoint.id])))):i._mouseout()}),!this._clickFun&&(this._clickFun=function(){i.activePoint&&i.click(i.activePoint)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapMultiMarkers.prototype.addMarkers=function(t){if(t&&0<t.length)for(var e=0;e<t.length;e++)if(t[e].opts.url){this._points[t[e].id]=t[e];var i=NPMap.T.setPoint(this._map,t[e]),o=t[e].opts||{};this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:[i.lon,i.lat]},properties:{id:t[e].id,layerId:this._layerID,title:o.label?o.label:"",icon:o.url,"icon-offset":o.iconOffset?o.iconOffset:void 0,"text-offset":o.labelOffset?o.labelOffset:void 0,"text-opacity":o.textOpacity?o.textOpacity:1,"icon-opacity":o.iconOpacity?o.iconOpacity:1,"icon-rotate":o.rotation?o.rotation:void 0,"icon-size":o.iconSize?o.iconSize:1,"text-size":o.fontSize?o.fontSize:12,"icon-rotation-alignment":o.showInMap?"map":"viewport","text-color":o.fontColor?o.fontColor:void 0}})}this._map._obj.getSource(this._layerID)?this._map._obj.getSource(this._layerID+"_source").setData(this._source.data):(this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._markerLayer),this._map._obj.addLayer(this._activeMarkerLayer)),this._initInteractive()},MapPlatForm.Base.MapMultiMarkers.prototype.setStyle=function(t){this._map&&!this._map._obj.getLayer(this._layerID+"_activeSymbol")&&(t&&t.url&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-image",t.url),t&&t.iconSize&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-size",t.iconSize),t&&t.fontSize&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-size",t.fontSize),t&&t.fontColor&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-color",t.fontColor),t&&t.iconOffset&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","icon-offset",t.iconOffset),t&&t.textOffset&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-offset",t.textOffset),t&&t.label&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","text-field",t.label))},MapPlatForm.Base.MapMultiMarkers.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.removeLayer(this._markerLayer.id),this._map._obj.removeLayer(this._activeMarkerLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activePoint=null,this._points={}},MapPlatForm.Base.MapMultiMarkers.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID)&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapMultiMarkers.prototype.setVisibile=function(t){t?(this._map._obj.getLayer(this._layerID+"_symbol")&&this._map._obj.setLayoutProperty(this._layerID+"_symbol","visibility","visible"),this._map._obj.getLayer(this._layerID+"_activeSymbol")&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","visibility","visible")):(this._map._obj.getLayer(this._layerID+"_symbol")&&this._map._obj.setLayoutProperty(this._layerID+"_symbol","visibility","none"),this._map._obj.getLayer(this._layerID+"_activeSymbol")&&this._map._obj.setLayoutProperty(this._layerID+"_activeSymbol","visibility","none"))},function(){var y=function(t,e,i){this.lonLats=t,this.context=e,this.opts=i,this.visibile=!0,this.pixels=[],this.nears=[],this.defaultStrokeColor="rgb(255,255,255)",this.strokeColor=this.defaultStrokeColor,this.fillColor=(this.strokeColor.substring(0,this.strokeColor.lastIndexOf(")"))+",1)").replace("rgb","rgba"),this.defaultFillColor=this.fillColor,this.backOffset=[40,0],this.acvtiveOffset=[5,0],this.font="18px 微软雅黑",this.fontColor="#000000",this.value=1,this.mainValue=1e3,this.center=this.opts.center?this.opts.center:null,this.name=this.opts.name?this.opts.name:"",this.resetStyle=function(){this.fillColor=this.defaultFillColor,this.strokeColor=this.defaultStrokeColor},this.reset=function(t,e,i,o){this.resetStyle(),this.pixels=[];for(var r=0,a=0,s=0,n=0;n<this.lonLats.length;n++)el={x:e.x+(this.lonLats[n][0]-t.lon)/i,y:e.y-(this.lonLats[n][1]-t.lat)/i},r+=el.x,a+=el.y,s++,this.pixels.push(el);this.centerXY={x:r/s,y:a/s},this.center&&o&&(this.centerXY={x:e.x+(this.center.lon-t.lon)/i,y:e.y-(this.center.lat-t.lat)/i})},this.draw=function(){if(0!=this.pixels.length){this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);if(this.context.closePath(),this.gradientColor1&&this.gradientColor2){var e=this.context.createLinearGradient(this.context.canvas.width/2,0,this.context.canvas.width/2,this.context.canvas.height);e.addColorStop(0,this.gradientColor1),e.addColorStop(1,this.gradientColor2),this.context.fillStyle=e,this.context.fill()}else this.context.fillStyle=this.fillColor,this.context.fill()}},this.drawLine=function(){if(0!=this.pixels.length){this.context.beginPath(),this.context.lineJoin="round",this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.strokeStyle=this.strokeColor,this.context.lineWidth=1,this.context.stroke()}},this.drawTJ=function(t){if(void 0!==this.value){var e=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value,this.centerXY.x-5,this.centerXY.y-20);t?(e.addColorStop(0,this._getOpacityColor(this.TJActivecolor)),e.addColorStop(1,this.TJActivecolor)):(e.addColorStop(0,this._getOpacityColor(this.TJcolor)),e.addColorStop(1,this.TJcolor)),this.context.fillStyle=e,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value,10,85*this.value),0<this.mainValue&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-20-.5,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=t?this.TJActivetextcolor:this.TJtextcolor,this.context.fillText(this.mainValue,this.centerXY.x,this.centerXY.y-85*this.value-30),this.context.restore())}},this._getOpacityColor=function(t){return-1<t.indexOf("rgba")?t=t.substring(0,t.lastIndexOf(","))+",0)":-1<t.indexOf("rgb")&&(t=(t=t.replace("rgb","rgba")).substring(0,xx.indexOf(")"))+",0)"),t},this.drawTJ2=function(t){if(void 0!==this.value){var e=this.context.createLinearGradient(this.centerXY.x-5,this.centerXY.y-85*this.value*t,this.centerXY.x-5,this.centerXY.y-20);e.addColorStop(0,this._getOpacityColor(this.TJcolor)),e.addColorStop(1,this.TJcolor),this.context.fillStyle=e,this.context.fillRect(this.centerXY.x-5,this.centerXY.y-20-85*this.value*t,10,85*this.value*t),0<this.mainValue&&(this.context.beginPath(),this.context.arc(this.centerXY.x,this.centerXY.y-.5-20,5,0,Math.PI),this.context.fill()),this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font="italic small-caps bold 14px arial",this.context.fillStyle=this.TJtextcolor,this.context.fillText(Math.ceil(this.mainValue*t),this.centerXY.x,this.centerXY.y-85*this.value*t-30),this.context.restore())}},this.drawBack=function(){if(0!=this.pixels.length){for(var t=1;t<this.pixels.length-1;t++){this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x+this.backOffset[0],this.pixels[t].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x+this.backOffset[0],this.pixels[t+1].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x+this.backOffset[0],this.pixels[t].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x+this.backOffset[0],this.pixels[t+1].y+this.backOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath();var e=(this.pixels[t+1].x+this.pixels[t].x)/2,i=(this.pixels[t+1].y+this.pixels[t].y)/2,o=this.context.createLinearGradient(e,i,e+this.backOffset[0],i+this.backOffset[1]);o.addColorStop(0,this.backShaderColor),o.addColorStop(1,this.backShaderColor2),this.context.fillStyle=o,this.context.fill()}this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore()}},this.drawActive=function(){if(0!=this.pixels.length)if(this.JianBian){this.context.beginPath(),this.context.moveTo(this.pixels[0].x,this.pixels[0].y);for(t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x,this.pixels[t].y);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}else{for(var t=1;t<this.pixels.length-1;t++)this.context.save(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x-this.acvtiveOffset[0],this.pixels[t+1].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.clip(),this.context.clearRect(0,0,this.context.canvas.width,this.context.canvas.height),this.context.restore(),this.context.beginPath(),this.context.moveTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x-this.acvtiveOffset[0],this.pixels[t+1].y-this.acvtiveOffset[1]),this.context.lineTo(this.pixels[t+1].x,this.pixels[t+1].y),this.context.lineTo(this.pixels[t].x,this.pixels[t].y),this.context.closePath(),this.context.fillStyle=this.backShaderColor,this.context.fill();this.context.beginPath(),this.context.moveTo(this.pixels[0].x-this.acvtiveOffset[0],this.pixels[0].y-this.acvtiveOffset[1]);for(var t=1;t<this.pixels.length;t++)this.context.lineTo(this.pixels[t].x-this.acvtiveOffset[0],this.pixels[t].y-this.acvtiveOffset[1]);this.context.closePath(),this.context.fillStyle=this.hoverColor,this.context.fill()}},this.drawTitle=function(){this.name&&this.centerXY&&(this.context.textAlign="center",this.context.font=this.font,this.context.fillStyle=this.fontColor,this.context.fillText(this.name,this.centerXY.x,this.centerXY.y),this.context.restore())},this.crossMul=function(t,e){return t.x*e.y-t.y*e.x},this.checkCross=function(t,e,i,o){var r={x:t.x-i.x,y:t.y-i.y},a={x:e.x-i.x,y:e.y-i.y},s={x:o.x-i.x,y:o.y-i.y},n=this.crossMul(r,s)*this.crossMul(a,s);return r={x:i.x-t.x,y:i.y-t.y},a={x:o.x-t.x,y:o.y-t.y},s={x:e.x-t.x,y:e.y-t.y},n<=0&&this.crossMul(r,s)*this.crossMul(a,s)<=0},this.containsPoint=function(t,e){var i,o,r,a;o={x:-1e5,y:(i=t).y};for(var s=0,n=0;n<e.length-1;n++)r=e[n],a=e[n+1],this.checkCross(i,o,r,a)&&s++;return r=e[e.length-1],a=e[0],this.checkCross(i,o,r,a)&&s++,s%2!=0},this.isContains=function(t){if(void 0!==this.pixels[0].x)return this.containsPoint(t,this.pixels);for(var e=0,i=0;i<this.pixels.length;i++)this.containsPoint(t,this.pixels[i])&&e++;return e%2!=0},this.setStyle=function(t){t&&(t.fillColor&&(this.fillColor=t.fillColor),t.strokeColor&&(this.strokeColor=t.strokeColor))}};MapPlatForm.Base.ShapMap=function(t,e,i){for(var o in this.mapContainer=t,this.CLASS_NAME="MapPlatForm.Base.ShapMap",this.fullExtent=this._getExtent(e),this.scrollTop=0,this._regions=[],this._backRegion=null,this.canvas=document.createElement("canvas"),this.canvas.style.position="absolute",this.canvas.style.left="0px",this.canvas.style.top="0px",this.canvas.width=t.offsetWidth,this.canvas.height=t.offsetHeight,this.canvas.style.pointerEvents="none",this.canvas.style.zIndex=205,t.appendChild(this.canvas),this.canvas.setAttribute("class","olScrollable"),this.context=this.canvas.getContext("2d"),this.canvasActive=document.createElement("canvas"),this.canvasActive.style.position="absolute",this.canvasActive.style.left="0px",this.canvasActive.style.top="0px",this.canvasActive.width=t.offsetWidth,this.canvasActive.height=t.offsetHeight,this.canvasActive.style.pointerEvents="none",this.canvasActive.style.zIndex=206,t.appendChild(this.canvasActive),this.canvasActive.setAttribute("class","olScrollable"),this.contextActive=this.canvasActive.getContext("2d"),this.points=i&&i.points?i.points:[],this.acvtiveRegion=null,this.legendColor=i&&i.legendColor?i.legendColor:{},this.legendValue=i&&i.legendValue?i.legendValue:{},this.maxValue=0,this.showAllTJ=!i||!1!==i.showAllTJ,this.showAllNames=!(!i||!i.showAllNames),this.legendValue)this.legendValue[o]>this.maxValue&&(this.maxValue=this.legendValue[o]);this.maxValue=this.maxValue?this.maxValue:100,"{}"===JSON.stringify(this.legendColor)?(this.JianBian=!0,this.gradientColor1=i&&i.gradientColor1?i.gradientColor1:"#3f96c3",this.gradientColor2=i&&i.gradientColor2?i.gradientColor2:"#175579",this.hoverColor=i&&i.hoverColor?i.hoverColor:"#154e6e"):this.hoverColor=i&&i.hoverColor?i.hoverColor:null,this.JianBian?this.backShaderColor=i&&i.backShaderColor?i.backShaderColor:"#11a4b5":this.backShaderColor=i&&i.backShaderColor?i.backShaderColor:"#bcdefd",this.backShaderColor2=i&&i.backShaderColor2?i.backShaderColor2:this.backShaderColor,this.JianBian?this.strokeColor=i&&i.strokeColor?i.strokeColor:"#15b1e5":this.strokeColor=i&&i.strokeColor?i.strokeColor:"#FFFFFF",this.TJcolor=i&&i.TJcolor?i.TJcolor:"rgba(255,198,0,255)",this.TJtextcolor=i&&i.TJtextcolor?i.TJtextcolor:this.TJcolor,this.TJActivecolor=i&&i.TJActivecolor?i.TJActivecolor:"rgba(242,19,75,255)",this.TJActivetextcolor=i&&i.TJActivetextcolor?i.TJActivetextcolor:this.TJActivecolor,this.backOffset=i&&i.backOffset?i.backOffset:[40,0],this.acvtiveOffset=i&&i.acvtiveOffset?i.acvtiveOffset:[5,0],this.font=i&&i.font?i.font:"14px 宋体",this.fontColor=i&&i.fontColor?i.fontColor:"#000000",this.shadowColor=i&&i.shadowColor?i.shadowColor:"rgba(61,154,235,0.2)",this.fillColors=["#7fc3ff","#3d9aeb","#2a7cc4","#215e94"],this.mouseOver=i?i.mouseOver:null,this.mouseOut=i?i.mouseOut:null,this.mouseMove=i?i.mouseMove:null,this.click=i?i.click:null,this.starRadio=i?i.starRadio:3,this.starColor=i?i.starColor:"rgba(255,255,255,0.8)",this._initPoints(),this.addGeoRegions(e)},MapPlatForm.Base.ShapMap.prototype._initInteractive=function(){var r=this;this.mapContainer.onmousemove=function(t){var e={x:t.offsetX,y:t.offsetY};e.y=e.y+r.scrollTop;for(var i=!1,o=0;o<r._regions.length;o++)if(r._regions[o].isContains(e)){if(r._regions[o]==r.acvtiveRegion)return void(r.mouseMove&&r.mouseMove(r.acvtiveRegion,e));i=!0,r.mouseOut&&r.acvtiveRegion&&(r.refresh(),r.mouseOut(r.acvtiveRegion)),r.acvtiveRegion=r._regions[o],r.mouseOver&&(r.refresh(r.acvtiveRegion),r.acvtiveRegion.drawTJ(!0),r.acvtiveRegion.drawTitle(),r.mouseOver(r.acvtiveRegion,e)),r.mouseMove&&r.mouseMove(r.acvtiveRegion,e);break}!i&&r.acvtiveRegion&&(r.mouseOut&&(r.refresh(),r.mouseOut(r.acvtiveRegion)),r.acvtiveRegion=null)},this.mapContainer.onmousedown=function(t){var e={x:t.offsetX,y:t.offsetY};r.curentXY=e},this.mapContainer.onclick=function(t){if(t.offsetX==r.curentXY.x&&t.offsetY==r.curentXY.y){var e={x:t.offsetX,y:t.offsetY};e.y=e.y+r.scrollTop;for(var i=0;i<r._regions.length;i++)if(r._regions[i].isContains(e)){isContains=!0,r.click&&r.click(r._regions[i]);break}}}},MapPlatForm.Base.ShapMap.prototype._initPoints=function(){for(var t=[],e=Math.ceil(this.points.length/1e3),i=0;i<this.points.length;i+=e)t.push(this.points[i]);this.points=t},MapPlatForm.Base.ShapMap.prototype._resetAndDrawPoint=function(t,e,i){for(var o=0;o<this.points.length;o++){var r=e.x+(this.points[o][0]-t.lon)/i,a=e.y-(this.points[o][1]-t.lat)/i;this.context.beginPath();var s=this.context.createRadialGradient(r,a,0,r,a,this.starRadio);s.addColorStop(0,this.starColor),s.addColorStop(1,"rgba(255,255,255,0)"),this.context.fillStyle=s,this.context.arc(r,a,this.starRadio,0,2*Math.PI,!0),this.context.closePath(),this.context.fill()}},MapPlatForm.Base.ShapMap.prototype.addGeoRegions=function(t){for(var e,i=this.fullExtent,o={lon:(i.minX+i.maxX)/2,lat:(i.minY+i.maxY)/2},r={x:this.canvas.width/2,y:this.canvas.height/2},a=(Math.abs(i.maxY-i.minY)/this.canvas.height>Math.abs(i.maxX-i.minX)/this.canvas.width?(e=this.canvas.height,Math.abs(i.maxY-i.minY)):(e=this.canvas.width,Math.abs(i.maxX-i.minX)))/(e-80),s=0;s<t.geometry.coordinates.length;s++)for(var n=t.geometry.coordinates[s],l=0;l<n.length;l++){var h=n[l];this._backRegion=new y(h,this.context,{center:o})}if(this._backRegion.strokeColor=this.strokeColor,this._backRegion.backOffset=this.backOffset,this._backRegion.backShaderColor=this.backShaderColor,this._backRegion.backShaderColor2=this.backShaderColor2,this._backRegion.shadowColor=this.shadowColor,this._backRegion.reset(o,r,a),this._backRegion.drawBack(),this._resetAndDrawPoint(o,r,a),this.JianBian&&(this._backRegion.gradientColor1=this.gradientColor1,this._backRegion.gradientColor2=this.gradientColor2,this._backRegion.JianBian=this.JianBian,this._backRegion.draw()),t.districts)for(s=0;s<t.districts.length;s++)if(t.districts[s]){var p=t.districts[s].geometry.coordinates[0][0],c=t.districts[s].name,u=JSON.parse(t.districts[s].center).coordinates,m=new y(p,this.contextActive,{center:u,name:c});m.defaultStrokeColor=this.strokeColor,m.strokeColor=this.strokeColor,m.defaultFillColor=this.legendColor[c]?this.legendColor[c]:this.fillColors[s%4],m.fillColor=this.legendColor[c]?this.legendColor[c]:this.fillColors[s%4],m.hoverColor=this.hoverColor?this.hoverColor:m.fillColor,m.JianBian=this.JianBian,m.backShaderColor=this.backShaderColor,m.backShaderColor2=this.backShaderColor2,m.acvtiveOffset=this.acvtiveOffset,m.font=this.font,m.fontColor=this.fontColor,m.value=void 0!==this.legendValue[c]?this.legendValue[c]/this.maxValue:void 0,m.mainValue=this.legendValue[c]?this.legendValue[c]:0,m.TJcolor=this.TJcolor,m.TJtextcolor=this.TJtextcolor,m.TJActivecolor=this.TJActivecolor,m.TJActivetextcolor=this.TJActivetextcolor,m.reset(o,r,a),this._regions.push(m)}this.v=0,this._anim(),this._initInteractive()},MapPlatForm.Base.ShapMap.prototype._anim=function(){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left="0px",this.canvasActive.style.top="0px";for(var t=0;t<this._regions.length;t++){var e=this._regions[t];this.JianBian?e.drawLine(!0):e.draw(!0),this.showAllNames&&e.drawTitle()}if(this.showAllTJ){this.v=this.v+.02;for(var i=0;i<this._regions.length;i++)this._regions[i].drawTJ2(this.v);var o=this;this.v<1&&setTimeout(function(){o._anim()},2)}},MapPlatForm.Base.ShapMap.prototype._getExtent=function(t){for(var e,i,o,r,a=t.geometry.coordinates,s=0;s<a.length;s++)for(var n=0;n<a[s].length;n++)for(var l=a[s][n],h=0;h<l.length;h++)0==n&&0==h&&0==s?(e=l[h][0],o=l[h][1],i=l[h][0],r=l[h][1]):(e>l[h][0]&&(e=l[h][0]),o<l[h][0]&&(o=l[h][0]),i>l[h][1]&&(i=l[h][1]),r<l[h][1]&&(r=l[h][1]));return{minX:e,minY:i,maxX:o,maxY:r}},MapPlatForm.Base.ShapMap.prototype.destory=function(){this.mapContainer.onclick=null,this.mapContainer.onmousemove=null,this.mapContainer.onmousedown=null,this.mapContainer&&(this.mapContainer=null),this._regions=[],this._backRegion=null,this.canvas.remove(),this.canvasActive.remove()},MapPlatForm.Base.ShapMap.prototype.refresh=function(t){this.contextActive.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasActive.style.left="0px",this.canvasActive.style.top="0px";for(var e=0;e<this._regions.length;e++){var i=this._regions[e];this.JianBian?i.drawLine(!0):i.draw(!0),this.showAllNames&&i.drawTitle()}if(t&&t.drawActive(),this.showAllTJ)for(var o=0;o<this._regions.length;o++)this._regions[o].drawTJ()}}(),function(){var t=MapPlatForm.Base.ArrowLine=function(t,e){this._map=t,this.id=NPMap.Utils.BaseUtils.uuid(),this._visible=!0,this._layers=[],this.image=new Image,this.image.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgEAYAAAAj6qa3AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAAgY0hSTQAAeiYAAICEAAD6AAAAgOgAAHUwAADqYAAAOpgAABdwnLpRPAAAAAZiS0dEAAAAAAAA+UO7fwAAAAlwSFlzAAAASAAAAEgARslrPgAAAjBJREFUaN7tmCFIQ1EUhvd0TmQTQdAgIrLkkmkIYjBYTGPBYhKLxWKwG8dQhphtYjCYLILNooJgGQwMBhGDhgV1bGznN/xb2GTHqfd6ZHjKz+DBvv977913uaHQ/7gdCAQSiVhz/HLhwUEAAC4vmbUac2+PGQTWnJ4FLC2h3QgEsr1tzelPAABgcpJFX150EZub1rz+RAgEsrDAtuVyGwvCXF215vUnAgCwvNy8FrROtcpMp615/YkQCGR9Heq8vfG6uTlrXs8islldRLHI66anrXndC6h/Bllwf18X8fDQWFStuT2J6O1lHh/rIm5vKWx01Jrbk4iBAeb5uS7i6ooiYjFrbvciBAIZGmLRmxt9/3B2xuzvt+Z2LwIAMDbGvLvTn4jDQ2ZPjzW3JxFTU7zTT0/6E7GzY83rT4RAIDMznW2tk8mf/l/YuvCHCUJBKCiX+aNa/UQXrHGdDW9rPM58fNTvfDZrzeuuuEAgIyNsVyjoi+DBAbMLzheaD1Kur/XiJye8Pvz3XtnvFY9EWOz0VH/ULy74Ixq15v558fr3m8WOjvQ7ns8zh4etuR0L2N3Vi9/fU9DEhDWv4+JbW3rx52dmImHN6664QCBra3rx11fm7Kw1r7viAIBUitk4+mqdSoW5uGjN67j4/DyzVGqztNcPRVdWrHndFRcIZHycxYpF/ZHf2LDmdS8AAJBO68UzGWtOfwIaO7qmjUvj3c/lmF2wdf2akL4+a47/6XDeAd6F40H7Yd0JAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDE4LTEyLTI4VDE1OjEzOjQyKzA4OjAwCT5KagAAACV0RVh0ZGF0ZTptb2RpZnkAMjAxOC0xMi0yOFQxNToxMzo0MiswODowMHhj8tYAAABKdEVYdHN2ZzpiYXNlLXVyaQBmaWxlOi8vL2hvbWUvYWRtaW4vaWNvbi1mb250L3RtcC9pY29uX3R4N3JpcjU3ZXpiL2ppYW50b3Uuc3Zn6JRGFAAAAABJRU5ErkJggg==",this.size=32,this.offsetX=-32,this._isAnimation=e,this._isLoadImage=!1};t.prototype._drawImage=function(){var e=this;return{width:this.size,height:this.size,data:new Uint8Array(this.size*this.size*4),onAdd:function(){var t=document.createElement("canvas");t.width=this.width,t.height=this.height,this.context=t.getContext("2d");this.context},render:function(){if(!e._isAnimation&&e._isLoadImage)return!1;e.offsetX=e.offsetX+1,0<e.offsetX&&(e.offsetX=-32);var t=this.context;return t.clearRect(0,0,this.width,this.height),t.drawImage(e.image,e.offsetX,0,32,32),t.drawImage(e.image,e.offsetX+32,0,32,32),this.data=t.getImageData(0,0,this.width,this.height).data,e._map._obj.triggerRepaint(),e._isLoadImage=!0}}},t.prototype.load=function(t){if(this._map._obj.hasImage(this.id+"arrawIcon")||this._map._obj.addImage(this.id+"arrawIcon",this._drawImage(),{pixelRatio:1}),t&&0<t.length)for(var e=0;e<t.length;e++){var i={id:this.id+"_line"+t[e].id,type:"line",source:t[e].layer._layerID+"_source",paint:{"line-width":t[e]._weight,"line-translate-anchor":"map","line-opacity":t[e]._visible?t[e]._opacity:1,"line-pattern":this.id+"arrawIcon"},filter:["==","id",t[e].id]};this._layers.push(i),this._map._obj.addLayer(i)}},t.prototype.remove=function(){if(this._map._obj.hasImage(this.id+"arrawIcon")&&this._map._obj.removeImage(this.id+"arrawIcon"),this._layers.length)for(var t=0;t<this._layers.length;t++)this._map._obj.getLayer(this._layers[t].id)&&this._map._obj.removeLayer(this._layers[t].id)},t.prototype.show=function(t){if(this._layers.length&&!this._visible){for(var e=0;e<this._layers.length;e++)this._map._obj.setLayoutProperty(this._layers[e].id,"visibility","visible");this._visible=!0}},t.prototype.hide=function(t){if(this._layers.length&&this._visible){for(var e=0;e<this._layers.length;e++)this._map._obj.setLayoutProperty(this._layers[e].id,"visibility","none");this._visible=!1}}}(),function(){var i=function(t,e){this.points=[t.point],this._map=e,this.id=t.id,this._point=t,this._count=60,this.offset=t.offset,this._currentIndex=0,this._linePoints=[],this._lineLength=20,this._visible=!0,this._show3D=!1;var i=document.createElement("div");i.className="marker",i.style.backgroundImage="url("+t.url+")",i.style.width=(t.iconSize?t.iconSize[0]:32)+"px",i.style.height=(t.iconSize?t.iconSize[1]:32)+"px",i.style.cursor="pointer";var o=this;i.addEventListener("click",function(){return t.click&&t.click instanceof Function&&t.click(o._point),!1}),this._element=i;var r=NPMap.T.setPoint(this._map,{lon:t.point.lon,lat:t.point.lat});this.marker=new mapboxgl.Marker({element:i,offset:this.offset}).setLngLat([r.lon,r.lat]),this.setUrl=function(t){this._point.url=t,i.style.backgroundImage="url("+t+")"},this.addToMap=function(){this.marker.addTo(this._map._obj)},this.addPoint=function(t){if(this._currentIndex=0,this.points.push(t),2!=this.points.length){var e=this.points[this.points.length-2],i=this.points[this.points.length-1],o=(i.lon-e.lon)/this._count*this._currentIndex+e.lon,r=(i.lat-e.lat)/this._count*this._currentIndex+e.lat,a=NPMap.T.setPoint(this._map,{lon:o,lat:r});this.marker.setLngLat([a.lon,a.lat]),0<this._linePoints.length&&a.lon==this._linePoints[this._linePoints.length-1].lon&&a.lat==this._linePoints[this._linePoints.length-1].lat||(this._linePoints.push(a),this._linePoints=this._linePoints.slice(-this._lineLength))}},this._calculateAngle=function(){var t=this.points[this.points.length-2],e=this.points[this.points.length-1];if(t.lon==e.lon&&t.lat==e.lat)return-100;var i=180*Math.atan2(e.lat-t.lat,e.lon-t.lon)/Math.PI;return(i=0<(i-=270)?i:360+i)/180*Math.PI},this.moveNext=function(){if(!(this.points.length<2||this._currentIndex>=this._count-1)){this._currentIndex++;var t=this.points[this.points.length-2],e=this.points[this.points.length-1],i=(e.lon-t.lon)/this._count*this._currentIndex+t.lon,o=(e.lat-t.lat)/this._count*this._currentIndex+t.lat,r=NPMap.T.setPoint(this._map,{lon:i,lat:o});if(this.marker.getLngLat().lng===r.lon&&this.marker.getLngLat().lat===r.lat||this.marker.setLngLat([r.lon,r.lat]),0<this._linePoints.length&&r.lon==this._linePoints[this._linePoints.length-1].lon&&r.lat==this._linePoints[this._linePoints.length-1].lat||(this._linePoints.push(r),this._linePoints=this._linePoints.slice(-this._lineLength)),this._show3D){var a=-100;t.lon==e.lon&&t.lat==e.lat&&(a=-100),this._map.getDistance(t,e)<1&&(a=-100);var s=[0,0,a=this._calculateAngle()];-100==a&&(s=[this._modelTransform.rotateX,this._modelTransform.rotateY,this._modelTransform.rotateZ]),this._modelTransform={translateX:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).x,translateY:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).y,translateZ:mapboxgl.MercatorCoordinate.fromLngLat([r.lon,r.lat],0).z,rotateX:s[0],rotateY:s[1],rotateZ:s[2],scale:3e-8}}}},this.hide=function(){this._visible=!1,this._element.style.display="none"},this.show=function(){this._visible=!0,this._element.style.display="block"},this.hide3DModel=function(){this._show3D&&(this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._element.style.display="block",this._show3D=!1)},this.show3DModel=function(){if(!this._show3D&&this._point.url3d){this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._element.style.display="none",this._show3D=!0;var t=0,e=0,i=[0,0,0];if(1<this.points.length){var o=this.points[this.points.length-2],r=this.points[this.points.length-1];t=(r.lon-o.lon)/this._count*this._currentIndex+o.lon,e=(r.lat-o.lat)/this._count*this._currentIndex+o.lat;i=[0,0,this._calculateAngle()]}else i=[0,0,0];var a=NPMap.T.setPoint(this._map,{lon:t,lat:e});this._modelTransform={translateX:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).x,translateY:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).y,translateZ:mapboxgl.MercatorCoordinate.fromLngLat([a.lon,a.lat],0).z,rotateX:i[0],rotateY:i[1],rotateZ:i[2],scale:3e-8};var n=this,s={id:"GPSRefreshManager-3DModel",type:"custom",renderingMode:"3d",onAdd:function(t,e){this.camera=new THREE.Camera,this.scene=new THREE.Scene;var i=new THREE.DirectionalLight(16777215);i.position.set(-200,0,200).normalize(),this.scene.add(i);var o=new THREE.DirectionalLight(16777215);o.position.set(200,0,200).normalize(),this.scene.add(o);var r=new THREE.DirectionalLight(16777215);r.position.set(0,-200,200).normalize(),this.scene.add(r);var a=new THREE.DirectionalLight(16777215);a.position.set(0,200,200).normalize(),this.scene.add(a),(new THREE.GLTFLoader).load(n._point.url3d,function(t){this.scene.add(t.scene)}.bind(this)),this.map=t,this.renderer=new THREE.WebGLRenderer({antialias:!0,canvas:t.getCanvas(),context:e}),this.renderer.autoClear=!1},render:function(t,e){var i=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(1,0,0),n._modelTransform.rotateX),o=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,1,0),n._modelTransform.rotateY),r=(new THREE.Matrix4).makeRotationAxis(new THREE.Vector3(0,0,1),n._modelTransform.rotateZ),a=(new THREE.Matrix4).fromArray(e),s=(new THREE.Matrix4).makeTranslation(n._modelTransform.translateX,n._modelTransform.translateY,n._modelTransform.translateZ).scale(new THREE.Vector3(n._modelTransform.scale,-n._modelTransform.scale,n._modelTransform.scale)).multiply(i).multiply(o).multiply(r);this.camera.projectionMatrix.elements=e,this.camera.projectionMatrix=a.multiply(s),this.renderer.state.reset(),this.renderer.render(this.scene,this.camera),this.map.triggerRepaint()}};this._map._obj.getLayer("GPSRefreshManager-3DModel")||(this._map._obj.getLayer("city_normal_building_id")?this._map._obj.addLayer(s,"city_normal_building_id"):this._map._obj.addLayer(s))}}},t=MapPlatForm.Base.GPSRefreshManager=function(t,e){this._map=t,this._gpsPoints={},this._status=1,this._popup=null,this._layerID=NPMap.Utils.BaseUtils.uuid(),this.interval=e&&e.interval?e.interval:50,this.time=e&&e.time?e.time:3e3,this._lineColor=e&&e.lineColor?e.lineColor:"red",this._lineWidth=e&&e.lineWidth?e.lineWidth:4,this._lineLength=e&&e.lineLength?e.lineLength:20,this._lineTypes=e&&e.lineTypes?e.lineTypes:{car:this._lineColor,man:"yellow"},this._source={type:"geojson",lineMetrics:!0,data:{type:"FeatureCollection",features:[]}},this._map._obj.addSource(this._layerID+"_source",this._source),this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this._lineTypes.man,"line-width":this._lineWidth,"line-gradient":["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,this._lineTypes.man]},filter:["all",["==","visible",!0],["==","type","man"]]},this._map._obj.addLayer(this._lineLayer),this._lineLayer2={id:this._layerID+"_line2",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":this._lineTypes.man,"line-width":this._lineWidth,"line-gradient":["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,this._lineTypes.car]},filter:["all",["==","visible",!0],["==","type","car"]]},this._map._obj.addLayer(this._lineLayer2),this._map._obj.getLayer("city_normal_building_id")&&(this._map._obj.moveLayer(this._layerID+"_line","city_normal_building_id"),this._map._obj.moveLayer(this._layerID+"_line2","city_normal_building_id"))};t.prototype.setGPSLineTpes=function(t){this._map._obj.setPaintProperty(this._layerID+"_line","line-gradient",["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,t.man]),this._map._obj.setPaintProperty(this._layerID+"_line2","line-gradient",["interpolate",["linear"],["line-progress"],0,"rgba(0,0,0,0)",1,t.car])},t.prototype.addGPSPoints=function(t){for(var e=0;e<t.length;e++)t[e].url3d=t[e].url3d?t[e].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[t[e].id]=new i(t[e],this._map),this._gpsPoints[t[e].id]._count=this.time/this.interval,this._gpsPoints[t[e].id]._lineLength=this._lineLength,this._gpsPoints[t[e].id].addToMap()},t.prototype.updataGPSPoints=function(t){if(this._status)for(var e=0;e<t.length;e++)this._gpsPoints[t[e].id]?(t[e].url3d=t[e].url3d?t[e].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[t[e].id]._point=t[e],this._gpsPoints[t[e].id].addPoint(t[e].point)):(t[e].url3d=t[e].url3d?t[e].url3d:NPMap.Utils.BaseUtils.getHostPath()+"/Netposa/car3d.gltf",this._gpsPoints[t[e].id]=new i(t[e],this._map),this._gpsPoints[t[e].id]._count=this.time/this.interval,this._gpsPoints[t[e].id]._lineLength=this._lineLength,this._gpsPoints[t[e].id].addToMap())},t.prototype.setLineVisible=function(t){t?(this._map._obj.setLayoutProperty(this._lineLayer.id,"visibility","visible"),this._map._obj.setLayoutProperty(this._lineLayer2.id,"visibility","visible")):(this._map._obj.setLayoutProperty(this._lineLayer.id,"visibility","none"),this._map._obj.setLayoutProperty(this._lineLayer2.id,"visibility","none"))},t.prototype.setGPSVisibleByFilter=function(e,i,o){var r=this;Object.keys(r._gpsPoints).forEach(function(t){r._gpsPoints[t]._point[e]==i&&r.setGPSVisibleByID(t,o)})},t.prototype.setGPSVisibleByID=function(t,e){if(this._gpsPoints[t])if(e)this._gpsPoints[t].show();else{this._gpsPoints[t].hide();var i=this._gpsPoints[t].marker.getPopup();i&&i.isOpen()&&i.remove()}},t.prototype.show3DModelByID=function(t){this._gpsPoints[t]&&this._gpsPoints[t].show3DModel()},t.prototype.hide3DModelByID=function(t){this._gpsPoints[t]&&this._gpsPoints[t].hide3DModel()},t.prototype.setGPSStyleByID=function(t,e){this._gpsPoints[t]&&(e.url&&this._gpsPoints[t].setUrl(e.url),e.offset&&this._gpsPoints[t].marker.setOffset(e.offset),e.iconSize&&(this._gpsPoints[t]._element.style.width=e.iconSize[0]+"px",this._gpsPoints[t]._element.style.height=e.iconSize[1]+"px"))},t.prototype.setGPSInfoWindowByID=function(t,e,i){if(this._popup&&(this._popup.remove(),this._popup=null),this._gpsPoints[t]){var o=i&&i.width?i.width:0,r=i&&i.height?i.height:0,a=this._gpsPoints[t]._point.iconSize?this._gpsPoints[t]._point.iconSize[1]:32;this._gpsPoints[t]._point.iconSize&&this._gpsPoints[t]._point.iconSize[0];this._popup=new mapboxgl.Popup({closeButton:!1,closeOnClick:!1,onlyLeftTop:!0,offset:[0-o/2,0-a-r-2]}).setDOMContent(e).addTo(this._map._obj),this._gpsPoints[t].marker.setPopup(this._popup),this._activeID=t,this._popup._container.setAttribute("id",this._layerID+"Infowindow")}},t.prototype.closeGPSInfoWindow=function(){this._popup&&(this._popup.remove(),this._popup=null)},t.prototype.getGPSInfoWindowID=function(){return this._popup&&this._popup._container?this._popup._container.getAttribute("id"):null},t.prototype.unbindInfoWindow=function(){this._activeID&&this._gpsPoints[this._activeID]&&this._gpsPoints[this._activeID].marker._popup&&(this._gpsPoints[this._activeID].marker._popup._map=null,this._gpsPoints[this._activeID].marker._popup=null)},t.prototype.bindInfoWindow=function(){this._activeID&&this._gpsPoints[this._activeID]&&this._popup&&(this._popup._map=this._map._obj,this._gpsPoints[this._activeID].marker._popup=this._popup)},t.prototype.bindCamera=function(t,e){this._isTrack=!0,this._bindGPSID=t,this._callBack=e},t.prototype.unbindCamera=function(){this._isTrack=!1,this._bindGPSID=null,this._callBack=null},t.prototype.start=function(){var o=this;this._status=!0;var i=function(){if(o._status){if(o._source.data.features=[],Object.keys(o._gpsPoints).forEach(function(t){o._gpsPoints[t].moveNext(o._bindGPSID==t);for(var e={type:"Feature",geometry:{type:"LineString",coordinates:[]},properties:{id:t,visible:o._gpsPoints[t]._visible,type:o._gpsPoints[t]._point.type?o._gpsPoints[t]._point.type:"car"}},i=0;i<o._gpsPoints[t]._linePoints.length;i++)e.geometry.coordinates.push([o._gpsPoints[t]._linePoints[i].lon,o._gpsPoints[t]._linePoints[i].lat]);o._source.data.features.push(e)}),o._map._obj.getSource(o._layerID+"_source").setData(o._source.data),o._gpsPoints[o._bindGPSID]&&o._isTrack){var t=o._gpsPoints[o._bindGPSID].marker.getLngLat();o._map._obj.jumpTo({center:[t.lng,t.lat]});var e=NPMap.T.getPoint(o._map,{lon:t.lng,lat:t.lat});o._callBack&&o._callBack(o._bindGPSID,new NPMap.Geometry.Point(e.lon,e.lat))}setTimeout(i,o.interval-15)}};setTimeout(i,this.interval-15)},t.prototype.stop=function(){this._status=0},t.prototype.getGPSPositionByID=function(t){var e=this._gpsPoints[t].marker.getLngLat(),i={lon:e.lng,lat:e.lat};return NPMap.T.getPoint(this._map,i)},t.prototype.getGPSVisibleByID=function(t){return this._gpsPoints[t]._visible},t.prototype.destory=function(){this.stop();var e=this;Object.keys(this._gpsPoints).forEach(function(t){e._gpsPoints[t].marker.remove(),e._gpsPoints[t]=null}),this._map._obj.removeLayer(this._layerID+"_line"),this._map._obj.removeLayer(this._layerID+"_line2"),this._map._obj.removeSource(this._layerID+"_source"),this._map._obj.getLayer("GPSRefreshManager-3DModel")&&this._map._obj.removeLayer("GPSRefreshManager-3DModel"),this._gpsPoints={},this._map=null}}(),function(){MapPlatForm.Base.ShapMap3D=function(t,e,i){this.geoRegion=e;var o=++NPMap.LayerIndex;this._layerID="RegionLayer"+o,this.CLASS_NAME="MapPlatForm.Base.ShapMap3D",this._map=t;var r=GeoJSON.read(e.geometry);this.fullExtent=r.getExtent(),this._regions=[],this._layers=[],this.acvtiveLayer=null,this.acvtiveRegion=null,this.activeFeature=null,this.opts=i||{},this.fillColor=this.opts.fillColors?this.opts.fillColors:["#A4D3EE","#7EC0EE","#63B8FF","#5CACEE","#87CEFF"];var a={};if(this.geoRegion.districts)for(var s=0;s<this.geoRegion.districts.length;s++){var n=s%this.fillColor.length;a[this.geoRegion.districts[s].name]=this.fillColor[n]}this.mapParam=this.opts.mapParam?this.opts.mapParam:null,this.markers=this.opts.markers?this.opts.markers:null,this.legendColor=this.opts.legendColor?this.opts.legendColor:a,this.opacity=this.opts.opacity?this.opts.opacity:.5,this.hoverColor=this.opts.hoverColor?this.opts.hoverColor:"#154e6e",this.fontSize=this.opts.fontSize?this.opts.fontSize:12,this.fontColor=this.opts.fontColor?this.opts.fontColor:"#000000",this.fontOpacity=this.opts.fontOpacity?this.opts.fontOpacity:1,this.fontOffset=this.opts.fontOffset?this.opts.fontOffset:[0,-1],this.minZoom=this.opts.minZoom?this.opts.minZoom:5,this.maxZoom=this.opts.maxZoom?this.opts.maxZoom:12,this.maxExtrusionHeight=this.opts.maxExtrusionHeight?this.opts.maxExtrusionHeight:1e4,this.minExtrusionHeight=this.opts.minExtrusionHeight?this.opts.minExtrusionHeight:500,this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:function(){},this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:function(){},this.click=this.opts.click?this.opts.click:function(){},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this.labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"]},minzoom:this.minZoom,maxzoom:this.maxZoom,filter:["==","$type","Point"]},this.addGeoRegions(e)},MapPlatForm.Base.ShapMap3D.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf("RegionLayer")){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&0<o.length)return[o[0]]}return[]},MapPlatForm.Base.ShapMap3D.prototype._mouseout=function(){this._map._obj.getCanvas().style.cursor="",this.mouseOut&&this.activeFeature&&(this.refresh(),this.mouseOut(this.acvtiveRegion)),this.acvtiveRegion=null,this.activeFeature=null,this.acvtiveLayer=null},MapPlatForm.Base.ShapMap3D.prototype._initInteractive=function(t){this._map._events.through=!0;var i=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var e=i._getFeature(t);0!=e.length?i.activeFeature&&e[0].properties.id==i.activeFeature.properties.id||(i._mouseout(),e&&0<e.length&&(i._map._obj.getCanvas().style.cursor="pointer",i.activeFeature=e[0],i.acvtiveRegion=i._regions[e[0].properties.index],i.acvtiveLayer=i._layers[e[0].properties.index],i.mouseOver&&i.activeFeature&&(i.refresh(i.acvtiveLayer),i.mouseOver(i.acvtiveRegion)))):i._mouseout()}),!this._clickFun&&(this._clickFun=function(){i.acvtiveRegion&&i.click(i.acvtiveRegion)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.ShapMap3D.prototype.addGeoRegions=function(geoRegion){if(geoRegion.districts){for(var i=0;i<geoRegion.districts.length;i++)if(geoRegion.districts[i]){var polygon=GeoJSON.read(geoRegion.districts[i].geometry);this._regions.push(geoRegion.districts[i]);var f={type:"Feature",geometry:{type:geoRegion.districts[i].geometry.type,coordinates:polygon._getCoordinates(this._map)},properties:{id:polygon.id,fillColor:this.legendColor[geoRegion.districts[i].name],index:i}},center=geoRegion.districts[i].center?GeoJSON.read(eval("("+geoRegion.districts[i].center+")")):polygon.getCenter();center=NPMap.T.setPoint(this._map,center);var l={type:"Feature",geometry:{type:"Point",coordinates:[center.lon,center.lat]},properties:{id:polygon.id,title:geoRegion.districts[i].name,"text-offset":this.fontOffset,"text-opacity":this.fontOpacity,"text-size":this.fontSize,"text-color":this.fontColor,index:i}};this._source.data.features.push(f),this._source.data.features.push(l)}this._map._obj.addSource(this._layerID+"_source",this._source);for(var i=0;i<geoRegion.districts.length;i++)if(geoRegion.districts[i]){var polygonLayer={id:this._layerID+"_polygon"+i,type:"fill-extrusion",source:this._layerID+"_source",paint:{"fill-extrusion-color":this.legendColor[geoRegion.districts[i].name],"fill-extrusion-opacity":this.opacity,"fill-extrusion-translate-anchor":"map","fill-extrusion-height":{stops:[[this.minZoom,this.maxExtrusionHeight],[this.maxZoom,this.minExtrusionHeight]]},"fill-extrusion-base":0},minzoom:this.minZoom,maxzoom:this.maxZoom,filter:["all",["==","index",i],["==","$type","Polygon"]]};this._layers.push(polygonLayer),this._map._obj.addLayer(polygonLayer)}for(var i=0;i<this._layers.length;i++)this._map._obj.setPaintProperty(this._layers[i].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[i].name]);this._map._obj.addLayer(this.labelLayer)}this.mapParam?this._map.jumpTo(this.mapParam):this._map.zoomToExtent(this.fullExtent),this.markers&&this._map.addOverlays(this.markers),this._initInteractive()},MapPlatForm.Base.ShapMap3D.prototype.setLegendColor=function(t){this.legendColor=t;for(var e=0;e<this._layers.length;e++)this._map._obj.setPaintProperty(this._layers[e].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[e].name])},MapPlatForm.Base.ShapMap3D.prototype.getRegionByName=function(t){if("string"==typeof t&&void 0!==t&&this._regions&&0<this._regions.length)for(var e=0;e<this._regions.length;e++)if(this._regions[e].name==t)return this._regions[e];return[]},MapPlatForm.Base.ShapMap3D.prototype.flash=function(t){var e;e="string"==typeof t&&void 0===t.name?this.getRegionByName(t):t;for(var i=0;i<this._regions.length;i++)e&&this._regions[i].name==e.name&&this._flash(i)},MapPlatForm.Base.ShapMap3D.prototype._flash=function(e){var i=this,o=0;this.timer&&(window.clearTimeout(this.timer),this.show(),this.timer=null);var r=function(){if(++o%2){i._map._obj.setPaintProperty(i._layers[e].id,"fill-extrusion-opacity",0);for(var t=0;t<i._source.data.features.length;t++)i._source.data.features[t].properties.index===e&&(i._source.data.features[t].properties["text-opacity"]=0)}else{i._map._obj.setPaintProperty(i._layers[e].id,"fill-extrusion-opacity",.8);for(t=0;t<i._source.data.features.length;t++)i._source.data.features[t].properties.index===e&&(i._source.data.features[t].properties["text-opacity"]=1)}i._map._obj.getSource(i._layerID+"_source").setData(i._source.data),i.timer=o<6?window.setTimeout(r,500):null};r()},MapPlatForm.Base.ShapMap3D.prototype.destory=function(){if(this._map){this._initInteractive(!0);for(var t=0;t<this._layers.length;t++)this._map._obj.removeLayer(this._layers[t].id);this._map._obj.removeLayer(this.labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null}this._regions=[],this._layers=[],this.acvtiveRegion=null,this.activeFeature=null},MapPlatForm.Base.ShapMap3D.prototype.refresh=function(t){for(var e=0;e<this._layers.length;e++)this._map._obj.setPaintProperty(this._layers[e].id,"fill-extrusion-color",this.legendColor[this.geoRegion.districts[e].name]);t&&this._map._obj.setPaintProperty(t.id,"fill-extrusion-color",this.hoverColor)}}(),MapPlatForm.Base.MapGeoLine=function(t,e){this.CLASS_NAME="MapGeoLine",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._lines={},this.activeFeature=null,this.activeLine=null,this.mouseOver=e?e.mouseOver:null,this.mouseOut=e?e.mouseOut:null,this.click=e?e.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]}},MapPlatForm.Base.MapGeoLine.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf(this._layerID)){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&0<o.length)return[o[0]]}return[]},MapPlatForm.Base.MapGeoLine.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activeLine&&this.mouseOut(this.activeLine),this.activeFeature=null},MapPlatForm.Base.MapGeoLine.prototype._initInteractive=function(t){var i=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var e=i._getFeature(t);0!=e.length?i.activeFeature&&e[0].properties.id==i.activeFeature.properties.id||(i._mouseout(),e&&0<e.length&&(i._map._obj.getCanvasContainer().style.cursor="pointer",i.activeFeature=e[0],i.activeLine=i._lines[e[0].properties.id],i.mouseOver&&i.activeLine&&i.mouseOver(i.activeLine))):i._mouseout()}),!this._clickFun&&(this._clickFun=function(){i.activeLine&&i.click(i.activeLine)}),t?(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this._clickFun)):(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this._clickFun))},MapPlatForm.Base.MapGeoLine.prototype.addGeoLines=function(t){if(t&&0<t.length){for(var e=0;e<t.length;e++)this._lines[t[e].id]=t[e],this._source.data.features.push({type:"Feature",geometry:{type:"LineString",coordinates:t[e]._getCoordinates(this._map)},properties:{id:t[e].id,layerId:this._layerID,color:t[e]._color,width:t[e]._weight,hasDash:t[e]._lineStyle!=NPMap.LINE_TYPE_SOLID,opacity:t[e]._visible?t[e]._opacity:0,join:"round",visible:t[e]._visible}});this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._lineLayer)}this._initInteractive()},MapPlatForm.Base.MapGeoLine.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.removeLayer(this._lineLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activeLine=null,this._lines={}},MapPlatForm.Base.MapGeoLine.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapRoomControl=function(t,e){this.CLASS_NAME="MapRoomControl",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._activeID=null,this._minZoom=e&&e.showZoom?e.showZoom:17,this._floorControlStyle=e?e.floorControlStyle:null,this._events=new NPMap.Events(this,["changed"]),this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._floorRegionLayer={id:this._layerID+"_floor_region",type:"fill",source:this._layerID+"_source",paint:{"fill-color":"#f5f2eb","fill-opacity":1},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"]]},this._normalFloorLineLayer={id:this._layerID+"_normal_floor_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#adafb1","line-width":3},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"],["==","active",!1]]},this._activeFloorLineLayer={id:this._layerID+"_active_floor_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":"#f69428","line-width":3},minzoom:this._minZoom,filter:["all",["==","datatype","floor"],["==","$type","Polygon"],["==","active",!0]]},this._roomLayer={id:this._layerID+"_room",type:"fill-extrusion",source:this._layerID+"_source",layout:{},paint:{"fill-extrusion-color":"#d4b7dd","fill-extrusion-base":0,"fill-extrusion-height":2.5,"fill-extrusion-opacity":.8},minzoom:this._minZoom,filter:["all",["==","datatype","room"],["==","$type","Polygon"]]},this._labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{name}","text-font":["Microsoft YaHei"],"text-size":11,"text-offset":[0,0],"text-anchor":"center"},paint:{"text-color":"#3b3d3d","text-opacity":1,"text-halo-width":1,"text-halo-color":"#FFF"},minzoom:this._minZoom,filter:["==","$type","Point"]},this._addLayers(),this._mapService=new MapPlatForm.Base.MapService(t)},MapPlatForm.Base.MapRoomControl.prototype._addLayers=function(){this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._floorRegionLayer),this._map._obj.addLayer(this._normalFloorLineLayer),this._map._obj.addLayer(this._activeFloorLineLayer),this._map._obj.getLayer("city_normal_building_id")&&(this._map._obj.moveLayer(this._floorRegionLayer.id,"city_normal_building_id"),this._map._obj.moveLayer(this._normalFloorLineLayer.id,"city_normal_building_id"),this._map._obj.moveLayer(this._activeFloorLineLayer.id,"city_normal_building_id")),this._map._obj.addLayer(this._roomLayer),this._map._obj.addLayer(this._labelLayer)},MapPlatForm.Base.MapRoomControl.prototype.showFloorChangeInMap=function(){this._map.addEventListener("moveend",this._updateFloors,this),this._updateFloors()},MapPlatForm.Base.MapRoomControl.prototype._updateFloors=function(){var t=this._map.getZoom(),e=document.getElementById("NPGIS_Room_Control");if(t<this._minZoom?e&&(e.style.display="none"):e&&(e.style.display="block"),t>=this._minZoom&&t<21){var i=this._map.getContainer(),o=map._obj.unproject({x:0,y:0}),r=map._obj.unproject({x:i.offsetWidth,y:0}),a=map._obj.unproject({x:i.offsetWidth,y:i.offsetHeight}),s=map._obj.unproject({x:0,y:i.offsetHeight});o=new NPMap.Geometry.Point(o.lng,o.lat),r=new NPMap.Geometry.Point(r.lng,r.lat),a=new NPMap.Geometry.Point(a.lng,a.lat),s=new NPMap.Geometry.Point(s.lng,s.lat);var n=new NPMap.Geometry.Polygon([o,r,a,s,o]),l=this;this._mapService.queryRoomFloorsByGeometry(n,function(t){l.addFloors(t.data)})}},MapPlatForm.Base.MapRoomControl.prototype.addFloors=function(t){if(t||(t=[]),0==(this.floors=t).length)return this._activeID=null,this._source.data.features=[],this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data),void this._removeControl();var e=[];if(this._activeID==t[0].id)for(var i=0;i<this._source.data.features.length;i++)this._source.data.features[i].properties.buildid==this._activeID&&e.push(this._source.data.features[i]);this._source.data.features=e;for(var o=0;o<t.length;o++)0==o?this._activeID!==t[o].id&&(this._activeID=t[o].id,this._removeControl(),this._addControl(t[o]),this.addFloor(t[o],!0)):this.addFloor(t[o],!1);this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},MapPlatForm.Base.MapRoomControl.prototype._changeFloor=function(t,e){for(var i=[],o=0;o<this._source.data.features.length;o++)this._source.data.features[o].properties.active||i.push(this._source.data.features[o]);this._source.data.features=i;var r=this;this._mapService.queryRoomFloor(t,e,function(t){r.addFloor(t.data[0],!0),r._map&&r._map._obj.getSource(r._layerID+"_source").setData(r._source.data)})},MapPlatForm.Base.MapRoomControl.prototype.addFloor=function(t,e){var i=t.floor.floor,o=i.properties.namecode;i.properties={floorname:o,buildid:t.id,datatype:"floor",active:e},this._source.data.features.push(i);for(var r=0;r<t.floor.shops.length;r++){var a=t.floor.shops[r],s=a.properties.name,n=a.properties.font_anthor_point;a.properties={buildid:t.id,floorname:o,datatype:"room",active:e},this._source.data.features.push(a),this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:n},properties:{name:s,floorname:o,buildid:t.id,active:e}})}for(r=0;r<t.floor.cons.length;r++){var l=t.floor.cons[r],h=l.properties.name;l.properties={buildid:t.id,floorname:o,name:h,active:e},this._source.data.features.push(l)}for(r=0;r<t.floor.pubs.length;r++){var p=t.floor.pubs[r],c=p.properties.name;p.properties={buildid:t.id,floorname:c,name:h,active:e},this._source.data.features.push(p)}e&&this._events.triggerEvt("changed",{buildid:t.id,floorname:o})},MapPlatForm.Base.MapRoomControl.prototype._addControl=function(t){var e=map.getContainer(),i=document.createElement("div");i.id="NPGIS_Room_Control",i.className="npgis-indoormap-floorbar-control",i.title=t.building.properties.name_cn,this._floorControlStyle&&this._floorControlStyle.bottom&&(i.style.bottom=this._floorControlStyle.bottom),this._floorControlStyle&&this._floorControlStyle.right&&(i.style.right=this._floorControlStyle.right);var o=document.createElement("div");o.className="panel-box";var r=document.createElement("div");r.className="select-dock";var a=document.createElement("div");a.className="floor-list-box";var s=document.createElement("ul");s.className="floor-list";for(var n=0,l=2,h=t.building.properties.floor_nonas,p=t.id,c=this,u=h.length-1;0<=u;u--){h[u]==t.floor.floor.properties.namecode&&(n=u);var m=document.createElement("li");m.className="floor-list-item",m.data=u;var y=document.createElement("div");y.innerText=h[u],y.className="floor-btn floor-nonas",m.appendChild(y),m.addEventListener("click",function(t){5<h.length?r.style.top=28*(l-t.currentTarget.data+5)+6+"px":r.style.top=28*(h.length-1-t.currentTarget.data)+"px";var e=t.currentTarget.innerText;e=(e=e.replace(/\r\n/g,"")).replace(/\n/g,""),c._changeFloor(p,e)}),s.appendChild(m)}if(a.appendChild(s),5<h.length){var d=document.createElement("div");d.className="floor-btn floor-nav floor-plus",d.addEventListener("click",function(){l<h.length-5&&(l++,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")});var _=document.createElement("div");_.className="floor-btn floor-nav floor-minus",_.addEventListener("click",function(){0<l&&(l--,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")}),s.style.top=6-28*(h.length-1-n-2)+"px",r.style.top="90px",o.appendChild(d),o.appendChild(r),o.appendChild(a),o.appendChild(_),i.addEventListener("mousewheel",function(t){t.deltaY<0?l<h.length-5&&(l++,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px"):0<l&&(l--,s.style.top=6-28*(h.length-1-n-l)+"px",r.style.top=28*(1+l)+6+"px")},!1)}else r.style.top=28*(h.length-1-n)+"px",o.appendChild(r),o.appendChild(a);i.appendChild(o),e.appendChild(i)},MapPlatForm.Base.MapRoomControl.prototype._removeControl=function(){var t=document.getElementById("NPGIS_Room_Control");t&&t.remove()},MapPlatForm.Base.MapRoomControl.prototype.destroy=function(){this._map.removeEventListener("moveend",this._updateFloors,this),this._map._obj.removeLayer(this._floorRegionLayer.id),this._map._obj.removeLayer(this._normalFloorLineLayer.id),this._map._obj.removeLayer(this._activeFloorLineLayer.id),this._map._obj.removeLayer(this._roomLayer.id),this._map._obj.removeLayer(this._labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._removeControl(),this._mapService=null,this._events=null},MapPlatForm.Base.MapRoomControl.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},MapPlatForm.Base.MapRoomControl.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},MapPlatForm.Base.MapGeoRegion=function(t,e){this.CLASS_NAME="MapGeoRegion",this._layerID=NPMap.Utils.BaseUtils.uuid(),this._map=t,this._polygons={},this._lines={},this.activeFeature=null,this.activePolygon=null,this.activeLine=null,this.opts=e||{},this.mouseOver=this.opts.mouseOver?this.opts.mouseOver:null,this.mouseOut=this.opts.mouseOut?this.opts.mouseOut:null,this.click=this.opts.click?this.opts.click:null,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._polygonLayer={id:this._layerID+"_polygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]},filter:["==","$type","Polygon"]},this._lineLayer={id:this._layerID+"_line",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]},this._activePolygonLayer={id:this._layerID+"_activePolygon",type:"fill",source:this._layerID+"_source",paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]},filter:["==","$type","Polygon"]},this._activeLineLayer={id:this._layerID+"_activeLine",type:"line",source:this._layerID+"_source",layout:{"line-join":"round","line-cap":"round"},paint:{"line-color":["get","color"],"line-width":["get","width"],"line-opacity":["get","opacity"]},filter:["==","$type","LineString"]},this._labelLayer={id:this._layerID+"_symbol",type:"symbol",source:this._layerID+"_source",layout:{"text-field":"{title}","text-font":["Microsoft YaHei"],"text-size":["get","text-size"],"text-offset":["get","text-offset"],"text-anchor":"center"},paint:{"text-color":["get","text-color"],"text-opacity":["get","text-opacity"]},filter:["==","$type","Point"]}},MapPlatForm.Base.MapGeoRegion.prototype._getFeature=function(t){for(var e=this._map._obj.style._order.length-1;0<=e;e--)if(-1<this._map._obj.style._order[e].indexOf(this._layerID)){var i=this._map._obj.style._order[e];if("none"==this._map._obj.getLayoutProperty(i,"visibility"))continue;var o=this._map._obj.queryRenderedFeatures(t.point,{layers:[i]});if(o&&0<o.length)return[o[0]]}return[]},MapPlatForm.Base.MapGeoRegion.prototype._mouseout=function(){this._map._obj.getCanvasContainer().style.cursor="",this.mouseOut&&this.activePolygon&&this.mouseOut(this.activePolygon),this.activeFeature=null,this.activeLine=null,this.activePolygon=null},MapPlatForm.Base.MapGeoRegion.prototype._initInteractive=function(t){var i=this;!this._mousemoveFun&&(this._mousemoveFun=function(t){var e=i._getFeature(t);0!=e.length?i.activeFeature&&e[0].properties.id==i.activeFeature.properties.id||(i._mouseout(),e&&0<e.length&&(i._map._obj.getCanvasContainer().style.cursor="pointer",i.activeFeature=e[0],i.activePolygon=i._polygons[e[0].properties.id],i.activeLine=i._lines[e[0].properties.id+"line"],i.mouseOver&&i.activePolygon&&(i.mouseOver(i.activePolygon),i._map._obj.setFilter(i._activePolygonLayer.id,["==","id",i.activePolygon.id]),i._map._obj.setFilter(i._activeLineLayer.id,["==","id",i.activeLine.properties.id])))):i._mouseout()}),!this.clickFun&&(this.clickFun=function(){i.activePolygon&&i.click(i.activePolygon)}),t?(this._map._obj.on("mousemove",this._mousemoveFun),this._map._obj.on("click",this.clickFun)):(this._map._obj.off("mousemove",this._mousemoveFun),this._map._obj.off("click",this.clickFun))},MapPlatForm.Base.MapGeoRegion.prototype.addGeoRegions=function(t,e){var i=(e=e||{}).fontSize?e.fontSize:12,o=e.fontColor?e.fontColor:"#000000",r=e.fontOpacity?e.fontOpacity:1,a=e.fontOffset?e.fontOffset:[0,0],s=!0;if(t&&0<t.length){for(var n=0;n<t.length;n++){t[n]&&"NPMap.Geometry.Polygon"!=t[n].CLASS_NAME&&(s=!1);var l=s?t[n]:GeoJSON.read(t[n].geometry);l._fillColor=s||void 0===t[n].properties.fillColor?l._fillColor:t[n].properties.fillColor,l._fillOpacity=s||void 0===t[n].properties.fillOpacity?l._fillOpacity:t[n].properties.fillOpacity,l._weight=s||void 0===t[n].properties.weight?l._weight:t[n].properties.weight,l._color=s||void 0===t[n].properties.color?l._color:t[n].properties.color,l._opacity=s||void 0===t[n].properties.opacity?l._opacity:t[n].properties.opacity;var h={type:"Feature",geometry:{type:(this._polygons[l.id]=l).polygonType,coordinates:l._getCoordinates(this._map)},properties:{id:l.id,fillColor:l._fillColor,fillOpacity:l._fillOpacity}};this._source.data.features.push(h);for(var p=0;p<l._getCoordinates(this._map).length;p++){var c={type:"Feature",geometry:{type:"LineString",coordinates:l._getCoordinates(this._map)[p][0]},properties:{id:l.id+"line",color:l._color,width:l._weight,opacity:l._opacity,join:"round"}};this._lines[l.id+"line"]=c,this._source.data.features.push(c)}if(!s||void 0!==t[n].properties.name){var u={type:"Feature",geometry:{type:"Point",coordinates:[l.getCenter().lon,l.getCenter().lat]},properties:{id:l.id,title:t[n].properties.name,"text-offset":a,"text-opacity":r,"text-size":i,"text-color":o}};this._source.data.features.push(u)}}this._map._obj.addSource(this._layerID+"_source",this._source),this._map._obj.addLayer(this._polygonLayer),this._map._obj.addLayer(this._lineLayer),this._map._obj.addLayer(this._activePolygonLayer),this._map._obj.addLayer(this._activeLineLayer),s&&void 0===t[n].properties.name||this._map._obj.addLayer(this._labelLayer)}this._initInteractive()},MapPlatForm.Base.MapGeoRegion.prototype.setStyle=function(t){t&&t.fillColor&&this._map._obj.setPaintProperty(this._layerID+"_activePolygon","fill-color",t.fillColor),t&&t.fillOpacity&&this._map._obj.setPaintProperty(this._layerID+"_activePolygon","fill-opacity",t.fillOpacity),t&&t.color&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-color",t.color),t&&t.width&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-width",t.width),t&&t.opacity&&this._map._obj.setPaintProperty(this._layerID+"_activeLine","line-opacity",t.opacity)},MapPlatForm.Base.MapGeoRegion.prototype.destory=function(){this._map&&(this._initInteractive(!0),this._map._obj.addLayer(this._polygonLayer.id),this._map._obj.addLayer(this._lineLayer.id),this._map._obj.addLayer(this._activePolygonLayer.id),this._map._obj.addLayer(this._activeLineLayer.id),this._labelLayer&&this._map._obj.addLayer(this._labelLayer.id),this._map._obj.removeSource(this._layerID+"_source"),this._map=null),this.activeFeature=null,this.activePolygon=null,this.activeLine=null,this._polygons={},this._lines={}},MapPlatForm.Base.MapGeoRegion.prototype.refresh=function(){this._map&&this._map._obj.getSource(this._layerID+"_source").setData(this._source.data)},function(){var t=MapPlatForm.Base.FlyManager=function(t){this._map=t,this.stops=[],this.currentIndex=0,this.status=!1,this.events=new NPMap.Events(this,["preStop","afterStop"])};t.prototype.addStops=function(t){this.stops=t},t.prototype.play=function(t){this.status||(this.currentIndex=0,this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this.status=!0,this.isRepeat=t,this._map.disableMapOperation(),this._play())},t.prototype._play=function(){var t=this;this.status&&this._map.setCenter(this.stops[t.currentIndex].position,this.stops[t.currentIndex].zoom,{bearing:this.stops[t.currentIndex].bearing,pitch:this.stops[t.currentIndex].pitch,speed:this.stops[t.currentIndex].speed,curve:this.stops[t.currentIndex].curve,callback:function(){t.status&&(t.events.triggerEvt("afterStop",{stop:t.stops[t.currentIndex]}),setTimeout(function(){t.currentIndex++,t.status&&(t.currentIndex<t.stops.length?(t.events.triggerEvt("preStop",{stop:t.stops[t.currentIndex]}),t._play()):t.isRepeat?(t.currentIndex=0,t.events.triggerEvt("preStop",{stop:t.stops[t.currentIndex]}),t._play()):(t.status=!1,t._map.enableMapOperation()))},t.stops[t.currentIndex].waitTime))},isOnlyFly:!0})},t.prototype.stop=function(){this._map._obj.stop(),this._map.enableMapOperation(),this.status=!1,this.currentIndex=0},t.prototype.rePlay=function(){this._map._obj.stop(),this.currentIndex=0,this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this._play()},t.prototype.continus=function(){this.events.triggerEvt("preStop",{stop:this.stops[this.currentIndex]}),this._play()},t.prototype.pause=function(){this._map._obj.stop(),this.status=!1},t.prototype.addEventListener=function(t,e,i){this.events.register(t,e)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)}}(),MapPlatForm.Base.Stop=function(t,e){this.position=t,this.bearing=e&&e.bearing?e.bearing:0,this.pitch=e&&e.pitch?e.pitch:0,this.zoom=e&&e.zoom?e.zoom:14,this.waitTime=e&&e.waitTime?e.waitTime:0,this.speed=e&&e.speed?e.speed:1.2,this.curve=e&&e.curve?e.curve:1.42,this.name=e&&e.name?e.name:"未知站点"},MapPlatForm.Base.Stop.prototype.setData=function(t){this._data=t},MapPlatForm.Base.Stop.prototype.getData=function(){return this._data},function(){var r=function(t,e,i,o,r,a){this.id=NPMap.Utils.BaseUtils.uuid(),this.maxR=e||1e3,this.minR=i||0,this.point=t,this.defaultR=o,this._legend=r||[0,"rgba(0,255,0,0)",.5,"rgba(0,255,0,0.8)",1,"rgba(255,0,0,0.8)"],this._layer={id:this.id,type:"heatmap",source:a,minzoom:0,maxzoom:22,paint:{"heatmap-weight":["interpolate",["linear"],["get","value"],0,0,1,1],"heatmap-intensity":1,"heatmap-color":["interpolate",["linear"],["heatmap-density"]],"heatmap-radius":100,"heatmap-opacity":.8},filter:["==","id",this.id]};for(var s=0;s<this._legend.length;s++)this._layer.paint["heatmap-color"].push(this._legend[s]);this.addMap=function(t){(this._map=t)._obj.addLayer(this._layer)},this._getPixelDistance=function(t,e){var i=this._map._obj.unproject({x:this._map._obj.transform.width,y:this._map._obj.transform.height/2}),o=this._map._obj.getCenter();return e/this._map.getDistance({lon:i.lng,lat:i.lat},{lon:o.lng,lat:o.lat})*this._map._obj.transform.width/2},this.removeMap=function(){this._map&&this._map.getLayer(this._layer.id)&&this._map._obj.removeLayer(this._layer.id)},this.changeRadius=function(t){this.defaultR=t;var e=this._getPixelDistance(this.point,t);this._layer.paint["heatmap-radius"]=e,this._map&&this._map._obj.getLayer(this._layer.id)&&(this.defaultR==this.minR?(this._map._obj.removeLayer(this._layer.id),this._map._obj.addLayer(this._layer)):(this._map._obj.setPaintProperty(this._layer.id,"heatmap-opacity",1-this.defaultR/this.maxR),this._map._obj.setPaintProperty(this._layer.id,"heatmap-radius",e)))},this.updataRadius=function(){var t=this._map._obj.getCenter(),e=this._getPixelDistance({lon:t.lng,lat:t.lat},this.defaultR);this._layer.paint["heatmap-radius"]=e,this._map&&this._map._obj.getLayer(this._layer.id)&&this._map._obj.setPaintProperty(this._layer.id,"heatmap-radius",e)}},t=MapPlatForm.Base.AnimationCircle=function(t){this._map=t,this.id=NPMap.Utils.BaseUtils.uuid(),this._visible=!0,this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._circles=[]};t.prototype._eventsFun=function(t){if(!this._callBack){var e=this;this._callBack=function(){for(var t=0;t<e._circles.length;t++)e._circles[t].updataRadius()}}t?(this._map._obj.on("zoom",this._callBack),this._map._obj.on("zoomend",this._callBack)):(this._map._obj.off("zoom",this._callBack),this._map._obj.off("zoomend",this._callBack))},t.prototype.addCircles=function(t){if(t&&0<t.length){for(var e=0;e<t.length;e++){var i=Math.floor(Math.random()*(t[e].maxR-t[e].minR)+t[e].minR),o=new r(t[e].point,t[e].maxR,t[e].minR,i,t[e].legend,this.id);this._circles.push(o)}this._setData(this._circles);for(e=0;e<this._circles.length;e++)this._circles[e].addMap(this._map),this._circles[e].updataRadius()}},t.prototype._setData=function(t){for(var e=0,i=t.length;e<i;e++){var o=this._map?NPMap.T.setPoint(this._map,t[e].point):t[e].point;this._source.data.features.push({type:"Feature",geometry:{type:"Point",coordinates:[o.lon,o.lat]},properties:{value:1,id:t[e].id}})}this._map._obj.getSource(this.id)?this._map._obj.getSource(this.id).setData(this._source.data):this._map._obj.addSource(this.id,this._source)},t.prototype.start=function(){var i=this;requestAnimationFrame(function(){for(var t=0;t<i._circles.length;t++){var e=i._circles[t].defaultR;(e+=20)>i._circles[t].maxR&&(e=i._circles[t].minR),i._circles[t].changeRadius(e)}i.start()})}}(),function(){var t=MapPlatForm.Base.MapAnimationCircle=function(t,e){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=e&&e.minZoom?e.minZoom:0,this._maxZoom=e&&e.maxZoom?e.maxZoom:22,this._layer={id:this._layerID,type:"fill",source:this._layerID,minzoom:this._minZoom,maxzoom:this._maxZoom,paint:{"fill-color":["get","fillColor"],"fill-opacity":["get","fillOpacity"]}},this._source={type:"geojson",data:{type:"FeatureCollection",features:[]}},this._map._obj.addSource(this._layerID,this._source),this._map._obj.addLayer(this._layer)};t.prototype.addCircles=function(t){if(t&&0<t.length){this._circles=[];for(var e=0;e<t.length;e++)if(t[e]&&t[e].point){var i=t[e].maxR?t[e].maxR:2e3,o=Math.floor(Math.random()*(i-0)),r={point:t[e].point,maxR:i,defaultR:o,color:t[e].color?t[e].color:"#FFFFFF"};this._circles.push(r)}this._setData(this._circles)}},t.prototype._getCoords=function(t,e){for(var i=this._map?NPMap.T.setPoint(this._map,t):t,o=NPMap.T.helper.webMoctorJW2PM(i.lon,i.lat),r=NPMap.Utils.MapUtil.createRegularPolygon(o,e,60),a=[],s=[],n=0;n<r.length;n++){var l=NPMap.T.helper.inverseMercator(r[n].lon,r[n].lat);s.push([l.lon,l.lat])}return a.push(s),a},t.prototype._setData=function(t){for(var e=0,i=t.length;e<i;e++)this._source.data.features.push({type:"Feature",geometry:{type:"Polygon",coordinates:this._getCoords(t[e].point,t[e].defaultR)},properties:{fillColor:t[e].color,fillOpacity:1-t[e].defaultR/t[e].maxR,defaultR:t[e].defaultR,maxR:t[e].maxR,center:t[e].point}});this._map._obj.getSource(this._layerID)?this._map._obj.getSource(this._layerID).setData(this._source.data):this._map._obj.addSource(this._layerID,this._source)},t.prototype.start=function(){this._remove=!1,this._start()},t.prototype._start=function(){var r=this;requestAnimationFrame(function(){if(!r._remove){for(var t=performance.now()%5e3/5e3,e=0;e<r._source.data.features.length;e++){var i=r._source.data.features[e].properties.maxR,o=i/2*.9*t+i/2*.1;r._source.data.features[e].properties.defaultR=o,r._source.data.features[e].properties.fillOpacity=.3*(1-t),r._source.data.features[e].geometry.coordinates=r._getCoords(r._source.data.features[e].properties.center,o)}r._map._obj.getSource(r._layerID).setData(r._source.data),r._start()}})},t.prototype.destory=function(){this._remove=!0,this._map._obj.getLayer(this._layerID)&&this._map._obj.removeLayer(this._layerID),this._map._obj.getSource(this._layerID)&&this._map._obj.removeSource(this._layerID),this._map=null}}(),function(){var g=function(t,e,i){this.map=t,this.tb=e,this.id=i.id,this.centerPoint=i.centerPoint,this.height=i.height,this.position=i.position;var o=i.points;this.name=i&&i.name?i.name:"",this.isBloom=null!=i.isBloom&&i.isBloom,this.floorNum=i.floorNum,this.buildBufferGeometry=new THREE.BufferGeometry,this.material=i.material,this.verticesData={},this.verticesData.repeat=null!=i.repeat&&i.repeat,this.verticesData.vecColor=null!=this.material.side.vecColor&&this.material.side.vecColor,this.verticesData.topColor=this.material.side.topColor?this.material.side.topColor:"white",this.verticesData.bottomColor=this.material.side.bottomColor?this.material.side.bottomColor:"white",this.vertices=[],this.normals=[],this.uvs=[],this.colors=[],this.verticesData.vertices=[],this.verticesData.normals=[],this.verticesData.uvs=[],this.verticesData.colors=[],this.verticesData.topvertices=[],this.verticesData.topnormals=[],this.verticesData.topuvs=[],this.verticesData.topcolors=[];for(var r=0,a=[],s=e.utils.projectToWorld([this.position.lon,this.position.lat,0]),n=0;n<o.length-1;n++){var l=NPMap.T.setPoint(t,new NPMap.Geometry.Point(o[n][0],o[n][1])),h=e.utils.projectToWorld([l.lon,l.lat,this.height]),p=[h.x-s.x,h.y-s.y,0];0==r&&(r=h.z),a.push(p)}!function(t,e,i,o){for(var r=t[0],a=[new THREE.Vector2(r[0],r[1])],s=r[0],n=r[0],l=r[0],h=r[0],p=1;p<t.length;p++)t[p][0]>n&&(n=t[p][0]),t[p][0]<s&&(s=t[p][0]),t[p][1]>h&&(h=t[p][1]),t[p][1]<l&&(l=t[p][1]),a.push(new THREE.Vector2(t[p][0],t[p][1]));var c=n-s,u=h-l,m=[],y=[];o.vecColor&&(m=NPMap.Utils.MapUtil.colorRgb(o.topColor),y=NPMap.Utils.MapUtil.colorRgb(o.bottomColor));var d,_,f=THREE.ShapeUtils.triangulateShape(a,[]);for(p=0;p<f.length;p++){var g=f[p];o.topvertices.push(a[g[0]].x,a[g[0]].y,e),o.topvertices.push(a[g[1]].x,a[g[1]].y,e),o.topvertices.push(a[g[2]].x,a[g[2]].y,e),o.topnormals.push(0,0,1),o.topnormals.push(0,0,1),o.topnormals.push(0,0,1),o.topuvs.push((a[g[0]].x-s)/c,(a[g[0]].y-l)/u),o.topuvs.push((a[g[1]].x-s)/c,(a[g[1]].y-l)/u),o.topuvs.push((a[g[2]].x-s)/c,(a[g[2]].y-l)/u),o.vecColor&&(o.topcolors.push(m[0]/256,m[1]/256,m[2]/256),o.topcolors.push(m[0]/256,m[1]/256,m[2]/256),o.topcolors.push(m[0]/256,m[1]/256,m[2]/256))}for(p=0;p<t.length;p++){var v=p+1;v==t.length&&(v=0);var M=(d=t[v][0]-t[p][0],{x:(_=t[v][1]-t[p][1])/Math.sqrt(d*d+_*_),y:-d/Math.sqrt(d*d+_*_)});o.vertices.push(t[p][0],t[p][1],t[p][2]+e),o.vertices.push(t[v][0],t[v][1],t[v][2]),o.vertices.push(t[p][0],t[p][1],t[p][2]),o.normals.push(M.x,M.y,0),o.normals.push(M.x,M.y,0),o.normals.push(M.x,M.y,0),o.vecColor&&(o.colors.push(m[0]/256,m[1]/256,m[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256));var P=Math.round(i/5);P<1&&(P=1),o.repeat?o.uvs.push(P/5,1):o.uvs.push(1,1),o.uvs.push(0,0),o.uvs.push(0,1),o.vertices.push(t[p][0],t[p][1],t[p][2]+e),o.vertices.push(t[v][0],t[v][1],t[v][2]+e),o.vertices.push(t[v][0],t[v][1],t[v][2]),o.normals.push(M.x,M.y,0),o.normals.push(M.x,M.y,0),o.normals.push(M.x,M.y,0),o.vecColor&&(o.colors.push(m[0]/256,m[1]/256,m[2]/256),o.colors.push(m[0]/256,m[1]/256,m[2]/256),o.colors.push(y[0]/256,y[1]/256,y[2]/256)),o.repeat?(o.uvs.push(P/5,1),o.uvs.push(P/5,0)):(o.uvs.push(1,1),o.uvs.push(1,0)),o.uvs.push(0,0)}}(a,r,this.floorNum,this.verticesData),this.buildBufferGeometry.addGroup(this.vertices.length/3,this.verticesData.topvertices.length/3,0);for(var c=0;c<this.verticesData.topvertices.length;c++)this.vertices.push(this.verticesData.topvertices[c]);for(c=0;c<this.verticesData.topnormals.length;c++)this.normals.push(this.verticesData.topnormals[c]);for(c=0;c<this.verticesData.topuvs.length;c++)this.uvs.push(this.verticesData.topuvs[c]);for(c=0;c<this.verticesData.topcolors.length;c++)this.colors.push(this.verticesData.topcolors[c]);this.buildBufferGeometry.addGroup(this.vertices.length/3,this.verticesData.vertices.length/3,1);for(c=0;c<this.verticesData.vertices.length;c++)this.vertices.push(this.verticesData.vertices[c]);for(c=0;c<this.verticesData.normals.length;c++)this.normals.push(this.verticesData.normals[c]);for(c=0;c<this.verticesData.uvs.length;c++)this.uvs.push(this.verticesData.uvs[c]);for(c=0;c<this.verticesData.colors.length;c++)this.colors.push(this.verticesData.colors[c]);var u,m,y,d=new Float32Array(this.vertices.length);(d.set(this.vertices),110<=parseInt(THREE.REVISION))?(this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(d,3)),(u=new Float32Array(this.normals.length)).set(this.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(u,3)),(m=new Float32Array(this.uvs.length)).set(this.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(m,2)),(y=new Float32Array(this.colors.length)).set(this.colors),this.buildBufferGeometry.setAttribute("color",new THREE.BufferAttribute(y,3))):(this.buildBufferGeometry.setAttribute("position",new THREE.BufferAttribute(d,3)),(u=new Float32Array(this.normals.length)).set(this.normals),this.buildBufferGeometry.setAttribute("normal",new THREE.BufferAttribute(u,3)),(m=new Float32Array(this.uvs.length)).set(this.uvs),this.buildBufferGeometry.setAttribute("uv",new THREE.BufferAttribute(m,2)),(y=new Float32Array(this.colors.length)).set(this.colors),this.buildBufferGeometry.setAttribute("color",new THREE.BufferAttribute(y,3)));this.mesh=new THREE.Mesh(this.buildBufferGeometry,[this.material.top,this.material.side]),this.mesh.name="GeoBuilding3D-"+i.id,this.mesh.eventID=i.id},t=MapPlatForm.Base.MapGeoBuilding3D=function(t,e,i){this._map=t,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.textureImageUrl=i&&i.textureImageUrl?i.textureImageUrl:void 0,this.buildTopColor=i&&i.buildTopColor?i.buildTopColor:"rgb(53,86,160)",this.buildSideColor=i&&i.buildSideColor?i.buildSideColor:["rgb(53,86,160)"],this.transparent=!(!i||null==i.transparent)&&i.transparent,this.repeat=!(!i||null==i.repeat)&&i.repeat,this.opacity=i&&i.opacity?i.opacity:1,this.buildMaterialStyle={textureImageUrl:this.textureImageUrl,buildTopColor:this.buildTopColor,buildSideColor:this.buildSideColor,transparent:this.transparent,repeat:this.repeat,opacity:this.opacity},this.edgesLineColor=i&&i.edgesLineColor?i.edgesLineColor:"yellow",this.tb=e,this.timer=null;var r=this;this.animationSpeed=i&&i.animationSpeed?i.animationSpeed:.15,this.beforeLayerID=i&&i.beforeLayerID?i.beforeLayerID:"city_normal_building_id",this.buildingsArrayGeoJson=[],this._geoBuilding3Ds=[],this._setBuildMaterial(this.buildMaterialStyle),r._map._obj.on("click",function(t){var e=function(t){for(var e=r.tb.queryRenderedFeatures(t),i=0;i<e.length;i++)if(-1!=e[i].object.name.indexOf("GeoBuilding3D"))return e[i];return null}(t.point);if(e){var i=e.object.eventID,o=r._geoBuilding3Ds[i];r._events.triggerEvt("click",{build:o})}})};t.prototype._setBuildMaterial=function(t){t.textureImageUrl?(this.texture=(new THREE.TextureLoader).load(t.textureImageUrl),this.texture.wrapS=this.texture.wrapT=THREE.RepeatWrapping,this.texture.repeat.set(t.repeat?5:1,1),this.buildSideMaterial=new THREE.MeshStandardMaterial({map:this.texture,transparent:t.transparent,opacity:t.opacity}),this.buildSideMaterial.repeat=t.repeat,this.buildTopMaterial=new THREE.MeshStandardMaterial({color:t.buildTopColor,transparent:t.transparent,opacity:t.opacity})):1<t.buildSideColor.length?(this.buildSideMaterial=new THREE.MeshStandardMaterial({vertexColors:THREE.VertexColors,transparent:t.transparent,opacity:t.opacity}),this.buildSideMaterial.topColor=t.buildSideColor[0],this.buildSideMaterial.bottomColor=t.buildSideColor[1],this.buildSideMaterial.vecColor=!0,this.buildTopMaterial=new THREE.MeshStandardMaterial({vertexColors:THREE.VertexColors,transparent:t.transparent,opacity:t.opacity})):(this.buildSideMaterial=new THREE.MeshStandardMaterial({color:t.buildSideColor[0],transparent:t.transparent,opacity:t.opacity}),this.buildTopMaterial=new THREE.MeshStandardMaterial({color:t.buildTopColor,transparent:t.transparent,opacity:t.opacity})),this.buildMaterials={},this.buildMaterials.top=this.buildTopMaterial,this.buildMaterials.side=this.buildSideMaterial},t.prototype.addBuildings=function(t){function _(t){if(!(t.length<2)){for(var e=0,i=0,o=0,r=t[1],a=2;a<t.length;a++)p2=t[a],area=(t[0][0]*r[1]+r[0]*p2[1]+p2[0]*t[0][1]-r[0]*t[0][1]-p2[0]*r[1]-t[0][0]*p2[1])/2,o+=area,e+=(t[0][0]+r[0]+p2[0])*area,i+=(t[0][1]+r[1]+p2[1])*area,r=p2;var s=e/o/3,n=i/o/3;return new NPMap.Geometry.Point(s,n)}}this.buildingsArrayGeoJson=t,this._geoBuilding3Ds=[];var f=this;for(var e in f._geoBuilding3Ds=function(t,e,i){for(var o=[],r=0;r<t.length;r++){var a=t[r].points,s=t[r].floorNum,n=_(a),l="";t[r].name&&(l=t[r].name);var h,p=t[r].height,c=new NPMap.Geometry.Point(t[r].points[0][0],t[r].points[0][1]),u=t[r].id,m=!!t[r].isBloom&&t[r].isBloom,y=NPMap.T.setPoint(e,c),d={id:u,material:f.buildMaterials,floorNum:s,repeat:f.repeat,centerPoint:n,height:p,name:l,position:y,points:a,isBloom:m};(h=new g(f._map,i,d)).data=t[r].data?t[r].data:{},o[u]=h}return o}(f.buildingsArrayGeoJson,f._map,f.tb),f._geoBuilding3Ds)f._geoBuilding3Ds[e].isBloom?f._geoBuilding3Ds[e].mesh.layers.set(1):f._geoBuilding3Ds[e].mesh.layers.set(0),f.tb.addAtCoordinate(f._geoBuilding3Ds[e].mesh,[f._geoBuilding3Ds[e].position.lon,f._geoBuilding3Ds[e].position.lat])},t.prototype.changeBuildMaterial=function(t){for(var e in t.repeat=this.repeat,this._setBuildMaterial(t),this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.material=[this.buildTopMaterial,this.buildSideMaterial]},t.prototype.upBuildHeight=function(t){if(!(1<t||0==t))for(var e in this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.scale.set(1,1,t)},t.prototype.downBuildHeight=function(t){if(!(t<0||0==t))for(var e in this._geoBuilding3Ds)this._geoBuilding3Ds[e].mesh.scale.set(1,1,t)},t.prototype._animationDownBuild=function(t){var e=this;this.timer=setTimeout(function(){e.scale=e.scale-.05,e.downBuildHeight(e.scale),e.scale<=0?t&&t instanceof Function&&t():e._animationDownBuild(t)},50)},t.prototype.animationDownBuild=function(t){this.scale=1,clearTimeout(this.timer),this._animationDownBuild(t)},t.prototype._animationUpBuild=function(t){var e=this;this.timer=setTimeout(function(){e.scale=e.scale+.05,e.upBuildHeight(e.scale),1<=e.scale?t&&t instanceof Function&&t():e._animationUpBuild(t)},50)},t.prototype.animationUpBuild=function(t){this.scale=0,clearTimeout(this.timer),this._animationUpBuild(t)},t.prototype.getBuildings=function(){return this._geoBuilding3Ds},t.prototype.setBuildSelectStyle=function(t){this.edgesLineColor=t&&t.edgesLineColor?t.edgesLineColor:this.edgesLineColor,this.setBuildSelection(this._selections)},t.prototype.setBuildSelection=function(t){for(var e in this.deleteBuildSelection(),this._selections=t)if(t[e]&&-1!=t[e].mesh.name.indexOf("GeoBuilding3D")){var i=new THREE.EdgesGeometry(t[e].buildBufferGeometry,1),o=new THREE.LineBasicMaterial({color:this.edgesLineColor}),r=new THREE.LineSegments(i,o);r.name="cubeLine"+t[e].id,this.tb.addAtCoordinate(r,[t[e].position.lon,t[e].position.lat])}},t.prototype.deleteBuildSelection=function(){for(var t in this._selections)this.tb.world.getObjectByName("cubeLine"+this._selections[t].id)&&this.tb.remove(this.tb.world.getObjectByName("cubeLine"+this._selections[t].id));this._selections=[]},t.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},t.prototype.destory=function(){for(var t in this._geoBuilding3Ds){var e=this._geoBuilding3Ds[t].id;this.tb.remove(this._geoBuilding3Ds[e].mesh),this._geoBuilding3Ds[t].buildBufferGeometry=null,this._geoBuilding3Ds[t].mesh=null,this._geoBuilding3Ds[t]=null}this._geoBuilding3Ds=null}}(),function(){var P=function(t,e,i){this.position=t,this.points=e,this.material=i.material,this.centerPoint=i.centerPoint,this.floorIndoorHeight=i.floorIndoorHeight,this.floorIndoorLabelHeight=i.floorIndoorLabelHeight,this.meshName=i.meshName,this.shape=new THREE.Shape;for(var o=0;o<e.length;o++)0==o?this.shape.moveTo(e[o][0],e[o][1]):this.shape.lineTo(e[o][0],e[o][1]);this.geometry=new THREE.ExtrudeGeometry(this.shape,{depth:this.points[0][2],steps:parseInt(1),bevelEnabled:!1});var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)r=Math.max(r,this.geometry.vertices[this.geometry.faces[o].a].z);var a=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==r&&this.geometry.vertices[this.geometry.faces[o].b].z==r&&this.geometry.vertices[this.geometry.faces[o].c].z==r?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),a+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(a+=1);if(2<=a)for(o=a;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.floorIndoorHeight=this.floorIndoorHeight,this.mesh.floorIndoorLabelHeight=this.floorIndoorLabelHeight,this.mesh.name=this.meshName},l=function(t,e){this.material=e.material,this.geometry=e.geometry;for(var i=0,o=0;o<this.geometry.faceVertexUvs[0].length;o++)i=Math.max(i,this.geometry.vertices[this.geometry.faces[o].a].z);var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==i&&this.geometry.vertices[this.geometry.faces[o].b].z==i&&this.geometry.vertices[this.geometry.faces[o].c].z==i?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),r+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(r+=1);if(2<=r)for(o=r;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.name=t},v=function(t,e,i){this.meshName=i.meshName,this.shape=new THREE.Shape,this.material=i.material,this.realHeight=i.height,this.floorNum=i.floorNum,this.position=t,this.points=e,this.count=this.points.length;for(var o=0;o<e.length;o++)0==o?this.shape.moveTo(e[o][0],e[o][1]):this.shape.lineTo(e[o][0],e[o][1]);this.geometry=new THREE.ExtrudeGeometry(this.shape,{depth:this.points[0][2],steps:parseInt(this.floorNum),bevelEnabled:!1});var r=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)r=Math.max(r,this.geometry.vertices[this.geometry.faces[o].a].z);var a=0;for(o=0;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.vertices[this.geometry.faces[o].a].z==r&&this.geometry.vertices[this.geometry.faces[o].b].z==r&&this.geometry.vertices[this.geometry.faces[o].c].z==r?(this.geometry.faceVertexUvs[0][o][0].set(.5,.5),this.geometry.faceVertexUvs[0][o][1].set(.5,.5),this.geometry.faceVertexUvs[0][o][2].set(.5,.5),a+=1):0==this.geometry.vertices[this.geometry.faces[o].a].z&&0==this.geometry.vertices[this.geometry.faces[o].b].z&&0==this.geometry.vertices[this.geometry.faces[o].c].z&&(a+=1);if(2<=a)for(o=a;o<this.geometry.faceVertexUvs[0].length;o++)this.geometry.faceVertexUvs[0][o][0].set(0,1),this.geometry.faceVertexUvs[0][o][1].set(0,0),this.geometry.faceVertexUvs[0][o][2].set(1,1),o+=1,this.geometry.faceVertexUvs[0][o][0].set(0,0),this.geometry.faceVertexUvs[0][o][1].set(1,0),this.geometry.faceVertexUvs[0][o][2].set(1,1);this.mesh=new THREE.Mesh(this.geometry,[this.material[0],this.material[1]]),this.mesh.name=this.meshName},t=MapPlatForm.Base.MapGeoIndoor3D=function(t,e){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=e&&e.minZoom?e.minZoom:11,this._maxZoom=e&&e.maxZoom?e.maxZoom:22,this.textureImageUrl=e&&e.textureImageUrl?e.textureImageUrl:void 0,this.buildTopColor=e&&e.buildTopColor?e.buildTopColor:"rgb( 65,105,225)",this.buildSideColor=e&&e.buildSideColor?e.buildSideColor:"rgb(44,72,139)",this.buildInternalTopColor=e&&e.buildInternalTopColor?e.buildInternalTopColor:"rgb(51,69,92)",this.buildInternalSideColor=e&&e.buildInternalSideColor?e.buildInternalSideColor:"rgb(137,125,139)",this.opacity=e&&e.opacity?e.opacity:1,this.isTransparent=null==e.isTransparent||e.isTransparent,this._animation=null==e.animation||e.animation,this.isCollision=null==e.isCollision||e.isCollision,this.tb=null,this.timer=null;var i=this;this.notCollisionMeshs=[],this.notCollisionBounds=[],this.notCollisionIndexs=[],this._buildingsLayer={id:this._layerID,type:"custom",renderingMode:"3d",render:function(t,e){t.enable(t.DEPTH_TEST),i._map.getZoom()>=i._minZoom&&(i.tb.update(),i.isCollision&&i._map._obj.isMoving()&&i._updateMarker3D(i))}},this.buildTopMaterial=new THREE.MeshLambertMaterial({color:this.buildTopColor,transparent:this.isTransparent,opacity:this.opacity}),null==this.textureImageUrl?this.buildSideMaterial=new THREE.MeshLambertMaterial({color:this.buildSideColor,transparent:this.isTransparent,opacity:this.opacity}):(this.buildingtexture=(new THREE.TextureLoader).load(this.textureImageUrl),this.buildSideMaterial=new THREE.MeshLambertMaterial({map:this.buildingtexture,transparent:this.isTransparent,opacity:this.opacity,depthTest:!0,depthWrite:!0,wireframe:!1})),this.floorTopMaterial=new THREE.MeshLambertMaterial({color:this.buildTopColor,transparent:this.isTransparent,opacity:this.opacity}),null==this.textureImageUrl?this.floorSideMaterial=new THREE.MeshLambertMaterial({color:this.buildSideColor,transparent:this.isTransparent,opacity:this.opacity}):(this.buildingtexture=(new THREE.TextureLoader).load(this.textureImageUrl),this.floorSideMaterial=new THREE.MeshLambertMaterial({map:this.buildingtexture,transparent:this.isTransparent,opacity:this.opacity,depthTest:!0,depthWrite:!0,wireframe:!1})),this.buildInternalTopMaterial=new THREE.MeshLambertMaterial({color:this.buildInternalTopColor,transparent:this.isTransparent,opacity:this.opacity}),this.buildInternalSideMaterial=new THREE.MeshLambertMaterial({color:this.buildInternalSideColor,transparent:this.isTransparent,opacity:this.opacity})};t.prototype.addBuildings=function(t){this.buildingsArrayGeoJson=t,this._geoBuilding3Ds;var g=this;this._map&&this._map._obj.getLayer(this._layerID)&&(this._map._obj.removeLayer(this._layerID),this.tb=null);g=this;this._buildingsLayer.onAdd=function(t,e){g.tb=new Threebox(t,e,{defaultLights:!0}),g._geoBuilding3Ds=function(t,e,i){for(var o=[],r=0;r<t.length;r++){var a=t[r].building.geometry.coordinates[0],s=2e4*t[r].building.properties.height,n=t[r].building.properties.numberofFloor,l=new NPMap.Geometry.Point(a[0][0],a[0][1]),h=NPMap.T.getPoint(e,l);h=NPMap.T.setPoint(e,h);for(var p,c=i.utils.projectToWorld([h.lon,h.lat,s]),u=[[0,0,c.z]],m=1;m<a.length-1;m++){var y=NPMap.T.getPoint(e,new NPMap.Geometry.Point(a[m][0],a[m][1]));y=NPMap.T.setPoint(e,new NPMap.Geometry.Point(y.lon,y.lat));var d=i.utils.projectToWorld([y.lon,y.lat,s]),_=[d.x-c.x,d.y-c.y,d.z];u.push(_)}var f={meshName:"build-"+r,material:[g.buildTopMaterial,g.buildSideMaterial],floorNum:n,height:s};p=new v(h,u,f),o.push(p)}return o}(g.buildingsArrayGeoJson,g._map,g.tb);for(var i=0;i<g._geoBuilding3Ds.length;i++)g.tb.addAtCoordinate(g._geoBuilding3Ds[i].mesh,[g._geoBuilding3Ds[i].position.lon,g._geoBuilding3Ds[i].position.lat])},g._animation,this._map._obj.getLayer("city_normal_building_id")?this._map._obj.addLayer(this._buildingsLayer,"city_normal_building_id"):this._map._obj.addLayer(this._buildingsLayer)},t.prototype.expandFloors=function(t,e){this.tb.world.getObjectByName("build-"+t)&&this.tb.remove(this.tb.world.getObjectByName("build-"+t)),this._removeBuild_Floor(),this._removeBuild_Floor_Internal(),this._removeBuild_Floor_Sprite(),this.currentBuildIndex=t,this.currentfloorNumIndex=e,this.currentBuild=this._geoBuilding3Ds[this.currentBuildIndex],this.currentBuildHeight=this.currentBuild.points[0][2],this.realCurrentBuildHeight=this.currentBuild.realHeight,this.currentBuildFloor=this.currentBuild.floorNum;this.floorStartHeightArr=[],this.realFloorStartHeightArr=[],this.floorEndHeightArr=[],this.realFloorEndHeightArr=[],this.floorIndoorHeightArr=[],this.realFloorIndoorHeightArr=[],this.floorIndoorLabelHeightArr=[],this.realFloorIndoorLabelHeightArr=[],this.depthReal=this.realCurrentBuildHeight/this.currentBuildFloor,this.depth=this.tb.utils.projectToWorld([0,0,this.currentBuildHeight/this.currentBuildFloor]).z;var i={geometry:new THREE.ExtrudeGeometry(this.currentBuild.shape,{depth:this.currentBuildHeight/this.currentBuildFloor,steps:parseInt(1),bevelEnabled:!1}),material:[this.floorTopMaterial,this.floorSideMaterial]};this.floorMeshArr=[];for(var o=0;o<this.currentBuildFloor;o++){var r=new l("floor-"+o,i);this.floorStartHeightArr[o]=this.currentBuildHeight*o/this.currentBuildFloor,this.realFloorStartHeightArr[o]=this.realCurrentBuildHeight*o/this.currentBuildFloor,this.floorEndHeightArr[o]=this.currentBuildHeight*o/this.currentBuildFloor,this.realFloorEndHeightArr[o]=this.realCurrentBuildHeight*o/this.currentBuildFloor,this.floorIndoorHeightArr[o]=this.floorEndHeightArr[o],this.realFloorIndoorHeightArr[o]=this.realFloorEndHeightArr[o],this.floorIndoorLabelHeightArr[o]=this.floorEndHeightArr[o]+this.currentBuildHeight/this.currentBuildFloor,this.realFloorIndoorLabelHeightArr[o]=this.realFloorEndHeightArr[o]+this.realCurrentBuildHeight/this.currentBuildFloor,this.floorMeshArr.push(r.mesh)}for(o=1;o<this.currentfloorNumIndex+1;o++)this.tb.addAtCoordinate(this.floorMeshArr[o-1],[this.currentBuild.position.lon,this.currentBuild.position.lat]),this.floorMeshArr[o-1].translateZ(this.floorEndHeightArr[o-1]);this.CurrentInitFloorJson=[],this.CurrentInitSpriteJson=[];for(var a=0;a<this.buildingsArrayGeoJson[0].floor.shops.length;a++){var s=[],n=[];for(o=0;o<this.buildingsArrayGeoJson[0].floor.shops[a].length;o++)s.push(this.buildingsArrayGeoJson[0].floor.shops[a][o].geometry.coordinates[0]),n.push(this.buildingsArrayGeoJson[0].floor.shops[a][o].properties.name);this.CurrentInitFloorJson.push(s),this.CurrentInitSpriteJson.push(n)}this.isCollision=!0,this.floorInternalsArrAll=this._initFloorInternals(this.CurrentInitFloorJson,this._map,this.tb),this.spriteInternalsArrAll=this._initSpriteInternals();for(o=0;o<this.floorInternalsArrAll[this.currentfloorNumIndex].length;o++)this.tb.addAtCoordinate(this.floorInternalsArrAll[this.currentfloorNumIndex][o].mesh,[this.floorInternalsArrAll[this.currentfloorNumIndex][o].position.lon,this.floorInternalsArrAll[this.currentfloorNumIndex][o].position.lat]),this.floorInternalsArrAll[this.currentfloorNumIndex][o].mesh.translateZ(this.floorIndoorHeightArr[this.currentfloorNumIndex]);this._updateMarker3D(this),this._map._obj.triggerRepaint()},t.prototype.unexpandFloors=function(t){this._removeBuild_Floor_Internal(),this._removeBuild_Floor_Sprite(),this._removeBuild_Floor(),null!=t?null==this.tb.world.getObjectByName("build-"+t)&&this.tb.addAtCoordinate(this.currentBuild.mesh,[this.currentBuild.position.lon,this.currentBuild.position.lat]):null==this.tb.world.getObjectByName("build-"+this.currentBuildIndex)&&this.tb.addAtCoordinate(this.currentBuild.mesh,[this.currentBuild.position.lon,this.currentBuild.position.lat]),this._map._obj.triggerRepaint()},t.prototype._initFloorInternals=function(t,e,i){function o(t){for(var e,i,o,r=0,a=0,s=0,n=t[1],l=2;l<t.length;l++)p2=t[l],area=(e=t[0],i=n,o=p2,(e[0]*i[1]+i[0]*o[1]+o[0]*e[1]-i[0]*e[1]-o[0]*i[1]-e[0]*o[1])/2),s+=area,r+=(t[0][0]+n[0]+p2[0])*area,a+=(t[0][1]+n[1]+p2[1])*area,n=p2;return[r/s/3,a/s/3]}for(var r=[],a=0;a<t.length;a++){for(var s=[],n=t[a],l=null,h=0;h<n.length;h++){var p=new NPMap.Geometry.Point(n[h][0][0],n[h][0][1]),c=o(n[h]);l=NPMap.T.getPoint(e,new NPMap.Geometry.Point(c[0],c[1])),l=NPMap.T.setPoint(e,new NPMap.Geometry.Point(l.lon,l.lat));var u=NPMap.T.getPoint(e,p);u=NPMap.T.setPoint(e,u);for(var m=i.utils.projectToWorld([u.lon,u.lat,3]),y=[[0,0,m.z]],d=1;d<n[h].length;d++){var _=NPMap.T.getPoint(e,new NPMap.Geometry.Point(n[h][d][0],n[h][d][1]));_=NPMap.T.setPoint(e,new NPMap.Geometry.Point(_.lon,_.lat));var f=i.utils.projectToWorld([_.lon,_.lat,3]),g=[f.x-m.x,f.y-m.y,f.z];y.push(g)}var v,M={meshName:"floor"+a+"-"+h,centerPoint:l,material:[this.buildInternalTopMaterial,this.buildInternalSideMaterial],floorIndoorHeight:this.floorIndoorHeightArr[h],floorIndoorLabelHeight:this.floorIndoorLabelHeightArr[h]};v=new P(u,y,M),s.push(v)}r.push(s)}return r},t.prototype._initSpriteInternals=function(){for(var t=[],e=0;e<this.floorInternalsArrAll.length;e++){for(var i=[],o=0;o<this.floorInternalsArrAll[e].length;o++){var r=this._makeTextPoint(this.CurrentInitSpriteJson[e][o],{});r.name="sprite"+e+"-"+o,r.floorIndoorHeight=this.floorIndoorHeightArr[e],r.floorIndoorLabelHeight=this.floorIndoorLabelHeightArr[e],i.push(r)}t.push(i)}return t},t.prototype._makeTextSprite=function(t,e){void 0===e&&(e={});e.hasOwnProperty("fontface")&&e.fontface,e.hasOwnProperty("fontsize")&&e.fontsize;var i=e.hasOwnProperty("borderThickness")?e.borderThickness:4,o=e.hasOwnProperty("borderColor")?e.borderColor:{r:0,g:0,b:0,a:1},r=e.hasOwnProperty("backgroundColor")?e.backgroundColor:{r:255,g:255,b:255,a:.5},a=(h=document.createElement("canvas")).getContext("2d");a.font="22px bold";a.measureText(t).width;a.fillStyle="rgba("+r.r+","+r.g+","+r.b+","+r.a+")",a.strokeStyle="rgba("+o.r+","+o.g+","+o.b+","+o.a+")",a.lineWidth=i,a.fillText(t,150,40,100);var s=new THREE.Texture(h);s.needsUpdate=!0;var n=new THREE.SpriteMaterial({map:s}),l=new THREE.Sprite(n),h=document.createElement("canvas");return l.scale.set(.4,.2,1),l},t.prototype._makeTextPoint=function(t){var e=document.createElement("canvas");e.width=200,e.height=200,e.style.width="100px",e.style.height="100px";var i=e.getContext("2d");i.font='24px " 微软雅黑';var o=i.measureText(t).width;o<=200?(i.lineWidth=6,i.strokeStyle="white",i.strokeText(t,100-o/2,100,200),i.fillStyle="black",i.fillText(t,100-o/2,100,200)):200<o&&(i.lineWidth=6,i.strokeStyle="white",i.strokeText(t,0,100,200),i.fillStyle="black",i.fillText(t,0,100,200));var r=new THREE.Texture(e);r.needsUpdate=!0;(new THREE.TextureLoader).load("../../css/images/camera_active.png");var a=new THREE.PointsMaterial({size:100,sizeAttenuation:!1,map:r,alphaTest:.3,transparent:!0}),s=new THREE.BufferGeometry,n=[];return n.push(0,0,0),s.setAttribute("position",new THREE.Float32BufferAttribute(n,3)),new THREE.Points(s,a)},t.prototype._caluPOIRect=function(t,e,i){var o=t.lon,r=t.lat,a=this._map._obj.project(new mapboxgl.LngLat(o,r),e);return{x:a.x-50,y:a.y-14,w:100,h:14}},t.prototype._isPOIRect=function(t,e){var i=t.x,o=t.y,r=t.w,a=t.h,s=e.x,n=e.y,l=e.w,h=e.h;return!(s<=i&&s+l<=i)&&(!(i<=s&&i+r<=s)&&(!(n<=o&&n+h<=o)&&!(o<=n&&o+a<=n)))},t.prototype._removeBuild=function(t){this.tb.world.getObjectByName("build-"+t)&&this.tb.remove(this.tb.world.getObjectByName("build-"+t))},t.prototype._removeBuild_Floor=function(){if(null!=this.floorMeshArr)for(var t=0;t<this.floorMeshArr.length;t++)this.tb.world.getObjectByName("floor-"+t)&&this.tb.remove(this.floorMeshArr[t])},t.prototype._removeBuild_Floor_Sprite=function(){if(this.floorInternalsArrAll){this.isCollision=!1;for(var t=0;t<this.floorInternalsArrAll.length;t++)for(var e=0;e<this.floorInternalsArrAll[t].length;e++)this.tb.world.getObjectByName("sprite"+t+"-"+e)&&this.tb.remove(this.tb.world.getObjectByName("sprite"+t+"-"+e))}},t.prototype._removeBuild_Floor_Internal=function(){if(this.floorInternalsArrAll)for(var t=0;t<this.floorInternalsArrAll.length;t++)for(var e=0;e<this.floorInternalsArrAll[t].length;e++)this.tb.world.getObjectByName("floor"+t+"-"+e)&&this.tb.remove(this.tb.world.getObjectByName("floor"+t+"-"+e))},t.prototype._updateMarker3D=function(t){var r=t;if(r.spriteInternalsArrAll){for(var a=0;a<r.notCollisionMeshs.length;a++){var s=[],n=[],l=[];r.notCollisionMeshs[a].forEach(function(t){var e=r._caluPOIRect(t.centerPoint,r.realFloorIndoorLabelHeightArr[a]);for(var i in s){var o=s[i];if(r._isPOIRect(o,e))return}s.push(e),n.push(parseInt(t.mesh.name.split("-")[1])),l.push(t)}),r.notCollisionBounds[a]=s,r.notCollisionIndexs[a]=n,r.notCollisionMeshs[a]=l}if(0==r.notCollisionMeshs.length)for(a=0;a<r.floorInternalsArrAll.length;a++){s=[],n=[],l=[];r.floorInternalsArrAll[a].forEach(function(t){var e=r._caluPOIRect(t.centerPoint,r.realFloorIndoorLabelHeightArr[a]);for(var i in s){var o=s[i];if(r._isPOIRect(o,e))return}s.push(e),n.push(parseInt(t.mesh.name.split("-")[1])),l.push(t)}),r.notCollisionBounds.push(s),r.notCollisionIndexs.push(n),r.notCollisionMeshs.push(l)}for(a=0;a<r.floorInternalsArrAll.length;a++)r.floorInternalsArrAll[a].forEach(function(t){var e=r._caluPOIRect(t.centerPoint,r.realFloorIndoorLabelHeightArr[a]);for(var i in r.notCollisionBounds[a]){var o=r.notCollisionBounds[a][i];if(parseInt(r.notCollisionMeshs[a][i].mesh.name.split("-")[1])==parseInt(t.mesh.name.split("-")[1]))return;if(r._isPOIRect(o,e))return}r.notCollisionBounds[a].push(e),r.notCollisionMeshs[a].push(t),r.notCollisionIndexs[a].push(parseInt(t.mesh.name.split("-")[1]))});for(var e=0;e<r.floorInternalsArrAll[r.currentfloorNumIndex].length;e++)-1==r.notCollisionIndexs[r.currentfloorNumIndex].indexOf(e)&&r.tb.world.getObjectByName("sprite"+r.currentfloorNumIndex+"-"+e)&&r.tb.remove(r.tb.world.getObjectByName("sprite"+r.currentfloorNumIndex+"-"+e)),-1==r.notCollisionIndexs[r.currentfloorNumIndex].indexOf(e)||r.tb.world.getObjectByName("sprite"+r.currentfloorNumIndex+"-"+e)||(r.tb.addAtCoordinate(r.spriteInternalsArrAll[r.currentfloorNumIndex][e],[r.floorInternalsArrAll[r.currentfloorNumIndex][e].centerPoint.lon,r.floorInternalsArrAll[r.currentfloorNumIndex][e].centerPoint.lat]),r.spriteInternalsArrAll[r.currentfloorNumIndex][e].translateZ(r.floorIndoorLabelHeightArr[r.currentfloorNumIndex]))}},t.prototype._update=function(){this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),100)},t.prototype.destory=function(){if(this.timer&&clearTimeout(this.timer),this._map&&this._map._obj.getLayer(this._layerID)){this._map._obj.removeLayer(this._layerID);for(var t=0;t<this._geoBuilding3Ds.length;t++)this.tb.remove(this._geoBuilding3Ds[t].mesh),this._geoBuilding3Ds[t].geometry=null,this._geoBuilding3Ds[t].mesh=null,this._geoBuilding3Ds[t].shape=null;for(t=this._geoBuilding3Ds.length=0;t<this.floorMeshArr.length;t++)this.tb.remove(this.floorMeshArr[t]),this.floorMeshArr[t]=null;for(t=this.floorMeshArr.length=0;t<this.floorInternalsArrAll.length;t++)this.tb.remove(this.floorInternalsArrAll[t]),this.floorInternalsArrAll[t].mesh=null,this.floorInternalsArrAll[t].geometry=null,this.floorInternalsArrAll[t].shape=null,this.floorInternalsArrAll[t]=null;for(t=this.floorInternalsArrAll.length=0;t<this.spriteInternalsArrAll.length;t++)this.spriteInternalsArrAll[t]=null;this.spriteInternalsArrAll.length=0,this.tb=null}}}(),function(){var b=function(t,e,i,o){this.position=t,this.points=e,this.count=e.length,this.geometry=new THREE.Geometry,this.colors=[];for(var r=0;r<e.length;r++)this.geometry.vertices.push(e[r]),this.geometry.colors.push(new THREE.Color(o[0],o[1],o[2])),this.colors.push(o[0],o[1],o[2]);this.line=new THREE.Line(this.geometry,i)},t=MapPlatForm.Base.MapGeoWallLine3D=function(t,e,i){this._map=t,this._springLines=[],this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=i&&i.minZoom?i.minZoom:11,this._lineCount=i&&i.lineCount?i.lineCount:10,this._animation=i&&i.animation,this.tb=e,this.timer=null,this._moveCount=Math.floor(this._lineHeight/this._speed)};t.prototype.addLines=function(t){var P=new THREE.LineBasicMaterial({color:16777215,vertexColors:!0,linewidth:5});this._geoLine3Ds=[],this.lines=t;this._geoLine3Ds=function(t,e,i,o){for(var r=[],a=0;a<t.length;a++){for(var s={index:0,count:o,lines:[],colors:[]},n=t[a].getPath(),l=t[a].getData(),h=(l=l||{}).colors?l.colors:["#6ff3f3","#f7e513","#a4ec62","#77e8b4","#81d1e8","#c48eec","#c48eec"],p=0;p<o;p++){for(var c=l.height?l.height:20,u=NPMap.T.setPoint(e,n[0]),m=i.utils.projectToWorld([u.lon,u.lat,(p+1)*(c/o)]),y=[new THREE.Vector3(0,0,m.z)],d=1;d<n.length;d++){var _=NPMap.T.setPoint(e,n[d]),f=i.utils.projectToWorld([_.lon,_.lat,(p+1)*(c/o)]),g=new THREE.Vector3(f.x-m.x,f.y-m.y,f.z);y.push(g)}var v=NPMap.Utils.MapUtil.colorRgb(h[p%h.length]),M=new b(u,y,P,[v[0]/256,v[1]/256,v[2]/256]);l.isBloom&&M.line.layers.set(1),s.lines.push(M),s.colors.push(M.geometry.colors)}r.push(s)}return r}(this.lines,this._map,this.tb,this._lineCount);for(var e=0;e<this._geoLine3Ds.length;e++)for(var i=0;i<this._geoLine3Ds[e].lines.length;i++)this.tb.addAtCoordinate(this._geoLine3Ds[e].lines[i].line,[this._geoLine3Ds[e].lines[i].position.lon,this._geoLine3Ds[e].lines[i].position.lat]);this._animation&&this._update()},t.prototype._update=function(){for(var t=0;t<this._geoLine3Ds.length;t++){this._geoLine3Ds[t].index<0&&(this._geoLine3Ds[t].index=this._geoLine3Ds[t].count-1);for(var e=0;e<this._lineCount;e++){var i=this._geoLine3Ds[t].colors[(this._geoLine3Ds[t].index+e)%this._lineCount];if(this._geoLine3Ds[t].lines[e].line.geometry._bufferGeometry){i=this._geoLine3Ds[t].lines[(this._geoLine3Ds[t].index+e)%this._lineCount].colors;this._geoLine3Ds[t].lines[e].line.geometry._bufferGeometry.setAttribute("color",new THREE.Float32BufferAttribute(i,3))}else this._geoLine3Ds[t].lines[e].line.geometry.colors=i}this._geoLine3Ds[t].index--}this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),200)},t.prototype.destory=function(){this.timer&&clearTimeout(this.timer);for(var t=0;t<this._geoLine3Ds.length;t++)for(var e=0;e<this._geoLine3Ds[t].lines.length;e++)this.tb.remove(this._geoLine3Ds[t].lines[e].line);this._geoLine3Ds.length=0}}(),MapPlatForm.Base.ClusterManager=function(t,e){this.CLASS_NAME="ClusterManager",this.map=t,this._layers={},this.opts=e},MapPlatForm.Base.ClusterManager.prototype.addClusters=function(t){var e=this.opts.clusterImage;for(var i in t)if(this.map.getLayerByName(i))(a=this.map.getLayerByName(i)).useCluster&&a.addClusters(t[i]);else{for(var o=[],r=0;r<e.length;r++)o.push(r%2==0?i+e[r]:e[r]);this.opts.clusterImage=o;var a=new NPMap.Layers.OverlayLayer(i,!0,this.opts),s=new NPMap.Symbols.ClusterPoints(t[i]);this.map.addLayer(a),a.addOverlay(s),this._layers[i]=a}},MapPlatForm.Base.ClusterManager.prototype.addClusterPoints=function(t,e){this.map.getLayerByName(t)&&this.map.getLayerByName(t).addClusterPoints(e)},MapPlatForm.Base.ClusterManager.prototype.setMakrerTypeVisiable=function(t,e,i){this.map.getLayerByName(t)&&this.map.getLayerByName(t).setMakrerTypeVisiable(e,i)},MapPlatForm.Base.ClusterManager.prototype.hide=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].hide()},MapPlatForm.Base.ClusterManager.prototype.show=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].show()},MapPlatForm.Base.ClusterManager.prototype.refresh=function(){for(var t in this._layers)this._layers.hasOwnProperty(t)&&this._layers[t].refresh()},MapPlatForm.Base.ClusterManager.prototype.getVisibleClustersByExtent=function(t,e){return this._layers[t]&&this._layers[t].getVisible()?this._layers[t].containFeatures(e,function(t){return t.location._visible}):[]},MapPlatForm.Base.ClusterManager.prototype.getVisibleByKey=function(t){return!(!this._layers[t]||!this._layers[t].getVisible())},function(){var l=function(t,e,i){this._map=t,this._tb=e,this.id=i.id,this.el=i.element,this.marker=new mapboxgl.Marker(this.el),this.position=i.position,this.height=i&&i.height?i.height:0,this.baseLineHeight=i&&i.baseLineHeight?i.baseLineHeight:0,this.lineColor=i&&i.lineColor?i.lineColor:"white",this.lineEnableTexture=!(!i||null==i.lineEnableTexture)&&i.lineEnableTexture,this.lineTextureUrl=i&&i.lineTextureUrl?i.lineTextureUrl:"",this.lineTextureWidth=i&&i.lineTextureWidth?i.lineTextureWidth:2,this.isBloom=null!=i.isBloom&&i.isBloom;var o=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.baseLineHeight]),r=o.z,a=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.height]),s=a.z;if(this.lineEnableTexture&&""!=this.lineTextureUrl){var n=(new THREE.TextureLoader).load(this.lineTextureUrl),l=new THREE.MeshBasicMaterial({map:n,side:THREE.DoubleSide,blending:THREE.AdditiveBlending,transparent:!0}),h=new THREE.BufferGeometry,p=new Float32Array(36),c=.01*this.lineTextureWidth;p.set([-1*c,0,r,c,0,r,c,0,s,c,0,s,-1*c,0,s,-1*c,0,r,0,-1*c,r,0,c,r,0,c,s,0,c,s,0,-1*c,s,0,-1*c,r]),110<=parseInt(THREE.REVISION)?h.setAttribute("position",new THREE.BufferAttribute(p,3)):h.addAttribute("position",new THREE.BufferAttribute(p,3));var u=new Float32Array(36);u.set([0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0]),110<=parseInt(THREE.REVISION)?h.setAttribute("normal",new THREE.BufferAttribute(u,3)):h.addAttribute("normal",new THREE.BufferAttribute(u,3));var m=new Float32Array(24);m.set([0,0,1,0,1,1,1,1,0,1,0,0,0,0,1,0,1,1,1,1,0,1,0,0]),110<=parseInt(THREE.REVISION)?h.setAttribute("uv",new THREE.BufferAttribute(m,2)):h.addAttribute("uv",new THREE.BufferAttribute(m,2)),this.line=new THREE.Mesh(h,l),this.line.name="MapMarker3DLine"+this.id}else{l=new THREE.LineBasicMaterial({color:this.lineColor,linecap:"round",linejoin:"round"});var y=new THREE.Geometry,d=new THREE.Vector3(0,0,o.z),_=new THREE.Vector3(0,0,a.z);y.vertices.push(d,_),this.line=new THREE.Line(y,l),this.line.name="MapMarker3DLine"+this.id}},t=MapPlatForm.Base.MapMarker3DLayer=function(t,e,i){this._map=t,this._tb=e,this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.lineVisiable=!(!i||null==i.lineVisiable)&&i.lineVisiable,this.projectChange=!i||null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this.marker3DsDataArray=[]};t.prototype.addMarker3Ds=function(t){for(var n=this,e=0;e<t.length;e++)this.marker3DsDataArray.push(t[e]);this._mapMarker3Ds=[];n._mapMarker3Ds=function(t,e,i){for(var o=[],r=0;r<t.length;r++){var a={id:NPMap.Utils.BaseUtils.uuid(),element:t[r].dom,position:t[r].point,height:t[r].height,baseLineHeight:t[r].baseLineHeight,lineColor:t[r].lineColor,lineEnableTexture:t[r].lineEnableTexture,lineTextureUrl:t[r].lineTextureUrl,lineTextureWidth:t[r].lineTextureWidth,isBloom:null!=t[r].isBloom&&t[r].isBloom},s=new l(n._map,n._tb,a);s.data=t[r].data?t[r].data:{},o.push(s)}return o}(n.marker3DsDataArray,n._map,n._tb);for(e=0;e<n._mapMarker3Ds.length;e++){var i=null;i=n.projectChange?NPMap.T.setPoint(n._map,new NPMap.Geometry.Point(n._mapMarker3Ds[e].position.lon,n._mapMarker3Ds[e].position.lat)):n._mapMarker3Ds[e].position,n.lineVisiable&&(n._mapMarker3Ds[e].isBloom?n._mapMarker3Ds[e].line.layers.set(1):n._mapMarker3Ds[e].line.layers.set(0),n._tb.addAtCoordinate(n._mapMarker3Ds[e].line,[i.lon,i.lat])),n._mapMarker3Ds[e].marker.setLngLat([i.lon,i.lat],n._mapMarker3Ds[e].height),n._mapMarker3Ds[e].marker.addTo(n._map._obj),function(t){var e=n._mapMarker3Ds[t];e.el.onclick=function(t){n._events.triggerEvt("click",e),t.stopPropagation()},e.el.onmousemove=function(){n._events.triggerEvt("mousemove",e)},e.el.onmouseover=function(){n._events.triggerEvt("mouseover",e)},e.el.onmouseout=function(){n._events.triggerEvt("mouseout",e)}}(e)}},t.prototype.removeMarker3Ds=function(){for(var t=0;t<this._mapMarker3Ds.length;t++){this._mapMarker3Ds[t].marker.remove();var e=this._tb.world.getObjectByName("MapMarker3DLine"+this._mapMarker3Ds[t].id);e&&this._tb.remove(e)}},t.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},t.prototype.destory=function(){this._events=null;for(var t=0;t<this._mapMarker3Ds.length;t++){this._mapMarker3Ds[t].marker.remove(),this._mapMarker3Ds[t].marker=null;var e=this._tb.world.getObjectByName("MapMarker3DLine"+this._mapMarker3Ds[t].id);e&&this._tb.remove(e),this._mapMarker3Ds[t].line=null,this._mapMarker3Ds[t]=null}this.marker3DsDataArray.length=0,this._mapMarker3Ds.length=0}}(),function(){var h=function(t,e,i){this._map=t,this._tb=e,this.id=i.id,this.position=i.position,this.height=i&&i.height?i.height:0,this.showType=i&&i.showType?i.showType:"text",this.imgUrl=i&&i.imgUrl?i.imgUrl:"",this.imgSize=i&&i.imgSize?i.imgSize:24,this.message=i&&i.message?i.message:"",this.baseLineHeight=i.baseLineHeight?i.baseLineHeight:0,this.lineColor=i&&i.lineColor?i.lineColor:"white",this.lineEnableTexture=null!=i.lineEnableTexture&&i.lineEnableTexture,this.lineTextureUrl=i&&i.lineTextureUrl?i.lineTextureUrl:"",this.lineTextureWidth=i&&i.lineTextureWidth?i.lineTextureWidth:2,this.isBloom=null!=i.isBloom&&i.isBloom,this.bounds=i.bounds;var o=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.baseLineHeight]),r=o.z,a=this._tb.utils.projectToWorld([this.position.lon,this.position.lat,this.height]),s=a.z;if("img"==this.showType&&(this.imgTexture=(new THREE.TextureLoader).load(this.imgUrl),this.material=new THREE.PointsMaterial({size:this.imgSize,sizeAttenuation:!1,map:this.imgTexture,alphaTest:.3,transparent:!0})),"text"==this.showType){var n=document.createElement("canvas");n.width=200,n.height=200,n.style.width="200px",n.style.height="200px";var l=n.getContext("2d");l.font='24px " 微软雅黑';var h=l.measureText(this.message).width;h<=200?(l.lineWidth=6,l.strokeStyle="white",l.strokeText(this.message,100-h/2,100,200),l.fillStyle="black",l.fillText(this.message,100-h/2,100,200)):200<h&&(l.lineWidth=6,l.strokeStyle="white",l.strokeText(message,0,100,200),l.fillStyle="black",l.fillText(message,0,100,200)),this.textTexture=new THREE.Texture(n),this.textTexture.needsUpdate=!0,this.material=new THREE.PointsMaterial({size:100,sizeAttenuation:!1,map:this.textTexture,alphaTest:.3,transparent:!0})}if(this.geometry=new THREE.BufferGeometry,this.geometry.setAttribute("position",new THREE.Float32BufferAttribute([0,0,0],3)),this.pointMesh=new THREE.Points(this.geometry,this.material),this.pointMesh.name="markerReal3D"+this.id,this.pointMesh.eventID=i.id,this.lineEnableTexture&&""!=this.lineTextureUrl){var p=(new THREE.TextureLoader).load(this.lineTextureUrl),c=new THREE.MeshBasicMaterial({map:p,side:THREE.DoubleSide,blending:THREE.AdditiveBlending,transparent:!0}),u=new THREE.BufferGeometry,m=new Float32Array(36),y=.01*this.lineTextureWidth;m.set([-1*y,0,r,y,0,r,y,0,s,y,0,s,-1*y,0,s,-1*y,0,r,0,-1*y,r,0,y,r,0,y,s,0,y,s,0,-1*y,s,0,-1*y,r]),110<=parseInt(THREE.REVISION)?u.setAttribute("position",new THREE.BufferAttribute(m,3)):u.addAttribute("position",new THREE.BufferAttribute(m,3));var d=new Float32Array(36);d.set([0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0]),110<=parseInt(THREE.REVISION)?u.setAttribute("normal",new THREE.BufferAttribute(d,3)):u.addAttribute("normal",new THREE.BufferAttribute(d,3));var _=new Float32Array(24);_.set([0,0,1,0,1,1,1,1,0,1,0,0,0,0,1,0,1,1,1,1,0,1,0,0]),110<=parseInt(THREE.REVISION)?u.setAttribute("uv",new THREE.BufferAttribute(_,2)):u.addAttribute("uv",new THREE.BufferAttribute(_,2)),this.line=new THREE.Mesh(u,c),this.line.name="markerLineReal3D"+this.id}else{var f=new THREE.LineBasicMaterial({color:this.lineColor,linecap:"round",linejoin:"round"}),g=new THREE.Geometry,v=(g=new THREE.Geometry,new THREE.Vector3(0,0,o.z)),M=new THREE.Vector3(0,0,a.z);g.vertices.push(v,M),this.line=new THREE.Line(g,f),this.line.name="markerLineReal3D"+this.id}},t=MapPlatForm.Base.MapMarkerReal3DLayer=function(t,e,i){this._map=t,this._tb=e;this._mapRealMarker3Ds=[],this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.isCollision=null!=i.isCollision&&i.isCollision,this.lineVisiable=null!=i.lineVisiable&&i.lineVisiable,this.projectChange=null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"])};t.prototype.addMarker3Ds=function(t){var l=this;this._mapRealMarker3Ds=[];for(var e in this._mapRealMarker3Ds=function(t,e,i){for(var o=[],r=0;r<t.length;r++){var a={point:t[r].point,height:t[r].height?t[r].height:0,message:t[r].message,showType:t[r].showType,imgSize:t[r].imgSize},s=l._caluPOIRect(a),n={id:NPMap.Utils.BaseUtils.uuid(),position:t[r].point,height:t[r].height?t[r].height:0,showType:t[r].showType,imgUrl:t[r].imgUrl,imgSize:t[r].imgSize,message:t[r].message,baseLineHeight:t[r].baseLineHeight?t[r].baseLineHeight:0,lineColor:t[r].lineColor,lineEnableTexture:t[r].lineEnableTexture,lineTextureUrl:t[r].lineTextureUrl,lineTextureWidth:t[r].lineTextureWidth,isBloom:null!=t[r].isBloom&&t[r].isBloom,bounds:s};o[t[r].id]=new h(l._map,l._tb,n),o[t[r].id].data=t[r].data?t[r].data:{}}return o}(t,l._map,l._tb),l._mapRealMarker3Ds){var i=null;if(i=l.projectChange?NPMap.T.setPoint(l._map,l._mapRealMarker3Ds[e].position):l._mapRealMarker3Ds[e].position,l._mapRealMarker3Ds[e].isBloom?l._mapRealMarker3Ds[e].pointMesh.layers.set(1):l._mapRealMarker3Ds[e].pointMesh.layers.set(0),l._tb.addAtCoordinate(l._mapRealMarker3Ds[e].pointMesh,[i.lon,i.lat]),0<l._mapRealMarker3Ds[e].height){var o=l._tb.utils.projectToWorld([l._mapRealMarker3Ds[e].position.lon,l._mapRealMarker3Ds[e].position.lat,l._mapRealMarker3Ds[e].height]).z;l._mapRealMarker3Ds[e].pointMesh.translateZ(o)}l.lineVisiable&&(l._mapRealMarker3Ds[e].isBloom?l._mapRealMarker3Ds[e].line.layers.set(1):l._mapRealMarker3Ds[e].line.layers.set(0),l._tb.addAtCoordinate(l._mapRealMarker3Ds[e].line,[i.lon,i.lat]))}l._map._obj.on("mousemove",function(t){var e=t.point;for(var i in l._mapRealMarker3Ds){var o=l._mapRealMarker3Ds[i],r={point:o.position,height:o.height,message:o.message,showType:o.showType,imgSize:o.imgSize},a=l._caluPOIRect(r);l._mapRealMarker3Ds[i].bounds=a,l._pointIsPOIRect(e,l._mapRealMarker3Ds[i].bounds)&&(l._events.triggerEvt("mousemove",l._mapRealMarker3Ds[i]),l._map._obj.getCanvas().style.cursor="pointer")}}),l._map._obj.on("click",function(t){var e=t.point;for(var i in l._mapRealMarker3Ds)l._pointIsPOIRect(e,l._mapRealMarker3Ds[i].bounds)&&l._events.triggerEvt("click",l._mapRealMarker3Ds[i])}),l._map._obj.on("mouseover",function(t){var e=t.point;for(var i in l._mapRealMarker3Ds)l._pointIsPOIRect(e,l._mapRealMarker3Ds[i].bounds)&&(l._events.triggerEvt("mouseover",l._mapRealMarker3Ds[i]),l._map._obj.getCanvas().style.cursor="pointer")}),l._map._obj.on("mouseout",function(t){var e=t.point;for(var i in l._mapRealMarker3Ds)l._pointIsPOIRect(e,l._mapRealMarker3Ds[i].bounds)&&(l._events.triggerEvt("mouseout",l._mapRealMarker3Ds[i]),l._map._obj.getCanvas().style.cursor="pointer")})},t.prototype.removeMarker3Ds=function(){for(var t=0;t<this._mapRealMarker3Ds.length;t++){var e=this._tb.world.getObjectByName("markerReal3D"+this._mapRealMarker3Ds[t].id);e&&this._tb.remove(e);var i=this._tb.world.getObjectByName("markerLineReal3D"+this._mapRealMarker3Ds[t].id);i&&this._tb.remove(i)}},t.prototype._caluPOIRect=function(t){var e=t.point;e=NPMap.T.setPoint(this._map,e);var i=t.height,o=t&&t.message?t.message:"",r=t.showType,a=t.imgSize,s=e.lon,n=e.lat,l=this._map._obj.project(new mapboxgl.LngLat(s,n),i);if("text"==r){var h=document.createElement("canvas");h.width=200,h.height=200,h.style.width="200px",h.style.height="200px";var p=h.getContext("2d");p.font='24px " 微软雅黑';var c=p.measureText(o).width;if(c<=200)var u=l.x-c/2/2,m=l.y-14,y=c/2,d=14;else if(200<c)u=l.x-50,m=l.y-14,y=100,d=14;return{x:u,y:m,w:y,h:d}}if("img"==r)return{x:l.x-a/2,y:l.y-a/2,w:a,h:a}},t.prototype._isPOIRect=function(t,e){var i=t.x,o=t.y,r=t.w,a=t.h,s=e.x,n=e.y,l=e.w,h=e.h;return!(s<=i&&s+l<=i)&&(!(i<=s&&i+r<=s)&&(!(n<=o&&n+h<=o)&&!(o<=n&&o+a<=n)))},t.prototype._pointIsPOIRect=function(t,e){var i=e.x,o=e.y,r=e.w,a=e.h,s=t.x,n=t.y;return i<s&&s<i+r&&o<n&&n<o+a},t.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},t.prototype.destory=function(){this._events=null;for(var t=0;t<this._mapRealMarker3Ds.length;t++){var e=this._tb.world.getObjectByName("markerReal3D"+this._mapRealMarker3Ds[t].id);e&&this._tb.remove(e),this._mapRealMarker3Ds[t].pointMesh=null;var i=this._tb.world.getObjectByName("markerLineReal3D"+this._mapRealMarker3Ds[t].id);i&&this._tb.remove(i),this._mapRealMarker3Ds[t].line=null,this._mapRealMarker3Ds[t]=null}this._mapRealMarker3Ds.length=0}}(),function(){var t=MapPlatForm.Base.AnimationRadar=function(t,e,i){this._map=t,this._layerID=NPMap.Utils.BaseUtils.uuid(),this._minZoom=i&&i.minZoom?i.minZoom:11,this._lineWidth=i&&i.lineWidth?i.lineWidth:5,this._lineColor=i&&i.lineColor?i.lineColor:"#FF0000",this._angleColor=i&&i.angleColor?i.angleColor:"#FF0000",this._animation=i&&i.animation,this.tb=e,this.timer=null,this.depthTest=!1,this._initMaterial()};t.prototype._initMaterial=function(){this.lineMaterial=new THREE.LineMaterial({color:this._lineColor,linewidth:5e-4*this._lineWidth,dashed:!1,transparent:!0,opacity:1,fog:!0,depthTest:this.depthTest,wireframe:!1}),this.dlineMaterial=new THREE.LineMaterial({color:this._lineColor,dashSize:1,gapSize:1,dashScale:2,scale:2,linewidth:.0015,opacity:1,depthTest:this.depthTest,dashed:!0}),this.dlineMaterial.defines.USE_DASH="",this.dlineMaterial1=new THREE.LineMaterial({color:this._lineColor,depthTest:this.depthTest,linewidth:.001,opacity:1}),this.matLineBasic=new THREE.LineBasicMaterial({color:this._lineColor,opacity:.2,transparent:!0,depthTest:this.depthTest}),this.rmaterial=new THREE.ShaderMaterial({uniforms:{color:{value:new THREE.Color(16777215)},fogColor:{value:this.tb.scene.fog.color},fogNear:{value:this.tb.scene.fog.near},fogFar:{value:this.tb.scene.fog.far}},depthTest:this.depthTest,fog:!0,vertexShader:"#include <fog_pars_vertex>\nattribute float cusmtomOpacity;attribute vec3 customColor;varying vec3 vColor;varying float vOpacity;void main() {vColor = customColor;vOpacity = cusmtomOpacity;vec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );gl_Position = projectionMatrix * mvPosition;\n\t#include <fog_vertex>\n\t}",fragmentShader:"#include <fog_pars_fragment>\nuniform vec3 color;varying vec3 vColor;varying float vOpacity;void main() {gl_FragColor = vec4(color * vColor,vOpacity);\n\t#include <fog_fragment>\n\t}",transparent:!0}),this.circleMaterial=this.rmaterial.clone(),this.materialCuXu=new THREE.LineMaterial({color:this._lineColor,linewidth:.005,depthTest:this.depthTest,opacity:1})},t.prototype.add=function(t,e){var X=this;this.obj=function(t,e,i,o,r,a){for(var s=NPMap.Utils.MapUtil.colorRgb(r),n={},l=NPMap.T.setPoint(i,t),h=o.utils.projectToWorld([l.lon,l.lat,0]),p=NPMap.T.helper.webMoctorJW2PM(l.lon,l.lat),c=NPMap.Utils.MapUtil.createRegularPolygon(p,e,60),u=[],m=[],y=[],d=0;d<c.length-1;d++){var _=NPMap.T.helper.inverseMercator(c[d].lon,c[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]);u.push(h.x-h.x,h.y-h.y,0),m.push(s[0]/256,s[1]/256,s[2]/256),y.push(0),u.push(f.x-h.x,f.y-h.y,0),m.push(s[0]/256,s[1]/256,s[2]/256),y.push(.2),_=NPMap.T.helper.inverseMercator(c[d+1].lon,c[d+1].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),u.push(f.x-h.x,f.y-h.y,0),m.push(s[0]/256,s[1]/256,s[2]/256),y.push(.2)}var g=new THREE.BufferGeometry,v=new Float32Array(u.length);v.set(u),g.setAttribute("position",new THREE.BufferAttribute(v,3));var M=new Float32Array(m.length);M.set(m),g.setAttribute("customColor",new THREE.BufferAttribute(M,3));var P=new Float32Array(y.length);P.set(y),g.setAttribute("cusmtomOpacity",new THREE.BufferAttribute(P,1));var b=new THREE.Mesh(g,X.circleMaterial);o.addAtCoordinate(b,[l.lon,l.lat]),n.circle=b;var x=[];for(d=0;d<c.length;d++)_=NPMap.T.helper.inverseMercator(c[d].lon,c[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),x.push(f.x-h.x,f.y-h.y,f.z);var S=new THREE.LineGeometry;S.setPositions(x);var L=new THREE.Line2(S,X.lineMaterial);o.addAtCoordinate(L,[l.lon,l.lat]),n.outLine=L;var w=[];for(d=0;d<c.length;d+=5)_=NPMap.T.helper.inverseMercator(c[d].lon,c[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),w.push(0,0,0),w.push(f.x-h.x,f.y-h.y,f.z);var C=new THREE.BufferGeometry;C.setAttribute("position",new THREE.Float32BufferAttribute(w,3)),C.computeBoundingSphere();var B=new THREE.Line(C,X.matLineBasic);o.addAtCoordinate(B,[l.lon,l.lat]),n.sline=B;var I=NPMap.Utils.MapUtil.createRegularPolygon(p,.85*e,60),k=[];for(d=0;d<I.length;d++)_=NPMap.T.helper.inverseMercator(I[d].lon,I[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),k.push(f.x-h.x,f.y-h.y,f.z);var T=new THREE.LineGeometry;T.setPositions(k);var R=new THREE.Line2(T,X.dlineMaterial);R.computeLineDistances(),o.addAtCoordinate(R,[l.lon,l.lat]),n.dline=R,n.lines=[];for(var F=0;F<3;F++){var E=NPMap.Utils.MapUtil.createRegularPolygon(p,e*(.7-.15*F),60),A=[];for(d=0;d<E.length;d++)_=NPMap.T.helper.inverseMercator(E[d].lon,E[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),A.push(f.x-h.x,f.y-h.y,f.z);if(0==F){n.lines2=[];for(var D=0;D<3;D++){for(vvs=[],d=20*D;d<20*D+8;d++)_=NPMap.T.helper.inverseMercator(E[d].lon,E[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),vvs.push(f.x-h.x,f.y-h.y,f.z);var N=new THREE.LineGeometry;N.setPositions(vvs);var j=new THREE.Line2(N,X.materialCuXu);o.addAtCoordinate(j,[l.lon,l.lat]),n.lines2.push(j)}}var O=new THREE.LineGeometry;O.setPositions(A);var G=new THREE.Line2(O,X.dlineMaterial1);G.computeLineDistances(),o.addAtCoordinate(G,[l.lon,l.lat]),n.lines.push(G)}var H=NPMap.Utils.MapUtil.createCircleSector(p,e,15,90,0,!1),U=[],q=[],z=[],V=new THREE.BufferGeometry;for(d=0;d<H.length-1;d++)_=NPMap.T.helper.inverseMercator(H[d].lon,H[d].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),U.push(h.x-h.x,h.y-h.y,0),q.push(s[0]/256,s[1]/256,s[2]/256),z.push(0),U.push(f.x-h.x,f.y-h.y,0),q.push(s[0]/256,s[1]/256,s[2]/256),z.push(.9/H.length*d),_=NPMap.T.helper.inverseMercator(H[d+1].lon,H[d+1].lat),f=o.utils.projectToWorld([_.lon,_.lat,0]),U.push(f.x-h.x,f.y-h.y,0),q.push(s[0]/256,s[1]/256,s[2]/256),z.push(.9/H.length*(d+1));var J=new Float32Array(U.length);J.set(U),V.setAttribute("position",new THREE.BufferAttribute(J,3));var W=new Float32Array(q.length);W.set(q),V.setAttribute("customColor",new THREE.BufferAttribute(W,3));var Z=new Float32Array(z.length);Z.set(z),V.setAttribute("cusmtomOpacity",new THREE.BufferAttribute(Z,1));var Y=new THREE.Mesh(V,X.rmaterial);return o.addAtCoordinate(Y,[l.lon,l.lat]),n.rMesh=Y,n}(t,e,this._map,this.tb,this._angleColor,this._lineColor),this.scale=0,clearTimeout(this.timer),this._update()},t.prototype.remove=function(){if(clearTimeout(this.timer),this.obj){this.tb.remove(this.obj.outLine),this.obj.outLine.geometry=null,this.obj.outLine=null,this.tb.remove(this.obj.sline),this.obj.sline.geometry=null,this.obj.sline=null,this.tb.remove(this.obj.dline),this.obj.dline.geometry=null,this.obj.dline=null;for(var t=0;t<this.obj.lines.length;t++)this.tb.remove(this.obj.lines[t]),this.obj.lines[t].geometry=null,this.obj.lines[t]=null;for(t=this.obj.lines.length=0;t<this.obj.lines2.length;t++)this.tb.remove(this.obj.lines2[t]),this.obj.lines2[t].geometry=null,this.obj.lines2[t]=null;this.obj.lines2.length=0,this.tb.remove(this.obj.rMesh),this.obj.rMesh.geometry=null,this.obj.rMesh=null,this.obj.circle&&(this.tb.remove(this.obj.circle),this.obj.circle.geometry=null,this.obj.circle=null),this.obj=null}},t.prototype._update=function(){if(this.scale=this.scale+.04,this.scale<1.04)this.obj.circle.scale.set(this.scale,this.scale,1),this.rmaterial.visible=!1,this.dlineMaterial.visible=!1,this.dlineMaterial1.visible=!1,this.matLineBasic.visible=!1,this.lineMaterial.visible=!1,this.materialCuXu.visible=!1;else{this.obj.circle&&(this.tb.remove(this.obj.circle),this.obj.circle.geometry=null,this.obj.circle=null),this.rmaterial.visible=!0,this.dlineMaterial.visible=!0,this.dlineMaterial1.visible=!0,this.matLineBasic.visible=!0,this.lineMaterial.visible=!0,this.materialCuXu.visible=!0,this.obj.rMesh.rotateZ(5/180*Math.PI),this.obj.dline.rotateZ(-.5/180*Math.PI);for(var t=0;t<this.obj.lines2.length;t++)this.obj.lines2[t].rotateZ(.5/180*Math.PI)}this._map.getZoom()>=this._minZoom&&(this._map._obj.repaint=!0),this.timer=setTimeout(this._update.bind(this),30)},t.prototype.destory=function(){this.remove(),this.rmaterial=null,this.dlineMaterial=null,this.dlineMaterial1=null,this.matLineBasic=null,this.lineMaterial=null,this.materialCuXu=null,this.circleMaterial=null}}(),function(){var n=function(t,e,i){this._map=t,this._tb=e,this.lineMarkerMaterial=new THREE.LineMaterial({color:i.color?i.color:"red",linewidth:i.weight?i.weight/1e3:.002,opacity:i.opacity?i.opacity:1,transparent:!0}),this.id=i.id,this.isBloom=null!=i.isBloom&&i.isBloom;var o=new THREE.LineGeometry,r=i.position,a=r[0];a=NPMap.T.setPoint(t,a);for(var s=[],n=this._tb.utils.projectToWorld([a.lon,a.lat,0]),l=0;l<r.length;l++){var h=r[l];h=NPMap.T.setPoint(t,h);var p=this._tb.utils.projectToWorld([h.lon,h.lat,0]),c=[p.x-n.x,p.y-n.y,0];s.push(c[0],c[1],0)}o.setPositions(s),this.position=a,this.line=new THREE.Line2(o,this.lineMarkerMaterial),this.line.name="threeOverLayPolyline-"+this.id},l=function(t,e,i){this._map=t,this._tb=e;var o,r;o=new THREE.MeshBasicMaterial({color:i.fillColor?i.fillColor:"white",transparent:!!i.fillOpacity,opacity:i.fillOpacity?i.fillOpacity:1}),r=new THREE.LineMaterial({linewidth:5e-4*i.weight,color:i.color?i.color:"black",transparent:!!i.opacity,opacity:i.opacity?i.opacity:1}),this.id=i.id,this.outLineMesh=[],this.isBloom=null!=i.isBloom&&i.isBloom;var a=i.position,s=a[0],n=!1;s instanceof Array&&(s=a[0][0],n=!0),s=NPMap.T.setPoint(t,s);var l=this._tb.utils.projectToWorld([s.lon,s.lat,0]);n||(a=[a]);for(var h=new THREE.Shape,p=0;p<a.length;p++){for(var c=[],u=0;u<a[p].length;u++){var m=a[p][u];m=NPMap.T.setPoint(t,m);var y=this._tb.utils.projectToWorld([m.lon,m.lat,0]),d=[y.x-l.x,y.y-l.y,0];c.push([d[0],d[1],0])}var _=[];if(0==p)for(u=0;u<c.length;u++)0==u?h.moveTo(c[u][0],c[u][1]):h.lineTo(c[u][0],c[u][1]),_.push(c[u][0],c[u][1],0);else{var f=new THREE.Path;for(u=0;u<c.length;u++)0==u?f.moveTo(c[u][0],c[u][1]):f.lineTo(c[u][0],c[u][1]),_.push(c[u][0],c[u][1],0);h.holes.push(f)}var g=new THREE.LineGeometry;g.setPositions(_);var v=new THREE.Line2(g,r);v.name="threeOverLayPolygonOutLine-"+this.id,this.outLineMesh.push(v)}this.position=s;var M=new THREE.ShapeBufferGeometry(h);this.mesh=new THREE.Mesh(M,o),this.mesh.name="threeOverLayPolygon-"+this.id},t=MapPlatForm.Base.MapThreeOverLayer=function(t,e,i){this._map=t,this._tb=e,this._minZoom=i&&i.minZoom?i.minZoom:11,this._maxZoom=i&&i.maxZoom?i.maxZoom:22,this.visible=null==i.visible||i.visible,this.projectChange=null==i.projectChange||i.projectChange,this._events=new NPMap.Events(this,["click","mousemove","mouseover","mouseout"]),this.PolylineOverLays=[],this.PolygonOverLays=[]};t.prototype.addOverLays=function(t){for(var e=0;e<t.length;e++)if("NPMap.Geometry.Polyline"===t[e].CLASS_NAME){var i,o=(i=t[e]).getPath(),r={id:i.getId(),position:o,color:i.getColor(),weight:i.getWeight(),opacity:i.getOpacity(),isBloom:i.isBloom};(i=new n(this._map,this._tb,r)).isBloom?i.line.layers.set(1):i.line.layers.set(0),this._tb.addAtCoordinate(i.line,[i.position.lon,i.position.lat]),this.PolylineOverLays.push(i)}else{if("NPMap.Geometry.Polygon"!==t[e].CLASS_NAME)continue;var a;o=(a=t[e]).getPath(),r={id:a.getId(),position:o,color:a.getColor(),fillColor:a.getFillColor(),isBloom:a.isBloom,opacity:a.getOpacity(),fillOpacity:a.getFillOpacity(),weight:a.getWeight()};if((a=new l(this._map,this._tb,r)).isBloom){a.mesh.layers.set(1);for(var s=0;s<a.outLineMesh.length;s++)a.outLineMesh[s].layers.set(1)}else{a.mesh.layers.set(0);for(s=0;s<a.outLineMesh.length;s++)a.outLineMesh[s].layers.set(0)}this._tb.addAtCoordinate(a.mesh,[a.position.lon,a.position.lat]);for(s=0;s<a.outLineMesh.length;s++)this._tb.addAtCoordinate(a.outLineMesh[s],[a.position.lon,a.position.lat]);this.PolygonOverLays.push(a)}},t.prototype.showOverLays=function(t){if(this.PolylineOverLays)for(var e=0;e<this.PolylineOverLays.length;e++){(i=this._tb.world.getObjectByName("threeOverLayPolyline-"+this.PolylineOverLays[e].id))&&(i.material.visible=null==t||t)}if(this.PolygonOverLays)for(e=0;e<this.PolygonOverLays.length;e++){var i=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[e].id),o=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[e].id);i&&(i.material.visible=null==t||t),o&&(o.material.visible=null==t||t)}},t.prototype.addEventListener=function(t,e,i){this._events.register(t,e,i)},t.prototype.removeEventListener=function(t,e){this._events.unregister(t,e)},t.prototype.removeAllOverlays=function(){if(this.PolylineOverLays){for(var t=0;t<this.PolylineOverLays.length;t++){(e=this._tb.world.getObjectByName("threeOverLayPolyline-"+this.PolylineOverLays[t].id))&&this._tb.remove(e)}this.PolylineOverLays[t]=null,this.PolylineOverLays.length=0}if(this.PolygonOverLays){for(t=0;t<this.PolygonOverLays.length;t++){var e;for((e=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[t].id))&&this._tb.remove(e);;){var i=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[t].id);if(!i)break;this._tb.remove(i)}}this.PolygonOverLays[t]=null,this.PolygonOverLays.length=0}},t.prototype.destory=function(){this.removeAllOverlays(),this._events=null},t.prototype.removeOverlays=function(t){for(var e=0;e<t;e++){var i=this._tb.world.getObjectByName("threeOverLayPolyline-"+t[e].id);if(i)return this._tb.remove(i),void this.PolylineOverLays.splice(e,1);i=this._tb.world.getObjectByName("threeOverLayPolygon-"+this.PolygonOverLays[e].id);var o=this._tb.world.getObjectByName("threeOverLayPolygonOutLine-"+this.PolygonOverLays[e].id);o&&this._tb.remove(o),i&&(this._tb.remove(i),this.PolygonOverLays.splice(e,1))}}}(),function(){var t=MapPlatForm.Base.MapBloomManager=function(t,e,i){this._map=t,this._tb=e,this.exposure=i&&i.exposure?parseFloat(i.exposure):1,this.bloomStrength=i&&i.bloomStrength?parseFloat(i.bloomStrength):1,this.bloomThreshold=i&&i.bloomThreshold?parseFloat(i.bloomThreshold):0,this.bloomRadius=i&&i.bloomRadius?parseFloat(i.bloomRadius):0};t.prototype.add=function(){var t={exposure:this.exposure,bloomStrength:this.bloomStrength,bloomThreshold:this.bloomThreshold,bloomRadius:this.bloomRadius},e=this;if(THREE.RenderPass){var i=new THREE.RenderPass(e._tb.scene,e._tb.camera),o=new THREE.UnrealBloomPass(new THREE.Vector2(window.innerWidth,window.innerHeight),1.5,.4,.85);o.threshold=t.bloomThreshold,o.strength=t.bloomStrength,o.radius=t.bloomRadius,e._tb.bloomComposer=new THREE.EffectComposer(e._tb.renderer),e._tb.bloomComposer.addPass(i),e._tb.bloomComposer.addPass(o),e._tb.renderer.toneMappingExposure=Math.pow(t.exposure,4)}},t.prototype.remove=function(){},t.prototype.destory=function(){this._tb.bloomComposer.renderToScreen=!1,this._tb.bloomComposer=null}}();