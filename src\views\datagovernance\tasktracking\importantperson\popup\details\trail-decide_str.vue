<template>
  <ui-modal v-model="visible" title="异常原因" :footerHide="true" width="89%">
    <div class="compare auto-fill">
      <section class="compare-wrapper">
        <div class="wrapper">
          <p class="mb-xs">置信身份</p>
          <div class="indentify-message">
            <ui-frame>
              <ui-image :src="personObj.facePath" class="image" />
            </ui-frame>
            <ul class="message-ul mt-sm ml-lg">
              <li class="message-li">
                <span class="label">姓名：</span>
                <span class="message">{{ personObj.name || '未知' }}</span>
              </li>
              <li class="message-li">
                <span class="label">性别：</span>
                <span class="message">{{ personObj.gender || '未知' }}</span>
              </li>
              <li class="message-li">
                <span class="label">证件号：</span>
                <span class="message">{{ personObj.idCard || '未知' }}</span>
              </li>
              <li class="">
                <tags-more :tagList="personObj.personTypes"></tags-more>
              </li>
            </ul>
          </div>
        </div>
        <div class="ml-lg mr-lg">
          <double-arrow></double-arrow>
        </div>
        <div class="wrapper">
          <p class="mb-xs">抓拍图片</p>
          <div class="indentify-message">
            <ui-frame>
              <ui-image :src="otherMsg.facePath" class="image" />
            </ui-frame>
            <ul class="message-ul mt-lg ml-lg">
              <li class="message-li">
                <span class="label">抓拍时间：</span>
                <span class="message"> {{ otherMsg.receiveTime || '未知' | filterDateFun }}</span>
              </li>
              <!-- <li class="message-li">
							<span class="label">姓名：</span>
							<span class="message">{{ rowData.name }}</span>
						</li> -->
              <li class="message-li">
                <span class="label"> 抓拍地点： </span>
                <span class="message">
                  {{ otherMsg.deviceName || '未知' }}
                </span>
              </li>
            </ul>
          </div>
        </div>
      </section>
      <ui-table
        class="ui-table mt-lg auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
      >
        <template #algorithmType="{ row }">
          <span>{{ row.algorithmType }}</span>
        </template>
      </ui-table>
      <div class="decision-box" v-if="tableData.length">
        <span>综合判定</span>
        <p>
          <span
            class="icon-font icon-zhiliangfen-line standard_icon"
            :class="isUnQuatify ? 'font-warning' : 'font-green'"
          >
            <i class="icon_text_error" :class="isUnQuatify ? 'font-warning' : 'font-green'">
              {{ isUnQuatify ? '不是同一人' : '是同一人' }}
            </i>
          </span>
        </p>
      </div>
    </div>
  </ui-modal>
</template>
<script>
import algorithm from '@/config/api/algorithm.js';
export default {
  props: {
    otherMsg: {
      default: () => {
        return {};
      },
    },
    personObj: {
      default: () => {
        return {};
      },
    },
  },
  computed: {
    isUnQuatify() {
      return this.tableData.some((item) => {
        return !item.flag;
      });
    },
  },
  data() {
    return {
      visible: false,
      tableColumns: [
        { title: '检测算法', key: 'algorithmType', slot: 'algorithmType' },
        { title: '识别结果', key: 'score' },
      ],
      tableData: [],
      minusTable: 600,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      rowData: {
        identityPhoto: '',
        name: '',
        gender: '',
        idCard: '',
        facePath: '',
        logTime: '',
        address: '',
      },
      extra: [], //算法
    };
  },
  created() {},
  methods: {},
  watch: {
    otherMsg(val) {
      if (val.extraResult != null) {
        this.tableData = JSON.parse(val.extraResult);
        let data = {};
        data.typeKey = 'algorithmVendorType';
        this.$http.post(algorithm.dictDataList, data).then((res) => {
          let Msg = res.data.data.entities;
          for (let i of this.tableData) {
            for (let k of Msg) {
              if (i.algorithmType == k.dataKey) {
                i.algorithmType = k.dataValue;
              }
            }
          }
        });
      } else {
        this.tableData = [];
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    UiFrame: require('@/components/ui-frame.vue').default,
    DoubleArrow: require('../../components/double-arrow.vue').default,
    TagsMore: require('@/components/tags-more_str.vue').default,
  },
};
</script>
<style lang="less" scoped>
.compare {
  width: 100%;
  height: 100%;
}
.compare-wrapper {
  color: #fff;
  display: flex;
  align-items: center;
  .wrapper {
    background: #0b2a52;
    padding: 15px 30px;
    flex: 1;
    .image {
      display: inline-block;
      width: 180px;
      height: 180px;
    }
  }
  .double-arrow {
    margin: 0 20px;
  }
}
.indentify-message {
  display: flex;
  .message-ul {
    .message-li {
      height: 30px;
      line-height: 30px;
      .label {
        color: #8797ac;
      }
    }
  }
}
.decision-box {
  display: flex;
  height: 100px;
  line-height: 100px;
  background: #092955;
  color: var(--color-primary);
  padding: 0 20px;
  align-items: center;
  > span,
  > p {
    flex: 1;
  }
}
.standard_icon {
  vertical-align: middle;
  font-size: 120px;
  position: relative;
  .icon_text_error {
    font-size: 16px;
    position: absolute;
    right: 20px;
    top: 10px;
    font-weight: bold;
    transform: rotate(-32deg);
  }
  .font-green {
    right: 25px;
    top: 7px;
  }
}
</style>
