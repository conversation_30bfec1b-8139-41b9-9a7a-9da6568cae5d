---
description: 
globs: 
alwaysApply: false
---
Description:
  统一 Vuex Store 添加规范，确保 store 目录结构、模块命名、导出方式、命名空间、注释等一致性和可维护性。

Globs:
  src/store/modules/**/*.js

## Store 添加规范
- 每个 store 模块应单独放在 `src/store/modules/` 目录下，建议按业务拆分子目录（如 admin、user 等）。
- 模块文件命名采用小驼峰（如 user.js、permission.js），子模块目录下入口为 `index.js`。
- 每个模块必须 `export default` 一个对象，包含 `state`、`mutations`、`actions`、`getters`，如有需要可只包含部分属性。
- 所有模块必须加上 `namespaced: true`，避免命名冲突。
- 复杂业务建议拆分子模块，子模块通过 `modules` 属性引入。
- 每个 state、mutation、action、getter 必须有注释，说明用途。
- 不允许直接修改 state，必须通过 mutation。
- 入口文件 `src/store/index.js` 需自动批量引入所有模块，禁止手动逐个 import。
- 新增模块需保证不影响现有 store 注册和命名空间。
- 推荐每个模块文件头部注明作者、创建时间、修改记录等信息。

## 目录结构示例
store/
  modules/
    myStore.js
  index.js

## 适用范围
- 适用于 `src/store/modules/` 目录下所有 Vuex store 模块的新增与维护。


