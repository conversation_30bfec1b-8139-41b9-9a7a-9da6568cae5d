<template>
  <div
    class="full-modal"
    v-if="isShow"
    :style="{
      top: `${offsetTop}px`,
      left: `${offsetLeft}px`,
      width: `calc(100% - ${offsetLeft}px)`,
      height: `calc(100% - ${offsetTop}px)`,
    }"
  >
    <header class="full-modal-header">
      <div style="display: flex">
        <div class="full-modal-header-back" @click="close">
          <i class="iconfont icon-return"></i>
        </div>
        <div class="full-modal-header-info">
          <div class="full-modal-header-text">{{ title }}</div>
        </div>
      </div>
    </header>
    <div class="content">
      <slot></slot>
    </div>
    <div class="footer" v-if="!footerHide">
      <div class="footer-btn btn-tog" @click="confirm">确定</div>
      <div class="footer-btn btn-sta" @click="close">取消</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "fullModal",
  props: {
    title: {
      type: String,
      default: "提示",
    },
    footerHide: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShow: false,
      offsetTop: 0,
      offsetLeft: 0,
    };
  },
  methods: {
    open() {
      this.setStyle();
      this.isShow = true;
    },
    close() {
      this.isShow = false;
      this.$emit("on-cancel");
    },
    confirm() {
      this.$emit("on-ok");
    },
    setStyle() {
      let mainDom = document.querySelector(".i-layout-content-main");
      this.offsetTop =
        mainDom.offsetTop +
        (mainDom.clientHeight - mainDom.childNodes[0].clientHeight) / 2;
      this.offsetLeft =
        mainDom.offsetLeft +
        (mainDom.clientWidth - mainDom.childNodes[0].clientWidth) / 2;
    },
  },
  activated() {},
  mounted() {},
};
</script>

<style lang="less" scoped>
@blueColor: #2c86f8;
.epl(@width) {
  width: @width;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.full-modal {
  font-size: 14px;
  width: 100%;
  height: 100%;
  background: white;
  position: fixed;
  border-radius: 5px;
  z-index: 999;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .content {
    flex: 1;
    padding: 10px;
    overflow: hidden;
    background: #fff;
  }
  .full-modal-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #d3d7de;
    background: #ffffff;
    box-sizing: border-box;
    user-select: none;
    position: relative;
    .full-modal-header-back {
      cursor: pointer;
      width: 70px;
      height: 100%;
      background-color: #2c86f8;
      color: white;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .full-modal-header-info {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .full-modal-header-text {
      font-weight: bold;
    }
  }
  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    background: #ffffff;
    padding-bottom: 10px;
    user-select: none;
    height: 50px;
    padding-top: 5px;
    .footer-btn {
      width: 80px;
      height: 30px;
      box-sizing: border-box;
      text-align: center;
      line-height: 30px;
      border-radius: 3px;
      margin-right: 20px;
      cursor: pointer;
    }
    .btn-tog {
      background: @blueColor;
      color: #fff;
    }
    .btn-sta {
      border: 1px solid @blueColor;
      color: @blueColor;
      background: #ffffff;
    }
  }
}
</style>
