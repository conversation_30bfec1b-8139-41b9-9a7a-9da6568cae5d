<template>
  <div class="line-chart-container">
    <title-section title-name="历史趋势 ">
      <div class="search" slot="content">
        <tag-view slot="mid" :list="tagList" @tagChange="changeStatus" ref="tagView" class="tag-view"></tag-view>
        <DatePicker
          class="ml-md"
          ref="DatePicker"
          v-if="dateType === 'DAY'"
          type="month"
          placeholder="请选择月份"
          format="yyyy年MM月"
          :value="month"
          :editable="false"
          @on-change="handleChange"
        ></DatePicker>
        <DatePicker
          class="ml-md"
          ref="yearPicker"
          v-else-if="dateType === 'MONTH'"
          type="year"
          placeholder="请选择年"
          format="yyyy年"
          :value="year"
          :editable="false"
          @on-change="handleChangeYear"
        ></DatePicker>
      </div>
    </title-section>
    <div class="echarts-box" v-ui-loading="{ loading: loading, tableData: Object.keys(echart) }">
      <draw-echarts
        v-if="Object.keys(echart).length"
        :echart-option="echartRin"
        :echart-style="rinStyle"
        ref="zdryChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
import ShadeCaptureStabilitylineChartTooltip from '@/views/governanceevaluation/evaluationoResult/result-modules/CaptureStability/components/line-chart-tooltip.vue';
import Vue from 'vue';
export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    TitleSection: require('@/views/governanceevaluation/evaluationoResult/components/title-section.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
  props: {
    columns: {},
  },
  mixins: [dealWatch],
  data() {
    let _that = this;
    return {
      barData: [],
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
      loading: false,
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      echart: {},
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
      tagList: ['日', '月'],
      dateType: 'DAY',
      tooltipFormatter: (data) => {
        let lineChartTooltipConstructor = Vue.extend(ShadeCaptureStabilitylineChartTooltip);
        let _this = new lineChartTooltipConstructor({
          el: document.createElement('div'),
          data() {
            return {
              data,
              dateType: _that.dateType,
              columns: _that.columns,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  mounted() {
    this.getDate();
    this.startWatch(
      '$route.query',
      () => {
        this.getDayCaptureStatistics();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    /**
     * DAY/MONTH
     * @param index
     * @param item
     */
    changeStatus(index, item) {
      if (item === '日') {
        this.dateType = 'DAY';
        this.getDate();
      } else if (item === '月') {
        this.month = null;
        this.monthIndex = 0;
        this.dateType = 'MONTH';
      }
      this.getDayCaptureStatistics();
    },
    /**
     * actualNum: 0
     * dateType: "MONTH"
     * fetchType: "NEW"
     * horizontal: "01"
     * indexName: "填报准确率"
     * qualifiedNum: 0
     * startTime: "2022-08-01 12:00:00"
     * unqualifiedNum: 0
     * vertical: "100.0"
     */
    initRin() {
      /**
       * data : {
       *   default: [],
       *   aaaa: [],
       *   bbb: []
       * }
       */
      let opts = {
        data: this.echart,
        dateType: this.dateType,
        columns: this.columns,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.echartRin = this.$util.doEcharts.lineCircleShadeControlStability(opts);
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear(value) {
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    async getDayCaptureStatistics() {
      try {
        const { regionCode, orgCode, statisticType, taskSchemeId, indexType, indexId } = this.$route.query;
        this.loading = true;
        let params = {
          taskSchemeId: taskSchemeId,
          indexId: indexId,
          indexType: indexType,
          dateType: this.dateType,
          year: this.year,
          month: this.monthIndex,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
        };
        /**
         * data : {
         *   default: [],
         *   aaaa: [],
         *   bbb: []
         * }
         */
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getHistoryTrend, params);
        this.echart = data || {};
        this.initRin();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.line-chart-container {
  position: relative;
  height: 100%;
  width: 100%;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  border-radius: 4px;
  .search {
    position: relative;
    display: flex;
    align-items: center;

    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;

      > div {
        color: #fff;
        margin-left: 42px;
        font-size: 14px;
      }

      .font-sky {
        color: #25e6fd;
      }

      .font-orange {
        color: #f78b2e;
      }
    }
  }

  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);

    .charts {
      height: 100% !important;
    }
  }
}
</style>
