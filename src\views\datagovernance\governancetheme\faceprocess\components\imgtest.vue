<template>
  <div>
    <ui-modal v-model="visible" title="图像上传及时性配置" width="39.06rem">
      <div class="upload-setting">
        <Form ref="formValidate" :model="formValidate" :label-width="0">
          <FormItem v-for="(item, index) in formValidate.items" :key="index" label="">
            <Row>
              <Col span="24">
                <div style="display: flex">
                  <div class="upload-setting-label">
                    <span>{{ item.label }}不超过</span>
                  </div>
                  <FormItem
                    :prop="'items.' + index + '.value1'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入数字',
                        trigger: 'blur',
                        type: 'number',
                      },
                    ]"
                  >
                    <Input v-model="item.value1" number style="width: 118px" />
                  </FormItem>
                  <FormItem>
                    <Select
                      v-model="item.value2"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      style="width: 60px"
                    >
                      <Option value="1">时</Option>
                      <Option value="2">分</Option>
                      <Option value="3">秒</Option>
                    </Select>
                  </FormItem>
                </div>
              </Col>
            </Row>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Button type="primary" @click="save">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    propData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      formValidate: {},
    };
  },
  created() {},
  mounted() {
    this.formValidate = JSON.parse(JSON.stringify(this.propData));
  },
  methods: {
    init(processOptions) {
      this.topicComponentId = processOptions.topicComponentId;
      this.visible = true;
      this.getUploadTime();
    },
    async getUploadTime() {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
        };
        let res = await this.$http.get(governancetheme.queryTopicComponentOfUploadTime, { params });
        const datas = res.data.data;
        if (datas.extraParam) {
          let extraParam = JSON.parse(datas.extraParam);
          if (!this.formValidate.items.length) {
            return false;
          }
          this.formValidate.items = this.formValidate.items.map((item) => {
            item.value1 = Number(extraParam[item.key1]);
            item.value2 = extraParam[item.key2];
            return item;
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    save() {
      const _this = this;
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          let extraParam = {};
          _this.formValidate.items.map((item) => {
            extraParam[item.key1] = item.value1;
            extraParam[item.key2] = item.value2;
          });
          extraParam = JSON.stringify(extraParam);
          _this.updateUploadTime(extraParam);
        } else {
          this.$Message.error('请将信息填写完整！');
        }
      });
    },
    async updateUploadTime(extraParam) {
      try {
        let params = {
          topicComponentId: this.topicComponentId,
          extraParam: extraParam,
        };
        await this.$http.post(governancetheme.updateTopicComponentOfUploadTime, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('图像上传及时性配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.formValidate = this.propData;
    },
  },
  watch: {
    propData() {
      this.formValidate = this.propData;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  padding: 20 51px 37px;
}
.upload-setting {
  width: 100%;
  margin-bottom: 16px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  &-label {
    font-size: 14px;
    color: #ffffff;
  }
  @{_deep} .ivu-input-wrapper {
    margin: 0 10px;
  }
}
</style>
