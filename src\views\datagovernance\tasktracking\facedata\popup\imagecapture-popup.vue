<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <tagView class="mt10" ref="tagView" :list="['图像模式', '设备模式']" @tagChange="tagChange1" />
    <div v-show="modelTag == 0">
      <face-device-search ref="faceSearchRef" @startSearch="startSearch">
        <p class="search-box fr">
          <Button type="primary" class="ml-lg button-blue" @click="exportExcel">
            <i class="icon-font icon-daochu delete-icon"></i>
            <span class="inline vt-middle ml-xs">导出</span>
          </Button>
        </p>
      </face-device-search>
      <p class="statics mt-sm mb-sm">
        {{ staticMessage.totalFace.label }}：<span class="active-color mr-sm">
          {{ staticMessage.totalFace.value }}</span
        >
        {{ staticMessage.captureExpectFace.label }}：
        <span class="font-red"> {{ staticMessage.captureExpectFace.value }}</span>
      </p>
      <div class="list-box">
        <face-list
          :face-loading="faceLoading"
          :face-list="faceList"
          :has-hover="faceHasHover"
          @uploadTips="uploadTips"
        ></face-list>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
    <div v-if="visible">
      <faceShebeiList
        ref="faceShebeiList"
        :minusHeight="368"
        v-show="modelTag == 1"
        v-model="modelTag"
        :listObj="listObj"
        :import-api="importApi"
        @unqualified="unqualified"
      />
    </div>
  </ui-modal>
</template>
<style lang="less" scoped>
.statics {
  color: #fff;
  .active-color {
    color: var(--color-bluish-green-text);
  }
}
.list-box {
  position: relative;
}
</style>
<script>
import tasktracking from '@/config/api/tasktracking';
import faceEnum from '../util/enum';
export default {
  props: {
    value: {},
    faceLoading: {
      default: false,
    },
    faceList: {
      default: () => [],
    },
    totalListCount: {
      default: 0,
    },
    staticsObject: {
      type: Object,
    },
    importTotalCount: {},
  },
  data() {
    return {
      importApi: { api: tasktracking.downloadFaceLibAbnormalDev },
      visible: false,
      styles: {
        //top: "0.5rem",
        width: '95%',
      },
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      title: '',
      staticMessage: {
        totalFace: {
          label: '人脸图像总量',
          value: 0,
        },
        captureExpectFace: {
          label: '抓拍时间异常图像',
          value: 0,
        },
      },
      options: {},
      faceHasHover: true,
      searchData: {
        startTime: '',
        endTime: '',
        deviceIds: [],
      },
      modelTag: 0,
      listObj: {
        columns: [],
        key: 'entities',
        loadData: (parameter) => {
          return this.$http
            .post(
              tasktracking.queryFaceLibAbnormalTaskPageDev,
              Object.assign(parameter, {
                topicComponentId: this.options.filedData.topicComponentId,
              }),
            )
            .then((res) => {
              // 把后台数组转为list返回
              return res.data;
            });
        },
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    init(option) {
      this.$refs.faceSearchRef.resetClick();
      this.pageData = { pageNum: 1, pageSize: 20, totalCount: 0 };
      this.staticMessage.totalFace.value = 0;
      this.staticMessage.captureExpectFace.value = 0;
      this.options = option;
      this.querySumStatistics();
      this.title = option.title;
      if (option.subTitle) {
        this.title = option.title + `-${option.subTitle}`;
      }
      if (this.$refs.tagView) {
        this.$refs.tagView.curTag = 0;
      }
      this.modelTag = 0;
      this.faceHasHover = true;
      const strategy = {
        // 图像抓拍时间准确性检测
        [faceEnum.aggregateEnums.accurrency]: () => {
          this.staticMessage.captureExpectFace.label = '抓拍时间异常图像';
          this.listObj.columns = this.columnsC([
            { title: '抓拍时间异常图像数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
        },
        // 图像上传及时性检测
        [faceEnum.aggregateEnums.timeliness]: () => {
          this.staticMessage.captureExpectFace.label = '上传超时图像';
          this.listObj.columns = this.columnsC([
            { title: '上传超时图像数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
        },
        // 大图URL检测
        [faceEnum.aggregateEnums.bigPic]: () => {
          this.staticMessage.captureExpectFace.label = '大图URL不可访问';
          this.listObj.columns = this.columnsC([
            { title: '大图URL不可访问数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
          this.faceHasHover = false;
        },
        // 短时大量抓拍检测
        [faceEnum.aggregateEnums.ShortCapture]: () => {
          this.staticMessage.captureExpectFace.label = '短时大量抓拍';
          this.listObj.columns = this.columnsC([
            { title: '短时大量抓拍图像数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
          this.faceHasHover = false;
        },
        // 重复图像识别处理
        [faceEnum.aggregateEnums.repeatImage]: () => {
          this.staticMessage.captureExpectFace.label = '重复人脸图像';
          this.listObj.columns = this.columnsC([
            { title: '重复图像数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
          this.faceHasHover = false;
        },
        // 大小图关联正确检测
        [faceEnum.aggregateEnums.bigContactSmall]: () => {
          this.staticMessage.captureExpectFace.label = '关联错误';
          this.listObj.columns = this.columnsC([
            { title: '大小图关联错误数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
        },
        // 小图唯一人脸检测处理
        [faceEnum.aggregateEnums.smallOnly]: () => {
          this.staticMessage.captureExpectFace.label = '非唯一脸';
          this.listObj.columns = this.columnsC([
            { title: '小图非唯一人脸数量', key: 'count', width: 200 },
            { title: '操作', slot: 'action', width: 140, fixed: 'right' },
          ]);
        },
      };
      strategy[this.options.title]();
      this.visible = true;
      this.$emit('popUpGetData', option, this.nextGetData);
      this.$nextTick(() => {
        this.$refs.faceShebeiList.info(true);
      });
    },
    columnsC(array) {
      let columns = [
        { type: 'index', width: 70, title: '序号', fixed: 'left' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 200,
          fixed: 'left',
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
        },
        { title: '组织机构', key: 'orgCode', width: 200 },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          width: 200,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          width: 200,
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          slot: 'macAddr',
          width: 200,
        },
        { title: this.global.filedEnum.ipAddr, slot: 'ipAddr', width: 200 },
        {
          title: this.global.filedEnum.sbgnlx,
          slot: 'sbgnlxText',
          width: 200,
        },
        { title: this.global.filedEnum.sbdwlx, slot: 'sbdwlxText', width: 200 },
        { title: '位置类型', slot: 'positionTypeText', width: 200 },
        { title: '安装地址', key: 'address', width: 200 },
      ];
      return columns.concat(array);
    },
    // 不合格图片改变图像模式参数
    async unqualified(val) {
      this.modelTag = 0;
      this.$refs.tagView.curTag = 0;
      this.$nextTick(() => {
        this.searchData.deviceIds = [val.deviceId];
        this.$emit('startSearch', this.searchData);
      });
    },
    // 设备模式/图像模式切换
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
      if (this.modelTag == 1) {
        //   this.$nextTick(() => {
        this.searchData.deviceIds = [];
        //     this.$refs.faceShebeiList.info(true)
        //   })
      } else {
        //   this.$nextTick(() => {
        //     this.$emit('changePageSize', 20)
        //   })
      }
    },

    nextGetData() {
      this.staticMessage.totalFace.value = this.staticsObject.accessDataCount;
      this.staticMessage.captureExpectFace.value = this.staticsObject.abnormalCount;
      this.pageData.totalCount = this.staticMessage.captureExpectFace.value;
    },
    startSearch(val) {
      Object.assign(this.searchData, val);
      this.$emit('startSearch', this.searchData);
    },
    changePage(val) {
      this.$emit('changePage', val);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.$emit('changePageSize', val);
    },
    // 查看不合格原因
    uploadTips(item) {
      // this.$emit('getExceptMessage',this.options, item)
      const strategy = {
        // 图像抓拍时间准确性检测
        [faceEnum.aggregateEnums.accurrency]: () => {
          let messageText = {
            popUpTitle: '抓拍时间异常',
            nomalLabelList: [
              { name: '抓拍时间', value: item.logTime },
              { name: '入库时间', value: item.createTime },
              { name: '结论', value: '' },
            ],
          };
          this.$emit('imageExceptMessageShow', messageText, this.options, item);
        },
        // 图像上传及时性检测
        [faceEnum.aggregateEnums.timeliness]: () => {
          let messageText = {
            popUpTitle: '上传超时',
            nomalLabelList: [
              { name: '设备类型', value: '' },
              { name: '抓拍时间', value: item.logTime },
              { name: '入库时间', value: '' },
              { name: '时延', value: '' },
            ],
          };
          this.$emit('imageExceptMessageShow', messageText, this.options, item);
        },
        // 大图URL检测
        [faceEnum.aggregateEnums.bigPic]: () => {
          this.$emit('imageExceptMessageShow', '', this.options, item);
        },
        // 重复图像识别处理
        [faceEnum.aggregateEnums.repeatImage]: () => {
          this.$emit('repeatImageShow');
        },
        // 大小图关联正确检测
        [faceEnum.aggregateEnums.bigContactSmall]: () => {
          let messageText = {
            popUpTitle: '上传超时',
          };
          this.$emit('imageExceptMessageShow', messageText, this.options, item);
        },
        // 小图唯一人脸检测处理
        [faceEnum.aggregateEnums.smallOnly]: () => {
          let messageText = {
            popUpTitle: '上传超时',
          };
          this.$emit('imageExceptMessageShow', messageText, this.options, item);
        },
      };
      strategy[this.options.title]();
    },
    async querySumStatistics() {
      try {
        let params = {
          topicComponentId: this.options.filedData.topicComponentId,
        };
        let { data } = await this.$http.get(tasktracking.querySumStatistics, {
          params: params,
        });
        this.staticMessage.totalFace.value = data.data.accessDataCount;
        this.staticMessage.captureExpectFace.value = data.data.existingExceptionCount;
        // this.pageData.totalCount = data.data.existingExceptionCount
      } catch (err) {
        console.log(err);
      }
    },
    async exportExcel() {
      try {
        let params = {
          topicComponentId: this.options.filedData.topicComponentId,
        };
        let res = await this.$http.post(
          tasktracking.downloadFaceLibAbnormalDev,
          Object.assign(params, this.searchData),
          {
            responseType: 'blob',
          },
        );
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    totalListCount(val) {
      this.pageData.totalCount = val;
    },
  },
  components: {
    FaceDeviceSearch: require('@/components/face-device-search.vue').default,
    FaceList: require('@/components/face-list.vue').default,
    tagView: require('@/components/tag-view').default,
    faceShebeiList: require('../../components/face-shebei-list').default,
  },
};
</script>
