<template>
  <div class="config-modal">
    <ui-modal v-model="visible" :title="moduleAction.title" width="62.5rem">
      <slot></slot>
      <div class="instorage-config">
        <p class="base-text-color line-p">请确定入库策略：</p>
        <section class="section-box">
          <p class="mt-md mb-md circle-p">
            <span class="base-text-color">设备的字段为空时：</span>
            <RadioGroup class="ml-md" v-model="searchParams.fieldNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
          <p class="mb-md circle-p">
            <span class="base-text-color">设备的字段不为空时：</span>
            <RadioGroup v-model="searchParams.fieldNotNull">
              <Radio label="0">覆盖资产库值</Radio>
              <Radio label="1">保留资产库值</Radio>
            </RadioGroup>
          </p>
        </section>
        <p class="mb-md ml-sm base-text-color">以下字段需特殊处理：</p>
        <compare-base
          :property-list="newPropertyList"
          :default-params="!!defaultParams && 'storageParam' in defaultParams ? defaultParams.storageParam : ''"
          @selectionChange="selectionChange"
        >
          <template #left-title>
            <div class="color-title mb-sm">待选择待入库字段</div>
          </template>
          <template #right-title>
            <div class="color-title mb-sm">请确定入库策略</div>
          </template>
        </compare-base>
      </div>
      <template #footer>
        <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
        <Button class="plr-30" type="primary" :disabled="isDisabled" @click="saveQuery">开始入库</Button>
      </template>
    </ui-modal>
  </div>
</template>

<script>
export default {
  data() {
    return {
      searchParams: {
        storageParam: {},
        fieldNull: '',
        fieldNotNull: '',
      },
      newPropertyList: [],
      isDisabled: false,
      visible: false,
    };
  },
  created() {},
  mounted() {},
  methods: {
    updateParams(params) {
      Object.assign(this.searchParams, params);
    },
    async saveQuery() {
      this.$emit('saveQuery', this.moduleAction.type, this.searchParams);
      // try {
      //   let { data } = await this.$http.post(
      //     assetsSync.updateConfig,
      //     this.searchParams
      //   )
      //   this.$Message.success('配置成功')
      //   this.cancel()
      // } catch (err) {
      //   console.log(err)
      // }
    },
    cancel() {
      this.visible = false;
      this.searchParams = {};
      this.componentName = 'RulesConfig';
    },
    selectionChange(storageParam) {
      this.searchParams.storageParam = JSON.stringify(storageParam);
    },
    handleIsDefault() {
      this.searchParams.storageParam = this.defaultParams.storageParam;
      this.searchParams.fieldNull = this.defaultParams.fieldNull;
      this.searchParams.fieldNotNull = this.defaultParams.fieldNotNull;
    },
    handleStrageParams(selection) {
      this.searchParams.storageParam = JSON.stringify(
        selection.map((item) => {
          let obj = {};
          obj.fieldName = item.checkColumnName;
          obj.fieldRemark = item.checkColumnValue;
          obj.addType = item.addType;
          return obj;
        }),
      );
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.newPropertyList = this.$util.common.deepCopy(this.propertyList);
        this.defaultParams ? this.handleIsDefault() : null;
      }
    },
  },
  computed: {},
  props: {
    value: {},
    propertyList: {
      default: () => [],
    },
    moduleAction: {
      default: () => {
        return {
          title: '资产入库',
          type: 'instore',
        };
      },
    },
    // 默认配置
    defaultParams: {},
  },
  components: {
    CompareBase: require('../compare-base').default,
  },
};
</script>
<style lang="less" scoped>
.config-modal {
  .transfer-table {
    height: 500px;
  }
  .component-style {
    max-height: 800px;
    padding: 10px;
    overflow-y: scroll;
  }
  .ml-sp {
    margin-left: 22px;
  }
  .instorage-config {
    padding: 0 20px;
    height: 705px;
    .section-box {
      color: #fff;
    }
    .line-p {
      position: relative;
      padding-left: 15px;
      &::before {
        content: '';
        position: absolute;
        height: 16px;
        width: 6px;
        background: var(--color-primary);
        top: 3px;
        left: 0px;
      }
    }
    .circle-p {
      position: relative;
      padding-left: 15px;
      &::before {
        content: '';
        position: absolute;
        width: 6px;
        height: 6px;
        background: var(--color-primary);
        top: 8px;
        left: 0px;
      }
    }
  }
}
.color-title {
  color: var(--color-diplay-title);
}
</style>
