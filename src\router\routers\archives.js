/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from "@/layouts/basic-layout";
import ArchivesLayout from "@/layouts/archives-layout";

// 实名档案
const peopleArchivePath =
  "views/holographic-archives/one-person-one-archives/people-archive";

// 视频档案
const videoArchivePath =
  "views/holographic-archives/one-person-one-archives/video-archive";

// 车辆档案
const vehicleArchivePath =
  "views/holographic-archives/one-vehicle-one-archives/vehicle-archive";

// 设备档案
const deviceArchivePath =
  "views/holographic-archives/one-plane-one-archives/view-device/device-archive";

// 非机动车档案
const nonMotorVehicleArchivePath =
  "views/holographic-archives/one-vehicle-one-archives/non-motor-archive";

// 场所档案
const placeArchivePath = "views/holographic-archives/place-archives";

// 感知设备档案
const fellArchivePath =
  "views/holographic-archives/fell-device/device-archives";

export default [
  {
    path: "/people-archive",
    name: "people-archive",
    component: ArchivesLayout,
    redirect: "/people-archive/people-dashboard",
    meta: {
      title: "实名档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/people-archive/people-dashboard",
        name: "people-dashboard",
        component: (resolve) => import("@/" + peopleArchivePath + "/dashboard"),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/people-archive/people-profile",
        name: "people-profile",
        component: (resolve) => import("@/" + peopleArchivePath + "/profile"),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/people-archive/people-activity-track",
        name: "people-activity-track",
        component: (resolve) =>
          import("@/" + peopleArchivePath + "/activity-track"),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/people-archive/people-relationship-map",
        name: "people-relationship-map",
        component: (resolve) =>
          import("@/" + peopleArchivePath + "/relationship-map"),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/video-archive",
    name: "video-archive",
    component: ArchivesLayout,
    redirect: "/video-archive/video-dashboard",
    meta: {
      title: "视频档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/video-archive/video-dashboard",
        name: "video-dashboard",
        component: (resolve) => import("@/" + videoArchivePath + "/dashboard"),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/video-archive/video-profile",
        name: "video-profile",
        component: (resolve) => import("@/" + videoArchivePath + "/profile"),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/video-archive/video-activity-track",
        name: "video-activity-track",
        component: (resolve) =>
          import("@/" + videoArchivePath + "/activity-track"),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/video-archive/video-relationship-map",
        name: "video-relationship-map",
        component: (resolve) =>
          import("@/" + videoArchivePath + "/relationship-map"),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/vehicle-archive",
    name: "vehicle-archive",
    component: ArchivesLayout,
    redirect: "/vehicle-archive/vehicle-dashboard",
    meta: {
      title: "车辆档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/vehicle-archive/vehicle-dashboard",
        name: "vehicle-dashboard",
        component: (resolve) =>
          import("@/" + vehicleArchivePath + "/dashboard"),

        meta: {
          title: "车辆概览",
          tagShow: true,
        },
      },
      {
        path: "/vehicle-archive/vehicle-profile",
        name: "vehicle-profile",
        component: (resolve) => import("@/" + vehicleArchivePath + "/profile"),
        meta: {
          title: "车辆资料",
          tagShow: true,
        },
      },
      {
        path: "/vehicle-archive/vehicle-activity-track",
        name: "vehicle-activity-track",
        component: (resolve) =>
          import("@/" + vehicleArchivePath + "/activity-track"),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/vehicle-archive/vehicle-relationship-map",
        name: "vehicle-relationship-map",
        component: (resolve) =>
          import("@/" + vehicleArchivePath + "/relationship-map"),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/non-motor-archive",
    name: "non-motor-archive",
    component: ArchivesLayout,
    redirect: "/non-motor-archive/vehicle-profile",
    meta: {
      title: "非机动车档案",
      icon: "icon-shouye",
    },
    children: [
      //   {
      //     path: "/non-motor-archive/vehicle-dashboard",
      //     name: "non-motor-archive-dashboard",
      //     component: (resolve) =>
      //       import("@/" + nonMotorVehicleArchivePath + "/dashboard"),

      //     meta: {
      //       title: "车辆概览",
      //       tagShow: true,
      //     },
      //   },
      {
        path: "/non-motor-archive/vehicle-profile",
        name: "non-motor-archive-profile",
        component: (resolve) =>
          import("@/" + nonMotorVehicleArchivePath + "/profile"),
        meta: {
          title: "车辆资料",
          tagShow: true,
        },
      },
      {
        path: "/non-motor-archive/vehicle-activity-track",
        name: "non-motor-archive-activity-track",
        component: (resolve) =>
          import("@/" + nonMotorVehicleArchivePath + "/activity-track"),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      //   {
      //     path: "/non-motor-archive/vehicle-relationship-map",
      //     name: "non-motor-archive-relationship-map",
      //     component: (resolve) =>
      //       import("@/" + nonMotorVehicleArchivePath + "/relationship-map"),
      //     meta: {
      //       title: "关系图谱",
      //       tagShow: true,
      //     },
      //   },
    ],
  },
  /* {
    path: "/target-control1",
    name: "target-control1",
    component: BasicLayout,
    children: [
      {
        path: "/data-warehouse-new/static-library/personnel-thematic-database",
        name: "personnel-thematic-database",
        tabShow: true,
        parentName: "data-warehouse-new/static-library",
        component: (resolve) =>
          require([
            "@/views/data-warehouse-new/static-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "静态库-人员静态库",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/target-control2",
    name: "target-control2",
    component: BasicLayout,
    children: [
      {
        path: "/data-warehouse-new/static-library/vehicle-thematic-database",
        name: "vehicle-thematic-database",
        tabShow: true,
        parentName: "data-warehouse-new/static-library",
        component: (resolve) =>
          require([
            "@/views/data-warehouse-new/static-library/vehicle-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "静态库-车辆静态库",
          tagShow: true,
        },
      },
    ],
  }, */
  {
    path: "/target-control1",
    name: "target-control1",
    component: BasicLayout,
    children: [
      {
        path: "/target-control/static-library/personnel-thematic-database",
        name: "personnel-thematic-database",
        tabShow: true,
        parentName: "target-control/static-library",
        component: (resolve) =>
          require([
            "@/views/target-control/static-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "静态库-人员静态库",
        },
      },
    ],
  },
  {
    path: "/target-control2",
    name: "target-control2",
    component: BasicLayout,
    children: [
      {
        path: "/target-control/static-library/vehicle-thematic-database",
        name: "vehicle-thematic-database",
        tabShow: true,
        parentName: "target-control/static-library",
        component: (resolve) =>
          require([
            "@/views/target-control/static-library/vehicle-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "静态库-车辆静态库",
        },
      },
    ],
  },
  {
    path: "/target-control3",
    name: "target-control3",
    component: BasicLayout,
    children: [
      {
        path: "/target-control/static-library/sensory-thematic-database",
        name: "sensory-thematic-database",
        tabShow: true,
        parentName: "target-control/static-library",
        component: (resolve) =>
          require([
            "@/views/target-control/static-library/sensory-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "静态库-感知静态库",
        },
      },
    ],
  },
  {
    path: "/device-archive",
    name: "device-archive",
    component: ArchivesLayout,
    redirect: "/device-archive/device-dashboard",
    meta: {
      title: "设备档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/device-archive/device-dashboard",
        name: "device-dashboard",
        component: (resolve) => import("@/" + deviceArchivePath + "/dashboard"),

        meta: {
          title: "设备概览",
          tagShow: true,
        },
      },
      {
        path: "/device-archive/device-profile",
        name: "device-profile",
        component: (resolve) => import("@/" + deviceArchivePath + "/profile"),
        meta: {
          title: "设备资料",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/place-archive",
    name: "place-archive",
    component: ArchivesLayout,
    redirect: "/place-archive/place-dashboard",
    meta: {
      title: "场所档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/place-archive/place-dashboard",
        name: "place-dashboard",
        component: (resolve) => import("@/" + placeArchivePath + "/dashboard"),

        meta: {
          title: "场所概览",
          tagShow: true,
        },
      },
      {
        path: "/place-archive/place-relationship-map",
        name: "place-relationship-map",
        component: (resolve) =>
          import("@/" + placeArchivePath + "/relationship-map"),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
  {
    path: "/device-archives",
    name: "device-archives",
    component: ArchivesLayout,
    redirect: "/device-archives/device-dashboard",
    meta: {
      title: "设备档案",
      icon: "icon-shouye",
      show: true,
    },
    children: [
      {
        path: "/device-archives/device-dashboard",
        name: "device-dashboards",
        component: (resolve) => import("@/" + fellArchivePath + "/dashboard"),
        meta: {
          title: "",
        },
      },
    ],
  },
];
