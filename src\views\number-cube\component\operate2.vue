<template>
  <div class="relation-graph-header-tool">
    <div class="toll-border"></div>
    <div
      class=""
      @click="$emit('dataFilter', 'link')"
      v-show="!currentTab.relation"
    >
      <i class="iconfont icon-guanxishaixuan"></i>
      <div>关系筛选</div>
    </div>
    <div class="" @click="$emit('dataFilter', 'node')">
      <i class="iconfont icon-shitishaixuan"></i>
      <div>实体筛选</div>
    </div>
    <div class="toll-border"></div>
    <div
      @click="$emit('pathDeductionHandle')"
      v-show="!currentTab.relation && !currentTab.analysis"
    >
      <i class="iconfont icon-lujingtuiyan"></i>
      <div>路径推演</div>
    </div>
    <div
      @click="$emit('lianjiefenxi')"
      v-show="!currentTab.relation && !currentTab.analysis"
    >
      <i class="iconfont icon-lianjiefenxi"></i>
      <div>连接分析</div>
    </div>
    <div
      @click="$emit('communityAnalysisHandle')"
      v-show="!currentTab.relation && !currentTab.analysis"
    >
      <i class="iconfont icon-shequnfenxi"></i>
      <div>社群分析</div>
    </div>
    <div class="triangle" v-show="!currentTab.relation">
      <i class="iconfont" :class="currentLayout.icon"></i>
      <div>{{ currentLayout.label }}</div>
      <div class="menus">
        <div
          v-for="(item, index) in layoutList"
          :key="item.label"
          @click="layoutClick(item, index)"
          :class="{ 'layout-checked': item.label === currentLayout }"
        >
          <i class="iconfont" :class="item.icon"></i>{{ item.label }}
        </div>
      </div>
    </div>
    <div class="toll-border" v-show="!currentTab.relation"></div>
    <div @click="closeView" v-show="currentTab.relation || currentTab.analysis">
      <i class="iconfont icon-close-circle-fill1"></i>
      <div>关闭</div>
    </div>
  </div>
</template>
<script>
import { layoutTypeList } from "../js/layoutParamsDict.js";
export default {
  components: {},
  props: {
    currentTab: {
      type: Object,
      default: () => {
        return { relation: false, analysis: false };
      },
    },
  },
  data() {
    return {
      currentLayout: { label: "力导布局", icon: "icon-ico_fushebuju" },
      layoutList: layoutTypeList,
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    layoutClick(item, index) {
      this.currentLayout = item;
      this.$emit("layoutClick", item, index);
    },
    closeView() {
      if (this.currentTab.relation) {
        this.$emit("closeRelationView");
      } else {
        this.$emit("closeAnalysis");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.relation-graph-header-tool {
  display: flex;
  flex-wrap: nowrap;
  margin-left: 8px;
  .toll-border {
    border-left: 1px solid #d3d7de;
    height: 48px;
    padding: 0px !important;
  }
  & > div {
    text-align: center;
    margin-right: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    padding: 4px;
    cursor: pointer;
    i {
      color: #333333;
      font-size: 18px;
    }
  }
  & > .triangle {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    display: block;
    background: linear-gradient(-38deg, #888888 4%, transparent 4%);
    .menus {
      position: absolute;
      padding: 10px 0;
      width: 142px;
      background: #ffffff;
      box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
      border-radius: 4px;
      z-index: 99;
      margin-top: 4px;
      display: none;
      & > div {
        height: 30px;
        padding-left: 16px;
        line-height: 30px;
        cursor: pointer;
        font-size: 14px;
        text-align: left;
        color: rgba(0, 0, 0, 0.9);
        i > {
          margin-right: 10px;
        }
        &:hover {
          background: #2c86f8;
          color: #fff;
          i {
            color: #fff;
          }
        }
      }
    }
  }
  & > .triangle:hover {
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: auto;
    display: block;
    border-bottom-right-radius: 0px;
    background: linear-gradient(-38deg, #2c86f8 4%, rgba(44, 134, 248, 0.1) 4%);
    .menus {
      display: block;
      &:hover {
        display: block;
      }
    }
  }
  & > div:hover {
    background: rgba(44, 134, 248, 0.1);
    border-radius: 4px;
    color: #2c86f8;
    & > i {
      color: #2c86f8;
    }
  }
}
.icon-close-circle-fill1 {
  color: #ff2929 !important;
}
</style>
