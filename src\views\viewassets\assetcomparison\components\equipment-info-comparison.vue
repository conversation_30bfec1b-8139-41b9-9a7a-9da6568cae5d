<template>
  <div>
    <ui-modal
      class="equipment-info-comparison"
      :title="title"
      :footer-hide="true"
      v-model="visible"
      :styles="styles"
      @onCancel="visible = false"
    >
      <div class="info-header">
        <RadioGroup :value="comparisonField" @on-change="handleChange">
          <Radio label="all">
            <span>全部对比字段</span>
          </Radio>
          <Radio label="same">
            <span>相同字段</span>
          </Radio>
          <Radio label="difference">
            <span>差异字段</span>
          </Radio>
        </RadioGroup>
      </div>
      <div class="info-content">
        <div class="info-content-header" :class="!scrollWidth ? 'scrollWidth' : ''">
          <span> 字段名称 </span>
          <span v-if="resultData.left" class="sourceName">
            <span>数据源：{{ resultData.left.tableName }}</span>
            <span class="btn" v-show="resultData.left.tableType === '0'">
              <Button v-show="!leftEdit" type="text" class="btn" @click="() => (leftEdit = true)">编辑</Button>
              <Button v-show="leftEdit" type="text" @click="saveData('left')">保存</Button>
            </span>
          </span>
          <span v-if="resultData.right" class="sourceName">
            <span> 数据源：{{ resultData.right.tableName }}</span>
            <span class="btn" v-show="resultData.right.tableType === '0'">
              <Button v-show="!rightEdit" type="text" class="btn" @click="() => (rightEdit = true)">编辑</Button>
              <Button v-show="rightEdit" type="text" @click="saveData('right')">保存</Button>
            </span>
          </span>
        </div>
        <div class="info-content-container">
          <span>
            <template v-if="headerData.length">
              <div v-for="(e, i) in headerData" :key="i">
                <span v-show="e && e.name"> {{ e.name }} </span>
              </div>
            </template>
            <div v-else>暂无数据</div>
          </span>
          <span>
            <template v-if="Object.keys(tableDataLeftList).length">
              <div v-for="(val, key, index) in tableDataLeftList" :key="index">
                <div
                  v-show="!leftEdit && key !== 'inconsistentColumns' && key !== 'idLeft'"
                  :class="
                    tableDataLeftList.inconsistentColumns && tableDataLeftList.inconsistentColumns.includes(key)
                      ? 'difference'
                      : 'normal'
                  "
                >
                  {{ val ? val : '暂无数据' }}
                </div>
                <Input
                  v-show="leftEdit && key !== 'inconsistentColumns' && key !== 'idLeft'"
                  v-model="tableDataLeftList[key]"
                  class="input"
                  style="width: 300px"
                  :disabled="key === 'deviceIDLeft'"
                />
              </div>
            </template>
            <div v-else>暂无数据</div>
          </span>
          <span>
            <template v-if="Object.keys(tableDataRightList).length">
              <div v-for="(val, key, index) in tableDataRightList" :key="index">
                <div
                  v-show="!rightEdit && key !== 'inconsistentColumns' && key !== 'idRight'"
                  :class="
                    tableDataRightList.inconsistentColumns && tableDataRightList.inconsistentColumns.includes(key)
                      ? 'difference'
                      : 'normal'
                  "
                >
                  {{ val ? val : '暂无数据' }}
                </div>
                <Input
                  v-show="rightEdit && key !== 'inconsistentColumns' && key !== 'idRight'"
                  v-model="tableDataRightList[key]"
                  class="input"
                  style="width: 300px"
                  :disabled="key === 'deviceIDRight'"
                />
              </div>
            </template>
            <div v-else>暂无数据</div>
          </span>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import assetcomparison from '@/config/api/assetcomparison.js';
export default {
  props: {
    // 弹框标题
    title: {
      type: String,
      default: '',
    },
    // 对比字段
    comparisonFields: {
      type: Array,
      default: () => [],
    },
    tableDataLeft: {
      type: Object,
      default: () => ({}),
    },
    tableDataRight: {
      type: Object,
      default: () => ({}),
    },
    resultData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '7.32rem',
      },
      edit: false,
      headerData: [],
      tableDataLeftList: {},
      tableDataRightList: {},
      comparisonField: 'all', // 对比字段
      scrollWidth: false, // 判断是否有滚动条,
      headerDataClone: [],
      headerKeys: [],
      leftEdit: false,
      rightEdit: false,
    };
  },
  methods: {
    async init() {
      this.comparisonField = 'all';
      this.visible = true;
      this.leftEdit = false;
      this.rightEdit = false;
      this.headerData = this.comparisonFields;
      this.headerKeys = this.headerData.map((e) => e.key);
      this.tableDataLeftList = this.tableDataLeft;
      this.tableDataRightList = this.tableDataRight;
      this.tableDataLeftListClone = JSON.parse(JSON.stringify(this.tableDataLeftList));
      this.tableDataRightListClone = JSON.parse(JSON.stringify(this.tableDataRightList));
      this.headerDataClone = JSON.parse(JSON.stringify(this.headerData));
    },
    async saveData(type) {
      let params = {};
      if (type === 'left') {
        params = {
          ...this.tableDataLeftList,
          idLeft: this.tableDataLeftListClone.idLeft,
          inconsistentColumns: undefined,
          deviceIDLeft: undefined,
        };
      } else {
        params = {
          ...this.tableDataRightList,
          idRight: this.tableDataRightListClone.idRight,
          inconsistentColumns: undefined,
          deviceIDRight: undefined,
        };
      }
      let res = await this.$http.post(assetcomparison.updateDeviceInfo, {
        ...params,
      });
      if (res.data.code === 200) {
        this.$Message.success('编辑成功');
        this.visible = false;
        this.$emit('handleResetTableData');
      }
    },
    handleChange(val) {
      this.comparisonField = val;
      this.leftEdit = false;
      this.rightEdit = false;
      if (val === 'all') {
        return this[`${val}Handler`]();
      }
      this.fillterHandler(val);
    },
    allHandler() {
      this.tableDataLeftList = { ...this.tableDataLeftListClone };
      this.tableDataRightList = { ...this.tableDataRightListClone };
      this.headerData = [...this.headerDataClone];
    },
    // 过滤表数据
    filterTableDataHandler(fieldType, array) {
      const { inconsistentColumns } = array;
      let tableData = {};
      if (inconsistentColumns) {
        for (const [key, value] of Object.entries(array)) {
          if (
            (fieldType === 'difference' && inconsistentColumns.includes(key)) ||
            (fieldType === 'same' && !inconsistentColumns.includes(key))
          ) {
            tableData[key] = value;
          }
        }
        return { ...tableData, inconsistentColumns };
      } else {
        if (this.comparisonField === 'difference') {
          return {};
        } else {
          return array;
        }
      }
    },
    // 过滤表头
    filterTableHeaderHandler(fieldType, array) {
      const { inconsistentColumns } = this.tableDataLeftList;
      if (inconsistentColumns) {
        return array.map((e) => {
          if (
            (fieldType === 'same' && !inconsistentColumns.includes(e.key)) ||
            (fieldType === 'difference' && inconsistentColumns.includes(e.key))
          ) {
            return {
              ...e,
            };
          }
        });
      } else {
        // 无差异数据不存在-返回空
        if (this.comparisonField === 'difference') {
          return [];
        } else {
          return array;
        }
      }
    },
    fillterHandler(val) {
      this.headerData = this.filterTableHeaderHandler(val, this.headerDataClone).filter((e) => e);
      this.tableDataLeftList = this.filterTableDataHandler(val, this.tableDataLeftListClone);
      this.tableDataRightList = this.filterTableDataHandler(val, this.tableDataRightListClone);
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    tableDataRightList: {
      handler(val) {
        this.$nextTick(() => {
          this.scrollWidth = Object.keys(val).length > 16;
        });
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.equipment-info-comparison {
  .info-header {
    height: 54px;
    line-height: 54px;
    padding: 0 20px;
    border-top: 1px solid var(--border-modal-footer);
    margin-top: 15px;
    .ivu-radio-group {
      .ivu-radio-wrapper {
        margin-right: 30px;
      }
    }
  }
  .info-content {
    .info-content-header {
      height: 50px;
      line-height: 50px;
      display: flex;
      align-items: center;
      background: var(--bg-table-header-th);
      position: relative;
      width: 99.8%;
      > span {
        display: inline-block;
        width: 33.3%;
        color: var(--color-primary);
        font-size: 14px;
        padding-left: 20px;
        border-left: 1px solid var(--border-modal-footer);
      }
      > span:first-child {
        border-left: none;
      }
      .sourceName {
        position: relative;
      }
      .btn {
        .ivu-btn {
          position: absolute;
          color: #19d5f6;
          right: 20px;
          top: 10px;
        }
      }
    }
    .scrollWidth {
      width: 100%;
    }
    .info-content-container {
      max-height: 596px;
      overflow: hidden;
      overflow-y: auto;
      display: flex;
      > span {
        display: inline-block;
        width: 33.3%;
        font-size: 14px;
        padding-left: 20px;
        height: auto;
        line-height: 40px;
        color: var(--color-content);
        border-left: 1px solid var(--border-modal-footer);
        .difference {
          color: var(--color-warning);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .normal {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      > span:first-child {
        border-left: none;
      }
    }

    .info-content-container:hover {
      background: var(--bg-content);
    }
  }
}
/deep/.ivu-modal {
  .ivu-modal-body {
    padding: 0 !important;
  }
}
</style>
