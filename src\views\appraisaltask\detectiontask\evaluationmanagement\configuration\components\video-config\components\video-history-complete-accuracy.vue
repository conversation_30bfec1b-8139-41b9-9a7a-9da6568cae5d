<template>
  <div class="video-container">
    <common-form
      :label-width="200"
      :form-data="formData"
      :form-model="formModel"
      ref="commonForm"
      :moduleAction="moduleAction"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
    >
      <div slot="waycondiction" class="mt-xs" v-if="formData.detectMode !== '3'">
        <p>
          <span class="base-text-color">检测条件：</span>
          <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
            >设备可用</Checkbox
          >
        </p>
        <p class="color-failed">说明：系统只检测满足过滤条件的设备，未选择过滤条件，检测全部设备。</p>
      </div>
    </common-form>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="200">
      <FormItem label="普通设备历史录像" v-if="!isImportant">
        <Input class="width-input" placeholder="请输入" v-model="formData.videoGeneralDay" />
        <span class="base-text-color ml-xs">天</span>
      </FormItem>
      <FormItem label="重点设备历史录像">
        <Input class="width-input" placeholder="请输入" v-model="formData.videoImportDay" />
        <span class="base-text-color ml-xs">天</span>
      </FormItem>
      <FormItem label="包含当天的历史录像">
        <RadioGroup v-model="formData.queryNow">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="有效缺少时长">
        <span class="font-white mr-xs">>=</span>
        <InputNumber class="width-input" placeholder="请输入" v-model.number="formData.missSecond"></InputNumber>
        <span class="font-white ml-xs">秒</span>
        <p class="font-red">说明：单次缺少不足有效时长，则认为是连续录像！</p>
      </FormItem>
      <FormItem label="更新历史录像完整状态">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="是否需要复检">
        <RadioGroup v-model="isRecheck" @on-change="handleRecheckValChange">
          <Radio label="1">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup>
        <span class="color-failed vt-middle">(备注：复检时不会取缓存结果！)</span>
      </FormItem>
      <FormItem label="复检设备" v-if="isRecheck === '1'">
        <RadioGroup v-model="formData.reinspect.model">
          <Radio label="UNQUALIFIED">检测不合格设备</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="复检次数" prop="maxCount" v-if="isRecheck === '1'">
        <InputNumber
          v-model.number="formData.reinspect.maxCount"
          class="mr-xs"
          :max="5"
          :min="1"
          :precision="0"
        ></InputNumber>
        <span class="base-text-color">次</span>
      </FormItem>
      <FormItem label="复检时间" v-if="isRecheck === '1'" prop="scheduleType">
        <RadioGroup v-model="formData.reinspect.scheduleType" @on-change="handleRecheckTimeChange">
          <Radio label="INTERVAL">时间间隔</Radio>
          <Radio label="CUSTOMIZE_TIME">自定义时间</Radio>
        </RadioGroup>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'INTERVAL'">
          <span class="base-text-color">检测结束</span>
          <InputNumber class="ml-md mr-xs" v-model.number="formData.reinspect.scheduleValue"></InputNumber>
          <Select class="width-mini" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="1">时</Option>
            <Option :value="2">分</Option>
          </Select>
          <span class="base-text-color ml-md">后，开始复检</span>
        </div>
        <div class="mt-sm" v-if="formData.reinspect.scheduleType === 'CUSTOMIZE_TIME'">
          <Select class="width-mini mr-sm" transfer v-model="formData.reinspect.scheduleKey">
            <Option :value="3">当天</Option>
            <Option :value="4">第二天</Option>
            <Option :value="5">第三天</Option>
          </Select>
          <TimePicker
            :value="formData.reinspect.scheduleValue"
            transfer
            format="HH:mm"
            placeholder="请选择"
            class="width-xs"
            @on-change="handleChangeTime"
          ></TimePicker>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  name: 'video-history-complete-accuracy',
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const validateCheduleType = (rule, value, callback) => {
      if (!this.formData.reinspect.scheduleType) {
        callback(new Error('请选择复检时间'));
      }
      if (
        this.formData.reinspect.scheduleType &&
        (!this.formData.reinspect.scheduleValue || !this.formData.reinspect.scheduleKey)
      ) {
        callback(new Error('请输入时间'));
      }
      callback();
    };
    const validateDetectionModePass = (rule, value, callback) => {
      !value.length ? callback(new Error('请选择检测内容')) : callback();
    };
    const validateMaxCount = (rule, value, callback) => {
      if (!this.formData.reinspect.maxCount) {
        callback(new Error('请输入复检次数'));
      }
      callback();
    };
    return {
      ruleCustom: {
        detectionMode: [{ validator: validateDetectionModePass, trigger: 'change', required: true }],
        scheduleType: {
          validator: validateCheduleType,
          required: true,
          trigger: 'change',
        },
        maxCount: [
          {
            validator: validateMaxCount,
            required: true,
            trigger: 'change',
          },
        ],
      },
      formData: {
        detectMode: '1',
        selectType: 0,
        deviceQueryForm: {
          detectPhyStatus: '0',
        },
        videoGeneralDay: null,
        videoImportDay: null,
        missSecond: null,
        queryNow: 0,
        isUpdatePhyStatus: 0,
      },
      isRecheck: '2', //是否复检
    };
  },
  computed: {
    isImportant() {
      return this.indexType === 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN';
    },
  },
  methods: {
    async handleSubmit() {
      const valid = await this.$refs['modalData'].validate();
      if (!valid) {
        this.$Message.error('请将配置信息填写完整！');
        return;
      }
      return this.$refs['commonForm'].handleSubmit();
    },
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    handleChangeTime(time) {
      this.formData.reinspect.scheduleValue = time;
    },
    handleRecheckTimeChange() {
      this.formData.reinspect.scheduleValue = null;
    },

    setRecheck() {
      this.isRecheck = this.formData.reinspect ? '1' : '2';
    },
    handleRecheckValChange(val) {
      if (val === '2') {
        this.formData.reinspect = null;
      } else {
        this.$set(this.formData, 'reinspect', {
          model: 'UNQUALIFIED',
          type: 'PROGRAM_BATCH',
          scheduleType: 'INTERVAL',
          scheduleKey: 2,
          plan: '2',
          scheduleValue: null,
          maxCount: 1,
        });
      }
    },
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
          };
          if (this.formData.reinspect) {
            this.formData.reinspect.maxCount = this.formData.reinspect.maxCount || null;
          }
          this.formData.missSecond = this.formData.missSecond || null;
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.setRecheck();
      },
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.video-container {
}
</style>
