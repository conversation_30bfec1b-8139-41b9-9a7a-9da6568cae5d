import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export const iconStaticsList = [
  {
    name: '实际检测设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'actualNum',
  },
  {
    name: '合格设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'qualifiedNum',
  },
  {
    name: '不合格设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'unqualifiedNum',
  },
  {
    name: '新增指标名称',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'qualified',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'input',
    key: 'errorMessage',
    label: '不合格原因',
    placeholder: '请输入不合格原因',
  },
];

export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    minWidth: 120,
    tooltip: true,
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    align: 'left',
    tooltip: true,
    minWidth: 80,
  },
  {
    title: '设备标签',
    slot: 'tagNames',
    minWidth: 100,
    tooltip: true,
  },
  {
    title: '统计结果',
    slot: 'qualifiedName',
    minWidth: 100,
    tooltip: true,
  },
  {
    title: '原因',
    key: 'errorMessages',
    minWidth: 160,
    tooltip: true,
  },
  {
    title: '检测时间',
    key: 'createTime',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'option',
    align: 'left',
    tooltip: true,
    width: 110,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];
