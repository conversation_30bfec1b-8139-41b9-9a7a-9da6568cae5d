<template>
  <ui-modal v-model="visible" title="自动分析配置" :styles="styles">
    <div class="note mb-lg">
      关键词匹配分析模型<span class="tip"
        >(说明：将设备的待分析字段和系统标签库模糊匹配，设备和匹配成功的标签关联。)</span
      >
    </div>
    <Form :model="formData" :label-width="80">
      <FormItem label="分析计划">
        <Tag
          size="medium"
          class="mr-sm mb-sm"
          v-for="(item, index) in analyzePropertyList"
          :closable="true"
          :key="index"
          :name="item.id"
          @on-close="cancelItem(item, index)"
          >{{ item.propertyNameLabel }}</Tag
        >
        <Button @click="customSearchData">
          <i class="icon-font icon-tianjia f-12"></i>
          <span class="inline vt-middle ml-sm">新增</span>
        </Button>
      </FormItem>
      <!-- <FormItem label="分析计划">
        <Select class="plan" type="datetime" v-model="formData.azsj" clearable placeholder="请选择分析计划">
          <Option v-for="(item, index) in analysisList" :key="index"></Option>
          <Option :value="1">测试</Option>
        </Select>
      </FormItem>
      <FormItem label="自动分析">
        <i-switch v-model="formData.autoAnalysis"></i-switch>
      </FormItem> -->
    </Form>
    <template #footer>
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="query" :loading="updateLoading" class="plr-30">确 定</Button>
    </template>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :checkbox-list="allPropertyList"
      :default-checked-list="defaultCheckedList"
      :field-name="fieldName"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </ui-modal>
</template>
<script>
import taganalysis from '@/config/api/taganalysis';
export default {
  props: {
    value: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '4.68rem',
      },
      formData: {
        analyzeProperty: [],
      },
      analyzePropertyList: [],
      customSearch: false,
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      contentStyle: {
        height: '3.125rem',
      },
      allPropertyList: [],
      defaultCheckedList: [],
      fieldName: {
        id: 'propertyName',
        value: 'propertyColumn',
      },
      switchLoading: false,
      updateLoading: false,
    };
  },
  created() {},
  methods: {
    async getPropertyList() {
      try {
        let { data } = await this.$http.post(taganalysis.queryPropertySearchList, {
          propertyType: '1',
        });
        this.allPropertyList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async initAnalyzeList() {
      try {
        const res = await this.$http.get(taganalysis.queryConfigList);
        this.analyzePropertyList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    cancelItem(item, index) {
      this.analyzePropertyList.splice(index, 1);
    },
    customSearchData() {
      this.defaultCheckedList = this.confirmAnalysisIds;
      this.customSearch = true;
    },
    confirmFilter(propertyList) {
      this.analyzePropertyList = propertyList.map((row) => {
        return {
          propertyNameLabel: row.propertyColumn,
          propertyName: row.propertyName,
          id: row.id,
        };
      });
      this.customSearch = false;
    },
    async query() {
      try {
        this.updateLoading = true;
        const propertyList = this.analyzePropertyList.map((row) => {
          return row.propertyName;
        });
        await this.$http.post(taganalysis.updatePropertyTagAnalysis, propertyList);
        const res = await this.$http.get(taganalysis.startTagAnalysis);
        this.$Message.success(res.data.msg);
        this.visible = false;
      } catch (err) {
        console.log(err);
      } finally {
        this.updateLoading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.getPropertyList();
        this.initAnalyzeList();
      }
    },
  },
  computed: {
    confirmAnalysisIds() {
      return this.analyzePropertyList.map((item) => {
        return item.propertyName;
      });
    },
  },
  components: {
    UiModal: require('@/components/ui-modal.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 20px 50px;
}
.note {
  font-size: 14px;
  color: var(--color-content);
  font-weight: 900;
  padding: 15px 20px;
  background-color: var(--bg-table-header-th);
  .tip {
    margin-left: 10px;
    color: var(--color-tips);
    font-size: 12px;
  }
}
.plan {
  width: 380px;
}
</style>
