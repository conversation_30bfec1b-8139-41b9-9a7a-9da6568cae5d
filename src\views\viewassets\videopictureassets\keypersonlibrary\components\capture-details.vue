<template>
  <ui-modal class-name="capture" v-model="visible" title="抓拍详情" footer-hide width="95.1rem">
    <div class="capture-details">
      <capture-details-left :personinfo="personinfo" :tag-list="tagList"></capture-details-left>
      <div class="capture-details-right auto-fill">
        <div class="capture-details-right-top">
          <label>
            抓拍总量：<span style="color: var(--color-sub-title)">{{ countDatas ? countDatas.count || 0 : 0 }}</span>
          </label>
          <label>
            准确性存疑数量：<span style="color: var(--color-failed)">{{
              countDatas ? countDatas.doubtfulCount || 0 : 0
            }}</span>
          </label>
          <label>
            上传超时：<span style="color: var(--color-failed)">{{
              countDatas ? countDatas.timeOutCount || 0 : 0
            }}</span>
          </label>
        </div>
        <div class="capture-details-right-search">
          <ui-label class="inline" label="抓拍时间">
            <DatePicker
              class="width-md"
              type="datetime"
              v-model="searchData.startTime"
              placeholder="请选择开始时间"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
              :options="startTimeOption"
            ></DatePicker>
            <span class="horizontalbar">—</span>
            <DatePicker
              class="width-md"
              type="datetime"
              v-model="searchData.endTime"
              placeholder="请选择结束时间"
              @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
              :options="endTimeOption"
            ></DatePicker>
          </ui-label>
          <ui-label class="inline ml-lg vt-middle" label="抓拍设备">
            <select-camera
              @pushCamera="pushCamera"
              :device-ids="searchData.deviceIds"
              :camera-type="[2]"
            ></select-camera>
          </ui-label>
          <!-- <ui-label class="inline ml-lg" label="抓拍类型" :width="70">
            <Select
              class="width-md"
              placeholder="请选择"
              clearable
              v-model="searchData.componentCode"
            >
              <Option
                v-for="(typeItem, tyIndex) in captureTypes"
                :key="tyIndex"
                :value="typeItem.componentCode"
              >{{ typeItem.msg }}
              </Option
              >
            </Select>
          </ui-label> -->
          <div class="inline ml-lg">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="clear">重置</Button>
          </div>
        </div>
        <div class="capture-details-right-content auto-fill" v-ui-loading="{ loading: loading, tableData: tableData }">
          <div class="group-unaggregated">
            <div class="group-item" v-for="(item, index) in tableData" :key="index">
              <div class="group-left">
                <div class="group-img" @click="lookScence(index)">
                  <ui-image :src="item.facePath" />
                  <span class="percent">{{ item.similarity ? item.similarity + '%' : 0 }}</span>
                  <p class="shadow-box" title="查看检测结果">
                    <i
                      class="icon-font icon-yichang search-icon mr-xs"
                      @click.stop="checkReason(item)"
                    ></i>
                  </p>
                  <i v-if="item.errIcon" :class="['icon-font', 'seal-icon', item.errIcon]"></i>
                </div>
                <div class="group-message">
                  <p class="mb-sm" :title="`抓拍时间：${item.logTime}`">
                    <i class="icon-font icon-shijian"></i>
                    <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.logTime || '暂无' }}</span>
                  </p>
                  <p :title="`抓拍地址：${item.address ? item.adress : ''}`">
                    <i class="icon-font icon-dizhi"></i>
                    <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.address || '暂无' }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </div>
    <look-scene v-model="visibleScence" :img-list="imgList" :view-index="viewIndex"></look-scene>
    <export-disqualify
      v-model="disqualifyShow"
      :disqualify-item="disqualifyItem"
      :activeBtn="activeBtn"
      :istasktracking="false"
      :title="title"
    >
      <template #unqualifiedheader>
        <div class="header">
          <RadioGroup
            v-if="errLength.length > 1"
            v-model="activeBtn"
            type="button"
            @on-change="handleChange"
            style="display: flex"
          >
            <Radio :class="activeBtn === '8009' ? 'active' : ''" label="8009">上传超时</Radio>
            <Radio :class="activeBtn === '10001' ? 'active' : ''" label="10001">轨迹照片准确性存疑</Radio>
          </RadioGroup>
          <p v-else>
            {{ activeBtn === '10001' ? '轨迹照片准确性存疑' : '上传超时' }}
          </p>
        </div>
      </template>
    </export-disqualify>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import persontype from '../../keypersonlibrary/persontype.js';

export default {
  props: {
    title: {
      type: String,
      default: '异常原因',
    },
  },
  data() {
    return {
      visible: false,
      tableData: [],
      searchData: {
        deviceIds: [],
        componentCode: '',
        startTime: null,
        endTime: null,
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      visibleScence: false,
      imgList: [],
      viewIndex: 0,
      personId: '',
      personinfo: {},
      captureTypes: [],
      disqualifyShow: false,
      disqualifyItem: {},
      activeBtn: '',
      isLibrary: true,
      countDatas: {},
      tagList: [],
      errLength: 0,
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    async init(id, islibrary = true) {
      this.isLibrary = islibrary;
      this.visible = true;
      this.personId = id;
      this.copySearchDataMx(this.searchData);
      // await this.getPersonIfo()
      await this.getPersonCounts();
      await this.getCaptureType();
      await this.initList();
    },
    // 查看大图
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
    // 查看异常原因
    checkReason(item) {
      this.disqualifyShow = true;
      if (item.errorMsgList.length < 2) {
        this.activeBtn = item.errorMsgList[0]['componentCode'];
      } else {
        this.activeBtn = '8009';
      }
      this.disqualifyItem = item;
      this.errLength = this.disqualifyItem.errorMsgList;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.initList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    // 选择设备
    pushCamera(list) {
      this.searchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    // 获取重点人员信息
    async getPersonIfo() {
      try {
        let res = await this.$http.get(equipmentassets.importantPersonDetail, {
          params: { id: this.personId },
        });
        this.personinfo = res.data.data;
        const personTypes = this.personinfo.personTypeText.split(',');
        this.tagList = persontype.filter((item) => {
          return personTypes.includes(item.tagName);
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 获取重点人员统计数据
    async getPersonCounts() {
      try {
        const url = !this.isLibrary ? 'queryPersonLibTaskGatherStatistics' : 'queryPersonLibGatherStatistics';
        let res = await this.$http.post(equipmentassets[url], {
          topicComponentId: null,
          importantPersonId: this.personId,
        });
        this.countDatas = res.data.data;
        this.personinfo = res.data.data.importantPersonArchivesVo;
        const personTypes = this.personinfo.archivesTagText.split(',');
        this.tagList = persontype.filter((item) => {
          return personTypes.includes(item.tagName);
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 获取抓拍类型
    async getCaptureType() {
      try {
        let res = await this.$http.get(equipmentassets.queryPersonLibTaskGatherPullData);
        this.captureTypes = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.initList();
    },
    async initList() {
      this.loading = true;
      try {
        const url = !this.isLibrary ? 'queryPersonLibTaskGatherDetail' : 'queryPersonLibGatherDetail';
        this.searchData.importantPersonId = this.personId;
        let res = await this.$http.post(equipmentassets[url], this.searchData);
        this.pageData.totalCount = res.data.data.total;
        // if (!this.isLibrary) {
        //   this.tableData = res.data.data.entities.map((item) => {
        //     item.similarity = (item.similarity * 100).toFixed(2);
        //     return item;
        //   });
        //   return;
        // }
        this.tableData = res.data.data.entities.map((item) => {
          if (item.errorMsgList) {
            const errs = item.errorMsgList.map((errItem) => errItem.errorMsg);
            if (errs.length) {
              if (errs.length > 1) {
                item.errIcon = 'icon-cunyichaoshi';
              } else {
                item.errIcon = errs[0] === '上传超时' ? 'icon-chaoshi' : 'icon-cunyi';
              }
            } else {
              item.errIcon = '';
            }
          }
          item.similarity = (item.similarity * 100).toFixed(2);
          return item;
        });
        this.imgList = this.tableData.map((row) => row.scenePath);
        this.loading = false;
      } catch (err) {
        console.log('err', err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    handleChange(val) {
      this.activeBtn = val;
    },
  },
  watch: {},
  components: {
    // TagsMore: require("@/components/tags-more").default,
    uiImage: require('@/components/ui-image').default,
    SelectCamera: require('@/components/select-camera.vue').default,
    CaptureDetailsLeft: require('./capture-details-left.vue').default,
    ExportDisqualify: require('@/views/datagovernance/tasktracking/importantperson/popup/export-disqualify.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.capture {
  > .ivu-modal {
    > .ivu-modal-content {
      > .ivu-modal-body,
      > .ivu-modal-header {
        padding: 0;
      }
    }
  }
}

.capture-details {
  display: flex;
  width: 100%;
  height: 800px;

  &-right {
    flex: 1;
    height: 100%;
    &-top {
      padding: 50px 20px 18px;
      font-size: 14px;
      color: var(--color-content);
      border-bottom: 1px solid var(--border-modal-footer);
      label {
        margin-right: 58px;
      }
    }
    &-search {
      padding: 20px;
    }
    &-content {
      position: relative;
      padding: 0 20px;
      .group-unaggregated {
        overflow-y: auto;
        padding-top: 10px;
        padding-right: 10px;
        display: flex;
        flex-wrap: wrap;
        .group-item {
          width: calc(calc(100% - 70px) / 7);
          margin: 0 5px 10px;
          padding: 10px 0;
          overflow: hidden;
          position: relative;
          background: var(--bg-info-card);
          border: 1px solid var(--border-info-card);
          border-radius: 4px;
        }
        .group-left {
          .group-img {
            position: relative;
            width: 167px;
            height: 167px;
            cursor: pointer;
            margin: 0 auto 10px;
            img {
              width: 100%;
              height: 100%;
            }
            .shadow-box {
              height: 28px;
              width: 100%;
              background: var(--bg-info-card-option);
              position: absolute;
              bottom: 0;
              display: none;
              padding-left: 10px;
              z-index: 10;
              > i {
                color: #2B84E2;
              }
            }
            &:hover {
              .shadow-box {
                display: block;
              }
            }
            .percent {
              position: absolute;
              top: 1px;
              left: 1px;
              display: inline-block;
              padding: 0 2px;
              min-width: 32px;
              text-align: center;
              background: var(--color-warning);
              color: #ffffff;
              z-index: 99;
            }
            .seal-icon {
              position: absolute;
              bottom: -14px;
              right: 1px;
              font-size: 60px;
              color: var(--color-failed);
              z-index: 99;
            }
          }
          .group-message {
            margin: 0 auto;
            padding: 0 15px;
            color: var(--color-label);
            .group-text {
              width: 124px;
              color: var(--color-content);
            }
            i {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}
.horizontalbar {
  margin: 0 9px;
  color: #1b82d2;
}
@{_deep} .ivu-modal {
  .title {
    z-index: 3;
  }
}
// @{_deep} .ivu {
//   &-tag {
//     &:hover {
//       background: var(--color-primary) !important;
//     }
//   }
// }
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.header {
  padding: 20px 0 0;
  margin-bottom: 20px;
  p {
    font-size: 16px;
    color: var(--color-content);
  }
}
.active {
  color: var(--color-content) !important;
  background: var(--color-primary) !important;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  .flex;
  min-width: 96px;
  width: auto !important;
  height: 36px;
  background: transparent;
  color: #56789c;
  border: 1px solid #1b82d2;
}
@{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
  background: var(--color-primary) !important;
}
</style>
