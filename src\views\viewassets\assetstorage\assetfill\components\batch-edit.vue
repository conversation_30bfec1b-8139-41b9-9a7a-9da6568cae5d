<template>
  <ui-modal v-model="visible" title="批量修改" :styles="styles" @query="query">
    <div class="mb-lg">
      <!-- 摄像机功能类型 -->
      <ui-label class="inline mb-sm mr-lg" :label="global.filedEnum.sbgnlx">
        <Select
          class="width-md"
          v-model="sbgnlx"
          clearable
          multiple
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
        >
          <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mb-sm mr-lg" label="摄像机能力集">
        <Select
          v-model="nlj"
          class="width-md"
          multiple
          filterable
          clearable
          :max-tag-count="1"
          :placeholder="`请选择摄像机能力集`"
        >
          <Option v-for="(item, index) in sq_nlj" :key="index" :label="item.dataValue" :value="`${item.dataKey}`">
          </Option>
        </Select>
      </ui-label>
    </div>
    <div class="over-flow base-text-color mb-lg">
      待修改的设备：
      <span class="fr"
        >已选择
        <span class="total-count">{{ batchData.length ? tableData.length : pageData.totalCount }}</span>
        个</span
      >
    </div>
    <div class="table-module auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltip-type">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
      </ui-table>
      <ui-page
        v-if="batchData.length === 0"
        class="page"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
    <template #footer>
      <Button @click="visible = false"> 取 消</Button>
      <Button class="ml-lg" type="primary" :loading="saveLoading" @click="query"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    batchData: {},
    searchParams: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '7rem',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      sbgnlx: [],
      nlj: [],
      loading: false,
      tableData: [],
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          width: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 110,
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
      ],
      saveLoading: false,
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        let { url, params } = this.getParams();
        let res = await this.$http.post(url, params);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    // 已入库
    getParams() {
      let url = '',
        params = {};
      params = this.searchParams;
      url = equipmentassets.getPageDeviceList;
      return {
        url,
        params,
      };
    },
    async query() {
      try {
        this.saveLoading = true;
        let params = {
          updateSbgnlx: this.sbgnlx.join('/'),
          updateNlj: this.nlj.join('/'),
        };
        if (this.batchData.length === 0) {
          Object.assign(params, this.searchParams);
        } else {
          Object.assign(params, {
            ids: this.batchData.map((row) => row.id),
          });
        }
        const res = await this.$http.post(equipmentassets.batchUpdate, params);
        this.$Message.success(res.data.msg);
        this.visible = false;
        this.$emit('init');
      } catch (err) {
        console.log(err);
      } finally {
        this.saveLoading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        if (this.batchData.length) {
          this.tableData = this.batchData;
        } else {
          this.init();
        }
      }
      this.visible = val;
    },
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      sq_nlj: 'algorithm/sq_nlj',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  height: 600px;
  display: flex;
  flex-direction: column;
}
.tooltip-type {
  width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.total-count {
  color: cyan;
}
</style>
