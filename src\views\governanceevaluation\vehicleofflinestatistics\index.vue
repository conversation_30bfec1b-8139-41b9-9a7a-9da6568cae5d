<template>
  <div class="vehicleofflinestatistics-container auto-fill">
    <statistics indexId="3013" :switchData="switchData">
      <template #title> 车辆卡口离线情况统计 </template>
    </statistics>
  </div>
</template>

<script>
export default {
  name: 'vehicleofflinestatistics',
  components: {
    statistics: require('@/views/governanceevaluation/videostreamstatistics/components/statistics.vue').default,
  },
  props: {},
  data() {
    return {
      switchData: [
        { label: '全量车辆卡口', value: 0 },
        { label: '重点车辆卡口', value: 1 },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.vehicleofflinestatistics-container {
  width: 100%;
  height: 100%;
  background: var(--bg-content);
  padding: 10px 20px 20px 20px;
}
</style>
