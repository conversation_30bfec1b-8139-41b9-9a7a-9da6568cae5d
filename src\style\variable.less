@_deep: ~'>>>';
// bg
@bg_041129: #041129;
@bg_011B37: #011b37;

@bg-darkblue-block: #041129; //系统背景色
@bg-menu-hover: rgba(43, 132, 226, 0.5); //菜单鼠标放入hover
@bg-menu-active: linear-gradient(270deg, rgba(19, 122, 185, 0) 0%, #2b84e2 100%); //菜单选中渐变色
@bg-menu-home: #073250; //菜单首页背景色
@bg-menu-top: linear-gradient(270deg, #11a3fb 0%, #09284d 100%); //菜单顶部渐变色
@bg-blue-primary: #0e2246;
@bg-blue-block: #071b39;
@bg-table-block: #041939;
@bg-blue-row: #062042;
@bg-table-header: #092955;
@input-inner-bg: #02162b;
// border
@border-input-normal: #10457e; // 输入框
@border-active: #27a4eb;
// font
@font-color-link: #2b84e2;
@font-color-icon: #22a8df;
@font-color-grey: #8d9cb1;
@font-color-white: #fff;
@font-color-special: #24edde;
@font-table-action: #2b84e2;

@color-success: #0e8f0e;
@color-failed: #bc3c19;
@color-wait: #979898;
@color-b77a2a: #b77a2a;
@color-17a8a8: #babdbd;
@color-D66418: #d66418;
@color-787878: #787878;
@color-D9B916: #d9b916;

@color-other: #1a82be;
@color-tip: #24e0ef;

@font-face {
  font-family: UniDreamLED;
  src: url('/font/UnidreamLED.ttf');
}
@font-face {
  font-family: YouSheBiaoTiHei;
  src: url('/font/YouSheBiaoTiHei.ttf');
}

.bg-color-grey {
  background-color: @color-wait;
}

.bg-b77a2a {
  background-color: @color-success;
}

.bg-D66418 {
  background-color: @color-D66418;
}
.bg-787878 {
  background-color: @color-787878;
}
.bg-D9B916 {
  background-color: @color-D9B916;
}

.bg-17a8a8 {
  background-color: @color-failed;
}

.bg-other {
  background-color: @color-other;
}

.font-D66418 {
  color: @color-D66418;
}

.font-green {
  color: @color-success;
}
.font-D9B916 {
  color: @color-D9B916;
}

.font-grey {
  color: @font-color-grey;
}

.font-white {
  color: @font-color-white;
}

.font-icon-color {
  color: @font-color-icon;
}

.font-table-action {
  color: @font-table-action;
}
