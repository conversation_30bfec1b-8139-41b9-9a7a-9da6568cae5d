<template>
  <div class="deal">
    <Form :label-width="80">
      <FormItem label="处理结果" required>
        <RadioGroup v-model="form.finishResult">
          <Radio label="已处理"></Radio>
          <!-- <Radio label="部分处理"
                 class="ml-lg"></Radio> -->
          <Radio label="不处理" class="ml-lg"></Radio>
        </RadioGroup>
        <span class="undeal-text" v-show="form.finishResult != '已处理'">（提示：无法处理的设备请提交报备流程！）</span>
      </FormItem>
      <FormItem label="原因反馈" v-show="form.finishResult != '已处理'" required>
        <Select v-model="form.feedbackResult" placeholder="请选择原因" class="form-item-width1">
          <Option v-for="item in reasonList" :value="item.value" :key="item.value">{{ item.label }} </Option>
        </Select>
      </FormItem>
      <FormItem label="处理说明" required>
        <Input
          v-model="form.finishSituation"
          class="remark"
          type="textarea"
          :autosize="{ minRows: 3, maxRows: 3 }"
          placeholder="请填写处理说明"
        ></Input>
      </FormItem>
    </Form>
    <!-- <ui-label class="flex ml-sm"
              label="上传图片"
              align="left">
      <upload-img :multiple="true"
                  @successPut="successPut"
                  ref="upload">
      </upload-img>
    </ui-label> -->
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      form: {
        finishResult: '已处理',
        finishSituation: '',
        feedbackResult: '',
      },
      reasonList: [
        { value: 'dismantle', label: '设备拆除' },
        { value: 'repair', label: '设备维修' },
        { value: 'other', label: '其他' },
      ],
    };
  },
  created() {},
  updated() {
    //以前isEmitForm = this.form.finishSituation !== '' && this.form.finishResult !== ''
    //处理说明不为空+处理结果已处理 || 处理说明不为空+处理结果不处理+原因反馈不为空
    let isEmitForm =
      this.form.finishSituation !== '' &&
      (this.form.finishResult == '已处理' || (this.form.finishResult == '不处理' && this.form.feedbackResult !== ''));
    if (isEmitForm) {
      this.$emit('updateDisabled', this.form);
    } else {
      this.$emit('updateDisabled', '');
    }
  },
  methods: {
    reset() {
      this.form.finishResult = '已处理';
      this.form.finishSituation = '';
      this.form.feedbackResult = '';
    },
  },
  watch: {
    'form.finishResult': {
      //若选择已处理则不展示原因反馈输入
      handler(val) {
        if (val === '已处理') {
          this.form.feedbackResult = '';
        }
      },
    },
  },
};
</script>
<style lang="less" scoped>
.deal {
  width: 100%;
}
.remark {
  width: 100%;
}
.flex {
  display: flex;
}
.item-margin {
  margin: 15px 0;
}

.undeal-text {
  display: inline-block;
  vertical-align: middle;
  padding-top: 2px;
  color: var(--color-failed);
  font-size: 14px;
}
</style>
