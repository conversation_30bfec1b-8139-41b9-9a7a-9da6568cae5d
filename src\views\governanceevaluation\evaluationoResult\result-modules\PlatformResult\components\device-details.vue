<template>
  <ui-modal v-model="visible" title="设备检测明细" :styles="styles" footer-hide>
    <!-- 查询条件 -->
    <dynamic-condition :formItemData="filterList" :formData="searchData" @search="search" @reset="reset">
      <template #otherButton>
        <div class="other-button ml-lg inline">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </div>
      </template>
    </dynamic-condition>
    <!-- 表格 -->
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #isImportant="{ row }">
        <span>{{ row.isImportant === 0 ? '普通设备' : '重点设备' }}</span>
      </template>
      <template #onlineStatus="{ row }">
        <Tag v-if="row.onlineStatus" :color="getOnlineStatusText(row).color">
          {{ getOnlineStatusText(row).label }}
        </Tag>
      </template>
      <template #action="{ row }">
        <span class="span-click font-blue" @click="openVideo(row)">视频播放</span>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <ui-modal
      class="video-player"
      v-model="videoVisible"
      title="播放视频"
      :styles="videoStyles"
      footerHide
      @onCancel="onVideoPlayCancel"
    >
      <div>
        <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
      </div>
    </ui-modal>
  </ui-modal>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import vedio from '@/config/api/vedio-threm';
import { deviceDatailsSearchData, deviceDatailsTableColumns, detectionResultArr } from '../util/enum/ReviewParticular';
export default {
  name: 'deviceDetails',
  props: {
    value: {},
    rowData: {
      type: Object,
      default: () => {},
    },
    parentRowData: {
      type: Object,
      default: () => {},
    },
    indexInfo: {
      type: Object,
    },
  },
  components: {
    uiTable: () => import('@/components/ui-table.vue'),
    dynamicCondition: () => import('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue'),
    EasyPlayer: () => import('@/components/EasyPlayer.vue'),
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.45rem',
      },
      videoStyles: {
        width: '5rem',
      },
      tableColumns: [],
      tableData: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        isImportant: null,
        onlineStatus: '',
        reasonList: [],
      },
      filterList: [],
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      exportLoading: false,
      videoVisible: false,
      videoUrl: '',
      deviceItem: {},
      reasonList: [],
    };
  },
  created() {},
  computed: {
    getOnlineStatusText() {
      return (row) => {
        let arr = detectionResultArr(row.onlineStatus);
        let arr1 = arr.filter((item) => row.onlineStatus == item.value);
        return arr1[0] ? arr1[0] : '';
      };
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (!val) return false;
      this.visible = val;
      // 根据 此次统计 配置的 【设备离线】 显示不同的表头    number --  0:离线状态  1：拉流状态
      this.tableColumns = deviceDatailsTableColumns(this.rowData);
      this.filterList = deviceDatailsSearchData(this.parentRowData);
      this.searchData.onlineStatus = this.rowData.onlineStatusByFilterValue; // 设置 默认的检测结果过滤条件
      this.getTableList();
      this.getReasonList();
    },
    reasonList: {
      handler(arr) {
        arr = arr.map((item) => {
          return { value: item, label: item };
        });
        this.filterList.map((item) => {
          if (item.key === 'reasonList') {
            this.$set(item, 'options', arr);
          }
        });
      },
      deep: true,
    },
  },
  methods: {
    search(data) {
      this.pageData.pageNum = 1;
      this.searchData = data;
      this.getTableList();
    },
    reset() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        isImportant: null,
        onlineStatus: '',
        reasonList: [],
      };
      this.getTableList();
    },
    // 不合格原因
    async getReasonList() {
      try {
        this.reasonList = [];
        let { batchId } = this.indexInfo ? this.indexInfo : this.$route.query;
        let params = {
          batchId: batchId,
        };
        let res = await this.$http.post(evaluationoverview.queryReasonList, params);
        this.reasonList = res.data.data || [];
      } catch (err) {
        console.log(err);
      }
    },
    async getTableList() {
      try {
        this.loading = true;
        let { pageNum, pageSize } = this.pageData;
        let { statisticType, indexId, access, batchId } = this.indexInfo ? this.indexInfo : this.$route.query;
        let { orgCode } = this.rowData;
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: orgCode,
          customParameters: {
            ...this.searchData,
          },
          pageNumber: pageNum,
          pageSize: pageSize,
        };
        let res = await this.$http.post(evaluationoverview.getThirdModelData, params);
        this.tableData = res.data.data?.entities || [];
        this.pageData.totalCount = res.data.data?.total || 0;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    // 视频播放
    async openVideo(row) {
      try {
        this.videoVisible = true;
        const { deviceId } = row;
        this.deviceItem = row;
        let res = await this.$http.post(vedio.getplay, { deviceId });
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
    // 停止视频播放
    onVideoPlayCancel() {
      this.videoUrl = '';
      this.$http.post(vedio.stop + this.deviceItem.deviceId);
    },
    async onExport() {
      try {
        this.exportLoading = true;
        let { statisticType, indexId, access, batchId } = this.indexInfo ? this.indexInfo : this.$route.query;
        let { orgCode } = this.rowData;
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: access,
          displayType: statisticType,
          orgRegionCode: orgCode,
          customParameters: {
            ...this.searchData,
          },
        };
        let res = await this.$http.post(evaluationoverview.pageListExport, params, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(res);
      } catch (error) {
        console.log(error);
      } finally {
        this.exportLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  min-height: 530px;
}
.span-click {
  cursor: pointer;
  text-decoration: underline;
}
</style>
