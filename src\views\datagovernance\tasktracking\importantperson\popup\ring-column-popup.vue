<template>
  <ui-modal :title="columnTitle" v-model="visible" :styles="styles" :footer-hide="true">
    <div class="top-wrapper">
      <span>最新更新时间：{{ lastCheckDate }}</span>
      <p class="search-box">
        <Button type="primary" class="ml-lg button-blue" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
      </p>
    </div>
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <div class="ring-box">
        <draw-echarts
          :echart-option="ringEchartsOption"
          :echart-style="ringStyle"
          ref="zdryChart"
          class="ring-charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="column-box">
        <draw-echarts
          :echart-option="columnEchartsOption"
          :echart-style="columnStyle"
          ref="zdryChart"
          class="charts"
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
    </div>
    <line-title title-name="问题数据列表"></line-title>
    <ui-select-tabs
      class="tabs-ui"
      @selectInfo="selectColumnInfo"
      :list="tabsList"
      v-if="tabsList.length"
    ></ui-select-tabs>
    <div class="table-wrapper">
      <ui-table
        class="ui-table mt-sm"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
        :minus-height="minusTable"
      >
        <template #faceImg="{ row }">
          <ui-image :src="row.identityPhoto" class="image" />
          <!-- <img src="@/assets/img/common/nodata.png" class="image"> -->
        </template>
        <template #action="{ row }">
          <span class="font-table-action pointer" @click="openUnquatifiyReason(row)">查看不合格原因</span>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <unquatifiyreason-popup
      v-model="unquatifiyReasonPopup"
      :un-quantify-table-columns="unQuantifyTableColumns"
      :table-data="unquatifiyTableData"
      :loading="tableLoading"
    ></unquatifiyreason-popup>
  </ui-modal>
</template>
<script>
import importantEnum from '../util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '95%',
      },
      activeRouterName: null,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: '人员照片', slot: 'faceImg' },
        { title: '姓名', key: 'name' },
        { title: '性别', key: 'genderText' },
        { title: '民族', key: 'nation' },
        { title: '证件类型', key: 'cardTypeText' },
        { title: '证件号', key: 'idCard' },
        { title: '重点人员类型', key: 'personTypeText' },
        { title: '居住地址', key: 'homeAddress' },
        { title: '操作', fixed: 'right', slot: 'action' },
      ],
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '200px',
        width: '570px',
      },
      columnStyle: {
        height: '180px',
        width: '1100px',
      },
      minusTable: 600,
      permission: '',
      columnTitle: '',
      ringEchartsOption: {},
      columnEchartsOption: {},
      tableData: [],
      tabsList: [],
      echartsLoading: false,
      popUpOption: {},
      unquatifiyReasonPopup: false,
      unQuantifyTableColumns: importantEnum.baseDisqulifyTableColumn,
      unquatifiyTableData: [],
      tableLoading: false,
      lastCheckDate: '',
      errorSelectList: [],
      // tabsList: [
      //     { name: '字段1为空', select: true },
      //     { name: '字段2为空', select: false },
      //     { name: '字段3为空', select: false },
      // ]
    };
  },
  created() {},
  mounted() {},
  methods: {
    init(popUpOption) {
      this.lastCheckDate = '';
      this.ringEchartsOption = {};
      this.columnEchartsOption = {};
      this.tableData = [];
      this.tabsList = [];
      this.unquatifiyTableData = [];
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.visible = true;
      this.columnTitle = popUpOption.title;
      if (popUpOption.subTitle) {
        this.columnTitle = `${popUpOption.title}-${popUpOption.subTitle}`;
      }
      this.popUpOption = popUpOption;
      this.popUpColumnGetData();
    },
    selectColumnInfo(val) {
      this.errorSelectList = val.map((item) => item.name);
      this.pageData.pageNum = 1;
      this.queryImportantPersonPageTaster();
    },
    popUpColumnGetData() {
      if (this.popUpOption.filedData.componentCode === importantEnum.aggregateCodeEnums['空值检测']) {
        this.unQuantifyTableColumns = importantEnum.emptyTableColumns;
      } else {
        this.unQuantifyTableColumns = importantEnum.baseDisqulifyTableColumn;
      }
      this.queryPersonCheckCountStatistics();
      this.queryCheckStatics(0).then((data) => {
        this.handleColumnData(data);
        this.handleTabsList(data.map((item) => item.errorMessage));
      });
      this.queryImportantPersonPageTaster();
    },
    handleColumnData(data) {
      let opts = {
        xAxisData: data.map((item) => item.errorMessage || '未知'),
        data: data.map((item) => item.count || 0),
      };
      this.columnEchartsOption = this.$util.doEcharts.taskTrackingColumn(opts);
    },
    handleTabsList(data) {
      this.tabsList = data.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
    },
    openUnquatifiyReason(row) {
      this.unquatifiyReasonPopup = true;
      this.queryPersonCheckResultDetail(row.id);
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.queryImportantPersonPageTaster();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.queryImportantPersonPageTaster();
    },
    // 获取人员异常数据列表
    async queryImportantPersonPageTaster() {
      try {
        let params = Object.assign(
          {
            reasons: this.errorSelectList,
            pageNumber: this.pageData.pageNum,
            pageSize: this.pageData.pageSize,
            //topicComponentId: this.popUpOption.filedData.topicComponentId,
          },
          {},
        );
        if (this.popUpOption.filedData.componentCode !== '1002') {
          params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        }
        this.tableLoading = true;
        let { data } = await this.$http.post(tasktracking.queryImportantPersonPageTaster, params);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.tableLoading = false;
      } catch (err) {
        this.tableLoading = false;
        console.log(err);
      }
    },
    // 获取人员检测结果详情--查看不合格原因
    async queryPersonCheckResultDetail(importantPersonId) {
      try {
        this.tableLoading = false;
        let { data } = await this.$http.post(tasktracking.queryPersonCheckResultDetail, {
          topicComponentId: this.popUpOption.filedData.topicComponentId,
          importantPersonId: importantPersonId,
        });
        this.unquatifiyTableData = data.data.entities;
        this.tableLoading = false;
      } catch (err) {
        this.tableLoading = false;
        console.log(err);
      }
    },
    // 获取检测数量统计(左侧环形图)
    async queryPersonCheckCountStatistics() {
      try {
        let params = {};
        // if ( this.popUpOption.filedData.componentCode !== importantEnum.aggregateCodeEnums['数据输出']) {
        //     params.topicComponentId = this.popUpOption.filedData.topicComponentId
        // }
        params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        let interfaceString = 'queryPersonCheckCountStatistics';
        this.echartsLoading = true;
        // 图像上传及时性检测/人员轨迹准确性检测优化
        if (['8009', '10001'].includes(this.popUpOption.filedData.componentCode)) {
          interfaceString = 'queryPersonLibCheckCountStatistics';
        }
        let { data } = await this.$http.post(tasktracking[interfaceString], params);
        this.lastCheckDate = data.data.lastCheckDate;
        let allData = data.data;
        let successData = {
          name: '合格数据',
          value: allData.goodCount,
          color: importantEnum.ringColorEnum.greenColor,
        };
        let failData = {
          name: '不合格数据',
          value: allData.failCount,
          color: importantEnum.ringColorEnum.redColor,
        };
        this.ringEchartsOption = this.handleRingsEcharts([successData, failData], '检测总量', allData.allCount);
        this.echartsLoading = false;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    handleRingsEcharts(data, text, subtext) {
      let options = {
        data: data,
        text: text,
        subtext: subtext,
        legendData: data.map((item) => item.name),
      };
      return this.$util.doEcharts.taskTrackingRing(options);
    },
    async queryCheckStatics(checkDataType) {
      try {
        let params = {
          checkDataType: checkDataType,
        };
        if (this.popUpOption.filedData.componentCode !== importantEnum.aggregateCodeEnums['数据输出']) {
          params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        }
        this.echartsLoading = true;
        let { data } = await this.$http.post(tasktracking.queryCheckStatics, params);
        return data.data;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    async exportExcel() {
      try {
        let params = {};
        if (this.popUpOption.filedData.componentCode !== importantEnum.aggregateCodeEnums['数据输出']) {
          params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        }
        if (this.errorSelectList.length) params.reasons = this.errorSelectList + '';
        let res = await this.$http.get(tasktracking.downloadPersonAbnormalData, {
          responseType: 'blob',
          params: params,
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    unquatifiyreasonPopup: require('./unquatifiyreason-popup.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  margin: 10px 0;
  .ring-box {
    width: 570px;
    margin-right: 10px;
  }
  .column-box {
    flex: 1;
    position: relative;
  }
  .ring-box,
  .column-box {
    background: var(--bg-sub-content);
  }
  .charts {
    flex: 1;
  }
}
.top-wrapper {
  display: flex;
  justify-content: space-between;
  color: #fff;
  .search-box {
    display: flex;
    .input-width {
      width: 230px;
    }
  }
}
@{_deep}.scheme_header {
  margin-top: 0;
}
.image {
  width: 40px;
  height: 40px;
}
.tabs-ui {
  color: #fff;
  margin: 10px 0;
}
</style>
