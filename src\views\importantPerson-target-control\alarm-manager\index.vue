<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-25 10:35:54
 * @Description: 
-->
<template>
  <div class="layout">
    <tabsPage v-model="selectLi" :list="componentList" />
    <keep-alive>
      <component :is="currentComponent.componentName" :key="selectLi" />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import AlarmManager from "./alarm-manager/index.vue";
import WanderingInvasion from "./wandering-invasion/index.vue";
import UnusualActivity from "./unusual-activity/index.vue";
import PeerAnalysis from "./peer-analysis/index.vue";
import CrossDomainFlow from "./cross-domain-flow/index.vue";
import RiskyBehavior from "./risky-behavior/index.vue";

export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    AlarmManager,
    WanderingInvasion,
    UnusualActivity,
    PeerAnalysis,
    CrossDomainFlow,
    RiskyBehavior,
  },
  data() {
    return {
      selectLi: 1,
      componentList: [
        {
          label: "重点人员布控报警",
          value: 1,
          componentName: "AlarmManager",
        },
        {
          label: "人员轨迹异常报警",
          value: 2,
          componentName: "WanderingInvasion",
        },
        {
          label: "重点场所人员活动",
          value: 3,
          componentName: "UnusualActivity",
        },
        {
          label: "与其他重点人同行",
          value: 4,
          componentName: "PeerAnalysis",
        },
        {
          label: "人员跨域流动入侵",
          value: 5,
          componentName: "CrossDomainFlow",
        },
        {
          label: "高危人员风险行为",
          value: 6,
          componentName: "RiskyBehavior",
        },
      ],
    };
  },
  computed: {
    currentComponent() {
      let component = this.componentList.find(
        (item) => item.value == this.selectLi
      ) || {
        label: "重点人员布控报警",
        value: 1,
        radioList: [],
        componentName: "AlarmManager",
      };
      return component;
    },
  },
  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
