<template>
  <div>
    <ui-label class="inline mb-sm mr-md" label="审核人">
      <Input class="width-lg" v-model="searchData.examineUser" placeholder="请输入审核人姓名"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="审核/入库时间">
      <DatePicker
        class="width-md"
        v-model="searchData.startExamineTime"
        type="datetime"
        placeholder="请选择开始时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startExamineTime')"
        :options="startTimeOption"
        confirm
      />
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.endExamineTime"
        type="datetime"
        placeholder="请选择结束时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endExamineTime')"
        :options="endTimeOption"
        confirm
      />
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endExamineTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startExamineTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      searchData: {},
    };
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
