<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xyqMtbahqc
 */
export default {
  props: {
    labelList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      myEchart: null
    }
  },
  mounted () {
    this.init()
  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      const documentWidth = document.body.clientWidth
      let repulsion = 110
      if (documentWidth <1700 && documentWidth > 1500) {
        repulsion = 100
      } else if (documentWidth < 1500) {
        repulsion = 70
      }
      var option = {
        grid: {
          containLabel: true
        },
        color: 'rgba(0, 0, 0, 0)',
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'rgba(0, 0, 0, 0.8)',
          formatter: '{b}',
          textStyle: {
            color: '#fff'
          }
        },
        series: [
          {
            type: 'graph',
            layout: 'force',
            force: {
              repulsion: repulsion, // 节点之间的斥力因子。
              edgeLength: 800, // 边的两个节点之间的距离，这个距离也会受 repulsion
              friction: 0.06, // 这个参数能减缓节点的移动速度。取值范围 0 到 1。
              gravity: 0.09, // 节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
              layoutAnimation: false
            },
            roam: true,
            animation: false,
            symbol: '',
            label: {
              show: true
            },
            width: '100%',
            height: '100%',
            data: this.labelList
          }
        ]
      }
      this.myEchart.setOption(option)
      this.myEchart.on('click', function (params) {
        _that.$router.push({
          path: '/label-management/label-info',
          query: {
            id: params.data.labelId,
            curName: params.name
          }
        })
      })
      window.addEventListener('resize', () => this.myEchart.resize())
    },
    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
