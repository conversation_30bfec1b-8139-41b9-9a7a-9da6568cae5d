<template>
  <div class="auto-fill">
    <Particular
      class="particular"
      :icon-list="iconList"
      :table-columns="tableColumns"
      :table-data="tableData"
      :total-count="totalCount"
      :table-loading="tableLoading"
      :form-item-data="formItemData"
      :form-data="formData"
      :isPage="false"
      @startSearch="startSearch"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize"
    >
      <template #otherButton>
        <Button slot="export" type="primary" class="button-export ml-md" @click="onExport" :loading="exportLoading">
          <i class="icon-font icon-daochu font-white mr-xs"></i>
          <span class="ml-xs">导出</span>
        </Button>
      </template>
      <template #qualified="{ row }">
        <span class="check-status" :class="row.qualified === '1' ? 'bg-success' : 'bg-failed'">
          {{ row.qualified === '1' ? '达标' : '不达标' }}
        </span>
      </template>
      <template #faceReportNum="{ row, index }">
        <span :class="row.faceReportNum < tableData[index]['faceConfigNum'] ? 'font-red' : ''">{{
          row.faceReportNum
        }}</span>
      </template>
      <template #vehicleReportNum="{ row, index }">
        <span :class="row.vehicleReportNum < tableData[index]['vehicleConfigNum'] ? 'font-red' : ''">{{
          row.vehicleReportNum
        }}</span>
      </template>
      <template #videoReportNum="{ row, index }">
        <span :class="row.videoReportNum < tableData[index]['videoConfigNum'] ? 'font-red' : ''">{{
          row.videoReportNum
        }}</span>
      </template>
    </Particular>
  </div>
</template>
<script>
/**
 * 本文件属于处理数据的中间层
 */
import { iconStaticsList, tableColumn, formItemData } from './util/enum/ReviewParticular.js';
import downLoadTips from '@/mixins/download-tips';
import dealWatch from '@/mixins/deal-watch';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  mixins: [downLoadTips, particularMixin, dealWatch],
  props: {},
  data() {
    return {
      iconList: iconStaticsList,
      tableColumns: tableColumn,
      formItemData: formItemData,
      formData: {
        deviceId: '',
        deviceName: '',
        phyStatus: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      totalCount: 0,
      tableLoading: false,
      tableData: [],
      selectTabs: [],
      exportLoading: false,
      exportList: [
        { name: '导出设备总表', type: false },
        { name: '按异常原因导出分表', type: true },
      ],

      reasonTableColumns: [
        // { type: "selection", width: 70 },
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'propertyValue' },
      ],
      reasonTableData: [],
      reasonLoading: false,
    };
  },
  created() {
    this.getSelectTabs();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      this.getTableList();
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置合格不合格图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
    },
    startSearch(params) {
      this.pageData.pageNum = 1;
      Object.assign(this.formData, params);
      this.formData = params;
      this.getTableList();
    },
    getTableList() {
      this.tableData = [];
      this.MixinGetTableData().then((data) => {
        this.tableData = data.entities || [];
        this.totalCount = data.total;
      });
    },
    handlePage(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    handlePageSize(pageData) {
      this.pageData = pageData;
      this.getTableList();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          access: 'TASK_RESULT',
          displayType: this.$route.query.statisticType,
        };
        params.orgRegionCode =
          params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getAbnormalLabel,params, 'post',this.$route.query.cascadeId, {})
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = data.map((item) => {
          return {
            name: item,
          };
        });
      } catch (error) {
        console.log(error);
      }
    },
    selectInfo(infoList) {
      this.errorMessages = infoList.map((item) => {
        return item.name;
      });
      this.formData.errorMessages = this.errorMessages;
      this.pageData.pageNum = 1;
      this.getTableList();
    },
    // 导出设备列表
    // async getExport() {
    //   this.exportLoading = true
    //   let params = {
    //     indexId: this.$route.query.indexId,
    //     batchId: this.$route.query.batchId,
    //     displayType: this.$route.query.statisticType,
    //     customParameters: this.formData,
    //   }
    //   params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode :
    //     this.$route.query.orgCode
    //   try {
    //     this.$_openDownloadTip()
    //     const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
    //     await this.$util.common.transformBlob(res.data.data)
    //     this.exportLoading = false
    //   } catch (err) {
    //     this.exportLoading = false
    //   }
    // },
    // 导出
    onExport() {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      this.MixinGetExport(params);
    },
    // 不合格原因
    checkReason(row) {
      this.getReason(row.deviceInfoId);
      this.$refs.nonconformance.init();
    },
    async getReason(deviceInfoId) {
      this.reasonLoading = true;
      let params = {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        // access: this.paramsList.access,
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
        customParameters: { deviceInfoId },
      };
      try {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        // let res = await superiorinjectfunc(this, evaluationoverview.getSecondaryPopUpData,params, 'post',this.$route.query.cascadeId, {})
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.reasonTableData = datas || [];
        // this.reasonPage.totalCount = datas.total
        this.reasonLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  components: {
    Particular: require('../../ui-pages/particular.vue').default,
  },
};
</script>
<style lang="less" scoped>
.particular {
  padding: 0 10px;
  .operation-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  @{_deep} .ui-table {
    .ivu-table {
      &:before {
        width: 0;
      }

      .ivu-table-header {
        tr {
          th {
            color: var(--color-table-header-th);
            border-right: none !important;
          }
        }
      }
    }

    .ivu-table {
      th,
      td {
        border: 1px solid var(--border-color) !important;
      }

      &:before {
        content: '';
        position: absolute;
        background-color: #0d477d !important;
      }

      .ivu-table-summary {
        td {
          background: #062042;
        }
      }
    }
  }
}
</style>
