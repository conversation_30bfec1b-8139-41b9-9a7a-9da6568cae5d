<template>
  <div class="home-view">
    <iframe :src='src' width='100%' height='100%'></iframe>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  name: 'home',
  components: {

  },
  props: {},
  data() {
    return {};
  },
  computed: {
    ...mapGetters({
      getIframeConfig: 'common/getIframeConfig'
    }),
    src() {
      return `${this.getIframeConfig}/#/EquipmentManage?isAppointRouterByIframe=1&useDesignatedUser=1`;
    },
  },
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100%;
}
</style>
