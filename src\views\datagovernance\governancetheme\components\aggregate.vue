<template>
  <div class="aggregate">
    <div
      :class="['aggregateDiv', aggregateOptions.datas ? (aggregateOptions.datas.length > 1 ? 'addborder' : '') : '']"
      :style="{
        width: aggregateOptions.datas ? (aggregateOptions.datas.length > 1 ? bWidth : dWidth) : '0',
        top: aggregateOptions ? aggregateOptions.top : '0',
        left: aggregateOptions ? aggregateOptions.left : '0',
      }"
    >
      <div class="process" v-for="(item, index) in aggregateOptions.datas" :key="index">
        <process-block :processOptions="item"></process-block>
      </div>
      <div class="add" v-if="aggregateOptions.addVisible">
        <div class="circle">
          <i class="icon-font icon-tianjia"></i>
        </div>
        <div class="text">拖拽添加组件</div>
      </div>
    </div>
  </div>
</template>
<script>
import ProcessBlock from './process-block.vue';
export default {
  props: {
    aggregateOptions: {
      type: Object,
      default: () => {},
    },
    dWidth: {
      type: String,
      default: '0',
    },
    bWidth: {
      type: String,
      default: '0',
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    ProcessBlock,
  },
};
</script>
<style lang="less" scoped>
.aggregate {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.aggregateDiv {
  position: absolute;
  // width: 13.82%;
}
.addborder {
  // width: 15.23%;
  padding: 10px;
  border: 1px solid var(--color-primary);
  .process:not(:last-child) {
    margin-bottom: 10px;
  }
  .add {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 196px;
    height: 96px;
    background: rgba(22, 83, 164, 0.2);
    border: 1px dashed var(--color-primary);
    opacity: 1;
    border-radius: 4px;
  }
  .circle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    margin-right: 10px;
    border: 1px solid var(--color-primary);
    border-radius: 50%;
    i {
      color: var(--color-primary);
      font-size: 12px;
    }
  }
  .text {
    font-size: 13px;
    color: var(--color-primary);
  }
}
</style>
