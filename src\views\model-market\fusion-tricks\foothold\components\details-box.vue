<!--
    * @FileDescription: 落脚地分析 人脸抓拍机
    * @Author: H
    * @Date: 2023/01/16
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="details-box" :class="{'details-box-pack': footerUpDown}">
        <div class="box-hint">
            <i class="iconfont icon-jiantou" @click="handleback"></i>
            <span @click="handleback">返回落脚点</span>
        </div>
        <div class="box-content">
            <div class="title">
                <div class="title-left">
                    <div class="title-icon">
                        <span>{{ 1 }}</span>
                    </div>
                    <p>人脸抓拍机</p>
                </div>
                <Icon type="ios-close" @click="handleback" />
            </div>
            <ul class="box-ul">
                <li class="box-li"
                    :class="{'box-li-pack': packUpDown[index], 'box-li-packend': index == (dataList.length- 1)&&packUpDown[index]}" 
                    v-for="(item, index) in dataList" :key="index">
                    <div class="box-li-top">
                        <Icon type="md-radio-button-on"></Icon>
                    </div>
                    <div class="box-li-bottom">
                        <div class="time-title" @click="handletimelist(item, index)">
                            <p><span class="time-date">{{ item.date }}</span> <span class="time-num">{{item.times}}次</span></p>
                            <p class="triangle" :class="{'active-triangle': !packUpDown[index]}"></p>
                        </div>
                        <div class="child_list" v-for="(it, ind) in item.children" :key="ind">
                            <p class="sec-radio"></p>
                            <div class="content-top" @click="handleListTrack(item.children, ind)">
                                <div class="content-top-img">
                                    <img v-lazy="it.sceneImg" alt="">
                                </div>
                                <div class="content-top-right">
                                    <span class="ellipsis">
                                        <ui-icon type="time" :size="14"></ui-icon>
                                        <span>{{ it.captureTime }}</span>
                                    </span>
                                    <!-- <span class="ellipsis">
                                        <ui-icon type="location" :size="14"></ui-icon>
                                        <span>{{ it.captureAddress }}</span>
                                    </span> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="footer" :class="{packArrow: footerUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ footerUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            dataList: [
                {
                    'date': '2020-3-24',
                    'times': '1',
                    'children': [
                        {
                           'captureTime': '2020-03-24 16:27:10',
                           'sceneImg': require('@/assets/img/face.png')
                        }
                    ]
                }
            ],
            total: 0,
            packUpDown: [true],
            footerUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(item) {
            this.dataList[0].children[0].sceneImg = item.sceneImg;
        },
        handleListTrack() { },
        handletimelist(item, index) {
            this.$set(this.packUpDown, index, !this.packUpDown[index]);
            if(!this.packUpDown[index] && !this.dataList[index].children) {
                // this.handleTrack(item, index);
            }
        },
        handleback() {
            this.$emit('goback')
        },
        // 收缩、展开
        handlePackup() {
            this.footerUpDown = !this.footerUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
@import '../../components/style/timeLine';
.details-box{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    height: calc( ~'100% - 30px' );
    transition: height 0.2s ease-out;
    .box-hint{
        width: 370px;
        height: 40px;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        filter: blur(0px);
        color: #2C86F8;
        font-size: 14px;
        line-height: 40px;
        padding-left: 14px;
        .icon-jiantou{
            transform: rotate(90deg);
            display: inline-block;
            cursor: pointer;
        }
        span{
           font-size: 14px;
           cursor: pointer;
           margin-left: 10px;
        }
    }
    .box-content{
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        margin-top: 10px;
        height: calc( ~'100% - 50px' );
        overflow-y: auto; 
        .title{
            .title-left{
                display: flex;
                align-items: center;
                .title-icon{
                    position: relative;
                    background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
                    background-size: 100% 100%;
                    width: 20px;
                    height: 22px;
                    color: #EA4A36;
                    > span {
                        position: absolute;
                        top: -12px;
                        width: 20px;
                        font-size: 10px;
                        color: #ea4a36;
                        text-align: center;
                    }
                }
            }
        }
    }
    .footer{
        color: #000000;
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translate(-50%, 0px);
        background: #fff;
        width: 100%;
        z-index: 30;
    }
}
.details-box-pack{
    height: 120px;
    transition: height 0.2s ease-out;
    overflow: hidden; 
}
</style>
