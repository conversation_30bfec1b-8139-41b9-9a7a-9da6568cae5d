<template>
  <div class="otherassets auto-fill">
    <charts-container :abnormal-count="countList" class="charts" />
    <div class="mt-lg">
      <ui-label class="inline mr-lg mb-sm" label="组织机构">
        <api-organization-tree
          ref="orgTree"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceId}`">
        <Input class="width-md" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceName}`">
        <Input class="width-md" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="品牌">
        <Input class="width-md" v-model="searchData.brand" placeholder="请输入品牌"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="设备型号">
        <Input class="width-md" v-model="searchData.model" placeholder="请输入型号"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.ipAddr}`">
        <Input class="width-md" v-model="searchData.ipAddr" :placeholder="`请输入${global.filedEnum.ipAddr}`"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.macAddr}`">
        <Input class="width-md" v-model="searchData.macAddr" :placeholder="`请输入${global.filedEnum.macAddr}`"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="设备类型">
        <Select class="width-md" v-model="searchData.deviceType" placeholder="请选择设备类型" clearable>
          <Option v-for="(item, index) in other_device_type" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!-- 设备状态 -->
      <ui-label class="inline mr-lg mb-sm" :label="global.filedEnum.phyStatus">
        <Select
          class="width-md"
          v-model="searchData.deviceStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="over-flow mb-sm">
      <div class="btn fr">
        <Button type="text" class="ml-sm" @click="exportModule">
          <span class="link">下载模板</span>
        </Button>
        <Upload
          action="/ivdg-asset-app/assertOtherDevice/importOtherAssert"
          ref="upload"
          class="inline"
          :show-upload-list="false"
          :headers="headers"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
        >
          <Button type="primary" class="ml-sm" :loading="importLoading">
            <i class="icon-font icon-daoruwentishebei f-12"></i>
            <span class="vt-middle ml-sm">导入设备</span>
          </Button>
        </Upload>
        <Button type="primary" class="ml-sm" @click="addDevice">
          <i class="icon-font icon-tianjia f-14"></i>
          <span class="vt-middle ml-sm">新增设备</span>
        </Button>
        <Button type="primary" class="ml-sm" :loading="deleteLoading" @click="batchDelete">
          <i class="icon-font icon-piliangshanchu mr-xs f-12"></i>
          <span>批量删除</span>
        </Button>
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="width-percent inline ellipsis" :class="row.rowClass">{{ row.deviceId }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <template #sourceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.sourceId">
            {{ row.sourceId }}
          </div>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.deviceStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.deviceStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #azsj="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.azsj">
            {{ row.azsj }}
          </div>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip class="mr-md" icon="icon-bianji2" content="编辑" @click.native="edit(row)"></ui-btn-tip>
            <ui-btn-tip class="mr-md" icon="icon-chakanxiangqing" content="查看" @click.native="view(row)"></ui-btn-tip>
            <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="singleDelete(row)"></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <add-edit
      v-model="addEditShow"
      :modal-action="addEditAction"
      :default-form="defaultForm"
      @update="search"
    ></add-edit>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'otherassets',
  props: {},
  data() {
    return {
      countList: [],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      searchData: {
        orgCode: '',
        deviceId: '',
        deviceName: '',
        brand: '',
        model: '',
        ipAddr: '',
        macAddr: '',
        deviceType: '',
        deviceStatus: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      importLoading: false,
      deleteLoading: false,
      loading: false,
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          minWidth: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '设备类型',
          key: 'deviceTypeName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '品牌',
          key: 'brand',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '型号',
          key: 'model',
          align: 'left',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        { width: 160, title: '入库时间', key: 'createTime' },
        { width: 160, title: '更新时间', key: 'modifyTime' },
        {
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
      checkedData: [],
      // 编辑
      addEditShow: false,
      addEditAction: {
        action: 'add',
        title: '新增设备',
      },
      defaultForm: {},
    };
  },
  async created() {
    await this.getAlldicData();
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async initStatistic() {
      try {
        const res = await this.$http.post(equipmentassets.queryOtherStatistics);
        let total = 0;
        // 这里需要使用字典中的排序方式，后端无法排序，然后从后端返回的数据中进行赋值
        this.countList = this.other_device_type.map((row) => {
          const statistic = res.data.data.find((rw) => rw.deviceType === row.dataKey);
          if (statistic) {
            this.$set(row, 'icon', `icon-${statistic.deviceType}`);
            this.$set(row, 'title', statistic.deviceTypeName);
            this.$set(row, 'count', statistic.total);
            total += statistic.total;
          } else {
            console.log(`字典中没有${row.dataValue}`);
          }
          return row;
        });
        this.countList.unshift({
          icon: 'icon-jianceshebeishuliang',
          title: '设备总量',
          count: total,
        });
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        this.initStatistic();
        const res = await this.$http.post(equipmentassets.getAssertOtherDevice, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNum = 1;
      this.pageData.pageNumber = 1;
      this.init();
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.search();
    },
    reset() {
      this.$refs.orgTree.reset();
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    addDevice() {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'add',
        title: '新增设备',
      };
    },
    edit(row) {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'edit',
        title: `编辑设备${row.deviceName}`,
      };
      this.getDevice(row);
    },
    view(row) {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'view',
        title: `查看设备${row.deviceName}`,
      };
      this.getDevice(row);
    },
    async getDevice(row) {
      try {
        const res = await this.$http.get(equipmentassets.viewOtherDevice, {
          params: { id: row.id },
        });
        this.defaultForm = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async exportModule() {
      try {
        const res = await this.$http.get(equipmentassets.downloadOtherTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.data.tip);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.search();
      } else if (res.code === 81598) {
        this.dealError(res.msg);
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    batchDelete() {
      const ids = this.checkedData.map((row) => row.id);
      if (ids.length === 0) return;
      this.$UiConfirm({
        content: `您要删除这${ids.length}项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDevice(ids);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    singleDelete(row) {
      this.$UiConfirm({
        content: `您要删除${row.deviceName}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDevice([row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteDevice(ids) {
      try {
        this.deleteLoading = true;
        const res = await this.$http.post(equipmentassets.assertOtherRemove, ids);
        this.$Message.success(res.data.msg);
        this.checkedData = [];
        this.search();
      } catch (err) {
        console.log(err);
        this.$Message.error(err.data.msg);
      } finally {
        this.deleteLoading = false;
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      phystatusList: 'algorithm/propertySearch_phystatus',
      other_device_type: 'algorithm/other_device_type',
    }),
  },
  components: {
    ChartsContainer: require('@/views/viewassets/components/chartsContainer').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    AddEdit: require('./add-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .charts-container {
  flex-wrap: wrap;
  margin-top: 0;
  // justify-content: space-around;
}
@{_deep} .charts-item {
  width: 288px !important;
  margin-top: 10px;
  margin-right: 10px !important;
  &:nth-child(6n) {
    margin-right: 0 !important;
  }

  .icon-bianmashebei {
    display: inline-block;
    transform: scale(0.8, 0.9);
  }
}
.otherassets {
  padding: 10px 20px;
  background-color: var(--bg-content);
  .link {
    text-decoration: underline;
  }
}
</style>
