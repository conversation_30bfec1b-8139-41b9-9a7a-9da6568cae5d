import { mapMutations, mapActions, mapGetters } from "vuex";
import { queryFusionDetails } from "@/api/viewParsingLibrary";
export const cutMixins = {
  data() {
    return {
      imageNumWidth: 120,
      num: 0,
      waiting: false,
    };
  },
  computed: {
    ...mapGetters({
      getPageInfo: "countCoverage/getPageInfo",
    }),
  },
  methods: {
    ...mapMutations({ setPageInfo: "countCoverage/setPageInfo" }),
    locationPlay(index) {
      let small_pic = document.getElementById("scroll-ul");
      let present = document.querySelector("#present");
      if (index >= 11) {
        present.style.left = 11 * this.imgBoxWidth + 10 + "px";
        small_pic.style.left =
          11 * this.imgBoxWidth - this.imageNumWidth * index + "px";
      } else {
        present.style.left = 10 + index * this.imgBoxWidth + "px";
      }
      this.num = index;
    },
    // 重置右侧滑动位置 保证对应选择位置对应
    resetLeftPage(num) {
      let small_pic = document.getElementById("scroll-ul");
      small_pic.style.left = "0px";
      this.num = num;
    },
    // 重置左侧滑动位置
    resetRightPage(num) {
      this.num = num;
    },
    // 此处与之前的不同，滑动数据全靠分页接口提供 不做已查询数据的保存，之前的保存之前数据会导致list节点越来越多，性能不太好
    play(i) {
      this.waiting = true;
      // 延时20ms保证动画完整完成 同时方式点击过快导致错位
      setTimeout(() => {
        // 数据列表偏移量
        let small_pic = document.getElementById("scroll-ul");
        let small_pic_left = parseInt(small_pic.offsetLeft);
        let present = document.querySelector("#present");
        // 选择框偏移量
        let present_left = parseInt(present.offsetLeft);
        if (i > this.num) {
          // 选择框到达最右边，保持不动
          // parseInt(present.offsetLeft)>= 1320
          if (parseInt(present.offsetLeft) >= this.imgBoxWidth * 11) {
            // 活动框到了最右边
            // 小图片向左
            if (
              parseInt(small_pic.offsetLeft) >
              small_pic_left - this.imageNumWidth * (i - this.num)
            ) {
              small_pic.style.left =
                parseInt(small_pic.offsetLeft) - this.imgBoxWidth + "px";
              this.num = i;
            } else {
              small_pic.style.left =
                small_pic_left - this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          } else {
            // 活动框向右
            if (
              parseInt(present.offsetLeft) <
              present_left + this.imageNumWidth * (i - this.num)
            ) {
              present.style.left =
                parseInt(present.offsetLeft) +
                (i - this.num) * this.imgBoxWidth +
                "px";
              this.num = i;
            } else {
              present.style.left =
                present_left + this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          }
        } else if (i < this.num) {
          // 选择框到达最左边，保持不动
          if (parseInt(present.offsetLeft) <= 10) {
            // 小图片整体向右
            if (
              parseInt(small_pic.offsetLeft) <
              small_pic_left - this.imageNumWidth * (i - this.num)
            ) {
              small_pic.style.left =
                parseInt(small_pic.offsetLeft) + this.imgBoxWidth + "px";
              this.num = i;
            } else {
              small_pic.style.left =
                small_pic_left - this.imageNumWidth * (i - this.num) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          } else {
            if (
              parseInt(present.offsetLeft) >
              present_left - this.imageNumWidth * (this.num - i)
            ) {
              present.style.left =
                parseInt(present.offsetLeft) -
                (this.num - i) * this.imgBoxWidth +
                "px";
              this.num = i;
            } else {
              present.style.left =
                present_left - this.imageNumWidth * (this.num - i) + "px";
              small_pic.getElementsByTagName("li")[i].className =
                "list-box presently";
              this.num = i;
            }
          }
        }
        this.stopWaiting();
      }, 20);
    },
    // 防止到第一个或者最后一个的逻辑是不再触发play，导致waiting一直为true，不能再切换
    stopWaiting() {
      this.waiting = false;
    },
  },
};
