/*
 * @Date: 2025-03-04 10:57:52
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-04 16:04:20
 * @FilePath: \icbd-view\src\assets\configureIcons\place-iconfont.js
 */
export const iconList = {
  id: "2544923",
  name: "启数所有项目合集",
  font_family: "iconfontconfigure",
  css_prefix_text: "icon-",
  description: "为了给系统管理中心配置图标",
  glyphs: [
    {
      icon_id: "Place_Hotel",
      name: "酒店",
      url: require("@/assets/img/map/mapPoint/map-hotel.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-hotel_active.png"),
      icon: "hotel",
      color: "#EB8A5D",
    },
    {
      icon_id: "Place_InterBar",
      name: "网吧",
      url: require("@/assets/img/map/mapPoint/map-hotel.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-hotel_active.png"),
      icon: "diannao",
      color: "#EB6C6C",
    },
    {
      icon_id: "Place_Shower",
      name: "桑拿浴室",
      url: require("@/assets/img/map/mapPoint/map-shower.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-shower.png"),
      icon: "sangna",
      color: "#2C86F8",
    },
    {
      icon_id: "Place_Skate",
      name: "溜冰场",
      url: require("@/assets/img/map/mapPoint/map-skate.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-skate.png"),
      icon: "liubingchang",
      color: "#1FAF8A",
    },
    {
      icon_id: "Place_Sing",
      name: "歌舞厅",
      url: require("@/assets/img/map/mapPoint/map-sing.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-sing.png"),
      icon: "gewuting",
      color: "#6C6CEA",
    },
    {
      icon_id: "Place_Movie",
      name: "影剧院",
      url: require("@/assets/img/map/mapPoint/map-movie.png"),
      hoverUrl: require("@/assets/img/map/mapPoint/map-movie.png"),
      icon: "dianyingyuan",
      color: "#FF7E68",
    },
  ],
};
