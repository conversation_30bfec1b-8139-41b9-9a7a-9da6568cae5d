<template>
  <section class="main-container">
    <div class="search-bar" v-show="!onlyResult">
      <search-face
        :searchFields="searchFields"
        ref="searchBar"
        @search="searchHandle"
        @reset="resetHandle"
      />
    </div>
    <div class="table-container">
      <!-- 没数据时不展示工具栏 -->
      <div
        class="data-export"
        v-if="
          queryParam.algorithmSelect &&
          queryParam.algorithmSelect.length < 2 &&
          dataList.length
        "
      >
        <Checkbox
          v-show="!onlyResult"
          @on-change="checkAllHandler"
          v-model="checkAll"
          >全选</Checkbox
        >
        <div class="export-box" v-if="!mapOnData">
          <Button
            v-show="!onlyResult"
            class="mr"
            @click.stop="exportShow = true"
            size="small"
          >
            <ui-icon type="daoru" color="#2C86F8"></ui-icon>
            导出
          </Button>
          <exportBox
            ref="exportbox"
            v-if="exportShow"
            @confirm="confirm"
            @cancel="hideExportModal"
          ></exportBox>
        </div>
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleSort('similarity')"
          size="small"
          v-if="queryParam.features && queryParam.features.length > 0"
        >
          <!-- <ui-icon type="daoru" color="#2C86F8"></ui-icon> -->
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'absTime' ? 'primary' : 'default'"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button
          v-show="!onlyResult"
          @click="dataAboveMapHandler"
          size="small"
          style="float: right"
        >
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
        <Button
          @click="handleFalls"
          size="small"
          style="float: right; margin-right: 10px"
        >
          {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
        </Button>
        <slot name="mutilAction"></slot>
      </div>
      <div
        class="table-box"
        v-if="
          queryParam.algorithmSelect && queryParam.algorithmSelect.length == 2
        "
      >
        <geling
          class="table-box-geling"
          ref="geling"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          @toControlTask="toControlTask"
          @handleGaitPage="handleGaitPage"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></geling>
        <hk
          class="table-box-hk"
          ref="hk"
          @collection="collection"
          @checkHandler="checkHandler"
          @archivesPage="archivesPage"
          @targetAdd="handleTargetAdd"
          @dataCograph="dataCograph"
          @toControlTask="toControlTask"
          @handleGaitPage="handleGaitPage"
          :mapOnData="mapOnData"
          :queryParam="queryParam"
        ></hk>
      </div>
      <div class="table-container-box" v-else>
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            :class="{ checked: item.isChecked }"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="collection paddingIcon">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <Checkbox
              v-show="!onlyResult"
              class="check-box"
              v-model="item.isChecked"
              @on-change="(e) => checkHandler(e, index)"
            ></Checkbox>
            <div class="img-content">
              <div class="similarity" v-if="item.idScore">
                <span
                  class="num"
                  :class="{
                    'gerling-num': queryParam.algorithmVendorType == 'GLST',
                  }"
                  v-if="item.idScore"
                  >{{ item.idScore }}%</span
                >
                <!-- <span class="gerling-num" v-if="item.idScore">{{ item.idScore }}%</span> -->
              </div>
              <!-- <span class="num" v-if="item.score">{{ item.score }}%</span> -->
              <template v-if="childDataSourceVal == 2">
                <ui-image
                  :src="item.traitImg"
                  alt="动态库"
                  @click.native="faceDetailFn(item, index)"
                />
              </template>
            </div>
            <!-- 动态库 -->
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-location"></i>
                </Tooltip>
                <!-- <span class="ellipsis" v-show-tips>{{item.detailAddress}}</span> -->
                <ui-textOver-tips
                  refName="detailAddress"
                  :content="item.deviceName"
                ></ui-textOver-tips>
              </p>
            </div>
            <div v-show="!onlyResult" class="fast-operation-bar">
              <Poptip trigger="hover" placement="bottom-end">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item, 1)">
                    <i class="iconfont icon-renlian1"></i>以图搜图
                  </p>
                  <p @click="archivesPage(item, 2)">
                    <i class="iconfont icon-renlian1"></i>身份核验
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <p @click="handleTargetAdd(item)">
                    <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
                  </p>
                  <p @click="toControlTask(item)">
                    <i class="iconfont icon-sheweiyouxiao"></i>一键布控
                  </p>
                  <p @click="handleGaitPage(item)">
                    <i class="iconfont icon-a-lianhe322"></i>人脸搜步态
                  </p>
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="loading-box" v-if="fallsPage">
            <ui-loading v-if="scrollLoading"></ui-loading>
          </div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty
          v-if="dataList.length === 0 && !listLoading && !fallsPage"
        ></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <id-card
      ref="idCard"
      v-if="idCardShow"
      @close="idCardShow = false"
      @targetAdd="handleTargetAdd"
    >
    </id-card>
    <!-- 动态库人脸详情 -->
    <details-face-modal
      v-if="videoShow"
      ref="videoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="videoShow = false"
    ></details-face-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(false, true)"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="warnModalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(true, false)"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </section>
</template>
<script>
import searchFace from "../components/search-faces";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import hlModal from "@/components/modal/index.vue";
import {
  queryFaceRecordSearch,
  faceDownload,
  taskView,
  picturePick,
  getBase64ByImageCoordinateAPI,
} from "@/api/wisdom-cloud-search";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { mapMutations, mapGetters, mapActions } from "vuex";
import { myMixins } from "../../components/mixin/index.js";
import geling from "../../components/face/geling";
import hk from "../../components/face/hk";
import exportBox from "../../components/export/export-box.vue";
import idCard from "./idCard-content.vue";
import directionModel from "../components/direction-model";
import { throttle } from "lodash";
import { deepCopy } from "@/util/modules/common";
export default {
  name: "faceContent",
  props: {
    // 档案资料抓拍弹框
    searchFields: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 轨迹中心
    mapOnData: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [myMixins], //全局的mixin
  components: {
    searchFace,
    detailsFaceModal,
    geling,
    hk,
    exportBox,
    hlModal,
    idCard,
    directionModel,
  },
  data() {
    return {
      videoShow: false, // 抓拍详情弹框
      listLoading: false, // 列表loading
      idCardShow: false, // 身份证 | 姓名搜索结果
      dataList: [], // 列表list
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      currentIndex: 0, // 当前选中的展示详情的次序
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      total: 0,
      childDataSourceVal: 2, // 子组件数据资源值
      checkAll: false, // 全选
      timeUpDown: false, // 时间排序
      similUpDown: false, // 相似度排序
      exportShow: false, // 导出弹框
      modalShow: false, // 导出loading弹框
      warnModalShow: false, // 中断导出提示框
      downTaskId: "", // 下载任务
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0, // 预计时间
      scrollLoading: false, // 瀑布流请求列表loading
      mayRequest: false, // 无限加载是否可以继续请求
      fallsPage: false, // 是否是瀑布流
    };
  },
  activated() {
    if (this.mapOnData) {
      // 轨迹搜索上图返回加载数据
      this.selectionList = [];
      this.queryList();
    }
  },
  created() {
    // 以搜索目标添加 路由进来查看是否调用
    if (this.$route.query.traitImg) {
      this.$nextTick(() => {
        let row = {
          traitImg: JSON.parse(this.$route.query.traitImg || "[]")[0],
        };
        let work = this.$route.query.work;
        // 以搜索目标添加进入 不需要默认了进行搜索，只做添加
        if (work === "add") {
          let fileData = new FormData();
          fileData.append("algorithmType", 1);
          fileData.append("fileUrl", row.traitImg);
          picturePick(fileData).then(async (res) => {
            if (res.data && res.data.length > 0) {
              const response = await this.getBase64ByImageCoordinate(
                res.data[0]
              );
              let urlList = {
                fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
                feature: response.data.feature,
                imageBase: response.data.imageBase,
              };
              this.$refs.searchBar.urlImgList([urlList, ""], 2);
              this.pageInfo = {
                pageNumber: 1,
                pageSize: 27,
              };
            } else {
              this.$Message.warning("未识别出人脸！");
            }
          });
        } else {
          this.handleTargetAdd(row);
        }
      });
    }
  },
  async mounted() {
    await this.getDictData();
    window.addEventListener("click", this.hideExportModal);
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      picData: "common/getWisdomCloudSearchData",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.id);
    },
    // 只展示搜索结果，不能全选 | 导出 | 数据上图 | 选中卡片 | 卡片操作
    onlyResult() {
      /**
       * 1. props中SearchFields中的noSearch优先级更高
       * 2. 当props中没有noSearch再判断route.query
       */
      if (this.searchFields.noSearch) {
        return true;
      }
      return this.$route.query.noSearch;
    },
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
    window.removeEventListener("click", this.hideExportModal);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),

    /**
     * @description: 关闭导出框
     */
    hideExportModal() {
      this.exportShow = false;
    },

    /**
     * @description: 获取翻页列表
     * @param {number} page 标识，1 - 前一页，2 - 后一页
     */
    getDataList(page = 0) {
      this.listLoading = true;
      this.dataList = [];
      let params = {
        ...this.getParams(),
        ...this.pageInfo,
      };
      queryFaceRecordSearch(params)
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.dataList = entities || [];
          // 勾选
          if (this.dataList.length && this.upImageData.length) {
            this.dataList.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.id)) {
                e.isChecked = true;
              }
            });
          }
          if (page == 1) {
            this.$refs.videoDetail.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.videoDetail.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人脸",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.listLoading = false;
          // this.isActivated = true
        });
    },
    /**
     * @description: 获取瀑布流列表
     */
    cardScroll() {
      this.mayRequest = false;
      let params = {
        ...this.getParams(),
        ...this.pageInfo,
      };
      queryFaceRecordSearch(params).then((res) => {
        if (res.data.entities.length < this.pageInfo.pageSize) {
          this.mayRequest = false;
          this.scrollLoading = false;
        } else {
          this.mayRequest = true;
          // 勾选
          if (res.data.entities.length && this.upImageData.length) {
            res.data.entities.forEach((e) => {
              if (this.alreadyUpImageIds.includes(e.id)) {
                e.isChecked = true;
              }
            });
          }
          this.logParams(
            params,
            {
              muen: "分类搜索",
              name: "查询分类搜索人脸",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
          // 请求4页的数据，展示更多
          // 当请求回来的数量小于pageSize，表示没有更多数据了，不需要再发请求了
          if (
            this.pageInfo.pageNumber <= 3 &&
            res.data.entities.length >= this.pageInfo.pageSize
          ) {
            this.pageInfo.pageNumber += 1;
            this.queryList(0, false);
          } else {
            this.scrollLoading = false;
          }
        }
        this.dataList.push(...res.data.entities);
      });
    },

    /**
     * @description: 瀑布 - 翻页切换
     */
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.dataList = [];
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.scrollLoading = true;
        this.queryList(0, false);
      } else {
        this.mayRequest = false;
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 27,
        };
        this.queryList();
      }
    },

    /**
     * @description: 搜索
     */
    searchHandle() {
      // 全景参数获取
      this.pageInfo.pageNumber = 1;
      // 当有身份证 | 姓名搜索时，打开独特的搜索界面，只在点击查询时才触发，分页等触发的搜索列表不做处理
      let { idCardNo, name } = this.$refs.searchBar.getQueryParams();
      if (idCardNo || name) {
        this.idCardShow = true;
        this.$nextTick(() => {
          this.$refs.idCard.searchList({ idCardNo, name });
        });
      } else {
        this.dataList = []; // 防止瀑布流模式下一直搜索一直添加
        this.queryList();
      }
    },

    /**
     * @description: 滚动到顶部
     */
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },

    /**
     * @description: 无限加载
     */
    handleScroll: throttle(function () {
      if (!this.mayRequest) return; // 避免在未加载完一直滚动发起多余的请求
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (_dis + 1000 > scrollHeight && this.mayRequest) {
        this.pageInfo.pageNumber += 1;
        this.queryList(0, false);
      }
    }, 200),

    /**
     * @description: 获取列表
     * @param {number} page 翻页，1 - 前一页，2 - 后一页
     * @param {boolean} noRest 排序
     */
    queryList(page = 0, noRest = false) {
      this.checkAll = false;
      // 查询参数处理
      this.queryParam = {
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
        ...this.$refs.searchBar.getQueryParams(),
      };
      this.queryParam.similarity = this.queryParam.similarity / 100;
      this.childDataSourceVal = this.queryParam.dataSource;
      if (this.compareTime()) {
        return;
      }
      if (this.queryParam.algorithmSelect.length == 2) {
        this.$nextTick(() => {
          if (!noRest) {
            this.queryParam.order = "desc";
            this.queryParam.sortField = "similarity";
          }
          this.$refs.geling.init();
          this.$refs.hk.init();
        });
      } else {
        if (this.queryParam.features.length > 0 && !noRest) {
          this.queryParam.order = "desc";
          this.queryParam.sortField = "similarity";
        }
        if (this.fallsPage) {
          this.cardScroll();
        } else {
          this.getDataList(page);
        }
      }
    },

    /**
     * @description: 整理发起列表请求所需的参数，有些queryParam中的参数接口并不需要
     * @return {object} 整理后的参数
     */
    getParams() {
      return {
        similarity: this.queryParam.similarity,
        algorithmVendorType: this.queryParam.algorithmVendorType,
        deviceIds: this.queryParam.deviceIds,
        startDate: !this.onlyResult ? this.queryParam.startDate : "",
        endDate: !this.onlyResult ? this.queryParam.endDate : "",
        glasses: this.queryParam.glasses,
        gender: this.queryParam.gender,
        cap: this.queryParam.cap,
        faceMask: this.queryParam.faceMask,
        age: this.queryParam.age,
        dataSource: this.queryParam.dataSource,
        features: this.queryParam.features,
        imageBases: this.queryParam.imageBases,
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
        videoIdentity: this.queryParam.videoIdentity,
        searchValue: this.queryParam.searchValue || undefined,
      };
    },

    /**
     * @description: 排序
     * @param {string} val 当前要排序的方式
     */
    handleSort(val) {
      if (val != this.queryParam.sortField) {
        if (val == "similarity") {
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      } else {
        if (val == "similarity") {
          this.similUpDown = !this.similUpDown;
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.timeUpDown = !this.timeUpDown;
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      }
      this.queryParam.sortField = val;
      this.getDataList();
    },

    /**
     * @description: 导出提示框
     * @param {boolean} modalShow 导出loading弹框
     * @param {boolean} warnModalShow 中断导出提示框
     */
    modalStatus(modalShow, warnModalShow) {
      this.modalShow = modalShow;
      this.warnModalShow = warnModalShow;
    },

    /**
     * @description: 中断下载
     */
    onOk() {
      this.modalStatus(false, false);
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },

    /**
     * @description: 轮询导出数据
     */
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },

    /**
     * @description: 确认导出
     * @param {object} param 导出配置
     */
    confirm(param) {
      let params = {
        ids: [],
        downloadPics: param.downloadPics,
        downloadSize: null,
        ...this.getParams(),
      };
      // type: 1导出选中数据
      if (param.type == "1") {
        this.dataList.forEach((e) => {
          e.isChecked && params.ids.push(e.id);
        });
        if (!params.ids.length) {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params.downloadSize = param.downloadSize;
      }
      this.hideExportModal();
      this.modalShow = true;
      faceDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {
          // this.$refs.exportbox.handleEnd();
        });
    },

    /**
     * @description: 重置
     */
    resetHandle() {
      // 目前不做排序的重置，后期产品有需要再说
      this.queryParam = {
        order: this.queryParam.order,
        sortField: this.queryParam.sortField,
      };
      this.pageInfo.pageNumber = 1;
      this.dataList = [];
      this.queryList();
    },

    /**
     * @description: 页码改变
     * @param {number} size 第几页
     */
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList(0, true);
    },

    /**
     * @description: 页数改变
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList(0, true);
    },

    /**
     * @description: 打开详情
     * @param {object} row 抓拍信息
     * @param {number} index 第几个
     */
    faceDetailFn(row, index) {
      this.currentIndex = index;
      this.videoShow = true;
      this.$nextTick(() => {
        let val = this.queryParam.dataSource;
        let collectionType = val == 2 ? 5 : 13;
        this.$refs.videoDetail.init(
          row,
          this.dataList,
          index,
          collectionType,
          this.pageInfo.pageNumber
        );
      });
    },

    /**
     * @description: 跳转到档案
     * @param {object} item 抓拍信息
     * @param {number} index 标识
     */
    archivesPage(item, index) {
      let pageUrl = "";
      let query = {
        imgUrl: "",
      };
      if (index == 1) {
        pageUrl =
          "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1";
        query.imgUrl = item.traitImg;
      } else {
        pageUrl = "/model-market/face-warfare/identity-authentivation?noMenu=1";
        query.imgUrl = item.sceneImg;
        query.selectSquare = item.rect;
      }
      /* const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          imgUrl: item[imgUrl],
        },
      });
      window.open(href, "_blank"); */
      this.$util.common.openNewWindow({
        path: pageUrl,
        query,
      });
    },

    /**
     * @description: 全选
     * @param {boolean} val 全选 | 全不选
     */
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },

    /**
     * @description: 选中某一项
     * @param {boolean} e 是否选中
     * @param {number} i 列表中的位置
     */
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    dataCograph(list) {
      this.pointCograph(list);
    },

    /**
     * @description: 数据上图
     */
    dataAboveMapHandler() {
      this.pointCograph(this.dataList);
    },
    pointCograph(dataList) {
      // 已上图的不需要再次上图 (新增上图)
      let seleNum = dataList.filter(
        (e) => e.isChecked && !this.alreadyUpImageIds.includes(e.id)
      );
      // 取消勾选上图的
      let deletNum = [];
      this.alreadyUpImageIds.forEach((e) => {
        dataList.forEach((item) => {
          if (item.id === e && !item.isChecked) {
            deletNum.push(item);
          }
        });
      });
      let isNum = dataList.filter((e) => e.isChecked);
      if (isNum.length === 0) {
        this.$Message.warning("请勾选上图数据");
        return;
      }
      // 合并需要上图的和删除上图的
      seleNum = seleNum.concat(deletNum);
      if (!seleNum.length) {
        this.$Message.warning("勾选的数据已上图，请勾选新的数据");
        return;
      }
      // 判断是否有坐标信息
      let listHasLocation = seleNum.filter((item) => {
        return item.geoPoint && item.geoPoint.lat && item.geoPoint.lon;
      });
      if (!listHasLocation.length) {
        this.$Message.warning("无经纬度信息，无法上图");
        return;
      }
      if (listHasLocation.length < seleNum.length) {
        this.$Message.warning(
          `已过滤${seleNum.length - listHasLocation.length}条无经纬度信息的数据`
        );
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        listHasLocation.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = this.getListNum;
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      listHasLocation.map((item) => {
        item.delePoints = true;
        item.deleType = "face";
      });
      let list = listHasLocation.map((item, index) => {
        return {
          age: item.age,
          ageUpLimit: item.ageUpLimit,
          eyeglass: item.eyeglass,
          isCap: item.isCap,
          mask: item.mask,
          absTime: item.absTime,
          deleType: item.deleType,
          detailAddress: item.detailAddress,
          deviceId: item.deviceId,
          deviceName: item.deviceName,
          driverFlag: item.driverFlag,
          featureId: item.featureId,
          geoPoint: item.geoPoint,
          id: item.id,
          lat: item.lat,
          location: item.location,
          lon: item.lon,
          recordId: item.recordId,
          sceneImg: item.sceneImg,
          sourceId: item.sourceId,
          traitImg: item.traitImg,
          vid: item.vid,
          isChecked: item.isChecked,
          structureTime: item.structureTime,
          infoKind: item.infoKind,
          sbgnlx: item.sbgnlx,
          gender: item.gender,
          ethnic: item.ethnic,
          skinColour: item.skinColour,
        };
      });
      this.$emit("dataAboveMapHandler", {
        type: "face",
        deleIdent: "face-" + this.getListNum,
        list,
      });
    },

    /**
     * @description: 收藏
     * @param {object} data 当前数据
     * @param {number} flag 收藏 | 取消
     */
    collection(data, flag) {
      let val = this.queryParam.dataSource;
      let param = {
        favoriteObjectId: data.id,
        favoriteObjectType: val == 2 ? 5 : 13,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$set(data, "myFavorite", "1");
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$set(data, "myFavorite", "2");
          this.$Message.success("取消收藏成功");
        });
      }
    },

    /**
     * @description: 详情弹框切上一个
     * @param {number} pageNum 页码
     */
    prePage(pageNum) {
      if (pageNum < 1) {
        this.$Message.warning("已经是第一个了");
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1, true);
        }
      }
    },

    /**
     * @description: 详情弹框切下一个
     * @param {number} pageNum 页码
     */
    nextPage(pageNum) {
      let num = pageNum - 1; // 这个时候的pageNum已经为下一页的页码了
      let size = this.pageInfo.pageSize;
      if (this.total <= num * size) {
        this.$Message.warning("已经是最后一个了");
      } else {
        if (!this.fallsPage) {
          this.pageInfo.pageNumber = pageNum;
          this.queryList(2, true);
        }
      }
    },

    /**
     * @description: 地图定位
     * @param {object} row 抓拍信息
     */
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },

    /**
     * @description: 目标添加
     * @param {object} row 抓拍信息
     */
    handleTargetAdd(row) {
      // 如果是从身份证 | 姓名搜索页过来的，那么需要关闭该组件
      if (this.idCardShow) {
        this.idCardShow = false;
      }
      // 使用搜索目标添加进行搜索时，如果身份证 | 姓名搜索项有内容，需要清空
      this.$refs.searchBar.clearStaticLibrarySearch();
      let fileData = new FormData();
      fileData.append("algorithmType", 1);
      fileData.append("fileUrl", row.traitImg);
      picturePick(fileData).then(async (res) => {
        if (res.data && res.data.length > 0) {
          const response = await this.getBase64ByImageCoordinate(res.data[0]);
          let urlList = {
            fileUrl: "data:image/jpeg;base64," + response.data.imageBase,
            feature: response.data.feature,
            imageBase: response.data.imageBase,
          };
          this.$refs.searchBar.urlImgList([urlList, ""], 2);
        } else {
          this.$Message.warning("未识别出人脸！");
        }
      });
    },

    /**
     * @description: 一键布控
     * @param {object} row 抓拍信息
     */
    toControlTask(row) {
      const { href } = this.$router.resolve({
        name: "control-task-add",
        query: {
          compareType: 1,
          noMenu: this.$route.query.noMenu,
          images: JSON.stringify([
            {
              fileUrl: row.traitImg,
            },
          ]),
        },
      });
      window.open(href, "_blank");
    },

    /**
     * @description: 根据图片坐标截取图片base64
     * @param {object} data 图片信息
     * @return {promise} 获取图片base64
     */
    getBase64ByImageCoordinate(data) {
      const params = { ...data, type: "face" };
      return getBase64ByImageCoordinateAPI(params);
    },

    // 人脸搜步态
    handleGaitPage(row) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=gaitContent&noMenu=1",
        query: {
          algorithmType: 5,
          imgUrl: row.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    // 获取选择的数据
    getSelectDataList() {
      const selectList = this.dataList.filter((e) => e.isChecked);
      return deepCopy(selectList);
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
.main-container {
  position: relative;
}
.faceDetail {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}
.data-above {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}
.idCardNo {
  background: rgba(0, 0, 0, 0.7);
  height: 26px;
  line-height: 26px;
  text-align: center;
  margin-top: -26px;
  z-index: 999;
  position: absolute;
  width: 100%;
  color: #2c86f8;
  // font-size: 13px;
  font-weight: 600;
}
</style>
