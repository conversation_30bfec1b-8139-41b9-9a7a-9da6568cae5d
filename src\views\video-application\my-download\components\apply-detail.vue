<template>
	<ui-modal v-model="visible" :title="'申请详情'" :r-width="600" :footer-hide="true">
		<div class="apply-detail">
			<div>申请书：</div>
			<ui-image :src="imgUrl" viewer />
			<div>申请理由：</div>
			<Input v-model="applyMsg" type="textarea" disabled></Input>
			<div>审批描述：</div>
			<Input v-model="handleReason" type="textarea" :disabled="!isEdit"></Input>
		</div>
		<div class="footer" v-if="isEdit">
			<Button class="mr-10" type="primary" @click="comfirmHandle('1')">同意</Button>
			<Button class="mr-10" type="error" @click="comfirmHandle('2')">驳回</Button>
			<Button @click="cancleHandle">取消</Button>
		</div>
	</ui-modal>
</template>
  <script>
	import { applyUpdate } from '@/api/videoApply.js'
	export default {
		data() {
			return {
				visible: false,
				applyMsg: '案件取证',
				imgUrl: '',
				applyId: '',
				handleReason: '',
				isEdit: false
			}
		},
		methods: {
			// 初始化
			show(item, isEdit) {
				this.imgUrl = item.applyUrl
				this.applyMsg = item.applyReason
				this.applyId = item.id
				this.isEdit = !!isEdit
				this.handleReason = item.handleReason
				this.visible = true
			},
			comfirmHandle(status) {
				applyUpdate({ ids: [this.applyId], status, handleReason: this.handleReason }).then(res => {
					this.visible = false
					this.$Message.success('审核成功')
					this.$emit('comfirmHandle')
				})
			},
			cancleHandle() {
				this.visible = false
			}
		}
	}
  </script>
  <style lang="less" scoped>
	.apply-detail {
		.ui-image {
			width: 100%;
			height: 300px;
		}
		/deep/ .ui-image .ui-image-div img {
			width: auto;
		}
		/deep/.ivu-input {
			height: 100px;
		}
	}
	.footer {
		display: flex;
		justify-content: center;
    	margin-top: 20px;
	}
</style>
  