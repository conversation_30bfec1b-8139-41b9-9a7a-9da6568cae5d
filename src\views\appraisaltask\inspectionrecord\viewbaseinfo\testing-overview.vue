<template>
  <div class="search-wrapper auto-fill">
    <div class="search-container">
      <div class="jump">
        <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
      </div>
      <!--      <div class="search">
        <ui-label :label="global.filedEnum.sbgnlx" :width="70" class="ml-lg fl">
          <Select
            clearable
            class="width-sm"
            v-model="formValidate.sbgnlx"
            placeholder="请选择摄像机功能类型"
            :max-tag-count="1"
          >
            <Option
              v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
              :key="'sbgnlx' + bdindex"
              :value="sbgnlxItem.dataKey"
              >{{ sbgnlxItem.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label :label="global.filedEnum.sbdwlx" :width="70" class="ml-lg fl">
          <Select
            clearable
            class="width-sm"
            v-model="formValidate.sbdwlx"
            :max-tag-count="1"
            :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          >
            <Option
              v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
              :key="'sbdwlx' + bdindex"
              :value="sbdwlxItem.dataKey"
              >{{ sbdwlxItem.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <Button type="primary" class="ml-lg" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetForm">重置</Button>
      </div>-->
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #orgCode="{ row }">
        <span v-if="formValidate.currentOrgCode == row.orgCode">{{ row.orgName }}</span>
        <span class="span-btn" v-else @click="onClickOrg(row)">{{ row.orgName }}</span>
      </template>
    </ui-table>
  </div>
</template>

<script>
import UiBreadcrumb from '../components/ui-breadcrumb';
import governanceevaluation from '@/config/api/governanceevaluation';
import user from '@/config/api/user';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'testing-overview',
  components: {
    UiBreadcrumb,
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          width: 50,
          align: 'center',
          className: 'header-table',
        },
        {
          title: '行政区划',
          key: 'orgName',
          slot: 'orgCode',
          align: 'center',
          width: 120,
          className: 'header-table',
        },
        {
          title: '设备总数',
          key: 'deviceCount',
          align: 'center',
          width: 76,
          className: 'header-table',
        },
        {
          title: '检测设备',
          key: 'checkCount',
          align: 'center',
          width: 76,
          className: 'header-table',
        },
        {
          title: '正常设备数',
          key: 'normalCount',
          align: 'center',
          width: 95,
          className: 'header-table',
        },
        {
          title: '异常设备数',
          key: 'abnormalCount',
          align: 'center',
          width: 95,
          className: 'header-table',
        },
        {
          title: '国标编码异常数',
          key: 'deviceIdCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: '设备名称异常数',
          key: 'deviceNameCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: '经纬度异常数',
          key: 'longitudeLatitudeCount',
          align: 'center',
          width: 114,
          className: 'header-table',
        },
        {
          title: `${this.global.filedEnum.sbgnlx}异常数`,
          key: 'sbgnlxCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: '行政区划异常数',
          key: 'civilCodeCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: `${this.global.filedEnum.sbdwlx}异常数`,
          key: 'sbcjqyCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: `${this.global.filedEnum.sbcjqy}异常数`,
          key: 'sbdwlxCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: 'MAC地址异常数',
          key: 'macAddrCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: 'IP地址异常数',
          key: 'ipAddrCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
        {
          title: `${this.global.filedEnum.phyStatus}异常数`,
          key: 'phyStatusCount',
          align: 'center',
          width: 133,
          className: 'header-table',
        },
      ],
      tableData: [],
      hasLast: false,
      formValidate: {
        currentOrgCode: '',
        sbdwlx: '',
        sbgnlx: '',
      },
      dictData: {},
      loading: false,
      breadcrumbData: [],
    };
  },
  methods: {
    ...mapActions({
      deviceTestOverviewAction: 'inspectionrecord/deviceTestOverviewAction',
    }),
    onClickOrg({ orgCode, orgName }) {
      this.formValidate.currentOrgCode = orgCode;
      this.breadcrumbData.push({ id: orgCode, add: orgName });
      this.init();
    },
    search() {
      this.init();
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    handleChange(val) {
      this.formValidate.currentOrgCode = val.id;
      this.init();
    },
    async init() {
      try {
        this.loading = true;
        let params = {
          // evaluationStatisticsTypeId: this.$parent.currentTree.id,
          regionCode: this.taskObj.regionCode,
          batchId: this.taskObj.indexResults[0].batchId,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getBasicOverviewV2, {
          params,
        });
        this.tableData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    resetForm() {
      this.formValidate = {
        currentOrgCode: '',
        sbdwlx: '',
        sbgnlx: '',
      };
      this.getDefaultOrgCode();
      this.search();
    },
    getDefaultOrgCode() {
      this.breadcrumbData = [];
      this.formValidate.currentOrgCode = this.taskObj.indexResults[0].regionCode;
      this.breadcrumbData.push({
        id: this.taskObj.indexResults[0].regionCode,
        add: this.taskObj.indexResults[0].regionName,
      });
    },
  },
  mounted() {
    this.getDefaultOrgCode();
    this.init();
    this.getDictData();
  },
  computed: {
    ...mapGetters({
      orgTreeData: 'common/getOrganizationList',
    }),
  },
  watch: {
    formValidate: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.deviceTestOverviewAction(val);
      },
    },
    taskObj: {
      deep: true,
      handler: function () {
        this.init();
      },
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  /deep/ .header-table {
    box-shadow: inset 1px -1px 0 0 var(--border-table);
  }
  @{_deep} td {
    background: var(--bg-content);
  }
  @{_deep} th {
    .ivu-table-cell {
      color: #8797ac;
    }
  }
  @{_deep} .ivu-table-wrapper {
    border-top: 1px solid var(--border-table);
  }
  @{_deep} .ivu-table-tip {
    overflow-x: auto;
  }
}
.filter {
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
}

.search-wrapper {
  margin-bottom: 10px;
  .search-container {
    display: flex;
    justify-content: space-between;
    margin: 10px 0 10px 0;
    .jump {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.span-btn {
  cursor: pointer;
  color: var(--color-primary);
}
</style>
