<!-- 轨迹图片可访问率 -->
<template>
  <div class="review-particular auto-fill">
    <div class="icon-statics-wrapper">
      <icon-statics :icon-list="iconList"></icon-statics>
      <div class="icon-statics-wrapper-right">
        <tag-view :list="tabList" @tagChange="changeMode" ref="tagView" class="tag-view mt-xs"></tag-view>
      </div>
    </div>
    <div class="auto-fill">
      <component
        :is="componentName"
        :form-item-data="formItemData"
        :form-data="formData"
        :table-columns="tableColumns"
        :result-data="resultData"
        :check-picture-params="checkPictureParams"
        :loading="tableLoading"
        :need-separate-total="true"
        :data-total-count="resultDataTotalCount"
        :filed-name-map="filedNameMap"
        :error-code-list="errorCodeList"
        @startSearch="startSearch"
        :ref="`${componentName}Ref`"
      >
        <template #otherButton v-if="activeMode === 'list'">
          <div class="other-button ml-lg inline">
            <span class="font-active-color mr-sm pointer update-btn f-14" v-if="statisticShow" @click="updateStatistics"
              >更新统计结果</span
            >
            <Button
              type="primary"
              class="mr-sm"
              v-permission="{
                route: $route.name,
                permission: 'batchrecheck',
              }"
              :disabled="isRecheck"
              @click="handleBatchRecheck"
            >
              <i class="icon-font icon-piliangfujian"></i>
              {{ isRecheck ? '复检中...' : '批量复检' }}
            </Button>
            <Button type="primary" :loading="exportLoading" @click="onExport">
              <i class="icon-font icon-daochu"></i> 导出
            </Button>
          </div>
        </template>
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image v-show="!tableLoading" :src="row.identityPhoto" />
          </div>
        </template>
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images pointer">
            <ui-image v-show="!tableLoading" :src="row.trackImage" />
          </div>
        </template>
        <template slot="outcome" slot-scope="{ row }">
          <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
            {{ qualifiedColorConfig[row.qualified].dataValue }}
          </Tag>
          <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
        </template>
        <template #reason="{ row }">
          <Tooltip :content="row.reason" transfer max-width="150">
            {{ row.reason }}
          </Tooltip>
        </template>
        <template #option="{ row, index }">
          <ui-btn-tip icon="icon-chakanxiangqing" content="结果详情" @click.native="clickRow(index)" class="mr-sm">
          </ui-btn-tip>
          <ui-btn-tip
            v-permission="{
              route: $route.name,
              permission: 'artificialreviewr',
            }"
            icon="icon-rengongfujian"
            content="人工复核"
            class="vt-middle f-14 mr-sm"
            @click.native="clickArtificialReview(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-permission="{
              route: $route.name,
              permission: 'recheck',
            }"
            icon="icon-fujian"
            content="复检"
            class="vt-middle f-14"
            @click.native="clickArtificialRecheck(row)"
          ></ui-btn-tip>
        </template>
      </component>
    </div>
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <AlgorithmLookScene
      v-model="picDetialsShow"
      :img-list="resultData?.entities || []"
      :viewIndex="picIndex"
      :filed-name-map="filedNameMap"
    ></AlgorithmLookScene>
    <!-- 复检 -->
    <recheck
      v-model="recheckVisible"
      :module-data="moduleData"
      :is-batch-recheck="isBatchRecheck"
      @handleUpdate="initAll()"
    ></recheck>
    <!-- 复核 -->
    <review-and-detail
      v-model="artificialVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="detailData"
      :error-code-list="errorCodeList"
      :total-count="resultDataTotalCount"
      :table-data="resultData.entities || []"
      :page-data="pageData"
      :filed-name-map="filedNameMap"
      :get-list-api="getListApi"
      :search-parames="formData"
      :custom-value-field="customValueField"
      @closeFn="updateTable"
    ></review-and-detail>
    <!-- 导出 -->
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapGetters } from 'vuex';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { tableColumns, iconStaticsList, listFormData, cardFormData } from './util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';
// 【批量复检】按钮逻辑
import batchRecheckBtn from '../mixins/batchRecheckBtn';
export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch, batchRecheckBtn],
  props: {
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 格式：  自定义字段：对应的取值字段
      customValueField: {
        qualified: 'qualified', // 是否合格字段
        reason: 'firstLevelResultTip', // 不合格原因取值字段
      },
      iconList: iconStaticsList,
      activeMode: 'list',
      tabList: ['图片模式', '聚档模式'],
      formData: {
        beginTime: '',
        endTime: '',
        qualified: '',
        outcome: '',
        causeErrors: [],
        deviceIds: [],
        errorCodes: [],
      },
      formItemData: listFormData,
      componentName: 'ListPattern',
      tableLoading: false,
      tableColumns: Object.freeze(tableColumns),
      pageData: {
        pageNum: 1,
        pageSize: 20,
      },
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      resultData: {},
      resultDataTotalCount: 0,
      cardList: [],
      statisticShow: false,
      checkPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      bigPictureShow: false,
      imgList: [],
      picDetialsShow: false,
      picIndex: 0,
      filedNameMap: {
        smallPicName: 'trackImage', // 小图
        bigPicName: 'trackLargeImage', // 大图
        resultTip: 'firstLevelResultTip', // 图片备注
        qualified: 'qualified', // 不合格
      },
      moduleData: {},
      isBatchRecheck: false,
      recheckVisible: false,
      isReview: false, // 人工复核 过后
      artificialVisible: false,
      detailData: {},
      errorCodeList: [], // 错误原因
      exportLoading: false,
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
      // 获取列表
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
    },
    // 获取不合格原因下拉列表  1 - 合格、2 - 不合格 3 - 无法检测
    async getQualificationList() {
      try {
        // 2 是图片模式
        let options = await this.getImageIndexErrorCode();
        let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
        findErrorCodes.options = options.map((item) => {
          return { value: item.key, label: item.value };
        });
        this.errorCodeList = findErrorCodes.options;
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 获取错误原因
     * @returns {Promise<{}>}
     */
    async getImageIndexErrorCode() {
      try {
        let params = {
          indexType: 'FACE_URL_AVAILABLE',
          model: 2,
        };
        let res = await this.$http.get(detectionResult.getUnqualifiedInfo, { params });
        return res.data.data || {};
      } catch (err) {
        console.log(err);
      }
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.activeMode === 'list' ? this.getTableData() : this.getImageList();
    },
    // 获取列表[mixin的方法]
    selfConfigGetListTotal() {
      this.activeMode === 'list' ? this.getTableDataTotal() : '';
    },
    showModal(row) {
      this.osdDetailVisible = true;
      this.osdDetailData = { ...row };
    },
    handleOsdModalHide() {
      this.$refs.osdModalRef.hide();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.pageData = params.pageData;
      Object.assign(this.formData, params.searchData);
      this.selfConfigGetList();
      if (params.isSearchTotal) {
        this.selfConfigGetListTotal();
      }
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    async getTableData() {
      try {
        this.tableLoading = true;
        const data = await this.MixinGetTableData();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.resultDataTotalCount = data;
      });
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 切换模式
    changeMode(type) {
      this.resultData = {};
      this.activeMode = !type ? 'list' : 'cad';
      this.formItemData = this.activeMode === 'list' ? listFormData : cardFormData;
      this.componentName = this.activeMode === 'list' ? 'ListPattern' : 'CardPattern';
      this.pageData.pageNum = 1;
      this.resultDataTotalCount = 0;
      if (type === 'list') {
        this.formData = {
          beginTime: '',
          endTime: '',
          qualified: '',
          causeErrors: [],
          deviceIds: [],
        };
        this.getQualificationList();
      } else {
        // 图片模式获取不合格原因传2
        this.formData = {
          name: '',
          idCard: '',
        };
        this.getQualificationList(2);
      }
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
    },
    async getImageList() {
      // 处理图片模式列表数据
      try {
        this.tableLoading = true;
        const data = await this.MixinGetImageList();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.tableLoading = false;
      }
    },
    clickRow(index) {
      this.picDetialsShow = true;
      this.picIndex = index;
    },
    // 人工复核更新统计接口
    async updateStatistics() {
      // 调用统计，并通知后端已更新[之前的逻辑]
      this.MixinGetStatInfo().then((data) => {
        iconStaticsList.forEach((item) => (item.count = data[item.fileName] || 0));
      });
      let data = {
        batchId: this.$route.query.batchId,
      };
      try {
        await this.$http.post(evaluationoverview.pushRecheckQueue, data);
        this.$Message.success('更新成功');
        this.statisticShow = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 复检
    clickArtificialRecheck(row) {
      this.moduleData = { deviceId: row.id, ...this.activeIndexItem };
      this.isBatchRecheck = false;
      this.recheckVisible = true;
    },
    // 批量复检
    handleBatchRecheck() {
      // isRecheck - 混入js
      if (this.isRecheck) return;
      this.moduleData = { ...this.activeIndexItem };
      this.isBatchRecheck = true;
      this.recheckVisible = true;
    },
    // 人工复核
    clickArtificialReview(row) {
      this.detailData = { ...row };
      this.artificialVisible = true;
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.pageData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.pageData.pageNum = newPage;
      // 重新设置当前页
      if (this.$refs.ListPatternRef) {
        this.$refs.ListPatternRef.pageData.pageNum = newPage;
      }
      if (isReview) {
        this.showRecountBtn();
        this.selfConfigGetList();
        this.selfConfigGetListTotal();
      } else if (newPage !== pageNum) {
        this.selfConfigGetList();
        this.selfConfigGetListTotal();
      }
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
    async getListApi({ pageNumber, pageSize, isUpdate, currentRow }) {
      try {
        let { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
        let customParameters = this.formData;
        // isUpdate=true  -- 表示需要更新某一条数据
        if (isUpdate) {
          pageNumber = 1;
          customParameters = {
            id: currentRow.id,
          };
        }
        let params = {
          indexId: indexId,
          batchId: batchId,
          access: 'TASK_RESULT',
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          customParameters: customParameters,
          pageNumber: pageNumber,
          pageSize: pageSize,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        return res;
      } catch (err) {
        throw new Error();
      }
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    // customizedAttrs() {
    //   return {
    //     iconList: this.iconList,
    //     tableColumns: this.tableColumns,
    //     tableData: this.tableData,
    //     formItemData: this.formItemData,
    //     formData: this.formData,
    //     tableLoading: this.tableLoading,
    //     totalCount: this.totalCount,
    //     cardList: this.cardList,
    //   };
    // },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    ListPattern: require('../components/list-pattern').default,
    LookScene: require('@/components/look-scene').default,
    CardPattern: require('./components/card-pattern').default,
    AlgorithmLookScene:
      require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/look-scene.vue').default,
    recheck: require('../components/recheck/index.vue').default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/components/face-vehicle-url-algorithm/mode-pic.vue')
        .default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.review-particular {
  padding: 0 10px;

  .icon-statics-wrapper {
    display: flex;
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
  }

  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;

    .ui-image {
      min-height: 56px !important;

      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
