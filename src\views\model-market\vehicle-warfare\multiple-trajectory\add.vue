<!--
 * @Author: du<PERSON>en
 * @Date: 2024-09-14 10:41:52
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-21 16:54:43
 * @Description: 
-->
<template>
  <div class="trait-add-container">
    <div class="trait-sub-container">
      <div>
        <div class="skills_row">
          <label class="label"><i class="required">*</i>开始时间:</label>
          <DatePicker
            class="date"
            v-model="frequency.startTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="案发时间"
            transfer
          ></DatePicker>
        </div>
        <div class="skills_row">
          <label class="label"><i class="required">*</i>结束时间:</label>
          <DatePicker
            class="date"
            v-model="frequency.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="案发时间"
            transfer
          ></DatePicker>
        </div>
        <div class="skills_row">
          <label class="label">设备选择:</label>
          <div class="check_device">
            <div class="select-tag-button" @click="handleSelectDevice">
              {{
                frequency.devices.length == 0
                  ? `选择设备`
                  : `已选(${frequency.devices.length})`
              }}
            </div>
            <div class="frame-selection" @click="mapToolVisibleHandlebar(true)">
              <ui-icon type="gateway" :size="20" title="框选"></ui-icon>
            </div>
          </div>
        </div>
        <div
          class="skills_row"
          :style="{
            cursor: resultList.length === maxLength ? 'not-allowed' : 'pointer',
          }"
        >
          <label class="label"><i class="required">*</i>添加目标:</label
          ><span class="limit-tips"
            >请添加{{ minLength }}至{{ maxLength }}个目标</span
          >
          <div
            class="skill_upload_container"
            :style="{
              'pointer-events':
                resultList.length === maxLength ? 'none' : 'auto',
            }"
          >
            <uiUploadImg
              ref="uploadImg"
              class="uploader-container"
              v-if="!featureInfo.originPic"
              :setDetect="true"
              :algorithmType="2"
              v-model="featureInfo.urlList"
              @imgUrlChange="imgUrlChange"
              size="large"
            />
            <div class="uploader-img" v-if="featureInfo.originPic">
              <p>
                <i
                  class="iconfont icon-shanchu"
                  title="删除"
                  @click="delUploadImg"
                ></i>
              </p>
              <img
                onerror="this.src='common/images/no.png'"
                :src="featureInfo.targetPic"
                class="img"
              />
              <div class="tools-bar">
                <span
                  ><i
                    class="iconfont icon-reload"
                    title="上传"
                    @click="reUploadImg"
                  ></i
                ></span>
              </div>
            </div>
            <div class="add-button" @click="addRecord">添加>></div>
            <div>
              <Input
                v-model="featureInfo.plateNo"
                placeholder="请输入车牌号"
                class="my-input-class"
                clearable
              ></Input>
              <ui-tag-select
                ref="bodyColor"
                @input="
                  (e) => {
                    input(e, 'plateColor');
                  }
                "
                class="tag-select-box"
              >
                <ui-tag-select-option
                  v-for="(item, $index) in licensePlateColorList"
                  :key="$index"
                  effect="dark"
                  :name="item.dataKey"
                  :ref="item.dataKey"
                >
                  <div
                    v-if="licensePlateColorArray[item.dataKey]"
                    :title="item.dataValue"
                    :style="{
                      borderColor:
                        licensePlateColorArray[item.dataKey].borderColor,
                    }"
                    class="plain-tag-color"
                  >
                    <div
                      :style="licensePlateColorArray[item.dataKey].style"
                    ></div>
                  </div>
                </ui-tag-select-option>
              </ui-tag-select>
            </div>
          </div>
        </div>
      </div>
      <div v-scroll class="list">
        <list-item
          v-for="(item, index) in resultList"
          :index="index"
          :key="index"
          :options="item"
          @delete="deleteRecord"
        />
        <ui-empty v-if="resultList.length == 0"></ui-empty>
      </div>
    </div>
    <div class="bottom-tools">
      <Button type="primary" class="btnwidth mr-20" @click="handleRouterMaker"
        >生成轨迹</Button
      >
      <Button class="btnwidth" @click="handleReset">重置</Button>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      @selectData="handleSelectData"
      :showOrganization="true"
    ></select-device>
    <!-- 框选设备 -->
    <select-map-modal
      v-if="selectmapShow"
      ref="selectmap"
      @configdata="handleConfig"
    ></select-map-modal>
  </div>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import uiUploadImg from "@/components/ui-upload-new-img/index";
import listItem from "./components/item";
import selectMapModal from "@/components/select-modal/select-map-modal.vue";
const DAYRANGE = 24 * 60 * 60 * 1000;
export default {
  name: "",
  components: {
    listItem,
    selectMapModal,
    uiUploadImg,
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: "dictionary/getLicensePlateColorList", // 车牌颜色
    }),
  },
  data: function () {
    return {
      licensePlateColorArray, // 车牌颜色
      dayRangeNum: 7,
      minLength: 2, // 最少添加目标
      maxLength: 5,
      frequency: {
        devices: [],
        startTime: new Date(new Date().getTime() - 60 * 1000 * 60 * 24 * 1),
        endTime: new Date(),
      },
      featureInfo: {
        plateNo: "",
        plateColor: "",
        originPic: "",
        targetPic: "",
        urlList: [],
      },
      resultList: [], //列表
      selectmapShow: false,
    };
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    input(e, key) {
      this.featureInfo[key] = e;
    },
    handleRouterMaker() {
      if (this.resultList.length === 0 || this.resultList.length < 2) {
        this.$Message.warning("请添加2个以上目标");
        return;
      }
      if (!this.frequency.startTime || !this.frequency.endTime) {
        this.$Message.warning("请选择时间");
        return;
      }
      if (
        new Date(this.frequency.startTime).getTime() >
        new Date(this.frequency.endTime).getTime()
      ) {
        this.$Message.warning("开始时间不能大于结束时间");
        return;
      }
      if (
        new Date(this.frequency.endTime).getTime() -
          new Date(this.frequency.startTime).getTime() >
        this.dayRangeNum * DAYRANGE
      ) {
        this.$Message.warning(`开始和结束时间间隔不超过${this.dayRangeNum}天`);
        return;
      }
      this.$emit(
        "routerMaker",
        Object.assign(this.frequency, { result: this.resultList })
      );
    },
    handleReset() {
      this.resultList = [];
      this.frequency.plateNo = "";
      this.frequency.devices = [];
      this.featureInfo.targetPic = "";
      this.featureInfo.originPic = "";
      this.featureInfo.urlList = [];
    },
    /**
     * 删除
     */
    deleteRecord(index) {
      this.resultList.splice(index, 1);
    },
    addRecord() {
      if (
        this.resultList.findIndex(
          (item) =>
            item.plateNo == this.featureInfo.plateNo &&
            item.plateColor == this.featureInfo.plateColor
        ) != -1
      ) {
        this.$Message.warning("目标已存在");
        return;
      } //重复就过滤掉
      if (!this.featureInfo.plateColor && !this.featureInfo.plateNo) {
        this.$Message.warning("请输入车牌号和选择车牌颜色");
        return;
      }

      if (!this.featureInfo.plateNo) {
        this.$Message.warning("请输入精确车牌查询！");
        return;
      }
      const xny =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;

      if (!xny.test(this.featureInfo.plateNo)) {
        return this.$Message.warning("请输入精确车牌查询！");
      }
      // // 新能源
      // let xny =
      //   /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([A-HJ-K][A-HJ-NP-Z0-9][0-9]{4}$))/;

      // // 燃油车
      // let ryc =
      //   /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;

      // if (this.featureInfo.plateNo.length == 7) {
      //   if (!ryc.test(this.featureInfo.plateNo)) {
      //     this.$Message.warning("请输入精确车牌查询！");
      //     return;
      //   }
      // } else if (this.featureInfo.plateNo.length == 8) {
      //   if (!xny.test(this.featureInfo.plateNo)) {
      //     this.$Message.warning("请输入精确车牌查询！");
      //     return;
      //   }
      // } else {
      //   this.$Message.warning("请输入精确车牌查询！");
      //   return;
      // }

      this.resultList.push({
        originPic: this.featureInfo.originPic || "",
        plateNo: this.featureInfo.plateNo,
        plateColor: this.featureInfo.plateColor,
        targetPic: this.featureInfo.targetPic,
      });

      // 成功添加后，清空已添加的目标
      this.featureInfo.targetPic = "";
      this.featureInfo.originPic = "";
      this.featureInfo.urlList = [];
      this.featureInfo.plateNo = "";
      this.featureInfo.plateColor = "";
      this.$refs.bodyColor.handleCheckAll();
    },
    reUploadImg() {
      this.featureInfo.targetPic = "";
      this.featureInfo.originPic = "";
      this.featureInfo.urlList = [];
      this.$nextTick(() => {
        this.$refs.uploadImg.$el.getElementsByTagName("input")[0].click();
      });
    },
    delUploadImg() {
      this.featureInfo.targetPic = "";
      this.featureInfo.originPic = "";
      this.featureInfo.urlList = [];
    },
    // 选择设备
    handleSelectDevice() {
      this.$refs.selectDevice.show(this.frequency.devices);
    },
    // 框选设备
    mapToolVisibleHandlebar() {
      this.selectmapShow = true;
    },
    // 选择设备数据
    handleSelectData(list) {
      this.frequency.devices = [...list];
    },
    handleConfig(value) {
      this.selectmapShow = false;
      value.map((item) => {
        item.select = true;
      });
      let deviceIds = [];
      if (this.frequency.devices.length == 0) {
        deviceIds = value;
      } else {
        let deviceId = new Map(
          this.frequency.devices.map((item) => [item.deviceId, item])
        );
        value.map((item) => {
          if (!deviceId.get(item.deviceId)) {
            deviceIds.push(item);
          }
        });
      }
      this.frequency.devices = [...this.frequency.devices, ...deviceIds];
    },
    /**
     * 图片上传结果返回
     */
    imgUrlChange(list) {
      if (list[0]) {
        this.featureInfo.originPic = list[0].fileUrl;
        this.featureInfo.targetPic = list[0].imageUrl;
        this.featureInfo.plateNo = list[0].detect?.plateNo || "";
        this.featureInfo.plateColor = list[0].detect?.plateColor || "";
        if (list[0].detect?.plateColor) {
          this.$refs[String(list[0].detect?.plateColor)][0].handleChange();
        } else {
          this.$refs.bodyColor.handleCheckAll();
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ivu-tag-select-option {
  margin-right: 5px !important;
}
.trait-add-container {
  text-align: center;
  .color-pick .item-text {
    display: none;
  }
  .xui-datepicker.xui-datepicker-style {
    width: 80%;
  }
  .xui-datepicker.xui-datepicker-style .date-input {
    width: 100%;
  }
  .skill_upload .upload_div {
    height: 275px;
  }
  .skill_upload {
    height: 300px;
    margin: 0px;
  }
  .uploader-container {
    height: 300px;
    position: relative;
    /deep/.ivu-upload {
      height: 100%;
      width: 100%;
    }
  }
  .trait-sub-container {
    position: relative;
    width: 100%;
    height: 92%;
    display: flex;
    padding: 30px 5%;
    grid-gap: 80px;
    text-align: left;
    justify-content: space-around;
  }
  width: 100%;
  height: 100%;
  background: url("~@/assets/img/searchIdentity.png") no-repeat;
  .bars-common {
    height: 36px;
    min-width: 150px;
    margin: 0px 10px;
  }
  .skill_upload_container {
    line-height: 15px;
    margin-left: 53px;
    margin-bottom: 20px;
    width: 280px;
    padding-top: 10px;
    margin-right: 10px;
  }
  .my-input-class {
    margin-top: 10px;
    width: 280px;
  }
  .tag-select-box {
    margin-top: 10px;
  }
  .skill_upload-self {
    position: relative;
  }
  .limit-tips {
    font-size: 12px;
    color: red;
  }
  .list {
    background: #fff;
    border: 1px dashed #2c86f8;
    width: 560px;
    padding: 10px 0px 10px 20px;
    position: relative;
  }
  .no-data-tips {
    text-align: center;
    margin-top: 50%;
    transform: translateY(-50%);
  }
  .skills_row {
    min-height: 56px;
    position: relative;
    padding: 5px 10px 0px 21px;
    .date {
      width: 200px;
    }
  }
  .skills_row > label {
    text-align: right;
    position: relative;
    margin-right: 5px;
  }
  .range {
    display: inline-block;
  }
  .check_device {
    display: inline-flex;
    .frame-selection {
      margin-left: 5px;
      i {
        font-size: 24px;
      }
    }
  }
  .required {
    color: red;
    font-style: normal;
    font-size: 12px;
    position: absolute;
    left: -8px;
    top: 0;
  }
  .filter-search-btn {
    width: 90%;
    margin-left: 0;
    margin: 10px auto 20px auto;
    border-radius: 0;
    display: block;
    height: 28px;
    line-height: 28px;
    font-size: 12px;
    color: rgb(255, 255, 255);
    text-align: center;
    background-color: rgb(202, 202, 202);
    box-shadow: 3px 3px 0 rgba(204, 204, 204, 0.5);
    background-color: rgb(45, 135, 249);
    cursor: pointer;
  }
  .add-button {
    position: absolute;
    color: rgb(45, 135, 249);
    right: -40px;
    font-size: 14px;
    bottom: 150px;
    cursor: pointer;
    top: 190px;
  }
  .bottom-tools {
    width: 560px;
    float: right;
    margin-right: 10%;
  }
  .uploader-img {
    p {
      position: absolute;
      top: 1px;
      right: 1px;
      width: 20px;
      height: 20px;
      border-radius: 10px;
      background: #e6e6e7;
      text-align: center;
      cursor: pointer;
      i {
        color: #fff;
        font-size: 14px;
        margin-top: 3px;
        line-height: 20px;
      }
      &:hover {
        background: #2c86f8;
      }
    }
    img {
      width: 100%;
      height: 100%;
    }
    .uploader-container;
  }
  .upload-img-btn {
    height: 100% !important;
    width: 100% !important;
  }
  .icon-reload {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    color: #fff;
  }
  .tools-bar {
    position: absolute;
    bottom: 0px;
    height: 24px;
    padding: 4px 0px;
    text-align: right;
    background: rgba(47, 43, 43, 0.51);
    left: 0px;
    right: 0px;
    padding: 0px 10px;
    span {
      width: 20px;
      text-align: center;
      height: 20px;
      top: 2px;
      padding: 2px;
      display: inline-block;
      &:hover {
        background: #2c86f8;
      }
    }
  }
}
</style>
