<template>
  <div>
    <Button type="primary" class="exportBtn mr-sm" @click="exportFn">
      <i class="icon-font icon-daochu f-12"></i>导出
    </Button>
    <div class="updata-time">
      <div class="search">
        <label for>组织机构</label>
        <api-organization-tree
          ref="orgCode"
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree"
          placeholder="请选择"
        ></api-organization-tree>
        <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
        <Button type="default" class="mr-sm" @click="resetSearch()">重置</Button>
        <!-- <Button type="default" class="mr-sm" @click="resetSearchDataMx(searchData, startSearch)">重置</Button> -->
      </div>
      <!-- 最新更新时间：2021-04-06 02:12:00 -->
    </div>
  </div>
</template>
<script>
import api2 from '@/config/api/car-threm.js';
import api from '@/config/api/vedio-threm.js';
export default {
  name: 'searchCom',
  props: {
    isOutput: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        deviceIds: [],
        type: '',
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {},
  methods: {
    // resetSearchDataMx() {
    // 	this.selectOrgTree.orgCode = null
    // 	this.searchData.orgCode = null
    // 	this.getVedioPage();
    // },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      // this.getVedioPage();
    },
    getVedioPage() {
      this.loading = true;
      this.searchData.reasonTypes = [6];
      this.$http.post(api.queryVideoResultPageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.loading = false;
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
      });
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetSearch() {
      delete this.searchData.orgCode;
      this.$refs.orgCode.reset();
      this.startSearch();
    },
    /**
     * responseType: 'blob'， 若有乱码问题用这个
     */
    exportFn() {
      var url = api2.carDataExport;

      var param = this.$parent.$parent.$parent.searchData;
      if (param.reasonTypes.length == 1) {
        if (param.reasonTypes[0] == 8) {
          url = api2.carOsdexport;
        } else {
          url = api2.carRealtimeHistoryExport;
        }
      }

      this.$http.post(url, param, { responseType: 'arraybuffer' }).then((res) => {
        if (res.status == 200) {
          let a = document.createElement('a');

          //ArrayBuffer 转为 Blob
          let blob = new Blob([res.data], {
            type: 'application/vnd.ms-excel',
          });

          let objectUrl = URL.createObjectURL(blob);
          a.setAttribute('href', objectUrl);

          var fileName = '导出数据';
          if (param.reasonTypes.length == 1) {
            if (param.reasonTypes[0] == 6) fileName = '实时视频流通畅检测';
            if (param.reasonTypes[0] == 7) fileName = '历史视频流通畅检测';
            if (param.reasonTypes[0] == 8) fileName = 'OSD字幕标注合规检测';
          }

          if (this.isOutput) fileName = '数据输出';
          let now = new Date(),
            year = now.getFullYear(),
            mon = now.getMonth() + 1,
            day = now.getDate(),
            hours = now.getHours(),
            min = now.getMinutes(),
            sec = now.getSeconds();
          var dataStr =
            '' +
            year +
            (mon < 10 ? '0' + mon : mon) +
            (day < 10 ? '0' + day : day) +
            '-' +
            (hours < 10 ? '0' + hours : hours) +
            (min < 10 ? '0' + min : min) +
            (sec < 10 ? '0' + sec : sec);

          a.setAttribute('download', '视频视图数据 - ' + fileName + ' - [iVDG] - [' + dataStr + '].xls');
          a.click();
        } else {
          this.$Message.error(res.data.msg);
        }
      });
    },
  },
  watch: {},
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.exportBtn {
  float: right;
  margin-top: 3px;
  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
</style>
