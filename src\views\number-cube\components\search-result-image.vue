<template>
  <div class="search-result-input">
    <ui-modal width="80%" v-model="modalShow" title="查询结果" @onOk="onOk">
      <div class="header">
        <div class="search-content">
          <div class="input-content">
            <Dropdown trigger="custom" :visible="visible" placement="bottom-start">
              <Input v-model="keyWords1" placeholder="请输入关键词检索，多个关键词请用空格隔开" class="search-input">
                <i class="iconfont icon-xiangji" slot="suffix" @click="pictureSearchHandle"></i>
                <Button type="primary" class="search-btn" slot="append" @click="toOrdinarySearchHandle">搜索</Button>
              </Input>
              <DropdownMenu slot="list">
                <search-pictures @cancel="visible = false" :picData="{ similarity: 66 }" @search="searchPictures" />
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
      <Row class="content">
        <Col :span="4" class="content-left">
          <div>全部实体</div>
          <p v-for="item in lableList" :key="item.Label" @click="lableClick(item)" :class="{ active: item.label === searchData.label }">
            <span class="title">{{ item.remark }}</span>
            <span class="number">{{ item.count }}</span>
          </p>
        </Col>
        <Col :span="14" class="content-center auto-fill">
          <UiTablePage ref="uiTablePage" :columns="this.columns()" :load-data="loadDataList" @on-selection-change="onSelectionChange">
            <template #image="{ row }">
              <img style="width: 50px; height: 50px" src="./people1.webp" :alt="row.name" />
            </template>
            <template #termOfValidity="{ row }">
              <span style="color: #2c86f8">{{ row.termOfValidity }}</span>
            </template>
          </UiTablePage>
        </Col>
        <Col :span="6" class="content-right">
          <div>
            <span
              >已选：<span>{{ selection.length }}</span
              >个</span
            >
            <span class="delete">清空</span>
          </div>
          <div v-for="item in selection" :key="item.id" class="table-tag">
            <img style="width: 50px; height: 50px" src="./people1.webp" alt="#" />
            <Icon type="md-close-circle" class="table-tag-icon" @click="deleteSelection(item)" />
          </div>
          <div class="footer-raios">
            <RadioGroup v-model="searchData.proximity">
              <Radio label="1"> 一度关系 </Radio>
              <Radio label="2"> 二度关系 </Radio>
              <Radio label="3"> 三度关系 </Radio>
            </RadioGroup>
          </div>
        </Col>
      </Row>
    </ui-modal>
  </div>
</template>
<script>
import imgloading from '@/assets/img/car1.webp'
import relationship from '@/assets/img/relationship.png'
import relationshipleft from '@/assets/img/relationship-left.png'
import relationshipright from '@/assets/img/relationship-right.png'
import { getGraphlLabel, getGraphlVertexByLabel } from '@/api/number-cube'
export default {
  components: {
    UiTablePage: require('@/components/ui-table-page').default,
    SearchPictures: require('./search-pictures.vue').default
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    keyWords: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      keyWords1: '',
      visible: false,
      modalShow: false,
      imgloading,
      relationship,
      relationshipleft,
      relationshipright,
      lableList: [],
      selection: [],
      searchData: { proximity: '1' },
      loadDataList: parameter => {
        return getGraphlVertexByLabel(Object.assign(parameter, this.searchData)).then(res => {
          res.data.entities = res.data.entities.map(val => {
            let obj = Object.assign(val.property, val)
            delete obj.property
            return obj
          })
          return res.data
        })
      }
    }
  },
  watch: {
    modalShow(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.modalShow = val
      if (val === true) {
        this.info()
      }
    }
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    columns() {
      if (this.searchData.label === '实名档案') {
        return [
          { type: 'selection', align: 'center', width: 40 },
          { type: 'index', width: 70, title: '序号', align: 'center' },
          { title: '图片', key: 'name', minWidth: 70, slot: 'image' },
          { title: '身份证号', key: 'idCardNo', minWidth: 140 },
          { title: '姓名', key: 'name', minWidth: 100 },
          { title: '相似度', key: 'termOfValidity', minWidth: 100, slot: 'termOfValidity' }
        ]
      } else {
        return [
          { type: 'selection', align: 'center', width: 40 },
          { type: 'index', width: 70, title: '序号', align: 'center' },
          { title: '图片', key: 'name', minWidth: 70, slot: 'image' },
          { title: '身份证号', key: 'idCardNo', minWidth: 140 },
          { title: '视频身份', key: 'name', minWidth: 100 },
          { title: '相似度', key: 'termOfValidity', minWidth: 100, slot: 'termOfValidity' }
        ]
      }
    },
    info() {
      getGraphlLabel({ texts: this.keyWords.split(',') }).then(res => {
        this.lableList = res.data || []
        this.searchData.label = this.lableList[0] ? this.lableList[0].label : ''
        this.searchData.texts = this.keyWords.split(',')
        this.lableList = [
          {
            count: 20,
            label: '实名档案',
            remark: '实名档案'
          },
          {
            count: 20,
            label: '视频档案',
            remark: '视频档案'
          }
        ]
        this.$refs.uiTablePage.info()
      })
    },
    lableClick(item) {
      this.searchData.label = item.label
      console.log(this.searchData, '====')
      this.$forceUpdate()
      this.$nextTick(() => {
        this.$refs.uiTablePage.info()
      })
    },
    onSelectionChange(selection) {
      this.selection = selection
    },
    onOk() {
      this.modalShow = false
      console.log(this.selection, '-')
      let query = {
        ids: this.selection
          .map(val => {
            return val.id
          })
          .join(','),
        proximity: this.searchData.proximity
      }
      this.$router.push({ name: 'number-cube-info', query: query })
    },
    pictureSearchHandle() {
      this.visible = !this.visible
    },
    toOrdinarySearchHandle() {},
    searchPictures() {}
  }
}
</script>
<style lang="less" scoped>
.search-result-input {
  .content {
    height: 582px;
    width: 100%;
    border: 1px solid #d3d7de;
    margin-top: 20px;
    & > .content-left {
      border-right: 1px solid #d3d7de;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.8);
      & > div {
        border-bottom: 1px solid #d3d7de;
        font-weight: bold;
        height: 40px;
        line-height: 40px;
        padding-left: 16px;
      }
      & > p {
        padding-left: 16px;
        padding-right: 16px;
        cursor: pointer;
        height: 34px;
        line-height: 34px;
        .number {
          color: #2c86f8;
          float: right;
        }
        &:hover {
          background: #2c86f8;
          color: #fff;
          .number {
            color: #fff;
          }
        }
      }
      .active {
        background: #2c86f8;
        color: #fff;
        .number {
          color: #fff;
        }
      }
    }
    & > .content-center {
      border-right: 1px solid #d3d7de;
      padding: 10px;
    }
    & > .content-right {
      & > div {
        border-bottom: 1px solid #d3d7de;
        height: 40px;
        line-height: 40px;
        padding-left: 10px;
        padding-right: 10px;
        margin-bottom: 2px;
        & > .delete {
          float: right;
          cursor: pointer;
        }
      }
      .table-tag {
        margin: 10px 0px 10px 10px;
        padding: 3px 10px;
        height: auto;
        line-height: normal;
        font-size: 14px;
        position: relative;
        overflow: inherit;
        cursor: pointer;
        display: inline-block;
        border-bottom: none;
        &:hover {
          .table-tag-icon {
            display: block;
          }
        }
        .table-tag-icon {
          color: #ea4a36;
          position: absolute;
          top: -11px;
          right: -7px;
          font-size: 17px;
          display: none;
        }
      }
      .footer-raios {
        border-bottom: 0px;
        border-top: 1px solid #d3d7de;
        position: absolute;
        bottom: 0px;
        width: 100%;
        /deep/ .ivu-radio-group {
          display: flex;
          justify-content: space-between;
        }
      }
    }
  }
  .header {
    .search-content {
      text-align: center;
      /deep/ .ivu-dropdown {
        width: 570px;
      }
      /deep/.ivu-input-suffix {
        right: 80px;
        left: none;
        z-index: 20;
        cursor: pointer;
      }
      /deep/ .header .ivu-icon-ios-close {
        top: 20px;
      }
    }
  }
}
.pl20 {
  padding-left: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pr6 {
  padding-right: 6px;
}
.pr30 {
  padding-right: 30px;
}
.inlineb {
  display: inline-block;
}
</style>
