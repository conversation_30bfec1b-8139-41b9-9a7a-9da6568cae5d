<template>
  <div class="search">
    <Form ref="form" :model="formData" class="form" inline>
      <FormItem label="任务名称:" prop="taskName" class="search-input">
        <Input
          placeholder="请输入"
          clearable
          v-model="formData.taskName"
          maxlength="50"
        />
      </FormItem>
      <FormItem
        :label="subTaskType == 'file' ? '文件名称:' : '设备名称:'"
        prop="resourceName"
        class="search-input"
      >
        <Input
          placeholder="请输入"
          clearable
          v-model="formData.resourceName"
          maxlength="50"
        />
      </FormItem>
      <FormItem
        label="算法名称:"
        prop="compareAlgorithmIds"
        class="search-input"
      >
        <Select
          v-model="formData.compareAlgorithmIds"
          clearable
          multiple
          :max-tag-count="1"
        >
          <Option
            :value="item.id"
            :key="item.id"
            v-for="item in algorithmList"
            >{{ item.name }}</Option
          >
        </Select>
      </FormItem>
      <FormItem
        :label="subTaskType == 'file' ? '文件类型:' : '处理类型:'"
        prop="taskDataType"
        class="search-input"
      >
        <Select v-model="formData.taskDataType" clearable multiple>
          <Option :value="0" :key="0">{{
            subTaskType == "file"
              ? "视频"
              : subTaskType == "real"
              ? "视频流"
              : "历史录像"
          }}</Option>
          <Option :value="1" :key="1">{{
            subTaskType == "file"
              ? "图片"
              : subTaskType == "real"
              ? "图片流"
              : "历史抓拍"
          }}</Option>
        </Select>
      </FormItem>
      <FormItem label="报警级别:" class="search-input">
        <alarmTabSelect
          :list="tagList"
          :selectLi="formData.alarmLevel"
          @selectChange="selectChange"
        ></alarmTabSelect>
      </FormItem>
      <div class="flex-b">
        <div>
          <FormItem label="任务状态:" prop="statusList" class="search-input">
            <Select
              v-model="formData.statusList"
              clearable
              multiple
              :max-tag-count="1"
            >
              <Option
                v-for="item in taskStatusList"
                :value="item.key"
                :key="item.key"
                placeholder="请选择"
                >{{ item.label }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="创建人员:" prop="creator" class="search-input">
            <Input
              placeholder="请输入"
              clearable
              v-model="formData.creator"
              maxlength="50"
            />
          </FormItem>
          <FormItem label="范围时间:" prop="daterange">
            <DatePicker
              style="width: 360px"
              v-model="daterange"
              type="datetimerange"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择自定义时间段"
              @on-change="dateChangeHandler"
              transfer
            >
            </DatePicker>
          </FormItem>
        </div>
        <FormItem class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
    </Form>
  </div>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import alarmTabSelect from "./alarm-tab-select.vue";
import hlDaterange from "@/components/hl-daterange/index.vue";
// 0未开启 1运行中 2暂停 3已完成
const taskStatusList = [
  { key: 0, label: "未开启" },
  { key: 1, label: "运行中" },
  // { key: 2, label: "等待" },
  { key: 3, label: "暂停" },
  { key: 4, label: "已完成" },
];
//
const tagList = [
  { value: -1, name: "全部" },
  { value: 1, name: "一级" },
  { value: 2, name: "二级" },
  { value: 3, name: "三级" },
];
export default {
  components: {
    hlDaterange,
    alarmTabSelect,
  },
  props: {
    subTaskType: {
      type: String,
      default: "",
    },
    algorithmList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tagList,
      taskStatusList,
      formData: {
        taskName: "",
        resourceName: "",
        compareAlgorithmIds: [],
        statusList: [],
        creator: "",
        startCreateTime: "",
        endCreateTime: "",
        taskDataType: [],
        taskLevelList: [],
        taskTypes: [],
        alarmLevel: -1,
      },
      daterange: [],
    };
  },
  computed: {},
  async created() {
    await this.getInitData();
  },
  methods: {
    ...mapActions({ getInitData: "multimodal/getInitData" }),
    // 查询
    startSearch() {
      this.$emit("searchForm", { ...this.formData });
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields();
      this.daterange = [];
      this.startSearch();
    },
    dateChangeHandler(val) {
      const startTime = val[0];
      const endTime = val[1] ? val[1].split(" ")[0] + " 23:59:59" : "";
      this.daterange = [startTime, endTime];
      this.formData.startCreateTime = startTime;
      this.formData.endCreateTime = endTime;
    },
    getSearchParam(type) {
      const param = { ...this.formData };
      param.taskLevelList =
        this.formData.alarmLevel == -1 ? [] : [this.formData.alarmLevel];
      return {
        ...param,
      };
    },
    selectChange(key) {
      this.formData.alarmLevel = key;
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;
  .form {
    width: 100%;
    .search-input {
      display: inline-flex;
      margin-right: 30px;
      /deep/ .ivu-input {
        width: 200px;
      }
      /deep/.ivu-select {
        width: 200px;
      }
    }
    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }
    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }
    .label-text {
      /deep/.ivu-form-item-label {
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines !important; // 这行必加，兼容ie浏览器
        width: 72px;
        white-space: nowrap;
      }
    }
    .btn-group {
      margin-right: 0;
    }
    .datepicker-wrap {
      display: flex;
      align-items: center;
      .line {
        height: 3px;
        width: 20px;
        background: #d2d8db;
        margin: 0 5px;
      }
      .hl-btn {
        color: #2c86f8;
        margin-left: 10px;
        cursor: pointer;
      }
      margin-right: 10px;
    }
  }
}
</style>
