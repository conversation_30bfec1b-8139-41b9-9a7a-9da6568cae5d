<template>
  <ul class="tag">
    <li
      :class="{ active: curTag == index }"
      v-for="(item, index) in list"
      :key="item.dataKey"
      @click="tagChange(index, item.dataKey)"
    >
      {{ item.dataValue }}
    </li>
  </ul>
</template>
<script>
export default {
  name: 'tasktracking',
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      curTag: 0,
    };
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      this.curTag = 0;
    },
    tagChange(index, value) {
      this.curTag = index;
      this.$emit('tagChange', value);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.tag {
  height: 34px;
  margin-right: 2px;
  // margin-top: 10px;
  li {
    float: left;
    text-align: center;
    color: #56789c;
    border: 1px solid #10457e;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
    height: 34px;
    padding: 0 10px;
    line-height: 34px;
    font-size: 14px;
    &:hover {
      background: var(--color-primary);
      cursor: default;
      color: #fff;
    }
  }
  .active {
    background: var(--color-primary);
    cursor: default;
    color: #fff;
  }
}
</style>
