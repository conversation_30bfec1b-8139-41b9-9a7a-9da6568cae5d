/*
 * @Date: 2025-02-12 14:39:11
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-03-31 11:25:54
 * @FilePath: \icbd-view\src\router\routers\juvenile-target-control.js
 */
/** When your routing table is too long, you can split it into small modules**/
import BasicLayout from "@/layouts/basic-layout";
import ArchivesLayout from "@/layouts/archives-layout";
// 未成年人档案
const juvenileArchivePath = "views/juvenile-archives";

export default [
  {
    path: "/juvenile-target-control2",
    name: "juvenile-target-control2",
    component: BasicLayout,
    children: [
      {
        path: "/juvenile-control-task/criminal-record/add",
        name: "juvenile-control-task-add",
        tabShow: true,
        parentName: "juvenile-target-control/juvenile-control-task",
        component: (resolve) =>
          require([
            "@/views/juvenile-target-control/juvenile-control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "新增布控",
        },
      },
      {
        path: "/juvenile-control-task/criminal-record/edit",
        name: "juvenile-control-task-edit",
        tabShow: true,
        parentName: "juvenile-target-control/juvenile-control-task",
        component: (resolve) =>
          require([
            "@/views/juvenile-target-control/juvenile-control-task/criminal-record/add.vue",
          ], resolve),
        meta: {
          title: "编辑布控",
        },
      },
      {
        path: "/juvenile-control-task/criminal-record/detail",
        name: "juvenile-control-task-detail",
        tabShow: true,
        parentName: "juvenile-target-control/juvenile-control-task",
        component: (resolve) =>
          require([
            "@/views/juvenile-target-control/juvenile-control-task/criminal-record/detail.vue",
          ], resolve),
        meta: {
          title: "布控详情",
        },
      },
    ],
  },
  {
    path: "/juvenile-data-storage2",
    name: "juvenile-data-storage2",
    component: BasicLayout,
    children: [
      {
        path: "/juvenile-data-storage/special-library/personnel-thematic-database",
        name: "juvenile-personnel-thematic-database",
        tabShow: true,
        parentName: "juvenile-data-storage/special-library",
        component: (resolve) =>
          require([
            "@/views/juvenile-data-storage/special-library/personnel-thematic-database/index.vue",
          ], resolve),
        meta: {
          title: "专题库-人像库",
        },
      },
    ],
  },
  {
    path: "/juvenile-archive",
    name: "juvenile-archive",
    component: ArchivesLayout,
    redirect: "/juvenile-archive/people-dashboard",
    meta: {
      title: "未成年人档案",
      icon: "icon-shouye",
    },
    children: [
      {
        path: "/juvenile-archive/people-dashboard",
        name: "juvenile-dashboard",
        component: (resolve) =>
          import("@/" + juvenileArchivePath + "/dashboard"),
        meta: {
          title: "人员概览",
          tagShow: true,
        },
      },
      {
        path: "/juvenile-archive/people-profile",
        name: "juvenile-profile",
        component: (resolve) => import("@/" + juvenileArchivePath + "/profile"),
        meta: {
          title: "人员资料",
          tagShow: true,
        },
      },
      {
        path: "/juvenile-archive/people-activity-track",
        name: "juvenile-activity-track",
        component: (resolve) =>
          import("@/" + juvenileArchivePath + "/activity-track"),
        meta: {
          title: "活动轨迹",
          tagShow: true,
        },
      },
      {
        path: "/juvenile-archive/people-relationship-map",
        name: "juvenile-relationship-map",
        component: (resolve) =>
          import(
            "@/views/holographic-archives/one-person-one-archives/people-archive/relationship-map"
          ),
        meta: {
          title: "关系图谱",
          tagShow: true,
        },
      },
    ],
  },
];
