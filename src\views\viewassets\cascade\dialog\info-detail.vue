<template>
  <ui-modal v-model="visible" title="信息详情" :styles="styles" footer-hide>
    <loading v-if="dataLoading" />
    <Collapse v-model="value1">
      <Panel name="1">
        <div class="header"><i class="icon1 icon-font icon-jichushuxing"></i>基础属性</div>
        <Row slot="content">
          <Col class="layout" span="12" v-for="(item, index) in dataList" :key="index">
            <div class="name" :class="item.required ? 'required' : ''">{{ item.name }}：</div>
            <div class="value">
              <template v-if="!['deviceStatus', 'checkStatus', 'buildStatus'].includes(item.value)">
                {{ item.label }}</template
              >
              <span
                v-if="item.value === 'deviceStatus'"
                class="build"
                :class="[item.label === '1' ? 'font-green' : 'font-red']"
              >
                {{ item.label === '1' ? '可用' : '不可用' }}
              </span>
              <span
                v-if="item.value === 'checkStatus'"
                class="check-status"
                :class="[item.label === '0' ? 'bg-failed' : 'bg-success']"
              >
                {{ item.label === '0' ? '异常' : '合格' }}
              </span>
              <span
                v-if="item.value === 'buildStatus'"
                class="build"
                :class="[item.label === '0' ? 'font-red' : 'font-green']"
              >
                {{ item.label === '1' ? '已建档' : '未建档' }}
              </span>
            </div>
          </Col>
        </Row>
      </Panel>
      <!--    <Panel name="2">
            <div class="header">
              <i class="icon2 icon-font icon-zhuangtaishuxing"></i>状态属性
            </div>
            <p slot="content"></p>
          </Panel>
          <Panel name="3">
            <div class="header">
              <i class="icon3 icon-font icon-guanlishuxing"></i>管理属性
            </div>
            <p slot="content"></p>
          </Panel>
          <Panel name="4">
            <div class="header">
              <i class="icon4 icon-font icon-biaoqianshuxing"></i>标签属性
            </div>
            <p slot="content"></p>
          </Panel>
          <Panel name="5">
            <div class="header">
              <i class="icon5 icon-font icon-anquanshuxing"></i>安全属性
            </div>
            <p slot="content"></p>
          </Panel>
          <Panel name="6">
            <div class="header">
              <i class="icon6 icon-font icon-zhongfutuxiangshibiechuli"></i>现场照片
            </div>
            <p slot="content"></p>
          </Panel>-->
    </Collapse>
  </ui-modal>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      dataLoading: false,
      styles: {
        width: '6rem',
      },
      dataList: [
        {
          name: `${this.global.filedEnum.deviceId}`,
          value: 'deviceId',
          required: true,
        },
        {
          name: `${this.global.filedEnum.deviceName}`,
          value: 'deviceName',
          required: true,
        },
        { name: `${this.global.filedEnum.macAddr}`, value: 'macAddr' },
        { name: `${this.global.filedEnum.ipAddr}`, value: 'ipAddr' },
        { name: `${this.global.filedEnum.longitude}`, value: 'longitude' },
        { name: `${this.global.filedEnum.latitude}`, value: 'latitude' },
        { name: '组织机构', value: 'gldw' },
        { name: '行政区划', value: 'civilCode' },
        { name: '功能类型', value: 'sbgnlxText' },
        { name: '点位类型', value: 'sbdwlxText' },
        { name: '采集区域', value: 'sbcjqyText' },
        { name: this.global.filedEnum.phyStatus, value: 'deviceStatus' },
        { name: '检测状态', value: 'checkStatus' },
        { name: '建档状态', value: 'buildStatus' },
        { name: '首次检测时间', value: 'fristCheckTime' },
        { name: '最新检测时间', value: 'recentlyCheckTime' },
        { name: '首次档案更新时间', value: 'fristBuildTime' },
        { name: '最新档案更新时间', value: 'recentlyBuildTime' },
        { name: '首次接受时间', value: 'firstReceiveTime' },
        { name: '最新接受时间', value: 'recentlyReceiveTime' },
        { name: '创建时间', value: 'createTime' },
      ],
      value1: '1',
    };
  },
  methods: {
    show(row) {
      this.visible = true;
      this.dataLoading = true;
      this.init(row);
    },
    async init(row) {
      try {
        // let deviceId = row.deviceId;
        // let res = await this.$http.get(cascade.deviceDetailInfoByDeviceId + deviceId)
        let detailData = row;
        this.dataList.forEach((item) => {
          item.label = detailData[item.value] || '--';
        });
        this.$set(this, 'dataList', this.dataList);
        this.dataLoading = false;
      } catch (err) {
        console.error(err);
        this.dataLoading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.layout {
  display: flex;
  color: var(--color-content);
  padding: 10px 0;

  .name {
    width: 200px;
    text-align: right;
    padding-right: 5px;
    color: var(--color-label);

    &.required {
      &:before {
        content: '*';
        color: var(--color-failed);
        margin-right: 3px;
      }
    }
  }

  .value {
    flex: 1;
    color: var(--color-content);
  }
}

.header {
  color: var(--color-content);
}

/deep/ .icon-font {
  font-size: 14px;
  margin-right: 10px;
}

/deep/ .ivu-icon-ios-arrow-forward {
  float: right;
  margin-top: 12px;
  color: var(--color-collapse-arrow);
}

/deep/ .ivu-collapse {
  border: 0;
  background: var(--bg-content);
}

/deep/ .ivu-collapse-header {
  border: 0 !important;
  color: var(--color-content);
}

/deep/ .ivu-collapse-item {
  background: var(--bg-collapse-item);
  border: 0 !important;
  margin-bottom: 6px;
  color: var(--color-content);
}

/deep/ .ivu-collapse-content {
  background: var(--bg-content);
}
</style>
