<template>
  <ui-modal
    ref="modal"
    v-model="visible"
    title="数据来源配置"
    class="modal"
    @onCancel="onCancel"
    @onOk="onOK"
  >
    <Search @searchForm="searchForm" ref="search" />
    <div class="count">
      <div class="cancle" @click="cancleSelect()">取消选择</div>
      <div>
        <span class="m-r18">已选</span>
        <span class="m-r18"
          >资源层<span class="blue"
            >（ {{ selectSize.resourceSize }} ）</span
          ></span
        >
        <span class="m-r18"
          >归档层<span class="blue">（ {{ selectSize.fileSize }} ）</span></span
        >
        <span class="m-r18"
          >业务层<span class="blue"
            >（ {{ selectSize.businessSize }} ）</span
          ></span
        >
      </div>
    </div>
    <Table
      class="auto-fill table"
      :columns="columns"
      border
      :loading="loading"
      :data="tableData"
      height="380"
    >
      <template #fieldNameCn="{ index }">
        <Input
          placeholder="请输入"
          v-model="tableData[index].fieldNameCn"
          maxlength="50"
          class="input-wid"
        ></Input>
      </template>
      <template #1="{ row, index }">
        <Checkbox
          v-model="row[1]"
          :disabled="row.disabled1"
          @on-change="checkboxChange(row, 1, index)"
        ></Checkbox>
      </template>
      <template #2="{ row, index }">
        <Checkbox
          v-model="row[2]"
          :disabled="row.disabled2"
          @on-change="checkboxChange(row, 2, index)"
        ></Checkbox>
      </template>
      <template #3="{ row, index }">
        <Checkbox
          v-model="row[3]"
          :disabled="row.disabled3"
          @on-change="checkboxChange(row, 3, index)"
        ></Checkbox>
      </template>
      <template #4="{ row, index }">
        <Checkbox
          v-model="row[4]"
          :disabled="row.disabled4"
          @on-change="checkboxChange(row, 4, index)"
        ></Checkbox>
      </template>
      <template #5="{ row, index }">
        <Checkbox
          v-model="row[5]"
          :disabled="row.disabled5"
          @on-change="checkboxChange(row, 5, index)"
        ></Checkbox>
      </template>
      <template #6="{ row, index }">
        <Checkbox
          v-model="row[6]"
          :disabled="row.disabled6"
          @on-change="checkboxChange(row, 6, index)"
        ></Checkbox>
      </template>
    </Table>
    <!-- 分页 -->
    <ui-page
      :current="pageInfo.pageNumber"
      :total="pageInfo.total"
      :page-size="pageInfo.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    >
    </ui-page>
    <!-- <ui-empty v-if="tableData.length < 1"></ui-empty> -->
  </ui-modal>
</template>

<script>
import { mapGetters, mapActions } from "vuex";
import { queryTableList, paramUpdate } from "@/api/config";
import Search from "./search";
export default {
  name: "UiConfirm",
  components: {
    Search,
  },
  props: {},
  data() {
    return {
      visible: false,
      loading: false,
      tableList: [
        { id: 1, fieldName: "测试" },
        { id: 2, fieldName: "测试2" },
        { id: 3, fieldName: "测试3" },
      ],
      oldData: [],
      tableData: [],
      selectTableData: [],
      allselectData: [],
      selectSize: {
        businessSize: 0,
        fileSize: 0,
        resourceSize: 0,
      },
      columns: [
        {
          title: "序号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "表名称", key: "sourceTable" },
        { title: "表注释", key: "sourceTableCn" },
      ],
      pageInfo: {
        pageNumber: 1,
        pageSize: 10,
        total: 0,
      },
      taskTypeList: [
        { dataKey: "1", dataValue: "警务数据" },
        { dataKey: "2", dataValue: "视图数据" },
        { dataKey: "3", dataValue: "感知数据 " },
      ],
      single: false,
      tableDataSourcesConfig: [
        { sourceTableId: 1, dataSourceKey: "1" },
        { sourceTableId: 2, dataSourceKey: "2" },
        { sourceTableId: 3, dataSourceKey: "3" },
      ],
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      datasourceTypeList: "dictionary/getDatasourceType", //数据来源
    }),
  },
  created() {},
  methods: {
    ...mapActions({}),

    // 初始化
    async show(row) {
      console.log("已有数据", row, JSON.parse(row.paramValue));
      if (row.paramValue) {
        // this.oldData = JSON.parse(row.paramValue).tableDataSourcesConfig
        // console.log('this.oldData', this.oldData)
        var { tableDataSourcesConfig, selectSize } = JSON.parse(row.paramValue);
        // console.log('---已有数据', JSON.parse(row.paramValue))
        this.allselectData = tableDataSourcesConfig || [];
        this.selectSize = selectSize || {
          businessSize: 0,
          fileSize: 0,
          resourceSize: 0,
        };
      }
      this.visible = true;
      this.columns = [
        {
          title: "序号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "表名称", key: "sourceTable" },
        { title: "表注释", key: "sourceTableCn" },
      ];
      this.datasourceTypeList.forEach((item) => {
        this.columns.push({
          title: item.dataValue,
          select: false,
          // num: `（20）`,
          slot: item.dataKey,
          renderHeader: (h, params) => {
            // console.log('params---', params)
            return h("div", [
              h("Checkbox", {
                props: {
                  value: params.column.select,
                  ref: "checkbox" + item.dataKey,
                },
                on: {
                  "on-change": (e) => {
                    this.topCheckboxChange(params, e);
                  },
                },
              }),
              h("strong", params.column.title),
              h("span", { style: { color: "#2C86F8" } }, params.column.num),
            ]);
          },
        });
      });
      this.queryTableList();
    },
    /**
     * 表格
     */
    async queryTableList() {
      this.loading = true;
      var param = {
        ...this.pageInfo,
        ...this.$refs.search.form,
        sourceType: "1",
      };
      await queryTableList(param)
        .then((res) => {
          // console.log('数据表查询结果', res)
          this.tableData = res.data.entities;
          this.pageInfo.total = res.data.total;

          if (this.allselectData.length > 0) {
            // 已配置
            console.log("---***", this.allselectData, this.tableData);
            this.tableData.forEach((item) => {
              this.datasourceTypeList.forEach((ite) => {
                var obj = this.allselectData.find((o) => {
                  return o.sourceTableId == item.sourceTableId;
                });
                if (
                  obj &&
                  ite.dataKey == obj.dataSourceKey &&
                  obj.dataSourceKey != ""
                ) {
                  item[ite.dataKey] = true;
                  this.$set(item, "disabled" + ite.dataKey, false);
                } else if (obj && obj.dataSourceKey != ite.dataKey) {
                  item[ite.dataKey] = false;
                  this.$set(item, "disabled" + ite.dataKey, true);
                } else {
                  item[ite.dataKey] = false;
                  this.$set(item, "disabled" + ite.dataKey, false);
                }
              });
            });
          } else {
            // 未配置
            this.tableData.forEach((item) => {
              this.datasourceTypeList.forEach((ite) => {
                item[ite.dataKey] = false;
                this.$set(item, "disabled" + ite.dataKey, false);
              });
            });
          }
          // console.log('表数据...', this.tableData)
        })
        .finally(() => {
          this.loading = false;
        });
      this.tableHeaderSelectStatus();
    },
    /**
     * 检查状态
     */
    checkStatus(val) {
      var arr = this.tableDataSourcesConfig.filter((item) => {
        return item.dataSourceKey == val;
      });
      if (arr.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    init() {
      this.visible = true;
    },
    // 查询
    searchForm(search) {
      const { resourceNameCn, resourceName, catalogId } = search;
      this.pageForm = {
        resourceNameCn: resourceNameCn,
        resourceName: resourceName,
        catalogId: catalogId ? catalogId.toString().split(",") : [], //入参要求数组
      };
      this.pageInfo.pageNumber = 1;
      this.queryTableList();
    },
    onCancel() {
      this.visible = false;
    },
    onOK() {
      // this.visible = false
      // console.log('table数据', this.tableData)
      var tableDataSourcesConfig = [];
      this.tableData.forEach((item) => {
        var obj = {
          sourceTableId: item.sourceTableId,
        };
        var val = "";
        this.datasourceTypeList.forEach((ite) => {
          if (item[ite.dataKey]) {
            val = ite.dataKey;
          }
        });
        obj.dataSourceKey = val;
        tableDataSourcesConfig.push(obj);
      });

      var param = {
        paramKey: "ICBD_TABLE_DATASOURCES_CONFIG",
        paramType: "icbd",
        paramValue: JSON.stringify({
          tableDataSourcesConfig: this.allselectData,
          selectSize: this.selectSize,
        }),
      };
      paramUpdate(param).then((res) => {
        this.$Message.success("修改成功");
        this.visible = false;
        this.$emit("refresh");
      });
      // console.log('最终提交数据', tableDataSourcesConfig)
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryTableList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryTableList();
    },
    /**
     * 表格中复选框发生变化
     * @param {*} row
     * @param {*} val
     * @param {*} index
     */
    checkboxChange(row, val, index) {
      // console.log('复选框发生了变化...', row)
      if (row[val]) {
        // 选中
        this.datasourceTypeList.forEach((item) => {
          if (item.dataKey != val) {
            row["disabled" + item.dataKey] = true;
            // this.$set(row, 'disabled'+item.dataKey, true)
          } else {
            // this.$set(row, 'disabled'+item.dataKey, false)
            row["disabled" + item.dataKey] = false;
          }
        });
        var obj = { sourceTableId: row.sourceTableId, dataSourceKey: val };
        this.allselectData.push(obj);
      } else {
        // 取消选中
        this.datasourceTypeList.forEach((item) => {
          row["disabled" + item.dataKey] = false;
          // this.$set(row, 'disabled'+item.dataKey, false)
        });
        this.allselectData = this.allselectData.filter((item) => {
          return item.sourceTableId != row.sourceTableId;
        });
      }

      // this.tableData[index] = row
      // this.$forceUpdate()
      this.selectSize.businessSize = this.allselectData.filter((item) => {
        return item.dataSourceKey == "3";
      }).length;
      this.selectSize.fileSize = this.allselectData.filter((item) => {
        return item.dataSourceKey == "2";
      }).length;
      this.selectSize.resourceSize = this.allselectData.filter((item) => {
        return item.dataSourceKey == "1";
      }).length;
      this.tableData[index] = row;
      this.tableHeaderSelectStatus();
    },
    tableHeaderSelectStatus() {
      var type1 = true;
      var type2 = true;
      var type3 = true;
      this.tableData.forEach((item) => {
        // console.log(item)
        if (type1) {
          if (!item[1] && !item.disabled1) {
            type1 = false;
          }
        }
        if (type2) {
          if (!item[2] && !item.disabled2) {
            type2 = false;
          }
        }
        if (type3) {
          if (!item[3] && !item.disabled3) {
            type3 = false;
          }
        }
      });
      //   console.log('this.columns---', this.columns)
      if (type1) {
        this.columns[3].select = true;
      } else {
        this.columns[3].select = false;
      }
      if (type2) {
        this.columns[4].select = true;
      } else {
        this.columns[4].select = false;
      }
      if (type3) {
        this.columns[5].select = true;
      } else {
        this.columns[5].select = false;
      }
    },
    /**
     * 表格标题复选框发生变化
     * @param {*} param 当前选择对象
     * @param {*} state 选中状态
     */
    topCheckboxChange(param, state) {
      //   console.log('--------change--------', param, state)
      var type = param.column.slot;
      this.tableData.forEach((item, index) => {
        // console.log('item', item)
        if (state) {
          if (!item[type] && !item["disabled" + type]) {
            item[type] = true;
            this.checkboxChange(item, type, index);
          }
        } else {
          if (item[type]) {
            item[type] = false;
            this.checkboxChange(item, type, index);
          }
        }
      });
    },
    cancleSelect() {
      this.selectSize = { businessSize: 0, fileSize: 0, resourceSize: 0 };
      this.tableData.forEach((item) => {
        this.datasourceTypeList.forEach((ite) => {
          item[ite.dataKey] = false;
          this.$set(item, "disabled" + ite.dataKey, false);
        });
      });
      this.columns[2].select = false;
      this.columns[3].select = false;
      this.columns[4].select = false;
      this.allselectData = [];
    },
  },
  beforeDestroy() {},
};
</script>

<style lang="less" scoped>
.modal {
  /deep/.ivu-modal {
    width: 1300px !important;
  }
  .content-wrapper {
    height: 600px;
    width: 1270px;
    .map {
      width: 100%;
      height: 100%;
    }
  }
  .search-input {
    width: 400px;
    margin-bottom: 10px;
    /deep/.ivu-input {
      width: 400px;
    }
    /deep/.ivu-icon-ios-search {
      color: #fff;
    }
  }
}
.count {
  border: 1px solid #2c86f8;
  background: #e8f2ff;
  margin-bottom: 20px;
  height: 36px;
  line-height: 36px;
  padding: 0 20px;
  .cancle {
    float: right;
    height: 36px;
    color: #2c86f8;
    cursor: pointer;
  }
  .m-r18 {
    margin-right: 18px;
  }
  .blue {
    color: #2c86f8;
  }
}
</style>
