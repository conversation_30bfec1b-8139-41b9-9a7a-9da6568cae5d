<!--
    * @FileDescription: 人员档案 - 实名档案
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="container">
        <div class="person">
            <!-- 查询 -->
            <Search @searchForm="searchForm" :queryParam.snyc="queryParam" :searchText="'高级检索'" />
            <div class="card-content">
                <div v-for="(item, index) in list" :key="index" class="card-item">
                    <custom-list
                        :index="index"
                        :cardData='item'
                        :cardColumns="cardColumns"
                        @click="handleDetail"  
                    >
                    </custom-list>
                </div>
                <ui-empty v-if="list.length === 0 && !loading"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </div>
            <!-- 分页 -->
            <ui-page :current="pageInfo.pageNumber" countTotal :showElevator="false" :total="total" :page-size="pageInfo.pageSize" @pageChange="pageChange" @pageSizeChange="pageSizeChange"></ui-page>
        </div>
        <details-modal v-if="detailsShow" ref="detailsModal" @close="detailsShow = false"></details-modal>    
    </div>
</template>
<script>
import CustomList from './components/custom-list';
import Search from './components/search';
import { queryBaseLibPersonPageList } from '@/api/person-archives-bb';
import detailsModal from './components/details-modal';
import { mapActions } from 'vuex';
export default {
    components: {
        CustomList,
        Search,
        detailsModal
    },
    data() {
        return {
            queryParam: {
                name: '',
                idcardNo: '',
                sex: '',
                source: '',
                phoneNo: '',
                ageStart: '',
                ageEnd: '',
            },
            pageInfo:{
                pageNumber:1,
                pageSize: 10
            },
            total: 0,
            list: [],
            loading: false,
            cardColumns:[
                {
                    title: 'name',
                    key: 'name',
                    icon: 'icon-xingming',
                },
                {
                    title: 'sex',
                    key: 'sex',
                    icon: 'icon-xingbie2',
                },
                {
                    title: 'age',
                    key: 'age',
                    icon: 'icon-time',
                },
                {
                    title: 'phrator',
                    key: 'phrator',
                    icon: 'icon-minzu1',
                },
                {
                    title: 'phoneNo',
                    key: 'phoneNo',
                    icon: 'icon-juhe',
                },
                {
                    title: 'idCardNo',
                    key: 'idCardNo',
                    icon: 'icon-shenfenzheng',
                },
            ],
            detailsShow: false,
        }
    },
    created() {
        this.getDictData();
        this.getList();
    },
    methods:{
        ...mapActions({
            getDictData: 'dictionary/getDictAllData'
        }),
        getList() {
            this.loading = true;
            let source = this.queryParam.source ? [this.queryParam.source] : [];
			let params = {
				"baseLibId": '',
				'confidenceSourceList': source,
				'phoneNo': this.queryParam.phoneNo,
				'name': this.queryParam.name,
				'idCardNo': this.queryParam.idCardNo,
				'sex': this.queryParam.sex,
				'ageStart': this.queryParam.ageStart,
				'ageEnd': this.queryParam.ageEnd,
                ...this.pageInfo
			}
            queryBaseLibPersonPageList(params)
            .then(res => {
                this.list = res.data.entities.map((item) => {
                    item.cardColumns = [
                        ...this.cardColumns,
                    ];
                    item.tagList = [];
                    item.tagList.push(
                        {
                            'labelName': item.baseLibName,
                            'labelColor': '#8BC34A',
                            'labelId': item.baseLibId
                        }
                    )
                    return item
                })
                this.total = res.data.total
            })
            .finally(() => {
                this.loading = false;
            })
        },
        // 详情
        handleDetail(value) {
            this.detailsShow = true;
            this.$nextTick(() => {
                console.log(value, 'value')
                this.$refs.detailsModal.show(value);
            })
        },
        searchForm(form) {
            console.log(form, 'form')
        },
        // 改变页码
        pageChange(pageNumber) {
            this.pageInfo.pageNumber = pageNumber
            this.getList()
        },
        // 改变分页个数
        pageSizeChange(size) {
            this.pageInfo.pageSize = size;
            this.pageInfo.pageNumber = 1;
            this.getList()
        }
    }
}
</script>    
<style lang="less" scoped>
.person{
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    .card-content{
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        flex: 1;
        margin: 0 -5px;
        align-content: flex-start;
        position: relative;
        .card-item{
            width: 20%;
            padding: 0 5px;
            box-sizing: border-box;
            margin-bottom: 10px;
            transform-style: preserve-3d;
            transition: transform 0.6s;
        }
    }
}
</style>