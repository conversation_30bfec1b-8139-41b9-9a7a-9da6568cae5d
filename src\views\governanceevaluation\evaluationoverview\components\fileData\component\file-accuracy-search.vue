<template>
  <div class="base-search">
    <ui-label class="fl" label="抓拍时间" :width="70">
      <div class="date-picker-box">
        <DatePicker
          class="input-width mb-md"
          v-model="imageSearchData.beginTime"
          type="datetime"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, imageSearchData, 'beginTime')"
        ></DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="input-width"
          v-model="imageSearchData.endTime"
          type="datetime"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, imageSearchData, 'endTime')"
        ></DatePicker>
      </div>
    </ui-label>

    <ui-label class="fl ml-lg" label="抓拍设备" :width="70">
      <select-camera class @pushCamera="pushCamera" :device-ids="imageSearchData.deviceIds"></select-camera>
    </ui-label>
    <ui-label class="fl ml-lg" label="抓拍类型" :width="70">
      <Select v-model="imageSearchData.judgmentResult" class="width-md" clearable placeholder="请选择抓拍类型">
        <Option value="1">抓拍正确</Option>
        <Option value="2">准确性存疑</Option>
      </Select>
    </ui-label>
    <ui-label :width="30" class="fl" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx(imageSearchData, startSearch)">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    modular: {
      default: 1,
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    treeData: {
      type: Array,
      default() {},
    },
  },
  data() {
    return {
      imageSearchData: {
        deviceIds: [],
        beginTime: '',
        endTime: '',
        judgmentResult: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.imageSearchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.imageSearchData.beginTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
    };
  },
  created() {
    this.copySearchDataMx(this.imageSearchData);
  },
  methods: {
    pushCamera(list) {
      this.imageSearchData.deviceIds = list.map((row) => {
        return row.deviceId;
      });
    },
    startSearch() {
      this.$emit('startSearch', this.imageSearchData);
    },
  },
  watch: {},
  components: {
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.small-width {
  width: 70px;
}
.base-search {
  overflow: hidden;
  padding-bottom: 0;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
</style>
