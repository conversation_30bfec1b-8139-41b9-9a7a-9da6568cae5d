<template>
  <div>
    <ui-modal ref="modal" :title="title">
      <!-- 数据输入 -->
      <div v-if="curItem == 1">
        <ul class="ul">
          <li :class="{ active: curTag == 1 }" @click="tagChange(1)">数据类型</li>
          <li :class="{ active: curTag == 2 }" @click="tagChange(2)">更新方式</li>
        </ul>
        <div v-if="curTag == 1">
          <p class="blue">请选择待治理视频监控类型：</p>
          <CheckboxGroup>
            <Row>
              <Col span="10">
                <Checkbox>重点视频监控</Checkbox>
              </Col>
              <Col span="10">
                <Checkbox>普通视频监控</Checkbox>
              </Col>
            </Row>
          </CheckboxGroup>
        </div>
        <div class="update" v-if="curTag == 2">
          <div>1. 定期巡检</div>
          <div>
            检测时间
            <Select v-model="model1" style="width: 80px; margin-left: 10px">
              <Option>每天</Option>
              <Option>每周</Option>
              <Option>每月</Option>
            </Select>
            <Select v-model="model1" style="width: 80px; margin-left: 10px">
              <Option v-for="(item, index) in weekList" :key="index">{{ item.label }}</Option>
            </Select>
            <TimePicker format="HH:mm" placeholder="00:00" style="width: 90px; margin-left: 10px"></TimePicker>
          </div>
          <div>2. 设备数据变更</div>
          <div>设备数据变化，变化数据重新检测</div>
          <div>3. 检测流程变更</div>
          <div>检测流程变更，全量数据重新检测</div>
          <div>4. 手动触发</div>
          <div>手动触发全量数据重新检测</div>
        </div>
      </div>

      <!-- 图像上传及时性配置 -->
      <div class="history" v-if="curItem == 2">
        历史视频流
        <Input v-model="value" placeholder="数字天数 " style="width: 100px" size="small" />天
      </div>

      <template slot="footer">
        <Button type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import { weekList } from './video.js';
export default {
  name: 'videwDialog',
  props: {},
  data() {
    return {
      title: '',
      curItem: 1,
      curTag: 1,
      weekList: weekList,
    };
  },
  created() {},
  methods: {
    showModal(val) {
      switch (val) {
        case 1:
          this.title = '数据输入';
          break;
        case 2:
          this.title = '历史视频流通畅检测';
          break;
      }
      this.curItem = val;
      this.$refs.modal.modalShow = true;
    },

    tagChange(val) {
      this.curTag = val;
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.blue {
  color: var(--color-primary);
  margin: 10px 0;
  margin-top: 30px;
}

.ul {
  width: 500px;
  // border: 1px solid #999;
  overflow: hidden;
  // margin-left: 10px;

  li {
    float: left;
    // width: 100px;
    text-align: center;
    color: #fff;
    border: 1px solid #1b82d2;
    padding: 10px 20px;
    margin-right: -1px;
    border-radius: 2px;
    cursor: pointer;
  }
  .active {
    background: var(--color-primary);
    cursor: default;
  }
}

.update {
  margin-top: 30px;
  color: #fff;
  div {
    line-height: 36px;
  }
}

.history {
  color: #fff;
  .ivu-input-wrapper {
    margin: 0 10px;
  }
}
</style>
