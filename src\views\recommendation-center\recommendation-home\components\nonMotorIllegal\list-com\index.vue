<template>
  <div class="no-helmet-list">
    <div class="no-helmet-card">
      <noHelmetCard 
          class="noHelmetCard" 
          v-for="(item,index) in dataList" 
          :key="index"
          :itemInfo="item"
          @refresh="queryList"/>
      <ui-empty v-if="(!dataList || !dataList.length) && !loading" />
    </div>
    <ui-page
      :current="pageInfo.pageNumber"
      :total="total"
      countTotal
      :page-size="pageInfo.pageSize"
      :page-size-opts="[15, 30, 60, 120]"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    >
    </ui-page>
    <ui-loading v-if="loading" />
  </div>
</template>

<script>
import { nonMotorIllegalSearchList } from "@/api/recommend";
import noHelmetCard from './no-helmet-card.vue'

export default {
  components: { noHelmetCard },
  props: {
    // 违法类型（31：未佩戴带头盔，32：非机动车载人）
    illegalType: {
      type: Number,
      default: 31
    }
  },
  data() {
    return {
      loading: false,
      searchForm: {},
      dataList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 15,
      },
      total: 0
    }
  },
  methods: {
    searchHandle(searchForm) {
      this.searchForm = searchForm
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    /**
     *
     * @param {*} page 详情翻页
     */
    queryList(page = false) {
      this.dataList = [];
      this.loading = true;
      let params = {
        startDate: this.searchForm.startDate,
        endDate: this.searchForm.endDate,
        associationIdCardStatus: this.searchForm.associationIdCardStatus,
        idCardNo: this.searchForm.idCardNo,
        deviceIds: this.searchForm.selectDeviceList.map(v => v.deviceGbId),
        ...this.pageInfo,
        illegalType: this.illegalType,
      };
      nonMotorIllegalSearchList(params)
        .then((res) => {
          this.dataList = res.data.entities;
          this.total = res.data.total;
          this.$forceUpdate();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    }
  }
}
</script>

<style scoped lang="less">
.no-helmet-list {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .no-helmet-card {
      flex: 1;
      display: flex;
      overflow: auto;
      flex-wrap: wrap;
      position: relative;
      align-content: flex-start;
    }
    .noHelmetCard {
      width: ~'calc(20% - 10px)';
      margin-right: 10px;
      margin-bottom: 12px;
    }
  }
</style>