<template>
  <div class="middle-swiper-container">
    <swiper class="middle-swiper" ref="mySwiper" :options="swiperOption">
      <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_zhuapai"></i>抓拍记录</h2>
            <span class="number color-warning" v-if="relationship.capture.num">{{ relationship.capture.num }}</span>
          </div>
          <div class="info-content">
            <div v-if="relationship.capture.data">
              <p class="info-item"><i class="iconfont icon-time"></i>{{ relationship.capture.data.captureTime }}</p>
              <p class="info-item"><i class="iconfont icon-location"></i>{{ relationship.capture.data.address }}</p>
            </div>
            <p v-else class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_changqudi"></i>常去地</h2>
            <!-- <span class="text color-warning">晨曦科技园</span> -->
          </div>
          <div class="info-content">
            <div v-if="relationship.place.data">
              <p class="info-item"><i class="iconfont icon-camera"></i>抓拍 {{ relationship.place.data.amount }} 次</p>
              <p class="info-item"><i class="iconfont icon-location"></i>{{ relationship.place.data.address }}</p>
            </div>
            <p v-else class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
          </div>
        </div>
      </swiper-slide>
      <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_anjian"></i>关联案件</h2>
            <span class="number color-error" v-if="relationship.law.num">{{ relationship.law.num }}</span>
          </div>
          <div class="info-content " :class="{'has-hover': relationship.law.data}">
          <!-- <div class="info-content has-hover"> -->
            <div v-if="relationship.law.data">
              <p class="info-item"><i class="iconfont icon-case"></i>{{ relationship.law.data.jyaq }}</p>
              <p class="info-item"><i class="iconfont icon-time"></i>{{ relationship.law.data.sgfssj }}</p>
              <p class="info-item"><i class="iconfont icon-location"></i>{{ relationship.law.data.sgfsdd }}</p>
            </div>
            <p v-else class="no-content"><i class="iconfont icon-zanwushuju"></i>暂无数据</p>
          </div>
        </div>
      </swiper-slide>
      <!-- <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_baojing"></i>管控报警</h2>
            <span class="number color-error">101</span>
          </div>
          <div class="info-content">
            <p class="info-item"><i class="iconfont icon-time"></i>2020-03-17 17:39:12</p>
            <p class="info-item"><i class="iconfont icon-location"></i>天津天津市南开区人民路780号</p>
          </div>
        </div>
      </swiper-slide> -->
      <!-- <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_tongxing"></i>同行人员</h2>
            <span class="number color-blue">10</span>
          </div>
          <div class="info-content img-content">
            <div class="img-item">
              <span class="badge">身</span>
              <img src="@/assets/img/demo/face/07.jpg" alt="" />
              <p><span>蹇震春</span></p>
            </div>
            <div class="img-item">
              <span class="badge">身</span>
              <img src="@/assets/img/demo/face/08.jpg" alt="" />
              <p><span>未知</span></p>
            </div>
            <div class="img-item">
              <span class="badge">身</span>
              <img src="@/assets/img/demo/face/22.jpeg" alt="" />
              <p><span>未知</span></p>
            </div>
            <div class="img-item">
              <span class="badge">身</span>
              <img src="@/assets/img/demo/face/29.png" alt="" />
              <p><span>未知</span></p>
            </div>
          </div>
        </div>
      </swiper-slide> -->

      <!-- <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_guanxi"></i>同户籍</h2>
            <span class="number color-blue">3</span>
          </div>
          <div class="info-content img-content">
            <div class="img-item">
              <img src="@/assets/img/demo/face/06.jpg" alt="" />
              <p><span>丈夫</span></p>
            </div>
            <div class="img-item">
              <img src="@/assets/img/demo/face/03.jpg" alt="" />
              <p><span>父亲</span></p>
            </div>
            <div class="img-item">
              <img src="@/assets/img/demo/face/04.jpg" alt="" />
              <p><span>母亲</span></p>
            </div>
            <div class="empty"></div>
          </div>
        </div>
      </swiper-slide> -->
      <!-- <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_yichang"></i>异常行为</h2>
            <span class="text color-blue">与重点人同行</span>
          </div>
          <div class="info-content img-content">
            <div class="img-item">
              <img src="@/assets/img/demo/face/05.jpg" alt="" />
              <p><span>单辰祥</span></p>
            </div>
            <div class="img-item">
              <img src="@/assets/img/demo/face/02.jpg" alt="" />
              <p><span>章希发</span></p>
            </div>
            <div class="img-item">
              <img src="@/assets/img/demo/face/03.jpg" alt="" />
              <p><span>方瑞时</span></p>
            </div>
            <div class="img-item">
              <img src="@/assets/img/demo/face/03.jpg" alt="" />
              <p><span>方瑞时</span></p>
            </div>
          </div>
        </div>
      </swiper-slide> -->
      <!-- <swiper-slide>
        <div class="swiper-slide-content">
          <div class="item-header">
            <h2 class="title"><i class="icon icon_cheliang"></i>车辆违法</h2>
            <span class="number color-error">3</span>
          </div>
          <div class="info-content">
            <p class="info-item"><i class="iconfont icon-carcard"></i>苏A8888</p>
            <p class="info-item"><i class="iconfont icon-time"></i>2020-03-17 17:39:12</p>
          </div>
        </div>
      </swiper-slide> -->
      <div class="swiper-pagination" slot="pagination"></div>
    </swiper>
  </div>
</template>
<script>
import { swiper, swiperSlide } from 'vue-awesome-swiper'

export default {
  components: { swiper, swiperSlide },
  props: {
    // 卡片标题
    title: {
      type: String,
      default: ''
    },
    relationship: {
      type: Object,
      default: () => {}
    },
    // 卡片内部间距，单位 px
    padding: {
      type: Number,
      default: 20
    },
    // 禁用鼠标悬停显示阴影
    disHover: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      swiperResize: true,
      swiperOption: {
        slidesPerView: 3,
        slidesPerColumn: 2,
        spaceBetween: 20,
        pagination: {
          el: '.swiper-pagination',
          clickable: true
        }
      }
    }
  },
  computed: {
    swiper() {
      return this.$refs.mySwiper.swiper
    }
  },
  mounted() {},
  methods: {}
}
</script>
<style lang="less" scoped>
.middle-swiper-container {
  width: 100%;
  padding: 30px 90px 0;
  .middle-swiper {
    margin-left: auto;
    margin-right: auto;
    padding-bottom: 36px;
    .swiper-slide {
      &:hover {
        z-index: 100;
      }
    }
  }
  .swiper-pagination {
    bottom: 0;
    /deep/ .swiper-pagination-bullet {
      width: 70px;
      height: 6px;
      border-radius: 0;
      background: rgba(255, 255, 255, 0.3);
      opacity: 1;
      &.swiper-pagination-bullet-active {
        background: #2c86f8;
      }
    }
  }
}
/deep/ .swiper-slide-content {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  height: 104px;
  .item-header {
    display: flex;
    align-items: center;
    font-weight: bold;
    border-bottom: 1px solid #d3d7de;
    padding: 2px 6px;
    font-size: 16px;
    height: 42px;
    .title {
      display: flex;
      align-items: center;
      margin-right: 10px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
      .icon {
        width: 30px;
        height: 30px;
        background-repeat: no-repeat;
        background-size: 100% 100%;
        margin-right: 10px;
        &.icon_anjian {
          background-image: url('~@/assets/img/archives/icons/icon_anjian.png');
        }
        &.icon_baojing {
          background-image: url('~@/assets/img/archives/icons/icon_baojing.png');
        }
        &.icon_changqudi {
          background-image: url('~@/assets/img/archives/icons/icon_changqudi.png');
        }
        &.icon_cheliang {
          background-image: url('~@/assets/img/archives/icons/icon_cheliang.png');
        }
        &.icon_guanxi {
          background-image: url('~@/assets/img/archives/icons/icon_guanxi.png');
        }
        &.icon_tongxing {
          background-image: url('~@/assets/img/archives/icons/icon_tongxing.png');
        }
        &.icon_yichang {
          background-image: url('~@/assets/img/archives/icons/icon_yichang.png');
        }
        &.icon_zhuapai {
          background-image: url('~@/assets/img/archives/icons/icon_zhuapai.png');
        }
      }
    }
    .text,
    .number {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .number {
      font-size: 24px;
    }
  }
  .info-content {
    padding: 10px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    overflow: hidden;
    border-radius: 4px;
    height: 64px;
    position: relative;
    &.has-hover {
      &:after {
        content: '';
        position: absolute;
        bottom: -6px;
        right: -6px;
        width: 0;
        height: 0;
        border-bottom: 8px solid transparent;
        border-top: 8px solid #d8d8d8;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        transform-origin: center center;
        transform: rotate(-45deg);
      }
      &:hover {
        height: auto;
        &:after {
          transform: rotate(135deg);
          bottom: 6px;
          right: 6px;
        }
      }
    }
    .info-item {
      width: 100%;
      display: flex;
      background: #fff;
      align-items: center;
      line-height: 25px;
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      .iconfont {
        margin-right: 4px;
        font-size: 12px;
      }
    }
    &.img-content {
      display: flex;
      justify-content: space-between;
      .empty {
        width: 44px;
      }
      .img-item {
        width: 44px;
        height: 44px;
        overflow: hidden;
        position: relative;
        border-radius: 4px;
        border: 1px solid #d3d7de;
        img {
          width: 100%;
          height: 100%;
        }
        .badge {
          position: absolute;
          width: 21px;
          height: 21px;
          background: #2c86f8;
          border-radius: 4px;
          left: -3px;
          top: -3px;
          color: #fff;
          font-size: 12px;
          line-height: 21px;
          text-align: center;
          transform: scale(0.8);
        }
        p {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 13px;
          background: rgba(0, 0, 0, 0.8);
          text-align: center;
          line-height: 13px;
          span {
            color: #fff;
            font-size: 12px;
            float: left;
            width: 100%;
            transform: scale(0.8);
          }
        }
      }
    }
  }
  .no-content {
    width: 100%;
    padding-top: 10px;
    text-align: center;
    font-size: 14px;
    color: #d3d7de;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
      margin-right: 10px;
      font-size: 14px;
    }
  }
}
</style>
