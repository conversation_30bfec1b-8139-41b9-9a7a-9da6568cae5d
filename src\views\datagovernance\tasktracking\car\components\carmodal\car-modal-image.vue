<template>
  <div>
    <!-- echats插槽 -->
    <slot name="echarts"></slot>

    <!-- 检索框 -->
    <search-view ref="faceSearchRef" :exportErrTypeList="exportErrTypeList" @startSearch="startSearch"></search-view>
    <!-- 数量简介插槽 -->
    <slot name="Introduction"></slot>

    <tabsView v-if="curIndex == 6" :list="tabsList[searchData.isOptimize]" @selectInfo="selectInfo" />
    <!-- <tagView
			ref="tagView"
			v-if="curIndex == 6"
			:needExpand="false"
			:list="['异常数据列表', '治理优化数据列表']"
			@tagChange="tagChange"
			class="mt20 mb20"
		/>-->
    <tabs
      class="mt20"
      v-if="curIndex == 6"
      v-model="searchData.isOptimize"
      :list="['异常数据列表', '治理优化数据列表']"
      @tagChange="tagChange"
    />
    <!-- 切换 -->
    <tabs
      class="mt20"
      v-if="curIndexShow"
      v-model="searchData.isOptimize"
      :list="['未优化', '已优化']"
      @tagChange="tagChange"
    />
    <!-- list列表 -->
    <div :style="curIndex == 6 ? 'height:1.32rem;position:relative' : 'height:2.8rem;position:relative'">
      <loading v-if="loading" style="margin: 0 auto; align-items: center; display: flex; z-index: 12"></loading>
      <Row v-if="curIndex !== 6" :style="!loading ? styleScroll : styleHidden">
        <Col class="carItem" span="3" v-for="(item, index) in cardList" :key="item.id">
          <div class="item">
            <div class="img" @click="view(index, item.bigImagePath)">
              <ui-image :src="item.smallImagePath" />
              <p class="shadow-box" style="z-index: 11" v-if="infoModalShow" title="查看检测结果">
                <em
                  class="icon-font icon-yichang search-icon mr-xs base-text-color"
                  @click.stop="abnormalDetail(item)"
                ></em>
              </p>
            </div>
            <div class="group-message">
              <p class="marginP" :title="`抓拍时间：${item.bigImageShotTime}`">
                <em class="icon-font icon-shijian"></em>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.bigImageShotTime }}</span>
              </p>
              <p :title="item.deviceName">
                <em class="icon-font icon-dizhi"></em>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{
                  item.deviceName ? item.deviceName : '暂无数据'
                }}</span>
              </p>
            </div>
          </div>
        </Col>
        <div class="no-data" v-if="cardList.length === 0 && !loading">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </Row>
      <!-- 数据输出 -->
      <Row v-else :style="styleScroll6">
        <Col class="carItem" span="3" v-for="(item, index) in cardList" :key="item.id">
          <div class="item">
            <div class="img" @click="view(index, item.bigImagePath)">
              <ui-image :src="item.smallImagePath" />
              <p class="shadow-box" style="z-index: 11" v-if="infoModalShow" title="查看检测结果">
                <em
                  class="icon-font icon-yichang search-icon mr-xs base-text-color"
                  @click.stop="abnormalDetail(item)"
                ></em>
              </p>
            </div>
            <div class="group-message">
              <p class="marginP" :title="`抓拍时间：${item.bigImageShotTime}`">
                <em class="icon-font icon-shijian"></em>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{ item.bigImageShotTime }}</span>
              </p>
              <p :title="item.deviceName">
                <em class="icon-font icon-dizhi"></em>
                <span class="group-text inline vt-middle ml-xs ellipsis">{{
                  item.deviceName ? item.deviceName : '暂无数据'
                }}</span>
              </p>
            </div>
          </div>
        </Col>
        <div class="no-data" v-if="cardList.length === 0 && !loading">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
        </div>
      </Row>
    </div>
    <!-- 分页 -->
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    ></ui-page>
    <!-- 展示大图 -->
    <lookScene v-model="lookSceneVisable" class="viewer" ref="viewer" :imgList="images"></lookScene>
    <!-- 详情弹框 -->
    <carModalDialog
      ref="moreInfoModal"
      :title="titleChild"
      :curIndex="curIndex"
      :isImportantObj="tagCategoryMap"
      :propertyNameObj="propertyNameObj"
      :id="infoObj.id"
      :algorithmVendorType="algorithmMap"
    />
  </div>
</template>
<script>
import carThrem from '@/config/api/car-threm';
import governancetheme from '@/config/api/governancetheme';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'moreDialog',
  props: {
    infoModalShow: {
      type: Boolean,
      default: false, // 详情按钮
    },
    curIndexShow: {
      type: Boolean,
      default: false, // 是否展示changes
    },
    curIndex: {
      type: Number,
      default: 1, // 当前展示步骤
    },
    modalTitle: {
      type: String,
      default: '图像抓拍时间准确性检测', // 弹框名称
    },
    titleChild: {
      type: String,
      default: '抓拍时间异常', // 详情弹框名称
    },
    bigImgShow: {
      type: Boolean,
      default: false, // 是否可点击大图
    },
    tabsList: {
      type: Object,
      default() {
        return { '0': [], '1': [] }; // 数据输出类型
      },
    },
  },
  data() {
    return {
      modelTag: 0, // 设备模式,图像模式
      loading: false,
      lookSceneVisable: false,
      infoObj: {}, // 单个详情信息
      images: [],
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
        isOptimize: '0',
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll: {
        position: 'relative',
        width: '100%',
        'min-height': '1.8rem',
        'max-height': '2.8rem',
        display: 'flex',
        'flex-wrap': 'wrap',
        'overflow-y': 'scroll',
      },
      styleHidden: {
        position: 'relative',
        width: '100%',
        height: '2.8rem',
        display: 'flex',
        'flex-wrap': 'wrap',
        'overflow-y': 'hidden',
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '1.32rem',
        display: 'flex',
        'flex-wrap': 'wrap',
        'overflow-y': 'scroll',
      },
      cardList: [],
      propertyNameObj: {}, // 检测类型
      exportErrTypeList: [], // 异常类型存放， 导出专用
    };
  },
  created() {},
  computed: {
    ...mapGetters({
      tagCategoryMap: 'algorithm/tag_categoryobj',
      tagCategoryList: 'algorithm/tag_category',
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmMap: 'algorithm/algorithmVendorTypeobj',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 处理大图小图展示事件
    view(index, img) {
      // if (!this.bigImgShow) {
      // 	this.$Message.warning('大图URL错误或缺失')
      // 	return
      // }
      this.images = [img];
      this.$nextTick(() => {
        this.lookSceneVisable = true;
      });
    },
    // 获取字典字段
    async getDic() {
      if (this.algorithmList.length == 0) await this.getAlldicData();
      // 算法
      if (this.curIndex == 4 || this.curIndex == 5) {
        // 检测类型
        this.$http
          .post(governancetheme.standardList, {
            keyWord: '',
            propertyType: 3, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员
          })
          .then((res) => {
            if (res.data.code === 200) {
              res.data.data.map((val) => {
                this.propertyNameObj[val.propertyName] = val.propertyColumn;
              });
            }
          });
      }
    },
    // 初始化
    async showModal(boolen) {
      await this.getDic();
      if (boolen) {
        await this.searchRefalsh();
      }
      this.searchData.isOptimize = 0;
      if (this.curIndex != 6) {
        this.searchData.reasonTypes = [this.curIndex];
      } else {
        this.searchData.reasonTypes = [];
      }
      this.cardList = [];
      if (this.curIndexShow || this.curIndex == 6) {
        // this.$refs.tagView.curTag = 0
        this.searchData.isOptimize = 0;
      }
      this.loading = true;
      await this.infoList();
    },
    // tags切换
    tagChange(val) {
      if (val != this.searchData.isOptimize) {
        this.searchData.isOptimize = val;
        this.cardList = [];
        this.searchRefalsh();
        this.infoList();
      }
    },
    // 加载list
    async infoList() {
      const params = JSON.parse(JSON.stringify(this.searchData));
      // params.startTime = this.$refs.faceSearchRef.searchData.startTime;
      // params.endTime = this.$refs.faceSearchRef.searchData.endTime;
      if (params.isOptimize == '0' && this.curIndex == 6) {
        params.isOptimize = '';
      }
      // 用于loading加载高度样式问题（可删）
      this.loading = true;
      try {
        let res = await this.$http.post(carThrem.queryVehicleResultPageList, params);
        if (res.data.code === 200) {
          this.pageData.totalCount = res.data.data.total;
          this.cardList = res.data.data.entities || [];
        }
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      }
    },
    // 展开详情
    abnormalDetail(item) {
      if (this.curIndex == 6) {
        this.$emit('abnormalDetail', item, this.searchData);
        return;
      }
      this.infoObj = item;
      this.$nextTick(() => {
        this.$refs.moreInfoModal.open(this.curIndex);
      });
    },
    // 分页
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchRefalsh();
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    // 初始化数据
    searchRefalsh() {
      this.searchData.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
    },
    // 检索
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.searchRefalsh();
      this.infoList();
    },
    selectInfo(list) {
      this.searchData.reasonTypes = list.map((val) => {
        return val.dataKey;
      });
      this.exportErrTypeList = this.searchData.reasonTypes;
      this.cardList = [];
      this.searchRefalsh();
      this.infoList();
    },
  },
  watch: {
    value: {
      handler(val) {
        this.modalShow = val;
      },
      immediate: true,
    },
  },
  components: {
    // tagView: require('./tags').default,
    carModalDialog: require('./car-modal-dialog').default,
    lookScene: require('@/components/look-scene').default,
    searchView: require('./search.vue').default,
    tabsView: require('@/components/ui-select-tabs').default,
    uiImage: require('@/components//ui-image').default,
    tabs: require('@/views/datagovernance/tasktracking/components/tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  max-width: 188px;
  .item {
    height: 100%;
    background: #0f2f59;
    .img {
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }
  }
}

.group-message {
  margin: 0 auto;
  padding: 4px 15px 0 15px;
  color: #afbcd4;
  .group-text {
    width: 140px;
  }
  i {
    font-size: 12px;
  }
  p {
    display: flex;
  }
}
.marginP {
  display: flex;
  margin-bottom: 6px;
  margin-top: 4px;
}
.mr20 {
  margin-right: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mt20 {
  margin-top: 20px;
}
.page {
  padding-top: 21px;
}
</style>
