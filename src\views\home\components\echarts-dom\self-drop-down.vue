<template>
  <div class="self-drop-down">
    <Dropdown @on-click="chooseOne" trigger="click" placement="bottom-start">
      <a href="javascript:void(0)">
        <span class="drop-name ellipsis inline vt-middle">{{ activeName }}</span>
        <Icon type="ios-arrow-down"></Icon>
      </a>
      <template #list>
        <DropdownMenu>
          <DropdownItem v-for="item in dropdownList" :key="item.id" :name="item.id" class="ellipsis">
            {{ item.label }}</DropdownItem
          >
        </DropdownMenu>
      </template>
    </Dropdown>
  </div>
</template>
<script>
export default {
  props: {
    defaultActiveId: {},
    dropdownList: {},
  },
  data() {
    return {
      activeId: null,
    };
  },
  watch: {
    defaultActiveId: {
      handler(val) {
        if (!val) return;
        this.activeId = val;
      },
      immediate: true,
    },
  },
  mounted() {},
  computed: {
    activeName() {
      let one = this.dropdownList.find((item) => item.id === this.activeId);
      return one?.label ?? '';
    },
  },
  methods: {
    chooseOne(val) {
      this.activeId = val;
      this.$emit('chooseOne', val);
    },
  },
};
</script>

<style lang="less" scoped>
.self-drop-down {
  position: absolute;
  top: 12px;
  z-index: 999;
  left: 10px;
  .drop-name {
    width: auto;
    max-width: 150px;
  }
  @{_deep} .ivu-dropdown-item {
    color: #fff;
  }
  @{_deep} .ivu-select-dropdown {
    background-color: #011b37;
    transform-origin: unset !important;
    .ivu-dropdown-menu {
      overflow-y: scroll;
      height: 130px;
      min-width: 100px;
      max-width: 200px;
    }
  }
  @{_deep} .ivu-dropdown-item:hover {
    background: #023960;
    color: var(--color-primary) !important;
  }
}
</style>
