<!--
    * @FileDescription: 时空碰撞 - 对象详情
    * @Author: H
    * @Date: 2023/04/3
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="details-box" :class="{'details-box-pack': footerUpDown}">
        <div class="box-hint">
            <i class="iconfont icon-jiantou" @click="handleback"></i>
            <span @click="handleback">返回分析结果</span>
        </div>
        <div class="box-content">
            <div class="title">
                <div class="title-left">
                    <p>对象详情</p>
                </div>
                <Icon type="ios-close" @click="handleback" />
            </div>
            <ul class="box-ul">
                <li class="headline-li" v-for="(ite, ind) in dataList" :key="ind"
                    :class="{'headline-li-pack': packUpDown[ind].flexible, 'headline-li-pad': ind > 0}"
                >
                    <div class="box-li-title" @click="handleHeadline(ite, ind)">
                        <div class="box-li-title-left">
                            <div class="list_icon" :style="{border:`3px solid ${drawColor[ind]}`}"></div>
                            <p class="box-li-title-text">条件{{conditionNum[ite.conditionNo -1]}} <span>({{ite.num}})</span></p>
                        </div>
                        <p class="triangle" :class="{'active-triangle': !packUpDown[ind].flexible}"></p>
                    </div>
                    <ul class="child-ul">
                        <li class="box-li"
                            v-for="(item, index) in ite.countVoList" :key="index"
                            :class="{'box-li-pack': packUpDown[ind].children[index], 'box-li-packend': index == (ite.countVoList.length- 1)&&packUpDown[ind].children[index]}"
                        >
                            <div class="box-li-top">
                                <Icon type="md-radio-button-on"></Icon>
                            </div>
                            <div class="box-li-bottom">
                                <div class="time-title" @click="handletimelist(item, index, ind)">
                                    <p><span class="time-date">{{ item.date }}</span> <span class="time-num">{{item.count}}次</span></p>
                                    <p class="triangle" :class="{'active-triangle': !packUpDown[ind].children[index]}"></p>
                                </div>
                                <div class="child_list" v-for="(it, ind) in item[listName[tabType]]" :key="ind">
                                    <p class="sec-radio"></p>
                                    <div class="content-top" @click="handleListTrack(it, ind)">
                                        <div class="content-top-img">
                                            <img v-lazy="it.traitImg" alt="">
                                        </div>
                                        <div class="content-top-right">
                                            <span class="ellipsis">
                                                <ui-icon type="time" :size="14"></ui-icon>
                                                <span>{{ it.absTime }}</span>
                                            </span>
                                            <span class="ellipsis">
                                                <ui-icon type="location" :size="14"></ui-icon>
                                                <span>{{ it.captureAddress }}</span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </li>
                <ui-empty v-if="dataList.length === 0 && loading == false"></ui-empty>
                <ui-loading v-if="loading"></ui-loading>
            </ul>
        </div>
        <div class="footer" :class="{packArrow: footerUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ footerUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
import { queryFaceDetail, queryVehicleDetail  } from '@/api/modelMarket';
export default {
    name: '',
    components:{
            
    },
    props: {
        drawColor: {
            type: Array,
            default: () => []
        }
    },
    data () {
        return {
            dataList: [],
            total: 0,
            packUpDown: [],
            footerUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            detailRequest: {
                'face': queryFaceDetail, // 人脸
                'vehicle': queryVehicleDetail, // 车辆
            },
            listName: {
                'face': 'faceDtailVoList',
                'vehicle': 'vehicleDtailVoList'
            },
            loading: false,
            tabType: '',
            conditionNum: ['一', '二', '三', '四', '五'], 
            pointList: []
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(item, type) {
            this.loading = true;
            this.tabType = type;
            this.detailRequest[type](item)
            .then(res => {
                this.dataList = res.data;
                this.dataList.map(item => {
                    this.packUpDown.push({
                        flexible: true,
                        children: new Array(item.countVoList.length).fill(true)
                    })
                })
                let list = [];
                this.dataList.map((item, index) => {
                    let num = 0;
                    item.countVoList.map(item => {
                        num += item.count;
                        list.push(...item[this.listName[type]])
                    })
                    item.num = num;
                })
                list.forEach((item, index) => {
                    item.id = 'id'+index
                })
                this.pointList = list;
                this.$emit('list', list)
            })
            .finally(()=>{
                this.loading = false;
            })
        },
        handleListTrack(ite, ind) { 
            let listIndex;
            this.pointList.map((item, index) => {
                if(item.id == ite.id ) {
                    listIndex = index;
                }
            })
            this.$emit('chooseMapItem', listIndex)
        },
        handletimelist(item, index, ind) {
            this.$set(this.packUpDown[ind].children, index, !this.packUpDown[ind].children[index]);
            // if(!this.packUpDown[index] && !this.dataList[index].countVoList) {
                // this.handleTrack(item, index);
            // }
        },
        handleHeadline(item, index) {
            this.$set(this.packUpDown[index], 'flexible', !this.packUpDown[index].flexible);
        },
        handleback() {
            this.$emit('goback')
        },
        // 收缩、展开
        handlePackup() {
            this.footerUpDown = !this.footerUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
@import '../../components/style/timeLine';
.details-box{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    height: calc( ~'100% - 30px' );
    transition: height 0.2s ease-out;
    .box-hint{
        width: 370px;
        height: 40px;
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        filter: blur(0px);
        color: #2C86F8;
        font-size: 14px;
        line-height: 40px;
        padding-left: 14px;
        .icon-jiantou{
            transform: rotate(90deg);
            display: inline-block;
            cursor: pointer;
        }
        span{
           font-size: 14px;
           cursor: pointer;
           margin-left: 10px;
        }
    }
    .box-content{
        background: #FFFFFF;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        margin-top: 10px;
        height: calc( ~'100% - 50px' );
        // overflow-y: auto;
        .box-ul{
            overflow-y: auto;
            position: relative;
            padding: 0 10px 0px 19px;
            .headline-li{
                transition: height 0.2s ease-out;
                .triangle{
                    width: 0;
                    height: 0;
                    border-left: 5px solid transparent;
                    border-right: 5px solid transparent;
                    border-top: 5px solid #888888;
                    margin-left: 10px;
                    transform: rotate(0deg);
                    transition: transform 0.2s;
                }
                .active-triangle{
                    transform: rotate(180deg);
                    transition: transform 0.2s;
                }
            }
            .headline-li-pack{
                .child-ul{
                    height: 0px;
                    overflow: hidden;
                    transition: height 0.2s ease-out;
                }
            }
            .headline-li-pad{
                margin-top: 10px;
            }
            .box-li-title{
                cursor: pointer;
            }
        }
        .title{
            .title-left{
                display: flex;
                align-items: center;
                .title-icon{
                    position: relative;
                    background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
                    background-size: 100% 100%;
                    width: 20px;
                    height: 22px;
                    color: #EA4A36;
                    > span {
                        position: absolute;
                        top: -12px;
                        width: 20px;
                        font-size: 10px;
                        color: #ea4a36;
                        text-align: center;
                    }
                }
            }
        }
        .box-li-packend{
           padding-bottom: 20px;
        }
    }
    .footer{
        color: #000000;
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translate(-50%, 0px);
        background: #fff;
        width: 100%;
        z-index: 30;
    }
}
.details-box-pack{
    height: 120px;
    transition: height 0.2s ease-out;
    overflow: hidden; 
}
</style>
