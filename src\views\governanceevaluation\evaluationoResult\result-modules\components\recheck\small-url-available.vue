<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel" >
    <template>
      <FormItem label="最大等待时长">
        <InputNumber
          v-model.number="formData.visitTimeout"
          :min="0"
          :max="Number.MAX_SAFE_INTEGER"
          :precision="0"
          placeholder="请输入最大等待时长"
          class="mr-sm width-input"
        ></InputNumber>
        <span class="base-text-color">毫秒</span>
      </FormItem>
<!--      <FormItem label="检测大图标注抓拍时间和地点">
        <RadioGroup v-model="formData.snap" >
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>-->
    </template>
  </basic-recheck>
</template>

<script>
export default {
  name: 'small-url-available',
  components: {
    BasicRecheck: require('./basic-recheck.vue').default
  },
  props: {},
  data() {
    return {
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: 1, // 复检计划
        scheduletime: '', // 自定义时间
        deviceIds: [],
        ocrModel: '',
        ocrModel2: '',
        visitTimeout: null,
        snap: null,
        isUpdatePhyStatus: '',
      },
    };
  },
  computed: {
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          deviceIds: this.formData.deviceIds,
          visitTimeout: this.formData.visitTimeout,
          snap: this.formData.snap,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
  },
  watch: {},
  filter: {},
  created() {},
  methods: {
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: 1, // 复检计划
        scheduletime: '', // 自定义时间
        deviceIds: [], //批量复检 选择设备
        isUpdatePhyStatus: '',
        visitTimeout: null,
        snap: 2,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.input-width-number {
  width: 380px;
}
@{_deep} .ivu-modal-body {
  max-height: 650px;
  overflow-y: auto;
}
@{_deep} .detect-content {
  margin-left: 70px;
}

@{_deep} .ivu-collapse {
  border: none;
  background: transparent;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      padding-left: 20px;
      background: #062f68;
      border: none;
      color: var(--color-primary);
      font-size: 16px;
      .ivu-icon,
      ivu-icon-ios-arrow-forward {
        float: right;
        margin-top: 10px;
        color: #ffffff;
      }
    }
    .ivu-collapse-content {
      background: transparent;
      padding: 0;
    }
  }
}
</style>
