<template>
  <div class="base-search">
    <ui-label class="inline" :label="global.filedEnum.deviceId" :width="70">
      <Input v-model="searchData.deviceId" class="width-lg" :placeholder="`请输入${global.filedEnum.deviceId}`"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="70">
      <Input
        v-model="searchData.deviceName"
        class="width-lg"
        :placeholder="`请输入${global.filedEnum.deviceName}`"
      ></Input>
    </ui-label>
    <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="fl ml-lg">
      <Select
        class="width-sm"
        v-model="searchData.sbdwlx"
        :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        clearable
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="ml-lg" label="设备联网状态" :width="100" v-if="paramsList.indexId === '3012'">
      <Select class="width-sm" v-model="searchData.outcome" clearable placeholder="请选择设备联网状态">
        <Option :value="1" label="已联网"></Option>
        <Option :value="2" label="未联网"></Option>
      </Select>
    </ui-label>
    <ui-label class="ml-lg" label="设备在线状态" :width="100" v-else>
      <Select class="width-sm" v-model="searchData.outcome" clearable placeholder="请选择设备联网状态">
        <Option :value="1" label="在线"></Option>
        <Option :value="2" label="离线"></Option>
      </Select>
    </ui-label>
    <ui-label :width="30" class="" label=" ">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="resetSearchDataMx()">重置</Button>
    </ui-label>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    paramsList: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        controlType: '',
        outcome: '',
      },
    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
    }),
  },
  created() {
    this.reashfalf();
  },
  methods: {
    // 初始化
    reashfalf() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        controlType: '',
        outcome: '',
      };
    },
    // 搜索
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    // 重置
    resetSearchDataMx() {
      this.reashfalf();
      this.$emit('startSearch', this.searchData);
    },
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  margin-top: 10px;
  .input-width {
    width: 200px;
  }
}
.exportBtn {
  .icon-daochu {
    font-size: 10px;
    margin-right: 5px;
    margin-top: -2px;
  }
}
.width-md {
  width: 150px !important;
}
</style>
