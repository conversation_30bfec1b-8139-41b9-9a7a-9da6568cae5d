<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem label="普通设备历史录像" v-show="!isImportant">
        <Input class="width-input" placeholder="请输入" v-model="formData.videoGeneralDay" />
        <span class="base-text-color ml-xs">天</span>
      </FormItem>
      <FormItem label="重点设备历史录像">
        <Input class="width-input" placeholder="请输入" v-model="formData.videoImportDay" />
        <span class="base-text-color ml-xs">天</span>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isUpdatePhyStatus: '',
        videoGeneralDay: null,
        videoImportDay: null,
      },
    };
  },
  created() {},
  methods: {
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        isUpdatePhyStatus: '',
        videoGeneralDay: null,
        videoImportDay: null,
      };
    },
  },
  watch: {},
  computed: {
    getAttrs() {
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: {
          videoGeneralDay: this.formData.videoGeneralDay,
          videoImportDay: this.formData.videoGeneralDay,
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        },
        ...this.$attrs,
      };
    },
    isImportant() {
      return this.moduleData && this.moduleData.indexType === 'VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN';
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
