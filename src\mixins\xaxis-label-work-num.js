/**
 * 此mixin针对echarts x轴标签名 字数过多导致重叠问题
 */
export default {
  data() {
    return {
      selfRefsName: null,
      selfOptions: null,
      selfShowBarNum: 6,
    };
  },
  created() {
    window.addEventListener('resize', this.resetXaxisLabelNumFn);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resetXaxisLabelNumFn);
  },
  activated() {
    this.resetXaxisLabelNumFn();
  },
  methods: {
    /**
     *x轴 label标签自适应换行
     * @param {滑动的echarts} echartRef
     * @param {滑动的echarts中options} opts
     * @param {当前xAxis显示个数} showBarNum
     */
    resetXaxisLabelNumFn(echartRef, opts, showBarNum) {
      echartRef = opts ? echartRef : this.selfRefsName;
      opts = opts ? opts : this.selfOptions;
      showBarNum = showBarNum || this.selfShowBarNum;
      if (!this.$refs[echartRef]) return;

      this.selfRefsName = echartRef;
      this.selfOptions = opts;
      this.selfShowBarNum = showBarNum;

      let gridWidth = this.$refs[echartRef].$el.clientWidth - this.$util.common.fontSize(100); //可以根据canvas的宽度和grid的right,left,width进行计算
      let fontsize = this.$util.common.fontSize(12); //字体大小
      let wordNum = parseInt(gridWidth / showBarNum / fontsize);
      let obj = {
        xAxis: opts.xAxis,
      };
      // 目前只考虑了 只有一个x轴的 情况
      obj.xAxis[0].axisLabel = {
        formatter: function (params) {
          if (params.length < wordNum) {
            return params;
          } else {
            var strs = params.split(''); //字符串数组
            var str = '';
            for (var i = 0, s; (s = strs[i++]); ) {
              //遍历字符串数组
              str += s;
              if (!(i % wordNum)) str += '\n'; //按需要求余
            }
            return str;
          }
        },
      };
      this.$refs[echartRef].setOption(obj);
    },
  },
};
