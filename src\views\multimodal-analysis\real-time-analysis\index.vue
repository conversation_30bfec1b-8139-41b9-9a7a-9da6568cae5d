<template>
  <div class="container">
    <!-- 查询 -->
    <Search
      ref="searchRef"
      :subTaskType="subTaskType"
      @searchForm="searchForm"
    />
    <div class="table-container">
      <div class="data-above">
        <div class="left-operater">
          <Button size="small" @click="handleAdd">
            <ui-icon type="jia" color="#2C86F8"></ui-icon>
            新增任务
          </Button>
          <Button class="mr" @click="batchStart" size="small">
            <ui-icon type="start" color="#2C86F8"></ui-icon>
            批量开始
          </Button>
          <Button class="mr" @click="batchStop" size="small">
            <ui-icon type="pause2" color="#2C86F8"></ui-icon>
            批量暂停
          </Button>
          <Button size="small" @click="handleDelJobs">
            <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
            批量删除
          </Button>
        </div>
        <div class="right-operater">
          <div class="flex-center" @click="handleSort">
            <i
              class="iconfont icon-moveup"
              style="color: #2c86f8"
              :class="{
                rotate: params.sortType === 'asc',
              }"
            ></i>
            &nbsp;&nbsp;时间排序
          </div>
        </div>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #effectiveTime="{ row }">
            <div>
              {{ row.effectiveStartTime }} -
              {{ row.effectiveEndTime || "永久" }}
            </div>
          </template>
          <template #workTime="{ row }">
            <div v-if="row.workStartTime">
              {{ row.workStartTime }} -
              {{ row.workEndTime }}
            </div>
            <div v-else>全时段</div>
          </template>
          <template #taskType="{ row }">
            <div>
              {{ taskTypeList[row.taskType] }}
            </div>
          </template>
          <template #status="{ row }">
            <div>
              {{ taskStatusList.find((item) => item.key == row.status).label }}
            </div>
          </template>
          <template #pointNum="{ row }">
            <DevicePop :deviceList="row?.taskResourceList || []"></DevicePop>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <addModal
      v-if="isShowAdd"
      ref="addModal"
      :title="subTaskId ? '编辑实时解析任务' : '新增实时解析任务'"
      :subTaskType="subTaskType"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      @updated="jobUpdated"
      @close="handlerClose"
    ></addModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "../components/search.vue";
import addModal from "../components/add-modal.vue";
import expandRow from "../components/expandRow.vue";
import TableAction from "../components/table-action.vue";
import { multiModalTaskPageList } from "@/api/multimodal-analysis.js";
import adjustPosition from "@/views/viewanalysis/components/adjustPosition.vue";
import DevicePop from "../components/device-pop.vue";
import { taskTypeList, taskStatusList } from "../enums/index.js";
import TaskHandler from "@/views/multimodal-analysis/mixins/taskHandler.js";
export default {
  name: "RealTimeAnalysis",
  components: {
    Search,
    addModal,
    adjustPosition,
    DevicePop,
  },
  props: {},
  mixins: [TaskHandler],
  data() {
    return {
      taskTypeList,
      taskStatusList,
      subTaskType: "real",
      list: [],
      childList: [],
      loading: false,
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        structuredParsingType: 1,
        sortType: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, { row, index }) => {
            const deviceInfoList = row.taskResourceList.map((item) => {
              const { deviceInfo, ...otherParam } = item;
              return {
                ...deviceInfo,
                ...otherParam,
                deviceId: deviceInfo?.id,
              };
            });
            return h(expandRow, {
              props: {
                tableList: deviceInfoList,
                columns: this.childColumns,
                currentJob: { ...row },
                subTaskType: this.subTaskType,
                subTaskStatus: row.status,
                switchLoading: this.switchLoading,
              },
              on: {
                handleEdit: (val) => {
                  this.handleEdit(row, val.deviceId);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: row.id,
                    structuredParsingType: row.structuredParsingType,
                  });
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val) => {
                  this.deleteTasks([val]);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "taskName", width: 180 },
        { title: "有效时间", slot: "effectiveTime" },
        { title: "处理时段", slot: "workTime", width: 180 },
        { title: "处理类型", slot: "taskType", width: 150 },
        { title: "解析类型", key: "structureAlgorithmName", width: 150 },
        { title: "点位数量", slot: "pointNum", width: 100 },
        { title: "任务状态", slot: "status", width: 100 },
        { title: "创建时间", key: "createTime", width: 190 },
        { title: "创建人", key: "creator", width: 130 },
        {
          title: "操作",
          width: 120,
          render: (h, { row, index }) => {
            return h(TableAction, {
              props: {
                row,
                subTaskType: this.subTaskType,
              },
              on: {
                taskStatusRealHandler: (val) => {
                  this.taskStatusHandler(val);
                },
                handleEdit: (val) => {
                  this.handleEdit(val);
                },
                handleSearch: (val) => {
                  this.toDetailByTask({
                    taskId: val.id,
                    ...val,
                  });
                },
                mapLoaction: (val) => {
                  this.mapLoaction(val);
                },
                handleDel: (val) => {
                  this.handleDel(val);
                },
              },
            });
          },
        },
      ],
      childColumns: [
        { title: "设备名称", slot: "name", align: "center" },
        { title: "任务状态", slot: "fileResourceStatus", align: "center" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  beforeDestroy() {},
  methods: {
    ...mapActions({
      getDictStructData: "dictionary/getDictStructData",
    }),
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sortType = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    getList(otherParam = {}) {
      this.loading = true;
      let param = {
        ...this.$refs.searchRef.getSearchParam(),
        ...this.params,
        ...otherParam,
      };
      multiModalTaskPageList(param)
        .then((res) => {
          this.list = res?.data?.entities || [];
          this.total = res?.data?.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    jobUpdated() {
      this.isShowAdd = false;
      this.params.pageNumber = 1;
      this.getList();
    },
    // 查询
    searchForm() {
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init();
      });
    },
    handleEdit(row, subDeviceId = "") {
      if (row.status != 0) {
        return;
      }
      this.subTaskId = row.id;
      this.subDeviceId = subDeviceId;
      this.isShowAdd = true;
      this.$nextTick(() => {
        this.$refs.addModal.init({ ...row });
      });
    },
    async batchStart() {
      if (this.selectedData.length == 0) {
        return this.$Message.warning("请选择任务");
      }
      await this.startJobs([...this.selectedData]);
      this.getList();
    },
    async batchStop() {
      if (this.selectedData.length == 0) {
        return this.$Message.warning("请选择任务");
      }
      await this.stopJobs([...this.selectedData]);
      this.getList();
    },
    // 任务开启/关闭
    async taskStatusHandler(value) {
      value.loading = true;
      // 开启
      if (value.status == 1) {
        const bool = await this.stopJobs([value]);
        value.status = bool ? 2 : 1;
      } else {
        const bool = await this.startJobs([value]);
        value.status = bool ? 1 : 2;
      }
      value.loading = false;
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      const deviceInfoList = item.taskResourceList.map((item) => {
        return item.deviceInfo;
      });
      var arrayP = [];
      deviceInfoList &&
        deviceInfoList.length > 0 &&
        deviceInfoList.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
    handlerClose() {
      this.isShowAdd = false;
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .data-above {
      .left-operater {
        display: flex;
      }
      .right-operater {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.8);
        .flex-center {
          display: flex;
          align-items: center;
          cursor: pointer;
        }
        .rotate {
          transform: rotate(180deg);
        }
      }

      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      button {
        margin-right: 10px;
      }
    }
    .table-content {
      flex: 1;
      overflow: scroll;
      .ui-table {
        height: 100%;
        overflow: unset;
        /deep/ .ivu-table-wrapper {
          overflow: unset;
        }
      }
    }
  }
}
</style>
