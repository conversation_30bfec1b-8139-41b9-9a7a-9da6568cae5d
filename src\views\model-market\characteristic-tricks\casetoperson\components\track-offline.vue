<!--
    * @FileDescription: 由案到人-轨迹详情-展示数据
    * @Author: H
    * @Date: 2024/04/12
 * @LastEditors: zhengmingming zhengmingming
 * @LastEditTime: 2024-05-17 17:10:37
 -->
<template>
  <ul class="box-ul">
    <li
      class="box-li"
      :class="{
        'box-li-pack': packUpDown[index],
      }"
      v-for="(item, index) in dataList"
      :key="index"
    >
      <div class="box-li-top">
        <Icon type="md-radio-button-on"></Icon>
      </div>
      <div class="box-li-bottom">
        <div class="time-title" @click="handletimelist(item, index)">
          <p>
            <span class="time-date">{{ item.name }}</span>
            <span class="time-num">{{ item.children.length }}次</span>
          </p>
          <p
            class="triangle"
            :class="{ 'active-triangle': packUpDown[index] }"
          ></p>
        </div>
        <div class="child_list" v-for="(it, ind) in item.children" :key="ind">
          <p class="sec-radio"></p>
          <div class="content-top" @click="handleListTrack(it, ind)">
            <div class="content-top-img">
              <img v-lazy="it.traitImg || item.defaultImg" alt="" />
            </div>
            <div class="content-top-right">
              <span class="content-top-right-time">
                <ui-icon type="time" :size="14"></ui-icon>
                <span :title="it.trajectoryTime">{{ it.trajectoryTime }}</span>
                <ui-tag
                  v-if="!!item.showLabel && it.label"
                  :color="it.label | getLabelInfo(item.showLabel, 'color')"
                  :style="{
                    margin: 0,
                    height: '20px',
                  }"
                  >{{
                    it.label | getLabelInfo(item.showLabel, "label")
                  }}</ui-tag
                >
              </span>
              <span class="content-top-right-time">
                <ui-icon type="location" :size="14"></ui-icon>
                <span :title="it.address">{{ it.address }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </li>
  </ul>
</template>

<script>
import UiTag from "@/components/ui-tag";
import { cloneDeep } from "lodash";
import { baseTrackTypeMap } from "./trackTypeMap";
export default {
  name: "",
  components: {
    UiTag,
  },
  filters: {
    getLabelInfo(code, map, key) {
      const data = map[code] || {};
      return data[key];
    },
  },
  data() {
    return {
      // dataList: [
      //     {
      //         'date': '人体抓拍',
      //         'times': '1',
      //         'children': [
      //             {
      //                'captureTime': '2020-03-24 16:27:10',
      //                'sceneImg': require('@/assets/img/face.png')
      //             }
      //         ]
      //     }
      // ],
      dataList: [],
      packUpDown: [],
    };
  },
  methods: {
    init(data) {
      let typeList = cloneDeep(baseTrackTypeMap);
      for (let key in typeList) {
        typeList[key].children = [];
      }
      data.forEach((item) => {
        if (typeList[item.dataType]) {
          const detailAttr = typeList[item.dataType].detailAttr;
          const detail = item[detailAttr];
          typeList[item.dataType].children.push({
            detail,
            traitImg: detail && detail.traitImg,
            ...item,
          });
        }
      });
      this.dataList = Object.keys(typeList)
        .map((type) => typeList[type])
        .filter((item) => item.children.length);
      this.packUpDown = this.dataList.map((item, index) => index !== 0);
    },
    handletimelist(item, index) {
      this.$set(this.packUpDown, index, !this.packUpDown[index]);
    },
    handleListTrack(row, index) {
      this.$emit("openPositionTheWindow", row, index);
    },
  },
};
</script>

<style lang='less' scoped>
@import "../../../components/style/timeLine";
.content-top-right {
  overflow: hidden;
  .content-top-right-time {
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-bottom: 6px;
    span {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 5px;
    }
  }
}
</style>