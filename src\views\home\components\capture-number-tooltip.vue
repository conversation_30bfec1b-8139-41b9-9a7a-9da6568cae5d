<template>
  <div class="tooltip-container">
    <div class="tooltip-title-box">
      <p>{{ getDate }}</p>
    </div>
    <div v-for="(item, index) in getList" :key="index" class="tooltip-content-box">
      <p class="legend-box">
        <span> {{ item.name }} </span>
        <span :style="{ color: item.color }" class="font-num">
          {{ data[0].data?.[item.key] || 0 }}
        </span>
      </p>
      <p v-if="cuptureMonthOrDay === 'month'" class="pointer link-text-box" onclick="window.openDetails()">日趋势</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'online-changes-tooltip',
  components: {},
  props: {},
  data() {
    return {
      // 月趋势
      detailsDataMonth: [
        {
          name: '当日抓拍',
          key: 'num',
          color: '#04CDF4',
        },
      ],
      // 日趋势
      detailsDataDay: [
        {
          name: '抓拍数量',
          key: 'num',
          color: '#04CDF4',
        },
      ],
    };
  },
  computed: {
    getList() {
      return this.cuptureMonthOrDay === 'month' ? this.detailsDataMonth : this.detailsDataDay;
    },
    getDate() {
      let { day, time } = this.data[0].data;
      let text =
        this.cuptureMonthOrDay === 'month' ? day : `${day} ${this.setPre(time)}:00-${this.setPre(time + 1)}:00`;
      return text;
    },
  },
  methods: {
    setPre(val) {
      return val >= 10 ? val : `0${val}`;
    },
  },
};
</script>

<style lang="less" scoped>
.tooltip-container {
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #1981f5;
  min-width: 230px;
  padding: 10px;
  color: #fff;
  .tooltip-content-box,
  .legend-box,
  .tooltip-title-box {
    color: #ffffff;
    display: flex;
    align-items: center;
    flex-direction: row;
  }
  .tooltip-content-box {
    justify-content: space-between;
  }
  .font-num {
    font-weight: 600;
    margin-left: 30px;
  }
}
</style>
