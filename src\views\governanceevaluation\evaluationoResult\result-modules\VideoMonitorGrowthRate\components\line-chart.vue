<template>
  <div class="line-chart-container">
    <title-section title-name="历史趋势 ">
      <div class="search" slot="content">
        <tag-view slot="mid" :list="tagList" @tagChange="changeStatus" ref="tagView" class="tag-view"></tag-view>
        <DatePicker
          class="ml-md"
          ref="DatePicker"
          v-if="dateType === 'DAY'"
          type="month"
          placeholder="请选择月份"
          format="yyyy年MM月"
          :value="month"
          :editable="false"
          @on-change="handleChange"
        ></DatePicker>
        <DatePicker
          class="ml-md"
          ref="yearPicker"
          v-else-if="dateType === 'MONTH'"
          type="year"
          placeholder="请选择年"
          format="yyyy年"
          :value="year"
          :editable="false"
          @on-change="handleChangeYear"
        ></DatePicker>
      </div>
    </title-section>
    <div class="echarts-box" v-ui-loading="{ loading: loading, tableData: Object.keys(echart) }">
      <draw-echarts
        v-if="Object.keys(echart).length"
        :echart-option="echartRin"
        ref="zdryChart"
        class="charts"
      ></draw-echarts>
      <span class="next-echart" v-if="Object.keys(echart).length > 20">
        <i
          class="icon-font icon-zuojiantou1 f-12"
          @click="scrollRight('propertyChart', Object.keys(echart), [], 20)"
        ></i>
      </span>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';

export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    TitleSection: require('@/views/governanceevaluation/evaluationoResult/components/title-section.vue').default,
    TagView: require('@/components/tag-view.vue').default,
  },
  props: {
    activeIndexItem: {},
  },
  mixins: [dealWatch],
  data() {
    return {
      barData: [],
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
      loading: false,
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      echart: {},
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
      tagList: ['月'],
      dateType: 'MONTH',
    };
  },
  mounted() {
    this.getDate();
    this.startWatch(
      '$route.query',
      () => {
        this.getDayCaptureStatistics();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    changeStatus() {
      this.month = null;
      this.monthIndex = 0;
      this.dateType = 'MONTH';
      this.getDayCaptureStatistics();
    },
    initRin() {
      let lengName = [
        {
          name: '当前设备数量',
          key: 'deviceNum',
          color: '#8943D0',
        },
        {
          name: '去年年末设备数量',
          key: 'lastYearDeviceNum',
          color: '#C194EF',
        },
      ];
      this.echartData = [];
      let series = [];
      lengName.forEach((items) => {
        series.push({
          name: items.name,
          data: this.echart.default.map((list) => list.detail[items.key] || 0),
          barWidth: '22px',
          barCategoryGap: '3%',
          type: 'bar',
          stack: items.stack,
          barGap: 0.3, //柱间距离
          yAxisIndex: 1,
          itemStyle: { color: items.color },
        });
      });
      let line = [
        {
          name: this.activeIndexItem.indexName,
          type: 'line',
          itemStyle: {
            color: '#E268CE',
          },
          symbol: 'circle',
          yAxisIndex: 0,
          data: this.echart.default.map((item) => item.detail.resultValue),
          valueType: 'percent',
        },
      ];
      let opts = {
        tooltipFormatter: (series) => {
          let str = '';
          series.reverse().forEach((item, index) => {
            if (index === 0) {
              str = `<p>${item.axisValueLabel}月</p>`;
            }
            str += `<p><span class="mr-md">${item.seriesName}</span>${item.value || 0}${
              item.seriesType === 'line' ? '%' : ''
            }</p>`;
          });
          return str;
        },
        xAxis: this.echart.default.map((item) => item.horizontal),
        series: [...series, ...line],
        lengName: [...lengName.map((item) => item.name), this.activeIndexItem.indexName],
      };
      this.echartRin = this.$util.doEcharts.ReviewConsequenceEcharts(opts);
      setTimeout(() => {
        this.setDataZoom('echartRin', [], 20);
      });
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear(value) {
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    async getDayCaptureStatistics() {
      try {
        const { regionCode, orgCode, statisticType, taskSchemeId, indexType, indexId } = this.$route.query;
        this.loading = true;
        let params = {
          taskSchemeId: taskSchemeId,
          indexId: indexId,
          indexType: indexType,
          dateType: this.dateType,
          year: this.year,
          month: this.monthIndex,
          displayType: statisticType,
          orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
          needCustomDetail: 1,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getHistoryTrend, params);
        this.echart = data || {};
        this.initRin();
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.line-chart-container {
  position: relative;
  height: 100%;
  width: 100%;
  box-shadow: var(--shadow-sub-echarts-content);
  .search {
    position: relative;
    display: flex;
    align-items: center;

    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;

      > div {
        color: #fff;
        margin-left: 42px;
        font-size: 14px;
      }

      .font-sky {
        color: #25e6fd;
      }

      .font-orange {
        color: #f78b2e;
      }
    }
  }

  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);

    .charts {
      height: 100% !important;
    }
  }
}
</style>
