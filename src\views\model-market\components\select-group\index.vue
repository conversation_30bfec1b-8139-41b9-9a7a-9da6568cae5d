<template>
  <el-cascader
    :value="value"
    size="small"
    :options="trajectoryTypeGroupList"
    :props="props"
    clearable
    collapse-tags
    @change="onChange"
  ></el-cascader>
</template>

<script>
const dataDesNameMap = {
  capture: "轨迹类",
  police: "警情类",
};
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      props: {
        multiple: true,
        label: "dataValue",
        value: "dataKey",
      },
    };
  },
  computed: {
    trajectoryTypeGroupList() {
      return this.trajectoryTypeList.reduce((pre, cur) => {
        const dataDes = cur.dataDes;
        const item = pre.find((item) => item.dataKey === dataDes);
        if (item) {
          item.children.push({ ...cur });
        } else {
          pre.push({
            dataKey: dataDes,
            dataValue: dataDesNameMap[dataDes],
            children: [{ ...cur }],
          });
        }
        return pre;
      }, []);
    },
  },
  methods: {
    onChange(val) {
      this.$emit("input", val);
    },
  },
};
</script>

<style lang="less" scoped>
.track_list {
  .sortord {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.45);
    display: flex;
    padding: 10px;
    align-items: center;
    margin-left: 5px;
    cursor: pointer;
    display: flex;
    column-gap: 10px;
    align-items: center;
  }

  .box-ul {
    padding: 0px 15px;

    .box-li {
      background: #f9f9f9;
      margin: 10px 0;
      padding: 10px;

      .box_li-title {
        display: flex;
        align-items: center;

        p {
          font-weight: 700;
          font-size: 14px;
          color: #f29f4c;
          margin-left: 15px;
        }
      }

      .content-top {
        display: flex;
        // overflow: auto;
        cursor: pointer;
        margin-top: 10px;

        img {
          width: 80px;
          height: 80px;
          border: 1px solid #cfd6e6;
          position: relative;

          .similarity {
            position: absolute;
            left: 0;
            top: 0;

            span {
              padding: 2px 5px;
              background: #4597ff;
              color: #fff;
              border-radius: 4px;
            }
          }
        }

        .content-top-right {
          margin-left: 11px;
          // margin-top: 3px;
          font-size: 14px;
          flex: 1;
          width: calc(~"100% - 60px");

          /deep/ .iconfont {
            margin-right: 5px;
          }

          .block {
            color: #000000;
          }
        }
      }
    }
  }
}
</style>