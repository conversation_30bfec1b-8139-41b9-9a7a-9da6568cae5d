import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import { defaultIconStaticsList } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';

export const iconStaticsList = [
  ...defaultIconStaticsList,
  {
    name: '幕标注合规性、时钟准确性',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    // causeErrors
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
    slot: 'select',
  },
];
// 字幕标注合规率
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    minWidth: 120,
    tooltip: true,
  },
  {
    title: '监控点位类型',
    key: 'sbdwlxText',
    align: 'left',
    tooltip: true,
    minWidth: 100,
  },
  {
    title: '设备状态',
    slot: 'phyStatus',
    align: 'left',
    tooltip: true,
    minWidth: 80,
  },
  {
    title: '检测方式',
    slot: 'detectionMode',
    minWidth: 100,
    tooltip: true,
  },
  {
    title: '检测结果',
    slot: 'outcome',
    minWidth: 100,
    tooltip: true,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '备注',
    key: 'reason',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '检测时间',
    key: 'videoStartTime',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '操作',
    slot: 'option',
    align: 'center',
    tooltip: true,
    minWidth: 100,
    fixed: 'right',
    className: 'table-action-padding', // 操作栏列-单元格padding设置
  },
];
