<template>
  <!--  <transition>-->
  <!-- 气泡组件中data-bbproxy="vue"这个属性为必填，配合init()方法使用，不然事件点击不触发 -->
  <div
    :id="id"
    style="position: relative"
    data-bbproxy="vue"
    class="c-bubble"
    :class="[bubbleMB === 'KeyPersonBubble' ? 'c-bubble-person' : '']"
  >
    <component
      :is="bubbleMB"
      :id="id"
      :bubbleData="bubbleData"
      :upBubleData="upBubleData"
      :isAdvance="isOpen"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template slot="closeBubble">
        <i class="iconfont icon-guanbi close bb-modal-header-close" @click="closeBubble"></i>
      </template>
      <template slot="errType">
        <slot name="bubbleErrType"></slot>
      </template>
    </component>
  </div>
  <!--  </transition>-->
</template>
<script>
import bubbleModules from '@/components/bubble';
export default {
  name: 'c-bubble',
  props: {
    id: {
      type: String,
    },
    isOpen: {
      type: Boolean,
    },
    bubbleData: {
      type: Object,
    },
    bubbleMB: {
      type: String,
    },
  },
  components: {
    ...bubbleModules,
  },
  watch: {
    bubbleData: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && this.bubbleMB === 'KeyPersonBubble') {
          this.upDateBubbleData();
        }
      },
      deep: true,
      immediate: true,
    },
    bubbleMB() {
      console.log(this.bubbleMB);
    },
  },
  data() {
    return {
      render: false,
      originParent: null,
      upBubleData: null,
    };
  },
  /**
   *  vue、angular等外部框架开发地图气泡容器组件时实现init、getElement、open、close三个方法
   */
  methods: {
    //必需的方法
    init() {
      /* eslint-disable no-undef */
      this.originParent = this.$el.parentElement;
      var compId = this.id;
      if (!FMapScope['compCache']) {
        FMapScope['compCache'] = {};
      }
      FMapScope['compCache'][compId] = this;
    },
    //必需的方法
    getElement() {
      return this.$el;
    },
    //必需的方法
    open() {
      this.render = true;
    },
    //必需的方法
    close() {
      this.render = false;
      this.originParent.appendChild(this.$el);
    },
    upDateBubbleData() {
      let self = this;
      this.$store
        .dispatch('queryPersonBeforeEdit', {
          idCard: this.bubbleData.cardNo || this.bubbleData.deviceId,
        })
        .then((data) => {
          data.remark = self.bubbleData.remark;
          self.upBubleData = self.bubbleData;
        });
    },
    closeBubble() {
      this.$emit('on-close-bubble');
    },
  },
  mounted() {
    this.init();
  },
  destroyed() {
    // todo
  },
};
</script>
<style lang="less">
//@-webkit-keyframes bubble {
//  0% {
//    transform: scale(0.2);
//  }
//  100% {
//    transform: scale(1);
//  }
//}
//.ol-selectable {
//  animation: bubble 1s 1;
//  transform-origin: left bottom;
//  max-width: 360px;
//  min-width: 360px;
//  z-index: 999;
//}
.edit-bubble-box {
  background: #07295d !important;
  border: 1px solid #2967c8 !important;
  box-shadow: 0 3px 10px #000000 !important;
  border-radius: 4px;
  min-width: 100px;
  min-height: 530px;
  width: auto;
  height: auto;
  &::before {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 15px;
    bottom: -21px;
    border: 11px solid;
    border-color: #23b1ed transparent transparent #23b1ed;
  }
  &::after {
    content: ' ';
    position: absolute;
    width: 0;
    height: 0;
    left: 16px;
    bottom: -19px;
    border: 10px solid;
    border-color: #07295d transparent transparent #07295d !important;
  }
}
.c-bubble {
  max-width: 380px;
  min-width: 360px;
  .bb-modal {
    &-header {
      &-close {
        position: absolute;
        right: -29px;
        top: -29px;
        //&:before{
        //  color: #07295d !important;
        //}
      }
    }
  }
}
</style>
