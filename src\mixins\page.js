/**
 * 通用混合
 * */
import { logRecord } from '@/api/user.js'
import { mapGetters } from 'vuex';
export const commonMixins =  {
    data() {
        return {}
    },
    computed: {
        ...mapGetters({
            userInfo: "userInfo", // 用户信息
        })
    },
    methods: {
        // 查询日志
        queryLog(data) {
            // type 类型(1 新增、2 编辑、3 删除、 4 查询、5 检索  6 登录 7 登出)
            let params = {
                "orgCode": this.userInfo.orgVoList[0].orgCode,
                "applicationCode": data.muen,
                "resourceCode": data.name,
                "type": data.type,
                "remark":data.remark, // "在地图上，单击图标查看【名称为L_JF3033龙新路行知实验学校大门口】的【资源】详情"
            };
            logRecord(params)
            .then(res =>{})
            .finally(() =>{})
        },
    }
}
