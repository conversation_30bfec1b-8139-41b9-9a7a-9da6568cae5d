/**
 * @example <Button v-clipboard="'345'" v-clipboard:callback="copy">copy</Button>
 */
export default function (Vue) {
  Vue.directive('clipboard', {
    bind(el, { value, arg }) {
      if (arg === 'callback') {
        el.$copyCallback = value;
      } else {
        el.$copyValue = value;
        const handle = () => {
          copyfun(el.$copyValue);
          if (el.$copyCallback) {
            el.$copyCallback(el.$copyValue);
          }
        };
        el.addEventListener('click', handle);
        el.$destroyCopy = () => el.removeEventListener('click', handle);
      }
    },
    unbind(el) {
      el.$destroyCopy();
    },
    componentUpdated(el, { value, arg }) {
      if (arg === 'callback') {
        el.$copyCallback = value;
      } else {
        el.$copyValue = value;
      }
    },
  });
}
// 内容复制
function copyfun(text) {
  // 动态创建 textarea 标签
  const textarea = document.createElement('textarea');
  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
  textarea.readOnly = 'readonly';
  textarea.style.position = 'absolute';
  textarea.style.left = '-9999px';
  // 将要 copy 的值赋给 textarea 标签的 value 属性
  textarea.value = text;
  // 将 textarea 插入到 body 中
  document.body.appendChild(textarea);
  // 选中值并复制
  textarea.select();
  return document.execCommand('Copy');
}
