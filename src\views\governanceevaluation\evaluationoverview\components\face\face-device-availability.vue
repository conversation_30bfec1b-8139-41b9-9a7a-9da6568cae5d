<template>
  <!-- 人脸卡口设备图片地址(url)可用率 -->
  <div class="face-device-availability" ref="contentScroll">
    <div class="information-header">
      <carUrlStatistics
        v-if="[2001, 2002].includes(paramsList.indexId)"
        :statistics-list="statisticsList"
        :isflexfix="true"
      ></carUrlStatistics>

      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-jiancejieguoxiangqing f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <tagView class="tagView fr" ref="tagView" :list="['设备模式', '图片模式']" @tagChange="tagChange1" />
      </div>
      <!-- 设备模式 -->
      <TableList
        v-if="modelTag == 0 && tableColumns.length > 0"
        ref="infoList"
        :contentClientHeight="contentClientHeight"
        :columns="tableColumns"
        :loadData="loadDataList"
      >
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <SearchCard :is-image-model="false" @startSearch="startSearch"></SearchCard>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
        <!-- 表格操作 -->
        <!-- <template #deviceId="{ row }">
          <span
            class="font-active-color pointer device-id"
            :class="row.rowClass"
            @click="deviceArchives(row)"
            >{{ row.deviceId }}</span
          >
        </template> -->
        <template #outcome="{ row }">
          <span
            class="check-status"
            :class="[
              row.outcome === '1' ? 'bg-success' : '',
              row.outcome === '2' ? 'bg-failed' : '',
              row.outcome === '3' ? 'bg-D66418' : '',
            ]"
            :title="row.description"
          >
            {{ handleCheckStatus(row.outcome) }}
          </span>
          <span
            v-permission="{
              route: $route.name,
              permission: 'artificialreviewr',
            }"
            class="ml-sm"
            v-if="row.dataMode === '3'"
          >
            (人工)
          </span>
        </template>
        <template #qualifiedRate="{ row }">
          <span>{{ row.qualifiedRate || 0 }}%</span>
        </template>
        <template #unqualifiedNum="{ row }">
          <span class="font-red">{{ row.unqualifiedNum }}</span>
        </template>
        <template #faceUrlFacePathErrorTotal="{ row }">
          <span class="font-red">{{ row.faceUrlFacePathErrorTotal }}</span>
        </template>
        <template #faceUrlScenePathErrorTotal="{ row }">
          <span class="font-red">{{ row.faceUrlScenePathErrorTotal }}</span>
        </template>
        <template #faceUrlScenePathNoTimeTotal="{ row }">
          <span class="font-red">{{ row.faceUrlScenePathNoTimeTotal }}</span>
        </template>
        <template #option="{ row }">
          <ui-btn-tip
            icon="icon-chakantupian"
            content="查看图片"
            @click.native="clickRow(row)"
            class="mr-sm"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="!isFormCascade()"
            icon="icon-rengongfujian"
            content="人工复核"
            class="vt-middle"
            @click.native="artificialReview(row)"
            v-permission="{
              route: $route.name,
              permission: 'artificialreviewr',
            }"
          ></ui-btn-tip>
        </template>
      </TableList>
      <!-- 图像模式 -->
      <TableCard
        ref="infoCard"
        class="card-list auto-fill"
        :loadData="loadDataCard"
        :cardInfo="cardInfo"
        v-if="modelTag == 1"
      >
        <div slot="search" class="hearder-title">
          <SearchCard
            :checkStatus="checkStatus"
            :checkList="checkList"
            :resultId="{
              indexId: paramsList.indexId,
            }"
            @startSearch="startSearch"
          ></SearchCard>
          <Button type="primary" class="btn_search" @click="getSecondExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
        <!-- 卡片 -->
        <template #card="{ row }">
          <InfoCard
            :list="row"
            :checkNum="1"
            :cardInfo="cardInfo"
            @bigImageUrl="bigImageUrl"
            :paramsData="paramsList"
            :check-data="checkList"
            @recount="recount"
          >
          </InfoCard>
        </template>
      </TableCard>
      <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
      <CheckPicture
        v-model="checkPicture"
        :list="currentRow"
        :check-data="checkList"
        :resultId="{
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        }"
        :tagList="tagList"
        :interFaceName="getSecondaryPopUpData"
        :checkNum="1"
        @recount="recount"
      ></CheckPicture>
    </div>
    <!-- 人工复核 -->

    <ui-modal v-model="artificialVisible" ref="artificialReview" title="人工复核" :styles="artificialStyles">
      <div class="artificial-data">
        <ui-label class="block" label="人工复核:" :width="80">
          <RadioGroup v-model="artificialData.qualified">
            <!-- <Radio :label="item.value" v-for="(item, index) in qualifiedList" :key="index">{{
              item.key
            }}</Radio> -->
            <Radio label="1">设备合格</Radio>
            <Radio label="2" class="ml-lg">设备不合格</Radio>
          </RadioGroup>
        </ui-label>
        <ui-label class="block mt-sm" label="" :width="80">
          <Input
            type="textarea"
            class="desc"
            v-model="artificialData.reason"
            placeholder="请输入备注信息"
            :rows="5"
            :maxlength="256"
          ></Input>
        </ui-label>
      </div>
      <template #footer>
        <Button type="primary" class="plr-30" @click="artificial">确定复核结果</Button>
      </template>
    </ui-modal>
  </div>
</template>

<style lang="less" scoped>
.face-device-availability {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .navBarWrap {
    position: sticky !important;
    top: 100px;
    width: 100%;
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 642px) !important;
      //min-height: 290px !important;
    }
  }
  .card-list {
    width: 100%;
    min-height: 460px !important;
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;

    .information-statistics {
      display: flex;
      width: calc(100% - 370px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
    }

    .information-ranking {
      width: 364px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 54px;
      line-height: 54px;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .hearder-title {
      position: relative;
      padding: 10px 0;

      .btn_search {
        position: absolute;
        right: 0px;
        top: 10px;
      }
    }
  }
}
.artificial-data {
  padding: 0 50px;
}
.desc {
  margin-left: 80px;
  width: 80%;
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
import { superiorinjectfunc } from '@/views/disposalfeedback/cascadelist/util/proxyInterface.js';

export default {
  mixins: [downLoadTips],
  name: 'face-device-availability',
  data() {
    return {
      tagList: [
        { label: 'URL可用', value: 'URLAvailable', outcome: 1 },
        {
          label: 'URL不可用',
          value: 'URLSmallUnvailable',
          outcome: 2,
        },
      ],
      rankLoading: false,
      getSecondaryPopUpData: evaluationoverview.getSecondaryPopUpData,

      statisticsList: [
        {
          name: '人脸卡口总量',
          value: 0,
          icon: 'icon-renliankakou',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          textColor: 'color1',
          type: 'number',
          key: 'total',
        },
        {
          name: '检测设备数量',
          value: 0,
          icon: 'icon-jianceshebeishuliang1',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'deviceDataTotal',

          textColor: 'color2',
        },
        {
          name: '检测合格设备数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          textColor: 'color9',
          key: 'passDeviceDataTotal',
        },
        {
          name: '检测不合格设备数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
          key: 'notPassDeviceDataTotal',
        },
        {
          name: '检测图片数量',
          value: 0,
          icon: 'icon-jiancetupianshuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          textColor: 'color6',
          key: 'faceDataTotal',
        },
        {
          name: '图片地址可用数量',
          value: 0,
          icon: 'icon-URLkeyongtupianshuliang',
          iconColor: 'icon-bg7',
          liBg: 'li-bg7',
          type: 'number',
          textColor: 'color7',
          key: 'passFaceDataTotal',
        },
        {
          name: '图片地址不可用数量',
          value: 0,
          icon: 'icon-URLbukeyongtupianshu',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          textColor: 'color8',
          type: 'number',
          key: 'notPassFaceDataTotal',
        },
        {
          name: '人脸卡口设备图片地址可用率',
          value: 0,
          icon: 'icon-renliankakoushebeitupiandizhikeyongshuai',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          textColor: 'color3',
          type: 'percentage',
          key: 'resultValue',
        },
      ],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',

          minWidth: 200,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          minWidth: 120,
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', tooltip: true, minWidth: 120 },
        { title: '点位类型', key: 'sbdwlxText', tooltip: true, minWidth: 120 },
        { title: '检测图片数量', key: 'total', tooltip: true, minWidth: 120 },
        {
          title: '可用图片数量',
          key: 'qualifiedNum',
          tooltip: true,
          minWidth: 150,
        },
        {
          title: '不可用图片数量',
          key: 'unqualifiedNum',
          slot: 'unqualifiedNum',
          tooltip: true,
          width: 200,
        },
        {
          title: '小图URL地址不可访问',
          key: 'urlThumbnailPathErrorTotal',
          className: 'font-red',
          tooltip: true,
          width: 160,
        },
        {
          title: '大图URL地址不可访问',
          key: 'urlLargePathErrorTotal',
          className: 'font-red',
          tooltip: true,
          width: 160,
        },
        {
          title: '小图URL地址为空',
          key: 'urlThumbnailPathNullTotal',
          className: 'font-red',
          tooltip: true,
          width: 140,
        },
        {
          title: '大图URL地址为空',
          key: 'urlLargePathNullTotal',
          className: 'font-red',
          tooltip: true,
          width: 140,
        },
        {
          title: '大图时间未标注数量',
          key: 'urlLargePathTimeNullTotal',
          className: 'font-red',
          tooltip: true,
          width: 160,
        },
        {
          title: '大图地址未标注数量',
          key: 'urlLargePathAddressNullTotal',
          className: 'font-red',
          tooltip: true,
          width: 160,
        },
        { title: '检测时间', key: 'startTime', tooltip: true, width: 200 },
        {
          title: '合格率',
          key: 'qualifiedRate',
          slot: 'qualifiedRate',
          tooltip: true,
          width: 120,
        },
        {
          title: '检测状态',
          key: 'outcome',
          slot: 'outcome',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '原因',
          key: 'description',
          tooltip: true,
          minWidth: 120,
        },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          width: 100,
          align: 'center',
        },
      ],
      tableData: [],
      searchData: {
        customParameters: { deviceId: '', deviceName: '', outcome: '' },
      },
      exportLoading: false,
      rankData: [],
      paramsList: {},
      statisticalList: {},
      loadDataList: async (parameter) => {
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getDetailData,
          Object.assign(
            parameter,
            {
              indexId: this.paramsList.indexId,
              batchId: this.paramsList.batchId,
              access: this.paramsList.access,
              displayType: this.paramsList.displayType,
              orgRegionCode: this.paramsList.orgRegionCode,
            },
            this.searchData,
          ),
          'post',
          this.$route.query.cascadeId,
          {},
        );
        return res.data;
      },
      loadDataCard: async (parameter) => {
        let params = Object.assign(
          parameter,
          {
            indexId: this.paramsList.indexId,
            batchId: this.paramsList.batchId,
            access: this.paramsList.access,
            displayType: this.paramsList.displayType,
            orgRegionCode: this.paramsList.orgRegionCode,
          },
          this.searchData,
        );
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.getPolyData,
          params,
          'post',
          this.$route.query.cascadeId,
          {},
        );
        return res.data;
      },
      modelTag: 0, // 设备模式,图像模式
      cardInfo: [
        { icon: 'icon-shijian', value: 'shotTime', text: 'tip' },
        { icon: 'icon-dizhi', value: 'address', text: 'tip' },
        { icon: 'icon-shujujiancegongju-01-01', value: 'resultTip', text: 'tip' },
      ],

      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      checkPicture: false, //查看图片
      currentRow: {},
      checkStatus: [
        { name: '图片可用', checkKey: '1' },
        { name: '图片不可用', checkKey: '2' },
      ],
      contentClientHeight: 0,
      //人工复核
      artificialStyles: {
        width: '3rem',
      },
      qualifiedList: [
        { key: '设备合格', value: '1' },
        { key: '设备不合格', value: '2' },
      ],
      artificialData: { qualified: '1', reason: '' },
      artificialVisible: false,
      artificialRow: {},
      checkList: [],
    };
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
  },
  filters: {
    separateNum(str) {
      str = String(str);
      let counter = 0;
      let result = '';
      for (let j = str.length - 1; j >= 0; j--) {
        counter++;
        result = str.charAt(j) + result;
        if (!(counter % 3) && j != 0) {
          result = ',' + result;
        }
      }

      return result;
    },
  },
  methods: {
    isFormCascade() {
      //级联清单 跳转 不需要 人工复核
      return this.$route.name === 'cascadelist';
    },
    recount() {
      this.$emit('update');
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.deviceId },
      });
      window.open(routeData.href, '_blank');
    },
    // 人工复核
    artificialReview(row) {
      this.artificialData.qualified = '1';
      this.artificialData.reason = '';
      this.artificialRow = row;
      this.artificialVisible = true;
    },
    async artificial() {
      let data = {
        data: {
          id: this.artificialRow.id,
          qualified: this.artificialData.qualified,
          reason: this.artificialData.reason,
          type: 'device',
        },
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
      };
      try {
        let res = await this.$http.post(evaluationoverview.manualRecheck, data);
        this.artificialVisible = false;
        this.artificialUpdate();
        this.$emit('update');
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      }
    },
    async getUrlIndexErrorCode() {
      try {
        let res = await this.$http.get(evaluationoverview.getUrlIndexErrorCode);
        this.checkList = res.data.data || [];
      } catch (error) {
        console.log(error);
      }
    },
    // 查看不合格图片
    clickRow(row) {
      this.currentRow = row;
      this.checkPicture = true;
    },
    handleCheckStatus(row) {
      const flag = {
        1: '合格',
        2: '不合格',
        3: '无法检测',
      };
      return flag[row];
    },
    // 大图展示
    bigImageUrl(item) {
      if (!item) {
        this.$Message.warning('大图图片地址缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    async init() {
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: { ...this.searchData.customParameters },
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportDeviceDetailData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },

    async getSecondExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: { ...this.searchData.customParameters },
        };
        this.$_openDownloadTip();
        /**------级联清单特殊替换处理接口(后端转发)-------**/
        let res = await superiorinjectfunc(
          this,
          evaluationoverview.exportSecondModelData,
          params,
          'post',
          this.$route.query.cascadeId,
          { responseType: '' },
        );
        // const res = await this.$http.post(evaluationoverview.exportSecondModelData, params)
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
        this.exportLoading = false;
      }
    },
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
    // 人工复核更成功后更新表格数据不跳转页码
    artificialUpdate() {
      try {
        this.modelTag = 0;
        this.$nextTick(() => {
          if (this.$refs.tagView) {
            this.$refs.tagView.curTag = 0;
          }
          this.$refs.infoList.handleUpdateArtificialTable();
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },

    rankList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
          if (this.paramsList.indexId === 2001) {
            this.statisticsList[7].name = '人脸卡口设备图片地址可用率';
          } else if (this.paramsList.indexId === 2002) {
            this.statisticsList[7].name = '重点人脸卡口设备图片地址可用率';
          }
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        } else {
          this.rankData = [];
        }
      },
      deep: true,
      immediate: true,
    },

    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.init();
          this.getUrlIndexErrorCode();
        }
      },
      deep: true,
      immediate: true,
    },
  },

  components: {
    TableList: require('./components/tableList.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    InfoCard: require('./components/infoCard.vue').default,
    tagView: require('./components/tags.vue').default,
    SearchCard: require('./components/searchCard.vue').default,
    CheckPicture: require('./components/check-picture.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    LookScene: require('@/components/look-scene').default,
    carUrlStatistics:
      require('@/views/governanceevaluation/evaluationoverview/components/car/component/car-url-statistics.vue')
        .default,
  },
};
</script>
