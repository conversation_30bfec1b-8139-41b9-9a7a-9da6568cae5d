<template>
  <div class="auto-fill my-message">
    <div class="search-module over-flow">
      <ui-label class="inline" label="消息类型">
        <Select class="width-md" v-model="searchData.type" placeholder="请选择消息类型">
          <Option v-for="(item, index) in typeList" :key="index" :label="item.value" :value="item.key"></Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" label="是否已读">
        <Select class="width-md" v-model="searchData.markRead" placeholder="请选择是否已读">
          <Option label="是" :value="1"></Option>
          <Option label="否" :value="0"></Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" label="">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </ui-label>
      <Button type="primary" class="fr" @click="readAll" :disabled="tableData.length === 0">全部标记已读</Button>
    </div>

    <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #typeText="{ row }">
        <span v-if="row.markRead === 0" class="round"></span>
        <span>{{ row.typeText }}</span>
      </template>
      <template #action="{ row }">
        <ui-btn-tip
          class="mr-md"
          :styles="{ color: '#438CFF', 'font-size': '14px' }"
          icon="icon-chakanxiangqing"
          content="查看详情"
          @click.native="view(row)"
        ></ui-btn-tip>
      </template>
    </ui-table>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </div>
</template>
<script>
import notification from '@/config/api/notification';
import { mapActions } from 'vuex';
export default {
  name: 'mymessage',
  props: {},
  data() {
    return {
      loading: false,
      typeList: [],
      searchData: {
        type: '',
        markRead: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableColumns: [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        {
          title: '消息类型',
          width: 300,
          slot: 'typeText',
          align: 'left',
          className: 'message-type',
        },
        {
          minWidth: 300,
          title: '消息内容',
          key: 'systemContent',
          align: 'left',
        },
        {
          width: 300,
          title: '通知时间',
          key: 'receiveTime',
          align: 'left',
        },
        {
          width: 60,
          title: '操作',
          slot: 'action',
          align: 'center',
        },
      ],
      tableData: [],
    };
  },
  created() {
    this.getTypeList();
    this.init();
  },
  methods: {
    ...mapActions({
      setNoticeNum: 'websocket/setNoticeNum',
    }),
    async getTypeList() {
      try {
        const res = await this.$http.get(notification.getTypes);
        this.typeList = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.post(notification.messagePageList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    async view(row) {
      await this.readMessage(row.id);
      this.$router.push({ path: row.uri, query: row.redirectParams ? JSON.parse(row.redirectParams) : {} });
    },
    readAll() {
      this.$UiConfirm({
        content: '您是否将全部信息标记为已读？',
        title: '提示',
      })
        .then(() => {
          this.readMessage();
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async readMessage(id) {
      try {
        const res = await this.$http.post(notification.markRead, {
          messageIds: id ? [id] : [],
          all: !id,
        });
        this.$Message.success(res.data.msg);
        this.init();
        this.setNoticeNum();
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.my-message {
  background-color: var(--bg-content);
  .round {
    position: absolute;
    left: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #bc3c19;
  }
  .search-module {
    padding: 20px;
  }
  .ui-table {
    padding: 0 20px;
    @{_deep}.message-type {
      position: relative;
      padding-left: 10px;
    }
  }
}
</style>
