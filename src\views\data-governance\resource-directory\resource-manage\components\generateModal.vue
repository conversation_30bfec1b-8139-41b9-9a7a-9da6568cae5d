<template>
  <ui-modal v-model="visible" :r-width="1200" title="生成数据" list-content @onOk="confirmHandle" class="config">
    <!-- 显示数据链接和复制按钮的区域 -->
    <label for="dataCountInput" class="mb-5 color-error">拟生成数据条数:</label>
    <input id="dataCountInput" type="number" v-model="dataCount">
    <Table class="auto-fill table" :columns="columns" border :loading="loading" :data="tableData" max-height="580">
      <template #fieldNameCn="{ index }">
        <span>{{ tableData[index].fieldNameCn }}</span>
      </template>
      <template #fieldType="{ index }">
        <Select placeholder="请选择" v-model="tableData[index].fieldType" transfer>
          <Option v-for="(type, index) in fieldTypeOptions" :value="type" :key="index">{{ type }}</Option>
        </Select>
        <!-- <span>{{ tableData[index].fieldType }}</span> -->
      </template>
      <template #fieldTypeLen="{ index }">
        <span>{{ tableData[index].fieldTypeLen }}</span>
      </template>
      <template #fieldExpress="{ index }">
        <div>
          <!-- 输入框或下拉框 -->
          <template v-if="['PHONE_NUMBER', 'STRING', 'DATE', 'DICTIONARY'].includes(tableData[index].fieldType)">
            <Select v-model="tableData[index].fieldExpress" filterable placeholder="请选择" transfer>
              <Option v-for="(option, index) in getDropdownOptions(tableData[index].fieldType)" :key="index" :value="option">{{ option }}</Option>
            </Select>
          </template>
          <template v-else>
            <Input placeholder="默认null" v-model="tableData[index].fieldExpress" />
          </template>
          </div>
      </template>
      <!-- 新增文本显示列 -->
      <template #textDisplay="{ index }">
        <span v-if="tableData[index].fieldType === 'STRING'">大写字母（A）小写字母（B）数字（1）</span>
        <span v-else-if="tableData[index].fieldType === 'DATE'">年月日时分秒</span>
        <span v-else-if="tableData[index].fieldType === 'PHONE_NUMBER'">电话号码</span>
        <span v-else-if="tableData[index].fieldType === 'NUMBER'">a,b（范围）</span>
        <span v-else-if="tableData[index].fieldType === 'DICTIONARY'">字典</span>
        <span v-else-if="tableData[index].fieldType === 'ID_CARD'">身份证号</span>
        <span v-else></span>
      </template>
        
    </Table>
  </ui-modal>
</template>

<script>
import { resourceField, queryResourceField } from '@/api/dataGovernance'

export default {
  computed: {
    selectedValue() {
      // 根据选中的选项获取实际值
      return this.selectedOption ? this.selectedOption.value : null;
    }
  },
  props: {
    //对应字典
    dictTypedata: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      resourceId: '',
      dataCount: '1',
      link: '',
      dataToSend:'',
      columns: [
        { title: '序号', align: 'center', width: 90, type: 'index', key: 'index', fixed: 'left' },
        { title: '字段名称', key: 'fieldName', width: 160, fixed: 'left' },
        { title: '字段中文名称', slot: 'fieldNameCn', width: 160 },
        { title: '数据类型', slot: 'fieldType', width: 140 },
        { title: '数据长度', slot: 'fieldTypeLen', width: 140 },
        { title: '数据附加条件', slot: 'fieldExpress', width: 160 },
        { title: '条件提示', slot: 'textDisplay', width: 140 }, 
      ],
      tableData: [],
      DICTIONARY: [],
      fieldTypeOptions: ['PHONE_NUMBER', 'ID_CARD', 'DATE', 'STRING', 'NUMBER', 'DICTIONARY', 'BLOB']
    }
  },
  methods: {
    // 初始化
    show(val) {
      
      this.DICTIONARY = this.dictTypedata.map(item => item.typeValue);
      this.visible = true
      this.resourceId = val.id
      this.loading = true
      queryResourceField(this.resourceId).then(res => {
        let data = res
        data.forEach(item => {
          item.fieldType = this.setFieldTypeByFieldNameCn(item.fieldNameCn, item.fieldType);
        });

        this.tableData = data.map(item => {
          return { ...item, fieldExpress: null };
        });
        this.loading = false;
      })
    },

    // 根据字段中文名称设定数据类型
    setFieldTypeByFieldNameCn(fieldNameCn, dbFieldType) {
      switch (true) {
        case fieldNameCn.includes('电话'):
        case fieldNameCn.includes('联系方式'):
        case fieldNameCn.includes('联系电话'):
          return 'PHONE_NUMBER';
        case fieldNameCn.includes('身份证号'):
        case fieldNameCn.includes('身份证'):
          return 'ID_CARD';
        case fieldNameCn.includes('日期'):
        case fieldNameCn.includes('时间'):
          return 'DATE';
        default:
          if (dbFieldType == 'CHAR' || dbFieldType == 'VARCHAR2' || dbFieldType == 'NUMBER' || dbFieldType == 'DATE' || dbFieldType === 'BLOB') {
            return this.convertDbTypeToFieldType(dbFieldType);
          } else {
            return 'STRING';
          }
      }
    },

    // 将数据库字段类型转换为对应的字段类型选项
    convertDbTypeToFieldType(dbFieldType) {
      switch (dbFieldType) {
        case 'CHAR':
        case 'VARCHAR2':
          return 'STRING';
        case 'NUMBER':
          return 'NUMBER';
        case 'DATE':
          return 'DATE';
        case 'BLOB':
          return 'BLOB';
        default:
          return 'STRING';
      }
    },
    // 根据数据类型获取对应的下拉框选项
    getDropdownOptions(fieldType) {
      switch (fieldType) {
        case 'PHONE_NUMBER':
          return ['移动', '联通', '电信', '所有'];
        case 'STRING':
          return ['A', 'a', '1', 'A1', 'a1', 'Aa1'];
        case 'DATE':
          return ['yyyy-MM-dd HH:mm:ss', 'dd-MM-yyyy HH:mm:ss', 'yyyy-MM-dd', 'dd-MM-yyyy', 'TIMESTAMP'];
        case 'DICTIONARY':
          return this.DICTIONARY
        default:
          return [];
      }
    },

    // 确认提交
    confirmHandle() {
          // 在提交表单时遍历tableData，将字典类型的value转换为key
      const dataToSend = this.tableData.map(item => {
        if (item.fieldType === 'DICTIONARY') {
          const index = this.dictTypedata.findIndex(dictItem => dictItem.typeValue === item.fieldExpress);
          if (index !== -1) {
            return {
              ...item,
              fieldExpress: this.dictTypedata[index].typeKey
            };
          }
        }
        return item;
      });
      console.log(dataToSend);
      let data = {
        resourceId: this.resourceId,
        dataCount: this.dataCount,
        fieldItems: dataToSend,
      };
      console.log(data);
      resourceField(data).then(res => {
        this.link = res;
        if (!this.link) {
          this.$Message.error('链接为空，无法复制');
        } else {
          // 实现复制功能
          // 创建一个 input 元素
          const input = document.createElement('input');
          // 设置 input 的 value 为链接内容
          input.value = this.link;
          // 将 input 添加到页面中
          document.body.appendChild(input);
          // 选中 input 内容
          input.select();
          // 模拟键盘复制操作
          document.execCommand('copy');
          // 删除 input 元素
          document.body.removeChild(input);
          // 提示复制成功
          this.$emit('refreshDataList')
          this.$Message.success('链接已复制到剪贴板');
          this.visible = false;
          window.location.href = this.link;
        }
      });
    },
  }
}
</script>
