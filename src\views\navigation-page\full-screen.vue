<template>
  <div class="container" v-drag @click="handleClick">
    <div class="full-screen">
      <i class="icon-font f-14" :class="getIcon"></i>
      <p>{{ getDesc }}</p>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'full-screen',
  data() {
    return {};
  },
  methods: {
    ...mapActions({
      setState: 'fullscreen/setState',
    }),
    handleClick() {
      this.setState(this);
    },
  },
  computed: {
    ...mapGetters({
      getDesc: 'fullscreen/getDesc',
      getIcon: 'fullscreen/getIcon',
      getState: 'fullscreen/getState',
    }),
  },
};
</script>

<style lang="less" scoped>
.container {
  position: fixed;
  top: 63%;
  bottom: 50%;
  right: 50px;
  cursor: pointer;
  z-index: 5;

  &:hover {
    .full-screen {
      background: #0c3b7a;
      box-shadow: 0 0 10px rgba(14, 200, 242, 0.8);
    }
  }
  &:active {
    .full-screen {
      background: #114c9a;
      box-shadow: 0 0 10px #0ec8f2;
    }
  }

  .full-screen {
    position: relative;
    width: 53px;
    height: 53px;
    background: #082a58;
    box-shadow: 0 0 10px rgba(14, 200, 242, 0.6);
    border-radius: 50%;
    opacity: 1;
    text-align: center;
    padding: 7px;
    i {
      background: linear-gradient(180deg, #0acef3 0%, #3f83f2 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    p {
      font-weight: bold;
      color: #08c8f3;
    }
  }
}
</style>
