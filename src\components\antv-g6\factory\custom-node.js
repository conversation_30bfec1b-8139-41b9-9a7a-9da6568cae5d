import { customIconfontShape } from "./custom-shape";

export class CustomNode {
  createCustomNode({ G6, G6Config, relationCanOperate }) {
    return G6.registerNode(
      "custom",
      {
        // 响应状态变化
        setState(name, value, item) {
          const group = item.getContainer();
          const shapes = group
            .get("children")
            .filter(
              (shape) =>
                shape.get("name") === "custom-circle-keyShape" ||
                shape.get("name") === "custom-img-shape" ||
                shape.get("name") === "iconfont-text-group"
            );
          shapes.forEach((shape) => {
            if (value) {
              shape.attr({
                ...G6Config.nodeStateStyles[name],
              });
            } else {
              if (item.hasState("selected")) {
                shape.attr({
                  ...G6Config.nodeStateStyles["selected"],
                });
              } else if (item.hasState("connectionAnalysis")) {
                shape.attr({
                  ...G6Config.nodeStateStyles["connectionAnalysis"],
                });
              } else if (item.hasState("analysis")) {
                shape.attr({
                  ...G6Config.nodeStateStyles["analysis"],
                });
              } else if (item.hasState("secondary")) {
                shape.attr({
                  ...G6Config.nodeStateStyles["secondary"],
                });
              } else {
                shape.attr({
                  opacity: 1,
                  stroke: "transparent",
                  shadowBlur: 0,
                });
              }
            }
          });

          const labelCfg = G6Config.nodeStateStyles[name]?.labelCfg;
          if (labelCfg) {
            const textShapes = group
              .get("children")
              .filter((shape) => shape.get("type") === "text");
            textShapes.forEach((text) => {
              if (value)
                text.attr({
                  ...labelCfg.style,
                });
              else
                text.attr({
                  opacity: 1,
                });
            });
          }
        },
        draw: (cfg, group) => {
          const size = cfg.size;
          const isGroup = cfg.isGroup;
          const width = size[0];
          const height = size[1];

          let keyShape = null;
          const circleShape = group.addShape("circle", {
            attrs: {
              r: 31,
              width: width,
              height: height,
              fill: isGroup ? "l(135) 0:#5BA3FF 1:#2C86F8" : "",
            },
            draggable: true,
            visible: cfg.visible,
            // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            name: "custom-circle-keyShape",
          });

          group.addShape("text", {
            attrs: {
              text: cfg.label,
              x: 0,
              y: isGroup ? 10 : height / 2 + 16,
              fill: isGroup ? "#fff" : "#000",
              textAlign: "center",
            },
            draggable: true,
            // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
            name: "custom-text-shape",
          });

          if (cfg.isGroup) {
            /**
             * 关系图谱-数量展示
             */

            keyShape = circleShape;

            group.addShape("circle", {
              attrs: {
                x: width / 2 - 11,
                y: -height / 2 + 11,
                r: 11,
                width: 22,
                height: 22,
                fill: "#fff",
              },

              // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
              name: "custom-circle-total",
            });

            group.addShape("text", {
              attrs: {
                text: cfg.ext.count,
                x: width / 2 - 11,
                y: -height / 2 + 19,
                fill: "rgba(44, 134, 248, 1)",
                textAlign: "center",
              },

              // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
              name: "custom-text-total",
            });

            /**
             * 关系图谱-展开
             */
            if (relationCanOperate) {
              group.addShape("circle", {
                attrs: {
                  x: width / 2 + 14,
                  y: 0,
                  r: 7,
                  width: 14,
                  height: 14,
                  fill: "#fff",
                  stroke: "rgba(44, 134, 248, 1)",
                },

                // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
                name: "custom-circle-expand",
              });

              group.addShape("text", {
                attrs: {
                  text: cfg.isExpand ? "-" : "+",
                  x: width / 2 + 14,
                  y: 7,
                  fill: "rgba(44, 134, 248, 1)",
                  textAlign: "center",
                  cursor: "pointer",
                },

                // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
                name: "custom-text-expand",
              });
            }
          } else {
            let imgeShape = null;
            if (
              /((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?/.test(
                cfg.img
              )
            ) {
              imgeShape = group.addShape("image", {
                attrs: {
                  x: -width / 2,
                  y: -height / 2,
                  width: width,
                  height: height,
                  img: cfg.img,
                },
                draggable: true,

                // 在 G6 3.3 及之后的版本中，必须指定 name，可以是任意字符串，但需要在同一个自定义元素类型中保持唯一性
                name: "custom-img-shape",
              });
              imgeShape.setClip({ type: "circle", attrs: cfg.clipCfg });
            } else {
              imgeShape = customIconfontShape(group, cfg);
            }

            keyShape = imgeShape;
          }

          return keyShape;
        },
        update: (cfg, item) => {
          const group = item.getContainer();
          const expandText = group.find(
            (row) => row.get("name") === "custom-text-expand"
          );
          expandText &&
            expandText.attr({
              text: cfg.isExpand ? "-" : "+",
            });
        },
      },
      "single-node"
    );
  }
}
