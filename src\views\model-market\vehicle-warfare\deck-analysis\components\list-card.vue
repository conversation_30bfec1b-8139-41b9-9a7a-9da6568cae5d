<!--
    * @FileDescription: 列表
 * @LastEditors: duansen
 * @LastEditTime: 2024-09-20 10:56:12
 -->
<template>
  <div class="imageCard" @click="toDetail">
    <div class="carCode" @click.stop="">
      <plateNo :plateNo="item.plateNo"></plateNo>
    </div>
    <div class="mid">
      <div class="mid-content mid-left">
        <div class="img">
          <img :src="item.traitImg" alt="" v-viewer @click.stop="" />
        </div>
        <span class="time">{{ item.absTime || "--" }} </span>
        <span
          class="result-msg"
          style="color: #999"
          v-if="item.whichVehicle === 0"
          >无效信息</span
        >
        <span
          class="result-msg"
          style="color: #f54a4a"
          v-else-if="item.whichVehicle === 1 || item.whichVehicle === 3"
          >套牌车辆</span
        >
        <span class="result-msg" style="color: #288ce2" v-else>疑似车辆</span>
        <span class="device" :title="item.deviceName">{{
          item.deviceName || "--"
        }}</span>
        <div class="orerate">
          <i
            class="iconfont icon-location"
            title="定位"
            @click.stop="handleLocation(item.geoPoint)"
          />
          <i
            class="iconfont icon-qiche"
            title="以图搜车"
            @click.stop="handleSearch(item.traitImg)"
          />
          <i
            class="iconfont icon-huodongguilv"
            title="行车轨迹"
            @click.stop="handleTrack(item.plateNo, item.absTime)"
          />
          <i
            class="iconfont icon-chepai"
            title="复制车牌"
            @click.stop="handleCopy(item.plateNo)"
          />
        </div>
      </div>
      <div class="pk-icon" :class="['handle-msg' + item.handleStatus]"></div>
      <div class="mid-content mid-right">
        <div class="img">
          <img :src="item.traitImgFake" alt="" v-viewer @click.stop="" />
        </div>
        <span class="time">{{ item.absTimeFake || "--" }}</span>
        <span
          class="result-msg"
          style="color: #999"
          v-if="item.whichVehicle === 0"
          >无效信息</span
        >
        <span
          class="result-msg"
          style="color: #f54a4a"
          v-else-if="item.whichVehicle === 1 || item.whichVehicle === 3"
          >套牌车辆</span
        >
        <span class="result-msg" style="color: #288ce2" v-else>疑似车辆</span>
        <span class="device" :title="item.deviceNameFake">{{
          item.deviceNameFake || "--"
        }}</span>
        <div class="orerate">
          <i
            class="iconfont icon-location"
            title="定位"
            @click.stop="handleLocation(item.geoPointFake)"
          />
          <i
            class="iconfont icon-qiche"
            title="以图搜车"
            @click.stop="handleSearch(item.traitImgFake)"
          />
          <i
            class="iconfont icon-huodongguilv"
            title="行车轨迹"
            @click.stop="handleTrack(item.plateNo, item.absTimeFake)"
          />
          <i
            class="iconfont icon-chepai"
            title="复制车牌"
            @click.stop="handleCopy(item.plateNo)"
          />
        </div>
      </div>
    </div>

    <mapPositionModal ref="mapPositionModal"></mapPositionModal>
  </div>
</template>

<script>
import plateNo from "@/components/ui-vehicle/index.vue";
import mapPositionModal from "@/components/map/map-position-modal.vue";
import { copyText } from "@/util/modules/common";
export default {
  name: "",
  components: {
    plateNo,
    mapPositionModal,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    toDetail() {
      this.$emit("toDetail");
    },
    handleLocation(geoPoint) {
      this.$refs.mapPositionModal.showPoints([
        {
          longitude: geoPoint.lon,
          latitude: geoPoint.lat,
        },
      ]);
    },
    handleSearch(traitImg) {
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1",
        query: {
          imgUrl: traitImg,
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    handleTrack(plateNo, absTime) {
      const { href } = this.$router.resolve({
        path: "/model-market/vehicle-warfare/car-path?noMenu=1",
        query: {
          plateNo: plateNo,
          absTime: this.$dayjs(absTime).valueOf(),
        },
      });
      this.$util.openNewPage(href, "_blank");
    },
    handleCopy(plateNo) {
      copyText(plateNo);
    },
  },
};
</script>

<style lang="less" scoped>
.imageCard {
  width: calc(~"33.3% - 10px");
  margin-right: 10px;
  margin-bottom: 10px;
  background-color: #f9f9f9;
  border: solid 1px #d3d7de;
  border-radius: 5px;
  height: 300px;
  cursor: pointer;
  &:hover {
    border-color: #0091ff;
  }
  .carCode {
    margin-top: 10px;
  }
  .mid {
    padding: 5px 5px 0;
    //background: #eee;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .mid-content {
      position: relative;
      .img {
        width: 185px;
        height: 185px;
        border: solid 1px #cfd6e6;
        img {
          width: 185px;
          height: 185px;
          object-fit: cover;
        }
      }
      .result-msg {
        font-size: 12px;
        display: inline-block;
        margin-left: 10px;
        position: relative;
        top: -4px;
      }
      .time {
        display: inline-block;
        max-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        margin-top: 6px;
      }
      .device {
        display: block;
        max-width: 185px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 12px;
        margin-top: 8px;
      }
      .orerate {
        position: absolute;
        left: 0;
        bottom: 0;
        background: #2c86f8;
        width: 100%;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        color: #fff;
        display: none;
      }
      &:hover {
        .orerate {
          display: flex;
        }
      }
    }
    .mid-left {
      margin-left: 10px;
      border: 1px solid #d3d7de;
      padding: 5px;
    }
    .mid-right {
      margin-right: 10px;
      border: 1px solid #d3d7de;
      padding: 5px;
    }
    .pk-icon {
      width: 50px;
      height: 50px;
      margin: 67px 4px;
      &.handle-msg0 {
        background: url("~@/assets/img/tpfx/invalid.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg1 {
        background: url("~@/assets/img/tpfx/fake-plate.png") no-repeat center
          center;
        background-size: contain;
      }
      &.handle-msg2,
      &.handle-msgundefined,
      &.handle-msgnull {
        background: url("~@/assets/img/tpfx/no-handle.png") no-repeat center
          center;
        background-size: contain;
      }
    }
  }
}
</style>
