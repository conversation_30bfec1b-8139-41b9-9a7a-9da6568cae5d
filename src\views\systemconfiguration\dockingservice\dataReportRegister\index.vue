<template>
  <div class="page-evaluationmanagement">
    <div class="content auto-fill">
      <div class="form">
        <ui-label class="fl" label="接口名称" :width="65">
          <Input class="width-md" v-model="searchData.intefaceName" placeholder="请输入接口名称" />
        </ui-label>
        <div class="inline ml-sm">
          <Button type="primary" @click="searchHandle">查询</Button>
          <Button class="ml-sm" @click="resetHandle">重置</Button>
        </div>
        <Button class="fr ml-sm" type="primary" @click="updateOrAddHandle()">
          <i class="icon-font icon-tianjia f-12 mr-sm"></i>
          <span class="inline vt-middle"> 接口注册</span>
        </Button>
      </div>
      <div class="table-box auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #algorithmVendorType="{ row }">{{ getAlgorithmLabel(row.algorithmVendorType) }} </template>
          <template #status="{ row }">{{ statusTypeMap[row.status] }}</template>
          <template #createTime="{ row }">
            <span>{{ row.createTime || '--' }}</span>
          </template>
          <template #intefaceType="{ row }">
            <span>{{ row.intefaceType | filterType(interfaceTypeList) }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip icon="icon-bianji2" content="编辑" @click.native="updateOrAddHandle(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page>
    </div>
    <updateOrAdd
      ref="updateOrAdd"
      :algorithmList="algorithmList"
      :algorithmTypeList="algorithmTypeList"
      :interfaceTypeList="interfaceTypeList"
      @refreshDataList="getTableList"
    />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import api from '@/config/api/cascadeIntefaceConfig';
import updateOrAdd from './components/update-or-add';
import UiTable from '@/components/ui-table';

export default {
  name: 'supInterfaceConfig',
  data() {
    return {
      loading: false,
      addOrEditModalShow: false,
      evaluationResultShow: false,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '接口名称', key: 'intefaceName' },
        { title: '接口地址', width: 320, key: 'intefaceUrl' },
        { title: '接口类型', slot: 'intefaceType' },
        { title: '上级平台IP', key: 'localIp' },
        { title: '上级平台端口号', key: 'localPort' },
        { title: '授权账户', key: 'username' },
        { title: '注册时间', slot: 'createTime' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 60,
        },
      ],
      tableData: [],
      addorEditModalAction: {
        title: '新增',
        action: 'add',
        visible: false,
      },
      addorEditModalData: {},
      evaluationlData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      searchData: {
        intefaceName: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      selectOrgTree: {
        orgCode: '',
      },
      resultTypeMap: {},
      statusTypeMap: {},
    };
  },
  components: {
    updateOrAdd,
    UiTable,
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
      interfaceTypeList: 'algorithm/report_interface_type',
    }),
  },
  async created() {
    if (this.interfaceTypeList.length == 0) await this.getAlldicData();

    this.getTableList();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async getTableList() {
      try {
        this.loading = true;
        let res = await this.$http.post(api.supInterfaceList, this.searchData);
        let resultData = res.data.data;
        this.tableData = resultData.entities;
        this.pageData.totalCount = resultData.total;
        this.loading = false;
      } catch (err) {
        this.loading = false;
      }
    },
    // 检索
    searchHandle() {
      this.searchData.pageNum = 1;
      this.getTableList();
    },
    // 重置
    resetHandle() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 10 };
      this.searchData = {
        intefaceName: '',
        params: { pageNumber: 1, pageSize: 10 },
      };
      this.algorithmVendorType = '';
      this.getTableList();
    },
    addSuccess() {
      this.getTableList();
    },
    // 新增编辑
    updateOrAddHandle(row) {
      this.$nextTick(() => {
        this.$refs.updateOrAdd.showModal(row);
      });
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      // this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.getTableList();
    },
    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },
  },
};
</script>
<style lang="less" scoped>
.page-evaluationmanagement {
  .left-content {
    background: var(--bg-content);
    float: left;
    width: 260px;
    padding: 10px;
    height: 100%;

    .record-title {
      padding: 10px 0;

      .add_case {
        .name {
          margin-left: 10px;
          position: relative;
          top: 2px;
        }
      }
    }

    .collapse-content-p {
      border: 1px solid transparent;
      border-radius: 4px;
      padding: 10px;
      color: @font-color-white;
      background: @bg-table-block;
      margin-bottom: 10px;

      &.active {
        border-color: @color-other;
      }
    }

    .assessment-list {
      position: relative;
    }
  }

  .content {
    // float: right;
    // width: calc(~"100% - 270px");
    width: 100%;
    height: 100%;
    // padding-top: 20px;
    background: var(--bg-content);

    .search-wrapper {
      overflow: hidden;
      padding: 0 12px 0 20px;

      .input-width {
        width: 363px;
      }
    }

    .table-box {
      padding: 0 20px;
      position: relative;

      .sucess {
        color: @color-success;
      }

      .error {
        color: @color-failed;
      }

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }

  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}
</style>
<style lang="less" scoped>
.page-indexmanagement {
  .form-content {
    padding: 0 50px;

    .ivu-form-item {
      margin-bottom: 20px;

      .time {
        width: 22%;
      }

      .lag {
        width: 13.5%;
        margin: 0 10px;
      }

      .canshu {
        width: 15%;
      }
    }
  }
}

.form {
  padding: 20px;

  .based-field-label {
    color: #fff;
    font-size: 14px;
    padding-right: 12px;
  }

  button {
    margin-left: 12px;
  }
}
</style>
