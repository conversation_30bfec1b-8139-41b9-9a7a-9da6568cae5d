<template>
  <ui-modal :title="wareHouseTitle" v-model="visible" :styles="styles" :footer-hide="true">
    <face-device-search ref="faceSearchRef" @startSearch="startSearch"> </face-device-search>
    <p class="statics mt-sm mb-sm">
      {{ totalFaceMessage }}：<span class="font-active-color ml-sm mr-md">{{ importTotalCount || 0 }}</span>
      {{ captureExpectFaceMes }}:
      <span class="font-red">{{ pageData.totalCount || 0 }}</span>
    </p>
    <Tabs @changeTab="changeTab"></Tabs>
    <div class="list-box">
      <face-list
        v-if="cur == 0"
        :face-list="faceList"
        :face-loading="tableLoading"
        @uploadTips="uploadTips"
      ></face-list>
      <div class="keypersonlibrary-content auto-fill" v-if="cur == 1">
        <div class="keypersonlibrary-content-wrap">
          <ui-gather-card
            class="card"
            v-for="(item, index) in cardList"
            :key="index"
            :list="item"
            :cardInfo="cardInfo"
            @detail="detail"
          ></ui-gather-card>
          <div class="no-data" v-if="!cardList.length">
            <i class="no-data-img icon-font icon-zanwushuju1"></i>
          </div>
        </div>
        <loading v-if="loading"></loading>
      </div>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <capture-details ref="CaptureDetails"></capture-details>
    </div>
    <image-tips-popup v-model="imageTips" pop-up-title="上传超时" :nomal-label-list="nomalLabelList"></image-tips-popup>
    <ui-modal title="轨迹判定" v-model="trailDecidePopupShow" :styles="styles" footer-hide>
      <div class="trail-box">
        <trail-decide :trail-detail-data="trailDetailData"></trail-decide>
      </div>
    </ui-modal>
  </ui-modal>
</template>
<style lang="less" scoped>
.statics {
  color: #fff;
}
.list-box {
  position: relative;
}
.trail-box {
  height: 600px;
}
.card {
  width: calc(calc(100% - 40px) / 4);
  margin: 0 5px 10px;
  float: left;
}
.keypersonlibrary-content {
  margin-top: 20px;
}
.keypersonlibrary-content-wrap {
  height: 538px;
  overflow-y: auto;
}
</style>
<script>
import importantEnum from '../util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
  },
  data() {
    return {
      imageTips: false,
      wareHouseTitle: '',
      visible: false,
      styles: {
        width: '95%',
      },
      activeRouterName: null,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'indexName' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'indexType' },
        { title: '组织机构', key: 'calculateMethod' },
        { title: `${this.global.filedEnum.longitude}`, key: 'criterion' },
        { title: `${this.global.filedEnum.latitude}`, key: 'standardsValue' },
        { title: `${this.global.filedEnum.macAddr}`, key: 'examineResult' },
        { title: this.global.filedEnum.ipAddr, key: 'examineResult' },
        { title: '安装地址', key: 'examineResult' },
      ],
      tableData: [],
      minusTable: 430,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      totalFaceMessage: '人脸图像总量',
      captureExpectFaceMes: '抓拍时间异常图像',
      searchData: {
        // deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
      },
      importTotalCount: 0,
      faceList: [],
      tableLoading: false,
      popUpOption: {},
      trailDecidePopupShow: false,
      nomalLabelList: [
        { name: '设备类型', value: '' },
        { name: '抓拍时间', value: '' },
        { name: '入库时间', value: '' },
        { name: '结论', value: '' },
      ],
      trailDetailData: {
        row: {},
      },
      timeType: ['时', '分', '秒'],
      cur: 0,
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'catchAmount' },
        { name: '异常轨迹：', value: 'exceptCount', color: '#BC3C19' },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    init(popUpOption) {
      this.importTotalCount = 0;
      this.faceList = [];
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
      };
      this.popUpOption = {};
      this.nomalLabelList = [
        { name: '设备类型', value: '' },
        { name: '抓拍时间', value: '' },
        { name: '入库时间', value: '' },
        { name: '结论', value: '' },
      ];
      this.trailDetailData = {};
      this.visible = true;
      this.wareHouseTitle = popUpOption.title;
      this.popUpOption = popUpOption;
      this.queryPersonLibPageTaskTaskTracker();
      // this.popUpOption.filedData.componentCode !== importantEnum.aggregateCodeEnums['数据输出']
      if (popUpOption.filedData.componentCode === importantEnum.aggregateCodeEnums['人员轨迹准确性检测优化']) {
        this.totalFaceMessage = '人员轨迹总量';
        this.captureExpectFaceMes = '错误轨迹';
      } else {
        this.totalFaceMessage = '人脸图像总量';
        this.captureExpectFaceMes = '上传超时图像';
      }
      this.querySumStatistics();
      // if(popUpOption.filedData && popUpOption.filedData.topicComponentStatistics){
      //     this.importTotalCount = popUpOption.filedData.topicComponentStatistics.accessDataCount
      // }
    },
    startSearch(searchData) {
      Object.assign(this.searchData, searchData);
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    uploadTips(rowItem) {
      const staticFuncObject = {
        [importantEnum.aggregateCodeEnums['图像上传及时性检测']]: () => {
          this.imageTips = true;
          this.queryPersonLibCheckResultDetail(rowItem.id).then((data) => {
            let result = data.list[0];
            const delaytime = `${result.timeDifference}，超过${result.extraParamObj.important}${
              this.timeType[result.extraParamObj.importantTime - 1]
            }`;
            this.nomalLabelList = [
              {
                name: '设备类型',
                value: data.isImportantDevice ? '重点人脸卡口' : '普通人脸卡口',
              },
              { name: '抓拍时间', value: result.logTime },
              { name: '入库时间', value: result.createTime },
              { name: '结论', value: delaytime },
            ];
            //this.nomalLabelList = overTimeFunc(data.list[0], data.isImportantDevice, rowItem)
          });
        },
        [importantEnum.aggregateCodeEnums['人员轨迹准确性检测优化']]: () => {
          this.queryPersonLibCheckResultDetail(rowItem.id).then((data) => {
            this.trailDetailData = data.list[0];
            this.trailDetailData.row = rowItem;
            this.trailDecidePopupShow = true;
          });
        },
      };
      staticFuncObject[this.popUpOption.filedData.componentCode]();
    },
    async queryPersonLibPageTaskTaskTracker() {
      try {
        this.tableLoading = true;
        let params = Object.assign({}, this.searchData);
        params.topicComponentId = this.popUpOption.filedData.topicComponentId;
        let { data } = await this.$http.post(tasktracking.queryPersonLibPageTaskTaskTracker, params);
        this.faceList = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.tableLoading = false;
      } catch (err) {
        this.tableLoading = false;
        console.log(err);
      }
    },
    // 获取人员轨迹检测结果详情
    async queryPersonLibCheckResultDetail(importantPersonLibId) {
      try {
        let { data } = await this.$http.post(tasktracking.queryPersonLibCheckResultDetail, {
          topicComponentId: this.popUpOption.filedData.topicComponentId,
          importantPersonLibId: importantPersonLibId,
        });
        return data.data;
      } catch (err) {
        console.log(err);
      }
    },
    async querySumStatistics() {
      try {
        let params = {
          topicComponentId: this.popUpOption.filedData.topicComponentId,
        };
        let { data } = await this.$http.get(tasktracking.querySumStatistics, {
          params: params,
        });
        this.importTotalCount = data.data.accessDataCount;
        // this.staticMessage.totalFace.value = data.data.accessDataCount
        // this.staticMessage.captureExpectFace.value = data.data.existingExceptionCount
        // this.pageData.totalCount = data.data.existingExceptionCount
        // console.log(this.staticMessage,'this.staticMessage')
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    changeTab(index) {
      this.cur = index;
      this.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
      };
      this.$refs.faceSearchRef.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
      };
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    detail(id, list) {
      let com_id = this.searchData.componentCode;
      this.$refs.CaptureDetails.init(id, list, com_id);
    },
    // 聚档接口
    async importantPersonPageList() {
      try {
        this.loading = true;
        if (this.wareHouseTitle == '图像上传及时性检测') {
          this.searchData.componentCode = '8009';
        }
        if (this.wareHouseTitle == '人员轨迹准确性检测优化') {
          this.searchData.componentCode = '10001';
        }
        let res = await this.$http.post(tasktracking.importantPersonPageList, this.searchData);
        const datas = res.data.data;
        this.cardList = datas.entities;
        // this.cardList = datas.entities.map((item) => {
        //     const personTypes = item.personTypeText.split(",");
        //     item.personTypes = persontype.filter((item) => {
        //          return personTypes.includes(item.tagName);
        //     });
        //     return item;
        // });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      this.$refs.faceSearchRef.searchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
      };
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    FaceDeviceSearch: require('@/components/face-device-search.vue').default,
    FaceList: require('@/components/face-list.vue').default,
    ImageTipsPopup: require('@/components/image-tips-popup.vue').default,
    TrailDecide: require('../components/trail-decide.vue').default,
    UiGatherCard: require('@/components/ui-gather-card_str.vue').default, //聚档模式
    CaptureDetails: require('./details/details.vue').default,
    Tabs: require('./details/tabs.vue').default,
  },
};
</script>
