<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :width="detailData.reportMethod == 2 && detailData.deviceList.length > 0 ? '90rem' : '35rem'"
    @onCancel="handleReset"
    :loading="loading"
  >
    <report-info v-if="detailData.status === '1'" :detailData="detailData"></report-info>
    <div v-else>
      <Collapse v-model="collapseReportValue">
        <Panel name="1">
          <div class="header">
            <i class="icon1 icon-font icon-tianbaoshebei f-14 mr-xs green-icon"></i>
            <span class="font-color f14">报备信息</span>
          </div>
          <div slot="content" class="content">
            <report-info :detailData="detailData"></report-info>
          </div>
        </Panel>
      </Collapse>
      <Collapse v-model="collapseResultValue">
        <Panel name="1">
          <div class="header">
            <i class="icon1 icon-font icon-shenhetongguoshuai f-14 mr-xs green-icon"></i>
            <span class="font-color f14">审核信息</span>
          </div>
          <div slot="content">
            <examine-info :detailData="detailData"></examine-info>
          </div>
        </Panel>
      </Collapse>
    </div>
    <template #footer>
      <Button class="plr-30" @click="handleReset">取 消</Button>
    </template>
  </ui-modal>
</template>

<script>
import examination from '@/config/api/examination.js';
export default {
  name: 'detail-info',
  props: {
    title: {
      type: String,
      default: '查看详情',
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      collapseReportValue: '1',
      collapseResultValue: '1',
      formData: {
        isExams: '',
        status: '1',
      },
      detailData: {},
    };
  },
  methods: {
    async init(id) {
      this.visible = true;
      let res = await this.$http.get(examination.getView + `/${id}`);
      let val = res.data.data;
      this.detailData = val;
      // this.detailData = this.$util.common.deepCopy(row)
      if (this.detailData.fileUrl) {
        this.detailData.fileUrl = val.fileUrl.split(',');
      }
    },
    handleReset() {
      this.visible = false;
    },
  },
  components: {
    ReportInfo: require('./report-info.vue').default,
    ExamineInfo: require('./examine-info.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .green-icon {
    background: linear-gradient(360deg, #159575 0%, #54f2b3 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }
}
@{_deep} .ivu-icon-ios-arrow-forward {
  float: right;
  margin-top: 12px;
  color: #99b9e6;
}
//待审核
@{_deep} .report-info {
  height: 600px;
}
@{_deep} .ivu-collapse {
  border: 0;
  background: var(--bg-content);
  &-header {
    border: 0 !important;
  }
  &-item {
    margin-bottom: 5px;
    background: var(--bg-collapse-item);
    border: 0 !important;
  }
  &-content {
    background: var(--bg-content);
  }
  //审核后
  .report-info {
    height: 300px;
  }
  .font-color {
    color: var(--color-content);
  }
}
.green-icon {
  color: var(--color-primary);
}
</style>
