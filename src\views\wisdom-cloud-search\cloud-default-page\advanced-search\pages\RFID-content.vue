<template>
  <section class="main-container">
    <div class="search-bar">
      <searchRFDI ref="searchRFDI" @searchInfo="searchInfo"></searchRFDI>
    </div>
    <div class="table-container">
      <div class="data-above" v-if="mapOnData">
        <!-- <Checkbox @on-change="checkAllHandler" v-model="checkAll">全选</Checkbox> -->
        <Button class="mr" @click="handleSort('absTime')" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button @click="dataAboveMapHandler" size="small">
          <ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
          数据上图
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="tableList"
          @on-selection-change="selectionChangeHandle"
        >
          <template #lon="{ row }">
            <div>{{ row.geoPoint.lon }}</div>
          </template>
          <template #lat="{ row }">
            <div>{{ row.geoPoint.lat }}</div>
          </template>
        </ui-table>
        <!-- <div class="list-card box-2" v-for="(item, i) in tableList" :key="i" :class="{ isChecked: item.isChecked }">
					<div class="content">
						<div class="collection">
							<div class="bg"></div>
							<ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
							<ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
						</div>
						<Checkbox class="check-box" v-if="mapOnData" v-model="item.isChecked" @on-change="e => checkHandler(e, i)">{{ undefined }}</Checkbox>
						<p class="identifier-content">
							<img class="img-icon" src="@/assets/img/icons/icon-RFID.png" alt="" />
							{{ item.rfidCode }}
						</p>
						<div class="bottom-info">
							<time>
								<Tooltip content="采集时间" placement="right" transfer theme="light">
									<i class="iconfont icon-time"></i>
								</Tooltip>
								{{item.absTime}}
							</time>
							<p>
								<Tooltip content="采集地点" placement="right" transfer theme="light">
									<i class="iconfont icon-location"></i>
								</Tooltip>
								<span class="ellipsis" v-show-tips>{{item.detailAddress}}</span>
							</p>
							<p>
								<Tooltip content="采集设备" placement="right" transfer theme="light">
									<i class="iconfont icon-shebeiguanli1"></i>
								</Tooltip>
								<span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
							</p>
						</div>
						<div class="operate-bar">
							<p class="operate-content">
								<ui-btn-tip content="收藏" icon="icon-shoucang" />
								<ui-btn-tip content="分析" icon="icon-fenxi" />
								<ui-btn-tip content="布控" icon="icon-dunpai" transfer />
							</p>
						</div>
					</div>
				</div>
				<div class="empty-card-2" v-for="(item, index) of 8 - (30 % 8)" :key="index + 'demo'"></div> -->
      </div>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="pageInfo.total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[40, 80, 120]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
      <!-- <ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty> -->
      <ui-loading v-if="loading"></ui-loading>
    </div>
  </section>
</template>
<script>
import { RFIDRecordSearchEx } from "@/api/wisdom-cloud-search";
import searchRFDI from "../components/search-RFDI";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { mapMutations, mapGetters } from "vuex";
import { myMixins } from "../../components/mixin/index.js";

export default {
  mixins: [myMixins], //全局的mixin

  name: "rfidContent",
  components: {
    searchRFDI,
  },
  props: {
    mapOnData: {
      type: Boolean,
      default: false,
    },
    mapList: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    mapList: {
      handler(val) {
        if (val && val.length > 0) {
          val.forEach((e) => {
            if (e.position && e.position.length) {
              e.position.forEach((item) => {
                this.hasBeenOnTheMapList.push(item.rfidCode);
              });
            }
          });
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      loading: false,
      queryParam: {
        sortField: "absTime",
        order: "desc",
      },
      tableList: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 40,
        total: 0,
      },
      selectMenuItemId: null,
      formRight: {},
      checkAll: false,
      list: [
        { key: "1" },
        { key: "2" },
        { key: "3" },
        { key: "4" },
        { key: "5" },
        { key: "6" },
        { key: "7" },
        { key: "8" },
        { key: "9" },
        { key: "10" },
        { key: "11" },
        { key: "12" },
        { key: "13" },
        { key: "14" },
        { key: "15" },
      ],
      timeUpDown: false,
      columns: [
        { type: "selection", width: 70, align: "center" },
        {
          title: "编号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "RFID编码", key: "rfidCode" },
        { title: "设备编号", key: "deviceId", width: 210 },
        { title: "设备地址", key: "deviceName" },
        { title: "车牌号码", key: "carNumer" },
        { title: "经度", slot: "lon" },
        { title: "纬度", slot: "lat" },
        { title: "进入时间", key: "startTime" },
        { title: "离开时间", key: "endTime" },
      ],
      selectionList: [],
      hasBeenOnTheMapList: [],
    };
  },
  computed: {
    ...mapGetters({
      getMaxLayer: "countCoverage/getMaxLayer",
      getNum: "countCoverage/getNum",
      getNewAddLayer: "countCoverage/getNewAddLayer",
      getListNum: "countCoverage/getListNum",
      upImageData: "map/getUpImageData",
    }),
    // 已上图数据
    alreadyUpImageIds() {
      return this.upImageData.map((e) => e.id);
    },
  },
  activated() {
    if (this.$route.query.deviceInfo) {
      this.$nextTick(() => {
        let deviceInfo = JSON.parse(this.$route.query.deviceInfo);
        this.$refs.searchRFDI.selectData([{ ...deviceInfo, select: true }]);
        this.queryParam.devices = [deviceInfo.deviceId];
        this.queryList();
      });
    } else {
      this.queryList();
    }
  },
  mounted() {
    // this.init()
  },
  methods: {
    // 排序
    handleSort(val) {
      this.queryParam.sortField = val;
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? "asc" : "desc";
      this.queryList();
    },
    queryList() {
      this.loading = true;
      this.checkAll = false;
      let formData = this.$refs.searchRFDI.formData;
      this.queryParam = { ...this.queryParam, ...formData };
      if (this.queryParam.timeSlot != "自定义") {
        this.dispTime();
      } else {
        this.queryParam.startDate = this.$dayjs(
          formData.perceiveDate[0]
        ).format("YYYY-MM-DD HH:mm:ss");
        this.queryParam.endDate = this.$dayjs(formData.perceiveDate[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
      }
      this.tableList = [];
      let params = {
        startDate: this.queryParam.startDate,
        endDate: this.queryParam.endDate,
        devices: this.queryParam.devices,
        rfidCode: this.queryParam.rfidCode,
        sortField: this.queryParam.sortField,
        order: this.queryParam.order,
      };
      RFIDRecordSearchEx({ ...params, ...this.pageInfo })
        .then((res) => {
          const { total, entities } = res.data;
          this.pageInfo.total = total;
          this.tableList = entities || [];
          if (this.tableList.length) {
            this.tableList.forEach((e, idx) => {
              if (
                this.alreadyUpImageIds.length &&
                this.alreadyUpImageIds.includes(e.id)
              ) {
                e.isChecked = true;
                e._checked = true; // iview table的手动选中
                this.selectionList.push(e);
                this.$set(this.tableList, idx, e);
              }
            });
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchInfo(obj) {
      this.pageInfo.pageNumber = 1;
      if (obj.selectDeviceList) {
        // 处理已选择设备
        var ids = [];
        obj.selectDeviceList.forEach((item) => {
          ids.push(item.deviceId);
        });
        obj.devices = ids;
      }
      this.queryParam = obj;
      this.queryList();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.queryList();
    },
    checkAllHandler(val) {
      this.tableList = this.tableList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    checkHandler(e, i) {
      this.tableList[i].isChecked = e;
      this.checkAll =
        this.tableList.filter((e) => e.isChecked).length ===
        this.tableList.length
          ? true
          : false;
    },
    ...mapMutations({
      setNum: "countCoverage/setNum",
      setList: "countCoverage/setList",
    }),
    selectionChangeHandle(val) {
      this.selectionList = val;
    },
    dataAboveMapHandler() {
      let seleNum = this.selectionList.filter(
        (e) => !this.alreadyUpImageIds.includes(e.id)
      );
      if (!seleNum.length) {
        this.$Message.warning("请选择上图数据");
        return;
      }
      let newNumLayer = this.getNum.layerNum + this.getNewAddLayer.layer + 1; //图层
      let newNumPoints =
        this.getNum.pointsNum +
        this.getNewAddLayer.pointsInLayer +
        seleNum.length; //点位
      if (Number(this.getMaxLayer.maxNumberOfLayer) < newNumLayer) {
        this.$Message.warning("已达到图层最大创建数量");
        return;
      }
      if (Number(this.getMaxLayer.maxNumberOfPointsInLayer) < newNumPoints) {
        this.$Message.warning("已达到上图最大点位总量");
        return;
      }
      let num = JSON.stringify(this.getListNum);
      this.setList(num++);
      this.setNum({ layerNum: newNumLayer, pointsNum: newNumPoints });
      seleNum.map((item) => {
        item.delePoints = true;
        item.deleType = "rfid";
      });
      let list = seleNum.filter(
        (e) => !this.hasBeenOnTheMapList.includes(e.rfidCode)
      );

      this.$emit("dataAboveMapHandler", {
        type: "rfid",
        deleIdent: "rfid-" + this.getListNum,
        list,
      });
    },
    /**
     * 收藏
     */
    collection(data, flag) {
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType: 11,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.queryList();
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.queryList();
        });
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
</style>
