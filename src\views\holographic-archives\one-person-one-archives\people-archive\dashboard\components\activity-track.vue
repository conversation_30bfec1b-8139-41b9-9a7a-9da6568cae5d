<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-23 18:40:07
 * @Description: 
-->
<template>
  <ui-card title="活动规律">
    <barEchart
      v-if="series[0].data.length !== 0"
      :title="{}"
      :legend="legend"
      :grid="grid"
      :xAxis="xAxis"
      :yAxis="yAxis"
      :series="series"
    ></barEchart>
    <ui-loading v-if="trackingLoading" />
    <ui-empty v-if="series[0].data.length === 0"></ui-empty>
  </ui-card>
</template>
<script>
import barEchart from "@/components/echarts/bar-echart";

export default {
  components: { barEchart },
  props: {
    trackingLoading: {
      type: Boolean,
      default: false,
    },
    // 活动轨迹x轴
    xAxis: {
      type: Object,
      default: () => {},
    },
    // 活动轨迹数据
    series: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      legend: {
        show: false,
      },
      grid: {
        left: "0",
        top: "5%",
        right: "0.1%",
        bottom: "0",
        containLabel: true,
      },

      yAxis: {
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
        },
        splitLine: {
          lineStyle: {
            type: "solid",
            color: "#D3D7DE",
          },
        },
      },
    };
  },
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped></style>
