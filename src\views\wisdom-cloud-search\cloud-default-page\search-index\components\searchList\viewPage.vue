<!--
    * @FileDescription: 视图设备
    * @Author: H
    * @Date: 2023/10/07
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="view_page">
        <UiListCard type="device" :data="itemList" @archivesDetailHandle="archivesDetailHandle(itemList)" @collection="getList" />
    </div>
</template>

<script>
import UiListCard from '@/components/ui-list-card';
export default {
    props:{
        itemList:{
            type: Object,
            default: () =>{
                return {}
            }
        }
    },
    components: {
        UiListCard,
    },
    data(){
        return {

        }
    },
    methods: {
        // 详情
        archivesDetailHandle() {

        },
        // 收藏
        getList(){

        },
    },
}
</script>

<style lang='less' scoped>

</style>