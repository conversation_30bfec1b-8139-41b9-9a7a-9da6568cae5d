<template>
  <div class="base-search">
    <div class="inline">
      <ui-label class="inline mb-sm mr-lg" label="组织机构" :width="70">
        <tree-select
          :tree-data="treeData"
          nodeKey="orgCode"
          class="width-md"
          v-model="searchData.orgCode"
          @current-change="currentChange"
          placeholder="请选择组织机构"
        >
        </tree-select>
      </ui-label>
      <ui-label class="inline mr-lg" label="设备编码" :width="70">
        <Input v-model="searchData.deviceId" class="width-lg" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg" label="设备名称" :width="70">
        <Input v-model="searchData.deviceName" class="width-lg" placeholder="请输入设备名称"></Input>
      </ui-label>
      <ui-label class="inline mr-lg" label="检测结果" :width="70">
        <Select class="width-lg" v-model="searchData.checkStatus" clearable placeholder="请选择检测结果">
          <Option :value="1" label="正常"></Option>
          <Option :value="2" label="异常"></Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg" label="异常原因" :width="70">
        <Select class="width-lg" v-model="searchData.messages" placeholder="请选择异常原因" multiple :max-tag-count="1">
          <Option value="ERROR_TODAY_NO_DATA" label="昨日无抓拍"></Option>
          <Option value="ERROR_NO_DATA" label="无抓拍数据"></Option>
          <Option value="ERROR_TOO_LESS_DATA" label="抓拍数据过少"></Option>
          <Option value="ERROR_DATA_SWOOP" label="抓拍数量突降"></Option>
        </Select>
      </ui-label>
    </div>
    <ui-label :width="0" label="" class="inline search-button">
      <Button type="primary" class="mr-sm mb-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mb-sm" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
      <slot name="search"></slot>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    width: {
      type: Number,
      default: 155,
    },
    searchList: {
      type: Array,
      default() {
        return [];
      },
    },
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      searchData: {
        orgCode: '',
        deviceId: '',
        deviceName: '',
        messages: [],
        checkStatus: '',
        resultId: '',
      },
      cardSearchList: [],
      dictData: {},
      treeData: [],
    };
  },
  methods: {
    getOrgTreeList(val) {
      if (val.orgCodeList && val.orgCodeList.length) {
        let { orgCode, resultId } = val.orgCodeList.find((item) => !item.disabled) || {};
        this.searchData.orgCode = orgCode || '';
        this.searchData.resultId = resultId || '';
        this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(val.orgCodeList)), 'id', 'parentId');
      }
    },
    currentChange({ orgCode, resultId }) {
      this.searchData.orgCode = orgCode;
      this.searchData.resultId = resultId;
      this.$emit('params-change', this.searchData);
    },
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetClick() {
      this.resetSearchDataMx1(this.searchData);
    },
    resetSearchDataMx1() {
      this.searchData = {
        orgCode: '',
        deviceId: '',
        deviceName: '',
        messages: [],
        checkStatus: '',
        resultId: '',
      };
      this.$emit('startSearch', this.searchData);
    },
  },
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.getOrgTreeList(val);
        this.copySearchDataMx(this.searchData);
        this.$nextTick(() => {
          this.startSearch();
        });
      },
    },
  },
  components: {
    TreeSelect: require('./tree-select').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .search-button {
    white-space: nowrap;
  }
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
</style>
