<template>
  <div class="auto-fill assetmanagement">
    <!-- 地图设备切换 -->
    <!-- <Tabs class="tabs" ref="tabs" @changeTab="changeTab" /> -->
    <EquipmentList v-if="activeKey == '0'" @mapInfo="mapInfo" />
    <!-- <DeviceMap ref="deviceMap" v-else>地图模式</DeviceMap> -->
  </div>
</template>
<script>
export default {
  name: 'assetmanagement',
  components: {
    EquipmentList: require('./equipmentList').default,
    // DeviceMap: require('./device-map').default,
    // Tabs: require('./tabs').default,
  },
  props: {},
  data() {
    return {
      activeKey: 0,
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  activated() {
    if (this.$refs.tabs) this.$refs.tabs.activeKey = 0;
  },
  mounted() {},
  methods: {
    changeTab(activeKey) {
      this.activeKey = activeKey;
    },
    // 地图跳转
    mapInfo(row) {
      this.activeKey = 1;
      this.$refs.tabs.activeKey = '1';
      this.$nextTick(() => {
        this.$refs.deviceMap.queryDeviceInfo(row.deviceId);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.equipmentlibrary {
  position: relative;
  .tabs {
    position: absolute;
    right: 30px;
    top: 20px;
    z-index: 11;
  }
}
</style>
