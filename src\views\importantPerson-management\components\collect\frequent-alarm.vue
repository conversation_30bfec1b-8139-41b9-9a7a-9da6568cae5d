<!--
 * @Date: 2025-01-15 10:53:49
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-28 17:17:58
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\frequent-alarm.vue
-->
<template>
  <div class="frequent-box">
    <div class="head-img-box">
      <div class="img-box">
        <img :src="data.traitImg" />
      </div>
      <div class="name-box">{{ data.name }}</div>
    </div>
    <div class="text-box">
      <div class="tag-text ellipsis">（{{ data?.bizLabels?.join(",") }}）</div>
      <div class="address-text ellipsis">{{ data.placeName }}</div>
      <div class="sub-text ellipsis">
        {{ data.absTime || "--" }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FrequentAlarm",
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.primary {
  color: #2c86f8;
}
.frequent-box {
  width: 120px;
  height: 100%;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  &::after {
    content: "";
    width: 100%;
    height: 90px;
    display: block;
    position: absolute;
    top: 25%;
    z-index: -1;
    background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
  }
  .head-img-box {
    height: 80px;
    width: 80px;
    overflow: hidden;
    border: 1px solid #45e8ff;
    border-radius: 50%;
    position: relative;
    margin: 0 auto;
    .name-box {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 22px;
      text-align: center;
      line-height: 22px;
      background: rgba(0, 0, 0, 0.6);
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }

    .img-box {
      width: 100%;
      height: 100%;
      > img {
        width: 100%;
        height: 100%;
      }
    }
    .flex-center {
      position: relative;
    }
  }
  .text-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    font-size: 12px;
    overflow: hidden;
    .tag-text {
      font-weight: 700;
      color: #ea4a36;
      text-align: center;
    }
    .address-text {
      font-weight: 400;
      font-size: 14px;
      color: #2c86f8;
      text-align: center;
    }
    .sub-text {
      text-align: center;
      color: rgba(0, 0, 0, 0.35);
    }
  }
}
</style>
