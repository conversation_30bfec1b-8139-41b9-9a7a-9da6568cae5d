<template>
  <div class="top" :class="visible ? 'more-search-show' : ''">
    <RadioGroup
      v-model="queryParam.operationType"
      type="button"
      @on-change="radioChange"
    >
      <Radio
        v-for="(item, index) in radioList"
        :key="index"
        :label="item.key"
        >{{ item.value }}</Radio
      >
    </RadioGroup>
    <div class="right">
      <div class="inline-box">
        <div class="title wid100">报警时间：</div>
        <ui-quick-date
          class="quick-date-wrap"
          ref="quickDateRef"
          v-model="dateType"
          @change="changeDateType"
        />
        <div class="item fixWidth">
          <div class="title wid100">任务类型：</div>
          <Select
            v-model="queryParam.taskParsingType"
            clearable
            @on-change="changeTaskType"
          >
            <Option
              v-for="item in taskTypeList"
              :value="item.value"
              :key="item.value"
              placeholder="请选择任务类型"
              >{{ item.label }}</Option
            >
          </Select>
        </div>
        <div class="item fixWidth">
          <div class="title wid100">任务名称：</div>
          <Select
            v-model="queryParam.taskIds"
            filterable
            clearable
            multiple
            :max-tag-count="1"
            placeholder="请选择任务"
          >
            <Option
              v-for="item in taskList"
              :key="item.id"
              :value="item.id"
              >{{ item.taskName }}</Option
            >
          </Select>
        </div>
      </div>
      <div class="btn">
        <Button class="margin" type="primary" @click="query()">查询</Button>
        <Button @click="reset()">重置</Button>
      </div>
      <div class="btn-group" @click="toggleSearch(!visible)">
        <img src="@/assets/img/down-circle-icon.png" alt />
        <div class="more more-search-text">
          {{ visible ? "普通检索" : "高级检索" }}
        </div>
      </div>
    </div>
    <div class="more-search">
      <slot></slot>
    </div>
  </div>
</template>
<script>
import { getLLMCompareTaskPageList } from "@/api/semantic-placement";
import { taskTypeList} from "./enum";

export default {
  props: {
    radioList: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      dateType: 1,
      queryParam: {
        operationType: 99,
        taskParsingType: "",
        taskIds: [],
        endTime: "",
        startTime: "",
      },
      taskTypeList: taskTypeList,
      visible: false,
      taskList: [],
    };
  },
  async mounted() {
    let query = this.$route.query;
    if (query.taskId) {
      this.queryParam.taskIds = [Number(query.taskId)];
    }
    if(query.taskType){
      this.queryParam.taskParsingType = Number(query.taskType);
      this.queryTaskList(this.queryParam.taskParsingType);
    }
    if (query.dateType) {
      this.dateType = Number(query.dateType);
      if (this.dateType === 4) {
        // 自定义时间，当为自定义时间时，query必定携带dateRange起止时间
        let { startDate, endDate } = query;
        this.changeDateType({ startDate, endDate });
        this.$nextTick(() => {
          this.$refs.quickDateRef.setDefaultDate([startDate, endDate]);
        });
      } else {
        this.$nextTick(() => {
          this.changeDateType(this.$refs.quickDateRef.getDate());
        });
      }
    } else {
      this.changeDateType(this.$refs.quickDateRef.getDate());
    }
  },
  methods: {
    /**
     * @description: 切换状态
     */
    radioChange() {
      this.$emit("query");
    },

    /**
     * @description: 切换日期
     * @param {object} value 当前选中的时间区间
     */
    changeDateType(value) {
      this.queryParam.startTime = value.startDate;
      this.queryParam.endTime = value.endDate;
    },

    /**
     * @description: 查询
     */
    query() {
      this.visible = false;
      this.$emit("query");
    },
    changeTaskType(val) {
      this.queryParam.taskIds = [];
      if (val) this.queryTaskList(val);
      else this.taskList = [];
    },
    async queryTaskList(val) {
      const res = await getLLMCompareTaskPageList({
        taskParsingType: val,
        pageSize: 9999,
        pageNumber: 1,
      });
      this.taskList = res?.data?.entities || [];
    },
    /**
     * @description: 重置
     */
    reset() {
      this.dateType = 1;
      // 如果重置前选了自定义时间，需要置空
      this.$refs.quickDateRef.setDefaultDate();
      this.queryParam.operationType = 99;
      this.queryParam.taskIds = [];
      this.queryParam.taskParsingType = "";
      this.$nextTick(() => {
        this.changeDateType(this.$refs.quickDateRef.getDate());
        this.$emit("reset");
      });
    },

    /**
     * @description: 选中tag值
     * @param {string} key 当前的类别
     * @param {object} item 选中的值
     */
    selectItem(key, item) {
      // 具体业务处理逻辑
      if (item) {
        this.queryParam[key] = item.value;
      } else {
        // 全部选项，不返回数据到后端
        this.queryParam[key] = null;
      }
    },

    /**
     * @description: 获取查询参数，暴露给父组件的方法
     * @return {object}
     */
    getQueryParams() {
      const { operationType, ...query } = this.queryParam;
      return {
        ...query,
        operationTypes: operationType == 99 ? [] : [operationType],
      };
    },

    /**
     * @description: 收起 | 展开高级搜索
     * @param {boolean} flag 状态
     */
    toggleSearch(flag = false) {
      this.visible = flag;
    },
  },
};
</script>
<style lang="less" scoped>
.top {
  position: relative;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dfdfdf;
  padding-bottom: 16px;
  /deep/ .ivu-radio {
    margin-right: 0 !important;
  }
  /deep/ .ivu-radio-wrapper-checked {
    background: rgba(44, 134, 248, 0.1) !important;
  }
  .right {
    display: flex;
    .inline-box {
      display: inline-flex;
      align-items: center;
    }
    .quick-date-wrap {
      display: flex;
      align-items: center;
    }
    .item {
      display: flex;
      .title {
        line-height: 36px;
        font-size: 14px;
      }
      .wid100 {
        width: 100px;
        min-width: 70px;
      }
      .wid200 {
        width: 200px;
        min-width: 70px;
      }
    }
    .fixWidth {
      width: 250px;
      margin-left: 26px;
    }
    .time-form {
      display: flex;
      align-items: center;
    }
    .right20 {
      margin-right: 20px;
    }
    .btn {
      padding: 0 30px;
      .margin {
        margin-right: 12px;
      }
    }
    .more {
      line-height: 36px;
      color: #1678f5;
      cursor: pointer;
    }
  }
}

.more-search {
  display: flex;
  position: absolute;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  background: #fff;
  z-index: 20;
  max-height: 0px;
  top: 100%;
  left: 0;
  transition: 0.3s;
  overflow: hidden;
  flex-direction: column;
  box-shadow: 0 2px 3px #b7cbe5;
}

.btn-group {
  display: flex;
  align-items: end;
  .more-search-text {
    display: flex;
    align-items: center;
    cursor: pointer;
    // margin-right: 30px;
    .icon-jiantou {
      margin-left: 2px;
      font-size: 18px;
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
.more-search-show {
  .more-search {
    max-height: 300px;
    transition: 0.7s;
    padding-top: 20px;
    margin-top: 1px;
  }
  .more-search-text {
    /deep/ .icon-jiantou {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
}
.btn-group {
  height: 34px;
  display: flex;
  align-items: center;
  cursor: pointer;
  img {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    transform: rotate(180deg);
    transition: transform 0.2s;
  }
}
.more-search-show {
  .advanced-search {
    max-height: 400px;
    transition: max-height 0.5s;
  }
  .btn-group {
    /deep/img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
}
</style>
