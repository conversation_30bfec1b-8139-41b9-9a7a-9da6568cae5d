<template>
  <div class="instorage-config">
    <slot name="custorm-content"></slot>
    <p class="mb-md ml-sp base-text-color">以下字段需特殊处理（优先级最高）：</p>
    <transfer-table
      :left-table-columns="columns1"
      :right-table-columns="columns2"
      :left-table-data="propertyList"
      :right-table-data="targetList"
      :leftLoading="leftLoading"
      :rightLoading="rightLoading"
      @onLeftToRight="selectionChange"
    >
      <template #left-title>
        <div class="mb-sm">
          <span class="base-text-color">待选择待入库字段</span>
        </div>
      </template>
      <template #right-title>
        <div class="mb-sm">
          <span class="base-text-color">请确定入库策略</span><span class="font-red ml-sm">(注:不能为空)</span>
        </div>
      </template>
    </transfer-table>
  </div>
</template>
<script>
export default {
  props: {
    propertyList: {
      default: () => [],
    },
    isFilterId: {
      type: Boolean,
      default: true,
    }, // 是否过滤id
  },
  data() {
    return {
      newPropertyList: [],
      columns1: Object.freeze([
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ]),
      columns2: Object.freeze([
        { title: ' ', align: 'center', width: 20 },
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
        {
          title: '入库策略',
          key: 'addType',
          render: (h, params) => {
            const row = params.row;
            const index = params.index;
            let stragety = [
              { dataKey: 1, dataValue: '覆盖' },
              { dataKey: 2, dataValue: '追加' },
              { dataKey: 3, dataValue: '舍弃' },
            ];
            return h(
              'i-select',
              {
                props: {
                  value: row.addType,
                  disabled: row.disabled,
                  placeholder: '请选择',
                  transfer: true,
                },
                on: {
                  'on-change': (val) => {
                    row.addType = val;
                    this.newPropertyList.forEach((item) => {
                      item.propertyName === row.checkColumnName ? this.$set(item, 'addType', val) : '';
                    });
                    this.targetList.splice(index, 1, row);
                  },
                },
              },
              stragety.map((row) => {
                return h('i-option', {
                  props: {
                    value: row.dataKey,
                    label: row.dataValue,
                  },
                });
              }),
            );
          },
        },
        {
          title: '操作',
          key: 'action',
          width: 60,
          align: 'center',
          render: (h, params) => {
            let row = params.row;
            const index = params.index;
            return h(
              'span',
              {
                props: {
                  row: row,
                },
                class: ['font-active-color', 'pointer'],
                on: {
                  click: () => {
                    const nameArr = ['id', 'deviceId'];
                    if (nameArr.includes(row.checkColumnName)) return false;
                    this.remove(index, row);
                  },
                },
              },
              this.getLabel(row),
            );
          },
        },
      ]),
      targetList: [],
      indexRuleId: '',
      taskSchemeId: '',
      leftLoading: false,
      rightLoading: false,
      extraParam: { detectionRange: { isDetect: '0', mode: 'all' } },
      indexConfig: {},
    };
  },
  methods: {
    getLabel(row) {
      const nameArr = ['id', 'deviceId'];
      let columnContent = '';
      columnContent = nameArr.includes(row.checkColumnName) ? '--' : '移除';
      return [columnContent];
    },
    selectionChange(selection) {
      const selectedNames = selection.map((item) => item.propertyName);
      this.newPropertyList.forEach((item) => {
        this.$set(item, '_checked', selectedNames.includes(item.propertyName) ? true : false);
      });
      this.targetList = selection.map((item) => {
        const obj = {
          checkColumnName: item.propertyName,
          checkColumnValue: item.propertyColumn,
          addType: item.addType || 1,
          disabled: false,
        };
        if (item.propertyName === 'id') {
          obj.addType = item.addType || 2;
        }
        if (item.propertyName === 'deviceId') {
          obj.disabled = true;
        }
        return obj;
      });
    },
    cancelSelectTable({ row }) {
      this.initNewPropertyList(row, 'propertyName');
    },
    remove(index, row) {
      this.newPropertyList.forEach((item) => {
        if (item.propertyName === row.checkColumnName) {
          this.$set(item, '_checked', false);
        }
      });
      this.targetList.splice(index, 1);
      this.initNewPropertyList(row, 'checkColumnName');
    },
    initNewPropertyList(row, propertyKey) {
      let propertyObj = this.propertyList.find((item) => item.propertyName === row[propertyKey]);
      this.newPropertyList.forEach((item) => {
        if (item.propertyName === propertyObj.propertyName) {
          this.$set(item, 'addType', propertyObj.addType);
        }
      });
    },
  },
  computed: {},
  watch: {
    propertyList: {
      handler(val) {
        this.newPropertyList = this.$util.common.deepCopy(val);
        if (this.isFilterId) {
          this.newPropertyList = this.newPropertyList.filter((item) => item.propertyName !== 'id');
        }
        const defaultList = this.newPropertyList.filter((item) => item._checked);
        this.targetList = defaultList.map((item) => {
          item.checkColumnName = item.propertyName;
          item.checkColumnValue = item.propertyColumn;
          return item;
        });
      },
      immediate: true,
    },
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.ml-sp {
  margin-left: 22px;
}
.instorage-config {
  padding: 0 20px;
  .section-box {
    color: var(--color-content);
  }
  @{_deep} .transfer-table-wrapper {
    height: 500px;
  }
}
</style>
