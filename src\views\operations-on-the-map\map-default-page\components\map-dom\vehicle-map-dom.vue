<template>
  <div class="dom">
    <header v-if="showHeader">
      <span>抓拍详情</span>
      <ui-icon
        type="close"
        :size="14"
        @click.native="() => $emit('close')"
      ></ui-icon>
    </header>
    <section class="dom-content">
      <!-- <div class="vehicle-list">
        <template v-for="(item, index) in collectionList">
          <div :key="index" @click="selectCollectionHandler(index)" :class="{ active: currentCollectionIndex === index }">
            <img src="~@/assets/img/mock/vehicle.png" alt="" />
            <span>{{ item.count }}</span>
          </div>
        </template>
      </div> -->
      <carousel
        ref="carousel"
        v-show="positionPoints.length"
        :clickIndexId="clickIndexId"
        :same-position-point="samePositionPoint"
        @changeVid="changeVid"
      ></carousel>
      <vehicle-map ref="vehiclemap" :vehicleInfo="vehicleInfo"></vehicle-map>
    </section>
    <footer v-if="cutIcon">
      <search-around
        @preDetial="preDetial"
        @nextDetail="nextDetail"
      ></search-around>
    </footer>
  </div>
</template>
<script>
import operateBar from "@/components/mapdom/operate-bar.vue";
import searchAround from "../search-around.vue";
import carousel from "./carousel.vue";
import vehicleMap from "@/components/mapdom/vehicle-map";
export default {
  components: {
    operateBar,
    searchAround,
    carousel,
    vehicleMap,
  },
  props: {
    positionPoints: {
      type: Array,
      default: () => [],
    },
    clickIndexId: {
      type: [Number, String],
      default: 0,
    },
    // 是否展示头部，现在乱七八糟的，加个判断
    showHeader: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    imgStyle() {
      const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
      const style = {
        transform: `scale(${scale}) rotate(${deg}deg)`,
        transition: enableTransition ? "transform .3s" : "",
        "margin-left": `${offsetX}px`,
        "margin-top": `${offsetY}px`,
      };
      style.maxWidth = style.maxHeight = "100%";
      return style;
    },
    // samePositionPoint(){
    //     return this.positionPoints.filter((item,index) => {
    //         if( item.deviceId === this.vehicleInfo.deviceId ){
    //             item.Index = index + 1
    //         }
    //         return item.deviceId === this.vehicleInfo.deviceId
    //     })
    // },
  },
  data() {
    return {
      tabList: [
        {
          name: "场景大图",
        },
        // {
        //   name: '历史视频'
        // }
      ],
      currentTabIndex: 0,
      collectionList: [{ count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }],
      currentCollectionIndex: 0,
      isChoose: false,
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: true,
      },
      vehicleInfo: {},
      vehicleArchives: {
        photoUrl: "",
      },
      makerName: "",
      cutIcon: true,
      samePositionPoint: [],
    };
  },
  methods: {
    preDetial() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.vehicleInfo.featureId
      );
      if (Index < 1) return;
      this.$emit("preDetial", Index - 1);
    },
    nextDetail() {
      let Index = this.positionPoints.findIndex(
        (item) => item.featureId === this.vehicleInfo.featureId
      );
      if (Index >= this.positionPoints.length - 1) return;
      this.$emit("nextDetail", Index + 1);
    },
    // 车辆默认先不调档案接口
    init(
      { plateNo = "" },
      pointItem,
      name = "",
      cutIcon = true,
      position = []
    ) {
      // 为了兼容之前的代码，暂时先返回写死的data: true
      return new Promise((resolve) => {
        this.transform = {
          scale: 1,
          deg: 0,
          offsetX: 0,
          offsetY: 0,
          enableTransition: false,
        };
        this.vehicleInfo = { ...pointItem };
        this.$refs.vehiclemap.init();
        this.makerName = name;
        this.cutIcon = cutIcon; //用于判断模态框下方左右切换显隐
        if (position.length > 0) {
          // 高级搜索，数据上图
          this.tabAdvanced(position);
        }
        if (this.clickIndexId) {
          //用于顶部切换
          this.tabmsgList();
        }
        resolve({ data: true });
      });
    },
    changeVid(item) {
      this.$emit("changeListTab", { ...item, name: this.makerName });
      this.vehicleInfo = { ...item };
      this.$refs.vehiclemap.init();
    },
    tabClick(index) {
      this.currentTabIndex = index;
    },
    tabmsgList() {
      this.samePositionPoint = this.positionPoints.filter((item, index) => {
        if (item.deviceId === this.vehicleInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.vehicleInfo.deviceId;
      });
      let tabIndex = 0;
      this.samePositionPoint.map((item, index) => {
        if (item.id == this.clickIndexId) {
          tabIndex = index;
        }
      });
      this.$refs.carousel.init(tabIndex);
    },
    tabAdvanced(list) {
      this.samePositionPoint = list.filter((item, index) => {
        if (item.deviceId === this.vehicleInfo.deviceId) {
          item.Index = index + 1;
        }
        return item.deviceId === this.vehicleInfo.deviceId;
      });
      let tabIndex = 0;
      this.$refs.carousel.init(tabIndex);
    },
    selectCollectionHandler(index) {
      this.currentCollectionIndex = index;
    },
    handleActions(action, options = {}) {
      if (this.loading) return;
      const { zoomRate, enableTransition } = {
        zoomRate: 0.2,
        enableTransition: true,
        ...options,
      };
      const { transform } = this;
      switch (action) {
        case "zoomOut":
          if (transform.scale > 0.2) {
            transform.scale = parseFloat(
              (transform.scale - zoomRate).toFixed(3)
            );
          }
          break;
        case "zoomIn":
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
          break;
      }
      transform.enableTransition = enableTransition;
    },
    handleMouseDown(e) {
      const { scale } = this.transform;
      if (scale === 1 || e.button !== 0) return;
      const { rafThrottle, on, off } = this.$util.common;
      const { offsetX, offsetY } = this.transform;
      const startX = e.pageX;
      const startY = e.pageY;
      this._dragHandler = rafThrottle((ev) => {
        this.transform.offsetX = offsetX + ev.pageX - startX;
        this.transform.offsetY = offsetY + ev.pageY - startY;
      });
      const dom = this.$refs["img"];
      on(dom, "mousemove", this._dragHandler);
      dom.addEventListener("mouseup", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      dom.addEventListener("mouseleave", () => {
        off(dom, "mousemove", this._dragHandler);
      });
      e.preventDefault();
    },
    handleDownload() {
      this.imgUrl = this.vehicleArchives.photoUrl;
      //下载图片地址和图片名
      let image = new Image();
      // 解决跨域 Canvas 污染问题
      image.setAttribute("crossOrigin", "anonymous");
      image.onload = function () {
        let canvas = document.createElement("canvas");
        canvas.width = image.width;
        canvas.height = image.height;
        let context = canvas.getContext("2d");
        context.drawImage(image, 0, 0, image.width, image.height);
        let url = canvas.toDataURL("image/png"); //得到图片的base64编码数据
        let a = document.createElement("a"); // 生成一个a元素
        let event = new MouseEvent("click"); // 创建一个单击事件
        a.download = "photo"; // 设置图片名称
        a.href = url; // 将生成的URL设置为a.href属性
        a.dispatchEvent(event); // 触发a的单击事件
      };
      image.src = this.imgUrl;
    },
  },
};
</script>
<style lang="less" scoped>
.dom-wrapper {
  position: relative;
  //   padding: 10px 28px 25px 0;
  padding: 10px 28px 0px 0;
  height: 100%;
}

.dom {
  width: 100%;
  // height: 520px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  position: relative;
  > header {
    height: 36px;
    line-height: 36px;
    background: rgba(211, 215, 222, 0.3);
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    border-radius: 4px 4px 0px 0px;
    color: rgba(0, 0, 0, 0.9);
    font-weight: bold;
    font-size: 14px;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
  }
  .dom-content {
    padding: 10px 20px;
    font-size: 14px;
    height: calc(~"100% - 46px");
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .vehicle-list {
      display: flex;
      margin-bottom: 5px;
      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        border: 1px solid #d3d7de;
        margin-right: 8px;
        cursor: pointer;
        position: relative;
        > img {
          width: 100%;
          height: 100%;
        }
        > span {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: #2c86f8;
          text-align: center;
          border-radius: 0px 0px 4px 0px;
          font-size: 12px;
          color: #ffffff;
          position: absolute;
          left: -1px;
          top: -1px;
        }
      }
      .active {
        border: 3px solid rgba(44, 134, 248, 1);
        > span {
          left: -3px;
          top: -3px;
        }
      }
    }

    .vehicle-info {
      margin-top: 7px;
      display: flex;
      &-left {
        width: 200px;
        height: 200px;
        position: relative;
        .smallImg {
          width: 200px;
          height: 200px;
          border: 1px solid #d3d7de;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 10px;
          background: #f9f9f9;
          > img {
            width: auto;
            height: auto;
            max-height: 200px;
            max-width: 200px;
          }
        }

        .similarity {
          position: absolute;
          left: 4px;
          top: 4px;
          border-radius: 4px;
          color: #fff;
          text-align: center;
        }
        .traffic-record {
          margin-top: 10px;
        }
        .personnel-files {
          margin-top: 10px;
          .complete-file {
            height: 18px;
            line-height: 18px;
            margin: 15px 0 17px 0;
            cursor: pointer;
            display: flex;
            align-items: center;
            > span {
              margin-left: 5px;
            }
          }
        }
      }
      &-right {
        width: 100%;
        height: 412px;
        .right-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .tablist {
            height: 28px;
            line-height: 28px;
            width: 400px !important;
            margin: 0;
            display: flex;
            align-items: center;
            .title {
              font-size: 14px;
              color: rgba(0, 0, 0, 0.9);
              padding-left: 9px;
              position: relative;
              height: 20px;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: space-between;
              > span {
                font-weight: bold;
              }
            }
            .title:before {
              content: "";
              position: absolute;
              width: 3px;
              height: 16px;
              top: 50%;
              transform: translateY(-50%);
              left: 0;
              background: #2c86f8;
            }
            .ivu-tabs-tab {
              float: left;
              border: 1px solid #2c86f8;
              border-right: none;
              padding: 0 15px;
              color: #2c86f8;
              &:hover {
                background: #2c86f8;
                color: #ffffff;
              }
              &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-right: none;
              }
              &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-right: 1px solid #2c86f8;
              }
            }
            .active {
              background: #2c86f8;
              color: #fff;
            }
          }
        }
        .right-content {
          margin-top: 6px;
          max-width: 580px;
          height: 370px;
          border: 1px solid #d3d7de;
          background: #f9f9f9;
          .complete-face {
            width: 580px;
            height: 430px;
            position: relative;
            text-align: center;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9f9f9;
            > img {
              width: 100%;
              height: auto;
              max-height: 430px;
              // max-width: 580px;
            }

            > span {
              display: inline-block;
              width: 100%;
              height: 30px;
              line-height: 30px;
              position: absolute;
              right: 0;
              bottom: 0;
              background: rgba(0, 0, 0, 0.5);
              .iconfont {
                color: #fff !important;
                padding: 0 12px;
                cursor: pointer;
              }
            }
          }
          .video {
            /deep/.easy-player {
              margin-top: 60px;
            }
          }
        }

        margin-left: 15px;
      }
    }
    .dom-content-p {
      margin-top: 6px;
      width: 100%;
      display: flex;
      height: 16px;
      line-height: 16px;
      .label {
        font-size: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        width: 55px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }
      .message {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .plateNo {
        cursor: pointer;
        color: #2c86f8;
      }
      .address {
        font-size: 12px;
        font-weight: bold;
        width: 140px;
        // margin-left: 12px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  > footer {
    height: 50px;
    border-top: 1px solid #d3d7de;
    padding: 0 20px;
    height: 55px;
    line-height: 55px;
    > span {
      color: #2c86f8;
      margin-right: 20px;
      cursor: pointer;
    }
  }
}
</style>
