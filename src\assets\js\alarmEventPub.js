/*
 * @Author: zhengming<PERSON> <EMAIL>
 * @Date: 2025-04-03 11:39:52
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-04-03 11:40:55
 * @FilePath: \icbd-view\src\assets\js\alarmEventPub.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
class EventEmitter {
  constructor() {
    // 存储事件及其对应的回调函数列表
    this.events = {};
  }

  /**
   * 订阅事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 事件触发时执行的回调函数
   */
  on(eventName, callback) {
    if (!this.events[eventName]) {
      this.events[eventName] = [];
    }
    this.events[eventName].push(callback);
  }

  /**
   * 发布事件
   * @param {string} eventName - 事件名称
   * @param {...any} args - 传递给回调函数的参数
   */
  emit(eventName, ...args) {
    if (this.events[eventName]) {
      this.events[eventName].forEach((callback) => callback(...args));
    }
  }

  /**
   * 取消订阅事件
   * @param {string} eventName - 事件名称
   * @param {function} callback - 要取消的回调函数
   */
  off(eventName, callback) {
    if (this.events[eventName]) {
      this.events[eventName] = this.events[eventName].filter(
        (cb) => cb !== callback
      );
    }
  }
}

const emitter = new EventEmitter();

export const alarmEventName = "alarmInfo";
export default emitter;

// // 订阅事件
// const callback = (message) => {
//     console.log(`Received message: ${message}`);
// };
// emitter.on('message', callback);

// // 发布事件
// emitter.emit('message', 'Hello, world!');

// // 取消订阅
// emitter.off('message', callback);

// // 再次发布事件，此时不会触发回调
// emitter.emit('message', 'This message will not be received.');
