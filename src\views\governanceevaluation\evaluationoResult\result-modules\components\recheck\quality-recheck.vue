<template>
  <basic-recheck v-bind="getAttrs" v-on="$listeners" @handleCancel="handleCancel">
    <template>
      <FormItem label="检测方法" prop="videoQualityDetectMode">
        <Select v-model="formData.videoQualityDetectMode" placeholder="请选择" transfer class="width-lg">
          <Option v-for="e in checkModelList" :key="e.dataKey" :value="e.dataKey">{{ e.dataValue }} </Option>
        </Select>
      </FormItem>
      <FormItem label="检测内容" prop="detectContent" v-if="this.formData.videoQualityDetectMode !== '2'">
        <template v-if="this.formData.videoQualityDetectMode === '1'">
          <p v-for="(qItem, qIndex) in detectionRules" :key="'q' + qIndex">
            <Checkbox true-value="1" false-value="0" v-model="formData[qItem.key]" :disabled="qItem.disabled">
              {{ qItem.text }}
            </Checkbox>
          </p>
        </template>
        <!-- 精简模型 -->
        <template v-if="this.formData.videoQualityDetectMode === '3'">
          <CheckboxGroup v-model="wangliCheckContent">
            <Checkbox
              v-for="(wqItem, wqIndex) in detectionRules"
              :key="'wq' + wqIndex"
              :label="wqIndex"
              :disabled="wqItem.disabled"
            >
              {{ wqItem.text }}
            </Checkbox>
          </CheckboxGroup>
        </template>
      </FormItem>
      <!--      综合质量评分    -->
      <FormItem
        required
        class="quality-score"
        label="综合质量评分低于"
        v-if="this.formData.videoQualityDetectMode === '3'"
      >
        <FormItem prop="threshold" label="">
          <Input class="width-mini" v-model="formData.threshold" />
          <span class="params-suffix ml-mini"> 分，且单项异常比高于 </span>
        </FormItem>
        <FormItem prop="minThreshold">
          <Input class="width-mini ml-xs" v-model="formData.minThreshold" />
          <span class="params-suffix"> %非必填，则图像质量不合格</span>
        </FormItem>
      </FormItem>
      <!--      说明    -->
      <FormItem label="说明：" v-if="this.formData.videoQualityDetectMode === '3'">
        <div class="font-red">
          图像质量越好，综合评分越高，多个检测项有细微问题（单项异常占比很低），也可能导致整体评分低，
          如果认为这类图片合格，则可以设置单项异常占比参数，综合质量评分低于设置值，
          且某一异常项占比较超过设置值才判定为不合格
        </div>
      </FormItem>
    </template>
  </basic-recheck>
</template>
<script>
import videoConfigsData from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/video-config';
const configData = videoConfigsData.data();
export default {
  inheritAttrs: false,
  props: {
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      checkModelList: configData.qualityCheckMethods,
      wangliCheckContent: [0, 1, 2, 3, 4, 5],
      formData: {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        videoQualityDetectMode: '',
        threshold: '80',
        minThreshold: '80',
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        isUpdatePhyStatus: '',
      },
    };
  },
  created() {
    this.resetForm();
  },
  methods: {
    handleCancel() {
      this.formData = {
        model: 'UNQUALIFIED', // 复检设备
        plan: '1', // 复检计划
        videoQualityDetectMode: '',
        threshold: '80',
        minThreshold: '80',
        scheduletime: '', // 自定义时间
        maxCount: 1, // 复检次数
        deviceIds: [],
        isUpdatePhyStatus: '',
      };
      this.resetForm();
    },
    resetForm() {
      configData.videoQualityCheckContent.forEach((row) => {
        this.$set(this.formData, row.key, '0');
      });
    },
  },
  watch: {},
  computed: {
    getAttrs() {
      let specificConfig = null;
      // 根据不同的检测方法需要不同的参数 因为不同的检测方法传入给后端的参数是不同的
      if (this.formData.videoQualityDetectMode === '1') {
        specificConfig = {};
        this.detectionRules.forEach((row) => {
          specificConfig[row.key] = this.formData[row.key];
        });
      } else if (this.formData.videoQualityDetectMode === '3') {
        specificConfig = {
          threshold: this.formData.threshold,
          minThreshold: this.formData.minThreshold,
        };
      } else {
        specificConfig = {
          isUpdatePhyStatus: this.formData.isUpdatePhyStatus || undefined,
        };
      }
      specificConfig.videoQualityDetectMode = this.formData.videoQualityDetectMode;
      specificConfig.isUpdatePhyStatus = this.formData?.isUpdatePhyStatus;
      return {
        moduleData: this.moduleData,
        formData: this.formData,
        specificConfig: specificConfig,
        ...this.$attrs,
      };
    },
    detectionRules() {
      if (this.formData.videoQualityDetectMode === '1') {
        return configData.videoQualityCheckContent;
      } else if (this.formData.videoQualityDetectMode === '3') {
        return configData.wangliVideoQualityCheckContent;
      }
      return [];
    },
  },
  components: {
    BasicRecheck: require('./basic-recheck.vue').default,
  },
};
</script>
<style lang="less" scoped>
.params-suffix {
  color: #fff;
  margin-left: 5px;
}
.quality-score {
  @{_deep} .ivu-form-item-content {
    margin-left: 0 !important;
    display: flex;
    align-items: center;
  }
}
</style>
