<template>
  <div class="alarm-list height-full" v-ui-loading="{ loading: listLoading, tableData: cardList }">
    <div class="archives-list-wrapper" ref="listRef">
      <!-- <alarm-card
        v-for="(item, index) in 2"
        :key="index"
        :data="item"
        :columns="item.cardColumns"
        :src="item.src"
        class="mr-sm mb-sm"
        @click="emit('onClickArchives', item)"
        :is-confidence="!!item?.idNumber"
        style="height: 175px"
      > -->
        <!-- <template v-for="(one, key, i) in slots" :key="i" v-slot:[key]>
          <slot :name="key" :item="item"/>
        </template> -->
      <!-- </alarm-card> -->
      <alarmCard class="alarnRow" v-for="item in 20" :key="item"/>
    </div>
    <!-- <ui-page
      v-model:page-num="pageData.pageNum"
      v-model:page-size="pageData.pageSize"
      :total-count="totalCount"
      @changePage="changePage"
      @changePageSize="changePageSize"
      :has-last="false"
    /> -->
  </div>
</template>
<script>
  import alarmCard from './sensory-card.vue'
  export default {
    components: { alarmCard },
    data() {
      return {}
    }
  }
</script>

<style lang="less" scoped>
// .archives-list {
//   display: flex;
//   flex-direction: column;
//   margin-top: 10px;
//   overflow: hidden;

  .archives-list-wrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    align-content: flex-start;
    overflow-y: scroll;
    ::v-deep(.card-container) {
      width: 344px;
    }
  }
// }
.alarm-list {
  display: flex;
  /* flex-direction: row; */
  flex-flow: wrap;
  justify-content: space-between;
  .archives-list-wrapper {
    display: flex;
    flex-wrap: wrap;
    flex: 1;
    align-content: flex-start;
    overflow-y: scroll;
    ::v-deep(.card-container) {
      width: 344px;
    }
  }
  .alarnRow {
    // width: 20%;
    // width: calc( 20% - 10px);
    width: ~'calc(20% - 10px)';
    margin-top: 12px;
    background: #fdfdfd;
  }
}
</style>
