<template>
  <div class="auto-fill">
    <TableList
      ref="infoList"
      class="table-list"
      :columns="columns"
      :minusHeight="minusHeight"
      :loadData="loadDataList"
      :paging="false"
      listKey="data"
    >
      <!-- 检索 -->
      <template slot="search">
        <div class="search-container">
          <div class="jump">
            <ui-breadcrumb :data="breadcrumbData" @change="handleChange"></ui-breadcrumb>
          </div>
          <div class="search">
            <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="ml-lg fl">
              <Select
                class="w200"
                v-model="searchData.sbdwlx"
                :clearable="true"
                :placeholder="`请选择${global.filedEnum.sbdwlx}`"
              >
                <Option
                  v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
                  :key="'sbdwlx' + bdindex"
                  :value="sbdwlxItem.dataKey"
                  >{{ sbdwlxItem.dataValue }}
                </Option>
              </Select>
            </ui-label>
            <Button type="primary" class="ml-lg" @click="startSearch">查询</Button>
            <Button class="ml-sm" @click="resetSearchDataMx1">重置</Button>
          </div>
        </div>
      </template>
      <!-- 表格操作 -->
      <template #orgName="{ row }">
        <span v-if="orgName == row.orgName">{{ row.orgName }}</span>
        <span class="font-table-action pointer" v-else @click="change(row)">{{ row.orgName }}</span>
      </template>
    </TableList>
  </div>
</template>
<script>
import user from '@/config/api/user';
import inspectionrecord from '@/config/api/inspectionrecord';
import { mapGetters } from 'vuex';
export default {
  components: {
    TableList: require('../components/tableList.vue').default,
    UiBreadcrumb: require('../components/ui-breadcrumb').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      breadcrumbData: [],
      searchData: {},
      minusHeight: 260, // 430
      columns: [
        {
          title: '组织机构',
          slot: 'orgName',
          className: 'header-table',
          align: 'center',
          width: 120,
        },
        {
          title: '视频监控数量总数',
          key: 'evaluatingVideoTotal',
          className: 'header-table',
          align: 'center',
          width: 130,
        },
        {
          title: '重点设备检测数量',
          key: 'focusVideoTotal',
          className: 'header-table',
          align: 'center',
          width: 130,
        },
        {
          title: '重点实时视频异常数',
          key: 'videoPlayingAbnormalTotal',
          className: 'header-table',
          align: 'center',
          width: 140,
        },
        {
          title: '重点历史视频异常数',
          key: 'videoHistoryAbnormalTotal',
          className: 'header-table',
          align: 'center',
          width: 140,
        },
        {
          title: '重点时钟异常数',
          key: 'videoClockAbnormalTotal',
          className: 'header-table',
          align: 'center',
          width: 120,
        },
        {
          title: '重点OSD字幕异常数',
          key: 'videoOsdAbnormalTotal',
          className: 'header-table',
          align: 'center',
          width: 150,
        },
        {
          title: '普通实时视频异常数',
          key: '1',
          className: 'header-table',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'videoGeneralPlayingTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'videoGeneralPlayingAbnormalTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通历史视频异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'videoGeneralHistoryTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'videoGeneralHistoryAbnormalTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通时钟异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'videoGeneralClockTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'videoGeneralClockAbnormalTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
          ],
        },
        {
          title: '普通OSD字幕异常数',
          className: 'header-table',
          key: '1',
          align: 'center',
          children: [
            {
              title: '检测数量',
              key: 'videoGeneralOsdTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
            {
              title: '异常数量',
              key: 'videoGeneralOsdAbnormalTotal',
              align: 'center',
              width: 160,
              className: 'header-table',
            },
          ],
        },
      ],
      params: {},
      infoObj: {},
      orgCode: '',
      orgName: '',
      loadDataList: () => {
        return this.$http
          .post(
            inspectionrecord.queryEvaluatingVideoGeneralSituationList,
            Object.assign({ ...this.params }, this.searchData),
          )
          .then((res) => {
            return res.data;
          });
      },
      dictData: {},
    };
  },
  created() {},
  mounted() {
    this.info();
    this.getDictData();
  },
  methods: {
    async info() {
      if (!this.taskObj.batchIds.length) {
        return;
      }
      // let { orgCode } = this.taskObj.orgCodeList.find((item) => !item.disabled) || {}
      // this.orgCode = orgCode || ''
      // if (!orgCode) return this.$Message.error('您没有此行政区划权限')
      // let orgName = this.taskObj.regionName
      const { batchIds, regionCode, regionName } = this.taskObj;
      this.orgName = regionName;
      const arr = this.breadcrumbData.filter((item) => {
        return item.add == regionName;
      });
      if (arr.length == 0) {
        this.breadcrumbData.push({ id: regionCode, add: regionName });
      }
      this.params = {
        batchIds,
        regionCode,
      };
      await this.$refs.infoList.info(true);
    },
    resetSearchDataMx1() {
      this.searchData = {};
      this.$refs.infoList.info(true);
    },
    startSearch() {
      this.$refs.infoList.info(true);
    },
    handleChange({ id, add }) {
      this.orgCode = id;
      this.orgName = add;
      if (add === this.taskObj.regionName) {
        this.params = {
          ...this.params,
          regionCode: this.orgCode,
          orgCode: undefined,
        };
      } else {
        this.params = {
          ...this.params,
          regionCode: undefined,
          orgCode: this.orgCode,
        };
      }
      this.$refs.infoList.info(true);
    },
    change(row) {
      let id = row.orgCode;
      let add = row.orgName;
      this.orgName = row.orgName;
      var arr = this.breadcrumbData.filter((item) => {
        return item.add == add;
      });
      if (arr.length == 0) {
        this.breadcrumbData.push({ id: id, add: add });
      }
      this.orgCode = row.orgCode;
      this.params = {
        ...this.params,
        regionCode: undefined,
        orgCode: row.orgCode,
      };
      this.$refs.infoList.info(true);
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
  computed: {
    ...mapGetters({
      orgTreeData: 'common/getOrganizationList',
    }),
  },
  watch: {
    '$parent.taskObj': {
      deep: true,
      handler: function () {
        this.info();
        this.getDictData();
      },
    },
  },
  filter: {},
};
</script>
<style lang="less" scoped>
.base-search {
  width: 100%;
  height: 50px;
  .w200 {
    width: 200px;
  }
}
/deep/ .table-list > .left-div > .ui-table .ivu-table-tbody td {
  background: var(--bg-content);
}
.table-list {
  /deep/ .ui-table {
    border-top: 1px solid var(--border-table);
    th .ivu-table-cell {
      color: #8797ac;
    }
  }
  /deep/ .ivu-table-body > table {
    border-right: 1px solid var(--border-table);
  }
  /deep/ .ivu-table-header {
    border-right: 1px solid var(--border-table);
  }
  /deep/ .header-table {
    box-shadow: none;
    // box-shadow: inset 1px -1px 0 0 #0d477d;
    border-left: 1px solid var(--border-table);
    border-bottom: 1px solid var(--border-table);
  }
  @{_deep} .ivu-table-tip {
    overflow-x: auto;
  }
}
.search-container {
  display: flex;
  justify-content: space-between;
  margin: 10px 0 10px 0;
  .jump {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.w200 {
  width: 200px;
}
</style>
