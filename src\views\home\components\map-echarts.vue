<template>
  <div class="map-echarts" :class="getFullscreen ? 'full-screen-container' : ''">
    <div id="map-chart" style="width: 100%; height: 100%" ref="chartsRef"></div>
  </div>
</template>
<script>
import { options } from '@/views/home/<USER>/old.geo.config.js';
import home from '@/config/api/home';
import mapMixin from '@/views/home/<USER>/map.Mixin.js';
export default {
  mixins: [mapMixin],
  props: {
    queryAccessData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      // 供mixin取值调用，每个地图动态配置字段
      mapStaticKey: 'civilCode',
    };
  },
  methods: {
    /**
     * 供mixin调用，必有
     * 地图toolTip的统计值
     */
    async getIndexMapStatistics() {
      try {
        let { data } = await this.$http.get(home.oldGetIndexMapStatistics);
        let list = data.data;
        return list;
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 供mixin调用，必有
     * 初始化地图配置
     */
    async handleMapFunction() {
      return await options(this.geoCoordMapData, this.queryAccessData);
    },
    /**
     * 供mixin调用，必有
     * 点击地图调用配置
     */
    async handleClickMapFunction() {
      return await options(this.geoCoordMapData);
    },
  },
};
</script>
<style lang="less" scoped>
.map-echarts {
  position: absolute;
  left: 50%;
  top: 13%;
  transform: translateX(-50%);
  width: 60%;
  height: 60%;
}
.full-screen-container {
  top: 15%;
  width: 54%;
}
</style>
