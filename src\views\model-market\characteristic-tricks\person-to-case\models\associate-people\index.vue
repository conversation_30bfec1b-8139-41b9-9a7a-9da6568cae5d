<template>
  <ui-modal
    width="90%"
    :value="value"
    title="寻找关联人员"
    ok-text="添加关联人"
    @onOk="onOk"
    @onCancel="onCancel"
  >
    <Row class="associate-people-content" :gutter="10">
      <Col :span="16">
        <div class="numberCube-content">
          <NumberCube
            v-if="routeInfo"
            mode="component"
            class="numberCube-page"
            :routeInfo="routeInfo"
            @getGraphNodesAndEdgesData="getGraphNodesAndEdgesData"
          />
        </div>
      </Col>
      <Col :span="8">
        <Table
          ref="table"
          :height="height"
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          @on-selection-change="onSelectionChange"
        >
          <template #loading>
            <ui-loading></ui-loading>
          </template>
        </Table>
      </Col>
    </Row>
  </ui-modal>
</template>
<script>
import { entitySearch } from "@/api/number-cube";
export default {
  components: {
    UiTablePage: require("@/components/ui-table-page").default,
    NumberCube: () => import("@/views/number-cube/info-g6-model.vue"),
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    checkAssociateItem: {
      type: Object,
      default: () => ({}),
    },
    limitJudgment: {
      type: Function,
    },
    configValue: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      tableLoading: false,
      height: 100,
      tableData: [],
      columns: [
        {
          title: "选择",
          width: 65,
          type: "selection",
          key: "index",
          renderHeader: (h) => h("span", {}, ["选择"]),
        },
        { title: "序号", width: 70, type: "index", key: "index" },
        {
          title: "照片",
          key: "propertyIcon",
          render(h, { row }) {
            return h("ui-image", {
              props: {
                src: row.propertyIcon,
              },
            });
          },
        },
        {
          title: "姓名",
          key: "xm",
        },
        {
          title: "关系种类",
          key: "relationTypes",
          width: 120,
        },
        {
          title: "关系总次数",
          key: "relationship",
          width: 120,
        },
      ],
      selection: [],
      routeInfo: null,
      index: 0,
      currentArchiveNo: null,
    };
  },
  created() {
    this.tableLoading = true;
    this.setRouteInfo();
  },
  mounted() {},
  methods: {
    async setRouteInfo() {
      const { entityId, archiveNo, graphBusinessType } =
        this.checkAssociateItem;
      let ids = entityId;
      let originalVertexJson = graphBusinessType;
      const { graphInfo, searchEntities } = this.configValue;
      const names = searchEntities || [];
      const graphInstanceId = graphInfo?.instanceId;
      this.currentArchiveNo = archiveNo;
      if (!ids) {
        const {
          data: { entities },
        } = await entitySearch({
          pageNumber: 1,
          pageSize: 1,
          name: names[this.index],
          searchValue: archiveNo,
          graphInstanceId,
        });
        this.index++;
        if (!entities || entities.length === 0) {
          if (this.index === names.length) {
            this.$Message.warning("未找到目标实体");
            this.tableLoading = false;
          } else this.setRouteInfo();
          return;
        }

        const { qsdi_fusion_instance_id, qsdi_fusion_original_id, id } =
          entities[0];
        ids = id;
        if (qsdi_fusion_instance_id && qsdi_fusion_original_id) {
          originalVertexJson = "01";
          this.checkAssociateItem.graphBusinessType = originalVertexJson;
        }
        // 下次就需要查询id
        this.checkAssociateItem.entityId = ids;
      }
      this.routeInfo = {
        ids: ids,
        maxDepth: 1,
        type: "detail",
        level: 1,
        graphInstanceId: graphInstanceId,
        graphBusinessType: originalVertexJson,
      };
    },
    getTableHeight() {
      this.height = document.querySelector(".numberCube-content").clientHeight;
    },
    getGraphNodesAndEdgesData(data) {
      this.getData(data);
      this.tableLoading = false;
    },
    getData({ edges = [], nodes = [] }) {
      const labels = this.configValue?.personEntities || [];
      const relationMap = {};
      for (const { source, target, label } of edges) {
        [target, source].forEach((item) => {
          relationMap[item] = [...(relationMap[item] || []), label];
        });
      }
      const data = nodes
        .filter(
          (node) =>
            labels.includes(node.ext.label) &&
            ["gmsfhm", "archiveNo", "sfzhm"].reduce(
              (curl, el) => curl || node.ext?.properties?.[el],
              ""
            ) !== this.currentArchiveNo
        )
        .map((node) => {
          const relationEdges = relationMap[node.id];
          const relationTypeEdges = new Set([...relationEdges]);
          return {
            id: node.id,
            ...node.ext.properties,
            xm: node.label,
            propertyIcon: node.ext.propertyIcon,
            // 关系种类
            relationTypes: [...relationTypeEdges].length,
            // 关系总次数
            relationship: relationEdges.length,
          };
        });
      this.getTableHeight();
      this.tableData = data;
    },
    onSelectionChange(selection) {
      this.selection = selection;
    },
    onOk() {
      const leg = this.selection.length;
      if (leg === 0) {
        this.$Message.warning("请选择一个实体");
        return;
      }
      const max = this.limitJudgment();
      if (max - leg < 0) {
        this.$Message.warning(`当前最多选择${max}个人员`);
        return;
      }

      this.$emit(
        "onSelectAssociatePeople",
        this.selection.map(({ propertyIcon, ...item }) => ({
          ...item,
          archiveNo: ["gmsfhm", "archiveNo", "sfzhm"].reduce(
            (curl, el) => curl || item[el],
            ""
          ),
          photos: [
            {
              photoUrl: propertyIcon,
            },
          ],
        }))
      );
      this.$emit("input", false);
    },
    onCancel() {
      this.$emit("input", false);
    },
  },
};
</script>
<style lang="less" scoped>
.numberCube-content {
  height: 60vh;

  .numberCube-page {
    height: 100%;
  }
}
</style>
