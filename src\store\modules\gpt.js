/*
 * @Date: 2024-09-12 15:04:48
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-12 18:11:05
 * @FilePath: \icbd-view\src\store\modules\gpt.js
 */
/**
 * 大模型
 */
export default {
  namespaced: true,
  state: {
    // 跳转轨迹页面参数
    trajectoryParams: {
      type: 1, // 1: 人员轨迹， 2：车辆轨迹
      info: {}, // 传参
    },
    // 跳转数智立方参数
    numCodeParams: {
      type: 3, // 3: 照片查询 4: 身份证查询
      info: {},
    }
  },
  getters: {
    getTrajectoryParams (state) {
      return state.trajectoryParams;
    },
    getNumCodeParams (state) {
      return state.numCodeParams;
    }
  },
  mutations: {
    setTrajectoryParams (state, info) {
      state.trajectoryParams = info;
    },
    setNumCodeParams (state, info) {
      state.numCodeParams = info;
    },
  },
  actions: {},
};
