<template>
  <div>
    <ui-modal ref="modal" title="车辆结构化">
      <p class="p">请选择车辆结构化算法：</p>
      <CheckboxGroup v-model="selectFactrys">
        <Row>
          <Col span="12" v-for="(item, index) in factryList" :key="index">
            <Checkbox :label="item.label"></Checkbox>
          </Col>
        </Row>
      </CheckboxGroup>

      <template slot="footer">
        <Button @click="submit" type="primary">保&nbsp;存</Button>
      </template>
    </ui-modal>

    <!-- <ui-modal> -->
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import api from '@/config/api/car-threm.js';
export default {
  name: 'carConfig',
  props: {},
  data() {
    return {
      itemList: [], // 页面所有块集合
      currentItem: null, // 当前操作的块
      factryList: [], // 厂商列表
      // selectFactryList: [], // 已选中厂商列表
      selectFactrys: [], // 厂商v-model
      curItem: 1,
    };
  },
  async created() {
    await this.queryCarList();
    if (this.algorithmList.length == 0) await this.getAlldicData();
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),

    showModal() {
      if (this.itemList.length > 0) {
        this.currentItem = this.itemList.filter((item) => {
          return item.componentName == '车辆结构化';
        })[0];
      } else {
        console.log('字典服务异常！！！');
      }
      this.queryFactryList();
      this.$refs.modal.modalShow = true;
      this.querySelectInfo();
    },

    // 查询已选中的数据
    querySelectInfo() {
      this.selectFactrys = [];
      // this.$http.get(api.queryCarJgh+ this.currentItem.id).then(res => {
      this.$http
        .get(api.queryCarJgh + '31')
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data;
            list.forEach((item) => {
              var label = this.getAlgorithmLabel(item.algorithmVendorType);
              this.selectFactrys.push(label);
            });
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    async queryCarList() {
      await this.$http
        .get(api.queryCarList + '3')
        .then((res) => {
          if (res.data.code == 200) {
            this.itemList = res.data.data;
            this.currentItem = res.data.data;
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    queryFactryList() {
      this.factryList = [];

      // this.$http.get(api.queryCsList+ this.currentItem.componentParam).then(res => {
      this.$http
        .get(api.queryCsList + '1')
        .then((res) => {
          if (res.data.code == 200) {
            var list = res.data.data;

            list.forEach((item) => {
              var obj = {
                algorithmType: item.algorithmType,
                algorithmVendorType: item.algorithmVendorType,
                label: this.getAlgorithmLabel(item.algorithmVendorType),
              };

              this.factryList.push(obj);
            });
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },

    getAlgorithmLabel(val) {
      var arr = this.algorithmList.filter((item) => {
        return val == item.dataKey;
      });
      return arr.length > 0 ? arr[0].dataValue : '--';
    },

    submit() {
      var list = [];

      if (this.selectFactrys.length == 0) {
        this.$Message.warning('至少选择一条');
        return;
      }

      this.selectFactrys.forEach((item) => {
        var arr = this.factryList.filter((ite) => {
          return ite.label == item;
        });
        if (arr.length > 0) {
          var obj = arr[0];
          obj.topicComponentId = 31;
          // obj.topicComponentId = this.currentItem.id;

          list.push(obj);
        }
      });

      this.$http
        .post(api.addCarJgh, list)
        .then((res) => {
          if (res.data.code == 200) {
            this.$refs.modal.modalShow = false;
            this.selectFactrys = [];
            console.log(res);
            this.$Message.success('配置成功');
          } else {
            console.log(res.data.msg);
          }
        })
        .finally(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.ivu-checkbox-group-item {
  padding: 6px 0;
}

.p {
  margin-top: 10px;
  color: #fff;
}
@{_deep} .ivu-modal-body {
  padding: 20px 51px 37px;
}
</style>
