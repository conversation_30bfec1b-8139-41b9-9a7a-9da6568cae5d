<template>
  <!-- 治理达标率 -->
  <div class="container" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-zhilidabiaoshuai">
      安全监测
      <template #filter>
        <Button type="text" @click="more">更多 <i class="icon-font icon-youjiantou1 f-12"></i></Button>
      </template>
    </HomeTitle>

    <div class="list-wrapper" v-ui-loading="{ loading: loading, tableData: data }">
      <div v-for="(item, index) in data" :key="index" class="mb-sm f-14 base-text-color ellipsis">
        <span class="icon-font f-14 icon-gaojingdeng icon-color"></span>
        <span class="ml-xs" v-html="item.value" :title="item.origin"></span>
      </div>
    </div>
    <ui-modal v-model="showMore" title="安全监测" :styles="styles" footer-hide>
      <div class="over-flow mb-lg">
        <Button type="primary" class="fr" @click="exportExcel">
          <i class="icon-font icon-daochu delete-icon"></i>
          <span class="inline vt-middle ml-xs">导出</span>
        </Button>
      </div>
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="tableLoading"
      >
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.container {
  position: absolute;
  top: 33.5%;
  right: 10px;
  width: 400px;
  height: 31%;
  background: rgba(0, 104, 183, 0.13);
  .list-wrapper {
    height: calc(100% - 38px);
    padding: 10px;
    overflow-y: auto;
    width: 100%;
  }
}
.icon-color {
  color: #fc6521;
}

.link {
  text-decoration: auto;
}
.full-screen-container {
  top: 36%;
  height: 31.5%;
}
.ui-table {
  min-height: 500px;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'standard-govern',
  data() {
    return {
      data: [],
      list: [],
      loading: false,
      showMore: false,
      tableLoading: false,
      styles: {
        width: '6rem',
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      tableColumns: [
        { title: '序号', width: 50, type: 'index', align: 'left' },
        { title: '漏洞主机', key: 'host', align: 'left' },
        { title: '漏洞名称', key: 'vuln_name', align: 'left' },
        { title: '严重程度', width: 80, key: 'vuln_level', align: 'left' },
        { title: '漏洞地址', key: 'vuln_url', align: 'left' },
        { title: '漏洞描述', key: 'description', align: 'left' },
        { title: '修复建议', key: 'advice', align: 'left' },
        { title: '探测任务', width: 80, key: 'task_name', align: 'left' },
        { title: '发现时间', width: 150, key: 'vuln_time', align: 'left' },
      ],
      tableData: [],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      userInfo: 'user/getUserInfo',
    }),
  },
  async created() {
    await this.initHome();
    this.replaceLink();
  },
  methods: {
    async initList(params) {
      try {
        let {
          data: { data },
        } = await this.$http.post(home.getSafetyInspection, params);
        return data;
      } catch (e) {
        console.log(e);
      }
    },
    async initHome() {
      try {
        let params = {
          username: this.userInfo.username,
        };
        this.loading = true;
        const data = await this.initList(Object.assign(params, this.searchData));
        this.list = data.resultList ? data.resultList : [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async initMore() {
      let params = {
        isHome: false,
        username: this.userInfo.username,
      };

      this.tableLoading = true;
      const data = await this.initList(Object.assign(params, this.searchData));
      this.tableData = data.dataList;
      this.pageData.totalCount = data.count;
      this.tableLoading = false;
    },
    replaceLink() {
      let reg =
        /(\d{1}|[1-9]{1}\d{1}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1}|[1-9]{1}\d{1}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1}|[1-9]{1}\d{1}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1}|[1-9]{1}\d{1}|1\d\d|2[0-4]\d|25[0-5])+(:\d+)?/gm;
      let list = [];
      this.list.map((item) => {
        let match = item.match(reg);
        list.push({
          origin: item,
          value:
            item &&
            item.replaceAll(
              reg,
              `<a href="http://${match && match[0]}" target="_blank" style="text-decoration: underline;">${
                match && match[0]
              }</a>`,
            ),
        });
      });
      this.data = list;
    },
    more() {
      this.initMore();
      this.showMore = true;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.initMore();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.initMore();
    },
    async exportExcel() {
      try {
        let res = await this.$http.get(home.exportSafetyInspection, {
          params: {
            username: this.userInfo.username,
          },
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      }
    },
  },
  components: {
    HomeTitle: require('./home-title').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
