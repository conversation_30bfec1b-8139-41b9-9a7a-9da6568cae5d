<template>
  <ui-modal v-model="visible" title="轮巡设置" :r-width="840" okText="启动" @onOk="comfirmHandle">
    <div class="top">
      <div class="item">
        <div class="label">轮巡窗口选择:</div>
        <div class="value">
          <Checkbox v-model="allChecked" @on-change="selectAll" style="margin: 0px;">全部可用窗口（{{options.winCount}}）</Checkbox>
        </div>
      </div>
      <div class="item">
        <div class="label">窗口设置:</div>
        <div class="value">
          <Select v-model="options.layout" @on-change="layoutChange" :style="{width: '100px'}">
            <Option v-for="(item, $index) in layoutOptions" :key="$index" :value="item.value">{{item.label}}</Option>
          </Select>
        </div>
      </div>
      <div class="item">
        <div class="label">间隔时间:</div>
        <div class="value">
          <Input v-model="options.inspectTime">
            <template #suffix>秒</template>
          </Input>
        </div>
      </div>
    </div>
    <div class="bottom" :data-type="options.layout">
      <div v-for="(item, index) in options.winCount" :key="item">
        <Checkbox v-if="!checkedIndexs.includes(index)" v-model="options.checkedLayout[index]"></Checkbox>
      </div>
    </div>
  </ui-modal>
</template>
<script>

export default {
  data() {
    return {
      visible: false,
      allChecked: false,
      options: {
        layout: "2*2",
        winCount: 4,
        inspectTime: 10,
        checkedLayout: [false, false, false, false]
      },
      layoutOptions: [{
        label: "单分屏",
        value: "1*1",
        count: 1
      },{
        label: "三分屏",
        value: "1A2",
        count: 3
      },{
        label: "三分屏",
        value: "2A1",
        count: 3
      },{
        label: "四分屏",
        value: "2*2",
        count: 4
      },{
        label: "六分屏",
        value: "1A5",
        count: 6
      },{
        label: "八分屏",
        value: "1A7",
        count: 8
      },{
        label: "九分屏",
        value: "3*3",
        count: 9
      },{
        label: "十六分屏",
        value: "4*4",
        count: 16
      }]
    }
  },
  props: {
    checkedIndexs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  methods: {
    // 初始化
    show(flag) {
      this.visible = flag ? true : false
      this.options = {
        layout: "2*2",
        winCount: 4,
        inspectTime: 10,
        checkedLayout: [false, false, false, false]
      }
      this.allChecked = false
    },
    // 确认提交
    comfirmHandle() {
      if (!this.options.checkedLayout.some(v => v)) {
        this.$Message.warning("请选择轮巡分屏")
        return
      }
      let checkedIndexs = []
      this.options.checkedLayout.forEach((item, index) => {
        if (item) checkedIndexs.push(index)
      })
      this.$emit("inspectStart", {checkedIndexs, ...this.options})
    },
    selectAll(value) {
      if (value) {
        this.options.checkedLayout = this.options.checkedLayout.map((v, index) => {
          if (!this.checkedIndexs.includes(index)) return true
        })
      } else {
        this.options.checkedLayout = this.options.checkedLayout.map((v, index) => {
          if (!this.checkedIndexs.includes(index)) return false
        })
      }
    },
    layoutChange(value) {
      let item = this.layoutOptions.find(v => v.value == value)
      this.options.winCount = item.count
      this.options.checkedLayout = Array.from({length: this.options.winCount}, () => false)
      this.allChecked = false
    }
  }
}
</script>
<style lang="less" scoped>
.top {
  display: flex;
//   justify-content: space-between;
  .item {
    margin-right: 30px;
    display: flex;
    align-items: center;
    .label {
      margin-right: 5px;
      color: rgba(0, 0, 0, 0.4);
    }
  }
}
.bottom {
  height: 500px;
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  & > div {
    background-color: #EBEDF1;
    background-image: url('~@/assets/img/player/bg-img.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 20%;
    .ivu-checkbox-wrapper {
      margin-left: 5px;
      margin-top: 2px;
    }
  }
  &[data-type = '1*1'] > div{
    height: 100%;
    width: 100%;
  }
  &[data-type = '1A2'] > div{
    height: calc(~'50% - 5px');
    width: calc(~'50% - 5px');
    &:nth-child(3n+1) {
      width: 100%;
      margin-bottom: 10px;
    }
    &:nth-child(3n+2) {
      margin-right: 10px;
    }
  }
  &[data-type = '2A1'] {
    display: block;
    & > div{
      height: calc(~'50% - 5px');
      width: calc(~'50% - 5px');
      float: left;
      &:nth-child(3n+1) {
        height: 100%;
        margin-right: 10px;
      }
      &:nth-child(3n+2) {
        margin-bottom: 10px;
      }
    }
  }
  &[data-type = '2*2'] > div{
    height: calc(~'50% - 5px');
    width: calc(~'50% - 5px');
    &:nth-child(2n+1) {
      margin-right: 10px;
    }
    &:nth-child(-n+2) {
      margin-bottom: 10px;
    }
  }
  &[data-type = '1A5'] {
    display: block;
    & > div{
      height: calc(~'33.3% - 7px');
      width: calc(~'33.3% - 7px');
      float: left;
      &:first-child {
        height: calc(~'66.6% - 5px');
        width: calc(~'66.6% - 5px');
        margin-right: 10px;
      }
      &:nth-child(4),&:nth-child(5) {
        margin-right: 10px;
      }
      &:nth-child(-n+3) {
        margin-bottom: 10px;
      }
    }
  }
  &[data-type = '1A7'] {
    display: block;
    & > div{
      height: calc(~'25% - 7.5px');
      width: calc(~'25% - 7.5px');
      float: left;
      &:first-child {
        height: calc(~'75% - 5px');
        width: calc(~'75% - 5px');
        margin-right: 10px;
      }
      &:nth-child(5),&:nth-child(6),&:nth-child(7) {
        margin-right: 10px;
      }
      &:nth-child(-n+4) {
        margin-bottom: 10px;
      }
    }
  }
  &[data-type = '3*3'] > div{
    height: calc(~'33.3% - 7px');
    width: calc(~'33.3% - 7px');
    &:nth-child(3n+1),&:nth-child(3n+2) {
      margin-right: 10px;
    }
    &:nth-child(-n+6) {
      margin-bottom: 10px;
    }
  }
  &[data-type = '4*4'] > div{
    height: calc(~'25% - 7.5px');
    width: calc(~'25% - 7.5px');
    &:nth-child(4n+1) {
      margin-right: 10px;
    }
    &:nth-child(4n+2) {
      margin-right: 10px;
    }
    &:nth-child(4n+3) {
      margin-right: 10px;
    }
    &:nth-child(-n+12) {
      margin-bottom: 10px;
    }
  }
}
/deep/ .ivu-input-suffix {
  display: flex;
  align-items: center;
}
</style>
