<!--
 * @Date: 2025-01-15 10:53:49
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-05 14:54:06
 * @FilePath: \icbd-view\src\views\juvenile\components\collect\frequent-alarm.vue
-->
<template>
  <div class="frequent-box">
    <div class="head-img-box">
      <div class="score-box">{{ data.total || 0 }}</div>
      <div class="img-box">
        <img :src="data.traitImg" />
      </div>
    </div>
    <div class="text-box">
      <div class="sub-mian-text" :title="data.communityName">
        {{ data.communityName || "--" }}
      </div>
      <div class="sub-sub-text">
        近一周出现
        <span class="primary">{{ data.appearNumber || 0 }}</span>
        天
      </div>
    </div>
  </div>
  <!-- <div style="display: flex; gap: 10px">

  </div> -->
</template>

<script>
export default {
  name: "FrequentAlarm",
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.primary {
  color: #2c86f8;
}
.frequent-box {
  width: 126px;
  height: 100%;
  max-height: 150px;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  &::after {
    content: "";
    width: 100%;
    height: 90px;
    display: block;
    position: absolute;
    top: 25%;
    z-index: -1;
    background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
  }
  .head-img-box {
    width: 80px;
    height: 80px;
    display: flex;
    margin: 0 auto;
    justify-content: center;
    align-items: center;
    position: relative;
    .score-box {
      position: absolute;
      top: 0;
      left: 0;
      width: 30px;
      height: 30px;
      background: linear-gradient(237deg, #ec9240 0%, #f7b93d 100%);
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      color: white;
      font-weight: bold;
    }

    .img-box {
      margin: 0 auto;
      width: 100%;
      height: 100%;
      overflow: hidden;
      border: 1px solid #45e8ff;

      border-radius: 50%;
      /deep/ img {
        width: 100%;
        max-height: 100%;
      }
    }
  }
  .text-box {
    width: 100%;
    text-align: center;
    text-wrap: nowrap;
    margin-top: 5px;
    display: flex;
    justify-content: space-around;
    flex-direction: column;
    flex: 1;
    .sub-mian-text {
      font-weight: bold;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.9);
      width: 100%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
    .sub-sub-text {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
    }
  }
}
</style>
