<template>
    <div class="i-layout-header-trigger i-layout-header-trigger-min">
        <Dropdown class="i-layout-header-user" transfer>
            <ui-icon type="shezhi" :size="20" :color="'#fff'"></ui-icon>
            <DropdownMenu slot="list">
                <DropdownItem>
                    <span @click="handleSetting">个性设置</span>
                </DropdownItem>
            </DropdownMenu>
        </Dropdown>
    </div>
</template>
<script>
export default {
    name: `iHeaderUser`,
    components: {
    },
    data() {
        return {
            
        };
    },
    computed: {
        
    },
    mounted() {
    },
    methods: {
        //设置
        handleSetting() {
            this.$emit('setting')
        },
    },
};
</script>
<style lang="less" scoped>
.i-layout-header-trigger {
    display: flex;
    margin-left: 15px;
    height: 40px;
}
</style>
  