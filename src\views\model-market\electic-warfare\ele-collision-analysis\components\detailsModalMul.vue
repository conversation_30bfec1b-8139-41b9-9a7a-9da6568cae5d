<!--
    * @FileDescription: 人体详情
    * @Author: H
    * @Date: 2023/5/16
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="dom-wrapper">
        <div class="dom" @click="($event) => $event.stopPropagation()"> 
            <header>
                <span>抓拍列表</span>
                <Icon type="md-close" size="14" @click.native="() => $emit('close', $event)"/>
            </header>
            <section class="dom-content" v-scroll>
                <div class="capture-list capture-list-two">
                    <div class="capture-one"
                        v-for="(data, index) in listData"
                        :key="index">
                        <p class="capture-title">
                            <span class="area">区域{{index+1}}&nbsp;&nbsp;</span>
                            <span class="count">(共{{data.data.total}}条)</span>
                        </p>
                        <div class="info-box">
                            <div class="list-card box-1" v-for="(item, index) in data.data.entities" :key="index" :class="{ checked: item.isChecked }">
                                <p class="img-content">
                                    <ui-image :src="item.sceneImg" v-viewer/>
                                    <b class="shade vehicle">
                                        <ui-plate-number :plateNo="item.plateNo" :color="item.plateColor" size='mini'></ui-plate-number>
                                    </b>
                                </p>
                                <div class="bottom-info">
                                    <time>
                                        <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                            <i class="iconfont icon-time"></i>
                                        </Tooltip>
                                        {{ item.absTime }}
                                    </time>
                                    <p>
                                        <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                            <i class="iconfont icon-location"></i>
                                        </Tooltip>
                                        <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
                                    </p>
                                </div>
                            </div>
                            <div class="empty-card-1" v-for="(item, index) of 5 - (data.data.entities.length % 5)" :key="index + 'demo'"></div>
                        </div>
                    </div>
                </div>
            </section>
            <footer></footer>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    // mixins: [commonMixins, cutMixins], //全局的mixin
    components:{   
    },
    props: {
        listData: {
            type: Array,
            default: () => []
        },
    },
    data () {
        return {
            loading: false
        }
    },
    watch:{
            
    },
    computed:{
    },
    async created() {  
    },
    mounted(){
    },
    methods: {
        pageChange() {

        },
        pageSizeChange() {

        },
        close() {
            this.$emit('close')
        }
    }
}
</script>

<style lang='less' scoped>
@import './style/modal';
.dom-content{
    display: flex;
    flex-direction: column;
    .info-box{
        flex: 1;
        overflow-y: auto;
        flex-wrap: wrap;
    }
    .box-1{
        width: 19%;
    }
    .capture-title {
        height: 30px;
        line-height: 30px;
        .area {
          font-size: 14px;
          font-weight: bold;
        }
      }
}
</style>
