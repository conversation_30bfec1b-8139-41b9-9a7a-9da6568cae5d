import commonToolTipConfig from '@/views/viewassets/assetstatistics/utils/config-toolTip.js';
let axisLabelFormatter = (params) => {
  if (params?.length > 4) {
    return params.substr(0, 4) + '...';
  } else {
    return params;
  }
};
export default function assetstatisticsMultipleBar(opts, config = {}) {
  /**
   * color
   * titleText
   * legendData
   * xAxisData
   * seriesData
   * toolTipDom - 自定义的toolTip组件
   * slotName: 组件插槽的名称
   * render： 组件插槽的vnode
   */
  let options = {
    // color: opts?.color ?? ['#37C6D4', '#6B4CE4','pink'],
    title: {
      text: opts?.titleText,
      top: '3%',
      left: '1%',
      textStyle: {
        fontSize: 12,
        color: $var('--color-base-text'),
        fontWeight: '500',
      },
    },
    tooltip: {
      axisPointer: {
        // 坐标轴指示器，坐标轴触发有效
        type: 'line', // 默认为直线，可选为：'line' | 'shadow' | 'cross' , shadow表示阴影
        lineStyle: {
          type: 'dashed',
          color: $var('--color-axis-pointer-line'),
        },
      },
      confine: true, //是否将 tooltip 框限制在图表的区域内。
      trigger: 'axis',
      // extraCssText: 'background: rgba(17, 71, 129, .74);border: 1px solid #2A8DEA', //添加阴影
      formatter: (params) => {
        let toolTipItem = {
          name: params?.[0]?.name ?? '',
          list: params.map((item) => {
            let one = {
              label: item.seriesName,
              color: item.color,
              num: item.value,
            };
            item?.data?.slotName ? (one.slotName = item.data.slotName) : null;
            item?.data?.slotRender ? (one.slotRender = item.data.slotRender) : null;
            return one;
          }),
        };
        if (typeof params?.[0]?.data === 'object' && 'outerSlot' in params[0].data) {
          toolTipItem.outerSlot = params?.[0]?.data.outerSlot;
        }
        // toolTipItem: {
        //   name: '测试',
        //   list: [
        //     {
        //       label: 'label1',
        //       color: 'red',
        //       num: '111',
        //       slotName: 'slotA',
        //       slotRender: h => [h('span', {}, 'sdlkad')]
        //     }
        //   ],
        // }
        return commonToolTipConfig(toolTipItem);
      },
    },
    animation: false,
    label: {
      show: false,
    },
    grid: {
      top: '15%',
      left: '1%',
      bottom: 25,
      right: '3%',
      containLabel: true,
    },
    legend: {
      show: true,
      right: '1%',
      top: '3%',
      itemGap: 10,
      itemWidth: 6,
      itemHeight: 6,
      data: opts?.legendData ?? [],
      textStyle: {
        color: $var('--color-base-text'),
        width: 100,
        overflow: 'truncate',
      },
    },
    xAxis: [
      {
        type: 'category',
        data: opts?.xAxisData ?? [],
        axisTick: {
          alignWithLabel: true,
        },
        nameTextStyle: {
          color: $var('--color-axis-lable'),
        },
        axisLine: {
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
        axisLabel: {
          interval: 0,
          // rotate: '45',
          color: $var('--color-axis-lable'),
          margin: 20,
          formatter: opts.axisLabelFormatter || axisLabelFormatter,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          color: $var('--color-axis-lable'),
          formatter: '{value}',
        },
        splitLine: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: $var('--color-axis-line'),
          },
        },
      },
    ],
    series: opts.seriesData.map((item) => {
      return {
        type: 'bar',
        barWidth: 8,
        //barGap: '-100%', // Make series be overlap
        // 可以直接盖掉
        ...item,
      };
    }),
  };
  // 有传入覆盖掉，否则用默认
  Object.keys(options).forEach((key) => {
    key in config ? Object.assign(options[key], config[key]) : null;
  });
  return options;
}
