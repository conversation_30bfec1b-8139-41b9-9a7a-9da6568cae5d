<template>
  <choose-device
    ref="chooseDevice"
    class="chooseDevice"
    :search-conditions="{ orgCodeList: getDefaultOrgExpandedKeys }"
    :table-columns="columns"
    :load-data="tableData"
    :selected-list="ids"
    :check-device-flag="searchData.checkDeviceFlag"
    @getOrgCode="getOrgCode"
    @getDeviceIdList="getDeviceIdList"
    :filter-fun="filterFun"
    key="chooseDevice"
    :regionCode="regionCode"
    @getOrgList="getOrgList"
  >
    <template #search-header>
      <ui-label class="inline right-margin mb-lg" label="设备编码" :width="65">
        <Input
          v-model="searchData.deviceId"
          class="input-width"
          placeholder="请输入设备编码"
          @on-blur="changeBlur"
          clearable
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="设备名称" :width="65">
        <Input
          v-model="searchData.deviceName"
          class="input-width"
          placeholder="请输入设备名称"
          clearable
          @on-blur="changeBlur"
        ></Input>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbdwlx" :width="95">
        <Select class="input-width" v-model="searchData.sbdwlx" placeholder="请选择" clearable @on-change="changeBlur">
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbgnlx" :width="110">
        <Select class="input-width" v-model="searchData.sbgnlx" placeholder="请选择" clearable @on-change="changeBlur">
          <Option v-for="(item, index) in propertySearchLbgnlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="上报状态" :width="65">
        <Select
          @on-change="changeBlur"
          class="input-width"
          v-model="searchData.cascadeReportStatus"
          placeholder="请选择上报状态"
          clearable
        >
          <Option key="0" value="0">未上报</Option>
          <Option key="1" value="1">已上报</Option>
        </Select>
      </ui-label>
      <ui-label class="inline right-margin mb-lg" label="是否检测" :width="65">
        <Select
          @on-change="changeBlur('isCheck')"
          class="input-width"
          v-model="searchData.isCheck"
          placeholder="请选择"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择 </Option>
          <Option value="1">是 </Option>
          <Option value="0">否 </Option>
        </Select>
      </ui-label>
      <!-- 基础信息状态 -->
      <ui-label class="inline right-margin mb-lg" label="基础信息状态" :width="93">
        <Select
          :disabled="searchData.isCheck != '1'"
          @on-change="changeBlur('base')"
          class="width-md leftSelect"
          v-model="searchData.baseCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          @on-change="changeBlur"
          :disabled="searchData.baseCheckStatus != '1'"
          class="width-md rightSelect"
          v-model="searchData.baseErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.basic" :key="item + index">{{ item }}</Option>
        </Select>
      </ui-label>
      <!-- 视图数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视图数据状态" :width="93">
        <Select
          @on-change="changeBlur('view')"
          :disabled="searchData.isCheck != '1'"
          class="width-md leftSelect"
          v-model="searchData.viewCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          @on-change="changeBlur"
          :disabled="searchData.viewCheckStatus != '1'"
          class="width-md rightSelect"
          v-model="searchData.viewErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.view" :key="item + index">{{ item }}</Option>
        </Select>
      </ui-label>
      <!-- 视频流数据状态 -->
      <ui-label class="inline right-margin mb-lg" label="视频流数据状态" :width="108">
        <Select
          @on-change="changeBlur('video')"
          :disabled="searchData.isCheck != '1'"
          class="width-md leftSelect"
          v-model="searchData.videoCheckStatus"
          placeholder="请选择是否合格"
          clearable
          :max-tag-count="1"
        >
          <Option value="">请选择 </Option>
          <Option value="0">合格</Option>
          <Option value="1">不合格</Option>
        </Select>
        <span class="split">-</span>
        <Select
          :disabled="searchData.videoCheckStatus != '1'"
          @on-change="changeBlur"
          class="width-md rightSelect"
          v-model="searchData.videoErrorMessageList"
          multiple
          placeholder="请选择错误类别"
          clearable
          :max-tag-count="1"
        >
          <Option :value="item" v-for="(item, index) in errorMessageMap.video" :key="item + index">{{ item }}</Option>
        </Select>
      </ui-label>
      <br />
      <div class="data-list mr-lg">
        <span class="fl mt-md mr-sm">标签类型</span>
        <ui-select-tabs
          class="ui-select-tabs"
          :list="errorList"
          @selectInfo="selectInfo"
          ref="uiSelectTabs"
        ></ui-select-tabs>
      </div>
      <div class="inline mb-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="resetSearch">重置</Button>
      </div>
    </template>
    <!-- 经纬度保留8位小数-->
    <template #longitude="{ row }">
      <span>{{ row.longitude | filterLngLat }}</span>
    </template>
    <template #latitude="{ row }">
      <span>{{ row.latitude | filterLngLat }}</span>
    </template>
    <template #baseCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.baseCheckStatus == 1 ? '不合格' : row.baseCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
    <template #viewCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.viewCheckStatus == 1 ? '不合格' : row.viewCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
    <template #videoCheckStatus="{ row }">
      <div :class="row.rowClass">
        <span
          class="check-status"
          :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
        >
          {{ row.videoCheckStatus == 1 ? '不合格' : row.videoCheckStatus == 0 ? '合格' : '--' }}
        </span>
      </div>
    </template>
  </choose-device>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import viewassets from '@/config/api/viewassets';
import taganalysis from '@/config/api/taganalysis';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    regionCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableData: (parameter, getTotal) => {
        let params = getTotal
          ? parameter
          : Object.assign(this.searchData, parameter, {
              queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
            });
        return this.$http
          .post(equipmentassets.queryDeviceInfoPageList, {
            ...params,
            sourceId: this.indexModule === '4' ? '1' : '',
          })
          .then((res) => {
            return res.data;
          });
      },
      columns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
          width: 100,
        },
      ],
      searchData: {
        checkDeviceFlag: '0',
        sourceId: '',
      },
      ids: [], // 选择的设备ids
      totalCount: null, // 设备全选总数量
      videoCheck: {},
      errorList: [], // 标签类型
      errorMessageMap: {
        basic: [],
        view: [],
        video: [],
      },
    };
  },

  watch: {},
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      getDefaultOrgExpandedKeys: 'common/getDefaultOrgExpandedKeys',
    }),
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
  },
  async created() {
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
    this.getTagList(); // 获取标签
    this.queryErrorList(); // 获取错误列表
  },
  methods: {
    async open() {
      this.clearTagsStatus();
      await this.setOrganizationList();
      this.searchData = {
        orgCodeList: this.getDefaultOrgExpandedKeys,
      };
      this.$refs.chooseDevice.init();
    },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
      setOrganizationList: 'common/setOrganizationList',
    }),
    resetSearch() {
      this.searchData = {};
      this.$refs.chooseDevice.search();
      this.clearTagsStatus();
    },
    changeBlur(optionType) {
      if (optionType) {
        if (optionType && !['1'].includes(this.searchData[optionType])) {
          if (optionType === 'isCheck') {
            this.searchData = {
              ...this.searchData,
              baseCheckStatus: '',
              viewCheckStatus: '',
              videoCheckStatus: '',
              baseErrorMessageList: [],
              viewErrorMessageList: [],
              videoErrorMessageList: [],
            };
          } else {
            this.searchData[`${optionType}ErrorMessageList`] = [];
          }
        }
      }
      this.$nextTick(() => {
        this.search();
      });
    },
    search() {
      this.$refs.chooseDevice.search();
    },
    // 左侧 树状选择
    getOrgCode(orgCodeList) {
      this.searchData = {};
      this.searchData.orgCodeList = orgCodeList;
      this.$refs.chooseDevice.search();
    },
    // 点击确认获取选择的设备id
    async getDeviceIdList({ chooseIds, selectedDevNum, checkDeviceFlag }) {
      this.ids = chooseIds;
      this.totalCount = selectedDevNum;
      this.searchData = {
        ...this.searchData,
        checkDeviceFlag,
      };
      // this.$emit('getDeviceQueryForm', {
      //   ...this.searchData,
      //   params: undefined,
      //   ids: [...this.ids],
      //   totalCount: this.totalCount,
      // })
      let params = {
        ...this.searchData,
        params: undefined,
        ids: [...this.ids],
        totalCount: this.totalCount,
      };
      try {
        let { data } = await this.$http.post(viewassets.assertCopyToReport, params);
        this.$Message.success(data.data);
        this.$emit('init');
      } catch (err) {
        console.log(err);
      }
    },

    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.search();
    },
    getOrgList(orgList) {
      this.searchData.orgCodeList = orgList.map((e) => {
        return e.orgCode;
      });
    },

    // 同时请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.errorMessageMap.basic = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.errorMessageMap.view = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.errorMessageMap.video = res3.data.data;

      // let res = await this.$http.get(equipmentassets.queryErrorReason)
      // const { data } = res.data
      // data.forEach((e) => {
      //   for (var key in e) {
      //     this.errorMessageMap[key] = e[key]
      //   }
      // })
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        this.errorList = this.$util.common.deepCopy(
          this.allTagList.map((row) => {
            return {
              name: row.tagName,
              id: row.tagId,
            };
          }),
        );
        // 已选择标签回显选中状态
        const { tagIds = [] } = this.searchData;
        if (tagIds.length > 0) {
          this.setTagsStatus(tagIds);
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 标签类型选中状态回显
    setTagsStatus(list) {
      if (list && list.length > 0) {
        list.forEach((item) => {
          this.errorList.forEach((ite) => {
            if (ite.id == item) {
              this.$set(ite, 'select', true);
            }
          });
        });
      }
    },
    // 标签类型选中状态清除
    clearTagsStatus() {
      this.errorList.forEach((item) => {
        if (item.select) {
          item.select = false;
          // this.$set(item, 'select', false);
        }
      });
    },
    filterFun(data) {
      return data.label.indexOf('济南市') !== -1;
    },
  },
};
</script>
<style lang="less" scoped>
.chooseDevice {
  /deep/ .ivu-modal-body {
    padding: 0 !important;
  }
}
.custom-select-wrap {
  height: 58px;
  display: flex;
}

.right-margin {
  margin-right: 15px;
}
.input-width {
  width: 140px;
}
@{_deep} .ivu-form-item-content {
  .ivu-dropdown {
    .ivu-dropdown-rel {
      .ivu-select-selection {
        height: 32px;
        line-height: 32px;
        .select-content {
          height: 32px;
          line-height: 32px;
          .ivu-select-placeholder {
            height: 32px;
            line-height: 32px;
          }
          .ivu-select-selected-value {
            height: 32px;
            line-height: 32px;
          }
        }
      }
    }
  }
}
@{_deep} .api-organization-tree {
  width: 100% !important;
}
@{_deep} .ivu-dropdown {
  width: 100% !important;
}
@{_deep} .select-width {
  width: 100% !important;
}

@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-6px, 3px) scale(0.45) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    height: 0.135417rem;
    line-height: 0.135417rem;
    padding: 0 6px;
    font-size: 12px !important;
    color: #ffffff;
    border-bottom: none !important;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
    .ivu-icon-ios-arrow-forward {
      font-size: 12px !important;
      color: #239df9;
      transform: rotate(-90deg) scale(0.4);
      &:before {
        font-family: 'icon-font';
        content: '\e7a3';
      }
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      padding-bottom: 0 !important;
    }
  }
}

@{_deep} .el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep} .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.retest {
  @{_deep} .ivu-form-item-content {
    line-height: 0.166667rem !important;
  }
}
@{_deep} .ivu-modal .title {
  z-index: 15;
}

.statustag {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}

.leftSelect {
  width: 130px !important;
}
.rightSelect {
  width: 198px !important;
}
.data-list {
  flex: 1;
  color: var(--color-content);
  font-size: 14px;
  .ui-select-tabs {
    margin-left: 70px;
  }
}
.check-status {
  padding: 0 8px;
}

.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
