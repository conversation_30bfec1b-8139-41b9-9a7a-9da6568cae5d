// Checkbox
.ivu-checkbox-wrapper {
  color: @title-font-color;
  font-size: @font-size14;
  .ivu-checkbox-inner {
    border-color: @iview-normal-color;
    margin-right: 6px;
  }
  .ivu-checkbox:hover .ivu-checkbox-inner {
    border-color: @iview-hover-color;
  }
  .ivu-checkbox-checked .ivu-checkbox-inner {
    border-color: @iview-active-color;
    background-color: @iview-active-color;
  }
  .ivu-checkbox-focus {
    box-shadow: none;
  }
  .ivu-checkbox-indeterminate {
    .ivu-checkbox-inner {
      border-color: @iview-hover-color;
      background-color: #fff;
    }
    .ivu-checkbox-inner:after {
      width: 8px;
      height: 8px;
      background-color: @iview-active-color;
      border: none;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .ivu-checkbox-disabled, .ivu-checkbox-disabled:hover {
    .ivu-checkbox-inner {
      border-color: @iview-normal-color;
      background-color: @iview-disabled-color;
    }
    .ivu-checkbox-inner:after {
      border-color: @iview-disabled-text-color;
    }
  }
}
.ivu-checkbox-wrapper-disabled {
  .ivu-checkbox-indeterminate {
    .ivu-checkbox-inner:after {
      background-color: @iview-normal-color;
    }
  }
}
// Radio
.ivu-radio-wrapper {
  color: @title-font-color;
  font-size: @font-size14;
  .ivu-radio-focus {
    box-shadow: none;
  }
  .ivu-radio {
    margin-right: 10px;
    .ivu-radio-inner {
      border-color: @iview-normal-color;
    }
  }
  .ivu-radio:hover .ivu-radio-inner {
    border-color: @iview-hover-color;
  }
  .ivu-radio-checked, .ivu-radio-checked:hover {
    .ivu-radio-inner {
      border-color: @iview-active-color;
    }
    .ivu-radio-inner:after {
      background-color: @iview-active-color;
    }
  }
  .ivu-radio-disabled, .ivu-radio-disabled:hover {
    .ivu-radio-inner {
      border-color: @iview-normal-color;
      background-color: @iview-disabled-color;
    }
  }
}
.ivu-radio-wrapper-disabled {
  .ivu-radio-checked, .ivu-radio-disabled:hover {
    .ivu-radio-inner::after {
      background-color: @iview-normal-color;
    }
  }
}
// Switch
.ivu-switch {
  width: 40px;
  height: 20px;
  border-radius: 10px;
  border-color: @iview-normal-color;
  background-color: @white;
}
.ivu-switch:after {
  width: 14px;
  height: 14px;
  background-color: @iview-active-color;
  left: 3px;
  top: 2px;
  border-radius: 50%;
}
.ivu-switch:not(.ivu-switch-disabled):focus {
  box-shadow: none;
}
.ivu-switch-checked {
  border-color: @iview-active-color;
  background-color: @iview-active-color;
}
.ivu-switch-checked:after {
  background-color: @white;
  left: unset;
  right: 3px;
}
// Input
.ivu-input-wrapper {
  .ivu-input-group-append {
    border-radius: 4px;
    overflow: hidden;
    border: none;
    width: 50px;
    height: 34px;
    padding: 0;
    .ivu-btn {
      width: 100%;
      height: 100%;
      border-radius: 0;
      background: @linear-color;
      border: none;
      color: #fff;
      margin: 0;
      .ivu-icon {
        margin: 0;
        font-size: 20px;
      }
    }
    .ivu-btn:hover {
      background: @linear-hover-color;
    }
    .ivu-btn:active {
      background: @linear-active-color;
    }
  }
}
.ivu-input {
  border-color: @iview-normal-color;
  height: 34px;
  padding: 0 10px;
}
.ivu-input:hover {
  border-color: @iview-hover-color !important;
  box-shadow: none !important;
}
.ivu-input:focus {
  box-shadow: none !important;
  border-color: @iview-active-color !important;
}
.ivu-input::-webkit-input-placeholder {
  color: @input-placeholder-normal-color;
}
.ivu-input:hover::-webkit-input-placeholder, .ivu-input:focus::-webkit-input-placeholder {
  // color: @input-placeholder-active-color;
}
.ivu-input[disabled], fieldset[disabled] .ivu-input {
  background-color: @iview-disabled-color;
  color: @title-font-color;
}
.ivu-input[disabled]:hover, fieldset[disabled] .ivu-input:hover {
  border-color: @iview-normal-color !important;
}
.ivu-input[disabled]::-webkit-input-placeholder {
  color: @input-placeholder-normal-color;
}
.ivu-form-item-error .ivu-input {
  background-color: @input-error-background;
  border-color: @input-error-border-color;
}
.ivu-form-item-error .ivu-input:focus {
  box-shadow: none;
}
.ivu-form-item-error-tip {
  font-size: @font-size12;
  line-height: 16px;
}
// Select
.ivu-select-single .ivu-select-selection {
  height: 34px;
  border-color: @iview-normal-color;
}
.ivu-select-placeholder, .ivu-select-selected-value {
  padding-left: 10px !important;
}
.ivu-select-single .ivu-select-selection .ivu-select-placeholder {
  color: @input-placeholder-normal-color;
}
.ivu-select-single .ivu-select-selection .ivu-select-placeholder, .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
  height: 34px;
  line-height: 34px;
}
.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
.ivu-select-arrow {
  color: @select-drop-down-icon-color;
}
.ivu-select-single .ivu-select-selection:hover {
  border-color: @iview-hover-color;
  .ivu-select-placeholder {
    color: @input-placeholder-active-color;
  }
}
.ivu-select-single .ivu-select-selection:focus {
  border-color: @iview-active-color;
  .ivu-select-placeholder {
    color: @input-placeholder-active-color;
  }
}
.ivu-select-disabled {
  .ivu-select-selection, .ivu-select-selection:hover, .ivu-select-selection:focus {
    border-color: @iview-normal-color;
    background-color: @iview-disabled-color;
    .ivu-select-placeholder {
      color: @iview-disabled-text-color;
    }
  }
  .ivu-select-placeholder {
    color: @iview-disabled-text-color;
  }
}
.ivu-select-input {
  color: @title-font-color;
}
.ivu-select-input[disabled] {
  color: @title-font-color;
  -webkit-text-fill-color: @title-font-color;
}
.ivu-select-disabled .ivu-select-selection .ivu-select-arrow {
  color: @iview-disabled-text-color;
}
.ivu-form-item-error {
  .ivu-select-selection, .ivu-select-selection:hover, .ivu-select-selection:focus {
    background-color: @input-error-background;
    border-color: @input-error-border-color;
    box-shadow: none;
  }
  .ivu-select-arrow {
    color: @select-drop-down-icon-color;
  }
}
.ivu-select-selected-value {
  color: @input-placeholder-active-color;
}
// 下拉框单选
.ivu-select-dropdown {
  background-color: @select-drop-down-background;
  border-radius: 4px;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  padding: 0;
  .ivu-select-item {
    color: @select-drop-down-item-text-normal-color;
    line-height: 20px;
    padding: 6px 10px;
  }
  .ivu-select-item:hover {
    background-color: @select-drop-down-item-normal-color;
  }
  .ivu-select-item-selected, .ivu-select-item-selected:hover {
    background-color: @select-drop-down-item-active-color;
    color: @select-drop-down-item-text-active-color;
  }
}
// 下拉框多选
.ivu-select-multiple{
  .ivu-select-item-selected, .ivu-select-item-selected:hover {
    background-color: @white;
    color: @primary-color;
  }
}

// Button
.ivu-btn {
  height: 34px;
  padding: 0 22px;
  border-radius: 4px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  &>span {
    display: flex;
    align-items: center;
    justify-content: center;
    &>i {
      margin-right: 10px;
      font-size: 16px;
    }
  }
}
.ivu-btn:focus {
  box-shadow: none;
}
.ivu-btn-primary[disabled], .ivu-btn-primary[disabled]:hover,
.ivu-btn-default[disabled], .ivu-btn-default[disabled]:hover,
.ivu-btn-dashed[disabled], .ivu-btn-dashed[disabled]:hover {
  border-color: @iview-normal-color;
  background-color: @iview-disabled-color;
  color: @iview-disabled-text-color;
}
// primary
.ivu-btn-primary {
  color: @button-primary-text-color;
  background-color: @link-normal-color;
  border-color: @link-normal-color;
  border-radius: 4px;
  .ivu-icon {
    font-size: 16px;
    color: @button-primary-text-color;
    margin-right: 4px;
  }
}
.ivu-btn-primary:hover {
  background-color: @link-hover-color;
  border-color: @link-hover-color;
}
.ivu-btn-primary:active {
  background-color: @link-active-color;
  border-color: @link-active-color;
}
// default、dashed
.ivu-btn-default, .ivu-btn-dashed {
  border-color: @link-normal-color;
  color: @link-normal-color;
  .ivu-icon {
    font-size: 16px;
    color: @link-normal-color;
    margin-right: 4px;
  }
}
.ivu-btn-default:hover, .ivu-btn-dashed:hover {
  border-color: @link-hover-color;
  color: @link-hover-color;
}
.ivu-btn-default:active, ivu-btn-dashed:active {
  border-color: @link-active-color;
  color: @link-active-color;
}
// text
.ivu-btn-text {
  color: @button-text-color;
}
.ivu-btn-text:hover {
  background-color: @button-text-hover-bg-color;
}
.ivu-btn-text:active {
  background-color: @button-text-active-bg-color;
}
.link-btn {
  color: @link-normal-color;
}
.link-btn:hover {
  background-color: transparent;
  color: @link-hover-color;
}
.link-btn:active {
  background-color: transparent;
  color: @link-active-color;
}
//large
.ivu-btn-large {
  font-size: 14px;
  padding: 0 30px;
}
//small
.ivu-btn-small {
  padding: 0 12px;
  height: 28px;
  font-size: 12px;
}
// Table
.ivu-table {
  font-size: 14px;
}
.ivu-table::before {
  height: 0;
  background-color: transparent;
}
.ivu-table-cell {
  // padding: 0 0 0 20px;
}
.ivu-table th {
  background-color: @table-th-bg-color;
  box-shadow: inset 0px -1px 0px 0px @table-th-box-shadow-color;
  color: @title-font-color;
  padding: 15px 0 !important;
  line-height: 20px;
  font-weight: normal;
}
.ivu-table td {
  padding: 14px 0;
  color: @text-font-color;
  line-height: 20px;
}
.ivu-table td, .ivu-table th {
  border-bottom: none;
}
.ivu-table-tbody {
  tr:nth-child(odd) {
    td {
      background-color: @table-tr-odd-bg-color;
    }
  }
  tr:nth-child(even) {
    td {
      background-color: @table-tr-even-bg-color;
    }
  }
  tr:hover {
    td {
      background-color: @table-tr-hover-bg-color;
    }
  }
}
.ivu-table-border td {
  border-bottom: 1px solid #fff;
}
.ivu-table-border td, .ivu-table-border th{
  border-color: @border-color !important;
}
// Page
.pages {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 64px;
  .total {
    color: #999;
    font-size: 16px;
    line-height: 22px;
    display: flex;
    align-items: center;
    .primary {
      line-height: 22px;
      margin: 0 4px;
    }
  }
}
.ivu-page {
  .ivu-icon-ios-arrow-forward:before {
    font-size: 18px;
    content: "\F341";
  }
  .ivu-icon-ios-arrow-back:before {
    font-size: 18px;
    content: "\F33F";
  }
  .ivu-page-next, .ivu-page-prev {
    border-color: @iview-normal-color;
    display: inline-flex;
    align-items: center;
    a {
      width: 100%;
      height: 100%;
      color: @page-pre-next-text-color;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
  .ivu-page-item {
    border-color: @iview-normal-color;
    line-height: normal;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    a {
      color: @page-item-text-color;
    }
  }
  .ivu-page-item:hover {
    border-color: @iview-hover-color;
    a {
      color: @iview-hover-color;
    }
  }
  .ivu-page-item-active, .ivu-page-item-active:hover {
    border-color: @iview-active-color;
    background-color: @iview-active-color;
    a {
      color: #fff;
    }
  }
  .ivu-page-item-jump-next {
    .ivu-icon-ios-more {
      color: @page-item-text-color;
    }
    .ivu-icon-ios-arrow-forward {
      color: @iview-active-color;
    }
  }
  .ivu-page-disabled {
    background-color: @iview-disabled-color;
    border-color: @iview-normal-color;
    a {
      color: @iview-disabled-text-color;
    }
  }
  .ivu-page-options-elevator {
    color: @page-text-color;
    input {
      min-width: 24px;
      height: 24px;
      font-size: 12px;
      border-color: @iview-normal-color;
      color: @title-font-color;
    }
    input:focus {
      box-shadow: none;
    }
    input:hover {
      border-color: @iview-hover-color;
    }
    input:active {
      border-color: @iview-active-color;
    }
  }
  .ivu-select-selection, .ivu-select-selection .ivu-select-selected-value {
    height: 24px !important;
    line-height: 24px;
  }
  .ivu-page-item-jump-next, .ivu-page-item-jump-prev, .ivu-page-next, .ivu-page-prev, .ivu-page-item {
    height: 24px;
    border-radius: 4px;
    min-width: 24px;
    line-height: 24px;
  }
  .ivu-page-item-jump-next:hover i:first-child, .ivu-page-item-jump-prev:hover i:first-child {
    display: inline-flex;
    align-items: center;
  }
  .ivu-select-selection {
    .ivu-select-selected-value, .ivu-select-arrow {
      color: #999999;
    }
  }
}
// dropdown
.ivu-dropdown {
  .ivu-select-dropdown {
    padding: 5px 0;
    .ivu-dropdown-item {
      height: 40px !important;
      border: none !important;
      padding: 0 20px !important;
      display: flex;
      align-items: center;
      .i-layout-menu-side-title {
        height: 100%;
        align-items: flex-start;
      }
      .i-layout-menu-side-title-text {
        color: @text-font-color;
      }
    }
    .ivu-dropdown-item:hover {
      background: @three-menu-item-hover-color;
    }
    .i-layout-menu-side-collapse-item-selected, .i-layout-menu-side-collapse-item-selected:hover {
      background: @three-menu-item-active-color !important;
      .i-layout-menu-side-title-text {
        color: @white !important;
      }
    }
  }
}

// tabs
.ivu-tabs {
  border-radius: 4px;
  &.ui-tabs {
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    .ivu-tabs-bar {
      border-bottom: 0;
      margin-bottom: 0;
      .ivu-tabs-nav-container {
        height: 30px;
        line-height: 30px;
        .ivu-tabs-tab {
          font-size: 16px;
          border: 0;
          margin-right: 0;
          border-radius: 0;
          color: rgba(0, 0, 0, .6);
          padding: 0 23px;
          background: transparent;
          left: -8px;
          position: relative;
          overflow: inherit;
          z-index: 0;
          &:after {
            content: '';
            transform: skewX(-18deg);
            position: absolute;
            background: #D3D7DE;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: -1;
          }
          &:hover {
            color: rgba(0, 0, 0, .8);
          }
          &.ivu-tabs-tab-active {
            // width: 110px;
            height: 30px;
            font-weight: bold;

            color: #fff;
            &:hover {
              color: #fff
            }
            &:after {
              background:  @linear-color;
            }
          }
        }
      }
    }
    .ivu-tabs-tabpane {
      padding: 0 20px;
    }
  }
}
// 按钮类型的tabs
.btn-tabs {
  border-radius: 0;
  .ivu-tabs-bar {
    margin-bottom: 10px;
    border: none;
    .ivu-tabs-nav-container {
      height: 34px !important;
    }
    .ivu-tabs-tab {
      height: 34px !important;
      margin: 0 !important;
      border: 1px solid @primary-color !important;
      border-radius: 0 !important;
      background: transparent !important;
      color: @primary-color !important;
      line-height: 18px;
      padding: 0 15px !important;
      display: inline-flex;
      align-items: center;
      border-right: none !important;
      transition: none !important;
    }
    .ivu-tabs-nav {
      .ivu-tabs-tab:nth-child(2) {
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
      }
      .ivu-tabs-tab:last-child {
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        border-right: 1px solid @primary-color !important;
      }
    }
    .ivu-tabs-tab-active {
      background: @primary-color !important;
      color: @white !important;
    }
  }
}
// anchor
.ivu-anchor-wrapper {
  width: 100%;
  background: @white;
  box-shadow: 0px 3px 5px 0px @layout-container-shadow-color;
  border-radius: 4px;
  margin-left: 0;
  padding: 20px 10px;
  .ivu-anchor-ink-ball {
    width: 16px;
    height: 10px;
    border: none;
    border-radius: 0;
    overflow: hidden;
  }
  .ivu-anchor-ink-ball::before {
    content: "";
    width: 10px;
    height: 10px;
    background: @primary-color;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    position: absolute;
    top: 0;
    left: 0;
  }
  .ivu-anchor-ink-ball::after {
    content: "";
    width: 10px;
    height: 10px;
    border-top-right-radius: 2px;
    background: @primary-color;
    position: absolute;
    top: 0px;
    left: 4px;
    transform: rotate(45deg);
  }
  .ivu-anchor::before, .ivu-anchor::after {
    content: '';
    width: 8px;
    height: 8px;
    border: 1px solid @primary-color;
    position: absolute;
    top: -8px;
    left: -3.5px;
    border-radius: 50%;
    background: @white;
    z-index: 100;
  }
  .ivu-anchor::after {
    top: unset;
    bottom: -8px;
    left: -3px;
  }
  .ivu-anchor-ink::before {
    width: 0px;
    background: transparent;
    border-left: 1px solid @border-color;
  }
  .ivu-anchor-link {
    padding: 12px 0;
    display: flex;
    align-items: center;

    .ivu-anchor-link-title {
      padding-left: 16px;
      font-size: 12px;
      color: @text-font-color;
      transition: none;
      // line-height: 18px;
    }
  }
  .ivu-anchor-link::before {
    content: '';
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background: @border-color;
    display: inline-block;
    margin-left: -4px;
  }
  .ivu-anchor-link:hover {
    .ivu-anchor-link-title {
      color: @link-hover-color;
    }
  }
  .ivu-anchor-link-active {
    .ivu-anchor-link-title {
      font-size: 14px;
    }
  }
  .ivu-anchor-link-active, .ivu-anchor-link-active:hover {
    .ivu-anchor-link-title {
      color: @link-active-color;
      font-weight: bold;
      font-family: 'MicrosoftYaHei-Bold';
    }
  }
}
// 日历
.ivu-date-picker {
  .ivu-date-picker-focused input:not([disabled]) {
    box-shadow: none;
  }
  .ivu-input-prefix i, .ivu-input-suffix i {
    color: #888;
  }
  .ivu-date-picker-header {
    border-bottom: 1px solid @border-color;
  }
  .ivu-picker-panel-icon-btn {
    color: #888;
  }
  .ivu-date-picker-header-label {
    color: @title-font-color;
    font-size: 14px;
    line-height: 20px;
  }
  .ivu-date-picker-header-label:hover {
    color: @primary-color;
  }
  .ivu-date-picker-cells-header span {
    color: @primary-color;
    font-size: 14px;
    line-height: 20px;
  }
  .ivu-date-picker-cells-cell em {
    color: @text-font-color;
    font-size: 14px;
    width: 20px;
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    margin: 4px;
  }
  .ivu-date-picker-cells-cell-today em {
    color: @primary-color;
  }
  .ivu-date-picker-cells-cell-today em:after {
    display: none;
  }
  .ivu-date-picker-cells-cell:hover em {
    background: #D3D7DE;
  }
  .ivu-date-picker-cells-cell-selected em, .ivu-date-picker-cells-cell-selected:hover em {
    background: @primary-color;
    color: @white;
  }
  .ivu-date-picker-cells-focused em {
    box-shadow: none;
  }
  .ivu-icon-ios-time-outline:before {
    content: "\F15B";
  }
}
.slider-content {
  display: flex;
  flex: 1;
  align-items: center;
  > span {
    font-size: 14px;
    color: rgba(0, 0, 0, .8);
  }
}
.ivu-slider {
  width: 235px;
  margin: 0 10px;
  .ivu-slider-wrap {
    background: @border-color;
    height: 6px;
    margin: 3px 0;
  }
  .ivu-slider-bar {
    height: 6px;
    background: linear-gradient(270deg, @primary-color 0%, #5BA3FF 100%);
  }
  .ivu-slider-button-wrap {
    top: -4px;
    .ivu-slider-button {
      width: 14px;
      height: 14px;
    }
    .ivu-tooltip {
      display: flex;
    }
  }
}
// 自定义菜单 （黑色的）
 .ivu-menu.ivu-menu-vertical {
    &.search-menu {
      background-color: #484847;
      &:after{
        display: none;
      }
      .ivu-menu-item {
        i {
          margin-right: 0;
          font-size: 20px;
        }
        padding: 0;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        &.ivu-menu-item-active:not(.ivu-menu-submenu) {
          background: @primary-color;
          &:after {
            width: 0;
            height: 0;
            position: absolute;
            top: 50%;
            right: -11px;
            margin-top: -3px;
            background: transparent;
            border-right: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-left: 6px solid @primary-color;
            border-top: 6px solid transparent;
          }
        }
      }
    }
  }

// 文字类型的单选框
.text-radio-group {
  .ivu-radio-wrapper {
    border: none !important;
    border-radius: 0 !important;
    color: @text-font-color;
    padding: 0 10px;
    height: 34px;
    line-height: 34px;
    display: inline-flex;
    align-items: center;
    .ivu-radio {
      display: none;
    }
  }
  .ivu-radio-wrapper:hover {
    color: @iview-hover-color;
  }
  .ivu-radio-wrapper-checked, .ivu-radio-wrapper-checked:hover {
    color: @primary-color;
    box-shadow: none;
  }
  .ivu-radio-wrapper:before, .ivu-radio-wrapper:after {
    display: none;
  }
}
// 标签
.ivu-tag {
  border-radius: 4px;
  padding: 0 6px;
  .ivu-tag-text {
    font-size: 14px;
    line-height: 20px;
  }
}
// 上传图片
.ivu-upload {
  .ivu-upload-drag {
    border-color: @border-color;
  }
  .ivu-upload-drag:hover {
    border-color: @primary-color;
  }
}
// 文字提示
.ivu-tooltip-popper[x-placement^=top] .ivu-tooltip-arrow{
    bottom: 4px;
}
.ivu-tooltip-popper[x-placement^=bottom] .ivu-tooltip-arrow{
    top: 4px;
}
