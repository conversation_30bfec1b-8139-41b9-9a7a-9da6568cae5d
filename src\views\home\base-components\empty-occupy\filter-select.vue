<template>
  <div class="index-container">
    <Dropdown
      trigger="click"
      @on-click="onClickDropdown"
      transfer
      transfer-class-name="filter-select-dropdown"
      ref="filterSelectRef"
    >
      <i class="icon-font icon-shaixuan f-16 color-filter"></i>
      <DropdownMenu slot="list">
        <DropdownItem
          v-for="(item, index) in list"
          :key="index"
          :selected="selected === item.key"
          :name="item.key"
          class="ellipsis"
        >
          <span class="dis-select">{{ item.name }}</span>
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>

<script>
export default {
  name: 'filter-select',
  components: {},
  props: {
    list: {
      type: Array,
      default: () => {
        return [];
      },
    },
    defaultSelected: '',
  },
  data() {
    return {
      selected: '',
    };
  },
  computed: {},
  mounted() {
    this.$nextTick(() => {
      // 解决全屏screenfull下，一些transfer弹框不展示问题
      let bigParent = this.$refs.filterSelectRef?.$parent?.$parent?.$parent?.$parent?.$el;
      let dropdownTransfer = document.querySelectorAll('.filter-select-dropdown');
      if (!bigParent || !dropdownTransfer) return;
      dropdownTransfer.forEach((dom) => {
        bigParent.appendChild(dom);
      });
    });
  },
  watch: {
    defaultSelected: {
      handler(val) {
        this.selected = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    resetSelected() {
      this.selected = this.defaultSelected;
    },
    onClickDropdown(val) {
      if (val === this.selected) return;
      this.selected = val;
      this.$emit('change', val);
    },
  },
};
</script>

<style lang="less" scoped>
.color-filter {
  color: #1d84a0;
  &:hover {
    color: #539aea;
  }
}
.mini-btn {
  width: 48px;
  height: 28px;
  line-height: 28px;
  padding: 0 5px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.index-container {
  position: relative;
  display: inline-block;
  .dropdown {
    width: 300px;
    height: 300px;
    overflow-y: auto;
  }
}
</style>
