<template>
  <ui-modal v-model="visible" title="关联标签" :styles="styles">
    <div class="content-wrap">
      <div class="left-module">
        <div class="tree">
          <ui-search-tree
            ref="uiSearchTree"
            node-key="orgCode"
            :scroll="385"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-keys="defaultExpandedKeys"
            :current-node-key="currentNodeKey"
            @selectTree="selectTree"
          ></ui-search-tree>
        </div>
        <div class="device-list auto-fill">
          <div class="search-module">
            <!--                <span class="base-text-color"-->
            <!--                >设备列表<span class="font-red">{{ pageData.totalCount }}</span-->
            <!--                >条</span-->
            <!--                >-->
            <!--            <Checkbox class="ml-lg" v-model="isExclude">反选</Checkbox>-->
            <div class="search-module-top">
              <ui-label label="关键词" :width="55" class="inline">
                <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入设备名称或编码"></Input>
              </ui-label>
              <ui-label :label="global.filedEnum.sbdwlx" :width="100" class="inline ml-lg">
                <Select
                  v-model="searchData.sbdwlx"
                  class="width-md"
                  :placeholder="`请选择${global.filedEnum.sbdwlx}`"
                  clearable
                >
                  <Option v-for="(item, index) in sbdwlxList" :key="index" :value="item.dataKey">
                    {{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <ui-label :label="global.filedEnum.sbgnlx" :width="110" class="inline ml-lg">
                <Select
                  v-model="searchData.sbgnlx"
                  class="width-md"
                  :placeholder="`请选择${global.filedEnum.sbgnlx}`"
                  clearable
                >
                  <Option v-for="(item, index) in sbgnlxList" :key="index" :value="item.dataKey">
                    {{ item.dataValue }}
                  </Option>
                </Select>
              </ui-label>
              <div class="inline ml-sm">
                <Button type="primary" @click="searchDevice">查询</Button>
                <Button class="ml-sm" @click="resetSearchDataMx(searchData, searchDevice)"> 重置 </Button>
              </div>
            </div>
            <div class="search-module-bottom mr-tp20">
              <div class="flex-1">
                <Checkbox class="checks mr-lg align-flex" v-model="isAll" @on-change="handleAllCheck"> 全选 </Checkbox>
                <Checkbox class="checks mr-sm align-flex" v-model="isExclude" @on-change="handleExcludeCheck">
                  排除
                </Checkbox>
                <RadioGroup v-if="isExclude" v-model="excludeType" @on-change="handleExcludeTypeChange">
                  <Radio label="0">全量设备排除选中设备</Radio>
                  <Radio label="1">筛选结果排除选中设备</Radio>
                </RadioGroup>
              </div>
              <p class="base-text-color">
                已选设备
                <span class="font-red">{{ checkDevices }}</span>
                条
              </p>
            </div>
          </div>
          <ui-table
            class="ui-table auto-fill"
            reserveSelection
            :table-columns="tableColumns"
            :table-data="tableData"
            :loading="deviceLoading"
            :default-store-data="defaultStoreData"
            @storeSelectList="storeSelectList"
          >
            <template slot="deviceId" slot-scope="{ row }">
              <span class="font-active-color pointer" @click="deviceArchives(row)">
                {{ row.deviceId }}
              </span>
            </template>
            <!-- 经纬度保留8位小数-->
            <template #longitude="{ row }">
              <span>{{ row.longitude | filterLngLat }}</span>
            </template>
            <template #latitude="{ row }">
              <span>{{ row.latitude | filterLngLat }}</span>
            </template>
          </ui-table>
          <ui-page
            class="page"
            :page-data="pageData"
            @changePage="changePage"
            @changePageSize="changePageSize"
          ></ui-page>
        </div>
      </div>
      <div class="right-module">
        <div class="search-module betw-flex">
          <span class="base-text-color">
            标签列表
            <span class="font-red">{{ tagData.length }}</span>
            条
          </span>
          <Input
            v-model="tagName"
            placeholder="请输入标签名称"
            search
            class="width-md ml-lg"
            @on-search="searchTag"
          ></Input>
        </div>
        <div class="tag-list" v-ui-loading="{ loading: tagLoading, tableData: tagData }">
          <div>
            <Checkbox
              class="ml-sm mb-sm"
              v-for="(item, index) in tagData"
              :key="index"
              :label="item.tagId"
              v-model="item.checked"
              @on-change="checkTag($event, item)"
            >
              <span class="tag-item ellipsis" :title="item.tagName">
                {{ item.tagName }}
              </span>
            </Checkbox>
          </div>
        </div>
        <div class="preview-tag">
          <ui-tag v-for="(item, index) in checkedTagData" @close="handleCloseTag(index)" :closeable="true" :key="index">
            {{ item.tagName }}
          </ui-tag>
        </div>
      </div>
    </div>
    <template #footer>
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="query" :loading="Queryloading" class="plr-30"> 确 定 </Button>
    </template>
  </ui-modal>
</template>
<script>
import user from '@/config/api/user';
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '9.7rem',
      },
      Queryloading: false,
      // isExclude: false,
      sbdwlxList: [],
      sbgnlxList: [],
      searchData: {
        keyWord: '',
        sbdwlx: '',
        sbgnlx: '',
        orgCode: '',
        checkStatuses: [],
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      currentNodeKey: null,
      tagName: '',
      dicDataEnum: Object.freeze({
        propertySearch_sbdwlx: 'sbdwlxList',
        sxjgnlx_receive: 'sbgnlxList',
      }),
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          title: '序号',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
        },
        {
          minWidth: 200,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 90,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 110,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
        },
        {
          width: 110,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
        },
      ],
      tableData: [],
      defaultStoreData: [],
      deviceLoading: false,
      checkedDeviceData: [],
      initTagData: [],
      tagData: [],
      tagLoading: false,
      checkedTagData: [],
      isAll: false,
      isExclude: false,
      excludeType: '0',
      checkDevices: 0,
      devTotalCounts: 0,
    };
  },
  created() {},
  methods: {
    selectTree(data) {
      this.searchData.orgCode = data.orgCode;
      this.searchDevice();
      this.getDevTotalCounts();
    },
    async initDictionary() {
      try {
        const res = await this.$http.post(user.queryDataByKeyTypes, Object.keys(this.dicDataEnum));
        Object.keys(this.dicDataEnum).forEach((key) => {
          let obj = res.data.data.find((row) => {
            return !!row[key];
          });
          this[this.dicDataEnum[key]] = obj[key];
        });
      } catch (err) {
        console.log(err);
      }
    },
    storeSelectList(selection) {
      this.checkedDeviceData = selection;
      if (!this.isAll && !this.isExclude) {
        this.checkDevices = selection.length;
      }
      if (this.isExclude) {
        this.getExcludeCheckDevs();
      }
    },
    async searchDevice() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      await this.initDeviceList();
      if (!this.isAll && !this.isExclude) {
        this.checkDevices = this.checkedDeviceData.length;
      } else {
        this.defaultStoreData = [];
        this.checkedDeviceData = [];
        if (this.isAll) {
          this.checkDevices = this.pageData.totalCount;
          this.handleDataChecked();
        } else {
          this.getExcludeCheckDevs();
        }
      }
      // if (this.isAll) {
      //   this.checkDevices = this.pageData.totalCount
      //   this.handleDataChecked()
      // } else {
      //   if (!this.isAll && !this.isExclude) {
      //     this.checkDevices = this.checkedDeviceData.length
      //   }
      //   if (this.isExclude) {
      //     this.getExcludeCheckDevs()
      //   }
      //   this.defaultStoreData = []
      //   this.checkedDeviceData = []
      // }
    },
    // 查询设备数据列表
    async initDeviceList() {
      this.deviceLoading = true;
      this.tableData = [];
      this.copySearchDataMx(this.searchData);
      try {
        let res = await this.$http.post(equipmentassets.getPageDeviceList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.deviceLoading = false;
      }
    },
    async changePage(val) {
      this.searchData.pageNumber = val;
      await this.initDeviceList();
      this.handleDataChecked();
    },
    async changePageSize(val) {
      this.searchData.pageSize = val;
      await this.searchDevice();
      this.handleDataChecked();
    },
    // 查询所有标签
    async initTagList() {
      try {
        this.tagLoading = true;
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagData = res.data.data.map((row) => {
          this.$set(row, 'checked', false);
          return row;
        });
        this.initTagData = this.tagData;
      } catch (err) {
        console.log(err);
      } finally {
        this.tagLoading = false;
      }
    },
    searchTag() {
      this.tagLoading = true;
      let searchArr = [];
      if (this.tagName !== '') {
        for (let i = 0, len = this.initTagData.length; i < len; i++) {
          let str = this.initTagData[i].tagName;
          if (str.indexOf(this.tagName) !== -1) {
            searchArr.push(this.initTagData[i]);
          }
        }
        this.tagData = searchArr;
      } else {
        this.tagData = this.initTagData;
      }
      this.tagLoading = false;
    },
    checkTag(isCheck, item) {
      if (isCheck) {
        this.checkedTagData.push(item);
      } else {
        const index = this.checkedTagData.findIndex((row) => row.tagId === item.tagId);
        this.checkedTagData.splice(index, 1);
      }
    },
    handleCloseTag(index) {
      let tag = this.tagData.find((row) => row.tagId === this.checkedTagData[index].tagId);
      this.$set(tag, 'checked', false);
      this.checkedTagData.splice(index, 1);
    },
    async query() {
      try {
        // 勾选设备或者全选或者排除
        if ((!this.checkedDeviceData.length && !this.isExclude && !this.isAll) || !this.checkedTagData.length) {
          return this.$Message.error('请选择设备和标签');
        }
        this.Queryloading = true;
        let params = {
          isAll: this.isAll,
          isExclude: this.isExclude,
          ids: this.checkedDeviceData.map((item) => item.id),
          tagIds: this.checkedTagData.map((item) => item.tagId),
        };
        if (this.isExclude) {
          params.keyWord = this.excludeType === '0' ? '' : this.searchData.keyWord;
          params.sbdwlx = this.excludeType === '0' ? '' : this.searchData.sbdwlx;
          params.sbgnlx = this.excludeType === '0' ? '' : this.searchData.sbgnlx;
          params.orgCode = this.excludeType === '0' ? '' : this.searchData.orgCode;
        }
        if (this.isAll) {
          params.keyWord = this.searchData.keyWord;
          params.sbdwlx = this.searchData.sbdwlx;
          params.sbgnlx = this.searchData.sbgnlx;
          params.orgCode = this.searchData.orgCode;
        }
        let { data } = await this.$http.post(taganalysis.batchUpdateDeviceInfoTag, params);
        this.$Message.success(data.msg);
        this.visible = false;
        this.$emit('update');
      } catch (e) {
        console.log(e);
      } finally {
        this.Queryloading = false;
      }
    },
    handleAllCheck(val) {
      if (val) {
        this.isExclude = false;
        this.checkDevices = this.pageData.totalCount;
      } else {
        this.checkDevices = 0;
      }
      this.handleDataChecked();
    },
    handleExcludeCheck(val) {
      if (val) {
        this.isAll = false;
        this.handleDataChecked();
        this.getExcludeCheckDevs();
      } else {
        this.checkDevices = this.checkedDeviceData.length ? this.checkedDeviceData.length : 0;
      }
    },
    handleExcludeTypeChange() {
      this.getExcludeCheckDevs();
    },
    handleDataChecked() {
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isAll);
        this.$set(item, '_disabled', this.isAll);
        return item;
      });
    },
    async getDevTotalCounts() {
      const params = {
        keyWord: '',
        sbdwlx: '',
        sbgnlx: '',
        orgCode: this.searchData.orgCode,
        checkStatuses: [],
        pageNumber: 1,
        pageSize: 20,
      };
      try {
        let res = await this.$http.post(equipmentassets.getPageDeviceList, params);
        this.devTotalCounts = res.data.data.total;
      } catch (err) {
        console.log('err', err);
      }
    },
    // 选择排除功能时计算已选设备数
    getExcludeCheckDevs() {
      // 全量设备总数：devTotalCounts   筛选结果设备总数：totalCount
      if (this.excludeType === '0') {
        // 全量设备排除选中设备s
        this.checkDevices =
          this.devTotalCounts - this.checkedDeviceData.length > 0
            ? this.devTotalCounts - this.checkedDeviceData.length
            : 0;
      } else {
        // 筛选结果排除选中设备
        this.checkDevices =
          this.pageData.totalCount - this.checkedDeviceData.length > 0
            ? this.pageData.totalCount - this.checkedDeviceData.length
            : 0;
      }
    },
  },
  watch: {
    value(val) {
      if (val) {
        this.getDevTotalCounts();
        this.initDictionary();
        this.initTagList();
        this.currentNodeKey = this.getDefaultSelectedOrg.orgCode || null;
        this.searchData.orgCode = this.getDefaultSelectedOrg.orgCode;
        this.initDeviceList();
        this.checkedDeviceData = [];
        this.checkedTagData = [];
        this.defaultStoreData = [];
        this.isAll = false;
        this.isExclude = false;
        this.excludeType = '0';
        this.checkDevices = '0';
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      defaultExpandedKeys: 'common/getDefaultOrgExpandedKeys',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    UiModal: require('@/components/ui-modal.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTag: require('@/components/ui-tag').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 20px 0 0 0;
}
.mr-tp20 {
  margin-top: 20px;
}
.flex-1 {
  flex: 1;
}
.betw-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.content-wrap {
  width: 100%;
  height: 750px;
  display: flex;
}
.left-module {
  //flex: 1;
  width: calc(100% - 410px);
  display: flex;
  height: 100%;
  border-top: 1px solid var(--border-modal-footer);
  .search-module {
    padding: 20px;
    //border-top: 1px solid var(--border-color);
    //border-bottom: 1px solid var(--border-color);
    //border-right: 1px solid var(--border-color);
    &-top {
    }
    &-bottom {
      display: flex;
    }
  }
  .tree {
    width: 300px;
    height: 100%;
    padding: 10px;
    overflow-x: auto;
  }
  .device-list {
    position: relative;
    height: 100%;
    //padding: 20px 20px 0 20px;
    border-left: 1px solid var(--border-modal-footer);
    border-right: 1px solid var(--border-modal-footer);
    overflow: visible;
  }
}
.right-module {
  width: 410px;
  border-top: 1px solid var(--border-modal-footer);
  .search-module {
    padding: 20px;
    //border-bottom: 1px solid var(--border-color);
    @{_deep} .ivu-input-icon {
      color: var(--color-primary) !important;
    }
  }
  .tag-list {
    width: 100%;
    height: 300px;
    padding: 0 20px 20px;
    overflow-x: hidden;
    overflow-y: auto;
    border-bottom: 1px solid var(--border-modal-footer);
    .tag-item {
      display: inline-block;
      vertical-align: middle;
      width: 80px;
    }
  }
  .preview-tag {
    padding: 20px;
    height: 325px;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
@{_deep} .checks .ivu-checkbox {
  margin-right: 10px !important;
}
</style>
