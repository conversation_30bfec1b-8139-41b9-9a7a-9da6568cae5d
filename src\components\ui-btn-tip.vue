<template>
    <Tooltip
        :placement="placement"
        max-width="400"
        :transfer="transfer"
        transfer-class-name="btn-tip"
    >
        <i
            v-if="icon"
            :class="['iconfont', disabled ? 'isdisabled' : '', icon]"
            :style="styles"
            @click="handleIconClick"
        ></i>
        <span v-else :class="contentclass" :style="styles">{{ btnText }}</span>
        <template #content>
            <span>{{ content }}</span>
            <slot name="content"></slot>
        </template>
    </Tooltip>
</template>
<script>
export default {
  components: {},
  props: {
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 内容
    content: {
      type: String,
      default: ''
    },
    //
    transfer: {
      type: Boolean,
      default: false
    },
    // 样式
    styles: {
      type: Object,
      default: () => {
      }
    },
    // 数据
    row: {
      type: Object,
      default: () => {
      }
    },
    // 无icon时展示的图标
    btnText: {
      type: String,
      default: ''
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    placement: {
      type: String,
      default: 'top'
    },
    contentclass: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {}
  },
  watch: {},
  created () {
  },
  deactivated () {
    const nodeList = document.querySelectorAll('.ivu-tooltip-popper')
    nodeList.forEach(e => {
      e.style.display = 'none'
    })
  },
  methods: {
    handleIconClick () {
      if (this.disabled) {
        return
      }
      this.$emit('handleClick', this.row)
    }
  }
}
</script>
<style lang="less" scoped>
.isdisabled {
  -webkit-filter: grayscale(100%); /* Chrome, Safari, Opera */
  filter: grayscale(100%);
  cursor: not-allowed;
}
.iconfont {
  cursor: pointer;
}
</style>
