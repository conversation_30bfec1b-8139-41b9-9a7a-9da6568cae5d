<template>
  <Tooltip transfer max-width="400" :placement="placement">
    <i
      v-if="icon"
      :class="['f-14', 'icon-font', disabled ? 'isdisabled' : '', icon]"
      :style="styles"
      @click="handleIconClick"
    ></i>
    <span v-else :class="contentclass" :style="styles">{{ btnText }}</span>

    <template #content>
      <span>{{ content }}</span>
      <slot name="content"> </slot>
    </template>
  </Tooltip>
</template>
<script>
export default {
  props: {
    // 图标
    icon: {
      type: String,
      default: '',
    },
    // 内容
    content: {
      type: String,
      default: '',
    },
    // 样式
    styles: {
      type: Object,
      default: () => {},
    },
    // 数据
    row: {
      type: Object,
      default: () => {},
    },
    // 无icon时展示的图标
    btnText: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placement: {
      type: String,
      default: 'bottom',
    },
    contentclass: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      /** https://github.com/iview/iview/issues/3110
       * Tooltip加在A页面的图标上面，点击进入B页面，然后返回A页面气泡还在，不消失 #3110
       */
      tooltipDisabled: false,
    };
  },
  created() {},
  methods: {
    handleIconClick() {
      if (this.disabled) {
        return;
      }
      this.$emit('handleClick', this.row);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.isdisabled {
  color: var(--color-btn-primary-disabled) !important;
  cursor: not-allowed;
}
</style>
