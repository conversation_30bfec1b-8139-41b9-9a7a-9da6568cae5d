<template>
  <div class="wifi-content-wrapper">
    <div class="btn-list">
      <Button @click="handleSort" size="small">
        <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
        <Icon type="md-arrow-round-up" v-else />
        时间排序
      </Button>
      <Button
        v-show="updateLayerId"
        size="small"
        @click="handleExport"
        :disabled="!updateLayerId"
        v-if="dataList.length"
      >
        <ui-icon
          type="daoru"
          :color="updateLayerId ? '#2C86F8' : 'rgba(0, 0, 0, 0.35)'"
        ></ui-icon>
        导出
      </Button>
    </div>
    <div class="wifi-content">
      <div v-for="(e, i) in dataList" :key="i" class="box-item">
        <div
          class="wifi-item"
          :class="{ active: currentClickIndex == i }"
          @click="chooseMapItem($event, i)"
        >
          <div class="header">
            <div class="header-left">
              <span
                class="serialNumber ellipsis"
                :class="{ activeNumber: currentClickIndex == i }"
              >
                <span>{{ i + 1 }}</span>
              </span>
            </div>
            <div class="header-name" v-show-tips>
              {{ e.deviceId || "--" }}
            </div>
            <ui-icon
              v-if="canWrite"
              class="ml-5 mr-5"
              type="shanchu"
              @click.native.stop="deleteItem(e, i)"
            ></ui-icon>
          </div>
          <div class="content">
            <div class="content-right">
              <!-- <span>
                <ui-icon type="MAC" :size="14"></ui-icon>
                <span>{{ e.name }}</span>
              </span> -->
              <span class="ellipsis">
                <ui-icon type="time" :size="14"></ui-icon>
                <span>{{ e.absTime || "--" }}</span>
              </span>
              <span class="ellipsis">
                <ui-icon type="location" :size="14"></ui-icon>
                <span>{{ e.address || "--" }}</span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="!dataList.length" />
  </div>
</template>

<script>
import { exportExcel } from "@/api/operationsOnTheMap";
export default {
  props: {
    currentClickIndex: {
      type: Number,
      default: -1,
    },
    orderType: {
      type: String,
      default: "",
    },
    dataList: {
      type: Array,
      default: () => [],
    },
    // 写权限，被分享的只能读
    canWrite: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      timeUpDown: false,
    };
  },
  watch: {
    // currentClickIndex: {
    //   handler (newVal){
    //       if(newVal > -1) {
    //           let list =  document.querySelectorAll('.box-item');
    //           list[newVal].scrollIntoView(false)
    //       }
    //   },
    // },
    orderType: {
      handler(newVal) {
        this.timeUpDown = newVal == "desc" ? false : true;
      },
    },
  },
  methods: {
    chooseMapItem($event, index) {
      $event.stopPropagation();
      this.$emit("chooseMapItem", index);
    },
    // 排序
    handleSort(val) {
      this.timeUpDown = !this.timeUpDown;
      this.$emit("changeOrder", this.timeUpDown ? "asc" : "desc");
    },
    handleExport() {
      exportExcel({
        id: this.updateLayerId,
        excelType: "gps",
      }).then((res) => {
        if (res.data) {
          let aLink = document.createElement("a");
          aLink.href = res.data;
          aLink.click();
        }
      });
    },
    deleteItem(item, index) {
      this.$emit("deleteItem", item, index);
    },
  },
};
</script>

<style lang="less" scope>
.btn-list {
  margin: 10px;
  display: flex;
  justify-content: space-between;
}
.wifi-content {
  padding: 10px;
  width: 360px;
  height: calc(~"100% - 56px");
  overflow: hidden;
  overflow-y: auto;
  cursor: pointer;
  .wifi-item {
    height: 92px;
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    .header {
      display: flex;
      justify-content: space-between;
      height: 42px;
      align-items: center;
      padding-left: 10px;
      &-left {
        display: flex;
        align-items: center;
        line-height: 42px;
        .serialNumber {
          position: relative;
          display: inline-block;
          width: 32px;
          height: 32px;
          margin-right: 14px;
          color: black;
          background: url("~@/assets/img/map/trajectory-red.png") no-repeat;
          > span {
            position: absolute;
            top: -10px;
            width: 32px;
            color: #ea4a36;
            text-align: center;
          }
        }
        .activeNumber {
          background: url("~@/assets/img/map/trajectory-blue.png") no-repeat !important;
          > span {
            color: #2c86f8 !important;
          }
        }
      }
      &-name {
        width: 275px;
        font-size: 14px;
        font-weight: bold;
        color: #f29f4c;
      }
    }
    .content {
      display: flex;
      padding: 0 10px;
      &-left {
        > img {
          width: 60px;
          height: 60px;
          border: 1px solid #d3d7de;
        }
      }
      &-right {
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        width: 330px;
        .iconfont {
          margin-right: 10px;
        }
      }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.1);
  }
}
</style>
