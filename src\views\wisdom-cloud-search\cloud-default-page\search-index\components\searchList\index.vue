<template>
  <div class="type_list">
    <!-- 多模态解析 -->
    <div class="video-archive module_list" v-if="getTotal('dmtjx')">
      <ModuleTitle
        label="多模态解析"
        :end="getTotal('dmtjx')"
        @more="handlePageMore('dmtjx')"
      />
      <div class="box_list">
        <div
          v-for="(item, index) in getList('dmtjx')"
          :key="index"
          class="card-item"
        >
          <ResultCard
            :data="item"
            @click.native="handleMutiAnalysis(index)"
          ></ResultCard>
        </div>
      </div>
    </div>

    <!-- 视频档案 -->
    <div class="video-archive module_list" v-if="getTotal('videoArchive')">
      <ModuleTitle
        label="视频档案"
        :end="getTotal('videoArchive')"
        @more="handlePageMore('videoArchive')"
      />
      <div class="box_list">
        <div
          v-for="(item, index) in getList('videoArchive')"
          :key="index"
          class="card-item"
        >
          <UiListCard
            :type="item.type === 'videoArchive' ? 'video' : 'people'"
            :data="item"
            :index="index"
            :isChange="!!item.realNameArchiveNo"
            @on-change="changeCardHandle(item, index)"
            :collectIcon="false"
            @archivesDetailHandle="archiveDetail(item, 'videoArchive')"
          />
        </div>
      </div>
    </div>

    <!-- 实名档案 -->
    <div
      class="real-name-archive module_list"
      v-if="getTotal('realNameArchive')"
    >
      <ModuleTitle
        label="实名档案"
        :end="getTotal('realNameArchive')"
        @more="handlePageMore('realNameArchive')"
      />
      <div class="box_list">
        <div
          v-for="(item, index) in getList('realNameArchive')"
          :key="index"
          class="card-item"
        >
          <UiListCard
            type="people"
            :data="item"
            :index="index"
            :collectIcon="false"
            @archivesDetailHandle="archiveDetail(item, 'realNameArchive')"
          />
        </div>
      </div>
    </div>

    <!-- 视图设备（一机一档） -->
    <div class="deviceArchive module_list" v-if="getTotal('deviceArchive')">
      <ModuleTitle
        label="视图设备"
        :end="getTotal('deviceArchive')"
        @more="handlePageMore('deviceArchive')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('deviceArchive')"
          :key="index"
        >
          <UiListCard
            type="cloudDevice"
            :collectIcon="false"
            :data="item"
            :index="index"
            @archivesDetailHandle="archiveDetail(item, 'deviceArchive')"
          />
        </div>
      </div>
    </div>

    <!-- 一车一档 -->
    <div class="vehicle-archive module_list" v-if="getTotal('vehicleArchive')">
      <ModuleTitle
        label="一车一档"
        :end="getTotal('vehicleArchive')"
        @more="handlePageMore('vehicleArchive')"
      />
      <div class="box_list">
        <div
          v-for="(item, index) in getList('vehicleArchive')"
          :key="index"
          class="card-item"
        >
          <UiListCard
            type="vehicle"
            :data="item"
            :collectIcon="false"
            :index="index"
            @archivesDetailHandle="archiveDetail(item, 'vehicleArchive')"
          />
        </div>
      </div>
    </div>

    <!-- 场所档案 -->
    <div class="vehicle-archive module_list" v-if="getTotal('placeArchive')">
      <ModuleTitle
        label="场所档案"
        :end="getTotal('placeArchive')"
        @more="handlePageMore('placeArchive')"
      />
      <div class="box_list">
        <div
          v-for="(item, index) in getList('placeArchive')"
          :key="index"
          class="card-item"
        >
          <UiListCard
            type="place"
            :data="item"
            :collectIcon="false"
            :index="index"
            @archivesDetailHandle="archiveDetail(item, 'placeArchive')"
          />
        </div>
      </div>
    </div>

    <!-- 人员报警 -->
    <div class="rybj module_list" v-if="getTotal('rybj')">
      <ModuleTitle
        label="人员报警"
        :end="getTotal('rybj')"
        @more="handlePageMore('rybj')"
      />
      <div class="box_list">
        <personnelAlarm
          v-for="(item, index) in getList('rybj')"
          :key="index"
          :itemList="item"
          @detailFn="handlePersonnel(item, index)"
        />
      </div>
    </div>

    <!-- 车辆报警 -->
    <div class="clbj module_list" v-if="getTotal('clbj')">
      <ModuleTitle
        label="车辆报警"
        :end="getTotal('clbj')"
        @more="handlePageMore('clbj')"
      />
      <div class="box_list">
        <vehicleAlarm
          v-for="(item, index) in getList('clbj')"
          :key="index"
          :itemList="item"
          @detailFn="handleVehicle(item, index)"
        />
      </div>
    </div>

    <!-- wifi报警 -->
    <div class="wifibj module_list" v-if="getTotal('wifibj')">
      <ModuleTitle
        label="WIFI报警"
        :end="getTotal('wifibj')"
        @more="handlePageMore('wifibj')"
      />
      <div class="box_list">
        <sensoryAlarm
          v-for="(item, index) in getList('wifibj')"
          :key="index"
          :itemList="item"
          @detailFn="handleVehicle(item, index)"
        />
      </div>
    </div>

    <!-- rfid报警 -->
    <div class="rfidbj module_list" v-if="getTotal('rfidbj')">
      <ModuleTitle
        label="RFID报警"
        :end="getTotal('rfidbj')"
        @more="handlePageMore('rfidbj')"
      />
      <div class="box_list">
        <sensoryAlarm
          v-for="(item, index) in getList('rfidbj')"
          :key="index"
          :itemList="item"
          @detailFn="handleVehicle(item, index)"
        />
      </div>
    </div>

    <!-- 电围报警 -->
    <div class="dwbj module_list" v-if="getTotal('dwbj')">
      <ModuleTitle
        label="电围报警"
        :end="getTotal('dwbj')"
        @more="handlePageMore('dwbj')"
      />
      <div class="box_list">
        <sensoryAlarm
          v-for="(item, index) in getList('dwbj')"
          :key="index"
          :itemList="item"
          @detailFn="handleVehicle(item, index)"
        />
      </div>
    </div>

    <!-- ETC报警 -->
    <div class="etcbj module_list" v-if="getTotal('etcbj')">
      <ModuleTitle
        label="ETC报警"
        :end="getTotal('etcbj')"
        @more="handlePageMore('etcbj')"
      />
      <div class="box_list">
        <sensoryAlarm
          v-for="(item, index) in getList('etcbj')"
          :key="index"
          :itemList="item"
          @detailFn="handleVehicle(item, index)"
        />
      </div>
    </div>

    <!-- 人脸抓拍 -->
    <div class="rlzp module_list" v-if="getTotal('rlzp')">
      <ModuleTitle
        label="人脸抓拍"
        :end="getTotal('rlzp')"
        @more="handlePageMore('rlzp')"
      />
      <div class="box_list">
        <facePage
          v-for="(item, index) in getList('rlzp')"
          type="1"
          :key="index"
          :itemList="item"
          @pageDetail="handleFaceDetails(item, index)"
        />
      </div>
    </div>

    <!-- 车辆抓拍 -->
    <div class="clzp module_list" v-if="getTotal('clzp')">
      <ModuleTitle
        label="车辆抓拍"
        :end="getTotal('clzp')"
        @more="handlePageMore('clzp')"
      />
      <div class="box_list">
        <facePage
          v-for="(item, index) in getList('clzp')"
          type="2"
          :key="index"
          :itemList="item"
          @pageDetail="handleVehicleDetails(item, index)"
        />
      </div>
    </div>

    <!-- 人体抓拍 -->
    <div class="rtzp module_list" v-if="getTotal('rtzp')">
      <ModuleTitle
        label="人体抓拍"
        :end="getTotal('rtzp')"
        @more="handlePageMore('rtzp')"
      />
      <div class="box_list">
        <facePage
          v-for="(item, index) in getList('rtzp')"
          :key="index"
          :itemList="item"
          @pageDetail="handleDetails(item, index, 'rtzp', 1)"
        />
      </div>
    </div>

    <!-- 非机动车抓拍 -->
    <div class="fjdczp module_list" v-if="getTotal('fjdczp')">
      <ModuleTitle
        label="非机动车抓拍"
        :end="getTotal('fjdczp')"
        @more="handlePageMore('fjdczp')"
      />
      <div class="box_list">
        <facePage
          v-for="(item, index) in getList('fjdczp')"
          :key="index"
          :itemList="item"
          @pageDetail="handleDetails(item, index, 'fjdczp', 2)"
        />
      </div>
    </div>

    <!-- MAC感知 -->
    <div class="macGz module_list" v-if="getTotal('macGz')">
      <ModuleTitle
        label="MAC感知"
        :end="getTotal('macGz')"
        @more="handlePageMore('macGz')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('macGz')"
          :key="index"
        >
          <perceptionPage
            :deviceType="2"
            :itemList="item"
            :key="index + 'mac'"
            fieldType="mac"
          />
        </div>
      </div>
    </div>

    <!-- RFID感知 -->
    <div class="rfidGz module_list" v-if="getTotal('rfidGz')">
      <ModuleTitle
        label="RFID感知"
        :end="getTotal('rfidGz')"
        @more="handlePageMore('rfidGz')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('rfidGz')"
          :key="index"
        >
          <perceptionPage
            :deviceType="3"
            :itemList="item"
            :key="index + 'rfidGz'"
            fieldType="rfidCode"
          />
        </div>
      </div>
    </div>
    <!-- 电围数据 -->
    <div class="dwGz module_list" v-if="getTotal('dwGz')">
      <ModuleTitle
        label="电围数据"
        :end="getTotal('dwGz')"
        @more="handlePageMore('dwGz')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('dwGz')"
          :key="index"
        >
          <perceptionPage
            :deviceType="4"
            :itemList="item"
            :key="index + 'dwGz'"
            fieldType="imsi"
          />
        </div>
      </div>
    </div>

    <!-- GPS数据 -->
    <div class="gpsGz module_list" v-if="getTotal('gpsGz')">
      <ModuleTitle
        label="GPS数据"
        :end="getTotal('gpsGz')"
        @more="handlePageMore('gpsGz')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('gpsGz')"
          :key="index"
        >
          <perceptionPage
            :deviceType="5"
            :itemList="item"
            :key="index + 'gpsGz'"
            fieldType="deviceId"
          />
        </div>
      </div>
    </div>
    <!-- etc感知 -->
    <div class="etcGz module_list" v-if="getTotal('etcGz')">
      <ModuleTitle
        label="ETC数据"
        :end="getTotal('etcGz')"
        @more="handlePageMore('etcGz')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('etcGz')"
          :key="index"
        >
          <perceptionPage
            :deviceType="5"
            :itemList="item"
            :key="index + 'etcGz'"
            fieldType="deviceId"
          />
        </div>
      </div>
    </div>
    <!-- Wi-Fi设备 -->
    <div class="wifiSb module_list" v-if="getTotal('wifiSb')">
      <ModuleTitle
        label="Wi-Fi设备"
        :end="getTotal('wifiSb')"
        @more="handlePageMore('wifiSb')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('wifiSb')"
          :key="index"
        >
          <equipmentPage
            :deviceType="7"
            :itemList="item"
            :key="index + 'wifiSb'"
          />
        </div>
      </div>
    </div>

    <!-- RFID设备 -->
    <div class="rfidSb module_list" v-if="getTotal('rfidSb')">
      <ModuleTitle
        label="RFID设备"
        :end="getTotal('rfidSb')"
        @more="handlePageMore('rfidSb')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('rfidSb')"
          :key="index"
        >
          <equipmentPage
            :deviceType="8"
            :itemList="item"
            :key="index + 'rfidSb'"
          />
        </div>
      </div>
    </div>

    <!-- gps设备 -->
    <div class="gpsSb module_list" v-if="getTotal('gpsSb')">
      <ModuleTitle
        label="GPS设备"
        :end="getTotal('gpsSb')"
        @more="handlePageMore('gpsSb')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('gpsSb')"
          :key="index"
        >
          <equipmentPage
            :deviceType="6"
            :itemList="item"
            :key="index + 'gpsSb'"
          />
        </div>
      </div>
    </div>

    <!-- etc设备 -->
    <div class="etcSb module_list" v-if="getTotal('etcSb')">
      <ModuleTitle
        label="ETC设备"
        :end="getTotal('etcSb')"
        @more="handlePageMore('etcSb')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('etcSb')"
          :key="index"
        >
          <equipmentPage
            :deviceType="16"
            :itemList="item"
            :key="index + 'etcSb'"
          />
        </div>
      </div>
    </div>

    <!-- 电围设备 -->
    <div class="dwSb module_list" v-if="getTotal('dwSb')">
      <ModuleTitle
        label="电围设备"
        :end="getTotal('dwSb')"
        @more="handlePageMore('dwSb')"
      />
      <div class="box_list">
        <div
          class="card-item"
          v-for="(item, index) in getList('dwSb')"
          :key="index"
        >
          <equipmentPage
            :deviceType="9"
            :itemList="item"
            :key="index + 'dwSb'"
          />
        </div>
      </div>
    </div>
    <!-- 用于普通数据展示 -->
    <ui-loading v-if="loading" />

    <!-- 资源类数据，当前版本不需要，先注释 -->
    <!-- <div
      class="module_list"
      v-for="(item, index) in notEmptyResourceList"
      :key="index"
    >
      <ModuleTitle
        :label="item.resource.resourceNameCn"
        :end="item.total"
        @more="handleResourceMore(item.resource)"
      />
      <PoliceOther
        :list="item.resourceResults"
        @checkDetail="handleResourceDetail($event, item.resource)"
      />
    </div> -->
    <!-- 资源类数据请求太漫长，独自一个loading -->
    <!-- 当loading展示时，隐藏resourceLoading，避免页面出现两个loading -->
    <!-- <div class="resource-loading" v-if="resourceLoading && !loading">
      <ui-loading>资源类数据加载中，请耐心等待...</ui-loading>
    </div> -->

    <ui-empty v-show="dataEmpty && !loading && !resourceLoading" />
    <details-face-modal
      v-if="faceShow"
      ref="faceDetail"
      @close="faceShow = false"
    />
    <details-vehicle-modal
      v-if="vehicleShow"
      ref="vehicleDetail"
      @close="vehicleShow = false"
    />
    <details-modal
      v-if="humanShow"
      ref="humanbody"
      @close="humanShow = false"
    />
    <!-- 人员报警 -->
    <alarmDetail
      v-if="detailShow"
      :tableList="moduleData.rybj.list"
      :alarmInfo="alarmInfo"
      :tableIndex="personnelIndex"
      ref="alarmDetail"
      @close="detailShow = false"
    />
    <!-- 车辆报警 -->
    <vehicleDetail
      v-if="vehicleDetail"
      :tableList="moduleData.clbj.list"
      :alarmInfo="vehicleInfo"
      :tableIndex="vehicleIndex"
      ref="vehicleDetailAlarm"
      @close="vehicleDetail = false"
    />
    <!-- 资源类数据详情 -->
    <PoliceDetail ref="resourceDetail" />
    <!-- 解析详情 -->
    <DetailMutiAnalysis
      v-if="showMutiDetail"
      ref="detailMutiAnalysisRef"
      :tableList="getList('dmtjx')"
      @close="showMutiDetail = false"
    ></DetailMutiAnalysis>
  </div>
</template>

<script>
import UiListCard from "@/components/ui-list-card";
import equipmentPage from "./equipmentPage.vue";
import perceptionPage from "./perceptionPage.vue";
import personnelAlarm from "./personnelAlarm.vue";
import vehicleAlarm from "./vehicleAlarm.vue";
import sensoryAlarm from "./sensoryAlarm.vue";
import alarmCard from "@/views/target-control/alarm-manager/people/components/alarm-card.vue";
import facePage from "./facePage.vue";
import detailsFaceModal from "@/components/detail/details-face-modal.vue";
import detailsVehicleModal from "@/components/detail/details-vehicle-modal.vue";
import detailsModal from "@/components/detail/details-modal.vue";
import alarmDetail from "@/views/target-control/alarm-manager/people/components/alarm-detail.vue";
import vehicleDetail from "@/views/target-control/alarm-manager/vehicle/components/vehicle-detail.vue";
import ModuleTitle from "./components/ModuleTitle.vue";
import PoliceOther from "@/views/wisdom-cloud-search/cloud-default-page/ordinary-search/components/police-other.vue";
import PoliceDetail from "@/views/wisdom-cloud-search/cloud-default-page/ordinary-search/components/police-detail.vue";
import ResultCard from "@/views/multimodal-analysis/multimodal-analysis-lib/components/result-card.vue";
import DetailMutiAnalysis from "@/components/detail/detail-muti-analysis.vue";
import {
  cameraDeviceSearch,
  faceCaptureSearch,
  vehicleCaptureSearch,
  humanCaptureSearch,
  nonmotorCaptureSearch,
  macSearch,
  rfidDataSearch,
  gpsDeviceSearch,
  etcDeviceSearch,
  etcDataSearch,
  baseStationDataSearch,
  gpsDataSearch,
  wifiDeviceSearch,
  rfidDeviceSearch,
  baseStationDeviceSearch,
  faceAlarmSearch,
  vehicleAlarmSearch,
  wifiAlarmSearch,
  rfidAlarmSearch,
  baseStationAlarmSearch,
  etcAlarmSearch,
  videoArchiveSearch,
  realNameArchiveSearch,
  vehicleArchiveSearch,
  resourceSearch,
  queryPoliceData,
  placeSearch,
} from "@/api/wisdom-cloud-search";
import { llmStructureSearchPageList } from "@/api/multimodal-analysis";
import { personBaseInfo } from "@/api/realNameFile";

const tagRegex = /<[^>]*>/g;

export default {
  name: "",
  components: {
    alarmCard,
    UiListCard,
    facePage,
    equipmentPage,
    perceptionPage,
    detailsFaceModal,
    detailsVehicleModal,
    detailsModal,
    personnelAlarm,
    vehicleAlarm,
    sensoryAlarm,
    alarmDetail,
    vehicleDetail,
    ModuleTitle,
    PoliceOther,
    PoliceDetail,
    ResultCard,
    DetailMutiAnalysis,
  },
  data() {
    return {
      faceShow: false,
      vehicleShow: false,
      humanShow: false,
      detailShow: false,
      vehicleDetail: false,
      showMutiDetail: false,
      alarmInfo: {},
      vehicleInfo: {},
      personnelIndex: 0,
      vehicleIndex: 0,
      loading: false,
      resourceLoading: false,
      moduleData: {
        dmtjx: {
          page: "/multimodal-analysis/multimodal-analysis-lib",
          req: llmStructureSearchPageList,
          getParams: (params) => ({
            searchType: 2,
            startTime: params.st,
            endTime: params.et,
          }),
          query: {
            searchType: 2,
          },
          total: 0,
          list: [],
        },
        rybj: {
          page: "/target-control/alarm-manager",
          req: faceAlarmSearch,
          total: 0,
          list: [],
        },
        clbj: {
          page: "/target-control/alarm-manager",
          req: vehicleAlarmSearch,
          total: 0,
          list: [],
        },
        wifibj: {
          page: "/target-control/alarm-manager",
          req: wifiAlarmSearch,
          total: 0,
          list: [],
        },
        rfidbj: {
          page: "/target-control/alarm-manager",
          req: rfidAlarmSearch,
          total: 0,
          list: [],
        },
        dwbj: {
          page: "/target-control/alarm-manager",
          req: baseStationAlarmSearch,
          total: 0,
          list: [],
        },
        etcbj: {
          page: "/target-control/alarm-manager",
          req: etcAlarmSearch,
          total: 0,
          list: [],
        },
        rlzp: {
          page: "/wisdom-cloud-search/search-center?sectionName=faceContent",
          req: faceCaptureSearch,
          total: 0,
          list: [],
          pageSize: 9,
        },
        clzp: {
          page: "/wisdom-cloud-search/search-center?sectionName=vehicleContent",
          req: vehicleCaptureSearch,
          total: 0,
          list: [],
          pageSize: 9,
        },
        rtzp: {
          page: "/wisdom-cloud-search/search-center?sectionName=humanBodyContent",
          req: humanCaptureSearch,
          total: 0,
          list: [],
          pageSize: 9,
        },
        fjdczp: {
          page: "/wisdom-cloud-search/search-center?sectionName=nonmotorVehicleContent",
          req: nonmotorCaptureSearch,
          total: 0,
          list: [],
          pageSize: 9,
        },
        macGz: {
          page: "/wisdom-cloud-search/search-center?sectionName=wifiContent",
          req: macSearch,
          total: 0,
          list: [],
        },
        rfidGz: {
          page: "/wisdom-cloud-search/search-center?sectionName=RFIDContent",
          req: rfidDataSearch,
          total: 0,
          list: [],
        },
        dwGz: {
          page: "/wisdom-cloud-search/search-center?sectionName=electricContent",
          req: baseStationDataSearch,
          total: 0,
          list: [],
        },
        etcGz: {
          page: "/wisdom-cloud-search/search-center?sectionName=etcContent",
          req: etcDataSearch,
          total: 0,
          list: [],
        },
        gpsGz: {
          page: "/wisdom-cloud-search/search-center?sectionName=gpsContent",
          req: gpsDataSearch,
          total: 0,
          list: [],
        },
        wifiSb: {
          page: "/holographic-archives/one-plane-one-archives/fell-device",
          req: wifiDeviceSearch,
          total: 0,
          list: [],
        },
        rfidSb: {
          page: "/holographic-archives/one-plane-one-archives/fell-device",
          req: rfidDeviceSearch,
          total: 0,
          list: [],
        },
        dwSb: {
          page: "/holographic-archives/one-plane-one-archives/fell-device",
          req: baseStationDeviceSearch,
          total: 0,
          list: [],
        },
        gpsSb: {
          page: "/holographic-archives/one-plane-one-archives/fell-device",
          req: gpsDeviceSearch,
          total: 0,
          list: [],
        },
        etcSb: {
          page: "/holographic-archives/one-plane-one-archives/fell-device",
          req: etcDeviceSearch,
          total: 0,
          list: [],
        },
        videoArchive: {
          page: "/holographic-archives/one-person-one-archives/video-archives",
          req: videoArchiveSearch,
          total: 0,
          list: [],
        },
        realNameArchive: {
          page: "/holographic-archives/one-person-one-archives/real-name-file",
          req: realNameArchiveSearch,
          total: 0,
          list: [],
        },
        vehicleArchive: {
          page: "/holographic-archives/one-vehicle-one-archives/vehicle-archives",
          req: vehicleArchiveSearch,
          total: 0,
          list: [],
        },
        deviceArchive: {
          page: "/holographic-archives/one-plane-one-archives/view-device",
          req: cameraDeviceSearch,
          total: 0,
          list: [],
        },
        // 场所档案
        placeArchive: {
          page: "/holographic-archives/place-archives",
          req: placeSearch,
          total: 0,
          list: [],
        },
      },
      queryInfo: {},
      // 资源类数据
      resourceList: [],
    };
  },
  computed: {
    notEmptyResourceList() {
      return this.resourceList.filter((item) => item.total);
    },
    // 空数据
    dataEmpty() {
      let empty = true;
      for (let key in this.moduleData) {
        if (this.moduleData[key].list.length) {
          empty = false;
        }
      }
      if (this.notEmptyResourceList.length) {
        empty = false;
      }
      return empty;
    },
  },
  methods: {
    /**
     * @description 获取每个模块的数量
     * @param {string} type 模块名称
     * @returns {number} 数量
     */
    getTotal(type) {
      return this.moduleData[type]?.total || 0;
    },

    /**
     * @description 获取每个模块的列表数据
     * @param {string} type 模块名称
     * @returns {array} 列表
     */
    getList(type) {
      return this.moduleData[type]?.list || [];
    },

    /**
     * @description: 获取页面数据
     * @param {object} query 查询条件
     */
    searchList(query, hideKeyList = []) {
      this.loading = true;
      this.queryInfo = query;
      //#region 搜索时先清空上次的搜索结果
      this.resourceList = [];
      hideKeyList.forEach((key) => {
        delete this.moduleData[key];
      });
      for (let key in this.moduleData) {
        this.moduleData[key].total = 0;
        this.moduleData[key].list = [];
      }
      //#endregion
      let params = {
        et: query.endDate,
        keywords: query.keyWords,
        pageNumber: 1,
        st: query.startDate,
      };
      let data = Object.values(this.moduleData);
      let reqs = data.map((item) => {
        return item.req({
          ...params,
          ...item.getParams?.(params),
          pageSize: item.pageSize || 5,
        });
      });
      Promise.all(reqs)
        .then((res) => {
          res.forEach((item, idx) => {
            if (item.data) {
              // 后端返回的entities不统一，有时为[]，有时为null
              data[idx].total = item.data.total || 0;
              data[idx].list = item.data.entities || [];
            }
          });
          // 视频档案可以切为实名档案，所以增加一个type字段
          this.moduleData.videoArchive.list.forEach((item) => {
            item.type = "videoArchive";
          });
        })
        .finally(() => {
          this.loading = false;
          // 当前版本不需要，先注释
          // this.getResourceData({ ...params, pageSize: 3 });
        });
    },

    /**
     * @description: 档案类型切换，实名与视频档案1:N，所以视频可以切实名，在实名中不可以切视频
     * @param {object} item 档案内容
     * @param {number} index 切换档案的序号
     */
    async changeCardHandle(item, index) {
      let list = this.moduleData.videoArchive.list;
      if (item.type === "videoArchive") {
        let obj = await this.personBaseInfo(item.realNameArchiveNo);
        list[index].type = "realNameArchive";
        list[index].xm = obj.xm;
        list[index].xbdm = obj.xbdm;
        list[index].mzdm = obj.mzdm;
        list[index].jgDzmc = obj.jgDzmc;
        list[index].gmsfhm = obj.gmsfhm;
        list[index].photos = obj.photos;
      } else {
        list[index].type = "videoArchive";
      }
      this.$forceUpdate();
    },

    /**
     * @description: 视频档案切实名档案获取基本信息
     * @param {archiveNo} string 档案好
     * @return {object} 实名档案基本信息
     */
    // 视频卡片切实名获取基本信息
    async personBaseInfo(archiveNo) {
      let info = {};
      let data = {
        archiveNo: archiveNo,
        dataType: 1,
      };
      await personBaseInfo(data).then((res) => {
        info = res.data;
        info.type = "people";
      });
      return info;
    },

    /**
     * @description: 获取资源类数据，接口返回数据时间较长，在其他接口后调用
     * @param {object} query 查询条件
     */
    getResourceData(query) {
      this.resourceLoading = true;
      resourceSearch(query)
        .then((res) => {
          this.resourceList = res.data || [];
        })
        .finally(() => {
          this.resourceLoading = false;
        });
    },

    /**
     * @description: 人员报警弹框详情
     * @param {object} item 数据
     * @param {number} index 点击的位置
     */
    handlePersonnel(item, index) {
      this.alarmInfo = item;
      this.personnelIndex = index;
      this.detailShow = true;
    },

    /**
     * @description: 车辆报警弹框详情
     * @param {object} item 数据
     * @param {number} index 点击的位置
     */
    handleVehicle(item, index) {
      this.vehicleInfo = item;
      this.vehicleIndex = index;
      this.vehicleDetail = true;
    },
    /**
     * @description: 多模态解析
     * @param {number} index 点击的位置
     */
    handleMutiAnalysis(index) {
      this.showMutiDetail = true;
      this.$nextTick(() => {
        this.$refs.detailMutiAnalysisRef.showList(index);
      });
    },

    /**
     * @description: 人脸详情弹框
     * @param {object} item 数据
     * @param {number} index 点击的位置
     */
    handleFaceDetails(item, index) {
      this.faceShow = true;
      this.$nextTick(() => {
        this.$refs.faceDetail.init(
          item,
          this.moduleData["rlzp"].list,
          index,
          5,
          1
        );
      });
    },

    /**
     * @description: 车辆详情弹框
     * @param {object} item 数据
     * @param {number} index 点击的位置
     */
    handleVehicleDetails(item, index) {
      this.vehicleShow = true;
      this.$nextTick(() => {
        this.$refs.vehicleDetail.init(
          item,
          this.moduleData["clzp"].list,
          index,
          1
        );
      });
    },

    /**
     * @description: 人体，非机动车
     * @param {object} item 数据
     * @param {number} index 点击的位置
     * @param {string} type 人体or非机动车
     * @param {number} num 标识， 人体 - 1，非机动车 - 2
     */
    //
    handleDetails(item, index, type, num) {
      this.humanShow = true;
      this.$nextTick(() => {
        this.$refs.humanbody.init(
          item,
          this.moduleData[type].list,
          index,
          num,
          1
        );
      });
    },

    /**
     * @description: 更多 - 跳转到相应页面
     * @param {string} type 模块类型
     */
    handlePageMore(type) {
      let query = {
        keyWords: this.queryInfo.keyWords,
        dateType: this.queryInfo.dateType,
        ...this.moduleData[type]?.query,
      };
      // 自定义时间，携带时间过去
      if (this.queryInfo.dateType === 4) {
        query.startDate = this.queryInfo.startDate;
        query.endDate = this.queryInfo.endDate;
      }
      if (type.endsWith("bj")) {
        let list = {
          rybj: { compareType: 1 },
          clbj: { compareType: 2 },
          wifibj: { compareType: 3 },
          rfidbj: { compareType: 5 },
          dwbj: { compareType: 4 },
          etcbj: { compareType: 6 },
        };
        query = {
          ...query,
          compareType: list[type].compareType,
          isCloudSearch: true,
        };
      } else {
        query = {
          ...query,
          pageName: type,
        };
      }
      this.$util.common.openNewWindow({
        path: `${this.moduleData[type].page}`,
        query: {
          ...query,
          noMenu: 1,
          noSearch: 1,
        },
      });
    },

    /**
     * @description: 更多（资源类）- 跳转到相应页面
     * @param {string} source 资源名称
     */
    handleResourceMore(source) {
      // 跳到分类搜索，为了避免其他设计问题，隐藏掉左侧菜单和树
      this.$util.common.openNewWindow({
        path: "/wisdom-cloud-search/search-center",
        query: {
          resourceName: source.resourceName,
          id: source.id,
          keyWords: this.queryInfo.keyWords,
          noMenu: 1,
          noSearch: 1,
        },
      });
    },

    /**
     * @description: 档案跳转详情
     * @param {object} item 档案信息
     * @param {string} type 档案类型
     */
    archiveDetail(item, type) {
      let name = "",
        query = {};
      // 处理带样式字符串
      switch (type) {
        case "videoArchive":
          if (item.type === "realNameArchive") {
            // 视频档切的实名档
            name = "people-archive";
            query = {
              archiveNo: item.realNameArchiveNo.replace(tagRegex, ""),
              source: "people",
              initialArchiveNo: item.realNameArchiveNo.replace(tagRegex, ""),
            };
          } else {
            name = "video-archive";
            // 视频档案需要archiveNo高亮展示，所以要处理一下
            query = {
              archiveNo: item.archiveNo.replace(tagRegex, ""),
              source: "video",
              initialArchiveNo: item.archiveNo.replace(tagRegex, ""),
            };
          }

          break;
        case "realNameArchive":
          name = "people-archive";
          query = {
            archiveNo: item.archiveNo.replace(tagRegex, ""),
            source: "people",
            initialArchiveNo: item.archiveNo.replace(tagRegex, ""),
          };
          break;
        case "deviceArchive":
          name = "device-archive";
          query = {
            archiveNo: item.deviceId.replace(tagRegex, ""),
          };
          break;
        case "vehicleArchive":
          name = "vehicle-archive";
          // 车辆档案需要plateNo高亮展示，所以要处理一下
          query = {
            archiveNo: JSON.stringify(item.archiveNo),
            plateNo: JSON.stringify(item.plateNo.replace(tagRegex, "")),
            source: "car",
            idcardNo: item.idcardNo,
          };
          break;
        case "placeArchive":
          // 如果使用place-archive路由会重定向一次，比如前边几个，直接使用place-dashboard
          name = "place-dashboard";
          query = {
            archiveNo: item.id,
            source: "place",
          };
          break;
      }
      const { href } = this.$router.resolve({
        name,
        query,
      });
      window.open(href, "_blank");
    },

    /**
     * @description: 资源类详情弹框
     * @param {object} event 资源类单数据
     * @param {object} item 资源类数据
     */
    handleResourceDetail(event, item) {
      let param = {
        resourceId: item.id,
        treeResourceName: item.resourceName,
        params: {
          id: {
            searchValue: event.id,
            searchType: 1,
          },
        },
      };
      queryPoliceData(param).then((res) => {
        this.$refs.resourceDetail.show(res.data);
      });
    },

    // 复制
    linkUrl(item) {
      let copyInput = document.createElement("input");
      document.body.appendChild(copyInput);
      copyInput.setAttribute("value", item);
      copyInput.select();
      document.execCommand("Copy");
      copyInput.remove();
    },
  },
};
</script>

<style lang="less" scoped>
.type_list {
  padding: 20px;
  overflow-y: auto;
  .module_list {
    margin-bottom: 30px;
  }
}
.alarnRow {
  width: ~"calc(20% - 10px)";
  margin-bottom: 12px;
  cursor: pointer;
}
// 视图
.box_list {
  display: flex;
  justify-content: start;
}
.card-item {
  width: calc(~"20% - 10px");
  margin-left: 10px;
  /deep/ .list-card {
    width: 100%;
  }
}
.empty-card-1 {
  width: calc(~"20% - 10px");
}
.resource-loading {
  position: relative;
  height: 100px;
}
</style>
