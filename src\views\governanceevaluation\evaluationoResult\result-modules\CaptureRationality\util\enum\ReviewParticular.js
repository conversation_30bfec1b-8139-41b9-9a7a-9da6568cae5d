// import global from '@/util/global'
// import {
//   defaultIconStaticsList
// } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js'
export const iconStaticsList = [
  {
    name: '设备总量',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-renliankakou',
    fileName: 'deviceNum',
  },
  {
    name: '实际检测设备数量',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'actualNum',
  },
  {
    name: '合格设备数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancebuhegeshebeishu',
    fileName: 'qualifiedNum',
  },
  {
    name: '不合格设备数量',
    count: '0',
    countStyle: {
      color: '#DD4826',
    },
    iconName: 'icon-jiancehegeshebeishu',
    fileName: 'unqualifiedNum',
  },
  {
    name: '昨日无抓拍数量',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'errorTodayNoData',
  },
  {
    name: '昨日活跃率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'todayActivePercent',
    type: 'percent', // 百分比
  },
];
