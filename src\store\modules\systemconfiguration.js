import axios from 'axios';
import user from '@/config/api/user';
export default {
  namespaced: true,
  state: {
    copyReceiveConfig: null, //复制的配置内容
    homeStyleType: '1', //首页风格配置
  },

  getters: {
    getCopyReceiveConfig(state) {
      return state.copyReceiveConfig;
    },
    getHomeStyleType(state) {
      return state.homeStyleType;
    },
  },
  mutations: {
    setCopyReceiveConfig(state, copyReceiveConfig) {
      state.copyReceiveConfig = copyReceiveConfig;
    },
    setHomeStyleType(state, homeStyleType) {
      state.homeStyleType = homeStyleType;
    },
  },
  actions: {
    setCopyReceiveConfig({ commit }, copyReceiveConfig) {
      commit('setCopyReceiveConfig', copyReceiveConfig);
    },
    async setHomeStyleType({ commit }) {
      try {
        const res = await axios.get(user.getParamDataByKeys, {
          params: {
            key: 'HOME_STYLE',
          },
        });
        commit('setHomeStyleType', res.data.data.paramValue);
      } catch (err) {
        console.error(err, 'err');
      }
    },
  },
};
