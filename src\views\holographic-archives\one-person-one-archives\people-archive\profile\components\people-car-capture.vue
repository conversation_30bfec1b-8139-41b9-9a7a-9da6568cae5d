<template>
  <ui-card :title="title" class="people-car-capture">
    <!-- <div slot="extra" class="more-btn" @click="peopleCarMore">更多<i class="iconfont icon-more"></i></div> -->
    <div class="people-car-capture-content">
      <Timeline class="people-car-capture-time-line">
        <TimelineItem v-for="(item, $index) in list" :key="$index">
          <img
            v-if="activeIndex === $index"
            src="@/assets/img/archives/icon-zhuapai-active.png"
            slot="dot"
            class="camera-icon"
            alt
          />
          <img
            v-else
            src="@/assets/img/archives/icon-zhuapai.png"
            slot="dot"
            class="camera-icon"
            alt
          />
          <div @click="changeCardHandle(item, $index)" class="line-card">
            <ui-image
              type="people"
              :src="item[options[type].leftPic]"
              class="line-card-img card-border-color"
            />
            <div
              :class="[
                activeIndex === $index
                  ? 'card-border-active-color card-active-bg'
                  : 'card-border-color card-bg',
              ]"
              class="line-card-content card-border-color"
            >
              <div class="line-card-name primary">{{ item.plateNo }}</div>
              <div class="line-card-time auxiliary-color">
                {{ item.absTime }}
              </div>
              <div class="line-card-address secondary-color ellipsis">
                {{ item.captureAddress }}
              </div>
            </div>
            <template v-if="type === 'people'">
              <div v-if="item.driverFlag === 1" class="pic-tag-warning-small">
                主驾
              </div>
              <div v-else class="pic-tag-primary-small">副驾</div>
            </template>
          </div>
        </TimelineItem>
      </Timeline>
      <template v-if="peopleCarCapture">
        <div class="car-img">
          <ui-image
            viewer
            type="people"
            :src="peopleCarCapture[options[type].mainPic]"
            class="card-border-color"
          />
        </div>
        <div class="info-card card-border-color card-bg">
          <div class="info-img-border card-border-color">
            <div
              v-for="(item, $index) in 4"
              :key="$index"
              class="horn border-primary"
            ></div>
            <div class="info-img">
              <ui-image
                viewer
                type="people"
                :src="peopleCarCapture[options[type].rightPic]"
              />
              <div
                v-if="peopleCarCapture.driverFlag === 1"
                class="pic-tag-warning"
              >
                主驾
              </div>
              <div v-else class="pic-tag-primary">副驾</div>
            </div>
          </div>
          <div class="info-ul">
            <div
              v-for="(item, $index) in options[type].field"
              :key="$index"
              class="info-li"
            >
              <div class="title-color">{{ item }}：</div>
              <!-- 车牌颜色 -->
              <template v-if="$index === 'plateColor'">
                <div class="text-color ellipsis">
                  {{
                    peopleCarCapture[$index]
                      | commonFiltering(licensePlateColorList)
                  }}
                </div>
              </template>
              <!-- 车辆颜色 -->
              <template v-else-if="$index === 'vehicleColor'">
                <div class="text-color ellipsis">
                  {{ peopleCarCapture[$index] | commonFiltering(vehicleColor) }}
                </div>
              </template>
              <!-- 车辆品牌 -->
              <template v-else-if="$index === 'vehicleBrand'">
                <div class="text-color ellipsis">
                  {{
                    peopleCarCapture[$index] | commonFiltering(vehicleBrandList)
                  }}
                </div>
              </template>
              <template v-else>
                <div
                  :class="
                    $index === 'plateNo' || $index === 'name' ? 'primary' : ''
                  "
                  class="text-color ellipsis"
                >
                  {{ peopleCarCapture[$index] }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </template>
    </div>
    <ui-modal
      v-model="visible"
      :r-width="dialogData.rWidth"
      :title="dialogData.title"
      :footerHide="true"
    >
      <Tabs type="card" :animated="false" class="btn-tabs">
        <TabPane label="12000123478854124536" name="tab1"> </TabPane>
        <TabPane label="12000123478854126677" name="tab2"> </TabPane>
        <TabPane label="12000123478854126677" name="tab3"> </TabPane>
      </Tabs>
      <div class="search">
        <Form class="">
          <div>
            <FormItem label="车牌号码:">
              <Input placeholder="请输入" />
            </FormItem>
            <FormItem label="抓拍时段:">
              <ui-tag-select>
                <ui-tag-select-option
                  v-for="(item, $index) in capturePeriodList"
                  :key="$index"
                  :name="item.name"
                >
                  {{ item.name }}
                </ui-tag-select-option>
              </ui-tag-select>
            </FormItem>
            <!-- <FormItem label="" class="custom-date">
                <DatePicker type="datetimerange" placeholder="请选择抓拍时段" class="ivu-date-picker-360"></DatePicker>
              </FormItem> -->
          </div>
          <FormItem class="btn-group">
            <Button type="primary">查询</Button>
            <Button>重置</Button>
          </FormItem>
        </Form>
      </div>
      <div class="portrait-capture-list">
        <PortraitCaptureCard
          v-for="(item, $index) in list"
          :key="$index"
          type="vehiclePlate"
          :data="item"
        />
      </div>
      <ui-page></ui-page>
    </ui-modal>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!list || !list.length) && !loading" />
  </ui-card>
</template>
<script>
import { mapGetters } from "vuex";
import PortraitCaptureCard from "./card/portrait-capture-card.vue";
export default {
  components: {
    PortraitCaptureCard,
  },
  props: {
    title: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "people",
    },
    list: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeIndex: 0,
      peopleCarCapture: "",
      options: {
        people: {
          leftPic: "traitImg",
          mainPic: "sceneImg",
          rightPic: "traitImg",
          field: {
            plateNo: "车牌号码",
            plateColor: "车牌颜色",
            vehicleBrand: "车辆品牌",
            vehicleColor: "车辆颜色",
            absTime: "抓拍时间",
            deviceName: "抓拍设备",
          },
        },
        vehicle: {
          leftPic: "carCapturePic",
          mainPic: "carCapturePic",
          rightPic: "perCapturePic",
          field: {
            name: "姓名",
            idCardNo: "身份证号",
            vid: "视频身份",
          },
        },
      },
      visible: false,
      dialogData: {
        title: "人车同拍",
        rWidth: 1036,
      },
      capturePeriodList: [
        { name: "近一天" },
        { name: "近三天" },
        { name: "近一周" },
        { name: "自定义" },
      ],
    };
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      vehicleColor: "dictionary/getBodyColorList", //车辆颜色
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
    }),
  },
  watch: {
    list() {
      if (this.list && this.list.length) {
        this.peopleCarCapture = this.list[0];
      }
    },
  },
  methods: {
    changeCardHandle(item, index) {
      this.activeIndex = index;
      this.peopleCarCapture = item;
    },
    peopleCarMore() {
      this.visible = true;
    },
  },
};
</script>
<style lang="less" scoped>
.people-car-capture {
  width: 100%;
  /deep/ .card-content {
    padding: 10px 20px 20px !important;
    display: flex;
  }
  /deep/.card-head {
    padding-right: 20px;
  }
  /deep/ .ui-image-div {
    img {
      max-width: 100% !important;
      max-height: 100% !important;
      height: auto !important;
      width: auto !important;
    }
  }
  .people-car-capture-content {
    width: 100%;
    display: flex;
    height: 390px;
    .people-car-capture-time-line {
      width: 22.5%;
      overflow: auto;
      .camera-icon {
        width: 24px;
      }
      .line-card {
        width: 100%;
        height: 70px;
        box-sizing: border-box;
        border: none;
        display: flex;
        position: relative;
        cursor: pointer;
        .line-card-img {
          width: 70px;
          height: 100%;
          box-sizing: border-box;
          border: 1px solid #fff;
        }
        .line-card-content {
          display: flex;
          flex: 1;
          padding: 5px 10px;
          flex-direction: column;
          overflow: hidden;
          border: 1px solid #fff;
          border-left: none;
          .line-card-name {
            font-size: 14px;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            line-height: 20px;
          }
          .line-card-time {
            font-size: 12px;
            line-height: 18px;
            margin-top: 2px;
          }
          .line-card-address {
            font-size: 12px;
            line-height: 16px;
            margin-top: 2px;
          }
        }
      }
    }
    .car-img {
      width: 55%;
      height: 100%;
      padding: 0 10px;
      & > img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border: 1px solid #fff;
        cursor: pointer;
      }
    }
    .info-card {
      width: 22.5%;
      height: 100%;
      padding: 20px;
      border: 1px solid #fff;
      .info-img-border {
        width: 150px;
        height: 150px;
        margin: 0 auto;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        border: 1px solid #fff;
        .horn {
          width: 8px;
          height: 8px;
          border: 1px solid #fff;
          border-right: none;
          border-bottom: none;
          position: absolute;
          top: -1px;
          left: -1px;
        }
        .horn:nth-child(2) {
          left: unset;
          right: -1px;
          border-right: 1px solid #fff;
          border-left: none;
        }
        .horn:nth-child(3) {
          top: unset;
          bottom: -1px;
          border-bottom: 1px solid #fff;
          border-top: none;
        }
        .horn:nth-child(4) {
          top: unset;
          bottom: -1px;
          right: -1px;
          left: unset;
          border-bottom: 1px solid #fff;
          border-top: none;
          border-right: 1px solid #fff;
          border-left: none;
        }
        .info-img {
          width: 140px;
          height: 140px;
          position: relative;
        }
      }
      .info-ul {
        display: flex;
        flex-direction: column;
        margin-top: 10px;
        .info-li {
          display: flex;
          align-items: center;
          margin-top: 10px;
          flex: 1;
          font-size: 14px;
          line-height: 20px;
          .title-color {
            font-weight: bold;
            font-family: "MicrosoftYaHei-Bold";
          }
          .text-color {
            flex: 1;
          }
          .primary {
            font-weight: bold;
            font-family: "MicrosoftYaHei-Bold";
          }
        }
      }
    }
  }
  /deep/ .ivu-modal-body {
    padding: 20px 30px 0 30px !important;
    max-height: 700px !important;
  }
  .search {
    margin-top: 6px;
    .ivu-form {
      display: flex;
      flex: 1;
      justify-content: space-between;
    }
    .custom-date {
      margin-left: -30px;
    }
    .btn-group {
      margin: 0;
    }
  }
  .portrait-capture-list {
    display: flex;
    margin: 0 -5px;
    max-height: 500px;
    overflow: auto;
    flex-wrap: wrap;
    .portrait-capture-card {
      width: calc(~"20% - 10px");
      margin: 0 5px 10px 5px;
    }
  }
}
</style>
