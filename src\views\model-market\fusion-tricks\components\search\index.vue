<!--
    * @FileDescription: 搜索
    * @Author: H
    * @Date: 2022/12/19
 * @LastEditors: duansen
 * @LastEditTime: 2024-07-24 11:56:33
 -->
<template>
  <div class="search_box">
    <div class="title">
      <p>{{ title }}</p>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <ul class="search_tab">
        <li
          v-for="(item, index) in tablist"
          :key="index"
          class="tabslist"
          @click="handleClickTab(index)"
          :class="{ active: tabIndex == index }"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="search_form">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="typeIndex && !tabIndex ? 60 : 85"
        >
          <template v-if="tabIndex == 0">
            <ul class="search_form_type">
              <li
                v-for="(item, index) in typeList"
                :key="index"
                class="typeTab"
                :class="{ typeActive: typeIndex == index }"
                @click="handleTypeClick(index)"
              >
                {{ item.name }}
              </li>
            </ul>
            <template v-if="typeIndex == 0">
              <FormItem label="身份证号:" prop="searchContent">
                <Input
                  v-model="formData.searchContent"
                  placeholder="请输入"
                ></Input>
              </FormItem>
            </template>
            <template v-else>
              <ui-upload-img
                ref="uploadImg"
                :algorithmType="algorithmType"
                :value="urlList"
                size="mini"
                @imgUrlChange="imgUrlChange"
              ></ui-upload-img>
              <div class="slider-content">
                <span class="similarity">相似度:</span>
                <Slider v-model="similarity"></Slider>
                <span>{{ similarity }}%</span>
              </div>
            </template>
            <template v-if="modalType == 'foothold'">
              <div class="wrapper">
                <p class="wrapper-title">落脚时间间隔:</p>
                <Input
                  v-model="formData.peerSecond"
                  placeholder="请输入"
                  class="wrapper-input"
                ></Input>
                <span class="unit">秒</span>
              </div>
              <div class="wrapper">
                <p class="wrapper-title">最少落脚次数:</p>
                <Input
                  v-model="formData.peerMinNumber"
                  placeholder="请输入"
                  class="wrapper-input"
                ></Input>
                <span class="unit">次</span>
              </div>
            </template>
          </template>
          <template v-if="tabIndex > 0">
            <template v-if="tabIndex == 1">
              <FormItem label="车辆号码" prop="plateNo">
                <Input v-model="formData.plateNo" placeholder="请输入"></Input>
              </FormItem>
            </template>
            <template v-if="tabIndex == 2">
              <FormItem label="RFID编码" prop="rfidCode">
                <Input v-model="formData.rfidCode" placeholder="请输入"></Input>
              </FormItem>
            </template>
            <template v-if="tabIndex == 3">
              <FormItem label="MAC地址" prop="macCode">
                <Input v-model="formData.macCode" placeholder="请输入"></Input>
              </FormItem>
            </template>
            <template v-if="tabIndex == 4">
              <FormItem label="ISMI编码" prop="ismiCode">
                <Input v-model="formData.ismiCode" placeholder="请输入"></Input>
              </FormItem>
            </template>
          </template>
          <!-- 当人员身份证号 && modalType为trajectory || foothold -->
          <!-- 非人员 -->
          <!-- 显示时间 -->
          <FormItem
            label="时  间:"
            prop="dateType"
            v-if="
              (tabIndex === 0 &&
                typeIndex === 0 &&
                (modalType == 'trajectory' || modalType == 'foothold')) ||
              tabIndex > 0
            "
          >
            <ui-quick-date
              style="width: 100%"
              ref="quickDateRef"
              v-model="formData.dateType"
              type="month"
              border
              @change="dataRangeHandler"
            />
          </FormItem>

          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div
      class="footer"
      :class="{ packArrow: packUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
    </div>
  </div>
</template>

<script>
import uiUploadImg from "@/components/ui-upload-img/index";
import { mapActions, mapGetters } from "vuex";
export default {
  name: "",
  props: {
    // 搜索标题
    title: {
      type: String,
      default: "搜索框",
    },
    // 融合战法类型
    modalType: {
      type: String,
      default: "",
    },
    // 默认选中的类型
    selectTabIndex: {
      type: Number,
      default: -1,
    },
  },
  components: {
    uiUploadImg,
  },
  data() {
    return {
      tablist: [
        { name: "人员" },
        { name: "车辆" },
        { name: "RFID" },
        { name: "Wi-Fi" },
        { name: "电围" },
      ],
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      tabIndex: 0,
      typeList: [{ name: "身份证号" }, { name: "人脸照片" }],
      typeIndex: 0,
      formData: {
        searchContent: "",
        plateNo: "",
        startDate: "",
        endDate: "",
        dateType: 2,
        peerSecond: "",
        peerMinNumber: "",
        rfidCode: "",
        macCode: "",
        ismiCode: "",
      },
      ruleValidate: {
        searchContent: [{ required: true, message: "请输入", trigger: "blur" }],
        plateNo: [{ required: true, message: "请输入", trigger: "blur" }],
        rfidCode: [{ required: true, message: "请输入", trigger: "blur" }],
        macCode: [{ required: true, message: "请输入", trigger: "blur" }],
        ismiCode: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      packUpDown: false,
      similarity: 85,
      algorithmType: "1",
      urlList: [],
    };
  },
  watch: {
    packUpDown: {
      handler(val) {
        this.$nextTick(() => {
          let box = document.querySelector(".search_condition");
          if (val) {
            box.style.overflow = "hidden";
          } else {
            setTimeout(() => {
              box.style.overflow = "inherit";
            }, 200);
          }
        });
      },
      immediate: true,
    },
    selectTabIndex: {
      handler(val) {
        this.tabIndex = val;
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
  },
  activated() {
    this.handleSimilar();
  },
  mounted() {
    this.dataRangeHandler(this.$refs.quickDateRef.getDate());
  },
  methods: {
    /**
     * @description: 活动轨迹 - 切换时间类型
     * @param {object} val 起止时间
     */
    dataRangeHandler(val) {
      this.formData.startDate = val.startDate;
      this.formData.endDate = val.endDate;
    },
    handleClickTab(index) {
      this.tabIndex = index;
      this.algorithmType = index + 1 + "";
      this.handleReset();
      this.$emit("tabClik");
    },
    handleTypeClick(index) {
      this.handleReset();
      this.typeIndex = index;
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
      this.$emit("packBox");
    },
    imgUrlChange(list) {
      this.urlList = list;
      this.$emit("imgUrlChange", this.urlList);
    },
    /**
     *
     */
    handleSearch() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.packUpDown = true;
          let params = { ...this.formData };
          let arr = [];
          if (this.urlList.length > 0) {
            this.urlList.forEach((ele) => {
              if (ele) {
                arr.push(ele.feature);
              }
            });
            params = {
              ...this.formData,
              features: arr,
              similarity: this.similarity,
            };
          }
          if (this.tabIndex == 0 && this.typeIndex == 1 && arr.length == 0) {
            //人员、人脸
            this.$Message.warning("请上传人脸照片！");
            return;
          }
          this.$emit("searchList", params, this.tabIndex, this.typeIndex);
        }
        // else {
        //     this.$emit('searchList', {}, this.tabIndex, this.typeIndex)
        // }
      });
    },
    // 获取相似度
    handleSimilar() {
      const { searchForPicturesDefaultSimilarity } = this.globalObj;
      this.similarity = Number(searchForPicturesDefaultSimilarity);
    },
    // 重置
    handleReset() {
      this.formData = {
        searchContent: "",
        plateNo: "",
        dateType: 2,
        rfidCode: "",
        macCode: "",
        ismiCode: "",
      };
      this.$nextTick(() => {
        this.dataRangeHandler(this.$refs.quickDateRef.getDate());
        this.$refs.quickDateRef.setDefaultDate(); // 清空日期组件的自定义时间，在下次选择自定义时回显为空
      });
      this.urlList = [];
      this.handleSimilar();
    },
  },
};
</script>

<style lang='less' scoped>
@import "../style/index";
.search_box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  // filter: blur(0px);
  .search_condition {
    max-height: 420px;
    transition: max-height 0.2s ease-out;
    // overflow: hidden;
    .search_tab {
      display: flex;
      justify-content: space-evenly;
      border-bottom: 1px solid #d3d7de;
      margin-bottom: 15px;
      .tabslist {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        width: 25%;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;
      }
      .active {
        color: #2c86f8;
        border-bottom: 3px solid #2c86f8;
      }
    }
    .search_form {
      padding: 0 15px 0 10px;
      &_type {
        display: flex;
        border: 1px solid #2c86f8;
        border-radius: 4px;
        margin-bottom: 15px;
        .typeTab {
          background: #ffffff;
          color: #2c86f8;
          width: 50%;
          height: 34px;
          text-align: center;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
        }
        .typeActive {
          background: #2c86f8;
          color: #fff;
        }
      }
      .btn-group {
        .btnwidth {
          width: 258px;
        }
      }
      .wrapper {
        display: flex;
        margin-bottom: 15px;
        align-items: center;
        .wrapper-title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          width: 95px;
        }
        .custom-time {
          height: 34px;
          line-height: 34px;
        }
        .wrapper-input {
          flex: 1;
        }
        &:first-child {
          .wrapper-title {
            width: 40px;
          }
        }
        .unit {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.8);
          margin-left: 5px;
        }
      }
    }
    .slider-content {
      margin: 15px 0;
      .similarity {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .search_condition-pack {
    max-height: 0px;
    transition: max-height 0.2s ease-out;
  }
  /deep/ .ivu-form-item {
    margin-bottom: 20px;
  }
  /deep/ .ivu-slider {
    flex: 1;
  }
  /deep/ .custom {
    width: 240px;
  }
  /deep/ .ivu-select-dropdown {
    left: 0 !important;
  }
  /deep/ .ivu-form-item-label {
    color: rgba(0, 0, 0, 0.45);
  }
}
</style>
