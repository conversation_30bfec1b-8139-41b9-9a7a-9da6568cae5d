<template>
  <ui-modal v-model="visible" title="查看考核记录" footer-hide :width="70">
    <ui-table class="auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #taskRunState="{ row }">
        <div>
          <span
            class="check-status"
            :class="[
              row.taskRunState == 0 ? 'bg-787878' : '',
              row.taskRunState == 1 ? 'bg-D66418' : '',
              row.taskRunState == 2 ? 'bg-success' : '',
              row.taskRunState == 3 ? 'bg-failed' : '',
              row.taskRunState == 4 ? 'bg-failed' : '',
            ]"
          >
            {{ row.taskRunStateText }}
          </span>
        </div>
      </template>
      <template #publishStatus="{ row }">
        <i-switch
          class="mr-md"
          :value="row.publishStatus"
          true-value="1"
          false-value="0"
          :before-change="() => handleBeforePublishStatus(row)"
          size="small"
        ></i-switch>
      </template>
      <template #action="{ row }">
        <div>
          <ui-btn-tip
            class="mr-md"
            v-if="row.taskRunState === '2'"
            :styles="{ color: 'var(--color-active)', 'font-size': '14px' }"
            :disabled="row.publishStatus !== VALUE_PUBLISH"
            icon="icon-chakanxiangqing"
            content="查看成绩"
            @click.native="view(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            v-if="row.taskRunState !== '1'"
            icon="icon-zhongxinzhihang"
            content="重新考核"
            class="mr-md"
            @click.native="again(row)"
          ></ui-btn-tip>
          <i v-else class="icon-font icon-zhongxinzhihang deleteicon mr-md" title="重新考核"></i>
        </div>
      </template>
    </ui-table>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {},
    recordParams: {},
  },
  data() {
    return {
      visible: false,
      loading: false,
      tableColumns: [
        {
          type: 'index',
          title: '排序',
          width: 50,
          align: 'center',
        },
        {
          title: '考核月份',
          key: 'examTimeStr',
        },
        {
          title: '考核状态',
          slot: 'taskRunState',
        },
        {
          title: '最新考核时间',
          key: 'lastExamEndTime',
        },
        {
          title: '发布状态',
          slot: 'publishStatus',
          align: 'center',
        },
        {
          title: '操作',
          slot: 'action',
          align: 'left',
          width: 100,
        },
      ],
      tableData: [],
      VALUE_PUBLISH: '1', // 发布
    };
  },
  created() {},
  methods: {
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.get(governanceevaluation.queryTaskHistoryList, {
          params: {
            examTaskId: this.recordParams.id,
          },
        });
        this.tableData = res.data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.init();
    },
    view(row) {
      if (row.publishStatus !== this.VALUE_PUBLISH) {
        return;
      }
      let routerName = this.recordParams.schemeType === 2 ? 'examinationScoreGAB' : 'examinationScore';
      this.$router.push({
        name: routerName,
        query: {
          time: row.lastExamStartTime,
          examTaskId: row.examTaskId,
        },
      });
    },
    again(row) {
      this.$UiConfirm({
        content: `您确认要立即执行此任务嘛?`,
        title: '警告',
      }).then(async () => {
        try {
          this.loading = true;
          const res = await this.$http.get(governanceevaluation.executeNow, {
            params: {
              jobId: row.jobId,
              examTaskHistoryId: row.id,
            },
          });
          this.$Message.success(res.data.msg);
        } catch (err) {
          console.log(err);
        } finally {
          this.loading = false;
        }
      });
    },
    // 启动、暂停
    async handleBeforePublishStatus(row) {
      return new Promise(async (resolve, reject) => {
        try {
          let params = {
            id: row.id,
            publishStatus: row.publishStatus === this.VALUE_PUBLISH ? '0' : '1',
          };
          this.loading = true;
          console.log(params);
          let res = await this.$http.post(governanceevaluation.cancelPublishTask, params);
          this.$Message.success(res.data.msg);
          resolve();
          await this.init();
        } catch (err) {
          console.log(err);
          reject();
        } finally {
          this.loading = false;
        }
      });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        this.init();
      }
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  height: 800px;
  display: flex;
  flex-direction: column;
}
.deleteicon {
  color: var(--color-btn-primary-disabled);
  cursor: no-drop;
}
</style>
