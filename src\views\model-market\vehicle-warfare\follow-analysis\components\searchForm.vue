<template>
  <div class="search_condition">
    <div class="search_form">
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">车牌号码:</p>
        </div>
        <div class="search_content">
          <Input v-model="queryParams.plateNo"></Input>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">开始时间:</p>
        </div>
        <div class="search_content daterange">
          <DatePicker
            v-model="queryParams.startTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            :clearable="false"
            placeholder="开始时间"
            transfer
            @on-change="timeChange"
          ></DatePicker>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">结束时间:</p>
        </div>
        <div class="search_content daterange">
          <DatePicker
            v-model="queryParams.endTime"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            :clearable="false"
            placeholder="结束时间"
            transfer
            @on-change="timeChange"
          ></DatePicker>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">出现频次:</p>
        </div>
        <div class="search_content">
          <span class="f-ib">{{ maxFre }}次</span>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">同行频次:</p>
        </div>
        <div class="search_content">
          <Input v-model="queryParams.compareCount"></Input>
        </div>
      </div>
      <div class="search_wrapper">
        <div class="search_title">
          <p class="search_strut">间隔时间:</p>
        </div>
        <div class="search_content">
          <Input
            v-model="queryParams.intervalTime"
            class="wrapper-input"
          ></Input
          ><span class="tip">&nbsp;秒</span>
        </div>
      </div>
      <div class="btn-group">
        <Button type="primary" class="btnwidth" @click="handleSearch"
          >查询</Button
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {
      queryParams: {
        plateNo: "",
        startTime: "",
        endTime: "",
        compareCount: 2,
        intervalTime: 120,
      },
      maxFre: 0,
    };
  },
  watch: {
    "queryParams.plateNo"(val) {
      this.$emit(
        "plateNoChange",
        val,
        this.$dayjs(this.queryParams.startTime).format("YYYY-MM-DD HH:mm:ss"),
        this.$dayjs(this.queryParams.endTime).format("YYYY-MM-DD HH:mm:ss")
      );
    },
  },
  mounted() {
    this.queryParams.startTime = this.$dayjs()
      .subtract(1, "day")
      .format("YYYY-MM-DD HH:mm:ss");
    this.queryParams.endTime = this.$dayjs().format("YYYY-MM-DD HH:mm:ss");
  },
  methods: {
    timeChange() {
      this.$emit(
        "plateNoChange",
        this.queryParams.plateNo,
        this.$dayjs(this.queryParams.startTime).format("YYYY-MM-DD HH:mm:ss"),
        this.$dayjs(this.queryParams.endTime).format("YYYY-MM-DD HH:mm:ss")
      );
    },
    // 查询
    handleSearch() {
      if (!this.queryParams.startTime || !this.queryParams.endTime) {
        this.$Message.error("请输入开始时间和结束时间");
        return;
      }
      this.queryParams.startTime = this.$dayjs(
        this.queryParams.startTime
      ).format("YYYY-MM-DD HH:mm:ss");
      this.queryParams.endTime = this.$dayjs(this.queryParams.endTime).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.$emit("search", this.queryParams);
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
</style>
