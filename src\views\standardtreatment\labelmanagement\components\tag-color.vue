<template>
  <div>
    <ul class="tag-list">
      <li
        class="tag-item pointer"
        v-for="(item, index) in tagList"
        :key="index"
        :style="{ background: item.color }"
        @click="tagCheckChange(item)"
      >
        <Icon type="md-checkmark font-white f-16" v-show="item.checkable" />
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  props: {
    // {color: '#0e8f0e', checkable: false, name: 'success'}
    defaultTagList: {
      type: Array,
      default: () => {
        return [
          { color: '#0E8F0E', checkable: false, name: '#0e8f0e' },
          { color: '#D66418', checkable: false, name: '#d66418' },
          { color: '#BC3C19', checkable: false, name: '#bc3c19' },
        ];
      },
    },
    // 可以多选
    mutipleChecked: {
      type: Boolean,
      default: false,
    },
    // 查看不让修改
    isView: {
      default: false,
    },
  },
  data() {
    return {
      tagList: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    tagCheckChange(one) {
      if (this.isView) return;
      this.$set(one, 'checkable', !one.checkable);
      if (!this.mutipleChecked) {
        this.tagList.forEach((item) => {
          if (one.checkable === true && item.name !== one.name) {
            item.checkable = false;
          }
        });
      }
      this.$emit('tagCheckChange', this.tagList);
    },
    /* ----------------------------------------- 自定义方法 ----------------------------------------- */
  },
  watch: {
    defaultTagList: {
      handler() {
        this.tagList = this.$util.common.deepCopy(this.defaultTagList);
      },
      deep: true,
      immediate: true,
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.tag-list {
  display: flex;
  .tag-item {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background: #fff;
    margin-right: 10px;
    border-radius: 2px;
    &:last-child {
      margin-right: 0;
    }
  }
}
</style>
