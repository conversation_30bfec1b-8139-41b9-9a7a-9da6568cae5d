<template>
  <ui-modal v-model="visible" title="已选择设备" width="50rem" @onCancel="handleReset" @query="handleSubmit">
    <div class="content-wrap auto-fill">
      <div class="mb-md">
        <Button type="primary" @click="bandleBatchRemove()">
          <span class="vt-middle">批量移除</span>
        </Button>
      </div>
      <ui-table
        reserveSelection
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      >
        <template slot="action" slot-scope="{ row }">
          <ui-btn-tip
            class="operatbtn"
            icon="icon-yichu1"
            content="移除"
            @click.native="handleRemove(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
  </ui-modal>
</template>
<script>
import equipmentassets from '../config/api/equipmentassets';
export default {
  props: {
    selectedData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      tableColumns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          minWidth: 170,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
          width: 80,
        },
      ],
      tableData: [],
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      allTableDatas: [],
      defaultStoreData: [],
    };
  },
  created() {},
  methods: {
    init() {
      this.defaultStoreData = [];
      this.visible = true;
    },
    handleReset() {},
    pageHandle() {
      this.tableData = this.$util.common.pagination(this.allTableDatas, this.pageData.pageNum, this.pageData.pageSize);
      this.pageData.totalCount = this.allTableDatas.length;
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.pageHandle();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.pageHandle();
    },
    storeSelectList(selection) {
      this.defaultStoreData = selection;
    },
    handleRemove(row) {
      let rmIndex = this.allTableDatas.findIndex((item) => {
        return item.id === row.id;
      });
      this.allTableDatas.splice(rmIndex, 1);
      this.pageHandle();
    },
    bandleBatchRemove() {
      this.defaultStoreData.forEach((item) => {
        this.handleRemove(item);
      });
    },
    handleSubmit() {
      this.visible = false;
      this.$emit('getSelectedList', this.allTableDatas);
    },
  },
  watch: {
    async selectedData(val) {
      if (val.length) {
        const ids = val.map((item) => item.id);
        const res = await this.$http.post(equipmentassets.queryDeviceInfoByIds, { ids: ids });
        this.allTableDatas = res.data.data;
        this.pageHandle();
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.content-wrap {
  height: 665px;
  padding: 0 20px 20px;
  background-color: var(--bg-content);
}
.ui-table {
  overflow: auto;
}
</style>
