<!--
    * @FileDescription: 落脚地分析搜索、对象信息
    * @Author: H
    * @Date: 2023/01/13
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <searchBox :title='searchType' modalType="foothold" @searchList="querySearch" ></searchBox>
        <div class="object-information" v-if="showlist">
            <div class="title">
                <p>{{ title }}</p>
                <Icon type="ios-close" @click="handleCancel" />
            </div>
            <div class="box-modal">
                <ul class="box-content" :class="{'overflow-box': objectMsgList.length > 3 }" v-infinite-scroll="load" infinite-scroll-distance='1'>
                    <li class="box-list" 
                        :class="{'active-box-list': objectIndex == index}"
                        @click="handleList(item, index)" 
                        v-for="(item, index) in objectMsgList" :key='index'>
                        <div class="content-top">
                            <div class="content-top-img">
                                <template v-if="item.photos && item.photos.length > 0">
                                    <img-list :dataObj="item" :index="index"></img-list>
                                </template>
                                <ui-image v-else viewer :src="typeIndex.tab==0 ? item.photo : item.photoUrl" />
                                <div v-if="item.score" class="similarity">
                                    <span>{{ getScore(item.score) }}%</span>
                                </div>
                            </div>
                            <div class="content-top-right">
                                <span class="ellipsis">
                                    <ui-icon :type="listIcon[typeIndex.tab][0]" :size="14"></ui-icon>
                                    <span class="block">{{ item[field[typeIndex.tab][0]] || '--' }}</span>
                                </span>
                                <span class="ellipsis">
                                    <ui-icon :type="listIcon[typeIndex.tab][1]" :size="14"></ui-icon>
                                    <span class="bule" :class="{'block': !item[field[typeIndex.tab][1]]}">{{ item[field[typeIndex.tab][1]] || '--' }}</span>
                                </span>
                                <span class="ellipsis">
                                    <ui-icon :type="listIcon[typeIndex.tab][2]" :size="14"></ui-icon>
                                    <span class="orange" :class="{'block': !item[field[typeIndex.tab][2]]}">{{ item[field[typeIndex.tab][2]] || '--' }}</span>
                                </span>
                            </div>
                        </div>
                        <div class="content-bottom">
                            <div class="iconList">
                                <!-- <opera-floor iconSec="icon-dangan2"></opera-floor> -->
                            </div>
                            <div class="analyseIcon" :class="{'activeAnaly':objectIndex === index}" @click="handleAnalyse($event, item, index)">
                                <ui-icon type="duoren" :size="14"></ui-icon>
                                <span>落脚点分析</span>
                            </div>
                        </div>
                    </li>
                    <ui-empty v-if="objectMsgList.length === 0 && loading== false"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                    <p class="loading" v-if="loadingText">加载中...</p> 
                    <p class="loading" v-if="noMore && objectMsgList.length !== 0">没有更多了</p>
                </ul>
            </div>
        </div>
        <foothold-analyse
            v-if="objModal" 
            :marginTop="removableTop"
            @analyse="handleSearchAnalyse"
            @cancel='objModal = false'></foothold-analyse>
    </div>
</template>

<script>
import { personTargetPageList, queryVehicleList } from '@/api/modelMarket';
import searchBox from './searchBox.vue';
import FootholdAnalyse from './footholdAnalyse.vue';
export default {
    name: '',
    props:{
        title: {
            type: String,
            default: '对象信息'
        },
    },
    components:{
        searchBox,
        FootholdAnalyse
    },
    data () {
        return {
            searchType: '落脚点分析',
            showlist: false,
            objectMsgList: [],
            loading: false,
            objectIndex: -1,
            detailRequest: {
                0: personTargetPageList, // 人脸
                1: queryVehicleList, // 车辆
            },
            listIcon:{
                0: ['xingming', 'shenfenzheng', 'camera'],
                1: ['chepai', 'xingming', 'shenfenzheng']
            },
            field:{
                0: [ 'xm', 'gmsfhm', 'archiveNo',],
                1: [ 'plateNo', 'ownerName', 'idcardNo'],
            },
            page: {
                "pageNumber": 1,
                "pageSize": 10, 
            },
            typeIndex: {
                tab: 0, 
                secTab: 0
            },
            total:0,
            loadingText: false,
            noMore: false,
            typeTag: {},
            top: 0,
            objModal: false,
            removableTop: 0,
            footholdObj: {}
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        // 高度
        objHeight() {
            setTimeout(() => {
                this.top = document.querySelector('.search_box').scrollHeight + 20;
            }, 210)
        },
        // 
        handlePackup() {
            this.objHeight();
        },
        // 点击列表
        handleList(item) {
            
        },
        load() {

        },
        /**
         * 查询
         * tab:0: 人员， 1：车辆， 2：RFID 3：WI-Fi 4: 电围
         * secTab 0：身份证号， 1：人脸照片
         */
        querySearch( item, tab, secTab ) {
            this.showlist = true;
            this.$emit('reset')
            this.typeTag = { tab, secTab };
            this.init(item, this.typeTag);
        },
        async init(item, typeTag) {
            this.typeIndex = typeTag;
            let res = {};
            if(typeTag.tab == 0) {
                res = [{
                    'id': 1,
                    'archiveNo': "371002200108212050",
                    'xm': "钱组上",
                    'gmsfhm' : '320102198703071905',
                    'photo': require('@/assets/img/mock/face.png'),
                }];
            }else{
                res = [
                    {
                        'id': 1,
                        'idcardNo': "371002200108212050",
                        'ownerName': "张伟",
                        'plateNo' : '苏A 34567',
                        'gmsfhm' : '******************',
                        'photoUrl': require('@/assets/img/mock/vehicle.png'),
                    },
                ];
            }
            this.objectMsgList = res;
        },
        // 落脚点分析
        handleAnalyse(event, item, index) {
            this.objectIndex = index;
            this.objModal = true;
            this.removableTop = event.clientY -283 - this.top;
            this.footholdObj = item;
            this.$emit('cutAnalyse')
        },
        // 开始分析
        handleSearchAnalyse(){
            this.$emit('searchAnalyse', this.footholdObj, this.typeTag);
            this.objModal = false;
        },
        // 关闭
        handleCancel() {
            this.showlist = false;
            this.$emit('reset');
        },
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
@import '../../components/style/boxContent';
.leftBox{
    position: absolute;
    top: 10px;
    left: 10px;
    .box-list{
        cursor: pointer;
    }
}
</style>
