<template>
  <div class="base-search">
    <div>
      <ui-label class="inline" :label="global.filedEnum.deviceId" :width="70">
        <Input
          v-model="searchData.deviceId"
          class="width-lg"
          :placeholder="`请输入${global.filedEnum.deviceId}`"
        ></Input>
      </ui-label>
      <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="70">
        <Input
          v-model="searchData.deviceName"
          class="width-lg"
          :placeholder="`请输入${global.filedEnum.deviceName}`"
        ></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="设备状态" :width="70">
        <Select class="width-sm" v-model="searchData.outcome" clearable placeholder="请选择检测结果">
          <Option :value="1" label="合格"></Option>
          <Option :value="2" label="不合格"></Option>
          <!-- <Option :value="3" label="无法检测"></Option> -->
        </Select>
      </ui-label>
      <ui-label :width="30" class="inline search-button" label=" ">
        <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
        <Button type="default" class="mr-lg" @click="resetSearchDataMx1(searchData, startSearch)">重置</Button>
      </ui-label>
    </div>
    <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
      <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
      <span class="inline ml-xs">导出</span>
    </Button>
  </div>
</template>
<script>
export default {
  props: {
    exportLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
      },
    };
  },
  async created() {
    this.reashfalf();
  },
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    resetSearchDataMx1() {
      this.reashfalf();
      this.$emit('startSearch', this.searchData);
    },
    reashfalf() {
      this.searchData = {
        deviceId: '',
        deviceName: '',
        outcome: '',
      };
    },
    getExport() {
      this.$emit('getExport');
    },
  },
};
</script>
<style lang="less" scoped>
.base-search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  padding-bottom: 10px;
  .date-picker-box {
    display: flex;
  }
  .input-width {
    width: 200px;
  }
}
.input-number-list {
  padding: 0px;
  width: 88px;
  /deep/ .ivu-input-number-controls-outside-btn {
    display: none;
  }
}
/deep/ .w150 {
  width: 150px;
}
</style>
