<template>
  <component :is="componentName" v-bind="attrs" v-on="$listeners"></component>
</template>
<script>
import * as VehicleUrlAvailable from '@/views/governanceevaluation/evaluationoResult/result-modules/UrlAvailableVehicle/util/enum/ReviewParticular.js';
import * as HistoryCompleteAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/util/enum/ReviewParticular.js';
import * as FaceUrlAvailable from '@/views/governanceevaluation/evaluationoResult/result-modules/UrlAvailableFace/util/enum/ReviewParticular.js';
import * as VideoPlayingAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoPlayingAccuracy/util/enum/ReviewParticular.js';
import * as VideoReadPromotionRate from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoReadPromotionRate/util/ReviewParticular.js';
import * as VideoHistoryPlayingAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryPlayingAccuracy/util/enum/ReviewParticular.js';
import * as VideoClockAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoClockAccuracy/util/enum/ReviewParticular.js';
import * as VideoOsdAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoOsdAccuracy/util/enum/ReviewParticular.js';
import * as videoOsdAccuracyPromotionRate from '@/views/governanceevaluation/evaluationoResult/result-modules/videoOsdAccuracyPromotionRate/util/ReviewParticular.js';
import * as VideoOsdClockAccuracy from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoOsdClockAccuracy/util/enum/ReviewParticular.js';
import * as VideoQualityPassRate from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoQualityPassRate/util/enum/ReviewParticular.js';
import * as VideoCommandImageOnlineRate from '@/views/governanceevaluation/evaluationoResult/result-modules/VideoCommandImageOnlineRate/util/enum/ReviewParticular.js';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
export default {
  inheritAttrs: false,
  mixins: [particularMixin],
  props: {
    value: {},
    moduleData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      componentName: null,
      recheckIndex: Object.freeze({
        VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: 'playing-recheck',
        VIDEO_PLAYING_ACCURACY: 'playing-recheck',
        VIDEO_READ_PROMOTION_RATE: 'playing-recheck',
        VIDEO_GENERAL_PLAYING_ACCURACY: 'playing-recheck',
        VIDEO_HISTORY_ACCURACY: 'history-recheck',
        VIDEO_GENERAL_HISTORY_ACCURACY: 'history-recheck',
        VIDEO_CLOCK_ACCURACY: 'clock-recheck',
        VIDEO_CLOCK_ACCURACY_PROMOTION_RATE: 'clock-recheck',
        VIDEO_GENERAL_CLOCK_ACCURACY: 'clock-recheck',
        VIDEO_OSD_ACCURACY: 'osd-accuracy-recheck',
        VIDEO_OSD_ACCURACY_PROMOTION_RATE: 'subtitle-recheck',
        VIDEO_GENERAL_OSD_ACCURACY: 'osd-accuracy-recheck',
        VIDEO_OSD_CLOCK_ACCURACY: 'subtitle-clock-recheck',
        VIDEO_GENERAL_OSD_CLOCK_ACCURACY: 'subtitle-clock-recheck',
        VIDEO_QUALITY_PASS_RATE: 'quality-recheck',
        VIDEO_QUALITY_PASS_RATE_RECHECK: 'quality-recheck',
        FACE_URL_AVAILABLE: 'url-available',
        FACE_SMALL_URL_AVAILABLE: 'small-url-available',
        FACE_FOCUS_URL_AVAILABLE: 'url-available',
        VEHICLE_URL_AVAILABLE: 'url-available',
        VEHICLE_URL_AVAILABLE_IMPORTANT: 'url-available',
        VIDEO_HISTORY_COMPLETE_ACCURACY: 'HistoryCompleteAccuracy',
        VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN: 'HistoryCompleteAccuracy',
        FOCUS_TRACK_URL_AVAILABLE: 'url-available',
      }),
      recheckBatchDevice: Object.freeze({
        VIDEO_COMMAND_IMAGE_ONLINE_RATE_IMPORTANT_SICHUAN: VideoCommandImageOnlineRate,
        VIDEO_PLAYING_ACCURACY: VideoPlayingAccuracy,
        VIDEO_READ_PROMOTION_RATE: VideoReadPromotionRate,
        VIDEO_GENERAL_PLAYING_ACCURACY: VideoPlayingAccuracy,
        VIDEO_HISTORY_ACCURACY: VideoHistoryPlayingAccuracy,
        VIDEO_GENERAL_HISTORY_ACCURACY: VideoHistoryPlayingAccuracy,
        VIDEO_CLOCK_ACCURACY: VideoClockAccuracy,
        VIDEO_CLOCK_ACCURACY_PROMOTION_RATE: VideoClockAccuracy,
        VIDEO_GENERAL_CLOCK_ACCURACY: VideoClockAccuracy,
        VIDEO_OSD_ACCURACY: VideoOsdAccuracy,
        VIDEO_OSD_ACCURACY_PROMOTION_RATE: videoOsdAccuracyPromotionRate,
        VIDEO_GENERAL_OSD_ACCURACY: VideoOsdAccuracy,
        VIDEO_OSD_CLOCK_ACCURACY: VideoOsdClockAccuracy,
        VIDEO_GENERAL_OSD_CLOCK_ACCURACY: VideoOsdClockAccuracy,
        VIDEO_QUALITY_PASS_RATE: VideoQualityPassRate,
        VIDEO_QUALITY_PASS_RATE_RECHECK: VideoQualityPassRate,
        FACE_URL_AVAILABLE: FaceUrlAvailable,
        FACE_SMALL_URL_AVAILABLE: FaceUrlAvailable,
        FACE_FOCUS_URL_AVAILABLE: FaceUrlAvailable,
        VEHICLE_URL_AVAILABLE: VehicleUrlAvailable,
        VEHICLE_URL_AVAILABLE_IMPORTANT: VehicleUrlAvailable,
        VIDEO_HISTORY_COMPLETE_ACCURACY: HistoryCompleteAccuracy,
        VIDEO_HISTORY_COMPLETE_ACCURACY_IMPORTANT_SICHUAN: HistoryCompleteAccuracy,
        FOCUS_TRACK_URL_AVAILABLE: FaceUrlAvailable,
      }),
      attrs: {},
      // 选择设备搜索条件
      formItemData: [],
      // 选择设备列表
      tableColumns: [],
    };
  },
  created() {},
  methods: {
    getAttrs() {
      return {
        ...this.$attrs,
        value: this.value,
        moduleData: this.moduleData,
        tableColumns: this.tableColumns,
        formItemData: this.formItemData,
      };
    },
  },
  watch: {
    value() {
      this.componentName = this.recheckIndex[this.moduleData.indexType];
      this.$nextTick(async () => {
        // [修改现场问题] 有传值优先用传值，不传就用配置
        this.formItemData = this.$attrs?.['form-item-data']
          ? this.$attrs['form-item-data']
          : this.recheckBatchDevice[this.moduleData.indexType].normalFormData;
        const tableColumns = this.recheckBatchDevice[this.moduleData.indexType].tableColumns;
        if (Array.isArray(tableColumns)) {
          this.tableColumns = tableColumns;
        } else {
          // 获取统计
          const data = await this.MixinGetStatInfo({ paramList: this.moduleData });
          this.tableColumns = this.recheckBatchDevice[this.moduleData.indexType].tableColumns({
            statisticalList: data,
          });
        }
        this.attrs = this.getAttrs();
      });
    },
  },
  components: {
    PlayingRecheck: require('./playing-recheck.vue').default,
    HistoryRecheck: require('./history-recheck.vue').default,
    ClockRecheck: require('./clock-recheck.vue').default,
    SubtitleRecheck: require('./subtitle-recheck.vue').default,
    OsdAccuracyRecheck: require('./osd-accuracy-recheck.vue').default,
    SubtitleClockRecheck: require('./subtitle-clock-recheck.vue').default,
    QualityRecheck: require('./quality-recheck.vue').default,
    UrlAvailable: require('./url-available.vue').default,
    SmallUrlAvailable: require('./small-url-available.vue').default,
    HistoryCompleteAccuracy: require('./history-complete-accuracy.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
