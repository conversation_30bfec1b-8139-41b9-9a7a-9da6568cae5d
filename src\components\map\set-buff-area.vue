<!--
 * @Date: 2024-09-14 15:18:29
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-18 14:18:44
 * @FilePath: \icbd-view\src\components\map\set-buff-area.vue
-->
<template>
  <div ref="infowindowBuff" class="infowindowBuff" id="infowindowBuff">
    <div class="buff-infowindow">
      <InputNumber
        :min="1"
        :max="500"
        v-model="buffDis"
        placeholder="范围"
        @on-change="changeBuffDis"
      />米
    </div>
  </div>
</template>
<script>
export default {
  name: "editBuffWin",
  data: function () {
    return {
      buffDis: 25, //缓冲区半径
    };
  },
  created: function () {
    //进入模块初始化缓冲区域半径默认值
    this.buffDis = 25;
  },
  methods: {
    //设置缓冲区域半径
    changeBuffDis: function () {
      var re = /^[1-9]\d*$/;
      if (!re.test(this.buffDis)) {
        this.$Message.warning("请输入正整数");
        return;
      }
      Toolkits.FnByShake(200, () => {
        if (parseInt(this.buffDis) < 5) {
          this.$Message.warning("最小输入距离为5!");
          return false;
        }
        if (parseInt(this.buffDis) > 500) {
          this.$Message.warning("最大输入距离为500!");
          this.buffDis = 500;
        }
        this.buffDis && this.$emit("changeBuffDis", parseInt(this.buffDis));
      });
    },
  },
};
</script>
<style lang="less">
.buff-infowindow {
  width: 95px;
  background: #fff;
  border-radius: 4px;
  display: flex;
  align-items: center;
}
</style>
