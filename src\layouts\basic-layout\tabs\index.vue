<template>
  <div :class="classes" :style="styles" class="i-layout-tabs">
    <div class="i-layout-tabs-main">
      <Tabs
        ref="Tabs"
        :value="current"
        :animated="false"
        type="card"
      >
        <!--eslint-disable-->
        <TabPane
          v-for="(page, $index) in opened"
          :key="Math.random($index, $index + 1)"
          context-menu
          :label="(h) => tabLabel(h, page)"
          :name="page.fullPath"
        />
      </Tabs>
    </div>
  </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from "vuex";
import tTitle from "../mixins/translate-title";

import Setting from "@/libs/configuration/setting";

import { getAllSiderMenu } from "@/libs/system";

export default {
  name: `iTabs`,
  mixins: [tTitle],
  computed: {
    ...mapState("admin/layout", [
      "showTabsIcon",
      "tabsFix",
      "tabsReload",
      "tabsOrder",
      "headerFix",
      "headerStick",
      "isMobile",
      "menuCollapse",
    ]),
    ...mapGetters("admin/menu", ["hideSider"]),
    classes() {
      return {
        "i-layout-tabs-fix": this.tabsFix,
      };
    },
    isHeaderStick() {
      return this.hideSider;
    },
    styles() {
      const style = {};
      if (this.tabsFix && !this.headerFix) {
        style.top = `${64 - this.scrollTop}px`;
      }

      const menuWidth = this.isHeaderStick
        ? 0
        : this.menuCollapse
        ? 80
        : Setting.menuSideWidth;
      if (!this.isMobile && this.tabsFix) {
        style.width = `calc(100% - ${menuWidth}px)`;
        style.left = `${menuWidth}px`;
      }

      return style;
    },
  },
  data() {
    return {
      // 得到所有侧边菜单，并转为平级，查询图标用
      allSiderMenu: getAllSiderMenu([]),
      scrollTop: 0,
      current: '',
      opened: []
    };
  },
  watch: {
    $route: {
      handler (to, from) {
        this.opened = [to]
        this.current = to.fullPath
      },
      immediate:true,
    }
  },
  mounted() {
    this.$refs.Tabs.scrollable = true;
    document.addEventListener("scroll", this.handleScroll, { passive: true });
    this.handleScroll();
  },
  beforeDestroy() {
    document.removeEventListener("scroll", this.handleScroll);
  },
  methods: {
    tabLabel(h, page) {
      const title = h(
        "span",
        this.tTitle(page.query.curName) ||
          this.tTitle(page.text) || page.meta.title || 
          "未命名"
      );
      const slot = [];
      if (this.showTabsIcon) {
        const currentMenu = {};
        let icon;
        if (currentMenu.icon) {
          icon = h("Icon", {
            props: {
              type: currentMenu.icon,
            },
          });
        } else if (currentMenu.custom) {
          icon = h("Icon", {
            props: {
              custom: currentMenu.custom,
            },
          });
        } else if (currentMenu.img) {
          icon = h("img", {
            attrs: {
              src: currentMenu.img,
            },
          });
        }
        if (icon) slot.push(icon);
        slot.push(title);
      } else {
        slot.push(title);
      }

      return h(
        "div",
        {
          class: "i-layout-tabs-title",
        },
        slot
      );
    },
    handleScroll() {
      if (this.tabsFix && !this.headerFix) {
        const scrollTop =
          document.body.scrollTop + document.documentElement.scrollTop;
        this.scrollTop = scrollTop > 64 ? 64 : scrollTop;
      }
    },
  },
};
</script>
