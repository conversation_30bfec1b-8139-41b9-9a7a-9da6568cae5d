<template>
  <div class="base-search">
    <div class="base-search-wrap">
      <slot name="search">
        <ui-label
          class="inline mr-lg mb-sm"
          :label="item.label"
          v-for="(item, index) in formItemData"
          :key="item.key + '-' + index"
        >
          <Input
            v-if="item.type === 'input'"
            v-model="innerFormData[item.key]"
            :class="[formItemSize, item.width ? 'reset-width' : '']"
            clearable
            :placeholder="'请输入' + item.label"
            :style="{ '--reset-width': `${item.width / 192}rem` }"
            @keyup.enter.native="startSearch"
          >
          </Input>
          <Select
            v-if="item.type === 'select'"
            :class="[formItemSize, item.width ? 'reset-width' : '']"
            v-model="innerFormData[item.key]"
            :placeholder="'请选择' + item.label"
            :disabled="item.disabled && item.disabled(item)"
            :multiple="!!item.selectMutiple"
            :max-tag-count="item.maxTagCount"
            :filterable="!!item.filterable"
            :style="{ '--reset-width': `${item.width / 192}rem` }"
            clearable
          >
            <Option
              v-for="(opt, ind) in item.options || []"
              :value="opt.value"
              :label="opt.label"
              :key="`${ind}${opt.value}`"
            ></Option>
          </Select>
          <api-organization-tree
            v-if="item.type === 'org'"
            ref="apiOrgTree"
            :custorm-node="true"
            :custorm-node-data="custormNodeData"
            :select-tree="formData"
            @selectedTree="
              (val) => {
                selectedOrgTree(val, item.key);
              }
            "
            :placeholder="'请选择' + item.label"
          >
          </api-organization-tree>
          <api-area-tree
            v-if="item.type === 'region'"
            ref="apiAreaTree"
            :select-tree="formData"
            @selectedTree="
              (area) => {
                selectedArea(area, item.key);
              }
            "
            :placeholder="'请选择' + item.label"
          ></api-area-tree>
          <!-- :device-ids="formData[item.key]" -->
          <select-camera
            v-if="item.type === 'camera'"
            :device-ids="formData[item.key] || []"
            @pushCamera="
              (list) => {
                pushCamera(list, item.key);
              }
            "
            class="select-camera"
            :width="item.width"
          ></select-camera>
          <div class="inline" v-if="item.type === 'start-end-time'">
            <slot :name="item.type">
              <DatePicker
                class="inline"
                v-model="innerFormData[item.startKey]"
                type="datetime"
                placeholder="请选择开始时间"
                confirm
                @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, innerFormData, item.startKey)"
              ></DatePicker>
              <span class="ml-sm mr-sm">--</span>
              <DatePicker
                class="inline"
                v-model="innerFormData[item.endKey]"
                type="datetime"
                placeholder="请选择结束时间"
                confirm
                @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, innerFormData, item.endKey)"
              ></DatePicker>
            </slot>
          </div>
          <div class="inline" v-if="item.type === 'start-end-num'">
            <slot :name="item.type">
              <InputNumber
                :precision="0"
                :max="Number.MAX_SAFE_INTEGER"
                v-model.number="innerFormData[item.startKey]"
                class="width-mini"
                :class="{ 'reset-width': item.width }"
                :style="{ '--reset-width': `${item.width / 192}rem` }"
                placeholder="请输入"
                clearable
              >
              </InputNumber>
              <span class="ml-sm mr-sm">--</span>
              <InputNumber
                :precision="0"
                :max="Number.MAX_SAFE_INTEGER"
                v-model.number="innerFormData[item.endKey]"
                class="width-mini"
                :class="{ 'reset-width': item.width }"
                :style="{ '--reset-width': `${item.width / 192}rem` }"
                placeholder="请输入"
                clearable
              >
              </InputNumber>
            </slot>
          </div>
        </ui-label>
      </slot>
      <slot>
        <span class="mb-sm">
          <Button v-if="formItemData.length" type="primary" class="mr-sm" @click="startSearch">查询</Button>
          <Button v-if="formItemData.length" type="default" @click="resetSearchDataMx(innerFormData, reset)">重置</Button>
        </span>
      </slot>
      <div class="search-btn mb-sm">
        <slot name="otherButton"></slot>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'DynamicCondition',
  props: {
    /**
     * @example
     * formItemData: [
     *         {
     *           type: 'input',
     *           key: 'searchValue',
     *           label: '关键词',
     *         },
     *         {
     *           type: 'select',
     *           key: 'qualified',
     *           label: '达标情况',
     *           placeholder:'请选择达标情况',
     *           options: [
     *             {value: '1',label:'达标'},
     *             {value: '2',label:'不达标'},
     *           ]
     *         },
     *         {
     *           type: 'camera',
     *           label: '摄像机',
     *           key:'camera'
     *         },
     *         {
     *           type: 'start-end-time',
     *           label: '抓拍时间',
     *           startKey:'camera',
     *           endKey:'camera',
     *         },
     *       ],
     */
    formItemData: {
      required: true,
      type: Array,
      default: () => [],
    },
    /**
     * 和formItemData中的key对应
     * @example
     * formData: {
     *         searchValue: '',
     *         qualified: '',
     *       },
     */
    formData: {
      required: true,
      type: Object,
      default: () => {},
    },
    formItemSize: {
      type: String,
      default: 'width-md',
    },
    needResetCopySearch: {},
  },
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      selectTree: {
        regionCode: '',
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      innerFormData: this.formData
    };
  },
  created() {
    this.copySearchDataMx(this.innerFormData);
  },
  mounted() {},
  methods: {
    startSearch() {
      this.$emit('search', this.innerFormData);
    },
    resetClick() {
      this.resetSearchDataMx(this.innerFormData);
    },
    selectedOrgTree(val, key) {
      this.innerFormData[key] = val.orgCode;
    },
    selectedArea(area, key) {
      this.innerFormData[key] = area.regionCode;
    },
    pushCamera(list, key) {
      this.innerFormData[key] = list.map((row) => {
        return row.deviceId;
      });
    },
    reset() {
      this.$emit('reset', this.innerFormData);
    },
  },
  watch: {
    // 重置查询条件
    needResetCopySearch: {
      handler() {
        this.copySearch = null;
        this.copySearchDataMx(this.innerFormData);
      },
      deep: true,
    },
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    SelectCamera: require('@/components/select-camera.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-search {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  &-wrap {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
  }
  .date-picker-box {
    display: flex;
  }
  .search-btn {
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }
  .select-camera {
    display: inline-block;
    vertical-align: middle;
    color: #fff;
  }
  .other-button {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    flex-wrap: nowrap;
  }
  @{_deep}.ivu-select-selection {
    > div {
      display: flex;
    }
  }
  .reset-width {
    width: var(--reset-width) !important;
  }
}
</style>
