<template>
	<ui-modal v-model="options.open" :mask="false" title="添加帧标记" :transfer="true" :z-index="10000" :r-width="1080" @onOk="comfirmHandle" class="add-frameMark">
		<div class="imgBox">
			<img class="image" :src="options.src" alt="" />
		</div>
		<Form ref="form" :model="form" :rules="ruleForm" inline>
			<FormItem label="标记名称:" prop="name">
				<Input v-model="form.name" :maxlength="50" placeholder="请输入" class="width-md" />
			</FormItem>
			<FormItem label="标记颜色:" prop="color">
				<color-select :currentColor.sync="form.color"></color-select>
			</FormItem>
			<FormItem label="标记时间:" prop="markTime">
				<DatePicker v-model="form.markTime" readonly :clearable="false" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="请选择时间" transfer></DatePicker>
			</FormItem>
			<FormItem label="备注:" prop="remark">
				<Input v-model="form.remark" placeholder="请输入" class="width-md" />
			</FormItem>
		</Form>
	</ui-modal>
</template>
<script>
	import colorSelect from '@/components/color-select.vue'
	export default {
		components: {
			colorSelect
		},
		props: {
			options: {
				type: Object,
				default: () => { }
			}
		},
		data() {
			return {
				form: {
					name: '',
					color: '#EB4B4B',
					remark: '',
					markTime: ''
				},
				ruleForm: {
					name: [{ required: true, message: '请输入', trigger: 'blur' }]
				}
			}
		},
		watch: {
			'options.open': {
				handler(val) {
					if (val) {
						this.form = {
							name: '',
							color: '#EB4B4B',
							markTime: this.$dayjs(this.options.absTime).format()
						}
					}
				}
			}
		},
		methods: {
			// 新增
			comfirmHandle() {
				this.$refs.form.validate(valid => {
					if (valid) {
						let params = {
							markTitle: this.form.name,
							markColor: this.form.color,
							remark: this.form.remark,
							markTime: this.form.markTime,
							...this.options
						}
						this.$emit('added-frameMark', params)
					}
				})
			},
			handleSelected(item) {
				this.form.color = item.value
			}
		}
	}
</script>
<style lang="less" scoped>
.imgBox {
	width: 100%;
	height: 500px;
	display: flex;
	align-items: center;
	justify-content: center;
}
.image {
	max-width: 100%;
	max-height: 100%;
}
.width-md {
	width: 200px;
}
.ivu-form {
	margin-top: 15px;
	display: flex;
}
.ivu-form-item {
	margin-bottom: 0;
	display: flex;
}
/deep/ .ivu-modal-content {
	margin: 0 auto;
}
</style>
