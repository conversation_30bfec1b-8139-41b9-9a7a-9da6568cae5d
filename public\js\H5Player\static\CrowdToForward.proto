/**
 * @Author:      mayih<PERSON>
 * @DateTime:    2017-03-07 11:37:18
 * @Email:       <EMAIL> 
 */

package INF;

message CrossLineMessage
{
	message Point
	{
        required int32 x = 1;
        required int32 y = 2;
	}
	message Line
	{
	    required Point start = 1;
	    required Point end   = 2;
	}
	message Rects
    {
        required Point start = 1;
        required Point end   = 2;
    }

    required int32 up_number   = 1;    //up person number
    required int32 down_number = 2;    //down person number
    repeated Rects rects       = 3;    // trackrects ;

	required Line line         = 4;             
	required Line arrow        = 5;
}

message CrowdMessage
{
    enum EventType
    {
		EVENT_CROWD  = 1;        /// < 过密
		EVENT_GATHER = 2;        /// < 聚集
		EVENT_STAND  = 3;        /// < 滞留
		EVENT_RETRO  = 4;        /// < 逆行
		EVENT_CHAOS  = 5;        /// < 混乱
    }

    enum EventStatus
    {
		EVENT_START    = 1;
		EVENT_STOP     = 2;
		EVENT_CONTINUE = 3;
    }

    message Point
    {
        required int32 x = 1;
        required int32 y = 2;
    }

    message EventRegion
    {
        repeated Point point = 1;
    }

    message CrowdEvent
    {
		required EventType event_type     = 1;
		required int32     person_number  = 2;
		required int64     start_time     = 3;
		required int64     stop_time      = 4;
		required int64     update_time    = 5;
		required EventStatus lift_flag    = 6;
		repeated EventRegion event_region = 7;
    }
    required int32 crowd_number    = 1;            /// < 当前场景人数
    required int32 safe_index      = 2;            /// < 当前场景安全指数
    required float density         = 3;            /// < 当前场景的人群密度
    repeated CrowdEvent event_list = 4;            /// < 当前发生的事件列表
    repeated bytes density_map     = 5;            /// < 密度分布向量
    repeated bytes stand_map       = 6;            /// < 滞留统计向量
    optional int64 frame_index     = 7;
    repeated int32 trace_array     = 8;
    message Parames
    {

    }
}
