<template>
  <div class="exam-score-examination-tendency height-full">
    <examinationtendency></examinationtendency>
  </div>
</template>
<script>
export default {
  name: 'examinationTendency',
  props: {},
  data() {
    return {};
  },
  provide() {
    return {
      examSchemeType: 1, //本级考核成绩
    };
  },
  methods: {},
  mounted() {},
  components: {
    examinationtendency: require('@/views/governanceevaluation/examinationresult/examinationtendency/index.vue')
      .default,
  },
};
</script>
<style lang="less" scoped></style>
