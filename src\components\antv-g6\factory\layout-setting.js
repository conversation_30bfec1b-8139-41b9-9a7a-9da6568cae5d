export const getLayoutConfig = (layoutType = "gForce") => {
  switch (layoutType) {
    case "force":
      return {
        type: "force",
        linkDistance: 200,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: true,
      };
    case "force2":
      return {
        type: "force2",
        linkDistance: 200,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: true,
      };
    case "gForce":
      return {
        type: "gForce", // 只有force这个布局切换的时候不会卡顿,force2 适合关系试图,gForce 适合首次布局的力图
        linkDistance: 200,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: true,
      };
    case "radial":
      return {
        type: layoutType,
        linkDistance: 200,
        unitRadius: 200,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: false,
      };
    case "concentric":
      return {
        type: layoutType,
        equidistant: true,
        linkDistance: 100,
        // maxLevelDiff: 0.5,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: false,
        // maxLevelDiff: 10, // 可选
      };
    case "circular":
      return {
        type: layoutType,
        linkDistance: 100,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: false,
      };
    case "grid":
      return {
        type: layoutType,
        nodeSize: 60,
        preventOverlap: true,
        gpuEnabled: false,
      };
    case "avoidlap":
      return {};
  }
};

export class LayoutSetting {
  graph = null;
  constructor({ graph }) {
    this.graph = graph;
  }

  updateLayout(layoutType) {
    this.graph.updateLayout(getLayoutConfig(layoutType));
  }
}
