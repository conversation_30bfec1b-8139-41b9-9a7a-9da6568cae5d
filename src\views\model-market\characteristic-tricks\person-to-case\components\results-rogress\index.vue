<!--
    * @FileDescription: 收缩框
-->
<template>
    <div class="results-rogress-box">
        <img :src="resultsLoadGif" />
        <Progress :percent="percent" :stroke-color="['#5BDBFF', '#2C86F8']" />
        <div class="name">碰撞分析中...</div>
    </div>
</template>

<script>

export default {
    props: {
        params: {
            type: Object,
            default: () => ({})
        },
        title: {
            type: String,
        },
    },
    mounted() {
        setTimeout(() => {
            this.$emit('onResultData', true)
        }, 2000);
    },
    data() {
        return {
            resultsLoadGif: require('@/assets/img/model/people-to-case/pzfx.gif'),
            percent: 60
        }
    },
    methods: {
    }
}
</script>

<style lang='less' scoped>
.results-rogress-box {
    height: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    img {
        width: 50px;
        height: 50px;
    }
}
</style>
