<!--
    * @FileDescription: mac报警
    * @Author: H
    * @Date: 2024/06/17
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-17 16:42:48
 -->
<template>
  <div class="container">
    <!-- 搜索 -->
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="alarm"
      :compareType="compareType"
      :radioList="radioList"
      :taskList="taskList"
    >
      <query ref="slotQuery" :compareType="compareType" />
    </searchForm>
    <!-- 操作栏 -->
    <div class="operate" v-show="tableList.length">
      <Checkbox
        v-model="allCheckStatus"
        :indeterminate="indeterminate"
        @click.native="allChecked()"
        >全选</Checkbox
      >
      <Dropdown style="margin-left: 20px" @on-click="batchStatus">
        <Button type="primary">
          批量处理
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="1">设为有效</DropdownItem>
          <DropdownItem :name="2">设为无效</DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div>
    <!-- 列表 -->
    <div class="card-content">
      <div class="list">
        <sensoryCard
          class="alarnRow"
          v-for="(item, index) in tableList"
          :key="index"
          @collection="collection"
          @refresh="tableListFn()"
          :alarmInfo="item"
          :compareType="compareType"
          @singleChecked="singleChecked"
        />
        <!-- 空占位，保证数据居左 -->
        <div
          class="alarnRow"
          v-for="(item, index) of 5 - (15 % 5)"
          :key="index + 'a'"
        ></div>
        <ui-loading v-if="loading"></ui-loading>
        <ui-empty v-if="tableList.length === 0 && !loading"></ui-empty>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <webSocket @sendInfo="sendInfo" />
  </div>
</template>
<script>
import { queryParamDataByKeys } from "@/api/config";
import {
  sensory,
  taskQueryList,
  batchHandleSensory,
} from "@/api/target-control";
import searchForm from "../../components/search-form.vue";
import query from "./components/query.vue";
import sensoryCard from "./components/sensory-card.vue";
import webSocket from "@/components/webSocket.vue";
import { commonMixins } from "@/mixins/app.js";
import {
  wifiAlarmSearch,
  rfidAlarmSearch,
  baseStationAlarmSearch,
  etcAlarmSearch,
} from "@/api/wisdom-cloud-search";

export default {
  components: {
    searchForm,
    query,
    sensoryCard,
    // alarmList,
    webSocket,
  },
  mixins: [commonMixins], //全局的mixin
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    // 是否从全景智叟进入
    isCloudSearch: {
      type: Boolean,
      default: false,
    },
    // 全景智叟 搜索关键词
    keyWords: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      radioList: [
        { key: 99, value: "全部", number: 0 },
        { key: 0, value: "未处理", number: 0 },
        { key: 1, value: "有效", number: 0 },
        { key: 2, value: "无效", number: 0 },
      ],
      queryParam: {
        selectDeviceList: [],
      },
      single: false,
      status: "",
      visible: false,
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      taskList: [],
      tableList: [],
      allCheckStatus: false,
      indeterminate: false,
      alarmInfo: {},
      alarmConfigInfo: {},
      loading: true,
      // 根据智叟传递的compareType调用不同的智叟接口
      cloudSearch: {
        3: { req: wifiAlarmSearch },
        5: { req: rfidAlarmSearch },
        4: { req: baseStationAlarmSearch },
        6: { req: etcAlarmSearch },
      },
    };
  },
  watch: {
    tableList: {
      handler(val) {
        var checkNum = val.filter((item) => item.checked).length;
        var tableNum = val.length;

        if (checkNum == tableNum && checkNum.length != 0) {
          this.allCheckStatus = true;
          this.indeterminate = false;
        } else {
          if (checkNum == 0) {
            this.allCheckStatus = false;
            this.indeterminate = false;
          } else {
            this.allCheckStatus = true;
            this.indeterminate = true;
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  async mounted() {
    //#region 初始化配置信息，警情级别 & 布控任务
    await queryParamDataByKeys(["ICBD_TARGET_CONTROL"]).then((res) => {
      if (res.data.length) {
        this.alarmConfigInfo = JSON.parse(res.data[0].paramValue);
      }
    });
    await taskQueryList({ compareType: this.compareType }).then((res) => {
      this.taskList = res.data;
    });
    //#endregion
    this.tableListFn();
  },
  methods: {
    /**
     * @description: 获取查询参数，并进行调整
     */
    getQueryParams() {
      /**
       * 参数来源：
       * 1. searchForm组件，普通搜索条件：this.$refs.searchForm.getQueryParams()
       * 2. query组件，高级检索条件：this.$refs.slotQuery.getQueryParams()
       * 3. $route.query，路由携带的参数
       * 4. 当前组件的参数：页码信息：this.params
       */
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
        ...this.$refs.slotQuery.getQueryParams(),
        compareType: this.compareType,
      };
      if (data.operationType == 99) {
        data.operationType = null;
      }
      data.simScore = data.simScore / 100;
      // 从工作台-报警过来不需要时间查询
      if (this.$route.query.noDate) {
        data.alarmTimeB = null;
        data.alarmTimeE = null;
      }
      return data;
    },

    tableListFn() {
      this.loading = true;
      this.tableList = [];
      // 全景智叟来的搜索
      if (this.isCloudSearch) {
        // 拿一下时间信息
        let param = this.getQueryParams();
        let params = {
          et: param.alarmTimeE,
          keywords: this.keyWords,
          pageNumber: param.pageNumber,
          pageSize: param.pageSize,
          st: param.alarmTimeB,
        };
        this.cloudSearch[Number(this.compareType)]
          .req(params)
          .then((res) => {
            this.tableList = res.data.entities;
            this.total = res.data.total;
            this.tableList.forEach((item) => {
              this.$set(item, "checked", false);
              let info = this.alarmConfigInfo.alarmLevelConfig.find(
                (ite) => ite.alarmLevel == item.taskLevel
              );
              item.bgIndex = Number(info.alarmColour);
            });
          })
          .finally(() => {
            this.loading = false;
          });
        return;
      }
      sensory(this.getQueryParams())
        .then((res) => {
          this.tableList = res.data.entities;
          this.total = res.data.total;
          this.tableList.forEach((item) => {
            this.$set(item, "checked", false);
            var info = this.alarmConfigInfo.vehicleAlarmLevelConfig.find(
              (ite) => ite.alarmLevel == item.taskLevel
            );
            item.bgIndex = Number(info.alarmColour);
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    collection() {
      this.tableListFn();
    },
    sendInfo(row) {
      // this.tableListFn()
    },
    allChecked() {
      this.tableList.forEach((item) => {
        if (this.allCheckStatus) {
          item.checked = false;
        } else {
          item.checked = true;
        }
      });
    },
    // 批量设置状态
    allConfigStatus() {
      var list = this.tableList.filter((item) => item.checked);
      var arr = [];
      list.forEach((item) => {
        arr.push({ alarmTime: item.alarmTime, alarmId: item.alarmId });
      });
      var param = {
        alarmRecordSimpleForms: arr,
        operationType: this.status,
      };
      batchHandleSensory(param).then((res) => {
        this.$Message.success(res.data);
      });
    },
    batchStatus(name) {
      var list = this.tableList.filter((item) => item.checked);
      var arr = [];
      list.forEach((item) => {
        arr.push({ alarmTime: item.alarmTime, alarmId: item.alarmId });
      });
      var param = {
        alarmRecordSimpleForms: arr,
        operationType: name,
        compareType: this.compareType,
      };
      batchHandleSensory(param).then((res) => {
        this.$Message.success(res.data);
      });
      setTimeout(() => {
        this.tableListFn();
      }, 1000);
    },
    // 单选
    singleChecked(flag) {},
    radioChange(row) {},
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.queryParam[key] = e;
      this.$forceUpdate();
    },
    taskChange() {},
    selectDevice() {},
    query(form) {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.queryParam = {
        ...this.queryParam,
        ...form,
        ...this.$refs.slotQuery.queryParam,
      };
      this.tableListFn();
    },
    reset(form) {
      this.$refs.slotQuery.reset();
      this.queryParam = {};
      let searchParams = this.$refs.slotQuery.queryParam;
      this.queryParam = { ...this.queryParam, ...searchParams, ...form };
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    /**
     * 获取当前时间的前后N天日期
     * @param {*} num 获取前N天的数据，2：两天后， -2 两天前
     */
    getConfigDate(num) {
      var today = new Date();
      var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * num;
      var targetday = new Date();
      targetday.setTime(targetday_milliseconds); //注意，这行是关键代码
      var cur_year = today.getFullYear();
      var cur_month = today.getMonth() + 1;
      var cur_day = today.getDate();
      cur_month = (cur_month < 10 ? "0" : "") + cur_month;
      cur_day = (cur_day < 10 ? "0" : "") + cur_day;
      var year = targetday.getFullYear();
      var month = targetday.getMonth() + 1;
      var day = targetday.getDate();
      month = (month < 10 ? "0" : "") + month;
      day = (day < 10 ? "0" : "") + day;
      return [
        year + "-" + month + "-" + day,
        cur_year + "-" + cur_month + "-" + cur_day,
      ];
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
  }
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    margin: 0 -0.02604rem;
    align-content: flex-start;
    position: relative;
  }
  .list {
    flex: 1;
    display: flex;
    flex-flow: wrap;
    justify-content: space-between;
    .alarnRow {
      width: ~"calc(20% - 10px)";
      margin-bottom: 12px;
      cursor: pointer;
    }
  }
}
</style>
