<template>
  <div class="assetsaudit auto-fill">
    <div class="assetsaudit-header">
      <div class="tab">
        <div
          v-for="(item, index) in deviceTypeList"
          :key="index"
          :class="['tab-item', active === item.value ? 'active' : '']"
          @click="changeType(item)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="btn-list">
        <Button type="text" class="ml-sm" @click="paramsConfig">
          <i class="icon-font icon-canshupeizhi"></i>
          <span class="ml-xs inline vt-middle link">参数配置</span>
        </Button>
        <span class="ml-md line"></span>
        <span class="ml-md audit-num">
          <i class="icon-font check-status-font f-16 mr-xs" :class="[activeIcon, getClassName]"></i>
          <span class="check-status-font f-16" :class="getClassName">{{ totalNum | formatNum }}</span>
        </span>
      </div>
    </div>
    <search-module
      ref="searchModule"
      :active="active"
      :device-type-list="deviceTypeList"
      :search-data="searchData"
      @search="search"
      @changeStatus="changeStatus"
    >
    </search-module>
    <table-module
      ref="TableModule"
      :active="active"
      :search-params="searchData"
      @getTotalNum="getTotalNum"
      @updateChooseData="updateChooseData"
      @audit="audit"
      @differenceDetails="differenceDetails"
      @abnormal="abnormal"
      @handleView="handleView"
      @auditRecords="auditRecords"
      @handleBatchAudit="handleBatchAudit"
      @changeCheckAll="changeCheckAll"
    ></table-module>
    <parameter-config ref="ParameterConfig" v-model="configShow"></parameter-config>
    <audit-module
      ref="AuditModule"
      v-model="auditModuleVisible"
      :differ-data="detailData"
      @handleUpdateAudit="handleUpdateAudit"
    ></audit-module>
    <upload-error
      v-model="uploadErrorVisible"
      :error-data="errorData"
      :error-columns="errorColumns"
      :footer-hide="footerHide"
    >
      <template #footer>
        <Button @click="cancelCompulsory" class="mr-lg"> 取 消</Button>
        <Button type="primary" :loading="compulsoryLoading" @click="compulsoryStorage"> 强制保存 </Button>
      </template>
    </upload-error>
    <batch-audit
      v-model="batchAuditVisible"
      :chooseAuditDataIds="chooseAuditDataIds"
      :chooseAbnormalDataIds="chooseAbnormalDataIds"
      :chooseDataLen="chooseDataLen"
      :check-all="checkAll"
      :search-data="searchData"
      @handleUpdateBatchAudit="handleUpdateBatchAudit"
    ></batch-audit>
    <device-detail
      v-model="deviceDetailShow"
      :modal-title="deviceDetailTitle"
      :modal-action="deviceDetailAction"
      :view-url="viewUrl"
      :view-device-id="viewDeviceId"
      :device-code="deviceCode"
      :unqualified="deviceUnqualified"
      :save-url="saveUrl"
      :save-method="saveMethod"
      :check-fun="checkFun"
      :check-data="checkData"
      :has-multiView="false"
    >
    </device-detail>
    <operation-record
      v-model="recordShow"
      :title="recordTitle"
      :record-type="recordType"
      :table-columns="recordColumns"
      :table-data="recordData"
      :fetch-method="recordFetch"
    ></operation-record>
    <difference
      v-model="differenceShow"
      :field-list="differenceList"
      :sign="sign"
      :check-fun="checkFun"
      :save-fun="saveDifference"
      @onCancel="diffCancel"
    >
      <template #filter>
        <RadioGroup class="fl" v-model="sign">
          <Radio label="differ">标记差异字段</Radio>
          <Radio class="ml-lg" label="unqualified">标记不合格字段</Radio>
        </RadioGroup>
        <Checkbox v-show="sign === 'differ'" class="fr" v-model="onlyDiff" @on-change="changeOnlyDiff">
          只显示差异字段
        </Checkbox>
        <Checkbox
          v-show="sign === 'unqualified'"
          class="fr"
          v-model="onlyUnqualified"
          @on-change="changeOnlyUnqualified"
        >
          只显示不合格字段
        </Checkbox>
      </template>
    </difference>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
const checkErrorColumns = [
  {
    title: '序号',
    type: 'index',
    width: 70,
    align: 'center',
  },
  {
    title: '不合格原因',
    key: 'errorMessage',
    tooltip: true,
  },
  {
    title: '检测规则名称',
    key: 'checkRuleName',
    tooltip: true,
  },
  {
    title: '不合格字段',
    key: 'propertyName',
    tooltip: true,
  },
  { title: '异常类型', key: 'errorType', tooltip: true },
  {
    title: '实际结果',
    key: 'propertyValue',
    tooltip: true,
  },
];
export default {
  name: 'assetsaudit',
  data() {
    return {
      active: '0',
      activeIcon: 'icon-daishenhe',
      deviceTypeList: [
        { label: '待审核', value: '0', classname: '', icon: 'icon-daishenhe' },
        // {label: '审核通过', value: '1', classname: 'font-success', icon: 'icon-shenhetongguo'}, // 暂时不做
        {
          label: '审核不通过',
          value: '2',
          classname: 'font-failed',
          icon: 'icon-shenhebutongguo',
        },
      ],
      searchData: {},
      configShow: false,
      differenceShow: false,
      differenceList: [],
      detailData: {},
      onlyDiff: false,
      onlyUnqualified: false,
      compulsoryLoading: false,
      checkData: null,
      sign: 'differ',
      auditModuleVisible: false,
      uploadErrorVisible: false,
      footerHide: true,
      errorData: [],
      errorColumns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '不合格原因',
          key: 'errorMessage',
          tooltip: true,
        },
        {
          title: '检测规则名称',
          key: 'checkRuleName',
          tooltip: true,
        },
        {
          title: '不合格字段',
          key: 'propertyName',
          tooltip: true,
        },
        { title: '异常类型', slot: 'errorType', tooltip: true },
        {
          title: '实际结果',
          key: 'propertyValue',
          tooltip: true,
        },
      ],
      batchAuditVisible: false,
      deviceDetailShow: false,
      choosedOrg: {},
      // 查看
      deviceDetailTitle: '设备信息',
      deviceDetailAction: 'view',
      viewDeviceId: 0,
      deviceCode: '',
      viewUrl: null,
      saveUrl: null,
      saveMethod: 'post',
      deviceUnqualified: null,
      // 批量审核
      chooseAuditDataIds: [], // 选中待审核id集合
      chooseAbnormalDataIds: [], //选中已审核id集合
      chooseDataLen: 0,
      totalNum: 0,
      // 记录
      recordShow: false,
      recordTitle: '审核记录',
      recordType: 'audit-records',
      recordData: [],
      recordColumns: [],
      recordFetch: null,
      compulsoryResolve: null,
      compulsoryReject: null,
      checkAll: false,
    };
  },
  activated() {
    this.$refs.searchModule.search();
  },
  methods: {
    changeType(item) {
      this.active = item.value;
      this.search(this.searchData);
    },
    changeStatus(deviceStatus) {
      this.type = deviceStatus;
    },
    search(searchData = {}) {
      let searchDatas = this.$util.common.deepCopy(searchData);
      this.searchData = Object.assign({}, searchDatas, {
        examineStatus: this.active,
      });
    },
    paramsConfig() {
      this.configShow = true;
    },
    updateChooseData(data) {
      this.chooseAbnormalDataIds = [];
      this.chooseDataLen = 0;
      if (!data.length) return false;
      this.chooseDataLen = data.length;
      this.chooseAuditDataIds = data.map((item) => {
        if (item.checkStatus === '2') {
          this.chooseAbnormalDataIds.push(item.id);
        }
        return item.id;
      });
    },
    async audit(row) {
      this.detailData = row;
      this.auditModuleVisible = true;
    },
    async abnormal(row) {
      try {
        const res = await this.$http.get(equipmentassets.queryUnqualifiedDetail, { params: { id: row.id } });
        this.uploadErrorVisible = true;
        this.footerHide = true;
        this.errorData = res.data.data;
      } catch (err) {
        console.log(err);
      }
    },
    handleView(row) {
      this.viewUrl = equipmentassets.viewFill;
      this.saveUrl = equipmentassets.updateFill;
      this.viewDeviceId = row.id;
      this.deviceCode = row.deviceId;
      this.deviceDetailAction = 'view';
      this.deviceDetailShow = true;
    },
    // 差异详情
    async differenceDetails(row) {
      try {
        const res = await this.$http.get(equipmentassets.queryDifferDetail, {
          params: {
            id: row.id,
          },
        });
        this.viewDeviceId = row.id;
        this.differData = res.data.data;
        this.differenceList = res.data.data;
        this.deviceDetailAction = 'edit';
        this.differenceShow = true;
        this.onlyDiff = true;
        this.differenceList = this.differData.filter((row) => row.isDiffer === '1');
      } catch (err) {
        console.log(err);
      }
    },
    diffCancel() {
      this.sign = 'differ';
      this.onlyDiff = false;
      this.onlyUnqualified = false;
    },
    // 检测方法
    checkFun(deviceData) {
      return new Promise(async (resolve, reject) => {
        try {
          this.compulsoryResolve = resolve;
          this.compulsoryReject = reject;
          if (this.deviceDetailAction === 'add') {
            await this.fillAddCheck(deviceData);
          } else {
            await this.fillUploadCheck(deviceData);
          }
        } catch (err) {
          console.log(err);
          reject(false);
        }
      });
    },
    // 检测错误信息
    checkError(err) {
      this.checkData = err.data.data;
      this.errorData = JSON.parse(this.checkData.checkResultJson).filter((row) => !row.isSuccess);
      this.errorColumns = checkErrorColumns;
      this.uploadErrorVisible = true;
      this.footerHide = false;
    },
    // 原始库检测
    async fillAddCheck(deviceData) {
      try {
        await this.$http.post(equipmentassets.fillAddCheck, deviceData);
        this.compulsoryResolve(true);
      } catch (err) {
        console.log(err, 'err');
        switch (err.data.code) {
          // 检测不合格
          case 9006400:
            this.checkError(err);
            break;
          // 参数错误
          case 9006401:
            this.compulsoryReject(false);
            break;
          default:
            this.compulsoryReject(false);
            break;
        }
      }
    },
    // 原始库更新检测
    async fillUploadCheck(deviceData) {
      // 如果为已入库设备，则需传入deviceInfoId并且将id为空
      let params = {
        id: this.viewDeviceId,
        deviceInfoId: deviceData.id,
      };
      try {
        // step 调用updateCheck使用，1- 校验 设备编码 2-校验检测规则。修改原始库设备信息时，两次调用updateCheck接口，第一次传1 第二次传2
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign(params, { step: 1 }, deviceData));
        await this.$http.post(equipmentassets.updateCheckFill, Object.assign(params, { step: 2 }, deviceData));
        this.compulsoryResolve(true);
      } catch (err) {
        console.log(err, 'err');
        switch (err.data.code) {
          // 检测出错误
          case 9006400:
            this.checkError(err);
            break;
          // 库中已有该数据
          case 9006402:
            this.$UiConfirm({
              content: err.data.msg,
              title: '警告',
            })
              .then(async () => {
                try {
                  await this.$http.post(
                    equipmentassets.updateCheckFill,
                    Object.assign(params, { step: 2 }, deviceData),
                  );
                  this.compulsoryResolve(true);
                } catch (err) {
                  console.log(err);
                  switch (err.data.code) {
                    case 9006400:
                      this.checkError(err);
                      break;
                    default:
                      this.compulsoryReject(false);
                      break;
                  }
                }
              })
              .catch((res) => {
                console.log(res);
                this.compulsoryReject(false);
              });
            break;
          default:
            this.compulsoryReject(false);
            break;
        }
      }
    },
    // 差异详情保存
    async saveDifference(deviceData) {
      try {
        const res = await this.$http.post(
          equipmentassets.updateFill,
          Object.assign(
            {
              accuracyVo: this.checkData,
              id: this.viewDeviceId,
            },
            deviceData,
          ),
        );
        this.$Message.success(res.data.msg);
        this.differenceShow = false;
        this.search();
      } catch (err) {
        console.log(err);
      }
    },
    // 强制入库
    compulsoryStorage() {
      this.uploadErrorVisible = false;
      this.compulsoryResolve(true);
    },
    cancelCompulsory() {
      this.uploadErrorVisible = false;
      this.compulsoryReject(false);
    },
    changeOnlyDiff(val) {
      if (val) {
        this.differenceList = this.differData.filter((row) => row.isDiffer === '1');
      } else {
        this.differenceList = this.differData;
      }
    },
    changeOnlyUnqualified(val) {
      if (val) {
        this.differenceList = this.differData.filter((row) => row.isUnqualified === '1');
      } else {
        this.differenceList = this.differData;
      }
    },
    // 查看审核记录
    auditRecords(row) {
      this.recordType = 'audit-records';
      this.recordTitle = '审核记录';
      this.recordShow = true;
      this.recordColumns = [
        {
          title: '序号',
          type: 'index',
          width: 60,
          align: 'center',
        },
        {
          title: '审核人',
          key: 'examineUser',
          align: 'left',
        },
        {
          title: '组织机构',
          key: 'orgNames',
          align: 'left',
        },
        {
          title: '联系电话',
          key: 'phone',
          align: 'left',
        },
        {
          title: '审核结果',
          key: 'examineStatusText',
          align: 'left',
        },
        {
          title: '审核时间',
          key: 'examineTime',
          align: 'left',
        },
        {
          title: '审核说明',
          key: 'remark',
          align: 'left',
        },
      ];
      this.recordFetch = async (searchData) => {
        try {
          const res = await this.$http.get(equipmentassets.queryExamineListByFillInId, {
            params: Object.assign(
              {
                fillInId: row.id,
              },
              searchData,
            ),
          });
          this.recordData = res.data.data;
        } catch (err) {
          console.log(err);
        }
      };
    },
    handleUpdateAudit() {
      this.auditModuleVisible = false;
      this.$refs.searchModule.search();
    },
    handleBatchAudit() {
      this.batchAuditVisible = true;
    },
    handleUpdateBatchAudit() {
      this.batchAuditVisible = false;
      this.$refs.searchModule.search();
    },
    getTotalNum(num) {
      this.totalNum = num;
    },
    changeCheckAll(check) {
      this.checkAll = check;
      if (check) {
        this.chooseDataLen = this.$refs.TableModule.pageData.totalCount;
      }
    },
  },
  computed: {
    getClassName() {
      const activeItem = this.deviceTypeList.find((item) => {
        if (item.value === this.active) {
          this.activeIcon = item.icon;
          return item;
        }
      });
      return activeItem.classname;
    },
  },
  components: {
    SearchModule: require('./components/search-module.vue').default,
    TableModule: require('./components/table-module.vue').default,
    ParameterConfig: require('./components/parameter-config.vue').default,
    Difference: require('@/views/viewassets/components/difference.vue').default,
    AuditModule: require('./components/audit-module.vue').default,
    UploadError: require('@/views/datagovernance/onlinegovernance/information/components/upload-error.vue').default,
    BatchAudit: require('./components/batch-audit.vue').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
    OperationRecord: require('@/views/viewassets/assetstorage/assetfill/components/operation-record/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .assetsaudit {
    .tab {
      border-bottom: 1px solid #d8d8d8;

      .tab-item {
        color: rgba(0, 0, 0, 0.35);
        &.active {
          color: var(--color-primary);
          border-left: 1px solid #d8d8d8;
          border-right: 1px solid #d8d8d8;
          &::before {
            background-color: var(--color-primary);
          }
          &::after {
            background-color: #fff;
          }
        }
      }
    }
    .btn-list {
      .link {
        color: var(--color-primary);
      }
      .icon-canshupeizhi {
        color: var(--color-primary);
      }
    }
  }
}
.assetsaudit {
  background-color: var(--bg-content);
  .assetsaudit-header {
    position: relative;
    margin: 20px 20px 0 20px;
    position: relative;
  }
  .tab {
    display: flex;
    border-bottom: 1px solid var(--devider-line);
    .tab-item {
      padding-top: 3px;
      cursor: pointer;
      width: 110px;
      height: 34px;
      line-height: 34px;
      color: #56789c;
      font-size: 14px;
      position: relative;
      text-align: center;
      &.active {
        color: var(--color-primary);
        border-left: 1px solid var(--devider-line);
        border-right: 1px solid var(--devider-line);
        &::before {
          position: absolute;
          left: 0;
          top: 0;
          content: '';
          display: inline-block;
          width: 100%;
          height: 3px;
          background-color: var(--color-primary);
        }
        &::after {
          position: absolute;
          left: 0;
          bottom: -1px;
          content: '';
          display: inline-block;
          width: 100%;
          height: 1px;
          background-color: var(--bg-content);
        }
      }
    }
  }
  .btn-list {
    position: absolute;
    right: 0;
    top: 0;
    .line {
      height: 18px;
      width: 2px;
      display: inline-block;
      background-color: var(--devider-line);
      vertical-align: middle;
    }
    .link {
      text-decoration: underline;
    }
    .audit-num {
      line-height: 34px;
    }
  }
  @{_deep} .check-status-font {
    padding: 0 !important;
    vertical-align: middle;
  }
}
</style>
