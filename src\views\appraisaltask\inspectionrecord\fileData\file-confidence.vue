<template>
  <div class="carInfo auto-fill">
    <!-- 统计 ---------------------------------------------------------------------------------------------------- -->
    <ChartsContainer :abnormalCount="abnormalCount" />
    <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
    <TableCard ref="infoCard" :cardInfo="cardInfo" :loadData="loadDataCard">
      <div slot="search" class="hearder-title">
        <!-- <SearchConfidence
          ref="searchCard"
          @startSearch="startSearch"
          :taskObj="taskObj"
          :treeData="treeData"
        /> -->
        <div style="color: #2b84e2">已聚档实有人口</div>
      </div>
      <!-- 卡片 -->
      <template #card="{ row }">
        <UiGatherCard class="card" :list="row" :personTypeList="personTypeList" :cardInfo="cardInfo"></UiGatherCard>
      </template>
    </TableCard>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  components: {
    UiGatherCard: require('../components/ui-gather-card.vue').default,
    // SearchConfidence: require('./component/searchConfidence').default,
    ChartsContainer: require('../components/chartsContainer').default,
    LookScene: require('@/components/look-scene').default,
    TableCard: require('./component/tableCard_str.vue').default,
  },
  props: {
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      bigPictureShow: false,
      // treeData: [],
      imgList: [],
      // selectKey: '', // 机构树
      infoObj: {},
      abnormalCount: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '抓拍总数: ', value: 'urlNum' },
      ],
      loadDataCard: (parameter) => {
        if (!this.taskObj.batchId) {
          console.log('%c!!!重要。 由于batchId为必输项，如果为空，固不发出请求。', 'color:red;');
        } else {
          return this.$http
            .post(
              governanceevaluation.personArchives,
              Object.assign(
                {
                  pageNumber: parameter.params.pageNumber,
                  pageSize: parameter.params.pageSize,
                  batchId: this.taskObj.batchId,
                  indexId: this.taskObj.indexId,
                  orgCode: this.taskObj.regionCode,
                },
                this.searchData,
              ),
            )
            .then((res) => {
              return res.data;
            });
        }
      },
    };
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    taskObj: {
      handler() {
        this.info();
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  created() {},
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async info() {
      // this.treeData = this.taskObj.orgCodeList && this.taskObj.orgCodeList.length > 0 ? this.$util.common.arrayToJson(JSON.parse(JSON.stringify(this.taskObj.orgCodeList)), 'id', 'parentId') : []
      // this.selectKey = this.taskObj.orgCode
      switch (this.currentTree.id) {
        case 601:
          this.abnormalCount = [
            {
              title: '人像档案置信数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '人像档案总数',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '人像档案未置信数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
          ];
          break;
        case 603:
          this.abnormalCount = [
            {
              title: '注册登记车辆总数',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '注册车辆已聚档数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
            {
              title: '注册车辆未聚档数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
            },
          ];
          break;
      }
      await this.static();
      this.$nextTick(() => {
        this.$refs.infoCard.info(true);
      });
    },
    static() {
      if (!this.taskObj.batchId) {
        console.log('%c!!!重要。 由于batchId为必输项，如果为空，固不发出请求。', 'color:red;');
        return;
      }
      this.$http
        .post(governanceevaluation.personArchivesDetailsCount, {
          batchId: this.taskObj.batchId,
          indexId: this.taskObj.indexId,
          orgCode: this.taskObj.regionCode,
        })
        .then((res) => {
          let data = res.data.data;
          this.abnormalCount = [
            {
              title: '人像档案置信数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.count,
            },
            {
              title: '人像档案总数',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.confidenceRatCount,
            },
            {
              title: '人像档案未置信数量',
              icon: 'icon-a-guijizhunqueshuaijiance2',
              count: data.confidenceRatCount - data.count,
            },
          ];
        });
    },
    // // 检索
    // startSearch(searchData) {
    //   this.searchData = {}
    //   this.selectKey = searchData.orgCodeList
    //   this.searchData = searchData
    //   this.$refs.infoCard.info(true)
    // },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  .tagView {
    display: inline-block;
    margin-top: 10px;
    z-index: 20;
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 10px 10px 0px;
  }
}
.hearder-title {
  color: #fff;
  margin-top: 10px;
  font-size: 16px;
  border-bottom: 1px solid var(--devider-line);
  padding-bottom: 9px;
  margin-bottom: 10px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.ui-images {
  width: 56px;
  height: 56px;
  margin: 5px 0;
  .ui-image {
    min-height: 56px !important;
    /deep/ .ivu-spin-text {
      img {
        width: 56px;
        height: 56px;
        margin-top: 5px;
      }
    }
  }
}
</style>
