<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :width="formData.reportMethod == 2 && formData.deviceList.length > 0 ? '90rem' : '35rem'"
    @onCancel="handleReset"
    @query="handleSubmit"
    :loading="loading"
  >
    <div class="box-content">
      <Form ref="formValidate" :model="formData" :rules="ruleValidate" :label-width="120" :label-colon="true">
        <FormItem label="报备名称" prop="reportName">
          <Input v-model="formData.reportName" class="iptWidth" placeholder="请输入报备名称"></Input>
        </FormItem>
        <FormItem label="报备区划" prop="civilCode">
          <api-area-tree
            class="api-area-tree area-tree-dropdown"
            :select-tree="selectTree"
            @selectedTree="selectedTree"
            placeholder="请选择报备区划"
          ></api-area-tree>
        </FormItem>
        <FormItem label="报备对象" prop="reportMethod">
          <div>
            <div v-show="formData.deviceList.length == 0">
              <RadioGroup @on-change="handleRadio" v-model="formData.reportMethod">
                <Radio label="1">平台报备</Radio>
                <Radio label="2">设备报备</Radio>
              </RadioGroup>
            </div>
            <div v-if="formData.reportMethod == 2 && formData.deviceList.length == 0">
              <div class="camera" @click="handleSelectDevice">请选择待报备设备</div>
            </div>
            <span v-if="formData.reportMethod == 2 && formData.deviceList.length > 0" class="hint"
              >共{{ formData.deviceList.length }}条设备</span
            >
          </div>
          <CheckboxGroup v-if="formData.reportMethod == 1" v-model="formData.reportType">
            <div class="checkboxinline" v-for="(reportItem, reIndex) in reportTypes" :key="'reportType' + reIndex">
              <Checkbox :label="reportItem.key">
                <span class="ml-xs">{{ reportItem.label }}</span>
              </Checkbox>
              <Tooltip placement="right-start">
                <i class="icon-font icon-wenhao f-16" :style="{ color: 'var(--color-warning)' }"></i>
                <div slot="content">
                  <p>
                    <span class="font-blue">【{{ reportItem.label }}】</span>有<span class="font-red ml-xs mr-xs">{{
                      !!correlationIndexList[reIndex + 1] ? correlationIndexList[reIndex + 1].length : 0
                    }}</span
                    >个检测指标有：
                  </p>
                  <p
                    class="ml-xs"
                    v-for="(correlateItem, corIndex) in correlationIndexList[reIndex + 1]"
                    :key="'corIndex' + corIndex"
                  >
                    {{ corIndex + 1 }}、{{ correlateItem.indexName }}
                  </p>
                </div>
              </Tooltip>
            </div>
          </CheckboxGroup>
          <p v-if="formData.reportMethod == 1" class="font-red f-14 report-type-tip">
            说明：选中以上任一报备类型成功后，其中的全部检测指标不参与检测，检测结果为“报备”状态
          </p>
        </FormItem>
        <FormItem v-if="formData.reportMethod == 1" label="报备时间段" required>
          <div class="align-flex time-wrap">
            <FormItem prop="begintime">
              <DatePicker
                type="datetime"
                v-model="formData.begintime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择开始时间"
                :options="startTimeOption"
                @on-change="handleChangeTime($event, 'beginTime')"
              ></DatePicker>
            </FormItem>
            <span class="line-padding">—</span>
            <FormItem prop="endtime">
              <DatePicker
                type="datetime"
                v-model="formData.endtime"
                format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择结束时间"
                :options="endTimeOption"
                @on-change="handleChangeTime($event, 'endTime')"
              ></DatePicker>
            </FormItem>
          </div>
        </FormItem>
        <FormItem label="报备备注">
          <Input
            v-model="formData.reportRemark"
            type="textarea"
            :autosize="{ minRows: 5, maxRows: 5 }"
            placeholder="请输入报备备注..."
          ></Input>
        </FormItem>
        <FormItem label="上传文件" prop="fileUrl">
          <div class="align-flex">
            <ui-upload
              ref="UiUpload"
              v-model="formData.fileUrl"
              :multiple="true"
              :accept="accept"
              :format="format"
              :upload-url="uploadUrl"
              :get-upload-params="getUploadParams"
              upload-placeholder="上传文件"
            >
              <template #downloadtip>
                <span class="font-red ml-xs f-14">注：可最多上传3个文件，每个文件不大于10M</span>
              </template>
            </ui-upload>
          </div>
        </FormItem>
      </Form>
      <div v-if="formData.reportMethod == 2 && formData.deviceList.length > 0" class="box-line"></div>
      <ui-table
        v-if="formData.reportMethod == 2 && formData.deviceList.length > 0"
        class="ui-table auto-fill unsetModal"
        reserveSelection
        :table-columns="tableColumns"
        :table-data="formData.deviceList"
        :loading="loading"
        :defaultStoreData="defaultStoreData"
      >
        <template slot="sblwzt" slot-scope="{ row }">
          <span
            :style="{
              color: row.sblwzt === '1' ? '#0E8F0E' : '#BC3C19',
            }"
          >
            {{ row.sblwzt == 1 ? '可用' : '不可用' }}
          </span>
        </template>
        <template slot="reportReason" slot-scope="{ row, index }">
          <Select
            class="input-width"
            v-model="row.reportReason"
            placeholder="请选择报备原因"
            clearable
            @on-change="handleChangeSele($event, row.reportReason, index)"
          >
            <Option v-for="item in reportReason" :key="item.dataKey" :value="item.dataKey">{{ item.dataValue }}</Option>
          </Select>
        </template>
        <template slot="beginTime" slot-scope="{ row, index }">
          <DatePicker
            class="width-mds"
            v-model="row.beginTime"
            type="datetime"
            placeholder="报备开始时间"
            confirm
            @on-change="handleChangeBegin($event, row.beginTime, index)"
          ></DatePicker>
        </template>
        <template slot="endTime" slot-scope="{ row, index }">
          <DatePicker
            class="width-mds"
            v-model="row.endTime"
            type="datetime"
            :placeholder="`报备结束时间${row.reportReason == 2 ? '(非必填)' : ''}`"
            confirm
            @on-change="handleChangeEnd($event, row.endTime, index)"
          ></DatePicker>
        </template>
      </ui-table>
    </div>
    <choose-device
      ref="ChooseDevice"
      :search-conditions="selectParams"
      :table-columns="chooseTableColumns"
      :load-data="leftData"
      :selected-list="formValidate.selectDevIds"
      :check-device-flag="selectParams.checkDeviceFlag"
      :regionCode="regionCode"
      needOtherSearch
      @getOrgCode="getOrgCode"
      @getDeviceIdList="getDeviceIdList"
      @exportModule="exportModule"
    >
      <template #search-header>
        <search-list
          ref="SearchList"
          :dict-data="dictData"
          :search-conditions="searchConditions"
          @search="search"
          @reset="reset"
        ></search-list>
      </template>
      <template #baseCheckStatus="{ row }">
        <span class="statustag" :class="row.baseCheckStatus === '1' ? 'bg-failed' : 'bg-success'">{{
          checktSatusText[row.baseCheckStatus]
        }}</span>
      </template>
      <template #viewCheckStatus="{ row }">
        <span class="statustag" :class="row.viewCheckStatus === '1' ? 'bg-failed' : 'bg-success'">{{
          checktSatusText[row.viewCheckStatus]
        }}</span>
      </template>
      <template #videoCheckStatus="{ row }">
        <span class="statustag" :class="row.videoCheckStatus === '1' ? 'bg-failed' : 'bg-success'">{{
          checktSatusText[row.videoCheckStatus]
        }}</span>
      </template>
      <template #phyStatus="{ row }">
        <span
          :style="{
            color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-failed)',
          }"
          >{{ row.phyStatus | filterType(phystatusList) }}</span
        >
      </template>
    </choose-device>
  </ui-modal>
</template>

<script>
import unified from '@/config/api/unified.js';
import { reportTypes, reportModalTableColumns, tableColumns } from '.././situationreporting.js';
import examination from '@/config/api/examination.js';
import equipmentassets from '@/config/api/equipmentassets';
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'update-report',
  props: {
    title: {
      type: String,
      default: '新增报备',
    },
    isEdit: {
      type: Boolean,
      default: false,
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      modalWidth: '90rem',
      //   modalWidth:'35rem',
      formData: {
        reportName: '',
        civilCode: '',
        reportMethod: '1',
        reportType: [],
        begintime: '',
        endtime: '',
        reportRemark: '',
        fileUrl: '',
        deviceList: [],
      },
      regionCode: '', //320000
      formValidate: {
        selectDevIds: [],
      },
      ruleValidate: {
        reportName: [{ required: true, message: '请输入报备名称', trigger: 'blur' }],
        civilCode: [{ required: true, message: '请选择报备区划', trigger: 'change' }],
        reportType: [{ required: true, type: 'array', message: '请选择报备类型', trigger: 'change' }],
        reportMethod: [{ required: true, validator: '', trigger: 'change' }],
        fileUrl: [{ required: true, type: 'string', message: '请上传文件', trigger: 'change' }],
        begintime: [{ required: true, message: '请选择开始时间', trigger: 'change', type: 'date' }],
        endtime: [{ required: true, message: '请选择结束时间', trigger: 'change', type: 'date' }],
      },
      reportTypes: [],
      accept: '.bmp, .jpeg,.jpg,.png,.zip,.7z,.doc,.docx,.xlsx',
      format: ['doc', 'docx', 'xlsx', 'bmp', 'jpg', 'png', 'JPEG', 'zip', '7z'],
      uploadUrl: unified.fileUpload,
      correlationIndexList: [],
      civilCodeData: [],
      selectTree: {
        regionCode: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.formData.endtime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let beginTime = new Date(this.formData.begintime);
          if (beginTime) {
            return date < beginTime;
          }
          return false;
        },
      },
      tableColumns: [],
      defaultStoreData: [],
      // 选择设备模态框
      selectParams: {},
      chooseTableColumns: [],
      dictData: {},
      checktSatusText: ['合格', '不合格'],

      leftData: (parameter, getTotal) => {
        const conditons = getTotal
          ? parameter
          : Object.assign(this.searchConditions, parameter, {
              queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
            });
        let params = this.$util.common.deepCopy(conditons);
        return this.$http.post(equipmentassets.queryDeviceInfoPageList, params).then((res) => {
          if (res.data.data.entities.length) {
            res.data.data.entities.forEach((item) => {
              switch (item.checkStatus) {
                case '1000':
                  item.checkStatus = '0';
                  item.checkStatusText = '待检测';
                  break;
                case '0000':
                  item.checkStatus = '1';
                  item.checkStatusText = '合格';
                  break;
                default:
                  item.checkStatus = '2';
                  item.checkStatusText = '不合格';
                  break;
              }
            });
          }
          return res.data;
        });
      },
      searchConditions: {
        checkDeviceFlag: '0', // 不选默认0, 全量排除： 1，筛选排除： 3, 全选： 2
        orgCodeList: [],
        sbdwlxList: [], // 摄像机点位类型
        sbgnlxList: [], // 功能类型
        deviceId: '', // 设备编码
        deviceName: '', // 设备名称
        cascadeReportStatus: '', // 上报状态
        tagIds: [], // 标签筛选
        isCheck: '', // 是否检测
        baseCheckStatus: '', // 基础信息异常状态
        baseErrorMessageList: [], // 基础信息异常原因
        videoCheckStatus: '', // 视频流信息异常状态
        videoErrorMessageList: [], // 视频流信息异常原因
        viewCheckStatus: '', // 视图信息异常状态
        viewErrorMessageList: [], // 视图信息异常原因
      },
    };
  },
  created() {
    this.reportTypes = reportTypes;
    this.tableColumns = reportModalTableColumns;
    this.chooseTableColumns = tableColumns;
    this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async init(id) {
      this.selectTree.regionCode = '';
      this.formData.reportRemark = '';
      this.formData.reportMethod = '1';
      this.formData.reportType = [];
      this.formData.deviceList = [];
      this.formData.begintime = '';
      this.formData.endtime = '';
      this.$refs.formValidate.resetFields();
      this.getCorrelationIndexListInfo();
      if (id) {
        let res = await this.$http.get(examination.getView + `/${id}`);
        let val = res.data.data;
        Object.keys(this.formData).map((item) => {
          if (item === 'reportType' && val[item]) {
            this.formData[item] = val[item].split(',');
          } else {
            this.formData[item] = val[item] ? val[item] : item == 'deviceList' ? [] : '';
          }
        });
        this.selectTree.regionCode = val.civilCode;
        this.formData.begintime = val.beginTime ? new Date(val.beginTime) : '';
        this.formData.endtime = val.endTime ? new Date(val.endTime) : '';
      }
      this.handleRadio();
      this.visible = true;
    },
    async getCorrelationIndexListInfo() {
      try {
        let {
          data: { data },
        } = await this.$http.get(examination.getCorrelationIndexList);
        this.correlationIndexList = data;
      } catch (error) {
        console.log(error);
      }
    },
    async handleSubmit() {
      if (this.formData.reportMethod == 2) {
        this.ruleValidate.reportType[0].required = false;
        let unIndex = [],
          timeIndex = [];
        this.formData.deviceList.map((item, index) => {
          //报备原因 为设备拆除(2)时不校验结束时间
          if (!item.reportReason || !item.beginTime || (item.reportReason != 2 && !item.endTime)) {
            unIndex.push(index + 1);
          }
          if (item.beginTime && item.endTime) {
            if (item.beginTime > item.endTime) {
              timeIndex.push(index);
            }
          }
        });
        if (unIndex.length > 0) {
          this.$Message.warning(`序号${unIndex.join(',')}项，有内容未选择`);
          return;
        }
        if (timeIndex.length > 0) {
          this.$Message.warning(`序号${unIndex.join(',')}项，报备开始时间大于报备截止时间`);
          return;
        }
      }
      const valid = await this.$refs['formValidate'].validate();
      if (!valid) return false;
      try {
        let url = '';
        let requestMethod = '';
        const params = this.$util.common.deepCopy(this.formData);
        params.reportType = this.$util.common.deepCopy(this.formData.reportType).join(',');
        delete params.begintime;
        delete params.endtime;
        if (this.isEdit) {
          params.id = this.detailData.id;
          url = 'update';
          requestMethod = 'put';
        } else {
          url = 'addReport';
          requestMethod = 'post';
        }
        const res = await this.$http[requestMethod](examination[url], params);
        this.handleReset();
        this.$emit('updateInfo');
        this.$Message.success(res.data.msg);
      } catch (error) {
        console.log(error);
      }
    },
    handleReset() {
      this.visible = false;
    },
    selectedTree(area) {
      this.formData.civilCode = area.regionCode;
      this.$refs.formValidate.validateField('civilCode');
    },
    handleChangeTime(date, timeType) {
      this.formData[timeType] = date;
    },
    getUploadParams(uploadData) {
      let uploadParams = [];
      uploadData.forEach((row) => {
        uploadParams.push(row.url || row.response.data.fileUrl);
      });
      return uploadParams.join(',');
    },
    // 打开选择设备
    async handleSelectDevice() {
      this.dictData = {
        sxjgnlx_receive: this.propertySearchSxjgnlx,
        propertySearch_sbdwlx: this.propertySearchLbdwlx,
        checkStatus: this.checkStatus,
        sourceList: this.sourceList,
        phystatusList: this.phystatusList,
      };
      await this.$refs.ChooseDevice.init();
    },
    getOrgCode(orgCodeList) {
      this.searchConditions.orgCodeList = orgCodeList;
      this.$refs.SearchList.reset();
    },
    getDeviceIdList(data) {
      data.map((item, index) => {
        this.$set(this.formData.deviceList, index, item);
      });
    },
    dealSearchConditions(data) {
      this.searchConditions = Object.assign(this.searchConditions, data);
    },
    search(data) {
      this.dealSearchConditions(data);
      this.$refs.ChooseDevice.search();
    },
    reset(data) {
      this.dealSearchConditions(data);
    },
    // 下载模板
    async exportModule() {
      let res = await this.$http.get(examination.downloadTemplate);
      await this.$util.common.transformBlob(res.data.data);
    },
    // 报备原因切换
    handleChangeSele(row, item, index) {
      this.formData.deviceList[index].reportReason = item;
    },
    // 报备开始时间
    handleChangeBegin(value, item, index) {
      this.formData.deviceList[index].beginTime = value;
    },
    // 报备截止时间
    handleChangeEnd(value, item, index) {
      this.formData.deviceList[index].endTime = value;
    },
    // 改变报备对象
    handleRadio() {
      let that = this;
      const validatefun = (rule, value, callback) => {
        if (value == 1) {
          if (that.formData.reportType.length == 0) {
            return callback(new Error('请选择报备类型'));
          } else {
            that.formData.deviceList = [];
            callback();
          }
        } else {
          if (that.formData.deviceList.length == 0) {
            return callback(new Error('请选择待报备设备'));
          } else {
            that.formData.reportType = [];
            that.formData.begintime = '';
            that.formData.endtime = '';
            callback();
          }
        }
      };
      this.$set(this.ruleValidate.reportMethod[0], 'validator', validatefun);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      reportReason: 'algorithm/evaluation_report_reason',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      phystatusList: 'algorithm/propertySearch_phystatus',
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
    }),
  },
  components: {
    UiUpload: require('@/components/ui-upload.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    chooseDevice: require('./choose-device.vue').default,
    SearchList: require('./search-list.vue').default,
  },
};
</script>

<style lang="less" scoped>
.box-content {
  display: flex;
  max-height: 650px;
  .box-line {
    width: 1px;
    background: var(--border-modal-footer);
    margin: 0 10px;
  }
}
.align-flex {
  display: flex;
  align-items: center;
}

.checkboxinline {
  display: flex;
  align-items: center;
}

.line-padding {
  padding: 0 10px;
}

.iptWidth {
  width: 380px;
}
.time-wrap {
  width: 100%;
}
.report-type-tip {
  line-height: 24px;
}
.hint {
  color: var(--color-content);
}
.area-tree-dropdown {
  @{_deep} .ivu-select-dropdown {
    left: 0 !important;
  }
}
@{_deep} .ivu-modal-body {
  padding: 0 50px 0 30px !important;
}
@{_deep} .select-width {
  width: 380px;
}
@{_deep} .ivu-checkbox-wrapper {
  margin-right: 0 !important;
}
.unsetModal {
  position: unset !important;
}
.unsetModal /deep/.ivu-table-wrapper {
  position: unset !important;
}
.width-mds {
  width: 180px;
}
.statustag {
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  padding: 2px 8px;
  border-radius: 4px;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  color: var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
}
</style>
