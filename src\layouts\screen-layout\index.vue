<template>
  <div class="screen-layout">
    <div class="screen-header">
      <p class="time"><i class="iconfont icon-time"></i> {{ nowTime }}</p>
      <p class="screen-title">
        {{ applicationInfo.applicationName || "启数感知大数据平台" }}-{{
          titleName
        }}大屏
      </p>
      <div class="online">
        <img :src="online" alt="" />
        <p>
          在线人数： <span>{{ linepeopleNum }}人</span>
        </p>
      </div>
    </div>
    <router-view />
  </div>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import { dateTime } from "@/util/modules/common";
import { getOnlineNum } from "@/api/largeScreen";
export default {
  name: "screenLayout",
  data() {
    return {
      nowTime: "",
      online: require(`@/assets/img/screen/online.png`),
      titleName: "",
      linepeopleNum: 0,
    };
  },
  computed: {
    ...mapGetters({
      applicationInfo: "applicationInfo",
    }),
  },
  created() {
    let index = this.$route.query.screen;
    let name = {
      1: "数据专题",
      2: "设备专题",
      3: "解析专题",
      4: "建设态势",
    };
    this.titleName = name[index];
    this.appInfo();
    this.init();
  },
  methods: {
    ...mapActions({
      appInfo: "appInfo",
    }),
    init() {
      setInterval(() => {
        this.nowTime = dateTime();
      }, 1000);
      getOnlineNum().then((res) => {
        this.linepeopleNum = res.data;
      });
    },
  },
};
</script>
<style scoped="scoped" lang="less">
.screen-layout {
  height: 100%;
  width: 100%;
  // display: flex;
  box-sizing: border-box;
  overflow-y: hidden;
  background: url("~@/assets/img/screen/screen-left.png") no-repeat,
    url("~@/assets/img/screen/screen-right.png") no-repeat,
    url("~@/assets/img/screen/screen-btm.png") no-repeat,
    url("~@/assets/img/screen/screen-bg.png") no-repeat;
  background-position: left, right, bottom, center;
  background-size: auto, auto, auto, cover;
  // flex-direction: column;
  .screen-header {
    width: 100%;
    height: 110px;
    background: url("~@/assets/img/screen/srceen-head.png") no-repeat;
    display: flex;
    justify-content: space-between;
    .time {
      font-size: 14px;
      font-weight: 400;
      color: #27b5ff;
      padding: 16px 0 0 40px;
      width: 420px;
    }
    .screen-title {
      font-size: 30px;
      font-family: "PangMenZhengDao";
      font-weight: 400;
      letter-spacing: 3px;
      color: #ffffff;
      text-align: center;
      padding-top: 6px;
      // text-shadow: 0px 1px 3px #00417B;
      text-shadow: 0px 1px 3px rgba(2, 20, 63, 0.1);
      background: linear-gradient(90deg, #ffffff 0%, #a6e8ff 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      flex: 1;
    }
    .online {
      width: 420px;
      display: flex;
      align-items: center;
      justify-content: end;
      padding-right: 40px;
      height: 55px;
      img {
        width: 16px;
        height: 16px;
      }
      p {
        font-weight: 400;
        font-size: 14px;
        color: #27b5ff;
      }
      span {
        font-size: 700;
        color: rgba(158, 222, 255, 1);
      }
    }
  }
}
</style>
