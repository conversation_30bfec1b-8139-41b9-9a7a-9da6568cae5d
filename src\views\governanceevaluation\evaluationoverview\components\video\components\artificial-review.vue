<template>
  <!-- 重点/普通时钟准确率 人工复核 -->
  <ui-modal class="artificial-review" v-model="visible" :title="title" :styles="styles">
    <div class="content" v-if="visible">
      <Row v-if="checkMsgVisible" :gutter="16">
        <Col span="14">
          <ui-image :src="currentObj.screenShot" class="image" />
        </Col>
        <Col span="10">
          <span class="ml-lg f-16 font-blue"> <i class="icon-font icon-jiance-01 font-blue f-16"></i>检测信息 </span>
          <ui-label align="right" class="block mt-md" label="设备时间:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.startTime" type="text"></Input>
            <ui-label align="right" class="block" label=" " :width="120">
              <i class="icon-font icon-wenhao ml-sm f-12 mark-color"></i>
              <span class="ml-xs font-red">OCR识别图片时间</span>
            </ui-label>
          </ui-label>
          <ui-label align="right" class="block" label="对应标准时间:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.ntpTime" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block" label=" " :width="120">
            <i class="icon-font icon-wenhao ml-sm f-12 mark-color"></i>
            <span class="ml-xs font-red">截取图片时的北京时间</span>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="时间误差:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.clockSkew" type="text"></Input>
          </ui-label>
          <ui-label align="right" class="block mb-sm" label="检测结果:" :width="120">
            <Input disabled class="width-lg ml-sm" v-model="currentObj.description" type="text"></Input>
          </ui-label>
        </Col>
      </Row>
      <slot name="search"></slot>
      <slot name="custormContent"></slot>
    </div>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </ui-modal>
</template>

<script>
export default {
  name: 'artificial-review',
  data() {
    return {
      visible: false,
      currentObj: {},
    };
  },
  props: {
    title: {
      type: String,
      default: '人工复核',
    },
    checkMsgVisible: {
      type: Boolean,
      default: true,
    },
    styles: {
      type: Object,
      default: () => {
        return {
          width: '6rem',
        };
      },
    },
    artificialRow: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    init() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
  },
  watch: {
    artificialRow: {
      handler(val) {
        this.currentObj = this.$util.common.deepCopy(val);
      },
      immediate: true,
      deep: true,
    },
  },
  components: {},
};
</script>

<style lang="less" scoped>
.content {
  color: #fff;
  padding: 10px 30px;
  .mark-color {
    color: var(--color-warning);
  }
  @{_deep}.ui-image {
    z-index: initial;
    min-height: 400px;
    max-height: 500px;
    overflow-y: auto;
    .ui-image-div {
      height: initial;
      .tileImage {
        width: 100%;
      }
    }
  }
  .ml-lgg {
    margin-left: 38px;
  }
}
</style>
