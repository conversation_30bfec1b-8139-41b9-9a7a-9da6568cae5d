<template>
  <ui-modal v-model="visible" :title="modalTitle" :styles="styles" class="ui-modal" @query="query">
    <div class="area-container">
      <div class="tree-wrapper">
        <ui-search-tree
          ref="uiTree"
          class="ui-search-tree"
          :node-key="nodeKey"
          :tree-data="searchTreeData"
          :default-props="defaultProps"
          noSearch
          checkStrictly
          showCheckbox
          expandAll
        >
          <template #label="{ node, data }">
            <div class="options" :class="data.children && 'is_parent'">
              <span>{{ data[defaultProps.label] }}</span>
              <Button
                v-if="!data.disabled"
                type="text"
                class="ml-sm"
                @click="checkAll(node, data)"
                :style="{ visibility: !data.children ? 'hidden' : '' }"
                >{{ `${data.checkAll ? '取消全选' : '全选'} ` }}</Button
              >
            </div>
          </template>
        </ui-search-tree>
        <loading v-if="loading"></loading>
      </div>
    </div>
  </ui-modal>
</template>

<script>
export default {
  name: 'org-modal',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  props: {
    value: {},
    defaultProps: {
      default: () => {
        return {
          label: 'orgName',
          children: 'children',
        };
      },
    },
    nodeKey: {
      default: 'orgCode',
    },
    treeData: {
      default: () => [],
    },
    defaultCheckedKeys: {
      default: () => [],
    },
    modalTitle: {
      default: '选择组织机构',
    },
  },
  data() {
    return {
      styles: {
        width: '3.5rem',
      },
      visible: false,
      loading: false,
      checkedTreeData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    query() {
      this.checkedTreeKeys = [...this.$refs.uiTree.getCheckedKeys()];
      this.checkedTreeNodes = [...this.$refs.uiTree.getCheckedNodes()];
      this.$emit('query', this.checkedTreeKeys, this.checkedTreeNodes);
      this.visible = false;
    },
    checkAll(node, data) {
      const isCheckAll = data.checkAll;
      node.childNodes.forEach((row) => {
        this.$set(row, 'checked', !isCheckAll);
      });
      this.$set(data, 'checkAll', !isCheckAll);
      this.$set(node, 'checked', !isCheckAll);
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
    treeData: {
      handler(val) {
        if (!val) return;
        this.searchTreeData = this.$util.common.deepCopy(val);
      },
      immediate: true,
    },
    defaultCheckedKeys: {
      handler(val) {
        if (!val) return;
        this.$nextTick(() => {
          this.$refs.uiTree.setCheckedKeys(val);
        });
      },
    },
  },
  computed: {},
};
</script>

<style lang="less" scoped>
@import 'index';
@{_deep} .ivu-modal-body {
  padding: 16px 50px 50px 50px;
}
.btn-mini {
  height: 24px;
  @{_deep} .ivu-input {
    height: 24px;
    line-height: 24px;
  }
}
.w100 {
  width: 100px;
}
.w230 {
  width: 230px;
}
.w160 {
  width: 160px;
}
.area-container {
  .area-filter {
    line-height: 34px;
  }
}
</style>
