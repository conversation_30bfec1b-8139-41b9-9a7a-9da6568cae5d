<!--
    * @FileDescription: 搜索条件
    * @Author: H
    * @Date: 2024/01/31
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-20 10:05:48
 -->
<template>
  <div class="search_box">
    <div class="title">
      <p>{{ title }}</p>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <div class="search_form">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="tabIndex == 4 ? 60 : 85"
        >
          <template v-if="tabIndex == 0">
            <ul class="search_form_type">
              <li
                v-for="(item, index) in typeList"
                :key="index"
                class="typeTab"
                :class="{ typeActive: typeIndex == index }"
                @click="handleTypeClick(index)"
              >
                {{ item.name }}
              </li>
            </ul>
            <template v-if="typeIndex == 0">
              <FormItem label="身份证号:" prop="searchContent">
                <Input
                  v-model="formData.searchContent"
                  placeholder="请输入"
                ></Input>
              </FormItem>
            </template>
            <template v-else>
              <ui-upload-img
                ref="uploadImg"
                :algorithmType="algorithmType"
                :value="urlList"
                size="mini"
                @imgUrlChange="imgUrlChange"
              ></ui-upload-img>
              <div class="slider-content">
                <span class="similarity">相似度:</span>
                <Slider v-model="similarity"></Slider>
                <span>{{ similarity }}%</span>
              </div>
            </template>
            <FormItem label="时  间:" prop="">
              <ui-quick-date
                style="width: 100%"
                ref="quickDateRef"
                v-model="formData.dateType"
                type="month"
                border
                @change="dataRangeHandler"
              />
            </FormItem>
            <div class="search_wrapper">
              <div class="search_title">
                <p class="search_strut">设备资源</p>
                :
              </div>
              <ul class="search_content">
                <li class="active-area-sele" @click="handleSelemodel">
                  选择设备/已选({{ formData.deviceIds.length }})
                </li>
                <li class="area-list" @click="handleSeleArea">
                  <ui-icon type="gateway" />
                </li>
              </ul>
            </div>
          </template>
          <template v-if="tabIndex == 1">
            <FormItem label="车辆号码" prop="plateNo">
              <Input v-model="formData.plateNo" placeholder="请输入"></Input>
            </FormItem>
          </template>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div
      class="footer"
      :class="{ packArrow: packUpDown }"
      @click="handlePackup"
    >
      <img :src="packUrl" alt="" />
      <p>{{ packUpDown ? "展开条件" : "收起条件" }}</p>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />
  </div>
</template>

<script>
import uiUploadImg from "@/components/ui-upload-img/index";
import { mapActions, mapGetters } from "vuex";
export default {
  name: "",
  props: {
    title: {
      type: String,
      default: "人员频次分析",
    },
  },
  components: {
    uiUploadImg,
  },
  data() {
    return {
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      typeList: [{ name: "身份证号" }, { name: "人脸照片" }],
      typeIndex: 0,
      formData: {
        searchContent: "",
        deviceIds: [],
        startDate: "",
        endDate: "",
        dateType: 2,
      },
      ruleValidate: {
        searchContent: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      packUpDown: false,
      similarity: 85,
      algorithmType: "1",
      urlList: [],
      tabIndex: 0,
      selectDeviceList: [],
      checkedLabels: [], // 已选择的标签
    };
  },
  watch: {
    packUpDown: {
      handler(val) {
        this.$nextTick(() => {
          let box = document.querySelector(".search_condition");
          if (val) {
            box.style.overflow = "hidden";
          } else {
            setTimeout(() => {
              box.style.overflow = "inherit";
            }, 200);
          }
        });
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
    }),
  },
  mounted() {
    this.dataRangeHandler(this.$refs.quickDateRef.getDate());
  },
  methods: {
    handleTypeClick(index) {
      this.handleReset();
      this.typeIndex = index;
    },
    handlePackup() {
      this.packUpDown = !this.packUpDown;
      this.$emit("packBox");
      // console.log(document.querySelector('.search_box').scrollHeight, 222)
    },
    imgUrlChange(list) {
      this.urlList = list;
      this.$emit("imgUrlChange", this.urlList);
    },
    handleSearch() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.packUpDown = true;
          let params = { ...this.formData };
          let arr = [];
          if (this.urlList.length > 0) {
            this.urlList.forEach((ele) => {
              if (ele) {
                arr.push(ele.feature);
              }
            });
            params = {
              ...this.formData,
              features: arr,
              similarity: this.similarity,
            };
          }
          if (this.tabIndex == 0 && this.typeIndex == 1 && arr.length == 0) {
            //人员、人脸
            this.$Message.warning("请上传人脸照片！");
            return;
          }
          this.$emit("searchList", params, this.tabIndex, this.typeIndex);
        }
      });
    },
    /**
     * 选择设备
     */
    handleSelemodel() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.formData.deviceIds = list.map((item) => item.deviceGbId);
    },
    handleSeleArea() {
      this.$emit("seleArea");
    },
    // 时间
    dataRangeHandler(val) {
      this.formData.startDate = val.startDate;
      this.formData.endDate = val.endDate;
    },
    // 获取相似度
    handleSimilar() {
      const { searchForPicturesDefaultSimilarity } = this.globalObj;
      this.similarity = Number(searchForPicturesDefaultSimilarity);
    },
    // 重置
    handleReset() {
      this.formData = {
        searchContent: "",
        dateType: 2,
        deviceIds: [],
      };
      this.urlList = [];
      this.handleSimilar();
      this.$nextTick(() => {
        this.dataRangeHandler(this.$refs.quickDateRef.getDate());
        this.$refs.quickDateRef.setDefaultDate(); // 清空日期组件的自定义时间，在下次选择自定义时回显为空
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.search_box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  // min-height: 254px;
  // overflow: hidden;
  // filter: blur(0px);
  .search_condition {
    // padding-bottom: 10px;
    max-height: 360px;
    transition: max-height 0.2s ease-out;
    // overflow: hidden;
    .search_form {
      padding: 0 10px;
      margin-top: 10px;
      /deep/ .custom {
        width: 250px;
      }
      /deep/.ivu-date-picker {
        width: 250px !important;
      }
      &_type {
        display: flex;
        border: 1px solid #2c86f8;
        border-radius: 4px;
        margin-bottom: 15px;
        .typeTab {
          background: #ffffff;
          color: #2c86f8;
          width: 175px;
          height: 34px;
          text-align: center;
          line-height: 34px;
          font-size: 14px;
          cursor: pointer;
        }
        .typeActive {
          background: #2c86f8;
          color: #fff;
        }
      }
      .btn-group {
        .btnwidth {
          width: 258px;
        }
      }
      .search_wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        margin-bottom: 15px;
        .search_title {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-right: 10px;
          display: flex;
        }
        .search_strut {
          text-align: right;
          width: 75px;
          font-weight: bold;
        }
        .search_content {
          display: flex;
          flex-wrap: wrap;
          flex: 1;
          .active-area-sele {
            width: 200px;
            height: 34px;
            border-radius: 4px;
            font-size: 14px;
            text-align: center;
            line-height: 34px;
            cursor: pointer;
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
          }
          .area-sele {
            border: 1px solid #d3d7de;
            color: rgba(0, 0, 0, 0.6);
            background: none;
          }
          .area-list {
            width: 34px;
            height: 34px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #d3d7de;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.6);
            margin-left: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
          }
          .active-area-list {
            border: 1px dashed #2c86f8;
            background: rgba(44, 134, 248, 0.1);
            color: rgba(44, 134, 248, 1);
            img {
              opacity: 0.6;
            }
          }
          .analyze_list {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.6);
            padding: 2px 10px;
            border-radius: 2px;
            border: 1px solid #d3d7de;
            margin-right: 10px;
            margin-bottom: 10px;
            cursor: pointer;
            .active_gradient {
              display: none;
            }
          }
          .active_analyze_list {
            color: rgba(44, 134, 248, 1);
            border: 1px solid rgba(44, 134, 248, 1);
            position: relative;
            .active_gradient {
              position: absolute;
              width: 12px;
              height: 12px;
              background: linear-gradient(
                315deg,
                #2c86f8,
                #2c86f8 50%,
                transparent 50%,
                transparent 100%
              );
              bottom: 0;
              right: 0;
              display: block;
            }
            .ivu-icon-ios-checkmark {
              position: absolute;
              bottom: -3px;
              right: -4px;
              color: #fff;
            }
          }
        }
        .search_text {
          color: rgba(0, 0, 0, 0.8);
          margin-left: 5px;
          font-size: 14px;
        }
      }
    }
    .slider-content {
      margin: 15px 0;
      .similarity {
        color: rgba(0, 0, 0, 0.45);
      }
    }
  }
  .search_condition-pack {
    max-height: 0px;
    transition: max-height 0.2s ease-out;
  }
  .footer {
    display: flex;
    justify-content: center;
    cursor: pointer;
    // margin: 10px 0;
    img {
      transform: rotate(0deg);
      transition: transform 0.2s;
    }
  }
  .packArrow {
    img {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
  /deep/ .ivu-form-item {
    margin-bottom: 20px;
  }
  /deep/ .ivu-slider {
    flex: 1;
  }
  /deep/ .ivu-select-dropdown {
    left: 0 !important;
  }
}
</style>
