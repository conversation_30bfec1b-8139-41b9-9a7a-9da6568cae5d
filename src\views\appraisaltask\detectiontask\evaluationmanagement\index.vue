<template>
  <div class="page-evaluationmanagement auto-fill">
    <div v-if="!componentName" class="evaluat_div">
      <div class="right-content auto-fill">
        <div class="ploe-statistics">
          <statistic-card :statistics-list="statisticsList" @taskSituationShow="taskSituationShow"></statistic-card>
        </div>
        <div class="search-wrapper">
          <ui-label label="任务名称" class="fl">
            <Input
              class="width-md"
              v-model="searchData.taskName"
              placeholder="请输入任务名称"
              @keyup.enter.native="search"
            ></Input>
          </ui-label>

          <ui-label class="ml-lg fl" label="方案类型">
            <Select class="width-md" placeholder="请选择方案类型" clearable v-model="searchData.schemeType">
              <Option v-for="(item, index) in moduleList" :key="index" :value="item.id">{{ item.name }}</Option>
            </Select>
          </ui-label>
          <ui-label :width="0" label=" " class="fl ml-lg">
            <Button type="primary" class="mr-sm fl" @click="search">查询 </Button>
            <Button class="mr-md fl" @click="resetSearchDataMx(searchData, search)"> 重置 </Button>
          </ui-label>
          <ui-label :width="0" label=" " class="fr">
            <Button type="primary" @click="operationTask('add')" class="fr">
              <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="添加"> </i>
              <span class="vt-middle">新增检测任务</span>
            </Button>
          </ui-label>
        </div>
        <div class="table-box auto-fill">
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
            <template #option="{ row }">
              <ui-btn-tip
                icon="icon-bianjirenwu"
                content="编辑任务"
                class="mr-md"
                @click.native="operationTask('edit', row)"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-peizhicanshu-01"
                content="配置参数"
                class="mr-md"
                :class="row.schemeType !== '3' && !row.regionCode ? 'disable-text-color' : 'btn-text-default'"
                @click.native="configuration(row)"
              ></ui-btn-tip>
              <ui-btn-tip
                icon="icon-shanchu3"
                content="删除任务"
                class="mr-md"
                :class="row.schemeType !== '1' ? 'btn-text-default' : 'disable-text-color'"
                @click.native="deleteindex(row)"
              ></ui-btn-tip>
              <create-tabs
                v-if="row.regionCode"
                class="inline btn-text-default"
                :component-name="themData.componentName"
                :important-tab-name="row.taskName + '-' + themData.text"
                :tabs-text="row.taskName"
                :tabs-query="{
                  taskSchemeId: row.taskSchemeId,
                }"
                @selectModule="selectModule"
              >
                <ui-btn-tip
                  class="mr-md"
                  content="指标详情"
                  icon="icon-chakanxiangqing"
                  @click.native="jump"
                ></ui-btn-tip>
              </create-tabs>
              <ui-btn-tip
                v-if="row.schemeType !== '3' && !row.regionCode"
                class="disable-text-color mr-md"
                content="指标详情"
                icon="icon-chakanxiangqing"
              ></ui-btn-tip>
              <!--  @ApiModelProperty("启动或暂停，0暂停，1启动") -->
              <ui-btn-tip
                class="mr-md"
                :content="row.status === 1 ? '启动' : '暂停'"
                :icon="row.status === 1 ? 'icon-qidong' : 'icon-zanting-ivdg'"
                @click.native="pauseAndRunJob(row)"
              ></ui-btn-tip>
            </template>
            <template #schemeType="{ row }">
              {{ getSchemeType(row.schemeType) }}
            </template>
          </ui-table>
        </div>
        <ui-page
          class="page menu-content-background"
          :page-data="searchData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </div>

      <addor-edit-modal
        v-model="addorEditModalShow"
        :org="org"
        :modal-action="addorEditModalAction"
        :modal-data="modalData"
        @update="update"
      >
      </addor-edit-modal>
      <ConfigurationModal v-model="configurationShow" :configuration-action="configurationAction" />
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
    <task-situation v-model="taskSituationVisible"></task-situation>
  </div>
</template>

<style lang="less" scoped>
.page-evaluationmanagement {
  height: 100%;
  .color_qualified {
    color: var(--color-success);
  }
  .color_unqualified {
    color: var(--color-failed);
  }
  .color_cannot {
    color: var(--color-warning);
  }
  .ml_lg {
    margin-left: 20px;
  }
  .deleteicon {
    color: rgba(255, 255, 255, 0.25);
    cursor: no-drop;
  }
  .evaluat_div {
    height: 100%;
    .left-content {
      border-right: 1px solid var(--border-color);
      float: left;
      width: 300px;
      padding: 20px;
      background: var(--bg-content);
      height: 100%;
      .record-title {
        padding: 10px 0;
        .add_case {
          .name {
            margin-left: 10px;
            position: relative;
            top: 2px;
          }
        }
      }
      @{_deep}.el-tree-node__content:hover {
        background-color: #023960;
        position: relative;
      }
      @{_deep}.collapse-content-p {
        border: 1px solid transparent;
        border-radius: 4px;
        padding: 10px;
        color: @font-color-white;
        background: @bg-table-block;
        margin-bottom: 10px;
        &.active {
          border-color: @color-other;
        }
      }
      .assessment-list {
        position: relative;
        @{_deep}.custom-tree-node {
          position: relative;
          height: 26px;
          line-height: 28px;
          font-size: 14px;
        }
        @{_deep}.el-tree-node {
          > .el-tree-node__content {
            position: relative;
            .examine {
              border: 1px solid var(--color-primary);
              // padding: 2px;
              position: absolute;
              right: 10px;
              border-radius: 50%;
              height: 20px;
              width: 20px;
              text-align: center;
              line-height: 18px;
              font-size: 12px;
              margin-top: 3px;
              .examine_text {
                font-size: 12px;
                color: var(--color-primary);
              }
            }
          }
        }
        @{_deep}.el-tree-node {
          &.is-current {
            > .el-tree-node__content {
              background-color: #184f8d;
              position: relative;
              .examine {
                border: 1px solid #fff;
                // padding:2px;
                position: absolute;
                // line-height: 15px;
                right: 10px;
                height: 20px;
                width: 20px;
                text-align: center;
                line-height: 18px;
                border-radius: 50%;
                font-size: 12px;
                margin-top: 3px;
                .examine_text {
                  font-size: 12px;
                  color: #fff;
                }
              }
            }
          }
        }
      }
      @{_deep}.custom-tree-node {
        position: relative;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .ploe-statistics {
      //margin: 20px 20px 0px;
      //background: var(--bg-info-card);
      padding: 20px 20px 0px 20px;
    }
    .right-content {
      float: right;
      width: 100%;
      height: 100%;
      background: var(--bg-content);

      @{_deep}.search-wrapper {
        //height: 35px;
        overflow: hidden;
        padding: 0 20px !important;
        margin: 10px 0px;
      }
      .table-box {
        padding: 0 20px 0 20px;
        position: relative;
        .sucess {
          color: var(--color-success);
        }
        .error {
          color: var(--color-failed);
        }
        .no-data {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        @{_deep} .ivu-table-tbody {
          td {
            padding: 10px 0 10px 0;
          }
        }
        @{_deep} .ivu-table-body {
          td {
            padding: 10px 0 10px 0;
          }
        }
      }
    }
  }
}

.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }
  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}

.btn-text-none {
  font-size: 14px;
  color: var(--color-primary);
}

.ml_ {
  margin-left: 85px;
}
@{_deep}.btn-text-default {
  cursor: pointer !important;
  font-size: 14px !important;
  .icon-zhongxinzhihang {
    cursor: pointer !important;
  }
  .icon-shanchurenwu {
    cursor: pointer !important;
  }
  .icon-bianjirenwu {
    cursor: pointer !important;
  }
  .icon-peizhicanshu-01 {
    cursor: pointer !important;
  }
  .icon-chakanxiangqing {
    color: #de990f !important;
    cursor: pointer !important;
  }
}
@{_deep}.disable-text-color {
  font-size: 14px !important;
  cursor: not-allowed !important;
  color: var(--color-btn-primary-disabled) !important;
  i {
    color: var(--color-btn-primary-disabled) !important;
    cursor: not-allowed !important;
  }
}
</style>
<style lang="less" scoped>
@{_deep}.page-indexmanagement {
  .form-content {
    padding: 0 50px;
    .ivu-form-item {
      margin-bottom: 20px;
      .time {
        width: 22%;
      }
      .lag {
        width: 13.5%;
        margin: 0 10px;
      }
      .canshu {
        width: 15%;
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dealWatch from '@/mixins/deal-watch';
export default {
  name: 'evaluationmanagement',
  mixins: [dealWatch],
  data() {
    return {
      componentName: null,
      componentLevel: 0,
      themData: {
        componentName: 'evaluationDetail', // 需要跳转的组件名
        text: '指标详情', // 跳转页面标题
        title: '指标详情',
        type: 'view',
        // title: "视图基础数据治理主题",
      },
      loading: false,
      addorEditModalShow: false,
      moduleList: [
        { name: '全量数据检测', id: '1' },
        { name: '上报考核检测', id: '2' },
        { name: '临时检测 ', id: '3' },
      ],
      tableColumns: [
        {
          type: 'index',
          tooltip: true,
          width: 50,
          title: '序号',
          align: 'center',
        },
        {
          title: '任务名称',
          key: 'taskName',
          tooltip: true,
          align: 'left',
          minWidth: 200,
        },
        {
          title: '检测对象',
          key: 'regionName',
          tooltip: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: '评测方案',
          key: 'schemeName',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '方案类型',
          tooltip: true,
          align: 'left',
          minWidth: 150,
          slot: 'schemeType',
        },
        {
          title: '指标总数',
          key: 'totalCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '未开始指标',
          key: 'notStartCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '进行中指标',
          key: 'processingCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '已暂停指标',
          key: 'pauseCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '已完成指标',
          key: 'completeCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '执行异常指标',
          key: 'errorCount',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '创建人',
          key: 'creator',
          tooltip: true,
          align: 'left',
          minWidth: 100,
        },
        {
          title: '创建时间',
          key: 'createTime',
          tooltip: true,
          align: 'left',
          minWidth: 150,
        },
        {
          title: '操作',
          slot: 'option',
          align: 'center',
          tooltip: true,
          width: 180,
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      tableData: [],
      minusTable: 288,
      addorEditModalAction: {
        title: '新增',
        action: 'add',
        visible: false,
      },
      modalData: {},
      searchData: {
        taskName: '',
        schemeType: null, //方案
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      editList: {},
      org: '',
      examineData: [],
      configurationShow: false,
      configurationAction: {},
      statisticsList: [
        {
          key: 'taskCount',
          name: '任务数量',
          value: 0,
          icon: 'icon-renwushuliang-01',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          textColor: 'color1',
        },
        {
          key: 'indexTotalCount',
          name: '指标总数',
          value: 0,
          icon: 'icon-zhibiaozongshu-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          textColor: 'color2',
        },
        {
          key: 'processingCount',
          name: '进行中指标',
          value: 0,
          icon: 'icon-jinhangzhong-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number',
          textColor: 'color3',
        },
        {
          key: 'todayCompleteCount',
          name: '今日已完成指标',
          value: 0,
          icon: 'icon-jinriyiwancheng-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          textColor: 'color4',
        },
        {
          key: 'todayPendingCount',
          name: '今日待执行指标',
          value: 0,
          icon: 'icon-jinridaizhihang-01',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          textColor: 'color5',
        },
        {
          key: 'errorCount',
          name: '任务异常指标',
          value: 0,
          icon: 'icon-renwuyichang-01',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          textColor: 'color6',
        },
      ],
      taskSituationVisible: false,
    };
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddorEditModal: require('./addor-edit-modal.vue').default,
    ConfigurationModal: require('./configuration/configuration-modal.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    evaluationDetail: require('@/views/appraisaltask/detectiontask/evaluationmanagement/evaluationDetail/index.vue')
      .default,
    StatisticCard: require('../components/statistic-card.vue').default,
    TaskSituation: require('./task-situation/index.vue').default,
  },
  created() {
    this.getParams();
    this.getTableList();
    this.taskStatistics();
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  mounted() {},
  methods: {
    async pauseAndRunJob({ taskSchemeId, status }) {
      let isOK = await this.$UiConfirm({
        content: `您确定${status === 1 ? '启动' : '暂停'}运行该任务下所有指标吗？`,
      });
      if (!isOK) return;
      this.$Message.success(`全部指标已${status === 1 ? '启动' : '暂停'}运行!`);
      let params = {
        taskSchemeId,
      };
      try {
        let { data } = await this.$http.get(governanceevaluation.pauseStartTaskSchemeJob, {
          params,
        });
        this.$Message.success(data.data);
      } catch (err) {
        console.log('err', err);
      }
    },
    jump() {
      window.sessionStorage.setItem('type', 'view');
    },
    async taskStatistics() {
      try {
        let res = await this.$http.get(governanceevaluation.taskManageStatistics);
        this.statisticsList.forEach((row) => {
          row.value = res.data.data[row.key];
        });
      } catch (err) {
        console.log(err);
      }
    },
    configuration(val) {
      if (val.schemeType !== '3' && !val.regionCode) {
        return;
      }
      this.configurationAction = {
        title: '配置任务参数',
        ...val,
      };
      this.configurationShow = true;
    },
    getSchemeType(schemeType) {
      switch (schemeType) {
        case '1':
          return '全量数据检测';
        case '2':
          return '上报考核检测';
        case '3':
          return '临时检测';
        default: {
          return '';
        }
      }
    },

    async getTableList() {
      let params = {
        taskName: this.searchData.taskName,
        schemeType: this.searchData.schemeType,
        pageNumber: this.searchData.pageNum,
        pageSize: this.searchData.pageSize,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.evaluationPageList, params);
        this.copySearchDataMx(this.searchData);
        this.tableData = res.data.data.entities;
        this.searchData.totalCount = res.data.data.total;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },

    // 新增和编辑弹窗内容
    async operationTask(action, row) {
      if (action === 'edit') {
        this.modalData = row;
      } else {
        this.modalData = {};
      }
      this.addorEditModalShow = true;
      const metaType = {
        add: () =>
          (this.addorEditModalAction = {
            title: '新增检测任务',
            action: 'add',
          }),
        // view: () => (this.addorEditModalAction = { title: '查看', action: 'view' }),
        edit: () =>
          (this.addorEditModalAction = {
            title: '编辑检测任务',
            action: 'edit',
          }),
      };
      metaType[action]();
    },

    // 删除弹窗内容
    deleteindex(row) {
      if (row.schemeType === '1') {
        return;
      }
      this.$UiConfirm({
        content: `您要删除任务：${row.taskName}  这项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteInit(row);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteInit(params) {
      try {
        let res = await this.$http.delete(governanceevaluation.deleteTaskScheme + '/' + params.taskSchemeId);
        this.$Message.success(res.data.msg);
        this.search();
        this.taskStatistics();
      } catch (err) {
        console.log(err);
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.getTableList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.getTableList();
    },

    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.getTableList();
    },

    update() {
      this.search();
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    taskSituationShow() {
      this.taskSituationVisible = true;
    },
  },
};
</script>
