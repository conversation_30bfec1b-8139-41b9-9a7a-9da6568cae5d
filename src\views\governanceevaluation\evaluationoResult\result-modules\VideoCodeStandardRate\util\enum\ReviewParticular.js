import global from '@/util/global';
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  {
    name: '视频监控总量:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'deviceCount',
  },
  {
    name: '实际检测数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'actualNum',
  },
  {
    name: '检测合格数量:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'qualifiedNum',
  },
  {
    name: '视频流编码规范率',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'isImportant',
    label: '设备重点类型',
    placeholder: '请选择设备重点类型',
    options: [
      { value: '0', label: '普通设备' },
      { value: '1', label: '重点设备' },
    ],
  },
  {
    type: 'select',
    key: 'qualified',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
];
export const FaceTableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'orgName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '视频流编码格式',
    key: 'videoCodeTypeText',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测结果',
    slot: 'qualified',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '原因',
    key: 'errorReason',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '检测时间',
    key: 'checkStartDate',
    tooltip: true,
    width: 200,
  },
  {
    title: '设备标签',
    slot: 'tagNames',
    tooltip: true,
    width: 200,
  },
  {
    title: '操作',
    slot: 'option',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];
