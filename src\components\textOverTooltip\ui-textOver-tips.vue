<!--
    * @FileDescription: 对于文字超出，鼠标浮动显示 主要针对自定以不生效标签
    * @Author: H
    * @Date: 2023/5/15
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="text-over-tips">
        <span :ref='refName' @mouseover="onMouseOver(refName)" v-html="content || '--'"></span>
    </div>
</template>

<script>
export default {
    name: '',
    props: {
        content: {
            type:[String, Number],
            default: ''
        },
        // 元素标识，(防止同一页面调用多次，不可重复)
        refName: { 
            type: String,
            default: 'a'
        }
    },
    components:{
            
    },
    data () {
        return {
            
        }
    },
    watch:{
            
    },
    mounted(){
    },
    methods: {
        onMouseOver(name) {
            this.$nextTick(() => {
                let parentWidth = this.$refs[name].parentNode.offsetWidth;
                let contentWidth = this.$refs[name].offsetWidth;
                let box = this.$refs[name];
                if(parentWidth < contentWidth) {
                    box.setAttribute("title", this.content);
                }else{
                    box.removeAttribute('title')
                }
            })
        }
    }
}
</script>

<style lang='less' scoped>
.text-over-tips{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

}
</style>
