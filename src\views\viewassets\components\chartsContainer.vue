<template>
  <div class="charts-container">
    <div
      :class="{ 'charts-item': true, [`bg-${index}`]: true }"
      v-for="(item, index) in abnormalCount"
      :key="index + item.title"
    >
      <!-- 不传icon，展示默认图标 -->
      <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
      <span
        :class="{
          'icon-font': true,
          'f-40': true,
          [item.icon]: true,
          [`icon-${index}`]: true,
        }"
      ></span>
      <div class="number-wrapper">
        <p class="f-12 base-text-color">{{ item.title }}</p>
        <p class="f-18 color-num">{{ item.count | formatNum }}</p>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    abnormalCount: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {},
};
</script>
<style lang="less" scoped>
.charts-container {
  margin-top: 10px;
  display: flex;
  overflow-x: auto;
  justify-content: flex-start;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding-bottom: 2px;
  .charts-item {
    flex-shrink: 0;
    margin-right: 10px;
    width: 304px;
    min-width: 200px;
    height: 88px;
    background: #0f2f59;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    .number-wrapper {
      display: inline-block;
      margin-left: 16px;
    }
  }
  .f-40 {
    font-size: 40px;
  }
  .icon-0 {
    background: var(--icon-card-gradient-cyan);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-1 {
    background: var(--icon-card-gradient-orange);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-2 {
    background: var(--icon-card-gradient-purple);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-3 {
    background: var(--icon-card-gradient-blue);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-4 {
    background: var(--icon-card-gradient-deep-purple);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-5 {
    background: var(--icon-card-gradient-green);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-6 {
    background: var(--icon-card-gradient-red);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-7 {
    background: var(--icon-card-gradient-deep-cyan);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-8 {
    background: var(--icon-card-gradient-grass-green);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-9 {
    background: var(--icon-card-gradient-light-pink);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-10 {
    background: var(--icon-card-gradient-mint-green);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .icon-11 {
    background: var(--icon-card-gradient-pink);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .bg-0 {
    background: var(--bg-card-cyan);
    box-shadow: var(--bg-card-cyan-shadow);
    .color-num {
      color: var(--font-card-cyan);
    }
  }
  .bg-1 {
    background: var(--bg-card-orange);
    box-shadow: var(--bg-card-orange-shadow);
    .color-num {
      color: var(--font-card-orange);
    }
  }
  .bg-2 {
    background: var(--bg-card-purple);
    box-shadow: var(--bg-card-purple-shadow);
    .color-num {
      color: var(--font-card-purple);
    }
  }
  .bg-3 {
    background: var(--bg-card-blue);
    box-shadow: var(--bg-card-blue-shadow);
    .color-num {
      color: var(--font-card-blue);
    }
  }
  .bg-4 {
    background: var(--bg-card-deep-purple);
    box-shadow: var(--bg-card-deep-purple-shadow);
    .color-num {
      color: var(--font-card-deep-purple);
    }
  }
  .bg-5 {
    background: var(--bg-card-green);
    box-shadow: var(--bg-card-green-shadow);
    .color-num {
      color: var(--font-card-green);
    }
  }
  .bg-6 {
    background: var(--bg-card-red);
    box-shadow: var(--bg-card-red-shadow);
    .color-num {
      color: var(--font-card-red);
    }
  }
  .bg-7 {
    background: var(--bg-card-deep-cyan);
    box-shadow: var(--bg-card-deep-cyan-shadow);
    .color-num {
      color: var(--font-card-deep-cyan);
    }
  }
  .bg-8 {
    background: var(--bg-card-grass-green);
    box-shadow: var(--bg-card-grass-green-shadow);
    .color-num {
      color: var(--font-card-grass-green);
    }
  }
  .bg-9 {
    box-shadow: var(--bg-card-light-pink-shadow);
    background: var(--bg-card-light-pink);
    .color-num {
      color: var(--font-card-light-pink);
    }
  }
  .bg-10 {
    background: var(--bg-card-mint-green);
    box-shadow: var(--bg-card-mint-green-shadow);
    .color-num {
      color: var(--font-card-mint-green);
    }
  }
  .bg-11 {
    background: var(--bg-card-pink);
    box-shadow: var(--bg-card-pink-shadow);
    .color-num {
      color: var(--font-card-pink);
    }
  }
}
</style>
