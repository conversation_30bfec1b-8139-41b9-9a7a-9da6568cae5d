<template>
  <ui-modal v-model="visible" :title="title" :styles="styles" :loading="loading" @query="handleSubmit">
    <Form ref="formValidate" :model="formData" :rules="ruleValidate" :label-width="80">
      <FormItem label="指标名称" class="" prop="indexActualName">
        <Input type="text" v-model="formData.indexActualName" :maxlength="20" placeholder="请输入指标名称"> </Input>
      </FormItem>
      <FormItem label="显示名称" class="" prop="indexName">
        <Input type="text" v-model="formData.indexName" :maxlength="20" placeholder="请输入显示名称"> </Input>
      </FormItem>
      <FormItem label="指标类型" prop="indexModule">
        <Select v-model="formData.indexModule" placeholder="请选择指标类型">
          <Option v-for="indexItem in indexModuleList" :key="indexItem.dataKey" :value="indexItem.dataKey">
            {{ indexItem.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="达标值" class="right-item mb-sm" prop="dataTargetValue">
        <div class="ipt-num-wrap">
          <InputNumber
            v-model.number="formData.dataTargetValue"
            :max="100"
            :min="1"
            placeholder="请输入达标值"
            class="ipt-item-width"
          ></InputNumber>
          <span class="params-suffix ml-md">%</span>
        </div>
      </FormItem>
      <FormItem label="计算公式" class="right-item mb-0">
        <span class="index-definition">{{ formData.indexDefinition }}</span>
      </FormItem>
      <FormItem label="评价标准" class="right-item mb-sm" prop="evaluationCriterion">
        <Input
          v-model="formData.evaluationCriterion"
          class="evaluationCriterion"
          type="textarea"
          :rows="7"
          placeholder="要求设备编码、设备名称、功能类型、设备厂家、行政区域、监控点类型、设备经纬度、设备IP地址均填报且正确。"
        />
      </FormItem>
      <p class="font-D66418 mt-md">
        备注：手动新增的指标仅用于统计已有指标（一个或多个）的检测结果，在【评测任务】环节选择检测任务和检测指标，系统把多个指标的检测结果融合分析统计，得到最终结果！
      </p>
    </Form>
  </ui-modal>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'new-index',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      title: '新增指标',
      styles: {
        width: '3.5rem',
      },
      loading: false,
      formData: {
        indexModule: '1',
        dataTargetValue: 100,
        indexDefinition: '合格设备/检测设备总量',
        evaluationCriterion:
          '要求设备编码、设备名称、功能类型、设备厂家、行政区域、监控点位类型、设备经纬度、设备IP地址均填报且正确。',
      },
      ruleValidate: {
        indexActualName: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
        indexModule: [{ required: true, message: '请请选择指标类型', trigger: 'change' }],
        dataTargetValue: [{ required: true, message: '请输入达标值', trigger: 'blur', type: 'number' }],
        evaluationCriterion: [{ required: true, message: '请输入评价标准', trigger: 'blur' }],
        indexName: [{ required: true, message: '请输入显示名称', trigger: 'blur' }],
      },
      indexModuleList: [
        { dataKey: '1', dataValue: '视图基础数据指标' },
        { dataKey: '2', dataValue: '人脸视图数据指标' },
        { dataKey: '3', dataValue: '车辆视图数据指标' },
        { dataKey: '4', dataValue: '视频监控数据指标' },
        { dataKey: '9', dataValue: '人体视图数据指标' },
      ],
    };
  },
  methods: {
    async handleSubmit() {
      const valid = await this.$refs['formValidate'].validate();
      if (!valid) {
        this.$Message.error('请将信息填写完整!');
        return false;
      }
      try {
        this.loading = true;
        await this.$http.post(governanceevaluation.addComposite, this.formData);
        this.$Message.success('新增指标成功!');
        this.$emit('refresh');
        this.visible = false;
      } catch (e) {
        this.loading = false;
        console.log(e);
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
};
</script>

<style lang="less" scoped>
@import url('../../components/common.less');
.ipt-num-wrap {
  width: 100%;
  display: flex;
}
.ipt-item-width {
  flex: 1;
}
.params-suffix {
  display: inline-block;
  color: #ffffff;
  line-height: 34px;
}
.index-definition {
  color: var(--color-primary);
}
</style>
