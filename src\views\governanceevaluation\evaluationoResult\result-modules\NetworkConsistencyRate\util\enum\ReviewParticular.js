import global from '@/util/global';
export const iconStaticsList = [
  {
    name: '视图库人脸卡口总量:',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-yingjianceshebeishuliang',
    fileName: 'beanReportedNum',
  },
  {
    name: '有图片未上报视图库人脸卡口数量:',
    count: '0',
    countStyle: {
      color: '#DE990F',
    },
    iconName: 'icon-shijijianceshebeishuliang',
    fileName: 'unReportedNum',
  },
  {
    name: '联网人脸卡口目录一致率:',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-jiancehegeshebeishu',
    fileName: 'resultValueFormat',
    type: 'percent', // 百分比
  },
];

export const tableColumn = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '行政区划',
    key: 'civilName',
    minWidth: 150,
    tooltip: true,
  },
  {
    title: '不合格原因',
    key: 'reason',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '检测时间',
    key: 'createTime',
    tooltip: true,
    width: 200,
  },
];

export const formItemData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
];
