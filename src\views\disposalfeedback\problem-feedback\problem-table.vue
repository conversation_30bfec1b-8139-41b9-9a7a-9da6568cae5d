<template>
  <div class="problem-table auto-fill mt-sm">
    <ui-table
      ref="uiTable"
      class="ui-table auto-fill"
      :loading="tableLoading"
      :table-columns="FEEDBACK_TABLE_COLUMNS"
      :table-data="showTableData"
      :reserveSelection="true"
      :is-all="isCheckAll"
      row-key="id"
      :default-store-data="defaultStoreData"
      @storeSelectList="storeSelectList"
      @selectTable="selectTable"
    >
      <template #causeCodesText="{ row }">
        <Tooltip placement="bottom" max-width="300">
          <p class="width-md ellipsis">{{ row.causeCodesText && row.causeCodesText.join(', ') }}</p>
          <div slot="content">
            {{ row.causeCodesText?.join(', ') }}
          </div>
        </Tooltip>
      </template>
      <template #submitTime="{ row }">
        <span>{{ returnFormatTime(row.submitTime) || '-' }}</span>
      </template>
      <template #effective="{ row }">
        <span :style="{ color: EFFECTIVE_STATUS[row.effective]?.colorOfTable }">{{
          EFFECTIVE_STATUS[row.effective]?.text
        }}</span>
      </template>
      <template #handleStatus="{ row }">
        <Tag :color="HANDLE_RESULT_STATUS[row.status]?.color">{{ HANDLE_RESULT_STATUS[row.status]?.text }}</Tag>
        <span class="f-12">{{ HANDLE_RESULT_STATUS[row.status]?.supplementText || '' }}</span>
      </template>
      <template #action="{ row }">
        <template v-if="row.operationList.length">
          <ui-btn-tip
            v-for="(item, index) in row.operationList?.slice(0, 3)"
            :key="index"
            :class="['operatbtn', 'mr-sm', item.class || '']"
            :style="[item.style || {}]"
            :icon="item.iconName"
            :content="item.name"
            @click.native="item.func(row)"
          ></ui-btn-tip>
        </template>
        <Poptip
          v-if="row.operationList.length && row.operationList.length > 3"
          placement="bottom-end"
          transfer
          popper-class="custom-transfer-poptip"
          :offset="18"
        >
          <Button type="text" class="operatbtn ellipsisbtn">
            <i class="icon-font icon-gengduo f-12"></i>
          </Button>
          <ul class="operation" slot="content">
            <li v-for="(item, index) in row.operationList.slice(3)" :key="index" @click="item.func(row)">
              {{ item.name }}
            </li>
          </ul>
        </Poptip>
      </template>
    </ui-table>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import {
  HANDLE_STATUS_ENUM,
  HANDLE_RESULT_STATUS,
  FEEDBACK_TABLE_COLUMNS,
  EFFECTIVE_ENUM,
  EFFECTIVE_STATUS,
} from './util/enum.js';
export default {
  props: {
    tableData: {
      type: Array,
      default() {
        return [];
      },
    },
    tableLoading: {
      type: Boolean,
      default() {
        return false;
      },
    },
    isCheckAll: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      FEEDBACK_TABLE_COLUMNS,
      HANDLE_RESULT_STATUS, //处理状态
      EFFECTIVE_STATUS, //有效状态
      defaultStoreData: [],
      selectData: [],
      showTableData: [], //添加了按钮组的表格数据
    };
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    ...mapGetters({
      permisstionsList: 'permission/getPermisstionsList',
      routerList: 'permission/getRouterList',
    }),
  },
  watch: {
    tableData: {
      handler(val) {
        this.showTableData = this.$util.common.deepCopy(val);
        this.setOptionList();
      },
      deep: true,
    },
  },
  methods: {
    storeSelectList(data) {
      this.selectData = this.$util.common.deepCopy(data);
    },
    //转换时间
    returnFormatTime(time) {
      let newTime = new Date(time);
      return this.$util.common.formatDate(newTime);
    },
    //点击删除
    handleDelete(row) {
      // 工单流程或已处理不允许删除；
      if (row.status !== HANDLE_STATUS_ENUM.UNHANDLED) {
        return;
      }
      this.$emit('handleDelete', row);
    },
    handleOption(optionName, isBatch = false, rowObj = {}) {
      this.$emit('handleOption', optionName, isBatch, rowObj);
    },
    //更换搜索条件时清空全选
    clearCheckAll() {
      this.selectData = [];
      this.$nextTick(() => {
        this.$refs.uiTable?.selectAll(false);
      });
    },
    selectTable(selection) {
      this.$emit('selectTable', selection);
    },
    //设置按钮组的显示和隐藏
    async setOptionList() {
      let optionList = {
        view: {
          name: '查看详情',
          func: (row) => this.handleOption('view', false, row),
          iconName: 'icon-chakanxiangqing',
          isShow: () => {
            return true;
          },
        },
        createOrder: {
          name: '生成工单',
          style: { color: '#19AA36' },
          func: (row) => this.handleOption('createOrder', false, row),
          iconName: 'icon-shengchenggongdan',
          isShow: (row, permissionObj) => {
            // 创建工单 状态已经改变不允许生成工单
            return (
              permissionObj[`${routerName}-createorder`] &&
              row.effective === EFFECTIVE_ENUM.EFFECTIVE &&
              row.markManual !== 1 &&
              row.status === HANDLE_STATUS_ENUM.UNHANDLED
            );
          },
        },
        viewOrder: {
          name: '查看工单',
          func: (row) => this.handleViewOrder(row),
          iconName: 'icon-chakangongdan',
          isShow: (row) => {
            return (
              [HANDLE_STATUS_ENUM.PROCESSING, HANDLE_STATUS_ENUM.PROCESSED].includes(row.status) && row.workOrderId
            );
          },
        },
        deal: {
          name: '处理',
          func: (row) => this.handleOption('deal', false, row),
          iconName: 'icon-chuli',
          permission: 'disposefeedback',
          isShow: (row, permissionObj) => {
            return (
              permissionObj[`${routerName}-disposefeedback`] &&
              row.effective !== EFFECTIVE_ENUM.UNCONFIRMED &&
              row.markManual !== 0 &&
              [HANDLE_STATUS_ENUM.UNHANDLED, HANDLE_STATUS_ENUM.PROCESSED].includes(row.status)
            );
          },
        },
        confirm: {
          name: '人工确认',
          class: 'font-orange',
          func: (row) => this.handleOption('confirm', false, row),
          iconName: 'icon-rengongqueren',
          isShow: (row) => {
            return row.status === HANDLE_STATUS_ENUM.UNHANDLED;
          },
        },
        delete: {
          name: '删除',
          func: (row) => this.handleDelete(row),
          iconName: 'icon-shanchu3',
          isShow: (row, permissionObj) => {
            return permissionObj[`${routerName}-deletefeedback`] && row.status === HANDLE_STATUS_ENUM.UNHANDLED;
          },
        },
      };
      //判定按钮权限
      const routerName = this.$route.name;
      let thisPermissionObj = {
        [`${routerName}-createorder`]: false,
        [`${routerName}-disposefeedback`]: false,
        [`${routerName}-deletefeedback`]: false,
      };
      this.permisstionsList.forEach((item) => {
        if (Object.keys(thisPermissionObj).includes(item)) {
          thisPermissionObj[item] = true;
        }
      });
      //添加按钮
      this.showTableData.forEach((row) => {
        this.$set(row, 'operationList', []);
        for (let key in optionList) {
          optionList[key].isShow(row, thisPermissionObj) && row.operationList.push(optionList[key]);
        }
      });
    },
    //跳转到查看工单
    handleViewOrder(row) {
      let hasPermission = this.routerList.findIndex((item) => item.name === 'myworkorder');
      if (hasPermission === -1) {
        this.$Message.warning({
          content: '您暂无治理工单的权限，请联系管理员添加!',
          duration: 3,
        });
        return;
      }
      //页面跳转到治理工单
      this.$router.push({
        name: 'myworkorder',
        params: {
          purpose: 'openOrderDetailModalById', //目的
          detailId: row.workOrderId,
        },
      });
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.problem-table {
  .ui-table {
    width: 100%;
    height: 700px;
  }
  @{_deep}.ivu-table-header {
    width: auto;
  }
}
.operation {
  li {
    padding: 6px 18px;
    color: var(--color-content);
    cursor: pointer;
    &:hover {
      background: var(--bg-select-item-active);
      color: var(--color-select-item-active);
    }
  }
}
</style>
