<template>
  <ui-modal
    v-model="visible"
    title="快捷应用配置"
    :r-width="1010"
    @onOk="comfirmHandle"
    @onCancel="handleCancel"
  >
    <div class="content">
      <div class="content-left">
        <div class="content-left-title">
          应用列表<span>({{ leftList.length }})</span>
        </div>
        <ul>
          <draggable
            :list="leftList"
            :group="usegroup"
            ghost-class="ghost"
            chosenClass="chosen"
            animation="300"
          >
            <transition-group class="modulemuen-list">
              <li
                class="modulemuen move"
                v-for="(item, index) in leftList"
                :key="index"
              >
                <div class="modulemuen-child">
                  {{ item.resourceCname }}
                </div>
              </li>
            </transition-group>
          </draggable>
        </ul>
      </div>
      <div class="content-right">
        <div class="centerdotted">
          <div class="centerCircle">
            <div class="centerCircle-msg">
              <div>
                <span>{{ menuList.length }}</span
                >/10
              </div>
              <p>已添加</p>
            </div>
          </div>
        </div>
        <!-- <div class="circle-content"> -->
        <transition-group tag="div" class="circle-content">
          <div
            class="circle-list"
            v-for="(item, index) in rightList"
            :key="index"
            :draggable="item.ident == 1"
            @dragstart="handleDragStart($event, item)"
            @dragover.prevent="handleDragOver($event, item)"
            @dragenter="handleDragEnter($event, item)"
            @dragend="handleDragEnd($event, item)"
            :class="'item' + index"
          >
            <draggable
              :class="'items' + index"
              :list="[rightList[index]]"
              :group="rightgroup[index]"
              @change="handleright"
              @add="handleAdd"
              ghost-class="ghost"
              chosenClass="chosen"
              :move="onMove"
            >
              <div class="circle" v-if="item.ident === 1">
                <div class="circle-child modulemuen-child">
                  {{ item.resourceCname }}
                </div>
                <div class="shadowDele">
                  <i
                    class="iconfont icon-shanchu"
                    @click="handleDelemenu(index)"
                  ></i>
                </div>
              </div>
              <div v-else class="vacancy" :class="'vacancy' + index">
                <div>
                  <Icon type="md-add" :size="24" />
                </div>
              </div>
            </draggable>
          </div>
        </transition-group>
        <!-- </div> -->
      </div>
    </div>
    <div class="localoading" v-if="loading">
      <ui-loading v-if="loading"></ui-loading>
    </div>
  </ui-modal>
</template>

<script>
import Setting from "@/libs/configuration/setting";
import draggable from "vuedraggable";
import { resourceList, motifyMyQuickApplication } from "@/api/home";
import { mapGetters } from "vuex";
export default {
  name: "",
  components: {
    draggable,
  },
  props: {
    list: {
      //已展示的
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      visible: false,
      usegroup: {
        name: "menu",
        pull: true,
        put: false,
      },
      rightgroup: [],
      menuList: [],
      leftList: [],
      rightList: [
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
        { ident: 0 },
      ],
      newObject: {},
      loading: false,
      ending: null,
      dragging: null,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({ userInfo: "userInfo" }),
  },
  created() {},
  mounted() {},
  methods: {
    handleDragStart(e, item) {
      if (item.ident === 0) {
        return;
      }
      this.dragging = item;
    },
    handleDragEnd(e, item) {
      if (item.ident === 0) {
        return;
      }
      if (this.ending.resourceCname === this.dragging.resourceCname) {
        return;
      }
      let newItems = [...this.rightList];
      const src = newItems.indexOf(this.dragging);
      const dst = newItems.indexOf(this.ending);
      newItems.splice(src, 1, ...newItems.splice(dst, 1, newItems[src]));
      this.rightList = newItems;
      this.$nextTick(() => {
        this.dragging = null;
        this.ending = null;
      });
    },
    handleDragOver(e) {
      // 首先把div变成可以放置的元素，即重写dragenter/dragover
      // 在dragenter中针对放置目标来设置
      e.dataTransfer.dropEffect = "move";
    },
    handleDragEnter(e, item) {
      if (item.ident === 0) {
        return;
      }
      // 为需要移动的元素设置dragstart事件
      e.dataTransfer.effectAllowed = "move";
      this.ending = item;
    },
    dataTreating() {
      this.menuList = this.rightList.filter((item) => item.ident);
      this.rightList.forEach((item) => {
        if (item.ident == 0) {
          this.rightgroup.push({
            name: "menu",
            pull: false,
            put: true,
          });
        } else {
          this.rightgroup.push({
            name: "menu",
            pull: false,
            put: false,
          });
        }
      });
    },
    show() {
      let jurisMenu = [];
      this.userInfo.sysApplicationVoList.forEach((item) => {
        if (item.applicationCode === applicationCode) {
          jurisMenu.push(...item.children);
        }
      });
      let newMenu = this.uselist(jurisMenu);
      let mapMenu = new Map(newMenu.map((item) => [item.id, item]));
      this.visible = true;
      this.handleCancel();
      resourceList().then((res) => {
        // 获取所有目录集合
        let lists = this.uselist(res.data);
        let listdata = [];
        lists.map((item) => {
          item.ident = 0;
          item.resourceId = item.id;
          if (mapMenu.get(item.resourceId) && item.isExternalLink != -1) {
            listdata.push(item);
          }
        });
        // 获取右边已存在的目录
        if (this.list.length > 0) {
          this.leftList = listdata.filter(
            (item) => !this.list.some((ele) => ele.resourceId == item.id)
          );
        } else {
          this.leftList = listdata;
        }
        console.log(this.leftList, "this.leftList");
        this.list.forEach((item) => {
          if (item.sort >= 0) {
            item.ident = 1;
            item.resourceCname = item.resourceName;
            this.$set(this.rightList, item.sort, item);
          }
        });
        this.dataTreating();
      });
    },
    // 处理目录数据
    uselist(list) {
      let menu = [];
      list.forEach((item, index) => {
        if (item.children && item.children.length > 0) {
          menu.push(...item.children);
        } else {
          menu.push(item);
        }
      });
      return menu;
    },
    // 确定
    comfirmHandle() {
      let params = [];
      this.rightList.map((item, index) => {
        item.sort = index;
      });
      this.rightList.map((item) => {
        if (item.ident !== 0) {
          params.push(item);
        }
      });
      if (params.length == 0) {
        this.$Message.warning("我的快捷应用已添加为空!");
        return;
      }
      this.loading = true;
      motifyMyQuickApplication(params)
        .then((res) => {
          if (res.code == 200) {
            this.visible = false;
            this.$Message.success("配置成功");
            this.$emit("motifyquery");
          }
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消
    handleCancel() {
      this.rightList = Array.apply(null, { length: 10 }).map(() => {
        return { ident: 0 };
      });
    },
    judgeDrag() {
      this.menuList = this.rightList.filter((item) => item.ident);
      if (this.menuList.length == 10) {
        this.usegroup = {
          name: "menu",
          pull: false,
          put: false,
        };
      } else {
        this.usegroup = {
          name: "menu",
          pull: true,
          put: false,
        };
      }
    },
    handleAdd(val) {
      let clsIndex = val.target._prevClass.split("");
      let numIndex = Number(clsIndex[clsIndex.length - 1]);
      let resourceId = this.newObject.resourceId || this.newObject.id;
      this.$set(this.rightList, numIndex, {
        ...this.newObject,
        sort: numIndex,
        resourceId: resourceId,
      });
      this.$set(this.rightgroup, numIndex, {
        name: "menu",
        pull: false,
        put: false,
      });
      this.judgeDrag();
    },
    onMove(e) {
      return false;
    },
    handleright(val) {
      val.added.element.ident = 1;
      this.newObject = val.added.element;
    },
    // 删除
    handleDelemenu(index) {
      this.leftList.push(this.rightList[index]);
      this.$set(this.rightList, index, { ident: 0 });
      this.$set(this.rightgroup, index, {
        name: "menu",
        pull: false,
        put: true,
      });
      this.menuList = this.rightList.filter((item) => item.status);
      this.judgeDrag();
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  height: 600px;
  display: flex;
  .move {
    cursor: move;
  }
  .content-left {
    width: 380px;
    height: 100%;
    border: 1px solid #d3d7de;
    .content-left-title {
      font-size: 14px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      background: #f9f9f9;
      border-bottom: 1px solid #d3d7de;
      padding: 10px 16px;
      span {
        color: #2c86f8;
      }
    }
    ul {
      max-height: calc(~"100% - 42px");
      overflow: auto;
      // overflow: visible;
    }
  }
  .modulemuen-list {
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    overflow: visible;
    padding-bottom: 20px;
    .modulemuen:hover {
      border: 3px solid #4597ff;
      box-shadow: 0px 5px 15px 0px rgba(44, 134, 248, 0.72);
    }
  }
  .modulemuen {
    width: 100px;
    height: 100px;
    margin: 10px 10px;
    border-radius: 50%;
    background: linear-gradient(
      180deg,
      rgba(91, 163, 255, 0.2),
      rgba(44, 134, 248, 0.2)
    );
    box-shadow: 0px 10px 24px 0px rgba(44, 134, 248, 0.2);
    // box-shadow:0px 5px 10px 0px rgba(44,134,248,0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    .modulemuen-child {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 80px;
      border-radius: 50%;
      background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
      color: #fff;
      font-size: 14px;
      font-weight: bold;
      word-break: keep-all;
      letter-spacing: 1px;
    }
  }
  .content-right {
    border: 1px solid #d3d7de;
    border-left: none;
    height: 100%;
    width: calc(~"100% - 380px");
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .centerdotted {
      width: 252px;
      height: 252px;
      border-radius: 50%;
      border: 3px dotted rgba(44, 134, 248, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .centerCircle {
      width: 208px;
      height: 208px;
      border-radius: 50%;
      background: linear-gradient(
        180deg,
        rgba(44, 134, 248, 0.04),
        rgba(44, 134, 248, 0.04)
      );
      display: flex;
      justify-content: center;
      align-items: center;
      .centerCircle-msg {
        width: 160px;
        height: 160px;
        border-radius: 50%;
        background: rgba(44, 134, 248, 0.05);
        font-size: 22px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        span {
          color: rgba(44, 134, 248, 1);
        }
        p {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
    .circle-content {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 100px;
      height: 100px;
      transform: translate(-50%, -50%);
      border-radius: 50%;
      z-index: 9;
      display: flex;
      justify-content: center;
      align-items: center;
      .modulemuen {
        display: none;
      }
      .circle-list {
        position: absolute;
        width: 100px;
        height: 100px;
        top: -8px;
        z-index: 10;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        .circle {
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background: linear-gradient(
            180deg,
            rgba(91, 163, 255, 0.2),
            rgba(44, 134, 248, 0.2)
          );
          box-shadow: 0px 10px 24px 0px rgba(44, 134, 248, 0.2);
          display: flex;
          justify-content: center;
          align-items: center;
          .shadowDele {
            width: 100%;
            height: 100%;
            position: absolute;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            opacity: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #fff;
            cursor: move;
            .icon-shanchu {
              cursor: pointer;
            }
          }
          &:hover {
            .shadowDele {
              opacity: 1;
            }
          }
        }
        .circle-child {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 80px;
          height: 80px;
          border-radius: 50%;
          background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
          color: #fff;
          font-size: 14px;
          font-weight: bold;
          word-break: keep-all;
          font-weight: 800;
          letter-spacing: 1px;
        }
      }
      .vacancy {
        border: 1px dotted #d3d7de;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: #ffffff;
        cursor: pointer;
        font-size: 20px;
        color: #d8d8d8;
        &:hover {
          border: 2px dotted #2c86f8;
          background: rgba(44, 134, 248, 0.1);
          color: #2c86f8;
        }
        .icon-jia {
          font-size: 35px;
        }
        div {
          width: 100%;
          height: 100%;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .item0 {
        transform: rotateZ(36deg) translateY(200px);
        .circle,
        .vacancy0 {
          transform: rotateZ(324deg);
        }
      }
      .item1 {
        transform: rotateZ(72deg) translateY(200px);
        .circle,
        .vacancy1 {
          transform: rotateZ(288deg);
        }
      }
      .item2 {
        transform: rotateZ(108deg) translateY(200px);
        .circle,
        .vacancy2 {
          transform: rotateZ(252deg);
        }
      }
      .item3 {
        transform: rotateZ(144deg) translateY(200px);
        .circle,
        .vacancy3 {
          transform: rotateZ(216deg);
        }
      }
      .item4 {
        transform: rotateZ(180deg) translateY(200px);
        .circle,
        .vacancy4 {
          transform: rotateZ(180deg);
        }
      }
      .item5 {
        transform: rotateZ(216deg) translateY(200px);
        .circle,
        .vacancy5 {
          transform: rotateZ(144deg);
        }
      }
      .item6 {
        transform: rotateZ(252deg) translateY(200px);
        .circle,
        .vacancy6 {
          transform: rotateZ(108deg);
        }
      }
      .item7 {
        transform: rotateZ(288deg) translateY(200px);
        .circle,
        .vacancy7 {
          transform: rotateZ(72deg);
        }
      }
      .item8 {
        transform: rotateZ(324deg) translateY(200px);
        .circle,
        .vacancy8 {
          transform: rotateZ(36deg);
        }
      }
      .item9 {
        transform: rotateZ(360deg) translateY(200px);
        .circle,
        .vacancy9 {
          transform: rotateZ(0deg);
        }
      }
    }
  }
  .ghost {
    // border: 1px solid red;
    background: rgba(44, 134, 248, 0.1);
    cursor: move;
    box-shadow: unset !important;
  }
  .chosen {
    cursor: move;
    box-shadow: unset !important;
  }
}
.localoading {
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  left: 45%;
  transform: translate(-50%, -50%);
}
/deep/ .ivu-btn {
  width: 72px;
}
</style>
