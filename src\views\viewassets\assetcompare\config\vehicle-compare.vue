<template>
  <div v-ui-loading="{ loading: loading }" class="height-full">
    <span class="tips mt-md">说明：资产库和本级联网平台、人脸视图库和车辆视图库定期对账，找出差异设备！</span>
    <div class="config-item mt-sm">
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">检测对象</span>
        </div>
        <div class="mt-lg">
          <ui-label class="inline mb-10" label="检测对象">
            <api-area-tree
              :select-tree="configInfo"
              @selectedTree="selectedArea"
              placeholder="请选择检测对象"
            ></api-area-tree>
          </ui-label>
        </div>
      </div>
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">对账计划</span>
          <i-switch
            class="mr-sm"
            v-model="configInfo.turnedOn"
            :true-value="1"
            :false-value="0"
          >
          </i-switch>
        </div>
        <div class="mt-lg">
          <TestPlan
            :form-data="configInfo"
            :form-model="'edit'"
            @checkTime="checkTime"
          ></TestPlan>
        </div>
      </div>
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">资产库数据范围</span>
        </div>
        <div class="mt-lg">
          <ui-label class="inline" label="功能类型">
            <Select class="width-md" v-model="configInfo.deviceSbgnlx" placeholder="请选择功能类型" clearable>
              <Option v-for="(item, index) in propertySearchLbgnlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>

          <ui-label class="inline mb-10 ml-lg" label="数据来源">
            <Select class="width-md" v-model="configInfo.deviceSource" placeholder="请选择数据来源" clearable>
              <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
            </Select>
          </ui-label>

          <ui-label class="inline mb-10 ml-lg" :label="global.filedEnum.phyStatus">
            <Select
              class="width-md"
              v-model="configInfo.devicePhyStatus"
              :placeholder="`请选择${global.filedEnum.phyStatus}`"
              clearable
            >
              <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
            </Select>
          </ui-label>
        </div>
      </div>
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">联网平台数据范围</span>
        </div>
        <div class="mt-lg">
          <span class='f-14 font-white '>车辆识别</span>
        </div>
      </div>
      <div class="subclass-config">
        <div class="subclass-title">
          <span class="title">请确定比对字段</span>
        </div>
        <div class="mt-lg">
          <transfer-table
            class="transfer-table"
            :left-table-columns="columns1"
            :right-table-columns="columns2"
            :left-table-data="filterPropertyList"
            :right-table-data="configInfo.compareField || []"
            @onLeftToRight="selectionChange"
            :leftLoading="leftLoading"
            :rightLoading="rightLoading"
          >
          </transfer-table>
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';
export default {
  props: {
    propertyList: {
      type: Array,
    },
    config: {
      type: Object,
    },
  },
  data() {
    return {
      loading: false,
      configInfo: {
        compareParam: [],
        deviceSbgnlx: '',
        deviceSource: '',
        devicePhyStatus: '',
        regionCode: '',
        cronType: '',
        cronData: [],
        timePoints: [],
        compareField: [],
        turnedOn: 0,
      },
      columns1: [
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      columns2: [
        { title: ' ', align: 'center', width: 20 },
        {
          title: '字段名',
          key: 'fieldName',
        },
        {
          title: '注释',
          key: 'fieldRemark',
        },
      ],
      leftLoading: false,
      rightLoading: false,

    };
  },
  computed: {
    ...mapGetters({
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
    filterPropertyList() {
      return this.propertyList.map(item => {
        let isChecked = this.configInfo.compareField && this.configInfo.compareField.find(val=> val.fieldName === item.propertyName)
        return {
          ...item,
          _checked: !!isChecked
        }
      })
    },
  },
  created() {
  },
  methods: {
    checkTime(val) {
      this.$set(this.configInfo, 'cronData', val.cronData);
      this.$set(this.configInfo, 'cronType', val.cronType);
      this.$set(this.configInfo, 'timePoints', val.timePoints);
    },
    selectedArea(area) {
      this.configInfo.regionCode = area.regionCode;
    },
    selectionChange(selection) {
      // this.targetList = selection.map((item) => {
      //   let obj = {};
      //   obj.checkColumnName = item.propertyName;
      //   obj.checkColumnValue = item.propertyColumn;
      //   return obj;
      // });
      this.configInfo.compareField = selection.map((item) => {
        let obj = {};
        obj.fieldName = item.propertyName;
        obj.fieldRemark = item.propertyColumn;
        return obj;
      })
    },
    save() {
      this.$emit('save', this.configInfo);
    },
  },
  watch: {
    config: {
      deep: true,
      immediate: true,
      handler(val) {
        this.configInfo = {
          ...this.configInfo,
          ...val
        };
      },
    }
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    TestPlan: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/test-plan.vue').default,
  },
};
</script>
<style lang="less" scoped>
.tips {
  color: #e44f22;
}

.config-item {
  border: 1px solid #062f68;
  padding: 10px 20px;
  color: #fff;
  margin-bottom: 20px;
  .config-title {
    i {
      color: #2b84e2;
    }
    span {
      color: #fff;
    }
    padding-bottom: 10px;
    border-bottom: 1px solid #062f68;
  }
  @{_deep}.ivu-tooltip-inner {
    max-width: fit-content;
  }
  .subclass-config {
    .subclass-title {
      margin-top: 20px;
      .title {
        display: inline-block;
        width: 210px;
        color: #2b84e2;
        font-weight: 900;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 14px;
          background-color: #2b84e2;
          vertical-align: middle;
          margin-right: 5px;
        }
      }
    }

    .transfer-table {
      height: 500px;
      @{_deep} .left-table {
        height: calc(100% - 40px) !important;
      }
      @{_deep} .right-table {
        height: calc(100% - 40px) !important;
      }
      .ivu-select {
        background: #02162b;
        border: 1px solid #10457e;
        border-radius: 5px;
      }
    }

  }
  .type {
    width: 200px;
  }
}
.icon-wenhao {
  color: #d66418;
}
</style>
