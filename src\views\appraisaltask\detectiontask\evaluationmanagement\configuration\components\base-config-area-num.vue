<template>
  <div class="base-config-area-num.vue">
    <div class="base-text-color f-16 mb-sm search">
      <span>重点必报采集区域达标数量</span>
      <div class="inline">
        <ui-label required class="inline" label="数据类型" :width="70">
          <Select class="width-sm" v-model="importantForm.type" placeholder="请选择行政区划">
            <Option v-for="(item, index) in typeOptions" :value="item.value" :key="`${index}-${item.value}`">{{
              item.label
            }}</Option>
          </Select>
        </ui-label>
        <ui-label required class="inline ml-lg" label="达标数量" :width="70">
          <InputNumber
            class="width-xs"
            :min="0"
            v-model="importantForm.value"
            placeholder="请输入达标数量"
          ></InputNumber>
        </ui-label>

        <Button class="ml-lg" type="primary" @click="clickBatchInput('importantCjqyList', 'importantForm')"
          >批量填入</Button
        >
      </div>
    </div>
    <ui-table class="ui-table" :table-columns="tableColumns('重点')" :table-data="importantCjqyList">
      <template #faceConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          :min="0"
          v-model="importantCjqyList[index]['faceConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
      <template #vehicleConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          v-model="importantCjqyList[index]['vehicleConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
      <template #videoConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          v-model="importantCjqyList[index]['videoConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
    </ui-table>
    <div class="base-text-color f-16 mb-sm mt-sm search">
      <span>其他必报采集区域达标数量</span>
      <div>
        <ui-label required class="inline" label="数据类型" :width="70">
          <Select class="width-sm" v-model="otherForm.type" placeholder="请选择行政区划">
            <Option v-for="(item, index) in typeOptions" :value="item.value" :key="`${index}-${item.value}`">{{
              item.label
            }}</Option>
          </Select>
        </ui-label>
        <ui-label required class="inline ml-lg" label="达标数量" :width="70">
          <InputNumber class="width-xs" :min="0" v-model="otherForm.value" placeholder="请输入达标数量"></InputNumber>
        </ui-label>
        <Button class="ml-lg" type="primary" @click="clickBatchInput('otherCjqyList', 'otherForm')">批量填入</Button>
      </div>
    </div>
    <ui-table class="ui-table auto-fill" :table-columns="tableColumns('其他')" :table-data="otherCjqyList">
      <template #faceConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          v-model="otherCjqyList[index]['faceConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
      <template #vehicleConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          v-model="otherCjqyList[index]['vehicleConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
      <template #videoConfigNum="{ index }">
        <InputNumber
          class="width-xs"
          v-model="otherCjqyList[index]['videoConfigNum']"
          placeholder="请输入上报数量"
        ></InputNumber>
      </template>
    </ui-table>
  </div>
</template>

<script>
export default {
  name: 'config-area-num',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  props: {
    value: {},
    data: {}, //所有的采集区域
  },
  data() {
    return {
      styles: {
        width: '5rem',
      },
      visible: false,
      loading: false,
      importantCjqyList: [],
      otherCjqyList: [],
      tableData: [],
      importantForm: {
        type: 'all',
        value: null,
      },
      otherForm: {
        type: 'all',
        value: null,
      },
      typeOptions: [
        { label: '全部', value: 'all' },
        { label: '人脸卡口', value: 'faceConfigNum' },
        { label: '车辆卡口', value: 'vehicleConfigNum' },
        { label: '视频监控', value: 'videoConfigNum' },
      ],
    };
  },
  computed: {},
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.resetForm();
      }
    },
    data: {
      deep: true,
      immediate: true,
      handler: function (val) {
        let data = this.$util.common.deepCopy(val || []);
        this.otherCjqyList = [];
        this.importantCjqyList = [];
        data.forEach((item) => {
          if (item.cjqyType === 0) {
            this.importantCjqyList.push({
              ...item,
              faceConfigNum: item.faceConfigNum || null,
              videoConfigNum: item.videoConfigNum || null,
              vehicleConfigNum: item.vehicleConfigNum || null,
            });
          } else {
            this.otherCjqyList.push({
              ...item,
              faceConfigNum: item.faceConfigNum || null,
              videoConfigNum: item.videoConfigNum || null,
              vehicleConfigNum: item.vehicleConfigNum || null,
            });
          }
        });
      },
    },
  },
  filter: {},
  mounted() {},
  methods: {
    tableColumns(title) {
      return [
        { title: '序号', type: 'index', width: 50, align: 'center' },
        { title: '采集区域编码', key: 'key', width: 100 },
        { title: `${title}必填采集区域`, key: 'value', minWidth: 200 },
        { title: '人脸卡口需上报数量', slot: 'faceConfigNum', minWidth: 120 },
        { title: '车辆卡口需上报数量', slot: 'vehicleConfigNum', minWidth: 120 },
        { title: '视频监控需上报数量', slot: 'videoConfigNum', minWidth: 120 },
      ];
    },
    resetForm() {
      this.importantForm = {
        type: 'all',
        value: null,
      };
      this.otherForm = {
        type: 'all',
        value: null,
      };
    },
    query() {
      return { importList: this.importantCjqyList, otherList: this.otherCjqyList };
    },
    /**
     * @param form 区分普通和重点批量输入
     * @param CjqyList 区分普通和重点列表
     */
    clickBatchInput(CjqyList, form) {
      this[CjqyList].forEach((item) => {
        if (this[form].type === 'all') {
          this.$set(item, 'faceConfigNum', this[form].value || null);
          this.$set(item, 'vehicleConfigNum', this[form].value || null);
          this.$set(item, 'videoConfigNum', this[form].value || null);
        } else {
          this.$set(item, this[form].type, this[form].value || null);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ui-table {
  height: 300px;
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
