<template>
  <div :class="isResult ? 'governancetoolset-child-result' : 'governancetoolset-child'">
    <div class="child-head">
      <div class="head"><i class="icon-font icon-shujujiancegongju"></i><span class="title">空间信息检测</span></div>
      <Button type="text" class="btn-back mr-sm" @click="backHandle">&lt;&lt; 返回</Button>
    </div>
    <div v-show="!isResult" class="governancetoolset-child-container">
      <Form ref="formData" :model="formData" :label-width="158" label-position="left" class="set-form">
        <FormItem prop="selectDeviceType" label="1、选择设备：">
          <RadioGroup v-model="formData.selectDeviceType">
            <Radio label="1">离线文件</Radio>
            <Radio label="2">系统选择</Radio>
          </RadioGroup>
          <div class="btns" v-if="formData.selectDeviceType === '1'">
            <FormItem
              prop="spatialInformationVoList"
              :rules="{
                required: true,
                type: 'array',
                message: '请上传文件',
                trigger: 'change',
              }"
            >
              <Upload
                action="/ivdg-device-data-service/governance/importExcel"
                :headers="headers"
                :data="uploadData"
                :show-upload-list="false"
                :beforeUpload="beforeUpload"
                :on-success="importSuccess"
                :on-error="importError"
                ref="upload"
              >
                <Button
                  custom-icon="icon-font icon-shangchuananmobantianxiedeexcelwenjian-01"
                  :loading="uploadLoading"
                  class="upload-btn"
                >
                  <span class="btn-text ellipsis">
                    {{ fileName ? fileName : '上传按模板填写的excel文件' }}
                  </span>
                </Button>
              </Upload>
            </FormItem>
            <Button type="text" :loading="downloadLoading" @click="downloadHandle">下载模板</Button>
          </div>
          <template v-else>
            <FormItem
              prop="spatialInformationVoList"
              :rules="{
                required: true,
                type: 'array',
                message: '请选择设备',
                trigger: 'change',
              }"
            >
              <div class="select-device" @click="selectDeviceBtn">
                <i v-show="!formData.spatialInformationVoList.length" class="icon-font icon-shitujichushuju"></i>
                <span class="select-device-text">{{
                  formData.spatialInformationVoList.length
                    ? `已选择${formData.spatialInformationVoList.length}条数据`
                    : '请选择设备'
                }}</span>
              </div>
            </FormItem>
          </template>
        </FormItem>
        <FormItem
          prop="testing"
          label="2、确定检测项："
          class="label-width"
          :rules="[{ validator: validate1, trigger: 'change' }]"
        >
          <div class="checkbox-wrapper">
            <div class="form-item">
              <FormItem prop="lonLatAccuracy">
                <Checkbox v-model="formData.lonLatAccuracy" label=""></Checkbox>
              </FormItem>
              <FormItem
                label="经纬度精度：不少于"
                prop="accuracy"
                :rules="[
                  {
                    required: formData.lonLatAccuracy,
                    type: 'number',
                    message: '请输入',
                    trigger: 'blur',
                  },
                ]"
              >
                <InputNumber v-model="formData.accuracy" :disabled="!formData.lonLatAccuracy" /><span
                  class="second-text"
                  >位小数</span
                >
              </FormItem>
            </div>
            <div class="form-item">
              <FormItem prop="lonRepeat">
                <Checkbox v-model="formData.lonRepeat" label=""></Checkbox>
              </FormItem>
              <FormItem
                label="经度重复：不大于"
                prop="longitude"
                :rules="[
                  {
                    required: formData.lonRepeat,
                    type: 'number',
                    message: '请输入',
                    trigger: 'blur',
                  },
                ]"
              >
                <InputNumber v-model="formData.longitude" :disabled="!formData.lonRepeat" /><span class="second-text"
                  >次</span
                >
              </FormItem>
            </div>
            <div class="form-item">
              <FormItem prop="latRepeat">
                <Checkbox v-model="formData.latRepeat" label=""></Checkbox>
              </FormItem>
              <FormItem
                label="纬度重复：不大于"
                prop="latitude"
                :rules="[
                  {
                    required: formData.latRepeat,
                    type: 'number',
                    message: '请输入',
                    trigger: 'blur',
                  },
                ]"
              >
                <InputNumber v-model="formData.latitude" :disabled="!formData.latRepeat" /><span class="second-text"
                  >次</span
                >
              </FormItem>
            </div>
            <div class="form-item">
              <FormItem prop="addressRepeat">
                <Checkbox v-model="formData.addressRepeat" label=""></Checkbox>
              </FormItem>
              <FormItem
                label="地址重复：不大于"
                prop="address"
                :rules="[
                  {
                    required: formData.addressRepeat,
                    type: 'number',
                    message: '请输入',
                    trigger: 'blur',
                  },
                ]"
              >
                <InputNumber v-model="formData.address" :disabled="!formData.addressRepeat" /><span class="second-text"
                  >次</span
                >
              </FormItem>
            </div>
            <div class="form-item">
              <FormItem prop="crossTheBorder">
                <Checkbox v-model="formData.crossTheBorder" label=""></Checkbox>
              </FormItem>
              <FormItem label="经纬度越界：超出所属行政区划"></FormItem>
            </div>
            <div class="form-item">
              <FormItem prop="deviationBool">
                <Checkbox v-model="formData.deviationBool" label=""></Checkbox>
              </FormItem>
              <FormItem
                label="经纬度与地址偏移：不大于"
                prop="deviation"
                :rules="[
                  {
                    required: formData.deviationBool,
                    type: 'number',
                    message: '请输入',
                    trigger: 'blur',
                  },
                ]"
              >
                <InputNumber v-model="formData.deviation" :disabled="!formData.deviationBool" /><span
                  class="second-text"
                  >米</span
                >
              </FormItem>
            </div>
          </div>
          <FormItem ref="testing" label="" class="testing"><span class="testing-text">请选择检查项</span></FormItem>
        </FormItem>
      </Form>
      <Button type="primary" class="submit-btn" @click="submitHandle('formData')">确定</Button>
    </div>
    <!-- 检测结果 -->
    <detection-result ref="detectionResult" v-show="isResult" :data-obj="formData"></detection-result>
    <!-- 选择设备 -->
    <select-device-modal
      ref="selectDeviceModle"
      v-if="selectModeVisible"
      :toolType="formData.type"
      :selectedDeviceList="selectedDeviceList"
      @setDeviceHandle="setDeviceHandle"
    />
  </div>
</template>
<script>
import governancetoolset from '@/config/api/governancetoolset';
export default {
  name: 'ClockInformationDetection',
  components: {
    'detection-result': require('./detection-result.vue').default,
    'select-device-modal': require('../components/select-device-modal.vue').default,
  },
  data() {
    return {
      formData: {
        type: '3',
        selectDeviceType: '1',
        spatialInformationVoList: [],
        testing: false,
        lonLatAccuracy: false,
        accuracy: null,
        lonRepeat: false,
        longitude: null,
        latRepeat: false,
        latitude: null,
        addressRepeat: false,
        address: null,
        crossTheBorder: false,
        deviationBool: false,
        deviation: null,
      },
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      selectedDeviceList: [],
      downloadLoading: false,
      uploadLoading: false,
      uploadData: {
        type: '3',
      },
      fileName: '',
      selectModeVisible: false,
      isResult: false,
    };
  },
  watch: {
    'formData.selectDeviceType': function () {
      this.formData.spatialInformationVoList = [];
    },
    'formData.lonLatAccuracy': function (val) {
      this.formData.accuracy = null;
      this.validate1(null, null, () => {
        return true;
      });
      if (!val) {
        setTimeout(() => {
          this.$refs.formData.validateField('accuracy', () => {});
        }, 100);
      }
    },
    'formData.lonRepeat': function (val) {
      this.formData.longitude = null;
      this.validate1(null, null, () => {
        return true;
      });
      if (!val) {
        setTimeout(() => {
          this.$refs.formData.validateField('longitude', () => {});
        }, 100);
      }
    },
    'formData.latRepeat': function (val) {
      this.formData.latitude = null;
      this.validate1(null, null, () => {
        return true;
      });
      if (!val) {
        setTimeout(() => {
          this.$refs.formData.validateField('latitude', () => {});
        }, 100);
      }
    },
    'formData.addressRepeat': function (val) {
      this.formData.address = null;
      this.validate1(null, null, () => {
        return true;
      });
      if (!val) {
        setTimeout(() => {
          this.$refs.formData.validateField('address', () => {});
        }, 100);
      }
    },
    'formData.crossTheBorder': function () {
      this.validate1(null, null, () => {
        return true;
      });
    },
    'formData.deviationBool': function (val) {
      this.formData.deviation = null;
      this.validate1(null, null, () => {
        return true;
      });
      if (!val) {
        setTimeout(() => {
          this.$refs.formData.validateField('deviation', () => {});
        }, 100);
      }
    },
  },
  methods: {
    validate1(rule, value, callback) {
      if (
        !this.formData.lonLatAccuracy &&
        !this.formData.lonRepeat &&
        !this.formData.latRepeat &&
        !this.formData.addressRepeat &&
        !this.formData.crossTheBorder &&
        !this.formData.deviationBool
      ) {
        setTimeout(() => {
          this.$refs.testing.validateState = 'error';
          this.$refs.testing.validateMessage = '请选择检测项';
        }, 10);
      } else {
        setTimeout(() => {
          this.$refs.testing.validateState = 'success';
          this.$refs.testing.validateMessage = '';
          callback();
        }, 10);
      }
    },
    // 配置确定
    submitHandle(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.isResult = true;
          this.$nextTick(() => {
            this.$refs.detectionResult.init();
          });
        }
      });
    },
    // 返回
    backHandle() {
      if (this.isResult) {
        this.isResult = false;
      } else {
        this.$router.push({ name: 'governancetoolset' });
      }
    },
    // 下载模板
    async downloadHandle() {
      try {
        this.downloadLoading = true;
        let params = {
          type: '3',
        };
        let res = await this.$http.post(governancetoolset.downloadTemplate, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
        this.downloadLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 上传文件之前
    beforeUpload(file) {
      if (!/\.(xlsx|xls)$/.test(file.name)) {
        this.$Message.error('文件格式错误，请上传“.xls”或“.xlsx”结尾的excel文件！');
        return false;
      }
      const isLt30M = file.size / 1024 / 1024 < 30;
      if (!isLt30M) {
        this.$Message.error('上传文件大小不能超过 30MB!');
        return false;
      }
      this.uploadLoading = true;
    },
    // 上传成功
    importSuccess(file, response) {
      this.uploadLoading = false;
      if (!file || file.length === 0) {
        this.$Message.warning('文件数据有误，请检查后重新上传');
        return false;
      }
      this.fileName = response.name;
      this.formData.spatialInformationVoList = file;
    },
    // 上传失败
    importError() {
      this.uploadLoading = false;
    },
    // 选择设备
    selectDeviceBtn() {
      this.selectModeVisible = true;
      this.$nextTick(() => {
        this.$refs.selectDeviceModle.init();
      });
    },
    setDeviceHandle(devices) {
      this.selectedDeviceList = devices;
      let list = [];
      devices.forEach((v) => {
        list.push({
          deviceId: v.deviceId,
          deviceName: v.deviceName,
          civilCode: v.civilCode,
          longitude: v.longitude,
          latitude: v.latitude,
          address: v.address,
        });
      });
      this.formData.spatialInformationVoList = list;
      this.$refs.formData.validateField('spatialInformationVoList', () => {});
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: var(--bg-content) url('~@/assets/img/datagovernance/governance_tool_set_child_bg.png') no-repeat
      center/cover;
    .child-head .head {
      color: #19d5f6;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .governancetoolset-child,
  .governancetoolset-child-result {
    background: #f1f3f6 url('~@/assets/img/datagovernance/governance_tool_set_child_bg_light.png') no-repeat
      center/cover;
    .child-head .head {
      color: var(--color-primary);
    }
  }
}
.governancetoolset-child,
.governancetoolset-child-result {
  display: flex;
  flex: 1;
  padding: 0 20px;
  box-sizing: border-box;
  flex-direction: column;
  height: 100%;
  position: relative;
  .child-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    height: 50px;
    .head {
      display: flex;
      align-items: center;
      .icon-font {
        font-size: 24px;
        margin-right: 5px;
      }
      .title {
        font-size: 16px;
        line-height: 20px;
        font-weight: bold;
      }
    }
  }
  .governancetoolset-child-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .set-form {
      .btns {
        display: flex;
        align-items: center;
      }
      .upload-btn {
        margin-right: 16px;
        display: flex;
        align-items: center;
        /deep/.icon-font {
          font-size: 20px;
          line-height: 20px;
        }
        /deep/span {
          height: 34px;
        }
        .btn-text {
          max-width: 200px;
          display: inline-block;
        }
      }
      .select-device {
        width: 270px;
        height: 34px;
        border: 1px dashed var(--color-active);
        background: rgba(43, 132, 226, 0.1);
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--color-active);
        .icon-font {
          font-size: 16px;
          margin-right: 10px;
        }
      }
      .select-device:hover {
        background: rgba(43, 132, 226, 0.2);
        border: 1px dashed #3c90e9;
      }
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          padding-right: 10px;
        }
        .ivu-form-item-content {
          display: flex;
        }
        .ivu-radio-group-item {
          margin-right: 30px;
          .ivu-radio {
            margin-right: 10px;
          }
        }
      }
      /deep/.label-width {
        .ivu-form-item {
          display: flex;
          .ivu-form-item-label {
            width: auto !important;
            padding-right: 0;
            margin-right: 10px;
          }
          .ivu-input {
            width: 85px;
          }
          .second-text {
            color: var(--color-content);
            white-space: nowrap;
            margin-left: 10px;
          }
        }
      }
      /deep/ .label-width > .ivu-form-item-content {
        flex-direction: column;
      }
      .form-item {
        display: flex;
        margin-bottom: 20px;
        /deep/ .ivu-form-item-label::before {
          display: none;
        }
      }
      /deep/ .ivu-checkbox-wrapper {
        margin-right: 10px;
      }
      .testing {
        .testing-text {
          height: 0;
          opacity: 0;
        }
      }
    }
    .submit-btn {
      margin-top: 10px;
      width: 200px;
    }
  }
  .btn-back {
    color: var(--color-active) !important;
  }
}
.governancetoolset-child-result {
  background: var(--bg-content);
}
</style>
