/**(还未完成)
 * loading + no-data处理
 * @param {loading} 例如：v-loading = "{ loading: true }" loading展示
 * @param {tableData} 例如：v-loading = "{ tableData: [] }" 显示nodata图片
 * @param {tableData} 例如：v-loading = "{ loading: rankLoading, tableData: rankData, type: 'img'}" 显示nodata-img图片 适用于列表为图片场景
 */
import Loading from '@/components/loading.vue';
import { emptyJSX, emptyJSXImg } from '@/util/module/common';

export default function (Vue) {
  const Mask = Vue.extend(Loading);
  Vue.directive('ui-loading', {
    // 当绑定元素插入到Dom中
    bind(el, bind) {
      const div = document.createElement('div');
      const mask = new Mask({
        el: div,
        data() {},
      });
      el.mask = mask;
      Vue.nextTick(() => {
        // 插入到目标元素
        insertDom(el, mask.$el);
        if (bind.value.tableData) {
          bind.value.noShowImg
            ? ''
            : el.insertAdjacentHTML('beforeEnd', bind.value.type === 'img' ? emptyJSXImg : emptyJSX);
          madeNodata(el, bind);
        }
        toggleLoading(el, bind);
      });
    },
    update: function (el, binding) {
      !!binding.value.tableData && madeNodata(el, binding);
      toggleLoading(el, binding);
    },
  });
}
const insertDom = (parent, el) => {
  parent.appendChild(el);
  parent.style.position = 'relative';
};
const toggleLoading = (el, bind) => {
  if (bind.value.loading) {
    el.mask.$el.style.display = 'block';
  } else {
    el.mask.$el.style.display = 'none';
  }
};
const madeNodata = (el, bind) => {
  let node = el.children;
  if (!node || !node.length) return;
  const noData = Array.from(node).find((row) => row.className === 'no-data');
  if (!noData) return;
  if (bind.value.tableData.length === 0) {
    noData.style.display = 'flex';
  } else {
    noData.style.display = 'none';
  }
};
