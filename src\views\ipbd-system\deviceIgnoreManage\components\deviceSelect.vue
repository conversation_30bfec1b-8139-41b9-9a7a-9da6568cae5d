<template>
  <div class="select-label-container modal-list-has-footer-content">
    <div class="organization" v-if="showOrganization">
      <div class="title">资源列表</div>
      <el-tree
        highlight-current
        ref="tree"
        :props="treeProps"
        lazy
        :load="loadNode"
        node-key="id"
        draggable
        :expand-on-click-node="false"
      >
        <span
          @click="handleNodeClick(data)"
          class="custom-tree-node"
          slot-scope="{ node, data }"
          :key="data.id"
        >
          <template>
            <span class="label">
              <i class="iconfont icon-fenju"></i>
              {{ node.data.orgName }}
            </span>
          </template>
        </span>
      </el-tree>
    </div>
    <div class="select-label-content">
      <Form
        inline
        ref="formData"
        :model="formData"
        class="form"
        @submit.native.prevent
      >
        <Row>
          <Col span="5">
            <FormItem label="功能类型:" prop="sbgnlxs">
              <Select placeholder="请选择" v-model="formData.sbgnlxs" clearable>
                <Option
                  v-for="item in sbgnlxList"
                  :value="item.dataKey"
                  :key="item.dataKey"
                  placeholder="请选择"
                  >{{ item.dataValue }}</Option
                >
              </Select>
            </FormItem>
          </Col>
          <Col span="5">
            <FormItem label="设备名称:" prop="deviceName">
              <Input
                v-model="formData.deviceName"
                size="small"
                placeholder="请输入"
              ></Input>
            </FormItem>
          </Col>
          <Col span="5">
            <FormItem label="设备编码:" prop="deviceId">
              <Input
                v-model="formData.deviceId"
                size="small"
                placeholder="请输入"
              ></Input>
            </FormItem>
          </Col>
          <!-- <Col span="5">
                      <FormItem label="安装地址:" prop="detailAddress">
                          <Input v-model="formData.detailAddress" size="small" placeholder="请输入"></Input>
                      </FormItem>
                  </Col> -->
          <Col span="4">
            <FormItem>
              <Button class="find" type="primary" @click="handleQuery"
                >查询</Button
              >
              <Button type="default" @click="resetForm">重置</Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <div class="manual" v-if="checkShow">
        <Checkbox
          class="checks mr-lg align-flex"
          v-model="isAll"
          @on-change="handleAllCheck"
        >
          全选
        </Checkbox>
        <Checkbox
          class="checks mr-lg align-flex"
          v-model="isExclude"
          @on-change="handleExcludeCheck"
          v-if="false"
        >
          排除
        </Checkbox>
        <RadioGroup v-if="isExclude" v-model="excludeType">
          <Radio label="1">全量排除</Radio>
          <Radio label="3">筛选排除</Radio>
        </RadioGroup>
      </div>
      <Table
        class="auto-fill table"
        ref="table"
        :height="470"
        :columns="columns"
        :data="tableData"
        :loading="tableLoading"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
      >
        <template #loading>
          <ui-loading></ui-loading>
        </template>
        <template slot="deviceGbId" slot-scope="{ row }">
          <span class="link-btn cursor-p" @click="deviceArchives(row)">{{
            row.deviceGbId
          }}</span>
        </template>
        <template slot="sbgnlx" slot-scope="{ row }">
          <span>{{ getGnlx(row.sbgnlx) }}</span>
        </template>
        <template #labels="{ row }">
          <ui-tag-poptip
            v-if="row.labels && row.labels.length"
            :data="row.labels"
          />
        </template>
      </Table>
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <div class="preview-select-label-content">
      <div class="info-bar">
        <span
          >已经选：<span>{{ selectTableData.length }}</span> 个</span
        >
        <span class="del-btn" @click="removeAllHandle">
          <i class="iconfont icon-shanchu"></i>清空
        </span>
      </div>
      <div class="label-container">
        <ul>
          <li v-for="(item, index) in selectTableData" :key="index">
            <Checkbox
              v-model="item.select"
              @on-change="selectChange(item, index)"
              >{{ item.deviceName }}</Checkbox
            >
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { queryDeviceList } from "@/api/wisdom-cloud-search";
import { queryUserDatascopeList } from "@/api/user";
import { deviceDirectoryTree } from "@/api/player";
import { mapGetters, mapActions } from "vuex";
import { commonMixins } from "@/mixins/app.js";
export default {
  components: {
    selectTree: require("@/components/select-modal/select-tree.vue").default,
  },
  props: {
    value: {
      // 已选择的标签
      type: Array,
      default: () => [],
    },
    // 设备类型， 1：摄像机， 2：wifi, 3: RFID, 4: 电围
    deviceType: {
      type: Number,
      default: 1,
    },
    // 组织机构显示标识
    showOrganization: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [commonMixins], //全局的mixin
  data() {
    return {
      formData: {
        sbgnlxs: "",
        deviceName: "",
        deviceId: "",
        detailAddress: "",
        orgCodes: [],
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
      total: 0,
      columns: [
        { title: "选择", width: 65, type: "selection", key: "index" },
        { title: "序号", width: 70, type: "index", key: "index" },
        { title: "功能类型", slot: "sbgnlx" },
        { title: "设备名称", key: "deviceName" },
        { title: "设备编码", key: "deviceGbId" },
        // { title: '安装地址', key: 'detailAddress' },
      ],
      tableData: [],
      selectTableData: [],
      selectOrgTree: {
        orgCode: null,
      },
      treeData: [],
      custormNodeData: {
        label: "未分配组织机构",
        orgCode: "-1",
      },
      isAll: false,
      isExclude: false,
      excludeType: "1",
      checkShow: false,
      treeProps: {
        label: "orgName",
        isLeaf: "isLeaf",
        children: "children",
      },
      tableLoading: false,
    };
  },
  watch: {
    value: {
      handler(data) {
        this.$nextTick(() => {
          if (data != null && data != undefined) {
            this.selectTableData = [...data];
            this.tableIsSelect();
          }
        });
      },
      immediate: true,
    },
  },
  async created() {
    this.setTreeData();
    await this.getDictData("propertySearch_sbgnlx");
    this.handleQuery();
  },
  computed: {
    ...mapGetters({
      sbgnlxList: "dictionary/getSbgnlxList", //摄像机功能类型
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictData",
    }),
    handleQuery() {
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    init() {
      this.tableData = [];
      var param = {
        // deviceType: this.deviceType,
        sbgnlxs: this.formData.sbgnlxs ? [this.formData.sbgnlxs] : [],
        filter: this.judgeUser,
      };
      queryDeviceList({ ...this.formData, ...this.pageInfo, ...param })
        .then((res) => {
          const { total, entities } = res.data;
          this.total = total;
          this.tableData = entities;
          this.tableIsSelect();
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {});
    },
    // 组织机构
    handleCheck(list) {
      this.formData.orgCodes = list ? list : [];
    },
    /**
     * table回显
     */
    tableIsSelect() {
      //  需要加延迟，否则数据切换分页，数据是上一次的数据
      setTimeout(() => {
        var obj = this.$refs.table.objData;
        // 清空table选中状态
        if (this.selectTableData.length == 0) {
          Object.keys(obj).forEach((key) => {
            obj[key]._isChecked = false;
          });
          return;
        }
        // 回显
        Object.keys(obj).forEach((key) => {
          var row = this.selectTableData.find((i) => {
            return obj[key].deviceId == i.deviceId;
          });
          if (row) {
            this.$refs.table.objData[key]._isChecked = true;
          }
        });
      }, 20);
    },
    /**
     * 显示model
     */
    show(list = [], keyWords = "", sbgnlxs = "") {
      if (sbgnlxs) {
        this.formData.sbgnlxs = sbgnlxs;
      }
      this.modalShow = true;
      this.selectTableData = JSON.parse(JSON.stringify(list)); //防止数据浅拷贝，改变父组件
      // this.formData.deviceType = this.deviceType
      this.formData.deviceName = keyWords;
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    /**
     * 右侧清空
     */
    removeAllHandle() {
      this.selectTableData = [];
      this.$refs.table.selectAll(false);
      this.$emit("input", []);
    },

    /**
     * 确定按钮
     */
    // confirmHandle() {
    //     this.modalShow = false;
    //     let checkDeviceFlag = '';
    //     if(this.isAll) {
    //         checkDeviceFlag = '2'
    //     }else if(this.isExclude) {
    //         checkDeviceFlag = this.excludeType
    //     }else{
    //         checkDeviceFlag = '0'
    //     }
    //     let params = {
    //         ...this.formData,
    //         checkDeviceFlag: checkDeviceFlag,
    //         sbgnlxList: this.formData.sbgnlxs ? [this.formData.sbgnlxs] : []
    //     }
    //     // 选择的数据
    //     let list = this.selectTableData.map(item => item);
    //     this.$emit("selectData", list, params)
    //     this.$refs['formData'].resetFields();
    // },
    // 取消
    // handleCancel() {
    //     this.$refs['formData'].resetFields();
    //     // this.$refs.selectTree.init();
    //     if(this.showOrganization) {
    //         // this.$refs.selectTree.resetCheck()
    //         this.$refs.tree.setCheckedNodes([])
    //         this.handleCheck(this.$refs.tree.setCheckedNodes([]))
    //     }
    //     this.isAll = false;
    //     this.isExclude = false;
    // },
    /**
     * table 选中一项
     */
    onSelect(selection, row) {
      var obj = this.selectTableData.find((item) => {
        return item.deviceId == row.deviceId;
      });
      row.select = true;
      if (!obj) {
        this.selectTableData.push(row);
      } else {
        obj.select = true;
      }
      this.$emit("input", this.selectTableData);
    },
    /**
     * table 取消选中一项
     */
    onSelectCancel(selection, row) {
      var num = this.selectTableData.findIndex((item) => {
        return item.deviceId == row.deviceId;
      });
      this.selectTableData.splice(num, 1);
      this.$emit("input", this.selectTableData);
    },
    /**
     * table 全选
     */
    onSelectAll(selection) {
      selection.forEach((item) => {
        item.select = true;
        var obj = this.selectTableData.find((itm) => {
          return itm.deviceId == item.deviceId;
        });
        if (!obj) {
          this.selectTableData.push(item);
        }
      });
      this.$emit("input", this.selectTableData);
    },

    /**
     * table 取消全选
     */
    onSelectAllCancel(selection) {
      this.tableData.forEach((item) => {
        var num = this.selectTableData.findIndex((itm) => {
          return itm.deviceId == item.deviceId;
        });
        if (num != -1) {
          this.selectTableData.splice(num, 1);
        }
      });
      this.$emit("input", this.selectTableData);
    },

    /**
     * 表格右侧 已选中内容操作
     */
    selectChange(row, index) {
      var obj = this.$refs.table.objData;
      if (row.select) {
        // 选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = true;
          }
        });
      } else {
        // 取消选中
        Object.keys(obj).forEach((key) => {
          if (obj[key].deviceId == row.deviceId) {
            obj[key]._isChecked = false;
          }
        });
      }
      this.$nextTick(() => {
        this.selectTableData.splice(index, 1);
        this.$emit("input", this.selectTableData);
      });
    },
    // 组织结构树数据
    setTreeData() {
      queryUserDatascopeList().then((res) => {
        this.treeData = res.data;
      });
    },
    loadNode(node, resolve) {
      // 加载子树数据的方法
      if (node.level === 0) {
        this.node = node;
        this.resolveFunc = resolve;
        deviceDirectoryTree({ orgCode: "", filter: false, deviceType: 1 }).then(
          (res) => {
            if (res.code === 200) {
              let list = res.data.deviceOrgList.filter((item) => {
                // if(item.allTotal > 0) {
                return item;
                // }
              });
              resolve(list);
            }
          }
        );
      } else {
        deviceDirectoryTree({
          orgCode: node.data.orgCode,
          filter: false,
          deviceType: 1,
        }).then((res) => {
          if (res.code === 200) {
            let list = res.data.deviceOrgList.filter((item) => {
              // if(item.allTotal > 0) {
              return item;
              // }
            });
            resolve(list);
          }
        });
      }
    },
    handleNodeClick(data) {
      this.checkShow = true;
      this.formData.orgCodes = [data.orgCode];
      this.init();
    },
    /**
     * 选择组织机构树
     */
    selectedOrgTree() {},
    /**
     * 重置表单
     */
    resetForm() {
      this.pageInfo = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.$refs["formData"].resetFields();
      if (this.showOrganization) {
        // this.$refs.selectTree.resetCheck()
        this.$refs.tree.setCheckedNodes([]);
        this.handleCheck(this.$refs.tree.setCheckedNodes([]));
      }
      this.init();
    },
    // 全选
    handleAllCheck(val) {
      // if(val) {
      //     this.isExclude  = false;
      // }
      // this.$refs.table.selectAll(val)
      // this.handleDataChecked()
      if (!val) {
        return;
      }
      if (this.selectTableData.length == 5000) {
        this.isAll = false;
        this.$Message.warning("单次选择最大数量不能大于5000，请重新选择");
        return;
      }
      this.tableLoading = true;
      var param = {
        deviceType: 1,
        sbgnlxs: [],
        filter: this.judgeUser,
      };
      queryDeviceList({
        ...this.formData,
        pageNumber: 1,
        pageSize: 5000,
        ...param,
      })
        .then((res) => {
          const { total, entities } = res.data;
          if (this.selectTableData.length + entities.length > 5000) {
            this.isAll = false;
            this.$Message.warning("单次选择最大数量不能大于5000，请重新选择");
          } else {
            entities.forEach((item) => {
              item.select = true;
              var obj = this.selectTableData.find((itm) => {
                return itm.deviceId == item.deviceId;
              });
              if (!obj) {
                this.selectTableData.push(item);
              }
            });
            this.comparisonList();
          }
        })
        .catch((err) => {
          console.error(err);
        })
        .finally(() => {
          this.tableLoading = false;
          this.$emit("input", this.selectTableData);
        });
    },
    // 对比当前已选数据
    comparisonList() {
      this.$nextTick(() => {
        var obj = this.$refs.table.objData;
        let selectList = new Map(
          this.selectTableData.map((item) => [item.deviceId, item])
        );
        Object.keys(obj).forEach((key) => {
          if (selectList.get(obj[key].deviceId)) {
            obj[key]._isChecked = true;
          }
        });
      });
    },
    // 排除
    handleExcludeCheck(val) {
      if (val) {
        this.isAll = false;
      }
      this.handleDataChecked();
    },
    // 点击全选或者排除时，对表格复选框进行操作
    handleDataChecked() {
      this.tableData = this.tableData.map((item) => {
        this.$set(item, "_checked", this.isAll);
        this.$set(item, "_disabled", this.isAll);
        return item;
      });
    },
    getGnlx(str) {
      if (!str) return "";
      var arr = str.split("/");
      var gnlx = "";
      arr.forEach((item) => {
        var row = this.sbgnlxList.find((i) => i.dataKey == item);
        gnlx += (row ? row.dataValue : item) + " / ";
      });
      return gnlx.substring(0, gnlx.length - 2);
    },
  },
};
</script>
<style lang="less" scoped>
.find {
  margin-right: 10px;
}
/deep/ .ivu-input-wrapper {
  width: 100% !important;
}
.select-label-container {
  border: 1px solid #d3d7de;
  border-radius: 4px;
  margin: 0 0px 0px;
  display: flex;
  .select-label-content {
    flex: 1;
    padding: 20px;
    padding-bottom: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
    border-right: 1px solid #d3d7de;
    .manual {
      display: flex;
    }
  }
  .label-head {
    height: 20px;
    background: linear-gradient(
      90deg,
      rgba(35, 168, 249, 0.2) 0%,
      rgba(73, 211, 253, 0) 100%
    );
    color: #2b84e2;
    position: relative;
    padding: 0 20px;
    font-size: 16px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    &:before {
      content: "";
      height: 20px;
      width: 4px;
      background: #2b84e2;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .label-container {
    overflow: auto;
    flex: 1;
    position: relative;
  }
  .organization {
    width: 240px;
    // padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    overflow: auto;
    border-right: 1px solid #d3d7de;
    .title {
      font-size: 14px;
      font-weight: 600;
      padding-left: 10px !important;
      color: rgba(0, 0, 0, 0.8);
      background: #f9f9f9;
    }
  }
  .preview-select-label-content {
    width: 250px;
    padding: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: auto;
    .info-bar {
      color: #515a6e;
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      align-items: center;
      padding-bottom: 10px;
      .icon-shanchu {
        font-size: 14px;
        margin-right: 5px;
      }
    }
    .label-container {
      padding-top: 10px;
    }
  }
}
.del-btn {
  cursor: pointer;
}
.mr20 {
  margin-right: 20px !important;
}

.table {
  margin-top: 10px;
}

/deep/ .ivu-icon {
  // color: #fff;
}

.form {
  width: 100%;
  .search-input {
    display: inline-flex;
    margin-right: 15px;
  }
  // /deep/ .ivu-input {
  //   width: 125px;
  // }
  /deep/.ivu-select {
    width: 100%;
  }

  /deep/.ivu-form-item {
    margin-bottom: 10px;
    margin-right: 15px;
    width: calc(~"100% - 15px");
    .ivu-form-item-content {
      flex: 1;
      .ivu-select {
        width: 100%;
      }
    }
  }
  /deep/ .ivu-form-item-label {
    white-space: nowrap;
    width: 72px;
    text-align-last: justify;
    text-align: justify;
    text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4513);
    font-size: 14px;
  }
  /deep/ .ivu-form-item-content {
    display: flex;
  }
  // /deep/ .ivu-form-item-content{
  //   float: left;
  // }
  // .btn-group {
  //   margin-right: 0;
  // }
}
.custom-tree-node {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-size: 14px;
    color: #000;
    i {
      color: #23a8f9;
      margin-right: 10px;
    }
  }
}
</style>
