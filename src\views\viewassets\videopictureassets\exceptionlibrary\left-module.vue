<template>
  <div class="left-module">
    <ui-menu :active="active">
      <template v-for="(item, index) in dataList">
        <sub-menu
          v-if="!!item.children && item.children.length > 0"
          class="data-classify"
          :name="item.value"
          :key="index"
        >
          <template #title>
            <i class="icon-font f-16 mr-sm" :class="item.icon"></i>
            <span class="inline vt-middle">{{ item.label }}</span>
          </template>
          <menu-item v-for="(row, i) in item.children" :key="i" :name="row.value" @click.native="selectMenu(row)">
            <span class="inline vt-middle">
              {{ row.label }}
            </span>
          </menu-item>
        </sub-menu>
        <menu-item v-else class="data-classify" :name="item.value" :key="index" @click.native="selectMenu(item)">
          <i class="icon-font f-16 mr-sm" :class="item.icon"></i>
          <span class="inline vt-middle">{{ item.label }}</span>
        </menu-item>
      </template>
    </ui-menu>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      active: '',
      dataList: [
        {
          label: '视图基础数据',
          value: 'basis',
          icon: 'icon-shitujichushuju',
        },
        {
          label: '人脸视图数据',
          value: 'face',
          icon: 'icon-renlianshitushuju',
        },
        {
          label: '车辆视图数据',
          value: 'vehicle',
          icon: 'icon-cheliangshitushuju',
        },
        {
          label: '视频流数据',
          value: 'videoStreaming',
          icon: 'icon-shipinliushuju',
        },
        {
          label: '重点人员数据',
          value: 'keyPersonnelBasis',
          icon: 'icon-zhongdianrenyuanshuju',
          children: [
            {
              label: '人员基础数据',
              value: 'keyPersonnelBasis',
            },
            {
              label: '人员轨迹数据',
              value: 'keyPersonnelTrajectory',
            },
          ],
        },
      ],
    };
  },
  created() {},
  activated() {
    this.selectMenu(this.dataList[0]);
  },
  methods: {
    selectMenu(row) {
      this.active = row.value;
      this.$emit('selectDataType', row);
    },
  },
  watch: {},
  components: {
    UiMenu: require('@/components/ui-menu/ui-menu.vue').default,
    MenuItem: require('@/components/ui-menu/menu-item.vue').default,
    SubMenu: require('@/components/ui-menu/sub-menu.vue').default,
  },
};
</script>
<style lang="less" scoped>
.left-module {
  width: 300px;
  height: 100%;
  float: left;
  border-right: 1px solid var(--border-color);
  padding: 20px;
  overflow-y: auto;
  .data-classify {
    border: 1px solid #144e8b;
    margin-bottom: 10px;
  }
  @{_deep}.menu-item {
    border-radius: 4px;
  }
  @{_deep}.menu-item-active {
    background: #023467 !important;
  }
  @{_deep}.sub-menu {
    border-radius: 4px;
  }
}
</style>
