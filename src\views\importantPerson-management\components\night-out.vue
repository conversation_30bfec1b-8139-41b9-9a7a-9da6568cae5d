<template>
  <div style="height: 100%; width: 100%">
    <div class="darktime-out" v-if="list.length > 0">
      <div
        class="dartktiem-item"
        v-for="(item, index) in list"
        :key="index"
        @click="handleDetailFn(item, index)"
      >
        <div class="left-content">
          <div class="item-image">
            <ui-image :src="item?.traitImg || ''"></ui-image>
          </div>
        </div>

        <div class="right-content">
          <div class="right-content-tob">
            <div class="name text-overflow">
              {{ item.name }}<span class="tag" v-if="item?.bizLabels?.length" :title="item?.bizLabels?.join(',')">（{{ item?.bizLabels?.join(",") }}）</span>
            </div>
            <div class="location text-overflow" v-if="type === 1">
              告警类型：<span class="location-name">{{
                alarmTypeFilter(item.trackAlarmType) || "-"
              }}</span>
            </div>
            <div class="location text-overflow" v-else>
              常住地：<span class="location-name" :title="item.address">{{
                item.address || "-"
              }}</span>
            </div>
          </div>
          <div class="right-content-bottom">
            <div class="picture-location text-overflow">
              <i class="iconfont icon-location mr-5"></i>{{ item.deviceName }}
            </div>
            <div class="picture-time text-overflow">
              <i class="iconfont icon-time mr-5"></i>{{ item.absTime }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <ui-empty v-if="list.length == 0"></ui-empty>
    <ui-loading v-if="loading" />
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="list"
    ></CaptureDetail>
  </div>
</template>

<script>
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
export default {
  name: "nightOut",
  components: {
    CaptureDetail,
  },
  props: {
    type: {
      type: Number,
      default: 1, // 1：人员轨迹异常报警 2：人员跨域流动入侵
    },
    idCardNo: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      selectIdCard: "",
      tableIndex: -1,
    };
  },
  methods: {
    alarmTypeFilter(val) {
      if (!val) return "";
      const alarmType = {
        1: "地点异常",
        2: "时间异常",
      };
      return alarmType[val] || "";
    },
    // 详情
    handleDetailFn(item, index) {
      this.selectIdCard = item.idCard;
      this.$refs.videoDetail.showList(index);
    },
  },
};
</script>

<style lang="less" scoped>
.darktime-out {
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;

  .dartktiem-item {
    height: 48px;
    display: flex;
    gap: 12px;
    justify-content: space-between;
    background: #f9f9f9;
    padding-right: 10px;
    cursor: pointer;
    .left-content {
      display: flex;
      align-items: center;

      .item-image {
        width: 48px;
        height: 100%;
        padding: 2px;
      }
    }

    .right-content {
      flex: 1;
      overflow: hidden;
      .right-content-tob {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        height: 28px;
        border-bottom: 1px #d3d7de dashed;
        .name {
          font-weight: 700;
          font-size: 16px;
          color: rgba(0, 0, 0, 0.9);
          max-width: 50%;
          .tag {
            font-size: 14px;
            color: #ea4a36;
          }
        }
        .location {
          flex: 1;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.75);
          text-align: right;
          .location-name {
            color: #5ba3ff;
          }
        }
      }
      .right-content-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 10px;
        height: 20px;
        line-height: 20px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        .picture-location {
          flex: 1;
        }
        .mr-5 {
          margin-right: 5px;
        }
        i {
          font-size: 12px;
        }
      }
    }
  }
}
</style>
