<!--
    * @FileDescription: 
    * @Author: H
    * @Date: 2024/04/25
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-07 14:25:41
 -->
<template>
  <div class="date-gather">
    <div class="gather-list-title">
      <div class="gather-box">
        <div class="gather-cricle cricle-yellow"></div>
        <p>0次</p>
      </div>
      <div class="gather-box">
        <div class="gather-cricle"></div>
        <p>1-2000次</p>
      </div>
      <div class="gather-box">
        <div class="gather-cricle cricle-bule"></div>
        <p>>2000次</p>
      </div>
    </div>
    <div class="month-title">{{ dateTitle }}年</div>
    <div class="gather-box">
      <div class="table-date">
        <div class="date-list">
          <div
            class="date-list-num"
            v-for="(item, index) in dateList"
            :key="index"
          >
            {{ item }}
          </div>
        </div>
        <div class="date-details">
          <div class="date-msg" v-for="(item, index) in dataList" :key="index">
            <!-- <div>{{ index }}</div> -->
            <ul class="date-msg-ul">
              <li
                class="date-msg-li"
                :class="{
                  'date-msg-li-yellow': ite == 0,
                  'date-msg-li-bule': ite > 2000,
                }"
                v-for="(ite, ind) in item.list"
                :key="ind"
              >
                {{ ite }}
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="time-frame">
        <div class="time-frame-list" v-for="(item, index) in 24" :key="index">
          {{ index }}
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      dateTitle: "",
      dateList: ["15日", "14日", "13日", "12日", "11日", "10日", "9日"],
      dataList: [
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
        {
          list: [],
        },
      ],
    };
  },
  mounted() {},
  methods: {
    init(list) {
      this.dateList = [];
      this.dateTitle = "";
      list.forEach((item, index) => {
        let date = item.date.split("-");
        this.dateTitle = date[0];
        this.dateList.push(date[1] + "-" + date[2]);
        this.dataList[index].list = [
          0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
          0,
        ];
        item.dateList.forEach((ite) => {
          if (ite.date < 24 && ite.date >= 0) {
            this.dataList[index].list[ite.date] = ite.count;
          }
        });
      });
      this.dataList.forEach((item) => {
        if (item.list.length < 24) {
          item.list = this.padArrayToLength(item.list, 24);
        }
      });
      console.log(this.dateList, this.dataList, "this.dataList");
    },
    padArrayToLength(array, length) {
      while (array.length < length) {
        array.push(0);
      }
      return array;
    },
  },
};
</script>
<style lang="less" scoped>
.date-gather {
  position: relative;
  .gather-list-title {
    position: absolute;
    top: -36px;
    display: flex;
    transform: translate(-50%, 10px);
    left: 50%;
    .gather-box {
      display: flex;
      align-items: center;
      .gather-cricle {
        width: 8px;
        height: 8px;
        border-radius: 5px;
        background: #ffffff;
        margin-right: 5px;
        margin-left: 10px;
        border: 1px solid rgba(0, 0, 0, 0.35);
      }
      .cricle-yellow {
        background: #ffcd2a;
        border: 1px solid #ffcd2a;
      }
      .cricle-bule {
        background: #2cbef8;
        border: 1px solid #2cbef8;
      }
      p {
        color: rgba(0, 0, 0, 0.35);
      }
    }
  }
  .month-title {
    margin-left: 20px;
    font-size: 14px;
  }
  .gather-box {
    .table-date {
      display: flex;
      .date-list {
        width: 60px;
        text-align: center;
        .date-list-num {
          padding: 3px 5px;
        }
      }
      .date-details {
        border: 2px solid #d3d7de;
        width: 100%;
        background: rgba(241, 241, 241, 0.3);
        .date-msg {
          min-width: 50px;
          text-align: center;
          padding: 3px 5px;
          .date-msg-ul {
            display: flex;
            justify-content: space-between;
            .date-msg-li {
              min-width: 50px;
              text-align: center;
            }
            .date-msg-li-yellow {
              background: #ffcd2a;
              color: #ffffff;
            }
            .date-msg-li-bule {
              background: #2cbef8;
              color: #ffffff;
            }
          }
        }
      }
    }
    .time-frame {
      width: calc(~"100% - 60px");
      display: flex;
      justify-content: space-between;
      margin-left: 60px;
      .time-frame-list {
        min-width: 50px;
        text-align: center;
        padding: 3px 5px;
      }
    }
  }
}
</style>
