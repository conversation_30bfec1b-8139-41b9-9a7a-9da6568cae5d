<template>
    <Modal ref="modal" title="请输入文件夹名称" v-model="modalOptions.open" class="modalExtendClass" @on-cancel="modalClosed">
        <template>
            <Input v-model="inputValue" clearable placeholder="请输入文件夹名称"></Input>
        </template>
        <div slot="footer" style="padding:10px;text-align:center;" class="nui-border">
            <Button type="primary" @click="saveValue">保存</Button>
        </div>
    </Modal>
</template>

<script>
    import treeDataUtil from '../mixins/treeDataUtil';
    export default {
        name: "editTreePop",
        data() {
            return {
                modalOptions: {
                    open: false
                },
                inputValue: ''
            }
        },
        mixins: [treeDataUtil],
        mounted() {
        },
        methods: {
            open(name){
                this.modalOptions.open = true;
                this.inputValue = name;
            },
            modalClosed() {
                this.$emit('editTreePopClose',{
                    close: true
                });
            },
            saveValue() {
                if(!this.inputValue) {
                    this.$Message.warning("请输入文件夹名称");
                    return false;
                }
                this.$emit('editTreePopClose',{
                    value: this.inputValue,
                    close: false
                });
                this.modalOptions.open = false;
            }
        }
    }
</script>

<style lang="less">
    .modalExtendClass {
        text-align: center;
        .xui-modal-box {
            top: 200px;
            width: 410px!important;
            .xui-modal-header {
                height: 46px;
            }
            .xui-modal-content {
                padding: 28px 50px 28px 50px;
                .np-date-picker-panel {
                    width: 100%;
                }
            }
            .xui-modal-footer {
                height: 46px;
                width: 100%;
                div {
                    text-align: right!important;
                    border: 0!important;
                    padding-right: 16px!important;
                }
            }
        }
    }
</style>
