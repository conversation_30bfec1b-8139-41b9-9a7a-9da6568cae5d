<template>
  <div class="myreport auto-fill">
    <div class="search-module">
      <ui-label label="汇报标题" class="inline">
        <Input class="width-md" v-model="searchData.title" placeholder="请输入汇报标题"></Input>
      </ui-label>
      <ui-label label="提交人" class="ml-lg inline">
        <Input class="width-md" v-model="searchData.sendName" placeholder="请输入提交人"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="提交时间">
        <DatePicker
          class="width-md"
          v-model="searchData.startSendTime"
          type="date"
          placeholder="请选择开始时间"
          :options="startTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startSendTime')"
        >
        </DatePicker>
        <span class="ml-sm mr-sm">--</span>
        <DatePicker
          class="width-md"
          v-model="searchData.endSendTime"
          type="date"
          placeholder="请选择结束时间"
          :options="endTimeOption"
          confirm
          @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endSendTime')"
        >
        </DatePicker>
      </ui-label>
      <ui-label class="ml-lg inline" label=" ">
        <Button type="primary" class="mr-sm" @click="search">查询</Button>
        <Button type="default" class="mr-lg" @click="resetSearchDataMx(searchData, search)">重置</Button>
      </ui-label>
    </div>
    <div class="table-module auto-fill">
      <div class="mb-sm button-option">
        <Button class="fr" type="primary" @click="handleAdd">
          <i class="icon-font icon-tianjia f-14 mr-xs" title="添加"> </i>
          <span> 新增汇报 </span>
        </Button>
      </div>
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #time="{ row }">
          <span>{{ row.startTime + '至' + row.endTime }}</span>
        </template>
        <template #receiveOrgCodeNameList="{ row }">
          <div class="width-percent inline ellipsis" :title="row.receiveOrgCodeNameList.join(',')">
            {{ row.receiveOrgCodeNameList.join(',') }}
          </div>
        </template>
        <template #modifyTime="{ row }">
          <span :class="{ sign: row.lookStatus === '0' || row.lookStatus === '2' }">{{ row.modifyTime }}</span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            icon="icon-bianji2"
            content="编辑"
            v-permission="{
              permission: 'editreport',
            }"
            v-if="row.adminDeliver"
            @click.native="handleEdit(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            icon="icon-chakanxiangqing"
            content="查看"
            v-permission="{
              permission: 'editreport',
              mutually: true,
              isShow: !row.adminDeliver,
            }"
            @click.native="handleEdit(row, true)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="ml-md"
            icon="icon-shanchu3"
            content="删除"
            v-permission="{
              permission: 'deletereport',
            }"
            @click.native="handleDelete(row)"
            v-if="row.adminDeliver"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page
        class="menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      >
      </ui-page>
    </div>
    <add-edit-report
      v-model="addEditShow"
      :modal-action="modalAction"
      :modal-data="modalData"
      @update="search"
    ></add-edit-report>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'myreport',
  props: {},
  data() {
    return {
      loading: false,
      searchData: {
        title: '',
        sendName: '',
        startSendTime: '',
        endSendTime: '',
        type: 1, // 类型 1工作汇报 2汇报查阅
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 1,
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endSendTime = new Date(this.searchData.endSendTime);
          if (endSendTime) {
            return date > endSendTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startSendTime = new Date(this.searchData.startSendTime);
          if (startSendTime) {
            return date < startSendTime;
          }
          return false;
        },
      },
      tableColumns: [
        {
          type: 'selection',
          title: '序号',
          width: 50,
          align: 'center',
        },
        {
          title: '汇报标题',
          key: 'title',
          width: 250,
        },
        {
          title: '汇报时间',
          slot: 'time',
        },
        {
          title: '汇报给',
          slot: 'receiveOrgCodeNameList',
          tooltip: true,
        },
        {
          title: '提交人',
          key: 'sendName',
          width: 200,
        },
        {
          title: '提交时间',
          slot: 'modifyTime',
          width: 180,
        },
        {
          title: '操作',
          slot: 'action',
          width: 80,
        },
      ],
      tableData: [],
      addEditShow: false,
      modalAction: {
        title: '新增',
        action: 'add',
      },
      modalData: {},
    };
  },
  created() {
    this.init();
  },
  computed: {},
  methods: {
    async init() {
      try {
        this.loading = true;
        this.copySearchDataMx(this.searchData);
        const res = await this.$http.post(equipmentassets.workReportList, this.searchData);
        this.tableData = res.data.data.entities;
        this.handleTableData();
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    handleTableData() {
      let userMessage = JSON.parse(window.sessionStorage.getItem('userMessage')).username;
      this.tableData.forEach((item) => {
        // 如果是管理员和发布人: 一定会有编辑删除按钮展示权限
        // 其余人如果有编辑删除按钮权限，但是只有发布人和 角色为管理员的能编辑删除
        userMessage === 'admin' || item.creator === userMessage
          ? this.$set(item, 'adminDeliver', true)
          : this.$set(item, 'adminDeliver', false);
      });
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    handleAdd() {
      this.modalAction = {
        title: '新增汇报',
        action: 'add',
      };
      this.modalData = {};
      this.addEditShow = true;
    },
    async getDetail(id) {
      try {
        const res = await this.$http.get(equipmentassets.workReportView, {
          params: {
            type: 1,
            id: id,
          },
        });
        let modalData = res.data.data;
        if (res.data.data.fileVos) {
          modalData.uploadData = res.data.data.fileVos.map((row) => {
            return {
              name: row.originalName,
              url: row.fileUrl,
              response: {
                data: [row.id],
              },
            };
          });
        }
        this.modalData = modalData;
      } catch (err) {
        console.log(err);
      }
    },
    async handleEdit(row, isView) {
      await this.getDetail(row.id);
      // 有按钮权限并且是管理员或者发布人
      if (row.adminDeliver && !isView) {
        this.modalAction = {
          title: '编辑汇报',
          action: 'edit',
        };
      } else {
        this.modalAction = {
          title: '查看汇报',
          action: 'view',
        };
      }
      this.addEditShow = true;
    },
    async deleteData(row) {
      this.$UiConfirm({
        content: `您要删除${row.title}，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          try {
            this.$http.delete(`${equipmentassets.workReportDelete}/${row.id}`).then(() => {
              this.$Message.success('删除成功！');
              this.search();
            });
          } catch (err) {
            console.log(err);
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    handleDelete(row) {
      this.deleteData(row);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.search();
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    AddEditReport: require('../components/add-edit-report.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark']{
  .myreport {
    .search-module {
      border-bottom: 1px solid var(--devider-line);
    }
  }
}
.myreport {
  position: relative;
  background: var(--bg-content);

  .button-option {
    padding-right: 20px;
  }

  .search-module {
    padding-bottom: 10px;
    margin: 20px 20px 10px;
    border-bottom: 1px solid #d8d8d8;
  }

  .ui-table {
    padding: 0 20px;
  }

  .sign {
    color: var(--bg-markers);
  }
}
</style>
