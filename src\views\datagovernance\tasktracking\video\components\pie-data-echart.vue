<template>
  <div style="width: 3.3rem; height: 0.78rem" id="dataEchart" ref="echart2"></div>
</template>
<script>
export default {
  name: 'tasktracking',
  props: ['ListNum'],
  data() {
    return {};
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.init();
    // })
    this.init();
  },
  methods: {
    init() {
      let myChart = this.$echarts.init(document.getElementById('dataEchart'));
      // let myChart = this.$echarts.init(this.$refs.echart2);
      var option = {
        tooltip: {
          trigger: 'item',
        },
        color: [
          {
            type: 'linear',
            x: 1,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#05CE98', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#32A19E', // 100% 处的颜色
              },
            ],
          },
          {
            x: 1,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: '#C02E02', // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#4D1F08', // 100% 处的颜色
              },
            ],
          },
        ],
        legend: {
          orient: 'vertical',
          right: '0',
          top: '30%',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
        },
        grid: {
          left: '10%',
          right: '50%',
          bottom: '10%',
          top: '10%',
          containLabel: true,
        },
        title: {
          text: '视频图像总量',
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          subtext: `${this.ListNum[4].accessDataCount}`,
          subtextStyle: {
            fontSize: 12,
            color: '#19C176',
          },
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['60%', '80%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 0,
            },
            label: {
              show: false,
            },
            data: [
              {
                value: this.ListNum[4].accessDataCount - this.ListNum[4].existingExceptionCount,
                name: '合格数据',
                // label: {
                //     show: false,
                //     normal: {
                //         color: '#05CE98',
                //     }
                // },
                // itemStyle: {
                //   normal: {
                //     color: {
                //       type: 'linear',
                //       colorStops: [
                //         { offset: 0, color: '#32A19E' },
                //         { offset: 1, color: '#05CE98' }
                //       ]
                //     }
                //   }
                // }
              },
              {
                value: this.ListNum[4].existingExceptionCount,
                name: '不合格数据',
                // label: {
                //     show: false,
                //     normal: {
                //         color: '#E9410F',
                //     }
                // },
                // itemStyle: {
                //   normal: {
                //     color: {
                //       type: 'linear',
                //       colorStops: [
                //         { offset: 0, color: '#983C0D' },
                //         { offset: 1, color: '#E9410F' }
                //       ]
                //     }
                //   }
                // }
              },
            ],
          },
        ],
      };

      myChart.setOption(option);
      window.onresize = function () {
        myChart.resize();
      };
    },
    resizeFn() {
      this.init();
      // let myChart = this.$echarts.init(this.$refs.echart2);
      // myChart.resize();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
