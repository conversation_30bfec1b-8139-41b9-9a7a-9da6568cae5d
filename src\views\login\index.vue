<template>
  <div class="height-full">
    <div class="login-bg" v-if="isReload">
      <div class="form-bg"></div>
      <div class="login-text">
        <img class="nav-logo" :src="systemConfig.logoUrl || require('@/assets/img/login/nav-title.png')" />
        <p class="login-cn">
          {{ systemConfig.applicationName }}
        </p>
      </div>
      <div class="login-wrapper">
        <div class="login-box" :class="loading ? 'login-loading' : ''"></div>
        <div class="login-container">
          <Form ref="loginForm" :model="loginForm" class="login-form" :rules="formRules">
            <FormItem prop="username">
              <Input
                type="text"
                v-model="loginForm.username"
                placeholder="请输入用户名"
                @on-enter="userEnter"
                :autofocus="!passwordFocus"
                clearable
                class="login-input"
              >
                <i class="icon-font icon-yonghuming-01 login-icon" slot="prefix"></i>
              </Input>
            </FormItem>
            <FormItem prop="password">
              <Input
                type="password"
                v-model="loginForm.password"
                placeholder="请输入密码"
                :autofocus="passwordFocus"
                ref="password"
                @on-enter="login"
                class="login-input"
                password
              >
                <i class="icon-font icon-mima-01 login-icon" slot="prefix"></i>
              </Input>
            </FormItem>
            <FormItem>
              <Button type="primary" :border="false" @click="pkiLogin" class="sign-button">PKI登录</Button>
              <Button type="primary" @click="login" class="sign-button">登&nbsp;&nbsp;&nbsp;&nbsp;录</Button>
            </FormItem>
          </Form>
        </div>
      </div>
      <div class="footer" v-if="systemConfig.showLogo">
        <div class="login-logo">
          <img src="@/assets/img/login/logo.png" alt="" />
        </div>
        <div class="line"></div>
        <div class="copyright">
          <p>版本号: V 1.2.0 推荐浏览器: Chrome 50 及以上版本</p>
          <p>南京启数智能系统有限公司</p>
          <p>Copyright© {{ getYear() }} 南京启数智能系统有限公司 版权所有</p>
        </div>
      </div>
    </div>
      <loading v-else></loading>
  </div>
</template>
<style lang="less" scoped>
@import url('./index.less');
</style>
<script>
import api from '@/config/api/user';
import thirdPartyConfig from '@/config/thirdParty.config.js';
import md5 from 'md5';
import { mapActions, mapGetters } from 'vuex';
export default {
  data() {
    return {
      loading: false,
      loginForm: {
        username: '',
        password: '',
      },
      formRules: {
        username: [{ required: true, message: '用户名不能为空', trigger: 'blur' }],
        password: [
          { required: true, message: '密码不能为空', trigger: 'blur' },
          {
            required: true,
            message: '密码长度应为6～16个字符',
            min: 6,
            max: 16,
            trigger: 'blur',
          },
        ],
      },
      passwordFocus: false,
      queryUser: {},
      isReload: false, // 默认不显示页面内容
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name) {
        window.location.reload();
      } else if(!to.query.loading){
        vm.isReload = true;
      }
    });
  },
  created() {
    this.loading = false;
    //此处截取浏览器参数是为了做第三方登录如果地址中有后端拼接的账户就自动做登录
    this.queryUser = this.$route.query;
    let username = window.sessionStorage.getItem('username');
    if (username) {
      this.passwordFocus = true;
      this.loginForm.username = username;
    } else {
      this.passwordFocus = false;
      this.loginForm.username = '';
    }
  },
  mounted() {},
  methods: {
    ...mapActions({
      startWebsock: 'websocket/startWebsock',
      handleLogin: 'user/handleLogin',
      setIsThirdParty: 'user/setIsThirdParty',
    }),
    async login() {
      try {
        let valid = await this.$refs['loginForm'].validate();
        if (!valid) return;
        this.setIsThirdParty('0');
        let res = null;
        this.loading = true;
        // loading-gif动画时间为2000毫秒
        const loadingTime = 1400;
        // 这里来记录接口返回时间 用来计算loading-gif的完成需要等待的剩余时间
        const sendDate = new Date().getTime();
        // 判断是否为单点登录如果不为单点登录则使用普通账号密码登录
        if (this.isWithDbPswd) {
          res = await this.$http.post(api.loginWithDbPswd, this.loginForm);
        } else {
          let param = 'username=' + this.loginForm.username + '&';
          param += 'password=' + md5(this.loginForm.password) + '&';
          param += 'grant_type=' + 'password' + '&';
          param += 'scope=' + 'server';
          res = await this.handleLogin(param);
        }
        // 快过期提醒
        if (res.data.tips) {
          this.$Message.warning(res.data.tips);
        }
        const receiveDate = new Date().getTime();
        const time = loadingTime - (receiveDate - sendDate) > 0 ? loadingTime - (receiveDate - sendDate) : 10;
        setTimeout(() => {
          // window.sessionStorage.setItem("token", res.data.access_token); //存储token
          this.$router.push({ name: this.getPortalConfig });
          // 开启全局websoket
          // this.startWebsock();
          this.loading = false;
        }, time);
      } catch (err) {
        // this.$Message.error('登录失败');
        this.loading = false;
        console.log(err);
      }
    },
    async pkiLogin() {
      const { distinguishVersion } = this.systemConfig;
      const distinguishConfig = thirdPartyConfig[distinguishVersion];
      //地区是否支持Pki登录
      if (!distinguishConfig?.isUsePki) {
        this.$Message.warning('暂未开启');
        return;
      }
      let isHasPlugin = distinguishConfig.validatePlugin();
      if (!isHasPlugin) {
        this.$Message.error('未安装PKI登录插件');
        return;
      }
      try {
        this.loading = true;
        this.setIsThirdParty('1');
        await distinguishConfig.loginAsync();
        this.loading = false;
        this.$router.push({ name: this.getPortalConfig });
      } catch (err) {
        this.loading = false;
        console.log(err);
      }
    },
    userEnter() {
      this.$refs.password.focus();
    },
    getYear() {
      return new Date().getFullYear();
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      getIsShowXiaoZhi: 'common/getIsShowXiaoZhi',
      getPortalConfig: 'common/getPortalConfig',
      systemConfig: 'common/getSystemConfig',
    }),
    isWithDbPswd() {
      return !!this.queryUser.username && !!this.queryUser.password;
    },
  },
  props: {},
};
</script>
