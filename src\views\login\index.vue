<template>
  <div v-if="isReload" class="login-bg">
    <div class="video-container">
      <div :style="fixStyle" class="filter">
        <div class="logo">
          <img
            class="logo-img"
            src="@/assets/img/login/logo-white.png"
            alt=""
          />
          <span class="logo-title">视综平台</span>
        </div>
        <div class="login-wrapper">
          <div class="main">
            <div class="login-text">登录</div>
            <div class="ui-tab">
              <div v-if="tabList.length" class="list">
                <div
                  v-for="(item, index) in tabList"
                  :key="index"
                  :class="{ active: item.value === tabActive }"
                  class="ivu-tabs-tab"
                  @click="tabClick(item)"
                >
                  <span>{{ item.label }}</span>
                </div>
              </div>
            </div>
            <Form ref="loginForm" :model="loginForm" class="login-form">
              <FormItem prop="username">
                <Input
                  :placeholder="`请输入${typeList[tabActive - 1].label}`"
                  v-model="loginForm.username"
                  :autofocus="!passwordFocus"
                  type="text"
                  clearable
                  class="login-input"
                  @on-enter="userEnter"
                >
                  <i
                    slot="prefix"
                    :class="`${typeList[tabActive - 1].iconName} iconfont`"
                  ></i>
                </Input>
              </FormItem>
              <FormItem prop="password">
                <Input
                  ref="password"
                  :type="passwordType"
                  v-model="loginForm.password"
                  :autofocus="passwordFocus"
                  placeholder="请输入密码"
                  class="login-input"
                  @on-enter="submit()"
                >
                  <i slot="prefix" class="iconfont icon-mima"></i>
                  <i
                    slot="suffix"
                    :class="`${iconPassword} iconfont font-16 auxiliary-color`"
                    @click="flag = !flag"
                  ></i>
                </Input>
              </FormItem>
              <FormItem
                class="login"
                style="margin-top: 30px; background: transparent !important"
              >
                <div class="btn loginBtn" @click="submit()">
                  {{ loading ? "登录中..." : "登 录" }}
                </div>
              </FormItem>
            </Form>
            <!-- <div class="bottom_kpi">
              <i class="iconfont icon-pki"></i>
              <span>PKI登录</span>
            </div> -->
          </div>
        </div>
        <div class="footer">
          <div class="login-logo">
            <img src="@/assets/img/login/logo.png" alt="" />
          </div>
          <div class="line"></div>
          <div class="copyright">
            <p>版本号: V{{ version }} 推荐浏览器: Chrome 50 及以上版本</p>
            <p v-for="(item,index) in copyright" :key="index">{{ item }}</p>
          </div>
        </div>
      </div>
      <video
        :style="fixStyle"
        autoplay
        loop
        class="fillWidth"
        v-on:canplay="canplay"
        muted
      >
        <source src="@/assets/img/login/login-video.mp4" type="video/mp4" />
        浏览器不支持 video 标签，建议升级浏览器。
      </video>
      <div class="poster hidden" v-if="!vedioCanPlay">
        <img
          :style="fixStyle"
          src="@/assets/img/login/light-login-bg.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>
<script>
import { getLoginType, getParamDataByKeys } from "@/api/user.js";
import { getUserInfo } from "@/libs/configuration/util.permission";
import config from "../../../package.json";

export default {
  data() {
    return {
      copyright:copyright,
      vedioCanPlay: false,
      fixStyle: "",
      loading: false,
      isShowCompanyInfo: "1", // 是否展示公司信息
      loginForm: {
        username: "",
        password: "",
      },
      passwordFocus: false,
      queryUser: {},
      tabList: [],
      tabActive: 1,
      typeList: [
        { label: "用户名", value: "1", iconName: "icon-yonghu" },
        { label: "身份证", value: "2", iconName: "icon-shenfenzheng1" },
        { label: "警号", value: "3", iconName: "icon-security-shield-full" },
      ],
      flag: false, //密码眼睛
      isReload: true, // 默认不显示页面内容
    };
  },
  beforeRouteEnter(to, from, next) {
    debugger;
    next((vm) => {
      if (from.name) {
        // window.location.reload();
      } else {
        vm.isReload = true;
      }
    });
  },
  computed: {
    version() {
      return config.version.split("-")[0];
    },
    isWithDbPswd() {
      return !!this.queryUser.username && !!this.queryUser.password;
    },
    passwordType() {
      return this.flag ? "text" : "password";
    },
    iconPassword() {
      return this.flag ? "icon-eye" : "icon-eye-close";
    },
  },

  mounted() {
    this.getLoginTypeList();
    this.canplay();
    this.onResizeHandler();
    // this.getParamDataByKeys()
  },
  methods: {
    canplay() {
      this.vedioCanPlay = true;
    },
    userEnter() {
      this.$refs.password.focus();
    },
    getLoginTypeList() {
      getLoginType().then((res) => {
        const { paramValue } = res.data;
        this.tabList = this.typeList.filter((item) => {
          return paramValue.includes(item.value);
        });
        this.tabActive = this.tabList[0].value;
      });
    },
    // 获取是否展示公司信息函数
    getParamDataByKeys() {
      getParamDataByKeys({ key: "ISSHOW_COMPANY_INFORMATION" }).then((res) => {
        this.isShowCompanyInfo = res.data.paramValue;
      });
    },
    async tabClick(item) {
      this.tabActive = item.value;
      this.$refs.loginForm.resetFields();
    },
    submit() {
      try {
        if (this.loginForm.username === "" || this.loginForm.password === "") {
          this.$Message.warning("用户名或密码不能为空");
          return;
        }
        if (
          this.loginForm.username.length > 16 ||
          this.loginForm.password.length < 6
        ) {
          this.$Message.warning("密码长度不能大于16位或者小于6位");
          return;
        }
        if (this.loading) return false;
        this.loading = true;
        this.$store
          .dispatch("handleLogin", {
            ...this.loginForm,
            username: `${this.tabActive},${this.loginForm.username}`,
          })
          .then((data) => {
            this.$Message.success("登录成功");
            getUserInfo("from-login", this.$route);
          })
          .finally(() => {
            this.loading = false;
          });
      } catch (err) {
        this.$Message.error("登录失败");
        this.loading = false;
        console.error(err);
      }
    },
    onResizeHandler() {
      window.onresize = () => {
        const windowWidth = document.body.clientWidth;
        const windowHeight = document.body.clientHeight;
        const windowAspectRatio = windowHeight / windowWidth;
        let videoWidth;
        let videoHeight;
        if (windowAspectRatio < 0.5625) {
          videoWidth = windowWidth;
          videoHeight = videoWidth * 0.5625;
          this.fixStyle = {
            height: windowWidth * 0.5625 + "px",
            width: windowWidth + "px",
            "margin-bottom": (windowHeight - videoHeight) / 2 + "px",
            "margin-left": "initial",
          };
        } else {
          videoHeight = windowHeight;
          videoWidth = videoHeight / 0.5625;
          this.fixStyle = {
            height: windowHeight + "px",
            width: windowHeight / 0.5625 + "px",
            "margin-left": (windowWidth - videoWidth) / 2 + "px",
            "margin-bottom": "initial",
          };
        }
      };
      window.onresize();
    },
  },
};
</script>
<style lang="less" scoped>
@import url("./index.less");
</style>
