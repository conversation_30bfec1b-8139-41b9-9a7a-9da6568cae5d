<template>
  <div class="carousel">
    <i
      class="iconfont icon-doubleleft mr-10 cursor-p"
      v-if="samePositionPoint.length > currentPageAction.pageSize"
      @click="toLeft"
    ></i>
    <div class="face-list-wrapper">
      <div
        class="face-list"
        :style="{
          marginLeft: `${transformWidth}`,
          width: `${totalSize * 100 + 20}%`,
        }"
      >
        <!-- <div class="face-list" :style="{transform: `translateX(${transformWidth})`,width: `${totalSize*100 + 2}%`}"> -->
        <div class="face-list-item" v-for="(item, index) in samePositionPoint">
          <div
            :key="index"
            @click="selectCollectionHandler(index, item)"
            class="list-img"
            :class="{ active: currentCollectionIndex === index }"
          >
            <img :src="item[imageKey]" alt="" />
            <span>{{ item.Index }}</span>
          </div>
          <p class="p-name-box" :title="item[nameKey]">
            {{ item[nameKey] || "--" }}
          </p>
        </div>
      </div>
    </div>
    <i
      class="iconfont icon-doubleright ml-10 cursor-p"
      v-if="samePositionPoint.length > currentPageAction.pageSize"
      @click="toRight"
    ></i>
  </div>
</template>

<script>
export default {
  props: {
    samePositionPoint: {
      type: Array,
      default: () => [],
    },
    nameKey: {
      type: String,
      default: "name",
    },
    imageKey: {
      type: String,
      default: "traitImg",
    },
  },
  data() {
    return {
      // collectionList: [{ count: 1 }, { count: 2 }, { count: 3 }, { count: 4 }, { count: 5 }, { count: 6 }, { count: 7 }, { count: 8 }, { count: 9 },
      // { count: 10 }, { count: 11 }, { count: 12 }, { count: 13 }, { count: 14 }, { count: 15 }, { count: 16 }, { count: 17 }, { count: 18 }, { count: 19 },
      // { count: 21 }, { count: 22 }, { count: 23 }, { count: 24 }, { count: 25 }, { count: 26 }, { count: 27 }, { count: 28 }, { count: 29 },
      // { count: 30 }],
      currentCollectionIndex: 0,
      currentPageAction: {
        pageSize: 10,
        pageNum: 1,
      },
      transformWidth: 0,
    };
  },
  created() {},
  methods: {
    init(index) {
      this.transformWidth = 0;
      this.currentPageAction = {
        pageSize: 10,
        pageNum: 1,
      };
      this.currentCollectionIndex = index;
    },
    selectCollectionHandler(index, item) {
      if (this.currentCollectionIndex != index) {
        this.currentCollectionIndex = index;
        this.$emit("changeVid", item);
      }
    },
    toLeft() {
      if (this.currentPageAction.pageNum > 1) {
        this.currentPageAction.pageNum--;
        this.transformWidth = `${-(this.currentPageAction.pageNum - 1) * 100}%`;
      }
    },
    toRight() {
      if (this.currentPageAction.pageNum < this.totalSize) {
        this.currentPageAction.pageNum++;
        this.transformWidth = `${-(this.currentPageAction.pageNum - 1) * 100}%`;
      }
    },
  },
  watch: {},
  mounted() {
    // 计算页数
    const dom = document.querySelector(".face-list-wrapper");
    if (dom) {
      this.currentPageAction.pageSize = Math.floor(dom.offsetWidth / 77);
    }
  },
  computed: {
    totalSize() {
      return Math.ceil(
        this.samePositionPoint.length / this.currentPageAction.pageSize
      );
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.carousel {
  position: relative;
  // height: 70px;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.face-list-wrapper {
  overflow: hidden;
  width: 100%;
}
.face-list {
  transform: translateX(0px);
  margin-left: 0;
  transition: margin-left 2s ease;
  display: flex;
  .face-list-item {
    width: fit-content;
    .p-name-box {
      width: 65px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
  .list-img {
    width: 65px;
    height: 65px;
    border: 1px solid #d3d7de;
    margin-right: 12px;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    display: inline-block;
    > img {
      max-width: 100%;
      max-height: 100%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    > span {
      display: inline-block;
      width: 25px;
      height: 16px;
      line-height: 16px;
      background: #2c86f8;
      text-align: center;
      border-radius: 0px 0px 4px 0px;
      font-size: 12px;
      color: #ffffff;
      position: absolute;
      left: 0;
      top: -1px;
    }
  }
  .active {
    border: 3px solid rgba(44, 134, 248, 1);
    color: #2c86f8;
  }
  .place-name {
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.8);
  }
}
</style>
