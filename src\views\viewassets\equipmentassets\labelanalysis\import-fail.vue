<template>
  <ui-modal v-model="visible" title="导入失败设备" :styles="styles" footer-hide>
    <ui-table
      class="ui-table auto-fill"
      ref="tableRef"
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="500"
    >
    </ui-table>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    tableData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '4rem',
      },
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
        },
      ],
    };
  },
  created() {},
  methods: {},
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
