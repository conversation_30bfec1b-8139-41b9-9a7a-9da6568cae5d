<template>
  <ui-modal ref="modal" :title="title" v-model="visible" :width="width">
    <div class="bg">
      <div class="form">
        <Form ref="form" :model="form" :rules="rules" :label-width="135">
          <FormItem label="SIP国际服务编码" prop="domainId">
            <Input v-model="form.domainId" placeholder="请输入SIP国际服务编码"></Input>
          </FormItem>
          <FormItem label="SIP服务国际域" prop="domain">
            <Input placeholder="请输入SIP服务国际域" v-model="form.domain"></Input>
          </FormItem>
          <FormItem label="SIP服务IP" prop="ip">
            <Input placeholder="请输入SIP服务IP" v-model="form.ip"></Input>
          </FormItem>
          <FormItem label="SIP服务端口" prop="port">
            <Input placeholder="请输入SIP服务端口" v-model.number="form.port"></Input>
          </FormItem>
          <FormItem label="SIP认证密码" prop="pwd">
            <Input placeholder="请输入SIP认证密码" v-model="form.pwd"></Input>
          </FormItem>
        </Form>
      </div>
    </div>
    <template slot="footer">
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" @click="query" class="plr-30">确 定</Button>
    </template>
  </ui-modal>
</template>
<script>
import algorithm from '@/config/api/algorithm';
export default {
  name: 'videoConfig',
  props: {},
  data() {
    return {
      width: 700,
      form: {},
      rules: {
        domainId: [{ required: true, message: '请填写SIP国际服务编码', trigger: 'blur' }],
        domain: [{ required: true, message: '请填写SIP服务国际域', trigger: 'blur' }],
        ip: [{ required: true, message: '请填写SIP服务IP', trigger: 'blur' }],
        port: [{ required: true, message: '请填写SIP服务端口', trigger: 'blur', type: 'number' }],
      },
      title: '',
      visible: false,
    };
  },
  created() {
    // this.$http.get(algorithm.gbConfig).then((res) => {
    //   if (res.data.code == 200) {
    //     this.form = res.data.data
    //     console.log(this.form)
    //   }
    // })
  },
  methods: {
    query() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$http.post(algorithm.gbConfig, this.form).then((res) => {
            if (res.data.code == 200) {
              this.$Message.success('配置成功');
              this.visible = false;
              this.$emit('addSuccess');
            }
          });
        }
      });
    },
    showModal(val, row) {
      switch (val) {
        case 1:
          this.title = '本域配置';
          // this.$refs.form.resetFields()
          break;
        case 2:
          this.form = row;
          this.title = '编辑本域配置';
          break;
      }
      this.visible = true;
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  padding: 0;
}
.bg {
  width: 100%;
  height: 100%;
  // padding-top: 10%;
  padding: 24px 50px 10px 50px;
  // background: url('../../../../assets/img/vediobg.png');
  background-size: 100% 100%;
}
.form {
  // width: 600px;
  margin: 0 auto;
  // margin-top: 33px;
}
</style>
