<template>
  <ui-modal v-model="visible" :title="title" :footerHide="true" :styles="styles">
    <div class="detail-contain auto-fill">
      <div class="detail-header">
        <span>
          <span class="mr-lg base-text-color f-14">最终结果</span>
          <i class="icon-font f-80 mr-sm vt-middle" :class="getClass"></i>
        </span>
        <span class="explain-text">{{ explainText }}</span>
      </div>
      <div class="auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #satisfy="{ row }">
            <span :class="row.cv ? 'font-green' : 'font-red'">
              {{ row.cv === '1' ? '满足' : '不满足' }}
            </span>
          </template>
        </ui-table>
      </div>
    </div>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'reason-detail',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '新增指标名称',
    },
    reasonDetailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
      tableColumns: [
        { type: 'index', tooltip: true, width: 50, title: '序号', align: 'center' },
        { title: '条件', key: 'message', tooltip: true, align: 'left', minWidth: 200 },
        { title: '满足情况', slot: 'satisfy', tooltip: true, align: 'left', width: 200 },
      ],
      tableData: [],
      loading: false,
      criteriaData: [
        { dataKey: 1, dataValue: '所有条件满足' },
        { dataKey: 2, dataValue: '任一条件满足' },
      ],
      explainText: '',
    };
  },
  methods: {
    async getOsdDetail() {
      try {
        const params = {
          indexId: this.$route.query.indexId,
          batchId: this.$route.query.batchId,
          displayType: this.$route.query.statisticType,
          customParameters: {
            id: this.reasonDetailData.id,
          },
        };
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        this.tableData = data;
      } catch (e) {
        console.log(e);
      }
    },
  },
  computed: {
    getClass() {
      let classArr = [];
      if (this.reasonDetailData.qualified === '1') {
        classArr = ['icon-hege1', 'font-green'];
      } else {
        classArr = ['icon-buhege', 'font-red'];
      }
      return classArr;
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
    reasonDetailData: {
      handler(val) {
        if (!this.visible) return false;
        const criteriaStandardVal = this.criteriaData.find((item) => val.criteriaStandard === item.dataKey);
        this.explainText = `说明：${criteriaStandardVal.dataValue}，数据${val.qualifiedName}`;
        this.getOsdDetail();
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.detail-contain {
  width: 100%;
  height: 600px;
  padding: 0 15px;
}
.detail-header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.explain-text {
  //color: #2b84e2;
  color: var(--color-content);
}
.f-80 {
  font-size: 80px;
}
</style>
