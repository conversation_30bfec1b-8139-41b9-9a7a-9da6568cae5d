/**
 * 统计: 多种纬度
 */
export let oneType = [
  {
    id: '1',
    name: '一类点',
    filedName: 'oneDwAmount',
    color: $var('--color-yellow-11'),
  },
  {
    id: '2',
    name: '二三类点',
    filedName: 'twoDwAmount',
    color: $var('--color-purple-10'),
  },
  {
    id: '4',
    name: '内部监控',
    filedName: 'innerDwAmount',
    color: $var('--color-blue-20'),
  },
];
export let twoType = [
  {
    id: '1',
    name: '视频监控',
    filedName: 'videoSurveillanceAmount',
    areaFiledName: 'videoAmount', // 采集类型区域字段名称
    color: $var('--color-blue-18'),
  },
  {
    id: '3',
    name: '人脸卡口',
    filedName: 'faceSwanAmount',
    areaFiledName: 'faceAmount', // 采集类型区域字段名称
    color: $var('--color-purple-9'),
  },
  {
    id: '2',
    name: '车辆卡口',
    filedName: 'vehicleBayonetAmount',
    areaFiledName: 'vehicleAmount', // 采集类型区域字段名称
    color: $var('--color-blue-19'),
  },
];
export const statisticList = [
  {
    name: '设备总量',
    filedName: 'deviceTotalAmount',
    value: 0,
    icon: 'icon-shebeizongliang',
    liBgColor: 'var(--bg-card-gradient-light-blue)',
  },
  {
    name: '视频监控',
    filedName: 'videoSurveillanceAmount',
    value: 0,
    icon: 'icon-ivdg-shipinjiankong',
    liBgColor: 'var(--bg-card-gradient-light-green)',
    superiorName: 'videoDataAmount',
    juniorList: Object.assign([], oneType),
  },
  {
    name: '人脸卡口',
    filedName: 'faceSwanAmount',
    value: 0,
    icon: 'icon-renliankakou',
    liBgColor: 'var(--bg-card-gradient-blue-purple)',
    superiorName: 'faceDataAmount',
    juniorList: Object.assign([], oneType),
  },
  {
    name: '车辆卡口',
    filedName: 'vehicleBayonetAmount',
    value: 0,
    icon: 'icon-cheliangkakou',
    liBgColor: 'var(--bg-card-gradient-dark-blue)',
    superiorName: 'vehicleDataAmount',
    juniorList: Object.assign([], oneType),
  },
  {
    name: '一类点',
    filedName: 'oneDwAmount',
    value: 0,
    icon: 'icon-yileidian1',
    liBgColor: 'var(--bg-card-gradient-light-orange)',
    superiorName: 'oneDwDataAmount',
    juniorList: Object.assign([], twoType),
  },
  {
    name: '二三类点  ',
    filedName: 'twoDwAmount',
    value: 0,
    icon: 'icon-ersanleidian1',
    liBgColor: 'var(--bg-card-gradient-light-purple)',
    superiorName: 'twoDwDataAmount',
    juniorList: Object.assign([], twoType),
  },
  {
    name: '内部监控',
    filedName: 'innerDwAmount',
    value: 0,
    icon: 'icon-neibujiankong',
    liBgColor: 'var(--bg-card-gradient-blue-green)',
    superiorName: 'innerDwDataAmount',
    juniorList: Object.assign([], twoType),
  },
];
