<template>
  <ui-modal
    v-model="visible"
    :title="type === 'view' ? '查看帧标记' : '编辑帧标记'"
    :footer-hide="type === 'view' ? true : false"
    :r-width="type === 'view' ? 1370 : 500"
    @onOk="comfirmHandle"
  >
    <div class="content">
      <ui-image v-if="type === 'view'" :src="form.markImage" viewer />
      <div v-if="type === 'view'" class="video-play" @click="handleRealTime(1)">
        <Icon type="md-film" />
        录像回放
      </div>
      <div class="right">
        <div v-if="type === 'view'" class="title">标记信息</div>
        <Form ref="form" :model="form" :rules="ruleForm">
          <FormItem label="标记名称:" prop="markTitle">
            <Input
              v-if="type === 'edit'"
              v-model="form.markTitle"
              :maxlength="50"
              placeholder="请输入"
            />
            <span v-else>{{ form.markTitle }}</span>
          </FormItem>
          <FormItem label="标记颜色:" prop="markColor">
            <color-select
              v-if="type === 'edit'"
              :currentColor.sync="form.markColor"
            ></color-select>
            <div
              v-else
              class="color-single"
              :style="{ backgroundColor: form.markColor }"
            ></div>
          </FormItem>
          <div v-if="type === 'view'">
            <FormItem label="设备名称:" :title="form.deviceName">{{
              form.deviceName || ""
            }}</FormItem>
            <FormItem label="所属组织:">{{ form.orgName || "" }}</FormItem>
            <FormItem label="标记人:">{{ form.creator || "" }}</FormItem>
            <!-- <FormItem label="标记时间:">{{form.markTime ||''}}</FormItem> -->
          </div>
          <FormItem label="标记时间:" prop="markTime">
            <DatePicker
              v-if="type === 'edit'"
              v-model="form.markTime"
              :clearable="false"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择时间"
              transfer
              :options="datePickerOption"
              disabled
            ></DatePicker>
            <span v-else>{{ form.markTime }}</span>
          </FormItem>
          <FormItem label="标记描述:" prop="remark">
            <Input
              v-if="type === 'edit'"
              v-model="form.remark"
              placeholder="请输入"
              type="textarea"
              :rows="4"
            />
            <span v-else :title="form.remark" class="remark">{{
              form.remark
            }}</span>
          </FormItem>
        </Form>
      </div>
    </div>
    <Modal
      v-model="videoModal"
      draggable
      scrollable
      @on-cancel="handleCancel"
      :mask="false"
      class-name="vertical-pos-modal"
      :title="title == 0 ? '实时播放' : '录像回放'"
    >
      <div class="video-box">
        <h5-player
          ref="H5Player"
          sceneFrom="singleVideo"
          dbFullScreen
          :options="{ layout: '1*1' }"
          :deviceObj="deviceObj"
          :iframes="iframes"
        >
        </h5-player>
      </div>
      <div slot="footer"></div>
    </Modal>
  </ui-modal>
</template>
<script>
import colorSelect from "@/components/color-select.vue";
import { frameUpdate, getDeviceById } from "@/api/frame.js";
import { getRelationCameraByGbId } from "@/api/wisdom-cloud-search";
import { getFrontBackDate } from "@/util/modules/common";
export default {
  components: {
    colorSelect,
  },
  data() {
    return {
      videoModal: false,
      deviceObj: {},
      title: 0,
      type: "view", //查看/编辑
      visible: false,
      form: {
        markTitle: "",
        markColor: "",
        remark: "",
      },
      ruleForm: {
        markTitle: [{ required: true, message: "请输入", trigger: "blur" }],
      },
      iframes: [],
      datePickerOption: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now() - 86400000;
        },
      },
    };
  },
  methods: {
    // 初始化
    async show(item, type = "view") {
      this.type = type;
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.form = { ...item };
        this.form.markColor =
          item.markColor == "1"
            ? "#EB4B4B"
            : item.markColor == "2"
            ? "#F29F4C"
            : item.markColor == "3"
            ? "#FDEE38"
            : item.markColor == "4"
            ? "#67D28D"
            : "#2379F9";
      });
    },
    // 新增/编辑
    comfirmHandle() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            id: this.form.id,
            markColor:
              this.form.markColor == "#EB4B4B"
                ? "1"
                : this.form.markColor == "#F29F4C"
                ? "2"
                : this.form.markColor == "#FDEE38"
                ? "3"
                : this.form.markColor == "#67D28D"
                ? "4"
                : "5",
            markTitle: this.form.markTitle,
            remark: this.form.remark,
            markTime: new Date(this.form.markTime),
          };
          frameUpdate(params).then((res) => {
            this.visible = false;
            this.$Message.success("修改成功");
            this.$emit("refreshDataList");
          });
        }
      });
    },
    handleRealTime(index) {
      // console.log(this.form);
      this.iframes = [
        {
          absTime: this.$dayjs(this.form.markTime).valueOf(),
          color: this.form.markColor,
          class: "",
          template: `<img src="${this.form.markImage}"/>`,
        },
      ];
      getDeviceById(this.form.deviceId).then((res) => {
        if (!res.data) {
          this.$Message.error("未查询到关联摄像机");
          return;
        }
        this.videoModal = true;
        this.title = index;
        let params = {
          deviceGbId: res.data.gbId,
          deviceId: this.form.deviceId,
          deviceName: this.form.deviceName,
          geoPoint: {
            lon: res.data.longitude,
            lat: res.data.latitude,
          },
          devicetype: index == 0 ? liveType : vodType,
          playType: index == 0 ? "live" : "vod",
        };
        let begintime = getFrontBackDate(this.form.markTime, 15000, 1);
        let endtime = getFrontBackDate(this.form.markTime, 15000, 0);
        this.deviceObj =
          index == 0 ? params : { ...params, begintime, endtime };
        if (index == 0) {
          this.queryLog({
            muen: "视频中心",
            name: "标记管理",
            type: "4",
            remark: `查看【${params.deviceName}】实时视频`,
          });
        } else {
          this.queryLog({
            muen: "视频中心",
            name: "标记管理",
            type: "4",
            remark: `查看【${params.deviceName}】,【${this.$dayjs(
              begintime
            ).format("YYYY-MM-DD HH:mm:ss")}-${this.$dayjs(endtime).format(
              "YYYY-MM-DD HH:mm:ss"
            )}】的历史视频`,
          });
        }
      });
    },
    handleCancel() {
      this.deviceObj = {};
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  display: flex;
  margin-bottom: 20px;
  img {
    width: 75%;
    height: 600px;
    margin-right: 20px;
  }
  .ui-image {
    width: 75%;
    height: 600px;
    margin-right: 20px;
    margin-bottom: 20px;
  }
  .video-play {
    position: absolute;
    left: 35%;
    bottom: 10px;
    cursor: pointer;
    height: 40px;
    line-height: 40px;
    background: #e5e5e5;
    padding: 0 10px;
    border-radius: 4px;
  }
  .video-play:hover {
    color: #2c86f8;
  }
  .right {
    flex: 1;
    overflow: hidden;
    .title {
      font-weight: 700;
      color: rgba(0, 0, 0, 0.8);
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      &::before {
        content: "";
        height: 16px;
        width: 3px;
        display: inline-block;
        background-color: #2c86f8;
        vertical-align: middle;
        margin-right: 5px;
      }
    }
    .color-single {
      width: 20px;
      height: 20px;
      display: inline-block;
      vertical-align: middle;
      background-color: red;
    }
    /deep/ .ivu-form-item {
      display: flex;
      width: 100%;
      margin-bottom: 10px;
      .ivu-form-item-label {
        width: 76px;
        text-align-last: justify;
        &::before {
          display: none;
        }
      }
      .ivu-form-item-content {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      input,
      textarea {
        width: 200px;
      }
    }
    .remark {
      white-space: pre-wrap;
    }
  }
}
.video-box {
  width: 500px;
  height: 300px;
}
/deep/.vertical-pos-modal {
  /deep/ .ivu-modal-content-drag {
    top: calc(50% + 180px);
  }
}
</style>
