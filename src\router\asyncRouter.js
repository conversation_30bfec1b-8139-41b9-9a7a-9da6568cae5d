/*
 * @Date: 2024-12-18 15:22:33
 * @LastEditors: zheng<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-05-08 15:05:46
 * @FilePath: \icbd-view\src\router\asyncRouter.js
 */
// 引入路由文件这种的公共路由
// import { constantRoutes } from '@/router/index'

export function getAsyncRoutes(routes) {
  const res = [];
  // 定义路由中需要的自定名
  const keys = ["path", "name", "children", "redirect", "meta"];
  // 遍历路由数组去重组可用的路由
  routes.forEach((item) => {
    const newItem = {};
    // 一级路由定位至basic-layout，无需再次处理
    if (Number(item.parentId) === 0) {
      newItem.component = item.component;
    } else if(item.isExternalLink == 1){
      newItem.component = (resolve) =>
        require([`@/views/outside-app/index.vue`], resolve);
    } else {
      newItem.component = (resolve) =>
        require([`@/views${item.path}/index.vue`], resolve);
    }
    for (const key in item) {
      if (keys.includes(key)) {
        newItem[key] = item[key];
      }
    }
    // 若遍历的当前路由存在子路由，需要对子路由进行递归遍历
    if (newItem.children && newItem.children.length) {
      newItem.redirect = newItem.children[0].path;
      newItem.children = getAsyncRoutes(item.children);
    }
    res.push(newItem);
  });
  // 返回处理好且可用的路由数组
  return res;
}
