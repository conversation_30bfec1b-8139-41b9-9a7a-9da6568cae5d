<template>
  <div class="title-container">
    <span class="icon-font color-green f-16" :class="icon"></span>
    <span
      @click="clickItem(item)"
      class="color-green ml-md pointer f-16"
      :class="item.id === activeValue && tabData.length > 1 ? 'link-text-active' : ''"
      v-for="item in tabData"
      :key="item.id"
      >{{ item.label }}</span
    >
    <span class="color-green f-14 fr">
      <slot name="filter"></slot>
    </span>
  </div>
</template>

<script>
/**
 * @example
 * <HomeTitle icon="icon-shebeizichan">
 设备资产
 <template #filter>
 test
 </template>
 </HomeTitle>
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    icon: {},
    data: {},
    value: {},
  },
  data() {
    return {
      tabData: [],
      activeValue: '',
    };
  },
  computed: {},
  watch: {
    data: {
      handler(val) {
        this.tabData = val || [];
      },
      deep: true,
      immediate: true,
    },
    value: {
      handler(val) {
        this.activeValue = val;
      },
      deep: true,
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    clickItem(val) {
      this.activeValue = val.id;
      this.$emit('input', val.id);
      this.$emit('on-change', val.id, val);
    },
  },
};
</script>

<style lang="less" scoped>
.color-green {
  color: #30e9f5;
}
.title-container {
  position: relative;
  padding: 0 10px 0 10px;
  background: linear-gradient(90deg, rgba(64, 225, 254, 0.36) 0%, rgba(64, 225, 254, 0) 100%);
  height: 32px;
  line-height: 32px;
  width: 100%;
  .link-text-active {
    color: #ffffff;
    position: relative;
    &::before {
      position: absolute;
      content: '';
      width: 100%;
      height: 1px;
      background: #ffffff;
      bottom: -6px;
    }
  }
}
</style>
