<template>
  <div id="lineChartPanel" ref="lineChartPanel" class="echarts-panel"></div>
</template>
<script>
import * as echarts from "echarts";
import { throttle } from "lodash";
import elementResizeDetectorMaker from " element-resize-detector";

export default {
  name: "LineChart",
  components: {},
  props: {},
  computed: {},
  data() {
    return {
      lineChart: null,
    };
  },
  watch: {},
  created() {},
  mounted() {
    this.initEcharts();
    const _this = this;
    const erd = elementResizeDetectorMaker();
    erd.listenTo(document.getElementById("lineChartPanel"), (element) => {
      _this.$nextTick(() => {
        // 监听到事件后执行的业务逻辑
      });
    });
  },
  beforeDestroy() {
    if (this.lineChart) {
      echarts.dispose(this.$refs.echarts);
      this.echarts = null;
    }
  },
  methods: {
    initEcharts() {
      if (this.lineChart) {
        echarts.dispose(this.$refs.echarts);
        this.lineChart = null;
      }
      this.lineChart = echarts.init(this.$refs.lineChartPanel);
      this.setChartOption();
    },
    // 改变函数 使用节流
    handleIViewTableResize: throttle(function (el) {
      this.fullHeight = el.clientHeight;
    }, 100),
    setChartOption() {
      const chartOption = {
        xAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisPointer: {
            value: "12",
            snap: true,
            lineStyle: {
              color: "#7581BD",
              width: 2,
            },
            handle: {
              show: true,
              color: "#7581BD",
            },
          },
          data: [
            0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18,
            19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
          ],
        },
        yAxis: {
          splitLine: {
            lineStyle: {
              color: ["#07355E"],
            },
          },
          type: "value",
        },
        series: [
          {
            data: [
              100, 101, 3, 103, 10, 105, 106, 107, 108, 12, 110, 111, 112, 113,
              114, 33, 116, 117, 118, 78, 120, 100, 122, 123, 124, 125, 126,
              127, 128, 129, 130,
            ],
            type: "line",
          },
        ],
      };
      this.lineChart.setOption(chartOption);
    },
    showLoading() {
      this.echarts.showLoading({
        // text: '加载中',
        color: "#2B84E2",
        textColor: "#fff",
        maskColor: "#08264d",
        zlevel: 0,
      });
    },
    hideLoading() {
      this.echarts.hideLoading();
    },
  },
};
</script>
<style lang="less" scoped>
.echarts-panel {
  height: 100%;
  width: 400px;
  background-color: #ccc;
}
</style>
