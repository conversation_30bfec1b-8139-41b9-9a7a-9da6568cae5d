export const GB28181 = 'GB28181'; //联网平台对账
export const GAT1400_FACE = 'GAT1400_FACE'; //人脸视图库对账
export const GAT1400_VEHICLE = 'GAT1400_VEHICLE'; //车辆视图库对账

export const TAB_LIST = [
  { label: '视频监控对账', desc: '联网平台', value: GB28181, icon: 'icon-shujujiance' },
  { label: '人脸卡口对账', desc: '人脸视图库', value: GAT1400_FACE, icon: 'icon-renliankakou' },
  { label: '车辆卡口对账', desc: '车辆视图库', value: GAT1400_VEHICLE, icon: 'icon-cheliangkakou' },
];

/**
 * 上下级对账指标
 * @type {{indexType: string, indexModule: string, indexName: string, indexId: string, componentName: string, indexModuleName: string}}
 */
export const VIDEO_DEVICE_REVOCATION = {
  'indexId': '4029',
  'indexModule': '4',
  'indexModuleName': '视频流数据',
  'indexName': '视频监控设备撤销率',
  'indexType': 'VIDEO_DEVICE_REVOCATION',
  'componentName': 'VideoDeviceRevocation',
};

export const COMPARE_TAB_LIST = [
  {
    label: '本级对账',
    componentName: 'CurrentLevelCompare',
  },
  {
    label: '上下级对账',
    componentName: 'SupSubCompare',
  },
];
/**
 * 韦恩图 颜色
 * @type {{A: string, B: string, 'A,B': string}}
 */
export const COLOR_MAP = {
  'A': $var('--color-opacity-pink'),
  'B': $var('--color-opacity-blue'),
  'A,B': $var('--color-green-14'),
};
