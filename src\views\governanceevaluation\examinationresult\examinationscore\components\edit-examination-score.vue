<!--编辑考核成绩-->
<template>
  <div class="examination-container">
    <ui-modal v-model="visible" title="编辑考核成绩" :width="95" @query="handleQuery" :loading="saveLoading">
      <div class="head-wrapper">
        <div class="base-text-color f-14">
          <span class="icon-font icon-renwushuliang-01 f-14 vt-middle mr-xs icon"></span>
          <span class="label-text">考核任务：</span>
          <span class="value-text">{{ currentTaskObject.tableTitleTaskName }}</span>
          <span class="icon-font icon-shijian f-14 vt-middle ml-md mr-xs icon"></span>
          <span class="label-text">考核时间：</span>
          <span class="value-text">{{ currentTaskObject.year }}年{{ currentTaskObject.month }}月</span>
          <div class="mb-md">
            <span class="tips">说明：【考核方案】中配置为【手动录入】的考核项才支持手动录入或编辑考核成绩！</span>
          </div>
        </div>
        <div>
          <Upload
            :action="importEditResultExcel"
            ref="upload"
            class="inline"
            :show-upload-list="false"
            :headers="headers"
            :data="uploadParams"
            :before-upload="beforeUpload"
            :on-success="importSuccess"
            :on-error="importError"
          >
            <Button type="primary" class="" :loading="importLoading">
              <i class="icon-font icon-daoruwentishebei f-12"></i>
              <span class="vt-middle ml-sm">导入</span>
            </Button>
          </Upload>
          <Button type="primary" class="ml-sm" @click="exportExcel" :loading="exportLoading">
            <i class="icon-font icon-daochu f-14"></i>
            <span class="vt-middle ml-sm">导出</span>
          </Button>
        </div>
      </div>

      <div class="tip-wrapper"></div>
      <div class="">
        <ui-table
          class="ui-table"
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          full-border
          v-ui-loading="{ tableData, loading }"
        >
        </ui-table>
      </div>
    </ui-modal>
    <ui-modal v-model="editVisible" title="编辑考核成绩" :width="25" @query="handleSetScore">
      <InputNumber v-model="score" placeholder="请输入考核成绩" class="width-percent"></InputNumber>
    </ui-modal>
    <score-detail ref="scoreDetail" @change="handleChange"></score-detail>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';

export default {
  name: 'edit-result-examination',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    ScoreDetail: require('./score-detail').default,
  },
  mixins: [downLoadTips],
  props: {},
  data() {
    return {
      visible: false,
      loading: false,
      tableData: [],
      tableHeader: [],
      tableColumns: [],
      preTableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '行政区划',
          key: 'regionName',
          align: 'center',
          width: 150,
        },
      ],
      currentTaskObject: {},
      baseIndexType: [],
      editVisible: false,
      score: null,
      currentRow: {},
      parentName: '',
      saveLoading: false,

      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
      importLoading: false,
      exportLoading: false,
      importEditResultExcel: governanceevaluation.importEditResultExcel,
    };
  },
  computed: {
    uploadParams() {
      if (!this.currentTaskObject) return {};
      let { examTaskId, examSchemeId, year, month, orgRegeionCode } = this.currentTaskObject;
      return {
        month,
        year,
        examSchemeId,
        examTaskId,
        orgRegeionCode,
      };
    },
  },
  methods: {
    async exportExcel() {
      try {
        this.exportLoading = true;
        const res = await this.$http.post(governanceevaluation.exportEditResultExcel, this.currentTaskObject, {
          responseType: 'blob',
        });
        await this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportLoading = false;
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    importSuccess(res) {
      this.initList();
      this.importLoading = false;
      this.$Message.success(res.msg);
    },
    async initList() {
      this.tableHeader = [];
      this.tableColumns = [];
      await this.getExamItemTypeTwo(this.currentTaskObject);
    },
    async init(params) {
      this.visible = true;
      this.tableHeader = [];
      this.tableColumns = [];
      this.currentTaskObject = params;
      await this.getExamItemTypeTwo(params);
    },
    handleChange(tableData) {
      let { column, index } = this.currentRow;
      this.tableData[index]['examModelChildrenList'][column['_index'] - 2]['examModelScoreChildrenList'] = JSON.parse(
        JSON.stringify(tableData),
      );
    },
    //组件外部调用
    async getExamItemTypeTwo(params) {
      try {
        this.loading = true;
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getExamItemTypeTwo, params);
        if (!data.headers.length) {
          this.$Message.error('系统考核不支持编辑考核成绩！');
          this.visible = false;
          return;
        }
        let regionVoList = data.regionVoList || [];
        this.tableHeader = data.headers || [];
        let examModelList = data.examModelList || [];
        this.baseIndexType = [];
        this.getTableColumns(this.tableHeader);
        this.getTableData(this.baseIndexType, regionVoList, examModelList);
        this.tableColumns = [...this.preTableColumns, ...this.tableHeader];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    getTableData(baseIndexType, regionVoList, examModelList) {
      this.tableData = [];
      regionVoList.forEach((item) => {
        let element = {
          regionName: item.regionName,
          regionCode: item.regionCode,
          examModelChildrenList: [],
        };
        baseIndexType.forEach((value) => {
          element.examModelChildrenList.push({
            code: value.code,
            parentCode: value.parentCode,
            examModelScoreChildrenList: [{ scoreDate: this.generateScoreDate(), score: null }],
          });
        });
        examModelList.forEach((modeList) => {
          if (item.regionCode === modeList.regionCode)
            modeList.examModelChildrenList.forEach((saveChildrenList) => {
              element.examModelChildrenList.forEach((defaultChildrenList) => {
                if (saveChildrenList.code === defaultChildrenList.code) {
                  defaultChildrenList.examModelScoreChildrenList = saveChildrenList.examModelScoreChildrenList;
                }
              });
            });
        });
        this.tableData.push(element);
      });
    },
    renderHeader(h, { column }) {
      return (
        <div>
          <span>{column.title}</span>
          <span class="block t-center">({column.score || 0}分)</span>
        </div>
      );
    },
    renderColumn(h, { row, column, index }) {
      // 2 平均 3 单个
      return (
        <span>
          {column.examMethodType === '2' ? (
            <span
              class="link"
              {...{
                on: {
                  'click': () => {
                    this.editColumn(row, column, index);
                  },
                },
              }}
            >
              录入每日成绩
            </span>
          ) : (
            <span>
              <span>{row['examModelChildrenList'][column['_index'] - 2]['examModelScoreChildrenList'][0]?.score}</span>
              <span
                class="link ml-sm"
                {...{
                  on: {
                    'click': () => {
                      this.editColumn(row, column, index);
                    },
                  },
                }}
              >
                修改
              </span>
            </span>
          )}
        </span>
      );
    },
    getTableColumns(data) {
      data.forEach((item) => {
        this.$set(item, 'title', item.name);
        this.$set(item, 'align', 'center');
        this.$set(item, 'minWidth', item.name.length * 18);
        if (item.level === 3) {
          this.baseIndexType.push(item);
        }
        if (item.level === 2) {
          this.parentName = item.name;
        }
        if (item.children) {
          this.getTableColumns(item.children || []);
        } else {
          delete item.children;
          this.$set(item, 'renderHeader', this.renderHeader);
          this.$set(item, 'render', this.renderColumn);
          this.$set(item, 'parentName', this.parentName);
        }
      });
    },
    async handleQuery() {
      try {
        this.saveLoading = true;
        let { examSchemeId, examTaskId, month, year } = this.currentTaskObject;
        let params = {
          examSchemeId,
          examTaskId,
          month,
          year,
          headers: this.tableHeader,
          examModelList: this.tableData,
        };
        let { data } = await this.$http.post(governanceevaluation.postStatisticsSave, params);
        this.$emit('on-search');
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      } finally {
        this.saveLoading = false;
      }
    },
    /* if (this.score > column.score || this.score < 0) {
        return this.$Message.error(`分项分值不小于0不大于${column.score}分`)
      } */
    handleSetScore() {
      let { column, index } = this.currentRow;
      if (this.score > column.score) {
        return this.$Message.error(`分项分值不大于${column.score}分`);
      }
      this.$set(this.tableData[index]['examModelChildrenList'][column['_index'] - 2], 'examModelScoreChildrenList', [
        { scoreDate: this.generateScoreDate(), score: this.score },
      ]);
      this.editVisible = false;
    },
    editColumn(row, column, index) {
      this.currentRow = { row, column, index };
      if (column.examMethodType === '2') {
        this.$refs.scoreDetail.init(this.currentRow, this.currentTaskObject);
      } else {
        this.editVisible = true;
        this.score = row['examModelChildrenList'][column['_index'] - 2]['examModelScoreChildrenList'][0]?.score || 0;
      }
    },
    /**
     * 格式化日期
     * @param date {Date}
     * @returns {string} YYYY-MM
     */
    formatDate(date) {
      let d = new Date(date),
        month = '' + (d.getMonth() + 1),
        day = '' + d.getDate(),
        year = d.getFullYear();

      if (month.length < 2) month = '0' + month;
      if (day.length < 2) day = '0' + day;
      return [year, month, day].join('-');
    },
    /**
     * 获取某年某月的最后一天
     * @param year  {number} 年
     * @param month {number} 月
     * @returns {Date}
     */
    getLastDayOfMonth(year, month) {
      return new Date(year, month, 0);
    },
    /**
     * 返回格式化后的日期
     * @returns {string}
     */
    generateScoreDate() {
      let { year, month } = this.currentTaskObject;
      let lastDay = this.getLastDayOfMonth(year, month);
      let reviewDate = new Date();
      let equal = this.equalDate(lastDay, reviewDate);
      if (equal) {
        return this.formatDate(new Date());
      }
      return this.formatDate(lastDay);
    },
    /**
     * 对比年月
     * @param date {Date}
     * @param targetDate {Date}
     * @returns {Boolean}
     */
    equalDate(date, targetDate) {
      return date.getFullYear() === targetDate.getFullYear() && date.getMonth() + 1 === targetDate.getMonth() + 1;
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .examination-container {
    position: relative;

    .head-wrapper {
      display: flex;
      justify-content: space-between;
    }
    .ui-table {
      height: 600px;
    }

    .icon {
      color: #888888;
    }
    .label-text {
      color: rgba(0, 0, 0, 0.45);
    }
    .value-text {
      color: rgba(0, 0, 0, 0.8);
    }

    .tips {
      color: #f29f4c;
    }
    .tip-wrapper {
      height: 15px;
      &:before {
        content: '';
        position: absolute;
        background-color: var(--border-color);
        height: 1px;
        width: 100%;
        left: 0;
      }
    }

    .ui-table {
      @{_deep} .link {
        color: var(--color-primary);
        cursor: pointer;
        text-decoration: underline;
      }

      @{_deep}.total {
        font-size: 22px;
      }

      @{_deep}.total-score {
        color: var(--color-bluish-green-text);
      }
    }
  }
}
.examination-container {
  position: relative;

  .head-wrapper {
    display: flex;
    justify-content: space-between;
  }
  .ui-table {
    height: 600px;
  }
  .icon {
    color: rgba(25, 213, 246, 1);
  }
  .label-text {
    color: rgba(25, 213, 246, 1);
  }
  .label-text {
    color: rgba(25, 213, 246, 1);
  }

  .tips {
    color: #e44f22;
  }
  .tip-wrapper {
    height: 15px;
    &:before {
      content: '';
      position: absolute;
      background-color: #0d477d;
      height: 1px;
      width: 100%;
      left: 0;
    }
  }

  .ui-table {
    @{_deep} .link {
      color: var(--color-primary);
      cursor: pointer;
      text-decoration: underline;
    }

    @{_deep}.total {
      font-size: 22px;
    }

    @{_deep}.total-score {
      color: var(--color-bluish-green-text);
    }
  }
}
</style>
