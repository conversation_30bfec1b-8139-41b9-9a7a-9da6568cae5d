<template>
  <div class="page-assessmentresult auto-fill" v-ui-loading="{ loading: assessResLoading }">
    <div class="right-content">
      <div class="title-center">
        <div class="conter-center">
          <span class="fs24"> {{ month }}月公安视频图像数据治理考核通报 </span>
          <div class="time fs12">
            <i class="iconfont icon-shijian"></i>
            &nbsp;发布时间： {{ releaseTime }}
          </div>
        </div>
      </div>
      <assessment-detail-text :assessment-text-obj="assessmentTextObj"></assessment-detail-text>
      <div class="rowTitle">
        <div>
          <i class="icon-font icon-yuekaohepaihang-01 f-20"></i>
          <span>月考核排行</span>
        </div>
      </div>
      <assessment-chart :month-rank="monthRank"></assessment-chart>
      <div class="rowTitle">
        <div>
          <i class="icon-font icon-yuehejieguotongbao-01 f-20"></i>
          月考核结果通报
        </div>
        <Button
          v-if="monthResultNotificationTabsLen"
          type="primary"
          @click="exportMonthResultHandle"
          :loading="btnLoading"
        >
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="导出"></i>
          <span class="vt-middle">导出</span>
        </Button>
      </div>
      <div class="tables">
        <monthly-assessment-result
          ref="MonthlyAssessmentResult"
          :month-result-notification="monthResultNotification"
          @pictureAttributeIntegrityBtn="pictureAttributeIntegrityBtn"
        ></monthly-assessment-result>
      </div>

      <div class="rowTitle">
        <div>
          <i class="icon-font icon-yuezuixinjianceqingkuang-01 f-20"></i>
          月最新检测情况
        </div>
        <Button type="primary" @click="monthNewImport" :loading="btnNewLoading">
          <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="导出"></i>
          <span class="vt-middle">导出</span>
        </Button>
      </div>
      <div class="tables table-module detection-table">
        <monthly-new-detection
          :month-new-result="monthNewResult"
          @selectModuleClick="selectDetectionModuleHandle"
        ></monthly-new-detection>
      </div>

      <div class="rowTitle down">
        <div>
          <i class="icon-font icon-fujianxiazai f-16"></i>
          附件下载
        </div>
      </div>
      <div class="downloads">
        <div v-for="(item, index) in downloadList" :key="index">
          {{ item.exceptionDownName }}
          <span class="size">{{ item.exceptionDownSize || 0 }}</span>
          <a :href="item.exceptionDownUrl" :download="item.exceptionDownName">下载</a>
        </div>
      </div>
      <picture-attribute-integrity
        ref="pictureAttributeIntegrity"
        :taskObj="infoObj"
        @selectModuleClick="selectModuleHandle"
      />
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import notice from '@/config/api/notice';
import downLoadTips from '@/mixins/download-tips';

export default {
  name: 'AssessmentResult',
  mixins: [downLoadTips],
  data() {
    return {
      month: 12,
      releaseTime: null,
      tableTitleYear: '',
      infoObj: '',
      taskList: [],
      tableTitleTaskName: '',
      searchData: {
        examSchemeId: '',
        examTaskId: '',
        orgCode: '',
      },
      btnLoading: false,
      btnNewLoading: false,
      echartsLoading: true,
      downloadList: [],
      componentName: null,
      assessmentTextObj: {},
      monthRank: '',
      monthResultNotification: '',
      monthResultNotificationTabsLen: 0,
      monthNewResult: '',
      assessResLoading: false,
    };
  },
  created() {
    this.assessResLoading = true;
  },
  mounted() {
    this.month = this.$route.query.month;
    this.releaseTime = this.$route.query.releaseTime;
    this.getResultDetail();
  },
  methods: {
    async getResultDetail() {
      try {
        const notificationInfoId = this.$route.query.id;
        const {
          data: { data },
        } = await this.$http.get(`${notice.queryByNotificationId}?notificationInfoId=${notificationInfoId}`, {});
        const {
          monthRank,
          monthNewResult,
          monthNewResultExport,
          monthResultNotification,
          overviewContent,
          overviewContentDetail,
          themeContent,
          downVos,
        } = data;
        this.assessmentTextObj = {
          overviewContent,
          overviewContentDetail: overviewContentDetail ? JSON.parse(overviewContentDetail) : '',
          themeContent,
        };
        this.monthRank = monthRank;
        this.monthResultNotification = monthResultNotification;
        this.monthResultNotificationTabsLen = monthResultNotification
          ? JSON.parse(monthResultNotification).body.length
          : 0;
        this.monthNewResult = monthNewResult;
        this.downloadList = downVos;
        this.monthNewResultExport = monthNewResultExport;
        this.assessResLoading = false;
      } catch (e) {
        console.log(e);
        this.assessResLoading = false;
      }
    },
    pictureAttributeIntegrityBtn(row) {
      this.infoObj = {
        ORG_REGEION_CODE: row.row.ORG_REGEION_CODE,
        title: row.column.title,
        parentTitle: row.column.parentTitle,
        score: row.column.score,
        examContentItemMonthResultId: row.row[row.column.key].examContentItemMonthResultId,
      };
      this.$refs.pictureAttributeIntegrity.init();
    },
    selectModuleHandle() {
      this.$emit('changeComponent', 'detectionToOverview');
    },
    selectDetectionModuleHandle() {
      this.$emit('changeComponent', 'overviewEvaluation');
    },
    // 月考核结果通报导出
    async exportMonthResultHandle() {
      try {
        this.btnLoading = true;
        const { headers, body } = JSON.parse(this.monthResultNotification);
        const params = { headers, body };
        let data = await this.$http.post(governanceevaluation.exportAssessment, params, {
          responseType: 'blob',
        });
        const year = this.$route.query.year;
        const orgName = body[0][0].orgRegeionName || '';
        await this.$util.common.exportfile(
          data,
          `${year}年${this.month}月-${orgName}视图数据月考核结果通报 -【IVDG】-【${this.$util.common.formatDate(
            new Date(),
          )}】`,
          'application/vnd.ms-excel;charset=UTF-8',
        );
      } catch (e) {
        console.log(e);
      } finally {
        this.btnLoading = false;
      }
    },
    // 月最新检测情况导出
    async monthNewImport() {
      try {
        this.btnNewLoading = true;
        this.$_openDownloadTip();
        const res = await this.$http.post(
          evaluationoverview.exportIndexOverviewReportData,
          JSON.parse(this.monthNewResultExport),
        );
        await this.$util.common.transformBlob(res.data.data);
      } catch (e) {
        console.log(e);
      } finally {
        this.btnNewLoading = false;
      }
    },
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  components: {
    pictureAttributeIntegrity: require('@/views/appraisaltask/assessmenTask/components/picture-attribute-integrity.vue')
      .default,
    AssessmentDetailText: require('./components/assessment-detail-text.vue').default,
    AssessmentChart: require('./components/assessment-chart.vue').default,
    MonthlyAssessmentResult: require('./components/monthly-assessment-result.vue').default,
    MonthlyNewDetection: require('./components/monthly-new-detection.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .rowTitle,
  .title-center .conter-center .time {
    color: #f5f5f5;
  }
  .table-box,
  .tables {
    @{_deep} .ui-table {
      border-top: 1px solid rgba(13, 71, 125, 0.6);
    }
    @{_deep} .ivu-table-body > table {
      border-right: 1px solid rgba(13, 71, 125, 0.6);
    }
    @{_deep} .ivu-table-header {
      border-right: 1px solid rgba(13, 71, 125, 0.6);
    }
    @{_deep} .header-table {
      border-left: 1px solid rgba(13, 71, 125, 0.6);
      border-bottom: 1px solid rgba(13, 71, 125, 0.6);
    }
  }

  .detection-table {
    .ui-table {
      border: 1px solid var(--border-color) !important;
      @{_deep} .ivu-table-body tr:nth-child(2n) td,
      @{_deep} .ivu-table-fixed tr:nth-child(2n) td {
        background: var(--bg-table-body-td-2n) !important;
      }
    }
    @{_deep}.ivu-table-fixed-header {
      th {
        background: #002f71 !important;
      }
    }

    @{_deep}.ivu-table-header {
      .standard-value-color {
        color: #cc8a27 !important;
      }
      .root-region-color {
        color: #19d5f6 !important;
      }
      &:first-child {
        tr {
          border-top: 1px solid var(--border-color) !important;
          border-left: 1px solid var(--border-color) !important;
          th {
            &:last-child {
              border-bottom: 1px solid var(--border-color) !important;
            }
          }
        }
      }
      tr {
        border-top: 1px solid var(--border-color) !important;
        border-left: 1px solid var(--border-color) !important;
        th {
          border-bottom: 1px solid var(--border-color) !important;
          border-left: 1px solid var(--border-color) !important;
        }
      }
    }

    @{_deep}.ivu-table-tbody {
      background-color: #08264d !important;

      tr {
        border-top: 1px solid var(--border-color) !important;
        border-right: 1px solid var(--border-color) !important;

        td {
          border-left: 1px solid var(--border-color) !important;
          border-bottom: 1px solid var(--border-color) !important;
        }
      }
    }
  }
}

[data-theme='light'],
[data-theme='deepBlue'] {
  .rowTitle,
  .title-center .conter-center .time {
    color: rgba(0, 0, 0, 0.75);
  }
  .table-box,
  .tables {
    @{_deep} .ui-table {
      border-top: 1px solid #d8d8d8;
    }
    @{_deep} .ivu-table-body > table {
      border-right: 1px solid #d8d8d8;
    }
    @{_deep} .ivu-table-header {
      border-right: 1px solid #d8d8d8;
    }
    @{_deep} .header-table {
      border-left: 1px solid #d8d8d8;
      border-bottom: 1px solid #d8d8d8;
    }
  }

  .detection-table {
    .ui-table {
      border: solid 1px #d8d8d8 !important;
      @{_deep} .ivu-table-body tr:nth-child(2n) td,
      @{_deep} .ivu-table-fixed tr:nth-child(2n) td {
        background: var(--bg-table-body-td-2n) !important;
      }
    }
    @{_deep}.ivu-table-fixed-header {
      th {
        background: #d8d8d8 !important;
      }
    }

    @{_deep}.ivu-table-header {
      .standard-value-color {
        color: var(--color-warning) !important;
      }
      .root-region-color {
        color: #48baff !important;
      }
      &:first-child {
        tr {
          border-top: solid 1px #d8d8d8 !important;
          border-left: solid 1px #d8d8d8 !important;
          th {
            &:last-child {
              border-bottom: 1px solid #d8d8d8 !important;
            }
          }
        }
      }
      tr {
        border-top: solid 1px #d8d8d8 !important;
        border-left: solid 1px #d8d8d8 !important;
        th {
          border-bottom: 1px solid #d8d8d8 !important;
          border-left: solid 1px #d8d8d8 !important;
        }
      }
    }

    @{_deep}.ivu-table-tbody {
      // background-color: #08264d !important;

      tr {
        border-top: solid 1px #d8d8d8 !important;
        border-right: solid 1px #d8d8d8 !important;

        td {
          border-left: solid 1px #d8d8d8 !important;
          border-bottom: 1px solid #d8d8d8 !important;
        }
      }
    }
  }
}

.page-assessmentresult {
  height: 100%;
  font-size: 14px;
  background: var(--bg-content);

  .month-echart {
    min-height: 200px;
  }

  /deep/ .head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  /deep/ .ivu-date-picker-cells-cell-focused {
    background: #2d8cf0;
    color: var(--color-content);
  }

  .right-content {
    float: right;
    width: 100%;
    // height: 100%;
    padding: 20px;
    background: var(--bg-content);
    @{_deep}.search-wrapper {
      overflow: hidden;
      padding: 0 20px !important;
      margin: 20px 0px;

      .el-date-editor {
        width: 212px;
      }

      .ivu-input,
      .el-input__inner {
        height: 34px;
        padding: 4px 32px 4px 8px;
        color: var(--color-input);
        font-size: 14px;
        line-height: 32px;
        border-radius: 4px;
        background: #02162b;
      }

      .el-input__inner:hover {
        border: 1px solid var(--color-primary);
      }

      .el-icon-date {
        display: none;
      }

      .ui-label {
        line-height: 34px;
      }

      .ivu-select {
        height: 34px;
      }

      .el-input__prefix {
        left: unset;
        right: 0;
        width: 32px;
      }

      .el-input__icon,
      .ivu-input-suffix i {
        color: var(--color-primary);
        font-size: 16px;
      }

      .ivu-input-suffix,
      .ivu-input-suffix i {
        line-height: normal;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .el-input__icon {
        line-height: 34px;
      }
    }

    .jump {
      padding: 0 20px !important;
    }

    .title-center {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 0;

      .conter-center {
        text-align: center;
        color: var(--color-content);

        & > span {
          display: block;
          margin-bottom: 20px;
          font-weight: bold;
        }
      }
    }

    .month-echart {
    }

    .table-box {
      padding: 0 0 20px 0;
      position: relative;

      .span-btn {
        cursor: pointer;
        color: var(--color-primary);
      }

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      @{_deep} .ivu-table-tbody {
        td {
          padding: 10px 0 10px 0;
        }
      }

      @{_deep} .ivu-table-body {
        td {
          padding: 10px 0 10px 0;
        }
      }

      /deep/ .header-table {
        box-shadow: none;
      }
    }
  }
}

.fs12 {
  font-size: 12px;
}

.fs24 {
  font-size: 24px;
}

.rowTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  border-bottom: 1px solid var(--border-modal-footer);
  margin-bottom: 10px;

  margin-top: 20px;

  i {
    color: var(--color-display-text);
    font-size: 24px;
    position: relative;
    &.icon-daochu {
      color: #fff;
    }
  }
}

.month-echart {
  // height: 300px !important;
}

.tables {
  height: 500px;
  background: var(--bg-table);
}

.ui-table {
  height: 100%;
}

.auto-fill {
  overflow: auto !important;
}

.down {
  color: #19d5f6;
}

.downloads {
  color: #a9bed9;
  line-height: 26px;

  .size {
    margin: 0 12px;
  }

  .load {
    color: var(--color-primary);
  }
}

.icon-shijian {
  font-size: 12px;
  color: #f5f5f5;
}

.tables {
  /deep/ .header-table {
    box-shadow: none;
  }

  @{_deep} .ivu-table-tbody {
    td {
      padding: 10px 0 10px 0;
    }
  }

  @{_deep} .ivu-table-body {
    td {
      padding: 10px 0 10px 0;
    }
  }

  @{_deep} .ui-table {
    th .ivu-table-cell {
      > div {
        display: flex;
      }
    }
  }

  @{_deep} .ivu-table-header {
    thead tr th {
      position: static;
    }
  }

  @{_deep} .header-table {
    box-shadow: none;
    .icon-xingzhuang,
    .icon-zhankai {
      line-height: 24px;
    }
  }

  @{_deep}.ivu-table-overflowX {
    & ~ .ivu-table-fixed {
      height: calc(~'100% - 11px');

      > .ivu-table-fixed-body {
        // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
        height: calc(~'100% - 150px - 11px') !important;
      }
    }
  }
}

@{_deep} .ivu-table-tip {
  overflow-x: auto;
}

@{_deep}.ivu-table-overflowX {
  & ~ .ivu-table-fixed-right,
  & ~ .ivu-table-fixed {
    height: 100%;

    > .ivu-table-fixed-body {
      // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
      height: calc(~'100% - 116px - 10px') !important;
    }
  }
}

@media screen and (max-width: 1620px) {
  @{_deep}.ivu-table-overflowX {
    & ~ .ivu-table-fixed-right,
    & ~ .ivu-table-fixed {
      height: 100%;

      > .ivu-table-fixed-body {
        // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
        height: calc(~'100% - 136px - 10px') !important;
      }
    }
  }
}

.a-export {
  float: right;
  padding: 6px 22px;
  border-radius: 3px;
  color: var(--color-content) !important;
  background: linear-gradient(180deg, #65a7ee 0%, #175cb2 100%);
  background: linear-gradient(180deg, #2b84e2 0%, #083a78 100%);
  border: none;
  padding-top: 8px;
}

.assessmentResult {
  color: var(--color-content);
  line-height: 26px;
  text-indent: 24px;

  .cityList {
    text-indent: 0;
    padding-left: 24px;
  }
}

@{_deep}.tooltip-title {
  color: var(--color-primary);
  font-size: 14px;
  font-weight: bold;
}

@{_deep}.ivu-tooltip-inner {
  max-width: 640px !important;
}

.can-click {
  cursor: pointer;
  text-decoration: underline !important;
}

.detection-table {
  @{_deep}.ivu-table table {
    border-collapse: unset !important;
  }

  @{_deep}.ivu-table-fixed-header {
    th {
      font-size: 13px;
      font-family: MicrosoftYaHei;
      line-height: 19px;
      color: #8ab9f8;
      opacity: 0.9;
    }
  }

  @{_deep}.ivu-table-header {
    width: auto;

    tr {
      th {
        &:first-child {
          border-left: none !important;
        }
      }
    }
  }

  @{_deep}.ivu-table-tbody {
    table {
      border-collapse: 0 !important;
      border-spacing: 0;
    }

    .ivu-table-column-left {
      .ivu-table-cell {
        padding: 0 15px !important;
      }
    }

    tr {
      td {
        &:first-child {
          border-left: none !important;

          @{_deep}.ivu-table-cell {
            padding: 0 5px !important;
          }
        }
      }
    }
  }

  @{_deep}.ivu-table-header {
    .none {
      display: none;
    }
  }
}
</style>
