@label-width: 100px;
@from-label-width: 380px;

.btn-text-default {
  cursor: pointer;
  font-size: 14px;
  color: var(--color-primary);
}
.color-white {
  color: #fff;
}
.width-160 {
  width: 160px;
}
.ml-100 {
  margin-left: @label-width;
}

.tip-red {
  font-size: 14px;
  color: #e44f22;
  line-height: 25px;
}

.tip-default {
  font-size: 14px;
  color: #fff;
  line-height: 25px;
}

//输入框宽度
.form-item-width {
  width: @from-label-width;
}

.params-suffix {
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  margin-left: 5px;
}

.params-pre {
  display: inline-block;
  float: left;
  width: 40px;
  height: 34px;
  line-height: 34px;
  font-size: 16px;
  color: var(--color-primary);
  text-align: center;
  border: 1px solid var(--border-input);
  opacity: 1;
  border-radius: 4px;
  margin-right: 10px;
}

.params-item {
  .params-label {
    font-size: 14px;
    line-height: 14px;
    color: var(--color-content);
    text-align: right;
    padding: 10px 12px 10px 0;
    width: @label-width;
  }
  .params-content {
    line-height: 32px;
    margin-left: @label-width;
    .params-desc {
      font-size: 14px;
      color: var(--color-content);
      line-height: 28px;
    }
    .params-tip {
      line-height: 28px;
      font-size: 14px;
      color: var(--color-tips);
      margin-bottom: 10px;
    }
  }
}

@{_deep} .ivu-modal-body {
  padding: 21px 50px 40px 50px;
}
