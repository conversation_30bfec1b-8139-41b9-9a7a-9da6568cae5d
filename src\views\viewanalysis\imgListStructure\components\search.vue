<template>
  <div class="search">
    <Form ref="form" :model="formData" class="form" inline>
      <FormItem label="设备名称:" prop="deviceName" class="search-input">
        <Input
          placeholder="请输入"
          clearable
          v-model="formData.deviceName"
          maxlength="50"
        />
      </FormItem>
      <FormItem label="设备资源:">
        <div class="select-tag-button" @click="selectDevice()">
          选择设备/已选（{{ formData.selectDeviceList.length }}）
        </div>
      </FormItem>
      <FormItem label="解析类型:" prop="dataType" class="search-input">
        <Select v-model="formData.dataType" placeholder="请选择" clearable>
          <Option
            v-for="item in structDataTypeList"
            :value="item.dataKey"
            :key="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="设备类型:" prop="deviceType" class="search-input">
        <Select v-model="formData.deviceType" placeholder="请选择" clearable>
          <Option :value="0">所有类型</Option>
          <Option :value="1">人像卡口</Option>
          <Option :value="2">车辆卡口</Option>
        </Select>
      </FormItem>

      <div class="flex-b">
        <div>
          <FormItem label="数据时间:" prop="dataTime" class="search-input">
            <DatePicker
              type="date"
              format="yyyy-MM-dd"
              v-model="formData.dataTime"
              placeholder="请选择"
            ></DatePicker>
          </FormItem>
        </div>
        <FormItem class="btn-group">
          <Button type="primary" @click="startSearch">查询</Button>
          <Button @click="resetHandle">重置</Button>
        </FormItem>
      </div>
    </Form>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { getConfigDate } from "@/util/modules/common";
export default {
  components: {},
  props: {
    subTaskType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      formData: {
        deviceName: "",
        deviceType: "",
        dataType: "",
        dataTypeList: "",
        selectDeviceList: [],
        deviceList: [],
        dataTime: "",
      },
      checkedLabels: [], // 已选择的标签
    };
  },
  computed: {
    ...mapGetters({
      structDataTypeList: "dictionary/getIvcpStatDataType", //解析类型
      structTaskStatusList: "dictionary/getStructTaskStatus", //实时视频解析任务状态
      structHistroytaskStatusList: "dictionary/getStructHistroytaskStatus", //历史视频解析任务状态
      filestrucureTaskStatusList: "dictionary/getFilestrucureTaskStatus", //文件解析任务状态
    }),
    statusList() {
      return this.subTaskType == "real"
        ? this.structTaskStatusList
        : this.subTaskType == "his"
        ? this.structHistroytaskStatusList
        : this.filestrucureTaskStatusList;
    },
  },
  created() {
    this.formData.dataTime = getConfigDate(-1)[0];
  },
  methods: {
    // 查询
    startSearch() {
      let { deviceName, dataType, dataTime, deviceType, selectDeviceList } = {
        ...this.formData,
      };
      let deviceList = [];
      if (selectDeviceList.length > 0) {
        selectDeviceList.forEach((item) => {
          deviceList.push(item.deviceGbId);
        });
      }
      this.$emit("searchForm", {
        deviceName: deviceName ? deviceName : "",
        deviceType: deviceType ? deviceType : "",
        dataTypeList: dataType ? [dataType] : [],
        deviceIdList: deviceList,
        dataTime: dataTime ? this.$dayjs(dataTime).format("YYYY-MM-DD") : "",
      });
    },
    /**
     * 选择设备
     */
    selectDevice() {
      this.$refs.selectDevice.show(this.formData.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.formData.selectDeviceList = list;
    },
    // 重置
    resetHandle() {
      // 清空选择设备
      this.$refs.selectDevice.removeAllHandle()
      this.$refs.form.resetFields();
      this.startSearch();
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  border-bottom: 1px solid #d3d7de;
  width: 100%;

  .form {
    width: 100%;

    .search-input {
      display: inline-flex;
      margin-right: 30px;

      /deep/ .ivu-date-picker {
        width: 200px;
      }

      /deep/ .ivu-input {
        width: 200px;
      }

      /deep/.ivu-select {
        width: 200px;
      }
    }

    /deep/.ivu-form-item {
      margin-bottom: 16px;
    }

    /deep/ .ivu-form-item-label {
      white-space: nowrap;
      width: 72px;
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
    }

    .label-text {
      /deep/.ivu-form-item-label {
        text-align-last: justify;
        text-align: justify;
        text-justify: distribute-all-lines !important; // 这行必加，兼容ie浏览器
        width: 72px;
        white-space: nowrap;
      }
    }

    .btn-group {
      margin-right: 0;
    }

    .datepicker-wrap {
      display: flex;
      align-items: center;

      .line {
        height: 3px;
        width: 20px;
        background: #d2d8db;
        margin: 0 5px;
      }

      .hl-btn {
        color: #2c86f8;
        margin-left: 10px;
        cursor: pointer;
      }

      margin-right: 10px;
    }
  }
}
</style>
