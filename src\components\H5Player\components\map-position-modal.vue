<template>
	<ui-modal v-model="modalShow" :mask="false" title="地图定位" footer-hide :r-width="1080" class="map-position" @onCancel="onCancel">
		<div class="map-container">
			<MapBase ref='map' class="map" v-if="positionPoints.length>0" cutIcon='track' :mapLayerConfig="{ showLatestLocation: true , } " :positionPoints="positionPoints" />
		</div>
	</ui-modal>
</template>
<script>
	import MapBase from '@/components/map/map-track.vue'
	import { queryCameraDeviceList } from '@/api/player'
	export default {
		components: {
			MapBase
		},
		props: {
			options: {
				type: Object,
				default: () => { }
			}
		},
		data() {
			return {
				modalShow: false,
				positionPoints: []
			}
		},
		methods: {
			async show(val) {
				this.positionPoints = []
				let pointObj = val
				if (!pointObj.geoPoint && !pointObj.longitude && !pointObj.latitude) {
					let result = await queryCameraDeviceList({deviceId: pointObj.deviceId})
        			pointObj = {...val, ...result.data[0]}
				}
				if (!pointObj.geoPoint) {
					pointObj.geoPoint = { lon: pointObj.longitude, lat:pointObj.latitude }
				}
				this.positionPoints.push(pointObj)
				this.modalShow = true;
			},
			onCancel() {
				this.positionPoints = []
				this.modalShow = false;
			}
		}
	}
</script>
<style lang="less" scoped>
.map-container {
	width: 100%;
	height: 60vh;
}
</style>
