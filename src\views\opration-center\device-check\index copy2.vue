<template>
	<div class="container">
		<div class="search-bar">
			<searchDevice ref="searchDevice" @searchInfo="searchInfo"></searchDevice>
		</div>
		<div class="card-container">
			<div class="big-car">
				<div class="bg1">
					<div class="title">设备总数</div>
					<div class="num">21,706,789</div>
					<img src="@/assets/img/opration-center/device5.png" alt="">
				</div>
			</div>
			<div class="big-car">
				<div class="bg2">
					<div class="title">数据通道数</div>
					<div class="num">63,589,001</div>
					<img src="@/assets/img/opration-center/device6.png" alt="">
				</div>
			</div>
			<div class="small-card-box">
				<div class="small-item" v-for="(item,index) in cardList" :key="index">
					<div class="title">{{item.title}}</div>
					<div class="num" :style="{color:item.blue_c}">{{item.num}}</div>
					<img :src="require('@/assets/img/opration-center/'+item.imageUrl)" alt="">
				</div>
			</div>
		</div>
		<div class="table-container">
			<div class="table-content">
				<ui-table :columns="columns" :data="tableList">
					<template #hh="{ row }">
						<Button :type=" row.hh=='1'?'success':row.hh=='2'?'error':'warning'" size="small">{{ row.hh=='1'?'正常':row.hh=='2'?'异常':'未知' }}</Button>
					</template>
				</ui-table>
			</div>
			<ui-empty v-if="tableList.length === 0 && loading == false"></ui-empty>
			<ui-loading v-if="loading"></ui-loading>
			<!-- 分页 -->
			<ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[10, 20, 40]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
		</div>
	</div>
</template>
 <script>
	import searchDevice from './components/search-device'
	import { wifiRecordSearchEx } from '@/api/wisdom-cloud-search'
	export default {
		name: 'deviceCheck',
		components: {
			searchDevice
		},
		data() {
			return {
				queryParam: {
				},
				tableList: [
					{ aa: '蚌埠市雪亮工程/临时调整点位', bb: 'Y_CQ3089禹王宫门口(人脸识别)(结构化)', cc: '************', dd: '8000', ee: '4,946', ff: '人脸', gg: '无数据', hh: '1' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					{ aa: '蚌埠市雪亮工程/临时调整点位/未分配', bb: 'ZX_09_17沱西入村口卡口', cc: '***********', dd: '37777', ee: '4,946', ff: '非机动车', gg: '正常', hh: '2' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '***********4', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/三期主副驾/禹会分局', bb: 'G206荆涂大桥南冷水村入口西侧卡口北向南1_2辅道(主副驾)', cc: '***********', dd: '37777', ee: '2,231', ff: '车辆', gg: '减少百分之20-50', hh: '3' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/怀远县（主副驾）', bb: 'HY中央花园东门停车场', cc: '34.54.116.1', dd: '8000', ee: '3,710', ff: '车辆', gg: '增长百分之20-50', hh: '1' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '***********4', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
					{ aa: '蚌埠市雪亮工程/主副驾设备/四期主副驾/蚌山分局', bb: 'HY卞和小区东门停车场', cc: '***********4', dd: '8000', ee: '3,710', ff: '人体', gg: '增长百分之20-50', hh: '1' },
				],
				pageInfo: {
					pageNumber: 1,
					pageSize: 10,
				},
				total: 9,
				loading: false,
				columns: [
					{ title: '编号', align: 'center', width: 90, type: 'index', key: 'index' },
					{ title: '分组路径', key: 'aa', },
					{ title: '设备名称', key: 'bb', },
					{ title: 'IP', key: 'cc', },
					{ title: '端口', key: 'dd', },
					{ title: '接入数据量', key: 'ee', },
					{ title: '数据类型', key: 'ff', },
					{ title: '统计状态', key: 'gg', },
					{ title: '端口连接状态', slot: 'hh' }

				],
				cardList: [
					{ title: '无数据通道', num: '531', imageUrl: 'device7.png' },
					{ title: '增长20%-50%', num: '3,125', imageUrl: 'device8.png' },
					{ title: '增长50%-80%', num: '1,234', imageUrl: 'device9.png' },
					{ title: '增长80%以上', num: '2,121', imageUrl: 'device10.png' },
					{ title: '数据浮动低于20%', num: '14,660', imageUrl: 'device1.png', blue_c: '#48baff' },
					{ title: '减少20%-50%', num: '3,325', imageUrl: 'device2.png', blue_c: '#48baff' },
					{ title: '减少50%-80%', num: '4,125', imageUrl: 'device3.png', blue_c: '#48baff' },
					{ title: '减少80%以上', num: '234', imageUrl: 'device4.png', blue_c: '#48baff' },
				]
			}
		},
		mounted() {
			// this.queryList()
		},
		methods: {
			queryList() {
				let formData = this.$refs.searchDevice.formData
				this.queryParam = { ...this.queryParam, ...formData }
				this.loading = true;
				this.tableList = []
				wifiRecordSearchEx({ ...this.queryParam, ...this.pageInfo })
					.then(res => {
						const { total, entities } = res.data
						this.total = total
						this.tableList = entities
					})
					.catch(err => {
						console.error(err)
					})
					.finally(() => {
						this.loading = false;
					})
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				this.queryParam = obj
				this.queryList()
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.queryList()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.queryList()
			},
		}
	}
</script>
<style lang="less" scoped>
	.container {
		padding: 0;
		width: 100%;
		.card-container {
			height: 230px;
			padding: 0 20px 20px 20px;
			display: flex;
			.big-car {
				width: 20%;
				height: 100%;
				padding-right: 20px;
				.bg1 {
					height: 100%;
					padding: 20px;
					position: relative;
					background: linear-gradient(315deg, #1c6df4 4%, #39a1fc 100%);
					box-shadow: 0 5px 10px 0 rgba(54, 151, 251, 0.5);
					border-radius: 4px;
				}
				.bg2 {
					height: 100%;
					padding: 20px;
					position: relative;
					background: linear-gradient(141deg, #37d9af 0%, #1ca884 100%);
					box-shadow: 0 5px 10px 0 rgba(34, 180, 143, 0.5);
					border-radius: 4px;
				}
				.title {
					font-size: 16px;
					color: #fff;
				}
				.num {
					font-size: 32px;
					color: #fff;
					font-weight: bold;
				}
				img {
					width: 200px;
					height: 130px;
					position: absolute;
					right: 0;
					bottom: 0;
				}
			}
			.small-card-box {
				flex: 1;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				.small-item {
					width: 23.6%;
					height: 44.7%;
					padding: 20px;
					position: relative;
					background: linear-gradient(270deg, #fff 0%, #eef7ff 100%);
					box-shadow: 0 3px 5px 0 rgba(147, 171, 206, 0.7);
					border-radius: 4px;
					margin-bottom: 20px;
					.title {
						font-size: 16px;
					}
					.num {
						font-size: 28px;
						color: #2c86f8;
						font-weight: bold;
					}
					img {
						width: 70px;
						height: 70px;
						position: absolute;
						right: 12px;
						bottom: 12px;
					}
				}
			}
		}
	}
	.table-container {
		padding: 0 20px 0;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: auto;
		position: relative;
		height: calc(~"100% - 320px");
		.table-content {
			overflow: auto;
			flex: 1;
			display: flex;
			flex-wrap: wrap;
			justify-content: start;
			align-content: flex-start;
			.ui-table {
				height: 100%;
			}
		}
	}
</style>