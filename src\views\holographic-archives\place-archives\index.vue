<template>
  <div class="container">
    <div class="device">
      <!-- 查询 -->
      <Search
        v-show="!$route.query.noSearch"
        ref="searchRef"
        @search="search"
      />
      <div class="card-content">
        <div v-for="(item, index) in list" :key="index" class="card-item">
          <UiListCard
            type="place"
            :data="item"
            :index="index"
            @archivesDetailHandle="archivesDetailHandle(item.id)"
            @collection="getList"
          />
        </div>
        <ui-empty v-if="list.length === 0"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
      <!-- 分页 -->
      <ui-page
        :current="pages.pageNumber"
        :total="total"
        :page-size="pages.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
  </div>
</template>
<script>
import Search from "./components/search.vue";
import UiListCard from "@/components/ui-list-card";
import { getPlaceArchivesListAPI } from "@/api/placeArchive";
export default {
  name: "PlaceArchives",
  components: { Search, UiListCard },
  data() {
    return {
      list: [], // 列表
      loading: false,
      total: 0,
      pages: {
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },

  mounted() {
    // 首次加载页面，需要获取子组件search的参数，所以在mounted中请求
    this.getList();
  },
  methods: {
    /**
     * @description: 查询列表
     * @param {boolean} keyWords 标识，标识用户主动点击查询按钮触发的该方法，此时不携带keyWords参数
     */
    getList(keyWords = true) {
      this.loading = true;
      let param = {
        ...this.$refs.searchRef.getQueryParams(),
        ...this.pages,
      };
      // 全景智搜跳转过来
      if (keyWords) {
        param = {
          ...param,
          searchValue: this.$route.query.keyWords || undefined,
        };
      }
      getPlaceArchivesListAPI(param)
        .then((res) => {
          const { entities, total } = res.data;
          this.list = entities;
          this.total = total;
          // 重新获取页码后回到顶部
          document.querySelector(".card-content").scrollTop = 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    /**
     * @description: 跳转到档案详情
     * @param {string} 场所档案id
     */
    archivesDetailHandle(archiveNo) {
      const { href } = this.$router.resolve({
        name: "place-dashboard",
        query: { archiveNo, source: "place" },
      });
      window.open(href, "_blank");
    },

    /**
     * @description: 查询
     */
    search() {
      this.pages.pageNumber = 1;
      this.getList(false);
    },

    /**
     * @description: 改变页码
     * @param {number} pageNumber 页码
     */
    pageChange(pageNumber) {
      this.pages.pageNumber = pageNumber;
      this.getList();
    },

    /**
     * @description: 改变每页数量
     * @param {number} size 每页数量
     */
    pageSizeChange(size) {
      this.pages.pageSize = size;
      this.pages.pageNumber = 1;
      this.getList();
    },
  },
};
</script>
<style lang="less" scoped>
.device {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  .card-content {
    display: flex;
    flex-wrap: wrap;
    overflow: auto;
    flex: 1;
    margin: 0 -5px;
    align-content: flex-start;
    position: relative;
    .card-item {
      width: 20%;
      padding: 0 5px;
      box-sizing: border-box;
      margin-bottom: 10px;
      transform-style: preserve-3d;
      transition: transform 0.6s;
      .list-card {
        width: 100%;
        backface-visibility: hidden;
        height: 198px;
        /deep/.list-card-content-body {
          height: 115px;
        }
        /deep/.content-img {
          width: 115px;
          height: 115px;
        }
        /deep/.tag-wrap {
          margin-top: 7px;
          .ui-tag {
            margin: 0 5px 0 0 !important;
          }
        }
      }
    }
  }
}
</style>
