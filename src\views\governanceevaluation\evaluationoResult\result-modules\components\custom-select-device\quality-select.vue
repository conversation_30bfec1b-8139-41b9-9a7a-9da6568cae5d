<template>
  <basic-select v-bind="getAttrs" v-on="$listeners">
    <!-- 表格插槽 -->
    <template slot="qualified" slot-scope="{ row }">
      <Tag :color="qualifiedColorConfig[row.qualified].color">
        {{ qualifiedColorConfig[row.qualified].dataValue }}
      </Tag>
    </template>
    <template #deviceId="{ row }">
      <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
        row.deviceId
      }}</span>
    </template>
    <template #phyStatus="{ row }">
      <span>
        {{ !row.phyStatus ? '--' : row.phyStatusText }}
      </span>
    </template>
    <template #videoStartTime="{ row }">
      <span>
        {{ !row.videoStartTime ? '--' : row.videoStartTime }}
      </span>
    </template>
    <template #reason="{ row }">
      <Tooltip :content="row.reason" transfer max-width="150">
        {{ row.reason }}
      </Tooltip>
    </template>
    <template #tagNames="{ row }">
      <tags-more :tag-list="row.tagList || []"></tags-more>
    </template>
  </basic-select>
</template>
<script>
import errorCodesMixins from './errorCodesMixins';
export default {
  props: {
    qualifiedColorConfig: {},
  },
  mixins: [errorCodesMixins],
  data() {
    return {
      formData: {
        deviceId: '',
        deviceName: '',
        outcome: '',
        errorCodes: null,
        online: '',
        normal: '',
        canPlay: '',
        tagIds: [],
      },
    };
  },
  created() {},
  methods: {},
  watch: {},
  computed: {
    getAttrs() {
      return {
        searchData: this.formData,
        ...this.$attrs,
        formItemData: this.formItemData, // errorCodesMixins.js
        moduleData: this.moduleData, // errorCodesMixins.js
      };
    },
  },
  components: {
    BasicSelect: require('./basic-select.vue').default,
    TagsMore: require('@/components/tags-more.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
