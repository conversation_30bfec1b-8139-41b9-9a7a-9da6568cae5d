<!--
    * @FileDescription: 由人案到人-搜索条件
 -->
<template>
  <div class="leftBox case_leftBox">
    <people-select
      ref="peopleSelect"
      :nextTep="step === 1"
      @onChangeStep="onChangeStep"
    />
    <div v-if="step > 1" class="search_box">
      <div class="title">
        <p class="title_num">2</p>
        <p class="title_text">选择警情、案件</p>
        <div class="switch_point" v-show="selectTable.length > 0">
          <i-switch :value="showPolice" @on-change="handleChangeSwitch" />
          <p>点位</p>
        </div>
      </div>
      <div class="search_condition">
        <template v-if="selectTable.length == 0">
          <div class="add_case" @click="handleCase">
            <Icon type="md-add-circle" />
            <p class="add_case_text">添加案情、案件</p>
          </div>
          <div class="btn-group">
            <Button class="back-btn" type="default" @click="changeStep(-1)"
              >返回</Button
            >
          </div>
        </template>
        <div class="list_box" v-else :class="{ 'search_box-pack': packUpDown }">
          <div class="type_title">
            <i class="iconfont icon-jingqing range"></i>
            <p class="type_name">
              警情 ( <span>{{ partSelectData.length }}</span> )
            </p>
          </div>
          <ul class="search_ul">
            <list
              v-for="(item, index) in partSelectData"
              :key="index"
              :rowObj="item"
              :showDel="resultStatus !== 3"
              @deleList="handleDele(index, 0)"
            >
            </list>
          </ul>
          <div class="type_title">
            <i class="iconfont icon-anjian range"></i>
            <p class="type_name">
              案件 ( <span>{{ caseSelectData.length }}</span> )
            </p>
          </div>
          <ul class="search_ul">
            <list
              v-for="(item, index) in caseSelectData"
              :key="index"
              :rowObj="item"
              :showDel="resultStatus !== 3"
              @deleList="handleDele(index, 1)"
            >
            </list>
          </ul>
        </div>
        <div class="footer-box" v-show="selectTable.length > 0">
          <div
            class="select-tag-button"
            v-show="resultStatus !== 3"
            @click="selectDevice()"
          >
            编辑警情、案件（已选{{ selectTable.length }}）
          </div>
          <div class="btn-group" v-show="step === 2">
            <Button
              type="primary"
              class="result-btn"
              v-show="selectTable.length > 0"
              @click="changeStep(1)"
              >下一步</Button
            >
            <Button class="back-btn" type="default" @click="changeStep(-1)"
              >返回</Button
            >
          </div>
          <div
            class="footer"
            v-show="selectTable.length > 0"
            :class="{ packArrow: packUpDown }"
            @click="handlePackup"
          >
            <img :src="packUrl" alt="" />
            <p>{{ packUpDown ? "展开" : "收起" }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="crash-analyse" v-if="step > 2">
      <div class="title">
        <p class="title_num">3</p>
        <p class="title_text">碰撞分析</p>
      </div>
      <div class="crash-form">
        <div v-if="resultStatus === 1">
          <div class="crash-box">
            <p class="crash-box-title">案发前:</p>
            <Select
              v-model="formData.happenedTimeBeforeHour"
              clearable
              class="wrapper-select"
            >
              <Option
                v-for="item in caseTimeTypeList"
                :value="item.dataKey"
                :key="item.dataKey"
                placeholder="请选择"
              >
                {{ item.dataValue }}</Option
              >
            </Select>
            <p>内</p>
            <div class="split"></div>
            <p class="crash-box-title">案发后:</p>
            <Select
              v-model="formData.happenedTimeAfterHour"
              :max-tag-count="4"
              clearable
              class="wrapper-select"
            >
              <Option
                v-for="item in caseTimeTypeList"
                :value="item.dataKey"
                :key="item.dataKey"
                placeholder="请选择"
              >
                {{ item.dataValue }}</Option
              >
            </Select>
            <p>内</p>
          </div>
          <div class="crash-scope">
            <p class="crash-box-title">碰撞范围:</p>
            <div class="slider-content">
              <Slider v-model="formData.distance" :max="1000"></Slider>
              <span>{{ formData.distance }}M</span>
            </div>
          </div>
        </div>
        <results-rogress v-if="resultStatus === 2" :percent="percent" />
        <div class="crash-from-detail" v-if="resultStatus === 3">
          <div
            class="crash-from-detail-item"
            v-for="item in crashList"
            :key="item.key"
          >
            <div class="item-label">{{ item.label }}</div>
            <div class="item-value">
              {{ formData[item.key] }}{{ item.suffix }}
            </div>
          </div>
        </div>
        <div class="btn-group">
          <Button
            v-if="resultStatus === 1"
            type="primary"
            class="result-btn"
            @click="handleAnalysis"
            >碰撞分析</Button
          >
          <Button
            v-if="resultStatus !== 2"
            class="back-btn"
            type="default"
            @click="resetResult"
            >返回</Button
          >
        </div>
      </div>
    </div>
    <!-- 选择设备 -->
    <select-case ref="selectCase" showOrganization @selectData="selectData" />
  </div>
</template>

<script>
import { mapActions, createNamespacedHelpers } from "vuex";
import { cloneDeep } from "lodash";
import peopleSelect from "./people-select";
import selectCase from "@/components/select-modal/select-case.vue";
import list from "@/components/model-market/list.vue";
import resultsRogress from "./results-rogress";
import {
  addPersonToCaseAnalysis,
  getPersonToCaseAnalysisProgress,
  getPersonToCaseAnalysisResult,
  getPersonToCaseMatchCenterPoints,
} from "@/api/modelMarket";

const {
  mapMutations: mapPersonToCaseMutations,
  mapActions: mapPersonToCaseActions,
  mapState: mapPersonToCaseState,
} = createNamespacedHelpers("personToCase");

const defaultFormData = {
  distance: 1000,
  happenedTimeBeforeHour: "",
  happenedTimeAfterHour: "",
};
export default {
  name: "",
  components: {
    selectCase,
    list,
    peopleSelect,
    resultsRogress,
  },
  data() {
    return {
      percent: 0,
      reqSeq: "",
      step: 1,
      resultStatus: 1,
      packUpDown: false,
      caseSelectData: [],
      partSelectData: [],
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      showPersonCond: false,
      formData: {
        ...defaultFormData,
      },
      pointSwitch: true,
      ruleValidate: {},
      crashList: [
        {
          label: "案发前",
          key: "happenedTimeBeforeHour",
          suffix: "小时内",
        },
        {
          label: "案发后",
          key: "happenedTimeAfterHour",
          suffix: "小时内",
        },
        {
          label: "碰撞范围",
          key: "distance",
          suffix: "M",
        },
      ],
    };
  },
  watch: {
    selectTable(val) {
      this.$emit("dealPoliceAndCaseTrackData", cloneDeep(val), true);
      val.length === 0 && this.step === 3 && this.changeStep(-1);
    },
  },
  computed: {
    ...mapPersonToCaseState({
      showPolice: (state) => state.showPolice,
    }),
    selectTable() {
      const list = this.dealPoliceAndCaseTrackData();
      return list;
    },
  },
  async created() {
    await this.getDictData();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    ...mapPersonToCaseActions(["getPeopleTrackList"]),
    ...mapPersonToCaseMutations(["setState"]),
    onChangeStep(index = 0, reqSeq) {
      this.reqSeq = reqSeq;
      this.changeStep(index);
    },
    // 选拔案件警情
    handleCase() {
      this.$refs.selectCase.show(this.partSelectData, this.caseSelectData);
    },
    selectDevice() {
      this.handleCase();
    },
    // 步骤选择
    changeStep(index) {
      this.step += index;
      index === 0 && (this.step = 1);
      if (index > 0) return;
      [2, 3]
        .filter((item) => this.step < item)
        .forEach((item) => {
          this[`clearStep${item}`]();
        });
    },
    resetResult() {
      if (this.resultStatus === 1) this.changeStep(-1);
      else this.resetResultData();
    },
    selectData(plist, clist) {
      this.partSelectData = plist || [];
      this.caseSelectData = clist || [];
      this.step === 2 && this.changeStep(1);
    },
    getReqSeq() {
      return this.reqSeq;
    },
    dealPoliceAndCaseTrackData() {
      let plist = this.partSelectData.map((item) => {
        return {
          bh: item.jjbh,
          caseTime: item.bjdhsj,
          geoPoint: item.geoPoint,
          policeDataType: 1,
          detail: { ...item },
        };
      });
      let clist = this.caseSelectData.map((item) => {
        return {
          bh: item.ajbh,
          caseTime: item.fxsj,
          geoPoint: item.geoPoint,
          policeDataType: 2,
          detail: { ...item },
        };
      });
      return [...plist, ...clist];
    },
    handleAnalysis() {
      if (
        this.formData.happenedTimeAfterHour == "" ||
        this.formData.happenedTimeBeforeHour == ""
      ) {
        this.$Message.warning("案发前后时间为必填项！");
        return;
      }
      if (
        this.caseSelectData.length === 0 &&
        this.partSelectData.length === 0
      ) {
        this.$Message.warning("请选择警情，案情！");
        return;
      }

      this.queryParams = {
        reqSeq: this.reqSeq,
        caseNos: this.caseSelectData.map((el) => el.ajbh),
        incidentNos: this.partSelectData.map((el) => el.jjbh),
        ...this.formData,
      };
      this.percent = 0;
      this.resultStatus = 2;
      addPersonToCaseAnalysis(this.queryParams)
        .then((res) => {
          if (res.code === 200) {
            this.progressBar();
          } else {
            this.resultStatus = 1;
          }
        })
        .catch((err) => {
          this.resultStatus = 1;
        });
    },
    // 获取进度
    progressBar() {
      getPersonToCaseAnalysisProgress(this.reqSeq)
        .then((res) => {
          if (res.code !== 200) {
            this.resultStatus = 1;
            return;
          }
          this.percent = res.data;
          if (this.percent === 100) {
            this.resultStatus = 3;
            this.getAnalysisResult();
            this.getPMatchCenterPoints();
            return;
          }
          setTimeout(() => {
            this.progressBar();
          }, 500);
        })
        .catch((error) => {
          this.resultStatus = 1;
        });
    },
    // 获取碰撞结果
    async getAnalysisResult() {
      const res = await getPersonToCaseAnalysisResult(this.reqSeq);
      if (res.code === 200) {
        if (res.data?.length) {
          const geoPointMap = {};
          for (const el of this.partSelectData) {
            geoPointMap[`1-${el.jjbh}`] = el;
          }
          for (const el of this.caseSelectData) {
            geoPointMap[`2-${el.ajbh}`] = el;
          }
          const data = res.data.map((item) => {
            const detail =
              geoPointMap[`${item.policeDataType}-${item.policeDataNo}`];
            return { ...item, geoPoint: detail.geoPoint, detail };
          });
          this.resultStatus = 3;
          const peopleList = this.$refs.peopleSelect.getPeopleList();
          const analysisResultList = peopleList
            .map((item) => {
              return {
                ...item,
                policeAndCase: data.filter(
                  (el) => el.idCard === item.archiveNo
                ),
              };
            })
            .filter((item) => item.policeAndCase.length > 0);
          this.setState({
            analysisResultList,
            // policeAndCaseTrackList: data,
          });
          // 打开右侧结果页面
          this.$emit("openRightBox");
        } else {
          this.$Message.warning("无碰撞结果");
          this.setState({ analysisResultList: [] });
        }
      }
    },
    // 获取碰撞区域
    async getPMatchCenterPoints() {
      const res = await getPersonToCaseMatchCenterPoints(this.reqSeq);
      if (res.code === 200) {
        const data = res.data;
        this.setState({
          analysisAreaList: data || [],
        });
      }
    },
    clearStep1() {
      this.getPeopleTrackList([]);
    },
    clearStep2() {
      this.caseSelectData = [];
      this.partSelectData = [];
      this.$refs.peopleSelect.onFlexible(false);
      this.clearStep1();
    },
    clearStep3() {
      this.formData = { ...defaultFormData };
      if (this.resultStatus !== 1) this.resetResultData();
    },
    resetResultData() {
      this.resultStatus = 1;
      this.$emit("onResultData");
    },
    // 删除警情，案情
    handleDele(index, type) {
      if (type == 0) {
        this.partSelectData.splice(index, 1);
      } else {
        this.caseSelectData.splice(index, 1);
      }
      // if (this.partSelectData.length == 0 && this.caseSelectData.length == 0) {
      //     this.pointSwitch = false
      // }
    },
    // 展开，收起
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    handleChangeSwitch(val) {
      this.setState({
        showPolice: val,
      });
    },
  },
};
</script>

<style lang='less' scoped>
@import "../../../components/style/index";

.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  display: flex;
  flex-direction: column;
  .search_box {
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    max-height: calc(~"100% - 250px");
    overflow: hidden;
    margin-top: 10px;
    .switch_point {
      font-weight: 400;
      font-size: 14px;
      color: #3d3d3d;
      display: flex;
      align-items: center;
      margin-left: 100px;

      p {
        margin-left: 10px;
      }
    }

    .search_condition {
      padding: 15px 15px;
      box-sizing: border-box;
      overflow: hidden;

      .add_case {
        width: 340px;
        height: 130px;
        background: url("~@/assets/img/model/searchIcon/casebg.png") no-repeat;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        /deep/ .ivu-icon-md-add-circle {
          font-size: 27px;
          color: #2c86f8;
        }

        .add_case_text {
          font-weight: 400;
          font-size: 14px;
          color: #2c86f8;
          margin-top: 12px;
        }
      }

      .list_box {
        max-height: calc(~"100vh - 550px");
        overflow-y: auto;

        .type_title {
          display: flex;
          align-items: center;

          .icon-jingqing {
            color: #ea4a36;
          }

          .icon-anjian {
            color: #4696fc;
          }

          .type_name {
            font-weight: 700;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);
            margin-left: 6px;

            span {
              color: #2c86f8;
            }
          }
        }
      }
    }

    .search_condition-pack {
      height: 0px;
      transition: height 0.2s ease-out;
      overflow: hidden;
      padding: 0;
    }

    .footer-box {
      .select-tag-button {
        width: 90%;
        margin: 0 auto;
      }
    }
  }

  .object-information {
    margin-top: 10px;
    width: 370px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    .search_form {
      padding: 5px;

      .wrapper-select {
        width: 200px;
      }

      .tag {
        display: flex;
        justify-content: space-between;
        width: 80%;
      }

      .details-form {
        .wrapper-box {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 15px;

          .wrapper-title {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.4535);
          }

          .wrapper-text {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.9);

            .tag-box {
              display: flex;

              .tag-box-li {
                font-weight: 400;
                font-size: 12px;
                color: #1faf8a;
                padding: 2px 6px;
                border: 1px solid;
                margin: 0 2px;
              }
            }
          }

          .wrapper-time {
            width: 100%;
          }
        }
      }
    }
  }

  .crash-analyse {
    margin-top: 10px;
    width: 370px;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    .crash-form {
      padding: 15px;

      .crash-from-detail {
        overflow: hidden;

        .crash-from-detail-item {
          float: left;
          width: 50%;
          height: 30px;
          display: flex;
          align-items: center;
          column-gap: 5px;
          font-weight: 400;
          font-size: 14px;

          .item-label {
            color: rgba(0, 0, 0, 0.45);
          }

          .item-value {
            font-weight: 600;
            color: rgba(0, 0, 0, 0.9);
          }
        }
      }

      .crash-box {
        display: flex;
        align-items: center;
        column-gap: 10px;

        .split {
          flex: 1;
        }
      }

      .crash-scope {
        margin-top: 10px;
        margin-bottom: 10px;
        display: flex;

        .slider-content {
          flex: 1;

          /deep/ .ivu-slider {
            width: 100%;
          }
        }
      }

      .crash-box-title {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.45);
        white-space: nowrap;
      }
    }
  }

  .search_box-pack {
    transition: height 0.2s ease-out;
    height: 0px;
    overflow: hidden;
  }

  .btn-group {
    margin-top: 10px;
    display: flex;

    .result-btn {
      flex: 4;
    }

    .back-btn {
      flex: 1;
    }
  }

  .wrapper-input {
    width: 80px;
  }
}

.footer {
  padding: 0;
}
/deep/ .ivu-form-item {
  margin-bottom: 10px;
}

.age_bracket {
  .age_type_select {
    display: flex;

    .age_type_li {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.6);
      padding: 2px 5px;

      background: #ffffff;
      border-radius: 2px 2px 2px 2px;
      border: 1px solid #d3d7de;
    }

    .age_all {
      background: #2c86f8;
      color: #ffffff;
    }
  }

  .age_num {
    .age_num_input {
      width: 100px;
    }

    .age_num_line {
      margin: 0 5px;
    }
  }
}
</style>
