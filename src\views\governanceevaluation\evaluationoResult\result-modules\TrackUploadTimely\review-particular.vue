<!-- 实时轨迹上传及时性 -->
<template>
  <div class="review-particular auto-fill">
    <div class="icon-statics-wrapper">
      <icon-statics :icon-list="iconList"></icon-statics>
      <div class="icon-statics-wrapper-right">
        <tag-view :list="tabList" @tagChange="changeMode" ref="tagView" class="tag-view mt-xs"></tag-view>
      </div>
    </div>
    <div class="auto-fill">
      <component
        :is="componentName"
        :form-item-data="formItemData"
        :form-data="formData"
        :table-columns="tableColumns"
        :result-data="resultData"
        :check-picture-params="checkPictureParams"
        :loading="loading"
        :need-separate-total="true"
        :data-total-count="resultDataTotalCount"
        @startSearch="startSearch"
      >
        <template #otherButton v-if="activeMode === 'list'">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i> 导出
          </Button>
        </template>
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image :src="row.identityPhoto" />
          </div>
        </template>
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images pointer">
            <ui-image :src="row.trackImage" />
          </div>
        </template>
        <template slot="outcome" slot-scope="{ row }">
          <Tag v-if="row.qualified in qualifiedColorConfig" :color="qualifiedColorConfig[row.qualified].color">
            {{ qualifiedColorConfig[row.qualified].dataValue }}
          </Tag>
          <span class="ml-sm" v-if="row.dataMode === '3'"> (人工) </span>
        </template>
        <template #reason="{ row }">
          <Tooltip :content="row.reason" transfer max-width="150">
            {{ row.reason }}
          </Tooltip>
        </template>
      </component>
    </div>
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <!-- 导出 -->
    <export-data ref="exportModule" :export-loading="exportLoading" @handleExport="handleExport"> </export-data>
  </div>
</template>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
import { mapGetters } from 'vuex';
// 外层公共配置
import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
// 本层配置文件
import { tableColumns, iconStaticsList, listFormData, cardFormData } from './util/enum/ReviewParticular.js';
import evaluationoverview from '@/config/api/evaluationoverview';
import detectionResult from '@/config/api/detectionResult';

export default {
  name: 'reviewParticular',
  mixins: [particularMixin, dealWatch],
  data() {
    return {
      iconList: iconStaticsList,
      activeMode: 'list',
      tabList: ['图片模式', '聚档模式'],
      formData: {
        beginTime: '',
        endTime: '',
        qualified: '',
        causeErrors: [],
        deviceIds: [],
      },
      formItemData: listFormData,
      componentName: 'ListPattern',
      tableLoading: false,
      tableColumns: Object.freeze(tableColumns),
      pageData: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
      resultData: {},
      resultDataTotalCount: 0,
      cardList: [],
      statisticShow: false,
      checkPictureParams: {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        orgRegionCode:
          this.$route.query.statisticType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode,
      },
      bigPictureShow: false,
      loading: false,
      imgList: [],
      exportLoading: false,
    };
  },
  created() {
    this.getQualificationList();
    this.startWatch(
      '$route.query',
      () => {
        this.initAll();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    initAll() {
      // 获取统计
      this.MixinGetStatInfo().then((data) => {
        // 设备模式统计
        iconStaticsList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, data.qualified);
          }
          item.count = data[item.fileName] || 0;
        });
      });
      // 获取列表
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
    },
    // 获取不合格原因下拉列表  1 - 合格、2 - 不合格 3 - 无法检测
    async getQualificationList() {
      let params = {
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
        access: 'TASK_RESULT',
        displayType: this.$route.query.statisticType,
        customParameters: this.formData,
      };
      params.orgRegionCode = params.displayType === 'REGION' ? this.$route.query.regionCode : this.$route.query.orgCode;
      try {
        let res = await this.$http.post(detectionResult.getVideoUnqualifiedInfo, params);
        let options = res.data.data || [];
        let findErrorCodes = this.formItemData.find((item) => item.key === 'errorCodes');
        if (findErrorCodes) {
          findErrorCodes.options = options.map((item) => {
            return { value: item.code, label: item.reason };
          });
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取列表[mixin的方法]
    selfConfigGetList() {
      this.activeMode === 'list' ? this.getTableData() : this.getImageList();
    },
    // 获取列表[mixin的方法]
    selfConfigGetListTotal() {
      this.activeMode === 'list' ? this.getTableDataTotal() : '';
    },
    showModal(row) {
      this.osdDetailVisible = true;
      this.osdDetailData = { ...row };
    },
    handleOsdModalHide() {
      this.$refs.osdModalRef.hide();
    },
    handlePage(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    startSearch(params) {
      this.pageData = params.pageData;
      Object.assign(this.formData, params.searchData);
      this.selfConfigGetList();
      if (params.isSearchTotal) {
        this.selfConfigGetListTotal();
      }
    },
    handlePageSize(val) {
      Object.assign(this.pageData, val);
      this.selfConfigGetList();
    },
    async getTableData() {
      try {
        this.loading = true;
        const data = await this.MixinGetTableData();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    getTableDataTotal() {
      // 通过接口单独获取总数
      this.MixinGetTableDataTotal().then((data) => {
        this.resultDataTotalCount = data;
      });
    },
    // 判断更新统计是否显示
    async showRecountBtn() {
      try {
        let res = await this.$http.get(evaluationoverview.showRecountBtn, {
          params: { batchId: this.$route.query.batchId },
        });
        this.statisticShow = res.data.data || false;
      } catch (err) {
        console.log(err);
      }
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 切换模式
    changeMode(type) {
      this.resultData = [];
      this.activeMode = !type ? 'list' : 'cad';
      this.formItemData = this.activeMode === 'list' ? listFormData : cardFormData;
      this.componentName = this.activeMode === 'list' ? 'ListPattern' : 'CardPattern';
      if (type === 'list') {
        this.formData = {
          beginTime: '',
          endTime: '',
          qualified: '',
          causeErrors: [],
          deviceIds: [],
        };
        this.getQualificationList();
      } else {
        // 图片模式获取不合格原因传2
        this.formData = {
          name: '',
          idCard: '',
        };
        this.getQualificationList(2);
      }
      this.selfConfigGetList();
      this.selfConfigGetListTotal();
    },
    async getImageList() {
      // 处理图片模式列表数据
      try {
        this.loading = true;
        const data = await this.MixinGetImageList();
        this.resultData = data;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 导出
    onExport() {
      this.$refs.exportModule.init(this.$route.query.batchId);
    },
    handleExport(val) {
      this.MixinGetExport(val);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      deviceTagData: 'governanceevaluation/deviceTagData',
    }),
    // customizedAttrs() {
    //   return {
    //     iconList: this.iconList,
    //     tableColumns: this.tableColumns,
    //     tableData: this.tableData,
    //     formItemData: this.formItemData,
    //     formData: this.formData,
    //     tableLoading: this.tableLoading,
    //     totalCount: this.totalCount,
    //     cardList: this.cardList,
    //   };
    // },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    ListPattern: require('../components/list-pattern').default,
    LookScene: require('@/components/look-scene').default,
    CardPattern: require('./components/card-pattern').default,
    exportData: require('../components/export-data').default,
  },
};
</script>
<style lang="less" scoped>
.review-particular {
  padding: 0 10px;

  .icon-statics-wrapper {
    display: flex;
    justify-content: space-between;
    height: 50px;
    line-height: 50px;
  }

  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;

    .ui-image {
      min-height: 56px !important;

      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
}
</style>
