<!--
    * @FileDescription: 开车打电话分析
    * @Author: H
    * @Date: 2024/05/28
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-02-11 14:35:23
-->
<template>
  <div class="analyse-wrap">
    <div class="search_box">
      <div class="title">
        <p>开车打电话分析</p>
      </div>
      <div class="form-box">
        <Form
          ref="form"
          :rules="ruleValidate"
          :model="formData"
          :label-width="80"
        >
          <FormItem label="开始时间:" prop="startTime">
            <DatePicker
              v-model="formData.startTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="结束时间:" prop="endTime">
            <DatePicker
              v-model="formData.endTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="结束时间"
              transfer
            ></DatePicker>
          </FormItem>
          <FormItem label="选择设备:">
            <ul class="search_content">
              <li class="active-area-sele" @click="handleSelemodel">
                选择设备/已选({{ formData.deivceList.length }})
              </li>
            </ul>
          </FormItem>
          <FormItem label="车牌号码:" prop="plateNo">
            <Input v-model="formData.plateNo" placeholder="请输入"></Input>
          </FormItem>
          <FormItem label="车牌颜色:">
            <ui-tag-select
              style="position: relative; top: 5px"
              ref="plateColor"
              @input="
                (e) => {
                  input(e, 'plateColor');
                }
              "
            >
              <ui-tag-select-option
                v-for="(item, $index) in licensePlateColorList"
                :key="$index"
                effect="dark"
                :name="item.dataKey"
              >
                <div
                  v-if="licensePlateColorArray[item.dataKey]"
                  :title="item.dataValue"
                  :style="{
                    borderColor:
                      licensePlateColorArray[item.dataKey].borderColor,
                  }"
                  class="plain-tag-color"
                >
                  <div
                    :style="licensePlateColorArray[item.dataKey].style"
                  ></div>
                </div>
              </ui-tag-select-option>
            </ui-tag-select>
          </FormItem>
          <FormItem label="车辆品牌:" prop="vehicleBrand">
            <Select
              v-model="formData.vehicleBrand"
              filterable
              clearable
              placeholder="请选择"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleBrandLists"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆型号:" prop="vehicleSubBrand">
            <Input
              v-model="formData.vehicleSubBrand"
              placeholder="请输入"
              clearable
            ></Input>
          </FormItem>
          <FormItem label="车辆颜色:" prop="vehicleColor">
            <Select
              v-model="formData.vehicleColor"
              clearable
              placeholder="请选择"
            >
              <Option
                v-for="(item, $index) in bodyColorList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="车辆类型:" prop="vehicleType">
            <Select
              v-model="formData.vehicleType"
              filterable
              clearable
              placeholder="请选择车辆类型"
              class="input-200"
            >
              <Option
                v-for="(item, $index) in vehicleTypeList"
                :key="$index"
                :value="item.dataKey"
                >{{ item.dataValue }}</Option
              >
            </Select>
          </FormItem>
          <div class="btn-group">
            <Button type="primary" class="btnwidth" @click="handleSearch"
              >查询</Button
            >
            <Button type="default" @click="handleReset">重置</Button>
          </div>
        </Form>
      </div>
    </div>
    <div class="table_box">
      <div class="data-export">
        <Button
          class="mr"
          :type="queryParam.order == 0 ? 'primary' : 'default'"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <Button class="mr" @click.stop="exportShow = true" size="small">
          <ui-icon type="daoru" color="#2C86F8"></ui-icon>
          导出
        </Button>
        <export-box
          ref="exportbox"
          v-if="exportShow"
          :wholePage="true"
          @confirm="confirm"
          @cancel="hideExportModal"
        />
      </div>
      <div class="table_content">
        <div class="list-card" v-for="(item, index) in dataList" :key="index">
          <div class="img-content">
            <img
              v-viewer
              :src="item.traitImg"
              alt=""
              @click.stop="handleDetail(item, index)"
            />
            <b class="shade vehicle">
              <ui-plate-number
                :plateNo="item.plateNo"
                :color="item.plateColor"
                size="mini"
              ></ui-plate-number>
            </b>
          </div>
          <div class="bottom-info">
            <time>
              <Tooltip
                content="抓拍时间"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-time"></i>
              </Tooltip>
              {{ item.absTime }}
            </time>
            <p>
              <Tooltip
                content="抓拍地点"
                placement="right"
                transfer
                theme="light"
              >
                <i class="iconfont icon-location"></i>
              </Tooltip>
              <span class="ellipsis" v-show-tips>{{ item.deviceName }}</span>
            </p>
            <!-- <div class="active-icon">
                            <i class="iconfont icon-location" title="定位" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-qiche" title="以图搜车" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-location" title="定位" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-qiche" title="以图搜车" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-location" title="定位" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-qiche" title="以图搜车" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-location" title="定位" @click.stop="handleIcon('1', 'type')"/>
                            <i class="iconfont icon-qiche" title="以图搜车" @click.stop="handleIcon('1', 'type')"/>
                        </div> -->
          </div>
        </div>
      </div>
      <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
      <!-- 分页 -->
      <ui-page
        :current="pageInfo.pageNumber"
        :total="total"
        countTotal
        :page-size="pageInfo.pageSize"
        :page-size-opts="[28, 56, 84, 112]"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      >
      </ui-page>
    </div>
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />

    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(false, true)"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="warnModalShow"
      title="提示"
      :r-width="500"
      @onCancel="modalStatus(true, false)"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import { licensePlateColorArray } from "@/libs/system";
import plateNo from "@/components/ui-vehicle/index.vue";
import { drivecalllist, exportDrivePhoneData } from "@/api/modelMarket";
import { getVehicleBrandList } from "@/api/vehicleArchives";
import { getDateTime } from "@/util/modules/common";
import ExportBox from "@/views/wisdom-cloud-search/search-center/components/export/export-box.vue";
import hlModal from "@/components/modal/index.vue";
import { taskView } from "@/api/wisdom-cloud-search";
export default {
  components: {
    plateNo,
    ExportBox,
    hlModal,
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        state: "",
        type: "",
        vehicleColor: "",
        vehicleType: "",
        vehicleBrand: "",
        plateColor: "",
        deivceList: [],
        vehicleSubBrand: "",
        startTime: "",
        endTime: "",
        plateNo: "",
      },
      queryParam: {
        order: 1,
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 28,
      },
      dataList: [],
      vehicleBrandLists: [],
      total: 0,
      timeUpDown: false,
      loading: false,
      selectDeviceList: [],
      ruleValidate: {},
      checkedLabels: [], // 已选择的标签
      licensePlateColorArray,
      exportShow: false, // 导出弹框
      modalShow: false, // 导出loading弹框
      warnModalShow: false, // 中断导出提示框
      downTaskId: "", // 下载任务
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0, // 预计时间
    };
  },
  async created() {
    window.addEventListener("click", this.hideExportModal);
    await this.getDictData();
    this.handleVehicleBrand();
    this.$nextTick(() => {
      this.formData.startTime = getDateTime(-6);
      this.formData.endTime = this.$dayjs(new Date()).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      // 推荐中心查看
      console.log("taskParams: ", this.taskParams);
      if (!Toolkits.isEmptyObject(this.taskParams)) {
        this.selectDeviceList = this.taskParams.params.selectDeviceList || [];
        this.formData.deivceList = this.selectDeviceList.map(
          (item) => item.deviceGbId
        );
        if (this.taskParams.queryStartTime)
          this.formData.startTime = this.taskParams.queryStartTime;
        if (this.taskParams.queryEndTime)
          this.formData.endTime = this.taskParams.queryEndTime;
        if (this.taskParams.taskResult) this.handleSearch();
      }
    });
  },
  destroyed() {
    clearInterval(this.loadIntervel);
    clearInterval(this.timeInterval);
    window.removeEventListener("click", this.hideExportModal);
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    /**
     * @description: 关闭导出框
     */
    hideExportModal() {
      this.exportShow = false;
    },

    /**
     * @description: 导出提示框
     * @param {boolean} modalShow 导出loading弹框
     * @param {boolean} warnModalShow 中断导出提示框
     */
    modalStatus(modalShow, warnModalShow) {
      this.modalShow = modalShow;
      this.warnModalShow = warnModalShow;
    },

    /**
     * @description: 中断下载
     */
    onOk() {
      this.modalStatus(false, false);
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },

    /**
     * @description: 轮询导出数据
     */
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },

    /**
     * @description: 确认导出
     * @param {object} param 导出配置
     */
    confirm(param) {
      let params = {
        ids: [],
        downloadPics: param.downloadPics,
        downloadSize: null,
        downNeed: true,
        ...this.formData,
        startTime: this.$dayjs(this.formData.startTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        endTime: this.$dayjs(this.formData.endTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
      };
      // type: 1导出选中数据
      if (param.type == "1") {
        params.ids = this.dataList.map((e) => e._id);
        if (!params.ids.length) {
          this.$Message.warning("当前页无数据！");
          return;
        }
      } else {
        params.downloadSize = param.downloadSize;
      }
      this.hideExportModal();
      this.modalShow = true;
      exportDrivePhoneData(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {
          // this.$refs.exportbox.handleEnd();
        });
    },
    // 查询
    handleSearch() {
      if (this.formData.startTime > this.formData.endTime) {
        this.$$Message.wraning("结束时间不能大于开始时间！");
        return;
      }
      this.pageInfo.pageNumber = 1;
      this.queryList();
    },
    queryList() {
      this.loading = true;
      this.dataList = [];
      let params = {
        ...this.formData,
        startTime: this.$dayjs(this.formData.startTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        endTime: this.$dayjs(this.formData.endTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        ...this.queryParam,
        ...this.pageInfo,
      };
      drivecalllist(params)
        .then((res) => {
          this.dataList = res.data.entities;
          this.total = res.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 重置
    handleReset() {
      this.$refs.form.resetFields();
      this.pageInfo.pageNumber = 1;
      this.formData.startTime = getDateTime(-6);
      this.formData.endTime = this.$dayjs(new Date()).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.$refs.plateColor.clearChecked();
      // 清空选择的设备
      this.$refs.selectDevice.removeAllHandle();
      this.queryList();
    },
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.queryParam.order = this.timeUpDown ? 0 : 1;
      this.queryList();
    },
    handleDetail(row, index) {
      console.log(row, index, "row, index");
    },
    handleVehicleBrand() {
      this.vehicleBrandLists = [];
      getVehicleBrandList("")
        .then((res) => {
          this.vehicleBrandLists = res.data;
        })
        .catch(() => {})
        .finally(() => {});
    },
    /**
     * 选择设备
     */
    handleSelemodel() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.formData.deivceList = list.map((item) => item.deviceGbId);
    },
    /**
     * 选择接口返回数据
     */
    input(e, key) {
      this.formData[key] = e;
      this.$forceUpdate();
    },
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.queryList();
    },
    pageSizeChange(page) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = page;
      this.queryList();
    },
  },
};
</script>
<style lang="less" scoped>
@import "../../style/index";
@import "../../style/vehicle";
.analyse-wrap {
  /deep/ .ivu-tag-select-option {
    margin-right: 6px;
  }
}
</style>
