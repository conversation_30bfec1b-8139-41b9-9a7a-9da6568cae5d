<template>
  <div class="picture-item">
    <div class="img-box">
      <Checkbox :style="selected ? 'display: block' : null" class="select-checkbox" v-model="selected"></Checkbox>
      <ui-image :src="imgSrc" />
      <slot name="button"></slot>
    </div>
    <div class="mt-xs">
      <slot name="message"></slot>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    showCheckbox: {
      type: Boolean,
      default: true,
    },
    imgSrc: {
      type: String,
    },
  },
  data() {
    return {
      selected: false,
    };
  },
  created() {},
  methods: {},
  watch: {
    value: {
      handler(val) {
        this.selected = val;
      },
      immediate: true,
    },
    selected(val) {
      this.$emit('input', val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.picture-item {
  width: 213px;
  padding: 15px;
  margin-left: 10px;
  margin-bottom: 10px;
  display: inline-block;
  background-color: var(--bg-info-card);
  border: 1px solid var(--border-info-card);
  border-radius: 4px;
  &:hover {
    .img-box {
      .select-checkbox {
        display: block;
      }
    }
  }
  .img-box {
    width: 183px;
    height: 179px;
    position: relative;
    .select-checkbox {
      display: none;
      position: absolute;
      left: 0;
      top: 0;
      z-index: 2;
      line-height: 16px;
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  @{_deep} .ui-image {
    z-index: 1;
  }
}
</style>
