<template>
  <div class="interface-exception auto-fill">
    <Collapse v-model="collapseValue" simple>
      <Panel name="1" class="mb-sm">
        <span class="title">工单处理 </span>
        <i-switch
          class="fr switch"
          v-model="formData['0701'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">有新工单指派</span>
          </ui-label>
          <ui-label label="通知接收人：" class="mt-sm">
            <span class="base-text-color f-14">工单待签收人</span>
          </ui-label>
          <ui-label label="时间间隔：" class="d_flex mt-sm">
            <InputNumber
              :min="0"
              :disabled="!isNetworkOpen"
              v-model="formData['0701'].interval"
              class="width-xs mr-sm"
            ></InputNumber>
            <span class="base-text-color f-14">分钟内不重复发送</span>
          </ui-label>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0701'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0701systemTemplateRef">
                    您有新的工单任务待签收，请及时处理！（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isNetworkOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0701dialogTemplateRef">
                    您有新的工单任务待签收，请及时处理！（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isNetworkOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0701homeTemplateRef">
                    您有新的工单任务待签收，请及时处理！（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isNetworkOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0701smsTemplateRef">
                    您有新的工单任务待签收，请及时处理！（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
    </Collapse>
  </div>
</template>
<script>
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      collapseValue: ['1'],
      listTaskSchemes: [], //检测任务
      formData: {
        '0701': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
          interval: 0,
        },
      },
    };
  },
  async created() {
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     * 0601： 资产审核不通过
     * 0701： 工单处理
     */
    await this.initMx(['0701']);
    this.$set(this.formData['0701'], 'interval', this.formData['0701'].sendTime[0].interval);
  },
  methods: {
    reset() {
      this.resetMx();
    },
    async save() {
      try {
        this.formData['0701'].systemTemplate = this.$refs['0701systemTemplateRef'].innerText;
        this.formData['0701'].dialogTemplate = this.$refs['0701dialogTemplateRef'].innerText;
        this.formData['0701'].homeTemplate = this.$refs['0701homeTemplateRef'].innerText;
        this.formData['0701'].smsTemplate = this.$refs['0701smsTemplateRef'].innerText;

        await this.saveMx(['0701']);
        // this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
  },
  watch: {},
  computed: {
    isEdit() {
      return this.action === 'edit';
    },
    isNetworkOpen() {
      return this.formData['0701'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.interface-exception {
  overflow-y: auto;

  .notification-method {
    label {
      display: block;
      margin-bottom: 5px;
    }
    &:nth-child(n + 2) {
      margin-left: 80px;
    }
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .message-content {
    margin-left: 10px;
    display: inline-block;
    border: 1px solid var(--border-input);
    padding: 0 10px;
    background-color: var(--bg-input);
    width: 760px;
    vertical-align: text-top;
    border-radius: 4px;
    span {
      color: var(--color-title);
    }
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: var(--color-navigation-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        margin: 0 20px;
        .title {
          font-size: 16px;
          font-weight: 900;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: var(--bg-content);
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: var(--bg-content);
      }
    }
  }
}
</style>
