[data-theme='dark'] {
  .userName {
    text-shadow: 0 0 10px var(--color-primary);
  }
  .userImg {
    background-color: #010f1f;
  }
  .user-Info-left::before {
    background: url('~@/assets/img/user-center/userHead_bg.png') no-repeat left bottom !important;
    background-size: 100% 100% !important;
    -webkit-animation: rotation 60s linear infinite !important;
    animation: rotation 60s linear infinite !important;
  }
}

[data-theme='deepBlue'] {
  .user-Info-left::before {
    background: url('~@/assets/img/user-center/userHead_bg_deepBlue.png') no-repeat left bottom !important;
    background-size: 100% 100% !important;
    -webkit-animation: rotation 60s linear infinite !important;
    animation: rotation 60s linear infinite !important;
  }
}

.personalCenterWrap {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  overflow-y: auto;
  position: relative;
  background:
    url('~@/assets/img/user-center/userCenter-right-bg.png') no-repeat right bottom/contain,
    url('~@/assets/img/user-center/userCenter-left-bg.png') no-repeat -12px bottom/contain;
  .title-text {
    border-bottom: 1px solid var(--border-color);
    position: relative;
    margin-left: 40px;
    height: 65px;
    line-height: 65px;

    .title-text-en {
      font-size: 36px;
      font-weight: bold;
      opacity: 0.6;
      line-height: 46px;
      position: absolute;
      bottom: -4px;
      left: 42px;
      color: var(--border-color);
    }

    .title-text-cn {
      font-size: 28px;
      font-weight: bold;
      color: var(--color-content);
      position: absolute;
      top: 4px;
      left: 0;
    }
  }
  .user-info-wrap {
    height: 89%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .user-Info {
    text-align: center;
    margin-top: -8%;
    vertical-align: top;
    min-width: 800px;
    position: relative;
    z-index: 3;

    .ivu-btn-primary {
      margin: 0px 5px;
    }

    .user-Info-left {
      width: 380px;
      height: 380px;
      position: relative;
      display: inline-block;
      vertical-align: top;

      .userImg {
        width: 180px;
        height: 180px;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        margin: auto;
        border-radius: 50%;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      div {
        position: absolute;
        bottom: -35px;
        width: 400px;
        text-align: center;
      }
    }

    .user-Info-left::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 380px;
      height: 380px;
      background: url('~@/assets/img/user-center/userHead_bg_light.png') no-repeat left bottom;
      background-size: 100% 100%;
      -webkit-animation: rotation 60s linear infinite;
      animation: rotation 60s linear infinite;
    }

    @keyframes rotation {
      from {
        transform: rotateZ(0deg);
      }

      to {
        transform: rotateZ(360deg);
      }
    }

    .user-Info-right {
      display: inline-block;
      vertical-align: top;
      padding: 25px 0 10px;
      margin-left: 85px;
      font-size: 16px;

      .info-item {
        margin: 15px auto;
        text-align: left;
        width: 455px;
        word-wrap: break-word;

        .userName {
          color: var(--color-content);
          font-size: 24px;
          font-weight: bold;
          position: relative;
          z-index: 0;
          transition: all 1.5s ease;
        }

        .userName::before {
          content: '';
          position: absolute;
          pointer-events: none;
          z-index: -1;
          top: 0;
          left: -139px;
          width: 530px;
          height: 105px;
          background: url('~@/assets/img/user-center/name_bg.png') no-repeat left bottom;
          background-size: 100% 100%;
        }

        .name-cn,
        .name-zn {
          display: inline-block;
          padding-bottom: 2px;
        }

        .name-cn {
          font-size: 14px;
          color: var(--color-primary);
        }

        .name-zn {
          font-size: 12px;
          color: var(--color-primary);
          opacity: 0.6;
          margin-left: 5px;
        }

        .info-item-des {
          font-size: 18px;
          color: var(--color-content);

          .ivu-divider {
            font-size: 18px;
            background-color: var(--color-primary);
          }
        }
      }

      .item-sec {
        display: flex;
        justify-content: space-between;
        width: 455px;
        padding-top: 20px;
      }
    }
  }

  .left {
    position: absolute;
    left: -20px;
    bottom: 0;
    z-index: 1;
    width: 552px;
    padding-top: 1%;
    height: 100%;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .right {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 1320px;
    z-index: 1;

    img {
      width: 100%;
    }
  }
}
