<template>
  <div class="task-expand-box">
    <ui-table
      :show-header="false"
      :stripe="false"
      :table-columns="expandTableColumns"
      :table-data="showGpuList"
      disabled-hover
    >
      <template #GPU="{ row }">
        <Progress
          style="width: 140px"
          :percent="row._viewGpuItem.percent"
          :stroke-color="row._viewGpuItem.percent > 90 ? '#E24A2B' : '#3FE3AC'"
          :hide-info="true"
        />
        <!-- xxx/4卡/15GB/0% -->
        <div>{{ row._viewGpuItem.text }}</div>
      </template>
    </ui-table>
  </div>
</template>
<script>
import { tableColumns } from '../utils/enum';
export default {
  name: 'taskExpandRow',
  props: {
    rowGpu: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      expandTableColumns: [],
    };
  },
  computed: {
    showGpuList() {
      let gpuList = this.rowGpu.slice(1);
      return gpuList.map((item) => {
        return { ...item, _viewGpuItem: this.returnGpuItem(item) };
      });
    },
  },
  methods: {
    setExpandTableColumn() {
      this.expandTableColumns = tableColumns(true).filter((item) => item.isShow);
    },
    //计算Gpu百分比
    returnGpuItem(item) {
      try {
        const percent = (item.gpuMemUsed / item.gpuMemTotal) * 100;
        const formatPercent = Number.isInteger(percent) ? percent : percent.toFixed(2) * 1;
        return {
          percent: formatPercent,
          text: `${item.model}/${item.gpuCount}卡/${item.gpuMemTotal}GB/${formatPercent}%`,
        };
      } catch (err) {
        console.log(err);
        return {
          percent: 0,
          text: '--',
        };
      }
    },
  },
  mounted() {
    this.setExpandTableColumn();
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.task-expand-box {
  @{_deep} .ivu-table {
    .ivu-table-overflowX {
      overflow-x: hidden;
    }
  }
  .progress-box {
    .ivu-progress {
      width: 140px;
    }
    @{_deep} .ivu-progress-inner {
      border: 1px solid #427ec9;
      background: #062042;
      padding: 2px;
    }
  }
}
</style>
