<template>
  <div class="base-search mb-sm">
    <ui-label class="inline" :label="global.filedEnum.deviceId" :width="70">
      <Input v-model="searchData.deviceId" class="width-lg" :placeholder="`请输入${global.filedEnum.deviceId}`"></Input>
    </ui-label>
    <ui-label class="inline ml-lg" :label="global.filedEnum.deviceName" :width="70">
      <Input
        v-model="searchData.deviceName"
        class="width-lg"
        :placeholder="`请输入${global.filedEnum.deviceName}`"
      ></Input>
    </ui-label>
    <ui-label class="inline ml-lg" label="联网状态" :width="70">
      <Select class="width-sm" v-model="searchData.deviceStatus" clearable placeholder="请选择检测结果">
        <Option value="1" label="联网"></Option>
        <Option value="2" label="未联网"></Option>
      </Select>
    </ui-label>
    <ui-label :width="0" label=" " class="inline ml-lg">
      <Button type="primary" class="mr-sm" @click="startSearch">查询</Button>
      <Button type="default" class="mr-lg" @click="clear">重置</Button>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      searchData: {
        deviceId: '',
        deviceName: '',
        deviceStatus: '',
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  mounted() {},
  methods: {
    startSearch() {
      this.$emit('startSearch', this.searchData);
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.startSearch);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
