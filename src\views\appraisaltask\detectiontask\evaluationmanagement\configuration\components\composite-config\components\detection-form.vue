<template>
  <div>
    <div class="detection-item alignflex mb-md" v-for="item in detectionData" :key="item.id">
      <div class="detection-item-col alignflex">
        <span>{{ item.indexName }}</span>
      </div>
      <div class="detection-item-col alignflex ml-lg">
        <span>最近检测：</span>
        <InputNumber v-model.number="item.count" :max="100" :min="1" placeholder="请输入" class="ipt-item-width">
        </InputNumber>
        <span class="params-suffix ml-md">次</span>
      </div>
      <div class="detection-item-col alignflex ml-lg">
        <span>检测结果：</span>
        <Select class="select-width" v-model="item.result" placeholder="请选择">
          <Option v-for="resItem in resList" :key="resItem.dataKey" :value="resItem.dataKey">
            {{ resItem.dataValue }}
          </Option>
        </Select>
      </div>
      <div class="detection-item-col alignflex ml-lg">
        <Select class="select-width" v-model="item.compareType" placeholder="请选择">
          <Option v-for="resItem in symbols" :key="resItem.dataKey" :value="resItem.dataKey">
            {{ resItem.dataValue }}
          </Option>
        </Select>
      </div>
      <div class="detection-item-col alignflex ml-lg">
        <InputNumber v-model.number="item.compareCount" :max="100" :min="1" placeholder="请输入" class="ipt-item-width">
        </InputNumber>
        <span class="params-suffix ml-md">次</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'dection-form',
  props: {
    detectionList: {
      type: Array,
      default: () => [],
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      detectionData: [],
      resList: [
        { dataKey: 0, dataValue: '合格' },
        { dataKey: 1, dataValue: '不合格' },
      ],
      symbols: [
        { dataKey: 2, dataValue: '>' },
        { dataKey: 0, dataValue: '=' },
        { dataKey: 1, dataValue: '<' },
      ],
    };
  },
  methods: {
    handleSave() {
      if (this.required) {
        for (let i = 0; i < this.detectionData.length; i++) {
          const keys = Object.keys(this.detectionData[i]);
          for (let j = 0; j < keys.length; j++) {
            if (this.detectionData[i][keys[j]] === '') {
              this.$Message.error(`请将${this.detectionData[i]['indexName']}的信息填写完整!`);
              return false;
            }
          }
        }
      }
      return this.detectionData;
    },
  },
  watch: {
    detectionList: {
      handler(val) {
        this.detectionData = val.map((item) => {
          return {
            compareCount: item.compareCount || 1,
            compareType: item.compareCount || 0,
            count: item.compareCount || 1,
            result: item.compareCount || 1,
            ...item,
          };
        });
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style lang="less" scoped>
.alignflex {
  display: flex;
  align-items: center;
}
.detection-item {
  width: 100%;
  padding: 0 10px;
  color: var(--color-content);
  &-col:nth-child(1) {
    flex: 1;
  }
  .select-width {
    width: 150px;
  }
}
</style>
