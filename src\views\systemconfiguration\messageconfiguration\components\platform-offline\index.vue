<template>
  <div class="interface-exception auto-fill">
    <Collapse v-model="collapseValue" simple>
      <Panel name="1" class="mb-sm">
        <span class="title">联网平台离线 </span>
        <i-switch
          class="fr switch"
          v-model="formData['0401'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">离线</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0401'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isNetworkOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0401', index)"
                  @on-clear="clearTask('0401', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0401', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isNetworkOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0401')"
                ></i>
                <i
                  v-if="index !== 0 && isNetworkOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0401', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0401')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0401')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0401')"
              :table-data="receiveData['0401'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isNetworkOpen ? '' : 'not-allowed']"
                  @click="() => isNetworkOpen && selectNotification('0401', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isNetworkOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0401', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0401'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0401systemTemplateRef">
                    【<span>#组织机构#_#联网平台#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isNetworkOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0401dialogTemplateRef">
                    【<span>#组织机构#_#联网平台#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isNetworkOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0401homeTemplateRef">
                    【<span>#组织机构#_#联网平台#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isNetworkOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0401smsTemplateRef">
                    【<span>#组织机构#_#联网平台#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
      <Panel name="2" class="mb-sm">
        <span class="title">人脸视图库离线</span>
        <i-switch
          class="fr switch"
          v-model="formData['0402'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">离线</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0402'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isFaceOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0402', index)"
                  @on-clear="clearTask('0402', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0402', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isFaceOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0402')"
                ></i>
                <i
                  v-if="index !== 0 && isFaceOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0402', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0402')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0402')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0402')"
              :table-data="receiveData['0402'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isFaceOpen ? '' : 'not-allowed']"
                  @click="() => isFaceOpen && selectNotification('0402', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isFaceOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0402', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0402'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0402systemTemplateRef">
                    【<span>#组织机构#_#人脸视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isFaceOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0402dialogTemplateRef">
                    【<span>#组织机构#_#人脸视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isFaceOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0402homeTemplateRef">
                    【<span>#组织机构#_#人脸视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isFaceOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0402smsTemplateRef">
                    【<span>#组织机构#_#人脸视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
      <Panel name="3">
        <span class="title">车辆视图库离线</span>
        <i-switch
          class="fr switch"
          v-model="formData['0403'].markEnable"
          :disabled="!isEdit"
          :true-value="1"
          :false-value="0"
          @click.native.stop
        />
        <div slot="content">
          <ui-label label="通知条件：">
            <span class="base-text-color f-14">离线</span>
          </ui-label>
          <ui-label label="检测任务：" class="mt-sm">
            <div class="task-list">
              <div v-for="(item, index) in formData['0403'].taskIds" :key="index" class="mb-sm">
                <Select
                  :value="item"
                  :disabled="!isVehicleOpen"
                  placeholder="请选择检测任务"
                  class="width-lg"
                  clearable
                  @on-change="(val) => selectTask(val, '0403', index)"
                  @on-clear="clearTask('0403', index)"
                >
                  <Option
                    v-for="(item, index) in listTaskSchemes"
                    :key="index"
                    :label="item.taskName"
                    :value="item.id"
                    :disabled="getTaskIdsDisabled(item.id, '0403', 'formData')"
                    >{{ item.taskName }}</Option
                  >
                </Select>
                <i
                  v-if="isVehicleOpen"
                  class="icon-font icon-tree-add ml-sm font-active-color"
                  @click="addTask('0403')"
                ></i>
                <i
                  v-if="index !== 0 && isVehicleOpen"
                  class="icon-font icon-shanchu1 ml-sm font-active-color"
                  @click="deleteTask('0403', index)"
                ></i>
              </div>
            </div>
          </ui-label>
          <div class="base-text-color f-14 mb-sm option-box">
            <span>通知接收人：</span>
            <div>
              <i class="icon-font icon-fuzhi" title="复制" @click="copyConfig('0403')"></i>
              <i
                :class="['icon-font icon-paste-full ml-sm', copyReceiveConfig ? null : 'not-copy']"
                title="粘贴"
                @click="paste('0403')"
              ></i>
            </div>
          </div>
          <div class="table-module auto-fill">
            <ui-table
              class="ui-table auto-fill"
              :table-columns="setTableColumns('0403')"
              :table-data="receiveData['0403'].receiveConfig"
            >
              <template #people="{ row }">
                <span
                  :class="['font-active-color', 'pointer', isVehicleOpen ? '' : 'not-allowed']"
                  @click="() => isVehicleOpen && selectNotification('0403', row)"
                >
                  {{ row.peopleList && row.peopleList.length ? '已配置' : '未配置' }}
                </span>
              </template>
              <template #option="{ index }">
                <ui-btn-tip
                  v-if="isVehicleOpen"
                  icon="icon-yichu1"
                  content="移除"
                  @handleClick="deleteRow('0403', index)"
                ></ui-btn-tip>
              </template>
            </ui-table>
          </div>
          <div class="mt-sm">
            <ui-label label="通知方式：">
              <CheckboxGroup class="notification-method" v-model="formData['0403'].template">
                <Checkbox label="system" :disabled="true">
                  <span>系统消息</span>
                  <span class="message-content" ref="0403systemTemplateRef">
                    【<span>#组织机构#_#车辆视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="dialog" :disabled="!isVehicleOpen">
                  <span>系统弹框</span>
                  <span class="message-content" ref="0403dialogTemplateRef">
                    【<span>#组织机构#_#车辆视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="home" :disabled="!isVehicleOpen">
                  <span>首页推送</span>
                  <span class="message-content" ref="0403homeTemplateRef">
                    【<span>#组织机构#_#车辆视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
                <Checkbox label="sms" :disabled="!isVehicleOpen">
                  <span>短信通知</span>
                  <span class="message-content" ref="0403smsTemplateRef">
                    【<span>#组织机构#_#车辆视图库#</span>】
                    <span>#异常原因#</span>, 请及时处理!（时间： <span>#time@yyyy-MM-dd HH:mm:ss#</span>）
                  </span>
                </Checkbox>
              </CheckboxGroup>
            </ui-label>
          </div>
        </div>
      </Panel>
    </Collapse>
    <people-select-module
      v-model="peopleSelectShow"
      title="选择通知对象"
      :default-people-list="defaultPeopleList"
      @pushPeople="pushPeople"
    ></people-select-module>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
import async from '../../mixins/async';
export default {
  mixins: [async],
  props: {
    // 判断编辑状态
    action: {
      type: String,
    },
  },
  data() {
    return {
      collapseValue: [],
      listTaskSchemes: [], //检测任务
      peopleSelectShow: false,
      tableColumns: [
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          type: 'index',
        },
        {
          type: 'expand',
          width: 50,
        },
        {
          title: '所属组织机构',
          minWidth: 300,
          key: 'orgName',
        },
        {
          title: '配置通知接收人',
          minWidth: 300,
          slot: 'people',
        },
        {
          width: 300,
          title: '操作',
          slot: 'option',
        },
      ],
      formData: {
        '0401': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
        '0402': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
        '0403': {
          markEnable: 0,
          template: [], //通知方式
          taskIds: [], //通知任务来源
          receiveConfig: [], //通知接收人
          systemTemplate: '',
          dialogTemplate: '',
          homeTemplate: '',
          smsTemplate: '',
        },
      },
      // 由于后端返回的数据不能兼容前端页面所以这里要单独处理接收人的数据
      receiveData: {
        '0401': {
          receiveConfig: [],
        },
        '0402': {
          receiveConfig: [],
        },
        '0403': {
          receiveConfig: [],
        },
      },
      defaultPeopleList: [],
      receiveConfigData: null, //点击配置接收人时记录
    };
  },
  async created() {
    await this.getListTaskSchemes();
    /**
     * 0000：通用参数
     * 0101：外部对接异常
     * 0201：系统运维异常
     * 0301：接口异常-人脸相关接口
     * 0302：接口异常-车辆相关接口
     * 0401：平台离线-联网平台离线
     * 0402：平台离线-人脸视图库离线
     * 0403：平台离线-车辆视图库离线
     * 0501：设备离线-视频监控设备离线
     * 0502：设备离线-人脸卡口设备离线
     * 0503：设备离线-车辆卡口设备离线
     */
    await this.initMx(['0401', '0402', '0403']);
    this.dealReceiveCofingEchoMx();
  },
  methods: {
    ...mapActions({
      setCopyReceiveConfig: 'systemconfiguration/setCopyReceiveConfig',
    }),
    selectNotification(type, org) {
      this.receiveConfigData = {
        type,
        org,
      };
      this.defaultPeopleList = this.getDefaultPeopleMx(org.peopleList);
      this.peopleSelectShow = true;
    },
    pushPeople(list) {
      // 如果已经保存的数据中的联系电话已经有，则以已保存的电话为填写值
      const peopleList = list.map((row) => {
        const people = this.defaultPeopleList.find((people) => people.id === row.id);
        if (people && people.phone) {
          this.$set(row, 'phone', people.phone);
        } else {
          this.$set(row, 'phone', row.phoneNumber);
        }
        return row;
      });
      // 根据点击selectNotification时的组织机构赋值
      const receiveOrg = this.receiveData[this.receiveConfigData.type].receiveConfig.find(
        (row) => row.orgCode === this.receiveConfigData.org.orgCode,
      );
      receiveOrg.peopleList = peopleList.map(({ id, orgCode, orgName, name, username, phone }) => {
        return {
          id,
          name,
          orgCode,
          orgName,
          username,
          phone,
          orgCodeByAttach: receiveOrg.orgCode,
          orgCodeByAttachName: receiveOrg.orgName,
        };
      });
    },
    reset() {
      this.receiveData = this.$util.common.deepCopy(this.initializedReceiveDataMx);
      this.resetMx();
    },
    async save() {
      try {
        this.dealReceiveCofingSaveMx();
        this.formData['0401'].systemTemplate = this.$refs['0401systemTemplateRef'].innerText;
        this.formData['0401'].dialogTemplate = this.$refs['0401dialogTemplateRef'].innerText;
        this.formData['0401'].homeTemplate = this.$refs['0401homeTemplateRef'].innerText;
        this.formData['0401'].smsTemplate = this.$refs['0401smsTemplateRef'].innerText;
        this.formData['0402'].systemTemplate = this.$refs['0402systemTemplateRef'].innerText;
        this.formData['0402'].dialogTemplate = this.$refs['0402dialogTemplateRef'].innerText;
        this.formData['0402'].homeTemplate = this.$refs['0402homeTemplateRef'].innerText;
        this.formData['0402'].smsTemplate = this.$refs['0402smsTemplateRef'].innerText;
        this.formData['0403'].systemTemplate = this.$refs['0403systemTemplateRef'].innerText;
        this.formData['0403'].dialogTemplate = this.$refs['0403dialogTemplateRef'].innerText;
        this.formData['0403'].homeTemplate = this.$refs['0403homeTemplateRef'].innerText;
        this.formData['0403'].smsTemplate = this.$refs['0403smsTemplateRef'].innerText;
        await this.saveMx(['0401', '0402', '0403']);
        // this.updateInitialMx();
      } catch (err) {
        console.log(err);
      }
    },
    async getListTaskSchemes() {
      try {
        this.listTaskSchemes = [];
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.listTaskSchemes = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    addTask(type) {
      this.formData[type].taskIds.push('');
    },
    deleteTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds.splice(index, 1);
      this.delReceiveOrgMx(type, this.formData);
    },
    async selectTask(val, type, index) {
      if (!val) return;
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = val;
      await this.setReceiveOrgMx(val, type);
      this.delReceiveOrgMx(type, this.formData);
    },
    clearTask(type, index) {
      this.setTaskIdsInfoMx(type, this.formData[type].taskIds, index);
      this.formData[type].taskIds[index] = '';
      this.delReceiveOrgMx(type, this.formData);
    },
    deleteRow(type, index) {
      this.receiveData[type].receiveConfig.splice(index, 1);
    },
    copyConfig(type) {
      this.setCopyReceiveConfig(this.$util.common.deepCopy(this.receiveData[type].receiveConfig));
      this.$Message.success('复制成功');
    },
    paste(type) {
      if (!this.copyReceiveConfig) return;
      this.receiveData[type].receiveConfig = this.$util.common.deepCopy(this.copyReceiveConfig);
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      copyReceiveConfig: 'systemconfiguration/getCopyReceiveConfig',
    }),
    isEdit() {
      return this.action === 'edit';
    },
    isNetworkOpen() {
      return this.formData['0401'].markEnable === 1 && this.action === 'edit';
    },
    isFaceOpen() {
      return this.formData['0402'].markEnable === 1 && this.action === 'edit';
    },
    isVehicleOpen() {
      return this.formData['0403'].markEnable === 1 && this.action === 'edit';
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    PeopleSelectModule: require('@/components/people-select-module.vue').default,
  },
};
</script>
<style lang="less" scoped>
.interface-exception {
  overflow-y: auto;
  .task-list {
    width: 310px;
    max-height: 300px;
    overflow-y: auto;
  }
  .table-module {
    clear: both;
    height: 260px;
  }
  .notification-method {
    label {
      display: block;
      margin-bottom: 5px;
    }
    &:nth-child(n + 2) {
      margin-left: 80px;
    }
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .message-content {
    margin-left: 10px;
    display: inline-block;
    border: 1px solid var(--border-input);
    padding: 0 10px;
    background-color: var(--bg-input);
    width: 760px;
    vertical-align: text-top;
    border-radius: 4px;
    span {
      color: var(--color-title);
    }
  }
  .option-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    i {
      color: var(--color-primary);
    }
    .not-copy {
      color: var(--color-btn-primary-disabled);
      cursor: not-allowed;
    }
  }
  @{_deep} .ivu-collapse {
    background: transparent;
    border: none;
    .ivu-collapse-item {
      border: none;
      .ivu-collapse-header {
        border: none;
        color: var(--color-navigation-title);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        background-color: var(--bg-collapse-item);
        height: 50px;
        line-height: 50px;
        margin: 0 20px;
        .title {
          font-size: 16px;
          font-weight: 900;
        }
        .switch {
          top: 50%;
          transform: translateY(-50%);
          margin-right: 20px;
        }
        i {
          float: right;
          color: #fff;
          line-height: 50px;
          font-size: 20px;
        }
      }
      .ivu-collapse-content {
        padding: 0;
        background: var(--bg-content);
        .ivu-collapse-content-box {
          padding: 10px 40px;
        }
      }
      &.ivu-collapse-item-active {
        background: var(--bg-content);
      }
    }
  }
}
</style>
