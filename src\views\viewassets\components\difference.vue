<template>
  <ui-modal
    class="difference"
    v-model="visible"
    :title="modalTitle"
    :styles="styles"
    :footer-hide="footerHide"
    @onCancel="handleCancel"
  >
    <div class="filter over-flow">
      <slot name="filter"></slot>
    </div>
    <comparison
      ref="comparison"
      :field-list="fieldList"
      :only-read="onlyRead"
      :check-fun="checkFun"
      :save-fun="saveFun"
      :sign="sign"
    >
      <template #contentTitle>
        <slot name="contentTitle"></slot>
      </template>
      <template #increaseTitle>
        <slot name="increaseTitle"></slot>
      </template>
      <template #content="{ row }">
        <slot name="content" :row="row"></slot>
      </template>
      <template #increaseContent="{ row }">
        <slot name="increaseContent" :row="row"></slot>
      </template>
    </comparison>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    onlyRead: {
      type: Boolean,
      default: false,
    },
    fieldList: {
      type: Array,
      default: () => [],
    },
    checkFun: {
      type: Function,
      default: () => {},
    },
    saveFun: {
      type: Function,
      default: () => {},
    },
    modalTitle: {
      type: String,
      default: '差异详情',
    },
    footerHide: {
      type: Boolean,
      default: true,
    },
    sign: {},
  },
  data() {
    return {
      styles: {
        width: '70%',
      },
      visible: false,
    };
  },
  created() {},
  methods: {
    handleCancel() {
      this.$refs.comparison.isEdit = false;
      this.$emit('onCancel');
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    Comparison: require('@/views/viewassets/components/comparison.vue').default,
  },
};
</script>
<style lang="less" scoped>
.difference {
  @{_deep} .ivu-modal-body {
    padding: 0;
  }
  .filter {
    border-top: 1px solid var(--border-modal-footer);
    padding: 20px;
  }
}
</style>
