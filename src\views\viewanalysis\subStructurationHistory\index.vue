<template>
  <div class="container">
    <!-- 查询 -->
    <Search ref="search" :subTaskType="subTaskType" @searchForm="searchForm" />
    <div class="table-container">
      <div class="data-above">
        <Button size="small" @click="handleAdd">
          <ui-icon type="jia" color="#2C86F8"></ui-icon>
          新增任务
        </Button>
        <Button class="mr" @click="handleSort" size="small">
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          创建时间排序
        </Button>
        <Button size="small" @click="handleDelJobs">
          <ui-icon type="shanchu1" color="#2C86F8"></ui-icon>
          批量删除
        </Button>
      </div>
      <div class="table-content">
        <ui-table
          :columns="columns"
          :data="list"
          :loading="loading"
          @on-expand="handleExpand"
          @on-select="onSelect"
          @on-select-cancel="onSelectCancel"
          @on-select-all="onSelectAll"
          @on-select-all-cancel="onSelectAllCancel"
        >
          <template #videoStartTime="{ row }">
            <div>
              {{ row.videoStartTime | timeFormat }} -
              {{ row.videoEndTime | timeFormat }}
            </div>
          </template>
          <template #totalTask="{ row }">
            <div>总数：{{ row.totalTask }}</div>
          </template>
          <template #progress="{}">
            <div>--</div>
          </template>
          <template #processTime="{}">
            <div>--</div>
          </template>
          <template #createTime="{ row }">
            <div>{{ row.createTime | timeFormat }}</div>
          </template>
          <template #applyStatus="{ row }">
            <div>
              {{
                row.applyStatus == 1
                  ? "通过"
                  : row.applyStatus == 2
                  ? "驳回"
                  : "未审核"
              }}
            </div>
          </template>
          <template #opreate="{ row }">
            <div class="opreate">
              <div class="tools">
                <Poptip trigger="hover" placement="left-start">
                  <i class="iconfont icon-gengduo"></i>
                  <div class="mark-poptip" slot="content">
                    <p @click="handleEdit(row)">
                      <i class="iconfont icon-bianji"></i>编辑
                    </p>
                    <p @click="handleSearch(row)">
                      <i class="iconfont icon-gaojisousuo"></i>资源检索
                    </p>
                    <p @click="mapLoaction(row)">
                      <i class="iconfont icon-dongtai-shangtudaohang"></i
                      >地图定位
                    </p>
                    <p @click="handleDel(row)">
                      <i class="iconfont icon-shanchu1"></i>删除
                    </p>
                  </div>
                </Poptip>
              </div>
            </div>
          </template>
        </ui-table>
      </div>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <!--添加/编辑 start-->
    <addModal
      v-model="isShowAdd"
      :subTaskType="subTaskType"
      :subTaskId="subTaskId"
      :subDeviceId="subDeviceId"
      @updated="jobUpdated"
    ></addModal>
    <adjustPosition
      v-model="isShowDragDialog"
      :pointsData="pointData"
      :noEdit="true"
    ></adjustPosition>
  </div>
</template>
<script>
import { mapActions } from "vuex";
import Search from "../components/search.vue";
import expandRow from "@/components/ui-table-expandRow/expandRow.vue";
import addModal from "../components/add-modal.vue";
import {
  getStructureJobList,
  getStructureRealTaskList,
  delTask,
  delJob,
} from "@/api/viewAnalysis";
import adjustPosition from "../components/adjustPosition.vue";
export default {
  name: "subStructurationHistory",
  components: {
    Search,
    expandRow,
    addModal,
    adjustPosition,
  },
  props: {},
  data() {
    return {
      subTaskType: "his",
      list: [],
      childList: [],
      loading: false,
      pageForm: {},
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
        structureJobType: 2,
        sort: "desc",
      },
      timeUpDown: false,
      columns: [
        {
          type: "expand",
          width: 50,
          render: (h, params) => {
            return h(expandRow, {
              props: {
                taskList: this.childList[params.index],
                columns: this.childColumns,
                currentJob: this.list[params.index],
                subTaskType: this.subTaskType,
              },
              on: {
                handleEdit: (val) => {
                  this.subTaskId = val.structureJobId;
                  this.subDeviceId = val.deviceGbId;
                  this.isShowAdd = true;
                },
                handleSearch: (val) => {
                  this.toDetailByTask(val);
                },
                handleMap: (val) => {
                  this.childMapLocation(val);
                },
                handleDel: (val, currentJob) => {
                  this.deleteTasks([val], currentJob);
                },
              },
            });
          },
        },
        { type: "selection", align: "center", width: 60 },
        { title: "任务名称", key: "name", width: 240 },
        { title: "录像时长", slot: "videoStartTime", minWidth: 250 },
        { title: "解析类型", slot: "totalTask", minWidth: 320 },
        { title: "处理进度", slot: "progress", minWidth: 150 },
        { title: "处理时长", slot: "processTime" },
        { title: "创建时间", slot: "createTime", minWidth: 40 },
        { title: "创建人", key: "creatorName" },
        { title: "审核状态", slot: "applyStatus" },
        { title: "操作", slot: "opreate", width: 100 },
      ],
      childColumns: [
        { title: "任务名称", slot: "name", width: 315, align: "center" },
        { title: "录像时长", slot: "videoTime", minWidth: 250 },
        { title: "状态", slot: "vehicle", minWidth: 320 },
        { title: "处理进度", slot: "progress", minWidth: 150 },
        { title: "处理时长", slot: "processTime", width: 200 },
        { title: "创建时间" },
        { title: "创建人" },
        { title: "操作", slot: "opreate", width: 120 },
      ],
      isShowAdd: false,
      subTaskId: "", //添加编辑页面的任务ID
      subDeviceId: "",
      selectedData: [],
      pointData: [], //用于地图上撒点的 点位数据
      isShowDragDialog: false,
      timer: null,
    };
  },
  created() {
    this.getDictStructData();
    this.getList();
    this.timer = setInterval(() => {
      let expandList = this.list.filter((v) => v._expanded);
      expandList.forEach((v) => {
        this.handleExpand(v, true);
      });
    }, 15000);
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    ...mapActions({
      getDictStructData: "dictionary/getDictStructData",
    }),
    // 排序
    handleSort() {
      this.timeUpDown = !this.timeUpDown;
      this.params.sort = this.timeUpDown ? "asc" : "desc";
      this.getList();
    },
    // 查询列表
    getList() {
      var param = Object.assign(this.pageForm, this.params, {
        pageNumber: this.params.pageNumber - 1,
      });
      getStructureJobList(param).then((res) => {
        const { list, total } = res.data;
        this.list = list || [];
        this.total = total;
        this.selectedData = [];
      });
    },
    jobUpdated() {
      this.getList();
    },
    // 查询
    searchForm(form) {
      this.pageForm = JSON.parse(JSON.stringify(form));
      this.params.pageNumber = 1;
      this.getList();
    },
    // 改变页码
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.getList();
    },
    // 改变分页个数
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.getList();
    },
    handleExpand(row, status) {
      let index = this.list.findIndex((v) => v.jobId == row.jobId);
      if (status) {
        this.list[index]._expanded = true;
        getStructureRealTaskList({
          structureJobId: row.jobId,
          structureJobType: this.params.structureJobType,
          sort: this.params.sort,
          pageNumber: 0,
          pageSize: 999,
        }).then((res) => {
          const { list } = res.data;
          this.$set(this.childList, index, list || []);
          if (!list.length) this.getList();
        });
      } else {
        this.list[index]._expanded = false;
      }
    },
    handleAdd() {
      this.subTaskId = "";
      this.subDeviceId = "";
      this.isShowAdd = true;
    },
    handleEdit(row) {
      this.subTaskId = row.jobId;
      this.subDeviceId = "";
      this.isShowAdd = true;
    },
    handleSearch(row) {
      this.toDetailByJob(row);
    },
    //定位--父列表 定位地图
    mapLoaction(item) {
      var arrayP = [];
      item.devices &&
        item.devices.length > 0 &&
        item.devices.filter((item) => {
          if (
            item.longitude &&
            parseInt(item.longitude) != 0 &&
            item.latitude &&
            parseInt(item.latitude) != 0
          ) {
            item.data = {
              deviceName: !!item["name"] ? item.name : null,
            };
            arrayP.push(item);
          }
        });
      if (arrayP.length < 1) {
        this.$Message.error("该任务无点位信息，无法定位");
        return;
      }
      this.pointData = arrayP;
      this.isShowDragDialog = true;
    },
    // 子列表 定位地图
    childMapLocation(item) {
      if (
        item.longitude &&
        parseInt(item.longitude) != 0 &&
        item.latitude &&
        parseInt(item.latitude) != 0
      ) {
        item.data = {
          deviceName: !!item["name"] ? item.name : null,
        };
        this.pointData = [item];
        this.isShowDragDialog = true;
      } else {
        this.$Message.error("该视频文件无点位信息，无法定位");
      }
    },
    onSelect(selection) {
      this.selectedData = selection;
    },
    onSelectCancel(selection) {
      this.selectedData = selection;
    },
    onSelectAll(selection) {
      this.selectedData = selection;
    },
    onSelectAllCancel() {
      this.selectedData = [];
    },
    handleDel(row) {
      this.deleteJobs([row]);
    },
    handleDelJobs() {
      this.deleteJobs(this.selectedData);
    },
    toDetailByJob(row) {
      const { href } = this.$router.resolve({
        name: "viewParsingLibrary",
        query: {
          noMenu: 1,
          type: 2,
          jobId: row.jobId,
          startDate: row.videoStartTime,
          endDate: row.videoEndTime,
        },
      });
      window.open(href, "_blank");
    },
    toDetailByTask(row) {
      const { href } = this.$router.resolve({
        name: "viewParsingLibrary",
        query: {
          noMenu: 1,
          type: 2,
          taskId: JSON.stringify(
            row.id.split(",").map((v) => {
              return { id: v, name: row.name };
            })
          ),
          startDate: row.videoStartTime,
          endDate: row.videoEndTime,
        },
      });
      window.open(href, "_blank");
    },
    //删除---删除任务&批量删除任务
    deleteJobs(data) {
      if (!data || !data.length) {
        this.$Message.warning("当前未选中任务");
        return;
      }
      const ids = data.map((item) => item.jobId).join(",");
      const names = data.map((item) => item.name).join(",");
      if (ids === "") {
        this.$Message.warning("当前未选中任务");
        return;
      }
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `您确认删除【${names}】历史结构化任务吗?`,
        onOk: () => {
          delJob(ids).then((res) => {
            this.queryLog({
              muen: "视图解析",
              name: "历史结构化",
              type: "3",
              remark: `删除【${names}】历史结构化任务。`,
            });
            res
              ? this.$Message.success("任务删除成功")
              : this.$Message.success("任务删除失败");
            this.getList();
          });
        },
      });
    },
    //批量删除子任务
    deleteTasks(files, currentJob) {
      if (!files || !files.length) {
        this.$Message.warning("当前未选中任务");
        return;
      }
      var _this = this;
      var arr_fileId = files.map(function (item) {
        return item.id;
      });
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `您确认删除【${files[0].name}】历史结构化任务吗?`,
        onOk: () => {
          delTask(arr_fileId.join(",")).then((res) => {
            this.queryLog({
              muen: "视图解析",
              name: "历史结构化",
              type: "3",
              remark: `删除【${currentJob.name}】历史结构化任务的【${files[0].name}】摄像机。`,
            });
            res
              ? _this.$Message.success("任务删除成功")
              : _this.$Message.success("任务删除失败");
            _this.handleExpand(currentJob, true);
          });
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  position: relative;
  .table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .data-above {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      button {
        margin-right: 10px;
      }
    }
    .table-content {
      flex: 1;
      .ui-table {
        height: 100%;
      }
    }
    .opreate {
      display: flex;
    }
    .tools {
      color: #2c86f8;
      width: 20px;
      height: 20px;
      text-align: center;
      line-height: 20px;
      margin-left: 5px;
      .icon-gengduo {
        transform: rotate(90deg);
        transition: 0.1s;
        display: inline-block;
      }
      p:hover {
        color: #2c86f8;
      }
      &:hover {
        background: #2c86f8;
        color: #fff;

        .icon-gengduo {
          transform: rotate(0deg);
          transition: 0.1s;
        }
        border-radius: 10px;
      }
      /deep/ .ivu-poptip-popper {
        min-width: 150px !important;
        width: 40px !important;
        height: auto;
      }
      /deep/.ivu-poptip-body {
        height: auto !important;
      }
      .mark-poptip {
        color: #000;
        cursor: pointer;
        text-align: left;
        /deep/ .ivu-icon-ios-add-circle-outline {
          font-weight: 600;
        }
      }
    }
    /deep/td.ivu-table-expanded-cell {
      padding: 0 !important;
    }
  }
}
</style>
