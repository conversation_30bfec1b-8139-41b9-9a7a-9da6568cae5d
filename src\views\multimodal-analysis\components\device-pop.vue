<template>
  <div class="device-poptip">
    <Poptip trigger="hover" placement="right-start">
      <div class="primary">
        {{ deviceList?.length || 0 }}
      </div>
      <div class="device-list" slot="content">
        <div
          class="device-item"
          v-for="(item, index) in deviceList"
          :title="item?.deviceInfo?.name || '--'"
          :key="index"
        >
          <i
            class="iconfont"
            :class="getDeviceIcon(item)"
            :style="{
              color: getDeviceIconColor(item),
            }"
          ></i
          >{{ item?.deviceInfo?.name || "--" }}
        </div>
      </div>
    </Poptip>
  </div>
</template>

<script>
export default {
  name: "DevicePop",
  props: {
    deviceList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  methods: {
    getDeviceIcon(item) {
      const { deviceInfo } = item;
      const param = {
        deviceType: deviceInfo?.type || "1",
        deviceChildType: deviceInfo?.workFunction || "2",
      };
      return Toolkits.getDeviceIconType(param);
    },
    getDeviceIconColor(item) {
      const { deviceInfo } = item;
      const param = {
        deviceType: deviceInfo?.type || "1",
        deviceChildType: deviceInfo?.workFunction || "2",
      };
      return Toolkits.getDeviceIconColor(param);
    },
  },
};
</script>

<style lang="less" scoped>
.device-poptip {
  /deep/.ivu-poptip-popper {
    width: auto !important;
    min-width: unset;
    .ivu-poptip-body {
      max-height: 300px;
      padding: 10px 16px;
    }
  }
  .device-item {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.8);
    padding: 6px 4px;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    i {
      margin-right: 10px;
    }
  }
}
</style>
