<template>
  <!-- 右下二（视图数据检测） -->
  <div class="view-detection" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shitushujujiance" v-model="activeValue" :data="tabData">
      <template #filter>
        <view-data-select v-model="viewData" @change="onChange"></view-data-select>
      </template>
    </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
      <draw-echarts
        :echart-option="detectionEchart"
        ref="attributeChart"
        class="echart"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
    </div>
  </div>
</template>
<style lang="less" scoped>
.view-detection {
  position: absolute;
  bottom: 10px;
  right: 25.5%;
  width: 24.5%;
  height: 33%;
  margin-left: 10px;
  margin-right: 10px;
  background: rgba(0, 104, 183, 0.13);
  z-index: 2;
  .detection-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }
  .echarts-box {
    width: 100% !important;
    height: calc(100% - 42px) !important;
    .echart {
      height: 100% !important;
      display: block !important;
    }
  }
}
.full-screen-container {
  height: 30.6%;
}
</style>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'view-detection',
  props: {
    queryAccessDataCount: {
      default: () => {},
    },
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      detectionEchart: {},
      echartsLoading: false,
      echartData: [],
      viewData: '2',
      activeValue: 'view',
      tabData: [{ label: '视图数据检测', id: 'view' }],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
    }),
  },
  mounted() {},

  methods: {
    async onChange() {
      await this.getSyntheticStatistics();
      await this.initRing();
    },
    getCountById() {
      if (this.viewData === '2') {
        this.echartData.accessDataCount = this.queryAccessDataCount.faceViewAmount || 0;
      } else if (this.viewData === '3') {
        this.echartData.accessDataCount = this.queryAccessDataCount.vehicleViewAmount || 0;
      } else if (this.viewData === '5') {
        this.echartData.accessDataCount = this.queryAccessDataCount.zdrTrackAmount || 0;
      }
    },
    async getSyntheticStatistics() {
      try {
        this.echartsLoading = true;
        let params = {
          indexModule: this.viewData,
        };
        let {
          data: { data },
        } = await this.$http.get(home.queryViewDataCount, { params });
        this.echartData = data;
        this.getCountById();
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    initRing() {
      this.barData = this.echartData.list.map((item) => item.count);
      let count = {
        accessDataCount: this.echartData.accessDataCount || 0,
        checkCount: this.echartData.checkCount || 0,
      };
      let opts = {
        xAxis: this.echartData.list.map((row) => row.desc),
        data: this.barData,
        targetDataTypeDesc: this.getTitle(this.viewData),
        count,
      };
      this.detectionEchart = this.$util.doEcharts.detectionColumn(opts);
    },
    getTitle(key) {
      if (key === '2') {
        return '人脸视图数据';
      } else if (key === '3') {
        return '车辆视图数据';
      } else if (key === '5') {
        return 'ZDR视图数据';
      }
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    ViewDataSelect: require('./view-data-select').default,
  },
  watch: {
    queryAccessDataCount: {
      deep: true,
      immediate: true,
      handler: async function () {
        await this.getSyntheticStatistics();
        await this.initRing();
      },
    },
  },
};
</script>
