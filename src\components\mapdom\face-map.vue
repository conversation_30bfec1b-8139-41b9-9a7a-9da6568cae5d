<template>
  <div class="face-info">
    <div class="face-info-left" style="width: 200px">
      <div class="smallImg" style="width: 200px; height: inherit">
        <img :src="faceInfo.traitImg" alt="" />
        <!-- <span class="similarity" v-if="faceInfo.score"
          >{{ faceInfo.score }}%</span
        > -->
      </div>
      <div class="title">
        <span :class="{ active: checkIndex == 0 }" @click="tabsChange(0)"
          >抓拍记录</span
        >
        <!-- 没有vid不展示档案 -->
        <span
          v-show="faceInfo.vid"
          :class="{ active: checkIndex == 1 }"
          @click="tabsChange(1)"
          >人员档案</span
        >
      </div>
      <div class="traffic-record" v-if="checkStatus">
        <div class="dom-content-p">
          <span class="label">抓拍地点</span><span>：</span>
          <!-- <span class="message" v-show-tips>{{ faceInfos.deviceName }}</span> -->
          <ui-textOver-tips
            class="message"
            refName="deviceName"
            :content="faceInfos.deviceName || faceInfos.captureAddress"
          ></ui-textOver-tips>
        </div>
        <div class="dom-content-p">
          <span class="label">抓拍时间</span><span>：</span>
          <span class="message">{{
            faceInfo.absTime || faceInfo.captureTime || "--"
          }}</span>
        </div>
        <structuredmsg :info="faceInfo" type="1"></structuredmsg>
      </div>
      <div class="personnel-files" v-else>
        <div class="dom-content-p">
          <span class="label">视频身份</span><span>：</span>
          <span
            class="address"
            :class="{ identity: faceInfo.vid }"
            @click="toDetail(faceInfo)"
            >{{ faceInfo.vid || "--" }}</span
          >
        </div>
        <div class="dom-content-p">
          <span class="label">姓名</span><span>：</span>
          <span class="message">{{ faceArchives.xm || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">联系方式</span><span>：</span>
          <span class="message">{{ faceArchives.lxdh || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">身份证号</span><span>：</span>
          <span class="address">{{ faceArchives.gmsfhm || "--" }}</span>
        </div>
        <div class="dom-content-p">
          <span class="label">家庭住址</span><span>：</span>
          <span class="address">{{ faceArchives.xzz_mlxxdz || "--" }}</span>
        </div>
      </div>
    </div>
    <div class="face-info-right common-info-right">
      <!-- <ui-image :src="faceInfo.sceneImg" alt="静态库" viewer /> -->
      <div class="fun-img" id="fun-img">
        <img
          id="imgBox"
          :src="faceInfo.sceneImg"
          @load="loadImage($event, faceInfo.rect)"
          alt=""
        />
        <div
          class="select-preview"
          :style="{
            left: imgBoxList.x + 'px',
            top: imgBoxList.y + 'px',
            width: imgBoxList.width + 'px',
            height: imgBoxList.height + 'px',
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getPersonInfoByPersonId } from "@/api/operationsOnTheMap";
import structuredmsg from "./structuredmsg.vue";
import { myMixins } from "@/views/operations-on-the-map/map-default-page/components/mixins/index.js";
export default {
  name: "",
  components: {
    structuredmsg,
  },
  mixins: [myMixins], //全局的mixin
  props: {
    // 抓拍记录数据
    faceInfo: {
      type: Object,
      default: () => {},
    },
    // 抓拍记录，人员档案tab切换
    // checkStatus: {
    //     type: Boolean,
    //     default: true
    // }
  },
  data() {
    return {
      faceArchives: {},
      checkStatus: true, // 抓拍记录，人员档案tab切换
      checkIndex: 0,
    };
  },
  watch: {},
  computed: {
    faceInfos() {
      return this.faceInfo;
    },
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.checkStatus = true;
      this.checkIndex = 0;
      this.faceArchives = {};
    },
    async tabsChange(index) {
      if (this.checkIndex == index) {
        return;
      }
      this.checkIndex = index;
      this.checkStatus = this.checkIndex == 0 ? true : false;
      if (!this.checkStatus) {
        try {
          if (!this.faceInfo.vid) {
            this.$Message.warning("档案不存在！");
            return;
          }
          let res = await getPersonInfoByPersonId({ vid: this.faceInfo.vid });
          if (res.code === 200) {
            this.faceArchives = res.data || {};
          }
        } catch (error) {
          this.$Message.warning("档案暂无数据");
        }
      }
    },
    toDetail(item) {
      if (!item.vid) {
        return;
      }
      const { href } = this.$router.resolve({
        name: "video-archive",
        query: {
          archiveNo: item.vid,
          source: "video",
          initialArchiveNo: item.vid,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";
.face-info {
  display: flex;
  margin-top: 10px;
  margin-bottom: 10px;
  &-left {
    position: relative;
    width: 200px;
    height: 200px;
    flex: 1;
    .smallImg {
      border: 1px solid #d3d7de;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
      background: #f9f9f9;
      position: relative;
      > img {
        // width: 200px;
        // height: 200px;
        max-width: 100%;
        max-height: 100%;
      }
    }
    .title {
      margin-top: 10px;
    }
    .traffic-record {
      margin-top: 10px;
    }
    .personnel-files {
      margin-top: 10px;
      .complete-file {
        height: 18px;
        line-height: 18px;
        margin: 15px 0 17px 0;
        cursor: pointer;
        display: flex;
        align-items: center;
        > span {
          margin-left: 5px;
          font-size: 12px;
        }
      }
    }
  }
  &-right {
    width: calc(~"100% - 220px");
    min-height: 360px;
    max-height: 400px;
    border: 1px solid #d3d7de;
    background: #f9f9f9;
    margin-left: 20px;
    position: relative;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .tablist {
        height: 28px;
        line-height: 28px;
        width: 400px !important;
        margin: 0;

        .ivu-tabs-tab {
          float: left;
          border: 1px solid #2c86f8;
          border-right: none;
          padding: 0 15px;
          color: #2c86f8;
          &:hover {
            background: #2c86f8;
            color: #ffffff;
          }
          &:first-child {
            border-top-left-radius: 4px;
            border-bottom-left-radius: 4px;
            border-right: none;
          }
          &:last-child {
            border-top-right-radius: 4px;
            border-bottom-right-radius: 4px;
            border-right: 1px solid #2c86f8;
          }
        }
        .active {
          background: #2c86f8;
          color: #fff;
        }
      }
    }
    .right-content {
      margin-top: 6px;
      max-width: 580px;
      height: 370px;
      .complete-face {
        width: 580px;
        height: 430px;
        position: relative;
        text-align: center;
        overflow: hidden;
        display: flex;
        justify-content: center;
        align-items: center;
        > img {
          // width: 100%;
          // height: 100%;
        }
        > span {
          display: inline-block;
          width: 100%;
          height: 30px;
          line-height: 30px;
          position: absolute;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);

          .iconfont {
            color: #fff !important;
            padding: 0 12px;
            cursor: pointer;
          }
        }
      }
      .video {
        /deep/.easy-player {
          margin-top: 60px;
        }
      }
    }
  }
}
</style>
