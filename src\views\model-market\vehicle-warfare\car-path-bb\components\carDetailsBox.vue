<!--
    * @FileDescription: 行车轨迹=>列表
    * @Author: H
    * @Date: 2023/09/18
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="detailsBox">
        <div class="details-list" v-show="detailsList">
            <div class="box-hint">
                <Icon type="ios-undo" @click="handleback" />
                <span @click="handleback">行车轨迹 > "{{ detailsParams.plateNo }}"结果</span>
            </div>
            <div class="serach-hint">
                <div class="left-hint">
                    <p class="hint_day">共<span class="count_number">{{ dayNum }}</span>天</p>
                    <span class="timerange">{{ startDateTime }}--{{ endDateTime }}</span>
                </div>  
                <!-- <Button icon="md-open" size="small">导出</Button> -->
            </div>
            <!-- 每日时间列表 -->
            <div class="table-box">
                <ul class="vehicle-ul">
                    <li class="vehicle-ul-li" 
                        v-for="(item, index) in vehicleList"
                        :key="index"  
                        @click="handleDetails(item, index)">
                        <p class="select-icon" @click="handleCheched($event, item, index)" :class="{'checked-icon': index === vehileIndex}">
                            <Icon type="md-checkmark-circle" class="checkmark-circle" />
                        </p>
                        <p class="line-style default">
                            <span class="line"></span>
                            <span class="circle"></span>
                            <span class="line"></span>
                        </p>
                        <p class="time-style">
                        <i class="iconfont icon-time"></i> 
                        <span>{{ item.dateStr }}</span>
                        </p>
                        <p class="count">{{ item.count }}次</p>
                        <p class="arrows">
                            <i class="iconfont icon-jiantou"></i> 
                        </p>
                    </li>
                    <ui-empty v-if="vehicleList.length == 0 && !loading"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                </ul>
                <!-- <Page size="small" :total="topTotal" :page-size="topPage.pageSize" transfer class="page" @on-change="handleTopPage" @on-page-size-change="handleTopPageSize" /> -->
            </div>
        </div>
        <div class="details-list" v-show="!detailsList">
            <div class="box-hint">
                <Icon type="ios-undo" @click="handlebackdetails" />
                <span @click="handlebackdetails">行车轨迹 > "{{ detailsParams.plateNo }}"结果</span>
            </div>
            <div class="serach-hint">
                <p class="hint_day">共<span class="count_number">{{  bottomTotal }}</span>条结果</p>
            </div>
            <!-- 列表详情 -->
            <div class="table-box">
                <ul class="path_ul">
                    <li class="path_ul_li"
                        :class="{'path_active': pathIndex == index}" 
                        v-for="(item, index) in dataList"
                        :key="index"
                        @click="handlePath(item, index)">
                        <div class="path_number">{{ index+1 }}</div>
                        <div class="path_right">
                            <div class="path_title">
                                <i class="iconfont icon-location"></i>
                                <ui-textOver-tips class="message" refName="deviceName" :content="item.deviceName"></ui-textOver-tips>
                                <!-- <span>{{ item.deviceName }}</span> -->
                            </div>
                            <div class="path_time">
                                <i class="iconfont icon-time"></i> 
                                <span>{{ item.absTime }}</span>
                            </div>
                        </div>
                    </li>
                    <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
                    <ui-loading v-if="loading"></ui-loading>
                </ul>
                <Page size="small" :total="bottomTotal" :page-size="bottomPage.pageSize" transfer class="page" @on-change="handleBottomPage" @on-page-size-change="handleBottomPageSize" />
            </div>
        </div>
    </div>
</template>

<script>
import { vehicleTrajectoryCount, queryVehicleTrajectory } from '@/api/modelMarket';
import { getDaysBetween } from '@/util/modules/common';
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            topTotal: 0,
            topPageSize: 13,
            detailsList: true,
            bottomTotal:0,
            topPage: {
                pageNumber: 1,
                pageSize: 13,
            },
            vehicleList: [],
            vehileIndex: '',
            bottomPage: {
                pageNumber: 1,
                pageSize: 10, 
            },
            dataList: [],
            pathIndex: 0,
            loading: false,
            detailsParams: { },
            startDateTime: 0,
            endDateTime: 0,
            dayNum: 0,
            pathRow: {}
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        handleList(param) {
            this.vehicleList = [];
            this.detailsParams = param;
            this.endDateTime = this.detailsParams.endDate.split(' ')[0];
            this.startDateTime = this.detailsParams.startDate.split(' ')[0];
            this.dayNum = getDaysBetween(this.startDateTime, this.endDateTime )
            this.detailsBox = false;
            this.loading = true;
            vehicleTrajectoryCount(param)
            .then(res => {
                this.vehicleList = res.data;
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        //详情列表
        handleDetails(row) {
            this.detailsList = false;
            this.pathRow = row;
            this.bottomTotal = 0;
            this.queryList();
        },
        queryList(){
            let startDate = '', endDate = '';
            if(this.pathRow.dateStr == this.endDateTime) {
                endDate = this.detailsParams.endDate;
            } else {
                endDate = this.pathRow.dateStr + ' 23:59:59';
            }
            if(this.pathRow.dateStr == this.startDateTime) {
                startDate = this.detailsParams.startDate;
            } else {
                startDate =  this.pathRow.dateStr + ' 00:00:00';
            }
            this.dataList = [];
            this.loading = true;
            let params = {
                bodyColor: this.detailsParams.bodyColor,
                plateColor: this.detailsParams.plateColor,
                plateNo: this.detailsParams.plateNo,
                startDate: startDate,
                endDate: endDate,
                ...this.bottomPage
            }
            queryVehicleTrajectory(params)
            .then(res => {
                this.dataList = res.data.entities;
                this.bottomTotal = res.data.total;
                this.$emit('vehicleDataList', this.dataList)
            })
            .finally(() =>{
                this.loading = false;
            })
        },
        handleCheched(event, item, index = 0) {
            event.stopPropagation();
            this.vehileIndex = index;
            // this.$emit('polyLine', index)
        },
        handlePath(item, index){
            this.pathIndex = index;
            this.$emit('sprinkleDot', index)
        },
        handleback() {
            this.$emit('backSearch')
        },
        handlebackdetails() {
            this.detailsList = true;
            this.$emit('backSearchdetails')
        },
        handleTopPage(){

        },
        handleTopPageSize() {

        },
        handleBottomPage(page) {
            this.bottomPage.pageNumber = page;
            this.pathIndex = 0;
            this.$emit('backSearchdetails')
            this.queryList()
        },
        handleBottomPageSize() {

        }
    }
}
</script>

<style lang='less' scoped>
.detailsBox{
    position: absolute;
    top: 10px;
    left: 10px;
    .details-list{
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        filter: blur(0px);
        position: relative;
        .box-hint{
            width: 370px;
            height: 40px;
            background: #2c86f8;
            border-bottom: 1px solid #D3D7DE;
            color: #fff;
            font-size: 14px;
            line-height: 40px;
            padding-left: 14px;
            display: flex;
            align-items: center;
            .icon-jiantou{
                transform: rotate(90deg);
                display: inline-block;
                cursor: pointer;
            }
            .ivu-icon-ios-undo{
                font-size: 20px;
                cursor: pointer;
            }
            span{
                font-size: 14px;
                cursor: pointer;
                margin-left: 10px;
            }
        }
        .serach-hint{
            display: flex;
            font-size: 14px;
            justify-content: space-between;
            margin-top: 2px;
            padding: 0 2px 0 4px;
            .left-hint{
                flex: 1;
                display: flex;
                align-items: center;
            }
            .hint_day{
                font-size: 14px;
                color: #a5b0b6;
                display: inline-block;
                .count_number{
                    margin: 0 2px;
                    color: #38aef3;
                }
            }
            .timerange{
                font-size: 14px;
                margin-left: 5px;
                white-space: nowrap;
            }
        }
        .table-box{
            display: flex;
            flex-direction: column;
            padding-bottom: 10px;
            height: calc(~'100vh - 200px');
        }
        .vehicle-ul{
            padding-bottom: 20px;
            position: relative;
            min-height: 200px;
            .vehicle-ul-li{
                display: flex;
                align-items: center;
                border-bottom: 1px dashed #a5b0b64f;
                padding: 10px 20px;
                cursor: pointer;
                &:hover{
                    background: #EEEEEE;
                }
                .select-icon{
                    cursor: pointer;
                    /deep/.ivu-icon-md-checkmark-circle{
                        font-size: 22px;
                    }
                }
                .checked-icon{
                    color: #2c86f8;
                }
                .line-style{
                    margin-left: 10px;
                    padding: 0;
                    display: flex;
                    align-items: center;
                    .line{
                        background: #a5b0b6;
                        // #38eaf3
                        padding: 2px 12px;
                        border-radius: 3px;
                        display: inline-block;
                    }
                    .circle{
                        padding: 6px;
                        border-radius: 45px;
                        background: #a5b0b6;
                        display: inline-block;
                        margin: 0 2px;
                    }
                }
                .time-style{
                    margin-left: 10px;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    .icon-time{
                        font-size: 18px;
                        margin-right: 4px;
                    }
                }
                .count{
                    width: 55px;
                    margin-left: 10px;
                    font-size: 14px;
                }
                .arrows{
                    font-size: 20px;
                    transform: rotate(-90deg);
                    margin-left: 5px;
                }
            }
        }
        .path_ul{
            padding: 0 10px 20px 10px;
            margin-top: 10px;
            position: relative;
            min-height: 200px;
            height: 100%;
            .path_ul_li{
                // background: #38aef3;
                // color: #fff;
                font-size: 14px;
                display: flex;
                padding: 5px 10px;
                align-items: baseline;
                cursor: pointer;
                .path_number{
                    background: #f25051;
                    color: #fff;
                    width: 36px;
                    height: 20px;
                    text-align: center;
                    line-height: 20px;
                    border-radius: 4px;
                }
                .path_right{
                    margin-left: 10px;
                    .path_title{
                        display: flex;
                    }
                    .message{
                        width: 260px;
                    }
                }
                .path_time{
                    margin-top: 5px;
                }
            }
            .path_active{
                background: #38aef3;
                color: #fff;
                .path_number{
                    color: #38aef3;
                    background: #fff;
                }
            }
        }

    }
    .page{
        text-align: center;
        margin-top: 10px;
    }
}
</style>
