<template>
  <div>
    <ul class="index-map-tool">
      <template v-for="(item, index) in toolMap">
        <li
          :title="item.title"
          :class="{ active: toolSelectedIndex === index }"
          :key="index"
          v-if="loadTool(item) && !noNeedTool.includes(item.needTool)"
          @click="
            toolSelectedIndex = index;
            item.fun(item.funName);
          "
        >
          <img :src="item.icon" alt="" />
        </li>
      </template>
      <slot name="addTool"></slot>
    </ul>
  </div>
</template>
<script>
export default {
  name: "map-tool",
  props: {
    // 是否框选完成
    drawComplete: {
      type: Boolean,
      default: false,
    },
    // 不需要的工具
    noNeedTool: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    drawComplete: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.toolSelectedIndex = 0;
        }
      },
      immediate: true,
    },
  },

  data() {
    return {
      toolSelectedIndex: 0,
      mapTool: ["move", "rectangle", "circle", "polygon", "line", "clear"],
      toolMap: [
        {
          title: "移动",
          icon: require("@/assets/img/map/move.png"),
          fun: this.cancelDraw,
          value: "move",
          funName: "cancelDraw",
          needTool: "move",
        },
        {
          title: "矩形框选",
          icon: require("@/assets/img/map/rectangle.png"),
          fun: this.selectDraw,
          value: "rectangle",
          funName: "selectRectangle",
          needTool: "rectangle",
        },
        {
          title: "圆形框选",
          icon: require("@/assets/img/map/circle.png"),
          fun: this.selectDraw,
          value: "circle",
          funName: "selectDrawCircleByDiameter", //selectDrawCircleByDiameter 带直径， selectCircle 不带直径
          needTool: "circle",
        },
        {
          title: "多边形框选",
          icon: require("@/assets/img/map/polygon.png"),
          fun: this.selectDraw,
          value: "polygon",
          funName: "selectPolygon",
          needTool: "polygon",
        },
        {
          title: "线选",
          icon: require("@/assets/img/map/line.png"),
          fun: this.selectDraw,
          value: "line",
          funName: "selectLine",
          needTool: "line",
        },
        {
          title: "清除",
          icon: require("@/assets/img/map/clear.png"),
          fun: this.clearDraw,
          value: "clear",
          needTool: "clear",
        },
        {
          title: "关闭",
          icon: require("@/assets/img/map/close.png"),
          fun: this.closeMapTool,
          value: "clear",
          needTool: "close",
        },
      ],
    };
  },
  created() {},
  mounted() {},
  methods: {
    loadTool(item) {
      return this.mapTool.findIndex((row) => row === item.value) !== -1;
    },
    cancelDraw() {
      this.$emit("cancelDraw");
    },
    selectDraw(drawType) {
      this.$emit("selectDraw", drawType);
    },
    clearDraw() {
      this.$emit("clearDraw");
    },
    closeMapTool() {
      this.toolSelectedIndex = 0;
      this.$emit("closeMapTool");
    },
  },
  computed: {},
  components: {},
};
</script>
<style lang="less" scoped>
.index-map-tool {
  position: absolute;
  bottom: 22px;
  left: 50%;
  margin-left: -115px;
  z-index: 7;
  height: 45px;
  li {
    float: left;
    width: 45px;
    height: 100%;
    text-align: center;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.7);
    &:hover {
      background: rgba(0, 0, 0, 0.49);
    }
    i {
      background-repeat: no-repeat;
      display: block;
      width: 24px;
      height: 24px;
    }
    > img {
      opacity: 0.6;
    }
  }
  .active {
    background: rgba(0, 0, 0, 0.49);
    > img {
      opacity: 1;
    }
  }
}
</style>
