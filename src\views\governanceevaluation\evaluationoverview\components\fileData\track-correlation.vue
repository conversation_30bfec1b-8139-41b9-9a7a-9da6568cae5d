<template>
  <!-- 轨迹设备关联率 -->
  <div class="basic-information auto-fill">
    <div class="information-header">
      <statistics class="statistics" :statistics-list="statisticsList" :isflexfix="false"></statistics>
    </div>
    <div class="information-main auto-fill">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <TableList ref="infoList" :columns="columns" :loadData="loadDataList">
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <SearchList
            ref="searchList"
            style="margin: 0"
            @startSearch="startSearch"
            :selectList="[
              { id: 0, label: '轨迹关联设备' },
              { id: 1, label: '轨迹设备未关联设备资产库' },
              { id: 2, label: '轨迹编码不存在' },
            ]"
          />
        </div>
        <!-- 表格操作 -->
        <template #trackImage="{ row }">
          <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
            <ui-image :src="row.trackImage" />
          </div>
        </template>
        <template #identityPhoto="{ row }">
          <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
            <ui-image :src="row.identityPhoto" alt="" />
          </div>
        </template>
        <template #isguan="{ row }">
          <span style="color: #c43d2c" v-if="row.synthesisResult === '1'">否</span>
          <span style="color: #19c176" v-else-if="row.synthesisResult === '0'">是</span>
          <span v-else>-</span>
        </template>
        <template #synthesisResult="{ row }">
          <span style="color: #19c176" v-if="row.synthesisResult === '1'">准确性合格</span>
          <span style="color: #c43d2c" v-else-if="row.synthesisResult === '0'">轨迹设备未关联设备资产库</span>
          <span style="color: #c43d2c" v-else-if="row.synthesisResult === '2'">轨迹设备编码不存在</span>
          <span v-else>-</span>
        </template>
        <template #similarity="{ row }">
          <span>{{ (row.similarity * 100).toFixed(2) }}<span v-if="row.similarity">%</span></span>
        </template>
        <template #algResult="{ row }">
          <span v-if="row.algResult">
            <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
              <span>{{ item.algorithmType }}:</span>
              <span>{{ item.score }}%</span>
            </div>
          </span>
          <span v-else>--</span>
        </template>
      </TableList>
    </div>
    <!-- 大图组件 ---------------------------------------------------------------------------------------------------- -->
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'track-correlation',
  components: {
    LookScene: require('@/components/look-scene').default,
    TableList: require('@/views/appraisaltask/inspectionrecord/components/tableList.vue').default,
    SearchList: require('@/components/track-detail-search.vue').default,
    statistics: require('@/components/icon-statistics').default,
  },
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      statisticalList: {},
      paramsList: {},
      bigPictureShow: false,
      imgList: [],
      searchData: {},
      statisticsList: [
        {
          name: 'ZDR人员总量',
          value: 0,
          icon: 'icon-ZRDzongliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'importPersonNumbr',
          textColor: 'color1',
        },
        {
          name: 'ZDR人像轨迹总量',
          value: 0,
          icon: 'icon-shishiguijishangchuanjishixingjiance',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'importPersonTrajectoryNumbr',
          textColor: 'color2',
        },
        {
          name: '检测轨迹数',
          value: 0,
          icon: 'icon-jianceguijishuliang',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'detectionAmount',
          textColor: 'color4',
        },
        {
          name: '关联设备轨迹数量',
          value: 0,
          icon: 'icon-guanlianshebeiguijishuliang',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'relatedAmount',
          textColor: 'color6',
        },
        {
          name: '未关联设备轨迹数量',
          value: 0,
          icon: 'icon-weiguanlianshebeiguijishuliang',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'number',
          key: 'unRelatedAmount',
          textColor: 'color5',
        },
        {
          name: '轨迹设备编码不存在',
          value: 0,
          icon: 'icon-guijishebeibianmabucunzai',
          iconColor: 'icon-bg10',
          liBg: 'li-bg10',
          type: 'number',
          key: 'noDevice',
          textColor: 'color10',
        },
        {
          name: '轨迹设备关联率',
          value: 0,
          icon: 'icon-guijishebeiguanlianxingjiance',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          qualified: true,
          key: 'relatedRate',
          textColor: 'color3',
        },
      ],
      columns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        {
          title: '抓拍图片',
          key: 'trackImage',
          slot: 'trackImage',
          tooltip: true,
          minWidth: 120,
        },
        { title: '抓拍时间', key: 'shotTime', tooltip: true, minWidth: 150 },
        { title: '抓拍点位', key: 'catchPlace', tooltip: true, minWidth: 150 },
        {
          title: '档案照',
          key: 'identityPhoto',
          slot: 'identityPhoto',
          minWidth: 120,
        },
        { title: '姓名', key: 'name', minWidth: 80 },
        { title: '证件号', key: 'idCard', minWidth: 150 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 200,
        },
        {
          title: '是否关联设备资产库',
          slot: 'isguan',
          width: 180,
          tooltip: true,
        },
        {
          title: '检测结果',
          key: 'synthesisResult',
          slot: 'synthesisResult',
          minWidth: 150,
        },
      ],
      loadDataList: (parameter) => {
        let data = {
          indexId: this.paramsList.indexId,
          regionCode: this.paramsList.regionCode,
          resultId: this.paramsList.resultId,
        };
        return this.$http
          .post(evaluationoverview.getDetailData, Object.assign(parameter, data, this.searchData))
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.regionCode) {
          this.paramsList = val;
          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    // 初始化
    async init() {
      this.searchData = {};
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 检索
    startSearch(searchData) {
      this.searchData = {};
      this.searchData = searchData;
      this.$refs.infoList.info(true);
    },
    //统计
    statistical() {
      this.statisticsList.map((val) => {
        if (val.key !== 'relatedRate') {
          val.value = this.statisticalList[val.key] || 0;
        }
        if (val.key === 'relatedRate') {
          let rate = this.statisticalList.relatedRate ? parseFloat(this.statisticalList.relatedRate) : '0.0';
          if (rate >= this.statisticalList.qualifiedRate) {
            val.qualified = true;
          } else {
            val.qualified = false;
          }
          val.value = rate + '%';
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.basic-information {
  .information-header {
    margin-top: 10px;
    height: 140px;
    display: flex;
    @{_deep}.information-statistics {
      width: 100%;
      height: 140px;
      padding: 20px;
      background: var(--bg-sub-content);
    }
    @{_deep}.information-statistics .statistics-ul li {
      width: 13.78% !important;
    }
  }
  .information-main {
    height: calc(100% - 140px);
    .abnormal-title {
      padding: 10px 0;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
  }
  .card {
    width: calc(calc(100% - 40px) / 4);
    margin: 0 5px 10px;
  }
  .ui-images {
    width: 56px;
    height: 56px;
    margin: 5px 0;
    .ui-image {
      min-height: 56px !important;
      /deep/ .ivu-spin-text {
        img {
          width: 56px;
          height: 56px;
          margin-top: 5px;
        }
      }
    }
  }
  /deep/.information-statistics .statistics-ul .icon-budabiao,
  .icon-dabiao {
    top: 70px !important;
  }
}
</style>
