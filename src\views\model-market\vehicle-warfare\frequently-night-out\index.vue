<!--
    * @FileDescription: 频繁夜出
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="frequently-night-out container">
        <!-- 地图 -->
        <mapCustom ref="mapBase" mapType="frequently-night-out" />
        <!-- 左面信息展示框 -->
        <leftBox ref="searchBar" :taskParams="taskParams" @search="handleSearch" v-show="searchBox"></leftBox>
        <detailsBox ref="detailBox" 
            @backSearch="handleBackSearch"
            @heatMap="setHeatMap"
            v-show="!searchBox"></detailsBox>
    </div>
</template>

<script>
import leftBox from './components/leftBox.vue';
import detailsBox from './components/detailsBox.vue';
import mapCustom from '../components/map/index.vue';
import { mapMutations } from 'vuex';
export default {
    name: 'frequently-night-out',
    components:{
        mapCustom,
        leftBox,
        detailsBox
    },
    props: {
        taskParams: {
            type: Object,
            default: () => ({})
        }
    },
    data () {
        return {
            searchBox: true,
        }
    },
    watch:{},
    computed:{ },
    activated() {},
    deactivated(){},
    async created() {
        if (Toolkits.isEmptyObject(this.taskParams)) {
            this.setLayoutNoPadding(true)
        }
    },
    destroyed() {
        this.setLayoutNoPadding(false)
    },
    mounted(){
            
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        handleSearch(params){
            console.log(params, 'params')
            this.searchBox = false;
            this.$nextTick(() => {
                this.$refs.detailBox.handleList(params)
            })
        },
        handleBackSearch(){
            this.searchBox = true;
            this.$refs.mapBase && this.$refs.mapBase.hideHeapMap()
        },
        setHeatMap(val) {
            this.$refs.mapBase && this.$refs.mapBase.renderHeatMap(val)
        }
    }
}
</script>

<style lang='less' scoped>
.frequently-night-out{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
