import { getAllReachablePaths } from "@/api/number-cube";
import { count } from "d3";
/*===========工具函数============*/
function isUseLabelEdge(item, edgeLabel) {
  if (!edgeLabel) return true;
  return item.ext.detail?.label === edgeLabel;
}
function isUseLabelNode(item, vertexLabel, selectNodeIds) {
  if (!vertexLabel || selectNodeIds.includes(item.id)) return true;
  return item?.group === vertexLabel;
}

function polyInterNodeEdge(edges) {
  const edgeMap = edges.reduce((cur, item) => {
    const { source, target } = item;
    const key = `${source}-${target}`;
    return cur[key] ? cur : { ...cur, [key]: [source, target] };
  }, {});
  return Object.keys(edgeMap).map((key) => edgeMap[key]); // Object.values(edgeMap);
}
// 获取两个节点之间路径上的所有的线
function getTwoNodeEdges2(edgeMaps, path) {
  const pathEdges = [];
  for (let i = 1; i < path.length; i++) {
    const source = path[i - 1];
    const target = path[i];
    const nodeIds1 = `${source}-${target}`;
    const nodeIds2 = `${target}-${source}`;
    const edgeIds = [
      ...(edgeMaps[nodeIds1] || []),
      ...(edgeMaps[nodeIds2] || []),
    ];
    if (edgeIds?.length > 0) pathEdges.push(...edgeIds);
  }
  return pathEdges;
}
// function filterLabelNodes(nodes,vertexLabel){
//   if(!vertexLabel) return nodes;
// }
function filterLabelEdges(
  edges,
  nodeLabelMaps,
  vertexLabel,
  edgeLabel,
  selectNodeIds
) {
  const labelEdges = [];
  for (let i = edges.length - 1; i >= 0; i--) {
    const edge = edges[i];
    const { source, target } = edge;
    const isEdgeLabel = isUseLabelEdge(edge, edgeLabel);
    const isSourceNodeLabel = isUseLabelNode(
      nodeLabelMaps?.[source] || {},
      vertexLabel,
      selectNodeIds
    );
    const isTargetNodeLabel = isUseLabelNode(
      nodeLabelMaps?.[target] || {},
      vertexLabel,
      selectNodeIds
    );
    if (isEdgeLabel && isSourceNodeLabel && isTargetNodeLabel)
      labelEdges.push(edge);
  }
  return labelEdges;
}
function getMinPathGroup(allPath) {
  let minRelation = 9999;
  let minPathArr = [];
  for (let i = allPath.length - 1; i >= 0; i--) {
    const path = allPath[i];
    const leg = path.length;
    if (leg < minRelation) {
      minPathArr = [path];
      minRelation = leg;
    } else if (leg === minRelation) {
      minPathArr.push(path);
    }
  }
  return minPathArr;
}
/*===========算法函数============*/
async function findG6AllPath(params, { nodes, edges }) {
  const { vertexLabel, edgeLabel, sourceId, targetId, type, maxDepth } = params;

  const nodeLabelMaps = nodes.reduce((cur, item) => {
    const { id } = item;
    return { ...cur, [id]: item };
  }, {});

  // const labelNodes = filterLabelNodes(nodes,vertexLabel);
  const labelEdges = filterLabelEdges(
    edges,
    nodeLabelMaps,
    vertexLabel,
    edgeLabel,
    [sourceId, targetId]
  );

  const relations = polyInterNodeEdge(labelEdges);
  if (relations.length === 0) {
    return {
      edges: [],
      nodes: [],
    };
  }
  const resp = await getAllReachablePaths({
    relations,
    maxDepth: maxDepth,
    start: sourceId,
    end: targetId,
  });
  let allPath = resp.data || [];

  if (allPath.length == 0) {
    return {
      edges: [],
      nodes: [],
    };
  }
  if (type === 2) {
    allPath = getMinPathGroup(allPath);
  }
  const start = performance.now();
  const edgeMaps = labelEdges.reduce((cur, item) => {
    const { source, target, id } = item;
    const key = `${source}-${target}`;
    cur[key] = [...(cur[key] || []), id];
    return cur;
  }, {});
  const allPathEdgeIds = [];
  const allPathNodeIds = [];
  for (let i = allPath.length - 1; i >= 0; i--) {
    const path = allPath[i];
    const pathMoreEdges = getTwoNodeEdges2(edgeMaps, path);
    allPathNodeIds.push(...path);
    allPathEdgeIds.push(...pathMoreEdges);
  }
  const end = performance.now();
  console.log(`算法执行时间：${end - start}ms`);

  return {
    edges: [...new Set(allPathEdgeIds)]?.map((id) => ({
      id,
    })),
    nodes: [...new Set(allPathNodeIds)]?.map((id) => ({ id })),
  };
}
// 查找单个实体的满足条件的所有关系【连接分析算法】
function getG6ConnectionPath(startNode, params, index, graphData) {
  index++;
  const { maxDepth, entityLabel, relationLabel } = params;
  const currentNodeIds = graphData.nodes.map((node) => node.getID());
  const currentEdgeIds = graphData.edges.map((edge) => edge.getID());
  const startNodeId = startNode.getID();
  // 获取与 node 关联的所有边
  const edges =
    startNode
      .getEdges()
      .filter(
        (edge) =>
          !currentEdgeIds.includes(edge.getID()) &&
          (!relationLabel || edge.getModel().ext.detail.label === relationLabel)
      ) || [];

  const nodes = edges.reduce((arr, edge) => {
    const sourceNode = edge.getSource();
    const targetNode = edge.getTarget();
    const node = sourceNode.getID() !== startNodeId ? sourceNode : targetNode;
    const flag =
      !currentNodeIds.includes(node.getID()) &&
      (!entityLabel || node.getModel().group === entityLabel);
    return flag ? [...arr, node] : arr;
  }, []);

  graphData.edges.push(...edges);
  graphData.nodes.push(...nodes);

  if (index < maxDepth && nodes.length > 0)
    nodes.forEach((node) => {
      getG6ConnectionPath(node, params, index, graphData);
    });
}

export { findG6AllPath, getG6ConnectionPath };
