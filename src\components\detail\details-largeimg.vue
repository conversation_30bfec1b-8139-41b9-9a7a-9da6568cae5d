<!--
    * @FileDescription: 详情右侧大图
    * @Author: H
    * @Date: 2023/5/23
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-05-09 15:17:45
-->
<template>
  <div class="drag-box" :class="[!overlay ? '' : 'overlay']">
    <div class="large-box" ref="largeBox">
      <div
        class="fun-img"
        ref="funImg"
        :style="{ transform: `scale(${rate})` }"
        @wheel="handleWheel"
        @click="handleClick"
        v-drag
      >
        <img
          :id="'imgBox' + id"
          class="largeImg"
          v-lazy="info[sceneImgKey]"
          @load="loadImage"
          alt=""
        />
        <div
          v-if="selectBox"
          class="select-preview"
          :style="{
            left: imgBoxList.x + 'px',
            top: imgBoxList.y + 'px',
            width: imgBoxList.width + 'px',
            height: imgBoxList.height + 'px',
          }"
        ></div>
        <div v-else>
          <div
            class="select-preview click-preview"
            v-for="(item, index) in boxStyleList"
            :key="index"
            :style="{
              left: item.x + 'px',
              top: item.y + 'px',
              width: item.width + 'px',
              height: item.height + 'px',
              borderColor: item.color,
              backgroundColor: getOpacityColor(item.color, 0.1),
              zIndex: item.zindex,
            }"
            @dblclick="handleBoxSelect($event, index, item.type)"
          ></div>
        </div>
      </div>
      <ui-loading v-if="loading"></ui-loading>
    </div>
    <slot name="description"></slot>
    <!-- 放大 -->
    <div class="screen-box" v-if="overlay">
      <div class="closeIcons"></div>
      <div class="closeIcon" @click="handleScreen($event)">
        <i class="iconfont icon-compress color-wihte"></i>
      </div>
      <div class="fun-img-screen" @click="handleClick" v-drag>
        <img
          id="imgBoxScreen"
          class="largeImg-screen"
          v-lazy="info[sceneImgKey]"
          @load="loadImageScreen"
          alt=""
        />
        <div
          class="select-preview-screen"
          :style="{
            left: imgBoxList.x + 'px',
            top: imgBoxList.y + 'px',
            width: imgBoxList.width + 'px',
            height: imgBoxList.height + 'px',
          }"
        ></div>
      </div>
    </div>
    <div class="operation-select">
      <ul class="operation-ul">
        <li
          class="opera-box"
          v-if="btnJur.includes('btxl')"
          @click="handleGait"
        >
          <span>步态序列图</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('tp')"
          @click="handleAllStructuring"
        >
          <i class="iconfont color-wihte icon-jiegouhua"></i>
          <span>{{
            !selectBox && selectBoxType == "all"
              ? "取消图片结构化"
              : "图片结构化"
          }}</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('rl')"
          @click="handleFaceStructuring"
        >
          <i class="iconfont color-wihte icon-renliantiqu"></i>
          <span>{{
            !selectBox && selectBoxType == "face" ? "取消人脸提取" : "人脸提取"
          }}</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('ytst')"
          @click="handleSearchImg"
        >
          <i
            class="iconfont color-wihte"
            :class="[
              algorithmType == 1
                ? 'icon-yitusoutu'
                : algorithmType == 2
                ? 'icon-qiche1'
                : algorithmType == 3
                ? 'icon-renti1'
                : 'icon-diandongche',
            ]"
          ></i>
          <span>以图搜图</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('ss')"
          @click="handleRealTime(0)"
        >
          <i class="iconfont icon-bofang color-wihte"></i>
          <span>实时播放</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('lx')"
          @click="handleRealTime(1)"
        >
          <Icon type="md-film" />
          <span>录像回放</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('fd')"
          @click="handleBlowUp"
        >
          <i class="iconfont icon-zoomin color-wihte"></i>
          <span>放大</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('sx')"
          @click="handleReduce"
        >
          <i class="iconfont icon-zoomout color-wihte"></i>
          <span>缩小</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('xz')"
          @click="handleDownload"
        >
          <i class="iconfont icon-download color-wihte"></i>
          <span>下载</span>
        </li>
        <li
          class="opera-box"
          v-if="info.myFavorite == '1' && btnJur.includes('sc')"
          @click="collection(info, 2)"
        >
          <i class="iconfont icon-yishoucang color-yellow"></i>
          <span>收藏</span>
        </li>
        <li
          class="opera-box"
          v-if="info.myFavorite !== '1' && btnJur.includes('sc')"
          @click="collection(info, 1)"
        >
          <i class="iconfont icon-shoucang color-wihte"></i>
          <span>收藏</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('hy')"
          @click="handleVenifcation"
        >
          <i class="iconfont icon-renlian1 color-wihte"></i>
          <span>身份核验</span>
        </li>
        <li
          class="opera-box"
          v-if="btnJur.includes('dmt')"
          @click="multimodalHandler"
        >
          <!-- v-if="btnJur.includes('dmt')" -->
          <!-- <i class="iconfont icon-renlian1 color-wihte"></i> -->
          <span>多模态解析</span>
        </li>
      </ul>
    </div>
    <Modal
      v-model="videoModal"
      draggable
      scrollable
      @on-cancel="handleCancel"
      :mask="false"
      class-name="vertical-pos-modal"
      :title="title == 0 ? '实时播放' : '录像回放'"
    >
      <div class="video-box">
        <h5-player
          ref="H5Player"
          sceneFrom="singleVideo"
          :options="{ layout: '1*1' }"
          :deviceObj="deviceObj"
          dbFullScreen
        >
        </h5-player>
      </div>
      <div slot="footer"></div>
    </Modal>
    <!-- 步态序列图 -->
    <Modal
      v-model="gaitModal"
      title="步态序列图"
      class-name="gait-modal"
      @on-cancel="onCancelByGait"
    >
      <div class="gait-img-box">
        <img
          v-if="info.sequenceImgUrl"
          v-lazy="info.sequenceImgUrl"
          alt="步态序列图"
        />
        <video
          :key="info.sequenceVideoUrl"
          v-if="info.sequenceVideoUrl"
          autoplay
          loop
          muted
          class="video"
        >
          <source :src="info.sequenceVideoUrl" type="video/mp4" />
          浏览器不支持 video 标签，建议升级浏览器。
        </video>
      </div>
      <div slot="footer"></div>
    </Modal>
  </div>
</template>

<script>
import { getAllPicturePick, cutImageBase64 } from "@/api/player";
import { addCollection, deleteMyFavorite } from "@/api/user";
import { picturePick, getBase } from "@/api/wisdom-cloud-search";
import { getRelationCameraByGbId } from "@/api/wisdom-cloud-search";
import { getFrontBackDate } from "@/util/modules/common";
import {
  getOpacityColor,
  getBaseAppRouterAddress,
} from "@/util/modules/common.js";
export default {
  name: "detailLargeImg",
  props: {
    info: {
      type: Object,
      default: () => {},
    },
    collectionType: {
      type: Number,
      default: 0,
    },
    boxSeleType: {
      type: String,
      default: "rect",
    },
    algorithmType: {
      type: [Number, String],
      default: 1,
    },
    btnJur: {
      type: Array,
      default: () => {
        return ["tp", "rl", "ytst", "ss", "lx", "fd", "sx", "xz", "sc"];
      },
    },
    // 渲染的图片地址
    sceneImgKey: {
      type: String,
      default: "sceneImg",
    },
    smallImgKey: {
      type: String,
      default: "sceneImg",
    },
    // 是否开启跨应用跳转
    acrossAppJump: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      id: Math.random(),
      rate: 1,
      overlay: false,
      imgBoxList: {},
      boxStyleList: [],
      selectListBox: [],
      styleBox: 0,
      start: {
        // 选中区域的起点
        x: 0,
        y: 0,
      },
      imgUrl: require("@/assets/img/mock/people.png"),
      defaultStyle: {
        marginTop: 0,
        marginLeft: 0,
        // width: img.width,
        // height: img.height,
        width: 800,
        height: 650,
      },
      startY: 0,
      startX: 0,
      videoModal: false,
      selectBox: true,
      selectBoxType: "",
      routeParam: {},
      loading: false,
      deviceObj: {},
      title: 0,
      gaitModal: false,
    };
  },
  watch: {
    info: {
      handler(val, oldVal) {
        if (!val[this.sceneImgKey]) {
          return;
        }
        this.$nextTick(() => {
          let img = document.querySelector(".fun-img");
          this.defaultStyle.width = img.offsetWidth;
          this.defaultStyle.height = img.offsetHeight;
          // img.style.marginTop = 0;
          // img.style.marginLeft = 0;
          // 重置缩放级别
          this.rate = 1;
          this.clearListBox();
          //   if (oldVal && val[this.sceneImgKey] == oldVal[this.sceneImgKey]) {
          this.loadImage();
          //   }
        });
      },
      immediate: true,
    },
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getOpacityColor,
    // 图片结构化
    handleFaceStructuring() {
      this.boxStyleList = [];
      this.selectListBox = [];
      if (!this.selectBox && this.selectBoxType == "face") {
        this.selectBox = true;
        return;
      }
      this.selectBoxType = "face";
      let fileData = new FormData();
      fileData.append("fileUrl", this.info[this.sceneImgKey]);
      // 步态 入参是 5
      fileData.append(
        "algorithmType",
        this.$route.query.sectionName === "gait" ? 5 : 1
      );
      return new Promise((reslove) => {
        picturePick(fileData).then((res) => {
          this.selectBox = false;
          let data = res.data;
          this.selectListBox = res.data;
          const imgBox = document.getElementById("imgBox" + this.id);
          const nw = imgBox.naturalWidth;
          const nh = imgBox.naturalHeight;
          const w = parseInt(window.getComputedStyle(imgBox).width);
          const h = parseInt(window.getComputedStyle(imgBox).height);
          const rateW = w / nw;
          const rateH = h / nh;
          data.forEach((item) => {
            let boxStyle = {};
            if (nh < 610) {
              boxStyle = {
                x: item.left,
                y: item.top,
                width: item.right - item.left,
                height: item.bottom - item.top,
                type: "face",
                color: this.getInfoByType("face").color,
                zindex: this.getInfoByType("face").zindex,
              };
            } else {
              boxStyle = {
                x: item.left * rateW,
                y: item.top * rateH,
                width: (item.right - item.left) * rateW,
                height: (item.bottom - item.top) * rateH,
                type: "face",
                color: this.getInfoByType("face").color,
                zindex: this.getInfoByType("face").zindex,
              };
            }
            this.boxStyleList.push(boxStyle);
          });
          const selectPreview = document.querySelector(".select-preview");
          if (selectPreview) selectPreview.style.display = "block";
          reslove(data || []);
        });
      });
    },
    // 全结构化
    handleAllStructuring() {
      this.boxStyleList = [];
      this.selectListBox = [];
      if (!this.selectBox && this.selectBoxType == "all") {
        this.selectBox = true;
        return;
      }
      this.selectBoxType = "all";
      let fileData = new FormData();
      fileData.append("fileUrl", this.info[this.sceneImgKey]);
      // 步态
      if (this.$route.query.sectionName === "gait") {
        fileData.append("algorithmVendorType", "yhsd");
      }
      getAllPicturePick(fileData).then((res) => {
        this.selectBox = false;
        let data = res.data;
        const imgBox = document.getElementById("imgBox" + this.id);
        const nw = imgBox.naturalWidth;
        const nh = imgBox.naturalHeight;
        const w = parseInt(window.getComputedStyle(imgBox).width);
        const h = parseInt(window.getComputedStyle(imgBox).height);
        const rateW = w / nw;
        const rateH = h / nh;
        data.forEach((v) => {
          if (v.positionVos && v.positionVos.length) {
            v.positionVos.forEach((item) => {
              let boxStyle = {};
              if (nh < 610) {
                boxStyle = {
                  x: item.left,
                  y: item.top,
                  width: item.right - item.left,
                  height: item.bottom - item.top,
                  type: v.type,
                  color: this.getInfoByType(v.type).color,
                  zindex: this.getInfoByType(v.type).zindex,
                };
              } else {
                boxStyle = {
                  x: item.left * rateW,
                  y: item.top * rateH,
                  width: (item.right - item.left) * rateW,
                  height: (item.bottom - item.top) * rateH,
                  type: v.type,
                  color: this.getInfoByType(v.type).color,
                  zindex: this.getInfoByType(v.type).zindex,
                };
              }
              this.boxStyleList.push(boxStyle);
              this.selectListBox.push(item);
            });
          }
        });
        document.querySelector(".select-preview").style.display = "block";
      });
    },
    // 结构化类型
    getInfoByType(type) {
      switch (type) {
        case "face":
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
        case "vehicle":
          return { color: "#4b8bff", path: "vehicleContent", zindex: 100 };
        case "human":
          return { color: "#bf3e50", path: "humanBodyContent", zindex: 300 };
        case "nonMotor":
          return {
            color: "#67c23a",
            path: "nonmotorVehicleContent",
            zindex: 200,
          };
        default:
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
      }
    },
    // 框选跳转
    async handleBoxSelect(e, index, type) {
      this.routeParam = {};
      let imgData = this.selectListBox[index];
      // let imgUrl =  this.info[this.sceneImgKey];
      const { imageUrl, bottom, left, right, top, feature } = imgData;
      // let base64Data = await cutImageBase64(imgData);
      // let routeParam = {
      //   fileUrl: "data:image/jpeg;base64," + base64Data.data.imageBase,
      //   feature: base64Data.data.feature,
      //   imageBase: base64Data.data.imageBase,
      // };
      let page = this.getInfoByType(type).path;
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center`,
        query: {
          // sectionName:page, urlList: [routeParam]
          sectionName: page,
          imgUrl: imageUrl,
          selectSquare: JSON.stringify({
            bottom,
            left,
            right,
            top,
            type,
            feature,
          }),
          noMenu: 1,
        },
      });
      this.goAppHrefPage(href);
    },
    // 图片大小倍数
    imgMultiple() {},
    clearListBox() {
      this.boxStyleList = [];
      if (!this.selectBox) {
        this.selectBox = true;
        return;
      }
    },
    // 以图搜图
    async handleSearchImg() {
      let pageUrl = "";
      let imgUrl = this.info.traitImg || this.info[this.sceneImgKey];
      if ([1].includes(this.algorithmType)) {
        const data = await this.handleFaceStructuring();
        if (!(data?.length > 0)) {
          return this.$Message.warning("未检测到人脸");
        }
        if (data.length > 1) {
          return this.$Message.warning(
            "存在多张人脸，请选择一张人脸图片双击跳转"
          );
        }
      }
      switch (this.algorithmType) {
        case 1:
          pageUrl =
            "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1";
          break;
        case 2:
          pageUrl =
            "/wisdom-cloud-search/search-center?sectionName=vehicleContent&noMenu=1";
          break;
        case 3:
          pageUrl =
            "/wisdom-cloud-search/search-center?sectionName=humanBodyContent&noMenu=1";
          break;
        case 4:
          pageUrl =
            "/wisdom-cloud-search/search-center?sectionName=nonmotorVehicleContent&noMenu=1";
          break;
      }
      const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          imgUrl,
        },
      });
      this.goAppHrefPage(href);
    },
    // 实时播放
    handleRealTime(index) {
      const { deviceId } = this.info;
      getRelationCameraByGbId(deviceId).then((res) => {
        if (res.data) {
          this.videoModal = true;
          this.title = index;
          const { gbId, name, latitude, longitude } = res.data;
          let params = {
            deviceId: gbId,
            deviceName: name,
            geoPoint: {
              lon: longitude,
              lat: latitude,
            },
            devicetype: index == 0 ? liveType : vodType,
            playType: index == 0 ? "live" : "vod",
          };
          let begintime = getFrontBackDate(
            this.info.absTime || this.info.alarmTime,
            15000,
            1
          );
          let endtime = getFrontBackDate(
            this.info.absTime || this.info.alarmTime,
            15000,
            0
          );
          this.deviceObj =
            index == 0 ? params : { ...params, begintime, endtime };
          if (index == 0) {
            this.queryLog({
              muen: "搜索中心",
              name: "抓拍详情",
              type: "4",
              remark: `查看【${name}】实时视频`,
            });
          } else {
            this.queryLog({
              muen: "搜索中心",
              name: "抓拍详情",
              type: "4",
              remark: `查看【${name}】,【${this.$dayjs(begintime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}-${this.$dayjs(endtime).format(
                "YYYY-MM-DD HH:mm:ss"
              )}】的历史视频`,
            });
          }
        } else {
          this.$Message.info("没有关联摄像机");
        }
      });
    },
    // quxiao
    handleCancel() {
      this.deviceObj = {};
    },
    // 滚轮
    handleWheel(e) {
      // 向上滚动（放大）
      if (e.wheelDelta > 0) {
        this.rate = this.rate + 0.1;
      } else {
        //缩小
        if (this.rate <= 0.2) {
          return;
        }
        this.rate = this.rate - 0.1;
      }
    },
    // 框选处理
    loadImage($event) {
      // let box = document.querySelector(".fun-img");
      let box = this.$refs.funImg;
      box.style.width = "100%";
      box.style.height = "100%";
      const imgBox = document.getElementById("imgBox" + this.id);
      const nw = imgBox.naturalWidth;
      const nh = imgBox.naturalHeight;
      const w = parseInt(window.getComputedStyle(imgBox).width);
      const h = parseInt(window.getComputedStyle(imgBox).height);
      const rateW = w / nw;
      const rateH = h / nh;
      box.style.width = w + "px";
      box.style.height = h + "px";
      // 居中，涉及到框选位置计算和移动，不能直接用flex布局
      // let largeBoxDom = document.querySelector(".large-box");
      let largeBoxDom = this.$refs.largeBox;
      box.style.marginLeft = (largeBoxDom.clientWidth - w) / 2 + "px";
      box.style.marginTop = (largeBoxDom.clientHeight - h) / 2 + "px";

      if (!this.info[this.boxSeleType]) {
        document.querySelector(".select-preview").style.display = "none";
        return;
      }

      this.$nextTick(() => {
        document.querySelector(".select-preview").style.display = "block";
      });

      let data = this.info[this.boxSeleType].split(",");
      if (nh < 610) {
        this.imgBoxList = {
          x: data[0],
          y: data[1],
          width: data[2] - data[0],
          height: data[3] - data[1],
        };
      } else {
        this.imgBoxList = {
          x: data[0] * rateW,
          y: data[1] * rateH,
          width: (data[2] - data[0]) * rateW,
          height: (data[3] - data[1]) * rateH,
        };
      }
    },
    loadImageScreen() {
      if (!this.info[this.boxSeleType]) {
        document.querySelector(".select-preview-screen").style.display = "none";
        return;
      }
      let box = document.querySelector(".fun-img-screen");
      box.style.width = "auto";
      box.style.height = "auto";
      const imgBox = document.getElementById("imgBoxScreen");
      const nw = imgBox.naturalWidth;
      const nh = imgBox.naturalHeight;
      const w = parseInt(window.getComputedStyle(imgBox).width);
      const h = parseInt(window.getComputedStyle(imgBox).height);
      const rateW = w / nw;
      const rateH = h / nh;
      let data = this.info[this.boxSeleType].split(",");
      // "rect":{ "left":0, "top":0,"right":0,"bottom":0}
      if (nh < 650) {
        this.imgBoxList = {
          x: data[0],
          y: data[1],
          width: data[2] - data[0],
          height: data[3] - data[1],
        };
      } else {
        this.imgBoxList = {
          x: data[0] * rateW,
          y: data[1] * rateH,
          width: (data[2] - data[0]) * rateW,
          height: (data[3] - data[1]) * rateH,
        };
      }
      box.style.width = w + "px";
      box.style.height = h + "px";
      this.$nextTick(() => {
        document.querySelector(".select-preview-screen").style.display =
          "block";
      });
    },
    // 收藏
    collection(item, flag) {
      let type = {
        5: "id",
        6: "id",
        14: "alarmTopId",
        15: "alarmId",
        16: "recordId",
        17: "recordId",
      };
      var param = {
        favoriteObjectId: item[type[this.collectionType]],
        favoriteObjectType: this.collectionType,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$emit("collection", "1");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$emit("collection", "2");
        });
      }
    },
    // 放大
    handleBlowUp() {
      this.rate = this.rate + 0.1;
    },
    // 缩小
    handleReduce() {
      if (this.rate <= 0.2) {
        return;
      }
      this.rate = this.rate - 0.1;
    },
    handleClick(e) {
      // 阻止向父组件冒泡
      // e.stopPropagation()
      // e.preventDefault()
    },
    // 下载
    handleDownload() {
      let list = this.info[this.sceneImgKey].split("/");
      let name = list[list.length - 1].split(".")[0];
      getBase(this.info[this.sceneImgKey]).then((res) => {
        let fileUrl = "data:image/jpeg;base64," + res.data;
        this.downloadImg(fileUrl, name);
      });
      // fetch(this.info[this.sceneImgKey])
      // .then(res => res.blob().then(blob => {
      //     const a = document.createElement('a'),
      //         url = window.URL.createObjectURL(blob),
      //         filename = name[0];
      //     a.href = url;
      //     a.download = filename;
      //     a.click();
      //     window.URL.revokeObjectURL(url);
      // }))
      // this.ddd(this.info[this.sceneImgKey],  name)
    },
    downloadImg(imgUrl, imgName) {
      // 创建隐藏的可下载链接
      // let  blob = 'http://pic.c-ctrip.com/VacationH5Pic/mice/wechat/ewm01.png';
      // var a = document.createElement('a');
      // a.style.display = 'none';
      // a.href = blob;
      // a.download = 'QRcode.jpg';
      // document.body.appendChild(a);
      // a.click();
      // //移除元素
      // document.body.removeChild(a);
      //canvans下载
      let src = imgUrl;
      var canvas = document.createElement("canvas");
      var img = document.createElement("img");
      img.onload = function (e) {
        canvas.width = img.width;
        canvas.height = img.height;
        var context = canvas.getContext("2d");
        context.drawImage(img, 0, 0, img.width, img.height);
        // window.navigator.msSaveBlob(canvas.msToBlob(),'image.jpg');
        // saveAs(imageDataUrl, '附件');
        canvas.getContext("2d").drawImage(img, 0, 0, img.width, img.height);
        canvas.toBlob((blob) => {
          let link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = imgName;
          link.click();
        }, "image/jpeg");
      };
      img.setAttribute("crossOrigin", "Anonymous");
      img.src = src;
    },
    handleVenifcation() {
      let pageUrl =
        "/model-market/face-warfare/identity-authentivation?noMenu=1";
      const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          // imgUrl: this.info.traitImg,
          imgUrl: this.info[this.sceneImgKey],
          selectSquare: this.info[this.boxSeleType],
        },
      });
      this.goAppHrefPage(href);
    },
    goAppHrefPage(href) {
      window.open(
        this.acrossAppJump ? getBaseAppRouterAddress(href) : href,
        "_blank"
      );
    },
    // 全屏
    handleFullScreen($event) {
      let img = document.querySelector(".fun-img");
      img.style.scale = 1;
      this.overlay = true;
      // this.loadImage()
    },
    // 全屏
    // handleFullScreen() {
    //     this.$viewerApi({
    //         images: [this.datailsInfo[this.sceneImgKey]]
    //     })
    // },
    // 取消全屏
    handleScreen(e) {
      e.stopPropagation();
      // e.preventDefault();
      if (this.overlay) {
        this.overlay = false;
      }
      this.loadImage();
    },
    // 步态序列图
    handleGait() {
      this.gaitModal = true;
    },
    onCancelByGait() {
      this.gaitModal = false;
    },
    onAction(type) {
      const param = {
        ...this.info,
        sceneImg: this.info[this.sceneImgKey],
      };
      this.$emit("onAction", type, { ...param });
    },

    // 多模态解析
    async multimodalHandler() {
      this.onAction(1);
    },
  },
};
</script>

<style lang="less" scoped>
@width: #000;
.drag-box {
  overflow: hidden;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}
.large-box {
  overflow: hidden;
  width: 100%;
  // height: 100%;
  // height: calc(~"100% - 50px");
  flex: 1;
  position: relative;
  border: 1px solid #d3d7de;
  background: #f9f9f9;
  .fun-img {
    transform: scale(1);
    cursor: grab;
  }
  .largeImg {
    width: auto;
    max-width: 100%;
    max-height: 100%;
  }
}
.operation-box {
  position: absolute;
  height: 50px;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .color-wihte {
    font-size: 20px;
    color: #fff;
    margin: 0 17px;
    cursor: pointer;
  }
  .color-yellow {
    font-size: 20px;
    color: #f29f4c !important;
    margin: 0 17px;
    cursor: pointer;
  }
}
.overlay {
  .screen-box {
    background-color: #000;
    opacity: 1;
    filter: alpha(opacity=100);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    overflow: hidden;
    // display: flex;
    // justify-content: center;
    // align-items: center;
  }
  .closeIcons {
    display: none;
  }
  .closeIcon {
    width: 30px;
    height: 30px;
    background: #fff;
    position: absolute;
    top: 0;
    right: 0;
    border-bottom-left-radius: 80%;
    text-align: center;
    cursor: pointer;
    .color-wihte {
      font-size: 20px;
      color: rgba(0, 0, 0, 0.8);
    }
  }
  .fun-img-screen {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
  }
  .fun-img {
    position: absolute;
    // transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
  }
  .largeImg-screen {
    // cursor: pointer;
    // max-width: 100% !important;
    // max-height: auto !important;
    width: auto;
    // height: 100%;
    max-width: 100%;
    max-height: 650px;
  }
  .operation-box {
    display: none;
  }
  .select-preview-screen {
    display: block;
  }
}
.operation-select {
  height: 50px;
  margin-top: 10px;
  .operation-ul {
    display: flex;
    justify-content: center;
    .opera-box {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 8px;
      cursor: pointer;
      background: #e5e5e5;
      margin: 0 10px;
      &:hover {
        color: #2d87f9;
      }
      // .color-wihte {
      // 	font-size: 20px;
      // 	color: #fff;
      // 	margin: 0 17px;
      // 	cursor: pointer;
      // }
      .color-yellow {
        color: #f29f4c !important;
        cursor: pointer;
      }
      span {
        margin-left: 10px;
      }
    }
  }
}
.select-preview {
  position: absolute;
  top: 0;
  left: 0;
  // width: 90px;
  // height: 90px;
  background: rgba(255, 234, 75, 0.1);
  // background: #FFEA4B;
  border-radius: 4px;
  border: 2px solid rgba(255, 234, 75, 1);
  display: none;
  &-header {
    &::before {
      content: "";
      position: absolute;
      width: 10px;
      height: 10px;
      top: 10px;
      left: 10px;
      border-top: 2px solid rgba(255, 234, 75, 1);
      border-left: 2px solid rgba(255, 234, 75, 1);
    }
    &::after {
      content: "";
      position: absolute;
      width: 10px;
      height: 10px;
      top: 10px;
      right: 10px;
      border-top: 2px solid rgba(255, 234, 75, 1);
      border-right: 2px solid rgba(255, 234, 75, 1);
    }
  }
  &-footer {
    &::before {
      content: "";
      position: absolute;
      width: 10px;
      height: 10px;
      bottom: 10px;
      left: 10px;
      border-bottom: 2px solid rgba(255, 234, 75, 1);
      border-left: 2px solid rgba(255, 234, 75, 1);
    }
    &::after {
      content: "";
      position: absolute;
      width: 10px;
      height: 10px;
      bottom: 10px;
      right: 10px;
      border-bottom: 2px solid rgba(255, 234, 75, 1);
      border-right: 2px solid rgba(255, 234, 75, 1);
    }
  }
}
.select-preview-screen {
  position: absolute;
  top: 0;
  left: 0;
  background: rgba(255, 234, 75, 0.1);
  border-radius: 4px;
  border: 2px solid rgba(255, 234, 75, 1);
  display: none;
}
.originalBox {
  opacity: 0;
}
.click-preview {
  display: block;
  cursor: pointer;
}
// -------视频弹出框------
.video-box {
  width: 100%;
  height: 300px;
}
/deep/.vertical-pos-modal {
  /deep/ .ivu-modal-content-drag {
    top: calc(50% + 180px);
  }
}
/deep/ .gait-modal .ivu-modal {
  width: 6rem !important;
  .gait-img-box {
    display: flex;
    justify-content: center;
    align-items: center;
    img {
      width: auto;
      max-width: 100%;
      max-height: 3.5rem;
    }
  }
}
</style>
