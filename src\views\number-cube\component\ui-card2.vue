<template>
  <div class="list-card card-box">
    <div class="list-card-head">
      <div class="head-left">
        {{ cardMessage.labelCn }}
      </div>
    </div>
    <div class="list-card-content">
      <div class="list-card-content-body">
        <div :class="'content-img'" class="card-border-color">
          <ui-image :src="cardMessage.propertyIcon ? cardMessage.propertyIcon : cardMessage.icon"
            :class="{ small: !cardMessage.propertyIcon }" :defaultIcon="cardMessage.propertyIcon" alt="图片" viewer />
          <!-- <img v-viewer :src="data.src" /> -->
        </div>
        <div class="content-info">
          <div class="info-li">
            <span class="info-name">名称：</span>
            <span class="info-value">{{ cardMessage.label }}</span>
          </div>
          <div class="info-li" v-for="(item, index) in propertyList" :key="index">
            <span class="info-name" :title="item.value">{{ item.label }}：</span>
            <span class="info-value" :title="item.value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardMessage: {},
    propertyList: {}
  },
  data () {
    return {

    }
  },
  mounted () { },
  methods: {
  },
  watch: {},
}
</script>
<style lang="less" scoped>
.list-card {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
  .list-card-head {
    height: 30px;
    border-bottom: 1px solid #fff;
    display: flex;
    align-items: center;
    padding: 0 10px;
    box-sizing: border-box;
    justify-content: space-between;
    overflow: hidden;
    border-top-right-radius: 4px;

    .head-left {
      display: flex;
      align-items: center;
    }
  }

  .list-card-content {
    margin: 10px 0 10px 10px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .list-card-content-body {
      display: flex;

      .content-img {
        width: 116px;
        height: 116px;
        border: 1px solid #fff;
        position: relative;

        &>img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          cursor: pointer;
        }
      }

      .content-info {
        width: 210px;
        max-height: 127px;
        padding-left: 10px;
        box-sizing: border-box;
        overflow: auto;
        .info-li {
          display: flex;
          // align-items: center;
          margin-bottom: 5px;

          .info-name {
            font-size: 12px;
            line-height: 18px;
            color: #181818;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            white-space: nowrap;
          }

          .info-value {
            font-size: 12px;
            line-height: 20px;
            color: #484847;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}
</style>
