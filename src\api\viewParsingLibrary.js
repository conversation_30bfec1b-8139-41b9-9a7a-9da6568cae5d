import request from '@/libs/request'
import { service, cloudSearch, modelSearch } from './Microservice'

// 视图库融合搜索
export function queryFusionSearch(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryFusionSearch',
        method: 'post',
        data
    })
}
// 视图库融合抓拍详情
export function queryFusionDetails(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryFusionDetails',
        method: 'post',
        data
    })
}
// 人脸抓拍搜索
export function queryFaceRecordSearch(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryFaceRecordSearch',
        method: 'post',
        data
    })
}
// 车辆抓拍搜索
export function queryVehicleRecordSearch(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryVehicleRecordSearch',
        method: 'post',
        data
    })
}
// 人体抓拍搜索
export function queryHumanRecordSearch(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryHumanRecordSearch',
        method: 'post',
        data
    })
}
// 非机动车抓拍搜索
export function queryNonmotorRecordSearch(data) {
    return request({
        url: modelSearch + '/ViewLibrarySearch/queryNonmotorRecordSearch',
        method: 'post',
        data
    })
}