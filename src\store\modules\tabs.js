import router from '@/router';
export default {
  namespaced: true,
  state: {
    cacheRouterList: [], //缓存的路由菜单列表
    activeRouterName: '',
  },

  getters: {
    getCacheRouterList(state) {
      return state.cacheRouterList;
    },
    getActiveRouterName(state) {
      return state.activeRouterName;
    },
  },
  mutations: {
    setCacheRouterList(state, cacheRouterList) {
      state.cacheRouterList = cacheRouterList;
    },
    /**
     *
     * @param {*} state
     * @param {当前要关闭的标签routerName} closeRouterName
     */
    closeCacheRouter(state, closeRouterName) {
      if (state.cacheRouterList.length === 1) {
        state.cacheRouterList.splice(0, 1);
        router.push({ name: 'home' });
        return;
      }
      // 如果关闭的是当前路由标签，则需要判断向后或向前激活标签并跳转路由
      let routering = null;
      const index = state.cacheRouterList.findIndex((row) => row.name === closeRouterName);
      if (state.activeRouterName === closeRouterName) {
        const isLast = index === state.cacheRouterList.length - 1;
        if (isLast) {
          routering = state.cacheRouterList[index - 1];
        } else {
          routering = state.cacheRouterList[index + 1];
        }
        state.activeRouterName = routering.name;
        // 判断将要跳转的路由是否为创建组件标签，如果为创建组件标签，则跳转至组件所在路由地址，并且传入该组件名称参数
        if (!!routering && !!routering.meta) {
          router.push({
            name: routering.meta.routeName,
            query: Object.assign(routering.meta.queryParams, { componentName: routering.meta.componentName }),
          });
        } else {
          router.push({ name: state.activeRouterName });
        }
      }
      state.cacheRouterList.splice(index, 1);
    },
    setActiveRouterName(state, activeRouterName) {
      state.activeRouterName = activeRouterName;
    },
  },
  actions: {
    setCacheRouterList({ commit }, cacheRouterList) {
      commit('setCacheRouterList', cacheRouterList);
    },
    closeCacheRouter({ commit }, closeRouterName) {
      commit('closeCacheRouter', closeRouterName);
    },
    setActiveRouterName({ commit }, activeRouterName) {
      commit('setActiveRouterName', activeRouterName);
    },
  },
};
