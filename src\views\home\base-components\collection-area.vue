<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: areaInfoList }">
    <draw-echarts
      class="charts"
      :echart-option="propertyEchart"
      :echart-style="ringStyle"
      ref="devieceNumChartRef"
      :echarts-loading="echartsLoading"
      @echartClick="echartClickJump"
    ></draw-echarts>
    <span class="next-echart">
      <i
        class="icon-font icon-zuojiantou1 f-12"
        @click="scrollRight('devieceNumChartRef', areaInfoList, [], comprehensiveConfig.homeNum)"
      ></i>
    </span>
  </div>
</template>

<script>
import TooltipDom from '@/views/home/<USER>/echarts-dom/tooltip-dom.vue';
import dataZoom from '@/mixins/data-zoom';
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapGetters } from 'vuex';

export default {
  name: 'device-number',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    evaluationIndexResult: {},
    loading: {},
  },
  data() {
    return {
      tabData: [
        { label: '设备数量', id: 'device' },
        { label: '采集区域类型', id: 'type' },
      ],
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      areaInfoList: [], // 采集区域类型数据
      deviceList: [], // 设备数量
      isIndexIdItem: {},
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    loading: {
      handler() {
        this.echartsLoading = this.loading;
      },
      immediate: true,
    },
    evaluationIndexResult: {
      async handler(val) {
        if (!val?.length) return;
        await this.getNumRankInfo(); // 采集区域类型
        this.handleAreaCharts();
      },
      immediate: true,
    },
  },
  filter: {},
  mounted() {},
  methods: {
    getIndexItem(indexId) {
      return this.evaluationIndexResult.find((item) => item.indexId === indexId);
    },
    echartClickJump(item) {
      this.$emit('on-jump', item.data.originData);
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('devieceNumChartRef', [], this.comprehensiveConfig.homeNum);
      });
    },
    // 采集区域类型
    async getNumRankInfo() {
      if (!this.getIndexItem(1008)) return;
      // 找到 采集区域数量达标率1008的数据，然后再请求接口
      let isIndexIdItem = this.getIndexItem(1008);
      this.echartsLoading = true;
      let data = {
        indexId: '1008',
        batchId: isIndexIdItem?.batchId,
        access: 'REPORT_MODE',
        displayType: 'REGION',
        orgRegionCode: isIndexIdItem?.civilCode,
        sortField: 'REGION_CODE',
      };
      try {
        let res = await this.$http.post(governanceevaluation.getNumRankInfo, data);
        this.areaInfoList = res.data.data || [];
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleAreaCharts() {
      if (!this.getIndexItem(1008)) return;
      let { indexId, batchId, indexType } = this.getIndexItem(1008);
      let qualityData = [];
      let disQualityData = [];
      let xAxisData = [];
      this.areaInfoList.forEach((item) => {
        let qualityRate = item?.resultValue ?? 0;
        // let qualityRate = (item?.qualifiedNum / item?.actualNum) * 100 || 0;
        let detailData = {
          name: item.regionName,
          title: '视频图像采集区域数量达标率',
          titleNum: `${qualityRate.toFixed(2)}%`,
          list: [
            {
              label: '采集区域类型达标数量',
              color: '#00FFC1',
              num: item?.qualifiedNum ?? 0,
            },
            {
              label: '采集区域类型不达标数量',
              color: '#FFA700',
              num: item?.unqualifiedNum ?? 0,
            },
          ],
        };
        qualityData.push({
          value: item.qualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId,
            batchId,
            indexType,
          },
        });
        disQualityData.push({
          value: item.unqualifiedNum,
          data: detailData,
          originData: {
            ...item,
            indexId,
            batchId,
            indexType,
          },
        });
        xAxisData.push(item.regionName);
      });
      this.propertyEchart = this.$util.doEcharts.getHomeAreaBar({
        toolTipDom: TooltipDom,
        title: '采集区域类型数量',
        series: [
          {
            name: '达标',
            color: ['#00FFC1', '#01EF77'],
            data: qualityData,
          },
          {
            name: '不达标',
            color: ['#FFA700', '#FFC52A'],
            data: disQualityData,
          },
        ],
        xAxisData: xAxisData,
      });
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  flex: 1;
  .charts {
    width: 100%;
    height: 100% !important;
  }
}
</style>
