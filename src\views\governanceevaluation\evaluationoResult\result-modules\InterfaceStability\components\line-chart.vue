<template>
  <div class="line-chart-container">
    <title-section title-name="历史趋势">
      <div class="search" slot="content">
        <tag-view slot="mid" :list="tagList" @tagChange="changeStatus" ref="tagView" class="tag-view"></tag-view>
        <DatePicker
          class="ml-md"
          ref="DatePicker"
          v-if="dateType === 'DAY'"
          type="month"
          placeholder="请选择月份"
          format="yyyy年MM月"
          :value="month"
          :editable="false"
          @on-change="handleChange"
        ></DatePicker>
        <DatePicker
          class="ml-md"
          ref="yearPicker"
          v-else-if="dateType === 'MONTH'"
          type="year"
          placeholder="请选择年"
          format="yyyy年"
          :value="year"
          :editable="false"
          @on-change="handleChangeYear"
        ></DatePicker>
      </div>
    </title-section>
    <div class="echarts-box">
      <draw-echarts
        v-if="Object.keys(echart).length"
        :echart-option="echartRin"
        :echart-style="rinStyle"
        ref="zdryChart"
        class="charts"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import dealWatch from '@/mixins/deal-watch';
import Vue from 'vue';
import lineChartTooltip from './line-chart-tooltip.vue';

export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    TitleSection: require('../../../components/title-section').default,
    TagView: require('@/components/tag-view.vue').default,
  },
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      barData: [],
      echartRin: {},
      rinStyle: {
        width: '100%',
        height: '100%',
      },
      loading: false,
      month: '',
      year: null,
      monthIndex: '', // 选中月份序号
      echart: {},
      totalCapture: 0, // 总量
      monthTotalCapture: 0, //月量
      tagList: ['日', '月'],
      dateType: 'DAY',
      indexName: '',
    };
  },
  mounted() {
    const indexData = {
      Published_Stability: '分布式身份确认接口稳定性',
      Portrait_Trajectory_Stability: '人像轨迹查询接口稳定性',
      Car_Trajectory_Stability: '车辆轨迹查询接口稳定性',
    };
    const paramList = this.$route.query;
    this.indexName = indexData[paramList.indexType];
    this.getDate();
    this.getDayCaptureStatistics();
    // this.startWatch('$route.query', () => {
    //   this.getDayCaptureStatistics()
    // }, {deep: true, immediate: true})
  },
  methods: {
    /**
     * DAY/MONTH
     * @param index
     * @param item
     */
    changeStatus(index, item) {
      if (item === '日') {
        this.dateType = 'DAY';
        this.getDate();
      } else if (item === '月') {
        this.month = null;
        this.monthIndex = 0;
        this.dateType = 'MONTH';
      }
      this.getDayCaptureStatistics();
    },
    /**
     * actualNum: 0
     * dateType: "MONTH"
     * fetchType: "NEW"
     * horizontal: "01"
     * indexName: "填报准确率"
     * qualifiedNum: 0
     * startTime: "2022-08-01 12:00:00"
     * unqualifiedNum: 0
     * vertical: "100.0"
     */
    initRin() {
      let opts = {
        data: this.echart,
        dateType: this.dateType,
      };
      this.echartRin = this.lineCircleShade(opts);
    },
    // 获取当前月份
    getDate() {
      this.year = new Date().getFullYear() + '';
      let month = new Date().getMonth() + 1;
      this.monthIndex = month > 10 ? month : `0${month}`;
      this.month = `${this.year}-${this.monthIndex}`;
    },
    // 日历切换月份
    handleChange(value) {
      this.month = `${value.slice(0, 4)}-${value.slice(5, 7)}`;
      this.monthIndex = value.slice(5, 7);
      this.year = value.slice(0, 4);
      this.getDayCaptureStatistics();
    },
    handleChangeYear() {
      this.getDayCaptureStatistics();
    },
    getDayCaptureStatistics() {
      this.echart = {
        default: [],
      };
      const xData = this.dateType === 'DAY' ? 31 : 12;
      for (let i = 1; i <= xData; i++) {
        this.echart.default.push({
          horizontal: i,
          vertical: '0',
          dateType: 'MONTH',
          // "startTime": "2022-08-17 08:41:17",
          fetchType: '',
          indexName: this.indexName,
          actualNum: 0,
          qualifiedNum: 0,
          unqualifiedNum: 0,
        });
      }
      this.initRin();
    },
    lineCircleShade(opts) {
      let defaultSeries = {
        // name: '抓拍数',
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 1,
          color: 'rgba(0, 255, 198, 1)',
          shadowColor: 'rgba(43, 132, 226, 1)',
          shadowBlur: 1,
          shadowOffsetY: 1,
        },
        label: {
          show: false,
          position: 'top',
          color: 'rgba(43, 132, 226, 1)',
        },
        itemStyle: {
          color: 'rgba(8, 38, 77, 1)',
          borderColor: 'rgba(0, 255, 198, 1)',
          borderWidth: 2,
        },
        areaStyle: {
          //自定义颜色，渐变色填充折线图区域
          color: new this.$echarts.graphic.LinearGradient(
            0,
            0,
            0,
            1, //变化度
            //渐变色
            [
              {
                offset: 0,
                color: 'rgba(0, 255, 198, 0.3)',
              },
              {
                offset: 1,
                color: 'rgba(29, 239, 247, 0)',
              },
            ],
          ),
        },
        tooltip: {
          show: true,
        },
      };
      let series = [];
      Object.keys(opts.data).forEach((key) => {
        let data = opts.data[key].map((row) => {
          return {
            value: row.vertical,
            name: row.indexName,
            ...row,
          };
        });
        series.push({
          ...defaultSeries,
          data: data,
          name: data.length && data[0].name,
        });
      });
      let xAxisData = opts.data.default && opts.data.default.map((row) => row.horizontal);
      return {
        grid: {
          left: '1%',
          right: '1%',
          top: 30,
          bottom: 20,
          containLabel: true,
        },
        tooltip: {
          trigger: 'axis',
          padding: [4, 10], //内边距
          formatter: (data) => {
            let lineChartTooltipConstructor = Vue.extend(lineChartTooltip);
            let _this = new lineChartTooltipConstructor({
              el: document.createElement('div'),
              data() {
                return {
                  data,
                  dateType: opts.dateType,
                };
              },
            });
            return _this.$el.outerHTML;
          },
          extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        },
        axisPointer: {
          type: 'line',
          lineStyle: {
            type: 'dashed',
            color: 'rgba(255, 255, 255, 0.3)',
          },
        },
        xAxis: [
          {
            type: 'category',
            nameTextStyle: {
              color: '#BBBEC0',
            },
            color: '#fff',
            data: xAxisData,
            axisTick: {
              alignWithLabel: true,
              show: false,
            },
            axisLine: {
              lineStyle: {
                color: '#097699',
              },
            },
            axisLabel: {
              interval: 0,
              color: '#fff',
              fontSize: 12,
              formatter: function (value) {
                if (value === 0) {
                  return value;
                }
                return value;
              },
            },
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '达标值',
            nameTextStyle: {
              color: '#ffff',
              align: 'right',
            },
            axisTick: {
              //y轴刻度线
              show: true,
            },
            splitLine: {
              lineStyle: {
                color: '#063d6b',
                width: 1,
              },
            },
            axisLine: {
              lineStyle: {
                color: '#097699',
              },
            },
            axisLabel: {
              formatter: function (value) {
                return `${value}%`;
              },
              //改变刻度字体样式
              color: '#fff',
            },
          },
        ],
        series: series,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.line-chart-container {
  position: relative;
  height: 100%;
  width: 100%;

  .search {
    position: relative;
    display: flex;
    align-items: center;

    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;

      > div {
        color: #fff;
        margin-left: 42px;
        font-size: 14px;
      }

      .font-sky {
        color: #25e6fd;
      }

      .font-orange {
        color: #f78b2e;
      }
    }
  }

  .echarts-box {
    height: calc(100% - 44.5px) !important;
    width: 100%;
    background: var(--bg-sub-echarts-content);

    .charts {
      height: 100% !important;
    }
  }
}
</style>
