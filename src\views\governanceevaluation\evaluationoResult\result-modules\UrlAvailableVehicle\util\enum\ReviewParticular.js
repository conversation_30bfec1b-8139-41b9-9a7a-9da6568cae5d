import {
  qualifiedColorConfig,
  iconStaticsFaceAndVehicle,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import global from '@/util/global';
export const iconStaticsList = [
  ...iconStaticsFaceAndVehicle,
  {
    name: '设备图片地址可用率',
    count: '0',
    countStyle: {
      color: 'var(--color-bluish-green-text)',
    },
    style: {
      color: 'var(--color-bluish-green-text)',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'select',
    key: 'errorCodes',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const imgFormData = [
  {
    type: 'start-end-time',
    label: '抓拍时间',
    startKey: 'startTime',
    endKey: 'endTime',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    width: 160,
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  {
    type: 'camera',
    label: '抓拍设备',
    key: 'deviceIds',
    width: 180,
  },
  {
    type: 'select',
    key: 'causeErrors',
    label: '不合格原因',
    selectMutiple: true,
    placeholder: '请选择异常原因',
    options: [],
  },
];
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',

    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    key: 'deviceOrgCodeName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '点位类型',
    key: 'sbdwlxText',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测图片数量',
    key: 'testPictureCount',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '合格图片数量',
    key: 'urlCanUseCount',
    tooltip: true,
    minWidth: 150,
  },
  {
    title: '不合格图片数量',
    key: 'urlNotUseCount',
    slot: 'urlNotUseCount',
    tooltip: true,
    width: 200,
  },
  {
    title: '合格率',
    key: 'qualifiedRate',
    slot: 'qualifiedRate',
    tooltip: true,
    width: 120,
  },
  {
    title: '检测结果',
    slot: 'checkStatus',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '原因',
    key: 'errorCodeName',
    tooltip: true,
    minWidth: 120,
  },
  // {
  //   title: '小图URL地址不可访问',
  //   key: 'urlThumbnailPathErrorTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 160,
  // },
  // {
  //   title: '大图URL地址不可访问',
  //   key: 'urlLargePathErrorTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 160,
  // },
  // {
  //   title: '小图URL地址为空',
  //   key: 'urlThumbnailPathNullTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 140,
  // },
  // {
  //   title: '大图URL地址为空',
  //   key: 'urlLargePathNullTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 140,
  // },
  // {
  //   title: '大图时间未标注数量',
  //   key: 'urlLargePathTimeNullTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 160,
  // },
  // {
  //   title: '大图地址未标注数量',
  //   key: 'urlLargePathAddressNullTotal',
  //   className: 'font-red',
  //   tooltip: true,
  //   width: 160,
  // },
  {
    title: '检测时间',
    key: 'startTime',
    tooltip: true,
    width: 200,
  },
  {
    title: '操作',
    slot: 'option',
    fixed: 'right',
    width: 100,
    align: 'center',
  },
];
