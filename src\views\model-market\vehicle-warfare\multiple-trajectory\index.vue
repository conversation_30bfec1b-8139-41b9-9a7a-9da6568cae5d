<template>
    <div class="multi-person-trait">
        <keep-alive include="first-step">
            <component :is="currenView" @routerMaker="routerMaker" :searchParams="searchParams"></component>
        </keep-alive>
    </div>
</template>
<script>
import firstStep from "./add";
import secondStep from "./list.vue";
import "@/pubsub/index";
import { mapMutations } from 'vuex';
export default {
    name: "multipleTrajectory",
    data: function () {
        return {
            searchParams: {},
            currenView: "first-step"
        }
    },
    components: {
        firstStep,
        secondStep
    },
    async created() {
        this.setLayoutNoPadding(true)
    },
    destroyed() {
        this.setLayoutNoPadding(false)
    },
    mounted() {
        $pubsub.subscribe("backto-aimpage", () => {
            $pubsub.unsubscribe("aim-checked");
            $pubsub.unsubscribe("record-list");
            $pubsub.unsubscribe("lapPoints-output");
            $pubsub.unsubscribe("show-lappoints-detail");
            $pubsub.unsubscribe("reshow-list");
            this.currenView = "first-step";
        })
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        /**
         * 生成轨迹
         */
        routerMaker(data) {
            this.searchParams = data;
            this.currenView = "second-step";
        }
    }
}
</script>
<style lang="less">
.multi-person-trait {
    position: relative;
    height: 100%;
    width: 100%;
}
</style>