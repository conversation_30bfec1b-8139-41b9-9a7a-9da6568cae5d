<template>
  <div class="base-result auto-fill">
    <result-title
      :active-index-item="activeIndexItem"
      :default-code-key="defaultCodeKey"
      :default-code="defaultCode"
      @on-change-code="onChangeOrgRegion"
    >
      <tag-view
        slot="mid"
        :list="reallyTagList"
        :default-active="defaultActive"
        @tagChange="changeStatus"
        ref="tagView"
        class="tag-view"
      ></tag-view>
    </result-title>
    <component
      :is="componentName"
      v-bind="handleProps()"
      @viewDetail="viewDetail"
      @viewDetectionDetail="viewDetectionDetail"
    >
      <template #customBtn="{ row, btnInfo }" v-if="componentName === 'StatisticalResult'">
        <div class="action">
          <template v-for="(btnItem, btnIndex) in btnInfo.btnArr">
            <!-- 检测详情 存在不显示的情况 -->
            <ui-btn-tip
              v-if="btnItem.type !== 'detection-detail' || (btnItem.type === 'detection-detail' && row.plan === 1)"
              :key="btnIndex"
              class="mr-sm"
              :icon="btnItem.icon"
              :content="btnItem.text"
              @handleClick="handleClick(btnItem.emitFun, row)"
            ></ui-btn-tip>
          </template>
        </div>
      </template>
    </component>
    <online-details v-model="onlineDetailShow" :get-echarts="getEcharts">
      <template #header>
        <div class="detail-title">
          <Button type="primary" :loading="exportLoading" @click="onExport">
            <i class="icon-font icon-daochu"></i>
            <span class="ml-sm">导出</span>
          </Button>
        </div>
        <icon-statics :icon-list="iconList"> </icon-statics>
      </template>
    </online-details>
    <detection-details
      v-model="detectionDetailShow"
      :row-data="selectedRow"
      @openDeviceDetails="openDeviceDetails"
    ></detection-details>
    <device-details
      v-model="deviceDetailShow"
      :row-data="deviceSelectedRow"
      :parent-row-data="selectedRow"
    ></device-details>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { iconStaticsList } from './util/enum/ReviewParticular.js';
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
export default {
  name: 'VideoGenralOsdAccuracy',
  mixins: [particularMixin],
  props: {
    activeIndexItem: {
      default: () => {
        return {
          indexName: '',
        };
      },
    },
  },
  data() {
    return {
      componentName: 'StatisticalEcharts',
      tagList: Object.freeze([
        {
          label: '统计图表',
          value: 'StatisticalEcharts',
        },
        {
          label: '统计列表',
          value: 'StatisticalResult',
        },
      ]),
      defaultActive: 0,
      defaultCode: '',
      defaultCodeKey: '',
      onlineDetailShow: false,
      iconList: iconStaticsList,
      code: null,
      pageData: {
        pageNum: 1,
        pageSize: 100,
      },
      detailData: {},
      detectionDetailShow: false,
      selectedRow: {},
      deviceDetailShow: false,
      deviceSelectedRow: {},
    };
  },
  created() {
    this.reallyTagList = this.tagList.map((item) => item.label);
  },
  methods: {
    onChangeOrgRegion(val, type) {
      this.defaultCodeKey = type;
      this.defaultCode = val;
    },
    changeStatus(index) {
      this.defaultActive = index;
      this.componentName = this.tagList[index].value;
    },
    // 动态组件 - 动态传参
    handleProps() {
      // 检测明细组件需要的参数 review-particular
      let props = {
        activeIndexItem: this.activeIndexItem,
        filterColumsMethod: this.filterColumsMethod,
      };
      return props;
    },
    async viewDetail(row) {
      try {
        this.code = row.civilCode;
        this.onlineDetailShow = true;
      } catch (err) {
        console.log(err);
      }
    },
    async getEcharts() {
      try {
        // 获取统计
        const statInfo = await this.MixinGetStatInfo({ code: this.code });
        // 设备模式统计
        this.iconList.forEach((item) => {
          if (item.fileName === 'resultValue') {
            // 配置设备图片地址可用率图标
            this.MixinSpecialIcon(item, statInfo.qualified);
          }
          item.count = statInfo[item.fileName] || 0;
        });
        // 获取详情
        const data = await this.MixinGetTableData(this.code);
        this.detailData = data.entities;
        const colors = [$cssVar('--color-failed'), $cssVar('--color-success'), '#2B84E2'];
        const state = ['离线', '在线', '报备'];
        const params = {
          colors: colors,
          state: state,
          yAxis: this.detailData.map((row) => row.checkTimeD),
          categoryData: this.detailData.map((row) => {
            return {
              value: row.duration,
              rowInfo: { ...row }, // 用于处理 其他业务逻辑
              textStyle: {
                /**
                 * 如果离线时间只有一条数据
                 * 则判断state： 2报备，1在线，0离线
                 * 如果有多条记录则显示离线时间
                 *  */
                color: () => {
                  if (row.timerAxis.length === 1) {
                    if (row.timerAxis[0].state === 2) {
                      return '#2B84E2';
                    } else if (row.timerAxis[0].state === 1) {
                      return 'var(--color-success)';
                    } else {
                      return 'var(--color-failed)';
                    }
                  } else {
                    return 'var(--color-failed)';
                  }
                },
              },
            };
          }),
          data: this.dealData(colors, state),
        };
        return params;
      } catch (err) {
        console.log(err);
      }
    },
    dealData(colors, state) {
      let timeData = [];
      this.detailData.forEach((row, index) => {
        row.timerAxis.forEach((rw) => {
          timeData.push({
            itemStyle: { color: colors[rw.state] },
            name: state[rw.state],
            /**
             * Echarts的x轴只能显示 xxxx-xx-xx xx:xx:xx
             * 这里y轴作为了日期所以年月只需要写死即可
             * 0,1,2代表y轴的索引，后两位代表x轴数据开始和结束
             *  */
            value: [index, `2020-01-01 ${rw.t1}`, `2020-01-01 ${rw.t2}`, rw.state === 0],
          });
        });
      });
      return timeData;
    },
    onExport() {
      const params = {
        displayType: this.$route.query.statisticType,
      };
      params.orgRegionCode = this.code;
      this.MixinGetExport(params);
    },
    handleClick(funName, row) {
      this[funName](row);
    },
    // 检测详情
    viewDetectionDetail(row) {
      this.selectedRow = row;
      this.detectionDetailShow = true;
    },
    openDeviceDetails(row) {
      this.deviceDetailShow = true;
      this.deviceSelectedRow = row;
    },
    //过滤表格列的方法
    filterColumsMethod(tableColumns, resultData = []) {
      // 指标配置需要在列表中拿到
      if (resultData?.length && resultData[0].plan === 2) {
        return tableColumns.map((item) => {
          //若检测计划 为对接平台保活信息判定平台是否离线（plan==2），不显示检测详情
          return item.slot != 'action'
            ? item
            : { ...item, btnArr: item.btnArr.filter((btn) => btn.type != 'detection-detail') };
        });
      }
      return tableColumns;
    },
  },
  computed: {
    ...mapGetters({
      aysc_check_status: 'assets/aysc_check_status',
    }),
  },
  watch: {},
  components: {
    TagView: require('@/components/tag-view.vue').default,
    ResultTitle: require('../../components/result-title.vue').default,
    StatisticalEcharts: require('./statistical-echarts.vue').default,
    StatisticalResult: require('../../common-pages/statistical-results/index.vue').default,
    OnlineDetails: require('./components/online-details.vue').default,
    IconStatics: require('@/components/icon-statics.vue').default,
    DetectionDetails: require('./components/detection-details.vue').default,
    DeviceDetails: require('./components/device-details.vue').default,
  },
};
</script>
<style lang="less" scoped>
.base-result {
  height: 100%;

  @{_deep}.tag-view {
    > li {
      height: 34px;
      line-height: 34px;
      padding: 0 20px;
    }
  }
  @{_deep}.active-blue td {
    color: var(--color-bluish-green-text) !important;
  }
}
</style>
