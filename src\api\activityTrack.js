import request from '@/libs/request'
import { holographicArchives } from './Microservice'
// 人脸下拉框
export function getPersonBaseInfo (data) {
  return request({
    url: holographicArchives + '/person/personBaseInfo',
    method: 'POST',
    data: data
  })
}
// 车辆下拉框
export function getVehicleList (data) {
  return request({
    url: holographicArchives + '/movement/vehicleList',
    method: 'POST',
    data: data
  })
}
// IMSI下拉框
export function getImsiList (data) {
  return request({
    url: holographicArchives + '/movement/imsiList',
    method: 'POST',
    data: data
  })
}
// 感知轨迹
export function getPerceivedTrajectory (data) {
  return request({
    url: holographicArchives + '/movement/perceivedTrajectory',
    method: 'POST',
    data: data
  })
}
// 出行轨迹全部
export function getTravelInfoPageList (data) {
  return request({
    url: holographicArchives + '/movement/travelInfoPageList',
    method: 'POST',
    data: data
  })
}

// 飞机出行轨迹
export function aircraftTravelTrajectoryPageList (data) {
  return request({
    url: holographicArchives + '/movement/aircraftTravelTrajectoryPageList',
    method: 'POST',
    data: data
  })
}
// 汽车出行轨迹
export function carTravelTrajectoryPageList (data) {
  return request({
    url: holographicArchives + '/movement/carTravelTrajectoryPageList',
    method: 'POST',
    data: data
  })
}
// 酒店出行轨迹
export function hotelStayTrajectoryPageList (data) {
  return request({
    url: holographicArchives + '/movement/hotelStayTrajectoryPageList',
    method: 'POST',
    data: data
  })
}
// 上网记录
export function onlineRecordPageList (data) {
  return request({
    url: holographicArchives + '/movement/onlineRecordPageList',
    method: 'POST',
    data: data
  })
}
// 火车出行轨迹
export function trainTravelTrajectoryPageList (data) {
  return request({
    url: holographicArchives + '/movement/trainTravelTrajectoryPageList',
    method: 'POST',
    data: data
  })
}
