<template>
  <div class="g6-toolbar-right" @click.stop>
    <div class="zoom-in" @click="zoomBtn('zoomIn')">
      <i class="iconfont icon-zoomin"></i>
    </div>
    <el-slider
      style="z-index: 898"
      v-model="zoom"
      vertical
      :format-tooltip="formatTooltip"
      :step="20"
      :min="20"
      :max="1000"
      height="200px"
    >
    </el-slider>
    <div class="zoom-out" @click="zoomBtn('zoomOut')">
      <i class="iconfont icon-zoomout"></i>
    </div>
    <div>
      <i class="iconfont icon-location1" @click="fitView"></i>
    </div>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      zoom: 100,
    };
  },
  created() {},
  methods: {
    formatTooltip(val) {
      return val + "%";
    },
    zoomBtn(code) {
      if (code === "zoomIn") {
        if (this.zoom < 1000) {
          this.zoom += 20;
        }
      } else {
        if (this.zoom > 20) {
          this.zoom -= 20;
        }
      }
    },
    fitView() {
      this.$emit("fitView");
    },
  },
  watch: {
    zoom(val) {
      this.$emit("changeZoom", val);
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.g6-toolbar-right {
  position: absolute;
  top: 200px;
  right: 10px;
  margin: 0;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .iconfont {
    cursor: pointer;
    font-size: 20px;
  }
  .zoom-in {
    margin-bottom: 5px;
  }
  .zoom-out {
    margin-top: 10px;
  }
}
</style>
