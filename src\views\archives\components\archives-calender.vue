<template>
  <div class="archives-calender">
    <div class="search">
      <DatePicker
        type="month"
        placeholder="请选择月份"
        format="yyyy年MM月"
        :value="yearMonth"
        :editable="false"
        :clearable="false"
        @on-change="handleChange"
      ></DatePicker>
      <div class="statistical-duration">
        <div>
          <span>在线天数：</span>
          <span class="font-sky f-16">{{ checkDayInfo.onlineDays }}天</span>
        </div>
        <div>
          <span>离线天数：</span>
          <span class="font-red f-16">{{ checkDayInfo.offlineDays }}天</span>
        </div>
        <div>
          <span>未检测天数：</span>
          <span class="font-red f-16">{{ checkDayInfo.noCheckDays }}天</span>
        </div>
      </div>
      <div class="statistical-status">
        <Badge color="#14AF2D" text="当前在线" />
        <Badge color="#E0400F" text="当前离线" />
      </div>
    </div>
    <Calender class="calender" format="MM月dd日" :year-month="yearMonth">
      <template #calenderDay="{ label, value, notThisMonth }">
        <div class="calender-item">
          <span>{{ label }}</span>
          <span v-show="!notThisMonth" :class="isOnline(value)"></span>
        </div>
      </template>
    </Calender>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    deviceId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      yearMonth: '',
      checkDayInfo: {
        list: [],
      },
    };
  },
  created() {
    let yearNumber = new Date().getFullYear();
    let monthNumber = new Date().getMonth() + 1;
    this.yearMonth = `${yearNumber}-${monthNumber}`;
    this.getIsOnlineStatistics();
  },
  methods: {
    handleChange(value) {
      let yearNumber = value.slice(0, 4);
      let monthNumber = value.slice(5, 7);
      this.yearMonth = `${yearNumber}-${monthNumber}`;
      this.getIsOnlineStatistics();
    },
    async getIsOnlineStatistics() {
      try {
        const { deviceId, yearMonth } = this;
        let res = await this.$http.get(equipmentassets.getIsOnlineStatistics, {
          params: {
            deviceId,
            month: yearMonth,
          },
        });
        const { data } = res.data;
        this.checkDayInfo = {
          ...data,
        };
      } catch (err) {
        console.log(err);
      }
    },
    isOnline(val) {
      const dayCheck = this.checkDayInfo.list.find((row) => row.day === val);
      if (dayCheck && dayCheck.hasOwnProperty('online')) {
        if (dayCheck.online === '1') {
          return 'badge-online';
        } else if (dayCheck.online === '2') {
          return 'badge-offline';
        }
      }
    },
  },
  watch: {},
  components: {
    Calender: require('@/components/calender').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .font-sky {
    color: #1faf81 !important;
  }
  .badge-online:before {
    background: #1faf8a !important;
  }
  .badge-offline:before {
    background: #ea4a36 !important;
  }
}
.archives-calender {
  .search {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    justify-content: space-between;
    .statistical-duration {
      margin-left: 26px;
      display: flex;
      align-items: center;
      flex-grow: 3;
      > div {
        color: var(--color-content);
        margin-left: 42px;
        font-size: 14px;
      }
      .font-sky {
        color: #25e6fd;
      }
    }
    .statistical-status {
      @{_deep}.ivu-badge {
        margin-left: 39px;
        .ivu-badge-status-dot {
          width: 10px;
          height: 10px;
          border-radius: 50%;
        }
        .ivu-badge-status-text {
          color: var(--color-content);
        }
      }
    }
  }
  .calender-item {
    position: relative;

    .badge-online,
    .badge-offline {
      position: relative;
      display: inline-block;
      width: auto;
    }

    .badge-online:before,
    .badge-offline:before {
      content: '';
      width: 11px;
      height: 11px;
      border-radius: 50%;
      position: absolute;
      right: -12px;
      top: -18px;
    }
    .badge-online:before {
      background: #14af2d;
    }
    .badge-offline:before {
      background: #e0400f;
    }
  }
}
</style>
