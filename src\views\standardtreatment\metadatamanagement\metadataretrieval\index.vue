<template>
  <!-- 元数据检索-暂时不做 -->
  <div class="metadata-search">
    <div>
      <img src="@/assets/img/metadatamanagement/metadata-title.png" />
      <br />
      <img src="@/assets/img/metadatamanagement/decoration.png" />
    </div>

    <div style="position: relative; display: inline-block">
      <div class="metadata-category">
        <div
          @click="categoryClick(item)"
          :class="item.type === category ? 'select-search' : ''"
          v-for="(item, index) in categoryList"
          :key="index"
        >
          {{ item.title }}
        </div>
      </div>
      <!-- 检索背景图 -->
      <img src="@/assets/img/metadatamanagement/search.png" />
      <!-- 检索框 -->
      <div style="position: absolute; width: 100%; height: 100%; top: 38px">
        <AutoComplete class="metadata-input" v-model="value" @on-search="remoteMethod" placeholder clearable>
          <div class="metadata-input-comple">
            共找到关键词“{{ value }}”结果 <span>{{ list.length }}</span
            >条
          </div>
          <Option v-for="(option, index) in list" :value="option.value" :key="index">
            【 表管理 】
            {{ option.label }}</Option
          >
        </AutoComplete>
        <Icon type="ios-search-outline" color="#1DCCFF" :size="46" style="position: relative; top: 14px" />
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'metadataretrieval',
  data() {
    return {
      value: '',
      list: [],
      lists: [
        { label: 'ryxxb-人员信息列表', value: '01' },
        { label: 'ryxxb-人员信息列表', value: '02' },
        { label: 'ryxxb-人员信息列表', value: '03' },
        { label: 'ryxxb-人员信息列表', value: '04' },
        { label: 'ryxxb-人员信息列表', value: '05' },
        { label: 'ryxxb-人员信息列表', value: '06' },
        { label: 'ryxxb-人员信息列表', value: '07' },
        { label: 'ryxxb-人员信息列表', value: '08' },
      ],
      categoryList: [
        { title: '基础字段', type: 'basedField' },
        { title: '基础表', type: 'basis' },
        { title: '字典表', type: 'dictionary' },
      ],
      category: 'basedField',
    };
  },
  mounted() {},
  methods: {
    // 检索下拉
    remoteMethod(item) {
      if (item) {
        setTimeout(() => {
          this.list = this.lists.map((val) => {
            return { label: item + val.label, value: val.value };
          });
        }, 500);
      }
    },
    categoryClick(item) {
      this.category = item.type;
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.metadata-search {
  background: url('../../../../assets/img/metadatamanagement/metadata-search.png');
  text-align: center;
  padding-top: 210px;
}

/deep/ .metadata-input {
  width: 84%;
  display: inline-block;
  height: 65%;
  div {
    height: 100%;
  }
  .ivu-input {
    height: 100%;
    background: none;
    border: none;
    box-shadow: none;
  }
  .ivu-select-selection {
    height: 100%;
    background: none;
    border: none;
    box-shadow: none;
    div {
      height: inherit;
      .ivu-select-input {
        height: inherit;
        color: #fff;
      }
    }
  }
  .metadata-input-comple {
    color: #ffffff;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    margin-left: 10px;
    span {
      color: #27e5cd;
    }
  }
  .ivu-select-dropdown {
    width: 100%;
    left: 0px !important;
    margin-top: 40px;
    text-align: left;
    height: auto;
    max-height: 400px;
    .ivu-select-item {
      color: #27e5cd;
    }
    .ivu-select-item:hover {
      background: rgba(67, 119, 188, 0.3);
    }
    .ivu-select-item-selected {
      background: rgba(67, 119, 188, 0.3);
    }
  }
  .ivu-icon-ios-close {
    color: #7c7c7c;
    font-size: 20px;
    top: 16px;
    cursor: pointer;
  }
}
.metadata-category {
  text-align: left;
  padding-left: 48px;
  div {
    display: inline-block;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    padding: 10px;
    cursor: pointer;
    border: 1px solid transparent;
  }
  .select-search {
    border: 1px solid var(--color-primary);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    color: #fff;
  }
}
</style>
