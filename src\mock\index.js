// 引入mockjs
import Mock from 'mockjs'
// require('./label-pool')
require('./model-market')

// 使用mockjs模拟数据
// 分页规则列表
Mock.mock('/label-manager-service/label/rule/page', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'total': 30,
      'entities|20': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'rules': [
            {
              'labelId': 1,
              'calculateType': '1',
              'calculateThreshold': '1',
              'column': '@ctitle'
            }
          ],
          'createTime': '@datetime',
          'creator': '@cname'
        }
      ]
    }
})
// 分页查询标签
Mock.mock('/label-manager-service/label/list', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'total': 30,
      'entities|50': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'color': '@color',
          'property': '1'
        }
      ]
    }
})
// 创建标签规则
Mock.mock('/label-manager-service/label/rule', 'post', {
  'code': 200,
  'msg': '成功',
  'data': null
})

// 创建标签规则
Mock.mock('/label-manager-service/label/rule/id/1', 'put', {
  'code': 200,
  'msg': '成功',
  'data': null
})

// 分页查询标签组
Mock.mock('/label-manager-service/label/group/page', 'post', {
  'code': 200,
  'msg': '成功',
  'data|6': [
    {
      'id|+1': 1,
      'name': '@ctitle',
      'type': '1',
      'property': '1',
      'objectCount': '@natural(1, 50)',
      'labels|2': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'color': '@color',
          'property': '1'
        }
      ]
    }
  ]
})

// 计算类型
Mock.mock('/qsdi-system-service/dictData/pageList', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'entities|20': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'color': '@color',
          'property': '1'
        }
      ]
    }
})

// 删除标签规则
Mock.mock('/label-manager-service/label/rule/ids/1', 'DELETE', {
  'code': 200,
  'msg': '成功',
  'data': null
})

// 查询标签规则
Mock.mock(`/label-manager-service/label/rule/id/1`, 'get', {
  'code': 200,
  'msg': '成功',
  'data': {
    'remark': '@ctitle',
    'name': '@ctitle',
    'rules|2': [
      {
        'id|+1': 1,
        'labelId|+1': 1,
        'calculateType': '1',
        'calculateThreshold': '1',
        'label': {
          'id|+1': 1,
          'name': '@ctitle',
          'labelGroup': {
            'id|+1': 1,
            'name': '年龄组'
          }
        }
      }
    ]
  }
})

// 分页标签分析列表
Mock.mock('/label-manager-service/label/task/page', 'post', {
  'code': 200,
  'msg': '成功',
  'data':
    {
      'total': 30,
      'entities|20': [
        {
          'id|+1': 1,
          'name': '@ctitle',
          'type': '1',
          'status': '1',
          'objectCount': '@natural(1, 50)',
          'createTime': '@datetime',
          'creator': '@cname'
        }
      ]
    }
})
