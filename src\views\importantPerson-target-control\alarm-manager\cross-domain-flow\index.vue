<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:08:12
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\night-out\index.vue
-->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="cirminalWith"
      :noMore="true"
      :radioList="[]"
    >
    </searchForm>
    <!-- 列表 -->
    <div class="list">
      <ui-table
        ref="tableRef"
        :columns="columns"
        :data="tableList"
        :loading="tableLoading"
      >
        <template #traitImg="{ row }">
          <div
            class="table-image"
            style="width: 65px; height: 80px; margin: auto"
          >
            <ui-image viewer type="people" :src="row.traitImg" />
          </div>
        </template>
        <template #photoUrl="{ row }">
          <div
            class="table-image"
            style="width: 65px; height: 80px; margin: auto"
          >
            <ui-image viewer type="people" :src="row.photoUrl" />
          </div>
        </template>
        <template #bizLabels="{ row }">
          <div style="color: red">{{ row?.bizLabels?.join(",") }}</div>
        </template>
        <template #idCardNoType="{ row }">
          <div>{{ row.idCardNoType | commonFiltering(identityTypeList) }}</div>
        </template>
        <template #idCardNo="{ row }">
          <span
            class="primary click-point"
            @click="goArchivesInfo(row.idCardNo)"
            >{{ row.idCardNo }}</span
          >
        </template>
        <template #action="{ row, index }">
          <div class="btn-tips">
            <ui-btn-tip
              content="详情"
              icon="icon-xiangqing"
              class="primary"
              @click.native="handleDetailFn(row, index)"
            />
          </div>
        </template>
      </ui-table>
    </div>

    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <CaptureDetail
      ref="videoDetail"
      isNoSearch
      :tableList="tableList"
    ></CaptureDetail>
  </div>
</template>

<script>
import { mapActions, mapGetters } from "vuex";
import searchForm from "../../components/search-form.vue";
import CaptureDetail from "@/views/juvenile/components/detail/capture-detail.vue";
import { intrusionPageList } from "@/api/monographic/importantPerson-management";
export default {
  name: "",
  components: { searchForm, CaptureDetail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    radioList: {
      type: Array,
      default: () => [
        { key: 99, value: "全部" },
        { key: 0, value: "未处理" },
        { key: 1, value: "有效" },
        { key: 2, value: "无效" },
      ],
    },
  },
  data() {
    return {
      tableList: [],
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "抓拍照片", slot: "traitImg", align: "center", width: 120 },
        { title: "人员照片", slot: "photoUrl", align: "center", width: 120 },
        { title: "姓名", key: "name", align: "center", width: 80 },
        { title: "前科类型", slot: "bizLabels", align: "center" },
        { title: "常住地", key: "address", align: "center" },
        { title: "证件类型", slot: "idCardNoType", align: "center" },
        { title: "身份证号", slot: "idCardNo", align: "center" },
        { title: "报警时间", key: "alarmTime", align: "center" },
        { title: "报警设备", key: "deviceName", align: "center" },
        { title: "操作", slot: "action", width: 80 },
      ],
      tableLoading: false,
      taskList: [],
    };
  },
  computed: {
    ...mapGetters({
      identityTypeList: "dictionary/getIdentityTypeList", // 证件类型
      nationTypeList: "dictionary/getNationTypeList", //民族类型
    }),
  },
  mounted() {
    this.query();
  },
  methods: {
    /**
     * @description: 获取报警列表
     */
    tableListFn() {
      this.tableLoading = true;
      let data = {
        ...this.params,
        ...this.$refs.searchForm.getQueryParams(),
      };
      intrusionPageList(data)
        .then((res) => {
          this.total = res.data?.total || 0;
          this.tableList =
            res.data?.entities?.map(
              ({ faceCapture, emphasisArchive, ...item }) => ({
                ...item,
                ...faceCapture,
                ...emphasisArchive,
              })
            ) || [];
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * @description: 手动触发查询
     */
    query() {
      this.params = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.tableListFn();
    },

    /**
     * @description: 重置，由searchForm组件手动点击触发的重置，不需要调用this.$refs.searchFormRef.reset()
     */
    reset() {
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.tableListFn();
    },
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.tableListFn();
    },
    // 详情
    handleDetailFn(item, index) {
      this.selectIdCard = item.idCardNo;
      this.$refs.videoDetail.showList(index);
    },
    // 跳转档案
    goArchivesInfo(idCard) {
      const { href } = this.$router.resolve({
        path: "/importantPerson-archive/people-dashboard",
        query: {
          archiveNo: idCard,
          source: "people",
          initialArchiveNo: idCard,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .list {
    flex: 1;
    padding-top: 16px;
    overflow: hidden;
    /deep/ .ui-table {
      height: 100%;
    }
  }
}
.primary {
  color: #2c86f8;
}
.click-point {
  cursor: pointer;
}
</style>
