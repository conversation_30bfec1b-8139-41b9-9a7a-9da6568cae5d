<!--
 * @FileDescription: 电围碰撞设备采集信息结果
 * @Date: 2024-09-20 11:16:54
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-20 11:45:13
 * @FilePath: \icbd-view\src\views\model-market\electic-warfare\ele-collision-analysis\components\informationCollectionMul.vue
-->
<template>
  <div class="dom-wrapper">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>采集列表</span>
        <Icon
          type="md-close"
          size="14"
          @click.native="() => $emit('close', $event)"
        />
      </header>
      <section class="dom-content" v-scroll>
        <div class="capture-list capture-list-two">
          <div
            class="capture-one"
            v-for="(data, index) in listData"
            :key="index"
          >
            <p class="capture-title">
              <span class="area">区域{{ index + 1 }}&nbsp;&nbsp;</span>
              <span class="count">(共{{ data.data.total }}条)</span>
            </p>
            <div class="table-content">
              <ui-table :columns="columns" :data="data.data.entities">
              </ui-table>
            </div>
          </div>
        </div>
      </section>
      <footer></footer>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  // mixins: [commonMixins, cutMixins], //全局的mixin
  components: {},
  props: {
    listData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      columns: [
        {
          title: "序号",
          align: "center",
          width: 90,
          type: "index",
          key: "index",
        },
        { title: "开始时间", key: "absTime" },
        { title: "IMSI编码", key: "imsi" },
        { title: "国际移动台设备识别码", key: "deviceId" },
      ],
    };
  },
  watch: {},
  computed: {},
  async created() {},
  mounted() {},
  methods: {
    pageChange() {},
    pageSizeChange() {},
    close() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/modal";
.dom-content {
  display: flex;
  flex-direction: column;
  .info-box {
    flex: 1;
    overflow-y: auto;
    flex-wrap: wrap;
  }
  .box-1 {
    width: 19%;
  }
  .capture-title {
    height: 30px;
    line-height: 30px;
    .area {
      font-size: 14px;
      font-weight: bold;
    }
  }
}
</style>
