<template>
  <components :is="componentName" v-bind="$props" ref="components"> </components>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {},
    };
  },
  created() {},
  methods: {
    async handleSubmit() {
      const validate = await this.$refs.components.handleSubmit();
      this.formData = this.$refs.components.formData;
      return validate;
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      algorithmVendorData: 'algorithm/getAlgorithmList',
    }),
    componentName() {
      switch (this.moduleAction.indexType) {
        // 分布式身份确认接口稳定性
        case 'VEHICLE_INFO_PASS_IMPORTANT':
          return 'VehicleInfoPassImportant';
        case 'VEHICLE_ACCURACY':
          return 'BasicAccuracy';
        // 车辆卡口设备图片存储时长达标率 - 3016
        case 'VEHICLE_IMAGE_STORE_PASS':
          return 'StorePassForm';
        case 'VEHICLE_URL_AVAILABLE':
        case 'VEHICLE_URL_AVAILABLE_IMPORTANT':
        case 'VEHICLE_URL_AVAILABLE_RECHECK':
          return 'VehicleUrlAvailable';
        case 'VEHICLE_OFFLINE_STAT':
          return 'FaceOfflineStat';
        case 'VEHICLE_QUALITY_PASS_RATE':
          return 'FaceCaptureQualityRate';
        default:
          return 'Original';
      }
    },
  },
  components: {
    StorePassForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/store-pass-form.vue')
        .default,
    Original: require('../vehicle-config').default,
    BasicAccuracy:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/basis-view-config/components/basic-accuracy.vue')
        .default,
    VehicleInfoPassImportant: require('./components/vehicle-info-pass-important').default,
    VehicleUrlAvailable:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/vehicle-config/components/vehicle-url-available.vue')
        .default,
    FaceOfflineStat:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-offline-stat.vue')
        .default,
    FaceCaptureQualityRate:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/face-view-data/components/face-capture-quality-rate.vue')
        .default,
  },
};
</script>
<style lang="less" scoped></style>
