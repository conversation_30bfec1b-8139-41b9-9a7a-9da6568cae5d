<template>
  <div style="height: 100%; width: 100%">
    <h5-player
      ref="H5Player"
      sceneFrom="onlyVideo"
      :options="defaultOptions"
      :showToolbarAllways="true"
      @inited="inited"
    ></h5-player>
  </div>
</template>

<script>
import {
  getPlaceConfig,
} from "@/api/monographic/place.js";
import { queryDevicePageList } from "@/api/target-control.js";
export default {
  name: "realTimeMonitor",
  components: {
  },
  data() {
    return {
      currentIndex: 0,
      timer: null,
      defaultOptions: {
        layout: '2*2',
        border: {
          borderWidth: "4px",
          borderColor: "#F1F1F1",
          focusBorderColor: "#F1F1F1",
        },
      },
      playerInited: false,
      importPartMonitorConfigVo: {}
    };
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer)
  },
  async mounted() {
    // 场所管控配置
    this.importPartMonitorConfigVo = await this.queryPlaceConfig();
    if (this.importPartMonitorConfigVo?.deviceList?.length > 0 && this.playerInited) {
      this.inspectStart({checkedIndexs: [0, 1, 2, 3], inspectTime: this.importPartMonitorConfigVo?.pollingInterval || 10})
    }
  },
  methods: {
    inited() {
      this.playerInited = true
      if (this.importPartMonitorConfigVo?.deviceList?.length > 0) {
        this.inspectStart({checkedIndexs: [0, 1, 2, 3], inspectTime: this.importPartMonitorConfigVo?.pollingInterval || 10})
      }
    },
    inspectStart(options) {
      let _this = this;
      this.currentIndex = 0
      const timerFn = function () {
          let checkedIndexs = options.checkedIndexs;
          let devices = _this.importPartMonitorConfigVo?.deviceList
            .slice(_this.currentIndex, _this.currentIndex + checkedIndexs.length)
            .map((v) => {
              let obj = { ...v };
              return { ...obj, devicetype: liveType, playType: "live" };
            });
          _this.currentIndex += checkedIndexs.length;
          // 超出后从头开始
          if (_this.currentIndex >= _this.importPartMonitorConfigVo?.deviceList.length) {
            _this.currentIndex = _this.currentIndex - _this.importPartMonitorConfigVo?.deviceList.length;
            let deviceAdd = _this.importPartMonitorConfigVo?.deviceList
              .slice(0, _this.currentIndex)
              .map((v) => {
                let obj = { ...v };
                return { ...obj, devicetype: liveType, playType: "live" };
              });
            devices = [...devices, ...deviceAdd];
          }
          _this.inspectPlay({ devices, indexs: checkedIndexs })
          // console.log({ devices, indexs: checkedIndexs });
      };
      if (this.importPartMonitorConfigVo?.deviceList.length > options.checkedIndexs.length) {
        timerFn();
        this.timer = setInterval(timerFn, options.inspectTime * 1000);
      }else {
        let devices = _this.importPartMonitorConfigVo?.deviceList.map((v) => {
          let obj = { ...v };
          return { ...obj, devicetype: liveType, playType: "live" };
        });
        this.inspectPlay({ devices, indexs: options.checkedIndexs })
      }
    },
    // 轮巡
    inspectPlay({ devices, indexs }) {
      devices.forEach((item, index) => {
        this.$refs.H5Player.playStream(
          { inspecting: true, ...item },
          item.playType,
          indexs[index]
        );
      });
    },
    queryPlaceConfig() {
      return new Promise(resolve => {
        getPlaceConfig().then((res) => {
          let placeData = res?.data?.paramValue || "{}";
          let json = JSON.parse(placeData);
          let deviceIds = json.importPartMonitorConfigVo.deviceIds;
          // 回填设备信息
          if (deviceIds.length) {
            queryDevicePageList({
              deviceIds: deviceIds,
              pageNumber: 1,
              pageSize: 9999,
            }).then(({ data }) => {
              let deviceList =
                data?.entities?.map((item) => {
                  return {
                    ...item,
                    deviceName: item.name,
                    deviceId: item.id,
                    deviceGbId: item.gbId,
                    deviceType: item.type,
                    select: true,
                  };
                }) || [];
              resolve({deviceList, pollingInterval: json.importPartMonitorConfigVo.pollingInterval})
            });
          } else {
            resolve({})
          }
        }).catch(() => {
          resolve({})
        })
      })
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .h5vp-content {
  .item-static {
    max-width: calc(~"100% - 40px");
  }
  .h5vp-video-container {
    background-color: #fff!important;
  }
}
</style>
