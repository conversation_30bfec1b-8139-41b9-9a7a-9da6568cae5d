import request from "@/libs/request";
import { icbdService, service } from "./Microservice";

// 视频播放帧标记

// 帧标记新增
export function frameAdd(data) {
  return request({
    url: icbdService + `/frame/add`,
    method: "post",
    data,
  });
}

// 帧标记列表查询
export function framePageList(data) {
  return request({
    url: icbdService + `/frame/pageList`,
    method: "post",
    data,
  });
}
// 帧标记编辑
export function frameUpdate(data) {
  return request({
    url: icbdService + `/frame/update`,
    method: "put",
    data,
  });
}
// 帧标记详情
export function frameDetail(id) {
  return request({
    url: icbdService + "/frame/view/" + id,
    method: "get",
  });
}
// 帧标记删除
export function frameDelete(id) {
  return request({
    url: icbdService + "/frame/remove/" + id,
    method: "delete",
  });
}
// 查询设备信息
export function getDeviceById(id) {
  return request({
    url: service + "/device/getDeviceById/" + id,
    method: "get",
  });
}
// 分享
export function frameShare(data) {
  return request({
    url: icbdService + `/frame/share`,
    method: "post",
    data,
  });
}
