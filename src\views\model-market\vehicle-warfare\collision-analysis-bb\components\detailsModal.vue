<!--
    * @FileDescription: 人体详情
    * @Author: H
    * @Date: 2023/5/16
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="dom-wrapper">
        <div class="dom" @click="($event) => $event.stopPropagation()"> 
            <header>
                <span>抓拍列表</span>
                <Icon type="md-close" size="14" @click.native="() => $emit('close', $event)"/>
            </header>
            <section class="dom-content">
                <!-- <div class="table-content"> -->
                    <div class="info-box">
                        <div class="list-card box-1" v-for="(item, index) in dataList" :key="index" :class="{ checked: item.isChecked }">
                            <p class="img-content">
                                <span class="num" v-if="item.similarity">{{ item.similarity || '0' }}%</span>
                                    <ui-image :src="item.traitImg" alt="动态库" @click.native="faceDetailFn(item, index)" />
                                    <b class="shade vehicle">
                                    <ui-plate-number :plateNo="item.plateNo" :color="item.plateColor" size='mini'></ui-plate-number>
                                </b>
                            </p>
                            <div class="bottom-info">
                                <time>
                                    <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                        <i class="iconfont icon-time"></i>
                                    </Tooltip>
                                    {{ item.absTime }}
                                </time>
                                <p>
                                    <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                        <i class="iconfont icon-location"></i>
                                    </Tooltip>
                                    <span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
                                </p>
                            </div>
                        </div>
                        <div class="empty-card-1" v-for="(item, index) of 5 - (dataList.length % 5)" :key="index + 'demo'"></div>
                        <ui-empty v-if="dataList.length === 0 && loading == false"></ui-empty>
                        <ui-loading v-if="loading"></ui-loading>
                    </div>
                <!-- </div> -->
                <ui-page :current="pageInfo.pageNumber" :total="total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[10, 20, 30, 40]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
            </section>
            <footer></footer>
        </div>
    </div>
</template>

<script>
// import { commonMixins } from '@/mixins/app.js';
// import { cutMixins } from './mixins.js';
import { getCollideDetail } from '@/api/modelMarket'
export default {
    name: '',
    // mixins: [commonMixins, cutMixins], //全局的mixin
    components:{   
    },
    data () {
        return {
            dataList: [],
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 10
            },
            loading: false
        }
    },
    watch:{
            
    },
    computed:{
    },
    async created() {  
    },
    mounted(){
    },
    methods: {
        init(row,data) {
            this.loading = true;
            let params = {
                plateNo: row.plateNo,
                ...this.pageInfo,
                timeSpaces: data,
                search:true
            };
            getCollideDetail(params)
            .then(res => {
                console.log(res, 'res')
            })
            .finally(() => {
                this.loading = false;
            })
        },
        pageChange() {

        },
        pageSizeChange() {

        },
        close() {
            this.$emit('close')
        }
    }
}
</script>

<style lang='less' scoped>
@import './style/modal';
.dom-content{
    display: flex;
    flex-direction: column;
    .info-box{
        flex: 1;
    }
}
</style>
