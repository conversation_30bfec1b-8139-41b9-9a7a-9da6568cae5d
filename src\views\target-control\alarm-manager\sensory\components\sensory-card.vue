<!--
    * @FileDescription: mac报警-列表
    * @Author: H
    * @Date: 2024/06/17
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-01-17 15:18:54
 -->
<template>
  <div class="alarm">
    <div class="top">
      <Checkbox
        v-if="isShowCheckBox"
        v-model="alarmInfo.checked"
        @on-change="cardBox($event)"
        @click.stop.native="() => {}"
      />
      <div class="level-title">
        <img
          v-if="alarmInfo.bgIndex == 1"
          class="level"
          src="@/assets/img/target/title1.png"
          alt
        />
        <img
          v-if="alarmInfo.bgIndex == 2"
          class="level"
          src="@/assets/img/target/title2.png"
          alt
        />
        <img
          v-if="alarmInfo.bgIndex == 3"
          class="level"
          src="@/assets/img/target/title3.png"
          alt
        />
        <img
          v-if="alarmInfo.bgIndex == 4"
          class="level"
          src="@/assets/img/target/title4.png"
          alt
        />
        <img
          v-if="alarmInfo.bgIndex == 5"
          class="level"
          src="@/assets/img/target/title5.png"
          alt
        />
        <div class="num">
          {{
            alarmInfo.taskLevel == 1
              ? "一级"
              : alarmInfo.taskLevel == 2
              ? "二级"
              : "三级"
          }}
        </div>
      </div>
      <!-- <div>
                <ui-btn-tip v-if="alarmInfo.myFavorite == 1" class="collection-icon" content="取消收藏" icon="icon-yishoucang" transfer @click.stop.native="collection(2)" />
                <ui-btn-tip v-else class="collection-icon"  content="收藏" icon="icon-shoucang" transfer @click.stop.native="collection(1)" />
            </div> -->
    </div>
    <div class="info">
      <div class="top-idname" v-if="compareType == 3">
        <div class="icons-box">
          <i class="iconfont icon-wifi"></i>
        </div>
        <p>{{ alarmInfo.mac }}</p>
      </div>
      <div class="top-idname" v-if="compareType == 5">
        <div class="icons-box">
          <i class="iconfont icon-RFID"></i>
        </div>
        <p>{{ alarmInfo.rfidCode }}</p>
      </div>
      <div class="top-idname" v-if="compareType == 4">
        <div class="icons-box">
          <i class="iconfont icon-ZM-dianwei"></i>
        </div>
        <p>{{ alarmInfo.imsi }}</p>
      </div>
      <div class="top-idname" v-if="compareType == 6">
        <div class="icons-box">
          <i class="iconfont icon-a-ETC1x"></i>
        </div>
        <p>{{ alarmInfo.obuId }}</p>
      </div>
      <div class="info-bottom">
        <div class="p">
          <div class="title">布控来源:</div>
          <div class="val">
            {{ alarmInfo.taskType == "1" ? "单体布控" : "库布控" }}
            {{
              alarmInfo.taskType == "1" ? "" : "（" + alarmInfo.libName + "）"
            }}
          </div>
        </div>
        <div class="p">
          <div class="title">报警时间:</div>
          <div class="val">{{ alarmInfo.alarmTime }}</div>
        </div>
        <div class="p">
          <div class="title">报警设备:</div>
          <div class="val" v-html="alarmInfo.deviceName"></div>
        </div>
        <div class="p">
          <div class="title">所属任务:</div>
          <div class="val">{{ alarmInfo.taskName }}</div>
        </div>
      </div>
    </div>
    <div class="status">
      <ui-image
        v-if="alarmInfo.operationType == 1"
        class="img"
        :src="valid"
      ></ui-image>
      <ui-image
        v-if="alarmInfo.operationType == 2"
        class="img"
        :src="invalid"
      ></ui-image>
      <ui-image
        v-if="alarmInfo.operationType == 0"
        class="img"
        :src="unproces"
      ></ui-image>
    </div>
    <div class="btn" v-if="isShowAction">
      <div
        v-if="[0, 2].includes(alarmInfo.operationType)"
        @click.stop="configStatus(1)"
      >
        设为有效<span></span>
      </div>
      <div
        v-if="[0, 1].includes(alarmInfo.operationType)"
        @click.stop="configStatus(2)"
      >
        设为无效<span></span>
      </div>
      <div @click.stop="() => {}" v-if="alarmInfo.operationType != 0">
        <Poptip
          trigger="hover"
          transfer
          word-wrap
          @on-popper-show="showHistory()"
        >
          历史处理
          <div slot="title">
            <div class="block"></div>
            <i>历史处理</i>
          </div>
          <div slot="content">
            <Timeline>
              <TimelineItem v-for="item in historyList" :key="item">
                <div class="time">
                  <div class="timeContent">
                    <div>{{ item.handleTime }}</div>
                    <div>操作人：{{ item.creatorName }}</div>
                  </div>
                </div>
                <div class="content">
                  <div class="content1">
                    <div class="p">
                      <span>处理操作：</span>
                      <div>
                        设为 <span v-if="item.operation == 1">"有效"</span
                        ><span v-else>"无效"</span>
                      </div>
                    </div>
                    <div class="p">
                      <span>处理意见：</span>
                      <div>{{ item.remark || "--" }}</div>
                    </div>
                  </div>
                </div>
              </TimelineItem>
            </Timeline>
          </div>
        </Poptip>
      </div>
    </div>
  </div>
</template>
<script>
import { addCollection, deleteMyFavorite } from "@/api/user";
import {
  batchHandleSensory,
  querySensoryAlarmHandleList,
} from "@/api/target-control";
import round from "@/assets/img/target/round.png";
import valid from "@/assets/img/target/valid.png";
import c1 from "@/assets/img/target/c-one.png";
import c2 from "@/assets/img/target/c-two.png";
import c3 from "@/assets/img/target/c-three.png";
import c4 from "@/assets/img/target/c-four.png";
import c5 from "@/assets/img/target/c-five.png";
import invalid from "@/assets/img/target/invalid.png";
import unproces from "@/assets/img/target/unproces.png";
export default {
  props: {
    alarmInfo: {
      type: Object,
      default: () => {},
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
    // 是否展示操作
    isShowAction: {
      type: Boolean,
      default: true,
    },
    isShowCheckBox: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      single: false,
      c1,
      c2,
      c3,
      round,
      valid,
      invalid,
      unproces,
      c4,
      c5,
      historyList: [],
    };
  },
  computed: {},
  activated() {},
  mounted() {},
  methods: {
    collection(num) {
      var param = {
        favoriteObjectId: this.alarmInfo.alarmId,
        favoriteObjectType: 15,
      };
      if (num == 1) {
        addCollection(param).then((res) => {
          this.$Message.success("收藏成功");
          this.$emit("collection");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$Message.success("取消收藏成功");
          this.$emit("collection");
        });
      }
    },
    cardBox(e) {
      this.$emit("singleChecked", e);
    },
    /**
     * 修改状态
     * @param {*} val
     */
    configStatus(val) {
      var param = {
        alarmRecordSimpleForms: [
          {
            alarmTime: this.alarmInfo.alarmTime,
            alarmId: this.alarmInfo.alarmId,
          },
        ],
        operationType: val,
        compareType: this.compareType,
      };
      batchHandleSensory(param).then((res) => {
        this.$Message.success(res.data);
        this.$emit("refresh");
      });
    },
    /**
     * 处理历史
     */
    showHistory() {
      var param = {
        alarmTime: this.alarmInfo.alarmTime,
        alarmId: this.alarmInfo.alarmId,
        compareType: this.compareType,
      };
      querySensoryAlarmHandleList(param).then((res) => {
        this.historyList = res.data;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.alarm {
  position: relative;
  // width: 340px;
  height: 190px;
  // box-shadow: 0 1px 3px #d9d9d9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #ededed;
  background: #f9f9f9;
  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
    .level {
      position: absolute;
      left: 50%;
      margin-left: -46px;
    }
    .level-title {
      width: 98px;
    }
    .num {
      position: absolute;
      width: 88px;
      text-align: center;
      color: #fff;
    }
  }
  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .block {
      position: relative;
      width: 100px;
      .animation {
        /deep/ .ui-image-div {
          border: 0;
          background: transparent;
        }
      }
      .desc {
        position: absolute;
        z-index: 9;
        background: rgba(0, 0, 0, 0.5);
        color: #fff;
        padding: 0 6px;
      }
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
      .c1 {
        color: #ea4a36;
      }
      .c2 {
        color: #e77811;
      }
      .c3 {
        color: #ee9f00;
      }
      .c4 {
        color: #36be7f;
      }
      .c5 {
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    padding: 12px 20px;
    .top-idname {
      color: #a786ff;
      font-size: 18px;
      font-weight: 800;
      display: flex;
      align-items: center;
      .icons-box {
        width: 20px;
        height: 20px;
        background: #a786ff;
        border-radius: 4px 4px 4px 4px;
        display: flex;
        justify-content: center;
        align-items: center;
        /deep/.iconfont {
          color: #ffffff;
        }
        /deep/.icon-a-ETC1x {
          font-size: 12px;
        }
      }
      p {
        margin-left: 10px;
      }
    }
    .info-bottom {
      flex: 1;
      z-index: 10;
      .p {
        display: flex;
        height: 26px;
        font-size: 14px;
        .title {
          color: rgba(0, 0, 0, 0.6);
          margin-right: 10px;
          line-height: 22px;
        }
        .val {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.9);
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
  }
  .status {
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .btn {
    position: absolute;
    width: 100%;
    background: #2c86f8;
    color: #fff;
    display: flex;
    text-align: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    bottom: -30px;
    transition: 0.3s;
    cursor: pointer;
    div {
      position: relative;
      flex: 1;
      span {
        display: inline-block;
        width: 2px;
        height: 20px;
        border-right: 1px solid #d1cbcb;
        position: absolute;
        right: -1px;
        top: 5px;
      }
    }
  }
  &:hover {
    border: 1px solid #2c86f8;
    .btn {
      bottom: 0;
      transition: 0.3s;
    }
  }
}
.collection-icon {
  /deep/ .iconfont {
    font-size: 14px;
    color: #fff;
  }
  .ivu-tooltip-rel {
    // margin-top: 3px;
  }
  /deep/ .icon-shoucang {
    color: #f29f4c !important;
    text-shadow: 0px 1px 0px #e1e1e1;
  }
  /deep/ .icon-yishoucang {
    color: #f29f4c !important;
  }
}
</style>
<style lang="less">
.ivu-poptip-popper {
  width: 450px !important;
  .block {
    width: 3px;
    background: #2c86f8;
    height: 16px;
    float: left;
    margin-top: 3px;
    margin-right: 6px;
  }
}
.ivu-timeline-item {
  .timeContent {
    display: flex;
    justify-content: space-between;
  }
  .content1 {
    .p {
      display: flex;
      margin-top: 10px;
      span {
        width: 80px;
      }
      div {
        flex: 1;
      }
    }
  }
}

.ivu-poptip-body {
  height: 300px;
  overflow: auto;
  .ivu-timeline-item-head {
    background-color: #e3dada;
    border: 0;
  }
  .ivu-timeline {
    .ivu-timeline-item {
      .ivu-timeline-item-tail {
        left: 10px;
      }
      .ivu-timeline-item-head {
        left: 4px;
      }
      &:first-child {
        .ivu-timeline-item-head {
          background-color: #2d8cf0;
          left: 4px;
        }
        .ivu-timeline-item-head::after {
          content: "";
          position: absolute;
          top: -3px;
          right: -3px;
          width: 19px;
          height: 19px;
          border: 1px solid #2d8cf0 !important;
          border-radius: 50px;
          z-index: 999;
        }
      }
    }
  }
}
/deep/ .ivu-checkbox-input {
  z-index: 999;
}
</style>
