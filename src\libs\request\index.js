import store from "@/store";
import axios from "axios";
import util from "@/libs/configuration/util";
import Setting from "@/libs/configuration/setting";

import { Message, Notice } from "view-design";
import router from "../../router";

// 不需要系统token 的接口
const notAutoApis = ["/qsdi-auth-service/oauth/token"];

const loginName = "Login";
const pending = [];
const CancelToken = axios.CancelToken;

export const clearLoginToken = () => {
  store.dispatch("clearLoginToken").then(() => {
    router.replace({
      name: loginName,
      query: {
        redirect: router.currentRoute.name,
      }, // 登录成功后跳入浏览的当前页面
    });
    // location.reload()
  });
}

let onlyOneTip = ""; // 如果没有权限，只提示一次标志
const removePending = (config) => {
  for (const p in pending) {
    const typeKey = config.params ? config.params.typekey || "" : "";
    if (pending[p].u === config.url + "&" + config.method + "&" + typeKey) {
      // 当当前请求在数组中存在时执行函数体
      pending[p].f("重复请求取消，地址：" + config.url); // 执行取消操作
      pending.splice(p, 1); // 把这条记录从数组中移除
    }
  }
};

// 处理分页接口返回的数据entities 为空的情况
const dealEntitiesData = (dataAxios) => {
  const entitiePageKeys = ["entities", "pageNumber", "pageSize"];
  const keys = Object.keys(dataAxios?.data || {}).filter((key) =>
    entitiePageKeys.includes(key)
  );
  if (keys.length === entitiePageKeys.length) {
    const data = dataAxios?.data || {};
    return {
      ...dataAxios,
      data: {
        ...data,
        entities: data?.entities || [],
      },
    };
  }
  return dataAxios;
};
// 是否正在刷新的标记
let isRefreshing = false;

/**
 * 保存最后一次请求的时间戳
 * token 不存在不更新时间戳
 * @param token
 */
function setLastRequestTime(token) {
  if (token) {
    window.localStorage.setItem("lastRequestTime", new Date().getTime());
  } else {
    window.localStorage.removeItem("lastRequestTime");
  }
}

// 创建一个错误
function errorCreate(msg) {
  const err = new Error(msg);
  errorLog(err);
  throw err;
}

// 记录和显示错误
function errorLog(err) {
  // 添加到日志
  // store.dispatch('admin/log/push', {
  //   message: '数据请求异常',
  //   type: 'error',
  //   meta: {
  //     error: err
  //   }
  // })
  // 打印到控制台
  // if (process.env.NODE_ENV === 'development') {
  //   util.log.error('>>>>>> Error >>>>>>')
  // }
  // 显示提示，可配置使用 iView 的 $Message 还是 $Notice 组件来显示
  if (Setting.errorModalType === "Message") {
    Message.error({
      content: err.message,
      duration: Setting.modalDuration,
    });
  } else if (Setting.errorModalType === "Notice") {
    Notice.error({
      title: "提示",
      desc: err.message,
      duration: Setting.modalDuration,
    });
  }
}

// 创建一个 axios 实例
const service = axios.create({
  baseURL: Setting.apiBaseURL,
  timeout: 60000, // 请求超时时间
});
// 请求拦截器
service.interceptors.request.use(
  (config) => {
    /**
     * 无感刷新 返回Promise 阻止请求
     */
    return new Promise((resolve, reject) => {
      try {
        // 在请求发送之前做一些处理
        const token = util.common.getToken();
        if (notAutoApis.includes(config.url)) {
          config.headers["Authorization"] = "Basic cXNkaTpxc2Rp";
          resolve(config);
        } else if (token) {
          config.headers["Authorization"] = `Bearer ${token}`;
        } else {
          config.headers["Authorization"] = "Basic cXNkaTpxc2Rp";
          resolve(config);
        }
        // 存储cancelToken可以用来阻止访问接口
        config.cancelToken = store.state.common.source.token;
        // 记录日志
        // 这里如果没有传入该接口对应的菜单地址则使用浏览器现在的菜单地址
        if (!config.headers.operatingRecord) {
          config.headers["operatingRecord"] = router.currentRoute.path;
        }

        // 灵析接口需要的token
        if (config.url.includes("/v1/api/public_chat")) {
          config.headers["Authorization"] =
            "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoxNDEwLCJjZWxscGhvbmUiOiIxMzk1MTY2NDM0MiIsImV4dHJhIjoiMjAyMy0xMS0wOCAxMDo0NToyOSIsInByb2plY3RfbmFtZSI6ImNoYXQtd2ViLXN2ci12MSJ9.I6a55LlpU6VuZrLA-iouGmBtr1sZvnEfByiv8IGt67s";
        }

        // 拦截重复请求(即当前正在进行的相同请求)
        // removePending(config) // 在一个ajax发送前执行一下取消操作
        config.cancelToken = new CancelToken((c) => {
          // 这里的ajax标识我是用请求地址&请求方式拼接的字符串，当然你可以选择其他的一些方式\
          const typeKey = config.params ? config.params.typekey || "" : "";
          pending.push({
            u: config.url + "&" + config.method + "&" + typeKey,
            f: c,
          });
        });
        const lastRequestTime = window.localStorage.getItem("lastRequestTime");
        if (
          // 1、判断最后一次请求的时间是否超时
          lastRequestTime &&
          new Date().getTime() - lastRequestTime >
            Setting.tokenExpires * 1000 &&
          config.url !== "/qsdi-auth-service/token/logout"
        ) {
          if (!isRefreshing) {
            isRefreshing = true;
            setTimeout(() => {
              store.dispatch("handleLogOut").then(() => {
                router.replace({
                  name: loginName,
                  query: {
                    redirect: router.currentRoute.name,
                  }, // 登录成功后跳入浏览的当前页面
                });
                isRefreshing = false;
              });
            }, 1000);
            errorLog({ message: "登录超时,请重新登录!" });
          }
          reject();
        } else {
          setLastRequestTime(token);
          resolve(config);
        }
      } catch (e) {
        console.log(e);
      }
    });
  },
  (error) => {
    // 发送失败
    console.error(error);
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // dataAxios 是 axios 返回数据中的 data
    const dataAxios = response.data;
    // 这个状态码是和后端约定的
    const { code } = dataAxios;
    // 根据 code 进行判断
    if (code === undefined || response.config.notCheckCode) {
      // 如果没有 code 代表这不是项目后端开发的接口
      return dataAxios;
    } else {
      // 有 code 代表这是一个后端接口 可以进行进一步的判断
      switch (code) {
        case 0:
          // [ 示例 ] code === 0 代表没有错误
          return dealEntitiesData(dataAxios);
        case 200:
          // [ 示例 ] code === 200 代表没有错误
          return dealEntitiesData(dataAxios);
        // 未登录
        case 20024:
          window.localStorage.clear();
          window.sessionStorage.clear();
          // // 重新返回登录界面登录
          if (router.currentRoute.name === loginName)
            return Promise.reject(dataAxios);
          errorCreate(
            `[ code: 20024 ] ${dataAxios.msg}: ${response.config.url}`
          );
          router.replace({
            name: loginName,
            query: {
              redirect: router.currentRoute.name,
            }, // 登录成功后跳入浏览的当前页面
          });
          break;
        case 42002:
          if (router.currentRoute.name !== onlyOneTip) {
            onlyOneTip = router.currentRoute.name;
            const menu = JSON.parse(sessionStorage.getItem("menuPermission"));
            Message.error({
              content: `您未获得${menu[router.currentRoute.name]}功能的授权`,
              duration: 5,
            });
          }
          break;
        case "xxx":
          // [ 示例 ] 其它和后台约定的 code
          errorCreate(`[ code: xxx ] ${dataAxios.msg}`);
          break;
        default:
          // 不是正确的 code
          errorCreate(`${dataAxios.msg}`);
          break;
      }
    }
  },
  (error) => {
    const errorInfo = error.response;
    if (error && error.response) {
      const requestUrl = errorInfo.config.url;
      switch (error.response.status) {
        case 400:
          error.message = "400请求错误";
          break;
        case 401:
          error.message =
            "状态码：" + errorInfo.status + "，提示：" + errorInfo.data.msg;
          clearLoginToken();
          break;
        case 403:
          if (window.localStorage.getItem("authorUrl")) {
            window.location.href = `${window.localStorage.getItem(
              "authorUrl"
            )}?redirect=${encodeURIComponent(window.location.href)}`;
          } else {
            window.location.href = window.location.host + "/404";
          }
          break;
        case 404:
          error.message = `404请求地址出错`;
          break;
        case 408:
          error.message = "408请求超时";
          break;
        case 500:
          error.message = "500服务器内部错误";
          break;
        case 501:
          error.message = "501服务未实现";
          break;
        case 502:
          error.message = "502网关错误";
          break;
        case 503:
          error.message = "503服务不可用";
          break;
        case 504:
          error.message = "504网关超时";
          break;
        case 505:
          error.message = "505HTTP版本不受支持";
          break;
        default:
          break;
      }
    }
    if (error.message.includes("timeout")) {
      error.message = "查询接口超时，请联系管理员!";
    }
    errorLog(error);
    return Promise.reject(error);
  }
);

export default service;
