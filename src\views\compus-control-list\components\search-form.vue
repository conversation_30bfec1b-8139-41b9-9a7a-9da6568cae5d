<template>
  <div class="searchForm">
    <Form :inline="true">
      <FormItem label="时间:">
        <hl-timerange ref="timerange" :captureTimePeriod="captureTimePeriod" @onceChange="handleTimeChange"
          @change="handleTimeChange" :reflectValue="formData.perceiveDate" :reflectTime="{
            startDate: formData.startDate,
            endDate: formData.endDate,
          }">
        </hl-timerange>
      </FormItem>
      <FormItem prop="schoolId" label="学校:">
        <Select v-model="formData.schoolId" filterable placeholder="请选择校园" class="input-200">
          <Option v-for="(item, $index) in schoolList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
        </Select>
        <Button class="ml-5" @click="toStaticLibrary" size="small">查看</Button>
      </FormItem>
      <FormItem label="陌生人判定:" prop="strangerFlag">
        <Select v-model="formData.strangerFlag" clearable filterable placeholder="请选择陌生人判定" class="input-200">
          <Option v-for="(item, $index) in strangerFlagList" :key="$index" :value="item.dataKey">{{item.dataValue}}</Option>
        </Select>
      </FormItem>
    </Form>
    <div class="btn-group ml-20">
      <Button type="primary" @click="searchHandle">查询</Button>
      <Button @click="resetHandle">重置</Button>
    </div>
  </div>
</template>

<script>
  import { mapActions, mapGetters } from 'vuex';
  export default {
    name: "",
    data() {
      return {
        formData: {
          perceiveDate: "24小时",
          startDate: "",
          endDate: "",
          schoolId: '',
          strangerFlag: "1",
        },
        captureTimePeriod: [
          { name: "24小时", value: "1" },
          { name: "近7天", value: "2" },
          { name: "近30天", value: "3" },
          { name: "自定义", value: "4" },
        ],
      };
    },
    computed: {
      ...mapGetters({
        schoolList: 'dictionary/getSchoolList', // 学校
        strangerFlagList: 'dictionary/getStrangerFlagList', // 检测状态
      })
    },
    watch: {},
    async created() {
      await this.getDictData();
      this.formData.schoolId = this.schoolList[0].dataKey
      this.searchHandle();
    },
    methods: {
      ...mapActions({
        getDictData: 'dictionary/getDictAllData'
      }),
      searchHandle() {
        this.$emit("search", this.formData);
      },
      resetHandle() {
        this.formData = {
          perceiveDate: "24小时",
          startDate: "",
          endDate: "",
          schoolId: this.schoolList[0].dataKey,
          strangerFlag: "",
        };
        this.$refs.timerange.clearChecked(false);
      },
      handleTimeChange(obj) {
        this.formData.perceiveDate = obj.timeSlot;
        this.formData.startDate = obj.startDate;
        this.formData.endDate = obj.endDate;
      },
      toStaticLibrary() {
        let item = this.schoolList.find(v => v.dataKey == this.formData.schoolId)
        const { href } = this.$router.resolve({
          name: "personnel-thematic-database",
          query: {
            id: item.dataKey,
            libName: item.dataValue,
            noMenu: 1
          },
        });
        this.$util.openNewPage(href, "_blank");
      }
    },
  };
</script>

<style lang='less' scoped>
  .searchForm {
    flex: 1;
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #D3D7DE;
    margin-bottom: 20px;
    
    .ivu-form-item {
      margin-bottom: 15px;

      /deep/.ivu-form-item-content {
        height: 34px;
        display: flex;
        align-items: center;
      }
    }
  }
</style>