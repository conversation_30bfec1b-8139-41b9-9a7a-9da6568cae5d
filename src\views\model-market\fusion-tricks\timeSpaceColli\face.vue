<!--
    * @FileDescription: 时空碰撞-人脸
    * @Author: H
    * @Date: 2023/4/11
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="face">
        <page :mapType='mapType'></page>
    </div>
</template>

<script>
import page from './index.vue';
export default {
    name: '',
    components:{
        page   
    },
    data () {
        return {
            mapType: {
                "mapName":'face',
                'sectionName': 'faceMap', // 模态框类型
                'seleType': 'Camera_Face', //框选类型
            }  
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
        
    },
    mounted(){
            
    },
    methods: {}
}
</script>

<style lang='less' scoped>
.face{
    width: 100%;
    height: 100%;
}
</style>
