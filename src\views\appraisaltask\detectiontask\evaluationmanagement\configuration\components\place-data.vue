<template>
  <!-- 场所数据 -->
  <div class="place-data">
    <Form ref="modalData" :label-width="160">
      <common-form
        ref="commonForm"
        :form-data="formData"
        :form-model="formModel"
        :module-action="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
      ></common-form>
      <FormItem label="检测规则设置" prop="rule">
        <rule-list
          :formModel="formModel"
          :indexRuleList="formData.ruleList"
          :moduleAction="moduleAction"
          topicType="7"
        ></rule-list>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'place-data',
  components: {
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    CommonForm: require('./common-form/index').default,
  },
  props: {
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        detectMode: '1',
        ruleList: [],
      },
    };
  },
  watch: {
    moduleAction: {
      handler(val) {
        if (val.config) {
          this.formData = {
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            deviceQueryForm: {
              filter: {
                checkDeviceFlag: 0,
                placeName: '',
                placeAlias: '',
                placeIds: [],
                sbcjqyList: [],
              },
              detectPhyStatus: '0',
            },
            ruleList: [],
          };
          this.getCheckRule();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 表单提交校验
    handleSubmit() {
      return this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    //保存
    async getView() {},
    async getCheckRule() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: this.moduleAction.indexType },
        });
        this.formData.ruleList = data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>

<style lang="less" scoped>
@import url('../../../../components/common.less');
.place-data {
  .dis-select {
    user-select: none;
  }
  .setting {
    color: var(--color-primary);
  }
  .params-item {
    margin-left: 60px;
  }
  .rule-container {
    margin-bottom: 60px;
  }
}
</style>
