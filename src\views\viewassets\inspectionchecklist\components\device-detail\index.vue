<template>
  <ui-modal v-model="visible" title="设备明细" :styles="styles" footer-hide>
    <div class="device-detail-modal auto-fill">
      <!-- 搜索条件 -->
      <dynamic-condition
        :form-item-data="showFilterList"
        :form-data="searchData"
        @search="searchFn"
        @reset="searchReset"
      >
      </dynamic-condition>
      <div class="devider-line"></div>
      <div class="flex-row mt-sm mb-md">
        <div>
          <Checkbox v-model="isCheckAll" @on-change="changeCheckAll" :disabled="!tableData.length">全选</Checkbox>
          <span class="base-text-color f-14" v-show="isCheckAll || selectData.length">
            <span class="ml-md">已选中</span>
            <span>
              <span class="color-failed">
                {{ (isCheckAll ? pageData.totalCount : selectData.length) | formatNum }}
              </span>
              <span>条</span>
            </span>
          </span>
        </div>
        <Button type="primary" :loading="exportLoading" :disabled="!tableData.length" @click="handleExport">
          <i class="icon-font icon-daochu f-14"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
      </div>
      <ui-table
        class="auto-fill"
        :loading="getDeviceListLoading"
        :table-columns="showColumns"
        :table-data="tableData"
        :reserveSelection="true"
        :is-all="isCheckAll"
        row-key="id"
        :default-store-data="defaultStoreData"
        @storeSelectList="storeSelectList"
      ></ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"></ui-page>
    </div>
  </ui-modal>
</template>
<script>
import {
  filterList, //搜索条件
  comprehensiveSearchKeys, //综合巡检的搜索条件的key
  videoSearchKeys,
  faceSearchKeys,
  vehicleSearchKeys,
  commonTableColumns, //共有的列
  comprehensiveTableColumns, // 综合巡检特有的列
  videoOnlyTableColumns,
  faceOnlyTableColumns,
  vehicleOnlyTableColumns,
} from './util';
import { mapGetters, mapActions } from 'vuex';
import viewassets from '@/config/api/viewassets.js';
import downLoadTips from '@/mixins/download-tips';

export default {
  name: 'device-detail-modal',
  mixins: [downLoadTips],
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    activedModule: {
      type: Object,
      default: () => {
        return {
          name: '综合巡检清单',
          id: 1,
        };
      },
    },
    detailParams: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      styles: {
        width: '8.6rem',
      },
      showFilterList: [],
      showColumns: [],
      visible: false,
      searchData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      tableData: [],
      getDeviceListLoading: false,
      isCheckAll: false, //全选
      exportLoading: false, //导出loading
      selectData: [],
      defaultStoreData: [],
      moduleTypeIdMap: {
        comprehensive: [1],
        video: [2, 3],
        face: [4, 5],
        vehicle: [6, 7],
      },
    };
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
    }),
  },
  watch: {
    value(val) {
      if (val) {
        this.clearData();
        this.getShowColumns();
        this.searchData = { ...this.detailParams, orgCode: '' };
        this.getDeviceList();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData', // 批量获取字典值
    }),
    //清空所有数据
    clearData() {
      this.pageData.totalCount = 0;
      this.pageData.pageNum = 1;
      this.clearSelectData();
    },
    //清空选择的数据
    clearSelectData() {
      this.isCheckAll = false;
      this.selectData = [];
      this.defaultStoreData = [];
    },
    // 根据moduleType的id选择对应显示的搜索条件和表格的列
    getShowColumns() {
      // 展示的列
      this.showColumns = [];
      // 过滤条件
      this.showFilterList = [];
      let filterParam = {
        propertySearchSxjgnlx: this.propertySearchSxjgnlx?.length
          ? this.propertySearchSxjgnlx.map((item) => {
              return {
                value: item.dataKey,
                label: item.dataValue,
              };
            })
          : [],
      };
      const filters = {
        comprehensive: filterList(comprehensiveSearchKeys, filterParam).filter((item) => item.isShow),
        video: filterList(videoSearchKeys).filter((item) => item.isShow),
        face: filterList(faceSearchKeys).filter((item) => item.isShow),
        vehicle: filterList(vehicleSearchKeys).filter((item) => item.isShow),
      };
      const columns = {
        comprehensive: [...commonTableColumns, ...comprehensiveTableColumns],
        video: [...commonTableColumns, ...videoOnlyTableColumns],
        face: [...commonTableColumns, ...faceOnlyTableColumns],
        vehicle: [...commonTableColumns, ...vehicleOnlyTableColumns],
      };

      Object.keys(this.moduleTypeIdMap).forEach((key) => {
        if (this.moduleTypeIdMap[key].includes(this.activedModule.id)) {
          this.showFilterList = filters[key];
          this.showColumns = columns[key];
        }
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      //改变页码不清空选择的数据
      this.getDeviceList(false);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      //改变页码不清空选择的数据
      this.getDeviceList(false);
    },
    //查询
    searchFn(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.getDeviceList();
    },
    //重置
    searchReset() {
      this.searchData = { regionCode: '', civilCode: '', orgCode: '' };
      this.getDeviceList();
    },
    changeCheckAll() {
      this.selectData = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.isCheckAll);
        this.$set(item, '_disabled', this.isCheckAll);
        return item;
      });
    },
    storeSelectList(data) {
      this.selectData = this.$util.common.deepCopy(data);
    },
    //获取设备列表
    async getDeviceList(isClearSelect = true) {
      try {
        this.tableData = [];
        this.getDeviceListLoading = true;
        let params = {
          ...this.searchData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          dataModule: this.activedModule.id,
        };
        //接口不需要的参数，但组件需要
        if (Object.keys(params).includes('regionCode')) {
          delete params.regionCode;
        }
        const {
          data: { data },
        } = await this.$http.post(viewassets.getInspectionDetailPageList, params);
        if (isClearSelect) {
          this.clearSelectData();
        }
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.getDeviceListLoading = false;
      }
    },
    //导出
    async handleExport() {
      try {
        this.exportLoading = true;
        let params = {
          ...this.searchData,
          pageNumber: this.pageData.pageNum,
          pageSize: this.pageData.pageSize,
          dataModule: this.activedModule.id,
        };
        if (this.selectData?.length) {
          params.ids = this.selectData.map((item) => item.id);
        }
        const {
          data: { data },
        } = await this.$http.post(viewassets.exportInspectionDetailPageList, params);
        this.$_openDownloadTip();
        await this.$util.common.transformBlob(data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
  },
  async created() {
    if (this.propertySearchSxjgnlx?.length === 0) await this.getAlldicData();
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
.device-detail-modal {
  height: 800px;
  .devider-line {
    width: 100%;
    height: 1px;
    background-color: var(--devider-line);
  }
  @{_deep}.error-text {
    color: #fff;
    background: var(--color-failed);
    padding: 2px 6px;
    border-radius: 4px;
  }
  @{_deep}.success-text {
    color: #fff;
    background: var(--color-success);
    padding: 2px 6px;
    border-radius: 4px;
  }
  @{_deep}.unknown-text {
    color: #fff;
    background: var(--color-warning);
    padding: 2px 6px;
    border-radius: 4px;
  }
}
</style>
