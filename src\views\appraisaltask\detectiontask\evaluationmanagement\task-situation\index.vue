<template>
  <ui-modal v-model="visible" title="每日任务运行情况" :width="95" footer-hide>
    <div class="search-module">
      <div>
        <ui-label class="inline" label="任务状态">
          <Select
            class="width-md"
            placeholder="请选择任务状态"
            clearable
            v-model="searchData.taskStatus"
            @on-change="init"
          >
            <Option v-for="(item, index) in resultList" :key="index" :value="item.value">
              {{ item.name }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline ml-lg" label="指标类型">
          <Select
            class="width-md"
            placeholder="请选择指标类型"
            clearable
            v-model="searchData.indexModule"
            @on-change="init"
          >
            <Option v-for="(item, index) in global.indexTypeList" :key="index" :value="item.id">
              {{ item.title }}
            </Option>
          </Select>
        </ui-label>
      </div>
      <ul class="task-status-legend">
        <li v-for="(item, index) in taskStatusList" :key="index">
          <span
            v-if="item.status === '0'"
            class="triangle"
            :style="{
              color: item.bgColor,
            }"
            >▼</span
          >
          <span
            v-else
            class="legend"
            :style="{
              backgroundColor: item.bgColor,
            }"
          ></span>
          <span class="ml-xs">{{ item.text }}</span>
        </li>
      </ul>
    </div>
    <ui-table class="quality-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
      <template #index="{ row, index }">
        <span
          :style="{
            color: row.taskStatus === '3' || row.taskStatus === '4' ? 'var(--color-warning)' : '',
          }"
        >
          {{ index + 1 }}
        </span>
      </template>
      <template #taskName="{ row }">
        <span
          :style="{
            color: row.taskStatus === '3' || row.taskStatus === '4' ? 'var(--color-warning)' : '',
          }"
        >
          {{ row.taskName }}
        </span>
      </template>
      <template #indexName="{ row }">
        <span
          :style="{
            color: row.taskStatus === '3' || row.taskStatus === '4' ? 'var(--color-warning)' : '',
          }"
        >
          {{ row.indexName }}
        </span>
      </template>
      <template #checkDataNum="{ row }">
        <span
          :style="{
            color: row.taskStatus === '3' || row.taskStatus === '4' ? 'var(--color-warning)' : '',
          }"
        >
          {{ row.checkDataNum === null ? row.detectModeText : row.checkDataNum }}
        </span>
      </template>
      <template #situation="{ row }">
        <bar class="bar" :result-list="resultList" :time-width="scaleWidth" :bar-data="row"></bar>
      </template>
    </ui-table>
  </ui-modal>
</template>
<script lang="jsx">
import evaluationoverview from '@/config/api/evaluationoverview';
import AScale from './components/a-scale.vue';
export default {
  props: {
    value: {
      type: Boolean,
    },
  },
  data() {
    return {
      visible: false,
      resultList: [
        { name: '未开始', value: '0', index: 0 },
        { name: '进行中', value: '1', index: 1 },
        { name: '已完成', value: '2', index: 2 },
        { name: '已暂停', value: '3', index: 3 },
        { name: '任务异常', value: '4', index: 4 },
      ],
      taskStatusList: [
        { text: '预计运行时间', status: '0', bgColor: 'var(--color-success)' },
        { text: '进行中', status: '1', bgColor: 'var(--color-success)' },
        { text: '异常', status: '3', bgColor: 'var(--color-failed)' },
        { text: '终止', status: '4', bgColor: 'var(--color-warning)' },
        { text: '运行结束', status: '2', bgColor: 'var(--color-offline)' },
      ],
      searchData: {
        taskStatus: '',
        indexModule: '',
        pageSize: 999,
      },
      loading: false,
      tableColumns: [
        { title: '序号', width: 55, slot: 'index', align: 'center', className: 'serial-number' },
        {
          title: '所属任务',
          align: 'left',
          slot: 'taskName',
          width: 240,
        },
        { title: '指标名称', align: 'left', slot: 'indexName', width: 240 },
        { title: '检测数据量', align: 'left', slot: 'checkDataNum', width: 110 },
        {
          title: ' ',
          align: 'left',
          slot: 'situation',
          className: 'situation',
          renderHeader: (h) => {
            return <AScale class={'a-scale'} scaleWidth={this.scaleWidth}></AScale>;
          },
        },
      ],
      tableData: [],
      scaleWidth: 950,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(evaluationoverview.taskIndexPageListByDay, this.searchData);
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.error(err, 'err');
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    Bar: require('./components/bar.vue').default,
    AScale,
  },
};
</script>
<style lang="less" scoped>
@{_deep}.ivu-modal-body {
  display: flex;
  height: 800px;
  flex-direction: column;
  position: relative;
  .ivu-table-header {
    overflow: visible;
    th {
      height: 100px;
    }
    .situation {
      overflow: visible;
      .ivu-table-cell {
        overflow: visible;
      }
      .a-scale {
        margin-left: 120px;
      }
    }
  }
  .ivu-table-cell {
    overflow: visible;
  }
  .ivu-table-cell-slot {
    display: flex;
    align-items: center;
  }
  .serial-number {
    .ivu-table-cell-slot {
      justify-content: center;
    }
  }
  .task-status-legend {
    display: flex;
    align-items: center;
    color: var(--color-content);
    li {
      display: flex;
      align-items: center;
      margin-left: 20px;
    }
    .legend {
      display: flex;
      width: 12px;
      height: 12px;
    }
  }
  .quality-table {
    .ivu-table-header,
    .ivu-table-body {
      table {
        colgroup {
          col {
            &:nth-child(2) {
              width: 260px;
            }
            &:nth-child(3) {
              width: 260px;
            }
            &:nth-child(4) {
              width: 110px;
            }
            &:nth-child(5) {
              width: 1060px;
            }
          }
        }
      }
    }
  }
}
.search-module {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-modal-footer);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.bar {
  margin-left: 120px;
}
</style>
