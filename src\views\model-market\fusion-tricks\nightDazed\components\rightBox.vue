<!--
    * @FileDescription: 昼伏夜出检索结果
    * @Author: H
    * @Date: 2023/02/01
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="rightBox" :class="{'rightBox-pack': footerUpDown}">
        <div class="title">
            <p>检索结果</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="box-bottom">
            <div class="query_condition">
                <ul class="tab-name">
                    <li class="tab-li" 
                        @click="handleClick(index, item)"
                        :class="{'active-tab': index == tabIndex}" 
                        v-for='(item, index) in tabList' 
                        :key="index"> 
                        {{ item.name }}
                    </li>
                </ul>
                <div class="box_form">
                    <p class="form_title">{{searchName[tabIndex]}}:</p>
                    <Input v-model="queryForm.numNo" placeholder="请输入" class="wrapper-input"></Input>
                    <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
                </div>
            </div>
            <p class="box_headline">共 <span>1</span>条检索结果</p>
            <div class="box-content">
                <ul class="box-top-ul">
                    <li class="box-top-li" v-for="(item, index) in detailList" :key='index'>
                        <div class="box-right">
                            <div class="content-top" @click="handleDetails()">
                                <div class="content-top-img">
                                    <!-- <ui-image viewer :src="item.sceneImg" /> -->
                                    <img v-lazy="item.sceneImg" alt="" />
                                </div>
                                <div class="content-top-right">
                                    <div class="content-top-right-name">
                                        <span class="ellipsis flex">
                                            <ui-icon :type="listIcon[tabIndex][0]" :size="14"></ui-icon>
                                            <span class="block">{{ item[field[tabIndex][0]] || '--' }}</span>
                                        </span>
                                        <p class="list_title">
                                            累计昼伏夜出:<span>{{ item.peerNum }}</span>天
                                        </p>
                                    </div>
                                    <span class="ellipsis">
                                        <ui-icon :type="listIcon[tabIndex][1]" :size="14"></ui-icon>
                                        <span class="bule" :class="{'block': !item[field[tabIndex][1]]}">{{ item[field[tabIndex][1]] || '--' }}</span>
                                    </span>
                                    <!-- <span class="ellipsis" v-if='tabIndex == 0'>
                                        <ui-icon :type="listIcon[tabIndex][2]" :size="14"></ui-icon>
                                        <span class="orange" :class="{'block': !item[field[tabIndex][2]]}">{{ item[field[tabIndex][2]] || '--' }}</span>
                                    </span> -->
                                </div>
                            </div>
                            <!-- <div class="content-bottom">
                                <div class="iconList">
                                    <opera-floor iconSec="icon-dangan2"></opera-floor>
                                </div>
                            </div> -->
                        </div> 
                    </li>
                </ul>
                <ul class="box-ul" v-if="timeListShow">
                    <li class="box-li" 
                        :class="{'box-li-pack': packUpDown[index], 'box-li-packend': index == (timeList.length- 1)&&packUpDown[index]}" 
                        v-for='(item, index) in timeList' :key="index">
                        <div class="box-li-top">
                            <Icon type="md-radio-button-on"></Icon>
                        </div>
                        <div class="box-li-bottom">
                            <div class="time-title" @click="handletimelist(item, index)">
                                <p><span class="time-date">{{ item.date }}</span> <span class="time-num">{{item.times}}</span>次</p>
                                <p class="triangle" :class="{'active-triangle': !packUpDown[index]}"></p>
                            </div>
                            <div class="child_list" v-for="(it, ind) in item.children" :key="ind">
                                <p class="sec-radio"></p>
                                <div class="content-top" @click="handleListTrack(item.children, ind)">
                                    <div class="content-top-img">
                                        <img v-lazy="it.sceneImg" alt="">
                                    </div>
                                    <div class="content-top-right">
                                        <span class="ellipsis">
                                            <ui-icon type="time" :size="14"></ui-icon>
                                            <span>{{ it.captureTime }}</span>
                                        </span>
                                        <span class="ellipsis">
                                            <ui-icon type="location" :size="14"></ui-icon>
                                            <span>{{ it.captureAddress }}</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <div class="footer" :class="{packArrow: footerUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ footerUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    components:{
            
    },
    data () {
        return {
            packUpDown: [],
            footerUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
            timeList:[ 
                {
                    'date': '2020-3-24',
                    'times': '1',
                    children: [
                        {
                            'captureTime': '2020-03-24  23:27:10',
                            'captureAddress': '福建省漳州市芗城区李冰大道150号',
                            'sceneImg': require('@/assets/img/demo/avatar-1.png')
                        }
                    ]
                },
                {
                    'date': '2020-3-25',
                    'times': '1',
                    children: [
                        {
                            'captureTime': '2020-03-25  01:27:10',
                            'captureAddress': '福建省漳州市芗城区李冰大道150号',
                            'sceneImg': require('@/assets/img/demo/avatar-1.png')
                        }
                    ]
                },
                {
                    'date': '2020-3-26',
                    'times': '1',
                    children: [
                        {
                            'captureTime': '2020-03-26  02:27:10',
                            'captureAddress': '福建省漳州市芗城区李冰大道150号',
                            'sceneImg': require('@/assets/img/demo/avatar-1.png')
                        }
                    ]
                },
                {
                    'date': '2020-3-27',
                    'times': '1',
                    children: [
                        {
                            'captureTime': '2020-03-27  03:27:10',
                            'captureAddress': '福建省漳州市芗城区李冰大道150号',
                            'sceneImg': require('@/assets/img/demo/avatar-1.png')
                        }
                    ]
                }
            ],
            timeListShow: false,
            tabIndex:0,
            tabList:[
                { 'name': '人脸', 'type': 'vidTrajectoryStatistics', 'childType': 'vidTrajectory'},
                { 'name': '车辆', 'type': 'carTrajectoryStatistics', 'childType': 'carTrajectory'},
                { 'name': 'RFID', 'type': 'vidTrajectory'},
                { 'name': 'IMSI', 'type': 'vidTrajectory'},
                { 'name': 'MAC', 'type': 'vidTrajectory'},
            ],
            queryForm: {
                numNo: ''
            },
            detailList: [ 
                { 
                    'xm':'张伟',
                    'peerNum': '4',
                    'archiveNo': '320102198703071905',
                    'plateNo': '苏A 88888',
                    'ownerName': '张开万',
                    'sceneImg': require('@/assets/img/demo/avatar-1.png')
                }
            ],
            listIcon:{
                0: ['xingming', 'shenfenzheng', 'camera'],
                1: ['chepai', 'xingming', 'shenfenzheng']
            },
            field:{
                0: [ 'xm', 'archiveNo','gmsfhm',],
                1: [ 'plateNo', 'ownerName', 'idcardNo'],
            },
            typeIndex: {
                tab: 0, 
                secTab: 0
            },
            searchName:{
                '0': '身份证号',
                '1': '车牌号码',
                '2': 'RFID编码',
                '3': 'MAC地址',
                '4': 'IMSI编码'
            }
        }
    },
    watch:{
            
    },
    computed:{
            
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        // 分类
        handleClick(index, item) {
            this.tabIndex = index;
            if(index == 1){
                this.detailList[0].sceneImg = require('@/assets/img/vehicle-1.png');
                this.timeList.map(item => {
                    item.children[0].sceneImg = require('@/assets/img/vehicle-1.png');
                })
            }else{
                this.detailList[0].sceneImg = require('@/assets/img/demo/avatar-1.png');
                this.timeList.map(item => {
                    item.children[0].sceneImg = require('@/assets/img/demo/avatar-1.png');
                })
            }
            this.timeListShow =  false;
            this.packUpDown = Array.apply(null, { length: this.timeList.length }).map(()=>{
                return true;
            });
        },
        // 查询
        handleSearch() {
            this.timeListShow = false;
            this.$emit('search')
        },
        handletimelist(item, index) {
            this.$set(this.packUpDown, index, !this.packUpDown[index]);
            if(!this.packUpDown[index] && !this.timeList[index].children) {
                // this.handleTrack(item, index);
            }
        },
        handleListTrack() {

        },
        // 检索数据详情
        handleDetails() {
            this.timeListShow = true;
            this.$emit('retrieveDetails')
        },
        // \
        handleCancel() {
            this.$emit('cancel')
        },
        // 展开、收起
        handlePackup(){
            this.footerUpDown = !this.footerUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
@import '../../components/style/index';
@import '../../components/style/timeLine';
.rightBox{
    width: 370px;
    position: absolute;
    right: 10px;
    top: 10px;
    background: #fff;
    height: calc( ~'100% - 20px' );
    transition: height 0.2s ease-out;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    .box-bottom{
        // padding: 10px 20px;
        height: calc( ~'100% - 80px' );
        overflow-y: auto;
        .query_condition{
            .tab-name{
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid #D3D7DE;
                .tab-li{
                    width: 20%;
                    text-align: center;
                    padding: 7px 0;
                    cursor: pointer;
                    font-size: 14px;
                }
                .active-tab{
                    color: #2C86F8;
                    border-bottom: 3px solid #2C86F8;
                }
            }
            .box_form{
                display: flex;
                padding: 15px;
                align-items: center;
                .form_title{
                    color: rgba(0,0,0,0.45);
                    font-size: 14px;
                    width: 60px;
                }
                .wrapper-input{
                    margin: 0 5px 0;
                    flex: 1;
                }
            }
        }
        .box_headline{
            font-size: 12px;
            color: rgba(0,0,0,0.6);
            padding: 0 15px;
            span{
                color: rgba(44, 134, 248, 1);
            }
        }
        .box-content{
            padding: 0 15px;
            .box-top-ul{
                border-bottom: 1px dashed rgba(211, 215, 222, 1);
                .box-top-li{
                    margin-top: 10px;
                    display: flex;
                    cursor: pointer;
                    .box-number{
                        width: 20px;
                        height: 20px;
                        color: #F29F4C;
                        border-radius: 50%;
                        border: 1px solid #F29F4C;
                        // line-height: 20px;
                        text-align: center;
                        margin-top: 34px;
                        margin-right: 8px;
                    }
                    
                    .box-right{
                        background: #F9F9F9;
                        flex: 1;
                        padding: 5px 10px 0px 5px;
                        &:hover{
                            background: rgba(44, 134, 248, 0.1);
                        }
                        .content-top{
                            display: flex;
                            &-img{
                                width: 80px;
                                height: 80px;
                                background: #F9F9F9;
                                border: 1px solid #d3d7de;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                img{
                                    width: auto;
                                    height: auto;
                                    max-height: 80px;
                                    max-width: 80px;
                                }
                            }
                            .content-top-right{
                                margin-top: 3px;
                                margin-left: 11px;
                                font-size: 14px;
                                width: calc( ~'100% - 91px' );
                                /deep/ .iconfont{
                                    margin-right: 5px;
                                }
                                .bule{
                                    color: #2C86F8;
                                }
                                .orange{
                                    color: #F29F4C;
                                }
                                .block{
                                    color: #000000;
                                }
                                &-name{
                                    display: flex;
                                    margin-bottom: 8px;
                                    .flex{
                                        flex:1;
                                    }
                                    .list_title{
                                        font-size: 12px;
                                        color: rgba(0,0,0,0.6);
                                        cursor: pointer;
                                        span{
                                            color: rgba(242, 159, 76, 1);
                                        }
                                    }
                                }
                            }
                        }
                        .content-bottom{
                            display: flex;
                            justify-content: space-between;
                            margin-top: 5px;
                            .iconList{
                                width: 80px;
                            }
                            .analyseIcon{
                                font-size: 12px;
                                color: #5584FF;
                                cursor: pointer;
                            }
                        }
                    }
                }
            }
            .box-ul{
                padding: 0;
            }
        }
    }
    .packArrow{
        img{
            transform: rotate(180deg);
            transition: transform 0.2s;
        }
    }
}
.rightBox-pack{
    height: 80px;
    transition: height 0.2s ease-out;
    overflow: hidden; 
}
</style>
