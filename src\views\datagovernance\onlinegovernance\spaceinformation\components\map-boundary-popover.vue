<template>
  <div class="map-boundary-box">
    <el-popover
      ref="popover1"
      popper-class="layer-popover"
      visible-arrow="false"
      placement="bottom"
      v-model="boundaryVisible"
      trigger="click"
      @show="showPop"
    >
      <div slot="reference" :class="['base-text-color', 'flex-aic', boundaryVisible ? 'font-blue' : '']">
        <i class="icon-font icon-ditubianjie mr-sm f-14"></i>
        <span class="f-14 pointer">地图边界</span>
      </div>
      <div class="layer-title flex-row">
        <span>地图边界</span>
        <i-switch
          class="mr-md"
          v-model="isUseBoundary"
          :before-change="beforeChangeUseBoundary"
          @on-change="handleChangeUseBoundary"
        ></i-switch>
      </div>
      <ui-search-tree
        ref="uiSearchTree"
        no-search
        class="ui-search-tree"
        :check-strictly="true"
        :show-checkbox="true"
        :default-props="defaultProps"
        :tree-data="treeData"
        :node-key="nodeKey"
      >
        <template #label="{ node, data }">
          <p class="flex-row">
            <span>
              {{ data.regionName }}
            </span>
            <Button
              v-if="data.children?.length && !data.disabled"
              type="text"
              class="mr-sm"
              @click="checkAllOnNode(node, data)"
              >{{ data.checkAll ? '取消' : '全选' }}</Button
            >
          </p>
        </template>
      </ui-search-tree>
      <div class="pop-button flex-aic">
        <Button size="small" class="mr-sm" @click="mapBoundaryCancel">取消</Button>
        <Button size="small" type="primary" :loading="confirmLoading" @click="mapBoundaryConfirm">确定</Button>
      </div>
    </el-popover>
  </div>
</template>
<script>
import mapApi from '@/config/api/map';
export default {
  props: {
    treeData: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      boundaryVisible: false,
      isUseBoundary: false, //开关
      defaultCheckedKeys: [],
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      nodeKey: 'regionCode',
      confirmLoading: false,
      boundaryObjs: [], //边界数组
    };
  },
  name: '',
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
  computed: {},
  methods: {
    showPop() {
      // console.log('this.tree', this.treeData)
    },
    //确认
    async mapBoundaryConfirm() {
      let checkedNodes = this.$refs.uiSearchTree.getCheckedNodes();
      if (!checkedNodes || !checkedNodes.length) {
        this.$Message.warning('请选择区域');
        return;
      }
      try {
        this.isUseBoundary = true;
        this.confirmLoading = true;
        this.boundaryObjs = [];
        for (let i = 0; i < checkedNodes.length; i++) {
          if (checkedNodes[i].regionCode == '000000') {
            continue;
          }
          try {
            let { data } = await this.$http.get(
              mapApi.getMapCoordinates + checkedNodes[i].regionCode + `.json?t=${Date.now()}`,
            );
            let boundaryObj = {
              ...data.features[0],
            };
            this.boundaryObjs.push(boundaryObj);
          } catch {
            this.$Message.error({
              content: `没有 ${checkedNodes[i].regionName} 的边界信息，无法绘制此区域`,
              duration: 2,
            });
            continue;
          }
        }
        this.$emit('addAreaOverlay', this.boundaryObjs);
      } catch (err) {
        console.log(err);
      } finally {
        this.confirmLoading = false;
        this.boundaryVisible = false;
      }
    },
    //取消
    mapBoundaryCancel() {
      this.boundaryVisible = false;
    },
    //全选节点
    checkAllOnNode(node, data) {
      let nodeCheckAll = data.checkAll || false;
      if (node.childNodes) {
        this.checkParent(node, !nodeCheckAll);
        node.childNodes.map((item) => {
          this.$set(item, 'checked', !nodeCheckAll);
        });
      }
    },
    checkParent(node, check) {
      this.$set(node, 'checked', check);
      this.$set(node.data, 'checkAll', check);
    },
    //还原数据
    clearBoundaryDatas() {
      this.isUseBoundary = false;
      this.$refs.uiSearchTree.setCheckedKeys([]);
    },
    //切换开关前
    beforeChangeUseBoundary() {
      return new Promise((resolve) => {
        let checkedKeys = this.$refs.uiSearchTree.getCheckedKeys();
        if (!checkedKeys?.length && !this.isUseBoundary) {
          this.$Message.warning('请选择区域');
        } else {
          resolve();
        }
      });
    },
    //切换地图边界开关
    handleChangeUseBoundary(val) {
      if (val) {
        this.mapBoundaryConfirm();
        return;
      }
      this.$emit('handleChangeUseBoundary', val);
    },
  },
  mounted() {},
};
</script>
<style lang="less" scoped>
.ui-search-tree {
  height: calc(100% - 102px);
}
.pop-button {
  height: 60px;
  border-top: 1px solid var(--devider-line);
  justify-content: center;
}
</style>
