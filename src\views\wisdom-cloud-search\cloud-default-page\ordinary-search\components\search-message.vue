<template>
  <div class="search" v-if="dataList && dataList.length > 0">
    <Form ref="form" :inline="true" :model="formData" class="form">
      <FormItem :label="`${item.fieldNameCn}：`" prop="searchValue" v-for="(item, index) in searchList" :key="index">
        <!-- 输入框 -->
        <Input v-if="item.searchType == '1'" placeholder="请输入" v-model="formData[item.fieldName].searchValue" maxlength="50" />
        <!-- 下拉框-->
        <Select v-if="item.searchType == '2'" v-model="formData[item.fieldName].searchValue" ref="select" transfer>
          <Option v-for="item in item.list" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
            {{ item.dataValue }}
          </Option>
        </Select>
        <!-- 时间 -->
        <template v-if="item.searchType == '3'">
          <DatePicker :editable="false" v-model="formData[item.fieldName].searchValue" type="datetimerange" format="yyyy-MM-dd HH:mm" placeholder="请选择" style="width: 290px"></DatePicker>
        </template>
        <!-- 日期 -->
        <template v-if="item.searchType == '6'">
          <DatePicker :editable="false" v-model="formData[item.fieldName].searchValue" type="daterange" format="yyyy-MM-dd" placeholder="请选择" style="width: 290px"></DatePicker>
        </template>
        <!-- 单选 -->
        <ui-tag-select
          @input="
            e => {
              formData[item.fieldName].searchValue = e
            }
          "
          ref="tagSelect"
          v-if="item.searchType == '4'"
        >
          <ui-tag-select-option v-for="(item, $index) in item.list" :key="$index" :name="item.dataKey">
            {{ item.dataValue }}
          </ui-tag-select-option>
        </ui-tag-select>
        <!-- 多选 -->
        <Select v-if="item.searchType == '5'" v-model="formData[item.fieldName].searchValue" ref="select" transfer multiple filterable :max-tag-count="1">
          <Option v-for="item in item.list" :value="item.dataKey" :key="item.dataKey" placeholder="请选择">
            {{ item.dataValue }}
          </Option>
        </Select>

      </FormItem>
      <FormItem class="btn-group ml-30">
        <Button type="primary" @click="startSearch(formData, 'search')">查询</Button>
        <Button @click="resetHandle">重置</Button>
      </FormItem>
    </Form>
  </div>
</template>
<script>
import { queryDataByKeyTypes } from '@/api/user'
export default {
  components: {},
  props: {
    searchList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formData: {},
      dataList: []
    }
  },
  watch: {
    searchList: {
      async handler(val) {
        this.init(val)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {},
  methods: {
    async init(val) {
      if (val && val.length > 0) {

        var row = this.$parent.getCurrentMenuInfo()


        let search = {}
        for (const e of val) {

          if(row && row[e.fieldName]){
            search[e.fieldName] = {
              searchValue: row[e.fieldName].searchValue,
              searchType: e.searchType
            }
          }else {
            search[e.fieldName] = {
              searchValue: null,
              searchType: e.searchType
            }
          }
          // 下拉框/单选/多选
          if (e.searchType == '2' || e.searchType == '4' || e.searchType == '5') {
            let keyList = await this.getDataByKeyTypes([e.dictionaryCode])
            let key = Object.keys(keyList[0])
            // 添加对应字典值
            e.list = keyList[0][key]
          }
        }
        this.formData = search
        this.dataList = val
      }
    },
    // 获取字典值
    async getDataByKeyTypes(val) {
      let res = await queryDataByKeyTypes(val)
      return res.data || []
    },
    // 查询
    startSearch(val, type) {
      let search = val
      if (val) {
        Object.keys(search).forEach(item => {
          if (search[item].searchType == '3' || search[item].searchType == '6') {
            if (search[item].searchValue && search[item].searchValue.length > 0) {
              if(search[item].searchValue[0] == ""){
                search[item].searchValue = null
                search[item].startDate = null
                search[item].endDate = null
              }else{
                let startTime = this.$dayjs(search[item].searchValue[0]).format('YYYY-MM-DD');
                let endTime = this.$dayjs(search[item].searchValue[1]).format('YYYY-MM-DD')
                search[item].startDate = startTime.split('-').join('');
                search[item].endDate = endTime.split('-').join('');
              }
            }
          }
          if (type === 'clear') {
            search[item] = {
              searchValue: '',
              searchType: search[item].searchType
            }

            if (search[item].searchType == '3') {
              search[item].searchValue = null
            }
          }

        })
      }
      let data = {
        params: {
          ...search
        }
      }
      this.$emit('searchForm', data)
    },
    // 重置
    resetHandle() {
      this.startSearch(this.formData, 'clear')
    }
  }
}
</script>
<style lang="less" scoped>
.search {
  width: 100%;
  position: relative;
  margin-bottom: 0px;
  .form {
    font-size: 14px;
    width: 100%;
    padding-top: 20px;
    .width-200 {
      width: 220px;
    }
  }
  .btn-group {
    margin-right: 0px;
    position: absolute;
    bottom: 0px;
    right: 0px;
  }
  /deep/.ivu-form-item{
    margin-right: 10px;
  }
}
</style>
