<template>
  <!-- 治理成效 -->
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: governEffectData }">
    <GovernEffect
      v-show="governEffectData.length"
      ref="governChart"
      :loading="echartsLoading"
      :data="governEffectData"
      :tableColumns="tableColumns"
    ></GovernEffect>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
export default {
  name: 'govern-tendency',
  props: {
    indexModuleSelectData: {},
    year: {},
  },
  data() {
    return {
      sourceData: {},
      governEffectData: [],
      echartsLoading: false,
      tableColumns: [
        {
          title: '治理内容',
          key: 'indexName',
          minWidth: 120,
          ellipsis: true,
          render: (h, { row }) => {
            return <span title={row.indexName}>{row.indexName}</span>;
          },
        },
        {
          title: '治理前（1月平均）',
          key: 'minResultValue',
          tooltip: true,
          align: 'center',
          minWidth: 70,
          render: (h, { row }) => {
            return <span class="color-red">{row.minResultValue}%</span>;
          },
          renderHeader: (h) => {
            return (
              <span>
                <p>治理前</p>
                <p>({this.minMonth}月平均)</p>
              </span>
            );
          },
        },
        {
          title: '治理后（12月平均）',
          key: 'maxResultValue',
          tooltip: true,
          align: 'center',
          minWidth: 70,
          render: (h, { row }) => {
            return <span class="color-blue">{row.maxResultValue}%</span>;
          },
          renderHeader: (h) => {
            return (
              <span>
                <p>治理后</p>
                <p>({this.maxMonth}月平均)</p>
              </span>
            );
          },
        },
        {
          title: '变化',
          key: 'changeValue',
          tooltip: true,
          align: 'right',
          minWidth: 70,
          render: (h, { row }) => {
            return (
              <span>
                <span class={Number.parseFloat(row.changeValue) >= 0 ? ['color-green'] : ['color-red']}>
                  {row.changeValue}%
                </span>
                <span
                  class={
                    Number.parseFloat(row.changeValue) >= 0
                      ? ['icon-font', 'icon-shangsheng', 'color-green']
                      : ['icon-font', 'icon-xiajiang', 'color-red']
                  }
                ></span>
              </span>
            );
          },
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      getHomeConfig: 'home/getHomeConfig',
      getFullscreen: 'home/getFullscreen',
    }),
    minMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['minMonth'];
      }
      return 1;
    },
    maxMonth() {
      if (this.governEffectData.length > 0) {
        return this.governEffectData[0]['maxMonth'];
      }
      return 12;
    },
  },
  async created() {
    await this.getAllEvaluationIndex();
    this.initList();
  },
  watch: {
    getHomeConfig: {
      handler(obj) {
        if (!this.governEffectData.length && obj?.taskSchemeId) {
          this.initList();
        }
      },
      deep: true,
    },
  },
  methods: {
    async initAll() {
      this.initList();
    },
    async initList() {
      try {
        let { taskSchemeId, regionCode } = this.getHomeConfig;
        this.echartsLoading = true;
        let params = {
          indexModule: this.indexModuleSelectData,
          taskSchemeId,
          year: this.year,
          civilCode: regionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(home.getHomeGovernanceResultList, params);
        this.governEffectData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    /**
     * 通过indexType获取IndexName
     * @param indexType
     */
    getIndexNameByIndexType(indexType) {
      let keys = Object.keys(this.sourceData);
      for (let j = 0; j < keys.length; j++) {
        let key = keys[j];
        let data = this.sourceData[key];
        for (let i = 0; i < data.length; i++) {
          let item = data[i];
          if (item.indexType === indexType) {
            return item.indexName;
          }
          if (indexType === 'SCHEME_INDEX_RATE') {
            return '指标达标率';
          }
        }
      }
    },
    async getAllEvaluationIndex() {
      try {
        this.echartsLoading = true;
        let {
          data: { data },
        } = await this.$http.get(home.getAllEvaluationIndex);
        this.sourceData = data || {};
      } catch (error) {
        this.echartsLoading = false;
      }
    },
  },
  components: {
    GovernEffect: require('@/views/home/<USER>/govern-effect.vue').default,
  },
};
</script>
<style lang="less" scoped>
.body-container {
  position: relative;
  height: calc(100% - 30px);
  overflow-y: auto;
  overflow-x: hidden;
  @{_deep} .govern-effect-container {
    .ui-table {
      height: 100%;
      width: 100%;
      .ivu-table-default {
        background-color: transparent !important;
      }
      .ivu-table-overflowX,
      .ivu-table-tip {
        overflow-x: hidden;
      }
      .ivu-table-header th {
        background: transparent !important;
        box-shadow: none !important;
        color: #89b9fb !important;
        height: 36px;
        font-size: 12px;
      }
      .ivu-table-body td {
        background: transparent !important;
        color: #ffffff;
        height: 36px;
        font-size: 12px;
      }
      .tips-box {
        display: none !important;
      }
    }
    .color-red {
      color: #c60235;
    }
    .color-blue {
      color: #00cdf7;
    }
    .color-green {
      color: #01ef77;
    }
  }
}
</style>
