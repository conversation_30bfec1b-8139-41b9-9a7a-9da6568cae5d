<template>
    <div class="alarm-list-container">
        <div v-scroll class="scroll-container">
            <transition-group name="fade-right" tag="ul">
                <li v-for="(item,index) in alarmList" :key="item.alarmId" :class="['alarm-item','alarm-style',index==0?'active':'']">
                    <div class="item-box">
                        <img v-viewer="option" @click.stop="" :src="item.traitImg" :data-src="item.sceneImg" >
                        <p>{{item.alarmTime}}</p>
                        <p :title="item.deviceName"><i :class="['icon-title','iconfont',getDeviceIcon(item)]"></i>{{item.deviceName}}</p>
                    </div>
                </li>
            </transition-group>
        </div>
    </div>
</template>
<script>
export default {
    name: "alarmListComponets",
    data() {
        return {
            option: {
                url: 'data-src'
            }
        };
    },
    props: ["alarmList"],
    methods: {
        /**
         * 获取图标
         */
        getDeviceIcon(item) {
            return Toolkits.getDeviceIconType(item);
        }
    },
    watch: {},
    mounted() {},
    components: {}
};
</script>
<style lang="less">
.alarm-list-container {
    overflow: hidden;
    & > ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    .box-shadow{
         box-shadow:8px 8px 2px rgba(0, 0, 0, .3);
         border:1px solid #2C86F8;
    }
    ul > li {
        margin-bottom: 15px;
        border-radius: 2px;
        &:hover{
            .box-shadow
        }
    }
    .alarm-item {
        height: 274px;
        width: 200px;
        padding: 5px;
        transition: all 1s;
        overflow: hidden;
    }
    .scroll-container{
        height: 100%;
    }
    .active{
        .box-shadow
    }
    .item-box {
        display: flex;
        flex-direction: column;
        justify-content: stretch;
        align-items: center;
        & > img {
            height: 200px;
            max-width: 100%;
        }
        text-align: center;
        p {
            padding: 5px;
            text-align: left;
            width:100%;
            text-overflow: ellipsis;
            word-break: normal;
            overflow: hidden;
            white-space: nowrap;
        }
        .icon-title {
            color:#336ff3;
            margin-right: 5px;
        }
    }
    .alarm-style {
        background: #fff;
    }
    .fade-right-enter-active,
    .fade-right-leave-active {
        transition: all 1s;
    }

    .fade-right-enter {
        transform: translateY(-30px);
        opacity: 0;
    }

    .fade-right-leave-to {
        transform: translateY(30px);
        opacity: 0;
    }
}
</style>
