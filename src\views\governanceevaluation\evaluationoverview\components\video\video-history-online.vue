<template>
  <!--  本年度视频历史最高在线情况 -->
  <div class="video-history-online auto-fill">
    <div class="content auto-fill">
      <div class="evaluation-header">
        <div class="filtrate">
          <span class="f-16 color-filter ml-sm">{{ paramsData.regionName }}-本年度视频历史最高在线情况</span>
        </div>
        <div>
          <span class="evaluation-time">
            <i class="icon-font icon-shijian1 f-14 mr-x"></i> 评测时间：{{ paramsData.examineTime || '未知' }}
          </span>
        </div>
      </div>
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
        <div>
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <ui-table class="auto-fill" :tableColumns="tableColumns" :table-data="tableData" :loading="loading"></ui-table>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  name: 'video-history-online',
  mixins: [downLoadTips],
  data() {
    return {
      exportLoading: false,
      loading: false,
      examineTime: '',
      tableColumns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 50,
          fixed: 'left',
        },
        {
          title: '地区',
          key: 'orgRegionText',
          align: 'center',
          minWidth: 100,
          fixed: 'left',
        },
      ],
      tableData: [],
      paramsData: {},
    };
  },
  created() {
    for (let i = 1; i < 13; i++) {
      let obj = {
        title: i + '月',
        key: (i - 1).toString(),
        align: 'center',
        minWidth: 100,
        children: [
          {
            title: '在线数',
            key: (i - 1).toString(),
            align: 'center',
            minWidth: 100,
          },
        ],
      };
      this.tableColumns.push(obj);
    }
    this.tableColumns = [
      ...this.tableColumns,
      {
        title: '历史最高在线数',
        key: 'maxCountOfYear',
        align: 'center',
        minWidth: 100,
        fixed: 'right',
        children: [
          {
            title: '在线数',
            key: 'maxCountOfYear',
            align: 'center',
            minWidth: 100,
            fixed: 'right',
          },
        ],
      },
    ];
  },
  activated() {
    this.paramsData = this.$route.query;
    this.getTableList();
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsData.indexId,
        batchId: this.paramsData.batchId,
        access: this.paramsData.access,
        displayType: this.paramsData.displayType,
        orgRegionCode: this.paramsData.orgRegionCode,
        customParameters: {},
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportOnlineRatePromotePopData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    async getTableList() {
      try {
        let params = {
          indexId: this.paramsData.indexId,
          batchId: this.paramsData.batchId,
          access: this.paramsData.access,
          displayType: this.paramsData.displayType,
          orgRegionCode: this.paramsData.orgRegionCode,
          customParameters: {},
        };
        let res = await this.$http.post(governanceevaluation.getPopUpData, params);
        this.tableData = res.data.data.map((item) => {
          item = { ...item, ...item.data };
          return item;
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>

<style lang="less" scoped>
.video-history-online {
  background: @bg-blue-block;
  background-size: 100% 100%;
  height: 100%;
  padding: 0 15px;

  .evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 48px;
    border-bottom: 1px solid var(--devider-line);

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
      cursor: pointer;
    }

    .evaluation-time {
      color: #a9bed9;
      font-size: 14px;
      margin-left: 10px;

      .mr-x {
        margin-right: 3px;
      }
    }

    .active {
      background: rgba(2, 57, 96, 1);
    }
  }

  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
  @{_deep}.ivu-table-header {
    width: auto;

    &:first-child {
      tr {
        border-top: 1px solid var(--border-color) !important;
        border-left: 1px solid var(--border-color) !important;

        th {
          &:last-child {
            border-bottom: 1px solid var(--border-color) !important;
          }
        }
      }
    }

    tr {
      border-top: 1px solid var(--border-color) !important;
      border-left: 1px solid var(--border-color) !important;

      th {
        border-bottom: 1px solid var(--border-color) !important;
        border-left: 1px solid var(--border-color) !important;

        span {
          color: #a9bed9;
        }

        div {
          color: #a9bed9;
        }
      }
    }
  }
  @{_deep} .ivu-table-fixed-header thead tr th {
    border: 1px solid var(--border-color) !important;
  }

  @{_deep}.ivu-table-tbody {
    table {
      border-collapse: 0 !important;
      border-spacing: 0;
    }
    tr {
      border-right: 1px solid var(--border-color) !important;
      td {
        border-left: 1px solid var(--border-color) !important;
        border-bottom: 1px solid var(--border-color) !important;
      }
    }
  }
}
</style>
