<template>
  <div class="tooltip-container">
    <p class="mb-xs f-14">{{ data[0].axisValue }}</p>
    <div v-for="(item, index) in data" :key="index" class="mb-sm">
      <span class="block" :style="{ 'background-color': item.color }"> </span>
      <span> {{ item.seriesName }}： </span>
      <span> {{ item.data || 0 }}分</span>
      <template v-if="item.taskList && item.taskList.length > 0">
        <p v-for="(it, indexs) in item.taskList" :key="indexs" class="task-content">
          <span> {{ it.name }}： </span>
          <span> {{ it.score || 0 }}分</span>
        </p>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'task-tooltip',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.tooltip-container {
  padding-left: 10px;
  font-size: 12px;
  max-height: 260px;
  padding-right: 20px;
  overflow: auto;
  .block {
    display: inline-block;
    height: 10px;
    width: 10px;
    line-height: 14px;
  }
  .task-content {
    margin-left: 15px;
    line-height: 24px;
  }
}
</style>
