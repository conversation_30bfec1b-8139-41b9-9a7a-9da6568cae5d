<template>
  <ui-card :title="title" class="assets-under-name">
    <div class="assets-under-name-content">
      <UiListCard :showBar="false" :collectIcon='false' class="li" v-for="(item, $index) in list" :key="$index" type="car" :data="item" :index='$index' :active='currentIndex === $index' v-on='$listeners' @archivesDetailHandle='archivesDetailHandle($index)' />
    </div>
    <ui-loading v-if="loading" />
    <ui-empty v-if="(!list || !list.length) && !loading" />
  </ui-card>
</template>
<script>
import UiListCard from '@/components/ui-list-card'
export default {
  components: {
    UiListCard
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      currentIndex: null,
    }
  },
  methods: {
    archivesDetailHandle (index) {
      this.currentIndex = index
      this.$emit("click", this.list[index], index)
    }
  }
}
</script>
<style lang="less" scoped>
.assets-under-name {
  /deep/ .card-content {
    padding: 15px !important;
    min-height: 212px;
    box-sizing: border-box;
  }
  .assets-under-name-content {
    // display: flex;
    overflow-x: auto;
    width: 100%;
    .list-card {
      margin: 0 5px;
    }
  }
}
.li {
  float: left;
  width: 24%;
  margin-top: 10px !important;
}
</style>
