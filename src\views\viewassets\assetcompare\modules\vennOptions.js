import { COLOR_MAP } from '@/views/viewassets/assetcompare/modules/enum';
import { formatNum } from '@/config/filter';

/**
 * 视频监控对账
 * @param opts {Object} {data: {}}
 */
export function assetCompareCardOptions(opts) {
    const {sourceName, targetName} = opts;
  let originData = [
    { label: `独（${sourceName}）`, size: formatNum(opts.data.targetOnly || 0), color: '#D66418' },
    { label: `独（${targetName}）`, size: formatNum(opts.data.sourceOnly || 0), color: '#B1B836' },
    {
      label: '差异数量',
      size: formatNum(opts.data.diff || 0),
      color: '#BC3C19',
      formatter: `\n同:${formatNum(opts.data.same || 0)}`,
    },
  ];
  return {
    legend: false, // 关闭图例
    tooltip: {
      customContent: (title, data) => {
        if (!data.length) return;
        let {
          data: { originData },
        } = data[0];
        let content = [];
        originData.forEach((item) => {
          content.push(`<div style='margin-bottom: 5px'>
                  <span style="display: inline-block;background: ${item.color};height: 6px;width: 6px;border-radius: 50%;vertical-align: middle;margin-right: 5px;"></span>
                  <span>${item.label}:</span>
                  <span style="color: ${item.color}">${item.size}</span>
                </div>`);
        });
        return `<div style='padding: 10px 5px 10px 5px'>
                ${content.join('')}
            </div>`;
      },
    },
    // interactions: [{ type: 'tooltip', enable: false }], // 关闭 tooltip 交互
    data: [
      { sets: ['A'], size: opts.data.targetOnly || 0, label: '独', originData, formatter: '' },
      { sets: ['B'], size: opts.data.sourceOnly || 0, label: '独', originData, formatter: '' },
      {
        sets: ['A', 'B'],
        size: opts.data.same || 0,
        label: '同',
        originData,
        formatter: `\n异:${opts.data.diff || 0}`,
      },
    ],
    label: {
      formatter: ({ label, size, formatter }) => {
        // console.log("val", val); { label, size }
        return `${label}:${formatNum(size || 0)}${formatter}`;
      },
    },
    color: (datum, defaultColor) => {
      return COLOR_MAP[datum.id];
    },
    setsField: 'sets',
    sizeField: 'size',
  };
}

export function supSubCompareOptions(opts){
  let originData = [
    {label: '撤', size: opts.data.finalRevocationNumber || 0, color: COLOR_MAP.A},
    {label: '新', size: opts.data.finalAddedNumber || 0, color: COLOR_MAP.B},
    {label: '修', size: opts.data.finalUpdatedNumber || 0, color: COLOR_MAP['A,B'],},
  ]
  return {
    height: 300,
    autoFit: true,
    legend: false, // 关闭图例
    // interactions: [{ type: 'legend-filter', enable: true }],
    // interactions: [{ type: 'tooltip', enable: false }], // 关闭 tooltip 交互
    tooltip: {
      customContent: (title, data) => {
        if (!data.length) return;
        let { data: {originData} } = data[0];
        let content = [];
        originData.forEach(item => {
          content.push(`<div style='margin-bottom: 5px'>
                  <span style="display: inline-block;background: ${item.color};height: 6px;width: 6px;border-radius: 50%;vertical-align: middle;margin-right: 5px;"></span>
                  <span class='mb-xs'>${item.label}:</span>
                  <span >${item.size}</span>
                </div>`)
        })
        return `<div style='padding: 5px'>
                ${content.join('')}
            </div>`;
      },
    },
    data: [
      { sets: ['A'], size: opts.data.finalRevocationNumber || 0, label: '撤', originData },
      { sets: ['B'], size: opts.data.finalAddedNumber || 0, label: '新', originData },
      { sets: ['A', 'B'], size: opts.data.finalUpdatedNumber || 0, label: '修', originData },
    ],
    label: {
      formatter({ label, size, formatter }) {
        // console.log("val", val); { label, size }
        return `${label}:${size}`;
      },
    },
    color: (datum, defaultColor) => {
      return COLOR_MAP[datum.id];
    },
    setsField: 'sets',
    sizeField: 'size',
  };
}