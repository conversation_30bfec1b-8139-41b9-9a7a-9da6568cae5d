.leftBox{
    position: absolute;
    top: 10px;
    left: 10px;
    // 头部名称
    .title{
        font-size: 16px;
        font-weight: bold;
        color: rgba(0,0,0,0.9);
        height: 40px;
        position: relative;
        line-height: 40px;
        padding-left: 20px;
        border-bottom: 1px solid #D3D7DE;
        display: flex;
        justify-content: space-between;
        align-items: center;
        top: 0;
        z-index: 1;
        background: #fff;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        &:before {
            content: '';
            position: absolute;
            width: 3px;
            height: 20px;
            top: 50%;
            transform: translateY(-50%);
            left: 10px;
            background: #2c86f8;
        }
        span{
            color: #2C86F8; 
        }
        /deep/.ivu-icon-ios-close{
            font-size: 30px;
            cursor: pointer;
        }
    }
    .search-box{
        background: #fff;
        box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
        border-radius: 4px;
        width: 370px;
        filter: blur(0px);
        .search_condition{
            max-height: 620px;
            transition: max-height 0.2s ease-out;
            // overflow: hidden;
            .search_form{
                padding: 10px 15px 10px 20px;
                .search_wrapper{
                    display: flex;
                    flex-wrap: wrap;
                    align-items: center;
                    margin-bottom: 15px;
                    .search_title{
                        font-size: 14px;
                        color: rgba(0, 0, 0, 0.45);
                        margin-right: 10px;
                        display: flex;
                    }
                    .search_strut{
                        // text-align: justify;
                        width: 65px;
                        // text-align-last: justify;
                        span{
                            color: red;
                        }
                    }
                    .search_content{
                        display: flex;
                        flex-wrap: wrap;
                        flex: 1;
                        align-items: center;
                        .active-area-sele{
                            width: 120px;
                            height: 34px;
                            border-radius: 4px;
                            font-size: 14px;
                            text-align: center;
                            line-height: 34px;
                            cursor: pointer;
                            border: 1px dashed #2C86F8;
                            background: rgba(44, 134, 248, 0.10);
                            color: rgba(44, 134, 248, 1);
                        }
                        .area-sele{
                            border: 1px solid #D3D7DE;
                            color: rgba(0,0,0,0.6);
                            background: none;
                        }
                        .area-list{
                            width: 34px;
                            height: 34px;
                            background: #FFFFFF;
                            border-radius: 4px;
                            border: 1px solid #D3D7DE;
                            cursor: pointer;
                            color: rgba(0,0,0,0.6);
                            margin-left: 10px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            img{
                                opacity: 1;
                            }
                        }
                        .active-area-list{
                            border: 1px dashed #2C86F8;
                            background: rgba(44, 134, 248, 0.10);
                            color: rgba(44, 134, 248, 1);
                            img{
                                opacity: .6;
                            }
                        }
                        .analyze_list{
                            font-size: 14px;
                            color: rgba(0, 0, 0, 0.6);
                            padding: 2px 10px;
                            border-radius: 2px;
                            border: 1px solid #D3D7DE;
                            margin-right: 10px;
                            margin-bottom: 10px;
                            cursor: pointer;
                            .active_gradient{
                                display: none;
                            }
                        }
                        .active_analyze_list{
                            color: rgba(44, 134, 248, 1);
                            border: 1px solid rgba(44, 134, 248, 1);
                            position: relative;
                            .active_gradient{
                                position: absolute;
                                width: 12px;
                                height: 12px;
                                background: linear-gradient(315deg, #2C86F8, #2C86F8 50%, transparent 50%, transparent 100%);
                                bottom: 0;
                                right: 0;
                                display: block;
                            }
                            .ivu-icon-ios-checkmark{
                                position: absolute;
                                bottom: -3px;
                                right: -4px;
                                color: #fff;
                             }
                        }
                    }
                    .search_text{
                        color: rgba(0,0,0,0.8);
                        margin-left: 5px;
                        font-size: 14px;
                    }
                }
                .search_special{
                    align-items: flex-start;
                }
                .btn-group{
                    .btnwidth{
                        width: 330px;
                    }
                }
            }
        }
        .search_condition-pack{
            max-height: 0px;
            transition: max-height 0.2s ease-out;
        }
    }
}