<template>
    <div class="result-list-page">
        <p class="search-header">
            <Button class="ml-10" type="primary" size="small" @click="goback(1)">返回</Button>
            <span>共 <span class="t-blue-color">{{currentItem.totalDay || 0}}</span> 条记录</span>
        </p>
        <ul class="result-item" v-scroll>
            <li class="result-item-li result-item-li-second"
                v-show="currentItem.totalDay > 0"
                v-for="(item,index) in currentItem.days" :key="index">
                <div class="result-item-line">
                    <span class="day font-blue">{{item.day}} {{params.startTime.substr(0,2)}}时 至 次日 {{params.endTime.substr(0,2)}}时</span>
                </div>
                <div class="result-item-line result-item-time" @click="gotoDetail(item, 1)">
                    <span>昼出频次:</span>
                    <span class="f-fr">{{item.dayCount}} 次</span>
                </div>

                <div class="result-item-line result-item-time" @click="gotoDetail(item, 0)">
                    <span>夜出频次:</span>
                    <span class="f-fr">{{item.nightlyCount}} 次</span>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
export default {
    name: 'ResultList2',
    props: {
        currentItem: {
            type: Object,
            default: {}
        },
        params: {
            type: Object,
            default: {}
        },
    },
    methods: {
        goback(step) {
            this.$emit('goback', step)
        },
        gotoDetail(item, type) {
            this.$emit('gotoDetail', {item, type})
        }
    }
}
</script>
<style lang="less" scoped>
.result-list-page{
    width: 100%;
    height: 100%;
    position: relative;
    .t-blue-color {
        color: #47a3ff;
    }
    .font-blue {
        color: #47a3ff;
    }
    .f-fr {
        float: right;
    }
    .search-header {
        height: 40px;
        line-height: 40px;
        margin-right: 10px;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .result-item {
        position: absolute;
        top: 35px;
        left: 0;
        right: 0;
        bottom: 0px;
        min-height: 62px;
        overflow: visible;
        .result-item-li {
            padding: 10px;
            border-bottom: 1px solid #d3d7de;
            cursor: pointer;
            overflow: hidden;
            .result-item-line {
                line-height: 30px;
            }
            .result-item-time {
                cursor: pointer;
                &:hover{
                    background-color: #f3f3f3;
                }
            }
        }
    }
}
</style>