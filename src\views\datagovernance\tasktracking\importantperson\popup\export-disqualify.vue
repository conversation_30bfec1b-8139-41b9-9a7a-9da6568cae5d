<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <slot name="unqualifiedheader">
      <ui-select-tabs
        class="tabs-ui"
        v-if="tabsList.length"
        :multiSelect="false"
        @selectInfo="selectInfo"
        :list="tabsList"
      >
      </ui-select-tabs>
    </slot>
    <div class="content-div">
      <ul class="image-tips-ul" v-if="nomalShow">
        <li v-for="(item, index) of nomalLabelList" :key="index">
          <span class="label mr-xs">{{ item.name }}: </span>
          <span
            class="content"
            :class="{
              'font-table-action': index !== nomalLabelList.length - 1,
              'font-red': index === nomalLabelList.length - 1,
            }"
          >
            {{ item.value }}
          </span>
        </li>
      </ul>
      <trail-decide v-else :trail-detail-data="trailDecideData"></trail-decide>
      <loading v-if="errorLoading"></loading>
    </div>
  </ui-modal>
</template>
<script>
// import { overTimeFunc } from "../util/selfutil";
import importantEnums from '../util/enum';
import tasktracking from '@/config/api/tasktracking';
export default {
  props: {
    popUpTitle: {
      default: '非唯一人脸判定',
    },
    disqualifyItem: {
      type: Object,
    },
    value: {},
    title: {
      type: String,
      default: '不合格原因',
    },
    activeBtn: {
      type: String,
      default: '',
    },
    istasktracking: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "1.2rem",
        width: '80%',
      },
      tabsList: [],
      nomalLabelList: [
        { name: '设备类型', value: '' },
        { name: '抓拍时间', value: '' },
        { name: '入库时间', value: '' },
        { name: '结论', value: '' },
      ],
      currentTabObject: {},
      allTabMessageObject: {},
      isImportantDevice: null,
      tableList: [],
      nomalShow: false,
      trailDecideData: {},
      errorLoading: false,
      timeType: ['时', '分', '秒'],
    };
  },
  created() {},
  mounted() {},
  computed: {
    isUnQuatify() {
      return this.tableList.some((item) => {
        return !item.value;
      });
    },
  },
  methods: {
    // 获取人员轨迹检测结果详情
    async queryPersonLibCheckResultDetail() {
      try {
        this.errorLoading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonLibCheckResultDetail, {
          importantPersonLibId: this.disqualifyItem.id,
        });
        // if( !data.data.list.length){
        //     console.log('暂无不合格原因')
        //     return
        // }
        // let fake = [
        //     {componentCode: '10001',errorMessage: '测试2'},
        //     {componentCode: '8009',errorMessage: '测试1'}
        // ]
        // this.handleAllData({ list: fake})
        // this.handleTabData( { list: fake} )
        this.handleAllData(data.data);
        this.handleTabData(data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.errorLoading = false;
      }
    },
    handleTabData(data) {
      this.tabsList = data.list.map((item) => {
        return {
          name: item.errorMessage,
          select: false,
          itemMessage: item,
        };
      });
      if (this.istasktracking) {
        this.selectInfo(this.tabsList[0]);
      }
    },
    selectInfo(item) {
      if (item.itemMessage.componentCode === '8009') {
        this.nomalShow = true;
      } else {
        this.nomalShow = false;
      }
    },
    handleAllData(data) {
      data.list.forEach((element) => {
        // 上传时间超时
        if (element.componentCode === importantEnums.aggregateCodeEnums['图像上传及时性检测']) {
          const overObj = data.list.filter((item) => item.componentCode === '8009');
          const delaytime = `${overObj[0].timeDifference}，超过${
            overObj[0].extraParamObj.important
          }${this.timeType[overObj[0].extraParamObj.importantTime - 1]}`;
          if (!this.istasktracking) {
            this.nomalLabelList = [
              {
                name: '数据类型',
                value: data.isImportantDevice ? '重点人员' : '普通人员',
              },
              { name: '抓拍时间', value: overObj[0].logTime },
              { name: '上传时间', value: overObj[0].createTime },
              { name: '时延', value: delaytime },
            ];
          } else {
            this.nomalLabelList = [
              {
                name: '设备类型',
                value: data.isImportantDevice ? '重点人脸卡口' : '普通人脸卡口',
              },
              { name: '抓拍时间', value: overObj[0].logTime },
              { name: '入库时间', value: overObj[0].createTime },
              { name: '结论', value: delaytime },
            ];
          }

          // this.nomalLabelList = overTimeFunc(
          //   element,
          //   data.isImportantDevice,
          //   this.disqualifyItem
          // );
          // this.nomalShow = true;
        }
        // 人员轨迹照片准确性存疑
        if (element.componentCode === importantEnums.aggregateCodeEnums['人员轨迹准确性检测优化']) {
          this.trailDecideData = element;
          this.trailDecideData.row = this.disqualifyItem;
          // this.nomalShow = false;
        }
      });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.tabsList = [];
        this.nomalLabelList = [];
        this.trailDecideData = {};
        if (this.istasktracking) {
          this.nomalLabelList = [
            { name: '设备类型', value: '' },
            { name: '抓拍时间', value: '' },
            { name: '入库时间', value: '' },
            { name: '结论', value: '' },
          ];
        } else {
          this.nomalLabelList = [
            { name: '数据类型', value: '' },
            { name: '抓拍时间', value: '' },
            { name: '上传时间', value: '' },
            { name: '时延', value: '' },
          ];
        }
        this.queryPersonLibCheckResultDetail();
      }
    },
    value(val) {
      this.visible = val;
    },
    activeBtn() {
      this.nomalShow = this.activeBtn === '8009' ? true : false;
    },
  },
  components: {
    TrailDecide: require('../components/trail-decide.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.image-tips-ul {
  display: flex;
  align-items: center;
  flex-direction: column;
  color: var(--color-content);
  padding: 70px 0 0 0;
  > li {
    height: 40px;
    line-height: 40px;
    width: 300px;
    .label {
      display: inline-block;
      width: 60px;
      text-align: left;
    }
  }
}
.determine-ul {
  color: var(--color-content);
  > li {
    height: 60px;
    line-height: 60px;
    display: flex;
    padding: 0 20px;
    > span {
      flex: 1;
    }
  }
  &:nth-child(even) {
    background: #041939;
  }
  &:nth-child(odd),
  .determine-ul-li-result {
    background: #062042;
  }
  .determine-ul-li-title {
    background: #092955;
  }
  .determine-ul-li-result {
    height: 100px;
    line-height: 100px;
    color: var(--color-primary);
    display: flex;
    > p {
      flex: 1;
    }
  }
  .standard_icon {
    vertical-align: middle;
    font-size: 120px;
    position: relative;
    .icon_text_error {
      font-size: 16px;
      position: absolute;
      right: 37px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
    .icon_text_succeed {
      font-size: 16px;
      position: absolute;
      right: 44px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
  }
}
.tabs-ui {
  margin-bottom: 20px;
}
.content-div {
  height: 650px;
  position: relative;
}
.url-p {
  text-align: center;
  padding-top: 150px;
}
@{_deep} .ivu-modal {
  &-body {
    padding: 20px 50px !important;
  }
}
</style>
