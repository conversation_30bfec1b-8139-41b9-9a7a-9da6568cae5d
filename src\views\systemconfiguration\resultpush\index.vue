<template>
  <div class="result-push">
    <div class="content">
      <div class="title">
        <span class="main-title">人脸与车辆卡口推送</span>
        <!-- <span class="subtitle">CONFIGURATION</span> -->
      </div>
      <ui-label label="检测任务">
        <Select
          v-model="taskSchemeId"
          placeholder="请选择检测任务"
          class="width-lg mb-lg"
          @on-change="selectTask"
          filterable
        >
          <Option v-for="(item, index) in taskList" :key="index" :label="item.taskName" :value="item.id">
            {{ item.taskName }}
          </Option>
        </Select>
      </ui-label>
      <ui-label label="评测指标">
        <Select
          class="width-lg mb-lg"
          placeholder="请选择评测指标"
          v-model="pushData.taskIndexIdList"
          multiple
          filterable
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in indexList" :key="index" :value="item.id">{{ item.indexName }} </Option>
        </Select>
      </ui-label>
      <Button class="button" type="primary" :loading="loading" @click="query">一键推送</Button>
      <div class="desc-title">推送说明：</div>
      <div class="desc">
        推送资产库中所有人脸卡口设备和车辆卡口设备（功能类型为人脸卡口或车辆卡口的设备）；任何一个指标检测不合格，设备标记为【不合格】状态推送；有检测合格且没有不合格记录的设备，标记为【合格】状态推送；无法检测（如：没有抓拍图片）与没有检测记录的设备，标记为【未检测】状态推送。
      </div>
    </div>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'resultpush',
  props: {},
  data() {
    return {
      loading: false,
      taskList: [],
      indexList: [],
      taskSchemeId: '',
      pushData: {
        taskName: '',
        taskIndexIdList: [],
      },
    };
  },
  created() {
    this.getListTaskSchemes();
  },
  methods: {
    async getListTaskSchemes() {
      try {
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getListTaskSchemes, {
          isDel: 0,
        });
        this.taskList = data || [];
      } catch (e) {
        console.log(e);
      }
    },
    // 下拉根据考核任务查询
    async getOptionalResultsByTaskType() {
      try {
        let params = {
          taskSchemeId: this.taskSchemeId,
        };
        let res = await this.$http.post(governanceevaluation.getTaskIndexGeneralConfig, params);
        this.indexList =
          res.data.data.indexConfigData.filter(
            (row) => !!row.id && (row.indexModule === '2' || row.indexModule === '3'),
          ) || [];
      } catch (err) {
        console.log(err);
      }
    },
    async query() {
      try {
        this.loading = true;
        const params = {
          taskName: this.pushData.taskName,
          taskIndexIdList: this.pushData.taskIndexIdList.map((id) => {
            return {
              taskIndexId: id,
              indexModel: this.indexList.find((row) => row.id === id).indexModule,
            };
          }),
        };
        const res = await this.$http.post(evaluationoverview.sharePushResult, params);
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    selectTask() {
      const task = this.taskList.find((row) => row.id === this.taskSchemeId);
      this.pushData.taskName = task.taskName;
    },
  },
  watch: {
    taskSchemeId() {
      this.pushData.taskIndexIdList = [];
      this.getOptionalResultsByTaskType();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .result-push {
    background: url('~@/assets/img/home/<USER>/home-config.png') no-repeat;
    .title {
      background: url('~@/assets/img/home/<USER>/home-config-title-dark.png') no-repeat;
    }
  }
}
[data-theme='light'] {
  .result-push {
    .title {
      background: url('~@/assets/img/home/<USER>/home-config-title-light.png') no-repeat;
    }
  }
}
[data-theme='deepBlue'] {
  .result-push {
    .title {
      background: url('~@/assets/img/home/<USER>/home-config-title-deepBlue.png') no-repeat;
    }
  }
}
.result-push {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background-size: cover;
  background: url('~@/assets/img/home/<USER>/home-config-light.png') no-repeat;
  .content {
    position: absolute;
    top: 20%;
  }
  .title {
    position: relative;
    height: 60px;
    width: 427px;
    background-size: cover;
    z-index: 1;
    margin-bottom: 20px;
    .main-title {
      display: inline-block;
      font-size: 26px;
      font-weight: bold;
      line-height: 20px;
      color: #ffffff;
      text-shadow: 0 0 10px #60a3f9;
      padding: 27px 0 6px 75px;
      z-index: 2;
    }
    .subtitle {
      display: inline-block;
      font-size: 26px;
      font-weight: bold;
      line-height: 20px;
      color: #04102a;
      position: absolute;
      left: 27%;
      top: 57%;
      z-index: -2;
    }
  }
  .button {
    width: 100%;
    margin-bottom: 50px;
  }
  .desc-title {
    font-weight: 700;
    color: var(--color-input);
  }
  .desc {
    width: 427px;
    color: var(--color-input);
  }
}
</style>
