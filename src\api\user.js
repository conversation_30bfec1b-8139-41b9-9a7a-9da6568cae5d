import request from "@/libs/request";
import { auth, service, manager } from "./Microservice";

// 登录
export function userLogin(data) {
  return request({
    url: auth + "/oauth/token",
    method: "post",
    data,
  });
}
// 刷新token
export function updateToken(data) {
  return request({
    url: auth + "/oauth/token?grant_type=refresh_token",
    method: "post",
    data,
  });
}
// 退出登录
export function userLogout(data) {
  return request({
    url: auth + "/token/logout",
    method: "delete",
    data,
  });
}
// 获取用详情
export function getUserInfo() {
  return request({
    url: service + "/user/info",
    method: "get",
  });
}

// 根据系统参数值获取系统参数数据
export function getParamDataByKeys(data) {
  return request({
    url: service + "/token/getParamDataByKeys",
    method: "get",
    params: data,
  });
}
// 不同登录方式
export function getLoginType(data) {
  return request({
    url: service + "/token/getLoginType",
    method: "get",
  });
}

// 根据单个字典编码获取字典列表
export function queryByTypeKey(data) {
  return request({
    url: service + "/dictData/queryByTypeKey",
    method: "get",
    params: data,
  });
}
// 根据多个字典编码批量获取字典列表
export function queryDataByKeyTypes(data) {
  return request({
    url: service + "/dictData/queryDataByKeyTypes",
    method: "post",
    data,
  });
}
// 查询个人用户信息
export const getPersonalInfo = ({ id }) => {
  return request({
    url: `${service}/user/personalInfo/${id}`,
    method: "get",
  });
};

// 修改用户信息（个人中心）
export const updatePersonalInfo = (data = {}) => {
  return request({
    url: service + "/user/updatePersonalInfo",
    method: "post",
    data,
  });
};

// 修改用户密码
export const updatePersonalPsw = (data) => {
  return request({
    url: service + "/user/updatePersonalPsw",
    method: "post",
    data,
  });
};

// 获取授权证书
export const getLicenseInfo = (data = {}) => {
  return request({
    url: auth + `/license/getLicenseInfo?applicationCode=${applicationCode}`,
    method: "get",
    data,
  });
};

// 获取授权通知天数
export const getAuthorNoticeDay = (data = {}) => {
  return request({
    url: `/qsdi-system-service/token/getParamDataByKeys?key=AUTH_NOTICE`,
    method: "get",
    data,
  });
};

// 获取授权跳转地址
export const getAuthorUrl = (data = {}) => {
  return request({
    url: `/qsdi-system-service/token/getParamDataByKeys?key=AUTH_URL`,
    method: "get",
    data,
  });
};

// 添加收藏
export function addCollection(data) {
  return request({
    url: manager + "/myFavorite/addMyFavorite",
    method: "post",
    data,
  });
}

// 取消收藏
export function deleteMyFavorite(data) {
  return request({
    url: manager + "/myFavorite/deleteMyFavorite",
    method: "post",
    data,
  });
}

// 获取当前系统信息
export const getApplicationInfo = (data = {}) => {
  return request({
    url:
      service + `/token/getApplicationInfo?applicationCode=${applicationCode}`,
    method: "get",
    data,
  });
};
// 组织机构
export function queryUserDatascopeList(data) {
  return request({
    url: service + "/user/queryUserDatascopeList",
    method: "post",
    data,
  });
}
//  全量组织机构
export function queryUserDatascopeListNew(data) {
  return request({
    url: service + "/user/queryUserDatascopeListNew",
    method: "post",
    data,
  });
}
// 用户列表
export function userPageList(data) {
  return request({
    url: service + "/user/pageList",
    method: "post",
    data,
  });
}
export function userPageListNew(data) {
  return request({
    url: service + "/user/pageListNew",
    method: "post",
    data,
  });
}
// 角色列表
export function rolePageList(data) {
  return request({
    url: service + "/system/role/pageList",
    method: "post",
    data,
  });
}
// 日志
export function logRecord(data) {
  return request({
    url: service + "/system/operate/log/record",
    method: "post",
    data,
  });
}
// 绑定用户名的ip、mac
export function bindIpMac(data) {
  return request({
    url: service + "/user/bindIpMac",
    method: "post",
    notCheckCode: true,
    data,
  });
}
// 根据用户名、ip、mac校验绑定信息
export function checkSafeExist(data) {
  return request({
    url: service + "/user/checkSafeExist",
    method: "post",
    notCheckCode: true,
    data,
  });
}
// ------------------个性设置---------------
//  查询用户个性化参数配置分页列表
export function settingQuery(data) {
  return request({
    url: manager + "/user/param/query",
    method: "post",
    data,
  });
}
// 用户个性化参数配置新增
export function settingAdd(data) {
  return request({
    url: manager + "/user/param/add",
    method: "post",
    data,
  });
}
//用户个性化参数配置编辑
export function settingUpdate(data) {
  return request({
    url: manager + "/user/param/update",
    method: "put",
    data,
  });
}
