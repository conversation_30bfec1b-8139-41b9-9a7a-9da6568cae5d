export const componentsMap = {
  // 档案数据
  ARCHIVES_PORTRAIT_CONFIDENCE_RATE: 'portraitFileConfidenceRate', //人像档案置信率
  ARCHIVES_VEHICLE_CONFIDENCE_RATE: 'vehicleFileConfidenceRate', //车辆档案置信率
  BASIC_DEVICE_HUNDRED: 'basic-device-hundred', //设备百人占比率
  VIDEO_DEVICE_REVOCATION: 'video-device-revocation',
  FOCUS_REPEAT_RATE: 'video-device-revocation',
  BASIC_FULL_DIR: 'basic-full-dir',
  BASIC_QUANTITY_STANDARD: 'basic-quantity-standard',
  FACE_QUANTITY_STANDARD: 'basic-quantity-standard',
  VEHICLE_QUANTITY_STANDARD: 'basic-quantity-standard',
  VIDEO_QUANTITY_STANDARD: 'basic-quantity-standard',
  BASIC_EMPHASIS_QUANTITY: 'basic-emphasis-quantity',
  FACE_URL_AVAILABLE: 'face-url-available',
  FACE_FOCUS_URL_AVAILABLE: 'face-url-available',
  VEHICLE_URL_AVAILABLE: 'face-url-available',
  VEHICLE_URL_AVAILABLE_IMPORTANT: 'face-url-available',
  FACE_CAPTURE_PASS: 'face-url-available',
  FACE_CAPTURE_SCORE: 'face-url-available',
  FACE_UPLOAD: 'face-url-available',
  FACE_FOCUS_UPLOAD: 'face-url-available',
  FACE_CLOCK: 'face-url-available',
  FACE_CAPTURE_COMPLETENESS_RATE: 'face-url-available',
  FACE_DATA_COMPLY_RULE_RATE: 'face-url-available',
  BODY_CLOCK: 'face-url-available', // 人体卡口设备时钟准确率
  BODY_UPLOAD: 'face-url-available', // 人体卡口设备及时上传率
  VEHICLE_FULL_INFO: 'face-url-available',
  VEHICLE_FULL_INFO_IMPORTANT: 'face-url-available',
  VEHICLE_INFO_PASS: 'face-url-available',
  VEHICLE_INFO_PASS_IMPORTANT: 'face-url-available',
  VEHICLE_CLOCK: 'face-url-available',
  VEHICLE_MAIN_PROP: 'face-url-available',
  VEHICLE_TYPE_PROP: 'face-url-available',
  VEHICLE_UPLOAD: 'face-url-available',
  VEHICLE_UPLOAD_IMPORTANT: 'face-url-available',
  VEHICLE_DATA_CONSISTENCY: 'face-url-available',
  VEHICLE_UPLOAD_COMPLIANCE: 'face-url-available',
  VEHICLE_URL_AVAILABLE_RECHECK: 'face-url-available',
  VIDEO_VALID_SUBMIT_QUANTITY: 'video-valid-submit-quantity',
  COMPOSITE_INDEX: 'composite-index',
};
