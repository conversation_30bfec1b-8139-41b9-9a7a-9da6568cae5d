<!--
    * @FileDescription: 人员档案 - 实名档案-列表
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div 
        class="card-container" 
        :class="{'cursor': showConfidence}" 
        @click="handleClick()" draggable="false" 
        @dragstart="$emit('dragstart', cardData)" 
        @dragend="$emit('dragend', cardData)">
        <div class="card-body">
			<div class="image-wrapper mr-sm">
				<!-- <image-swiper class="image" :srcKey="cardData.srcKey" :src="cardData.src" @on-change="onChangeSwiper" /> -->
                <swiper ref="mySwiper" :options="swiperOption" class="my-swiper" :id="`swipe${index + 1}`">
                    <swiper-slide v-for="(item, index2) in cardData.photoUrlList" :key="index2">
                        <ui-image :src="item.photoUrl" viewer/>
                        <div v-if="item.score && isPercentage" class="similarity">
                            <span>{{item.score}}{{ getScore(item.score) }}%</span>
                        </div>
                    </swiper-slide>
                </swiper>
				<div class="image-percentage" v-if="isPercentage">
					<slot name="percentage" :row="cardData"><span class="f-12">98%</span></slot>
				</div>
			</div>
			<div class="detail-wrapper">
				<div v-for="(item, index) in cardColumns" :key="index" class="wrapper-box f-14 multiple-ellipsis d_flex">
					<!-- <span class="icon-font detail-icon vt-middle" :class="item['icon']" /> -->
                    <i class="iconfont mr-5" :class="item['icon']"></i>
                    <!-- <span class="detail-value ml-xs ellipsis inline" v-if="item['key'] == 'sex'" :title="commonFiltering(cardData[item['key']], store.ipbdFaceCaptureGender)">
						{{ commonFiltering(cardData[item['key']], store.ipbdFaceCaptureGender)}}
                        {{ data[Object.keys(item)[0]] | commonFiltering(genderList) }}
					</span> -->
                    <span class="detail-value ml-xs ellipsis inline" v-if="item['key'] == 'sex'" :title="cardData[item['key']] | commonFiltering(genderList)">
                        {{ cardData[item['key']] | commonFiltering(genderList) }}
					</span>
					<p class="detail-value ml-xs ellipsis inline" v-else-if="item['key'] == 'idCardNo'">
						<Tooltip :content="cardData[item['key']] || '-'" placement="bottom" @click.stop="() => {return}">
							{{ cardData[item['key']] || '-' }}
						</Tooltip>
					</p>
					<span class="detail-value ml-xs ellipsis inline" v-else :title="cardData[item['key']]">{{ cardData[item['key']] || '-' }}</span>
				</div>
			</div>
			<div v-if="showConfidence" class="detail-confidence">
                <span class="f-14 inline">有轨迹</span>
			</div>
		</div>
        <div class="card-tag">
			<ui-tag-poptip v-if="cardData.tagList && cardData.tagList.length !== 0" :data="cardData.tagList" />
		</div>
    </div>
</template>
<script>
import { swiper, swiperSlide } from 'vue-awesome-swiper';
export default {
    components: { swiper, swiperSlide },
    props: {
        cardData:{
            type: Object,
            default: () => {
                return {}
            }
        },
        isPercentage: {
            type: Boolean,
            default: false
        },
        cardColumns:{
            type: Array,
            default: () => {
                return []
            }
        },
        index: {
            type: Number,
            default: 0
        },
    },
    data() {
        return {
            swiperOption: {
                direction: 'horizontal'
            }
        }
    },
    computed: {
        showConfidence(){
            if (this.cardData.confidenceSource) {
                return true
            } else {
                return false
            }
        }
    },
    created() {

    },
    methods: {
        handleClick () {
            if(this.showConfidence){
                this.$emit('click', this.cardData);
            }
        },
        onChangeSwiper(oldIndex, index) {
		    this.$emit('on-change', oldIndex, index, this.cardData)
	    }
    }
}
</script>
<style lang="less" scoped>
.card-container{
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    width: 348px;
    height: 200px;
    box-sizing: border-box;
    border-radius: 4px;
    border: 1px solid #d3d7de;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    background: #f9f9f9;
    &:before {
        content: "";
        background: url("../../../../../assets/img/card-bg/idcard.png") no-repeat;
        background-size: cover;
        position: absolute;
        width: 131px;
        height: 103px;
        right: 0px;
        bottom: 0px;
    }
    &:hover {
        border: 1px solid #2c86f8;
    }
    .card-body {
        position: relative;
        flex: 1;
        // overflow: hidden;
        display: flex;
        width: 100%;
        //height: calc(100% - 30px);
        padding: 10px 10px 0 10px;
        .image-wrapper {
            position: relative;
            width: 116px;
            height: 144px;
            border: 1px solid #cfd6e6;
            .swiper-container {
                width: 100%;
                height: 100%;
                .swiper-wrapper .swiper-slide {
                    width: 116px;
                    height: 144px;
                    text-align: center;
                    & > img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        cursor: pointer;
                    }
                }
            }
            .swiper-pagination {
                bottom: 0px;
                width: 100%;
            }
            /deep/.swiper-pagination-bullet {
                width: 6px;
                height: 6px;
                margin-left: 5px;
                background: #fff;
                opacity: 1;
            }
            /deep/.swiper-pagination-bullet-active {
                background: #2c86f8;
            }
            .image {
                height: 100%;
                width: 100%;
            }
            .image-percentage {
                position: absolute;
                z-index: 2;
                left: 0;
                top: 0;
                min-width: 37.44px;
                height: 20px;
                color: #fff;
                text-align: center;
                border-bottom-left-radius: 4px;
                background: linear-gradient(237deg, #27d676 7%, #36be7f 92%);
            }
        }
        .detail-wrapper {
            overflow-y: auto;
            flex: 1;
            .detail-icon {
                color: #888888;
            }
            .detail-value {
                color: rgba(0, 0, 0, 0.8);
            }
            .wrapper-box {
                align-items: center;
            }
        }
        .detail-confidence {
            position: absolute;
            top: 10px;
            right: 0;
            //width: 58px;
            height: 24px;
            line-height: 24px;
            padding: 0 10px;
            background: linear-gradient(243deg, #27d676 7%, #36be7f 91%);
            color: #fff;
            font-weight: bold;
            border-bottom-left-radius: 12px;
            border-top-left-radius: 12px;
        }
    }
    .card-tag {
        padding: 7px 10px 10px 10px;
    }
    .f-30 {
        font-size: 30px;
    }
}
.cursor {
    cursor: pointer;
}
.d_flex{
    display: flex;
}
.mr-sm{
    margin-right: 10px;
}
</style>
