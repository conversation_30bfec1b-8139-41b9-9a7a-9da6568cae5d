<template>
  <div class="governancetoolset">
    <div v-if="!createtabComponents.includes(activeComponent)" class="governancetoolset-component">
      <div class="governancetoolset-component-container">
        <div v-for="(item, index) in cardData" :key="index">
          <!-- 头 -->
          <div class="based-field-header">
            <i :class="['icon-font', 'font-blue', item.icon]"></i>
            <div class="title-header">{{ item.name }}</div>
          </div>
          <div class="card-list">
            <template v-for="(cardItem, index) in item.data">
              <create-tabs
                v-if="!!cardItem.activeName"
                class="card-list-item card-item-tabs"
                :key="'cardItem' + index"
                :component-name="cardItem.activeName"
                :tabs-text="cardItem.title"
                @selectModule="selectModule"
              >
                <card-item :cardData="cardItem" :card-bg-color="cardBgColor"></card-item>
              </create-tabs>
              <card-item
                v-else
                :key="'cardItem' + index"
                class="card-list-item"
                :cardData="cardItem"
                :card-bg-color="cardBgColor"
                @handleJump="jump"
                @handleDownLoad="downLoadFn"
              ></card-item>
            </template>
          </div>
        </div>
      </div>
    </div>
    <keep-alive :include="cacheRouterName">
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import governancetoolset from '@/config/api/governancetoolset';
import dealWatch from '@/mixins/deal-watch';
import { toolSetData } from './toolSetField';
// 简单搭下治理工具初始页面
export default {
  mixins: [dealWatch],
  name: 'governancetoolset',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
      toolsUrl: null, // 存放下载路径
      cardData: [],
      cardBgColor: 'linear-gradient(180deg, rgba(42, 94, 162, 0.61) 0%, rgba(18, 54, 109, 0.61) 100%)',
      createtabComponents: [
        'ClockInformationDetection',
        'ControlInformationDetection',
        'GBCodeDetectionManagement',
        'ReportGeneration',
        'CoordinateTransform',
      ], // 需要使用标签页打开的组件
      activeComponent: null,
    };
  },
  created() {
    this.getParams();
    if (document.documentElement.getAttribute('data-theme') === 'dark') {
      this.cardBgColor = 'linear-gradient(180deg, rgba(42, 94, 162, 0.61) 0%, rgba(18, 54, 109, 0.61) 100%)';
    } else {
      this.cardBgColor = '#fff';
    }
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        this.getParams();
      },
      { immediate: true },
    );
  },
  mounted() {
    this.cardData = this.$util.common.deepCopy(toolSetData);
    this.selectToolsetUrl();
  },
  methods: {
    async selectToolsetUrl() {
      let res = await this.$http.get(governancetoolset.selectToolsetUrl);
      this.toolsUrl = res.data;
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    //
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.activeComponent = this.$route.query.componentName;
      if (this.createtabComponents.includes(this.activeComponent)) {
        this.selectModule(this.$route.query.componentName);
      }
    },
    // OSD字幕设置、时钟信息重设、MAC地址自动获取 三合一下载功能
    downLoadFn() {
      if (!this.toolsUrl) {
        this.$Message.warning('暂时无下载文件产生');
        return;
      }
      this.$Message.success('请求成功，等待下载');
      window.open(this.toolsUrl, '_self');
    },
    jump(item) {
      if (item.jumpType === 'app') {
        window.open(this.openManagerAppUrl);
        return;
      } else if (item.jumpType === 'feedback') {
        window.open('/feedback.zip');
        return;
      }
    },
  },
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
      openManagerAppUrl: 'user/getManagerAppUrl',
    }),
    cacheRouterName() {
      let cacheList = [];
      this.cacheRouterList.forEach((row) => {
        let arr = row.path.split('/');
        // 去除首个空路由
        arr.splice(0, 1);
        arr.push(row.name);
        // 缓存路由为标签路由列表与当前所有路由
        cacheList = [...cacheList, ...arr];
      });
      cacheList = Array.from(new Set(cacheList));
      return cacheList;
    },
  },
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    ClockInformationDetection: require('./data-detection-tool/clock-information-detection').default,
    ReportGeneration: require('./other-tool/report-generation/index.vue').default,
    ControlInformationDetection: require('./data-detection-tool/control-information-detection').default,
    GBCodeDetectionManagement: require('./data-detection-tool/GB-code-detection-management').default,
    CardItem: require('./components/card-item').default,
    Information: require('@/views/datagovernance/onlinegovernance/information/index').default,
    CoordinateTransform: require('./data-detection-tool/coordinate-transform').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .card-list-item {
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
    border: 1px solid #d3d7de;
    background: #f9f9f9;
    border-radius: 4px;
  }
}
.card-list-item:hover {
  border: 2px solid var(--color-primary);
}
.governancetoolset {
  height: 100%;

  .governancetoolset-component {
    height: 100%;
    overflow: auto;
    background: var(--bg-content);

    .governancetoolset-component-container {
      padding: 10px 15px 0 15px;
    }

    .card-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      width: 100%;

      &-item {
        width: calc((100% - 50px) / 5);
        margin: 0 5px 10px;
      }
      .card-item-tabs{
        & > .card-item{
          height: 132px;
        }
      }

      &:after {
        content: '';
        display: block;
        flex: 1;
      }
    }

    .based-field-header {
      display: flex;
      margin: 0 5px 10px;
      border-bottom: 1px solid var(--border-modal-footer);
      align-items: center;

      .icon-font {
        font-size: 20px;
        color: #fff;
        margin-right: 6px;
      }

      .title-header {
        color: var(--color-content);
        font-size: 16px;
        font-weight: bold;
        line-height: 20px;
      }
    }

    .governancetoolset-testing-ul,
    .governancetoolset-government-ul {
      margin: 0 -5px;

      .governancetoolset-component-card {
        width: 25%;
        display: inline-block;
        margin-bottom: 8px;
        height: 185px;
        cursor: pointer;
        overflow: hidden;
        padding: 0 3px;
        position: relative;
        // & > div:hover {
        // 	border: 2px solid #239df9;
        // }
        .governancetoolset-component-card-item {
          width: 100%;
          height: 100%;
          border: 2px solid transparent;
          display: flex;
          flex-wrap: nowrap;
          position: relative;
          border-radius: 4px;
          overflow: hidden;

          .governancetoolset-component-card-item-body {
            width: 100%;
            height: 100%;
            display: flex;
            flex-wrap: nowrap;
            padding: 52px 40px 0 40px;
            background: url('../../assets/img/datagovernance/governancet_tool_set_bg1.png') no-repeat center/cover;
            position: relative;
            border-radius: 4px;
            overflow: hidden;

            .img {
              width: 88px;
              min-width: 88px;
              height: 88px;
              min-height: 88px;
              border-radius: 50%;
              background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
              margin-right: 34px;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              color: #f5f5f5;

              & > i {
                font-size: 52px;
              }
            }

            .content {
              .title {
                color: var(--color-active);
                margin-bottom: 10px;
                font-size: 16px;
                font-weight: bold;
                line-height: 22px;
              }

              .result {
                color: #c4d5e6;
                font-size: 14px;
                line-height: 22px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
              }
            }
          }

          .governancetoolset-component-card-item-body::after {
            content: '';
            width: 100%;
            height: 10px;
            background: linear-gradient(180deg, #4ba0f5 0%, #0c44a7 100%);
            position: absolute;
            top: 0;
            left: 0;
          }
        }

        .governancetoolset-component-card-item:hover {
          border: 2px solid #61eef8;
        }
      }
    }

    .governancetoolset-government-ul {
      .governancetoolset-component-card {
        .governancetoolset-component-card-item {
          .governancetoolset-component-card-item-body {
            background: url('../../assets/img/datagovernance/governancet_tool_set_bg2.png') no-repeat center/cover;

            .img {
              background: linear-gradient(180deg, #3dcff5 0%, #0f9789 100%);
            }

            .content {
              .title {
                color: #19d5f6;
              }
            }

            .icon-xiazai-01 {
              position: absolute;
              bottom: 0;
              right: 0;
              font-size: 22px;
              line-height: 22px;
              color: #19d5f6;
              padding: 10px;
            }

            .icon-xiazai-01:hover {
              color: #0fabb7;
            }
          }

          .governancetoolset-component-card-item-body::after {
            background: linear-gradient(180deg, #3dcff5 0%, #0f9789 100%);
          }
        }
      }
    }
  }

  .governancetoolset-child-component {
    height: 100%;
  }
}
</style>
