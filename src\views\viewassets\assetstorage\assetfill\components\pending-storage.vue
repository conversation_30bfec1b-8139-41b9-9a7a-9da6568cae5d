<template>
  <div class="pending-storage">
    <ui-label class="inline mr-lg" label="关  键  词">
      <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入设备编码/名称/IP/MAC"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="入库检测状态">
      <Select
        class="width-md"
        v-model="searchData.checkStatus"
        placeholder="请选择入库检测状态"
        clearable
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in checkStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="审核状态">
      <Select
        class="width-md"
        v-model="searchData.examineStatus"
        placeholder="请选择审核状态"
        clearable
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in examineStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="比对状态">
      <Select
        class="width-md"
        v-model="searchData.contrastStatus"
        placeholder="请选择比对状态"
        clearable
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in contrastStatusList" :key="index" :value="item.dataKey"
          >{{ item.dataValue }}
        </Option>
      </Select>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="填  报  人">
      <Input v-model="searchData.userName" class="width-md" placeholder="请输入填报人"></Input>
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="填报时间">
      <DatePicker
        class="width-md"
        v-model="searchData.startTime"
        type="datetime"
        placeholder="请选择开始时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'startTime')"
        :options="startTimeOption"
        confirm
      />
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.endTime"
        type="datetime"
        placeholder="请选择结束时间"
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
        :options="endTimeOption"
        confirm
      />
    </ui-label>
    <ui-label class="inline mb-sm mr-lg" label="入库标识">
      <Input v-model="searchData.putMark" class="width-md" placeholder="请输入入库标识"></Input>
    </ui-label>
  </div>
</template>
<script>
export default {
  props: {},
  data() {
    return {
      selectOrgTree: {
        orgCode: null,
      },
      selectTree: {
        regionCode: '',
      },
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let startTime = new Date(this.searchData.startTime);
          if (startTime) {
            return date < startTime;
          }
          return false;
        },
      },
      checkStatusList: [
        {
          dataValue: '待检测',
          dataKey: '0',
        },
        {
          dataValue: '合格',
          dataKey: '1',
        },
        {
          dataValue: '不合格',
          dataKey: '2',
        },
      ],
      examineStatusList: [
        {
          dataValue: '待审核',
          dataKey: '0',
        },
        {
          dataValue: '审核通过',
          dataKey: '1',
        },
        {
          dataValue: '审核不通过',
          dataKey: '2',
        },
      ],
      contrastStatusList: [
        {
          dataValue: '未对比',
          dataKey: '0',
        },
        {
          dataValue: '对比中',
          dataKey: '1',
        },
        {
          dataValue: '已对比',
          dataKey: '2',
        },
      ],
      searchData: {
        keyWord: '',
        checkStatus: '',
        examineStatus: '',
        contrastStatus: '',
        startTime: '',
        endTime: '',
        userName: '',
        putMark: '',
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    reset() {
      this.resetSearchDataMx(this.searchData);
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.pending-storage {
  display: inline;
}
</style>
