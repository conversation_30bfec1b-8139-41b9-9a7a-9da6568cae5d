<template>
  <div style="width: 90%; height: 200px" id="echart_bar"></div>
</template>
<script>
export default {
  name: 'barEchart',
  props: {
    barEchartList: {
      type: Object,
      default() {
        return { title: 'name', value: 'value', list: [] };
      },
    },
  },
  data() {
    return {};
  },
  mounted() {},
  watch: {
    barEchartList: {
      deep: true,
      handler() {
        if (this.barEchartList.list.length > 0) {
          this.init();
        }
      },
    },
  },
  methods: {
    init() {
      let VALUE = [];
      let TITLE = [];
      this.barEchartList.list.map((item) => {
        VALUE.push(item[this.barEchartList.value]);
        TITLE.push(item[this.barEchartList.title]);
      });
      let myChart = this.$echarts.init(document.getElementById('echart_bar'));
      var option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        grid: {
          top: '20%',
          right: '3%',
          left: '5%',
          bottom: '10%',
          containLabel: true,
        },
        xAxis: [
          {
            // type: 'category',
            data: TITLE,
            axisLine: {
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              margin: 20,
              color: '#e2e9ff',
              fontSize: 12,
              rotate: 8,
            },
          },
        ],
        yAxis: [
          {
            name: '单位：条',
            nameTextStyle: {
              color: '#fff',
              fontSize: 12,
              padding: [0, 20, 0, 0],
            },
            axisLabel: {
              formatter: function (value) {
                return value;
              },
              color: '#fff',
            },
            axisLine: {
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        series: [
          {
            type: 'bar',
            data: VALUE,
            barWidth: '50px',
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: '#00D5F7', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: '#1B6EC7', // 100% 处的颜色
                  },
                ],
                false,
              ),
            },
          },
        ],
      };

      myChart.setOption(option, true);
    },
    resizeFn() {
      this.init();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  position: absolute;
  width: 100%;
  height: 100%;
}
</style>
