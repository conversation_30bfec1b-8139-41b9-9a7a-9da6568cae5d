<template>
  <ui-modal v-model="visible" title="错误原因" :styles="styles" :footer-hide="footerHide" @onCancel="cancel">
    <ui-table
      class="ui-table auto-fill"
      ref="tableRef"
      :table-columns="errorColumns"
      :table-data="errorData"
      :minus-height="300"
    >
    </ui-table>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </ui-modal>
</template>
<script>
export default {
  props: {
    value: {
      type: Boolean,
    },
    errorData: {
      type: Array,
    },
    errorColumns: {
      type: Array,
    },
    footerHide: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '5rem',
      },
    };
  },
  created() {},
  methods: {
    cancel() {
      this.$emit('cancel');
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
