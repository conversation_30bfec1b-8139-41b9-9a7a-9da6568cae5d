let baseWidth = '20.8%';
let baseHeight = '33.3%';
let baseLeftRight = '1.6%';
export const longEchartPosition = {
  '3': {
    bottom: '0%',
    left: '23.4%',
    width: '53.2% ', // 长图表： 53.2%     正常：26.6%
    height: '33%',
  },
};
export const normalEchartPosition = {
  '3': {
    bottom: '0%',
    left: '23.4%',
    width: '26.6%', // 长图表： 53.2%     正常：26.6%
    height: '33%',
  },
  '4': {
    bottom: '0%',
    right: '23.4%',
    width: '26.6%',
    height: '33%',
  },
};
export const baseHomePosition = {
  '0': {
    top: '0%',
    left: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
  '1': {
    top: '33.3%',
    left: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
  '2': {
    top: '66.6%',
    left: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
  '5': {
    top: '66.6%',
    right: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
  '6': {
    top: '33.3%',
    right: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
  '7': {
    top: '0%',
    right: baseLeftRight,
    width: baseWidth,
    height: baseHeight,
  },
};
/**
 * 需要有操作的echarts配置，统一在empty-occupy文件中设置：具体用法看empty-occupy文件注释
 * 默认配置
 * 1. hasBackButton
 * 2. hasDatePicker: true, // 会有选择日期显示
 * 2. dateType:'year', // hasDatePicker为true时，DatePicker组件显示的type
   3. hasIndexChoose: true, // index-choose组件展示
   4. hasIndexModuleChoose: true, // 会有index-module-choose筛选展示
   5. isLongEchart：true, // 长图表，占据 底部两个dom
   6. hasFilterSelect: true, // 自定义下拉 筛选条件
   7. hasMoreButton: true // 会有 【 更多>> 】  显示
   8. isShowTip: true, //是否显示提示  为true时需要  indexId indexName indexType
   9. indexId: '1008',
      indexName: '视频图像采集区域数量达标率',
      indexType: 'BASIC_CJQY_QUANTITY_STANDARD',   额外字段 不影响配置
 * 
 */
const defaultConfigEnum = {
  DeviceNumber: {
    name: '设备数量',
    componentId: 'DeviceNumber',
    imgUrl: require('@/assets/img/home/<USER>/shebeishuliang.png'),
    visiualShow: true,
    styleIndex: '0', // 可变
  },
  CollectionArea: {
    name: '采集区域类型',
    componentId: 'CollectionArea',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/caijiquyuleixing.png'),
    imgUrl2: require('@/assets/img/home/<USER>/caijiquyuleixing.png'),
    styleIndex: '0',
    isShowTip: true,
    indexId: '1008',
    indexName: '视频图像采集区域数量达标率',
    indexType: 'BASIC_CJQY_QUANTITY_STANDARD',
  },
  DeviceQuality: {
    name: '设备质量',
    componentId: 'DeviceQuality',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/shebeizhiliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/shebeizhiliang.png'),
    styleIndex: '1',
    isShowTip: true,
    indexId: '1001',
    indexName: '填报准确率',
    indexModuleName: '视图基础数据',
    indexType: 'BASIC_ACCURACY',
  },
  VideoQuality: {
    name: '全量视频质量',
    componentId: 'VideoQuality',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/shipinliuzhiliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/shipinliuzhiliang.png'),
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '2',
  },
  VideoQualityImportant: {
    name: '重点视频质量',
    componentId: 'VideoQualityImportant',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/shipinliuzhiliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/shipinliuzhiliang.png'),
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '2',
    indexList: [
      'COMPOSITE_INDEX',
      'COMPOSITE_INDEX',
      'VIDEO_CLOCK_ACCURACY',
      'VIDEO_PLAYING_ACCURACY',
      'VIDEO_HISTORY_ACCURACY',
      'VIDEO_OSD_ACCURACY',
      'VIDEO_OSD_CLOCK_ACCURACY',
    ],
  },
  QualityFace: {
    name: '人脸视图质量',
    componentId: 'QualityFace',
    visiualShow: true,
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '3',
    imgUrl: require('@/assets/img/home/<USER>/renliankakoushituzhiliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/renliankakoushituzhiliang.png'),
  },
  QualityVehicle: {
    name: '车辆视图质量',
    componentId: 'QualityVehicle',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/cheliangkakoushituzhiliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/renliankakoushituzhiliang.png'),
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '3',
  },
  ZDRDataQuality: {
    name: 'ZDR数据质量',
    componentId: 'ZDRDataQuality',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/ZDRshujuzhiliang.png'),
    styleIndex: '4',
  },
  AssessmentRanking: {
    name: '月考核排行',
    componentId: 'AssessmentRanking',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/yuekaohepaihang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/kaohepaihang.png'),
    styleIndex: '7',
  },
  PlatformStability: {
    name: '平台稳定性',
    componentId: 'PlatformStability',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/pingtaiwendingxing.png'),
    imgUrl2: require('@/assets/img/home/<USER>/pingtaiwending.png'),
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '6',
  },
  GovernTendency: {
    name: '治理趋势',
    componentId: 'GovernTendency',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/zhiliqushi.png'),
    imgUrl2: require('@/assets/img/home/<USER>/zhiliqushi.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'year', // 组件 type
    hasIndexChoose: true, // index-choose组件展示
    styleIndex: '5',
  },
  GovernResult: {
    name: '治理成效',
    componentId: 'GovernResult',
    visiualShow: true,
    imgUrl: require('@/assets/img/home/<USER>/zhilichengxiao.png'),
    imgUrl2: require('@/assets/img/home/<USER>/zhilichengxiao.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'year', // 组件 type
    hasIndexModuleChoose: true, // 会有index-module-choose筛选展示
    styleIndex: '5',
  },
  VideoOnlineChanges: {
    name: '视频监控在线变化',
    componentId: 'VideoOnlineChanges',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zaixianbianhua.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
  },
  FaceOnlineChanges: {
    name: '人卡在线变化',
    componentId: 'FaceOnlineChanges',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zaixianbianhua.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
  },
  VehicleOnlineChanges: {
    name: '车卡在线变化',
    componentId: 'VehicleOnlineChanges',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zaixianbianhua.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
  },
  // 注意： 人脸抓拍数量 || 车辆抓拍数量 会根据  日||月趋势 条件的刷选 而 改变 date组件中的type值
  FaceCapturerNumber: {
    name: '人脸抓拍数量',
    componentId: 'FaceCapturerNumber',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zhuapaishuliang.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
    hasFilterSelect: true, // 自定义下拉 筛选条件
  },
  VehicleCapturerNumber: {
    name: '车辆抓拍数量',
    componentId: 'VehicleCapturerNumber',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zhuapaishuliang.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
    hasFilterSelect: true, // 自定义下拉 筛选条件
  },
  MessageNotify: {
    name: '消息通知',
    componentId: 'MessageNotify',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/xiaoxitongzhi.png'),
    styleIndex: '-1',
    hasMoreButton: true, // 会有 【 更多>> 】  显示
  },
  BayonetsOnlineNumber: {
    name: '卡口在线数量',
    componentId: 'BayonetsOnlineNumber',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/kakouzaixianshuliang.png'),
    imgUrl2: require('@/assets/img/home/<USER>/kakouzaixianshuliang.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '-1',
    isLongEchart: true, // 长图表，占据 底部两个dom
  },
  GovernWorkOrder: {
    name: '治理工单',
    componentId: 'GovernWorkOrder',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zhiligongdan.png'),
    imgUrl2: require('@/assets/img/home/<USER>/zhiligongdan.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    hasBackButton: true, // 会有返回按钮展示
    styleIndex: '-1',
  },
  VideoCancelRate: {
    name: '视频撤销', // 视频监控设备撤销率
    componentId: 'VideoCancelRate',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/shipinchexiaolv.png'),
    imgUrl2: require('@/assets/img/home/<USER>/shipinchexiaolv.png'),
    hasDatePicker: true, // 会有选择日期显示
    dateType: 'month', // 组件 type
    styleIndex: '-1',
  },
  ZDRDataGovernance: {
    name: 'ZDR数据治理',
    componentId: 'ZDRDataGovernance',
    visiualShow: false,
    imgUrl: require('@/assets/img/home/<USER>/zdrshujuzhili.png'),
    imgUrl2: require('@/assets/img/home/<USER>/zdrshujuzhili.png'),
    styleIndex: '-1',
  },
  PeopleAndCarPictures: {
    name: '人车抓拍图片质量',
    componentId: 'PeopleAndCarPictures',
    imgUrl: require('@/assets/img/home/<USER>/peopleAndCarPictures.png'),
    visiualShow: true,
    styleIndex: '0', // 可变
    isLongEchart: true, 
  },
  EquipmentActivity: {
    name: '人车设备活跃率',
    componentId: 'EquipmentActivity',
    imgUrl: require('@/assets/img/home/<USER>/peopleAndCarPictures.png'),
    visiualShow: true,
    styleIndex: '0', // 可变
  },
};
export const defaultComponentList = Object.values(defaultConfigEnum);
