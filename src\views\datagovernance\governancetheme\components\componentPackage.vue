<template>
  <div class="component-package auto-fill">
    <div class="component-package-title">
      <i class="icon-font icon-shu<PERSON><PERSON><PERSON><PERSON>an"></i>
      数据治理组件
    </div>
    <Input
      v-model="componentName"
      class="component-package-search"
      suffix="ios-search"
      placeholder="请输入搜索内容"
      style="width: 100%"
    />
    <Menu :theme="theme2" width="100%" class="auto-fill">
      <Submenu v-for="(menu, index) in menuData" :key="index" :name="index">
        <template slot="title"> {{ menu.componentGroupName }} </template>
        <MenuItem
          v-for="(subItem, subkey) in menu.componentVoList"
          :title="subItem.componentName"
          :key="'sub' + subkey"
          :name="index + '-' + subkey"
          ><Icon :type="subItem.icon" />
          <i :class="'icon-font icon-' + subItem.icon"></i>
          {{ subItem.componentName }}
        </MenuItem>
      </Submenu>
    </Menu>
  </div>
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  props: {},
  data() {
    return {
      theme2: 'dark',
      componentName: '',
      menuData: [
        // {
        //   title: "基础组件",
        //   subItems: [
        //     { title: "双组件集合包", icon: "ios-paper" },
        //     { title: "多组件集合包", icon: "ios-paper" },
        //     { title: "组件连接工具", icon: "ios-paper" }
        //   ]
        // },
        //  {
        //   title: "输入输出组件",
        //   subItems: [
        //     { title: "数据输入", icon: "ios-paper" },
        //     { title: "数据输出", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "标准转换组件",
        //   subItems: [
        //     { title: "字段映射", icon: "ios-paper" },
        //     { title: "字典映射", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "数据入库",
        //   subItems: [
        //     { title: "正式入库", icon: "ios-paper" },
        //     { title: "临时入库", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "基础质量检测组件",
        //   subItems: [
        //     { title: "空值检测", icon: "ios-paper" },
        //     { title: "重复检测", icon: "ios-paper" },
        //     { title: "数据类型检测", icon: "ios-paper" },
        //     { title: "数据长度检测", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "图像质量识别治理组件",
        //   subItems: [
        //     { title: "重复图像识别处理", icon: "ios-paper" },
        //     { title: "模糊图像识别处理", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "智能分析组件",
        //   subItems: [
        //     { title: "人脸结构化", icon: "ios-paper" },
        //     { title: "车辆结构化", icon: "ios-paper" },
        //     { title: "人脸聚类", icon: "ios-paper" },
        //     { title: "车辆聚类", icon: "ios-paper" },
        //     { title: "身份关联", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "视图基础数据质量检测组件",
        //   subItems: [
        //     { title: "IP地址格式检测", icon: "ios-paper" },
        //     { title: "MAC地址格式检测", icon: "ios-paper" },
        //     { title: "行政区划格式检测", icon: "ios-paper" },
        //     { title: "设备编码格式检测", icon: "ios-paper" },
        //     { title: "空间信息检测", icon: "ios-paper" },
        //     { title: "时钟信息检测设置", icon: "ios-paper" },
        //     { title: "设备在线检测", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "视图数据质量检测组件",
        //   subItems: [
        //     { title: "大图URL检测", icon: "ios-paper" },
        //     { title: "大小图关联正确检测", icon: "ios-paper" },
        //     { title: "小图唯一人脸检测处理", icon: "ios-paper" },
        //     { title: "人脸结构化属性完整性与准确检性测优化", icon: "ios-paper" },
        //     { title: "车牌识别准确性检测优化", icon: "ios-paper" },
        //     { title: "车辆结构化属性完整性与准确检性测优化", icon: "ios-paper" },
        //     { title: "图像上传及时性检测", icon: "ios-paper" },
        //     { title: "图像抓拍时间准确性检测", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "视频流数据质量检测组件",
        //   subItems: [
        //     { title: "OSD字幕检测与设置", icon: "ios-paper" },
        //     { title: "实时视频流畅通检测", icon: "ios-paper" },
        //     { title: "历史视频流畅通检测", icon: "ios-paper" }
        //   ]
        // },
        // {
        //   title: "人员数据质量检测组件",
        //   subItems: [
        //     { title: "人员轨迹准确性检测优化", icon: "ios-paper" }
        //   ]
        // },
      ],
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.$http.post(api.queryComponentList, { componentName: this.componentName }).then((res) => {
        if (res.data.code == 200) {
          this.menuData = res.data.data.filter((item) => {
            return item.componentVoList.length > 0;
          });
        } else {
          console.log(res.data.msg);
        }
      });
    },
  },
  watch: {
    componentName() {
      this.init();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.component-package {
  height: 100%;
  padding: 16px 11px;
  @{_deep} .ivu-input {
    border-color: var(--color-primary) !important;
  }
  @{_deep} .ivu-menu-dark {
    background: var(--bg-content);
    overflow-y: auto;
  }
  @{_deep} .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened .ivu-menu-submenu-title {
    background: #184f8d;
  }
  @{_deep} .ivu-menu-dark.ivu-menu-vertical .ivu-menu-opened {
    background: #041129;
  }
  @{_deep} .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item:hover,
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:active {
    background: #184f8d !important;
  }
  @{_deep}.ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title:active {
    background: #184f8d !important;
    outline-style: none;
  }
  @{_deep} .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu),
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-item-active:not(.ivu-menu-submenu):hover,
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu),
  .ivu-menu-dark.ivu-menu-vertical .ivu-menu-submenu-title-active:not(.ivu-menu-submenu):hover {
    background: linear-gradient(270deg, rgba(19, 122, 185, 0) 0%, #2b84e2 100%) !important;
  }
  @{_deep} .ivu-menu-submenu-title:hover {
    background: #184f8d !important;
  }
  .ivu-menu-submenu .ivu-menu-item,
  .ivu-menu-vertical .ivu-menu-submenu-title {
    height: 40px !important;
    line-height: 40px !important;
    padding: 0 14px !important;
  }
}

.ivu-menu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
<style lang="less" scoped>
.component-package {
  @{_deep}.ivu-input-prefix i,
  @{_deep}.ivu-input-suffix i {
    color: var(--color-primary) !important;
  }
  @{_deep}.ivu-menu-vertical .ivu-menu-submenu-title-icon {
    right: 0 !important;
  }
  &-title {
    font-size: 16px;
    color: #ffffff;
    i {
      font-size: 13px;
    }
  }
  &-search {
    margin: 12px 0 16px;
  }
}

.icon-font {
  font-size: 8px;
}
</style>
