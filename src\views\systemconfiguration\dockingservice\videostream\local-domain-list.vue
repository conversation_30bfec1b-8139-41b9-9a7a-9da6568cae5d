<template>
  <div class="page-evaluationmanagement height-full">
    <div class="content auto-fill">
      <div class="form">
        <Button class="fr ml-sm" type="primary" @click="config()">
          <i class="icon-font icon-peizhi f-12 mr-sm"></i>
          <span class="inline vt-middle">本域配置</span>
        </Button>
      </div>
      <div class="table-box auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #algorithmVendorType="{ row }">{{ getAlgorithmLabel(row.algorithmVendorType) }}</template>
          <template #online="{ row }">
            <span>{{ row.online | status }}</span>
          </template>
          <template #status="{ row }">{{ statusTypeMap[row.status] }}</template>
          <template #createTime="{ row }">
            <span>{{ row.createTime || '--' | filterDateFun }}</span>
          </template>
          <template #option="{ row }">
            <ui-btn-tip icon="icon-bianji2" class="mr-md" content="编辑" @click.native="edit(row)"></ui-btn-tip>
            <ui-btn-tip icon="icon-shanchu3" content="删除" @click.native="del(row)"></ui-btn-tip>
          </template>
        </ui-table>
      </div>
      <!-- <ui-page
        class="page menu-content-background"
        :page-data="pageData"
        @changePage="changePage"
        @changePageSize="changePageSize"
      ></ui-page> -->
    </div>
    <conFig ref="config" @addSuccess="addSuccess" />
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import algorithm from '@/config/api/algorithm';

export default {
  name: 'algorithm',
  data() {
    return {
      loading: false,
      algorithmVendorType: '',
      addorEditModalShow: false,
      evaluationResultShow: false,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: 'SIP国际服务编码', key: 'domainId' },
        { title: 'SIP服务国际域', key: 'domain' },
        { title: 'SIP服务IP', key: 'ip' },
        { title: 'SIP服务端口', key: 'port' },
        { title: 'SIP认证密码', key: 'pwd' },
        {
          title: '操作',
          slot: 'option',
          fixed: 'right',
          align: 'center',
          width: 90,
        },
      ],
      tableData: [],
      addorEditModalAction: {
        title: '新增',
        action: 'add',
        visible: false,
      },
      addorEditModalData: {},
      evaluationlData: {},
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      },
      searchData: {
        searchValue: '',
        params: {
          pageNumber: 1,
          pageSize: 10,
        },
      },
      defaultProps: {
        label: 'name',
        children: 'children',
      },
      selectOrgTree: {
        orgCode: '',
      },
      resultTypeMap: {},
      statusTypeMap: {},
    };
  },
  components: {
    conFig: require('./config').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      algorithmTypeList: 'algorithm/algorithmType',
    }),
  },
  async created() {
    if (this.algorithmList.length == 0) await this.getAlldicData();
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async init() {
      try {
        this.loading = true;

        this.$http
          .get(algorithm.gbConfig, {})
          .then((res) => {
            if (res.data.code == 200) {
              this.tableData = res.data.data;
              this.loading = false;
            }
          })
          .finally(() => {});
      } catch (error) {
        console.log(error);
      }
    },

    resetSearch() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 10 };
      this.searchData = {
        searchValue: '',
        params: { pageNumber: 1, pageSize: 10 },
        totalCount: 0,
        pageNum: 1,
        pageSize: 10,
      };
      this.algorithmVendorType = '';
      this.init();
    },

    addPage() {
      this.$refs.addPage.showModal(1);
      this.$refs.addPage.form = {};
    },

    addSuccess() {
      this.init();
    },

    config() {
      this.$refs.config.showModal(1);
    },

    edit(row) {
      this.$refs.config.showModal(2, row);
    },
    // 删除弹窗内容
    del(row) {
      this.$UiConfirm({
        content: '您要删除，是否确认?',
        title: '警告',
      })
        .then(() => {
          this.$http.delete(algorithm.gbConfig + `?id= ${row.id}`).then((res) => {
            if (res.status === 200) {
              this.$Message.success(res.data.msg);
              this.init();
            }
          });
        })
        .catch((res) => {
          console.log(res);
        });
    },

    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      // this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      // this.searchData.pageSize = val;
      this.init();
    },
    // 重置
    resetSearchData() {
      // this.copySearchDataMx(this.searchData);
      this.init();
    },
    // 检索
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
    // 选择管辖单位名称
    selectTree({ orgCode }) {
      this.searchData.orgCode = orgCode;
      this.init();
    },
    update() {
      this.init();
    },

    getAlgorithmLabel(val) {
      let a;
      this.$refs.addPage.csList.forEach((item) => {
        if (val == item.dataKey) {
          a = item.dataValue;
        }
      });
      return a;
    },
  },
};
</script>

<style lang="less" scoped>
.page-evaluationmanagement {
  .left-content {
    float: left;
    width: 260px;
    padding: 10px;
    background: var(--bg-content);
    height: 100%;
    .record-title {
      padding: 10px 0;
      .add_case {
        .name {
          margin-left: 10px;
          position: relative;
          top: 2px;
        }
      }
    }
    .collapse-content-p {
      border: 1px solid transparent;
      border-radius: 4px;
      padding: 10px;
      color: var(--color-label);
      background: @bg-table-block;
      margin-bottom: 10px;
      &.active {
        border-color: @color-other;
      }
    }
    .assessment-list {
      position: relative;
    }
  }
  .content {
    // float: right;
    // width: calc(~"100% - 270px");
    width: 100%;
    height: 100%;
    // padding-top: 20px;
    background: var(--bg-content);
    .search-wrapper {
      overflow: hidden;
      padding: 0 12px 0 20px;
      .input-width {
        width: 363px;
      }
    }
    .table-box {
      padding: 20px;
      position: relative;
      margin-top: -20px;
      .sucess {
        color: @color-success;
      }
      .error {
        color: @color-failed;
      }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
.left-item {
  @{_deep} .ivu-form-item-label {
    width: 200px;
  }
  @{_deep} .ivu-form-item-error-tip {
    line-height: 1;
  }
}
</style>
<style lang="less" scoped>
.page-indexmanagement {
  .form-content {
    padding: 0 50px;
    .ivu-form-item {
      margin-bottom: 20px;
      .time {
        width: 22%;
      }
      .lag {
        width: 13.5%;
        margin: 0 10px;
      }
      .canshu {
        width: 15%;
      }
    }
  }
}

.form {
  padding: 20px;
  .based-field-label {
    color: #fff;
    font-size: 14px;
    padding-right: 12px;
  }
  button {
    margin-left: 12px;
  }
  .icon-font {
    color: #ffffff;
  }
}
</style>
