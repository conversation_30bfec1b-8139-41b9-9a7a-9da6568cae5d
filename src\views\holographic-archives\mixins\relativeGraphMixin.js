import { getRelationMap } from '@/api/number-cube'
import { mapState, mapGetters } from 'vuex'
/**
 * 获取关系图谱数据
 * */
export default {
  props: {
    baseInfo: {
      type: Object | String,
      default: {}
    }
  },
  data() {
    return {
      MixinLoading: false,
      MixinRoot: {},
      // 只有组的信息
      MixinReativeStat: [
        {
          children: []
        }
      ],
      MixinEntityGroups: []
    }
  },
  created() {
    this.getRelationMap()
  },
  methods: {
    // 关系统计 - 关系图谱跳转
    MixinToRelationGraph(){
      let Item = this.filterSider.find(item => {
        return item.name.includes('map')
      })
      let { archiveNo, source, idcardNo } = this.$route.query
      this.$router.push({
        name: Item.name,
        query: {
          archiveNo: archiveNo,
          idcardNo: idcardNo,
          source: source,
        }
      })
    },
    MixinToNumCube() {
      let query = {
        ids: JSON.stringify(this.MixinRoot.id),
        maxDepth: 1,
        type: 'add',
        isRelation: true
      }
      this.$router.push({ name: 'number-cube-info', query: query })
    },
    // 后端自己拼接了些东西[前端手动过滤掉]
    // handleString(string, symbol = ':') {
    //   let symbolArray = string.split(symbol)
    //   let newString = symbolArray.length > 1 ? symbolArray[1] : symbolArray[0]
    //   return newString
    // },
    // 处理一些兼容情况
    handleParams(data){
      // 后端返回null的情况
      !data.entitys ? (data.entitys = []) : null
      !data.entityGroups ? (data.entityGroups = []) : null
      !data.relations ? (data.relations = []) : null
      // 找到root节点 - 设置
      let root = data.entitys.find(item => {
        return !!item.isCenter
      })
      !root ? (root = {id: this.baseInfo.archiveNo,
        displayField: this.baseInfo.archiveNo,
        ...this.baseInfo}) : null
      // 图片字段名字都不一样，哪个有取哪个
      const imageStragety = {
        photo: () => {
          root.propertyIcon = this.baseInfo.photo
        },
        photos: () => {
          if (Array.isArray(this.baseInfo.photos)) {
            root.propertyIcon = this.baseInfo.photos[0].photoUrl
          }
        },
        photoUrl: () => {
          root.propertyIcon = this.baseInfo.photoUrl
        }
      }
      if (!('propertyIcon' in root) || !root.propertyIcon) {
        for (let key in imageStragety) {
          if (key in this.baseInfo && !!this.baseInfo[key]) {
            imageStragety[key]()
            break
          }
        }
      }
      if(!data.entitys.length) data.entitys.push(root)
      return {
        data,
        root
      }
    },
    async getRelationMap() {
      try {
        this.MixinLoading = true
        const stragety = {
          car: 'vehicle_archive',
          video: 'vid_archive',
          people: 'real_name_archive'
        }
        let source = this.$route.query.source
        var params = {
          name: stragety[source] || 'real_name_archive',
          // 后端不统一，一会需要转一会不需要
          searchKey: source === 'car' ? JSON.parse(this.$route.query.archiveNo) : this.$route.query.archiveNo
        }
        let res = await getRelationMap(params)
        let { data, root } = this.handleParams(res.data)
        let atlasList = {}
        atlasList.rootId = root.id
        // 节点
        atlasList.nodes = [...data.entityGroups, ...data.entitys]
        // 关系
        atlasList.links = data.relations.map(item => {
          let times = ''
          if('properties' in item && !!item.properties){
            if('times' in item.properties){
              times = `${item.properties.times}次`
            }
          }
          return {
            from: item.sourceId,
            to: item.targetId,
            text: times,
            fontColor: '#2C86F8'
          }
        })
        this.atlasList = atlasList
        this.MixinRoot = root
        this.MixinReativeStat[0].id = root.id
        this.MixinReativeStat[0].img = root.propertyIcon || root.icon
        this.MixinEntityGroups = data.entityGroups
        this.MixinReativeStat[0].children = data.entityGroups.map(item => {
          item.text = item.labelCn
          return {
            id: item.id,
            num: item.count,
            text: item.labelCn
          }
        })
      } catch (error) {
        console.log(error)
      } finally {
        this.MixinLoading = false
      }
    },
  },
  computed: {
    ...mapGetters('admin/menu', ['filterSider'])
  }
}
