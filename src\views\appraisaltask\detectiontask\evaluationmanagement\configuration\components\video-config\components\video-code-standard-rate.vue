<template>
  <div class="video-code-standard-box" v-ui-loading="{ loading: formLoading }">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="190">
      <FormItem label="选择关联的检测任务" prop="taskSchemeId">
        <Select
          class="input-width-number"
          v-model="formData.taskSchemeId"
          clearable
          filterable
          placeholder="请选择关联的检测任务"
          @on-change="handleChangeTask"
        >
          <Option v-for="schemeItem in taskSchemeList" :key="schemeItem.taskSchemeId" :value="schemeItem.taskSchemeId">
            {{ schemeItem.schemeName }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="统计模式" prop="statisticalModel" class="statistical-modal-box">
        <CheckboxGroup :value="formData.statisticalModel" @on-change="changeStatisticalModalGroup">
          <Checkbox :label="SMODE_REGION" class="mr-md">行政区划</Checkbox>
          <Checkbox :label="SMODE_ORG" class="mr-md">组织机构</Checkbox>
          <Checkbox :label="SMODE_DEVICETAG" class="flex-aic">
            <span class="mr-sm">设备标签</span>
            <span class="font-gray" @click.prevent="addStatisticTag" v-if="formData.tagIds?.length"
              >（已选择{{ formData.tagIds.length }}标签）</span
            >
            <i class="icon-font icon-peizhibiduiziduan f-14 font-gray" @click.prevent="addStatisticTag"></i>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="选择指标" prop="indexId">
        <Select
          class="input-width-number"
          v-model="formData.indexId"
          clearable
          filterable
          :disabled="!formData.taskSchemeId"
          placeholder="请选择指标"
          :loading="selectloading"
        >
          <Option v-for="indexItem in indexList" :key="indexItem.indexType" :value="indexItem.indexId">
            {{ indexItem.indexName }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="检测计划" prop="cronData">
        <TestPlan
          class="mb-minus-lg"
          :form-data="formData"
          :form-model="formModel"
          :index-type="moduleAction.indexType"
          @checkTime="checkTime"
        ></TestPlan>
      </FormItem>
      <FormItem label="允许的编码格式" prop="videoCodeType">
        <CheckboxGroup v-model="formData.videoCodeType">
          <Checkbox v-for="item in this.codeTypeOptions" :key="item.id" :label="item.dataKey">{{
            item.dataValue
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
    </Form>
    <!--    关联标签弹窗-->
    <customize-filter
      v-model="addTagVisible"
      :customize-action="tagFilterAttrs.customizeAction"
      :content-style="tagFilterAttrs.contentStyle"
      :field-name="tagFilterAttrs.fieldTagName"
      :checkbox-list="tagFilterAttrs.allTagList"
      :default-checked-list="formData.tagIds"
      :show-clear-all="true"
      @confirmFilter="confirmTagFilter"
    >
    </customize-filter>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import dictData from '@/config/api/user.js';
import taganalysis from '@/config/api/taganalysis';
export default {
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      SMODE_DEVICETAG: this.global.STATISTICAL_MODAL.SMODE_DEVICETAG, //统计模式：设备标签
      SMODE_REGION: this.global.STATISTICAL_MODAL.SMODE_REGION, //统计模式：行政区划
      SMODE_ORG: this.global.STATISTICAL_MODAL.SMODE_ORG, //统计模式：组织机构
      formData: {
        cronData: [],
        taskSchemeId: '',
        indexId: '',
        videoCodeType: [],
      },
      ruleCustom: {
        cronData: [
          {
            required: true,
            message: '请选择检测计划',
            trigger: 'change',
            type: 'array',
          },
        ],
        taskSchemeId: [
          {
            required: true,
            message: '请选择关联的检测任务',
            trigger: 'change',
          },
        ],
        indexId: [
          {
            required: true,
            message: '请选择指标',
            trigger: 'change',
            type: 'number',
          },
        ],
        videoCodeType: [
          {
            required: true,
            message: '请至少选择一个编码格式',
            trigger: 'change',
            type: 'array',
          },
        ],
        statisticalModel: [
          {
            required: true,
            type: 'array',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择至少一个统计模式'));
              } else if (value.includes(3) && this.formData.tagIds?.length === 0) {
                callback(new Error('请选择待统计的设备标签'));
              } else {
                callback();
              }
            },
          },
        ],
      },
      taskSchemeList: [],
      indexList: [],
      selectloading: false,
      codeTypeOptions: [], //编码类型
      formLoading: false, //表单loading
      //选择标签
      addTagVisible: false,
      tagFilterAttrs: {
        allTagList: [],
        customizeAction: {
          title: '添加设备标签',
          leftContent: '选择设备标签及排序',
          rightContent: '设备标签显示',
          moduleStyle: {
            width: '70%',
          },
        },
        contentStyle: {
          height: `${500 / 192}rem`,
        },
        fieldTagName: {
          id: 'tagId',
          value: 'tagName',
        },
      },
    };
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
            videoCodeType: this.configInfo?.videoCodeType.split(',') || [],
          };
          this.getIndexList();
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.setDefaultStatisticalModel();
      },
      immediate: true,
    },
  },
  created() {
    this.getTaskList();
    this.getDictData();
    this.getTagList();
  },
  methods: {
    // 获取字典表接口
    async getDictData() {
      try {
        this.formLoading = true;
        const { data } = await this.$http.get(dictData.queryByTypeKey, { params: { typekey: 'video_code_type' } });
        this.codeTypeOptions = data.data;
      } catch (err) {
        console.log(err);
      } finally {
        this.formLoading = false;
      }
    },
    // 表单提交校验
    async handleSubmit() {
      return await this.$refs['modalData'].validate();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
    //获取任务列表
    async getTaskList() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, params);
        this.taskSchemeList = data.entities || [];
      } catch (e) {
        console.log(e);
      }
    },
    //获取指标列表
    async getIndexList() {
      try {
        this.selectloading = true;
        const params = {
          taskSchemeId: this.formData.taskSchemeId,
          pageNumber: 1,
          pageSize: 9999,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskIndexPageList, params);
        let filterIndex = [];
        if (this.moduleAction.indexType === 'VIDEO_CODE_STANDARD_RATE') {
          // 选择指标只能选择【实时视频流可调阅率】和【重点实时视频流可调阅率】
          filterIndex = ['VIDEO_PLAYING_ACCURACY', 'VIDEO_GENERAL_PLAYING_ACCURACY'];
        }
        this.indexList = data.entities.filter((item) => filterIndex.includes(item.indexType));
      } catch (e) {
        console.log(e);
      } finally {
        this.selectloading = false;
      }
    },
    //切换任务时指标自动切换
    async handleChangeTask() {
      this.indexList = [];
      this.formData.indexId = '';
      await this.getIndexList();
      if (this.indexList.length > 0) {
        this.formData.indexId = this.indexList[0]['indexId'];
      }
    },
    //选择时间
    checkTime(val) {
      Object.assign(this.formData, val);
    },
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagFilterAttrs.allTagList = res.data.data;
      } catch (err) {
        console.log(err);
        this.tagFilterAttrs.allTagList = [];
      }
    },
    // 添加统计标签
    async addStatisticTag() {
      this.addTagVisible = true;
    },
    async confirmTagFilter(val) {
      try {
        const tagIdList = val.map((item) => item.tagId);
        this.formData.tagIds = [...tagIdList];
        this.addTagVisible = false;
      } catch (err) {
        console.log(err);
      }
    },
    //设置默认的统计模式
    setDefaultStatisticalModel() {
      //指标未配置，继承任务的统计模式
      if (!this.formData.statisticalModel) {
        if (this.moduleAction.statisticalModelBo) {
          let models = this.moduleAction.statisticalModelBo.statisticalModel;
          this.formData.statisticalModel = this.$util.common.deepCopy(models);
        } else {
          this.formData.statisticalModel = [this.SMODE_ORG, this.SMODE_REGION];
        }
      }
      if (!this.formData.tagIds) {
        if (this.moduleAction.statisticalModelBo) {
          let tags = this.moduleAction.statisticalModelBo.tagIds;
          this.formData.tagIds = this.$util.common.deepCopy(tags);
        } else {
          this.formData.tagIds = [];
        }
      }
    },
    //勾选设备标签且未选择标签时，默认打开选择标签弹窗
    changeStatisticalModalGroup(group) {
      const oldStatisticalModel = [...this.formData.statisticalModel];
      this.formData.statisticalModel = group;
      let isChangeDeviceTag =
        group.includes(this.SMODE_DEVICETAG) && !oldStatisticalModel.includes(this.SMODE_DEVICETAG);
      if (isChangeDeviceTag && !this.formData.tagIds?.length) {
        this.addStatisticTag();
      }
    },
  },
  components: {
    TestPlan: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/test-plan.vue')
      .default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
};
</script>
<style lang="less" scoped>
.video-code-standard-box {
  .statistical-modal-box {
    @{_deep} .ivu-checkbox-group {
      display: flex;
      align-items: center;
      flex-direction: row !important;
    }
  }
  .input-width-number {
    width: 380px;
  }
  .mb-minus-lg {
    margin-bottom: -20px;
  }
}
</style>
