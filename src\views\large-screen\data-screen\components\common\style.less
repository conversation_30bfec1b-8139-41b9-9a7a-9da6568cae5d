.table{

    .table-header{
        display: flex;
        font-weight: 400;
        color: #ffffff;
        font-size: 14px;
        justify-content: space-between;
        padding: 0 15px;
        background: linear-gradient(180deg, rgba(7, 32, 70, 0) 0%, #064096 100%); 
        .table-column-pm{
            width: 50px;
        }
        .table-column-fxj{
            width: 40%;
        }
        .table-column-xysc{
            width: 40%;
        }
    }
    .table-content{
        
        .table-row{
            display: flex;
            justify-content: space-between;
            align-items: center;
            // border-bottom: ;
            &:nth-child(odd){
                padding: 3px 0;
                background: linear-gradient(90deg, rgba(6,55,131, 0) 0%, rgba(6,55,131,0.5) 51%, rgba(6,55,131,0) 100%);
            }
            .table-column-pm{
                width: 50px;
                text-align: center;
                font-size: 14px;
                font-weight: 700;
                color: #03ECF6;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .table-column-fxj{
                width: 40%;
                font-size: 14px;
                font-weight: 400;
                color: #D0DDE7;
                
            }
            .table-column-xysc{
                width: 40%;
                display: flex;
                align-items: center;
                justify-content: center;
                .time{
                    width: 40px;
                    margin-right: 10px;
                    font-size: 16px;
                    font-weight: 700;
                }
                .time-red{
                    color: #F76E38;
                }
                .time-greed{
                    color: #2DDF6C;
                }
                img{
                    width: 12px;
                    height: 14px;
                }
            }
        }
        
    }
}
.ranking{
    width: 100%;
    height: 91px;
    display: flex;
    align-items: flex-end;
    .ranking-no2{
        width: 135px;
        height: 81px;
        background: url('~@/assets/img/screen/no2.png') no-repeat;
        text-align: center;
    }
    .ranking-no1{
        width: 135px;
        height: 91px;
        background: url('~@/assets/img/screen/no1.png') no-repeat;
        text-align: center;
    }
    .ranking-no3{
        width: 135px;
        height: 78px;
        background: url('~@/assets/img/screen/no3.png') no-repeat;
        text-align: center;
    }
    .ranking-name{
        font-weight: 400;
        font-size: 14px;
        color: #D0DDE7;
    }
    .ranking-num{
        font-size: 18px;
        font-weight: 700;
        color: #F1FCFF;
    }
}
.table-list{
    height: calc(~'100% - 91px');
    background: linear-gradient(180deg, rgba(18, 36, 108, 0.9) 0%, rgba(2,14,66,0) 100%);
    border-radius: 4px 4px 4px 4px;
    padding: 10px;
    .td-li{
        height: 20%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &:nth-child(odd){
            background: rgba(0, 95, 238, 0.2);
        }
        &:nth-child(even){

        }
        .td-index{
            color: #03ECF6;
            font-size: 14px;
            margin-left: 15px;
        }
        .td-name{
            width: 35%;
            color: #D0DDE7;
            font-weight: 400;
        }
        .td-num{
            width: 24%;
            color: #ffffff;
            font-size: 18px;
            font-weight: 700;
        }

    }
}
// 四角
.angle{
    div{
        width: 10px;
        height: 10px;
        background: url('~@/assets/img/screen/angle.png') no-repeat; 
        &:nth-child(1){
            position: absolute;
            top: 0;
            left: 0;
            background-position: 0 0;
        }
        &:nth-child(2){
            position: absolute;
            top: 0;
            right: 0;
            background-position: -11px 0px;
        }
        &:nth-child(3){
            position: absolute;
            bottom: 0;
            left: 0;
            background-position: 0 -11px;
        }
        &:nth-child(4){
            position: absolute;
            bottom: 0;
            right: 0;
            background-position: -11px -11px;
        }
    }
}
// 竖虚线
.box-line{
    width: 0;
    height: 90px;
    border: 1px dashed #88CDFF;
    opacity: 0.5;
}
// 字体
.bigtitle{
    font-family: 'PangMenZhengDao';
    text-transform: none;
    letter-spacing: 1px;
    color: #ffffff;
    font-style: normal;
    font-weight: 400;
    text-shadow: 0px 1px 2px #004ED6;
    background: linear-gradient(90deg, #ffffff 0%, #ffffff 100%);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
}
.dinpro{
    font-family: 'DINPro';
    font-weight: 700;
    text-shadow: 0px 0px 10px #0988FF;
    font-style: normal;
    text-transform: none;
}
.dinpromini{
    font-family: 'DINPro';
    font-weight: 700;
    font-style: normal;
    text-transform: none;
}
// 地图弹框
.custom-tooltip-box{
    width: 265px;
    height: 318px;
    background: url('~@/assets/img/screen/maphoverbox.png') no-repeat !important; 
    background-size: cover;
    display: none;
    .custom-tooltip-style{
        padding: 0 20px;
    }
    .tooltip-title{
        margin-top: 10px;
        span{
            font-family: 'PangMenZhengDao';
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            font-style: normal;
            text-transform: none;
        }
        .tooltip-dot{
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: #F1FCFF;
            box-shadow: 0px 0px 5px 0px #0988FF;
            margin-right: 10px;
            display: inline-block;
        }
    }
    .tooltip-withe-line{
        width: 210px;
        height: 2px;
        background: linear-gradient(90deg, rgba(168,208,255,0) 1%, #FFFFFF 49%, rgba(168,208,255,0) 100%);
        margin: 10px 0;
    }
    .tooltip-content{
        
    }
    .tooltip-content-title{
        font-weight: 700;
        font-size: 14px;
        color: #AACDFA;
    }
    .tooltip-warpper{
        margin: 7px 0;
        display: flex;
        align-items: center;
        .tooltip-warpper-name{
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
        }
        .tooltip-warpper-content{
            font-family: 'DINPro';
            font-weight: 700;
            font-size: 20px;
            color: #098EFF;
            font-style: normal;
            text-transform: none;
        }
        .tooltip-warpper-huoyue{
            color: #2DDF6C;
        }
        .tooltip-warpper-contents{
            font-family: 'DINPro';
            font-weight: 700;
            font-size: 20px;
            color: #FFC963;
            font-style: normal;
            text-transform: none;
        }
        .tooltip-warpper-userName{
            font-weight: 400;
            font-size: 14px;
            color: #D0DDE7;
            font-style: normal;
            text-transform: none;
            margin-left: 5px;
        }
        .tooltip-warpper-no1{
            width: 22px;
            height: 24px;
            background: url('~@/assets/img/screen/ranking-0.png') no-repeat;
        }
        .tooltip-warpper-no2{
            width: 22px;
            height: 24px;
            background: url('~@/assets/img/screen/ranking-1.png') no-repeat;
        }
        .tooltip-warpper-no3{
            width: 22px;
            height: 24px;
            background: url('~@/assets/img/screen/ranking-2.png') no-repeat;
        }
    }
    .tooltip-line{
        width: 200px;
        height: 0px;
        border: 1px dashed #88CDFF;
        opacity: 0.5;
        margin: 20px 0 10px;
    }
}