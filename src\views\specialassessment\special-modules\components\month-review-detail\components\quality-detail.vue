<template>
  <div class="review-details auto-fill">
    <div class="review-details-header">
      <span class="review-details-title">
        <span class="review-details-rect"></span>
        <span class="ml-sm">{{ queryParams.title }}评测-{{ detailData.name }}{{ month }}月检测日明细</span>
      </span>
      <span class="goback-btn" @click="goBack">
        <i class="icon-font icon-fanhui f-14 mr-xs"></i>
        <span>返回</span>
      </span>
    </div>
    <div class="btn-bar mt-md mb-md">
      <slot name="export"></slot>
    </div>
    <div class="review-details-table auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @onSortChange="onSortChange"
      >
        <template #detection="{ row, column }">
          <span class="pointer underline-text" @click="viewDayDetail(row, column, '')">
            {{ row[column.key] }}
          </span>
        </template>
        <template #qualified="{ row, column }">
          <span class="pointer underline-text" @click="viewDayDetail(row, column, '1')">
            {{ row[column.key] }}
          </span>
        </template>
        <template #unqualified="{ row, column }">
          <span class="pointer underline-text unqualified-color" @click="viewDayDetail(row, column, '2')">
            {{ row[column.key] }}
          </span>
        </template>
        <template #rate="{ row, column }">
          <span
            v-if="row[column.key] || row[column.key] === 0"
            :class="getQual(row, column) ? '' : 'unqualified-color'"
          >
            {{ row[column.key] || 0 }}%</span
          >
        </template>
      </ui-table>
    </div>
    <day-review-details
      v-model="dayReviewDetailVisible"
      v-bind="$attrs"
      :data-dimension-enum="detailData.dataDimensionEnum"
      :index-data="indexData"
      :row-data="rowData"
    ></day-review-details>
  </div>
</template>
<script>
import { tableColumns } from '../utils/tableColumns';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'quality-detail',
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      exportLoading: false,
      queryParams: {},
      dayReviewDetailVisible: false,
      dataDimensionEnum: 'DEVICE_ALL',
      rowData: {},
      month: '',
      loading: false,
      tableColumns: [],
      tableData: [],
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
      sortType: '',
      currentSortField: '',
    };
  },
  mounted() {
    const examineTime = new Date(this.queryParams.examineTime);
    this.month = examineTime.getMonth() + 1;
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        let { examineTime, dateType, indexType, statisticsCode } = this.$route.query;
        let { dataDimensionEnum, code } = this.detailData;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType,
          dataDimensionEnum: dataDimensionEnum,
          nodeEnum: indexType,
          paramForm: {
            // indexIds: this.detailData.indexIds,
            orgRegionCode: statisticsCode === '3' ? null : code,
            tagIds: statisticsCode === '3' && code !== '-1' ? [Number(code)] : null,
          },
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (this.sortData.sortField) {
          params.paramForm.indexId = this.sortData.indexId;
          params.paramForm.sortField = this.sortData.sortField;
          params.paramForm.sort = this.sortData.sort;
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getSpecialDetailByMonth, params);
        if (!data || !data.entities) return false;
        this.handleTable(data.entities);
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    getQual(row, column) {
      const keyArr = column.key.split('-');
      const indexId = keyArr[0];
      return row[`${indexId}-qual`] === '1';
    },
    onSortChange({ key, order }) {
      this.sortType = order;
      this.currentSortField = key;
      if (order === 'normal') {
        this.sortData = {};
      } else {
        const keyArr = key.split('-');
        this.sortData = {
          indexId: keyArr[0],
          sortField: keyArr[1],
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
      this.$emit('sortChange', this.sortData);
    },
    goBack() {
      this.$emit('changeComponentName', ['StatisticsList']);
    },
    viewDayDetail(row, column, qualVal) {
      this.dayReviewDetailVisible = true;
      const keyArr = column.key.split('-');
      const indexId = keyArr[0];
      const detail = row.detail.find((item) => item.indexId == indexId);
      this.rowData = {
        qualVal,
        ...row,
        ...detail,
      };
    },
    handleTable(data) {
      try {
        let indexIds = [];
        this.tableData = data.map((item) => {
          let obj = { ...item };
          item.detail.forEach((detailItem) => {
            if (!indexIds.includes(detailItem.indexId)) {
              indexIds.push(detailItem.indexId);
            }
            Object.keys(detailItem).forEach((keyItem) => {
              if (keyItem === 'qual') {
                obj[keyItem] = detailItem[keyItem];
              }
              obj[`${detailItem.indexId}-${keyItem}`] = detailItem[keyItem];
            });
          });
          return obj || {};
        });
        this.tableColumns = this.tableColumns.map((colItem) => {
          if (!colItem.colkey && !colItem.indexIds) {
            return colItem;
          }
          indexIds.forEach((id) => {
            if (!!colItem.indexIds && colItem.indexIds.includes(id)) {
              colItem.children.forEach((childItem) => {
                childItem.key = id + '-' + childItem.keyName;
                // 表格刷新，表格列排序配置回显
                if (!!this.sortType && childItem.key === this.currentSortField) {
                  childItem.sortType = this.sortType;
                } else {
                  childItem.sortType = 'normal';
                }
              });
            }
          });
          return colItem;
        });
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {
    detailData: {
      handler() {
        this.queryParams = this.$route.query;
        this.tableColumns = tableColumns[this.queryParams.indexType];
        this.getTableList();
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DayReviewDetails: require('@/views/specialassessment/special-modules/components/day-review-details/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.review-details {
  &-header {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    background: var(--bg-navigation);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-rect {
    content: '';
    display: inline-block;
    width: 5px;
    height: 20px;
    background: var(--bg-title-rect);
  }
  &-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: var(--color-content);
  }
  &-table {
    padding: 0 20px;
  }
  .goback-btn {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    color: var(--color-btn-default);
    cursor: pointer;
  }
  .btn-bar {
    width: 100%;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
  }
  .underline-text {
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
  .icon-daochu {
    color: var(--color-btn-default);
  }
  .review-details-table {
    @{_deep} .ivu-table {
      &-header thead tr th {
        border: 1px solid var(--border-table);
      }
      .ivu-table-tbody td {
        border: 1px solid var(--border-table);
      }

      .ivu-table-tbody tr:first-child td {
        border-top: none;
      }
      &-overflowY {
        overflow-y: auto;
      }
    }
  }
}
</style>
