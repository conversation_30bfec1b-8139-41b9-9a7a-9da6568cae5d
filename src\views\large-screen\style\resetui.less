.screen{
    height:calc( ~'100% - 110px');
    display: flex;
    flex: 1;
    padding: 0 40px 20px;
    .screen-left, 
    .screen-right{
        width: 420px;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .screen-box{
            height: calc( ~'33% - 5px');
            position: relative;
            .form-box{
                font-size: 14px;
                display: flex;
                align-items: center;
                justify-content: end;
                position: absolute;
                top: -25px;
                right: 0;
                .warpper-title{
                    color: #27B5FF;
                }
                .warpper-content{
                    width: 95px;
                    margin-left: 10px;
                }
            }
            .tab{
                display: flex;
                position: absolute;
                top: 16px;
                right: 6px;
                .tab-btn{
                    padding: 2px 4px;
                    background: rgba(0, 150, 255, .1);
                    color: #7195D0;
                    font-size: 12px;
                    font-weight: 400;
                    margin-left: 5px;
                    cursor: pointer;
                }
                .tab-btn-active{
                    color: #ffffff;
                    background: linear-gradient(180deg, rgba(8,224,255,0) 0%, #08E0FF 100%);
                }
            }
            .data-box-ul{
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                overflow-y: auto;
            }
            .data-box{
                background: rgba(24, 98, 187, 0.1);
                width: 195px;
                height: calc( ~'50% - 5px');
                padding: 10px 10px 5px 10px;
                margin-bottom: 5px;
                position: relative;
                .data-box-top{
                    display: flex;
                    justify-content: space-between;
                    .box-top-left{
                        .data-box-name{
                            font-size: 14px;
                            font-weight: 400;
                            color: #ffffff;
                        }
                        .data-box-num{
                            font-family: 'DINPro';
                            font-size: 20px;
                            color: #03A9FF;
                        }
                    }
                    .icon-type{
                        width: 30px;
                        height: 30px;
                    }
                }
                .data-box-add{
                    font-size: 12px;
                    font-weight: 400;
                    color: #ffffff;
                    margin-top: 3%;
                }
                .box-add{
                    display: flex;
                    .box-add-left{
                        width: 100px;
                        font-size: 14px;
                        color: #2DDF5C;
                    }
                    .box-add-right{
                        font-size: 14px;
                        color: #2DDF5C;
                        display: flex;
                        align-items: center;
                        img{
                            width: 12px;
                            height: 14px;
                            margin-left: 10px;
                        }
                    }
                }
            }
            .data-box:last-child{
                margin-bottom: 0;
            }
            .data-box:nth-last-child(2){
                margin-bottom: 0;
            }
            .maintain-box{
                display:flex;
                flex-wrap: wrap;
                justify-content: space-between;
                height: 100%;
                .maintain-box-top{
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: space-evenly;
                }
                .maintain-box-bot{
                    width: 49%;
                    background: rgba(24,98, 187, 0.1);
                    display: flex;
                    justify-content: space-evenly;
                    align-items: center;
                    position: relative;
                }
                .detail-li{
                    text-align: center;
                    .detail-li-num{
                        font-size: 30px;
                        font-weight: 700;
                        color: #F1FCFF;
                    }
                    .detail-li-title{
                        font-size: 14px;
                        font-weight: 400;
                        color: #ffffff;
                    }
                }
            }
            .timeShow-box{
                display: flex;
                padding: 0 60px;
                justify-content: space-between;
                height: 100%;
                align-items: center;
                .box-title{
                    font-size: 14px;
                    font-weight: 400;
                    color: #ffffff;
                }
                .box-content{
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    .box-time{
                        width: 91px;
                        height: 39px;
                        color: #ffffff;
                        border: 2px solid;
                        border-top: none;
                        font-size: 26px;
                        font-weight: 700;
                        font-family: 'DINPro';
                        text-align: center;
                        margin: 15px 0 10px;
                    }
                    .box-left-time{
                        background: linear-gradient(180deg, rgba(20,142,255,0) 0%, rgba(20,142,255, 0.5) 100%);
                        border-image: linear-gradient(180deg, rgba(20, 142, 255, 0), rgba(20,142,255,1)) 2 2;
                    }
                    .box-right-time{
                        background: linear-gradient(180deg, rgba(255,227,46,0) 0%, rgba(225,227,46, 0.5) 100%);
                        border-image: linear-gradient(180deg, rgba(255, 227, 46, 0), rgba(225,227,46, 1)) 2 2;
                    }
                }
            }
            .table{

                .table-header{
                    display: flex;
                    font-weight: 400;
                    color: #ffffff;
                    font-size: 14px;
                    justify-content: space-between;
                    // padding: 0 15px;
                    background: linear-gradient(180deg, rgba(7, 32, 70, 0) 0%, #064096 100%); 
                    .table-column-pm{
                        width: 50px;
                        text-align: center;
                    }
                    .table-column-fxj{
                        width: 40%;
                        text-align: center;
                    }
                    .table-column-xysc{
                        width: 40%;
                    }
                    .table-column-dyl{
                        width: 40%;
                        text-align: center;
                    }
                    .table-column-ghjxzs{
                        width: 25%;
                        text-align: center;
                    }
                    .table-column-yjsl{
                        width: 25%;
                        text-align: center;
                    }
                }
                .table-content{
                    
                    .table-row{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        // border-bottom: ;
                        &:nth-child(odd){
                            padding: 3px 0;
                            background: linear-gradient(90deg, rgba(6,55,131, 0) 0%, rgba(6,55,131,0.5) 51%, rgba(6,55,131,0) 100%);
                        }
                        .table-column-pm{
                            width: 50px;
                            text-align: center;
                            font-size: 14px;
                            font-weight: 700;
                            color: #03ECF6;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .table-column-fxj{
                            width: 40%;
                            font-size: 14px;
                            font-weight: 400;
                            color: #D0DDE7;
                            text-align: center;
                        }
                        .table-column-ghjxzs{
                            width: 25%;
                            color: #03A9FF;
                            font-size: 16px;
                            text-align: center;
                        }
                        .table-column-yjsl{
                            width: 25%;
                            color: #FFC963;
                            font-size: 16px;
                            text-align: center;
                        }
                        .table-column-dyl{
                            width: 40%;
                            font-size: 14px;
                            color: #03A9FF;
                            text-align: center;
                            font-weight: 700;
                        }
                        .table-column-xysc{
                            width: 40%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            .time{
                                width: 40px;
                                margin-right: 10px;
                                font-size: 16px;
                                font-weight: 700;
                            }
                            .time-red{
                                color: #F76E38;
                            }
                            .time-greed{
                                color: #2DDF6C;
                            }
                            img{
                                width: 12px;
                                height: 14px;
                            }
                        }
                    }
                    
                }
            }
        }
    }
    .screen-main{
        flex: 1;
        position: relative;
        padding: 10px 20px 0 20px;
        height: 100%;
        background: url('~@/assets/img/screen/screen-quan.png') no-repeat;
        background-position: center;
        background-size: auto;
        display: flex;
        flex-direction: column;
        .content-top{
            display: flex;
            justify-content: space-around;
            .view-box{
                width: 411px;
                height: 170px;
                background: url('~@/assets/img/screen/midboxbg.png') no-repeat;
                position: relative;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px 17px;
                .title-img{
                    width: 180px;
                    height: 40px;
                    position: absolute;
                    background: url('~@/assets/img/screen/midtitle.png') no-repeat;
                    background-size: 100% 100%;
                    left: 50%;
                    transform: translateX(-50%);
                    top: -20px;
                    line-height: 40px;
                    text-align: center;
                    font-size: 20px;
                    color: #ffffff;
                }
                .view-box-contnet{
                    width: 100%;
                    height: 100%;
                    display: flex;
                    padding: 0 24px;
                    align-items: center;
                    justify-content: space-between;
                    .box-content-left{
                        .content-left-top, .content-left-bottom{
                            display: flex;
                            align-items: center;
                            .box-title{
                                font-weight: 400;
                                font-size: 14px;
                                color: #ffffff;
                                margin-right: 20px;
                            }
                            .bank-li-total{
                                font-size: 28px;
                                font-weight: 700;
                                color: #F1FCFF;
                                text-shadow: 0px 0px 10px #0988FF;
                                font-family: 'DINPro';
                            }
                        }
                    }
                    .box-content-right{
                        width: 110px;
                        height: 110px;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        background: url('~@/assets/img/screen/blue-ball.png') no-repeat;
                        .data-percent{
                            font-weight: 700;
                            font-size: 24px;
                            color: #ffffff;
                            font-family: 'DINPro';
                            font-style: normal;
                            text-transform: none;
                        }
                        p{
                            font-weight: 400;
                            font-size: 14px;
                            color: #ffffff;
                        }
                    }
                }
            }
        }
        .map-chart{
            flex:1;
        }
    }
    .data-box-ul ::-webkit-scrollbar {
        width: 1px;
    }
    ::-webkit-scrollbar{
        width: 4px;
        height: 10px;
        background-color: #02162b;
    }
    ::-webkit-scrollbar-track{
        box-shadow: inset 0 0 6px rgba(0,0,0, 0.3);
        border-radius: 10px;
        background-color: transparent;
    }
    ::-webkit-scrollbar-thumb{
        box-shadow: inset 0 0 6px rgba(0,0,0, 0.1);
        background-color: rgba(25,143,226, 0.58);
        border-radius: 10px;
    }
    ::-webkit-scrollber-corner{
        background-color: rgba(0, 0, 0, 0.4);
    }
    // 竖虚线
    .box-line{
        width: 0;
        height: 90px;
        border: 1px dashed #88CDFF;
        opacity: 0.5;
    }
    // 字体
    .bigtitle{
        font-family: 'PangMenZhengDao';
        text-transform: none;
        letter-spacing: 1px;
        color: #ffffff;
        font-style: normal;
        font-weight: 400;
        text-shadow: 0px 1px 2px #004ED6;
        background: linear-gradient(90deg, #ffffff 0%, #ffffff 100%);
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
    }
    .dinpro{
        font-family: 'DINPro';
        font-weight: 700;
        text-shadow: 0px 0px 10px #0988FF;
        font-style: normal;
        text-transform: none;
    }
    .dinpromini{
        font-family: 'DINPro';
        font-weight: 700;
        font-style: normal;
        text-transform: none;
    }
    /deep/ .ivu-select{
        &:hover{
            color: #567BBB;
        }
        .ivu-select-selection{
            border: 1px solid #098EFF;
            background: rgba(9,142,255, 0.1);
            color: #567BBB;
        }
        .ivu-select-selected-value{
            color: #567BBB;
        }
        .ivu-select-arrow{
            color: #567BBB;
        }
        .ivu-select-dropdown{
            background: rgba(9,142,255, 0.3);
        }
        .ivu-select-item, .ivu-select-placeholder{
            color: #27B5FF;
        }
        .ivu-select-item-selected:hover, 
        .ivu-select-item:hover, 
        .ivu-select-placeholder:hover, 
        .ivu-select-selection:hover{
            color: #fff;
            .ivu-select-placeholder{
                color: #fff;
            }
        }
        .ivu-select-item-selected{
            color: #fff;
        }
    }
}
.ranking{
    width: 100%;
    // height: 91px;
    height: 100%;
    display: flex;
    align-items: flex-end;
    .ranking-no2{
        width: 135px;
        height: 111px;
        background: url('~@/assets/img/screen/no2.png') no-repeat;
        text-align: center;
    }
    .ranking-no1{
        width: 135px;
        height: 121px;
        background: url('~@/assets/img/screen/no1.png') no-repeat;
        text-align: center;
    }
    .ranking-no3{
        width: 135px;
        height: 101px;
        background: url('~@/assets/img/screen/no3.png') no-repeat;
        text-align: center;
    }
    .ranking-name{
        font-weight: 400;
        font-size: 14px;
        color: #D0DDE7;
    }
    .ranking-num{
        font-size: 18px;
        font-weight: 700;
        color: #F1FCFF;
    }
}
// 地图弹框
.custom-tooltip-box{
    width: 265px;
    height: 318px;
    background: url('~@/assets/img/screen/maphoverbox.png') no-repeat !important; 
    background-size: 100% 100%;
    display: none;
    .custom-tooltip-style{
        padding: 0 20px;
    }
    .tooltip-title{
        margin-top: 10px;
        span{
            font-family: 'PangMenZhengDao';
            font-weight: 400;
            font-size: 22px;
            color: #FFFFFF;
            font-style: normal;
            text-transform: none;
        }
        .tooltip-dot{
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: #F1FCFF;
            box-shadow: 0px 0px 5px 0px #0988FF;
            margin-right: 10px;
            display: inline-block;
        }
    }
    .tooltip-withe-line{
        width: 210px;
        height: 2px;
        background: linear-gradient(90deg, rgba(168,208,255,0) 1%, #FFFFFF 49%, rgba(168,208,255,0) 100%);
        margin: 10px 0;
    }
    .tooltip-content{
        
    }
    .tooltip-content-title{
        font-weight: 700;
        font-size: 14px;
        color: #AACDFA;
    }
    .tooltip-warpper{
        margin: 7px 0;
        .tooltip-warpper-name{
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
        }
        .tooltip-warpper-content{
            font-family: 'DINPro';
            font-weight: 700;
            font-size: 20px;
            color: #098EFF;
            font-style: normal;
            text-transform: none;
        }
        .tooltip-warpper-contents{
            font-family: 'DINPro';
            font-weight: 700;
            font-size: 20px;
            color: #FFC963;
            font-style: normal;
            text-transform: none;
        }
    }
    .tooltip-line{
        width: 200px;
        height: 0px;
        border: 1px dashed #88CDFF;
        opacity: 0.5;
        margin: 20px 0 10px;
    }
}
.custom-tooltip-box-mini{
    width: 265px;
    height: 267px;
    background: url('~@/assets/img/screen/maphoverbox-mini.png') no-repeat !important; 
    background-size: 100% 100%;
    display: none;
}
.large-screen-footer{
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 50px;
    left: 50%;
    transform: translateX(-50%);
    .btn{
        width: 115px;
        height: 38px;
        font-size: 18px;
        color: #fff;
        font-weight: 400;
        margin: 0 30px; 
        background: url('~@/assets/img/screen/btnbg_normal.png') no-repeat;
        text-align: center;
        padding-top: 4px;
        cursor: pointer;
        span{
            font-family: 'PangMenZhengDao';
            text-transform: none;
            letter-spacing: 1px;
            text-shadow: 0px 1px 3px rgba(2, 20, 63, 0.1);
            background: linear-gradient(90deg, #ffffff 0%, #ffffff 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
    }
    .btn-active{ 
        background: url('~@/assets/img/screen/btnbg_active.png') no-repeat;
    }
}
// 四角
.angle{
    div{
        width: 10px;
        height: 10px;
        background: url('~@/assets/img/screen/angle.png') no-repeat; 
        &:nth-child(1){
            position: absolute;
            top: 0;
            left: 0;
            background-position: 0 0;
        }
        &:nth-child(2){
            position: absolute;
            top: 0;
            right: 0;
            background-position: -11px 0px;
        }
        &:nth-child(3){
            position: absolute;
            bottom: 0;
            left: 0;
            background-position: 0 -11px;
        }
        &:nth-child(4){
            position: absolute;
            bottom: 0;
            right: 0;
            background-position: -11px -11px;
        }
    }
}