<template>
  <div>
    <ui-modal
      v-model="visible"
      :styles="styles"
      :closable="closable"
      title="修改密码"
      :cancelBtn="!firstTime"
      @onCancel="handleCancel"
      @onOk="save('passwordForm')"
    >
      <Form
        ref="passwordForm"
        :model="passwordForm"
        :label-width="80"
        :rules="passwordFormValidate"
      >
        <FormItem label="旧密码" prop="oldPassword" required>
          <Input
            v-model="passwordForm.oldPassword"
            type="password"
            password
            placeholder="请输入旧密码"
            class="width-sm custom-icon"
          ></Input>
        </FormItem>
        <FormItem label="新密码" prop="newPassword" required>
          <Input
            v-model="passwordForm.newPassword"
            type="password"
            password
            placeholder="请输入新密码"
            class="width-sm custom-icon"
          >
          </Input>
        </FormItem>
        <FormItem label="确认密码" prop="surePassword" required>
          <Input
            v-model="passwordForm.surePassword"
            type="password"
            password
            placeholder="请输入确认密码"
            class="width-sm custom-icon"
          >
          </Input>
        </FormItem>
      </Form>
    </ui-modal>
  </div>
</template>
<script>
import { updatePersonalPsw } from "@/api/user";
import md5 from "md5";
import { mapActions } from "vuex";

export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorOldPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("旧密码为必填项"));
      } else {
        callback();
      }
    };
    const validatorNewPassword = (rule, value, callback) => {
      //   const reg = /^[A-Za-z0-9]{6,16}$/;
      // const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[?=.*\W][^]{8,16}$/;
      const reg = /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)[A-Za-z\d\W]{8,18}$/;
      if (value === "") {
        // Ad12@112
        callback(new Error("新密码为必填项"));
      } else if (!reg.test(value)) {
        callback(
          new Error(
            "密码至少包含大写字母,小写字母,数字,可以包含特殊字符,且不少于8位"
          )
        ); //密码长度要大于6位,由数字和字母组成
      } else if (value === this.passwordForm.oldPassword) {
        callback(new Error("新密码不能与旧密码一致"));
      } else {
        callback();
      }
    };
    const validatorSurePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("重复密码为必填项"));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error("两次输入密码不一致"));
      } else {
        callback();
      }
    };
    return {
      styles: {
        width: "2.6rem",
      },
      visible: false,
      saveModalLoading: false, // 保存密码按钮loading
      passwordForm: {
        oldPassword: "",
        newPassword: "",
        surePassword: "",
      },
      passwordFormValidate: {
        oldPassword: [{ validator: validatorOldPassword, trigger: "blur" }],
        newPassword: [{ validator: validatorNewPassword, trigger: "blur" }],
        surePassword: [{ validator: validatorSurePassword, trigger: "blur" }],
      },
      closable: true,
    };
  },
  computed: {
    firstTime() {
      return this.$route.query.firstTime;
    },
  },
  created() {
    if (this.$route.query.firstTime) {
      this.closable = false;
    }
  },
  watch: {
    value(val) {
      this.visible = val;
    },
    visible(val) {
      this.$emit("input", val);
      this.$refs.passwordForm.resetFields();
    },
  },
  methods: {
    ...mapActions({
      removeAreaList: "common/removeAreaList",
    }),
    async save(name) {
      const validate = await this.$refs[name].validate();
      if (!validate) return;
      const passwordForm = {
        newPsw: md5(this.passwordForm.newPassword),
        oldPsw: md5(this.passwordForm.oldPassword),
        confirmPsw: md5(this.passwordForm.surePassword),
      };
      updatePersonalPsw({ ...passwordForm })
        .then((res) => {
          this.$Message.success(res.msg);
          this.visible = false;
          // // 修改完密码之后重新登陆
          // window.sessionStorage.clear();
          this.removeAreaList();
          // this.$router.push({ name: "login" });
          this.$store.dispatch("handleLogOut").then((res) => {
            this.$router.push("/login");
          });
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.saveModalLoading = false;
        });
    },
    handleCancel() {
      if (this.$route.query.firstTime) {
        return;
      }
      this.visible = false;
    },
    cancel() {
      this.visible = false;
    },
  },
};
</script>
<style lang="less" scoped>
.width-sm {
  width: 100%;
}

/deep/ .ivu-modal-body {
  padding: 50px 50px 26px 50px;
}
/deep/ .ivu-modal-header {
  padding: 0;
}
.custom-icon {
  /deep/.ivu-input-suffix {
    i {
      color: #4597ff;
    }
    .ivu-icon-ios-eye-off-outline:before {
      font-family: iconfont;
      content: "\e8ff";
      font-size: 15px;
    }
    .ivu-icon-ios-eye-outline:before {
      font-family: iconfont;
      content: "\e78f";
      font-size: 15px;
    }
  }
  /deep/.ivu-icon-ios-close-circle:before {
    font-size: 15px;
  }
}
</style>
