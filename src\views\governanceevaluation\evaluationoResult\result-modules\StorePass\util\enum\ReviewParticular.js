import { qualifiedColorConfig } from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
import global from '@/util/global';
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
  // {
  //   type: 'select',
  //   key: 'errorCodes',
  //   label: '不合格原因',
  //   selectMutiple: true,
  //   placeholder: '请选择异常原因',
  //   options: [],
  // },
];
export const tableColumns = [
  {
    type: 'index',
    width: 70,
    title: '序号',
    align: 'center',
  },
  {
    title: `${global.filedEnum.deviceId}`,
    key: 'deviceId',

    minWidth: 200,
    tooltip: true,
  },
  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    minWidth: 200,
    tooltip: true,
  },
  {
    title: '组织机构',
    slot: 'orgName',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '点位类型',
    key: 'sbdwlxText',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测结果',
    slot: 'checkStatus',
    tooltip: true,
    minWidth: 120,
  },
  {
    title: '检测时间',
    key: 'startTime',
    tooltip: true,
    width: 200,
  },
  // {
  //   title: '操作',
  //   slot: 'option',
  //   fixed: 'right',
  //   width: 100,
  //   align: 'center',
  // },
];
