<template>
  <div class="position-card">
    <div class="base-header">
      <div
        :class="`serial ${
          checked ? 'serial-icon-checked' : 'serial-icon-unchecked'
        }`"
      >
        {{ index }}
      </div>
      <img class="avatar" :src="record.sceneImg" />
      <span class="content">
        <div class="devicename" :title="record.deviceName">
          {{ record.deviceName }}
        </div>
        <div class="date">{{ record.absTime }}</div>
      </span>
    </div>
  </div>
</template>
<script>
export default {
  name: "card",
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
    index: {
      type: Number,
    },
    checked: {
      type: <PERSON>olean,
    },
  },
  methods: {},
};
</script>
<style lang="less" scoped>
.position-card {
  height: 100%;
  background-color: #fff;
  padding: 5px 5px 5px 0;

  .base-header {
    display: flex;
    align-items: center;
    cursor: pointer;

    .serial {
      display: inline-block;
      width: 20px;
      height: 25px;
      text-align: center;
      line-height: 20px;
      color: #fff;
      margin-right: 10px;
    }

    .serial-icon-unchecked {
      background: url("~@/assets/img/map/aggregation/track-car-marker.png")
        no-repeat center;
    }

    .serial-icon-checked {
      background: url("~@/assets/img/map/aggregation/track-car-marker-hover.png")
        no-repeat center;
    }

    .avatar {
      display: inline-block;
      width: 40px;
      height: 40px;
      border: 1px solid lightgrey;
      margin-right: 10px;
    }

    .content {
      width: 200px;
      height: 40px;

      .devicename {
        color: #333333;
        font-size: 14px;
        overflow: hidden; //隐藏文字
        text-overflow: ellipsis; //显示 ...
        white-space: nowrap; //不换行
      }

      .date {
        color: #999999;
      }

      .action {
        font-size: 12px;
        color: #999999;
      }
    }
  }

  .more-content {
    padding: 10px;

    .total-block {
      color: #999999;

      .total {
        color: #2d87f9;
      }
    }
  }
}
</style>
