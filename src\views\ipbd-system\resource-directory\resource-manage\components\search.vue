<template>
  <div class="search">
    <Form :inline="true" ref="form" :model="form">
      <FormItem label="资源名称:" class="mr-30" prop="resourceName">
        <Input
          placeholder="请输入"
          v-model="form.resourceName"
          maxlength="50"
        ></Input>
      </FormItem>
      <FormItem label="中文名称:" class="mr-30" prop="resourceNameCn">
        <Input
          placeholder="请输入"
          v-model="form.resourceNameCn"
          maxlength="50"
        ></Input>
      </FormItem>
      <FormItem label="所属目录" class="mr-30 selet-input" prop="catalogId">
        <ui-treeSelect
          ref="tree"
          class="dialog-input select-node-tree filter-tree"
          v-model="form.catalogId"
          filterable
          check-strictly
          placeholder="请选择"
          default-expand-all
          show-checkbox
          :expand-on-click-node="false"
          check-on-click-node
          node-key="id"
          :treeData="list"
        >
        </ui-treeSelect>
      </FormItem>
      <FormItem class="btn-group">
        <Button type="primary" @click="startSearch">查询</Button>
        <Button @click="resetHandle">重置</Button>
      </FormItem>
    </Form>
    <div class="btns">
      <!-- <Button @click="add"><i class="iconfont icon-jia font-16" />新增</Button> -->
      <Button @click="createResourceByExcel"
        ><i class="iconfont icon-jia font-16" />新增</Button
      >
      <Button @click="delet" v-permission="['resource-delete']">
        <i class="iconfont icon-shanchu font-16" />删除</Button
      >
      <Button @click="exportData"
        ><i class="iconfont icon-export font-16" />导出</Button
      >
    </div>
  </div>
</template>
<script>
import UiTreeSelect from "@/components/ui-tree-select";
export default {
  components: {
    UiTreeSelect,
  },
  props: {
    list: {
      //所有目录
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      form: {
        resourceNameCn: "",
        resourceName: "",
        catalogId: "",
      },
    };
  },
  created() {},
  methods: {
    // 查询
    startSearch() {
      this.$emit("searchForm", this.form);
    },
    // 重置
    resetHandle() {
      this.$refs.form.resetFields();
      this.$refs.tree.removeInputValue();
      this.form.catalogId = "";
      this.startSearch();
    },
    // 删除
    delet() {
      this.$emit("delet");
    },
    // 新增
    add() {
      this.$emit("add");
    },
    // 导出
    exportData() {
      this.$emit("exportData");
    },
    // Excel一键导入
    createResourceByExcel() {
      this.$emit("createResourceByExcel");
    },
  },
};
</script>
<style lang="less" scoped>
.search {
  .mr-30 {
    margin-right: 30px;
  }
  .dialog-input {
    width: 200px;
  }
  /deep/.ivu-select {
    width: 200px !important;
  }
  .btns {
    float: right;
    .ivu-btn {
      margin-left: 10px;
    }
  }
  /deep/ .filter-tree .el-tree-node__content {
    margin-top: 0px;
  }
}
</style>
