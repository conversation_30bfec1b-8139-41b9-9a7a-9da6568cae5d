<template>
  <div class="manage-bar auto-fill">
    <RadioGroup v-model="collectStatus" @on-change="handleChange" type="button" style="display: flex">
      <Radio :class="collectStatus === '0' ? 'active' : ''" label="0">待治理 </Radio>
      <Radio :class="collectStatus === '1' ? 'active' : ''" label="1">已治理 </Radio>
    </RadioGroup>
    <div class="org-div">
      <api-organization-tree
        class="org-width mt-md mb-sm"
        :select-tree="selectOrgTree"
        @selectedTree="selectOrgCode"
        placeholder="请选择组织机构"
      >
      </api-organization-tree>
    </div>
    <div class="manage-wrap">
      <div class="search-bar mt-md">
        <Input
          class="serch-width"
          v-model="searchData.keyWord"
          placeholder="设备编号/设备名称"
          search
          enter-button
          @on-search="handleChange"
        ></Input>
        <span class="screen-btn" @click="handleSearch" v-clickoutside="dropHide">
          <i class="icon-font icon-shaixuan f-14"></i>
          <screen-box v-if="screenboxVisible" :dictData="dictData" @search="search"></screen-box>
        </span>
      </div>
      <Button
        v-if="collectStatus === '0'"
        :loading="addDeviceLoading"
        class="mt-md"
        type="primary"
        @click="addManageDevices"
      >
        <i class="icon-font icon-tianjia f-12"></i>
        <span class="vt-middle ml-sm">新建治理设备</span>
      </Button>
    </div>
    <ul class="contentlist auto-fill mt-md" v-ui-loading="{ loading: getDeviceListLoading, tableData: deviceList }">
      <li v-for="(item, index) in deviceList" :key="index" :class="[item.id === activeId ? 'activeli' : '']">
        <i class="devicon icon-font icon-shitujichushuju f-14 mr-5px"></i>
        <div class="devnametext mr-5px ellipsis" :title="item.deviceName">{{ item.deviceName }}</div>
        <ui-btn-tip
          class="activeicon f-14 mr-sm"
          icon="icon-bianji"
          content="编辑"
          @click.native="editDevice(item)"
        ></ui-btn-tip>
        <ui-btn-tip
          class="activeicon f-14 mr-sm"
          icon="icon-shanchu"
          content="删除"
          @click.native="deleteDevice(item)"
        ></ui-btn-tip>
        <!--        <i class="icon-font icon-bianji f-14 mr-sm  activeicon" @click="editDevice(item)"></i>-->
        <!--        <i class="icon-font icon-shanchu f-14 activeicon" @click="deleteDevice(item)"></i>-->
      </li>
    </ul>
    <div class="page-box">
      <Page
        class="page"
        simple
        :current="pageData.pageNum"
        :total="pageData.totalCount"
        :page-size="pageData.pageSize"
        @on-change="changePage"
      />
    </div>
    <choose-device
      ref="ChooseDevice"
      title="新增待治理设备"
      :search-conditions="searchConditions"
      :tableColumns="tableColumns"
      :loadData="leftData"
      @getOrgCode="getOrgCode"
      @getDeviceIdList="handleSave"
    >
      <template #search-header>
        <search-list
          ref="SearchList"
          :dict-data="dictData"
          :baseErrorReasonArr="baseErrorReasonArr"
          :search-conditions="searchConditions"
          @startSearch="startSearch"
        ></search-list>
      </template>
      <template #checkStatusText="{ row }"
        ><span class="statustag" :style="{ 'background-color': checktSatusBgcolor[row.checkStatus] }">{{
          row.checkStatusText
        }}</span>
      </template>
      <!--      <template #footer>-->
      <!--        <Button @click="handleReset" class="plr-30">取 消</Button>-->
      <!--        <Button type="primary" @click="handleSave" class="plr-30">确 定</Button>-->
      <!--      </template>-->
    </choose-device>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import info from '.././info.js';
import user from '@/config/api/user';
import equipmentassets from '@/config/api/equipmentassets';

export default {
  data() {
    return {
      collectStatus: '0',
      searchData: {
        keyWord: '',
      },
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      pageData: {
        pageNum: 1,
        pageSize: 100,
        totalCount: 0,
      },
      deviceList: [],
      activeId: '',
      searchConditions: { checkStatuses: [] }, // 新建-治理设备弹框筛选条件
      tableColumns: [],
      leftData: (parameter, type) => {
        let params = {};
        if (type === 'info') {
          params = parameter; // 查询当前组织机构下的全量设备
        } else {
          params = Object.assign(
            {
              ...this.searchConditions,
            },
            parameter,
          );
        }
        return this.$http.post(equipmentassets.queryDeviceInfoPageList, params).then((res) => {
          if (res.data.data.entities.length) {
            res.data.data.entities.forEach((item) => {
              switch (item.checkStatus) {
                case '1000':
                  item.checkStatus = '0';
                  item.checkStatusText = '待检测';
                  break;
                case '0000':
                  item.checkStatus = '1';
                  item.checkStatusText = '合格';
                  break;
                default:
                  item.checkStatus = '2';
                  item.checkStatusText = '不合格';
                  break;
              }
            });
          }
          return res.data;
        });
      },
      dictData: {},
      statusObj: {},
      checktSatusBgcolor: ['var(--color-warning)', 'var(--color-success)', 'var(--color-failed)'],
      screenboxVisible: false,
      orgCode: '',
      getDeviceListLoading: false,
      addDeviceLoading: false,
      baseErrorReasonArr: [], //基本错误原因
    };
  },
  async created() {
    this.tableColumns = info.tableColumns;
    // this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode
    // this.searchConditions.orgCode = this.selectOrgTree.orgCode
    await this.getDictData();
    this.getTreatedList(); // 默认获取未治理数据
    await this.getBaseErrorReason();
  },
  methods: {
    handleChange() {
      this.deviceList = [];
      this.pageData.pageNum = 1;
      this.getTreatedList();
    },
    // 获取治理设备列表
    async getTreatedList() {
      this.deviceList = [];
      this.getDeviceListLoading = true;
      let params = {
        checkStatuses: [],
        orgCode: this.selectOrgTree.orgCode,
        collectStatus: this.collectStatus,
        pageNumber: this.pageData.pageNum,
        pageSize: this.pageData.pageSize,
      };
      params = Object.assign(params, this.searchData);
      let res = await this.$http.post(equipmentassets.getSpaceDevList, params);
      this.deviceList = res.data.data.entities;
      this.$emit('getDevList', this.deviceList);
      this.getDeviceListLoading = false;
      this.pageData.totalCount = res.data.data.total;
    },
    search(data) {
      this.pageData.pageNum = 1;
      this.searchData = Object.assign(this.searchData, data);
      this.getTreatedList();
    },
    selectOrgCode(data) {
      this.pageData.pageNum = 1;
      this.searchData.orgCode = data.orgCode;
      this.searchConditions.orgCodeList = [data.orgCode];
      this.getTreatedList();
    },
    async addManageDevices() {
      await this.handleStatusDict();
      this.$refs.ChooseDevice.init();
    },
    async editDevice(item) {
      try {
        this.activeId = item.id;
        this.$emit('findDevById', item);
      } catch (err) {
        // console.log(err)
      }
    },
    deleteDevice(item) {
      try {
        this.$Modal.confirm({
          title: '警告',
          content: `您将要删除${item.deviceName}，是否确认？`,
          onOk: async () => {
            const ids = [];
            ids.push(item.id);
            await this.$http.delete(equipmentassets.spaceDevRemove + `/${ids}`);
            this.deviceList.forEach((row, index) => {
              if (row.id === item.id) {
                this.deviceList.splice(index, 1);
              }
            });
            this.$Message.success('删除成功！');
          },
          onCancel: () => {},
        });
      } catch (err) {
        // console.log(err)
      }
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.getTreatedList();
    },
    async handleSave(chooseObj) {
      // const ids = this.$refs.ChooseDevice.chooseTableData.map(item => item.id)
      const params = {
        orgCodes: this.$refs.ChooseDevice?.searchData.orgCodeList,
        deviceId: this.searchConditions.deviceId ? this.searchConditions.deviceId : '',
        deviceName: this.searchConditions.deviceId ? this.searchConditions.deviceId : '',
        sbdwlxList: this.searchConditions.sbdwlxList ? this.searchConditions.sbdwlxList : [],
        sbgnlxList: this.searchConditions.sbgnlxList ? this.searchConditions.sbgnlxList : [],
        checkStatuses: [],
        checkStatus: '',
        cascadeReportStatus: this.searchConditions.cascadeReportStatus ? this.searchConditions.cascadeReportStatus : '',
        errorMessageList: this.searchConditions.errorMessageList ? this.searchConditions.errorMessageList : [],
        tagIds: [],
        ids: chooseObj.chooseIds,
        selectDeviceMode: this.$refs.ChooseDevice.isAll ? '1' : '0',
      };
      this.addDeviceLoading = true;
      await this.$http.post(equipmentassets.saveBatch, params);
      // if (!!this.$refs.ChooseDevice.orgCodeList && this.$refs.ChooseDevice.orgCodeList.length) {
      //   this.selectOrgTree.orgCode = this.$refs.ChooseDevice.orgCodeList[0];
      // } else {
      //   this.selectOrgTree.orgCode = null;
      // }
      this.pageData.pageNum = 1;
      this.getTreatedList();
      this.$Message.success('新建治理设备成功！');
      this.addDeviceLoading = false;
      this.$refs.ChooseDevice.handleReset();
    },
    handleReset() {
      this.searchConditions.orgCodeList = [];
      this.$refs.ChooseDevice.handleReset();
    },
    getOrgCode() {}, // 获取orgCode
    async getDictData() {
      try {
        const params = [
          'propertySearch_sbdwlx',
          'propertySearch_sbgnlx',
          'check_status',
          'error_category',
          'device_space_error_type',
        ];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    }, // 获取字典信息
    handleStatusDict() {
      this.statusObj = {};
      this.dictData['check_status'].forEach((item) => {
        this.statusObj[item.dataKey] = item.dataValue;
      });
    },
    async startSearch(data) {
      this.searchConditions = Object.assign(this.searchConditions, data);
      this.$refs.ChooseDevice.search();
    },
    handleSearch() {
      this.screenboxVisible = true;
    },
    dropHide() {
      this.screenboxVisible = false;
    },
    //获取基本错误原因，用于匹配新建治理设备查询的错误原因筛选项
    async getBaseErrorReason() {
      let {
        data: { data },
      } = await this.$http.get(equipmentassets.queryErrorReason, { params: { errorType: 'BASICS' } });
      this.baseErrorReasonArr = [];
      let arr = [];
      for (let key in data) {
        arr.push({
          dataValue: data[key],
          dataKey: key,
          errorType: key.split('#')[1],
        });
      }
      this.baseErrorReasonArr = arr;
      arr = null;
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
    SearchList: require('.././components/search-list.vue').default,
    ScreenBox: require('.././components/screen-box.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .manage-bar {
    background: rgba(7, 41, 93, 0.9);
    .contentlist {
      li:hover {
        color: #f5f5f5;
        background: rgb(2 57 96);
      }
      .devicon {
        color: #23a179 !important;
      }
      .activeli {
        color: #f5f5f5;
        background: rgba(43, 132, 226, 0.7) !important;
      }
    }
    @{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
      color: #dddddd !important;
      background: #0b396a;
      border: 1px solid transparent;
      box-shadow: -1px 0 0 0 #0b396a;
    }

    @{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
      background: #0b396a !important;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .manage-bar {
    background: #f9f9f9;
    .contentlist {
      li:hover {
        color: rgba(0, 0, 0, 0.8);
        background: #d5e7fe;
        .activeicon{
          color: rgba(0, 0, 0, 0.4);
        }
      }
      .devicon {
        color: #1faf81 !important;
      }
      .activeli {
        color: #ffffff;
        background: var(--color-primary) !important;
        .devnametext,
        .devicon,
        .activeicon {
          color: #ffffff !important;
        }
      }
    }
    @{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
      color: var(--color-primary);
      background: #ffffff;
      border: 1px solid var(--bg-btn-primary-active);
      &.active {
        color: #ffffff;
        background: var(--color-primary);
        border: 1px solid var(--color-primary);
        box-shadow: -1px 0 0 0 var(--color-primary);
      }
    }

    @{_deep} .ivu-radio-group-button .ivu-radio-wrapper:before {
      background: var(--color-primary) !important;
    }
  }
}
.flex {
  display: flex;
  justify-content: center;
  align-content: center;
}

.manage {
  &-bar {
    width: 100%;
    //width: 313px;
    height: 100%;
    max-height: 770px;
    //opacity: 0.9;
  }

  &-wrap {
    padding: 0 20px;
    border-top: 1px solid var(--border-input);
  }
}

.active {
  background: var(--color-active) !important;
}

.org-div {
  padding: 0 20px;
  @{_deep} .select-width {
    width: calc(313px - 40px) !important;
  }
}

.org-width {
  width: 100%;
}

.search-bar {
  width: 100%;
  display: flex;
  align-items: center;

  .search-width {
    flex: 1;
  }
  @{_deep}.ivu-input-group-append.ivu-input-search{
    padding: 0 10px !important;
  }

  .screen-btn {
    .flex;
    width: 34px;
    height: 34px;
    margin-left: 10px;
    border: 1px solid var(--border-input);
    opacity: 1;
    border-radius: 4px;
    cursor: pointer;
    background-color: var(--bg-btn-default);

    i {
      color: var(--color-active);
      transform: translateY(5px);
      padding: 1px 10px;
    }
  }
}

.contentlist {
  width: 100%;
  overflow: auto;

  li {
    display: flex;
    padding: 6px 20px;
    // position: relative;
    .activeicon {
      display: none;
    }

    &:hover {
      .activeicon {
        display: inline-block;
      }
    }
  }

  .devicon {
    position: relative;
    .collection-status-img {
      position: absolute;
      left: 7px;
      top: -7px;
      width: 16px;
      height: 16px;
    }
  }

  .devnametext {
    flex: 1;
    font-size: 14px;
    color: var(--color-content);
    opacity: 0.9;
  }

  .activeli {
    .activeicon {
      display: inline-block;
    }
  }
}

.mr-5px {
  margin-right: 5px;
}

.mb-10px {
  margin-bottom: 10px;
}

.statustag {
  .flex;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: var(--bg-content);
  border-radius: 4px;
}
.page-box {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  padding: 10px 20px;
  background: var(--bg-content);
  color: var(--color-content);
}

@{_deep} .ivu-radio-group-button .ivu-radio-wrapper {
  .flex;
  flex: 1;
  height: 34px;
  line-height: 34px;
  border-radius: 0 !important;
}

@{_deep} .ivu-dropdown {
  position: relative;
  width: 100%;
}
@{_deep} .ivu-page-simple {
  .ivu-page-next,
  .ivu-page-prev {
    min-width: 24px !important;
    height: 24px !important;
    background: var(--bg-btn-default) !important;
    border: 1px solid var(--border-input) !important;
    color: var(--color-page-item) !important;
  }
}
@{_deep} .ivu-page-simple .ivu-page-simple-pager input {
  background: var(--bg-btn-default) !important;
  border: 1px solid var(--border-input) !important;
  color: var(--color-content) !important;
}
</style>
