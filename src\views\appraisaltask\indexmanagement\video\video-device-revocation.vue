<template>
  <common-form v-bind="$props" ref="formData">
    <template #data-target-value="{ row }">
      <FormItem label="达标值" class="right-item mb-sm" prop="dataTargetValue">
        <span class="base-text-color mr-xs"><=</span>
        <InputNumber
          v-model.number="row.dataTargetValue"
          :max="100"
          :min="1"
          placeholder="请输入达标值"
          class="data-target-value"
        ></InputNumber>
        <span class="base-text-color">%</span>
      </FormItem>
    </template>
  </common-form>
</template>
<script>
export default {
  name: 'video-device-revocation',
  props: {
    /**
     * 模式： edit 编辑 view 查看 add 新增
     */
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    /* 表单 */
    formData: {
      required: true,
      type: Object,
      default() {
        return {};
      },
    },
    value: {},
  },
  data() {
    return {};
  },
  methods: {
    async validate() {
      try {
        return await this.$refs.formData.validate();
      } catch (error) {
        throw new Error(error);
      }
    },
  },
  components: {
    CommonForm: require('@/views/appraisaltask/indexmanagement/components/common-form.vue').default,
  },
};
</script>
<style scoped lang="less">
.data-target-value {
  width: 360px;
}
</style>
