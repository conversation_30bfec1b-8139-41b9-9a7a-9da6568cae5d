/*
 * @Author: zhengmingming zhengmingming
 * @Date: 2024-04-15 11:11:54
 * @LastEditors: du<PERSON>en
 * @LastEditTime: 2024-09-10 10:08:27
 * @FilePath: \iras-viewc:\Users\<USER>\Desktop\项目\icbd-view\src\views\holographic-archives\mixins\relativeDetailMixin.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { relationIntro } from "@/api/number-cube";
import { mapGetters } from "vuex";

/**
 * 获取具体关系详情
 * */
export default {
  computed: {
    ...mapGetters("systemParam", ["archiveObj"]),
  },
  methods: {
    getRelationsEntitys(label, relations, entitys) {
      const relationsEntitys = [];
      relations
        .filter((relation) => relation.label === label)
        .forEach((el) => {
          const { targetId, label, items } = el;
          const selfItem = {
            ...this.MixinRoot,
            traitImg: this.MixinRoot.propertyIcon || this.MixinRoot.icon,
          };
          const otherItem = entitys.find(
            (entity) => entity.entityId === targetId
          );
          if (otherItem) {
            relationsEntitys.push({
              data: {
                otherItem: { ...otherItem },
                selfItem: selfItem,
              },
              myselfBigPic: selfItem.sceneImg,
              myselfPic: selfItem.traitImg,
              myselfText: this.$route.query.source === "car" ? "本车" : "本人",
              otherText: otherItem.displayField,
              otherPic: otherItem.propertyIcon,
              otherBigPic: otherItem.propertyIcon,
              times: items,
              relationType: label,
            });
          }
        });
      return relationsEntitys.sort((a, b) => b.times - a.times).splice(0, 3);
    },
    getRelationIntro_V2(data) {
      const { entityGroups, relations, entitys } = data;
      this.allRelativeList = entityGroups.map((item) => {
        const { labelCn, label } = item;
        return {
          title: labelCn,
          list: this.getRelationsEntitys(label, relations, entitys),
        };
      });
    },
    async getRelationIntro(item) {
      const sourceMap = {
        people: "realNameGraphInfo",
        video: "videoGraphInfo",
        place: "placeGraphInfo",
        car: "vehicleGraphInfo",
        zdr: "realNameGraphInfo",
        device: "deviceGraphInfo;",
      };
      const { source, archiveNo } = this.$route.query;
      if (!source) {
        // 设备档案目前没有source参数，在这里设置一下
        source = "device";
      }
      const graphInstanceId = this.archiveObj[sourceMap[source]]?.instanceId;
      try {
        let { data } = await relationIntro({
          relationType: item.id,
          searchKey: [archiveNo],
          graphInstanceId,
        });
        !data ? (data = []) : null;
        if (!data.length) return;
        // 只截取记录的3条
        let list = data.length > 2 ? data.splice(0, 3) : data;
        this.allRelativeList.push({
          title: item.labelCn,
          list: list.map((one) => {
            // 如果本人数据没有，就用baseInfo的
            let selfItem = {};
            let otherItem = {};
            if (one.detail.length > 1) {
              console.log(this.MixinRoot, "this.MixinRoot");
              let Index = one.detail.findIndex((item) => {
                return item.vid === this.MixinRoot.displayField;
              });
              selfItem = Index === 0 ? one.detail[0] : one.detail[1];
              otherItem = Index === 0 ? one.detail[1] : one.detail[0];
            }
            if (one.detail.length == 1) {
              selfItem = {
                ...this.MixinRoot,
                traitImg: this.MixinRoot.propertyIcon || this.MixinRoot.icon,
              };
              otherItem = one.detail[0];
            }
            return {
              data: {
                otherItem: otherItem,
                selfItem: selfItem,
              },
              describe: "抓拍",
              myselfBigPic: selfItem.sceneImg,
              myselfPic: selfItem.traitImg,
              myselfText: this.$route.query.source === "car" ? "本车" : "本人",
              otherText: otherItem.vid,
              otherPic: otherItem.traitImg,
              otherBigPic: otherItem.sceneImg,
              times: one.count,
              relationType: item.id,
            };
          }),
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    // MixinEntityGroups 这个在 mixin: relativeGraphMixin 这个里面
    MixinEntityGroups(val) {
      this.getRelationIntro_V2(val);
      // this.MixinEntityGroups.map(async (item) => {
      //   console.log("item is ", item);
      //   this.getRelationIntro(item);
      // });
    },
  },
};
