<template>
  <ui-modal
    v-model="visible"
    :title="title"
    :styles="styles"
    :footer-hide="isView"
    :class="{ 'modal-footer-hide': isView }"
  >
    <div class="image-wrapper" v-ui-loading="{ loading: deviceListLoading }">
      <div class="image-wrapper-left">
        <div class="image-carousel-wrapper">
          <PicCarousel
            :visible="visible"
            :imgList="[activePicItem]"
            :filed-name-map="filedNameMap"
            :show-algorithm-info="true"
          >
            <template #small-pic>
              <div class="small-pic" v-if="showSmallPic">
                <ui-image :src="activePicItem?.[filedNameMap.smallPicName]" />
              </div>
            </template>
          </PicCarousel>
        </div>
      </div>
      <div v-if="!isView" class="image-wrapper-right">
        <div class="mb-sm new-status-box">
          <span class="base-text-color inline mr-lg vt-middle new-status-box-one">
            最新状态：
            <span :class="[getNewStatus(activePicItem[filedNameMap.qualified]).className]">
              {{ getNewStatus(activePicItem[filedNameMap.qualified]).text }}
              {{ activePicItem.dataMode === '3' ? `(人工)` : '' }}
            </span>
          </span>
          <span
            class="base-text-color inline mr-lg vt-middle new-status-box-item-top"
            :title="activePicItem[filedNameMap.resultTip]"
          >
            原因：
            <span
              class="ellipsis error-description inline"
              :class="[getNewStatus(activePicItem[filedNameMap.qualified]).className]"
            >
              {{ activePicItem[filedNameMap.resultTip] }}
            </span>
          </span>
        </div>
        <div class="flex-div base-text-color">
          <span>人工复核：</span>
          <div class="radio-box">
            <RadioGroup class="radio-group" v-model="isQualify" @on-change="changePicRadio">
              <Radio class="mr-lg" label="1"> 图片可用</Radio>
              <Radio class="mr-lg" label="2"> 图片不可用</Radio>
            </RadioGroup>
            <CheckboxGroup
              class="checkbox-group"
              v-model="causeErrorList"
              v-show="isQualify === '2'"
              @on-change="handleUnQualified"
            >
              <span class="error-title">不合格原因: </span>
              <div class="error-item">
                <Checkbox class="mr-lg" v-for="(item, index) in errorCodeList" :key="index" :label="item.value">
                  {{ item.label }}
                </Checkbox>
              </div>
            </CheckboxGroup>
          </div>
        </div>
      </div>
    </div>
    <template slot="footer">
      <Button :loading="deviceBtnloading.preBtn" type="primary" class="plr-30" @click="preBulk" v-show="showPreButton">
        上一张
      </Button>
      <Button class="plr-30" type="primary" @click="artificial" :loading="saveModalLoading">确定复核结果</Button>
      <Button
        :loading="deviceBtnloading.nextBtn"
        type="primary"
        class="plr-30"
        @click="nextBulk"
        v-show="showNextButton"
      >
        下一张
      </Button>
    </template>
  </ui-modal>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  name: 'modePic',
  props: {
    // 本页所有数据
    tableData: [],
    totalCount: {},
    pageData: {
      default: () => {
        return {
          pageNum: 1,
          pageSize: 20,
        };
      },
    },
    reviewRowData: {}, // 当前的设备
    errorCodeList: {
      // 图片模式错误原因
      default: () => [],
    },
    title: {
      type: String,
      default: '图片人工复核',
    },
    value: {
      type: Boolean,
      default: false,
    },
    // 自定义字段名称 [兼容车辆和人脸字段不一致问题]
    filedNameMap: {
      default: () => {
        // 默认以人脸为主
        return {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          qualified: 'qualified', // 不合格
          description: 'reason', // 设备备注
          resultTip: 'resultTip', // 图片备注
          deviceDetailId: 'faceDeviceDetailId', // 单个图片的id
        };
      },
    },
    isView: {
      type: Boolean,
      default: false,
    },
    showSmallPic: {
      type: Boolean,
      default: true,
    },
    // 设备列表搜索条件
    searchParames: {
      type: Object,
    },
    // 调用方处理， 主要是 为了保持与原有的新增字段逻辑一直，确保字段没有丢失 , 目前整理 只有两个字段： 显示的不合格原因、 是否合格字段
    customValueField: {
      type: Object,
      default: () => {
        return {
          reason: 'resultTip', //  自定义字段： 取值字段
          qualified: 'qualified',
        };
      },
    },
    // 获取列表的接口 --- 若调用接口不一样，则需要调用方自行处理
    getListApi: {
      type: Function,
    },
  },
  computed: {
    showPreButton() {
      // 是第一页 && 是第一条图片 才不展示
      return this.cacheDeviceList.length && !(this.currentPageNum === 1 && this.activePicItemIndex === 0);
    },
    showNextButton() {
      // 最后一页，并且是最后一条图片才不展示
      return this.cacheDeviceList.length && !this.activePicItem.isLastPage;
    },
    // 设备的最新状态
    getNewStatus() {
      return (str) => {
        return {
          text: str == '1' ? '合格' : '不合格',
          className: str == '1' ? 'c-green' : 'c-red',
        };
      };
    },
  },
  data() {
    return {
      saveModalLoading: false,
      visible: false,
      isQualify: '1',
      // resultTip: '',
      activePicItem: {
        [this.filedNameMap.qualified]: '1', // 默认合格
      }, // 当前处理的图片
      activePicItemIndex: 0, // 当前处理图片的index
      causeErrorList: [],
      styles: {
        width: '9.5rem',
        height: '4.4rem',
      },
      deviceBtnloading: {
        preBtn: false,
        nextBtn: false,
      },

      // 一页一条数据的请求方案： 需要维护一个  isUnExistTable: Boolean， 代表该数据是否已经不符合当前筛选条件下的数据了  true: 与条件不符合，已不存在   false: 满足条件，存在
      // currentDeviceIndex: 0,
      cacheDeviceList: [], // 只有请求接口才会 缓存起来
      currentPageNum: 1, // 当前页码（基于总数算，对于一页一条数据去请求，该字段其实也代表了在总数据中第几条数据）
      isReview: false, // 是否复核过
      devicePageData: {
        pageSize: 50,
      },
      deviceListLoading: false,
    };
  },
  methods: {
    async initFn() {
      this.isReview = false;

      // 该条数据 在 外部表格中的索引
      let index = this.tableData.findIndex((item) => item.id === this.reviewRowData.id);
      // 该条数据 在 总数中的索引
      this.currentPageNum = (this.pageData.pageNum - 1) * this.pageData.pageSize + index + 1;

      // 默认查询 该条数据所在页码对应的 devicePageData.pageSize条数据
      this.cacheDeviceList = [];
      let res = await this.getDeviceList();
      this.cacheDeviceList = res.entities;
      this.activePicItemIndex = this.cacheDeviceList.findIndex((item) => item.id === this.reviewRowData.id);

      this.changeCarousel(this.activePicItemIndex);
    },
    handleUnQualified() {},
    changePicRadio() {
      // this.resultTip = ''
      this.causeErrorList = [];
    },
    async preBulk() {
      let { nextBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }

      try {
        this.deviceBtnloading.preBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.activePicItemIndex - 1]?.isUnExistTable) {
          this.currentPageNum--;
        }

        if (this.activePicItemIndex === 0) {
          // 已经不存在上一条，则调接口
          let res = await this.getDeviceList();
          let endIndex = res.entities.length - 1;
          // 注意： 要添加到数组前面
          this.cacheDeviceList = [...res.entities, ...this.cacheDeviceList];
          this.activePicItemIndex = endIndex;
        } else {
          this.activePicItemIndex--;
        }
        this.changeCarousel(this.activePicItemIndex);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.preBtn = false;
      }
    },
    async nextBulk() {
      let { preBtn } = this.deviceBtnloading;
      if (this.saveModalLoading || preBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      try {
        this.deviceBtnloading.nextBtn = true;
        // isUnExistTable： true  --> 代表 该条数据已经不存在 当前筛选条件下列表数据
        if (!this.cacheDeviceList[this.activePicItemIndex].isUnExistTable) {
          this.currentPageNum++;
        }
        this.activePicItemIndex++;

        if (!this.cacheDeviceList[this.activePicItemIndex]) {
          // 缓存中不存在下一条数据，则调接口
          let res = await this.getDeviceList();
          // 注意： 要添加到数组后面
          this.cacheDeviceList = [...this.cacheDeviceList, ...res.entities];
        }
        this.changeCarousel(this.activePicItemIndex);
      } catch (error) {
        console.log(error);
      } finally {
        this.deviceBtnloading.nextBtn = false;
      }
    },
    changeCarousel(index) {
      try {
        // 如果是第一条数据，就得获取上一页的数据去
        this.activePicItemIndex = index;
        this.activePicItem = this.cacheDeviceList[index];
        // this.resultTip = this.activePicItem[this.filedNameMap.resultTip]
        this.isQualify = this.activePicItem[this.filedNameMap.qualified] == '1' ? '2' : '1';
        // this.causeErrorList = this.activePicItem.causeError ? this.activePicItem.causeError.split(',') : [];
        this.causeErrorList = [];
      } catch (error) {
        console.log(error);
      }
    },
    async artificial() {
      let { preBtn, nextBtn } = this.deviceBtnloading;
      if (preBtn || nextBtn) {
        this.$Message.warning('数据正在请求中，请稍等！');
        return;
      }
      let params = {
        data: {
          id: this.activePicItem.id,
          qualified: this.isQualify,
          // reason: this.resultTip,
          deviceDetailId: this.activePicItem[this.filedNameMap.deviceDetailId],
          deviceId: this.activePicItem.deviceId,
          errorCode: (this.causeErrorList && this.causeErrorList.join(',')) || '',
          type: 'detail',
        },
        indexId: this.$route.query.indexId,
        batchId: this.$route.query.batchId,
      };
      try {
        this.saveModalLoading = true;

        let res = await this.$http.post(evaluationoverview.manualRecheck, params);
        this.$Message.success(res.data.msg);
        this.isReview = true;

        // 复核成功后，需要 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
        this.updateInfo();
      } catch (err) {
        console.log(err);
      } finally {
        this.saveModalLoading = false;
      }
    },
    // 获取设备数据
    async getDeviceList(customParams, isUpdate = false) {
      try {
        if (!isUpdate) {
          this.deviceListLoading = true;
        }
        let res = {};
        let { pageSize } = this.devicePageData;
        // 使用调用方参入的函数
        if (this.getListApi) {
          let params = {
            pageSize: pageSize,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            isUpdate: isUpdate,
            currentRow: { ...this.activePicItem },
          };
          res = await this.getListApi(params);
        } else {
          let { regionCode, orgCode, statisticType, indexId, batchId } = this.$route.query;
          let params = {
            indexId: indexId,
            batchId: batchId,
            displayType: statisticType,
            orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
            customParameters: this.searchParames,
            pageNumber: Math.floor((this.currentPageNum - 1) / pageSize) + 1,
            pageSize: pageSize,
          };
          // 以传入参数为主
          if (customParams) {
            params = { ...params, ...customParams };
          }
          res = await this.$http.post(evaluationoverview.getPolyData, params);
        }

        let { entities, lastPage } = res.data.data;

        if (!isUpdate) {
          // 注意： 如果中间存在 isUnExistTable=true , 那此时查询到的列表数据，存在部分数据已缓存过了 --- 每页一条查询是不会的，但现在支持根据 devicePageData.pageSize可配置
          entities = entities.filter((item) => !this.cacheDeviceList.some((cacheItem) => cacheItem.id === item.id));
        }

        entities = entities.map((item, index) => {
          // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
          if (this.customValueField) {
            Object.keys(this.customValueField).forEach((key) => {
              item[key] = item[this.customValueField[key]];
            });
          }
          return {
            ...item,
            isLastPage: lastPage && index === entities.length - 1, // 是否为最后一条数据
          };
        });
        res.data.data.entities = entities;

        return res.data.data;
      } catch (error) {
        console.log(error);
        throw new Error();
      } finally {
        this.deviceListLoading = false;
      }
    },
    // 更新下该条数据 +  判断下 该条数据是否还存在于 当前筛选条件下列表数据中
    async updateInfo() {
      if (this.searchParames?.outcome && this.searchParames?.outcome !== this.isQualify) {
        // 【检查结果】 刷选条件有值，并且与  当前设备复核结果不同
        this.cacheDeviceList[this.activePicItemIndex].isUnExistTable = true;
        this.activePicItem.isUnExistTable = true;

        // 主要是 下面调接口更新需要时间， 接口完成前 先前端替换， 这样就不会影响到上一条下一条显示数据没有更新问题 ， 且也不会影响到 关闭弹框时所做的判断
        // 其实就是 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
        if (this.customValueField) {
          this.cacheDeviceList[this.activePicItemIndex][this.customValueField.qualified] = this.isQualify;
          this.cacheDeviceList[this.activePicItemIndex].causeError =
            (this.causeErrorList && this.causeErrorList.join(',')) || '';
          this.activePicItem[this.customValueField.qualified] = this.isQualify;
          this.activePicItem.causeError = (this.causeErrorList && this.causeErrorList.join(',')) || '';
        }
      }
      let isLastPage = this.activePicItem.isLastPage;
      let params = {
        customParameters: {
          id: this.activePicItem.id,
        },
        pageNumber: 1,
      };
      let res = await this.getDeviceList(params, true);
      let obj = res.entities[0];
      // 保持与原有 指标review-particular.vue组件调用处理逻辑 一样，确保字段没有 少
      if (this.customValueField) {
        Object.keys(this.customValueField).forEach((key) => {
          obj[key] = obj[this.customValueField[key]];
        });
      }
      let activePicItemIndex = this.cacheDeviceList.findIndex((item) => item.id === obj.id);
      let info = {
        ...this.cacheDeviceList[activePicItemIndex],
        ...obj,
        isLastPage: isLastPage, // 保留之前的状态值
      };
      this.cacheDeviceList[activePicItemIndex] = info;
      if (this.activePicItem.id === obj.id) {
        this.activePicItem = info;
      }
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.initFn();
        }
      },
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
      if (!val) {
        let closeIndex = this.currentPageNum - 1;
        // 特殊处理，关闭弹框时，如果是 最后一条数据 且 复核 的情况
        let { isLastPage } = this.activePicItem;
        if (
          isLastPage &&
          this.searchParames?.outcome &&
          this.searchParames?.outcome !== this.activePicItem[this.customValueField.qualified]
        ) {
          closeIndex = closeIndex - 1;
        }
        this.$emit('closeFn', {
          isReview: this.isReview,
          closeIndex: closeIndex >= 0 ? closeIndex : 0,
        });
      }
    },
    activePicItem: {
      handler() {
        this.isQualify = this.activePicItem[this.filedNameMap.qualified] == '1' ? '2' : '1';
        this.causeErrorList = [];
      },
      deep: true,
    },
  },
  components: {
    PicCarousel: require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/pic-carousel.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
@{_deep} .ivu-modal-header {
  padding: 0;
}
@{_deep}.ivu-modal-body {
  padding: 5px 0 0;
  height: calc(100% - 66px) !important;
}
.modal-footer-hide @{_deep}.ivu-modal-body {
  height: 100% !important;
}
@{_deep}.image-carousel {
  height: 100%;
  .img-list {
    margin-top: 0 !important;
    height: 100%;
  }
}
@{_deep}.ivu-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}
@{_deep}.image-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .small-pic {
    position: absolute;
    left: 1px;
    top: 1px;
    width: 23%;
    & > .ui-image {
      border: 1px solid var(--color-primary);
    }
  }
  .image-wrapper-left {
    flex: 1;
    .image-carousel-wrapper {
      position: relative;
      height: 100% !important;
      overflow: hidden;
    }
  }
  .image-wrapper-right {
    width: 100%;
    height: 120px;
    padding: 10px 20px;
    border-top: 1px solid var(--border-modal-footer);
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 28px;
    .flex-div {
      display: flex;
      .radio-box {
        flex: 1;
        display: flex;
        .radio-group {
          width: 290px;
        }
        .checkbox-group {
          flex: 1;
          display: flex;
          .error-title {
            display: inline-block;
            width: 105px;
          }
          .error-item {
            flex: 1;
          }
        }
      }
    }
  }
}
.new-status-box {
  display: flex;
  .error-description {
    max-width: calc(100% - 50px);
  }
  .new-status-box-one {
    max-width: 220px;
  }
  .new-status-box-item-top {
    width: calc(100% - 250px);
    display: flex;
  }
  .new-status-box-item {
    width: 650px;
    display: flex;
    .error-description {
      max-width: calc(100% - 130px);
    }
  }
}
</style>
