<template>
  <ui-modal title="不合格原因" v-model="visible" :styles="styles" :footer-hide="true">
    <ui-select-tabs
      class="tabs-ui"
      v-if="tabsList.length"
      :multiSelect="false"
      @selectInfo="selectInfo"
      :list="tabsList"
    >
    </ui-select-tabs>
    <div class="content-div">
      <ul class="image-tips-ul" v-if="normalTipShow">
        <li v-for="(item, index) of nomalLabelList" :key="index">
          <span class="label mr-xs">{{ item.name }}: </span>
          <span
            class="content"
            :class="{
              'font-table-action': index !== nomalLabelList.length - 1,
              'font-red': index === nomalLabelList.length - 1,
            }"
          >
            {{ item.value }}
          </span>
        </li>
      </ul>
      <ul class="determine-ul mt-sm" v-if="algorithmResultShow">
        <li class="determine-ul-li-title">
          <span>检测算法</span>
          <span>判定结果</span>
        </li>
        <li class="determine-table-ul-li" v-for="(item, index) of tableList" :key="index">
          <span class="label">{{ item.name }}</span>
          <span class="" :class="item.value ? 'font-green' : 'font-warning'">
            {{ handleMessage(item) }}
          </span>
        </li>
        <li class="determine-ul-li-result">
          <p>综合判定</p>
          <p>
            <span
              class="icon-font icon-zhiliangfen-line standard_icon"
              :class="isUnQuatify ? 'font-warning' : 'font-green'"
            >
              <i class="icon_text_error" :class="isUnQuatify ? 'font-warning' : 'font-green'">
                {{ isUnQuatify ? '不达标' : '达标' }}
              </i>
            </span>
          </p>
        </li>
      </ul>
      <p class="url-p base-text-color" v-if="textTipShow">
        结论：
        <span class="font-warning">大图URL缺失或大图URL不可访问</span>
      </p>
      <loading v-if="errorLoading"></loading>
    </div>
  </ui-modal>
</template>
<script>
import tasktracking from '@/config/api/tasktracking';
let needErrorModalEnums = {
  // 图像抓拍时间准确性检测
  '8008': { componentCode: '8008', type: 'normalTipShow' },
  // 图像上传及时性检测
  '8009': { componentCode: '8009', type: 'normalTipShow' },
  // 大小图关联正确检测
  '8006': { componentCode: '8006', type: 'algorithmResultShow' },
  // 小图唯一人脸检测处理
  '8007': { componentCode: '8007', type: 'algorithmResultShow' },
  // 大图URL检测
  '8010': { componentCode: '8010', type: 'textTipShow' },
};
export default {
  props: {
    popUpTitle: {
      default: '非唯一人脸判定',
    },
    disqualifyItem: {
      type: Object,
    },
    value: {},
  },
  data() {
    return {
      visible: false,
      styles: {
        //top: "1.2rem",
        width: '80%',
      },
      tabsList: [],
      nomalLabelList: [
        { name: '抓拍时间', value: '' },
        { name: '入库时间', value: '' },
        { name: '结论', value: '' },
      ],
      normalTipShow: false,
      algorithmResultShow: false,
      textTipShow: false,
      currentTabObject: {},
      allTabMessageObject: {},
      isImportantDevice: null,
      tableList: [],
      errorLoading: false,
    };
  },
  created() {},
  mounted() {},
  computed: {
    isUnQuatify() {
      return this.tableList.some((item) => {
        return !item.value;
      });
    },
  },
  methods: {
    handleMessage(item) {
      if (item.isface) {
        return item.value ? '唯一人脸' : '非唯一人脸';
      } else {
        return item.value ? '正确' : '错误';
      }
    },
    selectInfo(val) {
      this.nomalLabelList = [];
      this.tableList = [];
      this.normalTipShow = false;
      this.algorithmResultShow = false;
      this.textTipShow = false;
      this.handleShowType(val);
    },
    // 查询人脸不合格原因
    async showExceptMessage() {
      this.errorLoading = true;
      try {
        let params = {
          faceLibAbnormalId: this.disqualifyItem.id,
          //topicComponentId: 49, //this.errorTabItemComponentId,
          deviceId: this.disqualifyItem.deviceId,
        };
        let { data } = await this.$http.post(tasktracking.queryFaceLibAbnormalInfoResult, params);
        this.isImportantDevice = data.data.isImportantDevice;
        data.data.list.forEach((item) => {
          // 图片重复暂时不做
          if (item.componentCode !== '5001') {
            this.tabsList.push({
              name: item.errorMessage,
              select: false,
              itemMessage: item,
            });
            this.allTabMessageObject[item.componentCode] = item;
          }
        });
        this.selectInfo(this.tabsList[0]);
        this.errorLoading = false;
      } catch (err) {
        this.errorLoading = false;
        console.log(err);
      }
    },
    handleShowType(val) {
      // 当前的tab的componentCode来判断展示那种形式
      let curComponentCode = val.itemMessage.componentCode;
      let modalTypeFlag = needErrorModalEnums[curComponentCode].type;
      this[modalTypeFlag] = true;
      // 当前tab下的信息
      let curInterfaceMessage = this.allTabMessageObject[curComponentCode];
      /*-------------普通的展示 -------------*/
      if (modalTypeFlag === 'normalTipShow') {
        // 图像上传及时性检测
        if (curComponentCode === '8009') {
          this.imageUploadTest(curInterfaceMessage);
        }
        // 图像抓拍时间准确性检测
        if (curComponentCode === '8008') {
          this.imageCaptureTest(curInterfaceMessage);
        }
      }
      /*-------------算法的展示 -------------*/
      if (modalTypeFlag === 'algorithmResultShow') {
        // 大小图关联正确检测
        if (curComponentCode === '8006') {
          this.bigAndSmallPicTest(curInterfaceMessage);
        }
        // 小图唯一人脸检测处理
        if (curComponentCode === '8007') {
          this.smallOnlyTest(curInterfaceMessage);
        }
      }
      /*-------------文字展示 ----------------*/
      if (modalTypeFlag === 'textTipShow') {
        this.algorithmResultShow = false;
        this.normalTipShow = false;
      }
    },
    // 图像上传及时性检测
    imageUploadTest(curInterfaceMessage) {
      this.nomalLabelList = [
        { name: '设备类型', value: '' },
        { name: '抓拍时间', value: curInterfaceMessage.logTime },
        { name: '入库时间', value: curInterfaceMessage.createTime },
        { name: '时延', value: '' },
      ];
      const timeEnum = {
        '1': '小时',
        '2': '分',
        '3': '秒',
      };
      let isTime = '';
      let isTimeType = '';
      if (this.isImportantDevice) {
        this.nomalLabelList[0].value = '重点人脸卡口';
        isTimeType = curInterfaceMessage.extraParamObj.importantTime;
        isTime = curInterfaceMessage.extraParamObj.important;
      } else {
        this.nomalLabelList[0].value = '普通人脸卡口';
        isTimeType = curInterfaceMessage.extraParamObj.generalTime;
        isTime = curInterfaceMessage.extraParamObj.general;
      }
      // 时延赋值
      const delaytime = `${curInterfaceMessage.timeDifference}，超过${isTime}${timeEnum[isTimeType]}`;
      let last = this.nomalLabelList[3];
      last.value = delaytime || '暂无';
    },
    // 图像抓拍时间准确性检测
    imageCaptureTest(result) {
      this.nomalLabelList = [
        { name: '抓拍时间', value: this.disqualifyItem.createTime },
        { name: '入库时间', value: this.disqualifyItem.logTime },
        { name: '结论', value: result.errorMessage },
      ];
    },
    // 大小图关联正确检测
    bigAndSmallPicTest(result) {
      this.tableList = result.extraResultObj.map((item) => {
        return {
          name: item.key,
          value: item.value,
          isface: false,
        };
      });
    },
    // 小图唯一人脸检测处理
    smallOnlyTest(result) {
      this.tableList = result.extraResultObj.map((item) => {
        return {
          name: item.key,
          value: item.value,
          isface: true,
        };
      });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      if (val) {
        this.tabsList = [];
        this.nomalLabelList = [];
        this.currentTabObject = {};
        this.allTabMessageObject = {};
        this.isImportantDevice = null;
        this.tableList = [];
        this.showExceptMessage();
      }
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.image-tips-ul {
  display: flex;
  align-items: center;
  flex-direction: column;
  color: #fff;
  padding: 70px 0 0 0;
  > li {
    height: 40px;
    line-height: 40px;
    width: 300px;
    .label {
      display: inline-block;
      width: 60px;
      text-align: left;
    }
  }
}
.determine-ul {
  color: #fff;
  > li {
    height: 60px;
    line-height: 60px;
    display: flex;
    padding: 0 20px;
    > span {
      flex: 1;
    }
  }
  &:nth-child(even) {
    background: #041939;
  }
  &:nth-child(odd),
  .determine-ul-li-result {
    background: #062042;
  }
  .determine-ul-li-title {
    background: #092955;
  }
  .determine-ul-li-result {
    height: 100px;
    line-height: 100px;
    color: var(--color-primary);
    display: flex;
    > p {
      flex: 1;
    }
  }
  .standard_icon {
    vertical-align: middle;
    font-size: 120px;
    position: relative;
    .icon_text_error {
      font-size: 16px;
      position: absolute;
      right: 37px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
    .icon_text_succeed {
      font-size: 16px;
      position: absolute;
      right: 44px;
      top: 10px;
      font-weight: bold;
      transform: rotate(-32deg);
    }
  }
}
.content-div {
  min-height: 400px;
  position: relative;
}
.url-p {
  text-align: center;
  padding-top: 150px;
}
</style>
