<template>
  <div class="information-statistics">
    <ul class="statistics-ul">
      <li
        v-for="(item, index) in statisticsList"
        :key="index"
        :class="[item.liBg, taskRun.includes(item.key) ? 'pointer' : '']"
        :title="taskRun.includes(item.key) ? '详情' : ''"
        @click="taskSituationShow(item)"
      >
        <div class="monitoring-data">
          <em class="icon-font f-50" :class="[item.icon, item.iconColor]"></em>
          <span class="information-data">
            <p>{{ item.name }}</p>
            <p class="statistic-num" :class="item.textColor">
              <countTo :startVal="0" :endVal="item.value || 0" :duration="3000"></countTo>
            </p>
          </span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'statistic-card',
  props: {
    statisticsList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      taskRun: ['processingCount', 'todayCompleteCount', 'todayPendingCount', 'errorCount'],
      cardObj: {},
    };
  },
  methods: {
    taskSituationShow(item) {
      if (this.taskRun.includes(item.key)) {
        this.$emit('taskSituationShow');
      }
    },
  },
  watch: {},
  components: {
    countTo: require('vue-count-to').default,
  },
};
</script>

<style scoped lang="less">
.information-statistics {
  display: flex;
  width: 100%;
  height: 100%;
  .icon-bg1 {
    background: var(--icon-card-gradient-mint-green) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .icon-bg2 {
    background: var(--icon-card-gradient-cyan) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .icon-bg3 {
    background: var(--icon-card-gradient-deep-purple) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .icon-bg4 {
    background: var(--icon-card-gradient-green) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .icon-bg5 {
    background: var(--icon-card-gradient-orange) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .icon-bg6 {
    background: var(--icon-card-gradient-light-pink) !important;
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }
  .color1 {
    color: var(--font-card-mint-green) !important;
  }
  .color2 {
    color: var(--font-card-cyan) !important;
  }
  .color3 {
    color: var(--font-card-deep-purple) !important;
  }
  .color4 {
    color: var(--font-card-green) !important;
  }
  .color5 {
    color: var(--font-card-orange) !important;
  }
  .color6 {
    color: var(--font-card-light-pink) !important;
  }

  .li-bg1 {
    background: var(--bg-card-mint-green) !important;
    box-shadow: var(--bg-card-mint-green-shadow);
  }
  .li-bg2 {
    background: var(--bg-card-cyan) !important;
    box-shadow: var(--bg-card-cyan-shadow);
  }
  .li-bg3 {
    background: var(--bg-card-deep-purple) !important;
    box-shadow: var(--bg-card-deep-purple-shadow);
  }
  .li-bg4 {
    background: var(--bg-card-green) !important;
    box-shadow: var(--bg-card-green-shadow);
  }
  .li-bg5 {
    background: var(--bg-card-orange) !important;
    box-shadow: var(--bg-card-orange-shadow) !important;
  }
  .li-bg6 {
    background: var(--bg-card-light-pink) !important;
    box-shadow: var(--bg-card-light-pink-shadow) !important;
  }

  .statistics-ul {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    li {
      flex: 1;
      height: 100px;
      display: flex;
      margin-right: 10px;
      align-items: center;
      border-radius: 4px;
      &:last-of-type{
        margin-right: 0;
      }
      div {
        display: flex;
        align-items: center;

        em {
          display: inline-block;
          height: 60px;
          width: 60px;
          line-height: 60px;
          text-align: center;
          font-size: 60px;
          margin-left: 40px;
        }
        .information-data {
          display: inline-block;
          height: 60px;
          flex: 1;
          margin-left: 14px;
          text-align: left;
          p {
            white-space: nowrap;
            font-style: normal;
            height: 30px;
            line-height: 30px;
            color: var(--color-content);
            font-size: 12px;
          }
          .statistic-num {
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
