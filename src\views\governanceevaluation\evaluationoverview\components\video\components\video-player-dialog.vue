<template>
  <ui-modal
    class="video-player"
    v-model="videoVisible"
    title="播放视频"
    :styles="videoStyles"
    footerHide
    @onCancel="onCancel"
  >
    <div style="margin-top: -15px">
      <EasyPlayer :videoUrl="videoUrl" fluent stretch ref="easyPlay"></EasyPlayer>
    </div>
  </ui-modal>
</template>

<script>
import video from '@/config/api/vedio-threm';
export default {
  name: 'video-player-dialog',
  data() {
    return {
      videoVisible: false,
      videoStyles: {
        width: '5rem',
      },
      playDeviceCode: '',
      videoUrl: '',
    };
  },
  methods: {
    init(deviceId) {
      this.videoVisible = true;
      this.getVideoUrl(deviceId);
    },
    onCancel() {
      this.videoUrl = '';
      this.$http.post(video.stop + this.playDeviceCode);
    },
    async getVideoUrl(deviceId) {
      try {
        let data = { deviceId };
        let res = await this.$http.post(video.getplay, data);
        if (res.data.msg != '成功') {
          this.$Message.error(res.data.msg);
        }
        this.videoUrl = res.data.data.hls;
      } catch (err) {
        console.log(err);
      }
    },
  },
  components: {
    EasyPlayer: require('@/components/EasyPlayer').default,
  },
};
</script>

<style></style>
