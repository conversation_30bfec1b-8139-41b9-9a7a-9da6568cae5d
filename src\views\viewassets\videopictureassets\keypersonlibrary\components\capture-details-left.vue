<template>
  <div class="capture-details-left">
    <div :class="['capture-details-left-sculpture', personinfo.identityPhoto ? '' : 'no-img-border']">
      <ui-image :src="personinfo.identityPhoto" />
    </div>
    <p class="capture-details-left-item">
      <span class="capture-details-left-item-label">姓名：</span>
      <span class="capture-details-left-item-value">{{ personinfo.name || '无' }}</span>
    </p>
    <p class="capture-details-left-item">
      <span class="capture-details-left-item-label">证件号：</span>
      <span class="capture-details-left-item-value">{{ personinfo.idCard || '无' }}</span>
    </p>
    <tags-more :tag-list="tagList" :default-tags="4" placement="left-start" bg-color="#2D435F"></tags-more>
  </div>
</template>
<script>
export default {
  props: {
    personinfo: {
      type: Object,
      default: () => {},
    },
    tagList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    TagsMore: require('@/components/tags-more').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.capture-details-left {
  width: 362px;
  height: 100%;
  padding: 50px 20px 27px;
  border-right: 1px solid var(--border-modal-footer);
  .no-img-border {
    border: 1px solid var(--border-modal-footer);
  }
  &-sculpture {
    width: 100%;
    height: 312px;
    margin-bottom: 27px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-item {
    margin-bottom: 14px;
    font-size: 14px;
    &-label {
      color: var(--color-info-card-label);
    }
    &-value {
      color: var(--color-info-card-content);
    }
  }
}
</style>
