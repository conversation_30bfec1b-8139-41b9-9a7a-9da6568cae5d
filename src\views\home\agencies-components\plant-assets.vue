<template>
  <!-- 关键属性检测 -->
  <div class="plant-assets" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shebeizichan">
      基础数据监测
      <template #filter>
        <TypeSelect @getName="getName" :dataList="typeList" defaultName="全部指标"></TypeSelect>
      </template>
    </HomeTitle>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: dataList }">
      <draw-echarts
        v-if="dataList.length != 0"
        ref="drawEcharts"
        :echartOption="echartOption"
        :echart-style="ringStyle"
        class="charts"
        @echartLegendselectchanged="legendselectchanged"
        @echartClick="echartClick"
      ></draw-echarts>
      <span class="next-echart" v-if="dataList.length > sizeNumber">
        <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('drawEcharts', dataList, [], sizeNumber)"></i>
      </span>
    </div>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';

export default {
  name: 'plant-assets',
  mixins: [dataZoom],
  props: {
    batchIds: {
      type: Array,
      default: () => [],
    },
    indexModules: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
      },
      sizeNumber: 13,
      echartOption: {},
      name: '全部指标',
      dataList: [],
      dataColor: ['#4193FC', '#FAE565', '#CABDDD', '#0DD083', '#84EA9E', '#F59835'],
      dataLenged: {}, // 记录图例是否处于选中状态
      dataObj: [], // 已处理数据格式
      nameArr: [], // x轴
      echartsLoading: false,
      timer: false,
      indexModuleType: 'basic',
      allLengedData: [],
      chartData: [],
    };
  },
  mounted() {
    if (window.innerWidth < 1850 && window.innerWidth > 1460) {
      this.sizeNumber = 10;
    } else if (window.innerWidth < 1460) {
      this.sizeNumber = 8;
    } else if (window.innerWidth > 1850) {
      this.sizeNumber = 13;
    }
    window.addEventListener('resize', this.resize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize);
  },
  methods: {
    echartClick(params) {
      let evaluationResultIndexList = this.dataList[params.dataIndex].evaluationResultIndexList;
      let one = evaluationResultIndexList.find((item) => item.jobStrategy === params.data.key);
      this.$emit('makeCreateTabs', one);
    },
    // 监听屏宽变化
    resize() {
      if (window.innerWidth < 1850 && window.innerWidth > 1460) {
        this.sizeNumber = 10;
      } else if (window.innerWidth < 1460) {
        this.sizeNumber = 8;
      } else if (window.innerWidth > 1850) {
        this.sizeNumber = 13;
      }
      if (!this.timer) {
        this.timer = true;
        setTimeout(() => {
          if (this.name !== '全部指标') {
            this.typeList.forEach((value) => {
              this.$refs.dispatchAction({
                type: this.name === value.name ? 'legendSelect' : 'legendUnSelect',
                name: this.name,
              });
            });
          } else {
            this.dataLenged = {};
            this.$refs.drawEcharts.dispatchAction({
              type: 'legendAllSelect',
            });
          }
          this.timer = false;
        }, 400);
      }
    },
    async queryDeviceCheckColumnReports() {
      try {
        this.echartsLoading = true;
        const params = {
          regionCode: this.getHomeConfig.regionCode,
          batchIds: this.batchIds,
          indexModule: this.indexModules[this.indexModuleType].value,
        };
        const {
          data: { data },
        } = await this.$http.post(home.queryHomePageResultIndex, params);
        this.dataList = [];
        if (!!data && data.length) {
          let filterData = data.filter(
            (item) => !!item.evaluationResultIndexList && item.evaluationResultIndexList.length,
          );
          if (filterData.length) {
            this.dataList = filterData.slice(0, 13);
            this.handleAll();
          }
        }
        this.echartsLoading = false;
      } catch (e) {
        this.echartsLoading = false;
        console.log(e);
      }
    },
    getName(val) {
      this.name = val;
      if (val === '全部指标') {
        this.$refs.drawEcharts.dispatchAction({
          type: 'legendAllSelect',
        });
        return;
      }
      this.typeList.forEach((value) => {
        this.$refs.drawEcharts.dispatchAction({
          type: val === value.name ? 'legendSelect' : 'legendUnSelect',
          name: value.name,
        });
      });
    },
    // 图例取消选中重新加载分割
    legendselectchanged(params) {
      for (let key in params.selected) {
        this.dataLenged[key] = params.selected[key];
      }
    },
    fontSize(res) {
      const clientWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      if (!clientWidth) return;
      const fontSize = clientWidth / 1920;
      return res * fontSize;
    },
    handleAll() {
      // 图例指标id
      let lengedIndexIds = [];
      let chartData = [];
      let spiltArr = []; // 分隔数据
      let evaluationResultIndexList = this.dataList.map((item) => {
        this.nameArr.push(item.regionName); // 纵坐标数据
        return item.evaluationResultIndexList;
      });
      // 二维数组转一维数组
      evaluationResultIndexList = evaluationResultIndexList.flat();
      evaluationResultIndexList.forEach((item) => {
        // lengedIndexIds 是否存在当前指标 indexId， 不存在则添加
        if (!lengedIndexIds.includes(item.indexId)) {
          let obj = {
            name: item.indexName,
            key: item.jobStrategy,
          };
          obj[item.jobStrategy] = [Math.abs(item.resultValue)];
          lengedIndexIds.push(item.indexId);
          chartData.push(obj);
        } else {
          // 存在则往里添加resultValue值
          let chartDataItem = chartData.find((indexItem) => indexItem.key === item.jobStrategy);
          chartDataItem[item.jobStrategy].push(Math.abs(item.resultValue)); // 将null值转换为0
        }
      });
      // 间隔数据处理
      chartData[0][chartData[0].key].forEach(() => {
        spiltArr.push(0);
      });
      this.allLengedData = chartData.map((item) => item.name);
      this.chartData = chartData;
      this.echartOption = this.handeOption(chartData);
      setTimeout(() => {
        this.setDataZoom('drawEcharts', [], this.sizeNumber);
      });
    },
    handeOption(chartData) {
      const tooltip = {
        show: 'true',
        trigger: 'axis', //触发类型
        axisPointer: {
          type: 'none', //去掉移动的指示线
        },
        confine: true,
        padding: [8, 10], //内边距
        extraCssText: 'background: rgba(13, 53, 96, 0.7);border: 1px solid #1684E4;opacity: 1;', //添加阴影
        formatter: function (params) {
          let result = `<div>${params[0].name}</div>`;
          params.forEach(function (item) {
            // 去除特殊样式series
            if (item.componentSubType !== 'pictorialBar' && item.seriesName !== '背景') {
              const dotHtml = `<span style="display:inline-block;margin-right:5px;width:10px;height:10px;background: linear-gradient(${item.color} 0%, ${item.color} 100%);"></span>`;
              const seriesName = `<span style="display:inline-block; margin-left:10px;">${item.seriesName}</span>`;
              const number = `<span style="display:inline-block;float: right;margin-left:10px;">${Math.abs(
                item.value,
              )}%</span>`;
              result += dotHtml + seriesName + number + '</br>';
            }
          });
          return result;
        },
      };
      const legend = {
        show: true,
        itemWidth: 10,
        itemHeight: 10,
        align: 'left',
        top: 10,
        left: 10,
        formatter: function (name) {
          return '{a|' + (name.length > 8 ? name.substr(0, 8) + '...' : name) + '}';
        },
        textStyle: {
          rich: {
            a: {
              width: 120,
              fontSize: this.fontSize(12),
              align: 'left',
              color: '#fff',
            },
          },
        },
        selected: this.dataLenged,
        tooltip: {
          show: true,
        },
      };
      const service = [
        // {
        //   // 分隔
        //   type: 'pictorialBar',
        //   itemStyle: {
        //     normal: {
        //       color: '#0F375F',
        //     },
        //   },
        //   symbolRepeat: 'fixed',
        //   symbolMargin: 2,
        //   symbol: 'rect',
        //   symbolClip: true,
        //   symbolSize: [18, 2],
        //   symbolPosition: 'start',
        //   symbolOffset: [1, 1],
        //   data: spiltArr,
        //   width: 2,
        //   z: 0,
        //   zlevel: 1,
        // },
      ];
      chartData.forEach((item, chartIndex) => {
        service.push({
          name: item.name,
          type: 'bar',
          barWidth: this.$util.common.fontSize(14),
          stack: '数量',
          data: item[item.key].map((one) => {
            return {
              value: one,
              key: item.key,
            };
          }),
          itemStyle: {
            color: this.dataColor[chartIndex],
          },
          z: 10,
          zlevel: 0,
        });
      });
      const option = {
        grid: {
          top: '22%',
          right: '3%',
          left: '-2%',
          bottom: '4%',
          containLabel: true,
        },
        tooltip: tooltip,
        legend: legend,
        xAxis: [
          {
            data: this.nameArr,
            axisLine: {
              lineStyle: {
                color: '#0375B4',
              },
            },
            axisTick: {
              show: false,
            },

            axisLabel: {
              margin: 10,
              color: '#e2e9ff',
              fontSize: this.fontSize(12),
              formatter: function (params) {
                let newName = '';
                if (params.length > 3) {
                  newName = params.substring(0, 3) + '...';
                } else {
                  newName = params;
                }
                return newName;
              },
              rotate: 30,
            },
          },
        ],
        yAxis: { show: false },
        series: service,
      };
      return option;
    },
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      comprehensiveConfig: 'common/getComprehensiveConfig',
      getHomeConfig: 'home/getHomeConfig',
    }),
    typeList() {
      let arr = [{ name: '全部指标' }];
      this.allLengedData.forEach((item) => {
        arr.push({ name: item });
      });
      return arr;
    },
  },
  watch: {
    batchIds: {
      handler(val) {
        if (!val.length || !this.getHomeConfig || !this.getHomeConfig.regionCode) return false;
        this.$nextTick(() => {
          this.queryDeviceCheckColumnReports();
        });
      },
      immediate: true,
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    HomeTitle: require('./home-title').default,
    TypeSelect: require('./type-select').default,
  },
};
</script>
<style lang="less" scoped>
.plant-assets {
  position: absolute;
  // bottom: 10px;
  width: 462px;
  background: rgba(0, 104, 183, 0.13);
  height: 48%;
  z-index: 11;

  .determinant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }

  .echarts-box {
    width: 100%;
    height: calc(100% - 32px) !important;

    .charts {
      width: 100%;
      height: 100% !important;
    }
  }

  .next-echart {
    top: 50%;
    right: 0;
    position: absolute;

    .icon-zuojiantou1 {
      color: rgba(45, 190, 255, 1);
      font-size: 12px;
      vertical-align: top !important;
    }

    &:active {
      .icon-zuojiantou1 {
        color: #4e9ef2;
        font-size: 12px;
        vertical-align: top !important;
      }
    }

    &:hover {
      .icon-zuojiantou1 {
        color: var(--color-primary);
        font-size: 12px;
        vertical-align: top !important;
      }
    }
  }
}

.full-screen-container {
  position: absolute;
  height: 45%;
  margin-left: 10px;
}
</style>
