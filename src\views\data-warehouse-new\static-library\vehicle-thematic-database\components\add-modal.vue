<template>
  <ui-modal
    v-model="visible"
    :title="!isEdit ? '新增' : '编辑'"
    :r-width="1010"
    @onOk="comfirmHandle"
  >
    <div ref="vehicleThematic" class="personnel-thematic-database">
      <Form
        ref="vehicleForm"
        :model="vehicleForm"
        :rules="ruleInline"
        inline
        class="personnel-form"
      >
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem prop="photoUrlList" class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <img
                  v-if="!previewImg"
                  src="@/assets/img/empty-page/null_img_icon.png"
                  class="avatar-null-img"
                  alt=""
                />
                <template v-else>
                  <!-- 适配当编辑时，删掉图片，再上传，此时previewImg存的是个字符串，只有地址 -->
                  <img
                    v-if="isEdit"
                    v-viewer
                    :src="previewImg.photoUrl || previewImg"
                    alt
                  />
                  <img v-else v-viewer :src="previewImg" alt />
                </template>
              </div>
              <UploadImg
                choosed
                ref="uploadImg"
                :isEdit="isEdit"
                :deleted="true"
                :multipleNum="10"
                :choosedIndex="avatarIndex"
                :defaultList="vehicleForm.photoUrlList"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="plateNo" label="车牌号码:">
                <Input
                  v-model="vehicleForm.plateNo"
                  placeholder="请输入车牌号码"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="natureOfUse" label="使用性质:">
                <!-- <Select v-model="vehicleForm.natureOfUse" placeholder="请选择使用性质" class="input-200"></Select> -->
                <Select
                  v-model="vehicleForm.natureOfUse"
                  placeholder="请选择使用性质"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in vehicleUseNature"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="plateColor" label="车牌颜色:">
                <Select
                  v-model="vehicleForm.plateColor"
                  placeholder="请选择车牌颜色"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in licensePlateColorList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="identificationCode" label="识别代码:">
                <Input
                  v-model="vehicleForm.identificationCode"
                  placeholder="请输入识别代码"
                  class="input-200"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleColor" label="车身颜色:">
                <Select
                  v-model="vehicleForm.vehicleColor"
                  placeholder="请选择车身颜色"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in plateColorIpbdList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="engineCode" label="发动机号:">
                <Input
                  v-model="vehicleForm.engineCode"
                  placeholder="请输入发动机号"
                  class="input-200"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleType" label="车辆类型:">
                <Select
                  v-model="vehicleForm.vehicleType"
                  filterable
                  placeholder="请选择车辆类型"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in vehicleClassTypeList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="registerDate" label="注册日期:">
                <DatePicker
                  @on-change="getRegisterDate"
                  :value="vehicleForm.registerDate"
                  format="yyyy-MM-dd"
                  type="date"
                  placeholder="请选择注册日期"
                  class="input-200"
                ></DatePicker>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="issueDate" label="发证日期:">
                <DatePicker
                  @on-change="getIssueDate"
                  :value="vehicleForm.issueDate"
                  format="yyyy-MM-dd"
                  type="date"
                  placeholder="请选择发证日期"
                  class="input-200"
                ></DatePicker>
              </FormItem>
              <FormItem prop="registerAddress" label="注册地址:">
                <Input
                  v-model="vehicleForm.registerAddress"
                  placeholder="请输入注册地址"
                  class="input-200"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="vehicleBrand" label="车辆品牌:">
                <Select
                  v-model="vehicleForm.vehicleBrand"
                  filterable
                  placeholder="请选择车辆品牌"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in vehicleBrandList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="useStatus" label="使用状态:">
                <!-- <Select v-model="vehicleForm.useStatus" placeholder="请选择使用状态" class="input-200">
                </Select> -->
                <Select
                  v-model="vehicleForm.useStatus"
                  placeholder="请选择使用状态"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in vehicleUseStatus"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
            </div>
          </div>
        </div>
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">车主信息</div>
        </div>
        <div class="information-form other-form">
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="name" label="车主姓名:" class="rfid-number">
                <Input
                  v-model="vehicleForm.name"
                  placeholder="请输入车主姓名"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="idCardNo" label="身份证号:" class="mac-number">
                <Input
                  v-model="vehicleForm.idCardNo"
                  placeholder="请输入身份证号"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="sex" label="性别:">
                <RadioGroup v-model="vehicleForm.sex" class="input-200">
                  <Radio label="1">男</Radio>
                  <Radio label="2">女</Radio>
                </RadioGroup>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem
                prop="drivingLicenseNo"
                label="驾照号码:"
                class="rfid-number"
              >
                <Input
                  v-model="vehicleForm.drivingLicenseNo"
                  placeholder="请输入驾照号码"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="phoneNo" label="电话号码:" class="mac-number">
                <Input
                  v-model="vehicleForm.phoneNo"
                  placeholder="请输入电话号码"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="national" label="民族:">
                <!-- <Select v-model="vehicleForm.national" transfer placeholder="请选择民族" class="input-200">
                </Select> -->
                <Select
                  v-model="vehicleForm.national"
                  transfer
                  placeholder="请选择民族"
                  class="input-200"
                >
                  <Option
                    v-for="(item, $index) in nationList"
                    :key="$index"
                    :value="item.dataKey"
                    >{{ item.dataValue }}</Option
                  >
                </Select>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="address" label="家庭地址:" class="rfid-number">
                <Input
                  v-model="vehicleForm.address"
                  placeholder="请输入家庭地址"
                  class="input-510"
                />
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
    </div>
  </ui-modal>
</template>
<script>
import { addVehicleLibInfo, motifyVehicleLibInfo } from "@/api/data-warehouse";
import { mapActions, mapGetters } from "vuex";
import UploadImg from "@/components/ui-upload-img";
export default {
  components: {
    UploadImg,
  },
  props: {
    // 当前库对象
    currentRow: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      visible: false,
      avatarIndex: "",
      isEdit: false, //false-新增, true-编辑
      vehicleForm: {
        photoUrlList: [],
        plateNo: "",
        natureOfUse: "",
        plateColor: "",
        identificationCode: "",
        vehicleColor: "",
        engineCode: "",
        vehicleType: "",
        registerDate: "",
        issueDate: "",
        registerAddress: "",
        vehicleBrand: "",
        useStatus: "",
        name: "",
        idCardNo: "",
        sex: "1",
        drivingLicenseNo: "",
        phoneNo: "",
        national: "",
        address: "",
      },
      previewImg: "",
      ruleInline: {
        plateNo: [
          { required: true, message: "请输入车牌号码", trigger: "blur" },
        ],
        plateColor: [
          { required: true, message: "请选择车牌颜色", trigger: "change" },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      plateColorIpbdList: "dictionary/getVehicleColorList", // 车身颜色
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      vehicleUseStatus: "dictionary/getVehicleUseStatus", //车辆使用状态
      vehicleUseNature: "dictionary/getVehicleUseNature", //车辆使用性质
      nationList: "dictionary/getNationList", //车辆使用性质
    }),
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    show(bool, item) {
      this.isEdit = bool;
      this.visible = true;
      this.previewImg = "";
      this.avatarIndex = "";

      this.$nextTick(() => {
        this.$refs.vehicleThematic.scrollTop = 0;
        this.$refs.vehicleForm.resetFields();
        if (this.isEdit) {
          // 编辑
          this.avatarIndex = 0;
          this.vehicleForm = JSON.parse(JSON.stringify(item));
          this.previewImg = item.photoUrlList[0];
          this.$forceUpdate();
        } else {
          this.vehicleForm.registerDate = "";
          this.vehicleForm.issueDate = "";
        }
      });
    },
    // 选择汽车照
    chooseHandle(item) {
      this.previewImg = item;
    },
    comfirmHandle() {
      this.$refs.vehicleForm.validate((valid) => {
        if (valid) {
          this.vehicleForm.featureLibId = this.currentRow.featureLibId;
          // 新增
          if (!this.isEdit) {
            addVehicleLibInfo(this.vehicleForm).then((res) => {
              this.visible = false;
              this.$Message.success("新增成功");
              this.$parent.init();
            });
          } else {
            //编辑
            this.vehicleForm.photoUrlList = [
              ...this.vehicleForm.photoUrlList,
              ...this.$refs.uploadImg.delImageList,
            ];
            motifyVehicleLibInfo(this.vehicleForm).then((res) => {
              this.visible = false;
              this.$Message.success("编辑成功");
              this.$parent.init();
            });
          }
        }
      });
    },
    getRegisterDate(time) {
      this.vehicleForm.registerDate = time;
    },
    getIssueDate(time) {
      this.vehicleForm.issueDate = time;
    },
  },
};
</script>
<style lang="less" scoped>
.input-200 {
  width: 200px;
}
.input-510 {
  width: 510px;
}
/deep/ .ivu-modal-body {
  padding: 0 !important;
}
.personnel-thematic-database {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  height: 620px;
  .personnel-form {
    padding: 0 30px;
    box-sizing: border-box;
    .form-item-title {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      border-bottom: 1px solid #fff;
      .title-line {
        width: 3px;
        height: 16px;
        margin-right: 6px;
      }
      .title-text {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
        font-family: "MicrosoftYaHei-Bold";
      }
    }
    .information-form {
      display: flex;
      justify-content: space-between;
      margin: 20px 0 30px 0;
      .essential-information-img {
        width: 240px;
        margin-right: 64px;
        display: flex;
        flex-direction: column;
        .avatar-img {
          width: 240px;
          height: 240px;
          border: 1px solid #fff;
          & > img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: pointer;
          }
          .avatar-null-img {
            cursor: unset;
          }
        }
        .upload-img {
          margin-top: 10px;
          justify-content: flex-start;
          /deep/ .upload-item {
            width: 54px;
            height: 54px;
            .ivu-icon-ios-add {
              font-size: 30px;
              font-weight: bold;
            }
            .upload-text {
              line-height: 18px;
              display: none;
            }
          }
        }
      }
      .information-body {
        flex: 1;
        .info-item {
          display: flex;
          justify-content: space-between;
          /deep/ .ivu-form-item {
            display: inline-flex;
            margin-right: 0;
            margin-bottom: 10px;
            .ivu-form-item-label {
              display: flex;
              align-items: center;
              justify-content: end;
              padding-right: 10px;
              white-space: nowrap;
            }
            .ivu-form-item-label::before {
              margin: 0;
            }
            .ivu-radio-wrapper {
              margin-right: 30px;
            }
          }
        }
      }
    }
    .essential-form {
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 80px !important;
        }
      }
      .img-formitem {
        margin: 0 !important;
      }
    }
    .other-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
          .label-text {
            width: 58px;
          }
        }
      }
      .info-item {
        justify-content: unset !important;
        .rfid-number {
          margin-right: 36px !important;
        }
        .mac-number {
          margin-right: 46px !important;
        }
      }
    }
  }
}
/deep/ .ivu-form-item-error-tip {
  z-index: 9;
}
</style>