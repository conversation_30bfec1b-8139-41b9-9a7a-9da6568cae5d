<template>
  <div class="index-detail">
    <ui-modal v-model="visible" :title="title" :width="95" footerHide>
      <Button type="primary" class="fr mb-sm mr-sm" @click="getExport" :loading="exportLoading">
        <i class="icon-font icon-daochu f-12 mr-sm vt-middle" title="添加"></i>
        <span class="vt-middle">导出</span>
      </Button>
      <div class="detail-content auto-fill clear-b" v-ui-loading="{ loading: loading, tableData: tableData }">
        <div class="table-box auto-fill">
          <!-- 统计详情 -->
          <ui-table
            class="ui-table table-content"
            ref="tableContent"
            :table-columns="tableColumns"
            :table-data="tableData"
            noFill
          >
            <template #EVALUATION_TIME="{ row }">
              <span v-if="systemConfig.distinguishVersion !== '4'">
                {{ row.EVALUATION_TIME }}
              </span>
              <Button type="text" v-else class="span-btn">
                {{ row.EVALUATION_TIME }}
              </Button>
            </template>
          </ui-table>
          <!-- 总计模块 -->
          <ui-table
            class="ui-table table-total auto-fill"
            ref="tableTotal"
            :show-header="false"
            :table-columns="totalTableColumns"
            :table-data="totalTableData"
            :span-method="handleSpan"
          >
          </ui-table>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
// import moke_detail from '../moke_detail.json';
import governanceevaluation from '@/config/api/governanceevaluation';
import CreateTabs from '@/components/create-tabs/create-tabs.vue';
import { mapGetters } from 'vuex';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  props: {
    taskObj: {
      type: [Object, String],
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      row: {},
      taskDetail: {},
      tableColumns: [
        {
          title: '检测时间',
          key: 'EVALUATION_TIME',
          slot: 'EVALUATION_TIME',
        },
      ],
      tableData: [],
      table_row: {}, //点击获取的数据
      examTitle: '',
      totalTableColumns: [],
      totalTableData: [],
      countColumnObj: {}, //合并总得分第二行所需
      exportLoading: false,
      listSearchData: {},
    };
  },
  watch: {
    taskObj: {
      handler(val) {
        if (val) {
          this.taskDetail = val;
          this.tableColumns = [
            {
              title: '检测时间',
              key: 'EVALUATION_TIME',
              slot: 'EVALUATION_TIME',
            },
          ];
          this.tableData = [];
        }
      },
      immediate: true,
    },
  },
  mounted() {
    // 监听下面总计表格滚动 上面表格和下面滚动保持一致
    this.$refs.tableTotal.$refs.table.$refs.body.addEventListener('scroll', this.handScrollLeft);
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let data = {
        examSchemeId: this.listSearchData.examSchemeId + '',
        examTaskId: this.listSearchData.examTaskId + '',
        month: this.listSearchData.month + '',
        orgRegionFlag: '2',
        year: this.listSearchData.year + '',
        orgRegeionCode: this.table_row.row.orgCode + '',
        category: this.table_row.column.category || '',
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(governanceevaluation.queryExamContentItemIndexResultListExport, data);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    financial(string, fractionDigits) {
      return Number.parseFloat(Number.parseFloat(string).toFixed(fractionDigits));
    },
    init(params, searchData) {
      this.listSearchData = searchData;
      this.visible = true;
      this.getDetail(params, searchData);
    },
    getDetail(params, searchData) {
      this.loading = true;
      let data = {
        examSchemeId: '' + searchData.examSchemeId,
        examTaskId: '' + searchData.examTaskId,
        month: '' + searchData.month,
        orgRegionFlag: '2', //这里按组织机构查询
        year: '' + searchData.year,
        orgRegeionCode: '' + params.row.orgCode,
      };
      params.column.category ? (data['category'] = params.column.category) : null;
      this.table_row = params;
      this.examTitle = this.table_row.column.name.replace('考核得分', '');
      this.title = `${this.table_row.row.ORG_REGEION_CODE} - ${this.examTitle}考核明细`;
      this.$http
        .post(governanceevaluation.queryExamContentItemIndexResultList, data)
        .then((res) => {
          let { headers, body } = res.data.data;
          // let { headers, body } = moke_detail

          this.tableColumns = this.$util.common.deepCopy(headers);
          /**
           * 处理表头
           * 把所有考核项增加得分
           */
          for (let i of this.tableColumns) {
            if (i.name !== '考核内容' && i.code !== 'EVALUATION_TIME') {
              i.name = `${i.name} (${i.score}分)`;
            }
            for (let k of i.children) {
              if (!k.name.includes('得分') && k.name !== '考核项') {
                if (k.code.includes('EX')) {
                  k.name = `${k.name} (${k.excessMaxScoreValue}分)`;
                } else {
                  k.name = `${k.name} (${k.score}分)`;
                }
              }
            }
          }
          this.handleTableHeaders(this.tableColumns);
          // 处理表内容
          this.handleTableBody(body);
          // 处理总计
          this.handleSummary(headers);
        })
        .catch((err) => {
          console.log(err);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleTableHeaders(arr) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.align = 'center';
        v.minWidth = 250;
        // 左侧一列浮动在左侧
        if (v.title === '考核内容' || v.title === '考核项' || v.title === '检测时间') {
          v.fixed = 'left';
          v.width = 250;
        }
        /**
         * 1.非得分非时间 直接展示
         * 2.时间那一列则更改为三角展示左下右上
         */
        if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') {
          v.renderHeader = this.renderTableHeader;
        } else if (v.code === 'EVALUATION_TIME') {
          v.slot = 'EVALUATION_TIME';
          v.renderHeader = this.randerSlash;
          v.className = 'triangle';
        }
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr) {
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        /**
         * 循环每一项插入列表中
         * 判断是时间，得分，指标，百分比
         * */

        v.forEach((k) => {
          if (k.code === 'EVALUATION_TIME') {
            tableData[i].EVALUATION_TIME = k.evaluationTimeStr;
          } else if (typeof k.evaluationResultValue === 'number') {
            tableData[i][k.code] = {
              score: k.evaluationResultValue + '%',
              // 四川省厅需求增加字段跳转页
              linkUrl: k.linkUrl || null,
              evaluationStatisticsId: k.evaluationStatisticsId || null,
            };
          } else {
            tableData[i][k.code] = {
              score: k.score,
            };
          }
        });
      });
      this.tableData = tableData;
    },
    /**
     * 总计列表合成
     * 共两行
     * 表头由 考核内容，考核分项表头组成 隐藏表头只显示表中的内容
     * 内容第一列 为总分 合并第一列
     * 内容第二行合并为总具体得分 合并第二列第二行所有的行
     */
    handleSummary(columns) {
      this.totalTableData = [];
      this.totalTableColumns = [];
      this.countColumnObj = {};
      /**
       * 第一行内容
       * 第一列为固定总分
       * 后面列为各个考核项平均得分
       * 第二行内容
       * 第一列为固定总分
       * 后面列为各个考核内容总分
       */
      let scores = {};
      let assessmentScores = {};
      columns.forEach((row) => {
        row.children.forEach((rw) => {
          if (rw.name === '考核项') {
            this.totalTableColumns.push({
              title: rw.name,
              key: 'examinationContent',
              width: 250,
              align: 'center',
              fixed: 'left',
              parentCode: rw.parentCode,
            });
            scores.examinationContent = '总得分';
            assessmentScores.examinationContent = '总得分';
          } else {
            this.totalTableColumns.push({
              title: rw.name,
              key: rw.code,
              minWidth: rw.children ? 250 * rw.children.length : 250,
              align: 'center',
              className: 'total',
              parentCode: rw.parentCode,
            });
            scores[rw.code] = '';
            if (rw.parentCode === row.code) {
              assessmentScores[rw.code] = row.finalName
                ? `${row.name}${row.finalName}`
                : `${row.name}最终得分：（--分）`;
              /**
               * 第二行内容横向合并需要计算合并的数量
               * 当指标的parentCode相同时就合并在一起
               */
              if (this.countColumnObj.hasOwnProperty(rw.parentCode)) {
                this.countColumnObj[rw.parentCode]++;
              } else {
                this.countColumnObj[rw.parentCode] = 0;
                this.countColumnObj[rw.parentCode]++;
              }
            }
          }
        });
      });
      /**
       * 下面用来计算总计行中的第一行，每个考核项的平均得分
       * 由于后端难以计算 所以此处由前端计算 后端为lf
       */
      let codeFrequency = {}; // 用来记录考核项有多少次得分
      this.tableData.forEach((row) => {
        Object.keys(scores).forEach((key) => {
          // 如果没有此考核项则添加此考核项为0次
          if (!codeFrequency.hasOwnProperty(key)) {
            codeFrequency[key] = 0;
          }
          /**
           *  如果返回的数据中有此code则把所有code相同的得分相加得到此code的总分
           */
          if (row.hasOwnProperty(key)) {
            scores[key] = scores[key] - 0 + Number(row[key].score);
            codeFrequency[key]++;
          } else if (row.hasOwnProperty(key + '_ITEM_SCORE')) {
            scores[key] = scores[key] - 0 + Number(row[key + '_ITEM_SCORE'].score);
            codeFrequency[key]++;
          }
        });
      });
      /**
       * 如果考核总分为空则标记为没有考核为--
       * 如果考核次数不为0 则计算平均考核得分
       */
      Object.keys(scores).forEach((key) => {
        if (scores[key] === '') {
          scores[key] = '--';
        } else {
          if (codeFrequency[key] !== 0) {
            scores[key] = this.financial(scores[key] / codeFrequency[key], 5);
          }
        }
      });
      this.totalTableData.push(scores);
      this.totalTableData.push(assessmentScores);
      /**
       * 第三行内容
       * 第一列为固定总分
       * 后面列合并为具体总得分
       * key为上面内容表格中的列的下标为1的key 因为下面总计表格中的表头是由上面统计内容的表头
       * 而且要合并第二列之后的所有列 所以要在列表中第二列赋值
       */
      this.totalTableData.push({
        examinationContent: '总得分',
        cellClassName: {
          [this.totalTableColumns[1].key]: 'total-score',
        },
        [this.totalTableColumns[1].key]: `${this.examTitle}考核最终得分：${
          this.table_row.row[this.table_row.column.code].score
        }`,
      });
    },
    evaluationDetailBtn(item) {
      let obj = item.row[item.column.key];
      this.$emit('selectModuleClick', obj);
    },
    handleSpan({ column, rowIndex, columnIndex }) {
      // 合并第一列中的总分
      if (rowIndex === 0 && columnIndex === 0) {
        return [3, 1];
      } else if (rowIndex !== 0 && columnIndex === 0) {
        return [0, 0];
      }
      // 合并第二行中的第二列之后的行
      // 直接往后合并相同parentCode的列
      if (rowIndex === 1 && columnIndex === 1) {
        return [1, Object.values(this.countColumnObj)[0]];
      }
      // 当发现后面的列和当前列相同时切掉此列，当不同时向后合并相同parentCode的列数
      if (rowIndex === 1 && columnIndex > 1) {
        if (column.parentCode === this.totalTableColumns[columnIndex - 1]['parentCode']) {
          return [0, 0];
        } else if (column.parentCode !== this.totalTableColumns[columnIndex - 1]['parentCode']) {
          return [1, this.countColumnObj[column.parentCode]];
        }
      }

      // 合并第三行第二列之后的所有行
      if (rowIndex === 2 && columnIndex === 1) {
        return [1, this.totalTableColumns.length - 1];
      } else if (rowIndex === 2 && columnIndex > 1) {
        return [0, 0];
      }
    },
    handScrollLeft() {
      this.$refs.tableContent.$refs.table.$refs.body.scrollLeft =
        this.$refs.tableTotal.$refs.table.$refs.body.scrollLeft;
    },
    renderTableTd(h, params) {
      if (!params.row[params.column.key] && params.row[params.column.key] !== 0) {
        return h('span', {}, '--');
      } else {
        return h(
          'span',
          {
            style: {
              textDecoration:
                params.row[params.column.key].linkUrl || params.row[params.column.key].evaluationStatisticsId
                  ? 'underline'
                  : 'none',
              color:
                params.row[params.column.key].linkUrl || params.row[params.column.key].evaluationStatisticsId
                  ? '#2B84E2'
                  : '#fff',
            },
            on: {
              click: () => {
                params.row[params.column.key].linkUrl && this.jumpToThird(params.row[params.column.key]);
                params.row[params.column.key].evaluationStatisticsId && this.$emit('showHkDetail', params);
              },
            },
          },
          params.row[params.column.key].score,
        );
        // 如果是得分则不增加跳转 如果是指标则跳转详情
        if (params.column.code.includes('SCORECODE')) {
          return h('span', {}, params.row[params.column.key].score);
        } else {
          return h(
            CreateTabs,
            {
              props: {
                componentName: 'detectionToOverview',
                tabsText: '评测结果', // 跳转页面标题
                tabsQuery: {
                  indexId: params.row[params.column.key].indexId,
                  code: params.row[params.column.key].orgRegeionCode,
                  access: 'TASK_RESULT',
                  batchId: params.row[params.column.key].evaluationBatchId,
                  taskIndexId: params.row[params.column.key].evaluationTaskIndexId,
                  startTime: params.row[params.column.key].evaluationTime,
                },
              },
              style: {
                color: 'var(--color-primary)',
                cursor: 'pointer',
                textDecoration: 'underline',
              },
              on: {
                selectModule: () => {
                  this.evaluationDetailBtn(params);
                },
              },
            },
            params.row[params.column.key].score,
          );
        }
      }
    },
    randerSlash(h) {
      return h(
        'div',
        {
          attrs: {
            class: 'type',
          },
        },
        [
          h(
            'strong',
            {
              attrs: {
                class: 'detection-time',
              },
            },
            '检测时间',
          ),
          h(
            'strong',
            {
              attrs: {
                class: 'detection-indicator',
              },
            },
            '检测指标',
          ),
        ],
      );
    },
    renderTableHeader(h, params) {
      return h('div', [h('div', {}, `${params.column.title}`)]);
    },
    jumpToThird(row) {
      window.open(row.linkUrl, '_blank');
    },
  },
  computed: {
    ...mapGetters({
      systemConfig: 'common/getSystemConfig',
    }),
  },
  beforeDestroy() {
    this.$refs.tableTotal.$refs.table.$refs.body.removeEventListener('scroll', this.handScrollLeft);
  },
};
</script>
<style lang="less" scoped>
.index-detail {
  color: #fff;
  @{_deep} .ivu-modal {
    height: 93%;
    > .ivu-modal-content {
      height: 100%;
      > .ivu-modal-body {
        height: 100%;
      }
    }
  }
  .detail-content {
    height: 91%;
    .table-content {
      .span-btn {
        @{_deep}span {
          text-decoration: underline;
        }
      }
      @{_deep}.ivu-table-wrapper {
        height: 590px !important;
      }
      @{_deep} .ivu-table {
        .ivu-table-body {
          overflow-x: hidden !important;
          border-right: 0;
        }
      }
      @{_deep}.ivu-table-overflowX {
        & ~ .ivu-table-fixed {
          height: 100%;
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
            height: calc(~'100% - 150px - 10px') !important;
          }
        }
      }

      @{_deep}.ivu-table-fixed {
        height: 100%;
        > .ivu-table-fixed-body {
          // 浮动高度应该为表格高度 - 浮动表头高度
          height: calc(~'100% - 150px - 10px') !important;
        }
      }
    }
    .table-total {
      @{_deep}.ivu-table-overflowX {
        & ~ .ivu-table-fixed {
          height: calc(~'100% - 11px');
          > .ivu-table-fixed-body {
            // 浮动高度应该为表格高度 - 浮动表头高度 - 滑块高度
            height: 100% !important;
          }
        }
      }

      @{_deep}.ivu-table-fixed {
        height: 100%;
        > .ivu-table-fixed-body {
          // 浮动高度应该为表格高度 - 浮动表头高度
          height: 100% !important;
        }
      }
    }
    // min-height: 260px;
    .ui-table {
      padding: 0 10px;
      @{_deep}.total {
        font-size: 22px;
      }
      @{_deep}.total-score {
        color: var(--color-bluish-green-text);
      }
      @{_deep}.ivu-table {
        &:before {
          width: 0;
        }
        .ivu-table-header {
          tr {
            th {
              border-right: none !important;
            }
          }
        }
      }
      @{_deep} .ivu-table {
        th,
        td {
          border: 1px solid var(--border-color) !important;
        }
        &:before {
          content: '';
          position: absolute;
          background-color: #0d477d !important;
        }
        .ivu-table-summary {
          td {
            background: #062042;
          }
        }
      }
    }
    @{_deep} .ivu-table-fixed-header {
      .triangle {
        padding: 0;
        .ivu-table-cell {
          padding: 0;
          width: 100%;
          height: 100%;
        }
        .type {
          position: relative;
          background-color: #0d477d;
          width: 100%;
          height: 100%;
          z-index: 0;
          &:after {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            clip-path: polygon(100% calc(100% - 0.5px), 100% 0px, 0px -0.5px);
            position: absolute;
            top: 0;
            background-color: #092955;
          }
          &:before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            clip-path: polygon(0px 0.5px, 0px 100%, calc(100% - 0.5px) calc(100% + 0.5px));
            position: absolute;
            top: 0;
            background-color: #092955;
          }
          .detection-time {
            position: absolute;
            left: 6px;
            bottom: 5px;
            z-index: 1;
          }
          .detection-indicator {
            position: absolute;
            right: 6px;
            top: 5px;
            z-index: 1;
          }
        }
      }
    }
  }
}
</style>
