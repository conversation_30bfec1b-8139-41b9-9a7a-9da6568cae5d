<template>
	<!-- <div class="container">碰撞分析</div> -->
    <div class="time-space-colli">
        <mapCustom ref="mapBase"
            :sectionName='mapType.sectionName'
            :allCameraList="allCameraList"
            @selectData="selectData"
            @chooseMapItem="chooseMapItem"
            :collisionPoints='collisionPoints'
            :mapType="mapType.seleType"
            :crashType="mapType.seleType"
            :currentClickIndex.sync="currentClickIndex"
            cutIcon="map"
        />
         <!-- 左侧搜索 -->
         <leftBox ref="leftBox" 
            @search="handleSearch"
            @areaClick="areaClick"
            @selectDraw="selectDraw"
            @checkChange="checkChange"
            @deleDraw="deleDraw"
            :typeName="mapType.mapName"></leftBox>
        <!-- 右侧列表 -->
        <!-- <rightBox 
            ref="rightBox" 
            v-show="rightBoxShow" 
            :tablist="tablists"
            @spaceTime="handleSpaceTime" 
            @cancel="handleCancel"></rightBox> -->
        <!-- 对象详情 -->
        <detailsBox ref="detailsBox" 
            @list="handleList" 
            @chooseMapItem="chooseMapItem"
            @openModal="hanldeOpenModal"
            @cancel="handleCancel"
            :drawColor="drawColor" 
            v-if="detailsShow" @goback="goback">
        </detailsBox>
        <detailsModal ref="detailsModal"
            v-if="detailsModalShow" 
            @close="close"   
        >
        </detailsModal>
        <frame-selection-movebox 
            id="selection-movebox" 
            @mousedown="handleMouseDown($event)" 
            v-if="selectMoveBox"
            :selectDeviceList="selectDeviceList"
            @boxSelect="boxSelect"
            ref="frameSelectionMovebox" >
        </frame-selection-movebox>
    </div>
</template>
<script>
import mapCustom from '../../components/map/index.vue';
import { mapMutations } from 'vuex';
import leftBox from './components/leftBox.vue';
import rightBox from './components/rightBox.vue';
import detailsBox from './components/detailsBox.vue';
import detailsModal from './components/detailsModal.vue';
import { deviceRegion } from '@/api/modelMarket'
import frameSelectionMovebox from '../../components/map/frame-selection-movebox.vue';
export default {
	name: "ele-collision-analysis",
    components:{
        mapCustom,
        leftBox,
        rightBox,
        detailsBox,
        frameSelectionMovebox,
        detailsModal
    },
    props: {
        mapType: {
            type: Object,
            default: () => {
                return {
                    "mapName": 'face',
                    'sectionName': 'faceMap', // 模态框类型
                    'seleType': 'Camera_Face', //框选类型
                }
            }
        }
    },
    data() {
        return {
            deviceList: [],
            drawStyle: {
                color: '#2C86F8', //颜色
                fillColor: '#2C86F8', //填充颜色
                weight: 3, //宽度，以像素为单位
                opacity: 1, //透明度，取值范围0 - 1
                fillOpacity: 0.2, //填充的透明度，取值范围0 - 1
                lineStyle: '#2C86F8',
                strokeColor: '#2C86F8',
                'showRadius': true
            },
            drawColor: ['#2C86F8','#1FAF81','#F29F4C','#A786FF','#48BAFF'],
            tablists:[],
            detailsList: [],
            rightBoxShow: false,
            detailsShow: false,
            collisionPoints:[],
            currentClickIndex: -1,
            mousel: 0,
            mouseT: 0,
            mouseX: 0,
            mouseY: 0,
            selectMoveBox: false,
            allCameraList: [],
            selectDeviceList: [],
            detailsModalShow: false,
            searchForm: ''
        }
    },
    async created() {
        this.setLayoutNoPadding(true)
    },
    destroyed() {
        this.setLayoutNoPadding(false)
    },
    mounted() {
        this.init();
        this.getMapLayerByTypeSite()
    },
    methods: {
        ...mapMutations('admin/layout', ['setLayoutNoPadding']),
        hanldeOpenModal(item) {
            this.detailsModalShow = true;
            this.$nextTick(() => {
                this.$refs.detailsModal.init(item, this.searchForm);
            })
        },  
        
        // 撒点数据
        async getMapLayerByTypeSite() {
            this.siteListFlat = [];
            let roleParam = {
                dataTypeList: [2]
            };
            let { data } = await deviceRegion(roleParam);
            data.entities.forEach(item => {
                item.mapType = item.deviceType
            })
            this.allCameraList = data.entities;
        },
        handleList(list) {
            this.collisionPoints = list;
        },
        // 搜索
        handleSearch(params) {
            this.searchForm = params;
            this.detailsShow = true;
            this.clearMap();
            this.$nextTick(() => {
                this.$refs.detailsBox.init(params);
            })
        },
        handleReset() {
            this.detailsShow = false;
            this.rightBoxShow = false; 
            this.$refs.mapBase.clearAll([1,2]);
            this.clearMap()
        },
        checkChange(event, row, index) {
            if(event) { //展示
                this.$refs.mapBase.showDraw(index);
            } else { //隐藏
                this.$refs.mapBase.hiddenDraw(index);
            }
        },
        areaClick(item) {
            this.selectMoveBox = true;
            this.$refs.frameSelectionMovebox.setTime({startDate: item.startAbsTime,endDate: item.endAbsTime})
            this.selectDeviceList = JSON.parse(JSON.stringify(item.deviceList));
        },
        selectDraw(type, index, list) {
            // this.clearMap()
            list.map((item, index) => {
                this.$set(this.drawColor, index, item.color)
            })
            this.$refs.mapBase.selectDraw(type, true, index, this.drawStyle);
        },
        deleDraw(index) {
            this.$refs.mapBase.clearModelDraw(index);
        },
        selectData(value) {
            this.$refs.leftBox.handleAdd(value);
        },
        handleCancel() {
           this.detailsShow = false; 
        },
        handleSpaceTime(params, type) {
            this.detailsShow = true;
            this.rightBoxShow = false;
            this.$nextTick(() => {
                this.$refs.detailsBox.init(params,type);
            })
        },
        goback() {
            this.detailsShow = false;
            this.rightBoxShow = true;
            this.clearMap()
        },
        // 清除地图上控件
        clearMap() {
            this.$refs.mapBase.resetCollMarker();
            this.$refs.mapBase.closeMapDom();
        },
        // 左侧选中项 点击普通搜索列表
        chooseMapItem (index, tab = '') {
            this.currentClickIndex = index;
        },
        // 拖拽
        init() {
            let box = document.querySelector('.time-space-colli')
            box.onmousemove = (event) => {
                let box = document.getElementById('selection-movebox');
                if(this.isClick) {
                    let divX = event.clientX - (this.mouseX - this.mousel);
                    let divY = event.clientY - (this.mouseY - this.mouseT);
                    box.style.top = divY + 'px';
                    box.style.left = divX + 'px';
                }
            }
            box.onmouseup = (event) => {
                this.isClick = false;
            }
        },
        handleMouseDown(event){
            let box = document.getElementById('selection-movebox');
            this.isClick = true;
            this.mouseX = event.clientX;
            this.mouseY = event.clientY;
            this.mousel = box.offsetLeft;
            this.mouseT = box.offsetTop;
        },
        handleMouseup(event) {
            this.isClick = false;
        }, 
        boxSelect() {
            this.selectMoveBox = false;
        },
        close() {
            this.detailsModalShow = false;
        }
    }
}
</script>
<style lang='less' scoped>
.time-space-colli{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
}
.move-div{
    width: 100px;
    height: 100px;
    background: #fff;
    position: absolute;
    left: 0;
    top: 0;
    user-select: none;
    border: 1px solid #000;
}
.move-box{
    position: absolute;
    top: 13px;
    left: 385px;
    z-index: 1000;
    /deep/.ocx-masker{
        display: none;
    } 
}
</style>