<template>
  <div class="unit-orderdetail-container">
    <div class="flex-row detail-header">
      <p class="f-16 base-text-color flex-aic">
        <span class="title-before mr-xs"></span>
        <span>任务名称：</span>
        <span>{{ taskName }}</span>
      </p>
      <div class="flex-aic">
        <api-organization-tree
          ref="orgTree"
          class="mr-md"
          :select-tree="searchData"
          :custorm-node="false"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
        <Checkbox
          v-model="searchData.isOwnOrgCode"
          :disabled="searchData.queryType !== QUERYTYPE_ASSIGN_UNIT"
          true-value="1"
          false-value="0"
          class="flex-aic"
          @on-change="changeOwnerOrder"
        >
          <span>本单位在手工单</span>
          <Tooltip
            max-width="300"
            content="【本单位在手工单】仅包含当前指派给本单位处理的工单！取消勾选，系统则会统计当前指派给本单位处理和下发给下级单位处理的所有工单！"
          >
            <i class="icon-font icon-wenhao f-14 ml-xs font-dark-blue" @click.stop.prevent></i>
          </Tooltip>
        </Checkbox>
      </div>
    </div>
    <div class="detail-content auto-fill mt-sm">
      <ui-switch-drop
        class="ui-switch-tab  mb-sm"
        v-model="queryConditionStatus"
        :tab-list="stateOptions"
        @changeTab="onChangeSwitchDrop"
      >
      </ui-switch-drop>
      <div class="component-container auto-fill">
        <all-work-order
          ref="workOrder"
          :common-search-data="searchData"
          :query-condition-status="queryConditionStatus"
          :statistics-detail="statisticsDetail"
          :show-search-keys="showSearchKeys"
          :is-show-more-search="false"
          @on-change-table-data="onChangeTableData"
        >
        </all-work-order>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { stateOptionsMap, ALLORDER, ASSIGN_UNIT } from '../util/enum.js';
import dealWatch from '@/mixins/deal-watch';

export default {
  name: 'unitorderdetail',
  mixins: [dealWatch],
  props: {},
  data() {
    return {
      queryConditionStatus: 'qb',
      componentName: null,
      searchData: {
        type: ALLORDER,
        orgCode: '000000',
        queryType: ASSIGN_UNIT,
        beginTime: '',
        endTime: '',
        isOwnOrgCode: '0', //本单位在手工单
      },
      stateOptions: [],
      defaultTag: 0,
      statisticsDetail: null,
      showSearchKeys: ['workOrderNum', 'workOrderName', 'leftSignDictKey'], //工单编码，工单名称，签收状态搜索条件
      taskName: '',
      taskId: null,
      QUERYTYPE_ASSIGN_UNIT: ASSIGN_UNIT,
    };
  },
  activated() {
    this.startWatch(
      '$route',
      () => {
        if (this.$route.query.componentName == 'unitorderdetail') {
          this.receiveParamsFromOtherPages(this.$route.query);
        }
      },
      { immediate: true },
    );
  },
  async mounted() {
    this.stateOptions = stateOptionsMap[this.searchData.queryType];
  },
  watch: {},
  methods: {
    selectedOrgTree(area) {
      this.searchData.orgCode = area.orgCode;
      this.orderSearch();
    },
    async onChangeSwitchDrop(val) {
      try {
        this.queryConditionStatus = val;
        await this.$nextTick();
        this.orderSearch();
      } catch (e) {
        console.log(e);
      }
    },
    async onChangeTableData({ total }) {
      let index = this.stateOptions.findIndex((item) => item.value === this.queryConditionStatus);
      if (index !== -1) {
        this.$set(this.stateOptions[index], 'total', total);
      }
    },
    //选中本单位在手工单触发
    changeOwnerOrder() {
      this.orderSearch();
    },
    orderSearch() {
      this.$refs.workOrder.search({ orderTaskIdList: [this.taskId] });
    },
    //接收从其他页面过来的数据
    receiveParamsFromOtherPages(params) {
      if (!params.purpose) {
        return;
      }
      //所有路由跳转的操作
      switch (params.purpose) {
        case 'pickTaskOrder':
          this.pickTaskOrder(params);
          break;
      }
    },
    //选中任务模式对应参数
    async pickTaskOrder(params) {
      if (
        this.searchData.queryType === params.queryType &&
        this.stateOptions === stateOptionsMap[this.searchData.queryType] &&
        this.taskName === params.taskName &&
        this.taskId === params.orderTaskId &&
        this.queryConditionStatus === params.queryConditionStatus &&
        this.searchData.orgCode === (params.orgCode || params.defaultOrg)
      ) {
        return;
      }
      this.searchData.queryType = params.queryType;
      this.stateOptions = stateOptionsMap[this.searchData.queryType];
      this.taskName = params.taskName;
      this.taskId = params.orderTaskId;
      this.queryConditionStatus = params.queryConditionStatus;
      if (this.$refs.orgTree) {
        this.$refs.orgTree.selectTree.orgCode = params.orgCode || params.defaultOrg;
        this.searchData.orgCode = params.orgCode || params.defaultOrg;
      }
      await this.$nextTick();
      this.orderSearch();
    },
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      initialOrgList: 'common/getInitialOrgList',
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    AllWorkOrder: require('@/views/disposalfeedback/governanceorder/module/all-work-order.vue').default,
    UiSwitchDrop: require('@/components/ui-switch-drop/ui-switch-drop.vue').default,
  },
  beforeDestroy() {},
};
</script>
<style lang="less" scoped>
.unit-orderdetail-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-content);
  display: flex;
  flex-direction: column;
  .detail-header {
    height: 50px;
    background: var(--bg-collapse-item);
    padding: 0 20px;
    .title-before {
      display: inline-block;
      height: 15px;
      width: 4px;
      background: var(--color-display-title-before);
    }
  }
  .detail-content {
    padding: 0 20px;
  }
}
</style>
