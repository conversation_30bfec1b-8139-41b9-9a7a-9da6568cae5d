<!--
    * @FileDescription: 疑似人-rfid关系绑定
    * @Date: 2024/08/09
    * @LastEditors: Z
    * @LastEditTime: 
-->
<template>
	<div class="people-follow container">
    <ui-loading v-if="loading" />
		<!-- 地图 -->
    <mapCustom ref="mapBase" mapType="peopleFollow" @createMapBack="createMapBack"/>
    <!-- 左面信息展示框 -->
    <div class="leftBox">
        <div class="search-box">
            <div class="title">
                <p>疑似人-RFID关系绑定</p>
            </div>
            <!-- 搜索框 -->
            <search-form ref="searchForm" v-if="showQueryCondition" @search="handleSearch"></search-form>
            <!-- 搜索结果 -->
            <div class="left-search-content" v-if="!showQueryCondition">
              <result-list :resultList="resultList" pageName="vid-rfid" @goback="goback" :pageParams="pageParams" @change-page="changePage" @change-Item="changeItem" @together-analyse="togetherAnalyse"></result-list>
            </div>
        </div>
    </div>
    <!-- 进度条 -->
    <search-progress :progress="progress" :percent="percent" v-show="showProgress" :isBigProgress="true"></search-progress>
    <!-- 右侧结果 -->
    <right-list-one :appearList="appearList" pageName='vidRFID' v-if="showResult3" @look-location="lookLocation"></right-list-one>

    <Modal :title="currentObj.deviceName"
               v-model="dialogVisible"
               footer-hide
               width="820px">
      <template>
        <ui-table :columns="columns" :data="currentObj.captureList" height="400">
					<template #sceneImg="{ row }">
						<img :src="row.sceneImg"
              v-viewer
              alt
              style="width:80px;height:80px;vertical-align:middle;cursor:pointer;">
					</template>
					<template #absTime="{ row }">
						<div>{{ row.absTime.toString().slice(5) }}</div>
					</template>
          <template #togetherScenePath="{ row }">
						<img src="@/assets/img/card-bg/imsi.png"
              alt
              style="width:80px;height:80px;vertical-align:middle;cursor:pointer;">
					</template>
					<template #togetherTime="{ row }">
						<div>{{ row.togetherTime.toString().slice(5) }}</div>
					</template>
				</ui-table>
      </template>
    </Modal>
	</div>
</template>
<script>
import mapCustom from '@/views/model-market/components/map/index.vue';
import searchForm from './components/searchForm.vue';
import { mapMutations } from 'vuex';
import { getUUID, getDateDiff } from "@/util/modules/common.js"
import { getProgress } from '@/api/modelMarket';
import { vidFindRFIDList, vidFindRFIDAlongPointList } from '@/api/recommend';
import SearchProgress from '@/components/SearchProgress.vue'
import resultList from '@/views/model-market/components/resultList.vue';
import RightListOne from '@/views/model-market/components/RightListOne.vue';

export default {
	name: "people-follow",
  components:{
      mapCustom,
      searchForm,
      SearchProgress,
      resultList,
      RightListOne
  },
  props: {
    taskParams: {
      type: Object,
      default: () => ({})
    }
  },
  data () {
      return {
          params: {},
          pageParams: {
            pageNumber: 1,
            totalCount: 0,
            pageSize: 10
          },
          currentUUID: '',
          resultList: [],
          showProgress: false,
          progress: 0,
          percent: 0,
          showQueryCondition: true,
          showResult3: false,
          currentInterval: null,
          currentItem: {},
          appearList: [],
          loading: false,
          mainPointList: [],
          togetherPointList: [],
          mapObj: null,
          currentObj: {
            deviceName: "",
            captureList: []
          },
          dialogVisible: false,
          columns: [
              { title: '序号', align: 'center', width: 90, type: 'index', key: 'index' },
              { title: '主人员', slot: 'sceneImg', },
              { title: '主人员出现时间', slot: 'absTime', },
              { title: '伴随电围', slot: 'togetherScenePath', },
              { title: '伴随电围出现时间', slot: 'togetherTime', },
              // { title: '相差距离(米)', key: 'distanc', },
              { title: '相差时间(秒)', key: 'diffTime', },
          ],
          currentUserIdCard: '',
      }
  },
  created() {
      this.setLayoutNoPadding(true)
      if(this.$route.query.vid) {
          this.$nextTick(() => {
              this.$refs.searchForm.queryParams.vid = this.$route.query.vid
          })
      }
      this.$nextTick(() => {
          // 推荐中心查看
          console.log('taskParams: ', this.taskParams)
          if (!Toolkits.isEmptyObject(this.taskParams)) {
              let params = {...this.taskParams.params}
              delete params.dayNum
              delete params.deviceGbIdList
              if (this.taskParams.queryStartTime) this.$refs.searchForm.queryParams.startTime = this.taskParams.queryStartTime
              if (this.taskParams.queryEndTime) this.$refs.searchForm.queryParams.endTime = this.taskParams.queryEndTime
              this.$refs.searchForm.queryParams = { ...this.$refs.searchForm.queryParams, ...params }
              this.$refs.searchForm.handleSearch()
          }
      })
  },
  destroyed() {
      this.setLayoutNoPadding(false)
  },
  methods: {
    ...mapMutations('admin/layout', ['setLayoutNoPadding']),
    // 创建完地图回调
    createMapBack (map) {
      this.mapObj = map;
    },
    handleSearch(form) {
      this.params = form
      this.queryList(true)
    },
    queryList (isSearch) {
      let self = this;
      if(new Date(this.params.startTime) > new Date(this.params.endTime)){
        this.$Message.info('开始时间不能大于结束时间')
        return
      }
      let param = {
        vid: self.params.vid,
        startTime: self.$dayjs(self.params.startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: self.$dayjs(self.params.endTime).format('YYYY-MM-DD HH:mm:ss'),
        rangTime: self.params.rangTime,
        minCount: self.params.minCount,
        type: 0,
        relationshipType: 2, //类型：1-电围设备与人脸抓拍设备  2-RFID设备与人脸抓拍设备
      };
      if (isSearch) {
        param.search = true;
        self.pageParams.pageNumber = 1;
        self.pageParams.pageSize = 10;
        self.pageParams.totalCount = 0;
      } else {
        param.search = false;
      }
      param.pageSize = self.pageParams.pageSize;
      param.pageNumber = self.pageParams.pageNumber;
      self.currentUUID = getUUID();
      param.progressId = self.currentUUID;
      self.showProgress = true;
      vidFindRFIDList(param).then(function (res) {
        if (res.code == 200) {
          let list = res.data.entities || []
          list.forEach(t => {
            t.type = 0
            if(!self.currentUserIdCard){
              t.showLink = false
            }else if(self.currentUserIdCard == t.idCard){
              t.showLink = false
            }else{
              t.showLink = true
            }
          }); 
          self.resultList = list || [];
          self.pageParams.totalCount = res.data.total;
          self.showQueryCondition = false;
        }
      }).finally(()=>{
        this.showProgress = false;
        this.progress = 0;
        if(this.currentInterval){
          clearInterval(this.currentInterval);
          this.currentInterval = null;
        }
      });
      if(this.showProgress){
        this.queryProgress();
      }
    },
    //查询当前任务的进度
    queryProgress(){
        let that = this;
        clearInterval(that.currentInterval);
        that.currentInterval = null;
        that.currentInterval = setInterval(()=>{
            getProgress({progressId: that.currentUUID}).then((resp)=>{
                if(resp.code == 200){
                    that.progress = resp.data;
                    that.percent = (resp.data/100)*360;
                }
            });
        },200);
    },
    goback(step){
      if (step == 0) {
        this.showQueryCondition = true;
        this.showResult3 = false
        this.resultList = []
        this.$refs.mapBase.clearPoint()
      }
    },
    changePage(val){
      this.pageParams.pageNumber = val;
      this.queryList(false);
    },
    changeItem (item) {
      let params = {
        vid: this.params.vid,
        rfidCode: item.rfidCode,
        startTime: this.$dayjs(this.params.startTime).format('YYYY-MM-DD HH:mm:ss'),
        endTime: this.$dayjs(this.params.endTime).format('YYYY-MM-DD HH:mm:ss'),
        search: true,
        rangTime: this.params.rangTime,
        relationshipType: 2, //类型：1-电围设备与人脸抓拍设备  2-RFID设备与人脸抓拍设备
      };
      let self = this;
      self.currentUUID = getUUID();
      params.progressId = self.currentUUID;
      self.showProgress = true;
      vidFindRFIDAlongPointList(params)
        .then(res => {
          if (res.code == "200") {
            let arr = res.data;
            let list = arr.filter(t => {
              return t.rfidRecordVo;
            });
            self.appearList = list;

            self.mainPointList = [];
            self.togetherPointList = [];
            list.forEach(m => {
              self.mainPointList.push(m.faceTrailVo);
              self.togetherPointList.push(m.rfidRecordVo);
            });
            self.showResult3 = true;
            // 请求地图事件
            self.initalPositionInMap();
          }
        })
        .finally(function () {
          clearInterval(self.currentInterval);
          self.currentInterval = null;
          self.showProgress = false;
          self.progress = 0;
        });
      self.queryProgress();
    },
    togetherAnalyse(item){
      this.showQueryCondition = true
      this.showResult3 = false
      this.$nextTick(() => {
        this.$refs.searchForm.vid = item.vid
      })
    },
    // 时间格式 2018-08-20 16:29:41
    systemTimeTransToS (sTime) {
      let msTime = new Date(sTime).getTime(); //得到毫秒数
      return msTime * 0.001;
    },
    jundgeTogetherCanmeras (item) {
      let self = this;
      let flagObj = {
        status: false,
        togetherTime: "",
        //togetherFacePath: "",
        togetherScenePath: ""
      };

      // 相同点位
      self.togetherPointList.forEach(comItem => {
        if (comItem.deviceId !== item.deviceId) {
          return;
        }
        let firstLogTimeInS = self.systemTimeTransToS(item.absTime);
        let theOtherFirstLogTimeInS = self.systemTimeTransToS(comItem.absTime);

        let distanceTime = theOtherFirstLogTimeInS - firstLogTimeInS;
        // 前后$scope.params.spare时间间隔内
        if (self.params.rangTime >= Math.abs(distanceTime)) {
          comItem.isTogetherCamera = true;
          flagObj.status = true;
          flagObj.togetherTime = comItem.absTime;
          //flagObj.togetherFacePath = comItem.traitImg;
          flagObj.togetherScenePath = comItem.sceneImg;
          return;
        }
      });
      return flagObj;
    },
    initalPositionInMap () {
      let self = this;
      let timeS = 0;
      if (!self.mainPointList.length && !self.togetherPointList.length) {
        return;
      }
      let markData = [];
      self.mainPointList.forEach((itemM, index) => {
        let togetherObj = self.jundgeTogetherCanmeras(itemM);
        if (itemM.deviceId) {
          if (itemM.longitude && itemM.latitude) {
            itemM.longitude = parseFloat(itemM.longitude);
            itemM.latitude = parseFloat(itemM.latitude);

            // 区别type： 'A'
            itemM.isType = "A";
            // 相同点位标识位；!!!
            itemM.isTogetherCamera = togetherObj.status;
            if (togetherObj.togetherTime) {
              itemM.togetherTime = togetherObj.togetherTime;
            }
            itemM.diffTime = Math.abs(
              getDateDiff(itemM.absTime, itemM.togetherTime)
            );
            if (togetherObj.togetherScenePath) {
              itemM.togetherScenePath = togetherObj.togetherScenePath;
            }
            // 时间轴变量添加；!!!
            itemM.long =
              index == 0 ? 0 : self.systemTimeTransToS(itemM.absTime) - timeS;
            timeS = self.systemTimeTransToS(itemM.absTime);
            itemM.absTimeS = timeS;
          }
        }
        let flag;
        markData.forEach(function (mapItem) {
          if (mapItem.deviceId == itemM.deviceId) {
            itemM.index = index + 1;
            mapItem.data.push(itemM);
            mapItem.title = mapItem.title + "," + (index + 1);
            mapItem.sameNum = !mapItem.isTogetherCamera
              ? mapItem.sameNum
              : mapItem.sameNum + 1;
            flag = true;
            return;
          }
        });
        if (!flag) {
          itemM.index = index + 1;
          markData.push({
            deviceId: itemM.deviceId,
            deviceName: itemM.deviceName,
            longitude: itemM.longitude,
            latitude: itemM.latitude,
            title: (index + 1).toString(),
            sameNum: !itemM.isTogetherCamera ? 0 : 1,
            data: [itemM]
          });
        }
      });
      self.mapObj.removeLayerByName("togetherLayer");
      var overlayLayer = new NPMapLib.Layers.OverlayLayer("togetherLayer");
      self.mapObj.addLayer(overlayLayer);
      markData.forEach(function (data) {
        // 主人员撒点
        var point = {
          lon: data.longitude,
          lat: data.latitude
        };
        var size = new NPMapLib.Geometry.Size(28, 28);
        var imgUrl = require("@/assets/img/map/mapPoint/map-qiangji.png");
        var icon = new NPMapLib.Symbols.Icon(imgUrl, size);
        icon.setAnchor(
          new NPMapLib.Geometry.Size(-size.width / 2, -size.height / 2)
        );
        var marker = new NPMapLib.Symbols.Marker(point);

        marker.setIcon(icon);
        overlayLayer.addOverlay(marker);
        // 伴随点标注
        if (data.sameNum) {
          var sameImgUrl = require("@/assets/img/map/new/icon_loca6.png");
          var sameSize = new NPMapLib.Geometry.Size(30, 38);
          var sameicon = new NPMapLib.Symbols.Icon(sameImgUrl, sameSize);
          var sameMark = new NPMapLib.Symbols.Marker(point);
          var sameLabel = new NPMapLib.Symbols.Label(data.sameNum.toString(), {
            offset: new NPMapLib.Geometry.Size(0, 22)
          });
          sameLabel.setStyle({
            fontSize: 12,
            color: "#000000",
            align: "center"
          });
          sameMark.setIcon(sameicon);
          sameMark.setLabel(sameLabel);
          overlayLayer.addOverlay(sameMark);
          // 伴随点信息框
          sameMark.addEventListener(NPMapLib.MARKER_EVENT_CLICK, function (
            a,
            b
          ) {
            self.dialogVisible = true;
            self.currentObj.deviceName = data.deviceName;
            self.currentObj.captureList = data.data;
          });
        }
      });

      if (self.togetherPointList.length > 0) {
        self.mapData = self.togetherPointList;
      } else {
        self.$Message.warning("没有查到相关轨迹");
      }
    },
    lookLocation(item){
      if (item.longitude && item.latitude) {
        this.$refs.mapBase.clearPoint()
        this.$refs.mapBase.sprinkleDot(item)
      }
    }
  },
  beforeDestroy(){
    if(this.currentInterval){
      clearInterval(this.currentInterval)
      this.currentInterval = null
    }
  }
}
</script>

<style lang='less' scoped>
.people-follow{
    padding: 0;
    position: relative;
    width: 100%;
    height: 100%;
    .leftBox{
      position: absolute;
      top: 10px;
      left: 10px;
      height: calc(~'100% - 20px');
      // 头部名称
      .title{
          font-size: 16px;
          font-weight: bold;
          color: rgba(0,0,0,0.9);
          height: 40px;
          position: relative;
          line-height: 40px;
          padding-left: 20px;
          border-bottom: 1px solid #D3D7DE;
          display: flex;
          justify-content: space-between;
          align-items: center;
          top: 0;
          z-index: 1;
          background: #fff;
          border-top-left-radius: 4px;
          border-top-right-radius: 4px;
          &:before {
              content: '';
              position: absolute;
              width: 3px;
              height: 20px;
              top: 50%;
              transform: translateY(-50%);
              left: 10px;
              background: #2c86f8;
          }
          span{
              color: #2C86F8; 
          }
          /deep/.ivu-icon-ios-close{
              font-size: 30px;
              cursor: pointer;
          }
      }
      .search-box{
          background: #fff;
          box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
          border-radius: 4px;
          width: 370px;
          height: 100%;
        .left-search-content {
          height: calc(~"100% - 40px");
        }
      }
    }
}
</style>