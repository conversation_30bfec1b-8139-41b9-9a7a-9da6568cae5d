<!--
 * @Author: du<PERSON><PERSON>
 * @Date: 2024-06-28 15:37:07
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-25 10:35:54
 * @Description: 
-->
<template>
  <div class="layout">
    <tabsPage v-model="selectLi" :list="componentList" />
    <keep-alive>
      <component :is="currentComponent.componentName" :key="selectLi" />
    </keep-alive>
  </div>
</template>
<script>
import tabsPage from "../components/tabs.vue";
import CriminalRecord from "./criminal-record/index.vue";
import FrequentEntertainment from "./frequent-entertainment/index.vue";
import SchoolOut from "./school-out/index.vue";
import NightOut from "./night-out/index.vue";
import CriminalWith from "./criminal-with/index.vue";
import ActivePeople from "./active-people/index.vue";

export default {
  name: "alarm-manager",
  components: {
    tabsPage,
    CriminalRecord,
    FrequentEntertainment,
    SchoolOut,
    NightOut,
    CriminalWith,
    ActivePeople,
  },
  data() {
    return {
      selectLi: 1,
      componentList: [
        {
          label: "前科人员发现报警",
          value: 1,
          componentName: "CriminalRecord",
        },
        {
          label: "频繁出入娱乐场所",
          value: 2,
          componentName: "FrequentEntertainment",
        },
        {
          label: "上学时间校外出现",
          value: 3,
          componentName: "SchoolOut",
        },
        {
          label: "深夜时间出行",
          value: 4,
          componentName: "NightOut",
        },
        {
          label: "与违法前科人同行",
          value: 5,
          componentName: "CriminalWith",
        },
        {
          label: "活跃人员分析",
          value: 6,
          componentName: "ActivePeople",
        },
      ],
    };
  },
  computed: {
    currentComponent() {
      let component = this.componentList.find(
        (item) => item.value == this.selectLi
      ) || {
        label: "前科人员发现报警",
        value: 1,
        radioList: [],
        componentName: "CriminalRecord",
      };
      return component;
    },
  },
  created() {
    if (this.$route.query.compareType) {
      this.selectLi = Number(this.$route.query.compareType);
    }
  },
};
</script>
<style lang="less" scoped>
.layout {
  width: 100%;
  height: inherit;
  display: flex;
  flex-direction: column;
  .tabs {
    height: 100px;
  }
}
</style>
