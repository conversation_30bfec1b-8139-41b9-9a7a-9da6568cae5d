<template>
  <div class="vehicle-modal">
    <common-form
      :label-width="getWidth"
      class="common-form"
      ref="commonForm"
      :moduleAction="moduleAction"
      :form-data="formData"
      :form-model="formModel"
      :task-index-config="taskIndexConfig"
      @updateFormData="updateFormData"
      @handleDetect="getDetect"
    >
      <div slot="extractCar">
        <template
          v-if="
            [
              'VEHICLE_INFO_PASS',
              'VEHICLE_MAIN_PROP',
              'VEHICLE_TYPE_PROP',
              'VEHICLE_FULL_INFO',
              'VEHICLE_FULL_INFO_IMPORTANT',
              'VEHICLE_UPLOAD',
              'VEHICLE_UPLOAD_IMPORTANT',
              'VEHICLE_CLOCK',
            ].includes(indexType)
          "
        >
          <FormItem label="每设备抽取图片" prop="captureNum">
            <InputNumber
              v-model="formData.captureNum"
              class="input-width"
              placeholder="请输入抽取设备图片"
              clearable
            ></InputNumber>
          </FormItem>
          <FormItem
            v-if="
              [
                'VEHICLE_INFO_PASS',
                'VEHICLE_FULL_INFO',
                'VEHICLE_FULL_INFO_IMPORTANT',
                'VEHICLE_MAIN_PROP',
                'VEHICLE_TYPE_PROP',
              ].includes(indexType)
            "
            label=""
            :class="{ 'mt-minus-sm': formData.captureNum }"
            prop="isMissPic"
          >
            <Checkbox v-model="formData.isMissPic" label="" :true-value="1" :false-value="0">
              <span>图片数量不足，则设备不合格</span>
            </Checkbox>
          </FormItem>
          <FormItem
            prop="deviceQueryForm.dayByCapture"
            class="mb-sm"
            label="图片抽取范围"
            :rules="[{ validator: validateDayByCapture, trigger: 'blur', required: true }]"
          >
            <span class="base-text-color mr-xs">近</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.dayByCapture"
              :min="0"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color">天，</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.startByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点至</span>
            <InputNumber
              v-model.number="formData.deviceQueryForm.endByCapture"
              :min="0"
              :max="23"
              :precision="0"
              class="mr-sm width-mini"
            ></InputNumber>
            <span class="base-text-color mr-sm">点</span>
          </FormItem>
        </template>
      </div>
      <div
        slot="waycondiction"
        class="mt-xs"
        v-if="['1', '3'].includes(formData.detectMode) && filterCondition.includes(indexType)"
      >
        <div>
          <span class="base-text-color">检测条件：</span>
          <div>
            <Checkbox class="ml-sm" v-model="formData.deviceQueryForm.detectPhyStatus" true-value="1" false-value="0"
              >设备可用</Checkbox
            >
            <Checkbox
              class="ml-sm"
              v-if="notReportionDeviceList.includes(indexType)"
              v-model="formData.deviceQueryForm.examReportStatus"
              true-value="1"
              false-value="0"
              >设备未报备</Checkbox
            >
          </div>
          <div v-if="['1', '3'].includes(formData.detectMode) && !['VEHICLE_ONLINE_RATE'].includes(indexType)">
            <FormItem
              prop="deviceQueryForm.dayByFilterOnline"
              label=""
              :rules="[{ validator: validateDayByFilterOnline, trigger: 'blur' }]"
            >
              <Checkbox class="ml-sm mb-sm" v-model="formData.deviceQueryForm.filterOnline">设备有流水</Checkbox>
              <span class="base-text-color">近</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.dayByFilterOnline"
                :min="0"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color">天，</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.startByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点至</span>
              <InputNumber
                v-model.number="formData.deviceQueryForm.endByFilterOnline"
                :min="0"
                :max="23"
                :precision="0"
                class="mr-sm width-mini"
              ></InputNumber>
              <span class="base-text-color mr-sm">点</span>
              <div class="capture-vehicle">
                <span class="base-text-color mr-sm">抓拍车辆不少于</span>
                <InputNumber
                  v-model.number="formData.deviceQueryForm.countByFilterOnline"
                  :min="0"
                  :precision="0"
                  class="mr-sm width-mini"
                ></InputNumber>
                <span class="base-text-color">张</span>
                <p class="color-failed">说明：系统只检测满足条件的设备。</p>
              </div>
            </FormItem>
          </div>
        </div>
      </div>
    </common-form>
    <Form ref="formData" class="form-content edit-form" :model="formData" :rules="ruleCustom" :label-width="getWidth">
      <template v-if="['VEHICLE_UPLOAD', 'VEHICLE_UPLOAD_IMPORTANT'].includes(indexType)">
        <FormItem label="图片上传时延" class="right-item mt-md">
          <FormItem
            class="inline one-time"
            prop="beforeTimeDelay"
            :rules="[{ validator: validateTimeDelayBefore, trigger: 'change', required: false }]"
          >
            <InputNumber
              v-model.number="formData.beforeTimeDelay"
              :formatter="(value) => `${parseInt(value)}`"
              class="width-mini"
            ></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="beforeTimeFormat">
            <Select v-model="formData.beforeTimeFormat" transfer class="width-mini ml-sm">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
          <span class="base-text-color"> <= 接收时间 - 抓拍时间 <= </span>
          <FormItem
            class="inline one-time"
            prop="timeDelay"
            :rules="[{ validator: validateTimeDelay, trigger: 'change', required: true }]"
          >
            <InputNumber v-model.number="formData.timeDelay" class="width-mini mr-sm"></InputNumber>
          </FormItem>
          <FormItem class="inline" prop="timeFormat">
            <Select v-model="formData.timeFormat" transfer class="width-mini">
              <Option value="s">秒</Option>
              <Option value="m">分</Option>
              <Option value="h">时</Option>
            </Select>
          </FormItem>
        </FormItem>
        <p class="color-failed" :style="{ marginLeft: `${getWidth}px` }">说明：不配置表示允许时间倒挂</p>
      </template>

      <FormItem
        label="抓拍时间和接收时间允许时间误差"
        class="right-item mb-sm"
        v-if="['VEHICLE_CLOCK'].includes(indexType)"
      >
        <InputNumber v-model.number="formData.timeDelay" placeholder="请输入误差时间" class="w292"></InputNumber>
        <Select v-model="formData.timeFormat" class="width-mini ml-sm" transfer>
          <Option value="s">秒</Option>
          <Option value="m">分</Option>
          <Option value="h">时</Option>
        </Select>
        <p class="label-color">时钟准确要求车辆数据的抓拍时间不得晚于数据接收时间，误差在30秒（可配置）以内。</p>
      </FormItem>
      <FormItem
        label="时间范围"
        class="right-item mb-sm"
        prop="name"
        v-if="
          ['VEHICLE_ONLINE_RATE', 'VEHICLE_DEVICE_CONNECT_INTERNET', 'VEHICLE_ONLINE_RATE_ADVANCE'].includes(indexType)
        "
      >
        <Select v-model="formData.timeDelay" class="select-width" placeholder="请选择时间范围" transfer>
          <Option value="5">昨天</Option>
          <Option value="1">当天</Option>
          <Option value="2">最近两天</Option>
          <Option value="3">最近一周</Option>
          <Option value="4">最近30天</Option>
          <Option value="6" v-if="['VEHICLE_ONLINE_RATE', 'VEHICLE_DEVICE_CONNECT_INTERNET'].includes(indexType)"
            >自定义</Option
          >
        </Select>
      </FormItem>
      <FormItem
        label=" "
        :class="[formData.lastHours && formData.lastHours < 24 ? 'mb-md' : 'mb-lg']"
        v-if="formData.timeDelay == '6'"
        prop="lastHours"
        :rules="[{ validator: validateLastHours, trigger: 'change', required: false }]"
      >
        <div class="lastHours-input base-text-color">
          最近<InputNumber
            :min="0"
            :step="1"
            :active-change="false"
            :precision="1"
            v-model="formData.lastHours"
            class="mr-sm ml-sm"
            placeholder="请输入"
          ></InputNumber
          >小时
        </div>
      </FormItem>
      <FormItem
        label=" "
        :class="['mb-sm', !formData.lastHours || formData.lastHours > 24 ? '' : 'mt-minus-sm']"
        v-if="formData.timeDelay == '6' && ['VEHICLE_ONLINE_RATE'].includes(indexType)"
      >
        <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
      </FormItem>
      <FormItem
        label="时间区间"
        class="mb-sm"
        v-if="formData.timeDelay != '6' && ['VEHICLE_ONLINE_RATE', 'VEHICLE_ONLINE_RATE_ADVANCE'].includes(indexType)"
      >
        <div class="inspection">
          <div class="row-inspection" v-for="(item, index) in formData.dateRang" :key="index">
            <FormItem>
              <div class="form-row">
                <span class="width-picker">
                  <Select class="time-picker" transfer v-model="item.hourStart" clearable>
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourEnd ? item.hourEnd <= it.value : false"
                      :class="item.hourEnd ? (item.hourEnd <= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
                <span class="color-bule mr-sm ml-sm">—</span>
                <span class="width-picker">
                  <Select class="time-picker" transfer v-model="item.hourEnd" clearable>
                    <Option
                      v-for="it in schemeList"
                      :value="it.value"
                      :key="it.value"
                      :disabled="item.hourStart ? item.hourStart >= it.value : false"
                      :class="item.hourStart ? (item.hourStart >= it.value ? 'notCheck' : '') : ''"
                      >{{ it.label }}</Option
                    >
                  </Select>
                </span>
                <span>
                  <span class="addition ml-sm" v-if="formData.dateRang.length - 1 === index" @click="toAdd"
                    ><i class="icon-font icon-tree-add f-16 font-other" title="添加"></i
                  ></span>
                  <span class="cancel ml-sm" v-if="index != 0" @click="toDel(index)"
                    ><i class="icon-font icon-shanchu1 f-14 font-other" title="删除"></i></span
                ></span>
              </div>
            </FormItem>
          </div>
        </div>
        <p class="label-color">说明：该时间区间内如果没有抓拍数据，则设备离线。</p>
      </FormItem>
      <FormItem
        label="车辆图片数量"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_DEVICE_CONNECT_INTERNET'].includes(indexType)"
      >
        <span class="base-text-color mr-sm">不少于</span>
        <InputNumber v-model="formData.imageNum"></InputNumber>
        <span class="base-text-color ml-sm">张</span>
        <p class="label-color">说明：该时间范围内如果有足够数量的抓拍数据，则设备联网。</p>
      </FormItem>
      <template v-if="['VEHICLE_DEVICE_CONNECT_INTERNET'].includes(indexType)">
        <FormItem label="多目设备检测" class="right-item mb-sm">
          <Checkbox v-model="formData.isMulti" :true-value="1" :false-value="0"
            >多目设备合并成一个设备进行检测统计</Checkbox
          >
        </FormItem>
        <FormItem
          label="多目设备定义"
          class="right-item mb-sm"
          prop="multiType"
          :rules="[{ validator: validateDayByMultiType, trigger: 'change', required: formData.isMulti ? true : false }]"
        >
          <RadioGroup class="mb-sm" v-model="formData.multiType" @on-change="setDefaultEmphasisData">
            <Radio :label="1" class="mr-lg" :disabled="!formData.isMulti">IP地址相同</Radio>
            <Radio :label="2" class="mr-lg" :disabled="!formData.isMulti">MAC地址相同</Radio>
            <Radio :label="3" :disabled="!formData.isMulti">IP地址相同和MAC地址相同</Radio>
          </RadioGroup>
        </FormItem>
      </template>
      <FormItem
        label="抓拍数据过少"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_CAPTURE_RATIONALITY'].includes(indexType)"
      >
        <span class="base-text-color">近 </span>
        <InputNumber :min="1" v-model="formData.timeDelay"></InputNumber>
        <span class="base-text-color"> 天，抓拍数量不大于 </span>
        <InputNumber :min="1" v-model="formData.imageNum"></InputNumber>
        <span class="base-text-color"> 张；</span>
      </FormItem>
      <FormItem
        label="抓拍数量突降"
        class="right-item mb-sm"
        prop="name"
        v-if="['VEHICLE_CAPTURE_RATIONALITY'].includes(indexType)"
      >
        <span class="base-text-color">检测当天抓拍数量较历史同一天的平均抓拍量相比，降低 </span>
        <InputNumber :min="1" :max="100" v-model="formData.captureDrop"></InputNumber>
        <span class="base-text-color"> %</span>
        <Tooltip transfer="true" max-width="420">
          <i class="icon-font icon-wenhao vt-middle icon-warning ml-sm"></i>
          <div slot="content">
            <p>抓拍数量突降计算逻辑：</p>
            <p>昨日抓拍量C1： 假设今日2021/10/20日发起检</p>
            <p>测，则C1=2021/10/19日抓拍量；</p>
            <p>历史同天抓拍量C2： 平台上线至2021年10月 19</p>
            <p>日号前所有星期二（10月19日是星期二）抓拍量的平均抓拍量；</p>
            <p>若（C2-C1）/C2>=50%（ 配置值），则判定抓拍数据量突降。</p>
          </div>
        </Tooltip>
      </FormItem>
      <!--   VEHICLE_VALID_SUBMIT_QUANTITY 车辆卡口有效报送数量达标率  -->
      <div v-if="indexType === 'VEHICLE_VALID_SUBMIT_QUANTITY'">
        <FormItem label="达标数量设置">
          <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true"
            >达标数量设置
            <span>{{ `已选择 ${formData.quantityConfig && formData.quantityConfig.length}个` }}</span>
          </Button>
        </FormItem>
        <FormItem label="有效设备检测">
          <div class="base-text-color">1、需产生过车数据</div>
          <FormItem class="inline base-text-color">
            <Select v-model="formData.deviceDetection.dataConfig.key" transfer disabled class="width-mini ml-sm">
              <Option value="now">当</Option>
            </Select>
            <Select v-model="formData.deviceDetection.dataConfig.value" transfer disabled class="width-mini ml-sm">
              <Option value="month">月</Option>
            </Select>
            <span class="mr-xs">，抓拍车辆不少于</span>
            <InputNumber
              v-model.number="formData.deviceDetection.dataConfig.quantity"
              placeholder="请输入图片上传时延"
              class="width-mini mr-xs"
            ></InputNumber>
            <span>张</span>
          </FormItem>
          <div class="base-text-color">2、基础信息填报完整准确</div>
          <rule-list
            :formModel="formModel"
            :indexRuleList="formData.deviceDetection.ruleList"
            topicType="1"
            :moduleAction="moduleAction"
          ></rule-list>
        </FormItem>
      </div>
      <!--车辆卡口设备位置完整率  -->
      <FormItem label="摄像机位置类型" v-if="['VEHICLE_EMPHASIS_LOCATION'].includes(moduleAction.indexType)">
        <RadioGroup class="mb-sm" v-model="formData.typeSource" @on-change="setDefaultEmphasisData">
          <Radio label="deviceTag">设备标签</Radio>
          <Radio label="deviceGather">采集区域字典</Radio>
        </RadioGroup>
        <div class="params-content mb-sm modal-video-image" v-if="formData.typeSource === 'deviceTag'">
          <ui-tag
            @close="handleClose(formData.emphasisData, item, index)"
            :closeable="item.source != 1"
            v-for="(item, index) in formData.emphasisData"
            :key="index + '-a' + item.key"
          >
            {{ item.value }}
          </ui-tag>
          <Button type="primary" @click="clickAdd('设备标签')">
            <i class="icon-font icon-tianjia f-12 mr-sm vt-middle" title="新增"> </i
            ><span class="vt-middle">新增</span></Button
          >
        </div>
        <div class="area" v-else-if="formData.typeSource === 'deviceGather'">
          <Button type="dashed" class="area-btn" @click="clickArea"
            >请选择采集区域类型
            <span>{{ `已选择 ${(formData.emphasisData || 0) && formData.emphasisData.length}个` }}</span></Button
          >
        </div>
      </FormItem>
      <template v-if="['VEHICLE_ACCURACY'].includes(indexType)">
        <FormItem label="检测规则设置" prop="rule">
          <rule-list
            :formModel="formModel"
            :formData="formData"
            :moduleAction="moduleAction"
            :indexRuleList="formData.ruleList"
          ></rule-list>
        </FormItem>
      </template>
      <template v-if="['VEHICLE_ASSET_REGISTER'].includes(indexType)">
        <FormItem label="资产库已注册车辆卡口来源" required>
          <RadioGroup v-model="formData.source">
            <Radio label="ALL" class="mr-lg">资产库中所有车辆卡口</Radio>
            <Radio label="REGISTERED">视图库中所有已注册车辆卡口</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="资产库已注册车辆卡口要求">
          <Checkbox v-model="formData.registeredRequireData.phyStatus" :true-value="1" false-value=""
            >设备可用</Checkbox
          >
        </FormItem>
        <FormItem label="未注册车辆卡口要求">
          <Checkbox v-model="formData.unregisteredRequireData.isOnline" :true-value="1" false-value="" class="mr-lg"
            >设备可用</Checkbox
          >
          <Checkbox v-model="formData.unregisteredRequireData.phyStatus" :true-value="1" false-value=""
            >设备在线</Checkbox
          >
        </FormItem>
      </template>
      <FormItem v-if="needPropertiesIndexType.includes(indexType)" label="检测结构化属性" prop="properties">
        <CheckboxGroup v-model="formData.properties">
          <Checkbox v-for="(item, index) in showVehicleProperties" :label="item.dataKey" :key="index">{{
            item.dataDes
          }}</Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem :label="isUpdatePhyStatus(indexType)[0]['label']" v-if="isUpdatePhyStatus(indexType).length > 0">
        <RadioGroup v-model="formData.isUpdatePhyStatus">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="对历史检测结果比对分析" v-if="historyComparisonConfigModule.includes(indexType)">
        <RadioGroup v-model="formData.isDetectContrast">
          <Radio :label="1">是</Radio>
          <Radio :label="0">否</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <quantity-config
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :data="formData.quantityConfig"
      :regionData="treeData"
      @query="quantityQuery"
    ></quantity-config>
    <OrgModal
      ref="orgModal"
      @query="choosQuery"
      :orgList="formData.orgCodeList"
      :formModel="formModel"
      :areaTreeData="areaTreeData"
      v-if="areaTreeData.length != 0"
    ></OrgModal>
    <customize-filter
      v-model="customSearch"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :checkbox-list="allDeviceFileList"
      :default-checked-list="defaultCheckedList"
      :field-name="fieldName"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
  </div>
</template>

<script>
import { defaultEmphasisData } from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field.js';
import { mapGetters, mapActions } from 'vuex';
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';
import {
  isUpdatePhyStatus,
  historyComparisonConfigModule,
} from '@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/static-field';
export default {
  components: {
    OrgModal: require('./org-modal/index.vue').default,
    CommonForm: require('./common-form/index.vue').default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    quantityConfig: require('@/components/quantity-config/index').default,
    AreaSelect: require('@/components/area-select').default,
    CustomizeFilter: require('@/components/customize-filter').default,
    UiTag: require('@/components/ui-tag').default,
  },
  props: {
    indexType: {
      type: String,
      default: '',
    },
    configInfo: {
      required: true,
      type: Object,
    },
    formModel: {
      required: true,
      type: String,
      default: 'edit',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
      treeData: 'common/getInitialAreaList',
      vehiclePropertyDictList: 'algorithm/getVehicleProperty',
      // ocrCheckModelList: 'algorithm/ivdg_image_ods_check_model',
    }),
    getWidth() {
      const customLabelWidthIndex = [
        'FACE_FOCUS_URL_AVAILABLE',
        'FACE_URL_AVAILABLE',
        'VEHICLE_ASSET_REGISTER',
        ...this.historyComparisonConfigModule,
      ];
      return customLabelWidthIndex.includes(this.indexType) ? 200 : 155;
    },
  },
  watch: {
    indexType: {
      handler() {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode, // 检测对象
        };
      },
      immediate: true,
    },
    formModel: {
      handler(val) {
        this.schemeList = this.getHour();
        if (val === 'edit') {
          this.formData = {
            ...this.configInfo,
            deviceQueryForm: {
              ...this.formData.deviceQueryForm,
              ...this.configInfo.deviceQueryForm,
            },
          };
        } else {
          const { regionCode, schemeType } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
            detectMode: '1',
            dateRang: [{ hourStart: null, hourEnd: null }],
            quantityConfig: [],
            deviceDetection: {
              dataConfig: {
                key: 'now', //lately 最近
                value: 'month', // 30 10
                quantity: 0,
              },
            },
            visitTimeout: null,
            isUpdatePhyStatus: schemeType === 1 ? 1 : 0,
          };
          if (this.needRuleConfig.includes(this.indexType)) {
            if (this.indexType === 'VEHICLE_ACCURACY') {
              this.$set(this.formData.ruleList, 'ruleList', []);
            } else {
              this.$set(this.formData.deviceDetection, 'ruleList', []);
            }
            this.getCheckRule();
          }
          if (['VEHICLE_ASSET_REGISTER'].includes(this.indexType)) {
            this.formData.source = 'ALL';
            this.formData.registeredRequireData.phyStatus = '';
            this.formData.unregisteredRequireData.isOnline = '';
            this.formData.unregisteredRequireData.phyStatus = '';
          }
        }
        this.setDefaultTime();
      },
      immediate: true,
    },
    // 多目设备检测
    'formData.isMulti': {
      handler(val) {
        if (val === 0) {
          this.formData.multiType = null;
        }
      },
    },
  },
  data() {
    return {
      filterCondition: [
        'VEHICLE_QUANTITY_STANDARD',
        'VEHICLE_CLOCK',
        'VEHICLE_UPLOAD',
        'VEHICLE_UPLOAD_IMPORTANT',
        'VEHICLE_FULL_INFO',
        'VEHICLE_FULL_INFO_IMPORTANT',
        'VEHICLE_INFO_PASS',
        'VEHICLE_INFO_PASS_IMPORTANT',
        'VEHICLE_MAIN_PROP',
        'VEHICLE_TYPE_PROP',
        'VEHICLE_ONLINE_RATE',
      ],
      algorithm: [], //算法
      formData: {
        // ocrAlgorithm: '1', //ocr算法
        algVendors: [], //选择厂商
        snap: null, //检测大图标注抓拍时间和地点
        ocrModel: '',
        // algorithm: '', //选择算法厂商
        timeFormat: '', //时延下拉框
        timeDelay: null, //图片上传时延
        // errorTime: '', //误差时间
        hourStart: null, //时间区间开始时间
        hourEnd: null, //时间区间结束时间
        // timeFrame:'', //时间范围
        imageNum: null, //车辆图片数量
        // captureDay: null, //抓拍天数
        captureNum: null, //抓拍数量
        captureDrop: null, //抓拍数量突降
        isMissPic: 0, //是否以抓拍图片数量判定设备不合格
        lastHours: null, //选中自定义，最近x小时
        orgCodeList: [],
        ruleList: [],
        registeredRequireData: {
          phyStatus: '1',
        },
        unregisteredRequireData: {
          phyStatus: '',
        },
        deviceQueryForm: {
          detectPhyStatus: '0',
          filterOnline: false,
          dayByFilterOnline: 2,
          countByFilterOnline: 1,
          startByFilterOnline: 0,
          endByFilterOnline: 23,
          dayByCapture: 2,
          startByCapture: 0,
          endByCapture: 23,
        },
        isUpdatePhyStatus: 0,
        properties: [],
      },
      ruleCustom: {
        ocrModel: {
          required: true,
          message: '请选择优先算法',
          trigger: 'change',
        },
        properties: {
          type: 'array',
          required: true,
          message: '请选择检测结构化属性',
          trigger: 'change',
        },
      },
      schemeList: [],
      regionalizationSelectVisible: false,
      orgCodes: [],
      areaTreeData: [],
      selectAreaTree: {
        regionCode: '',
      },
      customSearch: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      defaultCheckedList: [],
      customizeAction: {
        title: '新增分析数据字段',
        leftContent: '所有分析数据字段',
        rightContent: '已选择分析数据字段',
        moduleStyle: {
          width: '80%',
        },
      },
      allDeviceFileList: [],
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      contentStyle: {
        height: '3.125rem',
      },
      needRuleConfig: ['VEHICLE_VALID_SUBMIT_QUANTITY', 'VEHICLE_ACCURACY'], // 需要配置规则的指标
      notReportionDeviceList: [
        'VIDEO_PLAYING_ACCURACY',
        'VIDEO_GENERAL_PLAYING_ACCURACY',
        'VIDEO_GENERAL_HISTORY_ACCURACY',
        'VIDEO_HISTORY_ACCURACY',
        'FACE_ONLINE_RATE',
        'VEHICLE_ONLINE_RATE',
      ], // 未报备设备显示属性
      showVehicleProperties: [], // 车辆属性列表
      needPropertiesIndexType: [
        // 需要检测车辆属性的指标
        'VEHICLE_FULL_INFO',
        'VEHICLE_FULL_INFO_IMPORTANT',
        'VEHICLE_INFO_PASS',
        'VEHICLE_INFO_PASS_IMPORTANT',
      ], // 选择的车辆属性
      validateDayByFilterOnline: (rule, value, callback) => {
        let { filterOnline, dayByFilterOnline, startByFilterOnline, endByFilterOnline, countByFilterOnline } =
          this.formData.deviceQueryForm;
        if (
          filterOnline &&
          (!dayByFilterOnline ||
            (!startByFilterOnline && startByFilterOnline !== 0) ||
            (!endByFilterOnline && endByFilterOnline !== 0) ||
            !countByFilterOnline)
        ) {
          callback(new Error('设备有流水参数不能为空'));
        }
        if (filterOnline && startByFilterOnline > endByFilterOnline) {
          callback(new Error('设备有流水开始时间不能大于结束时间'));
        }
        callback();
      },
      validateDayByCapture: (rule, value, callback) => {
        let { dayByCapture, startByCapture, endByCapture } = this.formData.deviceQueryForm;
        if (!dayByCapture || (!startByCapture && startByCapture !== 0) || (!endByCapture && endByCapture !== 0)) {
          callback(new Error('图片抽取范围参数不能为空'));
        }
        if (startByCapture > endByCapture) {
          callback(new Error('图片抽取范围开始时间不能大于结束时间'));
        }
        callback();
      },
      validateTimeDelay: (rule, value, callback) => {
        let { timeDelay } = this.formData;
        if (!timeDelay && timeDelay !== 0) {
          callback(new Error('请输入时间'));
        } else if (timeDelay <= 0) {
          callback(new Error('只允许填写正数'));
        }
        callback();
      },
      validateTimeDelayBefore: (rule, value, callback) => {
        let { beforeTimeDelay } = this.formData;
        if (beforeTimeDelay > 0) {
          callback(new Error('不允许填写正数'));
        }
        callback();
      },
      validateLastHours: (rule, value, callback) => {
        let { timeDelay, lastHours } = this.formData;
        if (timeDelay == '6' && !lastHours) {
          callback(new Error('请输入自定义时间'));
        } else if (timeDelay == '6' && lastHours > 24) {
          callback(new Error('最近时间不能超过24小时'));
        } else {
          callback();
        }
      },
      validateDayByMultiType: (rule, value, callback) => {
        let { isMulti, multiType } = this.formData;
        if (isMulti === 1 && !multiType) {
          callback(new Error('请选择多目设备定义'));
        } else {
          callback();
        }
      },
      historyComparisonConfigModule: historyComparisonConfigModule,
    };
  },

  async created() {
    if (!this.vehiclePropertyDictList?.length) {
      await this.getVehicleProperty();
    }
    // 根据指标设置不同的车辆属性列表
    if (this.needPropertiesIndexType.includes(this.indexType)) {
      this.getShowVehicleProperties();
    }
  },
  methods: {
    ...mapActions({
      getVehicleProperty: 'algorithm/getVehicleProperty',
    }),
    isUpdatePhyStatus(indexType) {
      return isUpdatePhyStatus.filter((item) => item.indexType === indexType);
    },
    quantityQuery(data) {
      this.formData.quantityConfig = data;
    },

    validateForm() {
      let flag = false;
      if (!this.formData.ruleList || !this.formData.ruleList.length) flag = true;
      this.formData.ruleList &&
        this.formData.ruleList.map((item) => {
          if (item.isConfigure == 1) flag = true;
        });
      return flag;
    },
    async getCheckRule() {
      try {
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: 'BASIC_ACCURACY' },
        });
        let rule = data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: item.status,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.formData.deviceDetection.ruleList = rule;
        if (this.indexType == 'VEHICLE_ACCURACY') {
          this.formData.ruleList = rule;
        }
      } catch (err) {
        console.log(err);
      }
    },
    // 获取时间列表
    getHour() {
      let arr = [];
      for (var i = 0; i <= 24; i++) {
        let sum = null;
        if (i < 10) {
          sum = '0' + i + ':00';
        } else {
          sum = i + ':00';
        }
        arr.push({ label: sum, value: i });
      }
      return arr;
    },
    // 添加时间区间
    toAdd() {
      this.formData.dateRang.push({ hourStart: null, hourEnd: null });
    },
    // 删除时间区间
    toDel(index) {
      this.formData.dateRang.splice(index, 1);
    },
    handleChange(time, item) {
      item.planTime = time;
    },
    getDetect() {
      this.formData.deviceQueryForm.detectPhyStatus = '0';
      this.formData.deviceQueryForm.filterOnline = false;
    },
    updateFormData(val) {
      this.formData = {
        ...val,
        deviceQueryForm: {
          detectPhyStatus: this.formData.deviceQueryForm.detectPhyStatus,
          filterOnline: this.formData.deviceQueryForm.filterOnline,
          dayByFilterOnline: this.formData.deviceQueryForm.dayByFilterOnline,
          countByFilterOnline: this.formData.deviceQueryForm.countByFilterOnline,
          startByFilterOnline: this.formData.deviceQueryForm.startByFilterOnline,
          endByFilterOnline: this.formData.deviceQueryForm.endByFilterOnline,
          dayByCapture: this.formData.deviceQueryForm.dayByCapture,
          startByCapture: this.formData.deviceQueryForm.startByCapture,
          endByCapture: this.formData.deviceQueryForm.endByCapture,
          ...val.deviceQueryForm,
        },
      };
    },
    async handleSubmit() {
      if (!this.validateForm()) {
        this.$Message.error('请选择检测规则');
        return;
      }

      this.handleParams();
      const valid = await this.$refs['formData'].validate();
      return valid && this.$refs['commonForm'].handleSubmit();
    },
    setDefaultTime() {
      /**
       * http://192.168.1.111:8088/zentao/task-view-2279.html
       * http://221.226.110.138:65533/zentao/task-view-1752.html
       * 车辆卡口设备及时上传率  默认改为  不填、60分
       * 重点车辆卡口设备及时上传率  默认改为  不填、30分
       * 车辆卡口设备时钟准确率  30s
       */
      if (this.moduleAction.indexType === 'VEHICLE_UPLOAD_IMPORTANT') {
        if (!this.formData.timeDelay && !this.formData.timeFormat) {
          this.formData.timeDelay = 30;
          this.formData.timeFormat = 'm';
        }
        if (!this.formData.beforeTimeDelay && !this.formData.beforeTimeFormat) {
          this.formData.beforeTimeDelay = null;
          this.formData.beforeTimeFormat = 's';
        }
      }
      if (this.moduleAction.indexType === 'VEHICLE_UPLOAD') {
        if (!this.formData.timeDelay && !this.formData.timeFormat) {
          this.formData.timeDelay = 60;
          this.formData.timeFormat = 'm';
        }
        if (!this.formData.beforeTimeDelay && !this.formData.beforeTimeFormat) {
          this.formData.beforeTimeDelay = null;
          this.formData.beforeTimeFormat = 's';
        }
      }
      if (this.moduleAction.indexType === 'VEHICLE_CLOCK' && !this.formData.timeDelay && !this.formData.timeFormat) {
        this.formData.timeDelay = 0;
        this.formData.timeFormat = 's';
      }
      if (this.filterCondition.includes(this.indexType)) {
        this.formData.deviceQueryForm.dayByFilterOnline = this.formData.deviceQueryForm.dayByFilterOnline || null;
        this.formData.deviceQueryForm.countByFilterOnline = this.formData.deviceQueryForm.countByFilterOnline || null;
      }
    },
    //设置默认值
    setDefaultEmphasisData(val) {
      if (val === 'deviceGather') {
        if (
          this.moduleAction.indexType === 'FACE_EMPHASIS_LOCATION' &&
          (!this.formData.emphasisData || this.formData.emphasisData.length === 0)
        ) {
          this.formData.emphasisData = defaultEmphasisData;
        }
      } else {
        this.formData.emphasisData = [];
      }
    },
    handleClose(data, item, index) {
      this.$Modal.confirm({
        title: '警告',
        content: `您要删除${item.value}，是否确认?`,
        onOk: () => {
          data.splice(index, 1);
        },
      });
    },
    async clickAdd(type) {
      this.defaultCheckedList = [];
      try {
        this.defaultCheckedList = (this.formData.emphasisData || []).map((item) => item.key);
        this.customizeAction = {
          title: `新增${type}`,
          leftContent: `所有${type}`,
          rightContent: `已选择${type}`,
          moduleStyle: {
            width: '80%',
          },
        };
        this.customSearch = true;
        let params = {
          tagType: '2',
          isPage: false,
        };
        let {
          data: { data },
        } = await this.$http.post(taganalysis.getDeviceTag, params);
        this.allDeviceFileList = data;
      } catch (error) {
        console.log(error);
      }
    },
    clickArea() {
      try {
        this.areaSelectModalVisible = true;
        let data = this.formData.emphasisData || [];
        this.checkedTreeData = data.map((item) => item.key);
      } catch (e) {
        console.log(e);
      }
    },
    confirmArea(data, dataWithName) {
      this.formData.emphasisData = dataWithName;
    },
    confirmFilter(data) {
      this.customSearch = false;
      let list = [];
      data.map((item) => {
        list.push({
          key: item.tagId,
          value: item.tagName,
        });
      });
      this.formData.emphasisData = list;
    },
    //处理参数
    handleParams() {
      if (this.formData.snap == 0) {
        this.formData.ocrModel = '';
        this.formData.ocrModel2 = '';
      }
      //时间范围自定义则清空dateRang，其他清空lastHours。后端逻辑优先取dateRang字段
      if (this.formData.timeDelay != 6) {
        this.formData.lastHours = null;
      } else {
        this.formData.dateRang = [{ hourStart: null, hourEnd: null }];
      }
    },
    // 车辆数据完整率、准确率可配置检测车辆属性
    getShowVehicleProperties() {
      // (重点)车辆卡口设备过车数据准确率 不显示车辆型号
      if (['VEHICLE_INFO_PASS'].includes(this.indexType)) {
        this.showVehicleProperties = this.vehiclePropertyDictList.filter((item) => item.dataKey !== 'vehicleModel');
      } else {
        this.showVehicleProperties = this.vehiclePropertyDictList;
      }
      if (this.formModel !== 'edit') {
        this.formData.properties = this.showVehicleProperties.map((item) => item.dataKey);
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .lastHours-input .ivu-input-number {
    @{_deep}.ivu-input-number-handler-wrap {
      border-left: 1px solid #10457e;
      border-bottom: 1px solid #10457e;
      a {
        background: #02162b;
        color: var(--color-primary);
        .ivu-input-number-handler-down-inner,
        .ivu-input-number-handler-up-inner {
          color: var(--color-primary);
        }
      }
      .ivu-input-number-handler-down,
      .ivu-input-number-handler-up {
        border-color: #10457e;
      }
    }
  }
}
.vehicle-modal {
  /deep/.select-width,
  .input-width {
    width: 380px;
  }
  .w240 {
    width: 240px;
  }
  .w292 {
    width: 292px;
  }
  .w390 {
    width: 390px;
  }
  .params-pre {
    display: inline-block;
    float: left;
    width: 40px;
    height: 34px;
    line-height: 34px;
    font-size: 16px;
    color: var(--color-primary);
    text-align: center;
    border: 1px solid #10457e;
    opacity: 1;
    border-radius: 4px;
    margin-right: 10px;
  }
  .label-color {
    color: var(--color-tips);
  }
  .color-white {
    color: #fff;
  }
  .color-bule {
    color: #1b82d2 !important;
  }
  .width-picker {
    width: 174px;
  }
  .form-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
  }
  .assessTime {
    /deep/.ivu-form-item-label {
      &::before {
        content: '*';
        display: inline-block;
        margin-right: 0.020833rem;
        line-height: 1;
        font-family: SimSun;
        font-size: 0.072917rem;
        color: #ed4014;
      }
    }
  }
  .inspection {
    width: 100%;
    max-height: 200px;
    overflow-y: auto;
  }
  .capture-vehicle {
    margin-left: 40px;
  }
}
.notCheck {
  color: #56789c;
}
.one-time /deep/ .ivu-form-item-error-tip {
  width: 155px;
}
.lastHours-input .ivu-input-number {
  width: 100px;
  @{_deep}.ivu-input-number-handler-wrap {
    display: inline-block;
  }
}
</style>
