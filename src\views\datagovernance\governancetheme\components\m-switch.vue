<template>
  <i-switch class="iswitch" size="small" v-model="switch1" @on-change="change" />
</template>
<script>
import api from '@/config/api/car-threm.js';
export default {
  props: {
    options: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      switch1: false,
    };
  },
  created() {
    if (this.options.status == '1') {
      this.switch1 = true;
    } else {
      this.switch1 = false;
    }
  },
  methods: {
    change() {
      var param = {
        id: this.options.topicComponentId,
      };
      this.$http
        .put(api.updateStatus, param)
        .then((res) => {
          if (res.data.code == 200) {
            this.$Message.success(res.data.msg);
          } else {
            this.$Message.error(res.data.msg);
          }
        })
        .catch(() => {});
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
