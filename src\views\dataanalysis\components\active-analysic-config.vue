<template>
  <!-- 活跃分析配置 -->
  <div class="config-context">
    <div class="config-title">异常分析配置</div>
    <Form ref="anomalyFrom" label-colon :model="anomalyFormData" :rules="anomalyRuleCustom" :label-width="120">
      <FormItem label="分析内容" prop="content">
        <FormItem prop="lessBox" class="form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.lessBox"
            class="inline"
            @on-change="checkboxChange($event, 'thanMeNum', 'lessThs')"
          >
            抓拍过少
          </Checkbox>
          <div>
            <span>日均抓拍量少于</span>
            <InputNumber
              v-model="anomalyFormData.thanMeNum"
              :min="0"
              :precision="0"
              class="ml-sm mr-sm width-150"
            ></InputNumber>
            <span>张</span>
          </div>
        </FormItem>
        <FormItem prop="nbBox" class="form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.nbBox"
            class="inline"
            @on-change="checkboxChange($event, 'lessMePre', 'lessNbThs')"
          >
            抓拍量显著低于附近设备
          </Checkbox>
          <div>
            <span>日均抓拍量低于附近设备日均抓拍量的</span>
            <InputNumber v-model="anomalyFormData.lessMePre" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
            <span>%</span>
          </div>
        </FormItem>
        <FormItem prop="suddenBox" class="flex-label-item no-bottom form-item-box flex-between">
          <Checkbox
            v-model="anomalyFormData.suddenBox"
            class="inline"
            @on-change="checkboxChange($event, 'thenDayNum,suddenMepre', 'suddenThs')"
          >
            抓拍突降
          </Checkbox>
          <div class="inline flex-between-item">
            <div class="item-1">
              <span>>=</span>
              <InputNumber v-model="anomalyFormData.thenDayNum" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
              <span>天</span>
            </div>
            <div>
              <span>检测当天抓拍数量较历史同一天的平均抓拍量相比，降低</span>
              <InputNumber v-model="anomalyFormData.suddenMepre" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
              <span>%</span>
            </div>
          </div>
        </FormItem>
      </FormItem>
      <FormItem label="抓拍数据范围" prop="staDayNum">
        <div class="form-item-box no-bottom">
          <span>近</span>
          <InputNumber v-model="anomalyFormData.staDayNum" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>天抓拍数量</span>
        </div>
      </FormItem>
    </Form>
    <div class="line mb-lg"></div>
    <div class="config-title">影响分析配置</div>
    <Form ref="influenceFrom" label-colon :model="influenceFormData" :rules="influenceRuleCustom" :label-width="120">
      <FormItem label="影响因素" prop="influencingFactor">
        <div class="flex" v-if="anomalyFormData.lessBox">
          <AddElementItem
            :class="{ 'mb-lg': anomalyFormData.nbBox || anomalyFormData.suddenBox }"
            title="抓拍过少设备"
            :list="influenceFormData.lessThs"
            type-key="ACTIVE_ANA"
            @changeSelectItem="changeSelectItem($event, 'lessThs')"
          ></AddElementItem>
        </div>
        <div class="flex" v-if="anomalyFormData.nbBox">
          <AddElementItem
            :class="{ 'mb-lg': anomalyFormData.suddenBox }"
            title="抓拍量显著低于附近设备"
            :list="influenceFormData.lessNbThs"
            type-key="ACTIVE_ANA"
            @changeSelectItem="changeSelectItem($event, 'lessNbThs')"
          ></AddElementItem>
        </div>
        <div class="flex" v-if="anomalyFormData.suddenBox">
          <AddElementItem
            title="抓拍突降设备"
            :list="influenceFormData.suddenThs"
            type-key="ACTIVE_ANA,ACTIVE_SUDDEN_ANA"
            @changeSelectItem="changeSelectItem($event, 'suddenThs')"
          ></AddElementItem>
        </div>
      </FormItem>
      <FormItem label="异常占比阈值" prop="abThVal">
        <div class="form-item-box no-bottom">
          <span>>=</span>
          <InputNumber v-model="influenceFormData.abThVal" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>%</span>
        </div>
      </FormItem>
      <FormItem label="异常率阈值" prop="abPreval">
        <div class="form-item-box no-bottom">
          <span>>=</span>
          <InputNumber v-model="influenceFormData.abPreval" :min="0" class="ml-sm mr-sm width-150"></InputNumber>
          <span>%</span>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
export default {
  components: {
    AddElementItem: require('./add-element-item').default,
  },
  props: {
    factorConfig: {
      type: Object,
    },
    // add: 新增   edit: 编辑
    entryType: {
      type: String,
      default: 'add',
    },
  },
  data() {
    const validateIsLessBox = (rule, value, callback) => {
      let { lessBox, thanMeNum } = this.anomalyFormData;
      if (lessBox && !thanMeNum) {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    const validateIsNbBox = (rule, value, callback) => {
      let { nbBox, lessMePre } = this.anomalyFormData;
      if (nbBox && !lessMePre) {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    const validateIsSuddenBox = (rule, value, callback) => {
      let { suddenBox, thenDayNum, suddenMepre } = this.anomalyFormData;
      if (suddenBox && (!thenDayNum || !suddenMepre)) {
        callback(new Error('请输入'));
      } else {
        callback();
      }
    };
    const validateContent = (rule, value, callback) => {
      let { lessBox, nbBox, suddenBox } = this.anomalyFormData;
      if (!lessBox && !nbBox && !suddenBox) {
        callback(new Error('请选择分析内容'));
      } else {
        callback();
      }
    };
    const validateInfluencingFactor = (rule, value, callback) => {
      let { lessBox, nbBox, suddenBox } = this.anomalyFormData;
      let { lessThs, lessNbThs, suddenThs } = this.influenceFormData;
      if (
        (lessBox && lessThs.length === 0) ||
        (nbBox && lessNbThs.length === 0) ||
        (suddenBox && suddenThs.length === 0)
      ) {
        callback(new Error('请添加影响因素'));
      } else {
        callback();
      }
    };

    return {
      // 异常分析配置
      anomalyFormData: {
        lessBox: false,
        thanMeNum: null,
        nbBox: false,
        lessMePre: null,
        suddenBox: false,
        thenDayNum: null,
        suddenMepre: null,
        // 抓拍数据范围
        staDayNum: null,
      },
      anomalyRuleCustom: {
        lessBox: [
          {
            validator: validateIsLessBox,
            trigger: 'change',
          },
        ],
        nbBox: [
          {
            validator: validateIsNbBox,
            trigger: 'change',
          },
        ],
        suddenBox: [
          {
            validator: validateIsSuddenBox,
            trigger: 'change',
          },
        ],
        content: [
          {
            required: true,
            validator: validateContent,
            trigger: 'change',
          },
        ],
        staDayNum: [
          {
            required: true,
            message: '请输入抓拍数据范围',
            type: 'number',
            trigger: 'blur',
          },
        ],
      },

      // 影响分析配置
      influenceFormData: {
        lessThs: [],
        lessNbThs: [],
        suddenThs: [],
        abThVal: null,
        abPreval: null,
      },
      influenceRuleCustom: {
        influencingFactor: [
          {
            required: true,
            validator: validateInfluencingFactor,
            trigger: 'change',
          },
        ],
        abThVal: [
          {
            required: true,
            message: '请输入异常占比阈值',
            type: 'number',
            trigger: 'blur',
          },
        ],
        abPreval: [
          {
            required: true,
            message: '请输入异常占比阈值',
            type: 'number',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  computed: {
    formData() {
      return {
        anomalyFormData: this.anomalyFormData,
        influenceFormData: this.influenceFormData,
      };
    },
  },
  watch: {
    formData(val) {
      this.$emit('changeFormData', val);
    },
    factorConfig: {
      handler(val) {
        if (this.entryType === 'edit' && val) {
          this.anomalyFormData = this.$util.common.deepCopy(val.abnormalConfig);
          this.influenceFormData = this.$util.common.deepCopy(val.influenceConfig);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 异常分析配置
    checkboxChange(val, str = '', influencingVal = '') {
      if (!val) {
        let keyArr = str.split(',');
        keyArr.forEach((key) => {
          this.anomalyFormData[key] = null;
        });

        // 对应的 影响因素 也清空
        this.influenceFormData[influencingVal] = [];
      }
      this.$refs.anomalyFrom.validateField('content');
    },
    // 影响配置
    changeSelectItem(data, str = '') {
      this.influenceFormData[str] = data;
      this.$nextTick(() => {
        this.$refs.influenceFrom.validateField('influencingFactor');
      });
    },
    getFormData() {
      return this.formData;
    },
    async handleSubmit() {
      let anomalyValid = await this.$refs.anomalyFrom.validate();
      let influenceValid = await this.$refs.influenceFrom.validate();
      return {
        valid: anomalyValid && influenceValid,
        formData: this.formData,
      };
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
}
.width-150 {
  width: 150px;
}
@{_deep} .ivu-checkbox-wrapper {
  color: var(--color-content);
  .ivu-checkbox {
    margin-right: 0;
  }
}
@{_deep} .flex-label-item .ivu-form-item-content {
  .flex;
}
.config-context {
  padding: 32px 71px 0 71px;
  .config-title {
    color: var(--color-sub-title-inpage);
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 15px;
  }
  .line {
    width: 100%;
    height: 0;
    border: 1px dashed var(--border-color);
  }
  @{_deep} .flex-between .ivu-form-item-content {
    display: flex;
    justify-content: space-between;
  }
  .flex-between-item {
    text-align: right;
    .item-1 {
      margin-bottom: 16px;
    }
  }
  @{_deep} .ivu-form-item-label {
    padding-top: 18px;
  }
  @{_deep}.form-item-box {
    margin-bottom: 20px !important;
    background: var(--bg-form-item);
    padding: 8px 20px;
    color: var(--color-content);
    &.no-bottom {
      margin-bottom: 0 !important;
    }
  }
  .ivu-form-item {
    margin-bottom: 20px;
  }
}
</style>
