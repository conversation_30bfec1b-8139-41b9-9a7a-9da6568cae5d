<template>
  <div>
    <ui-modal class="details" v-model="visible" title="设备明细" :styles="styles" footer-hide @onCancel="cancel">
      <div v-ui-loading="{ loading: optionLoading }" class="content">
        <div>
          <ui-label class="inline mr-md" label="组织机构">
            <api-organization-tree
              ref="apiOrgTree"
              :custorm-node="true"
              :custorm-node-data="custormNodeData"
              :select-tree="selectOrgTree"
              @selectedTree="selectedOrgTree"
              placeholder="请选择组织机构"
            >
            </api-organization-tree>
          </ui-label>
          <ui-label class="inline mr-md" label="行政区划">
            <api-area-tree
              ref="apiAreaTree"
              :select-tree="selectTree"
              @selectedTree="selectedArea"
              placeholder="请选择行政区划"
            ></api-area-tree>
          </ui-label>
          <ui-label class="inline mb-sm mr-md" :label="`${global.filedEnum.deviceId}`">
            <Input v-model="searchData.deviceId" class="width-md" placeholder="请输入设备编码"></Input>
          </ui-label>
          <ui-label class="inline mb-sm mr-md" :label="`${global.filedEnum.deviceName}`">
            <Input v-model="searchData.deviceName" class="width-md" placeholder="请输入设备名称"></Input>
          </ui-label>
          <!-- 摄像机功能类型 -->
          <ui-label class="inline mb-sm mr-md" :label="global.filedEnum.sbgnlx">
            <Select
              class="width-md"
              v-model="searchData.sbgnlx"
              :placeholder="`请选择${global.filedEnum.sbgnlx}`"
              clearable
            >
              <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
                >{{ item.dataValue }}
              </Option>
            </Select>
          </ui-label>
          <ui-label label="上报状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.cascadeReportStatus" placeholder="请选择上报状态" clearable>
              <Option value="0">未上报</Option>
              <Option value="1">已上报</Option>
            </Select>
          </ui-label>
          <ui-label label="设备状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.phyStatus" placeholder="请选择设备状态" clearable>
              <Option v-for="(item, index) in phyList" :key="index" :label="item.label" :value="`${item.value}`">
              </Option>
            </Select>
          </ui-label>
          <ui-label label="质量情况" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.qualityStatus" placeholder="请选择质量情况" clearable>
              <Option value="2">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <ui-label label="填报状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.fillStatus" placeholder="请选择填报状态" clearable>
              <Option value="0">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <ui-label label="时钟状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.clockStatus" placeholder="请选择时钟状态" clearable>
              <Option value="0">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <ui-label label="视频流数据状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.videoStatus" placeholder="请选择视频流数据状态" clearable>
              <Option value="0">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <ui-label label="人脸数据状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.faceStatus" placeholder="请选择人脸数据状态" clearable>
              <Option value="0">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <ui-label label="车辆数据状态" class="inline mb-sm mr-md">
            <Select class="width-md" v-model="searchData.vehicleAccuracy" placeholder="请选择车辆数据状态" clearable>
              <Option value="0">不合格</Option>
              <Option value="1">合格</Option>
            </Select>
          </ui-label>
          <!-- 摄像机采集区域 -->
          <ui-label class="inline mb-sm mr-md" :label="`${global.filedEnum.sbcjqy}`">
            <Button type="dashed" class="width-md" @click="clickArea">
              {{ `已选择 ${searchData.sbcjqyList.length}个` }}
            </Button>
          </ui-label>
          <div class="inline">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="reset">重置</Button>
          </div>
        </div>
        <div class="btn-div mb-sm">
          <Checkbox v-model="checkAll" @on-change="handleAllCheck">全选</Checkbox>
          <div>
            <Button class="mr-sm" type="primary" @click="machineSetAvailable(2)">
              <i class="icon-font icon-benjisheweibukeyong"></i>
              <span class="inline vt-middle ml-xs">本级设为不可用</span>
            </Button>
            <Button class="mr-sm" type="primary" @click="machineSetAvailable(1)">
              <i class="icon-font icon-benjisheweikeyong"></i>
              <span class="inline vt-middle ml-xs">本级设为可用</span>
            </Button>
            <Button class="mr-sm" type="primary" @click="setAvailableReport(2)">
              <i class="icon-font icon-sheweibukeyongshangbao"></i>
              <span class="inline vt-middle ml-xs">设为不可用上报</span>
            </Button>
            <Button class="mr-sm" type="primary" @click="setAvailableReport(1)">
              <i class="icon-font icon-sheweikeyongshangbao"></i>
              <span class="inline vt-middle ml-xs">设为可用上报</span>
            </Button>
            <Button type="primary" @click="setAvailableReport(undefined)">
              <i class="icon-font icon-piliangshangbao"></i>
              <span class="inline vt-middle ml-xs">批量上报</span>
            </Button>
          </div>
        </div>
        <ui-table
          class="ui-table auto-fill"
          reserve-selection
          :table-columns="tableColumns"
          :table-data="tableData"
          :loading="loading"
          :is-all="checkAll"
          :default-store-data="defaultStoreData"
          @storeSelectList="storeSelectList"
        >
          <template #deviceId="{ row }">
            <span>{{ row.deviceId }}</span>
          </template>
          <template #deviceName="{ row }">
            <Tooltip :content="row.deviceName" :disabled="!row.deviceName">
              <div class="width-md inline ellipsis">
                {{ row.deviceName }}
              </div>
            </Tooltip>
          </template>
          <template #orgName="{ row }">
            <span>{{ row.orgName }}</span>
          </template>
          <template #civilName="{ row }">
            <span>{{ row.civilName }}</span>
          </template>
          <!-- 经纬度保留8位小数-->
          <template #longitude="{ row }">
            <span>{{ row.longitude | filterLngLat }}</span>
          </template>
          <template #latitude="{ row }">
            <span>{{ row.latitude | filterLngLat }}</span>
          </template>
          <template #ipAddr="{ row }">
            <span>{{ row.ipAddr }}</span>
          </template>
          <template #macAddr="{ row }">
            <span>{{ row.macAddr }}</span>
          </template>
          <template #sbgnlx="{ row }">
            <Tooltip :content="row.sbgnlxText" :disabled="!row.sbgnlxText">
              <div class="width-xs inline ellipsis">
                {{ row.sbgnlxText }}
              </div>
            </Tooltip>
          </template>
          <template #sbdwlx="{ row }">
            <span>{{ row.sbdwlxText }}</span>
          </template>
          <template #sbcjqy="{ row }">
            <Tooltip :content="row.sbcjqyText" :disabled="!row.sbcjqyText">
              <div class="width-xs inline ellipsis">
                {{ row.sbcjqyText }}
              </div>
            </Tooltip>
          </template>
          <template #cascadeReportStatus="{ row }">
            <span
              :style="{
                color: row.cascadeReportStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
              }"
              >{{ row.cascadeReportStatus === '0' ? '未上报' : '已上报' }}</span
            >
          </template>
          <template #phyStatus="{ row }">
            <span
              :style="{
                color: row.phyStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
              }"
              >{{ row.phyStatusText }}</span
            >
          </template>
          <template #qualityStatus="{ row }">
            <span
              :style="{
                color: row.qualityStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
              }"
              >{{ row.qualityStatus === '1' ? '合格' : '不合格' }}</span
            >
          </template>
          <template #action="{ row }">
            <div>
              <ui-btn-tip icon="icon-bianji2" content="编辑" @handleClick="edit(row)"></ui-btn-tip>
            </div>
          </template>
        </ui-table>
        <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>
    </ui-modal>
    <area-select v-model="areaSelectModalVisible" @confirm="confirmArea" :checked-tree-data-list="checkedTreeData">
    </area-select>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  props: {
    value: {
      type: Boolean,
    },
    detailData: {
      type: Object,
    },
  },
  data() {
    return {
      visible: false,
      loading: false,
      optionLoading: false,
      styles: {
        width: '9.4rem',
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      selectOrgTree: {
        orgCode: null,
      },
      selectTree: {
        regionCode: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      checkAll: false,
      areaSelectModalVisible: false,
      checkedTreeData: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        orgCode: '',
        civilCode: '',
        sbgnlx: '',
        cascadeReportStatus: '',
        phyStatus: '',
        qualityStatus: '',
        fillStatus: '',
        clockStatus: '',
        videoStatus: '',
        faceStatus: '',
        vehicleAccuracy: '',
        sbcjqyList: [],
        pageNumber: 1,
        pageSize: 20,
      },
      phyList: [
        { label: '在用', value: '1' },
        { label: '维修', value: '2' },
        { label: '拆除', value: '3' },
      ],
      tableColumns: [
        { type: 'selection', fixed: 'left', align: 'center', width: 50 },
        {
          title: '序号',
          type: 'index',
          fixed: 'left',
          align: 'center',
          width: 50,
        },
        {
          title: this.global.filedEnum.deviceId,
          slot: 'deviceId',
          fixed: 'left',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.deviceName,
          slot: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          slot: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          slot: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          slot: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          slot: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
          tooltip: true,
        },
        {
          minWidth: 130,
          title: `${this.global.filedEnum.sbcjqy}`,
          slot: 'sbcjqy',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `上报状态`,
          slot: 'cascadeReportStatus',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        {
          minWidth: 100,
          title: `质量情况`,
          slot: 'qualityStatus',
          tooltip: true,
        },
        {
          width: 70,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      chooseTableData: [],
      defaultStoreData: [],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
  },
  methods: {
    // 全选
    handleAllCheck() {
      this.chooseTableData = [];
      this.tableData = this.tableData.map((item) => {
        this.$set(item, '_checked', this.checkAll);
        this.$set(item, '_disabled', this.checkAll);
        return item;
      });
    },
    clickArea() {
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.searchData.sbcjqyList || [];
    },
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    async init() {
      try {
        this.loading = true;
        const res = await this.$http.post(equipmentassets.qualityManageList, this.searchData);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.pageData.pageNum = 1;
      this.searchData.pageNumber = 1;
      this.init();
    },
    reset() {
      this.selectTree.regionCode = null;
      this.selectOrgTree.orgCode = null;
      this.resetSearchDataMx(this.searchData, this.search);
    },
    cancel() {
      this.selectTree.regionCode = null;
      this.selectOrgTree.orgCode = null;
      this.chooseTableData = [];
      this.defaultStoreData = [];
      this.resetSearchDataMx(this.searchData);
    },
    storeSelectList(selection) {
      this.chooseTableData = selection;
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    edit(row) {
      this.$emit('edit', row);
    },
    machineSetAvailable(status) {
      this.$UiConfirm({
        content: `您将要设置本级为${status === 1 ? '可用' : '不可用'}，是否确认?`,
        title: '警告',
      }).then(async () => {
        try {
          this.optionLoading = true;
          let params = {
            ids: this.chooseTableData.map((row) => row.id),
            updatePhyStatus: status,
          };
          this.chooseTableData.length === 0 && Object.assign(params, this.searchData);
          const res = await this.$http.post(equipmentassets.updateDevicePhyStatus, params);
          this.$Message.success(res.data.msg);
          this.init();
        } catch (err) {
          console.log(err);
        } finally {
          this.optionLoading = false;
        }
      });
    },
    setAvailableReport(status) {
      this.$UiConfirm({
        content: `您将要${status === 1 ? '设为可用' : status === 2 ? '设为不可用' : '批量'}上报，是否确认?`,
        title: '警告',
      }).then(async () => {
        try {
          this.optionLoading = true;
          let params = {
            ids: this.chooseTableData.map((row) => row.id),
            type: status,
          };
          this.chooseTableData.length === 0 && Object.assign(params, this.searchData);
          const res = await this.$http.post(equipmentassets.qualityReportBatch, params);
          this.$Message.success(res.data.msg);
          this.init();
        } catch (err) {
          console.log(err);
        } finally {
          this.optionLoading = false;
        }
      });
    },
  },
  watch: {
    value(val) {
      if (val) {
        Object.assign(this.searchData, this.detailData);
        this.selectTree.regionCode = this.detailData?.civilCode;
        this.init();
      }
      this.visible = val;
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {
    ...mapGetters({
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 点位类型
    }),
  },
  components: {
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    AreaSelect: require('@/components/area-select').default,
  },
};
</script>
<style lang="less" scoped>
.details {
  @{_deep} .ivu-modal-body {
    height: 830px;
    display: flex;
    flex-direction: column;
  }
  .content {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .btn-div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  @{_deep} .ivu-tooltip-inner {
    max-width: fit-content;
  }
}
</style>
