<template>
  <div class="problem-solving" v-ui-loading="{ loading: tableLoading }">
    <ui-table :table-columns="columns" :table-data="tableData" :stripe="false" :disabledHover="true" :max-height="500">
      <template slot="context" slot-scope="{ row }">
        <div v-html="row.context"></div>
      </template>
      <template slot="action" slot-scope="{ row }">
        <create-tabs
          :componentName="resultData.componentName"
          :tabs-text="resultData.text"
          @selectModule="selectModule"
          class="inline"
          :tabs-query="{
            displayType: statisticType,
            indexId: row.indexId,
            code: row.regionCode,
            batchId: row.batchId,
            uuid: uuid,
          }"
        >
          <ui-btn-tip
            class="operatbtn"
            icon="icon-chakanxiangqing"
            :styles="{ color: '#438CFF' }"
            content="查看详情"
          ></ui-btn-tip>
        </create-tabs>
      </template>
    </ui-table>
  </div>
</template>
<script>
import evaluationreport from '@/config/api/evaluationreport';
export default {
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
  props: {
    taskType: {
      type: Number,
      default: 1,
    },
    uuid: {
      type: String,
      default: '',
    },
    statisticType: {
      type: String,
    },
  },
  data() {
    return {
      componentName: null,
      resultData: {
        componentName: 'overviewEvaluation', // 需要跳转的组件名
        text: '评测详情', // 跳转页面标题
        title: '评测详情',
        type: 'view',
        // title: "视图基础数据治理主题",
      },
      tableData: [],
      columns: [
        {
          title: '序号',
          type: 'index',
          align: 'center',
          width: 76,
        },
        {
          title: '单位',
          key: 'city',
          align: 'left',
          width: 150,
          tooltip: true,
        },
        {
          title: '主要问题',
          slot: 'context',
          align: 'left',
          tooltip: true,
        },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          className: 'table-action-padding', // 操作栏列-单元格padding设置
          width: 80,
        },
      ],
      tableLoading: false,
    };
  },
  computed: {},
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    init(list) {
      this.tableLoading = true;
      this.tableData = [];
      this.$http
        .post(evaluationreport.getProblemOverview, {
          indexModule: list.taskType,
          batchIds: list.batchIds,
          rootResultIds: list.rootResultIds,
          dataType: list.targetDataType,
          selfRegionCode: this.statisticType === 'ORG' ? list.selfOrgCode : list.selfRegionCode,
          displayType: this.statisticType,
        })
        .then((res) => {
          this.tableData = res.data.data;
        })
        .catch(() => {})
        .finally(() => {
          this.tableLoading = false;
        });
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = name;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.problem-solving {
  width: 100%;
  .ui-table {
    margin-top: 10px;
    width: 100%;
    @{_deep} .ivu-table-wrapper {
      min-height: 200px !important;
    }
    @{_deep} .ivu-table-tip {
      min-height: 200px !important;
    }
  }
}
</style>
