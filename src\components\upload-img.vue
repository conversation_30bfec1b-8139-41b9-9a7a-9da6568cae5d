<template>
  <div class="over-flow upload-img">
    <div v-for="(item, index) in successData" :key="index" class="upload-item">
      <div class="upload-list">
        <ui-image :src="item"></ui-image>
        <div class="upload-list-cover">
          <Icon
            type="ios-trash-outline"
            @click.native="handleRemove(index, $event)"
          ></Icon>
        </div>
      </div>
    </div>
    <Upload
      v-if="successData.length < multipleNum"
      ref="upload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :format="['jpg', 'jpeg', 'png']"
      :max-size="2048"
      :on-format-error="handleFormatError"
      :on-exceeded-size="handleMaxSize"
      :before-upload="handleBeforeUpload"
      :show-upload-list="false"
      :headers="{ 'Authorization': 'Bearer ' + $store.state.user.token }"
      type="drag"
      name="file"
      class="upload-item"
      action="/qsdi-system-service/file/upload"
    >
      <div v-if="!loading" class="upload-label card-bg">
        <Icon class="color-info" type="ios-add" />
        <span class="upload-text f-12 label-color">点击上传图片</span>
      </div>
      <div v-else class="loader-box">
        <ui-loading></ui-loading>
        <!-- <div class="loader">
          <svg
            id="Layer_1"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="24px"
            height="30px"
            viewBox="0 0 24 30"
            style="enable-background: new 0 0 50 50"
            xml:space="preserve"
          >
            <rect x="0" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
            <rect x="10" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0.15s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0.15s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
            <rect x="20" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0.3s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0.3s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
          </svg>
        </div> -->
      </div>
    </Upload>
  </div>
</template>
<script>
export default {
  name: 'UploadImg'
}
</script>
<style lang="less" scoped>
.upload-img {
  display: flex;
  justify-content: center;
}
.ivu-icon-ios-add {
  font-size: 40px;
  font-weight: bold;
}
.upload-item {
  width: 120px;
  height: 120px;
  margin-left: 5px;
  position: relative;
  &:first-child {
    margin-left: 0;
  }
  /deep/ .ivu-upload {
    height: 100%;
    width: 100%;
    &-drag {
      border-radius: 0;
    }
  }
}

.upload-label {
  width: 100%;
  height: 100%;
  border: solid 1px transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 0 !important;
}

.upload-list {
  width: 100%;
  height: 100%;
  line-height: 1;
  img {
    width: 100%;
    height: 100%;
  }
}
.upload-text {
  font-size: 14px;
}

.upload-icon {
  display: inline-block;
  width: 48px;
  height: 40px;
}

.upload-list:hover .upload-list-cover {
  display: flex;
}

.upload-list-cover {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 11;
}

.upload-list-cover i {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

.loader-box {
  height: 100%;
}

.loader {
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 1em;
  display: inline-block;
  vertical-align: top;
  z-index: 5;
  svg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>
<script>
export default {
  components: {},
  props: {
    // 最大上传图片张数
    multipleNum: {
      type: Number,
      default: () => {
        return 3
      }
    },
    // 默认已经上传的照片
    defaultList: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data () {
    return {
      successData: [],
      loading: false,
      // headers: {
      //   Authorization: `Bearer ${lo.token}`
      // }
    }
  },
  computed: {},
  watch: {
    defaultList (val) {
      this.successData = this.defaultList
    }
  },
  created () { },
  mounted () {
    this.successData = this.defaultList
  },
  methods: {
    handleBeforeUpload (file) {
      this.loading = true
    },
    handleError () {
      this.loading = false
      this.$Message.error('上传图片失败')
    },
    handleFormatError () {
      this.loading = false
      this.$Message.error('请上传以下文件类型:jpg、jpeg、png')
    },
    handleMaxSize () {
      this.loading = false
      this.$Message.error('上传图片大于2M请选择合适的图片')
    },
    handleSuccess (val) {
      if (val.code === 200) {
        this.successData.push(val.data.fileUrl)
        this.loading = false
        this.$emit('successPut', this.successData)
      } else {
        this.$Message.error('上传图片失败')
      }
    },
    handleRemove (index, even) {
      even.stopPropagation()
      this.successData.splice(index, 1)
      this.$emit('successPut', this.successData)
    },
    clear () {
      this.successData = []
    }
  }
}
</script>
