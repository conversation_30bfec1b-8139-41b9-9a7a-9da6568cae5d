<template>
  <div class="over-flow upload-img">
    <div class="upload-div" v-for="(item, index) in successData" :key="index">
      <div class="upload-item">
        <div class="upload-list">
          <ui-image :src="item"></ui-image>
          <div class="upload-list-cover">
            <Icon type="ios-trash-outline" @click.native="handleRemove(index, $event)"></Icon>
          </div>
        </div>
      </div>
      <p class="base-text-color width-xs ellipsis" :title="fileNameData[index]">
        {{ fileNameData[index] }}
      </p>
    </div>
    <Upload
      ref="upload"
      v-if="successData.length < multipleNum || multiple"
      :on-success="handleSuccess"
      :on-error="handleError"
      :format="['jpg', 'jpeg', 'png']"
      :max-size="2048"
      :on-format-error="handleFormatError"
      :on-exceeded-size="handleMaxSize"
      :before-upload="handleBeforeUpload"
      :show-upload-list="false"
      :headers="headers"
      :multiple="multiple"
      type="drag"
      name="file"
      class="upload-item"
      action="/qsdi-system-service/file/upload"
    >
      <div class="upload-label" v-if="!loading">
        <Icon type="ios-add" />
        <span class="upload-text f-12">点击上传图片</span>
      </div>
      <div class="loader-box" v-else>
        <div class="loader">
          <svg
            version="1.1"
            id="Layer_1"
            xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink"
            x="0px"
            y="0px"
            width="24px"
            height="30px"
            viewBox="0 0 24 30"
            style="enable-background: new 0 0 50 50"
            xml:space="preserve"
          >
            <rect x="0" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
            <rect x="10" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0.15s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0.15s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
            <rect x="20" y="13" width="4" height="5" fill="#ff6700">
              <animate
                attributeName="height"
                attributeType="XML"
                values="5;21;5"
                begin="0.3s"
                dur="0.6s"
                repeatCount="indefinite"
              />
              <animate
                attributeName="y"
                attributeType="XML"
                values="13; 5; 13"
                begin="0.3s"
                dur="0.6s"
                repeatCount="indefinite"
              />
            </rect>
          </svg>
        </div>
      </div>
    </Upload>
  </div>
</template>
<style lang="less" scoped>
[data-theme='dark'] {
  .upload-label {
    background-color: #022143;
    border: 1px dotted #1b82d2;
  }
  .upload-text {
    color: #56789c;
  }
  .upload-list,
  .loader-box {
    background-color: #022143;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .upload-label {
    background-color: rgba(44, 134, 248, 0.1028);
    border: 1px dotted var(--color-primary);
  }
  .upload-text {
    color: var(--color-primary);
  }
  .upload-list,
  .loader-box {
    background-color: rgba(44, 134, 248, 0.1028);
  }
}
.upload-img {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.ivu-icon-ios-add {
  font-size: 30px;
  color: var(--color-primary);
}
.upload-item {
  width: 120px;
  height: 120px;
  margin-left: 5px;
  position: relative;
  &:first-child {
    margin-left: 0;
  }
  @{_deep} .ivu-upload {
    height: 100%;
    width: 100%;
    &-drag {
      border-radius: 0;
    }
  }
}

.upload-label {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.upload-list {
  width: 100%;
  height: 100%;
  line-height: 1;
  img {
    width: 100%;
    height: 100%;
  }
}
.upload-text {
  font-size: 12px;
}

.upload-icon {
  display: inline-block;
  width: 48px;
  height: 40px;
}

.upload-list:hover .upload-list-cover {
  display: flex;
}

.upload-list-cover {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
  background: rgba(0, 0, 0, 0.6);
  z-index: 11;
}

.upload-list-cover i {
  color: #fff;
  font-size: 24px;
  cursor: pointer;
}

.loader-box {
  height: 100%;
}

.loader {
  height: 100%;
  width: 100%;
  text-align: center;
  padding: 1em;
  display: inline-block;
  vertical-align: top;
  z-index: 5;
  svg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}
</style>
<script>
export default {
  data() {
    return {
      successData: [],
      fileNameData: [],
      loading: false,
      headers: {
        Authorization: `Bearer ${sessionStorage.token}`,
      },
    };
  },
  created() {},
  mounted() {
    this.successData = this.defaultList;
  },
  methods: {
    handleBeforeUpload() {
      this.loading = true;
    },
    handleError() {
      this.loading = false;
      this.$Message.error('上传图片失败');
    },
    handleFormatError() {
      this.loading = false;
      this.$Message.error('请上传以下文件类型:jpg、jpeg、png');
    },
    handleMaxSize() {
      this.loading = false;
      this.$Message.error('上传图片大于2M请选择合适的图片');
    },
    handleSuccess(val) {
      if (val.code === 200) {
        this.successData.push(val.data.fileUrl);
        this.fileNameData.push(val.data.originalFilename);
        this.loading = false;
        this.$emit('successPut', this.successData, val.data);
      } else {
        this.$Message.error('上传图片失败');
      }
    },
    handleRemove(index, even) {
      even.stopPropagation();
      this.successData.splice(index, 1);
      this.fileNameData.splice(index, 1);
      this.$emit('successPut', this.successData, this.fileNameData);
    },
    clear() {
      this.successData = [];
      this.fileNameData = [];
    },
  },
  watch: {
    defaultList() {
      this.successData = this.defaultList;
    },
  },
  computed: {},
  props: {
    // 最大上传图片张数
    multipleNum: {
      type: Number,
      default: () => {
        return 3;
      },
    },
    // 默认已经上传的照片
    defaultList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  components: {},
};
</script>
