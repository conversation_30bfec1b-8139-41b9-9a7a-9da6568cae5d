<template>
  <div class="custom-select-wrap">
    <div class="camera fl" @click="selectCamera">
      <span class="font-blue camera-text" v-if="totalCount">已选择{{ totalCount }}条设备</span>
      <span v-else>
        <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
        <span class="font-blue camera-text">请选择设备</span>
      </span>
    </div>
    <choose-device
      ref="chooseDevice"
      class="choose-device"
      key="chooseDevice"
      :search-conditions="parameters"
      :table-columns="columns"
      :load-data="tableData"
      :selected-list="ids"
      :check-device-flag="parameters.checkDeviceFlag"
      :filter-fun="filterFun"
      :task-index-config="taskIndexConfig"
      :region-code="regionCode"
      need-other-search
      need-default-search-tag
      @getOrgCode="getOrgCode"
      @getDeviceIdList="getDeviceIdList"
    >
      <template #search-header>
        <ui-label class="inline right-margin mb-lg" label="设备编码">
          <Input
            v-model="searchData.deviceId"
            class="input-width"
            placeholder="请输入设备编码"
            @on-blur="changeBlur"
            clearable
          ></Input>
        </ui-label>
        <ui-label class="inline right-margin mb-lg" label="设备名称">
          <Input
            v-model="searchData.deviceName"
            class="input-width"
            placeholder="请输入设备名称"
            clearable
            @on-blur="changeBlur"
          ></Input>
        </ui-label>
        <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbdwlx">
          <Select
            class="input-width"
            v-model="searchData.sbdwlxList"
            placeholder="请选择"
            clearable
            multiple
            :max-tag-count="1"
            @on-change="changeBlur"
          >
            <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.sbgnlx">
          <Select
            class="input-width"
            v-model="searchData.sbgnlxList"
            placeholder="请选择"
            clearable
            multiple
            :disabled="moduleAction.indexModule !== '1'"
            :max-tag-count="1"
            @on-change="changeBlur"
          >
            <Option v-for="(item, index) in propertySearchLbgnlx" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <!-- 设备状态 -->
        <ui-label class="inline right-margin mb-lg" :label="global.filedEnum.phyStatus">
          <Select
            class="input-width"
            v-model="searchData.phyStatus"
            :placeholder="`请选择${global.filedEnum.phyStatus}`"
            clearable
            @on-change="changeBlur"
          >
            <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline right-margin mb-lg" label="上报状态">
          <Select
            class="input-width"
            v-model="searchData.cascadeReportStatus"
            placeholder="请选择上报状态"
            clearable
            @on-change="changeBlur"
          >
            <Option key="0" value="0">未上报</Option>
            <Option key="1" value="1">已上报</Option>
          </Select>
        </ui-label>
        <ui-label class="inline right-margin mb-lg" label="是否检测">
          <Select
            class="width-mini"
            v-model="searchData.isCheck"
            placeholder="请选择"
            clearable
            @on-change="changeBlur('isCheck')"
          >
            <Option value="">请选择 </Option>
            <Option value="1">是 </Option>
            <Option value="0">否 </Option>
          </Select>
        </ui-label>
        <!-- 基础信息状态 -->
        <ui-label class="inline right-margin mb-lg" label="基础信息状态">
          <Select
            class="width-md left-select"
            v-model="searchData.baseCheckStatus"
            placeholder="请选择是否合格"
            clearable
            :disabled="searchData.isCheck !== '1'"
            @on-change="changeBlur('base')"
          >
            <Option value="">请选择 </Option>
            <Option value="0">合格</Option>
            <Option value="1">不合格</Option>
          </Select>
          <span class="split">-</span>
          <Select
            class="width-md right-select multiple-select"
            v-model="searchData.baseErrorMessageList"
            placeholder="请选择错误类别"
            clearable
            multiple
            :disabled="searchData.baseCheckStatus !== '1'"
            :max-tag-count="1"
            @on-change="changeBlur"
          >
            <Option :value="item" v-for="(item, index) in errorMessageMap.basic" :key="item + index">{{ item }}</Option>
          </Select>
        </ui-label>
        <!-- 视图数据状态 -->
        <ui-label class="inline right-margin mb-lg" label="视图数据状态">
          <Select
            class="width-md left-select"
            v-model="searchData.viewCheckStatus"
            placeholder="请选择是否合格"
            clearable
            :disabled="searchData.isCheck !== '1'"
            @on-change="changeBlur('view')"
          >
            <Option value="">请选择 </Option>
            <Option value="0">合格</Option>
            <Option value="1">不合格</Option>
          </Select>
          <span class="split">-</span>
          <Select
            class="width-md right-select multiple-select"
            v-model="searchData.viewErrorMessageList"
            placeholder="请选择错误类别"
            multiple
            clearable
            :disabled="searchData.viewCheckStatus != '1'"
            :max-tag-count="1"
            @on-change="changeBlur"
          >
            <Option :value="item" v-for="(item, index) in errorMessageMap.view" :key="item + index">{{ item }}</Option>
          </Select>
        </ui-label>
        <!-- 视频流数据状态 -->
        <ui-label class="inline right-margin mb-lg" label="视频流数据状态">
          <Select
            class="width-md left-select"
            v-model="searchData.videoCheckStatus"
            placeholder="请选择是否合格"
            clearable
            :disabled="searchData.isCheck !== '1'"
            @on-change="changeBlur('video')"
          >
            <Option value="">请选择 </Option>
            <Option value="0">合格</Option>
            <Option value="1">不合格</Option>
          </Select>
          <span class="split">-</span>
          <Select
            class="width-md right-select multiple-select"
            v-model="searchData.videoErrorMessageList"
            placeholder="请选择错误类别"
            multiple
            clearable
            :disabled="searchData.videoCheckStatus != '1'"
            :max-tag-count="1"
            @on-change="changeBlur"
          >
            <Option :value="item" v-for="(item, index) in errorMessageMap.video" :key="item + index">{{ item }}</Option>
          </Select>
        </ui-label>
        <ui-label class="inline right-margin mb-sm" label="设备重点类型">
          <Select
            class="input-width"
            v-model="searchData.isImportant"
            placeholder="请选择设备重点类型"
            clearable
            :disabled="parameters.isImportantIndex === 1"
            @on-change="changeBlur"
          >
            <Option key="0" :value="0">普通设备</Option>
            <Option key="1" :value="1">重点设备</Option>
          </Select>
        </ui-label>
        <ui-label label="报备状态" class="inline right-margin mb-sm">
          <Select class="width-md" v-model="searchData.examReportStatus" placeholder="请选择报备状态" clearable>
            <Option value="0">未报备</Option>
            <Option value="1">已报备</Option>
          </Select>
        </ui-label>
        <ui-label label="设备功能类型扩展" class="inline right-margin mb-sm">
          <Select
            class="width-md"
            v-model="searchData.sbgnlxExtList"
            placeholder="请选择设备功能类型扩展"
            clearable
            multiple
            filterable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in sbgnlxExtList" :key="index" :value="item.dataKey">
              {{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label label="设备状态扩展" class="inline right-margin mb-sm">
          <Select
            class="width-md"
            v-model="searchData.phyStatusExtList"
            placeholder="请选择设备状态扩展"
            clearable
            multiple
            filterable
            :max-tag-count="1"
          >
            <Option v-for="(item, index) in phyStatusExtList" :key="index" :value="item.dataKey">
              {{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <ui-label class="inline mb-sm" label="数据来源">
          <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
            <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey"
              >{{ item.dataValue }}
            </Option>
          </Select>
        </ui-label>
        <!-- 人体指标才显示 -->
        <ui-label v-if="moduleAction.indexModule === '9'" label="扩展能力集" class="inline right-margin mb-sm">
          <Select
            class="input-width"
            v-model="searchData.kznlj"
            placeholder="请选择"
            clearable
            disabled
            @on-change="changeBlur"
          >
            <Option v-for="(item, index) in kznljList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
          </Select>
        </ui-label>
        <br />
        <div class="data-list mb-sm">
          <div class="select-tabs mr-lg">
            <span class="fl mr-sm">标签类型</span>
            <ui-select-tabs
              class="ui-select-tabs"
              :list="errorList"
              @selectInfo="selectInfo"
              ref="uiSelectTabs"
            ></ui-select-tabs>
          </div>
          <div class="inline">
            <Button type="primary" @click="search">查询</Button>
            <Button class="ml-sm" @click="resetSearch">重置</Button>
          </div>
        </div>
      </template>
      <template #phyStatus="{ row }">
        <span
          :style="{
            color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
          }"
          >{{ row.phyStatus | filterType(phystatusList) }}</span
        >
      </template>
      <!-- 经纬度保留8位小数-->
      <template #longitude="{ row }">
        <span>{{ row.longitude | filterLngLat }}</span>
      </template>
      <template #latitude="{ row }">
        <span>{{ row.latitude | filterLngLat }}</span>
      </template>
      <template #baseCheckStatus="{ row }">
        <div :class="row.rowClass">
          <span
            class="check-status"
            :class="row.baseCheckStatus === '0' ? 'bg-success' : row.baseCheckStatus === '1' ? 'bg-failed' : ''"
          >
            {{ row.baseCheckStatus === '1' ? '不合格' : row.baseCheckStatus === '0' ? '合格' : '--' }}
          </span>
        </div>
      </template>
      <template #viewCheckStatus="{ row }">
        <div :class="row.rowClass">
          <span
            class="check-status"
            :class="row.viewCheckStatus === '0' ? 'bg-success' : row.viewCheckStatus === '1' ? 'bg-failed' : ''"
          >
            {{ row.viewCheckStatus === '1' ? '不合格' : row.viewCheckStatus === '0' ? '合格' : '--' }}
          </span>
        </div>
      </template>
      <template #videoCheckStatus="{ row }">
        <div :class="row.rowClass">
          <span
            class="check-status"
            :class="row.videoCheckStatus === '0' ? 'bg-success' : row.videoCheckStatus === '1' ? 'bg-failed' : ''"
          >
            {{ row.videoCheckStatus === '1' ? '不合格' : row.videoCheckStatus === '0' ? '合格' : '--' }}
          </span>
        </div>
      </template>
    </choose-device>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import taganalysis from '@/config/api/taganalysis';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    moduleAction: {
      type: Object,
    },
    indexModule: {
      type: String,
      default: '',
    },
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    /* 表单 */
    parameters: {
      required: true,
      type: Object,
      default: () => {},
    },
    regionCode: {
      type: String,
      default: '',
    },
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
    // 若需要设置默认的搜索tag
    defaultSelectTagIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tableData: (parameter) => {
        let params = Object.assign(this.searchData, parameter, {
          queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
        });
        return this.$http.post(equipmentassets.queryDeviceInfoPageList, params).then((res) => {
          return res.data;
        });
      },
      columns: [
        { type: 'selection', align: 'center', width: 50 },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          align: 'left',
          width: 170,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          minWidth: 200,
          tooltip: true,
        },
        {
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          minWidth: 120,
          tooltip: true,
        },
        {
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          width: 120,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.ipAddr}`,
          key: 'ipAddr',
          align: 'left',
          minWidth: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          align: 'left',
          width: 110,
          tooltip: true,
        },
        {
          title: this.global.filedEnum.sbdwlx,
          key: 'sbdwlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.sbgnlx}`,
          key: 'sbgnlxText',
          align: 'left',
          width: 130,
          tooltip: true,
        },
        {
          title: '基础信息状态',
          slot: 'baseCheckStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '视图数据状态',
          slot: 'viewCheckStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '视频数据状态',
          slot: 'videoCheckStatus',
          align: 'left',
          width: 100,
        },
        {
          title: '设备状态',
          slot: 'phyStatus',
          align: 'left',
          width: 100,
        },
      ],
      searchData: {
        orgCodeList: [],
        checkDeviceFlag: '0',
        deviceId: '',
        deviceName: '',
        sbdwlxList: [],
        sbgnlxList: [],
        phyStatus: '',
        cascadeReportStatus: '',
        isCheck: '',
        baseCheckStatus: '',
        baseErrorMessageList: [],
        viewCheckStatus: '',
        viewErrorMessageList: [],
        videoCheckStatus: '',
        videoErrorMessageList: [],
        isImportant: '',
        sourceId: '',
        tagIds: [],
        sbgnlxExtList: [],
        phyStatusExtList: [],
        kznlj: '',
      },
      ids: [], // 选择的设备ids
      totalCount: null, // 设备全选总数量
      videoCheck: {},
      errorList: [], // 标签类型
      errorMessageMap: {
        basic: [],
        view: [],
        video: [],
      },
    };
  },

  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          const { ids = [], totalCount, isImportantIndex } = this.parameters;
          // 当不是从检测结果中搜索时才带入搜索条件
          if (this.parameters.customMode !== 2) {
            Object.keys(this.parameters).forEach((key) => {
              if (this.searchData.hasOwnProperty(key)) {
                this.searchData[key] = this.parameters[key];
              }
            });
            //普通指标不默认 重点指标默认重点
            this.searchData.isImportant = isImportantIndex === 1 ? isImportantIndex : '';
          }
          this.ids = ids;
          this.totalCount = totalCount;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchLbgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      phystatusList: 'algorithm/propertySearch_phystatus',
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      sbgnlxExtList: 'algorithm/getSbgnlxExt', // 设备功能类型扩展
      phyStatusExtList: 'algorithm/getPhyStatusExt', // 设备状态扩展
      kznljList: 'algorithm/sq_kznlj', // 扩展能力集
    }),
  },
  components: {
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
  },
  async created() {
    this.setDefaultsbgnlx();
    if (this.propertySearchLbdwlx.length == 0) await this.getAlldicData();
    this.getTagList(); // 获取标签
    this.queryErrorList(); // 获取错误列表
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    /**
     *  设置默认值
     */
    setDefaultsbgnlx() {
      //人脸视图数据 2
      //车辆视图数据 3
      //视频流数据 4
      //人体视图数据 9
      switch (this.indexModule) {
        case '2':
          this.searchData.sbgnlxList = ['3'];
          break;
        case '3':
          this.searchData.sbgnlxList = ['2'];
          break;
        case '4':
          this.searchData.sbgnlxList = ['1'];
          break;
        case '9':
          this.searchData.sbgnlxList = ['3'];
          this.searchData.kznlj = '11';
          break;
        default:
          this.searchData.sbgnlxList = [];
          this.searchData.kznlj = '';
      }
      /**
       * @水平
       * 视频监控数量达标率 VIDEO_QUANTITY_STANDARD  视频监控填报准确率 VIDEO_ACCURACY
       * 这两个指标不需要拉流 所以来源需要默认选中
       * 不要 sourceid == 1
       * 最新需求： 统一添加【数据来源】筛选条件
       */
      let { indexType, indexModule } = this.moduleAction;
      let indexTypeList = ['VIDEO_QUANTITY_STANDARD', 'VIDEO_ACCURACY'];
      this.searchData.sourceId = !indexTypeList.includes(indexType) && indexModule === '4' ? '1' : '';
    },
    resetSearch() {
      this.searchData = {};
      this.setDefaultsbgnlx();
      this.$refs.chooseDevice.search();
      this.clearTagsStatus();
      this.$refs.uiSelectTabs?.reset();
    },
    changeBlur(optionType) {
      if (optionType) {
        if (optionType && !['1'].includes(this.searchData[optionType])) {
          if (optionType === 'isCheck') {
            this.searchData = {
              ...this.searchData,
              baseCheckStatus: '',
              viewCheckStatus: '',
              videoCheckStatus: '',
              baseErrorMessageList: [],
              viewErrorMessageList: [],
              videoErrorMessageList: [],
            };
          } else {
            this.searchData[`${optionType}ErrorMessageList`] = [];
          }
        }
      }
      this.$nextTick(() => {
        this.search();
      });
    },
    search() {
      this.$refs.chooseDevice.search();
    },
    selectCamera() {
      try {
        this.setDefaultSelectTags(this.defaultSelectTagIds);
      } catch (err) {
        console.log(err);
      } finally {
        this.$refs.chooseDevice.init();
      }
    },
    // 左侧 树状选择
    getOrgCode(orgCodeList) {
      this.searchData.orgCodeList = orgCodeList;
      this.$refs.chooseDevice.search();
    },
    // 点击确认获取选择的设备id
    getDeviceIdList({ chooseIds, selectedDevNum, checkDeviceFlag, searchCustomData }) {
      this.ids = chooseIds;
      this.totalCount = selectedDevNum;
      // 如果是自定义搜索
      let params = null;
      if (searchCustomData) {
        params = {
          ...searchCustomData,
          checkDeviceFlag,
        };
      } else {
        params = {
          ...this.searchData,
          checkDeviceFlag,
        };
      }

      this.$emit('getDeviceQueryForm', {
        ...params,
        ids: [...this.ids],
        totalCount: this.totalCount,
      });
    },

    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((row) => {
        return row.id;
      });
      this.search();
    },

    // 同时请求3个异常列表
    async queryErrorList() {
      let res = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'BASICS' },
      });
      this.errorMessageMap.basic = res.data.data;

      let res2 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'IMAGE' },
      });
      this.errorMessageMap.view = res2.data.data;

      let res3 = await this.$http.get(equipmentassets.queryErrorReason, {
        params: { errorType: 'VIDEO' },
      });
      this.errorMessageMap.video = res3.data.data;
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        this.errorList = this.$util.common.deepCopy(
          this.allTagList.map((row) => {
            return {
              name: row.tagName,
              id: row.tagId,
            };
          }),
        );
        // 已选择标签回显选中状态
        const { tagIds = [] } = this.searchData;
        if (tagIds.length > 0) {
          this.setTagsStatus(tagIds);
        }
      } catch (err) {
        console.log(err);
      }
    },

    // 标签类型选中状态回显
    setTagsStatus(list) {
      if (list && list.length > 0) {
        list.forEach((item) => {
          this.errorList.forEach((ite) => {
            if (ite.id === item) {
              this.$set(ite, 'select', true);
            }
          });
        });
      }
    },
    // 标签类型选中状态清除
    clearTagsStatus() {
      this.errorList.forEach((item) => {
        if (item.select) {
          item.select = false;
          // this.$set(item, 'select', false);
        }
      });
    },
    filterFun(data) {
      return data.label.indexOf('济南市') !== -1;
    },
    //设置默认的tagIndex，父级有调用
    setDefaultSelectTags(tagIds) {
      // if(!tagIds?.length){
      //   return
      // }
      //给从设备库中选择 赋值设备标签
      this.errorList.forEach((item) => {
        this.$set(item, 'select', tagIds.includes(item.id) ? true : false);
      });
      this.searchData.tagIds = tagIds;
      if (this.$refs.chooseDevice) {
        //给从检测结果选择 赋值设备标签
        this.$refs.chooseDevice.tagList?.forEach((item) => {
          item.select = tagIds.includes(item.id) ? true : false;
        });
        this.$refs.chooseDevice.searchConditions.tagIds = tagIds;
        this.$refs.chooseDevice.searchResultData.tagIds = tagIds;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.choose-device {
  @{_deep} .ivu-modal-body {
    padding: 0 !important;
  }
}
.custom-select-wrap {
  height: 58px;
  display: flex;
}

.right-margin {
  margin-right: 15px;
}
.input-width {
  width: 140px;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
@{_deep} .ivu-form-item-content {
  .ivu-dropdown {
    .ivu-dropdown-rel {
      .ivu-select-selection {
        height: 32px;
        line-height: 32px;
        .select-content {
          height: 32px;
          line-height: 32px;
          .ivu-select-placeholder {
            height: 32px;
            line-height: 32px;
          }
          .ivu-select-selected-value {
            height: 32px;
            line-height: 32px;
          }
        }
      }
    }
  }
}

@{_deep} .ivu-collapse {
  background-color: transparent;
  border: none;
  &-item {
    border-top: none;
    &-active {
      .ivu-collapse-header > i {
        font-size: 12px !important;
        color: #239df9 !important;
        transform: rotate(0deg) translate(-6px, 3px) scale(0.45) !important;
      }
    }
  }
  .ivu-collapse-header {
    display: flex;
    height: 0.135417rem;
    line-height: 0.135417rem;
    padding: 0 6px;
    font-size: 12px !important;
    color: #ffffff;
    border-bottom: none !important;
    opacity: 0.9;
    word-break: break-all;
    i {
      margin-right: 0 !important;
    }
    .ivu-icon-ios-arrow-forward {
      font-size: 12px !important;
      color: #239df9;
      transform: rotate(-90deg) scale(0.4);
      &:before {
        font-family: 'icon-font';
        content: '\e7a3';
      }
    }
  }
  &-content {
    background: transparent;
    padding: 0;
    &-box {
      padding-bottom: 0 !important;
    }
  }
}

@{_deep} .el-tree-node__expand-icon {
  color: var(--color-primary);
}
@{_deep} .el-tree-node__expand-icon.is-leaf {
  color: transparent;
}
.retest {
  @{_deep} .ivu-form-item-content {
    line-height: 0.166667rem !important;
  }
}
@{_deep} .ivu-modal .title {
  z-index: 15;
}

.statustag {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}

.left-select {
  width: 130px !important;
}
.right-select {
  width: 170px !important;
}
.multiple-select {
  @{_deep} .ivu-select-selection {
    div {
      white-space: nowrap;
      overflow-x: auto;
    }
  }
}
.data-list {
  display: flex;
  align-items: center;
  width: 100%;
  color: var(--color-content);
  font-size: 14px;
}
.select-tabs {
  flex: 1;
  display: flex;
  align-items: center;
}
.ui-select-tabs {
  flex: 1;
}
.check-status {
  padding: 0 8px;
}

.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
