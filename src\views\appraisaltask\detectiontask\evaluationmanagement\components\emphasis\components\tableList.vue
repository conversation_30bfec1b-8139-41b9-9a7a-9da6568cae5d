<template>
  <div class="tableList auto-fill">
    <slot name="search"></slot>
    <div class="left-div auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :class="tableData.length > 0 ? '' : 'ui-table-scroll-nodata'"
        :loading="loading"
        :table-columns="columns"
        :table-data="tableData"
        v-if="columns"
        ref="table"
      >
        <template v-for="item in slotList" #[item.slot]="{ row, index }">
          <slot :name="item.slot" :row="row" :index="index"></slot>
        </template>
      </ui-table>
    </div>
    <ui-page
      v-if="paging"
      class="page"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    ></ui-page>
  </div>
</template>
<script>
export default {
  name: 'basedField',
  data() {
    return {
      bigPictureShow: false,
      imgList: [],
      loading: false,
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      searchData: {
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      tableData: [],
    };
  },
  created() {},
  mounted() {},
  methods: {
    // 此方法在父组件调用表格方法传true重置条件
    async info(boolen) {
      if (boolen) {
        this.tableData = [];
        this.pageData.totalCount = 0;
        await this.reset();
      }
      if (!this.paging) {
        this.searchData = {};
      }
      this.loading = true;
      const result = this.loadData(this.searchData);
      if (typeof result == 'object' && typeof result.then == 'function') {
        result
          .then((res) => {
            if (res) {
              this.tableData = res.data[this.listKey];
              this.pageData.totalCount = res.data.total;
            }
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        this.tableData = [];
        this.pageData.totalCount = 0;
        this.loading = false;
      }
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = { params: { pageNumber: 1, pageSize: 20 } };
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      this.pageData.pageNum = val;
      this.info();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.params.pageSize = val;
      this.pageData.pageSize = val;
      this.info();
    },
  },
  watch: {},
  computed: {
    slotList() {
      let array = [];
      this.columns.map((val) => {
        if (val.slot) {
          array.push(val);
        }
      });
      return array;
    },
  },
  props: {
    columns: {
      // 表头
      type: Array,
      default() {
        return [];
      },
    },
    loadData: {
      // 接口
      type: Function,
      default() {},
    },

    listKey: {
      // list取值
      type: String,
      default: 'entities',
    },
    paging: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped>
.tableList {
  height: 100%;
  width: 100%;
  background: var(--bg-content);
  .left-div {
    position: relative;
    width: 100%;
    min-height: 1px;
  }
  .ui-table {
    width: 100%;
  }
  .page {
    padding-right: 0;
  }
  .ui-table-scroll-nodata {
    /deep/ .ivu-table-tip {
      overflow-x: auto;
    }
  }
}
</style>
