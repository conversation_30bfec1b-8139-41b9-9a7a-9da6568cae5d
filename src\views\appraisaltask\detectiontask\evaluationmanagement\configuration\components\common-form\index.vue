<template>
  <div class="config-common">
    <Form v-bind="$attrs" :label-width="labelWidth" :model="formData" :rules="ruleValidate" ref="formValidate">
      <FormItem label="检测对象" class="detection-object mb-lg" prop="regionCode">
        <api-area-tree
          class="api-area-tree"
          :disabled="true"
          :select-tree="selectAreaTree"
          @selectedTree="selectedAreaTree"
          placeholder="请选择检测对象"
        ></api-area-tree>
      </FormItem>
      <FormItem
        label="统计模式"
        v-if="needStatisticMode.includes(moduleAction.indexType)"
        prop="statisticalModel"
        class="statistical-modal-box"
      >
        <CheckboxGroup :value="formData.statisticalModel" @on-change="changeStatisticalModalGroup">
          <Checkbox :label="SMODE_REGION" class="mr-md">行政区划</Checkbox>
          <Checkbox :label="SMODE_ORG" class="mr-md">组织机构</Checkbox>
          <Checkbox :label="SMODE_DEVICETAG" class="flex-aic">
            <span class="mr-sm">设备标签</span>
            <span class="font-gray" @click.prevent="addStatisticTag" v-if="formData.tagIds?.length"
              >（已选择{{ formData.tagIds.length }}标签）</span
            >
            <i class="icon-font icon-peizhibiduiziduan f-14 font-gray" @click.prevent="addStatisticTag"></i>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="检测方式" prop="detectMode" class="mb-lg">
        <Select
          class="select-width"
          v-model="formData.detectMode"
          :disabled="handleWayDisabled"
          placeholder="请选择检测方式"
          transfer
          @on-change="handleChangeWay"
        >
          <Option
            v-for="item in handleWayList"
            :key="item.value"
            :value="item.value"
            :disabled="item.disabled"
            @click.native="clickOption(item)"
          >
            {{ item.label }}
          </Option>
        </Select>
        <slot name="waycondiction">
          <!--          <div v-if="formData.detectMode === '1' || formData.detectMode === '2'" class="child-mode mt-sm" >-->
          <!--            <span class="base-text-color">是否检测【不可用】状态设备：</span>-->
          <!--            <RadioGroup v-model="formData.deviceQueryForm.detectPhyStatus" @on-change="handleDetectPhyStatus">-->
          <!--              <Radio label="1">是</Radio>-->
          <!--              <Radio label="0">否</Radio>-->
          <!--            </RadioGroup>-->
          <!--          </div>-->
        </slot>
      </FormItem>
      <!--  检测方式是抽检的情况展示 -->
      <spot-check
        v-if="formData.detectMode === '2'"
        :origin-form="defaultFormData"
        :area-tree-data="areaTreeData"
        :flat-tree-data="flatTreeData"
        @updateParams="updateParams"
      >
        <!-- 统一配置抽检数量(之前的代码，未动) -->
        <template v-slot:unify="{ isunify }">
          <FormItem label="抽检对象" v-if="isunify" key="chooseOrg" prop="orgList">
            <Button @click="chooseOrg">
              <span
                v-if="'orgCountMap' in formData && !!formData.orgCountMap && !!Object.keys(formData.orgCountMap).length"
                >{{ `已选择 ${Object.keys(formData.orgCountMap).length}个` }}</span
              >
              <span v-else>请选择需抽检的组织机构</span>
            </Button>
          </FormItem>
          <FormItem
            label="每对象抽取设备数量"
            v-if="isunify && !unDeviceNum.includes(moduleAction.indexType)"
            prop="deviceNum"
          >
            <InputNumber
              v-model="formData.deviceNum"
              class="select-width"
              placeholder="请输入抽取设备数量"
              clearable
            ></InputNumber>
            <p class="color-failed" v-if="['FACE_CAPTURE_SCORE'].includes(moduleAction.indexType)">
              备注：系统只抽取有抓拍图片的设备。
            </p>
          </FormItem>
        </template>
      </spot-check>
      <FormItem
        label="每对象抽取图片数量"
        v-if="formData.detectMode === '2' && needCaptureNum.includes(moduleAction.indexType)"
        prop="captureNum"
      >
        <InputNumber
          v-model.number="formData.captureNum"
          class="select-width"
          placeholder="请输入每对象抽取图片数量"
          clearable
        ></InputNumber>
      </FormItem>
      <template v-if="formData.detectMode === '3' && !unNeedChooseDevice.includes(moduleAction.indexType)">
        <FormItem label="选择设备" class="deviceSelectType mb-lg">
          <RadioGroup v-model="formData.selectType" @on-change="changeDeviceType">
            <Radio :label="1">按设备选择</Radio>
            <Radio :label="0" v-if="isDir(moduleAction.indexType)" class="mr-lg">按目录选择</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="按目录选择" v-if="formData.selectType === 0" prop="directoryNames" key="directoryNames">
          <choose-catalogue
            class="catalogue"
            :regionCode="formData.regionCode"
            :category-obj="categoryObj"
            @getDevCategoryData="getDevCategoryData"
          >
          </choose-catalogue>
        </FormItem>
        <custom-detection-mode
          ref="customDetectionMode"
          v-if="formData.selectType === 1"
          :index-module="moduleAction.indexModule"
          :module-action="moduleAction"
          :parameters="parameters"
          :task-index-config="taskIndexConfig"
          :form-model="formModel"
          :region-code="formData.regionCode"
          :default-select-tag-ids="computedChooseDeviceTagIds"
          @getDeviceQueryForm="getDeviceQueryForm"
        ></custom-detection-mode>
      </template>
      <slot name="extract"></slot>
      <slot name="extractCar"></slot>
      <FormItem label="选择场所" v-if="moduleAction.indexType === 'SITE_PLACE_ACCURACY'" prop="placeData">
        <Button type="dashed" class="area-btn" @click="clickPlace"
          >请选择场所
          {{
            `已选择${
              (formData.deviceQueryForm &&
                formData.deviceQueryForm.filter &&
                formData.deviceQueryForm.filter.placeIds.length) ||
              0
            }`
          }}个</Button
        >
      </FormItem>
      <FormItem
        label="匹配设备数据范围"
        v-if="moduleAction.indexType === 'FOCUS_DEVICE_RELATED_RATE'"
        prop="relateDataType"
      >
        <Select class="select-width" v-model="formData.relateDataType" placeholder="请选择匹配设备数据">
          <Option v-for="(item, index) in mateDeviceList" :key="index" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem
        label="检测计划"
        class="right-item mb-0"
        prop="cronData"
        v-if="moduleAction.indexType !== 'VIDEO_PLATFORM_ONLINE_RATE' && formData.detectMode !== '4'"
        :rules="[
          {
            required: moduleAction.indexType !== 'VIDEO_PLATFORM_ONLINE_RATE',
            message: '请选择检测计划',
            trigger: 'change',
            type: 'array',
          },
        ]"
      >
        <TestPlan
          :form-data="formData"
          :form-model="formModel"
          :index-type="moduleAction.indexType"
          @checkTime="checkTime"
        ></TestPlan>
      </FormItem>
      <FormItem label="达标数量设置" v-if="standardQuantityVisible">
        <Button type="dashed" class="area-btn" @click="regionalizationSelectVisible = true"
          >达标数量设置
          <span>{{ `已选择 ${(formData.quantityData || 0) && formData.quantityData.length}个` }}</span>
        </Button>
      </FormItem>
      <!--      2020, 3025, 4025 人脸、车辆、视频流达标率指标    -->
      <FormItem
        v-if="[2020, 3025, 4025].includes(this.moduleAction.indexId)"
        label="超额得分公式"
        prop="excessCoefficientValue"
      >
        <Input class="ipt-width-100" v-model="formData.excessCoefficientValue" />
        <span class="base-text-color ml-xs"> * <span class="ml-xs">( 实际推送数量 / 达标推送数量 - 1 )</span></span>
      </FormItem>
      <FormItem v-if="[2020, 3025, 4025].includes(this.moduleAction.indexId)" label="超额总分" prop="excessTotalValue">
        <Input class="ipt-width-100" v-model="formData.excessTotalValue" />
        <span class="base-text-color ml-xs"> 分</span>
        <Tooltip placement="top-start" max-width="400" class="ml-xs">
          <i class="icon-font icon-wenhao f-14 vt-middle" :style="{ color: 'var(--color-warning)' }"></i>
          <div slot="content">
            <span>用于系统计算每个行政区划为了得到满分所需上报的设备数量</span>
          </div>
        </Tooltip>
      </FormItem>
      <FormItem
        prop="algVendors"
        label="选择算法厂商"
        v-if="algorithmManufacturer.includes(moduleAction.indexType) && formData.detectMode !== '4'"
      >
        <CheckboxGroup v-model="formData.algVendors">
          <Checkbox :label="item.algorithmVendorType" v-for="(item, index) in algorithm" :key="index">
            {{ item.algorithmLable }}
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem v-if="['VIDEO__VALID_SUBMIT_QUANTITY'].includes(moduleAction.indexType)" label="有效设备检测">
        <rule-list
          :form-model="formModel"
          :form-data="formData"
          :module-action="'BASIC_ACCURACY'"
          :index-rule-list="formData.ruleList"
        ></rule-list>
      </FormItem>
    </Form>
    <org-modal
      v-model="orgModalVisible"
      :default-org-count-map="'whole' in defaultFormData && defaultFormData.whole ? defaultFormData.orgCountMap : {}"
      :form-model="formModel"
      :flat-tree-data="flatTreeData"
      :area-tree-data="areaTreeData"
      :org-list="defaultOrgList"
      @query="queryCommonConfigOrg"
    ></org-modal>
    <choose-device
      ref="ChooseDevice"
      node-key="deviceId"
      modal-title="场所"
      tree-node-key="regionCode"
      need-handle
      :table-columns="tableColumns"
      :load-data="leftData"
      :search-conditions="searchConditions"
      :default-props="defaultProps"
      :selected-list="
        formData.deviceQueryForm && formData.deviceQueryForm.filter && formData.deviceQueryForm.filter.placeIds
      "
      :check-device-flag="
        formData.deviceQueryForm && formData.deviceQueryForm.filter && formData.deviceQueryForm.filter.checkDeviceFlag
      "
      @getOrgCode="getOrgCode"
      @getDeviceIdList="getDeviceIdList"
    >
      <template #search-header>
        <ui-label class="inline mr-lg mb-lg" label="地名名称">
          <Input v-model="searchConditions.placeName" class="width-lg" placeholder="请输入地名名称" clearable></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-lg" label="地名俗称">
          <Input v-model="searchConditions.placeAlias" class="width-lg" placeholder="请输入地名俗称" clearable></Input>
        </ui-label>
        <ui-label class="inline mr-lg mb-lg" label="采集区域类型">
          <Button type="dashed" class="area-btn" @click="areaSelectModalVisible = true"
            >请选择采集区域 {{ `已选择 ${searchConditions.sbcjqyList.length} 个` }}</Button
          >
        </ui-label>
        <div class="inline mb-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="resetSearch">重置</Button>
        </div>
      </template>
    </choose-device>
    <area-select
      v-model="areaSelectModalVisible"
      :checked-tree-data-list="searchConditions.sbcjqyList"
      @confirm="confirmArea"
    ></area-select>
    <regionalization-select
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :styles="regionSelectSyles"
      :data="formData.quantityData"
      :assessment-items="assessmentItems"
      @query="quantityQuery"
    ></regionalization-select>
    <!--    关联标签弹窗-->
    <customize-filter
      v-model="addTagVisible"
      :customize-action="tagFilterAttrs.customizeAction"
      :content-style="tagFilterAttrs.contentStyle"
      :field-name="tagFilterAttrs.fieldTagName"
      :checkbox-list="tagFilterAttrs.allTagList"
      :default-checked-list="formData.tagIds"
      :show-clear-all="true"
      @confirmFilter="confirmTagFilter"
    >
    </customize-filter>
  </div>
</template>
<script>
import BaseForm from './base-form.vue';
export default {
  extends: BaseForm,
};
</script>

<style lang="less" scoped>
.select-width {
  width: 380px;
}
.mb-0 {
  margin-bottom: 0;
}

.child-mode {
  display: flex;
  align-items: center;
}
.ipt-width-100 {
  width: 100px;
}
.catalogue {
  @{_deep}.select-width {
    width: 382px !important;
  }
  @{_deep}.ivu-select-dropdown {
    width: 382px;
    left: 0 !important;
  }
}
.detection-object {
  @{_deep}.select-width {
    width: 380px;
  }
  .api-area-tree {
    @{_deep}.ivu-dropdown {
      .ivu-select-dropdown {
        left: 0px;
        .ivu-dropdown-menu {
          min-width: 382px;
        }
      }
    }
  }
}
.statistical-modal-box {
  @{_deep} .ivu-checkbox-group {
    display: flex;
    align-items: center;
    flex-direction: row !important;
  }
}
</style>
