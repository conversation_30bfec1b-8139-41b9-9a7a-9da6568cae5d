<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <draw-echarts
      v-show="echartList.length"
      class="charts"
      :echart-option="echartOption"
      :echart-style="echartStyle"
      ref="eChartRef"
      :echarts-loading="echartsLoading"
    ></draw-echarts>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('eChartRef', echartList, [], echartNum)"></i>
    </span>
  </div>
</template>

<script>
import Vue from 'vue';
import VideoCancelRateTooltip from '@/views/home/<USER>/video-cancel-rate-tooltip.vue';
import dataZoom from '@/mixins/data-zoom';
import home from '@/config/api/home';
import detectionResult from '@/config/api/detectionResult';
import { mapGetters } from 'vuex';
import videoCancelRateStyle from '@/views/home/<USER>/module/video-cancel-rate';
import commonStyle from '@/views/home/<USER>/module/common-style';

export default {
  name: 'VideoCancelRate',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    year: {
      type: String,
      default: '',
    },
    homePageConfig: {},
    styleType: {},
  },
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartNum: 8,
      echartOption: {},
      echartsLoading: false,
      echartList: [],
      tooltipFormatter: (data) => {
        let videoCancelRateTooltip = Vue.extend(VideoCancelRateTooltip);
        let _this = new videoCancelRateTooltip({
          el: document.createElement('div'),
          data() {
            return {
              toolTipData: data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      legendList: [{ name: '撤销率', color: 'rgba(22, 174, 22, 1)' }],
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
    homeStyle() {
      return videoCancelRateStyle[`style${this.styleType}`] || videoCancelRateStyle.style1;
    },
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  watch: {
    homePageConfig: {
      handler(val) {
        if (!val.taskSchemeId) return;
        this.initAll();
      },
      deep: true,
      immediate: true,
    },
  },
  created() {},
  methods: {
    async initAll() {
      try {
        this.echartsLoading = true;
        this.echartList = [];

        // 只要重新请求数据， 都需要 重置 到第一页
        this.viewEchartIndex = 0;
        let { taskSchemeId, regionCode } = this.homePageConfig;

        // 1. 先获取最后完成的批次号
        let params = {
          taskSchemeId: taskSchemeId,
          indexId: '4029',
          yearAndMonth: this.year,
        };
        let res = await this.$http.get(home.queryLatestBatchId, { params: params });
        let batchId = res?.data?.data || '';
        if (!batchId) return;

        // 2. 查询 检测结果详情-统计列表
        let data = {
          batchId: batchId,
          displayType: 'REGION',
          indexId: '4029',
          orgRegionCode: regionCode,
          qualified: '',
        };
        let result = await this.$http.post(detectionResult.getStatInfoList, data);
        let arr = result.data.data || [];
        arr = arr.map((item) => {
          return {
            ...item,
            rateSelf: item.resultValueFormat ? item.resultValueFormat.split('%')[0] : '0', // resultValueFormat: '0.0%'
          };
        });
        this.echartList = arr;
        this.setEchartOption();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    setEchartOption() {
      let barData = [
        {
          name: '撤销数量',
          data: this.echartList.map((item, index) => {
            let color = index % 2 === 0 ? this.homeStyle.barColorBlue : this.homeStyle.barColorGreen;
            return {
              ...item,
              value:
                item?.detail?.revocationNumConfig === 'addUp'
                  ? item?.detail?.addUpRevocationNumber
                  : item?.detail?.finalRevocationNumber,
              itemStyle: {
                color: {
                  type: 'linear',
                  colorStops: [
                    { offset: 0, color: color[0] },
                    { offset: 1, color: color[1] },
                  ],
                },
              },
              color: color[0],
            };
          }),
          barWidth: this.$util.common.fontSize(12),
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          yAxisIndex: 0,
        },
      ];

      let line = [
        {
          name: '撤销率',
          type: 'line',
          itemStyle: {
            color: this.homeStyle.lineColor,
          },
          symbol: 'diamond',
          yAxisIndex: 1,
          data: this.echartList.map((item) => {
            return {
              value: item.rateSelf,
              color: this.homeStyle.lineColor,
            };
          }),
          valueType: 'percent',
        },
      ];

      let opts = {
        legendList: this.legendList,
        xAxis: this.echartList.map((item) => item.civilName),
        series: [...barData, ...line],
        tooltipFormatter: this.tooltipFormatter,
        tooltipBg: this.commonStyle.tooltipBg,
      };
      this.echartOption = this.$util.doEcharts.baseHomeVideoCancelRate(opts);
      setTimeout(() => {
        this.setDataZoom('eChartRef', [], this.echartNum);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  height: calc(100% - 30px) !important;
}
.charts {
  width: 100%;
  height: 100% !important;
}
</style>
