import { getAlgorithmSelectList } from "@/api/multimodal-analysis.js";
export default {
  namespaced: true,
  state: {
    isFirst: true,
    structureAlgorithmList: [], // 大模型结构化算法列表
  },
  mutations: {
    setStructureAlgorithmList(state, list) {
      state.structureAlgorithmList = list;
    },
  },
  getters: {
    getStructureAlgorithmList(state) {
      return state.structureAlgorithmList;
    },
  },
  actions: {
    getInitData({ state, commit }, param = {}) {
      return new Promise((resolve, reject) => {
        if (state.structureAlgorithmList.length == 0) {
          getAlgorithmSelectList(param)
            .then((res) => {
              if (res.code == 200) {
                commit("setStructureAlgorithmList", res?.data || []);
                resolve(res);
              } else {
                reject(res);
              }
            })
            .catch((err) => {
              reject(err);
            });
        } else {
          resolve();
        }
      });
    },
  },
};
