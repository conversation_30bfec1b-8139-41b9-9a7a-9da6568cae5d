<template>
  <div
    class="card-item close"
    :style="{ background: !!cardData.disabled ? cardBgColor : cardBgColor }"
    @click="handleModuleShow(cardData)"
  >
    <div class="card-item-head">
      <div class="card-item-head-left mr-sm">
        <div class="icon-box flex-center" :style="{ 'background-color': cardData.iconBgColor }">
          <i :class="['icon-font', 'font-45', cardData.icon]" :style="{ color: '#fff' }"></i>
        </div>
      </div>
      <div class="card-item-head-right">
        <p class="card-item-title mb-xs">{{ cardData.title }}</p>
        <Poptip trigger="hover" :disabled="isDisabledTooltip" placement="bottom-end" word-wrap :content="cardData.desc">
          <div
            class="card-item-desc"
            :style="{ color: !!cardData.disabled ? '' : '' }"
            @mouseover="onMouseOverHeight(cardData.icon)"
          >
            <span :ref="cardData.icon">{{ cardData.desc }}</span>
          </div>
        </Poptip>
      </div>
    </div>
    <div v-if="!!cardData.download" class="card-item-foot">
      <i class="icon-font icon-xiazai-011 font-16 icon-operation-color"></i>
    </div>
  </div>
</template>

<script>
export default {
  name: 'card-item',
  props: {
    cardData: {
      type: Object,
      default: () => {},
    },
    cardBgColor: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      disableBgColor: '#21314A', // 暂未开放的背景颜色
      disableIconBgColor: '#2D415D', // 暂未开放的按钮背景颜色
      disableColor: '#677588', // 暂未开放的文字颜色
      isDisabledTooltip: true,
    };
  },
  methods: {
    handleModuleShow(data) {
      if (data.disabled) {
        this.$Message.warning('平台已具备该功能，请在评测中心选择对应指标进行检测，独立小工具敬请期待');
        return;
      } else if (data.jumpType) {
        this.$emit('handleJump', data);
      } else if (data.download) {
        this.$emit('handleDownLoad');
      } else {
        let params = {};
        if (data.governanceContent) {
          params.governanceContent = data.governanceContent;
        }
        if (data.sourceType) {
          params.sourceType = data.sourceType;
        }
        this.$router.push({
          name: data.componentName,
          params: params,
        });
      }
    },
    onMouseOverHeight(str) {
      let parentHeight = this.$refs[str].parentNode.offsetHeight;
      let contentHeight = this.$refs[str].offsetHeight;
      this.$set(this, 'isDisabledTooltip', contentHeight < parentHeight); // 判断是否禁用tooltip功能
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .card-item {
    .card-item-title {
      color: var(--color-bluish-green-text);
    }
    .card-item-desc {
      color: #a7c4ee;
    }
  }
  @{_deep} .ivu {
    &-poptip {
      &-inner {
        border: 1px solid #0d4a81 !important;
      }
      &-body-content-inner {
        color: #ffffff !important;
      }
      &-popper {
        &[x-placement^='bottom'] {
          .ivu-poptip-arrow {
            border-bottom-color: var(--color-primary) !important;
            &:after {
              border-bottom-color: #0d3560 !important;
              bottom: 1px;
            }
          }
        }
        &[x-placement^='left'] {
          .ivu-poptip-arrow {
            border-left-color: #0d4a81 !important;
            &:after {
              border-left-color: #0d3560 !important;
            }
          }
        }
      }
    }
    &-poptip-popper[x-placement^='top'] .ivu-poptip-arrow {
      border-top-color: #0d4a81 !important;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .card-item {
    .card-item-title {
      color: var(--color-primary);
    }
    .card-item-desc {
      color: rgba(0, 0, 0, 0.8);
    }
  }
  @{_deep} .ivu {
    &-poptip {
      &-inner {
        border: 1px solid #d8d8d8 !important;
      }
      &-body-content-inner {
        color: #ffffff !important;
      }
      &-popper {
        &[x-placement^='bottom'] {
          .ivu-poptip-arrow {
            border-bottom-color: var(--color-primary) !important;
            &:after {
              border-bottom-color: #d8d8d8 !important;
              bottom: 1px;
            }
          }
        }
        &[x-placement^='left'] {
          .ivu-poptip-arrow {
            border-left-color: #d8d8d8 !important;
            &:after {
              border-left-color: #d8d8d8 !important;
            }
          }
        }
      }
    }
    &-poptip-popper[x-placement^='top'] .ivu-poptip-arrow {
      border-top-color: #d8d8d8 !important;
    }
  }
  .card-item @{_deep}.icon-font.icon-operation-color {
    color: var(--color-primary) !important;
  }
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-item {
  position: relative;
  width: 100%;
  height: 136px;
  padding: 25px 10px 0 20px;
  border: 2px solid transparent;
  border-radius: 4px;
  cursor: pointer;

  &-head {
    display: flex;

    .icon-box {
      width: 69px;
      height: 69px;
      border-radius: 50%;
      cursor: default;

      i {
        cursor: default !important;
      }
    }

    .font-45 {
      font-size: 45px;
    }
  }

  &-title {
    font-weight: bold;
  }

  &-desc {
    font-weight: normal;
    line-height: 22px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  @media screen and (max-width: 1366px) {
    &-desc {
      -webkit-line-clamp: 2;
    }
  }

  &-foot {
    position: absolute;
    bottom: 5px;
    right: 10px;
    text-align: right;
  }
  // &:hover {
  //   border: 2px solid var(--color-active);
  // }

  .font-24 {
    font-size: 16px;
  }
}
@{_deep} .ivu {
  &-poptip {
    position: relative;
    width: 100%;
    &-rel {
      width: 100%;
    }
    &-arrow:after {
      border-bottom-color: transparent !important;
    }
  }
}
</style>
