<template>
  <ui-modal v-model="visible" footer-hide class='peers-modal'>
    <peer v-on='$listeners' v-bind='$attrs'></peer>
<!--    <template #header>
      <div class="detail-title">
        <p>同行 <span> {{ detail.peerNum || 0}} </span> 次</p>
      </div>
    </template>
    <ul class="peer-content-box" >
      <li class="content-box-li" v-for="(item, index) in detail.peerDetails" :key='`${index}-${item.peerCaptureId}`'>
        <div class="box-li-icon">{{ index + 1 }}</div>
        <div class="box-li-right">
          <p class="box-li-right-title">{{ item.deviceAddress || '&#45;&#45;' }}</p>
          <ul class="box-li-right-content">
            <li @click="peerMessage(item.source)">
              <div class="right-img-list">
                <ui-image :type='type' :src="item.source.traitImg || getImageCropper(item.source.sceneImg, item.source.vehicleLocation)" alt="" />
                <p class="right-img-list-tag tag-bule">对象</p>
              </div>
              <p class="box-li-right-time">{{ item.source.absTime || '&#45;&#45;'}}</p>
            </li>
            <li @click="peerMessage(item.peer)">
              <div class="right-img-list">
                &lt;!&ndash; <ui-image :src="item.ferriteDetailVo.traitImg" viewer /> &ndash;&gt;
                <ui-image :type='type' :src="item.peer.traitImg || getImageCropper(item.peer.sceneImg, item.peer.vehicleLocation )" alt="" />
                <p class="right-img-list-tag tag-yellow">同行</p>
                <div class="plateNumber">{{ item.peer.plateNo }}</div>
              </div>
              <p class="box-li-right-time">{{ item.peer.absTime || '&#45;&#45;'}}</p>
            </li>
          </ul>
        </div>
      </li>
    </ul>-->
  </ui-modal>
</template>
<script>
/**
 * 同行详情
 */
import Peer from '@/views/holographic-archives/components/peer.vue'
export default {
  name: "peer-modal",
  components: {Peer},
  props: {
    value: {},
    data: {
      required: true
    },
    type: {
      required: true
    },
  },
  data(){
    return {
      visible: false,
      detail: {}
    }
  },
  watch: {
    visible(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.visible = val
    },
    data: {
      deep: true,
      immediate: true,
      handler(val){
        this.detail = val || {}
      }
    }
  },
  methods: {
    peerMessage(item){
      this.$emit("on-click", item)
    }
  }
}
</script>
<style scoped lang='less'>

.peers-modal {
  /deep/ .ivu-modal-header {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  /deep/ .ivu-modal {
    top: -48px;
    left: 10px;
    margin: 0;
  }

  /deep/ .ivu-modal-body {
    height: 880px;
    overflow-y: auto;
  }

}

.peer-content-box{
  height: 640px;
  overflow: auto;
  position: relative;
  .content-box-li{
    margin-top: 10px;
    margin-bottom: 13px;
    display: flex;
    .box-li-icon{
      width: 24px;
      background: url('~@/assets/img/archives/mark-red.png') no-repeat;
      background-size: 24px 28px;
      display: flex;
      justify-content: center;
      height: 30px;
      color: #EA4A36;
    }
    .box-li-right{
      flex: 1;
      margin-left: 12px;
      &-title{
        font-size: 12px;
        font-weight: bold;
        color: #181818;
      }
      &-content{
        background: #F9F9F9;
        border-radius: 4px;
        padding: 10px 9px 12px;
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        li{
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .right-img-list{
          position: relative;
          width: 100px;
          margin-bottom: 10px;
          height: 100px;
          img{
            width: 100px;
            height: 100px;
            cursor: pointer;
          }
          &-tag{
            // width: 40px;
            // height: 20px;
            border-radius: 0px 0px 4px 0px;
            position: absolute;
            top: 0;
            color: #fff;
            font-size: 12px;
            padding: 1px 3px;
            z-index: 20;
          }
          .tag-bule{
            background: #2C86F8;
          }
          .tag-yellow{
            background:#F29F4C;
          }
          .plateNumber{
            position: absolute;
            bottom: 0px;
            left: 1px;
            background: rgba(0, 0, 0, 0.6);
            width: 98px;
            text-align: center;
            // opacity: 0.6;
            color: #FFFFFF;
            font-size: 14px;
          }
        }
      }
      .box-li-right-time{
        font-size: 12px;
        color: rgba(0,0,0,0.6);
      }
    }
  }
}
</style>