/*
 * @Date: 2024-12-20 11:04:23
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-03-28 14:33:38
 * @FilePath: \icbd-view\src\map\core\enum\LayerType.js
 */
//设备
export const LayerType = {
  PoleGroupPoint: {
    value: "PoleGroupPoint",
    text: "重合点",
    url: require("@/assets/img/map/mapPoint/map-pole.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-pole_active.png"),
  },
  Camera: {
    value: "Camera",
    text: "高清视频",
    url: require("@/assets/img/map/mapPoint/map-qiangji.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-qiangji_active.png"),
    offLineUrl: require("@/assets/img/map/mapPoint/map-qiangji_offline.png"),
    url2: require("@/assets/img/map/mapPoint/map-qiangji2.png"),
    url3: require("@/assets/img/map/mapPoint/map-qiangji3.png"),
  },
  Camera_QiuJi: {
    value: "Camera_QiuJi",
    text: "球机",
    url: require("@/assets/img/map/mapPoint/map-qiuji.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-qiuji_active.png"),
    offLineUrl: require("@/assets/img/map/mapPoint/map-qiuji_offline.png"),
    url2: require("@/assets/img/map/mapPoint/map-qiuji2.png"),
    url3: require("@/assets/img/map/mapPoint/map-qiuji3.png"),
  },
  Camera_QiangJi: {
    value: "Camera_QiangJi",
    text: "枪机",
    url: require("@/assets/img/map/mapPoint/map-qiangji.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-qiangji_active.png"),
    offLineUrl: require("@/assets/img/map/mapPoint/map-qiangji_offline.png"),
    url2: require("@/assets/img/map/mapPoint/map-qiangji2.png"),
    url3: require("@/assets/img/map/mapPoint/map-qiangji3.png"),
  },
  Camera_Face: {
    value: "Camera_Face",
    text: "人脸卡口",
    url: require("@/assets/img/map/mapPoint/map-face.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-face_active.png"),
  },
  Camera_Wifi: {
    value: "Camera_Wifi",
    text: "Wi-Fi设备",
    url: require("@/assets/img/map/mapPoint/map-wifi.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-wifi_active.png"),
  },
  Camera_Electric: {
    value: "Camera_Electric",
    text: "电围设备",
    url: require("@/assets/img/map/mapPoint/map-electric.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-electric_active.png"),
  },
  Camera_Vehicle: {
    value: "Camera_Vehicle",
    text: "车辆卡口",
    url: require("@/assets/img/map/mapPoint/map-vehicle.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-vehicle_active.png"),
  },
  Camera_RFID: {
    value: "Camera_RFID",
    text: "RFID设备",
    url: require("@/assets/img/map/mapPoint/map-rfid.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-rfid_active.png"),
  },
  Camera_ETC: {
    value: "Camera_ETC",
    text: "ETC设备",
    url: require("@/assets/img/map/mapPoint/map-etc.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-etc_active.png"),
  },
  // 警力
  Point_Police: {
    value: "Point_Police",
    text: "警力",
    url: require("@/assets/img/map/mapPoint/map-police.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-police_active.png"),
  },
  // 兴趣点
  Point_POI: {
    value: "Point_POI",
    text: "兴趣点",
    url: require("@/assets/img/map/mapPoint/map-government.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-government_active.png"),
  },
  //   Camera_Multi: { value: "Camera_Multi", text: "多功能" },
};
// 场所
export const siteType = {
  Place_Hotel: {
    value: "Place_Hotel",
    text: "酒店",
    url: require("@/assets/img/map/mapPoint/map-hotel.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-hotel_active.png"),
  },
  Place_InterBar: {
    value: "Place_InterBar",
    text: "网吧",
    url: require("@/assets/img/map/mapPoint/map-internetBar.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-internetBar_active.png"),
  },
  Place_Government: {
    value: "Place_Government",
    text: "政府机关",
    url: require("@/assets/img/map/mapPoint/map-government.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-government_active.png"),
  },
  Place_School: {
    value: "Place_School",
    text: "学校",
    url: require("@/assets/img/map/mapPoint/map-school.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-school_active.png"),
  },
  Place_Key: {
    value: "Place_Key",
    text: "重点场所",
    url: require("@/assets/img/map/mapPoint/map-keyPoint.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-keyPoint_active.png"),
  },
};
// 场所
export const placeType = {
  hotel: {
    text: "酒店",
    color: "#FFB04F",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    url: require("@/assets/img/map/mapPoint/map-hotel.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-hotel_active.png"),
  },
  diannao: {
    text: "网吧",
    color: "#EB6C6C",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    url: require("@/assets/img/map/mapPoint/map-internetBar.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-internetBar_active.png"),
  },
  sangna: {
    color: "#2C86F8",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    text: "桑拿浴室",
    url: require("@/assets/img/map/mapPoint/map-shower.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-shower.png"),
  },
  liubingchang: {
    color: "#1FAF8A",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    text: "溜冰场",
    url: require("@/assets/img/map/mapPoint/map-skate.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-skate.png"),
  },
  gewuting: {
    color: "#6C6CEA",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    text: "歌舞厅",
    url: require("@/assets/img/map/mapPoint/map-sing.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-sing.png"),
  },
  dianyingyuan: {
    text: "影剧院",
    color: "#48baff",
    clusterUrl: require("@/assets/img/map/camera_cluster_2.png"),
    url: require("@/assets/img/map/mapPoint/map-movie.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/map-movie.png"),
  },
};
// 未成年人报警
export const juvenileAlarmType = {
  LevelOne: {
    value: "LevelOne",
    text: "一级报警",
    url: require("@/assets/img/map/mapPoint/task-level-one.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/task-level-one.png"),
  },
  LevelTwo: {
    value: "LevelTwo",
    text: "二级报警",
    url: require("@/assets/img/map/mapPoint/task-level-two.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/task-level-two.png"),
  },
  LevelThree: {
    value: "LevelThree",
    text: "三级报警",
    url: require("@/assets/img/map/mapPoint/task-level-three.png"),
    hoverUrl: require("@/assets/img/map/mapPoint/task-level-three.png"),
  },
};
