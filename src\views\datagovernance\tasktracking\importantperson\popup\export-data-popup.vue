<template>
  <ui-modal :title="title" v-model="visible" :styles="styles" :footer-hide="true">
    <!-- <div class="top-wrapper"> 
			<span>最新更新时间：{{ modifyTime }}</span>
		</div> -->
    <line-title title-name="检测结果统计"></line-title>
    <div class="echarts-wrapper">
      <div class="ring-box">
        <draw-echarts
          :echart-option="allRingStaticsOption"
          :echart-style="ringStyle"
          ref="zdryChart1"
          class=""
          :echarts-loading="echartsLoading"
        ></draw-echarts>
      </div>
      <div class="rings-box">
        <template v-if="!!echartList.length">
          <draw-echarts
            v-for="(item, index) of echartList"
            :key="index"
            :echart-option="item.echartRingOption"
            :echart-style="ringStyle"
            :echarts-loading="echartsLoading"
            :ref="'zdryChart' + index"
            class="charts"
          >
          </draw-echarts>
        </template>
        <span class="no-data-text" v-else>暂无数据</span>
      </div>
    </div>
    <line-title title-name="问题数据列表" class="mb-sm"></line-title>
    <!-- <div class="tabs">
                <span v-for="(item,index) in tabList" :key="index" @click="tabClick(cur=index)" :class="cur==index ? 'cur' : ''">
                {{item.title}}</span>
            </div> -->
    <Tabs @changeTab="changeTab"></Tabs>
    <div class="keypersonlibrary-header">
      <ui-label class="inline" label="关键词" :width="55">
        <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入关键词"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="抓拍数量" :width="70">
        <Input v-model="searchData.captureNumberStart" class="width-sm" placeholder="请填写数量"></Input>
        <span class="horizontalbar">—</span>
        <Input v-model="searchData.captureNumberEnd" class="width-sm" placeholder="请填写数量"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="抓拍异常数量" :width="98">
        <Input v-model="searchData.captureAbnormalNumberStart" class="width-sm" placeholder="请填写数量"></Input>
        <span class="horizontalbar">—</span>
        <Input v-model="searchData.captureAbnormalNumberEnd" class="width-sm" placeholder="请填写数量"></Input>
      </ui-label>
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
    </div>
    <ui-select-tabs
      v-if="tabsList.length && cur == 0"
      class="tabs-ui"
      @selectInfo="selectInfo"
      :list="tabsList"
    ></ui-select-tabs>
    <div class="table-wrapper">
      <face-list
        :faceLoading="faceLoading"
        v-if="cur == 0"
        :face-list="faceList"
        height="1.17rem"
        @uploadTips="uploadTips"
      >
      </face-list>
      <!-- // -->
      <div class="keypersonlibrary-content auto-fill" v-if="cur == 1">
        <div class="keypersonlibrary-content-wrap">
          <ui-gather-card
            class="card"
            v-for="(item, index) in cardList"
            :key="index"
            :list="item"
            :cardInfo="cardInfo"
            @detail="detail"
          ></ui-gather-card>
          <div class="no-data" v-if="!cardList.length">
            <img v-if="themeType === 'dark'" src="@/assets/img/common/nodata.png" alt="" />
            <img v-else src="@/assets/img/common/nodata-light.png" alt="" />
          </div>
        </div>
        <loading v-if="loading"></loading>
      </div>

      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <capture-details ref="CaptureDetails"></capture-details>
    </div>
    <export-disqualify v-model="disqualifyShow" :disqualify-item="disqualifyItem"></export-disqualify>
  </ui-modal>
</template>
<script>
import importantEnum from '../util/enum';
import tasktracking from '@/config/api/tasktracking';
import { mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    modifyTime: {
      default: '',
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        width: '95%',
      },
      searchData: {
        captureNumberStart: null,
        captureNumberEnd: null,
        captureAbnormalNumberStart: null,
        captureAbnormalNumberEnd: null,
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
        orgCode: '',
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      ringStyle: {
        height: '180px',
        width: '300px',
      },
      title: '',
      faceLoading: false,
      disqualifyShow: false,
      disqualifyItem: {},
      faceSearchData: {
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
        reasons: [],
      },
      popUpOption: {},
      tabsList: [],
      allRingStaticsOption: {},
      echartList: [],
      echartsLoading: false,
      faceList: [],
      cur: 0, //tab模式切换
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'catchAmount' },
        { name: '异常轨迹：', value: 'exceptCount', color: '#BC3C19' },
      ],
      loading: false,
      tabList: [{ title: '图像模式' }, { title: '聚档模式' }],
    };
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
  methods: {
    init(option) {
      this.initClear();
      this.title = option.title;
      this.visible = true;
      this.popUpOption = option;
      this.getEchartsData();
    },
    initClear() {
      this.echartList = [];
      this.allRingStaticsOption = {};
      this.tabsList = [];
      this.faceList = [];
      this.faceSearchData = {
        deviceIds: [],
        startTime: '',
        endTime: '',
        pageNumber: 1,
        pageSize: 20,
        reasons: [],
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.faceSearchData.pageNumber = val;
      this.pageData.pageNum = val;
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    changePageSize(val) {
      this.faceSearchData.pageNumber = 1;
      this.searchData.pageNumber = 1;
      this.searchData.pageSize = val;
      this.faceSearchData.pageSize = val;
      this.pageData.pageSize = val;
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    handleTabsList(data) {
      this.tabsList = data.map((item) => {
        let one = {
          select: false,
          name: item,
        };
        return one;
      });
    },
    startFaceSearch(faceSearchData) {
      this.faceSearchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      Object.assign(this.faceSearchData, faceSearchData);
      this.queryPersonLibPageTaskTaskTracker();
    },
    getEchartsData() {
      this.queryPersonLibCheckCountStatistics();
      this.queryCheckStatics(1).then((data) => {
        this.handleTabsList(data.map((item) => item.errorMessage));
        data.forEach((item) => {
          let oneObject = {
            name: item.errorMessage,
            value: item.count,
            color: importantEnum.ringColorEnum.redColor,
          };
          this.echartList.push({
            echartRingOption: this.handleRingsEcharts([oneObject], item.errorMessage, item.count),
          });
        });
      });
      this.queryPersonLibPageTaskTaskTracker();
    },
    // 数据输出（图像）
    async queryPersonLibCheckCountStatistics() {
      try {
        let { data } = await this.$http.post(tasktracking.queryPersonCheckCountStatistics, {
          topicComponentId: this.popUpOption.filedData.topicComponentId,
        });
        let allData = data.data;
        let successData = {
          name: '检测合格',
          value: allData.goodCount,
          color: importantEnum.ringColorEnum.greenColor,
        };
        let failData = {
          name: '异常数据',
          value: allData.failCount,
          color: importantEnum.ringColorEnum.redColor,
        };
        this.allRingStaticsOption = this.handleRingsEcharts([successData, failData], '数据总量', allData.allCount);
      } catch (err) {
        console.log(err);
      }
    },
    async queryPersonLibPageTaskTaskTracker() {
      try {
        this.faceLoading = true;
        let { data } = await this.$http.post(tasktracking.queryPersonLibPageTaskTaskTracker, this.faceSearchData);
        this.faceList = data.data.entities;
        this.pageData.totalCount = data.data.total;
        this.faceLoading = false;
      } catch (err) {
        this.faceLoading = false;
        console.log(err);
      }
    },
    // 聚档接口
    async importantPersonPageList() {
      try {
        this.loading = true;
        this.searchData.componentCode = '1004';
        let res = await this.$http.post(tasktracking.importantPersonPageList, this.searchData);
        const datas = res.data.data;
        this.cardList = datas.entities;
        // this.cardList = datas.entities.map((item) => {
        //     const personTypes = item.personTypeText.split(",");
        //     item.personTypes = persontype.filter((item) => {
        //          return personTypes.includes(item.tagName);
        //     });
        //     return item;
        // });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
      }
    },
    // 人员检测错误报表统计(右侧) checkDataType：0:人脸 1：轨迹
    async queryCheckStatics(checkDataType) {
      try {
        let params = {
          checkDataType: checkDataType,
        };
        this.echartsLoading = true;
        let { data } = await this.$http.post(tasktracking.queryCheckStatics, params);
        this.echartsLoading = false;
        return data.data;
      } catch (err) {
        this.echartsLoading = false;
        console.log(err);
      }
    },
    handleRingsEcharts(data, text, subtext) {
      let options = {
        data: data,
        text: text,
        subtext: subtext,
        legendData: data.map((item) => item.name),
      };
      return this.$util.doEcharts.taskTrackingRing(options);
    },
    selectInfo(val) {
      this.faceSearchData.reasons = val.map((item) => item.name);
      this.faceSearchData.pageNumber = 1;
      this.queryPersonLibPageTaskTaskTracker();
    },
    uploadTips(item) {
      this.disqualifyShow = true;
      this.disqualifyItem = item;
    },
    detail(id, list) {
      this.$refs.CaptureDetails.init(id, list);
    },
    changeTab(index) {
      this.cur = index;
      this.searchData.pageSize = 20;
      this.pageData.pageSize = 20;
      this.searchData.captureNumberStart = null;
      this.searchData.captureNumberEnd = null;
      this.searchData.captureAbnormalNumberStart = null;
      this.searchData.captureAbnormalNumberEnd = null;
      this.searchData.keyWord = '';
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    // 搜索
    search() {
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
    clear() {
      this.searchData.captureNumberStart = null;
      this.searchData.captureNumberEnd = null;
      this.searchData.captureAbnormalNumberStart = null;
      this.searchData.captureAbnormalNumberEnd = null;
      this.searchData.keyWord = '';
      if (this.cur == 0) {
        this.queryPersonLibPageTaskTaskTracker();
      } else {
        this.importantPersonPageList();
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
      this.searchData = {
        captureNumberStart: null,
        captureNumberEnd: null,
        captureAbnormalNumberStart: null,
        captureAbnormalNumberEnd: null,
        keyWord: '',
        pageNumber: 1,
        pageSize: 20,
      };
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    FaceList: require('@/components/face-list.vue').default,
    ExportDisqualify: require('./export-disqualify.vue').default,
    LineTitle: require('@/components/line-title.vue').default,
    UiGatherCard: require('@/components/ui-gather-card_str.vue').default, //聚档模式
    CaptureDetails: require('./details/details.vue').default,
    Tabs: require('./details/tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.echarts-wrapper {
  display: flex;
  background: var(--bg-sub-content);
  margin: 10px 0;
  height: 180px;
  .ring-box {
    width: 570px;
    margin-right: 10px;
    .echarts {
      width: 540px !important;
    }
  }
  .rings-box {
    display: flex;
    flex: 1;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      background: #0d477d;
      height: 100px;
      width: 1px;
      left: 20px;
      top: 25px;
    }
  }
  .ring-box,
  .column-box {
    background: var(--bg-sub-content);
  }
  .charts {
    //flex: 1;
  }
}
.table-wrapper {
  position: relative;
  .no-data {
    top: 60%;
    > img {
      width: 609px;
      height: 325px;
    }
  }
}
.top-wrapper {
  display: flex;
  justify-content: space-between;
  color: #fff;
  .search-box {
    display: flex;
    .input-width {
      width: 230px;
    }
  }
}
.tabs-ui {
  color: #fff;
  margin: 10px 0;
}
.base-search {
  margin-top: 15px;
  margin-bottom: 0px;
}
.no-data-text {
  top: 50%;
  left: 50%;
  position: absolute;
  color: #fff;
  font-size: 16px;
  transform: translate(-50%, -50%);
}
.card {
  width: calc(calc(100% - 40px) / 4);
  margin: 0 5px 10px;
  float: left;
}
.keypersonlibrary-content {
  margin-top: 20px;
}
.keypersonlibrary-content-wrap {
  height: 290px;
  overflow-y: auto;
}
</style>
