<template>
  <div class="search-and-button">
    <div class="search-module">
      <ui-label class="inline mr-md mb-sm" label="工单类型" v-if="showSearchKeys.includes('indexId')">
        <Select
          v-model="searchData.indexModule"
          placeholder="请选择工单类型"
          filterable
          clearable
          class="width-md"
          @on-change="onChangeIndexModule"
        >
          <Option v-for="(item, index) in indexModules" :key="index" :value="item.dataKey">{{ item.dataValue }}</Option>
        </Select>
        <span class="ml-xs mr-xs font-blue">-</span>
        <Select
          v-model="searchData.indexId"
          placeholder="请选择指标"
          filterable
          clearable
          class="width-md"
          :disabled="!searchData.indexModule"
          @on-change="onChangeIndexId"
        >
          <Option v-for="(item, index) in filterIndexData" :key="index" :value="`${item.id}`">{{
            item.indexName
          }}</Option>
        </Select>
        <span class="ml-xs mr-xs font-blue">-</span>
        <Select
          v-model="searchData.errorReasonItem"
          filterable
          clearable
          multiple
          :max-tag-count="1"
          placeholder="请选择异常类型"
          :disabled="!searchData.indexId"
          class="width-md"
        >
          <Option v-for="(item, index) in errorList" :key="index" :value="item">{{ item }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-md mb-sm" label="工单编号" v-if="showSearchKeys.includes('workOrderNum')">
        <Input v-model="searchData.workOrderNum" clearable class="width-md" placeholder="请输入工单编号"></Input>
      </ui-label>
      <ui-label class="inline mr-md" label="工单名称" v-if="showSearchKeys.includes('workOrderName')">
        <Input v-model="searchData.workOrderName" clearable class="width-md" placeholder="请输入工单名称"></Input>
      </ui-label>
      <ui-label class="inline mr-md" label="任务名称" v-if="showSearchKeys.includes('taskName')">
        <Input v-model="searchData.taskName" clearable class="width-md" placeholder="请输入工单所属任务名称"></Input>
      </ui-label>
      <ui-label class="inline mr-md mb-sm" label="签收状态" v-if="showSearchKeys.includes('leftSignDictKey')">
        <Select v-model="searchData.signDictKey" placeholder="请选择签收状态" class="width-md">
          <Option v-for="(item, index) in signStatusList" :key="index" :value="item.dataKey">{{
            item.dataValue
          }}</Option>
        </Select>
      </ui-label>

      <div class="fr">
        <UiSearch class="ui-search mr-md" v-model="searchModel" v-if="isShowMoreSearch">
          <template #content>
            <div class="search-content">
              <ui-label class="inline mr-md mb-sm" label="签收状态" v-if="showSearchKeys.includes('signDictKey')">
                <Select v-model="searchData.signDictKey" placeholder="请选择签收状态" class="width-md">
                  <Option v-for="(item, index) in signStatusList" :key="index" :value="item.dataKey">{{
                    item.dataValue
                  }}</Option>
                </Select>
              </ui-label>
              <ui-label class="inline mr-md" label="工单创建人" v-if="showSearchKeys.includes('createName')">
                <Input v-model="searchData.createName" class="width-md" placeholder="请输入创建人名称"></Input>
              </ui-label>
              <ui-label
                class="inline mr-md"
                label="当前处理人"
                v-if="showSearchKeys.includes('currentTaskReceiverName')"
              >
                <Input
                  v-model="searchData.currentTaskReceiverName"
                  class="width-md"
                  placeholder="请输入处理人名称"
                ></Input>
              </ui-label>
              <ui-label class="inline mr-lg mb-sm" label="设备组织机构" v-if="showSearchKeys.includes('orgCode')">
                <api-organization-tree
                  :select-tree="selectOrgTree"
                  :custorm-node="true"
                  :custorm-node-data="custormNodeData"
                  @selectedTree="selectedOrgTree"
                  placeholder="请选择组织机构"
                >
                </api-organization-tree>
              </ui-label>
              <ui-label class="inline mr-md mb-sm" label="设备所属项目" v-if="showSearchKeys.includes('belongProject')">
                <Input v-model="searchData.belongProject" class="width-md" placeholder="请输入所属项目"></Input>
              </ui-label>
              <ui-label class="inline mr-md" label="设备IP地址" v-if="showSearchKeys.includes('ipAddr')">
                <Input v-model="searchData.ipAddr" class="width-md" placeholder="请输入IP地址"></Input>
              </ui-label>
              <ui-label class="inline mr-md" label="设备承建单位" v-if="showSearchKeys.includes('cjdw')">
                <Input v-model="searchData.cjdw" class="width-md" placeholder="请输入承建单位"></Input>
              </ui-label>
              <ui-label class="inline mr-md" label="设备维护单位" v-if="showSearchKeys.includes('whdw')">
                <Input v-model="searchData.whdw" class="width-md" placeholder="请输入维护单位"></Input>
              </ui-label>
              <ui-label class="inline mr-lg" label="紧急程度" v-if="showSearchKeys.includes('workLevel')">
                <Select class="width-md" v-model="searchData.workLevel" placeholder="请选择紧急程度" clearable>
                  <Option
                    :label="item.label"
                    :value="item.value"
                    v-for="(item, index) in workLevelOptions"
                    :key="`${item.value}-${index}`"
                  >
                    <div class="inline work-level-box mr-sm vt-middle" :class="item.style"></div>
                    <span>{{ item.label }}</span>
                  </Option>
                </Select>
              </ui-label>
            </div>
          </template>
        </UiSearch>
        <Button type="primary" @click="startSearch">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
    </div>
    <div class="btn-div mb-sm">
      <div class="btndiv-l">
        <slot name="checkAll"></slot>
      </div>
      <div class="btndiv-r">
        <Button
          type="primary"
          @click="$emit('addData')"
          v-permission="{ route: this.$route.name, permission: 'create' }"
        >
          <i class="icon-font icon-tianjia f-12"></i>
          <span class="vt-middle ml-sm">新增工单</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'Assign', true)"
          v-permission="{ route: this.$route.name, permission: 'assign' }"
          :loading="batchLoadingObj.Assign"
        >
          <i class="icon-font icon-piliangzhipai f-12"></i>
          <span class="vt-middle ml-sm">批量指派</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'batchSignTips', true)"
          v-permission="{ route: this.$route.name, permission: 'sign' }"
          :loading="batchLoadingObj.Sign"
        >
          <i class="icon-font icon-piliangqianshou font-white f-12"></i>
          <span class="vt-middle ml-sm">批量签收</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'batchReport', true)"
          :loading="batchLoadingObj.Report"
          v-permission="{ route: this.$route.name, permission: 'report' }"
        >
          <i class="icon-font icon-piliangbaobei f-12"></i>
          <span class="vt-middle ml-sm">批量报备</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'Deal', true)"
          v-permission="{ route: this.$route.name, permission: 'processing' }"
          :loading="batchLoadingObj.Deal"
        >
          <i class="icon-font icon-piliangchuli f-12"></i>
          <span class="vt-middle ml-sm">批量处理</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'Close', true)"
          v-permission="{ route: this.$route.name, permission: 'close' }"
          :loading="batchLoadingObj.Close"
        >
          <i class="icon-font icon-piliangguanbi f-12"></i>
          <span class="vt-middle ml-sm">批量关闭</span>
        </Button>
        <Button type="primary" class="ml-sm" @click="$emit('exportExcel')">
          <i class="icon-font icon-daochu f-12"></i>
          <span class="vt-middle ml-sm">导出</span>
        </Button>
        <Button
          type="primary"
          class="ml-sm"
          @click="$emit('handleBatchOptions', 'batchDelete', true)"
          :loading="batchLoadingObj.Delete"
          v-permission="{ route: this.$route.name, permission: 'delete' }"
        >
          <i class="icon-font icon-piliangshanchu f-12"></i>
          <span class="vt-middle ml-sm">批量删除</span>
        </Button>
      </div>
    </div>
  </div>
</template>
<script>
import user from '@/config/api/user';
import governancetask from '@/config/api/governancetask';
import { mapGetters } from 'vuex';
import fillReportMixin from '@/views/disposalfeedback/governanceorder/util/fillReportMixin';
import { workLevelOptions } from '@/views/disposalfeedback/governanceorder/util/enum.js';
export default {
  props: {
    commonSearchData: {},
    statisticsDetail: {},
    batchLoadingObj: {
      Deal: false,
      Assign: false,
      Close: false,
      Sign: false,
      Delete: false,
    },
    isShowMoreSearch: {
      type: Boolean,
      default: true,
    },
    showSearchKeys: {
      //所展示的搜索条件
      type: Array,
      default: () => {
        return [
          'indexId',
          'workOrderNum',
          'workOrderName',
          'taskName',
          'signDictKey',
          'orgCode', //组织机构
          'belongProject',
          'ipAddr',
          'cjdw',
          'whdw',
          'workLevel',
        ];
      },
    },
  },
  mixins: [fillReportMixin],
  data() {
    return {
      searchModel: false,
      defaultSearchData: {
        workOrderNum: '',
        workOrderName: '',
        createName: '',
        currentTaskReceiverName: '',
        signDictKey: '',
        belongProject: '',
        ipAddr: '',
        cjdw: '',
        whdw: '',
        orgCode: null,
        workLevel: null,
        errorReasonItem: [],
        indexModule: '',
        indexId: '',
        taskName: '',
      },
      searchData: {},
      errorList: [],
      signStatusList: [],
      state: ['100'],
      stateOptions: [
        {
          label: '全部',
          value: 'qb',
          total: 0,
        },
        {
          label: '未指派',
          value: 'wzp',
          total: 0,
        },
        {
          label: '待签收',
          value: 'dqs',
          total: 0,
        },
        {
          label: '超时未签收',
          value: 'cswqs',
          total: 0,
        },
        {
          label: '超时处理',
          value: 'cscl',
          total: 0,
        },
        {
          label: '待处理',
          value: 'dcl',
          total: 0,
        },
        {
          label: '超时未处理',
          value: 'cswcl',
          total: 0,
        },
        {
          label: '按时处理',
          value: 'ascl',
          total: 0,
        },
        {
          label: '检测不通过',
          value: 'jcbtg',
          total: 0,
        },
        {
          label: '超时签收',
          value: 'csqs',
          total: 0,
        },
        {
          label: '按时签收',
          value: 'asqs',
          total: 0,
        },
        {
          label: '已处理',
          value: 'ycl',
          total: 0,
        },
        {
          label: '一次处理通过',
          value: 'yccltg',
          total: 0,
        },
        {
          label: '多次处理通过',
          value: 'dccltg',
          total: 0,
        },
        {
          label: '待关闭',
          value: 'dgb',
          total: 0,
        },
        {
          label: '已关闭',
          value: 'ygb',
          total: 0,
        },
      ],
      myStateOptions: [
        {
          label: '全部',
          value: 'qb',
          total: 0,
        },
        {
          label: '待签收',
          value: 'dqs',
          total: 0,
        },
        {
          label: '超时未签收',
          value: 'cswqs',
          total: 0,
        },
        {
          label: '待处理',
          value: 'dcl',
          total: 0,
        },
        {
          label: '超时未处理',
          value: 'cswcl',
          total: 0,
        },
        {
          label: '检测不通过',
          value: 'jcbtg',
          total: 0,
        },
      ],
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
    workLevelOptions() {
      return workLevelOptions;
    },
  },
  created() {
    this.searchData = this.defaultSearchData;
    this.getDictData(); //mixin
    this.copySearchDataMx(this.searchData);
    this.getDictDataGroup();
  },
  methods: {
    onChangeIndexModule(val) {
      this.searchData.indexId = '';
      this.searchData.errorReasonItem = [];
      this.getEvaluationIndexByIndexModule(val); //fillReportMixin.js
    },
    onChangeIndexId(val) {
      this.searchData.errorReasonItem = [];
      if (!val) return;
      this.getErrorDictData();
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.startSearch();
      // countList
    },
    // 获取字典数据
    async getDictDataGroup() {
      try {
        let res = await this.$http.post(user.queryDataByKeyTypes, ['wordorder_sign']);
        this.signStatusList = res.data.data[0]['wordorder_sign'];
      } catch (error) {
        console.log(error);
      }
    },
    startSearch() {
      /*let params = Object.assign({}, this.searchData);
      if (!!this.errorReasonItem) {
        params.errorReasonItem = [this.errorReasonItem];
      } else {
        params.errorReasonItem = [];
      }*/
      this.$emit('search', this.searchData);
    },
    clear() {
      // this.errorReasonItem = '';
      this.resetSearchDataMx(this.searchData);
      // let params = Object.assign({}, this.searchData);
      /* if (!!this.errorReasonItem) {
        params.errorReasonItem = [this.errorReasonItem];
      } else {
        params.errorReasonItem = [];
      }*/
      this.$emit('clearAll', this.searchData);
    },
    async getErrorDictData() {
      try {
        let params = {
          indexModule: this.searchData.indexModule,
          indexId: this.searchData.indexId,
          ...this.commonSearchData,
        };
        let { data } = await this.$http.post(governancetask.getErrorDictData, params);
        this.errorList = data.data;
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    statisticsDetail: {
      async handler(val) {
        await this.getEvaluationIndex();
        if (!val) {
          this.searchData.indexModule = '';
          this.searchData.indexId = '';
          //因为需要外部控制搜索，故原来的传search事件改为合并searchdata的事件
          this.$emit('assingSearchData', this.searchData);
          return;
        }
        if (val) {
          let { indexId, indexModule } = val;
          this.searchData.indexModule = indexModule;
          this.searchData.indexId = indexId;
          this.getEvaluationIndexByIndexModule(indexModule); //fillReportMixin.js
          this.onChangeIndexId();
          this.$emit('assingSearchData', this.searchData);
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiSearch: require('@/components/ui-search.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
@import '~@/views/disposalfeedback/governanceorder/components/work-level-tag/index.less';

.work-level-box {
  height: 16px;
  width: 16px;
}

.null-data-text {
  color: var(--color-title);
  line-height: 1.5;
  text-align: center;
}
.search-and-button {
  position: relative;
  .btn-div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 1px solid var(--border-color);
    padding-top: 10px;
  }
  @{_deep}.ivu-select-item {
    width: 180px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  @{_deep} .search-content {
    padding: 20px 20px 10px 20px !important;
  }
}
</style>
