<template>
  <div class="function-distribute">
    <tab-title v-model="activeValue" :data="tabData" @on-change="onChangeTitle">
      <template #filter>
        <!-- <Button class="mr-sm download-scheme" type="text" @click="jump">查看详情</Button> -->
        <drop-select
          v-model="searchData.sbdwlxList"
          multiple
          :data="dropData"
          @on-change="handleChangeSelect"
        ></drop-select>
      </template>
    </tab-title>
    <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
      <draw-echarts
        class="charts"
        :echart-option="propertyEchart"
        :echart-style="echartStyle"
        ref="qualityChartRef"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
      <next-chart-icon
        v-if="echartList.length > echartsPageSize"
        @scrollRight="scrollRight('qualityChartRef', echartList, [], echartsPageSize)"
      ></next-chart-icon>
    </div>
  </div>
</template>

<script>
import { oneType, twoType } from '../utils/enum';
import dataZoom from '@/mixins/data-zoom';
import assetstatisticsMultipleBar from '@/views/viewassets/assetstatistics/utils/echarts-config-bar.js';
import equipmentassets from '@/config/api/equipmentassets';
import viewassets from '@/config/api/viewassets';

export default {
  mixins: [dataZoom],
  name: 'pie-chart',
  components: {
    NextChartIcon: require('../components/next-chart-icon.vue').default,
    DropSelect: require('../components/drop-select.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    TabTitle: require('../components/tab-title.vue').default,
  },
  props: {
    homeConfig: {
      default: () => ({}),
    },
    activeTab: {
      default: '1',
    },
  },
  data() {
    let dropData = oneType.map((item) => {
      return {
        label: item.name,
        id: item.id,
      };
    });
    return {
      echartsPageSize: 20,
      searchData: {
        sbdwlxList: [],
      },
      dropData: dropData,
      activeValue: 'deviceStatus',
      tabData: [{ label: '采集区域类型分布', id: 'deviceStatus' }],
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      propertyEchart: {},
      echartsLoading: false,
      echartList: [],
      areaData: [],
    };
  },
  async created() {
    await this.getKeyAreaList();
  },
  watch: {
    activeTab: {
      async handler() {
        await this.postQueryStatisticsByAmount();
        await this.handleQualityCharts();
      },
    },
    homeConfig: {
      async handler(val) {
        if (Object.keys(val).length > 0) {
          await this.postQueryStatisticsByAmount();
          await this.handleQualityCharts();
        }
      },
      deep: true,
    },
  },
  methods: {
    jump() {
      this.$router.push({
        name: 'evaluationoResult',
        query: {
          showResult: 1,
          regionCode: this.homeConfig.regionCode,
          statisticType: 'REGION',
          taskSchemeId: this.homeConfig.taskSchemeId,
          indexId: 1008,
          indexType: 'BASIC_CJQY_QUANTITY_STANDARD',
          //batchId: batchId,
        },
      });
    },
    //获取重点采集区域类型
    async getKeyAreaList() {
      if (this.areaData.length > 0) return;
      try {
        let {
          data: { data },
        } = await this.$http.post(viewassets.getKeyEquipmentConfig, {});
        this.areaData = data;
      } catch (e) {
        console.log(e);
      }
    },
    setEchartsList(data) {
      this.echartList = this.areaData.map((item) => {
        let area = data.find((value) => value.sbcjqy === item.code);
        return {
          sbcjqy: item.code,
          sbcjqyName: item.name,
          videoAmount: area ? area.videoAmount : 0,
          faceAmount: area ? area.faceAmount : 0,
          vehicleAmount: area ? area.vehicleAmount : 0,
          videoAmountStandard: area ? area.videoAmountStandard : -1,
          faceAmountStandard: area ? area.faceAmountStandard : -1,
          vehicleAmountStandard: area ? area.vehicleAmountStandard : -1,
        };
      });
    },
    async handleChangeSelect() {
      await this.postQueryStatisticsByAmount();
      await this.handleQualityCharts();
    },
    async onChangeTitle() {
      await this.postQueryStatisticsByAmount();
      await this.handleQualityCharts();
    },
    initCharts() {
      setTimeout(() => {
        this.setDataZoom('qualityChartRef', [], this.echartsPageSize);
      });
    },
    /**
     *     SBCJQY_TYPE_CODE("sbcjqy_type_code","采集类型区域分布")
     *     ,AMOUNT_CHANGE_TREND("amount_change_trend","数量变化趋势")
     *     ,PHYSTATUS_OVERVIEW_LIST("phystatus_overview_list","设备状态统计区域分布")
     *     ,IMPORTANT_OVERVIEW_LIST("important_overview_list","重点类型统计区域分布")
     *     ,CHECKSTATUS_OVERVIEW_LIST("checkstatus_overview_list","资产质量统计区域分布")
     *     ,VIDEOSTATUS_OVERVIEW_LIST("videostatus_overview_list","视频监控在线统计区域分布")
     *     ,FACESTATUS_OVERVIEW_LIST("facestatus_overview_list","人脸卡口在线统计区域分布")
     *     ,VEHICLESTATUS_OVERVIEW_LIST("vehiclestatus_overview_list","车辆卡口在线统计区域分布")
     * @returns {Promise<void>}
     */
    async postQueryStatisticsByAmount() {
      try {
        this.echartsLoading = true;
        let { regionCode } = this.homeConfig;
        let params = {
          civilCode: regionCode,
          type: this.activeTab,
          dataKey: 'sbcjqy_type_code',
          ...this.searchData,
        };
        let {
          data: { data },
        } = await this.$http.post(equipmentassets.postQueryStatisticsBySbcjqy, params);
        this.setEchartsList(data || []);
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    handleQualityCharts() {
      let xAxisData = [];
      let legendData = [];
      let dataList = [];
      let seriesData = twoType.map((item) => {
        legendData.push(item.name); // lengend
        return {
          name: item.name,
          filedName: item.areaFiledName,
          itemStyle: {
            normal: { color: item.color },
          },
          barWidth: 18,
          data: [],
        };
      });
      this.echartList.forEach((item) => {
        xAxisData.push({
          value: item.sbcjqy,
        });
        seriesData.forEach((one) => {
          let reallyItem = {
            // 不达标需要展示小圆点
            label: {
              show: true,
              position: [6, -5],
              color: 'transparent',
              rich: {
                b: {
                  backgroundColor: 'var(--color-warning)',
                  height: 8,
                  width: 8,
                  borderRadius: 8,
                },
              },
              formatter: function () {
                if (item[`${one?.filedName}Standard`] < 0) {
                  return ['{b|' + '}'];
                } else {
                  return '';
                }
              },
            },
            value: item[one?.filedName] ?? 0,
            isQualified: !(item[`${one?.filedName}Standard`] < 0),
          };
          // 需要补充toolTip的标题内容，插槽名字: supplement
          reallyItem.outerSlot = {
            supplement: () => <span class="ml-md">{item.sbcjqyName}</span>,
          };
          // 如果不达标，需要展示slot的内容
          if (!reallyItem.isQualified) {
            reallyItem.slotName = item.sbcjqy;
            reallyItem.slotRender = () => <span class="font-warning font-weight">不达标</span>;
          }
          one.data.push(reallyItem);
          dataList.push({
            value: reallyItem.value,
          });
        });
      });
      let data = {
        titleText: '设备数量',
        legendData: legendData,
        xAxisData: xAxisData,
        seriesData: seriesData,
        axisLabelFormatter: (params) => {
          if (params?.length > 6) {
            return params.substr(0, 6) + '...';
          } else {
            return params;
          }
        },
      };
      this.propertyEchart = assetstatisticsMultipleBar(data);
      this.initCharts();
    },
  },
};
</script>

<style lang="less" scoped>
.function-distribute {
  width: 100%;
  height: 320px;
  background: var(--bg-sub-echarts-content);
  box-shadow: var(--shadow-sub-echarts-content);
  .icon-chakanxiangqing {
    font-size: 30px;
    margin-right: 10px;
    color: var(--color-filter-funnel);
  }
  .body-container {
    width: 100%;
    position: relative;
    height: calc(100% - 30px) !important;

    .charts {
      width: 100%;
      height: 100% !important;
    }
  }
}
</style>
