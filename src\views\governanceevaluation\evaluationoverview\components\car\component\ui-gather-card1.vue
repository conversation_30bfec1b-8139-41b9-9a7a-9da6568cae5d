<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="gather">
    <div class="ui-gather-card">
      <div class="ui-gather-card-left">
        <div class="img ui-images1">
          <ui-image :src="list.imageUrl" />
          <p class="shadow-box" style="z-index: 11" title="查看大图">
            <i class="icon-font icon-yichang search-icon mr-xs base-text-color" @click="viewBigPic(list.imageUrl)"></i>
          </p>
          <p v-if="!!list.objectSnapshotId" class="shadow-copy" style="z-index: 11" :title="list.objectSnapshotId">
            <i class="base-text-color f-14">ID:</i>
            <span class="base-text-color id-num ellipsis f-14">{{ list.objectSnapshotId }}</span>
            <span class="copy-text f-14" v-clipboard="list.objectSnapshotId" v-clipboard:callback="copy">复制</span>
          </p>
        </div>
      </div>
      <div class="ui-gather-card-right">
        <div class="ui-gather-card-right-items" v-for="(item, index) in cardInfoLable" :key="index">
          <p class="ui-gather-card-right-items-p">
            <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
            <span
              v-if="!item.algorithm"
              :title="list[item.value] || '缺失'"
              class="ui-gather-card-right-item-value"
              :style="{ color: list[item.value] ? '#ffffff' : '#C43D2C' }"
              >{{ list[item.value] || '缺失' }}</span
            >
            <span
              v-else
              :title="list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失')"
              class="ui-gather-card-right-item-value"
              :style="{
                color: list[item.value] ? '#ffffff' : '#C43D2C',
              }"
            >
              {{ list[item.value] | filterType(algorithm(item.algorithm), 'dictKey', 'dictValue', '缺失') }}</span
            >
          </p>
        </div>
      </div>
    </div>
    <div class="gather-bottom" v-if="cardInfoImage.length > 0">
      <div
        v-for="(item, index) in cardInfoImage"
        :key="index"
        :class="index == 0 ? 'gather-bottom-left' : 'gather-bottom-right'"
      >
        <i v-if="index == 0" class="icon-font icon-shijian gather-bottom-icon" title="抓拍时间"></i>
        <i v-else class="icon-font icon-dizhi gather-bottom-icon" title="抓拍地点"></i>
        <span :title="list[item.value] || '缺失'">{{ list[item.value] || '缺失' }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  computed: {
    cardInfoImage() {
      let array = [];
      this.cardInfo.map((val) => {
        if (val.type === 'image') {
          array.push(val);
        }
      });
      return array;
    },
    cardInfoLable() {
      let array = [];
      this.cardInfo.map((val) => {
        if (!val.type) {
          array.push(val);
        }
      });
      return array;
    },
    ...mapGetters({
      colorType: 'algorithm/colorType',
      vehicleBandType: 'algorithm/vehicleBandType',
      vehicleClassType: 'algorithm/vehicleClassType',
      plateClassType: 'algorithm/plateClassType',
    }),
  },
  created() {},
  mounted() {},
  methods: {
    copy() {
      this.$Message.success('复制成功');
    },
    viewBigPic(item) {
      this.$emit('bigImageUrl', item);
    },
    algorithm(item) {
      return this[item];
    },
  },
  watch: {},
  components: {
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.gather {
  margin-bottom: 10px;
  background: #0f2f59;
  height: max-content;
  .ui-gather-card {
    padding: 10px;
    display: flex;
    &-left {
      width: 90px;
      height: 90px;
      margin-right: 10px;
      .img {
        position: relative;
        width: 100%;
        height: 90px;
      }
    }
    &-right {
      flex: 1;
      width: 50%;
      display: flex;
      flex-wrap: wrap;
      &-items {
        margin-bottom: 8px;
        font-size: 14px;
        width: 50%;
        height: 20px;
        &-label {
          color: #8797ac;
        }
        &-value {
          color: #ffffff;
        }
      }
    }
  }
  .ui-gather-card-right-items-p {
    // margin-bottom: 6px;
  }
  .ui-gather-card-right-item-value {
    color: #ffffff;
    color: rgb(255, 255, 255);
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    display: inline-block;
    text-overflow: ellipsis;
    position: relative;
    top: 5px;
  }
  .shadow-box {
    height: 28px;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    bottom: 0;
    display: none;
    padding-left: 10px;
    > i:hover {
      color: var(--color-primary);
    }
  }
  .shadow-copy {
    height: 28px;
    line-height: 28px;
    width: 100%;
    background: rgba(0, 0, 0, 0.3);
    position: absolute;
    top: 30px;
    display: none;
    padding-left: 10px;
    .id-num {
      width: 30px;
      vertical-align: top;
      display: inline-block;
    }

    i {
      vertical-align: top;
    }
    .copy-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: sub;
    }
    @media screen and (max-width: 1366px) {
      .id-num {
        vertical-align: top;
        display: inline-block;
        width: 15px; /*no*/
      }
      .copy-text {
        color: var(--color-primary);
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }
  .img:hover {
    .shadow-box {
      display: block;
    }
    .shadow-copy {
      display: block;
    }
  }
  .gather-bottom {
    color: #8797ac;
    border-top: 1px solid #23426b;
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    .gather-bottom-left {
      float: left;
      width: 40%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .gather-bottom-right {
      float: right;
      max-width: 50%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &-icon {
      font-size: 12px;
      margin-right: 4px;
      position: relative;
      top: -1px;
    }
  }
}
.ui-gather-card-right-item-label {
  color: #8797ac;
}
</style>
