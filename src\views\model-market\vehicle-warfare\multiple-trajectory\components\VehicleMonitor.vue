<template>
  <div class="map_detail" v-show="isShow">
    <div class="up"></div>
    <div class="infowindow_container">
      <div class="infowindow_head">
        <div class="title" :title="vehicleDetail.deviceName">
          {{ vehicleDetail.deviceName }}
        </div>
        <div class="title-btns">
          <i
            class="iconfont icon-close closewin"
            title="关闭"
            @click="closeInfoWindow"
          />
        </div>
      </div>
      <div class="infowindow_content">
        <i
          class="iconfont icon-doubleleft change_btn prev"
          @click="changeMarker('up')"
        />
        <i
          class="iconfont icon-doubleright change_btn next"
          @click="changeMarker('down')"
        />
        <div class="left_img_area">
          <div class="big_img_content">
            <div class="big_img">
              <img class="detail_img" v-viewer :src="vehicleDetail.sceneImg" />
            </div>
          </div>
        </div>
        <div class="right_info_area">
          <ul class="info_detail">
            <li class="declare" :title="vehicleDetail.absTime">
              <span>通过时间：</span>
              {{ vehicleDetail.absTime }}
            </li>
            <li class="declare" :title="vehicleDetail.deviceName">
              <span>抓拍地点：</span>
              {{ vehicleDetail.deviceName }}
            </li>
            <li class="declare" :title="vehicleDetail.plateNo">
              <span>车牌号：</span>
              {{ vehicleDetail.plateNo }}
            </li>
            <li class="declare blue">
              <span>车牌颜色：</span>
              {{ vehicleDetail.plateColor | commonFiltering(plateColorList) }}
            </li>
            <li class="declare blue">
              <span>车辆类型：</span>
              {{ vehicleDetail.vehicleType | commonFiltering(vehicleTypeList) }}
            </li>
            <li
              class="declare blue"
              :title="
                vehicleDetail.vehicleBrand | commonFiltering(vehicleBrandList)
              "
            >
              <span>车辆品牌：</span>
              {{
                vehicleDetail.vehicleBrand | commonFiltering(vehicleBrandList)
              }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
var self = null;
import { mapActions, mapGetters, mapMutations } from "vuex";
export default {
  name: "vehicleMonitor",
  props: {
    value: {
      type: Object,
      default: function () {
        return {};
      },
    },
  },
  computed: {
    ...mapGetters({
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      plateColorList: "dictionary/getLicensePlateColorList",
      vehicleBrandList: "dictionary/getVehicleBrandList", // 车辆品牌
    }),
  },
  async created() {
    await this.getDictData();
  },
  data: function () {
    return {
      isShow: false,
      vehicleDetail: {},
    };
  },
  watch: {
    value: {
      handler: function (val) {
        if (val) {
          this.isShow = val.isShow;
          this.showMapDetail(val);
        }
      },
      deep: true,
    },
  },
  methods: {
    showMapDetail: function (data) {
      //若该通行记录的点位信息不存在，则不显示大图详情
      if (!data.geoPoint.lon || !data.geoPoint.lat) {
        this.$Message.warning("该记录的点位信息缺失！");
        return;
      }
      this.vehicleDetail = data;
    },
    closeInfoWindow: function () {
      this.isShow = false;
      this.$emit("closeWin", true);
    },
    changeMarker: function (type) {
      this.$emit("changeMarker", type);
    },
  },
};
</script>
<style lang="less">
.map_detail {
  background-color: transparent;
  height: inherit;
  padding: 0 17px;
  .infowindow_container {
    cursor: default;
    float: left;
    background: #ffffff;
    .infowindow_head {
      width: 540px;
      height: 45px;
      .title {
        float: left;
        margin-left: 15px;
        line-height: 45px;
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .title-btns {
        float: right;
        margin-right: 15px;
        height: 45px;
        .item {
          float: left;
          width: 16px;
          height: 16px;
          margin-top: 12px;
          margin-left: 10px;
          cursor: pointer;
        }
        .closewin {
          cursor: pointer;
        }
      }
    }
    .infowindow_content {
      padding: 0 15px 15px 15px;
      font-size: 0;
      position: relative;
      .left_img_area {
        display: inline-block;
        width: 270px;
        margin-right: 12px;
        font-size: 0;
        text-align: center;
        vertical-align: middle;
        .big_img_content {
          .big_img {
            position: relative;
            display: block;
            width: 270px;
            height: 260px;
            margin: 0 auto 12px auto;
            .detail_img {
              display: block;
              width: 100%;
              height: 100%;
            }
            .check_img {
              position: absolute;
              display: block;
              right: 10px;
              bottom: 10px;
              width: 80px;
              height: 25px;
              line-height: 25px;
              font-size: 12px;
              border-radius: 20px;
              color: #fff;
              background-color: rgba(59, 121, 201, 0.5);
            }
          }
        }
        .slide_img {
          position: relative;
          .slide_btn {
            position: absolute;
            top: 50%;
            width: 16px;
            height: 16px;
            transform: translate3d(0, -50%, 0);
            -webkit-transform: translate3d(0, -50%, 0);
          }
          .prev_arrow {
            left: 0;
            width: 16px;
            height: 16px;
          }
          .next_arrow {
            right: 0;
            width: 16px;
            height: 16px;
          }
          .img_wrap {
            width: 100%;
            margin: 0 auto;
          }
          .small_img {
            float: left;
            width: 60px;
            height: 50px;
            padding: 1px;
            border: 1px solid #ddd;
            margin-right: 3px;
            background: url("~@/assets/img/default-img/vehicle_default.png")
              no-repeat center;
            img {
              width: 61px;
              height: 46px;
            }
            &:hover {
              border: 1px solid #ff8d33;
            }
          }
          .small_img.img_hover {
            border: 1px solid #ff8d33;
          }
          .small_img:last-child {
            margin-right: 0;
          }
        }
      }
      .right_info_area {
        display: inline-block;
        width: 210px;
        font-size: 12px;
        overflow: hidden;
        vertical-align: top;
        .declare {
          width: 100%;
          line-height: 24px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .blue {
          color: #0088cc;
        }
      }
      .change_btn {
        position: absolute;
        top: 50%;
        width: 20px;
        height: 20px;
        transform: translate3d(0, -50%, 0);
        -webkit-transform: translate3d(0, -50%, 0);
        cursor: pointer;
        background: #c5c8ce;
        border-radius: 50px;
        text-align: center;
        vertical-align: middle;
        line-height: 20px;
        color: white;
      }
      .prev {
        left: -17px;
        // .x-pic-icon30-next-circle-normal;
        // &:hover {
        //     .x-pic-icon30-next-circle-hover;
        // }
      }
      .next {
        right: -17px;
        // .x-pic-icon30-prev-circle-normal;
        // &:hover {
        //     .x-pic-icon30-prev-circle-hover;
        // }
      }
    }
  }
}
.olFramedCloudPopupContent {
  border-color: #e1e1e1 !important;
  background: transparent !important;
}
</style>
