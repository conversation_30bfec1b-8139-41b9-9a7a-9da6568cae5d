/* * 地图 **/
<template>
  <div class="map-boxs">
    <div :id="mapId" class="map"></div>
    <Modal
      ref="deviceModal"
      class-name="domModal"
      v-model="deviceModalOption.open"
      reset-drag-position
      footer-hide
      draggable
      sticky
      :title="deviceModalOption.title"
      :width="deviceModalOption.width"
      :styles="{ top: 0, width: 'auto' }"
      center
      :mask="false"
      @on-cancel="closeDeviceModal"
    >
      <component
        :ref="sectionMenuName"
        :is="sectionMenuName"
        :map-dom-data="mapDomData"
        :is-map-click="isMapClick"
        @preDetial="(Index) => $emit('update:currentClickIndex', Index)"
        @nextDetail="(Index) => $emit('update:currentClickIndex', Index)"
        @close="closeDeviceModal"
        :clickIndexId="clickIndexId"
      ></component>
    </Modal>
  </div>
</template>

<script>
import { NPGisMapMain } from "@/map/map.main";
import { mapGetters, mapActions, mapMutations } from "vuex";
import { LayerType, siteType } from "@/map/core/enum/LayerType.js";
import axios from "axios";
import vehicle from "@/views/operations-on-the-map/map-default-page/components/map-dom/vehicle-map-dom.vue";
let mapMain = {
  carPath: null, //行车轨迹
  dayNight: null, //昼伏夜出
  footholdAnalysis: null, //落脚点
  "frequently-night-out": null, //频繁夜出
  faceFootholdAnalysis: null, //落脚点
};
let mouseInfoWindow = [];
let vehicleDot = null;
let vehicleLayer = null;
let footholdLayer = null;
let trackLayer = null; // 轨迹撒点
let animationLine = null;
let _wonderLayer = null; // 轨迹连线
let heatLayer = null; //热力图
export default {
  name: "",
  components: {
    vehicle,
  },
  props: {
    // 是否禁止地图的滚动条事件
    disableScroll: {
      type: Boolean,
      default: false,
    },
    // 当前点击的轨迹节点
    currentClickIndex: {
      type: Number,
      default: 0,
    },
    // 切换类型 加载弹框
    sectionName: {
      type: String,
      default: "vehicle",
    },
    // 点击同行人(重合位置)
    peerIndex: {
      type: Number,
      default: -1,
    },
    // 资源图层-选中的图层名称
    layerCheckedNames: {
      type: Array,
      default: () => [],
    },
    // 行车轨迹
    trackPoints: {
      type: Array,
      default: () => [],
    },
    // 建多个地图
    mapType: {
      type: String,
      default: "",
    },
    cutIcon: {
      type: String,
      default: "",
    },
    // 落脚点
    // 标注数据
    footholdData: {
      default: () => {
        return [];
      },
      type: Array,
    },
    currentFootholdArea: null,
  },
  data() {
    return {
      mapId: "mapId" + Math.random(),
      mapDomData: {},
      sectionMenuName: "", // 当前选中的menuName
      points: [],
      clickIndexId: 0,
      footholdArea: null,
      footholdMarkers: [],
      RADIUS: 1000,
      currentBlueMarkerIds: "",
      mapFlag: [
        "A",
        "B",
        "C",
        "D",
        "E",
        "F",
        "G",
        "H",
        "I",
        "J",
        "K",
        "L",
        "M",
        "N",
        "O",
        "P",
        "Q",
        "R",
        "S",
        "T",
      ],
      deviceModalOption: {
        open: false,
        title: "抓拍详情",
        width: "950px",
      },
      isMapClick: false,
      markers: [],
    };
  },
  watch: {
    // 列表点击的index
    currentClickIndex: {
      handler(newVal) {
        if (newVal == -1) {
          return;
        }
        const { currentClickIndex, points = [], sectionMenuName } = this;
        const pointItem = (points.length && points[currentClickIndex]) || {};
        // 缺 经纬度 地图不展示点位
        if (
          !pointItem.geoPoint ||
          !pointItem.geoPoint.lon ||
          !pointItem.geoPoint.lat
        ) {
          return this.$Message.warning("经纬度信息不全");
        }
        this.sectionMenuName = pointItem["type"]
          ? pointItem.type
          : this.sectionName + "";
        // 普通搜索弹出模态框
        this.$nextTick(() => {
          if (pointItem) {
            if (pointItem["type"]) {
              this.selectItem(
                pointItem,
                this.sectionMenuName,
                null,
                false,
                false,
                true
              );
            } else {
              this.selectItem(pointItem, this.sectionMenuName, null, false);
            }
          }
          // 活动轨迹下方切换
          let cutIcon = this.cutIcon == "track" ? false : true;
          pointItem &&
            this.$refs[this.sectionMenuName]
              .init(points[currentClickIndex], pointItem, "", cutIcon, true)
              .then((res) => {
                if (res.data) {
                } else {
                  this.$Message.warning("暂无数据");
                }
              });
        });
        this.closeAllInfoWindow();
        // 人体和非机动车 recordId 人脸和车辆 id
        if (
          this.sectionMenuName == "face" ||
          this.sectionMenuName == "vehicle"
        ) {
          this.clickIndexId = this.points[this.currentClickIndex].id;
        } else {
          this.clickIndexId = this.points[this.currentClickIndex].recordId;
        }
        // this.sprinkleHandler(points)
      },
    },
    trackPoints: {
      handler(newVal) {
        if (newVal.length) {
          newVal.map((item, index) => {
            item.Index = index + 1;
          });
          this.points = [...newVal];
          this.closeAllInfoWindow(); //关闭弹窗
          this.clearPoint(); //重置
          this.sprinkleHandler(this.points); //撒点
          setTimeout(() => {
            this.rePosition(this.points);
          }, 200);
        } else {
          this.points = [];
          this.closeAllInfoWindow(); //关闭弹窗
          this.clearPoint(); //重置
        }
      },
      immediate: true,
    },
    sectionName: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.sectionMenuName = newVal + "";
        }
      },
      immediate: true,
    },
    footholdData() {
      this._updateData();
    },
    currentFootholdArea() {
      this.showFootholdArea();
    },
  },
  computed: {
    ...mapGetters({
      mapConfig: "common/getMapConfig",
      mapStyle: "common/getMapStyle",
      mapObj: "systemParam/mapObj",
      globalObj: "systemParam/globalObj",
      resourceCoverage: "map/getResourceCoverage",
    }),
    // 图层名称数组
    layerTypeList() {
      let list = [];
      for (let key in LayerType) {
        list.push(LayerType[key].value);
      }
      for (let key in siteType) {
        list.push(siteType[key].value);
      }
      return list;
    },
  },
  created() {},
  async mounted() {
    await this.getMapConfig();
  },
  beforeDestroy(){
    _wonderLayer = null;
  },
  methods: {
    ...mapActions({
      setMapConfig: "common/setMapConfig",
      setMapStyle: "common/setMapStyle",
    }),
    ...mapMutations({
      setPeerData: "map/setPeerData",
    }),
    async getMapConfig() {
      try {
        await Promise.all([this.setMapConfig(), this.setMapStyle()]);
        this._initMap(this.mapConfig);
      } catch (err) {
        console.log(err);
      }
    },
    // 初始化地图
    _initMap(data, style) {
      this.$nextTick(() => {
        // 配置初始化层级
        mapMain[this.mapType] = new NPGisMapMain();
        const mapId = this.mapId;
        mapMain[this.mapType].init(mapId, data, style);
        // 禁止滚动条
        if (this.disableScroll) {
          mapMain[this.mapType].map.disableScrollWheelZoom();
        }
        this.$emit("createMapBack", mapMain[this.mapType].map);
        this.configDefaultMap();
      });
    },
    /**
     * 系统配置的中心点和层级设置
     */
    configDefaultMap() {
      let mapCenterPoint = this.globalObj.mapCenterPoint;
      let mapCenterPointArray = !!mapCenterPoint
        ? this.globalObj.mapCenterPoint.split("_")
        : "";
      let mapLayerLevel = parseInt(this.globalObj.mapLayerLevel) || 14;
      let point = mapMain[this.mapType].map.getCenter();
      if (!!mapCenterPointArray.length) {
        point = new NPMapLib.Geometry.Point(
          parseFloat(mapCenterPointArray[0]),
          parseFloat(mapCenterPointArray[1])
        );
      }
      mapMain[this.mapType].map.centerAndZoom(point, mapLayerLevel);
    },
    // 配置最大层级Symbomap
    addlimitLayerNum() {
      if (!!Number(this.mapObj.maxNumberOfLayer) || 600) {
        let allLayers = mapMain[this.mapType].map.getAllLayers.length;
        if (allLayers >= this.limitLayerNum) {
          this.$Message.error("已超过配置最大图层数量");
          return false;
        }
      }
      return true;
    },
    // 加载点位到地图上(资源图层)
    _initSystemPoints2Map(points) {
      this.$nextTick(() => {
        setTimeout(() => {
          // 加载点位
          mapMain[this.mapType].renderMarkers(
            mapMain[this.mapType].convertSystemPointArr3MapPoint(points),
            this.getMapEvents()
          );
        }, 1000);
      });
    },
    // 点击 设备
    getMapEvents() {
      const opts = {
        click: (marker) => {
          return;
        },
        mouseover: (marker) => {
          this.showMouseDom(marker);
        },
        mouseout: (marker) => {
          this.closeMouseInfoWindow();
        },
      };
      return opts;
    },
    closeMouseInfoWindow() {
      mouseInfoWindow.forEach((row) => {
        row.close();
      });
    },
    moveModal() {
      let zoomLat = 0.0005;
      let zoom = mapMain[this.mapType].map.getMaxZoom();
      // 获取不同弹框位置偏移量
      if (zoom == 19) {
        zoomLat = 0.0001;
      } else if (zoom == 18) {
        zoomLat = 0.0002;
      } else if (zoom == 17) {
        zoomLat = 0.0005;
      }
      return zoomLat;
    },
    // 点击图层展示弹框,isMapClick 地图上点击不展示左右切换详情
    selectItem(
      pointItem,
      sectionName,
      isMapClick = false,
      isAprink = false,
      distance = false,
      noZoom = false
    ) {
      if (isAprink) {
        // 点击列表后 切换地图显示图片
        this.sprinkleHandler([pointItem]);
      }
      pointItem.currentClickIndex = this.currentClickIndex;
      this.isMapClick = isMapClick;
      this.closeAllInfoWindow();
      this.sectionMenuName = !!sectionName ? sectionName : sectionMenuName;
      // 显示之前先清除其他提示框
      const { sectionMenuName } = this;
      // const point = new NPMapLib.Geometry.Point(+pointItem.lon + pointLon, +pointItem.lat + pointLat)
      if (!noZoom) {
        const point = new NPMapLib.Geometry.Point(
          pointItem.lon || pointItem.geoPoint.lon,
          pointItem.lat || pointItem.geoPoint.lat
        );
        // 获取图层，计算弹框向下偏移量
        let zoomLat = this.moveModal();
        //mapMain[this.mapType].map.setCenter(new NPMapLib.Geometry.Point(pointItem.lon || pointItem.geoPoint.lon, (pointItem.lat || pointItem.geoPoint.lat) + 0.0025))
        mapMain[this.mapType].map.centerAndZoom(
          new NPMapLib.Geometry.Point(
            pointItem.lon || pointItem.geoPoint.lon,
            (pointItem.lat || pointItem.geoPoint.lat) + zoomLat
          ),
          mapMain[this.mapType].map.getMaxZoom()
        );
      }

      let obj = this.handleOffset();
      let title =
        this.sectionMenuName == "device" && pointItem.deviceName
          ? pointItem.deviceName
          : obj.title;
      this.deviceModalOption.title = title;
      this.deviceModalOption.width = obj.width;
      this.deviceModalOption.open = true;
      // 修改弹框位置
      this.$nextTick(() => {
        let dragDom = this.$refs.deviceModal.$el.querySelector(
          ".ivu-modal-content-drag"
        );
        if (dragDom) {
          let left = window.innerWidth / 2 - dragDom.offsetWidth / 2;
          let top = window.innerHeight / 2 - dragDom.offsetHeight;
          dragDom.style.left = left + "px";
          dragDom.style.top = (top < 90 ? 90 : top) + "px";
        }
      });
    },
    zoomInto(point, zoom) {
      this.$nextTick(() => {
        if (this.currentClickIndex == -1) return;
        const pointItem = point || this.points[this.currentClickIndex];
        // 获取图层，计算弹框向下偏移量
        let zoomLat = this.moveModal();
        let toZoom = zoom || mapMain[this.mapType].map.getMaxZoom();
        mapMain[this.mapType].map.centerAndZoom(
          new NPMapLib.Geometry.Point(
            pointItem.lon || pointItem.geoPoint.lon,
            (pointItem.lat || pointItem.geoPoint.lat) + zoomLat
          ),
          toZoom
        );
      });
    },
    handleOffset() {
      // 地图弹框
      const stragetyOffset = {
        face: { title: "抓拍详情", width: 820, height: 560 },
        humanbody: { title: "抓拍详情", width: 820, height: 560 },
        nonmotorVehicle: { title: "抓拍详情", width: 820, height: 560 },
        vehicle: { title: "抓拍详情", width: 820, height: 560 },
        device: { title: "设备详情", width: 532, height: 372 },
        default: { title: "详情", width: 490, height: 335 },
      };
      let obj =
        this.sectionMenuName in stragetyOffset
          ? stragetyOffset[this.sectionMenuName]
          : stragetyOffset.default;
      return obj;
    },
    getRoadNet(stops) {
      let linePoints = stops.join(";");
      let data = new FormData();
      data.append("stops", linePoints);
      axios.post("/npgisdataservice/gis/routing", data).then((res) => {
        // 路网画线
        this.renderLinesByRoadNet(res.data);
      });
    },
    // 根据路网创建地图线路
    renderLinesByRoadNet(roatNet) {
      if (!roatNet || !roatNet.length) {
        return;
      }
      let points = [];
      roatNet.forEach((item) => {
        if (!item.expend) return;
        if (item.expend.indexOf("POINT") === -1) {
          // 过滤到非路网点
          points = points.concat(
            item.expend.split(";").map((p) => p.split(","))
          );
        }
      });
      return this.createPolyline(points);
    },
    // 根据点信息画线
    createPolyline(data) {
      let points = [];
      for (let i = 0; i < data.length; i++) {
        let p = new NPMap.Geometry.Point(data[i][0], data[i][1]);
        points.push(p);
      }
      if (points.length === 0) {
        return;
      }
      if (_wonderLayer) {
        _wonderLayer.removeAllOverlays();
      } else {
        _wonderLayer = new NPMap.Layers.OverlayLayer("_wonderLayer", false);
        mapMain[this.mapType].map.addLayer(_wonderLayer);
      }
      let trackPolyline = new NPMapLib.Geometry.Polyline(points, {
        color: "#2D87F9",
        weight: 5, //线的宽度，以像素为单位
        opacity: 1, //线的透明度，取值范围0-1
        lineStyle: NPMapLib.LINE_TYPE_SOLID, //线的样式
      });
      _wonderLayer.addOverlay(trackPolyline);
      mapMain[this.mapType].map.zoomToExtent(trackPolyline.getExtent());

      let offset = new NPMap.Geometry.Size(-20, -20);
      let headerMarker = new NPMap.Symbols.Marker(points[0], {
        offset: offset,
      });
      let size = new NPMapLib.Geometry.Size(40, 40);
      let imgUrl =
        this.sectionName == "vehicle"
          ? require(`@/assets/img/map/temptracker.png`)
          : require(`@/assets/img/map/map-person-icon.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      headerMarker.setIcon(icon);
      _wonderLayer.addOverlay(headerMarker);

      let options = {
        color: "red",
        renderFirst: true,
        opacity: 0.01,
        layer: _wonderLayer,
        weight: 1,
        headerMarker: headerMarker,
      };
      animationLine = new NPMap.Symbols.AnimationLine(
        mapMain[this.mapType].map.id,
        points,
        options
      );
      animationLine.setSpeed(10);
      animationLine.start();
    },
    //关闭抓拍-弹框
    closeDeviceModal() {
      this.closeAllInfoWindow();
      this.$emit("chooseMapItem", -1);
    },
    // 轨迹连线
    trackConnect(flag) {
      if (animationLine) {
        animationLine.stop();
        animationLine.remove();
        animationLine = null;
      }
      if (flag) {
        if (this.points.length > 1) {
          this.getRoadNet(
            this.points
              .filter((v) => v.lon && v.lat)
              .map((v) => v.lon + "," + v.lat)
          );
        }
      } else {
        if (_wonderLayer) {
          mapMain[this.mapType].map.removeOverlay(_wonderLayer);
          _wonderLayer.removeAllOverlays();
          _wonderLayer = null;
        }
      }
    },
    // 普通搜索和轨迹撒点
    sprinkleHandler(points) {
      if (!points || points.length === 0) {
        return false;
      }
      let trackMarkers = [];
      for (let index = points.length - 1; index >= 0; index--) {
        let item = points[index];
        let imgUrl = "",
          label = null,
          icon = null,
          size = new NPMapLib.Geometry.Size(40, 40);
        if (
          (item.lon && item.lat) ||
          (item.geoPoint?.lon && item.geoPoint?.lat)
        ) {
          item.lon = item.lon ? item.lon : item.geoPoint.lon;
          item.lat = item.lat ? item.lat : item.geoPoint.lat;
          let marker = new NPMapLib.Symbols.Marker(item);
          marker.index = index;
          // 轨迹撒点
          // 文本标记
          label = new NPMapLib.Symbols.Label(
            `${item.Index ? item.Index : index + 1}`
          );
          // 多数同一点、 一点一数
          imgUrl = require(`@/assets/img/map/trajectory-${
            item.active ? "blue" : "red"
          }.png`);
          label.setOffset(new NPMapLib.Geometry.Size(-0.5, 23));
          label.setStyle({
            fontSize: 14, //文字大小
            fontFamily: "MicrosoftYaHei-Bold, MicrosoftYaHei", //字体
            color: !item.active ? "#EA4A36" : "#2C86F8", //文字前景色
            align: "cm", //对方方式
            isBold: true, //是否粗体
          });
          marker.setLabel(label);
          // 设置图片
          icon = new NPMapLib.Symbols.Icon(imgUrl, size);
          icon.setAnchor(
            new NPMapLib.Geometry.Size(-size.width / 2, -size.height)
          );
          marker.setIcon(icon);
          trackMarkers.push(marker);
        }
      }
      this.markers = [...trackMarkers];
      if (!!trackMarkers.length) {
        // 轨迹撒点
        if (!trackLayer) {
          trackLayer = new NPMapLib.Layers.OverlayLayer("track");
          mapMain[this.mapType].map.addLayer(trackLayer);
        }
        trackLayer.addOverlays(trackMarkers);
        let clickFn = (marker) => {
          console.log(marker.index);
          this.$emit("chooseMapItem", marker.index);
        };
        for (let index = trackMarkers.length - 1; index >= 0; index--) {
          trackMarkers[index].addEventListener(
            NPMapLib.MARKER_EVENT_CLICK,
            clickFn
          );
        }
      }
    },
    //轨迹回到可视区域
    rePosition(arrList) {
      let minLat, minLon, maxLon, maxLat, sortLat, sortLon;
      if (!arrList || arrList.length === 0) {
        return false;
      }
      sortLat = arrList.map((e) => e.lat);
      sortLon = arrList.map((e) => e.lon);
      minLat = Math.min(...sortLat);
      maxLat = Math.max(...sortLat);
      minLon = Math.min(...sortLon);
      maxLon = Math.max(...sortLon);
      let dLat = maxLat - minLat;
      let dLon = maxLon - minLon;
      let extent = new NPMapLib.Geometry.Extent(
        minLon - dLon * 0.1,
        minLat - dLat * 0.1,
        maxLon + dLon * 0.1,
        maxLat + dLat * 0.1
      );
      if (mapMain[this.mapType]) {
        mapMain[this.mapType].zoomToExtend(extent);
      }
    },
    // 撒点
    sprinkleDot(row) {
      let size = new NPMapLib.Geometry.Size(29, 29);
      let imgUrl = require(`@/assets/img/map/red-locate-icon.png`);
      let icon = new NPMapLib.Symbols.Icon(imgUrl, size);
      icon.setAnchor(new NPMapLib.Geometry.Size(-size.width / 2, -size.height));
      const point = new NPMapLib.Geometry.Point(
        row.lon || row.longitude || row.geoPoint.lon,
        row.lat || row.latitude || row.geoPoint.lat
      );
      let marker = new NPMapLib.Symbols.Marker(point);
      marker.setIcon(icon);
      // mapMain[this.mapType].map.addOverlay(marker)
      vehicleDot = new NPMapLib.Layers.OverlayLayer("vehicleDotImg");
      mapMain[this.mapType].map.addLayer(vehicleDot);
      vehicleDot.addOverlay(marker);
      vehicleDot.setZIndex(600);
      mapMain[this.mapType].map.centerAndZoom(
        new NPMapLib.Geometry.Point(
          row.lon || row.longitude || row.geoPoint.lon,
          row.lat || row.latitude || row.geoPoint.lat
        ),
        mapMain[this.mapType].map.getMaxZoom()
      );
    },
    clearPoint() {
      if (vehicleDot) {
        vehicleDot.removeAllOverlays();
      }
      if (animationLine) {
        animationLine.stop();
        animationLine.remove();
        animationLine = null;
      }
      if (_wonderLayer) {
        _wonderLayer.removeAllOverlays();
      }
      if (vehicleLayer) {
        vehicleLayer.removeAllOverlays();
      }
      if (footholdLayer) {
        footholdLayer.removeAllOverlays();
      }
      if (trackLayer) {
        trackLayer.removeAllOverlays();
      }
      this.closeAllInfoWindow(); //关闭弹窗
    },
    clearClickPoint() {
      if (vehicleDot) {
        vehicleDot.removeAllOverlays();
      }
    },
    // 关闭多个弹框
    closeAllInfoWindow() {
      this.deviceModalOption.open = false;
    },
    _updateData() {
      let vm = this;
      if (footholdLayer) {
        footholdLayer.removeAllOverlays();
      }
      vm.rendFootHoldPointLayer(vm.footholdData);
    },
    rendFootHoldPointLayer(listObj) {
      let footholdPoints = [];
      let footholdMarkers = [];
      let vm = this;
      if (footholdLayer) {
        footholdLayer.removeAllOverlays();
      } else {
        footholdLayer = new NPMapLib.Layers.OverlayLayer("footholdLayer");
        mapMain[vm.mapType].map.addLayer(footholdLayer);
      }

      for (let i = 0, length = listObj.length; i < length; i++) {
        let item = listObj[i];
        let footholdPoint = new NPMapLib.Geometry.Point(
          item.longitude,
          item.latitude
        );
        let footholdMarker = vm.createMarker({
          iconUrl: vm.getRedUrl(vm.mapFlag[i % vm.mapFlag.length]),
          width: 32,
          height: 32,
          point: footholdPoint,
          addition: {
            id: item.deviceId.toString(),
            iconFlag: vm.mapFlag[i % vm.mapFlag.length],
            byTimes: listObj[i].byTimes,
          },
        });
        footholdMarker.deviceName = item.deviceName;
        footholdPoints.push(footholdPoint);
        footholdMarkers.push(footholdMarker);
      }
      footholdLayer.addOverlays(footholdMarkers);
    },
    //创建标注
    createMarker(options) {
      var marker;
      var size = new NPMapLib.Geometry.Size(
          options.width > 0 ? options.width : 28,
          options.height > 0 ? options.height : 26
        ),
        icon = new NPMapLib.Symbols.Icon(options.iconUrl, size);
      marker = options.offset
        ? new NPMapLib.Symbols.Marker(options.point, {
            offset: options.offset,
          })
        : new NPMapLib.Symbols.Marker(options.point);
      marker.setIcon(icon);
      options.label && marker.setLabel(options.label);
      marker.setData(options.addition);
      marker.mouseover = options.mouseover;
      marker.mouseout = options.mouseout;
      marker.click = options.click;
      return marker;
    },
    getMarkersByCustomID(id, currentlayer, name) {
      var features = currentlayer.getOverlaysArry(),
        feature,
        customData;
      for (var i = 0; i < features.length; i++) {
        feature = features[i];
        if (feature) {
          customData = feature.getData();
          if (
            customData &&
            customData[name ? name : "id"] == id &&
            feature instanceof NPMapLib.Symbols.Marker
          ) {
            return feature;
          }
        }
      }
      return null;
    },
    /**
     * [blueMarker 将地图点位致蓝]
     */
    blueMarker(ids) {
      let vm = this;
      ids = ids.split(",");
      for (var i = 0; i < ids.length; i++) {
        var currentMarker = vm.getMarkersByCustomID(ids[i], footholdLayer);
        currentMarker.setZIndex(9000);
        vm.changeIcon(
          currentMarker,
          vm.getBlueUrl(currentMarker.getData().iconFlag),
          32,
          32
        );
      }
    },
    /**
     * [redMarker 将地图点位致红]
     */
    redMarker(ids) {
      let vm = this;
      ids = ids.split(",");
      for (var i = 0; i < ids.length; i++) {
        var currentMarker = vm.getMarkersByCustomID(ids[i], footholdLayer);
        currentMarker.setZIndex(-1);
        vm.changeIcon(
          currentMarker,
          vm.getRedUrl(currentMarker.getData().iconFlag),
          32,
          32
        );
      }
    },
    changeIcon(marker, iconUrl, width, height) {
      var icon = marker.getIcon();
      icon.setImageUrl(iconUrl);
      icon.setImageSize(new NPMapLib.Geometry.Size(width, height));
      marker.setIcon(icon);
      marker.refresh();
    },
    /*
        获取红色Maker的图片路径
        */
    getRedUrl(text) {
      let vm = this;
      if (vm.mapFlag.indexOf(text) >= 0) {
        return require("@/assets/img/map/marker/red_" + text + ".png");
      } else {
        return require("@/assets/img/map/marker/marker_num/red_" +
          text +
          ".png");
      }
    },
    /*
        获取蓝色Maker的图片路径
        */
    getBlueUrl(text) {
      let vm = this;
      if (vm.mapFlag.indexOf(text) >= 0) {
        return require("@/assets/img/map/marker/blue_" + text + ".png");
      } else {
        return require("@/assets/img/map/marker/marker_num/blue_" +
          text +
          ".png");
      }
    },
    showFootholdArea() {
      let self = this;
      if (!self.currentFootholdArea) return;
      let obj = self.currentFootholdArea;
      obj.x1 = obj.lessAccessLog.longitude;
      obj.y1 = obj.lessAccessLog.latitude;
      obj.x2 = obj.largeAccessLog.longitude;
      obj.y2 = obj.largeAccessLog.latitude;
      let radius,
        p1 = new NPMapLib.Geometry.Point(obj.x1, obj.y1),
        p2 = new NPMapLib.Geometry.Point(obj.x2, obj.y2);
      if (footholdLayer) {
        footholdLayer.removeOverlay(self.footholdArea);
      } else {
        footholdLayer = new NPMapLib.Layers.OverlayLayer("footholdLayer");
        mapMain[self.mapType].map.addLayer(footholdLayer);
      }

      if (self.footholdMarkers) {
        for (var i = 0; i < self.footholdMarkers.length; i++) {
          footholdLayer.removeOverlay(self.footholdMarkers[i]);
        }
      }
      if (obj.x1 === obj.x2 && obj.y1 === obj.y2) {
        radius = self.RADIUS;
        self.footholdArea = new NPMapLib.Geometry.Circle(p1, radius, {
          weight: 0,
          fillColor: "#f8a8a5", //填充颜色
          fillOpacity: 0.7, //填充的透明度，取值范围0-1
          text: "",
        });
      } else {
        radius = mapMain[self.mapType].map.getDistance(p1, p2, "EPSG:4326");
        self.footholdArea = new NPMapLib.Geometry.Ellipse(
          p1,
          p2,
          1.3 * radius,
          {
            weight: 0,
            fillColor: "#f8a8a5", //填充颜色
            fillOpacity: 0.7, //填充的透明度，取值范围0-1
            text: "",
          }
        );
      }
      footholdLayer.addOverlay(self.footholdArea);
      //mapMain[self.mapType].map.zoomToPoints([p1,p2]);
      mapMain[this.mapType].zoomToPoints([p1, p2]);
      //mapMain[self.mapType].map.setZoom(mapMain[self.mapType].map.getZoom()-1);

      var ids = obj.lessAccessLog.deviceId;
      if (obj.lessAccessLog.deviceId != obj.largeAccessLog.deviceId) {
        ids = ids + "," + obj.largeAccessLog.deviceId;
      }
      if (self.currentBlueMarkerIds) {
        self.redMarker(self.currentBlueMarkerIds);
      }
      self.currentBlueMarkerIds = ids;
      self.blueMarker(ids);
      self.zoomInto(
        { lon: (obj.x1 + obj.x2) / 2, lat: (obj.y1 + obj.y2) / 2 },
        15
      );
    },
    // 渲染热力图
    renderHeatMap(dataList, radius = 20) {
      if (!dataList || dataList.length === 0) {
        this.hideHeapMap(false);
        return false;
      }
      let opt = {
        isBaseLayer: false,
        opacity: 1.0,
        projection: "EPSG:900913",
        visible: true,
        radius: radius,
        name: "heatLayer",
      };
      if (!heatLayer) {
        heatLayer = new NPMapLib.Layers.HeatMapLayer("heatLayer", opt);
        mapMain[this.mapType].map.addLayers([heatLayer]);
      }
      // max 数据的 获取
      let countMax = 0;
      let countMin = 0;
      let currentCount = 0;
      let _dataList = dataList.map((val) => {
        currentCount = val.timesNum || 1;
        countMax = Math.max(countMax, currentCount);
        countMin = Math.min(countMin, currentCount);
        let lat = val.geoPoint?.lat || 32;
        let lon = val.geoPoint?.lon || 118;
        return {
          lat,
          lon,
          count: currentCount,
        };
      });
      let dataset = {
        max: countMax,
        min: countMin,
        data: _dataList,
      };
      heatLayer.setDataset(dataset);
      // todo: 修改地图层级 和中心点点位 按照视图解析那里
      this.flyByPoints(_dataList);
      heatLayer.show();
    },
    hideHeapMap(bool = false) {
      heatLayer && heatLayer[bool ? "show" : "hide"]();
    },
    //计算最大最小经纬度
    getZoomExtent(lon, lat, nowExtent) {
      if (lon < nowExtent.minLon) {
        nowExtent.minLon = lon;
      }
      if (lon > nowExtent.maxLon) {
        nowExtent.maxLon = lon;
      }
      if (lat < nowExtent.minLat) {
        nowExtent.minLat = lat;
      }
      if (lat > nowExtent.maxLat) {
        nowExtent.maxLat = lat;
      }
      return nowExtent;
    },
    /**
     * 根据多个点位定位地图视窗
     */
    flyByPoints(points) {
      let zoomExtent = {
        minLon: points[0].lon,
        maxLon: points[0].lon,
        minLat: points[0].lat,
        maxLat: points[0].lat,
      };
      for (let i = 0; i < points.length; i++) {
        const p = points[i];
        if (
          mapMain[this.mapType].map
            .getRestrictedExtent()
            .containsPoint({ lat: p.lat, lon: p.lon })
        ) {
          zoomExtent = this.getZoomExtent(p.lon, p.lat, zoomExtent);
        }
      }
      var ext = new NPMap.Geometry.Extent(
        zoomExtent.minLon,
        zoomExtent.minLat,
        zoomExtent.maxLon,
        zoomExtent.maxLat
      );
      mapMain[this.mapType].map.setZoom(
        mapMain[this.mapType].map.getZoom() - 1
      );
      mapMain[this.mapType].map.zoomToExtent(ext, false, true);
    },
  },
};
</script>

<style lang="less" scoped>
.map-boxs {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
  .map {
    height: 100%;
    width: 100%;
    position: relative;
  }
}
/deep/.domModal {
  .ivu-modal-body {
    padding: 0 !important;
  }
}
</style>
