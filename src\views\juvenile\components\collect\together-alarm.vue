<!--
    * @FileDescription: 违法前科人员同行
    * @Author: H
    * @Date: 2023/05/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-03-06 11:21:29
 -->
<template>
  <div class="together-box">
    <div class="head-location">
      <i class="iconfont icon-location mr-5"></i>{{ data.deviceName }}
    </div>
    <div class="images-box">
      <div class="image-box object-image">
        <div class="tag obj">对象</div>
        <ui-image :src="data.srcFaceCaptureVo.traitImg"></ui-image>
        <div class="name-info">
          {{ `${data.srcPersonInfo.name}`
          }}<span v-if="isAge">{{ `(${data.srcPersonInfo.age})` }}</span>
        </div>
        <div class="time">{{ data.srcFaceCaptureVo.absTime }}</div>
      </div>
      <div class="image-box together-image">
        <div class="tag together">同行</div>
        <ui-image :src="data.criminalFaceCaptureVo.traitImg"></ui-image>
        <div class="name-info">{{ `${data.criminalPersonInfo.name}` }}</div>
        <div class="time">{{ data.srcFaceCaptureVo.absTime }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "TogetherAlarm",
  props: {
    // 数据
    data: {
      type: Object,
      default: () => {},
    },
    // 展示年龄
    isAge: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
};
</script>

<style lang="less" scoped>
.mr-5 {
  margin-right: 5px;
}
.together-box {
  width: 300px;
  height: 80%;
  background: #f9f9f9;
  box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7016);
  border: 1px solid #d3d7de;
  padding: 5px 20px;
  .head-location {
    width: 100%;
    text-align: left;
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .images-box {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    height: 60%;
    justify-content: space-around;
    .together-image {
      border: 2px solid #ea4a36;
    }
    .image-box {
      width: 120px;
      height: 100%;
      position: relative;
      /deep/ img {
        width: auto;
        max-height: 100%;
      }
      .tag {
        position: absolute;
        top: 0;
        left: 0;
        background: #1faf8a;
        border-radius: 0 0 8px 0;
        color: #fff;
        padding: 3px 8px;
        z-index: 10;
      }
      .obj {
        background: #1faf8a;
      }
      .together {
        background: #ea4a36;
      }
      .name-info {
        position: absolute;
        bottom: 0px;
        width: 100%;
        background: rgba(0, 0, 0, 0.4979);
        color: #fff;
        text-align: center;
        line-height: 18px;
      }
      .time {
        width: 100%;
        text-align: center;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.35);
        line-height: 18px;
        text-wrap: nowrap;
        margin-top: 5px;
      }
    }
  }
}
</style>
