<template>
  <Modal
    v-model="visible"
    :title="isEdit ? '编辑' : '新增'"
    width="1010"
    class="modal-place-box"
    @onCancel="onCancel"
  >
    <div ref="personnelThematic" class="personnel-thematic-database">
      <Form
        ref="placeForm"
        :model="placeForm"
        inline
        class="personnel-form"
        :rules="ruleInline"
      >
        <div class="form-item-title card-border-color">
          <div class="title-line bg-primary"></div>
          <div class="title-text title-color">基本信息</div>
        </div>
        <div class="information-form essential-form">
          <FormItem prop="image" class="img-formitem">
            <div class="essential-information-img">
              <div class="avatar-img card-border-color">
                <ui-image viewer type="place" :src="previewImg" />
                <!-- <img v-viewer :src="placeForm.image" alt /> -->
              </div>
              <UploadImg
                choosed
                ref="uploadImg"
                :deleted="true"
                :isEdit="placeForm.image.length > 0"
                :multipleNum="1"
                :choosedIndex="avatarIndex"
                :defaultList="placeForm.image"
                class="upload-img"
                @on-choose="chooseHandle"
              />
            </div>
          </FormItem>
          <div class="information-body">
            <div class="info-item">
              <FormItem prop="name" label="场所名称:">
                <Input
                  v-model="placeForm.name"
                  placeholder="请输入场所名称"
                  class="input-200"
                />
              </FormItem>
              <FormItem prop="area" label="场所面积(m²):">
                <Input
                  v-model="placeForm.area"
                  placeholder="请输入场所面积"
                  type="number"
                  class="input-200"
                />
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="firstLevel" label="一级分类:">
                <Select
                  v-model="placeForm.firstLevel"
                  placeholder="请选择一级分类"
                  class="input-200"
                  @on-select="(node) => (placeForm.firstLevelName = node.label)"
                  filterable
                >
                  <Option
                    v-for="(item, $index) in placeFirstLevelList"
                    :key="item.typeCode"
                    :value="item.typeCode"
                    >{{ item.typeName }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="secondLevel" label="二级分类:">
                <Select
                  v-model="placeForm.secondLevel"
                  placeholder="请选择二级分类"
                  :disabled="placeForm.firstLevel === ''"
                  class="input-200"
                  @on-select="
                    (node) => (placeForm.secondLevelName = node.label)
                  "
                  filterable
                >
                  <Option
                    v-for="(item, $index) in secondLevelPlaceInFirst"
                    :key="item.typeCode"
                    :value="item.typeCode"
                    >{{ item.typeName }}</Option
                  >
                </Select>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="adcode" label="所属区县:">
                <Select
                  v-model="placeForm.adcode"
                  placeholder="请选择所属区县"
                  class="input-200"
                  filterable
                  @on-change="changeCity"
                  @on-select="(node) => (placeForm.adname = node.label)"
                >
                  <Option
                    v-for="item in cityList"
                    :value="item.regionCode"
                    :key="item.regionCode"
                    >{{ item.regionName }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem prop="towncode" label="所属街道:">
                <!-- 街道信息列表要根据区带过来，首次加载的时候街道信息获得比较慢，导致已经选择的内容看不到 -->
                <Select
                  v-if="!streetLoading"
                  v-model="placeForm.towncode"
                  placeholder="请选择所属街道"
                  class="input-200"
                  filterable
                  @on-select="(node) => (placeForm.townname = node.label)"
                >
                  <Option
                    v-for="item in streetList"
                    :value="item.regionCode"
                    :key="item.regionCode"
                    >{{ item.regionName }}</Option
                  >
                </Select>
                <Select
                  v-else
                  placeholder="请选择所属街道"
                  class="input-200"
                ></Select>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem
                prop="address"
                label="场所地址:"
                style="width: 100%"
                class="all-width"
              >
                <Input
                  v-model="placeForm.address"
                  placeholder="请输入场所地址"
                />
                <!-- class="input-520" -->
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="coord" label="场所区域设置:">
                <div class="select-tag-button input-200" @click="setArea">
                  请绘制区域
                </div>
              </FormItem>
              <FormItem prop="additionalDevices" label="场所设备:">
                <div class="device-select-box input-200">
                  <div class="select-tag-button" @click="selectListDevice">
                    选择设备/已选({{ selectDeviceList.length }})
                  </div>
                  <!-- <div class="area-item" @click="selectMapDevice">
                    <ui-icon type="gateway" />
                  </div> -->
                </div>
              </FormItem>
            </div>
            <div class="info-item">
              <FormItem prop="centerPoint" label="场所中心点:">
                <div
                  class="label-value link-active-color"
                  @click="showChooseMap"
                >
                  {{ placeForm.centerPoint || "请选择" }}
                </div>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
    </div>
    <template #footer>
      <div class="footer-btn">
        <Button @click="onCancel">取消</Button>
        <Button type="primary" @click="comfirmHandle">确定</Button>
      </div>
    </template>
    <UiMapDrawArea
      :title="'场所区域绘制'"
      :place-fence="placeFence"
      ref="areaEditRef"
      :is-edit="true"
      :isMulti="true"
      @close="closeArea"
      @completeDraw="completeDraw"
    ></UiMapDrawArea>
    <!-- v-if="mapVisible" -->
    <!-- 选择设备 -->
    <select-device
      ref="selectDevice"
      showOrganization
      :place-fence="placeFence"
      :checkedLabels="checkedLabels"
      @selectData="selectData"
    />
    <!-- 中心点选择 -->
    <ChooseCenterMap
      ref="centerMapShowRef"
      @confirm="centerPointConfirm"
      :place-fence="placeFence"
      :defaultCenterPoint="placeForm.centerPoint"
    ></ChooseCenterMap>
  </Modal>
</template>
<script>
import { mapActions, mapGetters } from "vuex";
import UploadImg from "@/components/ui-upload-img-static-library";
import UiMapDrawArea from "@/components/ui-map-draw-area";
import ChooseCenterMap from "./chooseCenterMap.vue";

import {
  getPlaceDeviceInfoV2,
  updatePlaceArchiveModify,
  addPlaceArchiveModify,
} from "@/api/operationsOnTheMap";
import {
  getCityByParentCodeAPI,
  queryReginNameforCode,
} from "@/api/placeArchive";
import { placeFenceType } from "@/views/holographic-archives/place-archives/dashboard/components/common";
import { queryDevicePageList } from "@/api/target-control.js";
import { getPlaceTypeList } from "@/api/placeArchive.js";
export default {
  components: {
    UploadImg,
    UiMapDrawArea,
    ChooseCenterMap,
  },
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.getCityList();
  },
  data() {
    return {
      visible: false,
      mapVisible: false,
      avatarIndex: 0,
      placeForm: {
        id: "", // 场所ID
        name: "",
        area: "",
        firstLevel: "",
        firstLevelName: "",
        secondLevel: "",
        secondLevelName: "",
        adcode: "",
        adname: "",
        towncode: "",
        townname: "",
        address: "",
        image: [],
        coord: "", // 场所围栏
        additionalDevices: "", // 场所设备 逗号分隔
        centerPoint: "", // 场所中心点 字符串数组
      },
      photoChange: false,
      previewImg: "",
      cityList: [], // 区县列表
      streetList: [], // 街道列表
      selectDeviceList: [], // 设备选择
      checkedLabels: [],
      ruleInline: {
        name: [
          {
            required: true,
            message: "请输入姓名",
            trigger: "blur",
            type: "string",
          },
        ],
        firstLevel: [
          { required: true, message: "请选择一级分类", trigger: "change" },
        ],
        secondLevel: [
          { required: true, message: "请选择二级分类", trigger: "change" },
        ],
        coord: [
          { required: true, message: "请选择场所区域", trigger: "change" },
        ],
      },
      otherParam: {
        pname: "",
        pcode: "",
        cityname: "",
        citycode: "",
      },
      streetLoading: false,
      placeFirstLevelList: [],
      placeLevelAll: [],
    };
  },
  computed: {
    // 一级场所下的二级场所
    secondLevelPlaceInFirst() {
      if (this.placeForm.firstLevel) {
        let firstLevel = this.placeFirstLevelList.find(
          (item) => item.typeCode == this.placeForm.firstLevel
        );
        if (!firstLevel) {
          return [];
        }
        let arr = this.placeLevelAll.filter(
          (item) => item.parentId == firstLevel.id
        );
        return arr;
      }
      return [];
    },
    // 场所围栏
    placeFence() {
      if (this.placeForm.coord) {
        let placeFence = placeFenceType.find(
          (v) => v.code === this.placeForm.firstLevel
        );
        if (!placeFence) {
          placeFence = {
            code: this.placeForm.firstLevel,
            name: this.placeForm.firstLevelName,
            color: "#2c86f8",
            layerType: "placeDefault",
          };
        }
        placeFence.data = [
          {
            properties: { ...this.placeForm },
            geometry: JSON.parse(this.placeForm.coord),
          },
        ];
        console.log(placeFence, "placeFence");
        return placeFence;
      }
    },
  },
  methods: {
    async initPlaceTypeList() {
      let { data } = await getPlaceTypeList({});
      this.placeLevelAll = data;
      this.placeFirstLevelList = data.filter((item) => item.parentId == -1);
    },
    showModal(value = {}) {
      this.visible = true;
      this.previewImg = "";
      // 每次打开编辑都重新获取场所，万一当前页修改过场所，可以及时更新
      this.initPlaceTypeList();
      this.$nextTick(async () => {
        this.$refs.placeForm.resetFields();
        this.placeForm.id = "";
        this.selectDeviceList = [];
        if (value.isEdit) {
          Object.keys(this.placeForm).forEach((item) => {
            this.placeForm[item] = value[item] || "";
          });
          await this.setStreetList(this.placeForm.adcode);
          this.previewImg = this.placeForm.image[0];
          if (this.placeForm.additionalDevices !== "") {
            // 回填场所设备信息
            let { data = { entities: [] } } = await queryDevicePageList({
              deviceIds: this.placeForm.additionalDevices.split(","),
              pageNumber: 1,
              pageSize: 9999,
            });
            this.selectDeviceList = data.entities.map((item) => {
              return {
                ...item,
                deviceName: item.name,
                deviceId: item.id,
                deviceGbId: item.gbId,
                deviceType: item.type,
                select: true,
              };
            });
          }
          // this.placeForm = { ...value };
          this.queryPlaceDevice();
        }
      });
    },
    onCancel() {
      this.visible = false;
    },
    // 选择证件照
    chooseHandle(item) {
      this.photoChange = true;
      if (item) {
        this.previewImg = item.photoUrl || item;
      } else {
        this.previewImg = "";
      }
    },
    /**
     * @description: 获取区县
     */
    getCityList() {
      // 不需要参数，后端已配置
      getCityByParentCodeAPI().then((res) => {
        this.cityList = res.data || [];
        // 获取省 市信息
        if (this.cityList.length > 0) {
          let parentCode = this.cityList[0].parentCode;
          queryReginNameforCode({ regionCode: parentCode }).then((res) => {
            // 省 市信息
            let list = res.data;
            this.otherParam.pcode = list[0].regionCode;
            this.otherParam.pname = list[0].regionName;
            this.otherParam.citycode = list[1].regionCode;
            this.otherParam.cityname = list[1].regionName;
          });
        }
      });
    },

    /**
     * @description: 获取街道，清空选中的街道数据
     */
    changeCity() {
      this.placeForm.towncode = "";
      getCityByParentCodeAPI({
        parentCode: this.placeForm.adcode,
      }).then((res) => {
        this.streetList = res.data || [];
      });
    },
    setStreetList(parentCode) {
      this.streetLoading = true;
      getCityByParentCodeAPI({
        parentCode,
      })
        .then((res) => {
          this.streetList = res.data || [];
        })
        .finally(() => {
          this.streetLoading = false;
        });
    },
    comfirmHandle() {
      this.$refs.placeForm.validate().then((vali) => {
        if (vali) {
          if (this.placeForm.id !== "") {
            updatePlaceArchiveModify({
              ...this.placeForm,
              image: this.previewImg,
            }).then((res) => {
              if (res.msg == "成功") {
                this.$Message.success("编辑成功");
                this.$emit("confirm");
                this.onCancel();
              }
            });
          } else {
            let param = {
              ...this.placeForm,
              image: this.previewImg,
              ...this.otherParam,
            };
            delete param.id;
            addPlaceArchiveModify(param).then((res) => {
              if (res.msg == "成功") {
                this.$Message.success("新增成功");
                this.$emit("confirm");
                this.onCancel();
              }
            });
          }
        }
      });
    },
    selectLabelHandle() {},
    setArea() {
      this.$refs.areaEditRef.show();
    },
    selectListDevice() {
      this.$refs.selectDevice.show(this.selectDeviceList);
    },
    selectMapDevice() {},
    /**
     * 已选择设备数据返回
     */
    selectData(list) {
      this.selectDeviceList = list;
      this.placeForm.additionalDevices = list
        .map((item) => item.deviceGbId)
        .join(",");
    },
    closeArea() {
      this.mapVisible = false;
    },
    /**
     * @description 完成地图围栏绘制
     * @param value 地图围栏绘制对象
     */
    completeDraw(value) {
      if (!value) {
        return;
      }
      this.mapVisible = false;
      this.placeForm.coord = value;
    },
    showChooseMap() {
      this.$refs.centerMapShowRef.init();
    },
    centerPointConfirm(value) {
      this.placeForm.centerPoint = JSON.stringify(value);
    },
    queryPlaceDevice() {
      getPlaceDeviceInfoV2(this.placeForm.id).then(({ data }) => {});
    },
  },
};
</script>
<style lang="less" scoped>
.input-200 {
  width: 200px;
}
.input-520 {
  width: 520px;
}
.link-active-color {
  color: #1a74e7;
  cursor: pointer;
}
.personnel-thematic-database {
  overflow-x: hidden;
  overflow-y: auto;
  padding: 20px;
  // height: 680px;
  .personnel-form {
    box-sizing: border-box;
    .form-item-title {
      display: flex;
      align-items: center;
      padding-bottom: 4px;
      border-bottom: 1px solid #fff;
      .title-line {
        width: 3px;
        height: 16px;
        margin-right: 6px;
      }
      .title-text {
        font-size: 14px;
        line-height: 20px;
        font-weight: bold;
        font-family: "MicrosoftYaHei-Bold";
      }
    }
    .information-form {
      display: flex;
      justify-content: space-between;
      margin: 20px 0 30px 0;
      .essential-information-img {
        width: 240px;
        margin-right: 64px;
        display: flex;
        flex-direction: column;
        .avatar-img {
          width: 240px;
          height: 320px;
          border: 1px solid #fff;
          /deep/ img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: pointer;
          }
          .avatar-null-img {
            cursor: unset;
          }
        }
        .upload-img {
          margin-top: 10px;
          justify-content: flex-start;
          /deep/ .upload-item {
            width: 54px;
            height: 54px;
            .ivu-icon-ios-add {
              font-size: 30px;
              font-weight: bold;
            }
            .upload-text {
              line-height: 18px;
              display: none;
            }
          }
        }
      }
      .information-body {
        flex: 1;
        .info-item {
          display: flex;
          justify-content: space-between;
          padding-bottom: 15px;
          /deep/ .ivu-form-item {
            display: inline-flex;
            margin-right: 0;
            margin-bottom: 10px;
            .ivu-form-item-label {
              display: flex;
              align-items: center;
              justify-content: end;
              padding-right: 10px;
              white-space: nowrap;
              .label-text {
                text-align-last: justify;
                display: inline-block;
              }
            }
            .label-value {
              height: 34px;
              display: flex;
              align-items: center;
            }
            .ivu-form-item-label::before {
              margin: 0;
            }
            .ivu-radio-wrapper {
              margin-right: 30px;
            }
          }
          .area-item {
            width: 40px;
            height: 34px;
            background: #ffffff;
            border-radius: 4px;
            border: 1px solid #d3d7de;
            cursor: pointer;
            color: rgba(0, 0, 0, 0.6);
            margin-left: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 20px;
          }
          .device-select-box {
            display: flex;
            gap: 10px;
            .select-tag-button {
              flex: 1;
            }
          }
          .all-width {
            /deep/ .ivu-form-item-content {
              flex: 1;
            }
          }
        }
      }
    }
    .essential-form {
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 80px !important;
          .label-text {
            width: 58px;
          }
        }
      }
      .img-formitem {
        margin: 0 !important;
      }
    }
    .vehicle-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
        }
      }
      .info-item {
        justify-content: unset !important;
        .license-plate-number {
          margin-right: 36px !important;
        }
        .license-plate-color {
          margin-right: 46px !important;
        }
      }
    }
    .other-form {
      margin: 20px 0;
      /deep/ .ivu-form-item {
        .ivu-form-item-label {
          width: 74px;
          .label-text {
            width: 58px;
          }
        }
      }
      .info-item {
        justify-content: unset !important;
        .rfid-number {
          margin-right: 36px !important;
        }
        .mac-number {
          margin-right: 46px !important;
        }
      }
    }
  }
}
.footer-btn {
  text-align: center;
}
/deep/ .ivu-modal-header {
  background: rgba(211, 215, 222, 0.29);
}
</style>
