<template>
  <div class="exam-score-gab-examination-tendency height-full">
    <examinationtendency></examinationtendency>
  </div>
</template>
<script>
export default {
  name: 'examinationTendencyGAB',
  props: {},
  data() {
    return {};
  },
  provide() {
    return {
      examSchemeType: 2, //上级考核成绩
    };
  },
  methods: {},
  mounted() {},
  components: {
    examinationtendency: require('@/views/governanceevaluation/examinationresult/examinationtendency/index.vue')
      .default,
  },
};
</script>
<style lang="less" scoped></style>
