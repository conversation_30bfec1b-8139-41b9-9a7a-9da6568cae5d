<!--
    * @FileDescription: 建设态势专题大屏-社会资源态势
    * @Author: H
    * @Date: 2024/06/11
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
    <div class="screen-content">
        <div class="screen-left">
            <card title="联网排名" class="screen-box">
                <div class="network-box">
                    <div class="box-content">
                        <img :src="getImgUrl('lwsxj.png')" alt="">
                        <p class="box-title">联网摄像机数量</p>
                        <count-to :start-val="0" :end-val="infoData.onInternetCameraNum" :duration="1000" class="dinpromini network-title"></count-to>
                    </div>
                    <div class="box-content">
                        <img :src="getImgUrl('lwzpj.png')" alt="">
                        <p class="box-title">联网抓拍机数量</p>
                        <count-to :start-val="0" :end-val="infoData.onInternetCaptureNum" :duration="1000" class="dinpromini network-title"></count-to>
                    </div>
                </div>
            </card>
            <card title="联网占比" class="screen-box">
                <pie-chart ref="pieChart" :picData="networkList"></pie-chart>
            </card>
            <card title="在线率统计" class="screen-box">
                <div class="maintain-box">
                    <div class="maintain-box-top">
                        <img :src="getImgUrl('zxl.png')" alt="">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ infoData.onlineRateTotal }}%</p>
                            <p class="detail-li-title">在线率</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot device-online">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ infoData.onlineNum }}</p>
                            <p class="detail-li-title">在线设备总量</p>
                        </div>
                    </div>
                    <div class="maintain-box-bot device-lw">
                        <div class="detail-li">
                            <p class="detail-li-num">{{ infoData.onInternetNum }}</p>
                            <p class="detail-li-title">联网设备总量</p>
                        </div>
                    </div>
                </div>
            </card>
        </div>
        <div class="screen-main">
            <div class="content-top">
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        规划建设数量
                    </div>
                    <div class="view-box-top"> 
                        <p>摄像机</p>
                        <p class="gross">
                            <count-to :start-val="0" :end-val="infoData.cameraPlanNum" :duration="1000" class="dinpro"></count-to>
                        </p>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-top"> 
                        <p>抓拍机</p>
                        <p class="gross">
                            <count-to :start-val="0" :end-val="infoData.capturePlanNum" :duration="1000" class="dinpro"></count-to>
                        </p>
                        
                    </div>
                </div>
                <div class="view-data view-box">
                    <div class="title-img bigtitle">
                        已建数量
                    </div>
                    <div class="view-box-bot"> 
                        <p class="new-today">摄像机</p>
                        <div class="chain-system">
                            <count-to :start-val="0" :end-val="infoData.cameraBuildNum" :duration="1000" class="dinpro"></count-to>
                        </div>
                    </div>
                    <div class="box-line"></div>
                    <div class="view-box-bot"> 
                        <p class="new-today">抓拍机</p>
                        <div class="chain-system">
                            <count-to :start-val="0" :end-val="infoData.captureBuildNum" :duration="1000" class="dinpro"></count-to>
                        </div>
                    </div>
                </div>
            </div>
            <map-chart ref="mapChart" class="map-chart"></map-chart>
        </div>
        <div class="screen-right">
            <card title="在线率排名" class="screen-box" :padding="0">
                <rangking-chart ref="onlineChart"></rangking-chart>
            </card>
            <card title="联网排名" class="screen-box" :padding="0">
                <rangking-chart ref="internetChart"></rangking-chart>
            </card>
            <card title="视频资源调阅排名" class="screen-box" :padding="0">
                <div class="table">
                    <div class="table-header">
                        <div class="table-column-pm">排名</div>
                        <div class="table-column-fxj">行业(委办员)</div>
                        <div class="table-column-dyl">调阅量</div>
                    </div>
                    <ul class="table-content">
                        <li class="table-row" v-for="(item, index) in timeList" :key="index">
                            <div class="table-column-pm">
                                <img v-if="index<3" :src="getImgUrl('ranking-'+index+'.png') " alt="">
                                <span v-else>{{ item.ranking }}</span>
                            </div>
                            <div class="table-column-fxj">{{ item.projectName }}</div>
                            <div class="table-column-dyl">
                                <count-to :start-val="0" :end-val="item.num" :duration="1000" class="dinpromini"></count-to>
                            </div>
                        </li>
                    </ul>
                </div>
            </card>
        </div>
    </div>
</template>
<script>
import { onInternetRateRank,
    onlineInfoStat,
    onlineRateRank,
    soRegionStat,
    videoAccessRank } from '@/api/largeScreen';
import card from '@/components/screen/srceen-card.vue';
import pieChart from './pie-chart.vue';
import rangkingChart from './rangking-chart.vue';
import mapChart from './map-chart.vue';
import CountTo from 'vue-count-to';
export default {
    components: {
        card,
        rangkingChart,
        pieChart,
        mapChart,
        CountTo
    },
    data() {
        return {
            timeList: [
                { ranking: 1, projectName: 'XXX分局', num: 0, type: 1, rankingUrl: 'ranking-0.png'},
                { ranking: 2, projectName: 'XXX分局', num: 0, type: 2, rankingUrl: 'ranking-1.png'},
                { ranking: 3, projectName: 'XXX分局', num: 0, type: 1, rankingUrl: 'ranking-2.png'},
                { ranking: 4, projectName: 'XXX分局', num: 0, type: 2},
                { ranking: 5, projectName: 'XXX分局', num: 0, type: 2},
                { ranking: 6, projectName: 'XXX分局', num: 0, type: 1},
                { ranking: 7, projectName: 'XXX分局', num: 0, type: 2},
            ],
            networkList: [
                { value: '0', name: '已联网', type: 'onInternetRate' },
                { value: '0', name: '未联网', type: 'unInternetRate' },
            ],
            infoData: {},
        }
    },
    created() {
        this.init();
        this.queryMap();
    },
    methods: {
        init() {
            // 联网率排名
            onInternetRateRank()
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.internetChart.init(list)
            })
            // 联网信息统计
            onlineInfoStat()
            .then(res => {
                this.infoData = res.data;
                this.networkList.forEach(item => {
                    item.value = res.data[item.type];
                })
                this.$refs.pieChart.init(this.networkList);
            })
            // 在线率排名
            onlineRateRank()
            .then(res => {
                let list = res.data.length > 6? res.data.slice(0,6) : res.data;
                this.$refs.onlineChart.init(list);
            })
            // 视频资源调阅排名
            videoAccessRank()
            .then(res => {
                res.data.forEach((item,  index) => {
                    this.timeList[index].projectName = item.projectName;
                    this.timeList[index].num = Number(item.projectValue);
                })
            })
        },
        queryMap() {
            soRegionStat()
            .then(res => {
                this.$refs.mapChart.init(res.data)
            })
        }, 
        getImgUrl(val) {
            return require(`@/assets/img/screen/${val}`)
        },
    }
}
</script>
<style lang='less' scoped>
@import "../../../style/resetui.less";
.screen-content{
    display: flex;
    width: 100%;
    height: 100%;
    // .bomColor{
    //     color: #2DDF6C;
    // }
    .network-box{
        display: flex;
        padding: 0 60px;
        justify-content: space-between;
        height: 100%;
        align-items: center;
        .box-title{
            font-size: 14px;
            font-weight: 400;
            color: #ffffff;
        }
        .box-content{
            display: flex;
            flex-direction: column;
            align-items: center;
            .box-time{
                width: 91px;
                height: 39px;
                color: #ffffff;
                border: 2px solid;
                border-top: none;
                font-size: 26px;
                font-weight: 700;
                font-family: 'DINPro';
                text-align: center;
                margin: 15px 0 10px;
            }
        }
        .network-title{
            font-size: 24px;
            color: #F1FCFF;
        }
    }
    .maintain-box{
        display:flex;
        flex-wrap: wrap;
        justify-content: space-between;
        height: 100%;
        .maintain-box-top{
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
        }
        .device-online{
            background: url('~@/assets/img/screen/zxsb.png') no-repeat !important;
            background-size: 100% 100% !important;
        }
        .device-lw{
            background: url('~@/assets/img/screen/lwsb.png') no-repeat !important;
            background-size: 100% 100% !important;
        }
        .detail-li{
            text-align: center;
            .detail-li-num{
                font-size: 30px;
                font-weight: 700;
                color: #F1FCFF;
            }
            .detail-li-title{
                font-size: 14px;
                font-weight: 400;
                color: #ffffff;
            }
        }
    }
    .content-top{
        display: flex;
        justify-content: space-around;
        .view-box{
            padding: 0 40px !important;
            .view-box-top{
                color: #fff;
                font-size: 14px;
                font-weight: 400;
                height: 50%;
                margin-top: 10px;
                .gross{
                    font-size: 28px;
                    color: #F1FCFF;
                    margin-top: 23px;
                    font-family: DINPro;
                    // span{
                    //     font-size: 14px;
                    //     color: #03A9FF;
                    // }
                }
                
            }
            
            .view-box-bot{
                margin-top: 10px;
                .new-today{
                    font-size: 14px;
                    color: #ffffff;
                    span{
                        font-size: 14px;
                        color: #2DDF6C;
                        margin-left: 10px;
                    }
                }
                .chain-system{
                    display: flex;
                    margin-top: 23px;
                    font-size: 28px;
                    color: #9EF98C;
                    font-family: DINPro;
                    .chain-system-num{
                        display: flex; 
                        align-items: center;
                        margin-left: 10px;
                        p{
                            font-size: 14px;
                            color: #03A9FF;
                        }
                        img{
                            width: 12px;
                            height: 14px;
                        }
                    }
                }
            }
        }
    }
}
</style>