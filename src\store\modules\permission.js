import { arrayToJson, deepCopy } from '@/util/module/common';
import { geticonClassName } from '@/util/module/common';
import common from '@/config/api/common';
import http from '@/config/http/http';
export default {
  namespaced: true,
  state: {
    routerList: [], //有权限的路由
    routerTreeList: [], //有权限的树结构路由
    allRouterList: [], //所有菜单路由
    permissions: null, //所有权限标识
    leftMenuList: [], //左侧菜单列表
  },

  getters: {
    getRouterList(state) {
      return state.routerList;
    },
    getRouterTreeList(state) {
      return state.routerTreeList;
    },
    // 根据路由name获取所有子路由
    getRouterChildrenByName(state) {
      return (name) => {
        let arr = [];
        state.routerList.forEach((row) => {
          if (row.name === name) {
            arr = row.children || [];
          }
        });
        return arr;
      };
    },
    getLeftMenuList(state) {
      return state.leftMenuList;
    },
    getPermisstionsList(state) {
      return state.permissions;
    },
  },

  mutations: {
    setRouterList(state, menus) {
      // 获取菜单的父级菜单url拼接为完整path
      // const getComponent = (route, url) => {
      //   if (route.parentId) {
      //     let menu = menus.find((rw) => rw.id === route.parentId);
      //     if (!menu) return url;
      //     url = `/${menu.resourceUrl}${url}`;
      //     return getComponent(menu, url);
      //   } else {
      //     return url;
      //   }
      // };
      state.routerList = menus.map((row) => {
        // 后端返回的url中是不包含'/'的所以这里由前端加上
        // let url = getComponent(row, `/${row.resourceUrl}`);
        return {
          path: row.componentUrl || `/${row.resourceUrl}`,
          name: row.resourceUrl,
          text: row.resourceCname,
          id: row.id,
          parentId: row.parentId,
          component: row.componentUrl || `/${row.resourceUrl}`,
          iconName: geticonClassName(row.iconUrl),
          isExternalLink: row.isExternalLink,
        };
      });
      state.routerTreeList = arrayToJson(deepCopy(state.routerList), 'id', 'parentId');
    },
    setPremissions(state, permissions) {
      state.permissions = permissions;
    },
    setLeftMenuList(state, leftMenuList) {
      state.leftMenuList = leftMenuList;
    },
    setAllRouterList(state, allRouterList) {
      state.allRouterList = allRouterList;
    },
  },

  actions: {
    async setRouterList({ commit, dispatch }) {
      let isThirdParty = window.sessionStorage.getItem('isThirdParty') || '0'; //避免刷新后调用获取用户信息的接口不对
      try {
        // 获取iVDG的菜单信息等
        const res = await dispatch(isThirdParty === '1' ? 'user/getUserInfoByThird' : 'user/getUserInfo', null, {
          root: true,
        });
        // 获取iVDG的所有菜单信息储存
        const allRouterRes = await http.get(common.getApplicationResourceList, {
          params: {
            applicationCode: '00000002',
          },
        });
        commit('setAllRouterList', allRouterRes.data.data);
        // 只返回页面或模块菜单
        const menus = res.data.data.resourceVoList.filter(
          (row) => row.resourceType === '1' || row.resourceType === '2',
        );
        const datascopeVoList = res.data.data.datascopeVoList;
        const permissions = res.data.data.permissions;
        commit('setRouterList', menus);
        commit('setPremissions', permissions);
        dispatch('common/setOrganizationList', datascopeVoList, {
          root: true,
        });
      } catch (err) {
        console.log(err);
      }
    },
    setLeftMenuList({ commit }, leftMenuList) {
      commit('setLeftMenuList', leftMenuList);
    },
  },
};
