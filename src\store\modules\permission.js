import store from '@/store'
import { asyncRoutes } from '@/router/router.config'
import BasicLayout from '@/layouts/basic-layout'
import iconsList from '@/assets/configureIcons/iconfont.json'
/**
 * @description 根据iconid 匹配icon className
 * @param {*} shortNumber iconid
 */
// 动态菜单图标
const iconsDataList = iconsList.glyphs
function geticonClassName (iconUrl) {
  let font_class
  for (let i = 0; i < iconsDataList.length; i++) {
    if (iconsDataList[i].icon_id == iconUrl) {
      font_class = iconsDataList[i].font_class
      break
    }
  }
  return font_class ? 'icon-' + font_class : null
}
/**
 * 添加动态(菜单)路由
 * @param {*} menuList 菜单列表
 * @param {*} routes 递归创建的动态(菜单)路由
 */
let btnPermission = []
export const fnAddDynamicMenuRoutes = (menuList = [], asyncRoutesList = []) => {
  // 动态路由处理，后续只做获取不做数据改变（addRouters）/ 获取所有按钮权限并删除
  const res = []
  // menuList遍历,数据聚合
  function menulistAll (routerList) {
  var delIndex = []
    routerList.map((val, index) => {
      val = Object.assign(val, val[val.resourceCode])
      if (val && val.resourceType === '3') {
        btnPermission.push(val.permission)
        // 删除按钮权限
        delIndex.push(index)
        // routerList.splice(num, 1)
      }

      if (val.children && val.children.length > 0) {
        menulistAll(val.children)
      }
    })
    delIndex.forEach(item => {
      routerList.splice(item, 1)
    })
    return routerList
  }
  // 子菜单处理
  function menulistChildren (routerList, parentUrl, parentNameA, newRoute) {
    if (!newRoute) {
      newRoute = []
    }
    routerList.map(val => {
      if (val && val.resourceType === '3') {
        btnPermission.push(val.permission)
      } else {
        let item = {
          path: `${parentUrl}/${val.permission}`,
          name: val.permission,
          text: val.resourceCname,
          id: val.id,
          parentId: val.parentId,
          parentNameA: parentNameA,
          component: `${parentUrl}/${val.permission}`,
          iconName: geticonClassName(val.iconUrl),
          meta: {
            title: val.resourceCname,
            icon: geticonClassName(val.iconUrl),
            resourceUrl: val.componentUrl || val.resourceUrl,
          },
          resourceCode: val.resourceCode || '',
          isExternalLink: val.isExternalLink,
          resourceUrl: val.componentUrl || val.resourceUrl,
          children: []
        }
        if (val.children && val.children.length > 0 && val.resourceType === '1') {
          item.path = `${parentUrl}/${val.permission}`; //? 导致点击一级目录，直接跳到有三级目录的菜单下
          menulistChildren(val.children, item.component, val.permission, item.children)
        }
        newRoute.push(item)
      }
    })
    return newRoute
  }
  // 父级菜单单独处理compoent
  menulistAll(menuList).forEach((item, index) => {
    // 通用权限
    if (item.resourceType == '8') return
    if (item.resourceType == '9') {
      store.commit('SET_MY_MENU', item.children)
      return
    }
  let router = {
      path: `/${item.permission}`,
      name: item.permission,
      id: item.id,
      parentId: item.parentId,
      component: BasicLayout,
      iconName: geticonClassName(item.iconUrl),
      meta: {
        title: item.resourceCname,
        icon: geticonClassName(item.iconUrl),
        resourceUrl: item.componentUrl || item.resourceUrl,
      },
      resourceCode: item.resourceCode || '',
      isExternalLink: item.isExternalLink,
      resourceUrl: item.componentUrl || item.resourceUrl,
    }
    if (item.children && item.children.length > 0) {
      router.children = menulistChildren(item.children, router.path, item.permission)
    } else {
      router.path = `/${item.permission}view`
      router.name = `${item.permission}view`
      router.children = [
        {
          path: `/${item.permission}`,
          name: item.permission,
          component: `/${item.permission}`,
          parentId: item.id,
          parentNameA: `/${item.permission}`,
          hidden: true,
          meta: {
            title: item.resourceCname,
            resourceUrl:item.componentUrl || item.resourceUrl,
            icon: null,
          },
        }
      ]
    }
    res.push(router)
  })
  return res
}

/**
 * 过滤账户是否拥有某一个权限，并将菜单从加载列表移除
 *
 * @param permission
 * @param route
 * @returns {boolean}
 */
function hasPermission (permission, route) {
  if (route.meta && route.meta.permission) {
    let flag = false
    for (let i = 0, len = permission.length; i < len; i++) {
      flag = route.meta.permission.includes(permission[i])
      if (flag) {
        return true
      }
    }
    return false
  }
  return true
}

function filterAsyncRouter (routerMap, sysApplicationMenu, roles = []) {
  const asyncRoutesList = asyncRoutes
  // 其他验证，如果有执行二次验证如果没有直接返回，
  if (roles.length > 0) {
    const accessedRouters = fnAddDynamicMenuRoutes(
      sysApplicationMenu,
      asyncRoutesList,
    ).filter((route) => {
      if (hasPermission(roles, route)) {
        if (route.children && route.children.length) {
          route.children = filterAsyncRouter(route.children, roles)
        }
        return true
      }
      return false
    })
    return accessedRouters
  } else {
    return fnAddDynamicMenuRoutes(sysApplicationMenu, asyncRoutesList)
  }
}
// 所有路由转为一级路由
const everyRoute = ({ routerList, newRouter, type = "dynamic" }) => {
  if (!newRouter) {
    newRouter = []
  }
  routerList.map(item => {
    if (type === 'dynamic') {
      newRouter.push(item)
    } else {
      if (item.tabShow && item.tabShow === true) {
        newRouter.push(item)
      }
    }
    if (item.children && item.children.length > 0) {
      everyRoute({ routerList: item.children, newRouter: newRouter, type: type })
    }
    // delete item.children
  })
  return newRouter
}
const permission = {
  state: {
    routers: [], // 所有路由
    myMenu: [],   // 我的菜单
    addRouters: [], // 动态路由
    btnPermission: [],// 按钮权限
    routeTile: [], // 平埔路由（静态）
  },
  mutations: {
    SET_ROUTERS: (state, addRouters) => {
      state.addRouters = addRouters
      // 动态路由地址一拼接，无需再次
      state.routers = asyncRoutes.concat(addRouters)

    },
    SET_BTN_PERMISSION: (state, btnPermission) => {
      state.btnPermission = btnPermission
    },
    SET_ROUTERTILE: (state, routeTile) => {
      state.routeTile = routeTile
    },
    SET_MY_MENU: (state, menu) => {
      state.myMenu = menu
    }
  },
  getters: {
    addRouters: (state) => state.addRouters || [],
    routers: (state) => state.routers || [],
    btnPermission: (state) => state.btnPermission || [],
    routeTile: (state) => state.routeTile || [],
    myMenu: (state) => state.myMenu || []
  },
  actions: {
    GenerateRoutes ({ commit, dispatch }, data) {
      return new Promise((resolve) => {
        btnPermission = [] // 清空按钮权限
        const sysApplicationMenu = data.children
        const accessedRouters = filterAsyncRouter(asyncRoutes, sysApplicationMenu)
        let newRouter = []
        everyRoute({ routerList: accessedRouters, newRouter: newRouter })
        let routeTile = []
        everyRoute({ routerList: asyncRoutes, newRouter: routeTile, type: 'staticState' })
        accessedRouters.push({ path: '*', redirect: '/404', hidden: true })
        commit('SET_ROUTERS', accessedRouters)
        commit('SET_ROUTERTILE', routeTile)
        commit('SET_BTN_PERMISSION', btnPermission)
        resolve(accessedRouters)
      })
    }
  }
}

export default permission
