<!--
    * @FileDescription: 数据月度趋势
    * @Author: H
    * @Date: 2024/04/28
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="chart">
        <div id="taskChart" class="taskChart" ref="taskChart"></div>
    </div>
</template>
<script>
import * as echarts from 'echarts'
export default{
    data() {
        return {
            formData: {
                type: ''
            },
            option: {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'rgba(2, 27, 71, 0.8)',
                    borderColor: '#098EFF',
                    borderWidth: 1,
                    textStyle: {
                        color: '#ffffff'
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: true,
                    axisLabel:{
                        interval: 0,
                        lineStyle: {
                            color: '#A8D0FF'
                        },
                        textStyle: {
                            color: '#B6CBD5'
                        }
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            type: 'dashed',
                            color: 'rgba(168, 208, 255, 0.2)'
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    data: ['1月','2月','3月','4月','5月','6月','7月','8月','9月','10月','11月','12月']
                },
                yAxis: {
                    type: 'value',
                    name: '(万)',
                    nameLocation: 'start',
                    nameTextStyle: {
                        color: '#B6CBD5',
                        padding: [0, 25, 0, 0]
                    },
                    nameGap: 15,
                    splitLine: {
                        show: false
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#B6CBD5'
                        },
                    }
                    
                },
                grid: {
                    top: '15px',
                    left: '30px',
                    right: '20px',
                    bottom: '30px'
                },
                series: [
                    {
                        data: [ '8','12','10','20','22','18','24','22','20','18','16','12' ],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 10,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: '#0096FF',
                            }
                        },
                        itemStyle: {
                            emphasis: {
                                color: '#0096FF',
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        },
                        smooth: true,
                    },
                    {
                        data: [ '12','16','14','24','26','22','28','26','24','22','20','16' ],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 10,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: '#00F4FC',
                            }
                        },
                        itemStyle: {
                            emphasis: {
                                color: '#0096FF',
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        },
                        smooth: true, 
                    },
                    {
                        data: [ '4','23','23','14','16','12','18','16','14','12','10','26' ],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 10,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: '#2AD169',
                            }
                        },
                        itemStyle: {
                            emphasis: {
                                color: '#0096FF',
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        },
                        smooth: true, 
                    },
                    {
                        data: [ '32','12','4','12','20','14','8','12','12','8','13','9' ],
                        type: 'line',
                        symbol: 'circle',
                        symbolSize: 10,
                        showSymbol: false,
                        lineStyle: {
                            normal: {
                                color: '#FFC963',
                            }
                        },
                        itemStyle: {
                            emphasis: {
                                color: '#0096FF',
                                borderColor: '#ffffff',
                                borderWidth: 2
                            }
                        },
                        smooth: true, 
                    }
                ]
            },
            myEchart: null
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            this.myEchart = echarts.init(this.$refs.taskChart)
            let option = {
                // title: this.title,
                // legend: this.legend,
                // grid: this.grid,
                // xAxis: this.xAxis,
                // yAxis: this.yAxis,
                // tooltip: this.tooltip,
                // series: this.series
            }
            this.myEchart.setOption(this.option)
            window.addEventListener('resize', () => this.myEchart.resize())
        },
        handleChange() {

        }
    }
}
</script>
<style lang='less' scoped>
.chart{
    height: 100%;
    width: 100%;
    position: relative;
    .taskChart{
        height: 100%;
        width: 100%;  
    }
}
</style>