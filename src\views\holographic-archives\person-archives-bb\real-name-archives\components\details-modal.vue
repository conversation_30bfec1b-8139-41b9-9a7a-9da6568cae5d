<!--
    * @FileDescription: 人员档案 - 实名档案-详情
    * @Author: H
    * @Date: 2023/03/7
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="dom-wrapper">
        <div class="dom" @click="($event) => $event.stopPropagation()">
            <header>
				<span>抓拍详情</span>
				<Icon type="md-close" size="14" @click.native="() => $emit('close', $event)" />
			</header>
            <section class="dom-content">
                <div class="info-box">
                    <div class="info-box-left">
                        <div class="info-photo"></div>
                        <div class="details-list">
                            <div class="wrapper-content">
								<span class="label">身份证号</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">姓名</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">轨迹数据</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">抓拍时间</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                            <div class="wrapper-content">
								<span class="label">抓拍地点</span>
								<ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName || '--'"></ui-textOver-tips>
							</div>
                        </div>
                    </div>
                    <div class="info-box-right">
                        <div class="charts-container">

                        </div> 
                        <div class="search">

                        </div>
                        <div class="table-content">
                            <div class="list-card box-1" v-for="(item, index) in dataList" :key="index">
                                <div class="img-content">
                                    <div class="similarity" v-if="item.idScore">
                                        <span class="num" :class="{'gerling-num':queryParam.algorithmVendorType == 'GLST'}" v-if="item.idScore">{{ item.idScore }}%</span>
                                    </div>
                                    <template>
                                        <ui-image :src="item.traitImg" alt="动态库" />
                                    </template>
                                </div>
                                <!-- 动态库 -->
                                <div class="bottom-info">
                                    <time>
                                        <Tooltip content="抓拍时间" placement="right" transfer theme="light">
                                            <i class="iconfont icon-time"></i>
                                        </Tooltip>
                                        {{ item.absTime }}
                                    </time>
                                    <p>
                                        <Tooltip content="抓拍地点" placement="right" transfer theme="light">
                                            <i class="iconfont icon-location"></i>
                                        </Tooltip>
                                        <ui-textOver-tips refName="detailAddress" :content="item.deviceName"></ui-textOver-tips>
                                    </p>
                                </div>
                            </div>
                            <div class="empty-card-1" v-for="(item, index) of 9 - (dataList.length % 9)" :key="index + 'demo'"></div>
                        </div>
                        <ui-empty v-if="dataList.length === 0 && !loading"></ui-empty>
                        <ui-loading v-if="loading"></ui-loading>
                        <ui-page :current="pageData.pageNumber" :total="total" countTotal :page-size="pageData.pageSize" :page-size-opts="[10, 20, 50, 100]" @pageChange="pageChange" @pageSizeChange="pageSizeChange"> </ui-page>
                    </div>
                </div>
            </section>
        </div>
    </div>
</template>
<script>
import { queryTrajectoryListPAndID, detailByIdNumber } from '@/api/person-archives-bb';
export default {
    data() {
        return {
            loading: false,
            dataList: [{'deviceName': '12435454'}],
            datailsInfo: {
                deviceName: '23234345'
            },
            pageData: {
                pageNumber: 1,
                pageSize: 20,
            },
            total: 0,
        }
    },
    created() {},
    methods: {
        show(data) {
            let params = {
                idNumber: data.idCardNo,
				archiveNo: data.archiveNo,
                ...this.pageData,
                beginDate: '',
			    endDate: '',
                algorithmManufacturer: '',
                profileId: '',
                deviceId: ''
            };
            queryTrajectoryListPAndID(params)
            .then(res => {

            })
            detailByIdNumber({ idCardNo: data.idCardNo })
            .then(res =>{

            })

        },
        queryList() {

        },
        pageChange(size) {
            this.pageData.pageNumber = size;
            this.queryList();
        },
        pageSizeChange(size) {
            this.pageData.pageNumber = 1;
            this.pageData.pageSize = size;
            this.queryList();
        },
        close() {
            this.$emit('close')
        }
    }
}
</script>
<style lang="less" scoped>
    @import "./style/index";
</style>
