<template>
  <div class="page-evaluationmanagement">
    <div class="left-content auto-fill">
      <div class="record-title font-active-color">
        <span class="base-text-color f-14">考核方案</span>
        <Button type="primary" class="fr" @click="addAssessment()">
          <i class="icon-font icon-tianjia f-14" title="添加"> </i>
          <span class="inline vt-middle ml-sm">新建方案</span>
        </Button>
      </div>
      <PlanLeft ref="PlanLeft" @selectModule="selectModule" @edit="addAssessment" />
    </div>
    <div class="right-content">
      <TableList ref="infoList" :columns="columns" :minusHeight="minusHeight" :loadData="loadData" :paging="false">
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <span class="f-14 pr100 base-text-color"
            >共
            <span class="f-16" style="color: var(--color-display-text)">{{ listLength }}</span>
            项考核内容</span
          >
          <span class="f-14 base-text-color"
            >总分：
            <span class="f-16" style="color: var(--color-failed)"> {{ num }}</span>
          </span>
          <Button type="primary" class="fr" @click="edit('add')">
            <i class="icon-font icon-tianjia f-14" title="添加"> </i>
            <span class="inline vt-middle ml-sm">新增考核</span>
          </Button>
        </div>
        <template #switchItem="{ row, index }">
          <i-switch
            class="iswitch"
            size="small"
            :value="row.enableState === '1' ? true : false"
            :before-change="() => handleBeforeChange(row, index)"
          />
        </template>
        <template #action="{ row }">
          <ui-btn-tip icon="icon-bianji2" class="mr-md f-14" content="编辑" @click.native="edit(row)"></ui-btn-tip>
          <ui-btn-tip
            icon="icon-shanchu3"
            class="f-14"
            v-if="row.editType"
            :styles="{ color: '#CC4242' }"
            content="删除"
            @click.native="del(row)"
          ></ui-btn-tip>
        </template>
      </TableList>
    </div>
    <!-- 考核方案新增编辑 -->
    <AddCheckModule ref="addCheckModule" :modalAction="modalAction" @query="query" />
    <EditssessmentModule ref="editCheckModule" :modalTitle="modalTitle" />
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';
export default {
  name: 'assessment',
  data() {
    return {
      modalAction: { title: '编辑方案' },
      modalTitle: { title: '编辑考核' },
      searchData: {
        schemeId: '',
      },
      tableList: [],
      num: '',
      listLength: 0,
      loadData: () => {
        return this.$http
          .get(governanceevaluation.schemeGetSchemeContent + '?id=' + this.searchData.schemeId)
          .then((res) => {
            let data = {
              data: {
                total: res.data.data.length || 0,
                entities: res.data.data.list,
              },
            };
            this.tableList = res.data.data.list || [];
            this.num = res.data.data.num;
            this.listLength = res.data.data.list.length || 0;
            return data;
          });
      },
      minusHeight: 208, // 表格
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '考核内容', key: 'name' },
        {
          title: '考核项数量',
          key: 'schemeContentCount',
          width: 180,
          align: 'center',
        },
        { title: '分值', key: 'score', width: 180, align: 'center' },
        { title: '是否启用', slot: 'switchItem', width: 180, align: 'center' },
        {
          title: '操作',
          slot: 'action',
          width: 70,
          fixed: 'right',
          align: 'center',
        },
      ],
    };
  },
  created() {},
  components: {
    PlanLeft: require('./plan-left').default,
    TableList: require('./components/tableList').default,
    AddCheckModule: require('./components/add-check-module').default,
    EditssessmentModule: require('./components/edit-assessment-module.vue').default,
  },
  methods: {
    // 切换考核方案
    selectModule(item) {
      this.searchData.schemeId = item.id;
      this.$refs.infoList.info(true);
    },
    // 刷新列表
    refalsh() {
      this.$refs.infoList.info(true);
    },
    // 打开新增编辑考核方案
    addAssessment(item) {
      this.$refs.addCheckModule.open(item);
    },
    // 新增考核返回
    query(val) {
      this.$refs.PlanLeft.get_left();
      val && this.$refs.PlanLeft.selectModule(val.data);
    },
    addKaoHe() {
      this.$refs.addAssessmentModule.open();
    },
    // 是否启用
    handleBeforeChange(row, index) {
      let state = this.tableList[index].enableState === '1' ? '0' : '1';
      return new Promise((resolve) => {
        this.$http.get(governanceevaluation.enableDisable + '?id=' + row.id + '&num=' + state).then((res) => {
          this.tableList[index].enableState = state;
          this.$Message.success(res.data.msg);
          this.getNum();
        });
      });
    },
    // 更新总积分
    getNum() {
      this.$http.get(governanceevaluation.schemeGetSchemeContent + '?id=' + this.searchData.schemeId).then((res) => {
        this.num = res.data.data.num;
      });
    },
    // 编辑考核内容
    edit(row) {
      if (row === 'add') {
        this.modalTitle.title = '新增考核';
        this.$refs.editCheckModule.open(row, this.searchData.schemeId);
      } else {
        this.$refs.editCheckModule.open(row.id, row.schemeId);
      }
    },
    del(row) {
      this.$UiConfirm({
        content: `您要删除${row.name}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.$http
            .get(governanceevaluation.removeSchemeContent, {
              params: {
                id: row.id,
                schemeContentCode: row.schemeContentCode,
                schemeId: row.schemeId,
              },
            })
            .then(() => {
              this.$Message.success('删除成功！');
              this.$refs.infoList.info(true);
            });
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 删除考核内容
    deleteItem() {},
  },
};
</script>
<style lang="less" scoped>
.page-evaluationmanagement {
  overflow: hidden;
  height: 100%;
  .record-title {
    height: 34px;
    line-height: 34px;
    margin: 0 10px 10px;
  }
  .hearder-title {
    height: 34px;
    line-height: 34px;
    margin-bottom: 12px;
  }
  .left-content {
    background: var(--bg-content);
    float: left;
    width: 300px;
    background: var(--bg-content);
    height: 100%;
    padding: 20px 10px 10px;
    border-right: 1px solid var(--border-modal-footer);
  }

  .right-content {
    float: right;
    width: calc(~'100% - 300px');
    height: 100%;
    padding: 20px 20px 0 20px;
    background: var(--bg-content);
    .hearder-title {
      margin-bottom: 10px;
    }
    .tab {
      padding: 10px;
    }
  }
}
.pr100 {
  padding-right: 100px;
}
</style>
