<template>
  <ui-modal v-model="visible" title="查看图片" :styles="styles" class="check-picture" footer-hide>
    <ui-label class="mr-lg inline" label="检测结果：" :width="70">
      <ui-switch-tab
        class="inline"
        v-model="searchData.outcome"
        :tab-list="tagList"
        @changeTab="changeTab"
      ></ui-switch-tab>
    </ui-label>
    <ui-label class="inline fr" label="异常原因" :width="70">
      <Select
        v-model="searchData.causeErrors"
        @on-change="changeErrorReason"
        class="width-lg fr"
        clearable
        multiple
        placeholder="请选择异常原因"
        :max-tag-count="1"
      >
        <Option v-for="(item, index) in checkList" :key="index" :value="item.value">{{ item.label }}</Option>
      </Select>
    </ui-label>
    <div class="check-content-box auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <!-- 动态渲染图片模式[横版、竖版] -->
      <component
        :is="imageComponents"
        :card-list="cardList"
        :img-key="imgKey"
        :filed-name-map="filedNameMap"
        @handleBigImgView="(row) => artificialReview(row, 'viewOnly')"
        @artificialReview="artificialReview"
        @algorithmsReview="(cardData) => $emit('algorithmsReview', cardData)"
      ></component>
    </div>
    <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    <!-- 人工复核 -->
    <review-and-detail
      mode="image"
      v-model="artificialVisible"
      :active-index-item="activeIndexItem"
      :review-row-data="detailData"
      :page-data="searchData"
      :total-count="searchData.totalCount"
      :table-data="cardList"
      :qualified-list="qualifiedList"
      :styles="{ width: '3.5rem' }"
      :review-visible="true"
      :custorm-params="reviewCustormParams"
      :error-code-list="checkList"
      :filed-name-map="filedNameMap"
      :title="reviewModalType==='viewOnly'? '查看图片' : '人工复核'"
      :review-modal-type="reviewModalType"
      :get-list-api="getListApi"
      :custom-value-field="customValueField"
      :search-parames="searchData"
      @closeFn="updateTable"
    ></review-and-detail>
  </ui-modal>
</template>
<style lang="less" scoped>
.check-picture {
  @media screen and (max-width: 1366px) {
    .ui-gather-card {
      margin-right: 10px;
      width: 186px !important; /*no*/
      height: 220px !important; /*no*/
    }
    @{_deep}.ivu-modal {
      width: 1202px !important; /*no*/
    }
    .ui-image-card {
      height: 170px !important; /*no*/
      width: 170px !important; /*no*/
      cursor: pointer;
    }
    .image-box {
      @{_deep}.ui-image {
        z-index: initial;
        .ui-image-div {
          .tileImage {
            height: 170px !important; /*no*/
            width: 170px !important; /*no*/
          }
        }
      }
    }

    .icon-box {
      width: 91% !important; /*no*/
      top: 156px !important; /*no*/
    }
  }

  @{_deep} .ivu-modal-header {
    padding: 0;
  }
  @{_deep} .ivu-modal-body {
    height: 100%;
  }
  @{_deep} .ivu-modal-content {
    width: 100%;
    height: 100%;
  }
  .check-content-box {
    height: 88%;
    padding-top: 10px;
  }
  .check-content-wrap {
    width: 100%;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    overflow-y: auto;
    margin-top: 10px;
    .empty-item {
      width: 188px; /*no*/
    }
  }
  .ui-gather-card {
    width: 188px; /*no*/
    height: auto; /*no*/
    margin-bottom: 10px;
    padding: 10px;
    background: var(--bg-info-card);
    border: 1px solid var(--border-info-card);
    position: relative;
    .image-box {
      position: relative;
      @{_deep}.ui-image {
        z-index: initial;
        .ui-image-div {
          .tileImage {
            height: 190px;
            width: 100%;
          }
        }
      }
    }
    &-right {
      flex: 1;
      &-item {
        margin-bottom: 8px;
        font-size: 12px;
        &-label {
          color: #8797ac !important;
          font-size: 12px;
        }
        &-value {
          color: #8797ac !important;
          font-size: 12px;
        }
        .wrapper {
          width: 100%;
        }
      }
    }
  }
  .icon-box {
    position: absolute;
    width: 100%;
    bottom: 0;
    // z-index: 99;
    padding: 0 5px;
    text-align: center;
    line-height: 30px;

    background: rgba(0, 0, 0, 0.39);
    .icon-inner-box {
      background-color: transparent;
    }
    .pointer {
      width: 100%;
      text-align: center;
    }
  }
  .ui-gather-card-image-item {
    font-size: 14px;
    margin: 4px 0 4px 0;
  }
  .ui-image-card {
    height: 190px; /*no*/
    width: 100%;
    cursor: pointer;
  }
  .img:hover {
    .shadow-artificial {
      display: block;
    }
  }
  .check-list {
    // width: 460px;
    margin-left: 80px;
    margin-top: 10px;
  }
  .check-text {
    display: inline-block;
    // width: 110px;
  }
  .desc {
    margin-left: 80px;
    width: 80%;
  }

  .shadow-artificial {
    height: 28px;
    line-height: 29px;
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    top: 172px;
    width: 90%;
    display: none;
    padding-left: 10px;
    .artificial-text {
      color: var(--color-primary);
      cursor: pointer;
      vertical-align: middle;
    }
    @media screen and (max-width: 1366px) {
      .artificial-text {
        color: var(--color-primary);
        cursor: pointer;
        vertical-align: middle;
      }
    }
  }
  .artificial-data {
    padding: 0 50px;
  }
}
</style>
<script>
import particularMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/particularMixin';
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [particularMixin, dealWatch],
  props: {
    //currentRow 当前行
    list: {
      type: Object,
      default: () => {},
    },
    value: {
      required: true,
      type: Boolean,
    },
    resultId: {},
    interface: {},
    imgKey: {},
    activeIndexItem: {
      type: Object,
      default: () => {},
    },
    getParams: {},
    // 样式 - 默认是竖版模式展示
    imageComponents: {
      default: 'picMode',
    },
    // 特殊处理数据回调函数（默认直接赋值返回）
    handleImageDataCallBack: {
      type: Function,
      default: (that, cardList) => {
        that.cardList = cardList;
      },
    },
    // 查看图片 [兼容取值字段不一致问题] - 人工复核（review-and-detail组件使用）
    filedNameMap: {
      default: () => {
        // 默认以人脸为主
        return {
          smallPicName: 'facePath', // 小图
          bigPicName: 'scenePath', // 大图
          qualified: 'qualified', // 不合格
          description: 'reason', // 设备备注
          resultTip: 'resultTip', // 图片备注
        };
      },
    },
    unImmediateInit: {
      //为true弹窗打开后不立即执行init函数
      type: Boolean,
      default: false,
    },
    customValueField: {}
  },
  data() {
    return {
      tagList: [
        {
          label: '合格',
          value: '1',
        },
        {
          label: '不合格',
          value: '2',
        },
      ],
      visible: false,
      searchData: {
        outcome: '1',
        causeErrors: [],
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '1400px',
        height: '800px',
      },
      imgList: [], // 大图图片
      bigPictureShow: false, //大图展示
      loading: false,
      artificialStyles: {
        width: '3rem',
      },
      checkList: [],
      artificialVisible: false,
      artificialRow: {},
      cardList: [],
      qualifiedList: [
        { key: '图片可用', value: '1' },
        { key: '图片不可用', value: '2' },
      ],
      detailData: {},
      mode: 'device', //默认设备模式  type: 'device', type: 'detail',
      errorCodeList: [], //图片模式错误原因
      reviewCustormParams: {},
      isPreventInit: false, //是否阻止弹窗打开后的立即执行的请求
      reviewModalType: 'review', //review 复核，viewOnly 查看图片
    };
  },
  methods: {
    getQualificationList(mode) {
      // 异常原因
      this.MixinDisQualificationList(mode).then((data) => {
        this.checkList = data.map((item) => {
          // 嘉鹏说: 设备模式查询需转换数字模式
          return { value: mode == 1 ? Number(item.key) : item.key, label: item.value };
        });
      });
    },
    // 人工复核
    artificialReview(row, viewType) {
      this.reviewModalType = viewType || 'review';
      let params = (this.getParams && this.getParams(row)) || {};
      this.detailData = {
        ...row,
        ...params,
      };
      this.artificialVisible = true;
      this.reviewCustormParams = {
        deviceDetailId:
          this.activeIndexItem.indexModule === '9'
            ? row.bodyDeviceDetailId
            : this.activeIndexItem.indexModule === '3'
            ? row.evaluationDeviceDetailId
            : row.faceDeviceDetailId,
        deviceId: row.deviceId,
        errorCode: (row.causeError && row.causeError.split(',')) || [],
        type: 'detail',
        taskIndexId: row.taskIndexId,
      };
    },
    changeErrorReason(val) {
      this.searchData.causeErrors = val;
      this.init();
    },
    async init() {
      if (this.isPreventInit) {
        //避免多次请求数据
        this.isPreventInit = false;
        return;
      }
      try {
        this.loading = true;
        let params = {
          customParameters: {
            outcome: this.searchData.outcome,
            causeErrors: this.searchData.causeErrors || [],
            faceDeviceDetailId: this.list.id, //人脸
            bodyDeviceDetailId: this.list.id, // 人体
            deviceIds: (this.list.deviceId && this.list.deviceId.split(',')) || [], //车辆参数
          },
          pageSize: this.searchData.pageSize,
          pageNumber: this.searchData.pageNum,
        };
        let { cardList, total } = await this.interface(params);
        this.handleImageDataCallBack(this, cardList ?? []);
        // this.cardList = cardList
        this.searchData.totalCount = total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changeTab(val) {
      this.$set(this.searchData, 'outcome', val);
      this.init();
    },
    // 大图展示
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageNum = 1;
      this.searchData.pageSize = val;
      this.init();
    },
    reset() {
      this.searchData = {
        outcome: '1',
        causeErrors: [],
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      };
    },
    // isReview：复核过      closeIndex：人工复核弹框关闭时 -设备 所在总表中的索引
    updateTable({ isReview, closeIndex }) {
      let { pageNum, pageSize } = this.searchData;
      let newPage = Math.floor(closeIndex / pageSize) + 1;
      this.searchData.pageNum = newPage;
      if (isReview || newPage !== pageNum) {
        this.init();
      }
    },
    async getListApi({ pageNumber, pageSize, isUpdate, currentRow }) {
      try {
        let customParameters = {
            outcome: this.searchData.outcome,
            causeErrors: this.searchData.causeErrors || [],
            faceDeviceDetailId: this.list.id, //人脸
            bodyDeviceDetailId: this.list.id, // 人体
            deviceIds: (this.list.deviceId && this.list.deviceId.split(',')) || [], //车辆参数
        };
        // isUpdate=true  -- 表示需要更新某一条数据
        if (isUpdate) {
          pageNumber = 1;
          customParameters = {
            id: currentRow.id,
            faceDeviceDetailId: this.list.id, //人脸
            bodyDeviceDetailId: this.list.id, // 人体
            deviceIds: (this.list.deviceId && this.list.deviceId.split(',')) || [], //车辆参数
          };
        }
        let params = {
          customParameters: customParameters,
          pageNumber: pageNumber,
          pageSize: pageSize,
        };
        let { apiReturn } = await this.interface(params);
        return apiReturn;
      } catch (err) {
        throw new Error();
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
      if (val) {
        this.isPreventInit = this.unImmediateInit;
        this.reset();
        this.init();
        this.getQualificationList(2);
      }
    },
    list: {
      deep: true,
      immediate: true,
      handler(val) {
        this.currentRow = val;
      },
    },
  },
  computed: {},

  components: {
    PicAlgorithm: require('@/views/governanceevaluation/evaluationoResult/components/pic-algorithm/index.vue').default,
    PicMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-mode.vue').default,
    PicRowMode: require('@/views/governanceevaluation/evaluationoResult/components/pic-row-mode/index.vue').default,
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    tagView: require('./tags.vue').default,
    LookScene: require('@/components/look-scene.vue').default,
    reviewAndDetail:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/components/review-and-detail/index.vue')
        .default,
  },
};
</script>
