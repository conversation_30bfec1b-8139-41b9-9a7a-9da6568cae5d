<template>
  <div class="thematic-database">
    <div class="container detail-info">
      <div class="info">
        <img :src="imgUrl" class="info-img" alt />
        <div class="info-content">
          <div class="name primary">{{ currentRow.libName }}</div>
          <div class="info-item">
            <span class="label title-color">更新时间：</span>
            <span class="value text-color">{{ currentRow.modifyTime }}</span>
          </div>
          <div class="info-item">
            <span class="label title-color">备注：</span>
            <span class="value text-color">{{ currentRow.remark }}</span>
          </div>
        </div>
      </div>
      <div class="statistics">
        <div class="label title-color">设备数量：</div>
        <count-to
          :start-val="0"
          :end-val="pageInfo.total || 0"
          :duration="1000"
          class="num color-warning"
        ></count-to>
      </div>
    </div>
    <div class="container content">
      <div class="search card-border-color">
        <Form ref="searchData" :model="searchData" inline>
          <FormItem
            v-if="currentRow.libCategory == '3'"
            prop="mac"
            label="mac地址:"
          >
            <Input v-model="searchData.mac" placeholder="请输入mac地址" />
          </FormItem>
          <FormItem
            v-if="currentRow.libCategory == '5'"
            prop="rfidCode"
            label="rfid地址:"
          >
            <Input v-model="searchData.rfidCode" placeholder="请输入rfid地址" />
          </FormItem>
          <FormItem
            v-if="currentRow.libCategory == '4'"
            prop="imsi"
            label="IMSI编码:"
          >
            <Input v-model="searchData.imsi" placeholder="请输入IMSI编码" />
          </FormItem>
          <FormItem
            v-if="currentRow.libCategory == '4'"
            prop="imei"
            label="国际移动台设备识别码:"
          >
            <Input
              v-model="searchData.imei"
              placeholder="请输入国际移动台设备识别码"
            />
          </FormItem>
          <FormItem
            v-if="currentRow.libCategory == '6'"
            prop="obuId"
            label="Etc编号:"
          >
            <Input v-model="searchData.obuId" placeholder="请输入Etc编号" />
          </FormItem>
          <FormItem class="btn-group">
            <Button type="primary" @click="init()">查询</Button>
            <Button @click="resetHandle">重置</Button>
          </FormItem>
        </Form>
        <div class="btn-group">
          <Button @click="addHandle" v-permission="['staticLibr-personal-add']"
            ><i class="iconfont icon-jia"></i>新增</Button
          >
          <Button
            @click="delHandle('')"
            v-permission="['staticLibr-personal-dele']"
            ><i class="iconfont icon-shanchu"></i>删除</Button
          >
          <!-- <Button
            @click="exportFn"
            v-permission="['staticLibr-personal-export']"
            ><i class="iconfont icon-export"></i>导出</Button
          > -->
        </div>
      </div>
      <div class="personnel-table">
        <ui-table
          :columns="columns"
          :data="tableData"
          @on-selection-change="selectionChangeHandle"
        >
          <template #action="{ row }">
            <div class="btn-tips">
              <ui-btn-tip
                content="编辑"
                icon="icon-bianji"
                class="primary"
                @click.native="editHandle(row)"
                v-permission="['staticLibr-personal-update']"
              />
              <ui-btn-tip
                content="删除"
                icon="icon-shanchu"
                class="primary"
                @click.native="delHandle(row)"
                v-permission="['staticLibr-personal-dele']"
              />
            </div>
          </template>
        </ui-table>
      </div>
      <ui-page
        :current="pageInfo.pageNumber"
        :total="pageInfo.total"
        :page-size="pageInfo.pageSize"
        @pageChange="pageChange"
        @pageSizeChange="pageSizeChange"
      ></ui-page>
    </div>
    <!-- 新增/编辑车辆 -->
    <AddModal ref="addModal" :currentRow="currentRow" />
  </div>
</template>
<script>
import { sensoryDataList, sensoryDataDel } from "@/api/data-warehouse";
import CountTo from "vue-count-to";
import AddModal from "./components/add-modal.vue";
export default {
  components: {
    CountTo,
    AddModal,
  },
  data() {
    return {
      currentRow: {},
      imgUrl: require("@/assets/img/default-img/vehicle_archives_default.png"),
      searchData: {
        mac: undefined,
        rfidCode: undefined,
        imsi: undefined,
        imei: undefined,
      },
      tableData: [],
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      selectionList: [],
    };
  },
  computed: {
    columns() {
      let columns = [
        { type: "selection", width: 60, align: "center" },
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "更新时间", key: "modifyTime" },
        { title: "操作", slot: "action", width: 174 },
      ];
      if (
        this.currentRow.libCategory == "3" ||
        this.currentRow.libCategory == "4"
      ) {
        columns.splice(2, 0, { title: "身份证号", key: "idCardNo" });
        columns.splice(2, 0, { title: "姓名", key: "name", width: 130 });
      }
      let row = "";
      if (this.currentRow.libCategory == "3")
        row = { title: "Mac地址", key: "mac" };
      if (this.currentRow.libCategory == "4") {
        row = { title: "IMSI编码", key: "imsi" };
        columns.splice(2, 0, { title: "国际移动台设备识别码", key: "imei" });
      }
      if (this.currentRow.libCategory == "5")
        row = { title: "Rfid地址", key: "rfidCode" };
      if (this.currentRow.libCategory == "6")
        row = { title: "Etc编号", key: "obuId" };
      if (row) columns.splice(2, 0, row);
      return columns;
    },
  },
  async created() {
    this.currentRow = this.$route.query;
    this.currentRow.libCount = parseInt(this.$route.query.libCount);
    this.$nextTick(() => {
      this.init();
    });
  },
  async activated() {},
  methods: {
    init() {
      this.pageInfo.pageNumber = 1;
      this.tableData = [];
      this.loading = true;
      var param = {
        ...this.searchData,
        ...this.pageInfo,
        libId: this.currentRow.id,
        libCategory: this.currentRow.libCategory,
      };
      sensoryDataList(param).then((res) => {
        this.tableData = res.data.entities;
        this.pageInfo.total = res.data.total;
      });
    },
    // 重置
    resetHandle() {
      this.$refs.searchData.resetFields();
      this.pageInfo.pageNumber = 1;
      this.init();
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.init();
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.init();
    },
    // 新增
    addHandle() {
      this.$refs.addModal.show(false);
    },
    // 编辑
    editHandle(item) {
      this.$refs.addModal.show(true, item);
    },
    // 表格选中框事件
    selectionChangeHandle(list) {
      this.selectionList = list;
    },
    // 删除
    delHandle(item) {
      if (item) {
        // 单个删除
        this.$Modal.confirm({
          title: "提示",
          width: 450,
          closable: true,
          content: `确定删除该设备信息？`,
          onOk: () => {
            // this.$Message.success('删除成功')
            sensoryDataDel({
              ids: [item.id],
              libCategory: item.libCategory,
            }).then((res) => {
              this.init();
            });
          },
        });
      } else {
        // 批量删除
        if (!this.selectionList.length) {
          this.$Message.warning("请选择要删除的设备信息");
        } else {
          this.$Modal.confirm({
            title: "提示",
            width: 450,
            closable: true,
            content: `确定批量删除设备信息？`,
            onOk: () => {
              let idlist = this.selectionList.map((item) => item.id);
              sensoryDataDel({
                ids: idlist,
                libCategory: this.selectionList[0].libCategory,
              }).then((res) => {
                this.$Message.success("删除成功");
                this.selectionList = [];
                this.pageInfo.pageNumber = 1;
                this.init();
              });
            },
          });
        }
      }
    },
    exportFn() {
      if (this.pageInfo.total == 0) return;
      if (!this.selectionList.length) {
        this.$Message.warning("请选择数据");
        return;
      }
      var ids = [];
      this.selectionList.forEach((item, index) => {
        ids.push(item.id);
      });
      var obj = {
        featureLibId: this.$route.query.featureLibId,
        ...this.searchData,
        ids,
      };
      var param = {
        taskName: 4,
        taskType: 2,
        condition: JSON.stringify(obj),
      };
      console.log(param);
      // doExportVehicleData(param).then((res) => {
      //   let aLink = document.createElement("a");
      //   aLink.href = res.data;
      //   aLink.click();
      // });
    },
    fileUploadSuccess(data) {
      this.init();
    },
  },
};
</script>
<style lang="less" scoped>
.thematic-database {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .detail-info {
    height: 120px;
    padding: 10px;
    flex: none;
    display: flex;
    margin-bottom: 10px;
    .info {
      display: flex;
      flex: 1;
      .info-img {
        width: 100px;
        height: 100px;
        object-fit: contain;
      }
      .info-content {
        padding-left: 20px;
        box-sizing: border-box;
        .name {
          font-size: 24px;
          font-family: "MicrosoftYaHei-Bold";
          font-weight: bold;
          line-height: 26px;
          margin-bottom: 4px;
          display: inline-block;
        }
        .info-item {
          margin-top: 10px;
          .label {
            font-size: 12px;
            font-family: "MicrosoftYaHei-Bold";
            font-weight: bold;
            line-height: 20px;
            white-space: nowrap;
            display: inline-block;
          }
          .value {
            font-size: 14px;
            line-height: 20px;
            display: inline-block;
          }
        }
      }
    }
    .statistics {
      padding: 0 10px;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .label {
        font-size: 12px;
        font-family: "MicrosoftYaHei-Bold";
        font-weight: bold;
        line-height: 18px;
        white-space: nowrap;
      }
      .num {
        font-family: "MicrosoftYaHei-Bold";
        font-size: 30px;
        font-weight: bold;
        line-height: 30px;
        margin-top: 8px;
      }
    }
  }
  .content {
    display: flex;
    flex-direction: column;
    .personnel-table {
      display: flex;
      flex: 1;
      .personnel-photo {
        width: 64px;
        height: 64px;
        object-fit: contain;
        cursor: pointer;
      }
    }
  }
}

.advanced-search-show {
  .advanced-search {
    max-height: 400px;
    transition: max-height 0.5s;
  }
  .advanced-search-text {
    /deep/img {
      transform: rotate(180deg);
      transition: transform 0.2s;
    }
  }
}

.search {
  position: relative;
}
.advanced-search-text {
  display: inline-block;
  margin-left: 10px;
  cursor: pointer;
  img {
    width: 16px;
    margin-top: 7px;
    float: left;
    margin-right: 3px;
  }
}

.advanced-search {
  display: flex;
  align-items: center;
  position: absolute;
  width: 100%;
  padding: 0 20px;
  box-sizing: border-box;
  margin-top: 1px;
  z-index: 10;
  max-height: 0px;
  transition: max-height 0.3s;
  overflow: hidden;
  .upload-input-list {
    display: flex;
    /deep/.ivu-upload {
      margin-right: 10px;
    }
  }
  .other-search {
    display: flex;
    flex: 1;
    padding: 10px 0 0 10px;
    box-sizing: border-box;
    flex-direction: initial;
    .other-search-top {
      display: flex;
      border-bottom: 1px dashed #fff;
    }
    .ivu-form-item {
      margin-bottom: 10px;
    }
    .other-search-bottom {
      width: 100%;
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      box-sizing: border-box;
      .slider-content {
        height: 34px;
      }
    }
  }
}
</style>
