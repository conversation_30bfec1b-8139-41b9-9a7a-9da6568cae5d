<template>
  <section class="content">
    <div class="content-left">
      <ui-card title="基本信息 " class="m-b20 newBaseInfo">
        <div class="info-content">
          <p>
            <span class="label">车辆品牌</span
            ><b class="half">{{ basicInfo.vehicleBrandCN }}</b>
            <span class="label">车辆颜色</span
            ><b>{{
              basicInfo.vehicleColor | commonFiltering(bodyColorList)
            }}</b>
          </p>
          <p>
            <span class="label">车牌颜色</span
            ><b class="half">{{
              basicInfo.plateColor | commonFiltering(plateColorList)
            }}</b>
            <span class="label">车辆类型</span>
            <b>{{
              basicInfo.vehicleType | commonFiltering(vehicleTypeList)
            }}</b>
          </p>
          <p>
            <span class="label">车辆型号</span
            ><b>{{ basicInfo.vehicleModel }}</b>
          </p>
          <p>
            <span class="label">登记日期</span
            ><b>{{ basicInfo.registerDate }}</b>
          </p>
          <p>
            <span class="label">登记地址</span
            ><b>{{ basicInfo.residenceAdress }}</b>
          </p>
        </div>
      </ui-card>
      <Tabs class="ui-tabs m-b20" type="card">
        <TabPane label="驾乘人员">
          <div class="card-swiper-container">
            <swiper class="card-swiper" :options="swiperOption">
              <swiper-slide v-for="(item, index) in driversList" :key="index">
                <div class="vehicle-container">
                  <div class="vehicle-info">
                    <img
                      class="vehicle-img"
                      :src="item.lastDrivingPic"
                      alt=""
                    />
                    <!-- <img class="vehicle-img" src="@/assets/img/demo/face/24.jpeg" alt="" /> -->
                    <span class="plate-number main-driver">{{
                      item.driverFlag == 1 ? "主驾" : "副驾"
                    }}</span>
                  </div>
                  <div class="info-content">
                    <p>
                      <span class="label">姓名</span><b>{{ item.perName }}</b>
                    </p>
                    <p>
                      <span class="label">身份证号</span
                      ><b class="color-primary">{{ item.idCardNo }}</b>
                    </p>
                    <p>
                      <span class="label">视频身份</span
                      ><b class="color-warning">{{ item.vid }}</b>
                    </p>
                    <p>
                      <span class="label">驾乘时间</span
                      ><b>{{ item.lastSnapTime }}</b>
                    </p>
                  </div>
                </div>
              </swiper-slide>
              <div class="swiper-pagination" slot="pagination"></div>
            </swiper>
          </div>
          <ui-empty v-if="driversList.length === 0" />
        </TabPane>
        <TabPane label="车主信息">
          <div class="owner-container">
            <ui-empty v-if="vehicleInfo.none" />
            <div class="owner-content" v-else>
              <img
                v-if="vehicleInfo.photos && vehicleInfo.photos.length > 0"
                class="owner-img"
                :src="vehicleInfo.photos[0].photoUrl"
                alt=""
              />
              <img
                v-else
                class="owner-img"
                src="@/assets/img/default-img/real_name_default.png"
                alt=""
              />
              <div class="info-content">
                <!-- <p><span class="label">驾乘方式</span><b>{{vehicleInfo.xm}}</b></p> -->
                <p>
                  <span class="label">车主姓名</span><b>{{ vehicleInfo.xm }}</b>
                </p>
                <p>
                  <span class="label">身份证号</span
                  ><b class="color-primary">{{ vehicleInfo.gmsfhm }}</b>
                </p>
                <p v-if="vehicleInfo.vids">
                  <span class="label">视频身份</span
                  ><b
                    class="color-warning ellipsis"
                    :title="vehicleInfo.vids.toString()"
                    >{{ vehicleInfo.vids.toString() }}</b
                  >
                </p>
                <p v-else>
                  <span class="label">视频身份</span
                  ><b class="color-warning ellipsis"></b>
                </p>
                <p>
                  <span class="label">联系电话</span
                  ><b>{{ vehicleInfo.lxdh }}</b>
                </p>
                <p>
                  <span class="label">居住地址</span
                  ><b>{{ vehicleInfo.xzzMlxxdz }}</b>
                </p>
              </div>
            </div>
          </div>
        </TabPane>
      </Tabs>
      <div class="ui-card m-b20">
        <div class="card-head">
          <p
            class="capture-title face-capture"
            :class="carType2 === 1 ? 'capture-active' : ''"
            @click="handleTab2(1)"
          >
            <span>最近抓拍</span>
          </p>
          <!-- <div class="capture-title car-capture" :class="carType2 === 2 ? 'capture-active' : ''" @click="handleTab2(2)">
            <span>最新报警</span>
          </div> -->
          <div slot="extra" class="play-btn mr-20 color-primary">
            <router-link
              v-if="carType2 === 1"
              target="_blank"
              :to="{
                name: 'cloud-default-page',
                query: {
                  componentName: 'advancedSearch',
                  sectionName: 'vehicleContent',
                  archiveNo: $route.query.plateNo,
                },
              }"
              >更多
            </router-link>
            <router-link
              v-else
              target="_blank"
              :to="{
                name: 'alarm-manager',
                query: { page: 'vehicle' },
              }"
              >更多
            </router-link>
          </div>
        </div>
        <div class="card-content" v-if="carType2 === 1">
          <snapRecord :list="snapList" type="vehicle"></snapRecord>
        </div>
        <div class="card-content alarm-content" v-else>
          <alarm-tab
            ref="alarmTab"
            :carType="2"
            :showCount="false"
            :plateNo="plateNo"
          ></alarm-tab>
        </div>
      </div>
    </div>
    <div class="content-middle">
      <!--标签图谱-->
      <label-cloud-view
        :labels="basicInfo.labels"
        :type="3"
        :info="basicInfo"
      />
      <!--底部swiper-->
      <middle-swiper :middleJson="middleJson" />
    </div>
    <div class="content-right">
      <!-- <ui-card title="关系统计" class="m-b20">
        <div slot="extra" class="play-btn mr-20 color-primary cursor-p" @click="toRelationGraph" v-if="!!MixinReativeStat[0].children.length">关系图谱</div>
        关系图谱
        <CollapseExpand
          :atlasList="MixinReativeStat"
          :styles="{
            0: { width: 60, height: 60, button: false },
            1: { width: 46, height: 46 }
          }"
          :options="options"
        />
      </ui-card> -->
      <ui-card title="最新报警" class="m-b20">
        <div slot="extra" class="play-btn mr-20 color-primary">
          <router-link
            target="_blank"
            :to="{
              name: 'alarm-manager',
              query: { page: 'vehicle' },
            }"
            >更多</router-link
          >
        </div>
        <div class="card-content alarm-content">
          <alarm-tab
            ref="alarmTab"
            :carType="2"
            :showCount="false"
            :plateNo="plateNo"
          ></alarm-tab>
        </div>
      </ui-card>
      <!--行为规律-->
      <law-behaviour
        v-if="plateNo"
        :dataType="3"
        :plateNo="plateNo"
      ></law-behaviour>
      <ui-card title="违法违章">
        <illegalSwiper
          v-if="ellegaList.length > 0"
          :dataList="ellegaList"
        ></illegalSwiper>
      </ui-card>
    </div>
  </section>
</template>
<script>
import { mapGetters, mapActions } from "vuex";
import {
  personBaseInfo,
  getVehicleBaseInfo,
  queryVehicleCaptureList,
  querySameOwner,
  activitiesNumStatics,
  queryRegularApproach,
  //   behavioralRulesStatics,
  behavioralRulesStaticsVehicle,
  queryVehicleCapture,
  queryVehicleRelatedCases,
  queryVehiclellegalList,
  queryDrivingPerson,
} from "@/api/vehicleArchives";
// import { behavioralRulesStatics } from '@/api/realNameFile'
import { swiper, swiperSlide } from "vue-awesome-swiper";
import snapRecord from "../../../components/snap-record";
import middleSwiper from "./components/middle-swiper";
import illegalSwiper from "./components/illegal-swiper";
import lawBehaviour from "../../../components/law-behaviour";
import labelCloudView from "../../../components/label-cloud-view/index";
import CollapseExpand from "@/components/relation-graph/collapse-expand";
import imgloading from "@/assets/img/car1.webp";
import { addViews } from "@/api/realNameFile";
import { vehiclePageList } from "@/api/target-control";
// import relativeGraphMixin from '@/views/holographic-archives/mixins/relativeGraphMixin.js'
import alarmTab from "@/views/perception-home/perception-site/components/alarm-tab.vue";
export default {
  //   mixins: [relativeGraphMixin],
  components: {
    swiper,
    swiperSlide,
    snapRecord,
    lawBehaviour,
    labelCloudView,
    middleSwiper,
    illegalSwiper,
    CollapseExpand,
    alarmTab,
  },
  props: {},
  data() {
    return {
      list: [1, 2, 3, 4],
      carType2: 1,
      info: {},
      labelList: [
        { id: "1", name: "重点人员", color: "#EA4A36" },
        { id: "2", name: "常住人口", color: "#48BAFF" },
        { id: "3", name: "涉毒人员", color: "#F29F4C" },
        { id: "4", name: "常住人口", color: "#1FAF8A" },
        { id: "11", name: "重点人员", color: "#EA4A36" },
        { id: "21", name: "常住人口", color: "#48BAFF" },
        { id: "31", name: "涉毒人员", color: "#F29F4C" },
        { id: "41", name: "常住人口1", color: "#1FAF8A" },
        { id: "12", name: "重点人员2", color: "#EA4A36" },
        { id: "22", name: "常住人口重点人员83", color: "#48BAFF" },
        { id: "32", name: "涉毒人员4", color: "#F29F4C" },
        { id: "42", name: "常住人口5", color: "#1FAF8A" },
        { id: "13", name: "重点人员6重点人员6", color: "#EA4A36" },
        { id: "33", name: "涉毒人员7", color: "#F29F4C" },
        { id: "18", name: "重点人员8", color: "#EA4A36" },
        { id: "72", name: "常住人口9", color: "#48BAFF" },
        { id: "63", name: "涉毒人员10", color: "#F29F4C" },
        { id: "45", name: "常住人口11", color: "#1FAF8A" },
        { id: "721", name: "常住人口9", color: "#48BAFF" },
        { id: "631", name: "涉毒人员110", color: "#F29F4C" },
        { id: "415", name: "常住人口111常住人口111", color: "#1FAF8A" },
      ],
      swiperOption: {
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
          renderBullet(index, className) {
            return `<span class="${className} swiper-pagination-bullet-custom">${
              index + 1
            }</span>`;
          },
        },
      },
      options: {
        disableDragNode: true,
        allowShowMiniToolBar: false,
        // disableZoom: true,
        defaultExpandHolderPosition: "hide",
        checkSelect: false,
        fontSize: 12,
        layouts: [
          {
            label: "中心",
            layoutName: "center",
            layoutClassName: "seeks-layout-center",
            // 节点间距
            distance_coefficient: 0.5,
          },
        ],
      },
      atlasList: [
        // {
        //   id: 1,
        //   text: '苏A 29999',
        //   img: imgloading,
        //   children: [
        //     // { id: 2, num: 2, text: '同抓拍' },
        //     // { id: 3, num: 2, text: '同涉案' },
        //     // { id: 4, num: 2, text: '驾乘人员' },
        //     // { id: 5, num: 2, text: '同车主' },
        //     // { id: 6, num: 2, text: '同车主' },
        //     // { id: 7, num: 2, text: '同车主' }
        //   ]
        // }
      ],
      timeSlotSeries: [
        {
          type: "bar",
          data: [],
          coordinateSystem: "polar",
          stack: "a",
          barCategoryGap: "0%",
        },
      ],
      timeSlotLoading: false,
      activeNumXAxis: {
        data: [1, 2, 3, 4, 5, 6, 7],
        axisLine: {
          lineStyle: {
            color: "#D3D7DE",
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: "rgba(0, 0, 0, 0.35)",
          // rotate: 40
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "dashed",
            color: "#D3D7DE",
          },
        },
      },
      activeNumSeries: [
        {
          name: "白天",
          type: "bar",
          stack: "one",
          data: [5, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#F29F4C",
          },
        },
        {
          name: "晚上",
          type: "bar",
          stack: "one",
          data: [1, 2, 3, 4, 5, 6, 7],
          barWidth: "30%",
          itemStyle: {
            color: "#2C86F8",
          },
        },
      ],
      activeNumLoading: false,
      snapList: [], // 抓拍记录
      basicInfo: {}, // 基本信息
      vehicleInfo: { none: true }, // 车主信息
      driversList: [], // 驾乘人员
      ellegaList: [], // 违法违章
      middleJson: {
        sameOwner: {}, // 同车主
        captureRecord: {}, // 抓拍记录
        oftenChannel: {}, // 经常途径
        relationCase: {}, // 关联案件
      },
      archiveNo: null,
      plateNo: null,
      idcardNo: null,
    };
  },
  computed: {
    ...mapGetters({
      // genderList: 'dictionary/getGenderList', //性别
      // nationList: 'dictionary/getNationList', //民族
      // vehicleClassTypeList: 'dictionary/getVehicleClassTypeList', //车牌类型
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      plateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      vehicleTypeList: "dictionary/getVehicleTypeList", //车辆类型
      // sbgnlxList: 'dictionary/getSbgnlxList' //摄像机功能类型
      relationList: "number-cube/getVehicleRelationList", //关系统计
    }),
  },
  watch: {
    // 'relationList': {
    //   deep: true,
    //   handler(val) {
    //     var list = []
    //     val.forEach((item, index) => {
    //       var obj = {
    //         id: index+2,
    //         num: item.count,
    //         text: item.nameCn
    //       }
    //       list.push(obj)
    //     })
    //     this.atlasList[0].children = list
    //     this.$refs.CollapseExpand.showSeeksGraph()
    //   }
    // }
  },
  mounted() {
    // this.relationStat({type: 'vehicle', searchKey: JSON.parse(this.$route.query.plateNo)})
    var list = [];
    this.relationList.forEach((item, index) => {
      var obj = {
        id: index + 2,
        num: item.count,
        text: item.nameCn,
      };
      list.push(obj);
    });
    // this.atlasList[0].img = this.baseInfo.photo
    // this.atlasList[0].children = list
    let query = this.$route.query;
    // 防止因为Anchor锚点导致的路由query参数丢失，跳档案都先跳到这个页面，先把参数存storage
    sessionStorage.setItem("query", JSON.stringify(query));
    var { archiveNo, plateNo, idcardNo } = query;
    this.archiveNo = JSON.parse(archiveNo);
    this.plateNo = JSON.parse(plateNo);
    this.idcardNo = idcardNo;
    this.init(JSON.parse(archiveNo), JSON.parse(plateNo));

    // 中间6块数据
    this.middleInfo();

    // 行为规律 2：近一周，3：近一月
    this.lawBehavior(3);

    // 浏览次数
    addViews({ module: this.$route.query.type }).then((res) => {});
  },
  methods: {
    // ...mapActions({
    //   relationStat: 'number-cube/relationStat',
    // }),
    toRelationGraph() {
      this.MixinToRelationGraph();
    },
    init(archiveNo, plateNo) {
      this.vehiclePageList();
      // 基本信息
      getVehicleBaseInfo(archiveNo).then((res) => {
        console.log("基本信息", res);
        this.basicInfo = res.data;
        // this.atlasList[0].img = res.data.photoUrl
      });

      if (this.idcardNo) {
        // 车主信息
        var param = {
          archiveNo: this.idcardNo,
          // archiveNo: '120101199203070253',
          dataType: 1,
        };
        personBaseInfo(param).then((res) => {
          console.log("车主信息", res);
          this.vehicleInfo = res.data || { none: true };
        });
      }

      // // 抓拍记录
      // // queryVehicleCaptureList('川KYN671').then(res =>{
      // queryVehicleCaptureList(plateNo).then(res =>{
      //   console.log('抓拍记录', res)
      //   this.snapList = res.data
      // })
      this.queryVehicleCaptureList();

      // 驾乘人员
      // queryDrivingPerson('川KSG711').then(res =>{
      queryDrivingPerson(plateNo).then((res) => {
        console.log("驾乘人员", res);
        this.driversList = res.data;
      });

      // 违法违章
      // queryDrivingPerson('川KSG711').then(res =>{
      queryVehiclellegalList(plateNo).then((res) => {
        console.log("违法违章", res);
        this.ellegaList = res.data;
      });
    },

    // 最近抓拍和最新报警切换
    handleTab2(val) {
      this.carType2 = val;
      if (val === 1) {
        // 抓拍记录
        this.queryVehicleCaptureList();
      } else {
        //最新报警
        this.vehiclePageList();
      }
    },
    // 抓拍记录
    queryVehicleCaptureList() {
      // queryVehicleCaptureList('川KYN671').then(res =>{
      queryVehicleCaptureList(this.plateNo).then((res) => {
        console.log("抓拍记录", res);
        this.snapList = res.data;
      });
    },
    async vehiclePageList() {
      var params = {
        plateNo: this.archiveNo,
        pageNumber: 1,
        pageSize: 5,
      };
      await vehiclePageList(params).then((res) => {
        this.snapList = res.data.entities;
        var row = this.snapList[0];
        row.total = res.data.total;
        this.middleJson.vehicleInfo = row;
        this.vehicleInfo = row;
        this.$forceUpdate();
        console.log("this.middleJson", this.middleJson);
      });
    },

    // 切换时间
    switchDayHandle(daysNum) {
      this.daysNum = daysNum;
    },
    /**
     * 中间6块数据请求
     */
    middleInfo() {
      // 同车主
      var idcardNo = "41110219880207969X";
      querySameOwner(this.idcardNo, this.plateNo).then((res) => {
        console.log("同车主", res.data);
        this.middleJson.sameOwner = res.data;
      });

      // 抓拍记录
      var plateNo = "川KSG711";
      queryVehicleCapture(this.plateNo).then((res) => {
        console.log("抓拍记录", res.data);
        this.middleJson.captureRecord = res.data;
      });

      // 经常途径
      var plateNo = "川KSG711";
      queryRegularApproach(this.plateNo).then((res) => {
        console.log("经常途径", res.data);
        this.middleJson.oftenChannel = res.data;
      });

      // 关联案件
      var plateNo = "川KSG711";
      queryVehicleRelatedCases(this.plateNo).then((res) => {
        console.log("关联案件", res.data);
        this.middleJson.relationCase = res.data;
      });
    },

    // 行为规律
    lawBehavior(val) {
      // 活动时间段
      this.timeSlotLoading = true;
      let params = {
        plateNo: this.plateNo,
        archiveNo: this.archivesId,
        // gmsfhm: '12345678952',
        type: val,
        // vid: ''
      };
      behavioralRulesStaticsVehicle(params)
        .then((res) => {
          console.log("行为规律", res.data);
          let daytimeRange = res.data.daytimeRange;
          // 后端返回白天、晚上开始时间格式为"白天开始时间-晚上开始时间"
          let dayStart = daytimeRange.split("-")[0];
          let dayEnd = daytimeRange.split("-")[1];
          let timeList = res.data.x;
          let dataList = res.data.y;
          dataList.forEach((v, i) => {
            // 当前时间》=白天开始时间且《晚上开始时间则为白天，否则为晚上
            if (timeList[i] >= dayStart && timeList[i] <= dayEnd) {
              this.timeSlotSeries[0].data.push({
                name: `白天-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#F29F4C",
                },
              });
            } else {
              this.timeSlotSeries[0].data.push({
                name: `晚上-${i + 1}点`,
                value: v,
                itemStyle: {
                  color: "#2C86F8",
                },
              });
            }
          });
        })
        .catch(() => {})
        .finally(() => {
          this.timeSlotLoading = false;
        });
      // 活动次数
      this.activeNumLoading = true;
      activitiesNumStatics({
        plateNo: this.plateNo,
        archiveNo: this.archivesId,
        gmsfhm: "12345678952",
        type: val,
        vid: "",
      })
        .then((res) => {
          if (val === 2) {
            this.activeNumXAxis.axisLabel.rotate = 0;
          } else {
            this.activeNumXAxis.axisLabel.rotate = 40;
          }
          this.activeNumXAxis.data = res.data.x;
          this.activeNumSeries[0].data = res.data.day;
          this.activeNumSeries[1].data = res.data.night;
        })
        .catch(() => {})
        .finally(() => {
          this.activeNumLoading = false;
        });
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  width: 100%;
  display: flex;
  height: 100%;
  overflow: auto;
  min-height: 760px;
  padding: 16px 20px 20px;
  box-sizing: border-box;
}
.content-left,
.content-right {
  width: 500px;
  display: flex;
  flex-direction: column;
  height: 100%;
  .ui-card,
  .ui-tabs {
    flex: 1;
  }
  /deep/ .card-content {
    height: calc(~"100% - 30px");
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  /deep/ .ivu-tabs-content {
    height: calc(~"100% - 30px");
  }
  .newBaseInfo {
    /deep/ .card-content {
      justify-content: start;
    }
  }
}

.info-content {
  p {
    display: flex;
    font-size: 14px;
    padding: 1% 0;
    color: rgba(0, 0, 0, 0.9);
    .label {
      text-align-last: justify;
      text-align: justify;
      text-justify: distribute-all-lines; // 这行必加，兼容ie浏览器
      width: 56px;
      font-weight: bold;
      white-space: nowrap;
      color: #2c86f8;
      margin-right: 15px;
    }
    b {
      font-weight: normal;
      word-break: break-all;
      &.half {
        width: 110px;
      }
      &.weight {
        font-size: 20px;
        line-height: 1;
      }
    }
  }
}
.card-swiper-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .card-swiper {
    padding-bottom: 30px;
    .swiper-pagination {
      text-align: left;
      display: flex;
      bottom: 0;
    }
    /deep/ .swiper-pagination-bullet-custom {
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      font-size: 12px;
      border-radius: 2px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #2c86f8;
      background: #fff;
      color: #2c86f8;
      opacity: 1;
      margin: 0;
      margin-right: 5px;
      &.swiper-pagination-bullet-active {
        color: #fff;
        background: #2c86f8;
      }
    }
  }
}
/deep/ .vehicle-container {
  display: flex;
  align-items: center;
  height: 180px;
  .vehicle-info {
    display: flex;
    flex-direction: column;
    margin-right: 20px;
    height: 100%;
    position: relative;
    .vehicle-img {
      width: 100%;
      height: 100%;
      // width: 180px;
      // height: 180px;
    }
    .plate-number {
      position: absolute;
      padding: 2px 8px;
      color: #fff;
      text-align: center;
    }
    .main-driver {
      background: #e99e53;
    }
    .co-driver {
      background: #2c86f8;
    }
  }
  .info-content {
    padding-left: 20px;
    height: 100%;
    border-left: 1px dashed #d3d7de;
  }
}
.owner-container {
  display: flex;
  align-items: center;
  height: 100%;
  position: relative;
  .owner-content {
    box-sizing: border-box;
    display: flex;
    height: 100%;
    align-items: center;
    padding: 20px 0 30px;
    .owner-img {
      width: 180px;
      max-height: 100%;
      margin-right: 20px;
    }
    .info-content {
      padding-left: 20px;
      border-left: 1px dashed #d3d7de;
    }
  }
}

@media screen and (max-height: 960px) {
  .vehicle-container {
    height: 160px;
    .vehicle-info {
      .vehicle-img {
        width: 180px;
      }
    }
  }
  .owner-container {
    .owner-content {
      .owner-img {
        width: 150px;
        height: auto;
        margin-right: 20px;
      }
    }
  }
}
.content-middle {
  overflow: hidden;
  flex: 1;
  display: flex;
  width: 100%;
  flex-direction: column;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 190px;
}
.card-swiper-container {
  /deep/ .swiper-slide {
    width: 460px !important;
  }
}
.capture-title {
  font-size: 16px;
  cursor: pointer;
  line-height: 30px;
  text-align: center;
  background: #d3d7de;
  color: #666;
  transform: skewX(-18deg);
  padding: 0 23px;
  left: -6px;
  span {
    transform: skewX(18deg);
    display: inline-block;
  }
}
.face-capture {
  position: relative;
}
.car-capture {
  position: absolute;
  left: 120px;
}
.capture-active {
  background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
  color: #fff;
  font-weight: bold !important;
}
.card-content {
  padding: 20px;
}
.alarm-content {
  padding: 20px 0;
}
</style>
