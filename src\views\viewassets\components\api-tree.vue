<template>
  <div class="inline api-organization-tree" v-clickoutside="dropHide">
    <!-- <span v-if="!!selectTree.title" class="f-12">{{ selectTree.title }}</span> -->
    <Dropdown trigger="custom" :visible="visible">
      <div
        class="ivu-select ivu-select-single select-width t-left width-md"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
      >
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!treeText">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeText">{{ treeText }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="isClose" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <el-tree
          class="tree"
          node-key="id"
          multiple
          :style="treeStyle"
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultKeys"
          @node-click="handleNodeClick"
          @current-change="currentChange"
        >
          <span class="custom-tree-node" slot-scope="{ node }">
            <span :class="{ 'nav-not-selected': node.disabled, allowed: node.disabled }">{{ node.label }}</span>
          </span>
        </el-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<style lang="less" scoped>
.select-width {
  width: 150px;
}
.tree {
  min-width: 150px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
  margin-right: 10px;
}

/deep/ .width-md,
.width-sm {
  width: 150px !important;
}
</style>
<script>
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      choosedOrg: {},
      treeText: '',
      visible: false,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      orgCode: '',
      defaultKeys: [],
      isClose: false, //清空按钮是否显示
    };
  },
  created() {},
  mounted() {},
  methods: {
    mouseenter() {
      if (this.treeText) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    // 节点选中触发
    handleNodeClick(data) {
      this.choosedOrg = data;
      this.treeText = data.orgName;
      this.orgCode = data.orgCode;
      this.$emit('change', data);
    },
    setTreeText() {},
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      this.visible = !this.visible;
    },
    currentChange(data) {
      if (data.disabled) {
        return false;
      }
      this.visible = false;
    },
    clear() {
      this.isClose = false;
      this.treeText = '';
      this.orgCode = '';
    },
  },
  watch: {
    orgCode(val) {
      this.$emit('input', val);
    },
    value() {
      if (!this.value) {
        this.orgCode = '';
        this.treeText = '';
      }
    },
  },
  computed: {
    ...mapGetters({
      defaultTreeData: 'common/getOrganizationList',
    }),
  },
  props: {
    value: {},
    treeData: {
      type: Array,
      default() {
        return this.defaultTreeData;
      },
    },
    treeStyle: {},
    placeholder: {
      type: String,
      default: () => {
        return '请选择组织机构';
      },
    },
  },
  components: {},
};
</script>
