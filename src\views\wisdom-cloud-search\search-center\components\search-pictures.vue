<template>
  <section class="search-pictures">
    <div class="header" :class="{'header-fight': fight}">
      <h3 class="title">以图搜图</h3>
      <RadioGroup v-model="algorithmType" class="radio-group">
        <Radio :label="'1'">人脸</Radio>
        <Radio :label="'2'">车辆</Radio>
        <Radio :label="'3'">人体</Radio>
        <Radio :label="'4'">非机动车</Radio>
      </RadioGroup>
      <Icon type="ios-close" @click="cancelHandle" />
    </div>
    <div class="body">
      <ui-upload-img ref="uploadImg" :algorithmType="algorithmType" :value="urlList" :size="size" @imgUrlChange="imgUrlChange" @deleteImgUrl='deleteImgUrl'></ui-upload-img>
    </div>
    <div class="footer">
      <div class="slider-content">
        <span class="similarity">相似度:</span>
        <Slider v-model="similarity"></Slider>
        <span>{{ similarity }}%</span>
      </div>
      <div class="btn-group">
        <Button type="default" @click="resetHandle">清空</Button>
        <Button type="primary" @click="confirmHandle">搜索</Button>
      </div>
    </div>
  </section>
</template>
<script>
import { mapActions, mapGetters } from 'vuex'
import uiUploadImg from '@/components/ui-upload-img/index'
import { number } from 'echarts'

export default {
  name: 'SearchPictures',
  components: { uiUploadImg },
  props: {
    // 关闭
    close: {
      type: Function,
      default: () => {}
    },
    size: {
      type: String,
      default: ''
    },
    // 标题旁样式调整
    fight: {
        type: Boolean,
        default: false
    },
    picData: {
      type: Object,
      default: () => {
        return {
          similarity: 60,
          algorithmType: '1',
          urlList: ['', '', '', '', '']
        }
      }
    }
  },
  data() {
    return {
      similarity: 60,
      algorithmType: '',
      dataCopper: [],
      urlList: [],
      tempUrl: '',
      imageRecognitionVisible: false,
      selectedUploadIndex: null //选中的uploadIndex
    }
  },
  watch: {
    picData: {
      handler(val) {
        // 赋值
        this.similarity = (val && val.similarity) ? Number(val.similarity) : this.globalObj.searchForPicturesDefaultSimilarity
        this.algorithmType = val.algorithmType || '1'
        this.urlList = val.urlList || ['', '', '', '', '']
      },
      deep: true,
      immediate: true
    },
    algorithmType(newVal, oldVal) {
      this.urlList = []
      this.$refs.uploadImg.urlList = ['', '', '', '', '']
      // this.$emit('imgUrlChange', this.urlList)
      this.$emit('clearPreview', this.urlList)
    }
  },
  computed: {
    ...mapGetters({
      globalObj: 'systemParam/globalObj'
    })
  },
  async mounted() {
    await this.getSystemAllData()
    const { searchForPicturesDefaultSimilarity } = this.globalObj
    this.similarity = Number(searchForPicturesDefaultSimilarity)
  },
  methods: {
    ...mapActions({
      getSystemAllData: 'systemParam/getSystemAllData'
    }),
    confirmHandle() {
      const { similarity, algorithmType, urlList } = this
      const params = {
        similarity,
        algorithmType,
        urlList
      }
      this.$emit('search', params)
    },
    cancelHandle() {
      this.$emit('cancel')
    },
    // 清空图片
    resetHandle() {
      this.urlList = ['', '', '', '', '']
      this.$emit('clearUrl')
    },
    //删除单个图片
    minImgDeleteHandle(index) {
      this.$set(this.urlList, index, '')
      this.$forceUpdate()
      this.imgUrlChange()
    },
    // 获取
    getMinImgUrl(url) {
      this.$set(this.urlList, this.selectedUploadIndex, url)
      this.$forceUpdate()
      this.imgUrlChange()
    },
    imgUrlChange(list) {
      this.urlList = list
      this.$emit('imgUrlChange', this.urlList)
    },
    deleteImgUrl(list) {
        this.urlList = list
        this.$emit('deleteImgUrl', this.urlList)
    }
  }
}
</script>
<style lang="less" scoped>
.search-pictures {
  width: 820px;
  padding-bottom: 10px;
  .header {
    color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    padding-bottom: 11px;
    padding-left: 20px;
    padding-right: 5px;
    border-bottom: 1px solid #d3d7de;
    padding-top: 5px;
    position: relative;
    .title {
      font: 600 18px MicrosoftYaHei-Bold, MicrosoftYaHei;
      margin-right: 80px;
      color: rgba(0, 0, 0, 0.9);
    }
    .radio-group {
      flex: 1;
      .ivu-radio-group-item {
        margin-right: 40px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
    .ivu-icon-ios-close {
      font-size: 32px;
      color: #888;
      position: absolute;
      top: 50%;
      margin-top: -18px;
      right: 5px;
      &:hover {
        color: #666;
      }
    }
  }
    .header-fight{
        .title{
            margin-right: 10px;
        }
        .radio-group {
            flex: 1;
            .ivu-radio-group-item {
                margin-right: 10px;
                &:last-child {
                    margin-right: 0;
                }
                /deep/.ivu-radio{
                    margin-right: 5px;
                }
            }
        }
    }
  .body {
    padding: 20px;
  }
  .footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 15px 20px;
    .similarity {
      color: rgba(0, 0, 0, 0.45);
    }
  }
}
</style>
