<template>
  <div class="topright-buttons-box flex-aic">
    <Checkbox v-model="isMarkPointOfOutside" size="small" @on-change="changeCheckbox">
      <span :class="['f-14', isMarkPointOfOutside ? 'font-blue' : '']">仅标记越界点位</span>
    </Checkbox>
    <Divider type="vertical" class="bg-blue" />
    <map-boundary-popover :treeData="areaList" v-on="$listeners"></map-boundary-popover>
    <Divider type="vertical" class="bg-blue" />
    <layer-management :allCameraList="layerAllCameraList" @handleDevLayer="handleDevLayer"></layer-management>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
  props: {
    allCameraList: {
      type: Array,
      default: () => [],
    },
    cameraList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      isMarkPointOfOutside: false, //是否仅标记越界点位
      preRenderCameraList: [], //复制展示的列表，用于在此基础上进行过滤
    };
  },
  name: '',
  components: {
    LayerManagement: require('./layer-management.vue').default,
    MapBoundaryPopover: require('./map-boundary-popover.vue').default,
  },
  computed: {
    ...mapGetters({
      areaList: 'common/getAreaList',
    }),
    layerAllCameraList() {
      //计算选中标记越界点位时传入图层管理的对象
      return this.isMarkPointOfOutside ? this.returnFilterPointOfOutside(this.allCameraList) : this.allCameraList;
    },
  },
  watch: {
    allCameraList: {
      handler(val) {
        this.preRenderCameraList = this.$util.common.deepCopy(val);
        this.isMarkPointOfOutside = false;
      },
    },
  },
  methods: {
    ...mapActions({
      setAreaList: 'common/setAreaList',
    }),
    handleDevLayer(val) {
      this.preRenderCameraList = val;
      let layerArr = this.returnFilterPointOfOutside(val);
      this.$emit('handleDevLayer', layerArr);
    },
    //仅标记越界点位
    changeCheckbox(val) {
      this.isMarkPointOfOutside = val;
      let layerArr = this.returnFilterPointOfOutside(this.preRenderCameraList);
      this.$emit('handleDevLayer', layerArr);
    },
    //过滤出异常信息为越界的点位
    returnFilterPointOfOutside(fliterArr) {
      //需要在现有的数组上过滤，传入arr
      let nowCameraList = fliterArr;
      let returnArr = [];
      if (this.isMarkPointOfOutside) {
        returnArr = nowCameraList.filter((item) => {
          return item.ruleCodes && item.ruleCodes.includes('1008#longitudeLatitude');
        });
      } else {
        returnArr = nowCameraList;
      }
      return returnArr;
    },
  },
  async mounted() {
    await this.setAreaList();
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .topright-buttons-box {
    background: #022143;
    border: 1px solid #174f98;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .topright-buttons-box {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
  }
}

.topright-buttons-box {
  position: absolute;
  top: 30px;
  right: 30px !important;
  z-index: 1000;
  padding: 4px 16px;
  .bg-blue {
    background-color: var(--devider-line);
  }
  @{_deep} .ivu-divider,
  .ivu-divider-vertical {
    margin: 0 16px;
  }

  @{_deep} .ivu-checkbox-wrapper {
    margin-right: 0;
    > .ivu-checkbox {
      margin-right: 5px;
    }
  }
}
</style>
