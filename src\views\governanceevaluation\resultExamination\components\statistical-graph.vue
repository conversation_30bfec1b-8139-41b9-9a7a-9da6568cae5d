<template>
  <div class="statistical-main" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <div class="evaluate-tendency">
      <!-- 卡片统计 -->
      <div class="statistic-list">
        <ul>
          <li
            v-for="(item, index) in statisticList"
            :key="index"
            :class="[item.listBg]"
            :style="{
              'padding-left': statisticList.length > 3 ? '30px' : '80px',
            }"
          >
            <div class="fl-st">
              <p class="icon-font icon-bottom icon-kaohedefen"></p>
              <div>
                <p class="card-name" :title="item.name">{{ item.name }}</p>
                <Tooltip class="tooltip-show" width="280" v-if="item.children.length > 0">
                  <span class="wenhao-icon-bg">
                    <i class="icon-font icon-wenhao vt-middle icon-warning pointer"></i>
                  </span>

                  <div slot="content">
                    <p class="mb-sm tooltip-content">
                      <span>{{ item.name }}=</span>
                      <span v-for="(it, indexs) in item.children" :key="indexs"
                        ><span v-if="indexs !== 0">+</span> {{ it.name }}
                      </span>
                    </p>
                    <p class="mb-sm tooltip-content">
                      <span class="score-show">{{ item.score }}</span
                      ><label class="ml-xs mr-xs">=</label>
                      <span v-for="(it, indexs) in item.children" :key="indexs"
                        ><label class="ml-xs mr-xs" v-if="indexs !== 0">+</label>
                        <span class="score-show">{{ it.score }}</span>
                      </span>
                    </p>
                  </div>
                </Tooltip>
                <p class="statistic-num">
                  {{ item.score }}
                </p>
              </div>
            </div>
            <span class="right-img"></span>
          </li>
        </ul>
      </div>
      <!-- 地市柱状图统计 -->
      <div class="mt-lg cl grap-content">
        <p><i class="icon-font icon-xiajikaohedefen score-icon"></i><span class="ml-sm fs-16">下级考核得分</span></p>
        <div class="echarts-box">
          <draw-echarts
            v-if="echartList.length"
            :echart-option="taskEchart"
            :echart-style="ringStyle"
            ref="taskChart"
            class="charts"
            :echarts-loading="echartsLoading"
          ></draw-echarts>
          <span class="next-echart" v-if="curtail(echartList).length > comprehensiveConfig.comprehensiveNum">
            <i
              class="icon-font icon-youjiantou1 f-12"
              @click="scrollRight('taskChart', echartList, [], comprehensiveConfig.comprehensiveNum)"
            ></i>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
import Vue from 'vue';
import taskTooltip from '@/views/governanceevaluation/resultExamination/components/task-tooltip';
export default {
  name: 'statisticalGraph',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    echartList: {
      type: Array,
      default: () => [],
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      statisticList: [],
      num: 0,
      lengName: [],
      taskEchart: {},
      colorList: [
        'icon-bg1',
        'icon-bg2',
        'icon-bg3',
        'icon-bg4',
        'icon-bg5',
        'icon-bg6',
        'icon-bg7',
        'icon-bg8',
        'icon-bg9',
        'icon-bg10',
      ],
      tooltipFormatter: (data) => {
        data.map((item) => {
          this.taskList.forEach((i) => {
            i.children.forEach((ite) => {
              if (item.axisValue == ite.regionName && item.seriesName === ite.name) {
                item.taskList = ite.children;
              }
            });
          });
        });
        let taskTooltipShow = Vue.extend(taskTooltip);
        let _this = new taskTooltipShow({
          el: document.createElement('div'),
          data() {
            return {
              data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      taskList: [],
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    echartList: {
      handler(val) {
        if (val.length > 0) {
          // 一级卡片统计
          if (val[0].children) {
            let statistic = val[0].children.map((i, index) => {
              return {
                name: i.name,
                score: i.score ? i.score : '0',
                listBg: this.colorList[index],
                children: i.children,
              };
            });
            this.statisticList = statistic;
            this.initRing(val);
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 过滤数据
    curtail(arr) {
      return arr.slice(1);
    },
    // 柱状统计图
    initRing(val) {
      var list = this.curtail(val);
      let yAxis = {};
      this.taskList = [];
      if (list.length > 0) {
        list.map((item) => {
          this.lengName.push(item.regionName);
          this.taskList.push({ children: item.children });
          if (item.children.length > 0) {
            item.children.map((i) => {
              if (!yAxis[i.name]) {
                yAxis[i.name] = [i.score];
              } else {
                yAxis[i.name].push(i.score);
              }
            });
          }
        });
      }
      let series = [];
      for (let key in yAxis) {
        series.push({
          name: key,
          type: 'bar',
          data: yAxis[key],
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barWidth: '30px',
          showSymbol: false,
          label: {
            show: false,
            position: 'top',
          },
        });
        this.num++;
      }
      let opts = {
        xAxis: this.lengName,
        data: series,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.taskEchart = this.$util.doEcharts.taskResultMade(opts);
      setTimeout(() => {
        this.setDataZoom('taskChart', [], this.comprehensiveConfig.comprehensiveNum);
      });
    },
  },
};
</script>
<style lang="less" scoped>
.statistical-main {
  width: 100%;
  height: 100%;
  .evaluate-tendency {
    height: 100%;
    color: #fff;
    .statistic-list {
      width: 100%;
      height: 26%;
      ul {
        width: 100%;
        overflow: auto;
        display: flex;
        justify-content: flex-start;
        align-content: center;
        padding: 20px;
        background-color: var(--bg-sub-content);
        li {
          position: relative;
          padding-top: 16px;
          padding-bottom: 16px;
          margin-right: 10px;
          border-radius: 10px;
          flex: 1;
          min-width: 340px;
          max-width: 865px;
          &:nth-last-child(1) {
            margin-right: 0;
          }
          .card-name {
            position: relative;
            max-width: 130px;
            height: 25px;
            line-height: 25px;
            font-size: 14px;
            font-weight: bold;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            z-index: 6;
          }
          .wenhao-icon-bg {
            display: inline-block;
            width: 16px;
            height: 16px;
            line-height: 16px;
            border-radius: 50%;
            background-color: #fff;
            vertical-align: middle;
            i {
              width: 16px;
              height: 16px;
            }
          }
          .tooltip-content {
            width: 100%;
          }
          .tooltip-show {
            position: absolute;
            z-index: 7;
            top: 16%;
            margin-left: 10px;
          }
          .statistic-num {
            color: #fff;
            margin-bottom: 10px;
            font-size: 45px;
            height: 60px;
            line-height: 60px;
          }
          .score-show {
            padding: 2px 15px;
            background: #02162b;
            border: 1px solid #10457e;
            border-radius: 2px;
          }
          .icon-bottom {
            display: inline-block;
            color: #fff;
            width: 109px;
            height: 109px;
            font-size: 45px;
            line-height: 115px;
            text-align: center;
            background: rgba(218, 245, 248, 0.2);
            border-radius: 50%;
            margin-right: 27px;
          }
          .right-img {
            z-index: 1;
            position: absolute;
            right: 0;
            bottom: 0;
            width: 150px;
            height: 100%;
            background: url('~@/assets/img/taskstatistical/task-img.png') no-repeat;
          }
        }
      }
    }
    /deep/.ivu-tooltip-inner {
      overflow: auto;
    }
    .fl-st {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .fs-16 {
      font-size: 16px;
      font-weight: bold;
    }
    .score-icon {
      font-size: 18px;
      background: var(--color-primary);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .grap-content {
      height: 72%;
      width: 100%;
      .echarts-box {
        position: relative;
        display: flex;
        width: 100%;
        height: 94%;
        .charts {
          height: 100%;
          width: 100%;
          display: inline-block;
          flex: 1;
        }
      }
      .next-echart {
        width: 22px;
        height: 22px;
        background: #02162b;
        border: 1px solid #10457e;
        opacity: 1;
        border-radius: 4px;
        top: 50%;
        right: 8px;
        position: absolute;
        text-align: center;
        line-height: 22px;
        transform: translate(0, -50%);
        .icon-youjiantou1 {
          color: var(--color-primary);
          font-size: 12px;
          vertical-align: top !important;
        }
        &:active {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid var(--color-primary);
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: #4e9ef2;
            font-size: 12px;
            vertical-align: top !important;
          }
        }
        &:hover {
          width: 22px;
          height: 22px;
          background: #02162b;
          border: 1px solid #146ac7;
          opacity: 1;
          border-radius: 4px;
          .icon-youjiantou1 {
            color: var(--color-primary);
            font-size: 12px;
            vertical-align: top !important;
          }
        }
      }
    }
  }
  .icon-bg1 {
    background: linear-gradient(92deg, #007bd9 0%, #0ca7dc 100%);
    opacity: 1;
  }
  .icon-bg2 {
    background: linear-gradient(92deg, #5f63cc 0%, #373dd1 100%);
    opacity: 1;
  }
  .icon-bg3 {
    background: linear-gradient(92deg, #185cea 0%, #1548b4 100%);
    opacity: 1;
  }
  .icon-bg4 {
    background: linear-gradient(92deg, #10b599 0%, #108faf 100%);
    opacity: 1;
  }
  .icon-bg5 {
    background: linear-gradient(180deg, #af8748 0%, #8f5d0e 100%);
    opacity: 1;
  }
  .icon-bg6 {
    background: linear-gradient(92deg, #e8396f 0%, #de4c52 100%);
    opacity: 1;
  }
  .icon-bg7 {
    background: linear-gradient(92deg, #a1bb12 0%, #569505 100%);
    opacity: 1;
  }
  .icon-bg8 {
    background: linear-gradient(92deg, #c911d6 0%, #7a15b4 100%);
    opacity: 1;
  }
  .icon-bg9 {
    background: linear-gradient(92deg, #07a22c 0%, #0d7d33 100%);
    opacity: 1;
  }
  .icon-bg10 {
    background: linear-gradient(92deg, #c45b8c 0%, #9d38a1 100%);
    opacity: 1;
  }
}
</style>
