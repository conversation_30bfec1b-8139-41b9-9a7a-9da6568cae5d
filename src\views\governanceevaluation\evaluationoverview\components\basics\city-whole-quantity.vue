<template>
  <!-- 全量目录完整率  市-->
  <div class="whole-quantity" ref="contentScroll">
    <div class="information-header">
      <div class="information-echart">
        <div class="should-echart ml-100">
          <draw-echarts
            :echart-option="municipalchart"
            :echart-style="shouldStyle"
            ref="municipalChart"
            class="municipal-chart"
          ></draw-echarts>
        </div>
        <div class="differentiate ml-40">
          <div class="differentiate-box">
            <div class="differentiate-top">已上报区县</div>
            <div class="differentiate-bottom">应上报区县</div>
          </div>
        </div>
        <div class="ride pl-100 pr-100"><span>×</span></div>
        <div class="should-echart">
          <draw-echarts
            :echart-option="countEchart"
            :echart-style="shouldStyle"
            ref="countyChart"
            class="county-chart"
          ></draw-echarts>
        </div>
        <div class="differentiate ml-40">
          <div class="differentiate-box">
            <div class="differentiate-top">已上报派出所</div>
            <div class="differentiate-bottom">应上报派出所</div>
          </div>
        </div>
        <div class="ride pl-100 pr-100"><span>=</span></div>
        <div class="filing-rate ml-md">
          <i class="icon-font f-50 icon-quanliangmuluwanzhengshuai filing-icon"></i>
          <div class="filing-content">
            <div class="superior">全量目录完整率</div>
            <div class="same-level">{{ statisticalList.bfdResultValue }}</div>
          </div>
        </div>
      </div>
      <div class="information-ranking">
        <div class="rank" v-if="rankData.length != 0">
          <div class="ranking-title">
            <title-content title="下级排行"></title-content>
          </div>
          <div class="ranking-list">
            <ul>
              <li v-for="(item, index) in rankData" :key="index">
                <div class="content-firstly">
                  <span class="bg_color firstly1" v-if="item.rank == 1">{{ item.rank }}</span>
                  <span class="bg_color firstly2" v-if="item.rank == 2">{{ item.rank }}</span>
                  <span class="bg_color firstly3" v-if="item.rank == 3">{{ item.rank }}</span>
                  <span class="bg_color firstly4" v-if="item.rank != 1 && item.rank != 2 && item.rank != 3">{{
                    item.rank
                  }}</span>
                </div>
                <Tooltip class="content-second" transfer :content="item.regionName">
                  <div>
                    <img class=" " v-if="item.rank == 1" src="@/assets/img/crown_1.png" alt="" />
                    <img class=" " v-if="item.rank == 2" src="@/assets/img/crown_2.png" alt="" />
                    <img class=" " v-if="item.rank == 3" src="@/assets/img/crown_3.png" alt="" />
                    <!-- <span>{{ item.regionName }}</span> -->
                    <span v-if="item.rank == 1">{{ item.regionName }}</span>
                    <span v-if="item.rank == 2">{{ item.regionName }}</span>
                    <span v-if="item.rank == 3">{{ item.regionName }}</span>
                    <span class="rankText" v-if="item.rank != 3 && item.rank != 2 && item.rank != 1">{{
                      item.regionName
                    }}</span>
                  </div>
                </Tooltip>
                <div class="content-thirdly">
                  <span class="thirdly">{{ item.standardsValue || 0 }}%</span>
                </div>

                <div class="content-fourthly">
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                  <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                  <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                  <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                  <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{
                    item.rankRise || 0
                  }}</span>
                  <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="no-data" v-if="rankData.length == 0">
          <i class="no-data-img icon-font icon-zanwushuju1"></i>
          <div class="null-data-text">暂无数据</div>
        </div>
      </div>
    </div>
    <div class="information-main navBarWrap">
      <div class="abnormal-title">
        <div class="btns fl">
          <Button
            class="btn_jk"
            :class="{ active: modelTag === 'BASIC_FULL_DIR_REPORT' }"
            @click="changeBtn('BASIC_FULL_DIR_REPORT')"
          >
            上报情况统计
          </Button>
          <Button
            class="btn_kk"
            :class="{ active: modelTag === 'BASIC_FULL_DIR_NOT' }"
            @click="changeBtn('BASIC_FULL_DIR_NOT')"
          >
            未上报目录
          </Button>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <!-- <div
        class="list auto-fill"
        ref="basicFullDirReport"
        v-show="modelTag == 'BASIC_FULL_DIR_REPORT'"
        v-ui-loading="{ loading: loading, tableData: tableData }"
      >

      </div> -->
      <ui-table
        :maxHeight="contentClientHeight"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        v-show="modelTag == 'BASIC_FULL_DIR_REPORT'"
      >
        <template #isReport="{ row }">
          <span v-if="row.isReport === false" class="font-red">否</span>
          <span v-if="row.isReport === true" class="font-green">是</span>
        </template>
      </ui-table>
      <div
        class="list auto-fill"
        ref="basicFullDirNot"
        v-ui-loading="{ loading: loading, tableData: treeData }"
        v-show="modelTag == 'BASIC_FULL_DIR_NOT'"
      >
        <ui-search-tree
          v-if="treeData.length !== 0"
          class="auto-fill"
          node-key="id"
          :no-search="false"
          :max-height="488"
          :tree-data="treeData"
          :default-props="defaultProps"
        >
        </ui-search-tree>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.whole-quantity {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  .navBarWrap {
    position: sticky !important;
    top: 100px;
    width: 100%;
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 518px) !important;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;

    .information-echart {
      display: inline-block;
      width: calc(100% - 350px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      .should-echart {
        width: 190px;
        vertical-align: middle;
        display: inline-block;
      }
      .differentiate {
        display: inline-block;
        height: 100%;
        width: 88px;
        vertical-align: middle;
        text-align: center;
        .differentiate-box {
          margin-top: 60px;
          .differentiate-top {
            height: 44px;
            line-height: 44px;
            width: 100%;
            border-bottom: 1px solid #2b84e2;
            color: #fff;
          }
          .differentiate-bottom {
            height: 44px;
            line-height: 44px;
            width: 100%;
            color: #fff;
          }
        }
      }
      .ride {
        display: inline-block;
        height: 100%;
        vertical-align: middle;
        position: relative;

        span {
          font-size: 50px;
          color: var(--color-primary);
          position: absolute;
          top: 65px;
          left: 88px;
        }
      }
      .pl-70 {
        padding-left: 70px;
      }
      .pr-70 {
        padding-right: 70px;
      }
      .pl-100 {
        padding-left: 90px;
      }
      .pr-100 {
        padding-right: 90px;
      }

      .pr-40 {
        padding-right: 40px;
      }
      .pl-40 {
        padding-left: 40px;
      }
      .ml-100 {
        margin-left: 70px;
      }
      .mr-100 {
        margin-right: 70px;
      }
      .mr-40 {
        margin-right: 40px;
      }
      .ml-40 {
        margin-left: 40px;
      }
      .filing-rate {
        vertical-align: middle;
        width: 280px;
        height: 112px;
        line-height: 112px;
        background: #05316b;
        display: inline-block;
        justify-content: center;
        align-items: center;

        .filing-icon {
          display: inline-block;
          height: 50px;
          width: 50px;
          font-size: 40px;
          line-height: 50px;
          text-align: center;
          color: #19c176;
          vertical-align: middle;
          margin-left: 42px;
        }
        .filing-content {
          display: inline-block;
          height: 50px;
          flex: 1;
          margin-left: 7px;
          text-align: left;
          vertical-align: middle;
          .superior {
            font-size: 14px;
            height: 25px;
            line-height: 25px;
            color: #f5f5f5;
          }
          .same-level {
            color: #19c176;
            height: 25px;
            line-height: 25px;
            font-size: 18px;
            font-family: Microsoft YaHei;
            font-weight: bold;
          }
        }
      }
    }
    .information-ranking {
      width: 350px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      position: relative;
      .rank {
        width: 100%;
        height: 100%;
      }
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                // width: calc(100% - 80px);
                width: 75px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .information-main {
    //height: calc(100% - 252px);
    .abnormal-title {
      height: 48px;
      line-height: 48px;
      .btns {
        .btn_jk {
          border-right: none !important;
        }

        button {
          color: #56789c;
          background-color: #12294e;
          border-radius: 0;
          &:hover {
            color: #fff !important;
            background-color: rgba(43, 132, 226, 1);
          }
        }
        .active {
          color: #fff;
          background-color: rgba(43, 132, 226, 1);
        }
      }
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    @{_deep}.ivu-table-wrapper {
      height: 100% !important;
    }
    @{_deep} .ivu-table-body {
      height: 100% !important;
    }
    @{_deep} .ui-table {
      position: relative;
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
    .list {
      background: var(--bg-sub-content);
      overflow-y: auto;
      height: 100%;
      // @{_deep}.ivu-table-body {
      //   height: 394px !important;
      // }
      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
    }
  }
}
</style>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'whole-quantity',
  data() {
    return {
      municipalEchart: {
        xAxisData: ['已上报区县 ', '未上报区县'],
        showData: [
          { name: '已上报区县', value: 0 },
          { name: '未上报区县', value: 0 },
        ],
        count: 0,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      countyEchart: {
        xAxisData: ['已上报派出所 ', '未上报派出所'],
        showData: [
          { name: '已上报派出所', value: 0 },
          { name: '未上报派出所', value: 0 },
        ],
        count: 0,
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      shouldStyle: {
        width: '190px',
        height: '190px',
      },
      chartDatas: [],
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
      rankData: [],
      rankLoading: false,
      taskType: '',
      treeData: [],
      loading: false,
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '区县', key: 'regionName' },
        { title: '是否上报', key: 'isReport', slot: 'isReport' },
        { title: '应上报派出所数', key: 'shouldReportCount' },
        { title: '实际上报派出所数', key: 'actualReportCount' },
        { title: '未上报派出所数', key: 'notReportCount' },
        { title: '全量目录完整率', key: 'resultValue' },
      ],
      tableData: [],
      searchData: { totalCount: 0, pageNum: 1, pageSize: 20 },
      exportLoading: false,
      statisticalList: {},
      curBtn: 1,
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      municipalchart: {},
      countEchart: {},
      modelTag: 'BASIC_FULL_DIR_REPORT',
      region: '',
      list: [],
      contentClientHeight: 0,
    };
  },

  activated() {},
  async mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 30 * proportion : 0;
    await this.municipalChart();
    await this.countyChart();
  },
  methods: {
    async getExport() {
      this.exportLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        tab: this.modelTag,
      };
      try {
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
        this.exportLoading = false;
      } catch (err) {
        console.log(err);
      }
    },
    municipalChart() {
      let xAxisData = this.municipalEchart.xAxisData;
      this.municipalEchart.showData.map((item) => {
        if (item.name === '已上报区县') {
          item.value = this.statisticalList.bfdYetFirstCount || 0;
        } else {
          item.value = this.statisticalList.bfdNotFirstCount || 0;
        }
      });
      this.municipalEchart.count = this.statisticalList.bfdShouldFirstCount || 0;
      let obj = {
        seriesName: '应上报区县',
        xAxisData: xAxisData,
        showData: this.municipalEchart.showData,
        count: this.municipalEchart.count,
        color: this.municipalEchart.color,
      };

      this.municipalchart = this.$util.doEcharts.evaluationOverRin(obj);
    },
    countyChart() {
      let xAxisData = this.countyEchart.xAxisData;
      this.countyEchart.showData.map((item) => {
        if (item.name === '已上报派出所') {
          item.value = this.statisticalList.bfdYetSecondCount || 0;
        } else {
          item.value = this.statisticalList.bfdNotSecondCount || 0;
        }
      });
      this.countyEchart.count = this.statisticalList.bfdShouldSecondCount || 0;
      let obj = {
        seriesName: '应上报派出所',
        xAxisData: xAxisData,
        showData: this.countyEchart.showData,
        count: this.countyEchart.count,
        color: this.countyEchart.color,
      };

      this.countEchart = this.$util.doEcharts.evaluationOverRin(obj);
    },
    changeBtn(val) {
      if (this.modelTag == val) {
        return;
      }
      this.modelTag = val;
      this.getTableData();
    },

    // 表格
    async getTableData() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          pageNumber: 0,
          pageSize: 0,
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        if (this.modelTag == 'BASIC_FULL_DIR_REPORT') {
          this.tableData = datas.fullDirReport;
        } else {
          if (this.paramsList.displayType == 'ORG') {
            this.defaultProps = { label: 'orgName', children: 'children' };
            this.treeData = this.$util.common.arrayToJson(
              this.$util.common.deepCopy(datas.fullDirNotReport),
              'id',
              'parentId',
            );
          } else {
            this.defaultProps = { label: 'regionName', children: 'children' };
            this.treeData = this.$util.common.arrayToJson(datas.fullDirNotReport, 'regionCode', 'parentCode');
          }
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  props: {
    statisticalData: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
    rankList: {
      type: Array,
      default: () => [],
    },
    regionType: {},
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val.length !== 0) {
          this.statisticalList = val;
          this.municipalChart();
          this.countyChart();
        }
      },
      deep: true,
      immediate: true,
    },
    rankList: {
      handler(val) {
        if (val.length !== 0) {
          this.rankData = val;
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      async handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          await this.getTableData();
        }
      },
      deep: true,
      immediate: true,
    },
    regionType: {
      handler(val) {
        if (val) {
          this.region = val;
        }
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
  },
};
</script>
