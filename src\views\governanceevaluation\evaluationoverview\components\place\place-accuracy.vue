<template>
  <div class="place-accuracy height-full">
    <div class="" v-if="!componentName" ref="contentScroll">
      <div class="statistics-container">
        <div class="statistics">
          <icon-statistics :statistics-list="abnormalCount" :isflexfix="false">
            <template #resultValue="{ row }">
              <span
                class="icon-font position f-14"
                :class="row.qualified === '2' ? 'icon-budabiao warning' : 'icon-dabiao success'"
              ></span>
            </template>
          </icon-statistics>
        </div>
        <div class="information-echart" v-ui-loading="{ loading: echartsLoading, tableData: echartsData }">
          <draw-echarts
            :echart-option="determinantEchart"
            :echart-style="ringStyle"
            ref="attributeChart"
            class="charts"
            :echarts-loading="echartsLoading"
          ></draw-echarts>
        </div>
      </div>
      <div class="abnormal-title">
        <div class="fl">
          <i class="icon-font icon-xiugaijilu f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测明细</span>
        </div>
        <div class="export fr">
          <Button type="primary" class="btn_search" @click="getExport" :loading="exportLoading">
            <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
            <span class="inline ml-xs f-14">导出</span>
          </Button>
        </div>
      </div>
      <div class="hearder-title mb-sm mt-sm">
        <search-place ref="searchPlace" @startSearch="startSearch" />
        <ui-select-tabs :list="selectTabs" @selectInfo="selectInfo" ref="uiSelectTabs"></ui-select-tabs>
      </div>
      <ui-table :maxHeight="contentClientHeight" :table-columns="columns" :table-data="tableData" :loading="loading">
        <template #checkStatus="{ row }">
          <span
            class="tag"
            :style="{
              background: row.checkStatus === 1 ? '#0E8F0E' : '#BC3C19',
            }"
            >{{ checkStatusList(row.checkStatus) }}</span
          >
        </template>
        <template #action="{ row }">
          <create-tabs
            :componentName="themData.componentName"
            :tabs-text="themData.text"
            @selectModule="selectModule"
            :tabs-query="{
              access: paramsList.access,
              batchId: paramsList.batchId,
              regionCode: row.regionCode,
              placeId: row.placeId,
              indexId: $route.query.indexId,
            }"
            class="inline w-16"
          >
            <ui-btn-tip icon="icon-chakanjietu" class="mr-sm" content="区域详情"></ui-btn-tip>
          </create-tabs>
          <ui-btn-tip
            icon="icon-chakanyichangxiangqing"
            content="不合格原因"
            :disabled="row.checkStatus === '1'"
            @click.native="checkReason(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
      <nonconformance
        :isPage="false"
        ref="nonconformance"
        title="场所填报准确率-检测不合格原因"
        :tableColumns="reasonTableColumns"
        :tableData="reasonTableData"
        :reasonPage="reasonPage"
        :reasonLoading="reasonLoading"
        @handlePageChange="handlePageChange"
        @handlePageSizeChange="handlePageSizeChange"
      ></nonconformance>
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import governanceevaluation from '@/config/api/governanceevaluation';
import downLoadTips from '@/mixins/download-tips';
export default {
  mixins: [downLoadTips],
  name: 'place-accuracy',
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    SearchPlace: require('./components/search-place').default,
    IconStatistics: require('@/components/icon-statistics').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    Nonconformance: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/nonconformance.vue')
      .default,
    CreateTabs: require('@/components/create-tabs/create-tabs').default,
    Placemanagement: require('@/views/datagovernance/onlinegovernance/placemanagement/index').default,
  },
  props: {
    /**
     * 右上角检测任务筛选
     */
    taskObj: {
      type: Object,
      default() {
        return {};
      },
    },
    /**
     * 左侧树结构
     */
    currentTree: {
      type: Object,
      default() {
        return {};
      },
    },
    paramsData: {
      type: Object,
      default: () => ({}),
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      treeData: [],
      getDefaultSelectedOrg: '',
      currentOrgObj: {}, //机构树
      loading: false,
      width: 155, // 设备模式数量检索展示对应label宽度
      bigPictureShow: false, // 打图展示
      imgList: [], // 大图图片
      abnormalCount: [
        {
          key: 'total',
          name: '场所总量',
          value: 0,
          icon: 'icon-yingjianceshebeishuliang',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color1',
        },
        {
          key: 'actualDetectionCount',
          name: '实际检测场所数量',
          value: 0,
          icon: 'icon-shijijianceshebeishuliang',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color2',
        },
        {
          key: 'qualifiedCount',
          name: '检测合格场所数',
          value: 0,
          icon: 'icon-jiancehegeshebeishu',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color3',
        },
        {
          key: 'unQualifiedCount',
          name: '检测不合格场所数',
          value: 0,
          icon: 'icon-jiancebuhegeshebeishu',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number', // number数字 percentage 百分比
          textColor: 'color4',
        },
        {
          key: 'resultValue',
          name: '场所填报准确率',
          value: 0,
          icon: 'icon-zhuapaishulianghegeshuai',
          iconColor: 'icon-bg5',
          liBg: 'li-bg5',
          type: 'percentage', // number数字 percentage 百分比
          textColor: 'color5',
        },
      ],
      echartsData: [],
      infoObj: [], // 统计接口返回
      searchData: {}, // 查询参数
      columns: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        { title: '地点名称', key: 'placeName', ellipsis: true, tooltip: true },
        { title: '地点俗称', key: 'placeAlias', ellipsis: true, tooltip: true },
        {
          title: '采集区域类型',
          key: 'sbcjqyText',
          ellipsis: true,
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          width: 150,
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          width: 150,
        },
        {
          title: '检测结果',
          key: 'checkStatus',
          width: 120,
          slot: 'checkStatus',
        },
        { title: '详细地址', key: 'placeStandardAddress', width: 200 },
        {
          title: '操作',
          slot: 'action',
          align: 'center',
          width: 100,
          className: 'table-action-padding',
          fixed: 'right',
        },
      ],
      tableData: [],
      echartsLoading: false,
      ringStyle: {
        width: '100%',
        height: '250px',
      },
      determinantEchart: {},
      barData: [],
      echartData: [],
      currentRow: {},
      exportLoading: false,
      selectTabsList: [],
      selectTabs: [],
      errorMessageList: [],
      reasonTableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { title: '不合格原因', key: 'errorMessage' },
        { title: '不合格字段', key: 'propertyName' },
        { title: '检测规则', key: 'componentName' },
        { title: '实际结果', key: 'result' },
      ],
      reasonTableData: [],
      reasonPage: { totalCount: 0, pageNum: 1, pageSize: 20 },
      reasonLoading: false,
      placeId: '',
      themData: {
        componentName: 'Placemanagement', // 需要跳转的组件名
        text: '区域管理', // 跳转页面标题
        title: '区域管理',
        type: 'view',
      },
      componentLevel: 2,
      componentName: '',
      statisticalList: {},
      paramsList: {},
      contentClientHeight: 0,
    };
  },
  computed: {},
  watch: {
    $route: 'getParams',
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      async handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.initList();
          this.getSelectTabs();
          await this.getPlaceGraphsInfo();
          await this.initRing();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  async created() {
    this.getParams();
    if (this.componentName) return;
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 370 * proportion : 0;
  },
  methods: {
    statistical() {
      Object.keys(this.statisticalList).map((key) => {
        this.abnormalCount.map((item) => {
          if (key === item.key) {
            item.value = this.statisticalList[key];
          }
        });
      });
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    handlePageChange(val) {
      this.reasonPage.pageNum = val;
      this.getReason();
    },
    handlePageSizeChange(val) {
      this.reasonPage.pageSize = val;
      this.getReason();
    },
    async getReason() {
      this.reasonLoading = true;
      let params = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgRegionCode: this.paramsList.orgRegionCode,
        customParameters: {
          placeId: this.placeId,
        },
        pageSize: this.reasonPage.pageSize,
        pageNumber: this.reasonPage.pageNum,
      };
      try {
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getPopUpData, params);
        this.reasonTableData = data || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.reasonLoading = false;
      }
    },
    // 不合格原因
    checkReason(row) {
      this.placeId = row.placeId;
      this.getReason();
      this.$refs.nonconformance.init();
    },
    selectInfo(val) {
      this.errorMessageList =
        val &&
        val.map((item) => {
          if (item.select) {
            return item.name;
          }
        });
      this.startSearch();
    },
    // 异常列表
    async getSelectTabs() {
      try {
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let res = await this.$http.post(evaluationoverview.getAbnormalLabel, params);
        this.selectTabs = res.data.data.map((item) => {
          let obj = {};
          obj.name = item;
          return obj;
        });
      } catch (error) {
        console.log(error);
      }
    },
    async getPlaceGraphsInfo() {
      try {
        this.echartsLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getGraphsInfo, params);
        this.echartsData = data || [];
      } catch (e) {
        // console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    async getExport() {
      try {
        this.exportLoading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            ...this.searchData,
            errorMessageList: this.errorMessageList,
          },
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(evaluationoverview.exportDeviceDetailData, params);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    initRing() {
      let opts = {
        xAxis: this.echartsData.map((item) => item.propertyName),
        data: this.echartsData.map((item) => item.count),
        echartsData: this.echartsData,
      };
      this.determinantEchart = this.$util.doEcharts.placeRationality(opts);
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    async initList() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          batchId: this.paramsList.batchId,
          access: this.paramsList.access,
          displayType: this.paramsList.displayType,
          orgRegionCode: this.paramsList.orgRegionCode,
          customParameters: {
            ...this.searchData,
            errorMessageList: this.errorMessageList,
          },
          pageSize: this.pageData.pageSize,
          pageNumber: this.pageData.pageNum,
        };
        let {
          data: { data },
        } = await this.$http.post(evaluationoverview.getDetailData, params);
        this.tableData = data.entities || [];
        this.pageData.totalCount = data.total || 0;
      } catch (e) {
        // console.log(e);
      } finally {
        this.loading = false;
      }
    },
    // 检索
    async startSearch(searchData) {
      this.searchData = searchData;
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.$nextTick(async () => {
        this.initList();
      });
    },
    // 统计参数填充
    checkStatusList(checkStatus) {
      return checkStatus === 1 ? '合格' : '不合格';
    },
  },
};
</script>
<style lang="less" scoped>
.place-accuracy {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 698px) !important;
      //min-height: 240px !important;
    }
  }
}
.hearder-title {
  color: #fff;
  font-size: 14px;
  .mr20 {
    margin-right: 20px;
  }
  .blue {
    color: #19c176;
  }
}
.tag {
  display: inline-block;
  width: 54px;
  height: 22px;
  border-radius: 4px;
  text-align: center;
  vertical-align: middle;
}

.white-circle {
  position: relative;
  display: inline-block;
  line-height: 10px;
  vertical-align: middle;
  height: 10px;
  width: 10px;
  border-radius: 50%;
  background: #f5f5f5;
}
.statistics-container {
  display: flex;

  .statistics {
    width: 766px;
    height: 252px;
    margin-top: 10px;
    margin-right: 10px;
    @{_deep} .information-statistics {
      background: var(--bg-sub-content);
      .statistics-ul {
        padding: 20px 5px 10px 20px;
      }
    }
  }
  .information-echart {
    flex: 1;
    height: 252px;
    background: var(--bg-sub-content);
    margin-top: 10px;

    .echarts-box {
      width: 100%;
      height: 100% !important;

      .charts {
        width: 100%;
        height: 100% !important;
      }
    }
  }
}
.abnormal-title {
  height: 48px;
  line-height: 48px;
  border-bottom: 1px solid rgba(7, 66, 119, 1);
  .color-filter {
    color: rgba(43, 132, 226, 1);
    vertical-align: middle;
  }
}
.success {
  color: #0e8f0e;
}
.warning {
  color: #bc3c19;
}
.position {
  position: absolute;
  right: 10px;
  top: 10px;
}
</style>
