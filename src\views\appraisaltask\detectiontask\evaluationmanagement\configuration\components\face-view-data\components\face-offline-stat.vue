<template>
  <div class="fase-offline-stat-box">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="150">
      <FormItem label="选择关联的检测任务" prop="taskSchemeId">
        <Select
          class="input-width-number"
          v-model="formData.taskSchemeId"
          clearable
          filterable
          placeholder="请选择关联的检测任务"
          @on-change="handleChangeTask"
        >
          <Option v-for="schemeItem in taskSchemeList" :key="schemeItem.taskSchemeId" :value="schemeItem.taskSchemeId">
            {{ schemeItem.schemeName }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="统计模式" prop="statisticalModel" class="statistical-modal-box">
        <CheckboxGroup :value="formData.statisticalModel" @on-change="changeStatisticalModalGroup">
          <Checkbox :label="SMODE_REGION" class="mr-md">行政区划</Checkbox>
          <Checkbox :label="SMODE_ORG" class="mr-md">组织机构</Checkbox>
          <Checkbox :label="SMODE_DEVICETAG" class="flex-aic">
            <span class="mr-sm">设备标签</span>
            <span class="font-gray" @click.prevent="addStatisticTag" v-if="formData.tagIds?.length"
              >（已选择{{ formData.tagIds.length }}标签）</span
            >
            <i class="icon-font icon-peizhibiduiziduan f-14 font-gray" @click.prevent="addStatisticTag"></i>
          </Checkbox>
        </CheckboxGroup>
      </FormItem>
      <FormItem label="选择指标" prop="indexId">
        <Select
          class="input-width-number"
          v-model="formData.indexId"
          clearable
          filterable
          :disabled="!formData.taskSchemeId"
          placeholder="请选择指标"
          :loading="loading"
        >
          <Option v-for="indexItem in indexList" :key="indexItem.indexType" :value="indexItem.indexId">
            {{ indexItem.indexName }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="检测计划" class="right-item mb-0" prop="cronData">
        <TestPlan
          :form-data="formData"
          :form-model="formModel"
          :index-type="moduleAction.indexType"
          @checkTime="checkTime"
        ></TestPlan>
      </FormItem>
      <FormItem label="统计范围" prop="statModel">
        <Select v-model="formData.statModel" placeholder="请选择统计范围" class="start-model mr-xs">
          <Option v-for="item in statisticRangeOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
        <RadioGroup v-model="formData.statRange">
          <Radio :label="1">本月</Radio>
          <Radio :label="2">上月</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="累计离线" prop="offLineDay">
        <span class="mr-xs base-text-color">>=</span>
        <InputNumber
          v-model="formData.offLineDay"
          :min="1"
          :max="Number.MAX_SAFE_INTEGER"
          class="offline-day"
          placeholder="请输入累计离线天数"
        ></InputNumber>
        <Select class="ml-sm width-num" v-model="formData.statDimension">
          <Option v-for="item in offlineOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
      </FormItem>
      <FormItem label="连续离线" prop="serialOffLineDay">
        <span class="mr-xs base-text-color">>=</span>
        <InputNumber
          v-model="formData.serialOffLineDay"
          :min="1"
          :max="Number.MAX_SAFE_INTEGER"
          class="offline-day"
          placeholder="请输入累计离线天数"
        ></InputNumber>
        <Select class="ml-sm width-num" v-model="formData.statDimension" transfer>
          <Option v-for="item in offlineOptions" :key="item.value" :value="item.value">{{ item.label }}</Option>
        </Select>
        <p class="color-failed">备注：一天检测多次以最后一次结果为准，当天未检测，则默认在线。</p>
      </FormItem>
    </Form>
    <!--    关联标签弹窗-->
    <customize-filter
      v-model="addTagVisible"
      :customize-action="tagFilterAttrs.customizeAction"
      :content-style="tagFilterAttrs.contentStyle"
      :field-name="tagFilterAttrs.fieldTagName"
      :checkbox-list="tagFilterAttrs.allTagList"
      :default-checked-list="formData.tagIds"
      :show-clear-all="true"
      @confirmFilter="confirmTagFilter"
    >
    </customize-filter>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import taganalysis from '@/config/api/taganalysis';

export default {
  props: {
    moduleAction: {
      type: Object,
      default: () => {},
    },
    indexType: {
      type: String,
      default: '',
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      SMODE_DEVICETAG: this.global.STATISTICAL_MODAL.SMODE_DEVICETAG, //统计模式：设备标签
      SMODE_REGION: this.global.STATISTICAL_MODAL.SMODE_REGION, //统计模式：行政区划
      SMODE_ORG: this.global.STATISTICAL_MODAL.SMODE_ORG, //统计模式：组织机构
      statisticRangeOptions: [{ label: '按月统计', value: 1 }],
      offlineOptions: [
        { label: '天', value: 1 },
        { label: '次', value: 2 },
      ],
      formData: {
        cronData: [],
        cronType: '',
        timePoints: [],
        taskSchemeId: '',
        indexId: '',
        statModel: 1, //--统计模式,1-按月统计
        statRange: 1, //-- 统计范围,(1-本月，2-上月)
        statDimension: 1, //-- 统计维度，按（1-天，2-次）
        offLineDay: 5, // -- 累计离线 天数/次数
        serialOffLineDay: 2, //--连续离线 天数/次数
      },
      ruleCustom: {
        cronData: [
          {
            required: true,
            message: '请选择检测计划',
            trigger: 'change',
            type: 'array',
          },
        ],
        taskSchemeId: [
          {
            required: true,
            message: '请选择关联的检测任务',
            trigger: 'change',
          },
        ],
        indexId: [
          {
            required: true,
            message: '请选择指标',
            trigger: 'change',
            type: 'number',
          },
        ],
        statModel: [
          {
            required: true,
            message: '请选择统计范围',
            trigger: 'change',
            type: 'number',
          },
        ],
        offLineDay: [
          {
            required: true,
            message: '请输入累计离线',
            trigger: 'change',
            type: 'number',
          },
        ],
        serialOffLineDay: [
          {
            required: true,
            message: '请输入连续离线',
            trigger: 'change',
            type: 'number',
          },
        ],
        statisticalModel: [
          {
            required: true,
            type: 'array',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!value.length) {
                callback(new Error('请选择至少一个统计模式'));
              } else if (value.includes(3) && this.formData.tagIds?.length === 0) {
                callback(new Error('请选择待统计的设备标签'));
              } else {
                callback();
              }
            },
          },
        ],
      },
      taskSchemeList: [],
      indexList: [],
      loading: false,
      //选择标签
      addTagVisible: false,
      tagFilterAttrs: {
        allTagList: [],
        customizeAction: {
          title: '添加设备标签',
          leftContent: '选择设备标签及排序',
          rightContent: '设备标签显示',
          moduleStyle: {
            width: '70%',
          },
        },
        contentStyle: {
          height: `${500 / 192}rem`,
        },
        fieldTagName: {
          id: 'tagId',
          value: 'tagName',
        },
      },
    };
  },
  watch: {
    formModel: {
      handler(val) {
        if (val === 'edit') {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
          };
          this.getIndexList();
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.setDefaultStatisticalModel();
      },
      immediate: true,
    },
  },
  created() {
    this.getTaskList();
    this.getTagList();
  },
  methods: {
    // 表单提交校验
    async handleSubmit() {
      return await this.$refs['modalData'].validate();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
    async getTaskList() {
      const params = {
        pageNumber: 1,
        pageSize: 9999,
      };
      try {
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.evaluationPageList, params);
        this.taskSchemeList = data.entities || [];
      } catch (e) {
        console.log(e);
      }
    },
    async getIndexList() {
      try {
        this.loading = true;
        const params = {
          taskSchemeId: this.formData.taskSchemeId,
          pageNumber: 1,
          pageSize: 9999,
        };
        const {
          data: { data },
        } = await this.$http.post(governanceevaluation.taskIndexPageList, params);
        let faceOffline = ['FACE_ONLINE_RATE'];
        let vehicleOffline = ['VEHICLE_ONLINE_RATE'];
        let videoOffline = ['VIDEO_PLAYING_ACCURACY', 'VIDEO_GENERAL_PLAYING_ACCURACY'];
        let filterIndex = [];
        if (this.moduleAction.indexType === 'FACE_OFFLINE_STAT') {
          filterIndex = faceOffline;
        } else if (this.moduleAction.indexType === 'VEHICLE_OFFLINE_STAT') {
          filterIndex = vehicleOffline;
        } else if (this.moduleAction.indexType === 'VIDEO_OFFLINE_STAT') {
          filterIndex = videoOffline;
        }
        this.indexList = data.entities.filter((item) => filterIndex.includes(item.indexType));
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async handleChangeTask() {
      this.indexList = [];
      this.formData.indexId = '';
      await this.getIndexList();
      if (this.indexList.length > 0) {
        this.formData.indexId = this.indexList[0]['indexId'];
      }
    },
    checkTime(val) {
      this.formData = {
        ...this.formData,
        ...val,
      };
    },
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.tagFilterAttrs.allTagList = res.data.data;
      } catch (err) {
        console.log(err);
        this.tagFilterAttrs.allTagList = [];
      }
    },
    // 添加统计标签
    addStatisticTag() {
      this.addTagVisible = true;
    },
    async confirmTagFilter(val) {
      try {
        const tagIdList = val.map((item) => item.tagId);
        this.formData.tagIds = [...tagIdList];
        this.addTagVisible = false;
      } catch (err) {
        console.log(err);
      }
    },
    //设置默认的统计模式
    setDefaultStatisticalModel() {
      //指标未配置，继承任务的统计模式
      if (!this.formData.statisticalModel) {
        if (this.moduleAction.statisticalModelBo) {
          let models = this.moduleAction.statisticalModelBo.statisticalModel;
          this.formData.statisticalModel = this.$util.common.deepCopy(models);
        } else {
          this.formData.statisticalModel = [this.SMODE_ORG, this.SMODE_REGION];
        }
      }
      if (!this.formData.tagIds) {
        if (this.moduleAction.statisticalModelBo) {
          let tags = this.moduleAction.statisticalModelBo.tagIds;
          this.formData.tagIds = this.$util.common.deepCopy(tags);
        } else {
          this.formData.tagIds = [];
        }
      }
    },
    //勾选设备标签且未选择标签时，默认打开选择标签弹窗
    changeStatisticalModalGroup(group) {
      const oldStatisticalModel = [...this.formData.statisticalModel];
      this.formData.statisticalModel = group;
      let isChangeDeviceTag =
        group.includes(this.SMODE_DEVICETAG) && !oldStatisticalModel.includes(this.SMODE_DEVICETAG);
      if (isChangeDeviceTag && !this.formData.tagIds?.length) {
        this.addStatisticTag();
      }
    },
  },
  components: {
    TestPlan: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/test-plan.vue')
      .default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
  },
};
</script>
<style lang="less" scoped>
.fase-offline-stat-box {
  .input-width-number {
    width: 380px;
  }
  .start-model {
    width: 273px;
  }
  .offline-day {
    width: 279px;
  }
  .statistical-modal-box {
    @{_deep} .ivu-checkbox-group {
      display: flex;
      align-items: center;
      flex-direction: row !important;
    }
  }
}
</style>
