<template>
  <div class="config-modal">
    <ui-modal v-model="visible" title="参数配置" width="62.5rem">
      <ui-switch-tab
        class="ui-switch-tab mb-sm ml-sm"
        v-model="tabs"
        :tab-list="stateOptions"
        @beforeChangeTab="beforeChangeTab"
      >
      </ui-switch-tab>
      <div v-if="visible">
        <keep-alive>
          <component
            :is="componentName"
            :property-list="propertyList"
            :active-store="activeStore"
            :default-params="defaultParams"
            @updateParams="updateParams"
            class="component-style"
          ></component>
        </keep-alive>
      </div>
      <template #footer>
        <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
        <Button class="plr-30" type="primary" :disabled="isDisabled" @click="saveQuery">保存</Button>
      </template>
    </ui-modal>
  </div>
</template>
<style lang="less" scoped>
.config-modal {
  .component-style {
    max-height: 800px;
    padding: 10px;
    overflow-y: scroll;
  }
}
</style>
<script>
import assetsSync from '@/config/api/assetsSync';
import { configDefaultParams } from '../../util/enum';

export default {
  data() {
    return {
      defaultParams: {},
      componentName: null,
      tabs: '0',
      isDisabled: false,
      stateOptions: [
        {
          label: '同步配置',
          value: '0',
          componentName: 'SyncConfig',
        },
        {
          label: '检测配置',
          value: '1',
          componentName: 'RulesConfig',
        },
        {
          label: '比对配置',
          value: '3',
          componentName: 'CompareConfig',
        },
        {
          label: '入库配置',
          value: '2',
          componentName: 'InstorageConfig',
        },
      ],
      visible: false,
      searchParams: {},
    };
  },
  created() {
    this.queryByConfigType();
  },
  mounted() {},
  methods: {
    updateParams(params) {
      Object.assign(this.searchParams, params);
    },
    beforeChangeTab(val, item) {
      this.componentName = item.componentName;
    },
    async queryByConfigType() {
      try {
        let { data } = await this.$http.get(assetsSync.queryByConfigType, { params: { configType: this.activeStore } });
        this.defaultParams = data.data;
        !this.defaultParams ? (this.defaultParams = configDefaultParams) : null;
        this.$emit('getConfigParams', this.defaultParams);
      } catch (err) {
        console.log(err);
      }
    },
    async saveQuery() {
      try {
        this.searchParams.configType = this.activeStore;
        !!this.defaultParams && 'id' in this.defaultParams ? (this.searchParams.id = this.defaultParams.id) : null;
        let params = Object.assign(this.defaultParams, this.searchParams);
        await this.$http.post(assetsSync.updateConfig, params);
        this.$Message.success('配置成功');
        this.cancel();
      } catch (err) {
        console.log(err);
      }
    },
    cancel() {
      this.visible = false;
      this.searchParams = {};
      this.defaultParams = {};
      this.tabs = '0';
      this.componentName = 'SyncConfig';
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      !val ? this.cancel() : this.queryByConfigType();
      if (val) {
        if (this.activeStore === '2') {
          this.componentName = 'SyncConfig';
          this.stateOptions = [
            {
              label: '同步配置',
              value: '0',
              componentName: 'SyncConfig',
            },
            {
              label: '检测配置',
              value: '1',
              componentName: 'RulesConfig',
            },
            {
              label: '比对配置',
              value: '3',
              componentName: 'CompareConfig',
            },
            {
              label: '入库配置',
              value: '2',
              componentName: 'InstorageConfig',
            },
          ];
          this.tabs = '0';
        } else {
          this.componentName = 'RulesConfig';
          this.stateOptions = [
            {
              label: '检测配置',
              value: '1',
              componentName: 'RulesConfig',
            },
            {
              label: '比对配置',
              value: '3',
              componentName: 'CompareConfig',
            },
            {
              label: '入库配置',
              value: '2',
              componentName: 'InstorageConfig',
            },
          ];
          this.tabs = '1';
        }
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
  computed: {},
  props: {
    value: {},
    // 共享联网平台同步: 2 | 视图库同步:3 |一机一档同步:4
    activeStore: {},
    propertyList: {}, // 全部字段
  },
  components: {
    UiSwitchTab: require('@/components/ui-switch-tab/ui-switch-tab.vue').default,
    RulesConfig: require('./rules-config.vue').default,
    InstorageConfig: require('./instorage-config.vue').default,
    CompareConfig: require('./compare-config.vue').default,
    SyncConfig: require('./sync-config.vue').default,
  },
};
</script>
