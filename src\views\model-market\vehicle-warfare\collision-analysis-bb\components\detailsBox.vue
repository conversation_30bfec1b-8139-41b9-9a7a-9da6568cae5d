<!--
    * @FileDescription: 时空碰撞 - 分析结果
    * @Author: H
    * @Date: 2023/04/3
    * @LastEditors: 
    * @LastEditTime: 
 -->
 <template>
    <div class="right_box" :class="{'rightBox-pack': packUpDown}">
        <div class="title">
            <p>时空碰撞</p>
            <Icon type="ios-close" @click="handleCancel" />
        </div>
        <div class="hint_title">
            共<span> {{ list.length }} </span> 条结果
        </div>
        <ul class="box-content">
            <li class="box-list" v-for="(item, index) in list" :key='index' @click="handleDetails(item, index)">
                <div class="content-top">
                    <div class="content-top-img">
                        <img v-lazy="item.photoUrl" alt="" />
                    </div>
                    <div class="content-top-right">
                        <div class="content-top-right-name">
                            <span class="ellipsis flex">
                                <ui-icon type="qiche1" :size="16"></ui-icon>
                                <span class="block">{{ item.plateNo || '--' }}</span>
                            </span>
                        </div>
                        <span class="ellipsis">
                            <ui-icon type="time" :size="14"></ui-icon>
                            <span class="bule">{{ item.time || '--' }}</span>
                        </span>
                        <span class="ellipsis">
                            <ui-icon type="location" :size="14"></ui-icon>
                            <span class="orange" >{{ item.deviceName || '--' }}</span>
                        </span>
                    </div>
                </div>
                <!-- <div class="content-bottom">
                    <div class="iconList">
                        <opera-floor iconSec="icon-dangan2"></opera-floor>
                    </div>
                </div> -->
            </li>
            <ui-empty v-if="list.length === 0 && loading == false"></ui-empty>
            <ui-loading v-if="loading"></ui-loading>
        </ul>
        <Page size="small" :total="total" :page-size="pageInfo.pageSize" transfer class="page" @on-change="pageChange" @on-page-size-change="pageSizeChange" />
        <div class="footer" :class="{packArrow: packUpDown}" @click="handlePackup">
            <img :src="packUrl" alt="">
            <p>{{ packUpDown ? '展开' : '收起'}} </p>
        </div>
    </div>
</template>

<script>
import { querySpaceCollisionList } from '@/api/modelMarket';
// import { myMixins } from '../../../mixins/index.js';
import { collideList } from '@/api/modelMarket'
export default {
    name: '',
    // mixins: [myMixins],
    components:{
            
    },
    props: {
        tablist: {
            type: Array,
            default: () => []
        },

    },
    data () {
        return {
            list: [],
            tabIndex: 0,
            listIcon:{
                'face': ['xingming', 'shenfenzheng', 'camera'],
                'vehicle': ['chepai', 'xingming', 'shenfenzheng']
            },
            loading: false,
            detailsList: {},
            searchData: [],
            tabTitle: [],
            pageInfo: {
                pageNumber: 1,
                pageSize: 10
            },
            total: 0,
            packUpDown: false,
            packUrl: require('@/assets/img/model/icon/arrow.png'),
        }
    },
    watch:{
            
    },
    computed:{
       
        
    },
    created() {
            
    },
    mounted(){
            
    },
    methods: {
        init(data) {
            this.loading = true;
            this.searchData = params;
            let params = {
                timeSpaces: data,
                ...this.pageInfo,
                search:true
            }
            collideList(params)
            .then(res => {
                this.list = res.data;
                this.total = 
                console.log(res, 'res')
            })
            .finally(() => {
                this.loading = false;
            })
        },
        handleDetails(item) {
            this.$emit('openModal', item)
        },
        handleCancel() {
            this.$emit('cancel')
        },
        pageChange() {
            
        },
        pageSizeChange() {},
        handlePackup() {
            this.packUpDown = !this.packUpDown;
        }
    }
}
</script>

<style lang='less' scoped>
@import './style/index';
.right_box{
    width: 370px;
    position: absolute;
    left: 10px;
    top: 10px;
    background: #fff;
    height: calc( ~'100% - 20px' );
    transition: height 0.2s ease-out;
    box-shadow: 0px 3px 5px 0px rgba(0,0,0,0.3);
    .hint_title{
        padding: 15px 20px 0 20px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0,0,0,0.6);
        span{
            color: rgba(44, 134, 248, 1);
        }
    }
    .box-content{
        padding: 0 15px;
        height: calc(~' 100% - 140px');
        overflow-y: auto;
        position: relative;
        .box-list{
            background: #F9F9F9;
            padding: 5px 10px 0px 5px;
            margin-top: 10px;
            &:hover{
                background: rgba(44, 134, 248, 0.1);
            }
            .content-top{
                display: flex;
                &-img{
                    width: 80px;
                    height: 80px;
                    background: #F9F9F9;
                    border: 1px solid #d3d7de;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    img{
                        width: auto;
                        height: auto;
                        max-height: 80px;
                        max-width: 80px;
                    }
                }
                .content-top-right{
                    margin-top: 3px;
                    margin-left: 11px;
                    font-size: 14px;
                    width: calc( ~'100% - 91px' );
                    /deep/ .iconfont{
                        margin-right: 5px;
                    }
                    .bule{
                        color: #2C86F8;
                    }
                    .orange{
                        color: #F29F4C;
                    }
                    .block{
                        color: #000000;
                    }
                    &-name{
                        display: flex;
                        margin-bottom: 8px;
                        .flex{
                            flex:1;
                        }
                        .list_title{
                            font-size: 12px;
                            color: rgba(0,0,0,0.6);
                            cursor: pointer;
                            span{
                                color: rgba(44, 134, 248, 1);
                            }
                        }
                    }
                }
            }
            .content-bottom{
                display: flex;
                justify-content: space-between;
                margin-top: 5px;
                .iconList{
                    width: 80px;
                }
                .analyseIcon{
                    font-size: 12px;
                    color: #5584FF;
                    cursor: pointer;
                }
            }
        }
    }
    .page{
        text-align: center;
    }
    .footer{
        // color: #000000;
        position: absolute;
        bottom: 0px;
        left: 50%;
        transform: translate(-50%, 0px);
        background: #fff;
        width: 100%;
    }
}
.rightBox-pack{
    height: 80px;
    transition: height 0.2s ease-out;
    overflow: hidden;
}
/deep/ .cover{
    overflow: hidden;
}
</style>
