<!--
 * @Date: 2025-01-24 15:26:03
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-27 10:13:07
 * @FilePath: \icbd-view\src\views\juvenile-target-control\juvenile-alarm-manager\criminal-with\index.vue
-->
<!-- 与违法前科人同行 -->
<template>
  <div class="container">
    <searchForm
      ref="searchForm"
      @query="query"
      @reset="reset"
      type="cirminalWith"
      :noMore="true"
      :radioList="[]"
    >
    </searchForm>
    <!-- 操作栏 -->
    <!-- <div class="operate" v-show="queryList.length">
      <Checkbox
        v-model="allCheckStatus"
        :indeterminate="indeterminate"
        @click.native="allChecked()"
        >全选</Checkbox
      >
      <Dropdown style="margin-left: 20px" @click="batchStatus">
        <Button type="primary">
          批量处理
          <Icon type="ios-arrow-down"></Icon>
        </Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="1">设为有效</DropdownItem>
          <DropdownItem :name="2">设为无效</DropdownItem>
        </DropdownMenu>
      </Dropdown>
    </div> -->
    <!-- 列表 -->
    <div class="list">
      <div v-for="(item, index) in queryList" :key="index">
        <TogetherAlarm
          class="together-alarm"
          :data="item"
          @click.native="goDetailInfo(item, index)"
        ></TogetherAlarm>
      </div>
      <ui-empty v-if="queryList.length === 0"></ui-empty>
      <ui-loading v-if="loading"></ui-loading>
    </div>
    <!-- 分页 -->
    <ui-page
      :current="params.pageNumber"
      :total="total"
      countTotal
      :page-size="params.pageSize"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
    ></ui-page>
    <TogetherDetail
      v-if="detailShow"
      ref="togetherDetailRef"
      :tableList="queryList"
      :alarmInfo="alarmInfo"
      :tableIndex="tableIndex"
      @close="close"
    />
  </div>
</template>

<script>
import searchForm from "../../components/search-form.vue";
import TogetherAlarm from "./components/together-alarm.vue";
import { travelAlongDetailPageList } from "@/api/monographic/juvenile.js";
import TogetherDetail from "@/views/juvenile/components/detail/together-detail.vue";
export default {
  name: "",
  components: { TogetherAlarm, searchForm, TogetherDetail },
  props: {
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      total: 0,
      params: {
        pageNumber: 1,
        pageSize: 20,
      },
      columns: [
        { type: "selection", align: "center", width: 60 },
        { title: "序号", type: "index", width: 80, align: "center" },
        { title: "人员照片", slot: "image", align: "center" },
        { title: "姓名", key: "name", align: "center" },
        { title: "年龄", key: "area", align: "center" },
        { title: "证件类型", key: "adname", align: "center", width: 100 },
        { title: "身份证号", key: "townname", align: "center" },
        { title: "民族", key: "address", align: "center" },
        { title: "籍贯", key: "modifyTime", align: "center" },
        { title: "报警时间", key: "action", align: "center" },
        { title: "次数", key: "action", align: "center" },
      ],
      alarmInfo: {},
      tableIndex: 0,
      queryList: [],
      allCheckStatus: false,
      indeterminate: false,
      detailShow: false,
      loading: false,
    };
  },
  mounted() {
    if (this.$route.query.idCardNo) {
      this.$refs.searchForm.queryParam.idCardNo = this.$route.query.idCardNo;
    }
    this.query();
  },
  methods: {
    batchStatus() {},
    async query() {
      this.loading = true;
      let { alarmTimeB, alarmTimeE, idCardNo } =
        this.$refs.searchForm.getQueryParams();
      let param = {
        ...this.params,
        startTime: alarmTimeB,
        endTime: alarmTimeE,
        idCardNo,
      };
      let { data } = await travelAlongDetailPageList(param);
      this.total = data?.total;
      this.queryList =
        data.entities.map(
          ({
            emphasisFaceCaptureVo,
            emphasisPersonInfo,
            srcFaceCaptureVo,
            srcPersonInfo,
            ...item
          }) => ({
            srcFaceCaptureVo,
            srcPersonInfo,
            criminalFaceCaptureVo: emphasisFaceCaptureVo,
            criminalPersonInfo: emphasisPersonInfo,
            ...item,
          })
        ) || [];
      this.loading = false;
    },
    reset() {
      this.query();
    },
    pageChange(pageNumber) {
      this.params.pageNumber = pageNumber;
      this.query();
    },
    pageSizeChange(size) {
      this.params.pageSize = size;
      this.params.pageNumber = 1;
      this.query();
    },
    goDetailInfo(item, index) {
      // this.$refs.togetherDetailRef.showInfo(item, index);
      this.tableIndex = index;
      this.detailShow = true;
    },
    close() {
      this.detailShow = false;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .operate {
    display: flex;
    justify-content: space-between;
    margin: 12px 0;
  }
  .list {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    overflow: scroll;
    margin-top: 20px;
    gap: 10px;
    position: relative;
    align-content: flex-start;
    .together-alarm {
      cursor: pointer;
    }
  }
}
</style>
