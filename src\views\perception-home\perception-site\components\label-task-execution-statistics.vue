<template>
  <ui-card>
    <div slot="title" class="head-title">
      <img src="@/assets/img/home/<USER>" alt=''/><span class="title-text">标签任务执行方式统计</span>
    </div>
    <div class="echart-wrap">
      <break-pie-echart v-if="labelTaskExecutionList.length" :data="labelTaskExecutionList"/>
    </div>
  </ui-card>
</template>
<script>
  import BreakPieEchart from './echarts/break-pie-echart.vue'
  export default {
    components: {
      BreakPieEchart
    },
    props: {
      statisticsList: {
        type: Array,
        default: () => []
      }
    },
    data () {
      return {
        labelTaskExecutionList: []
      }
    },
    watch: {
      'statisticsList': {
        handler (val) {
          this.labelTaskExecutionList = this.updataTaskExecution(this.statisticsList)
        },
        immediate: true
      }
    },
    methods: {
      /**
       * 标签任务执行方式统计
      */
      updataTaskExecution (list) {
        let executionList = []
        if (list.length) {
          const total = list.reduce((v, m) => v.count + m.count)
          list.forEach(v => {
            if (v.execType === '1') {
              executionList.push({
                dataKey: '1',
                name: '实时任务',
                value: v.count,
                itemStyle: {
                  color: '#E99E53'
                },
                percent: parseFloat(v.count / total).toFixed(2) * 100
              })
            } else if (v.execType === '2') {
              executionList.push({
                dataKey: '2',
                name: '周期任务',
                value: v.count,
                itemStyle: {
                  color: '#20D7FF'
                },
                percent: parseFloat(v.count / total).toFixed(2) * 100
              })
            }
          })
        } else {
          executionList = [
            {
              dataKey: '1',
              name: '实时任务',
              value: 0,
              itemStyle: {
                color: '#E99E53'
              },
              percent: 0
            },
            {
              dataKey: '2',
              name: '周期任务',
              value: 0,
              itemStyle: {
                color: '#20D7FF'
              },
              percent: 0
            }
          ]
        }
        return executionList
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    display: flex;
    flex: 1;
  }
</style>