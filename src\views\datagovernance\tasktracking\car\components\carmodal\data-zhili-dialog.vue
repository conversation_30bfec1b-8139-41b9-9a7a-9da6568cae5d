<template>
  <div>
    <ui-modal ref="modal" :title="title" :footerHide="true" v-model="modalvisible" :width="width">
      <!-- <tagView /> -->
      <tabsView ref="tabs" :list="tabsList" :multiSelect="false" :needExpand="false" @selectInfo="tagChange" />
      <!-- <tagView :list="tabsList" @tagChange="tagChange" /> -->

      <div class="content">
        <!-- 此次 0 是根据 list 下标作为参考对象 -->
        <div v-if="curIndex == 3">
          <div class="nullContent" v-if="dataInfo[3].list.length == 0">
            {{ this.dataInfo[3].reason }}
          </div>
          <div v-else>
            <div style="margin-bottom: 20px">
              车牌原始值：<span class="blue">{{ carNumber }}</span>
            </div>
            <ui-table
              v-if="dataInfo[3].list"
              class="ui-table"
              :table-columns="columns"
              :table-data="dataInfo[3].list[0].list"
              :loading="loading"
            >
              <template #jcsf="{ row, index }">
                <span class="blue" v-if="index == dataInfo[3].list[0].list.length - 1">{{ row.algorithmVendor }}</span>
                <span v-else>{{ getFactryName(row.algorithmVendor) }}</span>
              </template>
              <template #sbjg="{ row, index }">
                <span>
                  <span class="blue" v-if="index == dataInfo[3].list[0].list.length - 1">
                    <span v-if="row.result" class="bluebg">{{ row.recognitionResult || '无法判定' }}</span>
                    <span v-else class="greenbg">{{ row.recognitionResult || '无法判定' }}</span>
                  </span>
                  <span v-else>
                    <span v-if="row.recognitionResult == dataInfo[3].list[0].originalFieldValue" class="bluebg">{{
                      row.recognitionResult || '无法判定'
                    }}</span>
                    <span v-else class="greenbg">{{ row.recognitionResult || '无法判定' }}</span>
                  </span>
                </span>
              </template>
              <template #pdjg="{ row, index }">
                <span class="blue" v-if="index == dataInfo[3].list[0].list.length - 1">
                  <img v-if="row.result" src="@/assets/img/car-modal/consistent.png" alt="#" />
                  <img else src="@/assets/img/car-modal/no-consistent.png" alt="#" />
                </span>
                <span v-else>
                  <span v-if="row.recognitionResult == dataInfo[3].list[0].originalFieldValue" class="green">一致</span>
                  <span v-else class="red">不一致</span>
                </span>
              </template>
            </ui-table>
          </div>
        </div>

        <div v-if="curIndex == 4">
          <div class="nullContent" v-if="carTableList.list.length == 0">
            {{ carTableList.reason }}
          </div>
          <div v-else>
            <tabsView :list="configList" :multiSelect="false" :needExpand="false" @selectInfo="tagChange2" />

            <div style="margin: 20px 7px">
              车辆类型原始值：<span class="blue">{{ carValue }}</span>
            </div>

            <ui-table class="ui-table" :table-columns="columns" :table-data="carTableList.list" :loading="loading">
              <template #jcsf="{ row, index }">
                <span class="blue" v-if="index == carTableList.list.length - 1">{{ row.algorithmVendor }}</span>
                <span v-else>{{ getFactryName(row.algorithmVendor) }}</span>
              </template>
              <template #sbjg="{ row, index }">
                <span class="blue" v-if="index == carTableList.list.length - 1">{{ row.recognitionResult }}</span>
                <span v-else>{{ row.recognitionResult }}</span>
              </template>
              <template #pdjg="{ row, index }">
                <span>
                  <span class="blue" v-if="index == carTableList.list.length - 1">
                    <img v-if="!row.recognitionResult" src="@/assets/img/car-modal/unabletoverify.png" alt="#" />
                    <img
                      v-else-if="row.recognitionResult == carTableList.originalFieldValue"
                      src="@/assets/img/car-modal/consistent.png"
                      alt="#"
                    />
                    <img v-else src="@/assets/img/car-modal/no-consistent.png" alt="#" />
                  </span>
                  <span v-else>
                    <span v-if="row.recognitionResult == carTableList.originalFieldValue" class="green">一致</span>
                    <span v-else class="red">不一致</span>
                  </span>
                </span>
              </template>
            </ui-table>

            <div style="margin: 20px 7px">
              算法选举正确车辆类型为：<span class="blue">{{ carValueResult }}</span>
            </div>
          </div>
        </div>
      </div>
    </ui-modal>
    <look-scene ref="lookSceneRef" :imgList="smallImagePath" />
  </div>
</template>
<script>
import api from '@/config/api/car-threm';
import api2 from '@/config/api/algorithm';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'yichang',
  props: {},
  data() {
    return {
      width: 1300,
      title: '不合格原因',
      firstIndex: true, // 初始化
      curIndex: 0,
      curItem: null,
      modalvisible: false,
      textIndex: 0,
      dataInfo: [null, null, null, null, null],
      list: [
        '抓拍时间异常',
        '上传超时',
        // "图像模糊",
        '大图URL不可访问',
        '车牌准确性存疑',
        // "车辆结构化属性缺失",
        '车辆结构化属性正确性存疑',
      ],
      loading: false,
      columns: [
        { title: '检测算法', slot: 'jcsf' },
        { title: '识别结果', slot: 'sbjg' },
        { title: '判定结果', slot: 'pdjg' },
      ],
      showTable: true,
      tableData: [],
      allTabsList: [], // 所有分类
      tabsList: [], // 显示分类
      configList: [], // 结构化数据列表
      carNumber: '',
      factryList: [], // 检测算法列表
      allCarTableList: [], // 车辆结构化属性正确性存疑 全部数据
      carTableList: [], // 车辆结构化属性正确性存疑 当前数据
      carValue: '', // 车辆类型原始值
      carValueResult: '', // 车辆类型原始值
      deviceType: {}, // 设备类型
      smallImagePath: [], // 大图路径
    };
  },
  async created() {
    if (this.algorithmList.length == 0) await this.getAlldicData();
    this.getDeviceType();
  },
  computed: {
    ...mapGetters({
      algorithmList: 'algorithm/algorithmVendorType',
    }),
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    init() {
      this.tabsList = [];
      // this.$http.post(api.idQueryCar+ "512663").then(res => {
      this.$http.post(api.idQueryCar + this.curItem.vehicleInfoId).then((res) => {
        var list = res.data.data;
        list.forEach((item) => {
          if (item.reasonType == 4) {
            this.dataInfo[3] = item;
            if (item.resultDetail) {
              this.dataInfo[3].list = JSON.parse(item.resultDetail);
              this.carNumber = this.dataInfo[3].list[0].originalFieldValue;
              let obj = {
                algorithmVendor: '综合判定',
                recognitionResult: this.dataInfo[3].list[0].result,
                result: this.dataInfo[3].list[0].result == this.dataInfo[3].list[0].originalFieldValue ? true : false,
                // result: this.dataInfo[3].list[0].result == this.dataInfo[3].list[0].originalFieldValue ? true : false
              };
              this.dataInfo[3].list[0].list.push(obj);
            } else {
              this.dataInfo[3].list = [];
              this.dataInfo[3].reason = item.reason;
            }
          }
          if (item.reasonType == 5) {
            this.dataInfo[4] = item;
            if (item.resultDetail) {
              this.dataInfo[4].list = JSON.parse(item.resultDetail);

              // 各类型集合
              this.allCarTableList = this.dataInfo[4].list;
              this.carTableList = {};

              // 当前
              this.carTableList = JSON.parse(JSON.stringify(this.allCarTableList[0]));
              let obj = {
                algorithmVendor: '综合判定',
                recognitionResult: this.carTableList.result,
                result: this.carTableList.result ? true : false,
              };
              this.carTableList.list.push(obj);
              this.carValue = this.carTableList.originalFieldValue;
              this.carValueResult = this.carTableList.result;
            } else {
              this.carTableList.list = [];
              this.carTableList.reason = item.reason;
            }
          }

          let obj = this.allTabsList.find((it) => {
            return it.dataKey == item.reasonType;
          });
          if (obj) {
            if (this.firstIndex) {
              this.curIndex = parseInt(item.reasonType) - 1;
              this.firstIndex = false;
            }
            this.tabsList.push(obj);
          }
        });

        this.$refs.tabs.curIndex = 0;

        // this.curIndex = parseInt(this.tabsList[0].dataKey) -1;
      });
    },

    async queryCarThremSelect() {
      await this.$http.get(api.queryCarThremSelect + '36').then((res) => {
        if (res.data.code == 200) {
          this.configList = res.data.data.map((val) => {
            val.name = val.checkColumnValue;
            return val;
          });
        } else {
          console.log(res.data.msg);
        }
      });
    },

    showModal(list, item) {
      this.firstIndex = true;
      this.curItem = item;
      this.allTabsList = list.filter((item) => {
        return item.dataDes == '1';
      });
      this.queryCarThremSelect();
      this.init();
      // this.$refs.modal.modalShow = true;
      this.modalvisible = true;
    },

    tagChange2(arr) {
      this.carTableList = {};
      var objItem = this.allCarTableList.find((item) => {
        return item.originalField == arr.checkColumnName;
      });
      // 当前
      this.carTableList = JSON.parse(JSON.stringify(objItem));
      var obj = {
        algorithmVendor: '综合判定',
        recognitionResult: objItem.result,
        result: objItem.result ? true : false,
      };
      this.carTableList.list.push(obj);
      this.carValue = objItem.originalFieldValue;
      this.carValueResult = objItem.result;
    },

    getFactryName(key) {
      var obj = this.algorithmList.find((item) => {
        return item.dataKey == key;
      });
      return obj ? obj.dataValue : '--';
    },

    tagChange(arr) {
      this.curIndex = parseInt(arr.dataKey) - 1;
    },

    async getDeviceType() {
      await this.$http.get(api2.dictData + 'tag_category').then((res) => {
        if (res.data.code === 200) {
          res.data.data.map((val) => {
            this.deviceType[val.dataKey] = val.dataValue;
          });
        }
      });
    },

    showSmallImg() {
      this.smallImagePath.push(this.dataInfo[2].smallImagePath);
      this.$refs.lookSceneRef.visible = true;
    },
  },
  watch: {},
  components: {
    // tagView: require('./tags').default,
    UiTable: require('@/components/ui-table.vue').default,
    tabsView: require('@/components/ui-select-tabs').default,
    lookScene: require('@/components/look-scene').default,
  },
};
</script>
<style lang="less" scoped>
.blue {
  color: var(--color-primary);
}

.explain {
  color: #c76d28;
}

.red {
  color: #e44f22;
}

.green {
  color: #0e8f0e;
}

.greenbg {
  background: var(--color-primary);
  padding: 6px 10px;
  display: inline-block;
  border-radius: 6px;
  color: #fff;
}

.bluebg {
  background: #087120;
  padding: 6px 10px;
  display: inline-block;
  border-radius: 6px;
  color: #fff;
}

.content {
  padding-top: 20px;

  .item1 {
    position: relative;
    // padding-left: 80px;
    min-height: 30px;
    .a {
      color: var(--color-primary);
      cursor: pointer;
    }
  }
  .label {
    position: absolute;
    left: 0;
    display: inline-block;
    width: 80px;
    text-align: right;
  }
}

.item-detail {
  position: relative;
  padding-left: 90px;
  span:first-child {
    position: absolute;
    left: 0;
    width: 80px;
    text-align: right;
  }
}

.nullContent {
  display: flex;
  height: 160px;
  justify-content: center;
  align-items: center;
  color: #e44f22;
}

/deep/ .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}
</style>
