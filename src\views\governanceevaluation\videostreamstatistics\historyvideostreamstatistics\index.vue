<template>
  <div class="historyvideostreamstatistics">
    <statistics indexId="4008" :columns="tableColumns" :switchData="switchData">
      <template #title> 历史视频流调阅失败情况统计 </template>
      <template #day="{ searchData }">
        <ui-label label="调阅失败天数" :width="100" class="inline">
          <InputNumber v-model="searchData.startCount" placeholder="请输入开始天数" class="width-xs"></InputNumber>
          <span class="font-blue f-14 ml-xs mr-xs">—</span>
          <InputNumber v-model="searchData.endCount" placeholder="请输入结束天数" class="width-xs"></InputNumber>
          <span class="base-text-color f-14 ml-sm">天</span>
        </ui-label>
      </template>
    </statistics>
  </div>
</template>

<script>
export default {
  name: 'historyvideostreamstatistics',
  components: {
    statistics: require('@/views/governanceevaluation/videostreamstatistics/components/statistics.vue').default,
  },
  props: {},
  data() {
    return {
      switchData: [
        { label: '全量视频监控', value: 0 },
        { label: '重点视频监控', value: 1 },
      ],
      tableColumns: [
        { title: '序号', type: 'index', width: 70, align: 'center' },
        { title: '设备编码', key: 'deviceId', minWidth: 150, ellipsis: true, tooltip: true },
        { title: '设备名称', key: 'deviceName', minWidth: 150, ellipsis: true, tooltip: true },
        { title: '组织机构', key: 'orgName', minWidth: 100 },
        { title: '行政区划', key: 'civilName', minWidth: 100 },
        { title: '设备状态', key: 'phyStatus', slot: 'phyStatus', minWidth: 100 },
        { title: '设备类型', key: 'isImportant', slot: 'isImportant', minWidth: 100 },
        { title: '调阅失败天数', key: 'offlineDayCount', minWidth: 100 },
        { title: '最后一次调阅失败时间', key: 'offlineDate', minWidth: 100 },
        { title: '全部调阅失败时间', key: 'allOfflineDate', minWidth: 100, ellipsis: true, tooltip: true },
      ],
    };
  },
  computed: {},
  watch: {},
  filter: {},
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped>
.historyvideostreamstatistics {
  width: 100%;
  height: 100%;
  background: var(--bg-content);
  padding: 10px 20px 20px 20px;
}
</style>
