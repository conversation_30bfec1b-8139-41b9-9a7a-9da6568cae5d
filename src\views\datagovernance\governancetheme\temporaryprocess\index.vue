<template>
  <div id="temporaryprocess">
    <div class="temporaryprocess-left">
      <div class="temporaryprocess-left-optbtn">
        <Button type="primary">开始运行</Button>
        <Button type="primary">保存配置</Button>
      </div>
      <div v-for="(item, index) in aggregateOptions" :key="index">
        <aggre-connect :propData="item"></aggre-connect>
      </div>
    </div>
    <div class="temporaryprocess-right">
      <component-package></component-package>
    </div>
    <!-- <data-input ref="dataInput"></data-input>
    <field-mapping ref="fieldMapping"></field-mapping> -->
  </div>
</template>
<script>
export default {
  name: 'temporaryprocess',
  props: {},
  data() {
    return {
      aggregateOptions: [
        {
          addVisible: false,
          top: '2.0rem',
          left: '0.125rem',
          datas: [
            {
              name: 'dataInput',
              icon: 'icon-zu16191',
              title: '数据输入',
              desc: '选择待治理数据',
              left: '10px',
              iconPass: true,
              iconSetting: true,
            },
          ],
          connectingOptions: {
            width: '0.07rem',
            height: '0.04rem',
            top: '2.28rem',
            left: '1.18rem',
          },
        },
        {
          addVisible: false,
          top: '1.73rem',
          left: '1.3rem',
          datas: [
            {
              name: 'fieldMapping',
              icon: 'icon-ziduanyingshe',
              title: '字段映射',
              desc: '设置原始数据与标准数据的字段映射关系',
              left: '10px',
              iconSetting: true,
            },
            {
              name: 'dictionarymapping',
              icon: 'icon-zidianyingshe',
              title: '字典映射',
              desc: '设置原始数据与标准数据的数据字典的映射关系',
              left: '10px',
              iconSetting: true,
            },
          ],
          connectingOptions: {
            width: '0.07rem',
            height: '0.04rem',
            top: '2.28rem',
            left: '2.47rem',
          },
        },
        {
          addVisible: false,
          top: '2.07rem',
          left: '2.59rem',
          datas: [
            {
              name: 'formawarehouse',
              icon: 'icon-linshiruku',
              title: '临时入库',
              desc: '标准转换后的数据临时 存放在系统数据库，和 当前主题绑定。',
              left: '10px',
              iconSetting: true,
            },
          ],
          connectingOptions: {
            width: '0.07rem',
            height: '0.04rem',
            top: '2.28rem',
            left: '3.64rem',
          },
        },
        {
          addVisible: true,
          top: '1.48rem',
          left: '3.76rem',
          datas: [
            {
              icon: 'icon-kongzhijiance',
              title: '空值检测',
              desc: '检测字段是否为空',
              left: '10px',
              iconSetting: true,
            },
            {
              icon: 'icon-zhongfujiance',
              title: '重复检测',
              desc: '检测字段是否重复',
              left: '10px',
              iconSetting: true,
            },
          ],
          connectingOptions: {
            width: '0.07rem',
            height: '0.04rem',
            top: '2.28rem',
            left: '4.92rem',
          },
        },
        {
          addVisible: true,
          top: '0.67rem',
          left: '5.03rem',
          datas: [
            {
              icon: 'icon-IPdizhigeshijiance',
              title: 'IP地址格式检测',
              desc: '设置原始数据与标准数据的字段映射关系',
              left: '10px',
              iconSetting: false,
            },
            {
              icon: 'icon-MACdizhigeshijiance',
              title: 'MAC地址格式检测',
              desc: '检测MAC地址格式是否正确',
              left: '10px',
              iconSetting: false,
            },
            {
              icon: 'icon-hangzhengquhuageshijiance',
              title: '行政区划格式检测',
              desc: '设置原始数据与标准数 据的字段映射关系',
              left: '10px',
              iconSetting: false,
            },
            {
              icon: 'icon-shebeibianmageshijiance',
              title: '设备编码格式检测',
              desc: '需符合《GB/T 28181 2016》中关于设备编码的规定',
              left: '10px',
              iconSetting: false,
            },
            {
              name: 'testing',
              icon: 'icon-kongjianxinxijiance',
              title: '空间信息检测',
              desc: '经纬度与地址大量重复、 经纬度越界、经纬度偏 移检测',
              left: '10px',
              iconSetting: true,
            },
          ],
          connectingOptions: {
            width: '0.07rem',
            height: '0.04rem',
            top: '2.28rem',
            left: '6.19rem',
          },
        },
        {
          addVisible: false,
          top: '2.08rem',
          left: '6.3rem',
          datas: [
            {
              icon: 'icon-shizhongxinxijianceshezhi',
              title: '时钟信息检测设置',
              desc: '检测设备时钟是否准确， 并支持重设时钟',
              left: '10px',
              iconSetting: false,
            },
          ],
          connectingOptions: {
            width: '0.25rem',
            height: '0.043rem',
            top: '2.73rem',
            left: '6.63rem',
            angle: 90,
          },
        },
        {
          addVisible: false,
          top: '2.94rem',
          left: '6.3rem',
          datas: [
            {
              icon: 'icon-zu1665',
              title: '数据输出',
              desc: '追踪查阅数据最终检测结果',
              left: '10px',
              iconSetting: false,
            },
          ],
        },
      ],
    };
  },
  created() {},
  methods: {},
  watch: {},
  components: {
    // DataInput,
    // FieldMapping,
    ComponentPackage: require('@/views/datagovernance/governancetheme/components/componentPackage.vue').default,
    AggreConnect: require('../components/aggre-connect.vue').default,
  },
};
</script>
<style lang="less" scoped>
#temporaryprocess {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  background-image: url('../../../../assets/img/thememanagement/process-bg.png');
  background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  .temporaryprocess-left {
    position: relative;
    flex: 1;
    height: 100%;
    overflow: auto;
    // border-right: 1px solid var(--border-color);
    &-optbtn {
      position: absolute;
      top: 20px;
      right: 28px;
      button:first-child {
        margin-right: 20px;
      }
    }
  }
  .temporaryprocess-right {
    width: 243px;
    height: 100%;
    background-color: var(--bg-content);
  }
}
</style>
