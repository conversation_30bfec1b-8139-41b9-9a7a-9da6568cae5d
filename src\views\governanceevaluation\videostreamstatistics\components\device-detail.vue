<template>
  <div class="device-detail-container auto-fill">
    <div class="search">
      <search-bar @search="search" :searchData="searchData" ref="searchBar">
        <template #day="{ searchData }">
          <slot name="day" :searchData="searchData"> </slot>
        </template>
      </search-bar>
    </div>
    <div class="auto-fill">
      <div class="auto-fill">
        <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
          <template #isImportant="{ row, column }">
            <span>{{ isImportant(row[column.key]) }}</span>
          </template>
          <template #phyStatus="{ row, column }">
            <span :style="{ color: row.phyStatus === 1 ? '#0E8F0E' : '#BC3C19' }">
              {{ phyStatus(row[column.key]) }}
            </span>
          </template>
        </ui-table>
      </div>
      <ui-page class="ui-page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
  </div>
</template>

<script>
import governanceevaluation from '@/config/api/governanceevaluation.js';

export default {
  name: 'device-detail',
  components: {
    UiTable: require('@/components/ui-table').default,
    SearchBar: require('@/views/governanceevaluation/videostreamstatistics/components/search-bar.vue').default,
  },
  props: {
    columns: {
      default: () => [
        { title: '序号', type: 'index', width: 70, align: 'center' },
        { title: '设备编码', key: 'deviceId', minWidth: 150, ellipsis: true, tooltip: true },
        { title: '设备名称', key: 'deviceName', minWidth: 150, ellipsis: true, tooltip: true },
        { title: '组织机构', key: 'orgName', minWidth: 150 },
        { title: '行政区划', key: 'civilName', minWidth: 100 },
        { title: '设备状态', key: 'phyStatus', slot: 'phyStatus', minWidth: 100 },
        { title: '设备类型', key: 'isImportant', slot: 'isImportant', minWidth: 100 },
        { title: '离线天数', key: 'offlineDayCount', minWidth: 80 },
        { title: '最近一次离线时间', key: 'offlineDate', minWidth: 100 },
        { title: '全部离线时间', key: 'allOfflineDate', minWidth: 100, ellipsis: true, tooltip: true },
      ],
    },
  },
  data() {
    return {
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      tableData: [],
      searchData: {
        deviceId: '',
        deviceName: '',
        orgCode: '',
        civilCode: '',
        phyStatus: '',
        isImportant: '',
        startCount: null,
        endCount: null,
      },
      tableColumns: [],
    };
  },
  computed: {},
  watch: {
    columns: {
      immediate: true,
      deep: true,
      handler: function (val) {
        this.tableColumns = val;
      },
    },
  },
  filter: {},
  created() {
    this.setDefaultParams();
    this.initList();
  },
  methods: {
    setDefaultParams() {
      let { isImportant, monthPart, startCount, endCount, indexId, civilCode } = this.$route.query;
      this.$set(this.searchData, 'indexId', Number.parseInt(indexId));
      this.$set(this.searchData, 'civilCode', civilCode);
      this.$set(this.searchData, 'isImportant', Number.parseInt(isImportant));
      this.$set(this.searchData, 'monthPart', monthPart);
      this.$set(this.searchData, 'startCount', Number.parseInt(startCount));
      this.$set(this.searchData, 'endCount', Number.parseInt(endCount));
      this.$nextTick(() => {
        this.$refs.searchBar.selectTree.regionCode = civilCode;
      });
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.initList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.initList();
    },
    search(params) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = 20;
      this.searchData = params;
      this.initList();
    },
    isImportant(val) {
      return val === 1 ? '重点设备' : '普通设备';
    },
    phyStatus(val) {
      return val === 1 ? '可用' : '不可用';
    },
    async initList() {
      try {
        this.loading = true;
        let { pageSize, pageNum } = this.pageData;
        let params = {
          ...this.searchData,
          pageNumber: pageNum,
          pageSize: pageSize,
        };
        let {
          data: { data },
        } = await this.$http.post(governanceevaluation.getEvaluationDeviceOnlineStatisticsPageList, params);
        this.tableData = data.entities;
        this.pageData.totalCount = data.total;
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.device-detail-container {
  width: 100%;
  height: 100%;
  margin-bottom: -20px;
}
</style>
