<template>
  <div class="abnormal-wrapper">
    <div class="ranking-title f-14">
      <span class="icon-font icon-buhegeyuanyinfenbutongji mr-xs"></span>
      <slot name="title">
        <span>异常原因统计</span>
      </slot>
    </div>
    <div class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: echartData }">
      <draw-echarts
        v-if="echartData.length"
        :echart-option="determinantEchart"
        :echart-style="ringStyle"
        ref="attributeChart"
        class="charts"
        :echarts-loading="echartsLoading"
      ></draw-echarts>
    </div>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import dataZoom from '@/mixins/data-zoom';
import dealWatch from '@/mixins/deal-watch';
export default {
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  mixins: [dataZoom, dealWatch],
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '230px',
      },
      determinantEchart: {},
      echartsLoading: false,
      echartData: [],
    };
  },
  mounted() {
    this.startWatch(
      '$route.query',
      () => {
        this.getGraphsInfo();
      },
      { deep: true, immediate: true },
    );
  },
  methods: {
    async getGraphsInfo() {
      const { regionCode, orgCode, statisticType, indexId, access, batchId } = this.$route.query;
      this.echartsLoading = true;
      let data = {
        indexId: indexId,
        batchId: batchId,
        access: access || 'TASK_RESULT',
        displayType: statisticType,
        orgRegionCode: statisticType === 'REGION' ? regionCode : orgCode,
      };
      try {
        let res = await this.$http.post(evaluationoverview.getGraphsInfo, data);
        this.echartData = res.data.data || [];
        this.initRing();
      } catch (err) {
        console.log(err);
      } finally {
        this.echartsLoading = false;
      }
    },
    initRing() {
      this.barData = this.echartData.map((row) => {
        return {
          propertyColumn: row.propertyName,
          value: row.count,
          deviceRate: row.proportion || '0%',
        };
      });
      let opts = {
        xAxis: this.echartData.map((row) => row.propertyName),
        data: this.barData,
      };
      this.determinantEchart = this.$util.doEcharts.overviewColumn(opts);
      if (this.echartData.length != 0) {
        setTimeout(() => {
          this.setDataZoom('attributeChart', [], 10);
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
.abnormal-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  box-shadow: var(--shadow-sub-echarts-content);
  border-right: 4px;
  .ranking-title {
    padding: 10px;
    color: var(--color-title-echarts);
    background: var(--bg-sub-echarts-title);
    .icon-font {
      color: var(--color-icon-echarts);
    }
  }

  .echarts-box {
    background: var(--bg-sub-echarts-content);
    height: calc(100% - 44.5px) !important;
    width: 100%;
    .charts {
      height: 100% !important;
    }
  }
}
</style>
