import request from "@/libs/request";
import { modelMarket, service, modelSearch } from "./Microservice";

// 同行分析
// 人员对象信息
export function personTargetPageList(data) {
  return request({
    url: modelMarket + "/trajectoryAnalysis/personTargetPageList",
    method: "post",
    data,
  });
}
// 人脸同行分析--同行人员信息查询
export function queryFaceCapturePageList(data) {
  return request({
    url: modelMarket + "/facePeerAnalysis/queryFaceCapturePageList",
    method: "post",
    data,
  });
}
//  人脸同行分析--分析统计
export function queryFacePeerCountPageList(data) {
  return request({
    url: modelMarket + "/facePeerAnalysis/queryFacePeerCountPageList",
    method: "post",
    data,
  });
}
// 获取频次分析列表
export function getFrequencyAnalysisList(data) {
  return request({
    url: modelMarket + "/face/freqAnalysis/getFrequencyAnalysisList",
    method: "post",
    data,
  });
}
// 获取频次分析的轨迹
export function getFreqAnalysisAccessLogList(data) {
  return request({
    url: modelMarket + "/face/freqAnalysis/getFreqAnalysisAccessLogList",
    method: "post",
    data,
  });
}
// 查询人脸点位信息
export function queryFaceCameraPoint(data) {
  return request({
    url: modelMarket + "/facePeerAnalysis/queryFaceCameraPoint",
    method: "post",
    data,
  });
}

// 车辆同行对象查询
export function queryVehicleList(data) {
  return request({
    url: modelMarket + "/vehiclePeerAnalysis/queryVehicleList",
    method: "post",
    data,
  });
}
// 车辆同行分析--同行人员信息查询
export function queryVehicleCapturePageList(data) {
  return request({
    url: modelMarket + "/vehiclePeerAnalysis/queryVehicleCapturePageList",
    method: "post",
    data,
  });
}
// 车辆同行分析--分析统计
export function queryVehiclePeerCountPageList(data) {
  return request({
    url: modelMarket + "/vehiclePeerAnalysis/queryVehiclePeerCountPageList",
    method: "post",
    data,
  });
}
// 查询车辆点位信息
export function queryVehicleCameraPoint(data) {
  return request({
    url: modelMarket + "/vehiclePeerAnalysis/queryVehicleCameraPoint",
    method: "post",
    data,
  });
}

// 轨迹分析
// 获取人员关联对象
export function associatedObjects(data) {
  return request({
    url: modelMarket + "/trajectoryAnalysis/associatedObjects",
    method: "post",
    data,
  });
}
/**
 *     "是否是通过身份证号码查询的【人脸的时候用于区分sourceKey是实名档还是匿名档】")
 *     private Boolean hasIdCardSearch;
 *     @ApiModelProperty("类型  1:人脸 2：车辆 3：非机动车")
 *     private Integer type;
 *
 *     @ApiModelProperty("来源 分别表示vid/plateNo/plateNo")
 *     private String sourceKey;
 *
 *     @ApiModelProperty("同行 分别表示vid/plateNo/plateNo")
 *     private String peerKey;
 *
 *     @ApiModelProperty("查询时间 1：一天（今天）  2：一周（近7天）  3：一月（近30天）  4： 自定义时间")
 *     private Integer dateType;
 *
 *     @ApiModelProperty("开始时间 yyyy-MM-dd")
 *     private String startDate;
 *
 *     @ApiModelProperty("结束时间 yyyy-MM-dd")
 *     private String endDate;
 * @param data
 * @returns {AxiosPromise}
 */
export function queryPeerMapCaptureList(data) {
  return request({
    url: modelMarket + "/nonMotorPeerAnalysis/queryPeerMapCaptureList",
    method: "post",
    data,
  });
}
//获取轨迹信息-点位上图
export function trajectoryData(data) {
  return request({
    url: modelMarket + "/trajectoryAnalysis/trajectoryData",
    method: "post",
    data,
  });
}

// 获取轨迹统计信息 日期-次数
export function trajectoryStatisticsData(data) {
  return request({
    url: modelMarket + "/trajectoryAnalysis/trajectoryStatisticsData",
    method: "post",
    data,
  });
}

// 时空碰撞
// 查询
export function querySpaceCollisionList(data) {
  return request({
    url: modelMarket + "/faceCollision/querySpaceCollisionFaceList",
    method: "post",
    data,
  });
}
// 人脸详情
export function queryFaceDetail(data) {
  return request({
    url: modelMarket + "/faceCollision/querySpaceCollisionFaceDetail",
    method: "post",
    data,
  });
}
// 车辆详情
export function queryVehicleDetail(data) {
  return request({
    url: modelMarket + "/faceCollision/querySpaceCollisionVehicleDetail",
    method: "post",
    data,
  });
}

// 档案人员同行
export function queryFacePeerCountPageListByTimeliness(data) {
  return request({
    url:
      modelMarket + "/facePeerAnalysis/queryFacePeerCountPageListByTimeliness",
    method: "post",
    data,
  });
}

// 档案非机动车同行
export function queryNonMotorPeerAnalysisPageList(data) {
  return request({
    url:
      modelMarket + "/nonMotorPeerAnalysis/queryNonMotorPeerAnalysisPageList",
    method: "post",
    data,
  });
}

// 技战法框选设备
export function deviceRegion(data) {
  return request({
    url: service + "/device/region",
    method: "post",
    data,
  });
}
// 档案车辆同行
export function queryVehiclePeerCountPageListByTimeliness(data) {
  return request({
    url:
      modelMarket +
      "/vehiclePeerAnalysis/queryVehiclePeerCountPageListByTimeliness",
    method: "post",
    data,
  });
}
// 时空碰撞分析左侧结果列表
export function collideList(data) {
  return request({
    url: modelMarket + "/vehicle/timespace/collideList",
    method: "post",
    data,
  });
}
// 时空碰撞分析地图数量结果
export function getCollideDetail(data) {
  return request({
    url: modelMarket + "/vehicle/timespace/getCollideDetail",
    method: "post",
    data,
  });
}
// 人脸1：1
export function faceCompare(data) {
  return request({
    url: modelSearch + "/faceModel/faceCompare",
    method: "post",
    data,
  });
}

// 人脸1：N
export function faceIdentity(data) {
  return request({
    url: modelSearch + "/faceModel/faceIdentity",
    method: "post",
    data,
  });
}

// 车辆
// 昼伏夜出
export function getVehicleNocturnalListWithPage(data) {
  return request({
    url: modelMarket + "/vehicle/nocturnal/getNocturnalListWithPage",
    method: "post",
    data,
  });
}

export function getProgress(data) {
  return request({
    url: modelMarket + "/common/getProgress",
    method: "post",
    data,
  });
}

export function getNocturnalTrailList(data) {
  return request({
    url: modelMarket + "/vehicle/nocturnal/getNocturnalTrailList",
    method: "post",
    data,
  });
}
// 行车轨迹

// 查询指定日期车辆的行车轨迹
export function queryVehicleTrajectory(data) {
  return request({
    url: modelMarket + "/queryVehicleTrajectory",
    method: "post",
    data,
  });
}
// 按照天分组统计指定车辆行车次数
export function vehicleTrajectoryCount(data) {
  return request({
    url: modelMarket + "/vehicleTrajectoryCount",
    method: "post",
    data,
  });
}
// 人员频繁出没
export function analysisPageList(data) {
  return request({
    url: modelMarket + "/faceFrequentAppear/analysisPageList",
    method: "post",
    data,
  });
}
// 人员频繁出没--指定vid按照时间分组统计
export function countByDate(data) {
  return request({
    url: modelMarket + "/faceFrequentAppear/countByDate",
    method: "post",
    data,
  });
}
// 查询指定日期的抓拍轨迹
export function queryFaceTrajectory(data) {
  return request({
    url: modelMarket + "/faceFrequentAppear/queryFaceTrajectory",
    method: "post",
    data,
  });
}
// 人脸频次分析--日期分组统计查询
export function queryFaceFrequenciesDateList(data) {
  return request({
    url: modelMarket + "/frequencies/queryFaceFrequenciesDateList",
    method: "post",
    data,
  });
}
// 人脸频次分析--人脸频次分析详情查询
export function queryFaceFrequenciesDetailList(data) {
  return request({
    url: modelMarket + "/frequencies/queryFaceFrequenciesDetailList",
    method: "post",
    data,
  });
}
// 人脸频次分析--融合统计
export function queryFaceFrequenciesList(data) {
  return request({
    url: modelMarket + "/frequencies/queryFaceFrequenciesList",
    method: "post",
    data,
  });
}
// 车辆频次分析
export function queryVehicleFrequenciesList(data) {
  return request({
    url: modelMarket + "/frequencies/queryVehicleFrequenciesList",
    method: "post",
    data,
  });
}
// 车辆频次分析--日期分组统计查询
export function queryVehicleFrequenciesDateList(data) {
  return request({
    url: modelMarket + "/frequencies/queryVehicleFrequenciesDateList",
    method: "post",
    data,
  });
}
// 频次分析--车辆频次分析详情查询
export function queryVehicleFrequenciesDetailList(data) {
  return request({
    url: modelMarket + "/frequencies/queryVehicleFrequenciesDetailList",
    method: "post",
    data,
  });
}

// -----------------------------------------特色战法start-------------------------------------------
// -------由案到人start------
// 警情、案件统计接口
export function caseCount(data) {
  return request({
    url: modelMarket + "/casePerson/caseCount",
    method: "post",
    data,
  });
}
// 分页查询警情案件
export function queryCasePage(data) {
  return request({
    url: modelMarket + "/casePerson/queryCasePage",
    method: "post",
    data,
  });
}
// 碰撞分析
export function queryAnalyze(data) {
  return request({
    url: modelMarket + "/casePerson/analyze",
    method: "post",
    data,
  });
}
// 获取案件模型分析任务记录详细信息
export function taskView(taskId) {
  return request({
    url: modelMarket + `/case/task/view/${taskId}`,
    method: "get",
  });
}
// 根据任务编号查询碰撞结果
export function queryCaseAnalyze(taskId) {
  return request({
    url: modelMarket + "/casePerson/queryAnalyze?taskId=" + taskId,
    method: "get",
  });
}
// 查询指定关联人员轨迹集合
export function queryAnalyzeTrajectory(data) {
  return request({
    url: modelMarket + "/casePerson/queryAnalyzeTrajectory",
    method: "post",
    data,
  });
}
// -------由案到人end--------

// -------由人到案start------
// 根据条件获取人员聚类后轨迹信息【选择人员下一步】
export function getPersonClusterTrajectoryList(data) {
  return request({
    url: modelMarket + "/personToCaseAnalysis/getPersonClusterTrajectoryList",
    method: "post",
    data,
  });
}

// 新增由人到案数据分析【选择案件警情后碰撞分析】
export function addPersonToCaseAnalysis(data) {
  return request({
    url: modelMarket + "/personToCaseAnalysis/addPersonToCaseAnalysis",
    method: "post",
    data,
  });
}

// 碰撞进度
export function getPersonToCaseAnalysisProgress(reqSeq) {
  return request({
    url:
      modelMarket +
      `/personToCaseAnalysis/getPersonToCaseAnalysisProgress/${reqSeq}`,
    method: "get",
  });
}

// 碰撞结果
export function getPersonToCaseAnalysisResult(reqSeq) {
  return request({
    url:
      modelMarket +
      `/personToCaseAnalysis/getPersonToCaseAnalysisResult/${reqSeq}`,
    method: "get",
  });
}

// 获取匹配到碰撞数据的中心点位信息
export function getPersonToCaseMatchCenterPoints(reqSeq) {
  return request({
    url:
      modelMarket +
      `/personToCaseAnalysis/getPersonToCaseMatchCenterPoints/${reqSeq}`,
    method: "get",
  });
}

// 获取人员和案件/警情的轨迹分析详情
export function getAnalysisResultDetailByPerson(data) {
  return request({
    url: modelMarket + "/personToCaseAnalysis/getAnalysisResultDetailByPerson",
    method: "post",
    data,
  });
}

// 获取案件警情轨迹
export function getPersonToCaseAnalysisResultDetail(data) {
  return request({
    url:
      modelMarket + "/personToCaseAnalysis/getPersonToCaseAnalysisResultDetail",
    method: "post",
    data,
  });
}

// 获取人员轨迹详情
export function getPersonTrajectoryDetail(data) {
  return request({
    url: modelMarket + "/personToCaseAnalysis/getPersonTrajectoryDetail",
    method: "post",
    data,
  });
}
// -------由人到案end--------
// -----------------------------------------特色战法end ---------------------------------------------

// 人员首次入城
// 创建首次入城人员任务
export function addFaceFirstEntryCityTask(data) {
  return request({
    url: modelMarket + "/face/firstEntryCity/createTask",
    method: "post",
    data,
  });
}
// 删除首次入城人员任务
export function delFaceFirstEntryCityTask(taskId) {
  return request({
    url: modelMarket + `/face/firstEntryCity/deleteTask/${taskId}`,
    method: "post",
  });
}
// 获取首次入城人员首次抓拍-根据任务id
export function getFaceFirstEntryCityTaskDetail(data) {
  return request({
    url: modelMarket + "/face/firstEntryCity/getFirstCaptureByTask",
    method: "post",
    data,
  });
}
// 获取用户任务列表
export function getFaceFirstEntryCityTaskList() {
  return request({
    url: modelMarket + "/face/firstEntryCity/getUserTaskList",
    method: "get",
  });
}
// 车辆首次入城
// 创建首次入城车辆任务
export function addFirstEntryCityTask(data) {
  return request({
    url: modelMarket + "/vehicle/firstEntryCity/createTask",
    method: "post",
    data,
  });
}
// 删除首次入城车辆任务
export function delFirstEntryCityTask(taskId) {
  return request({
    url: modelMarket + `/vehicle/firstEntryCity/deleteTask/${taskId}`,
    method: "post",
  });
}
// 获取首次入城车辆首次抓拍-根据任务id
export function getFirstEntryCityTaskDetail(data) {
  return request({
    url: modelMarket + "/vehicle/firstEntryCity/getFirstCaptureByTask",
    method: "post",
    data,
  });
}
// 获取用户任务列表
export function getFirstEntryCityTaskList() {
  return request({
    url: modelMarket + "/vehicle/firstEntryCity/getUserTaskList",
    method: "get",
  });
}
// 卖淫人员分析
// 创建卖淫人员分析任务
export function addProstituteTask(data) {
    return request({
        url: modelMarket + "/face/prostitute/createTask",
        method: "post",
        data,
    });
}
// 多维碰撞
// 人员档案详情 
export function personDetail(data) {
    return request({
        url: modelMarket + "/face/multidimensional/timespace/personDetail",
        method: "post",
        data,
    });
}
// 删除卖淫人员分析任务
export function delProstituteTask(taskId) {
    return request({
        url: modelMarket + `/face/prostitute/deleteTask/${taskId}`,
        method: "post",
    });
}
// 获取卖淫人员列表-根据任务id
export function getProstituteDetail(data) {
    return request({
        url: modelMarket + "/face/prostitute/getProstituteListByTask",
        method: "post",
        data,
    });
}
// 获取卖淫人员抓拍列表-根据任务id
export function getProstituteCaptureList(data) {
    return request({
        url: modelMarket + "/face/prostitute/getProstituteCaptureListByTask",
        method: "post",
        data,
    });
}
//碰撞轨迹   
export function collideTrail(data) {
    return request({
        url: modelMarket + "/face/multidimensional/timespace/collideTrail",
        method: "post",
        data,
    });
}
// 获取用户任务列表
export function getProstituteTaskList() {
    return request({
        url: modelMarket + "/face/prostitute/getUserTaskList",
        method: "get"
    });
}

// 疑似车辆-ETC关系绑定推荐
export function vehicleEtcBindingList(data) {
    return request({
        url: modelMarket + "/vehicle/vehicleEtcRelated/list",
        method: "post",
        data,
    });
  }
// 分析结果统计 
export function collideCountList(data) {
    return request({
        url: modelMarket + "/face/multidimensional/timespace/collideCountList",
        method: "post",
        data,
    });
}
// 疑似人-电磁关系绑定
export function vidFindBaseStationList(data) {
  return request({
    url: modelMarket + "/fitAnalysis/vidFindBaseStationList",
    method: "post",
    data,
  });
}
// 查询频次
export function vidBaseStationPointCount(data) {
  return request({
    url: modelMarket + "/fitAnalysis/vidBaseStationPointCount",
    method: "post",
    data,
  });
}
export function alongBaseStationPointList2(data) {
  return request({
    url: modelMarket + "/fitAnalysis/vidFindBaseStationAlongPointList",
    method: "post",
    data,
  });
}
/** ---------------------开车打电话------------------ */
// 开车打电话分页列表
export function drivecalllist(data) {
  return request({
    url: modelMarket + "/drivecall/list",
    method: "post",
    data,
  });
}
// 未系安全带分页列表
export function seatbeltlist(data) {
  return request({
    url: modelMarket + "/seatbelt/list",
    method: "post",
    data,
  });
}
/** 频繁夜出 */
// 频繁夜出车辆列表
export function getFrequentNightOutList(data) {
  return request({
    url: modelMarket + "/vehicle/frequentNightOut/getFrequentNightOutList",
    method: "post",
    data,
  });
}
// 获取车辆各地点夜出频次
export function getNightOutFrequency(data) {
  return request({
    url: modelMarket + "/vehicle/frequentNightOut/getNightOutFrequency",
    method: "post",
    data,
  });
}
//昼伏夜出
export function getNocturnalListWithPage(data) {
  return request({
    url: modelMarket + "/face/nocturnal/getNocturnalListWithPage",
    method: "post",
    data,
  });
}
export function getNocturnalAccessLogList(data) {
  return request({
    url: modelMarket + "/face/nocturnal/getNocturnalAccessLogList",
    method: "post",
    data,
  });
}
/** 隐匿车 */
// 获取隐匿车辆列表
export function getHiddenVehicleList(data) {
  return request({
    url: modelMarket + "/vehicle/hiddenVehicle/getHiddenVehicleList",
    method: "post",
    data,
  });
}
// 获取隐匿车辆抓拍列表
export function getVehicleCaptureList(data) {
  return request({
    url: modelMarket + "/vehicle/hiddenVehicle/getVehicleCaptureList",
    method: "post",
    data,
  });
}
// 获取隐匿车辆每日抓拍次数
export function getVehicleDailyCaptureNum(data) {
  return request({
    url: modelMarket + "/vehicle/hiddenVehicle/getVehicleDailyCaptureNum",
    method: "post",
    data,
  });
}
/** 视频战法 */
/** 全景追逃 */
// 根据区域查询设备
export function getGeometryCamera(data) {
  return request({
    url: service + "/resource/geometry/panorama/track",
    method: "post",
    data,
  });
}

/** 警卫录像 */
// 根据框选查询设备
export function getNearResource(data) {
  return request({
    url: service + "/resource/region",
    method: "post",
    data,
  });
}
// 新增
export function addGuardRoute(data) {
  return request({
    url: modelMarket + "/police/line/add",
    method: "post",
    data,
  });
}
// 删除
export function delGuardRoute(ids) {
  return request({
    url: modelMarket + `/police/line/remove/${ids}`,
    method: "delete",
  });
}
// 查询分页列表
export function guardRouteList(data) {
  return request({
    url: modelMarket + "/police/line/pageList",
    method: "post",
    data,
  });
}
// 编辑
export function updateGuardRoute(data) {
  return request({
    url: modelMarket + `/police/line/update`,
    method: "put",
    data,
  });
}
// 详细信息
export function viewGuardRoute(id) {
  return request({
    url: modelMarket + `/police/line/view/${id}`,
    method: "get",
  });
}
/** 跟车分析 */
// 伴随车辆列表
export function alongVehicleList(data) {
  return request({
    url: modelMarket + "/vehicle/along/alongVehicleList",
    method: "post",
    data,
  });
}
// 伴随车辆点位列表
export function alongPointList(data) {
  return request({
    url: modelMarket + "/vehicle/along/alongPointList",
    method: "post",
    data,
  });
}
// 根据vehiclePlate、时间查询点位数量
export function getVehiclePointCount(data) {
  return request({
    url: modelMarket + "/vehicle/along/getPointCount",
    method: "post",
    data,
  });
}
// 感知数据时空碰撞分析
// 碰撞分析左侧结果列表
export function baseCollideList(data) {
  return request({
    url: modelMarket + "/basestation/timespace/collideList",
    method: "post",
    data,
  });
}
// 查询通行记录
export function baseTrailSearch(data) {
  return request({
    url: modelMarket + "/basestation/timespace/trailSearch",
    method: "post",
    data,
  });
}
// 碰撞分析地图数量结果
export function baseCollideDetail(data) {
  return request({
    url: modelMarket + "/basestation/timespace/collideDetail",
    method: "post",
    data,
  });
}

// 车辆
// 落脚点分析
export function vehiclePointList(data) {
  return request({
    url: modelMarket + "/vehicle/stayPoint/stayPointList",
    method: "post",
    data,
  });
}

export function findVehicleCameraTreeList(data) {
  return request({
    url: modelMarket + "/vehicle/nocturnal/getNocturnalListWithPage",
    method: "post",
    data,
  });
}

export function getRegion(data) {
  return request({
    url: service + "/device/region",
    method: "post",
    data,
  });
}
// 遮阳板检测分页列表
export function sunVisorlist(data) {
  return request({
    url: modelMarket + "/sunVisor/list",
    method: "post",
    data,
  });
}

/** 车辆多目标轨迹分析 */
// 获取多辆车抓拍以及重合点位
export function getVehicleTrajectory(data) {
  return request({
    url:
      modelMarket +
      "/vehicle/multiTargetTrajectory/getMultiVehicleCaptureAndCoincidentPoint",
    method: "post",
    data,
  });
}
// 获取无牌车列表
export function getNoPlateVehicleList(data) {
  return request({
    url: modelMarket + "/vehicle/noPlate/getNoPlateVehicleList",
    method: "post",
    data,
  });
}
// 无牌车状态修改
export function setNoPlateStatus(data) {
  return request({
    url: modelMarket + "/vehicle/noPlate/handle",
    method: "post",
    data,
  });
}
// 无牌车处理状态
export function handleNoPlateStatus(data) {
  return request({
    url: modelMarket + "/vehicle/noPlate/handleStatus",
    method: "post",
    data,
  });
}
// 无牌车历史处理列表
export function handleNoPlateStatusHistoryList(data) {
  return request({
    url: modelMarket + "/vehicle/noPlate/queryVehicleHandleList",
    method: "post",
    data,
  });
}

/** 套牌分析 */
// 套牌分析列表
export function fakeVehicleList(data) {
  return request({
    url: modelMarket + "/fake/plate/list",
    method: "post",
    data,
  });
}
// 套牌车辆详情
export function fakeVehicleDetail(id) {
  return request({
    url: modelMarket + `/fake/plate/detail/${id}`,
    method: "get",
  });
}
// 套牌车处理
export function fakeVehicleHandle(data) {
  return request({
    url: modelMarket + "/fake/plate/handle",
    method: "post",
    data,
  });
}

//#region 导出
// 无牌车导出
export function exportNoPlateData(data) {
  return request({
    url: modelMarket + "/search/vehicle/noPlateVehicleDownLoad",
    method: "post",
    data,
  });
}

// 未系安全带导出
export function exportNoSeatBeltData(data) {
  return request({
    url: modelMarket + "/search/vehicle/seatBeltDownLoad",
    method: "post",
    data,
  });
}

// 夜间遮阳板导出
export function exportSunvisorData(data) {
  return request({
    url: modelMarket + "/search/vehicle/sunVisorDownLoad",
    method: "post",
    data,
  });
}

// 开车打电话导出
export function exportDrivePhoneData(data) {
  return request({
    url: modelMarket + "/search/vehicle/driverCallDownLoad",
    method: "post",
    data,
  });
}
//#endregion
