<template>
  <div class='peer-content'>
    <div class="title">
      <p>同行 <span> {{ detail.peerNum || 0}} </span> 次</p>
    </div>
    <ul class="peer-content-box">
      <li class="content-box-li" v-for="(item, index) in detail.peerDetails" :key='`${index}-${item.peerCaptureId}`'>
        <div class="box-li-icon">{{ index + 1 }}</div>
        <div class="box-li-right">
          <p class="box-li-right-title">{{ item.deviceAddress || '--' }}</p>
          <ul class="box-li-right-content">
            <li @click="peerMessage(item.source)">
              <div class="right-img-list">
                <ui-image :type='type' :src="item.source.traitImg || item.source.sceneImg" alt="" />
                <p class="right-img-list-tag tag-bule">对象</p>
              </div>
              <p class="box-li-right-time">{{ item.source.absTime || '--'}}</p>
            </li>
            <li @click="peerMessage(item.peer)">
              <div class="right-img-list">
                <!-- <ui-image :src="item.ferriteDetailVo.traitImg" viewer /> -->
                <ui-image :type='type' :src="item.peer.traitImg || item.peer.sceneImg" alt="" />
                <p class="right-img-list-tag tag-yellow">同行</p>
                <div class="plateNumber">{{ item.peer.plateNo }}</div>
              </div>
              <p class="box-li-right-time">{{ item.peer.absTime || '--'}}</p>
            </li>
          </ul>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
/**
 * 同行详情弹框
 */
import getImageCropper from '@/mixins/mixinVehicleCrop'

export default {
  name: "peer-modal",
  mixins: [getImageCropper],
  props: {
    value: {},
    data: {
      required: true
    },
    type: {
      required: true
    },
  },
  data () {
    return {
      visible: false,
      detail: {}
    }
  },
  watch: {
    data: {
      deep: true,
      immediate: true,
      handler (val) {
        this.detail = val || {}
      }
    }
  },
  methods: {
    peerMessage (item) {
      this.$emit("on-click", item)
    }
  }
}
</script>
<style scoped lang='less'>
@import "~@/views/model-market/fusion-tricks/peerAnalysis/components/style/index.less";

.peers-modal {
  /deep/ .ivu-modal-header {
    padding-left: 0 !important;
    background-color: #fff !important;
  }

  /deep/ .ivu-modal {
    top: -48px;
    left: 10px;
    margin: 0;
  }

  /deep/ .ivu-modal-body {
    height: 880px;
    overflow-y: auto;
  }
}

.peer-content {
  margin-top: 10px;
  background: #fff;
  height: calc(~"100% - 80px");
  // height: 100%;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);

  .peer-content-box {
    height: 100%;
    overflow: auto;
    position: relative;
    .content-box-li {
      margin-top: 10px;
      margin-bottom: 13px;
      display: flex;
      .box-li-icon {
        width: 24px;
        background: url("~@/assets/img/archives/mark-red.png") no-repeat;
        background-size: 24px 28px;
        display: flex;
        justify-content: center;
        height: 30px;
        color: #ea4a36;
      }
      .box-li-right {
        flex: 1;
        margin-left: 12px;
        &-title {
          font-size: 12px;
          font-weight: bold;
          color: #181818;
        }
        &-content {
          background: #f9f9f9;
          border-radius: 4px;
          padding: 10px 9px 12px;
          display: flex;
          justify-content: space-between;
          margin-top: 5px;
          li {
            display: flex;
            flex-direction: column;
            align-items: center;
          }
          .right-img-list {
            position: relative;
            width: 100px;
            margin-bottom: 10px;
            height: 100px;
            img {
              width: 100px;
              height: 100px;
              cursor: pointer;
            }
            &-tag {
              // width: 40px;
              // height: 20px;
              border-radius: 0px 0px 4px 0px;
              position: absolute;
              top: 0;
              color: #fff;
              font-size: 12px;
              padding: 1px 3px;
              z-index: 20;
            }
            .tag-bule {
              background: #2c86f8;
            }
            .tag-yellow {
              background: #f29f4c;
            }
            .plateNumber {
              position: absolute;
              bottom: 0px;
              left: 1px;
              background: rgba(0, 0, 0, 0.6);
              width: 98px;
              text-align: center;
              // opacity: 0.6;
              color: #ffffff;
              font-size: 14px;
            }
          }
        }
        .box-li-right-time {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }
  }
}
</style>
