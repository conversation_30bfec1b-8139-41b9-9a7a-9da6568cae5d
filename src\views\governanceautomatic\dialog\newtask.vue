<template>
  <ui-modal v-model="visible" :title="title" width="96rem" @query="save" :loading="loading">
    <div class="title-bar">
      <i class="icon-font icon-renwutianjia f-16 mr-10"></i>
      <span>第一步：任务基础信息</span>
    </div>
    <Form ref="formValidate" class="mt-lg" :model="formValidate" :rules="ruleValidate">
      <FormItem class="inline mr-40" label="任务名称：" prop="taskName">
        <Input class="input-width" v-model="formValidate.taskName" placeholder="请输入任务名称"></Input>
      </FormItem>
      <FormItem class="inline" label="资源类型：" prop="sourceType">
        <Select
          class="select-width"
          v-model="formValidate.sourceType"
          placeholder="请选择资源类型"
          @on-change="handleChangeSource"
        >
          <Option v-for="(item, index) in sourceTypes" :key="'source' + index" :value="item.code">{{
            item.label
          }}</Option>
          <!-- <Option value="1">人脸</Option> -->
        </Select>
      </FormItem>
    </Form>
    <div class="title-bar">
      <i class="icon-font icon-renwutianjia f-16 mr-10"></i>
      <span>第二步：治理任务配置</span>
    </div>
    <Form
      v-if="formValidate.sourceType === '0' || formValidate.sourceType === '3'"
      ref="formInfo"
      class="mt-lg"
      :model="formValidate"
      :rules="ruleValidate"
    >
      <FormItem class="inline mr-40" label="治理内容：" prop="governanceContent">
        <Select
          class="select-width inline"
          v-model="formValidate.governanceContent"
          placeholder="请选择治理内容"
          @on-change="handleChangeContent"
        >
          <Option v-for="(item, index) in governanceContents" :key="'content' + index" :value="item.code">{{
            item.label
          }}</Option>
        </Select>
      </FormItem>
      <FormItem
        v-if="formValidate.sourceType === '0' || formValidate.sourceType === '3'"
        class="inline mr-40"
        label="数据对象："
        prop="objectType"
        :rules="[
          {
            required: formValidate.objectType === '2' || formValidate.objectType === '3' ? true : false,
            trigger: 'change',
          },
        ]"
      >
        <RadioGroup
          v-model="formValidate.objectType"
          @on-change="handleRadioChange(formValidate.objectType, 'objectType')"
        >
          <Radio label="1" class="mr-25">全部设备</Radio>
          <Radio label="2" class="mr-25">设备目录</Radio>
          <Radio label="3" class="mr-25">自定义设备</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem
        v-if="formValidate.sourceType === '0' || formValidate.sourceType === '3'"
        label="治理时间："
        prop="immediately"
        class=""
        :rules="[
          {
            required: formValidate.immediately === '1' || false,
            trigger: 'change',
          },
        ]"
        :required="formValidate.immediately === '1'"
      >
        <RadioGroup
          :class="formValidate.immediately === '1' ? '' : 'select-width'"
          v-model="formValidate.immediately"
          @on-change="handleRadioChange(formValidate.immediately, 'time')"
        >
          <Radio label="0" class="mr-25">立即执行</Radio>
          <Radio label="1" class="mr-25">自定义时间</Radio>
        </RadioGroup>
        <FormItem
          class="inline"
          v-if="formValidate.immediately === '1'"
          label=""
          prop="time"
          :rules="[
            {
              required: formValidate.immediately === '1' ? true : false,
              message: '请选择治理时间',
              trigger: 'change',
              type: 'date',
            },
          ]"
        >
          <DatePicker
            v-model="formValidate.time"
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择时间"
            style="width: 100%"
            @on-change="handleChangeTime"
          ></DatePicker>
        </FormItem>

        <FormItem
          v-if="formValidate.objectType === '3'"
          class="inline ml-140"
          label=""
          prop="deviceIds"
          :rules="[
            {
              required: formValidate.objectType === '3' && selectParams.checkDeviceFlag !== '2',
              message: '请选择设备',
              trigger: 'change',
              type: 'array',
            },
          ]"
        >
          <div class="device-content inline">
            <div class="camera" @click="selectCamera">
              <span class="font-blue camera-text" v-if="totalCount || formValidate.selectDevIds.length">
                已选择{{ totalCount || formValidate.selectDevIds.length }}条设备
              </span>
              <span v-else>
                <i class="icon-font icon-xuanzeshexiangji inline font-blue f-14 mr-sm"></i>
                <span class="font-blue camera-text">请选择设备</span>
              </span>
            </div>
          </div>
        </FormItem>
        <FormItem
          v-if="formValidate.objectType === '2'"
          class="inline ml-140"
          label=""
          prop="categoryIds"
          :rules="[
            {
              required: formValidate.objectType === '2' || false,
              message: '请选择设备目录',
              trigger: 'change',
              type: 'array',
            },
          ]"
        >
          <div class="catelogwidth">
            <choose-catalogue :category-obj="categoryObj" @getDevCategoryData="getDevCategoryData"></choose-catalogue>
          </div>
        </FormItem>
      </FormItem>
      <!-- 重设字幕 -->
      <SubtitleReset
        v-if="formValidate.governanceContent === '3' || formValidate.sourceType === '3'"
        :osdSubtitles="osdSubtitles"
        ref="subtitleReset"
      />
    </Form>
    <component :is="currentComponent" :ref="currentComponent" :edit-infos="editData" @getInfos="getInfos"></component>
    <choose-device
      ref="ChooseDevice"
      :search-conditions="selectParams"
      :table-columns="tableColumns"
      :load-data="leftData"
      :selected-list="formValidate.selectDevIds"
      :check-device-flag="selectParams.checkDeviceFlag"
      needOtherSearch
      @getOrgCode="getOrgCode"
      @getDeviceIdList="getDeviceIdList"
    >
      <!-- @getOrgCode="getOrgCode"
      @handlePage="handlePage"
      @handlePageSize="handlePageSize" -->
      <template #search-header>
        <search-list
          ref="SearchList"
          :dict-data="dictData"
          :search-conditions="searchConditions"
          @search="search"
          @reset="reset"
        ></search-list>
      </template>
      <template #baseCheckStatus="{ row }"
        ><span
          class="statustag"
          :style="{
            'background-color': checktSatusBgcolor[row.baseCheckStatus],
          }"
          >{{ checktSatusText[row.baseCheckStatus] }}</span
        >
      </template>
      <template #viewCheckStatus="{ row }"
        ><span
          class="statustag"
          :style="{
            'background-color': checktSatusBgcolor[row.viewCheckStatus],
          }"
          >{{ checktSatusText[row.viewCheckStatus] }}</span
        >
      </template>
      <template #videoCheckStatus="{ row }"
        ><span
          class="statustag"
          :style="{
            'background-color': checktSatusBgcolor[row.videoCheckStatus],
          }"
          >{{ checktSatusText[row.videoCheckStatus] }}</span
        >
      </template>
    </choose-device>
  </ui-modal>
</template>
<script>
import governanceautomatic from '@/config/api/governanceautomatic';
import user from '@/config/api/user';
import equipmentassets from '@/config/api/equipmentassets';
import task from '.././task';
import { mapGetters } from 'vuex';
export default {
  props: {
    isEdit: {
      type: Boolean,
      default: false,
    },
    sourceTypes: {
      type: Array,
      default: () => [],
    },
    governanceContent: {
      type: String,
      default: null,
    },
    sourceType: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      osdSubtitles: {}, // osd字幕设置
      visible: false,
      title: '新增检测任务',
      formValidate: {
        taskName: '',
        sourceType: '0',
        governanceContent: '1',
        objectType: '1',
        immediately: '0',
        time: '',
        businessType: '1', // 视图基础数据 (业务数据类型)
        selectDevIds: [],
        categoryIds: [],
        deviceIds: [],
      },
      ruleValidate: {
        taskName: [
          {
            required: true,
            message: '请输入任务名称',
            trigger: 'blur',
          },
        ],
        sourceType: [
          {
            required: true,
            message: '请选择检测对象',
            trigger: 'change',
          },
        ],
        governanceContent: [
          {
            required: true,
            message: '请选择治理内容',
            trigger: 'change',
          },
        ],
      },
      typeResource: [],
      editData: {},
      taskId: '',
      governanceContents: [],
      formdata: {},
      governanceTime: '',
      infos: {},
      dictData: {},
      currentComponent: null,
      isAll: false,
      isExclude: false,
      tableColumns: [],
      leftData: (parameter, getTotal) => {
        const conditons = getTotal
          ? parameter
          : Object.assign(this.searchConditions, parameter, {
              queryOrg: 1, //查询组织机构下级 （1 不查询）可以让查询设备速度更快
            });
        let params = this.$util.common.deepCopy(conditons);
        return this.$http.post(equipmentassets.queryDeviceInfoPageList, params).then((res) => {
          if (res.data.data.entities.length) {
            res.data.data.entities.forEach((item) => {
              switch (item.checkStatus) {
                case '1000':
                  item.checkStatus = '0';
                  item.checkStatusText = '待检测';
                  break;
                case '0000':
                  item.checkStatus = '1';
                  item.checkStatusText = '合格';
                  break;
                default:
                  item.checkStatus = '2';
                  item.checkStatusText = '不合格';
                  break;
              }
            });
          }
          return res.data;
        });
      },
      searchConditions: {
        orgCodeList: [],
        sbdwlxList: [], // 摄像机点位类型
        sbgnlxList: [], // 功能类型
        deviceId: '', // 设备编码
        deviceName: '', // 设备名称
        cascadeReportStatus: '', // 上报状态
        tagIds: [], // 标签筛选
        isCheck: '', // 是否检测
        baseCheckStatus: '', // 基础信息异常状态
        baseErrorMessageList: [], // 基础信息异常原因
        videoCheckStatus: '', // 视频流信息异常状态
        videoErrorMessageList: [], // 视频流信息异常原因
        viewCheckStatus: '', // 视图信息异常状态
        viewErrorMessageList: [], // 视图信息异常原因
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      loading: false,
      selectParams: {},
      totalCount: 0,
      // checktSatusBgcolor: [var(--color-warning), 'var(--color-success)', 'var(--color-failed)'],
      checktSatusBgcolor: ['var(--color-success)', 'var(--color-failed)'],
      checktSatusText: ['合格', '不合格'],
      errorMessageList: [],
      categoryObj: {},
      statusObj: {},
    };
  },
  created() {
    this.typeResource = task.typeResource;
    this.tableColumns = task.tableColumns;
  },
  methods: {
    async init(id) {
      this.selectParams = {};
      this.osdSubtitles = {};
      this.categoryObj = {};
      this.totalCount = 0;
      // 先清空 数据
      this.resetAllData();
      this.$nextTick(async () => {
        if (this.isEdit) {
          this.title = '编辑治理任务';
          this.taskId = id;
          await this.getTaskView();
          this.formValidate.time = new Date(this.editData.governanceTime) || '';
        } else {
          this.title = '新增治理任务';
          this.editData = {
            governanceContent: this.governanceContent,
            sourceType: this.sourceType,
          };
          this.formValidate.selectDevIds = [];
          this.formValidate.time = '';
        }
        this.formValidate.taskName = this.editData.taskName || '';
        this.formValidate.sourceType = this.editData.sourceType || '0';
        await this.getGovernanceContent();
        this.formValidate.governanceContent = this.editData.governanceContent || '1';
        switch (this.formValidate.governanceContent) {
          case '2':
            this.currentComponent = 'governinfo';
            break;
          case '4':
            this.currentComponent = 'functional';
            break;
          case '5':
            this.currentComponent = 'point';
            break;
          case '6':
            this.currentComponent = 'macGovern';
            break;
          case '7':
            this.currentComponent = 'deviceStatus';
            break;
          default:
            this.currentComponent = null;
            break;
        }
        this.formValidate.immediately = this.editData.executeType == 1 ? '0' : '1';
        this.governanceTime = this.editData.governanceTime || '';
        this.formValidate.objectType = this.formValidate.objectType || '1';
      });
      this.visible = true;
    },
    handleReset() {
      // this.$refs.SearchList.reset() // 清空选择设备搜索条件
      this.resetAllData();
      this.visible = false;
    },
    resetAllData() {
      this.$refs['formValidate'].resetFields();
      this.$refs['formInfo'].resetFields();
      this.$refs['subtitleReset']?.clearData();
      this.currentComponent = null;
      this.formValidate.selectDevIds = [];
      this.formValidate.deviceIds = [];
    },
    close() {
      this.handleReset();
      this.$emit('tableRender');
    },
    async save() {
      const valid = await this.$refs['formValidate'].validate((valid) => valid);
      let valid1 = true;
      if (this.formValidate.sourceType === '0' || this.formValidate.sourceType === '3') {
        valid1 = await this.$refs['formInfo'].validate((valid) => valid);
      }
      if (!valid || !valid1) {
        this.$Message.error('请将信息填写完整！');
        return false;
      }
      let params = {};
      let extensionData = {
        checkDeviceFlag: this.selectParams.checkDeviceFlag,
        objectType: this.formValidate.objectType, // 1-全部设备 2-按目录选择 3- 按设备选择
        totalCount: this.totalCount,
      }; // 选择设备列表筛选条件
      let filter = {};
      if (this.formValidate.objectType === '2') {
        filter = {
          directoryIds:
            !!this.categoryObj.checkIds && this.categoryObj.checkIds.length ? this.categoryObj.checkIds : [0], // 目录id
          customAreaNames: this.categoryObj.customAreaNames || [], // 目录中文名(前端转化使用)
        };
      } else if (this.formValidate.objectType === '3') {
        // 点位类型、功能类型以及组织机构后端需要数组[]
        const orgs = this.getInitialOrgList.map((item) => item.orgCode);
        const orgCodeList = this.searchConditions.orgCodeList ? this.searchConditions.orgCodeList : orgs;
        if (this.selectParams.searchCustomData) {
          filter = {
            ...this.selectParams.searchCustomData,
            orgCodeList: orgCodeList,
            ids: this.formValidate.selectDevIds, // 选择设备自增id
          };
        } else {
          filter = {
            ...this.searchConditions,
            orgCodeList: orgCodeList,
            ids: this.formValidate.selectDevIds, // 选择设备自增id
          };
        }
      }
      extensionData = Object.assign(filter, extensionData);
      params = {
        taskName: this.formValidate.taskName, // 任务名称
        sourceType: this.formValidate.sourceType, // 资源类型
        governanceContent: this.formValidate.governanceContent, // 治理内容
        governanceTime:
          this.formValidate.immediately === '1' ? this.governanceTime : this.$util.common.formatDate(new Date()), // 治理时间
        executeType: this.formValidate.immediately === '1' ? 2 : 1, // 执行类型
      };
      // 功能类型'4' 点位类型'5'
      if (['2', '4', '5', '6', '7'].includes(this.formValidate.governanceContent)) {
        params = Object.assign(params, this.infos);
      }
      if (['4', '5', '7'].includes(this.formValidate.governanceContent)) {
        let successPass = true;
        this.$refs[this.currentComponent].$refs['formValidateRef'].validate((valid) => {
          if (!valid) {
            successPass = false;
          }
        });
        if (!successPass) return;
      }
      if (this.isEdit) {
        params.id = this.editData.id;
      }
      // 重设字幕保存
      if (this.formValidate.governanceContent === '3' || this.formValidate.sourceType === '3') {
        if (this.$refs.subtitleReset.validate('formData') === 'error') {
          return false;
        }
        // 字幕设置参数
        extensionData.osdSubtitles = {
          textOsd: this.$refs.subtitleReset.saveObj().textOsd,
          nameOsd: this.$refs.subtitleReset.saveObj().nameOsd,
          addOsd: this.$refs.subtitleReset.saveObj().addOsd,
        };
      }
      params.extensionData = JSON.stringify(extensionData);
      this.updateTask(params);
    },
    async updateTask(params) {
      this.loading = true;
      try {
        await this.$http.post(governanceautomatic.edit, params);
        this.$Message.success('任务更新成功！');
        this.$emit('tableRender');
        this.handleReset();
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    handleChangeSource() {
      switch (this.formValidate.sourceType) {
        case '0':
          this.getGovernanceContent();
          this.currentComponent = null;
          // this.$refs.determineInfo.init(this.formValidate, this.editData, this.isEdit)
          break;
        // case '1':
        //   this.currentComponent = 'faceconfig'
        //   this.$nextTick(() => {
        //     this.$refs.faceconfig.init()
        //   })
        //   break
        // case '2':
        //   this.$refs.determineInfo.init()
        //   break
        case '3':
          this.getGovernanceContent();
          this.formValidate.governanceContent = '3';
          this.currentComponent = null;
          // this.$refs.determineInfo.init()
          break;
        // case '4':
        //   this.$refs.determineInfo.init()
        //   break
      }
      this.osdSubtitles = {};
    },
    handleChangeContent() {
      if (this.formValidate.governanceContent === '2') {
        this.currentComponent = 'governinfo';
      } else if (this.formValidate.governanceContent === '4') {
        this.currentComponent = 'functional';
      } else if (this.formValidate.governanceContent === '5') {
        this.currentComponent = 'point';
      } else if (this.formValidate.governanceContent === '6') {
        this.currentComponent = 'macGovern';
      } else if (this.formValidate.governanceContent === '7') {
        this.currentComponent = 'deviceStatus';
      } else {
        this.currentComponent = null;
      }
    },
    async getTaskView() {
      try {
        let res = await this.$http.get(governanceautomatic.getView, {
          params: { taskId: this.taskId },
        });
        this.editData = res.data.data;
        const extensionData = JSON.parse(this.editData.extensionData);
        this.selectParams = JSON.parse(this.editData.extensionData);
        this.totalCount = extensionData.totalCount;
        this.formValidate.objectType = extensionData.objectType;
        if (extensionData.objectType === '2') {
          // 设备目录
          this.formValidate.categoryIds = extensionData.directoryIds;
          this.categoryObj.directoryIds = extensionData.directoryIds;
          this.categoryObj.customAreaNames = extensionData.customAreaNames;
        } else if (extensionData.objectType === '3') {
          // 设备
          this.formValidate.categoryIds = [];
          this.formValidate.deviceIds = extensionData.ids;
          this.formValidate.selectDevIds = extensionData.ids;
          Object.keys(extensionData).forEach((key) => {
            if (this.searchConditions.hasOwnProperty(key)) {
              this.searchConditions[key] = extensionData[key];
            }
          });
        }
        this.osdSubtitles = extensionData.osdSubtitles || {};
      } catch (error) {
        console.log(error);
      }
    },
    handleRadioChange(val, name) {
      this.$nextTick(() => {
        if (name === 'objectType') {
          switch (val) {
            case '2':
              this.$refs.formInfo.validateField('categoryIds');
              break;
            case '3':
              this.$refs.formInfo.validateField('deviceIds');
              break;
          }
        } else {
          this.$refs.formInfo.validateField(name);
        }
      });
    },
    async selectCamera() {
      await this.getDictData();
      await this.handleStatusDict();
      await this.$refs.ChooseDevice.init();
    },
    // 根据资源类型获取治理内容
    async getGovernanceContent() {
      try {
        let res = await this.$http.get(governanceautomatic.getGovernanceContentBysourceType, {
          params: { sourceType: this.formValidate.sourceType },
        });
        this.governanceContents = res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    handleChangeTime(date) {
      this.governanceTime = date;
    },
    async getDictData() {
      try {
        // const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status', 'error_category' ]
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    getInfos(data) {
      this.infos = data;
    },
    dealSearchConditions(data) {
      this.searchConditions = Object.assign(this.searchConditions, data);
    },
    search(data) {
      this.dealSearchConditions(data);
      this.$refs.ChooseDevice.search();
    },
    reset(data) {
      this.dealSearchConditions(data);
    },
    getDeviceIdList(data) {
      this.formValidate.deviceIds = JSON.parse(JSON.stringify(data.chooseIds));
      this.formValidate.selectDevIds = data.chooseIds;
      this.selectParams = data;
      this.totalCount = data.selectedDevNum;
      this.$nextTick(() => {
        this.$refs.formInfo.validateField('deviceIds');
      });
    },
    getDevCategoryData(obj) {
      this.categoryObj = obj;
      this.formValidate.categoryIds = obj.categoryIds;
      this.$refs.formInfo.validateField('categoryIds');
    },
    getOrgCode(orgCodeList) {
      this.searchConditions.orgCodeList = orgCodeList;
      this.$refs.SearchList.reset();
    },
    handleStatusDict() {
      this.statusObj = {};
      this.dictData['check_status'].forEach((item) => {
        this.statusObj[item.dataKey] = item.dataValue;
      });
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      getInitialOrgList: 'common/getInitialOrgList',
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
  watch: {},
  components: {
    // determineInfo: require('./determine-info.vue').default,
    // faceconfig: require('./faceconfig.vue').default,
    governinfo: require('../components/governinfo.vue').default,
    functional: require('../components/functional.vue').default,
    point: require('../components/point.vue').default,
    macGovern: require('../components/mac-govern.vue').default,
    ChooseDevice: require('@/components/choose-device/choose-device.vue').default,
    SearchList: require('../components/search-list.vue').default,
    ChooseCatalogue: require('@/business-components/choose-catalogue.vue').default,
    SubtitleReset: require('./subtitle-reset.vue').default,
    deviceStatus: require('../components/device-status.vue').default,
  },
};
</script>
<style lang="less" scoped>
.title-bar {
  display: flex;
  align-items: center;
  padding: 11px 0;
  border-bottom: 1px dashed var(--border-modal-footer);
  i {
    font-size: 16px;
    background: var(--color-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  span {
    font-size: 16px;
    font-weight: bold;
    color: var(--color-display-title);
  }
}
.mr-10 {
  margin-right: 10px;
}
.mr-25 {
  margin-right: 25px;
}
.mr-40 {
  margin-right: 40px;
}
.ml-140 {
  margin-left: 140px;
}
.device-content {
  display: flex;
  justify-content: center;
  width: 230px;
  // margin: 15px 0;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.camera {
  width: 230px;
  margin: 0 auto;
  padding: 0;
  height: 34px;
  line-height: 32px;
  background: rgba(43, 132, 226, 0.1);
  border: 1px dashed var(--color-primary);
  &:hover {
    background: rgba(43, 132, 226, 0.2);
    border: 1px dashed #3c90e9;
  }
  .icon-xuanzeshexiangji {
    line-height: 30px;
  }
}
.input-width {
  width: 429px;
}
.select-width {
  width: 429px;
}
.statustag {
  .flex;
  width: 56px;
  height: 19px;
  font-size: 14px;
  color: #ffffff;
  border-radius: 4px;
}
@{_deep} .ivu {
  &-modal-body {
    padding: 10px 50px 30px;
  }
  &-form-item {
    //margin-bottom: 20px;
  }
}
.catelogwidth {
  width: 740px;
}
</style>
