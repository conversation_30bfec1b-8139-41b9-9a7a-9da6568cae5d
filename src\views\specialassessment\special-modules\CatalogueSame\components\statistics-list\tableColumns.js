import { renderHeaderStatistics } from '@/views/specialassessment/utils/menuConfig.js';

const tableColumns = (params) => {
  return {
    FACE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '视图库人脸卡口总量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '有图片但设备未上报数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '目录一致率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
    VEHICLE_CATALOGUE_SAME: [
      {
        title: '序号',
        type: 'index',
        align: 'center',
        width: 50,
      },
      {
        title: '行政区划',
        key: 'civilName',
        slot: 'civilName',
        align: 'left',
        tooltip: true,
        minWidth: 200,
        renderHeader: (h, { column, index }) => {
          return renderHeaderStatistics(h, { column, index, params });
        },
      },
      {
        title: '视图库车辆卡口总量',
        key: 'detection',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '有图片但设备未上报数量',
        key: 'unqualified',
        slot: 'unqualified',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
      {
        title: '目录一致率',
        key: 'rate',
        slot: 'rate',
        align: 'left',
        tooltip: true,
        sortable: 'custom',
        minWidth: 100,
      },
    ],
  };
};
export { tableColumns };
