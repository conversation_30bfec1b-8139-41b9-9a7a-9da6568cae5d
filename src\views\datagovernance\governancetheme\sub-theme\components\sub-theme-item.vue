<template>
  <div class="sub-theme-item">
    <div class="sub-theme-item-content">
      <div class="sub-theme-item-content-logoIcon">
        <i :class="['icon-font', themeData.isDefault === '1' ? 'icon-zhengshiruku-01' : 'icon-linshiruku-01']"></i>
      </div>
      <div class="sub-theme-item-content-title">{{ themeData.topicName }}</div>
      <div class="sub-theme-item-content-tag">
        <span>{{ themeData.isDefault === '1' ? '正式主题' : '临时主题' }}</span>
      </div>
      <ul class="sub-theme-item-content-list">
        <li>
          <span>数据总量：</span>
          <span style="color: #12b8d5">{{ themeData.topicStatistics.accessDataCount }}</span>
        </li>
        <li>
          <span>合格数据：</span>
          <span style="color: #0f9015">{{ themeData.topicStatistics.checkQualifiedCount }}</span>
        </li>
        <li>
          <span>治理优化：</span>
          <span style="color: #0f9015">{{ themeData.topicStatistics.governanceOptimizationCount }}</span>
        </li>
        <li>
          <span>异常数据：</span>
          <span style="color: #bc3c19">{{ themeData.topicStatistics.existingExceptionCount }}</span>
        </li>
      </ul>
      <!-- <ul class="sub-theme-item-content-list">
        <li class="colorlist">
          <span>创建人：</span>
          <span>{{ themeData.creator }}</span>
        </li>
        <li class="colorlist" v-if="themeData.isDefault !== '1'">
          <span>创建时间：</span>
          <span>{{ themeData.createTime }}</span>
        </li>
      </ul> -->
    </div>
    <div class="sub-theme-item-footer">
      <Poptip trigger="hover" content="编辑" placement="top">
        <i v-if="themeData.isDefault == '0'" class="icon-font icon-bianji" @click="edit(themeData)"></i>
      </Poptip>
      <create-tabs
        :componentName="themeData.isDefault === '1' ? 'viewprocess' : 'temporaryprocess'"
        :tabs-query="{ id: themeData.id }"
        :tabs-text="themeData.topicName + '数据配置'"
        @selectModule="selectModule"
      >
        <Poptip trigger="hover" content="主题配置" placement="top">
          <i class="icon-font icon-peizhi-1"></i>
        </Poptip>
      </create-tabs>
      <Poptip trigger="hover" content="任务追踪" placement="top">
        <i class="icon-font icon-renwuzhuizong" @click="task(themeData.id, 0, themeData.isDefault)"></i>
      </Poptip>
      <!-- <create-tabs
        componentName="tasktracking"
        :tabs-query="{
          id: themeData.id,
          taskTab: 0,
          isDefault: themeData.isDefault,
        }"
        tabs-text="任务追踪"
        @selectModule="selectModule"
      >
        
      </create-tabs> -->
      <!-- <i
        v-if="themeData.isDefault == '0'"
        class="icon-font icon-shanchu2"
        @click="remove(themeData)"
      ></i> -->
    </div>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
export default {
  props: {
    themeData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  created() {},
  methods: {
    selectModule(name) {
      this.$emit('selectModule', name);
    },
    // 删除
    async remove(data) {
      try {
        let params = [data.id];
        let res = await this.$http.post(governancetheme.removeSubtheme, params);
        if (res.data.code == '200') {
          this.$Message['success']({
            background: true,
            content: '删除成功！',
          });
          this.$emit('render');
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 编辑
    edit(data) {
      this.$emit('update', data);
    },
    task(id, taskTab, isDefault) {
      this.$router.push({
        name: 'tasktracking',
        query: { id, taskTab, isDefault },
      });
    },
  },
  watch: {},
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>
<style lang="less" scoped>
.flex {
  display: flex;
  align-items: center;
  justify-content: center;
}
.align-flex {
  display: flex;
  align-items: center;
}
.sub-theme-item {
  flex: 0 0 32%;
  margin: 0 10px 10px;
  background: #0f2f59;
  // border: 1px solid #2064af;
  border-radius: 4px;
  &-content {
    position: relative;
    padding: 18px 20px 0;
    height: 195px;
    &-logoIcon {
      position: absolute;
      top: 10px;
      right: 10px;
      i {
        font-size: 119px;
        color: #0084f6;
        opacity: 0.05;
        line-height: 119px;
        cursor: text;
      }
    }
    &-title {
      font-size: 16px;
      font-weight: bold;
      color: var(--color-primary);
    }
    &-tag {
      margin: 16px 0 11px;
      span {
        .flex;
        width: 76px;
        height: 29px;
        font-size: 14px;
        color: #ffffff;
        background: #0c4887;
        border: 1px solid var(--color-primary);
        border-radius: 16px;
      }
    }
    &-list {
      display: flex;
      flex-wrap: wrap;
      li {
        width: 50%;
        margin-bottom: 12px;
        font-size: 14px;
        span:first-child {
          color: #ffffff;
        }
      }
      .colorlist {
        span {
          color: #ffffff80 !important;
        }
      }
    }
  }
  &-footer {
    .align-flex;
    justify-content: flex-end;
    height: 40px;
    padding: 20px 10px;
    border-top: 1px solid #0d4a81;
    i {
      margin: 0 7px;
      font-size: 16px;
      color: #56789c;
      &:hover {
        color: var(--color-primary);
      }
      &:active {
        color: #4e9ef2;
      }
    }
  }
  @{_deep} .ivu-tooltip {
    &-inner {
      color: #ffffff;
      background: #0f2f59;
      border: 1px solid #0d4a81 !important;
    }
    &-popper[x-placement^='top'] .ivu-tooltip-arrow {
      border-top-color: #0d4a81;
      &:after {
        border-left-color: #0d3560 !important;
      }
    }
  }
}
</style>
