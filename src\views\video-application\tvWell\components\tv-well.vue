<template>
  <div class="tv-well-content">
    <div class="top">
      <div class="top-l">
        <i class="iconfont color-bule icon-liufenping"></i>
        <span class="label">选择电视墙</span>
        <Select size="small" v-model="currentWallId" @on-change="changeTvwall($event)" :style="{width: '150px'}">
          <Option :value="item.id" v-for="(item, index) in tvwallOptions" :key="index">{{ item.name }}</Option>
        </Select>
        <span class="tip">共{{currentWall.screenCount}}个监视器</span>
      </div>
      <div class="inspect" v-if="isInspecting">
        <div class="inspect-l">
          <div class="item">
            <div class="label">轮巡窗口选择:</div>
            <div class="value">
              <Checkbox v-model="inspectObj.allChecked" @on-change="selectAll">全选</Checkbox>
            </div>
          </div>
          <div class="item">
            已选{{inspectObj.checkedIndexs.length}}个
          </div>
          <div class="item">
            <div class="label">间隔时间:</div>
            <div class="value">
              <Input v-model="inspectObj.inspectTime" size="small">
                <template #suffix>秒</template>
              </Input>
            </div>
          </div>
        </div>
        <div class="inspect-r">
          <Button class="mr-10" size="small" type="primary" v-if="inspectStatus != 'starting'" @click="startInspect">启动轮巡</Button>
          <Button class="mr-10" size="small" @click="cancleInspect">取消轮巡</Button>
        </div>
      </div>
      <div class="top-r">
        <Button class="mr-10" size="small" type="primary" @click="closeAll">全部下墙</Button>
        <Button size="small" v-if="showPlan" @click="addToPlan">保存为上墙预案</Button>
      </div>
    </div>
    <div class="bottom" :style="{'grid-template-columns': `repeat(${currentWall.columns}, ${100/currentWall.columns}%)`, 'grid-template-rows': `repeat(${currentWall.rows}, ${100/currentWall.rows}%)`}">
      <div class="monitorItem" v-for="(item, index) in layoutList" :key="index" :class="focusIndex == index ? 'focused' : ''" @click="changeFocus(item, index)" @drop="dropHandler($event, index)" @dragover.prevent>
        <template v-if="item.screenId">
          <div class="title">
            <span>{{ item.name }} - {{item.screenId}}</span>
            <div>
              <i class="iconfont icon-jiesuo" v-if="!item.lockInfo || !item.lockInfo.lockStatus" title="锁定" @click.stop="handleLock(item)"></i>
              <i class="iconfont icon-suoding" v-else title="解锁" @click.stop="handleUnlock(item)"></i>
              <Checkbox v-if="isInspecting" :value="inspectObj.checkedIndexs.includes(index)" @on-change="checkboxChange($event, index)"></Checkbox>
            </div>
          </div>
          <div class="content" v-if="item.deviceName">
            <i class="iconfont icon-close" @click="closeItem(item)"></i>
            <div class="deviceName">{{item.deviceName}}</div>
          </div>
          <div class="content" v-else></div>
        </template>
        <template v-else>
          <div class="title"></div>
          <div class="content noscreen"></div>
        </template>
      </div>
    </div>

    <addToPlan ref="addToPlan" @addPlan="addPlan"></addToPlan>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import addToPlan from './add-to-plan.vue'
import { queryVideoWallList, queryDemodifier, planPlay, planStop, addPlan, fetchLive, stopLive, getWallLockInfo, lockWall, unlockWall, getRtspPvg } from '@/api/player'
export default {
  name: 'tv-well',
  components: {
    addToPlan
  },
  props: {
    showPlan: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      playingDeviceIds: [],
      currentWallId: '',
      tvwallOptions: [],
      currentWall: {
        columns: 0,
        rows: 0,
        screenCount: 0
      },
      layoutList: [],
      focusIndex: -1,
      isAutoNext: false,
      isInspecting: false,
      inspectObj: {
        inspectTime: 10,
        allChecked: false,
        checkedIndexs: []
      },
      inspectStatus: '',
    }
  },
  computed: {
    ...mapGetters({
      userInfo: "userInfo",
      ignoreDevices: "player/getIgnoreDevices"
    }),
    enableIndexs() {
      let indexs = []
      this.layoutList.forEach((item, index) => {
        if (item.screenId) indexs.push(index)
      })
      return indexs
    },
  },
  watch: {
    playingDeviceIds(val) {
      this.$emit('playingDeviceIds', val)
    }
  },
  mounted() {
    this.getVideoWallList()
  },
  activated() {
    this.currentWallId = ""
    this.playingDeviceIds = []
    this.getVideoWallList()
  },
  deactivated() {
    this.cancleInspect()
  },
  methods: {
    getVideoWallList() {
      queryVideoWallList({pageNumber:1, pageSize: 9999}).then(res => {
        this.tvwallOptions = res.data.entities
      })
    },
    handleNodeClick(item, index) {
      if (!this.layoutList.length) {
        this.$Message.error("请先选择电视墙！")
        return
      }
      this.focusIndex = index || index == 0 ? index : this.getAutoNextIndex()
      let current = this.layoutList[this.focusIndex]
      const lockStatus = current.lockInfo ? !!current.lockInfo.lockStatus : false
			const isLocked = lockStatus ? (Number(this.userInfo.shortNumber) < current.lockInfo.userLevel ? true : false) : false
      if (isLocked) {
        this.$Message.error(`电视墙窗口: ${current.screenId}已被锁定`)
        return
      }
      if (this.ignoreDevices.includes(item.deviceId)) {
        this.$Message.error("该点位已被屏蔽，暂时无法查看！")
        return
      }
      if (item.playType == 'live') {
        this.playByDevice(item)
      } else {
        // this.$refs.H5Player.openVideoSearch(item, true)
      }
    },
    async playByDevice(deviceItem) {
      let current = this.layoutList[this.focusIndex]
      if (current.deviceId) {
        this.playingDeviceIds = this.playingDeviceIds.filter(v => v != current.deviceId)
        // 停流
        tvWellRtsp == 'lwpt' && this.stopStreamUrl(current.videoUrl)
      }
      let gbId = deviceItem.deviceGbId ? deviceItem.deviceGbId : deviceItem.deviceId
      let videoUrl = ''
      if (tvWellRtsp == 'lwpt') {
        let result = await fetchLive({channel: gbId, device: lwptDevice ? lwptDevice : gbId, substream: false})
        videoUrl = result['RTSP']
      } else {
        let result = await getRtspPvg(gbId)
        if (!result['data']) {
          this.$Message.error('未获取到RTSP流地址')
          return
        }
        videoUrl = result['data']
      }
      planPlay({"demodifierId": current.demodifierId,"displayNo": current.displayNo,"url": videoUrl, "gbId": gbId}).then(res => {
        if (res.code == 200) {
          this.$set(current, 'deviceName', deviceItem.deviceName)
          this.$set(current, 'deviceId', deviceItem.deviceId)
          this.$set(current, 'videoUrl', videoUrl)
          this.playingDeviceIds.push(deviceItem.deviceId)
        }
      })
    },
    stopStreamUrl(videoUrl) {
      if (!videoUrl || tvWellRtsp !== 'lwpt') return
      const id = videoUrl.split("?id=")[1]
      const startIndex = videoUrl.lastIndexOf('/')
      const endIndex = videoUrl.indexOf('?')
      const gbId = videoUrl.substring(startIndex+1, endIndex)
      const streamid = `live/${gbId}/${id}`
      stopLive({streamid: streamid})
    },
    // 自动切换下一窗口
    getAutoNextIndex() {
      if (this.focusIndex == -1) {
        if (!this.enableIndexs.length) {
          this.$Message.error('暂无可用窗口')
          return
        } 
        this.focusIndex = this.enableIndexs[0]
      }else if (this.isAutoNext) {
        let i = this.enableIndexs.findIndex(v => v == this.focusIndex)
        let ni = i+1 > this.enableIndexs.length-1 ? 0 : i+1
        this.focusIndex = this.enableIndexs[ni]
      }
      this.isAutoNext = true
      return this.focusIndex
    },
    changeTvwall(val) {
      this.currentWallId = val
      return new Promise((resolve) => {
        if (this.isInspecting) {
          this.cancleInspect()
        }
        this.playingDeviceIds = []
        if (val) {
          let item = this.tvwallOptions.find(v => v.id == val)
          if (!item) {
            this.$Message.error("暂无该电视墙权限")
            return
          }
          this.focusIndex = -1
          this.isAutoNext = false
          this.currentWall.id = item.id
          this.currentWall.columns = item.columnNum || 0
          this.currentWall.rows = item.rowNum || 0
          this.getWallDetail(item.id).then(res => {
            resolve()
          })
        } else {
          this.focusIndex = -1
          this.isAutoNext = false
          this.currentWall = {
            columns: 0,
            rows: 0,
            screenCount: 0
          }
          this.layoutList = []
          resolve()
        }
      })
    },
    getWallDetail(id) {
      return new Promise(resolve => {
        queryDemodifier({videoWallId: id}).then(async res => {
          if (res.data.length) {
            this.currentWall.screenCount = res.data.reduce((pre, cur) => pre + cur.screenCount, 0)
            let list = this.handleCell()
            let layoutBox = []
            res.data.forEach(item => {
              item.screens.forEach(ite =>{
                  layoutBox.push(
                      {   
                          name: item.name,
                          demodifierId: item.id,
                          ...ite
                      },
                  )
              })
            })

            list.forEach(item => {
              layoutBox.forEach(i => {
                if(item.location == i.location){
                    item.demodifierId = i.demodifierId;
                    item.screenId = i.id;
                    item.displayNo = i.displayNo;
                    item.name = i.name;
                }
              })
            })

            for (const item of list) {
              let res = await getWallLockInfo({
                demodifierId: item.demodifierId,
                screenId: item.screenId
              })
              this.$set(item, 'lockInfo', res.data ? {lockStatus: res.data.lockStatus, userLevel: res.data.userLevel} : null)
            }
            this.layoutList = list
          } else {
            this.$Message.error('无该电视墙')
          }
          resolve()
        })
      })
    },
    handleCell() {
      let list = []
      for(let r = 0; r < this.currentWall.rows; r++) {
        for(let c = 0; c < this.currentWall.columns; c++) {
            list.push({ 'row': r+1, 'col': c+1, location: (r+1) +'-'+ (c+1) })
        }
      }
      return list
    },
    changeFocus(item, index) {
      if (item.screenId) {
        this.focusIndex = index
        this.isAutoNext = false
        this.$emit('changeFocus', index)
      }
    },
    closeItem(item) {
      // 停流
      this.stopStreamUrl(item.videoUrl)
      planStop({"demodifierId": item.demodifierId,"displayNo": item.displayNo}).then(res => {
        if (res.code == 200) {
          this.playingDeviceIds = this.playingDeviceIds.filter(v => v != item.deviceId)
          this.$set(item, 'deviceName', '')
          this.$set(item, 'deviceId', '')
          this.$set(item, 'videoUrl', '')
        }
      })
    },
    closeAll() {
      this.playingDeviceIds = []
      this.layoutList.forEach(v => {
        this.closeItem(v)
      })
    },
    addToPlan() {
      if (!this.enableIndexs.length) {
        this.$Message.error('预案下没有设备，请添加设备后再保存')
        return
      }
      this.$refs.addToPlan.show()
    },
    addPlan(name) {
      let list = this.layoutList.filter(v => v.videoUrl)
      addPlan({name, videoWallId: this.currentWallId, list}).then(res => {
        if (res.code == 200) this.$Message.success('添加预案成功')
      })
    },
    selectAll(val) {
      if (val) {
        this.inspectObj.allChecked = true
        this.inspectObj.checkedIndexs = [...this.enableIndexs]
      } else {
        this.inspectObj.allChecked = false
        this.inspectObj.checkedIndexs = []
      }
    },
    checkboxChange(val, index) {
      if (val) {
        this.inspectObj.checkedIndexs.push(index)
      } else {
        this.inspectObj.checkedIndexs = this.inspectObj.checkedIndexs.filter(v => v != index)
      }
      if (this.enableIndexs.length == this.inspectObj.checkedIndexs.length) {
        this.inspectObj.allChecked = true
      } else {
        this.inspectObj.allChecked = false
      }
    },
    inspecting() {
      this.isInspecting = true
      this.inspectStatus = ''
    },
    startInspect() {
      if (!this.inspectObj.checkedIndexs.length) {
        this.$Message.error('未选择任何监视器')
        return
      }
      this.$emit('inspectStart', this.inspectObj)
      this.inspectStatus = 'starting'
    },
    cancleInspect() {
      this.$emit('cancleInspect')
      this.inspectStatus = ''
      this.isInspecting = false
    },
    inspectPlay({devices, indexs}) {
      devices.forEach((item, index) => {
        this.focusIndex = indexs[index]
        this.playByDevice(item)
      })
    },
    closeAllInspect() {
      if (this.isInspecting) {
        // this.closeAll()
        this.inspectStatus = ''
        this.isInspecting = false
      }
    },
    async setPlan(item) {
      await this.changeTvwall(item.videoWallId)
      this.playingDeviceIds = []
      item.list.forEach(v => {
        let current = this.layoutList.find(j => j.screenId == v.screenId)
        const lockStatus = current.lockInfo ? !!current.lockInfo.lockStatus : false
        const isLocked = lockStatus ? (Number(this.userInfo.shortNumber) < current.lockInfo.userLevel ? true : false) : false
        if (isLocked) {
          this.$Message.error(`电视墙窗口: ${current.screenId}已被锁定`)
          return
        }
        planPlay({"demodifierId": current.demodifierId,"displayNo": current.displayNo,"url": v.videoUrl}).then(res => {
          if (res.code == 200) {
            current.deviceName = v.deviceName
            current.deviceId = v.deviceId
            current.videoUrl = v.videoUrl
            this.playingDeviceIds.push(v.deviceId)
          }
        })
      })
    },
    handleLock(item) {
      lockWall({
        demodifierId: item.demodifierId,
        screenId: item.screenId
      }).then(res => {
        if (res.code == 200) {
          this.$Message.success("锁定成功")
          item.lockInfo ? this.$set(item.lockInfo, 'lockStatus', 1) : this.$set(item, 'lockInfo', {lockStatus: 1, userLevel: Number(this.userInfo.shortNumber)})
        }
      })
    },
    handleUnlock(item) {
      unlockWall({
        demodifierId: item.demodifierId,
        screenId: item.screenId
      }).then(res => {
        if (res.code == 200) {
          this.$Message.success("解锁成功")
          this.$set(item.lockInfo, 'lockStatus', 0)
        }
      })
    },
    dropHandler(event, index) {
      let data = event.dataTransfer.getData('application/json')
      let dataObj = JSON.parse(data)
      console.log('拖拽释放：', data, index)
      this.handleNodeClick({...dataObj, playType: 'live'}, index)
    }
  }
}
</script>

<style scoped lang="less">
.tv-well-content {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  .top {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    align-items: center;
    &-l {
      display: flex;
      align-items: center;
      white-space: nowrap;
      i {
        color: #2c86f8;
        font-size: 14px;
      }
      .label {
        margin: 0 5px;
      }
      .tip {
        color: #a5b0b6;
        margin-left: 5px;
      }
    }
    .inspect {
      display: flex;
      flex: 1;
      justify-content: space-between;
      align-items: center;
      &-l {
        display: flex;
        align-items: center;
        .item {
          display: flex;
          align-items: center;
          margin-left: 5px;
          .label {
            margin-right: 5px;
          }
        }
      }
    }
  }
  .bottom {
    flex: 1;
    background: #2c3033;
    display: grid;
    .monitorItem {
      display: flex;
      flex-direction: column;
      margin: 5px;
      &.focused {
        border: 2px solid #2c86f8;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
      }
      .title {
        background: #ffffff;
        padding: 5px;
        border-top-left-radius: 5px;
        border-top-right-radius: 5px;
        height: 28px;
        display: flex;
        justify-content: space-between;
        .iconfont {
          cursor: pointer;
          margin-right: 5px;
        }
      }
      .content {
        flex: 1;
        background: #44484c;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        &:hover {
          .icon-close {
            display: block;
          }
        }
        .icon-close {
          display: none;
          position: absolute;
          top: 0;
          right: 0;
          cursor: pointer;
          &:before {
            background: red;
            color: #fff;
          }
        }
        .deviceName {
          max-width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #888888;
        }
        &.noscreen {
          background: url('~@/assets/img/player/nophoto.png') 50% no-repeat, #44484c;
        }
      }
    }
  }
  /deep/ .ivu-input-small {
    padding: 1px 7px;
    height: 22px;
    width: 100px;
  }
}
</style>