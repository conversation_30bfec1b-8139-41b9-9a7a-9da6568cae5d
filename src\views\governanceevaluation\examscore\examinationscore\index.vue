<template>
  <div class="exam-score-examination-score height-full">
    <examinationscore></examinationscore>
  </div>
</template>
<script>
export default {
  name: 'examinationScore',
  props: {},
  data() {
    return {};
  },
  provide() {
    return {
      examSchemeType: 1, //本级考核成绩
    };
  },
  methods: {},
  mounted() {},
  components: {
    examinationscore: require('@/views/governanceevaluation/examinationresult/examinationscore/index.vue').default,
  },
};
</script>
