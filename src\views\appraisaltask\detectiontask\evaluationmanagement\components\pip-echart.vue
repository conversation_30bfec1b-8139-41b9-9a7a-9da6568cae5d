<template>
  <div class="echart" ref="echart1"></div>
</template>
<script>
export default {
  name: 'tasktracking',
  props: ['deviceObj'],
  data() {
    return {
      text: '',
    };
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.init();
    // })
    this.init();
  },
  methods: {
    init() {
      let myChart = this.$echarts.init(this.$refs.echart1);
      var option = {
        tooltip: {
          trigger: 'item',
        },
        color: ['#C02E02', '#05CE98'],
        title: {
          text: '测评总数',
          x: 'center',
          y: 'center',
          textStyle: {
            fontSize: 12,
            color: '#fff',
          },
          subtext: `${this.deviceObj.total}`,
          subtextStyle: {
            fontSize: 12,
            color: '#19C176',
          },
        },
        legend: {
          orient: 'vertical',
          bottom: '25%',
          right: '1%',
          itemWidth: 14, // 图例标记的图形宽度。[ default: 25 ]
          itemHeight: 14, // 图例标记的图形高度。[ default: 14 ]
          itemGap: 21, // 图例每项之间的间隔。[ default: 10 ]横向布局时为水平间隔，纵向布局时为纵向间隔。
          textStyle: {
            fontSize: 14,
            color: '#fff',
          },
        },
        series: [
          {
            // name: "",
            type: 'pie',
            zlevel: 3,
            radius: ['60%', '80%'],
            avoidLabelOverlap: false,
            labelLine: {
              //设置延长线的长度
              length: 3, //设置延长线的长度
              length2: 30, //设置第二段延长线的长度
            },
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 0,
            },
            label: {
              show: false,
              // position: 'inside',
              // // formatter: '{c}\n{b}',
              //  normal: {
              //     formatter:'{d}%\n{b}',
              //     rich: {
              //         b: {
              //             fontSize: 12,
              //             lineHeight: 33,
              //             color:'#fff',
              //         },
              //         per: {
              //             color: '#eee',
              //             padding: [2, 4],
              //             borderRadius: 2
              //         }
              //     },
              //   },
            },
            data: [
              { value: this.deviceObj.unqualifiedNum, name: '问题数据' },
              { value: this.deviceObj.qualifiedNum, name: '合格数据' },
            ],
          },
        ],
      };

      myChart.setOption(option);
      // window.onresize = function(){
      //   myChart.resize();
      // }
    },
    resizeFn() {
      this.init();
      // let myChart = this.$echarts.init(this.$refs.echart2);
      // myChart.resize();
    },
  },
  watch: {
    deviceObj() {
      this.init();
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.echart {
  // position: absolute;
  // width: 100%;
  // height: 100%;
  width: 500px;
  height: 150px;
}
</style>
