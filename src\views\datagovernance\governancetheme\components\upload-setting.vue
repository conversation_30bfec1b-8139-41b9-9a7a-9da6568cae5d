<template>
  <div class="upload-setting">
    <div class="upload-setting-label">
      <span>{{ settingData.label }}</span>
      <span>不超过</span>
    </div>
    <Input v-model="settingData.value1" style="width: 118px" />
    <Select v-model="value2" style="width: 60px">
      <Option value="1">时</Option>
      <Option value="2">分</Option>
      <Option value="3">秒</Option>
    </Select>
  </div>
</template>
<script>
export default {
  props: {
    settingData: {
      type: Object,
      default: () => {},
    },
  },
  created() {},
  methods: {},
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped></style>
