<template>
  <div class="repeat-empty">
    <ui-modal v-model="visible" title="重复检测" width="66.14rem" @query="handleSave">
      <transfer-repeat
        title1="字段拾取"
        title2="字段名列表"
        tip="注:不能为空"
        :columns1="columns1"
        :columns2="columns2"
        :tableData1="propertyList"
        :tableData2="targetList"
        @selectionChange="selectionChange"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
      ></transfer-repeat>
    </ui-modal>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    topicType: {
      type: String,
      default: '',
    },
    topicId: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      visible: false,
      columns1: [
        { type: 'selection', width: 60 },
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      propertyList: [],
      columns2: [
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
        {
          title: '重复次数',
          key: 'address',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            return h('i-input', {
              props: {
                readonly: row._read,
                value: row.repeatCount,
                number: true,
              },
              style: { 'font-size': '14px' },
              on: {
                input: (val) => {
                  row.repeatCount = val;
                  this.targetList.splice(index, 1, row);
                },
              },
            });
          },
        },
      ],
      targetList: [],
      checkedList: [],
      checkedIds: [],
      leftLoading: false,
      rightLoading: false,
      indexConfig: {},
      defaultRep: [
        { checkColumnName: 'deviceId', checkColumnValue: '设备编码', repeatCount: 2 },
        { checkColumnName: 'macAddr', checkColumnValue: 'MAC地址', repeatCount: 2 },
      ],
    };
  },
  mounted() {},
  methods: {
    async init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.leftLoading = true;
      this.rightLoading = true;
      await this.getPropertyList();
      this.getDevice();
    },
    async getPropertyList() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let res = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        this.setDefaultValue(res.data.data.rulePropertyList || []);
      } catch (error) {
        console.log(error);
      }
    },
    async getDevice() {
      try {
        let params = {
          keyWord: '',
          propertyType: this.topicType, // 字段类型，1：视图；2：人脸；3：车辆；4：视频；5：重点人员 7: 场所数据指标
        };
        let res = await this.$http.post(governancetheme.standardList, params);
        this.propertyList = res.data.data.map((item) => {
          if (this.checkedIds.length) {
            this.checkedIds.forEach((checkId) => {
              if (item.propertyName == checkId) {
                item._checked = true;
              }
            });
          }
          return item;
        });
        this.targetList = this.checkedList;
        this.leftLoading = false;
        this.rightLoading = false;
      } catch (error) {
        console.log(error);
      }
    },
    selectionChange(selection) {
      selection = selection.map((item) => {
        let obj = {};
        obj.checkColumnName = item.propertyName;
        obj.checkColumnValue = item.propertyColumn;
        obj.repeatCount = item.repeatCount;
        this.targetList.forEach((checkedItem) => {
          if (item.propertyName === checkedItem.checkColumnName) {
            obj.repeatCount = checkedItem.repeatCount;
          }
        });
        return obj;
      });
      this.targetList = selection;
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let valid = this.targetList.filter((item) => {
          let reg = /^[1-9]\d*$/;
          if (!reg.test(item.repeatCount)) {
            this.$Message.error('重复次数仅支持输入大于0整数!');
            return item;
          } else if (!item.repeatCount || typeof item.repeatCount !== 'number') {
            this.$Message.error('请输入重复次数!');
            return item;
          }
        });
        if (valid.length) {
          return false;
        }
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          rulePropertyList: this.targetList,
        };
        await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.reset();
        this.$emit('render');
        this.$Message.success('重复检测配置成功！');
      } catch (error) {
        console.log(error);
      }
    },
    reset() {
      this.visible = false;
      this.checkedIds = [];
      this.propertyList = [];
      this.targetList = [];
      this.checkedList = [];
    },
    //基础数据检测规则-空值检测，默认加上资产库的10个字段 2984
    setDefaultValue(val) {
      if (val.length === 0) {
        this.checkedList = JSON.parse(JSON.stringify(this.defaultRep));
        this.checkedIds = this.defaultRep.map((item) => item.checkColumnName);
        return;
      }
      this.checkedIds = val.map((item) => item.checkColumnName);
      this.checkedList = val.map((item) => {
        return {
          checkColumnName: item.checkColumnName,
          checkColumnValue: item.checkColumnValue,
          repeatCount: item.repeatCount,
        };
      });
    },
  },
  watch: {},
  components: {
    TransferRepeat: require('@/components/transfer-repeat').default,
  },
};
</script>
<style lang="less" scoped>
.repeat-empty {
  @{_deep} .transfer-table {
    padding: 0;
  }
}
</style>
