<template>
  <div class="gather-personnel-trajectory">
    <div class="gather-personnel-trajectory-header">
      <ui-label class="inline" label="关键词" :width="55">
        <Input v-model="searchData.keyWord" class="width-md" placeholder="请输入姓名或证件号"></Input>
      </ui-label>
      <ui-label class="inline ml-md" label="抓拍数量" :width="70">
        <Input v-model="searchData.snapUpperLimit" class="width-xs" placeholder="请填写数量"></Input>
        <span class="horizontalbar">—</span>
        <Input v-model="searchData.snapDownLimit" class="width-xs" placeholder="请填写数量"></Input>
      </ui-label>
      <ui-label class="inline ml-md" label="抓拍异常数量" :width="98">
        <Input v-model="searchData.abnormalUpperLimit" class="width-xs" placeholder="请填写数量"></Input>
        <span class="horizontalbar">—</span>
        <Input v-model="searchData.abnormalDownLimit" class="width-xs" placeholder="请填写数量"></Input>
      </ui-label>
      <!-- <ui-label class="inline ml-md" label="数据来源：" :width="70">
        <Select
          class="width-sm"
          placeholder="请选择"
          clearable
          v-model="searchData.personType"
        >
          <Option
            v-for="(item, index) in importantTypes"
            :key="index"
            :value="item.dataKey"
            >{{ item.dataValue }}</Option
          >
        </Select>
      </ui-label> -->
      <div class="inline ml-lg">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="clear">重置</Button>
      </div>
    </div>
    <div class="gather-personnel-trajectory-content auto-fill" v-ui-loading="{ loading: loading, tableData: cardList }">
      <div class="gather-personnel-trajectory-content-wrap">
        <ui-gather-card
          class="card"
          v-for="(item, index) in cardList"
          :key="index"
          :list="item"
          :defaultTags="4"
          :cardInfo="cardInfo"
          @detail="detail"
        ></ui-gather-card>
      </div>
    </div>
    <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <capture-details ref="CaptureDetails"></capture-details>
  </div>
</template>
<script>
import equipmentassets from '@/config/api/equipmentassets';
import persontype from '../keypersonlibrary/persontype.js';
export default {
  props: {},
  data() {
    return {
      loading: false,
      searchData: {
        snapUpperLimit: null,
        snapDownLimit: null,
        abnormalUpperLimit: null,
        abnormalDownLimit: null,
        keyWord: '',
        orgCode: '',
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      cardList: [],
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'snapCount' },
        { name: '异常轨迹：', value: 'abnormalCount', color: 'var(--color-warning)' },
      ],
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.search();
  },
  methods: {
    search() {
      this.searchData.pageNumber = 1;
      this.getList();
    },
    async getList() {
      try {
        this.cardList = [];
        this.loading = true;
        let params = this.searchData;
        let res = await this.$http.post(equipmentassets.queryPersonLibPageTaskGather, params);
        const datas = res.data.data;
        this.cardList = datas.entities.map((item) => {
          const personTypes = item.personTypeText.split(',');
          item.personTypes = persontype.filter((item) => {
            return personTypes.includes(item.tagName);
          });
          return item;
        });
        this.pageData.totalCount = datas.total;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
    detail(id) {
      this.$refs.CaptureDetails.init(id, false);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.getList();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    clear() {
      this.resetSearchDataMx(this.searchData, this.search);
    },
  },
  watch: {},
  components: {
    UiGatherCard: require('@/components/ui-gather-card.vue').default,
    CaptureDetails: require('@/views/viewassets/videopictureassets/keypersonlibrary/components/capture-details.vue')
      .default,
  },
};
</script>
<style lang="less" scoped>
.gather-personnel-trajectory {
  padding: 0 15px;
  background-color: var(--bg-content);
  &-header {
    margin: 20px 5px;
  }
  &-content {
    // margin: 20px 0 0;
    position: relative;
    overflow-y: auto;
    &-wrap {
      // flex: 1;
      width: 100%;
      display: flex;
      display: -webkit-flex;
      flex-wrap: wrap;
    }
    .card {
      width: calc(calc(100% - 30px) / 3);
      margin: 0 5px 10px;
    }
  }
}
.horizontalbar {
  margin: 0 9px;
  color: #1b82d2;
}
</style>
