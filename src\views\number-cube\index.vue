<template>
  <div class="number-cube">
    <div class="container-center">
      <div class="container-center_bg">
        <h2 class="title"><img src="@/assets/img/number-cube/logo.png" /></h2>
        <div class="search-content">
          <div class="input-content">
            <Dropdown
              trigger="custom"
              :visible="visible"
              placement="bottom-start"
            >
              <Input
                v-model="queryData.searchValue"
                :placeholder="searchPlaceholder"
                class="search-input"
              >
                <i
                  class="iconfont icon-xiangji"
                  slot="suffix"
                  @click="pictureSearchHandle"
                ></i>
                <Button
                  type="primary"
                  :disabled="isImage"
                  class="search-btn"
                  slot="append"
                  @click="toOrdinarySearchHandle"
                  >搜索</Button
                >
              </Input>
              <div
                class="pic-box"
                v-if="isImage"
                @click="pictureSearchHandle"
              ></div>
              <DropdownMenu slot="list">
                <search-pictures
                  @cancel="closePictureModel"
                  :picData="queryData"
                  @search="searchPictures"
                  @imgUrlChange="imgUrlChange"
                />
              </DropdownMenu>
            </Dropdown>
          </div>
        </div>
      </div>
    </div>
    <!-- 背景图片 定位 -->
    <img class="top_left" src="@/assets/img/number-cube/top_left.png" alt="" />
    <img
      class="top_right"
      src="@/assets/img/number-cube/top_right.png"
      alt=""
    />
    <img
      class="bottom_left"
      src="@/assets/img/number-cube/bottom_left.png"
      alt=""
    />
    <img
      class="bottom_right"
      src="@/assets/img/number-cube/bottom_right.png"
      alt=""
    />
    <!-- 我的图谱 -->
    <div
      class="fixed-bottom-card"
      :class="
        visibleBottom
          ? visibleBottom1
            ? 'show-card1'
            : 'show-card'
          : 'none-card'
      "
    >
      <img
        v-if="visibleBottom"
        class="fixed-bottom"
        src="@/assets/img/number-cube/none.png"
        alt=""
        @click="visibleBottom = !visibleBottom"
      />
      <img
        v-else
        src="@/assets/img/number-cube/show.png"
        alt=""
        @click="visibleBottom = !visibleBottom"
      />
      <Card>
        <template slot="title">
          <div class="breadcrumb-container">
            <span class="box"></span>
            <span class="title">
              <i>我的画布</i>
            </span>
          </div>
        </template>
        <template slot="extra">
          <span class="showMore" @click="visibleBottom1 = !visibleBottom1">
            {{ visibleBottom1 ? "收起" : "展开" }}更多
            <ui-icon
              type="jiantou"
              :class="{
                arrowTransform: visibleBottom1,
                arrowTransformReturn: !visibleBottom1,
              }"
            />
          </span>
        </template>
        <Scroll :on-reach-bottom="handleReachBottom" :distance-to-edge="10">
          <Card
            class="fixed-bottom-card-children"
            :class="{ 'fixed-bottom-card-children1': (index + 1) % 5 === 0 }"
            v-for="(item, index) in list"
            :key="index"
          >
            <div @click="cardInfoClick(item)">
              <div class="graph">
                <ui-image :src="item.imageUrl" alt="图片" />
                <!-- <img src="./components/card_bg.png" /> -->
                <div class="detail">查看详情</div>
              </div>
              <div class="title-children" v-show-tips>{{ item.name }}</div>
              <div class="time">{{ item.createTime }}</div>
              <div class="operate-bar" @click.stop="">
                <p class="operate-content">
                  <ui-btn-tip
                    content="编辑"
                    @click.native="canvasUpdate(item)"
                    icon="icon-bianji"
                  />
                  <ui-btn-tip
                    class="delete-tip"
                    content="删除"
                    @click.native="canvasDel(item)"
                    icon="icon-shanchu"
                  />
                </p>
              </div>
            </div>
          </Card>
          <div v-if="noData" style="text-align: center; color: #999">
            没有更多了
          </div>
        </Scroll>
      </Card>
    </div>
    <!-- 保存 -->
    <RelationGraphSave
      :graphName="currentGraph.name"
      ref="relationGraphSave"
      v-model="relationGraphSaveShow"
      @graphSave="graphSave"
    />
    <!-- 检索结果 输入框-->
    <SearchResultInput
      v-model="searchResultInputShow"
      :picData="queryData"
      ref="searchResultInput"
    />
    <!-- 检索结果 以图搜图 -->
    <SearchResultImage
      v-model="searchResultImageShow"
      ref="searchResultImage"
    />
  </div>
</template>
<script>
import { mapMutations, mapActions, mapGetters } from "vuex";
import { anvasList, canvasRemove, canvasUpdate } from "@/api/number-cube";
export default {
  name: "searchIndex",
  components: {
    RelationGraphSave: require("./components/relation-graph-save.vue").default,
    SearchPictures: require("./components/search-pictures.vue").default,
    SearchResultInput: require("./components/search-result-input.vue").default,
    SearchResultImage: require("./components/search-result-image.vue").default,
  },
  computed: {
    ...mapGetters({
      globalObj: "systemParam/globalObj",
      relationObj: "systemParam/relationObj",
    }),
  },
  data() {
    return {
      isImage: false, // 是否以图搜图
      visible: false,
      visibleBottom1: false,
      visibleBottom: false,
      currentGraph: {},
      noData: false,
      relationGraphSaveShow: false,
      searchResultInputShow: false,
      searchResultImageShow: false,
      lists: [
        { name: "人脸同行分析图谱" },
        { name: "人人同驾乘分析图谱" },
        { name: "驾驶机动车分析图谱" },
        { name: "乘坐机动车分析图谱" },
        { name: "车辆伴随分析图谱" },
        { name: "同驾驶人分析图谱" },
        { name: "驾驶机动车分析图谱" },
        { name: "人脸同行分析图谱" },
        { name: "人人同驾乘分析图谱" },
        { name: "同驾驶人分析图谱" },
        { name: "同驾驶人分析图谱" },
        { name: "驾驶机动车分析图谱" },
        { name: "人脸同行分析图谱" },
        { name: "人人同驾乘分析图谱" },
        { name: "同驾驶人分析图谱" },
      ],
      list: [],
      queryData: {
        searchValue: "",
        algorithmType: "1",
        similarity: 66,
        urlList: ["", "", "", "", ""],
      },
      pages: {
        params: {
          pageNumber: 1,
          pageSize: 20,
          total: 0,
        },
      },
      searchPlaceholder: "请输入关键词检索，多个关键词请用空格隔开",
    };
  },
  async mounted() {
    if (this.$route.params.type) {
      this.visibleBottom = true;
    }
    await this.getSystemAllData();
    const { searchForPicturesDefaultSimilarity } = this.globalObj;
    this.queryData.similarity = Number(searchForPicturesDefaultSimilarity);
    this.setLayoutNoPadding(true);
    this.anvasList();
  },
  deactivated() {
    this.setLayoutNoPadding(false);
  },
  beforeDestroy() {
    this.setLayoutNoPadding(false);
  },
  methods: {
    ...mapActions({
      getSystemAllData: "systemParam/getSystemAllData",
    }),
    ...mapMutations("admin/layout", ["setLayoutNoPadding"]),
    anvasList() {
      this.list = [];
      const params = {
        graphId: this.relationObj.graphInfo.graphId,
        ...this.pages,
      };
      anvasList(params).then((res) => {
        console.log("我的画布列表------", res);
        this.list = [...this.list, ...res.data.entities];
        this.pages.params.total = res.data.total;
      });
    },
    // 打开头像搜索弹框
    pictureSearchHandle() {
      this.visible = !this.visible;
    },
    // 打开底部更多
    pictureSearchHandleBottom() {
      this.visibleBottom = !this.visibleBottom;
    },
    pictureSearchHandleBottom1() {
      this.visibleBottom = !this.visibleBottom;
    },
    // 查询
    searchForm() {
      console.log(111);
    },
    imgUrlChange(urlList) {
      // this.queryData.list = urlList
      this.queryData.urlList = urlList;

      let list = urlList.filter((item) => !!item);
      if (list.length == 0) {
        // 清空操作
        this.isImage = false;
        this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
      } else {
        // 添加图片
        this.setInputStatus(urlList);
        this.searchPlaceholder = "点击上传图片";
      }
    },
    /**
     * 设置输入框可用状态
     */
    setInputStatus(urlList) {
      var arr = urlList.filter((item) => {
        return item.fileUrl;
      });
      if (arr.length == 0) {
        this.isImage = false;
      } else {
        this.isImage = true;
      }

      if (this.isImage) {
        this.searchValue = "";
      }
    },
    // 图片查询
    searchPictures(e) {
      this.queryData = e;
      // console.log('queryData------', this.queryData)
      // let { algorithmType, similarity, urlList } = picData
      // let query = {
      //   algorithmType,
      //   similarity,
      //   searchValue: '',
      //   urlList: urlList
      // }
      const list = e.urlList.filter((e) => e && e.feature);
      if (!list.length) {
        this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
      } else {
        this.searchPlaceholder = "点击上传图片";
      }
      this.searchResultInputShow = true;
    },
    closePictureModel() {
      this.visible = false;
      this.searchPlaceholder = "请输入关键词检索，多个关键词请用空格隔开";
    },
    cardInfoClick(row) {
      this.$store.dispatch("number-cube/setSource", { type: 1 });
      const query = {
        id: row.id,
        name: row.name,
        type: "detail",
      };
      this.$router.push({ name: "number-cube-info", query: query });
    },
    toOrdinarySearchHandle() {
      if (!this.queryData.searchValue) {
        this.$Message.warning("请输入关键词，多个关键词请用空格隔开");
        return false;
      }
      this.$refs.searchResultInput.$refs.uiTablePage.pageData.pageNumber = 1;
      this.searchResultInputShow = true;
    },
    toAdvancedSearchHandle() {
      const path = this.$route.path;
      this.$router.push({ path, query: { componentName: "advancedSearch" } });
    },
    // 滚动
    handleReachBottom(dir) {
      if (
        this.pages.params.pageNumber * this.pages.params.pageSize >
        this.pages.params.total
      ) {
        this.noData = true;
        return;
      }

      return new Promise((resolve) => {
        setTimeout(() => {
          this.pages.params.pageNumber++;
          this.anvasList();
          resolve();
        }, 200);
      });
    },
    // 滚动
    canvasUpdate(row) {
      this.relationGraphSaveShow = true;
      this.currentGraph = row;
      // let query = {
      //   id: row.id,
      //   proximity: 1,
      //   name: row.name,
      // }
      // this.$router.push({ name: 'number-cube-info', query: query })
    },
    // 滚动
    canvasDel(row) {
      this.$Modal.confirm({
        title: "提示",
        width: 450,
        closable: true,
        content: `确定删除该画布？`,
        onOk: () => {
          canvasRemove(row.id).then((res) => {
            this.$Message.success("删除成功");
            this.anvasList();
          });
        },
      });
    },
    graphSave(name) {
      var param = JSON.parse(JSON.stringify(this.currentGraph));
      param.name = name;
      canvasUpdate(param).then((res) => {
        this.$Message.success("编辑成功");
        this.relationGraphSaveShow = false;
        this.anvasList();
      });
    },
  },
};
</script>
<style lang="less" scoped>
.number-cube {
  position: relative;
  flex: 1;
  border-radius: 0;
  padding: 0;
  height: 100%;
  background: #e8ebf4 url("~@/assets/img/number-cube/bj.png") no-repeat center
    bottom/100% 100%;
  .container-center {
    display: flex;
    align-items: center;
    height: 100%;
    justify-content: center;
    position: relative;
    padding-bottom: 230px;
    z-index: 99;
    // background-color: #e8ebf4;
    & > .container-center_bg::before {
      background: center bottom no-repeat;
      background-image: url("~@/assets/img/number-cube/center_bg.png");
      background-position: center bottom;
      background-size: contain;
      height: 480px;
      width: 424px;
      content: "";
      position: absolute;
      left: calc(~"50% - 212px");
      top: calc(~"50% - 264px");
    }
    &::before {
      background: center bottom no-repeat;
      background-image: url("~@/assets/img/number-cube/center.png");
      background-position: center bottom;
      background-size: contain;
      height: 700px;
      width: 700px;
      content: "";
      position: absolute;
      left: calc(~"50% - 350px");
      top: calc(~"50% - 369px");
      -webkit-animation: rotation 60s linear infinite;
      animation: rotation 60s linear infinite;
    }
    & .input-content {
      padding: 25px 31px;
      background: center bottom no-repeat;
      background-image: url("~@/assets/img/number-cube/input.png");
      background-position: center bottom;
      background-size: contain;
      height: 94px;
      width: 644px;
      // content: '';
      // position: absolute;
      // left: calc(~'50% - 322px');
      // top: calc(~'50% - 65px');
    }
    @keyframes rotation {
      from {
        transform: rotateZ(0deg);
      }

      to {
        transform: rotateZ(360deg);
      }
    }
  }
  .title {
    text-align: center;
    img {
      position: relative;
      // width: 344px;
      // height: auto;
    }
  }
  .information {
    text-align: center;
    font-size: 24px;
    margin-top: 25px;
    color: rgba(0, 0, 0, 0.9);
    .num {
      font-size: 44px;
      font-weight: 500;
      letter-spacing: -1px;
    }
  }
  .search-content {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: center;

    // padding-left: 125px;
    .advance-search-btn {
      color: #2c86f8;
      font-size: 16px;
      background-color: transparent;
      padding: 0 30px;
      &:hover {
        color: #4597ff;
      }
      &:active {
        color: #1a74e7;
      }
    }
    .search-input {
      width: 570px;
      height: 50px;
      //   border: 2px solid #358cfa;
      border: 1px solid transparent;
      overflow: hidden;
      /deep/ .ivu-input {
        height: 100%;
        font-size: 14px;
        padding-left: 20px;
        border: 1px solid #e8eaef;
        box-shadow: 0px 11px 28px 0px rgba(156, 186, 230, 0.3);
        &:hover,
        &:focus {
          border: 0 !important;
          // border-color: #9cbae6;
        }
      }
    }
    /deep/ .ivu-input-suffix {
      right: 111px;
      display: flex;
      align-items: center;
      z-index: 100;
      width: 50px;
      // height: 48px;
      top: 1px;
      background: #fff;
      cursor: pointer;
      .iconfont {
        font-size: 18px;
        color: #888;
        margin: 0 auto;
      }
    }

    /deep/ .ivu-input-group-append {
      width: 110px;
      border-radius: 0 !important;
      .ivu-btn {
        font-size: 20px;
      }
    }
    .input-content {
      position: relative;
      .pic-box {
        position: absolute;
        width: 460px;
        height: 50px;
        top: 0px;
        z-index: 2;
        cursor: pointer;
      }
    }
  }
  .top_left {
    position: absolute;
    left: 90px;
    top: 0px;
  }
  .top_right {
    position: absolute;
    right: 0;
    top: 0;
  }
  .bottom_left {
    position: absolute;
    left: -40px;
    bottom: -60px;
  }
  .bottom_right {
    position: absolute;
    right: 32px;
    bottom: 10px;
  }
  .operate-bar {
    height: 30px;
    background: linear-gradient(
      90deg,
      rgba(87, 187, 252, 0.8) 0%,
      #2c86f8 100%
    );
    border-radius: 0px 0px 4px 0px;
    position: absolute;
    right: -100%;
    transition: all 0.3s;
    bottom: 0;
    transform: skewX(-20deg);
    .operate-content {
      padding: 0 5px;
      transform: skewX(20deg);
      height: 100%;
      display: flex;
      align-items: center;
      color: #fff;
      /deep/ .ivu-tooltip-rel {
        padding: 6px;
      }
      .delete-tip {
        /deep/ .ivu-tooltip-popper {
          left: 20px !important;
        }
      }
      /deep/ .ivu-tooltip-inner {
        padding: 4px 6px;
        min-height: unset;
        left: 16px;
        span {
          width: max-content;
          display: inline-block;
        }
      }
    }
  }
  /deep/ .fixed-bottom-card > .ivu-card > .ivu-card-body {
    display: flex;
    flex-wrap: nowrap;
    padding: 6px 6px 10px 10px;
  }
  .fixed-bottom-card {
    width: 63%;
    margin-left: calc(~"19% - 10px");
    color: black;
    position: absolute;
    bottom: 10px;
    max-height: 0px;
    text-align: center;
    overflow: hidden;
    z-index: 101;
    img {
      cursor: pointer;
    }

    .fixed-bottom-card-children {
      width: 19%;
      overflow: hidden;
      margin-right: 1%;
      display: inline-block;
      background: #f9f9f9;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      border: 1px solid #d3d7de;
      position: relative;
      text-align: left;
      margin-bottom: 14px;
      /deep/ .ivu-card-body {
        padding: 10px;
        border: 1px solid #f9f9f9;
      }
      img {
        width: 200px;
        height: 150px;
      }
      .title-children {
        font-size: 15px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
        padding-bottom: 6px;
      }
      .time {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.35);
      }
      &:hover {
        background: #f9f9f9;
        box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
        border-radius: 4px;
        border: 1px solid #2c86f8;
        cursor: pointer;
        /deep/ .ivu-card-body {
          border: 1px solid #2c86f8;
        }
        .operate-bar {
          right: -6px;
          bottom: -1px;
        }
      }
    }
    .fixed-bottom-card-children1 {
      margin-right: 0;
    }
  }
  /deep/ .ivu-scroll-content {
    text-align: left;
  }
  .show-card {
    transition: max-height 0.5s;
    max-height: 360px;
    & > img {
      transition: 0.5s;
      position: relative;
      bottom: -4px;
    }

    /deep/ .ivu-scroll-container {
      transition: 0.5s;
      height: 705px !important ;
      overflow: hidden;
    }
  }
  .show-card1 {
    transition: 0.5s;
    max-height: 760px;
    & > img {
      transition: 0.5s;
      position: relative;
      bottom: -4px;
    }
    /deep/ .ivu-scroll-container {
      height: 660px !important ;
    }
  }
  .none-card {
    transition: 0.5s;
    bottom: 0;
    max-height: 25px;
    & > img {
      transition: 0.5s;
      position: relative;
      bottom: 0;
    }
    /deep/ .ivu-scroll-container {
      transition: 0.5s;
      height: 0px !important ;
    }
  }
  .breadcrumb-container {
    // padding-top: 16px;
    // padding-bottom: 20px;
    text-align: left;
    .box {
      display: inline-block;
      width: 3px;
      height: 20px;
      background: #2b84e2;
    }
    .title {
      position: relative;
      display: inline-block;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9);
      top: -3px;
      padding-left: 8px;
    }
  }
}

/deep/ .ivu-scroll-wrapper {
  width: 100% !important;
}

.graph {
  position: relative;
  /deep/ .ui-image-div {
    width: 198px;
    height: 180px;
    img {
      max-height: 100% !important;
      width: 100%;
      height: auto !important;
    }
  }
  .detail {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    top: 0;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0;
    font-size: 20px;
    color: #4595fc;
  }

  &:hover {
    .detail {
      opacity: 1;
    }
  }
}

.showMore {
  cursor: pointer;
  color: #4595fc;
  .arrowTransform {
    display: inline-block;
    transition: 0.8s;
    transform-origin: center;
    transform: rotateZ(180deg);
  }

  .arrowTransformReturn {
    display: inline-block;
    transition: 0.8s;
    transform-origin: center;
    transform: rotateZ(0deg);
  }
  .icon-jiantou {
    color: #4595fc !important;
  }
}
</style>
