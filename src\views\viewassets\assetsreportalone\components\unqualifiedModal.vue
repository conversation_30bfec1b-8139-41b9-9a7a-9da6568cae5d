<template>
  <ui-modal
    ref="modalChild"
    class="unqualified-modal"
    title="不合格数据反馈"
    v-model="visible"
    width="90%"
    :footerHide="true"
  >
    <!-- 表格 -->
    <div class="tables auto-fill">
      <TableList ref="tableList" :columns="tableColumns" :load-data="loadDataList" @selectAction="selectAction">
        <!-- 检索 -->
        <div slot="search" class="hearder-title">
          <Search ref="search" @startSearch="startSearch" />
          <!-- 操作按钮 -->
          <div class="operation">
            <Checkbox v-model="isAll">全选</Checkbox>
            <div class="right-btn fr">
              <Button type="primary" @click="exportExcel" :loading="exportLoading">
                <i class="icon-font icon-daochu f-14"></i>
                <span class="vt-middle ml-sm">导出</span>
              </Button>
              <Button type="primary" @click="report()"><i class="icon-font icon-piliangshangbao"></i> 批量上报 </Button>
            </div>
          </div>
        </div>
        <!-- 表格操作 -->
        <template #action="{ row }">
          <ui-btn-tip
            class="mr-md"
            icon="icon-tianbao"
            :styles="{ color: '#269F26', 'font-size': '14px' }"
            content="信息填报"
            @click.native="deviceModalShow(row)"
          ></ui-btn-tip>
          <ui-btn-tip
            class="mr-md"
            icon="icon-jiandanggengxin"
            :styles="{ color: '#DE990F', 'font-size': '14px' }"
            @click.native="report(row)"
            content="重新上报"
          ></ui-btn-tip>
          <ui-btn-tip
            :styles="{ color: '#CF3939', 'font-size': '14px' }"
            icon="icon-buhegeyuanyin"
            @click.native="errorReport(row)"
            content="不合格原因"
          ></ui-btn-tip>
        </template>
        <template #deviceId="{ row }">
          <span class="font-active-color pointer device-id" :class="row.rowClass" @click="deviceArchives(row)">{{
            row.deviceId
          }}</span>
        </template>

        <template #deviceStatus="{ row }">
          <span
            :style="{
              color: row.deviceStatus === '1' ? 'var(--color-success)' : 'var(--color-warning)',
            }"
            >{{ row.deviceStatus | filterType(phystatusList) }}</span
          >
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #sbgnlx="{ row }">
          <span>{{ row.sbgnlx | filterType(propertySearchSxjgnlx) }}</span>
        </template>
      </TableList>
      <!-- 不合格原因 -->
      <ErrorModal ref="errorModal" />
      <!-- 信息填报 -->
      <DeviceDetail
        v-model="deviceDetailShow"
        :choosed-org="choosedOrg"
        :deviceCode="deviceCode"
        modal-title="设备信息填报"
        modal-action="edit"
        :view-device-id="viewDeviceId"
        :fetch-url="fetchUrl"
        @update="search"
      />
    </div>
  </ui-modal>
</template>
<script>
import viewassets from '@/config/api/viewassets';
import equipmentassets from '@/config/api/equipmentassets';
import { mapGetters } from 'vuex';

export default {
  components: {
    TableList: require('../components/tableList').default,
    Search: require('../components/searchModal').default,
    ErrorModal: require('../components/errorModal').default,
    DeviceDetail: require('@/views/viewassets/components/device-detail.vue').default,
  },
  props: {
    intefaceList: {
      type: Array,
      default() {},
    },
    reportPlatformId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      deviceCode: '',
      viewDeviceId: 0,
      deviceDetailShow: false,
      visible: false,
      isAll: false,
      exportLoading: false,
      searchData: {},
      selectRows: [],
      choosedOrg: {},
      fetchUrl: equipmentassets.updateDeviceInfo,
      tableColumns: [
        { type: 'selection', width: 50, align: 'center', fixed: 'left' },
        {
          type: 'index',
          width: 50,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          slot: 'deviceId',
          fixed: 'left',
        },
        {
          width: 100,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          tooltip: true,
        },
        { width: 120, title: '组织机构', key: 'orgName', tooltip: true },
        { width: 100, title: '行政区划', key: 'civilName', tooltip: true },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          align: 'left',
          tooltip: true,
        },
        { minWidth: 100, title: '采集区域', key: 'sbcjqyText', tooltip: true },
        {
          minWidth: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'deviceStatus',
          tooltip: true,
        },
        { minWidth: 150, title: '下发时间', key: 'createTime', tooltip: true },
        {
          width: 110,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /* 操作栏列-单元格padding设置*/,
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      phystatusList: 'algorithm/propertySearch_phystatus',
    }),
  },
  watch: {},
  filter: {},
  created() {},
  mounted() {},
  methods: {
    // 初始化
    open() {
      this.$nextTick(() => {
        this.visible = true;
        this.isAll = false;
        this.selectRows = [];
        this.$refs.tableList.search();
      });
    },
    // 查询
    startSearch(searchData, choosedOrg) {
      this.searchData = searchData;
      this.choosedOrg = choosedOrg;
      this.$refs.tableList.search();
    },
    search() {
      this.$refs.tableList.search();
    },
    // 表格选中
    selectAction(rows) {
      this.selectRows = rows;
    },
    // 设备档案
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.did },
      });
      window.open(routeData.href, '_blank');
    },
    // 查看不合格原因
    errorReport(row) {
      this.$refs.errorModal.open(row);
    },
    // 上报 || 批量上报
    report(rows) {
      // if (!this.reportPlatformId) {
      //   this.$Message.warning('请先选择上报对象!')
      //   return
      // }
      // let intefaceName = ''
      // this.intefaceList.map((val) => {
      //   if (val.id == this.reportPlatformId) {
      //     intefaceName = val.intefaceName
      //   }
      // })
      let content = '';
      if (rows) {
        content = '确定上报吗？';
        this.reportAll(content, rows);
      } else if (this.isAll) {
        content = '确定上报吗？';
        this.reportAll(content);
      } else {
        content = '已选择' + this.selectRows.length + '条设备，确定上报吗？';
        if (this.selectRows.length == 0) {
          this.$Message.warning('请勾选待上报设备');
        } else {
          this.reportAll(content);
        }
      }
    },
    reportAll(content, rows) {
      this.$UiConfirm({
        content: content,
        title: '数据上报',
      }).then(() => {
        let params = JSON.parse(JSON.stringify(this.$refs.search.searchData));
        params.isAll = this.isAll ? 1 : 0;
        // params.orgCode = this.selectRows
        if (rows) {
          params.deviceIds = [rows.deviceId];
        } else if (!this.isAll) {
          params.deviceIds = this.selectRows.map((val) => {
            return val.deviceId;
          });
        } else {
          params.isAllReportDevice = 1;
        }
        params.reportPlatformId = this.reportPlatformId;
        this.$http.post(viewassets.batchReport, params).then(() => {
          this.$refs.tableList.search();
          this.selectRows = [];
          this.$Message.success('上报成功');
        });
      });
    },
    // 信息填报
    deviceModalShow(row) {
      this.deviceCode = row.deviceId;
      this.viewDeviceId = row.did;
      this.deviceDetailShow = true;
    },
    async exportExcel() {
      try {
        this.exportLoading = true;
        let params = {
          fallbackDetailIds: this.selectRows.map((row) => row.id),
        };
        const res = await this.$http.post(viewassets.exportReportDevice, Object.assign(params, this.searchData));
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
        this.$Message.error(err.msg);
      } finally {
        this.exportLoading = false;
      }
    },
    loadDataList(parameter) {
      const queryData = Object.assign(parameter, this.searchData);
      return this.$http.post(viewassets.reportDeviceFallbackDetail, queryData).then((res) => {
        return res.data;
      });
    },
  },
};
</script>
<style lang="less" scoped>
.unqualified-modal {
  .icon-piliangshangbao {
    font-size: 12px;
    margin-right: 10px;
    position: relative;
    top: -1px;
  }
}
/deep/ .operation {
  margin: 2px 0px 12px 0px;
  height: 34px;
  line-height: 34px;
  .right-btn {
    button {
      margin-left: 12px;
    }
  }
}
.tables {
  height: 660px;
}
</style>
