<template>
  <!--  -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="list auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
        <template #apiCheckTime="{ row }">
          <span>{{ row.apiCheckTime | filterDateFun }}</span>
        </template>
        <template #option="{ row }">
          <ui-btn-tip icon="icon-chakanxiangqing" content="查看" @handleClick="show(row.id, row.apiIndex)"></ui-btn-tip>
        </template>
      </ui-table>

      <loading v-if="loading"></loading>
    </div>
    <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    <detailData ref="detailDatas"></detailData>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .list {
    position: relative;
    margin-top: 10px;
    height: 750px;
    overflow-y: auto;

    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  data() {
    return {
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        {
          title: '调用时间',
          key: 'apiCheckTime',
          slot: 'apiCheckTime',
          tooltip: true,
          minWidth: 150,
        },
        { title: '是否成功', key: 'apiStatus', tooltip: true, minWidth: 150 },
        { title: '耗时', key: 'apiDuration', tooltip: true, minWidth: 150 },
        { title: '返回数据', key: 'option', slot: 'option', tooltip: true, minWidth: 150 },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: false,
      loading: false,
      bigPictureShow: false,
      msg: {},
    };
  },
  async mounted() {
    // await this.init()
  },

  methods: {
    show(id, apiIndex) {
      this.$refs.detailDatas.init(id, apiIndex);
    },
    info(item) {
      this.msg = item;
      this.init();
    },
    // 列表
    async init() {
      this.visible = true;
      let data = {
        resultId: this.msg.resultId,
        indexId: this.msg.indexId,
        pageSize: this.searchData.pageSize,
        pageNumber: this.searchData.pageNum,
        apiIndex: this.msg.apiIndex,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.InterdetailfaceList, data);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.apiStatus == 200) {
            i.apiStatus = '成功';
          } else {
            i.apiStatus = '失败';
          }
          i.apiDuration = (i.apiDuration % (1000 * 60)) / 1000 + ' 秒 ';
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    detailData: require('./detailData.vue').default,
  },
};
</script>
