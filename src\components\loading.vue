<template>
  <div class="cover" :style="{ background: bgColor }">
    <!-- <svg class="loading" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="40px" height="40px" viewBox="0 0 50 50" style="enable-background:new 0 0 50 50;" xml:space="preserve">
            <path :fill="color" d="M43.935,25.145c0-10.318-8.364-18.683-18.683-18.683c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615c8.072,0,14.615,6.543,14.615,14.615H43.935z">
                <animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="0.6s" repeatCount="indefinite" />
            </path>
        </svg> -->
    <img :src="loadingSrc" alt="" />
    <div class="text">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
.cover {
  z-index: 10;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  img {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateX(-50%) translateY(-50%);
    max-width: 100%;
    height: auto;
  }
}
.loading {
  z-index: 10;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
}
.text {
  height: calc(~'100% - 70px');
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 70px;
}
/*
    Set the color of the icon
*/
// svg path,
// svg rect {
//     fill: #ff6700;
// }
</style>

<script>
export default {
  data() {
    return {
      loadingSrc: '',
      bgColor: '',
    };
  },
  created() {},
  mounted() {
    if (['light'].includes(document.documentElement.getAttribute('data-theme'))) {
      this.loadingSrc = require('@/assets/img/common/loading-light.gif');
      this.bgColor = '#fff';
    } else if (['deepBlue'].includes(document.documentElement.getAttribute('data-theme'))) {
      this.loadingSrc = require('@/assets/img/common/loading-deepBlue.gif');
      this.bgColor = '#fff';
    } else {
      this.loadingSrc = require('@/assets/img/common/loading.gif');
      this.bgColor = '#071B39';
    }
  },
  methods: {},
  watch: {},
  computed: {},
  props: {
    color: {
      default: '#55c5f2',
    },
  },
  components: {},
};
</script>
