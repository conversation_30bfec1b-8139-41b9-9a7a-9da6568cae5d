// import parseTime, formatTime and set to filter
// export { parseTime, formatTime } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label;
  }
  return time + label + "s";
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time);
  if (between < 3600) {
    return pluralize(~~(between / 60), " minute");
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), " hour");
  } else {
    return pluralize(~~(between / 86400), " day");
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: "E" },
    { value: 1e15, symbol: "P" },
    { value: 1e12, symbol: "T" },
    { value: 1e9, symbol: "G" },
    { value: 1e6, symbol: "M" },
    { value: 1e3, symbol: "k" },
  ];
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (
        (num / si[i].value)
          .toFixed(digits)
          .replace(/\.0+$|(\.[0-9]*[1-9])0+$/, "$1") + si[i].symbol
      );
    }
  }
  return num.toString();
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0)
    .toString()
    .replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ","));
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * @description 公用过滤方法
 * @param {String} data 值
 * @param {Array} list 数据源列表
 * @param {String} keyName 键名
 * @param {String} valueName 值名
 * @param {String} nullValue 空值显示
 * row.type | commonFiltering(list)
 */
export function commonFiltering(data, list, keyName, valueName, nullValue) {
  const keyNames = keyName || "dataKey";
  const valueNames = valueName || "dataValue";
  const nullValues = nullValue || "--";
  if (!data && data !== 0) return nullValues;
  if (list.length === 0) return data;
  let arr = "";

  if (typeof data === "string") {
    arr = data.split("/");
  } else if (Object.prototype.toString.call(data) === "[object Array]") {
    arr = [...data];
  } else {
    arr = data.toString().split("/");
  }
  let str = "";
  if (arr.length > 1 && arr.length) {
    arr.forEach((item) => {
      for (const i of list) {
        if (item === i[keyNames]) {
          str += i[valueNames] + " / ";
          break;
        }
      }
    });
    return str.substring(0, str.length - 3);
  } else {
    for (const i of list) {
      if (data == i[keyNames]) {
        str = i[valueNames];
        break;
      } else {
        str = data;
      }
    }
    return str;
  }
}

/**
 * @description 文本内容超出显示省略号，超出显示tooltip提示
 * @param {string} msg 文本内容
 * @param {number} size 字体大小
 * @param {number} row 显示行数
 */
export function showToolTip(msg, size, parentDom, row = 1) {
  if (!msg || typeof msg === "undefined") return true;
  if (parentDom && parentDom.offsetWidth) {
    const app = document.querySelector("#app");
    const span = document.createElement("span");
    span.innerHTML = msg;
    span.style.fontSize = size + "px";
    app.appendChild(span);
    const isShow = span.offsetWidth > parentDom.offsetWidth * row;
    app.removeChild(span);
    return !isShow;
  }
}

/**
 * @description 验证8-20位数字或字母密码
 * @param {*} pwd 密码
 */
export const checkPwd = (pwd) => {
  const re = /^[a-zA-Z]\w{7,19}$/;
  if (re.test(pwd)) return true;
  else return false;
};

/**
 * @description 验证身份证号
 * @param {*} cardId 身份证号
 */
export const checkCardId = (cardId) => {
  // const idcard_patter = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  const idcard_patter =
    /^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/;
  if (idcard_patter.test(cardId)) return true;
  else return false;
};

/**
 * @description 验证手机号码
 * @param {Number} phoneNumber 手机号
 */
export const checkPhone = (phoneNumber) => {
  const phone_number_patter = /^1(3|4|5|7|8)\d{9}$/;
  if (phone_number_patter.test(phoneNumber)) return true;
  else return false;
};

/**
 * @description 验证邮箱
 * @param {*} email 邮箱
 */
export const checkEmail = (email) => {
  const email_patter =
    /^\w+((-\w+)|(\.\w+))*\@{1}\w+\.{1}\w{2,4}(\.{0,1}\w{2}){0,1}/gi;
  if (email_patter.test(email)) return true;
  else return false;
};

/**
 * @description 验证IP
 * @param {*} ip IP
 */
export const checkIp = (ip) => {
  const ip_patter =
    /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/gi;
  if (ip_patter.test(ip)) return true;
  else return false;
};

/**
 * @description 验证短号
 * @param {*} shortNumber 短号
 */
export const checkShortNumber = (shortNumber) => {
  const short_number_patter = /^\d{3,6}$/g;
  if (short_number_patter.test(shortNumber)) return true;
  else return false;
};
/**
 *
 * @param val
 * @return {number|string}
 */
export function filterNum(val) {
  if (!val) return 0;
  if (Number(val) < 0) return 0;
  if (Number(val) > 1) return 100;
  return (Number(val) * 100).toFixed(2);
}

/**
 *
 * @param imgUrl
 * @return {string}
 */
export function imgProxyToHttps(imgUrl) {
  if (!imgProxyPrefix || !/^https:\/\//.test(imgUrl)) return imgUrl;
  return `${imgProxyPrefix}${imgUrl}`;
  // imgUrl
  //   .replace(
  //     /((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.){3}(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])(?::(?:[0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?\//,
  //     "/"
  //   )
  //   .replace(/http(s)*:\/\//, imgProxyPrefix);
}

/**
 * @description 文件大小转换
 * @param {*} bytes
 */
export function fileSizeFormat(bytes) {
  if (bytes === 0) return "0 B";
  var k = 1024;
  var sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  var i = Math.floor(Math.log(bytes) / Math.log(k));
  var output = (bytes / Math.pow(k, i)).toFixed(1) + " " + sizes[i];
  return output;
}

/**
 * @description 毫秒数转成时间
 * @param {*} mss
 */
export function formatDuring(mss) {
  var days = parseInt(mss / (1000 * 60 * 60 * 24));
  var hours =
    days * 24 + parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60));
  var seconds = (mss % (1000 * 60)) / 1000;
  var time = `${hours < 10 ? `0${hours}` : hours}:${
    minutes < 10 ? `0${minutes}` : minutes
  }:${seconds < 10 ? `0${seconds}` : seconds}`;
  return time;
}

/**
 * 格式化长度
 * @param x - 带格式化的值
 * @param len - 要格式化的长度
 * @returns {string|*}
 * @private
 **/
var _formatLenth = function (x, len) {
  x = "" + x;
  len = len || 2;
  while (x.length < len) {
    x = "0" + x;
  }
  return x;
};

/**
 * @param {number} time
 * @param {string} noText 没有时间时显示为该字符串
 */
export function timeFormat(time, noText = "永久") {
  var date = new Date(time);
  return !time
    ? noText
    : date.getFullYear() +
        "-" +
        _formatLenth(date.getMonth() + 1) +
        "-" +
        _formatLenth(date.getDate()) +
        " " +
        _formatLenth(date.getHours()) +
        ":" +
        _formatLenth(date.getMinutes()) +
        ":" +
        _formatLenth(date.getSeconds());
}

/**
 * 将毫秒数转换成HH:mm:ss
 */
export function timeFormat_3(val, type) {
  if (!val) return "00:00:00";
  val = type === "EN" ? val * 1000 : val; //WM的处理时间单位为秒，因此转换为毫秒统一
  val /= 1000; //毫秒转换为秒
  var hours = Math.floor(val / 3600); //获取时间戳 差值对应的小时
  var minutes = Math.floor((val % 3600) / 60); //获取时间戳 差值对应的分钟
  var seconds = parseInt(
    Math.floor(((val % 3600) % 60) / 60) + (((val % 3600) % 60) % 60)
  ); //获取时间戳 差值对应的秒钟
  if (hours < 10) {
    hours = "0" + hours;
  }
  if (minutes < 10) {
    minutes = "0" + minutes;
  }
  if (seconds < 10) {
    seconds = "0" + seconds;
  }
  return hours + ":" + minutes + ":" + seconds; //获取视频时间总长度
}

/**
 * 毫秒转化为时分秒格式
 */
export function transTime(ms) {
  var time = parseFloat(ms) / 1000; //换算成秒
  var hh = 0,
    mm = 0,
    ss = 0;
  if (time != "") {
    if (time < 60) {
      hh = 0;
      mm = 0;
      ss = parseInt(time);
    } else if (time > 60 && time < 60 * 60) {
      hh = 0;
      mm = parseInt(time / 60.0);
      ss = parseInt((parseFloat(time / 60.0) - parseInt(time / 60.0)) * 60);
    } else if (time >= 60 * 60) {
      hh = parseInt(time / 3600.0);
      mm = parseInt((parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60);
      ss = parseInt(
        (parseFloat(
          (parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60
        ) -
          parseInt(
            (parseFloat(time / 3600.0) - parseInt(time / 3600.0)) * 60
          )) *
          60
      );
    }
  }
  return `${hh}时${mm}分${ss}秒`;
}
