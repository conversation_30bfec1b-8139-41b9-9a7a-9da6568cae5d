<template>
  <ui-modal v-model="visible" :title="modalAction.title" :styles="styles">
    <Form
      class="add-edit-form"
      ref="formRef"
      inline
      autocomplete="off"
      label-position="right"
      :rules="ruleCustom"
      :model="formCustom"
    >
      <FormItem :label="`知识名称`" prop="name">
        <Input
          type="text"
          class="input-width"
          v-model="formCustom.name"
          placeholder="请输入知识名称"
          :maxlength="20"
        ></Input>
      </FormItem>
      <FormItem label="知识内容" prop="description">
        <editor v-model="formCustom.description" ref="editorRef"></editor>
      </FormItem>
      <FormItem label="关联问题" class="relate-quetion">
        <ul class="quesition-ul">
          <li v-for="(item, index) in formCustom.relateQuetion" :key="index" class="mr-sm font-blue">
            {{ `#${item.errorMessage}#;` }}
          </li>
          <li>
            <Button type="primary" @click="addQuestion">
              <i class="icon-font icon-tianjia f-14"></i>
              <span class="vt-middle ml-sm">添加</span>
            </Button>
          </li>
        </ul>
      </FormItem>
      <FormItem prop="catalogueId" label="所属目录">
        <drop-ui-search-tree
          :select-tree="selectTree"
          :no-search="false"
          :show-checkbox="false"
          :simple-checked-nodes="true"
          node-key="id"
          placeholder="请选择所属目录"
          :tree-data="getCatalogTreeData"
          :current-node-key="currentNodeKey"
          :default-props="defaultProps"
          :defaultCheckedKeys="[]"
          expandAll
          @selectedTree="selectedTree"
        >
        </drop-ui-search-tree>
      </FormItem>
    </Form>
    <add-retive-question v-model="questionShow" @getRelateQuetion="getRelateQuetion"></add-retive-question>
    <template #footer>
      <Button class="plr-30" @click="cancel">取消</Button>
      <Button type="primary" @click="query(false)" :loading="saveLoading" class="plr-30">保存</Button>
      <Button type="primary" @click="query(true)" class="plr-30">发布</Button>
    </template>
  </ui-modal>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import knowledgebase from '@/config/api/knowledgebase';
export default {
  props: {
    value: {},
    modalAction: {
      default: () => {
        return {
          action: 'add',
          title: '新增知识',
          activeCatalog: {},
        };
      },
    },
  },
  data() {
    return {
      saveLoading: false,
      defaultProps: {
        id: 'id',
        label: 'name',
        children: 'children',
      },
      currentNodeKey: 0,
      questionShow: false,
      visible: false,
      styles: {
        width: '7rem',
      },
      formCustom: {
        name: '',
        description: '',
        catalogueId: null,
        relateQuetion: [],
      },
      selectTree: {},
      ruleCustom: {
        name: [
          {
            required: true,
            type: 'string',
            message: '请输入知识名称',
            trigger: 'blur',
          },
        ],
        catalogueId: [
          {
            required: true,
            type: 'number',
            message: '请选择所属目录',
            trigger: 'change',
          },
        ],
        description: [
          {
            required: true,
            type: 'string',
            message: '请输入知识内容',
            trigger: 'blur',
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    ...mapActions({
      setCatalogTreeData: 'knowledgebase/setCatalogTreeData',
    }),
    cancel() {
      this.visible = false;
    },
    release() {
      let editorHtml = this.$refs.editorRef.editorHtml;
      let params = {
        ...this.formCustom,
        description: editorHtml,
        questionIdList: this.formCustom.relateQuetion.map((item) => item.id),
      };
      this.$emit('directRelease', params);
    },
    resetFields() {
      this.selectTree = this.modalAction.activeCatalog;
      this.currentNodeKey = this.modalAction.activeCatalog.id;
      this.$refs.editorRef.editorHtml = '';
      this.formCustom = {
        name: '',
        catalogueId: this.modalAction.activeCatalog.id,
        description: '',
        relateQuetion: [],
      };
    },
    addQuestion() {
      this.questionShow = true;
    },
    getRelateQuetion(data) {
      this.formCustom.relateQuetion = data;
    },
    selectedTree(data) {
      this.formCustom.catalogueId = data.id;
    },
    query(isRelease = false) {
      this.$refs.formRef.validate(async (valid) => {
        if (!valid) return;
        try {
          this.saveLoading = false;
          let params = {
            ...this.formCustom,
            questionIdList: this.formCustom.relateQuetion.map((item) => item.id),
          };
          isRelease ? (params.publishStatus = '1') : (params.publishStatus = '0');
          this.isAdd ? null : (params.id = this.modalAction.editItem.id);
          const res = await this.$http.put(knowledgebase.updateKnowledgeData, params);
          this.$Message.success(res.data.msg);
          this.visible = false;
          this.$emit('update');
        } catch (err) {
          console.log(err);
        } finally {
          this.saveLoading = false;
        }
      });
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val) {
        this.resetFields();
        if (!this.isAdd) {
          this.formCustom.name = this.modalAction.editItem.name;
          this.formCustom.relateQuetion = this.modalAction.editItem.questionList;
          this.formCustom.catalogueId = this.modalAction.editItem.catalogueId;
          this.selectTree = {
            id: this.modalAction.editItem.catalogueId,
            name: this.modalAction.editItem.catalogueName,
          };
          this.currentNodeKey = this.modalAction.activeCatalog.id;
          this.$refs.editorRef.editorHtml = this.modalAction.editItem.description;
        }
      }
      this.visible = val;
    },
  },
  computed: {
    ...mapGetters({
      getCatalogTreeData: 'knowledgebase/getCatalogTreeData',
    }),
    isAdd() {
      return this.modalAction.action === 'add';
    },
  },
  components: {
    DropUiSearchTree: require('./drop-ui-search-tree.vue').default,
    Editor: require('./editor.vue').default,
    AddRetiveQuestion: require('./add-retive-question.vue').default,
  },
};
</script>
<style lang="less" scoped>
.add-edit-form {
  padding: 0 50px;
  @{_deep}.ivu-form-item {
    display: block;
  }
  .input-width {
    width: 800px;
  }
  .relate-quetion {
    margin-left: 14px /* no */;
  }
  .quesition-ul {
    display: flex;
  }
}
</style>
