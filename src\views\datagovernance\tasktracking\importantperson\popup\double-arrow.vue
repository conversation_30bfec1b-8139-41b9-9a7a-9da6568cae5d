<template functional>
  <div class="double-arrow-wrapper">
    <i class="arrow-left arrow"></i>
    <i class="arrow-right arrow"></i>
    <span class="arrow-title f-12">图像对比</span>
  </div>
</template>
<script>
export default {
  props: {},
};
</script>
<style lang="less" scoped>
.double-arrow-wrapper {
  width: 65px;
  height: 5px;
  background: var(--color-primary);
  position: relative;
  .arrow-title {
    position: absolute;
    top: -20px;
    left: 10px;
    color: var(--color-primary);
  }
  .arrow {
    width: 0;
    height: 0;
    display: inline-block;
    position: absolute;
    &.arrow-left {
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-right: 8px solid #2b84e2;
      left: -8px;
      top: -6px;
    }
    &.arrow-right {
      border-top: 8px solid transparent;
      border-bottom: 8px solid transparent;
      border-left: 8px solid #2b84e2;
      right: -8px;
      top: -6px;
    }
  }
}
</style>
