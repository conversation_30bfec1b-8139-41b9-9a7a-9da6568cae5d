<!--
    * @FileDescription: 云盘
    * @Author: H
    * @Date: 2023/5/17
    * @LastEditors: 
    * @LastEditTime: 
-->
<template>
	<div class="dom-wrapper">
		<div class="dom" @click="($event) => $event.stopPropagation()">
			<header>
				<span>图片信息</span>
				<Icon type="md-close" size="14" @click.native="() => $emit('close', $event)" />
			</header>
			<section class="dom-content">
				<div class="info-box">
					<div class="info-box-left">
						<ui-textOver-tips class="message title" refName="fileName" :content="datailsInfo.fileName"></ui-textOver-tips>
						<div class="through-record" v-if="checkStatus">
							<div class="wrapper-content">
								<span class="label">创建时间</span>：
								<ui-textOver-tips class="message" refName="createTime" :content="datailsInfo.createTime"></ui-textOver-tips>
							</div>
							<div class="wrapper-content">
								<span class="label">文件大小</span>：
								<span class="message">{{ datailsInfo.fileSize || '--' }}</span>
							</div>
                            <div class="wrapper-content">
								<span class="label">图片尺寸</span>：
								<span class="message">{{ datailsInfo.absTime || '--' }}</span>
							</div>
						</div>
					</div>
					<div class="info-box-right">
						<details-largeimg 
                            boxSeleType="rect" 
                            :info="{
                                'sceneImg': datailsInfo.fileUrl
                            } "
                            :btnJur= "['tp', 'rl', 'fd', 'sx', 'xz']"
                            @collection='collection' 
                            :collectionType="collectionType"
                            :algorithmType="1"
                            ></details-largeimg>
					</div>
				</div>
			</section>
			<footer>
				<i class="iconfont icon-doubleleft arrows mr-10 cursor-p" @click="handleLeft"></i>
				<div class="box-wrapper">
					<div class="present" id="present"></div>
					<ul class="list-wrapper" id="scroll-ul" :style="{'width': 120*cutList.length + 'px'}">
						<li class="list-box" @click="handleTab(item, index)" v-for="(item, index) in cutList" :key="index">
							<div class="img-box">
								<img v-lazy="item.fileUrl" alt="">
							</div>
						</li>
					</ul>
				</div>
				<i class="iconfont icon-doubleright arrows ml-10 cursor-p" @click="handleRight"></i>
			</footer>
		</div>
	</div>
</template>

<script>
	import { getPersonInfoByPersonId } from '@/api/operationsOnTheMap';
	import detailsLargeimg from './details-largeimg.vue';
	import { faceList } from './structuring.js';
	import { cutMixins } from './mixins.js';
	import { commonMixins } from '@/mixins/app.js';
	export default {
		name: '',
		mixins: [commonMixins, cutMixins], //全局的mixin
		components: {
			detailsLargeimg
		},
		data() {
			return {
				cutList: [],
				activeIndex: 0,
				faceList,
				transformWidth: 0,
				pageNum: 0,
				exampleImg: require('../../assets/img/login/bg.png'),
				rate: 1,
				datailsInfo: {},
				checkStatus: true,
				recordList: {},
				overlay: false,
				collectionType: 0,
				checkIndex: 0
			}
		},
		watch: {

		},
		computed: {
			totalSize() {
				return Math.ceil(this.cutList.length / 11)
			},
		},
		async created() {
			await this.getDictData()
		},
		mounted() {
		},
		methods: {
			// 开始数据
			init(row, list, index, type, page) {
				this.checkStatus = true;
				this.checkIndex = 0;
				this.cutList = list;
				this.activeIndex = index;
				this.datailsInfo = row;
				this.collectionType = type;
				this.$forceUpdate();
				let small_pic = document.getElementById("scroll-ul");
				small_pic.style.left = 0;
				this.setPageInfo({
					startPage: page,
					endPage: page
				})
				this.num = index;
				this.$nextTick(() => {
                    const divElement = document.querySelector('.list-wrapper');
                    const firstElement = divElement.firstChild;
                    console.log(firstElement.clientWidth, 'clientWidth');
                    this.imgBoxWidth = firstElement.clientWidth;
                    this.imageNumWidth = this.imgBoxWidth;
					this.locationPlay(index)
				})
			},
			// 切换
			handleTab(item, index) {
				this.checkIndex = 0;
				this.resetData();
				this.datailsInfo = item;
				this.activeIndex = index;
				this.play(index)
			},
			scrollIntoViews() {
				this.$nextTick(() => {
					let ul = document.querySelector('.list-wrapper');
					let li = [...ul.childNodes][this.activeIndex]
					li.scrollIntoView({
						behavior: "smooth",
					});
				})
			},
			collection(flag) {
				this.$set(this.cutList[this.activeIndex], 'myFavorite', flag)
			},
			async tabsChange(index) {
				if (this.checkIndex == index) {
					return
				}
				this.checkIndex = index;
				this.checkStatus = this.checkIndex == 0 ? true : false;
				if (!this.checkStatus) {
					if (!this.datailsInfo.vid) {
						// this.$Message.warning('暂无该人员档案信息');
						return
					}
					try {
						let res = await getPersonInfoByPersonId({ vid: this.datailsInfo.vid })
						if (res.code === 200 && !!res.data) {
							this.recordList = res.data || {}
						} else {
							throw error
						}
					} catch (error) {
						// this.$Message.warning('档案暂无数据')
					}
				}
			},
			toDetail() {
				if (!this.datailsInfo.vid) { return };
				const { href } = this.$router.resolve({
					name: 'video-archive',
					query: {
						archiveNo: this.datailsInfo.vid,
						source: 'video',
						initialArchiveNo: this.datailsInfo.vid
					}
				})
				// if (!this.datailsInfo.archiveNo) {
				//     this.$Message.warning('暂无该人员档案信息')
				//     return
				// }
				// const { href } = this.$router.resolve({
				//     name: 'people-archive',
				//     query: { 
				//         archiveNo: this.recordList.archiveNo,
				//         source: 'people',
				//         initialArchiveNo: this.recordList.archiveNo
				//     }
				// })
				window.open(href, '_blank')
			}
		}
	}
</script>

<style lang='less' scoped>
	@import "./style/index";
    .title{
        font-size: 18px;
        color: #2c86f8;
    }
</style>
