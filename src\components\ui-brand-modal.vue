<template>
  <ui-modal v-model="visible" title="车辆品牌" :r-width="1008" @onOk="comfirmHandle">
    <div class="brand-content">
      <div class="header">
        <span class="label-color">按品牌<span class="primary">拼音</span>首字母查找：</span>
        <div class="letter-ul">
          <div :class="!letter ? 'primary' : 'title-color'" class="letter-item" @click="letterHandle('')">全部</div>
          <div v-for="(item, $index) in letters" :key="$index" :class="letter === item ? 'primary' : 'title-color'" class="letter-item" @click="letterHandle(item)">{{ item }}</div>
        </div>
      </div>
      <div class="content">
        <div class="check-all">
          <Checkbox :indeterminate="indeterminate" :value="checkAll" @click.prevent.native="handleCheckAll">全选</Checkbox>
        </div>
        <CheckboxGroup v-model="checkAllGroup" @on-change="checkAllGroupChange">
          <Checkbox v-for="(item, $index) in vehicleBrandLists" :key="$index" :label="item.dataKey">
            <div v-if="vehicleBrandData[item.dataKey] && vehicleBrandData[item.dataKey].url" class="img-card card-border-color">
              <img :src="vehicleBrandData[item.dataKey].url" alt=""/>
            </div>
            <div v-else class="img-card default-logo-card card-border-color">
              <img src="@/assets/img/default-img/vehicle_brand_default.png" alt="" />
            </div>
            <div class="title-color ellipsis">{{ item.dataValue }}</div>
          </Checkbox>
        </CheckboxGroup>
        <ui-loading v-if="loading" />
        <ui-empty v-if="!loading && !vehicleBrandLists.length" />
      </div>
    </div>
  </ui-modal>
</template>
<script>
import { getVehicleBrandList } from '@/api/vehicleArchives'
import { vehicleBrandArray } from '@/libs/system'
import uiEmpty from './ui-empty.vue'
export default {
  components: { uiEmpty },
  data() {
    return {
      visible: false,
      letters: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'],
      letter: '',
      indeterminate: false,
      checkAll: false,
      checkAllGroup: [],
      loading: false,
      vehicleBrandData: {},
      vehicleBrandLists: []
    }
  },
  created() {
    this.vehicleBrandData = vehicleBrandArray;
  },
  methods: {
    show() {
      this.visible = true;
      this.requestVehicleBrandList()
    },
    letterHandle(item) {
      this.letter = item
      this.requestVehicleBrandList()
    },
    requestVehicleBrandList() {
      this.loading = true;
      this.vehicleBrandLists= [];
      getVehicleBrandList(this.letter)
        .then(res => {
          this.vehicleBrandLists = res.data
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false
        })
    },
    handleCheckAll() {
      if (this.indeterminate) {
        this.checkAll = false
      } else {
        this.checkAll = !this.checkAll
      }
      this.indeterminate = false
      if (this.checkAll) {
        this.checkAllGroup = this.vehicleBrandLists.map(v => {
          return v.dataKey
        })
      } else {
        this.checkAllGroup = []
      }
    },
    checkAllGroupChange(data) {
      if (data.length === this.vehicleBrandLists.length) {
        this.indeterminate = false
        this.checkAll = true
      } else if (data.length > 0) {
        this.indeterminate = true
        this.checkAll = false
      } else {
        this.indeterminate = false
        this.checkAll = false
      }
    },
    // 确定
    comfirmHandle() {
      if (this.checkAllGroup.length) {
        let list = this.checkAllGroup.map(item => item);
        this.$emit('on-change', list)
        this.visible = false;
      } else {
        this.$emit('on-change', [])
        this.visible = false;
        // this.$Message.warning('请选择车辆品牌')
      }
    },
    // 清空
    clearCheckAll() {
      this.indeterminate = false
      this.checkAll = false
      this.checkAllGroup = []
    }
  }
}
</script>
<style lang="less" scoped>
.brand-content {
  padding: 0 20px;
  .header {
    display: flex;
    align-items: center;
    .label-color {
      font-size: 14px;
      line-height: 20px;
    }
    .letter-ul {
      display: flex;
      .letter-item {
        font-size: 16px;
        line-height: 22px;
        padding: 0 8px;
        cursor: pointer;
      }
      .letter-item:hover {
        color: #4597ff;
      }
    }
  }
  .content {
    background: rgba(221, 225, 234, 0.29);
    border-radius: 4px;
    margin-top: 20px;
    padding: 10px;
    box-sizing: border-box;
    position: relative;
    .check-all {
      margin-left: 10px;
    }
    /deep/.ivu-checkbox-group {
      height: 400px;
      overflow: auto;
      .ivu-checkbox-wrapper {
        width: 70px;
        position: relative;
        margin: 10px;
        display: inline-flex;
        flex-direction: column;
        .ivu-checkbox {
          position: absolute;
          top: 3px;
          left: 3px;
        }
      }
    }
    .img-card {
      width: 70px;
      height: 70px;
      border: 1px solid #fff;
      box-sizing: border-box;
      border-radius: 4px;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &> img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .default-logo-card {
      &>img {
        width: 45px;
      }
    }
    .title-color {
      text-align: center;
      line-height: 20px;
      font-size: 14px;
      margin-top: 6px;
    }
  }
}
</style>
