<!--
 * @Date: 2025-01-15 10:53:49
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2025-02-26 13:52:06
 * @FilePath: \icbd-view\src\views\juvenile-archives\dashboard\components\collect\frequent-alarm.vue
-->
<template>
  <div class="frequent-box">
    <div class="head-img-box">
      <div class="score-box">{{ data.appearInRecreationCount }}</div>
      <div class="img-box">
        <img :src="data.photoUrl" alt="2313" />
      </div>
    </div>
    <div class="text-box">
      <div class="name-box">
        <span class="primary">{{ data.name }}</span
        >({{ data.age }}岁)
      </div>
      <div class="sub-text">(最后一次出现)</div>
      <div class="sub-text">{{ data.lastAppearInRecreationTime }}</div>
    </div>
  </div>
  <!-- <div style="display: flex; gap: 10px">

  </div> -->
</template>

<script>
export default {
  name: "FrequentAlarm",
  data() {
    return {};
  },
  props: {
    data: {
      type: Object,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.primary {
  color: #2c86f8;
}
.frequent-box {
  width: 120px;
  height: 130px;
  position: relative;
  cursor: pointer;
  &::after {
    content: "";
    width: 100%;
    height: 90px;
    display: block;
    position: absolute;
    bottom: 0;
    z-index: -1;
    background: linear-gradient(180deg, #eeeeee 0%, #ffffff 100%);
    border-radius: 4px 4px 4px 4px;
  }
  .head-img-box {
    position: relative;
    .score-box {
      position: absolute;
      top: 0;
      left: 10%;
      width: 30px;
      height: 30px;
      background: linear-gradient(237deg, #ec9240 0%, #f7b93d 100%);
      border-radius: 50%;
      text-align: center;
      line-height: 30px;
      color: white;
      font-weight: bold;
    }

    .img-box {
      margin: 0 auto;
      width: 70px;
      height: 70px;

      overflow: hidden;
      border: 1px solid #45e8ff;

      border-radius: 50%;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .text-box {
    width: 100%;
    text-align: center;
    font-size: 14px;
    text-wrap: nowrap;
    span {
      font-weight: bold;
    }

    .name-box {
      height: 20px;
      line-height: 20px;
    }
    .sub-text {
      height: 18px;
      line-height: 18px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.35);
      line-height: 16px;
    }
  }
}
</style>
