<template>
  <ui-modal width="450" v-model="modalShow" title="保存" class="relation-graph-save">
    <Form ref="formCustom" :model="formCustom" :label-width="80">
      <FormItem label="名称：" prop="name">
        <Input v-model="formCustom.name"></Input>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      modalShow: false,
      formCustom: { name: '' }
    }
  },
  computed: {},
  watch: {
    modalShow(val) {
      this.$emit('input', val)
    },
    value(val) {
      this.modalShow = val
    }
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {}
}
</script>
<style lang="less" scoped>
.relation-graph-save {
  /deep/.ivu-modal-wrap .ivu-modal .ivu-modal-content .ivu-modal-body {
    min-height: 0;
  }
  /deep/ .ivu-form-item {
    // margin-bottom: 0px;
  }
}
</style>
