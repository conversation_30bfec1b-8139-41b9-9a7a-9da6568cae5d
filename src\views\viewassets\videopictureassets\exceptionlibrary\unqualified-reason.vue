<template>
  <ui-modal v-model="visible" title="检测不合格原因" :styles="styles" footer-hide>
    <ui-table
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    ></ui-table>
    <ui-page
      class="page menu-content-background"
      :page-data="pageData"
      @changePage="changePage"
      @changePageSize="changePageSize"
    >
    </ui-page>
  </ui-modal>
</template>
<script>
import allInterface from '@/config/api/tasktracking';
export default {
  props: {},
  data() {
    return {
      visible: false,
      styles: {
        width: '6.5rem',
      },
      minusTable: 400,
      tableColumns: [
        { type: 'index', width: 50, title: '序号', align: 'center' },
        { key: 'errorMessage', title: '不合格原因' },
        { key: 'componentName', title: '检测规则' },
        { key: 'propertyName', title: '不合格字段' },
        { key: 'propertyValue', title: '实际结果' },
      ],
      loading: false,
      tableData: [],
      pageData: {
        pageNum: 1,
        pageSize: 10,
        totalCount: 0,
      },
      unqualifiedId: '',
    };
  },
  created() {},
  methods: {
    init(row) {
      this.visible = true;
      this.unqualifiedId = row.id;
      this.pageData.pageNum = 1;
      this.getTableData();
    },
    async getTableData() {
      this.loading = true;
      let params = {
        topicComponentId: null,
        importantPersonId: this.unqualifiedId,
        pageSize: this.pageData.pageSize,
        pageNum: this.pageData.pageNum,
      };
      let res = await this.$http.post(allInterface.queryPersonCheckResultDetail, params);
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    changePage(val) {
      this.pageData.pageNumber = val;
      this.getTableData();
    },
    changePageSize(val) {
      this.pageData.pageSize = val;
      this.getTableData();
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
