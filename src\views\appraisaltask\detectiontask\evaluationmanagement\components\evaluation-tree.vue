<template>
  <div class="evaluation">
    <div>
      <Input v-model="filterText" placeholder="请输入设备编码/名称" icon="ios-search" class="evaluation-keyword">
      </Input>
    </div>
    <div>
      <Select v-model="qualified" @on-change="chooseType(qualified)" class="evaluation-type">
        <Option :value="item.id" v-for="(item, index) in optionList" :key="index">{{ item.name }}</Option>
      </Select>
      <el-tree
        class="filter-tree"
        :data="nodeData"
        :default-expand-all="expandAll"
        @node-click="handleNodeClick"
        :filter-node-method="filterNode"
        ref="tree"
      >
        <span slot-scope="{ node, data }">
          <span :title="node.label" class="tree-label">{{ node.label }}</span>
          <span v-if="data.deviceName">{{ data.deviceName }}</span>
          <span v-if="data.total" class="tree-total">{{ data.total }}</span>
        </span>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  name: 'evaluation-tree',
  data() {
    return {
      filterText: '',
      deviceType: '',
      qualified: '0',
      optionList: [
        { name: '全部设备', id: '0' },
        { name: '合格设备', id: '1' },
        { name: '不合格设备', id: '2' },
        { name: '无法考核', id: '3' },
      ],
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {},
  methods: {
    //设备类型
    filterNode(value, data) {
      if (!value) return true;
      return data.deviceName.indexOf(value) !== -1;
    },

    onChangeDeviceType() {
      this.$emit('changeDeviceType', this.deviceType);
    },
    handleNodeClick(val) {
      this.$emit('clickNode', val, this.filterText);
    },
    chooseType(index) {
      if (index == 0) {
        index = '';
      }
      this.$emit('faceDetailIdx', index);
    },
  },
  // props: ["expandAll", "nodeData"],
  props: {
    expandAll: {},
    nodeData: {
      default() {
        return [];
      },
    },
    // nodeData: {
    //   type: Array,
    //   default: () => {
    //     return [];
    //   },
    // },
  },
};
</script>

<style lang="less" scoped>
.evaluation {
  .evaluation-keyword {
    margin-bottom: 10px;
    width: 265px;
  }
  .evaluation-type {
    margin-bottom: 10px;
    width: 265px;
  }

  @{_deep}.el-tree.el-tree-node:focus > .el-tree-node__content {
    background-color: #fff; //背景色
  }

  .filter-tree {
    width: 265px;
    height: 680px;
    overflow-y: scroll !important;
    //      @{_deep}.el-tree-node {
    //     @{_deep}.el-tree-node__children {
    //       width: 265px !important;
    //       height: 680px !important;
    //       overflow-y: scroll !important;
    //       @{_deep}.el-tree-node{
    //           height: 50px !important;
    //           width: 100% !important;
    //           @{_deep}.el-tree-node__children{
    //                height: 50px !important;
    //           width: 100% !important;
    //           }
    //       }
    //     }
    //   }
    // @{_deep}.el-tree-node__content:hover {
    //   background: #184f8d;
    // }
    // @{_deep}.el-tree-node__expand-icon.expanded {
    //   color: var(--color-primary);
    // }
    // @{_deep}.el-tree-node__content > .el-tree-node__expand-icon:not(.is-leaf) {
    //   color: var(--color-primary);
    // }
  }

  .tree-total {
    font-size: 14px;
    line-height: 24px;
    color: var(--color-bluish-green-text);
    margin-left: 20px;
  }
}
</style>
