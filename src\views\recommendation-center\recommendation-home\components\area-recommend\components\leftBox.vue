<!--
    * @FileDescription: 区域推荐
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <div class="search-box">
            <div class="title">
                <p>{{label}}</p>
            </div>
            <div class="search_condition">
                <div class="search_form">
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">视频身份</p>
                        </div>
                        <div class="search_content">
                            <Input v-model="queryParams.vid" class="wrapper-input"></Input>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">出现频次</p>
                        </div>
                        <div class="search_content">
                            <InputNumber v-model="queryParams.frequencyNum" class="wrapper-input"></InputNumber>
                            <span class="ml-10">次及以上</span>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">开始时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.startTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="开始时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="search_wrapper">
                        <div class="search_title">
                            <p class="search_strut">结束时间</p>
                        </div>
                        <div class="search_content">
                            <DatePicker v-model="queryParams.endTime" type="datetime" format="yyyy-MM-dd HH:mm:ss" placeholder="结束时间" transfer></DatePicker>
                        </div>
                    </div>
                    <div class="btn-group">
                        <Button type="primary" class="btnwidth" @click="handleSearch">查询</Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: '',
    data () {
        return {
            queryParams:{
                vid: '',
                frequencyNum: 5,
            }
        }
    },
    props: {
      label: {
        type: String,
        default: () => '区域推荐'
      }
    },
    async created() {
        this.queryParams.startTime = this.getAgoDay(30);
        this.queryParams.endTime = this.getAgoDay(0);
    },
    methods: {
        // 查询
        handleSearch() {
            this.queryParams.startTime = this.$dayjs(this.queryParams.startTime).format('YYYY-MM-DD HH:mm:ss');
            this.queryParams.endTime = this.$dayjs(this.queryParams.endTime).format('YYYY-MM-DD HH:mm:ss');
            this.$emit('search', this.queryParams)
        },
        // 时间判断
        getAgoDay(n){
            let date= new Date();
            let newDate = new Date(date.getTime() - n*24*60*60*1000);
            var Y = newDate.getFullYear() + '-';
            var M = (newDate.getMonth()+1 < 10 ? '0'+(newDate.getMonth()+1) : newDate.getMonth()+1) + '-';
            var D = newDate.getDate() < 10 ? '0'+ newDate.getDate() + ' ' : newDate.getDate() + ' ';
            var h = newDate.getHours() < 10 ? '0'+ newDate.getHours() + ':' : newDate.getHours() + ':';
            var m = newDate.getMinutes() < 10 ? '0'+ newDate.getMinutes() + ':' : newDate.getMinutes() + ':';
            var s = newDate.getSeconds() < 10 ? '0'+ newDate.getSeconds() : newDate.getSeconds();
            return Y+M+D+h+m+s;
        },
    }
}
</script>

<style lang='less' scoped>
@import './style/index';

/deep/ .ivu-date-picker{
    width: 100%;
}
/deep/ .ivu-tag-select-option{
    margin-right: 5px !important;
}
</style>
