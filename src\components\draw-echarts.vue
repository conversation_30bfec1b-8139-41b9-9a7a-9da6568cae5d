<template>
  <div ref="echarts" class="echarts height-full" :style="style"></div>
</template>
<style lang="less" scoped></style>
<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      echarts: null,
    };
  },
  created() {
    this.setDefaultStyle();
  },
  mounted() {},
  // 解决keep-alive 导致echarts显示异常
  async activated() {
    await this.$nextTick();
    this.echarts && this.echarts.resize();
  },
  methods: {
    checkOption() {
      let hasLoadingTag = typeof this.echartsLoading === 'boolean';
      let optionLength = this.echartOption ? Object.keys(this.echartOption).length : 0;
      if (hasLoadingTag || !!optionLength) {
        this.initEcharts();
      }
      // 有echartsLoding标志，并且没数据
      if (hasLoadingTag && !optionLength) {
        console.log('无初始化echarts数据');
      }
    },
    initEcharts() {
      if (this.echarts) {
        this.$echarts.dispose(this.$refs.echarts);
        this.echarts = null;
      }
      this.echarts = this.$echarts.init(this.$refs.echarts, 'themeEcharts');
      const option = this.dealEchartOption(this.$util.common.deepCopy(this.echartOption));
      this.echarts.setOption(option);
      //添加点击事件,传递出去--ning
      this.echarts.on('click', (params) => {
        this.$emit('echartClick', params);
      });
      this.echarts.on('mouseover', (params) => {
        this.$emit('echartMouseover', params);
      });
      this.echarts.on('mouseout', (params) => {
        this.$emit('echartMouseout', params);
      });
      // 图例点击事件
      this.echarts.on('legendselectchanged', (params) => {
        this.$emit('echartLegendselectchanged', params);
      });
      //右键
      // this.echarts.on('contextmenu',(params) => {
      //   this.$emit('contextmenu',params)
      // })
      window.addEventListener('resize', this.resize);
    },
    // echarts由4.x升级到5.x后 部分默认样式不符合要求这里统一更改
    dealEchartOption(option) {
      if (option?.tooltip) {
        if (!option.tooltip?.backgroundColor) {
          option.tooltip.backgroundColor = $var('--bg-tooltip');
        }
        if (!option.tooltip?.borderColor) {
          option.tooltip.borderColor = 'transparent';
        }
        if (!option.tooltip?.textStyle) {
          option.tooltip.textStyle = {
            color: $var('--color-base-text'),
          };
        }
      }
      return option;
    },
    resize() {
      let time = setTimeout(() => {
        this.echarts.resize();
        this.setDefaultStyle();
        time = null;
      }, 10);
    },
    setOption(option, bool) {
      this.echarts.setOption(option, bool);
    },
    showLoading() {
      let bgColor = '#08264d';
      let textColor = '#fff';
      if (['light', 'deepBlue'].includes(document.documentElement.getAttribute('data-theme'))) {
        bgColor = '#fff';
        textColor = 'rgba(0, 0, 0, 0.6)';
      }
      this.echarts.showLoading({
        //text: '加载中',
        color: '#2B84E2',
        textColor: textColor,
        maskColor: bgColor,
        zlevel: 0,
      });
    },
    hideLoading() {
      this.echarts.hideLoading();
    },
    //图例组件相关的行为，
    //https://echarts.apache.org/zh/api.html#action.legend
    dispatchAction(config) {
      if (!this.echarts) return;
      this.echarts.dispatchAction(config);
    },
    setDefaultStyle() {
      // 全局设置 echarts图标默认的样式
      this.$echarts.registerTheme('themeEcharts', {
        textStyle: {
          fontSize: this.$util.common.fontSize(12),
        },
        // backgroundColor: $var('--bg-echart'),
        tooltip: {
          backgroundColor: $var('--bg-tooltip'),
          textStyle: {
            color: $var('--color-base-text'),
          },
        },
        title: {
          textStyle: {
            color: $var('--color-axis-lable'),
          },
          subtextStyle: {
            color: $var('--color-axis-lable'),
          },
        },
        legend: {
          textStyle: {
            color: $var('--color-base-text'),
          },
        },
        categoryAxis: {
          nameTextStyle: {
            color: $var('--color-axis-lable'),
          },
          axisLabel: {
            color: $var('--color-axis-lable'),
          },
          // 轴线
          axisLine: {
            lineStyle: {
              color: $var('--color-axis-line'),
            },
          },
          // 刻度线
          axisTick: {
            lineStyle: {
              color: $var('--color-axis-line'),
            },
          },
          // 分隔线
          splitLine: {
            lineStyle: {
              color: $var('--color-split-line'),
            },
          },
        },
        valueAxis: {
          nameTextStyle: {
            color: $var('--color-axis-lable'),
          },
          axisLabel: {
            color: $var('--color-axis-lable'),
          },
          // 轴线
          axisLine: {
            lineStyle: {
              color: $var('--color-axis-line'),
            },
          },
          // 刻度线
          axisTick: {
            lineStyle: {
              color: $var('--color-axis-line'),
            },
          },
          // 分隔线
          splitLine: {
            lineStyle: {
              color: $var('--color-split-line'),
            },
          },
        },
      });
    },
  },
  watch: {
    echartOption: {
      handler(val) {
        this.$nextTick(() => {
          if (this.echarts) {
            const option = this.dealEchartOption(this.$util.common.deepCopy(val));
            this.setOption(option, true);
            this.echarts.resize();
          } else {
            this.checkOption();
          }
        });
      },
      immediate: true,
    },
    echartsLoading: {
      handler(val) {
        if (this.echarts) {
          val ? this.showLoading() : this.hideLoading();
        }
      },
      immediate: true,
    },
    getIsCollapsed() {
      let time = setTimeout(() => {
        if (this.echarts) this.echarts.resize();
        time = null;
      }, 200);
    },
    getFullscreen() {
      let time = setTimeout(() => {
        if (this.echarts) this.echarts.resize();
        time = null;
      }, 200);
    },
  },
  computed: {
    ...mapGetters({
      getIsCollapsed: 'common/getIsCollapsed',
      getFullscreen: 'home/getFullscreen',
    }),
    style() {
      let tempObj = this.$util.common.deepCopy(this.echartStyle);
      Object.keys(tempObj).forEach((key) => {
        if (tempObj[key].indexOf('px') !== -1) {
          tempObj[key] = parseInt(tempObj[key]) / 192 + 'rem';
        }
      });
      return tempObj;
    },
  },
  props: {
    echartStyle: {
      default() {
        return {
          height: '200px',
          width: '100%',
        };
      },
    },
    echartOption: {
      required: true,
    },
    echartsLoading: {
      default: null,
    },
  },
  components: {},
  beforeDestroy() {
    if (this.echarts) {
      this.$echarts.dispose(this.$refs.echarts);
      this.echarts = null;
    }
    window.removeEventListener('resize', this.resize);
  },
};
</script>
