<template>
  <div class="dom-wrapper">
    <i class="icon-font icon-guanbi close" @click="() => $emit('close')"></i>
    <div class="dom">
      <slot name="mapbubble">
        <section class="dom-content">
          <p class="dom-content-p">
            <span class="label">{{ global.filedEnum.deviceId }}</span
            >：
            <span class="message-active" @click="deviceArchives(mapDomData)" title="跳转档案">
              {{ mapDomData.deviceId }}
            </span>
            <span class="check-status" :class="[mapDomData.checkStatus === '1' ? 'bg-success' : 'bg-color-grey']">
              {{ mapDomData.checkStatus === '1' ? '在线' : '离线' }}
            </span>
          </p>
          <p class="dom-content-p">
            <span class="label">{{ global.filedEnum.deviceName }}</span
            >：
            <span class="message">{{ mapDomData.deviceName }}</span>
          </p>
          <p class="dom-content-p">
            <span class="label">组织机构</span>：
            <span class="message">{{ mapDomData.orgName }}</span>
          </p>
          <p class="dom-content-p">
            <span class="label">{{ global.filedEnum.longitude }}</span
            >：
            <span class="message">{{ mapDomData.lon }}</span>
          </p>
          <p class="dom-content-p">
            <span class="label">{{ global.filedEnum.latitude }}</span
            >：
            <span class="message">{{ mapDomData.lat }}</span>
          </p>
          <p class="dom-content-p">
            <span class="label">安装地址</span>：
            <span class="message">{{ mapDomData.address }}</span>
          </p>
          <p class="dom-content-p" v-show="mapDomData.errorMessage && mapDomData.errorMessage.length">
            <span class="label">错误类别</span>：
            <span class="exception-type">
              <template v-for="(e, i) in mapDomData.errorMessage">
                <span :key="i">{{ e }}</span>
                <span :key="i" v-if="i !== mapDomData.errorMessage.length - 1">、</span>
              </template>
            </span>
          </p>
          <p class="dom-content-p" v-show="mapDomData.tagList && mapDomData.tagList.length > 0">
            <span class="label">设备标签</span>：
            <span class="device-tag">
              <template v-for="e in list">
                <span class="message-tag" :key="e.tagId" ref="tag"> {{ e.tagName }}</span>
              </template>
              <span class="tag-ellipsis" v-show="showEllipsis">...</span>
            </span>
          </p>
        </section>
        <div class="img-box" v-show="mapDomData.imageUrls && mapDomData.imageUrls.length > 0">
          <img :src="item" v-for="item in mapDomData.imageUrls" :key="item.deviceID" />
          <!-- <look-scene
            v-model="visibleScence"
            :img-list="mapDomData.imageUrls"
            :view-index="viewIndex"
          ></look-scene> -->
        </div>
      </slot>
    </div>
    <div class="triangle"></div>
  </div>
</template>
<script>
export default {
  props: {
    mapDomData: {},
  },
  watch: {
    // 'mapDomData.tagList': {
    //   handler(val) {
    //     this.list = [...val]
    //     let width = 0
    //     this.$nextTick(() => {
    //       let array = this.$refs['tag']
    //       for (let i = 0; i < array.length; i++) {
    //         width = width + array[i].clientWidth + 8
    //         if (width >= 402) {
    //           this.$nextTick(() => {
    //             this.list = val.slice(0, i)
    //           })
    //           this.showEllipsis = true
    //           break
    //         } else {
    //           this.showEllipsis = false
    //         }
    //       }
    //     })
    //   },
    // },
  },
  data() {
    return {
      visibleScence: false,
      viewIndex: 0,
      showEllipsis: true,
      list: [],
    };
  },
  methods: {
    lookScence(index) {
      this.viewIndex = index;
      this.visibleScence = true;
    },
    handleCheckStatus(row) {
      const flag = {
        0: '未编辑',
        1: '已编辑',
        2: '检测异常',
      };
      return flag[row];
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { deviceId: item.deviceId },
      });
      window.open(routeData.href, '_blank');
    },
  },
  components: {
    // LookScene: require('@/components/look-scene.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .dom-wrapper {
    .triangle {
      background-color: #07295d;
      border: 2px solid #2967c8;
      border-right: transparent;
      border-bottom: transparent;
    }
  }
  .dom {
    background: #07295d;
    border: 2px solid #2967c8;
    box-shadow: 0px 3px 10px #000000;
    .dom-content {
      color: #f5f5f5;
      .dom-content-p {
        .check-status {
          color: #fff;
        }
        .label {
          color: #f5f5f5;
        }
        .exception-type,
        .message {
          color: #b4ceef;
        }
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .dom-wrapper {
    .triangle {
      background-color: #ffffff;
    }
  }
  .dom {
    background: #ffffff;
    margin: 0 6px;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    .dom-content {
      color: rgba(0, 0, 0, 0.8);
      .dom-content-p {
        .check-status {
          color: rgba(0, 0, 0, 0.8);
        }
        .label {
          color: rgba(0, 0, 0, 0.8);
        }
        .exception-type,
        .message {
          color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
}

.dom-wrapper {
  position: relative;
  padding: 10px 28px 0 0;
  height: 100%;
  .close {
    position: absolute;
    right: 0px;
    top: -6px;
    width: 24px;
    font-size: 24px;
    color: #768192;
  }
  .triangle {
    width: 10px;
    height: 10px;
    position: relative;
    left: 35px;
    top: -5px;
    transform: rotate(224deg);
    border-right: 0px;
    border-bottom: 0px;
  }
}
.dom {
  width: 309px;
  border-radius: 8px;
  .dom-title {
    color: var(--color-active);
    border-left: 4px solid var(--color-active);
    padding-left: 10px;
  }
  .dom-content {
    padding: 8px 8px 8px 11px;
    .dom-content-p {
      margin-top: 6px;
      width: 286px;
      vertical-align: text-top;
      display: flex;
      position: relative;
      .check-status {
        position: absolute;
        right: 0;
      }
      .label {
        display: inline-block;
        white-space: nowrap;
        width: 50px;
        text-align: justify;
        text-align-last: justify;
        text-justify: inter-ideograph;
      }
      .message-active {
        color: var(--color-active);
        cursor: pointer;
      }
      .message {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .exception-type {
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2; // 控制多行的行数
        -webkit-box-orient: vertical;
      }
      .device-tag {
        display: flex;
        flex-wrap: wrap;
        margin-top: -8px;
        .message-tag {
          padding: 3px 10px;
          background: var(--color-active);
          border-radius: 4px;
          color: var(--color-content);
          margin-right: 8px;
          margin-top: 8px;
        }
        .tag-ellipsis {
          height: 16px;
          margin-top: 8px;
          color: var(--color-active);
        }
      }
    }
  }
  .img-box {
    max-height: 305px;
    min-height: 98px;
    overflow: hidden;
    overflow-y: auto;
    > img {
      width: 90px;
      height: 90px;
      margin-left: 8.6px;
      margin-bottom: 3px;
    }
  }
}
</style>
