const serviceName = '/ivdg-asset-app';

export default {
  getAllDeviceTagByCondition: serviceName + '/deviceTag/pageList', // 查询所有重点场所列表
  addImportantArea: serviceName + '/importantArea/add', // 新增/修改重点场所目录
  getImportantAreaDevicePage: serviceName + '/importantArea/getImportantAreaDevicePage', // 查询重点场所关联设备
  getAllImportantArea: serviceName + '/importantArea/getAllImportantArea', // 分页查询重点场所目录
  deleteImportantArea: serviceName + '/importantArea/deleteImportantArea', // 删除重点场所目录
  removeImportantArea: serviceName + '/importantArea/removeImportantArea', // 移除设备重点场所关联目录
  addImportantAreaDevice: serviceName + '/importantArea/addImportantAreaDevice', // 新增重点场所关联目录
  getPageDeviceList: serviceName + '/device/pageList', //分页获取设备信息列表

  addCustomArea: serviceName + '/customArea/addCustomArea', // 新增目录类型
  updateCustomArea: serviceName + '/customArea/updateCustomArea', // 修改自定义目录
  getAllCustomAreaTree: serviceName + '/customArea/getAllCustomAreaTree', // 查询所有自定义目录列表

  getCustomAreaTree: serviceName + '/deviceDirectory/treeByOrgCode', //  按目录选择
  addCustomAreaTree: serviceName + '/customAreaTree/addCustomAreaTree', // 新增自定义目录树
  addCustomAreaTreeDevice: serviceName + '/customAreaTree/addCustomAreaTreeDevice', // 自定义目录关联设备
  deleteCustomAreaTree: serviceName + '/customAreaTree/deleteCustomAreaTree', // 删除自定义树节点
  deleteCustomArea: serviceName + '/customArea/deleteCustomArea', // 删除自定义目录
  getCustomAreaTreeDevicePage: serviceName + '/customAreaTree/getCustomAreaTreeDevicePage', // 查询自定义树关联设备
  updateCustomAreaTree: serviceName + '/customAreaTree/updateCustomAreaTree', // 修改自定义树节点
  removeCustomAreaTree: serviceName + '/customAreaTree/removeCustomAreaTree', // 移除设备关联目录
  downloadTemplate: serviceName + '/device/downloadTemplate', // 导入异常设备

  // getOrganizationDevice: '/iBDGDataPropertyApp/organizationDevice/getOrganizationDevice', // 查询组织关联设备-分页展示
  // addOrganizationDevice: '/iBDGDataPropertyApp/organizationDevice/addOrganizationDevice', // 新增组织关联设备
  // removeOrganizationDevice: '/iBDGDataPropertyApp/organizationDevice/removeOrganizationDevice', // 移除组织关联设备
  // 目录管理
  deviceDirectoryAdd: serviceName + '/deviceDirectory/add', // 新增目录 POST
  deviceDirectoryUpdate: serviceName + '/deviceDirectory/update', // 目录编辑 PUT
  deviceDirectoryPageList: serviceName + '/deviceDirectory/pageList', // 分页查询目录列表 POST
  deviceDirectoryTree: serviceName + '/deviceDirectory/treeByOrgCode', // 分页查询目录列表  get
  deviceDirectoryDelete: serviceName + '/deviceDirectory/remove/', // 分页查询目录列表 delete {ids}
  // 目录关联设备管理
  dirDeviceLink: serviceName + '/deviceDirectory/device/link', // 目录关联设备 post
  dirDevicePageList: serviceName + '/deviceDirectory/device/pageList', // 目录设备列表 post
  dirDeviceRemove: serviceName + '/deviceDirectory/device/remove/', // 目录设备列表 delete {ids}
  dirDeviceUpdate: serviceName + '/deviceDirectory/device/update', // 目录设备更新 put
  importDeviceLink: serviceName + '/deviceDirectory/device/importDeviceLink', // 导入的设备关联目录 post
  postExportByDirectory: serviceName + '/deviceDirectory/device/exportByDirectory', // 根据目录导出设备
  postUnlinkDirectory: serviceName + '/deviceDirectory/device/unlinkDirectory', // 删除设备关联目录
};
