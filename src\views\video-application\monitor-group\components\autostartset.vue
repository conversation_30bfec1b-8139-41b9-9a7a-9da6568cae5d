<template>
  <div class="autostartset-wrap">
    <div class="autostartset-header">
      自启动设置<i>(可设置5个时间段，请避免与其他预案时间冲突)</i>
    </div>
    <div class="autostartset-content">
      <div v-scroll style="height: 100%">
        <ul class="autostartset-time-wrap">
          <li
            class="autostartset-time-item"
            v-for="(list, index) in autoStartTimeList"
            :key="index"
          >
            <div class="time-set-left fl">时段{{ index + 1 }}</div>
            <div class="time-set-content fl">
              <p class="timeset">
                <i class="time">时间</i>
                <el-time-picker
                  size="small"
                  placeholder="开始时间"
                  v-model="list.startTime"
                  :picker-options="pickOptions"
                  style="width: 150px; height: auto; margin-left: 40px"
                >
                </el-time-picker>
                <span class="fr reset-btn" @click="resetRawl(list)">重置</span>
              </p>
              <p class="repeat">
                <i class="repeat-num">重复</i>
                <span
                  v-for="(week, index) in weekList"
                  :key="index"
                  class="week-item"
                  :class="{ active: list.week.indexOf(week) > -1 }"
                  @click="selectWeekRepeat(list, week)"
                  >每{{ week }}</span
                >
              </p>
            </div>
            <div class="time-set-addreduce fl">
              <span>
                <i
                  class="iconfont icon-jia"
                  @click="addAutoStartList()"
                  v-show="index === autoStartTimeList.length - 1"
                ></i>
                <i
                  class="iconfont icon-jian"
                  @click="reduceAutoStartList(index)"
                  v-show="autoStartTimeList.length !== 1"
                ></i>
              </span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import Manage from "../js/manage.js";
export default {
  name: "autostartset",
  data() {
    return {
      autoStartTimeList: [
        {
          startTime: "",
          endTime: "",
          week: [],
        },
      ],
      pickOptions: {
        selectableRange: "00:00:00 - 23:59:59",
      },
      weekList: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
    };
  },
  created() {
    Manage.add("step3", this);
  },
  methods: {
    selectWeekRepeat(list, week) {
      var index = list.week.findIndex((i) => {
        return i === week;
      });
      (index > -1 && list.week.splice(index, 1)) || list.week.push(week);
    },
    validateTimeWhole() {
      let validateTime = JSON.parse(JSON.stringify(this.autoStartTimeList));
      let validateParams = validateTime[validateTime.length - 1];
      /*if ((!validateParams.startTime && validateParams.endTime) || (validateParams.startTime && !validateParams.endTime) || (!validateParams.startTime && !validateParams.endTime && validateParams.week.length !== 0)) {
				this.$Message.warning("请填写完整的时段");
				return false;
			}
            if (validateParams.startTime > validateParams.endTime) {
				this.$Message.warning("开始时间不能大于结束时间");
				return false;
			}*/
      if (
        !validateParams.startTime ||
        (!validateParams.startTime && validateParams.week.length !== 0)
      ) {
        this.$Message.warning("请填写完整的时段");
        return false;
      }
      if (validateParams.week.length === 0) {
        this.$Message.warning("请选择重复周期");
        return false;
      }
      return true;
    },
    validateTimeHandler() {
      var msg;
      var validateTimeArr = JSON.parse(JSON.stringify(this.autoStartTimeList));
      var isTrue = true;
      if (validateTimeArr.length > 1) {
        validateTimeArr.some((item, index) => {
          if (index + 1 === validateTimeArr.length) {
            return true;
          }
          var isValidate = this.validateStep(validateTimeArr[index + 1], item);
          if (isValidate) {
            msg = `时间段${index + 1}与时间段${index + 2}冲突`;
            return true;
          }
        });
        if (msg) {
          this.$Message.warning(msg);
          isTrue = false;
        }
      }
      return isTrue;
    },
    validateStep(cmp1, cmp2) {
      var isValidate = false;
      cmp1.week.forEach((_i) => {
        cmp2.week.forEach((_l) => {
          if (_i === _l) {
            if (
              (this.compaerDate(cmp1.startTime, cmp2.startTime) &&
                !this.compaerDate(cmp1.startTime, cmp2.endTime)) ||
              (this.compaerDate(cmp1.endTime, cmp2.startTime) &&
                !this.compaerDate(cmp1.endTime, cmp2.endTime))
            ) {
              isValidate = true;
              return;
            }
          }
        });
      });
      return isValidate;
    },
    addAutoStartList() {
      if (this.autoStartTimeList.length >= 5) {
        this.$Message.warning("最多可设置5个时间段");
        return;
      }
      if (!this.validateTimeWhole()) {
        return;
      }
      this.autoStartTimeList.push({
        startTime: "",
        endTime: "",
        week: [],
      });
    },
    checkAutoStartTimeList() {
      return (
        this.autoStartTimeList.length === 1 &&
        !this.autoStartTimeList[0].startTime &&
        /* !this.autoStartTimeList[0].endTime &&*/
        this.autoStartTimeList[0].week.length === 0
      );
    },
    reduceAutoStartList(index) {
      this.autoStartTimeList.splice(index, 1);
    },
    getAutoStartTimeData() {
      //无启动时间，可以保存
      if (this.checkAutoStartTimeList()) return this.autoStartTimeList;
      //校验合法
      if (!this.validateTimeWhole()) {
        return;
      }
      //校验重复
      if (!this.validateTimeHandler()) return false;
      // 出去的时候需要处理成 时：分的形式
      this.autoStartTimeList.map((item) => {
        if (item.startTime) {
          const time = new Date(item.startTime);
          item.startTime =
            time.getHours().toString().padStart(2, 0) +
            ":" +
            time.getMinutes().toString().padStart(2, 0);
        }
      });
      return this.autoStartTimeList;
    },
    setDefaultStatus() {
      this.isEdit = false;
      this.autoStartTimeList = [
        {
          startTime: "",
          endTime: "",
          week: [],
        },
      ];
    },
    compaerDate(t1, t2) {
      var date = new Date(),
        a = t1.split(":"),
        b = t2.split(":");
      return date.setHours(a[0], a[1]) > date.setHours(b[0], b[1]);
    },
    setData(data) {
      this.isEdit = true;
      this.editId = data.id;
      data.tourTime.forEach((_i) => {
        _i.week = _i.week === "" ? [] : _i.week.split(",");
      });
      // 时分秒组件的传参需要进行处理value1: new Date(2016, 9, 10, 18, 40),
      data.tourTime.map((item) => {
        if (item.startTime) {
          const date = new Date();
          const time = item.startTime.split(":");
          item.startTime = new Date(
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            time[0] * 1,
            time[1] * 1,
            0
          );
        }
      });
      this.autoStartTimeList = data.tourTime;
    },
    resetRawl(list) {
      list.startTime = "";
      list.endTime = "";
      list.week = [];
    },
  },
};
</script>
<style lang="less">
.fr {
  float: right;
}
.el-time-panel__content::before,
.el-time-panel__content::after {
  margin-top: -7px !important;
  padding-top: 0 !important;
  line-height: 32px !important;
  height: 32px !important;
}
.autostartset-wrap {
  width: 100%;
  height: 100%;
  padding: 0 20px;
  .autostartset-header {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #666666;
    font-weight: bold;
    border-bottom: 1px solid rgba(165, 176, 182, 0.7);
    i {
      color: #2c86f8;
    }
  }
  .autostartset-content {
    width: 100%;
    height: calc(~"100% - 40px");
    .autostartset-time-wrap {
      width: 100%;
      margin: 0 auto;
      .autostartset-time-item {
        width: 60%;
        height: 100px;
        margin: 0 auto;
        margin-top: 20px;
        display: flex;
      }
      .time-set-left {
        color: #fff;
        width: 10%;
        height: 100%;
        line-height: 100px;
        text-align: center;
        font-size: 14px;
        background: #2c86f8;
      }
      .time-set-content {
        width: 80%;
        height: 100%;
        padding: 0 10px;
        border: 1px solid #2c86f8;
        .timeset,
        .repeat {
          height: 50px;
          line-height: 50px;
          .week-list-wrap {
            display: inline-block;
            vertical-align: middle;
            width: 90%;
          }
          .week-item {
            padding: 3px 7px;
            margin-left: 9px;
            border: 1px solid rgba(165, 176, 182, 0.7);
            border-radius: 10%;
            cursor: pointer;
            color: #a5b0b6;
            &:hover {
              background-color: #2c86f8;
              color: #fff;
            }
            &.active {
              background-color: #2c86f8;
              color: #fff;
            }
          }
        }
        .timeset {
          // .el-time-panel__content {
          // 	&::before, &::after{
          // 		margin-top: 0 !important;
          // 		padding-top: 0 !important;
          // 		line-height: 32px !important;
          // 	}

          // }
          border-bottom: 1px dashed rgba(165, 176, 182, 0.7);
          .el-input__icon.el-icon-time {
            display: none;
          }
          .reset-btn {
            color: #2c86f8;
            cursor: pointer;
          }
        }
      }
      .time-set-addreduce {
        width: 10%;
        height: 100%;
        span {
          display: block;
          text-align: center;
          width: 50px;
          line-height: 100px;
          color: #2c86f8;
          cursor: pointer;
        }
      }
    }
  }
}
.el-picker-panel.time-select.el-popper {
  z-index: 10002 !important;
}
</style>
