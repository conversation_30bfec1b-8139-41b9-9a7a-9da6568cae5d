<!--
    * @FileDescription: 
    * @Author: H
    * @Date: 2024/01/31
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
    <div class="leftBox">
        <searchBox ref="searchBox" @seleArea="seleArea" @searchList="querySearch" @packBox="handlePackup"></searchBox>
    </div>
</template>

<script>
import searchBox from './searchBox.vue';
import operaFloor from '../../../components/operat-floor/index.vue';
import imgList from '../../components/imgList/index.vue';
export default {
    name: '',
    components:{
        searchBox,
        operaFloor,
        imgList,
    },
    props: {
    },
    data () {
        return {
            loading: false,
            objectIndex: -1,
            loadingText: false,
            noMore: false,
            total:0,
            searchInfo: {},
            typaTag: {},
            top: 0,
        }
    },
    watch:{
            
    },
    computed:{
    },
    created() {
          
    },
    mounted(){
        this.objHeight();
    },
    methods: {
        // 高度
        objHeight() {
            setTimeout(() => {
                this.top = document.querySelector('.search_box').scrollHeight + 20;
            }, 210)
        },
        // 
        handlePackup() {
            this.objHeight();
        },
        querySearch(item, tab, secTab) {
            this.$emit('search', item)
        },
        seleArea() {
            this.$emit('seleArea')
        },
        init(item, tabIndex, isFirst= true) {

        },
        // 反显设备
        showDevice (list) {
            list.map(item =>{
                item.select = true;
            })
            this.$refs.searchBox.selectData(list);
        },
    }
}
</script>

<style lang='less' scoped>
// @import './style/index';
// @import '../../components/style/boxContent';
.leftBox{
    position: absolute;
    top: 10px;
    left: 10px;
    height: calc(~ '100% - 20px' );
}
</style>
