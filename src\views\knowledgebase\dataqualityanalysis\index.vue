<template>
  <div class="equipment-analysis auto-fill">
    <underline-menu v-model="governanceContent" :data="governanceContentData" @on-change="search"></underline-menu>
    <div class="analysis-title">
      <div class="base-text-color">
        <span>分析时间：</span>
        <span>{{ analysisTime }}</span>
      </div>
      <Button
        type="primary"
        @click="analysis"
        :loading="analysisLoading"
        v-permission="{
          route: $route.name,
          permission: 'analysis',
        }"
      >
        <i class="icon-font icon-shebeifenxi02"></i>
        <span class="inline ml-xs">{{ analysisLoading ? '分析中...' : '设备分析' }}</span>
      </Button>
    </div>
    <div class="search-content">
      <ui-label class="inline mr-lg mb-sm" label="组织机构">
        <api-organization-tree
          ref="orgTree"
          :select-tree="selectOrgTree"
          :custorm-node="true"
          :custorm-node-data="custormNodeData"
          @selectedTree="selectedOrgTree"
          placeholder="请选择组织机构"
        >
        </api-organization-tree>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="行政区划">
        <api-area-tree
          :select-tree="selectTree"
          @selectedTree="selectedArea"
          placeholder="请选择行政区划"
        ></api-area-tree>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceId}`">
        <Input class="width-md" v-model="searchData.deviceId" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.deviceName}`">
        <Input class="width-md" v-model="searchData.deviceName" placeholder="请输入设备名称"></Input>
      </ui-label>
      <!-- 设备状态 -->
      <ui-label class="inline mr-lg mb-sm" :label="global.filedEnum.phyStatus">
        <Select
          class="width-sm"
          v-model="searchData.phyStatus"
          :placeholder="`请选择${global.filedEnum.phyStatus}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in phystatusList" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label class="inline" label="设备重点类型">
        <Select class="width-sm" v-model="searchData.isImportant" clearable placeholder="请选择设备重点类型">
          <Option label="普通设备" :value="0"></Option>
          <Option label="重点设备" :value="1"></Option>
        </Select>
      </ui-label>
      <!-- 监控点位类型 -->
      <ui-label class="inline mr-lg mb-sm" :label="global.filedEnum.sbdwlx">
        <Select
          class="width-md"
          v-model="searchData.sbdwlx"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in propertySearchLbdwlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>

      <!-- 摄像机功能类型 -->
      <ui-label class="inline mr-lg mb-sm" :label="global.filedEnum.sbgnlx">
        <Select
          class="width-md"
          v-model="searchData.sbgnlx"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
          clearable
          :max-tag-count="1"
        >
          <Option v-for="(item, index) in propertySearchSxjgnlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <!-- 摄像机采集区域 -->
      <ui-label class="inline mr-lg mb-sm" :label="`${global.filedEnum.sbcjqy}列表`">
        <Button type="dashed" class="area-btn" @click="clickArea"
          >请选择采集区域 {{ `已选择 ${searchData.sbcjqyList.length}个` }}
        </Button>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="数据来源">
        <Select class="width-md" v-model="searchData.sourceId" placeholder="请选择数据来源" clearable>
          <Option v-for="(item, index) in sourceList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="search">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="mb-sm button-div">
      <ui-select-tabs
        class="ui-select-tabs"
        :list="tagList"
        @selectInfo="selectInfo"
        ref="uiSelectTabs"
      ></ui-select-tabs>
      <div class="fr">
        <Button
          type="primary"
          @click="batchAddLabel"
          v-permission="{
            route: $route.name,
            permission: 'batchAddLabel',
          }"
        >
          <i class="icon-font icon-piliangtianjiabiaoqian"></i>
          <span class="inline ml-xs">批量添加标签</span></Button
        >
        <Button
          v-permission="{
            route: $route.name,
            permission: 'shareDevice',
          }"
          class="ml-sm"
          type="primary"
          @click="shareDevice"
          :loading="shareLoading"
        >
          <i class="icon-font icon-zichangongxiang"></i>
          <span class="inline ml-xs">资产共享</span></Button
        >
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template #Index="{ row, index }">
          <div :class="row.rowClass">
            <span>{{ index + 1 }}</span>
          </div>
        </template>
        <template #deviceId="{ row }">
          <span class="width-percent inline ellipsis" :class="row.rowClass">{{ row.deviceId }}</span>
        </template>
        <template #deviceName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.deviceName">
            {{ row.deviceName }}
          </div>
        </template>
        <template #orgName="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.orgName">
            {{ row.orgName }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #ipAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.ipAddr">
            {{ row.ipAddr }}
          </div>
        </template>
        <template #macAddr="{ row }">
          <div class="width-percent inline ellipsis" :class="row.rowClass" :title="row.macAddr">
            {{ row.macAddr }}
          </div>
        </template>
        <template #sbgnlx="{ row }">
          <Tooltip
            placement="top"
            :content="row.sbgnlx | filterTypeMore(propertySearchSxjgnlx)"
            :disabled="row.sbgnlx.length < 2"
          >
            <div class="tooltip-type">
              {{ row.sbgnlx | filterTypeMore(propertySearchSxjgnlx) }}
            </div>
          </Tooltip>
        </template>
        <template #sbdwlx="{ row }">
          <span>{{ row.sbdwlx | filterType(propertySearchLbdwlx) }}</span>
        </template>
        <template #phyStatus="{ row }">
          <span
            :style="{
              color: row.phyStatus === '1' ? '#0E8F0E' : '#BC3C19',
            }"
            >{{ row.phyStatus | filterType(phystatusList) }}</span
          >
        </template>
        <template #sourceName="{ row }">
          <div
            class="width-percent inline ellipsis"
            :class="row.rowClass"
            :title="row.sourceId | filterSource(sourceList || [])"
          >
            {{ row.sourceId | filterSource(sourceList || []) }}
          </div>
        </template>
        <template #tagNames="{ row }">
          <Tooltip
            placement="bottom"
            :content="row.tagList && row.tagList.map((row) => row.tagName).join('、')"
            v-if="row.tagList !== null"
          >
            <span class="tag-names">{{ row.tagList && row.tagList.map((row) => row.tagName).join('、') }}</span>
          </Tooltip>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: '#DE990F', 'font-size': '14px' }"
              icon="icon-shebeidangan"
              content="设备档案"
              @click.native="deviceArchives(row)"
            ></ui-btn-tip>
            <ui-btn-tip icon="icon-tianjiabiaoqian" content="添加标签" @click.native="addLabel(row)"></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <area-select
      v-model="areaSelectModalVisible"
      @confirm="confirmArea"
      :checkedTreeDataList="checkedTreeData"
    ></area-select>
    <analysis v-model="analysisShow" @startAnalysis="getAnalysisStatus" ref="analysis"></analysis>
    <associated-label v-model="associatedShow" @query="addTag"></associated-label>
    <customize-filter
      v-model="labelShow"
      :customize-action="customizeAction"
      :content-style="contentStyle"
      :field-name="fieldName"
      :checkbox-list="allTagList"
      :default-checked-list="defaultCheckedList"
      @confirmFilter="confirmFilter"
    >
    </customize-filter>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import taganalysis from '@/config/api/taganalysis';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'dataqualityanalysis',
  props: {},
  data() {
    return {
      governanceContent: 'video-base',
      governanceContentData: [
        { code: 'video-base', label: '视频基础数据质量分析' },
        { code: 'video', label: '视频流质量分析' },
        { code: 'face', label: '人像图片质量分析' },
        { code: 'car', label: '车辆图片质量分析' },
      ],
      loading: false,
      analysisLoading: false,
      analysisTime: '',
      selectOrgTree: {
        orgCode: null,
      },
      custormNodeData: [
        {
          label: '未分配组织机构',
          orgCode: '-1',
        },
      ],
      selectTree: {
        regionCode: '',
      },
      areaSelectModalVisible: false,
      checkedTreeData: [],
      tagList: [],
      allTagList: [],
      searchData: {
        orgCode: '',
        regionCode: '',
        deviceId: '',
        deviceName: '',
        phyStatus: '',
        isImportant: '',
        sbdwlx: '',
        sbgnlx: '',
        sbcjqyList: [],
        tagIds: [],
        needTag: 1, //列表返回标签
        isAnalysis: 1,
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      tableData: [],
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        {
          title: '序号',
          width: 50,
          align: 'center',
          fixed: 'left',
          slot: 'Index',
        },
        {
          width: 180,
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          slot: 'deviceId',
          align: 'left',
          fixed: 'left',
          tree: true,
        },
        {
          minWidth: 190,
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: '组织机构',
          key: 'orgName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '行政区划',
          key: 'civilName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          align: 'left',
          slot: 'longitude',
          tooltip: true,
        },
        {
          width: 120,
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          align: 'left',
          slot: 'latitude',
          tooltip: true,
        },
        {
          width: 130,
          title: this.global.filedEnum.ipAddr,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: `${this.global.filedEnum.macAddr}`,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbgnlx}`,
          slot: 'sbgnlx',
          tooltip: true,
        },
        {
          minWidth: 120,
          title: `${this.global.filedEnum.sbdwlx}`,
          slot: 'sbdwlx',
        },
        {
          minWidth: 110,
          title: `${this.global.filedEnum.sbcjqy}`,
          key: 'sbcjqyText',
          tooltip: true,
        },
        {
          width: 100,
          title: `${this.global.filedEnum.phyStatus}`,
          slot: 'phyStatus',
          tooltip: true,
        },
        {
          width: 100,
          title: '数据来源',
          slot: 'sourceName',
          align: 'left',
          tooltip: true,
        },
        {
          title: '设备标签',
          key: 'tagNames',
          slot: 'tagNames',
          width: 150,
          align: 'center',
        },
        {
          width: 120,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      checkedData: [],
      analysisShow: false,
      associatedShow: false,
      labelShow: false,
      customizeAction: {
        title: '添加设备标签',
        leftContent: '选择设备标签及排序',
        rightContent: '设备标签显示',
        moduleStyle: {
          width: '70%',
        },
      },
      contentStyle: {
        height: `${500 / 192}rem`,
      },
      fieldName: {
        id: 'tagId',
        value: 'tagName',
      },
      defaultCheckedList: [],
      shareLoading: false,
      changeLabelDevice: null,
      timer: null,
    };
  },
  created() {
    this.getAlldicData();
    this.getTagList();
    this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
    this.searchData.orgCode = this.defaultSelectedOrg.orgCode;
    this.init();
    this.getAnalysisStatus();
    this.timer = setInterval(() => {
      this.getAnalysisStatus();
    }, 5000);
  },
  deactivated() {
    this.handleClearInterval();
  },
  destroyed() {
    this.handleClearInterval();
  },
  filters: {
    filterSource(val, list) {
      if (!val) return '';
      let sourceName = '';
      val.split(',').forEach((row) => {
        const source = list.find((item) => item.dataKey === row);
        sourceName += source.dataValue + '/';
      });
      return sourceName.substring(0, sourceName.length - 1);
    },
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    handleClearInterval() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    },
    // 查询所有标签
    async getTagList() {
      try {
        let res = await this.$http.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        this.allTagList = res.data.data;
        this.tagList = res.data.data.map((row) => {
          return {
            name: row.tagName,
            id: row.tagId,
          };
        });
      } catch (err) {
        console.log(err);
      }
    },
    analysis() {
      this.analysisShow = true;
    },
    async getAnalysisStatus() {
      try {
        const res = await this.$http.get(equipmentassets.queryAnalysis);
        this.analysisLoading = res.data.data.taskStatus === '1';
        this.analysisTime = res.data.data.lastTaskTime;
      } catch (err) {
        console.log(err);
      }
    },
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
      this.search();
    },
    selectedArea(area) {
      this.searchData.civilCode = area.regionCode;
    },
    selectInfo(infoList) {
      this.searchData.tagIds = infoList.map((item) => item.id);
      this.search();
    },
    async init() {
      try {
        this.loading = true;
        this.tableData = [];
        this.copySearchDataMx(this.searchData);
        let res = await this.$http.post(equipmentassets.getPageDeviceList, this.searchData);
        this.pageData.totalCount = res.data.data.total;
        this.tableData = res.data.data.entities;
      } catch (err) {
        console.log('err', err);
      } finally {
        this.loading = false;
      }
    },
    search() {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      this.$refs.orgTree.reset();
      this.selectOrgTree.orgCode = this.defaultSelectedOrg.orgCode;
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    changePage(val) {
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.search();
    },
    clickArea() {
      this.areaSelectModalVisible = true;
      this.checkedTreeData = this.searchData.sbcjqyList || [];
    },
    confirmArea(data) {
      this.searchData.sbcjqyList = data;
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    batchAddLabel() {
      this.associatedShow = true;
    },
    addLabel(row) {
      this.labelShow = true;
      this.changeLabelDevice = row;
      if (row.tagList && row.tagList.length) {
        this.defaultCheckedList = row.tagList.map((item) => {
          return item.tagId;
        });
      } else {
        this.defaultCheckedList = [];
      }
    },
    async confirmFilter(val) {
      try {
        let { data } = await this.$http.post(taganalysis.updateDeviceInfoTag, {
          id: this.changeLabelDevice.id,
          tagIds: val.map((row) => row.tagId),
        });
        this.$Message.success(data.msg);
        this.labelShow = false;
        this.init();
      } catch (err) {
        console.log(err);
      }
    },
    async shareDevice() {
      try {
        this.shareLoading = true;
        let params = Object.assign(
          {
            ids: this.checkedData.map((row) => row.id),
          },
          this.searchData,
        );
        const res = await this.$http.post(equipmentassets.shareDevice, params);
        this.$Message.success(res.data.msg);
      } catch (err) {
        console.log(err);
      } finally {
        this.shareLoading = false;
      }
    },
    addTag(queryloading, checkedTagData) {
      this.$UiConfirm({
        content: `您要为这${
          this.checkedData.length ? this.checkedData.length : this.pageData.totalCount
        }个设备添加标签，是否确认?`,
        title: '警告',
      })
        .then(async () => {
          try {
            queryloading = true;
            let params = Object.assign(
              {
                ids: this.checkedData.map((item) => item.id),
                addTagIdList: checkedTagData.map((item) => item.tagId),
              },
              this.searchData,
            );
            let { data } = await this.$http.post(equipmentassets.batchAddTag, params);
            this.$Message.success(data.msg);
            this.associatedShow = false;
            this.checkedData = [];
            this.init();
          } catch (err) {
            console.log(err);
          } finally {
            queryloading = false;
          }
        })
        .catch((res) => {
          console.log(res);
        });
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
  },
  watch: {},
  computed: {
    ...mapGetters({
      defaultSelectedOrg: 'common/getDefaultSelectedOrg',
      propertySearchLbdwlx: 'algorithm/propertySearch_sbdwlx', // 点位类型
      propertySearchSxjgnlx: 'algorithm/sxjgnlx_receive', // 功能类型
      checkStatus: 'algorithm/check_status', // 检测状态
      sourceList: 'algorithm/propertySearch_sourceId', //数据来源
      phystatusList: 'algorithm/propertySearch_phystatus',
      systemConfig: 'common/getSystemConfig',
    }),
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
    AreaSelect: require('@/components/area-select').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,
    Analysis: require('./analysis.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    AssociatedLabel: require('./associated-label.vue').default,
    CustomizeFilter: require('@/components/customize-filter.vue').default,
    UnderlineMenu: require('@/components/underline-menu').default,
  },
};
</script>
<style lang="less" scoped>
.equipment-analysis {
  background-color: var(--bg-content);
  .analysis-title {
    padding: 10px 20px 10px 20px;
    border-bottom: 1px solid var(--devider-line);
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .search-content {
    padding: 10px 20px 10px 20px;
  }
  .button-div {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    width: 100%;
    .ui-select-tabs {
      width: 80%;
    }
  }
  .ui-table {
    padding: 0 20px;
  }
  .tag-names {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: 150px;
  }
  @{_deep} .underline-menu-wrappe {
    margin-bottom: 0;
  }
}
</style>
