<template>
  <div class="statistical-main" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <div class="evaluate-tendency">
      <!-- 地市柱状图统计 -->
      <div class="mt-sm cl grap-content">
        <Checkbox v-model="sort" @on-change="onChangeSort">按得分排序</Checkbox>
        <div class="echarts-box">
          <draw-echarts
            v-if="echartList.length"
            :echart-option="taskEchart"
            :echart-style="ringStyle"
            ref="taskChart"
            class="charts"
            :echarts-loading="echartsLoading"
            @echartClick="echartClick"
            @echartLegendselectchanged="onSelectedChange"
          ></draw-echarts>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import dataZoom from '@/mixins/data-zoom';
import Vue from 'vue';
import taskTooltip from './task-tooltip';
export default {
  name: 'statisticalGraph',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    echartList: {
      type: Array,
      default: () => [],
    },
    echartsLoading: {
      type: Boolean,
      default: false,
    },
    search: {},
    data: {},
  },
  data() {
    return {
      sort: false, //排序
      ringStyle: {
        width: '100%',
        height: '100%',
      },
      num: 0,
      lengName: [],
      echartsSelected: [],
      name: '',
      taskEchart: {},
      searchData: {},
      taskList: [],
      tooltipFormatter: (data) => {
        data.map((item) => {
          this.taskList.forEach((i) => {
            i.children &&
              i.children.forEach((ite) => {
                if (item.axisValue == ite.regionName && item.seriesName === ite.name) {
                  item.taskList = ite.children;
                }
              });
          });
        });
        let taskTooltipShow = Vue.extend(taskTooltip);
        let _this = new taskTooltipShow({
          el: document.createElement('div'),
          data() {
            return {
              data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
    };
  },
  computed: {
    ...mapGetters({
      comprehensiveConfig: 'common/getComprehensiveConfig',
    }),
  },
  watch: {
    echartList: {
      handler(val) {
        this.initRing(val);
      },
      deep: true,
      immediate: true,
    },
    search: {
      handler(val) {
        this.searchData = val;
      },
      deep: true,
      immediate: true,
    },
    data: {
      handler(val) {
        this.taskList = val;
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    /**
     *
     * @param data {array} 考核任务统计
     * @param name {string} "车辆卡口考核得分"
     */
    sortEcharts(data, name) {
      //取出children
      let children = data.map((item) => item.children);
      //排序
      try {
        let sortChildren = children.sort((a, b) => {
          let aName = a.find((aItem) => aItem.name === name);
          let bName = b.find((bItem) => bItem.name === name);
          return bName.score - aName.score;
        });

        //还原
        return sortChildren.map((item) => {
          let { regionCode, regionName } = item[0];
          return {
            children: item,
            name: null,
            regionCode,
            regionName,
            score: null,
          };
        });
      } catch (e) {
        console.log('排序失败', e);
        return data;
      }
    },
    onChangeSort(val) {
      this.$emit('on-sort', val);
    },
    echartClick(val) {
      this.$emit('echartClick', val);
    },
    onSelectedChange(val) {
      this.echartsSelected = val.selected;
      let name = Object.keys(this.echartsSelected).find((key) => this.echartsSelected[key]);
      if (!this.sort || !name) return;
      this.initRing(this.sortEcharts(this.echartList, name));
    },
    // 柱状统计图
    initRing(list) {
      let { regionCode } = this.taskList.find((value) => value.id === this.searchData.examTaskId) || {};
      let yAxis = {};
      this.lengName = [];
      this.taskList = [];
      if (list.length > 0) {
        list.map((item) => {
          if (item.regionCode !== regionCode) {
            this.lengName.push(item.regionName);
            this.taskList.push({ children: item.children });
          }
          if (item.children.length > 0) {
            item.children.map((i) => {
              if (i.regionCode !== regionCode) {
                if (!yAxis[i.name]) {
                  yAxis[i.name] = [i.score];
                } else {
                  yAxis[i.name].push(i.score);
                }
              }
            });
          }
        });
      }
      let series = [];
      for (let key in yAxis) {
        series.push({
          name: key,
          type: 'bar',
          data: yAxis[key],
          stack: 'Ad',
          emphasis: {
            focus: 'series',
          },
          barWidth: '30px',
          showSymbol: false,
          label: {
            show: false,
            position: 'top',
          },
        });
        this.num++;
      }
      let opts = {
        selected: this.echartsSelected,
        xAxis: this.lengName,
        data: series,
        tooltipFormatter: this.tooltipFormatter,
      };
      this.taskEchart = this.$util.doEcharts.taskResultMadeSC(opts);
    },
  },
};
</script>
<style lang="less" scoped>
.statistical-main {
  width: 100%;
  height: 100%;
  .evaluate-tendency {
    height: 100%;
    color: var(--color-content);
    /deep/.ivu-tooltip-inner {
      overflow: auto;
    }
    .fl-st {
      display: flex;
      justify-content: flex-start;
      align-items: center;
    }
    .fs-16 {
      font-size: 16px;
      font-weight: bold;
    }
    .grap-content {
      height: 100%;
      width: 100%;
      .echarts-box {
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;
        .charts {
          height: 100%;
          width: 100%;
          display: inline-block;
          flex: 1;
        }
      }
    }
  }
}
</style>
