<template>
  <div class="">
    <img :src="url" alt :style="{transform:'scale('+multiples+')'}" />
    <div class="">
      
    </div>
  </div>
</template>
<script>


export default {
  components: {
   
  },
  props: {
    url: {
      type: String,
      default: ''
    },
  },
  
  data() {
    return {
      multiples: 1,
    }
  },
  
  methods: {
   
  }
}
</script>
<style lang="less" scoped>

</style>
