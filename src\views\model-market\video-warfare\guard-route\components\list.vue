<template>
  <div class="leftBox">
    <div class="leftBox_title">
      <p>警卫路线</p>
    </div>
    <div class="leftBox_list">
      <div class="list_header">
        <Button size="small" @click="handleAdd">新建</Button>
      </div>
      <div class="list_result">
        <div class="list-wrap">
          <div class="list-item" v-for="(item, index) in dataList" :key="index">
            <i class="iconfont icon-sheanxiansuo"/>
            <div @click.stop="viewItem(item)">{{ item.description }}</div>
            <i class="iconfont operater icon-bianji" title="编辑" @click.stop="edit(item)"/>
            <i class="iconfont operater icon-shanchu" title="删除" @click.stop="remove(item)"/>
          </div>
        </div>
        <ui-empty v-if="dataList.length === 0 && loading == false"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
        <Page size="small" :total="pageInfo.total" :page-size="pageInfo.pageSize" transfer show-total class="page" @on-change="handlePageChange" />
      </div>
    </div>
  </div>
</template>

<script>
import { guardRouteList, delGuardRoute } from '@/api/modelMarket'
export default {
  data() {
    return {
      dataList: [],
      loading: false,
      pageInfo: {
          pageNumber: 1,
          pageSize: 20,
          total: 0
      },
    }
  },
  mounted(){
    this.queryList()
  },
  methods: {
    handleAdd() {
      this.$emit('add')
    },
    refresh() {
      this.pageInfo.pageNumber = 1;
      this.queryList()
    },
    queryList() {
      this.loading = true;
      guardRouteList({pageNumber: this.pageInfo.pageNumber, pageSize: this.pageInfo.pageSize}).then(res => {
        let list = res.data ? res.data.entities : []
        this.pageInfo.total = res.data.total
        this.dataList = list
      }).finally(() =>{
          this.loading = false;
      })
    },
    handlePageChange(page) {
        this.pageInfo.pageNumber = page;
        this.queryList()
    },
    viewItem(item) {
      this.$emit('viewGuardRoute', item)
    },
    remove(data) {
      this.$Modal.confirm({
        title: '提示',
        closable: true,
        content: `确定删除该警卫路线吗？`,
        onOk: () => {
          delGuardRoute(data.id).then(res => {
            this.refresh()
          })
        }
      })
    },
    edit(item) {
      this.$emit('edit', item)
    }
  }
}
</script>

<style scoped lang="less">
.leftBox {
  position: absolute;
  top: 10px;
  left: 10px;
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;
  max-height: calc(~"100% - 20px");
  overflow: hidden;
  .leftBox_list {
    height: 80vh;
    display: flex;
    flex-direction: column;
    .list_header {
      text-align: right;
      padding: 10px;
      border-bottom: 1px solid #d3d7de;
    }
    .list_result {
      flex: 1;
      overflow-y: auto;
      position: relative;
      display: flex;
      flex-direction: column;
      .list-wrap {
        overflow-y: auto;
        flex: 1;
        .list-item {
          display: flex;
          align-items: center;
          padding: 3px 10px;
          cursor: pointer;
          div {
            flex: 1;
          }
          &:hover {
            background: rgba(44, 134, 248, .2);
            .operater {
              opacity: 1;
              color: #2c86f8;
            }
          }
          .operater {
            cursor: pointer;
            opacity: 0;
          }
          .icon-bianji {
            margin-right: 5px;
          }
          .icon-sheanxiansuo {
            color: #2c86f8;
            margin-right: 5px;
          }
        }
      }
      .page {
        text-align: right;
        padding: 10px;
      }
    }
  }
  // 头部名称
  .leftBox_title {
    font-size: 16px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    height: 40px;
    position: relative;
    line-height: 40px;
    padding-left: 20px;
    border-bottom: 1px solid #d3d7de;
    display: flex;
    justify-content: space-between;
    align-items: center;
    top: 0;
    z-index: 1;
    background: #fff;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    &:before {
      content: "";
      position: absolute;
      width: 3px;
      height: 20px;
      top: 50%;
      transform: translateY(-50%);
      left: 10px;
      background: #2c86f8;
    }
    span {
      color: #2c86f8;
    }
    /deep/.ivu-icon-ios-close {
      font-size: 30px;
      cursor: pointer;
    }
  }
}
</style>