<template>
  <ui-module
    :title="cardTitles"
    :title-model="curTitle"
    @tab-click="changeTitle"
  >
    <div slot="extra" class="btn-group" v-if="curTitle == 'person'">
      <Button
        v-for="(item, index) in timeList"
        :key="index"
        :type="item.value === dateType ? 'primary' : 'default'"
        size="small"
        @click="changeDateType(item.value)"
        >{{ item.label }}
      </Button>
    </div>
    <div class="data-parse-container">
      <template v-if="curTitle == 'person'">
        <ul class="analysis-list-box">
          <Row :gutter="18" :style="{ rowGap: '14px' }">
            <Col :span="24">
              <li
                class="analysis-item"
                :style="{
                  backgroundImage: `url(${require(`@/assets/img/place-control/people-static-bg1.png`)})`,
                }"
              >
                <p class="count-num">{{ total }}</p>
                <p class="data-name">活动人员</p>
              </li></Col
            >
            <Col :span="12">
              <li
                class="analysis-item"
                :style="{
                  backgroundImage: `url(${require(`@/assets/img/place-control/people-static-bg2.png`)})`,
                }"
              >
                <p class="count-num">{{ realNameNumber }}</p>
                <p class="data-name">已实名</p>
              </li></Col
            >
            <Col :span="12">
              <li
                class="analysis-item"
                :style="{
                  backgroundImage: `url(${require(`@/assets/img/place-control/people-static-bg3.png`)})`,
                }"
              >
                <p class="count-num">{{ noRealNameNumber }}</p>
                <p class="data-name">未实名</p>
              </li></Col
            >
          </Row>
        </ul>
      </template>
      <template v-else>
        <div v-if="practitionerList.length > 0" class="my-swiper-container">
          <swiper
            ref="mySwiper"
            :options="swiperOption"
            class="my-swiper"
            id="frequent-swiper"
          >
            <template>
              <swiper-slide
                v-for="(item, index) in practitionerList"
                :key="index"
              >
                <div
                  class="swiper-item"
                  @click="() => handleDetailFn(item, index)"
                >
                  <FrequentAlarm :data="item"></FrequentAlarm>
                </div>
              </swiper-slide>
            </template>
          </swiper>
          <div>
            <div
              class="swiper-button-prev snap-prev"
              slot="button-prev"
              id="frequentRight"
            >
              <i class="iconfont icon-caret-right"></i>
            </div>
            <div
              class="swiper-button-next snap-next"
              slot="button-next"
              id="frequentLeft"
            >
              <i class="iconfont icon-caret-right"></i>
            </div>
          </div>
        </div>
        <ui-empty v-else></ui-empty>
      </template>
      <ui-loading v-if="loading" />

      <peronWorkPlaceDetail ref="videoDetail" isNoSearch></peronWorkPlaceDetail>
    </div>
  </ui-module>
</template>

<script>
import UiModule from "@/views/holographic-archives/one-place-one-archives/place-archives/components/ui-module.vue";
import {
  queryPersonStatistics,
  queryPeronWorkPlace,
} from "@/api/monographic/place.js";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import FrequentAlarm from "./frequent-alarm.vue";
import peronWorkPlaceDetail from "./detail/peron-work-place-detail.vue";
import { getPlaceConfig } from "@/api/monographic/place.js";
export default {
  name: "placePeopleStatistics",
  components: {
    UiModule,
    swiper,
    swiperSlide,
    FrequentAlarm,
    peronWorkPlaceDetail,
  },
  data() {
    //  1：一周（近7天）  2：一月（近30天） 3: 三个月  4.一天=当天  0：自定义
    this.timeList = Object.freeze([
      { value: 4, label: "一天" },
      { value: 1, label: "一周" },
      { value: 2, label: "一月" },
    ]);
    return {
      loading: false,
      // 卡片title列表
      // cardTitles: [
      //   { label: "人员统计", value: "person" },
      //   { label: "疑似从业人员", value: "practitioner" },
      // ],
      curTitle: "person", // 当前选中的title
      dateType: 1, // 时间类型
      practitionerList: [],
      swiperOption: {
        slidesPerView: "3",
        // slidesPerGroup: 3,
        speed: 1000,
        navigation: {
          nextEl: "#frequentLeft",
          prevEl: "#frequentRight",
        },
        observer: true,
        observeParents: true,
      },
      total: 0,
      realNameNumber: 0,
      noRealNameNumber: 0,
      practitPlaceList: [],
    };
  },
  computed: {
    cardTitles() {
      const list = [{ label: "人员统计", value: "person" }];
      const practit = { label: "疑似从业人员", value: "practitioner" };
      this.practitPlaceList.includes(this.$route.query.archiveNo) &&
        list.push(practit);
      return list;
    },
  },
  async created() {
    await this.getPractitionerPlaceConfig();
    this.getParseData();
  },
  methods: {
    /**
     * @description: 切换标题
     * @param {string | number} val 标题value
     */
    changeTitle(val) {
      this.curTitle = val;
      this.getParseData();
    },

    /**
     * @description: 切换时间
     * @param {number} val 时间值
     */
    changeDateType(val) {
      if (val === this.dateType) return;
      this.dateType = val;
      this.getParseData();
    },

    /**
     * @description: 获取数据
     */
    getParseData() {
      this.loading = true;
      if (this.curTitle == "person") {
        let param = {
          placeId: this.$route.query.archiveNo,
          dayNumber: this.dataTypeTransfer(this.dateType),
        };
        queryPersonStatistics(param)
          .then((res) => {
            (this.total = res.data?.total || 0),
              (this.realNameNumber = res.data?.realNameNumber || 0);
            this.noRealNameNumber = res.data?.noRealNameNumber || 0;
          })
          .finally(() => {
            this.loading = false;
          });
      } else {
        let param = {
          pageNumber: 1,
          pageSize: 9,
          placeId: this.$route.query.archiveNo,
        };
        queryPeronWorkPlace(param)
          .then(({ data }) => {
            this.practitionerList = data?.entities || [];
          })
          .finally(() => {
            this.loading = false;
          });
      }
    },
    dataTypeTransfer(dateType) {
      //  1：一周（近7天）  2：一月（近30天） 3: 三个月  4.一天=当天  0：自定义
      if (dateType == 4) {
        return 1;
      } else if (dateType == 1) {
        return 7;
      } else if (dateType == 2) {
        return 30;
      }
    },
    // 详情
    handleDetailFn(item, index) {
      this.$refs.videoDetail.showList(item);
    },

    // 获取系统配置从业人员需要的场所
    async getPractitionerPlaceConfig() {
      const { data } = await getPlaceConfig();
      if (data) {
        let placeData = data?.paramValue || "{}";
        let json = JSON.parse(placeData);
        this.practitPlaceList = json.placeWorkPersonConfigVo.placeId;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.btn-group {
  padding-top: 10px;
  padding-right: 20px;
  button + button {
    margin-left: 10px;
  }
  .ivu-btn-small {
    border-color: #d3d7de;
    height: 24px;
    padding: 0 10px;
    color: rgba(0, 0, 0, 0.6);
    border-radius: 2px;
    &.ivu-btn-primary {
      color: #fff;
      border-color: #4597ff;
    }
  }
}
.data-parse-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .analysis-list-box {
    width: 80%;
    margin: 0 auto;
    padding: 0 18px;
    .analysis-item {
      display: flex;
      height: 70px;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      background: url("~@/assets/img/place-control/people-static-bg1.png")
        no-repeat;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .count-num {
        font-weight: 700;
        font-size: 25px;
        color: #2c86f8;
        margin-bottom: 5px;
      }
      .data-name {
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }
  .my-swiper-container {
    position: relative;
    height: 90%;
    width: 100%;
    padding: 0 15px;
    .my-swiper {
      margin: auto;
      height: 100%;
      overflow: hidden;
      .swiper-item {
        width: 100%;
        height: 100%;
      }
    }
  }
}

.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  text-align: center;
  line-height: 30px;
  margin-top: -15px;
  z-index: 999;
  .iconfont {
    color: #fff;
    font-size: 18px;
  }

  &:hover {
    background: rgba(0, 0, 0, 0.7);
  }

  &:active {
    background: rgba(0, 0, 0, 1);
  }
}

.swiper-button-prev {
  transform: rotate(180deg);
  left: 0;
}

.swiper-button-next {
  right: 0;
}
</style>
