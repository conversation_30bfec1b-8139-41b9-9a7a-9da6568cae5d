<template>
  <div class="choose-time">
    <div class="custom" v-if="currentKey === identKey.customKey">
      <DatePicker
        type="datetimerange"
        placement="bottom-end"
        :options="options"
        transfer
        :size="size"
        :format="format"
        @on-change="datePickerChange"
        @on-ok="onOk"
        placeholder="请选择时间"
      >
      </DatePicker>
      <Icon type="ios-close-circle-outline" :size="16" @click="close" />
    </div>
    <template
      v-for="(e, i) in timeList"
      v-if="identKey.type === 'text' && currentKey !== identKey.customKey"
    >
      <span
        :class="{ active: e[identKey.key] === currentKey }"
        :key="i"
        @click="choseTypeHandler(e[identKey.key], e)"
      >
        {{ e[identKey.value] }}
      </span>
    </template>
    <RadioGroup
      v-model="currentKey"
      @on-change="choseTypeHandler(currentKey)"
      v-if="identKey.type === 'radio' && currentKey !== identKey.customKey"
    >
      <Radio :label="e[identKey.key]" v-for="(e, i) in timeList" :key="i">{{
        e[identKey.value]
      }}</Radio>
    </RadioGroup>
    <ul
      v-if="identKey.type === 'tag' && currentKey !== identKey.customKey"
      class="horizontal-list"
    >
      <li
        :class="{ active: e[identKey.key] === currentKey }"
        class="f-14 mr-10 cursor-p"
        v-for="(e, i) in timeList"
        :key="i"
        @click="choseTypeHandler(e[identKey.key])"
      >
        {{ e[identKey.value] }}
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  props: {
    //默认值
    value: {
      default: "",
    },
    size: {
      default: "small",
    },
    timeList: {
      default: () => {
        return [
          { value: "近一周", key: 1 },
          { value: "近一个月", key: 2 },
          { value: "近三个月", key: 3 },
          { value: "自定义", key: 0 },
        ];
      },
    },
    identKey: {
      type: Object,
      default() {
        return {
          customKey: 0, // 自定义
          type: "text", //radio //tag
          key: "key",
          value: "value",
        };
      },
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    format: {
      type: String,
      default: "yyyy-MM-dd HH:mm:ss",
    },
  },
  data() {
    return {
      currentKey: "",
      times: [],
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.currentKey = val;
      },
    },
  },
  methods: {
    choseTypeHandler(key, row) {
      this.currentKey = key;
      this.times = [];
      if (this.identKey.customKey === this.currentKey) return;
      this.$emit("on-change-date", this.currentKey, this.times);
    },
    close() {
      this.currentKey = 1;
      this.times = [];
      this.$emit("on-change-date", this.currentKey, this.times);
    },
    datePickerChange(val) {
      this.times = val;
      // if (this.identKey.customKey === this.currentKey && !this.times.length)
    },
    onOk(val) {
      this.$emit("on-change-date", this.currentKey, this.times);
    },
  },
};
</script>

<style lang="less" scoped>
.choose-time {
  .custom {
    position: relative;
    /deep/.ivu-date-picker {
      width: 315px !important;
      .ivu-input-small {
        height: 22px;
      }
    }
    //.ivu-icon {
    //  margin-left: 8px;
    //  position: absolute;
    //  //right: -22px;
    //  //top: 8px;
    //  color: #888;
    //}
  }
  > span {
    height: 18px;
    background: #ffffff;
    border-radius: 2px;
    border: 1px solid #e8eaec;
    padding: 1px 7px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-right: 10px;
    cursor: pointer;
    &:last-child {
      margin-right: 0;
    }
  }
  /deep/ .ivu-radio-wrapper {
    line-height: 2.4;
  }
  .active {
    color: #fff;
    background: #2c86f8;
  }
  .horizontal-list {
    list-style: none; /* 移除默认的列表样式（项目符号）*/
    padding: 0; /* 移除内边距 */
    display: flex; /* 使用 Flex 布局 */
    li {
      line-height: 2.4;
      vertical-align: bottom; /* 文本底部与行框底部对齐 */
      height: 36px;
      padding: 0 6px;
      border-radius: 2px;
    }
  }
}
</style>
