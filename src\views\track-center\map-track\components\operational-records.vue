<template>
  <div>
    <!-- 作战记录 -->
    <Dropdown
      trigger="custom"
      :visible="operationalRecords"
      class="operational-records"
      @on-clickoutside="operationalRecords = false"
    >
      <div @click="openOperationsHandler" :class="{ open: operationalRecords }">
        <ui-icon type="caozuojilu"></ui-icon>
        <span>作战记录</span>
      </div>
      <DropdownMenu slot="list">
        <Timeline>
          <TimelineItem v-for="(e, i) in recordsList" :key="i">
            <p class="time">{{ e.createTime }}</p>
            <div class="content">
              <span>打开</span>
              <span
                class="records ellipsis"
                :class="{ active: openLayerIndex === i }"
                @click="openLayerManager(e, i)"
                >{{ e.name }}</span
              >
              <ui-icon
                type="bianji"
                v-if="e.permission == 'write'"
                title="编辑"
                @click.native="editRecord(e.name, i)"
              ></ui-icon>
              <ui-icon
                type="shanchu"
                v-if="e.permission == 'write'"
                title="删除"
                @click.native="deleteRecord(e, i)"
              ></ui-icon>
              <ui-icon
                type="caozuo"
                v-if="e.source == 'owner'"
                title="分享"
                @click.native="shareRecord(e, i)"
              ></ui-icon>
            </div>
          </TimelineItem>
          <div class="nodata" v-if="recordsList.length === 0">
            <ui-empty></ui-empty>
          </div>
        </Timeline>
      </DropdownMenu>
    </Dropdown>
    <!-- 作战记录编辑弹框 -->
    <ui-modal
      ref="modal"
      :value="visible"
      :r-width="450"
      title="编辑"
      class="modal"
      @onCancel="onCancel"
      @onOk="onOK"
    >
      <div>
        <span>名称:</span>
        <Input placeholder="请输入名称" v-model="name" :maxlength="20" />
      </div>
    </ui-modal>
    <select-user
      @selectData="handleUserData"
      showOrganization
      ref="selectUser"
    ></select-user>
  </div>
</template>

<script>
import {
  getAllCombatRecord,
  deleteCombat,
  updateCombat,
  receordShare,
} from "@/api/operationsOnTheMap";
import SelectUser from "@/components/select-modal/select-user.vue";
export default {
  components: {
    SelectUser,
  },
  data() {
    return {
      operationalRecords: false, // 点击作战记录样式
      recordsList: [], // 作战记录列表
      visible: false,
      currentEditIndex: -1, // 当前编辑的index
      openLayerIndex: -1, //当前打开的作战记录图层
      name: "", //作战记录名称
      shareId: "",
    };
  },
  methods: {
    // 编辑记录
    editRecord(name, i) {
      this.visible = true;
      this.name = name;
      this.currentEditIndex = i;
    },
    // 删除记录
    deleteRecord({ id }, i) {
      this.$Modal.confirm({
        title: "友情提示",
        width: 450,
        closable: true,
        content: `确定删除吗？`,
        onOk: () => {
          deleteCombat({ id })
            .then((res) => {
              if (res.code === 200) {
                // 判断删除的作战记录，是否是正在打开的
                if (this.openLayerIndex === i) {
                  this.openLayerIndex = -1;
                  // 同时地图关闭图层
                  this.$emit("closeLayerManager", true);
                }
                this.recordsList.splice(i, 1);
                this.$Message.success("删除成功");
              }
            })
            .catch((e) => {
              console.log(e);
            });
        },
      });
    },
    // 分享
    shareRecord(row) {
      this.shareId = row.id;
      if (row.sharedUserList && row.sharedUserList.length > 0) {
        row.sharedUserList.forEach((item) => {
          item.select = true;
          item.id = item.id || item.userId;
        });
      }
      this.$refs.selectUser.show(row.sharedUserList || []);
    },
    handleUserData(data) {
      receordShare({
        id: this.shareId,
        sharedUserIds: data.map((v) => v.id),
      }).then((res) => {
        if (res.code == 200) {
          this.$Message.success("分享成功");
        }
      });
    },
    // 新增-编辑保存名称
    onOK() {
      const { name, currentEditIndex, recordsList } = this;
      if (!name.length) {
        return this.$Message.warning("请输入名称");
      }
      const params = {
        id: recordsList[currentEditIndex].id,
        name,
      };
      updateCombat(params).then((res) => {
        if (res.code === 200) {
          this.$Message.success("编辑成功");
          recordsList[currentEditIndex].name = name;
          this.visible = false;
          this.$emit("openLayerManager", { name });
        }
      });
    },
    // 取消编辑名称
    onCancel() {
      this.visible = false;
    },
    // 打开作战记录图层
    openLayerManager({ name, dataText, id, source }, i) {
      this.openLayerIndex = i;
      this.$emit("openLayerManager", {
        name,
        layerMap: JSON.parse(dataText),
        id,
        source,
      });
    },
    // 点击打开作战记录
    openOperationsHandler() {
      if (!this.operationalRecords) {
        getAllCombatRecord().then((res) => {
          if (res.code === 200) {
            const { data = [] } = res;
            this.recordsList = [...data];
          }
          this.operationalRecords = true;
        });
      } else {
        this.operationalRecords = false;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.operational-records {
  position: absolute;
  top: 20px;
  left: 20px;
  width: 120px;
  height: 40px;
  background: #ffffff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
  /deep/.ivu-dropdown-rel {
    .iconfont {
      margin-right: 5px;
    }
  }
  .open {
    .iconfont {
      color: #2c86f8 !important;
    }
    > span {
      color: #2c86f8;
    }
  }
  /deep/.ivu-select-dropdown {
    width: 230px;
    padding: 20px 10px 0 10px;
    overflow: hidden auto;
    max-height: 210px;
    top: 41px !important;
    left: 0px !important;
    .ivu-timeline {
      &-item {
        &-head {
          width: 6px;
          height: 6px;
          left: 3px;
          background-color: #2c86f8;
        }
        &-content {
          top: -6px;
          .time {
            color: rgba(0, 0, 0, 0.6);
          }
          .content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .records {
              width: 90px;
              color: #2c86f8;
            }
            > span {
              color: rgba(0, 0, 0, 0.8);
            }
            .active {
              background-color: rgba(44, 134, 248, 0.1);
            }
          }
        }
      }
    }
    .ivu-timeline-item:last-of-type {
      padding: 0;
    }
  }
  .nodata {
    height: 190px;
  }
}
.modal {
  /deep/.ivu-modal-body {
    display: flex;
    align-items: center;
    // height: 120px;
    justify-content: center;
    > div {
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.45);
      .ivu-input-wrapper {
        width: 250px;
        margin-left: 5px;
      }
    }
  }
}
</style>
