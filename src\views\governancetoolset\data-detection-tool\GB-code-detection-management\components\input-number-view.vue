<template>
  <div class="view-container">
    <FormItem
      label=""
      v-for="(item, index) in allowNumList"
      :error="item.errorMsg"
      :show-message="item.isError"
      :key="index"
    >
      <InputNumber :min="0" :step="1" v-model="item.num" :max="1000" placeholder="请填写" />
      <span class="close-btn" @click="removeItemHandle(index)" v-if="allowNumList.length > 1"></span>
    </FormItem>
    <a v-if="allowNumList.length < inputMaxNumber" class="add-item-btn" @click="addItemHandle">
      <i class="icon-font icon-tree-add"></i>
    </a>
  </div>
</template>
<script>
export default {
  name: 'inputNumberView',
  props: {
    value: {
      type: String,
      default: '',
    },
    inputMaxNumber: {
      type: Number,
      default: 5,
    },
  },
  data() {
    return {
      allowNumList: [],
    };
  },
  watch: {
    allowNumList: {
      handler(arr) {
        const val = arr.map((item) => item.num).join(',');
        this.$emit('input', val);
      },
      deep: true,
    },
    value: {
      handler(val) {
        const numList = val.split(',');
        if (val) {
          this.allowNumList = numList.map((item, index) => {
            let num = item ? parseFloat(item) : null;
            let isError = false;
            let errorMsg = '';
            if (!num) {
              isError = true;
              errorMsg = '请填写正整数';
            } else {
              const isRepeat = numList.some((d, i) => item === d && index !== i);
              if (isRepeat) {
                isError = true;
                errorMsg = '数字不能重复';
              } else {
                isError = false;
                errorMsg = '';
              }
            }
            return { num, isError, errorMsg };
          });
        } else {
          this.allowNumList = [];
        }
      },
      immediate: true,
    },
  },
  methods: {
    addItemHandle() {
      this.allowNumList.push({ num: null });
    },
    removeItemHandle(index) {
      this.allowNumList.splice(index, 1);
    },
  },
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .close-btn {
    background: #ffffff;
    &:after {
      background: #02162b;
      top: 7px;
      left: 3px;
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .close-btn {
    background: #ffffff;
    border: 1px solid var(--color-primary);
    &:after {
      background: var(--color-primary);
      top: 6px;
      left: 2px;
    }
  }
}
.view-container {
  display: flex;
  align-items: center;
  min-height: 34px;
  flex-wrap: wrap;
  width: 800px;
  .ivu-form-item {
    position: relative;
    margin-right: 10px;
    margin-bottom: 14px;
    /deep/ .ivu-form-item-error-tip {
      padding-top: 1px;
      font-size: 12px;
    }
    &:hover {
      .close-btn {
        display: block;
      }
    }
    .close-btn {
      position: absolute;
      width: 14px;
      height: 14px;
      border-radius: 50%;
      top: -7px;
      right: -7px;
      text-align: center;
      display: none;
      cursor: pointer;
      &:after {
        content: '';
        position: absolute;
        width: 8px;
        height: 1px;
      }
    }
  }
  .add-item-btn {
    margin-bottom: 10px;
  }
  .ivu-input-number {
    margin: 0 !important;
  }
}
</style>
