<template>
  <div :id="id" class="container"></div>
</template>

<script>
import { Venn } from '@antv/g2plot';

export default {
  name: 'ui-venn',
  props: {
    options: {
      type: Object,
      default: 0,
    },
  },
  data() {
    return {
      venn: null,
      id: `container-${Math.random()}`,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.venn = new Venn(this.id, this.options);
      this.venn.render();
    },
  },
  watch: {
    options: {
      deep: true,
      async handler(val) {
        await this.$nextTick();
        this.venn.update(val);
      },
    },
  },
  beforeDestroy() {
    this.venn && this.venn.destroy();
  },
};
</script>
<style scoped lang="less">
.container {
  position: relative;
  height: 100%;
  width: 100%;
}
</style>
