<template>
  <div class="model-wrapper col-flex">
    <p class="title-p mt-sm mb-xs">请选择以下待治理设备：</p>
    <div class="filter-search-box auto-fill">
      <ui-label class="inline" label="设备编码">
        <Input class="width-sm" v-model="searchForm.deviceId" placeholder="请输入设备编码"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="承建单位" :width="70">
        <Input v-model="searchForm.cjdw" class="width-sm" placeholder="请输入承建单位"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="维护单位" :width="70">
        <Input v-model="searchForm.whdw" class="width-sm" placeholder="请输入维护单位"></Input>
      </ui-label>
      <ui-label class="inline ml-lg" label="设备状态" :width="70">
        <Select v-model="searchForm.phyStatus" clearable class="width-150">
          <Option v-for="item in phyStatusOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" label="报备状态" :width="70">
        <Select v-model="searchForm.isReport" clearable class="width-150">
          <Option v-for="item in isReportOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </ui-label>
      <ui-label class="inline ml-lg" label="设备状态扩展" :width="90">
        <Select v-model="searchForm.phyStatusExt" clearable class="width-150">
          <Option v-for="item in phyStatusExtOptions" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </Select>
      </ui-label>
      <div class="inline ml-lg">
        <Button type="primary" class="mr-sm" @click="startSearch"> 查询</Button>
        <Button type="default" @click="resetSearchData"> 重置</Button>
      </div>
    </div>
    <div class="content-box flex-1">
      <div class="left-content">
        <section class="tree-table">
          <ui-search-tree
            class="search-tree"
            placeholder="请输入组织机构名称或组织机构编码"
            node-key="id"
            :scroll="400"
            :tree-data="treeData"
            :default-props="defaultProps"
            @selectTree="selectTree"
          >
          </ui-search-tree>
        </section>
      </div>
      <section class="middle-content auto-fill">
        <div class="middle-search-box">
          <Checkbox class="ml-sm" v-model="allDevice" @on-change="checkedAll">
            <span class="table-text-content">全选</span>
          </Checkbox>
        </div>
        <ui-table
          ref="leftTableRef"
          class="ui-table auto-fill"
          reserveSelection
          :isAll="allDevice"
          :table-columns="tableColumns"
          :table-data="tableLeftData"
          :defaultStoreData="defaultLeftStoreData"
          :loading="loading"
          row-key="deviceId"
          @storeSelectList="storeSelectList"
        >
          <template #deviceId="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
        </ui-table>
        <ui-page
          class="page"
          :hasLast="false"
          :pageData="pageData"
          @changePage="changePage"
          @changePageSize="changePageSize"
        >
        </ui-page>
      </section>
      <section class="right-content auto-fill">
        <div class="choose-box">
          <p v-if="!allDevice">
            已选择设备列表<span class="color-warning"> {{ pageChooseData.totalCount }} </span>条
          </p>
          <p v-else>已选中全部设备</p>
          <span class="font-active-color pointer" @click="deleteBulk">批量移除</span>
        </div>
        <ui-table
          class="ui-table auto-fill"
          reserveSelection
          :special-jsx="specialJSX"
          :table-columns="rightTableColumns"
          :table-data="tableRightData"
          @storeSelectList="storeRightSelectList"
        >
          <template #deviceId="{ row }">
            <span class="font-active-color pointer" @click="deviceArchives(row)">{{ row.deviceId }}</span>
          </template>
          <template #action="{ row }">
            <ui-btn-tip
              class="operatbtn"
              icon="icon-yichu1"
              content="移除"
              @click.native="removeRightDevice(row)"
            ></ui-btn-tip>
          </template>
        </ui-table>
        <ui-page
          class="page"
          :hasLast="false"
          :pageData="pageChooseData"
          @changePage="changeChoosePage"
          @changePageSize="changeChoosePageSize"
        >
        </ui-page>
      </section>
    </div>
  </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
import global from '@/util/global';

let tableColumns = [
  { type: 'selection', align: 'center', width: 50, fixed: 'left' },
  { title: '序号', type: 'index', align: 'center', width: 40 },
  {
    title: `${global.filedEnum.deviceId}`,
    slot: 'deviceId',
    align: 'left',
    width: 170,
    tooltip: true,
  },

  {
    title: `${global.filedEnum.deviceName}`,
    key: 'deviceName',
    align: 'left',
    tooltip: true,
    minWidth: 130,
  },
  {
    minWidth: 120,
    title: '承建单位',
    key: 'cjdw',
    tooltip: true,
    align: 'left',
  },
  {
    minWidth: 120,
    title: '维护单位',
    key: 'whdw',
    tooltip: true,
    align: 'left',
  },
  { title: '异常原因', key: 'reason', align: 'left', tooltip: true, minWidth: 130 },
];
import governancetask from '@/config/api/governancetask';

let specialJSX = '<span class=\'font-active-color f-14\'>已选择全部数据</span>';
import { PHY_STATUS, IS_REPORT_STATUS, PHY_STATUS_EXT } from './enum.js';

export default {
  props: {
    searchTable: {
      default: () => {},
    },
  },
  data() {
    return {
      specialJSX: null,
      allDevice: false,
      tableColumns: Object.freeze(tableColumns),
      rightTableColumns: Object.freeze([
        ...tableColumns,
        { title: '操作', slot: 'action', align: 'center', width: 50, fixed: 'right' },
      ]),
      tableLeftData: [],
      defaultLeftStoreData: [],
      tableRightData: [],
      allRightData: [],
      rightSelectTableData: [],
      defaultProps: {
        label: 'orgName',
        children: 'children',
      },
      searchForm: {
        deviceId: null,
        cjdw: null,
        whdw: null,
        pageNumber: 1,
        pageSize: 20,
        orgCode: null,
        phyStatus: '', //设备状态
        isReport: null, //报备状态
        phyStatusExt: '', //设备扩展状态
      },
      pageChooseData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      loading: false,
      phyStatusOptions: PHY_STATUS, //设备物理状态
      isReportOptions: IS_REPORT_STATUS, //报备状态
      phyStatusExtOptions: PHY_STATUS_EXT, //设备扩展状态
    };
  },
  async created() {
    this.copySearchDataMx(this.searchForm);
    await this.setOrganizationList();
    // this.selectTree(this.getDefaultSelectedOrg)
  },
  methods: {
    checkedAll() {
      if (this.allDevice) {
        this.specialJSX = specialJSX;
        this.tableLeftData.forEach((item) => {
          this.$set(item, '_disabled', true);
          this.$set(item, '_checked', true);
          this.tableRightData = [];
          this.allRightData = [];
        });
        this.pageChooseData.totalCount = 0;
        this.defaultLeftStoreData = [];
      } else {
        this.specialJSX = null;
        this.tableLeftData.forEach((item) => {
          this.$set(item, '_disabled', false);
          this.$set(item, '_checked', false);
          this.tableRightData = [];
          this.allRightData = [];
        });
      }
      let params = { isCheckAll: this.allDevice };
      this.orgCode ? (params.orgCode = this.orgCode) : null;
      this.$emit('getEvaForm', params);
    },
    resetSearchData() {
      this.orgCode = null;
      this.resetSearchDataMx(this.searchForm, this.startSearch);
    },
    ...mapActions({
      setOrganizationList: 'common/setOrganizationList',
    }),
    storeSelectList(list) {
      let allRightObject = {};
      this.allRightData.forEach((item) => {
        allRightObject[item.id] = item;
      });
      this.allRightData = [];
      list.forEach((item) => {
        item.id in allRightObject ? null : this.$set(item, '_checked', false);
        this.allRightData.push(item);
      });
      this.pageChooseData.totalCount = this.allRightData.length;
      let start = (this.pageChooseData.pageNum - 1) * this.pageChooseData.pageSize;
      let end = this.pageChooseData.pageNum * this.pageChooseData.pageSize;
      this.tableRightData = this.allRightData.slice(start, end);
    },
    storeRightSelectList(list) {
      this.rightSelectTableData = list;
    },
    async init() {
      try {
        if (!this.searchTable.indexId) {
          return this.$Message.error('请选择指标批次');
        }
        this.loading = true;
        let params = Object.assign({}, this.searchForm, this.searchTable);
        // params.isCheckAll = this.allDevice ? '1' : '0'
        this.orgCode ? (params.orgCode = this.orgCode) : null;
        this.$emit('getEvaForm', Object.assign({ isCheckAll: this.allDevice }, params));
        let res = await this.$http.post(governancetask.pageListExcludeDeviceId, params);
        this.tableLeftData = res.data.data.entities.filter((item) => {
          return !item.workOrderId;
        });
        if (this.allDevice) {
          this.tableLeftData.forEach((item) => {
            this.$set(item, '_disabled', true);
            this.specialJSX = specialJSX;
            this.tableRightData = [];
          });
        } else {
          this.specialJSX = null;
        }
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    changeChoosePage(val) {
      this.pageChooseData.pageNum = val;
      this.tableRightData = this.allRightData.slice(
        (val - 1) * this.pageChooseData.pageSize,
        val * this.pageChooseData.pageSize,
      );
    },
    changeChoosePageSize(val) {
      this.pageChooseData.pageNum = 1;
      this.pageChooseData.pageSize = val;
      this.tableRightData = this.allRightData.slice(
        (this.pageChooseData.pageNum - 1) * this.pageChooseData.pageSize,
        this.pageChooseData.pageNum * this.pageChooseData.pageSize,
      );
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchForm.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchForm.pageNumber = 1;
      this.pageData.pageSize = val;
      this.searchForm.pageSize = val;
      this.init();
    },
    startSearch() {
      this.loading = true;
      this.init();
    },
    selectTree(data) {
      this.orgCode = data.orgCode;
      this.init();
    },
    reset() {
      this.visible = false;
      this.saveForm.taskPlannedDate = '';
      this.saveForm.taskContent = '';
      this.searchForm.orgCode = '';
      this.searchForm.keyWord = '';
      this.searchForm.cjdw = '';
      this.searchForm.whdw = '';
      this.clearAll();
    },
    clearAll() {
      this.pageChooseData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.tableLeftData = [];
      this.tableRightData = [];
      // this.$refs.uiSelectTabs.reset()
    },
    deleteBulk() {
      if (!this.rightSelectTableData.length) return this.$Message.error('请选择设备');
      this.rightSelectTableData.forEach((item) => {
        this.removeRightDevice(item);
      });
    },
    removeRightDevice(one) {
      let Index = this.tableRightData.findIndex((item) => item.id === one.id);
      let allIndex = this.allRightData.findIndex((item) => item.id === one.id);
      this.tableRightData.splice(Index, 1);
      this.allRightData.splice(allIndex, 1);
      this.pageChooseData.totalCount = this.allRightData.length;
      this.defaultLeftStoreData = this.$util.common.deepCopy(this.allRightData);
    },
    clearRightSelectTableData() {
      this.allDevice = false;
      this.pageChooseData.totalCount = 0;
      this.defaultLeftStoreData = [];
      this.allRightData = [];
      this.tableRightData = [];
    },
    clearLeftTableData() {
      this.tableLeftData = [];
    },
  },
  watch: {},
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getOrganizationList',
      getDefaultSelectedOrg: 'common/getDefaultSelectedOrg',
    }),
  },
};
</script>
<style lang="less" scoped>
.model-wrapper {
  // background-color: #0e2246;
  .content-title {
    height: 40px;
    line-height: 40px;
  }

  .content-box {
    height: 400px;
    display: flex;
    border-top: 1px solid var(--devider-line);
  }

  .title-p {
    padding: 0 10px;
    color: var(--color-display-text);
  }

  .left-content {
    .tree-table {
      display: flex;
      height: 400px;

      .search-tree {
        padding: 10px 10px 0 0;
      }
    }

    // .search-box {
    //   height: 50px;
    //   line-height: 50px;
    //   border-right: 1px solid var(--border-color);
    //   border-bottom: 1px solid var(--border-color);
    // }
  }

  .middle-content,
  .right-content {
    .choose-box {
      display: flex;
      justify-content: space-between;
      height: 40px;
      line-height: 40px;
      // padding: 0 25px 0 10px;
      color: var(--color-content);
    }
  }

  .middle-content {
    width: 715px;
    flex-grow: 1;
    position: relative;
    border-left: 1px solid var(--devider-line);
    border-right: 1px solid var(--devider-line);
    overflow: inherit;
    padding: 0 10px;

    .select-div {
      display: flex;

      .select-span {
        display: inline-block;
        height: 45px;
        line-height: 45px;
        color: #fff;
        font-size: 14px;
      }

      .tabs {
        flex: 1;
      }
    }

    .middle-search-box {
      height: 40px;
      line-height: 40px;
    }
  }

  .right-content {
    padding: 0 10px;
    width: 715px;
    display: flex;
    flex-direction: column;
  }

  .filter-search-box {
    height: 64px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    border-top: 1px solid var(--devider-line);

    .width-150 {
      width: 150px;
    }
  }
}
</style>
