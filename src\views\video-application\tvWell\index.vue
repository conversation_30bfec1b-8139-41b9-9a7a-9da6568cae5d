<template>
  <div class="video-play">
    <div class="video-left mr-10" :style="{ width: leftWidth + 'px' }">
      <ul class="search_tab">
        <li
          v-for="(item, index) in tablist"
          :key="index"
          class="tabslist"
          @click="handleClickTab(index)"
          :class="{ active: tabIndex == index }"
        >
          {{ item.name }}
        </li>
      </ul>
      <div class="tab-content">
        <component
          ref="treeRef"
          :is="tablist[tabIndex].componentName"
          @handleClick="handleNodeClick"
          :playingDeviceIds="playingDeviceIds"
          @inspecting="inspecting"
          @inspectPlay="inspectPlay"
          @closeAll="closeAllInspect"
          @setPlan="setPlan"
        ></component>
      </div>
      <toggle-button
        class="toggle-w"
        :boxWidth.sync="leftWidth"
      ></toggle-button>
    </div>
    <div class="right-content">
      <tv-well
        ref="tvWell"
        @playingDeviceIds="changePlayingDeviceIds"
        @inspectStart="inspectStart"
        @cancleInspect="cancleInspect"
      ></tv-well>
    </div>
  </div>
</template>

<script>
import deviceTree from "./components/device-tree.vue";
import groupTree from "./components/group-tree.vue";
import tvWell from "./components/tv-well.vue";
import plan from "./components/plan.vue";
import toggleButton from "@/components/toggle-button.vue";

export default {
  name: "video-play",
  components: {
    deviceTree,
    groupTree,
    tvWell,
    plan,
    toggleButton,
  },
  data() {
    return {
      tablist: [
        { name: "视频资源", componentName: "deviceTree" },
        { name: "我的分组", componentName: "groupTree" },
        { name: "上墙预案", componentName: "plan" },
      ],
      tabIndex: 0,
      playingDeviceIds: [],
      leftWidth:
        (parseFloat(
          window.getComputedStyle(window.document.documentElement)["font-size"]
        ) /
          192) *
        316,
    };
  },
  deactivated() {
    this.cancleInspect();
  },
  methods: {
    handleClickTab(index) {
      this.tabIndex = index;
    },
    handleNodeClick(item) {
      this.$refs.tvWell.handleNodeClick(item);
    },
    inspecting() {
      this.$refs.tvWell.inspecting();
    },
    inspectPlay({ devices, indexs }) {
      this.$refs.tvWell.inspectPlay({ devices, indexs });
    },
    closeAllInspect() {
      this.$refs.tvWell.closeAllInspect();
    },
    setPlan(item) {
      this.$refs.tvWell.setPlan(item);
    },
    changePlayingDeviceIds(arr) {
      this.playingDeviceIds = arr;
    },
    inspectStart(inspectObj) {
      if (this.$refs.treeRef.inspectStart)
        this.$refs.treeRef.inspectStart(inspectObj);
    },
    cancleInspect() {
      if (this.$refs.treeRef.cancleInspect) this.$refs.treeRef.cancleInspect();
    },
  },
};
</script>
<style lang="less" scoped>
.video-play {
  width: 100%;
  display: flex;
  .video-left {
    width: 316px;
    height: 100%;
    background-color: #fff;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
    position: relative;
    .search_tab {
      display: flex;
      justify-content: space-evenly;
      border-bottom: 1px solid #d3d7de;
      margin-bottom: 15px;
      .tabslist {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        width: 25%;
        text-align: center;
        padding: 7px 0;
        cursor: pointer;
      }
      .active {
        color: #2c86f8;
        border-bottom: 3px solid #2c86f8;
      }
    }
    .tab-content {
      height: calc(~"100% - 54px");
    }
    .toggle-w {
      position: absolute;
      right: 0;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
  .right-content {
    flex: 1;
    box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
    border-radius: 4px;
  }
}
</style>
