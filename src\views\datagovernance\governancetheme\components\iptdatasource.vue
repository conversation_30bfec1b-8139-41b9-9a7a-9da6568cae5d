<template>
  <div class="iptdatasource">
    <div class="dataType">
      <div class="dataTypeLabel">请选择原始待治理数据</div>
      <div class="tips"><span>?</span></div>
    </div>
    <div class="radiodiv">
      <RadioGroup v-model="this.formValidate.type">
        <Radio label="数据库"></Radio>
        <Radio label="kafka"></Radio>
      </RadioGroup>
    </div>
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :hide-required-mark="true" :label-width="80">
      <FormItem
        v-for="(item, index) in formValidate.items"
        :key="index"
        :label="label"
        :prop="'items.' + index + '.value'"
        :rules="{
          required: true,
          message: 'Item ' + item.index + '不能为空!',
          trigger: 'blur',
        }"
      >
        <Row>
          <Col span="18">
            <Select v-model="item.value" placeholder="请选择数据表">
              <Option value="beijing">New York</Option>
              <Option value="shanghai">London</Option>
              <Option value="shenzhen">Sydney</Option>
            </Select>
          </Col>
          <Col span="4" offset="1" class="align-flex">
            <div class="opration">
              <div class="add" @click="handleAdd">+</div>
              <i
                v-if="index + 1 === formValidate.items.length && index != 0"
                @click="handleRemove(index)"
                class="icon-font icon-shanchu2"
              ></i>
              <!-- <Icon
                v-if="index + 1 === formValidate.items.length && index != 0"
                type="ios-trash-outline"
                @click="handleRemove(index)"
              /> -->
            </div>
          </Col>
        </Row>
      </FormItem>
    </Form>
    <div class="dataTypeLabel">治理方式</div>
    <div class="way">
      <RadioGroup v-model="this.formValidate.way">
        <Radio label="统一流程治理"></Radio>
        <Radio label="分流程治理"></Radio>
      </RadioGroup>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    label: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      formValidate: {
        type: '数据库',
        way: '统一流程治理',
        items: [
          {
            value: '',
            index: 1,
            status: 1,
          },
        ],
      },
      ruleValidate: {
        city: [
          {
            required: true,
            message: 'Please select the city',
            trigger: 'change',
          },
        ],
        formDynamic: [
          {
            required: true,
            message: 'Please select gender',
            trigger: 'change',
          },
        ],
      },
    };
  },
  created() {},
  methods: {
    handleAdd() {
      this.index++;
      this.formValidate.items.push({
        value: '',
        index: this.index,
        status: 1,
      });
    },
    handleRemove(index) {
      this.formValidate.items.splice(index, 1);
    },
    handleValidate() {
      this.$refs['formValidate'].validate((valid) => {
        if (valid) {
          this.$Message.success('Success!');
        } else {
          this.$Message.error('Fail!');
        }
      });
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
.iptdatasource {
  font-size: 14px;
  .dataType {
    width: 100%;
    display: flex;
    align-items: center;

    .tips {
      width: 14px;
      height: 14px;
      font-size: 12px;
      color: #071b39;
      background: #c76d28;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
    }
  }
  .dataTypeLabel {
    margin-right: 9px;
    font-size: 14px;
    color: var(--color-primary);
  }
  .radiodiv {
    margin: 11px 0 14px;
  }
  .way {
    margin: 11px 0 28px;
  }
  .add {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid var(--color-primary);
    color: var(--color-primary);
    border-radius: 2px;
    cursor: pointer;
  }
  .opration {
    display: flex;
    align-items: center;
    i {
      font-size: 16px;
      color: #174f98;
      &:hover {
        color: var(--color-primary);
      }
      &:active {
        color: #4e9ef2;
      }
    }
  }
  .icon-font {
    margin: 0 10px;
  }
  @{_deep} .ivu-radio-wrapper {
    font-size: 14px;
  }
  @{_deep} .ivu-radio-inner {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    background: transparent;
    border: 1px solid var(--color-primary);
  }
  @{_deep} .ivu-radio-inner:after {
    width: 8px;
    height: 8px;
    background: var(--color-primary);
  }
  @{_deep} .ivu-select .ivu-select-selection {
    height: 34px;
    line-height: 34px;
    border-color: var(--color-primary);
    border: 1px solid var(--color-primary);
    border-radius: 4px;
  }
  @{_deep} .ivu-form-item {
    margin-bottom: 14px;
  }
  @{_deep} .ivu-select-arrow {
    color: var(--color-primary);
  }
}
.align-flex {
  display: flex;
  align-items: center;
}
</style>
