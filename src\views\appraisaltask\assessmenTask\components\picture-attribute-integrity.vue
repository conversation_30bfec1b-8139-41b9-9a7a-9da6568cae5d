<template>
  <div class="content">
    <ui-modal
      v-model="visible"
      :title="`${taskDetail.ORG_REGEION_CODE}-${taskDetail.title}情况明细`"
      width="70%"
      footer-hide
    >
      <div class="page-picture-attribute auto-fill">
        <div class="table-title">
          <span>
            {{ taskDetail.ORG_REGEION_CODE }}{{ taskDetail.parentTitle ? '-' + taskDetail.parentTitle : '' }}-{{
              taskDetail.title
            }}（设置分值：{{ taskDetail.score }}分）
          </span>
        </div>
        <div class="table-box auto-fill">
          <ui-table
            class="ui-table auto-fill table-data"
            :table-columns="tableColumns"
            :table-data="tableData"
            show-summary
            :summary-method="handleSummary"
            sumText="最终得分"
            :loading="loading"
            maxHeight="500"
          >
          </ui-table>
        </div>
      </div>
    </ui-modal>
  </div>
</template>
<script>
import evaluationoResultMixin from '@/views/governanceevaluation/evaluationoResult/util/Mixins/evaluationoResultMixin.js';
import CreateTabs from '@/components/create-tabs/create-tabs.vue';
import UIBtnTip from '@/components/ui-btn-tip.vue';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    taskObj: {
      type: [Object, String],
    },
  },
  mixins: [evaluationoResultMixin],
  components: {
    UiTable: require('@/components/ui-table.vue').default,
  },
  data() {
    return {
      visible: false,
      loading: false,
      row: {},
      taskDetail: {},
      tableColumns: [],
      tableData: [],
    };
  },
  watch: {
    taskObj: {
      handler(val) {
        if (val) {
          this.taskDetail = val;
          this.tableColumns = [];
          this.tableData = [];
          this.getDetail();
        }
      },
      immediate: true,
    },
  },
  methods: {
    init() {
      this.visible = true;
    },
    getDetail() {
      this.loading = true;
      this.$http
        .post(governanceevaluation.getExamStatisticsDetail, {
          examContentItemMonthResultId: this.taskDetail.examContentItemMonthResultId,
        })
        .then((res) => {
          let { headers, body } = res.data.data;
          // 处理表头
          this.handleTableHeaders(headers);
          this.tableColumns = headers;
          // 处理表内容
          this.handleTableBody(body);
        })
        .catch(() => {})
        .finally(() => {
          this.loading = false;
        });
    },
    handleTableHeaders(arr, title) {
      arr.forEach((v) => {
        v.title = v.name;
        v.key = v.code;
        v.className = 'header-table';
        v.align = 'center';
        v.parentTitle = title;
        if (v.code !== 'SINGLE_SCORE' && v.code !== 'EVALUATION_TIME') {
          v.renderHeader = this.renderTableHeader;
        }
        v.minWidth = v.title.length * 14 + 20;
        if (v.children && v.children.length) {
          this.handleTableHeaders(v.children, v.title);
        } else {
          if (v.code !== 'EVALUATION_TIME' && v.code !== 'SINGLE_SCORE') v.render = this.renderTableTd;
          delete v.children;
        }
      });
    },
    handleTableBody(arr) {
      let tableData = [];
      arr.forEach((v, i) => {
        tableData.push({});
        v.forEach((k) => {
          if (k.code === 'EVALUATION_TIME') {
            tableData[i].EVALUATION_TIME = k.evaluationTime;
          } else if (k.code === 'SINGLE_SCORE') {
            // 如果有得分描述就用得分描述
            tableData[i].SINGLE_SCORE = k.scoreDesc || k.score;
          } else {
            tableData[i][k.code] = k;
          }
        });
      });
      this.tableData = tableData;
    },
    handleSummary({ columns, data }) {
      const sums = {};
      let averageValue = 0;
      columns.forEach((column) => {
        const key = column.key;
        if (key === 'SINGLE_SCORE') {
          const values = data.map((item) => Number(item[key]));
          if (!values.every((value) => isNaN(value))) {
            const v = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            averageValue = (v / this.tableData.length).toFixed(2);
            sums[key] = {
              key,
              value: '',
            };
          } else {
            sums[key] = {
              key,
              value: '',
            };
          }
        } else {
          sums[key] = {
            key,
            value: '',
          };
        }
      });
      columns.forEach((column, index) => {
        const key = column.key;
        if (index === 0) {
          sums[key] = {
            key,
            value: `${this.taskDetail.title}最终得分：${averageValue}分`,
          };
          this.$nextTick(() => {
            document
              .getElementsByClassName(`ivu-table-column-${column.__id}`)
              [this.tableData.length + 1].setAttribute('colspan', this.tableColumns.length);
          });
          return;
        }
      });
      return sums;
    },
    evaluationDetailBtn(item) {
      let obj = item.row[item.column.key];
      this.$emit('selectModuleClick', obj);
    },
    renderTableHeader(h, params) {
      return h('div', [h('div', {}, params.column.title), h('div', {}, '评测值')]);
    },
    renderTableTd(h, params) {
      if (!params.row[params.column.key] && params.row[params.column.key] != 0) {
        return h('span', {}, '--');
      } else if (params.row[params.column.key].evaluationQualified === '3') {
        // 无法考核
        return h('span', {}, params.row[params.column.key].score + '%');
      } else {
        // 有得分描述的也不可点击
        let hasScoreDesc = !!params.row[params.column.key].scoreDesc;
        if (params.row[params.column.key].linkUrl) {
          return h(
            'a',
            {
              attrs: {
                href: params.row[params.column.key].linkUrl,
                target: '_blank',
              },
              style: {
                color: 'var(--color-primary)',
                cursor: 'pointer',
                textDecoration: 'underline',
              },
            },
            params.row[params.column.key].score + '%',
          );
        } else if (hasScoreDesc) {
          return h(
            'span',
            {
              style: {
                color: '#fff',
              },
            },
            params.row[params.column.key].scoreDesc,
          );
        } else if (this.isExistIndex(params.row[params.column.key]['indexType'])) {
          return h(UIBtnTip, {
            props: {
              content: '结果详情',
              btnText: params.row[params.column.key].score + '%',
            },
            style: {
              color: 'var(--color-primary)',
              cursor: 'pointer',
              textDecoration: 'underline',
            },
            nativeOn: {
              click: () => {
                let obj = params.row[params.column.key];
                this.$emit('jump', obj);
              },
            },
          });
        } else {
          return h(
            CreateTabs,
            {
              props: {
                componentName: 'detectionToOverview',
                tabsText: '评测结果', // 跳转页面标题
                tabsQuery: {
                  indexId: params.row[params.column.key].indexId,
                  code: params.row[params.column.key].orgRegeionCode,
                  access: 'EXAM_RESULT',
                  batchId: params.row[params.column.key].evaluationBatchId,
                  taskIndexId: params.row[params.column.key].evaluationTaskIndexId,
                  startTime: params.row[params.column.key].evaluationTime,
                },
              },
              style: {
                color: 'var(--color-primary)',
                cursor: 'pointer',
                textDecoration: 'underline',
              },
              on: {
                selectModule: () => {
                  if (hasScoreDesc) return;
                  this.evaluationDetailBtn(params);
                },
              },
            },
            params.row[params.column.key].score + '%',
          );
        }
      }
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  color: var(--color-content);
}
.page-picture-attribute {
  min-height: 260px;
  @{_deep}.head-center {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .table-title {
    color: var(--color-content);
    margin-bottom: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
  }
  .table-box {
    padding: 0 20px 20px 20px;
    position: relative;
    .span-btn {
      cursor: pointer;
      color: var(--color-primary);
    }
    .no-data {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    @{_deep} .ivu-table-tbody {
      td {
        padding: 10px 0 10px 0;
      }
    }
    @{_deep} .ivu-table-body {
      td {
        padding: 10px 0 10px 0;
      }
    }
    @{_deep} .ui-table > .ivu-table-with-summary .ivu-table {
      overflow: hidden;
    }
    @{_deep} .table-data > .ivu-table-with-summary .ivu-table > div {
      overflow-x: auto !important;
      overflow-y: auto !important;
    }
    @{_deep} .table-data > .ivu-table-with-summary .ivu-table > .ivu-table-header {
      overflow: hidden !important;
    }
    @{_deep} .ui-table {
      border-top: 1px solid var(--border-table);
      box-sizing: border-box;
      th .ivu-table-cell {
        color: #8797ac;
      }
    }
    @{_deep} .ivu-table-tip {
      min-height: 200px;
    }
    @{_deep} .ivu-table-body > table {
      border-right: 1px solid var(--border-table);
      box-sizing: border-box;
    }
    @{_deep} .ivu-table-header {
      border-right: 1px solid var(--border-table);
      box-sizing: border-box;
    }
    @{_deep} .header-table {
      box-shadow: none;
      // box-shadow: inset 1px -1px 0 0 #0d477d;
      border-left: 1px solid var(--border-table);
      border-bottom: 1px solid var(--border-table);
      box-sizing: border-box;
    }
  }
}
@{_deep} .ivu-table-summary {
  background-color: var(--border-table);
  border-top: none;
  .ivu-table-tbody {
    .ivu-table-row td {
      display: none;
    }
    .ivu-table-row td:first-child,
    .ivu-table-row td:last-child {
      display: table-cell;
      font-size: 22px;
      color: var(--color-bluish-green-text);
    }
  }
}
@{_deep} .ivu-table-summary tr td {
  background-color: transparent;
}
@{_deep}.ivu-modal-mask {
  z-index: 999 !important;
}
@{_deep}.ivu-modal-wrap {
  z-index: 999 !important;
}
</style>
