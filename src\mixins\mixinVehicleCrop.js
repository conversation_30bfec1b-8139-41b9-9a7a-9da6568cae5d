export default {
  methods: {
    async getImageCropper(src, location,) {
      try {
        // TODO 先注释 有bug
        let url =  await this.$util.common.imageCropper(src, location)
        return src
      }catch (e) {
        console.log(e)
        return src
      }
    },
    async removeUrl(data) {
      try {
        for (const item of data) {
          URL.revokeObjectURL(await item.traitImg || await item.photoUrl || await item.capturePic)
        }
      } catch (e) {
        console.log(e);
      }
    }
  }
}
