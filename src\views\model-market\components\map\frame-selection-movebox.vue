<!--
    * @FileDescription: 地图-框选列表（可移动）
    * @Author: H
    * @Date: 2024/04/09
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-09-26 11:07:45
 -->
<template>
  <div class="move-box" @mousedown="handleMouseDown($event)">
    <!-- <div class="ocx-masker"></div> -->
    <!-- @mousedown="handleMouseDown($event)" @mouseup="handleMouseup($event)" -->
    <div class="select-result-dom">
      <header></header>
      <div class="dom-title">碰撞区域</div>
      <section class="dom-content">
        <div class="dom-content-time">
          <div class="search_wrapper">
            <p class="search-wrapper-title">开始时间:</p>
            <DatePicker
              type="datetime"
              v-model="queryForm.startDate"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择"
            >
            </DatePicker>
          </div>
          <div class="search_wrapper">
            <p class="search-wrapper-title">结束时间:</p>
            <DatePicker
              type="datetime"
              v-model="queryForm.endDate"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择"
            >
            </DatePicker>
          </div>
        </div>
        <div class="option-show">
          <div class="optiop-num">
            选择设备<span>({{ selectPointsList.length }})</span>
          </div>
        </div>
        <div class="layerList">
          <div class="dom-content-list" slot="content">
            <template v-for="(item, index) in selectPointsList">
              <div class="dom-content-strip" :key="index">
                <ui-icon
                  :type="deviceTypeMap[item.LayerType].icon"
                  :color="deviceTypeMap[item.LayerType].color"
                ></ui-icon>
                <p>{{ item.deviceName }}</p>
                <i
                  class="iconfont icon-close"
                  @click="handleDele(item, index)"
                ></i>
              </div>
            </template>
          </div>
        </div>
        <div class="btn-group">
          <Button
            type="primary"
            size="small"
            class="btnwidth"
            @click.stop="handleSave"
            >保存</Button
          >
          <Button class="btnwidth" size="small" @click.stop="handleCancel"
            >取消</Button
          >
        </div>
      </section>
    </div>
  </div>
</template>
<script>
import { getConfigDate } from "@/util/modules/common";
export default {
  props: {
    selectDeviceList: {
      type: Array,
      default: () => [],
    },
    // 时空碰撞所选择的碰撞类型
    crashType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      index: -1,
      queryForm: {
        startDate: "",
        endDate: "",
      },
      // selectPointsList: {}, //框选设备
      moveBox: {
        x: 0,
        y: 0,
        l: 0,
        t: 0,
        isDown: false,
      },
      deviceTypeMap: {
        Camera: { icon: "shebeizichan", color: "#187AE4" },
        Camera_QiuJi: { icon: "qiuji", color: "#187AE4" },
        Camera_QiangJi: { icon: "shebeizichan", color: "#187AE4" },
        Camera_Vehicle: { icon: "qiche", color: "#1faf8a" },
        Camera_Face: { icon: "yonghu", color: "#48BAFF" },
        Camera_RFID: { icon: "RFID", color: "#8173FF" },
        Camera_Wifi: { icon: "wifi", color: "#914FFF" },
        Camera_Electric: { icon: "ZM-dianwei", color: "#614FFF" },
        Place_Hotel: { icon: "hotel", color: "#EB8A5D" },
        Place_InterBar: { icon: "diannao", color: "#EB6C6C" },
        Place_Government: { icon: "gejizhengfu", color: "#187AE4" },
        Place_School: { icon: "xuexiao", color: "#1faf8a" },
        Place_Key: { icon: "yishoucang", color: "#EA4A36" },
      },
    };
  },
  watch: {
    selectDeviceList: {
      handler(val) {
        // val.forEach(item => {
        //     item.check = true
        // })
        // this.selectPointsList = val;
      },
      // immediate: true
    },
  },
  computed: {
    //框选设备
    selectPointsList() {
      return this.selectDeviceList;
    },
  },
  created() {
    this.$nextTick(() => {
      this.movebox();
    });
    this.init();
  },
  methods: {
    init() {
      var arr = getConfigDate(-1);
      this.queryForm.startDate = arr[1] + " 00:00:00";
      this.queryForm.endDate = arr[1] + " 23:59:59";
      // endDate = this.$dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss');
    },
    handleSave() {
      let list = this.selectPointsList;
      let startDate = this.$dayjs(this.queryForm.startDate).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      let endDate = this.$dayjs(this.queryForm.endDate).format(
        "YYYY-MM-DD HH:mm:ss"
      );
      this.$emit("saveSelect", { list, startDate, endDate });
    },
    // 反显时间
    setTime(date) {
      this.queryForm.startDate = date.startDate;
      this.queryForm.endDate = date.endDate;
    },
    // 取消
    handleCancel() {
      this.$emit("boxSelect");
    },
    handleDele(item, index) {
      this.selectPointsList.splice(index, 1);
      this.$forceUpdate();
    },
    handleMouseDown(e) {
      console.log("点击了");
      this.$emit("mousedown", e);
      // let dv = document.querySelector('.move-box .select-result-dom');
      // this.moveBox.x = e.clientX;
      // this.moveBox.y = e.clientY;
      // this.moveBox.l = dv.offsetLeft;
      // this.moveBox.t = dv.offsetTop;
      // this.moveBox.isDown = true;
      // dv.style.cursor = 'move';
      // console.log(this.moveBox, 'this.moveBox')
    },
    handleMouseup() {
      // let dv = document.querySelector('.move-box .select-result-dom');
      // dv.style.cursor = 'default';
      // this.moveBox.isDown = false;
    },
    movebox() {
      // console.log(12434)
      // let dv = document.querySelector('.move-box .select-result-dom');
      // console.log(dv, 'dv')
      // window.onmousemove = (e) =>{
      //     e.stopPropagation()
      //     e.preventDefault()
      //     // console.log(this.moveBox.isDown, 2222)
      //     if(this.moveBox.isDown == false) {
      //         return
      //     }
      //     let nx = e.clientX;
      //     let ny = e.clientY;
      //     let nl = nx - (this.moveBox.x - this.moveBox.l );
      //     let nt = ny - ( this.moveBox.y - this.moveBox.t );
      //     dv.style.left = nl + 'px';
      //     dv.style.top = nt + 'px';
      // }
      // dv.onmouseup = (e) => {
      //     this.moveBox.isDown = false;
      // }
    },
  },
};
</script>
<style lang="less" scoped>
.move-box {
  // position: fixed;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
  // z-index: 999;
  .ocx-masker {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: rgba(102, 102, 102, 0.7);
  }
  .select-result-dom {
    width: 352px;
    height: 450px;
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px rgba(0, 21, 41, 0.12);
    border-radius: 4px;
    z-index: 999;
    > header {
      height: 5px;
      background: #4597ff;
    }
    .dom-title {
      padding: 10px 10px 0;
      font-size: 14px;
    }
    .dom-content {
      padding: 10px;
      .dom-content-time {
        .search_wrapper {
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          .search-wrapper-title {
            width: 70px;
          }
          /deep/ .ivu-date-picker {
            width: 300px;
          }
        }
      }
      .option-show {
        margin-bottom: 10px;
        font-size: 14px;
      }
      .layerList {
        height: 250px;
        overflow-y: auto;
        .dom-content-list {
          font-size: 14px;
          .dom-content-strip {
            display: flex;
            padding: 2px 10px 2px 0;
            &:hover {
              background: rgba(61, 183, 255, 0.1);
            }
            p {
              flex: 1;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              margin-left: 5px;
            }
            .icon-close {
              cursor: pointer;
            }
          }
        }
      }
      .btn-group {
        text-align: center;
      }
    }
  }
}
</style>
