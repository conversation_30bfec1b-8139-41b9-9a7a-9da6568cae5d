<template>
  <ui-modal title="数据输入-原始数据表" v-model="visible" :styles="styles" :footer-hide="true">
    <base-search @startSearch="startSearch"></base-search>
    <p class="statics mt-sm mb-sm">
      数据输入总量：<span class="active-color mr-sm">{{ pageData.totalCount }}</span>
    </p>
    <ui-table
      class="ui-table"
      :table-columns="tableColumns"
      :table-data="tableData"
      :minus-height="minusTable"
      :loading="loading"
    >
    </ui-table>
    <ui-page class="page" :pageData="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
  </ui-modal>
</template>
<style lang="less" scoped>
.statics {
  color: #fff;
  .active-color {
    color: var(--color-bluish-green-text);
  }
}
</style>
<script>
import allInterface from '@/config/api/tasktracking';
export default {
  props: {
    value: {},
    isBase: {
      default: true,
    },
  },
  data() {
    return {
      visible: false,
      styles: {
        // top: "0.5rem",
        width: '95%',
      },
      activeRouterName: null,
      tableColumns: [
        { type: 'index', width: 70, title: '序号' },
        { title: `${this.global.filedEnum.deviceId}`, key: 'deviceId' },
        { title: `${this.global.filedEnum.deviceName}`, key: 'deviceName' },
        { title: '组织机构', key: 'orgName' },
        { title: `${this.global.filedEnum.longitude}`, key: 'longitude' },
        { title: `${this.global.filedEnum.latitude}`, key: 'latitude' },
        { title: `${this.global.filedEnum.macAddr}`, key: 'macAddr' },
        { title: this.global.filedEnum.ipAddr, key: 'ipAddr' },
        { title: '安装地址', key: 'address' },
      ],
      tableData: [],
      minusTable: 430,
      loading: false,
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.visible = true;
      this.getDeviceInfoInitList();
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
      this.pageData = {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      };
      this.tableData = [];
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.searchData.pageNumber = val;
      this.getDeviceInfoInitList();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.searchData.pageNumber = 1;
      this.pageData.pageSize = val;
      this.searchData.pageSize = val;
      this.getDeviceInfoInitList();
    },
    startSearch(data) {
      this.pageData.pageNum = 1;
      this.searchData.pageNum = 1;
      Object.assign(this.searchData, data);
      this.getDeviceInfoInitList();
    },
    async getDeviceInfoInitList() {
      this.loading = true;
      let interfaceTracking = allInterface.baseInterface;
      if (!this.isBase) interfaceTracking = allInterface.temporateInterface;
      try {
        let { data } = await this.$http.post(interfaceTracking.queryDeviceInfoInitList, this.searchData);
        this.tableData = data.data.entities;
        this.pageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    BaseSearch: require('../components/base-search.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
  },
};
</script>
