<template>
  <!-- 接口稳定性弹框 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <div class="content auto-fill">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="检测结果详情"></line-title>
        <div class="list auto-fill">
          <!-- <table-data class="table-data" :tableData="tableData"></table-data> -->
          <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loadin="loading">
            <template #option="{ row }">
              <ui-btn-tip icon="icon-chakanxiangqing" content="查看调用详情" @handleClick="show(row)"></ui-btn-tip>
            </template>
          </ui-table>

          <loading v-if="loading"></loading>
        </div>
        <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
        </ui-page>
      </div>

      <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
    </div>
    <!-- <div class="no-box" v-else>
      <div class="no-data">
        <img src="@/assets/img/common/nodata.png" alt />
      </div>
    </div> -->
    <detail ref="detail"></detail>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .list {
      position: relative;
      margin-top: 10px;
      //   height: 450px;
      //   overflow-y: auto;

      .no-data {
        position: absolute;
        top: 50%;
        left: 50%;
      }
      img {
        width: 56px;
        height: 56px;
      }
      @{_deep}.ivu-table-cell-slot {
        margin-top: 10px;
      }
    }
  }
  /deep/.base-search {
    margin: 15px 0 0;
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import imgSrc from '@/assets/img/load-error-img.png';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      noImg: imgSrc,
      echartRing: {},
      ringStyle: {
        width: '650px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['接口调用成功次数', '接口调用失败次数'],
        showData: [
          { name: '接口调用成功次数', value: 0 },
          { name: '接口调用失败次数', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '接口稳定性',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableColumns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '接口名称', key: 'apiName', tooltip: true, minWidth: 150 },
        { title: '调用次数', key: 'Alltime', tooltip: true, minWidth: 150 },
        {
          title: '调用成功次数',
          key: 'apiSuccess',
          tooltip: true,
          minWidth: 150,
        },
        { title: '调用失败次数', key: 'apiFail', tooltip: true, minWidth: 150 },
        { title: '接口稳定性', key: 'apiRate', tooltip: true, minWidth: 150 },
        {
          title: '操作',
          key: 'option',
          slot: 'option',
          tooltip: true,
          minWidth: 150,
        },
      ],
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      loading: false,
      bigPictureShow: false,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},
    };
  },
  async mounted() {
    await this.init();
    await this.getStatistics();
    await this.initRing();
  },

  methods: {
    viewBigPic(item) {
      if (!item.trackLargeImage) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item.trackLargeImage];
      this.bigPictureShow = true;
    },
    viewBig(item) {
      this.imgList = [item.identityPhoto];
      this.bigPictureShow = true;
    },
    show(row) {
      this.$refs.detail.info(row);
    },
    // 列表
    async init() {
      let data = {
        resultId: this.$parent.row.resultId,
        indexId: this.$parent.row.indexId,
        pageSize: this.searchData.pageSize,
        pageNumber: this.searchData.pageNum,
        taskId: this.$route.query.id,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(governanceevaluation.InterfaceList, data);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.apiIndex == '1') {
            i.apiName = '人脸以图搜图接口';
          } else if (i.apiIndex == '2') {
            i.apiName = '车牌检索接口';
          }
          i.Alltime = i.apiSuccess + i.apiFail;
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.post(governanceevaluation.getDetailCount, {
          resultId: this.$parent.row.resultId,
          indexId: this.$parent.row.indexId,
        });
        this.trackList = res.data;
        this.moduleData.rateValue = this.$parent.row.resultValue || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === '接口调用成功次数') {
          item.value = this.trackList.zong - this.trackList.apiFail;
        } else {
          item.value = this.trackList.apiFail;
        }
      });
      this.zdryChartObj.count = this.trackList.zong;
      let formatData = {
        seriesName: '检测接口总次数',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },

    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    search() {
      this.searchData.pageNum = 1;
      this.init();
    },
  },
  watch: {
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    detail: require('./components/detail.vue').default,
  },
};
</script>
