<!--视频监控资产匹配率-->
<template>
  <div class="video-assetmatch-rate" ref="contentScroll">
    <div class="content">
      <div class="info-statics mt-sm mb-sm">
        <info-statics-list :staticsList="staticList"></info-statics-list>
      </div>
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果统计</span>
        </div>
      </div>
      <ui-table
        :maxHeight="contentClientHeight"
        :tableColumns="tableColumns"
        :table-data="tableData"
        :loading="loading"
      >
        <template v-for="item in tableColumns" :slot="item.slot" slot-scope="{ row }">
          <slot v-if="item.slot === 'orgRegionName'" :name="item.slot" :row="row">
            <span
              v-if="paramsData.orgRegionCode === row.orgRegionCode && row.orgRegionLevel !== '3'"
              class="province-color"
              :key="item.slot"
              >{{ row.orgRegionName }}</span
            >
            <span
              v-else-if="paramsData.orgRegionCode !== row.orgRegionCode && row.orgRegionLevel !== '3'"
              class="underline-text font-blue"
              :key="'s1' + item.slot"
              @click="handleNodeInfo(row.orgRegionCode)"
              >{{ row.orgRegionName }}</span
            >
            <span v-else :key="'s2' + item.slot">{{ row.orgRegionName }}</span>
          </slot>
          <slot v-if="item.slot === 'resultValueFormat'" :name="item.slot" :row="row"
            ><span :key="item.slot" :style="{ color: tagBgColor[row.qualified === '1' ? 0 : 1] }">{{
              row.resultValueFormat
            }}</span>
          </slot>
          <slot v-if="item.slot === 'qualified'" :name="item.slot" :row="row">
            <span
              :key="item.slot"
              class="status-tag"
              :style="{ 'background-color': tagBgColor[row.qualified === '1' ? 0 : 1] }"
              >{{ tagText[row.qualified === '1' ? 0 : 1] }}</span
            >
          </slot>
        </template>
      </ui-table>
    </div>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';
import { mapGetters, mapActions } from 'vuex';
import { matchRateStaticList, matchRateTableColumns } from './staticfields';

export default {
  name: 'video-assetmatch-rate',
  props: {
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      tableColumns: [],
      tableData: [],
      staticList: [],
      tagBgColor: ['#0E8F0E', '#BC3C19'],
      tagText: ['达标', '不达标'],
      exportLoading: false,
      curCode: '', // 当前激活的行政区划/组织机构
      contentClientHeight: 0,
    };
  },
  created() {
    this.staticList = matchRateStaticList;
    this.tableColumns = matchRateTableColumns;
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 60 * proportion : 0;
  },
  methods: {
    ...mapActions({
      setCacheRouterList: 'tabs/setCacheRouterList',
    }),
    async getTableList() {
      try {
        const params = {
          indexId: this.paramsData.indexId,
          batchId: this.paramsData.batchId,
          access: this.paramsData.access,
          displayType: this.paramsData.displayType,
          orgRegionCode: this.curCode,
          customParameters: {},
        };
        let res = await this.$http.post(evaluationoverview.getDetailData, params);
        const datas = res.data.data;
        this.tableData = datas.detail;
        this.staticList = this.staticList.map((item) => {
          item[item.key] = datas[item.key];
          return item;
        });
      } catch (e) {
        console.log(e);
      }
    },
    handleNodeInfo(orgRegionCode) {
      const querys = this.$router.history.current.query;
      let newQueryObj = { ...querys, code: orgRegionCode };
      const newQuery = JSON.parse(JSON.stringify(newQueryObj));
      const path = this.$router.history.current.path;
      this.$router.push({ path, query: newQuery });
      // 获取缓存标签页路由值并且修改相对应的code值
      let cacheRouterList = this.$util.common.deepCopy(this.cacheRouterList);
      let cacheRouterMatchRateIndex = null;
      // 找到匹配当前路由对象
      let cacheRouterMatchRate = cacheRouterList.find((item, index) => {
        let preComponentNames = ['taskToOverview', 'detectionToOverview', 'overviewEvaluation']; // 当前组件的上一级组件名
        // 缓存路由添加参数
        if (preComponentNames.includes(item.name)) {
          item.meta.queryParams.code = orgRegionCode;
          item.meta.queryParams.preCode = querys.code;
          item.meta.queryParams.displayType = this.paramsData.displayType;
          cacheRouterMatchRateIndex = index;
          return item;
        }
      });
      cacheRouterList.splice(cacheRouterMatchRateIndex, 1, cacheRouterMatchRate);
      this.setCacheRouterList(cacheRouterList);
      this.$emit('handleChangeCode', orgRegionCode);
      this.curCode = orgRegionCode;
      this.getTableList();
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.curCode = val.orgRegionCode;
          this.handleNodeInfo(val.orgRegionCode);
          this.getTableList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      cacheRouterList: 'tabs/getCacheRouterList',
    }),
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    InfoStaticsList: require('./components/info-statics-list').default,
  },
};
</script>

<style lang="less" scoped>
.video-assetmatch-rate {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      min-height: calc(100vh - 426px) !important;
    }
  }
  .content {
    padding-bottom: 10px;
    color: #fff;
    background: @bg-blue-block;
    border-radius: 4px;
  }

  .info-statics {
    width: 100%;
    height: 140px;
  }

  .abnormal-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;

    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }

  .province-color {
    color: #2baae2;
  }

  .underline-text {
    text-decoration: underline;
    cursor: pointer;
  }

  .status-tag {
    display: inline-block;
    width: 60px;
    height: 22px;
    line-height: 22px;
    font-size: 14px;
    text-align: center;
    border-radius: 4px;
    color: #ffffff;
  }
}
</style>
