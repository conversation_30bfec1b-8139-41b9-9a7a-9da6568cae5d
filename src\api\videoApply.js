import request from '@/libs/request'
import { manager } from './Microservice'

// 视频下载申请记录

// 视频下载申请记录新增
export function applyAdd(data) {
  return request({
    url: manager + `/videoDown/apply/add`,
    method: 'post',
    data
  })
}

// 视频下载申请记录列表查询
export function applyPageList(data) {
  return request({
    url: manager + `/videoDown/apply/pageList`,
    method: 'post',
    data
  })
}
// 视频下载申请记录编辑
export function applyUpdate(data) {
  return request({
    url: manager + `/videoDown/apply/update`,
    method: 'put',
    data
  })
}
// 视频下载申请记录详情
export function applyDetail(id) {
  return request({
    url: manager + '/videoDown/apply/view/' + id,
    method: 'get',
  })
}
// 视频下载申请记录删除
export function applyDelete(id) {
  return request({
    url: manager + '/videoDown/apply/remove/' + id,
    method: 'delete'
  })
}
