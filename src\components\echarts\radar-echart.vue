<template>
  <div class="echart-wrap">
    <div v-show="title.show" class="echart-text title-color">{{title.text}}</div>
    <div v-show="title.show" class="echart-subtext auxiliary-color">{{title.subtext}}</div>
    <div ref="echart" class="echart-content"></div>
  </div>
</template>
<script>
  import * as echarts from 'echarts'
  export default {
    props: {
      title: {
        type: Object,
        default: () => {
          return {
            show: true,
            text: '活动次数统计',
            subtext: "单位：次"
          }
        }
      },
      legend: {
        type: Object,
        default: () => {
          return {
            type: 'scroll',
            show: false,
            right: "0%",
            itemGap: 24,
            itemWidth: 8,
            itemHeight: 8,
            icon: 'rect',
            textStyle: {
              color: 'rgba(0, 0, 0, 0.35)',
              // lineHeight: 18,
              padding: [0, 0, 0, 3]
            }
          }
        }
      },
      color: {
        type: Array,
        default: () => {
          return ['#2C86F8', '#F29F4C']
        }
      },
      grid: {
        type: Object,
        default: () => {
          return {
            left: '0',
            top: '10%',
            right: '0.1%',
            bottom: '0',
            containLabel: true
          }
        }
      },
      tooltip: {
        type: Object,
        default: () => {
          return {
            show: true,
            borderColor: 'rgba(0, 0, 0, 0)'
          }
        }
      },
      radar: {
        type: Object,
        default: () => {
          return {
            radius: '55%',
            indicator: [
              { name: '完整性' },
              { name: '准确性' },
              { name: '唯一性' },
              { name: '关联性' },
              { name: '一致性' },
              { name: '规范性' }
            ],
            axisName: {
              color: 'rgba(0, 0, 0, 0.35)',
              fontFamily: 'Microsoft YaHei',
              fontSize: 12,
              lineHeight: 18
            },
            axisLine: {
              lineStyle: {
                color: '#F9F9F9'
              }
            },
            splitArea: {
              show: false
            },
            splitLine: {
              show: false
            }
          }
        }
      },
      series: {
        type: Array,
        default: () => {
          return [
            {
              type: 'radar',
              data: [
                {
                  symbol: 'circle',
                  symbolSize: 4,
                  itemStyle: {
                    color: '#fff',
                    borderColor: '#2C86F8',
                    borderWidth: 1
                  },
                  value: [500, 500, 500, 500, 500, 500],
                  areaStyle: {
                    color: '#fff'
                  },
                  lineStyle: {
                    width: 1,
                    color: 'rgba(255, 255, 255, 1)',
                    shadowBlur: 3,
                    shadowColor: 'rgba(147, 171, 206, 1)',
                    shadowOffsetY: 5
                  }
                }
              ],
              zlevel: 0,
              silent: true
            },
            {
              type: 'radar',
              symbol: 'none',
              data: [
                {
                  value: [300, 100, 200, 300, 400, 500],
                  name: '一周',
                  areaStyle: {
                    color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                      {
                        color: 'rgba(87, 187, 252, 0)',
                        offset: 0
                      },
                      {
                        color: 'rgba(44, 134, 248, 0.2)',
                        offset: 1
                      }
                    ])
                  },
                  lineStyle: {
                    width: 1
                  }
                },
                {
                  value: [100, 200, 300, 400, 500, 300],
                  name: '昨日',
                  areaStyle: {
                    color: new echarts.graphic.RadialGradient(0.1, 0.6, 1, [
                      {
                        color: 'rgba(255, 177, 91, 0)',
                        offset: 0
                      },
                      {
                        color: 'rgba(242, 159, 76, 0.2)',
                        offset: 1
                      }
                    ])
                  },
                  lineStyle: {
                    width: 1
                  }
                }
              ],
              zlevel: 1
            }
          ]
        }
      }
    },
    data() {
      return {
        myEchart: null
      }
    },
    mounted() {
      this.init()
    },
    deactivated () {
      this.removeResizeFun()
    },
    beforeDestroy () {
      this.removeResizeFun()
    },
    methods: {
      init() {
        this.myEchart = echarts.init(this.$refs.echart)
        let option = {
          legend: this.legend,
          color: this.color,
          grid: this.grid,
          tooltip: this.tooltip,
          radar: this.radar,
          series: this.series
        }
        this.myEchart.setOption(option)
        window.addEventListener('resize', () => this.myEchart.resize())
      },
      removeResizeFun() {
        window.removeEventListener('resize', () => this.myEchart.resize())
      }
    }
  }
</script>
<style lang="less" scoped>
  .echart-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .echart-text {
      font-size: 14px;
      font-family: 'MicrosoftYaHei-Bold';
      font-weight: bold;
      line-height: 20px;
      text-align: center;
    }
    .echart-subtext {
      margin-top: 8px;
      font-size: 12px;
      line-height: 18px;
    }
    .echart-content {
      flex: 1;
    }
  }
</style>
