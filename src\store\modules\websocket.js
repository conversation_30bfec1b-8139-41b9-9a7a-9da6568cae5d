import websocket from '@/config/api/websocket';
import notification from '@/config/api/notification';
import axios from 'axios';
export default {
  namespaced: true,
  state: {
    websock: null,
    ws_heart: null, // 维持心跳
    timeoutnum: null, // 断开重连
    lockReconnect: false, // 重连状态锁定
    logOutFlag: false, // websocket单点登陆退出标志
    websocketMsg: null, // websocket接收消息
    /**
     * message: 弹框展示信息
     * path: 跳转地址
     * redirectParams: 跳转参数
     *
     * type: 5000 资产同步获取平台数据loading
     */
    notifyConfig: {}, //弹框展示信息

    noticeNum: 0,
    tipsNum: 0, //弹框数量
  },
  getters: {
    getWebsock(state) {
      return state.websock;
    },
    getLogOutFlag(state) {
      return state.logOutFlag;
    },
    getWebsocketMsg(state) {
      return state.websocketMsg;
    },
    getNotifyConfig(state) {
      return state.notifyConfig;
    },
    getNoticeNum(state) {
      return state.noticeNum;
    },
    getTipsNum(state) {
      return state.tipsNum;
    },
  },
  mutations: {
    setWebsock(state, websock) {
      state.websock = websock;
    },
    removeWebsock(state) {
      if (state.timeoutnum) {
        clearTimeout(state.timeoutnum);
        state.timeoutnum = null;
      }
      if (state.ws_heart) {
        clearInterval(state.ws_heart);
        state.ws_heart = null;
      }
      state.websock = null;
    },
    setLogOut(state, logOutFlag) {
      state.logOutFlag = logOutFlag;
    },
    setWebsocketMsg(state, websocketMsg) {
      state.websocketMsg = websocketMsg;
    },
    setNotifyConfig(state, notify) {
      state.notifyConfig = {
        homeContent: notify.homeContent,
        message: notify.dialogContent || '',
        path: notify.uri || '',
        redirectParams: notify.redirectParams || '',
        id: notify.messageId || '',
      };
    },
    setNoticeNum(state, noticeNum) {
      state.noticeNum = noticeNum;
    },
    setTipsNum(state, tipsNum) {
      state.tipsNum = tipsNum;
    },
  },
  actions: {
    startWebsock({ commit, state, dispatch }) {
      if (typeof WebSocket === 'undefined') {
        console.log('您的浏览器不支持socket');
      } else {
        if (state.socket) {
          state.socket.close();
        }
        const userMessage = JSON.parse(window.sessionStorage.getItem('userMessage'));
        const wsurl = websocket.webSocketGlobal + userMessage.user_id;
        commit('setWebsock', new WebSocket(wsurl));
        state.websock.onopen = () => {
          console.log('WebSocket全局连接成功', state.websock.readyState);
          dispatch('resetHeart');
        };
        state.websock.onerror = () => {
          console.log('WebSocket全局连接失败');
          dispatch('reconnect');
        };
        state.websock.onmessage = (e) => {
          dispatch('resetHeart');
          const data = JSON.parse(e.data);
          // if (!data.resultCode) {
          //   return;
          // }
          // 账号在别处登陆20029，退出 / token已经失效20024,要退出
          // if (data.resultCode === 20029 || data.resultCode === 20024) {
          //   //Message.warning({content:data.msg,duration:8});
          //   console.log('token失效立刻断掉websoket');
          //   dispatch('removeWebsocket');
          //   commit('setLogOut', true);
          //   return;
          // }
          commit('setNotifyConfig', data);
          commit('setWebsocketMsg', data);
          commit('setNoticeNum', data.unread);
        };
        state.websock.onclose = () => {
          const userMessage = JSON.parse(window.sessionStorage.getItem('userMessage'));
          userMessage ? dispatch('reconnect') : null;
          console.log('WebSocket全局连接关闭');
        };
      }
    },
    reconnect({ state, dispatch }) {
      if (state.lockReconnect) {
        // 是否已经建立连接
        return;
      }
      state.lockReconnect = true;
      // 没连接上会一直重连，设置延迟避免请求过多
      state.timeoutnum && clearTimeout(state.timeoutnum);
      // 如果到了这里断开重连的倒计时还有值的话就清除掉
      state.timeoutnum = setTimeout(() => {
        console.log('重连中');
        // 然后重新连接
        dispatch('startWebsock');
        state.lockReconnect = false;
      }, 10000);
    },
    // 建立连接以及有新消息返回则重置心跳
    resetHeart({ state, dispatch }) {
      if (state.ws_heart) {
        clearInterval(state.ws_heart);
      }
      dispatch('startHeart');
    },
    // 心跳包，30s左右无数据浏览器会断开连接Heart
    startHeart({ state }) {
      if (state.websock) {
        state.ws_heart = setInterval(() => {
          state.websock.send(JSON.stringify('ping'));
        }, 30000);
      }
    },
    removeWebsocket({ commit, state }) {
      if (state.websock) {
        state.websock.close();
        commit('removeWebsock');
      }
    },
    setWebsocketMsg({ state }, websocketMsg) {
      state.websocketMsg = websocketMsg;
    },
    async setNoticeNum({ commit }) {
      try {
        let res = await axios.get(notification.getUnread);
        commit('setNoticeNum', res.data.data);
      } catch (err) {
        console.error(err);
      }
    },
    setThemeType({ commit }, themeType) {
      commit('setThemeType', themeType);
    },
    setTipsNum({ commit }, tipsNum) {
      commit('setTipsNum', tipsNum);
    },
  },
};
