<!--
    * @FileDescription: 列表
    * @Author: H
    * @Date: 2023/09/08
 * @LastEditors: liyuhang1 <EMAIL>
 * @LastEditTime: 2024-08-09 17:32:18
 -->
<template>
  <div class="list-card box-1">
    <!-- <div class="collection paddingIcon">
            <div class="bg"></div>
            <ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
            <ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
        </div> -->
    <div class="img-content">
      <div class="similarity" v-if="item.score">
        <span class="num gerling" v-if="item.score">{{ item.score }}%</span>
      </div>
      <template>
        <swiper
          ref="mySwiper"
          :options="swiperOption"
          class="my-swiper"
          :id="`swipe${item.id}`"
        >
          <swiper-slide v-for="(ite, index) in item.photoUrlList" :key="index">
            <ui-image :src="ite.photoUrl" viewer />
          </swiper-slide>
        </swiper>
        <div
          class="swiper-pagination"
          :id="`swipe${1}`"
          :class="item.photoUrlList.length < 2 ? 'my-pagination-hidden' : ''"
        ></div>
        <div class="idCardNo">{{ item.idCardNo }}</div>
      </template>
      <!-- <template>
                <ui-image :src="item.photoUrl" alt="静态库" viewer />
                <div class="idCardNo">{{ item.idCardNo }}</div>
            </template> -->
    </div>
    <!-- 静态库 -->
    <div
      class="bottom-info"
      :style="{ display: origin === 'SFIdentity' ? 'block' : 'none' }"
    >
      <time>
        <Tooltip content="姓名" placement="right" transfer theme="light">
          <i class="iconfont icon-xingming"></i>
        </Tooltip>
        {{ item.name }}
      </time>
      <p>
        <Tooltip content="库名称" placement="right" transfer theme="light">
          <i class="iconfont icon-shujulaiyuan"></i>
        </Tooltip>
        {{ item.libName }}
      </p>
    </div>
    <div class="fast-operation-bar">
      <Poptip trigger="hover" placement="right-start">
        <i class="iconfont icon-gengduo"></i>
        <div class="mark-poptip" slot="content">
          <p @click="handleSearchImage(item)">
            <i class="iconfont icon-renlian1"></i>以图搜图
          </p>
          <p @click="handleTargetAdd(item)">
            <Icon type="ios-add-circle-outline" size="14" />搜索目标添加
          </p>
        </div>
      </Poptip>
    </div>
    <!-- <div class="bottom-icon">
            <ui-btn-tip content="以图搜图" icon="icon-renlian1" @click.native="handleSearchImage(item)" />
        </div> -->
    <!-- <div class="operate-bar">
            <p class="operate-content">
                <ui-btn-tip content="档案" icon="icon-dangan2" @click.native="archivesPage(item)" />
                <ui-btn-tip content="分析" icon="icon-fenxi" />
                <ui-btn-tip content="布控" icon="icon-dunpai" transfer />
            </p>
        </div> -->
  </div>
</template>

<script>
import { swiper, swiperSlide } from "vue-awesome-swiper";
export default {
  name: "",
  components: {
    swiper,
    swiperSlide,
  },
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    // 从身份鉴别调用
    origin: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dataList: [],
      swiperOption: {
        direction: "horizontal",
      },
    };
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    collection() {},
    getScore(val) {
      let a = (val * 100).toFixed(2);
      return a;
    },

    /**
     * @description: 以图搜图
     * @param {object} item 当前信息
     */
    handleSearchImage(item) {
      if (!item.photoUrlList.length) {
        this.$Message.warning("未识别");
        return;
      }
      // 获取当前swiper展示的index
      let idx = document.getElementById("swipe" + item.id).swiper.activeIndex;
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        query: {
          imgUrl: item.photoUrlList[idx].photoUrl,
          work: 'search'
        },
      });

      window.open(href, "_blank");
    },
    // 目标添加
    handleTargetAdd(row) {
      let urlList = [];
      row.photoUrlList.forEach((row) => {
        if (row.photoUrl) {
          urlList.push(row.photoUrl);
        }
      });
      const { href } = this.$router.resolve({
        path: "/wisdom-cloud-search/search-center?sectionName=faceContent&noMenu=1",
        query: {
          traitImg: JSON.stringify(urlList),
          work: 'add'
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.list-card {
  position: relative;
  background-color: #f9f9f9;
  margin-bottom: 10px;
  height: min-content;
  // box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
  // border-radius: 4px;
  // border: 1px solid #D3D7DE;
  box-sizing: border-box;

  .operate-bar {
    width: 70px;
    height: 30px;
    background: linear-gradient(
      90deg,
      rgba(87, 187, 252, 0.8) 0%,
      #2c86f8 100%
    );
    border-radius: 0px 0px 4px 0px;
    position: absolute;
    right: -100%;
    transition: all 0.3s;
    bottom: 0;
    transform: skewX(-20deg);

    .operate-content {
      padding: 0 5px;
      transform: skewX(20deg);
      height: 100%;
      display: flex;
      align-items: center;
      color: #fff;

      /deep/ .ivu-tooltip-rel {
        padding: 6px;
      }
    }
  }
}

.collection {
  width: 30px;
  height: 30px;
  position: absolute;
  z-index: 2;
  top: 3px;
  right: 3px;

  .collection-icon {
    position: absolute;
    top: -1px;
    right: 1px;

    /deep/ .iconfont {
      font-size: 14px;
      color: #fff;
    }

    /deep/ .icon-shoucang {
      color: #888888 !important;
      text-shadow: 0px 1px 0px #e1e1e1;
    }

    /deep/ .icon-yishoucang {
      color: #f29f4c !important;
    }
  }
}

.paddingIcon {
  top: 13px;
  right: 13px;
}

.box-1 {
  width: 12%;
  // overflow: hidden;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #d3d7de;
  box-shadow: 1px 1px 7px #cdcdcd;
  margin-left: 6px;

  // &:hover {
  //     border: 1px solid #2c86f8;
  //     .operate-bar {
  //         right: -6px;
  //         bottom: -1px;
  //     }
  //     .bottom-info{
  //         display: none;
  //     }
  //     .bottom-icon{
  //         height: 51px;
  //         width: 100%;
  //         background: #2c86f8;
  //         display: block;
  //         padding: 10px;
  //         /deep/ .icon-renlian1{
  //             color: #fff;
  //         }
  //     }
  // }
  .img-content {
    width: 100%;
    position: relative;
    border: 1px solid #cfd6e6;
    height: 167px;

    img {
      width: 100%;
      height: 100%;
      display: block;
    }

    .similarity {
      position: absolute;
      z-index: 8;
      display: flex;
      flex-direction: column;
    }

    .shade {
      position: absolute;
    }

    .num {
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 4px;
      color: #fff;
      background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
      margin-bottom: 2px;
    }

    .gerling-num {
      font-size: 12px;
      padding: 2px 5px;
      border-radius: 4px;
      color: #fff;
      background: linear-gradient(180deg, #f29f4c 0%, #f29f4c 100%);
    }

    .shade {
      background: rgba(0, 0, 0, 0.7);
      font-size: 12px !important;
      width: 100%;
      bottom: 0;
      left: 0;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      line-height: 18px;
      padding: 3px 0;
    }

    .swiper-container {
      height: inherit;
    }
  }

  .bottom-info {
    display: none;
  }

  .bottom-icon {
    display: none;
  }
}

.img-content {
  width: 100%;
  position: relative;
  border: 1px solid #cfd6e6;
  height: 167px;

  img {
    width: 100%;
    height: 100%;
    display: block;
  }

  .similarity {
    position: absolute;
    z-index: 8;
    display: flex;
    flex-direction: column;
  }

  .shade {
    position: absolute;
  }

  .num {
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 4px;
    color: #fff;
    background: linear-gradient(180deg, #5ba3ff 0%, #2c86f8 100%);
    margin-bottom: 2px;
  }

  .gerling-num {
    font-size: 12px;
    padding: 2px 5px;
    border-radius: 4px;
    color: #fff;
    background: linear-gradient(180deg, #f29f4c 0%, #f29f4c 100%);
  }

  .shade {
    background: rgba(0, 0, 0, 0.7);
    font-size: 12px !important;
    width: 100%;
    bottom: 0;
    left: 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    line-height: 18px;
    padding: 3px 0;
  }

  .swiper-container {
    height: inherit;
  }
}

.bottom-info {
  padding-top: 5px;

  time,
  p {
    display: flex;
    align-items: center;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    width: 100%;

    .iconfont {
      margin-right: 2px;
      color: #888;
    }
  }
}

.bottom-icon {
  display: none;
}

.idCardNo {
  background: rgba(0, 0, 0, 0.7);
  height: 26px;
  line-height: 26px;
  text-align: center;
  margin-top: -26px;
  z-index: 999;
  position: absolute;
  width: 100%;
  color: #2c86f8;
  // font-size: 13px;
  font-weight: 600;
}

.fast-operation-bar {
  position: absolute;
  right: 10px;
  bottom: 35px;
  color: #2c86f8;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;

  .icon-gengduo {
    transform: rotate(90deg);
    transition: 0.1s;
    display: inline-block;
  }

  p:hover {
    color: #2c86f8;
  }

  &:hover {
    background: #2c86f8;
    color: #fff;

    .icon-gengduo {
      transform: rotate(0deg);
      transition: 0.1s;
    }

    border-radius: 10px;
  }

  /deep/ .ivu-poptip-popper {
    min-width: 150px !important;
    width: 40px !important;
    height: auto;
  }

  /deep/.ivu-poptip-body {
    height: auto !important;
  }
  .mark-poptip {
    color: #000;
    cursor: pointer;
    text-align: left;
    /deep/ .ivu-icon-ios-add-circle-outline {
      font-weight: 600;
    }
  }
}
</style>
