<template>
  <div>
    <ui-modal title="编辑字典" v-model="visible" :width="620">
      <div class="basedField-addOrEdit">
        <Form ref="modalData" :model="modalData" :rules="modalDataValidate" :label-width="120">
          <FormItem label="内部标识代码" prop="identCode" class="left-item">
            <Input type="text" v-model="modalData.identCode" class="width-input" :maxlength="20"></Input>
          </FormItem>
          <FormItem label="中文名称" prop="identName" class="left-item">
            <Input type="text" v-model="modalData.identName" class="width-input" :maxlength="20"></Input>
          </FormItem>
          <FormItem label="标识符" prop="tag" class="left-item">
            <Input type="text" v-model="modalData.tag" class="width-input" :maxlength="20"></Input>
          </FormItem>
          <FormItem label="字符格式" prop="fieldType" class="left-item">
            <Select v-model="modalData.fieldType" class="width-input" placeholder="请选择">
              <Option v-for="(item) in metaFieldTypeList" :key="item.id" :value="item.dataKey">{{
                item.dataValue
              }}</Option>
            </Select>
          </FormItem>
          <FormItem label="字符长度" prop="fieldLength" class="left-item">
            <Input type="text" v-model="modalData.fieldLength" class="width-input" :maxlength="20"></Input>
          </FormItem>
        </Form>
      </div>
      <template slot="footer">
        <Row>
          <Col :span="24" align="center">
            <Button type="primary" class="plr-30" @click="save">保 存</Button>
          </Col>
        </Row>
      </template>
    </ui-modal>
  </div>
</template>

<script>
import metadatamanagement from '@/config/api/metadatamanagement';

export default {
  data() {
    const validateIdentCode = (rule, value, callback) => {
      let regUrl = /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{1,20}$/;
      if (!value || value === '') {
        callback(new Error('请输入内部标识代码'));
      } else if (!regUrl.test(value)) {
        callback(new Error('请输入1-20位字母+数字的内部标识代码'));
      } else {
        callback();
      }
    };
    const validateIdentName = (rule, value, callback) => {
      let regUrl = /[^\u4E00-\u9FA5]{1,20}$/;
      if (!value || value === '') {
        callback(new Error('请输入中文名称'));
      } else if (regUrl.test(value)) {
        callback(new Error('请输入1-20位中文名称'));
      } else {
        callback();
      }
    };
    const validatetag = (rule, value, callback) => {
      // let regUrl = /[^a-zA-Z]{1,20}$/
      if (!value || value === '') {
        callback(new Error('请输入标识符'));
      }
      //  else if (regUrl.test(value)) {
      // 	callback(new Error('请输入1-20位英文字符'));
      // }
      else {
        callback();
      }
    };
    const validateFieldLength = (rule, value, callback) => {
      if (Number(value)) {
        this.modalData.fieldLength = String(Number(value));
      }
      if (!value || value === '') {
        callback(new Error('请输入字符长度'));
      }
      if (!Number(value)) {
        callback(new Error('请输入数字格式字符长度'));
      } else {
        callback();
      }
    };

    return {
      modalData: {},
      visible: false,
      modalDataValidate: {
        identCode: [{ required: true, validator: validateIdentCode, trigger: 'blur' }],
        identName: [{ required: true, validator: validateIdentName, trigger: 'blur' }],
        tag: [{ required: true, validator: validatetag, trigger: 'blur' }],
        fieldType: [{ required: true, message: '请选择字符格式', trigger: 'blur' }],
        fieldLength: [{ required: true, validator: validateFieldLength, trigger: 'blur' }],
      },
    };
  },
  created() {},
  mounted() {},
  methods: {
    open(row) {
      this.visible = true;
      // try {
      // 	let res = this.$http.get(metadatamanagement.dictView + row.id);
      // } catch (err) {
      // 	console.log(err);
      // }
      this.modalData = JSON.parse(JSON.stringify(row));
    },
    // 保存
    async save() {
      if (this.validate('modalData') === 'error') {
        return false;
      }
      try {
        let res = await this.$http.put(metadatamanagement.dictUpload, this.modalData);
        if (res.data.code === 200) {
          this.$emit('search');
        }
        this.visible = false;
      } catch (err) {
        console.log(err);
      }
    },
    validate(name) {
      let message = 'success';
      this.$refs[name].validate((valid) => {
        if (valid) {
          message = 'success';
        } else {
          message = 'error';
        }
      });
      return message;
    },
  },
  computed: {},
  props: {
    metaFieldTypeList: {
      type: Array,
      default() {},
    },
  },
  components: {},
};
</script>
<style lang="less" scoped>
.basedField-addOrEdit {
  padding-left: 30px;
  .width-input {
    width: 380px;
  }
}
</style>
