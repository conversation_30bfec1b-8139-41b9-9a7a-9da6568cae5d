<template>
  <!-- 关键属性检测 -->
  <div class="determinant-attribute" :class="getFullscreen ? 'full-screen-container' : ''">
    <HomeTitle icon="icon-shipinliushuju">
      档案数据检测
      <!--   总览、全览切换功能暂时注释，后期可能会启用   -->
      <!--      <div class="title-right">-->
      <!--        <i-->
      <!--            class="icon-font icon-shijian1"-->
      <!--            :class="{ active: !echartsShow }"-->
      <!--            @click="handleChaneTag(true)"-->
      <!--        ></i>-->
      <!--        <span @click="handleChaneTag(false)" :class="{ active: echartsShow }"-->
      <!--        >All</span-->
      <!--        >-->
      <!--      </div>-->
    </HomeTitle>
    <!-- v-ui-loading="{ loading: echartsLoading, tableData: echartData }" -->
    <div v-if="echartsShow" class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: columnarPieData }">
      <pie-echarts
        v-if="columnarPieData.length != 0"
        :columnarPieData="columnarPieData"
        ref="attributeChart"
        class="charts"
        @echartClick="echartClick"
      ></pie-echarts>
    </div>
    <div v-else class="echarts-box" v-ui-loading="{ loading: echartsLoading, tableData: dataList }">
      <draw-echarts
        v-if="dataList.length != 0"
        ref="drawEcharts"
        :echartOption="echartOption"
        class="charts"
        @echartClick="echartClick"
      ></draw-echarts>
    </div>
  </div>
</template>
<script>
import home from '@/config/api/home';
import { mapGetters } from 'vuex';
import { options } from './detectionfilechart';

export default {
  name: 'determinant-attribute',
  props: {
    batchIds: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      ringStyle: {
        width: '100%',
        height: '230px',
      },
      echartsShow: true,
      echartOption: {},
      echartsLoading: false,
      columnarPieData: [],
      dataList: {
        dataY: [],
        dataobj: [],
      },
      chartsParams: [],
    };
  },
  computed: {
    ...mapGetters({
      getFullscreen: 'home/getFullscreen',
      getHomeConfig: 'home/getHomeConfig',
    }),
  },
  mounted() {},
  methods: {
    echartClick(params) {
      if (!this.echartsShow) {
        let one = this.chartsParams[params.dataIndex].evaluationResultIndexList[params.seriesIndex];
        this.$emit('makeCreateTabs', one);
      } else {
        this.$emit('makeCreateTabs', params);
      }
    },
    async queryDeviceCheckColumnReports() {
      try {
        const params = {
          batchIds: this.batchIds,
        };
        const type = this.echartsShow ? 'regionCode' : 'regionCodes';
        params[type] = this.echartsShow ? this.getHomeConfig.regionCode : [this.getHomeConfig.regionCode];
        const {
          data: { data },
        } = await this.$http.post(home.getHomePageArchives, params);
        this.handleChartChange(data);
        this.echartsLoading = false;
      } catch (e) {
        this.echartsLoading = false;
        console.log(e);
      }
    },
    handleChaneTag(flag) {
      this.echartsShow = flag;
      this.$nextTick(() => {
        this.queryDeviceCheckColumnReports();
      });
    },
    handleAllChartOption(data) {
      this.chartsParams = data;
      this.dataList.dataY = data.map((item) => item.regionName);
      let stragety = {
        // 人像档案准确率
        ARCHIVES_PORTRAIT_ACCURACY: {
          name: '',
          jobStrategy: 'ARCHIVES_PORTRAIT_ACCURACY',
          list: [],
          color: '#4193FC',
        },
        // 人像档案置信率
        ARCHIVES_PORTRAIT_CONFIDENCE_RATE: {
          name: '',
          jobStrategy: 'ARCHIVES_PORTRAIT_CONFIDENCE_RATE',
          list: [],
          color: '#0DD083',
        },
      };
      data.forEach((ele) => {
        if (ele.evaluationResultIndexList.length) {
          ele.evaluationResultIndexList.forEach((one) => {
            one.jobStrategy in stragety ? (stragety[one.jobStrategy].name = one.indexName) : null;
            one.jobStrategy in stragety ? stragety[one.jobStrategy].list.push(one.resultValue) : null;
          });
        } else {
          stragety['ARCHIVES_PORTRAIT_ACCURACY'].list.push(0);
          stragety['ARCHIVES_PORTRAIT_CONFIDENCE_RATE'].list.push(0);
        }
      });
      this.dataList.dataobj = Object.values(stragety);
      this.echartOption = options(this.dataList);
    },
    async handleChartChange(data) {
      let realData =
        !!data && data[0] && 'evaluationResultIndexList' in data[0] ? data[0].evaluationResultIndexList : [];
      if (this.echartsShow) {
        this.columnarPieData = realData.map((item) => {
          return {
            name: item.indexName,
            value: !!item && !!item.resultValue ? Number(item.resultValue).toFixed(2) : 0,
            allMessage: item,
          };
        });
      } else {
        await this.handleAllChartOption(data);
      }
    },
  },
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    PieEcharts: require('./pie-echarts').default,
    HomeTitle: require('./home-title').default,
  },
  watch: {
    batchIds: {
      handler() {
        if (!this.getHomeConfig || !this.getHomeConfig.regionCode) return false;
        this.$nextTick(() => {
          this.queryDeviceCheckColumnReports();
        });
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less" scoped>
.determinant-attribute {
  position: absolute;
  bottom: 10px;
  width: 462px;
  background: rgba(0, 104, 183, 0.13);
  height: 48%;
  z-index: 11;

  .determinant-title {
    height: 32px;
    width: 100%;
    background: #40e1fe;
    opacity: 0.36;
  }

  .echarts-box {
    width: 100%;
    height: calc(100% - 32px) !important;

    .charts {
      width: 100%;
      height: 100% !important;
    }
  }

  .title-right {
    display: inline-block;
    float: right;

    i {
      margin-right: 10px;
    }

    cursor: pointer;
  }

  .active {
    color: #2596b0;
  }
}

.full-screen-container {
  position: absolute;
  bottom: 10px;
  height: 45%;
  margin-left: 10px;
}
</style>
