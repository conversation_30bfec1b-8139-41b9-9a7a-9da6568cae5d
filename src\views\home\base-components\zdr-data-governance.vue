<template>
  <div class="body-container" v-ui-loading="{ loading: echartsLoading, tableData: echartList }">
    <draw-echarts
      v-show="echartList.length"
      class="charts"
      :echart-option="echartOption"
      :echart-style="echartStyle"
      ref="eChartRef"
      :echarts-loading="echartsLoading"
    ></draw-echarts>
    <span class="next-echart">
      <i class="icon-font icon-zuojiantou1 f-12" @click="scrollRight('eChartRef', echartList, [], echartNum)"></i>
    </span>
  </div>
</template>

<script>
import Vue from 'vue';
import ZDRDataGovernanceTooltip from '@/views/home/<USER>/zdr-data-governance-tooltip.vue';
import dataZoom from '@/mixins/data-zoom';
import equipmentassets from '@/config/api/equipmentassets.js';
import zdrDataGovernanceStyle from '@/views/home/<USER>/module/zdr-data-governance';
import commonStyle from '@/views/home/<USER>/module/common-style';

export default {
  name: 'ZDRDataGovernance',
  mixins: [dataZoom],
  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
  },
  props: {
    year: {
      type: String,
      default: '',
    },
    homePageConfig: {},
    styleType: {},
    timeInfo: {
      type: Object,
      default: () => {
        return {
          value: 'all',
          startTime: '',
          endTime: '',
        };
      },
    },
  },
  data() {
    return {
      echartStyle: {
        width: '100%',
        height: '100%',
      },
      echartNum: 8,
      echartOption: {},
      echartsLoading: false,
      echartList: [],
      tooltipFormatter: (data) => {
        let zdrDataGovernanceTooltip = Vue.extend(ZDRDataGovernanceTooltip);
        let _this = new zdrDataGovernanceTooltip({
          el: document.createElement('div'),
          data() {
            return {
              toolTipData: data,
            };
          },
        });
        return _this.$el.outerHTML;
      },
      legendList: [],
      orgCode: '',
    };
  },
  computed: {
    homeStyle() {
      return zdrDataGovernanceStyle[`style${this.styleType}`] || zdrDataGovernanceStyle.style1;
    },
    commonStyle() {
      return commonStyle[`style${this.styleType}`] || commonStyle.style1;
    },
  },
  watch: {
    timeInfo: {
      handler() {
        this.initAll();
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.legendList = [
      {
        name: '合格',
        key: 'qualifiedNum',
        color: this.homeStyle.qualified,
        borderColor: this.homeStyle.qualifiedBorderColor,
      },
      {
        name: '不合格',
        key: 'notQualifiedNum',
        color: this.homeStyle.unqualified,
        borderColor: this.homeStyle.unqualifiedBorderColor,
      },
    ];
  },
  methods: {
    async initAll() {
      try {
        this.echartsLoading = true;
        this.echartList = [];

        // 只要重新请求数据， 都需要 重置 到第一页
        this.viewEchartIndex = 0;

        // 对应【视图治理 - ZDR数据治理】的入参
        let { startTime, endTime } = this.timeInfo;
        let data = {
          beginTime: startTime,
          endTime: endTime,
          sort: 'ASC',
        };
        let res = await this.$http.post(equipmentassets.queryStatisticsList, data);
        this.echartList = res?.data?.data || [];
        this.setEchartOption();
      } catch (e) {
        console.log(e);
      } finally {
        this.echartsLoading = false;
      }
    },
    setEchartOption() {
      let series = [];
      this.legendList.forEach((item) => {
        let seriesData = [];
        this.echartList.forEach((list) => {
          seriesData.push({
            ...list,
            value: list?.[item.key],
            color: item['color'][0],
          });
        });
        series.push({
          name: item.name,
          data: seriesData,
          barWidth: this.$util.common.fontSize(12),
          barCategoryGap: '3%',
          type: 'bar',
          stack: 'value',
          barGap: 0.3, //柱间距离
          yAxisIndex: 0,
          itemStyle: {
            borderWidth: 2, // 间距的宽度
            borderColor: {
              type: 'linear',
              colorStops: [
                { offset: 0, color: item.borderColor[0] },
                { offset: 1, color: item.borderColor[1] },
              ],
            }, //背景色
            color: {
              type: 'linear',
              colorStops: [
                { offset: 0, color: item.color[0] },
                { offset: 1, color: item.color[1] },
              ],
            },
          },
        });
      });

      let opts = {
        xAxis: this.echartList.map((item) => item.civilName),
        series: series,
        tooltipFormatter: this.tooltipFormatter,
        tooltipBg: this.commonStyle.tooltipBg,
      };
      this.echartOption = this.$util.doEcharts.baseHomeZDRDataGovernance(opts);
      setTimeout(() => {
        this.setDataZoom('eChartRef', [], this.echartNum);
      });
    },
  },
};
</script>

<style lang="less" scoped>
.body-container {
  width: 100%;
  position: relative;
  height: calc(100% - 30px) !important;
}
.charts {
  width: 100%;
  height: 100% !important;
}
</style>
