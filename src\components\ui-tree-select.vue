<template>
  <Dropdown
    trigger="custom"
    ref="drop"
    :visible="visibleValue"
    v-click-outside="onClickOutside"
    v-click-outside.mousedown="onClickOutside"
    v-click-outside.touchstart="onClickOutside"
  >
    <div
      @click="selectClick"
      class="ivu-select ivu-select-multiple ivu-select-default"
      :class="[
        visibleValue ? 'ivu-select-visible' : '',
        disabled ? 'ivu-select-disabled' : '',
        !multiple ? 'ivu-select-single' : '',
      ]"
    >
      <div class="ivu-select-selection" ref="treeSelect">
        <input type="hidden" />
        <div>
          <div
            class="ivu-tag ivu-tag-checked"
            v-for="(item, index) in values"
            :key="item.id"
          >
            <span class="ivu-tag-text">{{ item.title }}</span>
            <i
              class="ivu-icon ivu-icon-ios-close"
              @click.stop="removeTag(item, index)"
            ></i>
          </div>
          <span class="ivu-select-selected-value" v-show="singleDisplayValue">{{
            singleDisplayValue
          }}</span>
          <input
            type="text"
            v-model="query"
            v-if="!disabled && filterable"
            class="ivu-select-input"
            :style="inputStyle"
            autocomplete="off"
            :placeholder="
              values.length || singleDisplayValue ? '' : placeholder
            "
            spellcheck="false"
            @keydown="resetInputState"
            ref="input"
          />
          <i class="ivu-icon ivu-icon-ios-arrow-down ivu-select-arrow"></i>
        </div>
      </div>
    </div>
    <DropdownMenu slot="list">
      <el-tree
        class="filter-tree"
        v-bind="$attrs"
        node-key="id"
        :data="filterData"
        :props="defaultProps"
        default-expand-all
        :filter-node-method="filterNode"
        @check-change="handleCheck"
        ref="tree"
      >
      </el-tree>
    </DropdownMenu>
  </Dropdown>
</template>
<script>
export default {
  name: "ui-tree-select",
  props: {
    value: {
      type: [String, Number, Array],
      default: "",
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    expand: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "title",
      },
      capture: true,
      visibleValue: false,
      query: "",
      inputLength: 20,
      values: [],
      filterData: [],
      singleDisplayValue: "",
      singleDisplayValueId: "",
    };
  },
  computed: {
    inputStyle() {
      let style = {};
      if (this.filterable) {
        if (!this.values.length && !this.singleDisplayValue) {
          style.width = "100%";
        } else {
          style.width = `${this.inputLength}px`;
        }
      }
      return style;
    },
  },
  watch: {
    query() {
      this.$refs.tree.filter(this.query);
      this.visibleValue = true;
    },
    treeData: {
      handler(val) {
        if (val && val.length > 0) {
          this.filterData = JSON.parse(JSON.stringify(this.treeData));
        }
      },
      immediate: true,
    },
    value: {
      handler(val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.$refs.tree.setCheckedKeys(
              this.multiple ? this.value : [this.value]
            );
          }, 50);
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.filterData = JSON.parse(JSON.stringify(this.treeData));
    this.$nextTick(() => {
      setTimeout(() => {
        this.$refs.tree.setCheckedKeys(
          this.multiple ? this.value : [this.value]
        );
      }, 50);
    });
  },
  methods: {
    filterNode(value, data, node) {
      if (!value) return true;
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some((label) => label.indexOf(value) !== -1);
    },
    resetInputState() {
      this.inputLength = this.$refs.input.value.length * 12 + 20;
    },
    removeInputValue() {
      this.singleDisplayValue = "";
      this.$refs.tree.setCheckedKeys([]);
    },
    handleCheck(val, checked) {
      if (this.visibleValue) this.$refs.input.focus();
      if (this.query) this.query = "";
      if (this.multiple) {
        //多选
        this.values = this.$refs.tree.getCheckedNodes();
        setTimeout(() => {
          this.$emit(
            "input",
            this.values.map((item) => item.id)
          );
        }, 100);
      } else {
        //单选
        if (checked) {
          this.$refs.tree.setCheckedKeys([val.id]);
          this.singleDisplayValue = val.title;
          this.singleDisplayValueId = val.id;
          this.$emit("input", val.id);
          this.$emit("on-change-node", val);
        } else {
          if (this.singleDisplayValueId === val.id) {
            this.singleDisplayValue = "";
            this.$emit("input", "");
            this.$emit("on-change-node", "");
          }
        }
        this.visibleValue = false;
      }
      setTimeout(() => {
        this.$refs.drop.$children[0].$el.style.top =
          this.$refs.treeSelect.offsetHeight + "px";
      }, 50);
    },
    removeTag(item, index) {
      if (this.disabled) return false;
      this.values.splice(index, 1);
      this.$refs.input.focus();
      this.$refs.tree.setChecked(item.id, false);
    },
    selectClick() {
      if (this.disabled) return false;
      if (this.visibleValue) this.visibleValue = false;
      else this.visibleValue = true;
      this.$refs.input.focus();
    },
    onClickOutside(event) {
      this.visibleValue = false;
    },
  },
};
</script>
