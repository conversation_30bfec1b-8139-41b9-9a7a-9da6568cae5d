<template>
  <div class="row">
    <Form ref="form" :model="form" :rules="formRule" :label-width="80">
      <FormItem label="数据源" ref="tableType" prop="tableType">
        <Select v-model="form.tableType" placeholder="请选择数据源" @on-change="databaseChange()">
          <Option v-for="(item, index) in assetsDataSource" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="功能类型" v-if="form.tableType === '0' || form.tableType === '1'">
        <Select class="multiple" v-model="form.funtionTypes" multiple placeholder="请选择功能类型" :max-tag-count="1">
          <Option v-for="(item, index) in propertySearchLbgnlx" :key="index" :value="item.dataKey"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="来源标识" v-if="form.tableType === '0'">
        <Select v-model="form.sourceType" placeholder="请选择来源标识">
          <Option v-for="(item, index) in flagList" :key="index" :value="item.dataKey">{{ item.dataValue }} </Option>
        </Select>
      </FormItem>
      <FormItem label="设备标签" v-if="form.tableType === '0'">
        <div class="select-tag" @click="selectTags">
          <div v-if="form.tagIds.length === 0">请选择设备标签</div>
          <div v-else>已选择{{ form.tagIds.length }}个标签</div>
        </div>
      </FormItem>
    </Form>
    <div v-if="config" class="config" @click="configFn">
      <i class="icon-font icon-peizhibiduiziduan"></i>
      配置比对字段
    </div>
    <select-tags v-model="selectTagShow" @putTags="putTags" :selected-tag="form.tagIds"></select-tags>
  </div>
</template>

<script>
export default {
  name: 'viewassets',
  data() {
    return {
      form: {
        tableType: '',
        tagIds: [],
      },
      formRule: {
        tableType: [{ required: true, message: '请选择数据源', trigger: 'change' }],
      },
      flagList: [],
      selectTagShow: false,
    };
  },
  async created() {
    this.flagList = this.dataSourceCategory.filter((item) => {
      return item.dataKey != 2 && item.dataKey != 9;
    });
  },
  mounted() {},
  methods: {
    configFn() {
      this.$emit('configFn');
    },
    checkInfo() {
      let flag = true;
      if (!this.form.tableType) {
        flag = false;
        this.$refs.tableType.validateState = 'error';
        this.$refs.tableType.validateMessage = '请选择数据源';
      }
      return flag;
    },
    databaseChange() {
      this.form.funtionTypes = [];
      this.form.sourceType = '';
      this.$emit('databaseChange');
    },
    selectTags() {
      this.selectTagShow = true;
    },
    putTags(tagData) {
      this.form.tagIds = tagData.map((row) => row.tagId);
    },
  },
  watch: {
    dataSourceCategory(newV) {
      this.flagList = newV.filter((item) => {
        return item.dataKey != 2 && item.dataKey != 9;
      });
    },
  },
  computed: {},
  props: {
    assetsDataSource: {
      type: Array,
    },
    propertySearchLbgnlx: {
      type: Array,
    },
    dataSourceCategory: {
      type: Array,
    },
    config: {
      type: Boolean,
      default: false,
    },
    tableInfo: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    SelectTags: require('./select-tags.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .row {
    background: #fff;
    border: 1px solid transparent;
    box-shadow: 0px 2px 6px 0px rgba(0, 21, 41, 0.15);
    &:hover {
      border: 1px solid var(--color-primary);
    }
  }
}
.row {
  position: relative;
  height: 265px;
  margin-top: 50px;
  padding: 25px 50px 30px 38px;
  // background: #031e47;
  background: rgba(3, 30, 71, 0.5);
  border: 1px solid #24539c;
  border-radius: 4px;
  box-shadow: 0px 0px 30px #314a88;
}
.row:hover {
  border: 1px solid #5b8edd;
}
.config {
  position: absolute;
  bottom: -30px;
  left: 0;
  color: var(--color-btn-dashed);
  border-bottom: 1px solid var(--border-btn-dashed);
  cursor: pointer;
}

.icon-peizhibiduiziduan {
  font-size: 14px;
}

.select-tag {
  width: 270px;
  border-radius: 4px;
  border: 1px dashed var(--border-choose-device);
  color: var(--color-choose-device) !important;
  text-align: center;
  background-color: var(--bg-choose-device);
  cursor: pointer;
  :hover {
    background-color: var(--bg-choose-device-active);
  }
}
</style>
