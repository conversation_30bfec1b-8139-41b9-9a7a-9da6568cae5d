<template>
  <ui-modal v-model="visible" :styles="styles" title="参数配置" class="config">
    <tag-view class="over-flow" ref="tagView" :list="tagList" @tagChange="tagChange"></tag-view>
    <div v-if="active === 'rule'">
      <div class="base-text-color mt-sm mb-sm">请确认需要检测的规则：</div>
      <rule-list form-model="edit" :module-action="moduleAction" :index-rule-list="ruleList"></rule-list>
    </div>
    <div v-if="active === 'comparison'">
      <div class="mt-sm base-text-color mb-sm">请确认比对字段：</div>
      <transfer-repeat
        :columns1="columns1"
        :columns2="columns2"
        :tableData1="propertyList"
        :tableData2="targetList"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @selectionChange="selectionChange"
      ></transfer-repeat>
    </div>
    <template #footer>
      <Button class="ml-sm" @click="cancel">取 消</Button>
      <Button type="primary" @click="saveQuery">保 存</Button>
    </template>
  </ui-modal>
</template>
<script>
import assetsSync from '@/config/api/assetsSync';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    allPropertyList: {},
  },
  data() {
    return {
      styles: {
        width: '6rem',
      },
      visible: false,
      active: 'rule',
      tagList: [],
      defaultParams: {},
      searchParams: {},
      ruleList: [],
      configList: [
        {
          label: '规则配置',
          value: 'rule',
        },
        {
          label: '比对配置',
          value: 'comparison',
        },
      ],
      moduleAction: {
        indexType: 'BASIC_ACCURACY', // 填报准确率
        taskSchemeId: '1', //资产填报
      },
      columns1: [
        { type: 'selection', width: 50, align: 'center' },
        {
          title: '字段名',
          key: 'propertyName',
        },
        {
          title: '注释',
          key: 'propertyColumn',
        },
      ],
      columns2: [
        { title: ' ', key: '', width: 20, align: 'center' },
        {
          title: '字段名',
          key: 'checkColumnName',
        },
        {
          title: '注释',
          key: 'checkColumnValue',
        },
      ],
      targetList: [],
      propertyList: [],
      leftLoading: false,
      rightLoading: false,
    };
  },
  created() {
    this.tagList = this.configList.map((row) => row.label);
    this.getCheckRule();
  },
  methods: {
    tagChange(index) {
      this.active = this.configList[index].value;
    },
    async getCheckRule() {
      try {
        let res = await this.$http.get(governanceevaluation.getCheckRuleByIndexType, {
          params: { indexType: this.moduleAction.indexType },
        });
        let data = res.data.data.map((item) => {
          return {
            ruleId: item.id,
            isConfigure: false,
            ruleName: item.ruleName,
            ruleDesc: item.ruleDesc,
            ruleCode: item.ruleCode,
          };
        });
        this.ruleList = data;
      } catch (err) {
        console.log(err);
      }
    },
    setRule() {
      let ruleParam = JSON.parse(this.defaultParams.ruleParam);
      let ruleParamObject = {};
      ruleParam.forEach((ele) => {
        ruleParamObject[ele.ruleId] = ele;
      });
      this.ruleList.forEach((item) => {
        if (ruleParamObject[item.ruleId]) {
          item.isConfigure = ruleParamObject[item.ruleId].isConfigure;
        }
      });
    },
    async queryByConfigType() {
      try {
        const res = await this.$http.get(assetsSync.queryByConfigType, {
          params: { configType: '1' }, //资产填报为1
        });
        this.defaultParams = res.data.data;
        this.setRule();
        this.setComparison();
      } catch (err) {
        console.log(err);
      }
    },
    async saveQuery() {
      try {
        this.searchParams.configType = '1';
        !!this.defaultParams && 'id' in this.defaultParams ? (this.searchParams.id = this.defaultParams.id) : null;
        await this.$http.post(assetsSync.updateConfig, this.searchParams);
        this.$Message.success('配置成功');
        this.cancel();
      } catch (err) {
        console.log(err);
      }
    },
    selectionChange(selection) {
      const targetListObject = {};
      this.targetList.forEach((item) => {
        targetListObject[item.checkColumnName] = item;
      });
      selection.forEach((item) => {
        item.checkColumnName = item.propertyName;
        item.checkColumnValue = item.propertyColumn;
        item.checkColumnName in targetListObject
          ? this.$set(item, 'addType', targetListObject[item.checkColumnName].addType)
          : this.$set(item, 'addType', null);
      });
      this.targetList = selection;
      this.handleStrageParams(selection);
    },
    handleStrageParams(selection) {
      this.searchParams.compareParam = JSON.stringify(
        selection.map((item) => {
          let obj = {};
          obj.fieldName = item.propertyName;
          obj.fieldRemark = item.propertyColumn;
          obj.addType = item.addType;
          return obj;
        }),
      );
    },
    setComparison() {
      this.searchParams.compareParam = this.defaultParams.compareParam;
      let storageList = JSON.parse(this.defaultParams.compareParam);
      let targetListObject = {};
      // 处理右侧数据
      this.targetList = storageList.map((item) => {
        targetListObject[item.fieldName] = item;
        let one = {};
        one.checkColumnName = item.fieldName;
        one.checkColumnValue = item.fieldRemark;
        one.addType = item.addType;
        return one;
      });
      // 处理左侧的选中
      this.propertyList.forEach((item) => {
        if (item.propertyName in targetListObject) {
          this.$set(item, '_checked', true);
        }
      });
    },
    cancel() {
      this.$refs.tagView.curTag = 0;
      this.visible = false;
      this.searchParams = {};
      this.active = 'rule';
    },
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.queryByConfigType();
        this.propertyList = this.$util.common.deepCopy(this.allPropertyList);
      } else {
        this.cancel();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
    ruleList: {
      handler(val) {
        this.searchParams.ruleParam = JSON.stringify(val);
      },
      deep: true,
    },
  },
  components: {
    TagView: require('@/components/tag-view.vue').default,
    RuleList: require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/rules/index')
      .default,
    TransferRepeat: require('@/components/transfer-repeat').default,
  },
};
</script>
<style lang="less" scoped>
.config {
  @{_deep}.transfer-table {
    padding: 0;
    .transfer-table-left-content,
    .transfer-table-right-content {
      height: 300px;
      overflow-y: auto;
    }
  }
}
</style>
