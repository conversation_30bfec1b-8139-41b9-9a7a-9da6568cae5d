<template>
  <div class="dom-wrapper" v-if="isShow">
    <div class="dom" @click="($event) => $event.stopPropagation()">
      <header>
        <span>抓拍详情</span>
        <Icon type="md-close" size="14" @click.native="isShow = false" />
      </header>
      <section class="dom-content">
        <div class="info-box">
          <div class="info-box-left">
            <div class="community-box" v-if="datailsInfo.placeName">
              <img src="@/assets/img/community-management/community-icon.png" />
              {{ datailsInfo.placeName }}
            </div>
            <div
              class="community-box community-box-alarm"
              v-if="datailsInfo.trackAlarmType"
            >
              <img src="@/assets/img/community-management/alarm-icon.png" />
              {{ datailsInfo.trackAlarmType | alarmTypeFilter }}
            </div>
            <div class="thumbnail">
              <img
                v-lazy="datailsInfo.traitImg || datailsInfo.sceneImg"
                alt=""
              />
            </div>
            <div
              class="record-title"
              :style="{
                'justify-content': datailsInfo.vid
                  ? 'space-between'
                  : 'flex-start',
              }"
            >
              <span :class="{ active: checkIndex == 0 }"> 抓拍记录 </span>
            </div>
            <div class="through-record" v-if="checkIndex == 0">
              <div class="wrapper-content">
                <span class="label">抓拍地点</span>：
                <!-- <ui-textOver-tips class="message" refName="deviceName" :content="datailsInfo.deviceName"></ui-textOver-tips> -->
                <span
                  class="message ellipsis"
                  :class="datailsInfo.taskType != '3' ? 'device-click' : ''"
                  v-html="datailsInfo.deviceName"
                  :title="
                    datailsInfo.deviceName
                      ? datailsInfo.deviceName.replace(/(<\/?span.*?>)/gi, '')
                      : ''
                  "
                ></span>
              </div>
              <div class="wrapper-content">
                <span class="label">抓拍时间</span>：
                <span class="message">{{ datailsInfo.absTime || "--" }}</span>
              </div>
              <div class="line"></div>
              <div class="sub-title wrapper-content">
                <span class="label">人员信息</span><span>：</span>
              </div>
              <div
                class="wrapper-content"
                v-for="(item, index) in faceList"
                :key="index"
              >
                <span class="label">{{ item.title }}</span
                ><span>：</span>
                <span
                  class="message"
                  :class="
                    item.clickHandler && datailsInfo[item.key] ? 'herf' : ''
                  "
                  v-if="!item.dictionary"
                  @click="
                    item.clickHandler && datailsInfo[item.key]
                      ? item.clickHandler(datailsInfo[item.key])
                      : () => {}
                  "
                  >{{ datailsInfo[item.key] || "--" }}</span
                >
                <span class="message" v-else>{{
                  datailsInfo[item.key]
                    | commonFiltering(translate(item.dictionary))
                }}</span>
              </div>
            </div>
          </div>
          <div class="info-box-right">
            <i
              class="iconfont icon-doubleleft arrows cursor-p prev"
              @click="handleTab('left')"
            ></i>
            <details-largeimg
              boxSeleType="rect"
              :acrossAppJump="true"
              :info="datailsInfo"
              :btnJur="[
                'tp',
                'rl',
                'ytst',
                'ss',
                'lx',
                'fd',
                'sx',
                'xz',
                // 'sc',
                'hy',
              ]"
              @collection="collection"
              :collectionType="5"
              :algorithmType="1"
            ></details-largeimg>
            <i
              class="iconfont icon-doubleright arrows cursor-p next"
              @click="handleTab('right')"
            ></i>
          </div>
        </div>
      </section>
      <footer>
        <i
          class="iconfont icon-doubleleft arrows mr-10 cursor-p"
          @click="handleTab('left')"
        ></i>
        <div class="box-wrapper">
          <div class="present" id="present"></div>
          <ul
            class="list-wrapper"
            id="scroll-ul"
            :style="{ width: 120 * cutList.length + 'px' }"
          >
            <li
              class="list-box"
              @click="handleTab(item, index)"
              v-for="(item, index) in cutList"
              :key="item.recordId"
            >
              <div class="img-box">
                <img v-lazy="item.traitImg || item.sceneImg" alt="" />
              </div>
            </li>
          </ul>
        </div>
        <i
          class="iconfont icon-doubleright arrows ml-10 cursor-p"
          @click="handleTab('right')"
        ></i>
      </footer>
    </div>
  </div>
</template>

<script>
import { mapActions, mapGetters, mapMutations } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import detailsLargeimg from "@/components/detail/details-largeimg.vue";
import { cutMixins } from "./mixins.js";
import { commonMixins } from "@/mixins/app.js";
import { getAlarmDetailPageList } from "@/api/monographic/juvenile.js";
export default {
  name: "",
  mixins: [commonMixins, cutMixins], //全局的mixin
  components: {
    swiper,
    swiperSlide,
    detailsLargeimg,
  },
  props: {
    isNoSearch: {
      type: Boolean,
      default: false,
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    tableIndex: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      swiperOption: {
        spaceBetween: 10,
        slidesPerView: 12,
        freeMode: true,
        watchSlidesProgress: true,
        navigation: {
          nextEl: ".snap-next",
          prevEl: ".snap-prev",
        },
      },
      cutList: [],
      activeIndex: 0,
      faceList: [
        { key: "name", title: "姓名", dictionary: "" },
        { key: "age", title: "年龄", dictionary: "" },
        { key: "gender", title: "性别", dictionary: "genderList" },
        {
          key: "idCard",
          title: "身份证号",
          dictionary: "",
          clickHandler: (idCardNo) => {
            const { href } = this.$router.resolve({
              path:
                this.$route.path.split("-")?.[0] + "-archive/people-dashboard",
              query: {
                archiveNo: idCardNo,
                source: "people",
                initialArchiveNo: idCardNo,
              },
            });
            window.open(href, "_blank");
          },
        },
        { key: "national", title: "民族", dictionary: "nationList" },
        { key: "phoneNo", title: "联系方式", dictionary: "" },
        { key: "address", title: "户籍地址", dictionary: "" },
      ],
      transformWidth: 0,
      pageNum: 0,
      exampleImg: require("@/assets/img/login/bg.png"),
      rate: 1,
      datailsInfo: {},
      recordList: {},
      overlay: false,
      collectionType: 0,
      checkIndex: 0,
      imgBoxWidth: "",
      linkVehicle: {},
      pageParam: {
        pageNumber: 1,
        pageSize: 20,
      },
      isShow: false,
      bizAlarmType: "",
      idCardNo: "",
      firstPage: false,
      lastPage: false,
    };
  },
  watch: {},
  computed: {
    ...mapGetters({
      nationList: "dictionary/getNationList", //民族
      genderList: "dictionary/getGenderList", //性别
    }),
    totalSize() {
      return Math.ceil(this.cutList.length / 11);
    },
  },
  async created() {
    await this.getDictData();
  },
  mounted() {},
  methods: {
    // 切换
    handleTab(item, index) {
      if (item == "left") {
        if (this.activeIndex == 0) {
          if (this.isNoSearch) {
            this.activeIndex = this.cutList.length - 1;
            this.handleTab(this.cutList[this.activeIndex], this.activeIndex);
            return;
          }
          this.goLastpage();
          return;
        }
        index = this.activeIndex - 1;
        item = this.cutList[index];
      }
      if (item == "right") {
        if (this.activeIndex + 1 == this.cutList.length) {
          if (this.isNoSearch) {
            this.activeIndex = 0;
            this.handleTab(this.cutList[this.activeIndex], this.activeIndex);
            return;
          }
          this.goNextPage();
          return;
        }
        index = this.activeIndex + 1;
        item = this.cutList[index];
      }

      this.datailsInfo = {
        ...item,
      };
      this.activeIndex = index;
      this.play(index);
    },
    collection(flag) {
      this.$set(this.cutList[this.activeIndex], "myFavorite", flag);
    },
    // 不查询使用提供的列表
    showList(nowIndex) {
      this.cutList = this.tableList.map(
        ({ faceCaptureVo, idCardNo, ...el }) => ({
          idCard: idCardNo,
          ...el,
          ...faceCaptureVo,
        })
      );
      this.activeIndex = nowIndex;
      let info = this.cutList[nowIndex];
      this.datailsInfo = {
        ...info,
      };
      this.isShow = true;
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(nowIndex);
      });
    },
    /**
     * 首次查询
     * @param bizAlarmType 告警类型，默认1：1频繁出没娱乐场所 2上学时间校外出现 3深夜出现
     */
    async queryInfo(bizAlarmType, idCardNo = "") {
      this.isShow = true;
      this.bizAlarmType = bizAlarmType;
      this.idCardNo = idCardNo;
      this.pageParam = {
        pageNumber: 1,
        pageSize: 20,
      };
      await this.getDataList(bizAlarmType, idCardNo);
      this.resetLeftPage(0);
    },
    async getDataList(bizAlarmType, idCardNo, nowIndex = 0) {
      const param = {
        bizAlarmType: bizAlarmType || this.bizAlarmType,
        idCardNo: idCardNo || this.idCardNo,
        ...this.pageParam,
      };
      let { data } = await getAlarmDetailPageList(param);
      const dataList =
        data?.entities?.map(({ faceCaptureVo, idCardNo, ...el }) => ({
          idCard: idCardNo,
          ...el,
          ...faceCaptureVo,
        })) || [];
      this.firstPage = data.firstPage;
      this.lastPage = data.lastPage;
      this.activeIndex = nowIndex;
      this.datailsInfo = dataList[nowIndex] || {};
      this.cutList = dataList || [];
      this.$nextTick(() => {
        const divElement = document.querySelector(".list-wrapper");
        const firstElement = divElement.firstChild;
        this.imgBoxWidth = firstElement.clientWidth;
        this.imageNumWidth = this.imgBoxWidth;
        this.locationPlay(nowIndex);
      });
    },
    // 上一页
    goLastpage() {
      if (this.firstPage) {
        return this.$Message.warning("已经是第一页");
      }
      this.pageParam.pageNumber--;
      this.getDataList(this.bizAlarmType, this.idCardNo, 19);
      this.resetRightPage(19);
    },
    // 下一页
    goNextPage() {
      if (this.lastPage) {
        return this.$Message.warning("已经是最后一页");
      }
      this.pageParam.pageNumber++;
      this.getDataList(this.bizAlarmType, this.idCardNo, 0);
      this.resetLeftPage(0);
    },
    // 获取字典
    translate(value) {
      return this[value];
    },
  },
  filters: {
    alarmTypeFilter(val) {
      if (!val) return "";
      const alarmType = {
        1: "地点异常",
        2: "时间异常",
      };
      return alarmType[val] || "";
    },
  },
};
</script>

<style lang="less" scoped>
@import "./style/index";

.info-box-left {
  .community-box {
    cursor: pointer;
    width: 200px;
    height: 40px;
    display: flex;
    align-items: center;
    background: linear-gradient(
      270deg,
      rgba(44, 134, 248, 0) 0%,
      rgba(44, 134, 248, 0.1) 100%
    );
    font-size: 16px;
    color: #2c86f8;
    overflow: hidden;
    &.community-box-alarm {
      background: linear-gradient(
        270deg,
        rgba(234, 74, 54, 0) 0%,
        rgba(234, 74, 54, 0.4) 100%
      );
      color: #ea4a36;
    }
    img {
      width: 20px;
      height: 20px;
      margin: 0 10px;
    }
  }
  .record-title {
    display: flex;

    > span {
      cursor: pointer;
    }

    .record-right {
      margin-left: 20px !important;
    }
  }
}

.device-click {
  cursor: pointer;
  text-decoration: underline;
}

.rcgl {
  .title {
    color: #2c86f8;
  }

  .imgBox {
    cursor: pointer;
    width: 80%;
    margin: 5px 0;
    display: block;
    position: relative;

    img {
      width: 100%;
    }

    .driverFlag {
      position: absolute;
      right: 2px;
      top: 2px;
      color: #fff;
      background: red;
      border-radius: 20px;
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.line {
  margin: 10px 0;
  height: 1px;
  background: #d3d7de;
}

.sub-title {
  font-size: 12px;

  .label {
    color: #2c86f8 !important;
    font-weight: bold;
  }
}
.active-box {
  // width: 100px;
  // height: 100px;
  border: 2px rgba(44, 134, 248, 1) solid !important;
  // position: absolute;
  // z-index: 1;
  // left: 10px;
  // transition: all 0.2s;
}
.herf {
  color: #2c86f8 !important;
  cursor: pointer;
}
</style>
