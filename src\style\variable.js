export const darkTheme = {
  // 主色
  '--color-primary': '#2b84e2',
  // 滚动条
  '--bg-scrollbar': '#02162b',
  '--scrollbar-thumb': 'rgba(25, 143, 226, 0.58)',
  '--scrollbar-track': 'inset 0 0 6px rgba(0, 0, 0, 0.3)',
  '--shadow-scrollbar-thumb': 'none',

  '--bg-darkblue-block': '#041129',
  '--bg-header': 'linear-gradient(45deg, #092246, #146db0)',
  '--bg-menu-item-active': 'linear-gradient(90deg, #0a2643 0%, #108dc5 100%)',
  '--bg-menu-item-active-horizontal': 'linear-gradient(360deg, #0c7cb5 0%, #065781 100%)',
  '--bg-menu-item-after-horizontal': '#29c9fb',
  '--bg-menu-item-hover-horizontal': 'linear-gradient(360deg, rgba(12, 124, 181, 0.6) 0%, rgba(6, 87, 129, 0.6) 100%)',
  '--color-header-name': 'linear-gradient(to top, #6d7889 0%, #ffffff 30%)',
  '--bg-layout-header-tab': '#011B37',
  '--shadow-layout-header-tab': '0 1px 1px rgba(0, 0, 0, 0.1)',
  '--color-tab-pane': '#768192',
  '--bg-tab-pane-active': '#0d3560',
  '--color-tab-pane-active': '#fff',
  '--color-tab-icon': '#07355E',
  '--bg-layout-content': '#041129',
  '--bg-content': '#071b39',
  '--bg-sub-content': '#08264d', // 某个内容块的背景色
  '--bg-sub-echarts-content': '#08264d', // 某个带图表内容块的背景色
  '--border-sub-echarts-content': 'transparent', // 某个带图表内容块的边框色
  '--bg-sub-echarts-title': 'rgba(43, 132, 226, 0.1)', // 某个带图表内容块的标题背景色
  '--shadow-sub-echarts-content': 'none', // 某个带图标内容块的阴影
  '--color-icon-echarts': '#05FEF5', //坐标card左上角icon颜色
  '--color-title-echarts': '#05FEF5', //坐标card左上角文字颜色

  '--color-logo': 'linear-gradient(180deg, #08DFFE 0%, #119AED 100%)',
  '--shadow-tab-contextmenu': '0 1px 6px rgb(0 0 0 0.2)',

  '--color-page-item': '#2b84e2',
  '--bg-page-item-hover': '#010f1f',
  '--color-page-item-hover': '#2b84e2',

  '--color-no-data-img': '#385074',
  '--bg-avatar': '#0a3260',
  '--border-avatar': '#6BA6E3',
  '--color-avatar': '#99a4af',
  '--color-touxiang': '#F5F5F5',
  '--color-header-icon': '#A6BACD',

  // iview
  '--bg-table': '#071B39',
  '--bg-table-header-th': '#092955',
  '--color-table-header-th': '#fff',
  '--bg-table-body-td': '#062042',
  '--bg-table-body-td-2n': '#041939',
  '--bg-table-row-hover': '#073456',
  '--shadow-table-header': 'none',
  '--bg-checkbox-inner': '#02162b',
  '--border-checkbox-inner': '#10457e',
  '--bg-checkbox-disabled': '#02162b',
  '--border-checkbox-disabled': '#092a43',
  '--color-ivu-table-fixed-right': 'rgba(0, 0, 0, 0.4)',
  '--bg-table-tips': '#051124',
  '--border-table': 'rgba(13, 71, 125, 0.6)',
  '--bg-tree-checkbox-inner': '#114D98',
  '--border-tree-checkbox-inner': '#85A2D6',
  '--bg-tree-current-node-switch': '#02162B',

  // primary
  '--bg-btn-primary': 'linear-gradient(180deg, #2b84e2 0%, #083a78 100%)',
  '--bg-btn-primary-hover': 'linear-gradient(180deg, #65a7ee 0%, #175cb2 100%)',
  '--bg-btn-primary-active': 'linear-gradient(180deg, #408fe5 0%, #0b50a7 100%)',
  '--color-btn-primary-disabled': '#415060',
  '--bg-btn-primary-disabled': '#02162b',
  '--border-btn-primary-disabled': '#1d3a5a',
  // dashed
  '--bg-btn-dashed': '#02162b',
  '--color-btn-dashed': '#2b84e2',
  '--border-btn-dashed': '#174f98',
  '--color-btn-dashed-hover': '#4e9ef2',
  '--border-btn-dashed-hover': '#2b84e2',
  '--border-btn-dashed-active': '#146ac7',
  // default
  '--bg-btn-default': '#02162b',
  '--color-btn-default': '#2c86f8',
  '--border-btn-default': '#174f98',
  '--color-btn-default-hover': '#4e9ef2',
  '--border-btn-default-hover': '#2b84e2',
  '--border-btn-default-active': '#10457e',
  // text
  '--bg-btn-text': 'transparent',
  '--color-btn-text': '#2b84e2',
  '--border-btn-text': 'none',
  '--color-btn-text-hover': '#4e9ef2',
  '--border-btn-text-hover': 'none',
  '--border-btn-text-active': 'none',
  // swith
  '--bg-switch-after-default': '#FFFFFF',
  '--bg-switch-after-active': '#FFFFFF',
  '--bg-switch-default': '#02162B',
  '--bg-switch-default-disabled': '#02162B',
  '--bg-switch-active': '#2B84E2',
  '--bg-switch-active-disabled': '#27588E',
  '--border-switch-default': '#2B84E2',
  '--border-switch-default-disabled': '#092A43',

  '--border-modal-content': '#0d4b6f',
  '--color-modal-close': '#768192',
  '--border-modal-footer': '#0d477d',

  '--color-label': '#fff',
  '--border-input': '#10457e',
  '--bg-input': '#02162b',
  '--color-input': '#fff',
  '--color-input-disabled': '#CCCCCC',
  '--border-input-hover': '#2b84e2',
  '--color-input-placeholder': '#4f5d6b',
  '--bg-select-dropdown': '#051e43',
  '--color-select-item': '#fff',
  '--bg-select-item-hover': '#023960',
  '--bg-select-item-active': '#07346e',
  '--color-select-item-active': '#fff',
  '--color-select-item-hover': '#fff',
  '--bg-select-disabled': '#02162B',
  '--color-select-arrow': '#2b84e2',
  '--bg-tag': '#07346e',

  '--color-date-picker-header-label': '#8D9CB1',
  '--color-date-picker-cells-cell-disabled': '#000',

  '--bg-poptip': '#0d3560',
  '--bg-tooltip': '#0f2f59',
  '--border-tooltip': '#0d4a81',
  '--color-tooltip-inner': '#fff',
  '--border-tooltip-arrow': '#0d4a81',

  '--bg-msg': '#01152c',

  '--bg-ivu-input-group-append': 'linear-gradient(180deg, #2b84e2 0%, #083a78 100%)',
  '--bg-ivu-input-group-append-bover': 'linear-gradient(180deg, #65a7ee 0%, #175cb2 100%)',

  // element
  '--bg-el-tree-node': '#184f8d',
  '--color-el-tree-node__expand-icon': '#C0C4CC',
  '--bg-el-tree-node-is-expanded': 'transparent',
  '--bg-el-notification__title': '#037CBE',
  '--color-el-notification-shadow': 'rgba(0, 0, 0, .4)',
  '--color-el-notification__title': '#FFFFFF',
  '--color-el-notification__closeBtn': '#FFFFFF',
  '--bg-el-checkbox__inner': '#02162b',
  '--border-checkbox__inner': '#10457e',

  '--color-dropdown-item': 'rgba(255, 255, 255, 0.8)',
  '--color-dropdown-item-hover': '#ffffff',

  '--bg-quadrate-outside': 'rgba(188, 60, 25, 0.3)',
  '--bg-quadrate-interior': '#BC3C19',
  '--bg-circle-outside': 'rgba(255, 255, 255, 0.2)',
  '--bg-circle-interior': 'rgba(255, 255, 255, 0.5)',
  '--bg-card': '#0B264F',
  '--color-card': '#ffffff',
  '--bg-wenhao': 'linear-gradient(180deg, #F58D3D 0%, #B8580D 100%)',
  '--bg-markers': '#bc3c19',
  '--border-color': '#07355e',
  '--color-title': '#2B84E2',
  '--color-sub-title': '#27EEE1',
  '--color-content': '#FFFFFF',
  '--color-no-data-text': '#515A6e',
  '--bg-title-rect': '#239DF9',
  '--color-sub-title-inpage': '#fff',
  '--color-card-title': '#2B84E2',

  '--bg-collapse-item': '#062f68',
  '--color-collapse-arrow': '#99B9E6', //箭头可能用#fff，取设计规范里的色值
  '--devider-line': '#074277',
  '--bg-info-card': '#0F2F59',
  '--bg-info-card-option': 'rgba(0, 0, 0, 0.3)',
  '--color-info-card-option': '#2b84e2',
  '--color-info-card-label': '#8797AC',
  '--color-info-card-content': '#8797AC',
  '--border-info-card': 'transparent',
  '--bg-navigation': '#0a2144',
  '--color-navigation-title': '#2b84e2',

  '--color-success': '#0E8F0E',
  '--color-warning': '#D66418',
  '--color-failed': '#BC3C19',
  '--color-offline': '#4F5458',
  '--color-active': '#2B84E2',
  '--color-tips': '#e44f22',

  '--color-weizhipai': '#787878',
  '--color-yiguanbi': '#375878',
  '--color-jinxingzhong': '#D9B916',
  '--color-daiqianshou': '#087187',

  '--color-progress-default': '#2B84E2',
  '--color-progress-warning': '#E24A2B',
  '--color-progress-success': '#19A709',

  // 以下分别对应设计规范-数据展示卡片1-7种渐变背景色
  '--bg-card-gradient-light-blue': 'linear-gradient(359deg, #007BD9 -32%, #0CA7DC 98%)',
  '--bg-card-gradient-light-green': 'linear-gradient(180deg, #14E0E8 0%, #067E9C 100%)',
  '--bg-card-gradient-blue-purple': 'linear-gradient(180deg, #6EB5FC 0%, #6A29DC 100%)',
  '--bg-card-gradient-dark-blue': 'linear-gradient(0deg, #1641EE 0%, #67ACFB 100%)',
  '--bg-card-gradient-light-orange': 'linear-gradient(180deg, #B19741 0%, #9C6D36 98%)',
  '--bg-card-gradient-light-purple': 'linear-gradient(0deg, #7829E4 -35%, #B796E4 100%)',
  '--bg-card-gradient-blue-green': 'linear-gradient(180deg, #35BFFD 0%, #0B7CAF 98%)',
  // 卡片的icon的背景色 #DAF5F8 0.2
  '--bg-card-icon': 'rgba(218, 245, 248, 0.2)',

  // 以下分别对应设计规范-数据展示卡片1-12种背景色
  '--bg-card-cyan': 'rgba(16, 165, 170, 0.21)',
  '--bg-card-orange': 'rgba(172, 127, 13, 0.21)',
  '--bg-card-purple': 'rgba(116, 93, 204, 0.21)',
  '--bg-card-blue': 'rgba(72, 111, 236, 0.21)',
  '--bg-card-deep-purple': 'rgba(167, 73, 205, 0.21)',
  '--bg-card-green': 'rgba(12, 69, 68, 1)',
  '--bg-card-red': 'rgba(60, 33, 64, 1)',
  '--bg-card-deep-cyan': 'rgba(21, 61, 59, 1)',
  '--bg-card-grass-green': 'rgba(177, 197, 26, 0.21)',
  '--bg-card-light-pink': 'rgba(48, 43, 71, 1)',
  '--bg-card-mint-green': 'rgba(39, 178, 138, 0.21)',
  '--bg-card-pink': 'rgba(240, 77, 77, 0.21)',

  //  以下分别对应设计规范-数据展示卡片1-12种字体图标渐变色
  '--icon-card-gradient-cyan': 'linear-gradient(180deg, #1bafd5 0%, #0a9f90 100%)',
  '--icon-card-gradient-orange': 'linear-gradient(180deg, #b58e0f 0%, #bb6603 100%)',
  '--icon-card-gradient-purple': 'linear-gradient(360deg, #52049f 0%, #9f5ce4 100%)',
  '--icon-card-gradient-blue': 'linear-gradient(360deg, #1641ee 0%, #67acfb 100%)',
  '--icon-card-gradient-deep-purple': 'linear-gradient(180deg, #c058ea 0%, #a321d4 100%)',
  '--icon-card-gradient-green': 'linear-gradient(180deg, #26d82c 0%, #22c625 27%, #127d0a 79%)',
  '--icon-card-gradient-red': 'linear-gradient(180deg, #ee5bdb 0%, #87135c 88%)',
  '--icon-card-gradient-deep-cyan': 'linear-gradient(180deg, #1aaf76 0%, #308a8a 100%)',
  '--icon-card-gradient-grass-green': 'linear-gradient(180deg, #9b9944 0%, #406411 100%)',
  '--icon-card-gradient-light-pink': 'linear-gradient(180deg, #d14629 0%, #7c310e 100%)',
  '--icon-card-gradient-mint-green': 'linear-gradient(180deg, #22ad9f 0%, #11673b 86%)',
  '--icon-card-gradient-pink': 'linear-gradient(180deg, #bb5d69 0%, #a12c45 100%)',

  //  以下分别对应设计规范-数据展示卡片1-12种字体色
  '--font-card-cyan': '#19d5f6',
  '--font-card-orange': '#ebb225',
  '--font-card-purple': '#9075f8',
  '--font-card-blue': '#2f7ee5',
  '--font-card-deep-purple': '#af38dd',
  '--font-card-green': '#22c326',
  '--font-card-red': '#cb428d',
  '--font-card-deep-cyan': '#16a884',
  '--font-card-grass-green': '#b0b81d',
  '--font-card-light-pink': '#dd4826',
  '--font-card-mint-green': '#129b19',
  '--font-card-pink': '#ce415b',

  // 对应设计规范的导航2的Tag标签
  '--bg-nav-tag': '#071B39',
  '--color-nav-tag': '#8797AC',
  '--border-nav-tag': '#085C8A',
  '--bg-nav-tag-active': '#17497E',
  '--color-nav-tag-active': '#FFFFFF',
  '--border-nav-tag-active': '#085C8A',
  '--bg-nav': '#0B2348',

  // 对应设计规范的数据录入2中的选择设备
  '--bg-choose-device': 'rgba(43, 132, 226, 0.102)',
  '--border-choose-device': '#2B84E2',
  '--color-choose-device': '#2B84E2',
  '--bg-choose-device-active': 'rgba(43, 132, 226, 0.102)',
  '--border-choose-device-active': '#1994DE',
  '--color-choose-device-active': '#1994DE',

  '--bg-form-item': '#0A2144', // 对应设计规范 有背景色表单项
  '--color-form-label': 'rgba(255, 255, 255, 0.45)', // 表单的label
  '--color-form-icon-validate': '#ED4014', // 表单的必填项提醒的颜色

  // 对应设计规范 导航1 Tabs标签页
  '--color-switch-tab': '#56789C',
  '--color-switch-tab-active': '#2C86F8',
  '--bg-switch-tab': '#0A2043',
  '--color-switch-tag-tab': '#8F9BA7',
  '--border-switch-tag-tab': '#174F98',
  '--bg-switch-tag-tab': '#022143',
  // 对应设计规范 导航1 选项卡样式的标签页未激活颜色
  '--color-border-card-tab': '#56789C',
  // 对应设计规范 导航1 选项卡渐变标签页背景色
  '--bg-gradient-tab': 'linear-gradient(0deg, rgba(43, 132, 226, 0) -35%, #2B84E2 100%)',

  // 对应设计规范左侧tab栏
  '--bg-vertical-tab': 'rgba(5, 143, 179, 0.102)',
  '--bg-vertical-tab-hover': '#023960',
  '--bg-vertical-tab-active': '#2B84E2',
  '--border-vertical-tab': '#144E8B',
  '--color-vertical-tab': '#99A4AF',
  '--color-vertical-tab-active': '#FFFFFF',
  '--color-vertical-tab-hover': '#FFFFFF',
  '--devider-vertical-tab': '#394960',
  '--devider-vertical-tab-active': '#6EA9E8',
  '--devider-vertical-tab-hover': '#596E8C',
  '--color-vertical-tab-icon': '#99A4AF',
  '--color-vertical-tab-btn': '#56789C',
  '--color-vertical-tab-btn-disabled': '#415060',
  '--color-vertical-active-tab-btn': '#95C1F1', //已激活的卡片右侧按钮默认颜色
  '--color-vertical-active-tab-btn-hover': '#FFFFFF', //已激活的卡片右侧按钮hover的颜色
  '--color-vertical-active-tab-btn-disabled': '#415060', //已激活的卡片右侧按钮不可点击
  '--color-vertical-hover-tab-btn': '#F5F5F5', //hover的卡片右侧按钮可点击
  '--color-vertical-hover-tab-btn-hover': '#2B84E2', //hover的卡片右侧按钮悬停
  '--color-vertical-hover-tab-btn-disabled': '', //hover的卡 片右侧按钮不可点击

  //左右切换
  '--border-carousel-arrow': '#10457e',
  '--bg-carousel-arrow': '#02162b',
  '--bg-carousel-arrow-active': '#02162b',
  '--border-carousel-arrow-active': '#2b84e2',
  '--color-carousel-arrow': '#2b84e2',
  '--bg-carousel-arrow-hover': '#02162b',
  '--border-carousel-arrow-hover': '#2b84e2',
  '--icon-carousel-arrow-hover': '#2b84e2',

  //echarts分页
  '--bg-next-echarts': '#02162b',
  '--border-next-echarts': '#10457e',
  '--color-icon-next-echarts': '#2b84e2',
  '--bg-next-echarts-active': '#02162b',
  '--border-next-echarts-active': '#2b84e2',
  '--color-icon-next-echarts-active': '#4e9ef2',
  '--bg-next-echarts-hover': '#02162b',
  '--border-next-echarts-hover': '#146ac7',
  '--color-icon-next-echarts-hover': '#2b84e2',

  //Breadcrumb面包屑
  '--bg-breadcrumb-head': '#2b84e2',
  '--color-breadcrumb-item': '#FFFFFF',
  '--color-breadcrumb-arrows': 'rgba(0, 0, 0, 0.35)',
  '--color-breadcrumb-item-active': 'rgba(0, 0, 0, 0.9)',

  // 数据展示1 深色版蓝绿色字体对应
  '--color-display-title': '#27EEE1',
  '--color-display-sub-title': '#2B84E2',
  '--color-display-text': '#27EEE1',
  '--color-display-title-before': '#27EEE1',
  '--color-display-sub-title-before': '#2B84E2',
  '--color-bluish-green-text': '#05fef5',
  '--bg-bluish-green-text': 'rgba(5, 254, 245, 0.2)',

  // 图表tooltip
  '--bg-echart-tooltip': 'rgba(17, 71, 129, 0.74)', // #114781  0.74
  '--color-filter-funnel': '#A9BED9', //过滤的漏斗小图标
  // 表格操作中的默认按钮
  '--color-table-btn-default': '#2B84E2', //蓝色按钮
  '--color-table-btn-more': '#2B84E2',

  '--bg-title-card': 'linear-gradient(180deg, rgba(13, 93, 197, 0.6) 0%, rgba(13, 49, 97, 0.6) 100%)',
  '--bg-card-1': '#0a2754',
  '--color-tab-title': 'rgba(255, 255, 255, 0.6)',
};

export const lightTheme = {
  // 主色
  '--color-primary': '#2C86F8',
  // 滚动条
  '--bg-scrollbar': 'transparent',
  '--scrollbar-thumb': 'rgba(0, 0, 0, 0.2)',
  '--scrollbar-track': 'none',
  '--shadow-scrollbar-thumb': 'none',

  '--bg-darkblue-block': '#fff',
  '--bg-header': '#2C86F8',
  '--bg-menu-item-active': 'rgba(255, 255, 255, 0.2)',
  '--bg-menu-item-active-horizontal': 'rgba(255, 255, 255, 0.2)',
  '--bg-menu-item-after-horizontal': '#FFFFFF',
  '--bg-menu-item-hover-horizontal': 'rgba(255, 255, 255, 0.1)',
  '--color-header-name': '#F5F5F5',
  '--bg-layout-header-tab': '#fff',
  '--shadow-layout-header-tab': '0px 3px 5px 0px rgba(147, 171, 206, 0.7)',
  '--color-tab-pane': 'rgba(0, 0, 0, 0.6)',
  '--bg-tab-pane-active': 'rgba(44, 134, 248, 0.1)',
  '--color-tab-pane-active': '#2C86F8',
  '--color-tab-icon': '#D3D7DE',
  '--bg-layout-content': '#F1F3F6',
  '--bg-content': '#fff',
  '--bg-sub-content': '#F9F9F9', // 某个内容块的背景色
  '--bg-sub-echarts-content': '#fff', // 某个带图表内容块的背景色
  '--border-sub-echarts-content': '#D3D7DE', // 某个带图表内容块的边框色
  '--bg-sub-echarts-title': 'rgba(44, 134, 248, 0.05)', // 某个带图表内容块的标题背景色
  '--shadow-sub-echarts-content': '0px 3px 5px 0px rgba(147, 171, 206, 0.5)', // 某个带图标内容块的阴影
  '--color-icon-echarts': '#2C86F8', //坐标card左上角icon颜色
  '--color-title-echarts': 'rgba(0, 0, 0, 0.9)', //坐标card左上角文字颜色

  '--color-logo': 'linear-gradient(180deg, #FFF 0%, #FFF 100%)',
  '--shadow-tab-contextmenu': '0 1px 6px rgba(0, 2, 141, 0.15)',

  '--color-page-item': 'rgba(0, 0, 0, 0.35)',
  '--bg-page-item-hover': '#2b84e2',
  '--color-page-item-hover': '#fff',

  '--color-no-data-img': 'rgba(0, 0, 0, 0.35)',
  '--bg-avatar': '#0A68DE',
  '--border-avatar': 'rgba(255, 255, 255, 0.5)',
  '--color-avatar': 'rgba(0, 0, 0, 0.9)',
  '--color-touxiang': '#fff',
  '--color-header-icon': '#D9E8FA',

  // iview
  '--bg-table': '#fff',
  '--bg-table-header-th': '#EBEDF1',
  '--color-table-header-th': 'rgba(0, 0, 0, 0.9)',
  '--bg-table-body-td': '#F9F9F9',
  '--bg-table-body-td-2n': '#F1F1F1',
  '--bg-table-row-hover': '#ebf7ff',
  '--shadow-table-header': 'none',
  '--bg-checkbox-inner': '#fff',
  '--border-checkbox-inner': '#D3D7DE',
  '--bg-checkbox-disabled': '#e9e9e9',
  '--border-checkbox-disabled': '#d3d7de',
  '--color-ivu-table-fixed-right': 'rgba(0, 21, 41, 0.15)',
  '--bg-table-tips': '#fff',
  '--border-table': '#D8D8D8',
  '--bg-tree-checkbox-inner': '#0A64D4',
  '--border-tree-checkbox-inner': '#0A64D4',
  '--bg-tree-current-node-switch': '#FFFFFF',

  // primary
  '--bg-btn-primary': '#2c86f8',
  '--bg-btn-primary-hover': '#4597ff',
  '--bg-btn-primary-active': '#1a74e7',
  '--color-btn-primary-disabled': 'rgba(0, 0, 0, 0.35)',
  '--bg-btn-primary-disabled': '#e9e9e9',
  '--border-btn-primary-disabled': '#d3d7de',
  // dashed
  '--bg-btn-dashed': 'rgba(44, 134, 248, 0.1)',
  '--color-btn-dashed': '#2c86f8',
  '--border-btn-dashed': '#2c86f8',
  '--color-btn-dashed-hover': '#4597ff',
  '--border-btn-dashed-hover': '#4597ff',
  '--border-btn-dashed-active': '#1a74e7',
  // default
  '--bg-btn-default': '#fff',
  '--color-btn-default': '#2c86f8',
  '--border-btn-default': '#2c86f8',
  '--color-btn-default-hover': '#4597ff',
  '--border-btn-default-hover': '#4597ff',
  '--border-btn-default-active': '#1a74e7',
  // text
  '--bg-btn-text': 'transparent',
  '--color-btn-text': '#2B84E2', //原rgba(0, 0, 0, 0.9)，但基本上所有用到的text的按钮都是采用设计规范中link的样式
  '--border-btn-text': 'none',
  '--color-btn-text-hover': '#4597FF', //原rgba(0, 0, 0, 0.9)，但基本上所有用到的text的按钮都是采用设计规范中link的样式
  '--border-btn-text-hover': 'none',
  '--border-btn-text-active': 'none',
  // swith
  '--bg-switch-after-default': '#D3D7DE',
  '--bg-switch-after-active': '#FFFFFF',
  '--bg-switch-default': '#FFFFFF',
  '--bg-switch-default-disabled': '#E9E9E9',
  '--bg-switch-active': '#113961',
  '--bg-switch-active-disabled': '#81B8FF',
  '--border-switch-default': '#D3D7DE',
  '--border-switch-default-disabled': '#D3D7DE',

  '--border-modal-content': '#fff',
  '--color-modal-close': '#fff',
  '--border-modal-footer': '#D3D7DE',

  '--color-label': 'rgba(0, 0, 0, 0.45)',
  '--border-input': '#D3D7DE',
  '--bg-input': '#fff',
  '--color-input': 'rgba(0, 0, 0, 0.9)',
  '--color-input-disabled': 'rgba(0, 0, 0, 0.5)',
  '--border-input-hover': '#2C86F8',
  '--color-input-placeholder': 'rgba(0, 0, 0, 0.35)',
  '--bg-select-dropdown': '#fff',
  '--color-select-item': 'rgba(0, 0, 0, 0.8)',
  '--bg-select-item-hover': '#D9ECFF',
  '--bg-select-item-active': '#D9ECFF',
  '--color-select-item-active': '#2C86F8',
  '--color-select-item-hover': '#2C86F8',
  '--bg-select-disabled': '#E9E9E9',
  '--color-select-arrow': '#888888',
  '--bg-tag': '#2C86F8',

  '--color-date-picker-header-label': 'rgba(0, 0, 0, 0.8)',
  '--color-date-picker-cells-cell-disabled': '#D3D7DE',

  '--bg-poptip': '#fff',
  '--bg-tooltip': '#fff',
  '--border-tooltip-arrow': '#2C86F8',
  '--color-tooltip-inner': '#3D3D3D',
  '--border-tooltip': '#fff',

  '--bg-msg': '#fff',

  '--bg-ivu-input-group-append': 'linear-gradient(180deg, #5BA3FF 0%, #2C86F8 100%)',
  '--bg-ivu-input-group-append-bover': 'linear-gradient(180deg, #65a7ee 0%, #4597ff 100%)',

  // element
  '--bg-el-tree-node': '#fff',
  '--color-el-tree-node__expand-icon': '#888888',
  '--bg-el-tree-node-is-expanded': '#F2F6FC',
  '--color-el-notification-shadow': 'rgba(44, 134, 248, 0.4)',
  '--bg-el-notification__title': '#F2F3F5',
  '--color-el-notification__title': '#000000',
  '--color-el-notification__closeBtn': '#888888',
  '--bg-el-checkbox__inner': 'transparent',
  '--border-checkbox__inner': '#dcdee2',

  '--color-dropdown-item': 'rgba(0, 0, 0, 0.8)',
  '--color-dropdown-item-hover': '#000000',

  '--bg-quadrate-outside': 'rgba(234, 74, 54, 0.3)',
  '--bg-quadrate-interior': '#EA4A36',
  '--bg-circle-outside': 'rgba(0, 0, 0, 0.1)',
  '--bg-circle-interior': 'rgba(0, 0, 0, 0.35)',
  '--bg-card': '#EDF0F6',
  '--color-card': '#000000',
  '--bg-wenhao': 'linear-gradient(227deg, #FFCC65 -1%, #E77811 97%)',
  '--bg-markers': '#EA4A36',
  '--border-color': '#D3D7DE',
  '--color-title': '#2C86F8',
  '--color-sub-title': '#2CC6D1',
  '--color-content': 'rgba(0, 0, 0, 0.8)',
  '--color-no-data-text': 'rgba(0, 0, 0, 0.35)',
  '--bg-title-rect': '#2C86F8',
  '--color-sub-title-inpage': 'rgba(0, 0, 0, 0.9)',
  '--color-card-title': 'rgba(0, 0, 0, 0.9)',

  '--bg-collapse-item': '#F9F9F9',
  '--color-collapse-arrow': '#888888',
  '--devider-line': '#D8D8D8',
  '--bg-info-card': '#F9F9F9',
  '--bg-info-card-option': 'rgba(0, 0, 0, 0.7)',
  '--color-info-card-option': '#2C86F8',
  '--color-info-card-label': '#979797',
  '--color-info-card-content': 'rgba(0, 0, 0, 0.8)',
  '--border-info-card': '#D3D7DE',
  '--bg-navigation': 'rgba(44, 134, 248, 0.05)',
  '--color-navigation-title': 'rgba(0, 0, 0, 0.9)',

  '--color-success': '#1FAF81',
  '--color-warning': '#F29F4C',
  '--color-failed': '#EA4A36',
  '--color-offline': 'rgba(0, 0, 0, 0.35)',
  '--color-active': '#2C86F8',
  '--color-tips': '#EA4A36',

  '--color-weizhipai': 'rgba(0, 0, 0, 0.35)',
  '--color-yiguanbi': '#A6A6A6',
  '--color-jinxingzhong': '#FCDB3A',
  '--color-daiqianshou': '#1291AC',

  '--color-progress-default': '#2C86F8',
  '--color-progress-warning': '#E24A2B',
  '--color-progress-success': '#1FAF81',

  // 以下分别对应设计规范-数据展示卡片1-7种渐变背景色
  '--bg-card-gradient-light-blue': 'linear-gradient(359deg, #44AAF7 -32%, #3FC5F3 98%)',
  '--bg-card-gradient-light-green': 'linear-gradient(180deg, #38DDE4 0%, #5FC7E1 100%)',
  '--bg-card-gradient-blue-purple': 'linear-gradient(180deg, #92C8FF 0%, #9361EA 100%)',
  '--bg-card-gradient-dark-blue': 'linear-gradient(0deg, #6481F7 0%, #7AB6FA 100%)',
  '--bg-card-gradient-light-orange': 'linear-gradient(180deg, #F4DC8B 0%, #CB9A60 98%)',
  '--bg-card-gradient-light-purple': 'linear-gradient(0deg, #8F44F4 -35%, #D0B2F9 100%)',
  '--bg-card-gradient-blue-green': 'linear-gradient(180deg, #63C8F4 0%, #3CB3E8 98%)',
  // 卡片的icon的背景色 #DAF5F8 0.3
  '--bg-card-icon': 'rgba(218, 245, 248, 0.3)',

  // 以下分别对应设计规范-数据展示卡片1-12种背景色
  '--bg-card-cyan': '#E5FAFF',
  '--bg-card-orange': '#FCF2DC',
  '--bg-card-purple': '#EDE9FC',
  '--bg-card-blue': '#E6EBFC',
  '--bg-card-deep-purple': '#F2E2F9',
  '--bg-card-green': '#C7F0E4',
  '--bg-card-red': '#F4D8EC',
  '--bg-card-deep-cyan': '#DCFCFA',
  '--bg-card-grass-green': '#EFF3CF',
  '--bg-card-light-pink': '#FAE1DE',
  '--bg-card-mint-green': '#E0FACE',
  '--bg-card-pink': '#FDE1E1',

  // 以下分别对应设计规范-数据展示卡片1-12种box-shadow 深色版没有
  '--bg-card-cyan-shadow': '0px 1px 2px 0px rgba(22, 213, 180, 0.4)',
  '--bg-card-orange-shadow': '0px 1px 2px 0px rgba(203, 175, 113, 0.4)',
  '--bg-card-purple-shadow': '0px 1px 2px 0px #E7D8F7',
  '--bg-card-blue-shadow': '0px 1px 2px 0px #B2D0F6',
  '--bg-card-deep-purple-shadow': '0px 1px 2px 0px #ECD3F7',
  '--bg-card-green-shadow': '0px 1px 2px 0px #AED9CC',
  '--bg-card-red-shadow': '0px 1px 2px 0px #DBBCD2',
  '--bg-card-deep-cyan-shadow': '0px 1px 2px 0px #ACE7E5',
  '--bg-card-grass-green-shadow': '0px 1px 2px 0px #DCE2AE',
  '--bg-card-light-pink-shadow': '0px 1px 2px 0px #F9C3BC',
  '--bg-card-mint-green-shadow': '0px 1px 2px 0px #A7D8D6',
  '--bg-card-pink-shadow': '0px 1px 2px 0px #F7AEAE',

  //  以下分别对应设计规范-数据展示卡片1-12种字体图标渐变色
  '--icon-card-gradient-cyan': 'linear-gradient(180deg, #8EDFF4 0%, #3DE8D7 98%)',
  '--icon-card-gradient-orange': 'linear-gradient(180deg, #F7D461 0%, #EBA24D 98%)',
  '--icon-card-gradient-purple': 'linear-gradient(0deg, #9E63D8 -29%, #CB99FF 99%)',
  '--icon-card-gradient-blue': 'linear-gradient(0deg, #6A87FC 4%, #9BC9FF 100%)',
  '--icon-card-gradient-deep-purple': 'linear-gradient(180deg, #DF9CFA 0%, #CD70F0 100%)',
  '--icon-card-gradient-green': 'linear-gradient(180deg, #92DC95 6%, #61B35B 98%)',
  '--icon-card-gradient-red': 'linear-gradient(180deg, #F3B6EB 0%, #F386CB 98%)',
  '--icon-card-gradient-deep-cyan': 'linear-gradient(180deg, #B2F0D8 0%, #46C1C1 100%)',
  '--icon-card-gradient-grass-green': 'linear-gradient(180deg, #EAE872 0%, #A5D665 98%)',
  '--icon-card-gradient-light-pink': 'linear-gradient(180deg, #F0B0A2 4%, #FC9161 94%)',
  '--icon-card-gradient-mint-green': 'linear-gradient(180deg, #58F3D9 0%, #3FE58F 96%)',
  '--icon-card-gradient-pink': 'linear-gradient(180deg, #F4A3AE 0%, #F66F8C 98%)',

  //  以下分别对应设计规范-数据展示卡片1-12种字体色
  '--font-card-cyan': '#14C2E1',
  '--font-card-orange': '#EBB225',
  '--font-card-purple': '#9B84F4',
  '--font-card-blue': '#559CF7',
  '--font-card-deep-purple': '#CA76EB',
  '--font-card-green': '#7BBF7C',
  '--font-card-red': '#E46AAD',
  '--font-card-deep-cyan': '#22C199',
  '--font-card-grass-green': '#BEC70F',
  '--font-card-light-pink': '#FA8B6A',
  '--font-card-mint-green': '#30C79B',
  '--font-card-pink': '#DE667C',

  // 对应设计规范的导航2的Tag标签
  '--bg-nav-tag': '#F9F9F9',
  '--color-nav-tag': 'rgba(0, 0, 0, 0.6)',
  '--border-nav-tag': ' #D3D7DE',
  '--bg-nav-tag-active': 'rgba(44, 134, 248, 0.1)',
  '--color-nav-tag-active': '#2C86F8',
  '--border-nav-tag-active': '#2C86F8',
  '--bg-nav': '#F4F4F4',

  // 对应设计规范的数据录入2中的选择设备
  '--bg-choose-device': 'rgba(44, 134, 248, 0.102)',
  '--border-choose-device': '#2C86F8',
  '--color-choose-device': '#2C86F8',
  '--bg-choose-device-active': 'rgba(26, 116, 231, 0.102)',
  '--border-choose-device-active': '#1A74E7',
  '--color-choose-device-active': '#1994DE',

  '--bg-form-item': '#F9F9F9', // 对应设计规范 有背景色表单项
  '--color-form-label': 'rgba(0, 0, 0, 0.45)', // 表单的label
  '--color-form-icon-validate': '#EA4A36', // 表单的必填项提醒的颜色

  // 对应设计规范 导航1 Tabs标签页
  '--color-switch-tab': 'rgba(0, 0, 0, 0.8)',
  '--color-switch-tab-active': '#2C86F8',
  '--bg-switch-tab': '#F9F9F9',
  '--color-switch-tag-tab': '#2C86F8',
  '--border-switch-tag-tab': '#1A74E7',
  '--bg-switch-tag-tab': '#FFFFFF ',
  // 对应设计规范 导航1 选项卡样式的标签页未激活颜色
  '--color-border-card-tab': 'rgba(0, 0, 0, 0.35)',
  // 对应设计规范 导航1 选项卡渐变标签页背景色
  '--bg-gradient-tab': 'linear-gradient(0deg, rgba(164, 198, 241, 0) -40%, rgba(44, 134, 248, 0.2) 100%)',

  // 对应设计规范左侧tab栏
  '--bg-vertical-tab': '#F9F9F9',
  '--bg-vertical-tab-hover': '#D9ECFF',
  '--bg-vertical-tab-active': '#2C86F8',
  '--border-vertical-tab': '#D8D8D8',
  '--color-vertical-tab': 'rgba(0, 0, 0, 0.8)',
  '--color-vertical-tab-active': '#FFFFFF',
  '--color-vertical-tab-hover': 'rgba(0, 0, 0, 0.9)',
  '--devider-vertical-tab': '#D8D8D8',
  '--devider-vertical-tab-active': 'rgba(255, 255, 255, 0.5)',
  '--devider-vertical-tab-hover': '#C1BDBD',
  '--color-vertical-tab-icon': '#888888',
  '--color-vertical-tab-btn': '#D8D8D8',
  '--color-vertical-tab-btn-disabled': '#E9E9E9',
  '--color-vertical-active-tab-btn': '#95C3FB', //已激活的卡片右侧按钮默认颜色
  '--color-vertical-active-tab-btn-hover': '#FFFFFF', //已激活的卡片右侧按钮hover的颜色
  '--color-vertical-active-tab-btn-disabled': '#AFB6BF', //已激活的卡片右侧按钮hover的颜色
  '--color-vertical-hover-tab-btn': '#D8D8D8', //hover的卡片右侧按钮可点击
  '--color-vertical-hover-tab-btn-hover': '#2C86F8', //hover的卡片右侧按钮悬停
  '--color-vertical-hover-tab-btn-disabled': '#F7F3F3', //hover的卡片右侧按钮不可点击

  //左右切换
  '--border-carousel-arrow': '#2C86F8',
  '--bg-carousel-arrow-active': '#FFFFFF',
  '--border-carousel-arrow-active': '#2C86F8',
  '--bg-carousel-arrow': '#FFFFFF',
  '--color-carousel-arrow': '#2C86F8',
  '--bg-carousel-arrow-hover': '#4597FF',
  '--border-carousel-arrow-hover': '#2C86F8',
  '--icon-carousel-arrow-hover': '#FFFFFF',

  //echarts分页
  '--bg-next-echarts': '#FFFFFF',
  '--border-next-echarts': '#2C86F8',
  '--color-icon-next-echarts': '#2B84E2',
  '--bg-next-echarts-active': '#FFFFFF',
  '--border-next-echarts-active': '#2C86F8',
  '--color-icon-next-echarts-active': '#2B84E2',
  '--bg-next-echarts-hover': '#4597FF',
  '--border-next-echarts-hover': '#2C86F8',
  '--color-icon-next-echarts-hover': '#FFFFFF',

  //Breadcrumb面包屑
  '--bg-breadcrumb-head': '#2C86F8',
  '--color-breadcrumb-item': 'rgba(0, 0, 0, 0.35)',
  '--color-breadcrumb-arrows': 'rgba(0, 0, 0, 0.35)',
  '--color-breadcrumb-item-active': 'rgba(0, 0, 0, 0.9)',

  // 数据展示1 深色版蓝绿色字体对应
  '--color-display-title': 'rgba(0, 0, 0, 0.9)',
  '--color-display-sub-title': 'rgba(0, 0, 0, 0.9)',
  '--color-display-text': '#2C86F8',
  '--color-display-title-before': '#2C86F8',
  '--color-display-sub-title-before': '#2C86F8',
  '--color-bluish-green-text': '#2C86F8',
  '--bg-bluish-green-text': 'rgba(44, 134, 248, 0.2)',

  // 图表tooltip
  '--bg-echart-tooltip': '#FFFFFF', // #114781  0.74
  '--color-filter-funnel': '#888888', //过滤的漏斗小图标
  // 表格操作中的默认按钮
  '--color-table-btn-default': '#2C86F8', //蓝色按钮
  '--color-table-btn-more': '#2C86F8',

  '--bg-title-card': '#F9F9F9',
  '--bg-card-1': '#F9F9F9',
  '--color-tab-title': 'rgba(0, 0, 0, 0.35)',
 };

export const deepBlueTheme = {
  // 主色
  '--color-primary': '#113961',
  // 滚动条
  '--bg-scrollbar': 'transparent',
  '--scrollbar-thumb': 'rgba(0, 0, 0, 0.2)',
  '--scrollbar-track': 'none',
  '--shadow-scrollbar-thumb': 'none',

  '--bg-darkblue-block': '#fff',
  '--bg-header': '#113961',
  '--bg-menu-item-active': 'rgba(255, 255, 255, 0.2)',
  '--bg-menu-item-active-horizontal': 'rgba(255, 255, 255, 0.2)',
  '--bg-menu-item-after-horizontal': '#FFFFFF',
  '--bg-menu-item-hover-horizontal': 'rgba(255, 255, 255, 0.1)',
  '--color-header-name': '#F5F5F5',
  '--bg-layout-header-tab': '#fff',
  '--shadow-layout-header-tab': '0px 3px 5px 0px rgba(147, 171, 206, 0.7)',
  '--color-tab-pane': 'rgba(0, 0, 0, 0.6)',
  '--bg-tab-pane-active': 'rgba(17, 57, 97, 0.1)',
  '--color-tab-pane-active': '#113961',
  '--color-tab-icon': '#D3D7DE',
  '--bg-layout-content': '#F1F3F6',
  '--bg-content': '#fff',
  '--bg-sub-content': '#F9F9F9', // 某个内容块的背景色
  '--bg-sub-echarts-content': '#fff', // 某个带图表内容块的背景色
  '--border-sub-echarts-content': '#D3D7DE', // 某个带图表内容块的边框色
  '--bg-sub-echarts-title': 'rgba(44, 134, 248, 0.05)', // 某个带图表内容块的标题背景色
  '--shadow-sub-echarts-content': '0px 3px 5px 0px rgba(147, 171, 206, 0.5)', // 某个带图标内容块的阴影
  '--color-icon-echarts': '#113961', //坐标card左上角icon颜色
  '--color-title-echarts': 'rgba(0, 0, 0, 0.9)', //坐标card左上角文字颜色

  '--color-logo': 'linear-gradient(180deg, #FFF 0%, #FFF 100%)',
  '--shadow-tab-contextmenu': '0 1px 6px rgba(0, 2, 141, 0.15)',

  '--color-page-item': 'rgba(0, 0, 0, 0.35)',
  '--bg-page-item-hover': '#113961',
  '--color-page-item-hover': '#fff',

  '--color-no-data-img': 'rgba(0, 0, 0, 0.35)',
  '--bg-avatar': '#043053',
  '--border-avatar': 'rgba(255, 255, 255, 0.5)',
  '--color-avatar': 'rgba(0, 0, 0, 0.9)',
  '--color-touxiang': '#fff',
  '--color-header-icon': '#D9E8FA',

  // iview
  '--bg-table': '#fff',
  '--bg-table-header-th': '#EBEDF1',
  '--color-table-header-th': 'rgba(0, 0, 0, 0.9)',
  '--bg-table-body-td': '#F9F9F9',
  '--bg-table-body-td-2n': '#F1F1F1',
  '--bg-table-row-hover': '#ebf7ff',
  '--shadow-table-header': 'none',
  '--bg-checkbox-inner': '#fff',
  '--border-checkbox-inner': '#D3D7DE',
  '--bg-checkbox-disabled': '#e9e9e9',
  '--border-checkbox-disabled': '#d3d7de',
  '--color-ivu-table-fixed-right': 'rgba(0, 21, 41, 0.15)',
  '--bg-table-tips': '#fff',
  '--border-table': '#D8D8D8',
  '--bg-tree-checkbox-inner': '#011C37',
  '--border-tree-checkbox-inner': '#011C37',
  '--bg-tree-current-node-switch': '#FFFFFF',

  // primary
  '--bg-btn-primary': '#113961',
  '--bg-btn-primary-hover': '#284C70',
  '--bg-btn-primary-active': '#082E55',
  '--color-btn-primary-disabled': 'rgba(0, 0, 0, 0.35)',
  '--bg-btn-primary-disabled': '#e9e9e9',
  '--border-btn-primary-disabled': '#d3d7de',
  // dashed
  '--bg-btn-dashed': 'rgba(44, 134, 248, 0.1)',
  '--color-btn-dashed': '#113961',
  '--border-btn-dashed': '#113961',
  '--color-btn-dashed-hover': '#284C70',
  '--border-btn-dashed-hover': '#284C70',
  '--border-btn-dashed-active': '#082E55',
  // default
  '--bg-btn-default': '#fff',
  '--color-btn-default': '#113961',
  '--border-btn-default': '#113961',
  '--color-btn-default-hover': '#284C70',
  '--border-btn-default-hover': '#284C70',
  '--border-btn-default-active': '#082E55',
  // text
  '--bg-btn-text': 'transparent',
  '--color-btn-text': '#113961', //原rgba(0, 0, 0, 0.9)，但基本上所有用到的text的按钮都是采用设计规范中link的样式
  '--border-btn-text': 'none',
  '--color-btn-text-hover': '#284C70', //原rgba(0, 0, 0, 0.9)，但基本上所有用到的text的按钮都是采用设计规范中link的样式
  '--border-btn-text-hover': 'none',
  '--border-btn-text-active': 'none',
  // swith
  '--bg-switch-after-default': '#D3D7DE',
  '--bg-switch-after-active': '#FFFFFF',
  '--bg-switch-default': '#FFFFFF',
  '--bg-switch-default-disabled': '#E9E9E9',
  '--bg-switch-active': '#113961',
  '--bg-switch-active-disabled': '#8F9CA8',
  '--border-switch-default': '#D3D7DE',
  '--border-switch-default-disabled': '#D3D7DE',

  '--border-modal-content': '#fff',
  '--color-modal-close': '#fff',
  '--border-modal-footer': '#D3D7DE',

  '--color-label': 'rgba(0, 0, 0, 0.45)',
  '--border-input': '#D3D7DE',
  '--bg-input': '#fff',
  '--color-input': 'rgba(0, 0, 0, 0.9)',
  '--color-input-disabled': 'rgba(0, 0, 0, 0.5)',
  '--border-input-hover': '#113961',
  '--color-input-placeholder': 'rgba(0, 0, 0, 0.35)',
  '--bg-select-dropdown': '#fff',
  '--color-select-item': 'rgba(0, 0, 0, 0.8)',
  '--bg-select-item-hover': '#D8E8F7',
  '--bg-select-item-active': '#113961',
  '--color-select-item-active': '#ffffff',
  '--color-select-item-hover': 'rgba(0, 0, 0, 0.8)',
  '--bg-select-disabled': '#E9E9E9',
  '--color-select-arrow': '#888888',
  '--bg-tag': '#113961',

  '--color-date-picker-header-label': 'rgba(0, 0, 0, 0.8)',
  '--color-date-picker-cells-cell-disabled': '#D3D7DE',

  '--bg-poptip': '#fff',
  '--bg-tooltip': '#fff',
  '--border-tooltip-arrow': '#113961',
  '--color-tooltip-inner': '#3D3D3D',
  '--border-tooltip': '#fff',

  '--bg-msg': '#fff',

  '--bg-ivu-input-group-append': 'linear-gradient(180deg, #3D6C9B 0%, #113961 100%)',
  '--bg-ivu-input-group-append-bover': 'linear-gradient(180deg, #306FB0 0%, #082E55 100%)',

  // element
  '--bg-el-tree-node': '#fff',
  '--color-el-tree-node__expand-icon': '#888888',
  '--bg-el-tree-node-is-expanded': '#F2F6FC',
  '--color-el-notification-shadow': 'rgba(44, 134, 248, 0.4)',
  '--bg-el-notification__title': '#F2F3F5',
  '--color-el-notification__title': '#000000',
  '--color-el-notification__closeBtn': '#888888',
  '--bg-el-checkbox__inner': 'transparent',
  '--border-checkbox__inner': '#dcdee2',

  '--color-dropdown-item': 'rgba(0, 0, 0, 0.8)',
  '--color-dropdown-item-hover': '#000000',

  '--bg-quadrate-outside': 'rgba(234, 74, 54, 0.3)',
  '--bg-quadrate-interior': '#EA4A36',
  '--bg-circle-outside': 'rgba(0, 0, 0, 0.1)',
  '--bg-circle-interior': 'rgba(0, 0, 0, 0.35)',
  '--bg-card': '#EDF0F6',
  '--color-card': '#000000',
  '--bg-wenhao': 'linear-gradient(227deg, #FFCC65 -1%, #E77811 97%)',
  '--bg-markers': '#EA4A36',
  '--border-color': '#D3D7DE',
  '--color-title': '#113961',
  '--color-sub-title': '#2CC6D1',
  '--color-content': 'rgba(0, 0, 0, 0.8)',
  '--color-no-data-text': 'rgba(0, 0, 0, 0.35)',
  '--bg-title-rect': '#113961',
  '--color-sub-title-inpage': 'rgba(0, 0, 0, 0.9)',
  '--color-card-title': 'rgba(0, 0, 0, 0.9)',

  '--bg-collapse-item': '#F9F9F9',
  '--color-collapse-arrow': '#888888',
  '--devider-line': '#D8D8D8',
  '--bg-info-card': '#F9F9F9',
  '--bg-info-card-option': 'rgba(0, 0, 0, 0.7)',
  '--color-info-card-option': '#2C86F8',
  '--color-info-card-label': '#979797',
  '--color-info-card-content': 'rgba(0, 0, 0, 0.8)',
  '--border-info-card': '#D3D7DE',
  '--bg-navigation': 'rgba(44, 134, 248, 0.05)',
  '--color-navigation-title': 'rgba(0, 0, 0, 0.9)',

  '--color-success': '#1FAF81',
  '--color-warning': '#F29F4C',
  '--color-failed': '#EA4A36',
  '--color-offline': 'rgba(0, 0, 0, 0.35)',
  '--color-active': '#113961',
  '--color-tips': '#EA4A36',

  '--color-weizhipai': 'rgba(0, 0, 0, 0.35)',
  '--color-yiguanbi': '#A6A6A6',
  '--color-jinxingzhong': '#FCDB3A',
  '--color-daiqianshou': '#1291AC',

  '--color-progress-default': '#2C86F8',
  '--color-progress-warning': '#E24A2B',
  '--color-progress-success': '#1FAF81',

  // 以下分别对应设计规范-数据展示卡片1-7种渐变背景色
  '--bg-card-gradient-light-blue': 'linear-gradient(359deg, #44AAF7 -32%, #3FC5F3 98%)',
  '--bg-card-gradient-light-green': 'linear-gradient(180deg, #38DDE4 0%, #5FC7E1 100%)',
  '--bg-card-gradient-blue-purple': 'linear-gradient(180deg, #92C8FF 0%, #9361EA 100%)',
  '--bg-card-gradient-dark-blue': 'linear-gradient(0deg, #6481F7 0%, #7AB6FA 100%)',
  '--bg-card-gradient-light-orange': 'linear-gradient(180deg, #F4DC8B 0%, #CB9A60 98%)',
  '--bg-card-gradient-light-purple': 'linear-gradient(0deg, #8F44F4 -35%, #D0B2F9 100%)',
  '--bg-card-gradient-blue-green': 'linear-gradient(180deg, #63C8F4 0%, #3CB3E8 98%)',
  // 卡片的icon的背景色 #DAF5F8 0.3
  '--bg-card-icon': '#DAF5F8',

  // 以下分别对应设计规范-数据展示卡片1-12种背景色
  '--bg-card-cyan': '#E5FAFF',
  '--bg-card-orange': '#FCF2DC',
  '--bg-card-purple': '#EDE9FC',
  '--bg-card-blue': '#E6EBFC',
  '--bg-card-deep-purple': '#F2E2F9',
  '--bg-card-green': '#C7F0E4',
  '--bg-card-red': '#F4D8EC',
  '--bg-card-deep-cyan': '#DCFCFA',
  '--bg-card-grass-green': '#EFF3CF',
  '--bg-card-light-pink': '#FAE1DE',
  '--bg-card-mint-green': '#E0FACE',
  '--bg-card-pink': '#FDE1E1',

  // 以下分别对应设计规范-数据展示卡片1-12种box-shadow 深色版没有
  '--bg-card-cyan-shadow': '0px 1px 2px 0px rgba(22, 213, 180, 0.4)',
  '--bg-card-orange-shadow': '0px 1px 2px 0px rgba(203, 175, 113, 0.4)',
  '--bg-card-purple-shadow': '0px 1px 2px 0px #E7D8F7',
  '--bg-card-blue-shadow': '0px 1px 2px 0px #B2D0F6',
  '--bg-card-deep-purple-shadow': '0px 1px 2px 0px #ECD3F7',
  '--bg-card-green-shadow': '0px 1px 2px 0px #AED9CC',
  '--bg-card-red-shadow': '0px 1px 2px 0px #DBBCD2',
  '--bg-card-deep-cyan-shadow': '0px 1px 2px 0px #ACE7E5',
  '--bg-card-grass-green-shadow': '0px 1px 2px 0px #DCE2AE',
  '--bg-card-light-pink-shadow': '0px 1px 2px 0px #F9C3BC',
  '--bg-card-mint-green-shadow': '0px 1px 2px 0px #A7D8D6',
  '--bg-card-pink-shadow': '0px 1px 2px 0px #F7AEAE',

  //  以下分别对应设计规范-数据展示卡片1-12种字体图标渐变色
  '--icon-card-gradient-cyan': 'linear-gradient(180deg, #8EDFF4 0%, #3DE8D7 98%)',
  '--icon-card-gradient-orange': 'linear-gradient(180deg, #F7D461 0%, #EBA24D 98%)',
  '--icon-card-gradient-purple': 'linear-gradient(0deg, #9E63D8 -29%, #CB99FF 99%)',
  '--icon-card-gradient-blue': 'linear-gradient(0deg, #6A87FC 4%, #9BC9FF 100%)',
  '--icon-card-gradient-deep-purple': 'linear-gradient(180deg, #DF9CFA 0%, #CD70F0 100%)',
  '--icon-card-gradient-green': 'linear-gradient(180deg, #92DC95 6%, #61B35B 98%)',
  '--icon-card-gradient-red': 'linear-gradient(180deg, #F3B6EB 0%, #F386CB 98%)',
  '--icon-card-gradient-deep-cyan': 'linear-gradient(180deg, #B2F0D8 0%, #46C1C1 100%)',
  '--icon-card-gradient-grass-green': 'linear-gradient(180deg, #EAE872 0%, #A5D665 98%)',
  '--icon-card-gradient-light-pink': 'linear-gradient(180deg, #F0B0A2 4%, #FC9161 94%)',
  '--icon-card-gradient-mint-green': 'linear-gradient(180deg, #58F3D9 0%, #3FE58F 96%)',
  '--icon-card-gradient-pink': 'linear-gradient(180deg, #F4A3AE 0%, #F66F8C 98%)',

  //  以下分别对应设计规范-数据展示卡片1-12种字体色
  '--font-card-cyan': '#14C2E1',
  '--font-card-orange': '#EBB225',
  '--font-card-purple': '#9B84F4',
  '--font-card-blue': '#559CF7',
  '--font-card-deep-purple': '#CA76EB',
  '--font-card-green': '#7BBF7C',
  '--font-card-red': '#E46AAD',
  '--font-card-deep-cyan': '#22C199',
  '--font-card-grass-green': '#BEC70F',
  '--font-card-light-pink': '#FA8B6A',
  '--font-card-mint-green': '#30C79B',
  '--font-card-pink': '#DE667C',

  // 对应设计规范的导航2的Tag标签
  '--bg-nav-tag': '#F9F9F9',
  '--color-nav-tag': 'rgba(0, 0, 0, 0.6)',
  '--border-nav-tag': ' #D3D7DE',
  '--bg-nav-tag-active': 'rgba(17, 57, 97, 0.1)',
  '--color-nav-tag-active': '#113961',
  '--border-nav-tag-active': '#113961',
  '--bg-nav': '#F4F4F4',

  // 对应设计规范的数据录入2中的选择设备
  '--bg-choose-device': 'rgba(44, 134, 248, 0.102)',
  '--border-choose-device': '#113961',
  '--color-choose-device': '#113961',
  '--bg-choose-device-active': 'rgba(26, 116, 231, 0.102)',
  '--border-choose-device-active': '#2E7BCA',
  '--color-choose-device-active': '#2E7BCA',

  '--bg-form-item': '#F9F9F9', // 对应设计规范 有背景色表单项
  '--color-form-label': 'rgba(0, 0, 0, 0.45)', // 表单的label
  '--color-form-icon-validate': '#EA4A36', // 表单的必填项提醒的颜色

  // 对应设计规范 导航1 Tabs标签页
  '--color-switch-tab': 'rgba(0, 0, 0, 0.8)',
  '--color-switch-tab-active': '#113961',
  '--bg-switch-tab': '#F9F9F9',
  '--color-switch-tag-tab': '#113961',
  '--border-switch-tag-tab': '#113961',
  '--bg-switch-tag-tab': '#FFFFFF',
  // 对应设计规范 导航1 选项卡样式的标签页未激活颜色
  '--color-border-card-tab': 'rgba(0, 0, 0, 0.35)',
  // 对应设计规范 导航1 选项卡渐变标签页背景色
  '--bg-gradient-tab':
    'linear-gradient(0deg, rgba(164, 198, 241, 0) -40%, rgba(17, 57, 97, 0.2) 100%)',

  // 对应设计规范左侧tab栏
  '--bg-vertical-tab': '#F9F9F9',
  '--bg-vertical-tab-hover': '#D8E8F7',
  '--bg-vertical-tab-active': '#113961',
  '--border-vertical-tab': '#D8D8D8',
  '--color-vertical-tab': 'rgba(0, 0, 0, 0.9)',
  '--color-vertical-tab-active': '#FFFFFF',
  '--color-vertical-tab-hover': 'rgba(0, 0, 0, 0.9)',
  '--devider-vertical-tab': '#D8D8D8',
  '--devider-vertical-tab-active': 'rgba(255, 255, 255, 0.5)',
  '--devider-vertical-tab-hover': '#D8D8D8',
  '--color-vertical-tab-icon': '#888888',
  '--color-vertical-tab-btn': '#D8D8D8',
  '--color-vertical-tab-btn-disabled': '#E9E9E9',
  '--color-vertical-active-tab-btn': '#297FD6', //已激活的卡片右侧按钮默认颜色
  '--color-vertical-active-tab-btn-hover': '#FFFFFF', //已激活的卡片右侧按钮hover的颜色
  '--color-vertical-active-tab-btn-disabled': '#999999', //已激活的卡片右侧按钮hover的颜色
  '--color-vertical-hover-tab-btn': '#D8D8D8', //hover的卡片右侧按钮可点击
  '--color-vertical-hover-tab-btn-hover': '#113961', //hover的卡片右侧按钮悬停
  '--color-vertical-hover-tab-btn-disabled': '#F7F3F3', //hover的卡片右侧按钮不可点击

  //左右切换
  '--border-carousel-arrow': '#113961',
  '--bg-carousel-arrow-active': '#FFFFFF',
  '--border-carousel-arrow-active': '#113961',
  '--bg-carousel-arrow': '#FFFFFF',
  '--color-carousel-arrow': '#113961',
  '--bg-carousel-arrow-hover': '#284C70',
  '--border-carousel-arrow-hover': '#113961',
  '--icon-carousel-arrow-hover': '#FFFFFF',

  //echarts分页
  '--bg-next-echarts': '#FFFFFF',
  '--border-next-echarts': '#113961',
  '--color-icon-next-echarts': '#113961',
  '--bg-next-echarts-active': '#FFFFFF',
  '--border-next-echarts-active': '#113961',
  '--color-icon-next-echarts-active': '#113961',
  '--bg-next-echarts-hover': '#284C70',
  '--border-next-echarts-hover': '#113961',
  '--color-icon-next-echarts-hover': '#FFFFFF',

  //Breadcrumb面包屑
  '--bg-breadcrumb-head': '#113961',
  '--color-breadcrumb-item': 'rgba(0, 0, 0, 0.35)',
  '--color-breadcrumb-arrows': 'rgba(0, 0, 0, 0.35)',
  '--color-breadcrumb-item-active': 'rgba(0, 0, 0, 0.9)',

  // 数据展示1 深色版蓝绿色字体对应
  '--color-display-title': 'rgba(0, 0, 0, 0.9)',
  '--color-display-sub-title': 'rgba(0, 0, 0, 0.9)',
  '--color-display-text': '#113961',
  '--color-display-title-before': '#113961',
  '--color-display-sub-title-before': '#113961',
  '--color-bluish-green-text': '#2C86F8',
  '--bg-bluish-green-text': 'rgba(44, 134, 248, 0.2)',

  // 图表tooltip
  '--bg-echart-tooltip': '#FFFFFF', // #114781  0.74
  '--color-filter-funnel': '#888888', //过滤的漏斗小图标
  // 表格操作中的默认按钮
  '--color-table-btn-default': '#2C86F8', //蓝色按钮
  '--color-table-btn-more': '#2C86F8',

  '--bg-title-card': '#F9F9F9',
  '--bg-card-1': '#F9F9F9',
  '--color-tab-title': 'rgba(0, 0, 0, 0.35)',
};
