<template>
    <div class="point-content-wrapper">
        <div class="tablist">
            <div v-for="(item, index) in tabList" :key="index" :class="{ active: activeIndex == index }" class="ivu-tabs-tab" @click="tabClick(index)">
                <span>{{ item.name }}</span>
            </div>
        </div>
        <div class="point-content">
            <div v-for="(item, i) in pointList" :key="i">
                <div class="point-item" :class="{ active: currentIndex == i }" @click="chooseMapItem(i)">
                    <div class="header">
                        <div class="header-left">
                            <span class="serialNumber" :class="{ activeNumber: currentIndex == i }">
                                <span>{{ i + 1 }}</span>
                            </span>
                            <span class="pointName">{{ item.placeName }}</span>
                        </div>
                        <div class="header-right">
                            <span class="tag">{{ item.placeTypeName }}</span>
                            <ui-icon type="shoucang" :size="14" :color="'#F29F4C'"></ui-icon>
                        </div>
                    </div>
                    <div class="content">
                        <div class="content-right">
                            <span>
                                <ui-icon type="location" :size="16"></ui-icon>
                                <span>{{ item.placeStandardAddress }}</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <ui-loading v-if="loading" />
        <ui-empty v-if="!loading && !pointList.length" />
        <div class="general-search-footer">
            <ui-page :simple="true" :show-elevator="false" countTotal :show-sizer="false" :total="total" :current="pageInfo.pageNumber" :page-size="pageInfo.pageSize" size="small" show-total> </ui-page>
        </div>
    </div>
</template>

<script>
import {  queryPlaceInfoPageList } from '@/api/operationsOnTheMap';
import operateBar from '@/components/mapdom/operate-bar.vue'
export default {
    components: {
        operateBar
    },
    props: {
        //搜索条件
        searchPrams: {
            type: Object,
            default: () => { }
        },
    },
    data() {
        return {
            pointList: [ ],
            currentIndex: -1,
            total: 0,
            pageInfo: {
                pageNumber: 1,
                pageSize: 20
            },
            loading: false,
            activeIndex:0,
            tabList: [
                { name: '酒店', placeTypeCode: 'B1001' },
                { name: '网吧', placeTypeCode: 'B0903' },
                { name: '政府机关', placeTypeCode: 'B0101' },
                { name: '学校', placeTypeCode: 'B0701' },
                { name: '重点场所', placeTypeCode: 'B9999' }
            ],
        }
    },
    created() {
        this.init()
    },
    methods: {
        init() {
            this.querySearch();
        },
        tabClick(index) {
            this.pointList = []
            this.activeIndex = index
            this.pageInfo.pageNumber = 1;
            this.querySearch()
            this.$emit('chooseMapItem', -1, 'device')
        },
        querySearch() {
            this.loading = true;
            let params = {
                ...this.pageInfo,
                'placeTypeCode': this.tabList[this.activeIndex].placeTypeCode,
                'placeName': this.searchPrams.keyWords,
            }
            queryPlaceInfoPageList(params)
            .then(res => {
                if (res.code === 200) {
                    const { data: { entities = [], total = 0 } } = res
                    this.pointList = entities || [];
                    this.total = total;
                    entities.map(item => {
                        item.geoPoint = item.location;
                    })
                    this.$emit('mapResultHandler', entities)
                }
            })
            .catch(() => {
                this.pointList = []
                this.$emit('mapResultHandler', [])
            })
            .finally(() => {
                this.loading = false
            })
        },
        chooseMapItem(index) {
            this.currentIndex = index
            this.$emit('chooseMapItem', index)
        }
    }
}
</script>

<style lang="less" scope>
.point-content-wrapper{
    .tablist {
        height: 34px;
        line-height: 34px;
        margin: 10px 10px 0;
        width: calc(~'100% - 20px');
        position: relative;
        z-index: 6;
        .ivu-tabs-tab {
            float: left;
            border: 1px solid #2c86f8;
            border-right: none;
            width: 20%;
            text-align: center;
            font-size: 14px;
            color: #2c86f8;
            cursor: pointer;
            &:hover {
                background: #2c86f8;
                color: #ffffff;
            }
            &:first-child {
                border-top-left-radius: 4px;
                border-bottom-left-radius: 4px;
                border-right: none;
            }
            &:last-child {
                border-top-right-radius: 4px;
                border-bottom-right-radius: 4px;
                border-right: 1px solid #2c86f8;
            }
        }
        .active {
            background: #2c86f8;
            color: #fff;
        }
    }
}
.point-content {
  padding: 10px;
  width: 360px;
  height: calc(~'100% - 56px');
  overflow: hidden;
  overflow-y: auto;
  .point-item {
    height: 70px;
    box-shadow: inset 0px -1px 0px 0px #d3d7de;
    cursor: pointer;
    .header {
      display: flex;
      justify-content: space-between;
      height: 42px;
      line-height: 42px;
      padding: 0 10px;
      &-left {
        display: flex;
        align-items: center;
        width: 245px;
        .serialNumber {
          position: relative;
          display: inline-block;
          width: 32px;
          height: 32px;
          margin-right: 14px;
          color: black;
          background: url('~@/assets/img/map/trajectory-red.png') no-repeat;
          > span {
            position: absolute;
            top: -10px;
            width: 32px;
            color: #ea4a36;
            text-align: center;
          }
        }
        .pointName {
          font-size: 14px;
          font-weight: bold;
          color: rgba(0, 0, 0, 0.9);
          margin-left: -10px;
        }
        .activeNumber {
          background: url('~@/assets/img/map/trajectory-blue.png') no-repeat !important;
          > span {
            color: #2c86f8 !important;
          }
        }
      }
      &-right {
        display: flex;
        align-items: center;
        justify-content: center;

        > span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 36px;
          height: 20px;
          background: #f9f9f9;
          border-radius: 4px;
          border: 1px solid #d3d7de;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.35);
          margin-right: 10px;
        }
      }
    }
    .content {
      display: flex;
      padding: 0 10px;
      &-left {
        > img {
          width: 60px;
          height: 60px;
          border: 1px solid #d3d7de;
        }
      }
      &-right {
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        .iconfont {
          margin-right: 10px;
        }
      }
    }
  }
  .active {
    background: rgba(44, 134, 248, 0.1);
  }
}
</style>
