<template>
  <div class="historic-echarts">
    <PieEchart :title="{}" v-if="series[0].data.length !== 0" :color="color" :legend="legend" :graphic="graphic" :series="series" class="radar-echart" />
    <ui-loading v-if="loading" />
    <ui-empty v-if="series[0].data.length === 0"></ui-empty>
    <div class="btns">
      <Button class="card-btn" :class="typeBtn === 1 ? ' card-active-btn' : ''" @click="overviewBtn(1)">近一月</Button>
      <Button class="card-btn ml-10" :class="typeBtn === 2 ? ' card-active-btn' : ''" @click="overviewBtn(2)">近三月</Button>
    </div>
    <p class="legend-title auxiliary-color">单位:天</p>
  </div>
</template>
<script>
import PieEchart from '@/components/echarts/pie-echart'
export default {
  components: { PieEchart },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      typeBtn: 1,
      color: ['#48BAFF', '#1FAF8A', '#F29F4C'],
      legend: {
        show: true,
        orient: 'vertical',
        top: 'center',
        right: '6%',
        itemGap: 20,
        itemWidth: 8,
        itemHeight: 8,
        icon: 'rect',
        formatter: function (params) {
          let arr = [
            { value: 20, name: '有录像' },
            { value: 30, name: '无录像' },
            { value: 50, name: '未检测' }
          ]
          let num = null
          arr.forEach(i => {
            if (i.name === params) {
              num = i.value
            }
          })
          return [`{name|${params}}{value|${num}}`]
        },
        textStyle: {
          padding: [0, 0, 0, 5],
          rich: {
            name: {
              color: 'rgba(0, 0, 0, 0.35)',
              width: 110
            },
            value: {
              fontFamily: 'MicrosoftYaHei-Bold',
              fontWeight: 'bold'
            }
          }
        },
        data: [
          {
            name: '有录像',
            textStyle: {
              color: '#48BAFF'
            }
          },
          {
            name: '无录像',
            textStyle: {
              color: '#1FAF8A'
            }
          },
          {
            name: '未检测',
            textStyle: {
              color: '#F29F4C'
            }
          }
        ]
      },
      graphic: {
        elements: [
          {
            type: 'circle'
          }
        ]
      },
      series: [
        {
          type: 'pie',
          radius: ['55%', '82%'],
          center: ['28%', '50%'],
          label: {
            position: 'center',
            show: true,
            width: 80,
            height: 60,
            borderRadius: 30,
            backgroundColor: '#fff',
            formatter: function (params) {
              return [`{name|${params.percent}%}`]
            },
            rich: {
              name: {
                color: 'rgba(0, 0, 0, 0.6)',
                fontSize: 18,
                fontFamily: 'MicrosoftYaHei-Bold, MicrosoftYaHei',
                fontWeight: 'bold',
                padding: [22, 0, 0, 0]
              }
            }
          },
          emphasis: {
            label: {
              show: true,
              width: 80,
              height: 60,
              borderRadius: 30,
              backgroundColor: '#fff',
              formatter: function (params) {
                return [`{name|${params.percent} %}`]
              }
            }
          },
          labelLine: {
            show: false
          },
          data: []
        }
      ]
    }
  },
  watch: {
    list: {
      handler(val) {
        if (val.length !== 0) {
          this.legend.formatter = params => {
            let arr = []
            arr = val
            let num = null
            arr.map(i => {
              if (i.name === params) {
                num = i.value
              }
            })
            return [`{name|${params}}{value|${num}}`]
          }
          this.series[0].data = val
        }
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    overviewBtn(val) {
      this.typeBtn = val
      this.$emit('changeBtntime', this.typeBtn)
    }
  }
}
</script>
<style lang="less" scoped>
.historic-echarts {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  flex: 1;
  .charts {
    width: 100%;
    height: 200px;
  }
  .btns {
    position: absolute;
    top: -35px;
    right: 0;
  }
  .legend-title {
    position: absolute;
    right: 34px;
    top: 20px;
    font-size: 12px;
  }
}
</style>
