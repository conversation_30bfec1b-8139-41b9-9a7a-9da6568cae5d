<template>
  <div>
    <div class="header-left">
      <img v-if="themeType === 'dark'" src="@/assets/img/equipmentfiles/title-left.png" alt="" />
      <img v-else src="@/assets/img/equipmentfiles/title-left-light.png" alt="" />
    </div>
    <span> {{ title }} </span>
    <div class="header-right">
      <img v-if="themeType === 'dark'" src="@/assets/img/equipmentfiles/title-right.png" alt="" />
      <img v-else src="@/assets/img/equipmentfiles/title-right-light.png" alt="" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  computed: {
    ...mapGetters({
      themeType: 'common/getThemeType',
    }),
  },
};
</script>

<style lang="less" scoped>
div {
  > span {
    font-size: 16px;
    font-weight: bold;
    color: var(--color-sub-title-inpage);
    margin-right: 24px;
    margin-left: 24px;
  }
  .header-left,
  .header-right {
    display: inline-block;
    height: 14px;
    width: 50px;
    > img {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
