<template>
  <ui-table
    v-if="tableData.length"
    :table-columns="tableColumns"
    :table-data="tableData"
    :span-method="handleSpan"
    :stripe="false"
    :disabledHover="true"
  >
    <template v-for="(item, index) in slotsList" #[item.slot]="{ row }">
      <div :key="index">
        <create-tabs
          :componentName="resultData.componentName"
          :tabs-text="resultData.text"
          @selectModule="selectModule"
          class="inline"
          v-if="filterSlot(row[item.slot])"
          :tabs-query="{
            displayType: statisticType,
            indexId: filterFiledName(row[item.slot], 'indexId'),
            code: filterFiledName(row[item.slot], 'regionCode'),
            batchId: filterFiledName(row[item.slot], 'batchId'),
            uuid: uuid,
          }"
        >
          <span
            class="can-click"
            :class="
              item.className === 'root-region-color' ? 'root-region-color' : filterStandColor(row[item.slot], item)
            "
            :title="filterTip(row[item.slot])"
          >
            {{ filterKey(row[item.slot]) }}
          </span>
        </create-tabs>
        <span
          v-else
          class="forbin"
          :class="item.className === 'root-region-color' ? 'root-region-color' : filterStandColor(row[item.slot], item)"
          :title="filterTip(row[item.slot])"
        >
          {{ filterKey(row[item.slot]) }}
        </span>
      </div>
    </template>
  </ui-table>
</template>

<script>
const renderTableHeader = (h, params) => {
  params.column.rowSpan = 2;
  return h('div', {}, '数据类别');
};
export default {
  name: 'monthly-new-detection',
  props: {
    monthNewResult: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      tableLoading: false,
      tabList: [],
      slotsList: [],
      statisticType: 'REGION',
      resultData: {
        componentName: 'overviewEvaluation', // 需要跳转的组件名
        text: '评测详情', // 跳转页面标题
        title: '评测详情',
        type: 'view',
      },
      uuid: '',
      taskType: 1,
      tableColumns: [],
      tableData: [],
      countObj: {},
      countChildObj: {},
    };
  },
  methods: {
    // 月最新检测情况
    filterSlot(val) {
      return !!val && !!val.clickable;
    },
    filterTip(val) {
      if (!val) return '无法考核';
      if (val) {
        return val.clickTip;
      }
    },
    filterKey(val) {
      let title = val;
      if (!!val && val.hasOwnProperty('value')) {
        title = val.value ? val.value : '--';
        return title;
      }
      if (val instanceof Object || [undefined, null].includes(val)) {
        return '--';
      }
      return title;
    },
    filterStandColor(val, item) {
      if (item.key === 'standardValue') {
        return 'standard-value-color';
      }
      let isStandard = !val ? false : val.isStandard;
      if (isStandard) {
        return 'region-green';
      } else {
        return 'region-red';
      }
    },
    filterFiledName(val, name) {
      if (!val) return '';
      if (val) return val[name];
    },
    handleSpan({ row, rowIndex, columnIndex }) {
      //针对第一列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 1) {
        return [Object.values(this.countObj)[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 1) {
        if (row.moduleType === this.tableData[rowIndex - 1]['moduleType']) {
          return [0, 0];
        } else if (row.moduleType !== this.tableData[rowIndex - 1]['moduleType']) {
          return [this.countObj[row.moduleType], 1];
        }
      }
      //针对第二列
      //第一行数据直接向下合并
      if (rowIndex === 0 && columnIndex === 2) {
        return [Object.values(this.countChildObj)[0], 1];
      }
      if (rowIndex === 0 && columnIndex === 1) {
        return [Object.values(this.countChildObj)[0], 1];
      }
      /*
        之后的数据发现标识符相同的则置空, 标识符与上一列不符则开始向下合并
      */
      if (rowIndex !== 0 && columnIndex === 2) {
        if (row.categoryType === this.tableData[rowIndex - 1]['categoryType']) {
          return [0, 0];
        } else if (row.categoryType !== this.tableData[rowIndex - 1]['categoryType']) {
          return [this.countChildObj[row.categoryType], 1];
        }
      }
    },
    selectModule() {
      this.$emit('selectModuleClick');
    },
  },
  watch: {
    monthNewResult: {
      handler(val) {
        if (!val) return false;
        this.tableData = [];
        this.countObj = {};
        this.countChildObj = {};
        this.tableLoading = true;
        const monthNewDetectData = JSON.parse(val);
        this.slotsList = monthNewDetectData.headers[3].children;
        monthNewDetectData.headers[1] = {
          align: 'center',
          renderHeader: renderTableHeader,
          children: [
            {
              title: '',
              className: 'none',
              key: 'moduleName',
              align: 'center',
              width: 150,
            },
            {
              title: '',
              className: 'none',
              key: 'category',
              align: 'center',
              width: 150,
            },
          ],
        };
        this.tableColumns = monthNewDetectData.headers;
        this.tableData = monthNewDetectData.details;
        this.tableData.forEach((row) => {
          // 根据标识符统计相同类型数据
          if (row.moduleType) {
            if (this.countObj.hasOwnProperty(row.moduleType)) {
              this.countObj[row.moduleType]++;
            } else {
              this.countObj[row.moduleType] = 0;
              this.countObj[row.moduleType]++;
            }
          }

          if (row.categoryType) {
            if (this.countChildObj.hasOwnProperty(row.categoryType)) {
              this.countChildObj[row.categoryType]++;
            } else {
              this.countChildObj[row.categoryType] = 0;
              this.countChildObj[row.categoryType]++;
            }
          }
        });
        this.uuid = monthNewDetectData.uuid;
        this.tableLoading = false;
      },
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
  },
};
</script>

<style lang="less" scoped>
[data-theme='dark'] {
  .standard-value-color,
  .region-wh {
    color: #cc8a27 !important;
  }
  .root-region-color {
    color: #19d5f6 !important;
  }
  .region-red,
  .region-sdf {
    color: #f35327 !important;
  }
  .region-green {
    color: #3fce6f !important;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .standard-value-color,
  .region-wh {
    color: var(--color-warning) !important;
  }
  .root-region-color {
    color: #48baff !important;
  }
  .region-red,
  .region-sdf {
    color: var(--color-failed) !important;
  }
  .region-green {
    color: var(--color-success) !important;
  }
}

.can-click {
  cursor: pointer;
  text-decoration: underline !important;
}
</style>
