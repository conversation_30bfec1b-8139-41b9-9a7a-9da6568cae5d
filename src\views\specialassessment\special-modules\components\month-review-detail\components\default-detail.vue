<template>
  <div class="review-details auto-fill">
    <div class="review-details-header">
      <span class="review-details-title">
        <span class="review-details-rect"></span>
        <span class="ml-sm">{{ queryParams.title }}评测-{{ detailData.name }}{{ month }}月检测日明细</span>
      </span>
      <span class="goback-btn" @click="goBack">
        <i class="icon-font icon-fanhui f-14 mr-xs"></i>
        <span>返回</span>
      </span>
    </div>
    <div class="btn-bar mt-md mb-md">
      <slot name="export"></slot>
    </div>
    <div class="review-details-table auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @onSortChange="onSortChange"
      >
        <template #detection="{ row }">
          <span class="pointer underline-text" @click="viewDayDetail(row, '')">
            {{ row.detection }}
          </span>
        </template>
        <template #qualified="{ row }">
          <span class="pointer underline-text" @click="viewDayDetail(row, '1')">
            {{ row.qualified }}
          </span>
        </template>
        <template #unqualified="{ row }">
          <span class="pointer underline-text unqualified-color" @click="viewDayDetail(row, '2')">
            {{ row.unqualified }}
          </span>
        </template>
        <template #rate="{ row }">
          <span :class="row.qual === '1' ? '' : 'unqualified-color'">{{ row.rateFormat }}</span>
        </template>
      </ui-table>
    </div>
    <day-review-details
      v-model="dayReviewDetailVisible"
      v-bind="$attrs"
      :data-dimension-enum="detailData.dataDimensionEnum"
      :index-data="indexData"
      :row-data="rowData"
    ></day-review-details>
  </div>
</template>
<script>
import { tableColumns } from '../utils/tableColumns';
import { rateFormatFields } from '@/views/specialassessment/utils/menuConfig.js';
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'default-detail',
  props: {
    indexData: {
      type: Object,
      default: () => {},
    },
    detailData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      queryParams: {},
      dayReviewDetailVisible: false,
      rowData: {},
      month: '',
      loading: false,
      tableColumns: [],
      tableData: [],
      sortData: {
        sortField: '', // 排序字段
        sort: '', // 排序方式: ASC("升序") | DESC("降序")
      },
    };
  },
  created() {
    const examineTime = new Date(this.queryParams.examineTime);
    this.month = examineTime.getMonth() + 1;
    this.tableColumns = tableColumns[this.queryParams.indexType];
    this.tableColumns[this.tableColumns.length - 1].title = rateFormatFields[this.queryParams.indexType];
  },
  methods: {
    async getTableList() {
      try {
        this.loading = true;
        let { examineTime, dateType, indexType, statisticsCode } = this.$route.query;
        let { dataDimensionEnum, detail, code } = this.detailData;
        let params = {
          nodeDetails: this.indexData.details,
          examineTime: examineTime,
          type: dateType,
          dataDimensionEnum: dataDimensionEnum,
          nodeEnum: indexType,
          paramForm: {
            indexId: detail[0].indexId,
            orgRegionCode: statisticsCode === '3' ? null : code,
            tagIds: statisticsCode === '3' && code !== '-1' ? [Number(code)] : null,
          },
          statisticalModel: statisticsCode ? Number(statisticsCode) : null,
          displayType:
            statisticsCode === '1' ? 'ORG' : statisticsCode === '2' ? 'REGION' : statisticsCode === '3' ? 'TAG' : '',
        };
        if (this.sortData.sortField) {
          params.paramForm.sortField = this.sortData.sortField;
          params.paramForm.sort = this.sortData.sort;
        }
        const {
          data: { data },
        } = await this.$http.post(evaluationoverview.getSpecialDetailByMonth, params);
        if (!data) return false;
        this.tableData = data.entities.map((item) => {
          if (item.detail) {
            return {
              ...item,
              ...item.detail[0],
            };
          }
          return item;
        });
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    onSortChange({ key, order }) {
      if (order === 'normal') {
        this.sortData = {};
      } else {
        this.sortData = {
          sortField: key,
          sort: order.toUpperCase(),
        };
      }
      this.getTableList();
      this.$emit('sortChange', this.sortData);
    },
    goBack() {
      this.$emit('changeComponentName', ['StatisticsList']);
    },
    viewDayDetail(row, qualVal) {
      this.dayReviewDetailVisible = true;
      this.rowData = {
        qualVal,
        ...row,
        ...row.detail[0],
      };
    },
  },
  watch: {
    detailData: {
      handler() {
        this.queryParams = this.$route.query;
        this.getTableList();
      },
      immediate: true,
      deep: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DayReviewDetails: require('@/views/specialassessment/special-modules/components/day-review-details/index.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.review-details {
  &-header {
    width: 100%;
    height: 50px;
    padding: 0 20px;
    background: var(--bg-navigation);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-rect {
    content: '';
    display: inline-block;
    width: 5px;
    height: 20px;
    background: var(--bg-title-rect);
  }
  &-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: var(--color-content);
  }
  &-table {
    padding: 0 20px;
  }
  .goback-btn {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: normal;
    color: var(--color-btn-default);
    cursor: pointer;
  }
  .btn-bar {
    width: 100%;
    padding: 0 20px;
    display: flex;
    justify-content: flex-end;
  }
  .underline-text {
    text-decoration: underline;
  }
  .unqualified-color {
    color: var(--color-failed);
  }
  .qualified-color {
    color: var(--color-success);
  }
  .icon-daochu {
    color: var(--color-btn-default);
  }
}
</style>
