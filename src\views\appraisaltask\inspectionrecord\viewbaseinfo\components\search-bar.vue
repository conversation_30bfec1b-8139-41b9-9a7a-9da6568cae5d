<template>
  <div class="search">
    <div class="inline">
      <ui-label label="组织机构" :width="70" class="inline mr-lg mb-sm">
        <tree-select
          :tree-data="treeData"
          nodeKey="orgCode"
          class="width-md"
          v-model="formValidate.orgCode"
          @current-change="currentChange"
          placeholder="请选择组织机构"
        >
        </tree-select>
      </ui-label>
      <ui-label label="关键词" :width="50" class="inline mr-lg">
        <Input v-model="formValidate.keyWord" class="width-lg" clearable placeholder="请输入设备名称或设备编码"></Input>
      </ui-label>
      <ui-label v-if="currentTree.id === 105" :label="global.filedEnum.sbgnlx" :width="120" class="inline mr-lg">
        <Select
          clearable
          class="width-md"
          v-model="formValidate.sbgnlx"
          :placeholder="`请选择${global.filedEnum.sbgnlx}`"
          :max-tag-count="1"
        >
          <Option
            v-for="(sbgnlxItem, bdindex) in dictData['sxjgnlx_receive']"
            :key="'sbgnlx' + bdindex"
            :value="sbgnlxItem.dataKey"
            >{{ sbgnlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label v-if="currentTree.id === 108" :label="global.filedEnum.sbdwlx" :width="100" class="inline mr-lg">
        <Select
          clearable
          class="width-md"
          v-model="formValidate.sbdwlx"
          :max-tag-count="1"
          :placeholder="`请选择${global.filedEnum.sbdwlx}`"
        >
          <Option
            v-for="(sbdwlxItem, bdindex) in dictData['propertySearch_sbdwlx']"
            :key="'sbdwlx' + bdindex"
            :value="sbdwlxItem.dataKey"
            >{{ sbdwlxItem.dataValue }}
          </Option>
        </Select>
      </ui-label>
      <ui-label label="设备类型" :width="70" class="inline mr-lg">
        <Select class="width-md" v-model="formValidate.isImportantDevice" clearable placeholder="请选择设备类型">
          <Option :value="1" label="普通标签"></Option>
          <Option :value="2" label="重点标签"></Option>
        </Select>
      </ui-label>
      <ui-label label="检测结果" :width="70" class="inline">
        <Select class="width-md" v-model="formValidate.checkStatus" clearable placeholder="请选择检测结果">
          <Option :value="1" label="合格"></Option>
          <Option :value="2" label="不合格"></Option>
        </Select>
      </ui-label>
    </div>

    <ui-label :width="1" class="inline mb-sm search-button" label="">
      <Button type="primary" @click="search">查询</Button>
      <Button class="ml-sm" @click="resetForm">重置</Button>
    </ui-label>
  </div>
</template>

<script>
import user from '@/config/api/user';
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  name: 'search-bar',
  components: {
    TreeSelect: require('../../faceviewdata/component/tree-select').default,
  },
  props: {
    taskObj: {
      type: Object,
      default: () => {},
    },
    currentTree: {
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      formValidate: {
        checkStatus: '',
        isImportantDevice: '',
        keyWord: '',
        orgCode: '',
        sbdwlx: '',
        sbgnlx: '',
      },
      dictData: {},
      treeData: [],
    };
  },
  computed: {},
  watch: {
    taskObj: {
      deep: true,
      immediate: true,
      handler: function (val) {
        this.getOrgTreeList(val);
        this.$nextTick(() => {
          this.search();
        });
      },
    },
  },
  created() {
    this.getDictData();
  },
  methods: {
    getOrgTreeList(val) {
      if (!!val && val.regionCode) {
        this.getRegioncode(val.regionCode);
      }
    },
    async getRegioncode(code) {
      try {
        const params = { regioncode: code };
        const res = await this.$http.get(governanceevaluation.getOrgDataByRegioncode, { params });
        const orgCodeList = res.data.data;
        let { orgCode } = this.$util.common
          .jsonToArray(JSON.parse(JSON.stringify(orgCodeList)))
          .find((item) => !item.disabled);
        this.formValidate.orgCode = orgCode || '';
        this.treeData = this.$util.common.arrayToJson(JSON.parse(JSON.stringify(orgCodeList)), 'id', 'parentId');
      } catch (e) {
        console.log(e);
      }
    },
    currentChange({ orgCode }) {
      this.formValidate.orgCode = orgCode;
      this.$emit('params-change', this.formValidate);
    },
    async getDictData() {
      try {
        const params = ['propertySearch_sbdwlx', 'sxjgnlx_receive', 'check_status'];
        let res = await this.$http.post(user.queryDataByKeyTypes, params);
        const datas = res.data.data;
        params.forEach((key) => {
          let obj = datas.find((row) => {
            return !!row[key];
          });
          this.$set(this.dictData, key, obj[key]);
        });
      } catch (error) {
        console.log(error);
      }
    },
    search() {
      this.$emit('search', this.formValidate);
    },
    resetForm() {
      this.formValidate = {
        checkStatus: '',
        isImportantDevice: '',
        keyWord: '',
        orgCode: '',
        sbdwlx: '',
        sbgnlx: '',
      };
      this.$emit('search', this.formValidate);
    },
  },
};
</script>

<style lang="less" scoped>
.search {
  display: flex;
  justify-content: space-between;
  overflow: hidden;
  margin: 10px 0 10px 0;
  .search-button {
    white-space: nowrap;
  }
}
</style>
