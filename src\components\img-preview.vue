<template>
  <div class="drag-box" :class="[!overlay ? '' : 'overlay']">
    <div class="top">
      <div class="large-box-cut"  v-if="imgUrls && imgUrls.length > 0">
        <div
          class="fun-img-cut"
          @click="handleClick"
          v-drag
          @wheel="handleScroll"
          :style="{ transform: `scale(${rate})` }"
        >
          <img
            ref="imgBox"
            class="largeImg"
            v-lazy="imgUrls[currentIndex].url"
            alt=""
            @load="offsetBox"
          />
          <div
            class="select-preview click-preview"
            v-for="(item, index) in boxStyleList"
            :key="index"
            :style="{
              left: item.x + 'px',
              top: item.y + 'px',
              width: item.width + 'px',
              height: item.height + 'px',
              borderColor: item.color,
              backgroundColor: getOpacityColor(item.color, 0.1),
              zIndex: item.zindex,
            }"
            @dblclick="handleBoxSelect($event, index, item.type)"
          ></div>
        </div>
      </div>
      <div class="operation-box">
        <i class="iconfont icon-zoomin color-wihte" @click="handleBlowUp"></i>
        <i class="iconfont icon-zoomout color-wihte" @click="handleReduce"></i>
        <i
          class="iconfont icon-download color-wihte"
          @click="handleDownload"
        ></i>
        <i
          class="iconfont icon-expend color-wihte"
          @click="handleFullScreen"
        ></i>
      </div>
    </div>
    <div class="bottom" v-if="imgUrls && imgUrls.length > 1">
      <swiper ref="mySwiper" :options="swiperOption" class="my-swiper">
        <template v-for="(item, index) in imgUrls">
          <swiper-slide :key="index" @click.native="changeIndex(index)">
            <div class="swiper-item" :class="{ active: index == currentIndex }">
              <img :src="item.url" alt="" />
            </div>
          </swiper-slide>
        </template>
      </swiper>
      <div
        class="swiper-button-prev"
        :class="{ 'swiper-button-disabled': currentIndex == 0 }"
        @click="swiperChange('prev')"
      >
        <i class="iconfont icon-doubleleft"></i>
      </div>
      <div
        class="swiper-button-next"
        :class="{
          'swiper-button-disabled': currentIndex == imgUrls.length - 1,
        }"
        @click="swiperChange('next')"
      >
        <i class="iconfont icon-doubleright"></i>
      </div>
    </div>

    <!-- 放大 -->
    <div class="screen-box" v-if="overlay">
      <div class="fun-img-cut-screen" @click="handleClick" v-drag>
        <img
          id="imgBoxScreen"
          class="largeImg-screen"
          v-lazy="imgUrls[currentIndex].url"
          alt=""
        />
      </div>
      <div class="closeIcon" @click="handleScreen($event)">
        <i class="iconfont icon-compress color-wihte"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllPicturePick, cutImageBase64 } from "@/api/player";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import { base64ToFile, getOpacityColor } from "@/util/modules/common.js";
export default {
  name: "",
  components: { swiper, swiperSlide },
  props: {
    imgUrls: {
      type: Array,
      default: () => [],
    },
    // 是否显示结构化
    structured: {
      type: Boolean,
      default: () => false,
    },
  },
  data() {
    return {
      rate: 1,
      overlay: false,
      styleBox: 0,
      currentIndex: 0,
      swiperOption: {
        slidesPerView: 5,
        allowTouchMove: false,
      },
      boxStyleList: [],
      selectListBox: [],
    };
  },
  watch: {
    imgUrls: {
      handler(val) {
        if (!val || val.length == 0) return;
        this.currentIndex = 0;
        if (this.$refs.mySwiper)
          this.$refs.mySwiper.swiper.slideTo(this.currentIndex);
        this.rate = 1;
        this.$nextTick(() => {
          this.handleStructuring();
          // this.offsetBox();
        });
      },
      immediate: true,
    },
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    getOpacityColor,
    changeIndex(index) {
      this.currentIndex = index;
      this.handleStructuring();
    },
    swiperChange(type) {
      if (type == "prev" && this.currentIndex > 0) {
        this.currentIndex--;
      } else if (
        type == "next" &&
        this.currentIndex < this.imgUrls.length - 1
      ) {
        this.currentIndex++;
      }
      this.$refs.mySwiper.swiper.slideTo(this.currentIndex);
    },
    handleScroll(event) {
      if (event.deltaY > 0 && this.rate > 0.2) {
        this.rate -= 0.1;
      } else if (event.deltaY < 0) {
        this.rate += 0.1;
      }
    },
    // 放大
    handleBlowUp() {
      this.rate += 0.1;
    },
    // 缩小
    handleReduce() {
      if (this.rate > 0.2) this.rate -= 0.1;
    },
    handleClick(e) {
      // 阻止向父组件冒泡
      e.stopPropagation();
      e.preventDefault();
    },
    // 下载
    handleDownload() {
      fetch(this.imgUrls[this.currentIndex].url).then((res) =>
        res.blob().then((blob) => {
          const a = document.createElement("a"),
            url = window.URL.createObjectURL(blob),
            filename = this.imgUrls[this.currentIndex].name;
          a.href = url;
          a.download = filename;
          a.click();
          window.URL.revokeObjectURL(url);
        })
      );
    },
    // 全屏
    handleFullScreen() {
      this.overlay = true;
    },
    initialize() {
      this.overlay = false;
    },
    // 取消全屏
    handleScreen(e) {
      e.stopPropagation();
      if (this.overlay) {
        this.overlay = false;
      }
      this.rate = 1;
      this.offsetBox();
    },
    // 偏移量
    offsetBox() {
      // 计算偏移量, 居中
      let box = document.querySelector(".fun-img-cut");
      box.style.width = "100%";
      box.style.height = "100%";
      const imgBox = this.$refs.imgBox;
      const w = parseInt(window.getComputedStyle(imgBox).width);
      const h = parseInt(window.getComputedStyle(imgBox).height);

      box.style.width = w + "px";
      box.style.height = h + "px";
      // 居中，涉及到框选位置计算和移动，不能直接用flex布局
      let largeBoxDom = document.querySelector(".large-box-cut");
      console.log(
        "宽和高是在这里展示 ",
        largeBoxDom.clientWidth,
        largeBoxDom.clientHeight
      );
      box.style.marginLeft = (largeBoxDom.clientWidth - w) / 2 + "px";
      box.style.marginTop = (largeBoxDom.clientHeight - h) / 2 + "px";
    },
    // 图片结构化
    handleStructuring() {
      if (!this.structured) return;
      this.boxStyleList = [];
      this.selectListBox = [];
      let file = base64ToFile(this.imgUrls[this.currentIndex].url);
      let fileData = new FormData();
      fileData.append("file", file);
      getAllPicturePick(fileData).then((res) => {
        let data = res.data;
        const imgBox = this.$refs.imgBox;
        const nw = imgBox.naturalWidth;
        const nh = imgBox.naturalHeight;
        const w = parseInt(window.getComputedStyle(imgBox).width);
        const h = parseInt(window.getComputedStyle(imgBox).height);
        const rateW = w / nw;
        const rateH = h / nh;
        data.forEach((v) => {
          if (v.positionVos && v.positionVos.length) {
            v.positionVos.forEach((item) => {
              let boxStyle = {};
              if (nh < 610) {
                boxStyle = {
                  x: item.left,
                  y: item.top,
                  width: item.right - item.left,
                  height: item.bottom - item.top,
                  type: v.type,
                  color: this.getInfoByType(v.type).color,
                  zindex: this.getInfoByType(v.type).zindex,
                };
              } else {
                boxStyle = {
                  x: item.left * rateW,
                  y: item.top * rateH,
                  width: (item.right - item.left) * rateW,
                  height: (item.bottom - item.top) * rateH,
                  type: v.type,
                  color: this.getInfoByType(v.type).color,
                  zindex: this.getInfoByType(v.type).zindex,
                };
              }
              this.boxStyleList.push(boxStyle);
              this.selectListBox.push(item);
            });
          }
        });
      });
    },
    // 结构化类型
    getInfoByType(type) {
      switch (type) {
        case "face":
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
        case "vehicle":
          return { color: "#4b8bff", path: "vehicleContent", zindex: 100 };
        case "human":
          return { color: "#bf3e50", path: "humanBodyContent", zindex: 300 };
        case "nonMotor":
          return {
            color: "#67c23a",
            path: "nonmotorVehicleContent",
            zindex: 200,
          };
        default:
          return { color: "#ffea4b", path: "faceContent", zindex: 400 };
      }
    },
    // 框选跳转
    async handleBoxSelect(e, index, type) {
      this.routeParam = {};
      let imgData = this.selectListBox[index];
      let base64Data = await cutImageBase64(imgData);
      let routeParam = {
        fileUrl: "data:image/jpeg;base64," + base64Data.data.imageBase,
        feature: base64Data.data.feature,
        imageBase: base64Data.data.imageBase,
      };
      let page = this.getInfoByType(type).path;
      const { href } = this.$router.resolve({
        path: `/wisdom-cloud-search/search-center?sectionName=${page}&noMenu=1`,
        query: {
          // sectionName:page, urlList: [routeParam]
          sectionName: page,
          urlList: JSON.stringify(routeParam),
          noMenu: 1,
        },
      });
      window.open(href, "_blank");
    },
  },
};
</script>

<style lang="less" scoped>
.drag-box {
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top {
    width: 100%;
    flex: 1;
    overflow: hidden;
    position: relative;
  }
  .bottom {
    height: 40px;
    margin-top: 10px;
    padding: 0 18px 0 25px;
    position: relative;
    .swiper-item {
      img {
        height: 40px;
        cursor: pointer;
        -webkit-user-select: none; /* Safari */
        -moz-user-select: none; /* Firefox */
        -ms-user-select: none; /* IE10+/Edge */
        user-select: none; /* Standard */
      }
      &.active {
        img {
          border: 2px solid #2c86f8;
        }
      }
    }
  }
}
.large-box-cut {
  overflow: hidden;
  width: 100%;
  height: calc(~"100% - 30px");
  position: relative;
  .fun-img-cut {
    cursor: grab;
    width: 100%;
    height: 100%;
  }
  .largeImg {
    width: auto;
    max-width: 100%;
    max-height: 100%;
  }
}
.operation-box {
  position: absolute;
  height: 30px;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .color-wihte {
    font-size: 16px;
    color: #fff;
    margin: 0 10px;
    cursor: pointer;
  }
}
.overlay {
  .screen-box {
    background-color: #000;
    opacity: 1;
    filter: alpha(opacity=100);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
    overflow: hidden;
  }
  .closeIcons {
    display: none;
  }
  .closeIcon {
    width: 30px;
    height: 30px;
    background: #fff;
    position: absolute;
    top: 0;
    right: 0;
    border-bottom-left-radius: 80%;
    text-align: center;
    cursor: pointer;
    .color-wihte {
      font-size: 20px;
      color: rgba(0, 0, 0, 0.8);
    }
  }
  .fun-img-cut-screen {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: inherit;
    height: inherit;
  }
  .fun-img-cut {
    position: absolute;
    display: flex;
    justify-content: center;
  }
  .largeImg-screen {
    width: auto;
    max-width: 100%;
    max-height: 650px;
  }
  .operation-box {
    display: none;
  }
}
.swiper-button-prev,
.swiper-button-next {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: transparent;
  text-align: center;
  line-height: 30px;
  margin-top: -15px;
  .iconfont {
    color: #888888;
    font-size: 18px;
  }
  &:hover {
    i {
      color: #2c86f8;
    }
  }
  &:active {
    i {
      color: #2c86f8;
    }
  }
}
.swiper-button-prev {
  left: -5px;
}
.swiper-button-next {
  right: -5px;
}
.select-preview {
  position: absolute;
  top: 0;
  left: 0;
  // width: 90px;
  // height: 90px;
  background: rgba(255, 234, 75, 0.1);
  // background: #FFEA4B;
  border-radius: 4px;
  border: 2px solid rgba(255, 234, 75, 1);
  display: block;
}
.click-preview {
  display: block;
  cursor: pointer;
}
</style>
