<template>
  <viewer :images="list"
    @inited="inited"
    class="viewer" ref="viewer"
    >
      <template #default="scope">
        <img class="img" v-for="(src, index) in scope.images" :src="src.photoUrl" :key="index" @click="show()" alt />
      </template>
    </viewer>
</template>

<script>
import 'viewerjs/dist/viewer.css'
import { component as Viewer } from "v-viewer"
export default {
  name: 'UiImage',
  components: {
    Viewer
  },
  props: {
    list: {
      type: Array,
      default: [],
    },  
    url: {
      type: String, // 图片路径
      default: ''
    },
    
  },
  data() {
    return {}
  },
  
  mounted() {
   
  },
  methods: {
    inited (viewer) {
      this.$viewer = viewer
    },
    show () {
      this.$viewer.show()
    }
  }
}
</script>

<style lang="less">
.img {
  width: 100%;
  height: 100%;
}
</style>
