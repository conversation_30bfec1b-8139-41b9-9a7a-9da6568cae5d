<template>
  <ui-modal v-model="visible" :styles="styles" title="修改信息">
    <Form :label-width="100" ref="userForm" :rules="ruleValidate" :model="userForm">
      <Row>
        <Col span="12">
          <FormItem label="头像" prop="avatar">
            <upload-img
              :multipleNum="1"
              class="upload-img"
              :defaultList="defaultList"
              @successPut="uploadLogoSuccess"
              ref="upload"
            >
            </upload-img>
          </FormItem>
          <FormItem label="姓名" prop="name">
            <Input class="dialog-input" type="text" v-model="userForm.name" placeholder="请输入姓名"></Input>
          </FormItem>
          <FormItem label="账号" prop="username">
            <Input
              class="dialog-input"
              :disabled="true"
              type="text"
              v-model="userForm.username"
              placeholder="请输入账号"
            ></Input>
          </FormItem>
          <FormItem class="form-item gender-radio" label="性别" prop="sex">
            <div class="custom-password">
              <RadioGroup v-model="userForm.sex">
                <Radio label="0">男</Radio>
                <Radio label="1">女</Radio>
              </RadioGroup>
            </div>
          </FormItem>
        </Col>
        <Col span="12">
          <FormItem label="手机号" prop="phoneNumber">
            <Input
              class="dialog-input"
              v-model="userForm.phoneNumber"
              type="tel"
              maxlength="11"
              placeholder="请输入手机号"
            ></Input>
          </FormItem>
          <FormItem label="短号" prop="shortNumber">
            <Input class="dialog-input" v-model="userForm.shortNumber" type="text" placeholder="请输入短号"></Input>
          </FormItem>
          <FormItem label="邮箱" prop="email">
            <Input class="dialog-input" v-model="userForm.email" type="email" placeholder="请输入邮箱"></Input>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <template slot="footer">
      <Button @click="visible = false" class="plr-30">取 消</Button>
      <Button type="primary" class="default-btn plr-30" @click="commit">确 定</Button>
    </template>
  </ui-modal>
</template>
<script>
import md5 from 'md5';
import { checkPwd, checkCardId, checkPhone, checkEmail, checkShortNumber } from '@/util/module/common';
import user from '@/config/api/user';
import { mapActions } from 'vuex';

export default {
  name: 'userForm',
  data() {
    const validatePass = (rule, value, callback) => {
      if (this.userForm.passwordType == 1 && !checkPwd(value) && !this.userForm.isEdit) {
        callback(new Error('请输入大写或小写字母开头包含数字的8-20位密码'));
      } else {
        callback();
      }
    };
    const validateCardId = (rule, value, callback) => {
      if (checkCardId(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的身份证号'));
      }
    };
    const validatePhoneNumber = (rule, value, callback) => {
      if (value === '' || checkPhone(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的手机号'));
      }
    };
    const validateEmail = (rule, value, callback) => {
      if (value === '' || checkEmail(value)) {
        callback();
      } else {
        callback(new Error('请输入正确的邮箱'));
      }
    };
    const validateShortNumber = (rule, value, callback) => {
      if (value === '' || checkShortNumber(value)) {
        callback();
      } else {
        callback(new Error('请输入3-6位数字短号'));
      }
    };
    return {
      styles: {
        width: '5rem',
      },
      defaultPassword: '',
      userForm: {
        avatar: '',
        name: '',
        username: '',
        sex: '0',
        passwordType: undefined,
        password: '',
        workCode: '',
        cardId: '',
        phoneNumber: '',
        shortNumber: '',
        email: '',
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'blur' }],
        passwordType: [{ required: true, message: '请选择密码', trigger: 'blur' }],
        password: [{ validator: validatePass, trigger: 'blur' }],
        workCode: [{ required: true, message: '请输入警号', trigger: 'blur' }],
        cardId: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { validator: validateCardId, trigger: 'blur' },
        ],
        phoneNumber: [{ validator: validatePhoneNumber, trigger: 'blur' }],
        shortNumber: [{ validator: validateShortNumber, trigger: 'blur' }],
        email: [{ validator: validateEmail, trigger: 'blur' }],
      },
      roleList: [],
      organizationList: [],
      dataPermissionsTree: [],
      showPassword: false,
      visible: false,
    };
  },
  mounted() {},
  methods: {
    ...mapActions({
      getUserInfo: 'user/getUserInfo',
    }),
    successPut() {},
    async commit() {
      let valid = await this.$refs['userForm'].validate(() => {});
      if (!valid) return;
      let form = JSON.parse(JSON.stringify(this.userForm));
      if (form.password) form.password = md5(form.password);
      let res = await this.$http.put(user.updatePersonalInfo, form);
      this.visible = false;
      this.$Message.success(res.data.msg);
      this.getUserInfo();
    },
    resetFormFields() {
      this.$nextTick(() => {
        this.$refs['userForm'].resetFields();
      });
    },
    uploadLogoSuccess(val) {
      if (val && val.length) {
        this.userForm.avatar = val[0];
      } else {
        this.userForm.avatar = '';
      }
    },
    async getPersonalInfo() {
      let {
        data: { data },
      } = await this.$http.get(`${user.getPersonalInfo}/${this.userInfo.id}`);
      let { ...info } = data;
      this.userForm = {
        ...info,
      };
    },
  },
  components: {
    UploadImg: require('@/components/upload-img').default,
  },
  computed: {
    defaultList() {
      return this.userForm.avatar ? [this.userForm.avatar] : [];
    },
  },
  props: {
    value: {},
    userInfo: {},
  },
  watch: {
    value(val) {
      this.visible = val;
      if (val) {
        this.getPersonalInfo();
        this.resetFormFields();
      }
    },
    visible(val) {
      this.$emit('input', val);
    },
  },
};
</script>
<style lang="less" scoped>
@import './index.less';
</style>
