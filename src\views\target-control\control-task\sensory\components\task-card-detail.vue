<template>
  <div class="task">
    <div class="info">
      <div class="right">
        <img
          src="@/assets/img/default-img/vehicle_archives_default.png"
          alt=""
          srcset=""
        />
      </div>
      <div class="left" v-if="type == 1">
        <div class="p" v-if="compareType == 3">
          <div class="title">Mac地址</div>
          <span>:</span>
          <div class="val">{{ taskInfo.mac || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 4">
          <div class="title">IMSI编码</div>
          <span>:</span>
          <div class="val">{{ taskInfo.imsi || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 4">
          <div class="title nowrap" title="国际移动台设备识别码">
            国际移动台设备识别码
          </div>
          <span>:</span>
          <div class="val">{{ taskInfo.imei || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 5">
          <div class="title">Rfid地址</div>
          <span>:</span>
          <div class="val">{{ taskInfo.rfidCode || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 6">
          <div class="title">Etc编号</div>
          <span>:</span>
          <div class="val">{{ taskInfo.obuId || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 3 || compareType == 4">
          <div class="title">姓名</div>
          <span>:</span>
          <div class="val">{{ taskInfo.name || "--" }}</div>
        </div>
        <div class="p" v-if="compareType == 3 || compareType == 4">
          <div class="title">身份证号</div>
          <span>:</span>
          <div class="val">{{ taskInfo.idCardNo || "--" }}</div>
        </div>
      </div>
      <div class="left" v-else>
        <div class="p">
          <span class="lib">{{ taskInfo.libName }}</span>
        </div>
        <div class="p">
          <div class="title">设备数量</div>
          <span>:</span>
          <div class="val">{{ taskInfo.libCount }}</div>
        </div>
        <div class="p">
          <div class="title">更新时间</div>
          <span>:</span>
          <div class="val">{{ taskInfo.modifyTime }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { swiper, swiperSlide } from "vue-awesome-swiper";
import UiImage from "@/components/ui-image.vue";
export default {
  components: { swiper, swiperSlide, UiImage },
  props: {
    taskInfo: {
      type: Object,
      default: () => {},
    },
    // 1: 单体布控， 2：库布控
    type: {
      type: Number,
      default: 1,
    },
    index: {
      type: Number,
      default: 3,
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  data() {
    return {
      swiperOption: {
        direction: "horizontal",
        pagination: {
          el: ".swiper-pagination",
          clickable: true, //点击切换
        },
      },
    };
  },
  watch: {
    taskInfo: {
      handler(val) {
        let arr = [];
        if (val.photoUrlList && val.photoUrlList.length > 0) {
          arr = val.photoUrlList;
        }
        this.swiperOption.pagination = {
          el:
            arr.length > 1
              ? `#swipe${this.index + 1}` + ".swiper-pagination"
              : null, //控制分页显示隐藏
          clickable: true, //点击切换
        };
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      genderList: "dictionary/getGenderList", //性别
      nationList: "dictionary/getNationList", //民族
      licensePlateColorList: "dictionary/getLicensePlateColorList", //车牌颜色
      bodyColorList: "dictionary/getBodyColorList", //车辆颜色
      vehicleBrandList: "dictionary/getVehicleBrandList", //车辆品牌
      vehicleClassTypeList: "dictionary/getVehicleTypeList", //车辆类型
      vehicleUseStatus: "dictionary/getVehicleUseStatus", //车辆使用状态
      vehicleUseNature: "dictionary/getVehicleUseNature", //车辆使用性质
    }),
  },
  activated() {},
  mounted() {},
  methods: {
    collection() {},
  },
};
</script>
<style lang="less" scoped>
.task {
  position: relative;
  // width: 340px;
  // height: 190px;
  box-shadow: 0 1px 3px #d9d9d9;
  overflow: hidden;
  border-radius: 3px;
  border: 1px solid #ededed;
  .taskname {
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .name {
      color: #2c86f8;
      font-weight: 600;
      font-size: 16px;
      margin-top: 12px;
    }
    .num {
      background: linear-gradient(-90deg, #ea4a36 0%, #ff7d56 100%);
      width: 60px;
      height: 22px;
      line-height: 22px;
      text-align: center;
      font-size: 12px;
      color: #fff;
      margin-right: -10px;
      border-bottom-left-radius: 50px;
      border-top-left-radius: 50px;
      margin-top: 16px;
    }
  }
  .top {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding: 0 6px;
    .level {
      position: absolute;
      left: 50%;
      margin-left: -46px;
    }
  }

  .contrast {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding: 0 10px;
    .block {
      position: relative;
      width: 100px;
      .num {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        align-items: center;
        display: flex;
        justify-content: center;
        color: #2c86f8;
      }
    }
    .border {
      border: 1px solid #ebebeb;
    }
  }

  .info {
    display: flex;
    padding: 12px;
    .left {
      flex: 1;
      width: 0;
      .p {
        display: flex;
        line-height: 26px;
        .title {
          color: #999;
          width: 50px;
          // text-align: right;
          margin-left: 10px;
          text-align: justify;
          text-align-last: justify;
          text-justify: inter-ideograph;
          &.nowrap {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .val {
          flex: 1;
          overflow: hidden;
          margin-left: 10px;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .lib {
          font-size: 16px;
          color: #2c86f8;
          margin-left: 10px;
          font-weight: 600;
          margin-bottom: 12px;
        }
      }
    }
    .right {
      position: relative;
      width: 115px;
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      .img {
        width: 115px;
        height: 88px;
        margin-bottom: 6px;
      }
    }
  }

  .btn {
    position: absolute;
    width: 100%;
    background: #2c86f8;
    color: #fff;
    display: flex;
    text-align: center;
    justify-content: center;
    height: 30px;
    line-height: 30px;
    bottom: -30px;
    transition: 0.3s;
    cursor: pointer;
    div {
      position: relative;
      flex: 1;
      span {
        display: inline-block;
        width: 2px;
        height: 20px;
        border-right: 1px solid #d1cbcb;
        position: absolute;
        right: 1px;
        top: 5px;
      }
    }
  }

  &:hover {
    border: 1px solid #2c86f8;
    .btn {
      bottom: 0;
      transition: 0.3s;
    }
  }
}

.content-img {
  width: 116px;
  height: 116px;
  border: 1px solid #fff;
  position: relative;
  .swiper-container {
    width: 100%;
    height: 100%;
    .swiper-wrapper .swiper-slide {
      width: 116px;
      height: 116px;
      text-align: center;
      & > img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        cursor: pointer;
      }
    }
  }
  .swiper-pagination {
    bottom: 0px;
    width: 100%;
  }
  /deep/.swiper-pagination-bullet {
    width: 6px;
    height: 6px;
    margin-left: 5px;
    background: #fff;
    opacity: 1;
  }
  /deep/.swiper-pagination-bullet-active {
    background: #2c86f8;
  }
}

.license-plate-small {
  position: absolute;
  z-index: 9;
  width: 100%;
  text-align: center;
  padding: 6px;
}
</style>
