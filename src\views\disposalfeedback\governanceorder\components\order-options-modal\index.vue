<template>
  <div class="order-options-box">
    <ui-modal
      class="order-option"
      v-model="visible"
      :title="modalTitle"
      :styles="{ width: isBatch ? '7rem' : '3.5rem' }"
      @onCancel="cancel"
    >
      <div class="order-option-content auto-fill" :style="{ height: isBatch ? '3.2rem' : '' }">
        <component
          :is="componentName"
          :ref="componentName"
          :isBatch="isBatch"
          @updateDisabled="updateDisabled"
        ></component>
        <div v-if="componentName === 'Assign'">
          <div class="mb-md mt-md">
            <span class="base-text-color">短信通知：</span>
            <RadioGroup v-model="messageNotice" @on-change="changeNoticeType">
              <Radio label="1" class="mr-lg">是</Radio>
              <Radio label="0">否</Radio>
            </RadioGroup>
          </div>
          <div class="mb-md" v-if="messageNotice === '1'">
            <span class="base-text-color">联系电话：</span>
            <Input placeholder="请输入联系电话" class="width-md" v-model="tellphoneNumber" />
          </div>
        </div>
        <div class="select-table auto-fill" v-if="isBatch">
          <div class="select-title">待{{ titles[componentName] }}工单：</div>
          <p v-if="componentName === 'Assign' && batchStatisticsObj.total !== batchStatisticsObj.useCount" class="tips">
            共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）指派
          </p>
          <p v-if="componentName === 'Deal' && batchStatisticsObj.total !== batchStatisticsObj.useCount" class="tips">
            共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）处理
          </p>
          <p v-if="componentName === 'Close' && batchStatisticsObj.total !== batchStatisticsObj.useCount" class="tips">
            共{{ batchStatisticsObj.total - batchStatisticsObj.useCount }}条工单无需（或无权）关闭
          </p>
          <ui-table class="auto-fill" :tableColumns="modelColumns" :tableData="sucessData" :loading="batchTableLoading">
          </ui-table>
          <ui-page
            class="page menu-content-background"
            :page-data="batchTablePageData"
            transfer
            @changePage="changebatchTablePage"
            @changePageSize="changebatchTablePageSize"
          >
          </ui-page>
        </div>
      </div>
      <template #footer>
        <div v-if="!isBatch">
          <Button class="ml-sm plr-30" @click="cancel">取 消</Button>
          <Button type="primary" :loading="orderLoading" :disabled="isDisabled" @click="submit" class="plr-30"
            >确 认
          </Button>
        </div>
        <Button
          v-else
          :loading="orderLoading"
          type="primary"
          :disabled="!sucessData.length || isDisabled"
          @click="submit"
          class="plr-30"
          >确 认
        </Button>
      </template>
    </ui-modal>
  </div>
</template>
<script>
import { modelColumns } from '../../util/enum.js';
import governancetask from '@/config/api/governancetask';

export default {
  name: '',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    //是否批量操作
    isBatch: {
      type: Boolean,
      default: true,
    },
    //调用统计接口后返回的对象，内
    batchStatisticsObj: {
      type: Object,
      default: () => {
        return {
          total: 0, //总数
          useCount: 0, //可操作的数量
        };
      },
    },
    //批量操作的条件参数
    propSearchParams: {
      type: Object,
      default: () => {},
    },
    //此组件内的组件名称：Deal,Close,Assign
    componentName: {
      type: String,
      default: '',
    },
    //批量操作选中的数据
    selectData: {
      type: Array,
      default: () => {
        return [];
      },
    },
    //唯一标识对象，用作给后端传参使用，
    identificationObj: {
      type: Object,
      default: () => {
        return {
          identify: 'id', //取对象的哪个字段
          identifyParams: 'ids', //参数所需要的列表名称
        };
      },
    },
    isTaskOrder: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      visible: false,
      isDisabled: false,
      tellphoneNumber: '',
      messageNotice: '0',
      modelColumns: modelColumns,
      batchTableLoading: false,
      batchTablePageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      sucessData: [],
      checkIds: [],
      titles: {
        Assign: '指派',
        Deal: '处理',
        Close: '关闭',
      },
      orderLoading: false,
    };
  },
  computed: {
    modalTitle() {
      return '任务' + this.titles[this.componentName];
    },
  },
  watch: {
    value: {
      handler(val) {
        this.visible = val;
        if (val) {
          this.getBatchPageList();
        }
      },
      immediate: true,
    },
  },
  methods: {
    updateDisabled(datas) {
      this.isDisabled = !datas;
      if (datas) {
        this.tellphoneNumber = datas.phoneNumber || '';
      }
    },
    changeNoticeType(val) {
      if (val === '1' && !!this.$refs[this.componentName].people) {
        this.tellphoneNumber = this.$refs[this.componentName].people.phoneNumber || '';
      }
    },
    cancel() {
      this.$emit('closeOrderOptionsModal');
      this.$refs[this.componentName].reset();
      this.messageNotice = '0';
      this.tellphoneNumber = '';
    },
    //获取批量工单分页列表
    async getBatchPageList() {
      let params = {
        ...this.propSearchParams,
        pageNumber: this.batchTablePageData.pageNum,
        pageSize: this.batchTablePageData.pageSize,
      };
      this.batchTableLoading = true;
      try {
        const { data } = await this.$http.post(governancetask.batchPageList, params);
        this.sucessData = data.data.entities;
        this.batchTablePageData.totalCount = data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.batchTableLoading = false;
      }
    },
    //批量操作表格分页
    changebatchTablePage() {
      this.getBatchPageList();
    },
    //批量操作表格分页
    changebatchTablePageSize() {
      this.getBatchPageList();
    },
    submit() {
      let params;
      let url = '';
      let text = '';
      let form = {};
      this.checkIds = [];
      let checkAllNeedParams = {};
      if (this.isBatch && this.isCheckAll) {
        checkAllNeedParams = this.propSearchParams;
      } else if (this.isBatch && !this.isTaskOrder) {
        this.checkIds = this.selectData.map((item) => item.id);
      } else {
        this.checkIds = this.sucessData.map((item) => item.id);
      }
      //若全选需要带上搜索条件
      switch (this.componentName) {
        case 'Deal': // 工单处理
          form = this.$refs[this.componentName].form;
          params = {
            ids: this.checkIds, // 工单id
            batchType: 'deal',
            finishResult: form.finishResult, // 办理结果
            finishSituation: form.finishSituation, // 办理情况说明
            feedbackResult: form.feedbackResult, // 原因反馈
            ...checkAllNeedParams,
          };
          url = 'batchHandle';
          text = '工单处理';
          break;
        case 'Close': //  工单关闭
          form = this.$refs[this.componentName].form;
          params = {
            ids: this.checkIds,
            batchType: 'close',
            finishSituation: form.finishSituation,
            ...checkAllNeedParams,
          };
          url = 'batchClose';
          text = '工单关闭';
          break;
        case 'Assign': // 工单指派
          if (this.messageNotice === '1') {
            const phoneReg = /^[1][2,3,4,5,6,7,8,9][0-9]{9}$/;
            if (!phoneReg.test(this.tellphoneNumber)) {
              this.$Message.warning('请输入正确的手机号码');
              return;
            }
          }
          form = this.$refs[this.componentName].searchForm;
          params = {
            ids: this.checkIds, // 工单id
            batchType: 'assign',
            receiverId: form.assignMode === 0 ? form.people.username : '', // 指派人ID
            receiverName: form.assignMode === 0 ? form.people.name : '', // 指派人名称
            phoneNumber: this.tellphoneNumber,
            sendStatus: '1',
            assignMode: form.assignMode,
            assignList: form.assignList,
            ...checkAllNeedParams,
          };
          url = 'batchAssign';
          text = '工单指派';
          break;
      }
      this.handleOrder(params, url, text);
    },
    async handleOrder(params, url, text) {
      try {
        this.orderLoading = true;
        let { data } = await this.$http.post(governancetask[url], params);
        let { success = 0, failure = 0 } = data.data || {};
        // this.orderOptionShow = false;
        this.$refs[this.componentName].reset();
        if (this.componentName === 'Assign') {
          this.$Message.success({
            content: `共创建${Number.parseInt(success) + Number.parseInt(failure)}条工单，
          成功指派${success}条工单！${
              failure
                ? `有${failure}条工单无法匹配 ${
                    params.assignMode === 1 ? '维护单位联系人' : '对应单位的工单接收人'
                  }，无法指派！请在工单列表页面手动指派！`
                : ''
            } `,
            duration: 5,
          });
        } else if (this.componentName === 'Deal') {
          this.$Message.success('提交成功！请稍后查看系统检测结果');
        } else if (this.componentName === 'Close' && data.data.deviceNum && data.data.deviceNum * 1 > 0) {
          this.$Message.error(`${data.data.msg}`);
        } else {
          this.$Message.success(
            `${success ? `有${success}条${text}成功` : ''}${failure ? ` 有${failure}条${text}失败！` : ''}`,
          );
        }
        this.$emit('changeOrderOptionsModal');
      } catch (error) {
        console.log(error);
      } finally {
        this.orderLoading = false;
      }
    },
  },
  mounted() {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    Close: require('../close.vue').default,
    Deal: require('../deal.vue').default,
    Assign: require('../assign.vue').default,
  },
};
</script>
<style lang="less" scoped>
.order-option {
  @{_deep} > .modal {
    > .ivu-modal-wrap {
      > .ivu-modal {
        > .ivu-modal-content {
          > .ivu-modal-body {
            display: flex;
            justify-content: center;
          }
        }
      }
    }
  }

  &-content {
    width: 100%;
    // height: 620px;
    padding: 0 20px;

    .select-table {
      width: 100%;
      // height: 550px;
      display: flex;
      flex-direction: column;

      .tips {
        // padding: 0 20px;
        color: #e44f22;
        text-align: right;
      }

      .select-title {
        // padding: 0 20px;
        width: 110px;
        color: #fff;
        text-align: left;
      }
    }
  }
}
</style>
