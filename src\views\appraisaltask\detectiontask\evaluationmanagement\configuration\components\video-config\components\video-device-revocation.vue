<template>
  <div>
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="125">
      <common-form
        :label-width="125"
        :form-data="formData"
        :form-model="formModel"
        ref="commonForm"
        :moduleAction="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
      >
        <template #extract>
          <FormItem label="统计范围" class="right-item mb-sm" prop="statDimension">
            <Select v-model="formData.scope" class="select-width" placeholder="请选择统计范围" transfer disabled>
              <Option :value="1">按月统计</Option>
            </Select>
          </FormItem>
        </template>
      </common-form>
      <FormItem label="撤销数量">
        <RadioGroup v-model="formData.revocationNum">
          <Radio label="addUp">
            <span>累加变化统计</span>
          </Radio>
          <Radio label="atLast">
            <span>最终变化统计</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="请确定比对字段"> </FormItem>
      <transfer-table
        class="tt mb-sm mr-xs"
        :left-table-columns="leftTableColumns"
        :right-table-columns="rightTableColumns"
        :left-table-data="leftTableData"
        :right-table-data="rightTableData"
        :leftLoading="leftLoading"
        :rightLoading="rightLoading"
        @onLeftToRight="selectionChange"
      >
      </transfer-table>
    </Form>
  </div>
</template>

<script>
export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      ruleCustom: {},
      formData: {
        regionCode: '',
        detectMode: '',
        cronType: '',
        cronData: [],
        timePoints: [],

        compareParam: [{ fieldName: 'deviceId', fieldRemark: '设备编号' }],
        scope: 1,
        revocationNum: 'addUp', //撤销数量  累加变化统计:addUp    最终变化统计:atLast
      },
      indexRuleList: [],
      list: [],
      loading: false,
      leftTableColumns: [
        { key: 'fieldName', title: '字段名', minWidth: 100 },
        { key: 'fieldRemark', title: '注释', minWidth: 100 },
      ],

      leftTableData: [
        { fieldName: 'deviceId', fieldRemark: '设备编号', '_disabled': true },
        { fieldName: 'deviceName', fieldRemark: '设备名称' },
        { fieldName: 'civilCode', fieldRemark: '行政区域代码' },
        { fieldName: 'orgCode', fieldRemark: '组织机构代码' },
        { fieldName: 'address', fieldRemark: '设备安装地址' },
        { fieldName: 'ipAddr', fieldRemark: 'IPV4地址' },
        { fieldName: 'ipv6Addr', fieldRemark: 'IPV6地址' },
        { fieldName: 'macAddr', fieldRemark: 'MAC地址' },
        { fieldName: 'sbdwlx', fieldRemark: '摄像机点位类型' },
        { fieldName: 'sbgnlx', fieldRemark: '摄像机功能类型' },
        { fieldName: 'phyStatus', fieldRemark: '设备物理状态' },
        { fieldName: 'sblwzt', fieldRemark: '设备联网状态' },
        { fieldName: 'isOnline', fieldRemark: '设备是否在线状态' },
        { fieldName: 'port', fieldRemark: '端口' },
        { fieldName: 'longitude', fieldRemark: '经度' },
        { fieldName: 'latitude', fieldRemark: '维度' },
        { fieldName: 'sbcjqy', fieldRemark: '摄像机采集区域' },
      ],
      leftLoading: false,
      rightTableColumns: [],
      rightTableData: [],
      rightLoading: false,
    };
  },
  async created() {
    this.setFormData();
  },
  watch: {},
  methods: {
    selectionChange(val) {
      this.rightTableData = val.map((item) => {
        return {
          fieldName: item.fieldName,
          fieldRemark: item.fieldRemark,
        };
      });
    },
    setFormData() {
      if (this.formModel === 'edit') {
        this.formData = {
          ...this.configInfo,
        };
      } else {
        const { regionCode } = this.moduleAction;
        this.formData = {
          ...this.formData,
          regionCode: regionCode, // 检测对象
          detectMode: '1',
        };
      }
      this.setCheckConfig();
    },
    setCheckConfig() {
      this.leftTableData.forEach((item) => {
        let index = this.formData.compareParam.findIndex((value) => value['fieldName'] === item['fieldName']);
        this.$set(item, '_checked', index !== -1);
      });
      this.rightTableData = this.formData.compareParam;
    },
    validateForm() {
      if (!this.rightTableData.length) return false;
      return true;
    },

    // 表单提交校验
    async handleSubmit() {
      if (!this.validateForm()) {
        this.$Message.error('请选择对比字段');
        return false;
      }
      this.formData.compareParam = JSON.parse(JSON.stringify(this.rightTableData));
      return await this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
  },
  components: {
    TransferTable: require('@/components/transfer-table.vue').default,
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.tt {
  height: 500px;
}
</style>
