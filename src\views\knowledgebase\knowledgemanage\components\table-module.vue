<template>
  <div class="otherassets auto-fill">
    <div class="mt-sm search-box">
      <ui-label class="inline mr-lg mb-sm" label="知识名称">
        <Input class="width-md" v-model="searchData.name" placeholder="请输入知识名称"></Input>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="关联问题">
        <Select class="width-md" v-model="searchData.questionIdList" placeholder="请选择关联问题" multiple clearable>
          <Option v-for="item in getQuestionList" :key="item.id" :value="item.id">{{ item.errorMessage }} </Option>
        </Select>
      </ui-label>
      <ui-label class="inline mr-lg mb-sm" label="知识状态">
        <Select class="width-md" v-model="searchData.publishStatus" :placeholder="`请选择知识状态`" clearable>
          <Option value="0">未发布</Option>
          <Option value="1">已发布</Option>
        </Select>
      </ui-label>
      <div class="inline">
        <Button type="primary" @click="startSearch">查询</Button>
        <Button class="ml-sm" @click="reset">重置</Button>
      </div>
    </div>
    <div class="over-flow mb-sm">
      <!-- <Checkbox class="checks mr-lg"
                v-model="isAll"
                @on-change="">
        全选</Checkbox> -->
      <div class="btn fr">
        <Button type="text" :loading="templateLoading" class="ml-sm" @click="exportModule">
          <span class="link">下载模板</span>
        </Button>
        <Button type="primary" class="ml-sm" @click="addKnowledge">
          <i class="icon-font icon-tianjia f-14"></i>
          <span class="vt-middle ml-sm">新建知识</span>
        </Button>
        <Upload
          action="/ivdg-knowledge-app/knowledgeData/importKnowledgeData"
          ref="upload"
          class="inline"
          :show-upload-list="false"
          :headers="headers"
          :before-upload="beforeUpload"
          :on-success="importSuccess"
          :on-error="importError"
        >
          <Button type="primary" class="ml-sm">
            <i class="icon-font icon-daoruwentishebei f-12"></i>
            <span class="vt-middle ml-sm">导入</span>
          </Button>
        </Upload>
        <Button type="primary" class="btn_search ml-sm" @click="onExport">
          <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
          <span class="inline ml-xs">导出</span>
        </Button>
        <Button type="primary" class="ml-sm" :loading="deleteLoading" @click="batchDelete">
          <i class="icon-font icon-piliangshanchu mr-xs f-12"></i>
          <span>批量删除</span>
        </Button>
      </div>
    </div>
    <div class="table-module auto-fill">
      <ui-table
        class="ui-table auto-fill"
        :table-columns="tableColumns"
        :table-data="tableData"
        :loading="loading"
        @selectTable="selectTable"
      >
        <template #questionList="{ row }">
          {{ row.questionList && row.questionList.map((item) => item.errorMessage).join(',') }}
        </template>
        <template #publishStatusName="{ row }">
          <span class="check-status" :class="row.publishStatus == '0' ? 'bg-color-grey' : 'bg-success'">{{
            row.publishStatusName
          }}</span>
        </template>
        <template #action="{ row }">
          <div>
            <ui-btn-tip
              class="mr-md"
              :styles="{ color: 'rgb(67, 140, 255)', 'font-size': '12px' }"
              icon="icon-chakanxiangqing"
              content="查看详情"
              @click.native="viewDetail(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              icon="icon-bianji2"
              content="知识编辑"
              @click.native="editKnowledge(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              class="mr-md"
              :styles="{
                color: row.publishStatus == '0' ? '#DE990F' : '#18AFCF',
                'font-size': row.publishStatus == '0' ? '14px' : '20px',
              }"
              :icon="row.publishStatus == '0' ? 'icon-quxiaofabu' : 'icon-fabu-01'"
              content="发布"
              @click.native="releaseItem(row)"
            ></ui-btn-tip>
            <ui-btn-tip
              :styles="{ color: '#CF3939 ', 'font-size': '14px' }"
              icon="icon-piliangshanchu"
              content="删除"
              @click.native="singleDelete(row)"
            ></ui-btn-tip>
          </div>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <view-detail v-model="detailShow" :active-item="activeItem"></view-detail>
    <add-edit
      v-model="addEditShow"
      :modal-action="addEditAction"
      @update="startSearch"
      @directRelease="directRelease"
    ></add-edit>
  </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import knowledgebase from '@/config/api/knowledgebase';
import downLoadTips from '@/mixins/download-tips';
import equipmentassets from '@/config/api/equipmentassets';
export default {
  name: 'otherassets',
  props: {
    activeCatalog: {},
  },
  mixins: [downLoadTips],
  data() {
    return {
      searchData: {
        name: '',
        publishStatus: '',
        questionIdList: [],
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      importLoading: false,
      deleteLoading: false,
      loading: false,
      tableColumns: [
        { type: 'selection', width: 50, fixed: 'left', align: 'center' },
        { title: '序号', type: 'index', align: 'center', width: 50 },
        {
          width: 180,
          title: '知识名称',
          key: 'name',
          align: 'left',
        },
        {
          minWidth: 190,
          title: '所属目录',
          key: 'catalogueName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 130,
          title: '关联问题',
          slot: 'questionList',
          align: 'left',
          tooltip: true,
        },
        {
          width: 150,
          title: '知识状态',
          slot: 'publishStatusName',
          align: 'left',
          tooltip: true,
        },
        {
          width: 100,
          title: '创建人',
          key: 'creator',
          tooltip: true,
        },
        { width: 160, title: '创建时间', key: 'createTime' },
        { width: 160, title: '发布人', key: 'publishUser' },
        { width: 160, title: '发布时间', key: 'publishTime' },
        {
          width: 140,
          title: '操作',
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding' /*操作栏列-单元格padding设置*/,
        },
      ],
      tableData: [],
      checkedData: [],
      // 编辑
      addEditShow: false,
      addEditAction: {
        action: 'add',
        title: '新增设备',
        activeCatalog: {},
      },
      detailShow: false,
      activeItem: {},
      templateLoading: false,
      headers: {
        Authorization: `Bearer ${window.sessionStorage.token}`,
      },
    };
  },
  created() {
    this.copySearchDataMx(this.searchData);
    this.init();
  },
  methods: {
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    async onExport() {
      try {
        this.exportLoading = true;
        let params = {
          ...this.searchData,
          catalogueId: this.activeCatalog.id,
        };
        this.$_openDownloadTip();
        const res = await this.$http.post(knowledgebase.exportKnowledgeData, params, {
          responseType: 'blob',
        });
        this.$util.common.exportfile(res);
      } catch (err) {
        console.log(err);
      } finally {
        this.exportLoading = false;
      }
    },
    releaseItem(row) {
      this.$UiConfirm({
        content: `您要发布这${row.catalogueName}项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          let params = {
            id: row.id,
            publishStatus: row.publishStatus === '1' ? '0' : '1',
          };
          this.publishKnowledgeData(params);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    // 新建直接发布
    directRelease(data) {
      let params = {
        ...data,
        publishStatus: '1',
      };
      this.publishKnowledgeData(params);
    },
    async publishKnowledgeData(params) {
      try {
        await this.$http.put(knowledgebase.publishKnowledgeData, params);
        this.$Message.success('发布成功');
        this.init();
      } catch (err) {
        console.log(err);
      }
    },
    async initStatistic() {
      try {
        await this.$http.post(equipmentassets.queryOtherStatistics);
        this.startSearch();
      } catch (err) {
        console.log(err);
      }
    },
    async init() {
      try {
        this.loading = true;
        let { pageNum: pageNumber, pageSize } = this.pageData;
        let params = {
          ...this.searchData,
          pageNumber,
          pageSize,
          catalogueId: this.activeCatalog.id,
        };
        const res = await this.$http.post(knowledgebase.knowledgeDataPageList, params);
        this.tableData = res.data.data.entities;
        this.pageData.totalCount = res.data.data.total;
      } catch (err) {
        console.log(err);
      } finally {
        this.loading = false;
      }
    },
    startSearch() {
      this.pageData.pageNum = 1;
      this.init();
    },
    reset() {
      // this.$emit('resetCatalog')
      this.resetSearchDataMx(this.searchData, this.startSearch);
    },
    changePage(val) {
      this.pageData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.pageData.pageNum = 1;
      this.pageData.pageSize = val;
      this.init();
    },
    addKnowledge() {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'add',
        title: '新增知识',
        activeCatalog: this.activeCatalog,
      };
    },
    editKnowledge(row) {
      this.addEditShow = true;
      this.addEditAction = {
        action: 'edit',
        title: `编辑知识${row.name}`,
        activeCatalog: this.activeCatalog,
        editItem: row,
      };
    },
    viewDetail(row) {
      this.detailShow = true;
      this.activeItem = row;
    },
    async exportModule() {
      try {
        this.templateLoading = true;
        const res = await this.$http.get(knowledgebase.downloadTemplate);
        await this.$util.common.transformBlob(res.data.data);
      } catch (err) {
        console.log(err);
      } finally {
        this.templateLoading = false;
      }
    },
    beforeUpload() {
      this.importLoading = true;
    },
    importSuccess(res) {
      this.importLoading = false;
      if (res.code === 200) {
        if (res.data.failCount === 0) {
          this.$Message.success(res.data.tip);
        } else {
          this.$Message.warning({
            closable: true,
            content: res.data.tip,
          });
        }
        this.init();
      } else {
        this.$Message.error(res.msg);
      }
    },
    importError(res) {
      this.importLoading = false;
      this.$Message.error(res.msg);
    },
    selectTable(selection) {
      this.checkedData = selection;
    },
    batchDelete() {
      if (!this.checkedData.length) return this.$Message.error('请选择知识');
      let ids = this.checkedData.map((item) => item.id);
      this.$UiConfirm({
        content: `您要删除这${ids.length}项，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDevice(ids);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    singleDelete(row) {
      this.$UiConfirm({
        content: `您要删除${row.name}，是否确认?`,
        title: '警告',
      })
        .then(() => {
          this.deleteDevice([row.id]);
        })
        .catch((res) => {
          console.log(res);
        });
    },
    async deleteDevice(ids) {
      try {
        this.deleteLoading = true;
        const res = await this.$http.delete(knowledgebase.removeKnowledgeData, { data: { ids: ids } });
        this.$Message.success(res.data.msg);
        this.checkedData = [];
        this.startSearch();
      } catch (err) {
        console.log(err);
        this.$Message.error(err.data.msg);
      } finally {
        this.deleteLoading = false;
      }
    },
  },
  watch: {
    'activeCatalog.id'() {
      this.startSearch();
    },
  },
  computed: {
    ...mapGetters({
      getQuestionList: 'knowledgebase/getQuestionList',
      phystatusList: 'algorithm/propertySearch_phystatus',
      other_device_type: 'algorithm/other_device_type',
    }),
  },
  components: {
    viewDetail: require('./view-detail.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    AddEdit: require('./add-edit.vue').default,
  },
};
</script>
<style lang="less" scoped>
[data-theme='light'],
[data-theme='deepBlue'] {
  .search-box {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 10px;
  }
}
.otherassets {
  padding: 10px 10px 0 10px;
  background-color: var(--bg-content);
  .link {
    text-decoration: underline;
  }
}
</style>
