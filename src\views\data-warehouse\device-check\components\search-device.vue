<template>
	<div class="search  card-border-color">
		<Form :inline="true">
			<div class="general-search">
				<div class="input-content">
					<div class="input-content-row">
						<FormItem label="分组路径:">
							<Input v-model="formData.dirPath" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="设备名称:">
							<Input v-model="formData.deviceName" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="数据类型:">
							<Select v-model="formData.dataTypeList" placeholder="请选择" multiple :max-tag-count="1">
								<Option :value="item.dataKey" v-for="(item, index) in dataTypeArrayList" :key="index">{{ item.dataValue }}</Option>
								<!-- <Option :value="1">人脸</Option> 
								<Option :value="2">车辆</Option>
								<Option :value="3">非机动车</Option>
								<Option :value="4">人体</Option> -->
							</Select>
						</FormItem>
						<FormItem label="数据总量状态:">
							<Select v-model="formData.dataStatusList" placeholder="请选择" multiple :max-tag-count="1">
								<Option :value="item.dataKey" v-for="(item, index) in dataStatusArrayList" :key="index">{{ item.dataValue }}</Option>
								<!-- <Option :value="1">减少百分之20-50</Option>
								<Option :value="2">减少百分之50-80</Option>
								<Option :value="3">减少百分之80以上</Option>
								<Option :value="4">增长百分之20-50</Option>
								<Option :value="5">增长百分之50-80</Option>
								<Option :value="6">增长百分之80以上</Option>
								<Option :value="7">无数据</Option>
								<Option :value="8">正常</Option> -->
							</Select>
						</FormItem>
						<FormItem label="统计数据日期:">
							<Select v-model="formData.dataTime" placeholder="请选择">
								<Option v-for="item in dateTimeList" :key="item.value" :value="item.value">{{ item.label }}</Option>
							</Select>
						</FormItem>
					</div>
				</div>
				<div class="btn-group">
					<Button type="primary" @click="search">查询</Button>
					<Button type="default" @click="resetForm">重置</Button>
				</div>
			</div>
		</Form>
	</div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import { getConfigDate } from '@/util/modules/common';
export default {
	data() {
		return {
			formData: {
				dirPath: '',
				deviceName: '',
				dataTypeList: [],
				dataStatusList: [],
				dataTime: getConfigDate(-1)[0]
			},
			dateTimeList: [ ]
		}
	},
	computed: {
		...mapGetters({
			dataStatusArrayList: 'dictionary/getDataStatusList', // 数据状态
			dataTypeArrayList: 'dictionary/getDataTypeList', // 数据类型
		})
	},
	async created() {
		await this.getDictData();
		this.statisticsDate();
	},
	methods: {
		...mapActions({
			getDictData: 'dictionary/getDictAllData'
		}),
		statisticsDate() {
			this.dateTimeList = [];
			let num = [-1, -2, -3, -4, -5];
			num.forEach(item => {
				let date = getConfigDate(item);
				this.dateTimeList.push({
					value: date[0],
					label: date[0]
				})
			})
			this.dataTime = getConfigDate(-1)[0];
		},
		search() {
			let searchForm = {
				dirPath: this.formData.dirPath,
				deviceName: this.formData.deviceName,
				dataTypeList: this.formData.dataTypeList,
				dataStatusList: this.formData.dataStatusList,
				dataTime: this.formData.dataTime,
			};
			this.$emit('searchInfo', searchForm)
		},
		resetForm() {
			this.formData = {
				dirPath: '',
				deviceName: '',
				dataTypeList: [],
				dataStatusList: [],
				dataTime: getConfigDate(-1)[0],
			};
			this.$emit('searchInfo', this.formData)
		},

	}
}
</script>
<style lang="less" scoped>
	.btn-group {
		display: flex;
		align-items: flex-end;
		margin-bottom: 16px;
		justify-content: flex-end;
		flex: 1;
	}
	.search {
		padding: 16px 20px 0;
		border-bottom: 1px solid #ffff;
		display: flex;
		width: 100%;
		justify-content: space-between;
		position: relative;
		.ivu-form-inline {
			width: 100%;
		}
		.ivu-form-item {
			margin-bottom: 16px;
			margin-right: 30px;
			display: flex;
			align-items: center;
			/deep/ .ivu-form-item-label {
				white-space: nowrap;
				max-width: 90px;
			}
			/deep/ .ivu-form-item-content {
				display: flex;
			}
		}
		.general-search {
			display: flex;
			width: 100%;
			.input-content {
				width: 75%;
				display: flex;
				flex-wrap: wrap;
				.input-content-row {
					display: flex;
				}
			}
		}
	}
</style>
