<template>
  <div class="monitor-tooltip-container">
    <p v-if="cuptureMonthOrDay === 'month'" class="mb-13">{{ getDate }}</p>
    <div v-for="(item, index) in getList" :key="index" class="tooltip-content-box">
      <p class="legend-box mb-13">
        <span> {{ item.name }}: &nbsp;</span>
        <span :style="{ color: item.color }" class="font-num">
          {{ data[0].data?.[item.key] || 0 }}
        </span>
      </p>
    </div>
  </div>
</template>
<script>
export default {
  name: 'MonitorLineChartTooltip',
  props: {},
  data() {
    return {
      // 月趋势
      detailsDataMonth: [
        {
          name: '日抓拍',
          key: 'sum',
          color: 'var(--color-display-text)',
        },
        {
          name: '日新增',
          key: 'addSum',
          color: 'var(--color-display-text)',
        },
      ],
      // 日趋势
      detailsDataDay: [
        {
          name: '时间',
          key: 'timeText',
          color: 'var(--color-display-text)',
        },
        {
          name: '抓拍量',
          key: 'sum',
          color: 'var(--color-display-text)',
        },
      ],
    };
  },
  computed: {
    getList() {
      return this.cuptureMonthOrDay === 'month' ? this.detailsDataMonth : this.detailsDataDay;
    },
    getDate() {
      let { day, time, dayText } = this.data[0].data;
      let text =
        this.cuptureMonthOrDay === 'month' ? dayText : `${time} ${this.setPre(day)}:00-${this.setPre(day + 1)}:00`;
      return text;
    },
  },
  methods: {
    setPre(val) {
      return val >= 10 ? val : `0${val}`;
    },
  },
};
</script>
<style lang="less" scoped>
.monitor-tooltip-container {
  background: var(--bg-tooltip);
  border-radius: 4px;
  border: 1px solid var(--border-tooltip);
  min-width: 200px;
  padding: 25px 30px 7px 20px;
  color: var(--color-content);
  .mb-13 {
    margin-bottom: 13px;
  }
  .tooltip-content-box {
    .legend-box {
      color: var(--color-content);
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    // .legend-box ~ .legend-box {
    //   margin-bottom: 20px;
    // }
    .font-num {
      font-weight: 600;
      // margin-left: 30px;
    }
  }
}
</style>
