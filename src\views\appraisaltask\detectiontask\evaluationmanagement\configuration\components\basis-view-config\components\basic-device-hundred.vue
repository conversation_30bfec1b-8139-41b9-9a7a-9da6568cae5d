<template>
  <div class="basic-accuracy-container">
    <Form ref="modalData" :model="formData" :rules="ruleCustom" class="form-content" :label-width="125">
      <common-form
        :label-width="125"
        :form-data="formData"
        :form-model="formModel"
        ref="commonForm"
        :moduleAction="moduleAction"
        :task-index-config="taskIndexConfig"
        @updateFormData="updateFormData"
      >
      </common-form>
      <template>
        <FormItem label="设置人口和占比率" class="mb-md" prop="quantityData">
          <Button type="dashed" class="width-md" @click="regionalizationSelectVisible = true">
            <span>去设置 已设置{{ quantityDataCount }}个</span>
          </Button>
        </FormItem>
        <FormItem label="统计设备类型" prop="sbgnlx">
          <CheckboxGroup v-model="formData.sbgnlx">
            <Checkbox :label="item.value" v-for="(item, index) in deviceTypeList" :key="index">
              {{ item.label }}
            </Checkbox>
          </CheckboxGroup>
        </FormItem>
      </template>
    </Form>

    <basic-device-hundred-select
      v-model="regionalizationSelectVisible"
      v-if="regionalizationSelectVisible"
      :data="areaTreeData"
      @query="quantityQuery"
    >
    </basic-device-hundred-select>
  </div>
</template>

<script>
import evaluationoverview from '@/config/api/evaluationoverview';

export default {
  props: {
    indexType: {
      type: String,
      default: '',
    },
    moduleAction: {
      type: Object,
      default: () => {},
    },
    // 配置回显详情
    configInfo: {
      required: true,
      type: Object,
      default: () => {},
    },
    // 所有配置详情
    taskIndexConfig: {
      type: Object,
      default: () => ({}),
    },
    //标识 add-新增/edit-编辑
    formModel: {
      required: true,
      type: String,
      default: 'add',
    },
  },

  data() {
    return {
      ruleCustom: {
        sbgnlx: [{ message: '请选择统计设备类型', trigger: 'blur', type: 'array', required: true }],
      },
      formData: {
        regionCode: '',
        detectMode: '',
        cronType: '',
        cronData: [],
        timePoints: [],
        quantityData: [],
        sbgnlx: [],
      },
      deviceTypeList: [
        { label: '视频监控', value: 1 },
        { label: '车辆卡口', value: 2 },
        { label: '人脸卡口', value: 3 },
      ],
      list: [],
      areaTreeData: [],
      assessmentItems: [
        { title: '人口数量', key: 'sxjValue', optionLabel: '人口数量' },
        { title: '百人达标占比率', key: 'rlkkValue', optionLabel: '百人达标占比率' },
      ],
      loading: false,
      regionalizationSelectVisible: false,
    };
  },
  async created() {},
  watch: {
    configInfo: {
      handler(val) {
        if (this.formModel === 'edit' && val) {
          this.formData = {
            ...this.formData,
            ...this.configInfo,
          };
        } else {
          const { regionCode } = this.moduleAction;
          this.formData = {
            ...this.formData,
            regionCode: regionCode, // 检测对象
          };
        }
        this.setBasicDeviceHundred();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    quantityQuery(data) {
      this.formData.quantityData = data;
    },
    async getChildByCode() {
      try {
        this.loading = true;
        let { regionCode } = this.formData;
        let {
          data: { data },
        } = await this.$http.get(evaluationoverview.queryChildByCode, {
          params: { regionCode },
        });
        this.treeData = data || [];
      } catch (e) {
        console.log(e);
      } finally {
        this.loading = false;
      }
    },
    async setBasicDeviceHundred() {
      await this.getChildByCode();
      let { quantityData = [] } = this.formData;
      let setFilterTreedata = this.treeData.map((item) => {
        let targetQuantity = quantityData.find((value) => value.key === item.regionCode);
        return {
          ...item,
          ...targetQuantity,
          check: !!targetQuantity, //是否考核
        };
      });
      this.areaTreeData = this.$util.common.arrayToJson(
        JSON.parse(JSON.stringify(setFilterTreedata)),
        'regionCode',
        'parentCode',
      );
    },
    // 表单提交校验
    async handleSubmit() {
      return (await this.$refs['modalData'].validate()) && this.$refs['commonForm'].handleSubmit();
    },
    // 参数提交
    updateFormData(val) {
      this.formData = {
        ...val,
      };
    },
  },
  computed: {
    isView() {
      return this.formModel === 'view';
    },
    quantityDataCount() {
      if (!this.formData.quantityData) return 0;
      return this.formData.quantityData.length;
    },
  },
  components: {
    CommonForm:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/configuration/components/common-form/index.vue')
        .default,
    BasicDeviceHundredSelect:
      require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/basic-device-hundred-select.vue')
        .default,
  },
};
</script>
<style lang="less" scoped>
.basic-accuracy-container {
  margin: 0 30px 0 20px;
}
</style>
