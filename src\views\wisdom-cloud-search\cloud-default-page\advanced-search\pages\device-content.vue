<template>
	<section class="flex">
		<aside class="menu-container">
			<Menu class="menu-content" v-if="ipbdDeviceTypeList.length > 0" width="auto" :active-name="selectMenuItemId">
				<template v-for="(item, i) of ipbdDeviceTypeList">
					<MenuItem v-if="item.dataKey != 1" :key="i" :name="item.id" @click.native="typeList(item)">
					<span>{{ item.dataValue }}</span>
					<b>{{ item.num }}</b>
					</MenuItem>
				</template>
			</Menu>
		</aside>
		<section class="main-container">
			<div class="device">
				<!-- 查询 -->
				<div class="search">
					<Form :model="queryData" ref="form" inline label-position="right">
						<FormItem label="设备名称：" prop="deviceName">
							<Input v-model="queryData.deviceName" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="设备编码：" prop="deviceId">
							<Input v-model="queryData.deviceId" placeholder="请输入"></Input>
						</FormItem>
						<FormItem label="安装地址：" prop="address">
							<Input v-model="queryData.address" placeholder="请输入"></Input>
						</FormItem>
					</Form>
					<div class="btn-group">
						<Button type="primary" @click="startSearch">查询</Button>
						<Button type="default" @click="resetHandle">重置</Button>
					</div>
				</div>
				<div class="table-container">
					<div class="table-content">
						<div v-for="(item, index) in tableList" :key="index" class="list-card box-2">
							<div class="content">
								<div class="collection">
									<div class="bg"></div>
									<ui-btn-tip class="collection-icon" v-if="item.myFavorite == '1'" content="取消收藏" icon="icon-yishoucang" transfer @click.native="collection(item, 2)" />
									<ui-btn-tip class="collection-icon" v-else content="收藏" icon="icon-shoucang" transfer @click.native="collection(item, 1)" />
								</div>
								<p class='identifier-content'>
									<img v-show="queryData.deviceType == 2" class="img-icon" src="@/assets/img/icons/icon-wifi.png" alt="">
									<img v-show="queryData.deviceType == 3" class="img-icon" src="@/assets/img/icons/icon-RFID.png" alt="">
									<img v-show="queryData.deviceType == 4" class="img-icon" src="@/assets/img/icons/icon-electric.png" alt="">
									{{item.deviceId}}
								</p>
								<div class="bottom-info">
									<time>
										<Tooltip content="设备名称" placement="right" transfer theme="light">
											<i class="iconfont icon-leixing"></i>
										</Tooltip>
										<span class="ellipsis" v-show-tips>{{item.deviceName}}</span>
									</time>
									<p>
										<Tooltip content="位置" placement="right" transfer theme="light">
											<i class="iconfont icon-location"></i>
										</Tooltip>
										<span class="ellipsis" v-show-tips>{{item.detailAddress}}</span>
									</p>
								</div>
								<!-- <div class="operate-bar">
									<p class="operate-content">
										<ui-btn-tip content="收藏" icon="icon-shoucang" />
										<ui-btn-tip content="分析" icon="icon-fenxi" />
										<ui-btn-tip content="布控" icon="icon-dunpai" transfer />
									</p>
								</div> -->
							</div>
							<!-- <UiListCard :type="deviceType" :data="item" @archivesDetailHandle="archivesDetailHandle(item)" /> -->
						</div>
						<ui-empty v-if="tableList.length < 1"></ui-empty>
						<ui-loading v-if="loading"></ui-loading>
					</div>
				</div>
				<!-- <div class="card-content">
          <div v-for="(item, index) in tableList" :key="index" class="card-item">
            <UiListCard type="device" :data="item" @archivesDetailHandle="archivesDetailHandle(item)" />
          </div>
          <ui-empty v-if="list.length < 1"></ui-empty>
          <ui-loading v-if="loading"></ui-loading>
        </div> -->
				<ui-page :current="pageInfo.pageNumber" :total="pageInfo.total" countTotal :page-size="pageInfo.pageSize" :page-size-opts="[25, 50, 100]" @pageChange="pageChange" @pageSizeChange="pageSizeChange">
				</ui-page>
			</div>
		</section>
	</section>
</template>
<script>
	import { mapActions, mapGetters } from 'vuex'
	import { queryDeviceRecordSearchEx, countDeviceDataTotal } from '@/api/wisdom-cloud-search'
	import Search from '../../../../holographic-archives/one-plane-one-archives/components/search.vue'
	import UiListCard from '@/components/ui-list-card'
	import { addCollection, deleteMyFavorite } from '@/api/user'
	export default {
		name: 'advancedSearch',
		components: {
			Search,
			UiListCard
		},
		props: {},
		data() {
			return {
				loading: false,
				list: [],
				selectMenuItemId: null,
				deviceType: 'device',
				queryData: {},
				tableList: [],
				pageInfo: {
					pageNumber: 1,
					pageSize: 25,
					total: 0
				},
				menuList: [
					{ label: 'MAC设备', num: 0, id: 1 },
					{ label: '电围设备', num: 0, id: 2 },
					{ label: 'RFID设备', num: 0, id: 3 }
				]
			}
		},
		created() { },
		computed: {
			...mapGetters({
				ipbdDeviceTypeList: 'dictionary/getIpbdDeviceTypeList', // 设备类型
			})
		},
		async mounted() {
			await this.getDictData()
			this.selectMenuItemId = this.ipbdDeviceTypeList[1].id
			this.init()
			this.deviceCount()
		},
		methods: {
			...mapActions({
				getDictData: 'dictionary/getDictAllData'
			}),
			init() {
				// this.queryData.keyWords = this.$parent.indexSearchData.keyWords
				this.queryData.deviceType = this.ipbdDeviceTypeList.find(ite => { return ite.id == this.selectMenuItemId }).dataKey
				queryDeviceRecordSearchEx({ ...this.queryData, ...this.pageInfo }).then((res) => {
					const { total, entities } = res.data
					this.pageInfo.total = total
					this.tableList = entities
				}).catch(err => {
					console.error(err)
				}).finally(() => {
				})
			},
			deviceCount() {
				this.$nextTick(() => {
					countDeviceDataTotal({}).then((res) => {
						var obj = res.data
						this.ipbdDeviceTypeList.forEach(item => {
							if (item.dataKey == 2) {
								item.num = obj.wifi_device
								this.deviceType = ''
							}
							if (item.dataKey == 3) {
								item.num = obj.rfid_device
							}
							if (item.dataKey == 4) {
								item.num = obj.base_station_device
							}
						})
					}).catch(err => {
						console.error(err)
					}).finally(() => {
					})

				})
			},
			searchInfo(obj) {
				this.pageInfo.pageNumber = 1
				this.queryParam = obj
				this.init()
			},
			// 页数改变
			pageChange(size) {
				this.pageInfo.pageNumber = size
				this.init()
			},
			// 页数量改变
			pageSizeChange(size) {
				this.pageInfo.pageNumber = 1
				this.pageInfo.pageSize = size
				this.init()
			},
			selectMenuItemHandle(item) {
				this.pageInfo.pageNumber = 1
				this.selectMenuItemId = item
				this.init()
			},
			// 档案详情
			archivesDetailHandle(val) {
				const { href } = this.$router.resolve({
					name: 'device-archive',
					query: { type: val.type }
				})
				window.open(href, '_blank')
			},
			// 查询
			startSearch() {
				this.pageInfo.pageNumber = 1
				this.init()
			},
			// 重置
			resetHandle() {
				this.$refs.form.resetFields()
				this.startSearch()
			},
			/**
			 * 左侧设备类型事件
			 */
			typeList(row) {
				console.log('设备类型切换', row)
				this.selectMenuItemId = row.id
				this.init()
			},
			/**
			 * 收藏
			 */
			collection(data, flag) {
				console.log('wifi收藏信息', data, flag)
				var val = 7
				if (this.queryData.deviceType == 3) val = 8
				if (this.queryData.deviceType == 4) val = 9

				var param = {
					favoriteObjectId: data.deviceId,
					favoriteObjectType: val,
				}
				if (flag == 1) {
					addCollection(param).then(res => {
						this.$Message.success("收藏成功");
						this.init()
					})
				} else {
					deleteMyFavorite(param).then(res => {
						this.$Message.success("取消收藏成功");
						this.init()
					})
				}
			},
		}
	}
</script>
<style lang="less" scoped>
	@import "style/index";
	.menu-container {
		border-right: 1px solid #d3d7de;
		width: 280px;
		padding: 16px 0 0;
		z-index: 10;
		.search-content {
			padding: 0 15px;
			margin-bottom: 10px;
		}
		.menu-content {
			&:after {
				display: none;
			}
			.ivu-menu-item {
				display: flex;
				justify-content: space-between;
				padding-top: 7px;
				padding-bottom: 7px;
				&:after {
					display: none;
				}
				b {
					color: #2c86f8;
				}
				&.ivu-menu-item-active {
					background: #2c86f8;
					color: #fff;
					b {
						color: #fff;
					}
				}
			}
		}
	}
	.main-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		height: 100%;
		width: 100%;
		.table-container {
			padding: 0;
		}
		.search {
			.ivu-form-inline {
				display: flex;
				flex-wrap: wrap;
				flex: 1;
			}
			.ivu-form-item {
				margin-bottom: 16px;
				margin-right: 30px;
				display: flex;
				&:last-child {
					margin-right: 0;
				}
			}
		}
		.device {
			flex: 1;
			display: flex;
			flex-direction: column;
			height: 100%;
			padding: 16px 20px 0 21px;
			position: relative;
			.card-content {
				display: flex;
				flex-wrap: wrap;
				overflow: auto;
				flex: 1;
				margin: 0 -5px;
				align-content: flex-start;
				.card-item {
					width: 20%;
					padding: 0 5px;
					box-sizing: border-box;
					margin-bottom: 10px;
					transform-style: preserve-3d;
					transition: transform 0.6s;
					.list-card {
						width: 100%;
						backface-visibility: hidden;
						/deep/.tag-wrap {
							margin-top: 10px;
							.ui-tag {
								margin: 0 5px 0 0 !important;
							}
						}
					}
				}
			}
		}
	}

	.box-2 {
		width: 20% !important;
		height: auto !important;
	}
</style>
