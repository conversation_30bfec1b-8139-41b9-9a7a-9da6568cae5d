<template>
  <div ref="echart" class="echarts"></div>
</template>

<script>
import giftImageUrl from '../../../../assets/img/home/<USER>'
import * as echarts from 'echarts'
/**
 *  参考  https://www.makeapie.com/editor.html?c=xNDuOrPXlC
 */
export default {
  name: 'HomeTitle',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [
        { value: 0, name: '周期执行' },
        { value: 0, name: '实时执行' }
      ]
    },
    colors: {
      type: Array,
      default: () => ['#E99E53', '#20D7FF']
    }

  },
  data () {
    return {
      myEchart: null
    }
  },
  watch: {
    data: function (val) {
      this.init()
    }
  },
  mounted () {

  },
  deactivated () {
    this.removeResizeFun()
  },
  beforeDestroy () {
    this.removeResizeFun()
  },
  methods: {
    init () {
      const _that = this
      this.myEchart = echarts.init(this.$refs.echart)
      var option = {
        tooltip: {
          show: false
        },
        legend: {
          top: '4%',
          right: '0%',
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.75)',
            padding: [0, 0, 0, 6]
          }
        },
        // color: this.colors,
        graphic: { // 这个属性可以在饼图内部填充图片,文字等
          elements: [{
            type: 'image', // 需要填充图片,配置image,如果不需要图片可以配置其他的, text, circle, sector, ring, polygon, polyline, rect, line, bezierCurve, arc, group,
            style: {
              image: giftImageUrl, // 这里添加图片地址
              width: parseInt(_that.myEchart.getWidth() * 0.52 / 2),
              height: parseInt(_that.myEchart.getWidth() * 0.52 / 2)
            },
            left: 'center', //
            top: 'middle' // 配置图片居中
          }]
        },
        series: [
          {
            name: 'Access From',
            type: 'pie',
            radius: ['52%', '62%'],
            hoverAnimation: false,
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              // borderColor: 'rgba(7, 27, 57, 0.7)',
              borderWidth: 4
            },
            label: {
              position: 'outer',
              alignTo: 'edge',
              margin: 10,
              formatter: function (params) {
                if (params.data.dataKey === '1') {
                  return `{realtime|${params.percent}%}{value| / ${params.value}}\n{name|${params.name}}`
                } else {
                  return `{cycle|${params.percent}%}{value| / ${params.value}}\n{name|${params.name}}`
                }
              },
              rich: {
                cycle: {
                  fontSize: 14,
                  color: '#20D7FF',
                  fontWeight: 'bold',
                  lineHeight: 24
                },
                realtime: {
                  fontSize: 14,
                  fontWeight: 'bold',
                  lineHeight: 24,
                  color: '#E99E53'
                },
                value: {
                  color: '#fff',
                  fontWeight: 'bold',
                  fontSize: 14
                },
                name: {
                  fontSize: 12,
                  color: 'rgba(255, 255, 255, 0.75)',
                  lineHeight: 20
                }
              }
            },
            labelLine: {
              length: 15,
              maxSurfaceAngle: 80
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < _that.myEchart.getWidth() / 2
              const points = params.labelLinePoints
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width
              return {
                labelLinePoints: points
              }
            },
            data: this.data
          }
        ]
      }

      this.myEchart.setOption(option)
      this.myEchart.on('click', function (params) {
        if (params.componentType === 'series') {
          const query = {
            execType: params.data.dataKey
          }
          _that.$router.push({
            path: '/label-analysis/label-task',
            query: {
              taskInfo: JSON.stringify(query)
            }
          })
        }
      })
      window.addEventListener('resize', () => this.myEchart.resize())
    },

    removeResizeFun () {
      window.removeEventListener('resize', () => this.myEchart.resize())
    }
  }
}
</script>

<style lang="less" scoped>
// 高度100%
.echarts {
  width: 100%;
  height: 100%;
}
</style>
