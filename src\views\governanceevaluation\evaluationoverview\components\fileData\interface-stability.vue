<template>
  <!--接口稳定性详情 -->
  <div class="carInfo" ref="contentScroll">
    <div class="information-header">
      <!-- 统计 -->
      <carStatistics :statistics-list="statisticsList" :isflexfix="true"></carStatistics>
      <!-- 排行 -->
      <div class="information-ranking" v-ui-loading="{ loading: rankLoading, tableData: rankData }">
        <div class="ranking-title">
          <title-content title="下级排行"></title-content>
        </div>
        <div class="ranking-list">
          <ul>
            <li v-for="(item, index) in rankData" :key="index">
              <div class="content-firstly">
                <span class="bg_color firstly1" v-if="index == 0">1</span>
                <span class="bg_color firstly2" v-if="index == 1">2</span>
                <span class="bg_color firstly3" v-if="index == 2">3</span>
                <span class="bg_color firstly3" v-if="index == 3">4</span>
                <span class="bg_color firstly3" v-if="index == 4">5</span>
              </div>
              <Tooltip class="content-second" transfer :content="item.regionName">
                <div>
                  <img class=" " v-if="index == 0" src="@/assets/img/crown_1.png" alt="" />
                  <img class=" " v-if="index == 1" src="@/assets/img/crown_2.png" alt="" />
                  <img class=" " v-if="index == 2" src="@/assets/img/crown_3.png" alt="" />
                  <!-- <span>{{ item.regionName }}</span> -->
                  <span v-if="index == 0">{{ item.cityName }}</span>
                  <span v-if="index == 1">{{ item.cityName }}</span>
                  <span v-if="index == 2">{{ item.cityName }}</span>
                  <span v-if="index == 3">{{ item.cityName }}</span>
                  <span v-if="index == 4">{{ item.cityName }}</span>
                </div>
              </Tooltip>
              <div class="content-thirdly">
                <span class="thirdly">{{ item.apiRate }}%</span>
              </div>

              <!-- <div class="content-fourthly">
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'RISE'"></i>
                <i class="icon-font icon-shangsheng color-sheng f-16" v-if="item.rankType === 'SAME'"></i>
                <i class="icon-font icon-xiajiang f-16 color-jiang" v-if="item.rankType === 'DOWN'"></i>
                <span class="plus color-sheng ml-sm" v-if="item.rankType === 'RISE'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-jiang ml-sm" v-else-if="item.rankType === 'DOWN'">{{ item.rankRise || 0 }}</span>
                <span class="plus color-sheng ml-md" v-else>{{ item.rankRise == null ? 0 : item.rankRise }}</span>
              </div> -->
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="information-main">
      <div class="abnormal-title">
        <div>
          <i class="icon-font icon-jiancejieguotongji f-16 color-filter"></i>
          <span class="f-16 color-filter ml-sm">检测结果详情</span>
        </div>
      </div>
      <ui-table
        class="ui-table table"
        :table-columns="tableColumns"
        :table-data="tableData"
        :maxHeight="contentClientHeight"
        :loadin="loading"
      >
        <template #cityName="{ row }">
          <span @click="orgDetail(row)" class="span-btn">{{ row.cityName }}</span>
        </template>

        <template #apiIndexNew="{ row }">
          {{ interfaceName(row.apiIndexNew) }}
        </template>

        <template #apiRate="{ row }">
          {{ !!row.apiRate ? row.apiRate + '%' : 0 }}
        </template>
      </ui-table>

      <loading v-if="loading"></loading>
      <ui-page class="page" :page-data="searchData" @changePage="changePage" @changePageSize="changePageSize">
      </ui-page>
    </div>
    <!-- 详情 -->
    <Detail ref="detail" :detail="paramsList"></Detail>
  </div>
</template>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  name: 'InterfaceStability',
  components: {
    carStatistics:
      require('@/views/governanceevaluation/evaluationoverview/components/car/component/car-statistics.vue').default,
    titleContent: require('@/views/appraisaltask/evaluationReport/components/title-content.vue').default,
    UiTable: require('@/components/ui-table.vue').default,
    Detail: require('./component/detail.vue').default,
  },
  props: {
    indexName: {
      type: String,
      default: '',
    },
    statisticalData: {
      type: Object,
      default: () => {},
    },
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      loading: false,
      rankLoading: false,
      rankData: [],
      paramsList: {},
      minusHeight: 488, // 表格
      statisticalList: {}, //统计值
      tableData: [],
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      tableColumns: [
        {
          type: 'index',
          width: 70,
          title: '序号',
          fixed: 'left',
          align: 'center',
        },
        { title: '组织机构', slot: 'cityName', width: 180 },
        { title: '调用接口', slot: 'apiIndexNew' },
        { title: '调用次数', key: 'Alltime' },
        { title: '调用成功次数', key: 'apiSuccess' },
        { title: '调用失败次数', key: 'apiFail' },
        { title: '接口稳定性', slot: 'apiRate' },
      ],
      statisticsList: [
        {
          name: '下级单位数量',
          value: 0,
          icon: 'icon-xiajidanweishuliang-01',
          iconColor: 'icon-bg1',
          liBg: 'li-bg1',
          type: 'number',
          key: 'subordinateUnit',
          textColor: 'color1',
        },
        {
          name: '检测单位数数量',
          value: 0,
          icon: 'icon-jiancedanshushuliang-01',
          iconColor: 'icon-bg2',
          liBg: 'li-bg2',
          type: 'number',
          key: 'detectionUnitCount',
          textColor: 'color2',
        },
        {
          name: '接口稳定达标单位数数量',
          value: 0,
          icon: 'icon-jiekouwendingdabiaodanshushuliang-01',
          iconColor: 'icon-bg9',
          liBg: 'li-bg9',
          type: 'number',
          key: 'standardUnit',
          textColor: 'color9',
        },
        {
          name: '调用接口总次数',
          value: 0,
          icon: 'icon-tiaoyongjiekouzongcishu-01',
          iconColor: 'icon-bg8',
          liBg: 'li-bg8',
          type: 'number',
          key: 'count',
          textColor: 'color8',
        },
        {
          name: '调用成功次数',
          value: 0,
          icon: 'icon-tiaoyongchenggongcishu-01',
          iconColor: 'icon-bg6',
          liBg: 'li-bg6',
          type: 'number',
          key: 'apiSuccessCount',
          textColor: 'color6',
        },
        {
          name: '调用失败次数',
          value: 0,
          icon: 'icon-tiaoyongshibaicishu-01',
          iconColor: 'icon-bg4',
          liBg: 'li-bg4',
          type: 'number',
          key: 'apiFailCount',
          textColor: 'color4',
        },
        {
          name: '接口稳定性',
          value: 0,
          icon: 'icon-jiekouwendingxing-01',
          iconColor: 'icon-bg3',
          liBg: 'li-bg3',
          type: 'percentage',
          key: 'resultValue',
          textColor: 'color3',
        },
      ],
      contentClientHeight: 0,
    };
  },
  watch: {
    statisticalData: {
      handler(val) {
        if (val) {
          this.statisticalList = val;
          this.statistical();
        }
      },
      deep: true,
      immediate: true,
    },
    paramsData: {
      handler(val) {
        if (val.orgRegionCode) {
          this.paramsList = val;

          this.init();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    const proportion = window.screen.height / 1080;
    const clientHeight = this.$refs.contentScroll.clientHeight;
    this.contentClientHeight = clientHeight > 0 ? clientHeight - 175 * proportion : 0;
  },
  methods: {
    // 初始化
    async init() {
      let data = {
        indexId: this.paramsList.indexId,
        batchId: this.paramsList.batchId,
        access: this.paramsList.access,
        displayType: this.paramsList.displayType,
        orgCode: this.paramsList.orgRegionCode,
        orgRegionCode: this.paramsList.orgRegionCode,
        pageNumber: this.searchData.pageNum,
        pageSize: this.searchData.pageSize,
      };
      try {
        this.loading = true;
        this.tableData = [];
        let res = await this.$http.post(evaluationoverview.getDetailData, data);
        this.tableData = res.data.data.entities;
        for (let i of this.tableData) {
          if (i.apiIndex == '1') {
            i.apiName = '人脸以图搜图接口';
          } else if (i.apiIndex == '2') {
            i.apiName = '车牌检索接口';
          }
          i.Alltime = i.apiSuccess + i.apiFail;
        }
        this.searchData.totalCount = res.data.data.total;
        this.loading = false;
        if (this.tableData.length > 5) {
          this.rankData = this.tableData.slice(0, 5);
        } else {
          this.rankData = this.tableData;
        }
      } catch (err) {
        console.log(err);
        this.loading = false;
      } finally {
        this.loading = false;
      }
    },
    interfaceName(arr) {
      if (arr.length == 0) return '--';
      var str = '';
      var flag = false;
      arr.forEach((item) => {
        if (flag) str += ' / ';
        if (item == 1) str += '人脸接口';
        if (item == 2) str += '车辆接口';
        flag = true;
      });
      return str;
    },
    changePage(val) {
      this.searchData.pageNum = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.pageSize = val;
      this.init();
    },
    // 详情
    orgDetail(row) {
      this.$refs.detail.info(row);
    },
    //统计
    statistical() {
      this.statisticsList.map((val) => {
        val.value = this.statisticalList[val.key] || 0;
        if (val.key === 'resultValue') {
          val.value = this.statisticalList.resultValue + '%';
        }
        if (val.key === 'resultValue' && this.statisticalList.qualified === '1') {
          val.qualified = true;
        } else if (val.key === 'resultValue' && this.statisticalList.qualified !== '1') {
          val.qualified = false;
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
.carInfo {
  position: relative;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
  @{_deep} .ivu-table {
    &-tip,
    &-overflowX,
    &-body {
      //min-height: 290px !important;
      min-height: calc(100vh - 599px) !important;
    }
  }
  .information-main {
    //height: calc(100% - 140px);
    .abnormal-title {
      padding: 10px 0;
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid var(--devider-line);
      .color-filter {
        color: rgba(43, 132, 226, 1);
        vertical-align: middle;
      }
    }
    .ui-table {
      height: 100%;
    }
  }
  .information-header {
    margin-top: 10px;
    height: 252px;
    display: flex;
    .information-statistics {
      display: flex;
      width: calc(100% - 320px);
      height: 100%;
      background: var(--bg-sub-content);
      margin-right: 10px;
      padding: 20px 5px 0 15px;
      /deep/.statistics-ul li {
        width: 32% !important;
      }
      /deep/.statistics-ul li:last-child {
        width: 21.5% !important;
      }
    }

    .information-ranking {
      width: 370px;
      background: var(--bg-sub-content);
      height: 100%;
      padding: 10px;
      .ranking-title {
        height: 30px;
        text-align: center;
      }
      .ranking-list {
        width: 100%;
        height: calc(100% - 30px);
        overflow-y: auto;
        display: block !important;
        ul {
          width: 100%;
          height: 100%;
          li {
            display: flex;
            padding-top: 15px;
            align-items: center;
            .content-fourthly {
              display: inline-flex;
              font-size: 14px;
              position: relative;
            }
            div {
              display: flex;

              align-items: center;
              font-size: 14px;
              position: relative;
            }

            .content-firstly {
              // width: 60px;
              flex: 1;
              margin-left: 10px;
            }
            .content-thirdly {
              justify-content: center;
              flex: 1;
            }
            .content-fourthly {
              justify-content: center;
              // width: 90px;
              flex: 1;
            }
            .content-second {
              color: #fff;
              justify-content: center;
              // width: 150px;
              flex: 1;

              img {
                vertical-align: middle;
              }

              span {
                width: 125px;
                padding-left: 10px;
                display: inline-block;
                // text-align: center;
                vertical-align: middle;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .rankText {
                margin-left: 20px;
              }
            }

            .bg_color {
              min-width: 21px;
              min-height: 21px;
              text-align: center;
              font-weight: bold;
              color: #fff;
              font-size: 14px;
            }
            .firstly1 {
              background-color: #f1b700;
            }
            .firstly2 {
              background-color: #eb981b;
            }
            .firstly3 {
              background-color: #ae5b0a;
            }
            .firstly4 {
              background-color: var(--color-primary);
            }

            .thirdly {
              overflow: hidden;
              color: var(--color-primary);
            }
            .color-sheng {
              color: #0e8f0e;
              font-size: 14px;
            }
            .color-jiang {
              color: #bc3c19;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
  .abnormal-title {
    margin-top: 10px;
    padding-right: 2px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--devider-line);
    .color-filter {
      color: rgba(43, 132, 226, 1);
      vertical-align: middle;
    }
  }
  .span-btn {
    cursor: pointer;
    color: var(--color-primary);
  }
}
</style>
