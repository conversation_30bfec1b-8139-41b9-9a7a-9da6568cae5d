<template>
  <div class="container">
    <div class="alarmCount" @click.stop="goAlarm()">
      <i class="iconfont icon-jingqing range"></i>
      {{ $route.query.alarmCount || alarmCount }}
    </div>
    <div class="detail">
      <div class="detail-name">
        <div style="display: flex">
          <!-- <div :class="taskInfo.taskStatus == 1 ? 'status' : 'status2'">
                        {{ taskInfo.taskStatus == 1 ? '执行中' : 
                            taskInfo.taskStatus == 2 ? '停用' : 
                            taskInfo.taskStatus == 3 ? '布控到期' : '已删除'
                        }}
                    </div> -->
          <div
            :class="
              [0, 1, 2].includes(taskInfo.taskStatus) ? 'status' : 'status2'
            "
          >
            {{
              taskInfo.taskStatus == 0
                ? "未提交"
                : taskInfo.taskStatus == 1
                ? "待审核"
                : taskInfo.taskStatus == 2
                ? "执行中"
                : taskInfo.taskStatus == 3
                ? "驳回"
                : taskInfo.taskStatus == 4
                ? "已删除"
                : "布控到期"
            }}
          </div>
          <span class="name">{{ taskInfo.taskName }}</span>
        </div>
        <div>
          <span>创建人：</span>
          <span style="font-weight: 600">{{ taskInfo.creator }}</span>
          <span style="margin-left: 20px">创建时间：</span>
          <span style="font-weight: 600">{{ taskInfo.createTime }}</span>
        </div>
      </div>

      <div class="detail-title">
        <div class="basic">基本信息</div>
      </div>
      <div>
        <span>时间类型：</span>
        <span v-if="taskInfo.taskTimeType == 0">永久</span>
        <span v-else style="font-weight: 600"
          >自定义：{{ taskInfo.taskTimeB }} — {{ taskInfo.taskTimeE }}</span
        >
        <span style="margin-left: 20px">有效时段：</span>
        <span
          v-if="
            !taskInfo.validTimeRangeVoList ||
            !taskInfo.validTimeRangeVoList.length
          "
          >全天</span
        >
        <template v-else>
          <span
            style="font-weight: 600"
            :style="{
              marginRight:
                index == taskInfo.validTimeRangeVoList.length - 1
                  ? '0'
                  : '10px',
            }"
            v-for="(item, index) in taskInfo.validTimeRangeVoList"
            :key="index"
            >{{ item.beginTime }}—{{ item.endTime }}</span
          >
        </template>
        <span style="margin-left: 20px">布控级别：</span>
        <span style="font-weight: 600" :class="'level' + taskInfo.taskLevel">{{
          taskInfo.taskLevel == 1
            ? "一级"
            : taskInfo.taskLevel == 2
            ? "二级"
            : "三级"
        }}</span>
      </div>

      <div class="detail-title" :class="{ flip: targetShow }">
        <div class="basic">布控目标</div>
        <div class="btn" @click="targetFn()">
          <i class="iconfont icon-doubleleft target"></i
          >{{ targetShow ? "收起" : "展开" }}
        </div>
      </div>
      <div class="default-content">
        <div :class="{ 'target-content': !targetShow }">
          <div>
            <span>布控类型：</span>
            <span style="font-weight: 600">{{
              taskInfo.taskType == 1 ? "单体布控" : "库布控"
            }}</span>
            <span style="margin-left: 20px">报警域值：</span>
            <span style="font-weight: 600">{{ taskInfo.alarmThreshold }}%</span>
          </div>

          <!-- 单体布控 -->
          <div class="card-content" v-if="taskInfo.taskType == 1">
            <div class="list">
              <taskCard
                class="alarnRow"
                v-for="item in monomerList"
                :index="index"
                :key="item.id"
                :taskInfo="item"
              />
              <!-- 空占位，保证数据居左 -->
              <div
                class="alarnRow"
                v-for="(item, index) of 4 - (monomerList.length % 4)"
                :key="index"
              ></div>
            </div>
          </div>
          <!-- 库布控 -->
          <div class="card-content" v-else>
            <div class="list">
              <taskCard
                class="alarnRow lib"
                v-for="item in libList"
                :taskInfo="item"
                :type="2"
                :key="item.id"
                @click.native="archivesItemHandle(item)"
              />
              <!-- 空占位，保证数据居左 -->
              <div
                class="alarnRow"
                v-for="(item, index) of 4 - (libList.length % 4)"
                :key="index"
              ></div>
            </div>
          </div>

          <!-- <ui-page 
                        :current="pageInfo1.pageNumber" 
                        :total="pageInfo1.total" 
                        :page-size="pageInfo1.pageSize" 
                        @pageChange="pageChange($event, 1)"
                        @pageSizeChange="pageSizeChange($event, 1)"
                        >
                        </ui-page> -->
        </div>
      </div>

      <div class="detail-title" :class="{ flip: rangeShow }">
        <div class="basic">
          布控范围 （{{ taskInfo.scopeType == 1 ? "所有" : "自定义" }}）
        </div>
        <div class="btn" @click="rangeShow = !rangeShow">
          <i class="iconfont icon-doubleleft range"></i
          >{{ rangeShow ? "收起" : "展开" }}
        </div>
      </div>
      <div :class="{ 'range-content': !rangeShow }">
        <ui-table :columns="columns1" :data="controlRangeList"></ui-table>
        <ui-page
          :current="pageInfo2.pageNumber"
          :total="pageInfo2.total"
          :page-size="pageInfo2.pageSize"
          @pageChange="pageChange($event, 2)"
          @pageSizeChange="pageSizeChange($event, 2)"
        >
        </ui-page>
      </div>

      <div class="detail-title" :class="{ flip: noticeShow }">
        <div class="basic">
          报警通知 （{{ taskInfo.userScopeType == 1 ? "所有" : "自定义" }}）
        </div>
        <div class="btn" @click="noticeShow = !noticeShow">
          <i class="iconfont icon-doubleleft notice"></i
          >{{ noticeShow ? "收起" : "展开" }}
        </div>
      </div>
      <div :class="{ 'notice-content': !noticeShow }">
        <ui-table :columns="columns2" :data="noticeList"></ui-table>
        <ui-page
          :current="pageInfo3.pageNumber"
          :total="pageInfo3.total"
          :page-size="pageInfo3.pageSize"
          @pageChange="pageChange($event, 3)"
          @pageSizeChange="pageSizeChange($event, 3)"
        >
        </ui-page>
      </div>
      <div class="detail-title" :class="{ flip: noticeShow }">
        <div class="basic">通知方式</div>
      </div>
      <div>
        <span class="short_note">短信通知人员：</span>
        <span>{{ shortNote.join(",") || "" }}</span>
      </div>
      <div class="detail-title" :class="{ flip: noticeShow }">
        <div class="basic">布控申请</div>
      </div>
      <div>
        <span class="short_note">申请理由：</span>
        <span>{{ taskInfo.requestReason || "" }}</span>
      </div>
      <div class="flex-r">
        <span class="short_note">申请书：</span>
        <div class="imgBox">
          <img
            v-if="taskInfo.requestAttachmentUrl"
            v-viewer
            :src="taskInfo.requestAttachmentUrl"
            alt
          />
          <img v-else src="@/assets/img/empty-page/null_img_icon.png" alt />
        </div>
      </div>
      <div
        v-permission="['apply-monitor']"
        class="detail-title"
        v-if="taskInfo.taskStatus == 1"
      >
        <div class="basic">审核意见</div>
      </div>
      <div v-permission="['apply-monitor']" v-if="taskInfo.taskStatus == 1">
        <Form
          ref="formValidate"
          :model="formData"
          :label-width="85"
          :rules="ruleValidate"
        >
          <FormItem label="审核意见:" prop="applyReason">
            <Input
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 3 }"
              v-model="formData.applyReason"
              placeholder="请输入"
              maxlength="30"
            ></Input>
          </FormItem>
        </Form>
      </div>
      <div
        v-permission="['apply-monitor']"
        class="btns"
        v-if="taskInfo.taskStatus == 1"
      >
        <Button type="primary" @click="handlePass">通过</Button>
        <Button class="margin" @click="handleReject">驳回</Button>
      </div>
    </div>
  </div>
</template>
<script>
import {
  taskView,
  userPageList,
  queryDeviceInfoPageList,
  queryDevicePageList,
  queryFaceLibPageList,
  queryFaceLibPersonPageList,
  taskApply,
} from "@/api/target-control";
import { mapActions, mapGetters } from "vuex";
import taskCard from "./components/task-card-detail.vue";
export default {
  components: { taskCard },
  data() {
    return {
      taskInfo: {},
      formData: {
        applyReason: "",
      },
      columns1: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "设备名称", key: "name" },
        { title: "设备编码", key: "gbId" },
        { title: "安装地址", key: "place" },
      ],
      noticeList: [], // 报警通知
      controlRangeList: [], // 布控范围
      monomerList: [], // 单体布控
      libList: [], // 库布控
      columns2: [
        { type: "index", title: "序号", width: 68, align: "center" },
        { title: "姓名", key: "name" },
        { title: "用户名", key: "username" },
        { title: "角色", key: "roleNames" },
      ],
      tableData2: [],
      pageInfo1: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      pageInfo2: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      pageInfo3: {
        pageNumber: 1,
        pageSize: 20,
        total: 0,
      },
      targetShow: true,
      rangeShow: true,
      noticeShow: true,
      libIds: [],
      deviceIds: [],
      userIds: [],
      ruleValidate: {
        applyReason: [
          { required: true, message: "请输入审核意见", trigger: "blur" },
        ],
      },
      shortNote: [],
    };
  },
  props: {
    alarmCount: {
      type: [Number, String],
      default: "",
    },
    taskId: {
      type: [Number, String],
      default: "",
    },
    compareType: {
      type: [String, Number],
      default: () => "",
    },
  },
  computed: {},
  watch: {
    "$route.query"(val) {
      if (this.$route.name == "control-task-detail" && val.id) this.init();
    },
    taskId(val) {
      if (val) this.init();
    },
  },
  created() {
    this.getDictData();
  },
  activated() {},
  mounted() {
    // console.log('---', this.$route.query)
    this.init();
  },
  methods: {
    ...mapActions({
      getDictData: "dictionary/getDictAllData",
    }),
    // 通过
    handlePass() {
      this.$refs["formValidate"].validate((valid) => {
        if (valid) {
          this.passOrReject(2);
        }
      });
    },
    // 驳回
    handleReject() {
      this.$refs["formValidate"].validate((valid) => {
        if (valid) {
          this.passOrReject(3);
        }
      });
    },
    passOrReject(index) {
      let params = {
        taskId: this.$route.query.id || this.taskId,
        applyReason: this.formData.applyReason,
        checkUser: this.userInfo.username,
        taskStatus: index,
      };
      taskApply(params).then((res) => {
        this.$router.push({
          name: "control-task",
          query: { compareType: this.compareType },
        });
      });
    },
    init() {
      taskView(this.$route.query.id || this.taskId).then((res) => {
        this.taskInfo = res.data;
        this.libIds = [];
        this.userIds = [];
        this.deviceIds = [];
        this.taskInfo.libVoList.forEach((item) => {
          this.libIds.push(item.libId);
        });
        this.taskInfo.deviceVoList.forEach((item) => {
          this.deviceIds.push(item.deviceId);
        });
        this.taskInfo.userVoList.forEach((item) => {
          this.userIds.push(item.userId);
        });
        res.data.smsVoList.forEach((item) => {
          this.shortNote.push(item.phone);
        });
        if (this.taskInfo.taskType == 1) {
          // 单体布控
          this.monomerFn();
        } else {
          // 库布控
          this.libFn();
        }
        this.rangeFn();
        this.noticeFn();
      });
    },
    // 单体布控
    monomerFn() {
      var params = {
        faceLibId: this.libIds[0],
      };
      queryFaceLibPersonPageList(params).then((res) => {
        this.monomerList = res.data.entities || [];
        this.pageInfo1.total = res.data.total;
      });
    },
    // 库布控
    libFn() {
      var params = {
        ids: this.libIds,
      };
      queryFaceLibPageList(params).then((res) => {
        this.libList = res.data.entities;
        this.pageInfo1.total = res.data.total;
      });
    },
    // 布控范围
    rangeFn() {
      var params = {
        deviceIds: this.deviceIds,
        pageNumber: this.pageInfo2.pageNumber,
        pageSize: this.pageInfo2.pageSize,
      };
      queryDevicePageList(params).then((res) => {
        this.controlRangeList = res.data.entities;
        this.pageInfo2.total = res.data.total;

        // this.pageInfo2.total = 200
        // this.controlRangeList = []
        // for(var i = 0; i < 10; i++){
        //   this.controlRangeList = [...this.controlRangeList, ...res.data.entities]
        // }
      });
    },
    // 报警通知
    noticeFn() {
      this.noticeList = [];
      var params = {
        userIds: this.userIds,
        params: {
          pageNumber: this.pageInfo3.pageNumber,
          pageSize: this.pageInfo3.pageSize,
        },
      };
      userPageList(params).then((res) => {
        this.noticeList = res.data.entities;
        this.pageInfo3.total = res.data.total;
      });
    },
    targetFn() {
      this.targetShow = !this.targetShow;
    },
    // 改变页码
    pageChange(pageNumber, num) {
      this["pageInfo" + num].pageNumber = pageNumber;
      if (num == 1) {
        if (this.taskInfo.taskType == 1) {
          this.monomerFn();
        } else {
          this.libFn();
        }
      }

      if (num == 2) this.rangeFn();
      if (num == 3) this.noticeFn();
    },
    // 改变分页个数
    pageSizeChange(size, num) {
      this["pageInfo" + num].pageSize = size;
      this["pageInfo" + num].pageNumber = 1;
      if (num == 1) {
        if (this.taskInfo.taskType == 1) {
          this.monomerFn();
        } else {
          this.libFn();
        }
      }

      if (num == 2) this.rangeFn();
      if (num == 3) this.noticeFn();
    },
    goAlarm() {
      this.$router.push({
        name: "juvenile-alarm-manager",
        query: {
          taskId: this.taskInfo.taskId,
          // 多维布控，在报警中没有对应的tab，直接跳到人员报警
          compareType: this.compareType,
        },
      });
    },
    // 静态库详情
    archivesItemHandle(item) {
      if (item.libType === "1") {
        // 人员
        const { href } = this.$router.resolve({
          name: "juvenile-personnel-thematic-database",
          query: {
            archiveStatus: item.archiveStatus,
            featureLibId: item.featureLibId,
            id: item.id,
            libCount: item.libCount,
            libName: item.libName,
            libSource: item.libSource,
            libType: item.libType,
            modifyTime: item.modifyTime,
          },
        });
        window.open(href, "_blank");
      }
    },
  },
};
</script>
<style lang="less" scoped>
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow: auto;

  .alarmCount {
    position: absolute;
    right: 10px;
    padding: 5px 20px;
    background: linear-gradient(-90deg, #ea4a36 0%, #ff7d56 100%);
    line-height: 22px;
    font-size: 16px;
    color: #fff;
    margin-right: -10px;
    border-bottom-left-radius: 50px;
    border-top-left-radius: 50px;
    margin-top: 16px;
    cursor: pointer;
    .collection-icon {
      margin-right: 6px;
    }
  }
  .detail-name {
    display: flex;
    justify-content: space-between;
    height: 30px;
    align-items: center;
    margin-bottom: 20px;
    .status {
      display: initial;
      background: #2c86f8;
      height: 24px;
      line-height: 24px;
      margin-top: 5px;
      padding: 0px 10px;
      color: #fff;
      border-radius: 5px;
      font-weight: 600;
    }
    .status2 {
      display: initial;
      background: #dfe0e1;
      color: #666;
      border-radius: 5px;
      height: 24px;
      line-height: 24px;
      margin-top: 5px;
      padding: 0px 10px;
    }
    .name {
      font-weight: 600;
      font-size: 20px;
      margin-left: 10px;
    }
  }

  .detail {
    width: 80%;
    margin: 0 auto;
  }

  .detail-title {
    position: relative;
    // height: 22px;
    line-height: 22px;
    padding: 3px 0;
    padding-left: 10px;
    margin: 20px 0 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    background: linear-gradient(
      to right,
      rgba(233, 243, 255, 1),
      rgba(233, 243, 255, 0)
    );
    .basic {
      font-size: 14px;
    }

    .btn {
      font-weight: 300;
      color: #2c86f8;
      cursor: pointer;
    }
    .icon-doubleleft {
      display: inline-block;
      transform: rotate(-90deg);
      transition: transform 0.2s;
    }
  }
  .short_note {
    font-size: 14px;
    font-weight: bold;
  }
  .level1 {
    color: #ea4a36;
  }
  .level2 {
    color: #fc770b;
  }
  .level3 {
    color: #ffc300;
  }
  .detail-title::after {
    content: "";
    width: 3px;
    height: 22px;
    background: #2c86f8;
    position: absolute;
    left: 0;
  }
  .imgBox {
    height: 100px;
    width: 100px;
    img {
      max-height: 100%;
      max-width: 100%;
    }
  }
  .flex-r {
    display: flex;
  }
}

.card-content {
  display: flex;
  flex-wrap: wrap;
  overflow: auto;
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  margin: 0 -0.02604rem;
  align-content: flex-start;
  position: relative;
  // margin-top: 10px;
  .list {
    flex: 1;
    display: flex;
    /* flex-direction: row; */
    flex-flow: wrap;
    justify-content: space-between;
    .alarnRow {
      // width: 20%;
      // width: calc( 20% - 10px);
      width: ~"calc(25% - 10px)";
      margin-top: 12px;
      &.lib {
        cursor: pointer;
      }
    }
  }
}

.flip {
  .icon-doubleleft.target {
    transform: rotate(90deg);
    transition: transform 0.2s;
  }
  .icon-doubleleft.range {
    transform: rotate(90deg);
    transition: transform 0.2s;
  }
  .icon-doubleleft.notice {
    transform: rotate(90deg);
    transition: transform 0.2s;
  }
}

.target-content {
  height: auto;
  transition: 3s;
}
.range-content {
  height: auto;
  transition: 3s;
}
.notice-content {
  height: auto;
  transition: 3s;
}

.target-content {
  height: 0px;
  transition: 0.7s;
  overflow: hidden;
}
.range-content {
  height: 0px;
  transition: 0.7s;
  overflow: hidden;
}
.notice-content {
  height: 0px;
  transition: 0.7s;
  overflow: hidden;
}

/deep/ .ivu-table-body,
/deep/ .ivu-table-wrapper {
  height: auto !important;
}
.btns {
  text-align: center;
  padding-bottom: 10px;
  /deep/ .ivu-btn {
    margin: 0 6px;
  }
}
</style>
