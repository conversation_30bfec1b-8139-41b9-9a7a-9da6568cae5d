<template>
  <div class="important-data">
    <p class="select-box">
      <ui-label class="ml-lg" label="治理流程" :width="70">
        <Select v-model="process" :clearable="false" placeholder="请选择" class="input-width" @on-change="changeTopic">
          <Option :value="item.id" v-for="(item, index) of subThemeData" :key="index">
            {{ item.topicName }}
          </Option>
        </Select>
      </ui-label>
    </p>
    <div v-for="(item, index) in aggregateOptions" :key="index">
      <aggre-connect :propData="item" dWidth="14%" bWidth="14%"></aggre-connect>
    </div>
    <!-- 图片上传及时性检测、人员轨迹准确性检测优化 -->
    <warehouse-popup ref="warehousePopup"></warehouse-popup>
    <!-- 数据输入 -->
    <dataaccess-popup ref="dataaccessPopup" :tag-type-list="tagTypeList"> </dataaccess-popup>
    <!-- 字段映射、字典映射、正式入库 -->
    <rings-popup ref="ringsPopupRef" :tag-type-list="tagTypeList"> </rings-popup>
    <!-- 空值检测、重复检测、数据输出（基础） -->
    <ring-column-popup ref="columnRingRef"> </ring-column-popup>
    <!-- 数据输出（轨迹） -->
    <export-data-popup ref="exportDataPopup"> </export-data-popup>
    <div class="viewLoading">
      <loading v-if="viewLoading"></loading>
    </div>
  </div>
</template>
<script>
import importantEnum from './util/enum';
import tasktracking from '@/config/api/tasktracking';
import category from '@/config/api/catalogmanagement';
export default {
  name: 'importantData',
  props: {
    id: {
      type: Number,
    },
  },
  data() {
    return {
      styles: {
        width: '75%',
      },
      trailDecidePopupShow: false,
      aggregateOptions: importantEnum.aggregateOptions,
      viewLoading: false,
      tagTypeList: [],
      process: 1,
      subThemeData: [
        { topicName: '重点人基础信息', id: 1 },
        { topicName: '重点人轨迹', id: 2 },
      ],
    };
  },
  created() {
    this.getViewList();
    this.initDicContentFunc();
  },
  methods: {
    /* ----------------------------------------- 绑定方法 ----------------------------------------- */
    // showTrailDecide() {
    // 	this.trailDecidePopupShow = true;
    // },
    handleData(aggregateList) {
      if (this.process === 1) {
        this.aggregateOptions = this.$util.common.deepCopy(importantEnum.aggregateOptions);
      } else {
        this.aggregateOptions = this.$util.common.deepCopy(importantEnum.faceAggregateOptions);
      }

      // 面板数据拍平
      let allAggregateDatas = [];
      this.aggregateOptions.forEach((item) => {
        allAggregateDatas.push(...item.datas);
      });
      // 面板datas数据添加filedData字段（后端返回的所有数据）
      aggregateList.forEach((element, index) => {
        let isOne = allAggregateDatas[index];
        isOne.filedData = element;
        isOne.title = element.componentName;
        let staticListData = isOne.filedData.topicComponentStatistics;
        if (!staticListData || !staticListData.accessDataCount) return;
        // 成功数据需要前端计算
        isOne.list.forEach((item) => {
          if (item.fileName) {
            item.num = staticListData[item.fileName];
          }
          item.num = staticListData[item.fileName];
          if (item.fileName === 'successData') {
            item.num = staticListData.accessDataCount - staticListData.existingExceptionCount;
          }
          if (item.fileName === 'successDataRate') {
            item.num =
              ((staticListData.accessDataCount - staticListData.existingExceptionCount) /
                staticListData.accessDataCount) *
              100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
          if (item.fileName === 'existingExceptionRate') {
            item.num = item.num * 100;
            parseInt(item.num) === parseFloat(item.num)
              ? (item.num = item.num + '%')
              : (item.num = item.num.toFixed(2) + '%');
          }
        });
      });
    },
    async getViewList() {
      try {
        this.viewLoading = true;
        let { data } = await this.$http.get(tasktracking.viewImportantPersonTask, {
          params: { id: this.id, type: this.process },
        });
        this.handleData(data.data.componentList);
      } catch (err) {
        console.log(err);
      } finally {
        this.viewLoading = false;
      }
    },
    // 获取重点人员目录所有标签类型
    async initDicContentFunc() {
      try {
        const params = {
          isPage: false,
          tagCategory: '2',
        };
        let { data } = await this.$http.post(category.getAllDeviceTagByCondition, params);
        this.tagTypeList = data.data;
      } catch (err) {
        console.log(err);
      }
    },
    changeTopic() {
      this.getViewList();
    },
  },
  watch: {},
  components: {
    AggreConnect: require('../../governancetheme/components/aggre-connect.vue').default,
    warehousePopup: require('./popup/warehouse-popup.vue').default,
    dataaccessPopup: require('./popup/dataaccess-popup.vue').default,
    RingsPopup: require('./popup/rings-popup.vue').default,
    RingColumnPopup: require('./popup/ring-column-popup.vue').default,
    ExportDataPopup: require('./popup/export-data-popup.vue').default,
  },
};
</script>
<style lang="less" scoped>
.important-data {
  position: relative;
}
.select-box {
  position: absolute;
  top: 20px;
  .input-width {
    width: 200px;
  }
}
</style>
