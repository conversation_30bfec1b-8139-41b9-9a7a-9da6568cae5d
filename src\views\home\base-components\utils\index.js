import {
  longEchartPosition,
  normalEchartPosition,
  baseHomePosition,
  defaultComponentList,
} from '@/views/home/<USER>/utils/componentConfig';
import * as componentConfigStyle from '@/views/home/<USER>/utils/componentConfigStyle.js';

const styles = {
  style1: {
    longEchartPosition,
    normalEchartPosition,
    baseHomePosition,
    defaultComponentList,
  },
  style2: {
    longEchartPosition: componentConfigStyle.longEchartPosition,
    normalEchartPosition: componentConfigStyle.normalEchartPosition,
    baseHomePosition: componentConfigStyle.baseHomePosition,
    defaultComponentList: defaultComponentList,
  },
};

/**
 * 获取风格配置文件
 * @param style style1 or style2
 * @return {Object}
 */
export function getComponentConfig(style) {
  return styles[style];
}
