<template>
  <span :class="{ 'i-layout-header-trigger-min': showReload }" class="i-layout-header-trigger" @click="handleToggleMenuSide">
    <i v-show="!menuCollapse || isMobile" class="iconfont icon-indent"></i>
    <i v-show="menuCollapse && !isMobile" class="iconfont icon-outdent"></i>
  </span>
</template>
<script>
import { mapState, mapMutations, mapGetters } from 'vuex'

export default {
  name: `iHeaderCollapse`,
  computed: {
    ...mapState('admin/layout', [
      'isMobile',
      'isTablet',
      'isDesktop',
    //   'menuCollapse',
      'showReload'
    ]),
    ...mapGetters({
        menuCollapse: "menuCollapse", 
    }),
  },
  methods: {
    // ...mapMutations('admin/layout', [
    //   'updateMenuCollapse'
    // ]),
    ...mapMutations(['updateMenuCollapse']),
    // 展开/收起侧边栏
    handleToggleMenuSide (state) {
      if (this.isMobile) {
        this.updateMenuCollapse(false)
        this.$emit('on-toggle-drawer', state)
      } else {
        this.updateMenuCollapse(!this.menuCollapse)
      }
    }
  }
}
</script>
