<template>
  <div class="layer-management">
    <el-popover
      popper-class="layer-popover"
      visible-arrow="false"
      placement="bottom-end"
      v-model="layerVisible"
      trigger="click"
    >
      <div class="layer-title">图层管理</div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-equipmentlibrary"></i>
            </span>
            <span>视频监控</span>
          </div>
          <i-switch size="small" @on-change="(val) => layerCondition(val, 'sbgnlx', '5')" />
        </div>
      </div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-renliankakou"></i>
            </span>
            <span>人脸卡口</span>
            <span class="count">
              (<span>{{ allCameraList.filter((item) => item.sbgnlx.indexOf('2') !== -1).length }}</span
              >)
            </span>
          </div>
          <i-switch size="small" @on-change="(val) => layerCondition(val, 'sbgnlx', '2')" />
        </div>
      </div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-cheliangshitushuju"></i>
            </span>
            <span>车辆卡口</span>
            <span class="count">
              (<span>{{ allCameraList.filter((item) => item.sbgnlx.indexOf('1') !== -1).length }}</span
              >)
            </span>
          </div>
          <i-switch size="small" @on-change="(val) => layerCondition(val, 'sbgnlx', '1')" />
        </div>
      </div>
      <Divider></Divider>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-yileidian"></i>
            </span>
            <span>一类点</span>
          </div>
          <i-switch size="small" true-value="1" @on-change="(val) => layerCondition(val, 'sbdwlx', '1')" />
        </div>
      </div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-ersanleidian"></i>
            </span>
            <span>二三类点</span>
          </div>
          <i-switch size="small" @on-change="(val) => layerCondition(val, 'sbdwlx', ['2', '3'])" />
        </div>
      </div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-gonganneibujiankongdian"></i>
            </span>
            <span>公安内部监控点</span>
          </div>
          <i-switch size="small" true-value="4" @on-change="(val) => layerCondition(val, 'sbdwlx', '4')" />
        </div>
      </div>
      <Divider></Divider>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-kongjianxinxihegedianwei"></i>
            </span>
            <span>空间信息合格点位</span>
          </div>
          <i-switch size="small" @on-change="(val) => layerCondition(val, 'checkStatus', ['0000', '1000'])" />
        </div>
      </div>
      <div class="device-type">
        <div class="device-type-item">
          <div>
            <span class="type-bg">
              <i class="icon-font icon-kongjianxinxiyichangdianwei"></i>
            </span>
            <span>空间信息异常点位</span>
          </div>
          <i-switch
            size="small"
            @on-change="
              (val) => layerCondition(val, 'checkStatus', ['0100', '0001', '0010', '0011', '0101', '0110', '0111'])
            "
          />
        </div>
      </div>
      <div class="form-content"></div>
      <div slot="reference" :class="['base-text-color', layerVisible ? 'color-active' : '']">
        <!-- :class="[layerVisible ? 'layer-content-active' : '', 'layer-content']" -->
        <i class="icon-font icon-tucengguanli mr-sm f-14"></i>
        <span class="f-14 pointer">图层管理</span>
      </div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'layer-management',
  props: {
    allCameraList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      layerVisible: false,
      layerParams: {
        checkStatus: [],
        sbdwlx: [],
        sbgnlx: [],
      },
      devData: [],
    };
  },
  methods: {
    layerCondition(val, type, typeVal) {
      if (val) {
        if (Array.isArray(typeVal)) {
          typeVal.forEach((item) => {
            !this.layerParams[type].includes(item) ? this.layerParams[type].push(item) : '';
          });
        } else {
          !this.layerParams[type].includes(typeVal) ? this.layerParams[type].push(typeVal) : '';
        }
      } else {
        let typeValIndex;
        if (Array.isArray(typeVal)) {
          typeVal.forEach((item) => {
            typeValIndex = this.layerParams[type].indexOf(item);
            this.layerParams[type].splice(typeValIndex, 1);
          });
        } else {
          typeValIndex = this.layerParams[type].indexOf(typeVal);
          this.layerParams[type].splice(typeValIndex, 1);
        }
      }
      const fillerList = this.multiFilter(this.allCameraList, this.layerParams);
      this.layersFilter(fillerList);
    },
    // 图层过滤
    layersFilter(filterCameraList) {
      this.$emit('handleDevLayer', filterCameraList);
    },
    multiFilter(array, filters) {
      const filterKeys = Object.keys(filters);
      return array.filter((item) => {
        return filterKeys.every((key) => {
          if (!filters[key].length) {
            return true;
          } else if (item[key] && item[key].includes('/')) {
            return item[key].split('/').toString().includes(filters[key]);
          } else {
            return !!~filters[key].indexOf(item[key]);
          }
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.layer-management {
  // position: absolute;
  // top: 74px;
  // right: 30px !important;
  // z-index: 1000;
  .layer-content {
    position: relative;
    width: 38px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('~@/assets/img/device-map/layer.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .icon-font {
      color: #f5f5f5;
      font-size: 16px;
    }
  }
  .layer-content:hover,
  .layer-content-active {
    outline-style: none !important;
    background-image: url('~@/assets/img/device-map/layer-active.png') !important;
  }
}
</style>
<style lang="less">
[data-theme='dark'] {
  .layer-popover {
    background: #041d42;
    border: 1px solid #2967c8;
    .layer-title {
      border-bottom: 1px solid #1d4582;
    }
    .ivu-divider {
      background: #1d4582;
    }
    .device-type {
      .type-bg {
        background-image: url('~@/assets/img/device-map/layer-icon.png');
      }
    }
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .layer-popover {
    background: #ffffff;
    box-shadow: 0px 0px 12px 0px rgba(0, 21, 41, 0.15);
    .layer-title {
      border-bottom: 1px solid #d8d8d8;
    }
    .ivu-divider {
      background: #d8d8d8;
    }
    .device-type {
      .type-bg {
        width: 32px !important;
        height: 32px !important;
        background-image: url('~@/assets/img/device-map/layer-icon-light.png') !important;
      }
    }
  }
}
.layer-popover {
  margin-top: 0 !important;
  position: absolute !important;
  // top: 285px !important;
  top: 240px !important;
  z-index: 9 !important;
  width: 282px !important;
  padding: 0 !important;
  padding-bottom: 10px !important;
  color: var(--color-content);
  outline-style: none;
  .popper__arrow {
    display: none !important;
  }
  &__reference-wrapper {
    outline-style: none !important;
  }
  .layer-title {
    height: 44px;
    line-height: 44px;
    font-weight: bold;
    padding-left: 23px;
  }
  .device-type {
    padding: 11px 13px 4px 23px;
    .device-type-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > div {
        display: flex;
        align-items: center;
        .type-bg {
          display: inline-block;
          width: 34px;
          height: 34px;
          margin-right: 10px;
          background-size: 100% 100%;
          line-height: 34px;
          text-align: center;
          .icon-font {
            background-image: -webkit-linear-gradient(180deg, #5d9be2 0%, #1575f3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 14px !important;
          }
        }
        .count {
          font-weight: bold;
          color: rgb(187, 112, 57);
          margin-left: 10px;
        }
      }
    }
  }
  .ivu-divider {
    margin: 8px 0;
  }
}
</style>
