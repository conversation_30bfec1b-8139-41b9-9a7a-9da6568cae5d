<template>
  <div class="video-streaming auto-fill height-full">
    <div class="search-module">
      <div class="mb-lg">
        <ui-label class="inline" label="组织机构" :width="70">
          <api-organization-tree
            :select-tree="selectOrgTree"
            @selectedTree="selectedOrgTree"
            placeholder="请选择组织机构"
          >
          </api-organization-tree>
        </ui-label>
        <ui-label class="inline ml-lg" label="关键词" :width="55">
          <Input
            v-model="searchData.searchValue"
            class="keyword-input"
            placeholder="请输入设备名称/设备编码/经纬度/安装地址"
          ></Input>
        </ui-label>
        <!-- <ui-label class="inline ml-lg" label="数据来源" :width="70">
                    <Input v-model="searchData.sourceId" class="width-md"></Input>
                </ui-label> -->
        <div class="inline ml-lg">
          <Button type="primary" @click="search">查询</Button>
          <Button class="ml-sm" @click="clear">重置</Button>
        </div>
      </div>
      <ui-select-tabs :list="errorList" :need-expand="false" @selectInfo="selectInfo"></ui-select-tabs>
    </div>
    <div class="table-module auto-fill">
      <ui-table class="ui-table auto-fill" :table-columns="tableColumns" :table-data="tableData" :loading="loading">
        <template #deviceId="{ row }">
          <div
            :class="row.rowClass"
            class="ellipsis font-active-color pointer"
            :title="row.deviceId"
            @click="deviceArchives(row)"
          >
            {{ row.deviceId }}
          </div>
        </template>
        <!-- 经纬度保留8位小数-->
        <template #longitude="{ row }">
          <span>{{ row.longitude | filterLngLat }}</span>
        </template>
        <template #latitude="{ row }">
          <span>{{ row.latitude | filterLngLat }}</span>
        </template>
        <template #action="{ row }">
          <ui-btn-tip
            icon="icon-chakanyichangxiangqing"
            content="不合格原因"
            @click.native="viewRecord(row)"
          ></ui-btn-tip>
        </template>
      </ui-table>
      <ui-page class="page" :page-data="pageData" @changePage="changePage" @changePageSize="changePageSize"> </ui-page>
    </div>
    <video-error-detail v-model="recordShow" ref="errorDetail" :viewData="recordData"></video-error-detail>
  </div>
</template>
<script>
import api from '@/config/api/vedio-threm.js';
import user from '@/config/api/user.js';

export default {
  props: {},
  data() {
    return {
      loading: false,
      errorList: [],
      selectOrgTree: {
        orgCode: null,
      },
      searchData: {
        orgCode: null,
        keyWord: '',
        searchValue: '',
        sourceIds: [],
        reasonTypes: [],
        errorMessages: [],
        pageNumber: 1,
        pageSize: 20,
        params: {
          pageNumber: 1,
          pageSize: 20,
        },
      },
      pageData: {
        pageNum: 1,
        pageSize: 20,
        totalCount: 0,
      },
      recordShow: false,
      tableData: [],
      tableColumns: [
        { title: '序号', width: 50, align: 'center', type: 'index' },
        {
          title: `${this.global.filedEnum.deviceId}`,
          key: 'deviceId',
          width: 250,
          slot: 'deviceId',
          align: 'left',
          tooltip: true,
        },
        {
          title: `${this.global.filedEnum.deviceName}`,
          key: 'deviceName',
          width: 200,
          align: 'left',
          tooltip: true,
        },
        { title: '组织机构', key: 'orgName', width: 120, align: 'left', tooltip: true },
        {
          title: `${this.global.filedEnum.longitude}`,
          key: 'longitude',
          slot: 'longitude',
          width: 100,
          tooltip: true,
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.latitude}`,
          key: 'latitude',
          slot: 'latitude',
          width: 100,
          tooltip: true,
          align: 'left',
        },
        {
          title: `${this.global.filedEnum.macAddr}`,
          width: 150,
          key: 'macAddr',
          align: 'left',
          tooltip: true,
        },
        {
          title: this.global.filedEnum.ipAddr,
          width: 150,
          key: 'ipAddr',
          align: 'left',
          tooltip: true,
        },
        {
          title: '设备安装地址',
          width: 300,
          key: 'address',
          align: 'left',
          tooltip: true,
        },
        // { title: "异常原因", key: "latitude", align: "left"},
        {
          title: '操作',
          width: 60,
          slot: 'action',
          align: 'center',
          fixed: 'right',
          className: 'table-action-padding',
        },
      ],
      recordData: {},
    };
  },
  created() {
    this.queryErrorList();
    this.init();
  },
  methods: {
    selectedOrgTree(val) {
      this.searchData.orgCode = val.orgCode;
    },
    selectInfo(infoList) {
      this.searchData.reasonTypes = infoList.map((row) => {
        return row.dataKey;
      });
      this.search();
    },
    init() {
      // this.searchData.reasonTypes = [6,7,8];
      this.loading = true;
      this.$http
        .post(api.queryVideoResultPageList, this.searchData)
        .then((res) => {
          if (res.data.code == 200) {
            this.tableData = res.data.data.entities;
            this.pageData.totalCount = res.data.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
      // this.copySearchDataMx(this.searchData);
    },
    queryErrorList() {
      this.$http.get(user.queryByTypeKey + '?typekey=video_reason_type').then((res) => {
        if (res.data.code == 200) {
          console.log(res);
          var list = res.data.data;
          list.forEach((item) => {
            item.name = item.dataValue;
          });

          this.errorList = list;
        }
      });
    },
    clear() {
      this.searchData.orgCode = null;
      this.searchData.searchValue = null;
      this.searchData.sourceId = null;
      this.resetSearchDataMx(this.searchData, this.search);
    },
    search() {
      this.searchData.pageNumber = 1;
      this.init();
    },
    changePage(val) {
      this.searchData.params.pageNumber = val;
      // this.searchData.params.pageNumber = 1;
      // this.searchData.params.pageSize = 20;
      this.searchData.pageNumber = val;
      this.init();
    },
    changePageSize(val) {
      this.searchData.params.pageNumber = 1;
      this.searchData.params.pageSize = val;
      this.searchData.pageSize = val;
      this.search();
    },
    getVedioPage(id) {
      this.searchData.reasonTypes = [id];
      this.$http.post(api.queryVideoResultPageList, this.searchData).then((res) => {
        if (res.data.code == 200) {
          this.tableData = res.data.data.entities;
          this.pageData.totalCount = res.data.data.total;
        }
      });
    },
    viewRecord(row) {
      console.log(111);
      this.recordShow = true;
      this.recordData = row;
      this.$refs.errorDetail.setSearchData(this.searchData);
    },
    deviceArchives(item) {
      let routeData = this.$router.resolve({
        name: 'archives',
        query: { id: item.id },
      });
      window.open(routeData.href, '_blank');
    },
  },
  watch: {},
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    videoErrorDetail: require('./video-error-detail.vue').default,
    UiSelectTabs: require('@/components/ui-select-tabs.vue').default,

    ApiOrganizationTree: require('@/api-components/api-organization-tree.vue').default,
  },
};
</script>
<style lang="less" scoped>
.search-module {
  padding: 20px 20px;
  .keyword-input {
    width: 300px;
  }
}
.ui-table {
  padding: 0 20px;
}
</style>
