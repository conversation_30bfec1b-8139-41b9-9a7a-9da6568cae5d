<template>
  <div v-loading="tabLoading">
    <template v-if="governanceContentData.length > 0">
      <div class="governanceautomatic auto-fill" v-if="!componentName">
        <underline-menu
          v-model="governanceContent"
          :data="governanceContentData"
          @on-change="changeMenu"
          class="mb-sm"
        ></underline-menu>
        <component class="com-box-h" :is="activeTab" v-if="activeTab"></component>
        <task-view
          v-else
          :source-type="sourceType"
          :governance-content="governanceContent"
          :dicts-data="dicts"
          ref="taskView"
        ></task-view>
      </div>
    </template>
    <template v-if="governanceContentData.length === 0 && !tabLoading">
      <div class="no-data no-data-box-div">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
        <div class="null-data-text">
          暂无数据，请前往 <span class="link-text-box pointer" @click="openPage">治理项管理</span> 页面进行配置!
        </div>
      </div>
    </template>
    <zdr-result v-model="zdrResultShow"></zdr-result>
    <component v-if="!!componentName" :is="componentName"></component>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
import governanceautomatic from '@/config/api/governanceautomatic';
import { mapActions } from 'vuex';
export default {
  name: 'governanceautomatic',
  props: {},
  mixins: [dealWatch],
  data() {
    return {
      tabLoading: true,
      activeTab: '',
      componentName: '',
      componentLevel: 0,
      zdrResultShow: false,
      dicts: {
        taskStatusDict: [],
        sourceTypeDict: [],
      },
      governanceContent: null,
      sourceType: null,
      governanceContentData: [],
      isShowNewTask: false, // 是否需要 拉起 新增治理任务弹框
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (Object.keys(to.params).length) {
        vm.governanceContent = to.params.governanceContent;
        vm.sourceType = to.params.sourceType;
        vm.isShowNewTask = true;
      }
    });
  },
  async activated() {
    // 知识库 - 问题成因分析  or 治理项管理页面  跳转过来
    if ('tab' in this.$route.query) {
      this.governanceContent = this.$route.query.tab;
      this.setActiveTab(this.$route.query.tab);
    }
    this.startWatch('$route', () => {
      this.getParams();
    });
    this.dicts.taskStatusDict = await this.getDictData('getTaskStatusList'); // 获取任务状态
    this.dicts.sourceTypeDict = await this.getDictData('getSourceTypeList'); // 获取资源类型
    await this.getGovernanceContent();
  },
  methods: {
    ...mapActions({
      getTabListByApi: 'governanceconfig/getTabListByApi',
    }),
    changeMenu(val) {
      if (val === '3') {
        this.sourceType = '3';
      } else {
        this.sourceType = '0';
      }
      this.setActiveTab(val);
    },
    async getGovernanceContentBysourceType(code) {
      let {
        data: { data },
      } = await this.$http.get(governanceautomatic.getGovernanceContentBysourceType, {
        params: { sourceType: code }, //默认资源类型：视图基础数据
      });
      return data || [];
    },
    async getGovernanceContent() {
      // 从治理项管理配置中获取 -- 与基线版本 是有差别的
      try {
        this.tabLoading = true;
        let arr = await this.getTabListByApi();
        // 未启用 则不显示
        this.governanceContentData = arr.filter((item) => item.status);
      } catch (e) {
        console.log(e);
      } finally {
        // 跳转进来，可能配置为不显示，需要处理下
        if (this.governanceContentData.length === 0) {
          this.governanceContent = '';
        } else {
          let flag = this.governanceContentData.some((item) => item.code === this.governanceContent);
          if (!flag) {
            this.governanceContent = this.governanceContentData[0].code;
            this.setActiveTab(this.governanceContentData[0].code);
          } else {
            let selectItem = this.governanceContentData.filter((item) => item.code === this.governanceContent);
            this.setActiveTab(selectItem[0]?.code);
          }

          // 其他页面跳转到对应的视图治理项，并拉起【新增治理任务】弹框
          if (this.isShowNewTask) {
            this.isShowNewTask = false;
            this.$nextTick(() => {
              this.$refs.taskView.newTask();
            });
          }
        }
        this.tabLoading = false;
      }
    },
    /** 13、14 特殊处理
     *  { code: '13', label: '区域管理' },
     *  { code: '14', label: '空间信息治理' },
     *  { code: '15', label: '图片数据治理' },
     * @param val code
     */
    setActiveTab(val) {
      switch (val) {
        case '13':
          this.activeTab = 'PlaceManagement';
          break;
        case '14':
          this.activeTab = 'SpaceInformation';
          break;
        case '15':
          this.activeTab = 'ImageDataGovernance';
          break;
        case '16':
          this.activeTab = 'FaceDataGovernance';
          break;
        case '17':
          this.activeTab = 'VehicleDataGovernance';
          break;
        case '18':
          this.activeTab = 'ZDRDataGovernance';
          break;
        case '19':
          this.activeTab = 'VehicleFlowDataGovernance';
          break;
        default: {
          this.activeTab = null;
          return;
        }
      }
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    // 获取搜索条件字典
    async getDictData(type) {
      try {
        let res = await this.$http.get(governanceautomatic[type]);
        return res.data.data;
      } catch (error) {
        console.log(error);
      }
    },
    openPage() {
      this.$router.push({ path: '/systemconfiguration/governanceconfig' });
    },
  },
  watch: {},
  components: {
    UnderlineMenu: require('@/components/underline-menu').default,
    UiTable: require('@/components/ui-table.vue').default,
    ZdrResult: require('./result/zdr-result.vue').default,
    FaceResult: require('./result/face-result/index.vue').default,
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    SpaceInformation: require('@/views/datagovernance/onlinegovernance/spaceinformation/index.vue').default,
    PlaceManagement: require('@/views/datagovernance/onlinegovernance/placemanagement/index.vue').default,
    ImageDataGovernance: require('@/views/governanceautomatic/components/image-data-governance.vue').default,
    FaceDataGovernance: require('@/views/governanceautomatic/face-data-governance').default,
    VehicleDataGovernance: require('@/views/governanceautomatic/vehicle-data-governance').default,
    ZDRDataGovernance: require('@/views/governanceautomatic/zdr-data-governance').default,
    taskView: require('./task-view.vue').default,
    VehicleFlowDataGovernance: require('@/views/governanceautomatic/vehicle-flow-data-governance').default,
  },
};
</script>
<style lang="less" scoped>
.governanceautomatic {
  overflow: hidden;
  position: relative;
  height: 100%;
  background-color: var(--bg-content);
  color: var(--color-content);
  font-size: 14px;
  @{_deep}.underline-menu-wrapper {
    overflow: hidden;
    overflow-x: auto;
    .tab:last-of-type {
      margin-right: 0;
    }
  }
  .com-box-h {
    height: calc(100% - 64px);
  }
}
.no-data-box-div.no-data {
  width: 100%;
}
</style>
