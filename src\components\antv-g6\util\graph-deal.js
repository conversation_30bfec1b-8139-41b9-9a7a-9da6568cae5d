/**
 *
 * @param {*} graph
 * @param {Array} states
 */
export function clearStates(graph, states = []) {
  if (!graph) {
    console.error("请传入【graph】实例");
  }
  graph.setAutoPaint(false);
  graph.getNodes().forEach((node) => {
    node.clearStates(states);
  });

  states.forEach((state) => {
    const edges = graph.findAllByState("edge", state);
    edges.forEach((row) => {
      row.clearStates(states);
    });
  });

  graph.paint();
  graph.setAutoPaint(true);
}

export function getDisabletStates() {
  return ["secondary", "analysis"];
}
