<template>
  <Dropdown trigger="custom" ref="drop" :visible="visibleValue" v-clickoutside="onClickOutside" class="dropdown">
    <div
      @click="selectClick"
      class="ivu-select ivu-select-multiple ivu-select-default"
      :class="[
        visibleValue ? 'ivu-select-visible' : '',
        disabled ? 'ivu-select-disabled' : '',
        !multiple ? 'ivu-select-single' : '',
      ]"
    >
      <div class="ivu-select-selection" ref="treeSelect">
        <input type="hidden" />
        <div>
          <div v-for="(item, index) in values" :key="item.id">
            <div v-if="index < 1" class="ivu-tag ivu-tag-checked ellipsis">
              <span class="ivu-tag-text">{{ item[props.label] }}</span>
              <i class="ivu-icon ivu-icon-ios-close" @click.stop="removeTag(item, index)"></i>
            </div>
          </div>

          <div class="ivu-tag ivu-tag-checked" v-if="values.length > 1">
            <span class="ivu-tag-text">{{ `+${values.length - 1}` }}</span>
          </div>
          <span class="ivu-select-selected-value" v-show="singleDisplayValue">{{ singleDisplayValue }}</span>
          <input
            type="text"
            v-model="query"
            v-if="!disabled && filterable"
            class="ivu-select-input"
            :style="inputStyle"
            autocomplete="off"
            :placeholder="values.length || singleDisplayValue ? '' : placeholder"
            spellcheck="false"
            @keydown="resetInputState"
            ref="input"
          />
          <i class="ivu-icon ivu-icon-ios-arrow-down ivu-select-arrow"></i>
        </div>
      </div>
    </div>
    <DropdownMenu slot="list">
      <el-tree
        class="filter-tree"
        v-bind="$attrs"
        v-on="$listeners"
        :node-key="props.key"
        :data="filterData"
        :props="props"
        :filter-node-method="filterNode"
        @check-change="handleCheck"
        ref="tree"
      >
      </el-tree>
    </DropdownMenu>
  </Dropdown>
</template>
<script>
export default {
  name: 'ui-tree-select',
  props: {
    value: {
      type: [String, Number, Array],
      default: '',
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    expand: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    props: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'title',
          key: 'id',
        };
      },
    },
  },
  data() {
    return {
      capture: true,
      visibleValue: false,
      query: '',
      inputLength: 20,
      values: [],
      filterData: [],
      singleDisplayValue: '',
      singleDisplayValueId: '',
    };
  },
  computed: {
    inputStyle() {
      let style = {};
      if (this.filterable) {
        if (!this.values.length && !this.singleDisplayValue) {
          style.width = '100%';
        } else {
          style.width = `${this.inputLength}px`;
        }
      }
      return style;
    },
  },
  watch: {
    value: {
      handler(val) {
        this.$nextTick(() => {
          if (val && val.length) {
            this.$refs.tree.setCheckedKeys(this.multiple ? this.value : [this.value]);
            this.values = this.$refs.tree.getCheckedNodes();
          } else {
            this.values = [];
            this.$refs.tree.setCheckedKeys([]);
          }
        });
      },
      immediate: true,
    },
    query() {
      this.$refs.tree.filter(this.query);
      this.visibleValue = true;
    },
    treeData: {
      deep: true,
      immediate: true,
      handler: function () {
        this.filterData = JSON.parse(JSON.stringify(this.treeData));
        // this.$nextTick(() => {
        //   this.values = this.$refs.tree.getCheckedNodes()
        // })
      },
    },
  },
  methods: {
    filterNode(value, data, node) {
      if (!value) return true;
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some((label) => label.indexOf(value) !== -1);
    },
    resetInputState() {
      this.inputLength = this.$refs.input.value.length * 12 + 20;
    },
    handleCheck(val, checked) {
      if (this.visibleValue) this.$refs.input.focus();
      if (this.query) this.query = '';
      if (this.multiple) {
        //多选
        this.values = this.$refs.tree.getCheckedNodes();
        this.$emit(
          'input',
          this.values.map((item) => item[this.props.key]),
        );
      } else {
        //单选
        if (checked) {
          this.$refs.tree.setCheckedKeys([val[this.props.key]]);
          this.singleDisplayValue = val.title;
          this.singleDisplayValueId = val[this.props.key];
          this.$emit('input', val[this.props.key]);
          this.$emit('on-change-node', val);
        } else {
          if (this.singleDisplayValueId === val[this.props.key]) {
            this.singleDisplayValue = '';
            this.$emit('input', '');
            this.$emit('on-change-node', '');
          }
        }
        this.visibleValue = false;
      }
    },
    removeTag(item, index) {
      if (this.disabled) return false;
      this.values.splice(index, 1);
      this.$refs.input.focus();
      this.$refs.tree.setChecked(item[this.props.key], false);
    },
    selectClick() {
      if (this.disabled) return false;
      if (this.visibleValue) this.visibleValue = false;
      else this.visibleValue = true;
      this.$refs.input.focus();
    },
    onClickOutside() {
      this.visibleValue = false;
    },
  },
};
</script>
<style lang="less" scoped>
@import 'index.less';
</style>
