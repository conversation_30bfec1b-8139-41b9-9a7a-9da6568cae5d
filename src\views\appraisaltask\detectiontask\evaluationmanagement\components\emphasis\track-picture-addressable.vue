<template>
  <!-- 轨迹图片可访问率 -->
  <ui-modal class="reporting-accuracy" v-model="visible" title="查看详情" :styles="styles" :footer-hide="true">
    <!--  v-if="!!trackList && trackList.detectionAmount != 0" -->
    <div class="content auto-fill" v-if="!!trackList">
      <div class="container auto-fill">
        <div class="title_text">
          <span class="fl text_left mr-lg">指标名称：{{ this.$parent.row.indexName }}</span>
          <span class="fl text_left">评测时间：{{ this.$parent.row.createTime }}</span>
          <div class="export fr">
            <!-- <Button type="primary" class="btn_search">
              <i class="icon-font icon-daochu font-white f-14 mr-xs"></i>
              <span class="inline ml-xs">导出</span>
            </Button> -->
          </div>
        </div>
        <line-title title-name="检测结果统计"></line-title>
        <div class="statistics_list">
          <div class="statistics">
            <div class="sta_item_left">
              <draw-echarts
                :echart-option="echartRing"
                :echart-style="ringStyle"
                ref="zdryChart"
                class="charts"
              ></draw-echarts>
            </div>
            <div class="line"></div>
            <no-standard :module-data="moduleData"></no-standard>
          </div>
        </div>
        <line-title title-name="轨迹准确性存疑列表">
          <template slot="content">
            <slot name="content">
              <tagView class="tagView fr" ref="tagView" :list="['聚档模式', '图像模式']" @tagChange="tagChange1" />
            </slot>
          </template>
        </line-title>
        <!-- 图片模式------------------>
        <TableList
          v-if="modelTag == 0 && columns.length > 0"
          ref="infoList"
          :columns="columns"
          :loadData="loadDataList"
        >
          <!-- 检索 -->
          <div slot="search" class="hearder-title">
            <trajectory-search @startSearch="startSearch"></trajectory-search>
          </div>
          <!-- 表格操作 -->
          <template #trackImage="{ row }">
            <div @click="viewBigPic(row.trackLargeImage)" class="ui-images">
              <ui-image :src="row.trackImage" />
            </div>
          </template>
          <template #identityPhoto="{ row }">
            <div @click="viewBigPic(row.identityPhoto)" class="ui-images">
              <ui-image :src="row.identityPhoto" alt="" />
            </div>
          </template>
          <template #urlAvailableStatus="{ row }">
            <span style="color: #19c176" v-if="row.urlAvailableStatus == 1">URL可访问</span>
            <span style="color: #c43d2c" v-else>URL不可访问</span>
          </template>
          <template #thumbnailStatus="{ row }">
            <span style="color: #19c176" v-if="row.thumbnailStatus == 1">可用</span>
            <span style="color: #c43d2c" v-else-if="row.thumbnailStatus == 2">不可用</span>
            <span v-else>--</span>
          </template>
          <template #largeStatus="{ row }">
            <span style="color: #19c176" v-if="row.largeStatus == 1">可用</span>
            <span style="color: #c43d2c" v-else-if="row.largeStatus == 2">不可用</span>
            <span v-else>--</span>
          </template>
          <template #algResult="{ row }">
            <span v-if="row.algResult">
              <div v-for="(item, index) in JSON.parse(row.algResult)" :key="index">
                <span>{{ item.algorithmType }}:</span>
                <span>{{ item.score }}%</span>
              </div>
            </span>
            <span v-else>--</span>
          </template>
        </TableList>
        <!-- 聚档模式 ---------------------------------------------------------------------------------------------------- -->
        <TableCard ref="infoCard" :loadData="loadDataCard" :cardInfo="cardInfo" v-if="modelTag == 1">
          <div slot="search" class="hearder-title">
            <trajectory-search :is-image-model="false" class="mb-sm" @startSearch="startSearch"></trajectory-search>
          </div>
          <!-- 卡片 -->
          <template #card="{ row }">
            <UiGatherCard
              class="card"
              :list="row"
              :personTypeList="personTypeList"
              :cardInfo="cardInfo"
              @detail="detailInfo(row)"
            ></UiGatherCard>
          </template>
        </TableCard>
      </div>
    </div>

    <div class="no-box" v-else>
      <div class="no-data">
        <i class="no-data-img icon-font icon-zanwushuju1"></i>
      </div>
    </div>
    <LookScene v-model="bigPictureShow" :img-list="imgList"></LookScene>
    <person-detail ref="captureDetail" :resultId="this.$parent.row.resultId">
      <template #searchList>
        <trajectory-search class="capture-search" @startSearch="startDetailSearch"></trajectory-search>
      </template>
    </person-detail>
  </ui-modal>
</template>

<style lang="less" scoped>
.reporting-accuracy {
  @{_deep} .ivu-modal {
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 10px 20px;
    }
  }
  .no-box {
    width: 1686px;
    min-height: 820px;
    max-height: 820px;
  }
  .content {
    color: #fff;
    background: var(--bg-content);
    min-height: 820px;
    max-height: 820px;
    border-radius: 4px;
    position: relative;
    .container {
      .list_item {
        margin-top: 10px;
      }
      .title_text {
        width: 100%;
        height: 50px;
        display: inline-block;
        line-height: 50px;
        // background-color: #239df9;
        .text_left {
          margin-top: 10px;
        }
        .export {
          margin-top: 10px;
          //   position: absolute;
          //   top: 20px;
          //   right: 20px;
        }
      }
      .scheme_header {
        height: 30px;
        line-height: 30px;
        width: 100%;
        display: inline-block;
        margin-top: 10px;
        .scheme_line {
          width: 8px;
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          background: #239df9;
          opacity: 1;
        }
        .scheme_title {
          width: calc(100% - 18px);
          height: 30px;
          vertical-align: middle;
          display: inline-block;
          margin-left: 10px;
          padding-left: 10px;
          background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
        }
      }
      .statistics_list {
        width: 100%;
        height: 180px;
        line-height: 180px;
        background-color: var(--bg-sub-content);
        margin-top: 10px;
      }
      .statistics {
        width: 100%;
        height: 180px;
        line-height: 180px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sta_item_left {
          width: 48%;
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
        }
        .line {
          width: 1px;
          height: 70%;
          background-color: #0d477d;
        }
      }
    }
    .search-wrapper {
      height: 50px;
      display: flex;
      align-items: center;
      .input-width {
        width: 176px;
      }
    }

    .card {
      width: calc(calc(100% - 40px) / 4);
      margin: 0 5px 10px;
    }
    .hearder-title {
      color: #fff;
      margin-top: 10px;
      font-size: 14px;
      .mr20 {
        margin-right: 20px;
      }
      .blue {
        color: #19c176;
      }
    }
    .ui-images {
      width: 56px;
      height: 56px;
      margin-bottom: 9px;
      .ui-image {
        min-height: 56px !important;
        /deep/ .ivu-spin-text {
          img {
            width: 56px;
            height: 56px;
            margin-top: 5px;
          }
        }
      }
    }
  }
}
</style>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    value: {},
    statisticsList: {},
  },
  data() {
    return {
      echartRing: {},
      ringStyle: {
        width: '700px',
        height: '180px',
      },
      zdryChartObj: {
        xAxisData: ['URL可访问轨迹数量', 'URL不可访问轨迹数量'],
        showData: [
          { name: 'URL可访问轨迹数量', value: 0 },
          { name: 'URL不可访问轨迹数量', value: 0 },
        ],
        zdryTimer: null,
        count: 0,
        formData: {},
        color: [
          [
            { offset: 0, color: '#32A19E' },
            { offset: 1, color: '#05CE98' },
          ],
          [
            { offset: 0, color: '#983C0D' },
            { offset: 1, color: '#E9410F' },
          ],
        ],
      },
      moduleData: {
        rate: '轨迹图片可访问率',
        rateValue: 0,
        price: '达标值',
        priceValue: 0,
        result: '考核结果',
        resultValue: '',
        remarkValue: '',
      },
      tableData: [],
      minusTable: 600,
      searchData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styles: {
        width: '9rem',
      },
      visible: true,
      imgList: [require('@/assets/img/navigation-page/systemmanagement.png')],
      trackList: {},

      modelTag: 0, // 聚档模式,图像模式
      bigPictureShow: false,
      columns: [
        { type: 'index', width: 70, title: '序号', align: 'center' },
        { title: '人脸抓拍', key: 'trackImage', slot: 'trackImage' },
        { title: '抓拍时间', key: 'shotTime', width: 170 },
        { title: '抓拍点位', key: 'catchPlace' },
        { title: '档案照', key: 'identityPhoto', slot: 'identityPhoto' },
        { title: '姓名', key: 'name' },
        { title: '证件号', key: 'idCard' },
        {
          title: '小图URL是否可用',
          key: 'thumbnailStatus',
          slot: 'thumbnailStatus',
        },
        { title: '大图URL是否可用', key: 'largeStatus', slot: 'largeStatus' },
        {
          title: '检测结果',
          key: 'urlAvailableStatus',
          slot: 'urlAvailableStatus',
        },
      ],
      loadDataList: (parameter) => {
        return this.$http
          .post(
            governanceevaluation.pageTrackUrlAvailableDetails,
            Object.assign(
              parameter,
              {
                resultId: this.$parent.row.resultId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
      cardInfo: [
        { name: '姓名：', value: 'name' },
        { name: '证件号：', value: 'idCard' },
        { name: '轨迹总数：', value: 'catchAmount' },
        { name: '异常轨迹：', value: 'unqualifiedAmount', color: '#BC3C19' },
      ],
      loadDataCard: (parameter) => {
        return this.$http
          .post(
            governanceevaluation.pageTrackUrlAvailableClusters,
            Object.assign(
              parameter,
              {
                resultId: this.$parent.row.resultId,
              },
              this.searchData,
            ),
          )
          .then((res) => {
            return res.data;
          });
      },
    };
  },
  async mounted() {
    if (this.personTypeList.length == 0) await this.getAlldicData();
    await this.getStatistics();
    // if (this.personTypeList.length == 0) await this.getPersonTypeList()
    await this.initRing();
    await this.init();
  },

  methods: {
    handleCheckStatus(row) {
      const flag = {
        1: '可用',
        2: '不可用',
      };
      return flag[row];
    },
    ...mapActions({
      getAlldicData: 'algorithm/getAlldicData',
    }),
    tagChange1(val) {
      if (this.modelTag == val) {
        return;
      }
      this.searchData = {};
      this.modelTag = val;
      this.$nextTick(() => {
        if (this.modelTag === 0) {
          this.$refs.infoList.info(true);
        } else {
          this.$refs.infoCard.info(true);
        }
      });
    },
    async init() {
      this.modelTag = 0;
      this.$nextTick(() => {
        if (this.$refs.tagView) {
          this.$refs.tagView.curTag = 0;
        }
      });
      this.$nextTick(() => {
        this.$refs.infoList.info(true);
      });
    },
    // 详情
    detailInfo(info) {
      this.$refs.captureDetail.show(info);
    },
    startDetailSearch(params) {
      this.$refs.captureDetail.startSearch(params);
    },
    viewBigPic(item) {
      if (!item) {
        this.$Message.warning('大图URL缺失');
        return;
      }
      this.imgList = [item];
      this.bigPictureShow = true;
    },

    // 统计
    async getStatistics() {
      try {
        let res = await this.$http.get(governanceevaluation.getByAddressableResultId, {
          params: {
            resultId: this.$parent.row.resultId,
            indexId: this.$parent.row.indexId,
          },
        });

        if (!res.data.data) return;
        this.trackList = res.data.data;
        this.moduleData.rateValue = this.$parent.row.resultValue || 0;
        this.moduleData.priceValue = this.$parent.row.standardsValue || 0;
        this.moduleData.resultValue = this.$parent.row.qualifiedDesc || 0;
      } catch (error) {
        console.log(error);
      }
    },
    // echarts图表
    initRing() {
      let xAxisData = this.zdryChartObj.xAxisData;
      this.zdryChartObj.showData.map((item) => {
        if (item.name === 'URL可访问轨迹数量') {
          item.value = this.trackList.qualifiedAmount;
        } else {
          item.value = this.trackList.zdrImportantPersonAmount - this.trackList.qualifiedAmount;
        }
      });
      this.zdryChartObj.count = this.trackList.zdrImportantPersonAmount;
      let formatData = {
        seriesName: '检测轨迹图像',
        xAxisData: xAxisData,
        showData: this.zdryChartObj.showData,
        count: this.zdryChartObj.count,
        color: this.zdryChartObj.color,
      };
      this.zdryChartObj.formatData = formatData;
      this.echartRing = this.$util.doEcharts.evaluationPageRin(this.zdryChartObj.formatData);
    },
    startSearch(searchData) {
      this.searchData = searchData;
      if (this.modelTag === 0) {
        this.$refs.infoList.info(true);
      } else {
        this.$refs.infoCard.info(true);
      }
    },
  },
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/person_type',
    }),
  },
  watch: {
    // '$parent.taskObj': {
    //   deep: true,
    //   handler: function(val) {
    //     this.info()
    //   },
    // },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },

  components: {
    DrawEcharts: require('@/components/draw-echarts').default,
    lineTitle: require('@/components/line-title').default,
    noStandard: require('@/views/appraisaltask/detectiontask/evaluationmanagement/components/no-standard.vue').default,
    tagView: require('./components/tags.vue').default,
    LookScene: require('@/components/look-scene').default,
    TableList: require('./components/tableList.vue').default,
    TableCard: require('./components/tableCard.vue').default,
    UiGatherCard: require('./components/ui-gather-card.vue').default,
    personDetail: require('@/components/person-detail.vue').default,
    TrajectorySearch: require('./components/trajectory-search.vue').default,
  },
};
</script>
