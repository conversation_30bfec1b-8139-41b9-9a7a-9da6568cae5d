<template>
  <div class="tag-container" :class="`${getTypeCode(type)}-tag`">
    <span class="tag-desc"><slot></slot></span>
  </div>
</template>

<script>
export default {
  name: 'tag',
  props: {
    type: {
      default: '1',
    },
  },
  methods: {
    getTypeCode(type) {
      switch (type) {
        case '1':
          return 'default';
        case '2':
          return 'shangbao';
        case '3':
          return 'linshi';
        default:
          'default';
      }
    },
  },
};
</script>

<style lang="less" scoped>
[data-theme='light'] {
  .default-tag {
    background: #2c86f8;
  }
  .shangbao-tag {
    background: #1faf81;
  }
  .linshi-tag {
    background: #f29f4c;
  }
}
[data-theme='deepBlue'] {
  .default-tag {
    background: #113961;
  }
  .shangbao-tag {
    background: #1faf81;
  }
  .linshi-tag {
    background: #f29f4c;
  }
}
.tag-container {
  display: inline-block;
  height: 20px;
  line-height: 20px;
  opacity: 1;
  border-radius: 10px;
  text-align: center;
  padding: 0 10px 0 10px;
}
.tag-desc {
  color: #fff;
  font-size: 12px;
}
.default-tag {
  background: #058fb3;
}
.shangbao-tag {
  background: #069c6c;
}
.linshi-tag {
  background: #b78a0f;
}
</style>
