import cascadeList from '@/config/api/cascadeList';
import taganalysis from '@/config/api/taganalysis';
import evaluationoverview from '@/config/api/evaluationoverview';

import axios from 'axios';
export default {
  namespaced: true,
  state: {
    configData: {},
    deviceTagData: [],
    isRefresh: false, //刷新的flag
    showColRegion: false,
  },
  mutations: {
    setConfigData(state, params) {
      state.configData = params;
    },
    setDeviceTagData(state, params) {
      state.deviceTagData = params;
    },
    setIsRefresh(state, params) {
      state.isRefresh = params;
    },
    setShowColRegion(state, val) {
      state.showColRegion = val;
    },
  },
  getters: {
    configData(state) {
      return state.configData;
    },
    deviceTagData(state) {
      return state.deviceTagData;
    },
    tagListGetter(state) {
      return state.deviceTagData.map((row) => {
        return {
          name: row.tagName,
          id: row.tagId,
        };
      });
    },
    getIsRefresh(state) {
      return state.isRefresh;
    },
    getShowColRegion(state) {
      return state.showColRegion;
    },
  },
  actions: {
    async getCascadeConfig({ commit }) {
      try {
        let {
          data: { data },
        } = await axios.get(cascadeList.getCascadeConfig);
        commit('setConfigData', data || {});
      } catch (error) {
        console.log(error);
      }
    },
    //查询所有标签
    async getTagList({ commit, state }) {
      if (state.deviceTagData.length > 0) {
        return;
      }
      try {
        let {
          data: { data },
        } = await axios.post(taganalysis.getDeviceTag, {
          isPage: false,
        });
        commit('setDeviceTagData', data || []);
      } catch (err) {
        console.log(err);
      }
    },
    // 查询 统计列表是否需要显示 【建档匹配率】和乘积 列
    async getConfigByKeys({ commit }) {
      try {
        let res = await axios.get(`${evaluationoverview.getConfigByKeys}?keys=onlineField.enable`);
        commit('setShowColRegion', res.data.data['onlineField.enable']);
      } catch (err) {
        console.log(err);
      }
    },
  },
};
