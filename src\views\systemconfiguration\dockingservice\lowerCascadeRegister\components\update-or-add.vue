<template>
  <ui-modal ref="modal" :title="title" :width="600" @query="submitHandle">
    <Form ref="formData" :model="formData" :rules="rules" :label-width="130">
      <FormItem label="接口名称" prop="intefaceName">
        <Input v-model="formData.intefaceName" placeholder="接口名称"></Input>
      </FormItem>
      <FormItem label="注册对象" prop="orgCode">
        <api-organization-tree
          class="tree-style"
          :select-tree="selectOrgTree"
          @selectedTree="selectedOrgTree1"
          placeholder="请选择组织机构"
        />
      </FormItem>
      <FormItem label="接口类型" prop="intefaceType">
        <Select v-model="formData.intefaceType" placeholder="请选择接口类型">
          <Option :value="item.dataKey" v-for="(item, index) in interfaceTypeList" :key="index"
            >{{ item.dataValue }}
          </Option>
        </Select>
      </FormItem>
      <FormItem label="接口地址" prop="intefaceUrl">
        <Input v-model="formData.intefaceUrl" placeholder="请输入接口地址"></Input>
      </FormItem>
      <FormItem label="是否需要鉴权" prop="isAuth">
        <RadioGroup v-model="formData.isAuth">
          <Radio :label="'1'">是</Radio>
          <Radio :label="'0'">否</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="授权账户" :required="isRequire" prop="username" :class="{ requireStar: isRequire }">
        <Input type="text" v-model="formData.username" placeholder="授权本平台账户"></Input>
        <Input type="text" class="hide-input" placeholder="授权本平台账户"></Input>
      </FormItem>
      <FormItem label="授权密码" :required="isRequire" prop="password" :class="{ requireStar: isRequire }">
        <!--禁止自动填充-->
        <Input type="password" class="hide-input" placeholder="授权本平台密码"></Input>
        <Input type="password" v-model="formData.password" placeholder="授权本平台密码"></Input>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import api from '@/config/api/cascadeIntefaceConfig';
import apiOrganizationTree from '@/api-components/api-organization-tree';

export default {
  components: { apiOrganizationTree },
  props: ['interfaceTypeList'],
  data() {
    const c = (rule, value, callback) => {
      if (!value && this.isRequire) {
        return callback(new Error('请填写必填项'));
      } else {
        return callback();
      }
    };
    return {
      showItem: false,
      title: '',
      confirmLoading: null,
      formData: {
        'id': '',
        'intefaceName': '',
        'intefaceType': '',
        'intefaceUrl': '',
        'isAuth': '1',
        'orgCode': '',
        'password': '',
        'username': '',
      },
      selectOrgTree: {
        orgCode: '',
      },
      rules: {
        intefaceName: [{ required: true, message: '请输入接口名称', trigger: 'blur' }],
        intefaceType: [{ required: true, message: '请选择接口类型', trigger: 'change' }],
        intefaceUrl: [{ required: true, message: '请输入接口地址', trigger: 'blur' }],
        localIp: [{ required: true, message: '请输入IP', trigger: 'blur' }],
        isAuth: [{ required: true, message: '是否鉴权', trigger: 'change' }],
        orgCode: [{ required: true, message: '请选择组织机构', trigger: 'change' }],
        localPort: [{ required: true, message: '请输入端口', trigger: 'blur' }],
        password: [{ required: true, validator: c, trigger: 'blur' }],
        username: [{ required: true, validator: c, trigger: 'blur' }],
      },
    };
  },
  computed: {
    isRequire() {
      return this.formData.isAuth === '1';
    },
  },
  methods: {
    showModal(row) {
      this.$refs.modal.modalShow = true;
      let id = row ? row.id : '';
      this.title = id ? '编辑注册信息' : '接口注册';
      this.action = id ? 'update' : 'add';
      this.selectOrgTree.orgCode = '';
      this.$refs.formData.resetFields();
      if (id) {
        this.selectOrgTree.orgCode = row.orgCode;
        this.formData = { ...row };
      }
    },
    selectedOrgTree1(val) {
      this.formData.orgCode = val.orgCode;
    },
    // 提交
    submitHandle() {
      this.$refs['formData'].validate((valid) => {
        if (valid && !this.confirmLoading) {
          this.confirmLoading = this.$Message.loading({ content: 'Loading...', duration: 0 });
          this.$http[this.action === 'add' ? 'post' : 'put'](
            this.action === 'add' ? api.subInterfaceAdd : api.subInterfaceUpdate,
            this.formData,
          )
            .then((res) => {
              let data = res.data;
              this.$Message.success(data.msg);
              this.$refs.modal.modalShow = false;
              this.$emit('refreshDataList');
            })
            .finally(() => {
              this.confirmLoading();
              this.confirmLoading = null;
            });
        }
      });
    },
  },
};
</script>
<style lang="less" scoped>
/deep/ .ivu-modal-body {
  padding: 20px 60px 20px 50px;
}
/deep/ .tree-style {
  width: 100%;
  .ivu-dropdown {
    width: 100%;
  }
  .select-width {
    width: 100%;
  }
  .ivu-select-dropdown {
    width: 100%;
  }
}
.requireStar /deep/ .ivu-form-item-label:before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 12px;
  color: #ed4014;
}
</style>
