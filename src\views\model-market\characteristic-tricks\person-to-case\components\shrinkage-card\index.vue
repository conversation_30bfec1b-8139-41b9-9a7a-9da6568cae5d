<!--
    * @FileDescription: 收缩框
-->
<template>
  <div class="shrinkage-card-box" :class="{ 'search_box-pack': packUpDown }">
    <div class="title">
      <p class="title_num">{{ index }}</p>
      <p class="title_text">{{ title }}</p>
      <div class="switch_point">
        <slot name="action"></slot>
      </div>
    </div>
    <div
      class="search_condition"
      :class="{ 'search_condition-pack': packUpDown }"
    >
      <slot></slot>
    </div>
    <div class="footer-box" v-show="showFooter">
      <div
        class="footer"
        :class="{ packArrow: packUpDown }"
        @click="handlePackup"
      >
        <img :src="packUrl" alt="" />
        <p>{{ packUpDown ? "展开" : "收起" }}</p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    index: {
      type: Number,
      default: 0,
    },
    title: {
      type: String,
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      packUrl: require("@/assets/img/model/icon/arrow.png"),
      packUpDown: false,
    };
  },
  methods: {
    handlePackup() {
      this.packUpDown = !this.packUpDown;
    },
    onFlexible(flag) {
      this.packUpDown = flag;
    },
  },
};
</script>

<style lang='less' scoped>
@import "../../../../components/style/index";

.shrinkage-card-box {
  background: #fff;
  box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  width: 370px;

  .title_text {
    flex: 1;
  }

  .search_condition {
    padding: 15px;
    max-height: calc(~"100vh - 200px");
    overflow-y: auto;
    box-sizing: border-box;
  }

  .search_condition-pack {
    height: 0px;
    transition: height 0.2s ease-out;
    overflow: hidden;
    padding: 0;
  }

  .footer-box {
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.298);
    background: #ffffff;
    border-radius: 4px 4px 4px 4px;
  }
}
</style>
