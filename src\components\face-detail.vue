<template>
  <ui-modal v-model="captureVisible" title="抓拍详情" :footerHide="true" width="95%" @onCancel="cancel">
    <Row>
      <Col span="4">
        <div class="contentLeft" v-if="personObj">
          <div class="capture-details-left-sculpture">
            <ui-image :src="personObj.facePath" />
          </div>
          <div>姓名：{{ personObj.name || '未知' }}</div>
          <div>身份证：{{ personObj.idCard || '未知' }}</div>
          <tags-more
            v-if="personObj.personType"
            :tagList="personObj.personType"
            :defaultTags="4"
            placement="left-start"
            bgColor="#2D435F"
          ></tags-more>
        </div>
      </Col>
      <Col span="20" style="overflow: hidden">
        <div class="content">
          <div class="capture-details-right-top">
            <label
              >抓拍总量：<span>{{ this.personObj.urlNum }}</span></label
            ><label
              >准确性存疑抓拍数量：<span>{{ this.personObj.abnormalTrajectoryNum }}</span></label
            >
          </div>
          <slot name="accurySearchList"></slot>
          <div v-ui-loading="{ loading: loading, tableData: tableData }">
            <div :style="styleScroll6">
              <div class="carItem" v-for="(item, index) in tableData" :key="item.id">
                <div class="item">
                  <!-- <div class="num" v-if="item.similarity">{{item.similarity}}%</div> -->
                  <div class="num">
                    {{ item.similarity ? item.similarity + '%' : 0 }}
                  </div>
                  <div class="img" @click="viewBigPic(index, item.facePath)">
                    <!-- <span @click.stop="showDetail" v-show="index === isactive">1111</span> -->
                    <ui-image :src="item.facePath" />
                    <p class="shadow-box" title="查看检测结果">
                      <i
                        class="icon-font icon-yichang search-icon mr-xs base-text-color"
                        @click.stop="checkReason(item)"
                      ></i>
                    </p>
                  </div>

                  <div class="group-message">
                    <p class="marginP" :title="`抓拍时间：${item.shotTime || '暂无数据' | filterDateFun}`">
                      <i class="icon-font icon-shijian"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                        item.shotTime || '暂无数据' | filterDateFun
                      }}</span>
                    </p>
                    <p :title="item.catchPlace">
                      <i class="icon-font icon-dizhi"></i>
                      <span class="group-text inline vt-middle ml-xs ellipsis onlys">{{
                        item.catchPlace ? item.catchPlace : '暂无数据'
                      }}</span>
                    </p>
                  </div>
                  <!-- <i
                    class="icon-font icon-buhege"
                    v-if="!item.trackImage || !item.trackLargeImage"
                  ></i> -->
                </div>
              </div>
            </div>
            <!-- 分页 -->
            <ui-page
              class="page menu-content-background"
              :page-data="pageData"
              @changePage="changePage"
              @changePageSize="changePageSize"
            ></ui-page>
          </div>
        </div>
        <look-scene v-model="bigPictureShow" :img-list="imgList"></look-scene>
      </Col>
    </Row>
    <trail-decide :otherMsg="otherMsg" :personObj="personObj" ref="traildecide"></trail-decide>
    <track-reason ref="trackReason"></track-reason>
  </ui-modal>
</template>

<script>
import { mapGetters } from 'vuex';
import api from '@/config/api/governanceevaluation';
export default {
  name: 'captureDetail',
  props: {
    resultId: {},
    // 筛选条件
    defaultSearchData: {
      type: Object,
      // default() {
      //   return {
      //     trackType: 3,
      //   }
      // },
    },
    // 接口名称
    interFaceName: {
      default: api.personAccuracyDetails,
    },
  },
  data() {
    return {
      captureVisible: false,
      tableData: [],
      searchData: {
        pageNumber: 1,
        pageSize: 20,
      },
      pageData: {
        totalCount: 0,
        pageNum: 1,
        pageSize: 20,
      },
      styleScroll6: {
        position: 'relative',
        width: '100%',
        height: '500px',
        'overflow-y': 'scroll',
      },
      bigPictureShow: false,
      imgList: [],
      personObj: {}, // 人员信息对象
      loading: false,
      isactive: false, //鼠标移入事件
      otherMsg: {},
    };
  },
  async created() {},
  computed: {
    ...mapGetters({
      personTypeList: 'algorithm/personTypeList',
    }),
  },
  methods: {
    show(item) {
      this.personObj = item;
      this.infoList();
      this.captureVisible = true;
    },
    async infoList() {
      this.loading = true;
      let params = Object.assign(this.searchData, this.defaultSearchData);
      params.idCard = this.personObj.idCard;
      params.resultId = this.resultId;
      let res = await this.$http.post(this.interFaceName, Object.assign(this.searchData, params));
      this.tableData = res.data.data.entities;
      this.pageData.totalCount = res.data.data.total;
      this.loading = false;
    },
    viewBigPic(index, item) {
      this.imgList = [item];
      this.bigPictureShow = true;
    },
    // 分页
    changePage(val) {
      this.searchData.pageNumber = val;
      this.pageData.pageNum = val;
      this.infoList();
    },
    changePageSize(val) {
      this.searchData.pageNumber = 1;
      this.pageData.pageNum = 1;
      this.searchData.pageSize = val;
      this.pageData.pageSize = val;
      this.infoList();
    },
    captureDetail(item) {
      this.$refs.uploadModal.show(item);
    },
    reset() {
      this.pageData = { totalCount: 0, pageNum: 1, pageSize: 20 };
      this.searchData = {
        pageNumber: 1,
        pageSize: 20,
      };
    },
    // 检索，父组件调用
    startSearch(searchData) {
      this.searchData = Object.assign(this.searchData, searchData);
      this.infoList();
    },
    similarityVal(val) {
      return (val * 100).toFixed(2) + '%';
    },
    // 查看异常原因
    checkReason(item) {
      this.otherMsg = item;
      this.$refs.traildecide.visible = true;
    },
    cancel() {
      this.$emit('clean');
    },
  },
  watch: {
    captureVisible(val) {
      this.$emit('input', val);
    },
    value(val) {
      !val ? this.reset() : null;
      this.captureVisible = val;
    },
  },
  components: {
    // trajectoryModal: require('../component/trajectory-modal').default,
    // uploadModal: require('../component/upload-modal').default,
    uiImage: require('@/components/ui-image').default,
    TagsMore: require('@/components/tags-more_str').default,
    trackReason: require('@/components/trackReason-module').default,
    TrailDecide: require('@/views/datagovernance/tasktracking/importantperson/popup/details/trail-decide_str.vue')
      .default,
  },
};
</script>

<style lang="less" scoped>
.contentLeft {
  color: #fff;
  padding: 0 30px;
  div {
    margin-top: 12px;
  }

  img {
    width: 100%;
  }

  ul {
    li {
      float: left;
      padding: 6px 10px;
      background: #1a447b;
      border-radius: 4px;
      margin-right: 10px;
      margin-top: 10px;
    }
  }
}
.content {
  height: 600px;
  color: #fff;
}

/deep/ .ivu-modal-footer {
  height: 0 !important;
  padding: 0 !important;
}

.carItem {
  height: 236px;
  margin: 10px 10px 0px 0;
  width: 188px;
  display: inline-block;
  // max-width: 188px;
  .item {
    position: relative;
    height: 100%;
    background: #0f2f59;
    .num {
      position: absolute;
      right: 0;
      z-index: 100;
      padding: 10px;
      border-radius: 5px;
      background: rgba(42, 95, 175, 0.6);
    }
    .img {
      position: relative;
      cursor: pointer;
      position: relative;
      width: calc(100% - 28px);
      height: 167px;
      padding-top: 14px;
      margin-left: 14px;
      display: flex;
      align-items: center;
      .shadow-box {
        height: 28px;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        position: absolute;
        bottom: 0;
        display: none;
        padding-left: 10px;
        z-index: 100;
        > i:hover {
          color: var(--color-primary);
        }
      }
      &:hover {
        .shadow-box {
          display: block;
        }
      }
      span {
        position: absolute;
        top: 50%;
        left: 40%;
        z-index: 100;
        cursor: pointer;
      }
      .percent {
        position: absolute;
        top: 1px;
        left: 1px;
        display: inline-block;
        padding: 0 2px;
        min-width: 32px;
        text-align: center;
        background: #ea800f;
        color: #ffffff;
        z-index: 99;
      }
    }
    img {
      width: 100%;
      max-width: 100%;
      max-height: 156px;
      background: #999;
    }

    .group-message {
      padding-left: 12px;
      margin-top: 12px;
    }
    .icon-URLbukefangwen1 {
      color: #bc3c19;
      position: absolute;
      bottom: 54px;
      right: 14px;
      font-size: 60px;
      z-index: 10;
    }
  }
}
/deep/ .ivu-row {
  align-content: start;
}
.capture-details-left-sculpture {
  height: 210px;
  margin-bottom: 27px;
}
.capture-details-right-top {
  padding: 20px 0;
  font-size: 14px;
  color: #ffffff;
  label {
    margin-right: 58px;
    span {
      color: var(--color-bluish-green-text);
    }
  }
}
.onlys {
  width: 80%;
}
</style>
