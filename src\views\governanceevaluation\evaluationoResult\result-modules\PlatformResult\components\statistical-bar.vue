<template>
  <div class="info-statics-list">
    <ul>
      <li
        v-for="(item, index) in staticsList"
        :key="index"
        :class="['mb-sm', isEqual ? 'equal-division' : 'custormWidth', (index + 1) % equalNum === 0 ? '' : 'mr-sm']"
        :style="{ background: item.liBg, width: isEqual ? '' : '', 'box-shadow': item.boxShadow }"
      >
        <div class="left" :style="{ background: item.iconBgColor }">
          <i :class="['icon-font', 'f-50', 'icon-bg', item.icon]" :style="{ background: item.iconColor }"></i>
        </div>

        <div class="right">
          <p class="base-text-color">{{ item.name }}</p>
          <p class="statistic-num" :style="{ color: item.textColor }">
            <countTo
              :startVal="0"
              :endVal="Number(item[item.key] || 0)"
              :duration="3000"
              v-if="item.type != 'percentage'"
            >
            </countTo>
            <span v-if="item.type === 'percentage'">{{ !!item[item.key] ? item[item.key] : 0 }}</span>
            <!--            <span v-if="qualifiedVal === '2'" :class="qualifiedVal === '2' ? 'font-red' : 'font-green'">{{item[item.key]}}</span>-->
            <i class="icon-font icon-budabiao font-red" v-if="qualifiedVal === '2' && item.type == 'percentage'"></i>
            <i class="icon-font icon-dabiao font-green" v-if="qualifiedVal !== '2' && item.type == 'percentage'"></i>
          </p>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'info-statics-list',
  props: {
    staticsList: {
      type: Array,
      default: () => [],
    },
    isEqual: {
      type: Boolean,
      default: true,
    }, // 是否平均分配卡片宽度
    equalNum: {
      type: Number,
    }, // 每行卡片数（isEqual为false有效）
    qualifiedVal: {
      type: String,
      default: '',
    }, // 判断是否达标
  },
  data() {
    return {};
  },
  components: {
    countTo: require('vue-count-to').default,
  },
};
</script>

<style lang="less" scoped>
.center-flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

.info-statics-list {
  width: 100%;
  height: 100%;

  ul {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;

    li {
      position: relative;
      display: flex;
      align-items: center;
      //flex-wrap: wrap;
      height: 100%;
      border-radius: 4px;

      &:not(&:last-child) {
        //margin-right: 10px;
      }
    }

    .equal-division {
      width: 100%;
      //flex: 1;
    }
  }

  .left {
    .center-flex;
    width: 63px;
    height: 57px;
    margin-left: 50px;
    margin-right: 20px;
  }

  .icon-bg {
    -webkit-background-clip: text !important;
    color: #0000 !important;
  }

  .f-50 {
    font-size: 50px;
  }
  .statistic-num {
    font-size: 18px;
    font-family: Microsoft YaHei;
    font-weight: bold;
  }
  .icon-budabiao,
  .icon-dabiao {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 14px;
    cursor: default;
  }
}
</style>
