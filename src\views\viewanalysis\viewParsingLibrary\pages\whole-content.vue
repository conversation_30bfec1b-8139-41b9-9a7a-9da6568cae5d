<!--
    * @FileDescription: 全部
    * @Author: H
    * @Date: 2024/2/26
 * @LastEditors: zhengmingming <EMAIL>
 * @LastEditTime: 2025-01-23 16:13:59
 -->
<template>
  <section class="main-container">
    <div class="search-bar">
      <search-whole
        ref="searchBar"
        :queryParam.sync="queryParam"
        :visible.sync="visible"
        @search="searchHandle"
        @reset="resetHandle"
        @imgChange="imgChange"
      />
    </div>
    <div class="table-container">
      <div class="data-export">
        <Checkbox @on-change="checkAllHandler" v-model="checkAll"
          >全选</Checkbox
        >
        <!-- <div class="export-box">
                    <Button class="mr" @click="handleExport($event)" size="small"> 
                        <ui-icon type="daoru" color="#2C86F8"></ui-icon>
                        导出
                    </Button>
                    <exportBox ref="exportbox" v-if="exportShow" @confirm="confirm" @cancel="exportShow = false"></exportBox>
                </div> -->
        <Button
          class="mr"
          :type="queryParam.sortField == 'similarity' ? 'primary' : 'default'"
          @click="handleSort('similarity')"
          size="small"
          v-if="this.queryParam.features && this.queryParam.features.length > 0"
        >
          <Icon type="md-arrow-round-down" v-if="!similUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          相似度排序
        </Button>
        <Button
          class="mr"
          :type="queryParam.sortField == 'absTime' ? 'primary' : 'default'"
          @click="handleSort('absTime')"
          size="small"
        >
          <Icon type="md-arrow-round-down" v-if="!timeUpDown" />
          <Icon type="md-arrow-round-up" v-else />
          时间排序
        </Button>
        <!-- <Button @click="dataAboveMapHandler" size="small" style="float: right">
					<ui-icon type="dongtai-shangtudaohang" color="#2C86F8"></ui-icon>
					数据上图
				</Button> -->
        <Button
          @click="handleFalls"
          size="small"
          style="float: right; margin-right: 10px"
        >
          {{ fallsPage ? "传统翻页版本" : "瀑布流版本" }}
        </Button>
      </div>
      <div class="table-container-box">
        <div class="table-content" @scroll="handleScroll">
          <div
            class="list-card box-1"
            :class="{ checked: item.isChecked }"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="collection paddingIcon">
              <div class="bg"></div>
              <ui-btn-tip
                class="collection-icon"
                v-if="item.myFavorite == '1'"
                content="取消收藏"
                icon="icon-yishoucang"
                transfer
                @click.native="collection(item, 2)"
              />
              <ui-btn-tip
                class="collection-icon"
                v-else
                content="收藏"
                icon="icon-shoucang"
                transfer
                @click.native="collection(item, 1)"
              />
            </div>
            <Checkbox
              class="check-box"
              v-model="item.isChecked"
              @on-change="(e) => checkHandler(e, index)"
            ></Checkbox>
            <div class="img-content">
              <div class="similarity" v-if="item.idScore">
                <span class="num" v-if="item.idScore">{{ item.idScore }}%</span>
              </div>
              <template>
                <ui-image
                  :src="item.traitImg"
                  alt="动态库"
                  @click.native="faceDetailFn(item, index)"
                />
              </template>
            </div>
            <!-- 动态库 -->
            <div class="bottom-info">
              <time>
                <Tooltip
                  content="抓拍时间"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i class="iconfont icon-time"></i>
                </Tooltip>
                {{ item.absTime }}
              </time>
              <p>
                <Tooltip
                  content="抓拍地点"
                  placement="right"
                  transfer
                  theme="light"
                >
                  <i
                    v-if="item.taskType === '3'"
                    class="iconfont icon-a-filemode"
                    style="color: #08bba2"
                  ></i>
                  <i v-else class="iconfont icon-location"></i>
                </Tooltip>
                <ui-textOver-tips
                  refName="detailAddress"
                  :content="item.deviceName"
                ></ui-textOver-tips>
              </p>
            </div>
            <div class="fast-operation-bar">
              <Poptip trigger="hover" placement="right-start">
                <i class="iconfont icon-gengduo"></i>
                <div class="mark-poptip" slot="content">
                  <p @click="archivesPage(item, 1)">
                    <i class="iconfont icon-renlian1"></i>以图搜图
                  </p>
                  <p
                    v-if="item.dataType == 'face'"
                    @click="archivesPage(item, 2)"
                  >
                    <i class="iconfont icon-renlian1"></i>身份核验
                  </p>
                  <p @click="openDirectModel(item)">
                    <i class="iconfont icon-dongtai-shangtudaohang"></i>地图定位
                  </p>
                  <!-- <p @click="handleTargetAdd(item)"> <Icon type="ios-add-circle-outline" size="14" />搜索目标添加</p> -->
                </div>
              </Poptip>
            </div>
          </div>
          <div
            class="empty-card-1"
            v-for="(item, index) of 9 - (dataList.length % 9)"
            :key="index + 'demo'"
          ></div>
          <div class="gobacktop" @click="toTop">
            <div class="top-icon">
              <Icon type="md-download" />
            </div>
            <div class="top-text">
              <p>回到</p>
              <p>顶部</p>
            </div>
          </div>
        </div>
        <ui-empty v-if="dataList.length === 0 && !listLoading"></ui-empty>
        <ui-loading v-if="listLoading"></ui-loading>
        <!-- 分页 -->
        <ui-page
          v-if="!fallsPage"
          :current="pageInfo.pageNumber"
          :total="total"
          countTotal
          :page-size="pageInfo.pageSize"
          :page-size-opts="[27, 54, 81, 108]"
          @pageChange="pageChange"
          @pageSizeChange="pageSizeChange"
        >
        </ui-page>
      </div>
    </div>
    <!-- 动态库人脸详情 -->
    <details-whole-modal
      v-if="videoShow"
      ref="videoDetail"
      @prePage="prePage"
      @nextPage="nextPage"
      @close="videoShow = false"
    ></details-whole-modal>
    <hl-modal
      v-model="modalShow"
      title="提示"
      :r-width="500"
      @onCancel="loadCancel"
    >
      <div class="content">
        <p class="tipLoad">数据打包中，请等候......</p>
        <p>大约尚需{{ maybeTime }}秒</p>
      </div>
    </hl-modal>
    <ui-modal
      v-model="wranModalShow"
      title="提示"
      :r-width="500"
      @onCancel="onCancel"
      @onOk="onOk"
    >
      <div class="content">
        <p>当前存在打包任务，请确认是否离开！</p>
      </div>
    </ui-modal>
    <direction-model ref="directionModel"></direction-model>
  </section>
</template>
<script>
import searchWhole from "../components/search-whole";
import detailsWholeModal from "@/components/detail/details-whole-modal.vue";
import directionModel from "@/views/wisdom-cloud-search/search-center/advanced-search/components/direction-model";
import { myMixins } from "../mixin/index.js";
import hlModal from "@/components/modal/index.vue";
import { queryFusionSearch } from "@/api/viewParsingLibrary";
import exportBox from "@/views/wisdom-cloud-search/search-center/components/export/export-box.vue";
import { addCollection, deleteMyFavorite } from "@/api/user";
import {
  wholeDownload,
  taskView,
  picturePick,
} from "@/api/wisdom-cloud-search";
export default {
  components: {
    searchWhole,
    detailsWholeModal,
    hlModal,
    exportBox,
    directionModel,
  },
  mixins: [myMixins], //全局的mixin
  data() {
    return {
      dataList: [],
      listLoading: false,
      fallsPage: false,
      queryParam: {
        urlList: [],
        similarity: 80,
        selectDeviceList: [], // 选择设备
        selectTaskList: [], // 选择任务
        selectFileList: [], // 选择文件
        timeSlot: "近一天",
        features: [],
        selectType: "",
        startDate: "", // 抓拍时段 - 开始时间
        endDate: "", // 抓拍时段 - 结束时间
      },
      pageInfo: {
        pageNumber: 1,
        pageSize: 27,
      },
      total: 0,
      visible: false,
      videoShow: false,
      checkAll: false,
      exportShow: false,
      similUpDown: false,
      timeUpDown: false,
      modalShow: false,
      wranModalShow: false,
      loadIntervel: null,
      timeInterval: null,
      maybeTime: 0,
    };
  },
  watch: {
    exportShow(val) {
      //点击空白处隐藏
      if (val) {
        document.addEventListener("click", () => {
          this.exportShow = false;
        });
      } else {
        document.addEventListener("click", () => {});
      }
    },
  },
  created() {
    this.setLayoutNoPadding(true);
  },
  mounted() {},
  methods: {
    // 搜索
    searchHandle() {
      this.refreshDataList(true);
    },
    refreshDataList(isRefreshAll = false, noRest = false) {
      if (this.fallsPage) {
        this.pageInfo = {
          pageNumber: 1,
          pageSize: 48,
        };
        this.dataList = [];
      } else {
        const { pageNumber, pageSize } = this.pageInfo;
        this.pageInfo = {
          pageNumber: isRefreshAll ? 1 : pageNumber,
          pageSize: isRefreshAll ? 27 : pageSize,
        };
        this.mayRequest = false;
      }
      this.queryList(false, noRest);
    },
    async getDataList(page = 0) {
      this.dataList = [];
      this.listLoading = true;
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      delete params.selectFileList;
      delete params.selectTaskList;
      params.similarity = params.similarity / 100;
      queryFusionSearch({ ...params, ...this.pageInfo })
        .then((res) => {
          this.total = res.data.total;
          this.dataList = res.data.entities || [];
          if (page == 1) {
            this.$refs.videoDetail.prePage(this.dataList);
          } else if (page == 2) {
            this.$refs.videoDetail.nextPage(this.dataList);
          }
          this.logParams(
            params,
            {
              muen: "视图解析库",
              name: "查询视图解析库全部分类",
              type: "4",
            },
            this.queryParam.selectDeviceList
          );
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    /**
     *
     * @param {*} page 详情翻页
     * @param {*} noRest 排序
     */
    queryList(page = false, noRest = false) {
      let deviceIds = this.queryParam.selectDeviceList.map((item) => {
        return item.deviceGbId;
      });
      let files = this.queryParam.selectFileList.map((item) => {
        return item.id;
      });
      this.queryParam.deviceIds =
        this.queryParam.selectType == "3" ? files : deviceIds;
      let taskIds = this.queryParam.selectTaskList.map((item) => {
        return item.id;
      });
      this.queryParam.taskIds = taskIds;
      this.queryParam.taskType = this.queryParam.selectType;
      if (this.queryParam.features.length > 0 && !noRest) {
        this.queryParam.order = "desc";
        this.queryParam.sortField = "similarity";
      } else if (
        this.queryParam.features.length == 0 &&
        this.queryParam.sortField == "similarity" &&
        !noRest
      ) {
        this.queryParam.order = "desc";
        this.queryParam.sortField = "absTime";
      }
      if (this.fallsPage) {
        this.cardScroll(page);
      } else {
        this.getDataList(page);
      }
    },
    cardScroll(page = 0) {
      let params = JSON.parse(JSON.stringify(this.queryParam));
      delete params.urlList;
      delete params.selectDeviceList;
      delete params.selectFileList;
      delete params.selectTaskList;
      params.similarity = params.similarity / 100;
      this.listLoading = true;
      queryFusionSearch({ ...params, ...this.pageInfo })
        .then((res) => {
          if (page == 2) {
            this.$refs.videoDetail.nextPage(res.data.entities || []);
          }
          if (res.data.entities.length == 0) {
            this.mayRequest = false;
          } else {
            this.dataList.push(...res.data.entities);
            this.logParams(
              params,
              {
                muen: "视图解析库",
                name: "查询视图解析库人体",
                type: "4",
              },
              this.queryParam.selectDeviceList
            );
            if (this.pageInfo.pageNumber <= 1) {
              this.pageInfo.pageNumber += 1;
              this.queryList(false, true);
            }
            this.mayRequest = true;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 图片
    imgChange() {},
    // 重置
    resetHandle() {
      this.refreshDataList(true);
    },
    // 页数改变
    pageChange(size) {
      this.pageInfo.pageNumber = size;
      this.refreshDataList(false, true);
    },
    // 页数量改变
    pageSizeChange(size) {
      this.pageInfo.pageNumber = 1;
      this.pageInfo.pageSize = size;
      this.refreshDataList(false, true);
    },
    // 瀑布流展示
    handleFalls() {
      this.fallsPage = !this.fallsPage;
      this.refreshDataList(true, true);
    },
    checkHandler(e, i) {
      this.dataList[i].isChecked = e;
      this.checkAll =
        this.dataList.filter((e) => e.isChecked).length === this.dataList.length
          ? true
          : false;
    },
    faceDetailFn(row, index) {
      this.videoShow = true;
      this.$nextTick(() => {
        this.$refs.videoDetail.init(
          row,
          this.dataList,
          index,
          1,
          this.pageInfo.pageNumber
        );
      });
    },
    checkAllHandler(val) {
      this.dataList = this.dataList.map((e) => {
        return {
          ...e,
          isChecked: val,
        };
      });
    },
    // 导出
    handleExport($event) {
      $event.stopPropagation();
      this.exportShow = !this.exportShow;
    },
    loadCancel() {
      this.modalShow = false;
      this.wranModalShow = true;
    },
    onCancel() {
      this.modalShow = true;
      this.wranModalShow = false;
    },
    onOk() {
      this.modalShow = false;
      this.wranModalShow = false;
      clearInterval(this.loadIntervel);
      clearInterval(this.timeInterval);
    },
    downdata() {
      this.loadIntervel = setInterval(() => {
        taskView(this.downTaskId)
          .then((res) => {
            if (res.data) {
              this.downStatus = res.data.status;
              if (res.data.status != 0) {
                clearInterval(this.timeInterval);
              }
              if (res.data.status == 1) {
                let filePath = res.data.path;
                let urllength = filePath.split("/");
                let filename = urllength[urllength.length - 1];
                let flieType = filename.indexOf("zip") > 0 ? "zip" : "xlsx";
                let url = "http://" + document.location.host;
                Toolkits.ocxUpDownHttp(
                  "lis",
                  `${flieType}`,
                  `${url}${filePath}`,
                  `${filename}`
                );
                this.onOk();
              } else if (res.data.status == 2) {
                this.onOk();
                this.$Message.warning("打包失败当前任务结束！");
              }
            } else {
              this.onOk();
            }
          })
          .catch(() => {});
      }, 2000);
    },
    confirm(param) {
      let funparams = JSON.parse(JSON.stringify(this.queryParam));
      let params = {};
      if (param.type == "1") {
        let list = this.dataList.filter((e) => e.isChecked);
        if (list.length > 0) {
          let ids = list.map((item) => item.id);
          params = {
            ids,
            downloadPics: param.downloadPics,
            downloadSize: null,
            ...funparams,
          };
          this.exportShow = false;
          this.modalShow = true;
        } else {
          this.$Message.warning("请选择需要导出的数据！");
          return;
        }
      } else {
        params = {
          ids: [],
          downloadPics: param.downloadPics,
          downloadSize: param.downloadSize,
          ...funparams,
        };
        this.exportShow = false;
        this.modalShow = true;
      }
      wholeDownload(params)
        .then((res) => {
          this.downTaskId = res.data.taskId;
          this.maybeTime = res.data.maybeTime;
          this.timeInterval = setInterval(() => {
            if (this.maybeTime == 0) {
              clearInterval(this.timeInterval);
            } else {
              this.maybeTime -= 1;
            }
          }, 1000);
          this.downdata();
        })
        .finally(() => {});
    },
    // 排序
    handleSort(val) {
      if (val != this.queryParam.sortField) {
        this.queryParam.sortField = val;
        if (val == "similarity") {
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      } else {
        this.queryParam.sortField = val;
        if (val == "similarity") {
          this.similUpDown = !this.similUpDown;
          this.queryParam.order = this.similUpDown ? "asc" : "desc";
        } else {
          this.timeUpDown = !this.timeUpDown;
          this.queryParam.order = this.timeUpDown ? "asc" : "desc";
        }
      }
      this.refreshDataList(false, true);
    },
    /**
     * 跳转到一人一档页面
     */
    archivesPage(item, index) {
      let pageUrl = "";
      if (index == 1) {
        pageUrl =
          "/viewanalysis/viewParsingLibrary?sectionName=faceContent&noMenu=1";
      } else {
        pageUrl = "/model-market/face-warfare/identity-authentivation?noMenu=1";
      }
      const { href } = this.$router.resolve({
        path: pageUrl,
        query: {
          imgUrl: item.traitImg,
        },
      });
      window.open(href, "_blank");
    },
    openDirectModel(row) {
      const { geoPoint, deviceName } = { ...row };
      if (!geoPoint) {
        this.$Message.error("暂无坐标");
        return;
      }
      this.$refs.directionModel.show({ geoPoint, deviceName });
    },
    // 目标添加
    handleTargetAdd(row) {
      let fileData = new FormData();
      fileData.append("algorithmType", 1);
      fileData.append("fileUrl", row.traitImg);
      picturePick(fileData).then((res) => {
        if (res.data.length == 0) {
          this.$Message.error("未提取到人脸!");
          return;
        }
        let params = res.data[0];
        let urlList = {
          fileUrl: "data:image/jpeg;base64," + params.imageBase,
          feature: params.feature,
          imageBase: params.imageBase,
        };
        this.$refs.searchBar.urlImgList([urlList, ""], 2);
        this.refreshDataList(true);
      });
    },
    /**
     * 收藏
     */
    collection(data, flag) {
      var param = {
        favoriteObjectId: data.id,
        favoriteObjectType:
          data.dataType == "face"
            ? 24
            : data.dataType == "vehicle"
            ? 25
            : data.dataType == "human"
            ? 26
            : 27,
      };
      if (flag == 1) {
        addCollection(param).then((res) => {
          this.$set(data, "myFavorite", "1");
          this.$Message.success("收藏成功");
        });
      } else {
        deleteMyFavorite(param).then((res) => {
          this.$set(data, "myFavorite", "2");
          this.$Message.success("取消收藏成功");
        });
      }
    },
    /**
     * 上一个
     */
    prePage(pageNum) {
      if (pageNum < 1 || this.fallsPage) {
        this.$Message.warning("已经是第一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        if (!this.fallsPage) {
          this.queryList(1, true);
        }
      }
    },
    /**
     * 下一个
     */
    async nextPage(pageNum) {
      let size = this.pageInfo.pageSize;
      if (this.total <= pageNum * size) {
        this.$Message.warning("已经是最后一个了");
        return;
      } else {
        this.pageInfo.pageNumber = pageNum;
        this.queryList(2, true);
      }
    },
    toTop() {
      let scrollTop = document.querySelector(".table-content");
      scrollTop.scrollTo(0, 0);
    },
    handleScroll() {
      // 距离顶部距离
      let scrollTop = document.querySelector(".table-content").scrollTop;
      // 可视区的高度
      let windowHelght = document.querySelector(".table-content").clientHeight;
      // 滚动条的总高度
      let scrollHeight = document.querySelector(".table-content").scrollHeight;
      // 计算底部距离
      let _dis = scrollTop + windowHelght;
      if (this.pageInfo.pageNumber >= 6) {
        this.mayRequest = false;
        return;
      }
      if (_dis + 1000 > scrollHeight && this.mayRequest && !this.listLoading) {
        this.pageInfo.pageNumber += 1;
        this.queryList(false, true);
      }
    },
  },
};
</script>
<style lang="less" scoped>
@import "style/index";
@import "../../../wisdom-cloud-search/search-center/advanced-search/pages/style/index";
</style>
