export default {
  queryByConfigType: '/ivdg-asset-app/assertDeviceParamConfig/queryByConfigType', // 根据类型 获取详情'
  updateConfig: '/ivdg-asset-app/assertDeviceParamConfig/update', // 资产设备参数配置编辑
  viewConfig: '/ivdg-asset-app/assertDeviceParamConfig/view', // 根据ID 获取详情
  queryNewAsycRecord: '/ivdg-asset-app/assertDeviceAsycRecord/queryNewAsycRecord', // 查询最新的同步记录
  // middleFiled: 联网平台同步设备assertDeviceAsycNet、 一机一档同步设备assertDeviceAsycArchives、视图库同步设备assertDeviceAsycView
  getCommonPageList: (middleFiled) => `/ivdg-asset-app/${middleFiled}/pageList`,
  // commonRemove: (middleFiled) => `/ivdg-asset-app/${middleFiled}/remove`,
  commonUpdate: (middleFiled) => `/ivdg-asset-app/${middleFiled}/update`,
  commonView: (middleFiled) => `/ivdg-asset-app/${middleFiled}/view`,
  moveOrgCode: (middleFiled) => `/ivdg-asset-app/${middleFiled}/moveOrgCode`, // 移动组织机构
  checkAndCompareDevice: (middleFiled) => `/ivdg-asset-app/${middleFiled}/checkAndCompareDevice`, // 检测和比对设备
  moveBeforeStatistics: (middleFiled) => `/ivdg-asset-app/${middleFiled}/moveBeforeStatistics`, // 移动之前统计(全选需要看设备是否入库)
  storageDevice: (middleFiled) => `/ivdg-asset-app/${middleFiled}/storageDevice`, // 资产入库
  queryDifferDetail: (middleFiled) => `/ivdg-asset-app/${middleFiled}/queryDifferDetail`, // 差异详情

  updateDeviceOfPhyStatus: (middleFiled) => `/ivdg-asset-app/${middleFiled}/updateDeviceOfPhyStatus`, // 资产库设置状态删除
  updateDeviceOfIsDel: (middleFiled) => `/ivdg-asset-app/${middleFiled}/updateDeviceOfIsDel`, // 资产库设置不可用
  deleteListData: (middleFiled) => `/ivdg-asset-app/${middleFiled}/deleteListData`, // 资产库设置不可用
  queryUnqualifiedDetail: (middleFiled) => `/ivdg-asset-app/${middleFiled}/queryUnqualifiedDetail`, // 获取不合格原因
  pageListExport: (middleFiled) => `/ivdg-asset-app/${middleFiled}/pageListExport`, // 导出
  updateDeviceOfSourceId: (middleFiled) => `/ivdg-asset-app/${middleFiled}/updateDeviceOfSourceId`, // 删除联网平台来源标识
};
