<template>
  <div
    class="left-search-process"
    :class="{ 'left-search-process-detail': isBigProgress }"
  >
    <div class="section together-process">
      <div class="wrap">
        <!--大于180，则class=clip-auto circle，否则：circle-->
        <div class="circle" :class="{ 'clip-auto': percent >= 180 }">
          <!--度数为：当前进度*3.6-->
          <div
            class="percent left"
            :style="`transform: rotate(${percent}deg)`"
          ></div>
          <!--大于180，则class=percent right，否则为percent right wth0-->
          <div class="percent right" :class="{ wth0: percent < 180 }"></div>
        </div>
        <div class="num">
          <span>{{ progress || 0 }}</span
          >%
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    progress: {
      type: Number,
    },
    percent: {
      type: Number,
    },
    isBigProgress: {
      type: <PERSON><PERSON><PERSON>,
    },
  },
};
</script>

<style lang="less" scoped>
.left-search-process {
  position: absolute;
  width: 370px;
  height: auto;
  top: 10px;
  bottom: 10px;
  left: 10px;
  color: #fff;
  border-radius: 5px;
  background-color: #202442;
  overflow: hidden;
  z-index: 999;
  transition: left 200ms;
  z-index: 9999;
  background-color: rgba(55, 55, 55, 0.6);
  .section {
    width: 200px;
    height: 200px;
    position: relative;
    margin: 0 auto;
    vertical-align: middle;
    margin-top: 50%;
    .wrap {
      position: absolute;
      width: 200px;
      height: 200px;
      border-radius: 50%;
      top: 0;
      left: 0;
      background-color: #ccc;
      .circle {
        position: absolute;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -moz-box-sizing: border-box;
        -o-box-sizing: border-box;
        border: 1px solid #ccc;
        clip: rect(0, 200px, 200px, 100px);
        .percent {
          position: absolute;
          width: 200px;
          height: 200px;
          border-radius: 50%;
          box-sizing: border-box;
          -webkit-box-sizing: border-box;
          -ms-box-sizing: border-box;
          -moz-box-sizing: border-box;
          -o-box-sizing: border-box;
          top: -1px;
          left: -1px;
          &.left {
            border: 10px solid #ff6300;
            clip: rect(0, 100px, 200px, 0);
          }
          &.right {
            border: 10px solid #ff6300;
            clip: rect(0, 200px, 200px, 100px);
          }
          &.wth0 {
            width: 0;
          }
        }
      }
      .clip-auto {
        clip: rect(auto, auto, auto, auto);
      }
      .num {
        position: absolute;
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -ms-box-sizing: border-box;
        -moz-box-sizing: border-box;
        -o-box-sizing: border-box;
        width: 180px;
        height: 180px;
        line-height: 180px;
        text-align: center;
        font-size: 50px;
        left: 10px;
        top: 10px;
        border-radius: 50%;
        color: #ff6300;
        background: white;
        z-index: 1;
        span {
          font-size: 50px;
        }
      }
    }
  }
}
.left-search-process-detail {
  width: auto;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0;
  .section {
    position: absolute;
    left: 50%;
    margin-left: -100px;
    top: 50%;
    margin-top: -100px;
  }
}
</style>
