<!--
    * @FileDescription: 收缩框
-->
<template>
    <div class="results-rogress-box">
        <img :src="resultsLoadGif" />
        <Progress :percent="percent" :stroke-color="['#5BDBFF', '#2C86F8']" />
        <div class="name">碰撞分析中...</div>
    </div>
</template>

<script>

export default {
    props: {
        params: {
            type: Object,
            default: () => ({})
        },
        title: {
            type: String,
        },
        percent: {
            type: Number,
            default: 0
        }
    },
    mounted() {
        setTimeout(() => {
            this.$emit('onResultData', true)
        }, 2000);
    },
    data() {
        return {
            resultsLoadGif: require('@/assets/img/model/people-to-case/pzfx.gif'),
            // percent: 60
        }
    },
    methods: {
    }
}
</script>

<style lang='less' scoped>
.results-rogress-box {
    height: 140px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin-top: 15px;
    img {
        width: 50px;
        height: 50px;
    }
    /deep/.ivu-progress{
        width: 85%;
    }
    .name{
        font-weight: 400;
        font-size: 12px;
        color: #3D3D3D;
    }
}
</style>
