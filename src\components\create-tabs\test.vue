<template>
  <div>
    <div v-if="!componentName">
      <create-tabs
        v-for="(item, index) in componentList"
        :key="index"
        :component-name="item.componentName"
        :tabs-text="item.text"
        :tabs-query="item.tabsQuery"
        @selectModule="selectModule"
      >
        {{ item.text }}
      </create-tabs>
    </div>
    <keep-alive>
      <component :is="componentName"></component>
    </keep-alive>
  </div>
</template>
<script>
import dealWatch from '@/mixins/deal-watch';
export default {
  mixins: [dealWatch],
  name: 'test',
  props: {},
  data() {
    return {
      componentName: null, //如果有组件名称则显示组件，否则显示路由本身dom
      componentList: [
        { componentName: 'test1', text: '模块1', tabsQuery: { id: 'test1' } },
        { componentName: 'test2', text: '模块2' },
      ],
      componentLevel: 0, //组件标签层级 如果是标签组件套用标签组件需要此参数
    };
  },
  created() {
    this.getParams();
  },
  activated() {
    this.startWatch('$route', () => {
      this.getParams();
    });
  },
  methods: {
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel];
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
  },
  watch: {},
  computed: {},
  components: {
    CreateTabs: require('@/components/create-tabs/create-tabs.vue').default,
    test1: require('@/components/create-tabs/test1.vue').default,
    test2: require('@/components/create-tabs/test2.vue').default,
  },
};
</script>
<style lang="less" scoped></style>
