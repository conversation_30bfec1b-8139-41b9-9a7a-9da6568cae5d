<template>
  <ui-modal v-model="visible" title="经纬度越界检测配置" width="30%" @query="handleSave">
    <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="100">
      <FormItem label="不允许超过" prop="type">
        <RadioGroup v-model="formValidate.type">
          <Radio :label="1">
            <span>国界</span>
          </Radio>
          <Radio :label="2">
            <span>省界</span>
          </Radio>
          <Radio :label="3">
            <span>市界</span>
          </Radio>
          <Radio :label="4">
            <span>区县（界）</span>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="允许的误差" prop="criticalValue">
        <InputNumber v-model="formValidate.criticalValue" class="width-sm" />
        <span class="ml-sm base-text-color">米</span>
        <p class="color-failed">备注：设备超出边界的最短距离不大于允许的误差，系统判定为合格！</p>
      </FormItem>
    </Form>
  </ui-modal>
</template>
<script>
import governanceevaluation from '@/config/api/governanceevaluation';
export default {
  props: {
    /**
     * 指标详情
     */
    formData: {
      required: true,
      default: () => {},
    },
    /**
     * 规则详情
     */
    indexRule: {
      required: true,
      default: () => {},
    },
  },
  data() {
    return {
      visible: false,
      longitudeLatitudeOffset: 2000,
      indexConfig: {},
      formValidate: {
        criticalValue: null,
        type: '',
      },
      ruleValidate: {
        criticalValue: [{ required: true, message: '允许的误差不能为空', type: 'number', trigger: 'blur' }],
        type: [{ required: true, message: '不能为空', type: 'number', trigger: 'change' }],
      },
    };
  },
  created() {},
  methods: {
    resetForm() {},
    init(processOptions) {
      this.indexConfig = JSON.parse(JSON.stringify(processOptions));
      this.visible = true;
      this.getLngLat();
    },
    async getLngLat() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
        };
        let {
          data: { data },
        } = await this.$http.get(governanceevaluation.inquireIndexRulePropertyList, { params });
        let extraParam = JSON.parse(data.extraParam || '{}');
        this.formValidate = { ...this.formValidate, ...extraParam };
      } catch (error) {
        console.log(error);
      }
    },
    async handleSave() {
      try {
        let { indexRuleId, taskSchemeId, indexType } = this.indexConfig;
        let valid = await this.$refs.formValidate.validate(() => {});
        if (!valid) return;
        let params = {
          ruleId: indexRuleId,
          taskSchemeId,
          indexType,
          extraParam: JSON.stringify(this.formValidate),
        };
        let { data } = await this.$http.post(governanceevaluation.editIndexRulePropertyList, params);
        this.$Message.success(data.msg);
        this.visible = false;
      } catch (e) {
        console.log(e);
      }
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
@{_deep} .ivu-modal-body {
  margin-top: 0 !important;
  padding: 20px 50px !important;
}
</style>
