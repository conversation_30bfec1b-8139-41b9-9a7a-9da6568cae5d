export default {
  getAlgorithmList: '/ivdg-ai-service/algorithm/config/pageList', // 查询多算法厂商配置分页列表
  addAlgorithm: '/ivdg-ai-service/algorithm/config/add', // 多算法厂商配置新增
  updateAlgorithm: '/ivdg-ai-service/algorithm/config/update', // 多算法厂商配置编辑
  delAlgorithm: '/ivdg-ai-service/algorithm/config/remove', // {id}多算法厂商配置删除
  getAlgorithmDetail: '/ivdg-ai-service/algorithm/config/view/', // {id}获取多算法厂商配置详细信息
  algorithmselect: '/ivdg-ai-service/algorithm/config/select/', // 人脸结构化

  dictData: '/qsdi-system-service/dictData/queryByTypeKey?typekey=', // 数据字典

  dictDataList: '/qsdi-system-service/dictData/pageList', // 数据字典

  // standard1400: '/ivdg-evaluation-app/standard1400/select', //1400标准字典管理
  standard1400: '/ivdg-ai-service/dict/queryList', //1400标准字典管理
  queryListTransfer: '/ivdg-ai-service/dict/queryListTransfer', //查询字典

  // 视频流配置
  videoConfig: '/ivdg-stream-sip-service/stream/registerPlatform', //分页获取28181注册设备
  gbConfig: '/ivdg-stream-sip-service/stream/gbConfig', //获取国标配置
};
