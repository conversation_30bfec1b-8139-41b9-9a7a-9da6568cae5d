<template>
  <div class="label">
    <div class="label-title">
      <span></span>标签信息
      <i
        @click="add()"
        class="icon-set iconfont auxiliary-color icon-shezhi3"
      />
    </div>
    <div class="label-content">
      <div
        class="label"
        v-for="(item, index) in labelList"
        :key="index"
        :style="{
          color: item.labelColor,
          border: '1px solid' + item.labelColor,
        }"
      >
        {{ item.labelName }}
        <i
          v-if="item.resultFrom == null || item.resultFrom == -1"
          class="iconfont icon-close-circle-fill"
          @click="delLable(item)"
        ></i>
      </div>
      <!-- <div class="label-content-add">
        <Icon type="md-add" />
      </div> -->
    </div>
    <!-- 标签新增 -->
    <LabelAdd
      :labelType="labelType"
      :archiveNo="archiveNo"
      ref="labelAdd"
      @setCheckedLabel="setCheckedLabel"
    />
  </div>
</template>
<script>
import {
  motifyVehicleLableIds,
  getVehicleBaseInfo,
} from "@/api/vehicleArchives";
import { motifyRealNameLableIds, getPersonBaseInfo } from "@/api/realNameFile";
import { getDeviceArchivesInfo } from "@/api/device";
import {
  queryLabelsByIds,
  motifyVidArchiveLableIds,
  motifyDeviceLableIds,
  personDelLabel,
  videoDelLabel,
  vehicleDelLabel,
  deviceDelLabel,
  placeDelLabelAPI,
  modifyPlaceArchiveLabelsAPI,
} from "@/api/labelPool";
import { getPlaceArchiveDetailInfoAPI } from "@/api/placeArchive";
export default {
  components: {
    LabelAdd: require("./label-add").default,
  },
  props: {
    // people/人  car/车 video/视频 device/一机一档 place/场所 other/其它
    type: {
      type: String,
      default: "people",
    },
    // 标签类型：1-设备，2-人员，3-物品，4-事件，5-场所，6-车辆
    labelType: {
      type: Number,
      default: 0,
    },
    labelInfo: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      color: "#1FAF8A",
      labelList: [],
      archiveNo: "",
      textLabelList: [
        { labelName: "测试标签1", labelColor: "#1FAF8A" },
        { labelName: "测试标签2", labelColor: "#1FAF8A" },
        { labelName: "测试标签3", labelColor: "#1FAF8A" },
        { labelName: "测试标签4", labelColor: "#1FAF8A" },
      ],
    };
  },
  computed: {},
  watch: {
    labelInfo(newV, oldV) {
      this.queryLabelList(newV);
    },
  },
  filter: {},
  created() {},
  mounted() {
    this.archiveNo = this.$route.query.archiveNo;
    this.queryLabelList(this.labelInfo);
  },
  methods: {
    add() {
      this.$refs.labelAdd.init(this.labelList || []);
    },
    // 已选标签
    async setCheckedLabel(list) {
      var ids = [];
      list.forEach((item) => {
        ids.push(item.id);
      });

      var code = "";
      if ([1, 2, 5].includes(this.labelType)) {
        code = this.$route.query.archiveNo;
        this.archiveNo = this.$route.query.archiveNo;
      } else {
        code = JSON.parse(this.$route.query.archiveNo);
        this.archiveNo = JSON.parse(this.$route.query.archiveNo);
      }

      var param = {
        archiveNo: code,
        labelIds: ids,
      };

      let req = null;

      if (this.labelType == 2) {
        if (this.type == "people") {
          // 实名档案
          req = motifyRealNameLableIds;
        } else {
          // 路人档案
          req = motifyVidArchiveLableIds;
        }
      } else if (this.labelType == 6) {
        // 车辆档案
        req = motifyVehicleLableIds;
      } else if (this.labelType == 5) {
        // 场所档案
        req = modifyPlaceArchiveLabelsAPI;
      } else {
        // 设备档案（一机一档）
        param.deviceId = code;
        req = motifyDeviceLableIds;
      }
      req(param).finally(() => {
        this.$refs.labelAdd.closeModal();
        this.refreshData();
        req = null;
      });
    },
    /**
     * 已添加标签列表查询
     */
    queryLabelList(arr) {
      // 场所档案返回的labelIds为labelId以,拼接的字符串，需要转为数组
      if (typeof arr === "string") {
        arr = arr.split(",");
      }
      queryLabelsByIds({ labelIds: arr, archiveNo: this.archiveNo }).then(
        (res) => {
          this.labelList = res.data;
          if (this.labelList && this.labelList.length == 0) {
            this.labelList = textLabelList;
          }
        }
      );
    },
    /**
     * 添加完标签刷新最新标签信息
     */
    refreshData() {
      if (this.labelType == 2) {
        var param = {
          archiveNo: this.$route.query.archiveNo,
          dataType: this.type == "people" ? 1 : 2,
        };
        getPersonBaseInfo(param).then((res) => {
          this.queryLabelList(res.data.labelIds);
        });
      }

      // 车辆档案
      if (this.labelType == 6) {
        getVehicleBaseInfo(JSON.parse(this.$route.query.archiveNo)).then(
          (res) => {
            this.queryLabelList(res.data.labelIds);
          }
        );
      }

      // 设备档案（一机一档）
      if (this.labelType == 1) {
        let data = {
          deviceId: this.$route.query.archiveNo,
        };
        getDeviceArchivesInfo(data)
          .then((res) => {
            this.queryLabelList(res.data.labelIds);
          })
          .catch(() => {})
          .finally(() => {});
      }

      // 场所档案
      if (this.labelType == 5) {
        getPlaceArchiveDetailInfoAPI(this.$route.query.archiveNo).then(
          (res) => {
            this.queryLabelList(res.data.labelIds);
          }
        );
      }
    },
    // 删除标签
    delLable(row) {
      var _this = this;
      this.$Modal.confirm({
        title: "提示",
        closable: true,
        content: `确定删除吗？`,
        onOk: async () => {
          var param = {
            archiveNo: this.$route.query.archiveNo,
            // dataType: _this.labelType,
            deleteLabelid: row.id,
          };

          if (_this.labelType == 2) {
            if (_this.type == "people") {
              // 实名档案
              await personDelLabel(param).then((res) => {});
            } else {
              // 路人档案
              await videoDelLabel(param).then((res) => {});
            }
          } else if (_this.labelType == 6) {
            param.archiveNo = JSON.parse(this.$route.query.archiveNo);
            // 车辆档案
            await vehicleDelLabel(param).then((res) => {});
          } else if (this.labelType == 5) {
            // 场所档案
            await placeDelLabelAPI({
              archiveNo: this.$route.query.archiveNo,
              deleteLabelId: row.id, // 这里后端加强了命名规范，特殊处理一下
            });
          } else {
            // 设备档案（一机一档）
            param.deviceId = this.$route.query.archiveNo;
            await deviceDelLabel(param).then((res) => {});
          }
          this.$Message.success("删除成功");
          this.refreshData();
        },
      });
    },
  },
};
</script>
<style lang="less" scoped>
.label {
  margin-top: 20px;
  &-title {
    font-size: 14px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.9);
    & > span {
      display: inline-block;
      width: 5px;
      height: 5px;
      background: #2c86f8;
      margin-right: 7px;
      position: relative;
      top: -2px;
    }
  }
  &-content {
    width: calc(~"(100% + 10px)");

    & > div {
      display: inline-block;
      margin-top: 10px;
      margin-right: 10px;
      border-radius: 2px;
      padding: 2px 6px;
    }
    &-add {
      cursor: pointer;
      width: 66px;
      height: 27px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px dashed #d3d7de;
      text-align: center;
      .ivu-icon {
        font-size: 20px;
        position: relative;
        // top: 4px;
      }
    }

    .label {
      position: relative;
      .iconfont {
        display: none;
        color: #ff4949;
        position: absolute;
        top: -10px;
        right: -7px;
        font-size: 14px;
        cursor: pointer;
      }
    }
    .label:hover {
      .iconfont {
        display: block;
      }
    }
  }
  .icon-set {
    float: right;
    cursor: pointer;
  }
}
</style>
