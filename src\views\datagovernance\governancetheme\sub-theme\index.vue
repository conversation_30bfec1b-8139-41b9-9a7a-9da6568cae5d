<template>
  <div class="sub-theme">
    <div v-if="!componentName" class="sub-theme-wrap">
      <div class="sub-theme-header">
        <div class="sub-theme-header-left">
          <span><i class="icon-font icon-x<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"></i></span>
          <span>创建主题数：</span>
          <span>{{ subThemeData.length }}</span>
        </div>
        <!-- <div class="sub-theme-header-right">
          <Button type="primary" @click="newProcess"
            ><i class="icon-font icon-tianjia"></i>新建治理流程</Button
          >
        </div> -->
      </div>
      <div class="sub-theme-content">
        <sub-theme-item
          ref="subThemeItem"
          v-for="(item, index) in subThemeData"
          :key="index"
          :themeData="item"
          @selectModule="selectModule"
          @render="render"
          @update="update"
        ></sub-theme-item>
      </div>
    </div>
    <component v-else :is="componentName" type="1" :aggregateOptions="normalOptions"></component>
    <new-dialog ref="newdialog" :isEdit="isEdit" :editForm="editForm" @updateTheme="updateTheme"></new-dialog>
    <loading v-if="loading"></loading>
  </div>
</template>
<script>
import governancetheme from '@/config/api/governancetheme';
import { normalData } from './normalOptions';
import { temporaryData } from './temporaryOptions';
export default {
  name: 'subtheme',
  props: {},
  data() {
    return {
      componentName: null,
      themeData: [
        {
          componentName: 'viewprocess',
          tabsQuery: { id: 'test1' },
          text: '视图基础数据治理1',
          title: '视图基础数据治理1',
          type: 1,
          creater: 'Admin',
        },
        {
          componentName: 'temporaryprocess',
          text: '深圳南山分局设备数据临时治理',
          title: '深圳南山分局设备数据临时治理',
          type: 0,
          creater: 'Admin',
          date: '2021/4/28 12:12:23',
        },
        {
          componentName: 'viewprocess',
          text: '视图基础数据治理2',
          title: '视图基础数据治理2',
          type: 1,
          creater: 'Admin',
        },
      ],
      normalOptions: [],
      componentLevel: 1, //组件标签层级 如果是标签组件套用标签组件需要此参数
      subThemeData: [],
      editForm: {},
      isEdit: false,
      listData: [],
      loading: false,
    };
  },
  created() {
    this.getParams();
  },
  mounted() {
    this.loading = true;
    this.getThemeList();
  },
  methods: {
    // 新建治理流程
    newProcess() {
      this.isEdit = false;
      this.editForm = {};
      this.$refs.newdialog.init();
    },
    selectModule(name) {
      if (this.$route.query.componentName) {
        this.componentName = name.split('-')[this.componentLevel]; // 根据设置的层级获取组件名
      } else {
        this.componentName = null;
      }
    },
    getParams() {
      // 根据路由地址参数来判断加载组件或者路由本身
      this.selectModule(this.$route.query.componentName);
    },
    async getThemeList() {
      try {
        let params = {
          topicName: '',
        };
        this.listData = [];
        let res = await this.$http.post(governancetheme.subthemeList, params);
        this.subThemeData = res.data.data;
        this.loading = false;
        // .map((item) => {
        //   this.listData.push(item.topicStatistics);
        //   return item;
        // });
      } catch (error) {
        console.log(error);
      }
    },
    // 新建（更新）治理流程
    async updateTheme(data) {
      try {
        let params = data;
        let res = await this.$http.post(governancetheme.updateSubtheme, params);
        if (res.data.code == '200') {
          this.$Message['success']({
            background: true,
            content: `${this.isEdit ? '更新' : '新建'}成功！`,
          });
          this.getThemeList();
        } else {
          this.$Message['error']({
            background: true,
            content: `${res.data.msg}`,
          });
        }
      } catch (error) {
        console.log(error);
      }
    },
    render() {
      this.getThemeList();
    },
    // 治理流程更新弹框打开
    update(data) {
      this.isEdit = true;
      this.editForm = data;
      this.$refs.newdialog.init();
    },
  },
  watch: {
    $route: 'getParams',
    // 监听组件名变化，修改视图主题流程图配置项
    componentName() {
      if (this.componentName !== 'viewprocess') {
        this.normalOptions = JSON.parse(JSON.stringify(temporaryData));
        // this.normalOptions[2]["datas"][0]["title"] = "临时入库";
        // this.normalOptions[2]["datas"][0]["desc"] =
        //   "标准转换后的数据临时存放在系统数据库，和当前主题绑定。";
      } else {
        this.normalOptions = JSON.parse(JSON.stringify(normalData));
        // this.normalOptions[2]["datas"][0]["title"] = "正式入库";
        // this.normalOptions[2]["datas"][0]["desc"] =
        //   "标准转换后的数据存入系统标准库。";
      }
      return this.componentName;
    },
  },
  components: {
    SubThemeItem: require('./components/sub-theme-item.vue').default,
    NewDialog: require('./components/newdialog.vue').default,
    viewprocess: require('@/views/datagovernance/governancetheme/viewprocess/index.vue').default,
    temporaryprocess: require('@/views/datagovernance/governancetheme/viewprocess/index.vue').default,
    tasktracking: require('@/views/datagovernance/tasktracking/index.vue').default,
  },
};
</script>
<style lang="less" scoped>
.sub-theme {
  width: 100%;
  height: 100%;
  background-image: url('../../../../assets/img/thememanagement/subthemebackground.png');
  // background-color: #03142d;
  background-size: 100% 100%;
  background-position: left;
  background-repeat: no-repeat;
  &-header {
    padding: 20px 0;
    margin: 0 20px;
    // padding: 13px 10px 0;
    // height: 47px;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    border-bottom: 1px solid var(--border-color);
    &-left {
      font-size: 14px;
      span:nth-child(1) {
        margin-right: 10px;
        i {
          font-size: 13px;
          background: linear-gradient(to bottom, #12a4c9, #0084f6);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;

          // background: linear-gradient(360deg, #0084f6 0%, #12a4c9 100%);
        }
      }
      span:nth-child(2) {
        color: #ffffff;
      }
      span:nth-child(3) {
        color: var(--color-bluish-green-text);
      }
    }
    &-right {
      display: flex;
      align-items: center;
      i {
        margin-right: 12px;
        font-size: 12px;
        color: #ffffff;
      }
    }
  }
  &-content {
    padding: 0 10px;
    margin-top: 20px;
    display: flex;
    // justify-content: space-between;
    flex-wrap: wrap;
  }
}
</style>
