<template>
  <div class="portal-container">
    <div class="portal-header">
      <div class="header-left">
        <div class="bg">视图数据治理与运维平台</div>
      </div>
      <nav-header-right :portal="true"></nav-header-right>
    </div>
    <div class="portal-content">
      <div
        v-for="item in list"
        class="page-item"
        :class="{ 'page-item-disabled': item.disabled }"
        :key="item.id"
        @click="openPage(item)"
      >
        <div class="page-icon">
          <i class="iconfontconfigure" :class="`icon-${item.iconName}`"></i>
        </div>
        <span class="page-name"> {{ item.text }}</span>
        <div class="lint"></div>
      </div>
    </div>
  </div>
</template>
<script>
// 门户 页面  --- 只有【白底晴天蓝】这个主题样式
import { mapActions, mapGetters } from 'vuex/dist/vuex.common.js';
import equipmentassets from '@/config/api/equipmentassets';
import { initTheme } from '@/style/theme';

export default {
  name: 'protal',
  components: {
    NavHeaderRight: () => import('@/components/nav-header-right.vue'),
  },
  data() {
    return {
      localStorageTheme: '',
      list: [],
    };
  },
  computed: {
    ...mapGetters({
      routerTreeList: 'permission/getRouterTreeList',
    }),
  },
  created() {
    this.getList();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.localStorageTheme = localStorage.getItem('theme') || 'dark';
      initTheme('light');
    });
  },
  beforeRouteLeave(to, from, next) {
    initTheme(this.localStorageTheme);
    next();
  },
  methods: {
    ...mapActions({
      setIsPortalSkipPage: 'common/setIsPortalSkipPage',
    }),
    async getList() {
      let res = await this.$http.get(`/portal-list.json?t=${Date.now()}`);
      this.list = res.data;
    },
    async openPage(pageItem) {
      if (pageItem.disabled) return;

      let youYunLoginData;
      if (pageItem.isExternalLink) {
        switch (pageItem.name) {
          // 合众 (安全检测)
          case 'http://************:5000/auth/login':
            window.open('http://************:5000/auth/sso?lg_token=95e7a91037fe76b323884ada24aa6b31');
            break;
          // 优云 （运维管理）
          case 'http://*************/#/login':
            youYunLoginData = await this.$http.get(equipmentassets.getExternalLogin, {
              params: {
                externalType: 2,
              },
            });
            window.open(`http://*************/tenant/api/v1/sso/login${youYunLoginData.data.data}`);
            break;
          default:
            window.open(pageItem.name, '_blank');
            break;
        }
      } else {
        this.setIsPortalSkipPage(true);
        // 数据联网特殊处理
        if (pageItem.name === "watcherwheel") {
          let routeData = this.$router.resolve({
            name: pageItem.name,
          });
          window.open(routeData.href, '_blank');
        }else {
          this.$router.push({ name: pageItem.name });
        }

      }
    },
  },
};
</script>

<style lang="less" scoped>
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.portal-container {
  position: relative;
  height: 100%;
  width: 100%;
  background-color: #eef7ff !important;
  background-image: url('../../assets/img/home/<USER>');
  background-size: 100% 100%;
  .portal-header {
    display: flex;
    justify-content: space-between;
    height: 53px;
    padding-right: 20px;
    margin-bottom: 31px;
    .header-left {
      width: 100%;
      height: 84px;
      padding-right: 15px;
      margin-left: 212px;
      display: flex;
      .bg {
        background: url('~@/assets/img/home/<USER>');
        background-size: 100% 100%;
        width: 100%;
        font-size: 40px;
        color: #ffffff;
        letter-spacing: 3px;
        text-shadow: 0px 3px 1px #2e86f4;
        text-align: center;
        .flex;
      }
    }
  }
  .portal-content {
    display: flex;
    flex-wrap: wrap;
    padding: 186px 215px;
    max-height: calc(100% - 84px);
    overflow: hidden;
    overflow-y: auto;
    .page-item {
      width: 280px;
      height: 260px;
      margin: 0 20px 20px 0;
      background: linear-gradient(180deg, #f3f8ff 0%, #ffffff 100%);
      box-shadow: 0px 4px 10px 0px #ccdcf3;
      border-radius: 12px;
      flex-direction: column;
      cursor: pointer;
      .flex;
      &:nth-of-type(5n) {
        margin-right: 0 !important;
      }
      &:hover {
        box-shadow: 0px 6px 16px 0px rgba(116, 167, 244, 0.8);
      }
      .page-icon {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        background-color: #e1eefe;
        .flex;
        .iconfontconfigure {
          font-size: 30px;
          color: #2c86f8;
        }
      }
      .page-name {
        font-weight: 700;
        font-size: 24px;
        color: #1f2937;
        padding: 26px 0 10px;
      }
      .lint {
        width: 30px;
        height: 4px;
        background: #2c86f8;
      }
      &.page-item-disabled {
        cursor: not-allowed;
        opacity: 0.5;
      }
    }
  }
  & ::-webkit-scrollbar {
    background-color: transparent !important;
  }
}
@{_deep}.nav-right {
  .user-box {
    .message,
    .theme-icon,
    .i-icon {
      color: #2c86f8;
    }
    .user-name {
      color: rgba(0, 0, 0, 0.8);
    }
  }
}
</style>
