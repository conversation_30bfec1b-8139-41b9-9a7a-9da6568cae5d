<!--
    * @FileDescription: 区域推荐=>列表
    * @LastEditors: 
    * @LastEditTime: 
 -->
<template>
  <div class="detailsBox">
    <div class="details-list">
      <div class="box-hint">
        <Icon type="ios-undo" @click="handleback" />
        <span @click="handleback">{{ label }} > 查询结果</span>
      </div>
      <div class="table-box">
        <div class="total-add">
          <span
            >共<span class="total">{{ dataList.length || 0 }}</span
            >个区域</span
          >
        </div>
        <Scroll
          :on-reach-bottom="handleReachBottom"
          height="100%"
          :loading-text="loadingText"
          :distance-to-edge="10"
        >
          <li
            v-for="(item, index) in dataList"
            :key="index"
            @click="handleDetail(item)"
            class="ignorelist"
            :class="{ active: current.id === item.region.id }"
          >
            <img :src="item.region.coverUrl" />
            <div class="right ml-5">
              <div class="content">
                <span class="ignore-name ellipsis" :title="item.region.name"
                  >区域名称：{{ item.region.name }}
                </span>
                <span class="ignore-name ellipsis" :title="item.frequencyNum"
                  >出现频次：{{ item.frequencyNum || 0 }}
                </span>
              </div>
            </div>
          </li>
        </Scroll>
        <ui-empty v-if="dataList.length == 0 && !loading"></ui-empty>
        <ui-loading v-if="loading"></ui-loading>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "",
  data() {
    return {
      dataList: [],
      loading: false,
      detailsParams: {},
      loadingText: "加载中",
      isLast: false,
      current: {},
      pageInfo: {
        pageNumber: 1,
        pageSize: 20,
      },
    };
  },
  props: {
    label: {
      type: String,
      default: () => "区域推荐",
    },
    listFun: {
      type: Function,
      default: () => {},
    },
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  methods: {
    handleList(param) {
      this.resetAll();
      this.detailsParams = { ...param };
      this.detailsParams.startTime = this.$dayjs(
        this.detailsParams.startTime
      ).format("YYYY-MM-DD HH:mm:ss");
      this.detailsParams.endTime = this.$dayjs(
        this.detailsParams.endTime
      ).format("YYYY-MM-DD HH:mm:ss");
      this.queryList();
    },
    queryList() {
      this.loading = true;
      let params = { ...this.detailsParams };
      this.listFun(params)
        .then((res) => {
          let list = res.data || [];
          this.dataList = this.dataList.concat(list);
          if (list && list.length) {
            this.pageInfo.pageNumber += 1;
          } else {
            this.isLast = true;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleReachBottom() {
      this.loadingText = "加载中";
      if (this.isLast) {
        this.loadingText = "已经是最后一页了";
        return;
      }
      return this.queryList();
    },
    handleback() {
      this.$emit("backSearch");
    },
    resetAll() {
      this.current = {};
      this.isLast = false;
      this.pageInfo.pageNumber = 1;
      this.dataList = [];
    },
    handleDetail(record) {
      this.current = record.region;
      this.$emit("handleDetail", { ...record }, { ...this.detailsParams });
    },
  },
};
</script>

<style lang="less" scoped>
.detailsBox {
  position: absolute;
  top: 10px;
  left: 10px;
  height: calc(~"100% - 20px");
  .details-list {
    height: 100%;
    background: #fff;
    box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;
    width: 370px;
    filter: blur(0px);
    position: relative;
    .box-hint {
      width: 370px;
      height: 40px;
      background: #2c86f8;
      border-bottom: 1px solid #d3d7de;
      color: #fff;
      font-size: 14px;
      line-height: 40px;
      padding-left: 14px;
      display: flex;
      align-items: center;
      .icon-jiantou {
        transform: rotate(90deg);
        display: inline-block;
        cursor: pointer;
      }
      .ivu-icon-ios-undo {
        font-size: 20px;
        cursor: pointer;
      }
      span {
        font-size: 14px;
        cursor: pointer;
        margin-left: 10px;
      }
    }
  }
  .total-add {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 10px 0 10px;

    .total {
      color: #3f73f7;
      margin: 0 2px;
    }
  }
  .table-box {
    height: calc(~"100% - 40px");
    position: relative;
    /deep/ .ivu-scroll-wrapper {
      height: calc(~"100% - 40px");
      width: 100%;
      position: initial;
      .ivu-scroll-container {
        overflow: auto;
        height: 100%;
      }
      .ignorelist {
        padding-right: 5px;
        display: flex;
        align-items: center;
        img {
          width: 100px;
          height: 64px;
        }
      }
      .right {
        height: 64px;
        flex: 1;
        .content {
          width: calc(~"100% - 30px");
          .ignore-name {
            max-width: 100%;
            display: inline-block;
            vertical-align: middle;
            font-size: 13px;
            font-weight: bold;
          }
        }
      }
      li {
        position: relative;
        cursor: pointer;
        border-bottom: 1px dashed rgba(167, 172, 184, 0.3);
        padding: 5px;
      }

      li:hover {
        background: rgba(70, 121, 250, 0.1);
        color: #666666;
        .toolbar {
          display: inline-block;
        }
      }

      li.active {
        background: rgba(70, 121, 250, 0.1);
      }
      .ellipsis {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
  }
  .freno-item {
    position: relative;
    list-style: none;
    padding-left: 20px;
    padding-right: 20px;
    height: 60px;
    cursor: pointer;
    display: flex;
    align-items: center;
    .ui-image {
      width: 80px;
      height: 80px;
    }
    &:hover {
      background-color: rgba(44, 134, 248, 0.2);
      .record-tools {
        display: block;
      }
    }
    .nightOut-count {
      color: #666666;
    }
    .detail {
      height: 100%;
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .t-blue-color {
      color: #2c86f8;
    }
    .plate-number {
      font-size: 14px;
      font-weight: normal;
      display: inline-block;
    }
    .record-tools {
      // position: absolute;
      // right: 5px;
      margin-top: 22px;
      margin-left: 30px;
      display: none;
    }
  }
  .freno-item-hover {
    background-color: rgba(44, 134, 248, 0.2);
    .record-tools {
      display: block;
    }
  }
}
</style>
