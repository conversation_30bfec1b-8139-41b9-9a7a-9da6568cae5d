<template>
  <ui-modal
    class="video-unreported"
    v-model="visible"
    v-if="visible"
    :title="reportList.regionName + '未上报位置类型'"
    :styles="styles"
    :footer-hide="true"
  >
    <div class="unreported-content">
      <select-tree ref="selectTree" :tree-data="treeData"></select-tree>
    </div>
  </ui-modal>
</template>

<style lang="less" scoped>
.video-unreported {
  @{_deep} .ivu-modal {
    width: 520px;
    .ivu-modal-header {
      margin-bottom: 0;
    }
    .ivu-modal-body {
      padding: 0 20px 20px;
    }
    .unreported-content {
      height: 420px;
      overflow: auto;
      position: relative;
    }
  }
}
@{_deep} .ivu-collapse {
  background: #073167;
  border: none;
  .ivu-collapse-item {
    border: none;
    .ivu-collapse-header {
      border: none;
      color: #ffffff;
      i {
        float: right;
        line-height: 38px;
      }
    }
    .ivu-collapse-content {
      color: #bee2fb;
      background: #082249;
    }
  }
}
</style>
<script>
import evaluationoverview from '@/config/api/evaluationoverview';
export default {
  props: {
    reportList: {
      type: Object,
      default: () => {},
    },
    tag: {},
    paramsData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      value1: '1',
      visible: true,
      styles: { width: '5rem' },
      treeData: [],
      paramsList: [],
    };
  },
  mounted() {},

  methods: {
    async getUnreported() {
      try {
        this.loading = true;
        let params = {
          indexId: this.paramsList.indexId,
          orgRegionCode: this.reportList.regionCode,
          batchId: this.reportList.batchId,
          displayType: this.paramsList.displayType,
          tab: this.tag,
        };
        let res = await this.$http.post(evaluationoverview.getSecondaryPopUpData, params);
        const datas = res.data.data;
        this.treeData = datas.emphasisLocationNotReport;
        this.loading = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
  watch: {
    paramsData: {
      handler(val) {
        if (val.batchId) {
          this.paramsList = val;
          this.getUnreported();
        }
      },
      deep: true,
      immediate: true,
    },
    visible(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.visible = val;
    },
  },
  components: {
    selectTree: require('@/views/governanceevaluation/evaluationoverview/components/basics/select-tree.vue').default,
  },
};
</script>
