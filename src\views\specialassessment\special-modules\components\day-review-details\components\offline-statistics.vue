<template>
  <div class="day-review-details auto-fill">
    <dynamic-condition
      :form-item-data="formItemData"
      :form-data="formData"
      @search="(formData) => $emit('startSearch', formData, 'search')"
      @reset="(formData) => $emit('startSearch', formData, 'reset')"
    >
    </dynamic-condition>
    <div class="btn-bar mt-md mb-md">
      <slot name="btnslot"></slot>
    </div>
    <ui-table
      class="ui-table auto-fill"
      :table-columns="tableColumns"
      :table-data="tableData"
      :loading="loading"
      @onSortChange="onSortChange"
    >
      <template #offLineTotalOfM="{ row }">
        <Tooltip
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color">{{ row.offLineTotalOfM }}</span>
          <div slot="content" v-if="row.offLineTotalOfM">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.checkStartDate"
              :end-date="row.checkEndDate"
              :hiatus="JSON.parse(row.timeDayJsonStr)"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #serialOffLineOfM="{ row }">
        <Tooltip
          transfer
          transfer-class-name="calender-tip"
          placement="bottom"
          @on-popper-show="handlePopperShow($event, row)"
          @on-popper-hide="handlePopperHide($event, row)"
        >
          <span class="unqualified-color">{{ row.serialOffLineOfM }}</span>
          <div slot="content" v-if="row.serialOffLineOfM">
            <nopage-calender
              v-if="row.visible"
              :start-date="row.checkStartDate"
              :end-date="row.checkEndDate"
              :hiatus="JSON.parse(row.timeDayJsonStr)"
            ></nopage-calender>
          </div>
        </Tooltip>
      </template>
      <template #phyStatus="{ row }">
        <span v-if="row.phyStatusText" :class="row.phyStatus === '1' ? 'qualified-color' : 'unqualified-color'">
          {{ row.phyStatusText }}
        </span>
        <span v-else>--</span>
      </template>
      <template #tagNames="{ row }">
        <tags-more :tag-list="row.tagList || []"></tags-more>
      </template>
    </ui-table>
    <slot name="page"></slot>
  </div>
</template>
<script>
import { qualifiedColorConfig } from '../utils/dayReviewDetailsColumns.js';

export default {
  name: 'video-online',
  props: {
    tableColumns: {
      type: Array,
      default: () => [],
    },
    formItemData: {
      type: Array,
      default: () => [],
    },
    tableData: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    searchData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      styles: {
        width: '9.45rem',
      },
      formData: {},
      qualifiedColorConfig: Object.freeze(qualifiedColorConfig),
    };
  },
  methods: {
    onSortChange(column) {
      this.$emit('sortChange', column);
    },
    handlePopperShow(e, row) {
      this.$set(row, 'visible', true);
    },
    handlePopperHide(e, row) {
      this.$set(row, 'visible', false);
    },
  },
  watch: {
    searchData: {
      handler(val) {
        this.formData = { ...val };
      },
      deep: true,
      immediate: true,
    },
  },
  components: {
    UiTable: require('@/components/ui-table.vue').default,
    DynamicCondition: require('@/views/governanceevaluation/evaluationoResult/components/dynamic-condition.vue')
      .default,
    TagsMore: require('@/components/tags-more.vue').default,
    NopageCalender:
      require('@/views/governanceevaluation/evaluationoResult/result-modules/VideoHistoryComplete/components/nopage-calender.vue')
        .default,
  },
};
</script>

<style lang="less" scoped>
.btn-bar {
  display: flex;
  justify-content: flex-end;
}

.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }
}
.unqualified-color {
  color: #ea4a36;
}
.qualified-color {
  color: #1faf81;
}
@{_deep} .base-search {
  border-bottom: 1px solid var(--border-table);
}
</style>
<style lang="less">
.calender-tip {
  .ivu-tooltip-inner {
    padding: 0 !important;
    border: none !important;
  }

  .ivu-tooltip-arrow {
    border-bottom-color: rgba(0, 21, 41, 0.15) !important;
  }
}
</style>
