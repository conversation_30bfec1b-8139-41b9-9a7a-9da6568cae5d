<template>
  <!-- 目前 personTypes不统一，单独拆分组件-->
  <div class="ui-gather-card" @click="detail">
    <div class="ui-gather-card-left">
      <ui-image :src="list.identityPhoto" />
    </div>
    <div class="ui-gather-card-right">
      <p class="ui-gather-card-right-item" v-for="(item, index) in cardInfo" :key="index">
        <span class="ui-gather-card-right-item-label">{{ item.name }}</span>
        <span
          v-if="item.value != 'unqualifiedAmount'"
          :title="list[item.value]"
          class="ui-gather-card-right-item-value"
          :style="{ color: item.color ? item.color : '#ffffff' }"
          >{{ list[item.value] }}</span
        >
        <span v-if="item.value == 'unqualifiedAmount'" style="color: #8797ac">暂无</span>
      </p>
      <tags-more
        v-if="list.personTypes !== []"
        :personTypeList="personTypeList"
        :tagList="list.personTypes ? list.personTypes : []"
        :defaultTags="defaultTags"
        placement="left-start"
        bgColor="#2D435F"
      ></tags-more>
      <span v-if="list.personTypes === []" style="color: #8797ac">暂无</span>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    cardInfo: {
      type: Array,
      default: () => [],
    },
    list: {
      type: Object,
      default: () => {},
    },
    defaultTags: {
      type: Number,
      default: 4,
    },
    personTypeList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      tagData: [],
    };
  },
  created() {},
  methods: {
    detail() {
      this.$emit('detail', this.list.id);
    },
  },
  watch: {
    defaultTags() {
      return this.defaultTags;
    },
  },
  components: {
    TagsMore: require('./tags-more').default,
    uiImage: require('@/components/ui-image').default,
  },
};
</script>
<style lang="less" scoped>
.ui-gather-card:nth-child(4n + 0) {
  margin-right: 0px;
}
.ui-gather-card {
  display: flex;
  height: max-content;
  // min-height: 158px;
  margin-bottom: 10px;
  padding: 10px;
  background: #0f2f59;
  margin-right: 10px;
  &-left {
    width: 138px;
    height: 138px;
    margin-right: 20px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &-right {
    flex: 1;
    width: 260px;
    &-item {
      // margin-bottom: 8px;
      margin-bottom: 8px;
      font-size: 14px;
      &-label {
        color: #8797ac;
      }
      &-value {
        color: #ffffff;
      }
    }
  }
  .tag {
    margin-right: 10px;
    padding: 3px 10px;
    font-size: 12px;
    background-color: #0e8f0e;
    color: #ffffff;
    border-radius: 4px;
  }
  .ui-gather-card-right-item-value {
    display: inline-block;
    width: 64%;
    text-overflow: ellipsis;
  }
}
</style>
