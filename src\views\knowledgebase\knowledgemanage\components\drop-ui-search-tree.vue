<template>
  <div class="inline" v-clickoutside="dropHide">
    <Dropdown trigger="custom" :visible="visible" class="drop-down-wrapper">
      <div
        class="ivu-select ivu-select-single select-width t-left"
        :class="{ 'ivu-select-disabled': disabled }"
        @mouseenter="mouseenter"
        @mouseleave="mouseleave"
      >
        <div class="ivu-select-selection" @click="dropShow">
          <div>
            <span class="ivu-select-placeholder" v-if="!treeText">{{ placeholder }}</span>
            <span class="ivu-select-selected-value" v-if="treeText">{{ treeText }}</span>
          </div>
          <Icon type="ios-arrow-down ivu-select-arrow" v-if="!isClose || !clearable"></Icon>
          <Icon type="ios-close-circle ivu-select-arrow" @click.stop="clear" v-if="clearable && isClose" />
        </div>
      </div>
      <DropdownMenu slot="list" class="t-left" transfer>
        <ui-search-tree ref="uiTree" class="customize-tree mr-sm" v-bind="$attrs" @selectTree="selectTreeChange">
          <!-- <template #label="{ node, data }">
          <div class="custom-tree-node">
            <span>{{ node.label }}</span>
            <Poptip title=""
                    transfer
                    content="content"
                    placement="left-start"
                    v-model="node.poptipshow">
              <p @click="openAction(node, data)">Right Top</p>
              <template #content>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
                <p><Button>Info</Button></p>
              </template>
            </Poptip>
          </div>
        </template> -->
        </ui-search-tree>
      </DropdownMenu>
    </Dropdown>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
  data() {
    return {
      visible: false,
      defaultProps: {
        label: 'regionName',
        children: 'children',
      },
      defaultKeys: [],
      isClose: false, //清空按钮是否显示
      checkedData: {
        name: '',
      },
    };
  },
  created() {},
  methods: {
    mouseenter() {
      if (!!this.treeText && !this.disabled) {
        this.isClose = true;
      }
    },
    mouseleave() {
      this.isClose = false;
    },
    dropHide() {
      this.visible = false;
    },
    dropShow() {
      if (!this.disabled) {
        this.visible = !this.visible;
      }
    },
    clear() {
      this.$emit('selectedTree', this.selectTree);
    },
    reset() {
      this.selectTree.regionCode = '';
      this.isClose = false;
    },
    selectTreeChange(checkedData) {
      this.checkedData = checkedData;
      this.visible = false;
      this.$emit('selectedTree', checkedData);
    },
  },
  watch: {
    selectTree: {
      handler(val) {
        if (val) {
          this.checkedData = val;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ...mapGetters({
      treeData: 'common/getAreaList',
      initialAreaList: 'common/getInitialAreaList',
    }),
    treeText() {
      return this.checkedData.name;
    },
  },
  props: {
    //是否显示清空按钮
    clearable: {
      default: false,
    },
    /**
     * selectTree.id:
     * selectTree.name:
     */
    selectTree: {
      required: true,
    },
    // 是否可以选中父节点
    selectNode: {
      default() {
        return true;
      },
    },
    placeholder: {
      type: String,
      default: () => {
        return '请选择';
      },
    },
    disabled: {},
  },
  components: {
    UiSearchTree: require('@/components/ui-search-tree.vue').default,
  },
};
</script>

<style lang="less" scoped>
.drop-down-wrapper {
  position: relative;
  @{_deep}.ivu-select-dropdown {
    left: 0px !important;
    width: 400px !important;
    max-height: 600px;
    overflow-y: auto;
    .ui-search-tree .el-tree {
      overflow: inherit !important;
    }
  }
}
.select-width {
  width: 400px;
}

.tree {
  min-width: 400px;
  overflow-x: auto;
  font-size: 12px;
  max-height: 500px;
  padding-right: 10px;
}
</style>
