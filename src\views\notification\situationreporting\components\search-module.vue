<template>
  <div class="search-module">
    <!--    <div class="search-wrap">-->
    <ui-label class="inline mr-lg mb-sm" label="报备名称" :width="65">
      <Input v-model="searchData.reportName" class="input-width" placeholder="请输入报备名称" clearable></Input>
    </ui-label>
    <ui-label v-if="queryType === '2'" class="inline mr-lg mb-sm" label="报备区划" :width="65">
      <api-area-tree
        class="api-area-tree area-tree-dropdown input-width"
        :select-tree="selectTree"
        @selectedTree="selectedTree"
        placeholder="请选择报备区划"
      ></api-area-tree>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="报备类型" :width="65">
      <Select class="input-width" v-model="searchData.reportType" placeholder="请选择报备类型" clearable>
        <Option v-for="optItem in reportTypes" :key="optItem.key" :value="optItem.key">{{ optItem.label }}</Option>
      </Select>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="报备生效时间" :width="95">
      <DatePicker
        class="width-md"
        v-model="searchData.beginTime"
        type="datetime"
        placeholder="请选择开始时间"
        :options="startTimeOption"
        confirm
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'beginTime')"
      ></DatePicker>
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.endTime"
        type="datetime"
        placeholder="请选择结束时间"
        :options="endTimeOption"
        confirm
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'endTime')"
      ></DatePicker>
    </ui-label>
    <ui-label class="inline mr-lg mb-sm" label="审核状态" :width="65">
      <Select class="input-width" v-model="searchData.status" placeholder="请选择审核状态" clearable>
        <Option v-for="statusItem in statusData" :key="statusItem.value" :value="statusItem.value">{{
          statusItem.text
        }}</Option>
      </Select>
    </ui-label>
    <ui-label v-if="queryType === '2'" class="inline mr-lg mb-sm" label="审核时间" :width="65">
      <DatePicker
        class="width-md"
        v-model="searchData.reviewBeginTime"
        type="datetime"
        placeholder="请选择开始时间"
        :options="reviewBeginTimeOption"
        confirm
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'reviewBeginTime')"
      ></DatePicker>
      <span class="ml-sm mr-sm">--</span>
      <DatePicker
        class="width-md"
        v-model="searchData.reviewEndTime"
        type="datetime"
        placeholder="请选择结束时间"
        :options="reviewEndTimeOption"
        confirm
        @on-change="(formatTime, timeType) => changeTimeMx(formatTime, timeType, searchData, 'reviewEndTime')"
      ></DatePicker>
    </ui-label>
    <div class="inline mb-sm">
      <Button type="primary" class="mr-sm" @click="search"> 查询 </Button>
      <Button type="default" @click="reset"> 重置 </Button>
    </div>
    <!--    </div>-->
    <div class="line"></div>
    <div class="btn-wrap">
      <slot name="btnbar"></slot>
    </div>
  </div>
</template>

<script>
import { reportTypes, statusArr } from '.././situationreporting.js';
export default {
  name: 'search-module',
  props: {
    queryType: {
      type: String,
      default: '1',
    },
  },
  data() {
    return {
      defaultSearch: {
        reportName: '',
        reportType: '',
        beginTime: '',
        endTime: '',
        status: '',
      },
      searchData: {},
      startTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.endTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      endTimeOption: {
        disabledDate: (date) => {
          let beginTime = new Date(this.searchData.beginTime);
          if (beginTime) {
            return date < beginTime;
          }
          return false;
        },
      },
      reviewBeginTimeOption: {
        disabledDate: (date) => {
          let endTime = new Date(this.searchData.reviewEndTime);
          if (endTime) {
            return date > endTime;
          }
          return false;
        },
      },
      reviewEndTimeOption: {
        disabledDate: (date) => {
          let beginTime = new Date(this.searchData.reviewBeginTime);
          if (beginTime) {
            return date < beginTime;
          }
          return false;
        },
      },
      reportTypes: {},
      statusData: [],
      selectTree: {
        regionCode: '',
      },
    };
  },
  created() {
    this.reportTypes = reportTypes;
    this.statusData = statusArr;
  },
  methods: {
    selectedTree(area) {
      this.searchData.civilCode = area.regionCode;
    },
    startSearch() {
      this.searchData = this.$util.common.deepCopy(this.defaultSearch);
      this.copySearchDataMx(this.searchData);
      this.$emit('startSearch', this.searchData);
    },
    search() {
      this.$emit('startSearch', this.searchData);
    },
    reset() {
      this.selectTree.regionCode = '';
      this.resetSearchDataMx(this.searchData, this.search);
    },
    // DatePicker选择时间后格式化
    changeTimeMx(formatTime, timeType, searchData, timeField) {
      this.$set(searchData, timeField, formatTime);
    },
  },
  watch: {
    queryType: {
      handler(val) {
        this.searchData = this.$util.common.deepCopy(this.defaultSearch);
        if (val === '2') {
          this.searchData = Object.assign(
            {
              civilCode: '',
              reviewBeginTime: '',
              reviewEndTime: '',
            },
            this.searchData,
          );
        }
        this.copySearch = null;
        this.copySearchDataMx(this.searchData);
        this.$emit('startSearch', this.searchData);
      },
    },
  },
  components: {
    ApiAreaTree: require('@/api-components/api-area-tree.vue').default,
  },
};
</script>

<style lang="less" scoped>
.search-module {
  width: 100%;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.search-wrap {
  //display: flex;
  //flex: 1;
  //flex-wrap: wrap;
}
.line {
  width: 100%;
  height: 1px;
  margin-bottom: 10px;
  background-color: var(--border-input);
}
.btn-wrap {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
}
.input-width {
  width: 200px;
}
.select-input-width {
  width: 163px;
}
.align-flex {
  display: flex;
  align-items: center;
}
.leftSelect {
  width: 120px !important;
}
.rightSelect {
  width: 240px !important;
}
.data-list {
  flex: 1;
  color: var(--color-content);
  font-size: 14px;
  .ui-select-tabs {
    margin-left: 70px;
  }
}
.split {
  display: inline-block;
  padding: 0 10px;
  color: #2ca4fd;
}
</style>
