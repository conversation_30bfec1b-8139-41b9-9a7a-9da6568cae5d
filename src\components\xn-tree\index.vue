<template>
  <div class="xn-tree-container">
    <div class="tree" ref="tree"></div>
  </div>
</template>

<script>
import xnTree from "./src/xnTree.js";
export default {
  components: {},
  props: {
    option: {
      type: Object,
      default: () => {},
    },
    label: {
      type: [Function, String],
    },
    fileOpe: {
      type: Array,
      default: () => [],
    },
    videoOpe: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    fileOpeList() {
      return this.fileOpe.filter((v) => v.show);
    },
  },
  provide() {
    return {
      tabledata: this,
    };
  },
  emits: [
    "clickNode",
    "dblclickNode",
    "startInspect",
    "parseInspect",
    "stopInspect",
    "clickVideoTrack",
  ],
  data() {
    return {
      defaultOption: {
        label: (d) => {
          let dom = `
                        <div style="display: flex;justify-content: space-between">
                        <div>
                            <span>
                                ${d.label}
                            </span>
                        </div>
                        </div>
                        `;
          return dom;
        },
        id: "id",
        pId: false,
        selectType: "null", //radio,null,checkbox
        canMove: true,
        draggable: false,
        checkDisabled: function (d) {
          return d.id == "001";
        },
        autoOpen: function (d, level) {
          return level <= 2;
        },
        checkSticky: {
          //check关联
          on: "", //p,自动勾选父，c自动勾选子，function
          off: "pc",
        },
        on: {
          clickNode: ($t, nodedata, nodekey) => {
            if (this.fileOpeList.length && nodedata.isLeaf) {
              // 叶子节点操作
              this.fileOpeList.forEach((v) => {
                if ($t.hasClass(v.label)) {
                  v.clickFn(nodedata);
                  return false;
                }
              });
            }
            if (this.videoOpe.length && nodedata.isLeaf) {
              // 叶子节点操作
              this.videoOpe.forEach((v) => {
                if ($t.hasClass(v.label)) {
                  v.clickFn(nodedata);
                  return false;
                }
              });
            }
            // 父节点操作
            if ($t.hasClass("startInspect")) {
              this.$emit("startInspect", nodedata);
              return false;
            }
            if ($t.hasClass("parseInspect")) {
              this.$emit("parseInspect", nodedata);
              return false;
            }
            if ($t.hasClass("stopInspect")) {
              this.$emit("stopInspect", nodedata);
              return false;
            }
            if ($t.hasClass("handleEdit")) {
              this.$emit("handleEdit", nodedata);
              return false;
            }
            if ($t.hasClass("handleShare")) {
              this.$emit("handleShare", nodedata);
              return false;
            }
            if ($t.hasClass("handleDel")) {
              this.$emit("handleDel", nodedata);
              return false;
            }
            if ($t.hasClass("videoTrack")) {
              this.$emit("clickVideoTrack", nodedata);
              return false;
            }
            this.$emit("clickNode", nodedata);
            return false; //true则设置该节点为当前点击元素，false反之
          },
          dblclickNode: ($t, nodedata, nodekey) => {
            this.$emit("dblclickNode", nodedata);
            return false; //true则设置该节点为当前点击元素，false反之
          },
          hoverNode: (nodedata, $t) => {
            if ($t.hasClass("showMore")) {
              let h = $t.get(0).querySelector(".moreList").offsetHeight;
              let max =
                document.querySelector(".xn-tree-container").clientHeight -
                h -
                33;
              if ($t.get(0).querySelector(".moreList").offsetTop > max) {
                $t
                  .get(0)
                  .querySelector(
                    ".moreList"
                  ).style.transform = `translate(-70%, -${h + 33}px)`;
              }
              return false;
            }
            return false;
          },
        },
      },
      xnTree: null,
    };
  },
  activated() {
    if (this.$refs.tree.xnTree) this.$refs.tree.xnTree.refreshDom();
  },
  created() {
    this.finalOption = $.extend(
      true,
      {},
      this.defaultOption,
      this.option || {}
    );
    let label;
    if (typeof this.label == "string") {
      label = this.label;
    } else {
      label = (d, ins) => {
        let label = this.label(d, ins);
        let ope = "";
        if (this.fileOpeList.length && d.isLeaf) {
          let opeItems = "";
          this.fileOpeList.forEach((v) => {
            if (v.label === "移出分组") {
              // 视频监控分组 校验 是否时分享过来的分组
              if (d.$pId) {
                if (this.xnTree.getNodeById(d.$pId).permission !== "read") {
                  opeItems += `<div class="${v.label}">${v.label}</div>`;
                }
              }
            } else {
              opeItems += `<div class="${v.label}">${v.label}</div>`;
            }
          });
          if (this.videoOpe && this.videoOpe.length) {
            opeItems += `<div class="line"></div>`;
            this.videoOpe.forEach((v) => {
              opeItems += `<div class="${v.label}">${v.label}</div>`;
            });
          }
          ope += `
                            <div class="showMore">
                                <i class="iconfont icon-gengduo"></i>
                                <div class="moreList">
                                    ${opeItems}
                                </div>
                            </div>
                        `;
        }
        let dom = `
                        ${label}
                        ${ope}
                        `;
        let html = document.createElement("div");
        html.innerHTML = dom;
        // 对 wifi 电子围栏 rfid mac 进行处理 取消加入分组和查看档案的入口
        let deviceTypeList = ["3", "4", "5"];
        if (deviceTypeList.includes(html.children[0].getAttribute("dtype"))) {
          const opeItems = [];
          if (this.fileOpeList.length && d.isLeaf) {
            this.fileOpeList.forEach((v) => {
              if (v.label !== "加入分组" && v.label !== "查看档案") {
                if (v.label === "查看视频") {
                  opeItems.push(`<div class="${v.label}">查看信息</div>`);
                } else {
                  opeItems.push(`<div class="${v.label}">${v.label}</div>`);
                }
              }
            });
          }
          dom = `
                    ${label}
                    <div class="showMore">
                        <i class="iconfont icon-gengduo"></i>
                        <div class="moreList">
                            ${opeItems.join("")}
                        </div>
                    </div>
                        `;
        }
        return dom;
      };
    }
    this.finalOption.label = label;
  },
  methods: {
    resetOption(option, refresh) {
      this.xnTree.resetOption(option);
      if (refresh) {
        this.xnTree.refreshDom();
      }
    },
    checkAll(justsearch) {
      this.xnTree.checkAll(justsearch);
    },
    clearAll() {
      this.xnTree.clearAll();
    },
    addNode(nodeId, newNode) {
      this.xnTree.addNode(nodeId, newNode);
    },
    deleteNode(nodeId) {
      this.xnTree.deleteNode(nodeId);
    },
    editNode(nodeId, newNode) {
      this.xnTree.editNode(newNode);
    },
    initTree(treeData) {
      if (this.xnTree) this.xnTree.destory();
      console.log("treeData", treeData);
      this.xnTree = new xnTree(this.$refs.tree, treeData, this.finalOption);
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped lang="less">
.xn-tree-container {
  display: flex;
  flex-flow: column;
  height: 100%;
  color: rgba(0, 0, 0, 0.8);
  /deep/ .xntree-cont {
    transform: translateY(0);
    height: 100%;
  }
  flex: 1;
  /deep/ .offline {
    i {
      color: #888888 !important;
    }
  }
}
.tree {
  width: 100%;
  height: 100%;
  overflow: auto;
  /deep/ .xn-tree-icons {
    a {
      color: #c0c4cc;
    }
  }
}
/deep/ .xntree-item {
  .color-bule {
    color: #2c86f8;
  }
  .color-grey {
    color: grey;
  }
  .color-white {
    color: #fff;
  }
  .node-title {
    overflow: hidden;
    text-overflow: ellipsis;
    &.playing {
      color: #ff8d33;
      .label {
        font-weight: bold;
      }
      i.playing-icon {
        width: 18px;
        height: 18px;
        background: url("~@/assets/img/player/playing.gif") 50% no-repeat;
        display: inline-block;
      }
    }
    &.ignore {
      cursor: not-allowed;
      color: rgb(177, 181, 191);
      .iconfont {
        color: rgb(177, 181, 191) !important;
      }
    }
    i {
      margin: 0 3px;
    }
    .offline {
      i {
        color: #888888;
      }
    }
  }
  .operate {
    color: #2c86f8;
    visibility: hidden;
    margin-right: 5px;
  }
  &:hover {
    .operate {
      visibility: visible;
    }
    .showMore {
      i {
        visibility: visible;
      }
    }
  }
  .showMore {
    position: relative;
    i {
      color: #2c86f8;
      visibility: hidden;
      margin: 0 5px;
      transform: rotate(90deg);
      display: block;
    }
    &:hover {
      .moreList {
        display: block;
      }
    }
    .moreList {
      display: none;
      position: fixed;
      background-color: #fff;
      box-shadow: 0px 3px 5px 0px rgba(147, 171, 206, 0.7);
      border-radius: 4px;
      z-index: 99;
      transform: translate(-70%, -3px);
      div {
        padding: 0 10px;
        &:hover {
          background: rgba(44, 134, 248, 0.2);
        }
      }
    }
  }
}
</style>
