<template>
  <div class="charts-container">
    <span class="arrow arrow-left" v-if="showArrow">
      <i class="icon-font icon-zuojiantou" @click="leftArrow"></i>
    </span>
    <div class="charts-container-auto" :class="showArrow ? 'margin-container' : ''">
      <div :class="['charts-item', `bg-${index % 5}`]" v-for="(item, index) in abnormalCount" :key="index + item.title">
        <!-- 不传icon，展示默认图标 -->
        <!--      <span v-if="!item.icon" class="icon-font icon-exceptionlibrary f-32 icon-2"></span>-->
        <span :class="['icon-font', 'f-40', item.icon, `icon-${index % 5}`]"></span>
        <div class="number-wrapper">
          <p class="f-12 base-text-color">{{ item.title }}</p>
          <p class="f-18 color-num">{{ commaNumber(item.count || 0) }}</p>
        </div>
      </div>
    </div>
    <span class="arrow arrow-right" v-if="showArrow">
      <i class="icon-font icon-youjiantou" @click="rightArrow"></i>
    </span>
  </div>
</template>
<script>
export default {
  props: {
    abnormalCount: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      showArrow: false,
      itemWidth: 0,
      itemArray: [],
      lastItemLeft: 0,
      allWdith: 0,
      indexNow: 0,
      nowIndex: 0,
    };
  },
  computed: {},
  watch: {
    abnormalCount: {
      deep: true,
      immediate: true,
      handler: function () {
        this.showArrowC();
      },
    },
  },
  filter: {},
  created() {},
  mounted() {},
  methods: {
    showArrowC() {
      this.$nextTick(() => {
        this.allWdith = document.getElementsByClassName('charts-container')[0].offsetWidth;
        this.itemArray = [];
        this.itemArray = document.getElementsByClassName('charts-item');
        this.lastItemLeft = document.getElementsByClassName('charts-item')[this.itemArray.length - 1].offsetLeft;
        if (
          this.allWdith -
            this.lastItemLeft +
            document.getElementsByClassName('charts-item')[this.itemArray.length - 1].offsetWidth <
          0
        ) {
          this.showArrow = true;
        } else {
          this.showArrow = false;
        }
      });
    },
    // 向左平移
    rightArrow() {
      if (this.indexNow == this.itemArray.length - 1) {
        this.$Message.warning('已经到顶了');
        return;
      }
      if (!this.itemWidth) {
        for (let i = 0; i < this.itemArray.length; i++) {
          if (
            this.allWdith < this.itemArray[i].offsetLeft + this.itemArray[i].offsetWidth &&
            this.allWdith > this.itemArray[i].offsetLeft
          ) {
            this.itemWidth =
              this.itemArray[i].offsetLeft +
              this.itemArray[i].offsetWidth -
              this.allWdith +
              this.itemArray[i].offsetWidth;
            this.indexNow = i + 1;
            this.nowIndex = i;
          } else if (this.allWdith == this.itemArray[i].offsetLeft + this.itemArray[i].offsetWidth) {
            this.itemWidth = this.itemArray[i].offsetWidth;
            this.indexNow = i + 1;
            this.nowIndex = i;
          }
        }
      } else {
        this.indexNow += 1;
        this.itemWidth =
          this.itemArray[this.indexNow].offsetWidth + this.itemArray[this.indexNow].offsetLeft - this.allWdith;
      }
      document.getElementsByClassName('charts-container-auto')[0].style.transform =
        'translateX(-' + this.itemWidth + 'px)';
    },
    // 向右平移
    leftArrow() {
      if (this.indexNow == 0 || this.indexNow == this.nowIndex) {
        this.$Message.warning('已经到顶了');
        return;
      } else {
        if (this.indexNow - 1 != this.nowIndex) {
          this.indexNow -= 1;
          this.itemWidth =
            this.itemArray[this.indexNow].offsetWidth + this.itemArray[this.indexNow].offsetLeft - this.allWdith;
        } else {
          this.indexNow -= 1;
          this.itemWidth = 0;
        }
      }
      document.getElementsByClassName('charts-container-auto')[0].style.transform =
        'translateX(-' + this.itemWidth + 'px)';
    },
    commaNumber(num) {
      return num ? String(num).replace(/(\d)(?=(\d{3})+$)/g, '$1,') : 0;
    },
  },
};
</script>
<style lang="less" scoped>
.charts-container {
  position: relative;
  .charts-container-auto {
    z-index: 10;
    transition: all 0.5s;
    margin-top: 10px;
    display: flex;
    // overflow: hidden;
    justify-content: flex-start;
    flex-wrap: nowrap;
    padding-bottom: 2px;
    .charts-item {
      flex-shrink: 0;
      margin-right: 10px;
      width: 304px;
      height: 88px;
      background: #0f2f59;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      .number-wrapper {
        display: inline-block;
        margin-left: 16px;
      }
    }
    .f-40 {
      font-size: 40px;
    }
    .color-white {
      color: #fff;
    }
    .color-num {
      color: #19d5f6;
    }
    .icon-0 {
      background: var(--icon-card-gradient-cyan);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-1 {
      background: var(--icon-card-gradient-green);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-2 {
      background: var(--icon-card-gradient-pink);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-3 {
      background: var(--icon-card-gradient-orange);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .icon-4 {
      background: var(--icon-card-gradient-deep-purple);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .bg-0 {
      background: var(--bg-card-cyan);
      box-shadow: var(--bg-card-cyan-shadow);
      .color-num {
        color: var(--font-card-cyan);
      }
    }
    .bg-1 {
      background: var(--bg-card-green);
      box-shadow: var(--bg-card-green-shadow);
      .color-num {
        color: var(--font-card-green);
      }
    }
    .bg-2 {
      background: var(--bg-card-pink);
      box-shadow: var(--bg-card-pink-shadow);
      .color-num {
        color: var(--font-card-pink);
      }
    }
    .bg-3 {
      background: var(--bg-card-orange);
      box-shadow: var(--bg-card-orange-shadow);
      .color-num {
        color: var(--font-card-orange);
      }
    }
    .bg-4 {
      background: var(--bg-card-deep-purple);
      box-shadow: var(--bg-card-deep-purple-shadow);
      .color-num {
        color: var(--font-card-deep-purple);
      }
    }
  }

  .arrow {
    height: 88px;
    line-height: 88px;
    margin-top: 10px;
    z-index: 12;
    i {
      background: #123d77;
      text-align: center;
      font-size: 12px;
      height: 28px;
      line-height: 28px;
      width: 28px;
      border-radius: 50%;
      color: #598fd6;
    }
  }
  .arrow-left {
    position: absolute;
    left: 12px;
    opacity: 0.6;
  }
  .arrow-right {
    position: absolute;
    right: 12px;
    top: 0;
    opacity: 0.6;
  }
}
.charts-container:hover .arrow {
  opacity: 1;
}
</style>
