import global from '@/util/global';
import {
  qualifiedColorConfig,
  iconStaticsFaceAndVehicle,
} from '@/views/governanceevaluation/evaluationoResult/util/CommonEnum/ReviewParticular.js';
export const iconStaticsList = [
  ...iconStaticsFaceAndVehicle,
  {
    name: '',
    count: '0',
    countStyle: {
      color: '#269F26',
    },
    iconName: 'icon-shipinjiankongjianshezongliang',
    fileName: 'resultValue',
    type: 'percent', // 百分比
  },
];
export const normalFormData = [
  {
    type: 'input',
    key: 'deviceId',
    label: '设备编码',
  },
  {
    type: 'input',
    key: 'deviceName',
    label: '设备名称',
  },
  {
    type: 'select',
    key: 'outcome',
    label: '检测结果',
    placeholder: '请选择检测结果',
    options: Object.keys(qualifiedColorConfig).map((key) => {
      return {
        value: key,
        label: qualifiedColorConfig[key].dataValue,
      };
    }),
  },
];

let renderHeaderTotal = (h, { params }) => {
  return <span>{params?.totalTitle || '近x小时抓拍数量'}</span>;
};
export const FaceTableColumns = (params) => {
  return [
    { type: 'index', width: 70, title: '序号', align: 'center' },
    {
      title: `${global.filedEnum.deviceId}`,
      key: 'deviceId',

      minWidth: 200,
      tooltip: true,
    },
    {
      title: `${global.filedEnum.deviceName}`,
      key: 'deviceName',
      minWidth: 200,
      tooltip: true,
    },
    { title: '组织机构', key: 'orgName', tooltip: true, minWidth: 120 },
    { title: '点位类型', key: 'sbdwlxText', tooltip: true, minWidth: 120 },
    { title: '设备状态', key: 'description', slot: 'description', tooltip: true, minWidth: 120 },
    {
      title: '近x小时抓拍数量',
      key: 'total',
      tooltip: true,
      minWidth: 150,
      renderHeader: (h, { column, index }) => {
        return renderHeaderTotal(h, { column, index, params });
      },
    },
    { title: '检测结果', slot: 'outcome', tooltip: true, minWidth: 120 },
    { title: '检测时间', key: 'startTime', tooltip: true, width: 200 },
    { title: '设备标签', slot: 'tagNames', tooltip: true, width: 200 },
    { title: '操作', slot: 'option', fixed: 'right', width: 100, align: 'center' },
  ];
};
