<template>
  <div class="governinfo">
    <div>
      <div class="governinfo-title mb-15">2.1、设备信息自动获取来源</div>
      <Form ref="formValidate" class="formInfo" :model="formValidate" :label-width="0">
        <FormItem
          label=""
          prop="propertyJson"
          :rules="[
            {
              required: false,
              message: '请选择字段',
              trigger: 'change',
              type: 'array',
            },
          ]"
        >
          <div class="synchro-item mb-15">
            <Checkbox class="checkdesc" v-model="gbObtain" @on-change="validChange('propertyJson')"></Checkbox>
            <p>1、通过28181国标平台自动获取</p>
          </div>
          <CheckboxGroup
            class="align-flex wrap"
            v-model="formValidate.propertyJson"
            @on-change="checkGroupChange('propertyJson')"
          >
            <Checkbox
              v-for="(propertyItem, propertyIndex) in propertyFields"
              :key="'property' + propertyIndex"
              class="align-flex flex-4"
              :label="propertyItem.value"
              >{{ propertyItem.label }}</Checkbox
            >
            <!-- <Checkbox class="align-flex" label="sbgnlx">摄像机功能类型</Checkbox> -->
            <!-- <Checkbox class="align-flex" label="manufacturer"
            >设备厂商名称</Checkbox
          >
          <Checkbox class="align-flex" label="model">设备规格型号</Checkbox>
          <Checkbox class="align-flex" label="ipAddr">IPV4地址</Checkbox>
          <Checkbox class="align-flex" label="isOnline"
            >设备是否在线状态</Checkbox
          > -->
          </CheckboxGroup>
        </FormItem>
        <!-- <FormItem
        label=""
        prop="rice"
        :rules="[
          {
            required: formValidate.isDeviation,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox
            class="checkdesc"
            v-model="formValidate.isDeviation"
          ></Checkbox>
          <p>通过安装地址/经纬度信息，自动获取行政区划</p>
        </div>
        <RadioGroup v-model="formValidate.gender">
          <Radio label="male">安装地址为主</Radio>
          <Radio label="female">经纬度信息为主</Radio>
        </RadioGroup>
      </FormItem> -->
        <FormItem label="">
          <div class="synchro-item mb-15">
            <Checkbox class="checkdesc" v-model="sdkObtain" @on-change="validChange('sdkPropertyJson')"></Checkbox>
            <p>2、通过SDK直连设备自动获取：<span class="remark">（注：需要拿到设备IP、端口、用户名和密码）</span></p>
          </div>
          <CheckboxGroup
            class="align-flex wrap"
            v-model="formValidate.sdkPropertyJson"
            @on-change="checkGroupChange('sdkPropertyJson')"
          >
            <Checkbox
              v-for="(connectItem, connectIndex) in connectFields"
              :key="'connect' + connectIndex"
              class="align-flex flex-3"
              :label="connectItem.value"
              >{{ connectItem.label }}</Checkbox
            >
          </CheckboxGroup>
          <!-- <p class="remark">
            备注：“设备厂商、IP地址、端口号、通道号、账号、密码”等信息正确填写的设备才支持该功能。
          </p> -->
          <!-- <RadioGroup v-model="formValidate.autoUpdateMac">
          <Radio label="1">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup> -->
        </FormItem>
        <FormItem label="">
          <div class="synchro-item">
            <Checkbox
              class="checkdesc"
              true-value="1"
              false-value="2"
              v-model="formValidate.autoUpdateOnlineStatus"
              @on-change="updateLineChange"
              label=""
            ></Checkbox>
            <p>3、通过视频流检测结果自动刷新在线状态</p>
          </div>
          <!-- <RadioGroup v-model="formValidate.autoUpdateOnlineStatus">
          <Radio label="1">是</Radio>
          <Radio label="2">否</Radio>
        </RadioGroup> -->
        </FormItem>
      </Form>
    </div>
    <div>
      <div class="governinfo-title mb-15">2.2、自动获取同步策略</div>
      <Form ref="formValidate1" class="formInfo" :model="formValidate" :label-width="0">
        <FormItem label="">
          <div class="synchro-item mb-15">
            <!-- <Checkbox
            class="checkdesc"
            v-model="formValidate.isDeviation"
          ></Checkbox> -->
            <p>1 、字段为空时，自动填充；</p>
            <RadioGroup v-model="formValidate.nullValueSyncStrategy" class="align-flex">
              <Radio label="1">是</Radio>
              <Radio label="2">否</Radio>
            </RadioGroup>
          </div>
          <!-- <RadioGroup v-model="formValidate.modelSyncStrategy" class="align-flex">
          <Radio label="1" class="radio-flex">留原始值</Radio>
          <Radio label="2" class="radio-flex">国标平台为准</Radio>
          <Radio label="3" class="radio-flex">SDK获取值为准</Radio>
        </RadioGroup> -->
        </FormItem>
        <FormItem label="">
          <div class="synchro-item mb-15">
            <p>2 、字段不为空时，覆盖原始值；</p>
            <RadioGroup v-model="formValidate.syncStrategy" class="align-flex">
              <Radio label="1">是</Radio>
              <Radio label="2">否</Radio>
            </RadioGroup>
          </div>
        </FormItem>
        <FormItem label="">
          <p class="mb-15">3 、数据填充原则</p>
          <FormItem label="" class="item-padding">
            <div class="synchro-item mb-15">
              <span>1）设备规格型号：</span>
              <RadioGroup v-model="formValidate.modelSyncStrategy" class="align-flex">
                <Radio label="2" :disabled="modelDisabled">国标平台为准</Radio>
                <Radio label="3" :disabled="modelDisabled">SDK获取值为准</Radio>
              </RadioGroup>
            </div>
          </FormItem>
          <FormItem label="" class="item-padding">
            <div class="synchro-item mb-15">
              <span>2）设备在线状态：</span>
              <RadioGroup v-model="formValidate.onlineSyncStrategy" :disabled="onlineDisabled" class="align-flex">
                <Radio label="2" :disabled="onlineDisabled">国标平台为准</Radio>
                <Radio label="3" :disabled="onlineDisabled">视频流检测结果为准</Radio>
              </RadioGroup>
            </div>
          </FormItem>
        </FormItem>
        <!-- <FormItem label="设备其他字段：">
        <RadioGroup v-model="formValidate.syncStrategy" class="align-flex">
          <Radio label="2" class="radio-flex">保留原始值</Radio>
          <Radio label="1" class="radio-flex">覆盖原始值</Radio>
        </RadioGroup>
      </FormItem> -->
        <!-- <FormItem
        label=""
        prop="point"
        :rules="[
          {
            required: formValidate.isPoint,
            message: '请输入数字',
            trigger: 'blur',
            type: 'number',
          },
        ]"
      >
        <div class="synchro-item">
          <Checkbox
            class="checkdesc"
            v-model="formValidate.isPoint"
            @on-change="validChange('point')"
          ></Checkbox>
          <p>字段为空时，自动填充</p>
        </div>
      </FormItem> -->
        <!-- <FormItem label=""> -->
        <!-- <div class="synchro-item">
          <Checkbox
            class="checkdesc"
            v-model="formValidate.isDeviation"
          ></Checkbox>
          <p>字段不为空时：</p>
        </div> -->
      </Form>
      <!-- <p class="remark">
        备注：多个信息来源同时获取到设备信息，将按填充原则更新设备信息。
      </p> -->
    </div>
    <!-- <div class="synchro-header">
      <div class="synchro-header-bar"></div>
      <div class="synchro-header-title">信息获取方案</div>
    </div> -->
    <!-- <Form ref="formValidate" class="formInfo" :model="formValidate" :label-width="0">
      <FormItem
        label=""
        prop="propertyJson"
        :rules="[
          {
            required: false,
            message: '请选择字段',
            trigger: 'change',
            type: 'array',
          },
        ]"
      >
        <div class="synchro-item">
          <p>通过国标平台自动获取如下字段信息：</p>
        </div>
        <CheckboxGroup
          class="align-flex wrap"
          v-model="formValidate.propertyJson"
          @on-change="checkGroupChange"
        >
          <Checkbox
            v-for="(propertyItem, propertyIndex) in propertyFields"
            :key="'property' + propertyIndex"
            class="align-flex flex-4"
            :label="propertyItem.value"
            >{{ propertyItem.label }}</Checkbox
          >
        </CheckboxGroup>
      </FormItem>
      <FormItem label="">
        <div class="synchro-item">
          <p>通过连接设备SDK或者全网扫描工具，自动获取如下字段信息：</p>
        </div>
        <CheckboxGroup
          class="align-flex wrap"
          v-model="formValidate.sdkPropertyJson"
          @on-change="checkGroupChange"
        >
          <Checkbox
            v-for="(connectItem, connectIndex) in connectFields"
            :key="'connect' + connectIndex"
            class="align-flex flex-3"
            :label="connectItem.value"
            >{{ connectItem.label }}</Checkbox
          >
        </CheckboxGroup>
        <p class="remark">
          备注：“设备厂商、IP地址、端口号、通道号、账号、密码”等信息正确填写的设备才支持该功能。
        </p>
      </FormItem>
      <FormItem label="">
        <div class="synchro-item">
          <Checkbox
            class="checkdesc"
            true-value="1"
            false-value="2"
            v-model="formValidate.autoUpdateOnlineStatus"
            @on-change="updateLineChange"
          ></Checkbox>
          <p>通过视频流检测结果，获取设备在线状态</p>
        </div>
      </FormItem>
    </Form> -->
    <!-- <div class="synchro-header">
      <div class="synchro-header-bar"></div>
      <div class="synchro-header-title">信息同步策略</div>
    </div> -->
    <!-- <p>字段为空时，自动填充；</p>
    <p>字段不为空时，保留原始值：</p> -->
    <!-- <Form ref="formValidate1" class="formInfo" :model="formValidate" :label-width="0">
      <FormItem label="">
        <div class="synchro-item">
          <p>字段为空时，自动填充；</p>
          <RadioGroup v-model="formValidate.nullValueSyncStrategy" class="align-flex">
            <Radio label="1">是</Radio>
            <Radio label="2">否</Radio>
          </RadioGroup>
        </div>
      </FormItem>
      <FormItem label="">
        <div class="synchro-item">
          <p>字段不为空时，覆盖原始值：</p>
          <RadioGroup v-model="formValidate.syncStrategy" class="align-flex">
            <Radio label="1">是</Radio>
            <Radio label="2">否</Radio>
          </RadioGroup>
        </div>
      </FormItem>
      <FormItem label="">
        <p>数据填充原则：</p>
        <FormItem label="" class="item-padding">
          <div class="synchro-item">
            <p>设备规格型号：</p>
            <RadioGroup v-model="formValidate.modelSyncStrategy" class="align-flex">
              <Radio label="2" :disabled="modelDisabled">国标平台为准</Radio>
              <Radio label="3" :disabled="modelDisabled">SDK获取值为准</Radio>
            </RadioGroup>
          </div>
        </FormItem>
        <FormItem label="" class="item-padding">
          <div class="synchro-item">
            <p>设备在线状态：</p>
            <RadioGroup
              v-model="formValidate.onlineSyncStrategy"
              :disabled="onlineDisabled"
              class="align-flex"
            >
              <Radio label="2" :disabled="onlineDisabled">国标平台为准</Radio>
              <Radio label="3" :disabled="onlineDisabled">视频流检测结果为准</Radio>
            </RadioGroup>
          </div>
        </FormItem>
      </FormItem>
    </Form> -->
    <!-- <p class="remark">
      备注：多个信息来源同时获取到设备信息，将按填充原则更新设备信息。
    </p> -->
  </div>
</template>
<script>
export default {
  props: {
    editInfos: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      formValidate: {
        nullValueSyncStrategy: '1', // 字段为空是否自动填充 1-是 2-否
        syncStrategy: '2', // 1-覆盖原始值 2-保留原始值
        autoUpdateOnlineStatus: '2', // 通过视频流检测结果，获取设备在线状态 1-是  2-否
        propertyJson: [], // 通过国标平台自动获取字段
        sdkPropertyJson: [], // sdk配置字段属性
        modelSyncStrategy: '3', // 设备规格型号同步策略 1-保留原始值 2-国标平台为准 3-sdk获取值为准
        onlineSyncStrategy: '3', // 设备在线状态同步策略   2-国标平台为准 3-视频流检测结果为准
      },
      propertyFields: [
        // {label: '摄像机功能类型', value: 'sbgnlx'},
        { label: '行政区划', value: 'civilCode' },
        { label: '设备厂商名称', value: 'manufacturer' },
        { label: '设备规格型号', value: 'model' },
        { label: 'IPV4地址', value: 'ipAddr' },
        { label: `${this.global.filedEnum.longitude}`, value: 'longitude' },
        { label: `${this.global.filedEnum.latitude}`, value: 'latitude' },
        { label: '设备是否在线状态', value: 'isOnline' },
      ],
      connectFields: [
        { label: `${this.global.filedEnum.macAddr}`, value: 'macAddr' },
        { label: '设备规格型号', value: 'model' },
        { label: '设备软件版本', value: 'softVersion' },
        { label: '视频主码流编码格式', value: 'videoCodingM' },
        { label: '视频子码流编码格式', value: 'videoCodings' },
        { label: '摄像机支持的分辨率', value: 'resolution' },
      ],
      modelDisabled: true,
      onlineDisabled: true,
      gbObtain: false,
      sdkObtain: false,
    };
  },
  // created() {},
  created() {
    this.formValidate.syncStrategy = this.editInfos.syncStrategy || '2';
    this.formValidate.nullValueSyncStrategy = this.editInfos.nullValueSyncStrategy || '1';
    this.formValidate.autoUpdateOnlineStatus = this.editInfos.autoUpdateOnlineStatus || '2';
    this.formValidate.propertyJson = this.editInfos.propertyJson ? JSON.parse(this.editInfos.propertyJson) : [];
    this.formValidate.sdkPropertyJson = this.editInfos.sdkPropertyJson
      ? JSON.parse(this.editInfos.sdkPropertyJson)
      : [];
    this.formValidate.modelSyncStrategy = this.editInfos.modelSyncStrategy || '3';
    this.formValidate.onlineSyncStrategy = this.editInfos.onlineSyncStrategy || '3';
    this.checkGroupChange();
  },
  updated() {
    let data = JSON.parse(JSON.stringify(this.formValidate));
    data.propertyJson = JSON.stringify(data.propertyJson);
    data.sdkPropertyJson = JSON.stringify(data.sdkPropertyJson);
    this.$emit('getInfos', data);
  },
  methods: {
    validChange(name) {
      this.$nextTick(() => {
        switch (name) {
          case 'propertyJson':
            if (this.gbObtain) {
              this.formValidate.propertyJson = this.propertyFields.map((item) => item.value);
            } else {
              this.formValidate.propertyJson = [];
            }
            break;
          case 'sdkPropertyJson':
            if (this.sdkObtain) {
              this.formValidate.sdkPropertyJson = this.connectFields.map((item) => item.value);
            } else {
              this.formValidate.sdkPropertyJson = [];
            }
            break;
        }
        this.checkGroupChange();
        this.$refs.formValidate.validateField(name);
      });
    },
    // 国标平台字段
    checkGroupChange() {
      // if (val === 'propertyJson') {
      //   this.gbObtain = this.formValidate.propertyJson.length ? true : false
      // } else {
      //   this.sdkObtain = this.formValidate.sdkPropertyJson.length ? true : false
      // }
      this.gbObtain = this.formValidate.propertyJson.length ? true : false;
      this.sdkObtain = this.formValidate.sdkPropertyJson.length ? true : false;
      const propertymodel = this.formValidate.propertyJson.includes('model');
      const sdkmodel = this.formValidate.sdkPropertyJson.includes('model');
      if (propertymodel && sdkmodel) {
        this.modelDisabled = false;
        this.formValidate.modelSyncStrategy = '3';
      } else if (!propertymodel && !sdkmodel) {
        this.modelDisabled = true;
        this.formValidate.modelSyncStrategy = '';
      } else {
        this.modelDisabled = true;
        this.formValidate.modelSyncStrategy = !propertymodel ? '3' : '2';
      }
      this.updateLineChange();
    },
    // 通过视频流检测结果，获取设备在线状态
    updateLineChange() {
      const propertyonline = this.formValidate.propertyJson.includes('isOnline');
      if (propertyonline && this.formValidate.autoUpdateOnlineStatus === '1') {
        this.onlineDisabled = false;
        this.formValidate.onlineSyncStrategy = '3';
      } else if (!propertyonline && this.formValidate.autoUpdateOnlineStatus !== '1') {
        this.onlineDisabled = true;
        this.formValidate.onlineSyncStrategy = '';
      } else {
        this.onlineDisabled = true;
        this.formValidate.onlineSyncStrategy = !propertyonline ? '3' : '2';
      }
    },
    async valids() {
      const valid = await this.$refs['formValidate'].validate((valid) => valid);
      return valid;
    },
    handleReset() {
      this.$refs['formValidate'].resetFields();
      this.$refs['formValidate1'].resetFields();
    },
  },
  watch: {},
  components: {},
};
</script>
<style lang="less" scoped>
[data-theme='dark'] {
  .remark {
    color: #e44f22 !important;
  }
}
[data-theme='light'],
[data-theme='deepBlue'] {
  .remark {
    color: var(--color-tips) !important;
  }
}

.align-flex {
  display: flex;
  align-items: center;
}
.flex-3 {
  width: 30%;
}
.flex-4 {
  width: 23%;
}
.radio-flex3 {
  width: calc(calc(100% - 210px) / 3);
}
.mb-15 {
  margin-bottom: 15px;
}
.wrap {
  flex-wrap: wrap;
}
.governinfo {
  width: 100%;
  padding: 20px 30px;
  display: flex;
  font-size: 14px;
  background: var(--bg-sub-content);
  max-height: 320px;
  overflow-y: auto;
  &-title {
    color: var(--color-display-sub-title);
  }
  .checkdesc {
    margin-right: 0 !important;
  }
  .synchro-item {
    width: 100%;
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--color-content);
  }
  p {
    // width: 100%;
    font-size: 14px;
    color: var(--color-content);
    span {
      display: inline-block;
    }
  }
  .synchro {
    &-header {
      display: flex;
      height: 30px;
      margin: 10px 0;
      &-bar {
        width: 8px;
        margin-right: 6px;
        background: #239df9;
      }
      &-title {
        .align-flex;
        flex: 1;
        padding-left: 10px;
        font-size: 14px;
        font-weight: bold;
        color: var(--color-content);
        background: linear-gradient(90deg, #0a4f8d 0%, rgba(9, 40, 77, 0) 100%);
      }
    }
  }
}
@{_deep} .ivu {
  &-radio-wrapper {
    margin-right: 50px;
  }
  &-checkbox {
    margin-right: 10px;
    &-group {
      padding: 0 23px;
      &-item {
        display: flex !important;
        .mb-15;
      }
    }
  }
  &-radio {
    margin-right: 10px;
    &-group {
      padding: 0 23px;
    }
  }
  &-form-item {
    margin-bottom: 5px !important;
  }
}
.formInfo {
  @{_deep} .ivu-form-item {
    margin-bottom: 0 !important;
  }
}
.item-padding {
  padding: 0 23px !important;
}
@{_deep} .ivu-radio-wrapper .ivu-radio.ivu-radio-checked.ivu-radio-disabled .ivu-radio-inner {
  border-color: var(--color-active) !important;
  &:after {
    background-color: var(--color-active) !important;
  }
}
</style>
